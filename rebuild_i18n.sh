#!/bin/bash

#Fichier permettant d'extraire les chaines dans le code php et les bases de données

# Version EN
/usr/bin/php get-all-texts.php > all-texts-in-bdd.php
find . -type f -iname "*.php" | xgettext -f - --add-comments=/ -L PHP --keyword=_ --from-code=UTF-8
msgmerge --no-wrap -N ./htdocs/admin/_locale/en_GB/LC_MESSAGES/riashop.po messages.po > new.po
mv new.po ./htdocs/admin/_locale/en_EN.po
rm messages.po
rm all-texts-in-bdd.php

# Version DE
/usr/bin/php get-all-texts.php > all-texts-in-bdd.php
find . -type f -iname "*.php" | xgettext -f - --add-comments=/ -L PHP --keyword=_ --from-code=UTF-8
msgmerge --no-wrap -N ./htdocs/admin/_locale/de_DE/LC_MESSAGES/riashop.po messages.po > new.po
mv new.po ./htdocs/admin/_locale/de_DE.po
rm messages.po
rm all-texts-in-bdd.php

# Version IT
/usr/bin/php get-all-texts.php > all-texts-in-bdd.php
find . -type f -iname "*.php" | xgettext -f - --add-comments=/ -L PHP --keyword=_ --from-code=UTF-8
msgmerge --no-wrap -N ./htdocs/admin/_locale/it_IT/LC_MESSAGES/riashop.po messages.po > new.po
mv new.po ./htdocs/admin/_locale/it_IT.po
rm messages.po
rm all-texts-in-bdd.php

# Version ES
/usr/bin/php get-all-texts.php > all-texts-in-bdd.php
find . -type f -iname "*.php" | xgettext -f - --add-comments=/ -L PHP --keyword=_ --from-code=UTF-8
msgmerge --no-wrap -N ./htdocs/admin/_locale/es_ES/LC_MESSAGES/riashop.po messages.po > new.po
mv new.po ./htdocs/admin/_locale/es_ES.po
rm messages.po
rm all-texts-in-bdd.php

SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
HOME=/

TNT_ID=
CRONTABS_DIR=

# Traitement riashop pour les places de marché
20,50 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script confirm-orders-amazon --mode 0 2> /dev/null
23,53 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-amazon --mode 0
26,56 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script update-priceandquantity-amazon --mode 0 2> /dev/null
29,59 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script workqueue-amazon --mode 0 2> /dev/null

59 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script workqueue-ebay --mode 0
# TODO voir pour lancer le script par base mysql plutôt que par tenant
20 5 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script clean-workqueue-marketplace --mode 0

# Traitement des mises à jour des tarifs pour les places de marché
29,59 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script update-priceandquantity-ebay --mode 0

# Traitement des commandes provenant des places de marché
*/15 6-23 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script beezup/import-orders --mode 1
25,55 6-23,0-1 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-priceminister --mode 0
26,56 6-23,0-1 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-cdiscount --mode 0
27,57 6-23,0-1 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-rueducommerce --mode 0
28,58 6-23,0-1 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-ebay --mode 0
29,59 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-orders-pourdebon --mode 0

# Confirmation des commandes provenant des places de marché
*/15 6-23 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script beezup/notify-bl --mode 1
45 8,13,18 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script confirm-orders-cdiscount --mode 0
46 8,13,18 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script confirm-orders-rueducommerce --mode 0
47 8,13,18 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script confirm-orders-priceminister --mode 0
48 8,13,18 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script confirm-orders-ebay --mode 0

# Traitement Workqueue rue du commerce
29,59 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script send-mirakl-offers --mode 0
19,49 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script send-pourdebon-offers --mode 0

# Envoi des alertes de produits à nouveau disponibles
14 6 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script gu-livr-alerts-notify --mode 1

# Gestion des campagnes Email
0 18 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script send-boost-order --mode 1

# Gestion de la synchronisation Sineres
47 8,13 * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script sineres-import --mode 0

# Gestion de la synchronisation Salesforce
*/10 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script salesforce-import --mode 0
*/10 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script salesforce-export --mode 0

# Gestion de la synchronisation ios
*/15 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script devices-send-notifications --mode 0

# Import data HML
#30 1 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script "harmonia/import-prd" --mode 1
#40 1 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script "harmonia/import-users" --mode 1
#50 1 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script "harmonia/import-model_orders" --mode 1
#10 2 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script "harmonia/import-orders" --mode 1
#20 2 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script "harmonia/import-cod_list" --mode 1

# Gestion des imports
7 * * * * root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id $TNT_ID --script import-execution --mode 0

# Export commandes HML
#0 2 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-orders-harmonia --mode 1
#0 9 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-orders-harmonia --mode 1
#30 13 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-orders-harmonia --mode 1
#30 15 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-orders-harmonia --mode 1
#30 19 * * 7 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-orders-harmonia --mode 1

# Export rapports de visite HML
#0 3 * * 1-6 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 168 --script export-reports-harmonia --mode 1

# Reconstruction du cache de mercuriale toutes les 30 minutes pour Benjamin
#*/30 8-19 * * 1-5 root /usr/bin/php $CRONTABS_DIR/execute-script.php --tnt_id 135 --script rebuild-restrictions-cache --mode 1

# controles des tablettes actives
4 9 * * 1-5 root /usr/bin/php $CRONTABS_DIR/execute-script.php --script devices-check



# Attention ne rien ajouter en dernière ligne, celle ci n'est pas prise en compte par le démon vixie-cron.
# Toujours ajouter vos lignes actives au dessus de la présente notice

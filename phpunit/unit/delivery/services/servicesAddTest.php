<?php
	require_once('delivery.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class servicesAddTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester l'ajout d'un service valide 
         * @dataProvider validService 
         */
        public function testServicesValidAdd($name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign){

            $id = dlv_services_add($name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign);

            $this->assertTrue( true == $id, 'Erreur: dlv_services_add retourne un id invalide');

            // Vérifie que les champs sont corrects
            $rsrv = dlv_services_get($id);
            $this->assertTrue( $rsrv && ria_mysql_num_rows($rsrv) == 1, 'Erreur lors de la vérification des champs du service ajoutée');
            $srv = ria_mysql_fetch_assoc($rsrv);
        
            $this->assertEquals( $name, $srv['name'], 'Erreur: nom du service non conforme à la valeur lors de l\'ajout' );

            $this->assertEquals( $desc, $srv['desc'], 'Erreur: description du service non conforme à la valeur lors de l\'ajout' );

            $this->assertEquals( $url_site, $srv['url-site'], 'Erreur: Url du site Internet du transporteur du service non conforme à la valeur lors de l\'ajout' );

            $this->assertEquals( $url_colis, $srv['url-colis'], 'Erreur: Url de suivi des colis du service non conforme à la valeur lors de l\'ajout' );

            $this->assertEquals( $msg, $srv['alert-msg'], 'Erreur: message de notification du service non conforme à la valeur lors de l\'ajout' );

            $this->assertTrue( $active == $srv['is_active'], 'Erreur: propriété is_active du service non conforme à la valeur lors de l\'ajout' );
   
            $this->assertEquals( $price_ttc, $srv['price-ttc'], 'Erreur: Tarif forfaitaire TTC pour l\'utilisation du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $dealer_price_ht, $srv['dealer-price-ht'], 'Erreur: Tarif forfaitaire pour les revendeurs du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $dealer_free_ht, $srv['dealer-free-ht'], 'Erreur: Franco de port revendeur du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $weight_min, $srv['weight_min'], 'Erreur: poids minimum du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $weight_max, $srv['weight_max'], 'Erreur: poids maximum du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $ord_amount_min, $srv['ord_amount_min'], 'Erreur: montant minimum de commande du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertEquals( $ord_amount_max, $srv['ord_amount_max'], 'Erreur: montant maximum de commande du service non conforme à la valeur lors de l\'ajout' );
            
            $this->assertTrue( $accept_consign == $srv['accept_consign'], 'Erreur: propriété accept_consign du service non conforme à la valeur lors de l\'ajout' );
        }

        /** Fonction permettant de tester l'ajout d'un service invalide
         * @dataProvider invalidService
         */
        public function testServicesInvalideAdd($name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign, $error){

            $this->assertFalse(dlv_services_add($name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign), $error);
        }

        public static function validService(){
            return array(
                //         name               desc         url_site          url_colis       msg   active      zones   price_ttc dealer_price_ht dealer_free_ht weight_min weight_max ord_amount_min ord_amount_max accept_consign
                array('Nom service', 'Desc service', 'www.urlsite.fr', 'www.urlcolis.fr', 'message',    1,    array(array( 'id' => 2)),      null,       null,       null,        null,      null,       null,              null,             true),
            );
        }

        public static function invalidService(){
            return array(
                //         name               desc         url_site          url_colis       msg   active        zones price_ttc dealer_price_ht dealer_free_ht weight_min weight_max ord_amount_min ord_amount_max accept_consign        message d'erreur
                array(          '', 'Desc service', 'www.urlsite.fr', 'www.urlcolis.fr', 'message',    1,      array(),    null,       null,       null,        null,      null,            null,          null,        true, 'Erreur: ajout d\'un service avec un nom invalide'),
            );
        }
    }


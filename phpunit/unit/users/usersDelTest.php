<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class usersDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'un client 
		 */
		public function testUsersDel() {

            $this->assertTrue(gu_users_del(100), 'Erreur: le client n\'a pas été archivé');
		}

		/** Fonction permettant de vérifier la suppression d'un client
		 */
		public function testUsersVerifyDel(){

			$rusr = gu_users_get(100);
			$this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur lors de la vérification de l\'archivage du client');
		}
	}

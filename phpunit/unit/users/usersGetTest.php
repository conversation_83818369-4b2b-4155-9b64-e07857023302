<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class usersGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération d'un utilisateur par son id
		 */
		public function testUsersGetById() {

            $rusr = gu_users_get(1);
			$this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 1, 'Erreur lors de la récupération d\'un utilisateur par id');
			$usr = ria_mysql_fetch_assoc($rusr);       
            $this->assertEquals(1, $usr['id'], 'Erreur lors de la récupération d\'un utilisateur par id');


            $rusr = gu_users_get(1000);
			$this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur: gu_users_get retourne un résultat avec un identifant d\'utilisateur invalide');          
        }

        /** Fonction permettant de tester la récupération d'un utilisateur par son email
         */
        public function testUsersGetByEmail(){

            $rusr = gu_users_get(0, '<EMAIL>');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 1, 'Erreur lors de la récupération d\'un utilisateur par email');
            $usr = ria_mysql_fetch_assoc($rusr);
            $this->assertEquals(1, $usr['id'], 'Erreur lors de la récupération d\'un utilisateur par email');

            $rusr = gu_users_get(0, '<EMAIL>');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur: gu_users_get retourne un résultat avec un email inexistant');
        }

        /** Fonction permettant de tester la récupération d'utilisateur par son mot de passe
         */
        public function testUsersGetByPassword(){

            $rusr = gu_users_get(0, '', 'motdepasse');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 1, 'Erreur lors de la récupération d\'utilisateurs avec un filtre sur le mot de passe');
            $usr = ria_mysql_fetch_assoc($rusr);
            $this->assertEquals(1, $usr['id'], 'Erreur lors de la récupération d\'utilisateurs avec un filtre sur le mot de passe');

            $rusr = gu_users_get(0, '', 'motdepasseinexistant');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur: gu_users_get retourne un résultat avec un mot de passe jamais utilisé');
        }

        /** Fonction permettant de tester la récupération d'utilisateurs par leur profil
         */
        public function testUsersGetByProfil(){

            $rusr = gu_users_get(0, '', '', 2);
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 4, 'Erreur lors de la récupération d\'utilisateurs avec un filtre sur le profil');
            while($usr = ria_mysql_fetch_assoc($rusr)){
                $this->assertTrue(in_array($usr['id'], array(1,2,3,35)), 'Erreur lors de la récupération d\'utilisateurs avec un filtre sur le profil');
            }

            $rusr = gu_users_get(0, '', '', 1000);
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur: gu_users_get retourne un résultat avec un profil inéxistant comme filtre');
        }       

        /** Fonction permettant de tester la récupération d'un utilisateurs par sa référence
         */
        public function testUsersGetByRef(){
            
            $rusr = gu_users_get(0, '', '', 0, '', 0, 'REF1');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 1, 'Erreur lors de la récupération d\'un utilisateur par sa référence');
            $usr = ria_mysql_fetch_assoc($rusr);
            $this->assertEquals(1, $usr['id'], 'Erreur lors de la récupération d\'un utilisateur par sa référence');

            $rusr = gu_users_get(0, '', '', 0, '', 0, 'REFinexistante');
            $this->assertTrue($rusr && ria_mysql_num_rows($rusr) == 0, 'Erreur: gu_users_get retourne un résultat avec une référence d\'utilisateur jamais utilisé');
        }

        /** Fonction permettant de récupérer l'information accept_partners d'un client
         */
        public function testUsersAcceptPartnersGet(){

            $this->assertEquals( 1, gu_users_get_accept_partners(1), 'Erreur: information accept_partners non conforme à la valeur dans la base de donnée' );
            
            $this->assertEquals( 0, gu_users_get_accept_partners(2), 'Erreur: information accept_partners non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de récupérer l'adresse de facturation d'un client 
         */
        public function testUsersAdrInvoicesGet(){

            $this->assertEquals( 3, gu_users_get_adr_invoices(1), 'Erreur: adresse de facturation du client non conforme à la valeur dans la base de donnée' ) ;
        }

        /** Fonction permettant de tester la récupération de la forme de politesse à intégrer dans un email 
         */
        public function testUsersLetterCivilityGet(){

            $this->assertEquals( 'Cher Monsieur nom prenom,', gu_users_get_letter_civility(1), 'Erreur: formule de politesse incorrecte' );
        }

        /** Fonction permettant de récupérer l'information is_locked d'un client
         */
        public function testUsersIsLocked(){

            $this->assertEquals( 0, gu_users_get_is_locked(1), 'Erreur: information is_locked non conforme à la valeur dans la base de donnée' );

            $this->assertEquals( 1, gu_users_get_is_locked(2), 'Erreur: information is_locked non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester si l'utilisateur fait partie de la france métropolitaine et DOM-TOM 
         */
        public function testUsersIsFrance(){

            $this->assertTrue(gu_users_is_france(1), 'Erreur: gu_users_is_france retourne faux pour un client français');

            $this->assertFalse(gu_users_is_france(2), 'Erreur: gu_users_is_france retourne vrai pour un client non français');
        }

        /** Fonction permettant de tester si un ou plusieurs compte sont rattaché au compte client
         */
        public function testUsersIsParent(){

            $this->assertTrue(gu_users_is_parent(1), 'Erreur: gu_users_is_parent retourne faux pour un client parent');

            $this->assertFalse(gu_users_is_parent(2), 'Erreur: gu_users_is_parent retourne vrai pour un client qui n\'est pas parent');
        }


        /** Fonction permettant de tester l'éxistance d'un utilisateur
         */
        public function testUsersExists(){

            $this->assertTrue(gu_users_exists(1), 'Erreur: gu_users_exists retourne faux avec un utilisateur éxistant');
            
            $this->assertTrue(gu_users_exists(1, 2), 'Erreur: gu_users_exists retourne faux avec un utilisateur éxistant');

            $this->assertTrue(gu_users_exists(0, 0, '<EMAIL>'), 'Erreur: gu_users_exists retourne faux avec un utilisateur éxistant');

            $this->assertTrue(gu_users_exists(0, 0, '', 'REF1'), 'Erreur: gu_users_exists retourne faux avec un utilisateur éxistant');


            $this->assertFalse(gu_users_exists(1000), 'Erreur: gu_users_exists retourne vrai avec un utilisateur inéxistant');

            $this->assertFalse(gu_users_exists(1, 1), 'Erreur: gu_users_exists retourne vrai avec un utilisateur inéxistant');

            $this->assertFalse(gu_users_exists(0, 0, '<EMAIL>'), 'Erreur: gu_users_exists retourne vrai avec un utilisateur inéxistant');

            $this->assertFalse(gu_users_exists(0, 0, '', 'REFinexistante'), 'Erreur: gu_users_exists retourne vrai avec un utilisateur inéxistant');
        }
    }  

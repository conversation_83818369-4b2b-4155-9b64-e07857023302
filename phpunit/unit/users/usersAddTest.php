<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class userAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'insertion d'utilisateur valide
		 * @dataProvider valideUser
		 */
		public function testValidUserAdd($email, $type, $title, $firstname, $lastname, $password, $profile, $ref, $is_sync, $prc, $id, $parent,
			$accept_partners, $society, $siret, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $adr_email,
			$check_password, $address3, $country_state) {
				
			global $config;
			
			// Création  d'un compte utilisateur test
			$usr_id = gu_users_add_with_adresse($email, $type, $title, $firstname, $lastname, $password, $profile, $ref, $is_sync, $prc, $id, $parent,
				$accept_partners, $society, $siret, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $adr_email,
				$check_password, $address3, $country_state);
			
			// Vérifie que l'id est un integer
			$this->assertThat($usr_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
			), 'Erreur: gu_users_add_with_adresse retourne un identifiant invalide');

			// Vérifie l'id si le paramètre $id est précisé
			if($id != 0){
				$this->assertEquals($id, $usr_id, "Erreur: id du client non conforme à la valeur lors de l\'ajout");
			}

			//Vérifie que les champs sont corrects
			$rusr=gu_users_get($usr_id);
			$this->assertTrue( $rusr && ria_mysql_num_rows($rusr), 'Erreur lors de la vérification des champs du client ajouté');
			$usr = ria_mysql_fetch_assoc($rusr);

			$this->assertEquals($email, $usr['email'], 'Erreur: email de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(($profile==0)?PRF_CUSTOMER:$profile, $usr['prf_id'], 'Erreur: profil de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			if($type==1){ // Particulier
				$this->assertEquals( trim(ucfirst($firstname)), $usr['adr_firstname'], 'Erreur: prénom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( trim(ucfirst($lastname)), $usr['adr_lastname'], 'Erreur: nom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $usr['society'], 'Erreur: société de l\'utilisateur non conforme à la valeur lors de l\'ajout');
			}elseif($type==2){ // Société
				$this->assertEquals( '', $usr['adr_firstname'], 'Erreur: prénom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $usr['adr_lastname'], 'Erreur: nom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( trim(ucfirst($society)), $usr['society'], 'Erreur: société de l\'utilisateur non conforme à la valeur lors de l\'ajout');
			}else{ // Professionel
				$this->assertEquals( trim(ucfirst($firstname)), $usr['adr_firstname'], 'Erreur: prénom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( trim(ucfirst($lastname)), $usr['adr_lastname'], 'Erreur: nom de l\'utilisateur non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( trim(ucfirst($society)), $usr['society'], 'Erreur: société de l\'utilisateur non conforme à la valeur lors de l\'ajout');
			}
		
			$this->assertEquals($ref, $usr['ref'], 'Erreur: référence de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			$this->assertTrue(in_array($usr['prc_id'], array($prc,$config['default_prc_id'])), 'Erreur: catégorie tarifaire de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(($parent==null)?0:$parent, $usr['parent_id'], 'Erreur: id compte parent de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			$this->assertTrue($is_sync == $usr['is_sync'], 'Erreur: propriété is_sync de l\'utilisateur non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($type, $usr['type_id'], 'Erreur: type d\'utilisateur est non conforme à la valeur lors de l\'ajout');

			if($type == 1 ){ // Si c'est un utilisateur particulier
				$this->assertEquals($title, $usr['title_id'], 'Erreur: civilité de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}elseif($type == 2 ){ // Si c'est une société
				$this->assertEquals('', $usr['title_id'], 'Erreur: civilité de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}else{
				$this->assertTrue(in_array($usr['title_id'], array('', $title)), 'Erreur: civilité de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}

			if($password!=null){
				$this->assertEquals(md5($password), $usr['password'], 'Erreur: mot de passe de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}

			$this->assertTrue($accept_partners == $usr['accept_partners'], 'Erreur:propriété accept_partners de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			if( $type != 1){ // Si ce n'est pas un utilisateur particulier
				$this->assertEquals($siret, $usr['siret'], 'Erreur: numero de siret de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}else{
				$this->assertEquals('', $usr['siret'], 'Erreur: numero de siret de l\'utilisateur est non conforme à la valeur lors de l\'ajout');
			}

			$this->assertEquals(ucfirst($address1), $usr['address1'], 'Erreur: première partie de l\'adresse de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucfirst($address2), $usr['address2'], 'Erreur: deuxième partie de l\'adresse de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucfirst($address3), $usr['address3'], 'Erreur: troisième partie de l\'adresse de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($zipcode, $usr['zipcode'], 'Erreur: code postal de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucfirst($city), $usr['city'], 'Erreur: ville de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(strtoupper2($country), $usr['country'], 'Erreur: pays de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($phone, $usr['phone'], 'Erreur: numéro de téléphone de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($fax, $usr['fax'], 'Erreur: fax de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($mobile, $usr['mobile'], 'Erreur: numéro de téléphone portable de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($work, $usr['work'], 'Erreur: numéro de téléphone en journée de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucfirst($adr_name) , $usr['adr_desc'], 'Erreur: description de l\'adresse de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($adr_email, $usr['adr_email'], 'Erreur: email de l\'adresse de facturation de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

			$this->assertEquals($country_state, $usr['country_state'], 'Erreur: état / province de l\'adresse de l\'utilisateur est non conforme à la valeur lors de l\'ajout');

		}
				
		/** Fonction permettant de tester l'insertion d'utilisateur invalide
		 * @dataProvider invalideUser
		 */
		public function testInvalideUserAdd($email, $type, $title, $firstname, $lastname, $password, $profile, $ref, $is_sync, $prc, $id, $parent,
		$accept_partners, $society, $siret, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $adr_email,
		$check_password, $address3, $country_state, $error) {

			// Création  d'un compte utilisateur test
			$usr_id = gu_users_add_with_adresse($email, $type, $title, $firstname, $lastname, $password, $profile, $ref, $is_sync, $prc, $id, $parent,
			$accept_partners, $society, $siret, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $adr_email,
			$check_password, $address3, $country_state);

			// Vérifie que l'insertion a échoué
			$this->assertFalse($usr_id, $error);
		}

		public static function valideUser() {
			return array(
				//	  						email	  type	 title   		   firstname			 lastname	 password	  profile		ref			is_sync		prc		id	   parent accept_partners    society	 		   siret     address1    address2	 zipcode        city     country        phone            fax       mobile           work    adr_name           adr_email check_password   address3  country_state            
				array('<EMAIL>',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 null,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 ''),
				array('<EMAIL>',		 1,		 2,		 			  '',		 		   '',	'mdptest',		    1, 	 'ref1',		  true,	  3373,		 0,		 null,		 false,	  'société', '505 397 190 00024', 'address1', 'address2',   '79000',    'niort',   'france', '0505050505',		     '',		   '',		     '',		 '', '<EMAIL>',		false,		   '',		 ''),
				array('<EMAIL>',		 1,		 3,		 'prenomphpunit',		 'nomphpunit',		 null,		    2, 		 '',		 false,	  3374,		 0,		 null,		 false,		     '',				  '',		  '', 'address2',		 '',	'niort',	   'FR',		   '', '0505050505',		   '',		     '',	 'desc',		           '',		 true, 'address3',		 ''),
				array('<EMAIL>',		 2,		 4,		 'prenomphpunit',		 'nomphpunit',		 null,		    3,  	 '',		  true,		 0,	100000,		 null,		 false,	  'société',		 		  '', 'address1',		  '',		 '',		 '',		 '',		   '',		     '', '0606060606',		     '',		 '',		           '',		 true,		   '',		 ''),
				array('<EMAIL>',		 2,		 5,		              '',		 		   '',		 null,		 null,	 'ref2',		 false,		 0,	100001,	   100000,		 false,	  'société',		 		  '',		  '',		  '',	'75000',	'paris',		 '',		   '',		     '',		   '', '0505050505',		 '',		           '',		 true,		   '',		 ''),
				array('<EMAIL>',		 3,		 1,		 'prenomphpunit',		 'nomphpunit',	'mdptest',		   65,		 '',		 false,	    -1,		 0,		 null,		  true,		     '', '505 397 190 00024',		  '',		  '',		 '',		 '',		 '',		   '',		     '',		   '',		     '',	 'desc',		           '',		 true,		   '',		 ''),
			);
		}

		public static function invalideUser(){
			return array(
				//	  						email	  type	 title   		   firstname			 lastname	 password	  profile		ref			is_sync		prc		id	   parent accept_partners    society	 		   siret     address1    address2	 zipcode        city     country        phone            fax       mobile           work    adr_name           adr_email check_password   address3  country_state    message d'erreur        
				array('<EMAIL>',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 null,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur avec un email doublon'),
				array('<EMAIL>',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 null,		 '',		 false,		 0, 100000,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur avec un id doublon'),
				array(						   '',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 null,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur sans email '),
				array('<EMAIL>',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		'mdp',		 null,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur avec un mot de passe trop court'),
				array('<EMAIL>',		 1,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 -1,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur avec un identifiant de profil inéxistant'),
				array('<EMAIL>',		 2,		 1,		 'prenomphpunit',		 'nomphpunit',		 null,		 null,		 '',		 false,		 0,		 0,		 null,		 false,		     '',		 		  '',		  '', 		  '',		 '',		 '',		 '',  	   	   '',		     '',		   '',		     '',		 '',		           '',		 true,		   '',		 '', 'Erreur: ajout d\'un utilisateur professionnelle sans société'),
			);
		}
	}

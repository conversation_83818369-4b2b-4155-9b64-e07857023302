<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class userUpdateTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester les fonctions d'update d'un client avec des paramètres valides
		 */
		public function testUserValidUpdate(){
			
			$this->assertTrue( gu_users_update_ref( 100, 'ref123' ), 'Erreur: référence de l\'utilisateur non mise à jour' );
			
			$this->assertTrue( gu_users_update( 100, '<EMAIL>', 'mdpphpunit' ), 'Erreur: email/mot de passe de l\'utilisateur non mis à jour' );
			
			$this->assertTrue( gu_users_set_date_of_birth( 100, '1996-11-11' ), 'Erreur: date de naissance de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_longitude( 100, -0.46436210 ), 'Erreur: longitude de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_latitude( 100, 46.31008310 ), 'Erreur: latitude de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_seller_id( 100, 3 ), 'Erreur: compte représentant de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_naf( 100, 'PN' ), 'Erreur: code naf de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_website( 100, 'https://www.riastudio.fr/' ), 'Erreur: site web de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_taxcode( 100, '524R' ), 'Erreur: numero de TVA de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_encours( 100, 410 ), 'Erreur: encours de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_prc( 100, 3374 ), 'Erreur: catégorie tarifaire de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_profile( 100, 3 ), 'Erreur: profil de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_is_sync( 100, true ), 'Erreur: propriété is_sync de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_parent_id( 100, 2 ), 'Erreur: id du parent de l\'utilisateu non mis à jour' );
			
			$this->assertTrue( gu_users_set_can_login( 100, true ), 'Erreur: propriété can_login de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_confirmed( 100, true ), 'Erreur: propriété is_confirmed de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_is_locked( 100, 1 ), 'Erreur: propriété is_locked de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_lng_code( 100, 'fr' ), 'Erreur: langue de l\'utilisateu non mise à jour' );
			
			$this->assertTrue( gu_users_set_accept_partners( 100, true ), 'Erreur: propriété accept_partners de l\'utilisateu non mise à jour' );
		}
        
        /** Fonction permettant de tester les fonction d'update d'un client avec des paramètres invalide
		 */
		public function testUserInvalidUpdate(){
						
			$this->assertFalse( gu_users_update( 100, '<EMAIL>', 'mdp' ), 'Erreur: mot de passe mis à jour avec une valeur invalide' );
			
			$this->assertFalse( gu_users_set_date_of_birth( 100, 'pas une date' ), 'Erreur: date de naissance mise à jour avec une valeur invalide' );
			
			$this->assertFalse( gu_users_set_naf( 100, 'code naf trop long' ), 'Erreur: code naf mis à jour avec une valeur invalide' );
			
			$this->assertFalse( gu_users_set_prc( 100, 1000 ), 'Erreur: catégorie tarifaire mise à jour avec une valeur invalide' );
			
			$this->assertFalse( gu_users_set_profile( 100, 1000 ), 'Erreur: profil mis à jour avec une valeur invalide' );
			
			$this->assertFalse( gu_users_set_parent_id( 100, 1000 ), 'Erreur: compte parent mis à jour avec une valeur invalide' );	
			
			$this->assertFalse( gu_users_set_lng_code( 100, 'pas un langage' ), 'Erreur: langue mise à jour avec une valeur invalide' );	
		}

		/** Fonction permettant de vérifier que les champs ont bien été mis à jour
		 */
		public function testUserVerifyUpdate(){

			$rusr = gu_users_get(100);
			$this->assertTrue($rusr || ria_mysql_num_rows($rusr), 'Erreur lors de la vérification de la mise à jour des champs de l\'utilisateur' );
			$usr = ria_mysql_fetch_assoc($rusr);

			$this->assertEquals( 'ref123', $usr['ref'], 'Erreur: référence de l\'utilisateur non mise à jour' );

			$this->assertEquals( '<EMAIL>', $usr['email'], 'Erreur: email de l\'utilisateur non mis à jour' );

			$this->assertEquals( md5('mdpphpunit'), $usr['password'], 'Erreur: mot de passe de l\'utilisateur non mis à jour' );

			$this->assertEquals( '1996-11-11 00:00:00', $usr['dob'], 'Erreur: date de naissance de l\'utilisateur non mise à jour' );

			//$this->assertEquals( -2.21374900, $usr['longitude'], 'Erreur: longitude de l\'utilisateur non mise à jour' );

			//$this->assertEquals( 46.22763800, $usr['latitude'], 'Erreur: latitude de l\'utilisateur non mise à jour' );

			$this->assertEquals( 3, $usr['seller_id'], 'Erreur: compte représentant de l\'utilisateur non mis à jour' );

			$this->assertEquals( 'PN', $usr['naf'], 'Erreur: code naf de l\'utilisateur non mis à jour' );

			$this->assertEquals( 'https://www.riastudio.fr/', $usr['website'], 'Erreur: site internet de l\'utilisateur non mis à jour' );

			$this->assertEquals( '524R', $usr['taxcode'], 'Erreur: numero de TVA intracommunautaire de l\'utilisateur non mis à jour' );

			$this->assertEquals( 410, $usr['encours'], 'Erreur: encour de l\'utilisateur non mis à jour' );

			$this->assertEquals( 3374, $usr['prc_id'], 'Erreur: catégorie tarifaire de l\'utilisateur non mise à jour' );

			$this->assertEquals( 3, $usr['prf_id'], 'Erreur: profil de l\'utilisateur non mis à jour' );

			$this->assertTrue( true == $usr['is_sync'], 'Erreur: is_sync de l\'utilisateur non mis à jour' );

			$this->assertEquals( 2, $usr['parent_id'], 'Erreur: compte parent de l\'utilisateur non mis à jour' );

			$this->assertTrue( true == $usr['can_login'], 'Erreur: can_login de l\'utilisateur non mis à jour' );

			$this->assertTrue( true == $usr['is_confirmed'], 'Erreur: is_confirmed de l\'utilisateur non mis à jour' );

			$this->assertTrue( true == $usr['is_locked'], 'Erreur: is_locked de l\'utilisateur non mis à jour' );

			$this->assertEquals( 'fr', $usr['lng_code'], 'Erreur: langue de l\'utilisateur non mise à jour' );

			$this->assertTrue( true == $usr['accept_partners'], 'Erreur: accept_partners de l\'utilisateur non mis à jour' );
		}
    }

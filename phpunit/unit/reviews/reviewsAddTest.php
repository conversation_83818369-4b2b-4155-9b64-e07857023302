<?php
    require_once('site.reviews.inc.php');
    require_once('prd/reviews.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class reviewsAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'ajout d'un avis consommateur
         * @dataProvider validReviews
		 */
		public function testReviewsValidAdd($usr, $lastname, $firstname, $email, $subject, $desc, $note, $parent, $wst_id) {
            $_SERVER['REMOTE_ADDR']=1;
            
            $id=site_rvw_reviews_add($usr, $lastname, $firstname, $email, $subject, $desc, $note, $parent, $wst_id);
            $this->assertTrue( true == $id, 'Erreur: site_rvw_reviews_add retourne un identifiant invalide');
        
            // Vérifie que les champs sont corrects
            $rrvw = rvw_reviews_get($id);
            $this->assertTrue($rrvw && ria_mysql_num_rows($rrvw) == 1, 'Erreur lors de la vérification des champs de l\'avis consommateur ajouté');
            $rvw = ria_mysql_fetch_assoc($rrvw);
                   
            $this->assertEquals( $usr, $rvw['usr_id'], 'Erreur: Identifiant de l\'utilisateur émettant l\'avis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $firstname, $rvw['usr_firstname'], 'Erreur: Prénom de l\'émetteur non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $lastname, $rvw['usr_lastname'], 'Erreur: Nom de l\'émétteur non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $email, $rvw['usr_email'], 'Erreur: Adresse email de l\'émetteur non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $subject, $rvw['name'], 'Erreur: Titre de l\'avis consommateur non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $desc, $rvw['desc'], 'Erreur: Description/Contenu de l\'avis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $note, $rvw['note'], 'Erreur: Note attribué par le consommateur non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $parent, $rvw['parent'], 'Erreur: identifiant parent de l\'avis non conforme à la valeur lors de l\'ajout');
        }

        /** Fonction permettant de tester l'ajout d'un avis consommateur sur un produit
         * @dataProvider validPrdReviews
         */
        public function testPrdReviewsValid($prd, $usr, $name, $desc, $note, $parent, $usr_firstname, $usr_lastname, $usr_email, $usr_society, $note_dlv, $note_pkg){

            $id = prd_reviews_add($prd, $usr, $name, $desc, $note, $parent, $usr_firstname, $usr_lastname, $usr_email, $usr_society, $note_dlv, $note_pkg);
            $this->assertTrue( true == $id, 'Erreur prd_reviews_add retourne un identifiant invalide');
        
            // Vérifie que les champs sont corrects
            $rrvw = rvw_reviews_get($id);
            $this->assertTrue($rrvw && ria_mysql_num_rows($rrvw) == 1, 'Erreur lors de la vérification des champs de l\'avis consommateur ajouté');
            $rvw = ria_mysql_fetch_assoc($rrvw);

            
            $this->assertEquals( $name, $rvw['name'],  'Erreur: titre de l\'avis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $desc, $rvw['desc'],  'Erreur: description/contenu de l\'avis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $note, $rvw['note'],  'Erreur: note attribuée par l\'avis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $note_pkg, $rvw['note_pkg'],  'Erreur: note attribuée par l\'avis sur le colis non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $note_dlv, $rvw['note_dlv'],  'Erreur: note attribuée par l\'avis sur la livraison non conforme à la valeur lors de l\'ajout');
            
            $this->assertEquals( $prd, $rvw['prd_id'],  'Erreur: identifiant du produit concerné par l\'avis non conforme à la valeur lors de l\'ajout');
            
            if($usr == 0){
                $this->assertEquals( $usr_firstname, $rvw['usr_firstname'],  'Erreur: prénom de l\'utilisateur ayant créé l\'avis non conforme à la valeur lors de l\'ajout');
            
                $this->assertEquals( $usr_lastname, $rvw['usr_lastname'],  'Erreur: nom de famille de l\'utilisateur ayant créé l\'avis non conforme à la valeur lors de l\'ajout');
                
                $this->assertEquals( $usr_email, $rvw['usr_email'],  'Erreur: adresse email de l\'utilisateur ayant créé l\'avis non conforme à la valeur lors de l\'ajout'); 
                
                $this->assertEquals( $usr_society, $rvw['usr_society'],  'Erreur: nom de société de l\'utilisateur ayant créé l\'avis non conforme à la valeur lors de l\'ajout');
            }  
        }

        /** Fonction permettant de tester l'ajout d'un avis consommateur sur un produit avec des paramètres invalides
         * @dataProvider invalidPrdReviews
         */
        public function testPrdReviewsInvalid($prd, $usr, $name, $desc, $note, $parent, $usr_firstname, $usr_lastname, $usr_email, $usr_society, $note_dlv, $note_pkg, $error){

            $this->assertFalse(prd_reviews_add($prd, $usr, $name, $desc, $note, $parent, $usr_firstname, $usr_lastname, $usr_email, $usr_society, $note_dlv, $note_pkg), $error);
        }

        public function validReviews(){
            return array(
                //    usr lastname firstname              email          subject             desc     note parent wst_id
                array(1, 'nom', 'prenom', '<EMAIL>', 'Un sujet', 'Une description', 5, false, false)
            );
        }

        public function validPrdReviews(){
            return array(
                //   prd   usr   name                      desc     note   parent usr_firstname usr_lastname usr_email usr_society note_dlv note_pkg
                array(2,    1,  'Avis', 'Avis sur le produits 2',   null,   false,      null,    null,             null,    null,   null,    null),
                array(2,    0,  'Avis', 'Avis sur le produits 2',   null,   false,  'Prenom',   'Nom', '<EMAIL>',    null,   null,    null),
                array(2,    1,  'Avis', 'Avis sur le produits 2',      4,   false,      null,    null,             null,    null,      5,       4),
            );
        }

        public function invalidPrdReviews(){
            return array(
                //     prd    usr  name    desc     note   parent usr_firstname usr_lastname usr_email usr_society note_dlv note_pkg    message d'erreur
                array(1000,     0,  '',     '',     null,   false,  'prenom',   'nom',  '<EMAIL>',   null,   null,   null, 'Erreur: ajout d\'un avis consommateur avec un id produit invalide'),
                array(   2,     0,  '',     '',     null,   false,      null,    null,              null,   null,   null,   null, 'Erreur: ajout d\'un avis consommateur rattaché à aucun utilisateur'),
            );
        }
    }


<?php
	require_once('brands.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class brandsGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération d'une marque par son id
		 */
		public function testBrandsGetById(){
         
            $rbrd = prd_brands_get(1);
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd) == 1, 'Erreur lors de la récupération d\'une marque par son id');
            $brd = ria_mysql_fetch_assoc($rbrd);
            $this->assertEquals( 1, $brd['id'], 'Erreur lors de la récupération d\'une marque par son id');
            

            $rbrd = prd_brands_get(1000);
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd) == 0, 'Erreur: prd_brands_get retourne des informations avec un identifiant de marque invalide');   
        }  

        /** Fonction permettant de tester la récupération d'une marque par son nom
         */
        public function testBrandsGetByName(){

            $rbrd = prd_brands_get(0, false, 'Marque test 1');
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd) == 1, 'Erreur lors de la récupération d\'une marque par son nom');
            $brd = ria_mysql_fetch_assoc($rbrd);
            $this->assertEquals(1, $brd['id'], 'Erreur lors de la récupération d\'une marque par son nom'); 
            

            $rbrd = prd_brands_get(0, false, 'Marque inexistante');
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd) == 0, 'Erreur: prd_brands_get retourne des informations avec un nom de marque invalide');
        }

        /** Fonction permettant de tester la récupération du nom d'une marque
         */
        public function testBrandsGetName(){

            $this->assertEquals( 'Marque test 1', prd_brands_get_name(1) ,'Erreur: le nom de la marque ne correspond pas à la valeur dans la base de donnée');

            $this->assertFalse( prd_brands_get_name(1000), 'Erreur: prd_brand_get_name ne retourne pas faux avec un identifiant de marque inexistant');
        }

        /** Fonction permettant de tester la récupération de l'url du site internet d'une marque 
         */
        public function testBrandsGetUrl(){

            $this->assertEquals( 'http://urlalias.com/', prd_brands_get_url(1) ,'Erreur: l\'url du site internet de la marque ne correspond pas à la valeur dans la base de donnée');

            $this->assertFalse( prd_brands_get_url(1000), 'Erreur: prd_brand_get_url ne retourne pas faux avec un identifiant de marque inexistant');
        }

        /** Fonction permettant de tester la récupération des produits d'une marque
         */
        public function testBrandsGetProducts(){

            $products = prd_brands_get_prd_ids(1);
            $this->assertTrue(is_array($products) && count($products) == 3, 'Erreur: les produits lié à la marque ne correspondent pas aux valeurs dans la base de donnée');
            $this->assertTrue(in_array(1, $products) && in_array(2, $products) && in_array(3, $products), 'Erreur: les produits lié à la marque ne correspondent pas aux valeurs dans la base de donnée');
            
            
            $this->assertEquals(array(), prd_brands_get_prd_ids(1000), 'Erreur: prd_brands_get_prd_ids devrait retourné un tableau vide pour un identifiant de marque inexistant');
        }

        /** Fonction permettant de tester le nombre de produit associé à une marque
         */
        public function testBrandsCountProducts(){

            $this->assertEquals(1, prd_brands_count_products(3), 'Erreur: le nombre de produit de la marque ne correspond pas à la valeur dans la base de donnée' );

            $this->assertEquals(0, prd_brands_count_products(1000), 'Erreur: prd_brands_count_products devrait retourné 0 pour un identifiant de marque inexistant');
        }

        /** Fonction permettant de tester la recherche de marques
         */
        public function testBrandsSearch(){

            $brd = prd_brands_search('Marque');

            $this->assertTrue( is_array($brd) && sizeof($brd) == 4 && in_array(1, $brd) && in_array(2, $brd) && in_array(3, $brd) && in_array(4, $brd), 'Erreur lors de la récupération des marques avec un filtre sur le nom');

            $brd = prd_brands_search('Marque test 1');

            $this->assertTrue( is_array($brd) && sizeof($brd) == 1 && in_array(1, $brd), 'Erreur lors de la récupération des marques avec un filtre sur le nom');
        
        }

        /** Fonction permettant de tester l'éxistance d'une marque
         */
        public function testBrandsExists(){

            $this->assertTrue(prd_brands_exists(1) ,'Erreur: prd_brands_exists retourne faux avec un id de marque éxistant');

            $this->assertFalse(prd_brands_exists(10000), 'Erreur: prd_brands_exists renvoie vrai avec un identifiant de marque inéxistant');
        }
	}

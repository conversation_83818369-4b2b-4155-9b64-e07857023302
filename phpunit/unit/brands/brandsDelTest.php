<?php
	require_once('brands.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class brandsDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'une marque
		 */
		public function testBrandsDel() {

			$this->assertTrue(prd_brands_del(3), 'Erreur lors de la suppression d\'une marque');

			$this->assertTrue(prd_brands_del(4, true), 'Erreur lors de la suppression d\'une marque et de ses produits');
		}

		/** Fonction permettant de vérifier la suppression d'une marque
		 */
		public function testBrandsVerifyDel(){

			$rbrd = prd_brands_get(3);
			$this->assertTrue($rbrd && ria_mysql_num_rows($rbrd) == 0, 'Erreur lors de la vérification de la suppression de la marque');

			$rbrd = prd_brands_get(4);
			$this->assertTrue($rbrd && ria_mysql_num_rows($rbrd) == 0, 'Erreur lors de la vérification de la suppression de la marque');

			$rprd = prd_products_get(6);
			$this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur: les produits de la marque n\'ont pas été supprimé');
		}
	}

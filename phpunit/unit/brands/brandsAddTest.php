<?php
	require_once('brands.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class brandsAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'ajout d'une nouvelle marque
		 * @dataProvider validBrands
		 */
		public function testValidBrandsAdd($name, $title, $url, $is_sync, $desc, $publish) {

			$brd_id = prd_brands_add($name, $title, $url, $is_sync, $desc, $publish);

			//Vérifie que l'id est un integer
			$this->assertThat($brd_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
			), "Erreur: prd_brands_add retourne un id invalide");

			//Vérifie que les champs sont corrects
			$rbrd = prd_brands_get($brd_id);
			$this->assertTrue($rbrd && ria_mysql_num_rows($rbrd) == 1, 'Erreur lors de la vérification des champs de la marque ajoutéee');
			$brd = ria_mysql_fetch_assoc($rbrd);
		
			$this->assertEquals( $name, $brd['name'], 'Erreur: nom de la marque non conforme à la valeur lors de l\'ajout' );
			
			if($title == ''){
				$this->assertEquals( $name, $brd['title'], 'Erreur: titre de la marque non conforme à la valeur lors de l\'ajout' );
			}else{
				$this->assertEquals( $title, $brd['title'], 'Erreur: titre de la marque non conforme à la valeur lors de l\'ajout' );
			}
						
			$this->assertEquals( $url, $brd['url'], 'Erreur: url de la marque non conforme à la valeur lors de l\'ajout' );
			
			$this->assertTrue( $is_sync == $brd['is_sync'], 'Erreur: propriété is_sync de la marque non conforme à la valeur lors de l\'ajout' );
			
			$this->assertEquals( $desc, $brd['desc'], 'Erreur: description de la marque non conforme à la valeur lors de l\'ajout' );
			
			$this->assertTrue( $publish == $brd['publish'], 'Erreur: propriété publish de la marque non conforme à la valeur lors de l\'ajout' );
		}

		public function validBrands(){
			return array(
                //                   name           title             url   is_sync         desc          publish
                array('Nouvelle marque 1',            '',             '',     false,          '',         true),
                array('Nouvelle marque 2',       'Titre',          'url',      true,      'Desc',        false),
			);
		}
	}

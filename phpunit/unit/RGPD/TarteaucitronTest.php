<?php
require_once('RGPD/autoload.php');

use RGPD\Tarteaucitron;
/**
 *@backupGlobals disabled
 */
class TarteaucitronTest extends PHPUnit_Framework_TestCase
{
	public function testSerializeConfig()
    {
		$Tarteaucitron = new Tarteaucitron();

		$config = $Tarteaucitron->serializeConfig();
		$generated_config = json_decode($config, true);
		$expected_config = array (
			'privacyUrl' => '',
			'hashTag' => '#tarteaucitron',
			'cookieName' => 'tartaucitron',
			'orientation' => 'top',
			'showAlertSmall' => true,
			'cookieslist' => true,
			'adblocker' => false,
			'AcceptAllCta' => true,
			'highPrivacy' => false,
			'handleBrowserDNTRequest' => false,
			'removeCredit' => true,
			'moreInfoLink' => true,
			'useExternalCss' => false,
			'cookieDomain' => '',
			'readmoreLink' => '/cookiespolicy',
		);

        $this->assertEquals($expected_config, $generated_config);
	}

	public function testAddTracker()
	{
		$Tarteaucitron = new Tarteaucitron();
		$tracker = $this->getMockTracker();
		$Tarteaucitron->addTracker(
			$tracker
		);

		$this->assertContains($tracker, $Tarteaucitron->getTrackers());
	}

	public function testLangConfiguration()
	{
		$Tarteaucitron = new Tarteaucitron();
		$Tarteaucitron->addTracker(
			$this->getMockTracker()
		);
		$html = $Tarteaucitron->init();

		$this->assertContains('tarteaucitronForceLanguage = "fr";', $html);
	}

	public function testTarteaucitronCustomText()
	{
		$Tarteaucitron = new Tarteaucitron();
		$Tarteaucitron->addTracker(
			$this->getMockTracker()
		);
		$Tarteaucitron->withCustomText(array(
			'alertBigScroll' => 'TEST'
		));
		$html = $Tarteaucitron->init();

		$this->assertContains('tarteaucitronCustomText', $html);
		// test la présence du json avec le nouveau texte
		$this->assertContains('{"alertBigScroll":"TEST"}', $html);
	}

	public function testInitWithoutTrackers()
    {
		$Tarteaucitron = new Tarteaucitron();

		$html = $Tarteaucitron->init();

		$this->assertEquals('', $html);
	}

	public function testInitWithTrackers()
	{
		$Tarteaucitron = new Tarteaucitron();
		$TarteaucitronTrackerInterface = $this->getMockTracker();
		$Tarteaucitron->addTracker($TarteaucitronTrackerInterface);
		$html = $Tarteaucitron->init();

		$this->assertContains('<script src="/js/tarteaucitron/tarteaucitron.js"></script>', $html);
		// vérification qu'il n'y est pas de text personnalisé car non configuré
		$this->assertFalse(strstr( $html, 'tarteaucitronCustomText'));
		// vérification qu'il n'y est pas de style supplémentaire car il n'est pas configuré
		$this->assertFalse(strstr( $html, 'window.addEventListener("load", function () {'));
		$this->assertFalse(strstr( $html, 'linkElement = document.createElement("link");'));
		$this->assertFalse(strstr( $html, 'linkElement.rel = "stylesheet";'));
	}

	public function testInitWithDifferentJS()
	{
		$Tarteaucitron = new Tarteaucitron();
		$Tarteaucitron->withJsUrl('/js/tarteaucitron.js');
		$Tarteaucitron->addTracker(
			$this->getMockTracker()
		);
		$html = $Tarteaucitron->init();

		$this->assertContains('<script src="/js/tarteaucitron.js"></script>', $html);
	}

	public function testInitWithDifferentCss()
	{
		$Tarteaucitron = new Tarteaucitron();
		$Tarteaucitron->withOverrideCss('/css/tarteaucitron.css');
		$Tarteaucitron->addTracker(
			$this->getMockTracker()
		);
		$html = $Tarteaucitron->init();

		// vérification qu'il n'y est pas de style supplémentaire car il n'est pas configuré
		$this->assertContains('window.addEventListener("load", function', $html);
		$this->assertContains('linkElement = document.createElement("link");', $html);
		$this->assertContains('linkElement.rel = "stylesheet";', $html);
		$this->assertContains('linkElement.href = "/css/tarteaucitron.css";', $html);
		$this->assertContains('document.getElementsByTagName("head")[0].appendChild(linkElement);', $html);
		$this->assertContains('});', $html);
	}

	private function getMockTracker()
	{
		return $this->getMock('RGPD\Trackers\TarteaucitronTrackerInterface');
	}
}
<?php

require_once('orders.inc.php');

/**
 * @backupGlobals disabled
 */
class ordersSetRefTest extends PHPUnit_Framework_TestCase
{
	/**
	 * Test les contrôles de paramètres pour la fonction "ord_products_set_ref".
	 *
	 * @dataProvider invalidParameters
	 *
	 * @param  int    $orderId
	 * @param  int    $productId
	 * @param  int    $lineId
	 * @param  string $reference
	 * @return void
	 */
	public function testInvalidParametersWillReturnFalse($orderId, $productId, $lineId, $reference)
	{
		$this->assertFalse(
			ord_products_set_ref($orderId, $productId, $lineId, $reference)
		);
	}

	/**
	 * Test la fonction "ord_products_set_ref".
	 *
	 * @dataProvider validParameters
	 *
	 * @param  int    $orderId
	 * @param  int    $productId
	 * @param  int    $lineId
	 * @param  string $reference
	 * @return void
	 */
	public function testOrderSetRef($orderId, $productId, $lineId, $reference)
	{
		$this->assertTrue(
			ord_products_set_ref($orderId, $productId, $lineId, $reference)
		);
	}

	/**
	 * Fournit des paramètres valides pour tester la fonction "ord_products_set_ref".
	 *
	 * @return array
	 */
	public function validParameters()
	{
		return array(
			array(1, 1, 0, 'NEW_REF'),
		);
	}

	/**
	 * Fournit des paramètres invalides pour tester la fonction "ord_products_set_ref".
	 * 		- 1ère ligne : Mauvais identifiant de commande
	 * 		- 2ème ligne : Mauvais identifiant produit
	 * 		- 3ème ligne : Mauvais identifiant ligne
	 * 		- 4ème ligne : Mauvaise référence
	 *
	 * @return array
	 */
	public function invalidParameters()
	{
		return array(
			array(0, 1, 0, 'NEW_REF'),
			array(1, 0, 0, 'NEW_REF'),
			array(1, 1, 'WRONG_LINE_ID', 'NEW_REF'),
			array(1, 1, 0, ''),
		);
	}
}
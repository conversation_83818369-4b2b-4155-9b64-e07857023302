<?php
	require_once('orders.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class ordersUpdateTotalTest extends PHPUnit_Framework_TestCase {

        /** Cette fonction permet de tester la mise à jour du prix total d'une commande
         */
        public function testOrdersUpdateTotal(){
            
            $this->assertTrue(ord_orders_update_totals(3), 'Erreur: la mise à jour du prix total de la commande ne c\'est pas éfectué');

            $this->assertEquals(125, ord_orders_get_total(3), 'Erreur: le prix total de la commande n\'a pas été correctement mis à jour');
        }
		
	}

<?php 
	require_once('prd/colisage.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class colisageDelTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester la suppression d'un conditionnement
         */
        public function testColisageDel(){

            $this->assertTrue(prd_colisage_types_del(2), 'Erreur lors de la suppression du conditionnement');
        }

        /** Fonction permettant de vérifier la suppression d'un conditionnement
         */
        public function testColisageVerifyDel(){

            $this->assertTrue( false == prd_colisage_types_exists(2), 'Erreur lors de la vérification de la suppression du conditionnement');
        }
    }

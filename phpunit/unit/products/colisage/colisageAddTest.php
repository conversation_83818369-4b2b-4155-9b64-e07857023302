<?php 
	require_once('prd/colisage.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class colisageAddTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester l'ajout d'un conditionnement valide
         * @dataProvider validColisage
         */
        public function testColisageValideAdd($name, $qte, $is_sync, $dps_id, $pkg_id){

            $id = prd_colisage_types_add($name, $qte, $is_sync, $dps_id, $pkg_id);
            
            $this->assertTrue( true == $id, 'prd_colisage_type_add retourne un id invalide');
        
            // Vérifie que les champs sont corrects
            $rcol = prd_colisage_types_get($id);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la vérification des champs du conditionnement ajouté');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( $name, $col['name'], 'Erreur: nom du conditionnement non conforme à la valeur lors de l\'ajout');

            $this->assertEquals( $qte, $col['qte'], 'Erreur: quantité du conditionnement non conforme à la valeur lors de l\'ajout');

            $this->assertTrue( $is_sync == $col['is_sync'], 'Erreur: propriété is_sync du conditionnement non conforme à la valeur lors de l\'ajout');

            $this->assertEquals( $dps_id, $col['dps_id'], 'Erreur: dépot associé au conditionnement non conforme à la valeur lors de l\'ajout');

            $this->assertEquals( $pkg_id, $col['pkg_id'], 'Erreur: package associé au conditionnement non conforme à la valeur lors de l\'ajout');
        }

         /** Fonction permettant de tester l'ajout d'un conditionnement invalide
         * @dataProvider invalidColisage
         */
        public function testColisageInvalideAdd($name, $qte, $is_sync, $dps_id, $pkg_id, $error){
            
            $this->assertFalse( prd_colisage_types_add($name, $qte, $is_sync, $dps_id, $pkg_id), $error);
        }

        public static function validColisage(){
            return array(
                //                name              qte   is_sync dps_id pkg_id
                array( 'un conditionnement de test',  5,    false,  null,   null),
            );
        }

        public static function invalidColisage(){
            return array(
                //               name                 qte is_sync dps_id pkg_id        message d'erreur
                array(                           '',    5,  false,  null,   null, 'Erreur: ajout d\'un conditionnement avec un nom invalide'),
                array( 'un conditionnement de test',    0,  false,  null,   null, 'Erreur: ajout d\'un conditionnement avec une quantité invalide'),
            );
        }
    }

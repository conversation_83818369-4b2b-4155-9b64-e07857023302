<?php
	require_once('products.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class productsDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'un produit
		 */
		public function testProductsDel() {

            $this->assertTrue(prd_products_del(100), 'Erreur: le produit n\'a pas été archivé');
		}

		/** Fonction permettant de tester la suppression d'un produit
		 */
		public function testProductsVerifyDel(){

			$rprd = prd_products_get(100);
			$this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur lors de la vérification de l\'archivage du produit');
		}
	}

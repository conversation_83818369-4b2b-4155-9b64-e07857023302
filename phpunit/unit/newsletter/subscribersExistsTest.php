<?php
	require_once('newsletter.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class subscribersExistsTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la vérification d'un identifiant d'inscription
		 */
		public function testSubscribersExists() {

            $this->assertTrue(true == nlr_subscribers_exists(1), 'Erreur : nlr_subscrivers_exists retourne faux avec un identifant éxistant');
            $this->assertTrue(false == nlr_subscribers_exists(10000), 'Erreur : nlr_subscrivers_exists retourne vrai avec un identifiant inéxistant');
		}
	}

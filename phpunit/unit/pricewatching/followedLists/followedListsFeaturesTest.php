<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\PriceWatching\models\LinearRaised\Features\FollowedList;

/**
 * @group followedListsFeaturesTest
 * @backupGlobals disabled
 */
class followedListsFeaturesTest extends PHPUnit_Framework_TestCase {
	private $FollowedList;
	private $id;

	public function setUp() {
		$this->id = prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO);
		$this->FollowedList = new FollowedList($this->id);
		foreach($this->oldFollowedUsersProvider() as $user) {
			prw_followed_users::add(
				$this->id,
				$user['prf_id'],
				$user['id']
			);
		}
		foreach($this->oldFollowedProductsProvider() as $product) {
			prw_followed_products::add(
				$product['id'],
				$this->id,
				$product['rank'],
				$product['pmc'],
				$product['is_cpt']
			);
		}
	}

	public function testBatchUpdateUsers() {
		$users = $this->newFollowedUsersProvider();
		$result = $this->FollowedList->batchUpdateUsers($users);
		$this->assertTrue($result);
		$result = prw_followed_users::get($this->id);
		$this->assertTrue(ria_mysql_control_ressource($result));

		$this->assertEquals(count($users), ria_mysql_num_rows($result));
	}

	public function testBatchUpdateProducts() {
		$products = $this->newFollowedProductsProvider();
		$result = $this->FollowedList->batchUpdateProducts($products);
		$this->assertTrue($result);
		$result = prw_followed_products::get($this->id);
		$this->assertTrue(ria_mysql_control_ressource($result));

		$this->assertEquals(count($products), ria_mysql_num_rows($result));
	}

	public function oldFollowedUsersProvider() {
		return array(
			array('prf_id' => 3, 'id' => 34),
			array('prf_id' => 2, 'id' => 35),
			array('prf_id' => 3, 'id' => 36),
		);
	}

	public function oldFollowedProductsProvider() {
		return array(
			array('id' => 1, 'rank' => 1, 'pmc' => 10, 'is_cpt' => false),
			array('id' => 8, 'rank' => 2, 'pmc' => 6.08, 'is_cpt' => true),
			array('id' => 80, 'rank' => 3, 'pmc' => 12.8, 'is_cpt' => false),
			array('id' => 900, 'rank' => 4, 'pmc' => 12, 'is_cpt' => true),
			array('id' => 25887, 'rank' => 5, 'pmc' => 7.09, 'is_cpt' => false),
		);
	}

	public function newFollowedProductsProvider() {
		return array(
			array('id' => 1, 'rank' => 1, 'pmc' => 10, 'is_cpt' => false),
			array('id' => 8, 'rank' => 2, 'pmc' => 6.08, 'is_cpt' => true),
			array('id' => 80, 'rank' => 3, 'pmc' => 12.8, 'is_cpt' => false),
			array('id' => 900, 'rank' => 4, 'pmc' => 12, 'is_cpt' => true),
			array('id' => 25887, 'rank' => 5, 'pmc' => 7.09, 'is_cpt' => false),
			array('id' => 2, 'rank' => 6, 'pmc' => 10, 'is_cpt' => false),
			array('id' => 9, 'rank' => 7, 'pmc' => 6.08, 'is_cpt' => true),
			array('id' => 81, 'rank' => 8, 'pmc' => 12.8, 'is_cpt' => false),
			array('id' => 901, 'rank' => 9, 'pmc' => 12, 'is_cpt' => true),
			array('id' => 25887, 'rank' => 10, 'pmc' => 7.09, 'is_cpt' => false),
		);
	}

	public function newFollowedUsersProvider() {
		return array(
			array('prf_id' => 3, 'id' => 34),
			array('prf_id' => 2, 'id' => 35),
			array('prf_id' => 3, 'id' => 36),
			array('prf_id' => 5, 'id' => 37),
			array('prf_id' => 3, 'id' => 38),
			array('prf_id' => 3, 'id' => 39),
			array('prf_id' => 3, 'id' => 40),
			array('prf_id' => 1, 'id' => 41),
			array('prf_id' => 3, 'id' => 42),
		);
	}
}
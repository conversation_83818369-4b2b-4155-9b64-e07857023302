<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;

/**
 * @group followedListsTest
 * @backupGlobals disabled
 */
class followedListsCRUDTest extends PHPUnit_Framework_TestCase {
	public static $valid_ids = array();

	public static $inserted_ids = array();

	public static $valid_list = array('name' => 'Episserie', 'type' => prw_followed_lists::TYPE_YUTO, 'is_published' => false);

	public static $validProvider = array(
		array('name' => 'Episserie', 'type' => prw_followed_lists::TYPE_YUTO, 'is_published' => false),
		array('name' => 'Surgelés', 'type' => prw_followed_lists::TYPE_YUTO, 'is_published' => true),
		array('name' => 'Boissons', 'type' => prw_followed_lists::TYPE_YUTO, 'is_published' => true),
		array('name' => 'Produits ménagés', 'type' => prw_followed_lists::TYPE_WEB, 'is_published' => false)
	);

	/**
	 * @dataProvider validListProvider
	 */
	public function testAddingValidFollowedListBatch($name, $type, $publish) {
		$id = prw_followed_lists::add($name, $type, $publish);
		$this->assertTrue( (is_numeric($id) && $id > 0), "Erreur lors de l'ajout d'un assortiment valide.");
	}

	public function testAddingValidFollowedList() {
		$id = call_user_func_array(
			array('Riashop\PriceWatching\models\LinearRaised\prw_followed_lists','add'),
			self::$valid_list
		);
		$this->assertTrue( (is_numeric($id) && $id > 0), "Erreur lors de l'ajout d'un assortiment valide.");
		return $id;
	}
	/**
	 * @depends testAddingValidFollowedList
	 */
	public function testGetFollowedListByValidId($id) {
		$result = prw_followed_lists::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur : Le résultat de la requète ne devrait pas être vide");
		$list = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $list, "Erreur le résultat ne contient pas de clé id.");
		$this->assertArrayHasKey('name', $list, "Erreur le résultat ne contient pas de clé name.");
		$this->assertArrayHasKey('type', $list, "Erreur le résultat ne contient pas de clé type.");
		$this->assertArrayHasKey('is_published', $list, "Erreur le résultat ne contient pas de clé is_published.");
		$this->assertArrayHasKey('fls_id', $list, "Erreur le résultat ne contient pas de clé fls_id.");
		$this->assertArrayHasKey('date_created', $list, "Erreur le résultat ne contient pas de clé date_created.");
		$this->assertArrayHasKey('date_modified', $list, "Erreur le résultat ne contient pas de clé date_modified.");

		$this->assertEquals($list['id'], $id, "Erreur l'identifiant de recherche ne correspond pas à l'identifiant retourné");

		$valid = self::$valid_list;

		$this->assertEquals($list['name'], $valid['name'], "Erreur la valeur de la clé name ne correspond pas a celle inséré.");
		$this->assertEquals($list['type'], ($valid['type'] == prw_followed_lists::TYPE_YUTO ?'1':'0'), "Erreur la valeur de la clé type ne correspond pas a celle inséré.");
		$this->assertEquals($list['is_published'], ($valid['is_published']?'1':'0'), "Erreur la valeur de la clé is_published ne correspond pas a celle inséré.");

		return $id;
	}
	/**
	 * @depends testGetFollowedListByValidId
	 */
	public function testUpdateFollowedListByValidId($id) {
		$name = 'Boissons';
		$type = prw_followed_lists::TYPE_WEB;
		$fls_id = 8;
		$result = prw_followed_lists::update($id, null, $type);
		$this->assertTrue($result, "Erreur de la mise à jour");
		$result = prw_followed_lists::update($id, $name, null);
		$this->assertTrue($result, "Erreur de la mise à jour");
		$result = prw_followed_lists::update($id, null, null, $fls_id);
		$this->assertTrue($result, "Erreur de la mise à jour");
		$result = prw_followed_lists::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur : Le résultat de la requète ne devrait pas être vide");
		$list = ria_mysql_fetch_assoc($result)	;
		$valid = self::$valid_list;
		$this->assertEquals($list['id'], $id, "Erreur l'identifiant de recherche ne correspond pas à l'identifiant retourné");
		$this->assertEquals($list['name'], $name, "Erreur la valeur de la clé name ne correspond pas a celle inséré.");
		$this->assertEquals($list['type'], ($type == prw_followed_lists::TYPE_YUTO ?prw_followed_lists::TYPE_YUTO:prw_followed_lists::TYPE_WEB), "Erreur la valeur de la clé type ne correspond pas a celle inséré.");
		$this->assertEquals($list['is_published'], ($valid['is_published']?'1':'0'), "Erreur la valeur de la clé is_published ne correspond pas a celle inséré.");
		$this->assertEquals($list['fls_id'], $fls_id);

		return $id;
	}
	/**
	 * @depends testUpdateFollowedListByValidId
	 */
	public function testFailUpdateFollowedListByValidId($id) {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_lists::update($id);

		return $id;
	}
	/**
	 * @depends testUpdateFollowedListByValidId
	 */
	public function testSetDateModifiedFollowedListsByValidId($id) {
		$result = prw_followed_lists::setDateModified($id);
		$this->assertTrue($result, "Erreur de la mise à jour de la date de modification");
		return $id;
	}
	/**
	 * @depends testSetDateModifiedFollowedListsByValidId
	 */
	public function testPublishFollowedList($id) {
		$result = prw_followed_lists::publish($id);
		$this->assertTrue($result, "Erreur de la mise à jour de la publication");
		$result = prw_followed_lists::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur : Le résultat de la requète ne devrait pas être vide");
		$list = ria_mysql_fetch_assoc($result);
		$this->assertEquals($list['id'], $id, "Erreur l'identifiant de recherche ne correspond pas à l'identifiant retourné");
		$this->assertEquals($list['is_published'], '1', "Erreur la valeur de la clé is_published ne correspond pas a celle inséré.");
		return $id;
	}
	/**
	 * @depends testPublishFollowedList
	 */
	public function testUnPublishFollowedList($id) {
		$result = prw_followed_lists::publish($id, false);
		$this->assertTrue($result, "Erreur de la mise à jour de la dépublication");
		$result = prw_followed_lists::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur : Le résultat de la requète ne devrait pas être vide");
		$list = ria_mysql_fetch_assoc($result);
		$this->assertEquals($list['id'], $id, "Erreur l'identifiant de recherche ne correspond pas à l'identifiant retourné");
		$this->assertEquals($list['is_published'], '0', "Erreur la valeur de la clé is_published ne correspond pas a celle inséré.");

		return $id;
	}
	/**
	 * @depends testUnPublishFollowedList
	 */
	/* public function testSetSectionOnFollowedList($id) {
		prw_followed_lists::setSection($id, )
	} */

	/**
	 * @depends testUnPublishFollowedList
	 */
	public function testDeleteFollowedListByValidId($id) {
		$result = prw_followed_lists::delete($id);
		$this->assertTrue($result, "Erreur la suppression a eu une erreur");
		$result = prw_followed_lists::get($id);
		$this->assertNotTrue(ria_mysql_control_ressource($result), "Erreur : Le résultat de la requète ne devrait pas être vide");
	}

	public function testGetListByFlsId() {
		$fls_id = 8;
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, $fls_id);
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, 5);
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, $fls_id);
		$result = prw_followed_lists::get(null, null, null, $fls_id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(2, ria_mysql_num_rows($result));
	}
	/**
	 * @group failed
	 */
	public function testFailSetDateModifiedFollowedListsByValidId() {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_lists::setDateModified('testste');
	}

	/**
	 * @group failed
	 * @dataProvider invalidListProvider
	 */
	public function testAddingInvalidFollowedList($name, $type, $is_published) {
		$this->setExpectedException('InvalidArgumentException');
		$id = prw_followed_lists::add($name, $type, $is_published);
	}

	/**
	 * @group failed
	 * @dataProvider invalidListIdsProvider
	 */
	public function testFailGetInvalidFollowedListById($id) {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_lists::get($id);
		$this->assertEmpty($result, "Erreur : Le résultat doit être vide car auccun résultat ne devrais correspondre");
	}
	/**
	 * @dataProvider invalidGetListProvider
	 */
	public function testFailGetInvalidFollowedList($id, $is_publish, $type, $fls_id) {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_lists::get($id, $is_publish, $type, $fls_id);
	}

	public function validListProvider() {
		return self::$validProvider;
	}

	public function invalidListProvider() {
		return array(
			array('name' => 'Episserie', 'type' => 404, 'is_published' => true),
			array('name' => false, 'type' => prw_followed_lists::TYPE_YUTO, 'is_published' => true),
			array('name' => 'Boissons', 'type' => prw_followed_lists::TYPE_WEB, 'is_published' => null),
			array('name' => false, 'type' => 'bark', 'is_published' => 404)
		);
	}

	public function invalidListIdsProvider() {
		return array(
			'DoesNotExists' => array(array()),
			'NotNumeric' => array('test'),
			'boolean' => array(true),
			'ArrayNotInts' => array(array('test', 999999))
		);
	}

	function invalidGetListProvider() {
		return array(
			array(null, array(), prw_followed_lists::TYPE_YUTO, null),
			array(null, null, prw_followed_lists::TYPE_YUTO, 'test'),
			array(null, 8, null, 'test'),
		);
	}

	public function insertedIds() {
		return self::$inserted_ids;
	}
}
<?php
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products as LinearRaisedProduct;
/**
 * @group followedProductsTest
 * @backupGlobals disabled
 */
class followedProductsCRUDTest extends PHPUnit_Framework_TestCase {

	private $inserted_id_list = array();

	private $expected_counts = array(
		4 => 4,
		5 => 2,
	);
	/**
	 * @dataProvider valideFollowedProductsProvider
	 */
	public function testAddFollowedProduct($prd_id, $pfl_id, $rank, $pmc, $is_from_competition ) {
		$id = LinearRaisedProduct::add($prd_id, $pfl_id, $rank, $pmc, $is_from_competition);
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout");
		$this->inserted_id_list[] = $id;
	}

	/**
	 * @dataProvider invalideFollowedProductsProvider
	 */
	public function testFailAddFollowedProduct($prd_id, $pfl_id, $rank, $pmc, $is_from_competition ) {
		$this->setExpectedException('InvalidArgumentException');
		$id = LinearRaisedProduct::add($prd_id, $pfl_id, $rank, $pmc, $is_from_competition);
		$this->assertNotTrue(is_numeric($id) && $id > 0, "Erreur lors de la vérification des paramètre");
	}
	/**
	 * @dataProvider validPflIds
	 */
	public function testGetFollowedProduct($pfl_id) {
		$result = LinearRaisedProduct::get($pfl_id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrait pas être vide");
		$expected_count = $this->expected_counts[$pfl_id];
		$this->assertEquals($expected_count, ria_mysql_num_rows($result), "Erreur il devrais avoir ".$expected_count." ligne");
		$one = ria_mysql_fetch_assoc($result);

		$this->assertArrayHasKey('id', $one);
		$this->assertArrayHasKey('cpt_id', $one);
		$this->assertArrayHasKey('cpt_ref', $one);
		$this->assertArrayHasKey('disable', $one);
		$this->assertArrayHasKey('prd_id', $one);
		$this->assertArrayHasKey('pfl_id', $one);
		$this->assertArrayHasKey('rank', $one);
		$this->assertArrayHasKey('pmc', $one);
		$this->assertArrayHasKey('is_cpt', $one);
		$this->assertArrayHasKey('date_created', $one);
		$this->assertArrayHasKey('date_modified', $one);
		$this->assertArrayHasKey('date_lastcheck', $one);
	}

	public function testFAilGetFollowedProduct() {
		$result = LinearRaisedProduct::get(34);

		$this->assertNotTrue($result, "Erreur le résultat devrait être vide");
	}

	/**
	 * @dataProvider validPflIds
	 */
	public function testDeleteFollowedProduct($pfl_id) {
		$result = LinearRaisedProduct::delete($pfl_id);
		$this->assertTrue($result, "Erreur de la suppression des ligne");
		$result = LinearRaisedProduct::get($pfl_id);
		$this->assertEmpty($result, "Erreur le résultat devrait être vide du a la suppression");
	}

	public function valideFollowedProductsProvider() {
		return array(
			array('prd_id' => 3, 'pfl_id' => 4, 'rank' => 1, 'pmc' => 12.50, 'is_comp' => false),
			array('prd_id' => 4, 'pfl_id' => 4, 'rank' => 2, 'pmc' => 12, 'is_comp' => true),
			array('prd_id' => 9, 'pfl_id' => 4, 'rank' => 3, 'pmc' => 13, 'is_comp' => false),
			array('prd_id' => 8, 'pfl_id' => 4, 'rank' => 4, 'pmc' => 11, 'is_comp' => false),
			array('prd_id' => 3, 'pfl_id' => 5, 'rank' => 1, 'pmc' => 12.50, 'is_comp' => false),
			array('prd_id' => 4, 'pfl_id' => 5, 'rank' => 2, 'pmc' => 12, 'is_comp' => true),
		);
	}

	public function invalideFollowedProductsProvider() {
		return array(
			array('prd_id' => 'test', 'pfl_id' => 4, 'rank' => 1, 'pmc' => 12.50, 'is_comp' => false),
			array('prd_id' => 4, 'pfl_id' => 'estst', 'rank' => 2, 'pmc' => 12, 'is_comp' => true),
			array('prd_id' => 9, 'pfl_id' => 4, 'rank' => null, 'pmc' => 13, 'is_comp' => false),
			array('prd_id' => 8, 'pfl_id' => 4, 'rank' => 4, 'pmc' => 'null', 'is_comp' => false),
			array('prd_id' => 3, 'pfl_id' => 5, 'rank' => 1, 'pmc' => 12.50, 'is_comp' => null),
			array('prd_id' => 'dqsf', 'pfl_id' => 654, 'rank' => null, 'pmc' => 'tes', 'is_comp' => array()),
		);
	}

	public function validPflIds() {
		return array(
			array(4),
			array(5),
		);
	}
}
<?php

use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;

/**
 * @group linearRaisedTest
 * @backupGlobals disabled
 */
class linearRaisedCRUDTest extends PHPUnit_Framework_TestCase {
	/**
	 * @dataProvider validLinearRaisedProvider
	 */
	public function testAddLinearRaised($seller_id, $usr_id, $pfl_id, $groupe_id, $total = null, $total_section = null, DateTime $date=null) {
		$id = prw_linear_raised::add($seller_id, $usr_id, $pfl_id, $groupe_id, $total, $total_section, $date);
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un relevé lineaire");
	}

	public function testGetLinearRaised() {
		$inserted_list = array();
		foreach($this->validLinearRaisedProvider() as $params) {
			$inserted_list[] = call_user_func_array(
				array('Riashop\PriceWatching\models\LinearRaised\prw_linear_raised',"add"),
				$params
			);
		}
		$result = prw_linear_raised::get($inserted_list);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrais pas être vide");
		$this->assertEquals(4, ria_mysql_num_rows($result), "Erreur il devrais y avoir 4 ligne");

		$one = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $one);
		$this->assertArrayHasKey('author_id', $one);
		$this->assertArrayHasKey('usr_id', $one);
		$this->assertArrayHasKey('pfl_id', $one);
		$this->assertArrayHasKey('group_id', $one);
		$this->assertArrayHasKey('total_dn', $one);
		$this->assertArrayHasKey('total_dn_section', $one);
		$this->assertArrayHasKey('date_created', $one);
		$this->assertArrayHasKey('date_modified', $one);
	}

	public function testDeleteLinearRaised() {
		$inserted_list = array();
		foreach($this->validLinearRaisedProvider() as $params) {
			$inserted_list[] = call_user_func_array(
				array('Riashop\PriceWatching\models\LinearRaised\prw_linear_raised',"add"),
				$params
			);
		}
		$result = prw_linear_raised::delete($inserted_list);
		$this->assertTrue($result, "Erreur à la suppression");
		$result = prw_linear_raised::get($inserted_list);
		$this->assertNotTrue(ria_mysql_control_ressource($result), "Erreur le résultat devrais être vide du a la suppression");
	}

	public function testFailUpdateLinearRaised() {
		$this->setExpectedException('Exception');
		prw_linear_raised::update(900);
	}
	public function testFailUpdateLinearRaisedWithBadParameters() {
		$this->setExpectedException('InvalidArgumentException');
		prw_linear_raised::update(900, 'test', array());
	}

	public function testUpdateLinearRaised() {
		$id = prw_linear_raised::add(80, 50, 1, 2, 10, 20, new DateTime());
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un relevé lineaire");
		$author = 3;
		$usr_id = 2;
		$pfl_id = 4;
		$group_id = 2;
		$total_dn = 0;
		$total_dn_section = 25;

		$result = prw_linear_raised::update($id, $author);
		$this->assertTrue($result);

		$result = prw_linear_raised::update($id,null,$usr_id);
		$this->assertTrue($result);

		$result = prw_linear_raised::update($id,null,null,$pfl_id);
		$this->assertTrue($result);

		$result = prw_linear_raised::update($id,null,null,null,$group_id);
		$this->assertTrue($result);
		$result = prw_linear_raised::update($id,null,null,null,null,$total_dn);
		$this->assertTrue($result);
		$result = prw_linear_raised::update($id,null,null,null,null,null,$total_dn_section);
		$this->assertTrue($result);

		$result = prw_linear_raised::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrais pas être vide");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertEquals($author, $one['author_id']);
		$this->assertEquals($usr_id, $one['usr_id']);
		$this->assertEquals($pfl_id, $one['pfl_id']);
		$this->assertEquals($group_id, $one['group_id']);
		$this->assertEquals($total_dn, $one['total_dn']);
		$this->assertEquals($total_dn_section, $one['total_dn_section']);
	}

	public function validLinearRaisedProvider() {
		return array(
			array('seller_id' => 45646, 'usr_id' => 79879, 'pfl_id' => 4, 'group' => 3),
			array('seller_id' => 45646, 'usr_id' => 8482, 'pfl_id' => 4, 'group' => 3, 'total' => 90, 'total_section' => 70),
			array('seller_id' => 45646, 'usr_id' => 79879, 'pfl_id' => 4, 'group' => null, 'total' => null, 'total_section' => 70, 'date' => new DateTime('2016-05-09')),
			array('seller_id' => 45646, 'usr_id' => 79879, 'pfl_id' => 4, 'group' => null),
		);
	}
}
<?php

/**
 * @backupGlobals disabled
 */
class findNearestDateInArrayTest extends PHPUnit_Framework_TestCase
{
	/** @test */
	public function test_find_nearest_date_in_array_function()
	{
		$dates = array(
			date('Y-m-d', strtotime('1 week')),
			date('Y-m-d', strtotime('2 weeks')),
			date('Y-m-d', strtotime('1 month')),
			date('Y-m-d', strtotime('2 months')),
			date('Y-m-d', strtotime('6 months')),
			date('Y-m-d', strtotime('1 year')),
		);

		$this->assertEquals($dates[0], find_nearest_date_in_array($dates, date('Y-m-d')));
		$this->assertEquals($dates[2], find_nearest_date_in_array($dates, date('Y-m-d', strtotime('5 weeks'))));
		$this->assertEquals($dates[4], find_nearest_date_in_array($dates, date('Y-m-d', strtotime('5 months'))));
		$this->assertEquals($dates[5], find_nearest_date_in_array($dates, date('Y-m-d', strtotime('2 years'))));
	}

	/** @test */
	public function passing_an_empty_array_is_throwing_an_exception()
	{
		$this->setExpectedException(InvalidArgumentException::class);

		find_nearest_date_in_array(array(), date('Y-m-d'));
	}
}

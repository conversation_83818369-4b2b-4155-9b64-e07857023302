<?php
	require_once('categories.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class categoriesExistsTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester si une catégorie éxiste
		 */
		public function testCategoriesExists() {

            $this->assertTrue(prd_categories_exists(1), 'Erreur: prd_categories_exists retourne faux avec un id de catégorie éxistant');
            $this->assertFalse(prd_categories_exists(10000), 'Erreur: prd_categories_exists retourne vrai avec un identifiant inexistant');
		}
		
		/** Fonction permettant de tester si une hiérarchie entre 2 catégorie existe
		 */
		public function testCategoriesHierachyExists(){

			$this->assertTrue(false !== prd_categories_hierarchy_exists(3, 2), 'Erreur sur la fonction prd_categories_hierarchy_exists');

			$this->assertTrue(false !== prd_categories_hierarchy_exists(2, 4), 'Erreur sur la fonction prd_categories_hierarchy_exists');

			$this->assertTrue(false !== prd_categories_hierarchy_exists(3, 4), 'Erreur sur la fonction prd_categories_hierarchy_exists');

			$this->assertFalse(prd_categories_hierarchy_exists(1, 2), 'Erreur sur la fonction prd_categories_hierarchy_exists');

			$this->assertFalse(prd_categories_hierarchy_exists(4, 2), 'Erreur sur la fonction prd_categories_hierarchy_exists');
		}

    }


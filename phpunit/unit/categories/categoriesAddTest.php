<?php
	require_once('categories.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class categoriesAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'ajout d'une catégorie valide
         * @dataProvider validCateg
		 */
		public function testCategoriesValidAdd($name, $title, $desc, $parent, $publish, $is_sync, $ref, $is_solde, $cod_id ) {

            $cat_id = prd_categories_add($name, $title, $desc, $parent, $publish, $is_sync, $ref, $is_solde, $cod_id);

            // Vérifie que l'id est un integer
			$this->assertThat($cat_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
			), 'Erreur: prd_categories_add retourne un id invalide');

            
            // Vérifie que les  champs sont corrects
            $cat = prd_categories_get_array( $cat_id );

            $this->assertTrue( is_array($cat), 'Erreur lors de la vérification des champs de la catégorie ajoutée');

            $this->assertEquals( $name, $cat['name'], 'Erreur: nom de la catégorie non conforme à la valeur lors de l\'ajout');
            
            if($title != ''){
                $this->assertEquals( $title, $cat['title'], 'Erreur: titre de la catégorie non conforme à la valeur lors de l\'ajout');
            }else{
                $this->assertEquals( $name, $cat['title'], 'Erreur: titre de la catégorie non conforme à la valeur lors de l\'ajout');
            }

            $this->assertEquals( $desc, $cat['desc'], 'Erreur: description de la catégorie non conforme à la valeur lors de l\'ajout');

            $this->assertEquals( $parent, $cat['parent_id'], 'Erreur: id du parent de la catégorie non conforme à la valeur lors de l\'ajout');

            $this->assertTrue( $publish == $cat['publish'], 'Erreur: propriété publish de la catégorie non conforme à la valeur lors de l\'ajout');

            $this->assertTrue( $is_sync == $cat['is_sync'], 'Erreur: propriété is_sync de la catégorie non conforme à la valeur lors de l\'ajout');

            $this->assertEquals( $ref, $cat['ref'], 'Erreur: référence de la catégorie non conforme à la valeur lors de l\'ajout');
        }

        /** Fonction permettant de tester l'ajout d'une catégorie invalide
         * @dataProvider invalidCateg
         */
        public function testCategoriesInvalideAdd($name, $title, $desc, $parent, $publish, $is_sync, $ref, $is_solde, $cod_id, $error ){

            $this->assertFalse(prd_categories_add($name, $title, $desc, $parent, $publish, $is_sync, $ref, $is_solde, $cod_id), $error );
        }

        public function validCateg(){
            return array(
                //                  name        title              desc   parent     publish      is_sync        ref     is_solde   cod_id 
                array( 'Ajout categ test 1',          '',             '',        0,      false,       false,      null,       false,      0 ),
                array( 'Ajout categ test 2',     'Title',             '',        0,       true,       false,      null,        true,      0 ),
                array( 'Ajout categ test 3',          '',         'Desc',        3,       true,       false,     'ref',       false,      0 ),
                array( 'Ajout categ test 4',          '',         'Desc',        5,      false,        true,      null,       false,      0 ),

            );
        }

        public function invalidCateg(){
            return array(
                //                  name        title              desc   parent     publish      is_sync        ref     is_solde   cod_id            message d'erreur
                array( 'categ invalide',          '',             '',      10000,      false,       false,      null,       false,      0 , 'Erreur: une catégorie avec un identifiant de parent inéxistant à été ajoutée'),
            );
        }
    }


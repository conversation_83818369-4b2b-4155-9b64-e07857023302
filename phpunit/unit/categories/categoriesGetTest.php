<?php
	require_once('categories.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class categoriesGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération des données d'une catégorie par son id
		 */
		public function testCategoriesGetById() {

            $cat = prd_categories_get_array(1);
            $this->assertTrue(is_array($cat), 'Erreur lors de la récupération d\'une catégorie par son id');
            $this->assertEquals(1, $cat['id'], 'Erreur lors de la récupération d\'une catégorie par son id');    
            
            $cat = prd_categories_get_array(1000);
            $this->assertFalse($cat, 'Erreur prd_categories_get_array retourne des informations avec un id de catégorie invalide');
        }

        /** Fonction permettant de tester la récupération d'une catégorie par son nom
         */
        public function testCategoriesGetByName(){
            
            $cat = prd_categories_get_array(0, false, 0, 'catégorie fille 1');
            $this->assertTrue(is_array($cat) && isset($cat[0]) , 'Erreur lors de la récupération d\'une catégorie par son nom');
            $this->assertEquals(1, $cat[0]['id'], 'Erreur lors de la récupération d\'une catégorie par son nom');

            $cat = prd_categories_get_array(0, false, 0, 'nom invalide');
            $this->assertEquals(array(), $cat, 'Erreur prd_categories_get_array retourne des informations avec un nom de catégorie invalide');
        }

        /** Fonction permettant de tester la récupération de catégories avec un filtre sur la catégorie parente
         */
        public function testCategoriesGetByParent(){
           
            $cat = prd_categories_get_array(0, false, 2);
            $this->assertTrue(is_array($cat) && isset($cat[0]) && isset($cat[1]), 'Erreur lors de la récupération de catégories avec un filtre sur la catégorie parente');
            $this->assertEquals(4, $cat[0]['id'], 'Erreur lors de la récupération de catégories avec un filtre sur la catégorie parente');
            $this->assertEquals(5, $cat[1]['id'], 'Erreur lors de la récupération de catégories avec un filtre sur la catégorie parente');

            $cat = prd_categories_get_array(0, false, 1000);
            $this->assertEquals(array(), $cat, 'Erreur prd_categories_get_array retourne des informations avec un filtre sur la catégorie parente invalide');
        }

        /** Fonction permettant de tester la récupération de l'id du parent d'une catégorie
         */
        public function testCategoriesParentIdGet(){

            $this->assertEquals(3, prd_categories_get_parent_id(1), 'Erreur: id du parent de la catégorie non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération du nombre de produit d'une catégorie
         */
        public function testCategoriesPrdCountGet(){

            $this->assertEquals(2, prd_categories_get_prd_count(1), 'Erreur: nombre de produit de la catégorie non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération du nom d'une catégorie
         */
        public function testCategoriesNameGet(){

            $this->assertEquals('catégorie fille 1', prd_categories_get_name(1), 'Erreur: nom de la catégorie non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération de l'url d'une catégorie
         */
        public function testCategoriesUrlGet(){

            $this->assertEquals('/catalogue/parent/fille-1', prd_categories_get_url(1), 'Erreur: url de la catégorie non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération de tout les parents d'une catégorie
         */
        public function testCategoriesAllParentGet(){
            
            $parent = prd_categories_parents_get_array(4);
            $this->assertTrue(is_array($parent), 'Erreur: liste des parents de la caégorie non conforme à la valeur dans la base de donnée');

            $this->assertTrue($parent[0] == 3 && $parent[1] == 2, 'Erreur: liste des parents de la catégorie non conforme à la valeur dans la base de donnée');    
        }

        /** Fonction permettant de tester la récupération de tout les enfants d'une catégorie
         */
        public function testCategoriesAllChildGet(){

            $child = prd_categories_childs_get_list(3);
            $this->assertEquals('1,2,4,5',$child, 'Erreur: liste des enfants de la catégorie non conforme à la valeur dans la base de donnée');

            $child = prd_categories_childs_get_list(2);
            $this->assertEquals('4,5',$child, 'Erreur: liste des enfants de la catégorie incoprrnon conforme à la valeur dans la base de donnéeectes');

            $child = prd_categories_childs_get_list(1);
            $this->assertEquals('',$child, 'Erreur: liste des enfants de la catégorie non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération des marques représentées dans une catégorie
         */
        public function testCategoriesBrandGet(){

            $rbrd = prd_categories_brands_get(3,false);
            $this->assertTrue($rbrd && ria_mysql_num_rows($rbrd) == 2, 'Erreur lors de la récupération des marques représentées dans une catégorie');
            $brd1 = ria_mysql_fetch_assoc($rbrd);
            $brd2 = ria_mysql_fetch_assoc($rbrd);

            $this->assertEquals(1, $brd1['id'], 'Erreur lors de la récupération des marques représentées dans une catégorie');

            $this->assertEquals('Marque test 1', $brd1['name'], 'Erreur lors de la récupération des marques représentées dans une catégorie');

            $this->assertEquals(2, $brd2['id'], 'Erreur lors de la récupération des marques représentées dans une catégorie');

            $this->assertEquals('Marque test 2', $brd2['name'], 'Erreur lors de la récupération des marques représentées dans une catégorie');
        }

        /** Fonction permettant de tester la récupération des identifiants des catégories parentes et des catégories enfant d'une catégorie donnée.
         */
        public function testCategoriesHierarchyGet(){

            $rhierarchy = prd_categories_hierarchy_get(2);
            $parent = '';
            $child = '';
            while($hierarchy = ria_mysql_fetch_assoc($rhierarchy)){
                if($hierarchy['is-parent']){
                    $parent .= $hierarchy['hry'].',' ;
                }else{
                    $child .= $hierarchy['hry'].',' ;
                }
            }
            $parent = rtrim($parent, ',');
            $child = rtrim($child, ',');

            $this->assertTrue($parent == '3' && $child == '4,5', 'Erreur lors de la récupération des catégories parentes et enfant');

            
            $rhierarchy = prd_categories_hierarchy_get(3);
            $parent = '';
            $child = '';
            while($hierarchy = ria_mysql_fetch_assoc($rhierarchy)){
                if($hierarchy['is-parent']){
                    $parent .= $hierarchy['hry'].',' ;
                }else{
                    $child .= $hierarchy['hry'].',' ;
                }
            }
            $parent = rtrim($parent, ',');
            $child = rtrim($child, ',');

            $this->assertTrue($parent == '' && $child == '1,2,4,5', 'Erreur lors de la récupération des catégories parentes et enfant');
        }

        /** Fonction permettant de tester prd_categories_have_childs
         */
       public function testCategoriesHaveChild(){

           $this->assertTrue(prd_categories_have_childs(3), 'Erreur: la fonction prd_categories_have_childs retourne faux pour une catégorie avec enfants');
           
           $this->assertFalse(prd_categories_have_childs(1), 'Erreur: la fonction prd_categories_have_childs retourne faux pour une catégorie avec enfants');

           $this->assertFalse(prd_categories_have_childs(4), 'Erreur: la fonction prd_categories_have_childs retourne vrai pour une catégorie sans enfants');
       }

       /** Fonction permettant de tester la récupération du nombre de produit dans une catégorie
        */
       public function testCategoriesProductsCountGet(){

           $this->assertEquals(2, prd_categories_products_count_get(1), 'Erreur: le nombre de produit de la catégorie est non conforme à la valeur dans la base de donnée');
       }

       /** Fonction permettant de tester la récupération de la profondeur d'une catégorie dans l'arborescence
        */
       public function testCategoriesDepthGet(){

           $this->assertEquals(0, prd_categories_depth_get(3), 'Erreur: la profondeur de la catégorie dans l\'arborescence est non conforme à la valeur dans la base de donnée');

           $this->assertEquals(1, prd_categories_depth_get(1), 'Erreur: la profondeur de la catégorie dans l\'arborescence est non conforme à la valeur dans la base de donnée');

           $this->assertEquals(2, prd_categories_depth_get(4), 'Erreur: la profondeur de la catégorie dans l\'arborescence est non conforme à la valeur dans la base de donnée');
       }

        /** Fonction permettant de tester la récupération des familles de produits contenant des nouveauté
        */
        public function testCategoriesNewGet(){

            $rcat = prd_categories_get_for_new();
            $this->assertTrue(true == $cat = ria_mysql_fetch_assoc($rcat), 'Erreur lors de la récupération des familles de produits contenant des nouveauté');
            $this->assertEquals(3, $cat['id'], 'Erreur lors de la récupération des familles de produits contenant des nouveauté');
            $this->assertFalse($cat = ria_mysql_fetch_assoc($rcat), 'Erreur lors de la récupération des familles de produits contenant des nouveauté');

            $rcat = prd_categories_get_for_new(3);
            $this->assertTrue(true == $cat = ria_mysql_fetch_assoc($rcat), 'Erreur lors de la récupération des familles de produits contenant des nouveauté');
            $this->assertEquals(1, $cat['id'], 'Erreur lors de la récupération des familles de produits contenant des nouveauté');
 
            $rcat = prd_categories_get_for_new(1);
            $this->assertEquals(0, ria_mysql_num_rows($rcat), 'Erreur lors de la récupération des familles de produits contenant des nouveauté');
        }
	}

<?php
	require_once('categories.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class categoriesUpdateTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la mise à jour d'une catégorie avec des paramètres valide
         * @dataProvider validCateg
         */
		public function testCategoriesValidUpdate($name, $title, $desc, $parent, $publish, $date_from, $date_to, $is_solde, $cod_id) {
            
            $this->assertTrue(prd_categories_update(1,$name, $title, $desc, $parent, $publish, $date_from, $date_to, $is_solde, $cod_id) == true , 'Erreur lors de la mise à jour des informations de la catégorie');
            
            //Vérifie que les champs ont bien été mis à jour
            $rcat = prd_categories_get(1);
			$this->assertTrue($rcat || ria_mysql_num_rows($rcat), 'Erreur lors de la vérification des champs de la catégorie mise à jour');
            $cat = ria_mysql_fetch_array($rcat);
              
            $this->assertEquals( $name, $cat['name'], 'Erreur: nom de la catégorie non mis à jour');

            if($title == ''){
                $this->assertEquals( $name, $cat['title'], 'Erreur: titre de la catégorie non mis à jour');
            }else{
                $this->assertEquals( $title, $cat['title'], 'Erreur: titre de la catégorie non mis à jour');
            }            

            $this->assertEquals( $desc, $cat['desc'], 'Erreur: description de la catégorie non mise à jour');

            $this->assertEquals( $parent, $cat['parent_id'], 'Erreur: catégorie parente non mise à jour');

            $this->assertTrue( $publish == $cat['publish'], 'Erreur: propriété publish non mise à jour');

            if($date_from){
                $this->assertEquals( $date_from, $cat['date_from'].' '.$cat['hour_from'], 'Erreur: date de début d\'affichage non mise à jour');
            }

            if($date_to){
                $this->assertEquals( $date_to, $cat['date_to'].' '.$cat['hour_to'], 'Erreur: date de fin d\'affichage non mise à jour');
            }

        }

        /** Fonction permettant de tester la msie à jour d'une catégorie avec des paramètres invalide 
         * @dataProvider invalidCateg
         */
        public function testCategoriesInvalidUpdate($name, $title, $desc, $parent, $publish, $date_from, $date_to, $is_solde, $cod_id, $error){

            $this->assertFalse(prd_categories_update(1,$name, $title, $desc, $parent, $publish, $date_from, $date_to, $is_solde, $cod_id), $error);
        }

        public function validCateg(){
            return array(
                //            name        title        desc     parent      publish            date_from                date_to     is_solde    cod_id
                array(  'New name',          '',         '',        0,      false,                 false,                 false,      false,       0),
                array(  'New name', 'New title', 'New desc',        2,       true,    '09/05/2018 00:00',    '09/05/2019 00:00',       true,       0),
                array(  'New name',          '',         '',        3,      false,                 false,                 false,      false,       0),
            );
        }

        public function invalidCateg(){
            return array(
                //            name       title     desc     parent      publish     date_from       date_to     is_solde    cod_id      message d'erreur
                array(         '',         '',      '',        0,      false,          false,         false,      false,       0 , 'Erreur: mise à jour de la catégorie sans nom'),
            );
        }

        /** Fonction permettant de tester l'ajout de l'url virtuelle d'une catégorie donnée
         */
        public function testCategoriesUrlAliasAdd(){

            $this->assertEquals('/catalogue/categorie-parent/categorie-fille-2/', prd_categories_url_alias_add(2), 'Erreur lors de l\'ajout de l\'url virtuelle');
        }


        public function testCategoriesUpdate(){
            
            $this->assertTrue(prd_categories_set_index(1, true), 'Erreur lors de la mise à jour de l\index de la catégorie');
            
            $this->assertTrue(prd_categories_set_url(1, '\url'), 'Erreur lors de la mise à jour de l\'url de la catégorie');
            
            $this->assertTrue(prd_categories_set_desc(1, 'une nouvelle description'), 'Erreur lors de la mise à jour de la description de la catégorie');
            
            $this->assertTrue(prd_categories_update_keywords(1, 'un nouveau mot clé'), 'Erreur lors de la mise à jour des mots clé de la catégorie');
            
            $this->assertTrue(prd_categories_update_referencing(1, 'nouveau titre', 'nouvelle description'), 'Erreur lors de la mise à jour du référencement de la catégorie');  

            $this->assertTrue(prd_categories_set_ref(1, 'nouvelle ref'), 'Erreur lors de la mise à jour de la référence');

            // Vérifie que les champs ont bien été mis à jour
            $cat = prd_categories_get_array(1);
            $this->assertTrue(is_array($cat), 'Erreur lors de la vérification des champs de la catégorie mise à jour');

            $this->assertTrue(prd_categories_is_index(1), 'Erreur: la propriété cat_no_index de la categorie n\'a pas été mise à jour');

            $this->assertEquals('\url', $cat['url_alias'], 'Erreur: l\'url de la catégorie n\'a pas été mise à jour');

            $this->assertEquals('Une nouvelle description', $cat['desc'], 'Erreur: la description de la catégorie n\'a pas été mise à jour');

            $this->assertEquals('un nouveau mot clé', $cat['keywords'], 'Erreur: les mots clé de la categorie n\'ont pas été mis à jour');

            $this->assertEquals('nouveau titre', $cat['tag_title'], 'Erreur: le tite de la categorie utilisé pour le référencement n\'a pas été mis à jour');

            $this->assertEquals('nouvelle description', $cat['tag_desc'], 'Erreur: la description de la categorie utilisé pour le référencement n\'a pas été mise à jour');

            $this->assertEquals('nouvelle ref', $cat['ref'], 'Erreur: la référence de la categorie n\'a pas été mise à jour');

            
            // Vérifie la prise en compte de l'url perso
            $this->assertTrue(prd_categories_set_url_perso(1, '\urlperso'), 'Erreur lors de la mise à jour de l\'url perso de la catégorie');

            $cat = prd_categories_get_array(1);
            $this->assertTrue(is_array($cat), 'Erreur lors de la vérification des champs de la catégorie mise à jour');

            $this->assertEquals('\urlperso', $cat['url_alias'], 'Erreur: l\'url de la catégorie n\'a pas été mise à jour');
        }     
    }


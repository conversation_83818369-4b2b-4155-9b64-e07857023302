<?php 
/**
 *	\defgroup orders_serials Numéros de lots
 *	\ingroup orders 
 *  	@{	
*/

require_once('obj.serials.inc.php');

switch( $method ){
	/** @{@}
 	 * @{	
	 *  \page api-serials-add Ajout/Mise à jour 
	 *
	 * Cette fonction permet d'ajouter des lots à des commandes ou des produits par exemple.
	 *
	 *		\code
	 *			PUT/POST /serials/
	 *		\endcode
	 *
	 * @param int $cls_id Obligatoire, Identifiant de la classe de l'objet
	 * @param int $obj_id_0 Obligatoire, Identifiant 1 de l'objet
	 * @param int $obj_id_1 Facultatif, Identifiant 2 de l'objet
	 * @param int $obj_id_2 Facultatif, Identifiant 3 de l'objet
	 * @param date $dlc Facultatif, Date de la date limite de consommation
	 * @param date $production Facultatif, Date de la production du lot
	 * @param string $serial Obligatoire, Numéro du lot
	 * @param int $dps_id Facultatif, Numéro du dépot de stockage du lot
	 * @param int $qte Facultatif, Quantité concerné par le lot ( quantité dispo si c'est sur un produit, quantité prise si c'est sur une commande )
	 * @param string $ref_gescom Facultatif, Référence de la ligne dans la gestion commerciale 
	 *	
	 * @return json, avec les colonnes :
	 *	\code{.json}
	 *	   [{
	 *			"cls_id" : Identifiant de la classe de l'objet
	 *			"obj_id_0" : Identifiant 1 de l'objet
	 *			"obj_id_1" : Identifiant 2 de l'objet
	 *			"obj_id_2" : Identifiant 3 de l'objet
	 *			"ref_gescom" : Référence de la ligne dans la gestion commerciale 
	 *	   },...],
	 *	\endcode
	 *	@}
	*/
	case 'add': 
	case 'upd': 

		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		$content = array();
		foreach($objs as $lot){

			if( !isset($lot['cls_id']) ){
				throw new Exception("Paramètre cls_id invalide");
			}
			if( !isset($lot['serial']) || $lot['serial'] === '' ){
				throw new Exception("Paramètre serial invalide");
			}
			if( !isset($lot['obj_id_0']) || !is_numeric($lot['obj_id_0'])){
				throw new Exception("Paramètre obj_id_0 invalide");
			}
			if( !isset($lot['obj_id_1']) || !is_numeric($lot['obj_id_1'])){
				throw new Exception("Paramètre obj_id_1 invalide");
			}
			if( !isset($lot['obj_id_2']) || !is_numeric($lot['obj_id_2']) ){
				throw new Exception("Paramètre obj_id_2 invalide");
			}

			$dlc = isset($lot['dlc']) && isdateheure($lot['dlc']) ? $lot['dlc'] : null;
			$production = isset($lot['production']) && isdateheure($lot['production']) ? $lot['production'] : null;
			$dps_id = isset($lot['dps_id']) && is_numeric($lot['dps_id']) ? $lot['dps_id'] : null;
			$qte = isset($lot['qte']) && is_numeric($lot['qte']) ? $lot['qte'] : null;
			$ref_gescom = isset($lot['ref_gescom']) && $lot['ref_gescom'] ? $lot['ref_gescom'] : null;

			$data_for_response = $lot;

			if (!obj_serials_add( $lot['cls_id'], $lot['obj_id_0'], $lot['obj_id_1'], $lot['obj_id_2'], $dlc, $production, $lot['serial'], $dps_id, $qte, $ref_gescom ) ){
				throw new Exception("Erreur lors de la mise à jour du numéro de lot : ".print_r($data_for_response, true));
			}

			$content[] = $data_for_response;

		}
		$result = true;
		break;

	/** @{@}
	 * @{	
	 *  \page api-serials-del Suppression
	 *
	 * Cette fonction permet la suppressions des lots à des commandes ou des produits par exemple.
	 *
	 *		\code
	 *			DELETE /serials/
	 *		\endcode
	 *
	 * @param int $cls_id Obligatoire, Identifiant de la classe de l'objet
	 * @param int $obj_id_0 Facultatif, Identifiant 1 de l'objet
	 * @param int $obj_id_1 Facultatif, Identifiant 2 de l'objet
	 * @param int $obj_id_2 Facultatif, Identifiant 3 de l'objet
	 *	
	 * @return true si aucune erreur à eu lieu
	 *	@}
	 */
	case 'del': 
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		$content = array();
		foreach($objs as $lot){

			if( !isset($lot['cls_id']) ){
				throw new Exception("Paramètre cls_id invalide");
			}
			if( !isset($lot['serial']) || $lot['serial'] === '' ){
				throw new Exception("Paramètre serial invalide");
			}
			if( !isset($lot['obj_id_0']) || !is_numeric($lot['obj_id_0'])){
				throw new Exception("Paramètre obj_id_0 invalide");
			}
			if( !isset($lot['obj_id_1']) || !is_numeric($lot['obj_id_1'])){
				throw new Exception("Paramètre obj_id_1 invalide");
			}
			if( !isset($lot['obj_id_2']) || !is_numeric($lot['obj_id_2']) ){
				throw new Exception("Paramètre obj_id_2 invalide");
			}

			if (!obj_serials_del( $lot['cls_id'], $lot['obj_id_0'], $lot['obj_id_1'], $lot['obj_id_2'], $lot['serial'] ) ){
				throw new Exception("Erreur lors de la suppression du numéro de lot : ".print_r($lot, true));
			}
		}
		$result = true;
		break;
}

///@}
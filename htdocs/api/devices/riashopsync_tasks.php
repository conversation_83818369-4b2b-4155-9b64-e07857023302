<?php
// \cond onlyria

/** 
 * \defgroup api-devices-riashopsync_tasks Tâches de Riashopsync (A DEPLACER (VOIR AVEC SEB D) )
 * \ingroup Yuto
 * @{		
 *	
 * \page api-devices-riashopsync_tasks-get Chargement
 * Cette fonction récupère les tâches de riashopsync
 *
 *	\code
 *		GET /devices/riashopsync_tasks/
 *	\endcode
 *	
 * @return json sous la forme d'une liste ayant les éléments sous la forme suivant : 
 *	\code{.json}
 *		{
 *          "tsk_id": identifiant de la tache
 *          "tsk_active": tache activée ou non (1 pour activé, 0 pour désactivé)
 *		},
 * 	\endcode 
 * @}
*/

require_once('tasks.inc.php');

switch( $method ){
	// récupère les tâches de riashopsync
	case 'get':

		$result = true;

		$rtasks = tsk_tasks_activation_get( 0, $config['tnt_id'] );
		if( $rtasks && ria_mysql_num_rows($rtasks) ){
			while( $tsk = ria_mysql_fetch_assoc($rtasks) ){
				$content[] = array("tsk_id" => $tsk['tsk_id'], "tsk_active" => $tsk['is_active'] );
			}
		}

		break;
}
// \endcond
<?php
/** 
 * \defgroup api-devices-meta Méta-modèle
 * \ingroup Yuto 
 * @{		
 * \page api-devices-meta-get Chargement
 *
 * Cette fonction récupère les éléments du méta-modèle
 *
 *		\code
 *			GET /devices/meta/
 *		\endcode
 *
 * @param meta_cls_id Obligatoire, Identifiant du module du méta-modèle
 *	
 * @return Liste de méta-modèle avec les colonnes suivantes :
 *			- id : identifiant du contenu
 *			- id_X : optionnel, identifiants secondaires du contenu, ou "X" est le rang, à partir de 1
 *			- content : résultat de la méthode "get" appropriée pour le contenu
 * @}
*/

switch( $method ){
	case 'get':

		if( !isset($_GET['meta_cls_id']) ){
			throw new Exception("Le paramètre meta_cls_id est manquant ou incorrect");
		}else{
			$result = true;
			$content = dev_devices_get_meta_objects($_GET['meta_cls_id']);
		}

		break;
}
	
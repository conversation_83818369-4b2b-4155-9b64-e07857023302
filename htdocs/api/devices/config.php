<?php
/** 
 * \defgroup api-devices-config <PERSON>ur de champ avan<PERSON>  
 * \ingroup Yuto
 * @{	
 * \page api-devices-config-get Chargement
 * 
 * Cette fonction récupère les éléments de la configuration
 *
 *	\code
 *		GET /devices/config/
 *	\endcode
 *	
 * @return json, liste de variable de configuration avec les colonnes sous la forme :
 *	\code{.json}
 *		{
 *           "code": Code,
 *           "value": Valeur,
 *           "usr_id": identifiant utilisateur,
 *           "type": type de la configuration 
 *		}
 *	\endcode
 * @}
*/

switch( $method ){
	case 'get':

		$result = true;

		$exclude = array('site_dir', 'banners_dir', 'doc_dir', 'img_dir','paypal_url_callback','paypal_payment_type_id','paypal_pwd','paypal_signature','paypal_user','paypal_version','spplus_site_id','spplus_key','spplus_key_test','etransactions_merchant_3ds_id','etransactions_merchant_id','sogenactif_merchant_id','sogenactif_url_retour_auto','sogenactif_url_retour_ko','sogenactif_url_retour_ok','mercanet_merchant_id','so_colissimo_key','so_colissimo_pudoFOId','so_colissimo_url_ko','so_colissimo_url_ok','CMCIC_AUTO','CMCIC_CLE','CMCIC_CODESOCIETE','CMCIC_SERVEUR','CMCIC_TPE','CMCIC_URLKO','CMCIC_URLOK','CMCIC_VERSION','ebay_api_session','ebay_api_token','ebay_carriers','ebay_reload_token','sofinco_merchant_id','sofinco_return','sofinco_url','xlsoft_ftp_dir_import','xlsoft_ftp_dir','xlsoft_ftp_dir_images','xlsoft_cat_root','yesbycash_email_cancel','yesbycash_key','yesbycash_key_id','yesbycash_merchant_id','yesbycash_url_actions','yesbycash_url_nok','yesbycash_url_ok','yesbycash_notify_active','yesbycash_notify_delay','systempay_certicat','systempay_certicat_dev','systempay_site_id','systempay_url_cancel','systempay_url_cancel_register','systempay_url_error','systempay_url_error_register','systempay_url_return_register','paybox_id','paybox_key','paybox_rang','paybox_site','paybox_url_annule','paybox_url_attente','paybox_url_effectue','paybox_url_refuse','paybox_url_repondre_a','chronordv_accountNumber','chronordv_password','chronordv_shipper_zipcode','paybox_key_prod','paybox_porteur','payzen_certicat','payzen_certicat_dev','payzen_contract','payzen_site_id','payzen_state_multi','payzen_url_cancel','payzen_url_cancel_register','payzen_url_error','payzen_url_error_register','payzen_url_return_ok','payzen_url_return_register','web10_wsdl','web10_login','web10_society','web10_password','salesforce_login','salesforce_password','salesforce_yuto_usr','salesforce_wsdl','salesforce_config','sineres_ftp_password','sineres_ftp_login','sineres_ftp_url','paybox_key_dev','sync_sage_odbc_password','sync_sage_odbc_login','sync_sage_odbc_driver','sync_sage_odbc_dns');

		$rcfg = cfg_variables_get( array_keys($config), $exclude );
		if( $rcfg && ria_mysql_num_rows($rcfg) ){
			while( $cfg = ria_mysql_fetch_assoc($rcfg) ){

				if( !isset($config[$cfg['code']]) ) continue;

				$value = $config[$cfg['code']];

				if( $cfg['code'] == 'dlv_prd_references' && isset($config['fdv_use_dlv_prd_references']) && !$config['fdv_use_dlv_prd_references'] ){
					$value = array();
				}

				$content[] = array(
					"code" => $cfg['code'], 
					"value" => $value, 
					"usr_id" => 0, 
					"type" => $cfg['type'] 
				);
			}
		}

		if( isset($config['dev_version']) && $config['dev_version'] > 180 ){
			// Permet l'ajout de toutes les overrides sur des utilisateurs
			$rcfg = cfg_overrides_get(false, array(), '', -1);
			if( $rcfg && ria_mysql_num_rows($rcfg) ){
				while ($cfg = ria_mysql_fetch_assoc($rcfg)) {
					if( $cfg["usr_id"] > 0 ){
						$content[] = array(
							"code" => $cfg['code'], 
							"value" => $cfg["value"], 
							"usr_id" => $cfg["usr_id"], 
							"type" => $cfg['type'] 
						);
					}
				}
			}
		}

		$content[] = array(
			"code" => "is_yuto_essentiel", 
			"value" => tnt_tenants_is_yuto_essentiel() ? 1 : 0,
			"usr_id" => 0, 
			"type" => FLD_TYPE_BOOLEAN_YES_NO 
		);

		if( RegisterGCP::onGcloud() ){
			$content[] = array(
				"code" => "register_gcp_package", 
				"value" => RegisterGCP::getPackage($config['tnt_id']), 
				"usr_id" => 0, 
				"type" => FLD_TYPE_TEXT 
			);
		}

		break;
}
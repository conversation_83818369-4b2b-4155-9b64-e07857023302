<?php

// \cond onlyria

/**
 * \defgroup api-devices-task Tâche de synchronisation
 * \ingroup Yuto
 * @{
*/



// désactive certaines variables de restrictions sur le catalogue pour éviter certains soucis de mise à jour de produit (ex, le commercial n'a pas le droit d'avoir accès à un produit mais son client si)
$config['use_catalog_restrictions'] = 0;
$config['admin_catalog_hide_source_unpublished'] = 0;



switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-devices-task-add Ajout
	 *
	 * Cette fonction enregistre les informations de synchronisation d'un appareil, pour utilisation ultérieure par les fonctions de synchronisation.
	 * En un appel, l'appareil envoie les dates de dernière synchronisation de chaque classe synchronisée sur l'appareil. Voici un exemple :
	 *
	 * \code
	 * [
	 * 	{"cls_id":70,"date":"2019-11-06 15:48:46","seq":""},
	 * 	{"cls_id":40,"date":"2019-09-19 11:35:53","seq":""}
	 * ]
	 * \endcode
	 *
	 * Cet appel est réalisé deux fois par synchronisation : une fois pour les classes principales en début de synchronisation, une fois en milieu
	 * de synchronisation pour les classes secondaires.
	 *
	 * Il a été choisi de réunir les appels avec les dates/heures de dernière synchronisation en un appel pour éviter que Yuto soit obligé d'interroger
	 * toutes les classes pour savoir s'il y a des données à synchroniser.
	 *
	 *	\code
	 *		POST /devices/task/
	 *	\endcode
	 *
	 * @param json objs Un tableau json contenant les informations classe/date et heure de dernière synchronisation où :
	 * 		- cls_id est l'identifiant de classe.
	 * 		- date est la date du dernier objet synchronisé de cette classe
	 * 		- seq est la date du dernier numéro de séquence de cette classe (couchdb uniquement)
	 *
	 * @return json tableau contenant pour chaque classe ayant des contenus à synchroniser, le nombre d'éléments à synchroniser (ajout/modification/suppression) :
	 * 		\code
	 * 		{
	 * 			"result":true,
	 * 			"time":"2021-04-23 09:17:52",
	 * 			"message":"",
	 * 			"content":[
	 * 				{"cls":3,"count":"1"},
	 * 				{"cls":1,"count":"1"},
	 * 				{"cls":71,"count":"13"},*
	 * 			]
	 * 		}
	 * 		\endcode
	 *
	 * @}
	*/
	// récupère les taches nécessaires
	case 'add':
		$result = true;
		$content = array();

		$objs = json_decode($raw_data);// les données envoyé par la tablette sont de la forme suivante array( array('cls_id'=>X, 'date'=>'2015-02-02 00:00', 'seq'=>'JdiIODA...'), ... )
		if( is_array($objs) ){
			foreach( $objs as $obj ){
				$obj = json_decode(json_encode($obj), true); // conversion stdclass en tableau

				if( isset($obj["cls_id"], $obj['date']) && is_numeric($obj['cls_id']) ){

					$date_or_seq = isdateheure($obj['date']) ? $obj['date'] : false;
					if( isset($obj['seq']) && $obj['seq']!='' ){ // cas particulier des numéros de séquence à la place des dates
						$date_or_seq = $obj['seq'];
					}

					$count = 0;
					dev_devices_get_sync_objects($config['dev_id'],$obj["cls_id"], $count, $date_or_seq, true );
					if( $count > 0 ){
						$content[] = array( 'cls' => $obj["cls_id"], 'count' => $count );
					}

				}
			}
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-devices-task-upd Mise à jour
	 *
	 * Cette fonction met à jour les informations de dernière synchronisation d'un appareil.
	 *
	 *	\code
	 *		PUT /devices/task/
	 *	\endcode
	 *
	 * @param int $cls_id Obligatoire, Identifiant de classe.
	 * @param string $date_last_sync Facultative, Date de dernière synchronisation de l'appareil.
	 * @param string $date_last_obj Facultative, Date de dernière modification de l'élément le plus récemment synchronisé pour la classe.
	 * @param $seq_last_obj Facultative, Numéro de séquence de dernière modification de l'élément le plus récemment synchronisé pour la classe.
	 *
	 * @return true si la mise à jour s'est déroulée avec succès
	 * @}
	*/
	// met à jour les taches pour une tablette
	case 'upd':

		$objs = json_decode($raw_data);// les données envoyé par la tablette sont de la forme suivante array( array('cls_id'=>X, 'date'=>'2015-02-02 00:00'), ... )
		if( is_array($objs) ){
			foreach( $objs as $obj ){
				$obj = json_decode(json_encode($obj), true); // convertion stdclass en tableau

				if( isset($obj["cls_id"], $obj['date_last_obj'], $obj['date_last_sync']) && is_numeric($obj['cls_id']) ){

					$date_last_obj = isdateheure($obj['date_last_obj']) ? $obj['date_last_obj'] : false;
					$date_last_sync = isdateheure($obj['date_last_sync']) ? $obj['date_last_sync'] : false;
					$seq_last_obj = isset($obj['seq_last_obj']) ? $obj['seq_last_obj'] : false;

					if( !dev_devices_tasks_set( $config['dev_id'], $obj['cls_id'], $date_last_sync, $date_last_obj, $seq_last_obj ) ){
						throw new Exception('Erreur de mise à jour des taches');
					}

				}
			}
		}

		$result = true;

		break;
}

///@}
// \endcond
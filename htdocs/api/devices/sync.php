<?php
/** 
 * \defgroup api-devices-sync Contenus à synchroniser
 * \ingroup Yuto 
 * @{		
 * \page api-devices-sync-get Chargement
 * 
 * Cette fonction récupère les contenus d'une classe donnée à synchroniser avec les tablettes.
 *
 *		\code
 *			GET /devices/sync/
 *		\endcode
 *	
 * @param int $cls_id Obligatoire, identifiant de la classe.
 * @param datetime $date Optionnel, date de dernière mise à jour minimale des objets à retourner.
 * @param int $obj_id Optionnel, identifiant à tester
 * @param int $obj_id_1 Optionnel, identifiant à tester
 * @param int $obj_id_2 Optionnel, identifiant à tester
 *	
 * @return json avec les colonnes :
 *		\code{.json}
 *       {
 * 			"count": Compte le nombre de classe récupérées
 * 			"data" : liste en json des datas liées à la classe ( voir classe correspondante )
 *       },
 *		\endcode
 *
 * @see flux d'activité 
*/

// \cond onlyria

// désactive certaines variables de restrictions sur le catalogue pour éviter certains soucis de mise à jour de produit (ex, le commercial n'a pas le droit d'avoir accès à un produit mais son client si)
$config['use_catalog_restrictions'] = 0;
$config['admin_catalog_hide_source_unpublished'] = 0;

// \endcond

switch( $method ){
	// récupère les élements à synchroniser
	case 'get':

		if( !isset($_GET['cls_id']) ){
			throw new Exception("Le paramètre CLS_ID est manquant ou incorrect");
		}

		$date = isset($_GET['date']) && isdateheure($_GET['date']) ? $_GET['date'] : false;

		$count = 0;

		$check_id = 0;
		// si des id sont passé en paramètre on parcours les éléments jusqu'au bout pour savoir si une modification à eu lieu
		if( isset($_GET['obj_id']) ){
			$check_id = $_GET['obj_id'];

			if( isset($_GET['obj_id_1']) ){
				$check_id = array( $_GET['obj_id'], $_GET['obj_id_1'] );
			}
			if( isset($_GET['obj_id_2']) ){
				$check_id = array( $_GET['obj_id'], $_GET['obj_id_1'], $_GET['obj_id_2'] );
			}
		}

		$result = true;
		$data = dev_devices_get_sync_objects($config['dev_id'],$_GET['cls_id'], $count, $date, false, $check_id);

		// nécessaire pour le compteur sinon il est pas défini
		dev_devices_get_sync_objects($config['dev_id'],$_GET['cls_id'], $count, $date, true );

		$content = array(
				"count" => $count,
				"data" => $data
			);

		if( $check_id==0 ){
			// mise à jour de la tache coté riashop
			dev_devices_tasks_set( $config['dev_id'], $_GET['cls_id'], date('Y-m-d H:i:s'), $date );
		}

		break;

// ajout d'élement synchronisé en base
	case 'add':
/* les données sont envoyé en json
		$objs = json_decode($raw_data);
		if( is_array($objs) ){
			foreach( $objs as $obj ){

				if( !isset($obj->cls_id) ){
					throw new Exception("Le paramètre CLS_ID est manquant ou incorrect");
				}

				if( isset($obj->id_1, $obj->id_2) ){
					$object = array( $obj->id, $obj->id_1, $obj->id_2 );
				}elseif( isset($obj->id_1) ){
					$object = array( $obj->id, $obj->id_1 );
				}else{
					$object = array( $obj->id );
				}

				if( !dev_devices_sync_set( $config['dev_id'], $obj->cls_id, $object, $obj->date ) ){
					throw new Exception("Une erreur est survenue lors de la mise à jour de l'élément");
				}
			}
			$result = true;
		}
		else{
			error_log("[API] Erreur dans les données recues json incorrect dev_id=".$config['dev_id']);
		}
 */
		break;

	// supprime un élément
	case 'del':
/*
		// les données sont envoyé en json
		$data = json_decode($raw_data);

		if( sizeof($data) > 0 ){
			foreach( $data as $obj ){

				if( isset($obj->cls_id) ){

					if( isset($obj->id_1, $obj->id_2) ){
						$object = array( $obj->id, $obj->id_1, $obj->id_2 );
					}elseif( isset($obj->id_1) ){
						$object = array( $obj->id, $obj->id_1 );
					}else{
						$object = array( $obj->id );
					}

					if( !dev_devices_sync_del( $config['dev_id'], $obj->cls_id, $object ) ){
						throw new Exception("Une erreur est survenue lors de la suppression de l'élément");
						$code = _CODE_BAD_REQUEST;
					}
				}
			}
		}elseif( isset($_REQUEST['drop']) && $_REQUEST['drop'] == 1 ){

			mail('<EMAIL>','FDV debug drop database !',print_r($_REQUEST,true));

			// vide tous les objets si pas de paramètre
			if( !dev_devices_sync_del( $config['dev_id'], 0, 0) ){
				throw new Exception("Une erreur est survenue lors de la mise à jour de l'élément");
				$code = _CODE_BAD_REQUEST;
			}

			// vide tous les objects values TODO

		}

		$result = true;
 */
		break;
}

///@}
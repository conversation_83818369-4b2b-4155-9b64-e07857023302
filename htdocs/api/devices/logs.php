<?php
/**
 * \defgroup api-devices-logs Log
 * \ingroup Yuto
 * @{
 * \page api-devices-logs-add Ajout
 *
 * Cette fonction ajoute un log
 *
 *		\code
 *			POST /devices/logs/
 *		\endcode
 *
 * @param raw_data Obligatoire,
 * 	\code{.json}
 *     {
 *			"usr_id" : identifiant de l'utilisateur
 *			"adr_firstname" : Adresse
 *     }
 *	\endcode

 *
 * @return true si l'ajout s'est déroulé avec succès
*/

switch( $method ){
	case 'add':

		$usr_name = '';
		$rusr = gu_users_get($config['usr_id']);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);
			$usr_name = $usr['adr_firstname'].' '.$usr['adr_lastname'];
		}

		$tmp = "Tnt id : ".$config['tnt_id']." - ".$_REQUEST['logtoken']." \n";
		$tmp .= "Device id : ".$config['dev_id']." - ".$_REQUEST['token']." \n";
		$tmp .= "Version : ".$config['dev_version'].' - '.$config['dev_brand'].' - '.$config['dev_model']." \n";
		$tmp .= "Utilisateur : ".$usr_name." \n\n";

		$objs = json_decode($raw_data);
		if( is_array($objs) ){
			foreach( $objs as $obj ){
				$tmp .= $obj."\n\n";
			}

			// génération d'un json pour le log stack
			$final_array = array();
			$final_array["date"] = date('Y-m-d H:i:s');
			$final_array["tnt_id"] = $config['tnt_id'];
			$final_array["logtoken"] = $_REQUEST['logtoken'];
			$final_array["token"] = $_REQUEST['token'];
			$final_array["dev_version"] = $config['dev_version'];
			$final_array["dev_brand"] = $config['dev_brand'];
			$final_array["dev_model"] = $config['dev_model'];
			$final_array["dev_id"] = $config['dev_id'];
			$final_array["usr_id"] = $config['usr_id'];
			$final_array["usr_name"] = $usr_name;
			$final_array["logs"] = $objs;

			if( mail('<EMAIL>','Log '.($config['dev_brand'] == 'Apple' ? 'iOs':'Android').' - ' .($config['email_env_context'] != 'production' ? 'dev':'').' FDV '.$config['tnt_id'].' - '.$config['dev_id'], $tmp) ){

				api_log(json_encode($final_array), 'api-yuto');

				$result = true;
			}
		}

		break;
}

///@}
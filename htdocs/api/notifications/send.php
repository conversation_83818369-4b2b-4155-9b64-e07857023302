<?php 
/**
 * \defgroup send Envoi aux devices   
 * \ingroup Notifications
 * @{	 	
 * \page api-notifications-send-upd Mise à jour 
 *
 * Cette fonction permet l'envoi des notifications aux différents devices
 *
 *	\code
 *		PUT /notifications/send/
 *	\endcode
 *	
 * @param int $id Obligatoire, Identifiant de la notification à envoyer
 *	
 * @return true si la mise à jour s'est déroulée avec succès.
 * @}
*/

switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque le paramètre l'identifiant de la notification");
		}

		if( !nt_notifications_send($_REQUEST['id']) ){
			throw new Exception("Erreur dans l'envoi de la notification.");
		}
		$result = true;
		break;
}
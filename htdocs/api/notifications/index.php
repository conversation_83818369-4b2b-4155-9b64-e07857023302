<?php
/**
 * \defgroup Notifications Notifications
 * \ingroup oms
 * @{
*/

switch( $method ){
	/** @{@}
 	 * @{
     *
     * \page api-notifications-index-add Ajout
     *
     * Cette fonction permet d'ajouter une notification
     *
     * \code
     *      POST /notifications/
     * \endcode
     *
     * @param $nt_type_id Obligatoire, identifiant du type de notification
     * @param $nt_author_id Obligatoire, identifiant de l'auteur la notification
     * @param $nt_author_name Obligatoire, auteur de la notification
     * @param $nt_title Obligatoire, titre de la notification
     * @param $nt_desc Obligatoire, description de la notification
     * @param $nt_date_created Obligatoire, date de création de la notification
     * @param $notification_objects Facultatif, objects liés à la notification (user, order, products...)
     *
     * @return id l'identifiant de la notification
     *
	 * @}
	*/
	case 'add':

		if( !isset($_REQUEST['nt_type_id'])|| !is_numeric($_REQUEST['nt_type_id'])
        ||!isset($_REQUEST['nt_author_id'])|| !is_numeric($_REQUEST['nt_author_id'])
        ||!isset($_REQUEST['nt_author_name'])
        ||!isset($_REQUEST['nt_title'])
        ||!isset($_REQUEST['nt_desc'])
        ||!isset($_REQUEST['nt_date_created'])  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['notification_objects']) ){
			$_REQUEST['notification_objects'] = array();
		}

        //Ajout des champs principaux dans la table notification
		$notification_id = nt_notifications_add( $_REQUEST['nt_type_id'],$_REQUEST['nt_author_id'],$_REQUEST['nt_title'],$_REQUEST['nt_desc'],$_REQUEST['nt_date_created'] );
		if( $notification_id==null){
			throw new Exception('Erreur lors de la création de l\a notifications');
        }

        //Ajout des champs dans les tables liées
        foreach( $_REQUEST['notification_objects'] as $notification_object ){
            if( !nt_notification_objects_add($notification_id, $notification_object['nto_cls_id'], $notification_object['nto_obj_id_0'], $notification_object['nto_obj_id_1'], $notification_object['nto_obj_id_2']) ){
                throw new Exception('Erreur lors de la création des objets liés');
            }
        }

        if(!nt_notification_notify($notification_id)){
            throw new Exception('Erreur lors de l\'envoi du mail');
        }

        // Envoi une notification mail
        $notif = nt_notifications_get( $notification_id );
        if( is_array($notif) && count($notif) ){
            flow_notifications_send( CLS_NOTIFICATIONS, dev_devices_get_object_simplified(CLS_NOTIFICATIONS, array($notif['_id']), $notif) );
        }

        //Faire pareil pour la table nt_notification_read
        //N'est pas encore géré coté Yuto

		// if( $is_sync ){
		// 	if( !notifications_set_is_sync($notification_id, true) ){
		// 		throw new Exception('Erreur lors de la mise à jour de l\a notifications is sync');
		// 	}
		// } else {
		// 	if( !notifications_set_need_sync($notification_id, true) ){
		// 		throw new Exception('Erreur lors de la mise à jour de l\a notifications needsync');
		// 	}
		// }

		$result = true;
		$content = array('id' => $notification_id);

        break;
    /** @{@}
 	 * @{
     *
     * \page api-notifications-index-upd Mise à jour
     *
     * Cette fonction permet d'ajouter un rapport
     *
     *  \code
     *      PUT /notifications/
     *  \endcode
     *
     * @param int $nt_id Obligatoire, identifiant de la notification
     * @param $nt_type_id Obligatoire, identifiant de la notification
     * @param $nt_author_id Facultatif, identifiant de l'auteur la notification
     * @param $nt_author_name Facultatif, auteur de la notification
     * @param $nt_title Facultatif, titre de la notification
     * @param $nt_desc Facultatif, description de la notification
     * @param $nt_date_created Obligatoire, date de création de la notification
     * @param $notification_objects Facultatif, objects liés à la notification (user, order, products...)
     *
     * @return true si la mise à jour s'est déroulée avec succès.
     *
	 * @}
	*/
	case 'upd':

        if( !isset($_REQUEST['nt_id'])|| !is_numeric($_REQUEST['nt_id'])
        ||!isset($_REQUEST['nt_type_id'])|| !is_numeric($_REQUEST['nt_type_id'])
        ||!isset($_REQUEST['nt_author_id'])|| !is_numeric($_REQUEST['nt_author_id'])
        ||!isset($_REQUEST['nt_author_name'])
        ||!isset($_REQUEST['nt_title'])
        ||!isset($_REQUEST['nt_desc'])
        ||!isset($_REQUEST['nt_date_created'])  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['notification_objects']) ){
			$_REQUEST['notification_objects'] = array();
		}

		if( !notifications_upd( $_REQUEST['id'], $_REQUEST['nt_type_id'],$_REQUEST['nt_author_id'],$_REQUEST['nt_author_name'],$_REQUEST['nt_title'],$_REQUEST['nt_desc'],$_REQUEST['nt_date_created'],$_REQUEST['notification_objects'] )){
            throw new Exception('Erreur lors de la mise à jour de l\a notifications');
		}

        //Update des champs dans les tables liées
        //Pas encore géré
        // foreach( $_REQUEST['notification_objects'] as $notification_object ){
        //     nt_notification_objects_upd($notification_object['nto_nt_id'],$notification_object['nto_cls_id'], $notification_object['nto_obj_id_0'], $notification_object['nto_obj_id_1'], $notification_object['nto_obj_id_2'])
        // }

        //Faire pareil pour la table nt_notification_read
        //N'est pas encore géré coté Yuto

        // if( $is_sync ){
        // 	if( !notifications_set_is_sync($_REQUEST['id'], true) ){
        // 		throw new Exception('Erreur lors de la mise à jour de l\a notifications is sync');
        // 	}
        // } else {
        // 	if( !notifications_set_need_sync($_REQUEST['id'], true) ){
        // 		throw new Exception('Erreur lors de la mise à jour de l\a notifications needsync');
        // 	}
        // }

        $result = true;

		break;
}
///@}
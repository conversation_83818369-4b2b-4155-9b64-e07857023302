<?php
/** 
 * \defgroup api-chats-conversations Conversation 
 * \ingroup api-chats-index
 * @{
 * \page api-chats-conversations-add Ajout
 *
 * Cette fonction permet d'ajouter une conversation sur le chat 
 * 	
 *		\code
 *			POST /chats/conversations/
 *		\endcode
 *	
 * @param int $cls_id Obligatoire, identifiant de de la classe d'objet
 * @param int $obj_id_0 Obligatoire, identifiant de l'objet
 * @param int $obj_id_1 Obligatoire, identifiant de l'objet
 * @param int $obj_id_2 Obligatoire, identifiant de l'objet
 * @param date $date_created facultatif, Date de création de la conversation
 *	
 * @return id l'identifiant de la conversation
 * @}
*/

switch( $method ){
	case 'add':

		if(  !isset($_REQUEST['cls_id']) || !is_numeric($_REQUEST['cls_id'])
			|| !isset($_REQUEST['obj_id_0']) || !($_REQUEST['obj_id_0'])
			|| !isset($_REQUEST['obj_id_1'])
			|| !isset($_REQUEST['obj_id_2'])
			  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['date_created']) ){
			$_REQUEST['date_created'] = null;
		}

		$followers = array();
		$followers[] = $config['usr_id'];

		$ch_id = ch_conversations_add($followers, $_REQUEST['cls_id'], $_REQUEST['obj_id_0'], $_REQUEST['obj_id_1'], $_REQUEST['obj_id_2'], $_REQUEST['date_created']);
		if( !$ch_id ){
			throw new Exception('Erreur lors de la création de la conversation.');
		}

		$result = true;
		$content = array('id' => $ch_id);

		break;
}
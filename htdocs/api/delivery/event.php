<?php
/**
 *\defgroup delivery Livraison 
 *\ingroup scm
 * @{@}
 *  \defgroup api-delivery-event Evènement 
 *  \ingroup delivery
 * 	@{	   
 *	\page api-delivery-event-get Chargement
 *
 *	Cette fonction permet de récupérer un évènement de livraison
 *
 *		\code
 *			GET /delivery/event/
 *		\endcode
 *		
 * @param id Obligatoire, identifiant de l'évènement de livraison
 *
 * @return json sous la forme : 
 * 
 *		\code{.json}
 *			{
 *				"id" : identifiant de l'évènement,
 *				"name" : intitulé de l'évènement,
 *				"start" : date de début (inclus),
 *				"end" : date de fin (inclus),
 *				"start_en" : date de début format en,
 *				"end_en" : date defin format en
 *			},
 * 		\endcode 
 *	@}
*/
switch( $method ){
	case 'get':

		$_REQUEST['id'] = isset($_REQUEST['id']) && is_numeric($_REQUEST['id']) ? $_REQUEST['id'] : 0;

		$result = true;

		$revents = dlv_events_get($_REQUEST['id']);
		if( $revents && ria_mysql_num_rows($revents) ){
			while( $event = ria_mysql_fetch_assoc($revents) ){
				$content[] = $event;
			}
		}
		break;
}


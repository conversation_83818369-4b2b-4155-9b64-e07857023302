<?php
/**
 * \defgroup delivery_packages Types d'emballages 
 * \ingroup delivery
 * @{	
 * \page api-delivery-packages-get Chargement
 *
 * Cette fonction permet de récupérer types d'emballage
 *
 *		\code
 *			GET /delivery/packages/
 *		\endcode
 *
 * @return JSON sous la forme : 
 * \code{.json}
 *		{
 *			"id" : identifiant interne du colis,
 *			"width" : largeur du colis, en centimètres,
 *			"height" : hauteur du colis, en centimètres,
 *			"length" : longueur du colis, en centimètres,
 *			"name" : nom du colis,
 *			"price_ht" : prix hors taxes du colis, en euros,
 *			"supplier" : nom du fournisseur
 *		}	
 * \endcode			
 * @}
*/	
switch( $method ){
	case 'get':
		$result = true;

		$rpackages = dlv_packages_get();
		if( $rpackages && ria_mysql_num_rows($rpackages) ){
			while( $packages = ria_mysql_fetch_assoc($rpackages) ){
				$content[] = $packages;
			}
		}
		break;
}
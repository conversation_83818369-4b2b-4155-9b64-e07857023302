<?php
/** 
 * \defgroup api-rewards-stats Compte
 * \ingroup api-rewards
 * @{	
 *
 * \page api-rewards-stats-add Ajout/Mise à jour 
 *
 * Cette fonction permet l'ajout ( ou la mise à jour ) de points de fidelité sur un compte
 *
 *		\code
 *			POST /rewards/stats/
 *		 ou
 *			PUT /rewards/stats/
 *		\endcode
 *
 * @param raw_data Obligatoire, Donnée en json_decode
 *		\code{.json}
 *			{
 *					"id" : Facultatif, identifiant de la ligne de point
 *					"delete" : facultatif si true alors la ligne va être supprimé, dans ce cas précis l'id est obligatoire
 *					"usr" : Obligatoire, identifiant du client
 *					"pts" : nombre de points 
 *					"cls_id" : identifiant de la classe concerné par les pts
 *					"obj_id_0" : identifiant de l'obj
 *					"obj_id_1" : identifiant de l'obj
 *					"obj_id_2" : identifiant de l'obj
 *					"date_created" : date de création des points
 *					"name" : Désignation de la ligne de point	 
 *			}	
 *		\endcode
 *
 * @return Json, une liste avec les éléments traité sous la forme suivante :
 *		\code{.json}
 *			{
 *					"id" : identifiant de la ligne de point
 *					"usr" : identifiant du client
 *					"pts" : nombre de points 
 *					"cls_id" : identifiant de la classe concerné par les pts
 *					"obj_id_0" : identifiant de l'obj
 *					"obj_id_1" : identifiant de l'obj
 *					"obj_id_2" : identifiant de l'obj
 *					"date_created" : date de création des points
 *					"name" : Désignation de la ligne de point	 
 *			}	
 *		\endcode
 * @}
*/

switch( $method ){
	case 'upd': 
	case 'add': 
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		$content = array();
		foreach($objs as $stats){

			$id = isset($stats['id']) && is_numeric($stats['id']) ? $stats['id'] : 0;
			$delete = isset($stats['delete']) && $stats['delete'];
			$usr = isset($stats['usr']) && is_numeric($stats['usr']) ? $stats['usr'] : 0;
			$pts = isset($stats['pts']) && is_numeric($stats['pts']) ? $stats['pts'] : 0;
			$cls_id = isset($stats['cls_id']) && is_numeric($stats['cls_id']) ? $stats['cls_id'] : 0;
			$obj_id_0 = isset($stats['obj_id_0']) && is_numeric($stats['obj_id_0']) ? $stats['obj_id_0'] : 0;
			$obj_id_1 = isset($stats['obj_id_1']) && is_numeric($stats['obj_id_1']) ? $stats['obj_id_1'] : 0;
			$obj_id_2 = isset($stats['obj_id_2']) && is_numeric($stats['obj_id_2']) ? $stats['obj_id_2'] : 0;
			$date_created = isset($stats['date_created']) && isdateheure($stats['date_created']) ? dateheureparse($stats['date_created']) : null;
			$name = isset($stats['name']) ? $stats['name'] : "";
			$gescomref = isset($stats['gescomref']) ? $stats['gescomref'] : "";

			$data_for_response = array(
					"id" => $id,
					"delete" => $delete,
					"pts" => $pts,
					"usr" => $usr,
					"cls_id" => $cls_id,
					"obj_id_0" => $obj_id_0,
					"obj_id_1" => $obj_id_1,
					"obj_id_2" => $obj_id_2,
					"date_created" => $date_created,
					"name" => $name,
					"gescomref" => $gescomref
				);

			if( $delete ){
				if( !$id || !stats_rewards_del_by_ids($id) ){
					throw new Exception("Erreur lors de la suppression des pts de fidelité pour : ".print_r($data_for_response, true));
				}
			}else{

				if( !$usr ){
					throw new Exception("Paramètres invalide l'utilisateur est obligatoire.");
				}

				// si on a pas passer l'id, on check de voir si on trouve pas deja une ligne pour ces pts
				if( !$id && $cls_id && $obj_id_0 ){
					$keyobj = array($obj_id_0);
					if( $obj_id_1 ){
						$keyobj[] = $obj_id_1;
					}
					if( $obj_id_2 ){
						$keyobj[] = $obj_id_2;
					}
					$id = stats_rewards_objs_exists($cls_id, $keyobj, false, false, $name, '', true);
					$data_for_response['id'] = (int) $id;
				}

				// cas particulier dans le cas ou les pts sont négatif
				$cancel = false;
				if( $pts < 0 ){
					$cancel = true; 
					$pts = abs($pts);
				}

				if (!is_numeric($id) || $id <= 0) {
					// ajout
					$id = stats_rewards_add($usr, 0, 0, $cancel, $name, $cls_id, array($obj_id_0, $obj_id_1, $obj_id_2), $pts, false, false, -1, 0, $date_created);
					if( !$id ){
						throw new Exception("Erreur lors de l'ajout des pts de fidelité pour : ".print_r($data_for_response, true));
					}
					$data_for_response['id'] = $id;
				}else{
					// mise à jour
					if( !stats_rewards_update( $id, $name, $pts, 0, $cancel ) ){
						throw new Exception("Erreur lors de la mise à jours des pts de fidelité pour : ".print_r($data_for_response, true));
					}
				}
			}

			$content[] = $data_for_response;

		}
		$result = true;
		break;
}
<?php
/** 
 * \ingroup search
 * @{		
 * \page api-search-index-add Ajout / Mise à jour 
 *
 *	cette fonction indexe un contenu dans le moteur de recherche.
 *
 *		\code
 *			POST /search/
 *		\endcode
 *	
 *	 @param $cls_id Obligatoire, Identifiant de la classe (table "fld_classes")
 *	 @param $obj_id_0 Obligatoire,
 *	 @param $obj_id_1 Facultatif,
 *	 @param $obj_id_2 Facultatif,
 *	
 *	 @return true si l'ajout/la mise à jour s'est déroulé avec succès 
 * @}
*/

require_once('search_fields.inc.php');

switch( $method ){
	case 'add':
	case 'upd':
		if( !isset($_REQUEST['cls_id']) ){
			throw new BadFunctionCallException('L\'identifiant de classe est manquant.');
		}

		if( !isset($_REQUEST['obj_id_0']) ){
			throw new BadFunctionCallException('Certains paramètres sont manquants.');
		}

		$obj0 = $_REQUEST['obj_id_0'];

		if( is_numeric($obj0) && $obj0 > 0 ){
			switch( $_REQUEST['cls_id'] ){
				case CLS_BRAND:
					if( !prd_brands_index_add($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la marque #'.$obj0);
					}

					break;
				case CLS_CATEGORY:
					if( !prd_categories_index_add($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la catégorie #'.$obj0);
					}

					break;
				case CLS_CLASSIFY:
					if( !isset($_REQUEST['obj_id_1']) ){
						throw new RuntimeException('L\'identifiant catégorie est manquant.');
					}

					$languages = array('fr');

					$r_languages = wst_websites_languages_get();

					if( $r_languages && ria_mysql_num_rows($r_languages) ){
						while( $language = ria_mysql_fetch_assoc($r_languages) ){
							$languages[] = $language['lng_code'];
						}
					}

					$obj1 = $_REQUEST['obj_id_1'];

					foreach( $languages as $language ){
						if( !prd_search_results_add($obj0, $obj1, $language) ){
							throw new RuntimeException('Erreur lors de l\'indexation du classement (Catégorie #'.$obj1.', Produit #'.$obj0.')');
						}
					}

					break;
				case CLS_CGV_VERSION:
					if( !cgv_versions_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la version des CGV #'.$obj0);
					}

					break;
				case CLS_CMS:
					if( !cms_categories_index_add($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la page de contenu #'.$obj0);
					}

					break;
				case CLS_DOCUMENT:
					if( !doc_documents_add_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation du magasin #'.$obj0);
					}

					break;
				case CLS_FAQ_CAT:
					if( !faq_categories_add_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la catégorie (FAQ) #'.$obj0);
					}

					break;
				case CLS_FAQ_QST:
					if( !faq_questions_add_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la question (FAQ) #'.$obj0);
					}

					break;
				case CLS_MESSAGE:
					if( !gu_message_add_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation du message #'.$obj0);
					}

					break;
				case CLS_NEWS:
					if( !news_index_add($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de l\'actualité #'.$obj0);
					}

					break;
				case CLS_ORDER:
					// Seules les commandes non masquées sont indexées dans le moteur de recherche
					if( !ord_orders_is_masked($obj0) && !ord_orders_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la commande #'.$obj0);
					}

					break;
				case CLS_PRODUCT:
					if( !prd_products_index_rebuild($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation du produit #'.$obj0);
					}

					break;
				case CLS_STORE:
					if( !dlv_stores_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation du magasin #'.$obj0);
					}

					break;
				case CLS_TYPE_DOCUMENT:
					if( !doc_types_add_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation du type de document #'.$obj0);
					}

					break;
				case CLS_USER:
					if( !gu_users_is_tenant_linked($obj0) ){
						throw new RuntimeException('L\'utilisateur doit être lié à un "tenant".');
					}

					if( !gu_users_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de l\'utilisateur #'.$obj0);
					}

					break;
				case CLS_INVOICE:
					if( !ord_invoices_index($obj0) ){
						throw new RuntimeException('Erreur lors de l\'indexation de la facture #'.$obj0);
					}
					
					break;
			}
		}

		$result = true;

		break;
}
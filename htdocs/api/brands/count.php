<?php
/**
 * \defgroup count_brands Nombre de marques 
 * \ingroup brands
 * @{
 * \page api-brands-count-get Chargement
 *
 * Cette fonction retourne le nombre total de marques enregistrées
 *
 *		\code
 *			GET /brands/count/
 *		\endcode
 *
 * @return int Le nombre de marques (publiées et non publiées)
 * @}
*/
switch( $method ){
	case 'get':

		$result = true;
		$content = prd_brands_get_count();

		break;
}

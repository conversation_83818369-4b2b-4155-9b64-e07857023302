<?php
/**
 * \defgroup api-fields Champ avancé 
 * \ingroup config
 * @{@}
 * \defgroup api-fields-object-values Valeur 
 * \ingroup  api-fields
 * @{	 
 * \page api-fields-object-values-upd Mise à jour 
 *
 * Cette fonction permet de mettre à jour une valeur de champ avancé
 *	
 *		\code
 *			PUT /fields/objects_values/
 *		\endcode
 *	
 * @param cls_id Obligatoire, identifiant de la classe de l'objet
 * @param obj_id_0 Obligatoire, première partie de l'identifiant de l'objet
 * @param obj_id_1 Facultatif, deuxiéme partie de l'identifiant de l'objet
 * @param obj_id_2 Facultatif, troisième partie de l'identifiant de l'objet
 * @param fld_id Obligatoire, identifiant du champ avancé
 * @param value Obligatoire, valeur
 *	
 * @return true si la mise à jour s'est correctement déroulée 
 * @}
*/

// \cond onlyria
function save_data($fld){
	if( !isset($fld['value']) ){
		$fld['value'] = "";
	}


	if( !isset($fld['obj_id_0'],$fld['fld_id'],$fld['value']) || !is_numeric($fld['fld_id']) ){
		throw new Exception("Paramètres invalide");
	}
	if( !isset($fld['obj_id_1']) ) $fld['obj_id_1'] = 0;
	if( !isset($fld['obj_id_2']) ) $fld['obj_id_2'] = 0;
	if( !isset($fld['lng']) ) $fld['lng'] = 'FR';

	if( !is_numeric($fld['obj_id_0']) || !is_numeric($fld['obj_id_1']) || !is_numeric($fld['obj_id_2']) ){
		throw new Exception( "Les identifiants ne sont pas valide." );
	}

	fields_sync(isset($fld['cls_id']) ? $fld['cls_id'] : -1, $fld['obj_id_0'],$fld['obj_id_1'],$fld['obj_id_2'], array(array(
		'lng' => $fld['lng'],
		'fld_id' => $fld['fld_id'],
		'value' => $fld['value']
	)), false);

	return true;
}
// \endcond
switch( $method ){
	case 'upd': // mise à jour d'object values

		// controle les paramètres d'entrée
		if( !isset($_REQUEST['obj_id_0'],$_REQUEST['fld_id'],$_REQUEST['value']) || !is_numeric($_REQUEST['fld_id']) ){

			// mode "bulk"
			if( !is_array($objs) ){
				throw new Exception("Paramètres invalide");
			}

			foreach($objs as $fld){
				if( !save_data($fld) ){
					throw new Exception("Erreur d'ajout du champ ".print_r($fld, true));
				}
			}
			$result = true;

		}else{
			$result = save_data($_REQUEST);
		}

		break;
}

<?php
/** 
 * \defgroup api-users-deleted Virtuellement supprimé 
 * \ingroup crm
 * @{		 
 * \page api-users-deleted-get Chargement
 *
 *	cette fonction permet de savoir si un utilisateur est supprimé ou non 
 *
 *	 \code
 *		GET /users/deleted/
 *	 \endcode
 *	
 * @param int $id obligatoire, identifiant d'utilisateur
 *	
 * @return json sous la forme :
 *	\code{.json}
 *		{
 *      	"deleted": true si l'utilisateur est supprimé, False si il ne l'est pas 
 *		}
 * 	\endcode 	
*/
switch( $method ){
	case 'get': 
	
		// Paramètre identifiant
		if( !isset($_REQUEST['id']) ||  !is_numeric($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide");
		}

		$result = true;
		$content = array('deleted' => gu_users_is_virtual_deleted($_REQUEST['id']));

		break;
}

/// @}
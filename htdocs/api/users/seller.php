<?php
/** 
 * \defgroup api-users-seller Représentant    
 * \ingroup crm
 * @{
 * \page api-users-seller-upd Mise à jour 
 *
 * Cette fonction assigne un représentant à un client.
 *
 *		\code
 *			PUT /users/seller/
 *		\endcode
 *	
 * @param raw_data Obligatoire, Donnée en json_decode tel que :
 *		\code{.json}
 *			{
 *					"usr_id"			Obligatoire	: Identifiant du Client
 *					"usr_seller_id"		Obligatoire	: Identifiant du Représentant
 *			}
 *		\endcode
 *	
 * @return true si la mise à jour s'est déroulée avec succès 
*/
switch( $method ){
    case 'upd':
		global $method, $config;
		$obj = json_decode($raw_data, true);
		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $user){

			if(!isset($user["usr_id"], $user["usr_seller_id"])){
				throw new Exception("Paramètres invalide : usr_id = ".$user["usr_id"]." : usr_seller_id = ".$user["usr_seller_id"]);
			}

			if(!gu_users_set_seller_id( $user["usr_id"], $user["usr_seller_id"] )){
				throw new Exception("La mise à jour des représentants a échoué.");
			}
		}
		$result = true;
		break;
}

///@}
<?php
/**
 * \defgroup api-users-sell-units Règles d'unité de vente par client
 * \ingroup users
 * @{
*/

require_once('usr/sell-units-rules.inc.php');

switch( $method ){
	/** @{@}
 	 *	@{
 	 * \page api-users-bank-get Récupération des règles
 	 *
 	 * Cette fonction permet de récupérer les règles d'unité de vente selon certains critères.
 	 *	@param int $rule_id Optionnel, identifiant d'une règle
 	 * 	@param int $usr_id Optionnel, identifiant du client
 	 * 	@param int $cls_id Optionnel, classe d'objet sur lequel la règle est appliquée
 	 * 	@param int|array $obj Optionnel, tableau d'identifiant de l'objet
 	 * 	@param bool|string $last_modified Optionnel, date de dernière modification (seules les règles créée ou modifié après cette date inclues seront
 	 * retournées). La date doit être fournie au format suivant AAAA-MM-JJ [HH:MM:SS] (heure optionnel)
 	 * 	@param array $compl_data Optionnel, permet de compléter les données retournées par la fonction, valeurs acceptées :
 	 * 		- user : retourne des données supplémentaire sur le client ($compl_data = ['user' => true])
 	 * 								- firstname : prénom
 	 * 								- lastname : nom
 	 * 								- society : société
 	 * 								- email : adresse mail
 	 * 	@param array $sort Optionnel, permet de trier le résultat (par défaut, dernière règle créée), le tri se fait sur les colonnes du select (alias)
 	 * 	@param array $limit Optionnel, permet de retourner un résultat paginé, clé acceptée 'offset' et 'limit', par défault 'offset' sera à 0 et 'limit' au maximum
 	 *
	 * 	@return json Liste des règles contenant les colonnes suivantes :
	 *	\code{.json}
	 *		{
 	 * 			"id" : identifiant de la règle
 	 * 			"usr_id" : identifiant du compte client
 	 * 			"cls_id" : identifiant de la classe
 	 * 			"obj_id_1" : partie 1 de la clé de l'objet
 	 * 			"obj_id_2" : partie 2 de la clé de l'objet
 	 * 			"obj_id_3" : partie 3 de la clé de l'objet
 	 * 			"sell_unit" : unité de vente
 	 * 			"date_created" : date de création
 	 * 			"date_modified" : date de dernière modification
	 * 			"is_sync" : si oui (1) ou non (0) la règle est synchronisée
	 *		}
	 *	\endcode
	 *	@}
	 */
	case 'get':
		// Initialise les paramètres d'entrées
		$_REQUEST['rule_id'] = isset($_REQUEST['rule_id']) ? $_REQUEST['rule_id'] : 0;
		$_REQUEST['usr_id'] = isset($_REQUEST['usr_id']) ? $_REQUEST['usr_id'] : 0;
		$_REQUEST['cls_id'] = isset($_REQUEST['cls_id']) ? $_REQUEST['cls_id'] : [];
		$_REQUEST['obj'] = isset($_REQUEST['obj']) ? $_REQUEST['obj'] : 0;
		$_REQUEST['last_modified'] = isset($_REQUEST['last_modified']) ? $_REQUEST['last_modified'] : false;
		$_REQUEST['compl_data'] = isset($_REQUEST['compl_data']) ? $_REQUEST['compl_data'] : [];
		$_REQUEST['sort'] = isset($_REQUEST['sort']) ? $_REQUEST['sort'] : [];
		$_REQUEST['limit'] = isset($_REQUEST['limit']) ? $_REQUEST['limit'] : [];

		$r_rules = gu_sell_units_rules_get(
			$_REQUEST['rule_id'], $_REQUEST['usr_id'], $_REQUEST['cls_id'], $_REQUEST['obj'], $_REQUEST['last_modified'], $_REQUEST['compl_data'],
			$_REQUEST['sort'], $_REQUEST['limit']
		);

		if( !is_resource($r_rules) || !ria_mysql_num_rows($r_rules) ){
			throw new Exception( usr_sell_units_rules_error($r_rules) );
		}

		$ar_rules = [];

		while( $rule = ria_mysql_fetch_assoc($r_rules) ){
			$ar_rules[] = $rule;
		}

		$result = true;
		$content = $ar_rules;
		break;

	/** @{@}
 	 *	@{
 	 * 	\page api-users-bank-add Ajouter une nouvelle règle
 	 *
	 * 	Cette fonction permet d'ajout une nouvelle règle d'unité de vente.
	 *  Si une règle est déjà en place, elle sera mise à jour.
	 * 	@param int $usr_id Obligatoire, identifiant du client
	 * 	@param int $cls_id Obligatoire, classe d'objet sur lequel la règle est appliquée
	 * 	@param int|array $obj Obligatoire, tableau d'identifiant de l'objet
	 * 	@param float $sell_unit Obligatoire, $sell_unit Obligatoire, unité de vente
	 *
	 * 	@return json Liste des règles contenant les colonnes suivantes :
	 *	\code{.json}
	 *		{
	 *			"id" : identifiant de la nouvelle règle
	 * 		}
	 *	\endcode
	 *	@}
	 */
	case 'add':
		// Initialise les paramètres d'entrée
		$_REQUEST['usr_id'] = isset($_REQUEST['usr_id']) ? $_REQUEST['usr_id'] : false;
		$_REQUEST['cls_id'] = isset($_REQUEST['cls_id']) ? $_REQUEST['cls_id'] : false;
		$_REQUEST['obj'] = isset($_REQUEST['obj']) ? $_REQUEST['obj'] : false;
		$_REQUEST['sell_unit'] = isset($_REQUEST['sell_unit']) ? $_REQUEST['sell_unit'] : false;

		$res = gu_sell_units_rules_add( $_REQUEST['usr_id'], $_REQUEST['cls_id'], $_REQUEST['obj'], $_REQUEST['sell_unit'], 1 );
		if( !is_numeric($res) || $res <= 0 ){
			throw new Exception( usr_sell_units_rules_error($res) );
		}

		$result = true;
		$content = [ 'id' => $res ];
		break;

	/** @{@}
 	 *	@{
 	 * 	\page api-users-bank-upd Mise à jour d'une nouvelle règle
 	 *
	 * 	Cette fonction permet de mettre à jour une règle d'unité de vente.
 	 *	@param int $rule_id Obligatoire, identifiant d'une règle
	 * 	@param float $sell_unit Obligatoire, unité de vente
	 *
	 * 	@return true si la mise à jour s'est déroulée avec succès
	 *	@}
	 */
	case 'upd':
		// Initialise les paramètres d'entrée
		$_REQUEST['rule_id'] = isset($_REQUEST['rule_id']) ? $_REQUEST['rule_id'] : false;
		$_REQUEST['sell_unit'] = isset($_REQUEST['sell_unit']) ? $_REQUEST['sell_unit'] : false;

		$res = gu_sell_units_rules_upd( $_REQUEST['rule_id'], $_REQUEST['sell_unit'], true );
		if( $res !== true ){
			throw new Exception( usr_sell_units_rules_error($res) );
		}

		$result = true;
		break;

	/** @{@}
 	 *	@{
 	 * 	\page api-users-bank-del Supprime une ou plusieurs règles
 	 *
	 * 	Cette fonction permet de supprimer une ou plusieurs règles d'unité de vente.
	 *	@param int $rule_id Obligatoire, identifiant ou tableau d'identifiants de règles à supprimer
	 *
	 * 	@return true si la mise à jour s'est déroulée avec succès
	 *	@}
	 */
	case 'del':
		// Initialise les paramètres d'entrée
		$_REQUEST['rule_id'] = isset($_REQUEST['rule_id']) ? $_REQUEST['rule_id'] : false;

		$res = gu_sell_units_rules_del( $_REQUEST['rule_id'], true );
		if( $res !== true ){
			throw new Exception( usr_sell_units_rules_error($res) );
		}

		$result = true;
		break;
}

/** Cette fonction permet de générer un message d'erreur personnalisé selon le code erreur retourné par le moteur.
 * 	@param int $code Obligatoire, code erreur
 * 	@return string Le message d'erreur correspondant au code fourni en argument
 */
function usr_sell_units_rules_error( $code ){
	if( !is_numeric($code) ){
		$code = 99;
	}

	switch( $code ){
		case -1 :
			$error = _('identifiant de la règle incorrect');
			break;
		case -2 :
			$error = _('identifiant client incorrect');
			break;
		case -3 :
			$error = _('identifiant de la classe incorrect');
			break;
		case -4 :
			$error = _('identifiant d\'objet incorrect');
			break;
		case -6 :
			$error = _('date de dernière modification incorrecte');
			break;
		case -98 :
			$error = _('la règle n\'existe pas');
			break;
		case -99 :
		default :
			$error = _('erreur survenue lors de la récupération des règles');
			break;
	}

	return $error.' => '.print_r($_REQUEST, true);
}

/// @}
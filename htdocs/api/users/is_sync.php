<?php

/** 
 * \defgroup api-users-is_sync Synchronisation utilisateur 
 * \ingroup crm
 * @{		 
 * \page api-users-is_sync-get Chargement
 *
 *	cette fonction permet de savoir si un utilisateur est synchronisé avec la gestion commerciale ou non  
 *
 *	 \code
 *		GET /users/is_sync/
 *	 \endcode
 *	
 * @param int $id obligatoire, identifiant d'utilisateur
 *	
 * @return json sous la forme :
 *	\code{.json}
 *		{
 *      	"is_sync": true si l'utilisateur est synchronisé avec la gestion commerciale, False si il ne l'est pas 
 *		}
 * 	\endcode 	
 * @}
*/

switch( $method ){

	//Retourne true si l'utilisateur est synchronisé avec la gestion commerciale, false sinon.
	case 'get':

		// Paramètre identifiant
		if( !isset($_REQUEST['id']) ||  !is_numeric($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide");
		}

		$result = true;
		$is_sync = false;
		$rusr = gu_users_get($_REQUEST['id']);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);
			$is_sync= $usr['is_sync'];
		}
		$content = array('is_sync' => $is_sync);

		break;
}


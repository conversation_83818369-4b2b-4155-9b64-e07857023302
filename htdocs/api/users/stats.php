<?php

require_once('http.inc.php');

/**
 * \defgroup api-users-stats Comptes utilisateurs
 * \ingroup stats 
 *	@{
 *
 * \page api-users-stats-get Chargement
 * 
 * Cet appel retourne les statistiques suivantes sur les comptes clients
 *		- count : nombre de comptes utilisateurs
 *		- orders : nombre de commandes
 *		- total_ht : totaux hors taxes des commandes
 *		- total_ttc : totaux toutes taxes comprises
 *		- margin : marge totale HT
 *		- products : nombre de produits commandés (tient compte de la quantité)
 *		- skus : nombre de produits uniques commandés 
 *
 * \code
 *		GET /users/stats/
 * \endcode
 *
 * @param int $usr		   Facultatif, Identifiant (ou tableau d'identifiants) du compte client sur lequel filtre le résultat
 * @param int $prf         Facultatif, Identifiant (ou tableau d'identifiants) du profil utilisateur sur lequel filtrer le résultat (ou tableau d'identifiants)
 * @param int $seg         Facultatif, Identifiant (ou tableau d'identifiants) du segment sur lequel filtrer le résultat
 * @param int $seller      Facultatif, Identifiant (ou tableau d'identifiants) du compte représentant sur lequel filtrer le résultat
 * @param int $dateStart   Obligatoire, date de début pour le calcul des statistiques
 * @param int $dateStop    Obligatoire, date de fin pour le calcul des statistiques
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 *	\code{.json}
 *		{
 *		 "users" : nombre de comptes utilisateurs,
 *		 "orders" : nombre de commandes,
 *		 "conversion" : taux de conversion prospect / client,
 *		 "total_ht" : totaux hors taxes des commandes,
 *		 "total_ttc" : totaux toutes taxes comprises,
 *		 "margin" : marge totale HT,
 *		 "products" : nombre de produits commandés (tient compte de la quantité),
 *		 "skus" : nombre de produits uniques commandés
 *		}
 * 	\endcode 
*/

switch( $method ){
	case 'get':

		// Le résultat est mis en cache pour 15 minutes
		http_cache_control( 900 );

		// Filtres sur la période
		if(
			!isset($_GET['dateStart']) || !trim($_GET['dateStart']) || !isdate($_GET['dateStart'])
			|| !isset($_GET['dateStop']) || !trim($_GET['dateStop']) || !isdate($_GET['dateStop'])
		){
			throw new Exception("Les dates de début et de fin sont obligatoires (dateStart et dateStop)");
		}

		// Filtre sur le compte client
		$users = 0;
		if( isset($_GET['usr']) && is_array($_GET['usr']) ){
			foreach( $_GET['usr'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants utilisateurs fournis en arguments sont incorrects");
				}
			}
			$users = $_GET['usr'];
		}elseif( isset($_GET['usr']) && is_numeric($_GET['usr']) ){
			$users = $_GET['usr'];
		}

		// Filtre sur le profil utilisateur
		$profiles = 0;
		if( isset($_GET['prf']) && is_array($_GET['prf']) ){
			foreach( $_GET['prf'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants de profils fournis en arguments sont incorrects");
				}
			}
			$profiles = $_GET['prf'];
		}elseif( isset($_GET['prf']) && is_numeric($_GET['prf']) ){
			$profiles = $_GET['prf'];
		}

		// Filtre sur le segment
		$segments = 0;
		if( isset($_GET['seg']) && is_array($_GET['seg']) ){
			foreach( $_GET['seg'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants de segments fournis en arguments sont incorrects");
				}
			}
			$segments = $_GET['seg'];
		}elseif( isset($_GET['seg']) && is_numeric($_GET['seg']) ){
			$segments = $_GET['seg'];
		}

		// Filtre sur le représentant
		$sellers = 0;
		if( isset($_GET['seller']) && is_array($_GET['seller']) ){
			foreach( $_GET['seller'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants de représentant fournis en arguments sont incorrects");
				}
			}
			$sellers = $_GET['seller'];
		}elseif( isset($_GET['seller']) && is_numeric($_GET['seller']) ){
			$sellers = $_GET['seller'];
		}

		// Calcule les statistiques sur les comptes clients.
		$content = gu_users_get_stats( $users, $profiles, $sellers, $segments, $_GET['dateStart'], $_GET['dateStop'] );
		$result = is_array($content) && $content['count']!==false;

	break;
}

///@}
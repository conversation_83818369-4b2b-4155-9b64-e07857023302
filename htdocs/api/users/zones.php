<?php

//	\cond onlyria

/** 
 * \defgroup api-users-undelete Démasque un client 
 * \ingroup api-users-index
 * @{		
 * \page api-users-zones Code postaux, DEPRECIER, ce fichier doit disparaitre
 *	 - \subpage api-users-zones-get
 *	
 *	
 *	 \page api-users-zones-get Chargement
 *	 \code
 *		GET
 *		/users/zones/?logtoken={token}
 *	 \endcode
 *	 Cette fonction permet de récupérer tous les code postaux
 *	
 *	 @return array Tableau des zones
 *					- insee : Code insee
 *					- name : Nom de la ville
 *					- zipcode : Code postal
*/

switch( $method ){

	case 'get':
		$result = true;

		$rzones = sys_zones_get(0,'','',false,0,'',0,array(),-1,-1,true,true);

		if( $rzones && ria_mysql_num_rows($rzones) ){

			$zip_url = dirname(__FILE__).'/../upload-temp/zones-'.$config['tnt_id'].'-'.$config['dev_id'].'.zip';
			if( !file_exists($zip_url) ){
				$zip = new ZipArchive();
				if($zip->open($zip_url, ZIPARCHIVE::CREATE) == TRUE){

					$pas = 500;

					// les tablettes avec android 4.0 ( version 15 ) ne supporte pas les insert multiples
					if( $config['dev_os_version'] < 16 && $config['dev_brand']!='Apple' ){
						$pas = 1;
					}

					// préparation de la réponse en compactant les données ( préformaté pour une insertion en base de donnée )

					$cpt = 0;

					$sql_final = "";
					while( $zones = ria_mysql_fetch_assoc($rzones) ){
						if( $cpt>0 && $cpt%$pas == 0 ){
							$sql_final .= ';'."\n";
							$cpt = 0;
						}
						if( $cpt==0 ){
							$sql_final .= "insert into sys_zones (dzn_id,dzn_code,dzn_name,dzn_country,dzn_type_id,dzn_parent_id) values ";
						}

						$sql_final .= ($cpt>0 ? ',':'').'('.$zones['id'].',
									'.( !$zones['code'] ? 'null' : '"'.addslashes($zones['code']).'"').',
									'.( !$zones['name'] ? 'null' : '"'.addslashes($zones['name']).'"').',
									'.( !$zones['country_code'] ? 'null' : '\''.addslashes($zones['country_code']).'\'').',
									'.$zones['type_id'].',
									'.( !$zones['parent_id'] ? 'null' : ''.$zones['parent_id'].'').'
								)';

						$cpt++;
					}

					$sql_final .= ';'."\n";
					$zip->addFromString('zones.sql',  $sql_final);

					$zip->close();
				}
			}

			header("Content-type: application/octet-stream");
			header("Content-disposition: attachment; filename=zones.zip");
			ob_clean();
			flush();
			readfile($zip_url);
			@unlink($zip_url);
			exit;

		}
		break;
}

// \endcond
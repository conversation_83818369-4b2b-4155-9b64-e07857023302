<?php
/** 
 * \defgroup api-users-price_categories Catégories tarifaires   
 * \ingroup crm
 * @{	
 * \page api-users-price-categories-upd Mise à jour
 *
 * cette fonction permet la mise à jour de la catégorie tarifaire d'un client
 *
 *		\code
 *			PUT /users/price_categories/
 *		\endcode
 *
 * @param raw_data Obligatoire, Données en json_decode sous la forme :
 *		\code{.json}
 *			{
 *				"usr"			Obligatoire	: Identifiant du client
 *				"prc"			Obligatoire	: Identifiant de la catégorie tarifaire
 *			}
 *		\endcode
 *
 * @return true si la mise à jour s'est déroulé avec succès 
 * @}
*/

switch( $method ){
	case 'upd':
		global $method, $config;
		$obj = json_decode($raw_data, true);
		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $user){
			if(!isset($user["usr"], $user["prc"])){
				throw new Exception("Paramètres invalide");
			}
			if(!gu_users_set_prc($user['usr'], $user['prc'])){
				throw new Exception("La mise à jour de la catégorie tarifaire des clients à échouer.");
			}
		}
		$result = true;
		break;
}
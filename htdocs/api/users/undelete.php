<?php
/** 
 * \defgroup api-users-undelete Démasque un client 
 * \ingroup crm
 * @{		
 * \page api-users-undelete-upd Mise à jour
 *
 * Cette fonction permet de démasquer un client suite à un ajout
 *
 *	 	\code
 *			PUT /users/undelete/
 *		 \endcode
 *	
 * @param int $usr Obligatoire, Identifiant du compte client a activer
 *	
 * @return true si le client est désormais bien démasqué
 *	
*/

switch( $method ){

	case 'upd': 

		if( !isset($_REQUEST['usr']) ){
			throw new Exception( "Paramètre invalide.");
		}

		if( usr_restore_account($_REQUEST['usr']) ){
			$result = true;
		}
		
		break;
}

///@}
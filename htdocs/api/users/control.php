<?php
/** 
 * \defgroup api-users-control Contrôle de validité  
 * \ingroup crm
 * @{		 
 * \page api-users-control-get Chargement
 *
 *	cette fonction permet de savoir si un utilisateur est valide ou non pour sa création
 *
 *	 \code
 *		GET /users/control/
 *	 \endcode
 *	
 *	 @param int $usr_id Facultatif, identifiant d'utilisateur
 *	 @param string $usr_email Facultatif, adresse email d'un utilisateur
 *	
 *	 @return Liste d'utilisateur contenant les colonnes :
 *			- result : True si le compte peut être créer sans contrainte, False dans le cas contraire
 *			- message : Erreur potentiel pour la création du compte
*/
switch( $method ){
	case 'get':

		$usr_id = isset($_REQUEST['usr_id']) && is_numeric($_REQUEST['usr_id']) ? $_REQUEST['usr_id'] : 0;

		if( isset($_REQUEST['usr_email']) && trim($_REQUEST['usr_email']) ){
			if( !gu_valid_email($_REQUEST['usr_email'])){
				throw new Exception("Le domaine de l'adresse email n'est pas valide.");
			}
			if( !gu_users_check_email($_REQUEST['usr_email'], $usr_id)){
				throw new Exception("L'adresse email est déjà utilisé par un autre compte.");
			}
		}

		// suivant une variable de config du style :
		// 	key, key
		//	key
		// on controle l'unicité des lignes dans la base de donnée.
		if( isset($config['fdv_usr_edit_fields_unique']) && $config['fdv_usr_edit_fields_unique'] ){

			$rules = explode("\n", $config['fdv_usr_edit_fields_unique']);
			if( $rules && sizeof($rules) ){
				foreach( $rules as $rule ){
					$rule_is_valid = true;

					$fields = array();
					$keys = explode(",", $rule);
					if( $keys && sizeof($keys) ){
						foreach( $keys  as $key ){
							$key = trim($key);
							if( isset($_REQUEST[$key]) && trim($_REQUEST[$key]) ){
								$fields[$key] = $_REQUEST[$key];
							}
						}
					}

					if( sizeof($fields) && !gu_users_check_fields($usr_id, $fields) ){
						throw new Exception("Ce compte est déjà utilisé dans la base de donnée.");
					}
				}
			}

		}

		$result = true;

		break;
}

///@}
<?php
/**
 * \defgroup api-relations Relations entre objets
 * \ingroup config 
 * @{
*/
switch( $method ){
	/** @{@}
 	 * @{	
	 * \page api-relations-index-add Ajout 
	 *
	 * Cette fonction permet d'ajouter une relation entre deux objets.
	 *
	 *	\code
	 *		POST /relations/
	 *	\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode : 
	 *		\code{.json}
	 *			{
	 *			 "type"	Obligatoire	: identifiant du type de relation
	 *			 "src"	Obligatoire	: identifiant de l'objet source ou tableau d'identifiants dans le cas de clé composé
	 *			 "dst"	Facultatif	: identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
	 *			 "ref"	Facultatif	: référence de la relation
	 *			}
	 *		\endcode
	 * @return true si l'ajout s'est déroulé avec succès 
	 *
	 * @}
	*/
	case 'add': 
		global $method, $config;

		if(!isset($_REQUEST["src"], $_REQUEST["type"])){
			throw new Exception("Paramètres invalide");
		}

		// tente de voir si la relations existe ou pas, si oui on retourne l'id de la relation plutot que de lancer une exception
		$r = rel_relations_get( 0, $_REQUEST["src"], $_REQUEST["dst"], $_REQUEST["type"], $_REQUEST["ref"]);
		if( $r && ria_mysql_num_rows($r) ){
			$r = ria_mysql_fetch_assoc($r);
			$id = $r['id'];
		}else{
			$id = rel_relations_add($_REQUEST["src"], $_REQUEST["dst"], $_REQUEST["type"], $_REQUEST["ref"]);
		}


		if($id && $id>0){
			$result=true;
			$content = array('rro_id' => $id);
		}else if($id && $id < 0){
			throw new Exception(rel_err_describe($id));
		}else{
			throw new Exception("Une erreur est survenue lors de l'ajout de la relation.");
		}

		break;
	/** @{@}
 	 * @{	
	 * \page api-relations-index-del Suppression 
	 *
	 * Cette fonction permet la suppression des relations entre objets
	 *
	 *	\code 
	 *		DELETE /relations/
	 *	\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode
	 *		\code{.json}
	 *			{
	 *			  "array"   Obligatoire : Tableau de relations à supprimer
	 *			  "rro_id"	Obligatoire	: Identifiant d'une relation à supprimer
	 *			}
	 *		\endcode
	 *
	 * @return true si la suppression s'est déroulé avec succès 
     *      
	 * @}
	*/
	case 'del': 
		global $method, $config;
		$obj = json_decode($raw_data, true);

		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $rel){
			if(!isset($rel["rro_id"])){
				throw new Exception("Paramètres invalide");
			}
			if(!rel_relations_del($rel['rro_id'])){
				throw new Exception("Une erreur est survenue lors de la suppression de la relation ".$rel['rro_id'].".");
			}
		}
		$result = true;
		break;
}
///@}
<?php
/** 	 
 * \cond onlyria
 * \ingroup oms
 * \page api-bordereaux-index-add Ajout 
 * 
 * cette fonction permet de créer une archive (.zip) contenant les bordereaux des commandes reçues.
 *
 * 	\code
 *		POST /bordereaux/index/
 * 	\endcode
 *
 * @param pieces Obligatoire, Tableau d'identifiants de commande facturées à prendre en compte lors de la création des bordereaux
 *
 * @return true si l'ajout s'est déroulé avec succès. 
 *
*/

require_once('orders.inc.php');

switch( $method ){
	case 'add':
		if( !isset($_REQUEST['pieces']) ){
			throw new BadFunctionCallException('Les numéros de pièce sont manquants.');
		}

		$pieces = json_decode($raw_data);

		if( json_last_error() !== JSON_ERROR_NONE ){
			throw new BadFunctionCallException('Le JSON fourni n\'est pas bien formatté.');
		}

		require_once('/var/www/www.proloisirs.fr/cron/send_bordereaux.php');

		proloisirs_generate_zip_bordereaux(
			implode(',', $pieces->pieces)
		);

		$result = true;

		break;
}

// \endcond
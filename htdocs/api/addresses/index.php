<?php
/**
 * \defgroup adresses Adresses
 * \ingroup crm
 * @{
*/
	switch( $method ){
		/** @{@}
 		 * @{
		 * \page api-addresses-index-get Chargement
		 *
		 * Cette fonction récupére une liste d'adresses
		 *
		 *		\code
		 *			GET /addresses/
		 *		\endcode
		 *
		 * @param id  Obligatoire, identifiant ou tableau d'identifiants des adresses à récupérer
		 *
		 * @return Json Liste d'adressses sous la forme suivante :
		 * \code{.json}
		 *	{
		 *		"usr_id": identifiant de l'utilisateur,
		 *		"id": identifiant de l'adresse,
		 *		"type_id": identifiant du type d'adresse (1 : Particulier, 2 : Professionnel, 3 : Mixte),
		 *		"type_name": désignation du type d'adresse,
		 *		"title_id": identifiant de la civilité,
		 *		"title_name": libellé de la civilité,
		 *		"firstname": prénom (si type=1 ou 3),
		 *		"lastname": nom de famille (si type=1 ou 3),
		 *		"society": nom de l'entreprise (si type=2 ou 3),
		 *		"siret": Sir<PERSON> de l'entreprise,
		 *		"address1": première composante de l'adresse,
		 *		"address2": seconde composante de l'adresse,
		 *		"address3": troisième composante de l'adresse,
		 *		"postal_code": code postal,
		 *		"zipcode": code postal (alias),
		 *		"city": ville,
		 *		"country": pays,
		 *		"country_code": code du pays ISO,
		 *		"phone": numéro de téléphone,
		 *		"fax": fax,
		 *		"mobile": numéro de téléphone portable,
		 *		"phone_work": numéro de téléphone en journée,
		 *		"description": description de l'adresse,
		 *		"email": adresse email,
		 *		"date_created": "YYYY-MM-DD HH:MM:SS",
		 *		"date_modified": "YYYY-MM-DD HH:MM:SS,
		 *		"manual_location": "0",
		 *		"date_masked": date à laquelle l'adresse a été masqué (null si non masquée)
		 *		"latitude": Latitude,
		 *		"longitude": longitude,
		 *		"date_location": date de location,
		 *		"ref_gescom": reférence de l'adresse dans la gestion commerciale,
		 *		"country_state": Province
		 *	}
		 * \endcode
		 * @}
		*/
		case 'get':

			$ids = 0;
			if( isset($_GET['id']) && is_array($_GET['id']) ){
				foreach( $_GET['id'] as $id ){
					if( !is_numeric($id) ){
						throw new Exception("Les identifiants fournis en arguments sont incorrects");
					}
				}
				$ids = $_GET['id'];
			}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
				$ids = $_GET['id'];
			}

			$array = array();
			$radr = gu_adresses_get(0,$ids);

			while($adr = ria_mysql_fetch_assoc($radr)){
				$array[] = $adr;
			}

			$result = true;
			$content = $array;

			break;
		/**
 		 * @{
		 * \page api-addresses-index-add Ajout
		 *
		 * Cette fonction enregistre une nouvelle adresse de livraison ou de facturation.
		 *
		 *		\code
		 *			POST /addresses/
		 *		\endcode
		 *
		 * @param int usr_id Obligatoire, identifiant de l'utilisateur auquel l'adresse est rattachée.
		 * @param string title Obligatoire, Identifiant du titre (seulement si particulier)
		 * @param string firstname Obligatoire, Prénom de l'utilisateur (seulement si particulier)
		 * @param string lastname Obligatoire, Nom de l'utilisateur (seulement si particulier)
		 * @param string society Obligatoire, Nom de l'entreprise (seulement si professionnel)
		 * @param string siret Obligatoire, No SIRET (seulement si professionnel)
		 * @param string address1 Obligatoire, Première partie de l'adresse postale
		 * @param string address2 Obligatoire, Seconde partie de l'adresse postale
		 * @param string address3 Obligatoire, Troisième partie de l'adresse postale
		 * @param string zipcode Obligatoire, Code postal
		 * @param string city Obligatoire, Ville
		 * @param string country Obligatoire, Pays
		 * @param string phone Obligatoire, Numéro de téléphone
		 * @param string fax Obligatoire, Numéro de fax
		 * @param string mobile Facultatif, Numéro de téléphone portable
		 * @param string work Facultatif, Numéro de téléphone de travail
		 * @param string desc Facultatif, Description
		 * @param string ref_gescom Facultatif, Référence de l'adresse dans la gestion commerciale
		 * @param bool manual_location Facultatif, permet de rentrer une localisation manuelle (==1)
		 * @param float longitude Facultatif, longitude de la localisation manuelle
		 * @param float latitude Facultatif, latitude de la localisation manuelle
		 *
		 * @return Json Liste d'adressses sous la forme suivante :
		 *	\code{.json}
		 *		{
		 *		"id" : Identifiant de l'adresse créée
		 *		}
		 * 	\endcode
		 * @}
		*/
		case 'add':
			if( !isset($_REQUEST['address3']) ) $_REQUEST['address3'] = "";

			// ajout de l'adresse
			if( !isset($_REQUEST['usr_id'],$_REQUEST['title'],$_REQUEST['firstname'],$_REQUEST['lastname']) ) throw new Exception("Paramètres invalide" );
			if( !isset($_REQUEST['society'],$_REQUEST['siret'],$_REQUEST['address1'],$_REQUEST['address2'],$_REQUEST['address3']) ) throw new Exception("Paramètres invalide" );
			if( !isset($_REQUEST['zipcode'],$_REQUEST['city'],$_REQUEST['country'],$_REQUEST['phone'],$_REQUEST['fax']) ) throw new Exception("Paramètres invalide");


			if( !isset($_REQUEST['mobile']) ) $_REQUEST['mobile'] = '';
			if( !isset($_REQUEST['work']) ) $_REQUEST['work'] = '';

			if( !isset($_REQUEST['ref_gescom']) ) $_REQUEST['ref_gescom'] = '';

			$type = 1;
			if( trim($_REQUEST['society']) ){
				if( trim($_REQUEST['firstname']) || trim($_REQUEST['lastname']) ){
					$type = 3;
				}else{
					$type = 2;
				}
			}
			if( isset($_REQUEST['type']) && is_numeric($_REQUEST['type']) ){
				$type =	$_REQUEST['type'];
			}



			if( $_REQUEST['title']==0 ){
				$_REQUEST['title']=4;
			}

			$id = gu_adresses_add($_REQUEST['usr_id'],$type, $_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'], $_REQUEST['siret'], $_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'], $_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['mobile'], $_REQUEST['work'], '', '', null, $_REQUEST['address3'], $_REQUEST['ref_gescom'] );


			if( $id ){

				// ajoute la description de l'adresse si disponible
				if( isset($_REQUEST['desc']) ){
					gu_adresses_set_desc( $_REQUEST['usr_id'], $id, $_REQUEST['desc'] );
				}

				if (isset($_REQUEST['manual_location'], $_REQUEST['longitude'], $_REQUEST['latitude']) && $_REQUEST['manual_location'] == 1) {
					gu_adresses_set_coordinates($id, $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['date_location']);
				}

				$result = true;
				$content = array('id'=>$id);
			}

			break;
		/**
 		 * @{
		 * \page api-addresses-index-upd Mise à jour
		 *
		 * Cette fonction met à jour une adresse
		 *
		 *		\code
		 *			PUT /addresses/
		 *		\endcode
		 *
		 * @param int usr_id Obligatoire, identifiant de l'utilisateur auquel l'adresse est rattachée.
		 * @param int adr_id Obligatoire, identifiant de l'adresse à mettre à jour
		 * @param string title Obligatoire, Identifiant du titre (seulement si particulier)
		 * @param string firstname Obligatoire, Prénom de l'utilisateur (seulement si particulier)
		 * @param string lastname Obligatoire, Nom de l'utilisateur (seulement si particulier)
		 * @param string society Obligatoire, Nom de l'entreprise (seulement si professionnel)
		 * @param string siret Obligatoire, No SIRET (seulement si professionnel)
		 * @param string address1 Obligatoire, Première partie de l'adresse postale
		 * @param string address2 Obligatoire, Seconde partie de l'adresse postale
		 * @param string address3 Obligatoire, Troisième partie de l'adresse postale
		 * @param string zipcode Obligatoire, Code postal
		 * @param string city Obligatoire, Ville
		 * @param string country Obligatoire, Pays
		 * @param string phone Obligatoire, Numéro de téléphone
		 * @param string fax Obligatoire, Numéro de fax
		 * @param string mobile Facultatif, Numéro de téléphone portable
		 * @param string work Facultatif, Numéro de téléphone de travail
		 * @param string desc Facultatif, Description
		 * @param string ref_gescom Facultatif, Référence de l'adresse dans la gestion commerciale
		 * @param bool manual_location Facultatif, permet de rentrer une localisation manuelle (==1)
		 * @param float longitude Facultatif, longitude de la localisation manuelle
		 * @param float latitude Facultatif, latitude de la localisation manuelle
		 *
		 * @return true si la mise à jour est effectuée
		*/
		case 'upd':

			if( !isset($_REQUEST['address3']) ) $_REQUEST['address3'] = "";

			// historique a retirer si possible ( nde )..
			if( isset($_REQUEST['id']) ) $_REQUEST['usr_id'] = $_REQUEST['id'];

			// mise à jour de l'adresse
			if( !isset($_REQUEST['usr_id'],$_REQUEST['adr_id'],$_REQUEST['title'],$_REQUEST['firstname'],$_REQUEST['lastname']) ) throw new Exception("Paramètres invalide" );
			if( !isset($_REQUEST['society'],$_REQUEST['siret'],$_REQUEST['address1'],$_REQUEST['address2'],$_REQUEST['address3']) ) throw new Exception("Paramètres invalide" );
			if( !isset($_REQUEST['zipcode'],$_REQUEST['city'],$_REQUEST['country'],$_REQUEST['phone'],$_REQUEST['fax']) ) throw new Exception("Paramètres invalide");


			if( !isset($_REQUEST['mobile']) ) $_REQUEST['mobile'] = '';
			if( !isset($_REQUEST['work']) ) $_REQUEST['work'] = '';

			if( !isset($_REQUEST['ref_gescom']) ) $_REQUEST['ref_gescom'] = '';

			$type = 1;
			if( trim($_REQUEST['society']) ){
				if( trim($_REQUEST['firstname']) || trim($_REQUEST['lastname']) ){
					$type = 3;
				}else{
					$type = 2;
				}
			}

			if( $_REQUEST['title']==0 ){
				$_REQUEST['title']=4;
			}

			if (isset($_REQUEST['manual_location'], $_REQUEST['longitude'], $_REQUEST['latitude']) && $_REQUEST['manual_location'] == 1) {
				gu_adresses_set_coordinates($_REQUEST['adr_id'], $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['date_location']);
			}

			if( gu_adresses_update($_REQUEST['usr_id'],$_REQUEST['adr_id'],$type, $_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'], $_REQUEST['siret'], $_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'], $_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['mobile'], $_REQUEST['work'], $is_sync, null, null, null, $_REQUEST['address3'], $_REQUEST['ref_gescom']) ){

				// ajoute la description de l'adresse si disponible
				if( isset($_REQUEST['desc']) ){
					gu_adresses_set_desc( $_REQUEST['usr_id'], $_REQUEST['adr_id'], $_REQUEST['desc'] );
				}

				$result = true;
			}

			break;

		///@}

		/**
 		 * @{
		 * \page api-addresses-index-del Suppression
		 *
		 * Cette fonction permet la suppression d'une adresse
		 *
		 *		\code
		 *			DELETE /addresses
		 *		\endcode
		 *
		 * @param id Obligatoire, identifiant de l'adresse à supprimer
		 *
		 * @return true si la suppression est effectuée, sinon false.
		 * @}
		*/
		case 'del':

			if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
				throw new Exception("Paramètre invalide");
			}

			if( gu_adresses_exists($_REQUEST['id']) ){

				// controle que l'adresse n'st pas présente sur une commande, auquel cas, c'est juste un masquage qui va être éffectué
				if( gu_adresses_is_used($_REQUEST['id']) ){
					if( !gu_adresses_set_masked($_REQUEST['id']) ){
						throw new Exception("Erreur lors du maskage  de l'adresse.");
					}
				}else{
					if( !gu_adresses_del($_REQUEST['id']) ){
						throw new Exception("Erreur lors de la suppression de l'adresse.");
					}
				}
			}

			$result = true;
			break;
	}
///@}
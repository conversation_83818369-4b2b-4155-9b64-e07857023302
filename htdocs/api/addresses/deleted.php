<?php
/** 
 * \defgroup api-addresses-deleted Virtuellement supprimé 
 * \ingroup crm
 * @{		 
 * \page api-addresses-deleted-get Chargement
 *
 *	cette fonction permet de savoir si une adresse est supprimé ou non 
 *
 *	 \code
 *		GET /addresses/deleted/
 *	 \endcode
 *	
 * @param int $id obligatoire, identifiant de l'adresse
 *	
 * @return json sous la forme :
 *	\code{.json}
 *		{
 *      	"deleted": true si l'adresse est supprimé, False si il ne l'est pas 
 *		}
 * 	\endcode 	
*/
switch( $method ){
	case 'get': 
	
		// Paramètre identifiant
		if( !isset($_REQUEST['id']) ||  !is_numeric($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide");
		}

		$result = true;
		$content = array('deleted' => gu_adresses_is_virtual_deleted($_REQUEST['id']));

		break;
}

/// @}
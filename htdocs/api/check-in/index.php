<?php
/**
 *	\defgroup Check-In Check-In 
 *	\ingroup crm 
 *	@{	 
*/

switch( $method ){
	/** @{@}
	 * @{	 	
	 * \page api-check-in-index-get Chargement 
	 *
	 * Cette fonction permet de récuperer un check-in 
	 *
	 *	\code
	 *		GET	/check-in/
	 *	\endcode
	 *
	 * @param id obligatoire : identifiant du Check-in
	 *
	 * @return Json sous la forme suivante :
	 *	\code{.json}
	 *			{
     *  		"id": identifiant du check-in ,
     *  		"usr_id": identifiant de l'utilisateur,
     *  		"author_id": identifiant de l'auteur,
     *  		"date_start_en": Date de début au format EN : YYYY-MM-DD HH:MM:SS,
     *  		"date_end_en": Date de fin au format EN : YYYY-MM-DD HH:MM:SS,
     *   		"latitude": indique la latitude,
     *   		"longitude": indique la longitude,
     *   		"confirmed": booléen (1 : Validé 0 : non validé)
     *   		"date_created_en": Date de création au format EN : YYYY-MM-DD HH:MM:SS,
	 *			}	
	 *	\endcode
	 * @}
	 */
	case 'get':

		if( !isset($_REQUEST['id']) ){
			throw new Exception( "Paramètre invalide.");
		}

		$rcheckin = rp_checkin_get( $_REQUEST['id'] );
		if( $rcheckin && ria_mysql_num_rows($rcheckin) ){
			$result = true;
			$content = ria_mysql_fetch_assoc($rcheckin);
		}
		break;

	/** @{@}
	 * @{	 	
	 * \page api-check-in-index-add Ajout 
	 *
	 * Cette fonction ajoute un check-in.	
	 *
	 *	\code
	 *		POST /check-in/
	 *	\endcode
	 *	
	 * @param usr_id Obligatoire, identifiant du client
	 * @param author_id Obligatoire, identifiant de l'utilisateur qui est l'auteur du check-in
	 * @param date_start Obligatoire, date de début du check-in
	 * @param date_end Obligatoire, date de fin du check-in
	 * @param latitude Obligatoire, latitude du check-in
	 * @param longitude Obligatoire, longitude du check-in
	 * @param confirmed Obligatoire, détermine si le check-in a été validé ou non par l'auteur
	 * @param date_created Facultatif, date de création du check-in
	 *	
	 * @return id : Identifiant du check-in
	 *	
	 * @}
	*/
	case 'add':
		if(  !isset($_REQUEST['date_start']) || !isdateheure($_REQUEST['date_start'])
			|| !isset($_REQUEST['date_end']) || !isdateheure($_REQUEST['date_end'])
			|| !isset($_REQUEST['latitude']) || !is_numeric($_REQUEST['latitude'])
			|| !isset($_REQUEST['longitude']) || !is_numeric($_REQUEST['longitude'])
			|| !isset($_REQUEST['confirmed']) || !is_numeric($_REQUEST['confirmed'])
			|| !isset($_REQUEST['usr_id']) || !is_numeric($_REQUEST['usr_id'])
			|| !isset($_REQUEST['author_id']) || !is_numeric($_REQUEST['author_id'])  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['date_created']) || !$_REQUEST['date_created'] ){
			$_REQUEST['date_created'] = false;
		}

		// création du checkin
		$rck_id = rp_checkin_add( $_REQUEST['usr_id'], $_REQUEST['author_id'], $_REQUEST['date_start'], $_REQUEST['date_end'], $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['confirmed'], $_REQUEST['date_created'] );
		if( !$rck_id ){
			throw new Exception('Erreur lors de la création du check-in');
		}

		$error = false;

		// si une erreur à eu lieu on supprime le rapport
		if( $error ){
			rp_checkin_del($rck_id);
		}else{
			$result = true;
			$content = array('id' => $rck_id);
		}

		break;
	/** @{@}
	 * 	@{	 	
	 * \page api-check-in-index-upd Mise à jour
	 *
	 * Cette fonction met à jour un check-in.
	 *
	 *	\code
	 *		PUT /check-in/
	 *	\endcode
	 *
	 * @param usr_id Obligatoire, identifiant du client
	 * @param author_id Obligatoire, identifiant de l'utilisateur qui est l'auteur du check-in
	 * @param date_start Obligatoire, date de début du check-in
	 * @param date_end Obligatoire, date de fin du check-in
	 * @param latitude Obligatoire, latitude du check-in
	 * @param longitude Obligatoire, longitude du check-in
	 * @param confirmed Obligatoire, détermine si le check-in a été validé ou non par l'auteur
	 * @param date_created Facultatif, date de création du check-in
	 *	
	 * @return true si la mise a jour s'est déroulée sans problème. 
	 * @}
	*/
	case 'upd':
		if( !isset($_REQUEST['id'])
			||!isset($_REQUEST['date_start']) || !isdateheure($_REQUEST['date_start'])
			|| !isset($_REQUEST['date_end']) || !isdateheure($_REQUEST['date_end'])
			|| !isset($_REQUEST['latitude']) || !is_numeric($_REQUEST['latitude'])
			|| !isset($_REQUEST['longitude']) || !is_numeric($_REQUEST['longitude'])
			|| !isset($_REQUEST['confirmed']) || !is_numeric($_REQUEST['confirmed'])
			|| !isset($_REQUEST['usr_id']) || !is_numeric($_REQUEST['usr_id'])
			|| !isset($_REQUEST['author_id']) || !is_numeric($_REQUEST['author_id'])  ){
			throw new Exception('Paramètres invalide');
		}

		// création du checkin
		$rck_id = rp_checkin_upd( $_REQUEST['id'], $_REQUEST['usr_id'], $_REQUEST['author_id'], $_REQUEST['date_start'], $_REQUEST['date_end'], $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['confirmed'] );
		if( !$rck_id ){
			throw new Exception('Erreur lors de la mise à jour du check-in');
		}

		$result = true;
		break;
	/** @{@}
	 * 	@{	 	
	 *	\page api-check-in-del Suppression
	 *
	 *	Cette fonction permet de supprimer un check-in
	 *
	 *		\code
	 *			DELETE	/check-in/
	 *	 	\endcode
	 *
	 * @param id obligatoire : identifiant du check-in 
	 * 
	 * @return true si la suppression est effective
	 *	
	 *	@}
	*/
	case 'del':

		if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !rp_checkin_del($_REQUEST['id']) ){
			throw new Exception("Erreur lors de la suppression du checkin.");
		}

		$result = true;

		break;
}

/// @}
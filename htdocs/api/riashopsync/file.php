<?php

// \cond onlyria

/**
 *\defgroup api-riashopsync-file Permet de recupérer le zip du manager ou de l'updater en fonction des paramètres donné
 *\ingroup sync
 *@{
 *	 \page api-riashopsync-file-get
 *
 *	 cette fonction
 *
 *		\code
 *			GET /riashopsync/file/
 *		\endcode
 *
 *	 @param $module Obligatoire, soit manager ou updater pour obtenir le fichier correspondant
 *
 *	 @return mixed le fichier en brut ( readfile ) ou page vide.
*/

switch( $method ){
    case 'get':

	    if( !isset($_REQUEST['module']) || !in_array($_REQUEST['module'], array('manager', 'updater')) ){
	    	throw new Exception("Le module est obligatoire ou invalide");
	    }

		$branch_code = $config["sync_updater_branch"].'-standard';

		$folder_path = _RIASHOPSYNC_DEPLOY.$branch_code.'/';
		$path_to_zip = "";

		if( is_dir($folder_path) ){

			$files = array_diff(scandir($folder_path), array('.', '..'));

			foreach( $files as $f ){
				$regex_manager = "/^RiaShopSync_".$branch_code."_(.*)_\.zip/";
				$regex_updater = "/^RiaShopSyncUpdater_".$branch_code."_(.*)_\.zip/";
				if( preg_match($regex_manager, $f) && $_REQUEST['module'] == 'manager' ){
					$path_to_zip = $folder_path.$f;
				}
				else if( preg_match($regex_updater, $f) && $_REQUEST['module'] == 'updater' ){
					$path_to_zip = $folder_path.$f;
				}
			}
		}

		if( $path_to_zip ){
			header("HTTP/1.0 200");
			header('Cache-Control: private, store');
			header('Pragma: cache');
			header('Content-Type: application/x-force-download');
			header('Content-Disposition: attachment; filename='.$_REQUEST['module'].'.zip"');
			ob_clean();
			flush();
			readfile($path_to_zip);
		}
		exit();
		break;

}

///@}

// \endcond
<?php
require_once('PubSub.inc.php');


// \cond onlyria

/** 
 *\defgroup api-riashopsync-status Permet de connaitre le status de la synchro comme les version des softs
 *\ingroup sync
 *@{	
 *	 \page api-riashopsync-status-get 
 *
 *	 cette fonction 
 *
 *		\code
 *			GET /riashopsync/status/
 *		\endcode
 *
 *	 @return Un tableau avec les données suivantes : 
 * 			{
 *				branche : nom de la branche sur laquel est le tenant
 *				manager_version : dernier numéro de version pour le manager disponible sur cette branche
 *				updater_version : dernier numéro de version pour l'updater disponible sur cette branche
 * 			}
 *
 */
$actions_availables = array("start", "reboot", "stop");


switch( $method ){
    case 'upd':

    	if( !isset($_REQUEST['action']) || !in_array($_REQUEST['action'], $actions_availables)){
    		throw new Exception("Paramètre action invalide");
    	}

		PubSub::create("riashopsync_tnt_".$config['tnt_id'])->publish('action', 'Action api', array('value' => $_REQUEST['action']));

    	$result = true;
		break;
}

///@}

// \endcond
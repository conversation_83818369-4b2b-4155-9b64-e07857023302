<?php
// Ce fichier ne contient que des fonctions utilitaires pour l'api
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

require_once('PubSub.inc.php');
require_once('users.inc.php');

/**
 *	Fonction de log pour toutes l'api
 *	Elle permet de save en local (dans un fichier sur le disque) certaine information car dans le cas de paramètre post on voit rien dans les accès log
 *	Pour le moment c'est surtout utiliser pour les orders/complete
 *	
 *	@param message : Message à mettre dans le log (json idéalement pour que la data soit structuré)
 *	@param type : c'est le nom du fichier de log ou doit être push le log
 *	@return rien
 */
function api_log($message, $type){
	global $config;
	error_log(date('Y-m-d H:i:s').':'.$message."\n\r", 3, "/var/log/php/".$type.".log");
}

/**
 *	Cette fonction est utlilisé pour faire l'enregistrement des champs avancé. 
 *	Permet d'éviter de la duplication de code car le modèle d'envois de champs est identique pour différente classe ( commandes, compte client, ... )	
 *
 *	@param cls_id : identifiant de la classe
 *	@param obj0: identifiant de l'objet
 *	@param obj1: identifiant de l'objet
 *	@param obj2: identifiant de l'objet
 *	@param fields: tableau des données sous la forme suivante : 
 *			array (
 *				array(
 *					fld_id : identifiant du champs avancé
 *					value: valeur de l'objet pour le champs avancé
 *				)
 *			)
 *	@param delete_missing: si true alors on va supprimer tous les champs de l'objet pour écraser par ceux qui sont donnée, 
 *			si false on ce contentera de mettre à jour les champs uniquement donnée dans le paramètre fields
 *	@return rien
 */
function fields_sync($cls_id, $obj0, $obj1, $obj2, $fields, $delete_missing=true){
	global $config, $is_sync;

	$fld_ignore = array(_FLD_PRD_ORD_FREE, _FLD_PRD_ORD_PRICE_BRUT, _FLD_ORD_LINE_DISCOUNT, _FLD_ORD_LINE_WEIGHT, _FLD_PRD_COL_ORD_PRODUCT);
	foreach( $fields as $f ){
		if( !isset($f['lng']) ){
			$f['lng'] = 'FR';
		} 

		$old_value = fld_object_values_get(array($obj0, $obj1, $obj2), $f['fld_id'], $f['lng'], false, true);

		$fld_ignore[] = $f['fld_id'];

		$type = fld_fields_get_type( $f['fld_id'] );
		switch ($type) {
			case FLD_TYPE_BOOLEAN_YES_NO:
				if( in_array(strtolower($f['value']), array('1', 'yes', 'oui')) ){
					$f['value'] = 'Oui';
				}else if ( in_array(strtolower($f['value']), array('0', 'no', 'non')) ){
					$f['value'] = 'Non';
				}
				break;
		}

		fld_object_values_set( array($obj0, $obj1, $obj2), $f['fld_id'], $f['value'], $f['lng'], false );


		fields_push_to_riashopsync($cls_id, $obj0, $obj1, $obj2, $f['fld_id'], $f['value'], $old_value);

		// conserve l'url pour la génération du devis pdf.
		if( isset($config['devis_pdf_url']) && $config['devis_pdf_url']!='' && $f['fld_id']==_FLD_ORD_NOTIFY_KEY ){
			$config['devis_pdf_need_refresh'] = $config['site_url'].$config['devis_pdf_url'].'?k='.$f['value'];
		}
	}

	if($delete_missing) {
		// Supprime les valeurs de champ avancé absent.
		$rfld = fld_fields_get(0, 0, 0, 0, 0, array($obj0, $obj1, $obj2), null, array(), false, array(), null, $cls_id, null, false, null, null, false, null, true );
		if( $rfld && ria_mysql_num_rows($rfld) ){
			while( $fld = ria_mysql_fetch_assoc($rfld) ){
				if( !in_array($fld['id'], $fld_ignore) ){
					fld_object_values_set( array($obj0, $obj1, $obj2), $fld['id'], '', false, false );
				}
			}
		}
	}
}

/**
 *	Cette fonction permet de push la mise à jour du champs sur pubsup pour que riashopsync soit au courant
 *
 *	@param cls_id : identifiant de la classe
 *	@param obj0: identifiant de l'objet
 *	@param obj1: identifiant de l'objet
 *	@param obj2: identifiant de l'objet
 *	@param fld_id: identifiant du champs
 *	@param value: nouvelle valeur
 *	@param old_value: ancienne valeur du champs ( utilisé par riashopsync pour faire des controles )
 *	@return rien
 */
function fields_push_to_riashopsync($cls_id, $obj0, $obj1, $obj2, $fld_id, $value, $old_value=-1){
	global $config, $is_sync;

	// quand c'est pas la synchro 
	if( !$is_sync && fld_fields_is_push_riashopsync($fld_id) ){
		// demande une maj dans pubsub uniquement dans le cas ou la valeur est différente en bdd 
		if( $old_value !== $value ){
			PubSub::create("riashopsync_tnt_".$config['tnt_id'])->publish('fields', array(
				'cls_id' => $cls_id,
				'obj_0' => $obj0,
				'obj_1' => $obj1,
				'obj_2' => $obj2,
				'fld_id' => $fld_id,
				'value' => $value,
				'old_value' => $old_value,
			), array(
				'cls_id' => (string) $cls_id
			));
		}
	}
}

/**
 *	Cette fonction permet la mise à jour des modèles de saisies affecté à un objet
 *
 *	@param cls_id : identifiant de la classe
 *	@param obj0: identifiant de l'objet
 *	@param obj1: identifiant de l'objet
 *	@param obj2: identifiant de l'objet
 *	@param models: tableau des données sous la forme suivante : 
 *			array (
 *				array(
 *					mdl_id : identifiant du modèle
 *				)
 *			)
 *	@param delete_missing: si true alors on va supprimer tous les champs de l'objet pour écraser par ceux qui sont donnée, 
 *			si false on ce contentera de mettre à jour les champs uniquement donnée dans le paramètre fields
 *	@return rien
 */
function models_sync($cls_id, $obj0, $obj1, $obj2, $models, $delete_missing=true){
	global $config, $is_sync;

	$mdl_ignore = array();
	foreach( $models as $m ){
		$mdl_ignore[] = $m['mdl_id'];
		fld_object_models_add( array($obj0, $obj1, $obj2), $m['mdl_id']);
	}

	if($delete_missing) {
		// Supprime les modeles de saisie absent.
		$rmdl = fld_models_get(0, array($obj0, $obj1, $obj2), $cls_id, true, 0 );
		if( $rmdl && ria_mysql_num_rows($rmdl) ){
			while( $mdl = ria_mysql_fetch_assoc($rmdl) ){
				if( !in_array($mdl['id'], $mdl_ignore) ){
					fld_object_models_del( array($obj0, $obj1, $obj2), $mdl['id'], $cls_id);
				}
			}
		}
	}
}


/**
 *	Cette fonction permet la sauvegarde des signatures pour les commandes / bl / pl / factures
 *
 *	@param cls_id : identifiant de la classe
 *	@param obj0: identifiant de l'objet
 *	@param obj1: identifiant de l'objet
 *	@param obj2: identifiant de l'objet
 *	@param signatures: contenu text de la signature
 *	@return rien
 */
function signatures_sync($cls_id, $obj0, $obj1, $obj2, $signatures){

	foreach ($signatures as $sign) {


		$usr_id = null;
		if( isset($sign['usr_id']) && is_numeric($sign['usr_id']) ){
			$usr_id = $sign['usr_id'];
		}

		$firstname = null;
		if( isset($sign['firstname']) && $sign['firstname'] != "" ){
			$firstname = $sign['firstname'];
		}

		$lastname = null;
		if( isset($sign['lastname']) && $sign['lastname'] != "" ){
			$lastname = $sign['lastname'];
		}

		$function = null;
		if( isset($sign['function']) && $sign['function'] != "" ){
			$function = $sign['function'];
		}

		obj_signature_add( $cls_id, $obj0, $obj1, $obj2, $sign['signature'], $usr_id, $firstname, $lastname, $function );
	}
}

/** 
 * 	Permet de sauvegarder les serials.
 *
 *	@param int $cls_id Obligatoire : Clase de l'objet pour le quel on va synchroniser les serials
 *	@param $obj0 Obligatoire : Id 0 de l'objet
 *	@param $obj1 Facultatif : Id 1 de l'objet
 *	@param $obj2 Facultatif : Id 2 de l'objet
 *	@param $serials Obligatoire : Tableau avec les serials pour sauvegarder
 *			- cls_id : identifiant de la classe
 *			- obj_id_0 : identifiant 1
 *			- obj_id_1 : identifiant 2
 *			- obj_id_2 : identifiant 3
 *			- date_dlc : date limite de consommation
 *			- date_production : date de production
 *			- dps_id : numéro de dépot
 *			- ref_gescom : référence du lot dans la gescom
 *			- serial : numéro de serie
 *
 *	@return rien
 */
function serials_sync($cls_id, $obj0, $obj1=false, $obj2=false, $serials){

	$traited = array();

	for($i=0; $i < sizeof($serials); $i++){
		if( !isset($serials[$i]['id']) || !isset($serials[$i]['cls_id']) || !isset($serials[$i]['obj_id_0']) ||
			!isset($serials[$i]['obj_id_1']) || !isset($serials[$i]['obj_id_2']) || !isset($serials[$i]['serial']) ){
				throw new Exception("Les données sur le serial ne sont pas complète.".print_r($serials[$i], true));
		}
	}

	//Here we update the ids of every serial with the parent's ids to use the definitive id of the parent object
	foreach( $serials as $key => $serial ){
		$serials[$key]['obj_id_0'] = $obj0;
		if ($obj1 !== false) {
			$serials[$key]['obj_id_1'] = $obj1;
		}
		if ($obj2 !== false) {
			$serials[$key]['obj_id_2'] = $obj2;
		}
	}

	// vide l'échéancier de la commande pour tous reconstruire
	$traited_key = array();
	$objIds = array($obj0);
	if ($obj1 !== false && $obj1 > 0) {
		$objIds[] = $obj1;
	}
	if ($obj2 !== false && $obj2 >= 0) { //LineId can be 0
		$objIds[] = $obj2;
	}

	$rdel = obj_serials_get_all( $cls_id, $objIds, true );
	if( $rdel && ria_mysql_num_rows($rdel) ){
		while( $del = ria_mysql_fetch_assoc($rdel) ){

			$exists = false;
			foreach( $serials as $serial ){

				if( $serial['cls_id']   == $del['cls_id']   &&
					$serial['obj_id_0'] == $del['obj_id_0'] &&
					$serial['obj_id_1'] == $del['obj_id_1'] &&
					$serial['obj_id_2'] == $del['obj_id_2'] &&
					$serial['serial']   == $del['serial'] ){
				// if( $serial['id'] == $del['id']){
					$traited_key[] = $serial['id'];

					$dlc = isset($serial['date_dlc']) && isdateheure($serial['date_dlc']) ? $serial['date_dlc'] : null;
					$production = isset($serial['date_production']) && isdateheure($serial['date_production']) ? $serial['date_production'] : null;
					$dps_id = isset($serial['dps_id']) && is_numeric($serial['dps_id']) ? $serial['dps_id'] : null;
					$qte = isset($serial['qte']) && is_numeric($serial['qte']) ? $serial['qte'] : null;
					$ref_gescom = isset($serial['ref_gescom']) && $serial['ref_gescom'] ? $serial['ref_gescom'] : null;

					if( !obj_serials_upd( $serial['cls_id'], $serial['obj_id_0'], $serial['obj_id_1'], $serial['obj_id_2'], $dlc, $production, $serial['serial'], $dps_id, $qte, $ref_gescom) ){

						throw new Exception("Erreur lors de la mise à jour du serial.".print_r($serial, true));
					}

					$exists = true;
				}
			}

			if( !$exists ){
				obj_serials_del($del['cls_id'], $del['obj_id_0'], $del['obj_id_1'], $del['obj_id_2'], $del['serial']);
			}
		}
	}

	if( sizeof($serials) > 0 ){
		foreach( $serials as $serial ){
			if( in_array($serial['id'], $traited_key) ) continue;

			$dlc = isset($serial['date_dlc']) && isdateheure($serial['date_dlc']) ? $serial['date_dlc'] : null;
			$production = isset($serial['date_production']) && isdateheure($serial['date_production']) ? $serial['date_production'] : null;
			$dps_id = isset($serial['dps_id']) && is_numeric($serial['dps_id']) ? $serial['dps_id'] : null;
			$qte = isset($serial['qte']) && is_numeric($serial['qte']) ? $serial['qte'] : null;
			$ref_gescom = isset($serial['ref_gescom']) && $serial['ref_gescom'] ? $serial['ref_gescom'] : null;

			if( !obj_serials_add( $serial['cls_id'], $serial['obj_id_0'], $serial['obj_id_1'], $serial['obj_id_2'], $dlc, $production, $serial['serial'], $dps_id, $qte, $ref_gescom) ){

				throw new Exception("Erreur lors de la création du serial.".print_r($serial, true));
			}
		}
	}
}

/** 
 * 	Permet de mettre a jour les quantités des serials base des produits avec les produits de la piece passé comme argument.
 *	@param cls_id Obligatoire : Clase de l'objet pour le quel on va mettre a jour les serials
 *	@param piece_id Obligatoire : Id de la piece
 *	@param is_substraction Facultatif : True pour substraer les quantites sinon on les ajoute. False par defaut
 */
function serials_quantities_update($cls_id, $piece_id, $is_substraction=false){
	global $config;
	if( isset($config['fdv_ord_serials_active']) && $config['fdv_ord_serials_active'] ){
		if ($cls_id == CLS_ORDER) {
			$products = ord_products_get($piece_id);
			$serial_cls = CLS_ORD_PRODUCT;
		} else if ($cls_id == CLS_BL) {
			$products = ord_bl_products_get($piece_id);
			$serial_cls = CLS_BL_PRODUCT;
		} else if ($cls_id == CLS_RETURN) {
			$products = ord_returns_products_get(0, $piece_id);
			$serial_cls = CLS_RETURN_PRODUCTS;
		} else {
			return;
		}
		if( $products && ria_mysql_num_rows($products)){
			while( $prd = ria_mysql_fetch_assoc($products) ){
				//Ignore spacings
				if ($prd['id'] == 0) {
					continue;
				}


				if ( isset($config['dlv_prd_references']) && is_array($config['dlv_prd_references']) && in_array($prd['ref'], $config['dlv_prd_references']) ) {
					continue;
				}

				$serial = obj_serials_get($serial_cls, $piece_id, $prd['id'], (($cls_id == CLS_RETURN) ? $prd['line_id'] : $prd['line']));
				if( $serial && ria_mysql_num_rows($serial) ){
					$serial = ria_mysql_fetch_assoc($serial);
					$baseSerial = obj_serials_get(CLS_PRODUCT, $serial['obj_id_1'], 0, 0, $serial['serial'], $serial['dps_id']);
					if ($baseSerial && ria_mysql_num_rows($baseSerial)) {
						$baseSerial = ria_mysql_fetch_assoc($baseSerial);

						$base_osl_qte = false;
						if (isset($baseSerial['qte'])) {
							$base_osl_qte = $baseSerial['qte'];
						}

						if ($base_osl_qte === false) {
							continue;
						}

						$prd_qte = $prd['qte'];
						if (isset($prd['col_id']) && $prd['col_id'] > 0) {
							$prd_qte *= $prd['col_qte'];
						}
						$new_qte = max($is_substraction ? ($base_osl_qte - $prd_qte) : ($base_osl_qte + $prd_qte), 0);
						obj_serials_upd(CLS_PRODUCT, $baseSerial['obj_id_0'], $baseSerial['obj_id_1'], $baseSerial['obj_id_2'], null, null, $baseSerial['serial'], null, $new_qte,null);
					}
				}
			}
		}
	}
}

/** 
 *	Cette fonction envoie une notification sur les tablettes du parcs intéressées par l'évènement qui est arrivé.
 *	@param type_id Obligatoire : type de notification tel que définis dans notification.inc.php
 *	@param odd_usr Facultatif et Obligatoire : Obligatoire uniquement dans le cas où le type de notif est un update NT_TYPE_USER_UPD
 *	@param new_usr Obligatoire : l'utilisateur apres l'action effectuée.
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function push_notify_user_device_destinataires( $type_id, $old_usr, $new_usr ){
	global $config;

	$author_id = 0;
	$author_name = "";

	// permet de bloquer les notifs qu'au tablette
	if( isset($config['usr_id']) && is_numeric($config['usr_id']) && $config['usr_id'] ){

		$rcurrent_usr = gu_users_get($config['usr_id']);
		if( !$rcurrent_usr ){
			return false;
		}
		$device_user = ria_mysql_fetch_assoc($rcurrent_usr);

		$author_id = $config['usr_id'];
		$author_name = $device_user['adr_firstname'].' '.$device_user['adr_lastname'];
	}

	switch($type_id){
		case NT_TYPE_USER_ADD : {

			$is_contact = $new_usr['parent_id'] != 0;

			$usr_name = trim($new_usr['society'] != '' ? $new_usr['society'] : $new_usr['adr_firstname'].' '.$new_usr['adr_lastname']);

			if( trim($author_name) ){
				$title = $author_name.' a ajouté '.($is_contact ? 'le contact':'le compte')." ".$usr_name;
			}else{
				$title = ($is_contact ? 'Le contact':'Le compte')." ".$usr_name." a été ajouté" ;
			}

			$desc = '';
			if($new_usr['society'] != ''){
				$desc.='Société '.$new_usr['society']."\r\n";
			}
			if($new_usr['siret'] != ''){
				$desc.='Siret: '.$new_usr['siret']."\r\n";
			}
			if($new_usr['title_name'] != ''){
				$desc.=$new_usr['title_name'].' ';
			}
			if($new_usr['adr_firstname'] != ''){
				$desc.=$new_usr['adr_firstname'].' ';
			}
			if($new_usr['adr_lastname'] != ''){
				$desc.=$new_usr['adr_lastname']."\r\n";
			}
			if($new_usr['address1'] != ''){
				$desc.=$new_usr['address1']."\r\n";
			}
			if($new_usr['zipcode'] != ''){
				$desc.=$new_usr['zipcode'].' ';
			}
			if($new_usr['city'] != ''){
				$desc.=$new_usr['city'].' ';
			}
			if($new_usr['country'] != ''){
				$desc.=$new_usr['country']."\r\n";
			}
			if($new_usr['phone'] != ''){
				$desc.='Tél: '.$new_usr['phone']."\r\n";
			}
			if($new_usr['mobile'] != ''){
				$desc.='Mobile: '.$new_usr['mobile']."\r\n";
			}

			$usr_id=$new_usr['id'];
			break;
		}

		case NT_TYPE_USER_UPD : {
			$is_contact = $new_usr['parent_id'] != 0;

			$usr_name = trim($old_usr['society'] != '' ? $old_usr['society'] : $old_usr['adr_firstname'].' '.$old_usr['adr_lastname']);
			if( trim($author_name) ){
				$title = $author_name.' a modifié '.($is_contact ? 'le contact':'le compte')." ".$usr_name;
			}else{
				$title = ($is_contact ? 'Le contact':'Le compte')." ".$usr_name." a été modifié" ;
			}

			$desc = '';
			if($new_usr['society'] != '' && $old_usr['society'] != $new_usr['society']){
				if( trim($old_usr['society']) == '' ){
					$desc .= 'La société a été modifié par '.$new_usr['society']."\r\n";
				}else{
					$desc .= 'La société '.$old_usr['society'].' a été modifié par '.$new_usr['society']."\r\n";
				}
			}
			if($new_usr['siret'] != '' && $old_usr['siret'] != $new_usr['siret']){
				if( trim($old_usr['siret']) == '' ){
					$desc .= 'Le siret a été modifié par '.$new_usr['siret']."\r\n";
				}else{
					$desc .= 'Le siret '.$old_usr['society'].' a été modifié par '.$new_usr['siret']."\r\n";
				}
			}

			$old_name = "";
			if( ($new_usr['adr_firstname'] != '' && $old_usr['adr_firstname'] != $new_usr['adr_firstname']) ||
			 ($new_usr['adr_lastname'] != '' && $old_usr['adr_lastname'] != $new_usr['adr_lastname']) ){

				$old_name = $old_usr['adr_firstname']." ".$old_usr['adr_lastname'];
				$new_name = $new_usr['adr_firstname']." ".$new_usr['adr_lastname'];
				if( trim($old_name) == '' ){
					$desc .= 'Le nom a été modifié par '.$new_name."\r\n";
				}else{
					$desc .= 'Le nom '.$old_name.' a été modifié par '.$new_name."\r\n";
				}
			}
			if(($new_usr['address1'] != '' && $old_usr['address1'] != $new_usr['address1'])
			||($new_usr['zipcode'] != '' && $old_usr['zipcode'] != $new_usr['zipcode'])
			||($new_usr['city'] != '' && $old_usr['city'] != $new_usr['city'])){

				$olddesc = "";
				if($old_usr['address1'] != ''){
					$olddesc.=$old_usr['address1'].' ';
				}
				if($old_usr['zipcode'] != ''){
					$olddesc.=$old_usr['zipcode'].' ';
				}
				if($old_usr['city'] != ''){
					$olddesc.=$old_usr['city'].' ';
				}
				if($old_usr['country'] != ''){
					$olddesc.=$old_usr['country'];
				}

				$new_desc = "";
				if($new_usr['address1'] != ''){
					$new_desc.=$new_usr['address1'].' ';
				}
				if($new_usr['zipcode'] != ''){
					$new_desc.=$new_usr['zipcode'].' ';
				}
				if($new_usr['city'] != ''){
					$new_desc.=$new_usr['city'].' ';
				}
				if($new_usr['country'] != ''){
					$new_desc.=$new_usr['country'];
				}

				if( trim($old_name) == '' ){
					$desc .= 'L\'adresse a été modifié par:'."\r\n".$new_desc."\r\n";
				}else{
					$desc .= 'L\'adresse: '.$olddesc."\r\n a été modifié par:\r\n ".$new_desc."\r\n";
				}

			}

			if($new_usr['phone'] != '' && $old_usr['phone'] != $new_usr['phone']){

				try{ // permet de convertire les tels formatté, si c'est juste le format qui change on prend pas en compte la modif
					$phoneUtil = PhoneNumberUtil::getInstance();
					$old_phoneNumber = $phoneUtil->parse($old_usr['phone'], 'FR');
					$new_phoneNumber = $phoneUtil->parse($new_usr['phone'], 'FR');

					if ($phoneUtil->isValidNumber($old_phoneNumber)) {
						$old_usr['phone'] = $phoneUtil->format($old_phoneNumber, PhoneNumberFormat::E164);
					}
					if ($phoneUtil->isValidNumber($new_phoneNumber)) {
						$new_usr['phone'] = $phoneUtil->format($new_phoneNumber, PhoneNumberFormat::E164);
					}
				}catch(Exception $e){}

				if( $old_usr['phone'] != $new_usr['phone'] ){
					if( trim($old_usr['phone']) == '' ){
						$desc .= 'Le téléphone a été modifié par '.$new_usr['phone']."\r\n";
					}else{
						$desc .= 'Le téléphone '.$old_usr['phone'].' a été modifié par '.$new_usr['phone']."\r\n";
					}
				}
			}
			if($new_usr['mobile'] != '' && $old_usr['mobile'] != $new_usr['mobile']){

				try{ // permet de convertire les tels formatté, si c'est juste le format qui change on prend pas en compte la modif
					$phoneUtil = PhoneNumberUtil::getInstance();
					$old_phoneNumber = $phoneUtil->parse($old_usr['mobile'], 'FR');
					$new_phoneNumber = $phoneUtil->parse($new_usr['mobile'], 'FR');

					if ($phoneUtil->isValidNumber($old_phoneNumber)) {
						$old_usr['mobile'] = $phoneUtil->format($old_phoneNumber, PhoneNumberFormat::E164);
					}
					if ($phoneUtil->isValidNumber($new_phoneNumber)) {
						$new_usr['mobile'] = $phoneUtil->format($new_phoneNumber, PhoneNumberFormat::E164);
					}
				}catch(Exception $e){}

				if( $old_usr['mobile'] != $new_usr['mobile'] ){
					if( trim($old_usr['mobile']) == '' ){
						$desc .= 'Le mobile a été modifié par '.$new_usr['mobile']."\r\n";
					}else{
						$desc .= 'Le mobile '.$old_usr['mobile'].' a été modifié par '.$new_usr['mobile']."\r\n";
					}
				}
			}
			$usr_id=$new_usr['id'];

			break;
		}

		case NT_TYPE_USER_DELETE : {
			$is_contact = $old_usr['parent_id'] != 0;

			$usr_name = trim($old_usr['society'] != '' ? $old_usr['society'] : $old_usr['adr_firstname'].' '.$old_usr['adr_lastname']);
			if( trim($author_name) ){
				$title = $author_name.' a supprimé '.($is_contact ? 'le contact':'le compte')." ".$usr_name;
			}else{
				$title = ($is_contact ? 'Le contact':'Le compte')." ".$usr_name." a été supprimé";
			}

			$desc = '';
			if($old_usr['society'] != ''){
				$desc.='Société '.$old_usr['society']."\r\n";
			}
			if($old_usr['siret'] != ''){
				$desc.='Siret: '.$old_usr['siret']."\r\n";
			}
			if($old_usr['title_name'] != ''){
				$desc.=$old_usr['title_name'].' ';
			}
			if($old_usr['adr_firstname'] != ''){
				$desc.=$old_usr['adr_firstname'].' ';
			}
			if($old_usr['adr_lastname'] != ''){
				$desc.=$old_usr['adr_lastname']."\r\n";
			}
			if($old_usr['address1'] != ''){
				$desc.=$old_usr['address1']."\r\n";
			}
			if($old_usr['zipcode'] != ''){
				$desc.=$old_usr['zipcode'].' ';
			}
			if($old_usr['city'] != ''){
				$desc.=$old_usr['city'].' ';
			}
			if($old_usr['country'] != ''){
				$desc.=$old_usr['country']."\r\n";
			}
			if($old_usr['phone'] != ''){
				$desc.='Tél: '.$old_usr['phone']."\r\n";
			}
			if($old_usr['mobile'] != ''){
				$desc.='Mobile: '.$old_usr['mobile']."\r\n";
			}

			$usr_id=$old_usr['id'];
			break;
		}
	}

	if (!trim($desc)){
		//Il n'y a en fait pas eu de modifs, donc on envoie pas de notifs.
		//Cas où l'utilisateur a cliqué sur 'Enregistré' alors qu'il n'avait rien modifié.
		return true;
	}

	// ajout de la notif
	$nt_id = nt_notifications_add( $type_id, $author_id, $title, $desc );

	if( !$nt_id ){
		//Erreur lors de la création de la notification
		return false;
	}

	if( !nt_notification_objects_add ( $nt_id, CLS_USER, $usr_id, 0, 0) ){
		return false;
	}

	// Envoi une notification mail
	$notif = nt_notifications_get( $nt_id );
	if( is_array($notif) && count($notif) ){
		flow_notifications_send( CLS_NOTIFICATIONS, dev_devices_get_object_simplified(CLS_NOTIFICATIONS, array($notif['_id']), $notif) );
	}

	// ajout des destinataires à la notifications

	//Recupère la liste des devices actif du parc. Parmi tous ces devices on regarde, qui sont les représentants,
	//qui sont leur clients et si ils 'ont' le client modifié on envoie une notif
	$destinataires = array();
	$r_activated_devices = dev_devices_get(); //dev_devices_get(0, $config['usr_id']); renvoyait toujours la tablette utilisateur.
	while($r_activated_device = ria_mysql_fetch_assoc($r_activated_devices)){
		$rusr = gu_users_get($r_activated_device['usr_id'] );
		if( $rusr ){
			$usr = ria_mysql_fetch_assoc($rusr);

			switch($usr['prf_id']) {
				case PRF_ADMIN:
					if($r_activated_device['usr_id'] != $config['usr_id']){
						$destinataires[] = $r_activated_device['usr_id'];
					}
					break;
				case PRF_SELLER:
					$r_clients = gu_users_seller_customers_get($r_activated_device['usr_id']);
					foreach($r_clients as $r_client){
						if($r_client == $usr_id  && $r_activated_device['usr_id'] != $config['usr_id'] ){
							$destinataires[] = $r_activated_device['usr_id'];
						}
					}
					break;
			}
		}
	}
	$destinataires = array_unique($destinataires);

	//Il n'y a pas de destinataire pas de notifications à envoyer
	if( !sizeof($destinataires) ){
		return false;
	}

	if( !nt_notifications_set_users($nt_id, $destinataires) ){
		return false;
	}

	//envoie la notifs aux devices concernés
	RiaQueue::getInstance()->addJob(RiaQueue::WORKER_DEVICE_NOTIFICATION_SEND, array('id'=>$nt_id));

	return true;
}

/** 
 *	Cette fonction permet d'activer un compte client suite à une création seulement
 *	@param usr_id : identifiant du compte a restaurer
 */
function usr_restore_account($usr_id){
	if(!gu_users_exists($usr_id)){
		if( gu_users_set_deleted($usr_id, false) ){
			$rusr = gu_users_get($usr_id);
			if( $rusr && ria_mysql_num_rows($rusr)){
				$new_usr = ria_mysql_fetch_assoc($rusr);
				push_notify_user_device_destinataires(NT_TYPE_USER_ADD, null, $new_usr);
				gu_users_notify_shop_owner($usr_id);
				return true;
			}
		}
	}
	return false;
}

<?php

// \cond onlyria

/**
 * \defgroup api-sync-salesforce_linears_send Export des relevés linéaires dans SalesForce
 * \ingroup sync
 * @{
 * \page api-sync-salesforce_linears_send-add Ajout
 *
 * cette fonction permet l'ajout d'un relevé linéaire dans SalesForce
 *
 *		\code
 *			PUT /sync/salesforce_linears_send/
 *		\endcode
 *
 * @param int $id Obligatoire, Identifiant du relevé linéaire à envoyer
 *
 * @return true si l'ajout s'est déroulé avec succès
 * @}
*/

use Riashop\Salesforce\Tasks\LinearRaised\LinearRaised;

require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');


switch( $method ){
	case 'add':

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque le paramètre identifiant du relevé linéaire.");
		}

		try {
			sf_login('write');
			$LinearRaised = new LinearRaised($config['sf_connexion']);
			$LinearRaised->add($_REQUEST['id']);
			sf_logout();

			$result = true;
		} catch (Exception $e) {
			mail('<EMAIL>', 'err SF '.$config['tnt_id'].' : '.$_REQUEST['id'], $e->getMessage());
			throw new Exception("Erreur de création du relevé ".$_REQUEST['id']." : ".$e->getMessage());
		}

		break;
}

// \endcond
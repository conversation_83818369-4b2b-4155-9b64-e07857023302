<?php

use Riashop\Salesforce\Exceptions\RetryOverTaskException;

// \cond onlyria

require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

/**
 * \defgroup api-sync-salesforce_linears_send Export des relevés linéaires dans SalesForce
 * \ingroup sync
 * @{
 * \page api-sync-salesforce_save_row-add Ajout
 *
 * Cette fonction permet l'ajout d'une commande dans SalesForce
 *
 *		\code
 *			PUT /sync/salesforce_save_row/
 *		\endcode
 *
 * @param int $tsk_id Obligatoire, Identifiant de la tache SF a éxécuter
 * @param $record Obligatoire, Tableau de donnée en provenance de SF
 *
 * @return true si l'ajout s'est déroulé avec succès
 * @}
*/

cfg_images_load($config);

switch( $method ){
	case 'add':

		if( !isset($_REQUEST['tsk_id'],$_REQUEST['record']) || !is_array($_REQUEST['record'])){
			throw new Exception("Paramètre invalides.");
		}

		try {
			sf_save_row($_REQUEST['tsk_id'], $_REQUEST['record']);

			$result = true;
		} catch (RetryOverTaskException $e) {
			throw new Exception("RetryOverTaskException: Erreur de sf_save_row ".$_REQUEST['tsk_id']." : ".$e->getMessage());
		} catch (Exception $e) {
			mail('<EMAIL>', 'err SF', $e->getMessage());
			throw new Exception("Erreur de sf_save_row ".$_REQUEST['tsk_id']." : ".$e->getMessage());
		}

		break;
}

// \endcond
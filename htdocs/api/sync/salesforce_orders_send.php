<?php

// \cond only ria
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

/**
 * \defgroup api-sync-salesforce_orders_send Export des commandes dans SalesForce
 * \ingroup sync
 * @{
 * \page api-sync-salesforce_orders_send-add  Ajout
 *
 * cette fonction permet d'ajouter une commande dans SalesForce
 *
 *		\code
 *			POST /sync/salesforce_orders_send/
 *		\endcode
 *
 * @param int $id Obligatoire, Identifiant de la commande à envoyer
 *
 * @return true si l'ajout s'est déroulé avec succès
 * @}
*/

switch( $method ){
	case 'add':

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque le paramètre identifiant de la commande.");
		}

		try {
			sf_login('write');
			sf_order_add($_REQUEST['id']);
			sf_logout();

			$result = true;
		} catch (Exception $e) {
			mail('<EMAIL>', 'err SF '.$config['tnt_id'].' : '.$_REQUEST['id'], $e->getMessage());
			throw new Exception("Erreur de création de la commande ".$_REQUEST['id']." : ".$e->getMessage());
		}

		break;
}


// \endcond
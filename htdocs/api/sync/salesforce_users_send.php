<?php

// \cond only ria
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

/**
 * \defgroup api-sync-salesforce_orders_send Export des comptes / contact dans SalesForce
 * \ingroup sync
 * @{
 * \page api-sync-salesforce_users_send-add  Ajout
 *
 * cette fonction permet de modifier des contacts / comptes dans Salesforce
 *
 *		\code
 *			POST /sync/salesforce_users_send/
 *		\endcode
 *
 * @param int $id Obligatoire, Identifiant de l'utilisateur à envoyer
 *
 * @return true si l'ajout s'est déroulé avec succès
 * @}
*/

switch( $method ){
	case 'add':

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque le paramètre identifiant du compte.");
		}

		try {
			sf_login('write');
			sf_users_add($_REQUEST['id']);
			sf_logout();

			$result = true;
		} catch (Exception $e) {
			mail('<EMAIL>', 'err compte SF '.$config['tnt_id'].' : '.$_REQUEST['id'], $e->getMessage());
			throw new Exception("Erreur de modification du compte id=".$_REQUEST['id']." : ".$e->getMessage());
		}

		break;
}


// \endcond
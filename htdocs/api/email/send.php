<?php
/**
 * \defgroup Emails Emails
 * \ingroup crm
 * @{
 *
 * \page api-email-send-add Ajout
 *
 * cette fonction permet d'envoyer un mail au destinataires définis
 *
 *      \code
 *          POST /email/send/
 *      \endcode
 *
 * @param email_code   Obligatoire : Référence de la configuration de l'email dans riashop
 * @param subject      Obligatoire : Sujet du message
 * @param message      Obligatoire : Corps du message a envoyer
 *
 * @return true si l'envoi s'est déroulé sans problème.
 * @}
*/

switch( $method ){
    case 'add':
        // récupération des informations
        $email_code =  isset($_REQUEST['email_code']) ? $_REQUEST['email_code'] : null;
        $subject = isset($_REQUEST['subject']) ? $_REQUEST['subject'] : null;
        $message = isset($_REQUEST['message']) ? $_REQUEST['message'] : null;
        $is_html = isset($_REQUEST['is_html']) ? $_REQUEST['is_html'] : false;

        // Vérification des informations
        if( !isset($email_code) ){
            throw new Exception("Il manque le code de la configuration de l'email.");
        }
        if( !isset($subject) ){
            throw new Exception("Il manque le sujet de l'email.");
        }
        if( !isset($message) ){
            throw new Exception("Il manque le message de l'email.");
        }

        // Récupération des destinatires de l'email
        if ( $r_cfgs = cfg_emails_get( $email_code ) ){
            if ( $cfg = ria_mysql_fetch_assoc( $r_cfgs ) ){

                $email = new Email();
                $email->setFrom( '<EMAIL>' );
                $email->addTo( $cfg['to'] );
                $email->addCC( $cfg['cc'] );
                $email->addBcc( $cfg['bcc'] );
                $email->addBcc( '<EMAIL>' );
                $email->setReplyTo( $cfg['reply-to'] );

                $email->setSubject( $subject );

                if( $is_html ) {
                    $email->addHtml( $message );
                }else{
                    $email->addParagraph( $message );
                }

                $email->send();
                $result = true;
            }
        }else{
            throw new Exception("La configuration n'a pas été créé sur riashop : ".$email_code);
        }
        break;
}

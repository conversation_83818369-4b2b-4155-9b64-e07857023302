<?php
/**
 *  \defgroup state_bl Etat 
 *  \ingroup Bl
 * 
 *  @{
 *	 \page api-bl-state-upd Mise à jour 
 *
 *	 Cette fonction modifie l'état du bon de livraison
 *
 *	 \code	
 *		PUT /bl/state/
 *	 \endcode
 *	
 *	 @param id Obligatoire : Identifiant du Bon de Livraison
 *	 @param state Obligatoire : Etat du Bon de Livraison
 *	
 *	 @return true si la mise à jour s'est déroulée avec succès 
 * @}
*/

switch( $method ){
	case 'upd':

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : 0;

		if( !isset($_REQUEST['state']) || $id == 0 ){
			throw new Exception("Paramètre invalide.");
		}

		// log les données envoyé par la tablette
		api_log(json_encode($_REQUEST), 'api-states');

		// protection pour éviter un retour arrière sur le changement de status
		$old_state = ord_bl_get_state( $id );

		if( $old_state==$_REQUEST['state'] ){
			$result = true;
		}
		else if(    ($old_state == _STATE_INVOICE && !in_array($_REQUEST['state'], array(_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BL_EXP && !in_array($_REQUEST['state'], array(_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BL_READY && !in_array($_REQUEST['state'], array(_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_IN_PROCESS && !in_array($_REQUEST['state'], array(_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_PAY_CONFIRM && !in_array($_REQUEST['state'], array(_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_WAIT_PAY && !in_array($_REQUEST['state'], array(_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BASKET && !in_array($_REQUEST['state'], array(_STATE_WAIT_PAY,_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_DEVIS && !in_array($_REQUEST['state'], array(_STATE_WAIT_PAY,_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			){
			$result = false;
			$message = "Ancien status ".$old_state." sur bl ".$id." => refus de mise à jour ".$_REQUEST['state'];
		}
		else if( ord_bl_state_update( $id, $_REQUEST['state']) ){

			if( !$is_sync ){
				ord_bl_set_need_sync($id);
			}
			$result = true;
		}

		if (isset($config['dev_id']) && $config['dev_id']>0) {
			if ($old_state == _STATE_BL_PROGRESS && $_REQUEST['state'] == _STATE_BL_EXP) { 
				serials_quantities_update(CLS_BL, $id, true);
			} 
			//the next code is to add quantities to seials
			/*else if ($old_state == _STATE_BL_EXP && $_REQUEST['state'] == _STATE_BL_PROGRESS) {
				serials_quantities_update(CLS_BL, $id);
			}*/
		}

		break;
}

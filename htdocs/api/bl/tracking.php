<?php
/**
 *  \defgroup tracking_bl Tracking numéro des colis 
 *  \ingroup Bl
 * 
 *  @{ 	
 *	Cette fonction permet de mettre à jour le numéro des colis d'un bon de livraison (tracking colis)
 *
 *	\code
 *		PUT	/bl/tracking
 *	\endcode
 *
 *	\page api-bl-tracking-upd Mise à jour 
 *
 *	@param raw_data Obligatoire : Donnée en JSON sous la forme :
 *			\code
 *				 {
 *					"prd_id"	: Identifiant du produit
 *					"bl_id"		: Identifiant du bon de livraison    
 *					"line_id"	: Identifiant de la ligne
 *					"tracking"	: numéro de tracking
 *				 }
 *			\endcode
 *
 *	@return true si la mise à jour est effectuée sinon "paramètres invalides" 
 * @}
*/
switch( $method ){
	case 'upd':
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($objs as $bl_line){
			if(!isset($bl_line["prd_id"], $bl_line["bl_id"], $bl_line["line_id"], $bl_line["tracking"])){
				throw new Exception("Paramètres invalide sur une ligne");
            }

			if(!ord_bl_colis_set($bl_line["bl_id"], $bl_line["prd_id"], $bl_line["line_id"], $bl_line["tracking"])){
				throw new Exception("La mise à jour d'une ligne a échoué.".print_r($bl_line, true));
			}
		}
		$result = true;
		break;
}

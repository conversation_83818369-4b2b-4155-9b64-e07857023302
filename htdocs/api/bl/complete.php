<?php
// \cond onlyria
function update_bl($raw_data){
	global $method;

	$obj = json_decode($raw_data);
	$obj = json_decode(json_encode($obj), true);

	if( !isset($obj['head']) || !is_array($obj['head'])
		|| !isset($obj['lines']) || !is_array($obj['lines'])
		){
		throw new Exception("Paramètres invalide");
	}

	if( !isset($head['srv_id']) ){
		$head['srv_id'] = 0;
	}

	$head = $obj['head'];
	$lines = $obj['lines'];

	$signature = isset($obj['signature']) && is_array($obj['signature']) ? $obj['signature'] : false;
	$serials = isset($obj['serials']) && is_array($obj['serials']) ? $obj['serials'] : false;

	// controles des paramètres pour le bl
	if( !isset($head['usr'],$head['date'],$head['state'],$head['piece'],$head['ref']) ){
		throw new Exception("Paramètres bl invalide");
	}

	// la synchro va parfois envoyer le real_usr en plus de l'autre, cas des écritures des commandes sous le meme code client
	if( isset($head['real_usr']) && $head['real_usr'] > 0 ){
		$head['usr'] = $head['real_usr'];
	}

	// controles des paramètres pour les lignes
	foreach( $lines as $line ){
		if( !isset($line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva']) ){
			throw new Exception('Paramètres lignes invalide');
		}
	}

	$head_id = 0;

	// création / mise à jour de l'entete de bl
	if( $method == "add" ){

		if( trim($head['piece'])!=''){
			$rbl = ord_bl_get( 0, 0, false, false, false, array(), $head['piece'] );
			if( $rbl && ria_mysql_num_rows($rbl) ){
				$bl = ria_mysql_fetch_assoc($rbl);
				$head_id = $bl['id'];
			}
		}
		// on check que le bl n'existerais pas deja dans la DB avec ce numéro de pièce , si c'est le cas on ne le récrée pas !
		if( !$head_id ){
			$head_id = ord_bl_add_sage($head['usr'],$head['piece'],$head['ref'],$head['date'],$head['state'],$head['srv_id']);

			if( !$head_id ){
				throw new Exception("Une erreur est survenue lors de la création de l'entete du bl piece : ".$head['piece']);
			}
		}
	}else{
		$head_id = $head['id'];
	}


	// mise à jour de l'utilisateur
	if( isset($head['usr']) && $head['usr'] > 0 ){
		if( !ord_bl_update_user($head_id, $head['usr']) ){
			throw new Exception("Erreur dans la mise à jour de l'utilisateur du bl ".$head_id);
		}
	}

	// mise à jour de la référence du BL
	if( isset($head['ref']) ){
		if( !ord_bl_ref_update($head_id, $head['ref']) ){
			throw new Exception("Erreur dans la mise à jour de la référence ".$head['ref']." du bl ".$head_id);
		}
	}

	// mise à jour de la date du bl
	if( isset($head['date']) ){
		if( !ord_bl_update_date($head_id, $head['date']) ){
			throw new Exception("Erreur dans la mise à jour de la date du bl ".$head_id);
		}
	}


	// mise à jour du service de livraison
	/*
	if( $head['srv_id']!=0 ){
		if( !ord_bl_update_dlv_service($head_id, $head['srv_id']) ){
			throw new Exception("Erreur dans la mise à jour du service de livraison du bl");
		}
	}
	*/

	// Mise a jour du nombre de produits
	if( isset($head['products']) ){
		if( !ord_bl_update_products($head_id, $head['products']) ){
			throw new Exception("Erreur dans la mise du nombre de produits dans le bl");
		}
	}

	// mise à jours du contact
	if( isset($head['contact_id']) ){
		if( !ord_bl_set_contact_id( $head_id, $head['contact_id']) ){
			throw new Exception("Erreur dans la mise à jour du contact");
		}
	}

	// mise à jour de l'adresse de facturation
	if( isset($head['adr_invoices']) && $head['adr_invoices'] >0){
		if( !ord_bl_adr_invoice_set($head_id, $head['adr_invoices']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de facturation");
		}
	}

	// mise à jour de l'adresse de livraison
	if( isset($head['adr_delivery']) ){
		if( !ord_bl_adr_delivery_set($head_id, $head['adr_delivery']==0 ? false : $head['adr_delivery']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de livraison");
		}
	}

	// Mise à jour du need sync
	if( isset($head['need_sync']) && $head['need_sync']==1 ){
		ord_bl_set_need_sync($head_id);
	}

	// mise à jour de la date de livraison
	if( isset($head['date-livr']) ){
		if( !ord_bl_set_date_livr( $head_id, $head['date-livr']) ){
			throw new Exception("Erreur dans la mise à jour de la date de livraison");
		}
	} else if( isset($head['date_livr']) ){
		if( !ord_bl_set_date_livr( $head_id, $head['date_livr']) ){
			throw new Exception("Erreur dans la mise à jour de la date de livraison");
		}
	}

	// mise à jour du magasin
	/*
	if( isset($head['str_id']) ){
		if( !ord_bl_set_dlv_store( $head_id, $head['str_id']) ){
			throw new Exception("Erreur dans la mise à jour du magasin");
		}
	}
	 */

	// mise à jour du type de package
	/*
	if( isset($head['pkg_id']) ){
		if( !ord_bl_set_package( $head_id, $head['pkg_id']) ){
			throw new Exception("Erreur dans la mise à jour du type de package");
		}
	}
	 */

	// mise à jour des notes
	if( isset($head['dlv_notes']) ){
		if( !ord_bl_dlv_notes_set( $head_id, $head['dlv_notes']) ){
			throw new Exception("Erreur dans la mise à jour d'une note sur le bon de livraison");
		}
	}

	// mise à jour du seller id
	if( isset($head['seller_id']) ){
		$seller = $head['seller_id'] > 0 ? $head['seller_id'] : null;
		if( !ord_bl_set_seller_id( $head_id, $seller ) ){
			throw new Exception("Erreur dans la mise à jour du seller id");
		}
	}

	// mise à jour des commentaires
	if( isset($head['comments']) ){
		if( !ord_bl_comments_set( $head_id, $head['comments']) ){
			throw new Exception("Erreur dans la mise à jour du commentaire sur le bon de livraison");
		}
	}

	// mise à jour du depot
	if( isset($head['dps_id']) ){
		if( !ord_bl_set_deposit( $head_id, $head['dps_id']) ){
			throw new Exception("Erreur dans la mise à jour du depot");
		}
	}
	

	// mise à jours du revendeur
	/*
	if( isset($head['reseller_id']) ){
		if( !ord_bl_set_reseller_id( $head_id, $head['reseller_id']) ){
			throw new Exception("Erreur dans la mise à jour du revendeur");
		}
	}
	 */

	// mise à jours du contact du revendeur
	/*
	if( isset($head['reseller_contact_id']) ){
		if( !ord_bl_set_reseller_contact_id( $head_id, $head['reseller_contact_id']) ){
			throw new Exception("Erreur dans la mise à jour du contact du revendeur");
		}
	}
	 */


	// controles de lignes
	$lines_in = array();
	foreach( $lines as $line ){
		$lines_in[] = $line['prd'].'-'.$line['line'];

		$group_id = false;
		if( isset($line['group_id']) && is_numeric($line['group_id']) ){
			$group_id = $line['group_id'];
		}

		$group_parent_id = false;
		if( isset($line['group_parent_id']) && is_numeric($line['group_parent_id']) ){
			$group_parent_id = $line['group_parent_id'];
		}

		// le produit est une interligne
		if( $line['prd'] == 0 ){
			$ordspace = ord_bl_products_get_spacing($head_id, $line['line']);
			if(!($ordspace && ria_mysql_num_rows($ordspace))){
				if( !ord_bl_products_add_spacing($head_id, false, $line['line']) ){
					throw new Exception("Erreur lors de la création de la ligne interligne : ".$line['line']." - ".$line['notes']);
				}
			}
			$position = false;
			if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
				$position = $line['pos'];
			}
			if( !ord_bl_products_update_spacing($head_id, $line['line'], $line['notes'], $position) ){
				throw new Exception("Erreur lors de l'enregistrement de la ligne interligne : ".$line['line']." - ".$line['notes']);
			}
			if($group_id !== false){
				if(!ord_bl_products_set_group_id($head_id, 0, $line['line'], $group_id, $group_parent_id)){
					throw new Exception("Erreur lors de l'enregistrement du group de la ligne interligne : ".$line['line']." - ".$line['notes']);		
				}
			}
			continue;
		}


		if( !isset($line['colis']) ){
			$line['colis'] = "";
		}
		if( !isset($line['ord']) ){
			$line['ord'] = 0;
		}

		$price_ttc = false;
		if( isset($line['price_ttc']) && is_numeric($line['price_ttc']) ){
			$price_ttc = $line['price_ttc'];
		}

		$ecotaxe = 0;
		if( isset($line['ecotaxe']) && is_numeric($line['ecotaxe']) ){
			$ecotaxe = $line['ecotaxe'];
		}

		$parent = false;
		if( isset($line['parent']) && is_numeric($line['parent']) ){
			$parent = $line['parent'];
		}

		$child_line = false;
		if( isset($line['child_line']) && is_numeric($line['child_line']) ){
			$child_line = $line['child_line'];
		}


		$position = false;
		if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
			$position = $line['pos'];
		}

		$cod_id = false;
		if( isset($line['cod_id']) && is_numeric($line['cod_id']) ){
			$cod_id = $line['cod_id'];
		}

		$col_id = false;
		if( isset($line['col']) && is_numeric($line['col']) ){
			$col_id = $line['col'];
		}

		$rprd = ord_bl_products_get($head_id, 0, false, false, false, $line['prd'], $line['line']);
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$res = ord_bl_products_update_sage( $head_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['ord'],$line['colis'],$price_ttc,$ecotaxe,false, $parent, $child_line, $position, $cod_id, $col_id);
		}else{
			$res = ord_bl_products_add_sage( $head_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['ord'],$line['colis'],$price_ttc,$ecotaxe, false, false, $parent, $child_line, $position, $cod_id, $col_id);
		}


		if( !$res ){
			throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
		}

		// insertion du group_id
		if($group_id !== false){
			if(!ord_bl_products_set_group_id($head_id,  $line['prd'], $line['line'], $group_id, $group_parent_id)){
				throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);	
			}
		}

		// mise à jour des notes 
		$line['notes'] = isset($line['notes']) ? $line['notes'] : "";
		if( !ord_bl_products_notes_update($head_id,  $line['prd'], $line['line'], $line['notes']) ){
			throw new Exception("Erreur lors de l'enregistrement de la note sur le produit : ".$line['prd']);	
		}

		// ajout des champs avancés
		if(isset($line['fields'])) {
			$fields_delete_missing = (isset($line['fields_delete_missing']) && !$line['fields_delete_missing']) ? false : true;
			fields_sync(CLS_BL_PRODUCT, $head_id, $line['prd'], $line['line'], $line['fields'], $fields_delete_missing);
		}
	}

	// retire les lignes produits absente du BL
	$rprd = ord_bl_products_get($head_id);
	while( $prd = ria_mysql_fetch_assoc($rprd) ){
		$key = $prd['id'].'-'.$prd['line'];
		if( !in_array($key, $lines_in) ){
			if( !ord_bl_products_del($head_id,$prd['id'],$prd['line']) ){
				throw new Exception("Erreur lors de la suppression du produit : ".$prd['id']);
			}
		}
	}

	if(isset($head['fields'])) {
		$fields_delete_missing = (isset($head['fields_delete_missing']) && !$head['fields_delete_missing']) ? false : true;
		fields_sync(CLS_BL, $head_id, 0, 0, $head['fields'], $fields_delete_missing);
	}

	// ajout des modeles de saisie
	if(isset($head['models'])) {
		$models_delete_missing = (isset($head['models_delete_missing']) && !$head['models_delete_missing']) ? false : true;
		models_sync(CLS_BL, $head_id, 0, 0, $head['models'], $models_delete_missing);
	}

	// Mise a jour de totaux
	ord_bl_update_totals($head_id);

	// Ajout de la signature
	if( $signature && isset($signature['signature']) ){

		$usr_id = null;
		if( isset($signature['usr_id']) && is_numeric($signature['usr_id']) ){
			$usr_id = $signature['usr_id'];
		}

		$firstname = null;
		if( isset($signature['firstname']) && $signature['firstname'] != "" ){
			$firstname = $signature['firstname'];
		}

		$lastname = null;
		if( isset($signature['lastname']) && $signature['lastname'] != "" ){
			$lastname = $signature['lastname'];
		}

		$function = null;
		if( isset($signature['function']) && $signature['function'] != "" ){
			$function = $signature['function'];
		}

		obj_signature_add( CLS_BL, $head_id, 0, 0, $signature['signature'], $usr_id, $firstname, $lastname, $function );

	}

	// ajout de les serials	
	if ($serials) {
		serials_sync(CLS_BL_PRODUCT, $head_id, false, false, $serials);
	}

	return $head_id;
}
// \endcond 

/**
 * \defgroup complete_bl Bon de livraison Complet
 * \ingroup Bl
 * @{	 
*/	
switch( $method ){
		/** @{@}
 		 * @{		
		 * \page api-bl-complete-get Chargement
		 *
		 * Cette fonction récupére un bon de livraison complet
		 *
		 *		\code
		 *			GET /bl/complete/
		 *   	\endcode
		 *
		 * @param id Obligatoire : identifiant du bon de livraison 
		 * 
		 * @return JSON sous la forme suivante : 
		 *		\code{.json}
		 *		{
		 *		"related": 
		 *		{
         *			"products": [],
         *			"signature": []
         *		},
		 *		"id": identifiant du bon de livraison,
		 *		"piece": référence du bon de livraison dans la gestion commerciale,
		 *		"ref": référence du bon de livraison,
		 *		"date": date format fr,
		 *		"date_en": date format en,
		 *		"usr_id": identifiant de l'utilisateur,
		 *		"total_ht": prix hors taxes,
		 *		"total_ttc": prix toutes taxes comprises,
		 *		"state_id": identifiant du statut,
		 *		"state_name": nom du statut,
		 *		"srv_id": identifiant du service de livraison,
		 *		"adr_dlv_id": identifiant de l'adresse de livraison,
		 *		"adr_invoices": identifiant de l'adresse de facturation,
		 *		"products": nombre de produits dans le bon de livraison,
		 *		"str_id": identifiant du magasin,
		 *		"pkg_id": identifiant du packaging,
		 *		"pay_id": identifiant du moyen de payement,
		 *		"pmt_id": identifiant de la promotion,
		 *		"dps_id": identifiant du dépot de stockage,
		 *		"wst_id": identifiant du site,
		 *		"reseller_id": identifiant du revendeur,
		 *		"reseller_contact_id": identifiant du contact du revendeur,
		 *		"contact_id": identifiant du conctact qui a passé le bon de livraison,
		 *		"seller_id": identifiant du commercial,
		 *		"date_livr": date de livraison,
		 *		"dlv_notes": consigne de livraison,
		 *		"comments": commentaires,
		 *		"age": nombre de jour depuis la création du bon de livraison,
		 *		"date_modified": date de modification format fr,
		 *		"date_modified_en": date de modification format en,
		 *		"need_sync": détermine si le bon de livraison va être synchronisé
		 *		}	
		 * 		\endcode
		 * @}
		*/
	case 'get' :
		$rbl = ord_bl_get($_REQUEST['id']);

		if( $rbl && ria_mysql_num_rows($rbl) ) {
			$content = array();
			$cpt = 0;
			while ($bl = ria_mysql_fetch_assoc($rbl)) {
				if( ++$cpt > 500 ) break; // limite la récupération des données

				$content[] = dev_devices_get_object_simplified(CLS_BL, array($bl['id']));
			}
		}
		$result = true;
		break;
		/** 
 		 *  @{		
		 * \page api-bl-complete-add Ajout 
		 *
		 * Cette fonction ajoute un bon de livraison complet
		 *
		 *	\code
		 *		POST /bl/complete/
		 *  \endcode
		 *
		 * @param raw_data Obligatoire, Donnée en JSON :
		 *		\code{.json}
		 *			{
		 *	 			"head" Obligatoire : Entête du bl
		 *					"usr"				Obligatoire	: Identifiant de l'utilisateur
		 *					"date"				Obligatoire	: Date
		 *					"piece"				Obligatoire	: N° de pièce
		 *					"ref"				Obligatoire	: Référence
		 *					"state"				Obligatoire : Statut du bl
		 *					"srv_id"			Obligatoire	: Service de livraison
		 *				"lines" Obligatoire : Tableau de lignes de la commande
		 *					"prd"				Obligatoire	: Identifiant du produit
		 *					"line"				Obligatoire	: N° de line
		 *					"qte"				Obligatoire	: Quantité
		 *					"ref"				Obligatoire	: Référence du produit
		 *					"name"				Obligatoire	: Nom du produit
		 *					"price_ht"			Obligatoire	: Prix HT unitaire
		 *					"tva"				Obligatoire	: tva en coeficient
		 *					"price_ttc"			Facultatif	: prix TTC unitaire
		 *					"ecotaxe"			Facultatif	: Ecotaxe
		 *					"ord"				Obligatoire	: identifiant de la commande d'origine
		 *					"colis"				Obligatoire	: n° colis
		 *
		 *				"signature" Facultatif : Signature de la commande
		 *					"signature"	: Code de la signature sous une forme moveTo:X:Y..;
		 *					"usr_id"	: Identifiant de l'utilisateur qui as signé
		 *					"firstname"	: Prénom de la personne qui a signé
		 *					"lastname"	: Nom de la personne qui as signé
		 *					"function"	: Fonction de la personne qui a signé
		 *			}
		 *		\endcode
		 *
		 * @return Identifiant du bon de livraison
		 * @}
		*/
	case 'add':
		$bl_id = update_bl($raw_data);
		$result = true;
		$content = array('bl_id' => $bl_id);
		break;

		/** 
 		 *  @{\page api-bl-complete-upd Mise à jour 
		 *
		 * 	Cette fonction met à jour un bon de livraison complet
		 *
		 *	\code
		 *		PUT /bl/complete/
		 *  \endcode
		 *
		 *	Même syntaxe que pour l'ajout (ci-dessus)
		 *	@}
		*/
	case 'upd':
		$bl_id = update_bl($raw_data);
		$result = true;
		break;
}
 ///@}
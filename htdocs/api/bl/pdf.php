<?php
/**
 * \defgroup bl_pdf PDF
 * \ingroup bl
 * @{
 *
 * \page api-bl-pdf-get Chargement
 *
 * Cette fonction permet un retour fichier pdf du bon de livraison, attention ne fonctionne que pour certain client
 *
 *		\code
 *			GET /bl/pdf/
 *		\endcode
 *
 *	 @param $ord Obligatoire, Identifiant de la commande
 *
 *	 @return string Le fichier pdf sinon
*/

switch( $method ){
	case 'get':

		if( isset($_REQUEST['ord']) ){
			$_REQUEST['id'] = $_REQUEST['ord'];
		}

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide.");
		}

    cfg_images_load($config);

    try {
      require_once('Pdf/pdf.inc.php');
      $tmp = null;
      generate_bl($_REQUEST['id'], $tmp, $_REQUEST['id'].'.pdf');
      exit;
    } catch (Exception $e) {
      if ($e->getCode() >= 1000) {
        throw new Exception($e->getMessage());
      } else {
        error_log('[api - pdf] erreur lors de la génération du bon de livraison : '.$e->getMessage());
        throw new Exception('Erreur lors de la génération du bon de livraison');
      }
    }

		break;
}

///@}
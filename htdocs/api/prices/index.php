<?php
/**
 * \defgroup Prix Prix des produits
 * \ingroup cpq
 * @{
*/

///< Limite le nombre de références pouvant être acceptées en entrée
$limit_ref = 500;


function save_row($row){
	global $is_sync;

	if( !isset($row['type'],$row['value']) ){
		throw new Exception("Paramètres invalide ".print_r($row, true));
	}

    if( !isset($row['prd']) ){
    	$row['prd'] = 0;
    }
    if( !isset($row['cat']) ){
    	$row['cat'] = 0;
    }
    if( !isset($row['qte_min']) || $row['qte_min']==0 ){
    	$row['qte_min'] = 1;
    }
    if( !isset($row['qte_max']) || !is_numeric($row['qte_max']) || $row['qte_max']<=0 ){
    	$row['qte_max'] = false;
    }
	if( !isset($row['date_start']) || !isdateheure($row['date_start']) ){
    	$row['date_start'] = null;
    }
	if( !isset($row['date_end']) || !isdateheure($row['date_end']) ){
    	$row['date_end'] = null;
    }
    if( !isset($row['is_promo']) ) {
    	$row['is_promo'] = false;
    }
    if( !isset($row['name']) ){
    	$row['name'] = '';
    }
    if( !isset($row['ref_gescom']) ){
    	$row['ref_gescom'] = null;
    }
    if( !isset($row['kind']) ){
    	$row['kind'] = 1;
    }
    if( !isset($row['priority']) ){
    	$row['priority'] = null;
    }
    if( !isset($row['is_cumuled']) ){
    	$row['is_cumuled'] = true;
    }


	if( $row['date_end'] && substr($row['date_end'],0,4)>2035 ){ // le mysql n'accepte pas au dela de 2038!
		$row['date_end_new'] = date('2035-m-d H:i:s', strtotime($row['date_end']));
	}

	$conditions = array();
	if( isset($row['conditions']) && is_array($row['conditions'])){
		foreach($row['conditions'] as $cnd){
			$conditions[$cnd['fld_id']] = array(
					'symbol'=>$cnd['symbol'],
					'value'=>$cnd['value']
				);
		}
	}

	//test le cas ou le tarifs serait déjà présent avec l'identifiant de la gescom
	$row['id'] = isset($row['id']) && prc_prices_exists($row['id']) ? $row['id'] : prc_prices_get_by_ref_gescom($row['ref_gescom']);
	if( $row['id'] ){
		if( !prc_prices_update( $row['id'],$row['type'],$row['value'],$row['date_start'],$row['date_end'],$row['qte_min'],$row['prd'],$row['cat'],$row['is_cumuled'],$row['name'],$row['qte_max'], $row['ref_gescom'], $row['kind'], $row['priority'] ) ){
			throw new Exception("Erreur dans la mise à jour du tarifs ".print_r($row, true));
		}
		// dans le cas des majs on doit mettre les conditiones à jour après le plus simple étant de tous viréer pour les recréer
		if( !prc_price_conditions_del($row['id']) ){
			prc_prices_del($row['id']);
			throw new Exception("Erreur dans la suppression des conditions sur le tarif ".print_r($row, true));
		}
		foreach( $conditions as $fld=>$val ){
			if( !prc_price_conditions_add( $row['id'], $fld, $val['value'], $val['symbol'] )){
				prc_prices_del($row['id']);
				throw new Exception("Erreur dans la mise à jour des conditions sur le tarif ".print_r($row, true));
			}
		}


	}else{

		$group = 0;
	    if( isset($row['group-name']) && isdateheure($date_start) && isdateheure($date_end) ){
			$group_name = $row['group-name'];
			if( $promotion_groups = prc_promotion_groups_get()){
				while( $rgroup = ria_mysql_fetch_array($promotion_groups) ){
					if( $rgroup['name'] === $group_name ){
						$group = $rgroup['id'];
						break;
					}
				}
			}
			if( $group <= 0 )
				$group = prc_promotion_groups_add($group_name, $date_start, $date_end);
		}

		$row['id'] = prc_prices_add( $row['type'],$row['value'],$row['date_start'], isset($row['date_end_new']) ? $row['date_end_new'] : $row['date_end'],$row['qte_min'],$row['prd'],$row['cat'],$row['is_cumuled'],$is_sync,$row['name'],$conditions,$row['qte_max'],$row['is_promo'], $group, $row['ref_gescom'], $row['kind'], $row['priority'] ) ;
		if( !$row['id'] ){
			throw new Exception("Erreur dans la création du tarifs ".print_r($row, true));
		}
	}

	return $row;
}

switch( $method ){

    /** @{@}
 	 * @{
	 * \page api-prices-index-get Chargement
	 *
	 * Cette fonction renvoie une liste des produits et leur prix associés.
	 *
	 *	\code
	 *		GET /prices/
	 *	\endcode
	 *
	 *	Possibilité de passage de paramètres en format brut suivant le format JSON suivant (limitation à 500 références par appel)
	 *
	 *		\code
	 *			{"prd_ref":["ref1","ref2","ref3"]}
	 *			ou
	 *			{"prd_id":[1,2,3]}
	 * 		\endcode
	 *
	 * Cette fonction retourne le prix avec les conditions appliqués en fonction des paramètres suivants :
	 *
	 * @param int $usr Facultatif, identifiant de l'utilisateur (applique ses conditions de remise)
	 * @param int $prc Facultatif, identifiant de la catégorie tarifaire
	 * @param int $col Facultatif, identifiant du conditionnement
	 * @param int $qte Facultatif, quantité souhaité du produit (utilisé dans le cas de prix dégréssifs)
	 * @param int $prd_id Obligatoire, identifiant du produit
	 * @param string $prd_ref Facultatif, référence du produit (attention car si ce paramètre est renseigné le paramètre prd_id est ignoré)
	 *
	 * @return Json, liste de prix correspondans à la recherche. Pour chaque prix, on retrouve les informations suivantes :
	 *	\code{.json}
	 *       {
	 *			"prd_id" : identifiant du produit,
	 *			"prd_ref : référence du produit (non présent dans le cas où l'on demande la liste via des prd_ids directement),
	 *			"price_ht" : prix unitaire ht,
	 *			"tva_rate" : taux de tva,
	 *			"price_ttc" : prix unitaire ttc
	 *       }
	 *	\endcode
	 * @}
	*/
	case 'get':

		$params = array('usr' => 0, 'prc' => 0, 'ord' => 0, 'qte' => 1, 'col' => 0, 'prd' => array() );

		if( isset($_REQUEST['prd_ref']) && $_REQUEST['prd_ref'] ){

			if( is_array($_REQUEST['prd_ref']) ){
				if( sizeof($_REQUEST['prd_ref']) > $limit_ref ){
					throw new Exception("Il n'est pas possible de demander plus de ".$limit_ref." tarifs en une fois");
				}
			}

			$prd_ids = prd_products_get_id($_REQUEST['prd_ref']);
			if( !$prd_ids || $prd_ids == '' ){
				throw new Exception("Aucun produit trouvé avec les références données");
			}
			if( is_array($_REQUEST['prd_ref']) ){
				foreach($prd_ids as $ref => $id){
					$params['prd'][$ref] = $id;
				}
			}else{
				$params['prd'][$_REQUEST['prd_ref']] = $prd_ids;
			}

		}else if( isset($_REQUEST['prd_id']) && $_REQUEST['prd_id'] ){

			if( is_array($_REQUEST['prd_id']) ){
				if( sizeof($_REQUEST['prd_id']) > $limit_ref ){
					throw new Exception("Il n'est pas possible de demander plus de ".$limit_ref." tarifs en une fois");
				}
				// parcours les prds_id passé pour les ajouters à les requetes
				foreach( $_REQUEST['prd_id'] as $prd_id ){
					if( is_numeric($prd_id) ){
						$params['prd'][] = $prd_id;
					}
				}
			}
			else{
				if( !is_numeric($_REQUEST['prd_id']) ){
					throw new Exception("Les paramètres sont invalides");
				}

				$params['prd'] = array($_REQUEST['prd_id']);
			}

		}else{
			throw new Exception("Les paramètres sont invalides");
		}

		// préparation des autres paramètres
		if( isset($_REQUEST['usr']) && $_REQUEST['usr'] && is_numeric($_REQUEST['usr']) ){
			$params['usr'] = $_REQUEST['usr'];
		}

		if( isset($_REQUEST['prc']) && $_REQUEST['prc'] && is_numeric($_REQUEST['prc']) ){
			$params['prc'] = $_REQUEST['prc'];
		}

		if( isset($_REQUEST['qte']) && $_REQUEST['qte'] && is_numeric($_REQUEST['qte']) ){
			$params['qte'] = $_REQUEST['qte'];
		}

		if( isset($_REQUEST['col']) && $_REQUEST['col'] && is_numeric($_REQUEST['col']) ){
			$params['col'] = $_REQUEST['col'];
		}

		$array = array();

		if( sizeof($params['prd']) > 0 ){
			foreach( $params['prd'] as $ref => $prd_id ){
				$rprice = prd_products_get_price( $prd_id, $params['usr'], $params['prc'], $params['ord'], $params['qte'], $params['col'], false );
				if( $rprice ){
					while( $price = ria_mysql_fetch_assoc($rprice) ){
						$complement = array();
						$complement['prd_id'] = $prd_id;
						if( isset($_REQUEST['prd_ref']) ){
							$complement['prd_ref'] = $ref;
						}

						$array[] = array_merge($complement, $price);
					}
				}
			}
		}

		$content = $array;
		$result = true;

		break;

    /** @{@}
 	 * @{
	 * \page api-prices-index-add Ajout d'un prix
	 *
	 * Cette fonction renvoie permet la création ou la mise à jour d'un prix
	 *
	 *	\code
	 *		POST /prices/
	 *	\endcode
	 *
	 * @param raw_data Obligatoire, données au format Json
	 *		\code{.json}
	 *			[{
	 *					"id" : Facultatif, identifiant de la ligne de prix pour la mise à jour
	 *					"type" : Obligatoire, determine le type de prix : 1 prix net, 2 remise
	 *					"value" : Obligatoire, Montant du prix
	 *					"prd" : Facultatif, identifiant du produit
	 *					"cat" : Facultatif, identifiant de la catégorie
	 *					"qte_min" : Facultatif, quantité minimale d'application
	 *					"qte_max" : Facultatif, quantité maximale d'application
	 *					"date_start" : Facultatif, Date de début d'application du prix, attention format EN
	 *					"date_end" : Facultatif, Date de fin d'application du prix, attention format EN, laissé vide pour "infini"
	 *					"is_promo" : Facultatif, Determine si le prix est une promotion ou non
	 *					"name" : Facultatif, Libellé du prix
	 *					"kind" : Facultatif, Famille du prix, 1 pour prix, 2 pour taxes
	 *					"ref_gescom" : Facultatif, Identifiant unique du prix dans la gestion commerciale, permet de mieux gérer les éventuelles pertes de connexion
	 *					"priority" : Facultatif, Priorité d'application, attention pas utilisé dans tous les cas
	 *					"is_cumuled" : Facultatif, Détermine si le prix est cumulé ou non
	 *					"conditions" : [{
	 *						"fld_id" : Identifiant du champs avancé
	 *						"value" : Valeur de la conditions
	 *						"symbol" : Symbol d'inclusion (=, !=)
	 * 					}]
	 *			}]
	 *		\endcode
	 *
	 * @return Json, liste de prix pour lequel chaque entrée on retrouve les données suivantes :
	 *	\code{.json}
	 *       [{
	*					"id" :  identifiant de la ligne de prix qui aura été créée ou modifiée
	*					"type" :  détermine le type de prix, 1 prix net, 2 remise
	*					"value" :  Montant du prix
	*					"prd" :  identifiant du produit
	*					"cat" :  identifiant de la catégorie
	*					"qte_min" :  quantité minimale d'application
	*					"qte_max" :  quantité maximale d'application
	*					"date_start" :  Date de début d'application du prix, attention format EN
	*					"date_end" :  Date de fin d'application du prix, attention format EN, laissé vide pour "infini"
	*					"is_promo" :  Determine si le prix est une promotion ou non
	*					"name" :  Libéllé du prix
	*					"kind" :  Famille du prix, 1 pour tarif, 2 pour taxes
	*					"ref_gescom" :  Identifiant unique du prix dans la gestion commerciale, permet de mieux gérer les éventuelles perte de connexion
	*					"priority" : Priorité d'application, attention pas utilisé dans tous les cas
	*					"is_cumuled" :  Determine si le prix est cumulé ou non
	 *					"conditions" : [{
	 *						"fld_id" : Identifiant du champs avancé
	 *						"value" : Valeur de la conditions
	 *						"symbol" : Symbol d'inclusion (=, !=)
	 * 					}]
	 *       }]
	 *	\endcode
	 * @}
	*/
    case 'add':
    case 'upd':
        // Récupération des valeurs envoyés
        global $method, $config;
        $obj = json_decode($raw_data, true);

        // Vérifie que le contenu est bien un tableau de données
        if( !is_array($obj) ){
            throw new BadFunctionCallException("Les données reçues ne sont pas sous forme de tableau");
        }

        // Parcours de tout les élléments pour faire le traitement
        $final = array();
        foreach ($obj as $price) {
        	$final[] = save_row($price);
        }

        // Renvois que le traitement à bien été fait
        $result = true;
        $content = $final;
        break;
}

///@}
<?php
/**
 * \defgroup obj_image Objet image
 * \ingroup Image
 * @{
*/
switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-images-object-add Ajout
	 *
	 * Cette fonction permet d'ajout un objet image
	 *
	 * \code
	 *		POST /images/object/
	 * \endcode
	 *
	 * @param int $id Obligatoire, identifiant
	 * @param $obj_id_0 facultatif, identifiant de l'objet champ 0
	 * @param $obj_id_1 facultatif, identifiant de l'objet champ 1
	 * @param $obj_id_2 facultatif, identifiant de l'objet champ 2
	 * @param int $cls_id facultatif, identifiant de la classe
	 * @param $img_id facultatif, identifiant de l'image
	 * @param $type_id facultatif, identifiant du type
	 *
	 * @return id, identifiant de l'image
	 * @}
	*/
	case 'add':

		$obj_id_0 = 0;
		if (isset($_REQUEST["obj_id_0"]) && $_REQUEST["obj_id_0"] > 0) {
			$obj_id_0 = $_REQUEST["obj_id_0"];
		}
		$obj_id_1 = 0;
		if (isset($_REQUEST["obj_id_1"]) && $_REQUEST["obj_id_1"] > 0) {
			$obj_id_1 = $_REQUEST["obj_id_1"];
		}
		$obj_id_2 = 0;
		if (isset($_REQUEST["obj_id_2"]) && $_REQUEST["obj_id_2"] > 0) {
			$obj_id_2 = $_REQUEST["obj_id_2"];
		}
		$cls_id = 0;
		if (isset($_REQUEST["cls_id"]) && $_REQUEST["cls_id"] > 0) {
			$cls_id = $_REQUEST["cls_id"];
		}
		$img_id = 0;
		if (isset($_REQUEST["img_id"]) && $_REQUEST["img_id"] > 0) {
			$img_id = $_REQUEST["img_id"];
		}
		$type_id = 0;
		if (isset($_REQUEST["type_id"]) && $_REQUEST["type_id"] > 0) {
			$type_id = $_REQUEST["type_id"];
		}

		$pos = isset($_REQUEST['pos']) && is_numeric($_REQUEST['pos']) ? $_REQUEST['pos'] : null;
		$alt = isset($_REQUEST['alt']) ? $_REQUEST['alt'] : null;
		$publish = isset($_REQUEST['publish']) ? ($_REQUEST['publish']? true : false) : null;
		$is_main = isset($_REQUEST['is_main']) ? ($_REQUEST['is_main']? true : false) : null;

		if( !$cls_id || !$obj_id_0 || !$img_id ){
			throw new Exception("Paramètre invalide.");
		}

		if( $cls_id == CLS_CATEGORY  && $type_id == 0 ){

			// cas particulier pour aller renseigner les anciennes table
			if( isset($_REQUEST['deleted'])){
				if( !prd_cat_images_del($obj_id_0, $img_id) ){
					throw new Exception("Erreur lors de la suppresion de l'image à la catégorie.");
				}
			}else{
				if( !prd_cat_images_add_existing($obj_id_0, $img_id) ){
					throw new Exception("Erreur lors de l'ajout de l'image à la catégorie.");
				}
			}

			$content['id'] = -1;
			$result = true;

		}
		else if( $cls_id == CLS_PRODUCT && $type_id == 0 ){

			if( isset($_REQUEST['deleted'])){
				if( $is_main ){
					if( !prd_images_main_del($obj_id_0, $img_id) ){
						throw new Exception("Erreur lors de la suppression de l'image principale au produit.");
					}
				}else{
					if( !prd_images_del($obj_id_0, $img_id) ){
						throw new Exception("Erreur lors de la suppression de l'image au produit.");
					}
				}
			}else{
				if( $is_main ){
					if( !prd_images_main_add_existing($obj_id_0, $img_id, true, true, $alt, true) ){
						throw new Exception("Erreur lors de l'ajout de l'image principale au produit.");
					}
				}else{
					if( !prd_images_add_existing($obj_id_0, $img_id, true, true, $publish!==null ? $publish : true, $alt) ){
						throw new Exception("Erreur lors de l'ajout de l'image au produit.");
					}
				}
			}

			$content['id'] = -1;
			$result = true;
		}
		else{

			$rimg = img_images_objects_get( $type_id, $obj_id_0, $obj_id_1, $obj_id_2, 0, $img_id, $cls_id );
			if( $rimg && ria_mysql_num_rows($rimg) ){ // cas ou le lien existe déjà

				$img = ria_mysql_fetch_assoc($rimg);
				if( isset($_REQUEST['deleted'])){

					if( !img_images_objects_del( 0, 0, 0, $img['id'], 0, 0, 0 ) ){
						throw new Exception("Erreur lors de la suppression de l'image.");
					}

				}

				$content['id'] = $img['id'];
				$result = true;

			}else{
				$new_id = img_images_objects_add( $cls_id, $img_id, $type_id, $obj_id_0, $obj_id_1, $obj_id_2, $pos, $alt, $publish, $is_main);

				if ($new_id != null && $new_id > 0) {
					$result = true;
					$content['id'] = $new_id;
				}else{
					$result = false;
				}
			}
		}

		if( $result ){
			if( in_array($config['tnt_id'], array(59, 104, 105)) ){
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_IMG_SEND, array('tsk_id'=>TSK_IMAGES_OBJECTS_ADD, 'images_objects'=>array(
					'obj_id_0' => $obj_id_0,
					'obj_id_1' => $obj_id_1,
					'obj_id_2' => $obj_id_2,
					'cls_id' => $cls_id,
					'img_id' => $img_id,
					'type_id' => $type_id,
				)));
			}
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-images-object-del Suppression
	 *
	 * Cette fonction permet de suprrimer un objet image
	 *
	 *	\code
	 *		DELETE /images/object/
	 *	\endcode
	 *
	 * @param int $id, identifiant de l'objet image
	 *
	 * @return true si la suppression s'est déroulée avec succès
	 *
	 * @}
	*/
	case 'del':

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide.");
		}

		if( $_REQUEST['id'] < 0 ){

			if( !isset($_REQUEST["cls_id"]) ){
				throw new Exception("Paramètre cls_id invalide");
			}
			if( !isset($_REQUEST["obj_id_0"]) ){
				throw new Exception("Paramètre obj_id_0 invalide");
			}
			if( !isset($_REQUEST["img_id"]) ){
				throw new Exception("Paramètre img_id invalide");
			}

			switch ($_REQUEST["cls_id"]) {
				case CLS_PRODUCT:
					if( isset($_REQUEST["is_main"]) && $_REQUEST["is_main"]){
						if( !prd_images_main_del($_REQUEST["obj_id_0"], $_REQUEST["img_id"]) ){
							throw new Exception("Erreur lors de la suppression de l'image principale au produit.");
						}
					}else{
						if( !prd_images_del($_REQUEST["obj_id_0"], $_REQUEST["img_id"]) ){
							throw new Exception("Erreur lors de la suppression de l'image au produit.");
						}
					}
					break;
				case CLS_CATEGORY:
					if( !prd_cat_images_del($_REQUEST["obj_id_0"], $_REQUEST["img_id"]) ){
						throw new Exception("Erreur lors de la suppression de l'image au produit.");
					}
					break;
			}

		}else{
			if( !img_images_objects_del( 0, 0, 0, $_REQUEST['id'], 0, 0, 0 ) ){
				throw new Exception("Erreur lors de la suppression de l'image.");
			}
		}

		$result = true;

		break;
}

///@}
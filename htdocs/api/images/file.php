<?php
/**
 * \defgroup file_image Fichier image 
 * \ingroup Image
 * @{	
*/

// Configuration des images produits
cfg_images_load($config);

switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-images-file-get Chargement 
	 *
	 * Cette fonction récupére le fichier image 
	 *
	 * \code
	 *		GET /images/file/
	 * \endcode
	 *
	 * @param int $id Obligatoire, identifiant de l'image
	 *	
	 * @return En cas de succès, retourne l'image sans l'entête de réponse en JSON
	 * @}
	*/
	case 'get':

		if( isset($_GET['id']) ){

			$url = $config['img_dir'].'/yuto/'.$_GET['id'].'.jpg';
			if( !is_file($url) ){
				$url = $config['img_dir'].'/1000x1000/'.$_GET['id'].'.jpg';
				if( !is_file($url) ){
					$url = $config['img_dir'].'/500x500/'.$_GET['id'].'.jpg';
					if( !is_file($url) ){
						$url = $config['img_dir'].'/260x260/'.$_GET['id'].'.jpg';
						if( !is_file($url) ){
							exit;
						}
					}
				}
			}
			readfile($url);
			exit;
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-images-file-add Ajout
	 *
	 * Cette fonction ajoute le fichier d'une image.
	 *
	 * \code
	 *		POST /images/file/
	 * \endcode
	 * 	
	 * @param $image Obligatoire, le fichier de l'image
	 * @param $tmp_name Obligatoire, le nom de l'image 
	 *	
	 * @return id, Identifiant de l'image
	 * @}
	*/
	case 'add':

		if( !isset($_FILES['image']['tmp_name']) ){
			throw new Exception("Paramètre invalide.");
		}

		$result = false;
		$new_id = img_images_add($_FILES['image']['tmp_name']);
		if ($new_id != null && $new_id > 0) {
			img_images_set_source_name( $new_id, $new_id );
			$content["id"] = $new_id;
			$result = true;
		}

		break;
}

///@}
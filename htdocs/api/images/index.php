<?php
/**
 * \defgroup Image Image 
 * \ingroup dam 
 * @{	 
 * \page api-images-index-get Chargement 
 *
 * Cette fonction permet le chargement des images utilisées dans la boutique en ligne
 *
 * \code
 *		GET /images/
 * \endcode
 *	 
 * @param int $id Obligatoire, tableau d'identifiants d'image
 *	
 * @return Json sous la forme suivante pour chaque élément (tableau d'images) :
 *	\code{.json}
 *    	[{
 *           "id": identifiant de l'image,
 *           "src_name": nom d'origine de l'image,
 *           "type": format de l'image (jpg, png, gif...),
 *           "is_sync": image synchronisée (booléen),
 *           "is_associated": image ayant déjà été associée à un contenu (booléen),
 *           "date_modified": date de dernière modification (format EN) YYYY-MM-DD HH:MM:SS",
 *           "is_masked": l'image doit être masquée dans la fonctionnalité "associations automatiques" (booléen)
 *		 },
 *	\endcode
 * @}
*/

switch( $method ){
	case 'get':

		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}


		$array = array();
		$rimg = img_images_get($ids);

		while($img = ria_mysql_fetch_assoc($rimg)){
			$array[] = $img;
		}

		$result = true;
		$content = $array;

		break;
}
<?php
/**
 * \defgroup Notes Notes
 * \ingroup Collaboration
 * @{
*/
require_once('obj.notes.inc.php');

switch( $method ){
	/** @{@}
	 * @{
	 * \page api-notes-index-add Ajout
	 *
	 * Cette fonction permet d'ajouter une note
	 *
	 *		\code
	 *			POST /notes/
	 *		\endcode
	 *
	 * @param int $cls_id Obligatoire, identifiant de la classe de l'objet
	 * @param int $obj_id_0 Obligatoire, identifiant 0 de l'objet
	 * @param int $obj_id_1 facultatif, identifiant 1 de l'objet
	 * @param int $obj_id_2 facultatif, identifiant 2 de l'objet
	 * @param string $name Obligatoire, intitulé de la note
	 * @param string $content Obligatoire, détail de la note
	 * @param int $usr_id facultatif, identifiant de l'auteur
	 *
	 * @return identifiant de la note
	 * @}
	*/
	case 'add':

		if( !isset($_POST['cls_id'],$_POST['obj_id_0'],$_POST['name'],$_POST['content']) ){
			throw new Exception("Paramètres invalide");
		}

		if( !isset($_POST['usr_id']) ){
			$_POST['usr_id'] = null;
		}

		// dans le cas ou la note n'a pas de user en entré on prend celui de la tablette qui emet
		if( !$_POST['usr_id'] && isset($config['usr_id']) ){
			$_POST['usr_id'] = $config['usr_id'];
		}

		$obj = $_POST['obj_id_0'];
		if( isset($_POST['obj_id_1']) && $_POST['obj_id_1'] != 0 ){
			$obj = array($_POST['obj_id_0'],$_POST['obj_id_1']);

			if( isset($_POST['obj_id_2']) && $_POST['obj_id_2'] != 0 ){
				$obj[] = $_POST['obj_id_2'];
			}
		}


		$note_id = fld_object_notes_add( $_POST['cls_id'], $obj, $_POST['name'],$_POST['content'], $_POST['usr_id'], $is_sync );
		if( !$note_id ){
			throw new Exception("Une erreur est survenue lors de la l'ajout de l'élément");
		}else{
			$result = true;
			$content = array('id' => $note_id);
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-notes-index-upd Mise à jour
	 *
	 * Cette fonction permet de modifier une note.
	 *
	 *		\code
	 *			PUT /notes/
	 *		\endcode
	 *
	 * @param int $id Obligatoire, identifiant de la classe de l'objet
	 * @param string $name Obligatoire, intitulé de la note
	 * @param $content Obligatoire, détail de la note
	 *
	 * @return true si la modification s'est déroulée avec succès.
	 * @}
	*/
	case 'upd':


		if( !isset($_REQUEST['id'],$_REQUEST['name'],$_REQUEST['content']) ){
			throw new Exception("Paramètres invalide");
		}

		if( !fld_object_notes_exists($_REQUEST['id']) ){ // cas de note supprimé, on retourne true quand meme pour ne pas bloquer la synchro ?
			$result = true;
		}else{

			if( !fld_object_notes_upd( $_REQUEST['id'], $_REQUEST['name'],$_REQUEST['content'] ) ){
				throw new Exception("Une erreur est survenue lors de la mise à jour de l'élément");
			}else{
				$result = true;
			}
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-notes-index-del Suppression
	 *
	 * Cette fonction permet de modifier une note.
	 *
	 *		\code
	 *			DELETE /notes/
	 *		\endcode
	 *
	 * @param int $id Obligatoire, identifiant de la classe de l'objet
	 *
	 * @return true si la suppression s'est déroulée avec succès. 
	 * @}
	*/
	case 'del':

		if( !isset($_REQUEST['id']) || !fld_object_notes_exists($_REQUEST['id'])){
			throw new Exception("Paramètres invalide");
		}

		if( !fld_object_notes_del($_REQUEST['id']) ){
			throw new Exception("Une erreur est survenue lors de la suppression de l'élément");
		}else{
			$result = true;
		}

		break;
}
///@}
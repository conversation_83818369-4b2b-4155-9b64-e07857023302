<?php
/** \mainpage api.riashop.fr
 *
 *	\section intro_sec Introduction
 *
 *	Cette documentation décrit le fonctionnement de l'API de la plateforme Riashop. Cette API est destinée à réaliser
 *	des manipulations dans RiaShop.
 *	L'accès à cette API se fait via l'URL https://api.riashop.fr/
 *
 *	Il est possible de vérifier l'accès avec la requête suivante :
 *	https://api.riashop.fr?logtoken={token}
 *
 *	Le fonctionnement typique se fait via le passage de trois paramètres :
 *		- \c logtoken : identifie le client à l'origine de la requête. C'est une clé md5 propre à chacuns.
 *		- \c module : module RiaShop sur lequel une action doit être effectué (produits, commandes, clients, etc...)
 *		- \c action : type d'action à réaliser sur un ou plusieurs éléments du module (ajout, suppression, mise à jour, récupération)
 *
 *	La requête prend la forme https://api.riashop.fr/{module}/{action}?logtoken={token}
 *
 *	La méthode HTTP de la requête influence l'action effectué par l'API :
 *	- GET : Récupére des informations
 *	- POST : Ajoute une information
 *	- PUT : Met à jour une information
 *	- DELETE : Supprime une information
 *
 *	Les réponses ont toujours le format JSON suivant :
 *	- result	: Résultat de la fonction
 *	- time		: Date et heure de l'envoie de la réponse
 *	- message	: Message d'erreur en cas d'échec
 *	- content	: Contenue de la réponse en cas de succès, vide en cas d'échec
 *
 *	Les actions sont classifiées dans l'ensemble de la documentation suivant les termes :
 *	- GET : Chargement
 *	- POST : Ajout
 * 	- PUT : Mise à jour
 * 	- DELETE : Suppression
 *
 *	\defgroup cpq Tarification
 *	\defgroup oms Ventes
 *	\defgroup sync Synchronisation
 *	\defgroup crm Comptes
 *	\defgroup pim Catalogues
 *	\defgroup scm Logistique
 *	\defgroup dam Documents
 *	\defgroup stats Statistiques
 *	\defgroup config Configuration
 *	\defgroup search Recherche
 *	\defgroup Collaboration Collaboration
 *	\defgroup Yuto Yuto
 */

// \cond onlyria
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");

define('_CODE_GOOD',200);
define('_CODE_BAD_REQUEST',400);

set_include_path(__DIR__.'/../../include/');

define('_RIASHOPSYNC_DEPLOY', __DIR__."/../../sync_riashop");

require_once('strings.inc.php');

$is_cli = false; // variable déterminant si c'est la ligne de commande
$is_sync = false; // variable déterminant si c'est le logiciel de synchro
$is_fdv = false; // variable déterminant si c'est un yuto


// Initialisation des variables en mode ligne de commande
if(  php_sapi_name() === 'cli' ){
	$is_cli= true;

	$longopts = array(
		'module:',
		'action:',
		'logtoken:',
		'method:', // correpont à $_SERVER['REQUEST_METHOD']
		'data:', // correpond au $raw_data
	);
	$ar_params = getopt( '', $longopts );
    
	// check les paramètres d'entrée
	if (!ria_array_key_exists(array('module', 'action', 'logtoken', 'method', 'data'), $ar_params)) {
		error_log("Paramètre d'entrée invalide");
		exit(1);
	}
    
	$path = '/'.$ar_params['module'].'/'.$ar_params['action'].'/';
	unset($ar_params['module']);
	unset($ar_params['action']);

	$method = $ar_params['method'];
	if( !in_array($method, array('add','upd','del','get')) ){
		error_log('Méthode incorrecte.');
		exit(1);
	}
	unset($ar_params['method']);

	$_REQUEST['logtoken'] = $ar_params['logtoken'];
	$_GET['logtoken'] = $ar_params['logtoken']; // nécessaire pour la gcp et RegisterGcp.php
	unset($ar_params['method']);

	// passage des paramètres
	parse_str($ar_params['data'], $params);
	$_REQUEST = $params + $_REQUEST;
	$raw_data = $ar_params['data'];
	
//         global $worker_mariadb;
//         $worker_mariadb = $ar_params['data']['mariadb'];
//         print_r($worker_mariadb);
}
// initialisation des variables en mode http
else{
	$path = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
	$raw_data = file_get_contents('php://input');
	$_SERVER['REQUEST_METHOD'] = isset($_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE']) ? $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'] : $_SERVER['REQUEST_METHOD'];

	// le header http est spécifié
	switch($_SERVER['REQUEST_METHOD']){
		case 'GET':
			$method = 'get';
			break;
		case 'POST':
			$method = 'add';
			break;
		case 'PUT':
			$method = 'upd';
			break;
		case 'DELETE':
			$method = 'del';
			break;
		default:
			error_log('Méthode incorrecte.');
			exit(1);
	}

	// récupère les paramètres des methodes PUT et DELETE
	if( in_array($method, array('upd','del')) ){
		parse_str($raw_data, $params);
		$_REQUEST = $params + $_REQUEST;
	}
}

// si le raw data est du json alors on converti les données dans le $_REQUEST
$objs = false;
if( trim($raw_data) ){
	$objs = json_decode($raw_data, true);
	if(is_array($objs)){
		$_REQUEST = array_merge($_REQUEST, $objs);
	}
}

// gestion de paramètres global comme les limites
$offset = isset($_REQUEST['offset']) && is_numeric($_REQUEST['offset']) ? $_REQUEST['offset'] : 0;
$limit = isset($_REQUEST['limit']) && is_numeric($_REQUEST['limit']) ? $_REQUEST['limit'] : 150;


// if($objs)
// {
//     if(array_key_exists('mariadb', $objs))
//     {
//         global $worker_mariadb;
//         $worker_mariadb = $objs['mariadb'];
//     }
// 	if(array_key_exists('queue', $objs))
//     {
//         global $worker_queue;
//         $worker_queue = $objs['queue'];
//     }
// }

// laisser les require ici car sinon la connexion sql n'est pas correctment faite.
require_once( 'env.inc.php');
require_once('orders.inc.php');
require_once('devices.inc.php');
require_once('prd.stocks.inc.php');
require_once('notifications.inc.php');
require_once('utils.php');

// préparation des variables pour la réponse
$result = false; // par défaut toutes les requetes échous
$message = ''; // message d'erreur ou succès
$content = array() ; // contenu de la réponse final
$result_code = _CODE_GOOD;

// paramètre de sync
if( !$is_cli ){
	$is_sync = isset($_REQUEST['is_sync']) && $_REQUEST['is_sync'];
	$is_fdv = isset($_REQUEST['logtoken']) && (strlen($_REQUEST['logtoken']) == 6 || strlen($_REQUEST['logtoken']) == 9);
}

// identification du tenant
if( isset($_REQUEST['logtoken']) ){
	$infos = tnt_tenants_is_authorized( trim($_REQUEST['logtoken']), 0, $is_fdv );
	if( $infos['error'] ){
		exit(1);
	}else{
		// chargement de la configuration
		$config = array();
		$config['tnt_id'] = $infos['tenant'];
		$config['wst_id'] = $infos['website'];

		cfg_variables_load($config);

		// Attention ces deux load sont mis en commentaire pour limiter les requetes inutiles et sont fait dans les appels nécesaires.
		// cfg_images_load($config);
		// cfg_products_load($config);
	}
}else{
	exit(1);
}

// identification de l'utilisateur
$auth = false;
if( $is_fdv && (!isset($_REQUEST['token']) || !trim($_REQUEST['token'])) && $path != '/devices/' ){
	$message = "Le token utilisateur est incorrect.";
	$content = array( 'is_active' => 0 );
}
if( isset($_REQUEST['token']) && trim($_REQUEST['token']) && $path != '/auth/' ){
	// test si c'est un token pour devices
	$auth = dev_devices_is_authorized( $_REQUEST['token'] );
	if( $auth['error'] ){
		$message = "Le token utilisateur est incorrect.";
		$content = array( 'is_active' => 0 );
	}else{
		// chargement du device id
		$config['dev_id'] = $auth['dev_id'];
		$config['usr_id'] = $auth['usr_id'];
		$config['dev_key'] = $auth['dev_key'];
		$config['dev_brand'] = $auth['dev_brand'];
		$config['dev_model'] = $auth['dev_model'];
		$config['dev_version'] = $auth['dev_version'];
		$config['dev_os_version'] = $auth['dev_os_version'];

		if( $config['dev_version'] <= 180 ){
			// chargement des config liée à l'utilisateur pour le website 0
			$rconf = cfg_overrides_get( 0, array(), '', $config['usr_id'] );
			if( $rconf && ria_mysql_num_rows($rconf) ){
				while( $conf = ria_mysql_fetch_assoc($rconf) ){
					$config[$conf['code']] = $conf['value'];
				}
			}
			// chargement des config liée à l'utilisateur pour le website fdv
			$rconf = cfg_overrides_get( $config['wst_id'], array(), '', $config['usr_id'] );
			if( $rconf && ria_mysql_num_rows($rconf) ){
				while( $conf = ria_mysql_fetch_assoc($rconf) ){
					$config[$conf['code']] = $conf['value'];
				}
			}
		}

		dev_devices_usages_add($auth['dev_id']);
	}
}

if(!isset($config['usr_id'])) {
	$config['usr_id']=0;
}

// si pas de message alors on lance l'action demandé
if( $path == '/' ){
	$result = true;
	$content = array(
		'tnt_name' => tnt_tenants_get_name($config['tnt_id'])
	);

	if( isset($config['sso_active']) && $config['sso_active'] ){
		$content['sso_active'] = true;
		$content['sso_authendpoint'] = $config['sso_authendpoint'];
		$content['sso_client_id'] = $config['sso_client_id'];
		$content['sso_tokenendpoint'] = $config['sso_tokenendpoint'];
		$content['sso_userendpoint'] = $config['sso_userendpoint'];
	}

}else if( !trim($message) ){

	// switch en fonction de l'url
	$module = preg_replace('/^\/([a-z0-9\-_]+)\/.*$/', '$1', $path);

	$file = 'index';
	if( preg_match('/^\/'.$module.'\/([a-z0-9\_\-]+)\/.*$/', $path) ){
		$file = preg_replace('/^\/'.$module.'\/([a-z0-9\_\-]+)\/.*$/', '$1', $path);
	}

	// inclusion du fichier correspond à la requete
	$file =  dirname(__FILE__).'/'.$module.'/'.$file.'.php';
	try{
		if( !is_file($file) ){
			throw new Exception("Fichier introuvable");
		}else{
			include( $file );
		}
	}catch(RiaShopException $e){
		$message = $e->getMessage();
		$code = $e->getCode();
		$riaCode = $e->getRiaCode();
		$riaParam = $e->getRiaParam();
	}catch(Exception $e){
		$message = $e->getMessage();
		$code = $e->getCode();
		//$result_code = _CODE_BAD_REQUEST; commenté car ca pose des soucis pour le cache redirect google !
	}
}

switch ($result_code) {
	case _CODE_BAD_REQUEST: 	header( 'HTTP/1.1 400 BAD REQUEST' );		break;
	default: 					header("HTTP/1.1 200 OK"); 					break;
}

// convertion de la réponse en json.
$return = array( 'result' => $result, 'time' => date('Y-m-d H:i:s'), 'message' => $message, 'content' => $content );
// Add the exception code if possible.
if (isset($code) && !empty($code)) {
    $return["code"] = $code;
}
// Add the RiaShop error code if exist (in the case of RiaShopException).
if (isset($riaCode) && !empty($riaCode)) {
	$return["ria_code"] = $riaCode;
}
// Add missing params name if possible (in the case of RiaShopException).
if (isset($riaParam) && !empty($riaParam)) {
	$return["ria_param"] = $riaParam;
}

$json = json_encode( $return );

header('Content-Type: application/json; charset=utf-8');
print $json;

// \endcond

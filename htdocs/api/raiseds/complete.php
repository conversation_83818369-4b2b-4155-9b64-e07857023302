<?php
/**
 * \defgroup api-raiseds Relevés de linéaires
 * \ingroup oms
 * @{
*/	

use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;
use Riashop\PriceWatching\models\LinearRaised\prw_offers_states;

// \cond onlyria

function update_raised($raw_data){
	global $method, $config;
	$obj = json_decode($raw_data);
	$obj = json_decode(json_encode($obj), true);

	if( !isset($obj['head']) || !is_array($obj['head'])
		|| !isset($obj['offers']) || !is_array($obj['offers'])
		){
		throw new Exception("Paramètres invalide");
	}

	$head = $obj['head'];
	$offers = $obj['offers'];

	// controles des paramètres pour le invoices
	if( !isset($head['author_id'],$head['usr_id'],$head['pfl_id'],$head['group_id']) ){
		throw new Exception("Paramètres entête relevé invalide");
	}

	// controles des paramètres pour les lignes
	foreach( $offers as $offer ){
		if( !isset($offer['prd_id'],$offer['id'],$offer['plr_id']) ){
			throw new Exception('Paramètres lignes invalide');
		}
	}

	if( !isset($head['total_dn']) || !is_numeric($head['total_dn'])){
		$head['total_dn'] = null;
	}
	if( !isset($head['total_dn_section']) || !is_numeric($head['total_dn_section'])){
		$head['total_dn_section'] = null;
	}
	if( !isset($head['date_created']) || !isdate($head['date_created'])){
		$head['date_created'] = null;
	}

	// création / mise à jour de l'entête de invoices
	if( $method == "add" ){
		$plr_id = prw_linear_raised::add($head['author_id'], $head['usr_id'], $head['pfl_id'], $head['group_id'], $head['total_dn'], $head['total_dn_section'], $head['date_created']);

		if( !$plr_id ){
			throw new Exception("Une erreur est survenue lors de la création de l'entête du relevé");
		}
	}else{
		$plr_id = $head['id'];

		if( !prw_linear_raised::update($head['id'], $head['author_id'], $head['usr_id'], $head['pfl_id'], $head['group_id'], $head['total_dn'], $head['total_dn_section']) ){
			throw new Exception("Une erreur est survenue lors de la mise à jour de l'entete du relevé");
		}
	}

	// controles de lignes
	$offers_in = array();
	foreach( $offers as $offer ){

		$props = array(
			'landedprice' => is_numeric($offer['landedprice']) ? $offer['landedprice'] : null,
			// 'shippingprice' => is_float($offer['shippingprice']) ? $offer['shippingprice'] : null,
			// 'cpt_id' => is_numeric($offer['cpt_id']) ? $offer['cpt_id'] : null,
			// 'promo_price' => is_numeric($offer['promo_price']) ? $offer['promo_price'] : null,
			// 'url' =>  $offer['url'],
			'count' => is_numeric($offer['count']) ? $offer['count'] : null,
			'level' => is_numeric($offer['level']) ? $offer['level'] : null,
			'facings' => is_numeric($offer['facings']) ? $offer['facings'] : null,
			'positions' => is_numeric($offer['positions']) ? $offer['positions'] : null,
			'pwf_rank' => is_numeric($offer['pwf_rank']) ? $offer['pwf_rank'] : null,
			'pwf_pmc' => is_numeric($offer['pwf_pmc']) ? $offer['pwf_pmc'] : null,
			'is_cpt' => isset($offer['is_cpt']) && $offer['is_cpt'],
		);

		$roffer = \Riashop\PriceWatching\models\LinearRaised\prw_offers::get($plr_id, $offer['id'], $offer['prd_id']);
		if( $roffer && ria_mysql_num_rows($roffer) ){

			$props['usr_id'] = is_numeric($offer['usr_id']) ? $offer['usr_id'] : null;
			if( !\Riashop\PriceWatching\models\LinearRaised\prw_offers::update($offer['id'], $plr_id, $offer['prd_id'], $props) ){
				throw new Exception("Erreur lors de la mise é jour d'une offre : ".$plr_id." ".$offer['id']." ".$offer['prd_id']." ".$state['ps_id']);
			}

		}else{
			$offer['id'] = \Riashop\PriceWatching\models\LinearRaised\prw_offers::add($plr_id, $offer['prd_id'], $offer['usr_id'], $props);
			if( !$offer['id'] ){
				throw new Exception("Erreur lors de l'enregistrement d'une offre : ".$plr_id." ".$offer['id']." ".$offer['prd_id']);
			}
		}

		// gestion du status sur les offres
		if( isset($offer['states']) && sizeof($offer['states']) ){

			// pour faire simple on vire tous les status pour les recréer
			$rstates = prw_offers_states::delete($offer['id']);

			foreach($offer['states'] as $state){
				if( !prw_offers_states::add($offer['id'], $state['ps_id']) ){
					throw new Exception("Erreur lors de l'enregistrement d'un status sur une offre : ".$plr_id." ".$offer['id']." ".$offer['prd_id']." ".$state['ps_id']);
				}
			}
		}

		// ajout des champs avancés
		if( isset($offer['fields']) ) {
			$fields_delete_missing = (isset($offer['fields_delete_missing']) && !$offer['fields_delete_missing']) ? false : true;
			fields_sync(CLS_LINEAR_OFFERS, $offer['id'], $offer['prd_id'], $plr_id, $offer['fields'], $fields_delete_missing);
		}

		$offers_in[] = $offer['id'].'-'.$offer['prd_id'];
	}


	// retire les lignes offres absente du relevé
	$roffer = \Riashop\PriceWatching\models\LinearRaised\prw_offers::get($plr_id);
	if( $roffer ){
		while( $offer = ria_mysql_fetch_assoc($roffer) ){
			$key = $offer['id'].'-'.$offer['prd_id'];
			if( !in_array($key, $offers_in) ){
				if( !\Riashop\PriceWatching\models\LinearRaised\prw_offers::delete($plr_id, $offer['id'], $offer['prd_id']) ){
					throw new Exception("Erreur lors de la suppression d'une offre : ".$plr_id." ".$offer['id']." ".$offer['prd_id']);
				}
			}
		}
	}

	// ajout des champs avancés
	if(isset($head['fields'])) {
		$fields_delete_missing = (isset($head['fields_delete_missing']) && !$head['fields_delete_missing']) ? false : true;
		fields_sync(CLS_LINEAR_RAISED, $plr_id, 0, 0, $head['fields'], $fields_delete_missing);
	}

	if(isset($config['dev_id']) && $config['dev_id']>0) {
		switch($config['tnt_id']){
			case 59 :
			case 104 :
			case 105 :
				// lancement de l'export de la commande dans SalesForce en tâche asynchrone
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_LINEARS_SEND, array('id'=> $plr_id));
				break;
		}
	}

	return $plr_id;
}

// \endcond

switch( $method ){
    /** @{@}
 	 * @{
     * \page api-raiseds-complete-add Ajout 
     *   
	 * cette fonction permet l'ajout d'un nouveau relevé de linéaires
	 *
	 *		\code
	 *			POST raiseds/complete/
	 *		\endcode
     *   
     * @param raw_data Obligatoire, Donnée en json_decode: 
	 *	\code{.json}
 	 *     {
     *           "head" Obligatoire : Entête du relevé
     *               "id"                Obligatoire : Identifiant du relevé
     *               "author_id"         Obligatoire : Id de l'auteur du relevé
     *               "usr_id"            Obligatoire : id de l'utilisateur concerné par le relevé
     *               "pfl_id"            Obligatoire : id de la liste de produits suivi
     *               "group_id"          Obligatoire : id du group liant les relevés
     *               "date_created"      facultatif  : date de création du relevé
     *           "offers" Obligatoire : Tableau des offres du relevés
     *               "id" : Identifiant de l'offre
     *               "cpt_id" : Identifiant du concurrant
     *               "prd_id" : Identifiant du produit
     *               "landedprice" : prix relevé
     *               "shippingprice" : prix relevé pour les frais de port
     *               "promo_price" : prix relevé en promotion
     *               "url" :  url du produit
     *               "date_created" :  date de création de l'offre
     *               "usr_id" :  id de l'utilisateur concerné par le relevé
     *               "count" :  nb d'éléments
     *               "level" :  nb de niveau
     *               "facings" :  nb de facings
     *               "positions" :  positions de l'offre dans le relevé
     *               "pwf_rank" :  rank de l'offre dans le relevé
     *               "pwf_pmc" :  pmc de l'offre dans le relevé
     *               "plr_id" :  id du relevé
     *               "states" : Facultatif, liste des status pour une offre
     *               "ps_id" : id du status
 	 *     }
 	 *	\endcode

	 *
	 * @return Identifiant du relevé
	 *	
	 * @}
	*/
	case 'add':
		$plr_id = update_raised($raw_data);
		$result = true;
		$content = array('plr_id' => $plr_id);
		break;
    /** @{@}
 	 * @{	
	 * \page api-raiseds-complete-upd Mise à jour 
	 *
	 * Cette fonction permet de mettre un jour un relevé linéaire 
	 *
	 *	\code
	 *		PUT raiseds/complete/
	 *	\endcode
	 *
	 * @see ci-dessus ( ajout )
	 *
	 * @return true si le relevé est modifié sans erreur  
	 * @}
	*/
	case 'upd':
		$plr_id = update_raised($raw_data);
		$result = true;
		break;
}
///@}
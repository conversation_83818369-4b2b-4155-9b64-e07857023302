<?php
/**
 * \defgroup api-return Bons de retour client
 * \ingroup oms
 * @{
*/
require_once('ord.returns.inc.php');

switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-return-index-get Chargement
	 *
	 * Cette fonction permet de récupérer des bons de retour
	 *
	 *		\code
	 *			GET /return/
	 *		\endcode
	 *
	 * @param id obligatoire : identifiant du bon de retour 
	 * 
	 * @return json sous la forme suivante : 
	 *		\code{.json}
	 *         {
 	 *  			"id" : identifiant du retour
 	 *				"date" : date du retour
 	 *				"products" : nombre de produits du retour
 	 *				"total_ht" : total ht
 	 *				"total_ttc" : total ttc
 	 *				"user_id" : identifiant du client
 	 *				"ord_id" : identifiant de la commande
 	 *				"states_id" : état de la commande
 	 *				"prepaid_label" : étiquette prépayée (booléen)
	 *				"wst_id_ord" : identifiant du site de la commande à l'origine du retour
	 *				"need_sync": 
     *		    	"adr_invoices": 
     *		    	"piece": 
     *		    	"ref": 
     *		    	"adr_delivery": 
     *		    	"dlv_notes": 
     *		    	"pkg_id": 
     *		    	"srv_id": 
     *		    	"str_id": 
     *		    	"pay_id": 
     *		    	"pmt_id": 
     *		    	"seller_id": 
     *		    	"dps_id": 
     *		    	"wst_id": 
     *		    	"contact_id": 
     *		    	"reseller_id": 
     *		    	"reseller_contact_id": 
	 *	       }
	 *		\endcode
	 * @}
	*/
	case 'get':
		if( !isset($_REQUEST['id']) ){
			throw new Exception( 	"Paramètre invalide.");
		}

		$rreturn = ord_returns_get($_REQUEST['id'] );
		if( $rreturn && ria_mysql_num_rows($rreturn) ){
			$result = true;
			$content = ria_mysql_fetch_assoc($rreturn);
		}
		break;
	/** @{@}
 	 * @{
	 * \page api-return-index-del Suppression 
	 *
	 * Cette fonction permet la suppression des bons de retour
	 *
	 *		\code
	 *			DELETE /return/
	 *		\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode : 
	 * 
	 *		\code{.json}
	 *			{
	 *				"return_id"	Obligatoire	: Identifiant du bon de retour
	 *			}
	 *		\endcode

	 * @return true si la suppression s'est déroulée avec succès 
	 * @}
	*/
	case 'del':
		global $method, $config;
		$obj = json_decode($raw_data, true);
		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $return){
			if(!isset($return["return_id"])){
				throw new Exception("Paramètres invalide");
			}
			if(!ord_returns_del($return['return_id'])){
				throw new Exception("La suppression du return n°:".$return['return_id']." a échoué");
			}
		}
		$result = true;
		break;
}
///@}
<?php
/**
 * \defgroup api-return-states Status
 * \ingroup api-return
 * @{	 
 * \page api-return-states-get Chargement
 * 
 *		\code
 *			GET /return/states/
 *		\endcode
 *
 * Cette fonction récupère les éléments des status des retours 
 *	
 * @param $id Obligatoire, Identifiant de la commande
 * @param $products_states Facultatif, 1 pour recuperer les statuts des ligne de retour
 *
 * @return json avec les colonnes :
 *      \code{.json}
 *          {
 *				"id" : Identifiant de l'état du retour
 *				"name" : Libellé de l'état du retour
 *				"name_plural" : libellé de l'état du retour, au pluriel (Only for retunrs states)
 *				"desc" : description de l'état du retour
 *				"position": position de l'état du retour 
 *	        },
 *      \endcode
 * @}
*/
switch( $method ){
	case 'get':

		$result = true;

		$products_states = isset($_REQUEST['products_states']) && $_REQUEST['products_states']=='1';
		$rstates = ( $products_states == false ) ? ord_returns_states_get() : ord_returns_products_states_get()  ;
		if( $rstates && ria_mysql_num_rows($rstates) ){
			while($state = ria_mysql_fetch_assoc($rstates)){
				$content[] = $state;
			}
		}

		break;
}
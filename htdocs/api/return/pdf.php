<?php
/**
 * \defgroup return_pdf PDF
 * \ingroup return
 * @{
 *
 * \page api-return-pdf-get Chargement
 *
 * Cette fonction permet un retour fichier pdf du bon de retotur, attention ne fonctionne que pour certain client
 *
 *		\code
 *			GET /return/pdf/
 *		\endcode
 *
 *	 @param $ord Obligatoire, Identifiant de la commande
 *
 *	 @return string Le fichier pdf sinon
*/

switch( $method ){
	case 'get':
		if( !isset($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide.");
		}

    cfg_images_load($config);

    try {
      require_once('Pdf/pdf.inc.php');
      $tmp = null;
      generate_return($_REQUEST['id'], $tmp, $_REQUEST['id'].'.pdf');
      exit;
    } catch (Exception $e) {
      if ($e->getCode() >= 1000) {
        throw new Exception($e->getMessage());
      } else {
        error_log('[api - pdf] erreur lors de la génération du bon de retour : '.$e->getMessage());
        throw new Exception('Erreur lors de la génération du bon de retour');
      }
    }

		break;
}

///@}
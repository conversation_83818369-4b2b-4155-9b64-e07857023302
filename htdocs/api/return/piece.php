<?php

/**
 *  \defgroup api-return-piece Référence de la gestion commerciale 
 *  \ingroup api-return
 * 
 *  @{
 *	\page api-return-piece-upd Mise à jour
 *
 *	Cette fonction permet de mettre à jour le numéro de pièce d'un bon de retour
 *
 *	\code
 *		PUT	/return/piece
 *	\endcode
 *
 *	@param id Obligatoire : identifiant du bon de retour
 *	@param piece Obligatoire : numéro de pièce
 *
 *	@return true si la mise à jour s'est déroulé avec succès. 
 * @}
*/
switch( $method ){
	case 'upd':
		if( !isset($_REQUEST['id'], $_REQUEST['piece']) || !is_numeric($_REQUEST['id']) || !$is_sync){
			throw new Exception("Paramètre invalide.");
		}

		if( ord_returns_set_piece($_REQUEST['id'],$_REQUEST['piece']) ){
			$result = true;
		}
		break;
}
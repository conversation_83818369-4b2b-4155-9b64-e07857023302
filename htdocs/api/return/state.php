<?php
/**
 * \defgroup api-return-state Etat
 * \ingroup api-return
 * @{
 * \page api-return-state-upd Mise à jour
 * 
 * Cette fonction modifie l'état du bon de retour.
 *
 *		\code
 *			PUT /return/state/
 *		\endcode
 *	
 * @param int $id Obligatoire, Identifiant du bon de retour
 * @param $state Obligatoire, Etat du bon de retour
 *	
 * @return true si la mise à jour s'est déroulée avec succès 
 * @}
*/

require_once('ord.returns.inc.php');

switch( $method ){
	case 'upd':

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : 0;

		if( !isset($_REQUEST['state']) || $id == 0 ){
			throw new Exception("Paramètre invalide.");
		}

		// log les données envoyé par la tablette
		api_log(json_encode($_REQUEST), 'api-states');

		// protection pour éviter un retour arrière sur le changement de status
		$old_state = ord_returns_get_state( $id );

		if( $old_state==$_REQUEST['state'] ){
			$result = true;
		}
		if( ord_returns_state_update( $id, $_REQUEST['state']) ){
			ord_returns_set_need_sync($id);
			$result = true;
		}

		// if (isset($config['dev_id']) && $config['dev_id']>0) {
		// 	if ($old_state == ORD_RETURNS_STATE_CREATE && $_REQUEST['state'] == ORD_RETURNS_STATE_QUERY) { 
		// 		serials_quantities_update(CLS_RETURN, $id);
		// 	} 
		// 	//the next code is to add quantities to seials
		// 	/*else if ($old_state == ORD_RETURNS_STATE_QUERY && $_REQUEST['state'] == ORD_RETURNS_STATE_CREATE) {
		// 		serials_quantities_update(CLS_RETURN, $id, true);
		// 	}*/
		// }

		break;
}
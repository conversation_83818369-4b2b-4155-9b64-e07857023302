<?php 
/**
 * \ingroup dam
 * @{		
*/
switch( $method ){
	/** @{@}
	 * \page api-documents-types-get Chargement
	 *
	 * Cette fonction permet de récupérer les types de documents
	 *
	 *	 \code
	 *		GET /documents/types/
	 *	 \endcode
	 *	
	 * @param int $id Facultatif, identifiant du type de document
	 *	
	 * @return le document sans l'entête de réponse en JSON en cas de succès.
	 * @}
	*/
	case 'get': 	

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : "";

		$types = doc_types_get( $id );
		if( $types && sizeof($types) ) {
			$result = true;
			$content = $types;
		}

		break;

	/** @{@}
 	 * @{
	 * \page api-documents-types-add Ajout
	 *
	 * Cette fonction ajoute un type de document
	 *
	 * \code
	 *		POST /documents/types/
	 * \endcode
	 * 	
	 * @param $name Obligatoire, nom du type du document
	 * @param $desc Facultatif, description du type du document
	 * @param $parent Facultatif, identifiant d'un type de document pour créer une hiérarchie
	 *	
	 * @return id, Identifiant du documents
	 * @}
	*/

	case 'add':
		if( !isset($_REQUEST['name']) ){
			throw new Exception("Paramètres invalide. (name)");
		}

		if( isset($_REQUEST['parent']) && !doc_types_exists($_REQUEST['parent']) ){
			throw new Exception("Paramètres invalide. (parent)");
		} else {
			$_REQUEST['parent']=0;
		}

		$_REQUEST['desc'] = isset($_REQUEST['desc']) ? $_REQUEST['desc'] : "";


		$result = false;
		$new_id = doc_types_add($_REQUEST['name'], $_REQUEST['desc'], $_REQUEST['parent'] );
		if ($new_id != null && $new_id > 0) {
			$content["id"] = $new_id;
			$result = true;
		}

		break;

}
/**
 * @}
*/
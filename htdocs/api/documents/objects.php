<?php
/**
 * \defgroup api-documents-objects The document objects 
 * \ingroup api-documents
 *
 * \page api-documents-objects-upd Mise à jour 	 
*/

// \cond onlyria
function save_data($doc_obj){

	// Add optionnal parameters fro check
	if(!isset($doc_obj["obj_id_1"])) $doc_obj['obj_id_1'] = 0;
	if(!isset($doc_obj["obj_id_2"])) $doc_obj['obj_id_2'] = 0;
	if(!isset($doc_obj["is_deleted"])) $doc_obj['is_deleted'] = 0;

	// Check parameters
	if (!isset($doc_obj["obj_id_0"]) || !is_numeric($doc_obj["obj_id_0"])) {
		throw new Exception("Missing or invalid parameters. (obj_id_0)");
	}
	$obj_id_0 = $doc_obj["obj_id_0"];

	if (!isset($doc_obj["obj_id_1"]) || !is_numeric($doc_obj["obj_id_1"])) {
		throw new Exception("Missing or invalid parameters. (obj_id_1)");
	}
	$obj_id_1 = $doc_obj["obj_id_1"];

	if (!isset($doc_obj["obj_id_2"]) || !is_numeric($doc_obj["obj_id_2"])) {
		throw new Exception("Missing or invalid parameters. (obj_id_2)");
	}
	$obj_id_2 = $doc_obj["obj_id_2"];

	if (!isset($doc_obj["cls_id"]) || !is_numeric($doc_obj["cls_id"])) {
		throw new Exception("Missing or invalid parameters. (cls_id)");
	}
	$cls_id = $doc_obj["cls_id"];

	if (!isset($doc_obj["doc_id"]) || !is_numeric($doc_obj["doc_id"])) {
		throw new Exception("Missing or invalid parameters. (doc_id)");
	}
	$doc_id = $doc_obj["doc_id"];

	if (!isset($doc_obj["is_deleted"]) || !is_numeric($doc_obj["is_deleted"])) {
		throw new Exception("Missing or invalid parameters. (is_deleted)");
	}
	$is_deleted = $doc_obj["is_deleted"];

	// Check identifier
	if ($obj_id_0 <= 0) {
		throw new Exception("Invalid identifier value. (obj_id_0)");
	}
	if ($obj_id_1 < 0) {
		throw new Exception("Invalid identifier value (obj_id_1)");
	}
	if ($obj_id_2 < 0) {
		throw new Exception("Invalid identifier value (obj_id_2)");
	}
	if ($cls_id <= 0) {
		throw new Exception("Invalid identifier value (cls_id)");
	}
	if ($doc_id <= 0) {
		throw new Exception("Invalid identifier value (doc_id)");
	}

	// Check action
	if (($is_deleted < 0) || ($is_deleted > 1)) {
		throw new Exception("Invalid parameter value (is_deleted)");
	}

	// Instantiate obj_id
	$obj_id = array( $obj_id_0 );
	if( $obj_id_2 > 0 ){
		$obj_id = array( $obj_id_0, $obj_id_1, $obj_id_2 );
	} else if( $obj_id_1 > 0 ){
		$obj_id = array( $obj_id_0, $obj_id_1 );
	}

    // Check if action add
	if ($is_deleted==0){
		// Create link between document an an object
		if (doc_objects_add($doc_id,$cls_id,$obj_id)) {
			// Link is created
			return true;
		}
	}

	// Check if action del
	if ($is_deleted==1){
		if (!doc_documents_exists( $doc_id )) {
			return true;
		}
		// Delete link between document an an object
		if (doc_objects_del($doc_id,$cls_id,$obj_id)) {
			// Link is deleted
			return true;
		}
	}
	
	
	return false;
}
// \endcond

switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-documents-object-upd Ajout
	 *
	 * Cette fonction permet d'ajouter ou de supprimer une liason avec un document 
	 *	
	 * \code
	 *		POST /documents/objects/
	 * \endcode
	 *	
	 * @param int $doc_id Obligatoire, identifiant de la classe 
	 * @param int $cls_id Obligatoire, identifiant du document
	 * @param $obj_id_0 Obligatoire, identifiant de l'objet champ 0
	 * @param $obj_id_1 facultatif, identifiant de l'objet champ 1
	 * @param $obj_id_2 facultatif, identifiant de l'objet champ 2
	 * 
	 * or un tableau de parametres pour ajouter plusieur liens
	 *	
	 * @return true en cas de success, sinon génère une exception
	 * @}
	*/
	case 'upd': // Update document object link
		// Check input parameters
		if( !isset($_REQUEST['obj_id_0'],$_REQUEST['cls_id'],$_REQUEST['doc_id']) || !is_numeric($_REQUEST['obj_id_0']) || !is_numeric($_REQUEST['cls_id']) || !is_numeric($_REQUEST['doc_id']) ){
			// mode "bulk"
			if( !is_array($objs) ){
				throw new Exception("Missing or invalid parameters");
			}

			foreach($objs as $doc_obj){
				if( !save_data($doc_obj) ){
					throw new Exception("Error adding link ".print_r($doc_obj, true));
				}
			}
			$result = true;

		}else{
			$result = save_data($_REQUEST);
		}

		break;
}
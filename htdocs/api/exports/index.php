<?php
// \cond onlyria
/**
 * \defgroup Exports
 * \ingroup sync
 * @{
 * \page api-exports-index-add Ajout
 *
 * Cette fonction permet d'ajouter un export
 * \code
 * 		POST /export/index/
 * \endcode
 *
 * @param cls_id Obligatoire, Classe de l'export
 * @param wst_id Facultatif, identifiant d'un site (par défaut récupère le site par défaut)
 * @param exp_id Facultatif, identifiant de l'export (table "exp_exports")
 *  Pour l'export de produits
 * @param cat Obligatoire, identifiant de la catégorie dans laquelle les produits doivent se trouver
 * @param catchilds Obligatoire, booléen indiquant si les produits des sous-catégories doivent être retournés (true) ou s'il ne faut prendre que les produits directement sous la catégorie $cat
 * @param cols Obligatoire, tableau contenant la liste des colonnes à inclure dans le fichier CSV
 * @param flds Obligatoire, tableau contenant la liste des champs avancés à inclure dans le fichier CSV
 * @param thumb Obligatoire, tableau contenant les formats d'images à exporter. Les clés à utiliser son "main" pour l'image principale, et "second" pour la liste des images secondaires. Dans les deux cas, le fichier contiendra les images de meilleure résolution.
 * @param childonly Obligatoire, booléen indiquant s'il faut retourner les produits enfants seulement (true) ou non (false, valeur par défaut)
 * @param no_html Obligatoire, booléen indiquant si les balises HTML doivent être retirées automatiquement des champs description (true) ou non (false, valeur par défaut)
 * @param brand Obligatoire, identifiant de la marque dans laquelle les produits doivent se trouver
 * @param lngs Obligatoire, tableau de code iso 2 des langues dont l'on veut récupérer la traduction
 * @param for_excel Facultatif, boolean (False par défaut)
 * @param for_mac Facultatif, boolean (False par défaut)
 *  Pour l'export de catégories
 * @param heads Obligatoire, Définit les colonnes que l'on va exporter
 * @param cat Obligatoire, Identifiant de la catégorie dont on va faire l'export. Par défaut 0 pour récupérer sur toutes les catégories.
 * @param recursive Obligatoire, Variable booléenne. True si l'on va chercher les sous-catégories, false sinon. Par défaut à true.
 * @param ar_image_sizes Obligatoire, Tableau des tailles des champs image sélectionnés. Par défaut c'est un tableau vide.
 * @param exclude_cat_ids Obligatoire, Tableau des identifiants des catégories à ne pas prendre en compte. Par défaut c'est un tableau vide.
 * @param header_row Obligatoire, Ligne d'entête du fichier d'export.
 * @param for_excel Facultatif, boolean (False par défaut)
 * @param for_mac Facultatif, boolean (False par défaut)
 * @param bool seo Optionnel, indique si oui ou non les références automatique et personnalisé sont inclus à l'export
 *  Pour l'export de commandes
 * @param key Optionnel, tableau contenant des identifiants de commande :
 *            		- id : identifiant d'une commande
 *            		- parent_id : identifiant de la commande parente
 *            		- ref : référence d'une commande
 *            		- piece : numéro de pièce d'une commande
 * @param filter Optionnel, tableau contenant les principaux filtres de récupération :
 *           		- is_web : true -> ne retourne que les commandes web, false -> ne retourne que les commandes gescom, null -> retourne toutes les commandes (par défaut à null)
 *           		- origin : filtre sur l'origine de commande
 *           		- wst_id : null -> retourne les commandes liées à aucun site, identifiant (ou tableau) d'un site (par défaut à 0, ignoré)
 *           		- pay_id : null -> retourne les commandes liées à aucun moyen de paiement, identifiant (ou tableau) d'un moyen de paiement (par défaut à 0, ignoré)
 *           		- seller_id : null -> retourne les commandes liées à aucun représentant, identifiant (ou tableau) d'un représentant (par défaut à false, ignoré)
 *           		- pmt_id : null -> retourne les commandes liées à aucune promotion, identifiant (ou tableau) d'une promotion (par défaut à false, ignoré)
 *           		- state_id : identifiant (ou tableau) d'un statut (par défaut à 0, ignoré)
 *           		- usr_id : identifiant (ou tableau) de compte utilisateur (par défaut à 0, ignoré)
 *           		- fld : tableau de recherche sur les champs avancés
 *           		- parent_id : identifiant de la commande parent
 *           		- total_ht : tableau sous cette forme :  array( 'min' => 0, 'max' => 10)
 * @param period  Optionnel, tableau contenant les dates de début et/ ou de fin :
 *           		- start : date de début (format anglais)
 *           		- end : date de fin (format anglai)
 *
 * @return true si l'ajout s'est bien déroulé
 * @}
*/
require_once("exports.inc.php");
require_once("products.inc.php");


switch( $method ){
	case 'add':
		if( !isset($_REQUEST['cls_id']) ){
			throw new BadFunctionCallException('L\'identifiant de la classe de l\'export est manquant.');
		}

		if( !isset($_REQUEST['exp_id']) ){
			throw new BadFunctionCallException('L\'identifiant de l\'export est manquant.');
		}

		if( !isset($_REQUEST['wst_id']) ){
			$_REQUEST['wst_id'] = false;
		}

		$r_export = exp_exports_get( $_REQUEST['exp_id'] , array(), 0, $_REQUEST['wst_id'] );
		if( !$r_export || !ria_mysql_num_rows($r_export) ){
			throw new RuntimeException('Erreur lors de la récupération de l\'export '.$_REQUEST['exp_id'].' sur le site '.$_REQUEST['wst_id']);
		}
		$export = ria_mysql_fetch_assoc( $r_export );

		switch( $_REQUEST['cls_id'] ){
			case CLS_PRODUCT :
			case CLS_CATEGORY : {
				$for_excel = isset($_REQUEST['for_excel']) && $_REQUEST['for_excel'];
				$for_mac = isset($_REQUEST['for_mac']) && $_REQUEST['for_mac'];
				$prd_ids = 0;
				if(count($_REQUEST['prd_ids']) != 0){
					$prd_ids = $_REQUEST['prd_ids'];
				}

				switch( $_REQUEST['cls_id'] ){
					case CLS_PRODUCT :{
						if( !isset($_REQUEST['cat']) ||
							!isset($_REQUEST['catchilds']) ||
							!isset($_REQUEST['cols']) ||
							!isset($_REQUEST['flds']) ||
							!isset($_REQUEST['thumb']) ||
							!isset($_REQUEST['childonly']) ||
							!isset($_REQUEST['no_html']) ||
							!isset($_REQUEST['brand']) ||
							!isset($_REQUEST['lngs'])
						){
							throw new BadFunctionCallException('Certains paramètres sont manquants pour l\'export de produits.');
						}

						$seo = isset($_REQUEST['seo']) && $_REQUEST['seo'];

						$content = prd_exports_generated( $_REQUEST['cat'], $_REQUEST['catchilds'], $_REQUEST['cols'], $_REQUEST['flds'], $_REQUEST['thumb'], $_REQUEST['childonly'], $_REQUEST['no_html'], $_REQUEST['brand'], $_REQUEST['lngs'], $_REQUEST['exp_id'], $seo, $prd_ids );
						break;
					}
					case CLS_CATEGORY :{
						if( !isset($_REQUEST['heads']) ||
							!isset($_REQUEST['cat']) ||
							!isset($_REQUEST['recursive']) ||
							!isset($_REQUEST['ar_image_sizes']) ||
							!isset($_REQUEST['exclude_cat_ids']) ||
							!isset($_REQUEST['header_row'])
						){
							throw new BadFunctionCallException('Certains paramètres sont manquants pour l\'export de catégories.');
						}


						$rows[] = implode(';', $_REQUEST['header_row']);
						$data_rows = prd_categories_export( $_REQUEST['heads'], $_REQUEST['cat'], '', $_REQUEST['recursive'], $_REQUEST['ar_image_sizes'], $_REQUEST['exclude_cat_ids'], $_REQUEST['exp_id'] );

						if(!$data_rows || !is_array($data_rows)){
							$error = _('Erreur dans l\'exportation des données.');
						} else {
							$rows = array_merge($rows, $data_rows);
						}

						$content = array();
						foreach($rows as $row){
							$content[] = explode(';', $row);
						}

						break;
					}
				}


				// Création du fichier contenant les données exporté
				$handle = fopen($export['file_path'], 'w');
				if( is_array($content) && sizeof($content) > 1 ){
					foreach( $content as $line ){
						if ($for_excel || $for_mac) {
							foreach ($line as $key => $data) {
								if ($for_mac) {
									$data = str_replace("\r\n", " ", $data);
									$data = preg_replace('/[ ]{2,}/', ' ', $data);
									$line[$key]  = (is_string($data)) ? iconv("UTF-8", "macintosh", $data) : $data;
								}else{
									$line[ $key ] = (is_string($data)) ? iconv("UTF-8", "ISO-8859-1//TRANSLIT", $data) : $data;
								}
							}
						}

						fputcsv($handle, $line, ";");
					}
					exp_exports_set_finished( $export['id'] );
				}else{
					exp_exports_set_error( $export['id'], 'Erreur dans l\'exportation des données.' );
					throw new RuntimeException('Erreur lors de la génération de l\'export '.$_REQUEST['exp_id']);
				}
				fclose($handle);
				break;
			}
			case CLS_ORDER : {
				if( !isset($_REQUEST['key']) ){
					$_REQUEST['key'] = array();
				}

				if( !isset($_REQUEST['filter']) ){
					$_REQUEST['filter'] = array();
				}

				if( !isset($_REQUEST['period']) ){
					$_REQUEST['period'] = array();
				}

				$orders = array();
				$r_orders = ord_orders_get_simple($_REQUEST['key'], $_REQUEST['period'], $_REQUEST['filter']);

				if( !$r_orders || !ria_mysql_num_rows($r_orders) ){
					exp_exports_set_error( $export['id'], 'Erreur dans l\'exportation des données.' );
					throw new RuntimeException('Erreur lors de la génération de l\'export '.$_REQUEST['exp_id']);
				}

				while( $order = ria_mysql_fetch_assoc($r_orders) ){
					$orders[] = $order;
				}

				$nb_orders = count($orders);

				exp_exports_upd( $export['id'], 'processing', $nb_orders, 0 );

				require_once('excel/PHPExcel.php');

				if( isset($config['admin_export_order_version']) && $config['admin_export_order_version'] == 'v2' ){
					$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ','BA','BB','BC','BD','BE','BF','BG','BH','BI','BJ','BK','BL','BM','BN','BO','BP','BQ','BR','BS','BT','BU','BV','BW','BX','BY','BZ','CA','CB','CC','CD','CE','CF','CG','CH','CI','CJ','CK','CL','CM','CN','CO','CP','CQ','CR','CS','CT','CU','CV','CW','CX','CY','CZ','DA','DB','DC','DD','DE','DF','DG','DH','DI','DJ','DK','DL','DM','DN','DO','DP','DQ','DR','DS','DT','DU','DV','DW','DX','DY','DZ','EA','EB','EC','ED','EE','EF','EG','EH','EI','EJ','EK','EL','EM','EN','EO','EP','EQ','ER','ES','ET','EU','EV','EW','EX','EY','EZ','FA','FB','FC','FD','FE','FF','FG','FH','FI','FJ','FK','FL','FM','FN','FO','FP','FQ','FR','FS','FT','FU','FV','FW','FX','FY','FZ','GA','GB','GC','GD','GE','GF','GG','GH','GI','GJ','GK','GL','GM','GN','GO','GP','GQ','GR','GS','GT','GU','GV','GW','GX','GY','GZ'); // A -> GZ = 200 élèments

					// Création de l'objet PHPExcel
					$objPHPExcel = new PHPExcel();

					// Détermine les propriétés de la feuille Excel
					$objPHPExcel->getProperties()->setCreator("riaStudio")
											->setLastModifiedBy("riaStudio")
											->setTitle(_("Export des commandes"))
											->setSubject(_("Export des commandes"))
											->setDescription(_("Export des commandes"))
											->setKeywords("export commandes")
											->setCategory("");

					// Création du fichier
					$objWorksheet = $objPHPExcel->getActiveSheet();

					// 1° feuille : Contient l'entête des commande
					$objWorksheet->setTitle(_('Commandes'));

					// 2° feuille : Contient les lignes de commandes
					$objWorksheet2 = $objPHPExcel->createSheet();
					$objWorksheet2->setTitle(_('Lignes de commandes'));

					// 1° feuille : Nom des colonnes
					$objWorksheet->setCellValue('A1', _('Nom de l\'Entrepôt'));
					$objWorksheet->mergeCells('A1:A2');
					$objWorksheet->getColumnDimension('A')->setWidth(25);

					$objWorksheet->setCellValue('B1', _('Date de la Commande'));
					$objWorksheet->mergeCells('B1:B2');
					$objWorksheet->getColumnDimension('B')->setWidth(25);

					$objWorksheet->setCellValue('C1', _('Code Client'));
					$objWorksheet->setCellValue('C2', _('Code SPF - Code Logista'));
					$objWorksheet->getColumnDimension('C')->setWidth(25);

					$objWorksheet->setCellValue('D1', _('Nom Client'));
					$objWorksheet->mergeCells('D1:D2');
					$objWorksheet->getColumnDimension('D')->setWidth(25);

					$objWorksheet->setCellValue('E1', _('Prospect'));
					$objWorksheet->mergeCells('E1:E2');
					$objWorksheet->getColumnDimension('E')->setWidth(25);

					$objWorksheet->setCellValue('F1', _('Pièce Jointe'));
					$objWorksheet->mergeCells('F1:F2');
					$objWorksheet->getColumnDimension('F')->setWidth(25);

					$objWorksheet->setCellValue('G1', _('Type de Vente'));
					$objWorksheet->mergeCells('G1:G2');
					$objWorksheet->getColumnDimension('G')->setWidth(25);

					$objWorksheet->setCellValue('H1', _('Pack'));
					$objWorksheet->mergeCells('H1:H2');
					$objWorksheet->getColumnDimension('H')->setWidth(25);

					$objWorksheet->setCellValue('I1', _('Type de meuble'));
					$objWorksheet->mergeCells('I1:I2');
					$objWorksheet->getColumnDimension('I')->setWidth(40);

					$cat_furniture = 238649; // @var integer $cat_furniture Identifiant de la catégorie MEUBLES pour ?
					$cat_yuto = 236795;
					$other_params = [
						'exclude_cat' 			=> $cat_furniture,
						'exclude_cat_children'	=> true
					];
					$prds = prd_products_get_simple(0, '', false, $cat_yuto, true, false, false, false, $other_params);
					$col = 9; // Point de départ de la colonne (J)
					$colName = $colName1 = $colName2 = null; // Var init
					$limit = count($alphabet) - 1; // On limite le nombre de colonnes dynamique à la taille du tableau $alphabet écrit en dur
					$colName = ""; // var init / Nom de la cellule courante
					$prdArr = []; // array init / stockage des références de produits dans l'ordre de publication
					$ar_cats_id = prd_categories_childs_get_array($cat_furniture); // @var array $ar_cats_id tableau des ids des catégories enfants de la catégorie MEUBLES 238649
					$ar_cats_id[] = $cat_furniture;

					while( $prd = ria_mysql_fetch_assoc($prds) ){

						// Si le produit est un produit MEUBLE (vérification double)
						if( prd_products_categories_exists($prd['id'], $ar_cats_id) ){
							continue;
						}

						// Si le nombre d'article dépasse la limite de colonnes, on arrête la boucle
						if($col > $limit){
							break;
						}
						// Position des cellules et titre des colonnes
						$colName = $alphabet[$col];
						$colName1 = $alphabet[$col]."1";
						$colName2 = $alphabet[$col]."1:".$alphabet[$col]."2";
						$item = $prd['title'];
						$prdArr[] = $prd['ref'];
						// Génération des cellules
						$objWorksheet->setCellValue($colName1, $item);
						$objWorksheet->mergeCells($colName2);
						$objWorksheet->getColumnDimension($colName)->setWidth(40);
						$col++;
					}

					// Représentant
					$objWorksheet->setCellValue($colName1, _('Représentant'));
					$objWorksheet->mergeCells($colName2);
					$objWorksheet->getColumnDimension($colName)->setWidth(40);

					// 2° feuille : Nom des colonnes
					$objWorksheet2->setCellValue('A1', _('Référence RiaShop'));
					$objWorksheet2->getColumnDimension('A')->setWidth(18);

					$objWorksheet2->setCellValue('B1', _('Référence GESCOM'));
					$objWorksheet2->getColumnDimension('B')->setWidth(18);

					$objWorksheet2->setCellValue('C1', _('Référence'));
					$objWorksheet2->getColumnDimension('C')->setWidth(12);

					$objWorksheet2->setCellValue('D1', _('Désignation'));
					$objWorksheet2->getColumnDimension('D')->setWidth(80);

					$objWorksheet2->setCellValue('E1', _('Marque'));
					$objWorksheet2->getColumnDimension('E')->setWidth(12);

					$objWorksheet2->setCellValue('F1', _('Prix HT'));
					$objWorksheet2->getColumnDimension('F')->setWidth(11);

					$objWorksheet2->setCellValue('G1', _('Quantité'));
					$objWorksheet2->getColumnDimension('G')->setWidth(11);

					$objWorksheet2->setCellValue('H1', _('Total HT'));
					$objWorksheet2->getColumnDimension('H')->setWidth(13);

					//Police sur l'entête des colonnes
					$objWorksheet->getStyle('A1:'.$colName.'2')->getFont()->setBold(true);
					$objWorksheet->getStyle('A1:'.$colName.'2')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
					$objWorksheet->getStyle('A1:'.$colName.'2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
					$objWorksheet->getStyle('A1:'.$colName.'2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet->getStyle('A1:'.$colName.'2')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$objWorksheet2->getStyle('A1:H1')->getFont()->setBold(true);
					$objWorksheet2->getStyle('A1:H1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
					$objWorksheet2->getStyle('A1:H1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
					$objWorksheet2->getStyle('A1:H1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet2->getStyle('A1:H1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$ar_payments = ord_payment_types_get_array();
					$ar_states = ord_states_get_array();

					// Enregistre les entêtes de commandes
					$line = 3; $firstline = 3;
					$lineprd = 2; $firstprdline = 2;
					$cursor = 0;
					foreach( $orders as $order ){ // On traitre chaque commande ligne par ligne
						$cursor++;
						// Met à jour le nombre de ligne traité de l'export à chaque tranche de 10% de commande traités
						if( $export['id'] ){
							if( $nb_orders > 20 ){
								if( !($cursor%($nb_orders/10)) ){
									exp_exports_upd( $export['id'], '', $nb_orders, $cursor );
								}
							}
						}

						/* Commandes  */
						// Nom de l'entrepôt (A)
						$ar_deposits = prd_deposits_get_array($config['tnt_id']);
						$dps = null;
						foreach( $ar_deposits as $deposit ){
							if( $deposit['id']==$order['dps_id']){
								$dps = $deposit['name'];
							}
						}
						$objWorksheet->setCellValue('A'.$line, $dps);

						// Date de la commande (B)
						$time = date("d/m/Y", strtotime($order['date']));
						$objWorksheet->setCellValue('B'.$line, $time);

						// Code client (colonne C)
						$custInfo = gu_users_get($order['usr_id']);
						if( $custInfo && ria_mysql_num_rows($custInfo) ){
							$custCode = ria_mysql_fetch_assoc_all($custInfo);
							$res = $custCode[0]['ref']; // [0] car le tableau principal est imbriqué dans un tableau le contenant
						}else{
							$res = "Code inconnu";
						}
						$objWorksheet->setCellValue('C'.$line, $res);

						// Nom du client (D)
						$ord_adr = ord_orders_address_load($order);
						if($ord_adr['delivery']['society']){
							$objWorksheet->setCellValue('D'.$line, $ord_adr['delivery']['society']);
						}else{
							$objWorksheet->setCellValue('D'.$line, $ord_adr['delivery']['title_name']." ".$ord_adr['delivery']['lastname']." ".$ord_adr['delivery']['firstname']);
						}

						// Prospect (colonne E) (Choix unique)
						$prospect = fld_object_values_get($order['id'], 101777);
						if( $prospect !== false ){
							$objWorksheet->setCellValue('E'.$line, $prospect);
						}else{
							$objWorksheet->setCellValue('E'.$line, _('-'));
						}

						// Pièce jointe (colonne F)
						if( $prospect ){ // Si le client est prospect, on affiche le lien vers le RIB qui correspond à l'image principale
							$iban = fld_object_values_get($order['id'], 101779);
							if( $iban !== false ){
								$objWorksheet->setCellValue('F'.$line, _('RIB'));
								$objWorksheet->getCell('F'.$line)->getHyperlink()->setUrl('https://app.riashop.fr/admin/documents/images/download.php?image='.$iban);
							}else{
								$objWorksheet->setCellValue('F'.$line, _('Pas de RIB'));
							}
						}else{
							$objWorksheet->setCellValue('F'.$line, '-');
						}

						// Type de vente (colonne G) (Choix unique)
						$saleType = fld_object_values_get($order['id'], 101778);
						if( $saleType !== false ){
							$objWorksheet->setCellValue('G'.$line, $saleType);
						}else{
							$objWorksheet->setCellValue('G'.$line, _('-'));
						}

						// Pack (colonne H)
						$packType = fld_object_values_get($order['id'], 101786);
						if( $packType !== false ){
							$objWorksheet->setCellValue('H'.$line, $packType);
						}else{
							$objWorksheet->setCellValue('H'.$line, _('-'));
						}

						// On récupère le détail de chaque commande par ligne afin d'exploiter le résultat pour la colonne 'Type de meuble' (I)
						// et celles des produits (à partir de J)
						$r_ord_prds = ord_products_get($order['id']);
						$ar_prds_refs = [];
						$ord_prd_refs_furniture = '';
						if( ria_mysql_num_rows($r_ord_prds) ){
							while( $ord_prd = ria_mysql_fetch_assoc($r_ord_prds) ){
								// Lignes des produits en fonction des commandes dans TAB 2
								$objWorksheet2->setCellValue('A'.$lineprd, $order['id']);
								$objWorksheet2->setCellValue('B'.$lineprd, $order['piece']);
								$objWorksheet2->setCellValue('C'.$lineprd, $ord_prd['ref']);
								$objWorksheet2->setCellValue('D'.$lineprd, $ord_prd['title']);
								$objWorksheet2->setCellValue('E'.$lineprd, $ord_prd['brd_name']);
								$objWorksheet2->setCellValue('F'.$lineprd, number_format( $ord_prd['price_ht'], 2, '.', '' ));
								$objWorksheet2->setCellValue('G'.$lineprd, number_format( $ord_prd['qte'], 0, '', ' ' ));
								$objWorksheet2->setCellValue('H'.$lineprd, number_format( $ord_prd['total_ht'], 2, '.', '' ));
								$lineprd++;

								// Colonne TYPE DE MEUBLE
								if( prd_products_categories_exists($ord_prd['id'], $ar_cats_id) ){
									$ord_prd_refs_furniture .= $ord_prd['name'].', ';
									continue;
								}
								// Autres produits ne figurant pas dans TYPE DE MEUBLE
								$ar_prds_refs[$ord_prd['ref']] = $ord_prd;
							}
						}
						$ord_prd_refs_furniture = trim($ord_prd_refs_furniture, ', ');
						$ord_prd_refs_furniture = $ord_prd_refs_furniture == '' ? '-' : $ord_prd_refs_furniture;
						// Type de meuble (colonne I)
						$objWorksheet->setCellValue('I'.$line, $ord_prd_refs_furniture);

						// Quantité d'articles par commande (colonne J à avant-dernière)
						$col = 9; // index de la colonne de départ
						// On itère chaque colonne produit
						for( $i = 0; $i < (count($prdArr)-1); $i++ ){ // -1 pour réserver la dernière colonne au représentant
							$prd_ref = $prdArr[$i];
							$qte = 0;

							// Vérifie si la référence existe dans la commande, si oui on set la quantité commandée
							if( isset($ar_prds_refs[$prd_ref]) && is_array($ar_prds_refs[$prd_ref]) ){
								$qte = $ar_prds_refs[$prd_ref]['qte'];
							}
							// On rempli la cellule et on passe à la colonne suivante
							$colRef = $alphabet[$col];
							$objWorksheet->setCellValue($colRef.$line, $qte);
							$col++;
						}

						// Nom du représentant (dernière colonne)
						$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, (isset($order['seller_id']) && is_numeric($order['seller_id']) ? $order['seller_id'] : true));
						$seller_name = _('Inconnu');
						if( $r_seller && ria_mysql_num_rows($r_seller)){
							$seller = ria_mysql_fetch_assoc($r_seller);
							$seller_name = $seller['adr_lastname'].' '.$seller['adr_firstname'];
						}
						$objWorksheet->setCellValue($colName.$line, $seller_name);
						$line++;
					}

					//Mise en page des cellules
					$objWorksheet->getStyle('A'.$line.':'.$colName.$line)->getFont()->setBold(true);
					$objWorksheet->getStyle('A'.$line.':'.$colName.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
					$objWorksheet->getStyle('A'.$line.':'.$colName.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
					$objWorksheet->getStyle('A'.$line.':'.$colName.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
					$objWorksheet->getStyle('A'.$line.':'.$colName.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$lineprd--; $line--;

					// Gestion des alignements
					$objWorksheet->getStyle('A' . $firstline . ':'.$colName.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
					$objWorksheet->getStyle('A' . $firstline . ':'.$colName.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

					$objWorksheet2->getStyle('A' . $firstprdline . ':H'.$lineprd)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
					$objWorksheet2->getStyle('A' . $firstprdline . ':B'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet2->getStyle('C' . $firstprdline . ':E'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
					$objWorksheet2->getStyle('F' . $firstprdline . ':H'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

					$objWorksheet2->getStyle('F' . $firstprdline . ':F'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
					$objWorksheet2->getStyle('H' . $firstprdline . ':H'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');

					// Bordure
					$objWorksheet->getStyle('A1:'.$colName.$line)->getBorders()->applyFromArray(
						array(
							'allborders' => array(
								'style' => PHPExcel_Style_Border::BORDER_THIN,
							)
						)
					);
					$objWorksheet2->getStyle('A1:H'.$lineprd)->getBorders()->applyFromArray(
						array(
							'allborders' => array(
								'style' => PHPExcel_Style_Border::BORDER_THIN,
							)
						)
					);

					$objPHPExcel->setActiveSheetIndex(0);
				}else{
					$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

					// Création de l'objet PHPExcel
					$objPHPExcel = new PHPExcel();

					// Déterminé les propriétés de la feuille Excel
					$objPHPExcel->getProperties()->setCreator("riaStudio")
													->setLastModifiedBy("riaStudio")
													->setTitle(_("Export des commandes"))
													->setSubject(_("Export des commandes"))
													->setDescription(_("Export des commandes"))
													->setKeywords("export commandes")
													->setCategory("");

					// Création du fichier
					$objWorksheet = $objPHPExcel->getActiveSheet();

					// 1° feuille : Contient l'entête des commande
					$objWorksheet->setTitle(_('Commandes'));

					// 2° feuille : Contient les lignes de commandes
					$objWorksheet2 = $objPHPExcel->createSheet();
					$objWorksheet2->setTitle(_('Lignes de commandes'));

					// 1° feuille : Nom des colonnes
					$objWorksheet->setCellValue('A1', _('Référence RiaShop'));
					$objWorksheet->mergeCells('A1:A2');
					$objWorksheet->getColumnDimension('A')->setWidth(18);

					$objWorksheet->setCellValue('B1', _('Référence GESCOM'));
					$objWorksheet->mergeCells('B1:B2');
					$objWorksheet->getColumnDimension('B')->setWidth(18);
					$objWorksheet->getColumnDimension('A')->setWidth(18);

					$objWorksheet->setCellValue('C1', _('Référence commande'));
					$objWorksheet->mergeCells('C1:C2');
					$objWorksheet->getColumnDimension('C')->setWidth(18);

					$objWorksheet->setCellValue('D1', _('Référence représentant'));
					$objWorksheet->mergeCells('D1:D2');
					$objWorksheet->getColumnDimension('D')->setWidth(18);

					$objWorksheet->setCellValue('E1', _('Référence compte client'));
					$objWorksheet->mergeCells('E1:E2');
					$objWorksheet->getColumnDimension('E')->setWidth(18);

					$objWorksheet->setCellValue('F1', _('Date'));
					$objWorksheet->mergeCells('F1:F2');
					$objWorksheet->getColumnDimension('F')->setWidth(18);

					$objWorksheet->setCellValue('G1', _('Représentant de la commande'));
					$objWorksheet->mergeCells('G1:H1');

					$objWorksheet->setCellValue('G2', _('Nom'));
					$objWorksheet->getColumnDimension('G')->setWidth(25);

					$objWorksheet->setCellValue('H2', _('Prénom'));
					$objWorksheet->getColumnDimension('H')->setWidth(25);

					$objWorksheet->setCellValue('I1', _('Adresse de facturation'));
					$objWorksheet->mergeCells('I1:R1');

					$objWorksheet->setCellValue('I2', _('Société'));
					$objWorksheet->getColumnDimension('I')->setWidth(25);

					$objWorksheet->setCellValue('J2', _('Civilité'));
					$objWorksheet->getColumnDimension('J')->setWidth(15);

					$objWorksheet->setCellValue('K2', _('Nom'));
					$objWorksheet->getColumnDimension('K')->setWidth(25);

					$objWorksheet->setCellValue('L2', _('Prénom'));
					$objWorksheet->getColumnDimension('L')->setWidth(25);

					$objWorksheet->setCellValue('M2', _('Adresse'));
					$objWorksheet->mergeCells('M2:N2');
					$objWorksheet->getColumnDimension('M')->setWidth(35);
					$objWorksheet->getColumnDimension('N')->setWidth(35);

					$objWorksheet->setCellValue('O2', _('Code postal'));
					$objWorksheet->getColumnDimension('O')->setWidth(15);

					$objWorksheet->setCellValue('P2', _('Ville'));
					$objWorksheet->getColumnDimension('P')->setWidth(25);

					$objWorksheet->setCellValue('Q2', _('Pays'));
					$objWorksheet->getColumnDimension('Q')->setWidth(25);

					$objWorksheet->setCellValue('R2', _('Email'));
					$objWorksheet->getColumnDimension('R')->setWidth(35);

					$objWorksheet->setCellValue('S1', _('Adresse de livraison'));
					$objWorksheet->mergeCells('S1:AA1');

					$objWorksheet->setCellValue('S2', _('Société'));
					$objWorksheet->getColumnDimension('S')->setWidth(25);

					$objWorksheet->setCellValue('T2', _('Civilité'));
					$objWorksheet->getColumnDimension('T')->setWidth(15);

					$objWorksheet->setCellValue('U2', _('Nom'));
					$objWorksheet->getColumnDimension('U')->setWidth(25);

					$objWorksheet->setCellValue('V2', _('Prénom'));
					$objWorksheet->getColumnDimension('V')->setWidth(25);

					$objWorksheet->setCellValue('W2', _('Adresse'));
					$objWorksheet->mergeCells('W2:X2');
					$objWorksheet->getColumnDimension('W')->setWidth(35);
					$objWorksheet->getColumnDimension('X')->setWidth(35);

					$objWorksheet->setCellValue('Y2', _('Code postal'));
					$objWorksheet->getColumnDimension('Y')->setWidth(15);

					$objWorksheet->setCellValue('Z2', _('Ville'));
					$objWorksheet->getColumnDimension('Z')->setWidth(25);

					$objWorksheet->setCellValue('AA2', _('Pays'));
					$objWorksheet->getColumnDimension('AA')->setWidth(25);

					if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
						if( !isset($_GET['state']) || !$_GET['state'] ) {
							$objWorksheet->setCellValue('AB1', _('Statut'));
							$objWorksheet->mergeCells('AB1:AB2');
						}
					}

					$objWorksheet->setCellValue('AC1', _('Moyen de paiement'));
					$objWorksheet->getColumnDimension('AC')->setWidth(25);
					$objWorksheet->mergeCells('AC1:AC2');

					$objWorksheet->setCellValue('AD1', _('Relance panier ?'));
					$objWorksheet->getColumnDimension('AD')->setWidth(15);
					$objWorksheet->mergeCells('AD1:AD2');

					$objWorksheet->setCellValue('AE1', _('Total HT'));
					$objWorksheet->getColumnDimension('AE')->setWidth(15);
					$objWorksheet->mergeCells('AE1:AE2');

					$objWorksheet->setCellValue('AF1', _('Total TTC'));
					$objWorksheet->getColumnDimension('AF')->setWidth(15);
					$objWorksheet->mergeCells('AF1:AF2');

					// 1° feuille : Si l'on connait le status des commandes, on masque la colonne U
					if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
						$objWorksheet->getColumnDimension('AB')->setWidth( isset($_GET['state']) && $_GET['state']>0 ? 0 : 23 );
					}else{
						$objWorksheet->getColumnDimension('AB')->setWidth( 0 );
					}

					// 2° feuille : Nom des colonnes
					$objWorksheet2->setCellValue('A1', _('Référence RiaShop'));
					$objWorksheet2->getColumnDimension('A')->setWidth(18);

					$objWorksheet2->setCellValue('B1', _('Référence GESCOM'));
					$objWorksheet2->getColumnDimension('B')->setWidth(18);

					$objWorksheet2->setCellValue('C1', _('Référence'));
					$objWorksheet2->getColumnDimension('C')->setWidth(12);

					$objWorksheet2->setCellValue('D1', _('Désignation'));
					$objWorksheet2->getColumnDimension('D')->setWidth(80);

					$objWorksheet2->setCellValue('E1', _('Marque'));
					$objWorksheet2->getColumnDimension('E')->setWidth(12);

					$objWorksheet2->setCellValue('F1', _('Prix HT'));
					$objWorksheet2->getColumnDimension('F')->setWidth(11);

					$objWorksheet2->setCellValue('G1', _('Quantité'));
					$objWorksheet2->getColumnDimension('G')->setWidth(11);

					$objWorksheet2->setCellValue('H1', _('Total HT'));
					$objWorksheet2->getColumnDimension('H')->setWidth(13);

					// Police sur l'entête des colonnes
					$objWorksheet->getStyle('A1:AF2')->getFont()->setBold(true);
					$objWorksheet->getStyle('A1:AF2')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
					$objWorksheet->getStyle('A1:AF2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
					$objWorksheet->getStyle('A1:AF2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet->getStyle('A1:AF2')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$objWorksheet2->getStyle('A1:H1')->getFont()->setBold(true);
					$objWorksheet2->getStyle('A1:H1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
					$objWorksheet2->getStyle('A1:H1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
					$objWorksheet2->getStyle('A1:H1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet2->getStyle('A1:H1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$ar_payments = ord_payment_types_get_array();
					$ar_states = ord_states_get_array();

					// Enregistre les entêtes de commandes
					$line = 3; $firstline = 3;
					$lineprd = 2; $firstprdline = 2;
					$cursor = 0;
					foreach( $orders as $order ){
						$cursor++;
						// Met à jour le nombre de lignes traitées de l'export à chaque tranche de 10% de commande traitées
						if( $export['id'] ){
							if( $nb_orders > 20 ){
								if( !($cursor%($nb_orders/10)) ){
									exp_exports_upd( $export['id'], '', $nb_orders, $cursor );
								}
							}
						}

						// Récupère l'information du champ avancé 'Commande relancé par email'
						$notified = fld_object_values_get($order['id'], _FLD_ORD_CART_NOTIFY);
						if( in_array($notified, array('Oui', 'oui', '1')) ){
							$notified = 'Oui';
						}else{
							$notified = 'Non';
						}

						$user_ref = gu_users_get_ref( $order['usr_id'], true );
						if( trim($user_ref) == '' ){
							$user_ref = $order['usr_id'];
						}

						$objWorksheet->setCellValue('A'.$line, $order['id'] );
						$objWorksheet->setCellValue('B'.$line, $order['piece'] );
						$objWorksheet->setCellValue('C'.$line, $order['ref'] );
						$objWorksheet->setCellValue('D'.$line, $order['seller_id'] );
						$objWorksheet->setCellValue('E'.$line, $user_ref );
						$objWorksheet->setCellValue('F'.$line, $order['date'] );

                        $ord_adr = ord_orders_address_load($order, false);
                        if ($ord_adr === false) {
                            $ord_adr = ord_orders_address_load($order);
                        }

						//Représentant
						if ($order['seller_id']){
							$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $order['seller_id']);

							if ($r_seller && ria_mysql_num_rows($r_seller)){
								$seller = ria_mysql_fetch_assoc($r_seller);

								$objWorksheet->setCellValue('G' . $line, $seller['adr_lastname']);
								$objWorksheet->setCellValue('H' . $line, $seller['adr_firstname']);
							}
						}

						// Adresse de facuration
						if( $ord_adr['invoice']['type_id']==1 ){
							$objWorksheet->setCellValue('J' . $line, $ord_adr['invoice']['title_name']);
							$objWorksheet->setCellValue('K' . $line, $ord_adr['invoice']['lastname']);
							$objWorksheet->setCellValue('L' . $line, $ord_adr['invoice']['firstname']);
						}else{
							$objWorksheet->setCellValue('I' . $line, $ord_adr['invoice']['society']);
						}

						$objWorksheet->setCellValue('M' . $line, $ord_adr['invoice']['address1']);
						$objWorksheet->setCellValue('N' . $line, $ord_adr['invoice']['address2']);
						$objWorksheet->setCellValue('O' . $line, $ord_adr['invoice']['postal_code']);
						$objWorksheet->setCellValue('P' . $line, $ord_adr['invoice']['city']);
						$objWorksheet->setCellValue('Q' . $line, $ord_adr['invoice']['country']);

						if (isset($order['usr_id'])) {
							$email = gu_users_get_email($order['usr_id']);
							if ($email) {
								$objWorksheet->setCellValue('R' . $line, $email);
							}
						}

						// Adresse de livraison
						$objWorksheet->setCellValue('S'.$line, $ord_adr['delivery']['society'] );
						$objWorksheet->setCellValue('T'.$line, $ord_adr['delivery']['title_name'] );
						$objWorksheet->setCellValue('U'.$line, $ord_adr['delivery']['lastname'] );
						$objWorksheet->setCellValue('V'.$line, $ord_adr['delivery']['firstname'] );
						$objWorksheet->setCellValue('W'.$line, $ord_adr['delivery']['address1'] );
						$objWorksheet->setCellValue('X'.$line, $ord_adr['delivery']['address2'] );
						$objWorksheet->setCellValue('Y'.$line, $ord_adr['delivery']['postal_code'] );
						$objWorksheet->setCellValue('Z'.$line, $ord_adr['delivery']['city'] );
						$objWorksheet->setCellValue('AA'.$line, $ord_adr['delivery']['country'] );

						if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
							$objWorksheet->setCellValue('AB'.$line, $ar_states[$order['state_id']]['name'] );
						}

						$objWorksheet->setCellValue('AC'.$line, ($order['pay_id'] ? $ar_payments[$order['pay_id']]['name'] : '') );
						$objWorksheet->setCellValue('AD'.$line, $notified );

						if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
							$txt_total_ht = '';
							$txt_total_ttc = '';

							$ar_totals = ord_products_get_totals_by_currencies( $order['id'] );
							if( is_array($ar_totals) ){
								foreach( $ar_totals as $one_total_line ){
									if( trim($txt_total_ht) != '' ){
										$txt_total_ht .= ' / ';
									}

									$txt_total_ht .= ria_number_format( $one_total_line['total_ht'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );

									if( trim($txt_total_ttc) != '' ){
										$txt_total_ttc .= ' / ';
									}

									$txt_total_ttc .= ria_number_format( $one_total_line['total_ttc'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );
								}
							}

							$objWorksheet->setCellValue('AE'.$line, $txt_total_ht );
							$objWorksheet->setCellValue('AF'.$line, $txt_total_ttc );
						}else{
							$objWorksheet->setCellValue('AE'.$line, number_format( $order['total_ht'], 2, '.', '' ) );
							$objWorksheet->setCellValue('AF'.$line, number_format( $order['total_ttc'], 2, '.', '' ) );
						}

						// Enregistre les lignes de commandes
						$rpord = ord_products_get( $order['id'] );
						if( $rpord && ria_mysql_num_rows($rpord) ){
							while( $pord = ria_mysql_fetch_assoc($rpord) ){
								if( !is_numeric($pord['id']) || $pord['id'] <= 0 ){
									continue;
								}

								if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
									$price_ht = ria_number_format($pord['price_ht'], NumberFormatter::CURRENCY, 2, $pord['currency']);
									$total_ht = ria_number_format($pord['total_ht'], NumberFormatter::CURRENCY, 2, $pord['currency']);
								}else{
									$price_ht = number_format( $pord['price_ht'], 2, '.', '' );
									$total_ht = number_format( $pord['total_ht'], 2, '.', '' );
								}

								$objWorksheet2->setCellValue('A'.$lineprd, $order['id']);
								$objWorksheet2->setCellValue('B'.$lineprd, $order['piece']);
								$objWorksheet2->setCellValue('C'.$lineprd, $pord['ref']);
								$objWorksheet2->setCellValue('D'.$lineprd, $pord['title']);
								$objWorksheet2->setCellValue('E'.$lineprd, $pord['brd_name']);
								$objWorksheet2->setCellValue('F'.$lineprd, $price_ht);
								$objWorksheet2->setCellValue('G'.$lineprd, number_format( $pord['qte'], 0, '', ' ' ));
								$objWorksheet2->setCellValue('H'.$lineprd, $total_ht);
								$lineprd++;

							}
						}

						$objWorksheet->getStyle('F'.$line)->getAlignment()->setWrapText(true);
						$objWorksheet->getStyle('G'.$line)->getAlignment()->setWrapText(true);

						$line++;
					}

					if( !isset($config['ord_multi_currency']) || !$config['ord_multi_currency'] ){
						// Mise en place des totaux finaux
						$objWorksheet->mergeCells('A'.$line.':AC'.$line);
						$objWorksheet->setCellValue('A'.$line, _('Total :') );
						$objWorksheet->setCellValue('AE'.$line, '=SUM(AE' . $firstline. ':AE'.($line-1).')' );
						$objWorksheet->setCellValue('AF'.$line, '=SUM(AF' . $firstline . ':AF'.($line-1).')' );
						$objWorksheet->getStyle('A'.$line.':AF'.$line)->getBorders()->applyFromArray(
							array(
								'allborders' => array(
									'style' => PHPExcel_Style_Border::BORDER_THIN,
								)
							)
						);
						$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->setBold(true);
						$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
						$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
						$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
						$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
					}

					$lineprd--; $line--;

					// Gestion des alignements
					$objWorksheet->getStyle('A' . $firstline . ':AF'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
					$objWorksheet->getStyle('A' . $firstline . ':F'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet->getStyle('AE' . $firstline . ':AE'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
					$objWorksheet->getStyle('AF' . $firstline . ':AF'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

					$objWorksheet2->getStyle('A' . $firstprdline . ':H'.$lineprd)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
					$objWorksheet2->getStyle('A' . $firstprdline . ':B'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet2->getStyle('C' . $firstprdline . ':E'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
					$objWorksheet2->getStyle('F' . $firstprdline . ':H'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

					// Format de données
					if( !isset($config['ord_multi_currency']) || !$config['ord_multi_currency'] ){
						$objWorksheet->getStyle('AE' . $firstline . ':AE'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');
						$objWorksheet->getStyle('AF' . $firstline . ':AF'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');

						$objWorksheet2->getStyle('F' . $firstprdline . ':F'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
						$objWorksheet2->getStyle('H' . $firstprdline . ':H'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
					}

					// Bordure
					$objWorksheet->getStyle('A1:AF'.$line)->getBorders()->applyFromArray(
						array(
							'allborders' => array(
								'style' => PHPExcel_Style_Border::BORDER_THIN,
							)
						)
					);
					$objWorksheet2->getStyle('A1:H'.$lineprd)->getBorders()->applyFromArray(
						array(
							'allborders' => array(
								'style' => PHPExcel_Style_Border::BORDER_THIN,
							)
						)
					);

					$objPHPExcel->setActiveSheetIndex(0);
				}

				// Ecrit le fichier et le sauvegarde
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$objWriter->save($export['file_path']);

				exp_exports_set_finished( $export['id'] );
			}

			break;
			case CLS_BL: {

				if( !isset($_REQUEST['key']) ){
					$_REQUEST['key'] = array();
				}

				if( !isset($_REQUEST['filter']) ){
					$_REQUEST['filter'] = array();
				}

				if( !isset($_REQUEST['period']) ){
					$_REQUEST['period'] = array();
				}

				// Charge les bons de livraison
				$rbls = ord_bl_get(
					0, 0, false, false, false, array(), false, array(),
					$_REQUEST['period']['start'], $_REQUEST['period']['end'],
					false, 0,
					$_REQUEST['filter']['seller_id'], $_REQUEST['filter']['dps_id']
				);

				$nb_bls = $rbls ? ria_mysql_num_rows( $rbls ) : 0;

				if( !$nb_bls ){
					exp_exports_set_error( $export['id'], 'Erreur dans l\'exportation des données.' );
					throw new RuntimeException('Erreur lors de la génération de l\'export '.$_REQUEST['exp_id']);
				}

				exp_exports_upd( $export['id'], 'processing', $nb_bls, 0 );

				require_once('excel/PHPExcel.php');

				$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

				// Création de l'objet PHPExcel
				$objPHPExcel = new PHPExcel();

				// Déterminé les propriétés de la feuille Excel
				$objPHPExcel->getProperties()->setCreator("riaStudio")
												->setLastModifiedBy("riaStudio")
												->setTitle(_("Export des bons de livraison"))
												->setSubject(_("Export des bons de livraison"))
												->setDescription(_("Export des bons de livraison"))
												->setKeywords("export bons de livraison bl")
												->setCategory("");

				// Création du fichier
				$objWorksheet = $objPHPExcel->getActiveSheet();

				// 1° feuille : Contient l'entête des commande
				$objWorksheet->setTitle(_('Bons de livraison'));

				// 2° feuille : Contient les lignes de commandes
				$objWorksheet2 = $objPHPExcel->createSheet();
				$objWorksheet2->setTitle(_('Lignes de bons de livraison'));

				// 1° feuille : Nom des colonnes
				$objWorksheet->setCellValue('A1', _('Référence RiaShop'));
				$objWorksheet->mergeCells('A1:A2');
				$objWorksheet->getColumnDimension('A')->setWidth(18);

				$objWorksheet->setCellValue('B1', _('Référence GESCOM'));
				$objWorksheet->mergeCells('B1:B2');
				$objWorksheet->getColumnDimension('B')->setWidth(18);
				$objWorksheet->getColumnDimension('A')->setWidth(18);

				$objWorksheet->setCellValue('C1', _('Référence commande'));
				$objWorksheet->mergeCells('C1:C2');
				$objWorksheet->getColumnDimension('C')->setWidth(18);

				$objWorksheet->setCellValue('D1', _('Référence représentant'));
				$objWorksheet->mergeCells('D1:D2');
				$objWorksheet->getColumnDimension('D')->setWidth(18);

				$objWorksheet->setCellValue('E1', _('Référence compte client'));
				$objWorksheet->mergeCells('E1:E2');
				$objWorksheet->getColumnDimension('E')->setWidth(18);

				$objWorksheet->setCellValue('F1', _('Date'));
				$objWorksheet->mergeCells('F1:F2');
				$objWorksheet->getColumnDimension('F')->setWidth(18);

				$objWorksheet->setCellValue('G1', _('Représentant de la commande'));
				$objWorksheet->mergeCells('G1:H1');

				$objWorksheet->setCellValue('G2', _('Nom'));
				$objWorksheet->getColumnDimension('G')->setWidth(25);

				$objWorksheet->setCellValue('H2', _('Prénom'));
				$objWorksheet->getColumnDimension('H')->setWidth(25);

				$objWorksheet->setCellValue('I1', _('Adresse de facturation'));
				$objWorksheet->mergeCells('I1:R1');

				$objWorksheet->setCellValue('I2', _('Société'));
				$objWorksheet->getColumnDimension('I')->setWidth(25);

				$objWorksheet->setCellValue('J2', _('Civilité'));
				$objWorksheet->getColumnDimension('J')->setWidth(15);

				$objWorksheet->setCellValue('K2', _('Nom'));
				$objWorksheet->getColumnDimension('K')->setWidth(25);

				$objWorksheet->setCellValue('L2', _('Prénom'));
				$objWorksheet->getColumnDimension('L')->setWidth(25);

				$objWorksheet->setCellValue('M2', _('Adresse'));
				$objWorksheet->mergeCells('M2:N2');
				$objWorksheet->getColumnDimension('M')->setWidth(35);
				$objWorksheet->getColumnDimension('N')->setWidth(35);

				$objWorksheet->setCellValue('O2', _('Code postal'));
				$objWorksheet->getColumnDimension('O')->setWidth(15);

				$objWorksheet->setCellValue('P2', _('Ville'));
				$objWorksheet->getColumnDimension('P')->setWidth(25);

				$objWorksheet->setCellValue('Q2', _('Pays'));
				$objWorksheet->getColumnDimension('Q')->setWidth(25);

				$objWorksheet->setCellValue('R2', _('Email'));
				$objWorksheet->getColumnDimension('R')->setWidth(35);

				$objWorksheet->setCellValue('S1', _('Adresse de livraison'));
				$objWorksheet->mergeCells('S1:AA1');

				$objWorksheet->setCellValue('S2', _('Société'));
				$objWorksheet->getColumnDimension('S')->setWidth(25);

				$objWorksheet->setCellValue('T2', _('Civilité'));
				$objWorksheet->getColumnDimension('T')->setWidth(15);

				$objWorksheet->setCellValue('U2', _('Nom'));
				$objWorksheet->getColumnDimension('U')->setWidth(25);

				$objWorksheet->setCellValue('V2', _('Prénom'));
				$objWorksheet->getColumnDimension('V')->setWidth(25);

				$objWorksheet->setCellValue('W2', _('Adresse'));
				$objWorksheet->mergeCells('W2:X2');
				$objWorksheet->getColumnDimension('W')->setWidth(35);
				$objWorksheet->getColumnDimension('X')->setWidth(35);

				$objWorksheet->setCellValue('Y2', _('Code postal'));
				$objWorksheet->getColumnDimension('Y')->setWidth(15);

				$objWorksheet->setCellValue('Z2', _('Ville'));
				$objWorksheet->getColumnDimension('Z')->setWidth(25);

				$objWorksheet->setCellValue('AA2', _('Pays'));
				$objWorksheet->getColumnDimension('AA')->setWidth(25);

				if( !isset($_GET['state']) || !$_GET['state'] ) {
					$objWorksheet->setCellValue('AB1', _('Statut'));
					$objWorksheet->mergeCells('AB1:AB2');
				}

				$objWorksheet->setCellValue('AC1', _('Moyen de paiement'));
				$objWorksheet->getColumnDimension('AC')->setWidth(25);
				$objWorksheet->mergeCells('AC1:AC2');

				$objWorksheet->setCellValue('AD1', _('Total HT'));
				$objWorksheet->getColumnDimension('AD')->setWidth(15);
				$objWorksheet->mergeCells('AD1:AD2');

				$objWorksheet->setCellValue('AE1', _('Total TTC'));
				$objWorksheet->getColumnDimension('AE')->setWidth(15);
				$objWorksheet->mergeCells('AE1:AE2');

				// 1° feuille : Si l'on connait le status des commandes, on masque la colonne U
				$objWorksheet->getColumnDimension('AB')->setWidth( isset($_GET['state']) && $_GET['state']>0 ? 0 : 23 );

				// 2° feuille : Nom des colonnes
				$objWorksheet2->setCellValue('A1', _('Référence RiaShop'));
				$objWorksheet2->getColumnDimension('A')->setWidth(18);

				$objWorksheet2->setCellValue('B1', _('Référence GESCOM'));
				$objWorksheet2->getColumnDimension('B')->setWidth(18);

				$objWorksheet2->setCellValue('C1', _('Référence'));
				$objWorksheet2->getColumnDimension('C')->setWidth(12);

				$objWorksheet2->setCellValue('D1', _('Désignation'));
				$objWorksheet2->getColumnDimension('D')->setWidth(80);

				$objWorksheet2->setCellValue('E1', _('Prix HT'));
				$objWorksheet2->getColumnDimension('E')->setWidth(11);

				$objWorksheet2->setCellValue('F1', _('Quantité'));
				$objWorksheet2->getColumnDimension('F')->setWidth(11);

				$objWorksheet2->setCellValue('G1', _('Total HT'));
				$objWorksheet2->getColumnDimension('G')->setWidth(13);

				// Police sur l'entête des colonnes
				$objWorksheet->getStyle('A1:AE2')->getFont()->setBold(true);
				$objWorksheet->getStyle('A1:AE2')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
				$objWorksheet->getStyle('A1:AE2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
				$objWorksheet->getStyle('A1:AE2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
				$objWorksheet->getStyle('A1:AE2')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

				$objWorksheet2->getStyle('A1:G1')->getFont()->setBold(true);
				$objWorksheet2->getStyle('A1:G1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
				$objWorksheet2->getStyle('A1:G1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
				$objWorksheet2->getStyle('A1:G1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
				$objWorksheet2->getStyle('A1:G1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

				$ar_payments = ord_payment_types_get_array();
				$ar_states = ord_states_get_array();

				// Enregistre les entêtes de commandes
				$line = 3; $firstline = 3;
				$lineprd = 2; $firstprdline = 2;
				$cursor = 0;
				while( $bl = ria_mysql_fetch_assoc($rbls) ){
					$cursor++;
					// Met à jour le nombre de ligne traité de l'export à chaque tranche de 10% de commande traités
					if( $export['id'] ){
						if( $nb_bls > 20 ){
							if( !($cursor%($nb_bls/10)) ){
								exp_exports_upd( $export['id'], '', $nb_bls, $cursor );
							}
						}
					}

					$objWorksheet->setCellValue('A'.$line, $bl['id'] );
					$objWorksheet->setCellValue('B'.$line, $bl['piece'] );
					$objWorksheet->setCellValue('C'.$line, $bl['ref'] );
					$objWorksheet->setCellValue('D'.$line, $bl['seller_id'] );
					$objWorksheet->setCellValue('E'.$line, $bl['usr_id'] );
					$objWorksheet->setCellValue('F'.$line, $bl['date'] );

					// Charge l'adresse de facturation
					$rinv = gu_adresses_get( 0, $bl['adr_invoices'] );
					if( $rinv && ria_mysql_num_rows($rinv) ){
						$inv_address = ria_mysql_fetch_array($rinv);
					}else{
						$inv_address = array(
							'type_id' => 0,
							'title_name' => '',
							'lastname' => '',
							'firstname' => '',
							'society' => '',
							'address1' => '',
							'address2' => '',
							'postal_code' => '',
							'city' => '',
							'country' => ''
						);
					}

					// Charge l'adresse de livraison
					$rdlv = gu_adresses_get( 0, $bl['adr_dlv_id'] );
					if( $rdlv && ria_mysql_num_rows($rdlv) ){
						$dlv_address = ria_mysql_fetch_array($rdlv);
					}

					//Représentant
					if ($bl['seller_id']){
						$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $bl['seller_id']);

						if ($r_seller && ria_mysql_num_rows($r_seller)){
							$seller = ria_mysql_fetch_assoc($r_seller);

							$objWorksheet->setCellValue('G' . $line, $seller['adr_lastname']);
							$objWorksheet->setCellValue('H' . $line, $seller['adr_firstname']);
						}
					}

					// Adresse de facuration
					if( $inv_address['type_id']==1 ){
						$objWorksheet->setCellValue('J' . $line, $inv_address['title_name']);
						$objWorksheet->setCellValue('K' . $line, $inv_address['lastname']);
						$objWorksheet->setCellValue('L' . $line, $inv_address['firstname']);
					}else{
						$objWorksheet->setCellValue('I' . $line, $inv_address['society']);
					}

					$objWorksheet->setCellValue('M' . $line, $inv_address['address1']);
					$objWorksheet->setCellValue('N' . $line, $inv_address['address2']);
					$objWorksheet->setCellValue('O' . $line, $inv_address['postal_code']);
					$objWorksheet->setCellValue('P' . $line, $inv_address['city']);
					$objWorksheet->setCellValue('Q' . $line, $inv_address['country']);

					if (isset($bl['usr_id'])) {
						$email = gu_users_get_email($bl['usr_id']);
						if ($email) {
							$objWorksheet->setCellValue('R' . $line, $email);
						}
					}

					// Adresse de livraison
					$objWorksheet->setCellValue('S'.$line, $dlv_address['society'] );
					$objWorksheet->setCellValue('T'.$line, $dlv_address['title_name'] );
					$objWorksheet->setCellValue('U'.$line, $dlv_address['lastname'] );
					$objWorksheet->setCellValue('V'.$line, $dlv_address['firstname'] );
					$objWorksheet->setCellValue('W'.$line, $dlv_address['address1'] );
					$objWorksheet->setCellValue('X'.$line, $dlv_address['address2'] );
					$objWorksheet->setCellValue('Y'.$line, $dlv_address['postal_code'] );
					$objWorksheet->setCellValue('Z'.$line, $dlv_address['city'] );
					$objWorksheet->setCellValue('AA'.$line, $dlv_address['country'] );

					$objWorksheet->setCellValue('AB'.$line, $ar_states[$bl['state_id']]['name'] );
					$objWorksheet->setCellValue('AC'.$line, ($bl['pay_id'] ? $ar_payments[$bl['pay_id']]['name'] : '') );
					$objWorksheet->setCellValue('AD'.$line, number_format( $bl['total_ht'], 2, '.', '' ) );
					$objWorksheet->setCellValue('AE'.$line, number_format( $bl['total_ttc'], 2, '.', '' ) );

					// Enregistre les lignes de commandes
					$rpord = ord_bl_products_get( $bl['id'] );
					if( $rpord && ria_mysql_num_rows($rpord) ){
						while( $pord = ria_mysql_fetch_assoc($rpord) ){

							$objWorksheet2->setCellValue('A'.$lineprd, $bl['id']);
							$objWorksheet2->setCellValue('B'.$lineprd, $bl['piece']);
							$objWorksheet2->setCellValue('C'.$lineprd, $pord['ref']);
							$objWorksheet2->setCellValue('D'.$lineprd, $pord['name']);
							$objWorksheet2->setCellValue('E'.$lineprd, number_format( $pord['price_ht'], 2, '.', '' ));
							$objWorksheet2->setCellValue('F'.$lineprd, number_format( $pord['qte'], 0, '', ' ' ));
							$objWorksheet2->setCellValue('G'.$lineprd, number_format( $pord['total_ht'], 2, '.', '' ));
							$lineprd++;

						}
					}

					$objWorksheet->getStyle('E'.$line)->getAlignment()->setWrapText(true);
					$objWorksheet->getStyle('F'.$line)->getAlignment()->setWrapText(true);

					$line++;
				}

				// Mise en place des totaux finaux
				$objWorksheet->mergeCells('A'.$line.':AC'.$line);
				$objWorksheet->setCellValue('A'.$line, _('Total').' : ' );
				$objWorksheet->setCellValue('AD'.$line, '=SUM(AD' . $firstline. ':AD'.($line-1).')' );
				$objWorksheet->setCellValue('AE'.$line, '=SUM(AE' . $firstline . ':AE'.($line-1).')' );
				$objWorksheet->getStyle('A'.$line.':AE'.$line)->getBorders()->applyFromArray(
					array(
						'allborders' => array(
							'style' => PHPExcel_Style_Border::BORDER_THIN,
						)
					)
				);
				$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->setBold(true);
				$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
				$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
				$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
				$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

				$lineprd--; $line--;

				// Gestion des alignements
				$objWorksheet->getStyle('A' . $firstline . ':AE'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
				$objWorksheet->getStyle('A' . $firstline . ':F'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
				$objWorksheet->getStyle('AD' . $firstline . ':AE'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

				$objWorksheet2->getStyle('A' . $firstprdline . ':G'.$lineprd)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
				$objWorksheet2->getStyle('A' . $firstprdline . ':B'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
				$objWorksheet2->getStyle('C' . $firstprdline . ':D'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
				$objWorksheet2->getStyle('E' . $firstprdline . ':G'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

				// Format de données
				$objWorksheet->getStyle('AD' . $firstline . ':AD'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');
				$objWorksheet->getStyle('AE' . $firstline . ':AE'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');

				$objWorksheet2->getStyle('E' . $firstprdline . ':E'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
				$objWorksheet2->getStyle('G' . $firstprdline . ':G'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');

				// Bordure
				$objWorksheet->getStyle('A1:AE'.$line)->getBorders()->applyFromArray(
					array(
						'allborders' => array(
							'style' => PHPExcel_Style_Border::BORDER_THIN,
						)
					)
				);
				$objWorksheet2->getStyle('A1:G'.$lineprd)->getBorders()->applyFromArray(
					array(
						'allborders' => array(
							'style' => PHPExcel_Style_Border::BORDER_THIN,
						)
					)
				);

				$objPHPExcel->setActiveSheetIndex(0);

				// Ecrit le fichier et le sauvegarde
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$objWriter->save($export['file_path']);

				exp_exports_set_finished( $export['id'] );

			}
			case CLS_USER : {
				// Export des tarifs pour un compte client
				if( isset($_REQUEST['type']) && $_REQUEST['type'] == 'userprice' ){
					if( !isset($_REQUEST['user']) || !gu_users_exists($_REQUEST['user']) ){
						throw new BadFunctionCallException('L\'identifiant du compte client est absent');
					}

					// Charge l'environnement nécessaire pour le calcul des tarifs du client
					$user = ria_mysql_fetch_assoc( gu_users_get($_REQUEST['user']) );
					$_SESSION['usr_id'] = $user['id'];
					$_SESSION['usr_discount'] = $user['discount'];
					$_SESSION['usr_prf_id'] = $user['prf_id'];
					$_SESSION['usr_prc_id'] = $user['prc_id'];

					// Récupère les articles
					$r_product = prd_products_get(0, '', 0, true, 0, 0, -1, false, false, false, false, false, true, array('ref' => 'asc'));
					if( !$r_product ){
						throw new BadFunctionCallException('Impossible de récupérer les articles');
					}

					// Enregistre le nombre de ligne que contiendra l'export
					exp_exports_upd( $export['id'], 'processing', ria_mysql_num_rows($r_product), 0 );

					// Création de l'objet PHPExcel
					require_once('excel/PHPExcel.php');
					$objPHPExcel = new PHPExcel();

					// Déterminé les propriétés de la feuille Excel
					$objPHPExcel->getProperties()->setCreator("riaStudio")
						->setLastModifiedBy("riaStudio")
						->setTitle(_("Tarif client"))
						->setSubject(_("Tarif client"))
						->setDescription(_("Tarif client"))
						->setKeywords("tarif client")
						->setCategory("");

					// Création du fichier
					$objWorksheet = $objPHPExcel->getActiveSheet();
					$objWorksheet->setTitle(_('Tarifs'));

					// Création de l'entête du fichier
					$objWorksheet->setCellValue('A1', _('Tarifs net'));
					$objWorksheet->mergeCells('A1:C1');

					$objWorksheet->setCellValue('A2', _('Référence'));
					$objWorksheet->setCellValue('B2', _('Désignation'));
					$objWorksheet->setCellValue('C2', _('Prix unitaire HT'));

					// Règle la taille des colonnes
					$objWorksheet->getColumnDimension('A')->setWidth(15);
					$objWorksheet->getColumnDimension('B')->setWidth(40);
					$objWorksheet->getColumnDimension('C')->setWidth(15);

					// Applique un style sur l'entête
					$objWorksheet->getStyle('A1:C2')->getFont()->setBold(true);
					$objWorksheet->getStyle('A1:C2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
					$objWorksheet->getStyle('A1:C2')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					$line = 3;
					$nb_products = ria_mysql_num_rows( $r_product );

					while( $p = ria_mysql_fetch_assoc($r_product) ){
						if( !($line%($nb_products/10)) ){
							exp_exports_upd( $export['id'], '', $nb_products, $line );
						}

						if( substr($p['ref'],0,2)!='RG' ){
							$objWorksheet->setCellValue( 'A'.$line, $p['ref'] );
							$objWorksheet->setCellValue( 'B'.$line, $p['name'] );
							$objWorksheet->setCellValue( 'C'.$line, number_format( $p['price_ht'], 2, ',', ' ' ) );

							$line++;
						}
					}

					// Alignement des colonnes
					$objWorksheet->getStyle('A3:A'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
					$objWorksheet->getStyle('A3:A'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
					$objWorksheet->getStyle('B3:B'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
					$objWorksheet->getStyle('B3:B'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
					$objWorksheet->getStyle('C3:C'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
					$objWorksheet->getStyle('C3:C'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

					// Enregistre le fichier à l'emplacement demandé par l'export
					$objPHPExcel->setActiveSheetIndex(0);
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$objWriter->save($export['file_path']);

					exp_exports_set_finished($export['id']);
				}

				break;
			}
		}

		$result = true;
		$content = array();
	break;
}


// \endcond

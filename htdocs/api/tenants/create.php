<?php
/**
 * \defgroup Locataires Locataires
 * \ingroup crm
 * @{
 * \page api-tenants-create Ajout
 *
 *	Cette fonction permet d'ajouter un nouveau locataire
 *
 *		\code
 *			POST /tenants/create/
 *		\endcode
 *
 * @param {string} $name Obligatoire, Nom à donner à l'instance
 * @param {string} $admin_email Obligatoire, Adresse email de l'administrateur principal
 * @param {string} $package Obligatoire, nom de l'offre : essentiel, business, legacy
 * @param {string} $typ_subscription Obligatoire, type d'abonnement (soit year, soit month)
 * @param {int} $nb_devices Obligatoire, nombre d'appareils Yuto pouvant être connectés à l'instance
 * @param {int} $sub facultatif, Activation ou non de la gestion de l'abonnement
 *
 * @return true si l'ajout s'est déroulé avec succès
*/

require_once('RegisterGCP.inc.php');

switch( $method ){
	case 'add':
		if( isset($_REQUEST['version']) && $_REQUEST['version'] == 'v2' ){
			try{
				// TODO : Rendre dynamique la sélection du serveur maria
				// Step 1 : le serveur sera celui de la config_db = business (cf. registre => config)
				$ids = RegisterGCP::addNewTenant( 'business', $_REQUEST['data_tenant']['name'], [
					$_REQUEST['data_tenant'], $_REQUEST['data_yuto'], $_REQUEST['data_web']
				]);

				// Contrôle que tous les identifiants sont biens retourner (locataire + extranet)
				if(
					!is_array($ids) || !ria_array_key_exists(['tnt_id', 'web_id'], $ids)
				){
					throw new BadFunctionCallException("Erreur lors de la création du locataire : absence des identifiants.");
				}

				// Contrôle la présence du fichier de données de configuration
				if( !file_exists('/var/www/extranet-template-1/data/config.sql') ){
					throw new BadFunctionCallException('Impossible de charge le fichier de données de configuration.');
				}

				// Récupère le fichier contenant les données de configuration
				$data_config = file_get_contents( '/var/www/extranet-template-1/data/config.sql');

				// Remplace dans le SQL l'identifiant du locataire et l'identifiant de l'extranet
				$data_config = str_replace( '%tnt_id%', $ids['tnt_id'], $data_config );
				$data_config = str_replace( '%wst_id%', $ids['web_id'], $data_config );

				// Exécute le SQL
				$lines = explode("\n", $data_config);

				$templine = '';
				foreach( $lines as $line ){
					if( substr($line, 0, 2) == '--' || trim($line) == '' ){
						continue;
					}

					$templine .= $line;

					if( substr(trim($line), -1, 1) == ';' ){
						if( !ria_mysql_query($templine) ){
							throw new BadFunctionCallException('Erreur lors de l\'exécution du SQL de config : '.ria_mysql_error());
						}

						$templine = '';
					}
				}
			}catch( Exception $e ){
				throw new BadFunctionCallException( $e-> getMessage() );
			}
		}else{
			// Contrôle des informations obligatoire (name => société, admin_email => adresse mail de l'administrateur) à la création du tenant
			if( !isset($_REQUEST[ 'name']) || trim($_REQUEST['name']) == '' ){
				throw new BadFunctionCallException('Le nom de la société est obligatoire.');
			}

			if( !isset($_REQUEST[ 'admin_email']) || trim($_REQUEST['admin_email']) == '' ){
				throw new BadFunctionCallException('L\'adresse mail du compte administrateur principal est obligatoire.');
			}

			// Contrôle le type de formule choisi (essentiel ou business)
			if( !isset($_REQUEST['package']) || !in_array($_REQUEST['package'], array('essentiel', 'business', 'legacy')) ){
				throw new Exception('Le type d\'offre est obligatoire (essentiel, business)');
			}

			// Contrôle le type d'abonnement choisi (year => Annuel | month => mensuel)
			if( !isset($_REQUEST['typ_subscription']) || !in_array($_REQUEST['typ_subscription'], array('year', 'month')) ){
				throw new Exception('Le type d\'abonnement est obligatoire (year, month)');
			}

			// Contrôle le nombre de licences Yuto acceptées
			if( !isset($_REQUEST['nb_devices']) || !is_numeric($_REQUEST['nb_devices']) || $_REQUEST['nb_devices'] <= 0 ){
				throw new Exception('Le nombre de licences Yuto acceptées est obligatoire');
			}

			// Initialise la valeur par défaut des paramètres optionnels
			$arr_default = array(
				'address1' => '-',
				'address2' => '',
				'zipcode' => '-',
				'city' => '-',
				'admin_civility' => '',
				'admin_firstname' => '',
				'admin_lastname  ' => '',
				'testing' => false,
				'ord_id' => 0,
			);

			$data = array_merge($arr_default, $_REQUEST);

			// Activation ou non de la gestion de l'abonnement
			$sub = isset($_REQUEST['sub']) && $_REQUEST['sub'];

			// Traite la création d'un nouveau locataire + le compte administrateur principal
			// Dans le cas d'un échec, un mail détaillant la / les raisons est envoyé via la fonction d'ajout elle-même
			if( !RegisterGCP::add($data['name'], $data['package'], $data, $sub) ){
				return false;
			}
		}

		$result = true;
		break;
}

///@}
<?php
/**
 *  \defgroup Websites Websites
 *  \ingroup Locataires
 *	@{
 *
 *	\page api-tenants-websites-get Chargement
 *
 *	cette fonction renvoie la liste des websites disponibles pour un locataire donné
 *
 *		\code
 *			GET /tenants/websites/
 *		\endcode
 *
 * @return json avec les éléments sour la forme suivante :
 *	\code{.json}
 *		{
 *           "id": Identifiant,
 *           "name": Nom du site,
 *           "desc": Description,
 *           "ga_key": "",
 *           "gm_key": "",
 *           "date-created": Date de création,
 *           "site_title": titre du site,
 *           "meta_desc": description pour référencement,
 *           "meta_kwd": Mot-clés pour référencement,
 *           "type_id": Type du website ( site public, extranet, yuto),
 *           "type_name": Nom du type du website,
 *           "ord-detach": Si true : détache les paniers,
 *           "is_default": Website principal
 *		}
 * 	\endcode
*/


switch( $method ){
	case 'add':
		$old_ria_db_connect = $ria_db_connect;
		$old_config = $config;

		RegisterGCPConnection::init( (int) $_REQUEST['tenant'], true );

		// Récupère l'identifiant du tenant dans le registre
		$key_gcp = RegisterGCP::getRegisterKey( $_REQUEST['tenant'] );
		if( trim($key_gcp) == '' ){
			throw new BadFunctionCallException('Impossible de récupérer la clé du locataire.');
		}

		// Création du site Yuto
		$wst_name = $_REQUEST['website']['type'] == _WST_TYPE_EXTRANET ? 'Extranet' : 'Site public';
		$website_id = Monitoring::addWebsite( $_REQUEST['tenant'], $_REQUEST['website']['type'], $wst_name, $_REQUEST['website']);

		// Défini les droits pour le nouveau site
		Monitoring::setWebsiteRights( $_REQUEST['tenant'], $website_id, $_REQUEST['website']['type'], $_REQUEST['website']['package'], true );

		// Mise à jour du package
		$monitoring = new Monitoring();
		if( $_REQUEST['website']['type'] == _WST_TYPE_EXTRANET ){
			$monitoring->setPackageBtoB( $key_gcp, $_REQUEST['website']['package'] );
		}else{
			// TODO Step 2 : BtoC
		}

		// Contrôle la présence du fichier de données de configuration
		if( !file_exists('/var/www/extranet-template-1/data/config.sql') ){
			throw new BadFunctionCallException('Impossible de charge le fichier de données de configuration.');
		}

		// Récupère le fichier contenant les données de configuration
		$data_config = file_get_contents( '/var/www/extranet-template-1/data/config.sql' );

		// Remplace dans le SQL l'identifiant du locataire et l'identifiant de l'extranet
		$data_config = str_replace( '%tnt_id%', $_REQUEST['tenant'], $data_config );
		$data_config = str_replace( '%wst_id%', $website_id, $data_config );

		// Exécute le SQL
		$lines = explode("\n", $data_config);

		$templine = '';
		foreach( $lines as $line ){
			if( substr($line, 0, 2) == '--' || trim($line) == '' ){
				continue;
			}

			$templine .= $line;

			if( substr(trim($line), -1, 1) == ';' ){
				if( !ria_mysql_query($templine) ){
					throw new BadFunctionCallException('Erreur lors de l\'exécution du SQL de config : '.ria_mysql_error());
				}

				$templine = '';
			}
		}

		$ria_db_connect = $old_ria_db_connect;
		$config = $old_config;

		$result = true;
		break;
	case 'get':

		$content = array();
		$rweb = wst_websites_get();
		while( $web = ria_mysql_fetch_assoc($rweb) ){
			unset( $web['tnt_id'] );
			$content[] = $web;
		}

		$result = true;
		break;
}

///@}
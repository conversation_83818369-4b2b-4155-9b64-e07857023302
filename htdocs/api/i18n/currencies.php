<?php
/** 
 * \defgroup api-i18n-currencies Devises
 * \ingroup i18n
 * @{	
 * \page api-i18n-currencies-get Chargement
 *
 * Cette fonction récupère la liste des devises du monde. 
 *
 * \code
 *      GET /i18n/currencies/
 * \endcode
 * 
 * @return Json Liste des devises sous la forme suivante :
 * \code{.json}
 *	{
 *     "code": Code de la devise selon la norme ISO 4217,
 *     "name": Nom de la devise en français,
 *     "symbol": Symbole monétaire,
 *     "place": A quel emplacement va le symbole (before ou after),
 *     "decimals": Séparateur de décimales,
 *     "thousands": Séparateur de milliers
 *	}
 * \endcode
 * @}
*/
switch( $method ){
    case 'get' :
        $result = true;
	  	  $string = file_get_contents(dirname(__FILE__)."/currencies.json");
		    $content = json_decode($string, true);
	  	  break;
      }

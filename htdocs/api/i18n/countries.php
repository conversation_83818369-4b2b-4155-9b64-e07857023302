<?php
/** 
 * \defgroup api-i18n-countries Pays
 * \ingroup i18n
 * @{	
 * \page api-i18n-countries-get Chargement
 *
 * Cette fonction récupère la liste des pays du monde. 
 *
 * \code
 *		GET /i18n/countries/
 * \endcode
 * 
 * @return Json Liste des pays sous la forme suivante :
 * \code{.json}
 *	{
 *     "nameEN": nom du pays en anglais,
 *     "nameFR": nom du pays en français,
 *     "code": code du pays sur 2 caractères (aplha-2),
 *     "code3": code du pays sur 3 caractères (alpha-3),
 *     "codenum": code du pays en numérique ISO 3166	
 *	}
 * \endcode
 * @}
*/

switch ($method) {
	case 'get':
		$result = true;
		$string = file_get_contents(dirname(__FILE__)."/countries.json");
		$content = json_decode($string, true);
		break;
}

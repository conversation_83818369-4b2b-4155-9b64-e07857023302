<?php
/** 
 * \defgroup OnBoardingYuto Onboarding Yuto 
 * \ingroup Yuto
 * @{
 * 
 * \page api-actions-index-get Chargement
 * 
 * Cette fonction récupére une liste des actions
 * 
 *		\code
 *			GET /actions/
 *		\endcode
 *
 * @return Json Liste d'actions sous la forme suivante :
 *		\code{.json}
 *			{
 *           "id": identifiant de l'action,
 *           "qte": nombre de fois ou l'action doit être réalisé pour être complétée,
 *           "description": description de l'action,
 *           "package": 
 * 			}
 * 		\endcode
*/
require_once('act_actions.inc.php');

switch( $method ){
	case 'get':

		$rstates = act_actions_get()  ;
		if( $rstates && ria_mysql_num_rows($rstates) ){
			while($state = ria_mysql_fetch_assoc($rstates)){
				$content[] = $state;
			}
		}
		$result = true;

		break;
}

///@}
<?php
/**
 * \defgroup orders Commandes
 * \ingroup oms
 * @{
*/

// \cond onlyria
// cette fonction permet la mise à jour d'une en-tete de commande
function update_order($ord_id){

	// mise à jour de la date de commande
	if( isset($_REQUEST['date']) ){
		if( isdate($_REQUEST['date']) ){ // historique ce test ne devrait plus être fait si toutes les tablettes sont en version 14+
			$_REQUEST['date'] = $_REQUEST['date']." 00:00:00";
		}
		if( !ord_orders_set_date($ord_id, $_REQUEST['date'], true) ){
			throw new Exception("Erreur dans la mise à jour de la date de commande");
		}
	}

	// mise à jour de l'utilisateur
	if( isset($_REQUEST['usr']) && $_REQUEST['usr'] > 0 ){
		if( !ord_orders_update_user($ord_id, $_REQUEST['usr'], false) ){
			throw new Exception("Erreur dans la mise à jour de l'utilisateur");
		}
	}

	// mise à jour de l'adresse de facturation
	if( isset($_REQUEST['adr_invoices']) && $_REQUEST['adr_invoices'] >0){
		if( !ord_orders_adr_invoices_set($ord_id, $_REQUEST['adr_invoices']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de facturation");
		}
	}

	// mise à jour de l'adresse de livraison
	if( isset($_REQUEST['adr_delivery']) ){
		if( !ord_orders_adr_delivery_set($ord_id, $_REQUEST['adr_delivery']==0 ? false : $_REQUEST['adr_delivery']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de livraison");
		}
	}

	// mise à jour du seller id
	if( isset($_REQUEST['seller_id']) ){
		$seller = $_REQUEST['seller_id'] > 0 ? $_REQUEST['seller_id'] : null;
		if( !ord_orders_set_seller_id( $ord_id, $seller ) ){
			throw new Exception("Erreur dans la mise à jour du seller id");
		}
	}

	// mise à jour de la date de livraison
	if( isset($_REQUEST['date-livr']) ){
		if( !ord_orders_set_date_livr( $ord_id, $_REQUEST['date-livr']) ){
			throw new Exception("Erreur dans la mise à jour de la date de livraison");
		}
	}

	// mise à jour du service de livraison
	if( isset($_REQUEST['srv_id']) ){
		if( !ord_orders_set_dlv_service( $ord_id, $_REQUEST['srv_id']) ){
			throw new Exception("Erreur dans la mise à jour du service de livraison");
		}
	}

	// mise à jour du service du magasin
	if( isset($_REQUEST['str_id']) ){
		if( !ord_orders_set_dlv_store( $ord_id, $_REQUEST['str_id']) ){
			throw new Exception("Erreur dans la mise à jour du magasin");
		}
	}

	// mise à jour du dépot
	if( isset($_REQUEST['dps_id']) ){
		if( !is_numeric($_REQUEST['dps_id']) ) $_REQUEST['dps_id'] = 0;

		if( !ord_orders_set_deposit( $ord_id, $_REQUEST['dps_id']) ){
			throw new Exception("Erreur dans la mise à jour du dépot de livraison");
		}
	}

	// mise à jour du package
	if( isset($ord['pkg_id']) ){
		if( !is_numeric($_REQUEST['pkg_id']) ) $_REQUEST['pkg_id'] = 0;

		if( !ord_orders_set_package( $ord_id, $_REQUEST['pkg_id']) ){
			throw new Exception("Erreur dans la mise à jour du packaging de livraison");
		}
	}

	// mise à jour des notes
	if( isset($_REQUEST['dlv_notes']) ){
		if( !ord_orders_dlv_notes_set( $ord_id, $_REQUEST['dlv_notes']) ){
			throw new Exception("Erreur dans la mise à jour d'une note sur la commande");
		}
	}

	// mise à jour du paiement
	if( isset($_REQUEST['pay_id']) ){
		if( !ord_orders_pay_type_set( $ord_id, $_REQUEST['pay_id']) ){
			throw new Exception("Erreur dans la mise à jour d'un paiement");
		}
	}

	// mise à jour du code promotion
	if( isset($_REQUEST['pmt_id']) ){
		$code = pmt_codes_get_code($_REQUEST['pmt_id']);
		if( trim($code) ){
			// démaske la commande
			ord_orders_unmask($ord_id);
			if( !pmt_codes_apply_forced( $code, $ord_id ) ){
				throw new Exception("Erreur dans la mise à jour du code promo");
			}
			// remaske la commande
			ord_orders_unmask($ord_id, true, true);
		}
	}

	// mise à jour du token d'auth d'envoi d'email
	if( isset($_REQUEST['ord_notify_key']) ){
		fld_object_values_set( $ord_id, _FLD_ORD_NOTIFY_KEY, $_REQUEST['ord_notify_key'] );
	}

	// Mise à jour du need sync
	if( isset($_REQUEST['need_sync']) && $_REQUEST['need_sync']==1 ){
		ord_orders_set_need_sync($ord_id);
	}
}
// \endcond

switch( $method ){
	/** @{@}
 	 *	@{
	 *	\page api-orders-index-get Chargement
	 *
	 *	Cette fonction récupère une entête de commande
	 *
	 *		\code
	 *			GET /orders/
	 *		\endcode
	 *
	 *	@param int $id Obligatoire, Identifiant de la commande
	 *	@param bool $masked Facultatif, permet la récupération de commandes masquées (elles sont filtrées par défaut)
	 *
	 *	@return json La commande avec les colonnes :
	 *	\code{.json}
	 *       	{
	 *				"user" : identifiant du client ayant passé la commande
	 *				"id" : Identifiant de la commande
	 *				"ref" : Référence de la commande (identifiant complété par des 0, 8 caractères)
	 *				"piece" : numéro de pièce dans la gestion commerciale
	 *				"date" : date/heure de la commande
	 *				"date_en" : date au format US
	 *				"state_id" : identifiant du statut de commande
	 *				"state_name" : libellé du statut de commande
	 *				"products" : nombre d'articles contenus dans la commande
	 *				"total_ht" : montant total de la commande, hors taxes
	 *				"total_ttc" : montant total de la commande, ttc
	 *				"adr_invoices" : identifiant de l'adresse de facturation
	 *				"adr_delivery" : identifiant de l'adresse de livraison
	 *				"srv_id" : identifiant du service de livraison demandé par le client
	 *				"str_id" : identifiant du magasin dans lequel le client souhaite être livré
	 *				"dlv-notes" : consignes de livraison
	 *				"pmt_id" : identifiant d'un code promotion appliqué à la commande
	 *				"seller_id" : identifiant du représentant ayant enregistré la commande
	 *				"comments" : commentaires du représentant ayant enregistré la commande
	 *				"cnt_id" : identifiant de la commande dans le moteur de recherche
	 *				"ord_livr" : date de livraison prévue de la commande
	 *				"nsync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
	 *				"need_sync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
	 *				"total_ht_delivered" : montant total des articles non livrés de la commande
	 *				"dps_id" : dépôt de livraison de la commande (commandes fournisseurs)
	 *				"pkg_id" : package de livraison de la commande (commandes fournisseurs)
	 *				"age" : Age de la commande
	 *				"relanced" : Détermine si le client de la commande doit être relancé pour paiement complémentaire (Atos / Bigship uniquement)
	 *				"alert-livr" : Détermine si une alerte email pour les produits en rupture de la commande doit être envoyée (Bigship uniquement)
	 *				"rly_id" : Identifiant du point-relais de livraison
	 *				"parent" : Identifiant du compte parent, si la commande est liée à un compte enfant (à ignorer si la variable de configuration "parent_is_order_holder" n'est pas activée)
	 *				"wst_id" : Identifiant du site pour lequel la commande a été passée
	 *				"date_archived" : Date d'archivation
	 *				"date_modified" : Date de modification
	 *				"state_sage" : non utilisé
	 *				"opt-gift" : option cadeau activée oui / non
	 *				"opt-gift-message" : message de l'option cadeau
	 *				"pay_id" : identifiant du moyen de paiement de la commande
	 *				"card_id" : identifiant du type de CB de la commande
	 *				"reliquats" : est un reliquat oui / non
	 *				"ord_livr_fr" : date de livraison prévue au format FR
	 *				"date_modified_fr" : date de dernière modification au format FR
	 *				"masked" : commande masquée oui / non
	 *				"parent_id" : identifiant de la commande parent
	 *       },
	 *	\endcode
	 *	@}
	*/
	case 'get':
		if( !isset($_REQUEST['id']) ){
			throw new Exception( "L'identifiant de commande fourni est invalide");
		}

		$masked = isset($_REQUEST['masked']) && $_REQUEST['masked']=='1';

		$rord = ord_orders_get(
			0, $_REQUEST['id'], 0, 0, null, false, false, false, false, false, false, '', false, false, false, false, false, false, false, false, $masked
		);
		if( $rord && ria_mysql_num_rows($rord) ){
			$result = true;
			$content = ria_mysql_fetch_assoc($rord);
		}
		break;

	/** @{@}
 	 *	@{
	 *	\page api-orders-index-add Ajout
	 *
	 *	Cette fonction ajoute un entête de commande.
	 *
	 *		\code
	 *			POST /orders/
	 *		\endcode
	 *
	 *	@param int $usr Obligatoire, Identifiant de l'utilisateur propriétaire de la commande
	 *	@param $date Obligatoire, Date de la commande
	 *	@param int $state Obligatoire, Etat de la commande
	 *	@param string $piece Obligatoire, No pièce de la commande
	  *	@param string $ref Obligatoire, No référence de la commande
	 *	@param bool $masked Facultatif, Si la commande est masquée
	 *	@param string $state_sage Facultatif, Chaîne de caractère décrivant l'état de la commande dans la gestion commerciale
	 *	@param int $wst Facultatif, identifiant du site rattaché à la commande
	 *	@param int $adr_invoices Facultatif, Identifiant de l'adresse de facturation
	 *	@param int $adr_delivery Facultatif, Identifiant de l'adresse de livraison
	 *	@param int $seller_id Facultatif, Identifiant de vendeur a attribuer à la commande
	 *	@param $date-livr Facultatif, Date de livraison prévue de la commande
	 *	@param int $srv_id Facultatif, Identifiant du service de livraison
	 *	@param int $dps_id Facultatif, Identifiant du dépôt de stock
	 *	@param int $pkg_id Facultatif, Identifiant du package
	 *	@param string $dlv_notes Facultatif, Consignes de livraison
	 *
	 *	@return Identifiant de la commande
	 *	@}
	*/
	case 'add':
		if( !isset($_POST['usr'],$_POST['date'],$_POST['state'],$_POST['piece'],$_POST['ref']) ){
			throw new Exception("Paramètres invalide");
		}

		$state_sage = ''; $masked = true; $wst = false;

		if( isset($_POST['masked']) ){
			$masked = (bool) $_POST['masked'];
		}
		if( isset($_POST['state_sage']) && trim($_POST['state_sage']) != '' ){
			$state_sage = $_POST['state_sage'];
		}
		if( isset($_POST['wst']) && is_numeric($_POST['wst']) && $_POST['wst'] > 0 ){
			$wst = $_POST['wst'];
		}

		$ord_id = ord_orders_add_sage( $_POST['usr'], $_POST['date'], $_POST['state'], $state_sage, $_POST['piece'], $_POST['ref'], $masked, $wst );

		if( !$ord_id ){
			throw new Exception("Une erreur est survenue lors de la mise à jour de l'élément");
		}

		update_order($ord_id);

		$result = true;
		$content = array('ord_id' => $ord_id);

		// log les données envoyées par la tablette
		api_log('Commandes '.$config['tnt_id'].' FDV '.$ord_id.' - header add : '.print_r($_REQUEST,true), 'debug');

		break;

	/** @{@}
 	 *	@{
	 *	\page api-orders-index-upd Mise à jour
	 *
	 *	Cette fonction modifie un entête de commande.
	 *
	 *		\code
	 *			PUT /orders/
	 *		\endcode
	 *
	 *	@param int $id Obligatoire, Identifiant de la commande
	 * 	@param int $usr Facultatif, Identifiant de l'utilisateur propriétaire de la commande
	 * 	@param $date Facultatif, Date de la commande
	 * 	@param int $adr_invoices Facultatif, Identifiant de l'adresse de facturation
	 * 	@param int $adr_delivery Facultatif, Identifiant de l'adresse de livraison
	 * 	@param int $seller_id Facultatif, Identifiant de vendeur a attribuer à la commande
	 * 	@param $date-livr Facultatif, Date de livraison prévue de la commande
	 * 	@param int $srv_id Facultatif, Identifiant du service de livraison
	 * 	@param int $dps_id Facultatif, Identifiant du dépôt de stock
	 * 	@param int $pkg_id Facultatif, Identifiant du pacjage
	 * 	@param string $dlv_notes Facultatif, Consignes de livraison
	 * 	@param $ord_notify_key Facultatif, ?????
	 *	@param bool $need_sync Facultatif, Vrai pour indiquer que la commande doit être synchronisée
	 *
	 *	@return int Identifiant de la commande
	 *	@}
	*/
	case 'upd':
		if( !isset($_REQUEST['id']) ){
			throw new Exception("Paramètres invalide");
		}

		update_order($_REQUEST['id']);

		$result = true;

		// log les données envoyées par la tablette
		api_log('Commandes '.$config['tnt_id'].' FDV '.$_REQUEST['id'].' - header upd : '.print_r($_REQUEST,true), 'debug');

		break;
}

///@}
<?php
/**
 * \defgroup orders_seller Représentant de la commande  
 * \ingroup orders
 * @{	 	
 * \page api-orders-seller-upd Mise à jour 
 *
 *	 Cette fonction modifie le représentant de la commande.
 *
 *		\code
 *			PUT /orders/seller/
 *		\endcode
 *	
 * @param $ord Obligatoire, Identifiant de la commande
 * @param int $seller_id Obligatoire, Identifiant du représentant
 *	
 * @return true si le représentant de la commande est correctement modifié
 *
*/

switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['ord'], $_REQUEST['seller_id']) ){
			throw new Exception("Paramètre invalide.");
		}

		if( ord_orders_set_seller_id($_REQUEST['ord'], $_REQUEST['seller_id']) ){
			$result = true;
		}

		break;
}

///@}
<?php

require_once('http.inc.php');
/**
 * \defgroup orders_stats Commandes
 * \ingroup stats 
 * @{	
 * \page api-orders-stats-get Chargement
 *
 *		\code
 *			GET /orders/stats/
 *		\endcode
 *
 * @param string $date1 Obligatoire, date de début de la période à considérer
 * @param string $date2 Obligatoire, date de fin de la période à considérer
 * @param prf 			Facultatif, filtre sur le compte utilisateur
 * @param usr			Facultatif, filtre sur le compte client
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 * 
 *		\code{.json}
 *			{ 
 *				"volume" : nombre de commandes (Web)
 *				"total_ht" : total ht des commandes
 *				"total_ttc" : total ttc des commandes
 *				"average_ht" : panier moyen en ht
 *				"average_ttc" : panier moyen en ttc
 *				"marge" : marge brute
 *			}
 *		\endcode  
 * 
*/

switch( $method ){

	// permet de mettre à jour le total d'une commande
	case 'get':

		// Le résultat est mis en cache pour 15 minutes
		http_cache_control( 900 );

		if(
			!isset($_GET['date1']) || !trim($_GET['date1']) || !isdate($_GET['date1'])
			|| !isset($_GET['date2']) || !trim($_GET['date2']) || !isdate($_GET['date2'])
		){
			throw new Exception("Les dates de début et de fin sont obligatoires (date1 et date2)");
		}

		// Filtre sur le profil utilisateur
		$profiles = 0;
		if( isset($_GET['prf']) && is_array($_GET['prf']) ){
			foreach( $_GET['prf'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants de profils fournis en arguments sont incorrects");
				}
			}
			$profiles = $_GET['prf'];
		}elseif( isset($_GET['prf']) && is_numeric($_GET['prf']) ){
			$profiles = array( $_GET['prf'] );
		}

		// Filtre sur le compte client
		$users = 0;
		if( isset($_GET['usr']) && is_array($_GET['usr']) ){
			foreach( $_GET['usr'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$users = $_GET['usr'];
		}elseif( isset($_GET['usr']) && is_numeric($_GET['usr']) ){
			$users = array( $_GET['usr'] );
		}

		$content = ord_orders_get_average_totals(
			null, ord_states_get_ord_valid(), $_GET['date1'], $_GET['date2'], false, 0, false,
			$users, 0, 0, 0, 0, 0, 0, false, 0, $profiles
		);
		$result = is_array($content);

		break;
}

///@}
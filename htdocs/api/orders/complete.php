<?php
/**
 *	\defgroup orders_complete commande complète
 *	\ingroup orders
 *  @{
 */

// \cond onlyria
// cette fonction permet la mise à jour d'une en-tete de commande
function update_order($raw_data){
    global $method, $config, $is_fdv, $is_sync;

    $obj = json_decode($raw_data);
    $obj = json_decode(json_encode($obj), true);

    if( !isset($obj['head']) || !is_array($obj['head'])
        || !isset($obj['lines']) || !is_array($obj['lines'])
    ){
        throw new Exception("Paramètres d'entrée invalides : les clés head ou lines sont manquantes ou pas de type array");
    }

    $ord = $obj['head'];
    $lines = $obj['lines'];
    $signature = isset($obj['signature']) && is_array($obj['signature']) ? $obj['signature'] : false;
    $installments = isset($obj['installments']) && is_array($obj['installments']) ? $obj['installments'] : false;
    $serials = isset($obj['serials']) && is_array($obj['serials']) ? $obj['serials'] : false;
    $discount = isset($obj['discount']) && is_array($obj['discount']) ? $obj['discount'] : false;

    // controles des paramètres pour la commande
    if( !isset($ord['usr'],$ord['date'],$ord['state'],$ord['piece'],$ord['ref']) ){
        throw new Exception("Paramètres de commande invalides : les clés usr, date, state, piece et ref sont obligatoires");
    }

    // la synchro va parfois envoyer le real_usr en plus de l'autre, cas des écritures des commandes sous le meme code client
    if( isset($ord['real_usr']) && $ord['real_usr'] > 0 ){
        $ord['usr'] = $ord['real_usr'];
    }

    // controles des paramètres pour les lignes
    foreach( $lines as $line ){
        if( !isset($line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['datelivr']) ){
            throw new Exception('Paramètres de lignes invalide : les clés prd, line, ref, name, qte, price_ht, tva et datelivr sont obligatoires');
        }
    }

    // création / mise à jour de l'entete de commande
    if( $method == "add" ){
        $wst = false;
        if( isset($ord['wst']) && is_numeric($ord['wst']) && $ord['wst'] > 0 ){
            $wst = $ord['wst'];
        }

        $ord_id = 0;

        // dans le cas d'un ajout, on check si la commande n'aurait pas déjà été créé avec cette id
        if( $is_fdv && isset($ord['id'])){
            $ord_id = ord_orders_get_by_author_device($config['dev_id'], $ord['id'], $ord['usr']);
        }

        if( !$ord_id ){

            // si on donne un ord_id + piece et que la gescom est navision alors on ne conserve pas la piece
            if( isset($config['sync_global_gescom_type'])
                && $config['sync_global_gescom_type']==GESCOM_TYPE_DYNAMICS_NAVISION
                && isset($ord['id']) && $ord['id'] > 0
                && isset($ord['piece']) && $ord['piece']!=''
            ){
                $id = ord_orders_get_id($ord['piece']);
                if( $id > 0 ){
                    $ord['piece'] = '';
                }
            }

            $ord_id = ord_orders_add_sage( $ord['usr'], $ord['date'], $ord['state'], '', $ord['piece'], $ord['ref'], true, $wst );

            if( !$ord_id ){
                throw new Exception("Une erreur est survenue lors de la création de l'entete de commande : ord_orders_add_sage");
            }

            // dans le cas d'une tablette on va renseigner la personne qui a créer ce devis
            if( isset($config['usr_id']) && $config['usr_id'] ){
                ord_orders_states_add( $ord_id, _STATE_DEVIS, $config['usr_id'] );
            }

            // dans le cas des devis passé hors tablette on ajoute les datas sur pipe commercial
            if( !isset($config['dev_id']) || $config['dev_id'] <= 0 ){
                if( $ord['state'] == _STATE_DEVIS ){
                    // valeur par défaut en dur pour le moment, à voir plus tard pour faire une variable de config 1 = 10%
                    fld_object_values_set($ord_id, _FLD_ORD_SIGN_RATE, 1);
                    fld_object_values_set($ord_id, _FLD_ORD_PIPE_SIGN_DATE, date('Y-m-d H:i:s', strtotime($ord['date'].' +1 MONTH')));
                }
            }

            // met à jour les information pour eviter les doublons
            if( $is_fdv && isset($ord['id'])){
                ord_orders_set_author_device($ord_id, $config['dev_id'], $ord['id']);
            }
        }

    }else{
        $ord_id = $ord['id'];

        // masque la commande
        ord_orders_unmask($ord_id, true, true);
    }

    // mise à jour de la référence de la commande
    if( isset($ord['ref']) ){
        if( !ord_orders_ref_update($ord_id, $ord['ref']) ){
            throw new Exception("Erreur dans la mise à jour de la référence de la commande : ".$ord['ref']);
        }
    }

    // mise à jour de la date de commande
    if( isset($ord['date']) ){
        if( isdate($ord['date']) ){ // historique ce test ne devrait plus être fait si toutes les tablettes sont en version 14+
            $ord['date'] = $ord['date']." 00:00:00";
        }
        if( !ord_orders_set_date($ord_id, $ord['date'], true) ){
            throw new Exception("Erreur dans la mise à jour de la date de commande : ".$ord['date']);
        }
    }

    // mise à jour de l'utilisateur
    if( isset($ord['usr']) && $ord['usr'] > 0 ){
        if( !ord_orders_update_user($ord_id, $ord['usr'], false) ){
            throw new Exception("Erreur dans la mise à jour de l'utilisateur : ".$ord['usr']);
        }
    }

    // mise à jour de l'adresse de facturation
    if( isset($ord['adr_invoices']) && $ord['adr_invoices'] >0){
        if( !ord_orders_adr_invoices_set($ord_id, $ord['adr_invoices']) ){
            throw new Exception("Erreur dans la mise à jour de l'adresse de facturation : ".$ord['adr_invoices'] );
        }
    }

    $__rord = ord_orders_get_simple( ['id'=>$ord_id], [], ['include_masked' => true] );
    $__str_id = $__adr_id = $__rly_id = 0;

    if( ria_mysql_num_rows($__rord) ){
        $__ord = ria_mysql_fetch_assoc($__rord);

        $__str_id = is_numeric($__ord['str_id']) && $__ord['str_id'] > 0 ? $__ord['str_id'] : 0;
        $__rly_id = is_numeric($__ord['rly_id']) && $__ord['rly_id'] > 0 ? $__ord['rly_id'] : 0;
        $__adr_id = is_numeric($__ord['dlv_id']) && $__ord['dlv_id'] > 0 ? $__ord['dlv_id'] : 0;

    }

    // mise à jour de l'adresse de livraison sauf en cas d'une livraison en point relai
    if( !$__rly_id ){
        if( !$__adr_id && !$__str_id ){

            if( isset($ord['str_id']) && is_numeric($ord['str_id']) && $ord['str_id'] > 0 ){
                if( !ord_orders_set_dlv_store( $ord_id, $ord['str_id']) ){
                    throw new Exception("Erreur dans la mise à jour du magasin de livraison : ".$ord['str_id']);
                }
                ord_orders_set_relay($ord_id, null);
            }else{
                $dlv_adr = isset($ord['adr_delivery']) && is_numeric($ord['adr_delivery']) && $ord['adr_delivery'] > 0 ? $ord['adr_delivery'] : false;

                if( !ord_orders_adr_delivery_set($ord_id, $dlv_adr) ){
                    throw new Exception("Erreur dans la mise à jour de l'adresse de livraison : ".$dlv_adr );
                }
                ord_orders_set_dlv_store($ord_id, false);
                ord_orders_set_relay($ord_id, null);
            }

        }else{

            if( isset($ord['str_id']) && is_numeric($ord['str_id']) && $ord['str_id'] > 0 ){
                if( !ord_orders_set_dlv_store( $ord_id, $ord['str_id']) ){
                    throw new Exception("Erreur dans la mise à jour du magasin de livraison : ".$ord['str_id']);
                }
                ord_orders_set_relay($ord_id, null);
            }elseif( isset($ord['adr_delivery']) && is_numeric($ord['adr_delivery']) && $ord['adr_delivery'] > 0 ){

                if( !ord_orders_adr_delivery_set($ord_id, $ord['adr_delivery']) ){
                    throw new Exception("Erreur dans la mise à jour de l'adresse de livraison : ".$ord['adr_delivery'] );
                }
                ord_orders_set_dlv_store($ord_id, false);
                ord_orders_set_relay($ord_id, null);
            }
        }
    }

    // mise à jour de l'adresse de livraison
    // if( isset($ord['adr_delivery']) ){
    // 	if( !ord_orders_adr_delivery_set($ord_id, $ord['adr_delivery']==0 ? false : $ord['adr_delivery']) ){
    // 		throw new Exception("Erreur dans la mise à jour de l'adresse de livraison : ".( $ord['adr_delivery']==0 ? false : $ord['adr_delivery'] ) );
    // 	}
    // }

    // mise à jour du revendeur
    // if( isset($ord['str_id']) ){
    // 	if( !ord_orders_set_dlv_store( $ord_id, $ord['str_id']) ){
    // 		throw new Exception("Erreur dans la mise à jour du revendeur : ".$ord['str_id']);
    // 	}
    // }

    // mise à jour du seller id
    if( isset($ord['seller_id']) ){
        $seller = $ord['seller_id'] > 0 ? $ord['seller_id'] : null;
        if( !ord_orders_set_seller_id( $ord_id, $seller ) ){
            throw new Exception("Erreur dans la mise à jour du seller id : ".$ord['seller_id']);
        }
    }

    // mise à jour de la date de livraison
    if( isset($ord['date-livr']) ){
        if( !ord_orders_set_date_livr( $ord_id, $ord['date-livr']) ){
            throw new Exception("Erreur dans la mise à jour de la date de livraison : ".$ord['date-livr']);
        }
    } else if( isset($ord['date_livr']) ){
        if( !ord_orders_set_date_livr( $ord_id, $ord['date_livr']) ){
            throw new Exception("Erreur dans la mise à jour de la date de livraison : ".$ord['date_livr']);
        }
    }

    // mise à jour du service de livraison
    if( isset($ord['srv_id']) ){
        if( !ord_orders_set_dlv_service( $ord_id, $ord['srv_id']) ){
            throw new Exception("Erreur dans la mise à jour du service de livraison : ".$ord['srv_id']);
        }
    }

    // mise à jours du contact
    if( isset($ord['contact_id']) ){
        if( !ord_orders_set_contact_id( $ord_id, $ord['contact_id']) ){
            throw new Exception("Erreur dans la mise à jour du contact : ".$ord['contact_id']);
        }
    }

    // mise à jours du contact
    if( isset($ord['reseller_id']) ){
        if( !ord_orders_set_reseller_id( $ord_id, $ord['reseller_id']) ){
            throw new Exception("Erreur dans la mise à jour du revendeur : ".$ord['reseller_id']);
        }
    }

    // mise à jours de la monnaie
    if( isset($ord['currency']) && $ord['currency'] ){
        if( !ord_orders_set_currency( $ord_id, $ord['currency']) ){
            throw new Exception("Erreur dans la mise à jour de la monnaie : ".$ord['currency']);
        }
    }

    // mise à jours du contact du revendeur
    if( isset($ord['reseller_contact_id']) ){
        if( !ord_orders_set_reseller_contact_id( $ord_id, $ord['reseller_contact_id']) ){
            throw new Exception("Erreur dans la mise à jour du contact du revendeur : ". $ord['reseller_contact_id'] );
        }
    }

    // mise à jour du dépot
    if( isset($ord['dps_id']) ){
        if( !is_numeric($ord['dps_id']) ) $ord['dps_id'] = 0;

        if( !ord_orders_set_deposit( $ord_id, $ord['dps_id']) ){
            throw new Exception("Erreur dans la mise à jour du dépot de livraison : ".$ord['dps_id']);
        }
    }

    // mise à jour du package
    if( isset($ord['pkg_id']) ){
        if( !is_numeric($ord['pkg_id']) ) $ord['pkg_id'] = 0;

        if( !ord_orders_set_package( $ord_id, $ord['pkg_id']) ){
            throw new Exception("Erreur dans la mise à jour du package de livraison : ".$ord['pkg_id']);
        }
    }

    // mise à jour des notes
    if( isset($ord['dlv_notes']) ){
        if( !ord_orders_dlv_notes_set( $ord_id, $ord['dlv_notes']) ){
            throw new Exception("Erreur dans la mise à jour d'une note sur la commande : ".$ord['dlv_notes']);
        }
    }

    // mise à jour du paiement
    if( isset($ord['pay_id']) ){
        if( !ord_orders_pay_type_set( $ord_id, $ord['pay_id']) ){
            throw new Exception("Erreur dans la mise à jour d'un paiement : ".$ord['pay_id']);
        }
    }elseif( $is_fdv && !ord_orders_pay_type_set( $ord_id, _PAY_EMPTY) ){
        throw new Exception("Erreur dans la mise à jour d'un paiement : _PAY_EMPTY");
    }

    // mise à jour du token d'auth d'envoi d'email
    if( isset($ord['ord_notify_key']) ){
        fld_object_values_set( $ord_id, _FLD_ORD_NOTIFY_KEY, $ord['ord_notify_key'] );
    }

    // controles de lignes
    $lines_in = array();
    foreach( $lines as $line ){
        $lines_in[] = $line['prd'].'-'.$line['line'];

        $group_id = false;
        if( isset($line['group_id']) && is_numeric($line['group_id']) ){
            $group_id = $line['group_id'];
        }

        $group_parent_id = false;
        if( isset($line['group_parent_id']) && is_numeric($line['group_parent_id']) ){
            $group_parent_id = $line['group_parent_id'];
        }

        // le produit est une interligne
        if( $line['prd'] == 0 ){
            $ordspace = ord_products_get_spacing($ord_id, $line['line']);
            if(!($ordspace && ria_mysql_num_rows($ordspace))){
                if( !ord_products_add_spacing($ord_id, false, $line['line']) ){
                    throw new Exception("Erreur lors de la création de la ligne interligne : ".$line['line']." - ".$line['notes']);
                }
            }
            $position = false;
            if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
                $position = $line['pos'];
            }
            if( !ord_products_update_spacing($ord_id, $line['line'], $line['notes'], $position) ){
                throw new Exception("Erreur lors de l'enregistrement de la ligne interligne : ".$line['line']." - ".$line['notes']);
            }
            if($group_id !== false){
                if(!ord_products_set_group_id($ord_id, 0, $line['line'], $group_id, $group_parent_id)){
                    throw new Exception("Erreur lors de l'enregistrement du group de la ligne interligne : ".$line['line']." - ".$line['notes']);
                }
            }
            continue;
        }

        $parent = false;
        if( isset($line['parent']) && is_numeric($line['parent']) ){
            $parent = $line['parent'];
        }

        $child_line = false;
        if( isset($line['child_line']) && is_numeric($line['child_line']) ){
            $child_line = $line['child_line'];
        }

        $position = null;
        if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
            $position = $line['pos'];
        }

        $cod_id = false;
        if( isset($line['cod_id']) && is_numeric($line['cod_id']) ){
            $cod_id = $line['cod_id'];
        }

        $purchase_avg = null;
        if( isset($line['purchase_avg']) && is_numeric($line['purchase_avg']) ){
            $purchase_avg = $line['purchase_avg'];
        }

        $date_created = null;
        if( isset($line['date_created']) && isdateheure($line['date_created']) ){
            $date_created = $line['date_created'];
        }

        $ecotaxe = 0;
        if( isset($line['ecotaxe']) && is_numeric($line['ecotaxe']) ){
            $ecotaxe = $line['ecotaxe'];
        }

        // la date de livraison est avec les horaires par défaut et la date est en fr pour la fonction add_sage
        if( isdateheure($line['datelivr']) && $line['datelivr'] != '1970-01-01' && $line['datelivr'] != '0000-00-00 00:00:00' ){
            $line['datelivr'] = date('d/m/Y', strtotime($line['datelivr']));
        }else{
            $line['datelivr'] = "";
        }

        if( $line['price_ht'] == 0 && $line['price_ttc']> 0 ){
            $line['price_ttc'] = 0;
        }

        if( !prd_products_exists($line['prd']) ){
            // pour le moment si le produit à été supprimé entre le passage de la commande et maitnenant on est toléreant et on l'ajoute pas.
            // Attention cela peut, peut être poser des soucis à l'avenir pour certain client
            // cela à été fait pour limité les erreurs et intervention manuel.
            error_log(__FILE__.':'.__LINE__.' Un produit supprimé a voulu être ajouté a une commande : '.print_r($line, true));
            continue;
        }

        $rord = ord_products_get($ord_id, false, $line['prd'], '', $line['line'], false, 1);
        if( $rord && ria_mysql_num_rows($rord) ){
            $riaprd = ria_mysql_fetch_assoc($rord);

            // je suis yuto et le produit est "line_is_sync" alors pas de mise à jour possible, conflit avec la sync
            if( !$is_sync && $riaprd['line_is_sync'] ){
                continue;
            }

            $res = ord_products_update_sage( $ord_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['datelivr'],$line['price_ttc'],$ecotaxe,$parent,$child_line,$cod_id,$purchase_avg,$date_created,$position,false);
        }else{
            $res = ord_products_add_sage( $ord_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['datelivr'],$line['price_ttc'],$ecotaxe,$parent,$child_line,$cod_id,$purchase_avg,$date_created,$position,false);
        }

        if( !$res ){
            throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
        }

        if($group_id !== false){
            if(!ord_products_set_group_id($ord_id, $line['prd'], $line['line'], $group_id, $group_parent_id)){
                throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
            }
        }

        if( isset($line['col']) && is_numeric($line['col']) ){
            fld_object_values_set( array( $ord_id, $line['prd'], $line['line'] ), _FLD_PRD_COL_ORD_PRODUCT, $line['col'] );
        }

        $weight = 0;
        if( isset($line['weight']) && is_numeric($line['weight']) && $line['weight']>0 ) $weight = $line['weight'];
        if( $weight ){
            fld_object_values_set( array( $ord_id, $line['prd'], $line['line'] ), _FLD_ORD_LINE_WEIGHT, $weight );
        }

        if( isset($line['discount']) && is_numeric($line['discount']) ){
            fld_object_values_set( array( $ord_id, $line['prd'], $line['line'] ), _FLD_ORD_LINE_DISCOUNT, $line['discount'] );
        }

        if( isset($line['price_brut']) ){
            $line['price_brut'] = str_replace(array(',', ' '), array('.', ''), $line['price_brut']);
            if( is_numeric($line['price_brut']) ){
                fld_object_values_set( array( $ord_id, $line['prd'], $line['line'] ), _FLD_PRD_ORD_PRICE_BRUT, $line['price_brut'] );
            }
        }

        if( isset($line['free']) && $line['free']==1 ){
            fld_object_values_set( array( $ord_id, $line['prd'], $line['line'] ), _FLD_PRD_ORD_FREE, 'Oui' );
        }

        if( isset($line['notes']) ){
            ord_products_notes_update($ord_id, $line['prd'], $line['notes'], $line['datelivr'], $line['line'] );
        }

        // tous les produits en provenance de la sync doivent être marqué comme synchronisé
        if( $is_sync || (isset($line['line_is_sync']) && $line['line_is_sync']) ){
            ord_products_set_line_is_sync($ord_id, $line['prd'], $line['line'] );
        }

        // ajout des champs avancés
        if(isset($line['fields'])) {
            $fields_delete_missing = (isset($line['fields_delete_missing']) && !$line['fields_delete_missing']) ? false : true;
            fields_sync(CLS_ORD_PRODUCT, $ord_id, $line['prd'], $line['line'], $line['fields'], $fields_delete_missing);
        }
    }

    // retire les lignes produits non présente dans celle envoyé par la tablette
    $rord = ord_products_get($ord_id);
    if( $rord ){
        while( $prd = ria_mysql_fetch_assoc($rord) ){
            $key = $prd['id'].'-'.$prd['line'];
            if( !in_array($key, $lines_in) ){
                if( $prd['id']==0 ){
                    if( !ord_products_del_spacing($ord_id,$prd['line']) ){
                        throw new Exception("Erreur lors de la suppression de l'interligne : ord_products_del_spacing");
                    }
                }else{
                    if( !ord_products_del($ord_id,$prd['id'],$prd['line']) ){
                        throw new Exception("Erreur lors de la suppression du produit : ord_products_del");
                    }
                }
            }
        }
    }

    // ajout des champs avancés
    if(isset($ord['fields'])) {
        $fields_delete_missing = (isset($ord['fields_delete_missing']) && !$ord['fields_delete_missing']) ? false : true;
        fields_sync(CLS_ORDER, $ord_id, 0, 0, $ord['fields'], $fields_delete_missing);
    }

    // ajout des modeles de saisie
    if(isset($ord['models'])) {
        $models_delete_missing = (isset($ord['models_delete_missing']) && !$ord['models_delete_missing']) ? false : true;
        models_sync(CLS_ORDER, $ord_id, 0, 0, $ord['models'], $models_delete_missing);
    }

    // mise à jour des totaux
    
    if($is_fdv){
        ord_orders_set_totals($ord_id,$ord['total_ht'],$ord['total_ttc'],$ord['products']);
    }
    else{
        ord_orders_update_totals($ord_id);
    }

    // démaske la commande
    ord_orders_unmask($ord_id, false, true);

    // ajout de la signature
    if( $signature && isset($signature['signature']) ){
        $usr_id = null;
        if( isset($signature['usr_id']) && is_numeric($signature['usr_id']) ){
            $usr_id = $signature['usr_id'];
        }

        $firstname = null;
        if( isset($signature['firstname']) && $signature['firstname'] != "" ){
            $firstname = $signature['firstname'];
        }

        $lastname = null;
        if( isset($signature['lastname']) && $signature['lastname'] != "" ){
            $lastname = $signature['lastname'];
        }
        $function = null;
        if( isset($signature['function']) && $signature['function'] != "" ){
            $function = $signature['function'];
        }

        ord_orders_signature_add( $ord_id, $signature['signature'], $usr_id, $firstname, $lastname, $function );
    }

    // ajout de l'échéancier
    if( $installments ){

        $traited = array();

        for($i=0; $i < sizeof($installments); $i++){

            if( !isset($installments[$i]['id']) || !isset($installments[$i]['type_id']) || !isset($installments[$i]['piece_id']) ||
                !isset($installments[$i]['pay_id']) || !isset($installments[$i]['amount_total']) || !isset($installments[$i]['amount_rest']) ||
                !isset($installments[$i]['transaction_id']) || !isset($installments[$i]['date_created']) || !isset($installments[$i]['date_expired']) || !isset($installments[$i]['state']) ){
                throw new Exception("Les données sur l'échéancier ne sont pas complète : ".print_r($installments[$i], true));
            }

            if( !isset($installments[$i]['is_deadline']) ){
                $installments[$i]['is_deadline'] = null;
            }
            if( !isset($installments[$i]['amount_percent']) ){
                $installments[$i]['amount_percent'] = null;
            }
        }

        // vide l'échéancier de la commande pour tous reconstruire
        $traited_key = array();
        $rdel = ord_installments_get( 0, 1, $ord_id, false, false );
        if( $rdel && ria_mysql_num_rows($rdel) ){
            while( $del = ria_mysql_fetch_assoc($rdel) ){

                $exists = false;
                if( sizeof($installments) > 0 ){
                    foreach( $installments as $installment ){

                        if( $installment['id'] == $del['id']){
                            $traited_key[] = $del['id'];

                            if( !ord_installments_upd( $installment['id'], $installment['pay_id'], $installment['amount_total'], $installment['amount_rest'], $installment['date_created'], $installment['date_expired'], $installment['transaction_id'], $installment['state'], $installment['is_deadline'], $installment['amount_percent']) ){

                                throw new Exception("Erreur lors de la mise à jour de l'échéancier : ".print_r($installment, true));
                            }

                            $exists = true;
                        }
                    }
                }

                if( !$exists ){
                    ord_installments_del($del['id']);
                }
            }
        }

        if( sizeof($installments) > 0 ){
            foreach( $installments as $installment ){
                if( in_array($installment['id'], $traited_key) ) continue;

                if( !ord_installments_add( $installment['pay_id'], $installment['type_id'], $ord_id, $installment['amount_total'], $installment['amount_rest'], $installment['date_created'], $installment['date_expired'], $installment['transaction_id'], false, $installment['state'], $installment['is_deadline'], $installment['amount_percent']) ){

                    throw new Exception("Erreur lors de la création de l'échéancier : ".print_r($installment, true));
                }
            }
        }
    }

    // ajout de les serials
    if ($serials !== false) {
        serials_sync(CLS_ORD_PRODUCT, $ord_id, false, false, $serials);
    }

    // ajout de la remiseS
    if( $discount ){

        // permet d'appliquer une remise sur l'entete de commande
        $force_off = false;
        if( isset($discount['discount'], $discount['discount_type'], $discount['apply_on']) ){
            if( is_numeric($discount['discount']) && is_numeric($discount['discount_type']) && in_array($discount['apply_on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ){
                $force_off = array(
                    'discount' => $discount['discount'],
                    'discount_type' => $discount['discount_type'],
                    'apply_on' => $discount['apply_on']
                );
            }
        }

        ord_orders_update_totals($ord_id, $force_off);
    }

    // mise à jour du code promotion
    if( isset($ord['pmt_id']) ){
        $code = pmt_codes_get_code($ord['pmt_id']);
        if( trim($code) ){
            pmt_codes_apply_forced( $code, $ord_id );
        }
    }

    // Mise à jour du need sync
    if( isset($ord['need_sync']) && $ord['need_sync']==1 ){
        ord_orders_set_need_sync($ord_id);
    }

    // permet la regénération forcé du fichier pdf, fait à la fin du traitement pour éviter des pertes d'informations.
    if( isset($config['devis_pdf_need_refresh']) ){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $config['devis_pdf_need_refresh']."&regenerate=1");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $data = curl_exec($ch);
        curl_close($ch);
    }

    // si ma commande est à l'état de devis et n'a pas de produit on la retire directement.
    // if( $ord['state']==_STATE_DEVIS ){
    // 	$rord = ord_products_get($ord_id);
    // 	if( !$rord || !ria_mysql_num_rows($rord) ){
    // 		ord_orders_state_update( $ord_id, _STATE_CANCEL_MERCHAND);
    // 		ord_orders_unmask($ord_id, true, true);
    // 	}
    // }

    // log les données envoyé par la tablette
    $obj['ria_ord_id'] = $ord_id;
    $obj['ria_action'] = $method;
    api_log(json_encode($obj), 'api-orders');

    return $ord_id;
}
// \endcond

switch( $method ){
    /** @{@}
     *	@{
     * 		\page api-orders-complete-get Récupère toutes les données d'un pièce de ventes (devis, commande).
     *		\code
     *			GET /orders/complete/
     *		\endcode
     *
     *	@return json Les données suivantes :
     *		\code{.json}
     * 			{
     *				"user": (int) Identifiant du compte client,
     *				"id": (int) Identifiant de la pièce,
     *				"ref": (string) Référence personnalisé de la pièce,
     *				"piece": (string) Numéro de la pièce dans la gestion commerciale,
     *				"date_en": (datetime) Date de validation,
     *				"state_id": (int) Identifiant du statut,
     *				"state_name": (string) Intitulé du statut,
     *				"products": (int) Nombre de produits présents dans la pièce,
     *				"total_ht": (float) Total hors taxes,
     *				"total_ttc": (float) Total toutes taxes comprises,
     *				"adr_invoices": (int) Identifiant de l'adresse de facturation,
     *				"adr_delivery": (int) Identifiant de l'adresse de livraison,
     *				"srv_id": (int) Identifiant du service de livraison,
     *				"str_id": (int) Identifiant du magasin (soit de livraison, soit rattaché),
     *				"dlv-notes": (string) Commentaire de livraison,
     *				"opt-gift": (tinyint) Option cadeau choisi (0 : Non, 1 : Oui),
     *				"opt-gift-message": (string) Message lorsque l'option cadeau est choisi,
     *				"pay_id": (int) Identifiant du moyen de paiement,
     *				"card_id": (int) Identifiant du type de carte bleu utilisé (paiement par CB uniquement),
     *				"pmt_id": (int) Identifiant de la promotion rattachée,
     *				"seller_id": (int) Identifiant du représentant,
     *				"comments": (string) Commentaire libre,
     *				"reliquats": (tinyint) En reliquats (0 : Non, 1 : Oui),
     *				"ord_livr": (datetime) Date de livraison prévue de la commande,
     *				"is_sync": (tinyint) Est synchronisée avec la gestion commercie (0 : Non, 1 : Oui),
     *				"total_ht_delivered": (float) Montant HT des articles déjà livrés,
     *				"dps_id": (int) Identifiant du dépôt,
     *				"pkg_id": (int) Identifiant du type de package des articles,
     *				"age": (int) Nombre de jours depuis sa validation,
     *				"relanced": (tinyint) Détermine si le client de la commande doit être relancé pour paiement complémentaire (0 : Non, 1 : Oui),
     *				"alert-livr": (tinyint) Détermine si une alerte email pour les produits en rupture de la commande doit être envoyée (0 : Non, 1 : Oui),
     *				"rly_id": (int) Identifiant du point relai choisi pour la livraison,
     *				"parent": (int) Identifiant compte parent si la pièce est lié à un compte enfant,
     *				"wst_id": (int) Identifiant du site à l'origine,
     *				"date_archived": (datetime) Date d'archivage de la pièce,
     *				"masked": (tinyint) Détermine si la pièce est masquée (0 : Non, 1 : Oui),
     *				"date_modified": (datetime) Date de dernière mise à jour,
     *				"parent_id": (int) Identifiant de la pièce parente,
     *				"contact_id": (int) Identifiant du contact,
     *				"currency": (string) Code ISO 4217 de la devise,
     *				"reseller_id": (int) Identifiant du représentant lié à la pièce,
     *				"reseller_contact_id": (int) Identifiant du contact revendeur qui à prit la commande,
     *				"field_values": (array) Liste des champs avancés liés à la pièce, et pour chacun :
     *				  {
     *						"obj_value": (string) Valeur du champ,
     *						"lng_code": (string) Code de la langue,
     *						"obj_id_0": (int) Première partie de l'identifiant de l'objet,
     *						"obj_id_1": (int) Seconde partie de l'identifiant de l'objet,
     *						"obj_id_2": (int) Troisième partie de l'identifiant de l'objet,
     *						"id": (int) Identifiant du champ avancé,
     *						"type_id": (int) Identifiant du type de champ (cf. fld_types)
     *				  }
     *				"related": (array) Liste des objets liés à la pièce
     *					"products": (array) Liste des articles présents dans la pièce, et pour chacun : [
     *					{
     *						"data": {
     *								"ord_id": (int) Identifiant de la pièce,
     *								"id": (int) Identifiant du produit,
     *								"line": (int) Identifiant de la ligne (0 est possible),
     *								"ref": (string) Référence du produit,
     *								"name": (string) Nom du produit,
     *								"qte": (float) Quantité présente,
     *								"title": (string) Nom du produit,
     *								"prd_pos": (int) Position du produit dans la pièce,
     *								"desc": (string) Description du produit,
     *								"price_ht": (float) Prix unitaire hors taxe du produit,
     *								"tva_rate": (float) Taux de tva appliqué (1.000 pour aucune TVA),
     *								"price_ttc": (float) Prix unitaire toutes taxes comprises,
     *								"weight_ht": (float) Prix hors taxe au kilo,
     *								"weight_ttc": (float) Prix toutes taxes comprises au kilo,
     *								"total_ht": (float) Total hors taxe de la ligne,
     *								"total_ttc": (float) Total toutes taxes comprises de la ligne,
     *								"notes": (string) Commentaire sur la ligne (vide par défaut),
     *								"date_livr_en": (datetime) Date de livraison estimé du produit (null par défaut),
     *								"publish": (tinyint) Détermine si le produit est publié (0 : Non - par défaut, 1 : Oui),
     *								"weight": (int) Poids brut du produit (en gramme, null par défaut),
     *								"weight_net": (int) Poids net du produit (en gramme, null par défaut),
     *								"img_id": (int) Identifiant de l'image principale du produit (null par défaut),
     *								"date_created": (datetime) Date de création de la ligne,
     *								"date_modified": (datetime) Date de dernière mise à jour,
     *								"parent": (int) Identifiant du parent si la ligne est un composant de nomenclature variable (null par défaut),
     *								"child-line": (int) rang de la nomenclature si plusieurs existent dans la pièce (null par défaut),
     *								"group_id": (int) Identifiant du groupe (null par défaut),
     *								"group_parent_id": (int) Identifiant du parent du groupe (null par défaut),
     *								"ecotaxe": (float) Montant de l'ecotaxe,
     *								"is_bookmark": (tinyint) Détermine si le produit est un favori (0 : Non - par défaut, 1 : Oui),
     *								"sell_weight": (tinyint) Détermine si la quantité pour la ligne est un poids ou un volume (dans la plus petite unité possible, 0 : Non, 1 : Oui),
     *								"col_id": (int) Identifiant d'un conditionnement éventuel (0 si pas de contionnement),
     *								"col_qte": (float) Quantité en conditionnement (1 par défaut),
     *								"col_name": (string) Nom du colisage (par défaut chaine vide),
     *								"orderable": (tinyint) Détermine si le produit est commandable (0 : Non, 1 : Oui),
     *								"weight_net_total": (int) Poids net total (en gramme),
     *								"perishable": (tinyint) Détermine si le produit est périssable ou non (0 : Non - par défaut, 1 : Oui),
     *								"opt": (int) Identifiant de l'option pour une ligne de nomenclature variable (null par défaut),
     *								"real_qte": (float) Quantité permettant le passage de price_ht à price_ttc (combinaison de qte, col_qte et sell_weight),
     *								"cod": (int) Identifiant de la promotion ayant ajouté cette ligne (null par défaut),
     *								"type_promo": (int) Type de promotion (produit offert, X acheté Y offert, ... - null par défaut),
     *								"ord_child_id": (int) Identifiant de la commande enfant à laquelle la ligne (null par défaut),
     *								"pos": (int) alias de la position de la ligne,
     *								"barcode": (string) Code barre du produit (null par défaut),
     *								"line_dps_id": (int) Identifiant du dépôt utilisé sur la ligne de commande (null par défaut),
     *								"purchase_avg": (float) Prix d'achat du produit (null par défaut),
     *								"sell_points": (int) Nombre de points utilisé pour la ligne de commande (null par défaut),
     *								"brd_name": (string) Intitulé de la marque (null par défaut),
     *								"ref_gescom": (string) Référence gescom du produit (null par défaut),
     *								"discount": (float) Montant de la remise à la ligne,
     *								"discount_type": (int) Type de remise,
     *								"line_is_sync": (tinyint) Détermine si la ligne est synchronisé avec la gestion commercial (0 : Non, 1 : Oui),
     *								"marge": (float) Marge réalisé sur la ligne (null par défaut)
     *							},
     *							"field_values": (array) Liste des champs avancés liés à la pièce, et pour chacun :
     *				  		{
     *								"obj_value": (string) Valeur du champ,
     *								"lng_code": (string) Code de la langue,
     *								"obj_id_0": (int) Première partie de l'identifiant de l'objet,
     *								"obj_id_1": (int) Seconde partie de l'identifiant de l'objet,
     *								"obj_id_2": (int) Troisième partie de l'identifiant de l'objet,
     *								"id": (int) Identifiant du champ avancé,
     *								"type_id": (int) Identifiant du type de champ (cf. fld_types)
     *				  		}
     *							"related": {
     *							  "prd-promo": (array) Promotion(s) appliquée(s) sur la ligne, et pour chacune : [
     *									{
     *										"data": {
     *											"ord_id": (int) Identifiant de la pièce,
     *											"prd_id": (int) Identifiant du produit,
     *											"line_id": (int) Identifiant de la ligne (0 est possible),
     *											"cod_id": (int) Identifiant de la promotion ayant ajouté cette ligne
     *										}
     *									}
     *								]
     *							}
     *				    }
     *				  ],
     *				  "signature": (string) Points permettant de dessiner la signature,
     *				  "serials": (array) Liste des numéros de lots dans la pièce, et pour chacun [
     *						{
     *							"data": {
     *								"cls_id": (int) Identifiant de la classe,
     *								"obj_id_0": (int) Identifiant 1 de l'objet,
     *								"obj_id_1": (int) Identifiant 2 de l'objet,
     *								"obj_id_2": (int) Identifiant 3 de l'objet,
     *								"date_dlc": (datetime) Date limite de consomation,
     *								"date_production": (datetime) Date de production,
     *								"dps_id": (int) Identifiant du dépot,
     *								"serial": (string) Numéro de serie,
     *								"qte": (int) Quantité (null par défaut),
     *								"ref_gescom": (string) Référence de la gescom
     *							}
     *						}
     *					],
     *				  "installments": (array) Liste des accomptes versés [
     *						{
     *							"data": {
     * 								"id": (int) Identifiant de l'acompte,
     * 								"deleted": (tinyint) Détermine si l'acompte est supprimé (0 : Non, 1 : Oui),
     * 								"type": (int) Identifiant du type de pièce auquel l'acompte est rattaché,
     * 								"piece": (int) Pièce à laquelle l'acompte est rattaché,
     * 								"pay": (int) Identifiant du moyen de paiement de l'acompte,
     * 								"total": (float) Montant total de l'acompte,
     * 								"rest": (float) Montant non facturé,
     * 								"spend": (float) Montant facturé,
     * 								"transaction": (int) Identifiant de la transaction SPIS (null par défaut),
     * 								"date": (datetime) Date de création de l'acompte,
     * 								"expire": (datetime) Date d'expiration de l'acompte,
     * 								"state": (tinyint) Etat de l'accompte (0 : non versé, 1 : Versé),
     * 								"is_deadline": (tinyint) Détermine s'il s'agit d'une ligne d'échéancier (0 : Non - par défaut, 1 : Oui),
     * 								"amount_percent": (float) % de l'échéance si "is_deadline" à 1
     *							}
     *						}
     *					],
     *				  "states": (array) Liste des status de la pièce (historique), et pour chacun [
     *				    {
     *							"data": {
     *							  "ord_id": (int) Identifiant de la pièce,
     *							  "state_id": (int) Identifiant du statut,
     *							  "state_name": (string) Intitulé du statut,
     *							  "date_en": (datetime) Date de passage au statut,
     *							  "usr_id": (int) Identifiant de l'auteur du changement (null par défaut),
     *							}
     *				    }
     *				  ]
     *				},
     *				"linked": (array) Liste des informations complémentaires {
     *				  "user": (array) Informations sur le compte client [
     *				    {
     * 							"id": (int) Identifiant de l'utilisateur dans la base de données (ou tableau)
     * 							"ref": (string) Code client dans le logiciel de gestion commerciale
     * 							"society": (string) Nom de la société (professionnels uniquement)
     * 							"siret": (string) Numéro de siret
     * 							"phone": (string) Numéro de téléphone
     * 							"fax": (string) Numéro de fax
     * 							"mobile": (string) Numéro de téléphone portable
     * 							"work": (string) Numéro de téléphone dans la journée (travail)
     * 							"title_name": (string) Civilité de l'utilisateur (Monsieur, Madame, Mademoiselle)
     * 							"adr_firstname": (string) Prénom de l'utilisateur
     * 							"adr_lastname": (string) Nom de l'utilisateur
     * 							"address1": (string) Première partie de l'adresse
     * 							"address2": (string) Deuxième partie de l'adresse
     * 							"address3": (string) Troisième partie de l'adresse
     * 							"zipcode": (string) Code postal
     * 							"city": (string) Ville de l'adresse de facturation
     * 							"country": (string) Pays de l'adresse de facturation
     * 							"cnt_code": (string) Code pays de l'adresse de facturation à 2 caractères
     * 							"country_state": (string) Etat / province de l'adresse
     * 							"email": (string) Adresse email de l'utilisateur
     * 							"adr_email": (string) Adresse email de facturation
     * 							"adr_invoices": (int) Identifiant de l'adresse de facturation de l'utilisateur
     * 							"adr_delivery": (int) Identifiant de l'adresse de livraison par défaut de l'utilisateur
     * 							"prf_id": (int) Identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"prf_name": (string) Désignation du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"usr_date_created": (datetime) Date de création du compte
     * 							"last_login_en": (datetime) Date de dernière connexion
     * 							"orders": (int) Nombre de commandes passées par le client
     * 							"orders_web": (int) Nombre de commandes web passées par le client
     * 							"orders_canceled": (int) Nombre de commandes annulées
     * 							"orders_canceled_web": (int) Nombre de commandes web annulées
     * 							"display_prices": (string) Type d'affichage des prix (ttc ou ht)
     * 							"display_buy": (tinyint) Détermine si l'affichage du prix d'achat est effectué (0 : Non, 1 : Oui - revendeurs uniquement)
     * 							"opt_stocks": (tinyint) Testreindre l'affichage aux seuls produits à ceux en stocks (0 : Non - par défaut, 1 : Oui)
     * 							"opt_centralized": (tinyint) Restreindre l'affichage aux seuls produits centralisés (0 : Non - par défaut, 1 : Oui - revendeurs uniquement)
     * 							"prc_id": (int) Iidentifiant de la catégorie tarifaire à appliquer au client
     * 							"encours": (float) Encours du compte client
     * 							"dps_id": (int) Identifiant du dépôt de rattachement du client
     * 							"is_sync": (tinyint) Détermine si le compte est synchronisé avec la gestion commerciale (0 : Non - par défaut, 1 : Oui)
     * 							"naf": (string) Code NAF de l'activité principale du client (professionnels uniquement)
     * 							"website": (string) Adresse du site Internet du client (professionels uniquement)
     * 							"taxcode": (string) Numéro de TVA Intracommunautaire
     * 							"longitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"latitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"cac_id": (int) Identifiant de la catégorie comptable
     * 							"img_id": (int) Identifiant de l'image principale de l'utilisateur
     * 							"parent_id": (int) Identifiant du compte parent
     * 							"can_login": (tinyint) Détermine si l'utilisateur peut se connecter (0 : Non, 1 : Oui - par défaut)
     * 							"surname": (string) Surnom du compte utilisateur
     * 							"title_id": (int) Identifiant de civilité
     * 							"dob": (datetime) Date de naissance du compte
     * 							"lng_code": (string) Code de la langue par défaut du client
     * 							"encours_allow": (tinyint) Encours autorisé pour le compte (0 : Non, 1 : Oui - null par défaut)
     * 							"accept_partners": (tinyint) Détermine si le compte accepte de recevoir des offres des partenaires commerciaux (0 : Non - par défaut, 1 : Oui)
     * 							"seller_id": (int) Identifiant du commercial
     * 							"alert_cc": (string) Adresses email en copie
     * 							"title_name": (string) Nom de civilité
     * 							"type_id": (int) Type d'adresse de facturation
     * 							"wst_id": (int) Identifiant du site d'origine du compte (NULL si synchro)
     * 							"adr_desc": (string) Description de l'adresse de facturation
     * 							"is_locked": (tinyint) Détermine si le compte est bloqué (0 : Non - par défaut, 1 : Oui)
     * 							"opm_id": (int) Identifiant du modèle de paiement associé au client
     * 							"bnk_id": (int) Identifiant des informations bancaires principales du client
     * 							"rco_id": (int) Identifiant du code risque de l'utilisateur
     * 							"ref_gescom": (string) Référence de la gestion commercial
     * 							"is_confirmed": (tinyint) Détermine si l'inscription du compte client est confirmé (0 : Non, 1 : Oui - par défaut)
     * 							"date_confirmed_en": (datetime) Date de confirmation d'inscription
     * 							"is_masked": (tinyint) Détermine si l'utilisateur est masqué (0 : Non - par défaut, 1 : Oui)
     * 							"restrict_portfolio": (tinyint) Détermine si le représentant a un portefeuille client restreint au relation ou s'il peux voir tous les comptes (0 : peut tout voir, 1 : limité au portefeuille - par défaut)
     * 							"date_modified_en": (datetime) Date de dernière modification
     *				    }
     *				  ],
     *				  "adr_delivery": [
     *				    {
     *							"usr_id": (int) Identifiant du compte client,
     *							"id": (int) Identifiant de l'adresse,
     *							"type_id": (int) Identifiant du type d'adresse,
     *							"type_name": (string) Intitulé du type d'adresse,
     *							"title_id": (int) Identifiant de la civilité,
     *							"title_name": (string) Intitulé de la civilité,
     *							"firstname": (string) Prénom,
     *							"lastname": (string) Nom,
     *							"society": (string) Société,
     *							"siret": (string) Numéro de SIRET,
     * 							"address1": (string) Première partie de l'adresse
     * 							"address2": (string) Deuxième partie de l'adresse
     * 							"address3": (string) Troisième partie de l'adresse
     * 							"zipcode": (string) Code postal
     * 							"city": (string) Ville de l'adresse de facturation
     * 							"country": (string) Pays de l'adresse de facturation
     * 							"cnt_code": (string) Code pays de l'adresse de facturation à 2 caractères
     *							"phone": (string) Numéro de téléphone fixe,
     *							"fax": (string) Numéro de fax,
     *							"mobile": (string) Numéro de téléphone mobile,
     *							"phone_work": (string) Numéro de téléphone en journée,
     *							"description": (string) Description de l'adresse,
     *							"email": (string) Adresse mail liée à l'adresse,
     *							"date_created": (datetime) Date de création de l'adresse,
     *							"date_modified": (datetime) Date de modification de l'adresse,
     *							"manual_location": (tinyint) Détermine si la localisation de l'adresse a été faite manuel,
     *							"date_masked": (datetime) Date de désactivation de l'adresse (null par défaut),
     * 							"latitude": (float) Coordonnées gps de l'adresse (si disponible)
     * 							"latitude": (float) Coordonnées gps de l'adresse (si disponible)
     *							"date_location": (datetime) Date de géolocation (null par défaut),
     *							"ref_gescom": (string) Référence de l'adresse dans la gestion commerciale (null par défaut),
     *							"country_state": (string) Etat / province (null par défaut)
     *				    }
     *				  ],
     *				  "adr_invoice": [
     *				    {
     *							"usr_id": (int) Identifiant du compte client,
     *							"id": (int) Identifiant de l'adresse,
     *							"type_id": (int) Identifiant du type d'adresse,
     *							"type_name": (string) Intitulé du type d'adresse,
     *							"title_id": (int) Identifiant de la civilité,
     *							"title_name": (string) Intitulé de la civilité,
     *							"firstname": (string) Prénom,
     *							"lastname": (string) Nom,
     *							"society": (string) Société,
     *							"siret": (string) Numéro de SIRET,
     * 							"address1": (string) Première partie de l'adresse
     * 							"address2": (string) Deuxième partie de l'adresse
     * 							"address3": (string) Troisième partie de l'adresse
     * 							"zipcode": (string) Code postal
     * 							"city": (string) Ville de l'adresse de facturation
     * 							"country": (string) Pays de l'adresse de facturation
     * 							"cnt_code": (string) Code pays de l'adresse de facturation à 2 caractères
     *							"phone": (string) Numéro de téléphone fixe,
     *							"fax": (string) Numéro de fax,
     *							"mobile": (string) Numéro de téléphone mobile,
     *							"phone_work": (string) Numéro de téléphone en journée,
     *							"description": (string) Description de l'adresse,
     *							"email": (string) Adresse mail liée à l'adresse,
     *							"date_created": (datetime) Date de création de l'adresse,
     *							"date_modified": (datetime) Date de modification de l'adresse,
     *							"manual_location": (tinyint) Détermine si la localisation de l'adresse a été faite manuel,
     *							"date_masked": (datetime) Date de désactivation de l'adresse (null par défaut),
     * 							"latitude": (float) Coordonnées gps de l'adresse (si disponible)
     * 							"latitude": (float) Coordonnées gps de l'adresse (si disponible)
     *							"date_location": (datetime) Date de géolocation (null par défaut),
     *							"ref_gescom": (string) Référence de l'adresse dans la gestion commerciale (null par défaut),
     *							"country_state": (string) Etat / province (null par défaut)
     *				    }
     *				  ],
     *				  "service": [
     *				    {
     *							"id": (int) Identifiant du service de livraison,
     *							"name": (string) Intitulé du service de livraison
     *				    }
     *				  ],
     *				  "author": (array) Informations sur l'auteur du passage de la pièce [
     *				    {
     * 							"id": (int) Identifiant de l'utilisateur dans la base de données (ou tableau)
     * 							"ref": (string) Code client dans le logiciel de gestion commerciale
     * 							"society": (string) Nom de la société (professionnels uniquement)
     * 							"siret": (string) Numéro de siret
     * 							"phone": (string) Numéro de téléphone
     * 							"fax": (string) Numéro de fax
     * 							"mobile": (string) Numéro de téléphone portable
     * 							"work": (string) Numéro de téléphone dans la journée (travail)
     * 							"title_name": (string) Civilité de l'utilisateur (Monsieur, Madame, Mademoiselle)
     * 							"adr_firstname": (string) Prénom de l'utilisateur
     * 							"adr_lastname": (string) Nom de l'utilisateur
     * 							"address1": (string) Première partie de l'adresse
     * 							"address2": (string) Deuxième partie de l'adresse
     * 							"address3": (string) Troisième partie de l'adresse
     * 							"zipcode": (string) Code postal
     * 							"city": (string) Ville de l'adresse de facturation
     * 							"country": (string) Pays de l'adresse de facturation
     * 							"cnt_code": (string) Code pays de l'adresse de facturation à 2 caractères
     * 							"country_state": (string) Etat / province de l'adresse
     * 							"email": (string) Adresse email de l'utilisateur
     * 							"adr_email": (string) Adresse email de facturation
     * 							"adr_invoices": (int) Identifiant de l'adresse de facturation de l'utilisateur
     * 							"adr_delivery": (int) Identifiant de l'adresse de livraison par défaut de l'utilisateur
     * 							"prf_id": (int) Identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"prf_name": (string) Désignation du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"usr_date_created": (datetime) Date de création du compte
     * 							"last_login_en": (datetime) Date de dernière connexion
     * 							"orders": (int) Nombre de commandes passées par le client
     * 							"orders_web": (int) Nombre de commandes web passées par le client
     * 							"orders_canceled": (int) Nombre de commandes annulées
     * 							"orders_canceled_web": (int) Nombre de commandes web annulées
     * 							"display_prices": (string) Type d'affichage des prix (ttc ou ht)
     * 							"display_buy": (tinyint) Détermine si l'affichage du prix d'achat est effectué (0 : Non, 1 : Oui - revendeurs uniquement)
     * 							"opt_stocks": (tinyint) Testreindre l'affichage aux seuls produits à ceux en stocks (0 : Non - par défaut, 1 : Oui)
     * 							"opt_centralized": (tinyint) Restreindre l'affichage aux seuls produits centralisés (0 : Non - par défaut, 1 : Oui - revendeurs uniquement)
     * 							"prc_id": (int) Iidentifiant de la catégorie tarifaire à appliquer au client
     * 							"encours": (float) Encours du compte client
     * 							"dps_id": (int) Identifiant du dépôt de rattachement du client
     * 							"is_sync": (tinyint) Détermine si le compte est synchronisé avec la gestion commerciale (0 : Non - par défaut, 1 : Oui)
     * 							"naf": (string) Code NAF de l'activité principale du client (professionnels uniquement)
     * 							"website": (string) Adresse du site Internet du client (professionels uniquement)
     * 							"taxcode": (string) Numéro de TVA Intracommunautaire
     * 							"longitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"latitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"cac_id": (int) Identifiant de la catégorie comptable
     * 							"img_id": (int) Identifiant de l'image principale de l'utilisateur
     * 							"parent_id": (int) Identifiant du compte parent
     * 							"can_login": (tinyint) Détermine si l'utilisateur peut se connecter (0 : Non, 1 : Oui - par défaut)
     * 							"surname": (string) Surnom du compte utilisateur
     * 							"title_id": (int) Identifiant de civilité
     * 							"dob": (datetime) Date de naissance du compte
     * 							"lng_code": (string) Code de la langue par défaut du client
     * 							"encours_allow": (tinyint) Encours autorisé pour le compte (0 : Non, 1 : Oui - null par défaut)
     * 							"accept_partners": (tinyint) Détermine si le compte accepte de recevoir des offres des partenaires commerciaux (0 : Non - par défaut, 1 : Oui)
     * 							"seller_id": (int) Identifiant du commercial
     * 							"alert_cc": (string) Adresses email en copie
     * 							"title_name": (string) Nom de civilité
     * 							"type_id": (int) Type d'adresse de facturation
     * 							"wst_id": (int) Identifiant du site d'origine du compte (NULL si synchro)
     * 							"adr_desc": (string) Description de l'adresse de facturation
     * 							"is_locked": (tinyint) Détermine si le compte est bloqué (0 : Non - par défaut, 1 : Oui)
     * 							"opm_id": (int) Identifiant du modèle de paiement associé au client
     * 							"bnk_id": (int) Identifiant des informations bancaires principales du client
     * 							"rco_id": (int) Identifiant du code risque de l'utilisateur
     * 							"ref_gescom": (string) Référence de la gestion commercial
     * 							"is_confirmed": (tinyint) Détermine si l'inscription du compte client est confirmé (0 : Non, 1 : Oui - par défaut)
     * 							"date_confirmed_en": (datetime) Date de confirmation d'inscription
     * 							"is_masked": (tinyint) Détermine si l'utilisateur est masqué (0 : Non - par défaut, 1 : Oui)
     * 							"restrict_portfolio": (tinyint) Détermine si le représentant a un portefeuille client restreint au relation ou s'il peux voir tous les comptes (0 : peut tout voir, 1 : limité au portefeuille - par défaut)
     * 							"date_modified_en": (datetime) Date de dernière modification
     *				    }
     *				  ],
     *				  "deposit": (array) Information du dépôt rattaché à la pièce [
     *							{
     *								"id": (int) Identifiant du dépôt,
     *								"name": (string) Intitulé du dépôt,
     *								"is_main": (tinyint) Détermine s'il s'agit du dépôt principal (0 par défaut),
     * 								"address1": (string) Première partie de l'adresse
     * 								"address2": (string) Deuxième partie de l'adresse
     * 								"zipcode": (string) Code postal
     * 								"city": (string) Ville de l'adresse de facturation
     * 								"country": (string) Pays de l'adresse de facturation
     *								"phone": (string) Numéro de téléphone du dépôt,
     *								"fax": (string) Numéro de fax du dépôt,
     *								"email": (string) Adresse mail du dépôt,
     *								"is_sync": (tinyint) Détermine si le dépôt provient de la gestion commerciale,
     *								"desc": (string) Description du dépôt,
     *								"date_modified_en": (datetime) Date de dernière mise à jour,
     *								"date_created": (datetime) Date de création (null par défaut),
     *								"ref": (string) Référence du dépôt (null par défaut),
     *								"str_id": (int) Identifiant du magasin rattaché au dépôt (null par défaut)
     *							}
     *						],
     *				  "payment_type": (array) Information sur le moyen de paiement utilisé sur la pièce[
     *						{
     *							"id": (int) Identifiant du moyen de paiement,
     *							"name": (string) Intitulé du moyen de paiement,
     *							"date_modified": (datetime) Date de dernière mise à jour du moyen de paiement
     *						}
     *					],
     *				  "contact": (array) Informations sur le contact rattaché à la pièce [
     *				    {
     * 							"id": (int) Identifiant du contact dans la base de données (ou tableau)
     * 							"ref": (string) Code client dans le logiciel de gestion commerciale
     * 							"society": (string) Nom de la société (professionnels uniquement)
     * 							"siret": (string) Numéro de siret
     * 							"phone": (string) Numéro de téléphone
     * 							"fax": (string) Numéro de fax
     * 							"mobile": (string) Numéro de téléphone portable
     * 							"work": (string) Numéro de téléphone dans la journée (travail)
     * 							"title_name": (string) Civilité du contact (Monsieur, Madame, Mademoiselle)
     * 							"adr_firstname": (string) Prénom du contact
     * 							"adr_lastname": (string) Nom du contact
     * 							"address1": (string) Première partie de l'adresse
     * 							"address2": (string) Deuxième partie de l'adresse
     * 							"address3": (string) Troisième partie de l'adresse
     * 							"zipcode": (string) Code postal
     * 							"city": (string) Ville de l'adresse de facturation
     * 							"country": (string) Pays de l'adresse de facturation
     * 							"cnt_code": (string) Code pays de l'adresse de facturation à 2 caractères
     * 							"country_state": (string) Etat / province de l'adresse
     * 							"email": (string) Adresse email du contact
     * 							"adr_email": (string) Adresse email de facturation
     * 							"adr_invoices": (int) Identifiant de l'adresse de facturation du contact
     * 							"adr_delivery": (int) Identifiant de l'adresse de livraison par défaut du contact
     * 							"prf_id": (int) Identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"prf_name": (string) Désignation du profil utilisateur (tel que défini dans la table gu_profiles)
     * 							"usr_date_created": (datetime) Date de création du compte
     * 							"last_login_en": (datetime) Date de dernière connexion
     * 							"orders": (int) Nombre de commandes passées par le client
     * 							"orders_web": (int) Nombre de commandes web passées par le client
     * 							"orders_canceled": (int) Nombre de commandes annulées
     * 							"orders_canceled_web": (int) Nombre de commandes web annulées
     * 							"display_prices": (string) Type d'affichage des prix (ttc ou ht)
     * 							"display_buy": (tinyint) Détermine si l'affichage du prix d'achat est effectué (0 : Non, 1 : Oui - revendeurs uniquement)
     * 							"opt_stocks": (tinyint) Testreindre l'affichage aux seuls produits à ceux en stocks (0 : Non - par défaut, 1 : Oui)
     * 							"opt_centralized": (tinyint) Restreindre l'affichage aux seuls produits centralisés (0 : Non - par défaut, 1 : Oui - revendeurs uniquement)
     * 							"prc_id": (int) Iidentifiant de la catégorie tarifaire à appliquer au client
     * 							"encours": (float) Encours du compte client
     * 							"dps_id": (int) Identifiant du dépôt de rattachement du client
     * 							"is_sync": (tinyint) Détermine si le compte est synchronisé avec la gestion commerciale (0 : Non - par défaut, 1 : Oui)
     * 							"naf": (string) Code NAF de l'activité principale du client (professionnels uniquement)
     * 							"website": (string) Adresse du site Internet du client (professionels uniquement)
     * 							"taxcode": (string) Numéro de TVA Intracommunautaire
     * 							"longitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"latitude": (float) Coordonnées gps de l'adresse de livraison (si disponible)
     * 							"cac_id": (int) Identifiant de la catégorie comptable
     * 							"img_id": (int) Identifiant de l'image principale du contact
     * 							"parent_id": (int) Identifiant du compte parent
     * 							"can_login": (tinyint) Détermine si l'utilisateur peut se connecter (0 : Non, 1 : Oui - par défaut)
     * 							"surname": (string) Surnom du compte utilisateur
     * 							"title_id": (int) Identifiant de civilité
     * 							"dob": (datetime) Date de naissance du compte
     * 							"lng_code": (string) Code de la langue par défaut du client
     * 							"encours_allow": (tinyint) Encours autorisé pour le compte (0 : Non, 1 : Oui - null par défaut)
     * 							"accept_partners": (tinyint) Détermine si le compte accepte de recevoir des offres des partenaires commerciaux (0 : Non - par défaut, 1 : Oui)
     * 							"seller_id": (int) Identifiant du commercial
     * 							"alert_cc": (string) Adresses email en copie
     * 							"title_name": (string) Nom de civilité
     * 							"type_id": (int) Type d'adresse de facturation
     * 							"wst_id": (int) Identifiant du site d'origine du compte (NULL si synchro)
     * 							"adr_desc": (string) Description de l'adresse de facturation
     * 							"is_locked": (tinyint) Détermine si le compte est bloqué (0 : Non - par défaut, 1 : Oui)
     * 							"opm_id": (int) Identifiant du modèle de paiement associé au client
     * 							"bnk_id": (int) Identifiant des informations bancaires principales du client
     * 							"rco_id": (int) Identifiant du code risque du contact
     * 							"ref_gescom": (string) Référence de la gestion commercial
     * 							"is_confirmed": (tinyint) Détermine si l'inscription du compte client est confirmé (0 : Non, 1 : Oui - par défaut)
     * 							"date_confirmed_en": (datetime) Date de confirmation d'inscription
     * 							"is_masked": (tinyint) Détermine si l'utilisateur est masqué (0 : Non - par défaut, 1 : Oui)
     * 							"restrict_portfolio": (tinyint) Détermine si le représentant a un portefeuille client restreint au relation ou s'il peux voir tous les comptes (0 : peut tout voir, 1 : limité au portefeuille - par défaut)
     * 							"date_modified_en": (datetime) Date de dernière modification
     *				    }
     *				  ]
     *				}
     * 			}
     *		\endcode
     *	@}
     */
    case 'get':
        if( !isset($_REQUEST['id']) ){
            throw new Exception( "L'identifiant de commande fourni est invalide");
        }
        $masked = isset($_REQUEST['masked']) && $_REQUEST['masked']=='1';

        $content = dev_devices_get_object_simplified( CLS_ORDER, $_REQUEST['id'], null, [
            'callback_linked' => [
                // Charge les données liées au compte client
                'user' => [
                    'gu_users_get', [], [], ['user'],
                    // exclusion de certaines informations (les dates sont en français)
                    [], ['tenant', 'password', 'date_created', 'date_modified', 'last_login', 'date_password', 'distance', 'usr_latitude',
                        'usr_longitude', 'myprd', 'fur_alerts', 'discount'], CLS_USER
                ],
                // Charge les informations sur le dépôt lié
                'deposit' => [
                    'prd_deposits_get', [], [],
                    ['dps_id'],
                    [], ['quote_config', 'date_modified']
                ],
                // Charge les informations sur le service de livraison
                'service' => [ 'dlv_services_get', [], [], ['srv_id'], ['id', 'name'] ],
                // Charge les informations sur le moyen de paiement
                'payment_type' => [ 'ord_payment_types_get', [], [], ['pay_id', 'pay_id'], ['id', 'name', 'date_modified'] ],
                // Charge les informations sur le contact rattaché
                'contact' => [ 'gu_users_get', [], [], ['contact_id'] ],
                // Charge les informations sur le représentant
                'seller' => [ 'gu_users_get', [], [], ['seller_id'] ],
            ]
        ]);

        if( is_array($content) ){
            unset( $content['date'] );
            unset( $content['state_name_plural'] );
            unset( $content['state_sage'] );
            unset( $content['ord_livr_fr'] );
            unset( $content['date_modified_fr'] );
        }

        if( is_array($content['related']['products']) ){
            foreach( $content['related']['products'] as &$one_prd ){
                unset($one_prd['data']['date-livr'] );
                unset($one_prd['data']['prd_date_modified'] );
            }
        }

        if( is_array($content['related']['states']) ){
            foreach( $content['related']['states'] as &$one_state ){
                unset( $one_state['data']['date'] );
            }
        }

        // Tableau des taxes
        $content['tax'] = [
            'total' => 0.00,
            'content' => []
        ];

        $total_tax = $ecotaxe = 0;
        $r_product = ord_products_get( $_GET['id'] );
        if( $r_product ){
            while( $product = ria_mysql_fetch_assoc($r_product) ){
                if( ($product['total_ttc'] - $product['total_ht']) == 0 ){
                    continue;
                }

                $ecotaxe += $product['ecotaxe'] * $product['qte'];
                $total_tax += $product['ecotaxe'] * $product['qte'];

                $tax = (string) (($product['tva_rate'] - 1) * 100);

                if( !isset($content['tax']['content'][$tax]) ){
                    $content['tax']['content'][$tax] = [
                        'rate' => $product['tva_rate'],
                        'rate_name' => $tax.' %',
                        'base' => 0,
                        'amount' => 0,
                    ];
                }

                $content['tax']['content'][$tax]['base'] += $product['total_ht'];
                $content['tax']['content'][$tax]['amount'] += $product['total_ttc'] - $product['total_ht'];
                $total_tax += $product['total_ttc'] - $product['total_ht'];


            }
        }

        if( $ecotaxe > 0 ){
            $content['tax']['content']['ecotaxe'] = [
                'rate' => '',
                'rate_name' => 'ECO',
                'base' => '',
                'amount' => $ecotaxe
            ];
        }

        $content['tax']['total'] = $total_tax;

        // Charge les champs avancés lié au produit pour chaque ligne de commande
        if( isset($content['related']['products']) && is_array($content['related']['products']) ){

            foreach( $content['related']['products'] as $keyprdfld=>$one_prdfld ){
                $name_with_fields = $one_prdfld['data']['name'];

                // Charge les champs avancé renseigné
                $r_field = fld_fields_get( 0, 0, 0, 0, 0, $one_prdfld['data']['id'], false, [], false, [], null, CLS_PRODUCT );
                if( $r_field ){
                    while( $field = ria_mysql_fetch_assoc($r_field) ){
                        if( $field['under-name'] ){
                            $name_with_fields .= '<br >'.$field['name'].' : '.$field['obj_value'];
                        }

                        $content['related']['products'][ $keyprdfld ]['related']['field_values'][] = [
                            'obj_value' => $field['obj_value'],
                            'lng_code' => $field['lng_code'],
                            'obj_id_0' => $one_prdfld['data']['id'],
                            'obj_id_1' => "0",
                            'obj_id_2' => "0",
                            'id' => $field['id'],
                            'name' => $field['name'],
                            'type_id' => $field['type_id'],
                            'name_alias' => strtoupper( str_replace( '-', '_', urlalias( $field['name'], 'fr', true ) ) ),
                        ];
                    }
                }

                $content['related']['products'][ $keyprdfld ]['data']['name_with_fields'] = $name_with_fields;
            }
        }

        // Modèle de paiement
        if( isset($content['linked']['user'][0]['opm_id']) && is_numeric($content['linked']['user'][0]['opm_id']) && $content['linked']['user'][0]['opm_id'] > 0 ){
            if( $rdetails = ord_payment_model_details_get( $content['linked']['user'][0]['opm_id'] ) ){
                while( $detail = ria_mysql_fetch_assoc($rdetails) ){
                    $detail['id'] = $detail['pay_id'];
                    $detail['name'] = $detail['pay_name'];
                    $content['linked']['user'][0]['opm_detail'] = gu_users_payment_types_view( $detail );
                    $content['linked']['user'][0]['opm_days'] = $detail['days'];
                }
            }
        }

        break;
    /** @{@}
     *	@{
     *     \page api-orders-complete-add Ajout
     *
     *    Permet l'ajout d'une nouvelle commande complète
     *
     *		\code
     *			POST orders/complete/
     *		\endcode
     *
     * @param raw_data Obligatoire, Donnée en json_decode
     *	\code{.json}
     *     {
     *               "head Obligatoire : Entête de la commande
   *                   "usr"                   Obligatoire : Identifiant de l'utilisateur
     *                   "state"                 Obligatoire : Statut
     *                   "date"                  Obligatoire : Date
     *                   "piece"                 Obligatoire : N° de pièce
     *                   "ref"                   Obligatoire : Référence
     *                   "adr_invoices"          Facultatif  : Identifiant de l'adresse de facturation
     *                   "adr_delivery"          Facultatif  : Identifiant de l'adresse de livraison
     *                   "date-livr | date_livr" Facultatif  : Date de livraison
     *                   "dps_id"                Facultatif  : Identifiant du dépôt
     *                   "pkg_id"                Facultatif  : Identifiant du package
     *                   "srv_id"                Facultatif  : Identifiant du service de livraison
     *                   "dlv_notes"             Facultatif  : Notes de livraison
     *                   "pay_id"                Facultatif  : Identifiant du moyen de paiement
     *                   "pmt_id"                Facultatif  : Identifiant du code promotion
     *                   "seller_id"             Facultatif  : Identifiant du représentant
     *                   "livr"                  Facultatif  : Identifiant du site
     *                   "need_sync"             Facultatif  : Determine si le devis dois être envoyer en gescom
     *                   "ord_notify_key"        Facultatif  : Clé permettant l'envoi de la notification par email du devis
     *                   "contact_id"            Facultatif  : Identifiant du contact qui prend la commande
     *                   "reseller_id"           Facultatif  : Identifiant du revendeur de la commande
     *                   "reseller_contact_id"   Facultatif  : Identifiant du contact du revendeur qui prend la commande
     *                   "contact_id"            Facultatif  : Identifiant du contact qui prend la commande
     *                   "currency"              Facultatif  : Code de la devise utilisée (ISO 4217)
     *               	   "fields_delete_missing" Facultatif : Permet de ne pas supprimer les champs non données par le bundle
     *                   "fields"    Facultatif : Tableau de champ avancé, contenant les colonnes suivantes :
     *                       "fld_id"    Obligatoire : Identifiant du champ avancé
     *                       "value"    Obligatoire : Valeur
     *
     *               "lines Obligatoire : Tableau de lignes de la commande
   *                   "prd" : Identifiant du produit
     *                   "line" : N° de line
     *                   "parent"        : identifiant du produit parent
     *                   "child_line"    : identifiant du rang du produit dans la nomenclature variable (si pertinent)
     *                   "qte" : Quantité
     *                   "ref" : Référence du produit
     *                   "name" : Nom du produit
     *                   "notes" : Notes
     *                   "purchase_avg" : Prix d'achat moyen pondéré du produit au moment de la vente
     *                   "price_ht" : Prix HT unitaire
     *                   "tva" : tva en coeficient
     *                   "price_ttc" : prix TTC unitaire
     *                   "datelivr" : Date de livraison
     *                   "ecotaxe" 	   : Ecotaxe
     *                   "cod_id" 	   : Identifiant de la promotion
     *                   "col"           : identifiant du conditionnement
     *                   "weight"        : poids du produit
     *                   "discount"      : remise
     *                   "price_brut"    : prix brut (avant remise)
     *                   "free"          : Détermine si la ligne est gratuite
     *                   "fields_delete_missing" Facultatif : Permet de ne pas supprimer les champs non données par le bundle
     *                   "fields"    Facultatif : Tableau de champ avancé, contenant les colonnes suivantes :
     *                       "fld_id"    Obligatoire : Identifiant du champ avancé
     *                       "value"    Obligatoire : Valeur
     *
     *				"installments" : Liste de échéances de paiement
     *					- id : Identifiant de l'échéance
     *					- type_id : Identifiant du type de l'échéance
     *					- piece_id : Identifiant du numéro de la piece ( id de commande )
     *					- pay_id : Identifiant du moyen de paiement
     *					- amount_total : Montant total de l'écheance
     *					- date_created : date de création
     *					- amount_rest : Montant restan à payer
     *					- transaction_id : idenitifant de la transaction, utile pour le cas des paiement en ligne
     *					- date_expired : Date d'expiration de l'échéance
     *					- state : statue de l'échance, ( payé ou non )
     *					- is_deadline : détermine si l'échéance est terminé
     *					- amount_percent : pourcentage du paiement a faire sur la commande ( cas de paiement en 3x fois par exemple )
     *
     *               	"discount" Facultatif : Remise de la commande
     *                   "discount_type" : type de remise (valeur ou montant)
     *                   "apply_on"      : quel est la portée de la remise (ligne ou commandae)
     *
     *               	"serials" Facultatif : Numéro de série
     *					- cls_id : identifiant de la classe
     *					- obj_id_0 : identifiant 1
     *					- obj_id_1 : identifiant 2
     *					- obj_id_2 : identifiant 3
     *					- date_dlc : date limite de consommation
     *					- date_production : date de production
     *					- dps_id : numéro de dépot
     *					- ref_gescom : référence du lot dans la gescom
     *					- serial : numéro de serie
     *
     *               	"signature" Facultatif : Signature de la commande
     *                   	"signature" : Code de la signature sous une forme moveTo:X:Y..;
     *                   	"usr_id"    : Identifiant de l'utilisateur qui as signé
     *                   	"firstname" : Prénom de la personne qui a signé
     *                   	"lastname"  : Nom de la personne qui as signé
     *                   	"function"  : Fonction de la personne qui a signé
     *     }
     *	\endcode
     *
     *    @return int Identifiant de la commande
     *	@}
     */
    case 'add':

        $ord_id = update_order($raw_data);

        $result = true;
        $content = array('ord_id' => $ord_id);

        break;

    /** @{@}
     *	@{
     *	 \page api-orders-complete-upd Mise à jour
     *
     *	 Cette fonction modifie une commande.
     *
     *		\code
     *			PUT /orders/complete/
     *		\endcode
     *
     *	 @see Ci-dessus (ADD)
     *
     *	@}
     */
    case 'upd':

        $ord_id = update_order($raw_data);
        $result = true;

        break;
}

///@}

<?php
/**
 * \defgroup orders_pdf PDF
 * \ingroup orders
 * @{
 *
 * \page api-orders-pdf-get Chargement
 *
 * Cette fonction permet un retour fichier pdf du devis, attention ne fonctionne que pour certains clients
 *
 *		\code
 *			GET /orders/pdf/
 *		\endcode
 *
 *	 @param $id Obligatoire, Identifiant de la commande
 *	 @param $type Optionnel, type de format de sortie xls ou pdf (par défaut : pdf)
 *
 *	 @return un fichier zip contenant le pdf si devis_pdf_url est configuré, le fichier pdf sinon
*/

switch( $method ){
	case 'get':

		if( isset($_REQUEST['ord']) ){
			$_REQUEST['id'] = $_REQUEST['ord'];
		}

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Paramètre invalide.");
		}

		cfg_images_load($config);

		if( isset($config['devis_pdf_url']) && $config['devis_pdf_url']!=-1 ){
			if( strstr($config['devis_pdf_url'], '/apps/instances') ){
				$_GET = [
					'regenerate' => '1',
					'k' => $_REQUEST['id']
				];

				include( $config['devis_pdf_url'] );
			}else{
				// generation d'un lien vers la génération du devis pdf
				if( $config['tnt_id']==8 ){
					$url = str_replace('https', 'http', $config['site_url']);
				}else{
					$url = $config['site_url'];
				}

				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $url.$config['devis_pdf_url']."?regenerate=1&k=".$_REQUEST['id']);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("X-Forwarded-proto:https"));
				$data = curl_exec($ch);

				$info = curl_getinfo($ch);

				curl_close($ch);

				if( $info["http_code"] == 200 ){
					print $data;
					exit;
				}
			}
		} else {

			try {
				if (isset($_GET['type']) && strtolower($_GET['type']) == 'xls') {
					require_once('Export/order.inc.php');
					export_order($_REQUEST['id'], 'excel');
				} else {
					require_once('Pdf/pdf.inc.php');
					$tmp = null;
					generate_devis($_REQUEST['id'], false, null, $tmp, $_REQUEST['id'].'.pdf');
				}
				exit;
			} catch (Exception $e) {
				if ($e->getCode() >= 1000) {
					mail('<EMAIL>', 'api - pdf 1', $e->getMessage());
					throw new Exception($e->getMessage());
				} else {
					//mail('<EMAIL>', 'api - pdf 2', $e->getMessage());
					error_log('[api - pdf] erreur lors de la génération du devis : '.$e->getMessage());
					throw new Exception( 'Erreur lors de la génération du devis : '.$e->getMessage() );
				}
			}
		}
		break;
}

///@}
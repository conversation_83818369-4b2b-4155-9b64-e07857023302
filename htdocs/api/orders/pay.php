<?php 
/**
 *	\defgroup orders_pay Moyen de paiement 
 *	\ingroup orders 
 *  	@{	
 *	\page api-orders-pay-upd Mise à jour 
 *
 *	 Cette fonction modifie le moyen de paiement de la commande.	
 *
 *		\code
 *			PUT /orders/pay/
 *		\endcode
 *
 *	 @param $ord Obligatoire, Identifiant de la commande
 *	 @param $pay_type Obligatoire, Identifiant du moyen de paiement
 *	
 *	 @return true si le moyen de paiement de la commande a bien été mis à jour 
*/
switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['ord'], $_REQUEST['pay_type']) || !is_numeric($_REQUEST['ord']) || !ord_payment_types_exists($_REQUEST['pay_type']) ){
			throw new Exception("Paramètre invalide.");
		}

		if( ord_orders_pay_type_set($_REQUEST['ord'],$_REQUEST['pay_type']) ){
			$result = true;
		}
	
		break;
}

///@}
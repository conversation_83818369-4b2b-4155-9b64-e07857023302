<?php
/**
 * \defgroup orders_piece Numéro de piece sur les commandes
 * \ingroup orders
 * @{	 	
 * \page api-orders-piece-upd Mise à jour 
 *
 *	 Cette fonction modifie le numéro de piece de la commande.
 *
 *		\code
 *			PUT /orders/piece/
 *		\endcode
 *	
 *  @param $ord Obligatoire, Identifiant de la commande
 *  @param $piece Obligatoire, numéro de la piece 
 *	@param bool $no_action Optionnel, si True aucune autre action n'est effectuée (mail, indexation, etc...)
 *	@param bool $mail_owner Optionnel, si True et $no_action = False, l'email n'est pas envoyé mais les autres actions sont réalisées
 *	@param bool $update_total Optionnel, si True et $no_action = False, le montant total de la commande est mis à jour
 *	@param bool $apply_promo Optionnel, par défaut les promotions sont prises en compte dans le calcul des totaux, mettre faux pour les ignorer (commande déjà synchronisée avec remise appliquée sur chaque ligne de produit).
 *	
 * @return true si le numéro de piece de la commande est correctement modifié
 *
*/

switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['ord'], $_REQUEST['piece']) ){
			throw new Exception("Paramètre invalide.");
		}

		$_REQUEST['no_action'] = isset($_REQUEST['no_action']) ? $_REQUEST['no_action'] : false;
		$_REQUEST['mail_owner'] = isset($_REQUEST['mail_owner']) ? !$_REQUEST['mail_owner'] : true;
		$_REQUEST['update_total'] = isset($_REQUEST['update_total']) ? $_REQUEST['update_total'] : true;
		$_REQUEST['apply_promo'] = isset($_REQUEST['apply_promo']) ? $_REQUEST['apply_promo'] : true;

		if( ord_orders_piece_set($_REQUEST['ord'], $_REQUEST['piece'], $_REQUEST['no_action'], $_REQUEST['mail_owner'], $_REQUEST['update_total'], $_REQUEST['apply_promo']) ){
			$result = true;
		}

		break;
}

///@}
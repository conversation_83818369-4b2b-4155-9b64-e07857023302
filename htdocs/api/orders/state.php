<?php
/**
 * \defgroup orders_etat_commande Etat
 * \ingroup orders
 * @{
 * \page api-orders-state-upd Mise à jour
 *
 * Cette fonction modifie l'état de la commande
 *
 *		\code
 *			PUT /orders/state/
 *		\endcode
 *
 * @param $ord Obligatoire, Identifiant de la commande
 * @param $id Obligatoire, Identifiant
 * @param $state Obligatoire, Etat de la commande
 *
 * @return true si la mise à jour s'est correctement déroulée
*/

switch( $method ){
	case 'upd':

		$id = isset($_REQUEST['ord']) ? $_REQUEST['ord'] : 0;
		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : $id;

		if( !isset($_REQUEST['state']) || $id == 0 ){
			throw new Exception("Paramètre invalide.");
		}

		// log les données envoyé par la tablette
		api_log(json_encode($_REQUEST), 'api-states');

		// protection pour éviter un retour arrière sur le changement de status
		$old_state = ord_orders_get_state( $id );

		if( $old_state == $_REQUEST['state'] ){
			$result = true;
		}
		else if(    ($old_state == _STATE_INVOICE && !in_array($_REQUEST['state'], array(_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BL_EXP && !in_array($_REQUEST['state'], array(_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BL_READY && !in_array($_REQUEST['state'], array(_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_PREPARATION && !in_array($_REQUEST['state'], array(_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_IN_PROCESS && !in_array($_REQUEST['state'], array(_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_PREPARATION)) )
			|| ($old_state == _STATE_PAY_CONFIRM && !in_array($_REQUEST['state'], array(_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_WAIT_PAY && !in_array($_REQUEST['state'], array(_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_BASKET && !in_array($_REQUEST['state'], array(_STATE_WAIT_PAY,_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			|| ($old_state == _STATE_DEVIS && !in_array($_REQUEST['state'], array(_STATE_WAIT_PAY,_STATE_PAY_CONFIRM,_STATE_IN_PROCESS,_STATE_BL_READY,_STATE_BL_EXP,_STATE_INVOICE,_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) )
			){
			$result = false;
			$message = "Ancien status ".$old_state." sur cmd ".$id." => refus de mise à jour ".$_REQUEST['state'];
		}
		else if( ord_orders_state_update( $id, $_REQUEST['state'], '', true, $config['usr_id']) ){

			$result = true;

			// marque la commande comme à synchroniser seulement si ca vient pas de la synchro
			if( !$is_sync ){
				ord_orders_set_need_sync($id);
			}


			// dans le cas de colmar-frais la commande doit être découpé en X commandes en fonction de champs avancé
			if(isset($config['dev_id']) && $config['dev_id']>0) {

				// on retire l'id de la commande yuto pour éviter des écrasement par la suite
				if( $is_fdv ){
					ord_orders_set_author_device($id, $config['dev_id'], 1);
				}

				switch( $config['tnt_id'] ){
					case 41:
						// passage de la commande en masqué pour ne pas que la synchro l'utilise avant la fin du traitement
						ord_orders_unmask($id, true, true);

						// split de la commande
						if( !ord_orders_split_by_fields($id, 4163) ){ // id du champ de découpage colmar
							$result = false;
							$message = "Erreur dans le split de la commande, la commande est resté masqué : ".$id;
						}else{
							// démasque la commande
							ord_orders_unmask($id, false, true);
						}
						break;

					case 59 :
					case 104 :
					case 105 :
						// lancement de l'export de la commande dans SalesForce en tâche asynchrone
						if( !in_array($_REQUEST['state'], array(_STATE_CANCEL_USER,_STATE_CANCEL_MERCHAND)) ){
							RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_ORDERS_SEND, array('id'=> $id));
						}
						break;
					default:{
						if( isset($config['alert_shop_owner']) && $config['alert_shop_owner'] ){
							if (!in_array($_REQUEST['state'], ord_states_get_canceled()) ){
								ord_alert_shop_owner($id);
							}
						}
					}
				}

				if (($old_state == _STATE_DEVIS && in_array($_REQUEST['state'], array(_STATE_WAIT_PAY, _STATE_WAIT_VALIDATION)))
					|| $old_state == _STATE_INTERVENTION_DEVIS && in_array($_REQUEST['state'], array(_STATE_INTERVENTIONS_CONFIRM))) {
					serials_quantities_update(CLS_ORDER, $id, true);
				}
				//the next code is to add quantities to seials
				/*else if (($old_state == _STATE_DEVIS && in_array($_REQUEST['state'], array(_STATE_WAIT_PAY, _STATE_WAIT_VALIDATION)))
					|| $old_state == _STATE_INTERVENTION_DEVIS && in_array($_REQUEST['state'], array(_STATE_INTERVENTIONS_CONFIRM))) {
					serials_quantities_update(CLS_ORDER, $id);
				}*/
			}
		}

		break;
}
///@}
<?php
/**
 * \defgroup orders_ids Entête
 * \ingroup orders 
 *	@{	
 * 	\page api-orders-index-ids Chargement
 *
 *	Cette fonction récupère un identifiant de commande 
 *
 *		\code
 *			GET /orders/ids/
 *		\endcode
 *
 * @param int|array $piece Obligatoire, Numéro de pièce de commande
 *
 * @return json La ou les commandes demandées au format json avec les colonnes suivantes :
 * 		\code{.json}
 *		       {
 *					"ord_id" : Identifiant de la commande
 *					"ord_piece" : Numéro de pièce de la commande
 *       		},
 *		\endcode
*/

switch( $method ){
	// récupération d'ids de commandes
	case 'get':

		$pieces = 0;
		if( isset($_GET['piece']) && is_array($_GET['piece']) ){
			foreach( $_GET['piece'] as $piece ){
				if( !is_string($piece) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$pieces = $_GET['piece'];
		}elseif( isset($_GET['piece']) && is_string($_GET['piece']) ){
			$pieces = $_GET['piece'];
		}

		$array = array();
		// Si on a un array de pieces
		if (is_array($pieces)) {
			foreach ($pieces as $piece) {
				$idFOund = ord_orders_get_id($piece);
				if (!$idFOund)
				{
					$idFOund = 0;
				}
				$array[] = array('ord_id' => $idFOund, 'ord_piece' => $piece);
			}
		}
		// Si on a un simple numéro de pièce
		else {
			$idFOund = ord_orders_get_id($pieces);
			if (!$idFOund)
			{
				$idFOund = 0;
			}
			$array[] = array('ord_id' => $idFOund, 'ord_piece' => $pieces);
		}

		$result = true;
		$content = $array;

		break;
}
///@}
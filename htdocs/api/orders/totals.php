<?php
/**
 * \defgroup orders_totals Total
 * \ingroup orders 
 * @{	
 * \page api-orders-total-upd Mise à jour 
 *
 *	 Cette fonction recalcule le total de la commande
 *
 *		\code
 *			PUT /orders/totals/
 *		\endcode
 *	
 *	 @param int $ord Obligatoire, Identifiant de la commande
 *	 @param float $discount Facultatif, montant de la remise
 *	 @param string $discount_type Facultatif, mode d'application de la remise (order, min-prd, max-prd, min-line, max-line)
 *	 @param int $apply_on Facultatif, type de remise (0 pour montant, 1 pour pourcentage)
 *	
 *	 @return true si la mise à jour s'est déroulé avec succès ( le total de la commande est bien recalculé )
*/

switch( $method ){

	// permet de mettre à jour le total d'une commande
	case 'upd':

		if( !isset($_REQUEST['ord']) ){
			throw new Exception("Paramètre invalide.");
		}

		// permet d'appliquer une remise sur l'entete de commande
		$force_off = false;
		if( isset($_REQUEST['discount'], $_REQUEST['discount_type'], $_REQUEST['apply_on']) ){
			if( is_numeric($_REQUEST['discount']) && is_numeric($_REQUEST['discount_type']) && in_array($_REQUEST['apply_on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ){
				$force_off = array(
					'discount' => $_REQUEST['discount'],
					'discount_type' => $_REQUEST['discount_type'],
					'apply_on' => $_REQUEST['apply_on']
					);
			}
		}

		// log les données envoyé par la tablette
		api_log('Commandes '.$config['tnt_id'].' FDV '.$_REQUEST['ord'].' - totals : '.print_r($_REQUEST,true), 'debug');

		if( ord_orders_update_totals($_REQUEST['ord'], $force_off) ){
			$result = true;
		}

		break;
}

///@}
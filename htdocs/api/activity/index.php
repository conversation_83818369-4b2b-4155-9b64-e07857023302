<?php
/**
 * \defgroup api-activity Flux d'activité
 * @{
 *
 * \page api-actions-history-get Chargement
 *
 * 	Cette fonction permet de récupérer le flux d'activité
 *
 *	\code
 *		GET /activity/
 *	\endcode
 *
 *	@param array $cls_id Tableau des identifiant de classe a récupérer, si null alors on retournera tous.
 *	@param int $author_id Facultatif, identifiant de l'auteur des éléments
 *	@param int $usr_id Facultatif, identifiant de la personne concerné par le flux
 *	@param date $date_start Facultatif, date de début au format YYYY-MM-AA
 *	@param date $date_end Facultatif, date de fin au format YYYY-MM-AA
 *	@param bool $hydrated Facultatif, si true alors la recherche retournera les bundles completes ( ex: une commande sera envoyer avec ses champs avancés, ses produits, .. ) plus long en traitement
 *
 *	@return json, Tableau contenant les éléments sous la forme suivante (en fonction de la cls_id) :
 *	\code{.json}
 *           {
 *           "cls_id": 87 : rapport de visite,
 *           "date": date de création de l'élément,
 *           "data": {
 *               "id": Identifiant de la commande,
 *               "type_id": Identifiant du type de rapport,
 * 				 "type_name": Nom du type de rapport
 *               "usr_id": Identifiant de l'utilisateur,
 *               "usr_ref": Référence de l'utilisateur,
 *               "usr_is_sync": 1 si l'utilisateur est synchronisé, 0 si non-synchronisé,
 *               "usr_is_deleted": 1 si l'utilisateur est supprimé, 0 si non-supprimé,
 *               "usr_name": Nom de l'utilisateur,
 *               "usr_city": Ville de l'utilisateur,
 *               "author_id": Identifiant de l'auteur,
 *               "author_ref": référence de l'auteur,
 *               "author_is_sync": 1 si l'auteur est synchronisé, 0 si non-synchronisé,
 *               "author_is_deleted": 1 si l'auteur est supprimé, 0 si non-supprimé
 *               "author_name": Nom de l'auteur,
 *               "comments": Commentaires,
 *               "date_created_en": Date de création au format EN,
 *               "date_created": Date de création au format FR,
 *               "simple_date": Date au format "YYYY-MM-DD",
 *               }
 *          },
 *          {
 *           "cls_id": 4 : pièces de vente,
 *           "date": Date de création,
 *           "data": {
 *               "id": Identifiant de la commande,
 *               "piece": Identifiant de la commande dans la gestin commerciale,
 *               "ref": Référence de l'utilisateur,
 *               "usr_id": Identifiant de l'utilisateur,
 *               "date": Date de création,
 *               "date_livr": Date de livraison,
 *               "srv_id": Identifiant du service de livraison,
 *               "total_ht": Montant total hors-taxes,
 *               "total_ttc": Montant total Tout taxes comprises,
 *               "state_id": Identifiant du statut,
 *               "pay_id": Identifiant du moyen de payement,
 *               "inv_id": Identifiant de l'adresse de facturation
 *               "dlv_id": Identifiant de l'adresse de livraison,
 *               "rly_id": Identifiant du point relais,
 *               "str_id": Identifiant du magasin,
 *               "seller_id": Identifiant du commercial,
 *               "currency": Devise,
 *               "dps_id": Identifiant du dépot
 *               }
 *          },
 *          {
 *           "cls_id": 127 : Appels téléphoniques,
 *           "date": Date de création,
 *           "data": {
 *               "gcl_author_ref": Référence de l'auteur,
 *               "gcl_author_email": Email de l'auteur,
 *               "gcl_author_name": Nom de l'auteur,
 *               "gcl_usr_ref": Référence de l'utilisateur,
 *               "gcl_usr_email": Email de l'utilisateur,
 *               "gcl_author_id": Identifiant de l'auteur,
 *               "gcl_usr_dst": Identifiant de l'utilisateur de destination ,
 *               "gcl_type": type de l'appel,
 *               "gcl_date_created": Date de création,
 *               "gcl_phone": Numéro de telephone,
 *               "gcl_duration": Durée de la conversation,
 *               "gcl_comment": Commentaire,
 *               "gcl_android_id": Identifiant Androïd,
 *               "gcl_answered": L'appel a abouti,
 *               "gcl_dst_free": Détermine si l'utilisateur était disponible,
 *               "_fields": [],
 *               "need_sync": besoin de synchronisation,
 *               "_id": Identifiant de l'appel
 *               }
 *          },
 *          {
 *           "cls_id": 202 : Notifications,
 *           "date": Date de création,
 *           "data": {
 *               "nt_author_email": Email de l'auteur,
 *               "nt_author_id": Identifiant de l'auteur,
 *               "nt_author_name": Nom de l'auteur,
 *               "nt_author_ref": Référence de l'auteur,
 *               "nt_date_created": Date de création,
 *               "nt_desc": Description de la notification
 *               "nt_state": état de la notification
 *               "nt_title": titre de la notification
 *               "nt_type_id": Identifiant du type de notification
 *                  "objects": Liste des objets liés à la notification
 *               "sending": Liste des appareils sur laquelle la notification a été envoyée
 *               "users": Utilisateurs
 *               "_id": Identifiant de la notification
 *               }
 *          }
 *	\endcode
 */

switch( $method ){
	case 'get':

		$cls_allowed = array(CLS_ORDER, CLS_NOTIFICATIONS, CLS_REPORT, CLS_CALLS);
		$cls_selected = $cls_allowed;

		// Limite le résultat aux comptes liés à un représentant
		$seller_id = 0;
		$ar_usr_ids = [];
		if( isset($_REQUEST['seller_id']) && is_numeric($_REQUEST['seller_id']) && $_REQUEST['seller_id'] ){
			$seller_id = $_REQUEST['seller_id'];
			$ar_usr_ids = gu_users_seller_customers_get( $seller_id );
			if( !is_array($ar_usr_ids) || !count($ar_usr_ids) ){
				throw new Exception("Le représentant demandé n'est rattaché à aucun compte.");
			}
		}

		{ // Gestion des contrôles de paramètres
			// Contrôle le paramètre sur la classe
			if( isset($_REQUEST['cls_id']) ){

				if( is_numeric($_REQUEST['cls_id']) ){
					if( !in_array($_REQUEST['cls_id'], $cls_allowed) ){
						throw new Exception( "Cette identifiant de classe : ".$_REQUEST['cls_id']." n'est pas encore pris en compte.");
					}
					$cls_selected = array( $_REQUEST['cls_id']);
				}

				else if( is_array($_REQUEST['cls_id']) ){
					foreach( $_REQUEST['cls_id'] as $c ){
						if( !is_numeric($c) || !in_array($c, $cls_allowed) ){
							throw new Exception( "Cette identifiant de classe : ".$c." n'est pas encore pris en compte.");
						}
					}
					$cls_selected = $_REQUEST['cls_id'];
				}
				else{
					throw new Exception( "Paramètre invalide.");
				}
			}

			// Contrôle les paramètres de dates
			$date_start = null;
			$date_end = null;
			if( isset($_REQUEST['date_start']) ){
				if( !isdateheure($_REQUEST['date_start']) ){
					throw new Exception("La date de début donnée ne correspond pas à un format valide");
				}
				$date_start = dateheureparse($_REQUEST['date_start']);
			}
			if( isset($_REQUEST['date_end']) ){
				if( !isdateheure($_REQUEST['date_end']) ){
					throw new Exception("La date de fin donnée ne correspond pas à un format valide");
				}
				$date_end = dateheureparse($_REQUEST['date_end']);
			}

			// Contrôle les paramètres auteur et destinataire
			$usr_id = 0;
			if( isset($_REQUEST['usr_id']) ){
				if( !is_numeric($_REQUEST['usr_id']) ){
					throw new Exception("Le client demandé n'est pas valide");
				}
				$usr_id = $_REQUEST['usr_id'];
			}

			$author_id = 0;
			if( isset($_REQUEST['author_id']) ){
				if( !is_numeric($_REQUEST['author_id']) ){
					throw new Exception("L'auteur demandé n'est pas valide");
				}
				$author_id = $_REQUEST['author_id'];
			}

			$hydrated = isset($_REQUEST['hydrated']) && $_REQUEST['hydrated'];
		}

		// En sorti des contrôles, nous aurons :
		// 	- $cls_selected : un tableau contenant les classes attendues dans le résultat
		// 	- $date_start : date de début du résultat
		//	- $date_end : date de fin du résultat
		// 	- $usr_id : identifiant d'un compte client (seules les activités liées à celui-ci seront retournées)
		// 	- $author_id : identifiant d'un compte représentant (seules les activités réalisés par celui-ci seront retournées)

		$final_results = array();
		// on parcours les différentes classes donnée pour récupérer maximum 200 éléments de chaques
		foreach( $cls_selected as $cls){
			switch($cls){
				case CLS_ORDER :
					$states = [];

					if( isset($_GET['states']) && is_array($_GET['states']) ){
						foreach( $_GET['states'] as $one_state ){
							if( is_numeric($one_state) && $one_state > 0 ){
								$states[] = $one_state;
							}
						}
					}

					$rord = ord_orders_get_simple(array(
						), array(
							'start' => $date_start,
							'end' => $date_end
						), array(
							'usr_id' => $usr_id > 0 ? $usr_id : (count($ar_usr_ids) ? $ar_usr_ids : 0),
							'state_id' => count($states) ? $states : 0,
							'seller_id' => $author_id > 0 ? $author_id : false
						), array(
							'sort' => array('date' => 'desc'),
							'limit' => $limit
						)
					);

					if( $rord && ria_mysql_num_rows($rord) ){
						while( $ord = ria_mysql_fetch_assoc($rord) ){
							$final_results[] = array(
								'cls_id' => $cls,
								'date' => $ord['date'],
								'id' => $ord['id'],
								'data' => $hydrated ? dev_devices_get_object_simplified(CLS_ORDER, array($ord['id']), $ord) : $ord
							);
						}
					}
					break;
				case CLS_NOTIFICATIONS :
					$dates = array();
					if( $date_start ){
						$dates['start'] = $date_start;
					}
					if( $date_end ){
						$dates['end'] = $date_end;
					}

					$only_notif_addcpt = isset($_GET['states']) && in_array('addcpt', $_GET['states']);
					$only_notif_updcpt = isset($_GET['states']) && in_array('updcpt', $_GET['states']);

					if( $only_notif_addcpt && $only_notif_updcpt ){
						$only_notif_addcpt = $only_notif_updcpt = false;
					}

					$rnotifications = nt_notifications_get_by_view($author_id, $dates, 'desc', 0, (count($ar_usr_ids) > 0 ? 0 : $limit), $usr_id);
					if( $rnotifications && sizeof( $rnotifications) ){
						foreach( $rnotifications as $notifications ){
							if( is_array($notifications) ){// je sais pas pourquoi mais ya une prop total_row envoyer dans la liste
								// Dans le cas d'un filtre sur un représentant
								if( count($ar_usr_ids) ){
									if( count($final_results) >= $limit ){
										break;
									}

									// Contrôle que l'objet concerné par la notification est identifié
									if( !isset($notifications['objects'][0]['obj_id_0'], $notifications['objects'][0]['cls_id']) ){
										continue;
									}

									// S'il s'agit d'un compte client, vérifie que celui-ci est accessible au représentant
									if( $notifications['objects'][0]['cls_id'] == CLS_USER ){
										if( !in_array($notifications['objects'][0]['obj_id_0'], $ar_usr_ids) ){
											// Sinon on vérifié que l'auteur est le représentant
											if( $notifications['nt_author_id'] != $seller_id ){
												continue;
											}
										}
									}else{
										// Sinon on vérifié que l'auteur est le représentant
										if( $notifications['nt_author_id'] != $seller_id ){
											continue;
										}
									}
								}

								// Inclure unique que les ajouts de compte
								if( $only_notif_addcpt ){
									if( $notifications['nt_type_id'] != 5 ){
										continue;
									}
								}

								// Inclure unique que les ajouts de compte
								if( $only_notif_updcpt ){
									if( $notifications['nt_type_id'] != 6 ){
										continue;
									}
								}

								$final_results[] = array(
									'cls_id' => $cls,
									'date' => $notifications['nt_date_created'],
									'id' => $notifications['_id'],
									'data' => $hydrated ? dev_devices_get_object_simplified(CLS_NOTIFICATIONS, array($notifications['_id']), $notifications) : $notifications
								);
							}
						}
					}
					break;
				case CLS_REPORT :

					$rpr = rp_reports_get( 0, 0, ($usr_id > 0 ? $usr_id : (count($ar_usr_ids) ? $ar_usr_ids : 0)), $author_id, $date_start, $date_end, null, null, null, array('date_created' => 'desc'), false, false, $limit );
					if( $rpr && ria_mysql_num_rows($rpr) ){
						while( $rp = ria_mysql_fetch_assoc($rpr) ){
							$final_results[] = array(
								'cls_id' => $cls,
								'date' => $rp['date_created_en'],
								'id' => $rp['id'],
								'data' => $hydrated ? dev_devices_get_object_simplified(CLS_REPORT, array($rp['id']), $rp) : $rp
							);
						}
					}

					break;
				case CLS_CALLS :
					$dates = array();
					if( $date_start ){
						if( strlen($date_start) == 10 ){
							$date_start = $date_start.' 00:00:00';
						}

						$dates['date1'] = $date_start;
					}
					if( $date_end ){
						if( strlen($date_end) == 10 ){
							$date_end = $date_end.' 23:59:59';
						}

						$dates['date2'] = $date_end;
					}

					// On regarde si un ou plusiers types d'appels sont demandés
					$call_in = isset($_GET['states']) && in_array('in', $_GET['states']);
					$call_out = isset($_GET['states']) && in_array('out', $_GET['states']);
					$call_missed = isset($_GET['states']) && in_array('missed', $_GET['states']);

					$rcalls = gcl_calls_get_by_view( "", 0, (count($ar_usr_ids) > 0 ? 0 : $limit), $author_id, $dates, $usr_id, null);
					if( $rcalls && sizeof( $rcalls) ){
						foreach( $rcalls as $call ){
							if( is_array($call) ){// je sais pas pourquoi mais ya une prop total_row envoyer dans la liste
								// Dans le cas d'un filtre sur un représentant
								if( count($ar_usr_ids) ){
									if( count($final_results) >= $limit ){
										break;
									}

									// Contrôle que l'objet concerné par la notification est identifié
									if( !isset($call['gcl_author_id'], $call['gcl_usr_dst']) ){
										continue;
									}
									// S'il s'agit d'un compte client, vérifie que celui-ci est accessible au représentant
									if( !in_array($call['gcl_usr_dst'], $ar_usr_ids) ){
										// Sinon on vérifié que l'auteur est le représentant
										if( $call['gcl_author_id'] != $seller_id ){
											continue;
										}
									}
								}

								// Par défaut tous les appels sont incluts dans le résultat final
								$include_res = true;

								// S'il existe une restriction sur un type d'appels alors par défaut l'appel n'est pas inclut au résultat final
								// Il le sera seulement s'il correspond à des types donnés en paramètre
								if( $call_in || $call_out || $call_missed ){
									$include_res = false;
								}

								// Inclure les appels entrants au résultat final
								if( $call_in ){
									if( $call['gcl_type'] == 1 ){
										$include_res = true;
									}
								}

								// Inclure les appels sortants au résultat final
								if( $call_out ){
									if( $call['gcl_type'] == 2 ){
										$include_res = true;
									}
								}

								// Inclure les appels manqués au résultat final
								if( $call_missed ){
									if( $call['gcl_type'] != 1 && $call['gcl_type'] != 2 ){
										$include_res = true;
									}
								}

								if( $include_res ){
									$final_results[] = array(
										'cls_id' => $cls,
										'date' => $call['gcl_date_created'],
										'id' => $call['_id'],
										'data' => $hydrated ? dev_devices_get_object_simplified(CLS_CALLS, array($call['_id']), $call) : $call
									);
								}
							}
						}
					}

					break;
			}
		}

		// trie le résultat final
		array_multisort(array_column($final_results, 'date'), SORT_DESC, $final_results);

		// split le resultat suivant l'offset et la limite
		$final_results = array_slice($final_results, $offset, $limit);

		// envoi le résultat
		$result = true;
		$content = $final_results;
		break;
}

///@}
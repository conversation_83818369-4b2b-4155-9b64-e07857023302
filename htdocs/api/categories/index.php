<?php
/**
 *	\defgroup categories Catégories de produits 
 *	\ingroup pim 
 *  @{	 
*/

switch( $method ){
	/** @{@}
	 * 	@{	 
	 * 	\page api-categories-index-add Ajout 
	 *
	 *	Cette fonction enregistre une nouvelle catégorie de produits
	 *
	 *	 	\code
	 *			POST /categories/
	 *	 	\endcode
	 *
	 *	@param string name Obligatoire, désignation
	 *	@param string title Obligatoire, titre
	 *	@param bool publish Obligatoire, 1 publié : 0 non publié
	 *	@param string desc Facultatif, description
	 *	@param from-cat Facultatif, 
	 *	@param string ref Facultatif, référence de la catégorie
	 *	@param int parent Facultatif, id de la catégorie parente
	 *
	 *	@return int id l'Identifiant de la nouvelle catégorie
	 *
	 *	@}
	*/
	case 'add':
		if( !isset($_REQUEST['name'],$_REQUEST['publish']) ){
			throw new Exception( "Paramètre invalide.");
		}

		$from_cat = false;
		if( isset($_REQUEST['from-cat']) ){
			$from_cat = $_REQUEST['from-cat'];
		}

		$ref = null;
		if( isset($_REQUEST['ref']) ){
			$ref = $_REQUEST['ref'];
		}

		$title = "";
		if( isset($_REQUEST['title']) ){
			$title = trim($_REQUEST['title']);
		}

		$desc = "";
		if( isset($_REQUEST['desc']) ){
			$desc = trim($_REQUEST['desc']);
		}

		$parent = 0;
		if( isset($_REQUEST['parent']) && is_numeric($_REQUEST['parent']) && $_REQUEST['parent']>0 ){
			$parent = $_REQUEST['parent'];
		}elseif( !isset($_REQUEST['force_root']) || !$_REQUEST['force_root'] ){
			if( !$from_cat && is_numeric($config['sync_default_category']) && $config['sync_default_category']>0 ){
				$parent = $config['sync_default_category'];
			}
			elseif( $from_cat && is_numeric($config['sync_default_catalog']) && $config['sync_default_catalog']>0 ){
				$parent = $config['sync_default_catalog'];
			}
		}

		$id = prd_categories_add( $_REQUEST['name'], $title, $desc, $parent, $_REQUEST['publish'], $is_sync, $ref );

		if( !$id ){
			throw new Exception("Erreur lors de la création de la catégorie : ".$_REQUEST['name']." : ".$title." : ".$desc." : ".$parent." : ".$_REQUEST['publish']);
		}

		$result = true;
		$content = array('id'=>$id);

		break;

	/** @{@}
	 * 	@{	 
	 * 	\page api-categories-index-upd Mise à jour 
	 *
	 * 	Cette fonction permet la mise à jour une catégorie de produits
	 *
	 *		\code
	 *			PUT /categories
	 *		\endcode
	 *
	 *  @param int id obligatoire, identifiant de la catégorie 
	 *	@param string name Obligatoire, désignation
	 *	@param string title Obligatoire, titre
	 *	@param bool publish Obligatoire, 1 publié : 0 non publié
	 *	@param string desc Facultatif, description
	 *	@param bool from-cat Facultatif, 
	 *	@param string ref Facultatif, référence de la catégorie
	 *	@param int parent Facultatif, id de la catégorie parente
	 *
	 *	@return bool true en cas de succès, false en cas d'échec
	 *	@}
	*/

	case 'upd':

		if( !isset($_REQUEST['id'],$_REQUEST['name'],$_REQUEST['publish']) ){
			throw new Exception( "Paramètre invalide.");
		}

		$reindex = true;
		if( isset($_REQUEST['no-index']) && ($_REQUEST['no-index']==true || $_REQUEST['no-index']=='1' || $_REQUEST['no-index']==1) ){
			$reindex = false;
		}

		$title = "";
		if( isset($_REQUEST['title']) ){
			$title = trim($_REQUEST['title']);
		}
		$desc = "";
		if( isset($_REQUEST['desc']) ){
			$desc = trim($_REQUEST['desc']);
		}

		$parent = 0;
		if( isset($_REQUEST['parent']) ){
			$from_cat = false;
			if( isset($_REQUEST['from-cat']) ){
				$from_cat = $_REQUEST['from-cat'];
			}
			if( isset($_REQUEST['parent']) && is_numeric($_REQUEST['parent']) && $_REQUEST['parent']>0 ){
				$parent = $_REQUEST['parent'];
			}elseif( !isset($_REQUEST['force_root']) || !$_REQUEST['force_root'] ){
				if( !$from_cat && isset($config['sync_default_category']) && is_numeric($config['sync_default_category']) && $config['sync_default_category']>0 ){
					$parent = $config['sync_default_category'];
				}
				elseif( $from_cat && isset($config['sync_default_catalog']) && is_numeric($config['sync_default_catalog']) && $config['sync_default_catalog']>0 ){
					$parent = $config['sync_default_catalog'];
				}
			}

			$parent = is_numeric($parent) && $parent>0 ? $parent : NULL;
		}

		if( !prd_categories_update( $_REQUEST['id'], $_REQUEST['name'], $title, $desc, $parent, $_REQUEST['publish'] ) ){
			throw new Exception("Erreur lors de la mise à jour de la catégorie ". $_REQUEST['id']." : ".$_REQUEST['name']." : ".$title." : ".$desc." : ".$parent." : ".$_REQUEST['publish']);
		}

		$result = true;

		break;

	/** @{@}
	 * 	@{	 	
	 * 	\page api-categories-index-get Chargement
	 *
	 * 	Cette fonction permet la récupération d'une ou plusieurs catégories de produits
	 *
	 * 		\code
	 *			GET /categories/
	 *		\endcode
	 *
	 *	@param int|array id Facultatif, identifiant ou tableau d'identifiants de catégories
	 *	@param int parent Facultatif, identifiant d'une catégorie parente. Ce paramètre ne sera pris en compte que si id est vide
	 *
	 * 	@return json une liste de catégories contenant les propriétés suivantes :
	 *	\code{.json}
	 *	{
	 *		"id" : identifiant de la catégorie
	 *		"ref" : référence de la catégorie
	 *		"name" : désignation de la catégorie
	 *		"title" : désignation de la catégorie (surcharge utilisable en cas de synchronisation)
	 *		"desc" : description de la catégorie
	 *		"url_alias" : url de la catégorie dans le site public
	 *		"keywords" : mots clés associés à la catégorie
	 *		"parent_id" : identifiant de la catégorie parente
	 *		"products" : nombre de produits publiés contenus dans cette catégorie
	 *		"publish" : booléen indiquant si la catégorie est publiée ou non
	 *		"cnt_id" : identifiant du résultat de moteur de recherche associé à la catégorie
	 *		"pos" : position d'affichage de la catégorie (ordre)
	 *		"is_sync" : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
	 *		"tag_title" : nom de la page pour le référencement
	 *		"tag_desc" : description de la page pour le référencement
	 *		"date_from" : date de début d'affichage de la catégorie, au format jj/mm/aaaa
	 *		"hour_from" : heure de début d'affichage de la catégorie, au format hh:mm
	 *		"date_to" : date de fin d'affichage de la catégorie, au format jj/mm/aaaa
	 *		"hour_to" : heure de fin de la catégorie, au format hh:mm
	 *		"date_from_en" : date de début d'affichage au format en
	 *		"date_to_en" : date de fin d'affichage au format en
	 *		"first_publish" : date de première publication
	 *		"first_publish_en" : date de première publication au format en
	 *		"is_solde" : si la catégorie est exclusivement réservée au solde
	 *		"cod_id" : identifiant d'un code promotion déterminant le contenu de la catégorie
	 *		"no_index" : si oui ou non la catégorie doit être indexée
	 *		"is_active" : si oui ou non la catégorie est active
	 *		"date_created" : date/heure de création de la catégorie
	 *		"date_modified" : date/heure de dernière modification de la catégorie
	 *	}	
	 * 	\endcode
	 *	@}
	*/	
	case 'get':

		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants de catégories fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		$parent = 0;
		if( isset($_GET['parent']) && is_numeric($_GET['parent']) ){
			$parent = $_GET['parent'];
		}

		$array = array();
		$rcat = prd_categories_get( $ids, false, $parent );
		while($cat = ria_mysql_fetch_assoc($rcat)){
			$array[] = $cat;
		}

		$result = true;
		$content = $array;

	break;
}

///@}
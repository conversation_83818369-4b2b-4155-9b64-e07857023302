<?php
/**
 * \defgroup calls Appels 
 * \ingroup crm 
 * @{	 
*/
switch( $method ){
	/** @{@}
	 * @{	 	
	 * \page api-calls-index-get Chargement 
	 *
	 * Cette fonction permet récupérer une liste des appels sous forme de tableau 
	 *
	 *		\code
	 *			GET	/calls/
	 *	 	\endcode
	 *	
	 * @param int $id Facultatif, identifiant de l'appel
	 * @param string $ref Facultatif, reférence de l'appel
	 * @param bool $calls_is_sync Facultatif, appels uniquement synchronisé ou non
	 * @param bool $need_sync Facultatif, appels ayant besoin d'une synchronisation ou non
	 *	
	 * @return json, un tableau de données sous la forme d'une liste contenant pour chaque entrée tout le détail du rapport( voir fields_values ), détail du retour data :
	 *		\code{.json}
	 *		{
     *        "id": Identifiant,
     *        "type_id": Type de l'identifiant,
     *        "usr_id": Identifiant de l'utilisateur,
     *        "usr_ref": Réf<PERSON><PERSON>ce de l'utilisateur,
     *        "usr_is_sync": Utilisateur synchronisé ou non,
     *        "usr_is_deleted": Utilisateur supprimé ou non,
     *        "usr_name": Nom de l'utilisateur,
     *        "usr_city": ville de l'utilisateur,
     *        "author_id": Identifiant de l'auteur,
     *        "author_ref": Référence de l'auteur,
     *        "author_is_sync": Auteur synchronisé ou non,
     *        "author_is_deleted": Auteur supprimé ou non,
     *        "author_name": Nom de l'auteur,
     *        "comments": Commentaires,
     *        "date_created_en": Date de création au format anglo-saxon,
     *        "date_created": Date de création au format français",
     *        "simple_date": Date au format "YYYY-DD-MM",
     *        "type_name": nom du type
	 *		},
	 * 		\endcode 
	 * @}
	*/
	case 'get':

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : "";
		$need_sync = isset($_REQUEST['need_sync']) ? $_REQUEST['need_sync'] : null;
		$limit = isset($_REQUEST['limit']) ? $_REQUEST['limit'] : 1000;
		$offset = isset($_REQUEST['offset']) ? $_REQUEST['offset'] : 0;

		$calls = gcl_calls_get_by_view( $id, $offset, $limit, 0, array(), 0, $need_sync);
		if( $calls && sizeof($calls) ) {
			$result = true;
			$content = $calls;
		}

		break;

	/** @{@}
	 * @{	 
	 * \page api-calls-index-add Ajout
	 *
	 * Cette fonction permet d'ajouter un rapport d'appel
	 *
	 *	 	\code
	 *			POST /calls/
	 *	 	\endcode
	 *	
	 * @param $author_id Obligatoire, identifiant de d l'utilisateur qui emet l'appel
	 * @param $usr_dst Obligatoire, identifiant de l'utilisateur de destination
	 * @param $duration Obligatoire, durée de l'appel
	 * @param $type Obligatoire, type d'appel INCOMING_TYPE = 1, OUTGOING_TYPE = 2, MISSED_TYPE = 3, VOICEMAIL_TYPE = 4
	 * @param string $phone Obligatoire, numéro de téléphone
	 * @param string $date_created Facultatif, date de création de l'appel
	 * @param $callback_date Facultatif, date de rappel
	 * @param $answered Facultatif, détermine si la personne à répondu
	 * @param $dst_free Facultatif, détermine si la personne était disponible
	 * @param string $comment Facultatif, commentaire de l'appel
	 * @param $android_id Facultatif, identifiant androïd 
	 * @param $fields Facultatif,  
	 *	
	 * @return l'identifiant de l'appel
	 * @}
	*/
	case 'add':

		if( !isset($_REQUEST['author_id']) || !is_numeric($_REQUEST['author_id'])
			|| !isset($_REQUEST['usr_dst']) || !is_numeric($_REQUEST['usr_dst'])
			|| !isset($_REQUEST['duration']) || !is_numeric($_REQUEST['duration'])
			|| !isset($_REQUEST['type']) || !is_numeric($_REQUEST['type'])
			|| !isset($_REQUEST['phone'])  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['fields']) ){
			$_REQUEST['fields'] = array();
		}
		if( !isset($_REQUEST['date_created']) ){
			$_REQUEST['date_created'] = date('Y-m-d H:i:s');
		}
		if( !isset($_REQUEST['callback_date']) ){
			$_REQUEST['callback_date'] = null;
		}
		if( !isset($_REQUEST['answered']) ){
			$_REQUEST['answered'] = null;
		}
		if( !isset($_REQUEST['dst_free']) ){
			$_REQUEST['dst_free'] = null;
		}
		if( !isset($_REQUEST['comment']) ){
			$_REQUEST['comment'] = "";
		}
		if( !isset($_REQUEST['android_id']) ){
			$_REQUEST['android_id'] = false;
		}

		$call_id = gcl_calls_add( $_REQUEST['author_id'], $_REQUEST['usr_dst'], $_REQUEST['date_created'], $_REQUEST['type'], $_REQUEST['phone'], $_REQUEST['duration'], $_REQUEST['callback_date'], $_REQUEST['answered'], $_REQUEST['dst_free'], $_REQUEST['comment'], $_REQUEST['fields'], $_REQUEST['android_id'] );
		if( !$call_id ){
			throw new Exception('Erreur lors de la création de l\'appels');
		}

		if( $is_sync ){
			if( !gcl_calls_set_is_sync($call_id, true) ){
				throw new Exception('Erreur lors de la mise à jour de l\'appels is sync');
			}
		} else {
			if( !gcl_calls_set_need_sync($call_id, true) ){
				throw new Exception('Erreur lors de la mise à jour de l\'appels needsync');
			}
		}

		$result = true;
		$content = array('id' => $call_id);

		break;

	/** @{@}
	 * @{	 
	 * \page api-calls-index-upd Mise à jour
	 *
	 * Cette fonction permet d'ajouter un rapport	
	 *
	 *		\code
	 *			PUT /calls/
	 *		\endcode
	 *	
	 * @param int $id Obligatoire, identifiant de l'appel
	 * @param $author_id Obligatoire, identifiant de d l'utilisateur qui emet l'appel
	 * @param $usr_dst Obligatoire, identifiant de l'utilisateur de destination
	 * @param $duration Obligatoire, durée de l'appel
	 * @param $type Obligatoire, type d'appel INCOMING_TYPE = 1, OUTGOING_TYPE = 2, MISSED_TYPE = 3, VOICEMAIL_TYPE = 4
	 * @param string $phone Obligatoire, numéro de téléphone
	 * @param $callback_date Facultatif, date de rappel
	 * @param $answered Facultatif, détermine si la personne à répondu
	 * @param $dst_free Facultatif, détermine si la personne était disponible
	 * @param string $comment Facultatif, commentaire de l'appel
	 * @param $android_id Facultatif, identifiant androïd 
	 * @param $fields Facultatif,  
	 *
	 * @return true si la mise à jour s'est déroulé avec succès, false dans le cas contraire	
	 * @}
	*/
	case 'upd':

		if( !isset($_REQUEST['author_id']) || !is_numeric($_REQUEST['author_id'])
			|| !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id'])
			|| !isset($_REQUEST['usr_dst']) || !is_numeric($_REQUEST['usr_dst'])
			|| !isset($_REQUEST['duration']) || !is_numeric($_REQUEST['duration'])
			|| !isset($_REQUEST['type']) || !is_numeric($_REQUEST['type'])
			|| !isset($_REQUEST['phone'])  ){
			throw new Exception('Paramètres invalide');
		}

		if( !isset($_REQUEST['fields']) ){
			$_REQUEST['fields'] = array();
		}
		if( !isset($_REQUEST['callback_date']) ){
			$_REQUEST['callback_date'] = null;
		}
		if( !isset($_REQUEST['answered']) ){
			$_REQUEST['answered'] = null;
		}
		if( !isset($_REQUEST['dst_free']) ){
			$_REQUEST['dst_free'] = null;
		}
		if( !isset($_REQUEST['comment']) ){
			$_REQUEST['comment'] = null;
		}
		if( !isset($_REQUEST['android_id']) ){
			$_REQUEST['android_id'] = false;
		}

		if( !gcl_calls_upd( $_REQUEST['id'], $_REQUEST['author_id'], $_REQUEST['usr_dst'], $_REQUEST['type'], $_REQUEST['phone'], $_REQUEST['duration'], $_REQUEST['callback_date'], $_REQUEST['answered'], $_REQUEST['dst_free'], $_REQUEST['comment'], $_REQUEST['fields'], $_REQUEST['android_id'] ) ){
			throw new Exception('Erreur lors de la mise à jour de l\'appels');
		}else{

			if( $is_sync ){
				if( !gcl_calls_set_is_sync($_REQUEST['id'], true) ){
					throw new Exception('Erreur lors de la mise à jour de l\'appels is sync');
				}
			} else {
				if( !gcl_calls_set_need_sync($_REQUEST['id'], true) ){
					throw new Exception('Erreur lors de la mise à jour de l\'appels needsync');
				}
			}

			$result = true;
		}
		break;
}

/// @}
<?php
/**
 * \defgroup need_sync_calls Demande de synchronisation
 * \ingroup calls
 * @{	
 * \page api-calls-need_sync-upd Mise à jour 
 *	
 * Cette fonction modifie la demande de synchronisation sur les rapports de visites
 *
 *		\code
 *			PUT /calls/need_sync/
 *	 	\endcode
 *	
 * @param int $id Obligatoire, identifiant de l'appel
 * @param bool $need_sync Obligatoire, besoin de syncrhoniser l'appel 
 *	
 * @return true si la mise à jour s'est déroulée avec succès, false dans le cas contraire
*/
switch ($method) {

	case 'upd':

		if( !isset($_REQUEST['id']) || !isset($_REQUEST['need_sync']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !gcl_calls_set_need_sync($_REQUEST['id'], $_REQUEST['need_sync']) ){
			throw new Exception("Erreur lors de la modification du need_sync sur l'appel.");
		}

		if( $is_sync && !gcl_calls_set_is_sync($_REQUEST['id'], true) ){
			throw new Exception("Erreur lors de la modification du is_sync sur l'appel.");
		}

		$result = true;

		break;

}
///@}
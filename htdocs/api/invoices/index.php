<?php
/**
 * \defgroup facture_client Factures client
 * \ingroup oms
 * @{
*/
switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-invoices-index-get Chargement
	 *
	 * Cette fonction permet de récupérer une facture
	 *
	 *	\code
	 *		GET /invoices/
	 *	\endcode
	 *
	 * @param int id : identifiant de la facture
	 *
	 * @return json sous la forme d'une liste d'items sous la forme suivante :
	 *	\code{.json}
 	 *       {
	 *			"id" : identifiant de la facture
	 *			"usr_id" : identifiant du compte utilisateur
	 *			"year" : Année de la facture
	 *			"total_ht" : montant total hors taxe de la facture
	 *			"total_ttc" : montant total ttc de la facture
	 *			"piece" : code de la pièce sage correspondante
	 *			"ref" : référence externe de la facture
	 *			"date" : date de la facture au format jj/mm/aaaa à hh:mm
	 *			"datenotime" : date de la facture au format jj/mm/aaaa
	 *			"date_en" : date de la facture au format anglais
	 *			"age" : nombre de jours depuis la création de la facture
	 *			"discount" : pourcentage d'escompte sur la facture (ce pourcentage est inclut dans les montants HT et TTC)
	 *			"date_modified" : date de dernière modification
	 *			"date_modified_en" : date de dernière modification au format EN
	 *			"masked" : détermine si la facture est masquée
	 *			"date_deadline" : date d'échéance de la facture
	 *			"date_deadline_en" : date d'échéance de la facture au format EN
	 *       },
	 *	\endcode
	 * @}
	*/
	case 'get':
		if( !isset($_REQUEST['id']) ){
			throw new Exception( "Paramètre invalide.");
		}

		$rinv = ord_invoices_get($_REQUEST['id']);
		if( $rinv && ria_mysql_num_rows($rinv) ){
			$result = true;
			$content = ria_mysql_fetch_assoc($rinv);
		}
		break;
	/** @{@}
 	 * @{
	 * \page api-invoices-index-del Suppression
	 *
	 * Cette fonction permet la suppression des factures
	 *
	 * \code
	 *		DELETE /invoices/
	 * \endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode :
	 * 	\code{.json}
	 *     {
	 *			"inv_id" Obligatoire : Identifiant de la facture
 	 *     }
	 *	\endcode
	 *
	 * @return true si la suppression s'est déroulée avec succès
	 * @}
	*/
	case 'del':
		global $method, $config, $objs;
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($objs as $invoice){
			if(!isset($invoice["inv_id"])){
				throw new Exception("Paramètres invalide");
			}
			if(!ord_invoices_del($invoice['inv_id'])){
				throw new Exception("La suppression de la facture n°:".$invoice['inv_id']." a échoué");
			}
		}
		$result = true;
		break;
}

///@}
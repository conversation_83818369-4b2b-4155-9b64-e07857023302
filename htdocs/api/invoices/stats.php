<?php
/**
 * \defgroup facture_client_stats Statistiques Facture client
 * \ingroup oms
 * @{
*/

require_once('BigQuery.inc.php');
require_once('stats.inc.php');


switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-invoices-index-get Chargement
	 *
	 * Cette fonction permet de récupérer une facture
	 *
	 *	\code
	 *		GET /invoices/
	 *	\endcode
	 *
	 * @param int id : identifiant de la facture
	 *
	 * @return json sous la forme d'une liste d'items sous la forme suivante :
	 *	\code{.json}
 	 *       {
	 *			"id" : identifiant de la facture
	 *       },
	 *	\endcode
	 * @}
	*/
	case 'get':
	case 'add':

		if( $method == 'add' ){ // les data sont passé en raw

			$obj = json_decode($raw_data);
			$obj = json_decode(json_encode($obj), true);

			foreach( array('date_start', 'date_end', 'usr_id', 'prd_id', 'cat_id', 'seller_id', 'limit', 'top','bad','order_by') as $key ){
				if( isset($obj[$key]) ) $_REQUEST[$key] = $obj[$key];
			}
		}

		// date de début et de fin pour limiter la recherche attention c'est liée à la période
		// ex si je met une date debut au 15/01 et une période month, yaura du 01/01
		$date_start = isset($_REQUEST['date_start']) && isdate($_REQUEST['date_start']) ? $_REQUEST['date_start'] : null;
		$date_end = isset($_REQUEST['date_end']) && isdate($_REQUEST['date_end']) ? $_REQUEST['date_end'] : null;

		$usr_ids = isset($_REQUEST['usr_id']) && $_REQUEST['usr_id'] ? $_REQUEST['usr_id'] : 0;
		$prd_id = isset($_REQUEST['prd_id']) && is_numeric($_REQUEST['prd_id']) ? $_REQUEST['prd_id'] : 0;
		$cat_id = isset($_REQUEST['cat_id']) && is_numeric($_REQUEST['cat_id']) ? $_REQUEST['cat_id'] : 0;
		$seller_id = isset($_REQUEST['seller_id']) && is_numeric($_REQUEST['seller_id']) ? $_REQUEST['seller_id'] : 0;
		$limit = isset($_REQUEST['limit']) && is_numeric($_REQUEST['limit']) ? $_REQUEST['limit'] : 20;

		$top = isset($_REQUEST['top']) ? $_REQUEST['top'] : false;
		$bad = isset($_REQUEST['bad']) ? $_REQUEST['bad'] : false;
		$order_by  = isset($_REQUEST['order_by']) ? $_REQUEST['order_by'] : null;

		$usr_ids = control_array_integer( $usr_ids, false );
		if( $usr_ids === false ){
			throw new Exception('Le paramètre "usr_ids" doit être un numérique ou un tableau de numérique.');
		}
		$prd_id = control_array_integer( $prd_id, false );
		if( $prd_id === false ){
			throw new Exception('Le paramètre "prd_id" doit être un numérique ou un tableau de numérique.');
		}

		$cat_childs = null;
		$cat_ids = [];

		if( $cat_id > 0 ){
			$cat_childs = [];
			$cat_ids[] = $cat_id;

			$cchild = prd_categories_get(0, false, $cat_id);
			while( $child = ria_mysql_fetch_assoc($cchild) ){
				$cat_childs[] = $child['id'];
				$cat_ids[] = $child['id'];
			}
		}

		if( $seller_id == 0 ){
			$rslr = gu_users_seller_get($config['usr_id']);
			if( $rslr ){
				$slr = ria_mysql_fetch_assoc($rslr);
				$seller_id = $slr['seller_id'];
			}
		}

		$final_array = [];
		if( isset($_GET['version']) && $_GET['version'] == 'bigquery' ){
			if( $top ){
				$final_array = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getInvoiceStats(null, $date_start, $date_end, $usr_ids, $prd_id, $cat_id, $seller_id, $top, $cat_childs, $order_by, 0, $limit);
			}else{
				if( !isset($_REQUEST['period']) ){
					throw new Exception( "Paramètre invalide.");
				}

				$final_array = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getInvoiceStats($_REQUEST['period'], $date_start, $date_end, $usr_ids, $prd_id, $cat_id, $seller_id, null, null);
			}
		}else{
			// TEST REMPLACEMENT BIGQUERY PAR REQUETE SQL
			$start = microtime( true );

			if( $top ){
				$final_array = stats_invoices_get( $date_start, $date_end, $usr_ids, $prd_id, $cat_ids, $seller_id, 0, $top, null, $order_by, $limit );
			}else{
				$period = isset( $_REQUEST['period'] ) ? $_REQUEST['period'] : null;
				$final_array = stats_invoices_get( $date_start, $date_end, $usr_ids, $prd_id, $cat_ids, $seller_id, 0, false, $period, $order_by, $limit );
			}

			if( isset($_GET['debug']) ){
				var_dump( count($final_array) );
				var_dump( round((microtime(true) - $start), 6).'s');
				print '<pre>';
				print_r( $final_array );
				print '</pre>';
				exit;
			}
		}

		$content = $final_array;
		$result = true;

		break;
	/** @{@}
 	 * @{
	 * \page api-invoices-stats-upd Mise à jour
	 *
	 * Cette fonction permet de mettre à jour une facture pour calculé les statistiques
	 *
	 * \code
	 *		PUT /invoices/stats/
	 * \endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode :
	 * 	\code{.json}
	 *     {
	 *			"inv_id" Obligatoire : Identifiant de la facture
 	 *     }
	 *	\endcode
	 *
	 * @return true si la mise à jour est faite
	 * @}
	*/
	case 'upd':

		$invoices_ids = [];

		if( isset($_REQUEST['id']) && is_numeric($_REQUEST['id'])){
			$invoices_ids[] = $_REQUEST['id'];
		}

		if( isset($_REQUEST['ids']) && is_array($_REQUEST['ids'])){
			$invoices_ids = $_REQUEST['ids'];
		}

		if( sizeof($invoices_ids) == 0  ){
			throw new Exception( "Paramètre invalide.");
		}


		// check si la config est active pour le tenant
		// si non alors on va pas plus loin
		if( isset($config['stats_couchdb_active']) && $config['stats_couchdb_active'] ){


			$invoices = [];
			foreach($invoices_ids as $id){

				// Récupère l'identifiant du client lié à la facture
				$user_id = ord_invoices_get_user( $id );
				if( is_numeric($user_id) && $user_id > 0 ){
					// Pré-calcul des statistiques de facture
					stats_invoices_pre_calculted_exec( $id, $user_id );
				}

				$results  = dev_devices_get_object_simplified(CLS_INVOICE, $id);
				if( !$results ){
					throw new Exception("Facture non disponible");
				}

				// avant le push dans le couchdb on doit récupérer certaines datas

				// gestion du commercial
				$results['sellers'] = [];

				// ajout du commercial liée à la factures
				if( isset($results["seller_id"]) ){
					$results['sellers'][] = $results["seller_id"];
				}

				// ajout du commercial liée au client
				$usr_seller_id = gu_users_get_seller_id($results['usr_id']);
				if( $usr_seller_id ){
					$results['sellers'][] = $usr_seller_id;
				}

				// gestion pour le commercial dans le cas ou on utilise les relations suivant le client
				$relation_usr_ids = [];
				$parents = rel_relations_hierarchy_parents_get_ids(REL_SELLER_HIERARCHY, $results['usr_id']);
				if( $parents && sizeof($parents) ){
					$relation_usr_ids = $parents;
				}
				// gestion pour le commercial dans le cas ou on utilise les relations suivant le commercial de la facture
				foreach( $results['sellers'] as $slr ){
					$rsellers = gu_users_get(0, "", "", PRF_SELLER, '', 0, '', false, false, $slr);
					if( $rsellers && ria_mysql_num_rows($rsellers) ){
						while($seller = ria_mysql_fetch_assoc($rsellers) ){
							$parents = rel_relations_hierarchy_parents_get_ids(REL_SELLER_HIERARCHY, $seller['id']);
							if( $parents && sizeof($parents) ){
								$relation_usr_ids = array_merge($relation_usr_ids, $parents);
							}
						}
					}
				}

				if( sizeof($relation_usr_ids) ){
					$rsellers = gu_users_get($relation_usr_ids, "", "", PRF_SELLER);
					if( $rsellers && ria_mysql_num_rows($rsellers) ){
						while($seller = ria_mysql_fetch_assoc($rsellers) ){
							$results['sellers'][] = $seller['seller_id'];
						}
					}
				}

				$results['sellers'] = array_values(array_unique($results['sellers']));
				// fin gestion du commercial

				// déplacement des datas dans les lignes des produits pour simplifier le traitement du couchdb
				foreach( $results['related']['products'] as $k => $p ){
					// ajout de la marque
					$results['related']['products'][$k]['data']['brd_id'] = prd_products_get_brand($p['data']['id']);

					// ajout des sellers
					$results['related']['products'][$k]['data']['sellers'] = $results['sellers'];

					// ajout du usr_id
					$results['related']['products'][$k]['data']['usr_id'] = $results['usr_id'];

					// ajout de la date
					$results['related']['products'][$k]['data']['inv_date_year'] = date('Y', strtotime($results['date_en']));
					$results['related']['products'][$k]['data']['inv_date_month'] = date('n', strtotime($results['date_en']));
					$results['related']['products'][$k]['data']['inv_date_day'] = date('j', strtotime($results['date_en']));

					// ajout des catégories
					$final_cat_ids = [];
					$cat_ids = $final_cat_ids = prd_products_categories_get_array( $p['data']['id'] );
					foreach($cat_ids as $cat_id ){
						$parents = prd_categories_parents_get_array($cat_id, true);
						if( $parents ){
							$final_cat_ids = array_merge($parents, $final_cat_ids);
						}
					}
					$final_cat_ids = array_unique($final_cat_ids);

					$results['related']['products'][$k]['data']['cat_ids'] = $final_cat_ids;
				}

	 			// pour certaine raison de simplicité on remonte les produits
				$results["products"] = [];
				foreach($results["related"]["products"] as $prd){
					$results["products"][] = $prd['data'];
				}
				unset($results["related"]["products"]);

				$invoices[] = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->parseData(BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getSchema(), $results);

			}


			$content = $invoices;
		}
		$result = true;

		break;
}

///@}
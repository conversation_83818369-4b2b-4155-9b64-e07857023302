<?php

require_once('products.inc.php');

// \cond onlyria

/**
 * \defgroup api-products-classify Classements des produits
 * \ingroup Produits
 * @{
 * \page api-products-classify-get Chargement
 *
 *	\code
 *		GET /products/classify/
 *	\endcode
 *
 * 	@return
 * @}
*/
switch( $method ){
	case 'get':
		print _('non implémenté.');
		exit;

		break;
	/** @{@}
 	 * @{
	 * \page api-products-classify-get-add Ajout
	 *
	 * Cette fonction permet d'ajout un ou plusieurs classement de produits
	 *
	 * \code
	 *		POST /products/classify/
	 * \endcode
	 *
	 * @param array prd : Obligatoire, identifiants des articles
	 * @param array cat : Obligatoire, identifiants des catégories
	 * @param bool is_sync : Obligatoire, si le classement est synchronisé ou non
	 *
	 * @return true si l'ajout s'est déroulée avec succès
	 * @}
	*/
	case 'add':
		if( !isset($_REQUEST['prd']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "prd" n\'est pas défini.');
		}

		if( !isset($_REQUEST['cat']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "cat" n\'est pas défini.');
		}

		if( !isset($_REQUEST['is_sync']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "is_sync" n\'est pas défini.');
		}

		// Ce paramètre est réservé au moteur RiaShop et présent lorsque l'appel à l'ajout de classement est réalisé depuis la workqueue.
		// Il contient les paramètres permettant de gérer la suite de la création de classements initiées dans le moteur
		$data_workqueue = false;
		if( isset($_REQUEST['workqueue']) ){
			$data_workqueue = $_REQUEST['workqueue'];
		}

		if( !prd_products_add_to_cat( $_REQUEST['prd'], $_REQUEST['cat'], $_REQUEST['is_sync'], $data_workqueue ) ){
			throw new BadFunctionCallException('Erreur lors du classement(s) d\'article(s).');
		}


		$result = true;
		break;
	/** @{@}
 	 * @{
	 * \page api-products-classify-get-del Suppression
	 *
	 * Cette fonction permet de supprimer un ou plusieurs classement de produits
	 *
	 * \code
	 *		DELETE /products/classify/
	 * \endcode
	 *
	 * @param array prd : Obligatoire, identifiants des articles
	 * @param array cat : Obligatoire, identifiants des catégories
	 * @param bool is_sync : Obligatoire, si le classement est synchronisé ou non
	 * @param bool redirection : Optionnel, par défaut une 301 vers l'url canonique ou une url publiée sera mise en place, mettre False pour qu'elle ne le soit pas
	 *
	 * @return true si la suppression s'est déroulée avec succès
	 * @}
	*/
	case 'del':
		if( !isset($_REQUEST['prd']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "prd" n\'est pas défini.');
		}

		if( !isset($_REQUEST['cat']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "cat" n\'est pas défini.');
		}

		if( !isset($_REQUEST['is_sync']) ){
			throw new BadFunctionCallException('Le paramètre obligatoire "is_sync" n\'est pas défini.');
		}

		if( !isset($_REQUEST['redirection']) ){
			$_REQUEST['redirection'] = false;
		}

		// Ce paramètre est réservé au moteur RiaShop et présent lorsque l'appel à l'ajout de classement est réalisé depuis la workqueue.
		// Il contient les paramètres permettant de gérer la suite de la suppression de classements initiées dans le moteur
		$data_workqueue = false;
		if( isset($_REQUEST['workqueue']) ){
			$data_workqueue = $_REQUEST['workqueue'];
		}

		if( !prd_products_del_from_cat($_REQUEST['prd'], $_REQUEST['cat'], $_REQUEST['is_sync'], $_REQUEST['redirection'], $data_workqueue) ){
			throw new BadFunctionCallException('Erreur lors du déclassement d\'article.');
		}

		$result = true;
		break;
}

// \endcond
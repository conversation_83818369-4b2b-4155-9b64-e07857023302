<?php
/**
 * \defgroup api-product-stats Produits 
 * \ingroup stats
 * @{
 * \page api-products-stats Chargement
 *
 * Cet appel retourne les statistiques suivantes sur les produits :
 *
 *		\code
 *			GET /products/stats/
 *		\endcode
 *
 * @param string $date1 Obligatoire, date de début de la période à considérer
 * @param string $date2 Obligatoire, date de fin de la période à considérer
 *
 * @param int $cat Facultatif, Identifiant (ou tableau d'identifiants) de catégories de produits sur lesquelles filtrer le résultat
 * @param int $brd Facultatif, Identifiant (ou tableau d'identifiants) de marques sur lesquelles filtrer le résultat
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 *	\code{.json}
 *     {
 *			"count" : nombre de produits
 *			"published" : nombre de produits publiés
 *			"available" : nombre de produits actuellement disponibles en stock  
 *     },
 *	\endcode
 * @}
*/

require_once('http.inc.php');

if( $method=='get' ){

	// Le résultat est mis en cache pour 15 minutes
	http_cache_control( 900 );

	// Filtres sur la période
	if(
		!isset($_GET['dateStart']) || !trim($_GET['dateStart']) || !isdate($_GET['dateStart'])
		|| !isset($_GET['dateStop']) || !trim($_GET['dateStop']) || !isdate($_GET['dateStop'])
	){
		throw new Exception("Les dates de début et de fin sont obligatoires (dateStart et dateStop)");
	}

	// Filtre sur la catégorie de produits
	$categories = 0;
	if( isset($_GET['cat']) && is_array($_GET['cat']) ){
		foreach( $_GET['cat'] as $id ){
			if( !is_numeric($id) ){
				throw new Exception("Les identifiants de catégories fournies en arguments sont incorrects");
			}
		}
		$categories = $_GET['cat'];
	}elseif( isset($_GET['cat']) && is_numeric($_GET['cat']) ){
		$categories = $_GET['cat'];
	}

	// Filtre sur la marque
	$brands = 0;
	if( isset($_GET['brd']) && is_array($_GET['brd']) ){
		foreach( $_GET['brd'] as $id ){
			if( !is_numeric($id) ){
				throw new Exception("Les identifiants de marques fournies en arguments sont incorrects");
			}
		}
		$brands = $_GET['brd'];
	}elseif( isset($_GET['brd']) && is_numeric($_GET['brd']) ){
		$brands = $_GET['brd'];
	}

	// Calcule les statistiques sur les comptes clients.
	$content = prd_products_get_stats( $_GET['dateStart'], $_GET['dateStop'], $categories, $brands );
	$result = is_array($content) && $content['count']!==false;

}

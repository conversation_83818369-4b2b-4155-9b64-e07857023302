<?php
require_once('prd/resellers.inc.php');

/**
 * \defgroup api-products-resellers Revendeur 
 * \ingroup Produits
 * @{
*/

switch( $method ){
    /** @{@}
 	 * @{
     * \page api-resellers-index-upd Mise à jour 
     *
     * Permet l'ajout ou la modification d'un produit pour un revendeur
     *
     *      \code
     *          PUT /products/resellers/
     *      \endcode
     * 
     * @param raw_data Obligatoire, Donnée en json_decode sous la forme : 
     *		\code{.json}
     *			{
     *   				"prd_id"			Obligatoire	: Identifiant du produit
     *   				"usr_id"			Obligatoire	: Identifiant du revendeur
     *   				"stock"			    Facultatif	: Stock du produit en posséssion du revendeur
     *      			"price_ht"			Facultatif	: Prix HT du produit appliqué revendeur
     *   				"price_promo_ht"	Facultatif	: Promotion HT appliqué par le revendeur sur le produit
     *   				"prd_ref"	        Facultatif	: Référence du produit dans le catalogue du revendeur
     *   				"prd_publish"	    Facultatif	: Publication du produit
     *   				"wst_id"	        Facultatif	: Site du revendeur
     *			}
     *		\endcode
     *
     * @return true si la mise à jour s'est déroulée avec succès 
	 * @}
	*/
    case 'upd': 
        // Récupération des valeurs envoyés
        global $method, $config;
        $obj = json_decode($raw_data, true);

        // Vérifie que le contenu est bien un tableau de données
        if( !is_array($obj) ){
            throw new BadFunctionCallException("Les données reçues ne sont pas sous forme de tableau");
        }

        // Parcours de tout les élléments pour faire le traitement
        foreach ($obj as $prd) {

            // Vérifie que les éléments obligatoires sont bien présents
            if (!isset($prd['prd_id'], $prd['usr_id'])) {
                throw new BadFunctionCallException("Les paramètres obligatoire prd_id et usr_id n'ont pas été défini");
            }

            // Définition de valeur par défaut si aucun n'a été précisée
            if( !isset($prd['stock']) )          $prd['stock'] = false;
            if( !isset($prd['price_ht']) )       $prd['price_ht'] = false;
            if( !isset($prd['price_promo_ht']) ) $prd['price_promo_ht'] =false;
            if( !isset($prd['prd_ref']) )        $prd['prd_ref'] = false;
            if( !isset($prd['prd_publish']) )    $prd['prd_publish'] = false;
            if( !isset($prd['wst_id']) )         $prd['wst_id'] = 0;

            // Création de l'association
            if ( !prd_resellers_set( $prd['prd_id'], $prd['usr_id'], $prd['stock'], $prd['price_ht'], $prd['price_promo_ht'], $prd['prd_ref'], $prd['prd_publish'], $prd['wst_id'] ) ) {
                throw new Exception("Erreur lors de l'association du produit à son revendeur : ".print_r($prd, true));
            }
        }

        // Renvois que le traitement à bien été fait
        $result = true;
        break;
    /** @{@}
 	 * @{
     * \page api-resellers-index-del Suppression 
     *
     * Permet de supprimer une relation produit / revendeur
     *
     *      \code
     *          DELETE /products/resellers/
     *      \endcode
     *   
     * @param raw_data Obligatoire, Donnée en json_decode sous la forme :
     *		\code{.json}
     *			{
     *   				"prd_id"			Obligatoire	: Identifiant du produit
     *   				"usr_id"			Obligatoire	: Identifiant du revendeur
     *			}
     *		\endcode
     *
     * @return true si la suppresion s'est déroulée avec succès 
	 * @}
	*/
    case 'del':
        // Récupération des valeurs envoyés
        global $method, $config;
        $obj = json_decode($raw_data, true);

        // Vérifie que le contenu est bien un tableau de données
        if( !is_array($obj) ) {
            throw new BadFunctionCallException("Les données reçues ne sont pas sous forme de tableau");
        }

        // Parcours de tout les élléments pour faire le traitement
        foreach ($obj as $prd) {

            // Vérifie que les éléments obligatoires sont bien présents
            if ( !isset( $prd['prd_id'], $prd['usr_id'] ) )
                throw new BadFunctionCallException("Les paramètres obligatoires prd_id et usr_id n'ont pas été définis");

            // Supréssion de l'association
            if ( !prd_resellers_del( $prd['prd_id'], $prd['usr_id'] ) ) {
                throw new Exception("Erreur lors la suppression de l'association du produit à son revendeur : ".print_r($prd, true));
            }
        }
      
        // Renvois que le traitement à bien été fait
        $result = true;
        break;
}
///@}
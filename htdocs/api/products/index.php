<?php
/**
 * \defgroup Produits Produits 
 * \ingroup pim
 * @{	 	
 * \page api-products-index-get Chargement
 *
 * Cette fonction retourne l'ensemble des produits
 *
 *		\code
 *			GET /products/
 *		\endcode
 *	
 * @param int $id Facultatif, tableau d'identifiants de produit
 * @param string $ref Facultatif, référence du produit
 * @param int $limit Facultatif, nombre de produits maximum à retourner
 *	
 * @return Json, liste de produit contenant les colonnes avec les données suivantes:
 *	\code{.json}
 *		{
 *			"id" : identifiant du produit,
 *			"url_alias" : chemin virtuel, disponible uniquement lorsque le paramètre $cat est utilisé,
 *			"cat_id" : identifiant de la catégorie, disponible uniquement lorsque le paramètre $cat est utilisé,
 *			"ref" : référence du produit,
 *			"name" : nom du produit,
 *			"title" : titre du produit (nom si titre vide),
 *			"desc" : description du produit,
 *			"desc-long: description longue du produit,
 *			"ecotaxe" : montant de l'ecotaxe pour ce produit (inclus dans le prix),
 *			"barcode" : code barres du produit,
 *			"publish" : booléen indiquant si le produit est publié,
 *			"publish_cat" : booléen indiquant si le produit est publié en tenant compte de la visibilité des catégories dans lesquelles il est placé,
 *			"orderable" : détermine si l'article est commandable (orderable coché + [actif ou toujours en stock]),
 *			"childonly" : détermine si l'article est enfant seulement,
 *			"countermark" : booléen indiquant si le produit est vendu en contremarque,
 *			"centralized" : Produit centralisé,
 *			"ecotaxe" : montant de l'ecotaxe pour ce produit (inclus dans le prix),
 *			"new" : booléen indiquant si ce produit est classé comme nouveau,
 *			"date_first_published" : date de première publication,
 *			"date_published" : date de publication du produit. N'est renseigné que si le produit est publié.,
 *			"date_published_rss" : date de publication, formatée pour l'utilisation dans certains flux RSS (format : %a, %d %b %Y %H:%i:%s +0100),
 *			"brd_id" : identifiant de la marque du produit,
 *			"brd_title" : désignation de la marque du produit,
 *			"brd_img_id" : identifiant de l'image associée à la marque,
 *			"brd_url" : url du site internet de la marque,
 *			"weight" : poids brut du produit, en grammes,
 *			"weight_net" : poids net du produit, en grammes,
 *			"selled" : nombre de vente du produit,
 *			"hits" : nombre de consultations,
 *			"length" : Longueur du produit, en centimètres,
 *			"width" : Largeur du produit, en centimètres,
 *			"height" : Hauteur du produit, en centimètres,
 *			"stock" : Stock réel,
 *			"stock_real" : Stock réel (hors quantité en préparation),
 *			"stock_res" : Quantité du stock déjà vendue en attente de départ,
 *			"stock_com" : Quantité en commande chez le fournisseur, en attente de livraison,
 *			"stock_prepa" : Quantité en cours de préparation,
 *			"stock_livr" : Date de prochaine livraison,
 *			"keywords" : Mots clés complémentaires,
 *			"sleep" : article en sommeil,
 *			"img_id" : image principale du produit,
 *			"date_created" : date et heure de création du produit,
 *			"date_modified" : date et heure de dernière modification du produit,
 *			"new_rule" : ,
 *			"is_sync" : détermine si le produit est synchronisé avec la gestion commerciale,
 *			"taxcode" : code douanier du produit,
 *			"destockage" : booléen indiquant si le produit est actuellement en déstockage ou non,
 *			"completion" : taux de remplissage du produit (nombre de propriétés renseignées / nombre de propriétés),
 *			"supplier_ref" : référence fournisseur (fournisseur principal),
 *			"supplier_delay" : délai de livraison (fournisseur principal),
 *			"supplier_price" : prix d'achat du produit auprès du fournisseur principal,
 *			"supplier_barcode" : code barre du produit (fournisseur principal),
 *			"supplier_packing" : colisage du produit (fournisseur principal),
 *			"supplier_conversion" : conversion unités d'achat / unités de vente (fournisseur principal),
 *			"supplier_id" : identifiant du fournisseur (fournisseur principal),
 *			"avg" : prix d'achat moyen,
 *			"tag_title" : balise title de la page produit pour le référencement,
 *			"tag_desc" : balise méta-description de la page produit pour le référencement,
 *			"garantie" : la durée de grarantie,
 *			"is_sync" : détermine si le produit est synchronisé avec la gestion commerciale,
 *			"completion" : Taux de complétion,
 *			"sell_weight" : Détermine si l'article est vendu à l'unité de mesure ou à une unité indivisible (si 1, requiert une conversion dans ord_products),
 *			"nomenclature_type" : Identifiant du type de nomenclature,
 *			"purchase_avg" : Prix d'achat moyen pondéré,
 *			"selled_web" : nombre de ventes du produit sur le web,
 *			"follow_stock" : produit suivi en stock oui / non,
 *			"is_orderable" : détermine si le produit est coché "commandable" (note : un produit peut être coché mais avoir la valeur "orderable" False à cause de sa mise en sommeil),
 *			"date_first_published_en" : date de première publication au format en,
 *			"sun_id" : identifiant de l'unité de vente du produit,
 *			"prd_canonical_id" : 
 *		}
 *	\endcode
 * @}
*/

switch( $method ){
	case 'get':

		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		// filtre par référence
		$ref = '';
		if( isset($_GET['ref']) ){
			$ref = $_GET['ref'];
		}

		// Filtre "limit"
		$options = array();
		if( isset($_GET['limit']) && is_numeric($_GET['limit']) ){
			$options['limit'] = $_GET['limit'];
		}

		$array = array();

		$rprd = prd_products_get_simple($ids, $ref, false, 0, false, false, false, false, $options );

		while($prd = ria_mysql_fetch_assoc($rprd)){
			$array[] = $prd;
		}

		$result = true;
		$content = $array;

		break;

    case 'add':
        global $method, $config;
        $obj = json_decode($raw_data, true);

        if (!is_array($obj)) {
            throw new BadFunctionCallException("Les données reçues ne sont pas sous forme de tableau");
        }

        $results = array();

        foreach ($obj as $prd) {

            if (empty($prd['prd_ref']) || empty($prd['prd_name']) || empty($prd['prd_title']) || empty($prd['prd_taxcode']) || empty($prd['prd_barcode']) || empty($prd['prd_desc']) || empty($prd['prd_desc_long'])) {
                throw new Exception("Les paramètres obligatoires prd_ref, prd_name, prd_title, prd_taxcode, prd_barcode, prd_desc et prd_desc_long n'ont pas été définis" . print_r($prd, true));
            }

            if (empty($prd['prd_brd_id']) || !prd_brands_exists($prd['prd_brd_id'])) {
                $prd['prd_brd_id'] = 'null';
            }
            if (empty($prd['prd_stock_livr'])) {
                $prd['prd_stock_livr'] = 'null';
            }
            if (empty($prd['prd_sun_id']) || !prd_sell_units_exists($prd['prd_sun_id'])) {
                $prd['prd_sun_id'] = 'null';
            }

            $fields = array(
                "prd_ref",
                "prd_name",
                "prd_title",
                "prd_publish",
                "prd_publish_cat",
                "prd_countermark",
                "prd_taxcode",
                "prd_barcode",
                "prd_date_created",
                "prd_weight",
                "prd_weight_net",
                "prd_sleep",
                "prd_brd_id",
                "prd_desc",
                "prd_desc_long",
                "prd_garantie",
                "prd_stock_livr",
                "prd_width",
                "prd_length",
                "prd_height",
                "prd_nomenclature_type",
                "prd_ecotaxe",
                "prd_sell_weight",
                "prd_last_check",
                "prd_parent_id",
                "prd_childonly",
                "prd_purchase_avg",
                "prd_img_id",
                "prd_orderable",
                "prd_sun_id",
                "prd_follow_stock",
                "prd_new",
                "prd_ref_gescom"
            );

            $datas = array();
            $update = false;
            if (!empty($prd['prd_id'])) {
                $update = true;
            }

            foreach ($prd as $key => $value) {
                if (!in_array($key, $fields) || trim($value) == '') continue;

                if (is_string($value) || $key == 'prd_new') {
                    $escaped_value = '"' . addslashes($value) . '"';
                } elseif (is_bool($value)) {
                    $escaped_value = $value ? 1 : 0;
                } else {
                    $escaped_value = $value;
                }

                if ($update) {
                    $datas[] = $key . ' = ' . $escaped_value;
                } else {
                    $datas[$key] = $escaped_value;
                }

            }

            if (!$update) {
                $datas['prd_tnt_id'] = $config['tnt_id'];

                if (empty($datas['prd_date_created'])) {
                    $datas['prd_date_created'] = 'now()';
                }

                $sql = 'insert into prd_products (' . implode(', ', array_keys($datas)) . ') value (' . implode(', ', array_values($datas)) . ')';

                if (ria_mysql_query($sql)) {
                    $prd_id = ria_mysql_insert_id();
                    $results[] = array('ID article LMB' => $prd['prd_ref'], 'ID article RIA' => $prd_id);
                } else {
                    throw new Exception("Erreur lors de l'ajout du produit : " . print_r($prd, true));
                }
            } else {
                $sql = 'update prd_products set ' . implode(', ', $datas) . ' where prd_tnt_id = ' . $config['tnt_id'] . ' and prd_id = ' . $prd['prd_id'];

                if (!ria_mysql_query($sql)) {
                    throw new Exception("Erreur lors de la mise à jour du produit : " . print_r($prd, true));
                }
				
				$prd_id = $prd['prd_id'];
                $results[] = array('ID article LMB' => $prd['prd_ref'], 'ID article RIA' => $prd_id);
            }
			
			if(empty($prd_id) || empty($prd['cat_id'])) {
				continue;
			}
			
			if(!empty($prd['sellunit']) && ($prd['sellunit'] > 1)) {
				fld_object_values_set($prd_id, _FLD_PRD_SALES_UNIT, $prd['sellunit']);
			} else {
				fld_object_values_set($prd_id, _FLD_PRD_SALES_UNIT, '');
			}
      
            $rcat = prd_products_categories_get($prd_id);
            $prd_cats = [];

            if( $rcat && ria_mysql_num_rows($rcat) ){
                while ($cat = ria_mysql_fetch_array($rcat)) {
                    $prd_cats[] = $cat['cat'];
                }
            
            }

            $changes = [
                'delete' => array_diff($prd_cats, $prd['cat_id']),
                'add'    => array_diff($prd['cat_id'], $prd_cats)
            ];
            
            foreach ($changes as $action => $categories) {
                if (!empty($categories)) {
                    foreach ($categories as $cat_id) {
                        if ($action === 'delete') {
                            prd_products_del_from_cat($prd_id, $cat_id, true);
                        } else {
                            prd_products_add_to_cat($prd_id, $cat_id, true, false);
                        }
                    }
                }
            }

			if(!empty($prd['tva'])) {
				prc_tvas_add($prd['tva'], $prd_id, 0, true, null, null);
			}

			/*
			$is_sync = false;
			$sql = 'replace into prd_classify ( cly_tnt_id, cly_prd_id, cly_cat_id, cly_is_sync, cly_url_is_canonical, cly_date_created )
					values
						('.$config['tnt_id'].', '.$prd_id.', '.$prd['cat_id'].', '.( $is_sync ? 1 : 0 ).', '.'null' .', now() )';
			
			if (!ria_mysql_query($sql)) {
				throw new Exception("Erreur lors de la mise à jour des catégories des produits : " . print_r($prd, true));
			}
			*/
				
        }

        $result = true;
        $content = $results;
        break;

}

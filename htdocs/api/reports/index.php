<?php
/**
 *  \defgroup api-reports Rapport de visites 
 *  \ingroup crm
 *  @{
*/

// \cond onlyria
function update_report($raw_data){
	global $method, $config, $is_sync;

	if( !isset($raw_data['rpt_id']) || !is_numeric($raw_data['rpt_id'])
		|| !isset($raw_data['usr_id']) || !is_numeric($raw_data['usr_id'])
		|| !isset($raw_data['author_id']) || !is_numeric($raw_data['author_id'])  ){
		throw new Exception('Paramètres invalide');
	}

	if( !isset($raw_data['comments']) || !$raw_data['comments'] ){
		$raw_data['comments'] = false;
	}

	if( !isset($raw_data['date_created']) || !$raw_data['date_created'] ){
		$raw_data['date_created'] = false;
	}

	if( $method == "add" || (isset($raw_data['id']) && !rp_reports_exists($raw_data['id'])) ){

		$rpr_id = rp_reports_add( $raw_data['rpt_id'], $raw_data['usr_id'], $raw_data['author_id'], $raw_data['comments'], $raw_data['date_created'] );
		if( !$rpr_id ){
			throw new Exception('Erreur lors de la création du rapport');
		}

	}else{

		if( !isset($raw_data['id']) ){
			throw new Exception('Paramètres invalide');
		}

		$rpr_id = $raw_data['id'];
		if( !rp_reports_upd( $raw_data['id'], $raw_data['rpt_id'], $raw_data['usr_id'], $raw_data['author_id'], $raw_data['comments'] ) ){
			throw new Exception('Erreur lors de la modification du rapport '.print_r($raw_data, true));
		}

	}

	$error = false;

	// vide toutes les notes
	$robj = rp_report_notes_get($rpr_id);
	if( $robj && ria_mysql_num_rows($robj) ){
		while( $obj = ria_mysql_fetch_assoc($robj) ){
			rp_report_notes_del( $rpr_id, $obj['note_id']);
		}
	}

	// ajout des notes liées
	if( isset($raw_data['notes']) && is_array($raw_data['notes']) ){
		foreach( $raw_data['notes'] as $notes ){
			if( !isset($notes['rptn_id']) || !isset($notes['value']) ){
				$error = true;
				break;
			}
			if( !isset($notes['comments']) ){
				$notes['comments'] = false;
			}

			rp_report_notes_add( $rpr_id, $notes['rptn_id'], $notes['value'], $notes['comments']);
		}
	}

	// vide toutes les liaisons
	$robj = rp_report_objects_get($rpr_id);
	if( $robj && ria_mysql_num_rows($robj) ){
		while( $obj = ria_mysql_fetch_assoc($robj) ){
			rp_report_objects_del( $rpr_id, $obj['cls_id'], $obj['obj_id_0'], $obj['obj_id_1'], $obj['obj_id_2'] );
		}
	}

	// ajout des objets liées
	if( isset($raw_data['objects']) && is_array($raw_data['objects']) ){
		foreach( $raw_data['objects'] as $objects ){
			if( !isset($objects['cls_id']) || !isset($objects['obj_id_0']) || !isset($objects['obj_id_1']) || !isset($objects['obj_id_2']) ){
				$error = true;
				break;
			}

			rp_report_objects_add( $rpr_id, $objects['cls_id'], $objects['obj_id_0'], $objects['obj_id_1'], $objects['obj_id_2']);
		}
	}

	// mise à jour des champs avancé
	if(isset($raw_data['fields'])) {
		// $fields_delete_missing = (isset($raw_data['fields_delete_missing']) && !$raw_data['fields_delete_missing']) ? false : true;
		fields_sync(CLS_REPORT, $rpr_id, 0, 0, $raw_data['fields'], false); // par défaut on supprime pas les champs car c'est pas a jour sur toutes les tablettes, a faire sauté par la suite.
	}

	// ajout des signatures
	if(isset($raw_data['signatures'])) {
		signatures_sync(CLS_REPORT, $rpr_id, 0, 0, $raw_data['signatures']); 
	}

	if( $error && $method == "add"){
		rp_reports_del($rpr_id);
	}
	if( $error ){
		return false;
	}

	if( !$error ){

		// mise à jour de la référence du rapport si disponible
		if( isset($_REQUEST['ref']) && !rp_reports_set_ref($rpr_id, $_REQUEST['ref']) ){
			throw new Exception("Erreur lors de la modification de la référence du rapport.");
		}

		if( $is_sync ){
			if( !rp_reports_set_is_sync($rpr_id, true) ){
				throw new Exception('Erreur lors de la mise à jour du rapport de visite is sync');
			}
		} else {
			if( !rp_reports_set_need_sync($rpr_id, true) ){
				throw new Exception('Erreur lors de la mise à jour du rapport de visite needsync');
			}
		}

	}

	return $rpr_id;

}
// \endcond 

switch ($method) {
    /** @{@}
 	 *	@{
	 *	
	 *	\page api-reports-index-get Chargement
	 *
	 *	Cette fonction permet de récupérer un (ou plusieurs) rapport(s) .
	 *	 
	 *		\code
	 *			GET /reports/
	 *		\endcode
	 *	
	 * @param int $id Facultatif, identifiant du rapport
	 * @param int $usr_id Facultatif, identifiant de l'utilisateur
	 * @param $author Facultatif, identifiant de l'auteur
	 * @param string $date_start Facultatif, date de début
	 * @param string $date_end Facultatif, date de fin
	 * @param string $ref Facultatif, référence du rapport
	 * @param $type_id Facultatif, type du rapport
	 * @param $report_is_sync Facultatif, rapport uniquement synchronisation ou non
	 * @param bool $need_sync Facultatif, rapport ayant besoin d'une synchronisation ou non
	 *	
	 * @return json avec si le paramètre "id" est donnée 1 seul élement contenant l'ensemble du détail pour un rapport, si "id" n'est pas donnée retourne une liste contenant pour chaque entrée tout le détail du rapport (voir fields_values)
	 *	\code{.json}
	 *		{
     *        "id": Identifiant,
     *        "type_id": Type de l'identifiant,
     *        "usr_id": Identifiant de l'utilisateur,
     *        "usr_ref": Référence de l'utilisateur,
     *        "usr_is_sync": Utilisateur synchronisé ou non,
     *        "usr_is_deleted": Utilisateur supprimé ou non,
     *        "usr_name": Nom de l'utilisateur,
     *        "usr_city": ville de l'utilisateur,
     *        "author_id": Identifiant de l'auteur,
     *        "author_ref": Référence de l'auteur,
     *        "author_is_sync": Auteur synchronisé ou non,
     *        "author_is_deleted": Auteur supprimé ou non,
     *        "author_name": Nom de l'auteur,
     *        "comments": Commentaires,
     *        "date_created_en": Date de création au format anglo-saxon,
     *        "date_created": Date de création au format français",
     *        "simple_date": Date au format "YYYY-DD-MM",
     *        "type_name": nom du type
	 *		},
	 * 	\endcode 
	 *	@}
	*/
	case 'get':

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : 0;
		$type_id = isset($_REQUEST['type_id']) ? $_REQUEST['type_id'] : 0;
		$usr_id = isset($_REQUEST['usr_id']) ? $_REQUEST['usr_id'] : 0;
		$author = isset($_REQUEST['author']) ? $_REQUEST['author'] : 0;
		$date_start = isset($_REQUEST['date_start']) && isdateheure($_REQUEST['date_start']) ? $_REQUEST['date_start'] : null;
		$date_end = isset($_REQUEST['date_end']) && isdateheure($_REQUEST['date_end']) ? $_REQUEST['date_end'] : null;
		$ref = isset($_REQUEST['ref']) ? $_REQUEST['ref'] : null;
		$report_is_sync = isset($_REQUEST['report_is_sync']) ? $_REQUEST['report_is_sync'] : null;
		$need_sync = isset($_REQUEST['need_sync']) ? $_REQUEST['need_sync'] : null;

		$reports = rp_reports_get($id, $type_id, $usr_id, $author, $date_start, $date_end, $ref, $report_is_sync, $need_sync);

		if( $reports && ria_mysql_num_rows($reports) ) {
			$array = array();
			while ($rp = ria_mysql_fetch_assoc($reports)) {
				$array[] = dev_devices_get_object_simplified(CLS_REPORT, array($rp['id']));
			}
			if( sizeof($array) ){
				if( $id ){
					$content = $array[0];
				}else{
					$content = $array;
				}
			}
		}
		$result = true;
		break;
    /** @{@}
 	 *	@{
	 *	\page api-reports-index-add Ajout
	 *
	 *	Cette fonction permet d'ajouter un rapport de visite 
	 *
	 *		\code
	 *			POST /reports/
	 *		\endcode
	 *	
	 *	 @param $rpt_id Obligatoire, identifiant du type de rapport
	 *	 @param int $usr_id Obligatoire, identifiant de l'utilisateur
	 *	 @param $author_id Obligatoire, identifiant de l'auteur
	 *	 @param $comments Facultatif, commentaire
	 *	 @param string $date_created Facultatif, permet de définir la date de création du rapport ( format En )
	 *	 @param $notes Facultatif, ?????
	 *	 @param $objects Facultatif, ?????
	 *	
	 *	 @return id l'identifiant du rapport
	 *	@}
	*/
	case 'add':

		$rpr_id = update_report($_REQUEST);

		// si une erreur à eu lieu on supprime le rapport
		if( $rpr_id && is_numeric($rpr_id) ){
			$result = true;
			$content = array('id' => $rpr_id);
		}

		break;
    /** @{@}
 	 *	@{
	 *	 \page api-reports-index-upd Mise à jour 
	 *
	 *	Cette fonction permet de mettre à jour un rapport de visite
	 *
	 *		\code
	 *			PUT /reports/
	 *		\endcode
	 *	
	 *	 @param int $id Obligatoire, identifiant du rapport
	 *	 @param $rpt_id Obligatoire, identifiant du type de rapport
	 *	 @param int $usr_id Obligatoire, identifiant de l'utilisateur
	 *	 @param $author_id Obligatoire, identifiant de l'auteur
	 *	 @param $comments Facultatif, commentaire
	 *	 @param $notes Facultatif, ?????
	 *	 @param $objects Facultatif, ?????
	 *	
	 *	 @return true si la modification s'est déroulée avec succès, false dans le cas contraire
	 *	@}
	*/
	case 'upd':

		if( update_report($_REQUEST) ){
			$result = true;
		}

		break;
    /** @{@}
 	 *	@{
	 *	 \page api-reports-index-del Suppression
	 *
	 *	Cette fonction permet de supprimer un rapport
	 *
	 *		\code
	 *			DELETE /reports/
	 *		\endcode
	 *	
	 *	 @param int $id Obligatoire, identifiant du rapport
	 *	
	 *	 @return true si le rapport est supprimé, false dans le cas contraire
	 *	@}
	*/
	case 'del':

		if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !rp_reports_del($_REQUEST['id']) ){
			throw new Exception("Erreur lors de la suppression de du rapport.");
		}

		$result = true;

		break;
}
///@}
<?php 
/**
 * \defgroup reports_pdf PDF
 * \ingroup reports 
 * @{	
 *
 * \page api-reports-pdf-get Chargement 
 *
 * Cette fonction permet un retour fichier pdf du rapport  de visite, attention ne fonctionne que pour certain client
 *
 *		\code
 *			GET /reports/pdf/
 *		\endcode
 *	
 *	 @param $ord Obligatoire, Identifiant du rapport de visite
 *	
 *   @return un fichier zip contenant le pdf s'il y a des images, le fichier pdf sinon
*/

switch( $method ){
	case 'get': 

		try {
			require_once('Pdf/ReportPdf.php');
			$pdf = new ReportPdf($_REQUEST['report']);

			$pdf->build();

			$pdf->download();
			exit;
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
		break;
}

///@}
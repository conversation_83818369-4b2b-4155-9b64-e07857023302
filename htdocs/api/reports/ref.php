<?php
/**
 * \defgroup api-reports-ref Réf<PERSON><PERSON>ces 
 * \ingroup api-reports
 * @{
 * \page api-reports-ref-upd Mise à jour 
 *
 * Cette fonction modifie la référence sur un rapport de visite
 *
 *		\code
 *			PUT /reports/ref/
 *		\endcode
 *	
 * @param int $id Obligatoire, identifiant du rapport
 * @param string $ref Obligatoire, référence du rapport
 *	
 * @return true si la modification s'est déroulée avec succès, false dans le cas contraire
*/
switch ($method) {

	case 'upd':

		if( !isset($_REQUEST['id']) || !isset($_REQUEST['ref']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !rp_reports_set_ref($_REQUEST['id'], $_REQUEST['ref']) ){
			throw new Exception("Erreur lors de la modification de la référence du rapport.");
		}

		$result = true;

		break;

}
///@}
<?php
	require_once('products.inc.php');
	require_once('websites.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PRODUCT');

	unset($error);
	
    $website = wst_websites_get();
	
	// Détermine l'identifiant du site
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all') 
			$_SESSION['websitepicker'] = '0';
		else
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		header('Location: index.php');
		exit;
	}

	$wst_id = isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] ? $_SESSION['websitepicker'] : $config['wst_id'];
	
	$config['prd_list_length'] 		= isset($config['prd_list_length']) 	? $config['prd_list_length'] 	: 20;
	$config['prd_search_length'] 	= isset($config['prd_search_length']) 	? $config['prd_search_length'] 	: 0;
	$config['prd_new_days'] 		= isset($config['prd_new_days']) 		? $config['prd_new_days'] 		: 60;
	$config['prd_new_date'] 		= isset($config['prd_new_date']) 		? $config['prd_new_date'] 		: 'prd_date_created';

	// cfg_variables_load($config, array('prd_list_length', 'prd_search_length', 'prd_new_days'));
	$rovr = cfg_overrides_get( $wst_id, array(), array('prd_list_length', 'prd_search_length', 'prd_new_days', 'prd_new_date') );
	if( $rovr ){
		while( $ovr = ria_mysql_fetch_array($rovr) )
			$config[ $ovr['code'] ] = $ovr['value'];
	}
	
	// Sauvegarde la configuration des listes de résultats
	if( isset($_POST['save']) ){
		
		// Vérification des données saisies dans le formulaire
		if( !isset($_POST['length'],$_POST['limit'],$_POST['days']) ){
			$error = _("Un ou plusieurs paramètres de configuration sont manquants ou invalides.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		} elseif( !is_numeric($_POST['length']) || $_POST['length']<=0 || $_POST['length']>999 ){
			$error = _("La valeur saisie pour la longueur des listes de produits est incorrecte. \nCette valeur doit être numérique et comprise entre 1 et 999.");
		} else{
			// Si les données correspondent aux exigences
			if( cfg_overrides_set_value('prd_list_length', floor($_POST['length']), $wst_id)
				&& cfg_overrides_set_value('prd_search_length', $_POST['limit'], $wst_id)
				&& cfg_overrides_set_value('prd_new_days', $_POST['days'], $wst_id)
				&& cfg_overrides_set_value('prd_new_date', $_POST['new-date'], $wst_id)
				&& cfg_overrides_set_value('cat_sort_type', $_POST['prd-sort-type'])
				&& cfg_overrides_set_value('cat_sort_dir', $_POST['prd-sort-dir'])
			){
				header('Location: index.php');
				exit;
			}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
		}
	}
	
	// Remet la configuration par défault
	if( isset($_POST['default']) ){
		if( cfg_products_config_reset_defaults($wst_id) ){
			header('Location: index.php');
			exit;
		}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
	}
	
	// Réinitialisé le tri des listes de produits
	if( isset($_POST['default-sort']) ){
		if( !prd_categories_sort_types_reset() )
			$error = _("Une erreur inattendue s'est produite lors de la réinitialisation des tris des listes de produits.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Listes de produits') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Listes de produits"); ?></h2>

	<?php 
		// si le nombre de site du locataire est supérieur a un on affiche le filtre pour trier par site
		print view_websites_selector( $wst_id, false, '', false );
		
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<p><?php echo _("Les paramètres ci-dessous vous permettent de personnaliser vos listes de produits. En cas de besoin, n'hésitez pas à nous contacter pour la création de nouvelles valeurs."); ?></p>
<form action="index.php" method="post" onsubmit="return prdConfig()">
	<dl>
		<dt><?php echo _("Longueur des listes de produits :"); ?></dt>
		<dd><?php echo _("Au sein du catalogue, le nombre de produits par page est limité pour faciliter la lecture des listes et réduire les temps de chargement"); ?>.</dd>
		<dd><label for="length"><?php echo _("Combien de produits par page souhaitez-vous afficher :"); ?></label>
		<input type="number" name="length" id="length" min="1" step="1" size="2" maxlength="4" value="<?php print isset($config['prd_list_length']) && $config['prd_list_length']>0 ? $config['prd_list_length'] : 10; ?>" />
		</dd>
		<dd><?php echo _("Ce paramètre s'applique aussi bien aux listes de produits qu'aux résultats de recherche."); ?></dd>
	</dl>

	<dl>
		<dt><?php echo _("Résultats de recherche :"); ?></dt>
		<dd><?php echo _("Si votre catalogue est très important, les recherches imprécises peuvent retourner de très nombreux résultats. Lorsque cela survient, les réponses pertinentes se trouvent souvent dans les toutes premières pages."); ?></dd>
		<dd><?php echo _("D'un autre côté, l'affichage d'un grand nombre de résultats témoigne de la richesse de votre catalogue. Google affiche par exemple très souvent plusieurs millions de pages dans ses résultats de recherche."); ?></dd>
		<dd><label for="limit"><?php echo _("Si vous le souhaitez, vous pouvez"); ?> <?php echo _("limiter le nombre de résultats retournés par le moteur de recherche :"); ?></label></span>
			<select name="limit" id="limit">
				<option value="0" <?php if( $config['prd_search_length']==0 ) print 'selected="selected"'; ?>><?php echo _("Ne pas limiter le nombre de résultats (valeur par défaut)"); ?></option>
				<option value="30000" <?php if( $config['prd_search_length']==30000 ) print 'selected="selected"'; ?>><?php echo _("limiter à 30 000 résultats"); ?></option>
				<option value="20000" <?php if( $config['prd_search_length']==20000 ) print 'selected="selected"'; ?>><?php echo _("limiter à 20 000 résultats"); ?></option>
				<option value="10000" <?php if( $config['prd_search_length']==10000 ) print 'selected="selected"'; ?>><?php echo _("limiter à 10 000 résultats"); ?></option>
				<option value="5000" <?php if( $config['prd_search_length']==5000 ) print 'selected="selected"'; ?>><?php echo _("limiter à 5 000 résultats"); ?></option>
				<option value="1000" <?php if( $config['prd_search_length']==1000 ) print 'selected="selected"'; ?>><?php echo _("limiter à 1 000 résultats"); ?></option>
				<option value="500" <?php if( $config['prd_search_length']==500 ) print 'selected="selected"'; ?>><?php echo _("limiter à 500 résultats"); ?></option>
			</select>
		</dd>
	</dl>

	<dl>
		<dt><?php echo _("Nouveautés :"); ?></dt>
		<dd><?php echo _("Pour promouvoir vos produits les plus récents, vous avez la possibilité d'afficher un logo \"Nouveau\" qui attirera l'oeil du consommateur. L'affichage de ce logo peut être géré automatiquement pour vous, ou manuellement, en fonction du paramètre ci-dessous :"); ?></dd>
		<dd>
			<label for="days"><?php echo _("Gestion automatique des produits nouveaux :"); ?></label>
			<select name="days" id="days">
				<option value="0" <?php if( $config['prd_new_days']==0 ) print 'selected="selected"'; ?>><?php echo _("Me laisser gérer manuellement les produits nouveaux"); ?></option>
				<option value="15" <?php if( $config['prd_new_days']==15 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 15 jours comme Nouveaux"); ?></option>
				<option value="30" <?php if( $config['prd_new_days']==30 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 30 jours comme Nouveaux"); ?></option>
				<option value="45" <?php if( $config['prd_new_days']==45 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 45 jours comme Nouveaux"); ?></option>
				<option value="60" <?php if( $config['prd_new_days']==60 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 60 jours comme Nouveaux (valeur par défaut)"); ?></option>
				<option value="90" <?php if( $config['prd_new_days']==90 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 90 jours comme Nouveaux"); ?></option>
				<option value="120" <?php if( $config['prd_new_days']==120 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 120 jours comme Nouveaux"); ?></option>
				<option value="150" <?php if( $config['prd_new_days']==150 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 150 jours comme Nouveaux"); ?></option>
				<option value="180" <?php if( $config['prd_new_days']==180 ) print 'selected="selected"'; ?>><?php echo _("Afficher les produits de moins de 180 jours comme Nouveaux"); ?></option>
			</select>
		</dd>
		<dd><label for="new-date"><?php echo _("Calculer les nouveautés en fonction :"); ?></label>
			<select name="new-date" id="new-date">
				<option value="prd_date_created" <?php if( isset($config['prd_new_date']) && $config['prd_new_date']=='prd_date_created' ) print 'selected="selected"'; ?>><?php echo _("De la date de création du produit"); ?></option>
				<option value="prd_first_published" <?php if( isset($config['prd_new_date']) && $config['prd_new_date']=='prd_first_published' ) print 'selected="selected"'; ?>><?php echo _("De la date de première publication du produit"); ?></option>
				<option value="prd_date_published" <?php if( isset($config['prd_new_date']) && $config['prd_new_date']=='prd_date_published' ) print 'selected="selected"'; ?>><?php echo _("De la date de dernière publication du produit"); ?></option>
			</select>
		</dd>
		<dd><?php echo _("Si vous ne désirez pas qu'un produit apparaisse comme nouveau lors de sa création, vous pouvez sélectionner l'option \"Ne jamais afficher comme Nouveau\" sur sa fiche."); ?></dd>
		<dd><?php echo _("Si vous souhaitez au contraire afficher un produit comme nouveau, même après le nombre de jours ci-dessus, vous pouvez le faire en sélectionnant l'option \"Toujours afficher comme Nouveau\" sur sa fiche."); ?></dd>
	</dl>

	<dl>
		<dt><?php echo _("Tri des listes de produits :"); ?></dt>
		<dd><?php echo _("Personnaliser l’ordre d’affichage de vos produits est d’une importance des plus hautes : les produits les plus visibles sont les plus susceptibles d’être achetés par vos clients. A vous de déterminer selon quel critère vous souhaitez classer vos produits. Vous souhaitez afficher vos produits par ordre de tri :"); ?>
		<select name="prd-sort-type" id="prd-sort-type" onchange="displaySortDir();">
			<?php
				$rtype = sys_sort_types_get();
				if( $rtype ){
					while( $type = ria_mysql_fetch_array($rtype) ){
						if( $type['id']!=SORT_PERSO )
							print '<option value="'.$type['id'].'" '.( $config['cat_sort_type']==$type['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
					}
				}
			?></select>
			<select name="prd-sort-dir" id="prd-sort-dir" <?php print in_array($config['cat_sort_type'], array(SORT_PERSO,SORT_RANDOM)) ? 'style="display:none;"' : ''; ?>>
				<option value="asc" <?php print $config['cat_sort_dir']=='asc' ? 'selected="selected"' : ''; ?>><?php print _('Croissant'); ?></option>
				<option value="desc" <?php print $config['cat_sort_dir']=='desc' ? 'selected="selected"' : ''; ?>><?php print _('Décroissant'); ?></option>
			</select>.</dd>
		<dd><?php echo _("Vous avez la possibilité de changer ce mode de tri global dans chaque catégorie de produits afin d’adapter l’affichage de vos produits selon vos besoins et vos objectifs."); ?></dd>
		<dd><?php echo _("Si vous souhaitez réinitialiser tous les tris déjà mis en place, il vous suffit de cliquer sur le bouton :"); ?> <input type="submit" name="default-sort" id="default-sort" value="Réinitialiser" title="<?php echo _("Réinitialiser tous les tris des listes de produits."); ?>" onclick="return window.confirm('Vous êtes sur le point de réinitialiser tous les tris mis en place sur les listes de produits.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?');" /></dd>
	</dl>
	<div class="ria-admin-ui-actions">
		<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
		<input type="submit" name="default" value="<?php echo _("Par défaut"); ?>" />
	</div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
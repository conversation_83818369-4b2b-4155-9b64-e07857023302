<?php

// Chargement de l'identifiant du site web de type "Extranet"
$wst_extranet = 0;

$r_website = wst_websites_get( 0, false, $config['tnt_id'], false, _WST_TYPE_EXTRANET );
if( $r_website && ria_mysql_num_rows($r_website)){
	$website = ria_mysql_fetch_assoc( $r_website );

	$wst_extranet = $website['id'];
}

// Charge les données du locataire depuis le registre
$current_register = RegisterGCP::getDatas($config['tnt_id']);

// Si aucun Extranet pour le locataire n'a été trouvé alors on bloque l'accès
// On que la formule "essentiel" ou "business" n'est pas choisie
if( $wst_extranet <= 0 || !in_array($current_register['package_btob'], ['essentiel', 'business']) ){
	// On bloque l'accès à cette page de configuration
	header('HTTP/1.1 403 Forbidden',true,403);
	include(dirname(__FILE__).'/../../errors/403.php');
	exit;
}

// Variable de configuation contenant la personnalisation de l'extranet
$cfg_code = 'extranet_template_configs';

// Liste des différents nuanciers de couleur
// L'ajout d'un nouveau nuancier nécessitera la création d'une fiche de style dans l'extranet standardisé
$nuanciers = array(
	array('name' => 'Œillet', 'colors' => array('#F8E5F1', '#D177B2', '#855472', '#373230')),
	array('name' => 'Chewing-gum', 'colors' => array('#f7e5f7', '#ce74cd', '#825380', '#373230')),
	array('name' => 'Lavande', 'colors' => array('#f0e7f6', '#be79df', '#825380', '#373230')),
	array('name' => 'Orchidée', 'colors' => array('#e7eaf4', '#828bf8', '#585ea0', '#373230')),
	array('name' => 'Ciel', 'colors' => array('#d9eef9', '#639db6', '#4a6775', '#373230')),
	array('name' => 'Glace', 'colors' => array('#8ffcfe', '#5ea0a0', '#476a6a', '#373230')),
	array('name' => 'Embruns', 'colors' => array('#c4f5e6', '#5ea28d', '#476b5f', '#373230')),
	array('name' => 'Flore', 'colors' => array('#d2f5d0', '#5fa45d', '#476c45', '#373230')),
	array('name' => 'Kaki', 'colors' => array('#e0f2b6', '#889d59', '#476c45', '#373230')),
	array('name' => 'Banane', 'colors' => array('#fffda9', '#9a9857', '#666540', '#373230')),
	array('name' => 'Cantaloup', 'colors' => array('#fbe9bf', '#ab925b', '#716143', '#373230')),
	array('name' => 'Saumon', 'colors' => array('#fae6e5', '#e3746f', '#90524e', '#373230')),
	array('name' => 'Fraise', 'colors' => array('#f8e6ec', '#fe559c', '#ab3968', '#373230')),
	array('name' => 'Magenta', 'colors' => array('#f6e6f5', '#f543f4', '#954491', '#373230')),
	array('name' => 'Raisin', 'colors' => array('#ebe9f3', '#a584e3', '#7f3dd3', '#373230')),
	array('name' => 'Myrtille', 'colors' => array('#e7eaf1', '#7795d2', '#1c55e8', '#373230')),
	array('name' => 'Aqua', 'colors' => array('#e3ebf4', '#2099fc', '#32669a', '#373230')),
	array('name' => 'Turquoise', 'colors' => array('#95fbfc', '#47a3a3', '#3e6a6b', '#373230')),
	array('name' => 'Écume', 'colors' => array('#b9f9cd', '#44a76c', '#3c6d4d', '#373230')),
	array('name' => 'Printemps', 'colors' => array('#befab3', '#43aa34', '#3c6e31', '#373230')),
	array('name' => 'Citron vert', 'colors' => array('#c0fb94', '#6ba535', '#4d6c31', '#373230')),
	array('name' => 'Citron', 'colors' => array('#faf772', '#9b9a36', '#666630', '#373230')),
	array('name' => 'Mandarine', 'colors' => array('#f9e8d4', '#d7801c', '#895826', '#373230')),
	array('name' => 'Venise', 'colors' => array('#fbe6e1', '#ff603f', '#b7341b', '#373230')),
	array('name' => 'Bordeaux', 'colors' => array('#f0e9eb', '#CC8099', '#943559', '#373230')),
	array('name' => 'Prune', 'colors' => array('#efe9ee', '#b785b2', '#9b3c98', '#373230')),
	array('name' => 'Aubergine', 'colors' => array('#ebeaef', '#9a90b5', '#6f569c', '#373230')),
	array('name' => 'Minuit', 'colors' => array('#e8ebef', '#8696b3', '#496399', '#373230')),
	array('name' => 'Océan', 'colors' => array('#e7ebef', '#7e98b3', '#336699', '#373230')),
	array('name' => 'Menthe', 'colors' => array('#e3ecec', '#54a1a2', '#276d6e', '#373230')),
	array('name' => 'Mousse', 'colors' => array('#e4ede7', '#59a475', '#237044', '#373230')),
	array('name' => 'Trèfle', 'colors' => array('#e4ede4', '#59a64e', '#217318', '#373230')),
	array('name' => 'Fougère', 'colors' => array('#e5ede2', '#6fa345', '#436f19', '#373230')),
	array('name' => 'Asperge', 'colors' => array('#e9ece0', '#9a992b', '#67661d', '#373230')),
	array('name' => 'Moka', 'colors' => array('#eeeae5', '#b78d62', '#945302', '#373230')),
	array('name' => 'Cayenne', 'colors' => array('#f1e9e7', '#c88474', '#aa412b', '#373230')),
	array('name' => 'Neige', 'colors' => array('#eaeaea', '#959595', '#636363', '#373230')),
	array('name' => 'Canard', 'colors' => array('#d4ecf3', '#249dc1', '#1a7895', '#373230')),
	array('name' => 'Parme', 'colors' => array('#fbe6e2', '#df4125', '#bb2e1a', '#373230')),
	array('name' => 'Carotte', 'colors' => array('#fae7da', '#f0700d', '#975123', '#373230')),
);

// Liste des domaines pouvant être choisi pour l'intranet
$domains  = array(
	'monbtob.com',
	'monbtob.fr',
	'monb2b.com',
	'monb2b.fr'
);

$socials = array(
	array('name' => 'Facebook'),
	array('name' => 'Twitter'),
	array('name' => 'Instagram'),
	array('name' => 'Linkedin'),
	array('name' => 'Pinterest'),
	array('name' => 'Flickr'),
	array('name' => 'Youtube'),
	array('name' => 'Viadeo'),
);

/** Cette fonction permet de transformer une chaine en un code apdater à la configuration de l'extranet
 * 	TODO : Déplacer cette fonction dans Template.class.php
 * 	@param string $name Obligatoire, chaine à transformer
 * 	@return string La chaine transformer
 */
function transform_in_code($name){
	$name = str_replace(' ','', $name);
	$name = strtolower(str_replace('Œ','oe', $name));
	return str_remove_accents($name);
}

// récupère les datas
if( $config[$cfg_code] == null || $config[$cfg_code] == 'null' || $config[$cfg_code] == '[]' || trim($config[$cfg_code]) == ''){
	$config[$cfg_code] = "{}";
}

$current_config = json_decode($config[$cfg_code], true);

if( !isset($current_config['theme-color']) ){
	$current_config['theme-color'] = 'canard';
}

// populate la liste des réseaux sociaux
foreach( $socials as $k => $s ){
	$socials[$k]['value'] = "";

	if( isset($current_config[transform_in_code($s['name'])]) && $current_config[transform_in_code($s['name'])] ){
		$socials[$k]['value'] = $current_config[transform_in_code($s['name'])];
	}
}

// on découpe le vhost pour avoir le sous domain et le domain séparé
$subdomain = $domain = "";
if( isset($current_register['vhost']) && $current_register['vhost']  ){
	$split = explode('.', $current_register['vhost']);
	if( sizeof($split) == 3 ){
		$subdomain = $split[0];
		$domain = $split[1].'.'.$split[2];
	}
}

if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){

	$result = false;
	$errors = array();
	$content = null;
	$need_reload = false;
	$redirect = null;

	foreach( $socials as $s ){
		if( isset($_REQUEST[transform_in_code($s['name'])]) && $_REQUEST[transform_in_code($s['name'])] ){

			if( !preg_match('/^https:/', $_REQUEST[transform_in_code($s['name'])]) ){
				$errors[] = _('L\'url n\'est pas valide pour '.$s['name']);
			}else{
				$current_config[transform_in_code($s['name'])] = $_REQUEST[transform_in_code($s['name'])];
			}
		}else{
			$current_config[transform_in_code($s['name'])] = "";
		}
	}

	if( isset($_REQUEST['subdomain'], $_REQUEST['domain']) && trim($_REQUEST['subdomain']) != '' ){

		if( strlen($_REQUEST['subdomain']) > 32 || strlen($_REQUEST['subdomain']) < 3 ){
			$errors[] = _('Le sous domaine doit être compris entre 3 et 32 caractères.');
		}
		else if( !preg_match('/^[a-z0-9\-]+$/', $_REQUEST['subdomain'])  ){
			$errors[] = _('Le sous domaine ne peut pas contenir de caractères spéciaux.');
		}
		else {
			$finded = RegisterGCP::existVhost($_REQUEST['subdomain'].'.'.$_REQUEST['domain']);
			if( $finded && $finded['tnt_id'] != $config['tnt_id']) {
				$errors[] = _('Le domaine est déjà pris veuillez en choisir un autre.');
			}

			if( sizeof( $errors )==0 ){
				$url = 'https://'.$_REQUEST['subdomain'].'.'.$_REQUEST['domain'];

				// mise à jour du websitelanguge
				wst_websites_languages_url_set($wst_extranet, $config['i18n_lng'], $url);

				// Mise à jour de variable de configuration contenant l'URL
				cfg_overrides_set_value( "site_url", $url, $wst_extranet);
				cfg_overrides_set_value( "site_password_lost_url", $url.'/reinitialisation-mot-de-passe/', $wst_extranet);

				// mise à jour du vhost dans le datastore
				RegisterGCP::setVhost($config['tnt_id'], $_REQUEST['subdomain'].'.'.$_REQUEST['domain']);
			}
		}
	}

	if( isset($_REQUEST['theme-color']) ){
		foreach( $nuanciers as $n ){
			if( transform_in_code($n['name']) == $_REQUEST['theme-color'] ){
				$current_config["theme-color"] = $_REQUEST['theme-color'];
				break;
			}
		}
	}

	if( isset($_FILES['imp-file']) && $_FILES['imp-file']['tmp_name'] ){

		$file_parts = pathinfo($_FILES['imp-file']['name']);

		if( !in_array(strtolower($file_parts['extension']), array('jpg','gif','png')) ){
			$errors[] = _('L\'extension de votre fichier n\'est pas valide : jpg / gif / png.');
		}else{
			// upload l'img sur riashop
			$img_id = img_images_add($_FILES['imp-file']['tmp_name']);
			if( !$img_id ){
				$errors[] = _('Erreur lors de l\'ajout de l\'image.');
			}else{
				$current_config['theme-logo-img'] = $img_id;
				$need_reload = true;
			}
		}
	}

	if( isset($_FILES['imp-favicon']) && $_FILES['imp-favicon']['tmp_name'] ){

		$file_parts = pathinfo($_FILES['imp-favicon']['name']);

		if( !in_array(strtolower($file_parts['extension']), array('ico')) ){
			$errors[] = _('L\'extension de votre fichier n\'est pas valide : ico.');
		}else{
			// upload l'img sur riashop
			$res_copy = copy( $_FILES['imp-favicon']['tmp_name'], $config['doc_dir'].'/favicon.ico');
			// $img_id = img_images_add($_FILES['imp-favicon']['tmp_name']);
			if( !$res_copy ){
				$errors[] = _('Erreur lors de l\'ajout de l\'image.');
			}else{
				$current_config['theme-favicon'] = 'uploaded';//$img_id;
				$need_reload = true;
			}
		}
	}


	// enregistrement des données si pas d'erreur meme si ya une erreur peut être que d'autre partie du formulaire son correcte
	if( !cfg_overrides_set_value( $cfg_code, json_encode($current_config), $wst_extranet ) ){
		$errors[] = _('Erreur lors de l\'enregistrement de vos paramètres.');
	}else{
		if( sizeof( $errors )==0 ){
			$result = true;
			if( $need_reload ){
				$redirect= '/admin/config/extranet/index.php';
			}
		}
	}

	if( sizeof($errors) ){
		$content = $errors;
	}



	// Envoie la réponse au navigateur
	http_response_code(200);
	header('Content-Type: application/json');
	echo json_encode(array(
		'result' => $result,
		'time' => date('Y-m-d H:i:s'),
		'redirect' => $redirect,
		'message' => '',
		'content' => $content,
	));
	exit;
}


// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
//gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');



// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Configuration'), '/admin/tools/index.php' )
	->push( _('Configuration de l\'extranet') );

define('ADMIN_PAGE_TITLE', 'Configuration de l\'extranet');
require_once('admin/skin/header.inc.php');

?>

<div class="cfg-extranet">
	<div class="head-title">
		<h2><?php echo  _('Configuration de l\'extranet'); ?></h2>
	</div>

	<?php
		if( isset($_SESSION['success-save']) ){
			print '<div class="success">'.nl2br( $_SESSION['success-save'] ).'</div>';
			unset($_SESSION['success-save']);
		}
	?>

	<div class="notif"></div>

	<form method="post" enctype="multipart/form-data" class="extranet-form" autocomplete="off">
		<div class="block-actions">
			<div class="block-action --logo">
				<h3 class="title"><?php echo _('Importer votre logo') ?></h3>
				<?php
					if( isset($current_config['theme-logo-img']) && $current_config['theme-logo-img'] ){
						$size = $config['img_sizes']['medium'];
						?>
						<div class="current-logo">
							<?php
							print '<img src="'.$config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$current_config['theme-logo-img'].'.'.$size['format'].'" />';
							?>
						</div>
						<p class="action-box-info">Logo actif</p>
						<?php
					}
				?>

				<p><?php print _('Déposez votre fichier ci-dessous par un simple glissé-déposé ou utiliser la fonction "Sélectionner un fichier" :') ?></p>
				<div class="box">
					<input class="box__file" type="file" id="imp-file" name="imp-file"  />
					<label for="imp-file"><span><?php print _('Sélectionner un fichier'); ?></span></label>
				</div>
				<p class="field-helper"><?php print str_replace('[taille max]', '136 x 44 px', _('Taille idéale : [taille max] (JPG, PNG, SVG)')); ?></p>




				<h4><?php echo _('Ajouter votre favicon') ?></h4>
				<?php
					if( isset($current_config['theme-favicon']) && $current_config['theme-favicon'] ){
						$size = $config['img_sizes']['medium'];
						?>
						<div class="current-logo">
							<?php
							print '<img src="'.$config['img_url'].'/../documents/favicon.ico" />';
							?>
						</div>
						<p class="action-box-info">Favicon actif</p>
						<?php
					}
				?>
				<p><?php print _('Le favicon est l\'icône visible dans votre navigateur : dans la barre d\'adresse, les onglets ou encore les autres raccourcis.') ?></p>
				<div class="box">
					<input class="box__file" type="file" id="imp-favicon" name="imp-favicon" />
					<label for="imp-favicon"><span><?php print _('Sélectionner un fichier'); ?></span></label>
				</div>
				<p class="field-helper"><?php print str_replace('[taille max]', '16 x 16 px', _('Taille idéale : [taille max] (ICO)')); ?></p>

			</div>
			<div class="block-action --color">
				<h3 class="title"><?php echo _('Déterminer vos couleurs') ?></h3>

				<p><?php print _('Choisissez parmi les nuanciers suivants :') ?></p>
				<select id="color-picker" name="theme-color">
					<?php
					foreach( $nuanciers as $n ){
						$selected = isset($current_config['theme-color']) && transform_in_code($n['name']) == $current_config['theme-color'];

						?><option <?php print $selected ? 'selected="selected"':''; ?> value="<?php print transform_in_code($n['name']); ?>"><?php print $n['name']; ?></option><?php
					}
					?>
				</select>

				<?php
					//affichage des nuanciers
					foreach( $nuanciers as $n ){
						$selected = isset($current_config['theme-color']) && transform_in_code($n['name']) == $current_config['theme-color'];
					?>
						<div class="color-group color-<?php print transform_in_code($n['name']); ?> <?php print $selected ? 'color-selected':''; ?>">
							<?php
							foreach( $n['colors'] as $c ){
								print '<div class="color-bar" style="background-color:'.$c.'"></div>';
							}
							?>
						</div>
						<?php
					}
				?>
				<p class="action-box-info">Nuancier actif</p>
			</div>
			<div class="block-action --social">
				<h3 class="title"><?php echo _('Gérer vos réseaux sociaux') ?></h3>

				<p><?php print _('Les comptes pour lesquels vous renseignerez une URL s\'afficheront sur votre extranet :') ?></p>

				<div>
					<?php
						foreach( $socials as $social ){
							?>
							<div class="row-social row-<?php print transform_in_code($social['name']); ?>">
								<span class="ico"></span>
								<input class="input-social"
									placeholder="Url de votre compte <?php print $social['name']; ?>"
									type="text"
									name="<?php print transform_in_code($social['name']); ?>"
									value="<?php print $social['value']; ?>" />
							</div>
							<?php
						}
					?>
				</div>
			</div>

		</div>
	</form>

	<form method="post" enctype="multipart/form-data" class="domain-form" autocomplete="off">

		<div class="block-actions">
			<div class="block-action --publication">
				<h3 class="title"><?php echo _('Configuration et publication') ?></h3>

				<label for="subdomain">Adresse de votre site :</label>
				<div class="domain-row">
					<input class=""
						placeholder="Sous domaine"
						id="subdomain"
						type="text"
						maxlength="20"
						name="subdomain"
						<?php print $subdomain ? 'disabled="disabled"' : '' ?>
						value="<?php print $subdomain; ?>" />

					<span>.</span>
					<select
						id="domain"
						name="domain"
						<?php print $subdomain ? 'disabled="disabled"' : '' ?>>
						<?php
							foreach( $domains as $d ){
								$selected = $d == $domain;

								?><option <?php print $selected ? 'selected="selected"':''; ?> value="<?php print $d; ?>"><?php print $d; ?></option><?php
							}
						?>
					</select>
				</div>

				<p class="field-helper"><?php print _('L\'adresse que vous choisirez pour votre site ne pourra être modifiée. Elle doit comporter de 3 à 32 caractères, et être composée, à votre convenance : de lettres minuscules, de chiffres et du caractère « - ». Les espaces, accents et autres caractères ne seront pas acceptés.'); ?></p>

				<p class="domain-text action-box-info">Adresse active : <span class="innerdomain"><?php print $subdomain.'.'.$domain ?></span></p>

				<?php
					if( !isset($subdomain) || !$subdomain ){
						?>
						<button id="domain-validate-btn" class="domain-button button btn-text">Valider</button>
						<?php
					}
				?>
				<a id="domain-see-btn" class="domain-button button btn-text" target="_blank" href="<?php print $config['site_url']; ?>">Voir votre extranet</a>

			</div>
		</div>
	</form>
</div>

<?php
require_once('admin/skin/footer.inc.php');
?>

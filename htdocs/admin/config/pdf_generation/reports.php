<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PDF-GENERATION');

// Enregistre les données du formulaire
if( isset($_POST['save']) ){
    $has_error = false;

    // Image du logo du rapport de visite
	if( isset($_FILES['reports-logo'] , $_FILES['reports-logo']['name']) && trim($_FILES['reports-logo']['name']) ){
		if( $img_id = img_images_upload('reports-logo') ){
			$has_error &= cfg_overrides_set_value('pdf_generation_reports_logo', $img_id);
		} else {
			$errors[] = _('Erreur lors de l\'upload du logo du rapport de visite');
		}
	}
    
    // Taille du logo du rapport de visite
	if( isset($_POST['reports-logo-size-x']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_reports_logo_size_x', intval($_POST['reports-logo-size-x']));
	}
	if( isset($_POST['reports-logo-size-y']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_reports_logo_size_y', intval($_POST['reports-logo-size-y']));
    }
    
    header('Location: /admin/config/pdf_generation/reports.php');
	exit;
}

// Suppression du logo
if( isset($_POST['delete-logo']) ){
	if( !cfg_overrides_set_value('pdf_generation_reports_logo', '') ){
		$error = _('Une erreur est survenue lors de la suppression du logo');
	}

	if( !isset($error) ){
		header('Location: /admin/config/pdf_generation/reports.php');
		exit;
	}
}

define('ADMIN_PAGE_TITLE', _('Rapports de visite').' - '._('Génération PDF').' - '._('Configuration'));

require_once 'admin/skin/header.inc.php';
?>

<form action="" method="post" class="config-generation-pdf" enctype="multipart/form-data">
	<div class="config-row">
        <h2><?php print _('Configuration du PDF des rapports') ?></h2>
        <h3><?php print _('Logo') ?></h3>
		<?php if( $config['pdf_generation_reports_logo'] ){ ?>
			<?php $size = $config['img_sizes']['medium']; ?>
			<div id="img<?php print $config['pdf_generation_reports_logo']; ?>" class="row" style="margin-bottom: 10px;">
				<img src="<?php print $config['img_url']; ?>/<?php print $size['dir']; ?>/<?php print $config['pdf_generation_reports_logo'];?>.jpg" height="<?php print $size['height']; ?>" width="<?php print $size['width']; ?>">
			</div>
		<?php } ?>

		<?php if( $config['pdf_generation_reports_logo'] ){ ?>
			<input type="submit" name="delete-logo" value="<?php print _('Supprimer le logo'); ?>">
			
		<?php } ?>
		<div class="row" style="flex-direction: column">
			<br>
			<div class="notice" style="margin-bottom: 1rem; padding: 1rem;">
				<?php print _('Les images transparentes ne seront pas prises en compte.') ?>
			</div>
			<div class="logo-form-group">
				<label for="reports-logo"><?php print $config['pdf_generation_reports_logo'] ? _('Modifier votre logo :') : _('Choisissez votre logo :'); ?></label>
				<input type="file" id="reports-logo" name="reports-logo">
				<br>
				<br>
				<label><?php print _('Taille du logo (en pixels) :') ?></label>
				<input type="number" name="reports-logo-size-x" value="<?php print htmlspecialchars($config['pdf_generation_reports_logo_size_x']) ?>">
				<span>x</span>
				<input type="number" name="reports-logo-size-y" value="<?php print htmlspecialchars($config['pdf_generation_reports_logo_size_y']) ?>">
			</div>
		</div>
		<br>
    </div>
    <div class="ria-admin-ui-actions">
        <input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
		<a href="/admin/config/pdf_generation/index.php">
			<button type="button" style="cursor: pointer;"><?php print _('Annuler'); ?></button>
		</a>
    </div>
</form>


<?php
require_once('admin/skin/footer.inc.php');
?>

<?php

/** \file pdf_generation/invoice.php
 * Ce fichier permet l'édition de la configuration pour la génération de facture au format PDF.
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PDF-GENERATION');
gu_if_authorized_else_403('_RGH_ADMIN_ORDER_DL_INVOICE');

require_once 'delivery.inc.php';

define('ADMIN_PAGE_TITLE', _('Facturation').' - '._('Configuration'));

if( isset($_POST['delete-logo']) ){
	if( !cfg_overrides_set_value('pdf_generation_inv_logo', 0) ){
		$error = _('Une erreur est survenue lors de la suppression du logo');
	}

	if(!isset($error)){
		header('Location: /admin/config/pdf_generation/invoice.php');
		exit;
	}
}

// Enregistre les données du formulaire.
if( isset($_POST['save']) ){
	$errors = array();
	$has_error = false;

	if( isset($_POST['inv-name']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_inv_name', $_POST['inv-name']);
	}

	$has_error &= cfg_overrides_set_value('pdf_generation_inv_url', isset($_POST['inv-url']) ? trim($_POST['inv-url']) : '');

	if( isset($_FILES['inv-logo'] , $_FILES['inv-logo']['name']) && trim($_FILES['inv-logo']['name']) ){
		if( $img_id = img_images_upload('inv-logo') ){
			$has_error &= cfg_overrides_set_value('pdf_generation_inv_logo', $img_id);
		} else {
			$errors[] = _('Erreur lors de l\'upload du logo de la facture');
		}
	}

	if( isset($_POST['inv-logo-size-x']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_inv_logo_size_x', intval($_POST['inv-logo-size-x']));
	}

    if( isset($_POST['inv-logo-size-y']) ){
        $has_error &= cfg_overrides_set_value('pdf_generation_inv_logo_size_y', intval($_POST['inv-logo-size-y']));
    }
	if( isset($_POST['inv-logo-pos-x']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_inv_logo_pos_x', intval($_POST['inv-logo-pos-x']));
	}

	if( isset($_POST['inv-logo-pos-y']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_inv_logo_pos_y', intval($_POST['inv-logo-pos-y']));
	}
        
	if( isset($_POST['inv-font-size']) ){
		$has_error &= cfg_overrides_set_value('pdf_generation_inv_font_size', intval($_POST['inv-font-size']));
	}

    $has_error &= cfg_overrides_set_value('pdf_generation_inv_prd_img', intval(isset($_POST['inv-prd-img']) && $_POST['inv-prd-img']));

	if( $has_error ){
		$errors[] = _('Erreur lors de l\'enregistrement de la configuration de la facture');
	}

	$has_error = false;

	header('Location: /admin/config/pdf_generation/invoice.php');

	exit;
}

require_once('admin/skin/header.inc.php');

	if( isset($errors) && is_array($errors) ){
		foreach( $errors as $error ){ ?>
			<div class="error"><?php print nl2br(htmlspecialchars($error)); ?></div>
		<?php }
	} ?>
	<form action="" method="post" class="config-generation-pdf" enctype="multipart/form-data">
		<div class="config-row">
			<h2><?php print _('Configuration du PDF des factures') ?></h2>
			<h3><?php print _('Entête') ?></h3>
			<div class="pdf-header">
				<div class="notice">
					<p><?php print _('La balise #param[ref]# sera remplacée par la référence de la facture.') ?></p>
					<p>
						<span><?php print _('Le lien du site par défaut est : '); ?></span>
						<span class="bold"><?php print $config['site_url']; ?></span>
					</p>
				</div>
				<div>
					<label for="inv-name"><?php print _('Nom :'); ?></label>
					<input type="text" id="inv-name" name="inv-name" value="<?php print htmlspecialchars($config['pdf_generation_inv_name']) ?>">
				</div>
				<div>
					<label for="inv-url"><?php print _('Lien du site :'); ?></label>
					<input type="text" id="inv-url" name="inv-url" value="<?php print isset($config['pdf_generation_inv_url']) ? htmlspecialchars($config['pdf_generation_inv_url']) : ''; ?>">
				</div>
			</div>
			<h3><?php print _('Logo') ?></h3>
			<?php if( $config['pdf_generation_inv_logo'] ){
				$size = $config['img_sizes']['medium']; ?>
				<div id="img<?php print $config['pdf_generation_inv_logo']; ?>" class="row">
					<img src="<?php print $config['img_url']; ?>/<?php print $size['dir']; ?>/<?php print $config['pdf_generation_inv_logo'];?>.jpg" height="<?php print $size['height']; ?>" width="<?php print $size['width']; ?>">
				</div>
			<?php } ?>
			<div class="pdf-logo">
				<div class="notice">
					<?php print _('Les images transparentes ne seront pas prises en compte.') ?>
				</div>
				<div class="logo-form-group">
					<label for="inv-logo"><?php print _($config['pdf_generation_inv_logo'] ? _('Modifier votre logo :') : _('Choisissez votre logo :')) ?></label>
					<input type="file" id="inv-logo" name="inv-logo">
					<br>
					<label><?php print _('Taille du logo (en pixels) :') ?></label>
					<input type="number" name="inv-logo-size-x" value="<?php print htmlspecialchars($config['pdf_generation_inv_logo_size_x']) ?>">
					<span>x</span>
					<input type="number" name="inv-logo-size-y" value="<?php print htmlspecialchars($config['pdf_generation_inv_logo_size_y']) ?>">
				</div>
			</div>
			<?php if( $config['pdf_generation_inv_logo'] ){ ?>
				<input type="submit" name="delete-logo" value="<?php print _('Supprimer le logo'); ?>">
			<?php } ?>
			<h3><?php print _('Contenu') ?></h3>
			<div>
				<label for="inv-font-size"><?php print _('Taille de la police :'); ?></label>
				<select id="inv-font-size" name="inv-font-size">
					<option value="8" <?php print intval($config['pdf_generation_inv_font_size']) == 8 ? 'selected' : ''; ?>>8</option>
					<option value="9" <?php print intval($config['pdf_generation_inv_font_size']) == 9 ? 'selected' : ''; ?>>9</option>
					<option value="10" <?php print intval($config['pdf_generation_inv_font_size']) == 10 ? 'selected' : ''; ?>>10</option>
					<option value="11" <?php print intval($config['pdf_generation_inv_font_size']) == 11 ? 'selected' : ''; ?>>11</option>
					<option value="12" <?php print intval($config['pdf_generation_inv_font_size']) == 12 ? 'selected' : ''; ?>>12</option>
					<option value="14" <?php print intval($config['pdf_generation_inv_font_size']) == 14 ? 'selected' : ''; ?>>14</option>
					<option value="16" <?php print intval($config['pdf_generation_inv_font_size']) == 16 ? 'selected' : ''; ?>>16</option>
					<option value="18" <?php print intval($config['pdf_generation_inv_font_size']) == 18 ? 'selected' : ''; ?>>18</option>
					<option value="20" <?php print intval($config['pdf_generation_inv_font_size']) == 20 ? 'selected' : ''; ?>>20</option>
					<option value="24" <?php print intval($config['pdf_generation_inv_font_size']) == 24 ? 'selected' : ''; ?>>24</option>
				</select>
			</div>
			<div>
				<label><?php print _('Souhaitez-vous afficher l\'image du produit ?') ?></label>
				<label for="inv-prd-img-yes">
					<input type="radio" id="inv-prd-img-yes" name="inv-prd-img" value="1" <?php print $config['pdf_generation_inv_prd_img'] ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="inv-prd-img-no">
					<input type="radio" id="inv-prd-img-no" name="inv-prd-img" value="0" <?php print !$config['pdf_generation_inv_prd_img'] ? 'checked' : ''; ?>>
					<?php print _('Non') ?>
				</label>
			</div>
		</div>
		<div class="ria-admin-ui-actions">
            <input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
            <input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" />
        </div>
		<a href="/admin/config/pdf_generation/index.php">
			<button type="button"><?php print _('Retour'); ?></button>
		</a>
	</form>

<?php require_once('admin/skin/footer.inc.php'); ?>

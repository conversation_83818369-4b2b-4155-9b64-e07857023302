<?php
    /** \file inde.php
     * Ce fichier est la page qui permet la configuration des diverse variable pour Avis Vérifiés
     * Les variable sont :
     *     - la clé secrète
     *     - l'identifiant du website
     *     - 3 URLs qui permettent la récupération des avis
     */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
    gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_AVIS_VERIFIE');

    // Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Avis Vérifiés') . ' - ' . _('Configuration'));

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Avis Vérifiés') );


    if(isset($_POST['avis_verifie_activer']) && $_POST['avis_verifie_activer'] == 1){
        if((isset($_POST['avis_verifie_secret_key']) && $_POST['avis_verifie_secret_key'] == "") ||
           (isset($_POST['avis_verifie_website_id']) && $_POST['avis_verifie_website_id'] == "") ||
           (isset($_POST['avis_verifie_url_avis_site']) && $_POST['avis_verifie_url_avis_site'] == "") ||
           (isset($_POST['avis_verifie_url_list_products']) && $_POST['avis_verifie_url_list_products'] == "") ||
           (isset($_POST['avis_verifie_url_avis_produit']) && $_POST['avis_verifie_url_avis_produit'] == "") ||
           (isset($_POST['avis_verifie_debut']) && $_POST['avis_verifie_debut'] == "")
       ){
            $error = _("Une ou plusieurs informations obligatoires sont manquantes");
        }else{
            //Si l'on n'a pas réussi à écrire les données en base, on met un message d'erreur
            if (!cfg_overrides_set_value('avis_verifie_activer', $_POST['avis_verifie_activer'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_secret_key', $_POST['avis_verifie_secret_key'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_website_id', $_POST['avis_verifie_website_id'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_url_avis_site', $_POST['avis_verifie_url_avis_site'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_url_list_products', $_POST['avis_verifie_url_list_products'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_url_avis_produit', $_POST['avis_verifie_url_avis_produit'],$config['wst_id'])
                || !cfg_overrides_set_value('avis_verifie_debut', dateparse($_POST['avis_verifie_debut']),$config['wst_id'])
            ) {
                $error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de votre configuration. Veuillez réessayer ou prendre contact pour nous signaler le problème.');
            }

        }
    }

    require_once('admin/skin/header.inc.php');
?>

<h2><?php echo _("Avis Vérifiés"); ?></h2>

<div class="notice">
    <p><?php echo _("Voici les informations dont vous aurez besoin pour l'activation de la fonctionnalité Avis Vérifiés :"); ?></p>
    <ol>
    <?php
        $count = 0;
        $etapes = array(
            "Etape 1" => _("Allez sur Avis Vérifiés et authentifiez-vous"),
            "Etape 2" => _("Allez dans le menu") .  " <a href='https://www.avis-verifies.com/index.php?page=mod_param_contact'>configuration > Mon compte</a>",
            "Etape 3" => _("Depuis le panel \"Codes d'Intégrations\", renseignez votre clé secrète et l'identifiant du site web ci-dessous"),
            "Etape 4" => _("Allez dans le menu") . " <a href='https://www.avis-verifies.com/index.php?page=mod_avis_configuration_v3'>Intégration > Récupérer vos avis</a>",
            "Etape 5" => _("Cliquez sur l'onglet \"API Site\" :") . "<ul><li>" . _('Cochez la case "Activer la génération quotidienne des fichiers d\'avis site"') . "</li><li>" . _('Sélectionnez le format JSON puis enregistrer la configuration') . "</li></ul>",
            "Etape 6" => _("Sur ce même onglet, vous pourrez récupérer l'URL d'accès aux fichiers (sans le \".[format]\") et la renseigner ci-dessous"),
            "Etape 7" => _("Cliquez sur l'onglet \"API produit\"") . "<ul><li>" . _('Cocher la case "Activer la génération quotidienne des fichiers listes d\'avis par produit."') . "</li><li>" . _('Sélectionner le format JSON puis enregistrer la configuration') . "</li></ul>",
            "Etape 8" => _("Sur ce même onglet, vous pourrez récupérer :"). "<ul><li>" . _('L\'URL "Fichier liste des produits ayant un avis" et la renseigner ci-dessous') . "</li><li>" . _('L\'URL "Fichiers liste d\'avis par produit" sans le "[id_product].[format]" et la renseigner ci-dessous') . "</li></ul>"
        );
        foreach( $etapes as $etape => $consigne ){
            if( $count==0 ){
                print '	<li class="more-point">'. htmlspecialchars( $consigne ) .'...</li>';
            }
            print '		<li'.( $count>=0 ? ' class="more-info"' : '' ).'>'. htmlspecialchars( $consigne ).'</li>';
            $count++;
        }
    ?>
    </ol>
    <a class="more" onclick="return displayNotice();"><?php echo _("Lire la suite"); ?></a>
    <a class="more more-hide" onclick="return displayNotice();"><?php echo _("Réduire"); ?></a>
</div>
<p><?php echo _("Les paramètres ci-dessous vous permettent de gérer votre compte Avis Vérifiés."); ?></p>

    <form id="formulaire_identifiants_avis_verifie" method="post" action="/admin/config/avis_verifie/index.php">
        <?php
            if (isset($error)) {
                ?><div class="error"><?php print _(nl2br($error)); ?></div><?php
            }

            if (isset($_SESSION['instagram_callback_success']) && $_SESSION['instagram_callback_success']) {
                ?><div class="success"><?php echo _("Le token a bien été généré."); ?></div><?php
                unset($_SESSION['instagram_callback_success']);
            }
        ?>

        <dl>
            <dt><?php echo _("Données relatives à votre compte Avis Vérifiés"); ?></dt>
            <dd>
                <label for="avis_verifie_activer"><?php echo _("Activer Avis Vérifiés"); ?><span class="mandatory"></span> : </label>
                <label for="avis_verifie_activer"><input type="radio" id="avis_verifie_activer" name="avis_verifie_activer" value="1" <?php print (($config['avis_verifie_activer'])?'checked':''); ?>> <?php print _('Oui'); ?></label> <label for="avis_verifie_desactiver"><input type="radio" name="avis_verifie_activer" id="avis_verifie_desactiver" value="0"<?php print ((!$config['avis_verifie_activer'])?' checked':''); ?>> <?php print _('Non'); ?></label>

            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_debut"><span class="mandatory">*</span> <?php echo _("A partir du :"); ?></label>
               <input id="avis_verifie_debut" class="datepicker date" type="text" name="avis_verifie_debut"  value="<?php print htmlspecialchars(dateheureunparse(cfg_overrides_get_value('avis_verifie_debut',$config['wst_id'])));?>" />

            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_secret_key"><span class="mandatory">*</span> <?php echo _("Votre clé secrète :"); ?></label>
                <input id="avis_verifie_secret_key" type="text" name="avis_verifie_secret_key" size="60" value="<?php print htmlspecialchars(cfg_overrides_get_value('avis_verifie_secret_key',$config['wst_id']));?>" />
            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_website_id"><span class="mandatory">*</span> <?php echo _("Pour le site Site Web :"); ?></label>
                <select name="avis_verifie_website_id" id="avis_verifie_website_id">
                <?php
                    $selected = cfg_overrides_get_value( 'avis_verifie_website_id', $config['wst_id'] );
                    $websites = wst_websites_get();
                    while( $wst = ria_mysql_fetch_array($websites) ){
                        if( $wst['type_id']!=6 ){
                            print '<option value="'.$wst['id'].'"'.( $wst['id']==$selected ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $wst['name'] ).'</option>';
                        }
                    }
                ?>
                </select>
            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_url_avis_site"><span class="mandatory">*</span> <?php echo _("URL d'accès aux fichiers :"); ?></label>
                <input id="avis_verifie_url_avis_site" type="text" name="avis_verifie_url_avis_site" size="60" value="<?php print htmlspecialchars(cfg_overrides_get_value('avis_verifie_url_avis_site',$config['wst_id']));?>" />
            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_url_list_products"><span class="mandatory">*</span> <?php echo _("Fichier liste des produits ayant un avis :"); ?></label>
                <input id="avis_verifie_url_list_products" type="text" name="avis_verifie_url_list_products" size="60" value="<?php print htmlspecialchars(cfg_overrides_get_value('avis_verifie_url_list_products',$config['wst_id']));?>" />
            </dd>
            <dd class="block-avis<?php print (($config['avis_verifie_activer'])? '' : ' none'); ?>">
                <label for="avis_verifie_url_avis_produit"><span class="mandatory">*</span> <?php echo _("Fichiers liste d'avis par produit :"); ?></label>
                <input id="avis_verifie_url_avis_produit" type="text" name="avis_verifie_url_avis_produit" size="60" value="<?php print htmlspecialchars(cfg_overrides_get_value('avis_verifie_url_avis_produit',$config['wst_id']));?>" />
            </dd>
        </dl>

        <button name="button_save" type="submit" title="Sauvegarder les données relatives à votre compte Avis Vérifiés"><?php print _('Sauvegarder'); ?></button>
   </form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
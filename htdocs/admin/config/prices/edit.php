<?php

/**	\file edit.php
 *	Cette page sert de fiche de création et de modification aux catégories tarifaires.
 */

require_once('gu.categories.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PRICE_EDIT');

// Vérifie que la catégorie demandée tarifaire existe
if( !prd_prices_categories_exists(isset($_GET['prc']) ? $_GET['prc'] : 0) ){
	header('Location: index.php');
	exit;
}

// Bouton Supprimer la catégorie tarifaire
if( isset($_POST['delete']) ){
	if( !prd_prices_categories_get_is_sync($_GET['prc']) ){
		if( !prd_prices_categories_del($_GET['prc']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie tarifaire.");
		}
	}
	if( !isset($error) ){
		header('Location: index.php');
		exit;
	}
}

$error = false;

// Modification de la catégorie tarifaire.
if( isset($_POST['save']) ){
	$_POST['name'] = isset($_POST['name']) ? trim($_POST['name']) : '';

	if( !$_POST['name'] ){
		$error = _('Veuillez indiquer le nom de la catégorie tarifaire.');
	}else{
		if( !prd_prices_categories_update($_GET['prc'], $_POST['name'], isset($_POST['is-ttc']), $_POST['currency']) ){
			$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de la catégorie tarifaire.');
		}
	}

	if( !$error ){
		header('Location: index.php');
		exit;
	}
}

$prc = ria_mysql_fetch_assoc(
	prd_prices_categories_get($_GET['prc'])
);

// Devises utilisables. Les devises acceptées sont régies par la norme ISO 4217.
$currencies = array(
	'EUR' => _('Euro'),
	'USD' => _('Dollar des Etats-Unis'),
	'GBP' => _('Livre sterling'),
	'CHF' => _('Franc Suisse'),
	'DZD' => _('Dinar algérien'),
	'PLN' => _('Zloty')
);

// Défini le titre de la page
$page_title = _('Catégorie tarifaire').' : '.htmlspecialchars($prc['name']);
define('ADMIN_PAGE_TITLE', $page_title . ' - ' . _('Catégories Tarifaires') . ' - ' . _('Configuration'));

require_once('admin/skin/header.inc.php');

if( $error ) { ?>
	<div class="error"><?php print nl2br(htmlspecialchars($error)); ?></div>
<?php } ?>
<h2><?php print view_prc_is_sync($prc).'&nbsp;'.htmlspecialchars( $page_title ); ?></h2>
<form action="edit.php?prc=<?php print $prc['id']; ?>" method="post" onsubmit="return prcValidForm(this);">
	<table>
		<tbody>
			<tr>
				<td class="col170px">
					<label for="name"><span class="mandatory">*</span> <?php print _('Désignation :'); ?></label>
				</td>
				<td>
					<input type="text" id="name" name="name" value="<?php print htmlspecialchars($prc['name']); ?>" maxlength="75" <?php print $prc['is_sync'] ? 'readonly' : ''; ?>>
				</td>
			</tr>
			<tr>
				<td>
					<label for="currency"><?php print _('Devise :'); ?></label>
				</td>
				<td>
					<select id="currency" name="currency">
						<?php foreach( $currencies as $code => $currency ){ ?>
							<option value="<?php print $code; ?>" <?php print $prc['money_code'] === $code ? 'selected' : ''; ?>><?php print htmlspecialchars( $code.' - '.$currency ); ?></option>
						<?php } ?>
					</select>
				</td>
			</tr>
			<tr>
				<td>
					<label for="is-ttc">
						<?php print _('Prix'); ?>
						<abbr title="<?php print _('Toutes Taxes Comprises'); ?>">TTC</abbr>
						<span>*</span> :
					</label>
				</td>
				<td class="row_check_ttc">
					<input type="checkbox" id="is-ttc" name="is-ttc" <?php print $prc['ttc'] ? 'checked' : ''; ?> <?php print $prc['is_sync'] ? 'disabled' : ''; ?>>
				</td>
			</tr>
			<tr>
				<td><?php print _('Comptes clients :'); ?></td>
				<td><?php print ria_number_format($prc['users']); ?></td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
					<a href="index.php" class="button"><?php print _('Annuler'); ?></a>
					<?php if( $prc['id']>0 && !$prc['is_sync'] ){ ?>
					<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
	<span class="info-bulle">* <?php print str_replace("#param[nb_arrondi]#", $config['round_digits_count'], _('En cochant cette case, l\'arrondi à #param[nb_arrondi]# chiffres des tarifs sera effectué sur le prix TTC.')); ?></span>
</form>

<?php

require_once('admin/skin/footer.inc.php');
<?php

	/**	\file index.php
	 *	Cette page affiche la liste des Conditions Générales de Vente enregistrées
	 */

	require_once('cgv.inc.php');
	require_once('websites.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CGV');

	unset($error);

	// Gère le sélecteur de site
	if( isset($_GET['wst']) ){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = '0';
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
		header('Location: index.php');
		exit;
	}
	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;

	// Bouton Ajouter
	if( isset($_POST['name']) && trim($_POST['name']) ){
		if( !cgv_versions_add($_POST['name'], $wst_id) )
			$error = _("Une erreur est survenue lors de la création de la version.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( isset($_POST['ver']) && is_array($_POST['ver']) ){
			$error = '';
			foreach( $_POST['ver'] as $v )
				if( !cgv_versions_del($v, $wst_id) ){
					if( cgv_versions_exists($v, $wst_id) ){
						$ver = ria_mysql_fetch_array(cgv_versions_get($v, $wst_id));
					}else{
						$ver['name'] = '<Inconnue>';
						$ver['publish-date'] = '';
					}
					if( $ver['publish-date'] )
						$error .= _("La version {$ver['name']} a été publiée sur le site, et ne peut donc plus être supprimée.");
					else
						$error .= _("Une erreur inattendue s'est produite lors de la suppression de la version ${ver['name']}.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.")."\n";
				}
			if( !trim($error) ){
				header('Location: index.php');
				exit;
			}
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Conditions Générales de Vente') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Conditions Générales de Vente') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Conditions générales de vente'); ?></h2>

	<div class="notice"><?php print _('Pour des raisons légales, les versions successives de vos conditions générales de vente doivent être conservées. Pour modifier vos conditions générales de vente, il vous suffit de créer une version vierge ou de dupliquer une version existante.'); ?></div>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>
	<div class="stats-menu">
		<div class="ria-admin-ui-filters">
		<?php
			$website = wst_websites_get();
			// si le nombre de site du locataire est supérieur à un on affiche le filtre pour trier par site
			if(ria_mysql_num_rows($website)>1) {
				print view_websites_selector( $wst_id, false, '', true );
			} else {
				$wst_id = $config['wst_id'];
			}

			$actiondel = false;
			if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_DEL') && $wst_id ){
				$versions = cgv_versions_get( null, $wst_id, true );
				if( $versions && ria_mysql_num_rows($versions) ){
					while( $version = ria_mysql_fetch_array($versions) ){
						if( $version['publish-date']=='' ){
							$actiondel = true;
						}
					}
					ria_mysql_data_seek( $versions, 0 );
				}
			}
		?>
		</div>
	</div>
	<form action="index.php" method="post">
	<table id="table-cgv" class="checklist">
		<thead>
			<tr>
				<th id="cgv-sel"><?php print $actiondel ? '<input type="checkbox" class="checkbox" onclick="checkAllClick(this)" />' : ''; ?></th>
				<th id="cgv-name"><?php print _('Nom de la version'); ?></th>
				<th id="cgv-pub"><?php print _('Date de publication'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( is_numeric($wst_id) && $wst_id>0 ){

					$versions = cgv_versions_get( null, $wst_id, true );
					if( !ria_mysql_num_rows($versions) ){
						print '<tr><td colspan="3">'._('Aucune version').'</td></tr>';
					}else{
						$current = cgv_versions_get_current();
						while( $r = ria_mysql_fetch_array($versions) ){
							print '	<tr'.( $r['id']==$current ? ' class="current"' : '' ).'>
										<td headers="cgv-sel">';
							if( trim($r['publish-date'])=='' ){
								print '<input type="checkbox" class="checkbox" name="ver[]" value="'.$r['id'].'" />';
							}
							print '</td>';
							if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_EDIT') ){
								print '<td headers="cgv-name"><a href="articles.php?ver='.$r['id'].'&amp;wst='.$wst_id.'">'.htmlspecialchars($r['name']).'</a></td>';
							}else{
								print '<td headers="cgv-name">'.htmlspecialchars($r['name']).'</td>';
							}
							print '		<td headers="cgv-pub">'.ria_date_format($r['publish-date']).'</td>
									</tr>';
						}
					}
				} else {
					print '<tr><td colspan="3">'._('Veuillez sélectionner un site.').'</td></tr>';
				}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3">
					<?php print $actiondel ? '<input type="submit" name="del" class="btn-del" value="'._('Supprimer').'" onclick="return confirmCgvVersionsDelList();" />' : ''; ?>
					<div class="float-right">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_ADD') ){ ?>
						<label for="name"><?php print _('Ajouter une version :'); ?></label> <input type="text" name="name" id="name" maxlength="75" /> <input type="submit" value="<?php print _('Ajouter'); ?>" onclick="return validCgvVersionAdd(this.form)" />
						<?php } ?>
					</div>
				</td>
			</tr>
		</tfoot>
	</table>
	</form>

<?php
	require_once('admin/skin/footer.inc.php');
?>
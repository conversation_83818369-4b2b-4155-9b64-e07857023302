<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CGV');

	require_once('cgv.inc.php');

	isset($_GET['wst']) ? $wst = $_GET['wst'] : $wst = $config['wst_id'];
	
	// Vérifie l'existance de la version
	if( !isset($_GET['ver']) || !cgv_versions_exists($_GET['ver'], $wst) ){
		header('Location: index.php?wst='.$wst);
		exit;
	}

	// Vérifie l'existance de l'article (mode édition uniquement)
	if( isset($_GET['art']) && $_GET['art']!=0 && !cgv_articles_exists($_GET['art'], $wst) ){
		header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		cgv_articles_del($_GET['art'], $wst);
		header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
		exit;
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['name']) || !isset($_POST['desc']) )
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.")."\n"._("Veuillez vérifier.");
		elseif( !trim($_POST['name']) || !trim($_POST['desc']) )
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une * sont obligatoires.");
		elseif( $_GET['art']==0 ){
			if( !cgv_articles_add($_GET['ver'],$_POST['name'],$_POST['desc'],null, null, $wst) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'article.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
		}else{
			if( !isset($_GET['lng']) || !in_array($_GET['lng'], $config['i18n_lng_used']) || $_GET['lng']==$config['i18n_lng'] ){
				if( !cgv_articles_update($_GET['art'],$_POST['name'],$_POST['desc'], $wst) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'article.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
				}
			} else {
				// enregistre la traduction de l'article
				$values = array(
					_FLD_CGV_ART_NAME => $_POST['name'],
					_FLD_CGV_ART_DESC => $_POST['desc']
				);
				
				if( !fld_translates_add($_GET['art'], $_GET['lng'], $values) )
					$error = _("Une erreur inattendue s'est produite lors de la traduction de l'article.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
			}
		}
		if( !isset($error) ){
			header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
			exit;
		}
	}

	$ver = ria_mysql_fetch_array(cgv_versions_get($_GET['ver'], $wst));
	
	$readonly = trim( $ver['publish-date'] ) != '' ? true : false;
	if( isset($_SERVER['SCRIPT_URI']) && preg_match('/(\.maquettes\.riastudio\.fr|\.preprod\.riastudio\.fr)/i', $_SERVER['SCRIPT_URI']) ){
		$readonly = false;
	}


	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	$art = array( 'id'=>0, 'name'=>'', 'desc'=>'' );

	if( isset($_GET['name']) ){
		$art['name'] = ucfirst(trim($_GET['name']));
	}
	if( isset($_GET['art']) && $_GET['art']>0 ){
		$art = ria_mysql_fetch_array(cgv_articles_get($_GET['art'], null , null, $wst));
	}

	// Récupère les informations traduite
	if( $art['id']>0 && $lng!=$config['i18n_lng'] ){
		$tsk_art = fld_translates_get( CLS_CGV_ARTICLE, $art['id'], $lng, $art, array(_FLD_CGV_ART_NAME=>'name', _FLD_CGV_ART_DESC=>'desc'), true );
		$art['name'] = $tsk_art['name'];
		$art['desc'] = $tsk_art['desc'];
	}

	// titre de la page
	if( !isset($_GET['name']) ) {
		$title = $art['name'];
	} else {
		$title = _('Nouvel article de CGV');
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Conditions Générales de Vente'), '/admin/config/cgv/index.php' )
		->push( $title );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title . ' - ' . _('Conditions Générales de Vente') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

?>
	<h2><?php print htmlspecialchars($title); ?></h2>
	
	<?php

		// Affiche le menu de langue
		print view_translate_menu( 'edit.php?ver='.$_GET['ver'].'&amp;art='.$art['id'].'&amp;wst='.$wst, $lng );
	
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="edit.php?ver=<?php print $_GET['ver']; ?>&amp;art=<?php print $art['id']; ?>&amp;wst=<?php print $wst; ?>&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return validCgvArticleForm(this)">
		<table id="table-cgv-edit-article">
			<caption><?php print _('Fiche'); ?></caption>
			<tbody>
				<tr class="first">
					<td><label for="name"><span class="mandatory">*</span> <?php print _('Titre de l\'article :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($art['name']); ?>" <?php if( $readonly ) print 'disabled="disabled"'; ?> /></td>
				</tr>
				<tr>
					<td><label for="desc"><span class="mandatory">*</span> <?php print _('Contenu de l\'article :'); ?></label></td>
					<td><?php
						require_once('views.inc.php');
						if( $readonly ){
							print '<div class="riawysiwyg riawysiwyg-disabled" disabled="disabled">'.view_site_format_riawysiwyg($art['desc'], false, true, false, false).'</div>';
						} else {
							print '<textarea class="tinymce" name="desc" id="desc" rows="10" cols="50">'.view_site_format_riawysiwyg($art['desc'], false, true, false, false).'</textarea>';
						}
					?></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<?php if( !$readonly ){ ?>
					<input type="submit" name="save" id="save" value="<?php print _('Enregistrer'); ?>" />
					<?php } ?>
					<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" />
					<?php if( $art['id'] && !$readonly ){ ?>
					<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" onclick="return confirmCgvArticleDel();" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
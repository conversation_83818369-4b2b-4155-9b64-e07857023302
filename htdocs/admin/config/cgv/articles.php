<?php

	/**	\file articles.php
	 *
	 * 	Cette page permet l'affichage d'un article des conditions générales de vente
	 *
	 */

	require_once('cgv.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CGV_EDIT');

	unset($error);

	isset($_GET['wst']) ? $wst = $_GET['wst'] : $wst = $config['wst_id'];

	// Vérifie l'existance de la version
	if( !isset($_GET['ver']) || !cgv_versions_exists($_GET['ver'], $wst) ){
		header('Location: index.php?wst='.$wst);
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php?wst='.$wst);
		exit;
	}

	// Bouton Dupliquer / Copier
 	if( isset($_POST['copy']) ){
 		$id = cgv_versions_copy($_GET['ver'], $wst);
 		if( $id===false ){
 			$error = _("L'opération de copie a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
 		}else{
 			header('Location: articles.php?ver='.$id.'&wst='.$wst);
 			exit;
 		}
 	}

	// Publication d'une version
	if( isset($_POST['publish']) ){
		if( $id = cgv_versions_publish( $_GET['ver'], $wst ) ){
 			header('Location: articles.php?ver='.$id.'&wst='.$wst);
			exit;
		}else{
			if( cgv_versions_is_empty($_GET['ver'], $wst) )
				$error = _("Cette version est vide.")."\n"._("Pour être publiée, la version doit contenir au minimum un article.");
			else
				$error = _("Une erreur inattendue s'est produite lors de la publication de cette version.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}

	// Fonction d'ajout
	if( isset($_POST['artname']) && (trim($_POST['artname']) || isset($_POST['add'])) && !isset($_POST['del']) && !isset($error) ){
		header('Location: edit.php?ver='.$_GET['ver'].'&name='.urlencode($_POST['artname']).'&wst='.$wst);
		exit;
	}

	// Déplacement vers le haut
	if( isset($_GET['up']) && is_numeric($_GET['up']) ){
		cgv_articles_move_up($_GET['up'], $wst);
		header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['down']) && is_numeric($_GET['down']) ){
		$res = cgv_articles_move_down($_GET['down'], $wst);
		if( $res ){
			header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
			exit;
		}else{
			$error = _("Une erreur inattendue s'est produite.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}

	// Bouton "Enregistrer"
	if( isset($_POST['save']) && isset($_POST['name']) ){
		if( !cgv_versions_update( $_GET['ver'], $_POST['name'], $wst ) ){
			if( !trim($_POST['name']) ){
				$error = _("Le nom de la version est obligatoire. Veuillez l'indiquer.");
			}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	// Suppression de la version
	if( isset($_POST['delete']) ){
		if( cgv_versions_del($_GET['ver'], $wst) ){
			header('Location: index.php?wst='.$wst);
			exit;
		}else{
			$ver = ria_mysql_fetch_array(cgv_versions_get($_GET['ver'], $wst));
			if( $ver['publish-date'] )
				$error = _("Cette version à été publiée sur le site.")."\n"._("Pour des raisons légales, elle ne peut pas être supprimée.");
			else
				$error = _("Une erreur inattendue s'est produite lors de la suppression de cette version.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}

	// Suppression
	if( isset($_POST['del']) ){
		if( isset($_POST['art']) ){
			foreach( $_POST['art'] as $r )
				cgv_articles_del($r, $wst);
		}
		header('Location: articles.php?ver='.$_GET['ver'].'&wst='.$wst);
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Conditions Générales de Vente'), '/admin/config/cgv/index.php' )
		->push( _('CGV').' - '.$ver['name'] );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Conditions Générales de Vente') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	$ver = ria_mysql_fetch_array(cgv_versions_get($_GET['ver'], $wst));
?>

	<h2><?php print _('CGV'); ?> - <?php print htmlspecialchars($ver['name']); ?></h2>

	<?php
		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	?>

	<form action="articles.php?ver=<?php print $ver['id']; ?>&amp;wst=<?php print $wst; ?>" method="post" onsubmit="return validCgvVersionForm(this)">
		<input type="hidden" name="sort-ver" id="sort-ver" value="<?php print $ver['id']; ?>" />
	<table id="table-fiche-version">
		<caption><?php print _('Fiche'); ?></caption>
		<tbody>
			<tr>
				<td><label for="name"><?php print _('Nom de la version :'); ?></label></td>
				<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($ver['name']); ?>" maxlength="75" /></td>
			</tr>
			<tr class="publish">
				<td><label for="publish-date"><?php print _('Date de publication :'); ?></label></td>
				<td>
					<input type="text" name="publish-date" id="publish-date" disabled class="date" value="<?php print ria_date_format($ver['publish-date']); ?>" maxlength="10" <?php if( trim($ver['publish-date']) ) print 'disabled="disabled"'; ?> />
					<?php if( !trim($ver['publish-date']) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_PUBLISH') ){ ?>
						<input type="submit" name="publish" value="<?php print _('Publier maintenant'); ?>" onclick="return confirmCgvVersionsPublish()" />
					<?php } ?>
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />

				<input type="submit" name="cancel" value="<?php print _('Retour à la liste'); ?>" />
				<?php if( $ver['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_ADD') ){ ?>
					<input type="submit" name="copy" value="<?php print _('Copier'); ?>" title="<?php print _('Copier cette version et tous les articles qu\'elle contient'); ?>" />
				<?php }
					if( !trim($ver['publish-date']) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CGV_DEL') ){ ?>
					<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" onclick="return confirmCgvVersionsDel();" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>

	<table id="cgv" class="checklist">
		<caption><?php print _('Articles'); ?></caption>
		<thead>
			<tr>
				<th id="art-name"><?php print _('Titre'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				$articles = cgv_articles_get(NULL,NULL,$_GET['ver'], $wst);
				if( !ria_mysql_num_rows($articles) )
					print '<tr><td>'._('Aucun article').'</td></tr>';
				else{
					$counter = 1; $count = ria_mysql_num_rows($articles);
					while( $r = ria_mysql_fetch_array($articles) ){
						print '	<tr id="line-' . $r['id'] . '" class="ria-row-orderable">

									<td class="td-art-name" headers="art-name"><a href="edit.php?ver='.$ver['id'].'&amp;art='.$r['id'].'&amp;wst='.$wst.'">'._('Article').' '.$counter.' - '.htmlspecialchars($r['name']).'</a></td>

								</tr>';
						$counter++;
					}
				}
			?>
		</tbody>
		<tfoot <?php if( trim($ver['publish-date']) ){ ?> class="none" <?php }?>>
			<tr>
				<td>
					<?php if( trim($ver['publish-date']) ){ ?>
						&nbsp;
					<?php }else{ ?>
						<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return confirmCgvArticleDelList()" />
						<div class="float-right">
							<label for="artname"><?php print _('Ajouter un article :'); ?></label> <input type="text" name="artname" id="artname" maxlength="75" /> <input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
						</div>
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file index.php
	 *	Cette page permet la gestion des types de relations entre les produits.
	 */

	require_once('prd/relations.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL');

	// Bouton Ajouter
	if( isset($_POST['add-type']) ){
		header('location: /admin/config/catalogue/relations/edit.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete-type'], $_POST['type_id']) ){
		foreach( $_POST['type_id'] as $type ){
			if(!prd_relations_types_del($type,true)){
				$_SESSION['prd_relation_error'] = _('Une erreur est survenue lors de la suppression du type de relation.');
				break;
			}
		}

		if( !isset($error) ){
			$_SESSION['prd_relation_success'] = _('Le type de relations a bien été supprimée.');
			header('Location: /admin/config/catalogue/relations/index.php');
			exit;
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Catalogue'), '/admin/config/catalogue/index.php' )
		->push( _('Types de relations') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Types de relations').' - '. _('Catalogue') . ' - ' ._('Configuration') . ' - ' . _('Relations'));
	require_once('admin/skin/header.inc.php');

	$listeTypeRelation = prd_relations_types_get();
	$count = ria_mysql_num_rows( $listeTypeRelation );
?>
	<h2><?php echo _('Types de relations'); ?> (<?php print ria_number_format($count, 0, ',', ' ' ) ?>)</h2>
<?php
	if (isset($_SESSION['prd_relation_success'])) {
		print '<div class="success">'.nl2br($_SESSION['prd_relation_success']).'</div>';
		unset($_SESSION['prd_relation_success']);
	}
	if (isset($_SESSION['prd_relation_success'])) {
		print '<div class="error">'.nl2br($_SESSION['prd_relation_error']).'</div>';
		unset($_SESSION['prd_relation_success']);
	}
?>
	<form action="/admin/config/catalogue/relations/index.php" method="post">
		<table id="table-type-relations" class="checklist">
			<thead>
				<tr>
					<th id="rel-type-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="rel-type-nom"><?php echo _("Nom"); ?></th>
					<th id="rel-type-nom-pluriel"><?php echo _("Nom pluriel"); ?></th>
					<th id="rel-type-nb-produit" class="align-right"><?php echo _("Nombre de produits"); ?></th>
				</tr>
			</thead>
			<tbody><?php
				while( $type = ria_mysql_fetch_assoc($listeTypeRelation) ){
					$nb_relation = prd_relation_type_count($type['id']);
					if (!is_numeric($nb_relation)) {
						$nb_relation = 0;
					}

					?><tr>
						<td headers="rel-type-sel">
							<input type="checkbox" class="checkbox" name="type_id[]" value="<?php print $type['id'] ; ?>" />
						</td>
						<td headers="rel-type-nom"><a href="/admin/config/catalogue/relations/edit.php?type=<?php print $type['id']; ?>"><?php print htmlspecialchars( $type['name'] ); ?></a></td>
						<td headers="rel-type-nom-pluriel"><a href="/admin/config/catalogue/relations/edit.php?type=<?php print $type['id']; ?>"><?php print htmlspecialchars( $type['name_plural'] ); ?></a></td>
						<td headers="rel-type-nb-produit" class="align-right"><?php print ria_number_format($nb_relation); ?></td>
					</tr><?php
				}
			?></tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<?php if( $listeTypeRelation && ria_mysql_num_rows($listeTypeRelation) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_DELETE') ){ ?>
						<input type="submit" onclick="return confirm('<?php echo _('Vous êtes sur le point de supprimer un ou plusieurs types de relations.\nCette opération est irréversible et ne pourra pas être annulée.\nÊtes vous sûr de vouloir continuer ?'); ?>')" class="btn-del" name="delete-type" value="<?php echo _("Supprimer"); ?>" />
						<?php } ?>
					</td>
					<td colspan="2">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_AJOUT') ){ ?>
						<input type="submit" name="add-type" value="<?php echo _("Ajouter"); ?>" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php

require_once('admin/skin/footer.inc.php');
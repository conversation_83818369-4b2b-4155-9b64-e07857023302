<?php

	/**	\file edit.php
	 *	Cette page sert de fiche aux types de relations. Elle permet leur création, modification et suppression.
	 */

	require_once('delivery.inc.php');
	$is_popup = isset($_GET['popup']) && $_GET['popup'];

	// Vérifie que l'utilisateur à bien accès à cette page
	if( isset($_GET['type']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_UPDATE');
	}else{ // !isset($_GET['type'])
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_AJOUT');
	}

	unset($_SESSION['prd_relation_error']);
	unset($_SESSION['prd_relation_success']);

	// Bouton Enregistrer
	if( isset($_POST['save-type']) ){
		if( !is_string($_POST['name']) || trim($_POST['name']) =='' ){
			$_SESSION['prd_relation_error'] = _('Un nom doit être renseigné.');
		}
		if( !is_string($_POST['namePluriel']) || trim($_POST['namePluriel']) =='' ){
			$_SESSION['prd_relation_error'] = _('Un nom au pluriel doit être renseigné.');
		}
		$name = $_POST['name'];
		$namePluriel = $_POST['namePluriel'];
		if(!isset($_SESSION['prd_relation_error'] )){
			if( isset($_GET['type']) ){
				if( !prd_relations_types_update($_GET['type'], $name, $namePluriel ) ){
					$_SESSION['prd_relation_error'] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
				}else{
					$_SESSION['prd_relation_success'] = _('Le type de relations a bien été enregistré.');
				}
				if(!$is_popup){
					header('location: /admin/config/catalogue/relations/index.php');
					exit;
				}
			}

			if( !prd_relations_types_add($name, $namePluriel) ){
				$_SESSION['prd_relation_error'] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
			}else{
				$_SESSION['prd_relation_success'] = _('Le type de relations a bien été enregistré.');
			}
			if(!$is_popup){
				header('location: /admin/config/catalogue/relations/index.php');
				exit;
			}

		}
	}

	// Bouton Supprimer
	if( isset($_POST['del-type'], $_GET['type']) ){
		if(!prd_relations_types_del($_GET['type'])){
			$_SESSION['prd_relation_error'] = _('Une erreur est survenue lors de la suppression du type de relation.');
			break;
		}else{
			$_SESSION['prd_relation_success'] = _('Le type de relations a bien été supprimé.');
		}
		if(!$is_popup){
			header('location: /admin/config/catalogue/relations/index.php');
			exit;
		}
	}

	// Bouton Annuler
	if(isset($_POST['cancel-type']) ){
		header('location: /admin/config/catalogue/relations/index.php');
		exit;
	}

	// Chargement
	if( isset($_POST['name']) || isset($_POST['namePluriel'])){
		$data = $_POST;
	}elseif( isset($_GET['type']) ){
		$res = prd_relations_types_get($_GET['type']);
		if( !$res ){
			header('location:/admin/config/catalogue/relations/index.php');
			exit;
		}

		$data = ria_mysql_fetch_assoc($res);
	}else{
		$data = array(
			'name' => '',
			'desc' => ''
		);
	}

	// Défini le titre de la page
	$page_title = trim($data['name']) ? _('Relation de type').' '.$data['name'] : _('Nouveau type de relation');
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Types de relations').' - '._('Catalogue').' - '._('Configuration') );

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Catalogue'), '/admin/config/catalogue/index.php' )
		->push( _('Types de relations'), '/admin/config/catalogue/relations/index.php' )
		->push( $page_title );

	if($is_popup){
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
	}
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>
	<?php
		if( isset($_SESSION['prd_relation_error'])){
	?>
			<div class="notice error">
			<p><?php print nl2br($_SESSION['prd_relation_error']) ; ?></p>
			</div>
	<?php
		}
	?>
	<?php
		if( isset($_SESSION['prd_relation_success'])){
	?>
			<div class="notice success">
			<p><?php print nl2br($_SESSION['prd_relation_success']) ; ?></p>
			</div>
	<?php
		}
	?>
	<form action="/admin/config/catalogue/relations/edit.php?<?php echo (isset($_GET['type']) ? 'type='.$_GET['type'] : '')?><?php echo (isset($_GET['type'])?"&":'') .'popup=' ;?><?php echo ($is_popup)?'1':'0'; ?>" method="post">
		<table id="table-config-type-relation">
			<tbody>
				<tr>
					<td class="tdleft"><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input type="text" id="name" name="name" value="<?php echo (isset($data['name']) ? htmlspecialchars($data['name']) : ''); ?>"></td>
				</tr>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom au pluriel :'); ?></label></td>
					<td><input type="text" id="namePluriel" name="namePluriel" value="<?php echo (isset($data['name_plural']) ? htmlspecialchars($data['name_plural']) : ''); ?>"></td>
				</tr>
				<?php if( isset($data['id']) && $data['id']>0 ){ ?>
				<tr>
					<td><?php print _('Nombre de produits :'); ?></td>
					<td>
						<?php
							$nb_relation = prd_relation_type_count( $data['id'] );
							if( !is_numeric($nb_relation) ){
								$nb_relation = 0;
							}
							print ria_number_format($nb_relation);
						?>
					</td>
				</tr>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr>
					<td>
						<?php if(isset($_GET['type']) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_DELETE') ){
							?><input type="submit" name="del-type" value="<?php echo _("Supprimer"); ?>" class="float-left" />
						<?php } ?>
					</td>
					<td>
						<input type="submit" name="save-type" value="<?php echo (isset($_GET['type']) ? _('Enregistrer'): _('Ajouter')); ?>" />
						<input type="submit" name="cancel-type" <?php echo $is_popup ? 'onclick="hidePopup()"' : ''; ?> value="Annuler" />
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
<?php

	/**	\file index.php
	 *
	 * 	Cette page sert de page d'accueil à la section Configuration > Catalogue > Stock
	 *
	 */

	require_once('products.inc.php');
	require_once('websites.inc.php');
	require_once('prd.stocks.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CATALOG_STOCK');

	unset($error);

    $website = wst_websites_get();

	// Détermine l'identifiant du site
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all')
			$_SESSION['websitepicker'] = '0';
		else
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
	}
	//die();
	$wst_id = isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] ? $_SESSION['websitepicker'] : $config['wst_id'];
	
	$config['stock_change_signe']	= isset($config['stock_change_signe']) 	? $config['stock_change_signe'] 	: '+';
	$config['stock_change_value']	= isset($config['stock_change_value']) 	? $config['stock_change_value'] 	: 0;

	// cfg_variables_load($config, array('prd_list_length', 'prd_search_length', 'prd_new_days'));
	// 
	$rovr = cfg_overrides_get( $wst_id, array(), array('stock_change_signe', 'stock_change_value') );
	if( $rovr ){
		while( $ovr = ria_mysql_fetch_array($rovr) )
			$config[ $ovr['code'] ] = $ovr['value'];
	}
	
	// Sauvegarde la configuration des listes de résultats
	if( isset($_POST['save']) ){
		
		// Vérification des données saisies dans le formulaire
		if( !isset($_POST['signe_stock'],$_POST['value_stock']) ){
			$error = _("Un ou plusieurs paramètres de configuration sont manquants ou invalides.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		} elseif( !is_numeric($_POST['value_stock']) ){
			$error = _("La valeur saisie pour la valeur est incorrecte. \nCette valeur doit être numérique et comprise entre 1 et 999.");
		} else{
			// Si les données correspondent aux exigences
			if( cfg_overrides_set_value('stock_change_signe',$_POST['signe_stock'], $wst_id)
				&& cfg_overrides_set_value('stock_change_value', $_POST['value_stock'], $wst_id)
			){
				header('Location: index.php');
				exit;
			}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
		}
	}

	// Remet la configuration par défault
	if( isset($_POST['default']) ){
		if( cfg_stock_value_config_reset_defaults($wst_id) ){
			header('Location: index.php');
			exit;
		}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
	}
	


	define('ADMIN_PAGE_TITLE', _('Gestion des valeurs de stock') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Stock"); ?></h2>

	<?php
		// si le nombre de site du locataire est supérieur a un on affiche le filtre pour trier par site
		print view_websites_selector( $wst_id, false, '', false );

		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<p><?php echo _("Les paramètres ci-dessous vous permettent de personnaliser la gestion des stocks."); ?></p>
	<form action="index.php" method="post" onsubmit="return prdConfig()">
		<dl>
			<dt><?php echo _("Valeur du stock disponible"); ?></dt>
			<dd><?php echo _("Vous avez la possibilité d'ajouter ou de retirer automatiquement une quantité au stock déjà disponible de vos produits"); ?>.</dd>
			<dd><label for="length"><?php echo _("Quelle quantité automatique souhaitez-vous appliquer ?"); ?></label>
			<select name="signe_stock" id="signe_stock">
<?php
			$checked = (isset($config['stock_change_signe']) && $config['stock_change_signe'] == "+")? 'selected' : "";
?>
			<option value="+" <?php print $checked; ?>>+</option>
<?php
			$checked = (isset($config['stock_change_signe']) && $config['stock_change_signe'] == "-")? 'selected' : "";
?>
			<option value="-" <?php print $checked; ?>>-</option>
			</select>
			<input type="number" name="value_stock" id="value_stock" style="width: 4%;" value="<?php print isset($config['stock_change_value']) && $config['stock_change_value']>0 ? $config['stock_change_value'] : 0; ?>" />
			</dd>
		</dl>
		<div class="ria-admin-ui-actions">
			<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
			<input type="submit" name="default" value="<?php echo _("Par défaut"); ?>" />
		</div>
	</form>

<script>
	$(document).ready(function() {
		if( typeof $('#riawebsitepicker') != 'undefined' && $('#riawebsitepicker').length ){
			$('#riawebsitepicker .selectorview').click(function(){
				if($('#riawebsitepicker .selector').css('display')=='none'){
					$('#riawebsitepicker .selector').show();
				}else
					$('#riawebsitepicker .selector').hide();
			});

			$('#riawebsitepicker .selector a').click(function(){
				$('#riawebsitepicker .selectorview .left .view').html($(this).html());
				$('#riawebsitepicker .selector').hide();
				var url ='/admin/config/catalogue/stock/index.php?wst=' + $(this).attr('name').replace('w-', '');
				window.location.replace(url);
			});
		}
	});
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
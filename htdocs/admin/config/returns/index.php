<?php
	require_once('ord.returns.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_RETURN');
	
	unset($error, $success);
	
	// Enregistrement
	if (isset($_POST['submit'])) {
		if (! isset($_POST['returns-allowed'], $_POST['returns-delay'])) $error = 'Certaines données n\'ont pas été transmises.';
		elseif (! in_array($_POST['returns-allowed'], array(0, 1))) $error = 'Veuillez indiquer si vous acceptez les retours.';
		elseif (! ($_POST['returns-delay'] == '' || preg_match('#^[0-9]+$#', $_POST['returns-delay']))) $error = 'Le nombre de jours n\'est pas valide.';
		else {
			$cat = array();
			$modes = array();
			foreach ($_POST as $key => $value) {
				if (preg_match('#^returns-cat-([0-9]+)$#', $key, $match)) $cat[] = $match[1];
				if (preg_match('#^returns-mode-([0-9]+)$#', $key, $match)) $modes[] = $match[1];
			}
			// catégories
			if (! ord_returns_categories_set($cat)) $error = _('Une erreur s\'est produite lors de l\'enregistrement des catégories.');
			// modes
			elseif (! ord_returns_modes_allowed_set($modes)) $error = _('Une erreur s\'est produite lors de l\'enregistrement des modes.');
			else {
				// config
				if ($_POST['returns-delay'] != '') $_POST['returns-delay'] = max(ORD_RETURNS_LEGAL_DELAY, $_POST['returns-delay']);
				
				$rwst = wst_websites_get();
				$website = $rwst && ria_mysql_num_rows($rwst)==1 ? $config['wst_id'] : 0;
				
				$t = true;
				if ($t && $t = cfg_overrides_set_value('returns_allowed', $_POST['returns-allowed'], $website)) $config['returns_allowed'] = $_POST['returns-allowed'];
				if ($t && $t = cfg_overrides_set_value('returns_delay', $_POST['returns-delay'], $website)) $config['returns_delay'] = $_POST['returns-delay'];
				if (! $t) $error = _('Une erreur s\'est produite lors de l\'enregistrement.');
				else $success = _('Les modifications ont été enregistrées avec succès.');
			}
		}
	}
	
	// Ajouter raison
	if (isset($_POST['reason-submit'])) {
		if (! isset($_POST['reason-name'])) $error = _('Certaines données n\'ont pas été transmises.');
		elseif (! trim($_POST['reason-name'])) $error = _('Veuillez indiquez une raison.');
		elseif (! ord_returns_reason_add($_POST['reason-name'])) $error = _('Une erreur s\'est produite lors de l\'ajout.');
		else $success = _('La raison a été ajoutée avec succès.');
	}
	
	// Supprimer raison
	if (isset($_GET['submit-delete'])) {
		$del = array();
		foreach ($_GET as $key => $value) if (preg_match('#^reason-([0-9]+)$#', $key, $match)) $del[] = $match[1];
		if (count($del) == 0) $error = _('Vous devez sélectionner au moins une raison.');
		elseif (! ord_returns_reason_del($del)) $error = _('Une erreur s\'est produite lors de la suppression.');
		else {
			header('location: index.php');
			exit;
		}
	}
	
	// Déplacement
	if (isset($_GET['reason-source'], $_GET['reason-target'])) {
		if(! ord_returns_reason_permute_pos($_GET['reason-source'], $_GET['reason-target']) ){
			$error = _('Une erreur s\'est produite lors du déplacement.');
		}else{
			header('location: index.php');
			exit;
		}
	}
	
	$delay_legal = ORD_RETURNS_LEGAL_DELAY;

	define('ADMIN_PAGE_TITLE', _('Gestion des retours') . ' - '. _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<div id="content-config-returns">
	<?php
		if (isset($error)) print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		if (isset($success)) print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
	?>

	<form action="index.php" method="post">
	<h2><?php echo _("Gestion des retours"); ?></h2>
	<p>
		<?php echo _("Les paramètres ci-dessous vous permettent d’autoriser les retours produit et de définir sous quels critères vous acceptez que vos produits soient retournés."); ?>
	</p>

	<h3><?php echo _("Acceptez-vous les retours produit ?"); ?></h3>
	<input id="returns-allowed-1" type="radio" name="returns-allowed" value="1" <?php if ($config['returns_allowed']) print 'checked="checked"'; ?>/> <label class="marr30" for="returns-allowed-1"><?php echo _("Oui"); ?></label>
	<input id="returns-allowed-0" type="radio" name="returns-allowed" value="0" <?php if (! $config['returns_allowed']) print 'checked="checked"'; ?>/> <label for="returns-allowed-0"><?php echo _("Non"); ?></label>

	<h3><?php echo _("Catégories de produits"); ?></h3>
	<p>
		<?php echo _("Au sein du catalogue certaines catégories de produits ne se prêtent pas aux retours.<br/> Vous avez la possibilité de déterminer pour quelles catégories vous acceptez que vos articles soient retournés."); ?>
	</p>
	<div id="returns-categories">
	<?php
		$tree = array(0 => array('id' => 0, 'name' => 'Toutes les catégories', 'children' => array()));
		$query = prd_categories_get_all();
		while ($dat = ria_mysql_fetch_assoc($query)) {
			$nod =& $tree[$dat['id']];
			if (! $nod) $nod = array('children' => array());
			$nod['id'] = $dat['id'];
			$nod['name'] = $dat['name'];
			
			if (! $dat['parent_id']) $dat['parent_id'] = 0;
			$parent =& $tree[$dat['parent_id']];
			if (! $parent) $parent = array('children' => array());
			$parent['children'][$dat['id']] =& $tree[$dat['id']];
		}
		unset($nod, $parent);

		$cache_rules = ord_returns_category_allow_returns_cache();
		
		function view_admin_cfg_returns_display_tree($nod, $cache_rules) {
			$r = '';
			$r .= '<li>';
				$r .= '<input id="returns-cat-'.$nod['id'].'" name="returns-cat-'.$nod['id'].'" type="checkbox" '.(ord_returns_category_allow_returns($nod['id'], $cache_rules) ? 'checked="checked"' : '').'/>';
				$r .= '<label for="returns-cat-'.$nod['id'].'">'.$nod['name'].'</label>';
				$children = $nod['children'];
				if (count($children) > 0) {
					ksort($children);
					$r .= '<ul id="returns-children-'.$nod['id'].'">';
						foreach ($children as $child) $r .= view_admin_cfg_returns_display_tree($child, $cache_rules);
					$r .= '</ul>';
				}
			$r .= '</li>';
			return $r;
		}
		print '<ul>'.view_admin_cfg_returns_display_tree($tree[0], $cache_rules).'</ul>';
	?>
	</div>
	<div class="notice"><?php echo _("Les articles peuvent être retournés seulement s'ils appartiennent à au moins une hiérarchie complète de catégories acceptant les retours."); ?></div>
	
	<h3><?php echo _("Délai d’autorisation des retours"); ?></h3>

	<?php
		if( isset($config['returns_delay']) ){
			print str_replace('#param[form]#', '<input class="rgt" name="returns-delay" type="text" value="'.$config['returns_delay'].'" size="4"/>', _('Le consommateur dispose d’un délai de '.ORD_RETURNS_LEGAL_DELAY.' jours après la livraison de ses produits pour changer d’avis sur son achat selon le délai légal.<br/>Vous avez la possibilité d’offrir à vos clients un délai plus long pour qu’ils retournent leurs articles : vous acceptez les retours jusqu’à #param[form]# jours après la date de livraison.<br/>Le délai légal de '.ORD_RETURNS_LEGAL_DELAY.' jours sera pris par défaut si aucune valeur n’est saisie ou si la valeur saisie est inférieure au délai légal.'));
		}
	?>

	<h3><?php echo _("Modes de retour"); ?></h3>
	<p>
		<?php echo _("Certains clients préfèrent avoir le choix entre un remboursement ou un avoir. Sélectionnez les modes de retour autorisés sur le site."); ?>
	</p>
	<?php
		$rmodes = ord_returns_modes_get();
		while ($dat = ria_mysql_fetch_assoc($rmodes)) print '<div><input id="mode_'.$dat['id'].'" name="returns-mode-'.$dat['id'].'" type="checkbox"'.(ord_returns_is_mode_allowed($dat['id']) ? ' checked="checked"' : '').'/> <label for="mode_'.$dat['id'].'">'.$dat['name'].'</label></div>';
	?>
	
	<div class="wraper-hidden">
		<input id="submit" name="submit" type="submit" value="<?php echo _("Enregistrer"); ?>"/>
	</div>
	
	</form>
	
	<h3><?php echo _("Raisons des retours"); ?></h3>
	<p>
		<?php echo _("Il est important de connaître la raison pour laquelle le client souhaite retourner son produit.<br/>Vous avez la possibilité de créer vos propres raisons afin qu’elles soient adaptées aux problématiques de votre boutique."); ?>
	</p>
	<form id="form-reasons" action="index.php" method="post">
	<table id="table-reasons" class="checklist">
		<thead>
			<tr>
				<th><input id="reasons-check-all" type="checkbox" class="checkbox"/></th>
				<th id="reason-name"><?php echo _("Intitulé"); ?></th>
				<th id="cat-publish"><?php echo _("Déplacer"); ?></th>
			</tr>
		</thead>
		<tbody>
		<?php
			$reasons = ord_returns_reasons_get();
			if( !ria_mysql_num_rows($query) ){
				print '<td colspan="3">' . _("Aucun motif de retour") . '</td>';
			}else{
				while( $reason = ria_mysql_fetch_assoc($reasons) ){
					print '
						<tr id="line-' . $reason['id'] . '" class="ria-row-orderable">
							<td><input name="reason-'.$reason['id'].'" type="checkbox"/></td>
							<td>'.htmlspecialchars($reason['name']).'</td>
							<td class="align-center ria-cell-move">
								<div class="ria-row-catchable" title="'._('Déplacer').'"></div>
							</td>
						</tr>
					';
				}
			}
		?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3">
					<div class="flol">
						<!-- input id="reasons-delete" type="button" value="Supprimer"/ -->
					</div>
					<div class="flor">
						<label for="reason"><?php echo _("Ajouter une raison :"); ?></label>
						<input type="text" name="reason-name" id="reason" maxlength="75"/> <input name="reason-submit" type="submit" value="<?php echo _("Ajouter"); ?>"/>
					</div>
					<div class="clr"></div>
				</td>
			</tr>
		</tfoot>
	</table>
	</form>

	<p class="mart20">
		<input name="submit" type="submit" value="<?php echo _("Enregistrer"); ?>" onclick="$('#submit').click()"/>
	</p>
</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>

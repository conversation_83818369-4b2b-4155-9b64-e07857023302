<?php

	/**	\file index.php
	 * 
	 * 	Cette page permet la configuration des cartes cadeaux
	 * 
	 */

	require_once('cfg.variables.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_GIFT');
	
	$c_gift = array(
		'actived' 		=> isset($config['gifts_actived']) ? $config['gifts_actived'] : false,
		'apply_on'		=> isset($config['gifts_apply_on']) ? $config['gifts_apply_on'] : 'order',
		'days_valid'	=> isset($config['gifts_days_valid']) ? $config['gifts_days_valid'] : false,
		'into_franco'	=> isset($config['gifts_into_franco']) ? $config['gifts_into_franco'] : false,
		'into_promo'	=> isset($config['gifts_into_promo']) ? $config['gifts_into_promo'] : false,
		'min_amount'	=> isset($config['gifts_min_amount']) ? $config['gifts_min_amount'] : 1,
		'products'		=> isset($config['gifts_products']) ? $config['gifts_products'] : array(),
		'include_pmt'	=> isset($config['gifts_include_pmt']) ? $config['gifts_include_pmt'] : false
	);

	if( isset($_POST) ){
		if ($config['tnt_id'] == 13) {
			$_POST['min_amount'] = 1;
		}

		$c_gift = array(
			'actived' 		=> isset($_POST['actived']) && $_POST['actived'] ? true : $c_gift['actived'],
			'apply_on'		=> isset($_POST['apply_on']) ? $_POST['apply_on'] : $c_gift['apply_on'],
			'days_valid'	=> isset($_POST['days_valid']) ? $_POST['days_valid'] : $c_gift['days_valid'],
			'into_franco'	=> isset($_POST['into_franco']) && $_POST['into_franco'] ? true : $c_gift['into_franco'],
			'into_promo'	=> isset($_POST['into_promo']) && $_POST['into_promo'] ? true : $c_gift['into_promo'],
			'min_amount'	=> isset($_POST['min_amount']) && $_POST['min_amount'] ? true : $c_gift['min_amount'],
			'products'		=> isset($_POST['products']) ? explode(';', $_POST['products']) : $c_gift['products'],
			'include_pmt'	=> isset($_POST['include_pmt']) ? explode(';', $_POST['include_pmt']) : $c_gift['include_pmt']
		);
	}
	
	if( isset($_POST['save']) ){
		
		// Contrôle des paramètres
		if( !isset($_POST['actived'], $_POST['apply_on'], $_POST['days_valid'], $_POST['into_franco'], $_POST['into_promo'], $_POST['min_amount'], $_POST['products'], $_POST['include_pmt']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( !is_numeric($_POST['days_valid']) || $_POST['days_valid']<0 ){
			$error = _("La valeur saisie pour le nombre de jours de validité d'une carte cadeau est fausse. \nVeuillez saisir un nombre supérieur ou égal à zéro (zéro étant la valeur pour n'appliquer aucune limite).");
		}elseif( !is_numeric($_POST['min_amount']) || $_POST['min_amount']<1 ){
			$error = _("Le montant minimal des cartes cadeaux doit être un numérique supérieur à un.");
		}elseif( $_POST['actived'] && trim($_POST['products'])=='' ){
			$error = _("Afin d'activer les cartes cadeaux, vous devez préciser au minimum un produit \"carte cadeau\" sachant que le montant variable ne nécessite qu'un seul produit."); 
		}else{
			
			// Contrôle les produits donnés en paramètres
			$_POST['products'] = explode( ';', $_POST['products'] );
			
			foreach( $_POST['products'] as $key=>$ref ){
				if( !prd_products_exists_ref($ref, false) ){
					unset( $_POST['products'][$key] );
				}
			}
			
			if( $_POST['actived'] && !sizeof($_POST['products']) ){
				$error = _("Aucun des produits fournis n'existe.");
			}
		}
		
		// Si aucune erreur, on enregistre les variables
		if( !isset($error) ){
			if( 
				!cfg_overrides_set_value( 'gifts_actived', $_POST['actived'] ? 1 : 0, $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_apply_on', $_POST['apply_on'], $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_days_valid', $_POST['days_valid'], $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_into_franco', $_POST['into_franco'] ? 1 : 0, $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_min_amount', $_POST['min_amount'], $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_into_promo', $_POST['into_promo'] ? 1 : 0, $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_products', implode(', ', $_POST['products']), $config['wst_id'])
				|| !cfg_overrides_set_value( 'gifts_include_pmt', $_POST['include_pmt'] ? 1 : 0, $config['wst_id'])
			){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la configuration. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/gifts/index.php');
			$_SESSION['save-config-gifts'] = true;
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Cartes cadeaux') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _('Cartes cadeaux'); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}
	
	if( isset($_SESSION['save-config-gifts']) ){
		print '<div class="success">' . _("L'enregistrement de la configuration des cartes cadeaux s\'est correctement déroulé.") . '</div>';
		unset( $_SESSION['save-config-gifts'] );
	}
?>

<form action="/admin/config/gifts/index.php" method="post">
	<dl>
		<dd>
			<p><?php echo _("Depuis cette interface, vous pouvez configurer la gestion des cartes cadeaux. L'activation de cette option entraînera des restrictions dans le processus de commande de votre site Web, dans le cas où le client a commandé une ou plusieurs cartes cadeaux :"); ?></p>
			<ul>
				<li><?php echo _("Les moyens de paiements différés (chèque, virement...) ne seront plus disponibles"); ?></li>
				<li><?php echo _("Si aucun autre produit n'est présent dans la commande, l'étape \"Adresse de livraison\" sera ignorée"); ?></li>
			</ul>
		</dd>
		
		<dd>
			<label for="actived-yes"><?php echo _('Activer les cartes cadeaux :'); ?></label>
			<input type="radio" name="actived" id="actived-yes" value="1" <?php print $c_gift['actived'] ? 'checked="checked"' : ''; ?> />
			<label for="actived-yes"><?php echo _("Oui"); ?></label>
			<input type="radio" name="actived" id="actived-no" value="0" <?php print !$c_gift['actived'] ? 'checked="checked"' : ''; ?> />
			<label for="actived-no"><?php echo _("Non"); ?></label>
		</dd>
	</dl>
	
	<dl>
		<dt><?php echo _("Quels sont les produits \"cartes cadeaux\" ?"); ?></dt>
		<dd><?php echo _("Vous devez définir les produits qui sont identifiés comme étant des cartes cadeaux, leur prix sera alors utilisé comme montant de la carte cadeau."); ?> <?php if( $config['tnt_id']!=13){ ?><?php echo _("Si le produit n'a pas de prix, il sera alors considéré comme ayant un montant variable et l'internaute aura alors la possibilité de choisir lui-même le montant de la carte cadeau."); ?><?php } ?></dd>
		<dd>
			<input type="hidden" name="products" id="products" value="<?php print implode( ';', $c_gift['products'] ); ?>" />
			<table id="list-prd" class="checklist">
				<caption><?php echo _("Liste des produits"); ?></caption>
				<thead>
					<tr>
						<th id="check-del">
							<input type="checkbox" class="checkall" name="delall" onclick="checkAllClick(this)" />
						</th>
						<th id="prd-title"><?php echo _("Désignation"); ?></th>
						<th id="prd-amount" class="align-right"><?php echo _("Montant"); ?></th>
					</tr>
				</thead>
				<tbody><?php
					if( is_array($c_gift['products']) && sizeof($c_gift['products']) ){
						$rprd = prd_products_get_simple( 0, $c_gift['products'], false, 0, false, false, true );
						if( !$rprd || !ria_mysql_num_rows($rprd) ){
							print '
								<tr class="no-prd">
									<td colspan="3">' . _("Aucun produit n'est identifié comme étant une carte cadeau") . '</td>
								</tr>
							';
						}else{
							while( $prd = ria_mysql_fetch_array($rprd) ){
								print '
									<tr>
										<td class="td-check-del" headers="check-del">
											<input type="checkbox" name="del[]" id="del'.$prd['ref'].'" value="'.$prd['ref'].'" />
										</td>
										<td headers="prd-title">';
								if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_GIFT_EDIT') ){
									print  	view_prd_is_sync( $prd ).' <a href="/admin/catalog/product.php?cat=0&amp;prd='.$prd['id'].'">'.htmlspecialchars( $prd['name'] ).'</a>';
								}else{
									print  	view_prd_is_sync( $prd ).' '.htmlspecialchars( $prd['name'] );									
								}
								print ' </td>
										<td headers="prd-amount" class="align-right">
											'.( $prd['price_ttc']>0 && !in_array($prd['ref'], $config['gifts_prd_variable']) ? number_format( $prd['price_ttc'], 2, ',', ' ' ).' €' : 'Variable' ).'
										</td>
									</tr>
								';
							}
						}
					}else{
						print '
							<tr class="no-prd">
								<td colspan="3">' . _("Aucun produit n'est identifié comme étant une carte cadeau") . '</td>
							</tr>
						';
					}
				?></tbody>
				<tfoot>
					<tr>
						<td colspan="3">
							<div class="float-left">
								<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_GIFT_DEL') ){ ?>
								<input type="button" name="del" id="del" value="<?php echo _("Supprimer"); ?>" />
								<?php } ?>
							</div>
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_GIFT_ADD') ){ ?>
							<input type="button" name="add-prd" id="add-prd" value="<?php echo _("Ajouter un produit"); ?>" title="<?php echo _("Identifier le produit comme étant une carte cadeau"); ?>" />
							<?php } ?>
						</td>
					</tr>
				</tfoot>
			</table>
		</dd>
		
		<dd>
			<label><?php echo _("Si vous souhaitez limiter le montant minimal des cartes cadeaux ayant un montant variable, vous devez renseigner une valeur :"); ?></label>
			<input size="3" maxlength="3" type="text" name="min_amount" id="min_amount" value="<?php print $c_gift['min_amount']; ?>" />
			<?php echo _("€. Par défaut, le montant des cartes cadeaux doit simplement être supérieur ou égal à 1."); ?>
		</dd>
		
	</dl>
	
	<dl>
		<dd>
			<label for="into_franco-yes"><?php echo _("Faut-il inclure les cartes cadeaux lors du calcul des frais de port ?"); ?></label>
			<input type="radio" value="1" id="into_franco-yes" name="into_franco" <?php print $c_gift['into_franco'] ? 'checked="checked"' : ''; ?> />
			<label for="into_franco-yes"><?php echo _("Oui"); ?></label>
			<input type="radio" value="0" id="into_franco-no" name="into_franco" <?php print !$c_gift['into_franco'] ? 'checked="checked"' : ''; ?> />
			<label for="into_franco-no"><?php echo _("Non"); ?></label>
		</dd>
		<dd>
			<label for="into_promo-yes"><?php echo _("Souhaitez-vous appliquer les remises des codes promotions sur les cartes cadeaux ?"); ?></label>
			<input type="radio" value="1" id="into_promo-yes" name="into_promo" <?php print $c_gift['into_promo'] ? 'checked="checked"' : ''; ?> />
			<label for="into_promo-yes"><?php echo _("Oui"); ?></label>
			<input type="radio" value="0" id="into_promo-no" name="into_promo" <?php print !$c_gift['into_promo'] ? 'checked="checked"' : ''; ?> />
			<label for="into_promo-no"><?php echo _("Non"); ?></label>
		</dd>
	</dl>
	
	<dl>
		<dt><?php echo _("Génération du code promotion"); ?></dt>
		<dd>
			<label for="days_valid"><?php echo _("Quelle est la durée de validité d'une carte cadeau ?"); ?></label>
			<label for="days_valid"><?php echo _("Si vous souhaitez limiter dans le temps les cartes cadeaux, vous devez préciser une durée en jours :"); ?></label>
			<input size="4" maxlength="4" type="text" name="days_valid" id="days_valid" value="<?php print is_numeric($c_gift['days_valid']) && $c_gift['days_valid']>0 ? $c_gift['days_valid'] : 0; ?>" />
			<?php echo _("jour(s). Par défaut, les cartes cadeaux ne sont pas limitées dans le temps."); ?>
		</dd>
		<dd>
			<label for="apply_on"><?php echo _("Quelle est la zone d'application de la réduction ?"); ?></label>
			<select name="apply_on" id="apply_on">
				<option value="order" <?php print $c_gift['apply_on']=='order' ? 'selected="selected"' : ''; ?>><?php echo _("Sur toute la commande"); ?></option>
				<option value="min-prd" <?php print $c_gift['apply_on']=='min-prd' ? 'selected="selected"' : ''; ?>><?php echo _("Sur le produit le moins cher"); ?></option>
				<option value="max-prd" <?php print $c_gift['apply_on']=='max-prd' ? 'selected="selected"' : ''; ?>><?php echo _("Sur le produit le plus cher"); ?></option>
				<option value="min-line" <?php print $c_gift['apply_on']=='min-line' ? 'selected="selected"' : ''; ?>><?php echo _("Sur la ligne de commande la moins chère"); ?></option>
				<option value="max-line" <?php print $c_gift['apply_on']=='max-line' ? 'selected="selected"' : ''; ?>><?php echo _("Sur la ligne de commande la plus chère"); ?></option>
			</select>
		</dd>
		<dd>
			<label for="include_pmt-yes"><?php echo _("Inclure les articles en promotion :"); ?></label>
			<input type="radio" name="include_pmt" id="include_pmt-yes" value="1" <?php print $c_gift['include_pmt'] ? 'checked="checked"' : ''; ?> />
			<label for="include_pmt-yes"><?php echo _("Oui"); ?></label>
			<input type="radio" name="include_pmt" id="include_pmt-no" value="0" <?php print !$c_gift['include_pmt'] ? 'checked="checked"' : ''; ?> />
			<label for="include_pmt-no"><?php echo _("Non"); ?></label>
		</dd>
	</dl>
	
	<div class="ria-admin-ui-actions">
		<input type="submit" value="<?php echo _("Enregistrer"); ?>" name="save" />
	</div>
	
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
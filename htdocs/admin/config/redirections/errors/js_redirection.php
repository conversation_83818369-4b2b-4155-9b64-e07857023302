<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_404');

	require_once('search.inc.php');
	require_once('errors.inc.php');
	
	// Lance la recherche seulement si le champ texte est rempli
	$page = 1;
	if( isset($_GET['qr'], $_GET['type']) && trim($_GET['qr']!='') ){
		$tab = null;
		if( $_GET['type']!='-1' ){
			$tab = array( $_GET['type'] );
		}
		
		// Recherche
		$results = search3( 1, $_GET['qr'], 1, 0, true, false, 4, $tab, false, isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng'] );
		
		$results_count = $results ? ria_mysql_num_rows($results) : 0;
		$pages = ceil($results_count / $config['prd_list_length']);
		
		if( isset($_GET['page']) ){
			if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
				$page = 1;
			}elseif( $_GET['page']>$pages ){
				$page = $pages;
			}else{
				$page = $_GET['page'];
			}
		}
	}

    define('ADMIN_PAGE_TITLE', _('Rechercher un contenu de substitution') . ' - ' . _('Erreurs 404') . ' - ' . _('Redirections') . ' - ' . _('Configuration'));
    define('ADMIN_ID_BODY', 'popup-content');
    define('ADMIN_HEAD_POPUP', true);
    require_once('admin/skin/header.inc.php');
?>
	<form id="sh-redirection" action="js_redirection.php" method="get">
		<input type="hidden" name="id" id="id" value="<?php print $_GET['id']; ?>" />
        <input type="hidden" name="wst" id="wst" value="<?php print $_GET['wst']; ?>" />
        <input type="hidden" name="lng" id="lng" value="<?php print $_GET['lng']; ?>" />
        <input type="hidden" name="page" id="page" value="<?php print $page; ?>"  />
        
		<label for="search"><?php print _('Recherche :'); ?></label>
		<input type="text" name="qr" id="qr" value="<?php print isset($_GET['qr']) ? $_GET['qr'] : ''; ?>" />
		<select name="type" id="type"><option value="-1"><?php echo _("Tous types"); ?></option><?php
			$exclude = array( 'usr', 'ord', 'other-cnt', 'cgv' );
			$rtype = search_content_types_get();
			if( $rtype ){
				while( $type = ria_mysql_fetch_array($rtype) ){
					if( !in_array($type['code'], $exclude) ){
						print '<option value="'.$type['code'].'"'.( isset($_GET['type']) && $_GET['type']==$type['code'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
					}
				}
			}
		?></select>		
		<input type="submit" name="search" id="search" value="<?php echo _("Chercher"); ?>" />
	</form>
    
    <div id="lst-res-search">
        <?php 
        if( isset($results) && $results!=false ){
            if($page>1){
                ria_mysql_data_seek( $results, ($page-1)*$config['prd_list_length'] );
            } 
        ?>
            <table id="result-redirection" class="checklist">
                <caption>Liste des résultats (<?php print ria_mysql_num_rows($results); ?>)</caption>
                <thead>
                    <tr>
                        <th id="info"></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                        // url du site selon la langue
                        $url_site = $config['site_url'];
                        
                        if( isset($_GET['lng']) && $_GET['lng']!=$config['i18n_lng'] ){
                            $rurl = wst_websites_languages_get( $_GET['wst'], $_GET['lng'] );
                            if( $rurl && ria_mysql_num_rows($rurl) ){ 
                                $url_site = ria_mysql_result( $rurl, 0, 'url' );
							}
                        }
    
                        $count = 0;
                        while( ($r = ria_mysql_fetch_array($results)) && $count<$config['prd_list_length'] ){
                            print '<tr>';
                            
                            $info = array( 
                                'img_id' => $r['img_id'],
                                'name' => $r['name'],
                                'desc' => $r['desc'],
                                'url' => $r['url']
                            );
                            
                            // traduction des contenus
                            if( isset($_GET['lng']) && $_GET['lng']!=$config['i18n_lng'] ){
                                switch( $r['type_code']	){
                                    case 'prd' :
                                        $tsk = fld_translates_get( CLS_PRODUCT, $r['tag'], $_GET['lng'], $r, array(_FLD_PRD_NAME=>'name', _FLD_PRD_TITLE=>'name', _FLD_PRD_DESC=>'desc', _FLD_PRD_URL=>'url'), true ); break;
                                    case 'prd-cat' ;
                                        $tsk = fld_translates_get( CLS_CATEGORY, $r['tag'], $_GET['lng'], $r, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'name', _FLD_CAT_URL=>'desc', _FLD_PRD_URL=>'url'), true ); break;
                                    case 'faq-cat' :
                                        $tsk = fld_translates_get( CLS_FAQ_CAT, $r['tag'], $_GET['lng'], $r, array(_FLD_FAQ_CAT_NAME=>'name', _FLD_FAQ_CAT_DESC=>'desc', _FLD_FAQ_CAT_URL=>'url'), true ); break;
                                    case 'faq-qst' :
                                        $tsk = fld_translates_get( CLS_FAQ_QST, $r['tag'], $_GET['lng'], $r, array(_FLD_FAQ_QST_NAME=>'name', _FLD_FAQ_QST_DESC=>'desc', _FLD_FAQ_QST_URL=>'url'), true ); break;
                                    case 'news' :
                                        $tsk = fld_translates_get( CLS_NEWS, $r['tag'], $_GET['lng'], $r, array(_FLD_NEWS_NAME=>'name', _FLD_NEWS_DESC=>'desc', _FLD_NEWS_URL=>'url'), true ); break;
                                    case 'dlv-str' :
                                        $tsk = fld_translates_get( CLS_STORE, $r['tag'], $_GET['lng'], $r, array(_FLD_STR_NAME=>'name', _FLD_STR_DESC=>'desc', _FLD_STR_URL=>'url'), true ); break;
                                    case 'cms' :
                                        $tsk = fld_translates_get( CLS_CMS, $r['tag'], $_GET['lng'], $r, array(_FLD_CMS_NAME=>'name', _FLD_CMS_DESC=>'desc', _FLD_CMS_URL=>'url'), true ); break;
                                    case 'brd' :
                                        $tsk = fld_translates_get( CLS_BRAND, $r['tag'], $_GET['lng'], $r, array(_FLD_BRD_NAME=>'name', _FLD_BRD_TITLE=>'name', _FLD_BRD_DESC=>'desc', _FLD_BRD_URL=>'url'), true ); break;
                                    default :
                                        break;
                                }
                                
                                $info['name'] = isset($tsk['name']) ? $tsk['name'] : $info['name'];
                                $info['desc'] = isset($tsk['desc']) ? $tsk['desc'] : $info['desc'];
                                $info['url'] = isset($tsk['url']) ? $tsk['url'] : $info['url'];
                            }
                            
                            // Affichage de l'image
                            $img_size = $config['img_sizes']['small'];
							$img = ($info['img_id'] ? $info['img_id'] : $config['default_image']).'.'.$img_size['format'];
                            print '
                            <td class="redirection img" headers="img">
                                <a href="'.$url_site.$info['url'].'" target="_bank"><img src="/images/products/'.$img_size['dir'].'/'.$img.'" alt="'.htmlspecialchars($info['name']).'" title="'.htmlspecialchars($info['name']).'" /></a>
                            ';
                            
                            // Affichage du nom
                            print '
                                <div class="content-substitut">
                                    <a href="'.$url_site.$info['url'].'" target="_bank">'.htmlspecialchars($info['name']).'</a>
                                </div>
                            ';
                            
                            // Affichage de la description
                            $desc = $info['desc'];
                            if( strlen($desc)>105 ){
                                $desc = substr( $desc, 0, 102 ).'...';
							}
                            print '<div class="content-substitut">'.htmlspecialchars( $desc ).'</div>';
                            
                            // Redirection
                            print '
                                <div class="content-substitut">'.htmlspecialchars( $r['type_name'] ).' &raquo; <a href="'.$url_site.$info['url'].'" target="_bank">'.$info['url'].'</a></div>
                                <div>
                                    <input title="' . _("Cliquez dessus pour choisir ce contenu comme substitution.") . '" type="button" name="choose"  onclick="select_redirection(\''.$info['url'].'\')" value="Choisir"/>
                                </div>
                            </td>
                        </tr>';
                            
                            $count++;
                        }
                    ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td class="align-right">
                        <?php
                            $links = array();
                            if( $page>1 ){
                                $links[] = '<a href="js_redirection.php?qr='.urlencode($_GET['qr']).'&amp;page='.($page-1).'&amp;type='.$_GET['type'].'&amp;id='.$_GET['id'].'&amp;wst='.$_GET['wst'].'&amp;lng='.$_GET['lng'].'">&laquo; ' . _("Page précédente") . '</a>';
							}
                            for( $i=$page-5; $i<$page+5; $i++ )
                                if( $i>=1 && $i<=$pages ){
                                    if( $i==$page )
                                        $links[] = '<b>'.$i.'</b>';
                                    else
                                        $links[] = '<a href="js_redirection.php?qr='.urlencode($_GET['qr']).'&amp;page='.$i.'&amp;type='.$_GET['type'].'&amp;id='.$_GET['id'].'&amp;wst='.$_GET['wst'].'&amp;lng='.$_GET['lng'].'">'.$i.'</a>';
                                }
                            if( $page<$pages ){
                                $links[] = '<a href="js_redirection.php?qr='.urlencode($_GET['qr']).'&amp;page='.($page+1).'&amp;type='.$_GET['type'].'&amp;id='.$_GET['id'].'&amp;wst='.$_GET['wst'].'&amp;lng='.$_GET['lng'].'">' . _("Page suivante") . ' &raquo;</a>';
                            }
                            print implode(' | ',$links);
                        ?>
                        </td>
                    </tr>
                </tfoot>
            </table>
        <?php } ?>
        <script><!--
            function select_redirection(url){
                parent.select_redirection(url, <?php print $_GET['id'] ?>);
            }
        --></script>
<?php
    require_once('admin/skin/footer.inc.php');
?>
<?php

	// Cette fonctionnalité est réservée à l'interne
	if (!$config['USER_RIASTUDIO']) {
		exit;
	}
	
	$_GET['date1'] 			= isset($_GET['date1']) ? $_GET['date1']: false;
	$_GET['date2'] 			= isset($_GET['date2']) ? $_GET['date2']: false;
	$_GET['filter'] 		= isset($_GET['filter']) ? $_GET['filter']: '';
	$_GET['filter-type'] 	= isset($_GET['filter-type']) ? $_GET['filter-type']: '';
	$_GET['wst_id'] 		= isset($_GET['wst']) ? $_GET['wst']: 0;
	$_GET['no-redirection'] = isset($_GET['no-redirection']) ? $_GET['no-redirection']: false;
	$_GET['resolved'] 		= isset($_GET['resolved']) ? $_GET['resolved']: false;
	
	$r_error = err_errors_get( $_GET['wst'], '', $_GET['no-redirection'], $_GET['filter'], $_GET['filter-type'], $_GET['resolved'], $_GET['date1'], $_GET['date2'] );
	if (!$r_error) {
		exit;
	}

	$ar_website_urls = array();
	$r_all_urls = wst_websites_languages_get();
	if (!$r_all_urls || !ria_mysql_num_rows($r_all_urls)) {
		exit;
	}

	while ($all_url = ria_mysql_fetch_assoc($r_all_urls)) {
		$ar_website_urls[$all_url['wst'].'-'.$all_url['lng_code']] = $all_url['url'];
	}

	while ($error = ria_mysql_fetch_assoc($r_error)) {
		if (!array_key_exists($error['wst'] . '-' . $error['lng'], $ar_website_urls)) {
			continue;
		}

		$url_website = $ar_website_urls[$error['wst'] . '-' . $error['lng']];
		
		$check = Sitemap::checkUrl($error['url'], true, $url_website, false, false, false);
		if (isset($check['http_code']) && $check['http_code'] == '200') {
			err_errors_del($error['wst'], $error['lng'], $error['url']);
		}
	}

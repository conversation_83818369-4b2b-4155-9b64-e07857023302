<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_PERMANENT');

    $_POST['type'] = isset($_POST['type']) && in_array($_POST['type'], array('SELECTOR', 'SELECT')) ? $_POST['type'] : 'SELECTOR';
																											
	$html = '';
    if( isset($_POST['wst']) && $_POST['type']=='SELECTOR' ){
		
		// langue active pour un site
		$languages = array();
		$rl = wst_websites_languages_get( $_POST['wst'] );
		if( $rl ){
			while( $l = ria_mysql_fetch_array($rl) )
				$languages[ $l['lng_code'] ] = $l['name'];
		}
		
		if( sizeof($languages)>1 ){
			
			$_POST['lng'] = isset($_POST['lng']) ? $_POST['lng'] : $config['i18n_lng'];
			
			$html .= '	<div id="rialanguagepicker">';
            $html .= '		<div class="selectorview">';
            $html .= '	    	<div class="left">';
            $html .= '	        	<span class="function_name">' . _('Gestion des langues') . '</span><br>';
            $html .= '	            <span class="view">'.( $_POST['lng']!=='all' && trim($_POST['lng']) ? i18n_languages_get_name( $_POST['lng'] ) : _('Toutes les langues') ).'</span>';
			$html .= '			</div>';
            $html .= '	        <a name="btn" class="btn">';
            $html .= '	        	<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif" />';
            $html .= '	        </a>';
            $html .= '	        <div class="clear"></div>';
			$html .= '		</div>';
            $html .= '	    <div class="selector">';
			$html .= '			<a name="p-all">' . _("Toutes les langues") . '</a>';
			foreach( $languages as $code=>$name )
				$html .= '		<a name="p-'.$code.'">'.$name.'</a>';
			$html .= '		</div>';
			$html .= '	</div>';
		}
		
    } elseif( $_POST['type']=='SELECT' ){
		
		$_POST['wst'] = isset($_POST['wst']) ? $_POST['wst'] : 0;
		
		// langue active pour un site
		$languages = array();
		$rl = wst_websites_languages_get( $_POST['wst'] );
		if( $rl ){
			while( $l = ria_mysql_fetch_array($rl) )
				$languages[ $l['lng_code'] ] = $l['name'];
		}
		
		if( sizeof($languages)>1 ){
			$html .= ' <option value="-1">' . _("Veuillez sélectionner une langue") . '</option>';
			foreach( $languages as $code=>$name )
				$html .= ' <option value="'.$code.'">'.$name.'</option>';
		}
	}
	
	print $html;


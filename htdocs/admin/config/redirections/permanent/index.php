<?php
	require_once('rewrite.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_PERMANENT');

	// nombre de résultats par page
	$for_page = 30;

	// paramètres
	$wst = isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst']>=0 ? $_GET['wst'] : 0;
	$lng = isset($_GET['lng']) && in_array($_GET['lng'], array('', 'all') ) ? null : (isset($_GET['lng']) && (wst_websites_languages_exists($wst, $_GET['lng']) || $wst<=0) ? $_GET['lng'] : null);
	$filter = isset($_GET['filter']) ? $_GET['filter'] : '';

	// récupère les sites + création d'un tableau de tous les sites
	$website = wst_websites_get();
	$ar_website = array();
	if( $website && ria_mysql_num_rows($website) ){
		while( $web = ria_mysql_fetch_array($website) )
			$ar_website[ $web['id'] ] = $web['name'];
	}

	// récupère les redirections 301 publique
	$rred = rew_rewritemap_get( '', '', 301, $lng, $wst );

	// Calcule le nombre de pages
	$nb_red = $rred ? ria_mysql_num_rows( $rred ) : 0;
	$pages = ceil($nb_red / $for_page);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages )
			$page = $_GET['page'];
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-4;
	if( $pmin<1 )
		$pmin = 1;
	$pmax = $page+4;
	if( $pmax>$pages )
		$pmax = $pages;

	// affiche ou non la colonne 'Site'
	$show_site = is_array($ar_website) && sizeof($ar_website)>1 && $wst==0;
	$colspan = $show_site ? 3 : 2;

	// langue active pour un site
	$languages = array(); $ar_url_site = array();
	$rl = wst_websites_languages_get( $wst );
	if( $rl ){
		while( $l = ria_mysql_fetch_array($rl) ){
			$languages[ $l['lng_code'] ] = $l['name'];
			$ar_url_site[ $l['wst'].'-'.$l['lng_code'] ] = $l['url'];
		}
	}

	// affcihe ou non la colonne 'Version'
	$show_lng = (!is_array($languages) || sizeof($languages)>1) && $lng===null;
	$colspan = $show_site && $show_lng ? 4 : ( $show_site || $show_lng ? 3 : 2 );

	// enregistrement d'une nouvelle url de redirections
	// Array ( [save-new-red] => Enregistrer [new-source] => [new-dest] => [websites] => Array ( [0] => 22 [1] => 14 ) [langues] => Array ( [0] => en [1] => es [2] => fr [3] => ru ) )
	if( isset($_POST['save-new-red']) ){
		if( !isset($_POST['new-source']) || !trim($_POST['new-source']) ){
			$error = 1;
		} elseif( !isset($_POST['new-dest']) || !trim($_POST['new-dest']) ){
			$error = 1;
		} elseif( $_POST['new-source']==$_POST['new-dest'] ){
			$error = 2;
		}

		if( isset($error) ){
			switch( $error ){
				case 1 :
					$error = _('Une ou plusieurs informations obligatoires sont manquantes.');
					break;
				case 2 :
					$error = '';
					break;
				default :
					$error = _("Une erreur inattendue s'est produite lors l'enregistrement de la redirection. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					break;
			}
		} else {
			header('Location: /admin/config/redirections/permanent/index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Redirections permanentes') . ' - '. _('Redirections') . ' - '. _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Redirections permanentes"); ?> (<?php print ria_number_format($nb_red); ?>)</h2>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php?wst=<?php print $wst; ?>&amp;page=<?php print $page; ?>" method="post">
		<input type="hidden" name="wst" id="wst_id" value="<?php print $wst; ?>"  />
		<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />
		<input type="hidden" name="filter" id="filter" value="<?php print $filter; ?>" />
		<input type="hidden" name="page" id="page" value="<?php print $page; ?>" />

		<?php
			if( sizeof($ar_website)>1 || sizeof($languages)>1 )
				print '	<div class="stats-menu">';

				// si le nombre de site du locataire est supérieur a un on affiche le filtre pour trier par site
				print view_websites_selector( $wst, true, '', true );

			if( sizeof($languages)>1 ){
		?>
			<div id="rialanguagepicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Gestion des langues"); ?></span><br />
						<span class="view"><?php print $lng!==null && trim($lng) ? i18n_languages_get_name( $lng ) : _('Toutes les langues'); ?></span>
					</div>
					<a name="btn" class="btn">
						<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="p-all"><?php echo _("Toutes les langues"); ?></a><?php

					foreach( $languages as $code=>$name )
						print '<a name="p-'.$code.'">'.$name.'</a>';
				?></div>
			</div>
		<?php }
			if( sizeof($ar_website)>1 || sizeof($languages)>1 )
				print ' <div class="clear"></div></div>';
		?>


		<table id="lst-redirections" class="checklist">
			<caption>
					<label for="filter"><?php echo _("Recherche d'une URL contenant :"); ?></label>
					<input onkeypress="if( event.keyCode==13 ) return false;" type="text" name="filter-url" id="filter-url" value="<?php print isset($_POST['filter-url']) ? $_POST['filter-url'] : ''; ?>" />
			</caption>
			<thead>
				<tr>
					<th id="url_source"><?php echo _("URL source"); ?></th>
					<th id="url_dest"><?php echo _("Destination"); ?></th>
					<?php
						print $show_site ? '<th id="url_site" class="col100px">' . _("Site") . '</th>' : '';
						print $show_lng ? '<th id="url_lng">' . _("Version") . '</th>' : '';
					?>
				</tr>
			</thead>
			<tbody><?php
				if( !$rred || !ria_mysql_num_rows($rred) ){
					print '<tr><td colspan="2">Aucune redirection permanente n\'est en place pour l\'instant.</td>';
				} else {
					$count = 0;

					if( $page>1 )
						ria_mysql_data_seek( $rred, ($page-1)*$for_page );

					while( $red = ria_mysql_fetch_array($rred) ){
						if( ($count++)>($for_page-1) )
							break;

						$url_site = isset($ar_url_site[ $red['wst_id'].'-'.$red['lng_code'] ]) ? $ar_url_site[ $red['wst_id'].'-'.$red['lng_code'] ] : $config['site_url'];

						print '	<tr>
									<td headers="url_source"><a target="_blank" href="'.$url_site.$red['extern'].'">'.$red['extern'].'</a></td>
									<td headers="url_dest"><a target="_blank" href="'.$url_site.$red['intern'].'">'.$red['intern'].'</a></td>
									'.( $show_site ? '<td headers="url_site">'.( $red['wst_id']==0 ? 'Tous les sites' : $ar_website[ $red['wst_id'] ] ).'</td>' : '').
							'		'.( $show_lng ? '<td headers="url_lng">'.$languages[ $red['lng_code'] ].'</td>' : '').
							'	</tr>';
					}
				}
			?></tbody>
			<tfoot>
				<tr>
					<td class="left"><!-- input type="button" name="add-red" id="add-red" value="Ajouter" / --></td>
					<td id="pagination" colspan="<?php print $colspan-1; ?>"><?php
						if( $pages>1 ){
							if( $page>1 )
								print '<a onclick="return reload_redirections('.($page-1).')" href="index.php?page='.($page-1).'&amp;wst='.$wst.'&amp;lng='.$lng.'">&laquo; ' . _("Page précédente") . '</a> | ';
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page )
									print '<b>'.$page.'</b>';
								else
									print '<a onclick="return reload_redirections('.$i.')" href="index.php?page='.$i.'&amp;wst='.$wst.'&amp;lng='.$lng.'">'.$i.'</a>';
								if( $i<$pmax )
									print ' | ';
							}
							if( $page<$pages )
								print ' | <a onclick="return reload_redirections('.($page+1).')" href="index.php?page='.($page+1).'&amp;wst='.$wst.'&amp;lng='.$lng.'">' . _("Page suivante") . ' &raquo;</a>';
						} else {
							print '<b>1</b>';
						}
					?></td>
				</tr>
			</tfoot>
		</table>
	</form>
	<div id="popup_ria" class="maxipopup"><div class="popup_ria_drag"></div><div class="content"></div></div>
<?php
	require_once('admin/skin/footer.inc.php');
?>
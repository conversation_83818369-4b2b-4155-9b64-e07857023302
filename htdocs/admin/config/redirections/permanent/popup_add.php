<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_PERMANENT');

	$website = wst_websites_get();
	$ar_website = array();
	if( $website && ria_mysql_num_rows($website) ){
		while( $web = ria_mysql_fetch_array($website) )
			$ar_website[ $web['id'] ] = $web['name'];
	}
	
	// langue active pour un site
	$languages = array();
	$rl = wst_websites_languages_get( isset($_POST['wst']) ? $_POST['wst'] : 0 );
	if( $rl ){
		while( $l = ria_mysql_fetch_array($rl) )
			$languages[ $l['lng_code'] ] = $l['name'];
	}
	
	if( isset($_POST['save-new-red']) ){
		
		$_POST['lng'] = isset($_POST['lng']) && $_POST['lng']!='-1' ? $_POST['lng'] : '';
		
		if( !isset($_POST['new-source']) || !trim($_POST['new-source']) ){
			$error = 1;
		} elseif( !isset($_POST['new-dest']) || !trim($_POST['new-dest']) ){
			$error = 1;
		} elseif( $_POST['new-source']==$_POST['new-dest'] ){
			$error = 2;
		} elseif( (!is_array($ar_website) || sizeof($ar_website)>1) && (!isset($_POST['wst']) || $_POST['wst']<=0) ){
			$error = 3;
		} elseif( (!is_array($languages) || sizeof($languages)>1) && (!isset($_POST['lng']) || !trim($_POST['lng'])) ){
			$error = 4;
		} else {
			$res = rew_rewritemap_redirection_add( $_POST['new-source'], $_POST['new-dest'], $_POST['wst'], isset($_POST['lng']) && trim($_POST['lng']) ? $_POST['lng'] : '' );
			if( (int) $res>0 ){
				header('Location: popup_add.php?add=1');
				exit;
			} else
				$error = $res;
		}
		
		if( isset($error) ){
			switch( (int) $error ){
				case -1 :
					$error = "Un redirection est déjà en place pour l'url suivante : \n \"".$_POST['new-source']."\"";
					break;
				case 1 :
					$error = "Une ou plusieurs informations obligatoires sont manquantes.";
					break;
				case 2 :
					$error = "L'url source et l'url de destination ne peuvent pas être identiques";
					break;
				case 3 :
					$error = "Veuillez sélectionner le site pour lequel la redirection doit être mise en place.";
					break;
				case 4 :
					$error = "Veuillez sélectionner la version du site pour laquelle la redirection doit être mise en place.";
					break;
				default :
					$error = "Une erreur inattendue s'est produite lors de l'enregistrement de la redirection. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.";
					break;
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Redirections permanentes') . ' - ' . _('Redirections') . ' - ' . _('Configuration'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-add-redirect');
	require_once('admin/skin/header.inc.php');
?>
		<?php
            if( isset($error) ){
                print '<div class="error">'.nl2br(htmlspecialchars(_($error))).'</div>';
            } elseif( isset($_GET['add']) ){
				print '<div class="error-success">' . _("La redirection a bien été sauvegardée.") . '</div>';
			}
        ?>

        <form action="popup_add.php" method="post">
        	<?php 
				if( is_array($ar_website) && sizeof($ar_website)==1 ){
					$_POST['wst'] = $config['wst_id'];
					print '<input type="hidden" name="wst" id="wst" value="'.$config['wst_id'].'" />';
				}
			?>
            <table>
                <caption><?php echo _("Nouvelle redirection permanente") ; ?></caption>
                <col width="120px" /><col width="*" />
                <thead>
                    <tr>
                        <th colspan="2"><?php echo _("Informations") ; ?></th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <td colspan="2" align="right"><input type="submit" name="save-new-red" id="save-new-red" value="<?php echo _("Enregistrer") ; ?>" /></td>
                    </tr>
                </tfoot>
                <tbody>
                    <tr>
                        <th><label for="new-source"><span class="mandatory">*</span> <?php echo _("Url source :") ; ?></label></th>
                        <td><input type="text" name="new-source" id="new-source" value="<?php print isset($_POST['new-source']) ? $_POST['new-source'] : ''; ?>" /></td>
                    </tr>
                    <tr>
                        <th><label for="new-dest"><span class="mandatory">*</span> <?php echo _("Destination :") ; ?></label></th>
                        <td><input type="text" name="new-dest" id="new-dest" value="<?php print isset($_POST['new-dest']) ? $_POST['new-dest'] : ''; ?>" /></td>
                    </tr>
                    <?php
                        if( is_array($ar_website) && sizeof($ar_website)>1 ){
                            print '	<tr>';
                            print '		<th><label for="wst"><span class="mandatory">*</span> '._('Site :').'</label></th>';
                            print '		<td>';
                            print '			<select name="wst" id="wst">';
                            print '				<option value="-1">' . _("Veuillez sélectionner un site") .'</option>';
                            foreach( $ar_website as $id=>$name ){
                                print '			<option value="'.$id.'"'.( isset($_POST['wst']) && $_POST['wst']==$id ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $name ).'</option>';
                            }
                            print '			</select>';
                            print '		</td>';
                            print '	</tr>';
                        }
                        
						$display_lng = isset($_POST['wst']) && $_POST['wst']>0 && is_array($languages) && sizeof($languages)>1;
						print '	<tr id="langue" style="display: '.( $display_lng ? 'table-row' : 'none' ).'">';
						print '		<th><label for="lng"><span class="mandatory">*</span> '._('Versions :').'</label></th>';
						print '		<td>';
						print '			<select name="lng" id="lng">';
						print '				<option value="-1">' . _("Veuillez sélectionner une langue") . '</option>';
                        if( is_array($languages) && sizeof($languages)>1 ){
                            foreach( $languages as $code=>$name ){
                                print '		<option value="'.$code.'"'.( isset($_POST['lng']) && $_POST['lng']==$code ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $name ).'</option>';
                            }
                        }
						print '			</select>';
						print '		</td>';
						print '	</tr>';
                    ?>
                </tbody>
            </table>
        </form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
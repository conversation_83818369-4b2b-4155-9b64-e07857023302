<?php
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_UNIT');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['unit']) ){
		foreach( $_POST['unit'] as $z )
			if( !fld_units_del($z) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression d'une des unités.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Unités de mesure') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Charge la liste des unités de mesure
	$units = fld_units_get();
	$count = ria_mysql_num_rows($units);
?>
	<h2><?php echo _('Unités de mesure'); ?> (<?php print ria_number_format($count) ?>)</h2>

	<?php
		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	?>

	<form action="index.php" method="post">
	<table id="table-units" class="checklist">
		<thead>
			<tr>
				<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="name"><?php echo _("Nom de l'unité"); ?></th>
				<th id="symbol" class="align-right"><?php echo _("Symbole"); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( !ria_mysql_num_rows($units) )
					print '<tr><td colspan="3">' . _("Aucune unité de mesure") . '</td></tr>';
				else
					while( $r = ria_mysql_fetch_array($units) ){
						print '<tr>';
						print '<td headers="select"><input type="checkbox" class="checkbox" name="unit[]" value="'.$r['id'].'" /></td>';
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_UNIT_EDIT') ){
							print '<td headers="name"><a href="edit.php?unit='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
						}else{
							print '<td headers="name">'.htmlspecialchars($r['name']).'</td>';
						}
						print '<td headers="symbol" class="align-right">'.$r['symbol'].'</td>';
						print '<tr>';
					}
			?>
		</tbody>
		<tfoot>
			<tr><td colspan="3">
				<?php if( $units && ria_mysql_num_rows($units) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_UNIT_DEL') ){ ?>
				<input type="submit" name="del" class="btn-del" value="<?php echo _("Supprime"); ?>r" title="<?php echo _("Supprimer les unités sélectionnées"); ?>" onclick="return fldUnitConfirmDelList()" />
				<?php }
				if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_UNIT_ADD')) { ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter une unité"); ?>" onclick="return fldUnitAdd()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
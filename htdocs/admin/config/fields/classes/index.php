<?php

/** \file index.php
 * Cette page affiche la liste des classes personnalisées.
 */

require_once('fields.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS');

$error = false;

// Suppression de classes.
if( isset($_POST['del']) ){
	foreach( $_POST['del'] as $id ){
		if( !is_numeric($id) || $id <= 0 ){
			continue;
		}

		if( !fld_classes_del($id) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression des classes. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			break;
		}
	}

	if( !$error ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
}

define('ADMIN_PAGE_TITLE', _('Classes') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));

require_once('admin/skin/header.inc.php');

// Récupère les classes propres au client, retirer les deux derniers paramètres pour aussi récupérer les classes systèmes.
$r_classes = fld_classes_get(0, false, false, false, null, null, true);

$classes_count = ria_mysql_num_rows($r_classes);

?>

<h2><?php print _('Classes'); ?> (<?php print ria_number_format($classes_count) ?>)</h2>
<form action="/admin/config/fields/classes/index.php" method="post">
	<table id="table-config-classes" class="checklist">
		<thead>
			<tr>
				<th id="cls-check">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th id="cls-name"><?php print _('Nom de la classe'); ?></th>
				<th id="cls-nb-fld" class="align-right"><?php print _('Champs'); ?></th>
				<th id="cls-nb-obj" class="align-right"><?php print _('Objets'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php if( !$classes_count ){ ?>
				<tr>
					<td colspan="4"><?php print _('Aucune classe n\'est enregistrée pour le moment.'); ?></td>
				</tr>
			<?php }else{ ?>
				<?php while( $class = ria_mysql_fetch_array($r_classes) ){ ?>
					<?php $objects = fld_classes_get_objects_count($class['id']); ?>
					<tr>
						<td headers="cls-check">
							<input type="checkbox" name="del[]" value="<?php print $class['id']; ?>">
						</td>
						<td headers="cls-name">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_EDIT') ){ ?>
								<a href="/admin/config/fields/classes/edit.php?cls=<?php print $class['id']; ?>"><?php print htmlspecialchars($class['name']); ?></a>
							<?php }else{ ?>
								<?php print htmlspecialchars($class['name']); ?>
							<?php } ?>
						</td>
						<td headers="cls-nb-fld" class="align-right">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_EDIT') ){ ?>
								<a href="/admin/config/fields/fields/index.php?cls=<?php print $class['id']; ?>"><?php print ria_number_format($class['fields']).' champ'.($class['fields'] > 1 ? 's' : ''); ?></a>
							<?php }else{ ?>
								<?php print ria_number_format($class['fields']).' champ'.($class['fields'] > 1 ? 's' : ''); ?>
							<?php } ?>
						</td>
						<td headers="cls-nb-obj" class="align-right">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_EDIT') ){ ?>
								<a href="/admin/config/fields/classes/objects.php?cls=<?php print $class['id']; ?>"><?php print ria_number_format($objects).' objet'.($objects > 1 ? 's' : ''); ?></a>
							<?php }else{ ?>
								<?php print ria_number_format($objects).' objet'.($objects > 1 ? 's' : ''); ?>
							<?php } ?>
						</td>
					</tr>
				<?php } ?>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="4">
					<?php if( ria_mysql_num_rows($r_classes) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_DEL') ){ ?>
						<button type="submit" class="btn-del" title="<?php print _('Supprimer les classes sélectionnées'); ?>" onclick="return fldClassesConfirmDelList();"><?php print _('Supprimer'); ?></button>
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_ADD') ){ ?>
						<a href="/admin/config/fields/classes/edit.php?cls=0" class="button btn-add" title="<?php print _('Ajouter une classe'); ?>"><?php print _('Ajouter'); ?></a>
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');
<?php

	/**	\file popup-select-parent.php
	 *	Cette page est utilisée en popup pour permettre la sélection d'un objet parent.
	 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS');

require_once('fields.inc.php');

if( !isset($_GET['cls']) || !fld_classes_exists($_GET['cls'], true) ){
	$error = _("Une erreur inattendue s'est produite lors du chargement des objects.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
	$fatal_error = true;
}else{
	$cls_id = $_GET['cls'];
	$obj_id = isset($_GET['obj']) && is_numeric($_GET['obj']) && $_GET['obj']>0  ? $_GET['obj'] : -1;

	if( isset($_POST['parent']) ){
		$obj = null;
		if( $obj_id != -1){
			$r_obj = fld_objects_get($obj_id, $cls_id);
			if( $r_obj && ria_mysql_num_rows($r_obj)){
				$obj = ria_mysql_fetch_assoc($r_obj);
			}
		}

		$parent = null;
		$r_parent = fld_objects_get($_POST['parent'], $cls_id);
		if( $r_parent && ria_mysql_num_rows($r_parent)){
			$parent = ria_mysql_fetch_assoc($r_parent);
		}

		if( is_null($obj) || $obj === false ){
			$error = _('Objet non trouvé.');
		}else{
			print '<script>window.parent.addSelectParent( '.json_encode($parent['id']).', '.json_encode($parent['name']).' );</script>';
			exit;
		}
	}
}

define('ADMIN_PAGE_TITLE', _('Sélectionner un objet parent') . ' - ' . _('Classes') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
define('ADMIN_ID_BODY', 'popup-content');
define('ADMIN_HEAD_POPUP', true);

require_once('admin/skin/header.inc.php');

if( isset($error) ){
	print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
	if( isset($fatal_error) ){
		exit;
	}
}
?>

<form action="/admin/config/fields/classes/popup-select-parent.php?cls=<?php print $cls_id.( $obj_id != -1 ? '&amp;obj='.urlencode($obj_id) : '') ?>" method="post">
	<table id="tb-popup-liste-obj-classe" >
		<caption><?php echo _("Liste des objets de la classe"); ?></caption>
		<thead>
			<tr>
				<th id="obj-name"><?php echo _("Désignation"); ?></th>
				<th id="obj-childs"><?php echo _("Objets enfant"); ?></th>
			</tr>
		</thead>
		<tbody><?php
			// Récupère les classes propres au client, retiré les deux derniers paramètres pour aussi récupérer les classes systèmes
			$robj = fld_objects_get( 0, $cls_id, isset($_GET['srcobj']) && is_numeric($_GET['srcobj']) ? $_GET['srcobj'] : 0 );
			$no_child = true;
			if( $robj && ria_mysql_num_rows($robj) ){
				while( $obj = ria_mysql_fetch_array($robj) ){
					if( $obj['id'] == $obj_id ){
						continue;
					}
					
					$no_child = false;
					$childs = fld_objects_get( 0, $cls_id, $obj['id'], null, false, ($obj_id != -1 ? $obj_id : 0) );
					$childs = $childs ? ria_mysql_num_rows( $childs ) : 0;
					
					print '
						<tr>
							<td headers="obj-name">
								<input type="radio" name="parent" id="parent-'.htmlspecialchars($obj['id']).'" value="'.htmlspecialchars($obj['id']).'" />
								<label for="parent-'.$obj['id'].'">'.htmlspecialchars( $obj['name'] ).'</label>
							</td>
							<td headers="obj-childs">
					';
					
					if( $childs>0 ){
						print '
								<a href="/admin/config/fields/classes/popup-select-parent.php?cls='.urlencode($cls_id).'&amp;obj='.urlencode($obj_id).'&amp;srcobj='.urlencode($obj['id']).'">
						';
						
						if( $childs>1 ){
							print _('		Voir les ').$childs._(' objets enfants');
						}else{
							print _('		Voir l\'objet enfant');
						}
						print '
								</a>
						';
					}else{
						print _('Aucun objet enfant');
					}
					
					print '		
							</td>
						</tr>
					';
				}
				
				if( $obj_id==-1 ){
					print '
						<tr>
							<td headers="obj-name" colspan="2">
								<input type="radio" name="parent" id="parent-0" value="0" />
								<label for="parent-0">' . _("Aucun objet parent") . '</label>
							</td>
						</tr>
					';
				}
			}

			if( $no_child ){
				print '
					<tr>
						<td colspan="2">' . _("Aucun objet n'est pour le moment disponible.") . '</td>
					</tr>
				';
			}
		?></tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" title="<?php echo _("Sélectionner cet objet"); ?>" value="<?php echo _("Sélectionner"); ?>" class="btn-add" name="add" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<?php 
require_once('admin/skin/footer.inc.php');

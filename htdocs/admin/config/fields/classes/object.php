<?php

	/**	\file object.php
	 *	Cet écran affiche les propriétés d'une instance de classe personnalisée.
	 *	Les paramètres acceptés sont les suivants :
	 *	- $_GET['cls'] : Obligatoire, identifiant de la classe personnalisée
	 *	- $_GET['obj'] : Facultatif, identifiant de l'instance à afficher
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS');

	require_once('fields.inc.php');
	
	// Le paramètre $_GET['cls'] est obligatoire et doit être valide
	if( !isset($_GET['cls']) || !fld_classes_exists($_GET['cls']) ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
	
	// Charge la classe demandée
	$cls = ria_mysql_fetch_array( fld_classes_get($_GET['cls']) );
	
	// Pour le moment les classes systèmes ne sont pas accessibles
	if( $cls['tenant']!=$config['tnt_id'] ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
	
	$obj = array( 'id'=>0, 'name'=>'', 'parent_id'=>0, 'is_sync'=>false );
	if( isset($_GET['obj']) && is_numeric($_GET['obj']) && $_GET['obj']>0 ){
		$robj = fld_objects_get( $_GET['obj'] );
		if( $robj && ria_mysql_num_rows($robj) ){
			$obj = ria_mysql_fetch_array( $robj );
		}
	}
	
	// Annulation
	if( isset($_POST['cancel']) ){
		header('Location: /admin/config/fields/classes/objects.php?cls='.$_GET['cls']);
		exit;
	}
	
	// Enregistrement
	if( isset($_POST['save']) ){
		if( !isset($_POST['obj-name']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['obj-name'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		}else{
			$parent = isset($_POST['parent-id']) && fld_objects_exists($_POST['parent-id']) ? $_POST['parent-id'] : 0;
			
			if( $obj['id']==0 ){
				$tobj = fld_objects_add( $cls['id'], $_POST['obj-name'], $parent );
				if( !$tobj ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'objet. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}else{
					$obj['id'] = $tobj;
				}
			}else{
				if( $parent == $obj['id'] ){
					$error = _("Veuillez sélectionner un objet parent différent de l'objet courant.");
				}elseif( !fld_objects_update($obj['id'], $_POST['obj-name'], $parent) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour de l'objet.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
		
		if( $obj['id'] && !isset($error) ){
			$fields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $_GET['cls'] );
			if( $fields && ria_mysql_num_rows($fields) ){
				while( $f = ria_mysql_fetch_array($fields) ){
					if( $f['type_id']==FLD_TYPE_IMAGE ){
						if( isset($_FILES['fld'.$f['id']]) && trim($_FILES['fld'.$f['id']]['tmp_name'])!='' ){
							$value = img_images_upload('fld'.$f['id']);
							fld_object_values_set( $obj['id'], $f['id'], $value );
						}
						continue;
					}
					
					if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) ){
						$_POST['fld'.$f['id']] = '';
					}
					
					if( isset($_POST['fld'.$f['id']]) ){
						$value = $_POST['fld'.$f['id']];
						fld_object_values_set( $obj['id'], $f['id'], $value );
					} else {
						fld_object_values_set( $obj['id'], $f['id'], '' );
					}
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/fields/classes/object.php?cls='.$cls['id'].'&obj='.$obj['id']);
			exit;
		}
	}
	
	if( isset($_POST['obj-name']) ){
		$obj['name'] = $_POST['obj-name'];
	}

	define('ADMIN_PAGE_TITLE', ($obj['id']>0 ? htmlspecialchars($obj['name']) : _('Nouvel objet')).' - '._('Objets de la classe ').htmlspecialchars($cls['name']).' - ' . _('Classes') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print view_obj_is_sync($obj);
		if( $obj['id']>0 ){
			print _('Objet').' '.htmlspecialchars( $obj['name'] );
		}else{
			print _('Nouvel objet');
		}
	?></h2>
	
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
	?>
	
	<form action="/admin/config/fields/classes/object.php?cls=<?php print $cls['id']; ?>&amp;obj=<?php print $obj['id']; ?>" method="post" enctype="multipart/form-data">
		<input type="hidden" name="cls_id" id="cls_id" value="<?php print $cls['id']; ?>" />
		<input type="hidden" name="obj_id" id="obj_id" value="<?php print $obj['id']; ?>" />
			
		<table id="fields-classe-object">
			<tbody>
				<tr>
					<th colspan="2"><?php echo _("Général"); ?></th>
				</tr>
				<tr>
					<td>
						<span class="mandatory">*</span>
						<label for="obj-name"><?php echo _('Désignation :'); ?></label>
					</td>
					<td>
						<input type="text" name="obj-name" id="obj-name" value="<?php print $obj['name']; ?>" />
					</td>
				</tr>
				<?php
					// Charge et affiche les champs associés à cet objet
					$fields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array('fld-pos'=>'asc'), false, array(), null, $_GET['cls'] );
					if( $fields && ria_mysql_num_rows($fields) ){
						print '
							<tr>
								<th colspan="2">' . _('Avancés') . '</th>
							</tr>
						';
						while( $f = ria_mysql_fetch_assoc($fields) ){
							$f['obj_value'] = fld_object_values_get( $_GET['obj'], $f['id'] );
							
							if( $f['type_id'] == FLD_TYPE_SELECT_HIERARCHY ){
								$f['obj_value'] = fld_object_values_get( $_GET['obj'], $f['id'], '', true );
							}

							print '
								<tr>
									<td>
										<label for="fld'.$f['id'].'">'.htmlspecialchars( $f['name'] ).' :</label>
										'.( in_array( $f['type_id'], array(5, 6) ) ? '<sub><a href="#" onclick="return fldFieldModifyValues('.$f['id'].')">' . _("Editer la liste") . '</a></sub>' : '' ).'
									</td>
									<td>
										<div id="fields-'.$f['id'].'" style="max-width: 1000px">
											'.fld_fields_edit( $f, '', false ).'
										</div>
									</td>
								</tr>
							';
						}
					}
				?>
				<?php if( $obj['id'] > 0 ){?>
					<tr>
						<th colspan="2"><?php echo _("Relation"); ?></th>
					</tr>
					<tr>
						<td><?php echo _("Rattacher à un autre objet :"); ?></td>
						<td><?php
							$parent = array('id'=>0, 'name'=>'' );
							if( is_numeric($obj['parent_id']) && $obj['parent_id']>0 ){
								$rparent = fld_objects_get( $obj['parent_id'] );
								if( $rparent && ria_mysql_num_rows($rparent) ){
									$parent = ria_mysql_fetch_array( $rparent );
								}
							}
							print '
								<input type="hidden" name="parent-id" id="parent-id" value="'.$parent['id'].'" />
								<input type="text" name="parent-name" id="parent-name" value="'.$parent['name'].'" />
								<input type="button" class="btn-action-small" name="search-obj-parent" id="search-obj-parent" value="' . _("Sélectionner") . '" title="' . _("Choisir l'objet parent") . '" />
							';
						?></td>
					</tr>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" value="<?php echo _("Enregistrer"); ?>" name="save" />
						<input type="submit" value="<?php echo _("Annuler"); ?>" name="cancel" />
						<?php if( $obj['id']>0 ){ ?>
						<input type="submit" onclick="return fldObjectsConfirmDel();" value="<?php echo _("Supprimer"); ?>" name="del" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
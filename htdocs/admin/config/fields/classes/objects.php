<?php

	/**	\file objects.php
	 *	Ce fichier affiche la liste des instances d'une classe personnalisée. Les paramètres acceptés sont les suivants :
	 *	- $_GET['cls'] : Obligatoire, identifiant de la classe personnalisée dont on souhaite afficher les objets
	 *	- $_GET['obj'] : Facultatif, identifiant d'une instance de classe utilisée comme parent, dont on souhaite visualiser les enfants
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS');

	require_once('fields.inc.php');
	
	// Le paramètre $_GET['cls'] est obligatoire et doit être valide
	if( !isset($_GET['cls']) || !fld_classes_exists($_GET['cls'], true) ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
	
	// Charge la classe demandée
	$cls = ria_mysql_fetch_array( fld_classes_get($_GET['cls']) );
	
	// Le paramètre $_GET['obj'] est facultatif, il doit contenir l'identifiant d'un parent dont on souhaite visualiser les enfants
	$obj = isset($_GET['obj']) && is_numeric($_GET['obj']) && $_GET['obj']>0  ? $_GET['obj'] : -1;
	
	// Ajout d'une instance
	if( isset($_POST['add']) ){
		header('Location: /admin/config/fields/classes/object.php?cls='.$cls['id'].'&obj=0');
		exit;
	}
	
	// Suppression d'une instance
	if( isset($_POST['del']) ){
		foreach( $_POST['del'] as $d ){
			if( !is_numeric($d) || $d<=0 ){
				continue;
			}
			
			if( !fld_objects_del($d, true) ){
				$error = _("Une erreur est survenue lors de la suppression des objets. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				break;
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/fields/classes/objects.php?cls='.$cls['id'].'&obj='.$obj);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Objets de la classe ').htmlspecialchars($cls['name']).' - ' . _('Classes') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Objets de la classe"); ?> <?php print htmlspecialchars($cls['name']); ?></h2>
	<form action="/admin/config/fields/classes/objects.php?cls=<?php print $cls['id']; ?>&amp;obj=<?php print $obj; ?>" method="post">
		<table id="fields-classe-objects" class="checklist">
			<thead>
				<tr>
					<th id="obj-check">
						<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
					</th>
					<th id="obj-name"><?php echo _("Désignation"); ?></th>
					<th id="obj-childs"><?php echo _("Objets enfants"); ?></th>
				</tr>
			</thead>
			<tbody><?php
				// Récupère les classes propres au client, retiré les deux derniers paramètres pour aussi récupérer les classes systèmes
				$robj = fld_objects_get( 0, $cls['id'], $obj );
				if( !$robj|| !ria_mysql_num_rows($robj) ){
					print '
						<tr>
							<td colspan="3">' . _("Aucun objet") . '</td>
						</tr>
					';
				}else{
					while( $obj = ria_mysql_fetch_array($robj) ){
						$childs = fld_objects_get( 0, $cls['id'], $obj['id'] );
						$childs = $childs ? ria_mysql_num_rows( $childs ) : 0;
						
						print '
							<tr>
								<td headers="obj-check">
									<input type="checkbox" name="del[]" value="'.$obj['id'].'" />
								</td>
								<td headers="obj-name">
									'.view_obj_is_sync($obj).'
									<a href="/admin/config/fields/classes/object.php?cls='.$cls['id'].'&amp;obj='.$obj['id'].'">'.htmlspecialchars( $obj['name'] ).'</a>
								</td>
								<td headers="obj-childs">
									<a href="/admin/config/fields/classes/objects.php?cls='.$cls['id'].'&amp;obj='.$obj['id'].'">'.$childs.' enfant'.( $childs>1 ? 's' : '' ).'</a>
								</td>
							</tr>
						';
					}
				}
			?></tbody>
			<tfoot>
				<tr>
					<td colspan="3">
						<input type="submit" onclick="return fldObjectsConfirmDelList()" title="<?php echo _("Supprimer les classes sélectionnées"); ?>" value="<?php echo _("Supprimer"); ?>" class="btn-del" name="del" />
						<input type="submit" title="<?php echo _("Ajouter une classe"); ?>" value="<?php echo _("Ajouter"); ?>" class="btn-add" name="add" />
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
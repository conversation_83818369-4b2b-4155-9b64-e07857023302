<?php

	/**	\file edit.php
	 *	Cette page permet la création et la modification d'un modèle de saisie
	 *	Les fonctionnalités suivantes sont disponibles :
	 *	- Création d'un modèle de saisie
	 *	- Ajout de champs libres à un modèle de saisie
	 *	- Suppression de champs libres à un modèle de saisie
	 *	- Modification des propriétés du modèle de saisie
	 */

	require_once('fields.inc.php');

	$_GET['mdl'] = isset($_GET['mdl']) ? $_GET['mdl'] : 0;

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( $_GET['mdl'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_MODEL_EDIT');
	}else{ // $_GET['mdl'] == 0
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_MODEL_ADD');
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	
	// Vérifie la validité de l'identifiant d'unité passé en paramètre
	if( isset($_GET['mdl']) && $_GET['mdl']!=0 ){
		if( !fld_models_exists($_GET['mdl']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	// Suppression
	if( isset($_POST['del']) ){
		if( !fld_models_del($_GET['mdl']) ){
			$count = fld_object_models_get_count(0,$_GET['mdl']);
			if( $count>0 ){
				$error = str_replace("#param[nom_modele]#", fld_models_get_name($_GET['mdl']), str_replace("#param[nb_utilisation]#", $count, _("Le modèle #param[nom_modele]# est utilisé par #param[nb_utilisation]# élément(s).\nVeuillez détacher le modèle de ces éléments avant de le supprimer.")));
			}else{
				$error = _("Une erreur inattendue s'est produite lors de la suppression du modèle de saisie.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}else{
			header('Location: index.php');
			exit;
		}
	}

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order']) ){
		fld_models_order_update( $_GET['mdl'], $_POST['order'] );
		header('Location: edit.php?mdl='.$_GET['mdl']);
		exit;
	}
	
	// Déplacement vers le haut
	if( isset($_GET['up']) ){
		fld_model_fields_move_up( $_GET['mdl'], $_GET['up'] );
		header('Location: edit.php?mdl='.$_GET['mdl']);
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['dw']) ){
		fld_model_fields_move_down( $_GET['mdl'], $_GET['dw'] );
		header('Location: edit.php?mdl='.$_GET['mdl']);
		exit;
	}
		
	// Enregistrement
	if( isset($_POST['save']) || isset($_POST['save-stay']) ){
		if( !isset($_POST['name']) || !trim($_POST['name']) ){
			$error = _("Veuillez indiquer le nom du modèle de saisie.");
		}elseif( !isset($_POST['desc']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}elseif( isset($_GET['mdl']) && $_GET['mdl']==0 ){
			if( !isset($_POST['classtype']) || !is_numeric($_POST['classtype']) || $_POST['classtype']<=0 ){
				$error = _("Le type des données liées au modèle est absent ou invalide.");
			}else{
				// Ajout
				$res = fld_models_add($_POST['name'],$_POST['desc'],$_POST['classtype']);
				if( $res===ERR_NAME_EXISTS ){
					$error = _("Un modèle portant le même nom existe déjà.\nVeuillez en choisir un autre.");
				}elseif( $res===false ){
					$error =_( "Une erreur inattendue s'est produite lors de l'enregistrement du modèle.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}else{
					$_GET['mdl'] = $res;
				}
			}
		}elseif( isset($_GET['mdl']) && $_GET['mdl']>0 ){
			// Modification
			$res = fld_models_update($_GET['mdl'],$_POST['name'],$_POST['desc']);
			if( $res===ERR_NAME_EXISTS ){
				$error = _("Un modèle portant le même nom existe déjà.\nVeuillez en choisir un autre.");
			}elseif( $res===false || !fld_models_set_as_options($_GET['mdl'], (isset($_POST['as_options']) && $_POST['as_options'] == 'Oui')) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du modèle.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}

		if( !isset($error) && !isset($_POST['save-stay']) ){
			header('Location: index.php');
			exit;
		}
	}

	// Suppression de champs
	if( isset($_POST['del-fields']) ){
		if( isset($_POST['fld']) && is_array($_POST['fld']) ){
			foreach( $_POST['fld'] as $f ){
				fld_models_fields_del( $_GET['mdl'], $f );
			}
		}
	}
	
	// Chargement	
	$model = array('id'=>0,'name'=>'','desc'=>'', 'cls_id'=>CLS_PRODUCT, 'as_options' => 0);
	if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
		$model = ria_mysql_fetch_array(fld_models_get($_GET['mdl']));
	}
	if( isset($_POST['name']) ) $model['name'] = $_POST['name'];
	if( isset($_POST['classtype']) ) $model['cls_id'] = $_POST['classtype'];
	if( isset($_POST['desc']) ) $model['desc'] = $_POST['desc'];

	// Défini le titre de la page
	$page_title = $model['name'] ? _('Modèle :').' '.$model['name'] : 'Nouveau modèle de saisie';
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Modèle de saisie') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo htmlspecialchars( $page_title ); ?></h2>

	<?php

		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

	?>

	<form name="edit" id="edit" action="edit.php?mdl=<?php print $model['id'] ?>" method="post" onsubmit="return fldModelValidForm(this)">
		<input type="hidden" name="mdl-id" id="mdl-id" value="<?php print $model['id']; ?>" />
		<table>
			<tbody>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($model['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="classtype"><span class="mandatory">*</span> <?php echo _('Type de données :'); ?></label></td>
					<td>
						<select id="classtype" name="classtype" <?php  print !isset($_GET['mdl']) || $_GET['mdl']==0 ? '' : 'disabled="disabled"'; ?>>
							<?php
							if( $classes = fld_classes_get( 0, false, true, true, null, true ) ){
								while( $cls = ria_mysql_fetch_array($classes) ){
									print '<option value="'.$cls['id'].'" '.( $cls['id']==$model['cls_id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($cls['name']).'</option>';
								}
							}
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td><label for="desc"><?php echo _('Description :'); ?></label></td>
					<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($model['desc']); ?></textarea></td>
				</tr>

				<?php
					// S'il s'agit d'un modèle existant rattaché au produit, on active l'option de déclinaison
					if( $_GET['mdl'] > 0 && $model['cls_id'] == CLS_PRODUCT ){
						print '<tr>'
								.'<td><label for="as-options-y">Utiliser comme options de déclinaison</label></td>'
								.'<td>'
									.' <input type="radio" name="as_options" id="as-options-y" value="Oui" '.( $model['as_options'] ? 'checked="checked"' : '' ).' />'
									.' <label for="as-options-y">Oui</label>'
									.' <input type="radio" name="as_options" id="as-options-n" value="Non" '.( !$model['as_options'] ? 'checked="checked"' : '' ).' />'
									.' <label for="as-options-n">Non</label>'
								.'</td>'
							.'</tr>';
					}
				?>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save-stay" value="<?php echo _("Enregistrer"); ?>" />
					<input type="submit" name="save" id="save" value="<?php echo _("Enregistrer et revenir à la liste"); ?>"/>
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return fldModelCancelEdit()" />
					<?php if( $model['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_MODEL_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return fldModelConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>

		<?php
			// Détermine si les catégories enfant sont triées alphabétiquement ou non
			$ordered = false;
			if( isset($_GET['mdl']) ){
				$ordered = fld_models_order_get( $_GET['mdl'] );
			}
			
			$fields = fld_models_get_fields($model['id']);
		?>
		<table id="fields" class="checklist">
			<caption><?php echo _("Liste des champs").' ('.(ria_mysql_num_rows($fields) ? ria_mysql_num_rows($fields) : '0').')'; ?></caption>
			<thead>
				<tr>
					<th id="fld-select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="fld-name"><?php echo _("Nom du champ"); ?></th>
					<th id="fld-type"><?php echo _("Type"); ?></th>
					<th id="fld-cat"><?php echo _("Catégorie"); ?></th>
					<?php if( $ordered ){ ?><th id="fld-pos"><?php echo _("Déplacer"); ?></th><?php } ?>
				</tr>
			</thead>
			<tbody class="head-second">
				<?php
					if( $fields===false || !ria_mysql_num_rows($fields) ){
						print '<tr><td colspan="'.( $ordered ? 5 : 4 ).'">' . _("Aucun champ personnalisé") . '</td></tr>';
					}else{
						$oldcat = 0; $count = ria_mysql_num_rows($fields);
						while( $r = ria_mysql_fetch_array($fields) ){
							if( $r['cat_id']!=$oldcat ){
								print '<tr><th colspan="'.( $ordered ? '5' : '4' ).'">'.htmlspecialchars($r['cat_name']).'</th></tr>';
								$oldcat = $r['cat_id'];
							}
							print '	<tr id="line-' . $r['id'] . '" class="ria-row-orderable">
										<td headers="fld-select"><input type="checkbox" class="checkbox" name="fld[]" value="'.$r['id'].'" /></td>
										<td headers="fld-name">'.htmlspecialchars($r['name']).'</td>
										<td headers="fld-type">'.htmlspecialchars($r['type_name']).'</td>
										<td headers="fld-cat">'.htmlspecialchars($r['cat_name']).'</td>';
							if( $ordered ){
								print '<td headers="fld-pos" class="align-center ria-cell-move">';
								print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
								print '</td>';
							}
							print '</tr>';
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr><td colspan="<?php print $ordered ? 5 : 4; ?>">
					<?php if( ria_mysql_num_rows($fields)>0 ){ ?>
					<input type="submit" name="del-fields" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les champs sélectionnés de ce modèle"); ?>" <?php if( !$model['id'] ) print 'disabled="disabled"'; ?> />
					<?php } ?>
					<input type="submit" name="add-fields" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un champ personnalisé"); ?>" <?php if( !$model['id'] ) print 'disabled="disabled"'; ?> onclick="return fldModelAddField(<?php print $model['id']; ?>)" />
				</td></tr>
				<?php if( ria_mysql_num_rows($fields)>1 ){ ?>
				<tr><td colspan="<?php print $ordered ? 5 : 4; ?>" class="tfoot-grey align-left">
					<label><?php echo _('Trier ces champs par ordre :'); ?></label>
					<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php print _('Alphabétique'); ?></label>
					<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php print _('Personnalisé'); ?></label>
					<input type="submit" name="orderby" value="<?php echo _("Appliquer"); ?>" />
				</td></tr>
				<?php } ?>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
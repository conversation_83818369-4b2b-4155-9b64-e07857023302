<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	require_once('fields.inc.php');
	
	if( isset($_POST['save']) ){
		foreach( $_POST['val'] as $lng=>$values ){
			foreach( $values as $id=>$name ){
				if( trim($name)!='' && !fld_restricted_values_translates_add($id, $lng, $name) )
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des traductions. \nVeuillez réessayer ou prendre contact avec l'administrateur.");
				elseif( trim($name)=='' && ! fld_restricted_values_translates_del($id, $lng) )
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des traductions. \nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Traduction des valeurs autorisées') . ' - ' . _('Champs personnalisés') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	define('ADMIN_NO_MOBILE_STYLE', true);
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>'; 
	}
?>
		<form action="translate.php?fld=<?php print $_GET['fld']; ?>" method="post">
			<table class="checklist">
				<caption><?php echo _("Traduction des valeurs autorisées"); ?></caption>
				<?php
					foreach( $config['i18n_lng_used'] as $lang )
						print '<col width="75" />';
					
				?>
				<thead>
					<tr>
						<th id="lng-default"><?php print i18n_languages_get_name($config['i18n_lng']); ?></th>
						<?php
							$colspan = 1;
							foreach( $config['i18n_lng_used'] as $lang ){
								if( $lang!=$config['i18n_lng'] ){
									print '<th id="lng-'.$lang.'">'.i18n_languages_get_name($lang).'</th>';
									$colspan++;
								}
							}
						?>
					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="<?php print $colspan; ?>">
							<input type="submit" name="save" id="save" value="<?php echo _("Enregistrer"); ?>" />
						</td>
					</tr>
				</tfoot>
				<tbody><?php
					// Récupère les valeurs de restriction
					$ar_tsk_val = array();
					foreach( $config['i18n_lng_used'] as $lang ){
						$rvalues = fld_restricted_values_get( 0, $_GET['fld'], '', 0, '', $lang );
						if( $rvalues ){
							// Créer un tableau des values traduite
							while( $value = ria_mysql_fetch_array($rvalues) ){
								$ar_tsk_val[ $lang ][ $value['id'] ] = $value['name'];
							}
						}
					}
					$rvalues = fld_restricted_values_get( 0, $_GET['fld'], '', 0, '' );
					if( $rvalues ){
						// Créer un tableau des values traduite
						while( $value = ria_mysql_fetch_array($rvalues) ){
							print '	<tr>';
							print '		<td headers="lng-default">'.$value['name'].'</td>';
							foreach( $config['i18n_lng_used'] as $lang ){
								if( $lang!=$config['i18n_lng'] ){
									$val = isset($ar_tsk_val[$lang][$value['id']]) ? $ar_tsk_val[$lang][$value['id']] : '';
									print '	<td headers="lng-'.$lang.'">';
									print '		<input style="width:100%" type="text" name="val['.$lang.']['.$value['id'].']" id="val-'.$value['id'].'" value="'.$val.'" />';
									print '	</td>';
								}
							}
							print '	</tr>';
						}
					}
				?></tbody>
			</table>
		</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
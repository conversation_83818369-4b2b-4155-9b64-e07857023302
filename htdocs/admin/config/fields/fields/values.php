<?php

	/**	\file values.php
	 *	Permet la gestion de la liste des valeurs de restriction pour un champ de type liste de choix
	 *	Cet écran comprend les fonctionnalités suivantes :
	 *	- Ajouter une valeur
	 *	- Supprimer une ou plusieurs valeur(s)
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	require_once('fields.inc.php');

	unset( $error );

	$readonly = false;

	if( !isset($_GET['fld']) || !fld_fields_exists($_GET['fld']) ){
		$error = _("Le champ demandé n'a pas été trouvé.\nVeuillez fermer cette fenêtre.");
	}elseif( ( $readonly = fld_fields_get_is_sync($_GET['fld'], true) ) ){
		$error = _("Vous ne pouvez pas éditer les valeurs de ce champ.\nVeuillez fermer cette fenêtre.");
	}

	$is_hierarchy = false;
	$parent_id = false;
	
	// Chargement du champ à mettre à jour
	if( $rfld = fld_fields_get($_GET['fld']) ){
		if( $fld = ria_mysql_fetch_array($rfld) ){
			$is_hierarchy = $fld['type_id']==FLD_TYPE_SELECT_HIERARCHY;
		}
	}

	if( $is_hierarchy && isset($_GET['parent']) ){
		if( $rval = fld_restricted_values_get($_GET['parent']) ){
			$parent_id = $_GET['parent'];
		}
	}
	
	if( isset($_POST['name']) && trim($_POST['name']) ){
		if( fld_fields_is_tenant_linked($_GET['fld']) || !$readonly ){
			$res = fld_restricted_values_add( $_GET['fld'], $_POST['name'], $parent_id );
			if( $res==false ){
				
				// Par défaut, les virgules ne sont pas autorisées dans les intitulés (hormis pour les hiérarchies)
				if( fld_fields_get_type($_GET['fld'])!=FLD_TYPE_SELECT_HIERARCHY ){
					$val_parent_id = false;
					if( strpos($_POST['name'],',')!==false ){
						$error = _('Pour des raisons techniques, vous ne pouvez pas utiliser la virgule dans les intitulés de valeurs autorisées');
					}
				}
				if( !isset($error) ){
					$error = _("Une erreur inattendue s'est produite lors de la création de la valeur.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}else{
			$error = _("Ce champ est géré par le système, vous ne pouvez pas lui ajouter de valeurs.");
		}
	}

	// Bouton Supprimer une valeur
	if( isset($_POST['del']) && isset($_POST['val']) && is_array($_POST['val']) ){
		if( fld_fields_is_tenant_linked($_GET['fld']) || !$readonly ){
			foreach( $_POST['val'] as $v ){
				fld_restricted_values_del( $v );
			}
		}else{
			$error = _("Ce champ est géré par le système, vous ne pouvez pas supprimer les valeurs sélectionnées.");
		}
	}
	
	
	// Déplacement vers le haut d'une valeur
	if( isset($_GET['up-val']) ){
		if( !fld_restricted_values_move_up($_GET['up-val']) )
			$error = _("Une erreur inattendue s'est produite lors du déplacement de la valeur. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		
		if( !isset($error) ){
			header('Location: values.php?fld='.$_GET['fld']);
			exit;
		}
	}
	
	// Déplacement vers le bas d'une valeur
	if( isset($_GET['dw-val']) ){
		if( !fld_restricted_values_move_down($_GET['dw-val']) )
			$error = _("Une erreur inattendue s'est produite lors du déplacement de la valeur. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		
		if( !isset($error) ){
			header('Location: values.php?fld='.$_GET['fld']);
			exit;
		}
	}
	
	// Mise à jour du mode de tri
	if( isset($_POST['orderby']) ){
		fld_restricted_values_order_update(  $_GET['fld'], $_POST['order'] );
	}
	
	$ordered = fld_restricted_values_order_get( $_GET['fld'] );
	define('ADMIN_PAGE_TITLE', _('Modifier les valeurs autorisées') . ' - ' . _('Champs personnalisés') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	// Affichage des messages d'erreur
	if( isset($error) ){
		print '
			<div class="error">'.nl2br( htmlspecialchars($error) ).'</div>
		';
	}
	
	// Charge la liste des champs à modifier
	$parent_id = $parent_id==false ? 0 : $parent_id;
	$values = fld_restricted_values_get( 0, $_GET['fld'], '', $parent_id );
?>
<form action="values.php?fld=<?php print $_GET['fld'].( $parent_id!==false ? '&parent='.$parent_id : '' ); ?>" method="post">
<table id="values" class="checklist">
	<?php if( $is_hierarchy ){ ?>
	<caption>
	<?php 
		if( $is_hierarchy ){
			if( $parent_id!=false ){
				if( $rval = fld_restricted_values_get($parent_id) ){
					if( $val = ria_mysql_fetch_array($rval) )
						print '<a href="values.php?fld='.$_GET['fld'].( $val['parent']=='' ? '' : '&parent='.$val['parent'] ).'"><img src="../../../images/up.png" width="16" height="16" alt="Remonter d\'un niveau" title="' . _("Remonter d'un niveau") .'" /></a>';
				}
			}
		}
		print _('Liste des valeurs autorisées');
	?></caption>
	<?php } ?>
<thead style="display: table-header-group">
	<tr>
		<th id="select" style="text-align: left"><?php print !$readonly ? '<input type="checkbox" class="checkbox" onclick="checkAllClick(this)" />' : ''; ?></th>
		<th id="name"><?php echo _("Valeur"); ?></th>
		<?php if( $ordered ){ ?>
		<th id="sort"><?php echo _("Déplacer"); ?></th>
		<?php } ?>
	</tr>
</thead>
<tfoot>
	<tr>
		<td colspan="<?php print $ordered ? 3 : 2 ?>"><?php
			if( !$readonly ){
				if( ria_mysql_num_rows($values)>0 ){
					print '<input type="submit" name="del" id="del" value="' . _("Supprimer") . '" onclick="return fldRestrictedValueConfirmDelList()" style="float: left" />';
				}
				print '
						<input type="text" name="name" id="name" value="" maxlength="75" />
						<input type="submit" name="add" value="' . _("Ajouter") . '" />
				';
			}
		?></td>
	</tr>
	<?php if( ria_mysql_num_rows($values)>1 ){ ?>
	<tr>
		<td colspan="<?php print $ordered ? 3 : 2; ?>" style="text-align: left; background-color: #eee; padding: 3px;">
			<label><?php echo _("Trier ces valeurs par ordre :"); ?></label>
			<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php echo _("Alphabétique"); ?></label>
			<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php echo _("Personnalisé"); ?></label>
			<input type="submit" name="orderby" value="<?php echo _("Appliquer"); ?>" />
		</td>
	</tr>
	<?php } ?>
</tfoot>
<tbody>
<?php
	if( !ria_mysql_num_rows($values) ){
		print '<tr><td colspan="'.( $ordered ? 3 : 2 ).'">' . _("Aucune valeur autorisée") . '</td></tr>';
	}else{
		$count = 0;
		$nbval = ria_mysql_num_rows($values) ;
		while( $v = ria_mysql_fetch_array($values) ){
			if( !$is_hierarchy || $v['parent']==$parent_id || ($v['parent']=='' && $parent_id==0) ){
				$count ++ ;
				
				$content = htmlspecialchars( $v['name'] );
				if( $is_hierarchy ){
					$content = '<a href="values.php?fld='.$_GET['fld'].'&parent='.$v['id'].'">'. $content .'</a>';
				}
				
				print '<tr id="line-' . $v['id'] . '" class="ria-row-orderable">';
				print '<td headers="select">'.( !$readonly ? '<input type="checkbox" class="checkbox" name="val[]" value="'.$v['id'].'" />' : '' ).'</td>';
				print '<td headers="name">'. $content .'</td>';
					
				if( $ordered ){
					print '<td headers="sort" align="center" class="ria-cell-move">';
					print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
					print '</td>';
				}		
				
				print '</tr>';
			}
		}
	}
?>
</tbody>
</table>
</form>
<script><!--
	if( typeof parent.addValues != 'undefined' ){
		<?php 
			if( isset($_POST['name']) && trim($_POST['name']) ){
				if( $res !== false ){
		?>
			parent.addValues.push( <?php print $res; ?> );
		<?php 
				}
			}
		?>
	}
--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
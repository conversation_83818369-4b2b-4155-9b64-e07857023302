<?php

	/**	\file edit.php
	 *	Cette page représente la fiche d'un champ avancé. Elle permet sa création, sa modification ainsi que sa suppression (s'il n'est pas requis par le système).
	 */

	require_once('fields.inc.php');
	require_once('strings.inc.php');
	
	// Vérifie que l'utilisateur en cours peut accéder à cette page
	if( $_GET['fld']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD_EDIT');
	}elseif( $_GET['fld']==0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD_ADD');
	}
	
	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie la validité de l'identifiant d'unité passé en paramètre
	if( isset($_GET['fld']) && $_GET['fld']!=0 ){
		if( !fld_fields_exists($_GET['fld']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	$fld_sync = $sync = $readonly = false;
	$tenant_link = true;

	if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']!=0 ){
		$fld_sync = fld_fields_get_is_sync($_GET['fld']);
		$tenant_link = fld_fields_is_tenant_linked($_GET['fld']);
		$readonly = fld_fields_get_is_sync($_GET['fld'], true);

	}
	// $sync = isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']!=0 ? fld_fields_get_is_sync($_GET['fld']) : false;
	// $tenant_link = isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']!=0 ? fld_fields_is_tenant_linked($_GET['fld']) : true;
	
	// Suppression
	if( isset($_POST['del']) ){
		if( !$fld_sync || $tenant_link ){
			if( !fld_fields_del($_GET['fld']) ){
				$count = fld_fields_is_used($_GET['fld']);
				if( $count>0 ){
					$link_url = '';
					$fld_data = ria_mysql_fetch_array( fld_fields_get($_GET['fld']) );
					$class_name = fld_classes_get_name($fld_data['cls_id']);
					switch( $fld_data['cls_id'] ){
						case CLS_PRODUCT:
						case CLS_CATEGORY:
							$link_url = '../catalog/index.php';
							break;
						case CLS_STORE:
							$link_url = 'livraison/stores/index.php';
							break;
						case CLS_USER:
							$link_url = '../customers/index.php';
							break;
						case CLS_ORDER:
							$link_url = '../orders/orders.php';
							break;
						case CLS_BRAND:
							$link_url = '../catalog/brands/index.php';
							break;
					}
					
					$error = 'Le champ '.htmlspecialchars( $fld_data['name'] ).' est <a href="../../'.$link_url.'?fld='.$_GET['fld'].'">utilisé par '.$count.' '.($class_name ? substr($class_name,0,strlen($class_name)-1).'(s)' : 'élément(s)').'</a>.<br />Veuillez supprimer les valeurs avant de supprimer le champ.';
				}else{
					$error = _("Une erreur inattendue s'est produite lors de la suppression du champ personnalisé.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}else{
				header('Location: index.php');
				exit;
			}
		}elseif( !$tenant_link ){
			$error = _("Ce champ est synchronisé depuis votre gestion commerciale, il ne peut donc pas être supprimé.");
		}else{
			$error = _("Ce champ avancé est requis par le système, il ne peut donc pas être supprimé.");
		}
	}

	// Enregistrement
	if( isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['upd-values']) || isset($_POST['upd-values-hierarchy']) ){
		if( $tenant_link ){
			
			// Contrôles de saisie
			if( !isset($_POST['name']) || !trim($_POST['name']) ){
				$error = _("Veuillez indiquer le nom du champ personnalisé.");
			}elseif( !$fld_sync && (!isset($_POST['type']) || !is_numeric($_POST['type']) || $_POST['type']<=0) ){
				$error =_( "Veuillez indiquer de quel type de champ il s'agit.");
			}elseif( !$fld_sync && (!isset($_POST['cat']) || !is_numeric($_POST['cat']) || $_POST['cat']<=0) ){
				$error = _("Veuillez indiquer dans quelle catégorie le champ doit être classé.");
			}elseif( !isset($_POST['description']) ){
				$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
			
			if( !isset($error) ){

				if( !$fld_sync ){
					switch( $_POST['type'] ){
						case FLD_TYPE_TEXT:
						case FLD_TYPE_TEXTAREA:
							$_POST['precision'] = '';
							$_POST['unit'] = '';
							break;
						case FLD_TYPE_INT:
						case FLD_TYPE_FLOAT:
							if( $_POST['type']!=FLD_TYPE_FLOAT ) $_POST['precision'] = '';
							break;
						case FLD_TYPE_SELECT:
						case FLD_TYPE_SELECT_MULTIPLE:
						/*case FLD_TYPE_PRD_ID:*/
						case FLD_TYPE_SELECT_HIERARCHY:
							$_POST['min'] = $_POST['max'] = $_POST['precision'] = '';
							break;
					}
				}
				
				if( isset($_GET['fld']) && $_GET['fld']==0 ){
					// Ajout
					if( isset($_POST['classtype']) && is_numeric($_POST['classtype']) && $_POST['classtype']>0 ){
						if( $rcat=fld_categories_get($_POST['cat']) ){
							$cat = ria_mysql_fetch_array($rcat);
							if( $cat['cls_id']!=$_POST['classtype'] ){
								$error = _("Le type de données du champ et celui de sa catégorie ne correspondent pas.");
							}
						}
						if( !isset($error) ){
							$res = fld_fields_add($_POST['name'], $_POST['description'], $_POST['type'], $_POST['cat'], $_POST['unit'], $_POST['min'], $_POST['max'], $_POST['precision'], false, $_POST['classtype'], ($_POST['mandatory'] == '1' ? true : false), (isset($_POST['editeur_texte'])? false : true) );
							if( $res===ERR_NAME_EXISTS ){
								$error =_( "Un champ portant le même nom existe déjà.\nVeuillez choisir un autre nom.");
							}elseif( !$res ){
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du champ personnalisé.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
							}else{
								$_GET['fld'] = $res;

								$_SESSION['success_save_fld'] = str_replace("#param['nom_champ']#", $_POST['name'], _("Le champ personnalisé #param['nom_champ']# a été créé avec succès."));
							}
						}
					}else{
						$error = _("Le type de données du champ est invalide.");
					}
				}elseif( isset($_GET['fld']) && $_GET['fld']>0 ){
					if( !$fld_sync ){
						// Modification
						// La catégorie est du bon type de données (sauf hack, et dans ce cas fld_fields_update échoue)
						$res = fld_fields_update($_GET['fld'], $_POST['name'], $_POST['description'], $_POST['type'], $_POST['cat'], $_POST['unit'], $_POST['min'], $_POST['max'], $_POST['precision'], null, ($_POST['mandatory'] == '1' ? true : false), isset($_POST['editeur_texte'])? false : true);
						if( $res===ERR_NAME_EXISTS ){
							$error = _("Un champ portant le même nom existe déjà.\nVeuillez choisir un autre nom.");
						}elseif( !$res ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du champ personnalisé.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}
					}else{
						$res = fld_fields_set_name( $_GET['fld'],$_POST['name'] );
						if( $res===ERR_NAME_EXISTS ){
							$error = _("Un champ portant le même nom existe déjà.\nVeuillez choisir un autre nom.");
						}elseif( !$res ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du nom du champ personnalisé.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}else{
							$res = fld_fields_set_desc( $_GET['fld'],$_POST['description'] );
							if( $res ){
								$error = str_replace("#param[nom_champ]#", $_POST['name'], _("Le champ personnalisé #param[nom_champ]# a été mis à jour avec succès."));

							}else{
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la description du champ personnalisé.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
							}
						}
					}
				}
			}
		}elseif($readonly){
			$error = _("Ce champ avancé est requis par le système, il ne peut donc pas être édité.");
		}
		
		if( !isset($error) && !isset($_POST['save_stay']) && !isset($_POST['upd-values']) && !isset($_POST['upd-values-hierarchy']) ){
			$url = '/admin/config/fields/fields/index.php';
			if( isset($_POST['getcls']) && $_POST['getcls']>0 ){
				$url .= (strstr($url, '?') ? '&' : '?').'cls='.$_POST['getcls'];
			}
			if( isset($_POST['gettype']) && $_POST['gettype']>0 ){
				$url .= (strstr($url, '?') ? '&' : '?').'type='.$_POST['gettype'];
			}
			if( isset($_POST['getcat']) && $_POST['getcat']>0 ){
				$url .= (strstr($url, '?') ? '&' : '?').'cat='.$_POST['getcat'];
			}
			header('Location: '.$url);
			exit;
		}elseif (!isset($error)) {
			header('Location: /admin/config/fields/fields/edit.php?fld='.$_GET['fld']);
			exit;
		}
	}

	$cls_id = isset($_POST['classtype']) ? $_POST['classtype'] : ( isset($_GET['cls']) ? $_GET['cls'] : 0 );
	$cat_id = isset($_POST['cat']) ? $_POST['cat'] : (isset($_GET['cat']) ? $_GET['cat'] : '');
	$type_id = isset($_POST['type']) ? $_POST['type'] : (isset($_GET['type']) ? $_GET['type'] : '');

	// Chargement des données à modifier
	$field = array('id'=>0,'name'=>'','desc'=>'','type_id'=>$type_id,'cat_id'=>$cat_id,'unit_id'=>'','min'=>'','max'=>'','precision'=>'','cls_id'=>$cls_id,'is-sync'=>false, 'is_mandatory'=>false);
	if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']>0 ){
		$field = ria_mysql_fetch_array(fld_fields_get($_GET['fld']));
	}
	if( isset($_POST['name']) ) $field['name'] = $_POST['name'];
	if( isset($_POST['description']) ) $field['desc'] = $_POST['description'];
	if( isset($_POST['type']) ) $field['type_id'] = $_POST['type'];
	if( isset($_POST['cat']) ) $field['cat_id'] = $_POST['cat'];
	if( isset($_POST['unit']) ) $field['unit_id'] = $_POST['unit'];
	if( isset($_POST['min']) ) $field['min'] = $_POST['min'];
	if( isset($_POST['max']) ) $field['max'] = $_POST['max'];
	if( isset($_POST['precision']) ) $field['precision'] = $_POST['precision'];
	if( isset($_POST['editeur_texte']) ) $field['old_txt_type'] = $_POST['editeur_texte'];
	
	// Défini le titre de la page
	$page_title = ( isset($field['name']) && $field['name']!='' ) ? _('Champ personnalisé :') . ' ' . $field['name'] : _('Nouveau champ personnalisé');
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Champs personnalisés') . ' - ' . _('Structure des données') . ' - ' . _('Configuration') );
	require_once('admin/skin/header.inc.php');

	// Affichage du titre de la page
	echo '<h2>'.htmlspecialchars( $page_title ).'</h2>'; 

	// Affichage des messages d'erreur et de succès
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}elseif( $tenant_link && $readonly ){
		print '<div class="notice">' . _("Ce champ est synchronisé depuis votre gestion commerciale, certaines informations ne peuvent donc pas être modifiées.") .'</div>';
	}elseif( /*!$tenant_link &&*/ $readonly ){
		print '<div class="notice">' . _("Ce champ avancé est requis par le système, il ne peut donc pas être modifié ou supprimé.") .'</div>';
	}elseif( isset($_SESSION['success_save_fld']) ){
		print '<div class="success">'._(htmlspecialchars($_SESSION['success_save_fld'])).'</div>';
		unset($_SESSION['success_save_fld']);
	}

?>

	<form name="edit" id="edit" action="edit.php?fld=<?php print $field['id'] ?>" method="post" onsubmit="return fldFieldValidForm(this)">
		<input type="hidden" name="getcls" value="<?php print isset($_GET['cls']) ? $_GET['cls'] : 0; ?>" />
		<input type="hidden" name="gettype" value="<?php print isset($_GET['type']) ? $_GET['type'] : 0; ?>" />
		<input type="hidden" name="getcat" value="<?php print isset($_GET['cat']) ? $_GET['cat'] : 0; ?>" />
		<table class="tb-new-custom-field">
			<tbody>
				<tr><th colspan="2"><?php echo _("Propriétés générales"); ?></th></tr>
				<tr>
					<td class="col150px"><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input type="text" name="name" id="name" <?php print !$tenant_link || $readonly ? 'readonly="readonly"' : ''; ?> maxlength="75" value="<?php print htmlspecialchars($field['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="classtype"><span class="mandatory">*</span> <?php echo _('Type de données :'); ?></label></td>
					<?php if( $field['id']==0 ){ ?>
					<td>
						<select id="classtype" name="classtype">
							<?php
							if( $classes = fld_classes_get( 0, false, true, true, null, true ) ){
								while( $cls = ria_mysql_fetch_array($classes) ){
									print '<option value="'.$cls['id'].'" '.( $cls['id']==$cls_id ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $cls['name'] ).'</option>';
								}
							}
							?>
						</select>
					</td>
					<?php }else{ ?>
					<td>
						<input type="text" name="classtype" id="classtype" disabled="disabled" maxlength="75" value="<?php print htmlspecialchars( fld_classes_get_name($field['cls_id']) ); ?>" />
					</td>
					<?php } ?>
				</tr>
				<tr>
					<td><label for="type"><span class="mandatory">*</span> <?php echo _('Type :'); ?></label></td>
					<td><select <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> name="type" id="type" onchange="fldUpdateControls(this.form)" onkeyup="fldUpdateControls(this.form)">
						<option value=""><?php echo _("Veuillez sélectionner le type du champ"); ?></option>
						<?php
							$types = fld_types_get();
							while( $t = ria_mysql_fetch_array($types) ){
								print '<option value="'.$t['id'].'"'.( $field['type_id']==$t['id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($t['name']).'</option>';
							}
						?>
					</select></td>
				</tr>
				<tr>
					<td><label for="cat"><span class="mandatory">*</span> <?php echo _('Catégorie :'); ?></label></td>
					<td><select <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> name="cat" id="cat">
						<option value=""><?php echo _("Veuillez sélectionner la catégorie de classement"); ?></option>
						<?php
							if( $field['cls_id']!=0 ){
								$categories = fld_categories_get( 0,false,$field['cls_id'] );
								while( $c = ria_mysql_fetch_array($categories) ){
									print '<option value="'.$c['id'].'"'.( $field['cat_id']==$c['id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($c['name']).'</option>';
								}
							}else{
								if( $classes = fld_classes_get( 0, false, true, true, null, true ) ){
									while( $cls = ria_mysql_fetch_array($classes) ){
										$categories = fld_categories_get( 0,false,$cls['id'] );
										while( $c = ria_mysql_fetch_array($categories) ){
											print '<option value="'.$c['id'].'"'.( $field['cat_id']==$c['id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($cls['name'].' >> '.$c['name']).'</option>';
										}
									}
								}
							}
						?>
					</select></td>
				</tr>
				<tr id="edit_text" <?php print (isset($field) && $field['type_id'] == 2 && $field['cls_id'] == 87)? '' : 'class="none"';  ?>>
					<td><label for="editeur_texte"><?php echo _("Edition :"); ?></label></td>
					<td>
						<input type="checkbox" id="editeur_texte" name="editeur_texte" value="1" <?php print (isset($field['old_txt_type']) && $field['old_txt_type'] == 0) ? 'checked="checked"' : ''; ?>>
  						<label for="editeur_texte"><?php print _('Activer l\'éditeur de texte sur ce champ'); ?></label>
					</td>
				</tr>
				<tr>
					<td><label for="description"><?php echo _("Description :"); ?></label></td>
					<td><textarea <?php print !$tenant_link || $readonly ? 'readonly="readonly"' : ''; ?> name="description" id="description" rows="15" cols="40"><?php print htmlspecialchars($field['desc']); ?></textarea></td>
				</tr>
				<tr><th colspan="2"><?php echo _("Contrôles de saisie"); ?></th></tr>
				<tr id="property-mandatory">
					<td><label for="mandatory-y"><?php echo _("Obligatoire :"); ?></label></td>
					<td>
						<input type="radio" name="mandatory" id="mandatory-y" value="1" <?php print ($field['is_mandatory'] ? 'checked="checked"' : '').($readonly || !$tenant_link ? ' disabled="disabled"' : ''); ?>  />
						<label for="mandatory-y"><?php echo _("Oui"); ?></label>
						<input type="radio" name="mandatory" id="mandatory-n" value="0" <?php print (!$field['is_mandatory'] ? 'checked="checked"' : '').($readonly || !$tenant_link ? ' disabled="disabled"' : ''); ?> />
						<label for="mandatory-n"><?php echo _("Non"); ?></label>
					</td>
				</tr>
				<tr id="property-min">
					<td><label for="min" id="lbl-min"><?php echo _("Minimum :"); ?></label></td>
					<td><input <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> type="text" name="min" id="min" class="number" value="<?php print htmlspecialchars($field['min']); ?>" maxlength="12" /></td>
				</tr>
				<tr id="property-max">
					<td><label for="max" id="lbl-max"><?php echo _("Maximum :"); ?></label></td>
					<td><input <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> type="text" name="max" id="max" class="number" value="<?php print htmlspecialchars($field['max']); ?>" maxlength="12" /></td>
				</tr>
				<tr id="property-precision">
					<td><label for="precision" id="lbl-precision"><?php echo _("Précision :"); ?></label></td>
					<td><input <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> type="text" name="precision" id="precision" class="number" value="<?php print htmlspecialchars($field['precision']); ?>" maxlength="12" /></td>
				</tr>
				<tr id="property-values">
					<td><label for="values"><?php echo _("Valeurs acceptées :"); ?></label></td>
					<td>
						<?php if ($_GET['fld'] == 0) { ?>
							<div class="notice"><?php echo _("La gestion des valeurs acceptées ne pourra se faire qu'une fois la création confirmée. <br />Vous devez pour cela cliquer sur \"Enregistrer\"."); ?></div>
						<?php 
							}else{
								if( $field['id']>0 ){ 
									?>
										<div><?php 
											foreach( $config['i18n_lng_used'] as $lang ){
												print '<a class="language" href="#'.$lang.'">'.i18n_languages_get_name($lang).'</a>&nbsp;';
											}
										?></div>
										<input type="hidden" name="fld-restrected" id="fld-restrected" value="<?php print $field['id']; ?>" />
									<?php 
								}
								
								?>
								<select <?php print $readonly /*|| !$tenant_link*/ ? 'disabled="disabled"' : ''; ?> name="values" id="values" multiple="multiple" class="multiple"><?php
									if ($field['type_id'] == FLD_TYPE_SELECT_HIERARCHY) {
										$all_vals = array();
										
										$values = fld_restricted_values_get( 0, $field['id'], '', 0, '', isset($_GET['lng']) ? $_GET['lng'] : false );
										while( $r = ria_mysql_fetch_array($values) ){
											$name_val = $r['name'];
											$id = $r['id'];
											while( $vals2 = fld_restricted_values_get( $r['parent'] ) ){
												if( $r = ria_mysql_fetch_array($vals2) )
													$name_val = $r['name'].' >> '.$name_val;
											}
											$all_vals[] = array( 'id' => $id, 'name' => htmlspecialchars($name_val) );
										}
										
										$all_vals = array_msort( $all_vals, array( 'name'=>SORT_ASC ) );
										if( is_array($all_vals) && sizeof($all_vals) ){
											foreach( $all_vals as $val ){
												print '<option value="'.$val['id'].'" title="'.$val['name'].'">'.$val['name'].'</option>';
											}
										}else{
											print '<option value="">' . _("Aucune valeur saisie") . '</option>';
										}
									}else{
										$values = fld_restricted_values_get( 0, $field['id'] );
										if( $values && ria_mysql_num_rows($values) ){
											while( $r = ria_mysql_fetch_array($values) ){
												print '<option value="'.$r['id'].'">'.htmlspecialchars($r['name']).'</option>';   
											}
										}else{
											print '<option value="">' . _("Aucune valeur saisie") . '</option>';
										}
									}
								?></select>
								
								<br />

								<?php 
									if( /*$tenant_link*/ !$readonly ){
										$label = _("Modifier la liste");
										// if( /*$field['is-sync']*/ $sync ){
										// 	$label = _("Trier la liste");
										// }
								?>
									<input type="button" class="width-auto" name="upd-values" value="<?php print $label; ?>" onclick="return fldFieldModifyValues(<?php print $_GET['fld']; ?>, true);" />
									<?php if( sizeof($config['i18n_lng_used'])>1 ){ ?>
										<input type="button" class="width-auto" name="tsk-values" value="Traduire la liste" onclick="return fldFieldTranslateValues(<?php print $_GET['fld']; ?>, true);" />
									<?php } 
									}
								} ?>
					</td>
				</tr>
				<tr id="property-unit">
					<td><label for="unit" id="lbl-unit"><?php echo _("Unité de mesure :"); ?></label></td>
					<td><select <?php print $readonly || !$tenant_link ? 'disabled="disabled"' : ''; ?> name="unit" id="unit">
						<option value=""><?php echo _("Veuillez sélectionner une unité de mesure"); ?></option>
						<?php
							$units = fld_units_get();
							while( $u = ria_mysql_fetch_array($units) ){
								print '<option value="'.$u['id'].'" '.( $field['unit_id']==$u['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($u['name']).'</option>';
							}
						?>
					</select></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">			
					<?php if( /*$tenant_link ||*/ !$readonly ){ ?>
					<input type="submit" name="save_stay" id="save_stay" value="<?php echo _("Enregistrer"); ?>" <?php //print !$tenant_link ? 'disabled="disabled"' : ''; ?> />
					<input type="submit" name="save" id="save" value="<?php echo _("Enregistrer et revenir à la liste"); ?>" <?php //print !$tenant_link ? 'disabled="disabled"' : ''; ?> />
					<?php } ?>
					
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return fldFieldCancelEdit()" />
					<?php if( /*$tenant_link &&*/!$fld_sync && $field['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_DEL') ){ ?>
					<input type="submit" <?php print !$tenant_link ? 'disabled="disabled"' : ''; ?> name="del" value="<?php echo _("Supprimer"); ?>" onclick="return fldFieldConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>

	<script><!--
		$(document).ready(function () {
			fldUpdateControls(document.forms['edit']);
			
			<?php if( (isset($_POST['upd-values']) || isset($_POST['upd-values-hierarchy'])) && /*$tenant_link*/ !$readonly ){ ?>
				fldFieldModifyValues(<?php print $_GET['fld']; ?>)
			<?php }elseif( isset($_POST['tsk-values']) ){ ?>
				fldFieldTranslateValues(<?php print $_GET['fld']; ?>)
			<?php } ?>
			$('#property-values td a.language').click( fldFieldLoadValues );
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
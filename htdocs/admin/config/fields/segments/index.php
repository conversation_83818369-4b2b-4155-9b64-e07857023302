<?php

	/**	\file index.php
	 * 
	 * 	Cette page est dédiée à la gestion des segments. Elle affiche la liste des segments et permet leur ajout, modification, suppression.
	 * 
	 */

	unset($error);

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_SEGMENT');

	// pas de fonctionnement sans classe. La classe par défaut est CLS_USER (Compte client / Utilisateur)
	if( !isset($_GET['cls']) ){
		if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT') ){
			$_GET['cls'] = CLS_USER;
		}else{
			$_GET['cls'] = CLS_STORE;
		}
	}
	$cls_name = fld_classes_get_name( $_GET['cls'] );

	// Ajout d'un segment
	if( isset($_POST['add-seg']) ){
		header('Location: /admin/config/fields/segments/segment.php?cls='.$_GET['cls'].'&id=0');
		exit;
	}

	// Suppression d'un ou plusieurs segment(s)
	if( isset($_POST['del-seg']) ){
		if( isset($_POST['seg']) && is_array($_POST['seg']) && sizeof($_POST['seg']) ){
			foreach( $_POST['seg'] as $seg_id ){
				if( !seg_segments_del($seg_id) ){
					$error = _("Une erreur inattendue est survenue pendant la suppression du segment sélectionné.\nMerci de prendre contact avec nous pour nous signaler l'erreur.");
					break;
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['segment_delete_success'] = true;
			header('Location: /admin/config/fields/segments/index.php');
			exit;
		}
	}

	// Duplication d'un segment
	if( isset($_POST['copy']) ){
		if( isset($_POST['seg']) && is_array($_POST['seg']) && sizeof($_POST['seg']) ){
			foreach( $_POST['seg'] as $seg_id ){
				if( !seg_segments_duplicate($seg_id) ){
					$error = _("Une erreur inattendue est survenue pendant la copie du segment.\nMerci de prendre contact avec nous pour nous ginaler l'erreur.");
					break;
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['segment_duplicate_success'] = true;
			header('Location: /admin/config/fields/segments/index.php');
			exit;
		}
	}

	// Charge la liste des segments
	$rseg = seg_segments_get( 0, $_GET['cls'] );

	// Export des segments
	if( isset($_POST['export']) ){
		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export-segments.php');
		exit;
	}

	// Nombre de jours avant de forcer la mise à jour d'un segment
	$DAYS_REFRESH = 1;

	$can_move = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_MOVE');

	define('ADMIN_PAGE_TITLE', _('Segments').' - ' . _('Structure des données'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("Segmentation"); ?></h2>
<div class="stats-menu">
	<div id="selectorclass" class="riapicker">
		<div class="selectorview">
			<div class="left">
				<span class="function_name"><?php echo _("Classes"); ?></span>
				<br /><span class="view"><?php print htmlspecialchars($cls_name); ?></span>
			</div>
			<a class="btn" name="btn">
				<img class="fleche" src="/admin/images/stats/fleche.gif" alt="" />
			</a>
			<div class="clear"></div>
		</div>
		<div class="selector"><?php
			$rcls = fld_classes_get( 0, false, true, true, true );
			if( $rcls && ria_mysql_num_rows($rcls) ){
				while( $cls = ria_mysql_fetch_array($rcls) ){
					if( $cls['id'] == CLS_USER && !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT') ){
						continue;
					}
					print '<a name="cls-'.$cls['id'].'">'.htmlspecialchars( $cls['name'] ).'</a>';
				}
			}
		?></div>
	</div>
	<div class="clear"></div>
</div>
<?php
	// Affichage des messages d'erreur et de succès
	if( isset($error) ){
		print '
			<div class="error">'.nl2br($error).'</div>
		';
	}elseif( isset($_SESSION['segment_delete_success']) ){
		print '
			<div class="success">' . _("Les segments sélectionnés ont été supprimés.") . '</div>
		';

		unset( $_SESSION['segment_delete_success'] );
	}elseif( isset($_SESSION['segment_duplicate_success']) ){
		print '
			<div class="success">' . _("La duplication s'est correctement déroulée, vous retrouvez ci-dessous les copies.") . '</div>
		';

		unset( $_SESSION['segment_duplicate_success'] );
	}
?>
<form action="/admin/config/fields/segments/index.php?cls=<?php print $_GET['cls']; ?>" method="post">
	<input type="hidden" name="cls_id" id="cls_id" value="<?php print $_GET['cls']; ?>" />
	<table id="obj-seg" class="checklist">
		<thead>
			<tr>
				<th id="seg-del">
					<input type="checkbox" id="check-all-del" name="check-all-del" onclick="checkAllClick(this)" />
				</th>
				<th id="seg-name"><?php echo _("Nom"); ?></th>
				<th id="seg-desc"><?php echo _("Description"); ?></th>
				<th id="seg-objects" class="align-right"><?php echo _("Objet(s) rattaché(s)"); ?></th>
				<?php if( $can_move ){ ?>
				<th id="type-pos"><?php echo _("Déplacer"); ?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody><?php
			if( $rseg && ria_mysql_num_rows($rseg) ){
				$count = ria_mysql_num_rows($rseg);
				while( $seg = ria_mysql_fetch_array($rseg) ){
					print '	<tr  id="line-'.$seg['id'].'" class="ria-row-orderable">
								<td class="centertd">
									<input type="hidden" id="seg-id-'.$seg['id'].'" name="seg-id-'.$seg['id'].'" />
									<input type="checkbox" id="seg-'.$seg['id'].'" name="seg[]" value="'.$seg['id'].'" />
								</td>
								<td>';
					if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_EDIT') ){
						print '		<a href="/admin/config/fields/segments/segment.php?id='.$seg['id'].'">'.htmlspecialchars($seg['name']).'</a>';
					}else{
						print 		htmlspecialchars($seg['name']);
					}
					print '		</td>
								<td>'.(trim($seg['desc']) ? htmlspecialchars($seg['desc']) : '&nbsp;').'</td>
								<td class="align-right">';
					if( $_GET['cls'] == CLS_USER ){
						print '<a href="/admin/customers/index.php?seg='.$seg['id'].'">'.ria_number_format($seg['objects']).' '.($seg['objects'] > 1 ? _('clients rattachés') : _('client rattaché')).'</a>';
					}elseif( $_GET['cls'] == CLS_STORE ){
						print '<a href="/admin/config/livraison/stores/index.php?seg='.$seg['id'].'">'.ria_number_format($seg['objects']).' '.($seg['objects'] > 1 ? _('magasins rattachés') : _('magasin rattaché')).'</a>';
					}else{
						print ria_number_format($seg['objects']).' '.($seg['objects'] > 1 ? _('objets rattachés') : _('objet rattaché'));
					}
					print '		</td>';

					if( $can_move ){
						print '	<td headers="type-pos" class="ria-cell-move">';
						print '		<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
						print '	</td>';
					}
					print '	</tr>';
				}
			} else {
				?>
			<tr>
				<td colspan="<?php print $can_move? '5': '4'; ?>"><?php echo _("Aucun segment n'a été trouvé."); ?></td>
			</tr>
				<?php
			}
		?></tbody>
		<tfoot>
			<tr>
				<td class="tdleft" colspan="3">
				<?php if( $rseg && ria_mysql_num_rows($rseg) ){ ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_DEL') ){ ?>
						<input type="submit" name="del-seg" id="del-seg" value="<?php echo _("Supprimer"); ?>" onclick="return window.confirm(<?php echo _("Êtes-vous sûr(e) de vouloir supprimer les segments sélectionnés ?"); ?>)" />
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_ADD') ){ ?>
						<input type="submit" name="copy" value="<?php echo _("Dupliquer"); ?>" title="<?php echo _("Dupliquer le segment."); ?>" />
					<?php } ?>
					<input type="submit" name="export" id="export" value="<?php echo _("Exporter"); ?>" title="<?php echo _("Exporter la liste au format Excel"); ?>" />
				<?php } ?>
				</td>
				<td colspan="<?php print $can_move ? '2': '1'; ?>">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_ADD') ){ ?>
					<input type="submit" name="add-seg" id="add-seg" value="<?php echo _("Nouveau segment"); ?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
	<?php if( $_GET['cls'] == CLS_USER ){ ?>
	<div class="notice">
		<p><?php echo _("Les segments vous permettent de créer une sélection dynamique de vos comptes clients en fonction de critères de votre choix. Les segments ainsi créés pourront ensuite être exportés ou utilisés dans les domaines suivants :"); ?></p>
		<ul>
			<li><?php echo _("Création de codes promotion"); ?></li>
			<li><?php echo _("Affichage sélectif de bannières, d'actualités ou de contenus"); ?></li>
			<li><?php echo _("Accès à des documents"); ?><?php print $config['use_catalog_restrictions'] ? _(' ou à certaines parties du catalogue') : ''; ?></li>
			<li><?php echo _("Calcul des points de fidélité"); ?></li>
		</ul>
		<p><?php echo _("Vous pouvez définir un ordre de priorité entre les segments, via la fonction \"Déplacer\". Ce tri nous permet de savoir lequel doit être utilisé en priorité lors d'un conflit (ex : un segment peut bénéficier d'un code promotion et l'autre non, et l'un de vos clients respecte les critères pour les deux)."); ?></p>
	</div>
	<?php } ?>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file segment.php
	 *	Cette page permet la création et la mise à jour d'un segment de comptes clients. Un segment est une liste dynamique,
	 *	construite à partir de critères qui sont gérés depuis cet écran.
	 */

	require_once('sys.zones.inc.php');

	if( !isset($_GET['id']) ) $_GET['id'] = 0;

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( $_GET['id']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_EDIT');
	}else{ // $_GET['id'] == 0
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_ADD');
	}

	unset($error);
	unset($success);

	$to_news = isset($_GET['dest']) && $_GET['dest']=='NEWSLETTER_FILTER';

	$cls_id = isset($_GET['cls']) ? $_GET['cls'] : 0;
	if( !isset($_GET['id']) ){
		$_GET['id'] = 0;
	}

	if( $_GET['id'] ){
		$rseg = seg_segments_get( $_GET['id'] );
		$cls_id = $rseg && ria_mysql_num_rows($rseg) ? ria_mysql_result($rseg, 0, 'cls_id') : 0;
	}

	// la classe doit être segmentable
	if( !is_numeric($cls_id) || $cls_id <= 0 || !fld_classes_is_segmentable( $cls_id ) ){
		header('Location: /admin/config/fields/segments/index.php');
		exit;
	}

	if( isset($_GET['success']) && $to_news ){
		header('Location: /admin/tools/newsletter/filter.php?seg_id='.$_GET['id']);
		exit;
	}

	// suppression du segment
	if( isset($_POST['delete']) && $_GET['id']>0 ){
		if( !seg_segments_del( $_GET['id'] ) ){
			$error = _("Une erreur inattendue est survenue pendant la suppression du segment.\nMerci de prendre contact avec nous pour nous ginaler l'erreur.");
		}else{
			header('Location: /admin/config/fields/segments/index.php');
			exit;
		}
	}

	// annulation
	if( isset($_POST['cancel']) ){
		header('Location: /admin/config/fields/segments/index.php');
		exit;
	}

	// Redirige vers l'export "Excel" si cette action est demandée
	if (isset($_GET['downloadexport'])) {
		$file = $config['doc_dir'] . '/customers.csv';
		if (file_exists($file)) {
			header('Content-Description: File Transfer');
			header('Content-Type: application/octet-stream');
			header('Content-Disposition: attachment; filename="customers.csv"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
			ob_clean();
			flush();
			readfile($file);
			exit;
		} else {
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	// copie
	if( isset($_POST['copy']) && $_GET['id']>0 ){
		$id_new_seg = seg_segments_duplicate( $_GET['id'] );
		if( !$id_new_seg ){
			$error = _("Une erreur inattendue est survenue pendant la copie du segment.\nMerci de prendre contact avec nous pour nous ginaler l'erreur.");
		}

		if( !isset($error) ){
			header('Location: /admin/config/fields/segments/segment.php?id='.$id_new_seg);
			exit;
		}
	}

	// chargement du segment
	if( $_GET['id'] ){
		$rseg = seg_segments_get($_GET['id']);

		if( !$rseg ){
			header('Location: /admin/config/fields/segments/index.php');
			exit;
		}

		$s = ria_mysql_fetch_array($rseg);
		$s['objects'] = seg_segments_count_objects( $s['id'] );
		$page_title = $s['name'];
	}else{
		$page_title = _('Nouveau segment');

		$s = array('name'=>( isset($_POST['name']) ? $_POST['name'] : ( $to_news ? 'Filtre newsletter '.date('d/m/Y H:i') : '' ) ), 'desc'=>( isset($_POST['desc']) ? $_POST['desc'] : '' ), 'objects' => 0);
	}

	define('ADMIN_PAGE_TITLE', $page_title.' - ' . _('Segments') . ' - ' . _('Structure des données'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print htmlspecialchars( $page_title ); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}elseif( isset($success) ){
		print '<div class="success">'.nl2br($success).'</div>';
	}elseif( isset($_GET['success']) ){
		print '<div class="success">' . _('L\'enregistrement de votre segment s\'est correctement déroulé.') . '</div>';
	}

	$type_obj = '';
	if( $cls_id == CLS_USER ){
		$type_obj = 'comptes clients';
	}elseif( $cls_id == CLS_STORE ){
		$type_obj = 'magasins';
	}else{
		error_log(__FILE__.':'.__LINE__.' - classe non gérée');
	}
?>

	<form id="form-segment" action="/admin/config/fields/segments/segment.php?id=<?php print $_GET['id']; ?>" method="post">
		<table>
			<tbody>
				<tr>
					<td>
						<label for="name"><span class="mandatory">*</span> <?php echo _("Intitulé :"); ?></label>
					</td>
					<td>
						<input type="text" name="name" id="name" value="<?php print htmlspecialchars($s['name']); ?>" maxlength="75" />
					</td>
				</tr>
				<tr>
					<td>
						<label for="desc"><?php echo _('Description :'); ?></label>
					</td>
					<td>
						<textarea name="desc" id="desc" cols="50" rows="10"><?php print htmlspecialchars($s['desc']); ?></textarea>
					</td>
				</tr>
				<?php
				if( $_GET['id']>0 ){
					print '
						<tr>
							<td>
								<label>' . _("Nombre d'objets :") . '</label>
							</td>
							<td>
					';

					switch( $cls_id ){
						case CLS_USER: {
							print '
								<a href="/admin/customers/index.php?seg='.$s['id'].'">'.ria_number_format($s['objects'])._(' compte(s)') . '</a>
							';

							if( $s['objects'] > 0 ){
								$ar_user_ids = gu_users_get_by_segment($s['id']);

								if( is_array($ar_user_ids) && sizeof($ar_user_ids) ){
									$stat = array('count' => 0, 'total_ht' => 0, 'total_ttc' => 0, 'marge' => 0, 'nb_products' => 0 );

									$rstat = ord_orders_totals_get( ord_states_get_ord_valid(), $ar_user_ids, 0, 0, true );
									if( $rstat && ria_mysql_num_rows($rstat) ){
										$stat = ria_mysql_fetch_assoc( $rstat );
										$stat['nb_products'] = 0;

										$prd_stats = ord_products_totals_get( $ar_user_ids );
										if( $prd_stats && ria_mysql_num_rows($prd_stats) ){
											$prd_stats = ria_mysql_fetch_assoc( $prd_stats );

											$stat['nb_products'] = $prd_stats['count_prd'];
										}

										if( $stat['count']>0 ){
											print '</td></tr><tr><td>' . _('Statistiques :') . '</td><td>
												<ul class="ul-stats">
													<li>' . _("Nombre moyen de commandes réalisées par compte client :") . ' '.ria_number_format($stat['count'] / $s['objects'], NumberFormatter::DECIMAL, 2).'</li>
													<li>' . _("Nombre de produits par commande (hors port et assurance) :") . ' '.ria_number_format(($stat['count'] ? $stat['nb_products'] / $stat['count'] : 0), NumberFormatter::DECIMAL, 2).'</li>
													<li>' . _("Chiffre d’affaires moyen par compte client HT :") . ' '.ria_number_format($stat['total_ht'] / $s['objects'], NumberFormatter::CURRENCY, 2).'</li>
													<li>' . _("Chiffre d’affaires moyen par compte client TTC :") . ' '.ria_number_format($stat['total_ttc'] / $s['objects'], NumberFormatter::CURRENCY, 2).'</li>
													<li>' . _("Marge brute moyenne par compte client (HT) :") . ' '.ria_number_format($stat['total_margin_ht'] / $s['objects'], NumberFormatter::CURRENCY, 2).'</li>
													<li>' . _("Chiffre d’affaires total ") . '<abbr title="'._("Hors Taxes").'">' . _('HT') .'</abbr> : '.ria_number_format( $stat['total_ht'], NumberFormatter::CURRENCY, 2).'</li>
													<li>' . _("Chiffre d’affaires total ") . '<abbr title="'._('Toutes Taxes Comprises').'">'._('TTC').'</abbr> : '.ria_number_format($stat['total_ttc'], NumberFormatter::CURRENCY, 2).'</li>
													<li>' . _("Marge brute totale ") . '<abbr title="'._('Hors Taxes').'">'._('HT').'</abbr> : '.ria_number_format($stat['total_margin_ht'], NumberFormatter::CURRENCY, 2).'</li>
												</ul>
											';
										}

									}

								}
							}

							break;
						}
						case CLS_STORE: {
							print '
								<a href="/admin/config/livraison/stores/index.php?seg='.$s['id'].'">'.number_format( $s['objects'], 0, '.', ' ' )._(' magasin(s)') . '</a>
							';
						}
						default: {
							print number_format($s['objects'],0,'.',' ')._(' objet(s)');
						}
					}

					print '
							</td>
						</tr>
					';
				} ?>
				<tr>
					<th colspan="2"><?php echo _("Critères de segmentation"); ?></th>
				</tr>
				<tr>
					<td colspan="2">
						<div class="add-seg-cdt">
						<?php
							$rsc = seg_segment_criterions_get( 0, $_GET['id'] );

							$i = 0;
							if( $rsc && ria_mysql_num_rows($rsc) ){
								$rc = seg_criterions_get( 0, $cls_id );
								$rsource = seg_ord_sources_get();
								$date_codes = seg_dyn_date_get();
								$rwst = wst_websites_get();
								$rfield = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $cls_id );

								$igrp = 0; $grp_id = -1; $first = true; $count_in_group = -1;
								while( $sc = ria_mysql_fetch_assoc($rsc) ){
									$rsos_id = seg_criterions_sources_get($sc['sgc_id']);
									$sos_id = array();

									if( $grp_id!=$sc['grp_id'] ){
										$i=0;
									}
									if($rsos_id){
										while($row = ria_mysql_fetch_assoc($rsos_id)){
											$sos_id[]=$row['sos_id'];
										}
									}

									if( $count_in_group < 0 ){
										$scg = seg_segment_criterions_get( 0, $_GET['id'], $sc['grp_id'] );
										$count_in_group = $scg ? ria_mysql_num_rows($scg) : 0;
									}

									$html_source = '
									<div class="riapicker items-list-filters" id="cdt-source-'.$sc['grp_id'].'-'.$i.'">
										<div class="selectorview">
											<div class="left">
												<span class="view"></span></div>
												<a class="btn" name="btn">
													<img src="/admin/images/stats/fleche.gif" class="fleche" alt="" />
												</a>
												<div class="clear"></div>
											</div>
											<div class="selector">
									';
									$html_website = '<option value="-1">' . _("Tous") . '</option>';
									$html_fields = '<option value="-1"></option>';
									$htmlButtonRemove = '			<input type="button" onclick="return segCriterionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" title="' . _("Supprimer ce critère") . '" />';

									$typ = seg_criterions_get_type( $sc['sec_id'] );
									$grp = seg_criterions_get_group( $sc['sec_id'] );
									$code = seg_criterions_get_code( $sc['sec_id'] );

									if( $rwst && ria_mysql_num_rows($rwst) ){
										ria_mysql_data_seek( $rwst, 0 );
										while( $wst = ria_mysql_fetch_array($rwst) ){
											$html_website .= '<option '.( $wst['id']==$sc['wst_id'] ? 'selected="selected"' : '' ).' value="'.$wst['id'].'">'.htmlspecialchars( $wst['name'] ).'</option>';
										}
									}

									if( $grp==4 && !in_array($sc['sec_id'], seg_criterions_noweb()) ){
										if( $rsource && ria_mysql_num_rows($rsource) ){
											$old_opt = $in_opt = false;
											$source_group=1;

											ria_mysql_data_seek( $rsource, 0 );
											while( $source = ria_mysql_fetch_array($rsource) ){

												if( $source['group_id'] != 0 && $source['group_id'] != $old_opt ){
													if( $in_opt ){
														$html_source .= '</div>';
													}

													$html_source .= '<div>
													<a class="parent" name="gp-'.$source_group.$igrp.$i.'">
																		<input type="checkbox" id="gp-'.$source_group.$igrp.$i.'" value="gp-'.$source_group.$igrp.$i.'" />
																		<label for="gp-'.$source_group.$igrp.$i.'">'.htmlspecialchars( $source['group_name'] ).'</label>
																	</a>';
													$old_opt = $source['group_id'];
													$source_group++;
													$in_opt  = true;
												}

												$html_source .= '<a class="'.((in_array($source['id'], array(1,2,3)))? 'check-hidden' :  'child').'" name="'.$source['id'].'">
																	<input '.( in_array($source['id'],$sos_id) ? 'checked="checked"' : '' ).' type="checkbox" name="cdt-source['.$igrp.']['.$i.'][]" id="sos-'.$source['id'].$igrp.$i.'" value="'.$source['id'].'" />
																	<label for="sos-'.$source['id'].$igrp.$i.'">'.htmlspecialchars( $source['name'] ).'</label>
																</a>';
											}

											if( $in_opt ){
												$html_source .= '</div>';
											}
										}
									} else {
										$html_source .= '<option value="-1" selected="selected"></option>';
									}
									$html_source .= '</div></div>';
									if( seg_criterions_get_use_period($sc['sec_id']) ){
										$start = isdateheure( $sc['date_start'] ) ? date( 'd/m/Y', strtotime($sc['date_start']) ) : '';
										$end = isdateheure( $sc['date_end'] ) ? date( 'd/m/Y', strtotime($sc['date_end']) ) : '';
										$html_dates = '
											<div class="riapicker items-list-filters dates" id="cdt-dates-'.$sc['grp_id'].'-'.$i.'">
											<input type="hidden" class="cdt-dates" name="cdt-dates['.$sc['grp_id'].']['.$i.']" value="'.$sc['date_days'].'">
												<div class="selectorview">
													<div class="left">
														<span class="view">'.(isset($date_codes[$sc['date_days']])? $date_codes[$sc['date_days']] : '').'</span>
													</div>
													<a class="btn" name="btn">
														<img src="/admin/images/stats/fleche.gif" class="fleche" alt="" />
													</a>
													<div class="clear"></div>
												</div>
												<div class="selector">
										';

										foreach ($date_codes as $key => $value) {
											if($key === 'perso'){
												$html_dates .= '
													<a name="'.$key.'">
														<label for="sos-'.$key.$sc['grp_id'].$i.'">'.htmlspecialchars( $value ).'</label>
														<div class="dates-days">
														<input class="date datepicker date-start" type="text" name="dates-start['.$sc['grp_id'].']['.$i.']" id="cdt-dates-start-'.$sc['grp_id'].'-'.$i.'" value="'.$start.'" />
														<span class="end" for="cdt-dates-end-'.$sc['grp_id'].'-'.$i.'">-</span>
														<input  class="date datepicker date-end"type="text" name="dates-end['.$sc['grp_id'].']['.$i.']" id="cdt-dates-end-'.$sc['grp_id'].'-'.$i.'" value="'.$end.'" />
														</div>
													</a>
												';
												continue;
											}
											$html_dates .= '<a name="'.$key.'">'.htmlspecialchars( $value ).'</a>';
										}

										$html_dates .= '</div></div>';
									}

									if( seg_criterions_get_use_fields($sc['sec_code']) ){
										$typ = fld_fields_get_type( $sc['fld_id'] );

										if( $rfield && ria_mysql_num_rows($rfield) ){
											ria_mysql_data_seek( $rfield, 0 );

											while( $field = ria_mysql_fetch_array($rfield) ){
												$html_fields .= '<option '.( $field['id']==$sc['fld_id'] ? 'selected="selected"' : '' ).' title="'.htmlspecialchars( $field['name'] ).'" value="'.$field['id'].'">'.htmlspecialchars( $field['name'] ).'</option>';
											}
										}
									}

									if( $grp_id!=$sc['grp_id'] ){
										$grp_id = $sc['grp_id'];
										if( !$first ){
											print '		</div>';
											// print '		<input type="button" onclick="return segGroupCopy('.$igrp.');" class="btn-action-small" value="Copier le groupe" id="cdt-copy-'.$igrp.'" name="cdt-copy" title="Copier le groupe" />';
											print '		<input type="button" onclick="return segCriterionsAdd('.$igrp.');" class="btn-action-small" value="' . _("Ajouter un critère") . '" id="cdt-add-'.$i.'" name="cdt-add" title="' . _("Ajouter un critère à ce groupe de conditions") . '" />';
											print '	</fieldset>';
										}

										if( !$first ){
											$igrp++;
											$count_in_group = -1;
											print '<div class="cdt-separator-and">' . _('ET') . '</div>';
										}

										print '	<fieldset class="cdt-grp">
													<legend class="cdt-grp-legend">
														<label>'.sprintf(_('Groupe %d - Valide au moins un des critères suivants :'), $igrp + 1 ).'</label>
														<input class="cdt-grp-del input-icon-del" type="button" name="del-grp-cdt" id="del-grp-cdt-'.$i.'" value="x" title="' . _("Supprimer le groupe") . ' '.($igrp+1)._(' de critères') . '" />
													</legend>
													<div id="cdt-grp-list-cdt'.$igrp.'" class="cdt-grp-list-cdt">';
									}

									print '			<div class="cdt-config">';

									if( $i>0 ){
										print '			<div class="cdt-separator-or">' . _('OU') . '</div>';
									} else {
										print '			<div class="cdt-separator-or">&nbsp;</div>';
									}

									// Affiche la liste des critères de segmentation, avec un rupture par Groupe de critères
									print '				<div class="cdt-select-second">
															<select id="cdt-type-'.$igrp.'-'.$i.'" name="cdt-type['.$igrp.']['.$i.']" class="cdt-grp-cdt selectmenu">
																<option value=""></option>';

									$pos = 1; $grp_name = '';
									ria_mysql_data_seek( $rc, 0 );
									$rows = ria_mysql_num_rows($rc);
									while( $c = ria_mysql_fetch_array($rc) ){
										if( $grp_name!=$c['scg_name'] ){
											if( $pos!=1 ){
												print  '		</optgroup>';
											}
											print  '			<optgroup label="'.htmlspecialchars( $c['scg_name'] ).'">';
											$grp_name = $c['scg_name'];
										}

										print  '					<option '.( $c['id']==$sc['sec_id'] ? 'selected="selected"' : '' ).' value="'.$c['id'].'">'.htmlspecialchars( $c['name'] ).'</option>';

										if( $grp_name!=$c['scg_name'] || $pos==$rows ){
											print  '			</optgroup>';
										}

										$pos++;
									}

									print '			</select>
													<div class="div-source-cdt'.( $grp!=4 || in_array($sc['sec_id'], seg_criterions_noweb()) ? ' none' : '' ).'">
														<label for="cdt-source-'.$igrp.'-'.$i.'">' . _("Commande :") . '</label>'
														. $html_source .
													'</div>
													<div class="clear"></div>
													<div class="div-website-cdt'.( seg_criterions_get_use_wst($sc['sec_id']) && ria_mysql_num_rows($rwst)>1 ? '' : ' none' ).'">
														<label for="cdt-website-'.$igrp.'-'.$i.'">' . _("Site :") . '</label>
														<select class="cdt-website-cdt selectmenu" name="cdt-website['.$igrp.']['.$i.']" id="cdt-website-'.$igrp.'-'.$i.'">'
														. $html_website .
														'</select>
													</div>
													<div class="div-fields-cdt'.( seg_criterions_get_use_fields($sc['sec_code']) && ria_mysql_num_rows($rfield)>1 ? '' : ' none' ).'">
														<label for="cdt-fields-'.$igrp.'-'.$i.'">' . _("Champ :") . '</label>
														<select class="cdt-fields-cdt selectmenu" name="cdt-fields['.$igrp.']['.$i.']" id="cdt-fields-'.$igrp.'-'.$i.'">'
															. $html_fields .
														'</select>
													</div>';

									if( seg_criterions_get_use_period($sc['sec_id']) ){
										$start = isdateheure( $sc['date_start'] ) ? date( 'd/m/Y', strtotime($sc['date_start']) ) : '';
										$end = isdateheure( $sc['date_end'] ) ? date( 'd/m/Y', strtotime($sc['date_end']) ) : '';

										print '	<div class="div-dates-cdt">
													<label for="cdt-dates-'.$igrp.'-'.$i.'">' . _("Période :") . '</label>'
														. $html_dates ;
										// print '		<select class="select-dates-cdt selectmenu" name="cdt-dates['.$igrp.']['.$i.']" id="cdt-dates-'.$igrp.'-'.$i.'" style="width:250px;">';
										// print '			<option value="perso" '.(trim($sc['date_days'])===""?'selected="selected"':'').'>Personnalisée</option>';
										// print '			<option value="dyn" '.(trim($sc['date_days'])!==""?'selected="selected"':'').'>Dynamique</option>';
										// print '		</select>';
										// print '		<div class="div-dates" '.(!empty($sc['date_days'])?'style="display:none;"':'').'>';
										// print '			<label for="cdt-dates-start-'.$igrp.'-'.$i.'">Du : </label>';
										// print '			<input class="date datepicker date-start" type="text" name="dates-start['.$igrp.']['.$i.']" id="cdt-dates-start-'.$igrp.'-'.$i.'" value="'.$start.'" />';
										// print '			<label class="end" for="cdt-dates-end-'.$igrp.'-'.$i.'">au</label>';
										// print '			<input  class="date datepicker date-end"type="text" name="dates-end['.$igrp.']['.$i.']" id="cdt-dates-end-'.$igrp.'-'.$i.'" value="'.$end.'" />';
										// print '		</div>';
										// print '		<div class="div-days" '.(empty($sc['date_days'])?'style="display:none;"':'').'>';
										// print '			<label for="cdt-dates-days-'.$igrp.'-'.$i.'">De : </label>';
										// print '			<input  class="date date-days"type="text" name="date-days['.$igrp.']['.$i.']" id="cdt-dates-days-'.$igrp.'-'.$i.'" value="'.$sc['date_days'].'" />';
										// print '			<sapn>jour(s)</span>';
										// print '		</div>';
										print '	</div>';
									}

									print '				</div>
														<div class="cdt-psy-val">
															<select '.( $typ==8 || $code=='USR_GENDER' ? 'disabled="disabled"' : '' ).' id="cdt-symbol-'.$igrp.'-'.$i.'" name="cdt-symbol['.$igrp.']['.$i.']" class="cdt-psy selectmenu block">
																<option value="-1"></option>';

									$symbols = seg_criterions_get_symbols( $sc['sec_id'], CUSTOM_DATE, is_numeric($sc['fld_id']) && $sc['fld_id']>0 ? $sc['fld_id'] : 0 );

									foreach( $symbols as $s ){
										print '					<option '.( $s['symbol']==$sc['symbol'] ? 'selected="selected"' : '' ).' value="'.$s['symbol'].'">'.htmlspecialchars( $s['desc'] ).'</option>';
									}

									print '					</select>
														</div>
														<div class="div-cdt-value">';

									$attrID = 'cdt-value-'.$igrp.'-'.$i; $attrName = 'cdt-value['.$igrp.']['.$i.']';
									$value = '';
									switch( $typ ){
										case 3 :
											print '			<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-value" id="'.$attrID.'" name="'.$attrName.'" value="'.htmlspecialchars($sc['value']).'" /></span>'
																. $htmlButtonRemove .
															'<sub>' . _("La valeur saisie doit être un nombre entier") . '</sub>';
											break;
										case 4 :
											print '			<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-value" id="'.$attrID.'" name="'.$attrName.'" value="'.htmlspecialchars($sc['value']).'" /></span>'
																. $htmlButtonRemove .
																'<sub>' . _("La valeur saisie doit être un nombre entier ou décimal.") . '</sub>';
											break;
										case 8 :
											print '			<div class="cdt-form-bool">
																<input '.( $sc['value'] ? 'checked="checked"' : '' ).' type="radio" id="'.$attrID.'-y" name="'.$attrName.'" value="1" />
																<label for="'.$attrID.'-y">' . _("Oui") . '</label>
																<input '.( !$sc['value'] ? 'checked="checked"' : '' ).' type="radio" id="'.$attrID.'-n" name="'.$attrName.'" value="0" />
																<label for="'.$attrID.'-n">' . _("Non") . '</label>
															</div>'
															. $htmlButtonRemove .
															'<div class="clear"></div>';
											break;
										case 10 :
											print '			<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-prd-search datepicker" id="'.$attrID.'" name="'.$attrName.'" value="'.htmlspecialchars( dateheureunparse($sc['value']) ).'" /></span>'
															. $htmlButtonRemove .
															'<sub>' . _("La valeur saisie doit être une date au format JJ/MM/AAAA (exemple : 31/12/1972).") . '</sub>';
											break;
										case 1 :
										case 5 :
										case 6 :
										case 11 :
										case 12 :
											$ar_data = seg_criterions_get_objects_array( $code, $sc['fld_id'] );

											if( sizeof($ar_data) ){
												print '		<select class="selectmenu cdt-grp-value" id="'.$attrID.'" name="'.$attrName.'">';
												foreach( $ar_data as $data ){
													print '		<option '.( $data['id']==$sc['value'] ? 'selected="selected"' : '' ).' value="'.$data['id'].'">'.htmlspecialchars( $data['name'] ).'</option>';
												}
												print '		</select>'
															. $htmlButtonRemove;
												break;
											}
										default :
											$v = $sc['value'];
											switch( $sc['sec_code'] ){
												case 'USR_ID' :
												case 'USR_SELLER' :
													$value = $sc['value'];
													$v = gu_users_get_email( $value );
													break;
												case 'USR_USE_CODE_PROMOTION' :
													$v = pmt_codes_get_code( $sc['value'] );
													break;
												case 'USR_CAT_CONSULTED' :
												case 'USR_CAT_ORDERED' :
												case 'USR_CAT_CANCELLED' :
													$value = $sc['value'];
													$v = prd_categories_get_name( $value );
													break;
												case 'USR_PRD_CONSULTED' :
												case 'USR_PRD_ORDERED' :
												case 'USR_PRD_CANCELLED' :
													$value = $sc['value'];
													$v = prd_products_get_name( $value );
													break;
											}

											print '			<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-prd-search" id="'.$attrID.'" name="'.$attrName.'" value="'.htmlspecialchars($v).'" /></span>';
											switch( $sc['sec_code'] ){
												case 'USR_ID' :
												case 'USR_SELLER' :
													print '	<input type="button" value="' . _("Rechercher") . '" id="cdt-search-'.$igrp.'-'.$i.'" name="cdt-search" class="btn-action-small cdt-search-client" />';
													break;
												case 'USR_CAT_CONSULTED' :
												case 'USR_CAT_ORDERED' :
												case 'USR_CAT_CANCELLED' :
													print '	<input type="button" value="' . _("Rechercher") . '" id="cdt-search-'.$igrp.'-'.$i.'" name="cdt-search" class="btn-action-small cdt-search-cat" />';
													break;
												case 'USR_PRD_CONSULTED' :
												case 'USR_PRD_ORDERED' :
												case 'USR_PRD_CANCELLED' :
													print '	<input type="button" value="' . _("Rechercher") . '" id="cdt-search-'.$igrp.'-'.$i.'" name="cdt-search" class="btn-action-small cdt-search-prd" />';
													break;
											}

											print $htmlButtonRemove;
											break;
									}
									print '				<input type="hidden" value="'.htmlspecialchars($value).'" id="cdt-prd-search-key-'.$igrp.'-'.$i.'" name="cdt-search-key['.$igrp.']['.$i.']" class="btn-action-small" />
													</div>
													<div class="clear"></div>
												</div>
												<div class="clear"></div>';

									$i++;

									$first = false;
								}

								print '		</div>
											<input type="button" onclick="return segCriterionsAdd('.$igrp.');" class="btn-action-small" value="' . _("Ajouter un critère") . '" id="cdt-add-'.$i.'" name="cdt-add" title="' . _("Ajouter un critère") . '" />
										</fieldset>';
							}
						?>
							<input type="button" value="<?php echo _("Ajouter un groupe de conditions"); ?>" id="cdt-add-grp" name="cdt-add-grp" class="btn-action-small" />
						</div>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
				<td colspan="2">
					<?php if( $_GET['id']>0 ){ ?>
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_DEL') ){ ?>
							<input type="submit" name="delete" value="<?php echo _("Supprimer"); ?>" onclick="return window.confirm('<?php print _('Êtes-vous sûr(e) de vouloir supprimer ce segment ?'); ?>')" class="float-left" />
						<?php }
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT_ADD') ){ ?>
							<input type="submit" name="copy" value="<?php echo _("Dupliquer"); ?>" title="<?php print _('Créer une copie de ce segment'); ?>" class="float-left" />
						<?php } ?>
					<?php } ?>
						<input type="submit" name="save-main" id="save-main" value="<?php echo _("Enregistrer"); ?>" />
						<?php if( $cls_id==CLS_USER ){ ?>
							<input onclick="return downloadSegmentCustomers();" type="submit" name="export" value="<?php echo _("Exporter"); ?>" title="<?php echo _("Export la liste des comptes clients sous format tableur"); ?>" />
						<?php } ?>
						<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>
		<input type="hidden" name="to_news" id="to_news" value="<?php print $to_news; ?>" />
	</form>
	<script><!--
		// initalisation des variables globales
		var nbGroups = <?php print $_GET['id']>0 ? sizeof( seg_segment_criterions_get_groups($_GET['id']) ) : 0; ?>;
		var nbGroupCdts = {};
		var segClsID = <?php print $cls_id; ?>;
		var segID = <?php print $_GET['id']; ?>;

		function downloadSegmentCustomers(){
			displayPopup('<?php print _('Exporter la liste des clients'); ?>', '', '/admin/customers/popup-export-customers.php?seg=' + segID);
			return false;
		}
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_TRANSLATE');

	$csv_header = $csv_body = '';

	if( isset($_GET['lng']) && is_array($_GET['lng']) && sizeof($_GET['lng']) ){

		// Entête du fichier CSV
		$csv_header .= '"Contexte";"Texte d\'origine"';
		foreach( $_GET['lng'] as $lang ){
			$csv_header .= ';"'.htmlspecialchars( i18n_languages_get_name($lang) ).'"';
		}
		$csv_header .= "\n";
		

		// Créer un tableau de toutes les chaines traductible
		$ar_tsl = array();
		$rtsl = i18n_translates_get();
		if( $rtsl && ria_mysql_num_rows($rtsl) ){
			while( $tsl = ria_mysql_fetch_array($rtsl) ){
				if( trim($tsl['original'])=='' ){
					continue;
				}

				if( !isset($ar_tsl[ $tsl['code'].$tsl['md5'] ]) ){
					$ar_tsl[ $tsl['code'].$tsl['md5'] ] = array(
						'context' => $tsl['context'],
						'original' => trim( $tsl['original'] ),
						'tsl_by_lang' => array()
					);
				}

				$ar_tsl[ $tsl['code'].$tsl['md5'] ]['tsl_by_lang'][ strtolower($tsl['lng_code']) ] = trim( $tsl['translation'] );
			}
		}

		// print '<pre>';
		// print_r( $ar_tsl );
		// print '</pre>';
		// exit;

		// Corps du fichier CSV
		foreach( $ar_tsl as $tsl ){
			$csv_body .= '"'.$tsl['context'].'";"'.$tsl['original'].'"';
			foreach( $_GET['lng'] as $lang ){
				$txt = array_key_exists( strtolower($lang), $tsl['tsl_by_lang'] ) ? $tsl['tsl_by_lang'][ strtolower($lang) ] : '';
				$csv_body .= ';"'.$txt.'"';
			}
			$csv_body .= "\n";
		}
	}

	header("content-type:application/csv;charset=UTF-8");
	header('Content-disposition: attachment; filename="traductions.csv"');
	header('Pragma: no-cache');
	header('Expires: 0');

	print $csv_header;
	print $csv_body;
	exit;

<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_TRANSLATE');

	require_once('products.inc.php');
	
	if( !isset($_GET['export']) && !isset($_GET['cat']) ){
		$g_error = "1Une erreur inattendue s'est produite lors du chargement des informations.";
	}

	$no_edit = isset($_REQUEST['no-edit']) && $_REQUEST['no-edit'] ? true : false;

	if( !isset($g_error) ){
		$export = array(
			'pxp_id' => 0,
			'name' => '',
			'is_auto' => false,
			'period' => '',
			'period_info' => '',
			'cat_childs' => true,
			'childonly' => false
		);

		$thumb_main = $thumb_second = '';
		$default = array( 'ref', 'name', 'title' );

		if( isset($_GET['export']) && $_GET['export']!='-1' ){
			if( !prd_exports_exists($_GET['export']) ){
				$g_error = _("1Une erreur inattendue s'est produite lors du chargement des informations.");
			}else{
				$export = ria_mysql_fetch_array( prd_exports_get($_GET['export']) );

				$_GET['cat'] = $export['cat_id'];

				$info_export = json_decode( $export['columns'], true );
				$default = isset($info_export['cols']) ? array_keys( $info_export['cols'] ) : array();
				$fields  = isset($info_export['flds']) ? array_keys( $info_export['flds'] ) : array();

				if( isset($info_export['thumb_main']) ){
					$thumb_main = $info_export['thumb_main'];
				}

				if( isset($info_export['thumb_second']) ){
					$thumb_second = $info_export['thumb_second'];
				}
			}
		}

		$cat = isset($_GET['cat']) && prd_categories_exists($_GET['cat']) ? $_GET['cat'] : 0;
		
		$link = 'popup-export-products.php';
		if( $export['pxp_id'] ){
			$link .= '?export='.$export['pxp_id'];
		}else{
			$link = '?cat='.$cat;
		}

		// Enregistre un export produits
		if( isset($_POST['save-form-export']) ){
			if( !isset($_POST['col-filter']) || !is_array($_POST['col-filter']) || !sizeof($_POST['col-filter']) ){
				$error = _("Veuillez sélection une ou plusieurs informations inclusent dans cet export");
			}elseif( !isset($_POST['export-name'], $_POST['export-auto'], $_POST['export-period'], $_POST['day'], $_POST['month']) ){
				$error = _("Une ou plusieurs informations sont manquantes.");
			}elseif( trim($_POST['export-name'])=='' ){
				$error = _("Veuillez renseigner le nom à donner à cet export.");
			}else{
				$period = $_POST['export-auto'] ? $_POST['export-period'] : '';
				$period_info = $period=='week' ? $_POST['day'] : ( $period=='month' ? $_POST['month'] : '' );

				$columns['cols'] = isset($_POST['col-filter']) && is_array($_POST['col-filter']) ? $_POST['col-filter'] : array();
				$columns['flds'] = isset($_POST['fld-filter']) && is_array($_POST['fld-filter']) ? $_POST['fld-filter'] : array();

				if( in_array('img_main', array_keys($columns['cols'])) ){
					$columns['thumb_main'] = $_POST['size-img']['img_main'];
				}
				if( in_array('img_second', array_keys($columns['cols'])) ){
					$columns['thumb_second'] = $_POST['size-img']['img_second'];
				}
				$catchilds = isset($_POST['catchilds']) ? true : false;
				$childonly = isset($_POST['childonly']) ? true : false;

				if( $export['pxp_id']>0 ){
					if( !prd_exports_update( $export['pxp_id'], $_POST['export-name'], $columns, $_POST['export-auto'] ? true : false, $period, $period_info, $catchilds, $childonly) ){
						$error = _("Une erreur inattendue s'est produits lors de la mise à jour de l'export. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}else{
					$export['pxp_id'] = prd_exports_add( $_POST['export-name'], $cat, $columns, $catchilds, $_POST['export-auto'] ? true : false, $period, $period_info, $childonly );
					if( !$export['pxp_id'] ){
						$error = _("Une erreur inattendue s'est produits lors de l'enregistrement de l'export. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}
			}

			if( !isset($error) ){
				header('Location: /admin/catalog/popup-export-products.php?export='.$export['pxp_id'].'&no-edit=1');
				exit;
			}else{
				$no_edit = false;
			}
		}

		// Exécute un export produits
		if( isset($_POST['export']) ){
			include('export-products.php?cat='.$cat);
			exit;
		}

		if( sizeof($_POST) && isset($error) ){
			$export = array(
				'pxp_id' => $export['pxp_id'],
				'name' => isset($_POST['export-name']) ? $_POST['export-name'] : $export['name'],
				'is_auto' => isset($_POST['export-auto']) ? ($_POST['export-auto'] ? true : false) : $export['is_auto'],
				'period' => isset($_POST['export-auto'], $_POST['export-period']) && $_POST['export-auto'] ? $_POST['export-period'] : $export['period'],
				'period_info' => $_POST['export-period']=='week' ? $_POST['day'] : ( $_POST['export-period']=='month' ? $_POST['month'] : $export['period_info'] ),
				'cat_childs' => isset($_POST['catchilds']) && $_POST['catchilds'] ? true : $export['cat_childs'],
				'childonly' => isset($_POST['childonly']) && $_POST['childonly'] ? true : $export['childonly']
			);

			$default = isset($_POST['col-filter']) && is_array($_POST['col-filter']) ? array_keys( $_POST['col-filter'] ) : array();
			$fields  = isset($_POST['fld-filter']) && is_array($_POST['fld-filter']) ? array_keys( $_POST['fld-filter'] ) : array();

			$thumb_main = isset($_POST['size-img']['img_main']) ? $_POST['size-img']['img_main'] : '';
			$thumb_second = isset($_POST['size-img']['img_second']) ? $_POST['size-img']['img_second'] : '';
		}
	}

	define('ADMIN_PAGE_TITLE', _('Choix des langues') . ' - ' . _('Export des traductions') . ' - ' . _('Catalogue'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<p class="notice"><?php echo _("Vous pouvez choisir quelles seront les langues présentes dans l'export des chaînes à traduire"); ?></p>
	
	<table id="table-languages">
		<caption><?php echo _("Langues à exporter"); ?></caption>
		<thead>
			<tr>
				<th id="lng-code" class="col20px"><input type="checkbox" name="checkall" id="checkall" class="riacheckall" /></th>
				<th id="lng-name"><?php echo _("Langue"); ?></th>
			</tr>
		</thead>
		<tbody><?php
			foreach( $config['i18n_lng_used'] as $lng ){
				print '
					<tr>
						<td headers="lng-code">
							<input type="checkbox" name="lng[]" value="'.htmlspecialchars( $lng ).'" />
						</td>
						<td headers="lng-name">
							'.htmlspecialchars( i18n_languages_get_name($lng) ).'
						</td>
					</tr>
				';
			}
		?></tbody>
		<tfoot>
			<tr>
				<td colspan="2" class="right">
					<input type="button" name="export" id="export-translates" value="<?php echo _("Exporter"); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
<?php
	require_once('admin/skin/footer.inc.php');
?>

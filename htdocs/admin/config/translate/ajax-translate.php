<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_TRANSLATE');

	require_once('systems.inc.php');
	
	header("Content-Type: application/xml");
	
	if( isset($_POST['is_not_tsl']) ){ 
		$page = isset($_POST['page']) ? $_POST['page'] : 1;
		$lng = isset($_POST['lng']) ? $_POST['lng'] : '';
		$context = isset($_POST['context']) ? $_POST['context'] : '';
		$filter = isset($_POST['filter']) ? $_POST['filter'] : '';
		$is_not_tsl = isset($_POST['is_not_tsl']) && $_POST['is_not_tsl']==1 ? false : null; 
		
		// Récupère les traductions
		$rtsl = i18n_translates_get( $lng, $context, '', $is_not_tsl, $filter );
		
		$xml = '<success>';
		
		if( !$rtsl || !ria_mysql_num_rows($rtsl) ){
			$xml .= '<notranslation />';
			$xml .= '<nbpage nb="1" />';
			$xml .= '<nbresult nbr="0" />';
		} else {
			$xml .= '<nbpage nb="'.ceil(ria_mysql_num_rows($rtsl)/25).'" />';
			$xml .= '<nbresult nbr="'.ria_mysql_num_rows($rtsl).'" />';
			$xml .= '<usecontext use="'.( $context!='' ? 1 : 0 ).'" />';
			
			if( $page>1 && ($page-1)*25 < ria_mysql_num_rows($rtsl) )
				ria_mysql_data_seek( $rtsl, ($page-1)*25 );
			
			$count = 0;
			while( $tsl = ria_mysql_fetch_array($rtsl) ){
				if( $count>= 25 ) break;
				$xml .= '
					<translation
						lng="'.addslashes( $lng ).'"
						md5="'.addslashes( $tsl['md5'] ).'"
						original="'.htmlspecialchars($tsl['original']).'"
						code="'.addslashes( $tsl['code'] ).'"
						context="'.htmlspecialchars($tsl['context']).'"
					>'.htmlspecialchars($tsl['translation']).'</translation>
				';
				$count++;
			}
		}
		$xml .= '</success>';
		print $xml;
		
	} else {
		if( isset($_POST['md5'], $_POST['lng'], $_POST['context'], $_POST['tsl']) ){ 
			if( !i18n_translates_update( $_POST['lng'], $_POST['md5'], urldecode($_POST['tsl']), $_POST['context'] ) )
				print '<result type="0"><msg><![CDATA[]]></msg>\n</result>';
			else
				print '<result type="1"><msg><![CDATA[]]></msg>\n</result>';
		} else { print '<result type="0"><msg><![CDATA[]]></msg>\n</result>'; }
	}


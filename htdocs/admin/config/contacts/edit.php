<?php
	require_once('cnt.contacts.inc.php');
	require_once('cnt.types.inc.php');
	unset($error);

	$message_error = false;

	//Vérifie que l'utilisateur a bien accès à cette page
	if( isset($_GET['cnt']) && $_GET['cnt']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_VIEW');
	}elseif( !isset($_GET['cnt']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_ADD');
	}

	// Vérifie que l'utilisateur a bien le droit de modifier un contact propriétaire
	if( isset($_POST['save']) && isset($_GET['cnt']) && $_GET['cnt']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_EDIT');
	}

	// Vérifie l'existance du type de contact (mode édition uniquement)
	if( isset($_GET['cnt']) && $_GET['cnt']!=0 && !cnt_contacts_exists($_GET['cnt']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		cnt_contacts_del($_GET['cnt']);
		header('Location: index.php');
		exit;
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		// Vérifie que toutes les informations obligatoires sont disponibles
		if( !isset($_POST['types']) ) $_POST['types'] = array();
		if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname']) || !trim($_POST['firstname']) || !trim($_POST['lastname']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une * sont obligatoires.");
		}elseif( $_GET['cnt']==0 ){
			$_GET['cnt'] = cnt_contacts_add($_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['email'],$_POST['phone'],$_POST['fax'],$_POST['func']);
			if( !$_GET['cnt'] ){
				$error = 1; // Utilise le message par défaut
			}else{
				cnt_contacts_types_set( $_GET['cnt'], $_POST['types'] );
			}
		}elseif( $_GET['cnt']>0 ){
			if( !cnt_contacts_update($_GET['cnt'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['email'],$_POST['phone'],$_POST['fax'],$_POST['func']) ){
				$error = 1; // Utilise le message par défaut
			}else{
				cnt_contacts_types_set( $_GET['cnt'], $_POST['types'] );
			}
		}

		if( !isset($error) && isset($_FILES['image']) && $_FILES['image']['error']!=UPLOAD_ERR_NO_FILE ){
			if( !cnt_contacts_image_upload($_GET['cnt'],'image') )
				$error = 1;

		}

		if( isset($error) )
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du contact.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		else
			header('Location: index.php');
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Contacts Propriétaire') );

	define('ADMIN_PAGE_TITLE', _('Contacts Propriétaire') . ' - ' . _('Ajouter un contact'));
	require_once('admin/skin/header.inc.php');
?>


<?php
	$cnt = array('id'=>0,'title_id'=>'','title_name'=>'','firstname'=>'','lastname'=>'','email'=>'','phone'=>'','fax'=>'','func'=>'','img_id'=>'');
	if( isset($_GET['cnt']) && $_GET['cnt']>0 )
		$cnt = ria_mysql_fetch_array(cnt_contacts_get($_GET['cnt']));

	if( isset($error) )
		$message_error = '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
?>

<h2>
<?php if((isset($cnt['firstname']) && $cnt['firstname'] != '' ) && (isset($cnt['lastname']) && $cnt['lastname'] != '')) {
	echo 'Modification du contact ' . htmlspecialchars($cnt['firstname']) . ' ' . htmlspecialchars($cnt['lastname']);
} else {
	echo _('Ajouter un contact');
} ?>
</h2>

<?php echo $message_error; ?>

<form action="edit.php?cnt=<?php print $cnt['id']; ?>" enctype="multipart/form-data" method="post">
<table>
	<caption><?php printf(_('Informations générales du contact %s'), htmlspecialchars($cnt['firstname'].' '.$cnt['lastname'])); ?></caption>
	<tbody>
		<tr>
			<td><label for="title"><span class="mandatory">*</span> <?php print _('Civilité :'); ?></label></td>
			<td><?php
				$titles = gu_titles_get();
				while( $t = ria_mysql_fetch_array($titles) ){
					print '<input type="radio" class="radio" name="title" id="title-'.$t['id'].'" value="'.$t['id'].'" '.( $t['id']==$cnt['title_id'] ? ' checked="checked"':'' ).' /> ';
					print '<label for="title-'.$t['id'].'">'.$t['name'].'</label><br />';
				}
			?></td>
		</tr>
		<tr>
			<td><label for="firstname"><span class="mandatory">*</span> <?php print _('Prénom :'); ?></label></td>
			<td><input type="text" name="firstname" id="firstname" value="<?php print htmlspecialchars($cnt['firstname']); ?>" maxlength="45" /></td>
		</tr>
		<tr>
			<td><label for="lastname"><span class="mandatory">*</span> <?php print _('Nom de famille :'); ?></label></td>
			<td><input type="text" name="lastname" id="lastname" value="<?php print htmlspecialchars($cnt['lastname']); ?>" maxlength="45" /></td>
		</tr>
		<tr>
			<td><label for="email"><?php print _('Adresse email :'); ?></label></td>
			<td><input type="email" name="email" id="email" value="<?php print htmlspecialchars($cnt['email']); ?>" maxlength="75" /></td>
		</tr>
		<tr>
			<td><label for="phone"><?php print _('Numéro de téléphone :'); ?></label></td>
			<td><input type="text" name="phone" id="phone" value="<?php print htmlspecialchars($cnt['phone']); ?>" maxlength="20" /></td>
		</tr>
		<tr>
			<td><label for="fax"><?php print _('Numéro de fax :'); ?></label></td>
			<td><input type="text" name="fax" id="fax" value="<?php print htmlspecialchars($cnt['fax']); ?>" maxlength="20" /></td>
		</tr>
		<tr>
			<td><label for="func"><?php print _('Fonction :'); ?></label></td>
			<td><input type="text" name="func" id="func" value="<?php print htmlspecialchars($cnt['func']); ?>" maxlength="40" /></td>
		</tr>
		<tr>
			<td><label><?php print _('Type(s) de contact :'); ?></label></td>
			<td class="padding-bottom-10">
				<?php
					$selected = isset($_GET['type']) && is_numeric($_GET['type']) ? array($_GET['type']) : array();
					if( $cnt['id']>0 ){
						$rsel = cnt_contacts_types_get( $cnt['id'] );
						while( $r = ria_mysql_fetch_array($rsel) )
							$selected[] = $r['id'];
					}

					$types = cnt_types_get();
					while( $t = ria_mysql_fetch_array($types) ){
						print '<input type="checkbox" class="checkbox" name="types[]" id="type-'.$t['id'].'" value="'.$t['id'].'" '.( array_search($t['id'],$selected)!==false ? ' checked="checked"':'' ).' /> ';
						print '<label for="type-'.$t['id'].'">'.htmlspecialchars($t['name']).'</label><br />';
					}
				?>
			</td>
		</tr>
		<tr><th colspan="2"><?php print _('Image associée'); ?></th></tr>
		<tr>
			<td><label for="image"><?php print $cnt['img_id'] ? _('Remplacer l\'image par :') : _('Ajouter une image :'); ?></label></td>
			<td><input type="file" name="image" id="image" /></td>
		</tr>
		<tr>
			<td><?php print _('Image associée :'); ?></td>
			<td>
				<?php
					if( !$cnt['img_id'] ){
						print _('Aucune image associée');
					}else{
						$size = $config['img_sizes']['medium'];
						print '	<div class="preview">
									<img alt="image contact" src="'.$config['img_url'].'/'.$size['dir'].'/'.$cnt['img_id'].'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />
								</div>';
					}
				?>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr><td colspan="2">
			<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
			<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return cancelEdit()" />
			<?php if( $cnt['id'] && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_DEL') ){ ?>
			<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" onclick="return confirmDel();" />
			<?php } ?>
		</td></tr>
	</tfoot>
</table>
</form>
<?php
// Disable tous les champs/boutons si on accède à cette page en lecture seul
if( isset($_GET['cnt']) && $_GET['cnt'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_EDIT') ){ ?>
	<script>
	$(document).ready(function(){
		$('table').find('input, select, textarea').attr('disabled', 'disabled');
	});
	</script>
<?php }

	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file index.php
	 *	Cette page affiche la liste des types de contacts propriétaire
	 */

	require_once('cnt.types.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_TYPE');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		/* Suppression d'un ou plusieurs types */
		if( isset($_POST['type']) && is_array($_POST['type']) ){
			foreach( $_POST['type'] as $p ){
				cnt_types_del($p);
			}
			header('Location: index.php');
			exit;
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Contacts Propriétaire'), '/admin/config/contacts/index.php' )
		->push( _('Types de contacts') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Types de contacts') . ' - ' . _('Contacts') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Charge la liste des types de contacts
	$types = cnt_types_get();
	$count = ria_mysql_num_rows($types);

?>
<h2><?php echo _('Types de contacts'); ?> (<?php print ria_number_format($count) ?>)</h2>

<form action="index.php" method="post">
<table id="table-config-type-contact" class="checklist">
	<thead>
		<tr>
			<th id="type-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this);"></th>
			<th id="type-name"><?php echo _('Désignation'); ?></th>
			<th id="type-desc"><?php echo _('Description'); ?></th>
			<th id="type-cnt" class="align-right"><?php echo _('Contacts'); ?></th>
		</tr>
	</thead>
	<?php

		// Calcule le nombre de pages
		$pages = ceil($count / 25);
		if( $pages==0 ) $pages = 1;

		// Détermine la page en cours de consultation
		$page = 1;
		if( isset($_GET['page']) && is_numeric($_GET['page']) ){
			if( $_GET['page']>0 && $_GET['page']<=$pages )
				$page = $_GET['page'];
		}

		// Détermine les limites inférieures et supérieures pour l'affichage des pages
		$pmin = $page-5;
		if( $pmin<1 )
			$pmin = 1;
		$pmax = $pmin+9;
		if( $pmax>$pages ){
			$pmax = $pages;
		}

	?>

	<tbody>
		<?php
			if( $count==0 ){
				print '<tr><td colspan="4">' . _('Aucun type de contacts') . '</td></tr>';
			}else{
				ria_mysql_data_seek( $types, ($page-1)*25 );
				$lcount = 0;
				while( ($r = ria_mysql_fetch_array($types)) && $lcount<25 ){
					print '	<tr>
								<td headers="type-sel"><input type="checkbox" class="checkbox" name="type[]" value="'.$r['id'].'" /></td>';
					if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_TYPE_EDIT') ){
						print '	<td headers="type-name"><a href="edit.php?type='.$r['id'].'&amp;page='.$page.'" title="' . _("Afficher la fiche de ce type de contact") . '">'.htmlspecialchars($r['name']).'</a></td>';
					}else{
						print '	<td headers="type-name">'.htmlspecialchars($r['name']).'</td>';
					}
					if( strlen($r['desc'])>255 ) $r['desc'] = substr($r['desc'],0,252).'...';
					print '		<td headers="type-desc">'.htmlspecialchars($r['desc']).'</td>
								<td headers="type-cnt" class="align-right">'.( $r['contacts']>0 ? '<a href="/admin/config/contacts/index.php?type='.$r['id'].'">'.$r['contacts'].'</a>' : '0' ).'</td>
							</tr>';
					$lcount++;
				}
			}
		?>
	</tbody>
	<tfoot>
		<?php if( $pages>1 ){ ?>
		<tr>
			<td colspan="2" class="page">Page <?php print $page ?>/<?php print $pages; ?></td>
			<td class="pages" colspan="2">
				<?php
					if( $pages>1 ){
						if( $page>1 )
							print '<a href="index.php?page='.($page-1).'">&laquo; ' . _("Page précédente") .'</a> | ';
						for( $i=$pmin; $i<=$pmax; $i++ ){
							if( $i==$page )
								print '<b>'.$page.'</b>';
							else
								print '<a href="index.php?page='.$i.'">'.$i.'</a>';
							if( $i<$pmax )
								print ' | ';
						}
						if( $page<$pages )
							print ' | <a href="index.php?page='.($page+1).'">' . _("Page suivante") . ' &raquo;</a>';
					}
				?>
			</td>
		</tr>
		<?php } ?>
		<tr><td colspan="2" class="align-left">
			<?php if( $count && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_TYPE_DEL')){ ?>
			<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return confirmDelList()" />
			<?php } ?>
		</td><td colspan="2">
			<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_TYPE_ADD') ){ ?>
			<input type="submit" name="add" value="Ajouter" />
			<?php } ?>
		</td></tr>
	</tfoot>
</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
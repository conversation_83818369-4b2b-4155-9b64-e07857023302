<?php

	/**	\file edit.php
	 *
	 * 	Cette page permet la création et la modification de types de contacts
	 *
	 */

	require_once('cnt.contacts.inc.php');
	require_once('cnt.types.inc.php');
	unset($error);

	// Vérifie que l'utilisateur en cours à bien accès à cette page
	if( isset($_GET['type']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_TYPE_EDIT');
	}elseif( !isset($_GET['type']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT_TYPE_ADD');
	}

	// Vérifie l'existance du type de contact (mode édition uniquement)
	if( isset($_GET['type']) && $_GET['type']!=0 && !cnt_types_exists($_GET['type']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		doc_types_del($_GET['type']);
		header('Location: index.php');
		exit;
	}

	// Suppression
	if( isset($_POST['del']) ){
		/* Suppression d'un ou plusieurs contacts */
		if( isset($_POST['cnt']) && is_array($_POST['cnt']) ){
			foreach( $_POST['cnt'] as $p ){
				cnt_contacts_del($p);
			}
			header('Location: /admin/config/contacts/types/edit.php?type='.$_POST['type']);
			exit;
		}
	}

	if( isset($_POST['add'], $_POST['type']) ){
		if( is_numeric($_POST['type']) ){
			header('Location: /admin/config/contacts/edit.php?type='.$_POST['type']);
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		// Vérifie que toutes les informations obligatoires sont disponibles
		if( !isset($_POST['name'],$_POST['desc']) || !trim($_POST['name']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une * sont obligatoires.");
		}elseif( $_GET['type']==0 ){
			$_GET['type'] = cnt_types_add($_POST['name'],$_POST['desc']);
			if( !$_GET['type'] )
				$error = 1; // Utilise le message par défaut
		}elseif( $_GET['type']>0 ){
			if( !cnt_types_update($_GET['type'],$_POST['name'],$_POST['desc']) )
				$error = 1; // Utilise le message par défaut
		}

		if( isset($error) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du type de contact.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			header('Location: index.php');
		}
	}

	$type = array('id'=>0,'name'=>'','desc'=>'');
	if( isset($_GET['type']) && $_GET['type']>0 ){
		$type = ria_mysql_fetch_array(cnt_types_get($_GET['type']));
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Contacts Propriétaire'), '/admin/config/contacts/index.php' )
		->push( _('Types de contacts'), '/admin/config/contacts/types/index.php' )
		->push( $type['id'] ? $type['name'] : 'Nouveau type' );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Types de contacts') . ' - ' . _('Contacts') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Types de contacts'); ?></h2>

<?php

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}
?>

<form action="edit.php?type=<?php print $type['id']; ?>" method="post">
	<table>
		<caption><?php printf(_('Type %s'), htmlspecialchars($type['name'])); ?></caption>
		<tbody>
			<tr>
				<td><label for="name"><span class="mandatory">*</span> <?php print _('Désignation :'); ?></label></td>
				<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($type['name']); ?>" maxlength="45" /></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Description :'); ?></label></td>
				<td><textarea name="desc" id="desc" rows="5" cols="50"><?php print htmlspecialchars($type['desc']); ?></textarea></td>
			</tr>
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
				<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return cancelEdit()" />
				<?php if( $type['id'] && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_TYPE_DEL')){ ?>
				<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" onclick="return confirmDel();" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
</form>

<?php
	if( $type['id']>0 ){
		require_once('cnt.contacts.inc.php');
		$rcnt = cnt_contacts_get( 0, $type['id'] );
?>
		<form action="/admin/config/contacts/types/edit.php?type=<?php print $type['id']; ?>" method="post">
			<table id="table-conf-contact">
				<caption><?php print _('Contacts'); ?></caption>
				<thead>
					<tr>
						<th id="cnt-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
						<th id="cnt-name"><?php print _('Nom et prénom'); ?></th>
						<th id="cnt-email"><?php print _('Email'); ?></th>
						<th id="cnt-phone"><?php print _('Téléphone'); ?></th>
						<th id="cnt-fax"><?php print _('Fax'); ?></th>
						<th id="cnt-types"><?php print _('Types'); ?></th>
					</tr>
				</thead><tbody><?php
					if( !$rcnt || !ria_mysql_num_rows($rcnt) ){
						print '<tr><td colspan="6">'._('Aucun contacts').'</td></tr>';
					}else{
						while( $r = ria_mysql_fetch_array($rcnt) ){
							print '	<tr><td headers="cnt-sel"><input type="checkbox" class="checkbox" name="cnt[]" value="'.$r['id'].'" /></td>';
							if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_VIEW') ){
								print '	<td headers="cnt-name"><a href="/admin/config/contacts/edit.php?cnt='.$r['id'].'" title="'._('Afficher la fiche de ce contact').'">'.htmlspecialchars($r['title_name'].' '.$r['firstname'].' '.$r['lastname']).'</a></td>';
							}else{
								print '	<td headers="cnt-name">'.htmlspecialchars($r['title_name'].' '.$r['firstname'].' '.$r['lastname']).'</td>';
							}
							print '		<td headers="cnt-email">'.$r['email'].'</td>
										<td headers="cnt-phone">'.str_replace( ' ', '&nbsp;', $r['phone'] ).'</td>
										<td headers="cnt-fax">'.str_replace( ' ', '&nbsp;', $r['fax'] ).'</td>
										<td headers="cnt-types">';
							$types = cnt_contacts_types_get( $r['id'] );
							$ar_types = array();
							while( $t = ria_mysql_fetch_array($types) )
								$ar_types[$t['id']] = htmlspecialchars($t['name']);
							print implode(', ', $ar_types );
							print '		</td>
									</tr>';
						}
					}
				?>
				</tbody>
				<tfoot class="foot-grey">
					<tr>
						<td colspan="2">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_DEL') ) { ?>
							<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return window.confirm('<?php print _('Vous êtes sur le point de supprimer un ou plusieurs contacts, êtes vous sûr(e) de vouloir continuer ?'); ?>');" />
							<?php } ?>
						</td>
						<td colspan="4">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_ADD') ) { ?>
							<input type="hidden" name="type" id="type" value="<?php print $type['id']; ?>" />
							<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
							<?php } ?>
						</td>
					</tr>
				</tfoot>
			</table>
		</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
?>
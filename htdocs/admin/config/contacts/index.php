<?php

	/**	\file index.php
	 *	Cette page affiche la liste des contacts et propose les fonctionnalités suivantes :
	 *	- Ajout d'un contact
	 *	- Accès à la fiche du contact pour modification
	 *	- Suppression d'un ou plusieurs contacts
	 */

	require_once('cnt.contacts.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CONTACT');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		/* Suppression d'un ou plusieurs contacts */
		if( isset($_POST['cnt']) && is_array($_POST['cnt']) ){
			foreach( $_POST['cnt'] as $p ){
				cnt_contacts_del($p);
			}
			header('Location: index.php');
			exit;
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Contacts Propriétaire') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Contacts Propriétaire') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Charge la liste des contacts
	$type = 0;
	if( isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type']>0 ){
		$type = $_GET['type'];
	}

	$contacts = cnt_contacts_get( 0, $type );
	$count = $contacts ? ria_mysql_num_rows($contacts) : 0;

?>
<h2><?php print _('Contacts Propriétaire'); ?> (<?php print ria_number_format($count) ?>)</h2>

<form action="index.php" method="post">
	<table id="table-config-contact" class="checklist">
		<thead>
			<tr>
				<th id="cnt-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="cnt-name"><?php print _('Nom et prénom'); ?></th>
				<th id="cnt-email"><?php print _('Email'); ?></th>
				<th id="cnt-phone"><?php print _('Téléphone'); ?></th>
				<th id="cnt-fax"><?php print _('Fax'); ?></th>
				<th id="cnt-types"><?php print _('Types'); ?></th>
			</tr>
		</thead>
		<?php


			// Calcule le nombre de pages
			$pages = ceil($count / 25);
			if( $pages==0 ) $pages = 1;

			// Détermine la page en cours de consultation
			$page = 1;
			if( isset($_GET['page']) && is_numeric($_GET['page']) ){
				if( $_GET['page']>0 && $_GET['page']<=$pages )
					$page = $_GET['page'];
			}

			// Détermine les limites inférieures et supérieures pour l'affichage des pages
			$pmin = $page-5;
			if( $pmin<1 )
				$pmin = 1;
			$pmax = $pmin+9;
			if( $pmax>$pages ){
				$pmax = $pages;
			} ?>

		<tbody>
			<?php
				if( $count==0 ){
					print '<tr><td colspan="6">'._('Aucun contacts').'</td></tr>';
				}else{
					ria_mysql_data_seek( $contacts, ($page-1)*25 );
					$lcount = 0;
					while( ($r = ria_mysql_fetch_array($contacts)) && $lcount<25 ){
						print '	<tr>
									<td headers="cnt-sel">
										<input type="checkbox" class="checkbox" name="cnt[]" value="'.$r['id'].'" />
									</td>';
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_VIEW') ){
							print '	<td headers="cnt-name"><a href="edit.php?cnt='.$r['id'].'&amp;page='.$page.'" title="'._('Afficher la fiche de ce contact').'">'.htmlspecialchars($r['title_name'].' '.$r['firstname'].' '.$r['lastname']).'</a></td>';
						}else{
							print '	<td headers="cnt-name">'.htmlspecialchars($r['title_name'].' '.$r['firstname'].' '.$r['lastname']).'</td>';
						}
						print '		<td headers="cnt-email">'.$r['email'].'</td>
									<td headers="cnt-phone">'.str_replace( ' ', '&nbsp;', $r['phone'] ).'</td>
									<td headers="cnt-fax">'.str_replace( ' ', '&nbsp;', $r['fax'] ).'</td>
									<td headers="cnt-types">';
						$types = cnt_contacts_types_get( $r['id'] );
						$ar_types = array();
						while( $t = ria_mysql_fetch_array($types) )
							$ar_types[] = htmlspecialchars($t['name']);
						print implode(', ', $ar_types );
						print '		</td>';
						print '	</tr>';
						$lcount++;
					}
				}
			?>
		</tbody>
	<?php
	if( ($count && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_DEL')) || gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_ADD') ){ ?>
		<tfoot>
			<?php if( $pages>1 ){ ?>
			<tr>
				<td colspan="2" class="page"><?php printf(_('Page %d/%d'), $page, $pages); ?></td>
				<td colspan="4" class="pages">
					<?php
						if( $pages>1 ){
							if( $page>1 )
								print '<a href="index.php?page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page )
									print '<b>'.$page.'</b>';
								else
									print '<a href="index.php?page='.$i.'">'.$i.'</a>';
								if( $i<$pmax )
									print ' | ';
							}
							if( $page<$pages )
								print ' | <a href="index.php?page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
						}
					?>
				</td>
			</tr>
			<?php }
				if( ($count && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_DEL')) || gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_ADD') ) { ?>
			<tr>
				<td colspan="2" class="align-left">
				<?php if( $count && gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_DEL') ){ ?>
				<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return confirmDelList()" />
				<?php } ?>
				</td>
				<td colspan="4">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_CONTACT_ADD') ){ ?>
				<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
				<?php } ?>
				</td>
			</tr>
			<?php } ?>
		</tfoot>
		<?php } ?>
	</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
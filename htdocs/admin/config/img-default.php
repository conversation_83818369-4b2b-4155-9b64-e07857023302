<?php

	/**	\file img-default.php
	 *	Cette page permet la consultation et la mise à jour de l'image par défaut qui apparaîtra dans les listes de produits
	 *	lorsque la photographie du produit n'est pas disponible.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_IMG');

	if( isset($_GET['wst_id']) && is_numeric($_GET['wst_id']) && $_GET['wst_id'] >= 0 ){
		$_SESSION['websitepicker'] = $_GET['wst_id'];
	}

	$website = 0;
	if( isset($_SESSION['websitepicker']) ){
		$website = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	$img_default = cfg_overrides_get_value( 'default_image', $website );
	
	// Bouton Enregistrer
	if( isset($_POST['save-new-img']) ){
		if( !isset($_FILES) || !is_array($_FILES) || !sizeof($_FILES) ){
			$error = _("Merci de sélectionner une nouvelle image à utiliser par défaut.");
		}else{
			$img_id = img_images_upload( 'new-img' );
			if( !is_numeric($img_id) || $img_id <= 0 || !cfg_overrides_set_value( 'default_image', $img_id, $website ) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image par défault. Merci de prendre contact pour nous signaler ce problème.");
			}
		}

		if( !isset($error) ){
			$_SESSION['save-new-img-ok'] = true;
			header('Location: /admin/config/img-default.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Image par défaut').' - '._('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("Image par défaut"); ?></h2>

<?php
	print view_websites_selector( $website, true, 'riapicker', false, 'Tous les sites', false, false );
?>

<div class="notice"><?php echo _("Vous pouvez ici définir une image qui sera utilisée par défaut dans le cas où un contenu n'aurait pas d'image."); ?></div>

<dl>
	<dt><?php echo _("Image actuellement utilisée"); ?></dt>
	<dd><?php
		if( is_numeric($img_default) && $img_default > 0 ){
			$size = $config['img_sizes']['medium'];

			print '
				<img src="/images/products/'.$size['dir'].'/'.$img_default.'.'.$size['format'].'" width="'.$size['width'].'" height="'.$size['height'].'" alt="Image par défaut" />
			';
		}else{
			print '
				<img src="/admin/images/default.jpg" class="margin-bottom-10" width="150" height="150" alt="Image par défaut" />
				<p class="notice">' . _("Aucune image n'est pour le moment définie.") . '</p>
			';
		}
	?></dd>
</dl>

<dl>
	<dt><?php echo _('Définir une nouvelle image'); ?></dt>
	<dd>
		<?php
			if( isset($error) ){
				print '<div class="error">'.nl2br( $error ).'</div>';
			}elseif( isset($_SESSION['save-new-img-ok']) ){
				print '<div class="success">' . _("L'image par défaut a bien été enregistrée.") . '</div>';
				unset( $_SESSION['save-new-img-ok'] );
			}
		?>

		<form action="/admin/config/img-default.php" method="post" enctype="multipart/form-data">
			<input type="file" name="new-img" value="" />
			<input type="submit" name="save-new-img" value="<?php echo _("Enregistrer"); ?>" />
		</form>
	</dd>
</dl>

<?php
	require_once('admin/skin/footer.inc.php');
?>
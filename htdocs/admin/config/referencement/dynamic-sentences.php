<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF');

	require_once( 'view.translate.inc.php' );
	require_once( 'websites.inc.php' );

    $cls = isset($_GET['cls']) ? $_GET['cls'] : 0;
    $tag = isset($_GET['tag']) ? $_GET['tag'] : '';

    if ($cls === 0){
        $cls = isset($_SESSION['sentence_cls_id']) ? $_SESSION['sentence_cls_id'] : 0;
        unset($_SESSION['sentence_cls_id']);
    }
    if ($tag === ''){
        $tag = isset($_SESSION['sentence_tag']) ? $_SESSION['sentence_tag'] : '';
        unset($_SESSION['sentence_tag']);
    }

    if (isset($_POST['save'])){
        $cls = $_POST['class-selector'];
        $tag = $_POST['tag-selector'] == 1 ? 'title' : 'desc';
        $content = trim($_POST['sentence-editor']);
        $lng_code = $_POST['lng_code'];
        if ($content != '' ){ //On vérifie que le champ textuel est rempli
            $constants = seo_templates_constants_get_by_class($cls);
            foreach($constants as $key => $constant){
                if ($constant['fld_id'] != null){
                    $content = str_replace('['.$constant['name'].']', 'fld-'.$constant['fld_id'], $content);
                }
            }
            if (!seo_templates_exists(0, $cls, $tag, $content, $lng_code)){ //On teste l'existance en base, afin de ne pas avoir de doublons
                if (isset($_POST['seo_id'])){ //Si on modifie une phrase
                    if (!seo_templates_update($_POST['seo_id'], $content)){
                        $error = _("Une erreur s'est produite lors de la mise à jour de la phrase.")."\n"._("Veuillez réessayer ou prendre contact pour signaler l'erreur.");
                    }
                } else { //Si on ajoute une nouvelle phrase
                    if (!seo_templates_add($cls, $tag, $content, $lng_code)){
                        $error = _("Une erreur s'est produite lors de l'enregistrement de la phrase.")."\n"._("Veuillez réessayer ou prendre contact pour signaler l'erreur.");
                    }
                }
            } else {
                $error = _("La phrase que vous souhaitez enregistrer existe déjà.")."\n"._("Elle ne doit pas être identique à une autre déjà existante."); 
            }
        } else {
            $error = _("La phrase que vous souhaitez enregistrer est vide.")."\n"._("Veuillez renseigner le champ avant d'enregistrer");
        }
        if (!isset($error)){
            header("Location: dynamic-sentences.php?cls=".$cls."&tag=".$tag);
            exit;
        }
    }
    
	$lng = view_selected_language();

	define('ADMIN_PAGE_TITLE', _('Phrases dynamiques').' - '._('Référencement').' - '._('Configuration'));
    require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Phrases dynamiques'); ?></h2>
    <?php
        if( isset($error) ){
            print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
        }elseif( isset($success) ){
            print '<div class="error-success">'.nl2br(htmlspecialchars($success)).'</div>';
        }

        $cls_ids = seo_templates_constants_get_classes();

        print view_translate_menu("dynamic-sentences.php", $lng);
    ?>
    <form method="post" action="dynamic-sentences.php">
        <input type="hidden" id="lng_code" name="lng_code" value="<?php print $lng; ?>" />
        <div id="class-select">
            <label for="class-selector"><?php print _('Choix d\'une classe :'); ?></label>
            <select name="class-selector" id="class-selector">
                <option name="class-choice" id="class-choice" value="0"></option>
                <?php 
                    foreach ($cls_ids as $key => $cls_id) {
                        $class_name = fld_classes_get_name($cls_id);
                        print '<option name="class-'.$cls_id.'" value="'.$cls_id.'" '.($cls == $cls_id ? 'selected' : '').'>'._($class_name).'</option>';
                    }
                ?>
            </select>
        </div>
        <div id="tag-select">        
            <label for="tag-selector"><?php print _('Choix du type :'); ?></label>
            <select name="tag-selector" id="tag-selector">
                <option name="tag-choice" id="tag-choice" value="0"></option>
                <option name="title" id="title-tag" <?php ($tag == 'title' ? print 'selected' : '') ?> value="1"><?php print _('Titre'); ?></option>
                <option name="desc" id="desc-tag" <?php ($tag == 'desc' ? print 'selected' : '') ?> value="2"><?php print _('Description'); ?></option>
            </select>
        </div>
        <table id="edition-table">
            <caption><?php print _('Edition des phrases de référencement personnalisé'); ?></caption>
            <tbody>
                <tr>        
                    <td id="sentence">
                        <textarea name="sentence-editor" id="sentence-editor" rows="6"></textarea>
                    </td>
                    <td id="save-column">
                        <input class="action" type="button" id="variable-select" name="select" value="<?php print _('Ajouter'); ?>" title="<?php print _('Ajouter la variable dans la phrase'); ?>" />
                    </td>
                    <td>      
                        <select name="constant-selector" id="constant-selector" size="6"></select>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td id="save-column" colspan="3">
                        <input type="submit" id="sentence-save" name="save" value="<?php print _('Enregister'); ?>" title="<?php print _('Enregistrer la phrase'); ?>" />
                    </td>
                </tr>
            </tfoot>
        </table>
    </form>
<?php
	require_once('admin/skin/footer.inc.php');
?>

<!--
<div class=""> 
    <span class="">
    </span> 
    <span class="" title="Bouton de suppression">
    </span> 
</div>
-->
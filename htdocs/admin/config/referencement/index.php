<?php
	require_once( 'view.translate.inc.php' );
	require_once( 'sitemaps.inc.php' );
	require_once( 'websites.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF');
	
	$website = wst_websites_get();
	
	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'];
	//Bouton Enregistre, onglet référencement
	if( isset($_POST['save-ref']) ){
		$wst = ria_mysql_fetch_array( wst_websites_get($_POST['wst_id']) );
		if ( !isset($_POST['auto-sitemap']) || !in_array($_POST['auto-sitemap'], array(0, 1))){
			$error = _('Certaines informations n\'ont pas été transmises.');
		}
		else{
			if ($config['sitemap_auto'] != $_POST['auto-sitemap']){
				if (! cfg_overrides_set_value('sitemap_auto', $_POST['auto-sitemap'], $config['wst_id'])){
					$error = _('Une erreur s\'est produite lors de l\'enregistrement.');
				}
				else{
					$config['sitemap_auto'] = $_POST['auto-sitemap'];
					if (! sitemaps_reload()) $error = _('Une erreur s\'est produite lors de la mise à jour des sitemaps.');
				}
			}
			if (! isset($error)){
				if( !isset($_POST['tag_title']) || trim($_POST['tag_title'])=='' ){
					$error = " Le titre par défaut du site ne peut être vide. Veuillez renseigner au minimum ce champ.";
				}elseif( !isset($_GET['lng']) || strtolower($_GET['lng'])==$config['i18n_lng'] ){
					$_POST['tag_desc'] = isset($_POST['tag_desc']) ? $_POST['tag_desc'] : '';
					$_POST['keywords'] = isset($_POST['keywords']) ? $_POST['keywords'] : '';
					$_POST['site_desc'] = isset($_POST['site_desc']) ? $_POST['site_desc'] : '';
					if( $_POST['wst_id']>0 && !wst_websites_update_desc($_POST['wst_id'], $_POST['site_desc']) ){
						$error = str_replace("#param[nom_site]#", $wst['name'], _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement du site '#param[nom_site]#'.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur"));
					}
					elseif( !wst_websites_meta_update($_POST['wst_id'], $_POST['tag_title'], $_POST['tag_desc'], $_POST['keywords']) ){
						$error = str_replace("#param[nom_site]#", $wst['name'], _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement du site '#param[nom_site]#'.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur"));
					}
					else{
						$success = str_replace("#param[nom_site]#", $wst['name'], _("L'enregistrement du référencement pour le site '#param[nom_site]#' a bien fonctionné."));
					}
				}elseif( isset($_GET['lng']) && in_array(strtolower($_GET['lng']),$config['i18n_lng_used']) ){
					$values = array(
						_FLD_WST_DESC=>$_POST['site_desc'],
						_FLD_WST_TAG_TITLE=>$_POST['tag_title'],
						_FLD_WST_TAG_DESC=>$_POST['tag_desc'],
						_FLD_WST_TAG_KEYWORDS=>$_POST['keywords']
					);
					if( !fld_translates_add($_POST['wst_id'], $_GET['lng'], $values) ){
						$error = str_replace("#param[nom_site]#", $wst['name'], _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement du site '#param[nom_site]#'.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur"));
					}
					else{
						$success = str_replace("#param[nom_site]#", $wst['name'], _("L'enregistrement du référencement pour le site '#param[nom_site]#' a bien fonctionné."));
					}
				}
			}
		}
	}
	
	// Détermine le site sélectionné.
	if( !isset($error) && isset($_GET['wst'])){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = $config['wst_id'];
		}
		else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
		if (isset($success)){
			$_SESSION['cfg_referencement_success'] = $success;
		}
		header('Location: index.php'.( isset($_GET['lng']) ? '?lng='.$_GET['lng'] : ''));
		exit;
	}
	
	$lng = view_selected_language();
	define('ADMIN_PAGE_TITLE', _('Référencement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Référencement"); ?></h2>
	
		<form id="form-referencement-site" name="referencement-site" action="index.php?lng=<?php print $lng; ?>&amp;wst=<?php print $wst_id ?>" method="post">
		<?php
			// Affiche le menu de langue
			if (isset($_SESSION['cfg_referencement_success'])) {
				$success = $_SESSION['cfg_referencement_success'];
				unset($_SESSION['cfg_referencement_success']);
			}
			
			if( isset($error) ){
				print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
			}elseif( isset($success) ){
				print '<div class="error-success">'.nl2br(htmlspecialchars($success)).'</div>';
			}
		?>
		
		<div class="<?php isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1 ? 'ref-menu2' : 'ref-menu'; ?>">
			<?php
				print view_websites_selector( $wst_id, false, '', true );
				print view_translate_menu( 'index.php?wst='.$wst_id, $lng ); 
			?>
			<div class="clear"></div>
		</div>

		<?php
			// Récupère les informations sur le site sélectionné
			$website = ria_mysql_fetch_array( wst_websites_get($wst_id) );
			
			if( $lng!=$config['i18n_lng'] ){
				$tsk_website = fld_translates_get( CLS_CATEGORY, $website['id'], $lng, $website, array(_FLD_WST_TAG_TITLE=>'site_title', _FLD_WST_TAG_DESC=>'meta_desc', _FLD_WST_TAG_KEYWORDS=>'meta_kwd', _FLD_WST_DESC=>'desc'), true );
				$website['site_title'] = $tsk_website['site_title'];
				$website['desc'] = $tsk_website['desc'];
				$website['meta_desc'] = $tsk_website['meta_desc'];
				$website['meta_kwd'] = $tsk_website['meta_kwd'];
			}
		?>
			<input type="hidden" name="wst_id" id="wst_id" value="<?php print $wst_id; ?>" />
			<table id="table-conf-referencement">
				<caption><?php echo _("Référencement personnalisé du site "); ?>'<?php print htmlspecialchars($website['name']); ?>'</caption>
				<tbody>
					<tr>
						<td><label for="tag_title"><?php print _("Titre du site :"); ?></label><br /><sub><?php echo _("(Balise title)"); ?></sub></td>
						<td><input type="text" name="tag_title" id="tag_title" value="<?php print htmlspecialchars($website['site_title']); ?>" maxlength="100" /></td>
					</tr>
					<tr>
						<td><label for="site_desc"><?php print _("Description du site :"); ?></label><br /><sub><?php echo _("(Il s'agit d'une description de votre site)"); ?></sub></td>
						<td><textarea class="ref-desc" name="site_desc" id="site_desc" cols="40" rows="10"><?php print htmlspecialchars( $website['desc'] ); ?></textarea></td>
					</tr>
					<tr>
						<td><label for="tag_desc"><?php print _('Description :'); ?><br /><sub><?php echo _("(Balise méta-description,"); ?> <br /><?php echo _("156 caractères maximum"); ?> <br /> <br /> <?php echo _("Cette description sera utilisée en priorité pour le référencement)"); ?></sub></label></td>
						<td><textarea class="ref-desc" name="tag_desc" id="tag_desc" cols="40" rows="10"><?php print htmlspecialchars($website['meta_desc']); ?></textarea></td>
					</tr>
				</tbody>
			</table>
			<input type="hidden" name="keywords" value="<?php print htmlspecialchars($website['meta_kwd']); ?>" />

			<h3><?php echo _("Sitemap"); ?></h3>
			<p>
				<?php echo _("La création d'un plan Sitemap permet aux moteurs de recherche Google, Bing et Yahoo! de faciliter l'indexation de votre site dans l'optique d'obtenir un meilleur référencement."); ?>
			</p>
			<p>
				<?php echo _("Souhaitez-vous générer automatiquement le plan Sitemap de votre site ?"); ?>
				<?php
					$options = array(1 => 'Oui', 0 => 'Non');
					foreach ($options as $key => $option){
						print '<input id="auto-sitemap-'.$key.'" type="radio" value="'.$key.'" name="auto-sitemap"'.(($config['sitemap_auto'] == $key) ? ' checked="checked"' : '').' /> <label class="label-option" for="auto-sitemap-'.$key.'">'.$option.'</label>';
					}
				?>
			</p>
			<p>
				<?php echo _("Le fichier \"index\" de votre plan Sitemap sera automatiquement placé à la racine de votre site."); ?>
			</p>
			<p id="custom-p">
				<input type="submit" name="save-ref" value="<?php echo _("Enregistrer"); ?>" />
			</p>
		</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
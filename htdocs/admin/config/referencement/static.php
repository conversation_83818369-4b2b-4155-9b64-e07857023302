<?php

	/**	\static.php
	 *	Cette page affiche la liste des pages statiques, pour permettre la mise à jour de leur référencement personnalisé.
	 */

	require_once( 'view.translate.inc.php' );
	require_once( 'sitemaps.inc.php' );
	require_once( 'websites.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF_STATIC');

	// Bouton Ajouter
	if( isset($_POST['add-tag']) ){
		header('Location: /admin/config/referencement/edit-tag.php');
		exit;
	}

	// Suppression d'une ou plusieurs personnnalisation de référencement de pages statiques
	if( isset($_POST['del-tag']) ){
		if( isset($_POST['stag-id']) && is_array($_POST['stag-id']) ){
			foreach( $_POST['stag-id'] as $tag ){
				if( is_numeric($tag) && $tag>0 ){
					if( !rew_tags_del($tag) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression du réferencement personnalisée d'une page statique.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/config/referencement/static.php');
			exit;
		}
	}

	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'];

	// Détermine le site sélectionné.
	if( !isset($error) && isset($_GET['wst'])){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = $config['wst_id'];
		}
		else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
		if (isset($success)){
			$_SESSION['cfg_referencement_success'] = $success;
		}

		header('Location: static.php'.( isset($_GET['lng']) ? '?lng='.$_GET['lng'] : ''));
		exit;
	}

	$lng = view_selected_language();

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Pages statiques') . ' - ' . _('Référencement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Chargement des données
	$rtag = rew_tags_get( 0, '', $wst_id, $lng );
	$count = ria_mysql_num_rows( $rtag );

?>
	<h2><?php print _("Pages statiques"); ?> (<?php print ria_number_format($count) ?>)</h2>

	<?php
		// Affichage des messages d'erreur et de succès
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}elseif( isset($success) ){
			print '<div class="error-success">'.nl2br(htmlspecialchars($success)).'</div>';
		}
	?>

	<div class="<?php isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1 ? 'ref-menu2' : 'ref-menu'; ?>">
		<?php
			print view_websites_selector( $wst_id, false, '', true );
			print view_translate_menu( 'static.php?wst='.$wst_id, $lng );
		?>
		<div class="clear"></div>
	</div>
	<form name="referencement-site" class="ref-page-static" action="static.php?lng=<?php print $lng; ?>&amp;wst=<?php print $wst_id ?>" method="post">
		<div class="notice"><?php echo _("Vous trouverez ci-dessous un tableau contenant le référencement personnalisé de toutes les pages statiques."); ?></div>
		<table id="conf-stats" class="checklist">
			<thead>
				<tr>
					<th>
						<input type="checkbox" name="checkall" value="" />
					</th>
					<th><?php echo _("Désignation"); ?></th>
				</tr>
			</thead>
			<tbody><?php
				if( !$rtag || !ria_mysql_num_rows($rtag) ){
					print '
						<tr>
							<td colspan="2">' . _("Aucune personnalisation enregistrée sur les pages statiques") . '</td>
						</tr>
					';
				}else{
					while( $tag = ria_mysql_fetch_array($rtag) ){?>
						<tr>
							<td>
								<input type="checkbox" name="stag-id[]" value="<?php print $tag['id']; ?>" />
							</td>
							<td>
								<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_REF_STATIC_EDIT') ){ ?>
								<a href="/admin/config/referencement/edit-tag.php?tag=<?php print $tag['id']; ?>"><?php print htmlspecialchars( $tag['name'] ); ?></a>
								<?php }else{
								print htmlspecialchars( $tag['name'] );
								} ?>
							</td>
						</tr><?php
					}
				}
			?></tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_REF_STATIC_DEL') ){ ?>
						<input type="submit" name="del-tag" class="float-left" value="<?php echo _("Supprimer"); ?>" />
						<?php } ?>

						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_REF_STATIC_ADD') ){ ?>
						<input type="submit" name="add-tag" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter une personnalisation du référencement d'une page statique."); ?>" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
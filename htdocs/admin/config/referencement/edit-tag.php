<?php
	require_once( 'view.translate.inc.php' );
	require_once('rewrite.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['tag']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF_STATIC_EDIT');
	}else{ // !isset($_GET['tag'])
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF_STATIC_ADD');
	}

	// retour sur la liste des personnalisations
	if( isset($_POST['cancel']) ){
		header('Location: /admin/config/referencement/static.php');
		exit;
	}

	// Suppression d'une personnalisation
	if( isset($_POST['del']) ){
		if( isset($_GET['tag']) && is_numeric($_GET['tag']) && $_GET['tag']>0 ){
			if( !rew_tags_del($_GET['tag']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de cettte personnalisation. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/config/referencement/static.php');
			exit;
		}
	}

	// Enregistre une personnalisation de référencement d'une page statique
	if( isset($_POST['save']) ){
		if( !isset($_POST['tag-lang']) ){
			$_POST['tag-lang'] = $config['i18n_lng'];
		}

		if( !isset($_POST['tag-id'], $_POST['tag-name'], $_POST['tag-url'], $_POST['tag-wst'], $_POST['tag-lang'], $_POST['tag-title'], $_POST['tag-desc']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['tag-name'])=='' || trim($_POST['tag-url'])=='' || trim($_POST['tag-title'])=='' || trim($_POST['tag-desc'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		}elseif( !is_numeric($_POST['tag-wst']) || $_POST['tag-wst']<=0 ){
			$error = _("Veuillez sélectionner le site sur lequel se trouve la page.");
		}elseif( trim($_POST['tag-lang'])=='' ){
			$error = _("Veuillez sélectionner la langue du site sur lequel se trouve la page.");
		}else{
			// On récupère l'adresse du site internet choisi
			$rsite_url = cfg_overrides_get( $_POST['tag-wst'], array(), 'site_url' );
			$rsite_url = wst_websites_languages_get( $_POST['tag-wst'], $_POST['tag-lang'] );
			if( !$rsite_url || !ria_mysql_num_rows($rsite_url) ){
				$error = _("Une erreur inattendue s'est produite lors de la récupération de l'adresse du site.");
			}else{
				$site_url = ria_mysql_result( $rsite_url, 0, 'url' );
				$url = str_replace( $site_url, '', $_POST['tag-url'] );

				if( trim($url)=='' || trim($url)=='/' ){
					$error = _("L'url de la page ne peut être vide ou correspondre à l'url de la page d'accueil.");
				}elseif( substr($_POST['tag-url'], 0, 1)!='/' && !strstr($_POST['tag-url'], $site_url) ){
					$error = _("L'url saisie ne semble pas présente sur le site sélectionné.");
				}else{
					if( !is_numeric($_POST['tag-id']) || $_POST['tag-id']<=0 ){
						$_POST['tag-id'] = rew_tags_add( $_POST['tag-url'], $_POST['tag-name'], $_POST['tag-title'], $_POST['tag-desc'], $_POST['tag-wst'], $_POST['tag-lang'] );
						if( $_POST['tag-id']=='-1' ){
							$error = _("L'url saisie n'est pas présente sur le site choisi.");
						}elseif( $_POST['tag-id']=='-2' ){
							$error = _("L'url saisie fait référence à un contenu dont le référencement peut être personnalisé depuis sa fiche.");
						}elseif( !$_POST['tag-id'] ){
							$error = _("Une erreur inattendue s'est produite lors de l'ajout de la personnalisation. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
						}
					}else{
						if( !rew_tags_update($_POST['tag-id'], $_POST['tag-name'], $_POST['tag-title'], $_POST['tag-desc']) ){
							$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la personnalisation. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
						}
					}
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/config/referencement/edit-tag.php?tag='.$_POST['tag-id']);
			exit;
		}
	}

	$lng = view_selected_language();

	$tag = array(
		'id' => isset($_GET['tag']) && is_numeric($_GET['tag']) && $_GET['tag'] ? $_GET['tag'] : 0,
		'name' => '',
		'url' => '',
		'wst_id' => isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'],
		'lng_code' => $lng,
		'tag_title' => '',
		'tag_desc' => ''
	);

	if( $tag['id']>0 ){
		$rtag = rew_tags_get( $tag['id'] );
		
		if( $rtag && ria_mysql_num_rows($rtag) ){
			$tag = ria_mysql_fetch_array( $rtag );
		}
	}

	if( sizeof($_POST)>0 ){
		$tag['name'] = isset($_POST['tag-name']) ? $_POST['tag-name'] : $tag['name'];
		$tag['url'] = isset($_POST['tag-url']) ? $_POST['tag-url'] : $tag['url'];
		$tag['wst_id'] = isset($_POST['tag-wst']) ? $_POST['tag-wst'] : $tag['wst_id'];
		$tag['lng_code'] = isset($_POST['tag-lang']) ? $_POST['tag-lang'] : $tag['lng_code'];
		$tag['tag_title'] = isset($_POST['tag-title']) ? $_POST['tag-title'] : $tag['tag_title'];
		$tag['tag_desc'] = isset($_POST['tag-desc']) ? $_POST['tag-desc'] : $tag['tag_desc'];
	}

	$title = $tag['id']<=0 ? _('Ajout d\'une personnalisation') : _('Edition d\'une personnalisation');

	define('ADMIN_PAGE_TITLE', _($title).' - ' . _('Référencement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars($title).'</h2>
		<p>' . _("Vous pouvez à partir d'ici personnaliser le référencement d'une page statique.") .'</p>
	';
?>
	<form action="/admin/config/referencement/edit-tag.php?tag=<?php print $tag['id']; ?>" method="post">
		<input type="hidden" name="tag-id" value="<?php print $tag['id']; ?>" />

		<?php
			if( isset($error) ){
				print '<div class="error">'.nl2br( $error ).'</div>';
			}
			$rwst = wst_websites_get( 0, false, null, true );

			if( $rwst && ria_mysql_num_rows($rwst)==1 ){
				print '
					<input type="hidden" name="tag-wst" value="'.$config['wst_id'].'" />
				';
			}
		?>

		<table id="table-edit-personnalisation" class="cheklists">
			
			<tbody><?php
				$rlang = wst_websites_languages_get( $tag['wst_id'] );

				print '
					<tr>
						<th colspan="3">' . _("Informations générales") . '</th>
					</tr>
					<tr>
						<td>
							<span class="mandatory">*</span>
							<label for="tag-name">' . _('Désignation :') . '</label>
						</td>
						<td colspan="2">
							<input type="text" name="tag-name" id="tag-name" value="'.htmlspecialchars( $tag['name'] ).'" />
						</td>
					</tr>
					<tr>
				';

				if( $tag['id']>0 ){
					$site_url = '';
					$rurl = wst_websites_languages_get( $tag['wst_id'], $tag['lng_code'] );
					if( $rurl && ria_mysql_num_rows($rurl) ){
						$url = ria_mysql_fetch_array( $rurl );

						$site_url = $url['url'];
					}

					print '
						<td>' . _("Url de la page :") . '</td>
						<td colspan="2">
							<input type="hidden" name="tag-url" id="tag-url" value="'.htmlspecialchars( $tag['url'] ).'" />
					';

					if( trim($site_url)!='' ){
						print '
							<a target="_blank" href="'.$site_url.$tag['url'].'">'.htmlspecialchars( $site_url.$tag['url'] ).'</a>
						';
					}else{
						print '
							'.htmlspecialchars( $tag['url'] ).'
						';
					}

					print '
						</td>
					';
				}else{
					print '
						<td>
							<label for="tag-url"><span class="mandatory">*</span> ' . _("Url de la page :") . '</label>
						</td>
						<td colspan="2">
							<input type="text" name="tag-url" id="tag-url" value="'.htmlspecialchars( $tag['url'] ).'" />
						</td>
					';
				}

				print '
					</tr>
				';

				if( $rwst && ria_mysql_num_rows($rwst)>1 ){
					print '
						<tr>
					';

					if( $tag['id']>0 ){
						print '
							<td>' . _("Site :") . '</td>
							<td colspan="2">
								<input type="hidden" name="tag-wst" value="'.$tag['wst_id'].'" />
								'.htmlspecialchars( wst_websites_get_name($tag['wst_id']) ).'
							</td>
						';
					}else{
						print '
							<td>
								<label for="tag-wst"><span class="mandatory">*</span> ' . _("Site :") . '</label>
							</td>
							<td colspan="2">
								<select name="tag-wst" id="tag-wst">
						';

						while( $wst = ria_mysql_fetch_array($rwst) ){
							$selected = $tag['wst_id']==$wst['id'] ? 'selected="selected"' : '';

							print '
										<option '.$selected.' value="'.$wst['id'].'">'.htmlspecialchars( $wst['name'] ).'</option>
							';
						}

						print '
								</select>
							</td>
						';
					}

					print '
						</tr>
					';
				}

				if( $rlang && ria_mysql_num_rows($rlang)>0 ){
					print '
						<tr>
					';

					if( $tag['id']>0 ){
						print '
							<td>' . _("Langue :") . '</td>
							<td colspan="2">
								<input type="hidden" name="tag-lang" value="'.$tag['lng_code'].'" />
								'.htmlspecialchars( i18n_languages_get_name($tag['lng_code']) ).'
							</td>
						';
					}else{
						print '
							<td>
								<label for="tag-lang"><span class="mandatory">*</span> ' . _("Langue :") . '</label>
							</td>
							<td colspan="2">
								<select name="tag-lang" id="tag-lang">
						';

						$ar_uniq_lang = array();
						while( $lang = ria_mysql_fetch_array($rlang) ){
							if( in_array($lang['lng_code'], $ar_uniq_lang) ){
								continue;
							}

							$selected = $tag['lng_code']==$lang['lng_code'] ? 'selected="selected"' : '';

							print '
										<option '.$selected.' value="'.$lang['lng_code'].'">'.htmlspecialchars( $lang['name'] ).'</option>
							';

							$ar_uniq_lang[] = $lang['lng_code'];
						}

						print '
								</select>
							</td>
						';
					}

					print '
						</tr>
					';
				}

				print '
					<tr>
						<th colspan="3">' . _("Référencement") . '</th>
					</tr>
					<tr>
						<td>
							<label for="tag-title"><span class="mandatory">*</span> ' . _("Titre de la page :") .  '</label>
						</td>
						<td>
							<textarea class="referencing" name="tag-title" id="tag-title">'.htmlspecialchars( $tag['tag_title'] ).'</textarea>
						</td>
						<td>
							<textarea class="referencing readonly" name="tag-title-def" id="tag-title-def" readonly="readonly"></textarea>
						</td>
					</tr>
					<tr>
						<td>
							<label for="tag-desc"><span class="mandatory">*</span> ' . _("Description de la page :") .  '</label>
						</td>
						<td>
							<textarea class="referencing" name="tag-desc" id="tag-desc">'.htmlspecialchars( $tag['tag_desc'] ).'</textarea>
						</td>
						<td>
							<textarea class="referencing readonly" name="tag-desc-def" id="tag-desc-def" readonly="readonly"></textarea>
						</td>
					</tr>
				';
			?></tbody>
			<tfoot>
				<tr>
					<td colspan="3">
						<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_REF_STATIC_DEL') ){ ?>
						<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" />
						<?php } ?>
						<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	print '
		<div class="notice">
			' . _('Si par exemple votre page "Mentions légales" est accessible à l\'url "') . ''.htmlspecialchars( $config['site_url'] ).'/mentions-legales/", ' . _("vous devrez saisir dans le champ \"Url de la page\" :") . '
			<ul>
				<li>' . _("Soit") .' '.htmlspecialchars( $config['site_url'] ).'/mentions-legales/</li>
				<li>' . _("Soit") .' /mentions-legales/</li>
			</ul>
		</div>
	';

	require_once('admin/skin/footer.inc.php');
?>
<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF');

	require_once('websites.inc.php');

	if( isset($_GET['get-lang']) && is_numeric($_GET['get-lang']) && $_GET['get-lang'] ){
		$rlang = wst_websites_languages_get( $_GET['get-lang'] );
		if( $rlang && ria_mysql_num_rows($rlang)>0 ){
			print '
				<select name="tag-lang" id="tag-lang">
			';

			$ar_uniq_lang = array();
			while( $lang = ria_mysql_fetch_array($rlang) ){
				if( in_array($lang['lng_code'], $ar_uniq_lang) ){
					continue;
				}

				print '
					<option value="'.$lang['lng_code'].'">'.htmlspecialchars( $lang['name'] ).'</option>
				';

				$ar_uniq_lang[] = $lang['lng_code'];
			}

			print '
				</select>
			';
		}
	}

	if( isset($_GET['get-ref-default']) && is_numeric($_GET['get-ref-default']) && $_GET['get-ref-default'] ){
		$ar_ref = array( 'title'=>'', 'desc'=>'' );

		$rwebsite = wst_websites_get( $_GET['get-ref-default'] );
		if( $rwebsite && ria_mysql_num_rows($rwebsite) ){
			$website = ria_mysql_fetch_array( $rwebsite );
			
			if( isset($_GET['lang']) && $_GET['lang']!=$config['i18n_lng'] ){
				$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], $_GET['lang'], $website, array(_FLD_WST_TAG_TITLE=>'site_title', _FLD_WST_TAG_DESC=>'meta_desc') );
				$website['site_title'] = $tsk_website['site_title'];
				$website['meta_desc'] = $tsk_website['meta_desc'];
			}

			$ar_ref = array(
				'title' => $website['site_title'],
				'desc'	=> $website['meta_desc']
			);
		}

		print json_encode( $ar_ref );
	}

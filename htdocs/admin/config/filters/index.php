<?php
	
	/**	\file index.php
	 * 
	 * 	Cette page permet la configuration des adresses IP filtrées
	 * 
	 */

	require_once('tnt.filters.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FILTER');

	if( isset($_POST['add']) ){
		header('Location: /admin/config/filters/edit.php?flt=0');
		exit;
	}

	if( isset($_POST['delete']) && is_array($_POST['delete']) && sizeof($_POST['delete'])>0 ){
		foreach( $_POST['delete'] as $del ){
			if( !tnt_filters_del($del) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression du filtre.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/config/filters/index.php');
			exit;
		}
	}

	// Chargement des données
	$rflt = tnt_filters_get();
	$nb_filters = $rflt ? ria_mysql_num_rows($rflt) : 0;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Adresses IP filtrées') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("Adresses IP filtrées"); ?> (<?php print ria_number_format($nb_filters) ?>)</h2>
<p><?php echo _("Veuillez trouver ci-dessous la liste complète des adresses ips automatiquement exclues des statistiques de consultation et de recherche :"); ?></p>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
?>
<form action="/admin/config/filters/index.php" method="post">
    <table id="tb-ip-filtrees">
        <thead>
            <tr>
                <th id="flt-check"><input type="checkbox" name="check-all" id="check-all" value="" onclick="checkAllClick(this)" /></th>
                <th id="flt-name"><?php echo _("Désignation"); ?></th>
            </tr>
        </thead>
        <tbody><?php
            if( $rflt && ria_mysql_num_rows($rflt) ){
                while( $flt = ria_mysql_fetch_array($rflt) ){ ?>
					<tr>
						<td headers="flt-check"><input type="checkbox" name="delete[]" value="<?php print $flt['id']; ?>" /></td>
						<td headers="flt-name">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FILTER_EDIT') ){ ?>
							<a href="/admin/config/filters/edit.php?flt=<?php print $flt['id']; ?>"><?php print htmlspecialchars( $flt['name'] ); ?></a>
							<?php }else{
							print htmlspecialchars( $flt['name'] );
							} ?>
						</td>
					</tr><?php
				}
            } else { ?>
                <tr><td colspan="2"><?php echo _("Aucun filtre enregistré pour le moment"); ?></td></tr><?php
            }
        ?></tbody>
        <tfoot>
            <tr>
                <td colspan="2">
					<?php if( $nb_filters>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FILTER_DEL') ){ ?>
                    <input class="btn-del" type="submit" name="del" value="<?php print _('Supprimer'); ?>" />
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FILTER_ADD') ){ ?>
                    <div class="float-right">
                        <input type="submit" name="add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un nouveau filtre"); ?>" />
                    </div>
					<?php } ?>
                </td>
            </tr>
        </tfoot>
    </table>
    <div class="notice"><?php echo _("Nous vous recommandons d’exclure l’adresse IP de vos locaux professionnels, ainsi que celles du domicile des personnes amenées à travailler régulièrement sur le site. Vous garantissez ainsi que les statistiques ne seront pas influencées par des comportements ne reflétant pas l’utilisation réellement faites par les utilisateurs finaux."); ?></div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
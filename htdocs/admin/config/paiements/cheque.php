<?php
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PAIEMENT_CHEQUE');
	
	$active = cfg_overrides_get_value( 'sys_cheque_notify_active', $config['wst_id'] );
	$time 	= cfg_overrides_get_value( 'sys_cheque_notify_days', $config['wst_id'] );
	
	$active = in_array( $active, array('Oui', 'oui', '1') ) ? true : false;
	$time	= is_numeric($time) && $time > 0 ? $time : 7;

	// enregistrement des configurations, règlement par chèque avec contrôle
	if( isset($_POST['save-paiement']) ){
		$s_active 	= isset($_POST['active']) && $_POST['active'] == '1' ? '1' : '0';
		$s_time 	= isset($_POST['time']) && is_numeric($_POST['time']) && $_POST['time'] > 0 ? $_POST['time'] : 7;

		if( isset($_POST['cheque'], $_POST['cheque-days']) ){
			if( !is_numeric($_POST['cheque-days']) || $_POST['cheque-days']<0 ){
				$error = _("Le nombre de jours à partir duquel le chèque est considéré comme validé doit être un entier supérieur ou égal à 0.");
			} else {
				if( 
					!cfg_overrides_set_value('sys_cheque_actived', $_POST['cheque']) 
					|| !cfg_overrides_set_value('sys_cheque_days', $_POST['cheque-days'])
					|| !cfg_overrides_set_value( 'sys_cheque_notify_active', $s_active, $config['wst_id'] )
					|| !cfg_overrides_set_value( 'sys_cheque_notify_days', $s_time, $config['wst_id'] )
				){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des configurations. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			
			if( !isset($error) ){
				$_SESSION['save-config-cheque'] = true;
				header('Location: /admin/config/paiements/cheque.php');
				exit;
			}
		}
	}
	
	$cheque = false; $days = 0;
	if( $rc = ria_mysql_fetch_array(cfg_overrides_get(0, array(), 'sys_cheque_actived', 0)) ){
		$cheque = $rc['value'];
	}
	
	if( $rc = ria_mysql_fetch_array(cfg_overrides_get(0, array(), 'sys_cheque_days', 0)) ){
		$days = $rc['value'];
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Règlement par chèque') . ' - ' . _('Modes de règlement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _('Règlement par chèque'); ?></h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}elseif( isset($_SESSION['save-config-cheque']) ){
			print '<div class="success">' . _('La configuration du paiement par chèque a bien été enregistrée') . '.</div>';
			unset( $_SESSION['save-config-cheque'] );
		}
	?>
	
	<form action="/admin/config/paiements/cheque.php" method="post">
		<div class="notice"><?php echo _("Vous pouvez activer le règlement par chèque avec un contrôle entre la réception du chèque et le fait que le paiement soit considéré comme validé."); ?></div>
		<dl>
			<dt><?php echo _("Activation"); ?></dt>
			<dd>
				<label for="cheque-y"><?php echo _("Activer le règlement par chèque :"); ?></label>
				<input <?php print $cheque ? 'checked="checked"' : ''; ?> type="radio" name="cheque" id="cheque-y" value="1" />&nbsp;<label for="cheque-y"><?php echo _("Oui"); ?></label>
				<input <?php print !$cheque ? 'checked="checked"' : ''; ?> type="radio" name="cheque" id="cheque-n" value="0" />&nbsp;<label for="cheque-n"><?php echo _("Non"); ?></label>
			</dd>
		</dl>

		<dl>
			<dt><?php echo _("Validation"); ?></dt>
			<dd><?php echo _("Une fois le chèque réceptionné, vous pourrez alors passer la commande à l'état \"Règlement reçu, en attente de validation\". À partir de ce moment, tant que vous n'avez pas choisi de commencer le traitement de la commande, cette dernière restera dans RiaShop. Vous pouvez aussi fixer un délai à partir duquel le chèque est automatiquement considéré commande validé après réception."); ?></dd>
			
			<dd>
				<br /><label for="cheque-days"><?php echo _("Le chèque est considéré comme validé à partir de"); ?> </label>&nbsp;<input class="col-numeric" maxlength="2" size="2" type="text" name="cheque-days" id="cheque-days" value="<?php print $days; ?>" />&nbsp;<?php echo _("jour(s)."); ?>
			</dd>
		</dl>

		<dl>
			<dt><?php echo _("Relance"); ?></dt>
			<dd><?php echo _("Vous pouvez activer l'envoi d'un mail de relance lorsqu'aucun chèque n'a pas encore été réceptionné pour une commande."); ?></dd>
			<dd>
				<br /><label for="active-1"><?php echo _("Activer la relance :"); ?></label>
				<input <?php print $active ? 'checked="checked"' : ''; ?> type="radio" name="active" id="active-1" value="1" /> <label for="active-1"><?php echo _("Oui"); ?></label>
				<input <?php print !$active ? 'checked="checked"' : ''; ?> type="radio" name="active" id="active-0" value="0" /> <label for="active-0"><?php echo _("Non"); ?></label>
			</dd>
			<dd><label for="active-1"><?php echo _("Envoyer la relance"); ?></label>&nbsp;<input class="col-numeric" type="text" size="4" name="time" name="time" value="<?php print $time; ?>" />&nbsp;<?php echo _("jour(s) après la confirmation de commande."); ?></dd>
		</dl>
		
		<div class="ria-admin-ui-actions">
			<input type="submit" name="save-paiement" id="save-paiement" value="<?php echo _("Enregistrer"); ?>" />
		</div>
		
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
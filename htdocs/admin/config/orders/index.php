<?php
	require_once('products.inc.php');
	require_once('websites.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_ORDER');
	
	unset($error);
	
	$rovr = cfg_overrides_get( 0, array(), array('ord_archiving_auto', 'ord_archiving_days') );
	if( $rovr ){
		while( $ovr = ria_mysql_fetch_array($rovr) ){
			$config[ $ovr['code'] ] = $ovr['value'];
		}
	}
	
	// Sauvegarde la configuration
	if( isset($_POST['save']) ){
	
		// Vérification des données saisies dans le formulaire
		if( isset($_POST['archiving_auto'],$_POST['archiving_days']) ){

			if( !is_numeric($_POST['archiving_days']) || $_POST['archiving_days']<=0 || $_POST['archiving_days']>999 ){
				$error = _("La valeur saisie pour le délai d'archivage des commandes est incorrecte.\nCette valeur doit être numérique et comprise entre 1 et 999 jours.");
			}else{
				// Si les données correspondent aux exigences
				if( cfg_overrides_set_value('ord_archiving_days', $_POST['archiving_days'], 0) 
					&& cfg_overrides_set_value('ord_archiving_auto', ($_POST['archiving_auto'] == '1' ? 1 : 0), 0) 
				){
					header('Location: index.php');
					exit;
				}else{
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}
		}
	}
	
	define('ADMIN_PAGE_TITLE', _('Archiver les commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _('Archiver les commandes'); ?></h2>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php" method="post">

		<?php if($config['allow_orders_update_state']){ ?>
			<dl>
				<dd>
					<label for="archiving_auto_1"><?php echo _("Souhaitez vous archiver automatiquement vos commandes ? : "); ?></label>
					<label><input type="radio" name="archiving_auto" id="archiving_auto_1" value="1" <?php print $config['ord_archiving_auto'] == 1 ? 'checked="checked"':''?>/> <?php echo _("Oui"); ?></label>
					<label><input type="radio" name="archiving_auto" id="archiving_auto_0" value="0" <?php print $config['ord_archiving_auto'] == 0 ? 'checked="checked"':''?>/> <?php echo _("Non"); ?></label>
				</dd>
				<dd><label for="archiving_days"><?php echo _("Délai souhaité pour l'archivage de vos commandes : "); ?></label>
				<input type="text" name="archiving_days" id="archiving_days" size="2" maxlength="3" value="<?php print isset($config['ord_archiving_days']) && $config['ord_archiving_days']>0 ? $config['ord_archiving_days'] : 0; ?>" /> <?php echo _("jours"); ?>
				</dd>
			</dl>
		<?php } ?>

		<div class="ria-admin-ui-actions">
			<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
		</div>
		
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
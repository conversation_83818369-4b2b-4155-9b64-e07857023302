<?php

	/**	\file edit.php
	 *	Ce fichier fait partie d'un ensemble destiné à la gestion des cycles, développement spécifique réalisé pour la société Boero.
	 *	Merci de ne pas traduire ces fichiers / ne pas inclure les fonctions de traduction, car nous n'en aurons pas l'utilité.
	 */

	require_once('site.inc.php');
	require_once('boero.cycles.inc.php');
	require_once('fields.inc.php');
	require_once('strings.inc.php');
	// require_once('view.site.inc.php');

	unset($error);

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Validation du paramètre $_GET['ccl']
	if( isset($_GET['ccl']) && is_numeric($_GET['ccl']) && $_GET['ccl']>0 && !boero_cycles_exists($_GET['ccl']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( boero_cycles_del( $_GET['ccl'] ) ){
			header('Location: index.php');
			exit;
		}else{
			$error = "Une erreur inattendue est survenue lors de la suppression du cycle.\nVeuillez réessayer.";
		}		
	}

	// Enregistrement
	if( isset($_POST['save']) ){
		if( !isset($_POST['code']) || !trim($_POST['code']) ){
			$error = "Veuillez indiquer le code du cycle.";
		}elseif( !isset($_POST['name']) || !trim($_POST['name']) ){
			$error = "Veuillez indiquer le nom du cycle.";
		}else{
			if( isset($_GET['ccl']) && is_numeric($_GET['ccl']) && $_GET['ccl']>0 ){
				if( !boero_cycles_update($_GET['ccl'],$_POST['code'],$_POST['name'],$_POST['desc'],$_POST['prepa']) ){
					$error = "Une erreur inattendue s'est produite lors de l'enregistrement du cycle";
				}
			}else{
				$result = boero_cycles_add($_POST['code'],$_POST['name'],$_POST['desc'],$_POST['prepa']);
				if( !$result ){
					$error = "Une erreur inattendue s'est produite lors de l'enregistrement du cycle";
				}else{
					header('Location: edit.php?ccl='.$result);
					exit;
				}
			}
		}
		if( !isset($error) && isset($noredir) && $noredir==false ){
			header('Location: index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', 'Cycles - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2>Cycles</h2>

	<?php
		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';

		$cycle = array( 'id'=>0, 'code'=>'', 'name'=>'', 'desc'=>'' , 'prepa'=>'' );
		if( isset($_GET['ccl']) && $_GET['ccl']>0 ){
			$cycle = ria_mysql_fetch_array(boero_cycles_get($_GET['ccl']));
		}
		if( isset($_POST['code']) ){
			$cycle['code'] = $_POST['code'];
		}
		if( isset($_POST['name']) ){
			$cycle['name'] = $_POST['name'];
		}
		if( isset($_POST['desc']) ){
			$cycle['desc'] = $_POST['desc'];
		}
		if( isset($_POST['prepa']) ){
			$cycle['prepa'] = $_POST['prepa'];
		}
	?>
	
	<form action="edit.php?ccl=<?php print $cycle['id']; ?>" method="post">
		<input type="hidden" name="ccl-id" id="ccl-id" value="<?php print $cycle['id']; ?>" />
		<table>
			<caption>Propriétés du cycle</caption>
		<tfoot>
			<tr>
				<td colspan="13">
					<input type="submit" name="save" value="Enregistrer" />
					<input type="submit" name="cancel" value="Annuler" />
					<?php if( $cycle['id']>0 ){ ?>
					<input type="submit" name="del" value="Supprimer" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>			
		<tbody>
			<tr>
				<td><label for="code">Code <span class="mandatory">*</span> :</label></td>
				<td><input type="text" name="code" id="code" class="code" maxlength="6" value="<?php print $cycle['code']; ?>" /></td>
			</tr>
			<tr>
				<td><label for="name">Désignation <span class="mandatory">*</span> :</label></td>
				<td><input type="text" name="name" id="name" maxlength="45" value="<?php print $cycle['name']; ?>" /></td>
			</tr>
			<tr>
				<td><label for="desc">Description :</label></td>
				<td><textarea cols="60" rows="4" name="desc" id="desc" style="height: 125px;"><?php print $cycle['desc']; ?></textarea></td>
			</tr>
			<tr>
				<td><label for="prepa">Préparation :</label></td>
				<td><textarea cols="60" rows="4" name="prepa" id="prepa"><?php print $cycle['prepa']; ?></textarea></td>
			</tr>
		</tbody>
		</table>
		
		<table class="list">
		<?php if(preg_match('`^Y`',$cycle['code']) || preg_match('`^C`',$cycle['code'])){ 
		?>
				<caption>Etapes</caption>
			<thead>
				<tr>
					<th colspan="5">&nbsp;</th>
					<th colspan="3">Epaisseur film humide par couche</th>
					<th colspan="3">Epaisseur film sec par couche</th>
					<th colspan="8">Intervalle de recouvrement (h)</th>
					<th colspan="2">&nbsp;</th>
					<th colspan="2">N° grain 3M</th>
				</tr>
				<tr>
					<th colspan="11">&nbsp;</th>
					<th colspan="2">10°C</th>
					<th colspan="2">15°C</th>
					<th colspan="2">20°C</th>
					<th colspan="2">30°C</th>
					<th colspan="4">&nbsp;</th>
				</tr>
				<tr>
					<th id="step">Etape</th>
					<th id="layers">Nombre de couches</th>
					<th id="prd-ref">Produit conseillé</th>
					<th id="prd-name">Désignation</th>
					<th id="extrait">% Extrait sec</th>
					<th id="epaisseur-hum-min">min</th>
					<th id="epaisseur-hum-std">std</th>
					<th id="epaisseur-hum-max">max</th>
					<th id="epaisseur-sec-min">min</th>
					<th id="epaisseur-hum-std">std</th>
					<th id="epaisseur-sec-max">max</th>
					<th id="inter-dix-min">min</th>
					<th id="inter-dix-max">max</th>
					<th id="inter-qui-min">min</th>
					<th id="inter-qui-max">max</th>
					<th id="inter-vin-min">min</th>
					<th id="inter-vin-max">max</th>
					<th id="inter-tre-min">min</th>
					<th id="inter-tre-max">max</th>
					<th id="diluant">Diluant</th>
					<th id="pourcentage">% (max)</th>
					<th id="grain1">1</th>
					<th id="grain2">2</th>
				</tr>
			</thead>
			<tfoot>
				<tr><td colspan="24">
					<?php if( isset($_GET['ccl']) ){ ?>
					<input type="button" value="Ajouter" onclick="addStep()" />
					<?php } ?>
				</td></tr>
			</tfoot>	
			<tbody>
		<?php
		}else{?>
				<caption>Etapes</caption>
			<thead>
				<tr>
					<th colspan="4">&nbsp;</th>
					<th colspan="2">Intervalle entre chaque couche</th>
					<th colspan="2">Epaisseur du film</th>
					<th colspan="2">Rendement</th>
					<th colspan="2">Diluant</th>
					<th>&nbsp;</th>
				</tr>
				<tr>
					<th id="step">Etape</th>
					<th id="prd-ref">Produit conseillé</th>
					<th id="prd-name">Désignation</th>
					<th id="layers">Nombre de couches</th>
					<th id="intervalle-mini">a 20° mini</th>
					<th id="intervalle-maxi">a 20° maxi</th>
					<th id="epaisseur-humide">humide</th>
					<th id="epaisseur-sec">sec</th>
					<th id="redement-theorique">théorique</th>
					<th id="redement-pratique">pratique</th>
					<th id="diluant">Quantité</th>
					<th id="pourcentage-diluant">Pourcentage</th>
					<th id="application">Mode d\'application</th>
				</tr>
			</thead>
			<tfoot>
				<tr><td colspan="13">
					<?php if( isset($_GET['ccl']) ){ ?>
					<input type="button" value="Ajouter" onclick="addStep()" />
					<?php } ?>
				</td></tr>
			</tfoot>
			<tbody>
		<?php } ?>
		<?php
				if( $cycle['id']==0 ){
					print '<tr><td colspan="13">Veuillez enregistrer le cycle avant d\'indiquer les étapes qui le compose.</td></tr>';
				}else{
					$count = 0;
					$products = boero_cycle_products_get( $cycle['id'] );
					if( ria_mysql_num_rows($products)==0 ){
						print '<tr><td colspan="13">Aucune étape</td></tr>';
					}else{
						while( $prd = ria_mysql_fetch_array($products) ){
							$count++;
							
				?>
		<?php if(preg_match('`^Y`',$cycle['code']) || preg_match('`^C`',$cycle['code'])){ 
		?>
			<tr>
				<td headers="step"><?php print'<a rel="shadowbox;width=445;height=400" href="/admin/config/cycles/step.php?pos='.$prd['pos'].'&amp;prd='.$prd['id'].'&amp;ccl='.$cycle['id'].'">'.$prd['pos'].'</a>'; ?></td>
				<td headers="layers" class="number"><?php print $prd['layers']; ?></td>
				<td headers="prd-ref"><?php print $prd['ref']; ?></td>
				<td headers="layers"><?php print $prd['name']; ?></td>
				<td headers="extrait" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 36, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-hum-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 38, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-hum-std" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 22, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-hum-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 39, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-sec-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 40, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-hum-std" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 23, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="epaisseur-sec-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 41, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-dix-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 42, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-dix-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 43, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-qui-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 44, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-qui-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 45, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-vin-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 28, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-vin-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 29, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-tre-min" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 46, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="inter-tre-max" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 47, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="diluant" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 26, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="pourcentage" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 27, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="grain1" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 48, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				<td headers="grain2" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 49, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
				</tr>
		
		<?php }else{?>
					<tr>
						<td headers="step" class="number"><?php print'<a rel="shadowbox;width=445;height=400" href="/admin/config/cycles/step.php?pos='.$prd['pos'].'&amp;prd='.$prd['id'].'&amp;ccl='.$cycle['id'].'">'.$prd['pos'].'</a>'; ?></td>
						<td headers="prd-ref"><?php print $prd['ref']; ?></td>
						<td headers="prd-name"><?php print $prd['name']; ?></td>
						<td headers="layers" class="number"><?php print $prd['layers']; ?></td>
						<td headers="intervalle-mini" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 28, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="intervalle-maxi" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 29, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="epaisseur-humide" class="taille"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 22, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="epaisseur-sec" class="taille"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 23, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="redement-theorique" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 24, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="redement-pratique" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 30, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="diluant" class="number"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 26, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="pourcentage-diluant" class="pourcentage"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( 27, 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
						<td headers="application"><?php
							$fld = ria_mysql_fetch_array(fld_fields_get( $prd['mode'], 0, 0, 0, 0, $prd['id'] ));
							print fld_fields_view($fld);
						?></td>
					</tr>
				<?php 
						}
					}
				}
			?>
		<?php }?>
		</tbody>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');	
?>
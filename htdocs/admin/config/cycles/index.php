<?php

	/**	\file index.php
	 *	Ce fichier fait partie d'un ensemble destiné à la gestion des cycles, développement spécifique réalisé pour la société Boero.
	 *	Merci de ne pas traduire ces fichiers / ne pas inclure les fonctions de traduction, car nous n'en aurons pas l'utilité.
	 */

	require_once('site.inc.php');
	require_once('boero.cycles.inc.php');

	unset($error);

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( !isset($_POST['ccl']) ){
			$error = _("Veuillez sélectionner les cycles à supprimer");
		}else{
			if( !is_array($_POST['ccl']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression du cycle.");
			}else{
				foreach( $_POST['ccl'] as $p ){
					if( !boero_cycles_del($p) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression du cycle.");
						break;
					}
				}
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Boutons Ajouter
	if( isset($_POST['add']) ){
			header('Location: edit.php');
			exit;
	}

	define('ADMIN_PAGE_TITLE', _('Cycles') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2>Cycles</h2>

	<?php
		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	?>

	<form action="index.php" method="post">
	<table class="checklist">
		<caption><?php print _('Liste des cycles'); ?></caption>
		<col width="25" /><col width="75" /><col width="425" />
	<thead>
		<tr>
			<th id="ccl-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="ccl-code"><?php print _('Code'); ?></th>
			<th id="ccl-name"><?php print _('Désignation'); ?></th>
		</tr>
	</thead>
	<tfoot>
		<tr>
			<td colspan="3">
				<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return cclConfirmDelList()" />
				<div style="float: right">
					<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
				</div>
			</td>
		</tr>
	</tfoot>
	<tbody>
		<?php
			$cycles = boero_cycles_get();
			if( !ria_mysql_num_rows($cycles) ){
				print '<tr><td colspan="3">'._('Aucun cycle').'</td></tr>';
			}else{
				while( $r = ria_mysql_fetch_array($cycles) ){
					print '<tr>';
					print '<td headers="ccl-sel"><input type="checkbox" class="checkbox" name="ccl[]" value="'.$r['id'].'" /></td>';
					print '<td headers="ccl-code"><a href="edit.php?ccl='.$r['id'].'">'.htmlspecialchars($r['code']).'</a></td>';
					print '<td headers="ccl-name">'.htmlspecialchars($r['name']).'</td>';
					print '</tr>';
				}
			}
		?>
	</tbody>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file index.php
	 *	Cette page gère la configuration des adresses emails pour les alertes emails.
	 */

	require_once('email.inc.php');
	require_once('cfg.emails.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_MAIL');
	
	// Sélecteur de site
	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst']>0 ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}
	
	$wst = isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] ? $_SESSION['websitepicker'] : $config['wst_id'];
	
	// Charge la configuration
	$emails = cfg_emails_get( '', $wst );
	
	$tab = '';
	if( $emails && ria_mysql_num_rows($emails) ){
		// Détermine l'onglet en cours de consultation
		$tab = ria_mysql_result( $emails, 0, 'code' );
		if( isset($_GET['tab']) && cfg_emails_code_exists( $_GET['tab'], $wst )){
			if( !gu_user_is_authorized('_RGH_ADMIN_STATS_PRICE_WATCHING') && $_GET['tab'] === 'price-watching'){
				header('location: index.php');
				exit;
			}
			$tab = $_GET['tab'];
		}
		ria_mysql_data_seek( $emails, 0 );
		while( $t = ria_mysql_fetch_array($emails) ){
			if( isset($_POST[$t['code']]) ){
				$tab = $t['code'];
			}
		}

		// Enregistrement des modifications
		$one_set = false;
		ria_mysql_data_seek( $emails, 0 );
		while( $t = ria_mysql_fetch_array($emails) ){
			if( isset($_POST[ $t['code'].'-from' ]) || isset($_POST[ $t['code'].'-to' ]) || isset($_POST[ $t['code'].'-cc' ]) || isset($_POST[ $t['code'].'-bcc' ]) || isset($_POST[ $t['code'].'-reply-to' ])){
				if( !isset($_POST[ $t['code'].'-from' ]) ) $_POST[ $t['code'].'-from' ] = '';
				if( !isset($_POST[ $t['code'].'-to' ]) ) $_POST[ $t['code'].'-to' ] = '';
				if( !isset($_POST[ $t['code'].'-cc' ]) ) $_POST[ $t['code'].'-cc' ] = '';
				if( !isset($_POST[ $t['code'].'-bcc' ]) ) $_POST[ $t['code'].'-bcc' ] = '';
				if( !isset($_POST[ $t['code'].'-reply-to' ]) ) $_POST[ $t['code'].'-reply-to' ] = '';
				cfg_emails_set( $t['code'], $_POST[ $t['code'].'-from' ], $_POST[ $t['code'].'-to' ], $_POST[ $t['code'].'-cc' ], $_POST[ $t['code'].'-bcc' ], $wst, $_POST[ $t['code'].'-reply-to' ] );
				$one_set = true;
			}
		}
		if( $one_set ){
			$emails = cfg_emails_get( '', $wst );
		}
	}

	if( isset($_POST['save-return-path']) ){
		$value = $_POST['return-path'];

		if( !in_array($_POST['return-path'], array('default', 'from')) ){
			if( !isset($_POST['return-path-txt']) || !trim($_POST['return-path-txt']) || !isemail($_POST['return-path-txt']) ){
				$error = _("Merci de renseigner l'adresse e-mail à utiliser pour le return-path.");
			}else{
				$value = $_POST['return-path-txt'];
			}
		}

		if( !isset($error) ){
			if( !cfg_overrides_set_value('email_return_path', $value, $wst) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du return-path.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/config/emails/index.php?wst='.$wst);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Adresses emails') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _('Adresses emails'); ?></h2>

	<?php
		print view_websites_selector( $wst, false, 'riapicker', false );
	?>
	
<p><?php echo _('Du suivi des commandes à la récupération de mots de passe, de nombreux emails différents sont envoyés par la plateforme. Configurez ici les adresses expéditrices ainsi que les copies cachées.'); ?></p>

<p><?php echo _("Pour chaque adresse email, vous pouvez indiquer soit directement l'adresse (ex: <EMAIL>), soit combiner un nom et une adresse (ex: Alertes Emails &lt;<EMAIL>&gt;)."); ?></p>

<form action="index.php?wst=<?php print $wst; ?>&amp;tab=<?php print $tab; ?>" method="post">
	<input type="hidden" name="tab" id="tab" value="<?php print $tab; ?>" />
	<div id="tabstrip-vertical-container">

		<div class="menu-tabstrip"><?php
			if( $emails && ria_mysql_num_rows($emails) ){
				print '
					<ul class="tabstrip-vertical">
				';

				ria_mysql_data_seek( $emails, 0 );
				while( $t = ria_mysql_fetch_array($emails) ){
					if( $t['code'] === 'price-watching' && !gu_user_is_authorized('_RGH_ADMIN_STATS_PRICE_WATCHING') ){
						continue;
					}
					if( $t['code'] === 'update-port' && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_PORT') ){
						continue;
					}
					print '<li><input type="submit" name="'.$t['code'].'" value="'.htmlspecialchars( $t['name'] ).'" '.( $tab==$t['code'] ? 'class="selected"' : '' ).' /></li>';
				}

				print '
					</ul>
				';
			}
		?></div>
		
		<?php
			if( $emails && ria_mysql_num_rows($emails) ){
				print '
					<div id="tabpanel-vertical">
				';

				ria_mysql_data_seek( $emails, 0 );
				while( $t = ria_mysql_fetch_array($emails) ){
					if( $tab==$t['code'] ){
						if( $t['desc'] ){
							print '<p>'.nl2br(htmlspecialchars($t['desc'])).'</p>';
						}
						if( $t['allow-from'] ){
							print '
								<div>
									<label for="'.$t['code'].'-from">' . _('Expéditeur') . ' : </label>
									<input type="text" name="'.$t['code'].'-from" id="'.$t['code'].'-from" value="'.htmlspecialchars($t['from']).'" maxlength="75" />
								</div>
							';		
						}
						if( $t['allow-to'] ){
							print '
								<div>
									<label for="'.$t['code'].'-to">' . _('Destinataire') . ' : </label>
									<input type="text" name="'.$t['code'].'-to" id="'.$t['code'].'-to" value="'.htmlspecialchars($t['to']).'" maxlength="75" />
								</div>
							';		
						}
						if( $t['allow-cc'] ){
							print '
								<div>
									<label for="'.$t['code'].'-cc">' . _('Copie visible à') . ' : </label>
									<textarea name="'.$t['code'].'-cc" id="'.$t['code'].'-cc" cols="40" rows="7">'.htmlspecialchars($t['cc']).'</textarea>
									<span class="label">&nbsp;</span><sub>' . _('(Séparez les adresses par une virgule ou un retour à la ligne)') . '</sub>
								</div>
							';		
						}
						if( $t['allow-bcc'] ){
							print '
								<div>
									<label for="'.$t['code'].'-bcc">' . _('Copie cachée à') . ' : </label>
									<textarea name="'.$t['code'].'-bcc" id="'.$t['code'].'-bcc" cols="40" rows="7">'.htmlspecialchars($t['bcc']).'</textarea>
									<span class="label">&nbsp;</span><sub>' . _('(séparez les adresses par une virgule ou un retour à la ligne)') . '</sub>
								</div>
							';		
						}
						if( $t['allow-reply-to'] ){
							print '
								<div>
									<label for="'.$t['code'].'-reply-to">' . _('Répondre à') . ' : </label>
									<input type="text" name="'.$t['code'].'-reply-to" id="'.$t['code'].'-reply-to" value="'.htmlspecialchars($t['reply-to']).'" maxlength="75" />
								</div>
							';
						}
					}
				}
				print '
						<div class="actions">
							<input type="submit" value="' . _('Enregistrer') . '" />
							<input type="submit" value="' . _('Annuler') . '" />
						</div>
					</div>
				';
			}
		?>
	</div>
</form>

<div class="clear"></div><br /><br />

<form action="index.php?wst=<?php print $wst; ?>" method="post">
	<?php
		$return_path 		= 'default';

		$r_return_path = cfg_overrides_get( $wst, array(), 'email_return_path' );
		if( $r_return_path && ria_mysql_num_rows($r_return_path) ){
			$tmp = ria_mysql_fetch_assoc( $r_return_path );
			$return_path = $tmp['value'];
		}

		$return_path_txt 	= !in_array($return_path, array('default', 'from')) ? $return_path : '';
		
		if( isset($_POST['return-path']) ){
			$return_path = $_POST['return-path'];
		}
		
		if( isset($_POST['return-path-txt']) ){
			$return_path_txt = $_POST['return-path-txt'];
		}
	?>

	<h2>Return-Path</h2>
	<p><?php echo _("Vous pouvez indiquer ici l'adresse e-mail où les bounce-backs (ex. : erreur de livraison d'un e-mail) doivent être livrés. Trois solutions sont disponibles :"); ?></p>
	<ul class="no-style">
		<li>
			<label for="return-path-1">
				<input <?php print $return_path == 'default' ? 'checked="checked"' : ''; ?> type="radio" name="return-path" id="return-path-1" value="default" />
				<?php echo _("Laisser RiaShop gérer cette option"); ?>
			</label>
		</li>
		<li>
			<label for="return-path-2">
				<input <?php print $return_path == 'from' ? 'checked="checked"' : ''; ?>  type="radio" name="return-path" id="return-path-2" value="from" />
				<?php echo _("Utiliser la même adresse que l'expéditeur de la notification"); ?>
			</label>
		</li>
		<li>
			<label class="nested-inputs" for="return-path-3">
				<input <?php print !in_array($return_path, array('default', 'from')) ? 'checked="checked"' : ''; ?>  type="radio" name="return-path" id="return-path-3" value="perso" />
				<?php echo _("Personnalisé, indiquer dans le champ suivant l'adresse :"); ?> <input type="text" name="return-path-txt" value="<?php print htmlspecialchars($return_path_txt); ?>" /></li>
			</label>
		</li>
	</ul>

	<input type="submit" name="save-return-path" value="<?php echo _("Enregistrer"); ?>" />
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
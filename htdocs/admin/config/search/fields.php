<?php

/** \file fields.php
 * 	Ce fichier permet de gérer les informations qui seront prises en compte lors de l'indexation des contenus dans le moteur de recherche.
 * 	Aujourd'hui seuls les articles sont modifiable, à l'avenir tous les contenus auront leur place dans cette interface.
 */

// Vérifier que l'utilisateur en cours peut modifier les règles d'indexation des contenus
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_SEARCH_ENGINE');

require_once('search_fields.inc.php');

define('ADMIN_PAGE_TITLE', _('Champs à indexer').' - '._('Moteur de recherche').' - '._('Configuration'));

if( $_POST ){
	search_select_fields($_POST['fields']);

	header('Location: '.$_SERVER['HTTP_REFERER']);
}

$can_edit = search_fields_can_edit_fields();
$last_update = !$can_edit ? DateTime::createFromFormat('Y-m-d H:i:s', $config['admin_search_engine_fields_updated_at']) : null;

require_once('admin/skin/header.inc.php');

?>

<h2 style="margin-bottom: 0;"><?php print _('Configuration des champs à indexer') ?></h2>
<form action="" method="post" id="form-search-engine-index">
	<div class="config-row">
		<div class="notice">
			<?php print _('Vos modifications prendront un peu de temps avant d\'être visible dans le moteur de recherche.'); ?>
		</div>
		<?php foreach( search_field_categories_get() as $category ){ ?>
			<h3><?php print _($category['title']); ?></h3>
			<?php foreach( search_fields_get($category['id']) as $field ) { ?>
				<div>
					<label for="index-<?php print $field['id']; ?>">
						<input type="checkbox"
							id="index-<?php print $field['id']; ?>"
							name="fields[]"
							value="<?php print $field['id']; ?>"
							<?php print $field['disabled'] || search_is_field_selected($category['class_id'], $field['slug']) ? 'checked' : ''; ?>
							<?php print $field['disabled'] ? 'disabled' : ''; ?>>
						<?php print $field['title']; ?>
					</label>
				</div>
			<?php } ?>
		<?php } ?>
	</div>
	<hr>
	<?php if( $can_edit ){ ?>
		<button><?php print _('Enregistrer'); ?></button>
		<a href="/admin/config/search/index.php">
			<button type="button"><?php print _('Annuler'); ?></button>
		</a>
	<?php }else{ ?>
		<div class="notice">
			<?php print _('Vous devez attendre le <strong>'.$last_update->add(new DateInterval('P7D'))->format('j/m \à H\hi').'</strong> avant de pouvoir de nouveau modifier les champs à indexer.'); ?>
		</div>
	<?php } ?>
</form>

<?php

require_once('admin/skin/footer.inc.php');
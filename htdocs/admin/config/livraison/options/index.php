<?php

require_once('delivery.inc.php');

define('ADMIN_PAGE_TITLE', _('Options').' - '._('Livraison des commandes').' - '._('Configuration'));

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_OPTION');

if( isset($_POST['wst_id']) && wst_websites_exists($_POST['wst_id'], $config['tnt_id']) ){
	$_SESSION['websitepicker'] = (int) $_POST['wst_id'];
}

$override_wst_config = isset($_SESSION['websitepicker']) && wst_websites_exists($_SESSION['websitepicker'], $config['tnt_id']);
$website = $override_wst_config ? $_SESSION['websitepicker'] : 0;

// Bouton "Enregistrer".
if( isset($_POST['save']) ){
	if( !empty($_POST['opt-booking-lock-time']) && is_numeric($_POST['opt-booking-lock-time']) ){
		cfg_overrides_set_value('admin_delivery_lock_time_plage', ((int) $_POST['opt-booking-lock-time']), $website);
	}else{
		$error = 'Vous devez renseigner une valeur (en minutes) pour la réservation des plages.';
	}

	if( !dlv_options_set(isset($_POST['dlv-stores']), isset($_POST['gift']), (isset($_POST['gift-ref']) ? $_POST['gift-ref'] : ''), (isset($_POST['opt-free']) ? $_POST['dealer-free-ht'] : ''), (isset($_POST['opt-price']) ? $_POST['dealer-price-ht'] : ''), $website) ){
		$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de vos options de livraison.');
	}

	if( isset($_POST['pourcent-weight']) ){
		$pourcent = isset($_POST['act-pourcent-weight']) && $_POST['act-pourcent-weight'] && trim($_POST['pourcent-weight'])
			? floatval($_POST['pourcent-weight'])
			: null;

		if( !((is_numeric($pourcent) && $pourcent >= 0) || is_null($pourcent)) ){
			$error = _('Veuillez renseigner un pourcentage de majoration du poids de la commande supérieur ou égal à zéro.');
		}elseif( !cfg_overrides_set_value('cmd_pourcent_packing', $pourcent, $website) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la majoration du poids de la commande. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
	}

	if( !isset($error) ){
		$_SESSION['dlv_options_success'] = true;

		header('Location: index.php');
		exit;
	}
}

// Bouton "Par défaut".
if( isset($_POST['default']) ){
	cfg_overrides_del_value('admin_delivery_lock_time_plage', $website);
	dlv_options_set(0, false, '', null, null, $website);

	header('Location: index.php');
	exit;
}

$cmd_pourcent_packing = $config['cmd_pourcent_packing'];

if( $override_wst_config ){
	$cmd_pourcent_packing = cfg_overrides_get_value('cmd_pourcent_packing', $website);

	if( $cmd_pourcent_packing === false ){
		$r_cmd_pourcent_packing = cfg_overrides_get(0, array(), 'cmd_pourcent_packing');

		if( $r_cmd_pourcent_packing && ria_mysql_num_rows($r_cmd_pourcent_packing) ){
			$cfg_cmd_pourcent_packing = ria_mysql_fetch_assoc($r_cmd_pourcent_packing);
			if( intval($cfg_cmd_pourcent_packing['wst_id']) === 0 ){
				$cmd_pourcent_packing = $cfg_cmd_pourcent_packing['value'];
			}
		}
	}
}

$options = dlv_options_get($website);

require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Options de livraison'); ?></h2>
<?php if( isset($error) ){ ?>
	<div class="error"><?php print nl2br(htmlspecialchars($error)); ?></div>
<?php }elseif( isset($_SESSION['dlv_options_success']) ){ ?>
	<div class="success"><?php print _('Vos options ont été enregistrées avec succès.'); ?></div>
	<?php unset($_SESSION['dlv_options_success']); ?>
<?php } ?>
<form action="" method="post" id="form-delivery-options">
	<?php print view_websites_selector($website, false, 'riapicker wst-selector', false, _('Tous les sites'), false); ?>
	<input type="hidden" class="wst-id" name="wst_id" value="<?php print isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : intval($config['wst_id']); ?>">
	<dl>
		<dt>
			<input type="checkbox" id="act-pourcent-weight" name="act-pourcent-weight" value="1" <?php print $cmd_pourcent_packing ? 'checked' : '';?>>
			<label for="act-pourcent-weight"><?php print _('Majorer le poids de la commande'); ?></label>
		</dt>
			<dd class="description">
				<?php print _('Ce pourcentage sera ajouté lors du calcul du poids de la commande permettant ainsi de mieux tenir compte de l\'emballage dans le choix du service de livraison.'); ?>
			</dd>
		<dd>
			<label for="pourcent-weight"><?php print _('Pourcentage :'); ?></label>
			<input type="text" id="pourcent-weight" name="pourcent-weight" value="<?php print htmlspecialchars($cmd_pourcent_packing); ?>" onkeyup="$(this).val() ? $('#act-pourcent-weight').attr('checked', 'checked') : $('#act-pourcent-weight').removeAttr('checked')"> %
		</dd>
	</dl>
	<dl>
		<dt>
			<input type="checkbox" id="dlv-stores" name="dlv-stores" value="1" <?php print $options['dlv-stores'] ? 'checked' : ''; ?>>
			<label for="dlv-stores"><?php print _('Activer la livraison en magasin'); ?></label>
		</dt>
		<dd class="description">
			<p><?php print _('Si vous activez cette option, vos clients pourront choisir de faire livrer leurs commandes dans l\'un de vos magasins.'); ?></p>
			<p><?php print _('Avant d\'activer cette option, vous devrez renseigner la liste de'); ?> <a href="/admin/config/livraison/stores/index.php"><?php print _('vos magasins'); ?></a>.</p>
			<p><?php print _('Par défaut, cette option est <u>désactivée</u>.'); ?></p>
	</dl>
	<dl>
		<dt>
			<input type="checkbox" id="gift" name="gift" value="1" <?php print $options['gift'] ? 'checked' : ''; ?>>
			<label for="gift"><?php print _('Activer l\'option cadeau'); ?></label>
		</dt>
		<dd class="description">
			<p><?php print _('L\'option cadeau est un service complémentaire, qui vous permet de proposer l\'emballage de la commande dans un papier cadeau accompagné d\'une carte.'); ?></p>
			<p><?php print _('Par défaut, cette option est <u>désactivée</u>.'); ?></p>
		</dd>
		<dd><label for="gift-ref"><?php print _('Référence de l\'article option cadeau :'); ?></label>
			<input type="text" id="gift-ref" name="gift-ref" value="<?php print htmlspecialchars($options['gift-ref']); ?>" maxlength="16">
		</dd>
		<dd></dd>
	</dl>
	<dl>
		<dt>
			<input type="checkbox" id="opt-free" name="opt-free" value="1" <?php print $options['dealer-free-ht'] ? 'checked' : ''; ?>>
			<label for="opt-free"><?php print _('Franco de port revendeurs'); ?></label>
		</dt>
		<dd><?php print _('Offrir les frais de port à vos revendeurs pour toute commande supérieure à'); ?>
		<input type="text" id="dealer-free-ht" class="price" name="dealer-free-ht" value="<?php print $options['dealer-free-ht'] ? ria_number_format($options['dealer-free-ht'], NumberFormatter::DECIMAL, 2) : ''; ?>"> €
		<?php print _('HT'); ?>.
	</dd>
	</dl>
	<dl>
		<dt>
			<input type="checkbox" id="opt-price" name="opt-price" value="1" <?php print $options['dealer-price-ht'] ? 'checked' : ''; ?>>
			<label for="opt-price"><?php print _('Forfait frais de port revendeurs'); ?></label>
		</dt>
		<dd>
			<?php print _('Ajouter le montant forfaitaire suivant pour les frais de port des revendeurs :'); ?>
			<input type="text" id="dealer-price-ht" class="price" name="dealer-price-ht" value="<?php print $options['dealer-price-ht'] ? ria_number_format($options['dealer-price-ht'], NumberFormatter::DECIMAL, 2) : ''; ?>"> €
			<?php print _('HT'); ?>.
		</dd>
	</dl>
	<dl>
		<dt>
			<label for="opt-price"><?php print _('Temps de réservation d\'une plage'); ?></label>
		</dt>
		<dd class="description"><?php print _('Cette option vous permet de déterminer pour combien de temps une plage est réservée à un utilisateur (pour les commandes non confirmées).'); ?></dd>
		<dd>
			<?php print _('Combien de temps une plage doit être considérée comme réservée'); ?> :
			<input type="text" id="opt-booking-lock-time" class="price" name="opt-booking-lock-time" value="<?php print htmlspecialchars(cfg_overrides_get_value('admin_delivery_lock_time_plage', $website)); ?>">
			<?php print _('minutes'); ?>
		</dd>
	</dl>
	<div class="buttons">
		<button type="submit" name="save"><?php print _('Enregistrer'); ?></button>
		<button type="submit" name="default"><?php print _('Par défaut'); ?></button>
	</div>
</form>

<?php

require_once('admin/skin/footer.inc.php');
<?php

	/**	\file popup-sector-edit.php
	 * 
	 * 	Ce fichier fourni une interface permettant le choix d'un secteur
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

$ordered = false;

define('ADMIN_PAGE_TITLE', _('Choisir un secteur'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');
$worked = false;
if (isset($_POST['delete']) && $_POST['delete']=='true') {
	$error = false;
	if(count($_POST["secteurs"])>1){
		$msg =_('Vos secteurs ont bien été supprimés');
	}else{
		$msg =_('Votre secteur a bien été supprimé');
	}
	foreach ($_POST["secteurs"] as $secteurId) {
		if(is_numeric($secteurId)){
			if(!dlv_sectors_delete($secteurId)){
				$error = true;
			}
		}
	}
	if(!$error){
		$worked =  true;
		?>
		<div class="success">
			<p><?php print htmlspecialchars( $msg ); ?></p>
		</div>
		<?php
	}else{
		?>
		<div class="error">
			<p><?php echo _("Une erreur s'est produite lors de la supression du secteur id :"); ?> <?php print $secteurId ?> </p>
		</div>
		<?php

	}
}
if(isset($_POST["sectorname"])&&$_POST["sectorname"] !="" ){
	if(dlv_sectors_set(0,$_POST["sectorname"])){
		$worked =  true;
		?>
		<div class="success">
			<p><?php echo _("Votre secteur à bien été ajouté"); ?></p>
		</div>
		<?php 
	}else{
		?>
		<div class="error">
			<p><?php echo _("Une erreur s'est produite lors de l'enregistrement du secteur :"); ?> <?php echo htmlspecialchars( $_POST["sectorname"] ); ?></p>
		</div>
		<?php 
	}
}
if($_GET['id'] && is_numeric($_GET['id'])){
	$secteurs = dlv_sectors_get();
}else{
	$secteurs =false;
}
?>

<form id="edit-sector-form" action="popup-sector-edit.php?id=<?php print $_GET['id'] ?>" method="post">
	<table id="sectors" class="checklist">
		<caption><?php echo _("Liste des secteurs").' ('.( $secteurs===false || !ria_mysql_num_rows($secteurs) ? '0' : ria_mysql_num_rows($secteurs)).')'; ?></caption>
		<thead>
			<tr>
				<th id="cat-sel" class="col-check"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="cat-name"><?php echo _("Désignation"); ?></th>
				<?php if( $ordered ){ ?><th id="cat-pos"><?php echo _("Déplacer"); ?></th><?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php
			if( $secteurs===false || !ria_mysql_num_rows($secteurs) ){
				print '<tr><td colspan="'.( $ordered ? 3 : 2 ).'">' . _("Aucun secteur") . '</td></tr>';
			}else{
				$count = ria_mysql_num_rows($secteurs); $current = 0;
				while( $r = ria_mysql_fetch_array($secteurs)){
					$sectorList[$current]["id"] = $r['id'];
					$sectorList[$current]["name"] = $r['name'];
					print '
						<tr id="line-' . $r['id'] . '" class="ria-row-orderable">
							<td headers="cat-sel"><input type="checkbox" class="checkbox" name="secteurs[]" value="'.$r['id'].'" /></td>
							<td headers="secteur-name">'.htmlspecialchars($r['name']).'</td>
					';
					if( $ordered ){
						print '<td headers="cat-pos" align="center" class="ria-cell-move">';
						print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
						print '</td>';
					}
					print '</tr>';
					$current++;
				}
			}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="<?php print $ordered ? 3 : 2; ?>">
					<?php if( $ordered ){ ?><<input type="submit" name="move" class="btn-move" value="<?php echo _("Déplacer"); ?>" title="<?php echo _("Déplacer les catégories sélectionnées"); ?>" /><?php } ?>
					<input type="hidden" name="delete" value="">
					<input type="button" class="btn-del" id="delete-button" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les secteurs sélectionnées"); ?>" />
				</td>
			</tr>
			<tr>
				<td colspan="<?php print $ordered ? 3 : 2; ?>" class="tfoot-grey">
					<label for="sectorname"><?php echo _("Ajouter secteur"); ?> </label>
					<input type="text" name="sectorname" id="sectorname" maxlength="75" /> <input type="submit" value="<?php echo _("Ajouter"); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<?php
if($worked){
	if (!isset($sectorList)) {
		$sectorList = array();
	}
	?>
	<script>
		window.parent.refreshSectors(<?php echo json_encode($sectorList); ?>);
	</script>
	<?php
}
?>
<script>
	$(document).ready(function() {
		$("#delete-button").click(function(event){
			// checkCheckbox();
			if( !$('#sectors tbody [type="checkbox"]:checked').length ){
				return false;
			}

			if(window.confirm("<?php print _('Vous êtes sur le point de supprimer les secteurs sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?'); ?>")){
				$('input[name="delete"]').attr('value','true');
				$('#edit-sector-form').submit();
			}
		});
	});
</script>
<?php
?>
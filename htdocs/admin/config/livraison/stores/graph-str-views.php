<?php

	/**	\file graph-str-views.php
	 * 
	 * 	Ce fichier fournit les graphiques de consultations de la boutique pour un produit donné (fiches consultées)
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

	require_once('db.inc.php');

	if(isset($_GET['wst']) && $_GET['wst']==0 ) unset($_GET['wst']);

	if( !isset($_GET['str']) || !dlv_stores_exists($_GET['str']) )
		exit;

	$max = 0;
	if( isset($_GET['day']) ){
		view_date_in_session( $_GET['day'], $_GET['day'], (isset($_GET['wst']) ? $_GET['wst'] : false) );

		if( !preg_match( '/^[0-9]{4}\-[0-9]{2}\-[0-9]{2}$/', $_GET['day'] ) )
			$_GET['day'] = date('Y-m-d');
		
		$graph_title = _('Recherche(s) revendeurs du ').preg_replace( '/^([0-9]{4})\-([0-9]{2})\-([0-9]{2})$/', '\3/\2/\1', $_GET['day'] );

		// Pour un jour donné
		$labels = array();
		$consultations = array();

		// Initialise les tableaux de données
		for( $i=0; $i<24; $i++ ){
			$labels[] = str_pad( $i, 2, '0', STR_PAD_LEFT ).'h';
			$consultations[] = 0;
		}
		
		// Remplit les tableaux de données avec les informations provenant de la base
		$datas = stats_stores_search_seen( $_GET['str'], $_GET['day'], '', '', isset($_GET['wst']) ? $_GET['wst'] : false );
		if( $datas ){
			while( $r = ria_mysql_fetch_array($datas) ){
				$max = $r['hits']>$max ? $r['hits'] : $max;
				if($r['hour'] != null && $r['hits'] !=null)
					$consultations[ $r['hour'] ] = $r['hits'];
			}
		}
	}elseif( isset($_GET['date1'], $_GET['date2'])  ){
		view_date_in_session( $_GET['date1'], $_GET['date2'], (isset($_GET['wst']) ? $_GET['wst'] : false) );
		$months = array('', 'Janvier','Février','Mars','Avril','Mai','Juin','Juillet','Août','Septembre','Octobre','Novembre','Décembre');
		$days = array('', 'Lundi','Mardi','Mercredi','Jeudi','Vendredi','Samedi','Dimanche');
		
		//calcul le nombre de jour de l'interval donné
		$nbjours = abs(round((strtotime($_GET['date1']) - strtotime($_GET['date2']))/(60*60*24)-1));
		
		$graph_title = _('Recherche(s) revendeurs du ').dateformat(date('d/m/Y',strtotime($_GET['date1'])))._(' au ').dateformat(date('d/m/Y',strtotime($_GET['date2'])));
	
		$date_start = strtotime($_GET['date1']);
		$date_stop = strtotime($_GET['date2']);
			
			
		// Construit les tableaux de données
		$consultations = array();
		
		if( $nbjours > 62 ) {// affichage par mois
			
			// Calcule les libellés du graphique
			$labels = array();
			$month_ts = strtotime($_GET['date1']);
			while( $month_ts<=strtotime($_GET['date2']) ){
				$labels[] = $months[(int)date('m',$month_ts)].' '.(date('Y',$month_ts));
				$month_ts = strtotime('+1 month',$month_ts);
			}
			
			// Initialise les tableaux de données
			for( $i=0; $i<sizeof($labels); $i++ ){
				$consultations[] = 0;
			}
			
			// Remplit les tableaux de données avec les informations provenant de la base
			$datas = stats_stores_search_seen( $_GET['str'], $_GET['date1'], $_GET['date2'], 'month', isset($_GET['wst']) ? $_GET['wst'] : false );
			if( $datas ){
				while( $r = ria_mysql_fetch_array($datas) ){
					$max = $r['hits']>$max ? $r['hits'] : $max;
					if(isset($consultations[ $r['month'] ]) && $r['month'] != null && $r['hits'] !=null )
						$consultations[ $r['month'] ] += $r['hits'];
				}
			}
		}else{ // affichage par jour
			
			// Calcule les libellés du graphique
			$labels = array();
			$day_ts = $date_start;
			while( $day_ts<=$date_stop ){
				$labels[] = $days[(int)date('N',$day_ts)].' '.(date('d',$day_ts));
				$day_ts = strtotime('+1 day',$day_ts);
			}
			
			// Initialise les tableaux de données
			for( $i=0; $i<sizeof($labels); $i++ ){
				$consultations[] = 0;
			}
			
			// Remplit les tableaux de données avec les informations provenant de la base
			$datas = stats_stores_search_seen( $_GET['str'], $_GET['date1'], $_GET['date2'], 'day', isset($_GET['wst']) ? $_GET['wst'] : false );
			if( $datas ){
				while( $r = ria_mysql_fetch_array($datas) ){
					$max = $r['hits']>$max ? $r['hits'] : $max;
					if(isset($consultations[ $r['day'] ]) && $r['day'] != null && $r['hits'] !=null)
						$consultations[ $r['day'] ] += $r['hits'];
				}
			}
		}
	}

	$total = 0;
	foreach( $consultations as $consultation ){
		$total += $consultation;
	}

	$max = $max>5 ? $max : 6;
	$graph = new Graph(600,400,'auto');
	$graph->img->SetMargin(60,40,40,80);
	$graph->img->SetAntiAliasing();
	$graph->SetScale('textlin');
	$graph->SetScale('intlin',0, $max, 0, 0);
	$graph->SetMarginColor('white');
	$graph->SetFrame(false);
	$graph->title->Set($graph_title);
	$graph->title->SetFont(FF_VERDANA,FS_NORMAL,14);
	$graph->title->SetMargin(10);

	$graph->xaxis->SetFont(FF_ARIAL,FS_NORMAL,8);
	$graph->xaxis->SetTickLabels($labels);
	$graph->xaxis->SetLabelAngle(45);
	function yFormat($val){
		if( $val==(int)$val )
			return number_format( $val, 0, '.', ' ' );
	}
	$graph->yaxis->SetLabelFormatCallback('yFormat');

	$p1 = new LinePlot($consultations);
	$p1->SetLegend('Affichage ('.number_format( $total, 0 , '', ' ' ).')');
	$p1->SetColor('#FFD061');
	$p1->SetCenter();
	$graph->Add($p1);

	$graph->Stroke();

<?php

	/**	\file edit.php
	 *	Cette page sert de fiche à un magasin. Elle permet l'ajout, la modification et la suppression d'un magasin.
	 */

	require_once('delivery.inc.php');
	require_once('views.inc.php');
	require_once('dlv_store_plage.inc.php');
	unset($error);

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

	// Vérifie que l'utilisateur a bien accès à cette page
	if( isset($_GET['str'])  && $_GET['str'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_VIEW');
	}elseif( !isset($_GET['str']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_ADD');
	}
	// Vérifie que l'utilisateur a bien le droit de modifier un magasin
	if( isset($_GET['str'])  && $_GET['str'] != 0 ){
		if( isset($_POST['save']) || isset($_POST['savefields']) || isset($_POST['save-images']) || isset($_POST['save-ref']) || isset($_POST['save-periods-summer']) || isset($_POST['add-team']) || isset($_POST['del-team']) ){
			gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_EDIT');
		}
	}

	$type_period = array( "morning_summer"=>1, "afternoon_summer"=>2, "morning_winter"=>3, "afternoon_winter"=>4 );

	// Sauvegarde les périodes
	if(isset($_POST['save-periods-summer'])){
		for ($i=1; $i <= 2 ; $i++) {  //pour chaque type de période
			$r_magasin = per_objects_get( CLS_STORE, $_GET['str'], 0, 0, 0, array($i));
			if($r_magasin && ria_mysql_num_rows($r_magasin)){
				$magasin = ria_mysql_fetch_assoc($r_magasin);
			}else{
				$magasin['id'] = per_objects_add(CLS_STORE, $i ,$_GET['str']);
			}

			$days =  array( 1 => 'monday', 2 =>'thuesday', 3 =>'wednesday', 4 =>'thursday', 5 =>'friday', 6 =>'saturday', 7 =>'sunday' );
			foreach($days as $key => $day){ //pour chaque jour
				if(isset($_POST['period'][$i][$day]['start']) && isset($_POST['period'][$i][$day]['end']) ){
					$r_period = per_periods_get($magasin['id'], $key);
					if($r_period && ria_mysql_num_rows($r_period)){
						$period = ria_mysql_num_rows($r_period);
						if($_POST['period'][$i][$day]['start'] == '' || $_POST['period'][$i][$day]['end'] == ''){
							per_periods_del($magasin['id'], $key);
						}else{
							per_periods_update($magasin['id'], $key, $_POST['period'][$i][$day]['start'], $_POST['period'][$i][$day]['end'], true);
						}
					}else{
						per_periods_add( $magasin['id'], $day, $_POST['period'][$i][$day]['start'], $_POST['period'][$i][$day]['end'], true );
					}
				}
			}
		}

		header('Location: /admin/config/livraison/stores/edit.php?str='.$_GET['str'].'&tab=opening');
		exit;
	}

	$page = !isset($_GET['page']) || !is_numeric($_GET['page']) ? 1 : $_GET['page'];

	// Vérification de l'identifiant du magasin
	if( isset($_GET['str']) && $_GET['str']!='' ){
		if( !dlv_stores_exists($_GET['str']) ){
			header('Location: index.php?page='.$page);
			exit;
		}
	}

	$str_id = isset($_GET['str']) ? $_GET['str'] : 0;
	// Sauvegarde des jours et horaires d'expédition des colis
	$success = '';
	if( isset($_POST['save-exp']) ){
		$str_id = $_POST['str'];
		if( !dlv_stores_exists($str_id) ){
			header('Location: index.php?page='.$page);
			exit;
		}
		// Supprime les périodes déjà enregistrées
		if( !dlv_store_opening_del($str_id) ){
			$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		}

		// Parcour tous les jours de la semaine
		if( isset($_POST['hr']) && !isset($error) ){
			foreach( $_POST['hr'] as $day=>$hours ){

				// Parcour chaque heure pour trouver les périodes d'horaires
				$temp = $count = $start = $end = 0;
				$first = true;
				foreach( $hours as $key=>$hour ){
					if( $first ) {
						$start = $hour;
						$temp = $hour;
						$count++;
						$first = false;
					}

					$h = $hour - $temp;
					if( $h>1 ){ // Il s'agi d'une autre période

						// On enregistre la période
						$end = $end>0 ? $end : $start;
						if( !dlv_store_opening_add($str_id, $day, $start, $end) ){
							$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
						}
						$start = $hour;
						$temp = $hour;
						$count = 0;
					} else {
						// Il s'agit de la même périod
						$end = $hour;
						$temp = $hour;
						$count++;
					}
					$end = $hour;
				}

				// On enregistre la dernière période
				if( !dlv_store_opening_add($str_id, $day, $start, $end) ){
					$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
				}
			}
		}
		if( !isset($error) ){
			$success = _("L'enregistrement des horaires d'ouverture du magasin s'est correctement déroulé.");
		}
	}


	// Sauvegarde de l'équipe
	if( isset($_POST['add-team']) ){
		header('location: edit-team.php?str='.$_GET['str']);
		exit;
	}
	if( isset($_POST['del-team'], $_POST['teams']) ){
		foreach($_POST['teams'] as $team ){
			$res = dlv_store_teams_get($_GET['str'], $team);
			$row = ria_mysql_fetch_assoc($res);
			if( !img_images_del($row['img_id'], true) ){
				$error = _('Une erreur est survenue lors de la suppression d\'employé.');
				break;
			}
			if( !dlv_store_teams_del($_GET['str'], $team) ){
				$error = _('Une erreur est survenue lors de la suppression d\'employé.');
				break;
			}
		}
		if( !isset($error) ){
			$success = _('La suppression a bien fonctionné.');
		}
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php?page='.$page);
		exit;
	}

	// Chargement du magasin à modifier
	$str = array(
		'id'=>'', 'name'=>'', 'desc'=>'', 'address1'=>'', 'address2'=>'', 'zipcode'=>'', 'city'=>'', 'sct_id'=>0, 'country'=>'FRANCE',
		'manager'=>'', 'phone'=>'', 'fax'=>'', 'email'=>'', 'website'=>'', 'allow_delivery'=>'', 'latitude'=>'', 'longitude'=>'',
		'tag_title'=>'', 'tag_desc'=>'', 'keywords'=>'', 'is_sync'=>0, 'publish'=>'', 'clickandcollect' => ''
	);
	$page_title = _('Nouveau magasin');
	if( isset($_GET['str']) && $_GET['str']!='' ){
		$str = ria_mysql_fetch_array(dlv_stores_get( $_GET['str'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
		$page_title = $str['name'];
	}

	// Recherche l'onglet en cours de consultation
	$tab = '';
	if( isset($_POST['tabGeneral']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=general&page='.$page);
		exit;
	}elseif( isset($_POST['tabImages']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=images&page='.$page);
		exit;
	}elseif( isset($_POST['tabFields']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=fields&page='.$page);
		exit;
	}elseif( isset($_POST['tabReferencement']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=ref&page='.$page);
		exit;
	}elseif( isset($_POST['tabStatistiques']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=stats&page='.$page);
		exit;
	}elseif( isset($_POST['tabHoraires']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=opening&page='.$page);
		exit;
	}elseif( isset($_POST['tabEquipe']) ){
		header('Location: edit.php?str='.$_GET['str'].'&tab=team&page='.$page);
		exit;
	}elseif( isset($_GET['tab']) ){
		$tab = $_GET['tab'];
	}

	// Par défaut, l'ouverture se fait sur l'onglet Général
	if( !in_array($tab,array('general','images','fields','ref', 'stats', 'team', 'opening')) ){
		$tab = 'general';
	}

	// Suppression du magasin
	if( isset($_POST['del']) && isset($_GET['str']) ){
		if( $str['is_sync'] ){
			header('Location: index.php?page='.$page);
			exit;
		} else {
			if( dlv_stores_del($_GET['str']) ){
				header('Location: index.php?page='.$page);
				exit;
			}else{
				$error = _("La suppression du magasin a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
			}
		}
	}

	// Enregistrement des images associées au magasin
	if( isset($_POST['save-images']) ){
		if( isset($_FILES['image']) && $_FILES['image']['error']!=UPLOAD_ERR_NO_FILE ){
			if( !isset($_POST['types']) ){
				$error = _("Veuillez sélectionner au moins une catégorie qui décrive le contenu de l'image.");
			} elseif( !dlv_stores_images_upload( $_GET['str'], $_POST['types'], 'image' ) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image principale.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				header('Location: edit.php?str='.$_GET['str'].'&tab=images&page='.$page);
				exit;
			}
		}
		$tab = 'images';
	}

	// Suppression d'images associées au magasin
	if( isset($_POST['del-images']) ){
		if( isset($_POST['imgs']) && count($_POST['imgs']) > 0 ){
			foreach( $_POST['imgs'] as $id ) {
				dlv_stores_images_del($_GET['str'], $id);
			}
		}

		if( !isset($error) ){
			header('Location: edit.php?str='.$_GET['str'].'&tab=images&page='.$page);
			exit;
		}
	}

	// Enregistrement des données contenues dans l'onglet Général (Identité, publication, adresse et coordonnées, description)
	if( isset($_POST['save']) ){
		if( $str['id'] && $str['is_sync'] ){
			$_POST['country'] = $str['country'];
		}

		// Vérification des paramètres d'entrée
		if( !isset($_POST['name'],$_POST['desc'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['manager'],$_POST['phone'],$_POST['fax'],$_POST['email'],$_POST['website']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes ou invalides.\nVeuillez réessayer ou prendre contact avec l'adminisrateur.");
		}elseif( !trim($_POST['country']) || $_POST['country']==-1 ){
			$error = _("L'information du pays de localisation du magasin n'a pas été renseigné.");
		}elseif( !trim($_POST['name']) ){
			$error = _("Le nom du magasin est obligatoire pour pouvoir l'enregistrer.\nVeuillez l'indiquer.");
		}elseif( !isset($_GET['str']) || $_GET['str']=='' ){
			$store_id = dlv_stores_add($_POST['name'], '', $_POST['desc'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], $_POST['country'], $_POST['manager'], $_POST['phone'], $_POST['fax'], $_POST['email'], $_POST['website'], isset($_POST['delivery']), $_POST['sector'], 0, false, isset($_POST['publish']), null, null, isset($_POST['clickandcollect']), isset($_POST['timeslots_enabled']));
			if( $store_id ){
				// Ajout de la latitude et de la longitude (latitude comprise entre -90° et +90° bornes incluses et la longitude comprises entre-180° et +180° bornes incluses)
				if ( ($_POST['latitude']>=-90 && $_POST['latitude']<=90) ){
					if ( !dlv_stores_set_latitude($store_id,$_POST['latitude']) ){
						$error = _("Une erreur est survenue lors de l'enregistrement de la latitude.");
					}
				}else{
					$error = _("La latitude doit être comprise entre -90° et +90° (bornes incluses).");
				}
				if ( ($_POST['longitude']>=-180 && $_POST['longitude']<=180) ){
					if ( !dlv_stores_set_longitude($store_id,$_POST['longitude']) ){
						$error = _("Une erreur est survenue lors de l'enregistrement de la longitude.");
					}
				}else{
					$error = _("La longitude doit être comprise entre -180° et +180° (bornes incluses).");
				}
			}else{
				$error = _("La création du magasin a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
			}
		}else{
			$store_id = $_GET['str'];
			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
				if( !dlv_stores_update($_GET['str'],$_POST['name'],'',$_POST['desc'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['manager'],$_POST['phone'],$_POST['fax'],$_POST['email'],$_POST['website'],isset($_POST['delivery']),$_POST['sector'],isset($_POST['publish']), isset($_POST['clickandcollect']), isset($_POST['timeslots_enabled'])) ){
					$error = _("L'enregistrement du magasin a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
				}

				if( isset($_POST['title']) ){
					dlv_stores_set_title( $_GET['str'], $_POST['title'] );
				}
			}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
				$old = ria_mysql_fetch_array( dlv_stores_get( $_GET['str'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ) );
				$values = array(
					_FLD_STR_NAME=>$_POST['name'],
					_FLD_STR_DESC=>$_POST['desc']
				);

				if( !fld_translates_add($_GET['str'], $_GET['lng'], $values) ){
					$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}elseif( !dlv_stores_update($_GET['str'],$old['name'],'',$old['desc'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['manager'],$_POST['phone'],$_POST['fax'],$_POST['email'],$_POST['website'],isset($_POST['delivery']),$_POST['sector'],isset($_POST['publish'])) ){
					$error = _("L'enregistrement du magasin a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
				}
			}

			if( !isset($error) ){
				// Mise à jour de la latitude et de la longitude (latitude comprise entre -90° et +90° et la longitude comprises entre-180° et +180° bornes incluses)
				if( ($_POST['latitude']>=-90 && $_POST['latitude']<=90) ){
					if ( !dlv_stores_set_latitude($_GET['str'],$_POST['latitude']) ){
						$error = _("Une erreur est survenue lors de la mise à jour de la latitude.");
					}
				}else{
					$error = _("La latitude doit être comprise entre -90° et +90° (bornes incluses).");
				}
				if( ($_POST['longitude']>=-180 && $_POST['longitude']<=180) ){
					if ( !dlv_stores_set_longitude($_GET['str'],$_POST['longitude']) ){
						$error = _("Une erreur est survenue lors de la mise à jour de la longitude.");
					}
				}else{
					$error = _("La longitude doit être comprise entre -180° et +180° (bornes incluses).");
				}
			}
		}
		dlv_store_sales_types_del($_GET['str']);
		if( isset($_POST['sale']) ){
			// Si c'est une création de magasin on récupère l'id du magasin nouvellement créé sinon c'est une mise à jour on récupère alors id du magasin dans la variable get
			$strId = isset($store_id) ? $store_id : $_GET['str'];
			if( !dlv_store_types_add($strId,$_POST['sale']) )
				$error = _("Une erreur est survenue lors du rattachement des types de ventes au magasin.");
		}
		if( !isset($error) ){
			header('Location: edit.php?str='.$store_id.'&page='.$page);
			exit;
		}
	}

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Action sur l'onglet "Avancés"
	if( isset($_GET['str']) && is_numeric($_GET['str']) && $_GET['str'] > 0 ){
		view_admin_tab_fields_actions( CLS_STORE, $_GET['str'], $lng );
	}

	// Sauvegarde le référencement
	if( isset($_GET['str']) && is_numeric($_GET['str']) && $_GET['str'] > 0 ){
		view_admin_tab_referencement_actions( CLS_STORE, $_GET['str'], $lng );
	}
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Magasins') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print view_str_is_sync($str).' '.htmlspecialchars($page_title); ?></h2>

	<?php
		// Affichage des erreurs
		if (isset($_SESSION['referencement_edit_success'])) {
			$success = $_SESSION['referencement_edit_success'];
			unset($_SESSION['referencement_edit_success']);
		}
		if (isset($_SESSION['referencement_edit_error'])) {
			$error = $_SESSION['referencement_edit_error'];
			unset($_SESSION['referencement_edit_error']);
		}

		if( isset($error) && trim($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

		if( isset($success) && trim($success) !== ''){
			print '<div class="error-success">'.htmlspecialchars($success).'</div>';
		}

		if( isset($_GET['str']) && $_GET['str']!='' ){
			// Récupère les informations traduite
			if( $lng!=$config['i18n_lng'] ){
				$tsk_str = fld_translates_get( CLS_PRODUCT, $str['id'], $lng, $str, array(_FLD_STR_NAME=>'name', _FLD_STR_DESC=>'desc' ), true );
				$str['name'] = $tsk_str['name'];
				$str['desc'] = $tsk_str['desc'];
			}
		}

		// Affiche le menu de langue pour permettre la traduction de la fiche Magasin
		if( isset($_GET['str']) && $_GET['str']!='' ){
			print view_translate_menu( 'edit.php?str='.$str['id'].'&amp;tab='.$tab.'&amp;page='.$page, $lng );
		}
	?>

	<form action="edit.php?str=<?php print $str['id'] ?>&amp;page=<?php print $page; ?>&amp;lng=<?php print $lng; ?>" enctype="multipart/form-data" method="post" onsubmit="return strValidForm(this)" id="form_store">

	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php echo _("Général"); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php
		if(isset($_GET['str']) && $_GET['str']!=''){
			if( view_admin_show_tab_fields( CLS_STORE, $_GET['str'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php echo _("Avancé"); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php
			}
		}
		?>
		<li><input type="submit" name="tabImages" value="<?php echo _("Images"); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
		<?php if( tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabReferencement" value="<?php echo _("Référencement"); ?>" <?php if( $tab=='ref' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabHoraires" value="<?php echo _("Horaires"); ?>" <?php if( $tab=='opening' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabEquipe" value="<?php echo _("Equipe"); ?>" <?php if( $tab=='team' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabStatistiques" value="<?php echo _("Statistiques"); ?>" <?php if( $tab=='stats' ) print 'class="selected"'; ?> /></li>
	</ul>
	<div id="tabpanel">

	<?php if( $tab=='general' ){ ?>
	<table>
		<tbody>
			<?php if( $str['id']>0 ){ ?>
			<tr>
				<td><label for="strid"><?php echo _('Identifiant :'); ?></label></td>
				<td colspan="2"><input type="text" name="strid" id="strid" value="<?php print $str['id']; ?>" maxlength="75" disabled="disabled" /></td>
			</tr>
			<?php } ?>
			<tr>
				<td><span class="mandatory">*</span> <label for="name"><?php echo _('Nom :'); ?></label></td>
				<td colspan="2"><input type="text" name="name" id="name" value="<?php print htmlspecialchars($str['name']); ?>" maxlength="75" <?php print $str['is_sync'] ? 'readonly="readonly"' : ''; ?> /></td>
			</tr>
			<?php
				if( $str['is_sync'] ){
					print '
						<tr>
							<td><label for="name">' . _('Titre :') . '</label></td>
							<td colspan="2"><input type="text" name="title" id="title" value="'.htmlspecialchars( $str['str_title'] ).'" maxlength="75" /></td>
						</tr>
					';
				}
			?>
			<tr>
				<td><label><?php print _('Publication :'); ?></label></td>
				<td colspan="2">
					<input type="checkbox" name="publish" id="publish" value="1" <?php if( $str['publish'] ) print 'checked="checked"'; ?> <?php if( !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_PUBLISH') ) print 'disabled="disabled"'; ?> />
					<label for="publish"><?php echo _("Afficher ce magasin sur le site"); ?></label>
				</td>
			</tr>
			<tr>
				<td><label><?php print _('Livraison :'); ?></label></td>
				<td colspan="2">
					<input type="checkbox" name="delivery" id="delivery" value="1" <?php if( $str['allow_delivery'] ) print 'checked="checked"'; ?> />
					<label for="delivery"><?php echo _("Autoriser la livraison dans ce magasin"); ?></label>
				</td>
			</tr>
			<?php if( isset($config['dlv_store_clickandcollect']) && $config['dlv_store_clickandcollect'] == 1 ) {?>
				<tr>
					<td>
						<label><?php print _('Click&Collect :'); ?></label>
					</td>
					<td colspan="2">
						<input type="checkbox" name="clickandcollect" id="clickandcollect" value="1" <?php if($str['clickandcollect'] == '1') print 'checked="checked"'; ?> />
						<label for="clickandcollect"><?php print _("Autoriser le Click&Collect dans ce magasin"); ?></label>
					</td>
				</tr>
			<?php } ?>
			<?php if( !empty($config['store_dlv_hour']) ){ ?>
				<tr id="tr_timeslots_enabled" <?php print !$str['clickandcollect'] ? 'style="display: none;"' : ''; ?>>
					<td>
						<label for=""><?php print _('Activer plage horaire :'); ?></label>
					</td>
					<td colspan="2">
						<input type="checkbox" id="timeslots_enabled" name="timeslots_enabled" value="1" <?php print !empty($str['timeslots_enabled']) ? 'checked' : ''; ?>>
						<label for="timeslots_enabled"><?php print _('Activer les plages horaires'); ?></label>
					</td>
				</tr>
			<?php } ?>
			<tr><th colspan="3"><?php echo _("Adresse"); ?></th></tr>
			<tr>
				<td><label for="address1"><?php echo _('Adresse :'); ?></label></td>
				<td colspan="2"><input type="text" name="address1" id="address1" value="<?php print htmlspecialchars($str['address1']); ?>" maxlength="75" <?php print $str['is_sync'] ? 'readonly="readonly"' : ''; ?> /></td>
			</tr>
			<tr>
				<td>&nbsp;</td>
				<td colspan="2"><input type="text" name="address2" id="address2" value="<?php print htmlspecialchars($str['address2']); ?>" maxlength="75" <?php print $str['is_sync'] ? 'readonly="readonly"' : ''; ?> /></td>
			</tr>
			<tr>
				<td><label for="zipcode"><?php echo _('Code postal :'); ?></label></td>
				<td colspan="2"><input type="text" name="zipcode" id="zipcode" class="zipcode" value="<?php print htmlspecialchars($str['zipcode']); ?>" maxlength="20" <?php print $str['is_sync'] ? 'readonly="readonly"' : ''; ?> /></td>
			</tr>
			<tr>
				<td><label for="city"><?php echo _('Ville :'); ?></label></td>
				<td colspan="2"><input type="text" name="city" id="city" value="<?php print htmlspecialchars($str['city']); ?>" maxlength="75" <?php print $str['is_sync'] ? 'readonly="readonly"' : ''; ?> /></td>
			</tr>
			<tr>
				<td>
					<label for="sector"><?php echo _('Secteur géographique :'); ?></label>
					<sub><a class="edit" href="#" onclick="editSectors(<?php print $str['id'] ?>)"><?php print _('Éditer la liste'); ?></a></sub>
				</td>
				<td colspan="2"><select name="sector" id="sector">
				<?php
					$sectors = dlv_sectors_get();
					print '<option value="0">Aucun secteur géographique</option>';
					while( $sector = ria_mysql_fetch_array($sectors) ){
						print '<option value="'.$sector['id'].'" '.( $sector['id']==$str['sct_id'] ? ' selected="selected"' : '' ).'>'.$sector['name'].'</option>';
					}
				?>
					</select>
				</td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label for="country"><?php echo _('Pays :'); ?></label></td>
				<td colspan="2"><?php
					require_once('sys.countries.inc.php');
					$countries = sys_countries_get();
					if( $countries && ria_mysql_num_rows($countries) ){
						print '	<select name="country" id="country" '.($str['is_sync'] ? 'disabled="disabled"' : '').'>';
						print '		<option value="-1">&nbsp;</option>';
						while( $country = ria_mysql_fetch_array($countries) )
							print '	<option value="'.strtoupper2($country['name']).'" '.( strtoupper2(trim($country['name']))==$str['country'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($country['name']).'</option>';
						print '</select>';
					}
				?></td>
			</tr>
			<tr><th colspan="3"><?php echo _("Contact"); ?></th></tr>
			<tr>
				<td><label for="manager"><?php echo _('Gérant :'); ?></label></td>
				<td colspan="2"><input type="text" name="manager" id="manager" maxlength="75" value="<?php print htmlspecialchars($str['manager']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="phone"><?php echo _('Téléphone :'); ?></label></td>
				<td colspan="2"><input type="text" name="phone" id="phone" maxlength="20" value="<?php print htmlspecialchars($str['phone']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="fax"><?php echo _('Fax :'); ?></label></td>
				<td colspan="2"><input type="text" name="fax" id="fax" maxlength="20" value="<?php print htmlspecialchars($str['fax']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="email"><?php echo _('Email :'); ?></label></td>
				<td colspan="2"><input type="email" name="email" id="email" maxlength="75" value="<?php print htmlspecialchars($str['email']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="website"><?php echo _('Site Internet :'); ?></label></td>
				<td colspan="2"><input type="text" name="website" id="website" maxlength="255" value="<?php print htmlspecialchars($str['website']); ?>" /></td>
			</tr>
			<tr><th colspan="3"><?php echo _("Coordonnées géographiques"); ?></th></tr>
			<tr>
				<td><label for="latitude"><?php echo _('Latitude :'); ?></label></td>
				<td colspan="2"><input type="text" name="latitude" id="latitude" value="<?php if( isset($_POST['latitude']) ) print $_POST['latitude']; else print htmlspecialchars($str['latitude']); ?>" maxlength="20" /></td>
			</tr>
			<tr>
				<td><label for="longitude"><?php echo _('Longitude :'); ?></label></td>
				<td colspan="2"><input type="text" name="longitude" id="longitude" value="<?php if( isset($_POST['longitude']) ) print $_POST['longitude']; else print htmlspecialchars($str['longitude']); ?>" maxlength="20" /></td>
			</tr>
			<tr><th colspan="3"><?php echo _("Présentation"); ?></th></tr>
			<tr>
				<td><label for="desc"><?php echo _("Description/Présentation :"); ?></label></td>
				<td colspan="2"><textarea class="tinymce" name="desc" id="desc" rows="15" cols="40"><?php print view_site_format_riawysiwyg($str['desc'], false, true, false, false); ?></textarea></td>
			</tr>
			<?php
				$sales_types = dlv_sales_types_get();
				if( ria_mysql_num_rows($sales_types)>0 ){
					$nb_rows = ceil(ria_mysql_num_rows($sales_types)/2);
			?>
			<tr><th colspan="3"><?php echo _("Type de ventes"); ?></th></tr>
			<tr class="align-center"><td colspan="3"><a href="#" class="check-all"><?php echo _("Tous cocher"); ?></a> | <a href="#" class="uncheck-all"><?php echo _("Tous décocher"); ?></a></td></tr>
			<tr>
					<?php
						$count = 1;
						while( $sale_type = ria_mysql_fetch_array($sales_types) ){
							if( $count==1 ){
								print '<td></td><td class="sale-type">';
							}
							print '		<input type="checkbox" name="sale[]" id="sale-'.$sale_type['id'].'" value="'.$sale_type['id'].'" '.( isset($_GET['str']) && dlv_store_sales_types_proposed($_GET['str'],$sale_type['id']) ? 'checked="checked"' : '').'/> <label for="sale-'.$sale_type['id'].'">'.htmlspecialchars($sale_type['name']).'</label>';
							$count++;

							if( $count == 2 ){
								print '</td><td class="sale-type">';
							}elseif( $count==3 ){
								print '</td></tr>';
								$count = 1;
							}
						}
						if( $count==2 ){
							print '</td></tr>';
						}
					?>
			</tr>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr><td colspan="3">
				<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
				<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return strCancelEdit()" />
				<?php if( $str['id']!='' && !$str['is_sync'] && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _('Supprimer'); ?>" onclick="return strConfirmDel()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>

	<?php }elseif( $tab=='images' ){ ?>
	<script>
		$(document).ready(function() {
			init_img(<?php print CLS_STORE; ?>, <?php print $str['id']; ?>);
		});
	</script>
	<table>
		<tbody>
			<tr>
				<td>
					<label for="image"><?php print _('Ajouter une image :'); ?></label>
				</td>
				<td colspan="2">
					<input type="file" name="image" id="image">
				</td>
			</tr>
			<tr>
				<td><?php print _('Catégories :'); ?></td>
				<td colspan="2">
					<?php
						$types = dlv_stores_img_types_get();
						while( $t = ria_mysql_fetch_array($types) ){
							print '<input type="checkbox" class="checkbox" name="types[]" id="type-'.$t['id'].'" value="'.$t['id'].'" /> ';
							print '<label for="type-' . $t['id'] . '">' . htmlspecialchars($t['name']) . '</label><br />';
						}
					?>
				</td>
			</tr>
			<tr>
				<th colspan="3"><?php echo _('Images associées'); ?></th>
			</tr>
				<tr>
					<td colspan="3" class="list-shop-image">
						<?php print view_admin_img_list(CLS_STORE, $str['id']); ?>
					</td>
				</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3">
					<input type="submit" name="save-images" value="<?php echo _("Enregistrer"); ?>" title="<?php echo _("Enregistrer les modifications"); ?>" />
					<input type="submit" name="del-images" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les images sélectionnées"); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
	<?php
	}elseif( $tab=='fields' ){
		print view_admin_tab_fields( CLS_STORE, $_GET['str'], $lng );
	}elseif( $tab=='ref' ){
		$str = ria_mysql_fetch_array(dlv_stores_get( $_GET['str'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));

		// Affiche le menu de langue
		print view_admin_tab_referencement(CLS_STORE, $_GET['str'], $lng);

	}elseif( $tab == 'stats' ){ ?>

		<script>
		<!--[CDATA[
			<?php print view_date_initialized( 0, '', false, array('callback'=>'load_reports') ); ?>
			function load_reports(){
				window.location.href = 'edit.php?&tab=stats&page=1&str=<?php print $_GET["str"]; ?>&date1=' + $('[name=date1]').val() + '&date2=' + $('[name=date2]').val() +  '&wst=' + $('[name=wst]').val()  + '&last=' + $('[name=last]').val();
			}

		//	]]-->
		</script>
		<script>
		<!--[CDATA[
			parameters.push("str="+<?php print $_GET['str']; ?>);
		//	]]-->
		</script>
		<div class="stats-menu"><?php
			$website = wst_websites_get();
			$wst_id = false;
			if( isset($_SESSION['websitepicker']) && !isset($_GET['wst']) ){
				$_GET['wst'] = str_replace('w-', '', $_SESSION['websitepicker']);
			}

			if( isset($_GET['wst']) ){
				if($_GET['wst']=='all'){
					$wst_id = false;
				}else{

					$wst_id = str_replace('w-','',$_GET['wst']);
				}
			}

			// si le nombre de site du locataire est supérieur a un on affiche le filtre pour trier par site
			if( ria_mysql_num_rows($website)>1 ){ ?>

			<div id="riawebsitepicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Site web"); ?></span><br/>
						<?php if( $wst_id ){
								$tw = ria_mysql_fetch_array(wst_websites_get($wst_id));
								print '<span class="view">'.$tw['name'].'</span>';
							} else { ?>
								<span class="view"><?php echo _("Tous les sites"); ?></span>
						<?php }?>
					</div>
					<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="all"><?php echo _("Tous les sites"); ?></a>
					<?php
						while($w = ria_mysql_fetch_array($website)){
							print '<a name="w-'.$w['id'].'">'.$w['name'].'</a>';
						}
					?>
				</div>
			</div>
			<?php } ?>

			<div id="riadatepicker"></div>
			<?php
				if( isset($_GET['date1'], $_GET['date2'], $_GET['last']) ){
					print '<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>';
					print '<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>';
					print '<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>';
				}
				if( isset($_GET['wst']) ){
					print '<input type="hidden" name="wst" id="wst" value="'.$_GET['wst'].'"/>';
				}
			?>
			<div class="clear"></div>
		</div>

		<?php
			//recupere les dates
			if( isset($_SESSION['datepicker_period'], $_SESSION['datepicker_date1']) ){
				if( $_SESSION['datepicker_period']=='Aujourd\'hui' && strtotime($_SESSION['datepicker_date1'])!=strtotime(date('Y-m-d')) ){
					$_SESSION['datepicker_date1'] = date('d/m/Y');
					$_SESSION['datepicker_date2'] = date('d/m/Y');
				}
			}
			$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
			$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
			$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

			// Affiche les graphiques relatifs à la sélection
			if( $date1 == $date2 ){
				$arg = 'day='.$date1;
			}else{
				$arg = 'date1='.$date1.'&amp;date2='.$date2;
			}
			if( isset($_GET['wst']) && $wst_id ){
				$arg .='&amp;wst='.$wst_id;
			}
		?>

		<div id="graph-str-views"></div>
		<hr class="hr-magasin-stat"/>
		<div id="graph-str-contacts"></div>
		<?php
			view_import_highcharts();
			require_once( 'admin/highcharts/graph-str-view.php' );

			require_once( 'admin/highcharts/graph-str-contacts.php' );
		?>
	<?php } elseif( $tab == 'opening' ) {?>
			<p class="notice">
				<?php print _('Dans une semaine normale, veuillez sélectionner ci-dessous les jours et heures durant lesquels le magasin est ouvert :'); ?>
			</p>
			<input type="hidden" name="str" value="<?php echo $str_id ?>">
			<div class="table-layout-large">
				<table id="tb-day-exp">
					<caption><?php print _("Horaires d'ouverture"); ?></caption>
					<thead>
						<tr>
							<th><?php echo _("Jour"); ?></th>
							<th colspan="2" class="align-center"> <?php echo _("Matin"); ?> </th>
							<th colspan="2" class="align-center"> <?php echo _("Après-midi"); ?> </th>
						</tr>
						<tr id="th-width-day-exp">
							<th></th>
							<th class="align-center"><?php echo _("Ouverture"); ?></th>
							<th class="align-center"><?php echo _("Fermeture"); ?></th>
							<th class="align-center"><?php echo _("Ouverture"); ?></th>
							<th class="align-center"><?php echo _("Fermeture"); ?></th>
						</tr>
					</thead>
					<tbody><?php
						$days = array(
							1 => 'Lundi',
							2 => 'Mardi',
							3 => 'Mercredi',
							4 => 'Jeudi',
							5 => 'Vendredi',
							6 => 'Samedi',
							0 => 'Dimanche'
						);

						$id_days = array(
							'Lundi'=>'monday',
							'Mardi'=>'thuesday',
							'Mercredi'=>'wednesday',
							'Jeudi'=>'thursday',
							'Vendredi'=>'friday',
							'Samedi'=>'saturday',
							'Dimanche'=>'sunday'
						);
						//récupère les horraires du magasin
						$hour = dlv_stores_opening_hours_get($_GET['str']);

						foreach( $days as $key=>$day ){
							$count = 0;

							?><tr id="<?php print $id_days[$day]; ?>">
								<td headers="day-exp"><?php print _($day); ?></td>
								<td class="align-center">
									<div class="input-group clockpicker" data-placement="left" data-align="top" data-autoclose="true">
										<input type="text" class="form-control" name="period[1][<?php print $id_days[$day]; ?>][start]" value="<?php print isset($hour[$key]['morning']['start']) ? $hour[$key]['morning']['start'] : ''; ?>">
										<span class="input-group-addon">
											<span class="glyphicon glyphicon-time"></span>
										</span>
									</div>
								</td>
								<td class="align-center">
									<div class="input-group clockpicker" data-placement="left" data-align="top" data-autoclose="true">
										<input type="text" class="form-control" name="period[1][<?php print $id_days[$day]; ?>][end]" value="<?php print isset($hour[$key]['morning']['end'])? $hour[$key]['morning']['end'] : ''; ?>" />
										<span class="input-group-addon">
											<span class="glyphicon glyphicon-time"></span>
										</span>
									</div>
								</td>
								<td class="align-center">
									<div class="input-group clockpicker" data-placement="left" data-align="top" data-autoclose="true">
										<input type="text" class="form-control" name="period[2][<?php print $id_days[$day]; ?>][start]" value="<?php print isset($hour[$key]['afternoon']['start'])? $hour[$key]['afternoon']['start'] : ''; ?>" />
										<span class="input-group-addon">
											<span class="glyphicon glyphicon-time"></span>
										</span>
									</div>
								</td>
								<td class="align-center">
									<div class="input-group clockpicker" data-placement="left" data-align="top" data-autoclose="true">
										<input type="text" class="form-control" name="period[2][<?php print $id_days[$day]; ?>][end]" value="<?php print isset($hour[$key]['afternoon']['end'])? $hour[$key]['afternoon']['end'] : ''; ?>" />
										<span class="input-group-addon">
											<span class="glyphicon glyphicon-time"></span>
										</span>
									</div>
								</td>
							</tr><?php
						}
					?></tbody>
					<tfoot>
						<tr>
							<td colspan="5">
								<input type="submit" name="save-periods-summer" value="<?php echo _("Enregistrer"); ?>">
								<input type="submit" name="cancel" class="button" value="<?php echo _("Annuler"); ?>" onclick="">
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
			<!--- Gestion des fermeture exceptionnelle -->
			<p class="notice margin-top-10">
				<?php echo _("Si durant une période donnée vous n'êtes pas en mesure de réaliser des expéditions (quelle que soit la raison), veuillez l'indiquer ci-dessous. RiaShop prendra ainsi en compte ces dates dans le calcul des délais d'expédition et de livraison."); ?>
			</p>
			<div id="form-closing" method="post">
				<input type="hidden" name="str_id" id="str_id" value="<?php echo $str_id ?>">
				<div class="closing-menu">
					<div id="closingpicker">
						<div class="selectorview">
							<div class="left">
								<span class="function_name"><?php echo _("Fermetures"); ?></span><br/>
								<?php
									$period = isset($_GET['period']) && ( $_GET['period']>=0 || $_GET['period']<3 ) ? $_GET['period'] : 0;

									switch($period){
										case 0 :
											print '<span class="view">' . _("Toutes") . '</span>';
											break;
										case 1 :
											print '<span class="view">' . _("Actuelles") . '</span>';
											break;
										case 2 :
											print '<span class="view">' . _("&Agrave; venir") . '</span>';
											break;
										default :
											print '<span class="view">' . _("Toutes") . '</span>';
											break;
									}
								?>
							</div>
							<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
							<div class="clear"></div>
						</div>
						<div class="selector">
							<a name="p-0"><?php echo _("Toutes"); ?></a>
							<a name="p-1"><?php echo _("Actuelles"); ?></a>
							<a name="p-2"><?php echo _("&Agrave; venir"); ?></a>
						</div>
					</div>
					<div class="clear"></div>
				</div>
				<table id="tb-closing">
					<caption><?php echo _("Période de fermetures exceptionnelles"); ?></caption>
					<thead>
						<tr>
							<th id="clg-name"><?php echo _("Nom de la période"); ?></th>
							<th id="clg-start" class="col130px"><?php echo _("Date de début (inclus)"); ?></th>
							<th id="clg-end" class="col130px"><?php echo _("Date de fin (inclus)"); ?></th>
							<th id="clg-action" class="col20px"></th>
						</tr>
					</thead>
					<tbody>
						<?php
							// Récupère les dates de fermeture exceptionnelle
							$rclosing = dlv_events_get(0, $period, $str_id);

							if( $rclosing==false || ria_mysql_num_rows($rclosing)==0 ){
								// Affiche un message si aucune fermeture n'existe
								print '
									<tr id="none-closing">
										<td colspan="4">' . _("Aucune fermeture exceptionnelle n'est enregistrée") . '</td>
									</tr>
								';
							} else {
								// Affichage des fermetures exceptionnelle déjà enregistré
								while( $closing = ria_mysql_fetch_array($rclosing) ){
									print '
										<tr id="closing-'.$closing['id'].'">
											<td headers="clg-name" class="td-info">'.htmlspecialchars($closing['name']).'</td>
											<td headers="clg-start" class="td-date">'.ria_date_format($closing['start']).'</td>
											<td headers="clg-end" class="td-date">'.ria_date_format($closing['end']).'</td>
											<td headers="clg-action" class="td-action">
												<a class="del" onclick="closing_del('.$closing['id'].');">
													<img class="edit-closing" src="/admin/images/del.svg" alt="Editer" title="' . _("Editer les informations concernant cette fermeture exceptionnelle") . '" onclick="closing_edit('.$closing['id'].', \''.htmlspecialchars($closing['name']).'\', \''.$closing['start'].'\', \''.$closing['end'].'\');" />
													<div id="save-load-'.$closing['id'].'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>
												</a>
											</td>
										</tr>
									';
								}
							}
						?>
					</tbody>
					<tfoot>
						<tr>
							<td colspan="4" style="text-align:right">
								<input type="button" name="add-closing" id="add-closing" value="<?php echo _("Nouvelle Période"); ?>" title="<?php echo _("Ajouter une nouvelle période de fermeture exceptionnelle"); ?>" onclick="closing_add();" />
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
			<!--- Gestion des plages horaires pour les livraisons -->
<?php
			// On affiche ce block seulement si le client possède la configuration
			if( $config['store_dlv_hour'] && $str['timeslots_enabled'] ){ ?>
				<h3><?php print _('Plage horaire de livraison'); ?></h3>
				<p class="notice">
					<?php print _('Permet de configurer les plages horaires de livraison.'); ?>
				</p>
				<table>
					<caption><?php print _('Plages horaires'); ?></caption>
					<thead>
						<tr>
							<th rowspan="2"><?php print _('Jour'); ?></th>
							<th colspan="3"><?php print _('Plage horaire'); ?></th>
						</tr>
						<tr>
							<th><?php print _('Matin'); ?></th>
							<th><?php print _('Après-midi'); ?></th>
							<th><?php print _('Soirée'); ?></th>
						</tr>
					</thead>
					<tbody>
<?php
					$days = array(
						1 => 'Lundi',
						2 => 'Mardi',
						3 => 'Mercredi',
						4 => 'Jeudi',
						5 => 'Vendredi',
						6 => 'Samedi',
						0 => 'Dimanche'
					);

					foreach ($days as $key => $day) {
						// récupération des plage horaire pour la journée voulu
						$stmt_plage = dsp_plage_get_parts($_GET['str'], $key);
?>
						<tr>
							<td><?php print $day; ?></td>
<?php
							if($stmt_plage != false){
								foreach($stmt_plage as $part){
									print '<td>';
									foreach ($part as $plage) {
										print '<span class="dlv_hour_plage" data-id="' . $plage['id'] . '">' . $plage['debut'] . ' <img class="edit-plage" src="/admin/images/expeditions/edit.png" alt="'._('Editer').'" title="'._('Editer les informations concernant cette plage horaire').'" style="height: 15px; cursor: pointer;" data-id="' . $plage['id'] . '"></span>';
									}


									print '</td>';
								}
							}else{
								print '<td colspan="3">' . _('Aucune plage définie') . '</td>';
							}
?>
						</tr>
<?php

					}
?>
					</tbody>
					<tfoot>
						<tr>
							<td colspan="4" style="text-align:right">
								<input type="button" name="del-plage" id="del-plage" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer plage horaire"); ?>" />
								<input type="button" name="add-plage" id="add-plage" value="<?php echo _("Nouvelle plage"); ?>" title="<?php echo _("Ajouter une nouvelle plage horaire"); ?>" />
							</td>
						</tr>
					</tfoot>
				</table>

<?php
			}
?>
	<?php }elseif( $tab == 'team' ){ ?>
		<input type="hidden" name="tabEquipe" value="<?php echo $_GET['tab'] ?>">
		<table>
			<caption><?php echo _("Membres de l'équipe"); ?></caption>
			<tbody>
				<tr>
					<td>
						<ul id="employees">
							<?php $res = dlv_store_teams_get($_GET['str']);
							if( !$res ){
								echo '<li><p>' . _("Aucun employé pour le moment") . '</p></li>';
							}else{
								while( $row = ria_mysql_fetch_assoc($res) ){ ?>
									<li>
										<div class="employee">
											<input type="checkbox" class="checkbox none" name="teams[]" value="<?php echo $row['id'] ?>">
											<div class="employee-image">
											<?php if( isset($row['img_id']) && $row['img_id']!=0 ){ ?>
												<img src="<?php echo $config['img_url'].'/'.$config['img_sizes']['medium']['dir'].'/'.$row['img_id'].'.jpg'?>" width="<?php echo $config['img_sizes']['medium']['width']?>" height="<?php echo  $config['img_sizes']['medium']['height']?>" />
											<?php } ?>
											</div>
											<div class="employee-info">
												<p class="name"><?php print htmlspecialchars($row['firstname'].' '.$row['lastname']); ?></p>
												<p class="email"><?php print htmlspecialchars($row['email']) ?></p>
												<p class="job"><?php print htmlspecialchars($row['job_name']) ?></p>
												<p class="job"><?php print htmlspecialchars($row['srv_name']) ?></p>
												<a href="edit-team.php?str=<?php echo $_GET['str']?>&team=<?php echo $row['id']; ?>" class="employee-edit edit button"><?php echo _("Editer"); ?></a>
											</div>
										</div>
									</li>
								<?php }
							} ?>
						</ul>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td>
						<input type="submit" id="del-main" name="del-team" value="<?php echo _("Supprimer"); ?>">
						<input type="submit" id="add-employee" name="add-team" value="<?php echo _("Ajouter"); ?>">
					</td>
				</tr>
			</tfoot>
		</table>
	<?php } ?>
	</div>
	</form>

<div id="popup_ria" class="maxipopup"><div class="popup_ria_drag"></div><div class="content"></div></div>

<script>
	// Disable tous les champs/boutons si on accède à cette page en lecture seul
	<?php if( isset($_GET['str']) && $_GET['str'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_EDIT') ){ ?>
		$(document).ready(function(){
			$('table').find('input, select, textarea').attr('disabled', 'disabled')
			$('table a.edit').remove();
		});
	<?php } ?>

	<?php if( isset($_GET['str']) && $tab=='images' && !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_STR') ){ ?>
		$(document).ready(function(){
			$('table').find('input, select, textarea').attr('disabled', 'disabled')
		});
	<?php } ?>

</script>


<script><!--
var old_title ='';
var old_desc ='';
var old_resp ='';
var comment_id ='';
--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file popup-plage-horaire.php
	 * 
	 * 	Ce fichier fournit une interface permettant la mise à jour des horaires d'ouverture d'un magasin
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

	require_once('dlv_store_plage.inc.php');
	define('ADMIN_PAGE_TITLE', _('Ajouter un plage horaire'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
	
	if(isset($_POST['add']) || isset($_POST['edit'])){
		if(!isset($_POST['type'])){
			$error = _('Veuillez selectionner un type de plage horaire.');
		}else{
			if(isset($_POST['heure_debut']) && $_POST['heure_debut'] != "" && isset($_POST['heure_fin']) && $_POST['heure_fin'] != "" && isset($_POST['disponibilite']) && isset($_POST['store'])){
				if($_POST['type'] == 1){
					if(!isset($_POST['day_plage'])){
						$error = _('Veuillez sélectionner une journée.');
					}else{
						if(isset($_POST['add'])){
							$add = dsp_plage_add($_POST['store'],$_POST['heure_debut'],$_POST['heure_fin'],$_POST['disponibilite'],$_POST['day_plage']);

							if(!$add){
								$error = _('Un erreur s\'est produit, plage non ajouté');
							}else{
								$success = _('La plage horaire a bien été enregistrée.');
							}
						}

						if(isset($_POST['edit'])){
							$edit = dsp_plage_update($_POST['plage'],$_POST['heure_debut'],$_POST['heure_fin'],$_POST['disponibilite'],$_POST['day_plage']);
							$_GET['plage'] = $_POST['plage'];

							if(!$edit){
								$error = _('Un erreur s\'est produit, plage non modifié');
							}else{
								$success = _('Les modifications apportées à cette plage horaire ont été enregistrées.');
							}
						}
					}
				}

				if($_POST['type'] == 0){
					if(!isset($_POST['days_select'])){
						$error = _('Veuillez sélectionner une journée.');
					}else{
						if(isset($_POST['add'])){
							$hour = dlv_stores_opening_hours_get($_POST['store']);
							foreach ($_POST['days_select'] as $day) {
								$current_hour = $_POST['heure_debut'];
								$current_deb = strtotime($current_hour);
								$current_fin = strtotime('+' . $_POST['duree'] . ' minutes',$current_deb);
								while ($current_fin <= strtotime($_POST['heure_fin'])) {
									if( (($current_deb >= strtotime($hour[$day]['morning']['start']) && $current_deb <= strtotime($hour[$day]['morning']['end'])) || 
										($current_deb >= strtotime($hour[$day]['afternoon']['start']) && $current_deb <= strtotime($hour[$day]['afternoon']['end']))) &&
										(($current_fin >= strtotime($hour[$day]['morning']['start']) && $current_fin <= strtotime($hour[$day]['morning']['end'])) ||
										($current_fin >= strtotime($hour[$day]['afternoon']['start']) && $current_fin <= strtotime($hour[$day]['afternoon']['end'])))
									  ){
										$add = dsp_plage_add($_POST['store'],date('H:i',$current_deb),date('H:i',$current_fin),$_POST['disponibilite'],$day);
//
										if(!$add){
											$error = _('Un erreur s\'est produit, plage non ajouté');
										}else{
											$success = _('Votre configuration de plages horaires a bien été prise en compte, une ou plusieurs plages horaires ont été créées en accords avec la configuration souhaitée.');
										}
									}
									$current_deb = $current_fin;
									$current_fin = strtotime('+' . $_POST['duree'] . ' minutes',$current_deb);
								}
								
							}
							
						}
					}
				}
			}else{
				$error = _('Veuillez renseigné les données.');
			}
		}
	}

	$store = isset($_REQUEST['store'])?$_REQUEST['store']:0;
	if(isset($_GET['plage'])){
		$stmt = dsp_plage_get($store,$_GET['plage']);
		if($stmt){
			$plage = ria_mysql_fetch_assoc($stmt);
		}

	}

	// Affichage des messages d'erreur
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	// Affichage des messages de validation
	if( isset($success) ){
		print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
	}
	
?>

<?php if(!isset($error) && (isset($_POST['add']) || isset($_POST['edit']))){ ?>
	<script type="text/javascript">
		parent.window.location.reload();
	</script>
<?php } ?>
<form action="/admin/config/livraison/stores/popup-plage-horaire.php" method="post">
	<input type="hidden" name="store" value="<?php print $store; ?>">
	<table cellpadding="0" cellspacing="0">
		<caption><?php print (isset($_GET['plage'])?_('Modification d\'une plage'):_('Définition d\'une nouvelle plage')); ?></caption>
		<tbody>
			<tr>
				<td><?php print _('Type'); ?><span class="mandatory">*</span></td>
				<td><input type="radio" id="default" name="type" value="0" <?php print (isset($error) && $_POST['type'] == 0)?'checked':''; ?>><label for="default"><?php print _('Défaut'); ?></label>		<input type="radio" id="personnalise" name="type" value="1" <?php print !(isset($error) && $_POST['type'] == 0)?'checked':''; ?>><label for="personnalise"><?php print _('Personnalisé'); ?></label></td>
			</tr>
			<tr class="formPerso">
				<td><?php print _('Jours'); ?><span class="mandatory">*</span></td>
				<td>
					<select style="width: auto; " name="day_plage" id="day_plage">
<?php
				$days = array( 
					1 => 'Lundi',
					2 => 'Mardi',
					3 => 'Mercredi',
					4 => 'Jeudi',
					5 => 'Vendredi',
					6 => 'Samedi',
					0 => 'Dimanche' 
				);

				foreach( $days as $key=>$day ){
					if( isset($plage) && $plage['day'] == $key){
						$selected = ' selected';
					}elseif(isset($error) && $_POST['day_plage'] == $key){
						$selected = ' selected';
					}else{
						$selected = '';
					}
					print '<option value="' . $key . '"' . $selected . '>' . $day . '</option>';
				}
?>
					</select>
				</td>
			</tr>
			<tr class="formDefault">
				<td><?php print _('Jours'); ?><span class="mandatory">*</span></td>
				<td>
<?php
				$days = array( 
					1 => 'Lundi',
					2 => 'Mardi',
					3 => 'Mercredi',
					4 => 'Jeudi',
					5 => 'Vendredi',
					6 => 'Samedi',
					0 => 'Dimanche' 
				);
				
				//ria_dump($_POST['days_select']);
				foreach( $days as $key=>$day ){
					print '<input type="checkbox" id="' . $day . $key . '" name="days_select[]" value="' . $key . '" ' . ((isset($error) && in_array($key, $_POST["days_select"]))?"checked":"") . '>';
  					print '<label for="' . $day . $key . '">' . $day . '</label>	';
				}
?>
				</td>
			</tr>
			<tr class="formDefault">
				<td><?php print _('Durée')?><span class="mandatory">*</span></td>
				<td><input type="number" class="form-control" name="duree" id="duree">  <?php print _('minutes'); ?></td>
			</tr>
			<tr>
				<td><?php print _('Heure de début'); ?><span class="mandatory">*</span></td>
				<td>
					<div class="input-group clockpickerPlage" data-placement="bottom" data-align="top" data-autoclose="true">
						<input type="text" class="form-control" name="heure_debut" value="<?php print isset($plage['debut'])?$plage['debut']:(isset($_POST['heure_debut'])?$_POST['heure_debut']:""); ?>"/>
						<span class="input-group-addon">
							<span class="glyphicon glyphicon-time"></span>
						</span>
					</div>
				</td>
			</tr>
			<tr>
				<td><?php print _('Heure de fin'); ?><span class="mandatory">*</span></td>
				<td>
					<div class="input-group clockpickerPlage" data-placement="bottom" data-align="top" data-autoclose="true">
						<input type="text" class="form-control" name="heure_fin" value="<?php print isset($plage['fin'])?$plage['fin']:(isset($_POST['heure_fin'])?$_POST['heure_fin']:""); ?>"/>
						<span class="input-group-addon">
							<span class="glyphicon glyphicon-time"></span>
						</span>
					</div>
				</td>
			</tr>
			<tr>
				<td><?php print _('Disponibilité'); ?><span class="mandatory">*</span></td>
				<td><input type="number" class="form-control" name="disponibilite" id="disponibilite" value="<?php print isset($plage['dispo'])?$plage['dispo']:(isset($_POST['disponibilite'])?$_POST['disponibilite']:1); ?>"></td>
			</tr>
		</tbody>
		<tfoot>
			<td colspan="2" style="text-align:right">
			<?php
				if(isset($_GET['plage'])){
					print '<input type="hidden" name="plage" value="' . $_GET['plage'] . '">';
					print '<input type="submit" name="edit" value="' . _('Modifier') . '">&nbsp';
				}else{
					print '<input type="submit" name="add" value="' . _('Ajouter') . '">&nbsp';
				}
			?>
			<input type="button" name="annuler" onclick="return parent.hidePopup();" value="<?php print _('Annuler'); ?>">
			</td>
		</tfoot>
	</table>
</form>

<script>
	$(document).ready(function() {
		$('.formPerso').hide();
		$('.formDefault').hide();

		if (typeof $('.clockpickerPlage').clockpicker !== "undefined") {
			$('.clockpickerPlage').clockpicker();
		}

		 $('[type=radio][name=type]').change(function () {
            if ($('input[name=type]:checked').val() == 0) {
                $('.formPerso').hide();
				$('.formDefault').show();
            }else if($('input[name=type]:checked').val() == 1){
            	$('.formPerso').show();
				$('.formDefault').hide();
            }
        });

		 $('[type=radio][name=type]').change();

	});
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
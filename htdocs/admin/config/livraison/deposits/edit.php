<?php

	/**	\file edit.php
	 *	Cette page représente la fiche d'un dépôt de stockage. Elle permet la création, la modification et la suppression d'un dépôt.
	 */

	require_once('delivery.inc.php');

	// Vérifie que l'utilisateur a bien accès à cette page
	if( $_GET['dps'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_DEPOT_VIEW');
	}else{ // $_GET['dps'] == 0
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_DEPOT_ADD');
	}
	// Vérifie que l'utilisateur a bien le droit de modifier un dépot
	if( $_GET['dps'] != 0 && isset($_POST['save']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_DEPOT_EDIT');
	}

	
	$current_dps = isset( $_GET['dps'] ) ? $_GET['dps'] : 0;
	
	if( $current_dps > 0 ){

		$deposits = prd_deposits_get( $current_dps );
		if( !$deposits ){
			header('Location: index.php');
			exit;
		}
		$dps = ria_mysql_fetch_array( $deposits );

	}else{
		$dps = array('id' => 0, 'name' => '', 'desc' => '', 'is_sync' => 0, 'is_main' => 0, 'address1' => '', 'address2'=>'', 'country'=>'', 'city' => '','phone' => '' , 'fax' =>'', 'email' => '', 'zipcode' => '', 'is_sync'=>0 );
	}
	
	unset($error);
	
	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}
	
	// Suppression
	if( isset($_POST['del']) ){
		if( $dps['is_sync'] ){
			$error = _("Vous ne pouvez pas supprimer un dépôt synchronisé.");
		}
		else if( $dps['is_main'] ){
			$error = _("Vous ne pouvez pas supprimer le dépôt principal.");
		}
		else if( !prd_deposits_del($dps['id']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression du dépôt.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}
	
	// Enregistrement
	if( isset($_POST['save']) ){
		if( $dps['is_sync'] ){
			$_POST['name'] = $dps['name'];
			$_POST['address1'] = $dps['address1'];
			$_POST['address2'] = $dps['address2'];
			$_POST['zipcode'] = $dps['zipcode'];
			$_POST['city'] = $dps['city'];
			$_POST['country'] = $dps['country'];
			$_POST['phone'] = $dps['phone'];
			$_POST['fax'] = $dps['fax'];
			$_POST['email'] = $dps['email'];			
		}
		if( !isset( $_POST['main'] ) ) $_POST['main'] = 0;
		
		// Vérification des paramètres d'entrée
		if( !isset($_POST['name'],$_POST['desc'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['phone'],$_POST['fax'],$_POST['email']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes ou invalides.\nVeuillez réessayer ou prendre contact avec l'adminisrateur.");
		}elseif( !$dps['is_sync'] && ( !trim($_POST['country']) || $_POST['country']==-1 ) ){
			$error = _("L'information du pays de localisation du dépôt n'a pas été renseigné.");
		}elseif( !trim($_POST['name']) ){
			$error = _("Le nom du dépôt est obligatoire pour pouvoir l'enregistrer.\nVeuillez l'indiquer.");
		}elseif( !isset($_GET['dps']) || $_GET['dps']=='' || $_GET['dps']==0 ){
			
			$res = prd_deposits_add($_POST['name'],$_POST['main'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['phone'],$_POST['fax'],$_POST['email'],$_POST['desc'], 0);
			if( !$res ){
				$error = _("La création du dépôt a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
			}
		}else{
			
			if( !prd_deposits_update( $current_dps,$_POST['name'],$_POST['main'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['phone'],$_POST['fax'],$_POST['email'],$_POST['desc']) ){
				$error = _("L'enregistrement du dépôt a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact avec l'administrateur");
			}
			
		}
		
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
		
		$dps = array('id' => 0, 'name' => $_POST['name'], 'country' => $_POST['country'], 'desc' => $_POST['desc'], 'is_sync' => $dps['is_sync'], 'is_main' => $_POST['main'], 'address1' => $_POST['address1'], 'address2'=>$_POST['address2'], 'city' =>$_POST['city'], 'phone' => $_POST['phone'] , 'fax' => $_POST['fax'], 'email' => $_POST['email'], 'zipcode' => $_POST['zipcode']);
	}
	
	// Défini le titre de la page
	$page_title = $dps['name']  ? htmlspecialchars($dps['name']) : 'Nouveau dépôt';
	define('ADMIN_PAGE_TITLE', $page_title.' - ' . _('Dépôts de stockage') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print view_dps_is_sync($dps).' '. $page_title; ?></h2>

	<?php
		
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

		if( $dps['is_sync'] ){
			print '<div class="notice">'._('Les champs grisés sont synchronisés avec votre ERP. Ils ne sont pas modifiables directement dans RiaShop.').'</div>';
		}
		
	?>

	<form action="edit.php?dps=<?php print $dps['id'] ?>" method="post">
		<table>
			<tbody>
				<tr><th colspan="2"><?php echo _('Général'); ?></th></tr>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($dps['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="main"><?php print _('Principal :'); ?></label></td>
					<td>
						<input type="checkbox" name="main" id="main" value="1" <?php if( $dps['is_main'] ) print 'checked="checked"'; ?> />
						<label for="main"><?php print _('Ce dépôt est le dépôt principal'); ?></label>
					</td>
				</tr>
				<tr><th colspan="2"><?php echo _('Adresse'); ?></th></tr>
				<tr>
					<td><label for="address1"><?php echo _('Adresse :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="address1" id="address1" value="<?php print htmlspecialchars($dps['address1']); ?>" maxlength="75" /></td>
				</tr>
				<tr>
					<td>&nbsp;</td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="address2" id="address2" value="<?php print htmlspecialchars($dps['address2']); ?>" maxlength="75" /></td>
				</tr>
				<tr>
					<td><label for="zipcode"><?php echo _('Code postal :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="zipcode" id="zipcode" class="zipcode" value="<?php print htmlspecialchars($dps['zipcode']); ?>" maxlength="20" /></td>
				</tr>
				<tr>
					<td><label for="city"><?php echo _('Ville :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="city" id="city" value="<?php print htmlspecialchars($dps['city']); ?>" maxlength="75" /></td>
				</tr>
				<tr>
					<td><label for="country"><span class="mandatory">*</span> <?php echo _('Pays :'); ?></label></td>
					<td><?php
						require_once('sys.countries.inc.php');

						$countries = sys_countries_get();
						if( $countries && ria_mysql_num_rows($countries) ){
							print '	<select '.($dps['is_sync'] ? 'disabled="disabled"' : '' ).' name="country" id="country">';
							print '		<option value="-1">&nbsp;</option>';
							while( $country = ria_mysql_fetch_array($countries) )
								print '	<option value="'.strtoupper2($country['name']).'" '.( mb_strtoupper($country['name'])==$dps['country'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($country['name']).'</option>';
							print '</select>';
						}
					?></td>
				</tr>
				<tr><th colspan="2"><?php echo _("Contact"); ?></th></tr>	
				<tr>
					<td><label for="phone"><?php echo _('Téléphone :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="phone" id="phone" maxlength="20" value="<?php print htmlspecialchars($dps['phone']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="fax"><?php echo _('Fax :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="text" name="fax" id="fax" maxlength="20" value="<?php print htmlspecialchars($dps['fax']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="email"><?php echo _('Email :'); ?></label></td>
					<td><input <?php $dps['is_sync'] ? print 'disabled="disabled"' :'' ?> type="email" name="email" id="email" maxlength="75" value="<?php print htmlspecialchars($dps['email']); ?>" /></td>
				</tr>
			
				<tr><th colspan="2"><?php echo _("Présentation"); ?></th></tr>
				<tr>
					<td><label for="desc"><?php echo _("Description/Présentation :"); ?></label></td>
					<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($dps['desc']); ?></textarea></td>
				</tr>
				
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return zoneCancelEdit()" />
					<?php if( $dps['id']>0 && !$dps['is_sync'] && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return depotConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>

	<script>
		// Disable tous les champs/boutons si on accède à cette page en lecture seul
		<?php if( $_GET['dps'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_EDIT') ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
			});
		<?php } ?>
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
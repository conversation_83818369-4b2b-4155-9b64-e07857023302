<?php

	/**	\file index.php
	 * 
	 * 	Cette page permet la configuration des dépôts de livraison
	 * 
	 */

	require_once('prd/deposits.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_DEPOT');

	// Ajout
	if( isset($_POST['add']) ){
		header('Location: edit.php?dps=0');
		exit;
	}

	// Exportation
	if( isset($_POST['export']) ){
		if( isset($_POST['deposits']) && is_array($_POST['deposits']) && count($_POST['deposits']) ){
			$data_dps = '';
			foreach( $_POST['deposits'] as $dps ){
				$data_dps .= ( trim($data_dps) != '' ? '&' : '' ).'dps[]='.$dps;
			}

			header('Location: /admin/config/livraison/deposits/export.php?'.$data_dps);
			exit;
		}else{
			$_SESSION['info-dps-export'] = true;
			header('Location: /admin/config/livraison/deposits/index.php');
			exit;
		}
	}

	unset($error);

	// Suppression dans la liste
	if( isset($_POST['delete']) && isset($_POST['deposits']) ){
		$error = '';
		foreach( $_POST['deposits'] as $z ){
			$tmp = prd_deposits_get( $z );
			if( !$tmp ) continue;
			$tmp = ria_mysql_fetch_array( $tmp );
			
			if( $tmp['is_main'] ){
				$error .= _('Le dépôt principal ne peut être supprimé. ');
				continue;
			}
			if( $tmp['is_sync'] ) {
				$error .= _('Les dépôts synchronisés ne peuvent être supprimé. ');
				continue;
			}
			
			if( !prd_deposits_del($z) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	$checkbox = true;

	$colspan = $checkbox ? 3 : 2;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Dépôts de stockage') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
	
	// Charge la liste des dépôts de stockage
	$deposits = prd_deposits_get();
	$deposits_count = ria_mysql_num_rows($deposits);
?>
	<form action="index.php" method="post">

	<h2>
		<?php echo _('Dépôts de stockage'); ?> (<?php print ria_number_format($deposits_count) ?>)

		<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_ADD') ){ ?>					
			<input type="submit" name="add" class="float-right" value="<?php echo _("Ajouter"); ?>" title="<?php print _('Ajouter un dépôt de stockage') ?>" />
		<?php } ?>
	</h2>

	<?php
		if( isset($error) && $error != '' ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

		if( isset($_SESSION['info-dps-export']) ){
			print '<div class="notice">' . _("L'export des stocks nécessite la sélection d'un ou plusieurs dépôts.") . '</div>';	
			unset( $_SESSION['info-dps-export'] );
		}
	?>

	<table id="table-deposits" class="checklist" style="width: 100%">
		<thead>
			<tr>
				<?php if( $checkbox ){ ?>
				<th id="cat-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<?php } ?>
				<th id="cat-name"><?php echo _("Nom"); ?></th>
				<th id="cat-principal"><?php echo _("Principal"); ?> ?</th>
			</tr>
		</thead>
		<tbody>
		<?php		
			if( $deposits===false || !ria_mysql_num_rows($deposits) ){
				print '<tr><td colspan="'.$colspan.'">' . _("Aucun dépôts") . '</td></tr>';
			}else{
				$count = ria_mysql_num_rows($deposits);
				while( $r = ria_mysql_fetch_array($deposits) ){
					if( $checkbox ){
						print '<tr><td headers="cat-sel"><input type="checkbox" class="checkbox" name="deposits[]" value="'.$r['id'].'" /></td>';
					}
					if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_VIEW') ){
						print '<td headers="cat-name">'.view_dps_is_sync($r).' <a href="edit.php?dps='.$r['id'].'" title="' . _("Editer le contenu de ce dépôt") . '">'.htmlspecialchars($r['name']).'</a></td>';
					}else{
						print '<td headers="cat-name">'.view_dps_is_sync($r).' '.htmlspecialchars($r['name']).'</td>';					
					}
					print '<td>'.($r['is_main'] ? _('Oui'):_('Non')).'</td>';
					print '</tr>';
				}
			}
		?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="<?php print $colspan; ?>">
					<?php if( ria_mysql_num_rows($deposits) ){
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_DEL') ){ ?>
						<input type="submit" name="delete" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les dépôts sélectionnées"); ?>" onclick="return window.confirm(<?php print _('Vous êtes sur le point de supprimer les dépôts sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?\n\n'); ?>);" />
						<?php } ?>
						<input type="submit" name="export" class="btn-del float-left" value="<?php echo _("Exporter"); ?>" title="<?php echo _("Exporter le stock de tous les produits"); ?>" />
					<?php } ?>

					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_DEPOT_ADD') ){ ?>					
					<input type="submit" name="add" value="<?php echo _("Ajouter"); ?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
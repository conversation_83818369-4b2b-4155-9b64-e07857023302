<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_DEPOT');

	header('Content-disposition: attachment; filename="export-produits.csv"');
	header('Content-type: application/octetstream; charset=utf-8');
	header('Pragma: no-cache');
	header('Expires: 0');
	
	$ar_lines = array(
		array(_('Référence'), _('Désignation'), _('Dépôt'), _('Disponible'), _('Réservé'), _('Commandé'), _('Préparation'), _('Mini'), _('Maxi'))
	);

	if( isset($_GET['dps']) && is_array($_GET['dps']) && count($_GET['dps']) ){
		$ar_products = array();

		$r_product = prd_products_get_simple();
		if( $r_product ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$ar_products[ $product['id'] ] = $product;
			}
		}

		if( count($ar_products) ){
			$r_stock = prd_dps_stocks_get( 0, $_GET['dps'] );
			if( $r_stock ){
				while( $stock = ria_mysql_fetch_assoc($r_stock) ){
					if( !array_key_exists($stock['prd'], $ar_products) ){
						continue;
					}

					$ar_lines[] = array(
						$ar_products[ $stock['prd'] ]['ref'],
						$ar_products[ $stock['prd'] ]['title'],
						$stock['dps_name'],
						$stock['qte'],
						$stock['res'],
						$stock['com'],
						$stock['prepa'],
						$stock['mini'],
						$stock['maxi']
					);
				}
			}
		}
	}

	echo "\xEF\xBB\xBF"; // BOM UTF-8
	
	if( is_array($ar_lines) && count($ar_lines) ){
		foreach( $ar_lines as $line ){
			$tmp = '';
			foreach( $line as $c ){
				$tmp .= ( trim($tmp)!='' ? ';' : '' ).'"'.str_replace('"', '""', $c).'"';
			}

			print $tmp."\n";
		}
	}

	exit;

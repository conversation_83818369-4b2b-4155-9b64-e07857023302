<?php
require_once('delivery.inc.php');

// Vérifie que l'utilisateur en cours à accès à cette page
if( isset($_GET['srv']) ){
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_EDIT');
}else{ // !isset($_GET['srv'])
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_ADD');
}

$errors = array();
do{
	//sauvegarde
	if( isset($_POST['save-main']) ){
		if( !is_string($_POST['name']) || trim($_POST['name']) =='' ){
			$errors[] = _('Un nom doit être renseigné.');
		}
		$name = htmlspecialchars($_POST['name']);
		$desc = htmlspecialchars($_POST['desc']);
		
		if( count($errors) ){
			break;
		}
		
		if( isset($_GET['srv']) ){
			if( !dlv_store_services_update($_GET['srv'], $name, $desc ) ){
				$errors[] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
				break;
			}
			header('location: /admin/config/livraison/jobs/services/index.php?success=true');
			exit;
		}
		
		if( !dlv_store_services_add($name, $desc) ){
			$errors[] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
			break;
		}
		header('location: /admin/config/livraison/jobs/services/index.php?success=true');
		exit;
	}
	// suppression
	if( isset($_POST['del-main'], $_GET['srv']) ){
		if(!dlv_store_services_del($_GET['srv'])){
			$errors[] = _('Une erreur est survenue lors de la suppression d\'un service.');
			break;
		}
		header('location: /admin/config/livraison/jobs/services/index.php?success=del');
		exit;
	}
	if(isset($_POST['cancel-main']) ){
		header('location: /admin/config/livraison/jobs/services/index.php');
		exit;
	}
	
	//affichage
	if( isset($_POST['name'])){
		$data = $_POST;
	}elseif( isset($_GET['srv']) ){
		$res = dlv_store_services_get($_GET['srv']);
		if( !$res ){
			header('location: /admin/config/livraison/jobs/services/index.php');
			exit;
		}
		
		$data = ria_mysql_fetch_assoc($res);
	}else{
		$data = array(
			'name' => '',
			'desc' => ''
		);
	}
}while(0);

$page_title = trim($data['name']) ? htmlspecialchars($data['name']) : _('Nouveau service');
define('ADMIN_PAGE_TITLE', $page_title.' - '._('Service') . ' - ' . _('Magasins') . ' - ' . _('Livraison des commandes'));
require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>
	<?php
		if( count($errors) ){
			echo '<div class="notice error">';
			foreach($errors as $error){
				echo '<p>'.$error.'</p>';
			}
			echo '</div>';
		}
	?>
	<form action="edit.php<?php echo (isset($_GET['srv']) ? '?srv='.$_GET['srv'] : '')?>" method="post">
		<table id="table-services-edit">
			<tbody>
				<tr>
					<td><label for="name"><?php echo _('Nom :'); ?></label></td>
					<td><input type="text" id="name" name="name" value="<?php echo (isset($data['name']) ? htmlspecialchars($data['name']) : '')?>"></td>
				</tr>
				<tr>
					<td><label for="desc"><?php echo _('Description du poste :'); ?></label></td>
					<td><textarea name="desc" id="desc" cols="50" rows="10"><?php echo (isset($data['desc']) ? htmlspecialchars($data['desc']) : '')?></textarea></td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td class="tdleft">
						<?php if(isset($_GET['srv']) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_DEL') ){?><input type="submit" name="del-main" value="<?php echo _("Supprimer"); ?>"> <?php } ?>
					</td>
					<td>
						
						<input type="submit" name="save-main" value="<?php echo (isset($_GET['srv']) ? _('Enregistrer'): _('Ajouter')) ?>">
						<input type="submit" name="cancel-main" value="Annuler">
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
	

<?php
require_once('admin/skin/footer.inc.php');
?>
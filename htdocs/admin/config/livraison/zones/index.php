<?php

	/**	\file index.php
	 *	Cette page affiche la liste des zones de livraison
	 */

	require_once('delivery.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_ZONE');

	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	$rovr = cfg_overrides_get( $config['wst_id'], array(), 'multiple_dlv_rule' );
	if( $rovr ){
		while( $ovr = ria_mysql_fetch_array($rovr) ){
			$config[ $ovr['code'] ] = $ovr['value'];
		}
	}

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['zone']) ){
		foreach( $_POST['zone'] as $z )
			if( !dlv_zones_del($z) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression d'une des zones.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	//Modification ou ajout de la variable de config multiple-dlv-rule
	if( isset($_POST['save-multiple-dlv-rule']) && isset($_POST['multiple-rules-select'])){
		$websites = wst_websites_get_array();
		foreach ($websites as $wst_id) {
			if (!cfg_overrides_set_value('multiple_dlv_rule', $_POST['multiple-rules-select'], $wst_id)) {
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre choix'.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_DEL');
	$colspan = $checkbox ? 3 : 2;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Zones de livraison') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
	
	// Charge la liste des zones de livraison
	$zones = dlv_zones_get();
	$zones_count = ria_mysql_num_rows( $zones );
?>
	<form action="index.php" method="post">

	<h2>
		<?php echo _("Zones de livraison"); ?> (<?php print ria_number_format($zones_count) ?>)

		<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_ADD') ){ ?>
			<input type="submit" name="add" class="btn-add float-right" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter une zone de livraison"); ?>" onclick="return zoneAdd()" />
		<?php } ?>

	</h2>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
		
		$multi_dlv_rule = isset($config['multiple_dlv_rule']) && in_array($config['multiple_dlv_rule'], array('min', 'max', 'sum')) ? $config['multiple_dlv_rule'] : 'max';
	?>

	<?php if (isset($config['dlv_active_port_config']) && $config['dlv_active_port_config'] ){ ?>
		<p><?php echo _('Choisissez une règle en cas de frais de port multiples :'); ?></p>
		<select id="multiple-delivery-rules" name="multiple-rules-select">
			<option id="dlv-min" value="min" <?php print $multi_dlv_rule=="min" ? 'selected="selected"' : '' ; ?>><?php echo _("Les frais de port auront la valeur de la zone la moins chère"); ?></option>
			<option id="dlv-max" value="max" <?php print $multi_dlv_rule=="max" ? 'selected="selected"' : '' ; ?>><?php echo _("Les frais de port auront la valeur de la zone la plus chère"); ?></option>
			<option id="dlv-sum" value="sum" <?php print $multi_dlv_rule=="sum" ? 'selected="selected"' : '' ; ?>><?php echo _("Les frais de port vaudront la somme des frais de chaque zone"); ?></option>
		</select>
		<input title="Sauvegarder les modifications" type="submit" class="action" value="<?php echo _("Enregistrer"); ?>" name="save-multiple-dlv-rule"/> 
	<?php } ?>

	<table id="table-config-zones" class="checklist" style="width: 100%">
		<thead>
			<tr>
				<?php if( $checkbox ){ ?>
				<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<?php } ?>
				<th id="name"><?php echo _("Nom de la zone"); ?></th>
				<th id="is-active"><?php echo _("Activée ?"); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( !ria_mysql_num_rows($zones) ){
					print '<tr><td colspan="'.$colspan.'">' . _("Aucune zone de livraison") . '</td></tr>';
				}else{
					while( $r = ria_mysql_fetch_array($zones) ){
						print '	<tr>';
						if( $checkbox ){
							print '	<td headers="select"><input type="checkbox" class="checkbox" name="zone[]" value="'.$r['id'].'" /></td>';
						}
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_VIEW') ){
							print '	<td headers="name"><a href="edit.php?zone='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
						}else{
							print '	<td headers="name">'.htmlspecialchars($r['name']).'</td>';						
						}
						print '	<td headers="is-active">'.( $r['is_active'] ? _('Oui') : _('Non') ).'</td>
							</tr>';
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr><td colspan="<?php print $colspan; ?>">
				<?php if( ria_mysql_num_rows($zones) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_DEL') ){ ?>
				<input type="submit" name="del" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les zones de livraison sélectionnées"); ?>" onclick="return zoneConfirmDelList()" />
				<?php } ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter une zone de livraison"); ?>" onclick="return zoneAdd()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
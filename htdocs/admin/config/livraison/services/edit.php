<?php

	/**	\file edit.php
	 *	Cette page sert de fiche aux services de livraison. Elle permet la création, la modification et la suppression
	 *	d'un service de livraison.
	 */

	require_once('delivery.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_SRV');
	
	// Vérifie que l'utilisateur a bien accèder à cette page
	if( isset($_GET['srv']) && $_GET['srv'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_SRV_VIEW');
	}elseif( !isset($_GET['srv']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_SRV_ADD');
	}
	// Vérifie que l'utilisateur a bien le droit de modifier un service de livraison
	if( isset($_POST['save']) && isset($_GET['srv']) && $_GET['srv']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_SRV_EDIT');
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie la validité de l'identifiant de service passé en paramètre
	if( isset($_GET['srv']) && $_GET['srv']!=0 ){
		if( !dlv_services_exists($_GET['srv']) ){
			header('Location: index.php');
			exit;
		}
	}

	// Recherche l'onglet en cours de consultation
	$tab = '';
	if( isset($_POST['tabGeneral']) ){
		header('Location: edit.php?srv='.$_GET['srv'].'&tab=general');
		exit;
	}elseif( isset($_POST['tabFields']) ){
		header('Location: edit.php?srv='.$_GET['srv'].'&tab=fields');
		exit;
	}elseif( isset($_GET['tab']) ){
		$tab = $_GET['tab'];
	}
	
	// Par défaut, l'ouverture se fait sur l'onglet Général
	if( !in_array($tab,array('general','fields')) ){
		$tab = 'general';
	}

	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	if( $tab == 'general' ){
		// Enregistrement
		if( isset($_POST['save']) ){
			
			$_POST['active'] = isset($_POST['active']);
			$_POST['accept_consign'] = isset($_POST['accept_consign']);
			$_POST['zones'] = isset($_POST['zones']) ? $_POST['zones'] : array();
			$type_id = isset($_POST['type_id']) && trim($_POST['type_id']) ? $_POST['type_id'] : null;
			$fld_id = isset($_POST['field']) && trim($_POST['field']) ? $_POST['field'] : null;
			$_POST['prd_ref'] = isset($_POST['prd_ref']) && trim($_POST['prd_ref']) ? $_POST['prd_ref'] : null;
			if( !isset($_POST['name']) || !trim($_POST['name']) ){
				$error = _("Veuillez indiquer le nom du service de livraison.");
			}elseif( !isset($_POST['desc'],$_POST['url-site'],$_POST['url-colis'],$_POST['alert-msg']) ){
				$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}elseif( !is_null($_POST['prd_ref']) && !prd_products_is_port($_POST['prd_ref'])){
				$error = _('La référence du produit que vous avez renseignée ne fait pas référence à un produit frais de port.')."\n"._('Veuillez réessayer ou prendre contact avec l\'administrateur.');
			}elseif( isset($_GET['srv']) && $_GET['srv']==0 ){
				// Ajout
				$zones = array();
				foreach( $_POST['zones'] as $zone ){
					$zones[] = array(
						'id' => $zone,
						'hours-min' => isset($_POST['hours-min'][$zone]) ? round($_POST['hours-min'][$zone]*24) : '',
						'hours-max' => isset($_POST['hours-max'][$zone]) ? round($_POST['hours-max'][$zone]*24) : '',
						'price-ht' => isset($_POST['price'][$zone]) ? ($_POST['price'][$zone] / _TVA_RATE_DEFAULT) : 0,
						'free-ht' => isset($_POST['free'][$zone]) ? ($_POST['free'][$zone] / _TVA_RATE_DEFAULT) : 0
					);
				}

				$srv_id = dlv_services_add( 
					$_POST['name'],
					$_POST['desc'],
					$_POST['url-site'],
					$_POST['url-colis'],
					$_POST['alert-msg'],
					$_POST['active'],
					$zones,
					$_POST['price-ttc'],
					$_POST['dealer-price-ht'],
					$_POST['dealer-free-ht'],
					$_POST['weight_min'],
					$_POST['weight_max'],
					$_POST['ord_amount_min'],
					$_POST['ord_amount_max'], 
					$_POST['accept_consign'],
					$type_id,
					$fld_id,
					$_POST['prd_ref']
				);
				if (!$srv_id) {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du service.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}elseif( isset($_GET['srv']) && $_GET['srv']>0 ){
				$srv_id = $_GET['srv'];
				
				if( $lng == $config['i18n_lng'] ){
					
					// Ajout
					$zones = array();
					foreach( $_POST['zones'] as $zone ){
						$zones[] = array(
							'id' => $zone,
							'hours-min' => isset($_POST['hours-min'][$zone]) ? round($_POST['hours-min'][$zone]*24) : '',
							'hours-max' => isset($_POST['hours-max'][$zone]) ? round($_POST['hours-max'][$zone]*24) : '',
							'price-ht' => isset($_POST['price'][$zone]) ? ($_POST['price'][$zone] / _TVA_RATE_DEFAULT) : 0,
							'free-ht' => isset($_POST['free'][$zone]) ? ($_POST['free'][$zone] / _TVA_RATE_DEFAULT) : 0
						);
					}

					// Modification
					if( !dlv_services_update(
						$_GET['srv'],
						$_POST['name'],
						$_POST['desc'],
						$_POST['url-site'],
						$_POST['url-colis'],
						$_POST['alert-msg'],
						$_POST['active'],
						$zones,
						$_POST['price-ttc'],
						$_POST['dealer-price-ht'],
						$_POST['dealer-free-ht'],
						$_POST['weight_min'],
						$_POST['weight_max'],
						$_POST['ord_amount_min'],
						$_POST['ord_amount_max'], 
						$_POST['accept_consign'],
						$type_id,
						$fld_id,
						$_POST['prd_ref']
					)) {
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du service.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
					}
				}else{
					// Array ( [srv-id] => 411 [save] => Enregistrer [name] => name en [desc] => [url-site] => [url-colis] => [alert-msg] => ) 
					$values = array(
						_FLD_SRV_NAME 		=> $_POST['name'],
						_FLD_SRV_DESC 		=> $_POST['desc'],
						_FLD_SRV_URL_SITE 	=> $_POST['url-site'],
						_FLD_SRV_URL_COLIS 	=> $_POST['url-colis'],
						_FLD_SRV_ALERT_MSG 	=> $_POST['alert-msg']
					);

					// Traduction du service de livraison
					if( isset($_GET['srv']) && is_numeric($_GET['srv']) && $_GET['srv'] > 0 ){
						if( !fld_translates_add($_GET['srv'], $_GET['lng'], $values) ){
							$error = _("Une erreur inattendue s'est produite lors de la traduction de ce service de livraison.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}
				}
			}

			if( isset($srv_id) && is_numeric($srv_id) && $srv_id > 0 ){
				if( is_array($_FILES) && sizeof($_FILES) ){
					dlv_services_image_upload( $srv_id, 'image' );
				}
			}

			// Section réservé au formule essentiel / business du web
			if( in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){
				if( $_POST['type_id'] == _SRV_TYPE_CMT ){
					if( !ria_array_key_exists(['rly_presta', 'rly_login', 'rly_password'], $_POST) ){
						$error = _('Une ou plusieurs informations obligatoires sont manquantes.');
					}elseif( trim($_POST['rly_presta']) == '' || trim($_POST['rly_login']) == '' || trim($_POST['rly_password']) == ''){
						$error = _('Une ou plusieurs informations obligatoires sont manquantes.');
					}

					if( !isset($error) ){
						if(
							!dlv_relay_types_used_add($_POST['rly_presta'], $_POST['rly_login'], $_POST['rly_password'])
							|| !dlv_services_update_presta_id($srv_id, $_POST['rly_presta'])
						){
							$error = _("Une erreur inattendue s'est produite lors de la mise à jour des informations de connexion au prestataire externe.");
						}
					}
				}
			}

			if( !isset($error) ){
				header('Location: edit.php?srv='.$srv_id.'&lng='.$lng);
				exit;
			}
		}

		if( isset($_POST['del-srv-img']) ){
			if( !dlv_services_image_del($_GET['srv']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de l'image. \nVeuillez réessayer ou prendre contact pour nous signaler ce problème.");
			}

			if( !isset($error) ){
				header('Location: edit.php?srv='.$_GET['srv'].'&lng='.$lng);
				exit;
			}
		}

		// Suppression
		if( isset($_POST['del']) ){
			if( !dlv_services_del($_GET['srv']) )
				$error = _("Une erreur inattendue s'est produite lors de la suppression du service.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			else{
				header('Location: index.php');
				exit;
			}
		}
	}

	// Action sur l'onglet "Avancés"
	if( isset($_GET['srv']) && is_numeric($_GET['srv']) && $_GET['srv'] > 0 ){
		view_admin_tab_fields_actions( CLS_DLV_SERVICE, $_GET['srv'], $lng );
	}

	if( !(isset($_GET['srv']) && is_numeric($_GET['srv']) && $_GET['srv'] > 0) ){
		$lng = $config['i18n_lng'];
	}
	
	// Initialise un service de livraison vide
	$srv = array(
		'id' => 0,
		'name' => '',
		'desc' => '',
		'url-site' => '',
		'url-colis' => '',
		'alert-msg' => 'Votre commande vient d\'être expédiée.'."\n".'Elle a été prise en charge par notre transporteur %nom%.',
		'is_active' => false,
		'zones' => array(),
		'price-ttc' => '',
		'dealer-price-ht' => '',
		'dealer-free-ht' => '',
		'weight_min' => '',
		'weight_max' => '',
		'ord_amount_max' => '',
		'ord_amount_min' => '',
		'accept_consign' => true,
		'img_id' => null,
		'type_id' => null,
		'prd_ref' => null
	);
	$page_title = _('Nouveau service de livraison');
	// Charge le service de livraison demandé
	if( isset($_GET['srv']) && is_numeric($_GET['srv']) && $_GET['srv']>0 ){
		$srv = ria_mysql_fetch_array(dlv_services_get($_GET['srv']));
		$srv['zones'] = array();
		
		$zones = dlv_zones_get( 0, true, $srv['id'] );
		if( $zones && ria_mysql_num_rows($zones) ) {
			while( $r = ria_mysql_fetch_array($zones) ){
				$srv['zones'][ $r['id'] ] = array( 'hours-min' => $r['hours-min'], 'hours-max'=>$r['hours-max'], 'price-ht'=>$r['price-ht'], 'free-ht'=>$r['free-ht'] );
			}
		}

		if( $lng != $config['i18n_lng'] ){
			$srv = i18n::getTranslation( CLS_DLV_SERVICE, $srv, true, $lng );
		}
		$page_title = htmlspecialchars($srv['name']);
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Services de livraison') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo $page_title; ?></h2>

	<?php

		// Affiche le menu de langue
		if( isset($_GET['srv']) && is_numeric($_GET['srv']) && $_GET['srv']>0 ){
			print view_translate_menu( '/admin/config/livraison/services/edit.php?srv='.$_GET['srv'], $lng );
		}

		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

	?>

	<form action="edit.php?srv=<?php print $srv['id'] ?><?php print $lng != $config['i18n_lng'] ? '&amp;lng='.$lng : ''; ?>&amp;tab=<?php print $tab; ?>" method="post" enctype="multipart/form-data">
		<input type="hidden" name="srv-id" id="srv-id" value="<?php print $srv['id']; ?>" />

		<?php if( view_admin_show_tab_fields( CLS_DLV_SERVICE, $srv['id'] ) ){ ?>
		
			<?php if( !isset($_GET['srv']) || (is_numeric($srv['id']) && $srv['id'] > 0) ){ ?>
			<ul class="tabstrip">
				<li><input type="submit" name="tabGeneral" value="<?php echo _("Général"); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
				<?php if( view_admin_show_tab_fields( CLS_DLV_SERVICE, $srv['id'] ) ){ ?>
				<li><input type="submit" name="tabFields" value="<?php echo _("Avancé"); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
				<?php } ?>
			</ul>
			<?php }?>

			<div id="tabpanel">
		<?php }?>

			<?php if( $tab == 'general' ){ ?>
			<table id="table-conf-livr-service-edit">
				<tbody>
					<tr>
						<th colspan="5"><?php print _('Général'); ?></th>
					</tr>
					<tr>
						<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
						<td colspan="4"><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($srv['name']); ?>" /></td>
					</tr>
					<tr>
						<td><label for="type_id"> <?php print _('Type :') ?></label></td>
						<td colspan="4">
							<select name="type_id">
								<option value=""><?php print _('Choisissez un type de service') ?></option>
							<?php 

							// Si l'on est sur une instance essentiel ou business pour le web alors les types de service sont limitées à domiciles / points relais
							$select_type_id = 0;
							if( in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){
								$select_type_id = [_SRV_TYPE_CMT, _SRV_TYPE_DOM];
							}

							$r_srv_types = dlv_services_types_get( $select_type_id );
							if ($r_srv_types && ria_mysql_num_rows($r_srv_types)) {
								$group = null;
								while ($srv_type = ria_mysql_fetch_assoc($r_srv_types)) {
									if (strtoupper($group) != strtoupper($srv_type['group'])) {
										if (!is_null($group)) {
											print '</optgroup>';
										}
										$group = $srv_type['group'];
										print '<optgroup label="'.htmlspecialchars($group).'">';
										$group = strtoupper($group);
									}

									print '<option value="'. htmlspecialchars($srv_type['id']).'"'
											. ($srv['type_id'] == $srv_type['id'] ? ' selected="selected"' : '')
										.'>' 
										. htmlspecialchars($srv_type['name']) 
										. '</option>';
								}
							}
							?>
							</select>
						</td>
					</tr>

					<?php
						// Section réservé au formule essentiel / business du web
						if( in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){
							// Cette section est toujours présent, mais n'est rendu visible que si le type de service est points relais
							// ou bien que ce type a été choisi dans le select au dessus

							// Charge les prestataires externes gérant des points relais
							$r_presta = dlv_relay_types_get();

							$ar_login = [ 'login' => '', 'password' => ''];
							if( is_numeric($srv['presta_id']) && $srv['presta_id'] > 0 ){
								$r_used = dlv_relay_types_used_get( $srv['presta_id'] );
								if( $r_used && ria_mysql_num_rows($r_used) ){
									$ar_login = ria_mysql_fetch_assoc($r_used);
								}
							}

							print '<tr class="rly_option '.( $srv['type_id'] != _SRV_TYPE_CMT ? 'hide' : '' ).'">'
								.'<td><label for="rly_presta"><span class="mandatory">*</span> '._('Prestataire :').'</label></td>'
								.'<td colspan="4">'
									.'<select name="rly_presta" id="rly_presta">
										.<option value="">'._('Sélectionner un prestataire').'</option>';

							if( $r_presta ){
								while( $presta = ria_mysql_fetch_assoc($r_presta) ){
									print '<option value="'.$presta['id'].'" '.( $srv['presta_id'] == $presta['id'] ? 'selected="selected"' : '' ).'>'
											.htmlspecialchars( $presta['name'] )
										.'</option>';
								}
							}

							print '</select>'
									.'</td>'
								.'</tr>'
								.'<tr class="rly_option '.( $srv['type_id'] != _SRV_TYPE_CMT ? 'hide' : '' ).'">'
									.'<td><label for="rly_login"><span class="mandatory">*</span> '._('Login :').'</label></td>'
									.'<td colspan="4"><input type="text" name="rly_login" id="rly_login" value="'.htmlspecialchars($ar_login['login']).'" /></td>'
								.'</tr>'
								.'<tr class="rly_option '.( $srv['type_id'] != _SRV_TYPE_CMT ? 'hide' : '' ).'">'
									.'<td><label for="rly_password"><span class="mandatory">*</span> '._('Mot de passe :').'</label></td>'
									.'<td colspan="4"><input type="text" name="rly_password" id="rly_password" value="'.htmlspecialchars($ar_login['password']).'" /></td>'
								.'</tr>';
						}
					?>

					<tr>
						<td><label for="desc"><?php echo _('Description :'); ?></label></td>
						<td colspan="4"><textarea name="desc" id="desc" rows="5" cols="40" class="short"><?php print htmlspecialchars($srv['desc']); ?></textarea></td>
					</tr>
					<tr>
						<td><label for="url-site"><?php echo _('Url du site Internet :'); ?></label></td>
						<td colspan="4"><input type="text" name="url-site" id="url-site" maxlength="75" value="<?php print htmlspecialchars($srv['url-site']); ?>" /></td>
					</tr>
					<tr>
						<td><label for="url-colis"><?php echo _('Url de suivi des colis :'); ?></label></td>
						<td colspan="4"><input type="text" name="url-colis" id="url-colis" maxlength="255" value="<?php print htmlspecialchars($srv['url-colis']); ?>" /></td>
					</tr>
					<tr>
						<td><label for="alert-msg"><?php echo _('Message de notification :'); ?></label><br /><sub><?php echo _("(avis d'expédition)"); ?></sub></td>
						<td colspan="4"><textarea name="alert-msg" id="alert-msg" class="short" rows="5" cols="40"><?php print htmlspecialchars($srv['alert-msg']); ?></textarea></td>
					</tr>
					<?php if( $lng == $config['i18n_lng'] ){ ?>
					<tr>
						<td><label for="active"><?php echo _('Activé :'); ?></label></td>
						<td colspan="4"><input type="checkbox" class="checkbox" name="active" id="active" <?php print $srv['is_active'] ? 'checked="checked"' : '' ?> /> <label for="active"><?php echo _("Proposer ce service de livraison dans la boutique"); ?></label></td>
					</tr>
					<tr>
						<td><label for="accept_consign"><?php echo _('Consigne de livraison :'); ?></label></td>
						<td colspan="4"><input type="checkbox" class="checkbox" name="accept_consign" id="accept_consign" <?php print $srv['accept_consign'] ? 'checked="checked"' : '' ?> /> <label for="accept_consign"><?php echo _("Permettre la saisie de consigne de livraison"); ?></label></td>
					</tr>
					<tr>
						<td><label for="price-ttc"><?php echo _('Montant forfaitaire :'); ?></label></td>
						<td colspan="4"><input type="text" name="price-ttc" id="price-ttc" class="price" maxlength="8" <?php if( $srv['price-ttc'] ) print 'value="'.number_format($srv['price-ttc'],2,',',' ').'"'; ?> /> &euro; <abbr title="Toutes Taxes Comprises"><?php echo _("TTC"); ?></abbr></td>
					</tr>
					<tr>
						<td><label for="dealer-price-ht"><?php echo _('Montant forfaitaire revendeurs :'); ?></label></td>
						<td colspan="4"><input type="text" name="dealer-price-ht" id="dealer-price-ht" class="price" maxlength="8" <?php if( $srv['dealer-price-ht'] ) print 'value="'.number_format($srv['dealer-price-ht'],2,',',' ').'"'; ?> /> &euro; <abbr title="Hors Taxes">HT</abbr></td>
					</tr>
					<tr>
						<td><label for="dealer-free-ht"><?php echo _('Franco de port :'); ?></label></td>
						<td colspan="4"><input type="text" name="dealer-free-ht" id="dealer-free-ht" class="price" maxlength="8" <?php if( $srv['dealer-free-ht'] ) print 'value="'.number_format($srv['dealer-free-ht'],2,',',' ').'"'; ?> /> &euro; <abbr title="Hors Taxes">HT</abbr></td>
					</tr>
					<tr>
						<td><label for="weight_min"><?php echo _('Poids minimum :'); ?></label></td>
						<td colspan="4"><input type="text" name="weight_min" id="weight_min" class="price" maxlength="15" <?php if( $srv['weight_min'] ) print 'value="'.$srv['weight_min'].'"'; ?> /> <abbr title="Grammes">g</abbr></td>
					</tr>
					<tr>
						<td><label for="weight_max"><?php echo _('Poids maximum :'); ?></label></td>
						<td colspan="4"><input type="text" name="weight_max" id="weight_max" class="price" maxlength="15" <?php if( $srv['weight_max'] ) print 'value="'.$srv['weight_max'].'"'; ?> /> <abbr title="Grammes">g</abbr></td>
					</tr>
					<tr>
						<td><label for="ord_amount_min"><?php echo _('Montant minimum de commande :'); ?></label></td>
						<td colspan="4"><input type="text" name="ord_amount_min" id="ord_amount_min" class="price" maxlength="8" <?php if( $srv['ord_amount_min'] ) print 'value="'.number_format($srv['ord_amount_min'],2,',',' ').'"'; ?> /> &euro; <abbr title="Hors Taxes">HT</abbr></td>
					</tr>
					<tr>
						<td><label for="ord_amount_max"><?php echo _('Montant maximum de commande :'); ?></label></td>
						<td colspan="4"><input type="text" name="ord_amount_max" id="ord_amount_max" class="price" maxlength="8" <?php if( $srv['ord_amount_max'] ) print 'value="'.number_format($srv['ord_amount_max'],2,',',' ').'"'; ?> /> &euro; <abbr title="Hors Taxes">HT</abbr></td>
					</tr>
					<tr>
						<td><label for="ord_amount_max"><?php echo _('Logo :'); ?></label></td>
						<td colspan="4" class="padding-bottom-10">
							<?php
								if( is_numeric($srv['img_id']) && $srv['img_id'] > 0 ){
									$size = $config['img_sizes']['small'];
									print '
										<img src="/images/products/'.$size['dir'].'/'.$srv['img_id'].'.'.$size['format'].'" width="'.$size['width'].'" height="'.$size['height'].'" /><br />
										<input class="btn-del" type="submit" name="del-srv-img" value="' . _('Supprimer') . '" />';
								}else{
									print '<input class="btn-del" type="file" name="image" value="" />';
								}
							?>
						</td>
					</tr>
					<tr>
						<td><label for="prd_ref"><?php echo _('Référence du produit frais de port :'); ?></label></td>
						<td colspan="4"><input type="text" name="prd_ref" id="prd_ref" maxlength="30" <?php if( $srv['prd_ref'] ) print 'value="'.$srv['prd_ref'].'"'; ?> /></td>
					</tr>
					<?php if( !in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){ ?>
					<tr><th colspan="5"><?php echo _("Zones de livraison"); ?></th></tr>
					<tr class="th-head-second thead-none">
						<th><?php echo _("Zone"); ?></th>
						<th class="align-right col200px"><?php echo _("Délai minimum de livraison"); ?></th>
						<th class="align-right col200px"><?php echo _("Délai maximum de livraison"); ?></th>
						<th class="align-right col145px"><?php echo _("Montant forfaitaire"); ?></th>
						<th class="align-right col145px"><?php echo _("Franco de port"); ?></th>
					</tr>
					<?php
						$zones = dlv_zones_get( 0, true );
						if( !$zones || !ria_mysql_num_rows($zones) ){
							print '<tr><td colspan="5">' . _("Aucune zone de livraison n\'est pour le moment active. Vous pouvez gérer les zones depuis") . ' <a href="/admin/config/livraison/zones/index.php">' . _("Configuration > Livraison > Zones") . '</a>.</td></tr>';
						}
						while( $r = ria_mysql_fetch_array($zones) ){
							$checked = $hours_min = $hours_max = '';
							$price = $free = 0;
							
							if( isset($srv['zones'][$r['id']]) ){
								$checked = 'checked="checked"';
								$hours_min = round( $srv['zones'][ $r['id'] ]['hours-min'] / 24 );
								$hours_max = round( $srv['zones'][ $r['id'] ]['hours-max'] / 24 );

								$price = round( ($srv['zones'][ $r['id'] ]['price-ht'] * _TVA_RATE_DEFAULT), 2 );
								$free = round( ($srv['zones'][ $r['id'] ]['free-ht'] * _TVA_RATE_DEFAULT), 2 );
							}
							
							print '	<tr>
										<td data-label="'._('Zone : ').' ">
											<input type="checkbox" class="checkbox" name="zones['.$r['id'].']" id="zone'.$r['id'].'" value="'.$r['id'].'" '.$checked.' /> 
											<label for="zone'.$r['id'].'">'.htmlspecialchars($r['name']).'</label>
										</td>
										<td class="align-right delais" data-label="'._('Délais minimum de livraison : ').' ">
											<input class="qte" type="text" name="hours-min['.$r['id'].']" id="hours-min" maxlength="4" value="'.$hours_min.'" /> ' . _('jours(s)') . '
										</td>
										<td class="align-right delais" data-label="'._('Délais maximum de livraison : ').' ">
											<input class="qte" type="text" name="hours-max['.$r['id'].']" id="hours-max" maxlength="4" value="'.$hours_max.'" /> ' . _('jours(s)') . '
										</td>
										<td class="align-right" data-label="'._('Montant forfaitaire : ').' ">
											<input type="text" name="price['.$r['id'].']" id="price-ht" class="price" value="'.number_format( $price, 2 , '.', ' ').'" /> €
										</td>
										<td class="align-right" data-label="'._('Franco de port : ').' ">
											<input type="text" name="free['.$r['id'].']" id="free-ht" class="price" value="'.number_format( $free, 2 , '.', ' ').'" /> €
										</td>
									</tr>';
						}
						}
					}
					?>
				</tbody>
				<tfoot>
					<tr><td colspan="5">
						<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" onclick="return srvValidForm($(this));" />
						<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return srvCancelEdit()" />
						<?php if( $srv['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_DEL') ){ ?>
						<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return srvConfirmDel()" />
						<?php } ?>
					</td></tr>
				</tfoot>
			</table>
			<?php }elseif( $tab == 'fields' ){
				print view_admin_tab_fields( CLS_DLV_SERVICE, $srv['id'], $lng );
			} ?>
			
		<?php if( view_admin_show_tab_fields( CLS_DLV_SERVICE, $srv['id'] ) ){ ?>
		</div>
		<?php } ?>
		
	</form>

	<script>
		// Disable tous les champs/boutons si on accède à cette page en lecture seul
		<?php if( isset($_GET['srv']) && $_GET['srv'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_EDIT') ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
			});
		<?php } ?>
	</script>

<?php
	require_once('admin/skin/footer.inc.php');
?>
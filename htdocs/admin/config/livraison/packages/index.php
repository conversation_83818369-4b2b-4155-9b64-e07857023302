<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV');

	require_once('delivery.inc.php');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['pkg']) ){
		foreach( $_POST['pkg'] as $z )
			if( !dlv_packages_del($z) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression d'un des colis.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Colis') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _('Colis'); ?></h2>

	<?php
		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	?>

	<form action="index.php" method="post">
	<table class="checklist">
		<caption><?php echo _("Liste des colis disponibles"); ?></caption>
		<col width="25" /><col width="125" /><col width="125" /><col width="125" /><col width="125" />
	<thead>
		<tr>
			<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="pkg-length"><?php echo _("Longueur"); ?> (<abbr title="centimètres">cm</abbr>)</th>
			<th id="pkg-width"><?php echo _("Largeur"); ?> (<abbr title="centimètres">cm</abbr>)</th>
			<th id="pkg-height"><?php echo _("Hauteur"); ?> (<abbr title="centimètres">cm</abbr>)</th>
			<th id="pkg-price-ht"><?php echo _("Prix d'achat"); ?> <abbr title="Hors Taxes"><?php echo _("HT"); ?></abbr></th>
		</tr>
	</thead>
	<tfoot>
		<tr><td colspan="5">
			<input type="submit" name="del" class="btn-del" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les colis sélectionnées"); ?>" onclick="return pkgConfirmDelList()" />
			<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un colis"); ?>" onclick="return pkgAdd()" />
		</td></tr>
	</tfoot>
	<tbody>
		<?php
			$packages = dlv_packages_get();
			if( !ria_mysql_num_rows($packages) )
				print '<tr><td colspan="5">Aucun colis</td></tr>';
			else
				while( $r = ria_mysql_fetch_array($packages) ){
					print '<tr>';
					print '<td headers="select"><input type="checkbox" class="checkbox" name="pkg[]" value="'.$r['id'].'" /></td>';
					print '<td headers="pkg-length"><a href="edit.php?pkg='.$r['id'].'">'.number_format($r['length'],0,',',' ').'</a></td>';
					print '<td headers="pkg-width">'.number_format($r['width'],0,',',' ').'</td>';
					print '<td headers="pkg-height">'.number_format($r['height'],0,',',' ').'</td>';
					print '<td headers="pkg-price-ht">'.( trim($r['price_ht']) ? number_format($r['price_ht'],2,',',' ') : '' ).'</td>';
					print '<tr>';
				}
		?>
	</tbody>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
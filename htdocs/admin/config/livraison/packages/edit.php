<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV');

	require_once('delivery.inc.php');

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie la validité de l'identifiant de zone passé en paramètre
	if( isset($_GET['pkg']) && $_GET['pkg']!=0 ){
		if( !dlv_packages_exists($_GET['pkg']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	// Suppression
	if( isset($_POST['del']) ){
		if( !dlv_packages_del($_GET['pkg']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression du colis.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Enregistrement
	if( isset($_POST['save']) ){
		$_POST['price-ht'] = str_replace( ',', '.', $_POST['price-ht'] );
		if( !isset($_POST['length']) || !trim($_POST['length']) ){
			$error = _('Veuillez indiquer la longueur du colis');
		}elseif( !is_numeric($_POST['length']) ){
			$error = _('Veuillez indiquer un nombre entier valide dans le champ « Longueur »');
		}elseif( $_POST['length']<=0 ){
			$error = _('La longueur du colis doit être supérieure à 0');
		}elseif( !isset($_POST['width']) || !trim($_POST['width']) ){
			$error = _('Veuillez indiquer la largeur du colis');
		}elseif( !is_numeric($_POST['width']) ){
			$error = _('Veuillez indiquer un nombre entier valide dans le champ « Largeur »');
		}elseif( $_POST['width']<=0 ){
			$error = _('La largeur du colis doit être supérieure à 0');
		}elseif( !isset($_POST['height']) || !trim($_POST['height']) ){
			$error = _('Veuillez indiquer la hauteur du colis');
		}elseif( !is_numeric($_POST['height']) ){
			$error = _('Veuillez indiquer un nombre entier valide dans le champ « Hauteur »');
		}elseif( $_POST['height']<=0 ){
			$error = _('La hauteur du colis doit être supérieure à 0');
		}elseif( trim($_POST['price-ht']) && !is_numeric($_POST['price-ht']) ){
			$error = _('Veuillez indiquer un prix valide dans le champ « Prix »');
		}elseif( trim($_POST['price-ht']) && $_POST['price-ht']<=0 ){
			$error = _('Le prix du colis doit être supérieur à 0');
		}elseif( isset($_GET['pkg']) && $_GET['pkg']==0 ){
			// Ajout
			if( !dlv_packages_add($_POST['width'],$_POST['height'],$_POST['length'],$_POST['price-ht'],$_POST['supplier']) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}elseif( isset($_GET['pkg']) && $_GET['pkg']>0 ){
			// Modification
			if( !dlv_packages_update($_GET['pkg'],$_POST['width'],$_POST['height'],$_POST['length'],$_POST['price-ht'],$_POST['supplier']) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du colis.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Colis') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Colis"); ?></h2>

	<?php

		$pkg = array('id'=>0,'width'=>'','height'=>'','length'=>'','price_ht'=>'','supplier'=>'');
		if( isset($_GET['pkg']) && is_numeric($_GET['pkg']) && $_GET['pkg']>0 )
			$pkg = ria_mysql_fetch_array(dlv_packages_get($_GET['pkg']));

		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
			if( isset($_GET['pkg']) ) $pkg['id'] = $_GET['pkg'];
			if( isset($_POST['width']) ) $pkg['width'] = $_POST['width'];
			if( isset($_POST['height']) ) $pkg['height'] = $_POST['height'];
			if( isset($_POST['length']) ) $pkg['length'] = $_POST['length'];
			if( isset($_POST['price-ht']) ) $pkg['price_ht'] = $_POST['price-ht'];
			if( isset($_POST['supplier']) ) $pkg['supplier'] = $_POST['supplier'];
		}

	?>

	<form action="edit.php?pkg=<?php print $pkg['id'] ?>" method="post" onsubmit="return pkgValidForm(this)">
		<table>
			<caption><?php echo _("Fiche Colis"); ?></caption>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
				<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return pkgCancelEdit()" />
				<?php if( $pkg['id']>0 ){ ?>
				<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return pkgConfirmDel()" />
				<?php } ?>
			</td></tr>
		</tfoot>
		<tbody>
			<tr><th colspan="2"><?php echo _("Dimensions"); ?></th></tr>
			<tr>
				<td><label for="length"><span class="mandatory">*</span> <?php echo _('Longueur :'); ?></label></td>
				<td><input type="text" class="size" name="length" id="length" maxlength="12" value="<?php print htmlspecialchars($pkg['length']); ?>" /> <abbr title="centimètres">cm</abbr></td>
			</tr>
			<tr>
				<td><label for="width"><span class="mandatory">*</span> <?php echo _('Largeur :'); ?></label></td>
				<td><input type="text" class="size" name="width" id="width" maxlength="12" value="<?php print htmlspecialchars($pkg['width']); ?>" /> <abbr title="centimètres">cm</abbr></td>
			</tr>
			<tr>
				<td><label for="height"><span class="mandatory">*</span> <?php echo _('Hauteur :'); ?></label></td>
				<td><input type="text" class="size" name="height" id="height" maxlength="12" value="<?php print htmlspecialchars($pkg['height']); ?>" /> <abbr title="centimètres">cm</abbr></td>
			</tr>
			<tr><th colspan="2"><?php echo _("Approvisionnement"); ?></th></tr>
			<tr>
				<td><label for="price-ht"><?php echo _("Prix d'achat"); ?> <abbr title="Hors Taxes"><?php echo _("HT"); ?></abbr> :</label></td>
				<td><input type="text" class="price" name="price-ht" id="price-ht" maxlength="12" value="<?php print htmlspecialchars($pkg['price_ht']); ?>" /> &euro;</td>
			</tr>
			<tr>
				<td><label for="supplier"><?php echo _('Fournisseur :'); ?></label></td>
				<td><input type="text" name="supplier" id="supplier" maxlength="75" value="<?php print htmlspecialchars($pkg['supplier']); ?>" /></td>
			</tr>
		</tbody>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
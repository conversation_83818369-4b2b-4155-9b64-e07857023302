<?php

    /** \file index.php
     * 
     *  Cette page permet la configuration du lien avec Instagram
     * 
     */

	define('ADMIN_PAGE_TITLE', _('Instagram') . ' - ' . _('Configuration'));
    require_once('Instagram.inc.php');
    
    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_INSTAGRAM');

    $instagram = new Instagram();

    
    if(isset($_POST['client_id']) && isset($_POST['client_secret'])){
        if($_POST['client_id'] == '' || $_POST['client_secret'] == '') {
            $error = _("Vous n'avez pas saisi tout les champs.\nVeuillez compléter les champs manquants.");
        }else{
            //Si l'on n'a pas réussi à écrire les données en base, on met un message d'érreur
            if (
                !cfg_overrides_set_value('instagram_client_id', $_POST['client_id'])
                || !cfg_overrides_set_value('instagram_client_secret', $_POST['client_secret'])
            ) {
                $error = _('Erreur lors de l\'écriture à la base.');
            }else{ //Sinon on redirige vers l'API pour continuer le traitement
                header('Location: https://api.instagram.com/oauth/authorize/?client_id='.$_POST['client_id'].'&redirect_uri='.$instagram->getRedirectUri().'&response_type=code&scope=basic');
                exit;
            }
        }
    }

    $data = array(
        'client_id' => isset($_POST['client_id']) ? $_POST['client_id'] : $config['instagram_client_id'],
        'client_secret' => isset($_POST['client_secret']) ? $_POST['client_secret'] : $config['instagram_client_secret'],
    );

    require_once('admin/skin/header.inc.php');
?>

<h2><?php echo _("Instagram"); ?></h2>

<div class="notice">
    <p><?php echo _("Voici les informations dont vous aurez besoin pour l'activation, sur votre compte, de la fonctionnalité nécessaire à l'utilisation du flux Instagram :"); ?></p>
    <ul>
    <?php
        $count=0;
        $etapes = array(
            "Etape 1" => _("1. Allez sur <a href='https://www.instagram.com'>Instagram</a> et authentifiez vous avec le compte souhaité."),
            "Etape 2" => _("2. Allez dans la partie développeur du site sur") . " <a href='https://www.instagram.com/developer'>www.instagram.com/developer</a>.",
            "Etape 3" => _("3. Cliquez sur le bouton \"Register your Application\". Si la fonctionnalité développer n'est pas encore activée, vous allez être redirigé vers la page correspondante, sinon passez à l'étape 5"),
            "Etape 4" => _("4. Remplissez le formulaire proposé par Instagram et validez, une fois cela effectué, vous êtes redirigés sur la page développeur, vous pouvez recliquer sur \"Register your Application\"."),
            "Etape 5" => _("5. Cliquez sur \"Register a new client\"."),
            "Etape 6" => _("6. Remplissez le nouveau formulaire à votre disposition. Les champs minimum requis sont Application Name, Description, Website URL (l'adresse du site) et Valid redirect URIs, ce dernier dans lequel vous mettrez") . " <span style='text-decoration: underline;'>".$instagram->getRedirectUri()."</span>",
            "Etape 7" => _("7. Vos identifiants sont générés. Pour connaître ceux que nous avons besoin, cliquez sur \"Manage\" dans l'encadré correspondant à votre client."),
            "Etape 8" => _("8. Remplissez les champs ci-dessous en copiant-collant les clés Client ID et Client Secret."),
            "Etape 9" => _("9. Validez en cliquant sur \"Générer le token\". La première fois, vous allez être redirigé vers une page Instagram vous demandant une autorisation. Faites \"Authorize\" pour valider. Votre Instagram est paramétré !"),
            "Information" => _("Si votre token n'est plus actif, un message apparaîtra dans cette rubrique RiaShop pour vous avertir de son expiration. Dans ce cas, vous aurez juste à cliquer sur le bouton \"Regénérer le token\"."),
            "Complement" => _("Si vous voulez changer de compte Instagram pour votre flux, il suffit de réitérer les mêmes instructions, et de remplacer les champs client id et client secret par vos nouvelles clés. Enfin, cliquez sur Regénérer le token. Le changement de compte pour votre site sera effectif sous 1h.")
        );
        foreach($etapes as $etape => $consigne){
            print '		<li'.( $count>=0 ? ' class="more-info"' : '' ).'>'.$consigne.'</li>';
            $count++;
        }
    ?>
    </ul>
    <a class="more" onclick="return displayNotice();"><?php echo _("En savoir plus"); ?></a>
    <a class="more more-hide" onclick="return displayNotice();"><?php echo _("Réduire"); ?></a>
</div>
<p><?php echo _("Les paramètres ci-dessous vous permettent de gérer votre flux Instagram"); ?></p>

    <form id="formulaire_identifiants_instagram" method="post" action="/admin/config/instagram/index.php">
        <?php
            if (isset($error)) {
                ?><div class="error"><?php print _(nl2br($error)); ?></div><?php
            }

            if (isset($_SESSION['instagram_callback_success']) && $_SESSION['instagram_callback_success']) {
                ?><div class="success"><?php echo _("Le token a bien été généré."); ?></div><?php
                unset($_SESSION['instagram_callback_success']);
            }

            if (isset($_SESSION['instagram_callback_error']) && $_SESSION['instagram_callback_error']) {
                ?><div class="error"><?php
                    if (isset($_SESSION['instagram_callback_message']) && $_SESSION['instagram_callback_message']) {
                        print htmlspecialchars($_SESSION['instagram_callback_message']);
                        unset($_SESSION['instagram_callback_message']);
                    }else{
                        ?><?php echo _("Erreur dans la génération du token, veuillez réessayer. Si l'erreur persiste, n'hésitez pas à nous contacter."); ?><?php
                    }

                    unset($_SESSION['instagram_callback_error']);
                ?></div><?php
            }
        ?>

        <dl>
            <dt><?php echo _("Données relatives à votre compte Instagram"); ?></dt>
            <dd>
                <label for="client_id"><span class="mandatory">*</span> <?php echo _("Votre Client ID :"); ?></label>
                <input id="client_id" type="text" name="client_id" value="<?php print htmlspecialchars($data['client_id']);?>" />
            </dd>
            <dd>
                <label for="client_secret"><span class="mandatory">*</span> <?php echo _("Votre Client Secret :"); ?> </label>
                <input id="client_secret" type="text" name="client_secret"  value="<?php print htmlspecialchars($data['client_secret']);?>" />
            </dd>
        </dl>

        <div class="ria-admin-ui-actions">
            <?php 
                if (trim($config['instagram_client_id']) == '' || trim($config['instagram_client_secret']) == '' || trim($config['instagram_token']) == '') {
                    ?><input id="button_generate" type="submit" value="<?php echo _("Générer le token"); ?>" name="generate" /><?php 
                } elseif (!$instagram->isTokenValid()) {
                    ?>
                        <p><?php echo _("Le token permettant la récupération des articles sur Instagram n'est plus valide, merci de le regénérer :"); ?>
                        <input id="button_regenerate" type="submit" value="<?php echo _("Regénérer le token"); ?>" name="regenerate" /></p>
                    <?php 
                } 
            ?>
        </div>
   </form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
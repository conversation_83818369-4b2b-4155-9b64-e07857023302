<?php
	/** \file index.php
	 * 	Ce fichier permet de gérer les horaires d'expédition des colis ainsi que les jours fériés pour lesquels l'expédition est maintenus.
	 * 	Il permet aussi de gérer des fermutures exceptionnelles qui peuvent être prisent en compte lors du calcul de la date de livraison.
	 */
	require_once( 'delivery.inc.php' );

	// Vérifie que l'utilisateur en cours à accès à la configuration des horaires et dates d'expédition
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_EXPEDITIONS');
	
	// Sauvegarde des jours et horaires d'expédition des colis
	if( isset($_POST['save-exp']) ){
		$wst_id = isset($_POST['exp-website']) && $_POST['exp-website'] ? $_POST['wst_id'] : 0;
		
		// Supprime les périodes déjà enregistrées
		if( !dlv_expedition_periods_del($wst_id) )
			$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'expédition des colis. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		
		// Parcour tous les jours de la semaine
		if( isset($_POST['hr']) && !isset($error) ){
			foreach( $_POST['hr'] as $day=>$hours ){
				
				// Parcour chaque heure pour trouver les périodes d'horaires
				$temp = $count = $start = $end = 0;
				$first = true;
				foreach( $hours as $key=>$hour ){
					if( $first ) {
						$start = $hour; 
						$temp = $hour; 
						$count++;
						$first = false;
					}
					
					$h = $hour - $temp;
					if( $h>1 ){ // Il s'agi d'une autre période
						
						// On enregistre la période
						$end = $end>0 ? $end : $start;
						if( !dlv_expedition_periods_add($wst_id, $day, $start, $end) )
							$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'expédition des colis. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
						$start = $hour;
						$temp = $hour;
						$count = 0;
					} else {
						// Il s'agit de la même périod
						$end = $hour;
						$temp = $hour;
						$count++;
					}
					$end = $hour;
				}
				
				// On enregistre la dernière période
				if( !dlv_expedition_periods_add($wst_id, $day, $start, $end) )
					$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'expédition des colis. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
			}
		}
		if( !isset($error) )
			$success = _("L'enregistrement des horaires d'expédition des colis s'est correctement déroulé.");
	}

	define('ADMIN_PAGE_TITLE', _("Horaires d'expédition") . ' - ' . _("Configuration"));
	require_once('admin/skin/header.inc.php');

	$exp_wst_exists = dlv_expedition_periods_exists_for_sites();
	if( isset($error) )
		print '<div class="error">'. $error .'</div>';
	elseif( isset($success) ){
		if( $exp_wst_exists )
			$_GET['website'] = isset($_POST['wst_id']) ? $_POST['wst_id'] : 0;
		print '<div class="error-success">'. $success .'</div>';
	}
?>

	<h2><?php echo _('Calendrier des expéditions'); ?></h2>
	<p>
		<?php echo _("Les paramètres ci-dessous sont utilisés pour calculer les dates d'expédition et de livraison. La précision de ces dates influe directement sur la qualité perçue par vos clients. <br />En B2C, ces dates sont d'autant plus importantes que depuis la loi « Châtel », votre client peut refuser le colis si celui-ci arrive après la date limite de livraison."); ?>
	</p>
	
	<h3><?php print _('Jours et heures d\'expédition (en semaine normale)'); ?></h3>
	<form id="form-expedition" action="index.php" method="post">
		<p>
			<?php echo _("Dans une semaine normale, veuillez sélectionner ci-dessous les jours et heures durant lesquels des expéditions ont lieu :"); ?>
		</p>
		<?php 
			$exp_website = $exp_wst_exists || ( isset($_GET['website']) && wst_websites_exists($_GET['website']) );
			$websites = wst_websites_get();
			if( $websites!==false && ria_mysql_num_rows( $websites )>1 ){
		?>
		<ul>
			<li class="exp-website">
				<input type="radio" name="exp-website" id="exp-website-0" value="0" <?php print $exp_website ? '' : 'checked="checked"'; ?> />
				<label for="exp-website-0"><?php echo _("Horaires d'expédition identiques pour tous les sites"); ?></label>
			</li>
			<li class="exp-website">
				<input type="radio" name="exp-website" id="exp-website-1" value="1" <?php print $exp_website ? 'checked="checked"' : ''; ?> />
				<label for="exp-website-1"><?php echo _("Horaires d'expédition spécifiques pour chaque site"); ?></label>
				<div class="exp-menu"  <?php print $exp_website ? '' : 'style="display:none;"'; ?>>
					<div id="expeditionspicker">
						<div class="selectorview">
							<div class="left">
								<span class="function_name"><?php echo _("Sites Internet"); ?></span><br/>
								<?php
									$website = isset($_GET['website']) && wst_websites_exists($_GET['website']) ? wst_websites_get($_GET['website']) : wst_websites_get();
									$wst = ria_mysql_fetch_array( $website );
									print '<span class="view">'.$wst['name'].'</span>';
								?>
							</div>
							<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
							<div class="clear"></div>
						</div>
						<div class="selector">
							<?php
								while( $w = ria_mysql_fetch_array($websites) )
									print '<a name="w-'.$w['id'].'">'.$w['name'].'</a>';
							?>
						</div>
					</div>
					<div class="clear"></div>
				</div>
			</li>
		</ul>
		<?php
				$wst_id = $wst['id'];
				print '<input type="hidden" name="wst_id" id="wst_id" value="'.$wst['id'].'" />';
			} else {
				$wst_id = 0;
				print '<input type="hidden" name="wst_id" id="wst_id" value="0" />';
			}
			if( !$exp_website )
				$wst_id = 0;
		?>
		<div class="table-layout-large">
			<table id="tb-day-exp" class="tb-day-exp1">
				<caption><?php echo _("Jours et heures d'expédition (en semaine normale)"); ?></caption>
				<thead>
					<tr>
						<th id="day-exp"><?php echo _("Jour"); ?></th>
						<th id="period-exp"><?php echo _("Période"); ?></th>
						<th id="midnight"><?php echo _("Minuit"); ?></th>
						<th id="hr-four"><?php echo _("4h00"); ?></th>
						<th id="hr-eight"><?php echo _("8h00"); ?></th>
						<th id="noon"><?php echo _("Midi"); ?></th>
						<th id="hr-sixteen"><?php echo _("16h00"); ?></th>
						<th id="hr-twenty"><?php echo _("20h00"); ?></th>
						<th id="action-exp"></th>
					</tr>
				</thead>
				<tbody>
					<?php 
						
						$hourly = array();
						// Récupère tous les horaires d'expédition
						$rperiod = dlv_expedition_periods_get($wst_id);
						if( $rperiod!==false ){
							while( $period = ria_mysql_fetch_array($rperiod) ){
								// Récupère le nom du jour de la semaine de cette période
								$day = dlv_day_get_name($period['day']);
								
								// Information sur les périodes
								if( !isset($lbl_period[$day]) ) $lbl_period[$day] = '';
								$lbl_period[$day] .= $period['start'];
								if( $period['end']!=$period['start'] ){
									$end = ($period['int-end']+1)<10 ? '0'.($period['int-end']+1).':00' : ($period['int-end']+1).':00';
									$lbl_period[$day] .= '&nbsp;à&nbsp;'.$end.' ';
								} else {
									$end = ($period['int-end']+1)<10 ? '0'.($period['int-start']+1).':00' : ($period['int-start']+1).':00';
									$lbl_period[$day] .= '&nbsp;à&nbsp;'.$end.' ';
								}
								
								// Retrouve toute les heures contenus dans les pédiodes et les insert dans un tableau
								for( $i=$period['int-start'] ; $i<=$period['int-end'] ; $i++ )
									$hourly[$day][] = $i;
							}
						}
						
						$days = array( 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche' );
						$id_days = array( 'Lundi'=>'monday', 'Mardi'=>'thuesday', 'Mercredi'=>'wednesday', 'Jeudi'=>'thursday', 'Vendredi'=>'friday', 'Samedi'=>'saturday', 'Dimanche'=>'sunday' );
						
						$count = 0;
						foreach( $days as $key=>$day ){
							print '	<tr>';
							print '		<td headers="day-exp">'._($day).'</td>';
							print '		<td headers="period-exp" class="period" id="period-'.$id_days[$day].'">'.( isset($lbl_period[$id_days[$day]]) ? $lbl_period[$id_days[$day]] : _('Aucune expédition') ).'</td>';
							print '		<td headers="midnight">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(0, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(0, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(1, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(1, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(2, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(2, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(3, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(3, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-four">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(4, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(4, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(5, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(5, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(6, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(6, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(7, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(7, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-eight">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(8, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(8, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(9, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(9, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(10, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(10, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(11, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(11, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="noon">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(12, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(12, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(13, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(13, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(14, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(14, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(15, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(15, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-sixteen">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(16, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(16, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(17, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(17, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(18, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(18, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(19, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(19, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-twenty">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(20, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(20, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(21, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(21, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(22, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(22, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(23, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(23, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td>';
							print '		<td headers="action-exp" class="reinit">';
							print '			<a class="del" onclick="reset(\''.$id_days[$day].'\')">' . _('Aucune') . '</a>';
							print '		</td>';
							print '	</tr>';
							$count = 0;
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="9">
							<input type="submit" name="save-exp" id="save-exp" value="<?php echo _("Enregistrer"); ?>" />
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
	</form>
	
	<h3><?php echo _("Jours fériés"); ?> </h3>
	<p>
		<?php echo _("Si vous souhaitez maintenir les expéditions un jour férié, faites le savoir à vos clients en sélectionnant la journée ci-dessous."); ?>
	</p>

	<form id="form-holidays" action="index.php" method="post">
		<!-- <div class="table-layout-large"> -->
			<table id="tb-holidays">
				<thead>
					<tr id="th-year">
						<th>
						</th>
						<th class="align-center"><?php echo _("Année ") . date('Y'); ?></th>
						<th class="align-right"><img class="hld-nav-year" src="/admin/images/expeditions/feries_active_right.svg" onclick="holidays(<?php print date('Y')+1; ?>)" alt="Année <?php print date('Y')+1; ?>" title="<?php echo _("Année ") . date('Y')+1; ?>" /></th>
					</tr>
					<tr>
						<th id="hld-date"><?php echo _("Date"); ?></th>
						<th id="hld-name"><?php echo _("Fête"); ?></th>
						<th id="hld-exp"><?php echo _("Expédition"); ?></th>
					</tr>
				</thead>
				<tbody>
					<?php
						// Récupère toutes les dates de jours fériés (pour l'année en cours de modification) où les expéditions ont lieu
						$rhld = dlv_holidays_get( date('Y'), true );
						
						// Construit un tableau de toutes les dates
						$current_holidays = array();
						if( $rhld!=false ){
							while( $hld = ria_mysql_fetch_array($rhld) ){
								$current_holidays[] = $hld['date'];
							}
						}
						
						$holidays = holidays(date('Y'));
						foreach( $holidays as $date=>$name ){
							print '	<tr>
										<td headers="hld-date" class="hld-date">'.dateformatcomplet( strtotime($date) ).'</td>
										<td headers="hld-name">'.$name.'</td>
										<td headers="hld-exp" class="hld-exp">
											<label for="hld-exp-yes-'.$date.'">
												<input type="radio" name="date['.$date.']" id="hld-exp-yes-'.$date.'" value="1" '.( in_array($date, $current_holidays) ? 'checked="checked"' : '' ).' />' . _("Oui") . '</label>
											<label for="hld-exp-no-'.$date.'">
												<input type="radio" name="date['.$date.']" id="hld-exp-no-'.$date.'" value="0" '.( in_array($date, $current_holidays) ? '' : 'checked="checked"' ).' />' . _("Non") . '</label>
										</td>
									</tr>';
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="3"><input type="button" name="save-hld" id="save-hld" value="<?php echo _("Enregistrer"); ?>" onclick="holidays_save();" /></td>
					</tr>
				</tfoot>
			</table>
		<!-- </div> -->
	</form>
	<h3><?php echo _("Périodes de fermeture exceptionnelles"); ?></h3>
	<p>
		<?php echo _("Si durant une période donnée vous n'êtes pas en mesure de réaliser des expéditions (quelle que soit la raison), veuillez l'indiquer ci-dessous. RiaShop prendra ainsi en compte ces dates dans le calcul des délais d'expédition et de livraison."); ?>
	</p>
	<form id="form-closing" action="index.php" method="post">
		<?php
			// Récupère les dates de fermeture exceptionnelle
			$rclosing = dlv_events_get(0, $period);
			if( ria_mysql_num_rows($rclosing)>10 ){
		?>
		<div class="closing-menu">
			<div id="closingpicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Fermetures"); ?></span><br/>
						<?php
							$period = isset($_GET['period']) && ( $_GET['period']>=0 || $_GET['period']<3 ) ? $_GET['period'] : 0;

							switch($period){
								case 0 :
									print '<span class="view">' . _("Toutes") . '</span>';
									break;
								case 1 :
									print '<span class="view">' . _("Actuelles") . '</span>';
									break;
								case 2 :
									print '<span class="view">' . _("&Agrave; venir") . '</span>';
									break;
								default :
									print '<span class="view">' . _("Toutes") . '</span>';
									break;
							}
						?>
					</div>
					<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="p-0"><?php echo _("Toutes"); ?></a>
					<a name="p-1"><?php echo _("Actuelles"); ?></a>
					<a name="p-2"><?php echo _("&Agrave; venir"); ?></a>
				</div>
			</div>
			<div class="clear"></div>
		</div>
		<?php } ?>
		<!-- <div class="table-layout-large"> -->
			<table id="tb-closing">
				<thead>
					<tr>
						<th id="clg-name"><?php echo _("Nom de la période"); ?></th>
						<th id="clg-start"><?php echo _("Date de début (inclus)"); ?></th>
						<th id="clg-end"><?php echo _("Date de fin (inclus)"); ?></th>
						<th id="clg-action"></th>
					</tr>
				</thead>
				<tbody>
					<?php						
						if( $rclosing==false || ria_mysql_num_rows($rclosing)==0 ){
							// Affiche un message si aucune fermeture n'existe
							print '	<tr id="none-closing">
										<td colspan="4">' . _("Aucune fermeture exceptionnelle n'est enregistrée") . '</td>
									</tr>';
						} else {
							// Affichage des fermetures exceptionnelle déjà enregistré
							while( $closing = ria_mysql_fetch_array($rclosing) ){
								print '	<tr id="closing-'.$closing['id'].'">
											<td headers="clg-name" class="td-info">'.htmlspecialchars($closing['name']).'</td>
											<td headers="clg-start" class="td-date">'.ria_date_format($closing['start']).'</td>
											<td headers="clg-end" class="td-date">'.ria_date_format($closing['end']).'</td>
											<td headers="clg-action" class="td-action">
												<img class="edit-closing" src="/admin/images/expeditions/edit.png" alt="'._('Editer').'" title="' . _("Editer les informations concernant cette fermeture exceptionnelle") . '" onclick="closing_edit('.$closing['id'].', \''.addslashes($closing['name']).'\', \''.$closing['start'].'\', \''.$closing['end'].'\');" />
												<div id="save-load-'.$closing['id'].'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>
												<br /><a class="del button" onclick="closing_del('.$closing['id'].');">' . _("Supprimer") . '</a>
											</td>
										</tr>';
							}
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="4" style="text-align:right">
							<input type="button" name="add-closing" id="add-closing" value="<?php echo _("Nouvelle Période"); ?>" title="<?php echo _("Ajouter une nouvelle période de fermeture exceptionnelle"); ?>" onclick="closing_add();" />
						</td>
					</tr>
				</tfoot>
			</table>
		<!-- </div> -->
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
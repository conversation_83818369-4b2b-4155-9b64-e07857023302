<?php

	/** \file ajax-closing.php
	 *	Ce fichier répond à des appels Ajax pour la mise à jour des dates de fermetures exceptionnelles.
	 */

	// Vérifie que l'utilisateur en cours à accès à la configuration des horaires et dates d'expédition
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_EXPEDITIONS');

	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	// Suppression d'une fermeture exceptionnelle
	if( isset($_POST['delclosing']) ){
		
		if( !isset($_POST['closing']) || !dlv_events_del($_POST['closing']) ){
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur inattendue est survenue lors de la suppression d'une fermeture exceptionnelle.") . "<br />" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
			$xml .= "</result>";
		}else{
			$xml .= "<result type=\"1\">";
			$xml .= "<success>" . _("La suppression de la fermeture exceptionnelle s'est correctement déroulée.") . "</success>";
			$xml .= "</result>";
		}
		
	} elseif( isset($_POST['addclosing']) ){ // Gestion de l'ajout ou mise à jour après des vérifications
		$str_id = (isset($_POST['str_id']) && is_numeric($_POST['str_id']))? $_POST['str_id'] : 0; 
		if( !isset($_POST['closing']) ){ 
			
			// L'identifiant de la fermeture est manquant
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur inattendue est survenue lors de l'enregistrement d'une fermeture exceptionnelle.") . " <br />'" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
			$xml .= "</result>";
			
		} elseif( trim($_POST['name-period-'.$_POST['closing']])=='' ){
			
			// Le libellé attribué à la fermeture est vide
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle.") . "<br />" . _("Veuillez renseigner le champ \"Nom de la période\".") . "</error>";
			$xml .= "</result>";
			
		} elseif( !isdate($_POST['start-period-'.$_POST['closing']]) || !isdate($_POST['end-period-'.$_POST['closing']]) ){
			
			// La date de début ou la date de fin n'est pas renseignée
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle.") .  "<br />" . _("Veuillez renseigner une date de début et une date de fin.") . "</error>";
			$xml .= "</result>";
			
		} elseif( !cmp_date(dateparse($_POST['end-period-'.$_POST['closing']]), dateparse($_POST['start-period-'.$_POST['closing']])) ){
			
			// La date de fin est antérieur à la date de début
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle."). "<br />" . _("La date de fin ne peut être antérieur à la date de début.") . "</error>";
			$xml .= "</result>";
			
		} elseif( $_POST['closing']==0 ){
			
			// L'identifiant est à 0, il s'agit donc de l'ajout d'une nouvelle fermeture
			$evt = dlv_events_add($_POST['name-period-0'], dateparse($_POST['start-period-0']).' 00:00', dateparse($_POST['end-period-0']).' 23:59', $str_id );
			if( !$evt ){
				$xml .= "<result type=\"0\">";
				$xml .= "<error>" . _("Une erreur inattendue est survenue lors de l'ajout d'une fermeture exceptionnelle.") . "<br />" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
				$xml .= "</result>";
			} else {
				$xml .= "<result type=\"1\" add=\"1\" id=\"".$evt."\" name=\"".$_POST['name-period-0']."\" start=\"".$_POST['start-period-0']."\" end=\"".$_POST['end-period-0']."\">";
				$xml .= "<success>" . _("L'ajout de la fermeture exceptionnelle s'est correctement déroulé.") . "</success>";
				$xml .= "</result>";
			}
			
		} else {
			// L'identifiant est supérieur à 0, il s'agit donc d'une mise à jour
			$id = $_POST['closing'];
			if( !dlv_events_update($id, $_POST['name-period-'.$id], dateparse($_POST['start-period-'.$id]).' 00:00', dateparse($_POST['end-period-'.$id]).' 23:59' ) ){
				$xml .= "<result type=\"0\">";
				$xml .= "<error>" . _("Une erreur inattendue est survenue lors de la mise à jour d'une fermeture exceptionnelle.") . "<br />" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
				$xml .= "</result>";
			} else {
				$xml .= "<result type=\"1\" add=\"0\" id=\"".$id."\" name=\"".$_POST['name-period-'.$id]."\" start=\"".$_POST['start-period-'.$id]."\" end=\"".$_POST['end-period-'.$id]."\">";
				$xml .= "<success>" . _("La mise à jour de la fermeture exceptionnelle s'est correctement déroulée.") . "</success>";
				$xml .= "</result>";
			}
			
		}
		
	}
	
	// Affichage de la réponse XML
	print $xml;
    exit;
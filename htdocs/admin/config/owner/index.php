<?php

	require_once('site.inc.php');
	require_once('sys.naf.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_OWNER');

	unset($error);

	// Boutons Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst']>=0 ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}
	$default = false;
	$wst = $config['wst_id'];
	$rwst = wst_websites_get();

	if( ria_mysql_num_rows($rwst)> 1 ){
		$default = true;
		$wst = isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>=0 ? $_SESSION['websitepicker'] : $config['wst_id'];
	}
	// Boutons Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['owner-type']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
		}elseif( !isset($_POST['address1']) || !isset($_POST['address2']) || !isset($_POST['zipcode']) || !isset($_POST['city']) || !isset($_POST['inscription']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
		}elseif( isset($_POST['naf']) && trim($_POST['naf'])!='' && !sys_naf_codes_exists($_POST['naf']) ){
			$error = _("Le code NAF saisi n'est pas reconnu.");
		}elseif( $_POST['owner-type']==0 ){
			$_POST['capital'] = 0 . str_replace(' ','',str_replace(',','.',$_POST['capital']));
			if( !isset($_POST['name']) || !trim($_POST['name']) || !isset($_POST['capital']) || !is_numeric($_POST['capital']) || !isset($_POST['publication']) || !isset($_POST['redaction']) )
				$error =_( "Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.\nVeuillez vérifier. Les champs marqués d'une * sont obligatoires.");
			elseif( !site_owner_set_society( $_POST['name'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], $_POST['inscription'], $_POST['capital'], $_POST['publication'], $_POST['redaction'], $wst ) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}elseif( $_POST['owner-type']==1 ){
			if( !isset($_POST['firstname']) || !trim($_POST['firstname']) || !isset($_POST['lastname']) || !trim($_POST['lastname']) )
				$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.\nVeuillez vérifier. Les champs marqués d'une * sont obligatoires.");
			elseif( !site_owner_set_person($_POST['firstname'], $_POST['lastname'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], $_POST['inscription'], $wst) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec l'administrateur");
		}
		if( isset($_POST['phone']) ){
			site_owner_set_phone( $_POST['phone'], $wst );
		}
		if( isset($_POST['fax']) ){
			site_owner_set_fax( $_POST['fax'], $wst );
		}
		if( isset($_POST['email']) ){
			site_owner_set_email( $_POST['email'], $wst );
		}
		if( isset($_POST['taxcode']) ){
			site_owner_set_taxcode( $_POST['taxcode'], $wst );
		}
		if( isset($_POST['naf']) ){
			site_owner_set_naf( $_POST['naf'], $wst );
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Informations sur le propriétaire') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _("Informations sur le propriétaire"); ?></h2>
	<?php
		print view_websites_selector( $wst, true, 'riapicker', false );

		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

		$owner = site_owner_get( $wst, $default );
	?>

	<div class="notice"><?php echo _("Les mentions légales à faire apparaître sur vos dispositifs digitaux dépendent du statut social de votre entreprise."); ?></div>

	<form action="index.php" method="post" onsubmit="return validOwnerForm(this)">
	<table class="w-600">
		<caption>
			<input type="radio" name="owner-type" value="0" id="owner-type1" <?php if( !$owner['type'] ) print 'checked="checked"'; ?> /> <label for="owner-type1"><?php echo _("Vous représentez une personne morale (société) :"); ?></label>
		</caption>
		<tbody>
			<tr>
				<td><label for="name"><span class="mandatory">*</span> <?php print _('Raison Sociale :'); ?></label></td>
				<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($owner['name']); ?>" /></td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Siège Social"); ?></th>
			</tr>
			<tr>
				<td><label for="address1"><?php echo _('Adresse :'); ?></label></td>
				<td><input type="text" name="address1" id="address1" maxlength="75" value="<?php print htmlspecialchars($owner['address1']); ?>" /></td>
			</tr>
			<tr>
				<td>&nbsp;</td>
				<td><input type="text" name="address2" id="address2" maxlength="75" value="<?php print htmlspecialchars($owner['address2']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="zipcode"><?php echo _('Code postal :'); ?></label></td>
				<td><input type="text" name="zipcode" id="zipcode" maxlength="20" value="<?php print htmlspecialchars($owner['zipcode']); ?>" class="zipcode" /></td>
			</tr>
			<tr>
				<td><label for="city"><?php echo _('Ville :'); ?></label></td>
				<td><input type="text" name="city" id="city" maxlength="75" value="<?php print htmlspecialchars($owner['city']); ?>" /></td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Inscription au RCS ou au répertoire des métiers"); ?></th>
			</tr>
			<tr>
				<td><label for="inscription"><?php echo _("Numéro d'inscription :"); ?></label></td>
				<td><input type="text" name="inscription" id="inscription" maxlength="75" value="<?php print htmlspecialchars($owner['inscription']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="capital"><?php echo _("Capital social :"); ?></label></td>
				<td><input type="text" class="price" name="capital" id="capital" maxlength="12" value="<?php if( $owner['capital'] ) print number_format($owner['capital'],0,',',' '); ?>" /> (en &euro;)</td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Autres informations (si applicables)"); ?></th>
			</tr>
			<tr>
				<td><label for="publication"><?php echo _("Directeur de la publication :"); ?></label></td>
				<td><input type="text" name="publication" id="publication" maxlength="75" value="<?php print htmlspecialchars($owner['publication']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="redaction"><?php echo _("Responsable de la rédaction :"); ?></label></td>
				<td><input type="text" name="redaction" id="redaction" maxlength="75" value="<?php print htmlspecialchars($owner['redaction']); ?>" /></td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" id="save" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" id="cancel" />
				</td>
			</tr>
		</tfoot>
	</table>

	<table class="w-600">
		<caption><input type="radio" name="owner-type" value="1" id="owner-type2" <?php if( $owner['type'] ) print 'checked="checked"'; ?> /> <label for="owner-type2"><?php echo _("Vous représentez une personne physique (entreprise individuelle) :"); ?></label></caption>
		<tbody>
			<tr>
				<td><label for="firstname"><span class="mandatory">*</span> <?php echo _("Votre prénom :"); ?></label></td>
				<td><input type="text" name="firstname" id="firstname" maxlength="75" value="<?php print htmlspecialchars($owner['firstname']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="lastname"><span class="mandatory">*</span> <?php echo _("Votre nom :"); ?></label></td>
				<td><input type="text" name="lastname" id="lastname" maxlength="75" value="<?php print htmlspecialchars($owner['lastname']); ?>" /></td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Votre adresse"); ?></th>
			</tr>
			<tr>
				<td><label for="address1-2"><?php echo _("Adresse :"); ?></label></td>
				<td><input type="text" name="address1" id="address1-2" maxlength="75" value="<?php print htmlspecialchars($owner['address1']); ?>" /></td>
			</tr>
			<tr>
				<td>&nbsp;</td>
				<td><input type="text" name="address2" id="address2-2" maxlength="75" value="<?php print htmlspecialchars($owner['address2']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="zipcode-2"><?php echo _("Code postal :"); ?></label></td>
				<td><input type="text" name="zipcode" id="zipcode-2" maxlength="20" class="zipcode" value="<?php print htmlspecialchars($owner['zipcode']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="city-2"><?php echo _("Ville :"); ?></label></td>
				<td><input type="text" name="city" id="city-2" maxlength="75" value="<?php print htmlspecialchars($owner['city']); ?>" /></td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Inscription au RCS ou au répertoire des métiers (si applicable)"); ?></th>
			</tr>
			<tr>
				<td><label for="inscription-2"><?php echo _("Numéro d'inscription :"); ?></label></td>
				<td><input type="text" name="inscription" id="inscription-2" maxlength="75" value="<?php print htmlspecialchars($owner['inscription']); ?>" /></td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" id="save-2" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" id="cancel-2" />
				</td>
			</tr>
		</tfoot>
	</table>

	<table class="w-600">
		<caption><?php echo _("Autres informations utiles"); ?></caption>
		<tbody>
			<tr>
				<th colspan="2"><?php echo _('Moyens de contact'); ?></th>
			</tr>
			<tr>
				<td><label for="phone"><?php echo _('Numéro de téléphone :'); ?></label></td>
				<td><input type="text" name="phone" id="phone" maxlength="21" value="<?php print htmlspecialchars($owner['phone']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="fax"><?php echo _('Numéro de fax :'); ?></label></td>
				<td><input type="text" name="fax" id="fax" maxlength="21" value="<?php print htmlspecialchars($owner['fax']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="email"><?php echo _('Adresse email :'); ?></label></td>
				<td><input type="email" name="email" id="email" maxlength="75" value="<?php print htmlspecialchars($owner['email']); ?>" /></td>
			</tr>
			<tr>
				<th colspan="2"><?php echo _("Autres informations légales"); ?></th>
			</tr>
			<tr>
				<td><label for="naf"><?php echo _('Code NAF :'); ?></label></td>
				<td><input type="text" name="naf" id="naf" maxlength="5" value="<?php print isset($_POST['naf']) ? $_POST['naf'] : htmlspecialchars($owner['naf']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="taxcode"><?php echo _("Numéro de TVA intracommunautaire :"); ?></label></td>
				<td><input type="text" name="taxcode" id="taxcode" maxlength="20" value="<?php print htmlspecialchars($owner['taxcode']); ?>" /></td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" id="saveother" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" id="cancelother" />
				</td>
			</tr>
		</tfoot>
	</table>

	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
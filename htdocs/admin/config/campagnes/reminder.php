<?php

	/**	\file reminder.php
	 *
	 * 	Cette page permet la configuration des campagnes de type Rappel
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CAMPAGNE_REMINDER');

	//---------- Nouvelle configuration ----------

	if(isset($_POST["save-reminder"])){

		if(isset($_POST["active-reminder"]) && $_POST["active-reminder"] == 1){

			cfg_overrides_set_value("reminder_orders_activated",1,$config["wst_id"]);

			$arrConfig = array();
			$arrConfig["days"] = (isset($_POST["reminder-days"]) && is_numeric($_POST["reminder-days"]) ? $_POST["reminder-days"] : 0);
			if($arrConfig["days"] == 0){
				$error = _("Veuillez saisir un minimum de jours.");
			}

			$json = json_encode($arrConfig);
			cfg_overrides_set_value("reminder_orders_config",$json,$config["wst_id"]);

		}else if($_POST["active-reminder"] == 0 || $error){
			//Supprimer les variables
			cfg_overrides_del_value("reminder_orders_activated",$config["wst_id"]);
			cfg_overrides_del_value("reminder_orders_config",$config["wst_id"]);
		}

		if( !isset($error)){
			$_SESSION["success_reminder_reward"] = 1;
			header('Location: /admin/config/campagnes/reminder.php');
			exit;
		}
	}

	//---------- Charger la configuration actuel ----------

	$jsonConfig = cfg_overrides_get_value("reminder_orders_config",$config["wst_id"]);
	$promoActivated = cfg_overrides_get_value("reminder_orders_activated",$config["wst_id"]);

	if($promoActivated && $jsonConfig){
		$currentConfig = json_decode($jsonConfig,true);
	}else{
		$currentConfig = array("days"=>0);
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Campagnes'), '/admin/config/campagnes/index.php' )
		->push( _('Incitation de commande') );

	require_once('promotions.inc.php');
	define('ADMIN_PAGE_TITLE', _('Incitation de commande') . ' - ' . _('Campagnes') . ' - ' . _('Configuration') );
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Incitation de commande'); ?></h2>
<div class="notice">
	<?php print _('Le système permet d\'envoyer une notification X jours après le passage d’une commande ayant générée des points de fidélité, le contenu contiendra les articles du catalogue produits achetables avec des points.'); ?>
</div>
<?php
	if( isset($error)){
		print '<div class="notice">'.htmlspecialchars( $error ).'</div>';
	}
	if( isset($_SESSION["success_reminder_reward"]) && $_SESSION["success_reminder_reward"] == 1){
		print '<div class="notice">'._('Votre configuration a bien été prise en compte').'</div>';
		unset($_SESSION["success_reminder_reward"]);
	}
?>
<form method="post">
	<table id="table-reminder-catalog" class="table-reminder-catalog">
		<tbody>
			<tr>
				<th colspan="2"><?php print _('Général'); ?></th>
			</tr>
			<tr>
				<td class="col230px">
					<label for="active-sponsor-y"><span class="mandatory">*</span> <?php print _('Activer ce système :'); ?></label>
				</td>
				<td>
					<input class="active-radio" <?php echo ( $promoActivated == 1 ? "checked='checked'" : "" ) ?>  type="radio" name="active-reminder" id="active-reminder-y" value="1" />
					<label for="active-reminder-y"><?php print _('Oui'); ?></label>
					<input class="active-radio" <?php echo ( !$promoActivated ? "checked='checked'" : "" ) ?> type="radio" name="active-reminder" id="active-reminder-n" value="0" />
					<label for="active-reminder-n"><?php print _('Non'); ?></label>
				</td>
			</tr>
			<tr class="details" <?php echo ( !$promoActivated? "style='display:none'" : "" ) ?>>
				<td class="col230px">
					<label for="remise-day-valid"><span class="mandatory">*</span> <?php print _('Délai après commande :'); ?></label>
				</td>
				<td>
					<input type="text" name="reminder-days" id="reminder-days" class="price" maxlength="8" value="<?php echo $currentConfig['days'] ?>" />&nbsp; <?php print _('jours(s)'); ?>
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" value="<?php print _('Enregistrer'); ?>" name="save-reminder" />
			</td></tr>
		</tfoot>
	</table>
</form>
<script>
	$(document).ready( function(){
		$(".active-radio").click(function(e){
			if($(".active-radio:checked").val() == 0 ){
				$(".details").hide();
			}else{
				$(".details").show();
			}
		});
	});
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
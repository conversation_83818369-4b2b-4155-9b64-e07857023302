<?php

	/**	\file
	 *	Ce fichier réalise la déconnexion de l'utilisateur. Une fois déconnecté, il ne peut
	 *	plus accéder au back-office sans devoir s'authentifier à nouveau
	 */
	
	if( !isset($admin_account) || $admin_account === null ){
		require_once('users.inc.php');

		// Vérifie l'identifiant de l'utilisateur
		if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || !gu_users_exists($_SESSION['usr_id']) ){
			header('Location: /admin/');
			exit;
		}

		// Déconnecte l'utilisateur
		gu_users_disconnect();

		header('Location: /admin/');
		exit;
	}else{
		// Appel de la fonction de déconnexion d'un administrateur
		$admin_account->disconnect();

		// Supprime les variables lié à l'administration mutualisé
		if( isset($_SESSION['admin_tnt_id']) ){
			unset($_SESSION['admin_tnt_id']);
		}

		// Suppression d'une quelconque connexion SAML
		require_once('SAML.inc.php');
		RiaSaml::getInstance()->logout();

		header('Location: /');
		exit;
	}

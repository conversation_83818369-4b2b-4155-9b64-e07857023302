<?php if (!isset($alerts)) {
    header('Location: \admin');
}?>
<table id="table-availability" class="checklist large" >
	<caption><?php print _('Alertes de').' '.$type ?></caption>
	<thead>
		<tr>
			<th id="prd-sel"><input type="checkbox" name="prd-sel[]" onclick="checkAllClick(this)" /></th>
			<th id="prd-ref"><?php print _('Référence')?></th>
			<th id="prd-name"><?php print _('Désignation')?></th>
			<th id="date"><?php print _('En attente depuis le')?></th>
			<th id="once"><?php print _('À chaque').' '.$action ?></th>
			<th id="last"><?php print _('Dernier envoi')?></th>
		</tr>
	</thead>
	<tfoot>
		<tr>
			<td colspan="2" class="tdleft">
				<input type="submit" name="del-sel" value="<?php print _('Supprimer')?>" />
			</td>
			<td colspan="4">
				<label for="ref"><?php print _('Ajouter une référence :')?></label>
				<input type="text" name="ref" id="ref" maxlength="16" />
				<input type="submit" name="add-ref" value="<?php print _('Ajouter')?>" />
			</td>
		</tr>
	</tfoot>
	<tbody>
		<?php
			if( !ria_mysql_num_rows($alerts) ){ ?>
				<tr><td colspan="6"><?php printf(_('Aucune alerte de %s enregistrée'), $type)?></td></tr>
			<?php }else{
				while ($r = ria_mysql_fetch_assoc($alerts)) {

					$alert = $alertFormatage($r);
					$sync = '';
					$rp = prd_products_get_simple( $alert['id'] );
					if( $rp && ria_mysql_num_rows($rp) ){
						$p = ria_mysql_fetch_array( $rp );
						$sync = view_prd_is_sync( $p ).' ';
						$alert['ref'] = $p['ref'];
					}
					$rcat = prd_products_categories_get($alert['id'], true);
					if( $rcat && ria_mysql_num_rows($rcat) ){
						$cat = ria_mysql_fetch_array($rcat);
						$alert['ref'] = '<a href="/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$alert['id'].'" target="_bank">'.htmlspecialchars($alert['ref']).'</a>';
					}
					?>
					<tr>
						<td headers="prd-sell"><input type="checkbox" name="prd-sel[]" value="<?php echo $alert['id']?>" id="prd-sell-<?php echo $alert['id']?>" /></td>
						<td headers="prd-ref"><?php echo $sync.$alert['ref']?></td>
						<td headers="prd-name"><?php echo htmlspecialchars($p['name'])?></td>
						<td headers="date" class="align-right"><?php echo ria_date_format($alert['date_created'])?></td>
						<td headers="once" class="align-right"><?php echo !$alert['once'] ? _('Oui') : _('Non')?></td>
						<td headers="last" class="align-right"><?php echo ria_date_format($alert['last_notified'])?></td>
					</tr>
			<?php } ?>
		<?php } ?>
	</tbody>
</table>
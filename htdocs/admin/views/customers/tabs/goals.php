<?php

	/**	\file goals.php
	 *	Cette page est utilisée en include par le fichier htdocs/admin/customers/edit.php pour permettre la saisie
	 *	des objectifs.
	 */

	if (!isset($usr)) {
		header('Location: /admin/customers/index.php');
	}
	
		if( !isset($_GET['goal']) ){	
			$r_objectifs = obj_objectifs_get_kpi();
			$actived = obj_objectifs_get_actived($_GET['usr']);?>

			<table symmary="<?php echo _("Liste des objectifs pouvant être activé pour ce représentant"); ?>" class="checklist" id="goals">
				<caption><?php print _('Liste des objectifs'); ?></caption>
				<tbody><?php
					while( $objectifs = ria_mysql_fetch_assoc($r_objectifs) ){?>
						<tr>
							<?php
								if( is_array($actived) && in_array($objectifs['id'], $actived) ){?>
									<td class="col70px"><input class="btn-action actif" type="button" id="<?php print $objectifs['id']; ?>" value="<?php print _('Actif'); ?>" title="<?php print _('Cliquez pour désactiver cette objectif'); ?>"></td><?php
								}else{?>
									<td class="col70px"><input class="btn-action inactif" type="button" id="<?php print $objectifs['id']; ?>" value="<?php print _('Inactif'); ?>" title="<?php print _('Cliquez pour activer cette objectif'); ?>"></td><?php
								}
							?>
							<td><a class="link_goal" href="#" id="<?php print $objectifs['id']; ?>"><?php print $objectifs['name']; ?></a></td>
						</tr><?php
					}
				?></tbody>
				<tfoot>
					<tr>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				</tfoot>
			</table><?php
		}else{
			$goals = ria_mysql_fetch_assoc(obj_objectifs_get_kpi($_GET['goal']));
			?><div class="notice"><?php echo _("Vous pouvez paramétrer ci-dessous l'objectif"); ?> "<?php print htmlspecialchars($goals['name']); ?>" <?php echo _("par période mensuelle, trimestrielle ou annuelle. Vous pouvez définir une période de date à date en utilisant la zone \"Périodes personnalisées\".</p><p>Si vous souhaitez revenir à la liste des objectifs cliquez sur le lien suivant :"); ?> <a href="/admin/customers/edit.php?usr=<?php print $_GET['usr']; ?>&tab=goals"><?php echo _("Revenir à la liste des objectifs"); ?></a></div><br />
			
			<div id="goals"><?php 
				print view_goals($_GET['usr'], $_GET['goal'], date('Y')); 
			?></div><?php
		}
		?>


		<script><!--
		<?php

			if( isset($_GET['goal']) ){	?>
				$(document).ready(
					function(){
						loadDatePicker();

						formatInput();
					}
				)

				function formatInput(){
					$('.obj-value-input input').each(function(){
						var type = $(this).data('type');
						var val = $(this).val();
						
						if ($.trim(val)) {
							switch (type) {
								case 'int' : {
									val = parseFInt(val);
									if (isNaN(val)) {
										$(this).css( 'color', 'red' );
										break;
									}

									val = number_format(val, 0, '', ' ');
									break;
								}
								case 'decimal' : {
									val = parseFFloat(val);
									if (isNaN(val)) {
										$(this).css( 'color', 'red' );
										break;
									}

									val = number_format(val, 2, ',', ' ');
								}
							}
							
							$(this).val( val );
						}
					});
				}

				function loadDatePicker(){
					$('input.datepicker').each(function(){
						var temp = this ;
						
						// Implémente le sélecteur de date sur chacun d'entre eux.
						$(this).DatePicker({
							format:'d/m/Y',
							date: $(this).val(),
							current: $(this).val(),
							starts: 1,
							beforeShow: function(){    
								$(".datepicker").css('font-size', '4px') 
							},
							onChange: function(formated, dates){
								var date = $(temp).val();
								if(dates != 'Invalid Date'){
									$(temp).val(formated);
									if( formated!=date )
									$(temp).DatePickerHide();
								}
							}
						});
					});	
				}

				$(document).on('click', '#save-new-period', function(e){
					if( $('#name-period').val().length && $('#start-period').val().length && $('#end-period').val().length ){
						$.ajax({
							url: "/admin/customers/ajax-goals.php?new=1&usr=<?php print $_GET['usr']; ?>&obj=<?php print $_GET['goal']; ?>&name="+$('#name-period').val()+"&start="+$('#start-period').val()+"&end="+$('#end-period').val(),
							success : function(result){
								if( result != 'error' ){
									$("#new-period").before(result);
								}

								$('#new-period').hide();
								$('#name-period').val('');
								$('#start-period').val('');
								$('#end-period').val('');

								$('#add-period').parent().show();
							}
						});
					}
				});

				$(document).on('click', '#cancel-new-period', function(e){
					$('#new-period').hide();
					$('#add-period').parent().show();
					return false;
				});
				
				
				
				$(document).on('click', '#add-period', function(e){
					$('#new-period').show();
					$('#add-period').parent().hide();
					return false;
				});
			
				function addPeriodsPerso( id, name, value, dateStart, dateEnd ){
					
					html = '<tr id='+id+'>';
					html += '	<td>'+name+' (du '+dateStart+' au '+dateEnd+')</td>';
					html += '	<td class="align-right">-- €</td>';
					html += '	<td class="align-center"><span class="obj-value-input"><input class="obj-value price" name="perso['+id+']" type="text" value="'+value+'" placeholder="--"> €</span><img class="obj-del" id="'+id+'" title="<?php print _("Supprimer"); ?>" alt="<?php print _("Supprimer"); ?>" src="/admin/images/del.svg" ></td>'
					html += '</tr>';
					$('#period-perso').after(html);
					
				}
			
				$(document).on('click', '.obj-del', function(e){
					var id = parseInt($(this).data('id'));
					if (isNaN(id)) {
						return false;
					}
					
					$.ajax({
						url : '/admin/customers/ajax-goals.php?remove=1&id='+id,
						success: function(result){
							if( result != 'error' ){
								$('tr#'+id).remove();
							}
						}
					});

					return false;
				});
			
				function cancel_new_period(){
					$('#new-period').remove();
				}
				
				$(document).on('click', '#period-month-validate', function(){
					$('.obj-value-month').val($('#period-month').val());
				})
				
				$(document).on('click', '#period-trimester-validate', function(){
					$('.obj-value-trimester').val($('#period-trimester').val());
				})
				
				
				$(document).on('click', '#cancel', function(){
					window.location.href = 'edit.php?usr=<?php print $_GET['usr'];?>&prf=<?php print $_GET['prf']; ?>&tab=goals';
				})
				
				function reload_goal( idGoal, year){
					$.ajax({
						url : '/admin/customers/ajax-goals.php?reload=1&usr=<?php print $_GET['usr']; ?>&goal='+idGoal+'&year='+year,
						beforeSend : function(){
							$('.load-ajax-opacity').show();
						},
						success : function(html){
							$('div#goals').html(html);
							loadDatePicker();
							formatInput();
						}
					}).done(function(){
						$('.load-ajax-opacity').hide();
					}).fail(function(){
						$('.load-ajax-opacity').hide();
					})
				}
				
				$(document).on('blur', 'input.price', function(){
					var type = $(this).data('type');
					var val = $(this).val();
					
					if ($.trim(val)) {
						switch (type) {
							case 'int' : {
								val = parseFInt(val);
								if (isNaN(val)) {
									$(this).css( 'color', 'red' );
									break;
								}

								val = number_format(val, 0, '', ' ');
								break;
							}
							case 'decimal' : {
								val = parseFFloat(val);
								if (isNaN(val)) {
									$(this).css( 'color', 'red' );
									break;
								}

								val = number_format(val, 2, ',', ' ');
							}
						}
						
						$(this).val( val );
					}
				});
				
				$(document).on('focus', 'input.price', function(){
					$(this).css( 'color', 'black' );
					$(this).attr( 'title', '' );
				});<?php

			}else{ ?>

				$('.link_goal').click(function(e){
					e.preventDefault();
					document.location.href="/admin/customers/edit.php?usr=<?php print $_GET['usr'];?>&prf=<?php print $_GET['prf']; ?>&tab=goals&goal="+$(this).attr('id');
				})
				
				
				$(document).on( 'click', '.actif', function(){
					var button = $(this);
					$.ajax({
						url: "/admin/customers/ajax-goals.php?activate=0&goal="+$(this).attr('id')+"&usr=<?php print $_GET['usr'];?>",
						success: function(result){
							if( result != 'error' ){
								button.removeClass('actif');
								button.addClass('inactif');
								button.attr('title', "<?php print _('Cliquez pour activer cette objectif'); ?>");
								button.val('Inactif');
							}
						}
					})
				});

				$(document).on( 'click', '.inactif', function(){
					var button = $(this);
					$.ajax({
						url: "/admin/customers/ajax-goals.php?activate=1&goal="+$(this).attr('id')+"&usr=<?php print $_GET['usr'];?>",
						success: function(result){
							if( result != 'error' ){
								button.removeClass('inactif');
								button.addClass('actif');
								button.attr('title', "<?php print _('Cliquez pour désactiver cette objectif'); ?>");
								button.val('Actif');
							}
						}
					})
				})<?php
			}?>
		//--></script>
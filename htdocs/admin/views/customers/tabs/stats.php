<?php if (!isset($usr)) {
	header('Location: /admin/customers/index.php');
}?>
<?php view_import_highcharts(); ?>
<script><!--
	var urlHighcharts = '';
--></script>
<table width="520">
	<caption><?php print _('Centres d\'intérêt de l\'utilisateur')?> <?php print htmlspecialchars($adr['lastname'].', '.$adr['firstname']); ?></caption>
	<col width="255" /><col width="*" />
<tbody>
	<tr><th colspan="2"><?php print _('Produits les plus consultés')?></th></tr>
	<tr>
		<td colspan="2"><?php
			require_once( 'admin/highcharts/graph-user-views-products.php' );
		?></td>
	</tr>
	<tr>
		<th colspan="2"><?php print _('Catégories les plus consultées')?></th>
	</tr>
	<tr>
		<td colspan="2"><?php
			require_once( 'admin/highcharts/graph-user-views-categories.php' );
		?></td>
	</tr>
	<tr>
		<th colspan="2"><?php print _('Intentions d\'achat non finalisées')?></th>
	</tr>
	<tr>
		<td colspan="2">
			<table width="100%">
				<tr>
					<th id="prd-ref"><?php print _('Référence')?></th>
					<th id="prd-name"><?php print _('Désignation')?></th>
					<th id="prd-price-ht"><?php print _('Prix')?> <acronym title="<?php print _('Hors Taxes')?>"><?php print _('HT')?></acronym></th>
					<th id="prd-whishes"><?php print _('Mises en panier')?></th>
				</tr>
			<?php
				$rproducts = ord_products_intentions_get( $_GET['usr'] );
				if( !$rproducts || !ria_mysql_num_rows($rproducts) ){
					?>
						<tr>
							<td colspan="4"><?php print _('Aucun produit mis en panier puis abandonné')?></td>
						</tr>
					<?php
				}else{
					// on crée un tabluea des souhaits non finalisés (en clé l'ID, en valeur le nombre de souhaits)
					$ar_products = array();
					while( $product = ria_mysql_fetch_array($rproducts) ){
						if( !prd_products_is_port_id($product['prd_id']) )
							$ar_products[ $product['prd_id'] ] = $product['count_add'];
					}
					// on charge le détail de tous les produits en une requête
					$rp = prd_products_get( array_keys($ar_products) );
					if( !$rp || !ria_mysql_num_rows($rp) ){
					?>
						<tr>
							<td colspan="4"><?php print _('Aucun produit mis en panier puis abandonné')?></td>
						</tr>
					<?php
					}else{
						while( $p = ria_mysql_fetch_array($rp) ){
					?>
						<tr>
							<td headers="prd-ref">
								<a href="../catalog/product.php?prd=<?php print $p['id']; ?>&cat=0"><?php print htmlspecialchars($p['ref']); ?></a>
							</td>
							<td headers="prd-name"><?php print htmlspecialchars($p['name']); ?></td>
							<td headers="prd-price-ht" align="right"><?php print number_format($p['price_ht'],2,',',' '); ?></td>
							<td headers="prd-whishes" align="right"><?php print number_format($ar_products[$p['id']],0,',',' '); ?></td>
						</tr>
					<?php
						}
					}
				}
			?>
			</table>

		</td>
	</tr>
</tbody>
</table>
<?php if (!isset($usr)) {
	header('Location: /admin/customers/index.php');
}?>
<?php
	require_once('prd/reviews.inc.php');
?>
<?php if( !isset($_GET['rvw']) ){ ?>
	<table class="checklist">
		<caption><?php print _('Avis consommateur')?></caption>
		<col width="25" /><col width="247" /><col width="145" /><col width="80" />
	<thead>
		<tr>
			<th><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th><?php print _('Titre')?></th>
			<th><?php print _('Produit')?></th>
			<th><?php print _('Publié ?')?></th>
		</tr>
	</thead>
	<tfoot>
		<tr><td colspan="4">
			<input type="submit" name="cancel-rvw" value="<?php print _('Annuler')?>" />
		</td></tr>
	</tfoot>
	<tbody>
		<?php
			$reviews = prd_reviews_get(0,0,$_GET['usr']);
			if( !ria_mysql_num_rows($reviews) )
				print '<tr><td colspan="4">'._('Aucun avis consommateur').'</td></tr>';
			else{
				while( $r = ria_mysql_fetch_array($reviews) ){
					print '<tr'.( $r['prd_deleted'] ? ' class="deleted" title="'._('Ce produit a été supprimé').'"' : '' ).'>';
					print '<td><input type="checkbox" class="checkbox" name="rvw[]" value="'.$r['id'].'" /></td>';
					print '<td><a href="edit.php?usr='.$_GET['usr'].'&amp;rvw='.$r['id'].'">'.( $r['name']!='' ? htmlspecialchars($r['name']) : _('Editer avis') ).'</a></td>';
					print '<td>';
					$url = '';
					if( $r['prd_id']>0 && ($p = ria_mysql_fetch_array(prd_products_get_simple( $r['prd_id'] ))) ){
						print view_prd_is_sync( $p );
						$rcat = prd_products_categories_get($p['id'], true);
						if( $cat = ria_mysql_fetch_array(prd_products_categories_get($p['id'], true)) )
							$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$p['id'];
					}
					print $url!='' ? '<a href="'.$url.'" target="_bank">'.htmlspecialchars($r['prd_name']).'</a>' : htmlspecialchars($r['prd_name']);
					print '</td>';
					print '<td>'.( $r['publish']==1 ? _('Oui') : _('Non') ).'</td>';
					print '</tr>';
				}
			}
		?>
	</tbody>
	</table>
<?php }else{ 
		$review = ria_mysql_fetch_array(prd_reviews_get($_GET['rvw']));
		
		$url = $sync = '';
		if( $review['prd_id']>0 && ($p = ria_mysql_fetch_array(prd_products_get_simple( $review['prd_id'] ))) ){
			$sync = view_prd_is_sync( $p );
			$rcat = prd_products_categories_get($p['id'], true);
			if( $cat = ria_mysql_fetch_array(prd_products_categories_get($p['id'], true)) )
				$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$p['id'];
		}
	?>
	<input type="hidden" name="rvw" value="<?php print $review['id']; ?>" />
	<!-- Fiche Avis consommateur -->
	<table cellpadding="0" cellspacing="0">
		<caption><?php print _('Avis consommateur')?></caption>
		<col width="150" />
	<tfoot>
		<tr><td colspan="2">
			<input type="submit" name="save-review" value="<?php print _('Enregistrer')?>" />
			<input type="submit" name="cancel-edit-review" value="<?php print _('Annuler')?>" />
			<input type="submit" name="delete-review" value="<?php print _('Supprimer')?>" />
		</td></tr>
	</tfoot>
	<tbody>
		<tr>
			<td><label for="author"><?php print _('Produit :'); ?></label></td>
			<td><span id="author"><?php 
				print $sync!='' ? $sync.'&nbsp;' : '';
				print $url!='' ? '<a href="'.$url.'" target="_bank">'.htmlspecialchars($review['prd_name']).'</a>' : htmlspecialchars($review['prd_name']);
			?></span></td>
		</tr>
		<tr>
			<td><label for="date"><?php print _('Date de création :'); ?></label></td>
			<td><span id="date"><?php print $review['date']; ?></span></td>
		</tr>
		<tr>
			<td><label for="name"><?php print _('Titre de la critique :'); ?></label></td>
			<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($review['name']); ?>" maxlength="75" /></td>
		</tr>
		<tr>
			<td><label for="desc"><?php print _('Critique détaillée :'); ?></label></td>
			<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($review['desc']); ?></textarea></td>
		</tr>
		<?php if( $config['prd_reviews_note'] ){ ?>
		<tr>
			<td><label for="note"><?php print _('Note attribuée au produit :'); ?></label></td>
			<td>
				<?php
					for( $note=$config['prd_reviews_note_max']; $note>0; $note-=$config['prd_reviews_note_step'] ){
						print '<input type="radio" class="radio" name="note" id="note-'.$note.'" value="'.$note.'" '.($review['note']==$note ? 'checked="checked"':'').'/> ';
						print '<label id="lbl-note-'.$note.'" for="note-'.$note.'" title="'.$note.' / '.$config['prd_reviews_note_max'].'"><i>'.$note.' / '.$config['prd_reviews_note_max'].'</i></label><br />';
					}
				?>
			</td>
		</tr>
		<?php } ?>
		<tr>
			<td><label for="publish"><?php print _('Publication :'); ?></label></td>
			<td>
				<input type="checkbox" class="checkbox" name="publish" id="publish" value="1" <?php print $review['publish'] ? 'checked="checked"':''; ?> /> 
				<label for="publish"><?php print _('Publier cet avis sur le site')?></label>
			</td>
		</tr>
	</tbody>
	</table>
	<!-- /Fiche Avis consommateur -->
<?php } ?>
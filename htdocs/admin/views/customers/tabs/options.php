<?php if (!isset($usr)) {
	header('Location: /admin/customers/index.php');
}?>
<?php
	// Charge les états de commande pour lesquelles des notifications sont prévuées
	$states = array();
	$r_states = gu_ord_alerts_get($_GET['usr']);
	while( $r = ria_mysql_fetch_array($r_states) )
		$states[] = $r['state_id'];

	$email = gu_users_get_email($_GET['usr']);
	$r_news = nlr_subscribers_get(NEWSLETTER_TYPE_INSCRIPT, 0, $email, 0);
?>
<table class="table-options" cellpadding="0" cellspacing="0">
	<caption><?php print _('Alertes emails')?></caption>
	<tfoot>
		<tr><td>
			<input type="submit" name="save-options" value="<?php print _('Enregistrer')?>" />
			<input type="submit" name="cancel-options" value="<?php print _('Annuler')?>" />
			<input type="submit" name="default-options" value="<?php print _('Par défaut')?>" title="<?php print _('Restaurer les options par défaut')?>" />
		</td></tr>
	</tfoot>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-1" value="1" <?php if(in_array(1,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-1"><?php print _('Relances de panier')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-3" value="3" <?php if(in_array(3,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-3"><?php print _('Commande enregistrée / En attente de paiement')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-4" value="4" <?php if(in_array(4,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-4"><?php print _('Paiement reçu / Commande à traiter')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-5" value="5" <?php if(in_array(5,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-5"><?php print _('Commande en cours de traitement')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-11" value="11" <?php if(in_array(11,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-11"><?php print _('Commande en cours de préparation')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-6" value="6" <?php if(in_array(6,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-6"><?php print _('Commande prête à expédier')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-7" value="7" <?php if(in_array(7,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-7"><?php print _('Commande expédiée')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-24" value="24" <?php if(in_array(24,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-24"><?php print _('Commande disponible en magasin')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-<?php print _STATE_INV_STORE; ?>" value="<?php print _STATE_INV_STORE; ?>" <?php if(in_array(_STATE_INV_STORE,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-<?php print _STATE_INV_STORE; ?>"><?php print _('Commande retirée en magasin')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-8" value="8" <?php if(in_array(8,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-8"><?php print _('Commande facturée')?></label>
	</td></tr>
	<tr><td>
		<input type="checkbox" class="checkbox" name="ord-states[]" id="ord-state-9" value="9" <?php if(in_array(9,$states)) print 'checked="checked"'; ?> />
		<label for="ord-state-9"><?php print _('Commande annulée')?></label>
	</td></tr>
	<tr><td>
		<label for="alert-cc"><?php print _('Envoyer une copie des alertes emails à l\'adresse suivante :'); ?></label>
		<input type="text" name="alert-cc" id="alert-cc" size="45" maxlength="75" value="<?php print htmlspecialchars($usr['alert_cc']); ?>" />
	</td></tr>
</table>

<table class="table-options" width="225" cellpadding="0" cellspacing="1">
	<caption><?php print _('Abonnements aux newsletters')?></caption>
	<?php 
		if ($r_news && ria_mysql_num_rows($r_news)>0){ ?>
			<tfoot>
				<tr><td colspan="2" style="background-color: #eee; padding: 3px;">
					<input type="submit" name="unsubscribe-newsletter" value="<?php print _('Désinscrire')?>" />
				</td></tr>
			</tfoot>
			<?php
				while ($newsletter = ria_mysql_fetch_assoc($r_news)){
					?>
					<tr>
						<td>
							<input type="checkbox" class="checkbox" name="sub[]" value="<?php print $newsletter['cat_id']; ?>" />
						</td>
						<td><?php print $newsletter['cat']; ?></td>
					</tr>
					<?php
				}
		} else { ?>
			<tr><td colspan="2">
				<?php print _('Le client n\'est abonné à aucune newsletter')?>
			</td></tr>
			<?php
		}
	?>
</table><?php

	$r_cgv = gu_users_cgv_get( $_GET['usr'] );

	if( $r_cgv && ria_mysql_num_rows($r_cgv) ){?>
		<table class="table-options table-cgv" cellpadding="0" cellspacing="1">
			<caption><?php print _('Acceptation des CGV'); ?></caption>
			<thead>
				<tr>
					<th><?php print _('Version'); ?></th>
					<th><?php print _('Date d\'aceptation'); ?></th>
				</tr>
			</thead>
			<tbody><?php
				while( $cgv = ria_mysql_fetch_assoc($r_cgv) ){
					$web_site = wst_websites_get_name( $cgv['wst'] );	?>
					<tr>
						<td><a href="/admin/config/cgv/articles.php?ver=<?php print $cgv['id']; ?>&wst=<?php print $cgv['wst']; ?>"> <?php print $web_site.' - '.$cgv['name']; ?></a></td>
						<td><?php print date('d/m/Y', strtotime($cgv['date_accepted'])).' à '.date('H:i', strtotime($cgv['date_accepted'])) ; /*print dateheureunparse($cgv['date_accepted']);*/ ?></td>
					</tr><?php
				}?>
			</tbody>
		</table><?php
	}?>
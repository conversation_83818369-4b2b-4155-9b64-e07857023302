<?php
	if (!isset($usr)) {
		header('Location: /admin/customers/index.php');
		exit;
	}
?>
<div id="usr-contacts">
	<a name="new-msg"></a>
	<input type="hidden" name="tab_file" id="tab_file" value="" />
	<input type="hidden" name="hd-usr" id="hd-usr" value="<?php print $_GET['usr']; ?>" />
	<div class="clear"></div>
		<?php
			$have_rep = null;
			$have_usr = null;
			$dir = 'asc';
			$file = str_replace(__DIR__.'/', '', __FILE__);
			$title_page = view_usr_is_sync($usr)._(' Utilisations du formulaire de contact');
			$type = ria_mysql_fetch_assoc( gu_messages_types_get( 0, 'CONTACT') );

			isset($_GET['start'])? $date_start=$_GET['start'] : $date_start=false;
			isset($_GET['end'])? $date_end=$_GET['end'] : $date_end=false;
			isset($_GET['ord'])? $ord=$_GET['ord'] : $ord=false;

			$messages = false;

			$key = array();
			if( isset($_GET['ord']) ){
				$key['id'] = $_GET['ord'];
			}

		
			// Récupère les commandes du client
			$orders = false;
			$r_order = ord_orders_get_simple($key, array(), array('usr_id' => $_GET['usr']), array());
			while( $order = ria_mysql_fetch_assoc($r_order) ){
				$orders[] = $order['id'];
			}

			// Récupère les messages liés aux commandes du client
			if( $orders ){
				$r_msg = messages_get( 0, '', 0, 0, 0, false, true, false, 0, 0, false, false, null, null, false, false, null, false, '', false, false, false, false, false, $orders);
				while( $msg = ria_mysql_fetch_assoc($r_msg) ){
					$messages[$msg['id']]=$msg;					
				}
			}
			

			// Récupère les messages liés au client
			$r_msg = messages_get( $_GET['usr'], '', 0, 0, 0, false, true, false, 0, 0, $date_start, $date_end);
			while( $msg = ria_mysql_fetch_assoc($r_msg) ){
				if( isset($_GET['ord']) && $msg['ord_id'] != null && $msg['ord_id'] != $_GET['ord']){
					continue;
				}
				$messages[$msg['id']]=$msg;					
			}
			
			require __DIR__.'/../../../moderation/view_contact.php';
		?>
	<div id="cnt-footer">
		<input class="action" type="button" name="write-msg" value="<?php print _('Nouveau message')?>" onclick="show_form_rep('0');" />
	</div>
</div>
<div class="clear"></div>
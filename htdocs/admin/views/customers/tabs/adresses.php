<?php
if( !isset($usr) && !isset($_GET['popup']) ){
    header('Location: /admin/customers/index.php');
}


if(isset($_GET['carnet'])){ // insère le CSS si c'est une popup
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _('Adresse'));
	define('ADMIN_NO_MOBILE_STYLE', true);

	require_once('admin/skin/header.inc.php');
}


$error = null;
$ord_id = null;
$ord = null;
$is_dlv_adr = isset($_GET['dlv-adr']);
if( isset($_GET['ord']) && ord_orders_exists($_GET['ord']) ){
	$ord_id = $_GET['ord'];
	$r_ord = ord_orders_get_with_adresses($_GET['usr'], $ord_id);
	if( $r_ord && ria_mysql_num_rows($r_ord) ){
		$ord = ria_mysql_fetch_assoc($r_ord);
	}

	if( isset($_GET['adr']) ){
		// Ajout d'une adresse magasin comme adresse de livraison
		if( substr( $_GET['adr'], 0, 6 ) === "store-" ){
			if( !ord_orders_set_dlv_store( $_GET['ord'], substr($_GET['adr'], 6) ) ){
				$error = _('Erreur lors de la modification de l\'adresse de livraison');
			}
		}else{ // Met à jour l'adresse de livraison
			$r_adr = gu_adresses_get($_GET['usr'], $_GET['adr']);
			if( !$r_adr || !ria_mysql_num_rows($r_adr) ){
				$error = _('Adresse inconnue');
			}else{
				$adr = ria_mysql_fetch_assoc($r_adr);
				if( isset($_GET['dlv-adr']) ){
					// Supprime le magasin de livraison pour le remplacer par l'adresse de livraison sélectionnée
					if( !ord_orders_set_dlv_store($_GET['ord']) || !ord_orders_adr_delivery_set($ord['id'], $adr['id']) ){
						$error = _('Erreur lors de la modification de l\'adresse de livraison');
					}else{
						$r_ord = ord_orders_get_with_adresses($_GET['usr'], $ord_id);
						if ($r_ord && ria_mysql_num_rows($r_ord)) {
							$ord = ria_mysql_fetch_assoc($r_ord);
						}
						if ($ord['rly_id']) ord_orders_set_relay( $ord_id, null );
						if ($ord['srv_id']) ord_orders_set_dlv_store( $ord_id );
					}
				}else{
					if( !ord_orders_adr_invoices_set($ord['id'], $adr['id']) ){
						$error = _('Erreur lors de la modification de l\'adresse de facturation');
					}else{
						$r_ord = ord_orders_get_with_adresses($_GET['usr'], $ord_id);
						if( $r_ord && ria_mysql_num_rows($r_ord) ){
							$ord = ria_mysql_fetch_assoc($r_ord);
						}
					}
				}
			}
		}
	}
}

	if ($error) {
		echo '<div class="error">' . _($error) . '</div>';
	}

	// Affiche les adresses de facturation et de livraison
	$types = array(
		array(
			'name' => _('Adresse de facturation'),
			'name_plural' => _('Adresses de facturation'),
			'type' => 'invoice'
		),
		array(
			'name' => _('Adresse de livraison'),
			'name_plural' => _('Adresses de livraison'),
			'type' => 'delivery'
		),
		array(
			'name' => _('Adresse magasin'),
			'name_plural' => _('Adresses magasin'),
			'type' => 'store'
		),
	);
	$id_adr_invoice = null;
	foreach( $types as $t ){

		$adr_array = array();

		if( in_array( $t['type'], array('invoice', 'delivery')) ){

			$addresses = gu_adresses_get(
				$_GET['usr'], 0, '', $sort=array(), false, '', '', '', '', '', '', '', false, false, false, false, ($t['type'] == 'invoice')? true : false
			);

			while( $addresse = ria_mysql_fetch_assoc($addresses) ){
				$adr_array[] = $addresse;
			}
			$count = ria_mysql_num_rows($addresses);
		}else{
			// Récupère les magasins lié au compte
			$r_relations = rel_relations_get( 0, $_GET['usr'], null, REL_RESELLER_STORE_HIERARCHY );
			$count = 0;
			while( $relation = ria_mysql_fetch_assoc($r_relations) ){
				// Récupère l'adresse du magasin
				$store = ria_mysql_fetch_assoc( dlv_stores_get($relation['dst_0']) );
				$user = ria_mysql_fetch_assoc( gu_users_get($_GET['usr']) );

				$addresse = array(
					'id' => 'store-'.$store['id'],
					'type_id' => 3,
					'society' => $store['title'],
					'title_name' => $user['title_name'],
					'firstname' => $user['adr_firstname'],
					'lastname' => $user['adr_lastname'],
					'address1' => $store['address1'],
					'addres2' => $store['address2'],
					'addres3' => '',
					'zipcode' => $store['zipcode'],
					'country' => $store['country'],
					'city' => $store['city'],
					'phone' => $store['phone'],
					'fax' => $store['fax'],
					'mobile' => '',
				);

				$adr_array[] = $addresse;

				$count++;
			}

			// On n'affiche pas le bloc "adresse magasin" si il n'y a aucune adresse
			if( $count == 0 ){
				break;
			}
		}

		print '<h2 class="title-addresses">';
		if( $count<2 ){
			print htmlspecialchars( $t['name'] );
		}else{
			print htmlspecialchars( $t['name_plural'] );
			if( !$t['type'] == 'delivery' ){
				print ' ('.number_format( $count, 0, ',', " " ) .')';
			}
		}
		print '</h2>';

		foreach( $adr_array as $adr ){
			// on sauvegarde l'id de l'adresse de facturation
			if( $t['type'] == 'invoice' ){
				$id_adr_invoice = $adr['id'];
			}

			print '<address class="h-card" id="address-'.$adr['id'].'">';
			switch( $adr['type_id'] ){
				default:
				case 3:
				case 2:
					print $adr['society'] ? '<span class="p-name">'.htmlspecialchars( $adr['society'] ).'</span>' : '' ;
				case 1:
					print trim($adr['title_name'].' '.$adr['firstname'].' '.$adr['lastname']) ?
						'<span class="p-name">'.htmlspecialchars( $adr['title_name'].' '.$adr['firstname'].' '.$adr['lastname'] ).'</span>' : '';
					break;
			}
			print '<span class="p-street-address">'.htmlspecialchars( $adr['address1'] ).'</span>';
			if( isset($adr['address2']) && trim( $adr['address2'] ) ){
				print '<span class="p-extended-address">'.htmlspecialchars( $adr['address2'] ).'</span>';
			}
			if( isset($adr['address2']) && trim( $adr['address3'] ) ){
				print '<span class="p-extended-address">'.htmlspecialchars( $adr['address3'] ).'</span>';
			}
			print '<span><span class="p-postal-code">'.htmlspecialchars( $adr['zipcode'] ).'</span> <span class="p-locality">'.htmlspecialchars( $adr['city'] ).'</span></span>';
			print '<span class="p-country-name">'.htmlspecialchars( $adr['country'] ).'</span>';

			if( trim( $adr['phone'] ) ){
				print '<span class="p-tel"><abbr title="'._('Téléphone').'">'._('Tél.').'</abbr> : '.htmlspecialchars( $adr['phone'] ).'</span>';
			}
			if( trim( $adr['fax'] ) ){
				print '<span class="p-tel">Fax : '.htmlspecialchars( $adr['fax'] ).'</span>';
			}
			if( trim( $adr['mobile'] ) ){
				print '<span class="p-tel"><abbr title="'._('Téléphone mobile').'">'._('Mob.').'</abbr> : '.htmlspecialchars( $adr['mobile'] ).'</span>';
			}

			if( $t['type'] != 'invoice' && $adr['id'] === $id_adr_invoice ) {
				print '<i>( '. _('Adresse de facturation').' )</i>';
			}elseif( $t['type'] != 'store' ){
				print '<a href="/admin/customers/popup-address.php?usr='.$_GET['usr']
					.'&adr='.$adr['id'].''
					.(isset($_GET['popup'])? '&popup=1' : '')
					. ($ord_id ? '&ord=' . intval($ord_id) : '')
					. ($is_dlv_adr ? '&dlv-adr=1' : '')
					.'" class="popup-adr'.($is_dlv_adr ? ' dlv' : ' inv').' iframe button" title="'._('Modifier cette adresse').'">'._('Modifier').'</a>';

			}

			// On n'affiche pas le bouton de selection de l'adresse pour l'adresse déjà selectionné
			$show_select_buton = false;
			if( $ord_id ){
				if( $t['type'] == 'store' ){
					if( $is_dlv_adr && 'store-'.$ord['str_id'] != $adr['id'] ){
						$show_select_buton = true;
					}
				}else{
					if( $ord['str_id'] ){
						$show_select_buton= true;
					}else{
						if( !$is_dlv_adr && $ord['inv_id'] != $adr['id'] || $is_dlv_adr && $ord['dlv_id'] != $adr['id'] ){
							$show_select_buton = true;
						}
					}
				}
			}

			if( $show_select_buton ){
				print '<a href="#" data-adr="'.$adr['id'].'" class="popup-adr'.($is_dlv_adr ? ' dlv' : ' inv').' iframe button select-addr" title="'._('Sélectionner cette adresse').'">'
						._('Sélectionner')
					.'</a>';
			}

			print '</address>';
		}

		//Affiche le bouton d'ajout d'adresse dans le bloc "adresse de livraison"
		if( $t['type'] == 'delivery' ){
			print '
				<address class="h-card" id="address-new">
					<a href="/admin/customers/popup-address.php?usr='.$_GET['usr'].''.(isset($_GET['popup'])? '&popup=1' : '') . ($ord_id ? '&ord=' . $ord_id : '') . ($is_dlv_adr ? '&dlv-adr=1' : '') .'" class="popup-adr add iframe button" id="button-add-adresse" title="'._('Ajouter une adresse de livraison').'">'._('Ajouter une adresse').'</a>
				</address>
			';
		}

		$select_address = '/admin/views/customers/tabs/adresses.php?usr='.$_GET['usr']
				. ($is_dlv_adr ? '&dlv-adr=1' : '')
				. (isset($_GET['popup'])? '&popup=1' : '')
				. ($ord_id ? '&ord=' . $ord_id : '') ;
	}


?>
<div class="clear"></div>

<script><!--

	$('.select-addr').click(function(){
		select_address( $(this).attr('data-adr') );
	});

	function select_address( adr_id ) {
		<?php if( isset($_GET['popup']) ){ ?>
			$.ajax({
				url: '<?php echo $select_address; ?>&adr='+adr_id,
				success: function (response){
					$('body').html(response);
					parent.update_order_user( <?php print $_GET['usr']; ?>, <?php print $ord_id; ?> );
				},
			});
		<?php }else{ ?>
			window.parent.$.fancybox.close();
		<?php } ?>

		return false;
	}
--></script>
<?php
if(isset($_GET['carnet'])){
	require_once('admin/skin/footer.inc.php');
}
?>
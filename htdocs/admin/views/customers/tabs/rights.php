<?php

    /** \file rights.php
     *  Ce fichier contient l'onglet Droits de la fiche client. Il affiche la liste des droits d'accès aux sites
     *  associés à ce compte client.
     */

	 // Ce fichier peut uniquement être utilisé comme include de la fiche client, il ne peut pas être ouvert directement.
	if( !isset($usr) ){
		header('Location: /admin/customers/index.php');
		exit;
	}
?>
<?php if( !tnt_tenants_is_yuto_essentiel() ){ ?>
	<?php if( tnt_tenants_have_websites() ){ ?>
		<?php if( $usr['prf_id']==PRF_ADMIN ){ ?>
			<p class="notice"><?php print _('Pour gérer ses droits dans l\'administration, cliquez sur le lien suivant :'); ?> <a href="/admin/customers/edit.php?usr=<?php print $_GET['usr']; ?>&tab=rights&siteright=0"><?php print _('Voir les droits sur l\'administration'); ?></a></p>
		<?php } ?>
		<p><?php print _('À partir d\'ici, vous pouvez définir les droits qui sont accessibles à ce compte.')?></p>
		<table id="prf-rights">
			<caption><?php print _('Gestion des droits')?></caption>
			<col width="25" /><col width="400" />
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save-rights" value="<?php print _('Enregistrer')?>" />
					<input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
				</td></tr>
			</tfoot>
			<tbody><?php
			
				$ar_rights = array();
				if( isset($error, $_POST['rghprf']) ){
					$ar_rights = $_POST['rghprf'];
				}else{
					$ar_rights = gu_users_rights_get_array( $_GET['usr'], false );
				}

				$rcat = gu_categories_rights_get();
				if( $rcat && ria_mysql_num_rows($rcat) ){
				
					$first = true;
					while( $cat = ria_mysql_fetch_array($rcat) ){
						
						$rghs = gu_rights_get( 0, '', $cat['id'] );
						if( $rghs && ria_mysql_num_rows($rghs) ){
							$count_rgh = gu_categories_rights_count_rights( $cat['id'] );
							$count_rgh_usr = gu_users_rights_count( $_GET['usr'], $cat['id'] );
							
							print '	<tr class="linecat '.( $first ? 'inexpansible' : 'expansible' ).'">';
							print '		<th class="checkall">';
							print '			<input class="catrgh" type="checkbox" name="catrgh[]" id="catrgh-'.$cat['id'].'" value="'.$cat['id'].'"'.( $count_rgh==$count_rgh_usr ? ' checked="checked"' : '' ).' />';
							print '		</th>';
							print '		<th>'.$cat['name'].'</th>';
							print '	</tr>';
							while( $rgh = ria_mysql_fetch_array($rghs) ){
								print '	<tr class="linergh linergh-'.$cat['id'].'"'.( $first ? '' : ' style="display: none;"' ).'>';
								print '		<td>';
								print '			<input class="checkright catrgh-'.$cat['id'].'" type="checkbox" name="rghprf[]" id="rghprf-'.$rgh['id'].'" value="'.$rgh['id'].'"'.( in_array($rgh['id'], $ar_rights) ? ' checked="checked"' : '' ).' />';
								print '		</td><td>';
								print '			<label for="rghprf-'.$rgh['id'].'" title="'.htmlspecialchars( $rgh['name']." - ".$rgh['desc'] ).'">'.$rgh['name'].'</label>';
								print '		</td>';
								print '	</tr>';
							}
							$first = false;
						}
					}
				
				} else {
					print '<tr><td colspan="2">&nbsp;</td></tr>';
				}
	?></tbody>
</table>
<?php } ?>

	<?php if( $config['use_catalog_restrictions'] ){ ?>
		<h3><?php print _('Droits sur le catalogue')?></h3>
		<input type="hidden" name="usr" value="<?php print $usr['id'] ?>"/>
		<input type="hidden" name="usr_value_txt" value="<?php print $usr['email']; ?>"/>
	<?php
		require_once('prd/restrictions.inc.php');
		require_once('prd/restrictions.admin.inc.php');

		$restrictions = prd_restrictions_get( false, $config['wst_id'], null, false, false, false, array('fld'=>_FLD_USR_ID, 'value'=> $usr['id']) ); // utilisateur
		$readonly = false;

		if( !$restrictions || !ria_mysql_num_rows( $restrictions ) ){
			$readonly = true;
			$restrictions = prd_restrictions_get( false, $config['wst_id'], $usr['id'] );
		}

		print view_conditions_table($restrictions, $readonly);
	}
	
}

if ($usr['prf_id'] != PRF_ADMIN) {
	$name = gu_users_get_name( $_GET['usr'] );
	$rchild = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', $_GET['usr'] );

	if( !$usr['parent_id'] && (!$rchild || !ria_mysql_num_rows($rchild)) ){?>
		<p><?php print _('Depuis cet onglet, vous pouvez gérer la hiérarchie entre les comptes clients. Vous allez pouvoir :'); ?></p>
		<ul>
			<li><?php print _('soit définir le responsable de')?> <?php print htmlspecialchars( trim($name) ); ?>,</li>
			<li><?php printf(_('soit utiliser le compte de %s comme responsable de plusieurs autres comptes clients.'), htmlspecialchars( $name ))?></li>
		</ul>
		
		<h3><?php print _('Définir le responsable de')?> <?php print htmlspecialchars( $name ); ?></h3>
		<p><?php printf(_('En utilisant le formulaire ci-dessous, vous pouvez placer le compte %s sous la responsabilité d\'un autre compte :'), htmlspecialchars( $name ))?></p>
		<label for="new-parent"><?php print _('Adresse mail du responsable :'); ?></label>
		<div>
			<input type="text" name="select-new-parent" id="select-new-parent" value="" size="45" maxlength="100" />
			<input type="hidden" name="new-parent" id="new-parent" value="" />
			<input type="submit" name="save-new-parent" id="save-new-parent" value="<?php print _('Enregistrer')?>" />
		</div>
	<?php } elseif( $usr['parent_id'] ){
		print '	<h3>'._('Définir un nouveau responsable à').' '.htmlspecialchars( $name ).'</h3>';
		
		$rparent = gu_users_get( $usr['parent_id'] );
		if( $rparent && ria_mysql_num_rows($rparent) ){
			$parent = ria_mysql_fetch_array( $rparent );
			print '	<p>'.sprintf(_('Le compte de %s est sous la responsabilité de %s'), $name, '<a href="/admin/customers/edit.php?usr='.$parent['id'].'&amp;tab=rights" target="_blank">'.$parent['adr_firstname'].' '.$parent['adr_lastname'].( trim($parent['society']) ?' ('.$parent['society'].')' : '' ).' &lt;'.$parent['email'].'&gt;</a>').', <a class="del" href="/admin/customers/edit.php?usr='.$_GET['usr'].'&amp;del-parent=1" onclick="return window.confirm(\''.sprintf(_('Vous êtes sur le point de retirer le compte <%s> comme responsable de <%s>.'), $parent['email'], $usr['email']).'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n\n'._('Êtes vous sur de vouloir continuer ?').'\');" title="'.sprintf(_('Retirer le compte <%s> comme responsable de <%s>'), $parent['email'], $usr['email']).'>">'._('Supprimer le lien').'</a></p>';
		} ?>
		<p><?php printf(_('En utilisant le formulaire ci-dessous, vous pouvez placer le compte %s sous la responsabilité d\'un autre compte :'), htmlspecialchars( $name ) ); ?></p>
		<label for="new-parent"><?php print _('Adresse mail du responsable :'); ?></label>
		<input type="text" name="select-new-parent" id="select-new-parent" value="" />
		<input type="hidden" name="new-parent" id="new-parent" value="" />
		<input type="submit" name="save-new-parent" id="save-new-parent" value="<?php print _('Enregistrer')?>" />
	<?php } 
	if( !$usr['parent_id'] ){ ?>
		<h3><?php print _('Comptes clients sous sa responsabilité')?></h3>
		<p><?php printf(_('Vous trouverez dans le tableau ci-dessous tous les comptes clients dont %s est responsable.'), $name)?></p>
		<table class="checklist" summary="<?php print _('Relations entre comptes clients')?>">
			<col width="125" /><col width="225" /><col width="225" /><col width="225" /><col width="100" />
			<thead>
				<tr>
					<th id="usr-ref"><?php print _('Code client')?></th>
					<th id="usr-email"><?php print _('Email')?></th>
					<th id="usr-addr"><?php print _('Adresse')?></th>
					<th id="usr-comp"><?php print _('Compléments')?></th>
					<th id="usr-supp"></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="5">
						<label for="new-relation"><?php print _('Ajouter un compte client (via son email) :'); ?></label>
						<input type="text" name="select-new-relation" id="select-new-relation" value="" />
						<input type="hidden" name="new-relation" id="new-relation" value="" />
						<input type="submit" name="save-new-relation" id="save-new-relation" value="<?php print _('Enregistrer')?>" />
						<input type="submit" name="new-usr-child" id="new-usr-child" value="<?php print _('Créer un nouveau compte lié')?>" />
					</td>
				</tr>
			</tfoot>
			<tbody><?php
				if( !$rchild || !ria_mysql_num_rows($rchild) ){
					print '<tr><td colspan="5">'._('Aucun compte client n\'est sous sa responsabilité.').'</td></tr>';
				} else {
				
					while( $child = ria_mysql_fetch_array($rchild) ){
						print '	<tr>';
						print '		<td headers="usr-ref">'.view_usr_is_sync($child).' <a href="/admin/customers/edit.php?usr='.$child['id'].'">'.( trim($child['ref']) ? $child['ref'] : $child['id'] ).'</a></td>';
						print '		<td headers="usr-email">'.$child['email'].'</td>';
						print '		<td headers="usr-addr">';
						print htmlspecialchars( $child['address1'] ).'<br />';
						print trim( $child['address2'] ) ? htmlspecialchars( $child['address2'] ).'<br />' : '';
						print trim( $child['address3'] ) ? htmlspecialchars( $child['address3'] ).'<br />' : '';
						print htmlspecialchars( $child['zipcode'] ).' '.htmlspecialchars( $child['city'] );
						print '</td>';
						print '		<td headers="usr-comp">';
						print trim( $child['phone'] ) ? _('Téléphone').' : '.htmlspecialchars( $child['phone'] ).'<br />' : '';
						print trim( $child['fax'] ) ? _('Fax').' : '.htmlspecialchars( $child['fax'] ).'</td>' : '';
						print '		<td headers="usr-supp">';


						print '			<a href="/admin/customers/edit.php?usr='.$_GET['usr'].'&amp;del-link-usr=1&amp;child='.$child['id'].'" onclick="return window.confirm('.sprintf('\''._('Vous êtes sur le point de retirer le compte <%s> comme responsable de <%s>.').'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n\n'._('Êtes vous sur de vouloir continuer ?').'\'', $usr['email'], $child['email']).')" title="'.sprintf(_('Retirer le compte <%s> comme responsable de <%s>'), $usr['email'], $child['email']).'>.">'._('Retirer le lien').'</a>';
						// print '			<br /><a class="del" href="/admin/customers/edit.php?usr='.$_GET['usr'].'&amp;del-usr-child=1&amp;child='.$child['id'].'"onclick="return window.confirm(\'Vous êtes sur le point de supprimer le compte <'.$child['email'].'>. \nCette opération est irréversible et ne pourra pas être annulée. \n\nÊtes vous sur de vouloir continuer ?\');" title="Supprimer le compte <'.$child['email'].'>">Supprimer</a>';
						print '		</td>';
						print '	</tr>';
					}
					
				}
			?></tbody>
		</table>
	<?php 
	}
 } ?>
<?php if (!isset($usr)) {
	header('Location: /admin/customers/index.php');
}?>
<?php
$type = _('disponibilité');
$action = _('remise en stock');
$alerts = gu_livr_alerts_get($_GET['usr']);
$alertFormatage = function($alert) {
	$formated = array();
	$ref = $alert['ref'];
	$sync = '';
	if (!is_null($alert['date_notified_en'])) {
		$last = new DateTime($alert['date_notified_en']);
		$alert['date_notified_en'] = $last->format('d/m/Y');
	}
	$formated = array(
		'id' => $alert['id'],
		'ref' => $ref,
		'date_created' => $alert['date_created'],
		'once' => $alert['restocking'] == 0 ? true : false,
		'last_notified' => $alert['date_notified_en']
	);
	return $formated;
};
?>
<?php include __DIR__.'/../alerts-table.php'?>
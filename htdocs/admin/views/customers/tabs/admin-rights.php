<?php

    /** \file admin-rights.php
     *  Ce fichier contient l'onglet Droits de la fiche client. Il affiche la liste des droits d'accès au back-office RiaShop
     *  associés à ce compte client.
     */

	// Ce fichier peut uniquement être utilisé comme include de la fiche client, il ne peut pas être ouvert directement.
	if( !isset($usr) ){
		header('Location: /admin/customers/index.php');
		exit;
	}

	$is_yuto = $_GET['usr'] > 0 && gu_users_get_prf($_GET['usr']) == PRF_SELLER && !ria_array_get($config, 'seller_admin_access', false);
?>
<input type="hidden" id="usr-id" value="<?php print $_GET['usr']; ?>">
<?php if( tnt_tenants_have_websites() ){ ?>
	<p class="notice"><?php print _("Pour gérer ses droits en dehors de l'administration, cliquez sur le lien suivant :"); ?> <a href="/admin/customers/edit.php?usr=<?php print $_GET['usr']; ?>&tab=rights&siteright=1"><?php echo _("Voir les droits sur les sites"); ?></a></p>
<?php } ?>

<?php
	print view_admin_rights($config['tnt_id'], $config['wst_id'], $config['USER_RIASTUDIO'], $is_yuto);
?>
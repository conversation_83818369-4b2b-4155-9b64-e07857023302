<?php

    /** \file rewards.php
     *  Ce fichier contient l'onglet Fidélité de la fiche client.
	 * 
	 * 	Il permet :
	 * 	- l'inscription et la désinscription au programme de fidélité
	 *  - la génération d'un lien de parrainage et le suivi des parrainages réalisés
	 *  - la consultation des statistiques du compte fidélité
	 *  - la consultation des points actifs et leur origine
	 *  - la consultation de l'ensemble des mouvements sur le compte fidélité
     */

	// Ce fichier peut uniquement être utilisé comme include de la fiche client, il ne peut pas être ouvert directement.
	if (!isset($usr)) {
		header('Location: /admin/customers/index.php');
		exit;
	}

?>
<?php if (isset($config['rwd_register_required']) && $config['rwd_register_required']) { ?>
	<h2><?php print _('Inscriptions')?></h2>

	<form action="/admin/customers/edit.php?usr=<?php print $_GET['usr']; ?>&amp;tab=rewards" action="post"><?php
		if (isset($_SESSION['rwu-uninscript-success'])) {
			print '<div class="success">'._('La désinscription a bien été prise en compte.').'</div>';
			unset($_SESSION['rwu-uninscript-success']);
		}
		if (isset($_SESSION['rwu-inscript-success'])) {
			print '<div class="success">'._('L\'inscription a bien été prise en compte.').'</div>';
			unset($_SESSION['rwu-inscript-success']);
		}

		if (rwd_users_is_enabled($_GET['usr'])) {
			?>
			<p><?php print _('Ce compte client participe actuellement au programme de fidélité :'); ?> 
				<input type="submit" name="rwu-uninscript" value="<?php print _('Désinscrire')?>" /></p>
			<?php
		}else{
			?>
			<p><?php print _('Ce compte client ne participe pas au programme de fidélité :'); ?> 
				<input type="submit" name="rwu-inscript" value="<?php print _('Inscrire')?>" /></p>
			<?php
		}
	?></form><br />

	<table class="checklist" cellspacing="0" cellpadding="0">
		<caption><?php print _('Période d\'inscription au programme')?></caption>
		<col width="305" /><col width="305" />
		<thead>
			<tr>
				<th id="rwu-inscript"><?php print _('Inscription')?></th>
				<th id="rwu-uninscript"><?php print _('Désinscription')?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				$r_inscript = rwd_users_get( $_GET['usr'] );
				if ($r_inscript && ria_mysql_num_rows($r_inscript)) {
					while ($inscript = ria_mysql_fetch_assoc($r_inscript)) {
						?>
							<tr>
								<td align="center" headers="rwu-inscript"><?php 
									print isdateheure($inscript['date_inscript'])   ? date( 'd/m/Y à H:i', strtotime($inscript['date_inscript']) )   : '-'; 
								?></td>
								<td align="center" headers="rwu-uninscript"><?php 
									print isdateheure($inscript['date_uninscript']) ? date( 'd/m/Y à H:i', strtotime($inscript['date_uninscript']) ) : '-'; 
								?></td>
							</tr>
						<?php
					}
				}else{
					?><tr><td colspan="2"><?php print _('Aucune inscription enregistrée')?></td></tr><?php
				}
			?>
		</tbody>
	</table>
	<p>&nbsp;</p>
	<?php 
		}

		$r_sp_link 	 = gu_sponsor_links_get( $_GET['usr'] );
		$r_sp_promo  = gu_sponsor_promotions_get( $_GET['usr'] );
		$r_sp_points = gu_sponsor_points_get( $_GET['usr'] );

	?>
	
	<h2><?php print _('Parrainages')?></h2>
	<p><?php printf(_('Vous pouvez créer un nouveau lien de parrainage via le formulaire ci-dessous. Le type de parrainage est déterminé par la %sconfiguration du programme de fidélité%s.'), '<a href="/admin/tools/rewards/config/index.php">', '</a>')?></p>
	<label for="godson">
		<?php print _('Ajouter :'); ?>
		<input type="text" name="godson" id="godson" value="" />
		<input type="submit" name="save-godson" value="<?php print _('Enregistrer')?>" />
	</label>
	
	<p>&nbsp;</p>
	<p><?php print _('Vous trouverez ci-dessous tous les parrainages réalisés par ce compte client.')?></p>
	
	<?php
		if (
			!($r_sp_link && ria_mysql_num_rows($r_sp_link)) &&
			!($r_sp_promo && ria_mysql_num_rows($r_sp_promo)) &&
			!($r_sp_points && ria_mysql_num_rows($r_sp_points))
		) {
			?><div class="notice"><?php print _('Aucun parrainage pour le moment')?></div><?php
		}

		if ($r_sp_link && ria_mysql_num_rows($r_sp_link)) {
	?>

	<table class="checklist" cellspacing="0" cellpadding="0">
		<col width="400" /><col width="150" />
		<caption><?php print _('Parrainages via un lien')?></caption>
		<thead>
			<tr>
				<th id="sponsor_email"><?php print _('Email')?></th>
				<th id="sponsor_date"><?php print _('Date')?></th>
			</tr>
		</thead>
		<tbody><?php
			if( !$r_sp_link || !ria_mysql_num_rows($r_sp_link) ){
				print '<tr><td colspan="4">'._('Aucun parrainage via un lien pour le moment.').'</td></tr>';
			}else{
				while( $sponsor = ria_mysql_fetch_array($r_sp_link) ){
					print '
						<tr>
							<td headers="sponsor_email">'.htmlspecialchars( $sponsor['email'] ).'</td>
							<td headers="sponsor_date">'.htmlspecialchars( $sponsor['date'] ).'</td>
						</tr>
					';
				}
			}
		?></tbody>
	</table>

	<?php
		}

		if ($r_sp_promo && ria_mysql_num_rows($r_sp_promo)) {
	?>

	<table class="checklist" cellspacing="0" cellpadding="0">
		<col width="240" /><col width="150" /><col width="160" />
		<caption><?php print _('Parrainage avec remise d\'un code promotion')?></caption>
		<thead>
			<tr>
				<th id="sponsor_email"><?php print _('Email')?></th>
				<th id="sponsor_cod"><?php print _('Promotion')?></th>
				<th id="sponsor_used"><?php print _('Utilisé ?')?></th>
			</tr>
		</thead>
		<tbody><?php
			if( !$r_sp_promo || !ria_mysql_num_rows($r_sp_promo) ){
				print '<tr><td colspan="4">'._('Aucun parrainage avec remise d\'un code promotion pour le moment.').'</td></tr>';
			}else{
				while( $sponsor = ria_mysql_fetch_array($r_sp_promo) ){
					$txt_cod = '<a href="/admin/promotions/specials/edit.php?id='.$sponsor['cod'].'&amp;type='._PMT_TYPE_REWARD.'">'.htmlspecialchars( _('Code n°').''.$sponsor['cod'] ).'</a>';
					if( !$sponsor['valid'] && !$sponsor['used'] ){
						$txt_cod = '<a href="/admin/promotions/specials/edit.php?id='.$sponsor['cod'].'&amp;type='._PMT_TYPE_REWARD.'" class="barre">'.htmlspecialchars( _('Code n°').''.$sponsor['cod'] ).'</a>';
					}

					print '
						<tr>
							<td headers="sponsor_email">'.htmlspecialchars( $sponsor['email'] ).'</td>
							<td headers="sponsor_cod">'.htmlspecialchars( $txt_cod ).'</td>
							<td headers="sponsor_used">'.( $sponsor['used'] ? sprintf(_('Oui (%d points)'), $sponsor['points']) : _('Non') ).'</td>
						</tr>
					';
				}
			}
		?></tbody>
	</table>

	<?php
		}

		if ($r_sp_points && ria_mysql_num_rows($r_sp_points)) {
	?>

	<table class="checklist" cellspacing="0" cellpadding="0">
		<col width="390" /><col width="160" />
		<caption><?php print _('Parrainage avec gain de points de fidélité')?></caption>
		<thead>
			<tr>
				<th id="sponsor_email"><?php print _('Email')?></th>
				<th id="sponsor_used"><?php print _('Utilisé ?')?></th>
			</tr>
		</thead>
		<tbody><?php
			if( !$r_sp_points || !ria_mysql_num_rows($r_sp_points) ){
				print '<tr><td colspan="4">'._('Aucun parrainage avec gain de points de fidélité pour le moment.').'</td></tr>';
			}else{
				while( $sponsor = ria_mysql_fetch_array($r_sp_points) ){
					print '
						<tr>
							<td headers="sponsor_email">'.htmlspecialchars( $sponsor['email'] ).'</td>
							<td headers="sponsor_used">'.( $sponsor['used'] ? _('Oui') : _('Non') ).'</td>
						</tr>
					';
				}
			}
		?></tbody>
	</table>

	<?php 
		}
	?>
	
	<p>&nbsp;</p>

	<h2><?php print _('Statistiques')?></h2>
	<p><?php print _('Vous pouvez ajouter des points de fidélité pour ce client :'); ?> <input type="button" class="edit-rewards" data-id="0" data-usr="<?php print $_GET['usr']; ?>" value="<?php print _('Ajouter des points de fidélité')?>" /></p>
	
	
	<p>&nbsp;</p>
	<div id="view_rewards_global_stats">
		<?php print view_rewards_get_global_stats( $_GET['usr'] ); ?>
	</div>
	<div id ="view_rewards_stats">
		<?php print view_rewards_get_stats( $_GET['usr'] ); ?>
	</div>
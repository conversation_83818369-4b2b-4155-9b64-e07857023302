<?php
	if (!isset($usr)) {
		header('Location: /admin/customers/index.php');
	}

	if ($usr['latitude'] == '0.00000000' && $usr['longitude'] == '0.00000000') {
		$usr['latitude'] = $usr['longitude'] = '';
	}

	// Remplace le texte "NC" (insenble à la casse) par vide sur les informations du compte
	$adr['firstname'] = strtolower2($adr['firstname']) == 'nc' ? '' : $adr['firstname'];
	$adr['lastname'] = strtolower2($adr['lastname']) == 'nc' ? '' : $adr['lastname'];
	$adr['society'] = strtolower2($adr['society']) == 'nc' ? '' : $adr['society'];
	$adr['siret'] = strtolower2($adr['siret']) == 'nc' ? '' : $adr['siret'];
	$adr['address1'] = strtolower2($adr['address1']) == 'nc' ? '' : $adr['address1'];
	$adr['zipcode'] = strtolower2($adr['zipcode']) == 'nc' ? '' : $adr['zipcode'];
	$adr['city'] = strtolower2($adr['city']) == 'nc' ? '' : $adr['city'];
	$adr['phone'] = strtolower2($adr['phone']) == 'nc' ? '' : $adr['phone'];

if (!$usr['is_confirmed']) { ?>
	<div class="notice"><?php print _('La création de ce compte client est en attente d\'une confirmation, il ne pourra donc pas se connecter.')?> <input type="submit" name="usr-create-confirmed" value="<?php print _('Confirmer')?>" /></div>
<?php }
if( $usr['is_sync'] ){ ?>
<p class="notice"><?php print _('Les champs grisés sont synchronisés avec votre ERP : ils ne sont pas modifiables directement dans RiaShop'); ?></p>
<?php } ?>
<table id="table-fiche-client-general">
	<tbody>
		<tr>
			<td class="valign-center"><?php print ( $usr['is_sync'] ? '' : '<label for="ref">' )._('Code client :').( $usr['is_sync'] ? '' : '</label>' ); ?></td>
			<td>
				<?php
					if( $usr['is_sync'] ){
						print '<input type="hidden" name="ref" value="'. htmlspecialchars($usr['ref']) .'"/>';
						print '<span class="readonly">'.( trim($usr['ref'])!='' ? htmlspecialchars($usr['ref']) : 'Aucun code client' ).'</span>';
					}else{
				?>
					<input type="text" class="ref" name="ref" id="ref" value="<?php print htmlspecialchars($usr['ref']); ?>" maxlength="17"  autocomplete="off" />
				<?php
					}
				?>
			</td>
		</tr>
		<tr><td class="valign-center"><label for="type"><?php print _('Type d\'adresse :'); ?></label></td><td>
			<select name="type" id="type" onchange="switch_adr_type(this.form)" onkeyup="switch_adr_type(this.form)">
				<?php
					$types = gu_adr_types_get();
					while( $t = ria_mysql_fetch_array($types) ){
						print '<option value="'.$t['id'].'"'.( $t['id']==$adr['type_id'] ? ' selected="selected"':'' ).'>'.htmlspecialchars($t['name']).'</option>';
					}
				?>
			</select>
		</td></tr>
		<tr id="adr-civ"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
			<td><label><span class="mandatory">*</span> <?php print _('Civilité :'); ?></label></td><td>
			<?php
				$titles = gu_titles_get();
				while( $r = ria_mysql_fetch_array($titles) ){
					print '<input type="radio" class="radio" name="title" id="title-'.$r['id'].'" value="'.$r['id'].'" '.( $adr['title_id']==$r['id'] ? ' checked="checked"':'').'/> ';
					print '<label class="inline" for="title-'.$r['id'].'">'.htmlspecialchars($r['name']).'</label> ';
				}
			?>
			</td>
		</tr>
		<tr id="adr-firstname"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
			<td class="valign-center"><label for="firstname"><span class="mandatory">*</span> <?php print _('Prénom :'); ?></label></td>
			<td><input type="text" name="firstname" id="firstname" value="<?php print htmlspecialchars($adr['firstname']); ?>" maxlength="75" /></td>
		</tr>
		<tr id="adr-lastname"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
			<td class="valign-center"><label for="lastname"><span class="mandatory">*</span> <?php print _('Nom de famille :'); ?></label></td>
			<td><input type="text" name="lastname" id="lastname" value="<?php print htmlspecialchars($adr['lastname']); ?>" maxlength="75" /></td>
		</tr>
		<tr id="adr-society"<?php if( $adr['type_id']==1 ) print ' class="none"'; ?> >
			<td class="valign-center"><label for="society"><span class="mandatory">*</span> <?php print _('Société :'); ?></label></td>
			<td><input type="text" name="society" id="society" value="<?php print htmlspecialchars($adr['society']); ?>" maxlength="75" autocomplete="off" /></td>
		</tr>
		<tr id="adr-siret"<?php if( $adr['type_id']==1 ) print ' class="none"'; ?> >
			<td class="valign-center"><label for="siret"><?php print _('SIRET :'); ?></label></td>
			<td><input type="text" name="siret" id="siret" value="<?php print formatSiret($adr['siret']); ?>" maxlength="17" size="17" />
			</td>
		</tr>
		<tr id="usr-naf"<?php if( $adr['type_id']==1 ) print ' class="none"'; ?> >
			<td class="valign-center"><label for="naf"><?php print _('Code NAF :'); ?></label></td>
			<td><input type="text" name="naf" id="naf" value="<?php print htmlspecialchars($usr['naf']); ?>" maxlength="7" size="7" />
			</td>
		</tr>
		<tr>
			<td class="valign-center"><label for="address1"><?php print _('No et rue :'); ?></label></td>
			<td>
				<?php
					// L'adresse postale et l'adresse mail sont en lecture seule dans le cas où la tâche de synchronisation de mise à jour des adresses (id 52) n’est pas activée.
					$task_is_active = false;
					$r_task = tsk_tasks_activation_get(TSK_ADR_UPD, $config['tnt_id']);
					if( $r_task && ria_mysql_num_rows($r_task) ){
						$task = ria_mysql_fetch_assoc($r_task);
						$task_is_active = $task['is_active'] ? true : false;
					}

					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="address1" value="'.htmlspecialchars($adr['address1']).'" />';
						print '<span class="readonly">'.( trim($adr['address1'])!='' ? htmlspecialchars($adr['address1']) : _('Aucun N° et rue enregistrés') ).'</span>';
					}else{
						print '<input type="text" name="address1" id="address1" maxlength="75" value="'.htmlspecialchars($adr['address1']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<?php if( $task_is_active || !$usr['is_sync'] || (trim($adr['address2'])!='' && trim($adr['address3'])!='') ){ ?>
		<tr>
			<td class="valign-center"><label for="address2"><?php print _('Complément :'); ?></label></td>
			<td>
				<?php
					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="address2" value="'.htmlspecialchars($adr['address2']).'"/>';
						print '<span class="readonly">'.htmlspecialchars($adr['address2']).'</span>';
					}else{
						print '<input type="text" name="address2" id="address2" maxlength="75" value="'.htmlspecialchars($adr['address2']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<tr>
			<td><label for="address3"></label></td>
			<td>
				<?php
					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="address3"  value="'.htmlspecialchars($adr['address3']).'" />';
						print '<span class="readonly">'.htmlspecialchars($adr['address3']).'</span>';
					}else{
						print '<input type="text" name="address3" id="address3" maxlength="75" value="'.htmlspecialchars($adr['address3']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<?php } ?>
		<tr>
			<td class="valign-center"><label for="zipcode"><?php print _('Code postal :'); ?></label></td>
			<td>
				<?php
					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="zipcode" value="'.htmlspecialchars($adr['zipcode']).'" />';
						print '<span class="readonly">'.( trim($adr['zipcode'])!='' ? htmlspecialchars($adr['zipcode']) : _('Aucun code postal enregistré')).'</span>';
					}else{
						print '<input type="text" name="zipcode" id="zipcode" class="zipcode" maxlength="9" value="'.htmlspecialchars($adr['zipcode']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<tr>
			<td class="valign-center"><label for="city"><?php print _('Ville :'); ?></label></td>
			<td>
				<?php
					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="city" value="'.htmlspecialchars($adr['city']).'"/>';
						print '<span class="readonly">'.( trim($adr['city'])!='' ? htmlspecialchars($adr['city']) : _('Aucune ville enregistrée')).'</span>';
					}else{
						print '<input type="text" name="city" id="city" maxlength="75" value="'.htmlspecialchars($adr['city']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<tr>
			<td class="valign-center"><label for="country"><span class="mandatory">*</span> <?php print _('Pays :'); ?></label></td>
			<td>
				<?php
					$countries = sys_countries_get();
					$countrie = false;
					if( !$task_is_active && $usr['is_sync'] ){
						while( $c = ria_mysql_fetch_array($countries) ){
							$c_name = str_replace('-', ' ', $c['name']);
							$adr_country = str_replace('-', ' ', $adr['country']);
							if (strtoupper2($c_name) == strtoupper2($adr_country)) {
								$countrie = true;
							}
						}

						print '<input type="hidden" name="country" value="'.htmlspecialchars($adr_country).'"/>';
						print '<span class="readonly">'.( $countrie && trim($adr_country)!='' ? htmlspecialchars($adr_country) : _('Aucun pays enregistré')).'</span>';
					}else{
						?>
						<select name="country" id="country">
							<option value="">&nbsp;</option>
							<?php
								while( $c = ria_mysql_fetch_array($countries) ){
									$c_name = str_replace('-', ' ', $c['name']);
									$adr_country = str_replace('-', ' ', $adr['country']);

									$selected = '';
									if (strtoupper2($c_name) == strtoupper2($adr_country)) {
										$selected = ' selected="selected"';
									}
									print '<option value="'.htmlspecialchars($c['name']).'"'.$selected.'>'.htmlspecialchars($c['name']).'</option>';
								}
							?>
						</select>
						<?php
					}
				?>

			</td>
		</tr>
		<tr>
			<td colspan="2" class="align-center"><div id="usr-map"></div></td>
		</tr>
		<tr id="show-gps">
			<td colspan="2"><a href="#" class="show-gps button"><?php print _('Afficher les coordonnées'); ?></a></td>
		</tr>
		<tr class="latLng none">
			<td class="valign-center"><label for="city"><?php print _('Latitude :'); ?></label></td>
			<td><input type="text" name="latitude" id="latitude" maxlength="75" value="<?php print htmlspecialchars($usr['latitude']); ?>" /></td>
		</tr>
		<tr class="latLng none">
			<td class="valign-center"><label for="city"><?php print _('Longitude :'); ?></label></td>
			<td><input type="text" name="longitude" id="longitude" maxlength="75" value="<?php print htmlspecialchars($usr['longitude']); ?>" /></td>
		</tr>
		<tr><th colspan="2"><?php print _('Contact')?></th></tr>
		<tr>
			<td class="valign-center"><label for="email"><?php print _('Email :'); ?></label></td>
			<td>
				<?php
					if( !$task_is_active && $usr['is_sync'] ){
						print '<input type="hidden" name="email" value="'.htmlspecialchars($usr['email']).'" />';
						print '<span class="readonly">'.( trim($usr['email'])!='' ? htmlspecialchars($usr['email']) : _('Aucun code postal enregistré')).'</span>';
					}else{
						print '<input type="email" id="email" name="email" value="'.htmlspecialchars($usr['email']).'" autocomplete="off" />';
					}
				?>
			</td>
		</tr>
		<tr><td class="valign-center"><label for="tel"><?php print _('Téléphone :'); ?></label></td><td><input type="text" id="tel" name="tel" value="<?php print htmlspecialchars($adr['phone']); ?>" autocomplete="off" /></td></tr>
		<tr><td class="valign-center"><label for="fax"><?php print _('Fax :'); ?></label></td><td><input type="text" id="fax" name="fax" value="<?php print htmlspecialchars($adr['fax']); ?>" autocomplete="off" /></td></tr>
		<tr><td class="valign-center"><label for="mobile"><?php print _('Portable :'); ?></label></td><td><input type="text" id="mobile" name="mobile" value="<?php print htmlspecialchars($adr['mobile']); ?>" autocomplete="off" /></td></tr>
		<tr><td class="valign-center"><label for="phone-work"><?php print _('Téléphone en journée :'); ?></label></td><td><input type="text" id="phone-work" name="phone-work" value="<?php ( $adr['phone_work']!='' ? print htmlspecialchars($adr['phone_work']) : '' ); ?>" autocomplete="off" /></td></tr>
		<tr><td class="valign-center"><label for="website"><?php print _('Site web :'); ?></label></td><td><input type="text" id="website" name="website" value="<?php ( $usr['website']!='' ? print htmlspecialchars($usr['website']) : '' ); ?>" autocomplete="off" /></td></tr>
		<tr><th colspan="2"><?php print _('Autorisations')?></th></tr>
		<?php if( !tnt_tenants_is_yuto_essentiel() ){ ?>
		<tr><td><?php print _('Désactiver compte :'); ?></td>
			<td class="valign-center">
				<input type="radio" id="desactive_compte" name="can_login" value="0" <?php print (!$usr['can_login'])?"checked":""; ?>>
  				<label for="desactive_compte"><?php print _('Oui'); ?></label>
				<input type="radio" id="active_compte" name="can_login" value="1" <?php print ($usr['can_login'])?"checked":""; ?>>
  				<label for="active_compte"><?php print _('Non'); ?></label>
			</td>
		</tr>
		<?php } ?>
		<tr><td class="valign-center"><label for="usr-prf-id"><span class="mandatory">*</span> <?php print _('Droits d\'accès :'); ?></label></td><td>
			<select name="usr-prf-id" id="usr-prf-id">
			<?php
				$profiles = gu_profiles_get();
				while( $r = ria_mysql_fetch_array($profiles) ){
					print '<option value="'.$r['id'].'"'.( $usr['prf_id']==$r['id'] ? ' selected="selected"':'' ).'>'.htmlspecialchars($r['name']).'</option>';
				}
			?>
			</select>
		</td></tr>
		<?php
		if( !tnt_tenants_is_yuto_essentiel() ){
			// S'il s'agit d'un représentant, fournit un lien rapide vers la liste de ses clients
			if( $usr['prf_id'] == PRF_SELLER ){?>
					<tr>
						<td><?php print _('Voir tout le fichier client  :'); ?></td>
						<td class="valign-center">
							<input type="radio" id="unrestrict_portfolio" name="restrict_portfolio" value="0" <?php print (!$usr['restrict_portfolio'])?"checked":""; ?>>
							<label for="unrestrict_portfolio"><?php print _('Oui'); ?></label>
							<input type="radio" id="restrict_portfolio" name="restrict_portfolio" value="1" <?php print ($usr['restrict_portfolio'])?"checked":""; ?>>
							<label for="restrict_portfolio"><?php print _('Non'); ?></label>
						</td>
					</tr>
					<tr>
						<td>&nbsp;</td>
						<td><a href="index.php?seller=<?php echo $usr['id'] ?>"><?php print _('Liste des comptes clients associés à ce représentant')?></a></td>
					</tr>
			<?php }elseif( $usr['prf_id']!=PRF_ADMIN ){ // Si le compte n'est associé à aucun représentant, propose d'effectuer cette association
				// tente de charger le représentant
				$seller = null;
				if( $usr['seller_id'] ){
					$rseller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $usr['seller_id'] );
					if( $rseller && ria_mysql_num_rows($rseller) ){
						$seller = ria_mysql_fetch_array($rseller);
					}
				}

				if( $seller!==null ){
					$sync = $usr['is_sync'] && $seller['is_sync'];
					print '
						<tr>
							<td class="valign-center">'._('Représentant').' :</td>
							<td id="seller-change-block">'.( $sync ? '<img class="sync" src="/admin/images/sync/1.svg" title="'._('Le lien avec ce représentant est synchronisé depuis votre gestion commerciale.').'" alt="'._('Le lien avec ce représentant est synchronisé depuis votre gestion commerciale.').'" /> ' : '' ).'<a href="edit.php?usr='.$seller['id'].'&prf='.PRF_SELLER.'">'.gu_users_get_name( $seller['id'] ).'</a>'.( !$sync ? ' (<a onclick="return showSelectSeller(this, '.$usr['id'].')">'._('Modifier').'</a>|<a onclick="return detachSeller(this, '.$usr['id'].');">'._('Détacher').'</a>)' : '' ).'</td>
						</tr>
					';
				}else{
					print '
						<tr>
							<td>&nbsp;</td>
							<td id="seller-change-block"><a class="edit" onclick="return showSelectSeller(this, '.$usr['id'].')">'._('Assigner un représentant').'</a></td>
						</tr>
					';
				}
			}?>
			<tr>
				<td class="valign-center"><?php print ( $usr['is_sync'] ? '' : '<label for="usr-prc-id">' )._('Catégorie tarifaire :').( $usr['is_sync'] ? '' : '</label>' ) ?></td>
				<td>
					<?php
						$usr_prc_id = isset($_POST['usr-prc-id']) ? $_POST['usr-prc-id'] : gu_users_get_prc( $usr['id'], false, false );
						$categories = prd_prices_categories_get(0, false, false, false, false);

						// si client synchro, on n'affiche pas le select
						if( $usr['is_sync'] ){
							print '<span class="readonly">';
							if( ria_mysql_num_rows($categories) ){
								while( $r = ria_mysql_fetch_array($categories) ){
									if( $usr_prc_id==$r['id'] ){
										print htmlspecialchars($r['name']);
									}
								}
							}else{
								print _('Aucune catégorie tarifaire');
							}
							print '</span>';
						}else{
					?>
					<select name="usr-prc-id" id="usr-prc-id">
						<option value=""><?php print _('Veuillez sélectionner une catégorie tarifaire')?></option>
						<?php
							while( $r = ria_mysql_fetch_array($categories) ){
								print '<option value="'.$r['id'].'"'.( $usr_prc_id==$r['id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($r['name']).'</option>';
							}
						?>
					</select>
					<?php
						}
					?>
				</td>
			</tr>
			<?php
				$categoriesConta = gu_accounting_categories_get();
				if( $categoriesConta && ria_mysql_num_rows($categoriesConta) ){
			?>
				<tr>
					<td class="valign-center">
						<?php print ( $usr['is_sync'] ? '' : '<label for="usr-cac-id">' )._('Catégorie comptable :').( $usr['is_sync'] ? '' : '</label>' ) ?>
					</td>
					<td>
						<?php
						// si client synchro, on n'affiche pas le select
						if( $usr['is_sync'] ){
							print '<span class="readonly">';
							while( $r = ria_mysql_fetch_array($categoriesConta) ){
								if( $usr['cac_id']===$r['id'] ){
									print htmlspecialchars($r['name']);
								}
							}
							print '</span>';
						}else{
						?>
						<select name="usr-cac-id" id="usr-cac-id" <?php print $usr['is_sync'] ? 'disabled="disabled"' : ''; ?>>
							<option value=""><?php print _('Veuillez sélectionner une catégorie comptable')?></option>
							<?php
								while( $r = ria_mysql_fetch_assoc($categoriesConta) ){
									print '<option value="'.$r['id'].'"'.( $usr['cac_id']===$r['id'] ? ' selected="selected"' : '').'>'.htmlspecialchars($r['name']).'</option>';
								}
							?>
						</select>
						<?php
						}
						?>
					</td>
				</tr>
			<?php
				}
			?>
			<tr>
				<td class="valign-center"><?php print ( $usr['is_sync'] ? '' : '<label for="usr-taxcode">' )._('Numéro de TVA intracommunautaire :').( $usr['is_sync'] ? '' : '</label>' ); ?></td>
				<td>
					<?php
						if( $usr['is_sync'] ){
							print '<span class="readonly">'.( trim($usr['taxcode'])!='' ? htmlspecialchars($usr['taxcode']) : 'Aucune TVA intracommunautaire' ).'</span>';
						}else{
					?>
						<input type="text" name="usr-taxcode" id="usr-taxcode" value="<?php print htmlspecialchars($usr['taxcode']); ?>" maxlength="17" />
					<?php
						}
					?>
				</td>
			</tr>
			<?php if( $usr['id']>0 ){ ?>
			<tr><td class="valign-center"><?php print _('Tarifs nets :'); ?></td><td><a href="edit.php?downloadprice=1&usr=<?php print $usr['id']; ?>"><?php print _('Télécharger les tarifs')?></a></td></tr>
			<?php } ?>
			<tr id="row-payment-types"><td class="valign-center"><?php print _('Moyens de paiements :'); ?></td><td>
			<?php
				// Si l'utilisateur à un modèle de paiement attaché à son compte, affiche ce modèle
				if( $usr['opm_id'] ){
					if( $rdetails = ord_payment_model_details_get( $usr['opm_id'] ) ){
						while( $detail = ria_mysql_fetch_assoc($rdetails) ){
							$detail['id'] = $detail['pay_id'];
							$detail['name'] = $detail['pay_name'];
							print gu_users_payment_types_view( $detail ).'<br />';
						}
						print '<br/>';
					}
				}

				// on charge les moyen de paiement de l'utilisateur si il est synchronisé on les affiches
				$pay = gu_users_payment_types_get( $usr['id'] );
				$user_pay_ids = array();
				if( !$pay || !ria_mysql_num_rows($pay) ){
					print '<span class="error">'._('Aucun moyen de paiement autorisé').'</span>';
				}else{
					while( $p = ria_mysql_fetch_assoc($pay) ){
						$user_pay_ids[] = $p['id'];
						if ($usr['is_sync']) {
							print gu_users_payment_types_view( $p ).'<br />';
						}
					}
				}

				// si l'utilisateur n'est pas synchronisé on affiche toutes les options de paiement et on coche celle de l'utilisateur
				if (!$usr['is_sync']) { ?>
					<div>
					<?php
						$pay = ord_payment_types_get();
						if( !ria_mysql_num_rows($pay) ){?>
							<span class="error"><?php echo _('Aucun moyen de paiement de définie'); ?></span>
						<?php }else{ ?>
							<?php while( $res=ria_mysql_fetch_array($pay) ){ ?>
								<input
									type="checkbox"
									name="paiements[]"
									id="opt-<?php echo $res['id']?>"
									value="<?php echo $res['id']?>"
									<?php echo in_array($res['id'], $user_pay_ids)? 'checked="checked"': ''?>
								/><label for="opt-<?php echo $res['id'];?>" class="inline">
									<?php echo htmlspecialchars( $res['name'] );?>
								</label><br/>
						<?php }
					} ?>
					</div>
				<?php } ?>
			</td></tr>
			<?php $prc = ria_mysql_fetch_assoc(prd_prices_categories_get($usr['prc_id'])); ?>
			<tr><td><?php print _('Encours :'); ?></td><td><?php print ria_number_format($usr['encours'], NumberFormatter::CURRENCY, 2, (!empty($prc['money_code']) ? $prc['money_code'] : '')) ?></td></tr>
			<tr>
				<td><?php print ( $usr['is_sync'] ? '' : '<label for="dps">' )._('Dépôt principal :').( $usr['is_sync'] ? '' : '</label>' ) ?></td>
				<td>
					<?php
						$deposits = prd_deposits_get();
						if( ria_mysql_num_rows($deposits) ){
							print _('Aucun dépôt principal');
						}else{
							while( $dps = ria_mysql_fetch_array($deposits) ){
								print htmlspecialchars($dps['name']);
							}
						}
					?>
				</td>
			</tr>
			<?php
				$r_code_risk = gu_risk_code_get();
				if( $r_code_risk && ria_mysql_num_rows($r_code_risk) ){
			?>
			<tr>
				<td class="valign-center">
					<?php print ( $usr['is_sync'] ? '' : '<label for="code_risk">' )._('Code risque :').( $usr['is_sync'] ? '' : '</label>' ) ?>
				</td>
				<td>
					<?php if( $usr['is_sync'] ){
						print '<span class="readonly">';
						while( $code_risk = ria_mysql_fetch_array($r_code_risk) ){
							if( $code_risk['id']==$usr['rco_id'] ){
								print htmlspecialchars($code_risk['name']);
							}
						}
						print'</span>';
					}else{ ?>
					<select name="code_risk" id="code_risk">
						<option value=""></option>
					<?php
						$usr['rco_id'] = isset($_POST['code_risk']) ? $_POST['code_risk'] : $usr['rco_id'];
						while( $code_risk = ria_mysql_fetch_array($r_code_risk) ){
							print '<option value="'.$code_risk['id'].'"'.( $code_risk['id']==$usr['rco_id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($code_risk['name']).'</option>';
						}
					?>
					</select>
					<?php
					}
					?>
				</td>
			</tr>
			<?php } ?>
		<?php }
		if( $usr['prf_id']==PRF_SELLER || $usr['prf_id']==PRF_ADMIN || tnt_tenants_have_websites() ){ ?>
		<tr><th colspan="2"><?php print _('Changement du mot de passe')?></th></tr>
		<tr><td class="valign-center"><label for="password1"><?php print _('Nouveau mot de passe :'); ?></label></td><td><input type="password" class="text" name="password1" id="password1" maxlength="32" autocomplete="new-password" /></td></tr>
		<tr><td class="valign-center"><label for="password2"><?php print _('Confirmation :'); ?></label></td><td><input type="password" class="text" name="password2" id="password2" maxlength="32" autocomplete="new-password" /></td></tr>
		<?php } ?>

		<tr><th colspan="2"><?php print _('Divers')?></th></tr>
		<tr><td><label for="notes"><?php print _('Commentaires :'); ?></label></td><td><textarea id="notes" name="notes" rows="10" cols="40"><?php
				print htmlspecialchars( $usr['notes'] );
		?></textarea></td></tr>
		<?php if( $usr['type_id']==1 ){ ?>
		<tr><td class="valign-center"><label for="dob"><?php print _('Date de naissance :'); ?></label></td><td><input type="text" id="dob" name="dob" value="<?php print dateheureunparse($usr['dob'], false); ?>"></td></tr>
		<?php
		}

		if( wst_websites_is_fdv_app( $config['tnt_id'] ) ){ ?>
		<tr>
			<td class="valign-center"><label for="mask"><?php print _('Affichage :'); ?></label></td>
			<td>
				<input type="checkbox" class="checkbox" name="mask" id="mask" value="1" <?php print $usr['is_masked'] ? 'checked="checked"':'' ?> />
				<label for="mask"><?php print _('Masquer ce compte dans Yuto'); ?></label>
			</td>
		</tr>
		<?php }

		if(tnt_tenants_have_websites() && is_numeric($usr['wst_id']) && $usr['wst_id'] > 0) {
			$wst_name = wst_websites_get_name($usr['wst_id']);
			?>
			<tr>
				<td class="valign-center"><label for="wst"><?php print _('Créé depuis :'); ?></label></td>
				<td><?php print htmlspecialchars($wst_name) ?></td>
			</tr>
		<?php } ?>

		<tr><td class="valign-center"><?php print _('Date de création :'); ?></td><td><?php print ria_date_format($usr['date_created']); ?></td></tr>
		<?php if( tnt_tenants_have_websites() ){

				$fisrt_login = gu_users_logins_get_first( $usr['id'] );
				if( isdateheure($fisrt_login) ){ ?>
				<tr><td class="valign-center"><?php print _('Première connexion :'); ?></td><td><?php
					print date( 'd/m/Y à H:i', strtotime($fisrt_login) );
				?></td></tr>
			<?php }
			if( $usr['last_login'] ){ ?>
			<tr><td class="valign-center"><?php print _('Dernière visite :'); ?></td><td><?php print $usr['last_login'] ? $usr['last_login'] : _('Non disponible'); ?></td></tr>
			<?php }

				if( isset($usr['last_login_child']) ){
					if( !is_null($usr['last_login_child']) ){
						$date = new DateTime($usr['last_login_child']);
						?>
							<tr><td class="valign-center"><?php print _('Dernière visite d\'un compte sous sa responsabilité :'); ?> </td><td><?php print $date->format( _('Le d/m/Y à H:i:s') ); ?></td></tr>
						<?php
					}
				}
				$stats = stats_origins_get( $usr['id'], CLS_USER );
				if( $stats && ria_mysql_num_rows($stats) ){
					$stat = ria_mysql_fetch_array( $stats );
					print '<tr><th colspan="2">'._('Origine du client').'</th></tr>';
					print view_source_origin($stat, 'table');
				}

		} ?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="2">
				<div class="float-left">
					<?php if( ($usr['prf_id']==PRF_SELLER || $usr['prf_id']==PRF_ADMIN || tnt_tenants_have_websites()) && cfg_emails_code_exists('pwd-lost', $config['wst_id']) ){ ?>
					<input type="submit" name="lost-pwd" value="<?php print _('Mot de passe oublié')?>" title="<?php print htmlspecialchars(_('Envoyer l\'email "Mot de passe oublié"'))?>" />
					<?php
						}

						if( !in_array($usr['prf_id'], array(PRF_ADMIN, PRF_SELLER)) ){
					?>
					<input type="button" class="user-add-asset" data-usr="<?php print $_GET['usr']; ?>" value="<?php print _('Créer un avoir'); ?>" title="<?php print _('Créer un avoir pour ce client');?>" />
					<?php } ?>
					<input type="button" id="export" name="export" value="<?php print _('Exporter')?>" title="<?php print htmlspecialchars(_('Exporter les données de l\'utilisateur'))?>" onclick="exportCustomersAccount()" />
				</div>
				<div class="float-right">
					<input type="hidden" id="usr_id" name="usr_id" value="<?php print htmlspecialchars($usr['id']) ?>" />
					<input type="submit" name="save-main" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications')?>" />
					<input type="submit" name="cancel-main" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications')?>" />
					<?php if( !$usr['is_sync'] && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_DEL') ){ ?>
					<input type="submit" name="del" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer ce compte')?>" />
					<?php } ?>
				</div>
			</td>
		</tr>
	</tfoot>
</table>

<script>

	$('#latitude, #longitude').change(function(){
		if( googleMap ){
			marqueur.setPosition(new google.maps.LatLng( $('#latitude').val(), $('#longitude').val() ));
			googleMap.panTo(marqueur.getPosition());
		}
	})

	$('a.show-gps').click(function(e){
		e.preventDefault();
		$('tr#show-gps').addClass('none');
		$('tr.latLng').removeClass('none');
	})

	var googleMap;
	var marqueur
	// Initialise la carte
	function initMap(){

		var latitude = null;
		var longitude = null;

		<?php if( $usr['latitude'] && $usr['longitude'] && $usr['zipcode'] && $usr['city'] && $usr['address1']){ ?>
			latitude = <?php print $usr['latitude']; ?>;
			longitude = <?php print $usr['longitude']; ?>;<?php
		} ?>

		if( longitude!=null && latitude!=null ){
			googleMap = new google.maps.Map(document.getElementById('usr-map'));
			googleMap.setZoom(12);

			marqueur = new google.maps.Marker({
					map: googleMap,
					position: new google.maps.LatLng( latitude, longitude ),
					icon: '/admin/images/customers/marker.png',
				});

			//Evenement lors du clic sur la google map
			google.maps.event.addDomListener(googleMap, 'click', function(e) {
				marqueur.setPosition(e.latLng);

				$('#latitude').val(e.latLng.lat());
				$('#longitude').val(e.latLng.lng());
			});

			googleMap.panTo(marqueur.getPosition());
		}else{
			$('tr#show-gps').addClass('none');
			$('#usr-map').addClass('none');
		}


	}
</script>

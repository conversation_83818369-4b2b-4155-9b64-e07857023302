<?php if (!isset($usr)) {
	header('Location: /admin/customers/index.php');
}?>
<input type="hidden" name="usr_id" id="usr_id" value="<?php print $_GET['usr']; ?>" />
<p class="notice"><?php print _('Vous trouverez ci-dessous toutes les listes personnalisées créées par ce client.')?></p>
<table id="list-whishlist" class="checklist">
	<caption><?php print _('Listes personnalisées')?></caption>
	<thead>
		<tr>
			<th id="wishlist-del" data-label="<?php print _('Tout cocher : '); ?> "><input type="checkbox" name="checkall" value="" /></th>
			<th id="wishlist-name" class="thead-none"><?php print _('Désignation')?></th>
			<th id="wishlist-publish" class="thead-none"><?php print _('Publiée')?> ?</th>
			<th id="wishlist-prds" class="thead-none"><?php print _('Produits')?></th>
		</tr>
	</thead>
	<tbody><?php
		$rgwt = gu_wishlists_get( false, $_GET['usr'], null, false );
		if( !$rgwt || !ria_mysql_num_rows($rgwt) ){
			print '
				<tr>
					<td colspan="4">'._('Aucune liste personnalisée n\'existe pour le moment.').'</td>
				</tr>
			';
		}else{
			while( $gwt = ria_mysql_fetch_array($rgwt) ){
				print '
					<tr>
						<td class="td-check-del" headers="wishlist-del">
							<input type="checkbox" name="wishlist[]" value="'.$gwt['id'].'" />
						</td>
						<td headers="wishlist-name" data-label="'._('Désignation : ').' ">
							<a href="#" onclick="return editWishList('.$gwt['id'].');">'.htmlspecialchars( $gwt['name'] ).'</a>
						</td>
						<td headers="wishlist-publish" data-label="'._('Publiée : ').' ">
							'.( $gwt['publish'] ? _('Oui') : _('Non') ).'
						</td>
						<td headers="wishlist-prds" data-label="'._('Produits : ').' ">
							<a onclick="showWishlist('.$gwt['id'].', '.$_GET['usr'].')" href="#" title="'._('Voir le contenu de la liste personnalisée').'">
								'.number_format( $gwt['products'], 0, ',', ' ' ).' produit'.( $gwt['products']>1 ? 's' : '' ).'
							</a>
						</td>
					</tr>
				';
			}
		}
	?></tbody>
	<tfoot>
		<tr>
			<td colspan="4">
				<input class="checkbox" type="submit" name="del-wishlist" value="<?php print _('Supprimer')?>" />
				<input type="button" name="add-wishlist" id="add-wishlist" value="<?php print _('Ajouter')?>" />
			</td>
		</tr>
	</tfoot>
</table>
<?php if (!isset($alerts)) {
	header('Location: \admin');
}?>

<form method="post" action="index.php">
	<table id="tb-alert-dispo" class="tablesorter checklist">
		<caption><?php echo _("Alertes de"); ?> <?php echo $type ?> (<?php print ria_mysql_num_rows($alerts) ?> <?php echo _("alertes"); ?>)</caption>
		<col width="25" />
		<thead>
			<tr>
				<th id="sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="prd-ref" class="sorter-text"><?php echo _("Référence"); ?></th>
				<th id="prd-name"><?php echo _("Désignation"); ?></th>
				<th id="brd-name"><?php echo _("Marque"); ?></th>
				<th id="email"><?php echo _("Adresse email"); ?></th>
				<th id="account"><?php echo _("Compte client"); ?></th>
				<th id="date"><?php echo _("En attente depuis le"); ?></th>
				<th id="restocking"><abbr title="<?php echo str_replace("#param[action]#", $action, _("Si l'envoi de l'alerte est fait à chaque #param[action]# du produit")); ?>"><?php echo str_replace("#param[action]#", $action, _("À chaque #param[action]#")); ?></abbr></th>
				<th id="last_date"><?php echo _("Dernier envoi"); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td colspan="9" style="text-align: left;">
					<input type="submit" name="delete" value="<?php echo _("Supprimer"); ?>" />
					<input type="submit" name="export" value="<?php echo _("Exporter"); ?>" />
				</td>
			</tr>
		</tfoot>
		<tbody>
			<?php if( !ria_mysql_num_rows($alerts) ){ ?>
					<tr><td colspan="9"><?php echo _("Aucune adresse"); ?></td></tr>
			<?php }else{ ?>
				<?php while( ($alert = ria_mysql_fetch_array($alerts)) ) {
						$alert = $alertFormatage($alert);
						$rp = prd_products_get_simple( $alert['id'] );
						$sync = $url = ''; $idCat = 0;
						if( $rp && ria_mysql_num_rows($rp) ){
							$p = ria_mysql_fetch_assoc( $rp );
							$sync = view_prd_is_sync( $p );
							$sync = trim($sync)!='' ? $sync.' ' : '';
							$alert['ref'] = $p['ref'];
							$rcat = prd_products_categories_get($alert['id'], true);
							if( $rcat && ($cat=ria_mysql_fetch_array( $rcat )) ){
								$idCat = $cat['cat'];
								$alert['ref'] = '<a href="/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$alert['id'].'" target="_bank">'.htmlspecialchars($alert['ref']).'</a>';
							}
						}
						?>
						<tr>
						<td headers="sel"><input type="checkbox" class="checkbox" name="sub[]" value="<?php echo $alert['email']?>|<?php echo $alert['id']?>" /></td>
						<td headers="prd-ref"><?php echo $sync.$alert['ref']?></td>
						<td headers="prd-name"><?php echo $p['name']?></td>
						<td headers="brd-name"><?php echo ( isset($p['brd_title']) ? $p['brd_title'] : '' )?></td>
						<td headers="email"><a href="mailto:<?php echo $alert['email']?>"><?php echo htmlspecialchars($alert['email'])?></a></td>
						<td headers="account"><?php echo ( $alert['usr_id'] ? '<a href="../../customers/edit.php?usr='. $alert['usr_id'].'">Oui</a>' : 'Non' )?></td>
						<td headers="date"><?php echo $alert['date_created']?></td>
						<td headers="restocking"><?php echo ( !$alert['once'] ? 'Oui' : 'Non' )?></td>
						<td headers="last_date"><?php echo $alert['last_notified']?></td>
						</tr>
				<?php } ?>
			<?php } ?>
		</tbody>
	</table>
</form>
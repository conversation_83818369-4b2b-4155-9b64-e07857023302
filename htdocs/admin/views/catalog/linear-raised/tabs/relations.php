<?php

if( !isset($tab_view, $list) ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

function relationUserTable( $id, $name ){
	ob_start();
	?>
		<table id="<?php echo $id; ?>" class="pmt-rules table-responsables">
			<thead>
				<tr>
					<th id="th-responsables-1">
						<input class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox">
					</th>
					<th id="th-responsables-2"><?php echo _($name); ?></th>
				</tr>
			</thead>
			<tbody>
				<tr class="template none">
					<td>
						<input name="del[]" class="checkbox" type="checkbox" value="">
					</td>
					<td>
						<a class="url" href=""></a>
					</td>
				</tr>
				<tr class="no-info">
					<td colspan="2"><?php print _('Aucun compte sélectionné'); ?></td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input
							type="submit"
							value="<?php echo _('Supprimer'); ?>"
							name="del"
							class="button float-left">
						<input
							type="submit"
							value="<?php echo _('Ajouter'); ?>"
							name="add"
							class="button">
					</td>
				</tr>
			</tfoot>
		</table>
	<?php
	return ob_get_clean();
}
?>
<p class="notice"><?php print 'Cliquez sur "Enregistrer" pour que vos modifications soient prisent en compte.' ?></p>
<form method="post" id="relations_form" data-id="<?php echo $list['id']; ?>">
	<input type="hidden" name="id" id="id" value="<?php echo $list['id']; ?>">
	<table id="table-comptes-clients">
		<tbody>
			<tr>
				<td colspan="2" class="pmt-spe-rules">
					<?php echo relationUserTable('users', 'Comptes client'); ?>
				</td>
			</tr>
			<tr>
				<td colspan="2" class="pmt-spe-rules">
					<?php echo relationUserTable('managers', 'Responsables'); ?>
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" id="save" name="save" value="<?php echo _('Enregistrer'); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<script>
	var linearRaised = 'linear-raised',
		state = {
			modified: false,
			users : {},
			managers: {}
		};

	function updateUsers(id, email, name) {
		var obj = {};
			obj[id] = {
				id: id,
				email: email,
				name: name
			}
		state = {
			modified: true,
			users: $.extend({}, state.users, obj),
			managers: state.managers
		};

		render('#users', 'users');
	}

	function updateManagers(id, email, name) {
		state = {
			modified: true,
			users: state.users,
			managers: $.extend({}, state.managers, {
				id: {
					id: id,
					email: email,
					name: name
				}
			})
		};

		render('#managers', 'managers');
	}

	function deleteUsers(ids) {
		var users = state.users;
		$.each(ids, function(i, id){
			delete users[id];
		})
        state = {
			modified: true,
			managers: state.managers,
			users: users,
		}
		render('#users', 'users');
	}

	function deleteManagers(ids) {
		var managers = state.managers;
		$.each(ids, function(i, id){
			delete managers[id];
		})
        state = {
			modified: true,
			users: state.users,
			managers: managers,
		}
		render('#managers', 'managers');
	}

	// vérifier que l'id n'est pas déjà enregistrée ou selectionnée

	function render(el, stateKey) {

		var table = $(el);
		// remove toute les lignes du tableau
		$(table).find('.item').remove();
		var template = $(table).find('.template').clone();
		if (!Object.values(state[stateKey]).length) {
			$('.no-info', table).show();
			$('[name=del]', table).hide();
			return;
		}

		$('[name=del]', table).show();
		$('.no-info', table).hide();
		$.each(Object.values(state[stateKey]), function(i, user){
			var tr = template.clone();
			tr.removeClass('template')
				.removeClass('none')
				.addClass('item')
				.attr('data-id', user.id);

			$('.checkbox', tr).val(user.id);
			$('.url', tr).html(user.name);
			$('.url', tr).attr('href', '/admin/customers/edit.php?usr='+user.id);
			$('tbody', table).append(tr.show());
		})
	}


	$('.no-info td').html($('<div>').addClass('notice').text('Chargement...'));
	$.ajax({
		url: '/admin/ajax/catalog/linear-raised/index.php',
		data: {id: $('#id').val(), action: 'getUsers'},
		type: 'get',
	}).done(function(json){
		if (json.result) {
			var newState = {
				modified: false,
				users : {},
				managers: {},
			}
			if (typeof json.content.users === 'object') {
				newState.users = json.content.users;
			}
			if (typeof json.content.managers === 'object') {
				newState.managers = json.content.managers;
			}
			state = newState;
			render('#users', 'users');
			render('#managers', 'managers');
		}

		$('.no-info td').text('Aucun compte sélectionné');
	})

	$('#users').on('click', 'input[name="add"]', function () {
		displayPopup('<?php print _('Sélectionner un compte client'); ?>', '', '/admin/ajax/orders/ncmd-customers-change.php?prf[]=2&prf[]=3&get-all-name=true&callback=updateUsers');

		return false;
	});

	$('#users').on('click', 'input[name="del"]', function(e) {
		var ids = [];
		$('input[name="del[]"]:checked', $('#users')).each(function(i, el){
			ids.push($(this).val());
		})
		if (ids.length) {
			deleteUsers(ids);
		}
		return false;
	});

	$('#managers').on('click', 'input[name="del"]', function(e) {
		var ids = [];

		$('input[name="del[]"]:checked', $('#managers')).each(function(i, el) {
			ids.push($(this).val());
		})

		if (ids.length) {
			deleteManagers(ids);
		}

		return false;
	});

	$('#managers').on('click', 'input[name="add"]', function () {
		displayPopup('<?php print _('Sélectionner un reponsable'); ?>', '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&prf[]=1&prf[]=5&get-all-name=true&callback=updateManagers');

		return false;
	});

	$('#relations_form').on('submit', function(e) {
		var data = {
			id: $(this).data('id'),
			users:$.extend({}, state.users,state.managers),
		};
		$('div.success').remove();
		$.ajax({
			url: '/admin/ajax/catalog/linear-raised/index.php?action=batchUsers',
			type: 'post',
			data: data,
		}).done(function(json){
			if (json.result) {
				state.modified = false;
				$('.tabstrip').before(
					$('<div>').addClass('success').text(json.message)
				);
				setTimeout(function(){
					$('div.success').slideUp("slow", function() { $('div.success').remove();});
				}, 8000);
			}
		})

		return false;
	});

	window.onbeforeunload = function() {
		if (state.modified) {
			return '<?php print _("Vous n\'avez pas enregistré vos modifications. Êtes-vous sûr de vouloir quitter cette page ?"); ?>';
		}
	};
</script>
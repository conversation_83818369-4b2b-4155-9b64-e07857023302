<?xml version="1.0" encoding="UTF-8"?>
<svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.1 (89650) - https://sketch.com -->
    <title>logos / RS</title>
    <desc>Created with <PERSON>ket<PERSON>.</desc>
    <defs>
        <linearGradient x1="100%" y1="0%" x2="0%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5377FB" offset="0%"></stop>
            <stop stop-color="#4462D3" offset="99.2345861%"></stop>
        </linearGradient>
        <path d="M4,0 L56,0 C58.209139,-4.05812251e-16 60,1.790861 60,4 L60,56 C60,58.209139 58.209139,60 56,60 C39.9413663,60 23.8827325,60 7.82409876,60 C6.54939917,60 5.27469959,60 4,60 C1.790861,60 -1.73547709e-16,58.209139 0,56 L0,4 C-2.705415e-16,1.790861 1.790861,4.05812251e-16 4,0 Z" id="path-2"></path>
        <filter x="-26.7%" y="-23.3%" width="153.3%" height="153.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.08 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="logos-/-RS" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group-2-Copy-2">
            <g id="Group-3">
                <g id="shapes-/-blue_shadow">
                    <g id="Shape-Copy-5">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                    </g>
                </g>
                <path d="M18.468,39.6 L18.468,34.452 L22.4016,34.452 L25.9656,39.6 L30.5592,39.6 L26.4144,33.66 C28.8168,32.6304 30.216,30.5976 30.216,27.8256 C30.216,23.6808 27.1272,21.12 22.1904,21.12 L14.1912,21.12 L14.1912,39.6 L18.468,39.6 Z M21.9528,31.0464 L18.468,31.0464 L18.468,24.6048 L21.9528,24.6048 C24.5664,24.6048 25.8864,25.7928 25.8864,27.8256 C25.8864,29.832 24.5664,31.0464 21.9528,31.0464 Z M39.6672,39.9168 C44.9472,39.9168 47.508,37.2768 47.508,34.188 C47.508,27.4032 36.7632,29.7528 36.7632,26.3472 C36.7632,25.1856 37.74,24.2352 40.2744,24.2352 C41.9112,24.2352 43.68,24.7104 45.396,25.6872 L46.716,22.44 C45,21.3576 42.624,20.8032 40.3008,20.8032 C35.0472,20.8032 32.5128,23.4168 32.5128,26.5584 C32.5128,33.4224 43.2576,31.0464 43.2576,34.5048 C43.2576,35.64 42.228,36.4848 39.6936,36.4848 C37.476,36.4848 35.1528,35.6928 33.5952,34.5576 L32.1432,37.7784 C33.78,39.0456 36.7368,39.9168 39.6672,39.9168 Z" id="RS" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>
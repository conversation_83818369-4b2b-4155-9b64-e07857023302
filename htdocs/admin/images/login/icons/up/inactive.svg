<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.1 (89650) - https://sketch.com -->
    <title>icons / up / inactive</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="16" cy="16" r="16"></circle>
        <filter x="-26.6%" y="-20.3%" width="153.1%" height="153.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.195093969 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="icons-/-up-/-inactive" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icons-/-plus-/-plus_electric_blue_shadow-copy">
            <g id="Oval-Copy-4">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <g id="Group" transform="translate(16.044444, 15.600000) scale(1, -1) rotate(90.000000) translate(-16.044444, -15.600000) translate(13.600000, 11.200000)" stroke="#002251" stroke-linecap="round" stroke-width="2">
                <line x1="2.44444444" y1="3.75590829" x2="2.44444444" y2="9.06756473" id="Path-955" transform="translate(2.444444, 6.411737) rotate(-315.000000) translate(-2.444444, -6.411737) "></line>
                <line x1="2.44444444" y1="-1.16478606e-12" x2="2.44444444" y2="5.31165644" id="Path-955-Copy" transform="translate(2.444444, 2.655828) rotate(-585.000000) translate(-2.444444, -2.655828) "></line>
            </g>
        </g>
    </g>
</svg>
<?php
	
	define( 'IS_HTTP_ERROR', true );

	define('ADMIN_PAGE_TITLE', _('La page demandée n\'a pas été trouvée'));
	define('ADMIN_CLASS_BODY', 'http-errors' );
	define('ADMIN_ID_BODY', 'error-404' );

	require_once('admin/skin/header.inc.php');
?>

	<div class="wrapper" id="wrapper">
    <div class="wrapper-inner">
      <main id="main">
        <div class="message-section">
          <div class="container">
            <div class="message-block">
              <div class="img-holder">
                <img src="/admin/dist/images/404_illus.svg" alt="<?php print _('Erreur 404') ?>">
              </div>
              <div class="title-holder">
                <h2><?php echo _("Oups, erreur 404"); ?></h2>
                <p><?php echo _("Il semblerait que la page demandée n’existe pas... Nous sommes désolés pour ce désagrément !"); ?></p>
              </div>
              <div class="search-form"><form id="search-404" action="/admin/search/index.php" method="get">
                <strong class="title"><?php echo _("Souhaitez-vous faire une recherche ?") ?></strong>
                <div class="input-holder">
                  <input name="q" id="error-q" type="search" placeholder="<?php echo _("Effectuer une recherche..."); ?>">
                  <button type="submit"></button>
                </div>
              </form></div>
              <a href="/admin/index.php" class="btn btn-blue"><?php echo _("Non merci, ramenez-moi à l’accueil"); ?></a>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
<script>
	$(document).ready(function (){

		$('#search-404').submit(function (){
			if( $('#error-q').val().trim()=='' ){
				return false;
			}
		});

	});
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
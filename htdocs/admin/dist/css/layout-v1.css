@charset "UTF-8";
:root {
  --mdc-theme-primary: $dark-color;
}

@-webkit-keyframes mdc-ripple-fg-radius-in {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
            transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
  }
  to {
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
            transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
  }
}

@keyframes mdc-ripple-fg-radius-in {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
            transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
  }
  to {
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
            transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
  }
}

@-webkit-keyframes mdc-ripple-fg-opacity-in {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: 0;
  }
  to {
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
}

@keyframes mdc-ripple-fg-opacity-in {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: 0;
  }
  to {
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
}

@-webkit-keyframes mdc-ripple-fg-opacity-out {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
  to {
    opacity: 0;
  }
}

@keyframes mdc-ripple-fg-opacity-out {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
  to {
    opacity: 0;
  }
}

.mdc-ripple-surface--test-edge-var-bug {
  --mdc-ripple-surface-test-edge-var: 1px solid #000;
  visibility: hidden;
}

.mdc-ripple-surface--test-edge-var-bug::before {
  border: var(--mdc-ripple-surface-test-edge-var);
}

.mdc-button {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 2.25rem;
  font-weight: 500;
  letter-spacing: 0.08929em;
  text-decoration: none;
  text-transform: uppercase;
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  padding: 0 8px 0 8px;
  display: inline-flex;
  position: relative;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 64px;
  height: 36px;
  border: none;
  outline: none;
  /* @alternate */
  line-height: inherit;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-appearance: none;
  overflow: hidden;
  vertical-align: middle;
  border-radius: 4px;
}

.mdc-button::before, .mdc-button::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-button::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-button.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-button.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-button.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-button.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-button.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-button::before, .mdc-button::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-button.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.mdc-button:active {
  outline: none;
}

.mdc-button:hover {
  cursor: pointer;
}

.mdc-button:disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.37);
  cursor: default;
  pointer-events: none;
}

.mdc-button.mdc-button--dense {
  border-radius: 4px;
}

.mdc-button:not(:disabled) {
  background-color: transparent;
}

.mdc-button:not(:disabled) {
  color: #232E63;
  /* @alternate */
  color: var(--mdc-theme-primary, #232E63);
}

.mdc-button::before, .mdc-button::after {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button::before, .mdc-button::after {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-button:hover::before {
  opacity: 0.04;
}

.mdc-button:not(.mdc-ripple-upgraded):focus::before, .mdc-button.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-button:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-button:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-button.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-button .mdc-button__icon {
  /* @noflip */
  margin-left: 0;
  /* @noflip */
  margin-right: 8px;
  display: inline-block;
  width: 18px;
  height: 18px;
  font-size: 18px;
  vertical-align: top;
}

[dir="rtl"] .mdc-button .mdc-button__icon, .mdc-button .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: 0;
}

.mdc-button__label + .mdc-button__icon {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: 0;
}

[dir="rtl"] .mdc-button__label + .mdc-button__icon, .mdc-button__label + .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 0;
  /* @noflip */
  margin-right: 8px;
}

svg.mdc-button__icon {
  fill: currentColor;
}

.mdc-button--raised .mdc-button__icon,
.mdc-button--unelevated .mdc-button__icon,
.mdc-button--outlined .mdc-button__icon {
  /* @noflip */
  margin-left: -4px;
  /* @noflip */
  margin-right: 8px;
}

[dir="rtl"] .mdc-button--raised .mdc-button__icon, .mdc-button--raised .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--unelevated .mdc-button__icon,
.mdc-button--unelevated .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--outlined .mdc-button__icon,
.mdc-button--outlined .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: -4px;
}

.mdc-button--raised .mdc-button__label + .mdc-button__icon,
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon,
.mdc-button--outlined .mdc-button__label + .mdc-button__icon {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: -4px;
}

[dir="rtl"] .mdc-button--raised .mdc-button__label + .mdc-button__icon, .mdc-button--raised .mdc-button__label + .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon,
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--outlined .mdc-button__label + .mdc-button__icon,
.mdc-button--outlined .mdc-button__label + .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: -4px;
  /* @noflip */
  margin-right: 8px;
}

.mdc-button--raised,
.mdc-button--unelevated {
  padding: 0 16px 0 16px;
}

.mdc-button--raised:disabled,
.mdc-button--unelevated:disabled {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.37);
}

.mdc-button--raised:not(:disabled),
.mdc-button--unelevated:not(:disabled) {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button--raised:not(:disabled),
  .mdc-button--unelevated:not(:disabled) {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-button--raised:not(:disabled),
.mdc-button--unelevated:not(:disabled) {
  color: #fff;
  /* @alternate */
  color: var(--mdc-theme-on-primary, #fff);
}

.mdc-button--raised::before, .mdc-button--raised::after,
.mdc-button--unelevated::before,
.mdc-button--unelevated::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button--raised::before, .mdc-button--raised::after,
  .mdc-button--unelevated::before,
  .mdc-button--unelevated::after {
    /* @alternate */
    background-color: var(--mdc-theme-on-primary, #fff);
  }
}

.mdc-button--raised:hover::before,
.mdc-button--unelevated:hover::before {
  opacity: 0.08;
}

.mdc-button--raised:not(.mdc-ripple-upgraded):focus::before, .mdc-button--raised.mdc-ripple-upgraded--background-focused::before,
.mdc-button--unelevated:not(.mdc-ripple-upgraded):focus::before,
.mdc-button--unelevated.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-button--raised:not(.mdc-ripple-upgraded)::after,
.mdc-button--unelevated:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-button--raised:not(.mdc-ripple-upgraded):active::after,
.mdc-button--unelevated:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-button--raised.mdc-ripple-upgraded,
.mdc-button--unelevated.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-button--raised {
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-button--raised:hover, .mdc-button--raised:focus {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.mdc-button--raised:active {
  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.mdc-button--raised:disabled {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
}

.mdc-button--outlined {
  border-style: solid;
  padding: 0 14px 0 14px;
  border-width: 2px;
}

.mdc-button--outlined:disabled {
  border-color: rgba(0, 0, 0, 0.37);
}

.mdc-button--outlined:not(:disabled) {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-button--dense {
  height: 32px;
  font-size: .8125rem;
}

@-webkit-keyframes mdc-checkbox-unchecked-checked-checkmark-path {
  0%,
  50% {
    stroke-dashoffset: 29.78334;
  }
  50% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes mdc-checkbox-unchecked-checked-checkmark-path {
  0%,
  50% {
    stroke-dashoffset: 29.78334;
  }
  50% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@-webkit-keyframes mdc-checkbox-unchecked-indeterminate-mixedmark {
  0%,
  68.2% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
  }
  68.2% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0, 1);
            animation-timing-function: cubic-bezier(0, 0, 0, 1);
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}

@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark {
  0%,
  68.2% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
  }
  68.2% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0, 1);
            animation-timing-function: cubic-bezier(0, 0, 0, 1);
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}

@-webkit-keyframes mdc-checkbox-checked-unchecked-checkmark-path {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
    opacity: 1;
    stroke-dashoffset: 0;
  }
  to {
    opacity: 0;
    stroke-dashoffset: -29.78334;
  }
}

@keyframes mdc-checkbox-checked-unchecked-checkmark-path {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
    opacity: 1;
    stroke-dashoffset: 0;
  }
  to {
    opacity: 0;
    stroke-dashoffset: -29.78334;
  }
}

@-webkit-keyframes mdc-checkbox-checked-indeterminate-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-checked-indeterminate-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-checked-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
  }
}

@keyframes mdc-checkbox-indeterminate-checked-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
  }
}

@-webkit-keyframes mdc-checkbox-checked-indeterminate-mixedmark {
  from {
    -webkit-animation-timing-function: mdc-animation-deceleration-curve-timing-function;
            animation-timing-function: mdc-animation-deceleration-curve-timing-function;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
}

@keyframes mdc-checkbox-checked-indeterminate-mixedmark {
  from {
    -webkit-animation-timing-function: mdc-animation-deceleration-curve-timing-function;
            animation-timing-function: mdc-animation-deceleration-curve-timing-function;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-checked-mixedmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(315deg);
            transform: rotate(315deg);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-indeterminate-checked-mixedmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(315deg);
            transform: rotate(315deg);
    opacity: 0;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-unchecked-mixedmark {
  0% {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    opacity: 1;
  }
  32.8%,
  100% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark {
  0% {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    opacity: 1;
  }
  32.8%,
  100% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    opacity: 0;
  }
}

.mdc-checkbox {
  display: inline-block;
  position: relative;
  flex: 0 0 18px;
  box-sizing: content-box;
  width: 18px;
  height: 18px;
  padding: 11px;
  line-height: 0;
  white-space: nowrap;
  cursor: pointer;
  vertical-align: bottom;
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
}

.mdc-checkbox::before, .mdc-checkbox::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-checkbox::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-checkbox.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-checkbox.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-checkbox.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-checkbox.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-checkbox.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-checkbox::before, .mdc-checkbox::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-checkbox::before, .mdc-checkbox::after {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-checkbox:hover::before {
  opacity: 0.08;
}

.mdc-checkbox:not(.mdc-ripple-upgraded):focus::before, .mdc-checkbox.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-checkbox:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-checkbox:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-checkbox.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-checkbox::before, .mdc-checkbox::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-checkbox.mdc-ripple-upgraded::before, .mdc-checkbox.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-checkbox.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-checkbox__checkmark {
  color: #fff;
}

.mdc-checkbox__mixedmark {
  border-color: #fff;
}

.mdc-checkbox__background::before {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-checkbox__background::before {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate) ~ .mdc-checkbox__background {
  border-color: rgba(0, 0, 0, 0.54);
  background-color: transparent;
}

.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
  border-color: #fff;
  /* @alternate */
  border-color: var(--mdc-theme-secondary, #fff);
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-secondary, #fff);
}

@-webkit-keyframes mdc-checkbox-fade-in-background-0 {
  0% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
  50% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

@keyframes mdc-checkbox-fade-in-background-0 {
  0% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
  50% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

@-webkit-keyframes mdc-checkbox-fade-out-background-0 {
  0%, 80% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
  100% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
}

@keyframes mdc-checkbox-fade-out-background-0 {
  0%, 80% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
  100% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background, .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
  -webkit-animation-name: mdc-checkbox-fade-in-background-0;
          animation-name: mdc-checkbox-fade-in-background-0;
}

.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background, .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
  -webkit-animation-name: mdc-checkbox-fade-out-background-0;
          animation-name: mdc-checkbox-fade-out-background-0;
}

.mdc-checkbox__native-control:disabled:not(:checked):not(:indeterminate) ~ .mdc-checkbox__background {
  border-color: rgba(0, 0, 0, 0.26);
}

.mdc-checkbox__native-control:disabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:disabled:indeterminate ~ .mdc-checkbox__background {
  border-color: transparent;
  background-color: rgba(0, 0, 0, 0.26);
}

@media screen and (-ms-high-contrast: active) {
  .mdc-checkbox__mixedmark {
    margin: 0 1px;
  }
}

.mdc-checkbox--disabled {
  cursor: default;
  pointer-events: none;
}

.mdc-checkbox__background {
  /* @noflip */
  left: 11px;
  /* @noflip */
  right: initial;
  display: inline-flex;
  position: absolute;
  top: 11px;
  bottom: 0;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 45%;
  height: 45%;
  transition: background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border: 2px solid currentColor;
  border-radius: 2px;
  background-color: transparent;
  pointer-events: none;
  will-change: background-color, border-color;
}

.mdc-checkbox[dir="rtl"] .mdc-checkbox__background,
[dir="rtl"] .mdc-checkbox .mdc-checkbox__background {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 11px;
}

.mdc-checkbox__checkmark {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  opacity: 0;
}

.mdc-checkbox--upgraded .mdc-checkbox__checkmark {
  opacity: 1;
}

.mdc-checkbox__checkmark-path {
  transition: stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  stroke: currentColor;
  stroke-width: 3.12px;
  stroke-dashoffset: 29.78334;
  stroke-dasharray: 29.78334;
}

.mdc-checkbox__mixedmark {
  width: 100%;
  height: 0;
  -webkit-transform: scaleX(0) rotate(0deg);
          transform: scaleX(0) rotate(0deg);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border-width: 1px;
  border-style: solid;
  opacity: 0;
}

.mdc-checkbox--upgraded .mdc-checkbox__background,
.mdc-checkbox--upgraded .mdc-checkbox__checkmark,
.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,
.mdc-checkbox--upgraded .mdc-checkbox__mixedmark {
  transition: none !important;
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background, .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background, .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background, .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background {
  -webkit-animation-duration: 180ms;
          animation-duration: 180ms;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path {
  -webkit-animation: 180ms linear 0s mdc-checkbox-unchecked-checked-checkmark-path;
          animation: 180ms linear 0s mdc-checkbox-unchecked-checked-checkmark-path;
  transition: none;
}

.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-unchecked-indeterminate-mixedmark;
          animation: 90ms linear 0s mdc-checkbox-unchecked-indeterminate-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-unchecked-checkmark-path;
          animation: 90ms linear 0s mdc-checkbox-checked-unchecked-checkmark-path;
  transition: none;
}

.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-checkmark;
          animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-checkmark;
  transition: none;
}

.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-mixedmark;
          animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark {
  -webkit-animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-checkmark;
          animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-checkmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark {
  -webkit-animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-mixedmark;
          animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark {
  -webkit-animation: 300ms linear 0s mdc-checkbox-indeterminate-unchecked-mixedmark;
          animation: 300ms linear 0s mdc-checkbox-indeterminate-unchecked-mixedmark;
  transition: none;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background {
  transition: border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1), background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1);
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path,
.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path {
  stroke-dashoffset: 0;
}

.mdc-checkbox__background::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0, 0);
          transform: scale(0, 0);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
  will-change: opacity, transform;
}

.mdc-ripple-upgraded--background-focused .mdc-checkbox__background::before {
  content: none;
}

.mdc-checkbox__native-control:focus ~ .mdc-checkbox__background::before {
  -webkit-transform: scale(2.75, 2.75);
          transform: scale(2.75, 2.75);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  opacity: 0.12;
}

.mdc-checkbox__native-control {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  opacity: 0;
  cursor: inherit;
}

.mdc-checkbox__native-control:disabled {
  cursor: default;
  pointer-events: none;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  opacity: 1;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__mixedmark {
  -webkit-transform: scaleX(1) rotate(-45deg);
          transform: scaleX(1) rotate(-45deg);
}

.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  opacity: 0;
}

.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__mixedmark {
  -webkit-transform: scaleX(1) rotate(0deg);
          transform: scaleX(1) rotate(0deg);
  opacity: 1;
}

.mdc-floating-label {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
  position: absolute;
  /* @noflip */
  left: 0;
  /* @noflip */
  -webkit-transform-origin: left top;
          transform-origin: left top;
  transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  /* @alternate */
  line-height: 1.15rem;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: text;
  overflow: hidden;
  will-change: transform;
}

[dir="rtl"] .mdc-floating-label, .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  right: 0;
  /* @noflip */
  left: auto;
  /* @noflip */
  -webkit-transform-origin: right top;
          transform-origin: right top;
  /* @noflip */
  text-align: right;
}

.mdc-floating-label--float-above {
  cursor: auto;
}

.mdc-floating-label--float-above {
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
}

.mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-standard 250ms 1;
          animation: mdc-floating-label-shake-float-above-standard 250ms 1;
}

@-webkit-keyframes mdc-floating-label-shake-float-above-standard {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-standard {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
}

.mdc-form-field {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.01786em;
  text-decoration: inherit;
  text-transform: inherit;
  color: rgba(0, 0, 0, 0.87);
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87));
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.mdc-form-field > label {
  order: 0;
  /* @noflip */
  margin-right: auto;
  /* @noflip */
  padding-left: 4px;
}

[dir="rtl"] .mdc-form-field > label, .mdc-form-field[dir="rtl"] > label {
  /* @noflip */
  margin-left: auto;
  /* @noflip */
  padding-right: 4px;
}

.mdc-form-field--align-end > label {
  order: -1;
  /* @noflip */
  margin-left: auto;
  /* @noflip */
  padding-right: 4px;
}

[dir="rtl"] .mdc-form-field--align-end > label, .mdc-form-field--align-end[dir="rtl"] > label {
  /* @noflip */
  margin-right: auto;
  /* @noflip */
  padding-left: 4px;
}

.mdc-icon-button {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  width: 48px;
  height: 48px;
  padding: 12px;
  font-size: 24px;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  border: none;
  outline: none;
  background-color: transparent;
  fill: currentColor;
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.mdc-icon-button::before, .mdc-icon-button::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-button::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-icon-button.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-button.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-icon-button.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-icon-button.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-icon-button.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-button::before, .mdc-icon-button::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-icon-button.mdc-ripple-upgraded::before, .mdc-icon-button.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-button.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-button svg,
.mdc-icon-button img {
  width: 24px;
  height: 24px;
}

.mdc-icon-button:disabled {
  color: rgba(0, 0, 0, 0.38);
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38));
  cursor: default;
  pointer-events: none;
}

.mdc-icon-button::before, .mdc-icon-button::after {
  background-color: #000;
}

.mdc-icon-button:hover::before {
  opacity: 0.04;
}

.mdc-icon-button:not(.mdc-ripple-upgraded):focus::before, .mdc-icon-button.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-icon-button:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-icon-button:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-icon-button.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-icon-button__icon {
  display: inline-block;
}

.mdc-icon-button__icon.mdc-icon-button__icon--on {
  display: none;
}

.mdc-icon-button--on .mdc-icon-button__icon {
  display: none;
}

.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on {
  display: inline-block;
}

.mdc-icon-toggle {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  color: rgba(0, 0, 0, 0.87);
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-light, rgba(0, 0, 0, 0.87));
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 48px;
  height: 48px;
  padding: 12px;
  outline: none;
  font-size: 1.5rem;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* @alternate */
  will-change: initial;
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-toggle::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-icon-toggle.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-toggle.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-icon-toggle.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-icon-toggle.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-icon-toggle.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-icon-toggle.mdc-ripple-upgraded::before, .mdc-icon-toggle.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-toggle.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  background-color: black;
}

.mdc-icon-toggle:hover::before {
  opacity: 0.04;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded):focus::before, .mdc-icon-toggle.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-icon-toggle.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-icon-toggle::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-toggle--disabled {
  color: rgba(0, 0, 0, 0.38);
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38));
  pointer-events: none;
}

.mdc-line-ripple {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 180ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  z-index: 2;
}

.mdc-line-ripple--active {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
  opacity: 1;
}

.mdc-line-ripple--deactivating {
  opacity: 0;
}

.mdc-ripple-surface {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  position: relative;
  outline: none;
  overflow: hidden;
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-ripple-surface::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-ripple-surface.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-ripple-surface.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-ripple-surface.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-ripple-surface.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-ripple-surface.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  background-color: #000;
}

.mdc-ripple-surface:hover::before {
  opacity: 0.04;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-ripple-surface.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-ripple-surface.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded] {
  overflow: visible;
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded]::before, .mdc-ripple-surface[data-mdc-ripple-is-unbounded]::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::before, .mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface--primary::before, .mdc-ripple-surface--primary::after {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-ripple-surface--primary::before, .mdc-ripple-surface--primary::after {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-ripple-surface--primary:hover::before {
  opacity: 0.04;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface--primary.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-ripple-surface--primary.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-ripple-surface--accent::before, .mdc-ripple-surface--accent::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-ripple-surface--accent::before, .mdc-ripple-surface--accent::after {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-ripple-surface--accent:hover::before {
  opacity: 0.08;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface--accent.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-ripple-surface--accent.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-notched-outline {
  display: flex;
  position: absolute;
  right: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  height: 100%;
  /* @noflip */
  text-align: left;
  pointer-events: none;
}

[dir="rtl"] .mdc-notched-outline, .mdc-notched-outline[dir="rtl"] {
  /* @noflip */
  text-align: right;
}

.mdc-notched-outline__leading, .mdc-notched-outline__notch, .mdc-notched-outline__trailing {
  box-sizing: border-box;
  height: 100%;
  border-top: 1px solid;
  border-bottom: 1px solid;
  pointer-events: none;
}

.mdc-notched-outline__leading {
  /* @noflip */
  border-left: 1px solid;
  /* @noflip */
  border-right: none;
  width: 12px;
}

[dir="rtl"] .mdc-notched-outline__leading, .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-left: none;
  /* @noflip */
  border-right: 1px solid;
}

.mdc-notched-outline__trailing {
  /* @noflip */
  border-left: none;
  /* @noflip */
  border-right: 1px solid;
  flex-grow: 1;
}

[dir="rtl"] .mdc-notched-outline__trailing, .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-left: 1px solid;
  /* @noflip */
  border-right: none;
}

.mdc-notched-outline__notch {
  flex: 0 0 auto;
  width: auto;
  max-width: calc(100% - 12px * 2);
}

.mdc-notched-outline .mdc-floating-label {
  display: inline-block;
  position: relative;
  top: 17px;
  bottom: auto;
  max-width: 100%;
}

.mdc-notched-outline .mdc-floating-label--float-above {
  text-overflow: clip;
}

.mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  max-width: calc(100% / .75);
}

.mdc-notched-outline--notched .mdc-notched-outline__notch {
  /* @noflip */
  padding-left: 0;
  /* @noflip */
  padding-right: 8px;
  border-top: none;
}

[dir="rtl"] .mdc-notched-outline--notched .mdc-notched-outline__notch, .mdc-notched-outline--notched .mdc-notched-outline__notch[dir="rtl"] {
  /* @noflip */
  padding-left: 8px;
  /* @noflip */
  padding-right: 0;
}

.mdc-notched-outline--no-label .mdc-notched-outline__notch {
  padding: 0;
}

.mdc-text-field-helper-text {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.03333em;
  text-decoration: inherit;
  text-transform: inherit;
  display: block;
  margin-top: 0;
  /* @alternate */
  line-height: normal;
  margin: 0;
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  will-change: opacity;
}

.mdc-text-field-helper-text::before {
  display: inline-block;
  width: 0;
  height: 16px;
  content: "";
  vertical-align: 0;
}

.mdc-text-field-helper-text--persistent {
  transition: none;
  opacity: 1;
  will-change: initial;
}

.mdc-text-field--with-leading-icon .mdc-text-field__icon,
.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  position: absolute;
  bottom: 16px;
  cursor: pointer;
}

.mdc-text-field__icon:not([tabindex]),
.mdc-text-field__icon[tabindex="-1"] {
  cursor: default;
  pointer-events: none;
}

.mdc-text-field {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  border-radius: 4px 4px 0 0;
  display: inline-flex;
  position: relative;
  box-sizing: border-box;
  height: 56px;
  overflow: hidden;
  will-change: opacity, transform, color;
}

.mdc-text-field::before, .mdc-text-field::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-text-field::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-text-field.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-text-field.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-text-field.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-text-field.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-text-field.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-text-field::before, .mdc-text-field::after {
  background-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field:hover::before {
  opacity: 0.04;
}

.mdc-text-field:not(.mdc-ripple-upgraded):focus::before, .mdc-text-field.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-text-field::before, .mdc-text-field::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-text-field.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input {
  color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field .mdc-text-field__input {
  caret-color: #232E63;
  /* @alternate */
  caret-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom-color: rgba(0, 0, 0, 0.42);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field .mdc-line-ripple {
  background-color: #232E63;
  /* @alternate */
  background-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--textarea) {
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.mdc-text-field:not(.mdc-text-field--disabled) + .mdc-text-field-helper-text {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__icon {
  color: rgba(0, 0, 0, 0.54);
}

.mdc-text-field:not(.mdc-text-field--disabled) {
  background-color: whitesmoke;
}

.mdc-text-field .mdc-floating-label {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
  top: 18px;
  pointer-events: none;
}

[dir="rtl"] .mdc-text-field .mdc-floating-label, .mdc-text-field .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--textarea .mdc-floating-label {
  /* @noflip */
  left: 4px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-floating-label, .mdc-text-field--textarea .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 4px;
}

.mdc-text-field--outlined .mdc-floating-label {
  /* @noflip */
  left: 4px;
  /* @noflip */
  right: initial;
  top: 17px;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-floating-label, .mdc-text-field--outlined .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 4px;
}

.mdc-text-field--outlined--with-leading-icon .mdc-floating-label {
  /* @noflip */
  left: 36px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--outlined--with-leading-icon .mdc-floating-label, .mdc-text-field--outlined--with-leading-icon .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 36px;
}

.mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above {
  /* @noflip */
  left: 40px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above, .mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 40px;
}

.mdc-text-field__input {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
  align-self: flex-end;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px 16px 6px;
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  border-bottom: 1px solid;
  border-radius: 0;
  background: none;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.mdc-text-field__input::-webkit-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input:-ms-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input::-ms-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input::placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input:focus {
  outline: none;
}

.mdc-text-field__input:invalid {
  box-shadow: none;
}

.mdc-text-field__input:-webkit-autofill + .mdc-floating-label {
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
  cursor: auto;
}

.mdc-text-field--outlined {
  border: none;
  overflow: visible;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.24);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--outlined .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
}

.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading, .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing, .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

.mdc-text-field--outlined .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) scale(1);
          transform: translateY(-144%) scale(1);
}

.mdc-text-field--outlined .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) scale(0.75);
          transform: translateY(-130%) scale(0.75);
}

.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--outlined::before, .mdc-text-field--outlined::after {
  content: none;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--outlined .mdc-text-field__input {
  display: flex;
  padding: 12px 16px 14px;
  border: none !important;
  background-color: transparent;
  z-index: 1;
}

.mdc-text-field--outlined .mdc-text-field__icon {
  z-index: 2;
}

.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__trailing {
  border-width: 2px;
}

.mdc-text-field--outlined.mdc-text-field--disabled {
  background-color: transparent;
}

.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom: none;
}

.mdc-text-field--outlined.mdc-text-field--dense {
  height: 48px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-134%) scale(1);
          transform: translateY(-134%) scale(1);
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: 0.8rem;
}

.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-120%) scale(0.8);
          transform: translateY(-120%) scale(0.8);
}

.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-dense 250ms 1;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-text-field__input {
  padding: 12px 12px 7px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label {
  top: 14px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-text-field__icon {
  top: 12px;
}

.mdc-text-field--with-leading-icon .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-text-field__icon, .mdc-text-field--with-leading-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-text-field__input, .mdc-text-field--with-leading-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon .mdc-floating-label {
  /* @noflip */
  left: 48px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-floating-label, .mdc-text-field--with-leading-icon .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) translateX(-32px) scale(1);
          transform: translateY(-144%) translateX(-32px) scale(1);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-144%) translateX(32px) scale(1);
          transform: translateY(-144%) translateX(32px) scale(1);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) translateX(-32px) scale(0.75);
          transform: translateY(-130%) translateX(-32px) scale(0.75);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"], [dir="rtl"]
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-130%) translateX(32px) scale(0.75);
          transform: translateY(-130%) translateX(32px) scale(0.75);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake, .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir="rtl"] .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl 250ms 1;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label {
  /* @noflip */
  left: 36px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 36px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-134%) translateX(-21px) scale(1);
          transform: translateY(-134%) translateX(-21px) scale(1);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-134%) translateX(21px) scale(1);
          transform: translateY(-134%) translateX(21px) scale(1);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: 0.8rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-120%) translateX(-21px) scale(0.8);
          transform: translateY(-120%) translateX(-21px) scale(0.8);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"], [dir="rtl"]
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-120%) translateX(21px) scale(0.8);
          transform: translateY(-120%) translateX(21px) scale(0.8);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense 250ms 1;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense[dir="rtl"] .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl 250ms 1;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label {
  /* @noflip */
  left: 32px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 32px;
}

.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon .mdc-text-field__icon, .mdc-text-field--with-trailing-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon .mdc-text-field__input, .mdc-text-field--with-trailing-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 12px;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon, .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input, .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: auto;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon {
  /* @noflip */
  right: 12px;
  /* @noflip */
  left: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  right: auto;
  /* @noflip */
  left: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon,
.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  bottom: 16px;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 12px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 44px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label {
  /* @noflip */
  left: 44px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 44px;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 44px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: auto;
  /* @noflip */
  right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon {
  /* @noflip */
  right: 12px;
  /* @noflip */
  left: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  right: auto;
  /* @noflip */
  left: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 44px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 44px;
}

.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-70%) scale(0.8);
          transform: translateY(-70%) scale(0.8);
}

.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-dense 250ms 1;
}

.mdc-text-field--dense .mdc-text-field__input {
  padding: 12px 12px 0;
}

.mdc-text-field--dense .mdc-floating-label {
  font-size: .813rem;
}

.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: .813rem;
}

.mdc-text-field__input:required ~ .mdc-floating-label::after,
.mdc-text-field__input:required ~ .mdc-notched-outline .mdc-floating-label::after {
  margin-left: 1px;
  content: "*";
}

.mdc-text-field--textarea {
  display: inline-flex;
  width: auto;
  height: auto;
  transition: none;
  overflow: visible;
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.24);
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--textarea .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
}

.mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading, .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

.mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing, .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

.mdc-text-field--textarea::before, .mdc-text-field--textarea::after {
  content: none;
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--textarea .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) scale(1);
          transform: translateY(-144%) scale(1);
}

.mdc-text-field--textarea .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--textarea.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--textarea .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) scale(0.75);
          transform: translateY(-130%) scale(0.75);
}

.mdc-text-field--textarea.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--textarea .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--textarea .mdc-text-field__input {
  align-self: auto;
  box-sizing: border-box;
  height: auto;
  margin: 8px 1px 1px 0;
  padding: 0 16px 16px;
  border: none;
}

.mdc-text-field--textarea .mdc-floating-label {
  top: 17px;
  bottom: auto;
  width: auto;
  pointer-events: none;
}

.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__trailing {
  border-width: 2px;
}

.mdc-text-field--fullwidth {
  width: 100%;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea) {
  display: block;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea)::before, .mdc-text-field--fullwidth:not(.mdc-text-field--textarea)::after {
  content: none;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea) .mdc-text-field__input {
  padding: 0;
}

.mdc-text-field--fullwidth.mdc-text-field--textarea .mdc-text-field__input {
  resize: vertical;
}

.mdc-text-field--fullwidth.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--textarea) {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--dense + .mdc-text-field-helper-text {
  margin-bottom: 4px;
}

.mdc-text-field + .mdc-text-field-helper-text {
  margin-right: 12px;
  margin-left: 12px;
}

.mdc-text-field--outlined + .mdc-text-field-helper-text {
  margin-right: 16px;
  margin-left: 16px;
}

.mdc-form-field > .mdc-text-field + label {
  align-self: flex-start;
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused .mdc-text-field__input:required ~ .mdc-floating-label::after,
.mdc-text-field--focused .mdc-text-field__input:required ~ .mdc-notched-outline .mdc-floating-label::after {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--focused + .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg) {
  opacity: 1;
}

.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple {
  background-color: #b00020;
  /* @alternate */
  background-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--invalid + .mdc-text-field-helper-text--validation-msg {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid .mdc-text-field__input {
  caret-color: #b00020;
  /* @alternate */
  caret-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid.mdc-text-field--with-trailing-icon:not(.mdc-text-field--with-leading-icon):not(.mdc-text-field--disabled) .mdc-text-field__icon {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid.mdc-text-field--with-trailing-icon.mdc-text-field--with-leading-icon:not(.mdc-text-field--disabled) .mdc-text-field__icon ~ .mdc-text-field__icon {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid + .mdc-text-field-helper-text--validation-msg {
  opacity: 1;
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--disabled {
  background-color: #fafafa;
  border-bottom: none;
  pointer-events: none;
}

.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--disabled .mdc-text-field__input {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-floating-label {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled + .mdc-text-field-helper-text {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__icon {
  color: rgba(0, 0, 0, 0.3);
}

.mdc-text-field--disabled:not(.mdc-text-field--textarea) {
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.mdc-text-field--disabled .mdc-floating-label {
  cursor: default;
}

.mdc-text-field--textarea.mdc-text-field--disabled {
  background-color: transparent;
  background-color: #f9f9f9;
}

.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--textarea.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom: none;
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-textarea {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-textarea {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

:root {
  --mdc-theme-primary: #232E63;
  --mdc-theme-secondary: #fff;
  --mdc-theme-background: #fff;
  --mdc-theme-surface: #fff;
  --mdc-theme-error: #b00020;
  --mdc-theme-on-primary: #fff;
  --mdc-theme-on-secondary: #fff;
  --mdc-theme-on-surface: #000;
  --mdc-theme-on-error: #fff;
  --mdc-theme-text-primary-on-background: rgba(0, 0, 0, 0.87);
  --mdc-theme-text-secondary-on-background: rgba(0, 0, 0, 0.54);
  --mdc-theme-text-hint-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-disabled-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-icon-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-primary-on-light: rgba(0, 0, 0, 0.87);
  --mdc-theme-text-secondary-on-light: rgba(0, 0, 0, 0.54);
  --mdc-theme-text-hint-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-disabled-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-icon-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-primary-on-dark: white;
  --mdc-theme-text-secondary-on-dark: rgba(255, 255, 255, 0.7);
  --mdc-theme-text-hint-on-dark: rgba(255, 255, 255, 0.5);
  --mdc-theme-text-disabled-on-dark: rgba(255, 255, 255, 0.5);
  --mdc-theme-text-icon-on-dark: rgba(255, 255, 255, 0.5);
}

.mdc-theme--primary {
  color: #232E63 !important;
  /* @alternate */
  color: var(--mdc-theme-primary, #232E63) !important;
}

.mdc-theme--secondary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-secondary, #fff) !important;
}

.mdc-theme--background {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-background, #fff);
}

.mdc-theme--surface {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-surface, #fff);
}

.mdc-theme--error {
  color: #b00020 !important;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020) !important;
}

.mdc-theme--on-primary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-primary, #fff) !important;
}

.mdc-theme--on-secondary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-secondary, #fff) !important;
}

.mdc-theme--on-surface {
  color: #000 !important;
  /* @alternate */
  color: var(--mdc-theme-on-surface, #000) !important;
}

.mdc-theme--on-error {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-error, #fff) !important;
}

.mdc-theme--text-primary-on-background {
  color: rgba(0, 0, 0, 0.87) !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87)) !important;
}

.mdc-theme--text-secondary-on-background {
  color: rgba(0, 0, 0, 0.54) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-background, rgba(0, 0, 0, 0.54)) !important;
}

.mdc-theme--text-hint-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-disabled-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-icon-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-primary-on-light {
  color: rgba(0, 0, 0, 0.87) !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-light, rgba(0, 0, 0, 0.87)) !important;
}

.mdc-theme--text-secondary-on-light {
  color: rgba(0, 0, 0, 0.54) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-light, rgba(0, 0, 0, 0.54)) !important;
}

.mdc-theme--text-hint-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-disabled-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-icon-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-primary-on-dark {
  color: white !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-dark, white) !important;
}

.mdc-theme--text-secondary-on-dark {
  color: rgba(255, 255, 255, 0.7) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-dark, rgba(255, 255, 255, 0.7)) !important;
}

.mdc-theme--text-hint-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--text-disabled-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--text-icon-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--primary-bg {
  background-color: #232E63 !important;
  /* @alternate */
  background-color: var(--mdc-theme-primary, #232E63) !important;
}

.mdc-theme--secondary-bg {
  background-color: #fff !important;
  /* @alternate */
  background-color: var(--mdc-theme-secondary, #fff) !important;
}

.mdc-typography {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.mdc-typography--headline1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 6rem;
  line-height: 6rem;
  font-weight: 500;
  letter-spacing: -0.01562em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 3.75rem;
  line-height: 3.75rem;
  font-weight: 500;
  letter-spacing: -0.00833em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline3 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 3rem;
  line-height: 3.125rem;
  font-weight: 500;
  letter-spacing: normal;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline4 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 2.125rem;
  line-height: 2.5rem;
  font-weight: 500;
  letter-spacing: 0.00735em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline5 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: normal;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline6 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1.25rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: 0.0125em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--subtitle1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--subtitle2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.375rem;
  font-weight: 500;
  letter-spacing: 0.00714em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--body1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.03125em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--body2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.01786em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--caption {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.03333em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--button {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 2.25rem;
  font-weight: 500;
  letter-spacing: 0.08929em;
  text-decoration: none;
  text-transform: uppercase;
}

.mdc-typography--overline {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: 0.16667em;
  text-decoration: none;
  text-transform: uppercase;
}

body {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
}

html {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
  font-weight: 400;
}

/**
 * Firefox specific rule
 */
@-moz-document url-prefix() {
  body {
    font-weight: lighter !important;
  }
}

* {
  transition: all .2s;
  box-sizing: border-box;
}

/* Surcharge Police sur Google Maps */
.gm-style {
  font-family: 'Montserrat', sans-serif !important;
}

/* Personalisations des champs standards */
/* Inputs */
input[type="color"],
input[type="date"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="text"],
input[type="time"],
input[type="url"],
input[type="week"],
select,
textarea {
  background-color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  width: 400px;
  max-width: 400px;
  border: 1px solid #A9A9A9;
  border-radius: 6px;
  margin-top: 2px;
  margin-bottom: 2px;
  padding: 0 4px;
}

input[type="color"] ,
input[type="date"] ,
input[type="datetime-local"] ,
input[type="email"] ,
input[type="month"] ,
input[type="number"] ,
input[type="password"] ,
input[type="search"] ,
input[type="tel"] ,
input[type="text"] ,
input[type="time"] ,
input[type="url"] ,
input[type="week"] ,
select ,
textarea {
  vertical-align: top;
}

@media (max-width: 767px) {
  input[type="color"],
  input[type="date"],
  input[type="datetime-local"],
  input[type="email"],
  input[type="month"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="text"],
  input[type="time"],
  input[type="url"],
  input[type="week"],
  select,
  textarea {
    width: 100%;
  }
}

thead input[type="color"], tfoot input[type="color"], thead
input[type="date"], tfoot
input[type="date"], thead
input[type="datetime-local"], tfoot
input[type="datetime-local"], thead
input[type="email"], tfoot
input[type="email"], thead
input[type="month"], tfoot
input[type="month"], thead
input[type="number"], tfoot
input[type="number"], thead
input[type="password"], tfoot
input[type="password"], thead
input[type="search"], tfoot
input[type="search"], thead
input[type="tel"], tfoot
input[type="tel"], thead
input[type="text"], tfoot
input[type="text"], thead
input[type="time"], tfoot
input[type="time"], thead
input[type="url"], tfoot
input[type="url"], thead
input[type="week"], tfoot
input[type="week"], thead
select, tfoot
select, thead
textarea, tfoot
textarea {
  width: auto;
}

input[type="color"]:hover,
input[type="date"]:hover,
input[type="datetime-local"]:hover,
input[type="email"]:hover,
input[type="month"]:hover,
input[type="number"]:hover,
input[type="password"]:hover,
input[type="search"]:hover,
input[type="tel"]:hover,
input[type="text"]:hover,
input[type="time"]:hover,
input[type="url"]:hover,
input[type="week"]:hover,
select:hover,
textarea:hover {
  border: 1px solid #232E63;
}

input[type="color"]:focus:not(#mdc-input),
input[type="date"]:focus:not(#mdc-input),
input[type="datetime-local"]:focus:not(#mdc-input),
input[type="email"]:focus:not(#mdc-input),
input[type="month"]:focus:not(#mdc-input),
input[type="number"]:focus:not(#mdc-input),
input[type="password"]:focus:not(#mdc-input),
input[type="search"]:focus:not(#mdc-input),
input[type="tel"]:focus:not(#mdc-input),
input[type="text"]:focus:not(#mdc-input),
input[type="time"]:focus:not(#mdc-input),
input[type="url"]:focus:not(#mdc-input),
input[type="week"]:focus:not(#mdc-input),
select:focus:not(#mdc-input),
textarea:focus:not(#mdc-input) {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="color"].input-edit-success,
input[type="date"].input-edit-success,
input[type="datetime-local"].input-edit-success,
input[type="email"].input-edit-success,
input[type="month"].input-edit-success,
input[type="number"].input-edit-success,
input[type="password"].input-edit-success,
input[type="search"].input-edit-success,
input[type="tel"].input-edit-success,
input[type="text"].input-edit-success,
input[type="time"].input-edit-success,
input[type="url"].input-edit-success,
input[type="week"].input-edit-success,
select.input-edit-success,
textarea.input-edit-success {
  border: 1px solid #7fcc7f;
  box-shadow: 0px 0px 3px #7fcc7f;
}

input[type="color"].input-edit-error,
input[type="date"].input-edit-error,
input[type="datetime-local"].input-edit-error,
input[type="email"].input-edit-error,
input[type="month"].input-edit-error,
input[type="number"].input-edit-error,
input[type="password"].input-edit-error,
input[type="search"].input-edit-error,
input[type="tel"].input-edit-error,
input[type="text"].input-edit-error,
input[type="time"].input-edit-error,
input[type="url"].input-edit-error,
input[type="week"].input-edit-error,
select.input-edit-error,
textarea.input-edit-error {
  border: 1px solid #f99494;
  box-shadow: 0px 0px 3px #f99494;
}

input[type="color"]:disabled,
input[type="date"]:disabled,
input[type="datetime-local"]:disabled,
input[type="email"]:disabled,
input[type="month"]:disabled,
input[type="number"]:disabled,
input[type="password"]:disabled,
input[type="search"]:disabled,
input[type="tel"]:disabled,
input[type="text"]:disabled,
input[type="time"]:disabled,
input[type="url"]:disabled,
input[type="week"]:disabled,
select:disabled,
textarea:disabled {
  background-color: #E6E6E6;
}

input[type="color"]:-moz-read-only,
input[type="date"]:-moz-read-only,
input[type="datetime-local"]:-moz-read-only,
input[type="email"]:-moz-read-only,
input[type="month"]:-moz-read-only,
input[type="number"]:-moz-read-only,
input[type="password"]:-moz-read-only,
input[type="search"]:-moz-read-only,
input[type="tel"]:-moz-read-only,
input[type="text"]:-moz-read-only,
input[type="time"]:-moz-read-only,
input[type="url"]:-moz-read-only,
input[type="week"]:-moz-read-only,
select:-moz-read-only,
textarea:-moz-read-only {
  background-color: #f0f0f0;
}

input[type="color"]:read-only,
input[type="date"]:read-only,
input[type="datetime-local"]:read-only,
input[type="email"]:read-only,
input[type="month"]:read-only,
input[type="number"]:read-only,
input[type="password"]:read-only,
input[type="search"]:read-only,
input[type="tel"]:read-only,
input[type="text"]:read-only,
input[type="time"]:read-only,
input[type="url"]:read-only,
input[type="week"]:read-only,
select:read-only,
textarea:read-only {
  background-color: #f0f0f0;
}

dd input[type="color"],
dd input[type="date"],
dd input[type="datetime-local"],
dd input[type="email"],
dd input[type="month"],
dd input[type="password"],
dd input[type="search"],
dd input[type="tel"],
dd input[type="text"],
dd input[type="time"],
dd input[type="url"],
dd input[type="week"],
dd select,
dd textarea {
  width: auto !important;
}

#devis-logo-disposition, #devis-font-size, #inv-font-size {
  width: auto !important;
}

tr > td > input:not([type="submit"]):not([type="button"]):only-child,
tr > td > select:only-child {
  width: 100%;
}

label + input[type='text'], label + input[type='number'], dd select, input.small {
  vertical-align: middle !important;
}

/* #mdc-input:focus, #mdc-text-field__icon:focus, #ord-ref:focus {
 	border: none !important;
 	box-shadow: none !important;
 } */
input[type="file"]::-ms-value {
  background-color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  width: 100%;
  max-width: 400px;
  border: 1px solid #A9A9A9;
  border-radius: 6px;
  margin-top: 2px;
  margin-bottom: 2px;
  vertical-align: middle;
  padding: 0 4px;
}

input[type="file"]::-ms-value:hover {
  border: 1px solid #232E63;
}

input[type="file"]::-ms-value:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="file"]::-ms-value.input-edit-success {
  border: 1px solid #7fcc7f;
  box-shadow: 0px 0px 3px #7fcc7f;
}

input[type="file"]::-ms-value.input-edit-error {
  border: 1px solid #f99494;
  box-shadow: 0px 0px 3px #f99494;
}

input[type="file"]::-ms-value:read-only {
  background-color: white;
}

input[type="file"]::-ms-value:disabled {
  background-color: #E6E6E6;
}

input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not(.mdc-text-field__input), select:not([multiple]) {
  height: 24px;
}

input[type="file"]::-ms-value {
  height: 24px;
  box-sizing: border-box;
}

input[type="text"].date {
  min-width: auto;
}

/* Textarea */
textarea {
  padding: 4px;
  max-width: 100%;
}

/* Select */
select:-moz-read-only {
  background-color: white;
}
select:read-only {
  background-color: white;
}

select:disabled {
  background-color: #E6E6E6;
}

select:not([multiple]) {
  background-image: url("/admin/dist/images/input-select.svg");
  background-position: right -1px top -1px;
  background-repeat: no-repeat;
  background-size: 29px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding-right: 30px !important;
}

/* Boutons */
input[type="button"]:not(.cdt-grp-del),
input[type="reset"],
input[type="submit"],
button,
.button {
  display: inline-block;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  margin-top: 2px;
  margin-bottom: 2px;
  vertical-align: middle;
  min-height: 24px;
}

input[type="button"]:not(.cdt-grp-del):hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
button:hover,
.button:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none !important;
}

input[type="button"]:not(.cdt-grp-del):disabled,
input[type="reset"]:disabled,
input[type="submit"]:disabled,
button:disabled,
.button:disabled {
  cursor: default;
  background-color: #E6E6E6;
}

input[type="button"]:not(.cdt-grp-del):disabled:hover,
input[type="reset"]:disabled:hover,
input[type="submit"]:disabled:hover,
button:disabled:hover,
.button:disabled:hover {
  background-color: #E6E6E6;
  color: #232E63;
}

input[type="button"]:not(.cdt-grp-del):focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button:focus,
.button:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

/* Number */
input[type="number"] {
  width: 50px;
}

/* Checkbox, Radio */
input[type="checkbox"],
input[type="radio"] {
  background-color: #fff;
  border: 1px solid #A9A9A9;
  vertical-align: middle;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 14px !important;
  height: 14px;
  display: inline-block;
}

input[type="checkbox"]:-moz-read-only,
input[type="radio"]:-moz-read-only {
  background-color: white;
}

input[type="checkbox"]:read-only,
input[type="radio"]:read-only {
  background-color: white;
}

input[type="checkbox"]:disabled,
input[type="radio"]:disabled {
  background-color: #E6E6E6;
}

input[type="checkbox"]:hover,
input[type="radio"]:hover {
  border: 1px solid #232E63;
}

input[type="checkbox"]:focus,
input[type="radio"]:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="checkbox"] {
  border-radius: 3px;
}

input[type="checkbox"]:checked {
  background-image: url("/admin/dist/images/checkbox-checked.svg");
}

input[type="checkbox"]:indeterminate {
  background-clip: content-box;
  padding: 2px;
  background-color: #5377FB;
}

input[type="radio"] {
  border-radius: 100%;
}

input[type="radio"]:checked {
  background-image: radial-gradient(ellipse at center, #5377FB 50%, #fff 50%);
}

/* == Input file == */
/* Surcharge pour moteur de rendu Webkit, Blink */
input[type="file"]::-webkit-file-upload-button {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  -webkit-appearance: none;
          appearance: none;
  vertical-align: middle;
  min-height: 24px;
}

input[type="file"]::-webkit-file-upload-button:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none;
}

input[type="file"]::-webkit-file-upload-button:disabled {
  cursor: default;
  background-color: #E6E6E6;
}

input[type="file"]::-webkit-file-upload-button:disabled:hover {
  background-color: #E6E6E6;
  color: #232E63;
}

input[type="file"]::-webkit-file-upload-button:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

/* Surcharge pour moteur de rendu Edge */
input[type="file"]::-ms-browse {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  appearance: none;
  vertical-align: middle;
  min-height: 24px;
}

input[type="file"]::-ms-browse:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none;
}

input[type="file"]::-ms-browse[disabled] {
  cursor: default;
  background-color: #E6E6E6;
}

input[type="file"]::-ms-browse[disabled]:hover {
  background-color: #E6E6E6;
  color: #232E63;
}

input[type="file"]::-ms-browse:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

#site-content fieldset div input:not(.cdt-grp-del),
#site-content fieldset div textarea,
#site-content #tabpanel-vertical div input,
#site-content #tabpanel-vertical div textarea {
  display: inline-block;
}

label {
  vertical-align: middle;
}

/* pour les éléments de type label > input + texteLabel + input */
.nested-inputs {
  display: flex;
  align-items: center;
  /* & > input:first-child {
		flex: 1 1 auto;
	} */
}

@media (max-width: 767px) {
  .nested-inputs {
    display: inline-block;
  }
}

.nested-inputs > * {
  margin-right: 5px;
}

.nested-inputs > input:last-child {
  margin-left: 5px;
}

input.icon-del-cat {
  vertical-align: middle;
  margin-top: -3px;
}

.cdt-grp-legend label {
  display: inline-block;
}

.cdt-grp-legend input {
  display: inline-block !important;
}

#site-content .links .cnt-infos .del-link {
  float: left;
  height: 23px;
  width: 23px;
  margin-right: 5px;
}

/* Tabstrip */
.tabstrip {
  list-style-type: none;
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #232E63;
}

.tabstrip li {
  margin: 0;
  padding: 0;
  border-top: 1px solid #232E63;
  border-bottom: 1px solid #232E63;
  border: 1px solid #232E63;
  background-color: #fff;
  border-top-right-radius: 10px;
  position: relative;
  margin-right: -10px;
  overflow: hidden;
  margin-bottom: -1px;
}

.tabstrip li > * {
  border-style: none;
  background-color: transparent;
  margin: 0 !important;
  padding: 0 14px 0 24px;
  background-repeat: no-repeat;
  border-radius: 0;
  display: block;
  line-height: 24px;
  color: #232E63;
  width: 100%;
}

.tabstrip li > *:hover {
  background-color: #DADCFF;
  color: #232E63;
  text-decoration: none;
}

.tabstrip li > *.selected {
  text-decoration: none;
  background-color: #5377FB;
  color: #ffffff;
  cursor: default;
}

.tabstrip li > *.selected:focus {
  background-color: #5377FB;
  color: #ffffff;
}

.tabstrip li > *:focus {
  border: 0 none;
  background-color: #DADCFF;
}

.tabstrip li.selected {
  background-color: #5377FB;
}

.tabstrip li.selected > * {
  color: #ffffff;
}

.tabstrip li.selected > *:hover {
  background-color: #5377FB;
  text-decoration: none;
}

.tabstrip li.selected > *:focus {
  border: 0 none;
  background-color: #5377FB;
}

.tabstrip li:nth-child(1) {
  z-index: 29;
}

.tabstrip li:nth-child(2) {
  z-index: 28;
}

.tabstrip li:nth-child(3) {
  z-index: 27;
}

.tabstrip li:nth-child(4) {
  z-index: 26;
}

.tabstrip li:nth-child(5) {
  z-index: 25;
}

.tabstrip li:nth-child(6) {
  z-index: 24;
}

.tabstrip li:nth-child(7) {
  z-index: 23;
}

.tabstrip li:nth-child(8) {
  z-index: 22;
}

.tabstrip li:nth-child(9) {
  z-index: 21;
}

.tabstrip li:nth-child(10) {
  z-index: 20;
}

.tabstrip li:nth-child(11) {
  z-index: 19;
}

.tabstrip li:nth-child(12) {
  z-index: 18;
}

.tabstrip li:nth-child(13) {
  z-index: 17;
}

.tabstrip li:nth-child(14) {
  z-index: 16;
}

.tabstrip li:nth-child(15) {
  z-index: 15;
}

.tabstrip li:nth-child(16) {
  z-index: 14;
}

.tabstrip li:nth-child(17) {
  z-index: 13;
}

.tabstrip li:nth-child(18) {
  z-index: 12;
}

.tabstrip li:nth-child(19) {
  z-index: 11;
}

.tabstrip li:nth-child(20) {
  z-index: 10;
}

.tabstrip li:nth-child(21) {
  z-index: 9;
}

.tabstrip li:nth-child(22) {
  z-index: 8;
}

.tabstrip li:nth-child(23) {
  z-index: 7;
}

.tabstrip li:nth-child(24) {
  z-index: 6;
}

.tabstrip li:nth-child(25) {
  z-index: 5;
}

.tabstrip li:nth-child(26) {
  z-index: 4;
}

.tabstrip li:nth-child(27) {
  z-index: 3;
}

.tabstrip li:nth-child(28) {
  z-index: 2;
}

.tabstrip li:nth-child(29) {
  z-index: 1;
}

.tabstrip li:nth-child(30) {
  z-index: 0;
}

.tabstrip li:first-child > * {
  padding-left: 14px;
}

.tabstrip-vertical {
  list-style-type: none;
  margin: 0px;
  padding: 0px;
}

.tabstrip-vertical li {
  margin: 0px;
  padding: 0px;
  border-top: 1px solid #232E63;
  border-bottom: 1px solid #232E63;
  border: 1px solid #232E63;
  background-color: #fff;
  overflow: hidden;
  margin-bottom: -1px;
}

.tabstrip-vertical li:nth-child(1) {
  z-index: 29;
}

.tabstrip-vertical li:nth-child(2) {
  z-index: 28;
}

.tabstrip-vertical li:nth-child(3) {
  z-index: 27;
}

.tabstrip-vertical li:nth-child(4) {
  z-index: 26;
}

.tabstrip-vertical li:nth-child(5) {
  z-index: 25;
}

.tabstrip-vertical li:nth-child(6) {
  z-index: 24;
}

.tabstrip-vertical li:nth-child(7) {
  z-index: 23;
}

.tabstrip-vertical li:nth-child(8) {
  z-index: 22;
}

.tabstrip-vertical li:nth-child(9) {
  z-index: 21;
}

.tabstrip-vertical li:nth-child(10) {
  z-index: 20;
}

.tabstrip-vertical li:nth-child(11) {
  z-index: 19;
}

.tabstrip-vertical li:nth-child(12) {
  z-index: 18;
}

.tabstrip-vertical li:nth-child(13) {
  z-index: 17;
}

.tabstrip-vertical li:nth-child(14) {
  z-index: 16;
}

.tabstrip-vertical li:nth-child(15) {
  z-index: 15;
}

.tabstrip-vertical li:nth-child(16) {
  z-index: 14;
}

.tabstrip-vertical li:nth-child(17) {
  z-index: 13;
}

.tabstrip-vertical li:nth-child(18) {
  z-index: 12;
}

.tabstrip-vertical li:nth-child(19) {
  z-index: 11;
}

.tabstrip-vertical li:nth-child(20) {
  z-index: 10;
}

.tabstrip-vertical li:nth-child(21) {
  z-index: 9;
}

.tabstrip-vertical li:nth-child(22) {
  z-index: 8;
}

.tabstrip-vertical li:nth-child(23) {
  z-index: 7;
}

.tabstrip-vertical li:nth-child(24) {
  z-index: 6;
}

.tabstrip-vertical li:nth-child(25) {
  z-index: 5;
}

.tabstrip-vertical li:nth-child(26) {
  z-index: 4;
}

.tabstrip-vertical li:nth-child(27) {
  z-index: 3;
}

.tabstrip-vertical li:nth-child(28) {
  z-index: 2;
}

.tabstrip-vertical li:nth-child(29) {
  z-index: 1;
}

.tabstrip-vertical li:nth-child(30) {
  z-index: 0;
}

.tabstrip-vertical li > * {
  border-style: none;
  background-color: transparent;
  margin: 0 0 -1px !important;
  padding: 6px 10px;
  background-repeat: no-repeat;
  border-radius: 0;
  display: block;
  color: #232E63;
  width: 100%;
  height: auto !important;
  text-align: left;
  margin-bottom: -1px;
}

.tabstrip-vertical li > *:hover {
  background-color: #DADCFF;
  color: #232E63;
  text-decoration: none;
}

.tabstrip-vertical li > *.selected {
  text-decoration: none;
  background-color: #5377FB;
  color: #ffffff;
}

.tabstrip-vertical li > *:focus {
  border: 0 none;
  background-color: #DADCFF;
}

#tabstrip-vertical-container {
  min-width: 65%;
}

@media (min-width: 1024px) {
  #tabstrip-vertical-container {
    display: flex;
  }
}

#popup-content.tabs .tabstrip li:first-child {
  margin-left: 10px;
}

#popup-content.tabs .tabscontent {
  padding: 10px 15px;
}

#tabpanel {
  border: 1px solid #232E63;
  border-top-style: none;
  padding: 5px;
  clear: both;
}

#tabpanel-vertical {
  margin-left: -1px;
  border: 1px solid #232E63;
  padding: 12px 12px 12px 32px;
}

@media (max-width: 767px) {
  #tabpanel-vertical {
    padding: 12px;
  }
}

#site-content #tabpanel-vertical p {
  margin-bottom: 12px;
}

/* fieldsets */
#site-content #tabpanel-vertical div input.checkbox {
  width: auto;
}

#site-content #tabpanel-vertical div {
  padding: 3px;
}

#site-content #tabpanel-vertical div sub {
  padding-left: 163px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div sub {
    padding-left: 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  #site-content #tabpanel-vertical div sub {
    padding-left: 0;
  }
}

#site-content #tabpanel-vertical label,
#site-content #tabpanel-vertical div span.label {
  display: inline-block;
  width: 158px;
  text-align: right;
  padding-right: 5px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical label,
  #site-content #tabpanel-vertical div span.label {
    display: block;
    width: 100%;
    text-align: left;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  #site-content #tabpanel-vertical label,
  #site-content #tabpanel-vertical div span.label {
    text-align: left;
  }
}

#site-content #tabpanel-vertical div label.inline {
  display: inline;
  float: none;
}

#site-content #tabpanel-vertical div.checkbox {
  padding-left: 195px;
}

#site-content #tabpanel-vertical div input,
#site-content #tabpanel-vertical div textarea {
  width: 345px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div input,
  #site-content #tabpanel-vertical div textarea {
    width: 100%;
  }
}

#site-content #tabpanel-vertical div.actions input {
  width: auto;
}

#site-content #tabpanel-vertical textarea {
  display: block;
  width: 490px;
}

#site-content #tabpanel-vertical div.actions {
  padding-left: 163px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div.actions {
    padding-left: 0;
  }
}

/* Ensembles des messages d'information, warning, erreurs et retour d'événements. */
.error {
  color: red;
  background-color: #FFDDDD;
  border-color: red;
}

.notice {
  background-color: #FFFBCC;
  border-color: #E6DB55;
}

.notice-default {
  background-color: #FFFBCC;
  border-color: #E6DB55;
}

.success {
  color: green;
  background-color: #ddffdd;
  border-color: green;
}

.error-success {
  color: green;
  background-color: #C2FFBF;
  border-color: green;
}

.notice, .notice-default, .error, .error-success, .success {
  display: block;
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 5px;
  border-style: solid;
  border-width: 1px;
}

.notice > *:last-child, .notice-default > *:last-child, .error > *:last-child, .error-success > *:last-child, .success > *:last-child {
  margin-bottom: 0;
}

.notice b, .notice-default b, .error b, .error-success b, .success b {
  font-size: 600;
}

.notice label, .notice-default label, .error label, .error-success label, .success label {
  font-weight: 600;
}

.notice select, .notice-default select, .error select, .error-success select, .success select {
  width: 300px !important;
  height: 28px;
  margin-left: 45px;
}

.notice .more, .notice-default .more, .error .more, .error-success .more, .success .more {
  display: block;
  text-decoration: underline;
}

.notice .more-info,
.notice .more-hide, .notice-default .more-info,
.notice-default .more-hide, .error .more-info,
.error .more-hide, .error-success .more-info,
.error-success .more-hide, .success .more-info,
.success .more-hide {
  display: none;
}

.notice.inline-block, .notice-default.inline-block, .error.inline-block, .error-success.inline-block, .success.inline-block {
  display: inline-block;
  margin: 0;
}

.notice-msg {
  margin-top: 10px;
}

.notice-default p {
  text-align: left;
  margin-left: 165px;
}

table .error {
  color: #ff0000 !important;
  background-color: white !important;
  border-style: none;
  margin: 0;
  padding: 0 !important;
}

.load-ajax-opacity {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  background: black;
  opacity: 0.5;
  z-index: 9998;
  display: none;
}

.message-ajax-opacity {
  display: none;
  z-index: 9999;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des fichiers catalog/nomenclature.php et catalog/edit-nomenclature.php
 */
#table-nomenclatures-variables {
  width: 500px;
}

#table-nomenclatures-variables .td-with-checkbox {
  width: 25px;
}

#table-nomenclatures-variables .td-without-checkbox {
  width: 400px;
}

#table-propriete-emplacement #thname {
  width: 170px;
}

@media (max-width: 320px) {
  #table-propriete-emplacement #thname {
    font-size: 10px;
  }
}

#table-propriete-emplacement #td-input-name {
  width: 330px;
}

#table-produits-utilisables #td-produits-1 {
  width: 170px;
  vertical-align: bottom;
}

#table-produits-utilisables #td-produits-2 {
  width: 330px;
}

#table-produits-utilisables select {
  max-width: 500px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des fichiers ./catalog/discount/index.php 
 * et ./view.admin.inc.php (fonction view_state_periodpicker)
 */
/* sélecteur de période pour les tarifs */
.riapicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

/* Tableau de Tarifs conditionnels pour le catalogue */
#lst-prices #sync {
  width: 35px;
}

#lst-prices #information {
  width: 400px;
}

#lst-prices #conditions-prc {
  width: 650px;
}

#lst-prices #action-prc {
  width: 110px;
}

/* Tableau TVA */
#grps-tva {
  width: 760px;
}

#grps-tva #th-grps-tva-1 {
  width: 670px;
}

#grps-tva #th-grps-tva-2 {
  width: 90px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des fichiers ./catalog/authorizations/index.php 
 * et ./view.admin.inc.php (fonction view_websites_selector)
 */
/* sélecteur de période pour les tarifs */
.riawebsitepicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

#table-authorizations {
  margin-top: 10px;
}

#table-new-autorization #td-new-autorization {
  width: 150px;
}

#table-conditions-authorization {
  width: 100%;
  max-width: 800px;
}

#table-conditions-authorization #rec-fld {
  width: 305px;
}

#table-conditions-authorization #rec-symbol {
  width: 180px;
}

#table-conditions-authorization #rec-none {
  width: 20px;
}

#table-conditions-authorization .tr-rec-new-fld td.td-widthout-et select.rec-new-fld {
  width: 100% !important;
  margin-left: 0;
}

#table-conditions-authorization .tr-rec-new-fld td.td-width-et {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#table-conditions-authorization .tr-rec-new-fld td.td-width-et select {
  width: 270px !important;
}

#table-conditions-authorization .span-valeur {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#table-conditions-authorization .span-valeur .input-value-condition {
  width: 160px !important;
}

/* Droit d'accès */
#tb-authorization-edit {
  width: 100%;
  max-width: 100%;
  /* RESPONSIVE */
}

#tb-authorization-edit thead th:first-child {
  width: 20px;
}

#tb-authorization-edit thead th:nth-child(3) {
  width: 200px;
}

#tb-authorization-edit thead th:nth-child(4) {
  width: 180px;
}

#tb-authorization-edit thead th:nth-child(5) {
  width: 250px;
}

#tb-authorization-edit thead th:last-child {
  width: 180px;
}

#tb-authorization-edit td.td-liste-et {
  width: 30px;
}

#tb-authorization-edit td.td-widthout-et select.rec-new-fld {
  text-align: left;
  margin-left: 0;
}

#tb-authorization-edit td.td-width-et {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#tb-authorization-edit td.td-width-et .et {
  margin-right: 10px;
}

#tb-authorization-edit td.td-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#tb-authorization-edit td.td-value input[type=text] {
  width: auto !important;
}

#tb-authorization-edit td.action {
  padding: 10px !important;
}

#tb-authorization-edit td.action input.action {
  margin: 5px 5px 5px 0 !important;
}

@media (max-width: 1024px) {
  #tb-authorization-edit tr.dataID td.action {
    border-left: 0;
  }
  #tb-authorization-edit tr.dataID td.sync {
    border-right: 0;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit caption, #tb-authorization-edit tbody, #tb-authorization-edit thead, #tb-authorization-edit tfoot {
    width: 100%;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit tr {
    display: flex;
    flex-wrap: wrap;
  }
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des tableaux en général
 */
#site-content table, #popup-content table {
  border: 1px solid #A9A9A9;
  margin-bottom: 15px;
  clear: both;
  border-spacing: 0;
  border-collapse: collapse;
  min-width: 625px;
}

#site-content table #desc-long_parent table, #site-content table .toolBarGroup table, #popup-content table #desc-long_parent table, #popup-content table .toolBarGroup table {
  min-width: 0 !important;
}

#site-content table caption, #popup-content table caption {
  color: white;
  background-color: #232E63;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  white-space: nowrap;
}

#site-content table caption img, #popup-content table caption img {
  background-color: #fff;
  padding: 1px;
}

#site-content table td, #site-content table th, #site-content table caption, #site-content table tfoot, #popup-content table td, #popup-content table th, #popup-content table caption, #popup-content table tfoot {
  padding: 5px 0 5px 10px;
}

#site-content table thead:not(input), #popup-content table thead:not(input) {
  text-align: left;
}

#site-content table thead #select, #popup-content table thead #select {
  width: 25px;
}

#site-content table tbody th, #popup-content table tbody th {
  background: #DADCFF;
}

#site-content table tbody tr:last-child td, #popup-content table tbody tr:last-child td {
  border-bottom: none;
}

#site-content table tr, #popup-content table tr {
  vertical-align: top;
}

#site-content table td:last-child, #site-content table th:not(.th-customers):last-child, #popup-content table td:last-child, #popup-content table th:not(.th-customers):last-child {
  padding-right: 10px;
}

#site-content table tfoot, #popup-content table tfoot {
  border-top: 1px solid #A9A9A9;
  padding: 0;
}

#site-content table tfoot.bg-grey, #popup-content table tfoot.bg-grey {
  background-color: #E6E6E6;
}

#site-content table fieldset, #popup-content table fieldset {
  padding: 5px 10px !important;
  /* min-width: 590px; */
  max-width: 100%;
  /* padding: 10px; */
  margin-top: 10px;
  /*&:not(.cdt-grp) div.cdt-psy-val {
                max-width: 180px;
            }*/
}

#site-content table fieldset legend, #popup-content table fieldset legend {
  font-weight: bold;
  margin-left: 10px;
}

#site-content table fieldset input[type=text], #popup-content table fieldset input[type=text] {
  vertical-align: baseline !important;
}

#site-content table fieldset label, #popup-content table fieldset label {
  margin-right: 3px;
}

#site-content table fieldset div.cdt-config, #popup-content table fieldset div.cdt-config {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

#site-content table fieldset div.cdt-config .cdt-grp-list-cdt, #popup-content table fieldset div.cdt-config .cdt-grp-list-cdt {
  display: flex;
  flex-direction: column;
}

#site-content table fieldset div.cdt-config .cdt-grp-list-cdt .cdt-grp-message, #popup-content table fieldset div.cdt-config .cdt-grp-list-cdt .cdt-grp-message {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#site-content table fieldset div.cdt-config div.cdt-psy-val, #popup-content table fieldset div.cdt-config div.cdt-psy-val {
  flex: 1 1 auto;
  flex-direction: column;
  position: relative;
  margin-left: 5px;
  margin-right: 5px;
  display: none;
}

#site-content table fieldset div.cdt-config div.cdt-psy-val select.cdt-psy, #popup-content table fieldset div.cdt-config div.cdt-psy-val select.cdt-psy {
  width: 200px;
}

#site-content table fieldset div.cdt-config div.cdt-psy-val .cdt-icon-del, #site-content table fieldset div.cdt-config div.cdt-psy-val .cdt-grp-del, #site-content table fieldset div.cdt-config div.cdt-psy-val .input-icon-del, #popup-content table fieldset div.cdt-config div.cdt-psy-val .cdt-icon-del, #popup-content table fieldset div.cdt-config div.cdt-psy-val .cdt-grp-del, #popup-content table fieldset div.cdt-config div.cdt-psy-val .input-icon-del {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -8px;
  height: 23px !important;
  width: 23px !important;
}

#site-content table fieldset div.cdt-config div.cdt-psy-val .cdt-same-qte input, #popup-content table fieldset div.cdt-config div.cdt-psy-val .cdt-same-qte input {
  display: inline;
  margin-left: 0;
}

#site-content table fieldset div.cdt-config div.cdt-psy-val .cdt-calculated select.cdt-calculated-cdt, #popup-content table fieldset div.cdt-config div.cdt-psy-val .cdt-calculated select.cdt-calculated-cdt {
  margin-left: 5px;
}

#site-content table fieldset div.cdt-config .div-cdt-value, #popup-content table fieldset div.cdt-config .div-cdt-value {
  flex: 1 1 auto;
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  padding-right: 30px;
  position: relative !important;
}

#site-content table fieldset div.cdt-config .div-cdt-value input.input-icon-del, #popup-content table fieldset div.cdt-config .div-cdt-value input.input-icon-del {
  position: absolute !important;
  /* top: 50%;
                        margin-top: -8px; */
  right: 0;
  width: 23px !important;
}

#site-content table fieldset div.cdt-config .div-cdt-value .cdt-form-bool, #popup-content table fieldset div.cdt-config .div-cdt-value .cdt-form-bool {
  height: 100%;
  display: flex;
  align-items: center;
}

#site-content table fieldset div.cdt-config .cdt-select-second .div-website-cdt, #popup-content table fieldset div.cdt-config .cdt-select-second .div-website-cdt {
  display: flex;
  align-items: center;
}

#site-content table fieldset div.cdt-config .cdt-select-second .div-fields-cdt, #popup-content table fieldset div.cdt-config .cdt-select-second .div-fields-cdt {
  display: flex;
  align-items: center;
}

#site-content table fieldset div.cdt-config input.input-icon-del, #popup-content table fieldset div.cdt-config input.input-icon-del {
  position: absolute !important;
  top: 50%;
  margin-top: -8px;
  right: 0;
  width: 23px !important;
}

#site-content table fieldset .table-goupe-limites, #popup-content table fieldset .table-goupe-limites {
  width: 100%;
  border: none;
  margin-bottom: 0;
  padding: 5px 0 5px 10px;
  /* tbody {
                    vertical-align: top;
                    text-align: left;
                    tr:nth-child(2n+0){
                        background-color:$grey-color;
                    }
                    td.td-cdt-config {
                        display: flex;
                        justify-content: space-between;
                        .cdt-grp-cdt {
                            width: 230px !important;
                        }
                        .cdt-psy-val
                        {
                            display: inline;
                            label {
                                text-align: left !important;
                            }
                        }
                        .cdt-grp-del {
                            align-self: center;
                        }
                    }
                } */
}

#site-content table fieldset .table-goupe-limites tfoot, #popup-content table fieldset .table-goupe-limites tfoot {
  border-top: 1px solid #A9A9A9;
}

#site-content table:not(.tb-kpi) {
  /* RESPONSIVE */
}

#site-content table:not(.tb-kpi).w-600 {
  width: 600px;
}

@media (max-width: 1024px) {
  #site-content table:not(.tb-kpi).w-600 {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi) {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi) caption, #site-content table:not(.tb-kpi) tbody, #site-content table:not(.tb-kpi) thead, #site-content table:not(.tb-kpi) tfoot {
    width: 100%;
  }
}

#site-content table:not(.tb-kpi) caption {
  order: 0;
}

#site-content table:not(.tb-kpi) thead {
  order: 1;
}

#site-content table:not(.tb-kpi) tbody {
  order: 2;
}

#site-content table:not(.tb-kpi) tfoot {
  order: 3;
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi) tr {
    display: flex;
    flex-wrap: wrap;
  }
}

#popup-content table th, #popup-content table td {
  vertical-align: inherit;
}

/* Contient le titre de la table et les filtres/options/tri */
.table-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* working-shift */
#tb-day-exp tbody td {
  border-bottom: 1px solid #E6E6E6;
  height: 35px;
  vertical-align: middle;
}

#tb-day-exp tbody td.reinit {
  text-align: center;
}

#tb-day-exp tbody td.period {
  color: #232E63;
  font-size: 10px;
  text-align: center;
}

#tb-day-exp tbody td div.hr-active:hover {
  cursor: pointer;
}

#tb-day-exp div.hr-active,
#tb-day-exp div.hr-inactive {
  float: left;
  height: 25px;
  margin-left: 1px;
  width: 20px;
}

#tb-day-exp div.hr-inactive {
  background-image: url("/admin/images/expeditions/horaires_no.svg");
}

#tb-day-exp div.hr-active {
  background-image: url("/admin/images/expeditions/horaires_yes.svg");
}

#tb-day-exp input.hr-check {
  display: none;
}

.table-cols-changed {
  border: 1px solid #A9A9A9;
  margin-bottom: -2px;
  overflow-x: auto;
  overflow-y: hidden;
}

#tb-holidays #th-year th {
  background: #232E63;
  color: #fff;
  border-color: #232E63;
}

div.hr-inactive:hover {
  cursor: pointer;
}

#tb-redirection tbody img.edit-url, #tb-closing tbody img.edit-closing {
  border: medium none;
  cursor: pointer;
  width: 20px;
  margin-bottom: 3px;
}

#site-content .table-layout-large {
  /* RESPONSIVE */
}

@media (max-width: 1024px) {
  #site-content .table-layout-large {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table {
    display: table;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table caption, #site-content .table-layout-large table tbody, #site-content .table-layout-large table thead, #site-content .table-layout-large table tfoot {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table tr {
    display: table-row;
  }
}

@media (max-width: 1024px) {
  #site-content #tb-day-exp {
    width: 1060px;
  }
  #site-content #tb-holidays > tbody > tr {
    border-bottom: 1px solid #A9A9A9;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    flex-direction: column;
    border-bottom: none;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child label {
    padding: 3px;
    width: 100%;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child label input {
    margin-right: 5px;
    vertical-align: middle;
  }
}

@media (max-width: 1023px) {
  #site-content, #popup-content {
    /* .table-responsive th + td {
            padding-left: 10px;
        } */
  }
  #site-content table.table-responsive,
  #site-content table.table-responsive.checklist,
  #site-content table.table-responsive thead,
  #site-content table.table-responsive tbody,
  #site-content table.table-responsive tr,
  #site-content table.table-responsive th,
  #site-content table.table-responsive td, #popup-content table.table-responsive,
  #popup-content table.table-responsive.checklist,
  #popup-content table.table-responsive thead,
  #popup-content table.table-responsive tbody,
  #popup-content table.table-responsive tr,
  #popup-content table.table-responsive th,
  #popup-content table.table-responsive td {
    display: block;
  }
  #site-content table.table-responsive thead, #popup-content table.table-responsive thead {
    display: none;
  }
  #site-content table.table-responsive tfoot, #popup-content table.table-responsive tfoot {
    display: flex;
    flex-direction: column;
    padding: 5px;
  }
  #site-content table.table-responsive tfoot > tr:first-child, #popup-content table.table-responsive tfoot > tr:first-child {
    display: flex;
    flex-wrap: wrap;
  }
  #site-content table.table-responsive tbody td,
  #site-content table.table-responsive.checklist tbody td[headers="select"],
  #site-content table.table-responsive.checklist tbody td[headers="desc"],
  #site-content table.table-responsive.checklist tbody td[headers="ref"], #popup-content table.table-responsive tbody td,
  #popup-content table.table-responsive.checklist tbody td[headers="select"],
  #popup-content table.table-responsive.checklist tbody td[headers="desc"],
  #popup-content table.table-responsive.checklist tbody td[headers="ref"] {
    padding: 5px 5px 5px 150px;
    width: 100%;
    position: relative;
    margin-top: -1px;
    background: #fff;
  }
  #site-content table.table-responsive tbody td:nth-child(odd), #popup-content table.table-responsive tbody td:nth-child(odd) {
    background-color: #E6E6E6;
  }
  #site-content table.table-responsive tbody td::before, #popup-content table.table-responsive tbody td::before {
    padding: 10px;
    content: attr(data-label);
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    bottom: 0;
    background-color: #DADCFF;
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  #site-content table.table-responsive tbody tr, #popup-content table.table-responsive tbody tr {
    /* margin-bottom: 1rem; */
    border-top: 1px solid #A9A9A9;
  }
}

#site-content .authorizations tr.odd {
  background-color: #E6E6E6;
}

#site-content .authorizations tr.odd td {
  background: none;
}

#table-une-commande tr {
  vertical-align: top;
}

#fields-classe-objects {
  width: 625px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des fichiers customers/new.php (en popup dans Catalogue > Relevés linéaires > "un relevé" > onglet "Relations" > ajouter > Créer un nouveau compte)
 */
/* tableaux dans la popup */
#popup-content #td-new-user-1 {
  width: 215px;
}

#popup-content #td-new-user-2 {
  width: 360px;
}

#popup-content table#new-usr {
  width: 575px;
}

#popup-content table#new-usr tbody td {
  border-bottom: none;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des pages Clients > Tous les comptes
 */
/* tableau de synthèse des clients ./customers/index.php */
#table-synthese-order thead #hd-order-total {
  width: 130px;
}

#table-synthese-order thead #hd-order-prds {
  width: 160px;
}

#table-synthese-order thead #hd-order-ht, #table-synthese-order thead #hd-order-ttc {
  width: 100px;
}

#table-synthese-order thead #hd-order-margin {
  width: 150px;
}

#table-synthese-order thead #hd-order-avg-ht {
  width: 120px;
}

#table-synthese-order thead #hd-order-avg-ttc {
  width: 130px;
}

#table-synthese-order tbody img.loader {
  width: 20px;
  height: 20px;
}

/* tableau  de la liste des comptes dans ./customers/index.php */
#list-customers thead #usr-sel {
  width: 25px;
}

#list-customers thead .th-customers {
  width: 170px;
}

/* tableau des fiches clients, onglet général dans ./wiews/customers/tabs/general.php */
#table-fiche-client-general {
  width: 575px;
}

#table-fiche-client-general #td-fiche-client-general-1 {
  width: 215px;
}

#table-fiche-client-general #td-fiche-client-general-2 {
  width: 360px;
}

#table-fiche-client-general #usr-map {
  width: 100%;
  height: 275px;
  position: relative;
}

/* Pop-up créer un avoir */
#select-discount-type {
  width: auto !important;
}

/* tableau des fiches clients, onglet droits dans ./include/right.inc.php */
table#rights {
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
  border-spacing: 0px;
}

table#rights .th-rights {
  width: 50%;
}

table#rights ul {
  list-style-type: none;
  margin-left: 5px;
}

table#rights ul ul {
  margin: 5px 20px;
}

table#rights ul li {
  margin: 5px 0;
}

/* Comptes Clients, "Nom du client" > onglet "Adresses" */
#address-new #button-add-adresse {
  margin: 30px;
}

/* Comptes Clients, "Nom du client" > onglet "Adresses", "ajouter une adresse" : Tableau dans la popup ajouter une adresse */
#popup-content #table-fiche-client {
  width: 100%;
}

#popup-content #table-fiche-client tbody td {
  border-bottom: 0;
}

#popup-content #table-fiche-client tbody td #td-fiche-client-1 {
  width: 160px;
}

/* Comptes Clients, "Nom du client" > onglet "Disponibilité" */
#table-disponibilite #prd-sel {
  width: 20px;
}

#table-disponibilite #prd-ref {
  width: 100px;
}

#table-disponibilite #date {
  width: 150px;
}

#table-disponibilite #once {
  width: 200px;
}

#table-disponibilite #last {
  width: 120px;
}

/* Comptes Clients, "Nom du client" > onglet "Favoris" */
#list-whishlist #wishlist-del {
  width: 20px;
}

#list-whishlist #wishlist-name {
  width: 300px;
}

#list-whishlist #wishlist-publish {
  width: 80px;
}

#list-whishlist #wishlist-prds {
  width: 100px;
}

/* Tableau popup Ajouter une liste personnalisée */
#table-new-wishliste {
  width: 520px;
}

#table-new-wishliste #td-new-wishliste-1 {
  width: 150px;
}

#table-new-wishliste #td-new-wishliste-2 {
  width: 300px;
}

/* Comptes Clients, "Nom du client" > onglet "Relations" */
.hierarchies .checkall {
  width: 20px;
}

.hierarchies .code-client {
  width: 100px;
}

.hierarchies .societe {
  width: 120px;
}

.hierarchies .nom, .hierarchies .adresse, .hierarchies .complements {
  width: 150px;
}

.hierarchies .email {
  width: 200px;
}

/* Comptes Clients, "Nom du client" > onglet "Images"  */
#table-customers-images #td-customers-images-1 {
  width: 160px;
}

#table-customers-images #td-customers-images-2 {
  width: 330px;
}

#table-images-liees-mediatheque li.preview img {
  width: 100%;
  height: 100%;
}

/* Comptes Clients, "Nom du client" > onglet "Objectifs" */
#goals #td-goals-1 {
  width: 60px;
}

#goals #td-goals-2 {
  width: 500px;
}

#table-objectifs #goal-month {
  width: 100px;
}

#table-objectifs #goal-turnover-ord, #table-objectifs #goal-turnover, #table-objectifs #goal-turnover-inv, #table-objectifs #goal-margin-now {
  width: 150px;
}

#table-objectifs #goal-turnover-facture, #table-objectifs #goal-margin-goal {
  width: 200px;
}

#stats-rewards #del-rewards {
  width: 20px;
}

#stats-rewards #hdate {
  width: 100px;
}

#stats-rewards #husr {
  width: 290px;
}

#stats-rewards #hrwa {
  width: 300px;
}

#stats-rewards #hpts {
  width: 115px;
}

#stats-rewards #hconvert {
  width: 70px;
}

#stats-rewards #hlimit {
  width: 100px;
}

#stats-rewards #edit {
  width: 290px;
}

#documents,
#table-images {
  /* tfoot {
		border-top: 1px solid $medium-light-color;
	} */
}

#foot-products {
  width: 100%;
}

#editproduct #icon-del-cat {
  width: 16px;
}

/* Catégories */
/* Une catégorie > "edition" > onglet "place de marché" */
#tbl-export thead tr td:first-child {
  width: 180px;
}

#tbl-export thead tr td:last-child {
  width: 650px;
  max-width: 100%;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de Commandes 
 */
/* Tableau de Synthèse des commmandes */
#tb-synthese-order #hd-order-total {
  width: 90px;
}

#tb-synthese-order #hd-order-ht, #tb-synthese-order #hd-order-ttc {
  width: 180px;
}

#tb-synthese-order #hd-order-avg-ht, #tb-synthese-order #hd-order-avg-ttc {
  width: 120px;
}

#tb-synthese-order .loader {
  width: 20px;
  height: 20px;
}

/* Tableau de la liste des commandes */
#table-liste-commandes {
  width: 100%;
}

#table-liste-commandes #ord-id, #table-liste-commandes #ord-state {
  width: 100px;
}

#table-liste-commandes #ord-date, #table-liste-commandes #ord-ht, #table-liste-commandes #ord-ht {
  width: 120px;
}

#table-liste-commandes #lst_orders #td-ord-id {
  width: 200px;
}

#table-une-commande {
  width: 100%;
}

#table-une-commande #td-order-ref {
  width: 190px;
}

#table-une-commande #table-date-montant thead th {
  width: 150px;
}

#table-une-commande #table-adresse-livraison {
  width: 100%;
}

#table-une-commande #ord-addresses {
  width: 100%;
  margin: 0;
  border-style: none;
}

#table-une-commande #ord-addresses .th-user-adress {
  width: 170px;
}

#table-retours, #order-models {
  width: 100%;
}

#table-retours #ord-id, #order-models #ord-id {
  width: 50px;
}

#table-retours #ord-date, #order-models #ord-date {
  width: 200px;
}

#table-retours #ord-products, #table-retours #ord-ht, #table-retours #ord-ttc, #order-models #ord-products, #order-models #ord-ht, #order-models #ord-ttc {
  width: 160px;
}

.th-150 {
  width: 150px;
}

#table-champs-personnalises {
  width: 100%;
}

#table-adresse-livraison .padding-top-6 {
  padding-top: 6px !important;
}

#table-adresse-livraison .ord-prd-info {
  padding-top: 8px;
  padding-left: 3px;
}

#ord-products {
  width: 100%;
  margin: 0 !important;
  border-style: none !important;
}

#ord-products thead tr th {
  background-color: #f1f1f1 !important;
}

#ord-products thead tr th .th-ord-prod-20 {
  width: 20px;
}

#ord-products thead tr th .th-ord-prod-100 {
  width: 100px;
}

#ord-products thead tr th .th-ord-prod-125 {
  width: 125px;
}

#ord-products thead tr th #ord-pos {
  width: 40px;
}

#ord-prd-ref-name {
  width: 100%;
}

#ord-prd-ref-name #td-width-0 {
  width: 0;
}

#ord-prd-ref-name #td-prd-ref-name-1 {
  width: 125px;
  padding-top: 5px !important;
  word-break: break-all;
}

#ord-prd-ref-name #padding-top-5 {
  padding-top: 5px !important;
}

#ord-prd-ref-name .td-style {
  padding-left: 15px !important;
  padding-top: 6px !important;
  font-size: 10px;
}

#ord-prd-ref-name #td-style {
  padding-left: 15px !important;
  padding-top: 12px !important;
  font-size: 10px;
}

#ord-prd-ref-name .td-style-3 {
  padding-top: 6px;
}

#ord-prd-ref-name .td-style-4 {
  padding-top: 4px !important;
  padding-left: 3px;
}

.td-padding-0 {
  padding: 0;
}

/* Models */
#table-propriete-model {
  width: 100%;
}

#table-propriete-model #td-propr-model {
  width: 150px;
}

.table-articles {
  width: 100%;
}

.table-articles .th-art-w20 {
  width: 20px;
}

.table-articles .th-art-w150 {
  width: 150px;
}

.table-articles .th-art-w180 {
  width: 180px;
}

.table-articles #ord-pos {
  width: 40px;
}

#autorization {
  width: 100%;
}

#autorization .th-autor-mod-1 {
  width: 20px;
}

#autorization .th-autor-mod-2 {
  width: 200px;
}

/* Retours */
form#form-return #td-form-return-1 {
  width: 200px;
}

form#form-return #td-form-return-2 {
  width: 300px;
}

form#form-return #td-form-return-2 input {
  width: 150px;
}

#table-synthese-order #hd-order-total {
  width: 150px;
}

#table-synthese-order #hd-order-ht, #table-synthese-order #hd-order-ttc {
  width: 200px;
}

.list {
  width: 100%;
}

.list #ord-id, .list #ord-ht, .list #ord-ttc {
  width: 100px;
}

.list #ord-date, .list #ord-products {
  width: 150px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de la page Clients > Gestion des droits d'accès
 */
/* Tableau des Droits d'accès des comptes clients */
#table-profiles #prf-name, #table-profiles #prf-pl-name, #table-profiles #prf-users {
  width: 200px;
}

#table-propriete #td-propriete-1 {
  width: 135px;
}

#table-propriete #td-propriete-2 {
  width: 360px;
}

#table-infos-generales-profiles #td-infos-gen-prof-1 {
  width: 135px;
}

#table-infos-generales-profiles #td-infos-gen-prof-2 {
  width: 400px;
}

#prf-rights {
  width: 425px;
}

#prf-rights #td-prf-rights-1 {
  width: 25px;
}

#prf-rights #td-prf-rights-2 {
  width: 400px;
}

#prf-rights #td-prf-rights-3 {
  width: 425px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des tableaux des promotions
 */
/* Tableaux de Promotions > Promotions sur les produits */
/* Tableau Promotions */
#lst-pmt .information {
  width: 500px;
}

#lst-pmt .conditions-prc {
  width: 550px;
}

#lst-pmt .action {
  width: 105px;
}

form#pmt-search {
  margin-bottom: 10px;
}

form#pmt-search .left a {
  line-height: 35px;
}

#groupspromotionpicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

/* Tableau Nouvelle promotions */
form#frm-add-promo table#promo-info {
  width: 600px;
}

form#frm-add-promo table#promo-info .td-promo-info {
  width: 300px;
}

form#frm-add-promo table#promo-info .td-promo-info :first-child {
  float: left;
}

form#frm-add-promo table#promo-info tr#cdt-header, form#frm-add-promo table#promo-info tr td#cdts {
  display: none;
}

form#frm-add-promo div.notice {
  width: 595px;
  margin-bottom: 5px;
}

form#frm-add-promo table#tb-promo-remise thead tr th {
  width: 600px;
}

form#frm-add-promo table#tb-promo-remise td#grp-prd-promo {
  display: none;
}

form#frm-add-promo table#tb-promo-remise tfoot tr td input#add-prd-promo {
  float: left;
}

form#frm-add-promo table#tb-prd-promo #prd-ref {
  width: 300px;
}

form#frm-add-promo table#tb-prd-promo #prd-remise {
  width: 200px;
}

form#frm-add-promo table#tb-prd-promo #prd-remise-val {
  width: 100px;
}

form#frm-add-promo img.del {
  border: none;
  float: left;
  margin-right: 5px;
  margin-top: 3px;
  width: 10px;
  cursor: pointer;
}

/* codes promo */
table#table-codes-promo #pmt-select {
  width: 25px;
}

table#table-codes-promo #pmt-label {
  width: 120px;
}

table#table-codes-promo #pmt-code {
  width: 80px;
}

table#table-codes-promo #pmt-desc {
  width: 350px;
}

table#table-codes-promo #pmt-state {
  width: 145px;
}

table#table-codes-promo .opened {
  background-color: #ebffeb;
}

table#table-codes-promo .incoming {
  background-color: #ecebff;
}

table#pmt-special {
  width: 100%;
}

table#pmt-special tbody .td-pmt-spec {
  width: 95px;
}

table#pmt-special tbody th {
  background-color: #232E63;
  color: #fff;
}

table#pmt-special .cdt-grp-rule-items {
  width: auto !important;
}

table#tb-tabProducts, table#tb-tabCustomers {
  width: 500px;
}

div#tb-tabVariations table {
  width: 500px;
}

div#tb-tabVariations table th#var-del {
  width: 25px;
}

div#tb-tabVariations table th#var-code {
  width: 315px;
}

table.tb-tabStats {
  width: 775px;
}

table.tb-tabStats th:not(:first-child) {
  width: 90px;
}

#tb-tabSerie #pmt-select {
  width: 25px;
}

#tb-tabSerie #pmt-serie-etat, #tb-tabSerie #pmt-serie-cmd {
  width: 90px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de la page Clients > Segments 
 */
/* Tableau de Segmentation */
#obj-seg #seg-del {
  width: 25px;
}

#obj-seg #seg-name, #obj-seg #seg-desc, #obj-seg #seg-objects {
  width: 200px;
}

#obj-seg #type-pos {
  width: 75px;
}

/* Tableau de Création d'un nouveau Segmentation */
#table-creation-new-segment #td-creat-new-segment {
  width: 140px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de l'onglet configuration
 */
/* Information propriétaires */
table.w-600 tbody td:first-child {
  width: 175px;
}

@media (max-width: 1024px) {
  table.w-600 tbody td:first-child {
    width: auto;
  }
}

/* Contacts Propriétaire */
#table-config-contact #cnt-sel {
  width: 25px;
}

#table-config-contact #cnt-name {
  width: 250px;
}

#table-config-contact #cnt-email {
  width: 175px;
}

#table-config-contact #cnt-phone, #table-config-contact #cnt-fax {
  width: 115px;
}

#table-config-contact #cnt-types {
  width: 225px;
}

/* Types de contacts */
#table-config-type-contact {
  width: 650px;
}

#table-config-type-contact #type-sel {
  width: 25px;
}

#table-config-type-contact #type-name {
  width: 175px;
}

#table-config-type-contact #type-cnt {
  width: 100px;
}

#table-conf-contact #cnt-sel {
  width: 25px;
}

#table-conf-contact #cnt-name {
  width: 250px;
}

#table-conf-contact #cnt-email {
  width: 175px;
}

#table-conf-contact #cnt-phone, #table-conf-contact #cnt-fax {
  width: 115px;
}

#table-conf-contact #cnt-types {
  width: 225px;
}

/* Zones de livraison */
#table-config-zones #name, #table-config-services #name {
  width: 325px;
}

#table-config-zones #is-active, #table-config-services #is-active {
  width: 100px;
}

#tb-tabProducts {
  width: 500px;
}

/* Services de livraison */
#table-conf-livr-service-edit tbody tr th:first-child {
  width: 210px;
}

#table-conf-livr-service-edit tbody tr th:nth-child(2), #table-conf-livr-service-edit tbody tr th:nth-child(3) {
  width: 100px;
}

#table-conf-livr-service-edit tbody tr td.delais input.qte {
  width: 40px !important;
}

/* Magasins */
#list-stores #ref {
  width: 125px;
}

#list-stores #name {
  width: 225px;
}

#list-stores #address, #list-stores #contact {
  width: 275px;
}

#tb-day-exp #th-width-day-exp th {
  width: 125px;
}

#tb-day-exp #th-width-day-exp th:first-child {
  width: 95px;
}

#hr-magasin-stat {
  margin-top: 50px;
  margin-bottom: 50px;
}

#edit-employee tr:first-child td:first-child {
  width: 150px;
}

/* Services / postes */
#table-services thead th#cat-sel, #table-postes thead th#cat-sel {
  width: 25px;
}

#table-services thead th:nth-child(2), #table-postes thead th:nth-child(2) {
  width: 250px;
}

#table-services thead th:last-child, #table-postes thead th:last-child {
  width: 175px;
}

#table-services-edit tbody th:first-child td:first-child, #table-poste-edit tbody th:first-child td:first-child {
  width: 100px;
}

/* Dépôts */
#table-deposits thead th#cat-sel {
  width: 25px;
}

#table-deposits thead th#cat-name {
  width: 425px;
}

#table-deposits thead th#cat-principal {
  width: 100px;
}

#table-services-edit tbody th:first-child td:first-child, #table-poste-edit tbody th:first-child td:first-child, #table-fiche-classe tbody th:first-child td:first-child {
  width: 100px;
}

/* Gestion des retours */
#table-reasons thead th:first-child {
  width: 25px;
}

#table-reasons thead th#reason-name {
  width: 525px;
}

#table-reasons thead th#cat-publish {
  width: 50px;
}

/* Structure des données > classes */
#table-config-classes #cls-check {
  width: 25px;
}

#table-config-classes #cls-name {
  width: 300px;
}

#table-config-classes #cls-nb-fld {
  width: 100px;
}

#table-config-classes #cls-nb-obj {
  width: 125px;
}

#table-fiche-classe tbody td:first-child td:last-child, #fields-classe-object tbody td:first-child td:last-child {
  width: 200px;
}

#table-fiche-classe tbody td #parent-name, #fields-classe-object tbody td #parent-name {
  width: 311px;
}

#fields-classe-objects #obj-check {
  width: 25px;
}

#fields-classe-objects #obj-name {
  width: 220px;
}

#fields-classe-objects #obj-childs {
  width: 105px;
}

/* Models */
#models #name {
  width: 325px;
}

#models #fields, #models #objects {
  width: 125px;
}

#fields #fld-select {
  width: 25px;
}

#fields #fld-name, #fields #fld-type, #fields #fld-cat {
  width: 275px;
}

#fields #fld-pos {
  width: 100px;
}

#typing-template-tab #name {
  width: 225px;
}

/* Champs Presonnalisés */
#table-champs-personnalises {
  width: 925px;
}

#table-champs-personnalises thead tr th {
  width: 300px;
}

/* Champs Presonnalisés / Unités de mesure */
#categories #name, #table-units #name {
  width: 325px;
}

#categories #pos, #categories #symbol, #table-units #pos, #table-units #symbol {
  width: 75px;
}

#categories tbody td:last-child, #table-units tbody td:last-child {
  text-align: center;
}

/* Segments */
#obj-seg thead tr th {
  width: 200px;
}

#obj-seg thead tr th:first-child {
  width: 25px;
}

#obj-seg thead tr th:last-child {
  width: 75px;
}

#form-segment tbody tr:first-child td:first-child {
  width: 140px;
}

/* CGV */
#table-cgv #cgv-sel {
  width: 25px;
}

#table-cgv #cgv-name {
  width: 450px;
}

#table-cgv #cgv-pub {
  width: 175px;
}

#table-cgv .current {
  font-weight: bold;
}

#table-fiche-version {
  width: 520px;
}

#cgv {
  width: 520px !important;
}

#cgv #art-sel {
  width: 25px;
}

#cgv .td-art-name {
  width: 450px;
}

#cgv .td-art-move {
  width: 50px;
}

#table-cgv-edit-article tbody tr:first-child td:first-child {
  width: 150px;
}

/* Paramètres de tarification */
#table-param-cat-tarifaires #prc-sel, #table-param-cat-tarifaires #prd-sel, #table-produits-exclure #prc-sel, #table-produits-exclure #prd-sel {
  width: 25px;
}

#table-param-cat-tarifaires #prc-name, #table-param-cat-tarifaires #prd-name, #table-produits-exclure #prc-name, #table-produits-exclure #prd-name {
  width: 400px;
}

#table-param-cat-tarifaires #prc-ttc, #table-produits-exclure #prc-ttc {
  width: 75px;
}

#table-param-cat-tarifaires #prc-users, #table-produits-exclure #prc-users {
  width: 100px;
}

#table-produits-exclure {
  width: 625px;
}

#table-cat-tarifaire-site td {
  width: 200px;
}

.prc-default-options {
  margin-top: 12px;
  margin-bottom: 12px;
}

#prc-default {
  margin-top: 12px;
}

#table-type-relations #rel-type-sel, #table-type-relations td:first-child {
  width: 25px;
}

#table-type-relations #rel-type-nom {
  width: 250px;
}

#table-type-relations #rel-type-nom-pluriel {
  width: 100px;
}

/* Adresses emails */
/* Catalogue */
#table-config-type-relation .tdleft {
  width: 145px;
}

/* Référencement */
#form-referencement-site #table-conf-referencement tbody tr:first-child th:first-child {
  width: 150px;
}

#form-referencement-site #table-conf-referencement tbody tr:first-child th:last-child {
  width: 300px;
}

#form-referencement-site .label-option {
  margin-right: 10px;
}

#form-referencement-site #custom-p {
  margin-top: 30px;
}

#conf-stats thead th:first-child {
  width: 20px;
}

#conf-stats thead th:last-child {
  width: 400px;
}

#table-edit-personnalisation tbody tr:last-child td {
  width: 200px;
}

#table-edit-personnalisation tbody tr:last-child td:first-child {
  width: 200px;
}

#class-select label, #tag-select label {
  width: 130px;
  display: inline-flex;
}

#edition-table {
  width: 705px;
  max-width: 100%;
}

#edition-table tbody {
  padding-right: 10px;
}

#edition-table tbody td {
  max-width: 100%;
}

#edition-table tbody td select#constant-selector {
  height: 100% !important;
  background-image: none !important;
  overflow: hidden;
  width: 200px !important;
}

#edition-table tbody td #sentence {
  width: 400px;
}

#edition-table tbody td #save-column {
  width: 50px;
}

#edition-table tbody td:last-child {
  width: 200px;
}

#sentences-table {
  margin-top: 10px;
}

#sentences-table tr {
  border-collapse: collapse;
}

#sentences-table tr td.sentence-text {
  width: 600px;
  border-right: 1px solid #CCCCCC;
  padding: 10px;
  vertical-align: middle;
}

@media (max-width: 1023px) {
  #sentences-table tr td.sentence-text {
    border-right: 0;
  }
}

#sentences-table tr td.td-action {
  width: 104px;
  text-align: center;
}

@media (max-width: 1023px) {
  #sentences-table tr:last-child {
    border-bottom: 0;
  }
}

/* Redirections */
#lst-error-404 #url {
  width: 325px;
}

#lst-error-404 #count {
  width: 95px;
}

#lst-error-404 #site {
  width: 120px;
}

#lst-error-404 #lng {
  width: 95px;
}

/* Horaires d'expéditions */
.tb-day-exp1 thead tr th#day-exp {
  width: 75px;
}

.tb-day-exp1 thead tr th#period-exp {
  width: 220px;
}

.tb-day-exp1 thead tr th#action-exp {
  width: 80px;
}

#tb-holidays thead tr#th-year th {
  width: 200px;
}

#tb-closing #clg-start, #tb-closing #clg-end {
  width: 130px;
}

#tb-closing #clg-action {
  width: 20px;
}

/* Traduction */
#div-orange {
  margin-bottom: 10px;
  padding-bottom: 5px;
}

#tb-translate #tsk_checked {
  width: 20px;
}

#tb-translate #tsl_context {
  width: 50px;
}

#tb-translate #tsl_original, #tb-translate #tsl_translate {
  width: 250px;
}

#tb-translate #action {
  width: 20px;
}

/* modification des inputs trop grands */
dd input.col-numeric, input#min_amount, input#days_valid, select#apply_on {
  width: auto;
}

/* Règlements par virement */
#tb-transfer tbody#transfert-positif tr:first-child td:first-child {
  width: 100px;
}

#tb-transfer tbody#transfert-positif tr:first-child td:nth-child(2) {
  width: 500px;
}

#tb-transfer tbody #trf-delete {
  width: 20px;
}

#tb-transfer tbody #trf-describe {
  width: 400px;
}

/* Adresses IP Filtrées */
#tb-ip-filtrees #flt-check {
  width: 25px;
}

#tb-ip-filtrees #flt-name {
  width: 300px;
}

#tnt-filters tbody tr:first-child td:first-child {
  width: 150px;
}

#tnt-filters tbody tr:first-child td:nth-child(2) {
  width: 300px;
}

/* Cartes cadeaux */
#list-prd #check-del {
  width: 28px;
}

#list-prd #prd-title {
  width: 475px;
}

#list-prd #prd-amount {
  width: 100px;
}

/* Campagnes */
#table-dob tr.details td:first-child {
  width: 230px;
}

#table-dob tr.details td:nth-child(2) {
  width: 400px;
}

#table-reminder-catalog tr.details td:first-child {
  width: 170px;
}

#table-reminder-catalog tr.details td:nth-child(2) {
  width: 200px;
}

/* Instagram */
#formulaire_identifiants_instagram label {
  width: 135px;
  display: inline-block;
}

#formulaire_identifiants_instagram input {
  width: 170px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de la catégorie Yuto
 */
/* Gestion de parc / Flotte d'appareils */
#tb-liste-fabricant #dev-checked {
  width: 25px;
}

#tb-liste-fabricant #dev-key {
  width: 355px;
}

#tb-liste-fabricant #dev-version {
  width: 75px;
}

#tb-liste-fabricant #dev-user {
  width: 200px;
}

#tb-liste-fabricant #dev-last-sync {
  width: 150px;
}

#tb-liste-fabricant #dev-last-location {
  width: 160px;
}

#tb-exec-sql .label {
  width: 125px;
}

.bg-pink {
  background-color: #ffe5e5;
}

/* Horaires d'activité */
.tb-day-exp2 thead th {
  width: 95px;
}

.tb-day-exp2 thead th#day-exp {
  width: 75px;
}

.tb-day-exp2 thead th#period-exp {
  width: 220px;
}

.tb-day-exp2 thead th#action-exp {
  width: 180px !important;
}

#form-holidays thead th {
  width: 200px;
}

#tb-closing #clg-start, #tb-closing #clg-end {
  width: 170px;
}

#tb-closing #clg-action {
  width: 20px;
}

#yuto-notifications thead th:not(:first-child) {
  width: 175px;
}

/* Rapports d'appels */
#calls-report #reports-created {
  width: 250px;
}

#calls-report #reports-author, #calls-report #reports-dest {
  width: 300px;
}

/* Rapports de visites */
#tb-type-rapports thead th {
  width: 350px;
}

/* Rapports Présentation des produits */
#tb-reports-pres-produits thead th {
  width: 275px;
}

#tb-reports-pres-produits thead th#reports-id {
  width: 175px;
}

#tb-reports-pres-produits thead th#reports-created {
  width: 150px;
}

/* Chiffre d'affaires */
#tb-total-ca thead th {
  width: 150px;
}

#tb-total-ca thead th:first-child {
  width: 200px;
}

/* Temps de visite */
#tb-moyenne-rdv #avg_duration {
  width: 525px;
}

#tb-moyenne-rdv #avg_revenue, #tb-moyenne-rdv #avg_yield {
  width: 200px;
}

#t_spent_time thead th {
  width: 200px;
}

#t_spent_time thead th:first-child {
  width: 325px;
}

/* Palmarès */
#notif p {
  font-size: 12px;
  font-weight: bold;
  color: #666666;
  fill: #666666;
  margin-top: 100px;
  text-align: center;
}

.tb-kpi {
  border-collapse: collapse;
  display: inline-table;
  clear: none;
  margin-right: 15px;
}

@media (min-width: 768px) {
  .tb-kpi {
    width: 340px;
  }
}

@media (max-width: 768px) {
  .tb-kpi {
    width: 300px;
  }
}

.tb-kpi caption {
  box-sizing: border-box;
}

.tb-kpi tr td {
  border-bottom: 1px solid #ccc;
}

.tb-kpi tr.show-seller {
  background-color: lightgrey;
}

.tb-kpi tr.show-seller:hover {
  background-color: #C7C7C7;
}

.tb-kpi tr.hidden-seller {
  display: none;
}

.tb-kpi tfoot a {
  display: block;
}

/* Configuration */
#tb-ovr-usr {
  margin-top: 10px;
  width: 850px;
}

#tb-ovr-usr #cls-check, #tb-ovr-usr #cls-nb-fld {
  width: 250px;
}

#tb-ovr-usr #cls-name {
  width: 350px;
}

#tb-ovr-usr .bg-green-color {
  background-color: #ebffeb;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de la catégirie Comparateurs
 */
#tb-ctr-cat thead tr th, #tb-ctr-prd thead tr th {
  width: 100px;
}

#tb-ctr-cat thead tr th#cat, #tb-ctr-prd thead tr th#cat {
  width: 200px;
}

#tb-ctr-cat thead tr th#cat-margin, #tb-ctr-cat thead tr th#prd-margin, #tb-ctr-prd thead tr th#cat-margin, #tb-ctr-prd thead tr th#prd-margin {
  width: 104px;
}

#tb-ctr-cat thead tr th#cat-roi, #tb-ctr-cat thead tr th#prd-roi, #tb-ctr-prd thead tr th#cat-roi, #tb-ctr-prd thead tr th#prd-roi {
  width: 84px;
}

#tb-ctr-cat thead tr th#prd-check, #tb-ctr-prd thead tr th#prd-check {
  width: 25px;
}

#tb-ctr-cat thead tr th#prd, #tb-ctr-prd thead tr th#prd {
  width: auto;
}

#tb-ctr-cat thead tr th#prd-export, #tb-ctr-prd thead tr th#prd-export {
  width: 75px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de l'onglet Statistiques
 */
/* Meilleures ventes */
#bestseller-prd #best-ref {
  width: 190px;
}

#bestseller-prd #best-orders {
  width: 75px;
}

#bestseller-prd #best-by-ord {
  width: 130px;
}

#bestseller-prd #best-ca, #bestseller-prd #best-ca-ord {
  width: 120px;
}

/* Ventes par catégories*/
#bestseller-bycat #best-orders {
  width: 75px;
}

#bestseller-bycat #best-by-ord {
  width: 145px;
}

#bestseller-bycat #best-ca, #bestseller-bycat #best-ca-ord {
  width: 120px;
}

/* Fidélité */
.fidelity #val_last_year, .fidelity #val_current_year {
  width: 150px;
}

/* Recherches */
#table-synthese-search thead th {
  width: 145px;
}

#table_search #volume, #table_search #nbres, #table_search #avg {
  width: 100px;
}

#table_search #ctr {
  width: 120px;
}

#table_search #website {
  width: 130px;
}

/* Redirections */
#table_search {
  width: 100%;
}

#table_search .td-check {
  width: 25px;
}

#table_search #types {
  width: 85px;
}

#table_search #emp {
  width: 280px;
}

#table_search #website {
  width: 130px;
}

#table_search input.w80 {
  width: 80%;
}

/* Suggestions de recherche */
#table-suggest-search #active {
  width: 25px;
}

#table-suggest-search #results {
  width: 100px;
}

#table-suggest-search #search {
  width: 350px;
}

#table-suggest-search #site {
  width: 100px;
}

/* Poids des produits */
.prd-weight thead tr:last-child th {
  width: 100px;
}

.prd-weight thead tr:last-child th#w-cat, .prd-weight thead tr:last-child th#wn-cat {
  width: 350px;
}

#prd-completing #desc {
  width: 450px;
}

/* Produits sans images */
#form-no-image {
  padding-top: 10px;
}

#form-no-image ul.tabstrip {
  margin-top: 10px;
}

#prd-no-image {
  width: 100%;
}

#prd-no-image thead th#prd-taux {
  width: 160px;
}

#prd-no-image thead th:last-child {
  text-align: right;
}

.div-padding-10 {
  padding: 10px;
}

#prd-conversion #w-ref {
  width: 50px;
}

#prd-conversion #w-name {
  width: 320px;
}

#prd-conversion #w-conversion {
  width: 200px;
}

#prd-conversion #w-margin_rate {
  width: 160px;
}

#prd-conversion #w-price_sell, #prd-conversion #w-price_purchase {
  width: 130px;
}

span#tooltip-shiftKey {
  margin-bottom: 10px;
}

span#tooltip-shiftKey span#t-s-1 {
  float: left;
  vertical-align: middle;
}

span#tooltip-shiftKey span#t-s-1 img {
  vertical-align: middle;
  border: 0 none;
  width: 48px;
  height: 48px;
}

span#t-s-2 {
  font-weight: bold;
  font-size: 40px;
  vertical-align: bottom;
}

.tb-stat-ref thead tr th {
  width: 35%;
}

.tb-stat-ref thead tr th:first-child {
  width: 30%;
}

/* Balises Meta Description en double */
#stat-tag-double {
  width: 870px;
}

#stat-tag-double #tag-descript {
  width: 240px;
}

#stat-tag-double #tag-object {
  width: 628px;
}

/* Veille tarifaire */
#stats-price-watching th {
  width: 30px;
}

#stats-price-watching th:first-child {
  width: 50px;
}

#stats-price-watching th:nth-child(2) {
  width: 300px;
}

#stats-price-watching th:nth-child(3) {
  width: 100px;
}

#stats-price-watching th:nth-child(4), #stats-price-watching th:nth-child(5) {
  width: 80px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de la page Outils
 */
form #table-tools-news {
  width: 830px;
}

form #table-tools-news #news-sel {
  width: 25px;
}

form #table-tools-news #news-name {
  width: 425px;
}

form #table-tools-news #news-pub-date {
  width: 75px;
}

form #table-tools-news #news-pub {
  width: 125px;
}

form #table-tools-news #news-pos {
  width: 180px;
}

#actu_reviews {
  width: 648px !important;
}

#actu_reviews .th-actu-rev-20 {
  width: 20px;
}

#actu_reviews .th-actu-rev-584 {
  width: 584px;
}

#actu_reviews .th-actu-rev-75 {
  width: 80px;
}

#table-categories-actualites {
  width: 600px;
}

#table-categories-actualites #cat-sel {
  width: 25px;
}

#table-categories-actualites #cat-name {
  width: 425px;
}

#table-categories-actualites #news-count {
  width: 70px;
}

#class thead th:first-child {
  width: 25px;
}

#class thead #name, #class thead #nb-suscribers {
  width: 200px;
}

#class thead #type {
  width: 250px;
}

#table-list-newsletter #sel {
  width: 25px;
}

#table-list-newsletter #email-th, #table-list-newsletter #tel-th {
  width: 250px;
}

#table-list-newsletter #state {
  width: 390px;
}

#table-list-newsletter #account, #table-list-newsletter #cat {
  width: 125px;
}

#table-synthese-order #hd-rewards-total, #table-synthese-order #hd-rewards-used {
  width: 150px;
}

#table-synthese-order #hd-rewards-no-used {
  width: 180px;
}

/* Campagne marketing */
#table-campagnes-sms #cpg-id {
  width: 25px;
}

#table-campagnes-sms #cpg-start, #table-campagnes-sms #cpg-end, #table-campagnes-sms #cpg-send {
  width: 150px;
}

#table-stats-details-messages {
  width: 600px;
}

#table-stats-details-messages #hphone, #table-stats-details-messages #hdate {
  width: 150px;
}

#table-stats-details-messages #husr {
  width: 290px;
}

/* Alertes de disponibilités */
#tb-alert-dispo #sel {
  width: 25px;
}

#tb-alert-dispo #prd-name, #tb-alert-dispo #brd-name {
  width: auto;
}

#tb-alert-dispo #account, #tb-alert-dispo #last_date, #tb-alert-dispo #date, #tb-alert-dispo #restocking {
  width: 125px;
}

/* FAQ */
#faq_categories #faq-sel {
  width: 25px;
}

#faq_categories #faq-name {
  width: 400px;
}

#faq_categories #faq-qst {
  width: 50px;
}

#faq_categories #faq-qst-pub {
  width: 150px;
}

#table-faq-categorie-general {
  width: 100%;
}

#table-faq-categorie-general tbody tr:first-child td:first-child {
  width: 215px;
}

#faq_questions #qst-sel {
  width: 25px;
}

#faq_questions #qst-name {
  width: 400px;
}

#faq_questions #qst-pub {
  width: 60px;
}

/* Bannières et Zones d'actions */
#table-banners #bnr-sel, #table-zones-actions #bnr-sel {
  width: 25px;
}

#table-banners #bnr-name, #table-zones-actions #bnr-name {
  width: 325px;
}

#table-banners #bnr-url, #table-zones-actions #bnr-url {
  width: 225px;
}

#table-banners #bnr-from, #table-banners #bnr-to, #table-banners #bnr-emp, #table-zones-actions #bnr-from, #table-zones-actions #bnr-to, #table-zones-actions #bnr-emp {
  width: 125px;
}

#table-banners #bnr-pos, #table-zones-actions #bnr-pos {
  width: 175px;
}

#table-banners .bg-color-green, #table-zones-actions .bg-color-green {
  background-color: #ebffeb;
}

#table-banners .bg-color-blue, #table-zones-actions .bg-color-blue {
  background-color: #ecebff;
}

#table-modif-banners tbody td:first-child, #table-zones-edit tbody td:first-child {
  width: 130px;
}

#table-zones-edit input[type=text]#date_from, #table-zones-edit input[type=text]#date_to, #table-zones-edit select#plc {
  width: auto !important;
}

/* Gestion de contenu */
div#name-contenu {
  border-bottom: solid 1px #c0c0c0;
  width: 530px;
  margin-bottom: 10px;
}

div#name-contenu h3 {
  display: inline;
  border: none;
}

div#name-contenu div.ria-cell-move {
  float: right;
}

div#name-contenu div:last-child {
  clear: right;
}

#table-gestion-contenu thead th:first-child, #cms-categories thead th:first-child {
  width: 25px;
}

#table-gestion-contenu thead th:nth-child(2), #cms-categories thead th:nth-child(2) {
  width: 400px;
}

#table-gestion-contenu thead th:nth-child(3), #cms-categories thead th:nth-child(3) {
  width: 75px;
}

/* CMS, glossaires, configuration de l'import */
#editcms td.tdw-150, #table-glossary-def td.tdw-150, #table-glossary-edit td.tdw-150, #mapping-general td.tdw-150 {
  width: 150px;
}

#tb-redirection #url {
  width: 705px;
}

#tb-redirection #action {
  width: 50px;
}

#tb-redirection img.edit-url {
  width: 25px;
  height: 25px;
}

#div-select-type-modif {
  width: 775px;
  padding-bottom: 5px;
}

#div-select-type-modif select {
  float: right;
}

#div-select-type-modif div:last-child {
  clear: right;
}

#table-liste-versions #cms-ver-sel {
  width: 25px;
}

#table-liste-versions #cms-ver-date {
  width: 350px;
}

#table-liste-versions #cms-ver-type {
  width: 175px;
}

#table-liste-versions #cms-ver-user {
  width: 100px;
}

#table-liste-versions #cms-ver-up {
  width: 125px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:first-child {
  width: 25px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(2) {
  width: 375px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(3) {
  width: 150px;
  text-align: center;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(4) {
  width: 100px;
  text-align: center;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:last-child {
  width: 125px;
  text-align: center;
}

#table-liste-versions tfoot tr#pagination td:first-child {
  text-align: left;
}

div.rev-form #table-type-modif {
  float: right;
}

div.rev-form #table-type-modif #label-vertical-align {
  vertical-align: top;
}

div.rev-form div:last-child {
  clear: right;
}

#footer-no-npad {
  padding: 5px 10px;
}

/* Erratums */
#table-erratums {
  width: 800px;
}

#table-erratums #err-sel {
  width: 25px;
}

#table-erratums #err-ref {
  width: 50px;
}

#table-erratums #err-name {
  width: 225px;
}

#table-erratums #err-desc {
  width: 325px;
}

#table-erratums #err-date {
  width: 100px;
}

/* Glossaires */
#table-glossaire {
  width: 90%;
}

#table-glossaire th:first-child {
  width: 25px;
}

#table-glossaire #name, #table-glossaire #name_pl {
  width: 145px;
}

#table-glossaire #etat {
  width: 55px;
}

#table-glossaire #date {
  width: 150px;
}

#table-glossaire tfoot td input.float-left {
  margin-right: 3px;
}

/* Outils > Parrainage / Points de fidélité > Configuration */
#site-content form#form-rewards ul.tabstrip li:first-child {
  width: 78px !important;
}

#tb-tabConfig tbody td:first-child {
  width: 275px;
}

#tb-tabConfig tbody td:last-child {
  width: 495px;
}

#tb-tabActions #actions {
  width: 250px;
}

#tb-tabActions #desc {
  width: 400px;
}

#tb-tabActions #rwa-params {
  width: 275px;
}

#tb-tabSponsors tr.tr-filleul-reduc td:first-child {
  width: 210px;
}

#tb-tabSponsors tr.tr-filleul-reduc td:last-child {
  width: 400px;
}

/* Statistiques */
#stats-rewards {
  margin-top: 10px;
}

#stats-rewards #hdate, #stats-rewards #hlimit {
  width: 100px;
}

#stats-rewards #husr {
  width: 290px;
}

#stats-rewards #hrwa {
  width: 300px;
}

#stats-rewards #hpts {
  width: 115px;
}

#stats-rewards #hconvert {
  width: 70px;
}

/* Imports */
#table-imports thead th:first-child {
  width: 25px;
}

#table-imports thead th:nth-child(2) {
  width: 300px;
}

#table-imports thead th:nth-child(3) {
  width: 100px;
}

#table-imports thead th:nth-child(4), #table-imports thead th:nth-child(6) {
  width: 200px;
}

#table-imports thead th:nth-child(5) {
  width: 150px;
}

/* Imports */
#table-map-file thead th:nth-child(-n+2) {
  width: 300px;
}

#imp-form {
  width: 100%;
  max-width: 100% !important;
  table-layout: fixed;
}

@media (max-width: 768px) {
  #imp-form tr {
    display: flex;
    flex-direction: column;
  }
}

#imp-form tr th.header > * {
  display: block;
  position: relative;
  padding-right: 14px;
}

#imp-form tr th.header > *::after {
  position: absolute;
  right: 3px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

#imp-form tr th.header.th-proprietaire .tablesorter-header-inner {
  min-width: 95px;
}

#imp-form tr th.header.th-execution .tablesorter-header-inner {
  min-width: 100px;
}

#imp-form tr th.header.th-etat .tablesorter-header-inner {
  min-width: 90px;
}

#imp-form tr input[type="text"]:-moz-read-only {
  background-color: transparent;
  border: 0;
}

#imp-form tr input[type="text"]:read-only {
  background-color: transparent;
  border: 0;
}

#imp-form tr input[type="text"]:-moz-read-only:focus {
  border: 0;
  box-shadow: none;
}

#imp-form tr input[type="text"]:read-only:focus {
  border: 0;
  box-shadow: none;
}

@media (max-width: 768px) {
  #imp-form td.align-right, #imp-form td.align-center {
    text-align: left !important;
  }
}

#imp-form td.name-file {
  word-wrap: break-word;
  -webkit-hyphens: auto;
      -ms-hyphens: auto;
          hyphens: auto;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des Pop-up
 */
/* Popup Créer un lien vers un nouvel objet */
#popup-content #table-create-link-object #res-check, #popup-content #table-create-link-object tr:first-child td:first-child {
  width: 20px;
}

/* Produits offerts */
/* Popup rechercher un produit */
#tb-popup-catalogue {
  border-collapse: collapse;
  display: inline-table;
  clear: none;
  margin-right: 15px;
}

@media (min-width: 768px) {
  #tb-popup-catalogue {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #tb-popup-catalogue {
    width: 300px;
  }
}

#tb-popup-catalogue caption {
  box-sizing: border-box;
}

#tb-popup-catalogue thead th:first-child {
  width: 20px;
}

#tb-popup-catalogue thead th:last-child {
  width: 30px;
}

#tb-popup-catalogue td {
  border-top: 0;
  display: inline-block;
}

/* Popup résultats de la recherche de produits */
.lst-prd-table thead th.th-lst-10 {
  width: 10px;
}

.lst-prd-table thead th.th-lst-20 {
  width: 20px;
}

.lst-prd-table thead th.th-lst-80 {
  width: 80px;
}

.lst-prd-table thead th.th-lst-100 {
  width: 100px;
}

.lst-prd-table thead th.th-lst-130 {
  width: 130px;
}

/* Popup Sélection un objet de rattachement */
#tb-popup-liste-obj-classe {
  width: 100%;
}

#tb-popup-liste-obj-classe thead th#obj-name {
  width: 225px !important;
}

#tb-popup-liste-obj-classe thead th#obj-childs {
  width: 140px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de l'onglet Modération
 */
.tb-contact-moderation {
  max-width: 100%;
}

.tb-contact-moderation #th-author {
  width: 300px;
}

.tb-contact-moderation #th-message {
  width: 568px;
  max-width: 100%;
}

.tb-spam {
  width: 650px;
  max-width: 100%;
}

.tb-spam #checkbox {
  width: 10px;
}

.tb-spam #commentaire {
  width: 620px;
}

.tb-spam #type {
  width: 20px;
}

#tb-prd-a-moderer {
  width: 600px;
  max-width: 100%;
}

#tb-prd-a-moderer #prd-ref {
  width: 25px;
}

#td-mod-proprietes-prd {
  width: 100%;
}

#td-mod-proprietes-prd tr:first-child td:first-child {
  width: 200px;
}

#td-mod-demandes-modifications {
  width: 100%;
}

#td-mod-demandes-modifications #fur-sel {
  width: 25px;
}

#td-mod-demandes-modifications #fur-from, #td-mod-demandes-modifications #fur-field {
  width: 175px;
}

#td-mod-demandes-modifications #fur-previous, #td-mod-demandes-modifications #fur-next {
  width: 50%;
}

#td-mod-demandes-modifications #fur-date {
  width: 95px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS de Clients > Gestion des négociations
 */
/* tableaux dans Comptes clients • Gestion des négociations • Nouvelle règle de négociation */
#td-contexte {
  width: 150px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des options
 */
/* Catalogue */
#tb-ovr-usr #name {
  width: 200px;
}

#tb-ovr-usr #desc {
  width: 350px;
}

#tb-ovr-usr #value {
  width: 150px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS du fichier view.admin.inc.php
 */
/* Table Médiathèque */
.table-images-liees-mediatheque .td-table-mediatheque {
  width: 150px;
}

.table-images-liees-mediatheque .td-table-mediatheque a {
  font-size: 0.9em;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des tableaux de la Médiathèque
 */
#type-docs #type-sel {
  width: 25px;
}

#type-docs #type-name, #type-docs #type-desc {
  width: 175px;
}

#type-docs #type-documents {
  width: 100px;
}

#type-docs #type-pos-2 {
  width: 75px;
}

#documents {
  width: 100%;
  max-width: 860px;
}

#documents #doc-sel {
  width: 25px;
}

#documents #doc-name {
  width: 325px;
}

#documents #doc-file {
  width: 225px;
}

#documents #doc-remp {
  width: 280px;
}

#documents #type-pos {
  width: 75px;
}

#documents a:hover {
  text-decoration: none !important;
}

#table-objets-associes {
  width: 465px;
}

#table-objets-associes #obj-sel {
  width: 12px;
}

#table-objets-associes #obj-name {
  width: 450px;
}

#lst-downloads .th-lst-dl-150 {
  width: 150px;
}

#lst-downloads .th-lst-dl-250 {
  width: 250px;
}

#lst-downloads .th-lst-dl-100 {
  width: 100px;
}

.seg-obj-infos .del-obj-seg {
  width: 16px;
  height: 13px;
}

#table-hosts th {
  width: 100px;
}

#table-hosts th:first-child {
  width: 150px;
}

form#table-list-hosts table {
  width: 390px !important;
}

form#table-list-hosts table #chl-sel, form#table-list-hosts table #pls-sel, form#table-list-hosts table #med-sel {
  width: 30px;
}

form#table-list-hosts table #chl-name, form#table-list-hosts table #pls-name, form#table-list-hosts table #med-name {
  width: 350px;
}

#table-edit-hosts #td-edit-hosts-150 {
  width: 150px;
}

#table-edit-hosts #td-edit-hosts-350 {
  width: 350px;
}

#table-edit-hosts #list-channels .del-obj-seg {
  width: 16px;
  height: 13px;
}

/* Non utilisés */
.img-images {
  list-style: none;
  display: inline-block;
  margin: 10px 5px;
  height: 156px;
  width: 156px;
}

.img-images .input-del-item {
  margin-bottom: 3px;
}

.img-images a {
  height: 150px;
  width: 150px;
  display: block;
  border: 1px solid #DEDEDE;
}

.img-images a img {
  padding: 2px;
}

:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des fichiers ./catalog/linear-raised/index.php 
 * et ./catalog/linear-raised/section.php 
 * et ./catalog/linear-raised/edit.php 
 */
/* Tableau de Listes des sections d'assortiments */
#table-sections-assortiments {
  width: 536px;
}

#table-sections-assortiments #th-sections-1 {
  width: 25px;
}

#table-sections-assortiments #th-sections-2 {
  width: 400px;
}

/* Tableau de Liste d'assortiments produits <col width="25" /><col width="300" /><col width="100" /><col width="200" /><col width="150" />*/
#table-assortiments-produits #th-assortiments-1 {
  width: 25px;
}

#table-assortiments-produits #th-assortiments-2 {
  width: 300px;
}

#table-assortiments-produits #th-assortiments-3 {
  width: 150px;
}

#table-assortiments-produits #th-assortiments-4 {
  width: 200px;
}

#table-assortiments-produits #th-assortiments-5 {
  width: 150px;
}

/* Tableau Section */
#table-section #td-section-1 {
  width: 160px;
}

#table-section #td-section-2 {
  width: 410px;
}

/* Tableau Informations générales, dans l'onglet général */
#table-gestion-releves-infos-generales {
  width: 570px;
}

#table-gestion-releves-infos-generales table.pmt-rules {
  width: 100%;
}

#table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-1 {
  width: 25px;
}

#table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-2 {
  width: 330px;
}

#table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-3 {
  width: 100px;
}

#table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-3 .pmc {
  width: 80px;
}

#table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-4, #table-gestion-releves-infos-generales table.pmt-rules .td-pmt-rules-5 {
  width: 50px;
}

#table-gestion-releves-infos-generales #td-general-1 {
  width: 160px;
}

#table-gestion-releves-infos-generales #td-general-2 {
  width: 410px;
}

/*Tableaux gestion des relations dans l'onglet relations*/
#table-comptes-clients {
  width: 560px;
}

#table-comptes-clients .table-responsables {
  width: 100%;
}

#table-comptes-clients .table-responsables #th-responsables-1 {
  width: 25px;
}

#table-comptes-clients .table-responsables #th-responsables-2 {
  width: 510px;
}

/* tablessorter */
table.tablesorter thead tr th {
  vertical-align: middle !important;
  cursor: pointer;
}

table.tablesorter thead tr .header > * {
  display: block;
}

table.tablesorter thead tr .header > *::after {
  content: '';
  cursor: pointer;
  background-image: url("/admin/dist/images/up-down.svg");
  background-repeat: no-repeat;
  background-position: center right;
  background-size: 12px;
  display: inline-block;
  width: 12px;
  height: 12px;
  vertical-align: middle;
  margin-left: 2px;
}

table.tablesorter thead tr .headerSortUp > *::after {
  background-image: url("/admin/dist/images/up.svg");
}

table.tablesorter thead tr .headerSortDown > *::after {
  background-image: url("/admin/dist/images/down.svg");
}

table.tablesorter thead tr .headerSortDown,
table.tablesorter thead tr .headerSortUp {
  background-color: #c1c4ff !important;
}

.edit-cat {
  position: relative;
  display: inline-block;
  font-size: 13px;
  margin-left: 5px;
  padding: 0 0 0 20px;
  font-weight: 500;
  text-decoration: none;
  vertical-align: middle;
  line-height: 20px;
}

.edit-cat:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  background-image: url(/admin/dist/images/configuration_off.svg);
}

.with-cols-options {
  position: relative;
}

.with-cols-options #display-cols-options.edit-cat {
  padding: 0 0 0 24px;
  line-height: 16px;
}

.with-cols-options #display-cols-options.edit-cat::before {
  height: 16px;
  margin-top: -8px;
  background-image: url(/admin/dist/images/columns.svg);
}

.menu-cols .cols {
  position: absolute;
  font-size: 13px;
  background-color: #fff;
  border: 1px solid #A9A9A9;
  height: 300px;
  overflow-y: scroll;
  padding: 2px;
  z-index: 99;
}

.menu-cols .col {
  clear: both;
  color: #000;
  display: block;
  font-weight: 500;
  margin: 5px;
  position: static;
}

/* = Section En tête = */
.ria-admin-ui-header {
  display: flex;
  align-items: center;
  background-color: #232E63;
  color: #ffffff;
  padding: 18px 8px;
}

@media (min-width: 768px) {
  .ria-admin-ui-header {
    justify-content: space-between;
    padding: 18px 14px;
  }
}

/* == Colonne Bouton Menu == */
@media (min-width: 1024px) {
  .ria-admin-ui-toggle-menu {
    display: none;
  }
}

.ria-admin-ui-toggle-menu button {
  margin: 0;
  display: inline-block;
  border: 0 none;
  padding: 0;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  outline: none;
  background-color: transparent;
  border-radius: 5px;
}

.ria-admin-ui-toggle-menu button:hover {
  background-color: transparent;
}

.ria-admin-ui-toggle-menu button::before, .ria-admin-ui-toggle-menu button::after {
  display: none;
}

.ria-admin-ui-toggle-menu button img, .ria-admin-ui-toggle-menu button svg {
  width: 32px;
  height: 32px;
}

.ria-admin-ui-toggle-menu button:focus {
  box-shadow: none;
}

/* == Colonne Logo == */
.ria-admin-ui-logo-moto {
  padding-left: 8px;
}

@media (min-width: 768px) {
  .ria-admin-ui-logo-moto {
    padding-left: 15px;
    padding-right: 110px;
  }
}

/* === Widget Logo === */
.ria-admin-ui-logo {
  width: 92px;
}

.ria-admin-ui-moto {
  font-size: 12px;
  font-weight: 400;
}

.ria-admin-ui-moto a, .ria-admin-ui-moto a:visited {
  color: ghostwhite;
  text-decoration: none;
}

/* == Colonne Barre de recherche == */
.ria-admin-ui-searchbar {
  flex: 1 1 auto;
}

.ria-admin-ui-header .ria-admin-ui-searchbar {
  display: none;
}

@media (min-width: 768px) {
  .ria-admin-ui-header .ria-admin-ui-searchbar {
    display: block;
  }
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  height: 30px;
  background-color: #fff;
  border-radius: 20px;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input {
  padding-left: 24px;
  width: 100%;
  height: 100%;
  border: 0 none;
  max-width: none;
  font-size: 14px;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input:hover {
  border: 0 none;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__icon {
  background-color: transparent;
  border: 0 none;
  max-width: none;
  bottom: 3px;
}

.ria-admin-ui-searchbar .mdc-text-field__icon {
  padding: 0;
  margin: 0;
}

.ria-admin-ui-searchbar .mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom: 0 none;
}

.ria-admin-ui-searchbar .mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom: 0 none;
}

/* == Colonne actions Mes options, Déconnexion == */
.ria-admin-ui-headactions {
  display: none;
}

@media (min-width: 1024px) {
  .ria-admin-ui-headactions {
    display: flex;
  }
}

.ria-admin-ui-headactions .mdc-button {
  height: 30px;
  font-size: 12px;
  font-weight: 400;
  padding: 0 20px;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: unset;
  position: relative;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active {
  color: #fff;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active::before, .ria-admin-ui-headactions .mdc-button.ria-button--active::after {
  position: absolute;
  width: auto;
  height: 100%;
  top: 0;
  right: 10px;
  left: 10px;
  bottom: 0;
  background-color: #5377FB;
  opacity: 1;
  border-radius: 5px;
  z-index: -1;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active:hover {
  text-decoration: none;
}

/* Fil d'ariane, breadcrumbs emplacement en cours */
#site-location {
  font-size: 11px;
  margin-bottom: 15px;
}

/* Cache le h1 Riashop, et le timer */
h1, .page_timer {
  display: none !important;
}

.mdc-button:not(:disabled).ria-button--light {
  color: #fff;
}

.mdc-button:not(:disabled).ria-button--outline-light {
  color: #fff;
}

.mdc-button:not(:disabled).ria-button--outline-light:hover {
  background-color: #fff;
  color: #232E63;
  text-decoration: none;
}

.mdc-button:not(:disabled).ria-button--outline-dark:hover {
  background-color: #232E63;
  color: #fff;
  text-decoration: none;
}

.mdc-button:not(:disabled).ria-button--tti {
  text-transform: none;
}

.ria-button--outline-dark {
  border: 1px solid #232E63;
}

.ria-button--outline-light {
  border: 1px solid #fff;
}

.ria-button--bordered {
  border: 1px solid #ffffff;
}

/* Zone de contenu */
#site-content {
  margin-left: 10px;
  margin-right: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
  min-width: 0;
  min-height: 500px;
}

@media (min-width: 768px) {
  #site-content {
    margin-left: 20px;
    margin-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media (min-width: 1024px) {
  #site-content {
    margin-left: 230px;
    margin-right: 30px;
    padding-top: 30px;
    padding-bottom: 30px;
    min-width: 726px;
    min-height: 672px;
  }
}

#site-content .mceToolbar .toolBarGroup, #site-content .defaultSkin .mceIframeContainer {
  box-shadow: none;
}

.mceLayout {
  max-width: 960px;
  margin-top: 0 !important;
  width: 100% !important;
}

/* Copyright */
#site-footer {
  display: none;
}

#site-footer {
  display: block;
  font-size: 10px;
  padding: 2px;
  color: #232E63;
  background-color: #DADCFF;
  bottom: 0px;
  left: 0px;
  right: 0px;
  border-top: 1px solid #A9A9A9;
  z-index: 2;
}

@media (max-width: 1023px) {
  #site-footer {
    display: none;
  }
}

.ria-admin-ui-filters {
  display: flex;
  flex-wrap: wrap;
}

.ria-admin-ui-overview {
  margin-top: 10px;
  clear: both;
}

.ria-admin-ui-actions {
  margin-top: 10px;
}

/* Popup d'information a la connexion */
:root {
  --mdc-theme-primary: $dark-color;
}

.ria-admin-ui-intro-wrapper {
  display: none;
}

.ria-admin-ui-intro {
  text-align: center;
  color: #232E63;
  padding: 22px;
}

@media (min-width: 1024px) {
  .ria-admin-ui-intro {
    padding: 30px;
  }
}

.ria-admin-ui-intro-media {
  border: 0 none;
  width: 150px;
  margin-bottom: 22px;
}

.ria-admin-ui-intro-title {
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 6px;
}

.ria-admin-ui-intro-caption {
  font-size: 13px;
  margin-bottom: 32px;
}

.ria-admin-ui-intro-button {
  width: 156px;
  height: 42px !important;
  background-image: linear-gradient(to bottom, #3e58e4, #2439b7);
  color: #fff !important;
  border: 0 none !important;
}

.ria-admin-ui-intro-button:hover {
  background-image: linear-gradient(to bottom, #2845e1, #2032a2);
}

:root {
  --mdc-theme-primary: $dark-color;
}

/* Transforme le menu principal en barre d'outils */
#site-menu-search {
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  flex-basis: 0;
}

#site-menu-search #site-submenu {
  top: 140px;
}

/* Champs spécifiques à la liste des commandes */
.ria-admin-ui-menu #ord-ref, .ria-admin-ui-menu #ret-ref {
  max-width: 148px;
}

/* = Menu = */
/* Menu principal de l'admin */
.ria-admin-ui-menu {
  list-style-type: none;
  font-size: 12px;
  margin-bottom: 0;
  margin-top: 0;
  /* Sous menu (Contextuel), Submenu */
}

.ria-admin-ui-menu li, .ria-admin-ui-menu ul {
  margin: 0;
  padding: 0;
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu {
    border-bottom: 1px solid #A9A9A9;
    height: 66px;
    display: flex;
  }
}

.ria-admin-ui-menu > li {
  flex: 0 1 150px;
}

.ria-admin-ui-menu > li > .site-menu-btn {
  color: #222842;
  text-decoration: none;
  line-height: 44px;
  padding: 0 5px;
  display: block;
  font-size: 12px;
}

.ria-admin-ui-menu > li > .site-menu-btn:hover, .ria-admin-ui-menu > li > .site-menu-btn.tab-menu-active {
  background-color: #5377FB;
  color: #fff;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active) {
    border-color: #fff;
  }
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu > li > .site-menu-btn {
    padding-top: 42px;
    padding-bottom: 11px;
    position: relative;
    line-height: 13px;
    text-align: center;
  }
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active) {
    border-color: #fff;
  }
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active):hover {
    border-left: 1px solid #A9A9A9;
    border-right: 1px solid #A9A9A9;
  }
}

.ria-admin-ui-menu label {
  margin-bottom: 8px;
  display: block;
}

.ria-admin-ui-menu .ria-admin-ui-submenu {
  color: #232E63;
  display: block;
  list-style-type: circle;
  list-style-position: outside;
  padding-bottom: 18px;
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu .ria-admin-ui-submenu {
    position: absolute;
    left: 0px;
    width: 200px;
    margin-top: 1px;
  }
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul {
  margin-bottom: 0;
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul li {
  position: relative;
  border-top: 1px solid #fff;
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul li a::before, .ria-admin-ui-menu .ria-admin-ui-submenu ul li div::before {
  content: '\002022';
  font-size: 28px;
  line-height: 10px;
  vertical-align: top;
  margin-right: 4px;
}

.ria-admin-ui-menu .ria-admin-ui-submenu > li {
  text-align: left;
  list-style-type: none;
  border-bottom: 1px solid #A9A9A9;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled {
  border-style: none;
  display: block;
  color: #232E63;
  padding: 10px 12px 6px;
  text-decoration: none;
  position: relative;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a.ria-admin-ui-searchbar,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div.ria-admin-ui-searchbar,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled.ria-admin-ui-searchbar {
  margin: 0;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a:hover,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div:hover,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled:hover {
  text-decoration: none;
  background-color: #DADCFF;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a.ria-admin-ui-submenu--active,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div.ria-admin-ui-submenu--active,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled.ria-admin-ui-submenu--active {
  background-color: #5377FB;
  color: #fff;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled {
  cursor: default;
  display: block;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > ul > li > span.disabled {
  display: block;
  color: #232E63;
  padding: 10px 12px 6px;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > ul > li > span.disabled:before {
  content: '\002022';
  font-size: 28px;
  line-height: 10px;
  vertical-align: top;
  margin-right: 4px;
}

#site-menu-home {
  display: none;
}

.ria-admin-ui-menubar {
  background-color: #fff;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menubar {
    position: absolute;
    z-index: 10000;
    width: 100%;
    width: 100vw;
    max-width: 260px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    background-image: none;
    height: auto;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
    transition: -webkit-transform 0.2s ease-out;
    transition: transform 0.2s ease-out;
    transition: transform 0.2s ease-out, -webkit-transform 0.2s ease-out;
    max-height: calc(100% - 74px);
    overflow-y: auto;
    padding-bottom: 10px;
  }
  .ria-admin-ui-menubar.is-open {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar {
  margin: 10px 12px;
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  border: 1px solid #A9A9A9;
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input {
  padding-left: 8px;
}

.ria-admin-ui-menubar .ria-admin-ui-headactions {
  display: none;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menubar .ria-admin-ui-headactions {
    display: block;
  }
}

.ria-admin-ui-menubar .ria-admin-ui-headactions .mdc-button {
  padding: 0 12px;
  margin: 3px 0;
}

.ria-admin-ui-menubar .ria-admin-ui-headactions .ria-button--outline-dark {
  margin-left: 12px;
}

.ria-admin-ui-searchbar--top-menu {
  display: block;
}

@media (min-width: 1024px) {
  .ria-admin-ui-searchbar--top-menu {
    display: none;
  }
}

.ria-admin-ui-legals {
  font-size: 10px;
  color: #232E63;
  margin-top: 13px;
  padding: 0 12px;
}

@media (min-width: 1024px) {
  .ria-admin-ui-legals {
    display: none;
  }
}

.ria-sprite {
  background-repeat: no-repeat;
  display: block;
  position: absolute;
  top: 6px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 28px;
  height: 28px;
}

@media (max-width: 1023px) {
  .ria-sprite {
    position: static;
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: scale(0.7);
            transform: scale(0.7);
  }
}

.ria-sprite-catalog {
  background-image: url("/admin/dist/images/catalogue_off.svg");
}

.site-menu-btn:hover .ria-sprite-catalog, .tab-menu-active .ria-sprite-catalog {
  background-image: url("/admin/dist/images/catalogue.svg");
}

.ria-sprite-config {
  background-image: url("/admin/dist/images/configuration_off.svg");
}

.site-menu-btn:hover .ria-sprite-config, .tab-menu-active .ria-sprite-config {
  background-image: url("/admin/dist/images/configuration.svg");
}

.ria-sprite-customers {
  background-image: url("/admin/dist/images/clients_off.svg");
}

.site-menu-btn:hover .ria-sprite-customers, .tab-menu-active .ria-sprite-customers {
  background-image: url("/admin/dist/images/clients.svg");
}

.ria-sprite-media {
  background-image: url("/admin/dist/images/mediatheque_off.svg");
}

.site-menu-btn:hover .ria-sprite-media, .tab-menu-active .ria-sprite-media {
  background-image: url("/admin/dist/images/mediatheque.svg");
}

.ria-sprite-moderation {
  background-image: url("/admin/dist/images/moderation_off.svg");
}

.site-menu-btn:hover .ria-sprite-moderation, .tab-menu-active .ria-sprite-moderation {
  background-image: url("/admin/dist/images/moderation.svg");
}

.ria-sprite-order {
  background-image: url("/admin/dist/images/commandes_off.svg");
}

.site-menu-btn:hover .ria-sprite-order, .tab-menu-active .ria-sprite-order {
  background-image: url("/admin/dist/images/commandes.svg");
}

.ria-sprite-pda {
  background-image: url("/admin/dist/images/yuto_off.svg");
}

.site-menu-btn:hover .ria-sprite-pda, .tab-menu-active .ria-sprite-pda {
  background-image: url("/admin/dist/images/yuto.svg");
}

.ria-sprite-yuto {
  background-image: url("/admin/dist/images/yuto_off.svg");
}

.site-menu-btn:hover .ria-sprite-yuto, .tab-menu-active .ria-sprite-yuto {
  background-image: url("/admin/dist/images/yuto.svg");
}

.ria-sprite-promotions {
  background-image: url("/admin/dist/images/promotions_off.svg");
}

.site-menu-btn:hover .ria-sprite-promotions, .tab-menu-active .ria-sprite-promotions {
  background-image: url("/admin/dist/images/promotions.svg");
}

.ria-sprite-stats {
  background-image: url("/admin/dist/images/statistiques_off.svg");
}

.site-menu-btn:hover .ria-sprite-stats, .tab-menu-active .ria-sprite-stats {
  background-image: url("/admin/dist/images/statistiques.svg");
}

.ria-sprite-tools {
  background-image: url("/admin/dist/images/outils_off.svg");
}

.site-menu-btn:hover .ria-sprite-tools, .tab-menu-active .ria-sprite-tools {
  background-image: url("/admin/dist/images/outils.svg");
}

.ria-sprite-comparators {
  background-image: url("/admin/dist/images/comparateurs_off.svg");
}

.site-menu-btn:hover .ria-sprite-comparators, .tab-menu-active .ria-sprite-comparators {
  background-image: url("/admin/dist/images/comparateurs.svg");
}

.ria-admin-ui-menu #site-menu-options {
  flex-basis: 1px;
}

.ria-admin-ui-menu #site-menu-options .site-menu-btn {
  width: 1px;
  visibility: hidden;
  overflow: hidden;
  padding-left: 0;
  padding-right: 0;
  text-indent: -9999px;
}

/* Coupe le contenu de la colonne url pour chrome */
td[headers="bnr-url"] {
  /* These are technically the same, but use both */
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  /* This is the dangerous one in WebKit, as it breaks things wherever */
  word-break: break-all;
  /* Instead use this non-standard one: */
  word-break: break-word;
}

/* Surcharge le tinymce */
#site-content .defaultSkin .mceIframeContainer {
  padding: 0 !important;
  overflow: hidden;
}

.defaultSkin table.mceToolbar,
.defaultSkin table.mceToolbar *,
.mceSplitButtonMenu,
.mceSplitButtonMenu *,
.mceListBoxMenu,
.mceListBoxMenu *,
.mceMenu,
.mceMenu *,
.mce_forecolor,
.mce_forecolor *,
[id^="mce"], [id^="mce"] * {
  box-sizing: content-box;
  transition: none;
}

@media (max-width: 767px) {
  .mceToolbar tbody tr {
    display: flex;
    flex-wrap: wrap;
  }
}

/* Page Mes options • Mon compte */
.myaccount label {
  padding-right: 5px;
  display: block;
}

@media (min-width: 768px) {
  .myaccount label {
    float: left;
    width: 175px;
    text-align: right;
  }
}

.myaccount label.inline {
  display: inline;
  float: none;
}

@media (max-width: 767px) {
  .myaccount dd {
    margin-bottom: 8px;
  }
}

.none {
  display: none !important;
}

.align-center {
  text-align: center !important;
}

.align-right {
  text-align: right !important;
}

.align-left {
  text-align: left !important;
}

.valign-center {
  vertical-align: center !important;
}

.width-auto {
  width: auto !important;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.float-none {
  float: none !important;
}

img.sync {
  width: 16px;
  height: 16px;
  border: 0 !important;
  vertical-align: middle;
  margin-right: 2px;
}

img.icon-del-cat {
  border: 0 !important;
}

h2 img.sync {
  border: 0 !important;
  width: 28px;
  height: 28px;
}

.no-border {
  border: 0 !important;
}

a.del-link, .del-link a, #pmt-special input.cdt-grp-del, input.input-icon-del {
  background-image: url("/admin/images/cut_inactive.svg");
  background-repeat: no-repeat;
  background-color: transparent !important;
  color: transparent !important;
  display: block;
  height: 23px;
  width: 23px;
  padding: 0;
  border: 0 !important;
  color: transparent;
}

a.del-link:hover, .del-link a:hover, #pmt-special input.cdt-grp-del:hover, input.input-icon-del:hover {
  background-image: url("/admin/images/cut_rollover.svg");
}

.fleche-stats {
  width: 16px;
  height: 8px;
}

.color-red {
  color: red !important;
}

.fleche-move {
  width: 16px;
  height: 16px;
  border-style: medium none !important;
}

.large {
  width: 100%;
}

.clear-right {
  clear: right;
}

.clear-left {
  clear: left;
}

.clear-both {
  clear: both;
}

.display-flex {
  display: flex;
}

.block {
  display: block;
}

.fleche {
  width: 16px;
  height: 8px;
}

.inline-block {
  display: inline-block;
}

.uppercase {
  text-transform: uppercase;
}

.bg-white {
  background-color: #FFF !important;
}

.border-none {
  border: medium none;
}

.margin-top-10 {
  margin-top: 10px;
}

.div-padding-15 {
  padding: 15px;
}

.bold {
  font-weight: bold;
}

/*# sourceMappingURL=layout-v1.css.map */

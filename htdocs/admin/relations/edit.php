<?php
require_once('relations.inc.php');

/** Ce fichier permet l'affichage de l'onglet "Relations", il affiche tous les blocs de relation liée au type d'objet demandé.
 * Attention il est à inclure dans d'autre partie de l'administration et ne gére pas le header/footer.
 * Il doit fonctionner de manière autonome et demande de définir les variables suivante dans le code appellant :
 * 		- src_cls : Identifiant de la classe source
 * 		- src : Identifiants de l'objet source, attention cela doit être un tableau dans le cas de clé composé
 * \warning : inclure le fichier javascript "admin/js/relations.js"
 */

// controles des paramètres d'entré
if( !isset($src_cls) ){
	if( isset($_GET['src_cls']) ){
		$src_cls = $_GET['src_cls'];
	}
}
if( !isset($src) ){
	$src = array();
	if( isset($_GET['src_0']) ){
		$src[0] = $_GET['src_0'];
	}
	if( isset($_GET['src_1']) ){
		$src[1] = $_GET['src_1'];
	}
	if( isset($_GET['src_2']) ){
		$src[2] = $_GET['src_2'];
	}
}

if( !isset($src_cls) || !isset($src) ){
	print _('Veuillez vérifier les paramètres nécessaire au fichier relation/edit.');
	exit;
}

// si nous avons soumis une requete de suppression de relation
if( isset($_REQUEST['del-rel']) ){
	foreach( $_REQUEST['del-rel'] as $rel_id ){

		if( !rel_relations_del($rel_id) ){
			$error = _('Une erreur est survenue lors de la suppression des relations.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.');
		}

	}
}

if( $src_cls && $src ){

	/** Cette fonction se charge de générer le code html pour un tableau de relations
	 *	@param int $type_id Obligatoire, Identifiant du type de la relation
	 *	@param int|array $src Obligatoire, Identifiant ou tableau d'identifiants dans le cas d'un objet composé, pour une relation dans le sens Src => Dst, l'objet doit être de classe équivalente à celle défini dans le type de relation "rrt_src_cls"
	 *	@param int|array $dst Obligatoire, Identifiant ou tableau d'identifiants dans le cas d'un objet composé, pour une relation dans le sens Dst => Src, l'objet doit être de classe équivalente à celle défini dans le type de relation "rrt_dst_cls"
	 *	@warning Dans le cas d'une relation bidirectionnel, il faut appeler 2 fois la fonction pour afficher un tableau des relations enfants et un pour les relations parents
	 *		Il n'est donc pas possible d'appeler cette fonction avec la src et la dst en même temps.
	 */
	function view_admin_object_relation_type($type_id, $src, $dst){

		$rtype = rel_relations_types_get($type_id);
		if( !$rtype || !ria_mysql_num_rows($rtype) ){
			return false;
		}
		$type = ria_mysql_fetch_assoc($rtype);

		if( $src && $dst ){
			return false;
		}

		if( $src ){
			$src = $src === null ? 0 : $src;
			$src = control_array_integer( $src, false, true, true );
			if( $src==false || !$src[0] ){
				$src = array(0);
			}
			while( sizeof($src) < COUNT_OBJ_ID ){
				$src[] = 0;
			}

			// liste les relations présente
			$rrel = rel_relations_get(0, $src, null, $type['id']);
		}

		if( $dst ){
			$dst = $dst === null ? 0 : $dst;
			$dst = control_array_integer( $dst, false, true, true );
			if( $dst==false || !$dst[0] ){
				$dst = array(0);
			}
			while( sizeof($dst) < COUNT_OBJ_ID ){
				$dst[] = 0;
			}

			// liste les relations présente
			$rrel = rel_relations_get(0, null, $dst, $type['id']);
		}

		// liste des colonnes entete pour le tableau
		// cols[] = array (
		//		'name' => Nom de la colonne
		//		'size' => Taille de la colonne
		// )
		$cols = array();

		// tableau des résultats il doit comporter la liste des objets à afficher de la facon suivante :
		// data[] = array (
		//		'id' => Identifiant de l'objet
		//		'cols' => Liste des données sous forme de tableau, les colonnes doivent avoir le même ordre que le tableau des cols
		// )
		$data = array();

		// Switch en fonction de la classe de l'objet de destination l'affichage difère.
		// Ce switch est à implémenter en fonction des classes nécessaires.

		switch( $type['dst_cls'] ){
			// Compte clients
			case CLS_USER :
				$cols[] = array('name' => _('Code client'), 'size' => 100 );
				$cols[] = array('name' => _('Société'), 'size' => 120 );
				$cols[] = array('name' => _('Nom'), 'size' => 150 );
				$cols[] = array('name' => _('Email'), 'size' => 200 );
				$cols[] = array('name' => _('Adresse'), 'size' => 150 );
				$cols[] = array('name' => _('Compléments'), 'size' => 150 );

				$usr_ids = array();
				if( $rrel && ria_mysql_num_rows($rrel) ){
					while( $rel = ria_mysql_fetch_assoc($rrel) ){
						if( $dst ){
							$usr_ids[$rel['src_0']] = $rel['id'];
						}else{
							$usr_ids[$rel['dst_0']] = $rel['id'];
						}
					}
				}
				if( sizeof($usr_ids) ){
					$rusr = gu_users_get(array_keys($usr_ids), '', '', 0, '', 0, '', 'set');
					if( $rusr ){
						while( $usr = ria_mysql_fetch_assoc($rusr) ){
							$data[] = array(
								'id' => $usr_ids[$usr['id']],
								'cols' => array(
										'<a href="/admin/customers/edit.php?usr='.$usr['id'].'&prf='.$usr['prf_id'].'">'.( $usr['ref'] ? htmlspecialchars($usr['ref']) : 'n/a').'</a>',
										htmlspecialchars($usr['society']),
										htmlspecialchars($usr['adr_firstname'].' '.$usr['adr_lastname']),
										htmlspecialchars($usr['email']),
										htmlspecialchars($usr['address1']),
										htmlspecialchars($usr['address2']),
									)
								);
						}
					}
				}

				break;
			// Produits
			case CLS_PRODUCT :
				$cols[] = array('name' => _('Référence'), 'size' => 100 );
				$cols[] = array('name' => _('Nom'), 'size' => 200 );

				$prd_ids = array();
				if( $rrel && ria_mysql_num_rows($rrel) ){
					while( $rel = ria_mysql_fetch_assoc($rrel) ){
						if( $dst ){
							$prd_ids[$rel['src_0']] = $rel['id'];
						}else{
							$prd_ids[$rel['dst_0']] = $rel['id'];
						}
					}
				}
				if( sizeof($prd_ids) ){
					$rprd = prd_products_get_simple(array_keys($prd_ids), '', false, 0, false, false, false, false, array('childs' => true));
					if( $rprd ){
						while( $prd = ria_mysql_fetch_assoc($rprd) ){
							$data[] = array(
								'id' => $prd_ids[$prd['id']],
								'cols' => array(
										htmlspecialchars($prd['ref']),
										htmlspecialchars($prd['title'])
									)
								);
						}
					}
				}

				break;
			default :
				return;
				break;
		}

		$html = '
			<div class="virtual-form">
				<input type="hidden" name="type" value="'.$type['id'].'"/>
				<input type="hidden" name="src_cls" value="'.$type['src_cls'].'"/>
				<input type="hidden" name="dst_cls" value="'.$type['dst_cls'].'"/>';

				if( $src ){
					$html .= '
						<input type="hidden" name="src_0" value="'.$src[0].'"/>
						<input type="hidden" name="src_1" value="'.$src[1].'"/>
						<input type="hidden" name="src_2" value="'.$src[2].'"/>
					';
				}
				if( $dst ){
					$html .= '
						<input type="hidden" name="dst_0" value="'.$dst[0].'"/>
						<input type="hidden" name="dst_1" value="'.$dst[1].'"/>
						<input type="hidden" name="dst_2" value="'.$dst[2].'"/>
					';
				}

		$html .= '
				<table class="checklist hierarchies">
					<caption>'.$type['name_plural'].' '.($src ? _('enfant'):_('parent')).' </caption>
					<thead>
						<tr>
							<th><input type="checkbox" onclick="checkAllClick(this)" name="checkall" class="checkall"></th>
						';
						foreach( $cols as $col ){
							$html .= '<th class="'.urlalias($col['name']).'" id="'.urlalias($col['name'].'-'.$type['name_plural'].' '.($src ? _('enfant'):_('parent'))).'">'.$col['name'].'</th>';
						}
		$html .= '		</tr>
					</thead>
					<tfoot>
						<tr>
							<td class="tdleft" colspan="2">
		';
		if( sizeof($data)>0 ){
			$html .= '<input type="button" value="' . _("Supprimer") . '" title="' . _("Supprimer les relations sélectionnés") . '" name="rel-del" class="del-rel">';
		}
		$html .= '
							</td>
							<td class="align-right" colspan="'.(sizeof($cols)-1).'">
								<input type="button" name="rel-add" value="' . _("Ajouter"). '" class="button add-rel">
							</td>
						</tr>
					</tfoot>
					<tbody>';

					if( sizeof($data) > 0 ) {
						foreach($data as $d){
							$html .= '
								<tr>
									<td><input type="checkbox" name="del-rel[]" value="'.$d['id'].'"/></td>';

									foreach( $d['cols'] as $key => $col ){
										$html .= '<td headers="'.urlalias($cols[$key]['name'].'-'.$type['name_plural'].' '.($src ? _('enfant'):_('parent'))).'">'.$col.'</td>';
									}

							$html .= '
								</tr>
							';
						}
					}else{
						$html .= '
							<tr>
								<td colspan="'.(sizeof($cols)+1).'">' . _("Aucune relation") . '</td>
							</tr>
						';
					}

		$html .= '	</tbody>
				</table>
			</div>
		';

		return $html;
	}


	?>

<div class="block-notice-container relations-container">

	<input type="hidden" name="reload_src_cls" value="<?php print $src_cls ?>"/>
	<input type="hidden" name="reload_src_0" value="<?php print $src[0] ?>"/>
	<input type="hidden" name="reload_src_1" value="<?php print $src[1] ?>"/>
	<input type="hidden" name="reload_src_2" value="<?php print $src[2] ?>"/>
<?php

	// dans certain cas nous traitons l'affichage différement
	$exclude_type = array();
	switch($src_cls){
		case CLS_USER:
			$exclude_type[] = REL_USR_HIERARCHY;
			$exclude_type[] = REL_SELLER_HIERARCHY;

			// affichage de la hiérarchie comptes
			?>
			<div class="notice"><?php echo _("Les tableaux présentés ci-dessous vous permettent de gérer des relations complexes entre utilisateurs dans le cas de groupement à plusieurs niveaux de droit."); ?></div>
			<?php
			print view_admin_object_relation_type(REL_USR_HIERARCHY, 0, $src);
			print view_admin_object_relation_type(REL_USR_HIERARCHY, $src, 0);

			// affichage des relations entre commerciaux
			?>
			<div class="notice"><?php echo _("Les tableaux présentés ci-dessous vous permettent de lier des commerciaux entre eux ou d'attribuer un client à plusieurs commerciaux même si votre gestion commerciale ne vous le permet pas. Si vous ajoutez un commercial A au dessus d'un commercial B, alors le commercial A récupère l'ensemble des clients du commercial B."); ?></div>
			<?php
			print view_admin_object_relation_type(REL_SELLER_HIERARCHY, 0, $src);
			print view_admin_object_relation_type(REL_SELLER_HIERARCHY, $src, 0);

			break;
	}

	// on récupère toutes les relations dans le sens enfant => parent pour cette classe
	$rtype = rel_relations_types_get(0, 0, $src_cls);
	if( $rtype && ria_mysql_num_rows($rtype) ){
		while( $type = ria_mysql_fetch_assoc($rtype) ){
			if( in_array($type['id'], $exclude_type) ) continue;
			print view_admin_object_relation_type($type['id'], 0, $src);
		}
	}
	// on récupère toutes les relations dans le sens parent => enfant pour cette classe
	$rtype = rel_relations_types_get(0, $src_cls);
	if( $rtype && ria_mysql_num_rows($rtype) ){
		while( $type = ria_mysql_fetch_assoc($rtype) ){
			if( in_array($type['id'], $exclude_type) ) continue;
			print view_admin_object_relation_type($type['id'], $src, 0);
		}
	}

}
?>
</div>
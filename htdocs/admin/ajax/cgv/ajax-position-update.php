<?php

	/**	\file ajax-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un article des conditions générales de vente.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CGV_EDIT');

	if (! isset($_GET['ver'], $_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$ver = $_GET['ver'];
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('cgv.inc.php');
	
	$response = array('success' => cgv_articles_position_update($ver, $source, $target, $action));
	
	print json_encode($response);
	exit;


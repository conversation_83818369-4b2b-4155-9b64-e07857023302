<?php 

if (! isset($config['admin_submenu_state'])) throw new Exception('Erreur config !');

require_once('cfg.variables.inc.php');


if( !$config['admin_submenu_state'] ){
    $config['admin_submenu_state'] = 1;
}else{
    $config['admin_submenu_state'] = 0;
}

$response = array('success' => cfg_overrides_set_value( 'admin_submenu_paneling_state', $config['admin_submenu_state'], 0, $_SESSION['usr_id'], 0 ));

print json_encode($response);

exit;
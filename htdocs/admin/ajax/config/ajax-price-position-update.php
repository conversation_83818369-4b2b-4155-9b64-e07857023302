<?php

	/**	\file ajax-price-position-update.php
	 * 
	 *	Ce fichier permet la mise à jour de la position de l'ordre de priorité des tarifs.
	 *
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PRICE');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	if (! is_numeric($source)) throw new Exception;
	if (! is_numeric($target)) throw new Exception;
	if (! in_array($action, array('before', 'after'))) throw new Exception;
	
	require_once('prices.inc.php');
	$rppf = prc_fields_get();
	if (! $rppf) throw new Exception(mysql_error());
	$data = array();
	while ($r = ria_mysql_fetch_assoc($rppf)) $data[$r['priority']] = $r;
	
	// Insère la ligne virtuelle
	$i = 1;
	$found = false;
	foreach ($data as $pos => $dat) {
		if ($pos != $i) {
			$data[$i] = array('fld_id' => -1, 'priority' => $i);
			$found = true;
			break;
		}
		$i++;
	}
	if (! $found) $data[$i] = array('fld_id' => -1, 'priority' => $i);
	
	// Trouve la source et la cible
	foreach ($data as $pos => $dat) {
		if ($dat['fld_id'] == $source) { $a = $pos; }
		elseif ($dat['fld_id'] == $target) { $b = $pos; }
	}
	if (! isset($a, $b)) throw new Exception;
	
	if ($a > $b && $action === 'after') $b++;
	elseif ($a < $b && $action === 'before') $b--;
	
	if ($a < $b) {
		$start = $a;
		$end = $b-1;
		$step = +1;
	}
	else {
		$start = $a;
		$end = $b+1;
		$step = -1;
	}
	$end += $step;
	
	// Déplacement
	for ($i=$start; $i!=$end; $i+=$step) {
		$data[$i] = $data[$i+$step];
		$data[$i]['priority'] = $i;
	}
	$data[$end]['fld_id'] = $source;
	
	// Sauvegarde
	for ($i=$start; $i!=$end+$step; $i+=$step) {
		if ($data[$i]['fld_id'] != -1){
			if( !prc_fields_set_priority( $data[$i]['fld_id'], $data[$i]['priority'] ) ){
				throw new Exception;
			}
		}
	}
	
	print json_encode(array('success' => true));
	exit;


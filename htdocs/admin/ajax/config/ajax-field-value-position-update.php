<?php

	/**	\file ajax-field-value-position-update.php
	 *	Ce fichier permet la mise à jour de la position des valeurs d'un champ avancé de type Liste de choix.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('fields.inc.php');
	
	$response = array('success' => fld_restricted_values_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;

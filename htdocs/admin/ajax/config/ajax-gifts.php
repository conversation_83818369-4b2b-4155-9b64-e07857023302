<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_GIFT');

	require_once('products.inc.php');
	require_once('cfg.variables.inc.php');

	if( isset($_GET['get-info-gift']) ){
		$json = array( 'isSync'=>0, 'price_ttc'=>0 );

		if( isset($_GET['prd']) && prd_products_exists($_GET['prd']) ){
			$prd = ria_mysql_fetch_array( prd_products_get($_GET['prd']) );

			$json = array(
				'isSync' => $prd['is_sync'],
				'amount' => $prd['price_ttc']>0 && !in_array($prd['ref'], $config['gifts_prd_variable']) ? number_format( $prd['price_ttc'], 2, ',', ' ' ).'€' : 'Variable'
			);
		}
		
		print json_encode( $json );
		exit;
	}

	if( isset($_GET['save-list-prd'], $_GET['products']) ){
		$products = explode( ';', $_GET['products'] );
		if( !cfg_overrides_set_value('gifts_products', implode(', ', $products), $config['wst_id']) ){
			print 'ko';
		}else{
			print 'ok';
		}

		exit;
	}

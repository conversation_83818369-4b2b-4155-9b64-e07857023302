<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_MODEL');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('fields.inc.php');
	
	$response = array('success' => fld_models_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;

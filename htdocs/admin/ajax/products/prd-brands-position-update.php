<?php

	/**	\file prd-brands-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un marque.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND');

	if( !isset($_POST['source'], $_POST['target'], $_POST['action']) ){
		error_log( __FILE__.':'.__LINE__.' error post brands position update' );
		return false;
	}
	
	$res = obj_position_update( DD_BRAND, $_POST['source'], $_POST['target'], $_POST['action'] );
	
	$response = array( 'success' => $res );
	
	print json_encode( $response );
	exit;
<?php

    /** \file popup-colisage-selector.php
     * 
     *  Ce fichier fournit une interface graphique permettant la sélection des conditionnements dans lesquels un produit
     *  est disponible. Les paramètres à fournir sont les suivants :
     *  - prd_id : Obligatoire, identifiant du produit
     * 
     *  Cette interface est utilisée dans le processus de création ou de modification de commande, d'où le choix du droit associé
     * 
     */

    // Vérifie que l'utilisateur en cours peut accéder à cette page
    if( !gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') && !gu_user_is_authorized('_RGH_ADMIN_ORDER_EDIT') ){
        header('HTTP/1.0 403 Forbidden');
        exit;
    }

    $is_ajax = false;
    if (!isset($_GET['prd_id'])){
        $error = _('Il manque des paramètres');
    }

    if (is_numeric($_GET['prd_id'])){
        $_GET['prd_id'] = array($_GET['prd_id']);
    }

    if (!is_array($_GET['prd_id'])){
        $error = _('Le paramètre fourni n\'est pas au bon format');
    }

    //si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Sélection des conditionnements'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
        print '<div class="error">'.nl2br($error).'</div>';
        exit;
    } elseif(!isset($error)) {
        print '<div class="notice">'._('Veuillez sélectionner un ou plusieurs conditionnement parmi les choix proposés').'</div>';
    }
?>

<form method="post" action="#">
    <?php 
        foreach ($_GET['prd_id'] as $prd) { 
            $r_colisage = prd_colisage_classify_get(0, $prd);
            if(!$r_colisage){
                print '<div class="error">'.nl2br(sprintf(_('Une erreur s\'est produite lors de la récupération des conditionnements du produit %s.'), $prd)).'</div>';
            }
        ?>
            <table class="checklist colisage-table" id="colisage-table-<?php print $prd; ?>">
                <input type="hidden" name="prd_id" id="prd_id" value="<?php print $prd; ?>" />
                <caption><?php printf(_('Sélection des conditionnements - %s'), prd_products_get_name($prd)); ?></caption>
        
                <col width="20" />
                <col width="*" />
                <col width="120" />
        
                <thead>
                    <tr>
                        <th><input type="checkbox" class="all-colisage-select" name="all-colisage-select" /></th>
                        <th><?php print _('Conditionnement'); ?></th>
                        <th><?php print _('Quantité'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                        if($r_colisage && ria_mysql_num_rows($r_colisage) ){
                            print '
                                <tr>
                                    <td><input type="checkbox" class="check-colisage" id="colisage-select-0" name="colisage-select-0" rel="0" /></td>
                                    <td><label for="colisage-select-0">A l\'unité</label></td>
                                    <td align="center"><input type="text" class="size" id="colisage-number-0" name="colisage-number-0" value="1"/></td>
                                </tr>';
                            while( $colisage = ria_mysql_fetch_assoc($r_colisage) ){
                                print '	<tr>
                                            <td><input type="checkbox" class="check-colisage" id="colisage-select-'.$colisage['col_id'].'" name="colisage-select-'.$colisage['col_id'].'" rel="'.$colisage['col_id'].'" /></td>
                                            <td><label for="colisage-select-'.$colisage['col_id'].'">'.$colisage['col_name'].' (Qté '.round($colisage['qte']).')</label></td>
                                            <td align="center"><input type="text" class="size" id="colisage-number-'.$colisage['col_id'].'" name="colisage-number-'.$colisage['col_id'].'" value="1"/></td>
                                        </tr>';
                            }
                        }
                        else print '<tr><td colspan="3">'._('Aucun conditionnement').'</td></tr>';
                    ?>
                </tbody>
            </table>
    <?php } ?>

    <input type="submit" name="save" id="save_button" value="<?php print _('Valider ma sélection'); ?>" style="float: right;" />
</form>

<?php 
    if( !$is_ajax ){
?>
    <script>
        $(document).ready(function(){
            $(".all-colisage-select").click(function(){
                var table_id = $(this).closest('table.colisage-table').attr('id');
                if ($(this).is(":checked")){
                    $("#"+table_id+" .check-colisage").each(function(){
                        $(this).prop('checked', true);
                    });
                } else {
                    $("#"+table_id+" .check-colisage").each(function(){
                        $(this).prop('checked', false);
                    });
                }
            });

            $(".check-colisage").click(function(){
                var table_id = $(this).closest('table.colisage-table').attr('id');
                var all_checked = true;
                $("#"+table_id+" .check-colisage").each(function(){
                    if (!$(this).is(":checked")){
                        all_checked = false;
                        return false;
                    }
                });
                if (all_checked == true){
                    $("#"+table_id+" .all-colisage-select").prop('checked', true);
                } else {
                    $("#"+table_id+" .all-colisage-select").prop('checked', false);
                }
            });

            $("#save_button").click(function(){
                var reponse = true;
                $(".size").each(function(){
                    var id = $(this).val();
                    if (!$.isNumeric(id) || Math.floor(id) != id || id <= 0){
                        alert('<?php print _('Un ou plusieurs champs sont incorrects, la quantité doit être un nombre entier supérieur à 0.').'\n'._('Veuillez vérifier.'); ?>');
                        reponse = false;
                    }
                });
                if (!reponse) {
                    return false;
                }
                var colisage_ids = "";
                var colisage_qtes = "";
                var prd_ids = "";
                var count = 0;
                $(".check-colisage").each(function(){
                    if ($(this).is(":checked")){
                        var table_id = $(this).closest('table.colisage-table').attr('id');
                        if (count > 0){
                            colisage_ids += ',';
                            colisage_qtes += ',';
                            prd_ids += ',';
                        }
                        colisage_ids += $(this).attr("rel");
                        colisage_qtes += $("#"+table_id+" input#colisage-number-"+$(this).attr("rel")).val();
                        prd_ids += $("#"+table_id+" input#prd_id").val();
                        count++;
                    }
                });
                if (prd_ids == "" || colisage_ids == "" || colisage_qtes == ""){
                    alert('<?php print _('Veuillez sélectionner au moins un conditionnement, ou alors fermez la popup.'); ?>');
                    return false;
                }
                window.parent.parent_select_colisage(prd_ids, colisage_ids, colisage_qtes);

                window.parent.hidePopup();
            });
        });
    </script>

<?php
        require_once('admin/skin/footer.inc.php'); 
    }

<?php

    /** \file popup-nomenclature-selector.php
     * 
     *  Ce fichier fourni une interface graphique permettant la sélection d'une nomenclature
     *  Il est utilisé dans le process de modification de commande, pas en création.
     * 
     *  Les paramètres à utiliser sont les suivants :
     *  - prd_id : Obligatoire, identifiant du produit
     * 
     */

	// Vérifie que l'utilisateur en cours à accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $is_ajax = false;
    if (!isset($_GET['prd_id'])){
        $error = _('Il manque des paramètres');
    }

    if (!isset($error)) {
        $r_options = prd_nomenclatures_options_get( $_GET['prd_id'], 0, null, array('opt_name') );
        if(!$r_options){
            $error = _('Une erreur s\'est produite lors de la récupération des nomenclatures.');
        }
    }


    //si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Sélection de la nomenclature'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
        print '<div class="error">'.nl2br($error).'</div>';
    } elseif(!isset($error) && ria_mysql_num_rows($r_options)) {
        print '<div class="notice">'._('Veuillez sélectionner une nomenclature pour ce produit').'</div>';
    }
?>

<form method="post" action="popup-nomenclature-selector.php">
    <?php 
        if( isset($r_options) && $r_options && mysql_num_rows($r_options) ){
            print '<table class="nomenclature-group-options" style="margin-bottom:10px;"><tbody>';
            print '<caption>'._('Liste des choix').'</caption>';
            $last_name = '';

            $count = 0;
            while( $option = mysql_fetch_assoc($r_options) ){
                if (trim($last_name) != '' && $last_name != $option['opt-name']) {
                    $count = 0;
                }
                    if(!$count){
                        print ' <tr>
                                    <th align="left">'.htmlspecialchars($option['opt-name']).'</th>
                                </tr>';
                    }
                        $count++;
                        print ' <tr>
                                    <td align="center" style="border: none;">
                        ';
                            $last_name = $option['opt-name'];
                            
                            $rpoption = prd_options_products_get($option['opt']);
                            if( $rpoption && mysql_num_rows($rpoption) ){
                                $hidden_select = mysql_num_rows( $rpoption ) == 1;

                                if( $hidden_select ){
                                    $p = mysql_fetch_assoc( $rpoption );
                                    if( $rp = prd_products_get_simple( $p['prd'] ) ){
                                        $p = mysql_fetch_assoc( $rp );
                                    }
                                    
                                    mysql_data_seek( $rpoption, 0 );
                                }

                                print '<input type="hidden" class="ord-nmc-id" id="ord-nmc-id-'.$option['opt'].'" name="ord-nmc-id" value="'.$option['opt'].'"/>';
                                print '<select name="opt-'.$option['opt'].'" id="opt-'.$option['opt'].'" class="select-nomenclature present '.(mysql_num_rows($rpoption) == 1 ? 'one' : '').'" data-jcf=\'{"wrapNative": false, "wrapNativeOnMobile": false}\'>';
                                $no_selected = true;
                                    print '
                                        <option value="0">'._("Sélectionner une option").'</option>
                                    ';
                                while( $p = mysql_fetch_assoc($rpoption) ){
                                    if( $rp = prd_products_get_simple( $p['prd'] ) ){
                                        if( $p = mysql_fetch_assoc($rp) ){
                                            print '
                                                <option value="'.$p['id'].'">'.htmlspecialchars($p['title']).'</option>
                                            ';
                                        }
                                    }
                                }
                                print '</select>';
                            }
                            print '</td>';
                        print '</tr>';
            }
                print '</tbody></table>';
            print '<input type="button" id="ord-nmc-add" name="ord-nmc-add" value="'._('Ajouter au panier').'"/>';
        }
    ?>
</form>

<?php 
    if( !$is_ajax ){
?>
    <script>
        var prd_id = <?php print $_GET['prd_id']; ?>;
        $(document).ready(function(){
            $('.page_timer').hide();
            $("#ord-nmc-add").click(function(){
                var childs_ids = "";
                var nomenclature_ids = "";
                var count = 0;
                var is_addable = true;
                $(".ord-nmc-id").each(function(){
                    if (count > 0){
                        nomenclature_ids += ",";
                    }
                    nomenclature_ids += $(this).val();
                    count++;
                });
                count = 0;
                $(".select-nomenclature").each(function(){
                    if (count > 0){
                        childs_ids += ",";
                    }
                    if($(this).val() == 0){
                        is_addable = false;
                        return false;
                    }
                    childs_ids += $(this).val();
                    count++;
                });
                if (is_addable){
                    window.parent.parent_select_nomenclature(prd_id, childs_ids, nomenclature_ids);
                    
                    window.parent.hidePopup();
                } else {
                    alert("<?php print _("Veuillez sélectionner toutes les options.") ?>");
                }

            });
        });
    </script>

<?php
        require_once('admin/skin/footer.inc.php'); 
    }

<?php

	/**	\file json-products.php
	 * 	Ce fichier retourne une liste de produits filtrée en fonction des arguments. Les filtres acceptés sont les suivants :
	 * 	- p : numéro de la page à retourner
	 *  - limit : nombre de résultats maximum à retourner
	 *  - no_weight : si true, seuls les produits sans poids sont retournés
	 * 	Le tri est toujours le même, par nombre de ventes descendant puis par nom croissant.
	 * 
	 * 	Il n'est utilisé que sur la page Statistiques > "Poids des produits à compléter" dont le poids n'est pas renseigné utilisé ce fichier,
	 *  d'où le droit d'accès défini à _RGH_ADMIN_STATS_PRD_WEIGHT_FILL.
	 */
	
	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_WEIGHT_FILL');
	
	// Inclut les fonctions pour les produits
	require_once('products.inc.php');
	
	// Défini si la recherche fait partie d'une pagination
	$page = isset($_POST['p']) && is_numeric($_POST['p']) && $_POST['p']>0 ? $_POST['p'] : 1;
	
	// Détermine le nombre limite de produits retournés
	$limit = isset($_POST['limit']) && is_numeric($_POST['limit']) && $_POST['limit']>0 ? $_POST['limit'] : 18446744073709551615;

	// Active ou non le filtre sur le poids des produits
	$no_weight = isset($_POST['no_weight']) && $_POST['no_weight'] ? true : false;
	
	// Récupère les produits
	$rprd = prd_products_get(0, '', 0, true, 0, 0, -1, false, false, false, false, false, true, array("selled"=>"desc", "name"=>"asc"), false, false, false, false, false, false, null, null, false, true, false, false, false, null, false, $no_weight);
	
	// Tableau contenant tous les produits
	$products = array();
	
	if( $rprd!=false ){
		
		// On se place sur la position de départ de lecture des résultats
		if( $page>1 )
			ria_mysql_data_seek( $rprd, ($page-1)*$limit );
		
		$count = 0;
		while( $prd = ria_mysql_fetch_array($rprd) ){ 
			
			// Intégration d'un tableau contenant les informations sur un produit
			$products[] = array(
				'id'		=> $prd['id'],
				'ref'		=> $prd['ref'],
				'title'		=> $prd['title'],
				'name'		=> $prd['name'],
				'desc'		=> $prd['desc'],
				'desc-long'	=> $prd['desc-long'],
				'ecotaxe'	=> $prd['ecotaxe'],
				'barcode' 	=> $prd['barcode'],
				'price_ht' 	=> $prd['price_ht'],
				'tva_rate' 	=> $prd['tva_rate'],
				'price_ttc'	=> $prd['price_ttc'],
				'publish'	=> $prd['publish'],
				'date_published'	=> $prd['date_published'],
				'brd_id'		=> $prd['brd_id'],
				'brd_title'		=> $prd['brd_title'],
				'brd_img_id'	=> $prd['brd_img_id'],
				'brd_url'		=> $prd['brd_url'],
				'weight'		=> $prd['weight'],
				'weight_net'	=> $prd['weight_net'],
				'selled'		=> $prd['selled'],
				'length'		=> $prd['length'],
				'width'			=> $prd['width'],
				'height'		=> $prd['height'],
				'stock'			=> $prd['stock'],
				'stock_res'		=> $prd['stock_res'],
				'stock_com'		=> $prd['stock_com'],
				'stock_prepa'	=> $prd['stock_prepa'],
				'stock_livr'	=> $prd['stock_livr'],
				'centralized'	=> $prd['centralized'],
				'keywords'		=> $prd['keywords'],
				'sleep'			=> $prd['sleep'],
				'destockage'	=> $prd['destockage'],
				'taxcode'		=> $prd['taxcode'],
				'tag_title'		=> $prd['tag_title'],
				'tag_desc'		=> $prd['tag_desc'],
				'garantie'		=> $prd['garantie'],
				'is_sync'		=> $prd['is_sync'],
				'completion'	=> $prd['completion'],
				'supplier_id'	=> $prd['supplier_id'],
				'supplier_ref'	=> $prd['supplier_ref'],
				'supplier_delay'		=> $prd['supplier_delay'],
				'supplier_price'		=> $prd['supplier_price'],
				'supplier_barcode'		=> $prd['supplier_barcode'],
				'supplier_packing'		=> $prd['supplier_packing'],
				'supplier_conversion'	=> $prd['supplier_conversion']
			);
			
			// Comptabilise le nombre de produits ajoutés dans la liste
			$count++;
			
			// Si le nombre de produits fixé et atteint, on arrête la lecture du résultats
			if( $count>=$limit ){
				break;
			}
		}
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $products );

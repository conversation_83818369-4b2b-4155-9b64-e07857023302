<?php

	/**	\file ajax-pmt-specials.php
	 *	Ce fichier est appelé en ajax pour gérer les promotions spéciales.
	 */

	require_once('promotions.inc.php');
	require_once('segments.inc.php');
	require_once('prd/colisage.inc.php');

	// Vérifie que l'utilisateur en cours peut intervenir sur les promotions
	gu_if_authorized_else_403('_MDL_PROMO');
	
	if( isset($_GET['getinforef']) ){
		$info = array('id'=>0, 'name'=>'', 'ref'=>'' );

		if( isset($_GET['ref']) && trim($_GET['ref'])!='' ){
			$rp = prd_products_get_simple( 0, $_GET['ref'], false, $config['cat_root'], true );
			if( $rp && ria_mysql_num_rows($rp) ){
				$p = ria_mysql_fetch_assoc( $rp );

				$info = array( 'id'=>$p['id'], 'name'=>$p['name'], 'ref'=>$p['ref'] );

			}
		}

		print json_encode( $info );
	}elseif( isset($_GET['getallcustomers'], $_GET['idCod']) ){
		
		$cod = array( 'all-customers' => isset($_SESSION['admin_create_promo']['customers']['include']) ? $_SESSION['admin_create_promo']['customers']['include'] : 0 );
		
		$all = false;
		if( is_numeric($_GET['idCod']) && $_GET['idCod']>=0 ){
			$rcod = pmt_codes_get( $_GET['idCod'] );
			if( $rcod && ria_mysql_num_rows($rcod) ){
				$cod = ria_mysql_fetch_array($rcod);
			}
		}
		
		print json_encode( array('all' => $cod['all-customers']) );
		
	}elseif( isset($_GET['addruleCustomers'], $_GET['idCod'], $_GET['type'], $_GET['cnt'], $_GET['include']) ){
		
		$res = false;
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['customers']['rules']) ){
				$name = '';
				
				switch( $_GET['type'] ){
					case 'prf' : 
						$name = gu_profiles_get_name( $_GET['cnt'] );
						break;
					case 'seg' : 
						$segments = seg_segments_get( $_GET['cnt'] , CLS_USER );
						if( $segments && ria_mysql_num_rows($segments) ){
							$segment = ria_mysql_fetch_array($segments);
							$name = $segment['name'];
						}
						break;
					case 'usr' :
						$rusr = gu_users_get( $_GET['cnt'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$usr = ria_mysql_fetch_array( $rusr );
							$name = trim( $usr['title_name'].' '.$usr['society'].' '.$usr['adr_firstname'].' '.$usr['adr_lastname'] );
						}
						break;
				}
				
				if( trim($name)!='' ){
					if( isset($_SESSION['admin_create_promo']['customers']['rules'][$_GET['type'].'-'.$_GET['cnt']]) ){
						unset($_SESSION['admin_create_promo']['customers']['rules'][$_GET['type'].'-'.$_GET['cnt']]);
					}
					
					$_SESSION['admin_create_promo']['customers']['rules'][$_GET['type'].'-'.$_GET['cnt']] = array( 
						'type'=>$_GET['type'], 
						'id'=>$_GET['cnt'], 
						'include'=>$_GET['include'],
						'name'=>$name
					);
					
					$res = true;
				}
			}
		} else {
			switch( $_GET['type'] ){
				case 'seg' :
					$res = pmt_segments_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'prf' :
					$res = pmt_profiles_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'usr' :
					$res = pmt_users_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
			}
		}
		
		print json_encode( array('done'=>$res) );
		
	} elseif( isset($_GET['getcountcustomers'], $_GET['idCod']) ){
		print json_encode( array('count'=>pmt_codes_get_users_count( $_GET['idCod'] )) );
	} elseif( isset($_GET['getincludecustomers'], $_GET['idCod']) ){
		$ar_rules = array();

		$r_code = pmt_codes_get($_GET['idCod']);

		$code = ria_mysql_fetch_assoc($r_code);

		if( $code['type'] == 10){
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_EDIT');
		}elseif( $code['type'] == 6){
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_EDIT');
		}else{
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_EDIT');
		}
		
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['customers']['rules']) && is_array($_SESSION['admin_create_promo']['customers']['rules']) ){
				foreach( $_SESSION['admin_create_promo']['customers']['rules'] as $rule ){
					$ar_rules[ $rule['type'] ][] = array(
						'id' => $rule['id'], 'include'=>($rule['include'] ? '+' : '-'), 'name'=>$rule['name']
					);
				}
			}
		}else{
			// charge les règles pour les clients
			$rusr = pmt_users_get( $_GET['idCod'] );
			if( $rusr && ria_mysql_num_rows($rusr) ){
				
				while( $usr = ria_mysql_fetch_array($rusr) ){
					$name = $usr['title_name'].' '.$usr['firstname'].' '.$usr['lastname'].' '.$usr['society'];
					if (trim($name) == '') {
						$name = $usr['email'];
					}
					
					$ar_rules['usr'][] = array( 'id'=>$usr['id'], 'include'=>($usr['include'] ? '+' : '-'), 'name'=>$name );
				}
				
			}
			
			// charge les règles pour les profiles
			$rprf =  pmt_profiles_get( $_GET['idCod'] );
			if( $rprf && ria_mysql_num_rows($rprf) ){
				while( $prf = ria_mysql_fetch_array($rprf) ){
					$ar_rules['prf'][] = array( 'id'=>$prf['id'], 'include'=>($prf['include'] ? '+' : '-'), 'name'=>$prf['name'] );
				}
			}

			// charge les règles pour les segments 
			$rseg = pmt_segments_get( $_GET['idCod'] );
			if( $rseg && ria_mysql_num_rows($rseg) ){
				while( $seg = ria_mysql_fetch_array($rseg) ){
					$ar_rules['seg'][] = array( 'id'=>$seg['id'], 'include'=>($seg['include'] ? '+' : '-'), 'name'=>$seg['name'] );
				}
			}
		}
		
		print json_encode( $ar_rules );
		
	} elseif( isset($_GET['delrulecustomers'], $_GET['idCod'], $_GET['type'], $_GET['cnt']) ){
		
		$res = false;
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['customers']['rules'][$_GET['type'].'-'.$_GET['cnt']]) ){
				unset( $_SESSION['admin_create_promo']['customers']['rules'][$_GET['type'].'-'.$_GET['cnt']] );
				$res = true;
			}
		}else{
			foreach( $_GET['type'] as $key=>$type ){
				if( !isset($_GET['cnt'][ $key ]) ){
					continue;
				}

				$cnt = $_GET['cnt'][ $key ];
				
				switch( $type ){
					case 'prf' :
						$res = pmt_profiles_del( $_GET['idCod'], $cnt );
						break;
					case 'seg' :
						$res = pmt_segments_del( $_GET['idCod'], $cnt );
						break;
					case 'usr' :
						$res = pmt_users_del( $_GET['idCod'], $cnt );
						break;
				}
			}
		}
		
		print json_encode( array('done'=>$res) );
		
	} elseif( isset($_GET['saveallcustomers'], $_GET['idCod'], $_GET['all']) ){
		
		$res = pmt_codes_set_all_customers( $_GET['idCod'], $_GET['all'] );
		
		print json_encode( array( 'done' => $res ) );
		
	} elseif( isset($_GET['getallcatalog'], $_GET['idCod']) ){
		
		if( !$_GET['idCod'] ){
			$all = isset($_SESSION['admin_create_promo']['products']['include']) ? $_SESSION['admin_create_promo']['products']['include'] : 1;
			$include_pmt = isset($_SESSION['admin_create_promo']['products']['promo']) ? $_SESSION['admin_create_promo']['products']['promo'] : 0;
			$only_destock = isset($_SESSION['admin_create_promo']['products']['destock']) ? $_SESSION['admin_create_promo']['products']['destock'] : 0;
		}else{
			$all = false;
			$include_pmt = $only_destock = true;

			if( is_numeric($_GET['idCod']) && $_GET['idCod']>=0 ){
				$rcod = pmt_codes_get( $_GET['idCod'] );
				if( $rcod && ria_mysql_num_rows($rcod) ){
					$cod = ria_mysql_fetch_assoc( $rcod );

					$all = $cod['all-catalog'];
					$include_pmt = $cod['include_pmt'];
					$only_destock = $cod['only_destock'];
				}
			}
		}
		
		print json_encode( array('all' => $all, 'include_pmt' => $include_pmt, 'only_destock' => $only_destock) );
		
	} elseif( isset($_GET['addrule'], $_GET['idCod'], $_GET['type'], $_GET['cnt'], $_GET['include']) ){
		
		$res = false;
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['products']['rules']) ){
				if( isset($_SESSION['admin_create_promo']['products']['include'][$_GET['type'].'-'.$_GET['cnt']]) ){
					unset( $_SESSION['admin_create_promo']['products']['include'][$_GET['type'].'-'.$_GET['cnt']] );
				}
				
				$name = ''; $products = '';
				switch( $_GET['type'] ){
					case 'prd' :
						$name = prd_products_get_name( $_GET['cnt'] );
						break;
					case 'cat' :
						$name = prd_categories_get_name( $_GET['cnt'] );
						$products = prd_categories_get_prd_count( $_GET['cnt'] );
						break;
					case 'brd' :
						$rbrd = prd_brands_get( $_GET['cnt'] );
						if( $rbrd && ria_mysql_num_rows($rbrd) ){
							$brd = ria_mysql_fetch_array( $rbrd );
							
							$name = $brd['name'];
							$products = $brd['products'];
						}
						break;
					case 'set' :
						$name = 'Références '.str_replace( ';~;', ' à ', $_GET['cnt'] );
						break;
				}
				
				if( trim($name)!='' ){
					$_SESSION['admin_create_promo']['products']['rules'][$_GET['type'].'-'.$_GET['cnt']] = array(
						'type'=>$_GET['type'],
						'id'=>$_GET['cnt'],
						'include'=>$_GET['include'],
						'name'=>$name,
						'prds'=>(trim($products)!='' && $products>=0 ? $products : '')
					);
					
					$res = true;
				}
			}
		}else{
			switch( $_GET['type'] ){
				case 'prd' :
					if( preg_match('/ref-/', $_GET['cnt']) ){
						$_GET['cnt'] = str_replace('ref-', '', $_GET['cnt']);
						$_GET['cnt'] = prd_products_get_id( $_GET['cnt'] );
						if( !is_numeric($_GET['cnt']) || $_GET['cnt']<=0 ){
							$res = false;
							break;
						}
					}
					$res = pmt_products_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'cat' :
					$res = pmt_categories_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'brd' :
					$res = pmt_brands_add( $_GET['idCod'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'set' :
					$ref = explode( ';~;', $_GET['cnt'] );
					if( !is_array($ref) || sizeof($ref)!=2 ){
						$res = false;
					} else {
						$res = pmt_products_sets_add( $_GET['idCod'], $ref[0], $ref[1], $_GET['include'] );
					}
					break;
			}
		}
		
		print json_encode( array('done' => $res) );
		
	} elseif( isset($_GET['getincludeprd'], $_GET['idCod']) ){
		
		$ar_rules = array();

		$r_code = pmt_codes_get($_GET['idCod']);
		
		$code = ria_mysql_fetch_assoc($r_code);
		
		if( $code['type'] == 10){
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_EDIT');
		}elseif( $code['type'] == 6){
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_EDIT');
		}else{
			$ar_rules['can-edit'] = gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_EDIT');
		}
		
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['products']['rules']) && is_array($_SESSION['admin_create_promo']['products']['rules']) ){
				foreach( $_SESSION['admin_create_promo']['products']['rules'] as $rule ){
					$ar_rules[ $rule['type'] ][] = array(
						'id'	 	=> $rule['id'], 
						'include'	=> ($rule['include'] ? '+' : '-'), 
						'name'		=> $rule['name'], 
						'prds'		=> (trim($rule['prds'])!='' ? $rule['prds'] : '')
					);
				}
			}
		}else{
			// charge les règles pour les produits
			$rprd = pmt_products_get( $_GET['idCod'] );
			if( $rprd && ria_mysql_num_rows($rprd) ){
				
				while( $prd = ria_mysql_fetch_array($rprd) ){
					$prd['discount'] = str_replace( '.000', '', number_format($prd['discount'], 3, '.', '') );

					$name_prd = $prd['ref'].' - '.$prd['name'];
					$name_prd = str_replace( $prd['ref'].' - '.$prd['ref'], $prd['ref'], $name_prd );
					
					$prd_col = array();
					
					$r_pmt_prd = pmt_products_colisage_get( $_GET['idCod'], $prd['id'] );
					if( $r_pmt_prd ){
						while( $pmt_prd = ria_mysql_fetch_assoc($r_pmt_prd) ){
							if( $pmt_prd['colisage'] !== null){
								if( $pmt_prd['colisage'] == 0 ){
									$prd_col[] = array(
										'prd_id'		=> $pmt_prd['prd_id'],
										'pmt_id'		=> $pmt_prd['pmt_id'],
										'col_id'		=> $pmt_prd['colisage'],
										'id'			=> $pmt_prd['id'],
										'name'			=> 'A l\'unité',
									);
								}else{
									$prd_col[] = array(
										'prd_id'		=> $pmt_prd['prd_id'],
										'pmt_id'		=> $pmt_prd['pmt_id'],
										'col_id'		=> $pmt_prd['colisage'],
										'qte'			=> $pmt_prd['colisage_qte'],
										'id'			=> $pmt_prd['id'],
										'name'			=> $pmt_prd['colisage_name'],
									);
								}
							}
						}
					}

					$ar_rules['prd'][] = array( 
						'id'			=> $prd['id'], 
						'name'			=> $name_prd,
						'include'		=> ($prd['include'] ? '+' : '-'), 
						'url' 			=> '/admin/catalog/product.php?cat=0&prd='.$prd['id'],
						'discount'		=> is_numeric($prd['discount']) && $prd['discount'] > 0 ? $prd['discount'] : '',
						'discountType' 	=> is_numeric($prd['discount_type']) && $prd['discount_type'] >= 0 ? $prd['discount_type'] : '',
						'prd_col'		=> $prd_col,
					);
				}

				// charge les types de conditionnement disponible
				$colisage = prd_colisage_types_get(0, false, array('rname' => 'asc', 'qte' => 'asc'));
				if ($colisage) {
					while ($col = ria_mysql_fetch_array($colisage)) {
						$ar_rules['prd']['col'][] = array(
							'id'		=> $col['id'],
							'name'		=> $col['name'],
							'qte'		=> $col['qte'],
						);
					}
				}
				
			}
			
			// charge les règles pour les catégories
			$rcat =  pmt_categories_get( $_GET['idCod'] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				
				while( $cat = ria_mysql_fetch_array($rcat) ){
					$cat['discount'] = str_replace( ',000', '', number_format($cat['discount'], 3, ',', '') );

					$ar_rules['cat'][] = array( 
						'id'			=> $cat['id'], 
						'name'			=> $cat['name'], 
						'include'		=> ($cat['include'] ? '+' : '-'), 
						'prds'			=> $cat['products'],
						'url' 			=> '/admin/catalog/index.php?cat='.$cat['id'],
						'discount'		=> is_numeric($cat['discount']) && $cat['discount'] > 0 ? $cat['discount'] : '',
						'discountType' 	=> is_numeric($cat['discount_type']) && $cat['discount_type'] >= 0 ? $cat['discount_type'] : '',
					);
				}
				
			}
			
			// charge les règles pour les marques
			$rbrd =  pmt_brands_get( $_GET['idCod'] );
			if( $rbrd && ria_mysql_num_rows($rbrd) ){
				
				while( $brd = ria_mysql_fetch_array($rbrd) ){
					$brd['discount'] = str_replace( ',000', '', number_format($brd['discount'], 3, ',', '') );

					$ar_rules['brd'][] = array( 
						'id'			=> $brd['id'], 
						'name'			=> $brd['name'], 
						'include'		=> ($brd['include'] ? '+' : '-'), 
						'prds'			=> $brd['products'],
						'url'			=> '/admin/catalog/index.php?brd='.$brd['id'],
						'discount'		=> is_numeric($brd['discount']) && $brd['discount'] > 0 ? $brd['discount'] : '',
						'discountType' 	=> is_numeric($brd['discount_type']) && $brd['discount_type'] >= 0 ? $brd['discount_type'] : '',
					);
				}
				
			}
			
			// charge les règles pour les marques
			$rset =  pmt_products_sets_get( $_GET['idCod'] );
			if( $rset && ria_mysql_num_rows($rset) ){
				
				while( $set = ria_mysql_fetch_array($rset) ){
					$set['discount'] = str_replace( ',000', '', number_format($set['discount'], 3, ',', '') );
					
					$name = 'Références '.$set['ref_start'].' à '.$set['ref_stop'];
					$ar_rules['set'][] = array( 
						'id'		=> $set['ref_start'].';~;'.$set['ref_stop'], 
						'name'		=> $name, 
						'include'	=> ($set['include'] ? '+' : '-'), 
						'prds'		=> $set['products'],
						'discount'		=> is_numeric($set['discount']) && $set['discount'] > 0 ? $set['discount'] : '',
						'discountType' 	=> is_numeric($set['discount_type']) && $set['discount_type'] >= 0 ? $set['discount_type'] : '',
					);
				}
				
			}
		}
		
		print json_encode( $ar_rules );
		
	} elseif( isset($_GET['delrule'], $_GET['idCod'], $_GET['type'], $_GET['cnt']) ){
		
		$res = false;
		if( !$_GET['idCod'] ){
			if( isset($_SESSION['admin_create_promo']['products']['rules'][$_GET['type'].'-'.$_GET['cnt']]) ){
				unset( $_SESSION['admin_create_promo']['products']['rules'][$_GET['type'].'-'.$_GET['cnt']] );
			}
		}else{
			foreach( $_GET['type'] as $key=>$type ){
				if( !isset($_GET['cnt'][ $key ]) ){
					continue;
				}

				$cnt = $_GET['cnt'][ $key ];

				switch( $type ){
					case 'prd' :
						$res = pmt_products_del( $_GET['idCod'], $cnt );
						break;
					case 'cat' :
						$res = pmt_categories_del( $_GET['idCod'], $cnt );
						break;
					case 'brd' :
						$res = pmt_brands_del( $_GET['idCod'], $cnt );
						break;
					case 'set' :
						$ref = explode( ';~;', $cnt );
						if( !is_array($ref) || sizeof($ref)!=2 ){
							$res = false;
						} else {
							$res = pmt_products_sets_del( $_GET['idCod'], $ref[0], $ref[1] );
						}
						break;
				}
			}
		}
		
		print json_encode( array( 'done' => $res ) );
		
	} elseif( isset($_REQUEST['saveallcatalog'], $_REQUEST['idCod'], $_REQUEST['all'], $_REQUEST['include_pmt'], $_REQUEST['only_destock']) ){
		
		$res = pmt_codes_set_all_catalog( $_REQUEST['idCod'], $_REQUEST['all'] );
		$res = $res && pmt_codes_set_include_pmt( $_REQUEST['idCod'], $_REQUEST['include_pmt'] );
		$res = $res && pmt_codes_set_only_destock( $_REQUEST['idCod'], $_REQUEST['only_destock'] );

		if( isset($_REQUEST['prd-discount']) ){
			$error = false;

			foreach ($_REQUEST['prd-discount'] as $type => $objects) {
				foreach ($objects as $id => $val) {
					$val = !is_numeric($val) || $val <=0 ? null : $val;
					$typ = isset($_REQUEST['prd-discount-type'][ $type ][ $id ]) && in_array($_REQUEST['prd-discount-type'][ $type ][ $id ], pmt_discount_type_get_all()) ? $_REQUEST['prd-discount-type'][ $type ][ $id ] : false;
					
					if( $typ === false ){
						$val = $typ = null;
					}

					switch ($type) {
						case 'prd':
							if( !pmt_products_set_discount($_REQUEST['idCod'], $id, $val, $typ) ){
								$error = true;
							}
							break;
						case 'cat':
							if( !pmt_categories_set_discount($_REQUEST['idCod'], $id, $val, $typ) ){
								$error = true;
							}
							break;
						case 'brd':
							if( !pmt_brands_set_discount($_REQUEST['idCod'], $id, $val, $typ) ){
								$error = true;
							}
							break;
						case 'set':
							$ref = explode( ';~;', $id );
							if( !is_array($ref) || count($ref)!=2 ){
								$error = true;
							}elseif( !pmt_products_sets_set_discount($_REQUEST['idCod'], $ref[0], $ref[1], $val, $typ) ){
								$error = true;
							}
					}

					if( $error ){
						$res = false;
						break(2);
					}
				}
			}
		}
		print json_encode( array( 'done' => $res ) );

	} elseif( isset($_GET['generated']) ){
		
		$code = pmt_codes_generated();
		print strtoupper( $code );
		
	} elseif( isset($_GET['getCdt'], $_GET['type']) ){
	
		print json_encode( pmt_conditions_get_array(0, $_GET['type']) );
		
	} elseif( isset($_GET['getSymbols'], $_GET['cdt']) ){
		
		print json_encode( pmt_condition_symbols_get_array($_GET['cdt']) );
		
	} elseif( isset($_GET['getForm'], $_GET['cdt']) ){
		
		$ar_data = array(
			'type' => pmt_conditions_get_type( $_GET['cdt'] ),
			'data' => array()
		);
		
		switch( $_GET['cdt'] ){
			case _PMT_CDT_SRV :
				$rsrv = dlv_services_get( 0, true );
				if( $rsrv ){
					while( $srv = ria_mysql_fetch_array($rsrv) ){
						$ar_data['data'][] = array( 'id' => $srv['id'], 'name' => $srv['name'] );
					}
				}
				break;
			case _PMT_CDT_PAY_TYPE :
				$rpay = ord_payment_types_get();
				if( $rpay ){
					while( $pay = ria_mysql_fetch_array($rpay) ){
						$ar_data['data'][] = array( 'id' => $pay['id'], 'name' => $pay['name'] );
					}
				}
				break;
			case _PMT_CDT_PRD_COL_TYPE:
				$rcol = prd_colisage_types_get(0);

				if ($rcol) {
					while ($col = ria_mysql_fetch_array($rcol)) {
						$ar_data['data'][] = array(
							'id' => $col['id'], 
							'name' => $col['name'] . ' (Qté: ' . str_replace(',00', '', number_format($col['qte'], 2, ',', '')) . ')'
						);
					}
				}
				break;
		}
		
		print json_encode( $ar_data );
	} elseif( isset($_GET['savePmt']) ){
		if( !in_array($_GET['typePromo'], array(_PMT_TYPE_CODE, _PMT_TYPE_REWARD, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS, _PMT_TYPE_CREDIT)) && (!isset($_GET['pmt-label']) || trim($_GET['pmt-label'])=='') ){
			$error = _("Veuillez renseigner un libellé pour cette promotion.");
		} elseif( in_array($_GET['typePromo'], array(_PMT_TYPE_CODE, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS, _PMT_TYPE_CREDIT)) && (!isset($_GET['code']) || trim($_GET['code'])=='') ){
			$error = _("Veuillez renseigner un code pour cette promotion.");
		} elseif( isset($_GET['date-start']) && trim($_GET['date-start'])!='' && !isdate($_GET['date-start']) ){
			$error = _("Veuillez renseigner une date de début valide, au format JJ/MM/YYYY.");
		} elseif( isset($_GET['date-stop']) && trim($_GET['date-stop'])!='' && !isdate($_GET['date-stop']) ){
			$error = _("Veuillez renseigner une date de fin valide, au format JJ/MM/YYYY.");
		} elseif( $_GET['idCod']<=0 && isset($_GET['code']) && trim($_GET['code']) && pmt_codes_exists(null, $_GET['code']) ){
			$error = sprintf(_("Le code promotion \"%s\" existe déjà."), htmlspecialchars($_GET['code']));
		} elseif( $_GET['typePromo'] == _PMT_TYPE_SOLDES && !pmt_soldes_date_in_period($_GET['date-start']) && !pmt_soldes_date_in_period($_GET['date-stop']) ){
			$error = _("Les dates saisies ne correspondent à aucune période de soldes.");
		} else {
			switch( $_GET['typePromo'] ){
				case _PMT_TYPE_CODE :
				case _PMT_TYPE_CREDIT : 
				case _PMT_TYPE_REWARD :
				case _PMT_TYPE_GIFTS :
				case _PMT_TYPE_REMISE :
				case _PMT_TYPE_SOLDES :
				{
					$_GET['discount'] = isset($_GET['discount']) ? str_replace(',', '.', $_GET['discount']) : 0;
					if( !isset($_GET['discount']) || !is_numeric($_GET['discount']) || $_GET['discount']<0 ){
						$error = _("Veuillez renseigner le montant de la réduction.");
					}
					
					break;
				}
				case _PMT_TYPE_PRD :
				{	
					$ar_pops = array();
					if( !isset($_GET['list-pop-ref']) || !is_array($_GET['list-pop-ref']) || !sizeof($_GET['list-pop-ref']) ){
						$error = _("Veuillez définir un ou plusieurs produits offerts.");
					}else{
						foreach( $_GET['list-pop-ref'] as $key=>$prd_ref ){
							$prd_id = prd_products_get_id( $prd_ref, $config['cat_root'] );
							if( !$prd_id ){
								$prd_id = prd_products_get_id( $prd_ref );
							}
							
							if( $prd_id ){
								$qty = isset($_GET['list-pop-qty'][$key]) && is_numeric($_GET['list-pop-qty'][$key]) && $_GET['list-pop-qty'][$key] ? $_GET['list-pop-qty'][$key] : 1;

								$ar_pops[ $prd_id ] = $qty;
							}else{
								$error = sprintf(_('La référence "%s" ne fait plus ou pas parti de vos références.'), htmlspecialchars( $prd_ref ));
							}
						}
					}
					
					break;
				}
				case _PMT_TYPE_REDUC :
				{
					if( !isset($_GET['buy']) || !is_numeric($_GET['buy']) || $_GET['buy']<=0 ){
						$error = _("Veuillez préciser une quantité achetée supérieure à zéro pour profiter de cette réduction dégressive.");
					} elseif( !isset($_GET['get'], $_GET['nb']) || !is_array($_GET['get']) || !is_array($_GET['nb']) ){
						$error = _("Veuillez renseigner au moins une ligne de réduction dégressive.");
					} else {
					
						$g_num = true;
						foreach( $_GET['get'] as $g ){
							if( !is_numeric($g) || $g<=0 ){
								$g_num = false;
								break;
							}
						}
						
						$nb_num = true; 
						$nb_inf_buy = false;
						$pos = 0;
						foreach( $_GET['nb'] as $nb ){
							if( !is_numeric($nb) || $nb<=0 ){
								$nb_num = false;
								break;
							} elseif( $nb<=$_GET['buy'] || $nb<=$pos ){
								$nb_inf_buy = true;
								break;
							}
							
							$pos = $nb;
						}
						
						if( !$g_num ){
							$error = _("Chaque pourcentage de réduction doit être une valeur numérique supérieure à 0.");
						} elseif( !$nb_num ){
							$error = _("Chaque position du produit doit être une valeur numérique supérieure à 0.");
						} elseif( $nb_inf_buy ){
							$error = _("Chaque position du produit doit être supérieure à la quantité achetée ainsi qu'à la position saisie à la ligne précédente.")."<br />"._("Par exemple : 1 acheté est égal à 50 % sur le 2ème et 75% sur le 3ème.");
						}
					}
					
					if( !isset($error) ){
						if( isset($_GET['cdt-type']) ){
							$not_completed = false;
							$ar_type_spe = array( _PMT_CDT_SRV, _PMT_CDT_PAY_TYPE, _PMT_CDT_PRD_COL_TYPE );
							
							foreach( $_GET['cdt-general'] as $grp=>$rule_items ){
								foreach( $_GET['cdt-type'][$grp] as $k=>$type ){
									if( !is_numeric($type) || $type<=0 ){
										$not_completed = true;
									}elseif( !isset($_GET['cdt-symbol'][$grp][$k]) || $_GET['cdt-symbol'][$grp][$k]=='-1' ){
										$not_completed = true;
									}elseif( !isset($_GET['cdt-value'][$grp][$k]) || !((in_array($type, $ar_type_spe) && $_GET['cdt-value'][$grp][$k]!='-1') || (!in_array($type, $ar_type_spe) && trim($_GET['cdt-value'][$grp][$k])!='')) ){
										$not_completed = true;
									}
								}
							}
							
							
							if( $not_completed ){
								$error = _("Une ou plusieurs conditions ne sont pas complètement renseignées.");
							}
						}
					}
					
					break;
				}
				case _PMT_TYPE_BUY_X_FREE_Y :
				{
					if( !isset($_GET['buy']) || !is_numeric($_GET['buy']) || $_GET['buy']<=0 ){
						$error = _("Veuillez renseigner une quantité achetée supérieure à zéro.");
					} elseif( !isset($_GET['free']) || !is_numeric($_GET['free']) || $_GET['free']<=0 ){
						$error = _("Veuillez renseigner une quantité offerte supérieure à zéro.");
					}
					break;
				}
				case _PMT_TYPE_CHEEKBOOK :
				{
					if( !isset($_GET['idCod']) || !pmt_codes_exists($_GET['idCod']) ){
						if( !isset($_GET['pmt-nb-cheekbook']) || !is_numeric($_GET['pmt-nb-cheekbook']) || $_GET['pmt-nb-cheekbook']<=0 ){
							$error = _("Veuillez renseigner un nombre de chéquiers à générer supérieur à zéro.");
						}
						if( !isset($_GET['pmt-qte-gen']) || !is_numeric($_GET['pmt-qte-gen']) || $_GET['pmt-qte-gen']<=0 ){
							$error = _("Veuillez préciser un nombre de chèques présents dans chaque chéquier supérieur à zéro.");
						}
					}
				}
				case _PMT_TYPE_BA :
				{
					if( !isset($_GET['idCod']) || !pmt_codes_exists($_GET['idCod']) ){
						if( !isset($_GET['pmt-qte-gen']) || !is_numeric($_GET['pmt-qte-gen']) || $_GET['pmt-qte-gen']<=0 ){
							$error = _("Veuillez renseigner une quantité de bons d'achat à générer supérieure à zéro.");
						}
					}
					break;
				}
			}
			
			// enregistrement des informations de la promotion
			if( !isset($error) ){
				$type = $_GET['typePromo'];
				
				$label 		 		= isset($_GET['pmt-label']) && trim($_GET['pmt-label'])!='' ? $_GET['pmt-label'] : false;
				$code 		 		= isset($_GET['code']) && trim($_GET['code'])!='' ? $_GET['code'] : false;
				$date_start  		= isset($_GET['date-start']) && trim($_GET['date-start']) ? $_GET['date-start'] : null;
				$date_stop 	 		= isset($_GET['date-stop']) && trim($_GET['date-stop']) ? $_GET['date-stop'] : null;
				$used_max 	 		= isset($_GET['used-max']) ? $_GET['used-max'] : 0;
				$reusable 	 		= isset($_GET['reusable']) && $_GET['reusable']=='0' ? false : true;
				$include_pmt 		= isset($_GET['include_pmt']) && $_GET['include_pmt'] ? true : false;
				$only_destock 		= isset($_GET['only_destock']) && $_GET['only_destock'] ? true : false;
				$first_order 		= isset($_GET['first_order']) && $_GET['first_order'] ? true : false;
				$available_stocks 	= isset($_GET['available_stocks']) && $_GET['available_stocks'] ? true : false;
				$prd_in_cart 		= isset($_GET['prd_in_cart']) && $_GET['prd_in_cart'] ? true : false;
				$one_by_cart 		= isset($_GET['one_by_cart']) && $_GET['one_by_cart'] ? true : false;
				$tva_rate			= ria_array_get($_GET, 'discount-tva', _TVA_RATE_DEFAULT);
				
				$hour_start	 = isset($_GET['hour-start']) && ishour($_GET['hour-start']) ? $_GET['hour-start'] : '00:00:00';
				$hour_stop	 = isset($_GET['hour-stop']) && ishour($_GET['hour-stop']) ? $_GET['hour-stop'] : '23:59:59';
				$parent 	 = in_array($type, array(_PMT_TYPE_BA,_PMT_TYPE_CHEEKBOOK)) ? 0 : null;

				$date_start  = $date_start.' '.$hour_start;
				$date_stop   = $date_stop.' '.$hour_stop;
				
				// enregistre l'entête de la promotion
				if( !isset($_GET['idCod']) || !pmt_codes_exists($_GET['idCod']) ){
					$cod = pmt_codes_add( $label, $type, $code, $_GET['pmt-cmt'], $parent );
					if( !$cod ){
						$error = _("Une erreur inattendue s'est produite lors de la création de la promotion.");
					}
				} else {
					$old_cod = false;
					$rc = pmt_codes_get( $_GET['idCod'] );
					if( $rc && ria_mysql_num_rows($rc) ){
						$old_cod = ria_mysql_fetch_array( $rc );
					}

					$cod = $_GET['idCod'];
					// mise à jour de l'entête de promotion
					$upd = pmt_codes_update( $_GET['idCod'], $label, $code, $_GET['pmt-cmt'] );
					if( !$upd ){
						$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.");
					}
				}
				
				// enregistre le bénéfice de la promotion (selon le type)
				if( !isset($error) ){
					$ar_cods = array( $cod );
					
					// supprime les bénéfices actuels pour sauvegarder les nouveaux
					if( !pmt_offers_del($cod) ){
						$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des bénéfices de la promotion.");
					} else {
						
						switch( $_GET['typePromo'] ){
							case _PMT_TYPE_CODE :
							case _PMT_TYPE_REWARD :
							case _PMT_TYPE_BA :
							case _PMT_TYPE_CHEEKBOOK :
							case _PMT_TYPE_GIFTS :
							case _PMT_TYPE_REMISE :
							case _PMT_TYPE_SOLDES :
							case _PMT_TYPE_CREDIT :
								$discount_type 	= isset($_GET['discount-type']) ? $_GET['discount-type'] : 1;
								$apply_on 		= isset($_GET['remise-on']) ? $_GET['remise-on'] : 'order';
								
								if( !pmt_offers_add($cod, $type, $_GET['discount'], $discount_type, '', 0, 0, 0, $apply_on, $date_start, $date_stop, $used_max, $reusable, false, $include_pmt, $first_order, $available_stocks, false, false, true, $only_destock,$tva_rate ) ){
									$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des bénéfices de la promotion.");
								}
								
								break;
							case _PMT_TYPE_PRD :
								$off_id = pmt_offers_add( 
									$cod, $type, 0, 0, sizeof($ar_pops), 0, 0, 0, null, $date_start, $date_stop, $used_max, $reusable, false, $include_pmt, $first_order, $available_stocks, $ar_pops, $prd_in_cart, $one_by_cart, $only_destock,$tva_rate
								);

								if( !$off_id ){
									$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des bénéfices de la promotion.");
								}
								
								break;
							
							case _PMT_TYPE_REDUC :
								
								foreach( $_GET['get'] as $k=>$g ){
									$prd_pos = $_GET['nb'][ $k ];
									$discount = $_GET['get'][ $k ];
									
									if( !pmt_offers_add($cod, $type, $discount, 1, '', $_GET['buy'], 0, $prd_pos, null, $date_start, $date_stop, $used_max, $reusable, false, $include_pmt, $first_order, $available_stocks, false, false, true, $only_destock,$tva_rate) ){
										$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des bénéfices de la promotion.");
										break;
									}
								}
								
								break;
							
							case _PMT_TYPE_BUY_X_FREE_Y :

								if( !pmt_offers_add($cod, $type, 0, 0, '', $_GET['buy'], $_GET['free'], 0, null, $date_start, $date_stop, $used_max, $reusable, false, $include_pmt, $first_order, $available_stocks, false, false, true, $only_destock, $tva_rate) ){
									$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des bénéfices de la promotion.");
								}
								
								break;
						}
					}

					// Si le nombre de sites est égal à 1, la promotion est applicable par défaut sur celui-ci
					$rwst = wst_websites_get();
					if( $rwst && ria_mysql_num_rows($rwst)==1 ){
						while( $wst = ria_mysql_fetch_array($rwst) ){
							$_GET['wst'][] = $wst['id'];
						}
					}

					// Enregistre les sites internets sur lesquels la promotion est applicable
					if( isset($_GET['wst']) && is_array($_GET['wst']) && sizeof($_GET['wst']) ){
						if( !pmt_websites_update($cod, $_GET['wst']) ){
							// $error = "Une erreur inattendue s'est prouite lors de la sauvegarde des sites internets où la promotion est applicable.";
						}
					}elseif( !pmt_websites_del($cod) ){
						// $error = "Une erreur inattendue s'est prouite lors de la sauvegarde des sites internets où la promotion est applicable.";
					}
				}
			
				// enregistre les conditions
				if( !isset($error) && !pmt_code_groups_del_all($cod) ){
					$error = _("Une erreur inattendue s'est produite lors de la création des conditions.");
				}elseif( isset($_GET['cdt-general']) && is_array($_GET['cdt-general']) && sizeof($_GET['cdt-general']) ){
					// supprime les conditions actuelles pour sauvegarder les nouvelles
					foreach( $_GET['cdt-general'] as $grp=>$rule_items ){
						if( !isset($_GET['cdt-type'][$grp]) || !sizeof($_GET['cdt-type'][$grp]) ){
							continue;
						}
						
						$rule = isset($_GET['cdt-between'][$grp]) ? $_GET['cdt-between'][$grp] : 'and';
						if( !($new_grp = pmt_code_groups_add( $cod, $rule, $rule_items)) ){
							$error = _("Une erreur inattendue s'est produite lors de la création des groupes de conditions.");
						} else {
							
							// enregistrement des conditions par groupe
							foreach( $_GET['cdt-type'][$grp] as $k=>$c ){
								if( trim($c)=='' ){
									continue;
								}
								
								$psy = $_GET['cdt-symbol'][$grp][$k];
								$val = $_GET['cdt-value'][$grp][$k];
								$apply_on = null;
								if( isset($_GET['cdt-calculated'][$grp][$k]) && in_array($_GET['cdt-calculated'][$grp][$k], array('order', 'rules')) ){
									$apply_on = $_GET['cdt-calculated'][$grp][$k];
								}
								
								$same_qty = null;
								if( isset($_GET['same-qty'][$grp][$k]) && $_GET['same-qty'][$grp][$k] ){
									$same_qty = $_GET['same-qty'][$grp][$k];
								}
								
								if( !pmt_code_conditions_add( $cod, $c, $psy, $new_grp, $val, $apply_on, $same_qty) ){
									$error = _("Une erreur inattendue s'est produite lors de la création des conditions.");
								}
							}
							
						}
					}
				}
			
				if( !isset($error) ){
					$_GET['chk_srv'] = isset($_GET['chk_srv']) && is_array($_GET['chk_srv']) && sizeof($_GET['chk_srv']) ? $_GET['chk_srv'] : false;
					if( !pmt_codes_services_update($cod, $_GET['chk_srv']) ){
						$error = _("Une erreur s'est produite lors de l'enregistrement des services de livraison pour lesquels les frais de port sont offerts");
					}
				}
				
				$include_pmt = isset($_GET['include_pmt']) ? true : (isset($old_cod['include_pmt']) && $old_cod['include_pmt'] ? true : false);
				$only_destock = isset($_GET['only_destock']) ? true : (isset($old_cod['only_destock']) && $old_cod['only_destock'] ? true : false);
				
				$tres = true;
				
				// Mise à jour de l'information d'inclusion/exclusion de tout le catalogue
				if( $tres && !pmt_codes_set_all_catalog($cod, (isset($_GET['pmt-all-catalog']) && $_GET['pmt-all-catalog'] ? true : false)) ){
					$error = _("Une erreur s'est produite lors de l'enregistrement de la règle d'inclusion générale sur catalogue.");
				}
				
				// Mise à jour de l'information d'inclusion des promotions
				if( !pmt_codes_set_include_pmt($cod, $include_pmt) ){
					$tres = false;
				}

				if( !pmt_codes_set_only_destock($cod, $only_destock) ){
					$tres = false;
				}
				
				// Enregistre les règles d'inclusion / exclusion de produits
				if( $tres && isset($_SESSION['admin_create_promo']['products']['rules']) && is_array($_SESSION['admin_create_promo']['products']['rules']) ){
					foreach( $_SESSION['admin_create_promo']['products']['rules'] as $rule ){
						switch( $rule['type'] ){
							case 'prd' :
								if( preg_match('/ref-/', $rule['id']) ){
									$rule['id'] = str_replace('ref-', '', $rule['id']);
									$rule['id'] = prd_products_get_id( $rule['id'] );
									if( !is_numeric($rule['id']) || $rule['id']<=0 ){
										$tres = false;
										break;
									}
								}
								$tres = pmt_products_add( $cod, $rule['id'], $rule['include'] );
								break;
							case 'cat' :
								$tres = pmt_categories_add( $cod, $rule['id'], $rule['include'] );
								break;
							case 'brd' :
								$tres = pmt_brands_add( $cod, $rule['id'], $rule['include'] );
								break;
							case 'set' :
								$ref = explode( ';~;', $rule['id'] );
								if( !is_array($ref) || sizeof($ref)!=2 ){
									$tres = false;
								} else {
									$tres = pmt_products_sets_add( $cod, $ref[0], $ref[1], $rule['include'] );
								}
								break;
						}
						
						if( !$tres ){
							$error = _("Une erreur s'est produite lors de l'enregistrement des règles d'inclusion / d'exclusion des produits.");
							break;
						}
					}
				}
				
				// Mise à jour de l'information d'inclusion/exclusion de tous les comptes clients
				if( $tres && !pmt_codes_set_all_customers($cod, isset($_GET['pmt-all-customers']) && $_GET['pmt-all-customers'] ? true : false) ){
					$error = _("Une erreur s'est produite lors de l'enregistrement de la règle d'inclusion générale des comptes clients.");
				}
				
				// Enregistre les règles d'inclusion/exclusion des comptes clients
				if( $tres && isset($_SESSION['admin_create_promo']['customers']['rules']) && is_array($_SESSION['admin_create_promo']['customers']['rules']) ){
					foreach( $_SESSION['admin_create_promo']['customers']['rules'] as $rule ){
						switch( $rule['type'] ){
							case 'prf' :
								$tres = pmt_profiles_add( $cod, $rule['id'], $rule['include'] );
								break;
							case 'seg' :
								$tres = pmt_segments_add( $cod, $rule['id'], $rule['include'] );
								break;
							case 'usr' :
								$tres = pmt_users_add( $cod, $rule['id'], $rule['include'] );
								break;
						}
						
						if( !$tres ){
							$error = _("Une erreur s'est produite lors de l'enregistrement des règles d'inclusion / d'exclusion des comptes clients.");
							break;
						}
					}
				}
				
				// Enregistre les champs avancés
				$ar_fld = array();
				if( !isset($error) ){
					if( $fields = fld_fields_get( 0, 0, 0, 0, 0, $_GET['idCod'], null, array(), false, array(), null, CLS_PMT_CODE) ){
						while( $f = ria_mysql_fetch_array($fields) ){
							if( !$f['is-sync'] ){
								if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_GET['fld'.$f['id']]) ){
									$_GET['fld'.$f['id']] = '';
								}
								
								if( isset($_GET['fld'.$f['id']]) ){
									$value = $_GET['fld'.$f['id']];
									fld_object_values_set( $cod, $f['id'], $value );
									
									$ar_fld[] = $f['id'];
								}
							}
						}
					}
				}
				
				if( !isset($error) && sizeof($ar_fld) ){
					$rmdl = fld_fields_get_models( $ar_fld );
					while( $mdl = ria_mysql_fetch_array($rmdl) ){
						if( $mdl['cls_id']==CLS_PMT_CODE ){
							if( !fld_object_models_add( $cod, $mdl['id'] ) ){
								$error = _("L'ajout du modèle de saisie a échoué pour une raison inconnue.");
								break;
							}
						} else {
							$error = _("Le modèle de saisie sélectionné n'est pas valide.");
							break;
						}
					}
				}
				
				// Enregistre les segments
				if( !isset($error) ){
					if( isset($_SESSION['admin_create_promo']['customers']['segments']) && is_array($_SESSION['admin_create_promo']['customers']['segments']) ){
						foreach( $_SESSION['admin_create_promo']['customers']['segments'] as $seg ){
							if( !seg_objects_add( CLS_PMT_CODE, $cod, $seg['id']) ){
								$error = _("Une erreur s'est produite lors de l'enregistrement des segments.");
								break;
							}
						}
					}
				}
				
				// Création des codes promotions contenus dans le chéquier
				if( !isset($error) && $_GET['typePromo']==_PMT_TYPE_CHEEKBOOK ){
					$qty = isset($_GET['pmt-qte-gen']) && is_numeric($_GET['pmt-qte-gen']) && $_GET['pmt-qte-gen']>0 ? $_GET['pmt-qte-gen'] : 0;
					for( $i=0 ; $i < $qty ; $i++ ){
						$code = pmt_codes_generated();
						// Génération des bons de livraison
						$cod_child = pmt_codes_add( '', $_GET['typePromo'], $code, '', $cod );
						if( !$cod_child ){
							$error = _('Une erreur inattendue s\'est produite lors de la création du bon d\'achat.')."<br />"._('Veuillez réessayer ou prendre contact pour signaler le problème.');
						}
						$ar_cods[] = $cod_child;
					}
				}
				
				// Si aucune erreur lors de la création on copie la souche x fois précisée pour les chéquiers
				if( !isset($error) && $_GET['typePromo']==_PMT_TYPE_CHEEKBOOK ){
					$c = isset($_GET['pmt-nb-cheekbook']) ? $_GET['pmt-nb-cheekbook'] - 1 : 0;
					if( $c>0 ){
						// Copie $c fois la souche qui vient d'être créée
						for( $i=0 ; $i<$c ; $i++ ){
							if( !($copy = pmt_codes_copy($cod)) ){
								$error = _("Une erreur inattendue s'est produite lors de la création des chéqiuers.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
								break;
							}else{
								$ar_cods[] = $copy;
							}
						}
					}
				}
				
			}
		}
		
		if( isset($error) ){
			// En cas d'erreur, tous les codes promotions pouvant avoir été sont supprimés afin d'aviter toute création partielle
			if( isset($ar_cods) && is_array($ar_cods) && sizeof($ar_cods) ){
				foreach( $ar_cods as $cod ){
					pmt_codes_del( $cod );
				}
			}
			
			print json_encode( array('error' => $error) );
		} else {
			$_SESSION['save-pmt-ok'] = true;
			print json_encode( array('cod' => (string) $cod) );
		}
	} elseif( isset($_GET['genBA'], $_GET['parent'], $_GET['qty'], $_GET['typePromo']) ){
		if( !isset($_GET['parent']) || !pmt_codes_exists($_GET['parent']) ){
			$error = _('Une erreur inattendue s\'est produite lors de la génération des bons d\'achat.').'<br />'._('Veuillez réessayer ou prendre contact pour nous signaler le problème.');
		} else {
			$qty = isset($_GET['qty']) && is_numeric($_GET['qty']) && $_GET['qty']>0 ? $_GET['qty'] : 1;
			for( $i=0 ; $i < $qty ; $i++ ){
				$code = pmt_codes_generated();
				// Génération des bons de livraison
				$cod = pmt_codes_add( '', $_GET['typePromo'], $code, '', $_GET['parent'] );
				if( !$cod ){
					$error = _('Une erreur inattendue s\'est produite lors de la création du bon d\'achat.').'<br />'._('Veuillez réessayer ou prendre contact pour signaler le problème.');
				}
			}
		}
		
		if( isset($error) ){
			print json_encode( array('error' => $error) );
		} else {
			print json_encode( array('success' => true) );
		}
	} elseif( isset($_GET['relaodserie'], $_GET['idCod']) ){
		$html = '';
		$pagination = '';
		
		$rcode = pmt_codes_get( null, null, false, false, false, $_GET['idCod'] );
		if( $rcode && ria_mysql_num_rows($rcode) ){
			// Limite d'affichage des bons d'achat
			$limit_BA = 50;
			
			// Gestion de la pagination
			$link = '/admin/promotions/specials/edit.php?id='.$_GET['idCod'].'&type='.$_GET['type'];
			$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page']>0 ? $_GET['page'] : 1;
			$pages = $rcode && ria_mysql_num_rows($rcode) ? ceil( ria_mysql_num_rows($rcode) / $limit_BA ) : 1;
			if( $page>$pages ){
				$page = $pages;
			}
			
			$pmin = $page - 2;
			$pmin = $pmin < 1 ? 1 : $pmin;
			$pmax = $pmin + 4;
			$pmax = $pmax>$pages ? $pages : $pmax;
			
			ria_mysql_data_seek( $rcode, ($page-1)*$limit_BA );
			
			$i = 1;
			while( $code = ria_mysql_fetch_array($rcode) ){
				if( $i>$limit_BA ){
					break;
				}
				
				$status = '';
				if( $code['used']>0 ){
					$status = '<span class="pmt-used">'._('Utilisé').'</span>';
				} else {
					$status = '<span class="pmt-not-used">'._('En cours').'</span>';
				}
				
				$orders = '';
				$ar_orders = pmt_codes_get_orders( $code['id'] );
				foreach( $ar_orders as $o ){
					if( trim($orders)!='' ){
						$orders .= '<br />';
					}
					
					$orders .= '<a href="/admin/orders/order.php?ord='.$o.'">N°'.$o.'</a>';
				}
				
				$html .= '
					<tr>
						<td headers="pmt-select"><input type="checkbox" class="checkbox" name="pmt[]" value="'.$code['id'].'" />
						<td>'.$code['code'].'</td>
						<td>'.$status.'</td>
						<td>'.$orders.'</td>
					</tr>
				';
				
				$i++;
			}
			
			if( $pages>1 ){
				if( $page>1 ){
					$pagination .= '<a onclick="return reloadPromotionsSerie('.($page-1).');" href="'.$link.'&amp;page='.($page-1).'#tabSerie">&laquo; '._('Page précédente').'</a> | ';
				}
				
				for( $i=$pmin; $i<=$pmax; $i++ ){
					if( $i==$page ){
						$pagination .= '<b>'.$page.'</b>';
					}else{
						$pagination .= '<a onclick="return reloadPromotionsSerie('.($i).');" href="'.$link.'&amp;page='.($i).'#tabSerie">'.$i.'</a>';
					}
					
					if( $i<$pmax ){
						$pagination .= ' | ';
					}
				}
				
				if( $page<$pages ){
					$pagination .= ' | <a onclick="return reloadPromotionsSerie('.($page+1).');" href="'.$link.'&amp;page='.($page+1).'#tabSerie">'._('Page suivante').' &raquo;</a>';
				}
			}
		}
		
		print json_encode( array('html' => $html, 'pagination'=>$pagination) );
	} elseif( isset($_GET['idCod']) && (isset($_GET['del-variations']) || isset($_GET['save-variations'])) ){
		if( isset($_GET['save-variations']) ){
			if( !isset($_GET['code']) || trim($_GET['code'])=='' ){
				$error = _("Veuillez renseigner une nouvelle orthographe.");
			}else{
				$res = pmt_codes_variations_add( $_GET['idCod'], $_GET['code'] );
				if( $res===-1 ){
					$error = _("Cette orthographe est déjà utilisée.");
				}elseif( !$res ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout de la nouvelle orthographe.");
				}
			}
		}

		if( isset($_GET['del-variations']) ){
			if( isset($_GET['var']) && is_array($_GET['var']) && sizeof($_GET['var']) ){
				foreach( $_GET['var'] as $v ){
					if( !pmt_codes_variations_del($_GET['idCod'], $v) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression des orthographes sélectionnées.");
						break;
					}
				}
			}
		}

		if( !isset($error) ){
			$tbody = '';

			$rvar = pmt_codes_variations_get( $_GET['idCod'] );
			if( !$rvar || !ria_mysql_num_rows($rvar) ){
				$tbody .= '
					<tr>
						<td colspan="2">'._('Aucune orthographe approchée n\'est encore définie pour le code promotion.').'</td>
					</tr>
				';
			}else{
				while( $var = ria_mysql_fetch_array($rvar) ){
					$tbody .= '
						<tr>
							<td>
								<input type="checkbox" name="del-var[]" id="del-var-'.$var['cvt_id'].'" value="'.$var['cvt_id'].'" />
							</td>
							<td>
								<label for="del-var-'.$var['cvt_id'].'">'.htmlspecialchars( $var['code'] ).'</label>
							</td>
						</tr>
					';
				}
			}

			print json_encode( array('success' => $tbody) );
		}else{
			print json_encode( array('error' => $error) );
		}
	} elseif( isset($_GET['linkPrdCol'], $_GET['prd'], $_GET['col'], $_GET['pmt'], $_GET['include']) ){
		if( !pmt_products_colisage_exists($_GET['pmt'], $_GET['prd'], $_GET['col']) ){
			pmt_products_add( $_GET['pmt'], $_GET['prd'], $_GET['include'], 0, null, $_GET['col'] );
		}

		$r_pmt_prd = pmt_products_colisage_get( $_GET['pmt'], $_GET['prd'] );

		$first = true;
		$html = '<tr><td>'._('Conditionnement :').'</td>';
		if( $r_pmt_prd ){
			while( $pmt_prd = ria_mysql_fetch_assoc($r_pmt_prd) ){
				if( $pmt_prd['colisage'] !==null ){
					if( $pmt_prd['colisage'] == 0 ){
						$pmt_prd['colisage_name'] = 'A l\'unité';
					}
					if( !$first ){
						$html .= '<tr><td></td>';
					}
					$html .= '<td><input onclick="return unlinkPrdCol('.$pmt_prd['pmt_id'].', '.$pmt_prd['prd_id'].', '.$pmt_prd['colisage'].', '.$_GET['include']. ')" type="image" src="../../images/del-cat.svg" width="16" height="16" class="icon-del-cat" /> ' .$pmt_prd['colisage_name'].(parseInt($pmt_prd['colisage_qte']) != 0 ?' (Qté: '.parseInt($pmt_prd['colisage_qte']).')':'').'</td></tr>';
					$first = false;
				}
			}
		}
		print $html;
	} elseif( isset($_GET['unlinkPrdCol'], $_GET['pmt'], $_GET['prd'], $_GET['col'], $_GET['include']) ){
		pmt_products_del( $_GET['pmt'], $_GET['prd'], $_GET['col']);
		$r_pmt_prd = pmt_products_colisage_get( $_GET['pmt'], $_GET['prd'] );

		if( !ria_mysql_num_rows($r_pmt_prd) ){
			pmt_products_add( $_GET['pmt'], $_GET['prd'], $_GET['include'] );
		}

		
		$cols = '';
		$first = true;
		if( $r_pmt_prd && ria_mysql_num_rows($r_pmt_prd) ){
			while( $pmt_prd = ria_mysql_fetch_assoc($r_pmt_prd) ){
				if( $pmt_prd['colisage'] !==null ){
					if( $pmt_prd['colisage'] == 0 ){
						$pmt_prd['colisage_name'] = 'A l\'unité';
					}
					if( !$first ){
						$cols .= '<tr><td></td>';
					}
					$cols .= '<td><input onclick="return unlinkPrdCol('.$pmt_prd['pmt_id'].', '.$pmt_prd['prd_id'].', '.$pmt_prd['colisage'].', '.$_GET['include']. ')" type="image" src="../../images/del-cat.svg" width="16" height="16" class="icon-del-cat" /> ' .$pmt_prd['colisage_name'].(parseInt($pmt_prd['colisage_qte']) != 0 ?' (Qté: '.parseInt($pmt_prd['colisage_qte']).')':'').'</td></tr>';
					$first = false;
				}
			}
		}

		if( $cols != '' ){
			$html =  '<tr><td>' . _('Conditionnement :') . '</td> '.$cols;
		}else{
			$html = '<tr><td>' . _('Conditionnement :') . '</td><td><span style="margin-left:5px">' . _('Tout type de conditionnement') . '</span></td></td>';
		}
		print $html;

	} 

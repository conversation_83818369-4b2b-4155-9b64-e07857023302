<?php

	/**	\file popup-add-object.php
	 * 	Ce fichier est utilisé pour créer des relations entre deux objets de la base de données.
	 */

	require_once('relations.inc.php');

	$get_data = array(); // utilisé pour la pagination

	$src = array();
	if( isset($_GET['src_0']) && is_numeric($_GET['src_0']) ){
		$src[0] = $_GET['src_0'];
		$get_data[] = 'src_0='.$_GET['src_0'];
	}
	if( isset($_GET['src_1']) && is_numeric($_GET['src_1']) ){
		$src[1] = $_GET['src_1'];
		$get_data[] = 'src_1='.$_GET['src_1'];
	}
	if( isset($_GET['src_2']) && is_numeric($_GET['src_2']) ){
		$src[2] = $_GET['src_2'];
		$get_data[] = 'src_2='.$_GET['src_2'];
	}

	$dst = array();
	if( isset($_GET['dst_0']) && is_numeric($_GET['dst_0']) ){
		$dst[0] = $_GET['dst_0'];
		$get_data[] = 'dst_0='.$_GET['dst_0'];
	}
	if( isset($_GET['dst_1']) && is_numeric($_GET['dst_1']) ){
		$dst[1] = $_GET['dst_1'];
		$get_data[] = 'dst_1='.$_GET['dst_1'];
	}
	if( isset($_GET['dst_2']) && is_numeric($_GET['dst_2']) ){
		$dst[2] = $_GET['dst_2'];
		$get_data[] = 'dst_2='.$_GET['dst_2'];
	}

	$type = false;
	if( isset($_GET['type']) && is_numeric($_GET['type']) ){
		$type = $_GET['type'];
		$get_data[] = 'type='.$_GET['type'];
	}
	$src_cls = false;
	if( isset($_GET['src_cls']) && is_numeric($_GET['src_cls']) ){
		$src_cls = $_GET['src_cls'];
		$get_data[] = 'src_cls='.$_GET['src_cls'];
	}
	$dst_cls = false;
	if( isset($_GET['dst_cls']) && is_numeric($_GET['dst_cls']) ){
		$dst_cls = $_GET['dst_cls'];
		$get_data[] = 'dst_cls='.$_GET['dst_cls'];
	}

	$limit_for_page = 30;

	$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;

	$reload = false;
	if( isset($_GET['sel']) ){
		if( isset($dst_cls) && is_numeric($dst_cls) ){
			foreach( $_GET['sel'] as $o ){
				$err_id = false;

				// ajout des relations
				if( $src ){
					$err_id = rel_relations_add($src, $o, $type);
				}elseif( $dst ){
					$err_id = rel_relations_add($o, $dst, $type);
				}else{
					$error = _("Aucun objet n'a été trouvé.");
				}

				if( $err_id && $err_id < 0 ){
					$error = rel_err_describe($err_id);
				}
				if( !$err_id ){
					$error = _('Une erreur est survenue lors de l\'enregistrement de la relation').'\n'._('Veuillez réessayer ou prendre contact pour nous signaler le problème.');
				}

				if( !isset($error) || !$error ){
					$reload = true;
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Lie un objet'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
?>

	<form action="/admin/ajax/relations/popup-add-object.php" method="get">
		<input type="hidden" name="page" value="<?php print $page; ?>" />
		<input type="hidden" name="type" value="<?php print $type; ?>" />
		<input type="hidden" name="src_cls" value="<?php print $src_cls; ?>" />
		<input type="hidden" name="dst_cls" value="<?php print $dst_cls; ?>" />
		<input type="hidden" name="src_0" value="<?php print $src ? $src[0] : ''; ?>" />
		<input type="hidden" name="src_1" value="<?php print $src ? $src[1] : ''; ?>" />
		<input type="hidden" name="src_2" value="<?php print $src ? $src[2] : ''; ?>" />
		<input type="hidden" name="dst_0" value="<?php print $dst ? $dst[0] : ''; ?>" />
		<input type="hidden" name="dst_1" value="<?php print $dst ? $dst[1] : ''; ?>" />
		<input type="hidden" name="dst_2" value="<?php print $dst ? $dst[2] : ''; ?>" />

		<?php

			if( isset($dst_cls) && is_numeric($dst_cls) && $dst_cls ){
				$is_cls_linked = fld_classes_is_tenant_linked( $dst_cls );

				if( !$is_cls_linked && !in_array($dst_cls, array(CLS_BANNER, CLS_ERRATUM)) ){
					print '
						<p>
							<label for="q">'._('Rechercher un client :').'</label>
							<input type="text" name="q" id="q" value="'.( isset($_GET['q']) ? $_GET['q'] : '' ).'" />
							<input type="submit" name="search" value="'._('Rechercher').'" />
						</p>
					';
				}

				if( isset($_GET['q']) && trim($_GET['q'])!='' || $is_cls_linked || in_array($dst_cls, array(CLS_BANNER, CLS_ERRATUM)) ){
					// On peut réaliser une recherche
					$ar_results = array();
					switch( $dst_cls ){
						case CLS_PRODUCT :
							$type = 'prd';
							break;
						case CLS_USER :
							$type = 'usr';
							break;
						case CLS_CATEGORY :
							$type = 'prd-cat';
							break;
						case CLS_ORDER :
							$type = 'ord';
							break;
						case CLS_BRAND :
							$type = 'brd';
							break;
						case CLS_STORE :
							$type = 'dlv-str';
							break;
						case CLS_CMS :
							$type = 'cms';
							break;
						case CLS_NEWS :
							$type = 'news';
							break;
					}

					if( isset($type) && trim($type)!='' ){
						$search = search(
							array('seg'=>1, 'keywords'=>$_GET['q'], 'page'=>1, 'limit'=>0, 'published'=>false, 'section'=>false, 'action'=>6, 'type'=>array($type)),
							false, 0, -1, true, false, 0, true
						);

						if( is_array($search) && sizeof($search) ){
							foreach( $search as $s ){
								if( $dst_cls==CLS_PRODUCT ){
									$ar_results[] = array(
										'id' => $s['search']['tag'],
										'name' => $s['get']['ref'].' - '.$s['search']['name']
									);
								}else{
									$ar_results[] = array(
										'id' => $s['search']['tag'],
										'name' => $s['search']['name']
									);
								}
							}
						}
					}else{
						switch( $dst_cls ){
							case CLS_BANNER :
								$rb = adv_banners_get();
								if( $rb && ria_mysql_num_rows($rb) ){
									while( $b = ria_mysql_fetch_array($rb) ){
										$ar_results[] = array(
											'id' => $b['id'],
											'name' => $b['name']
										);
									}
								}
								break;
							case CLS_ERRATUM :
								$re = cat_erratums_get();
								if( $re && ria_mysql_num_rows($re) ){
									while( $e = ria_mysql_fetch_array($re) ){
										$ar_results[] = array(
											'id' => $e['id'],
											'name' => $e['prd_name']
										);
									}
								}
							default :
								if( $is_cls_linked ){
									$ro = fld_objects_get( 0, $dst_cls );
									if( $ro && ria_mysql_num_rows($ro) ){
										while( $o = ria_mysql_fetch_array($ro) ){
											$ar_results[] = array(
												'id' => $o['id'],
												'name' => $o['name']
											);
										}
									}
								}
								break;
						}
					}

					$pages = sizeof( $ar_results ) ? ceil(sizeof( $ar_results )/$limit_for_page) : 0;
					$page = $page>$pages ? $pages : $page;

					$pmin = $page-5;
					$pmax = $pmin+9;
					$pmin = $pmin<1 ? 1 : $pmin;
					$pmax = $pmax>$pages ? $pages : $pmax;

					print '
						<table class="checklist">
							<caption>'.( sizeof($ar_results).' résultat'.( sizeof($ar_results)>1 ? 's' : '') ).'</caption>
							<thead>
								<tr>
									<th id="res-check" class="col-check">
										<input type="checkbox" class="checkbox" onclick="checkAllClick(this);" />
									</th>
									<th id="res-name">'._('Désignation').'</th>
								</tr>
							</thead>
							<tbody>
					';

					if( !sizeof($ar_results) ){
						print '
							<tr>
								<td colspan="2">'._('Aucun résultat').'</td>
							</tr>
						';
					}else{
						$i = 1;

						$results = array_slice( $ar_results, $limit_for_page*($page-1), $limit_for_page );
						foreach( $results as $id=>$r ){
							if( $i>$limit_for_page ){
								break;
							}

							print '
								<tr>
									<td headers="res-check" class="pos-center">
										<input type="checkbox" name="sel[]" id="sel-'.$r['id'].'" value="'.$r['id'].'" />
									</td>
									<td headers="res-name">
										<label for="sel-'.$r['id'].'">'.htmlspecialchars( $r['name'] ).'</label>
									</td>
								</tr>
							';

							$i++;
						}
					}

					print '
							</tbody>
							<tfoot>
								<tr>
									<td colspan="2">
										<div style="float: left;">
											<input type="submit" name="select" value="'._('Sélectionner').'" onclick="return !$(\'.pos-center input:checked\').length ? false : true;" />
										</div>
										<div style="float: right;">
					';

					if( $pages>1 ){
						$plink = '/admin/ajax/relations/popup-add-object.php?'.implode('&amp;',$get_data).'&amp;q='.$_GET['q'];

						if( $page>1 ){
							print '<a href="'.$plink.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
						}
						for( $i=$pmin; $i<=$pmax; $i++ ){
							if( $i==$page ){
								print '<b>'.$page.'</b>';
							}else{
								print '<a href="'.$plink.'&amp;page='.($i).'">'.$i.'</a>';
							}

							if( $i<$pmax ){
								print ' | ';
							}
						}
						if( $page<$pages ){
							print ' | <a href="'.$plink.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
						}
					}

					print '
										</div>
									</td>
								</tr>
							</tfoot>
						</table>
					';
				}
			}
		}
		?>
	</form>
<?php
	if( $reload ){
		print '
			<script><!--
				parent.reloadObjects();
			--></script>
		';
	}

	require_once('admin/skin/footer.inc.php');
?>
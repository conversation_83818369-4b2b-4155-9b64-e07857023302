<?php

    /** \file json-city-zipcode.php
     * 
     *  Ce fichier est appelé en Ajax et retourne la liste complètes des villes et codes postaux définies pour un pays donné.
     *  Le filtrage des résultats est réalisé par la suite en Javascript par le fichier zipcode-city-autocompletion.js.
     * 
     *  Les paramètres en appel sont les suivants :
     *  - country : nom du pays (en majuscules).
     *
     *  La réponse est fournie au format Json et représente un volume important (1,5MB, en 700 à 800ms). Comme elle bouge peu,
     *  la mise en cache HTTP est autorisée pour améliorer les performances de chargement.
     * 
     *  Elle est construite comme suit :
     *  - city
     *      - liste des villes suivies de leur code postal entre parenthèses (ex : "ROGLIANO (20248)")
     *  - zipcode
     *      - liste des codes postaux suivis de leur ville (ex : "04000 - ENTRAGES")
     * 
     *  Le poids du fichier pourrait être divisé par plus de deux si les deux informations (code postal, ville) n'étaient pas envoyées deux fois. 
     */

    require_once("sys.zones.inc.php");

    // Autorise la mise en cache pour une semaine car le fichier est coûteux (poids / temps de génération)
    $seconds_to_cache = 604800;
    $expires = gmdate("D, d M Y H:i:s", time() + $seconds_to_cache) . " GMT";

    header( 'Cache-Control: max-age='.$seconds_to_cache.',public', true );
    header( 'Expires: '.$expires );
    header( 'Pragma: cache', true );

    if( isset($_GET['country']) ){
    	switch ($_GET['country']) {
    		case 'FRANCE':
    			$data = sys_zones_get_list(_ZONE_INSEE,_ZONE_ZIPCODES);
    			break;
    		case 'ITALIE':
    			$data = sys_zones_get_list(_ZONE_VILLE_ITALIE,_ZONE_ZIPCODE_ITALIE);
    			break;
    		case 'ALLEMAGNE':
    			$data = sys_zones_get_list(_ZONE_VILLE_ALLEMAGNE,_ZONE_ZIPCODE_ALLEMAGNE);
    			break;
    		case 'ESPAGNE':
    			$data = sys_zones_get_list(_ZONE_VILLE_ESPAGNE,_ZONE_ZIPCODE_ESPAGNE);
    			break;
            case 'LIECHTENSTEIN':
                $data = sys_zones_get_list(_ZONE_VILLE_LIECHTENSTEIN,_ZONE_ZIPCODE_LIECHTENSTEIN);
                break;
            case 'POLYNÉSIE FRANÇAISE':
                $data = sys_zones_get_list(_ZONE_VILLE_TAHITI,_ZONE_ZIPCODE_TAHITI);
                break;
            case 'PORTUGAL':
                $data = sys_zones_get_list(_ZONE_VILLE_PORTUGAL,_ZONE_ZIPCODE_PROTUGAL);
                break;
            case 'SUISSE':
                $data = sys_zones_get_list(_ZONE_VILLE_SUISSE,_ZONE_ZIPCODE_SUISSE);
                break;
            case 'PAYS-BAS':
                $data = sys_zones_get_list(_ZONE_VILLE_PAYSBAS,_ZONE_ZIPCODE_PAYSBAS);
                break;
            case 'AUTRICHE':
                $data = sys_zones_get_list(_ZONE_VILLE_AUTRICHE,_ZONE_ZIPCODE_AUTRICHE);
                break;
            case 'IRLANDE':
                $data = sys_zones_get_list(_ZONE_VILLE_IRLANDE,_ZONE_ZIPCODE_IRLANDE);
                break;
            case 'BELGIQUE':
                $data = sys_zones_get_list(_ZONE_VILLE_BELGIQUE,_ZONE_ZIPCODE_BELGIQUE);
                break;
    	}
	    
	    if($data){
		    foreach($data as $d){
		        $result['zipcode'][] = $d['zipcode'].' - '.$d['city'];
		    }
		    print json_encode($result);
		}


	}else{
		print '';
	}
    exit;
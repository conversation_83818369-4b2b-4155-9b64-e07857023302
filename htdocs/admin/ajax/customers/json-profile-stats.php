<?php

/** \file json-profile-stats.php
 *
 * Ce fichier est chargé de fournir les statistiques de commande (Commandes/client, produits par commande, etc...) qui
 * sont utilisées en entête dans la liste des clients.
 *
 * Ces données ont été extraites du fichier d'origine car elle ralentissaient fortement l'affichage de la page.
 * Elles sont désormais chargée en ajax pour que la vitesse de chargement ressentie soit plus élevée.
 *
 * @param int $users_count Obligatoire, Nombre de comptes utilisateurs utilisés dans le calcul
 * @param int $prf         Facultatif, Identifiant du profil client sur lequel filtrer le résultat
 * @param int $seg         Facultatif, Identifiant du segment sur lequel filtrer le résultat
 * @param int $seller      Facultatif, Identifiant du compte représentant sur lequel filtrer le résultat
 * @return string Un résultat au format JSON comprenant les colonnes suivantes :
 *		- users_count : nombre de comptes utilisateurs
 *		- count : nombre de commandes
 *		- total_ht : totaux hors taxes des commandes
 *		- total_ttc : totaux toutes taxes comprises
 *		- marge : marge total HT
 *		- [usr_id] : si $group_usr et $user, $prf_id ou $seg_id, identifiant du client de la ligne
 *		- sum_qte : nombre de produits commandés (tient compte de la quantité)
 *		- count_prd : nombre de produits unique commandés
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

require_once('orders.inc.php');

header('Content-Type: application/json');
header('Cache-Control: max-age=900, public, no-check', true);
header('Expires: '.gmdate('D, d M Y H:i:s \G\M\T', time() + 900), true);
header_remove('Pragma');

// Contrôle le paramètre "users_count".
if( !isset($_GET['users_count']) || !is_numeric($_GET['users_count']) ){
	return;
}

if( $_GET['users_count'] <= 0 ){
	$_GET['users_count'] = 1;
}

// Filtre "Profil".
if( !isset($_GET['prf']) ){
	$_GET['prf'] = '';
}

if( $prf = $_GET['prf'] ){
	$_GET['prf'] = explode(',', $prf);
}

// Filtre "Segment".
if( !isset($_GET['seg']) || !seg_segments_exists($_GET['seg']) ){
	$_GET['seg'] = 0;
}

// Filtre "Représentant".
if( !isset($_GET['seller']) || !is_numeric($_GET['seller']) || $_GET['seller'] < 0 ){
	$_GET['seller'] = false;
}

$stats = array(
	'users_count' => $_GET['users_count'],
	'count' => 0,
	'count_prd' => 0,
	'total_ht' => 0,
	'total_ttc' => 0,
	'marge' => 0,
	'nb_products' => 0,
);

// Calcule les statistiques.
$r_stat = ord_orders_totals_get(ord_states_get_ord_valid(), 0, 0, 0, true, false, $_GET['prf'], $_GET['seg'], false, $_GET['seller']);

//Statistique conversion
// Récupération du nombre total d'utilisateur via le profile ou segment sélectionné
$total_user = ria_mysql_num_rows(gu_users_get(0, '', '',$_GET['prf'] ,'', 0, '', false,false,false, false,  '',false,  0,'',  0,false, true,null,$_GET['seg'], false, false, null, false, 0, 0, false, false));

if( $r_stat && ria_mysql_num_rows($r_stat) ){

	$stats = ria_mysql_fetch_assoc($r_stat);

	$ord_users = $stats['usersCount']; // Nombre de compte avec au moins une commande
	$stats['users_count'] = $_GET['users_count'];

	$stats['conversion'] = ria_number_format($ord_users > 0 ? ($ord_users / $total_user) : 0, NumberFormatter::PERCENT, 2); // Tau de conversion en pourcentage
	$stats['users_avg_orders'] = ria_number_format($stats['users_count'] > 0 ? ($stats['count'] / $stats['users_count']) : 0, NumberFormatter::DECIMAL, 2);
	$stats['products_avg_orders'] = ria_number_format( $stats['count'] > 0 ? ($stats['count_prd'] / $stats['count']) : 0, NumberFormatter::DECIMAL, 2);
	$stats['avg_turnover_ht'] = ria_number_format($stats['users_count'] > 0 ? ($stats['total_ht'] / $stats['users_count']) : 0, NumberFormatter::CURRENCY, 2);
	$stats['avg_turnover_ttc'] = ria_number_format($stats['users_count'] > 0 ? ($stats['total_ttc'] / $stats['users_count']) : 0, NumberFormatter::CURRENCY, 2);
	$stats['markup'] = ria_number_format($stats['users_count'] > 0 ? ($stats['marge'] / $stats['users_count']) : 0, NumberFormatter::CURRENCY, 2);
	$stats['total_ht'] = ria_number_format($stats['total_ht'], NumberFormatter::CURRENCY, 2);
	$stats['total_ttc'] = ria_number_format($stats['total_ttc'], NumberFormatter::CURRENCY, 2);
}

print json_encode($stats);
<?php

	/**	\file ajax-segments.php
	 * 	Ce fichier permet le chargement et la modification de segments de clientèle. De nombreuses actions sont supportées :
	 * 	- getCdt : retourne les criètres de segmentation disponibles pour une classe donnée
	 *  - getSymbols : retourne les symboles de comparaison acceptés pour un critère de segmentation donné
	 *  - getOrdSource : 
	 *  - getWebsite :
	 *  - getDatesAccept
	 *  - copyGrp
	 *  - getForm
	 *  - getFields
	 *  - saveSeg
	 *  - addObjSeg
	 *  - delObjSeg
	 */

	// Vérifie que l'utilisateur en cours à accès à l'administration des segments
	if( !gu_users_admin_rights_used('_MENU_CUSTOMERS_SEGMENTS') && !gu_users_admin_rights_used('_MENU_CONFIG_FIELDS_SEGMENTS') ){
		exit;
	}

	require_once( 'segments.inc.php' );
	require_once( 'newsletter.inc.php' );
	require_once( 'sys.zones.inc.php' );
	
	if( isset($_GET['getCdt'], $_GET['cls']) ){
		
		header('Content-type: text/json');
		print json_encode( seg_criterions_get_array( $_GET['cls'] ) );
		
	} elseif( isset($_GET['getSymbols'], $_GET['cdt']) ){
		
		$fld_id = isset($_GET['field']) && is_numeric($_GET['field']) && $_GET['field']>0 ? $_GET['field'] : 0;
		$ar_symbols = seg_criterions_get_symbols( $_GET['cdt'], CUSTOM_DATE, $fld_id );

		header('Content-type: text/json');
		print json_encode( $ar_symbols );
		
	} elseif( isset($_GET['getOrdSource'], $_GET['cdt']) ){
		
		header('Content-type: text/json');
		$grp = seg_criterions_get_group( $_GET['cdt'] );
		if( $grp==4 && !in_array($_GET['cdt'], seg_criterions_noweb()) ){
			print json_encode( seg_ord_sources_get_array() );
		}
		
	} elseif( isset($_GET['getWebsite'], $_GET['cdt']) ){
		
		header('Content-type: text/json');
		if( seg_criterions_get_use_wst($_GET['cdt']) ){
			$rwst = wst_websites_get();
			
			$ar_wst = array();
			while( $wst = ria_mysql_fetch_array($rwst) ){
				$ar_wst[] = array( 'id'=>$wst['id'], 'name'=>$wst['name'] );
			}
			
			print json_encode( $ar_wst );
		}
		
	} elseif( isset($_GET['getDatesAccept'], $_GET['cdt']) ){
		
		header('Content-type: text/json');
		print json_encode( array('res' => seg_criterions_get_use_period($_GET['cdt']), 'dates' => seg_dyn_date_get()) );
		
	}elseif( isset($_GET['copyGrp'], $_GET['seg'], $_GET['grp']) ){
		
		header('Content-type: text/json');
		$res = seg_segment_criterions_copy_group( $_GET['seg'], $_GET['grp'] );
		print json_encode( array('r' => $res) );
		
	} elseif( isset($_GET['getForm'], $_GET['cdt']) ){
		
		$fld_id = false;
		if( isset($_GET['field']) && is_numeric($_GET['field']) && $_GET['field']>0 ){
			$fld_id = $_GET['field'];

			$ar_data = array(
				'type' => fld_fields_get_type( $_GET['field'] ),
				'data' => array()
			);
		}else{
			$ar_data = array(
				'type' => seg_criterions_get_type( $_GET['cdt'] ),
				'data' => array()
			);
		}
		
			
		$code = seg_criterions_get_code( $_GET['cdt'] );
		if( trim($code)!='' ){
			$ar_data['data'] = seg_criterions_get_objects_array( $code, $fld_id );
		}

		header('Content-type: text/json');
		print json_encode( $ar_data );
		
	} elseif( isset($_GET['getFields']) ){

		$ar_fields = array();

		if( isset($_GET['cls']) && is_numeric($_GET['cls']) && $_GET['cls']>0 ){
			$rfields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $_GET['cls'] );

			if( $rfields && ria_mysql_num_rows($rfields) ){
				while( $field = ria_mysql_fetch_array($rfields) ){
					$ar_fields[] = array( 'id'=>$field['id'], 'name'=>$field['name'] );
				}
			}
		}

		header('Content-type: text/json');
		print json_encode( $ar_fields );

	}elseif( isset($_POST['saveSeg']) ){
		
		$error = '';
		
		// vérification
		if( !isset($_POST['name']) || trim($_POST['name'])=='' ){
			$error .= ( trim($error)!='' ? '<br />' : '' )._('Veuillez renseigner un intitulé pour ce segment.');
		}
			
		$has_conditions = false;
		if( isset($_POST['cdt-type']) && is_array($_POST['cdt-type']) ){
			$g = 1;
			foreach( $_POST['cdt-type'] as $grp=>$criterions ){
				foreach( $criterions as $key=>$cid ){
					$rc = seg_criterions_get( $cid );
					if( !$rc || !ria_mysql_num_rows($rc) ) continue;
					$c = ria_mysql_fetch_array( $rc );
					
					// La valeur n'est pas nécessaire pour les symbôles "Est vide" ainsi que "N'est pas vide"
					if( $_POST['cdt-symbol'][$grp][$key]=="=''" || $_POST['cdt-symbol'][$grp][$key]=="!=''" ){
						break;
					}

					$value = isset($_POST['cdt-search-key'][$grp][$key]) && trim($_POST['cdt-search-key'][$grp][$key])!='' ? $_POST['cdt-search-key'][$grp][$key] : '';
					if( $value=='' ){
						$value = isset($_POST['cdt-value'][$grp][$key]) && trim($_POST['cdt-value'][$grp][$key])!='' ? $_POST['cdt-value'][$grp][$key] : '';
					}
					
					if( $value=='' ){
						$error .= ( trim($error)!='' ? '<br />' : '' ).sprintf(_('Veuillez préciser une valeur pour le critère "%s" du groupe n°%d'), $c['name'], $g);
					}
				}
				
				$g++;
			}
		}
		
		if( trim($error)=='' ){
			$desc = trim($_POST['desc'])!='' ? $_POST['desc'] : false;
			$seg = isset($_POST['seg']) ? $_POST['seg'] : 0;
			
			// enregistre le segment
			$addSeg = false;
			if( $seg!=0 ){
				if( !seg_segments_upd( $seg, $_POST['name'], $desc) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour des informations du segment.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			} else {
				$addSeg = true;
				if( !($seg=seg_segments_add( $_POST['name'], $_POST['cls'], $desc )) ){
					$error = _("Une erreur inattendue s'est produite lors de la création du segment.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			$rsc = seg_segment_criterions_get( 0, $seg);

			while( $sc = ria_mysql_fetch_assoc($rsc) ){
				if( !seg_criterions_sources_del( $sc['sgc_id'] ) ){
					$error = _("Une erreur inattendue s'est produite lors de la préparation à l'enregistrement des critères.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
					break;
				}
			}

			if( !seg_segment_criterions_del(0, $seg) ){
				$error = _("Une erreur inattendue s'est produite lors de la préparation à l'enregistrement des critères.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}

			
			if( isset($_POST['cdt-type']) && is_array($_POST['cdt-type']) ){
				$g = 1;
				foreach( $_POST['cdt-type'] as $grp=>$criterions ){
					foreach( $criterions as $key=>$cid ){
						if( !is_numeric($cid) || $cid<=0 ) continue;
						$value = isset($_POST['cdt-search-key'][$grp][$key]) && trim($_POST['cdt-search-key'][$grp][$key])!='' ? $_POST['cdt-search-key'][$grp][$key] : '';
						if( $value=='' ){
							$value = isset($_POST['cdt-value'][$grp][$key]) && trim($_POST['cdt-value'][$grp][$key])!='' ? $_POST['cdt-value'][$grp][$key] : '';
						}
						
						switch( $cid ){
							case 25 :
								$value = pmt_codes_get_id( $value );
								break;
							case 23 :
							case 35 :
								$email = isset($_POST['cdt-value'][$grp][$key]) && trim($_POST['cdt-value'][$grp][$key])!='' ? $_POST['cdt-value'][$grp][$key] : '';
								if( trim($email)!='' ){
									$rusr = gu_users_get( 0, $email );
									if( $rusr && ria_mysql_num_rows($rusr) ){
										$usr = ria_mysql_fetch_array( $rusr );
										$value = $usr['id'];
									}
								}
								break;
						}

						// enregistre les critères
						// $sos_id = isset($_POST['cdt-source'][$grp][$key]) && $_POST['cdt-source'][$grp][$key]!=-1 ? $_POST['cdt-source'][$grp][$key] : 0;	
						$sos_id = 0;
						$symbol = isset($_POST['cdt-symbol'][$grp][$key]) ? $_POST['cdt-symbol'][$grp][$key] : '=';
						$wst	= isset($_POST['cdt-website'][$grp][$key]) && $_POST['cdt-website'][$grp][$key]!=-1 ? $_POST['cdt-website'][$grp][$key] : 0;
						$fld 	= isset($_POST['cdt-fields'][$grp][$key]) && $_POST['cdt-fields'][$grp][$key]!=-1 ? $_POST['cdt-fields'][$grp][$key] : 0;

						$date_s = $date_e = $date_days = false;
						
						if( isset($_POST['cdt-dates'][$grp][$key]) ){							
							$date_days = $_POST['cdt-dates'][$grp][$key];

							if($_POST['cdt-dates'][$grp][$key]==='perso'){
								if( isset($_POST['dates-start'][$grp][$key]) ){
									if( trim($_POST['dates-start'][$grp][$key])!='' && !isdate($_POST['dates-start'][$grp][$key]) ){
										$error .= ( trim($error)!='' ? '<br />' : '' )._("La date de début n'est pas correcte, veuillez saisir une date au format JJ/MM/AAAA ou bien laisser le champ vide");
									}else{
										$date_s = $_POST['dates-start'][$grp][$key];
									}
								}
								
								if( isset($_POST['dates-end'][$grp][$key]) ){
									if( trim($_POST['dates-end'][$grp][$key])!='' && !isdate($_POST['dates-end'][$grp][$key]) ){
										$error .= ( trim($error)!='' ? '<br />' : '' )._("La date de fin n'est pas correcte, veuillez saisir une date au format JJ/MM/AAAA ou bien laisser le champ vide");
									}else{
										$date_e = $_POST['dates-end'][$grp][$key];
									}
								}

								if( $date_s && $date_e ){
									if( strtotime(dateparse($date_s))>strtotime(dateparse($date_e)) ){
										$error .= ( trim($error)!='' ? '<br />' : '' )._("La date de début est supérieure à la date de fin");
									}
								}								
							}				
						}

						if( trim($error)=='' ){
							$result_add_criterion = seg_segment_criterions_add( $seg, $cid, $grp, $symbol, $value, $sos_id, $wst, $fld, $date_s, $date_e, $date_days );
							if( $result_add_criterion<=0 ){
								$error .= ( trim($error)!='' ? '<br />' : '' ).seg_err_describe( $result_add_criterion, $cid, ($grp+1) );
							}
							if(isset($_POST['cdt-source'][$grp][$key]) && $_POST['cdt-source'][$grp][$key]>0){
								foreach ($_POST['cdt-source'][$grp][$key] as $key => $value) {
									seg_criterions_sources_add( $result_add_criterion, $value );
								}
							}else{
								seg_criterions_sources_add( $result_add_criterion, 1 );
							}
						}
					}
					
					$g++;
				}
			}
		}
		
		if( isset($seg) ){
			seg_segments_count_objects( $seg, true );
		}
		
		header('Content-type: text/json');
		if( trim($error)!='' ){
			if( isset($addSeg) && $addSeg && is_numeric($seg) && $seg>0 ){
				seg_segments_del( $seg );
			}
			print json_encode( array('success'=>0, 'data'=>$error) );
		} else {
			print json_encode( array('success'=>1, 'data'=>$seg) );
		}
	} elseif( isset($_GET['addObjSeg'], $_GET['clsID'], $_GET['obj0'], $_GET['obj1'], $_GET['obj2']) && is_numeric($_GET['addObjSeg']) && $_GET['addObjSeg']>0 ){
		$error = false;
		
		if( !is_numeric($_GET['obj0']) || !is_numeric($_GET['obj1']) || !is_numeric($_GET['obj2']) ){
			$error = _("Impossible de récupérer l'identifiant de l'objet auquel vous souhaitez rattacher un segment.")." "._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}else{
			if( $_GET['clsID']==CLS_PMT_CODE && $_GET['obj0']==0 ){
				$segment = '';
				if( isset($_SESSION['admin_create_promo']['customers']['segments']) && is_array($_SESSION['admin_create_promo']['customers']['segments']) ){
					$rseg = seg_segments_get( $_GET['addObjSeg'] );
					if( $rseg && ria_mysql_num_rows($rseg) ){
						$seg = ria_mysql_fetch_array( $rseg );
						$_SESSION['admin_create_promo']['customers']['segments'][$_GET['clsID'].'-'.$_GET['obj0'].'-'.$_GET['obj1'].'-'.$_GET['obj2']] = array(
							'cls' => $_GET['clsID'],
							'obj0' => $_GET['obj0'],
							'obj1' => $_GET['obj1'],
							'obj2' => $_GET['obj2'],
							'id' => $seg['id'],
							'name' => $seg['name']
						);
					}
					
					if( sizeof($_SESSION['admin_create_promo']['customers']['segments']) ){
						foreach( $_SESSION['admin_create_promo']['customers']['segments'] as $seg ){
							$segment .= '<input class="del-obj-seg" type="image" name="del-seg-'.$seg['id'].'" src="/admin/images/del-cat.png" width="16" height="13" title="'._('Retirer ce segment').'" />&nbsp;';
							$segment .= htmlspecialchars( $seg['name'] ).'<br />';
						}
					}else{
						$segment = _('Aucune restriction liée aux segments.');
					}
				}
			}else{
				if( !seg_objects_add($_GET['clsID'], array($_GET['obj0'], $_GET['obj1'], $_GET['obj2']), $_GET['addObjSeg']) ) {
					$error = _("Une erreur inattendue s'est produite lors du rattachement du segment.")." "._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				} else {
					$segment = '';
					$robject = seg_objects_get_segments( $_GET['clsID'], array($_GET['obj0'], $_GET['obj1'], $_GET['obj2']) );
					if( $robject &&  ria_mysql_num_rows($robject) ){
						while( $obj = ria_mysql_fetch_array($robject) ){
							$segment .= '<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" width="16" height="13" title="'._('Retirer ce segment').'" />&nbsp;';
							$segment .= htmlspecialchars( $obj['name'] ).'<br />';
						}
					} else { $segment = _('Aucune restriction liée aux segments.'); }
				}
			}
		}
		
		if( trim($error)!='' ){
			print json_encode( array('success'=>0, 'data'=>$error) );
		} else {
			print json_encode( array('success'=>1, 'data'=>$segment) );
		}
	} elseif( isset($_GET['delObjSeg'], $_GET['clsID'], $_GET['obj0'], $_GET['obj1'], $_GET['obj2']) && is_numeric($_GET['delObjSeg']) && $_GET['delObjSeg']>0 ){
		$error = false;
		
		if( !is_numeric($_GET['obj0']) || !is_numeric($_GET['obj1']) || !is_numeric($_GET['obj2']) ){
			$error = _("Impossible de récupérer l'identifiant de l'objet auquel vous souhaitez rattacher un segment.")." "._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}else{
			if( $_GET['clsID']==CLS_PMT_CODE && $_GET['obj0']==0 ){
				$ar_ex_seg = array(); $segment = ''; $option = '';
				if( isset($_SESSION['admin_create_promo']['customers']['segments']) && is_array($_SESSION['admin_create_promo']['customers']['segments']) ){
					if( isset($_SESSION['admin_create_promo']['customers']['segments'][$_GET['clsID'].'-'.$_GET['obj0'].'-'.$_GET['obj1'].'-'.$_GET['obj2']]) ){
						unset( $_SESSION['admin_create_promo']['customers']['segments'][$_GET['clsID'].'-'.$_GET['obj0'].'-'.$_GET['obj1'].'-'.$_GET['obj2']] );
					}
					
					if( sizeof($_SESSION['admin_create_promo']['customers']['segments']) ){
						foreach( $_SESSION['admin_create_promo']['customers']['segments'] as $seg ){
							$ar_ex_seg[] = $seg['id'];
							$segment .= '<input class="del-obj-seg" type="image" name="del-seg-'.$seg['id'].'" src="/admin/images/del-cat.png" width="16" height="13" title="'._('Retirer ce segment').'" />&nbsp;';
							$segment .= htmlspecialchars( $seg['name'] ).'<br />';
						}
					}else{
						$segment = _('Aucune restriction liée aux segments.');
					}
					
					$rseg = seg_segments_get( 0, CLS_USER );
					$option .= '<option value="-1">'._('Choisir un segment').'</option>';
					if( $rseg && ria_mysql_num_rows($rseg) ){
						while( $seg = ria_mysql_fetch_array($rseg) ){
							if( in_array($seg['id'], $ar_ex_seg) ) continue;
							$option .= '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
						} 
					}
				}
			}else{
				if( !seg_objects_del( $_GET['clsID'], array($_GET['obj0'], $_GET['obj1'], $_GET['obj2']), $_GET['delObjSeg'] ) ) {
					$error = _("Une erreur inattendue s'est produite lors du la suppression du rattachement au segment.")." "._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				} else {
					$ar_ex_seg = array();
					$segment = $option = '';
					$robject = seg_objects_get_segments( $_GET['clsID'], array($_GET['obj0'], $_GET['obj1'], $_GET['obj2']) );
					if( $robject &&  ria_mysql_num_rows($robject) ){
						while( $obj = ria_mysql_fetch_array($robject) ){
							$ar_ex_seg[] = $obj['id'];
							$segment .= '<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" width="16" height="13" title="'._('Retirer ce segment').'" />&nbsp;';
							$segment .= htmlspecialchars( $obj['name'] ).'<br />';
						}
					} else { $segment = _('Aucune restriction liée aux segments.'); }
					
					$rseg = seg_segments_get( 0, CLS_USER );
					$option .= '<option value="-1">'._('Choisir un segment').'</option>';
					if( $rseg && ria_mysql_num_rows($rseg) ){
						while( $seg = ria_mysql_fetch_array($rseg) ){
							if( in_array($seg['id'], $ar_ex_seg) ) continue;
							$option .= '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
						} 
					}
				}
			}
		}
		
		if( trim($error)!='' ){
			print json_encode( array('success'=>0, 'data'=>$error) );
		} else {
			print json_encode( array('success'=>1, 'data'=>$segment, 'option'=>$option) );
		}
	}

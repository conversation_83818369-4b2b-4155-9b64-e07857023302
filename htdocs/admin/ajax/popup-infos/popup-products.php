<?php
	require_once('products.inc.php');
	
	if( !isset($_GET['prd']) || !prd_products_exists($_GET['prd']) ){
		$g_error = _("Le produit donné en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$prd = ria_mysql_fetch_array( prd_products_get_simple($_GET['prd']) );
	}

	define('ADMIN_PAGE_TITLE', _('Produit') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Informations sur le produit').'</caption>
				<col width="150" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Référence :').'</td>
						<td>'.htmlspecialchars( $prd['ref'] ).'</td>
					</tr>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $prd['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Titre :').'</td>
						<td>'.htmlspecialchars( $prd['title'] ).'</td>
					</tr>
					<tr>
						<td>'._('Marque :').'</td>
						<td>'.htmlspecialchars( $prd['brd_title'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td>'.htmlspecialchars( $prd['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description longue :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $prd['desc-long'] ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');

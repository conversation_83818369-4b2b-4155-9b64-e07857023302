<?php
	require_once('tools.faq.inc.php');

	if( !isset($_GET['cat']) || !faq_categories_exists($_GET['cat']) ){
		$g_error = _("La catégorie de FAQ donnée en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$cat = ria_mysql_fetch_array( faq_categories_get($_GET['cat']) );
	}

	define('ADMIN_PAGE_TITLE', _('Catégorie FAQ') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Catégorie').'</caption>
				<col width="175" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $cat['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $cat['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Questions :').'</td>
						<td>'.number_format( $cat['questions'], 0 , '', ' ').'</td>
					</tr>
					<tr>
						<td>'._('Questions publiées :').'</td>
						<td>'.number_format( $cat['questions_published'], 0 , '', ' ').'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');

<?php
	require_once('news.inc.php');

	if( !isset($_GET['news']) || !news_exists($_GET['news']) ){
		$g_error = _("L'actualité donnée en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$news = ria_mysql_fetch_array( news_get($_GET['news']) );
	}

	define('ADMIN_PAGE_TITLE', _('Document') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Actualité').'</caption>
				<col width="175" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $news['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td>'.$news['intro'].'</td>
					</tr>
					<tr>
						<td>'._('Contenu :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $news['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Publiée ?').'</td>
						<td>'.( $news['published'] ? _('Oui') : _('Non') ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');

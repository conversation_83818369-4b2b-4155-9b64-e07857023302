<?php
	require_once('tools.faq.inc.php');

	if( !isset($_GET['qst']) || !faq_questions_exists($_GET['qst']) ){
		$g_error = _("La question de FAQ donnée en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$qst = ria_mysql_fetch_array( faq_questions_get($_GET['qst']) );
	}

	define('ADMIN_PAGE_TITLE', _('Question FAQ') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Question').'</caption>
				<col width="175" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $qst['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $qst['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Publiée ?').'</td>
						<td>'.( $qst['publish'] ? _('Oui') : _('Non') ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');

<?php

	/**	\file json-delivery-alerts.php
	 * 
	 * 	Ce fichier fourni une api Ajax permettant la récupération des alertes de livraison. La pagination est supportée. Le nombre de résultats
	 * 	maximum retourné par page est de 50. Par défaut, la 1ère page de résultat est retournée.
	 * 
	 * 	Les paramètres acceptés sont les suivants :
	 * 
	 * 	- p : Facultatif, numéro de la page de résultat devant être retournée. La valeur par défaut est 1.
	 * 
	 */

	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ALERT');

	// Inclut les fonctions pour les alertes de livraison
	require_once('users.inc.php');
	
	// Défini si la recherche fait partie d'une pagination
	$page = isset($_GET['p']) && is_numeric($_GET['p']) && $_GET['p']>0 ? $_GET['p'] : 1;
	
	// Tableau contenant tous les alertes
	$alerts = array();
	
	// Récupère les alerts
	if( $ralert = gu_livr_alerts_get() ){
		
		$count_total = ria_mysql_num_rows($ralert);
		
		// On se place sur la position de départ de lecture des résultats
		if( $page > 1 && $count_total > ( ($page-1)*50 ) ){
			ria_mysql_data_seek( $ralert, ($page-1)*50 );
		}elseif( $count_total ){
			ria_mysql_data_seek( $ralert, 0 );
		}
		
		$count = 0;
		while( $alert = ria_mysql_fetch_assoc($ralert) ){
			$rp = prd_products_get_simple( $alert['id'] );

			if( !$rp || !ria_mysql_num_rows($rp) ){
				continue;
			}

			$p = ria_mysql_fetch_assoc( $rp );
			
			// Intégration d'un tableau contenant les informations sur une alerte de livraison
			$alerts[] = array(
				'usr_id'		=> $alert['usr_id'],
				'usr_email'		=> $alert['usr_email'],
				'id'			=> $alert['id'],
				'ref'			=> $alert['ref'],
				'name'			=> $alert['name'],
				'brdName'		=> $p['brd_title'],
				'img_id'		=> $alert['img_id'],
				'date_created'	=> $alert['date_created']
			);
			
			// Comptabilise le nombre d'alert ajouté dans la liste
			$count++;
			
			// Si le nombre d'alert retourné est fixé et atteint, on arrête la lecture du résultats
			if( $count>=50 )
				break;
		}
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $alerts );

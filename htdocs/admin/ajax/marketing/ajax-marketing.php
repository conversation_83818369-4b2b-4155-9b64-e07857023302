<?php

/**	\file ajax-marketing.php
 * 
 * 	Ce fichier fourni une api json sur les fonctions d'envoi de SMS de la solution. Les paramètres à fournir sont les suivants :
 *  - cpg : Obligatoire, identifiant de la campagne sur laquelle les fonctions vont porter
 *  - function : Obligatoire, détermine l'opération à exécuter sur la campagne. Les valeurs acceptées sont les suivantes :
 * 		- getRules
 * 		- addRule
 * 		- delRule
 * 		- delRules
 * 		- copyRules
 * 		- getIncludeCount
 * 		- executeCampaign
 * 
 * 	Chaque fonction comporte des arguments différents, adaptés à ce qui doit être fait.
 * 
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

if( !IS_AJAX || !isset($_GET['cpg'], $_GET['function']) || !is_numeric($_GET['cpg']) || $_GET['cpg'] <= 0 || !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
	exit;
}

require_once( 'Marketing/models/Campaigns.inc.php' );

$rCampaign = CampaignsManager::getCampaigns($_GET['cpg']);

if( !$rCampaign || !ria_mysql_num_rows($rCampaign) ){
	exit;
}

$campaign = ria_mysql_fetch_assoc($rCampaign);
$Executed = false;

if( trim( $campaign['date_executed'] ) != '' ){
	$dateExecuted = new DateTime($campaign['date_executed']);
	$now = new DateTime();
	if( $dateExecuted < $now ){
		$Executed = true;
	}
}


switch( $_GET['function'] ){
	case 'getRules':
		$rules = getRules($_GET['cpg']);
		echo json_encode($rules);
		exit;
	case 'addRule':
		if( !isset($_GET['value'], $_GET['ruleType'], $_GET['include']) || $Executed){
			exit;
		}
		if( $_GET['include'] === '1' || $_GET['include'] === '0' ){
			$include = (bool) $_GET['include'];
		}else{
			$include = true;
		}
		$rule = addRule($_GET['cpg'], $_GET['ruleType'], $_GET['value'], $include);
		echo json_encode($rule);
		exit;
	case 'delRule':
		if( !isset($_GET['value'], $_GET['ruleType']) || $Executed){
			exit;
		}
		$r = delRule($_GET['cpg'], $_GET['ruleType'], $_GET['value']);
		echo json_encode(array('success'=> $r));
		exit;
	case 'delRules':
		$r = delRules($_GET['cpg']);
		echo json_encode(array('success'=> $r));
		exit;
	case 'copyRules':
		if( isset($_GET['cpg_src']) || $Executed){
			$r = copyRules($_GET['cpg_src'], $_GET['cpg']);
		}else{
			$r = false;
		}
		echo json_encode(array('success'=>$r));
		exit;
	case 'getIncludeCount':
		$count = getIncludeCount($campaign);
		echo json_encode(array('count'=>$count));
		exit;
	case 'executeCampaign':
		if( $Executed ){
			exit;
		}
		$r = executeCampaign($_GET['cpg']);
		//$r = true;
		echo json_encode(array('success'=>$r));
		exit;
}

/** Cette fonction permet d'ajouter un segment ou un téléphone mobile ou un compte client a une campagne
 *	@param int $cpg_id Obligatoire, identifiant de la campagne
 * 	@param string $type Type d'ajout a réalisé seg, usr, mobile
 *	@param $value la valeur a ajouter
 *  @param bool $include Facultatif si la valeur est incluse ou non dans la campagne
 *
 *	@return array la règle qui sera ajoutée :
 * 				- type
 * 				- include
 * 				- valuep
 * 				- label
 */
function addRule($cpg_id, $type, $value, $include=true){
	if( !is_numeric($cpg_id) && $cpg_id <=0 ){
		return false;
	}
	$rule = array(
		'type' => $type,
		'include' => $include,
		'label' => ''
	);

	switch( $type ){
		case 'seg':
			if( !CampaignsManager::addCampaignsSegments($cpg_id, $value, $include) ){
				$rule['value'] = false;
			}else{
				$rule['value'] = $value;
				$rSeg = seg_segments_get($value);
				$seg = ria_mysql_fetch_assoc($rSeg);
				$rule['label'] = $seg['name'];
			}
			break;
		case 'usr':
			if( !CampaignsManager::addCampaignsUser($cpg_id, $value, $include) ){
				$rule['value'] = false;
			}else{
				$rule['value'] = $value;
				$rule['label'] = gu_users_get_name($value);
			}
			break;
		case 'mobile':
			if( !$v = conv_france_international_number($value) ){
				$rule['value'] = false;
			}else{
				if( !CampaignsManager::addCampaignsPhone($cpg_id, $v, $include) ){
					$rule['value'] = false;
				}else{
					$rule['value'] = $rule['label'] = $v;
				}
			}
			break;
	}
	return $rule;
}

/** Cette fonction permet de supprimer un segment ou un téléphone mobile ou un compte client a une campagne
 *	@param int $cpg_id identifiant de la campagne
 * 	@param string $type Type d'ajout a réalisé seg, usr, mobile
 *	@param $value la valeur a ajouter
 *  @param bool $include Facultatif si la valeur est incluse ou non dans la campagne
 *
 * @return bool true si succès, false si erreur
 */
function delRule($cpg_id, $type, $value){
	if( !is_numeric($cpg_id) && $cpg_id <=0 ){
		return false;
	}
	switch( $type ){
		case 'seg':
			if( !CampaignsManager::delCampaignsSegments($cpg_id, $value) ){
				return false;
			}
			return true;
		case 'usr':
			if( !CampaignsManager::delCampaignsUsers($cpg_id, $value) ){
				return false;
			}
			return true;
		case 'mobile':
			if( !CampaignsManager::delCampaignsPhones($cpg_id, $value) ){
				return false;
			}
			return true;
	}
}

function delRules($cpg_id){
	if( !is_numeric($cpg_id) && $cpg_id <=0 ){
		return false;
	}

	$error = false;
	if( !CampaignsManager::delCampaignsSegments($cpg_id) ){
		$error = true;
	}
	if( !CampaignsManager::delCampaignsUsers($cpg_id) ){
		$error = true;
	}
	if( !CampaignsManager::delCampaignsPhones($cpg_id) ){
		$error = true;
	}
	if( $error ){
		return false;
	}
	return true;
}

/** Cette fonction permet de récupérer toutes les règles associer a une campagne
 *	@param int $cpg_id identifiant de la campagne
 *
 * 	@return array un tableau avec chaque règle
 */
function getRules( $cpg_id ){
	if( !is_numeric($cpg_id) && $cpg_id <=0 ){
		return false;
	}
	$rules = array();
	$r = CampaignsManager::getCampaignsPhones($cpg_id);
	if( $r ){
		while( $rule = ria_mysql_fetch_assoc($r) ){
			$rule['type'] = 'mobile';
			$rule['value'] = conv_france_international_number($rule['value']);
			$rule['label'] = conv_france_international_number($rule['value']);
			$rules[] = $rule;
		}
	}
	$r = CampaignsManager::getCampaignsUsers($cpg_id);
	if( $r ){
		while( $rule = ria_mysql_fetch_assoc($r) ){
			$rule['type'] = 'usr';
			$rule['label'] = gu_users_get_name($rule['value']);
			$rules[] = $rule;
		}
	}
	$r = CampaignsManager::getCampaignsSegments($cpg_id);
	if( $r ){
		while( $rule = ria_mysql_fetch_assoc($r) ){
			$rule['type'] = 'seg';
			$rule['value'] = $rule['seg_id'];
			$rSeg = seg_segments_get($rule['seg_id']);
			if( $rSeg && ria_mysql_num_rows($rSeg) ){
				$seg = ria_mysql_fetch_assoc($rSeg);
				$rule['label'] = $seg['name'];
				$rules[] = $rule;
			}
		}
	}
	return $rules;
}

/** Cette fonction permet de copier les eègle d'une campagne a une autre
 *	@param int $src_cpg_id Identifiant de la campagne depuis laquelle on veux copier
 *	@param int $dest_cpg_id Identifiant de la campagne vers laquelle recopier
 *
 *	@return bool true si succès false si erreur
 */
function copyRules($src_cpg_id, $dest_cpg_id){
	$error = false;
	if( !CampaignsManager::copyCampaignsUsers($src_cpg_id, $dest_cpg_id) ){
		$error = true;
	}
	if( !CampaignsManager::copyCampaignsPhones($src_cpg_id, $dest_cpg_id) ){
		$error = true;
	}
	if( !CampaignsManager::copyCampaignsSegments($src_cpg_id, $dest_cpg_id) ){
		$error = true;
	}

	if( $error ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer le nombre potentiel de message a envoyer */
function getIncludeCount($campaign){
	try{
		$campaign = new Campaigns( $campaign );
		$campaign->setCampaignUsers();
		return count($campaign->getUsersMobile());
	}catch(Exception $e){
		return false;
	}
}

function executeCampaign($cpg_id){
	if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
		return false;
	}

	$date = date( "Y-m-d H:i:s" );
	$r_campaign = CampaignsManager::getCampaigns( $cpg_id, false, $date, $date );
	if( !$r_campaign ){
		return false;
	}

	$campaign = ria_mysql_fetch_assoc( $r_campaign );

	if( trim( $campaign['period'] ) != '' ){
		return false;
	}

	try{
		$obj = new Campaigns( $campaign );
		$obj->exec();
	}catch( Exception $e ){
		error_log( $e->getMessage() );
		return false;
	}

	return true;
}

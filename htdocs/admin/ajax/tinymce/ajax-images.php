<?php
	require_once('images.inc.php');
	
	$thumb = $config['img_sizes']['medium'];

	$data = array();
	$data['nbpages'] = 0;
	$data['thumb'] = $thumb;
	
	$_POST['imgs'] = isset($_POST['imgs']) ? $_POST['imgs'] : array();
	
	if( isset($_POST['get-source']) ){
		$data = img_images_source_infos( $_POST['img_id'] );
	}elseif( isset($_POST['create-perso']) ){
		if( !img_images_thumbnails_refresh($_POST['create-perso'], false, array( 'width'=>$_POST['width'], 'height'=>$_POST['height'] )) ){
			$data = array( 'result' => '0' );
		}else{
			$data = array( 'result' => '1' );
		}
	}elseif( isset($_POST['images']) ){
		$_POST['q'] = isset($_POST['q']) ? urldecode( trim($_POST['q']) ) : '';
		$rimg = img_images_get( 0, '', '', '', $_POST['q'], null, false, ( $_POST['type'] == 'all' ? false: $_POST['type']) );
		
		if( $rimg && ria_mysql_num_rows($rimg) ){
			
			$_POST['page'] = isset($_POST['page']) && is_numeric($_POST['page']) && $_POST['page']>0 ? $_POST['page'] : 1;
			
			if(	ria_mysql_num_rows($rimg) > ($_POST['page']-1)*75 ){
				ria_mysql_data_seek( $rimg, ($_POST['page']-1)*75 );
			
				$count = 0; $no_files = 0;
				while( $img = ria_mysql_fetch_array($rimg) ){
					$file = $config['img_dir'].'/'.$thumb['dir'].'/'.$img['id'].'.'.$thumb['format'];
					if( !file_exists($file) ){ $no_files++; continue; }
					if( $count>=75 ) break;
					
					$data['images'][] = array(
						'id' => $img['id'],
						'name' => $img['src_name']
					);
					
					$count++;
				}
				
				$data['nbpages'] = ceil( (ria_mysql_num_rows($rimg)-$no_files) / 75 );
			}
		} 

		if( !$rimg || !ria_mysql_num_rows($rimg) || !$data['nbpages'] ){
			
			$rsearch = search3( 1, $_POST['q'], 1, 0, true, false, 6, array($_POST['type']) );
			if( $rsearch && ria_mysql_num_rows($rsearch) ){
				if(	ria_mysql_num_rows($rsearch) > ($_POST['page']-1)*75 ){
					
					ria_mysql_data_seek( $rsearch, ($_POST['page']-1)*75 );
					
					$count = 0; $no_files = 0;
					while( $res = ria_mysql_fetch_array($rsearch) ){
						if( in_array($res['img_id'], $_POST['imgs']) ) continue;
						$file = $config['img_dir'].'/'.$thumb['dir'].'/'.$res['img_id'].'.'.$thumb['format'];
						if( !$res['img_id'] || !file_exists($file) ){ $no_files++; continue; }
						if( $count>=75 ) break;
						
						$data['images'][] = array(
							'id' => $res['img_id'],
							'name' => $res['name']
						);
						
						$_POST['imgs'][] = $res['img_id'];
						$count++;
					}
					
					$data['nbpages'] = ceil( (ria_mysql_num_rows($rsearch)-$no_files) / 75 );
					
				}
			}
			
		}
	} elseif( isset($_GET['size']) ){
		if( isset($config['img_sizes'][ $_GET['size'] ]) ){
			$data = $config['img_sizes'][ $_GET['size'] ];
		} else {
			$data = $config['img_sizes']['small'];
		}
	} elseif( isset($_GET['selectImage']) ){
		$data = array( 'id' => '', 'name' => '', 'url' => '' );
		if( $_GET['selectImage']>0 ){
			$rimg = img_images_get( $_GET['selectImage'] );
			if( $rimg && ria_mysql_num_rows($rimg) ){
				$img = ria_mysql_fetch_array( $rimg );
				
				$thumb = $config['img_sizes']['medium'];
				$data['url'] = $config['img_url'].'/'.$thumb['dir'].'/'.$img['id'].'.'.$thumb['format'];
				$data['name'] = $img['src_name'];
				$data['id'] = $img['id'];
				
				$data = array_merge( $data, img_images_source_infos($img['id']) );
			}
		}

	}
	
	header('Content-type: text/json');
	header('Content-type: application/json');
	print json_encode( $data );
    exit;
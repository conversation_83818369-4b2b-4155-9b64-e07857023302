<?php
	
	require_once('search.inc.php');
	require_once('documents.inc.php');
	require_once('products.inc.php');
	require_once('categories.inc.php');
	
	$json = array();
	
	if( isset($_GET['search'], $_GET['tab']) ){
		
		$type = array( 'prd-cat', 'prd' );
		switch( $_GET['tab'] ){
			case 'product_tab' :
				$type = array( 'prd' ); break;
			case 'category_tab' :
				$type = array( 'prd-cat' ); break;
			case 'cms_tab' :
				$type = array( 'cms' ); break;
		}
		
		$res = false;
		switch( $_GET['tab'] ){
			case 'product_tab' :
			case 'category_tab' :
			case 'cms_tab' :
				$res = search3( 1, $_GET['term'], 1, 0, false, false, 6, $type );
				break;
			case 'document_tab' :
				$res = doc_documents_get( 0, 0, false, $_GET['term'], false );
				break;
			case 'store_tab' :
				$res = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), $_GET['term'], true );
				break;
		}
		
		if( $res && ria_mysql_num_rows($res) ){
			ria_mysql_data_seek( $res, 0 );
			$ar_tags = array();
			while( $r = ria_mysql_fetch_array($res) ){
				if( in_array(!in_array( $_GET['tab'], array('document_tab','store_tab') ) ? $r['tag'] : $r['id'], $ar_tags) ){
					continue;
				}
				
				if( $_GET['tab']=='product_tab' ){
					if( prd_products_get_childonly($r['tag']) ){
						continue;
					}
				}
				
				$json[] = array(
					'id'	=> !in_array( $_GET['tab'], array('document_tab','store_tab') ) ? $r['tag'] : $r['id'],
					'value'	=> $_GET['term'],
					'label' => $r['name']
				);
				
				$ar_tags[] = !in_array( $_GET['tab'], array('document_tab','store_tab') ) ? $r['tag'] : $r['id'];
				
				if( sizeof($json)>=25 ){
					break;
				}
			}
		}
	}
	
	// header('Content-type: text/json');
	// header('Content-type: application/json');
	print json_encode( $json );
    exit;
<?php

	/**	\file ajax-type-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un type de document.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_TYPE');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('documents.inc.php');
	
	$response = array('success' => doc_types_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;

<?php

    require_once('delivery.inc.php');

    if( isset($_GET['getincludeprd'], $_GET['zone']) ){
        $ar_rules = array();

        if ($_GET['zone']){
            // charge les règles pour les produits
            $rprd = dlv_zones_products_get( $_GET['zone'] );
            if( $rprd && ria_mysql_num_rows($rprd) ){
                
                while( $prd = ria_mysql_fetch_assoc($rprd) ){
                    $name_prd = $prd['ref'].' - '.$prd['name'];
                    $name_prd = str_replace( $prd['ref'].' - '.$prd['ref'], $prd['ref'], $name_prd );
                    
                    $ar_rules['prd'][] = array( 
                        'id'			=> $prd['id'], 
                        'name'			=> $name_prd,
                        'include'		=> ($prd['include'] ? '+' : '-'), 
                        'url' 			=> '/admin/catalog/product.php?cat=0&prd='.$prd['id'],
                    );
                }
                
            }
            
            // charge les règles pour les catégories
            $rcat =  dlv_zones_categories_get( $_GET['zone'] );
            if( $rcat && ria_mysql_num_rows($rcat) ){
                
                while( $cat = ria_mysql_fetch_assoc($rcat) ){
                    $ar_rules['cat'][] = array( 
                        'id'			=> $cat['id'], 
                        'name'			=> $cat['name'], 
                        'include'		=> ($cat['include'] ? '+' : '-'), 
                        'prds'			=> $cat['products'],
                        'url' 			=> '/admin/catalog/index.php?cat='.$cat['id'],
                    );
                }
                
            }
            
            // charge les règles pour les marques
            $rbrd =  dlv_zones_brands_get( $_GET['zone'] );
            if( $rbrd && ria_mysql_num_rows($rbrd) ){
                
                while( $brd = ria_mysql_fetch_assoc($rbrd) ){
                    $ar_rules['brd'][] = array( 
                        'id'			=> $brd['id'], 
                        'name'			=> $brd['name'], 
                        'include'		=> ($brd['include'] ? '+' : '-'), 
                        'prds'			=> $brd['products'],
                        'url'			=> '/admin/catalog/index.php?brd='.$brd['id'],
                    );
                }
            }
        }
        print json_encode( $ar_rules );

    } elseif( isset($_GET['delrule'], $_GET['zone'], $_GET['type'], $_GET['cnt']) ){
		$res = false;
        if ($_GET['zone']){    
			foreach( $_GET['type'] as $key=>$type ){
				if( !isset($_GET['cnt'][ $key ]) ){
					continue;
				}

				$cnt = $_GET['cnt'][ $key ];

				switch( $type ){
					case 'prd' :
						$res = dlv_zones_products_del( $_GET['zone'], $cnt );
						break;
					case 'cat' :
						$res = dlv_zones_categories_del( $_GET['zone'], $cnt );
						break;
					case 'brd' :
                        $res = dlv_zones_brands_del( $_GET['zone'], $cnt );
						break;
				}
			}
		}
        print json_encode( array( 'done' => $res ) );
    } elseif( isset($_GET['addrule'], $_GET['zone'], $_GET['type'], $_GET['cnt'], $_GET['include']) ){
		$res = false;
        if ($_GET['zone']) {
			switch( $_GET['type'] ){
				case 'prd' :
					if( preg_match('/ref-/', $_GET['cnt']) ){
						$_GET['cnt'] = str_replace('ref-', '', $_GET['cnt']);
						$_GET['cnt'] = prd_products_get_id( $_GET['cnt'] );
						if( !is_numeric($_GET['cnt']) || $_GET['cnt']<=0 ){
							$res = false;
							break;
						}
					}
					$res = dlv_zones_products_add( $_GET['zone'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'cat' :
					$res = dlv_zones_categories_add( $_GET['zone'], $_GET['cnt'], $_GET['include'] );
					break;
				case 'brd' :
					$res = dlv_zones_brands_add( $_GET['zone'], $_GET['cnt'], $_GET['include'] );
					break;
			}
		}
		print json_encode( array('done' => $res) );
		
	} elseif( isset($_REQUEST['saveallcatalog'], $_REQUEST['zone'], $_REQUEST['all'])){
		$all_catalog = isset($_POST['dlv-all-catalog']) ? $_POST['dlv-all-catalog'] : 1;
        $res = dlv_zones_set_all_catalog($_REQUEST['zone'], $_REQUEST['all']);

		print json_encode( array( 'done' => $res ) );
    }

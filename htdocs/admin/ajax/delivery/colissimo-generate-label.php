<?php

$ord_id = isset($_GET['ord_id']) ? intval($_GET['ord_id']) : null;
$bl_id = isset($_GET['bl_id']) ? intval($_GET['bl_id']) : null;
$colis = isset($_GET['colis']) ? $_GET['colis'] : false;
$checkExisting = isset($_GET['checkExisting']) ? !!intval($_GET['checkExisting']) : true;
$sort = isset($_GET['sort']) ? $_GET['sort'] : false;
$supplier = isset($_GET['supplier']) ? $_GET['supplier'] : false;
$prd = isset($_GET['prd']) ? $_GET['prd'] : false;
$line = isset($_GET['line']) ? $_GET['line'] : false;
$correled = isset($_GET['correled']) ? !!intval($_GET['correled']) : false;

header('Content-Type: application/json; charset: UTF-8;');

try {
    if (!gu_user_is_authorized('_RGH_ADMIN_BL_GENERATE_LABEL_EDIT')) {
        throw new ColissimoClientException(_('Accès refusé'));
    }

    if (!isset($config['colissimo_api_contract_number']) || !isset($config['colissimo_api_password'])) {
        throw new Exception(_('Clés d\'API Colissimo manquantes'));
    }

    require_once('Colissimo/Client.php');
    require_once('Colissimo/ClientException.php');
    require_once('Colissimo/HttpClient.php');

    $colissimoApiClient = new ColissimoClient();
    $colissimoApiClient->setHttpClient(
        new ColissimoHttpClient($config['colissimo_api_contract_number'], $config['colissimo_api_password'])
    );

    $parameters = array();
    if (isset($_GET['insurance'])) {
        if (floatval($_GET['insurance']) < 0) {
            throw new ColissimoClientException('La valeur de l\'assurance n\'est pas valide');
        } else {
            $parameters = array('letter' => array ('parcel' => array ('insuranceValue' => floatval($_GET['insurance']))));
        }
    }

    $data = $colissimoApiClient->generateLabelFromBl($bl_id, $ord_id, $colis, $sort, $supplier, $prd, $line, false, true, $parameters);
} catch (ColissimoClientException $e) {
    error_log($e->getMessage(), E_USER_ERROR);
    $data = array('error' => $e->getMessage());
} catch (Exception $e) {
    error_log($e->getMessage(), E_USER_ERROR);
    $data = array('error' => 'Erreur lors de la génération de l\'étiquette');
}

print json_encode($data, JSON_FORCE_OBJECT);
exit;

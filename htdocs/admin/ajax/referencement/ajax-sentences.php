<?php

    /** \file ajax-sentences.php
     *  Ce fichier intervient dans la mise en place de phrase dynamique pour le référencement de contenu dans RiaShop.
     *  ex. "Retrouvez notre magasin [nom du magasin] près de chez-vous"
     *  Ces phrases sont ensuites utilisés dans la meta-desc ou title du site.
     */

    // Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REF');

    if(!isset($_GET['cls_id']) || !is_numeric($_GET['cls_id']) || $_GET['cls_id'] < 0){
        print json_encode(array('code' => '400', 'response' => _('Classe invalide ou manquante.')));
    } elseif(!isset($_GET['tag']) || !is_numeric($_GET['tag']) || $_GET['tag'] < 0){
        print json_encode(array('code' => '400', 'response' => _('Identifiant de balise invalide ou manquante.')));
    } elseif(!isset($_GET['lng']) || trim($_GET['lng']) == ''){
        print json_encode(array('code' => '400', 'response' => _('Langue invalide ou manquante.')));
    } else {
        if (isset($_GET['del'])) {
            if (!isset($_GET['seo_id']) || !is_numeric($_GET['seo_id']) || $_GET['seo_id'] <= 0){
                print json_encode(array('code' => '400', 'response' => _('Identifiant de la phrase invalide ou manquant.')));
            } else {
                $seo_id = $_GET['seo_id'];
                if (!seo_templates_del($seo_id)){
                    print json_encode(array('code' => '400', 'response' => _('Une erreur est survenue lors de la suppression de la phrase de référencement personnalisé.').'<br />'._('Veuillez réessayer ou prendre contact pour signaler l\'erreur.')));
                } else {
                    print json_encode(array('code' => '100') );
                }
            }
        } else {
            $tag = $_GET['tag'] == 1 ? 'title' : 'desc';
            $cls = $_GET['cls_id'];
            $constants = seo_templates_constants_get_by_class($cls);
            $r_sentences = seo_templates_get(0, $tag, $cls, $_GET['lng']);

            if (!$r_sentences){
                print json_encode(array('code' => '400', 'response' => _('Une erreur est survenue lors de la récupération des phrases de référencement personnalisé.').'<br />'._('Veuillez réessayer ou prendre contact pour signaler l\'erreur.')));
            }

            $html_constants = '';
            $html_sentences = '';

            foreach ($constants as $key => $constant) {
                $html_constants .= '<option class="variable-option" value="'.$key.'" ondblclick="optionSelection()" >'.$constant['name'].'</option>';
            }

            if (ria_mysql_num_rows($r_sentences)){
                $html_sentences .= '
                <table id="sentences-table" class="checklist">
                    <caption>'._('Phrases enregistrées').'</caption>
                    <tbody>';



                while ($sentence = ria_mysql_fetch_array($r_sentences)) {
                    $constants = seo_templates_constants_get_by_class($cls);
                    $content = $sentence['content'];
                    foreach($constants as $key => $constant){
                        if ($constant['fld_id'] !== null){
                            $content = str_replace('fld-'.$constant['fld_id'], '['.$constant['name'].']', $content);
                        }
                    }
                    $html_sentences .= '<tr id="sentence-' . $sentence['id'] . '">';
                    $html_sentences .= '<td headers="sentence" class="sentence-text">' . htmlspecialchars($content) . '</td>';
                    $html_sentences .= '<td headers="action" class="td-action">';
                    $html_sentences .= '<a class="edit-sentence button" onclick="editSentence(' . $sentence['id'] . ')">'._('Editer').'</a>';
                    $html_sentences .= '<br /><a class="del edit button" onclick="delSentence(' . $sentence['id'] . ')">'._('Supprimer').'</a>';
                    $html_sentences .= '</td>';
                    $html_sentences .= '</tr>';
                }

                $html_sentences .= '
                    </tbody>
                </table>
                ';
            }

            $_SESSION['sentence_cls_id'] = $cls;
            $_SESSION['sentence_tag'] = $tag;

            print json_encode(array('code' => '100', 'response' => array('html_constants' => $html_constants, 'html_sentences' => $html_sentences ) ) );
        }
    }




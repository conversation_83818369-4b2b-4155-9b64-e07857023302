<?php

	/**	\file json-moderation.php
	 * 
	 * 	Ce fichier permet le chargement en Ajax d'une liste de messages à modérer. Les paramètres acceptés sont les suivants :
	 * 	- p : Facultatif, page de résultat à retourner. La valeur par défaut est 1.
	 *  - limit : Facultatif, nombre de maximum de résultat à retourner. La valeur par défaut est 25.
	 * 	- wst : Facultatif, identifiant du website à retourner.
	 * 	- str : Facultatif, identifiant du magasin sur lequel filtrer le résultat
	 *  - sort : Facultatif, colonne sur laquelle le résultat sera trié. La valeur par défaut est cnt_date_created
	 *  - dir : Facultatif, direction dans laquelle le résultat sera trié. La valeur par défaut est asc (croissant).
	 *  - type : Facultatif, type de message sur lequel filtrer le résultat
	 *  - usr : Facultatif, identifiant de l'auteur du message sur lequel filtrer le résultat
	 *  - have-usr : Facultatif, si non renseigné, tous les messages sont retournés. Si vrai, seul les messages dont l'auteur possède un compte client. Si faux, seuls les contacts ne disposant d'aucun compte client.
	 *  - have-rep : Facultatif, si non renseigné, tous les messages. Si vrai, seul les messages ayant reçu une réponse. Si faux, seul les messages n'ayant pas encore reçu de réponse.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	// Inclut les fonctions pour les contacts
	require_once('users.inc.php');
	require_once('contacts.inc.php');
	require_once('view.admin.inc.php');
	
	// Défini si la recherche fait partie d'une pagination
	$page = isset($_GET['p']) && is_numeric($_GET['p']) && $_GET['p']>0 ? $_GET['p'] : 1;
	
	// Détermine le nombre limite de contacts retournés
	$limit = isset($_GET['limit']) && is_numeric($_GET['limit']) && $_GET['limit']>0 ? $_GET['limit'] : 25;
	
	// Filtres
	$wst_id	  = 0;
	if( !isset($_GET['wst']) || trim($_GET['wst'])=='' || !is_numeric($_GET['wst']) || $_GET['wst']<0 ){
		if( isset($_SESSION['websitepicker']) ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}else{
		$_SESSION['websitepicker'] = $_GET['wst'];
		$wst_id = $_GET['wst'];
	}

	$str_id = isset($_GET['str']) && is_numeric($_GET['str']) && $_GET['str'] ? $_GET['str'] : 0;

	$have_usr = !isset($_GET['have-usr']) || trim($_GET['have-usr'])=='' || $_GET['have-usr']>1 || $_GET['have-usr']<0 ? null : $_GET['have-usr'];
	$have_rep = !isset($_GET['have-rep']) || trim($_GET['have-rep'])=='' || $_GET['have-rep']>1 || $_GET['have-rep']<0 ? null : $_GET['have-rep'];
	
	// Tri
	$sort = isset($_GET['sort']) && trim($_GET['sort'])!='' ? $_GET['sort'] : 'cnt_date_created';
	$dir = isset($_GET['dir']) && in_array($_GET['dir'], array('asc', 'desc')) ? $_GET['dir'] : 'asc';
	
	// Type de message
	$type = isset($_GET['type']) && gu_messages_types_exists($_GET['type']) ? $_GET['type'] : '';

	// Utilisateur propriétaire des messages
	$usr = isset($_GET['usr']) && is_numeric($_GET['usr']) ? $_GET['usr'] : 0;
	
	// Récupère les contacts, 0, 0, 0, false, true
	if($usr == 0){
		$rmsg = messages_get( 0 , $type, 0, 0, 0, false, true, false, 0, 0, false, false, $have_rep, $have_usr, array('cnt_date_created'=>$dir), false, null, false, '', $wst_id, $str_id );
	}else{
		$rmsg = messages_get( $usr, '', 0, 0, 0, false, true );
	}
	// print 'params : <br />';
	// print 'page : '.$page.'<br />';
	// print 'limit : '.$limit.'<br />';
	// print 'wst_id : '.$wst_id.'<br />';
	// print 'str_id : '.$str_id.'<br />';
	// print 'have_usr : '.$have_usr.'<br />';
	// print 'have_rep : '.$have_rep.'<br />';
	// print 'sort : '.$sort.'<br />';
	// print 'dir : '.$dir.'<br />';
	// print 'type : '.$type.'<br />';
	// Tableau contenant tous les contacts
	$messages = array();
	
	if( $rmsg!=false ){
		
		// Information générale
		$messages['nb_msg'] = ria_mysql_num_rows($rmsg);
		$messages['moderate'] = gu_messages_types_is_moderate($type);
		$messages['tenant'] = $config['tnt_id'];
		$messages['type'] = $type;
		
		// On se place sur la position de départ de lecture des résultats
		if( $page>1 && ria_mysql_num_rows($rmsg)>(($page-1)*$limit) )
			ria_mysql_data_seek( $rmsg, ($page-1)*$limit );
		
		$count = 0;
		while( $msg = ria_mysql_fetch_array($rmsg) ){
			if( $msg['usr_id']>0 ){
				$rusr = gu_users_get($msg['usr_id']);
				if( $rusr && ria_mysql_num_rows($rusr) )
					$usr = ria_mysql_fetch_array($rusr);
			}
			
			$origins = '';
			$stats = stats_origins_get( $msg['id'], CLS_MESSAGE );
			if( $stats && ria_mysql_num_rows($stats) ){
				$stat = ria_mysql_fetch_array( $stats );
				$origins .= '<div class="infos-compl">';
				$origins .= view_source_origin($stat);
				$origins .= '</div>';
			}
			
			// Intégration d'un tableau contenant les informations sur un alert de livraison
			$messages['message'][] = array(
				'id' => $msg['id'],
				'subject' => $msg['subject'],
				'body' => nl2br( htmlspecialchars($msg['body']) ),
				'date_created' => $msg['date_created'],
				'usr_id' => $msg['usr_id'],
				'is_sync' => $msg['is_sync'],
				'nbrep' => $msg['nbrep'],
				'note' => $msg['note'],
				'note_dlv' => $msg['note_dlv'],
				'note_pkg' => $msg['note_pkg'],
				'ip' => view_ats_ip( $msg['ip'], $msg['id'] ),
				'spam_id' => $msg['spam_id']!=0 ? $msg['spam_id'] : 0,
				'is_publish' => $msg['publish'],
				'moderation_comment' => $msg['comment'] !== null ? $msg['comment'] : '',
				'date_publish' => $msg['date_publish']!='' ? $msg['date_publish'] : '',
				'origins' => $origins
			);
			
			
			if( $msg['usr_id'] && isset($usr) ){
				$messages['message'][$count]['firstname'] = $usr['adr_firstname'];
				$messages['message'][$count]['lastname'] = $usr['adr_lastname'];
				$messages['message'][$count]['society'] = $usr['society'];
				$messages['message'][$count]['email'] = $usr['email'];
				$messages['message'][$count]['phone'] = $usr['phone'];
				$messages['message'][$count]['surnom'] = fld_object_values_get( $usr['id'], _FLD_PSEUDO );
			} else {
				$messages['message'][$count]['firstname'] = $msg['firstname'];
				$messages['message'][$count]['lastname'] = $msg['lastname'];
				$messages['message'][$count]['society'] = $msg['society'];
				$messages['message'][$count]['email'] = $msg['email'];
				$messages['message'][$count]['phone'] = $msg['phone'];
			}
			
			if( $msg['usr_publish']>0 && ($usrP = ria_mysql_fetch_array( gu_users_get($msg['usr_publish']) )) ){
				$messages['message'][$count]['usr_publish'] = $msg['usr_publish'];
				$messages['message'][$count]['adr_firstname'] = $usrP['adr_firstname'];
				$messages['message'][$count]['adr_lastname'] = $usrP['adr_lastname'];
				$messages['message'][$count]['usr_publish_is_sync'] = $usrP['is_sync'];
			} else {
				$messages['message'][$count]['usr_publish'] = 0;
				$messages['message'][$count]['adr_firstname'] = '';
				$messages['message'][$count]['adr_lastname'] = '';
				$messages['message'][$count]['usr_publish_is_sync'] = 0;
			}
			
			// Récupère le devis conserné par le commentaire
			if( is_numeric($msg['ord_id']) && $msg['ord_id'] ){
				$messages['message'][$count]['ord_url'] = '/admin/orders/order.php?ord='.$msg['ord_id'];
				$messages['message'][$count]['ord_id'] = $msg['ord_id'];
			}
			
			// Récupère le produit conserné par le commentaire
			if( is_numeric($msg['prd_id']) && $msg['prd_id'] && prd_products_exists($msg['prd_id']) ){
				$prd = ria_mysql_fetch_array( prd_products_get_simple($msg['prd_id']) );
				
				$url = '#';
				$rcat = prd_products_categories_get($prd['id'], true);
				if( $rcat!=false && ria_mysql_num_rows($rcat)>0 ){
					$cat = ria_mysql_fetch_array($rcat);
					$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$prd['id'];
				}
				
				$messages['message'][$count]['prd_url'] = $url;
				$messages['message'][$count]['prd_is_sync'] = $prd['is_sync'];
				$messages['message'][$count]['prd_title'] = $prd['title'];
			}
			
			// Récupère les destinataires
			if( $msg['email_to'] ){
				$to = explode(',', $msg['email_to']);
				foreach( $to as $key=> $email ){
					if( trim($email) == '' ){
						continue;
					}

					$show_user = true;
					if( $config['tnt_id']==8 ){
						$rstr = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, true, $email );
						if( $rstr && ria_mysql_num_rows($rstr) ){
							$str = ria_mysql_fetch_array( $rstr );
							
							$messages['contact'][$count]['to'][] = array(
								'id' => $str['id'],
								'name' => $str['name'],
								'title' => $str['title'],
								'email' => $email,
								'is_sync' => $str['is_sync'],
								'type' => 'store'
							);

							$show_user = false;
						}
					}

					if( $show_user ){
						$usr_to = gu_users_get( 0, $email );
						if( $usr_to!=false && ria_mysql_num_rows($usr_to)>0 ){
							$usr_to = ria_mysql_fetch_array($usr_to);
							$messages['contact'][$count]['to'][] = array(
								'id' => $usr_to['id'],
								'firstname' => $usr_to['adr_firstname'],
								'lastname' => $usr_to['adr_lastname'],
								'society' => $usr_to['society'],
								'email' => $email,
								'is_sync' => $usr_to['is_sync'],
								'type' => 'user'
							);
						} else {
							$messages['contact'][$count]['to'][] = array(
								'id' => 0,
								'email' => $email
							);
						}
					}
				}
			}else{
				$messages['contact'][$count]['to'][] = array(
						'id' => '',
						'firstname' => '',
						'lastname' => '',
						'society' => '',
						'email' => '',
						'is_sync' => 0
				);
			}

			
			// Récupère les destinataires en copie
			if( $msg['email_cc'] ){
				$cc = explode(',', $msg['email_cc']);
				foreach( $cc as $key=> $email ){
					if( trim($email) == '' ){
						continue;
					}

					$show_user = true;
					if( $config['tnt_id']==8 ){
						$rstr = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, true, $email );
						if( $rstr && ria_mysql_num_rows($rstr) ){
							$str = ria_mysql_fetch_array( $rstr );
							
							$messages['contact'][$count]['cc'][] = array(
								'id' => $str['id'],
								'name' => $str['name'],
								'title' => $str['title'],
								'email' => $email,
								'is_sync' => $str['is_sync'],
								'type' => 'store'
							);

							$show_user = false;
						}
					}

					if( $show_user ){
						$usr_cc = gu_users_get( 0, $email );
						if( $usr_cc!=false && ria_mysql_num_rows($usr_cc)>0 ){
							$usr_cc = ria_mysql_fetch_array($usr_cc);
							$messages['contact'][$count]['cc'][] = array(
								'id' => $usr_cc['id'],
								'firstname' => $usr_cc['adr_firstname'],
								'lastname' => $usr_cc['adr_lastname'],
								'society' => $usr_cc['society'],
								'email' => $email,
								'is_sync' => $usr_cc['is_sync'],
								'type' => 'user'
							);
						} else {
							$messages['contact'][$count]['cc'][] = array(
								'id' => 0,
								'email' => $email
							);
						}
					}
				}
			}else{
				$messages['contact'][$count]['cc'][] = array(
						'id' => '',
						'firstname' => '',
						'lastname' => '',
						'society' => '',
						'email' => '',
						'is_sync' => 0
				);
			}
			
			$messages['contact'][$count]['docs'] = array();
			$rimg = gu_messages_images_get( $msg['id'] );
			if( $rimg && ria_mysql_num_rows($rimg) ){
				$sizeImg = $config['img_sizes']['medium'];
				$c = 1;
				while( $img = ria_mysql_fetch_array($rimg) ){
					$messages['contact'][$count]['docs'][] = '<a href="'.$config['img_url'].'/'.$sizeImg['dir'].'/'.$img['id'].'.'.$sizeImg['format'].'" target="_blank">Image '.$c.'</a>';
					$c++;
				}
			}
			
			$rd = messages_files_get( 0, $msg['id'] );
			if( $rd && ria_mysql_num_rows($rd) ){
				while( $d = ria_mysql_fetch_array($rd) )
					$messages['contact'][$count]['docs'][] = '<a href="/admin/customers/dl.php?file='.$d['id'].'">'.$d['name'].'</a>';
			}
			
			// Récupère les champs avancés de chaque contacts
			$fields_libs = fld_fields_get( 0, 0, -2, 0, 0, $msg['id'], null, array(), false, array(), null, CLS_MESSAGE );
			if( $fields_libs ){
				while($fld = ria_mysql_fetch_array($fields_libs)) {
					$messages['contact'][$count]['infos'][] = array(
						'name' => $fld['name'],
						'value' => fld_fields_view($fld)
					);
				}
			}
			
			// Récupère les réponses au message de contact
			if( $msg['nbrep']>0 ){
				$rreply = contacts_get_replies( $msg['id'] );
				if( $rreply!=false ){
					$r = 0;
					while( $reply = ria_mysql_fetch_array($rreply) ){
						$messages['contact'][$count]['reply'][] = array(
							'id' => $reply['id'],
							'firstname' => $reply['firstname'],
							'lastname' => $reply['lastname'],
							'society' => $reply['society'],
							'email' => $reply['email'],
							'phone' => $reply['phone'],
							'subject' => $reply['subject'],
							'body' => nl2br($reply['body']),
							'date_created' => $reply['date_created'],
							'usr_id' => $reply['usr_id']
						);
						
						// Récupère les pièces jointes d'une réponse
						$rfile = messages_files_get(0,$reply['id']);
						if( $rfile!=false ){
							while( $file = ria_mysql_fetch_array($rfile) ){
								// Taille du fichier
								$size = round(($file['size']/1024),1);
								$size = $size>1024 ? $size = round(($size/1024),1).' Mo' : $size.' Ko';
								
								$messages['contact'][$count]['reply'][$r]['file'][] = array(
									'id' => $file['id'],
									'name' => $file['name'],
									'size' => $size
								);
							}
						}
						
						// Incremente le nombre de réponse
						$r++;
					}
				}
			}
			
			// Comptabilise le nombre d'alert ajouté dans la liste
			$count++;
			
			// Si le nombre d'alert retourné est fixé et atteint, on arrête la lecture du résultats
			if( $count>=$limit )
				break;
		}
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $messages );

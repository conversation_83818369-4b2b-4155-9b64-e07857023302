<?php

	/**	\file ajax-fields.php
	 * 	Ce fichier permet de réaliser des actions sur les champs avancés en Ajax. Les actions supportées sont les suivantes :
	 * 	- del-img-field : permet la suppression de l'image enregistrée dans un champ avancé
	 *  - reload-values :
	 *  - getcat :
	 *  Pour chacune de ces actions, les paramètres vont être différents :
	 * 	- del-img-field :
	 * 		- cls : obligatoire, identifiant de la classe
	 * 		- fld : obligatoire, identifiant du champ
	 * 		- obj1 : obligatoire, identifiant de l'objet à mettre à jour
	 * 		- obj2 : obligatoire, identifiant de l'objet à mettre à jour
	 * 		- obj3 : obligatoire, identifiant de l'objet à mettre à jour
	 * 	- reload-values :
	 * 		- fld : obligatoire, identifiant du champ
	 * 	- getcat :
	 * 		- cls : obligatoire, identifiant de la classe
	 */

	require_once('fields.inc.php');
	
	if( isset($_POST['del-img-field']) ){
		if( !isset($_POST['cls'], $_POST['fld'], $_POST['obj1'], $_POST['obj2'], $_POST['obj3']) ){
			print 'ko';
			exit;
		}

		$obj = array();
		if( is_numeric($_POST['obj1']) && $_POST['obj1']>0 ){
			$obj[] = $_POST['obj1'];
		}
		if( is_numeric($_POST['obj2']) && $_POST['obj2']>0 ){
			$obj[] = $_POST['obj2'];
		}
		if( is_numeric($_POST['obj3']) && $_POST['obj3']>0 ){
			$obj[] = $_POST['obj3'];
		}
		
		if( fld_object_values_set($obj, $_POST['fld'], '') ){
			print 'ok';
		}else{
			print 'ko';
		}

		exit;
	}

	if( isset($_GET['reload-values']) ){
		if( !isset($_GET['fld']) || !fld_fields_exists($_GET['fld']) ){
			return;
		}
		
		if (!isset($_GET['in-config']) || !$_GET['in-config']) {
			$f = ria_mysql_fetch_array( fld_fields_get($_GET['fld']) );
			print fld_fields_edit( $f, '', false, isset($_GET['val_check']) ? $_GET['val_check'] : '' );
		}else{
			$type_id = fld_fields_get_type($_GET['fld']);
			$lng = isset($_GET['lng']) ? $_GET['lng'] : false;
			
			if ($type_id == FLD_TYPE_SELECT_HIERARCHY) {
				$all_vals = array();

				$values = fld_restricted_values_get(0, $_GET['fld'], '', 0, '', $lng);
				while ($r = ria_mysql_fetch_array($values)) {
					$name_val = $r['name'];
					$id = $r['id'];
					while ($vals2 = fld_restricted_values_get($r['parent'])) {
						if ($r = ria_mysql_fetch_array($vals2))
							$name_val = $r['name'] . ' >> ' . $name_val;
					}
					$all_vals[] = array('id' => $id, 'name' => htmlspecialchars($name_val));
				}

				$all_vals = array_msort($all_vals, array('name' => SORT_ASC));
				if (is_array($all_vals) && sizeof($all_vals)) {
					foreach ($all_vals as $val) {
						print '<option value="' . $val['id'] . '" title="' . $val['name'] . '">' . htmlspecialchars( $val['name'] ) . '</option>';
					}
				} else {
					print '<option value="">'._('Aucune valeur saisie').'</option>';
				}
			} else {
				$values = fld_restricted_values_get(0, $_GET['fld'], '', 0, '', $lng);
				if ($values && ria_mysql_num_rows($values)) {
					while ($r = ria_mysql_fetch_array($values)) {
						print '<option value="' . $r['id'] . '">' . htmlspecialchars($r['name']) . '</option>';
					}
				} else {
					print '<option value="">'._('Aucune valeur saisie').'</option>';
				}
			}
		}

		exit;
	}

	if( isset($_GET['getcat'], $_GET['cls']) ){
		$categories = fld_categories_get( 0, false, $_GET['cls'] );
		while( $c = ria_mysql_fetch_array($categories) ){
			print '<option value="'.$c['id'].'">'.htmlspecialchars($c['name']).'</option>';
		}
		exit;
	}

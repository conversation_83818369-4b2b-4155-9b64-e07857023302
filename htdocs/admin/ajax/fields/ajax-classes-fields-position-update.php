<?php

	/**	\file ajax-classes-fields-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'une champ personnalisé dans une classe.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 *	- cls : identifiant de la classe à mettre à jour
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS_EDIT');

	if( !isset($_POST['source'], $_POST['target'], $_POST['action'], $_GET['cls']) ){
		throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	}
	
	require_once('fields.inc.php');
	
	$response = array(
		'success' => fld_classes_fields_position_update( $_POST['source'], $_POST['target'], $_POST['action'], $_GET['cls'] )
	);
	
	print json_encode($response);
	exit;

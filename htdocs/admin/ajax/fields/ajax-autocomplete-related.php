<?php
	/**	\file ajax-autocomplete-related.php
	 * 	Ce fichier est appelé pour renvoyer une liste d'objets (id et nom, utilisé pour la sélection multiple d'objets)
	 *	La classe des objets à retourner est fournie en paramètre ($_GET['cls']), par son identifiant
	 *	Renvoie soit les objets correspondants aux ids fournis en paramètre ($_GET['v']),
	 *	soit les objets dont le nom commence par une chaîne donnée en paramètre pour l'autocomplétion ($_GET['q'])
	 *	
	 *	@param int $_GET['cls'] Classe des objets (ex : CLS_CMS)
	 *	@param string $_GET['v'] Optionnel, liste des identifiants des objets à retourner séparés par des virgules
	 *	@param string $_GET['q'] Si $_GET['v'] n'est pas renseigné, chaîne utilisée pour l'autocomplétion
								Le nom des objets doit commencer par cette chaîne
	 *	@return string La liste des objets avec id et name au format json
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS_EDIT');
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER_INSCRIPT');
	
	$response = array();
	
	if( !isset($_GET['cls']) ) {
		throw new Exception('Paramètre cls manquant !');
	}
	if( !isset($_GET['q']) && !isset($_GET['v']) ) {
		throw new Exception('Paramètre q ou v manquant !');
	}
	
	// Transforme $_GET['v'] en tableau d'ids
	$v = false;
	if( isset($_GET['v']) ){
		$v = explode(',', $_GET['v']);
		foreach ($v as &$value) {
			$value = trim($value);
			if( !is_numeric($value) ) {
				$v = false;
				break;
			}
		}
		unset($value);
	}
	
	switch ($_GET['cls']) {
		case CLS_CMS : {	// Gestion de contenu
			$rcat = false;
			if( isset($_GET['q']) ){
				$rcat = cms_categories_get( 0, false, true, -1, false, false, true, null, $_GET['q'], null, false );
			}elseif( $v!==false ){
				$rcat = cms_categories_get( $v, false, false, -1, false, false, true, null, false, null, false );
			}
			if( $rcat ){
				while( $cat = ria_mysql_fetch_assoc($rcat) ){
					$response[] = $cat;
				}
			}
			break;
		}
		// case CLS_PRODUCT :{
			// $rprd = false;
				// $rprd = prd_products_get_simple();
			// if( $rprd ){
				// while( $cat = ria_mysql_fetch_assoc($rprd) ){
					// $response[] = $cat;
				// }
			// }
			// break;
		// }
		default : {
			throw new Exception('La classe ' . $_GET['cls'] . ' n\'est pas implémentée !');
		}
	}
	
	print json_encode($response);
	exit;
<?php
	/**	\file popup-order-duplication.php
     * 
	 *	Cette popup est utilisée dans le processus de duplication de commande,
     *	elle permet la sélection du compte client auquel la commande va être associée.
     *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_CREATE');
	
    $is_ajax = false;
    if (!isset($_GET['ord_id'])){
        $error = _('Il manque des paramètres');
    }
    if (!isset($_GET['user'])){
        $_GET['user'] = 0;
    }

    // Si requête ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Duplication de commande'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
        print '<div class="error">'.nl2br($error).'</div>';
        exit;
    } 
?>

<form method="post" action="popup-order-duplication.php">
    <?php 
        print '<div class="ord-duplicate-div" id="ord-duplicate-ref">';
            print '<label class="ord-duplicate-label" for="ord-ref-duplicate">'._('Référence :').'</label>';
            print '<input type="text" name="ord-ref" id="ord-ref-duplicate"'.(isset($_GET['ref']) && trim($_GET['ref']) ? 'value="'.$_GET['ref'].'"' : '' ).' />';
        print '</div>';

        print '<div class="ord-duplicate-div" id="ord-duplicate-user">';
            print '<label class="ord-duplicate-label" for="upd-ord-usr">'._('Compte client :').'</label>';
            if( $_GET['user'] ){
                $r_user = gu_users_get($_GET['user']);
                if ($r_user && ria_mysql_num_rows($r_user)){
                    $usr = ria_mysql_fetch_assoc($r_user);
                }
                
                if( !$r_user ){
                    $ref = str_pad( $_GET['user'], 8, '0', STR_PAD_LEFT );
                    print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars( $ref ).'</span>';
                }else{
                    if( !$usr['ref'] ){
                        $usr['ref'] = str_pad( $_GET['user'], 8, '0', STR_PAD_LEFT );
                    }
                    print '<a href="/admin/customers/edit.php?usr='.$_GET['user'].'" target="_blank">';
                    print view_usr_is_sync( $usr ).' '.htmlspecialchars( $usr['ref'] ).' '.htmlspecialchars( gu_users_get_name($_GET['user']) );
                    print '</a>';
                }
            }
            print '<input type="hidden" id="ord-user-duplicate" name="ord-user" value="'.$_GET['user'].'">';
            print ' <a href="#" id="upd-ord-usr">'.( $_GET['user'] ? _('Modifier') : _('Sélectionner un compte client') ).'</a>';
        print '</div>';

        print '<input type="button" id="ord-validate-duplication" name="ord-validate-duplication" value="'._('Dupliquer').'"/>';
    ?>
</form>

<?php 
    if( !$is_ajax ){
?>
    <script>
        var ord_id = <?php print $_GET['ord_id']; ?>;
        $(document).ready(function(){
            $('.page_timer').hide();

            $("#ord-validate-duplication").click(function(){
                var ref_input = $("#ord-ref-duplicate").val();
                var user_id = $("#ord-user-duplicate").val();
        
                window.parent.parent_order_duplicate(ord_id, ref_input, user_id);
                window.parent.hidePopup();

            });
        }).delegate( '#upd-ord-usr', 'click', function(e){
            e.preventDefault();

            var ref_input = $("#ord-ref-duplicate").val();

            window.parent.displayPopup( 'Sélectionner un compte client', '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&ord-duplicate=1&ord='+ord_id+'&ref_input='+ref_input, '', 916, 557 );
        });
    </script>

<?php
        require_once('admin/skin/footer.inc.php'); 
    }

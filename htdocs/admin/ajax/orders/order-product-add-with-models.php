<?php
    /** \file order-product-add-with-moddels.php
     *  Ce fichier permet de gérer l'ajout d'un panier modèle à une commande.
     *  Cet ajout peut être fait via l'identifiant du modèle (ajoutera tous les produits) ou bien via une restriction des produits d'un modèle.
     */
    
	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $product_add_admin = true;

    if ( !isset( $_POST['mdl_id'] ) || !is_numeric( $_POST['mdl_id'] ) || $_POST['mdl_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant du modèle de commande est invalide ou manquant.')));
    } elseif ( !isset( $_POST['ord_id'] ) || !is_numeric( $_POST['ord_id'] ) || $_POST['ord_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant.')));
    } elseif ( !isset( $_POST['prd_ids'] ) || $_POST['prd_ids'] == "" ){
         print json_encode(array('code' => '400', 'response' => _('Les identifiants de produits sont manquants.')));
    } elseif (isset($_POST['target']) && (!isset($_POST['target']['id']) || !is_numeric($_POST['target']['id']) || $_POST['target']['id'] < 0 || !isset($_POST['target']['line']) || !is_numeric($_POST['target']['line']) || $_POST['target']['line'] < 0) ){
        print json_encode( array('code' => '400', 'response' => _('Les informations sur la position à laquelle insérer le produit sont invalides ou manquantes') ) );
    } else {
        $order_id = $_POST['ord_id'];
        $model_id = $_POST['mdl_id'];
        if ($_POST['prd_ids'] != "'0'" && $_POST['prd_qtes'] != "'0'"){ //Si on a choisi des produits dans le modèle de commande
            $product_ids = explode(',', $_POST['prd_ids']);
            $product_qtes = explode(',', $_POST['prd_qtes']);
            $product_lines = explode(',', $_POST['prd_lines']);

            foreach($product_ids as $key => $id){
                $prd_id = intval( str_replace( "'" , "" , $id ) );
                $prd_qte = intval( str_replace( "'" , "" , $product_qtes[$key] ) );
                $prd_line = intval( str_replace( "'" , "" , $product_lines[$key] ) );

                $is_colisage = prd_colisage_classify_exists($prd_id);
                $colisage_id = false;
                if ($is_colisage && $colisage_id = fld_object_values_get(array($model_id, $prd_id, $prd_line), _FLD_PRD_COL_ORD_PRODUCT) ){
                    $r_colisage = prd_colisage_types_get(parseInt($colisage_id));
                    $colisage = ria_mysql_fetch_assoc($r_colisage);
                } else {
                    $is_colisage = false;
                }

                $line = ord_products_add($order_id, $prd_id, $prd_qte, '', true, null, $is_colisage ? $colisage_id : 0, false, 0, 0, false, false, true, true);
                if ($line !== false){
                    if (isset($_POST['target'])){
                        ord_products_position_update( $order_id, array('id' => $prd_id, 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
                    }
                } else {
                    $error = sprintf(_("Une erreur inatendue est survenue lors de l'insertion dans la commande du modèle n°%s."), $model_id)."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
                    break;
                }
            }
        } else { //Si on a choisi directement le modèle
            $r_products = ord_products_get( $model_id );
            if (!$r_products || !ria_mysql_num_rows($r_products)){
                $error = sprintf(_('Une erreur est survenue lors de la récupération des produits du modèle n°%s.'), $model_id).'<br />'._('Veuillez réessayer ou nous contacter pour signaler l\'erreur.');
            } else {
                while($product = ria_mysql_fetch_assoc($r_products)) {
                    $is_colisage = prd_colisage_classify_exists($product['id']);
                    $colisage_id = false;
                    if ($is_colisage && $colisage_id = fld_object_values_get(array($model_id, $product['id'], $product['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
                        $r_colisage = prd_colisage_types_get(parseInt($colisage_id));
                        $colisage = ria_mysql_fetch_assoc($r_colisage);
                    } else {
                        $is_colisage = false;
                    }


                    $line = ord_products_add($order_id, $product['id'], $product['qte'], '', true, null, $is_colisage ? $colisage_id : 0, false, 0, 0, false, false, true, true);
                    if ($line !== false){
                        if (isset($_POST['target'])){
                            ord_products_position_update( $order_id, array('id' => $product['id'], 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
                        }
                    } else {
                        $error = sprintf(_("Une erreur inatendue est survenue lors de l'insertion dans la commande du modèle n°%s."), $model_id)."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
                        exit;
                    }
                }
            }
        }
        if (isset($error)){
            print json_encode(array('code' => '400', 'response' => $error));
        } else {
            print json_encode(array('code' => '100', 'response' => _('L\'ajout du modèle de commande s\'est correctement déroulé.')));
        }
    }


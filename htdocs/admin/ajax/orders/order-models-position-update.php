<?php

	/**	\file ajax-models-position-update.php
	 *	Ce fichier permet la mise à jour de la position des modèles de commandes.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source, sous la forme "id-produit,id-ligne"
	 *	- target : identifiant de l'objet cible, sous la forme "id-produit,id-ligne"
	 *	- action : soit "before" soit "after"
	 *	- ord : identifiant de la commande à mettre à jour
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL_EDIT');

if( !isset($_POST['source'], $_POST['target'], $_POST['action']) ){
	error_log(__FILE__.':'.__LINE__.' error post models position update');
	return false;
}

print json_encode(array(
	'success' => obj_position_update(DD_MODEL, $_POST['source'], $_POST['target'], $_POST['action']),
));

exit;
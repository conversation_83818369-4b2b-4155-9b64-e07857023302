<?php 
    $is_ajax = false;
    if (!isset($_GET['mdl_id']) || !is_numeric($_GET['mdl_id']) || $_GET['mdl_id'] < 0){
        print '<div class="error">Il manque des paramètres</div>';
        exit;
    }

    if ($_GET['mdl_id'] == 0){
        $r_models = ord_orders_get_with_adresses( 0, 0, _STATE_MODEL );
    } else {
        $r_model = ord_orders_get_with_adresses( 0, $_GET['mdl_id'], _STATE_MODEL );
        $model = ria_mysql_fetch_assoc($r_model);
        if (!$model){
            print '<div class="error">'.sprintf(_('Une erreur est survenue lors de la récupération du modèle n°%s.'), $_GET['mdl_id']).'\n'._('Veuillez réessayer ou nous contacter pour signaler l\'erreur.').'</div>';
            exit;
        }

        $r_products = ord_products_get( $_GET['mdl_id'] );
    }

    if (isset($r_models) && !$r_models){
        print '<div class="error">'._('Une erreur est survenue lors de la récupération des modèles de commande.').'\n'._('Veuillez réessayer ou nous contacter pour signaler l\'erreur.').'</div>';
        exit;
    } 
    if (isset($r_products) && !$r_products){
        print '<div class="error">'.sprintf(_('Une erreur est survenue lors de la récupération des produits du modèle n°%s.'), $_GET['mdl_id']).'\n'._('Veuillez réessayer ou nous contacter pour signaler l\'erreur.').'</div>';
        exit;
    }

    //si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Sélection du modèle de commandes'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }

    if($_GET['mdl_id'] != 0){
?>
<form method="post" action="#">
    <table id="order-prd-models" class="list checklist" style="margin-bottom: 10px;">
        <caption>
            <a name="return-menu" href="popup-order-models-selector.php?mdl_id=0">
                <img width="16" height="16" title="<?php print _('Remonter d\'un niveau'); ?>" alt="<?php print _('Remonter d\'un niveau'); ?>" src="/admin/images/up.png">
            </a>
            <?php printf(_('Liste des produits du modèle %s'), $model['ref'])?>
        </caption>
        <col width="20" /><col width="150" /><col width="*" /><col width="100" />
        <thead>
            <tr>
                <th id="prd-sel"><input type="checkbox" onclick="checkAllClick(this)" class="checkbox"/></th>
                <th id="prd-tref"><?php print _('Référence')?></th>
                <th id="prd-dsn"><?php print _('Désignation')?></th>
                <th id="prd-qte"><?php print _('Quantité')?></th>
            </tr>
        </thead>
        <tbody>
        <?php

            // Affichage des commandes
            if( !ria_mysql_num_rows($r_products) )
                print '<tr><td colspan="6">'._('Aucun produits.').'</td></tr>';
            else{
                $current = 0;
                $count = ria_mysql_num_rows($r_products);
                while( $r = ria_mysql_fetch_assoc($r_products) ){
                    if( $r!=null ){
                        $is_colisage = prd_colisage_classify_exists($r['id']);
                        $colisage_id = false;
                        if ($is_colisage && $colisage_id = fld_object_values_get(array($_GET['mdl_id'], $r['id'], $r['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
                            $r_colisage = prd_colisage_types_get(parseInt($colisage_id));
                            $colisage = ria_mysql_fetch_assoc($r_colisage);
                        } else {
                            $is_colisage = false;
                        }                            
                        print '<tr id="line-' . $r['id'] . '">';
                        print '<td headers="prd-select"><input type="checkbox" name="select_prd[]" value="'.$r['id'].'" /></td>';
                        print '<td headers="prd-tref" align="center">'.$r['ref'].'</td>';
                        print '<td headers="prd-dsn">'.$r['name'].' '.($is_colisage ? ' - '.$colisage['name'].' ('.parseInt($colisage['qte']).')' : '').'</td>';
                        print '<td headers="prd-qte" align="center">'.$r['qte'].'</td>';
                        print '<input type="hidden" id="prd-qte" value="'.$r['qte'].'" />';
                        print '<input type="hidden" id="prd-line" value="'.$r['line'].'" />';
                        print '</tr>';
                    }
                    $current++;
                }
            }
        ?>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="4"><input type="button" id="save-prd-choice" name="save-prd-choice" value="<?php print _('Ajouter au panier'); ?>" /></td>
            </tr>
        </tfoot>
    </table>

</form>


    <?php } else { ?>
        <table id="order-models" class="list checklist" width="100%">
	<caption><?php print _('Liste des modèles')?></caption>
	<col width="100" /><col width="100" /><col width="*" /><col width="90" /><col width="130" />
<thead>
	<tr>
		<th id="ord-id"><?php print _('Numéro')?></th>
		<th id="ord-tref"><?php print _('Référence')?></th>
		<th id="ord-comment"><?php print _('Commentaires')?></th>
        <th id="ord-products"><?php print _('Produits')?></th>
        <th id="ord-select"></th>
	</tr>
</thead>
<tbody>
<?php

	// Affichage des commandes
	if( !ria_mysql_num_rows($r_models) )
		print '<tr><td colspan="6">'._('Aucun modèle.').'</td></tr>';
	else{
		$current = 0;
		$count = ria_mysql_num_rows($r_models);
		while( $r = ria_mysql_fetch_assoc($r_models) ){
			if( $r!=null ){
                print '<tr id="line-' . $r['id'] . '" align="center">';
                print '<input type="hidden" id="mdl-id-'.$r['id'].'" value="'.$r['id'].'" />';
				print '<td headers="ord-id"><a href="popup-order-models-selector.php?mdl_id='.$r['id'].'" title="'._('Afficher la fiche de ce modèle').'">'.ord_orders_name('',$r['piece'],$r['id']).'</a></td>';
				print '<td headers="ord-tref">'.$r['ref'].'</td>';
				print '<td headers="ord-comment">'.$r['comments'].'</td>';
                print '<td headers="ord-products">'.$r['products'].'</td>';
                print '<td headers="ord-select-all"><a id="mdl-add" name="mdl-add">'._('Ajouter ce modèle').'</a></td>';
				print '</tr>';
			}
			$current++;
		}
	}
?>
</tbody>
</table>
    <?php } ?>


<script><!--
    $(document).ready(function(){
        var model_id = <?php print $_GET['mdl_id']; ?>;

        $("#save-prd-choice").click(function(){
            var prd_ids = '';
            var prd_qtes = '';
            var prd_lines = '';
            var count = 0;
            $("tbody input").each(function(){
                if ($(this).is(":checked")){
                    if (count > 0){
                        prd_ids += ',';
                        prd_qtes += ',';
                        prd_lines += ',';
                    }
                    prd_ids += $(this).val(); //valeur de l'input contenant l'id du produit
                    prd_qtes += $(this).parents('tr#line-'+$(this).val()).find('#prd-qte').val(); //Valeur de l'input contenant la quantité de produits
                    prd_lines += $(this).parents('tr#line-'+$(this).val()).find('#prd-line').val(); //Valeur de l'input contenant la quantité de produits
                    count++;
                }
            });

            if (prd_ids == "" || prd_qtes == "" || prd_lines == ""){
                alert('<?php print _('Veuillez sélectionner au moins un produit, ou alors fermez la popup.'); ?>');
                return false;
            }

            window.parent.parent_select_products_by_model(model_id, prd_ids, prd_qtes, prd_lines);

            window.parent.hidePopup();
        });
        $("#mdl-add").click(function(){
            model_id = $("#mdl-add").parents('tr').find('input').val();

            window.parent.parent_select_products_by_model(model_id, 0, 0, 0);

            window.parent.hidePopup();
        });
    });
--></script>


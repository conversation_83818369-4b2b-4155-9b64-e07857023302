<?php

	/**	\file ncmd-customers-change.php
	 *	Cette page est utilisée en popup et permet la sélection d'un compte client pour association à une pièce de vente
	 *	ou un rapport de visite.
	 *	Elle permet également la création d'un compte client.
	 *	Si on ne souhaite pas la création de compte depuis cette popup, il faut passer le paramètre no-add en paramètre.
	 */

	require_once('profiles.inc.php');
	$close = false; 
	
	// Page appelante
	if( !isset($_GET['callback']) ){
		$_GET['callback'] = 'parent_select_user';
	}
	
	if( !isset($_GET['get-all-name']) ){
		$_GET['get-all-name'] = false;
	}
	
	if( !isset($_GET['prf']) ){
		$_GET['prf'] = 0;
	}

	$prf = control_array_integer( $_GET['prf'], false );

	// Nom des champs input qui recevront les valeurs en retour
	$hidden = '';
	$url_other_args = '?customers=1';
	if( is_array($prf) && sizeof($prf) ){
		foreach( $prf as $one_prf ){
			$url_other_args .= '&prf[]='.$one_prf;
		}
	}
	
	// Détermine s'il est possible d'ajouter un compte client depuis cette page ou non.
	if( isset($_GET['no-add']) && $_GET['no-add'] ){
		$url_other_args .= '&no-add=1';
		$hidden .= '<input type="hidden" name="no-add" value="1" />';
	}

	if( isset($_GET['input_id_usr_id']) && $_GET['input_id_usr_id']!='' ){
		$url_other_args .= '&input_id_usr_id='.urlencode($_GET['input_id_usr_id']);
		$hidden .= '<input type="hidden" name="input_id_usr_id" value="'.$_GET['input_id_usr_id'].'" />';
	}

	if( isset($_GET['input_id_usr_email']) && $_GET['input_id_usr_email']!='' ){
		$url_other_args .= '&input_id_usr_email='.urlencode($_GET['input_id_usr_email']);
		$hidden .= '<input type="hidden" name="input_id_usr_email" value="'.$_GET['input_id_usr_email'].'" />';
	}

	// Termes de recherche
	if( isset($_GET['q']) ){
		$hidden .= '<input type="hidden" name="get_q" id="get_q" value="'.$_GET['q'].'" />';	
	}

	if( isset($_GET['ncmd-rights']) ){
		$hidden .= '<input type="hidden" name="ncmd-rights" id="ncmd-rights" value="'.$_GET['ncmd-rights'].'" />';	
	}

	if( isset($_GET['upd-ord-usr']) ){
		$hidden .= '<input type="hidden" name="upd-ord-usr" id="upd-ord-usr" value="'.$_GET['upd-ord-usr'].'" />';
		$hidden .= '<input type="hidden" name="ord" id="ord" value="'.$_GET['ord'].'" />';
	}

	define('ADMIN_PAGE_TITLE', _('Sélection de client'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<div class="tabscontent">
	<?php 
	if( isset($error) && $error ){
		print '<div class="error">'.htmlspecialchars( $error ).'</div>';
	}
	?>
			<form action="/admin/ajax/orders/ncmd-customers-change.php" method="get">
				<?php print $hidden; ?>
				<div>
					<label for="autoload"><?php print _('Rechercher un utilisateur :'); ?></label>
					<input id="autoload" type="text" name="q" value="<?php print isset($_GET['q']) ? $_GET['q'] : ''; ?>"/>
				</div>
				<br/>
				<table class="search-customers-results">
					<caption><?php print _('Résultats'); ?></caption>
					<thead>
						<tr>
							<th><abbr title="<?php print _('Référence'); ?>"><?php print _('Réf.'); ?></abbr></th>
							<th><?php print _('Nom'); ?></th>
							<th><?php print _('Email'); ?></th>
							<th></th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td colspan="4"><?php print _('Aucun résultat'); ?></td>
						</tr>
					</tbody>					
					<?php if( !isset($_GET['no-add']) || !$_GET['no-add'] ){ ?>
					<tfoot>
						<tr>
							<td colspan="4">
								<input type="button" name="new-usr" id="new-usr" title="<?php print _('Ajouter un client'); ?>" value="<?php print _('Ajouter un client'); ?>" />
							</td>
						</tr>
					</tfoot>
					<?php } ?>
				</table>
			</form>
	</div>
	<script>
		$('document').ready(function(){

			if( typeof $('#get_q') != 'undefined' && $('#get_q').length ){
				table = $('.search-customers-results tbody');
				$('tr',table).remove();
				$(table).append('<tr><td colspan="4"><?php print _('Recherche en cours'); ?></td></tr>');
				
				if( current_ajax_request ) current_ajax_request.abort();
				var form = $(this).parents('form'); 
				
				current_ajax_request = $.ajax({
					url : '/admin/ajax/orders/ncmd-customers-search.php<?php print $url_other_args; ?>&q=' + $('#get_q').val(), 
					data : form.serialize(), 
					success:function(html){
						$('tr',table).remove();
						$(table).append(html);
						customers_edit_init();
					}
				});
			}
			
			// Affiche le compte client qui a été créé
			<?php if( isset($_GET['usr']) ){ ?>
				table = $('.search-customers-results tbody');
				$('tr',table).remove();
				$(table).append('<tr><td colspan="4"><?php print _('Recherche en cours'); ?></td></tr>');
				
				if( current_ajax_request ) current_ajax_request.abort();
				var form = $(this).parents('form'); 
				
				current_ajax_request = $.ajax({
					url : '/admin/ajax/orders/ncmd-customers-search.php<?php print $url_other_args; ?>&usr=<?php print $_GET['usr']; ?>', 
					data : form.serialize(), 
					success:function(html){
						$('tr',table).remove();
						$(table).append(html);
						customers_edit_init();
					}
				});
			<?php }
			if( $close ){ ?>
				window.parent.parent_refresh();
				window.parent.hidePopup();
			<?php } ?>

					
			var current_ajax_request = false;

			var delay = (function(delay){
				var out = delay;
				return function(callback, delay){
					clearTimeout(out);
					out = setTimeout(callback, delay);
				}
			})(null);

			// Code permettant de bloquer la touche 'enter' pour éviter le submit du formulaire qui recharge la popup de recherche de client
			// Ce blocage evite de perdre des informations sur la popup pour la recherche (ex : le parent de la popup)
			$('#autoload').keydown(function(event){
			    if(event.keyCode == 13) { // ketcode 13 => toucher 'enter'
			      event.preventDefault();
			      return false;
			    }
			  });


			$('#autoload').on('keyup',function(){
				delay(function(){
					table = $('.search-customers-results tbody');
					$('tr',table).remove();
					$(table).append('<tr><td colspan="4"><?php print _('Recherche en cours'); ?></td></tr>');
					
					if( current_ajax_request ) current_ajax_request.abort();
					var form = $('#autoload').parents('form'); 
					
					current_ajax_request = $.ajax({
						url : '/admin/ajax/orders/ncmd-customers-search.php<?php print $url_other_args; ?>', 
						data : form.serialize(), 
						success:function(html){
							$('tr',table).remove();
							$(table).append(html);
							customers_edit_init();
						}
					});
				},600)
			});
			
			function customers_edit_init(){
				$('.search-customers-results tr').hover(function(){
					$('td', this ).addClass('odd');
					$('td', this ).css('cursor','pointer');
				}, function(){
					$('td', this ).removeClass('odd');
					$('td', this ).css('cursor','none');
				});			
			}
			
			$('.search-customers-results tr').live('click',function(){
				var callBack = '<?php print $_GET['callback']; ?>';
				var allname = <?php print $_GET['get-all-name'] ? 'true' : 'callBack === \'updateUsers\''; ?>;

				if( $(this).attr('data-id') > 0 ){
					<?php
						$return_url_args = '';
						if( isset($_GET['input_id_usr_id']) && $_GET['input_id_usr_id']!='' ){
							$return_url_args .= ', "'.$_GET['input_id_usr_id'].'"';
						}else{
							$return_url_args .= ', ""';
						}
						if( isset($_GET['input_id_usr_email']) && $_GET['input_id_usr_email']!='' ){
							$return_url_args .= ', "'.$_GET['input_id_usr_email'].'"';
						}else{
							$return_url_args .= ', ""';
						}
					?>

					if ($('#ncmd-rights').length) {
						window.location.href = "/admin/ajax/orders/ncmd-rights.php?choose-usr=" + $(this).attr('data-id');
						return false;
					}

					<?php if( isset($_GET['upd-ord-usr']) ){  ?>
						window.parent.update_order_user($(this).attr('data-id'), $('#ord').val());
						window.parent.hidePopup();
					<?php }elseif( isset($_GET['ord-duplicate']) ){  ?>
						window.parent.ord_duplicate_user($(this).attr('data-id'), <?php print $_GET['ord'] ?>, '<?php print addslashes($_GET['ref_input']); ?>');
						window.parent.hidePopup();
					<?php }else{ ?>
						if( allname ){
							window.parent[callBack]( $(this).attr('data-id'), $(this).attr('data-email'), $(this).attr('data-allname')<?php print $return_url_args; ?> );
						}else if( callBack=='parent_select_user' ){
							window.parent[callBack]( $(this).attr('data-id'), $(this).attr('data-email') <?php print $return_url_args; ?> );
						}else{
							window.parent[callBack]( $(this).attr('data-id'), $(this).attr('data-email'), $(this).attr('data-name')<?php print $return_url_args; ?> );
						}
					<?php } ?>

					window.parent.hidePopup();
				}
			});

			$('#new-usr').click(function(){
				<?php
					$url_add_base = '/admin/customers/new.php?popup=1';
					if( isset($_GET['no-add']) && $_GET['no-add'] ){
						$url_add_base .= '&amp;no-add=1';
					}

					if($_GET['callback'] == 'getUserReport'){
						$url_add_base .= '&callback=getUserReport';
					}
				?>
				<?php if( isset($_GET['upd-ord-usr']) ){  ?>
					window.location.href = "<?php print $url_add_base; ?>&upd-ord-usr=1&ord=<?php print $_GET['ord']; ?>";
				<?php }elseif( isset($_GET['ord-duplicate']) ){  ?>
					window.location.href = "<?php print $url_add_base; ?>&ord-duplicate=1&ord=<?php print $_GET['ord']; ?>&ref_input=<?php print $_GET['ref_input'] ?>";
				<?php }else{ ?>
					window.location.href = "<?php print $url_add_base; ?>";
				<?php } ?>
			})

		});
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
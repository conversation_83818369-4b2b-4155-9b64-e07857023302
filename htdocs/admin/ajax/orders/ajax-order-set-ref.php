<?php

    /** \file ajax-order-set-ref.php
     *  Ce fichier permet la mise à jour en Ajax de la référence de commande. Les paramètres sont les suivants :
     *  - ord_id : obligatoire, identifiant de la commande à mettre à jour
     *  - new_ref : obligatoire, nouvelle référence de commande
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    if(!isset($_GET['ord_id'], $_GET['new_ref'])){
        print json_encode(array('code' => '400', 'message' => _('Il manque des paramètres')));
    } else {
        if (!ord_orders_ref_update($_GET['ord_id'], $_GET['new_ref'])){
            print json_encode(array('code' => '400', 'message' => _('Une erreur est survenue lors de l\'assignation de la référence à la commande.')."\n"._('Veuillez réessayer ou prendre contact pour nous signaler l\'erreur')));
        } else {
            print json_encode(array('code' => '100', 'message' => _('Ajout de la référence de la commande réussi')));
        }
    }


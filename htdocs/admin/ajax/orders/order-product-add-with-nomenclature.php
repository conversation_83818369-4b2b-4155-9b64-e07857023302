<?php 
    /** \file order-product-add-with-nomenclature.php
     *  Ce fichier gère l'ajout d'une nomenclature à une commande lors de l'édition d'une commande.
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $product_edit_admin = true;

    if ( !isset( $_POST['prd_id'] ) || !is_numeric( $_POST['prd_id'] ) || $_POST['prd_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant du produit est invalide ou manquant.')));
    } elseif ( !isset( $_POST['ord_id'] ) || !is_numeric( $_POST['ord_id'] ) || $_POST['ord_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant.')));
    } elseif ( !isset( $_POST['childs_ids'] ) || $_POST['childs_ids'] == "" ){
         print json_encode(array('code' => '400', 'response' => _('Les identifiants de nomenclature sont manquants.')));
    } elseif ( !isset( $_POST['nomenclature_ids'] ) || $_POST['nomenclature_ids'] == "" ){
        print json_encode(array('code' => '400', 'response' => _('Les identifiants de nomenclature sont manquants.')));
    } elseif (isset($_POST['target']) && (!isset($_POST['target']['id']) || !is_numeric($_POST['target']['id']) || $_POST['target']['id'] < 0 || !isset($_POST['target']['line']) || !is_numeric($_POST['target']['line']) || $_POST['target']['line'] < 0) ){
        print json_encode( array('code' => '400', 'response' => _('Les informations sur la position à laquelle insérer le produit sont invalides ou manquantes') ) );
    } else {
        $order_id = $_POST['ord_id'];
        if (!isset($error)){
            $childs_ids = explode(',', $_POST['childs_ids']);
            $nomenclature_ids = explode(',', $_POST['nomenclature_ids']);

            $nomenclatures = array();
            foreach($nomenclature_ids as $key => $id){
                $nom_id = intval( str_replace( "'" , "" , $id ) );
                $child_id = intval( str_replace( "'" , "" , $childs_ids[$key] ) );
                $nomenclatures[$nom_id] = $child_id;
            }

            if (sizeof($nomenclatures)){
                $line = ord_products_add($order_id, $_POST['prd_id'], 1 , '', true, $nomenclatures, 0, false, 0, 0, false, false, true, true);
                if ($line !== false){
                    if (isset($_POST['target'])){
                        ord_products_position_update( $order_id, array('id' => $_POST['prd_id'], 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
                    }
                } else {
                    $error = _("Une erreur inatendue est survenue lors de l'insertion dans la commande du produit et de ses nomenclatures.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
                }
            } else {
                $error = _("Une erreur inatendue est survenue lors de la récupération des nomenclatures.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
            }
        }
        if (isset($error)){
            print json_encode(array('code' => '400', 'response' => $error));
        } else {
            print json_encode(array('code' => '100', 'response' => _('L\'ajout du produit et de ses nomenclatures s\'est correctement déroulé.')));
        }
    }


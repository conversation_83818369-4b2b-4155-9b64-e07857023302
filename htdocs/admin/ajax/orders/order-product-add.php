<?php

	/**	\file order-product-add.php
	 *	Ce fichier permet l'ajout d'un produit simple (hors colisage et nomenclature) à une commande existante, en cours de modification.
	 *	Les arguments suivants sont attendus :
	 *	- prd_id : identifiant du produit à ajouter
	 *	- ord_id : identifiant de la commande à modifier
	 *  - target : facultatif, tableau associatif indiquant la position à laquelle le produit doit être ajouté.
	 *		- id : 
	 *		- line : 
	 *	Elle fournit sa réponse au format json. Sa réponse est composée comme suit :
	 *	- code :
	 *		- 100 : tout s'est bien déroulé
	 *		- 200 : tout s'est bien déroulé (produit nomenclaturé ou colisé)
	 *		- 400 : une erreur s'est produite.
	 *	- response : message d'erreur ou de succès
	 *	- data : optionnel, uniquement dans le cas d'un article nomenclaturé ou colisé
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

	// Identifie que l'ajout du produit se fait depuis l'administration
	// La variable global est par la suite utilisée dans le moteur
	$product_edit_admin = true;
	$GLOBALS['product_add_admin'] = true;

	// Le paramètre prd_id est obligatoire. Identifiant du produit à ajouter à la pièce de vente.
	if( !isset($_POST['prd_id']) || !is_numeric($_POST['prd_id']) || $_POST['prd_id']<0 ){
		print json_encode(array('code' => '400', 'response' => _('L\'identifiant du produit est invalide ou manquant.')));
	// Le paramètre ord_id est obligatoire. Identifiant de la pièce de vente à modifier.
	}elseif( !isset($_POST['ord_id']) || !is_numeric($_POST['ord_id']) || $_POST['ord_id']<=0 ){
		print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant.')));
	// Les informations sur la position de création de la ligne sont facultatives. Si elles sont fournies, elles sont vérifiées ici.
	} elseif (isset($_POST['target']) && (!isset($_POST['target']['id']) || !is_numeric($_POST['target']['id']) || $_POST['target']['id'] < 0 || !isset($_POST['target']['line']) || !is_numeric($_POST['target']['line']) || $_POST['target']['line'] < 0) ){
		print json_encode( array('code' => '400', 'response' => _('Les informations sur la position à laquelle insérer le produit sont invalides ou manquantes.') ) );
	} else {
		$is_nomenclature = prd_nomenclatures_options_exists($_POST['prd_id']);
		$colisage_exists = prd_colisage_classify_exists($_POST['prd_id']);

		if( $_POST['prd_id']==="0" ){ // Ajout d'un interligne
			$line = ord_products_add_spacing( $_POST['ord_id'], true );
			if ($line !== false){
				if (isset($_POST['target'])){
					ord_products_position_update( $_POST['ord_id'], array('id' => 0, 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
				}
				print json_encode( array('code' => '100', 'response' => _('L\'ajout de l\'interligne dans la commande s\'est correctement déroulé.') ) );
			} else {
				print json_encode( array('code' => '400', 'response' => _('Une erreur est survenue lors de l\'ajout de l\'interligne dans la commande.')."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.") ) );
			}
		} elseif( !$is_nomenclature && !$colisage_exists ){ // Ajout d'un article qui n'est ni nomenclaturé ni colisé
			
			// Ajout du produit demandé à la pièce de vente
			$line = ord_products_add( $_POST['ord_id'], $_POST['prd_id'], 1, '', false, null, 0, false, 0, 0, false, false, true, true, false, true );

			if( $line!==false ){
				/**
				 * Applique un champ avancé "_FLD_PRD_ORD_FREE" à "Oui".
				 */
				fld_object_values_set(array($_POST['ord_id'], $_POST['prd_id'], $line), _FLD_PRD_ORD_FREE, 'Oui');

				// Met à jour la position de la ligne dans la pièce de vente, si celle-ci est fournie
				if( isset($_POST['target']) ){
					ord_products_position_update( $_POST['ord_id'], array('id' => $_POST['prd_id'], 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
				}

				print json_encode( array('code' => '100', 'response' => _('L\'ajout du produit dans la commande s\'est correctement déroulé.') ) );
			} else {
				print json_encode( array('code' => '400', 'response' => _('Une erreur est survenue lors de l\'ajout du produit dans la commande.')."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.") ) );
			}
		} else { // Ajout d'un article colisé ou nomenclaturé
			print json_encode(
				array(
					'code' => '200', 
					'response' => array(
						'nomenclature' => $is_nomenclature ? '1' : '0',
						'colisage' => $colisage_exists ? '1' : '0'
					), 
					'data' => !isset($_POST['target']) ? array() : array(
						'target' => array(
							'id' => $_POST['target']['id'],
							'line' => $_POST['target']['line']
						)
					)
				)
			);
		}
	}


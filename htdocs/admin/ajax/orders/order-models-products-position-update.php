<?php

	/**	\file ajax-models-products-position-update.php
	 *	Ce fichier permet la mise à jour de la position des produits dans un modèle de commande.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source, sous la forme "id-produit,id-ligne"
	 *	- target : identifiant de l'objet cible, sous la forme "id-produit,id-ligne"
	 *	- action : soit "before" soit "after"
	 *	- ord : identifiant de la commande à mettre à jour
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL_EDIT');

	if( !isset($_POST['source'], $_POST['target'], $_POST['action']) ){
		error_log( __FILE__.':'.__LINE__.' error post models position update' );
		return false;
	}
	
	$d = explode('-', $_POST['source']);
	$res = obj_position_update( DD_MODEL_PRODUCTS, $_POST['source'], $_POST['target'], $_POST['action'], $d[0] );

	if( $res ){
		$ord_id = isset($_GET['ord']) && is_numeric($_GET['ord']) && $_GET['ord'] == $d[0] ? $_GET['ord'] : $d[0];
		
		$source = array('id' => $d[1], 'line' => $d[2]);
		$target = explode('-', $_POST['target']);
		$target = array('id' => $target[1], 'line' => $target[2]);
		
		$action = $_POST['action'];

		require_once 'orders.inc.php';

		$res = ord_products_position_update($ord_id, $source, $target, $action);
	}
	
	$response = array( 'success' => $res );
	

	print json_encode( $response );
	exit;

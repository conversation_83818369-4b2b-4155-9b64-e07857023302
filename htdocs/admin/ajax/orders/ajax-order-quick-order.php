<?php

    /** \ajax-order-quick-order.php
     *  Ce fichier gère les operations liees à la commande rapide : verification de l'existence des references fournies et ajout en masse au panier:
     *  Les paramètres sont les suivants :
     *  - ord_id : obligatoire, identifiant de la commande à dupliquer (pour toutes les actions)
     *  - action : obligatoire, l'action àréaliser (recherche ou ajout)
     *  - references : obligatoire, listes des références à rechercher ou ajouter
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $result = false;
    if (!isset( $_POST['ord_id'] ) || !is_numeric( $_POST['ord_id'] ) || $_POST['ord_id'] <= 0) {
        $msg = _('L\'identifiant de la commande est invalide ou manquant.');
    } else {
        $order_id = $_POST['ord_id'];
        $action = $_POST['action'];
        switch ($action) {
            case 'search' : // Recherche des produits à partir de leur référence;
                if (!isset($_POST['order_data'])) {
                    $msg = _('Recherche impossible. Aucune référence fournie.');
                } else {
                    $order_data = $_POST['order_data'];
                    $not_found = [];
                    foreach ($order_data as  $k => $od) {
                        $rprord = prd_products_get_simple( 0, $od['reference'], false );
                        $product = ria_mysql_fetch_array($rprord);
                        if ($product === false) { // Reference non trouvee
                            $not_found[] = $od['reference'];
                            unset($order_data[$k]);
                        } else {
                            $order_data[$k]['id_product'] = (int)$product['id'];
                        }
                    }
                    $result  = true;
                    $data = [
                        'not_found' => $not_found,
                        'order_data' => $order_data
                    ];
                }
                break;
            case 'add' : // Ajout des produits au panier
                $order_data = $_POST['order_data'];
                if (empty($order_data) || !$order_id) {
                    $msg = 'Impossible d\'ajouter les produits à la commande';
                } else {
                    function ord_products_add_quick_order($order_id, $id_product, $qty) {
                        return ord_products_add($order_id, $id_product, $qty);
                    }
                    foreach ($order_data as $od) {
                        $qty = (isset($od['quantity']) && (int)$od['quantity'] > 0) ? (int)$od['quantity'] : 1;
                        if (!$od['id_product'] || (ord_products_add_quick_order($order_id, $od['id_product'], $qty) === false)) {
                            $msg = sprintf('Echec lors de l\'ajout de la reference "%s". Veuillez recharger cette page.', $od['reference']);
                            break;
                        }
                    }
                    ord_orders_refresh($order_id);
                    $result = true;
                    $msg = 'Produits ajoutés à la commande. Veuillez patienter. Cette page va être rechargée.';
                }
                break;
            default :
                $msg = _('Action non reconnue.');
                break;
        }
    }

    // Retour
    print json_encode(
        array(
            'result' => $result,
            'msg' => isset($msg) ? $msg : null,
            'data' => isset($data) ? $data : null,
        )
    );

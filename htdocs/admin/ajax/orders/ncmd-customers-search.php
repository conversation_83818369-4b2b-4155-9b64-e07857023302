<?php

	/**	\file ncmd-customers-search.php
	 * 	Ce fichier est destiné à être appelé en Ajax pour fournir une liste d'utilisateurs correspondants à une recherche.
	 *  Cette recherche intervient dans le processus de création/mise à jour de commande pour modifier le compte client.
	 *  auquel la commande est rattachée. Il est appelé uniquement depuis ncmd-customers-search.php.
	 * 	Cette recherche intervient aussi lors de la sélection de compte client pouvant profiter d'une promotion.
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page et qu'il possède est des droits nécessaires à cela
	gu_if_authorized_else_403(array('_RGH_ADMIN_ORDER_CREATE', '_RGH_ADMIN_ORDER_EDIT', '_RGH_ADMIN_PROMO_SOLDE_ADD', '_RGH_ADMIN_PROMO_REWARD_ADD', '_RGH_ADMIN_PROMO_PRD_ADD'), false);

	if (!isset($_GET['prf'])) {
		$_GET['prf'] = 0;
	}

	$result_content = '';

	if (isset($_GET['q'])) {
		$rsearch = search3(1, $_GET['q'], 1, 50, false, false, 6, array('usr'));

		if ($rsearch && ria_mysql_num_rows($rsearch)) {
			while ($search = ria_mysql_fetch_array($rsearch)) {
				$rusr = gu_users_get($search['tag'], '', '', $_GET['prf']);

				if ($rusr && ria_mysql_num_rows($rusr)) {
					$usr = ria_mysql_fetch_array($rusr);

					$name = implode(', ', array_filter(array(
						trim(htmlspecialchars($usr['society'])),
						trim(htmlspecialchars($usr['adr_lastname'])),
						trim(htmlspecialchars($usr['adr_firstname']))
					)));

					$allname = view_usr_is_sync($usr) . ' ' . trim(htmlspecialchars($usr['adr_firstname'] . ' ' . $usr['adr_lastname'] . ' ' . $usr['society']));

					$result_content.= '<tr data-id="' . $usr['id'] . '" data-email="' . htmlspecialchars($usr['email']) . '" data-name="' . htmlspecialchars($name) . '" data-allname="' . htmlspecialchars($allname) . '">
							<td>' . htmlspecialchars($usr['ref']) . '</td>
							<td>' . htmlspecialchars($name) . '</td>
							<td>' . htmlspecialchars($usr['email']) . '</td>
							<td><a href="#" class="button">' . _('Sélectionner') . '</a></td>
						</tr>
					';
				}
			}
		}
	}

	if (isset($_GET['usr'])) {
		$rusr = gu_users_get($_GET['usr']);

		if ($rusr && ria_mysql_num_rows($rusr)) {
			$usr = ria_mysql_fetch_array($rusr);

			$name = implode(', ', array_filter(array(
				trim(htmlspecialchars($usr['society'])),
				trim(htmlspecialchars($usr['adr_lastname'])),
				trim(htmlspecialchars($usr['adr_firstname']))
			)));

			$allname = view_usr_is_sync($usr) . ' ' . trim(htmlspecialchars($usr['adr_firstname'] . ' ' . $usr['adr_lastname'] . ' ' . $usr['society']));
			
			$result_content.= '
				<tr data-id="' . $usr['id'] . '" data-email="' . htmlspecialchars($usr['email']) . '" data-name="' . htmlspecialchars($name) . '" data-allname="' . htmlspecialchars($allname) . '">
					<td>' . htmlspecialchars($usr['ref']) . '</td>
					<td>' . htmlspecialchars($name) . '</td>
					<td>' . htmlspecialchars($usr['email']) . '</td>
					<td><a href="#">' . _('Sélectionner') . '</a></td>
				</tr>
			';
		}
	}

	// Affiche un message d'erreur si aucun résultat
	if (!$result_content) {
		$result_content = '<tr><td colspan="4">'._('Aucun résultat').'</td></tr>';
	}

	// Si requete Ajax on ne va pas plus loin
	if (IS_AJAX) {
		print $result_content;
		exit;
	}
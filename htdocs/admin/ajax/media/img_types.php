<?php 

/**	\file img_types.php
 * 
 * 	Fichier ajax permettant, l'ajout, et le suppression des types d'images. Les paramètres acceptés sont les suivants :
 * 	- action : Obligatoire, type d'action à réaliser. Les valeurs acceptées sont les suivantes :
 * 		- add : permet la création d'un nouvel type d'image
 * 		- update : permet la mise à jour d'un type d'image existant
 * 		- del : permet la suppression d'un type d'image existant
 * 
 * 	@todo les objects de type obj_id 1, obj_id 2 ne sont pas encore pris en compte à compléter au besoin
 * 
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

$json = array();
if (isset($_GET['action'])) {
	if ($_GET['action'] == 'add') {
		if (isset($_GET['type_name'], $_GET['cls_id'], $_GET['obj0'], $_GET['obj1'], $_GET['obj2'] ) && trim($_GET['type_name']) != "") {

			$type_id = img_images_types_add($_GET['cls_id'], $_GET['type_name']);
			if (!$type_id || $type_id <= 0) {
				$json['error'] = _('L\'ajout du type d\'image a échoué');
			}else{
				$json['data']['id'] = $type_id;
				$json['message'] = _('Le type d\'image a bien été ajouté');
				$json['html'] = view_admin_img_objects( $_GET['cls_id'], $_GET['obj0'], $_GET['obj1'], $_GET['obj2'], $type_id, true);
			}

		}else{
			$json['error'] = _('Paramètres incorrects.');
		}
	}else if ($_GET['action'] == 'del') {
		if (isset($_GET['type_id']) && $_GET['type_id'] > 0) {

			$deleted = img_images_types_del($_GET['type_id']);
			if (!$deleted) {
				$json['error'] = _('La suppression du type a échoué');
			}else{
				$json['message'] = _('Le type a bien été supprimé');
			}

		}else{
			$json['error'] = _('Paramètres incorrects.');
		}
	}else if ($_GET['action'] == 'update') {
		if (isset($_GET['type_id'], $_GET['type_name']) && $_GET['type_id'] > 0 && trim($_GET['type_name']) != '') {

			$updated = img_images_types_update($_GET['type_id'], $_GET['type_name']);
			if (!$updated) {
				$json['error'] = _('La suppression du type a échoué');
			}else{
				$json['message'] = _('Le type a bien été modifié');
			}

		}else{
			$json['error'] = _('Paramètres incorrects.');
		}
	}

	if (isset($json['error'])) {
		header('HTTP/1.1 400 Bad Request');
	}else{
		header('HTTP/1.1 200 OK');
	}
	header('Content-Type: application/html');
	print json_encode($json);
}


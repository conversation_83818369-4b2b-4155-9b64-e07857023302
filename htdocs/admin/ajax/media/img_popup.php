<?php

	/**	\file img_popup.php
	 * 
	 * 	Ce fichier fourni une interface qui permet l'upload d'une nouvelle image et/ou son association à un contenu (produits, actualités, etc...)
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

	if( !isset($_GET['classe']) ){
		$error_params = true;
	}
	
	if( isset($error_params) ){
		print _('Il manque des paramètres');
		exit;
	}

	$add_new_imgs = false;
	$data_obj = $url_obj = '';

	if( $_GET['classe'] == 'add_imgs' ){
		$add_new_imgs = true;
	}else{
		if( !isset($_GET['obj']) || !is_numeric($_GET['classe']) || !control_array_integer($_GET['obj'], false, true)){
			$error_params = true;
		}else{
		
			$objs = $_GET['obj'];
			$url_obj = '&obj[]='.implode( '&obj[]=', $objs );
			$data_obj = array_pad($objs, 3, 0);
		}
	}


	$tab_add = $tab_media = $close = false; 

	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'media';
	}

	if( $add_new_imgs ){
		$_GET['tab'] = 'add';
	}
	
	switch( $_GET['tab'] ){
		case "add":
			$tab_add = true;
			break;
		default:
			$tab_media = true;
			break;
	}

	if( isset($_FILES['img1']) ){
	
		$img_names = array('img1','img2','img3');
		// permet l'upload d'une image sur un produit
		foreach( $img_names as $name ){
			if( isset($_FILES[$name]) && $_FILES[$name]['error']!=UPLOAD_ERR_NO_FILE ){
				$id = false;
				switch( $_GET['classe'] ){
					case CLS_PRODUCT: 
						$id = prd_images_upload( $data_obj[0], $name );
						break;
					case CLS_NEWS: 
						$id = news_image_upload( $data_obj[0], $name );
						break;
					case CLS_CMS: 
						$id = cms_images_upload( $data_obj[0], $name );
						break;
					case CLS_CATEGORY: 
						$id = prd_cat_images_upload( $data_obj[0], $name );
						break;
					case CLS_CTR_MODELS:
						$id = ctr_images_upload( $name, $data_obj[0] );
						break;
					case CLS_CTR_MKT:
						$id = ctr_catalogs_images_upload( $name, $data_obj[0], $data_obj[1] );
						break;
					case CLS_FAQ_CAT: 
						$id = faq_cat_images_upload( $data_obj[0], $name );
						break;
					case CLS_FAQ_QST: 
						$id = faq_qst_images_upload( $data_obj[0], $name );
						break;
					case CLS_DOCUMENT: 
						$id = doc_images_upload( $data_obj[0], $name );
						break;
					case CLS_TYPE_DOCUMENT: 
						$id = doc_types_images_upload( $data_obj[0], $name );
						break;
					case 'add_imgs':
						$id = img_images_upload( $name );
						break;
				}
				if( !$id ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image secondaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}	
		}
		$close = true;
		
	}

	// Permet l'association d'une image existante à un objet
	if (isset($_POST['save'])) {
		
		if (! (isset($_POST['media']) && $_POST['media'])){
			$error = _('Vous devez sélectionner une image');
		}else {
			$has_error = false;
			switch( $_GET['classe'] ){
				case CLS_PRODUCT: 
					if (! prd_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break; 
				case CLS_NEWS:
					if (! news_image_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_CMS:
					if (! cms_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_CATEGORY:
					if (! prd_cat_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_CTR_MODELS:
					if( !ctr_images_add_existing($_POST['media'], $_GET['obj'][0]) ){
						$has_error = true;
					}
					break;
				case CLS_CTR_MKT:
					if( !ctr_catalogs_images_add_existing($_POST['media'], $_GET['obj'][0], $_GET['obj'][1]) ){
						$has_error = true;
					}
					break;
				case CLS_FAQ_CAT:
					if (! faq_cat_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_FAQ_QST:
					if (! faq_qst_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_DOCUMENT:
					if (! doc_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				case CLS_TYPE_DOCUMENT:
					if (! doc_types_images_add_existing($_GET['obj'][0], $_POST['media'])){
						$has_error = true;
					}
					break;
				default:
					// Nouveau, permet de généralisé l'ajout des images sur n'importe quel classe
					if (fld_classes_exists($_GET['classe'])) {
						if (!img_images_objects_add($_GET['classe'], $_POST['media'], $_POST['obj_type_id'], $data_obj[0], $data_obj[1], $data_obj[2])) {
							$has_error = true;
						}
					}
					break;
			}
			
			if ($has_error){
				$error = _('Une erreur inattendue s\'est produite lors de la mise à jour de l\'image.');
			}else{
				$success = _('L\'image a été ajoutée avec succès.');
			}
		}
		
		if( isset($_POST['ajax']) && IS_AJAX){		
			if (isset($error)) echo '<div class="msg errors">' . $error . '</div>';
			if (isset($success)) echo '<div class="msg success">' . $success . '</div>';
			exit;
		}
	}
	
	$msg_success = '';
	switch( $_GET['classe'] ){
		case CLS_PRODUCT: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées au produit.');
			break;
		case CLS_NEWS: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à l\'actualité.');
			break;
		case CLS_CMS: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la page de contenu.');
			break;
		case CLS_CATEGORY: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la catégorie.');
			break;
		case CLS_CTR_MODELS: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées au modèle.');
			break;
		case CLS_CTR_MKT: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la surcharge.');
			break;
		case CLS_FAQ_CAT: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la catégorie de la FAQ.');
			break;
		case CLS_FAQ_QST: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la question de la FAQ.');
			break;
		case CLS_DOCUMENT: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées au document.');
			break;
		case CLS_TYPE_DOCUMENT: 
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées au type de document.');
			break;
		case 'add_imgs':
			$msg_success .= _('Les images ci-dessous ont été automatiquement ajoutées à la Médiathèque.');
			break;
	}

	define('ADMIN_PAGE_TITLE', _('Ajouter des images'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'tabs');
	require_once('admin/skin/header.inc.php');

	if( $add_new_imgs ){
		// print '<h3>Ajouter une ou plusieurs images à votre Médiathèque</h3>';
	}else{
		$obj_type_id = isset($_GET['obj_type_id']) ? $_GET['obj_type_id'] : '';
		print '
			<ul class="tabstrip">		
				<li '.( $tab_media ? 'class="selected"' : '' ).'><a href="?obj_type_id='.$obj_type_id.'&classe='.$_GET['classe'].$url_obj.'&amp;tab=media">'._('Médiathèque').'</a></li>	
				<li '.( $tab_add ? 'class="selected"' : '' ).'><a href="?obj_type_id='.$obj_type_id.'&classe='.$_GET['classe'].$url_obj.'&amp;tab=add">'._('Ajouter').'</a></li>	
			</ul>
		';
	}
?>
	<div class="tabscontent">
		<?php if( $tab_media ){ ?>
			<form id="form" action="/admin/ajax/media/img_popup.php?classe=<?php print $_GET['classe'].$url_obj; ?>" method="post" enctype="multipart/form-data">
				<input id="media" name="media" type="hidden" />
				<input id="type" name="type" type="hidden" />
				<input type="hidden" name="images" />
				<input type="hidden" name="$data_obj" value="<?php print $data_obj[0]; ?>" />
				<input type="hidden" name="$data_obj" value="<?php print $data_obj[1]; ?>" />
				<input type="hidden" name="$data_obj" value="<?php print $data_obj[2]; ?>" />
				<?php
					if (isset($error)) echo '<div class="msg errors">' . $error . '</div>';
					if (isset($success)) echo '<div class="msg success">' . $success . '</div>';
				?>
				<div style="float: right">
					<input id="src" name="q" type="text" />
					<input id="search" name="search" type="submit" class="button" value="<?php print _('Rechercher'); ?>" />
				</div>
					<div class="clear"></div>
				<div id="mediatheque-container">
					<div id="nav">
						<ul>
							<li>
								<a id="type-all" class="racine dir all selected" href="#"><strong><?php print _('Médiathèques'); ?></strong></a>
								<ul>
									<li><a id="type-prd" class="dir prd" href="#"><?php print _('Produits'); ?></a></li>
									<li><a id="type-prd-cat" class="dir prd-cat" href="#"><?php print _('Catégories'); ?></a></li>
									<li><a id="type-str" class="dir str" href="#"><?php print _('Magasins'); ?></a></li>
									<li><a id="type-cms" class="dir cms" href="#"><?php print _('Contenu'); ?></a></li>
									<li><a id="type-news" class="dir news" href="#"><?php print _('Actualité'); ?></a></li>
								</ul>
							</li>
						</ul>
					</div>
					<div id="mediatheque"></div>
					<div class="clear"></div>
				</div>
				<div class="padt">
					<input name="save" type="submit" class="button" value="<?php print _('Ajouter'); ?>" />
				</div>
			</form>
			<script>
				// Variable contenant l'url vers les images
				var img_url = "<?php print $config['img_url']; ?>";

				$(document).ready(function() {
					$('#file').focus(function() {
						if (current) {
							current.removeClass('selected');
							current = null;
						}
						$('#mode_file').click();
					});
					
					var count = 0;
					var ajax;
					var mediatheque = $('#mediatheque');
					var h = mediatheque.height();
					
					var getId = function(img) {
						var id = (new RegExp('^.*-([0-9]+)$')).exec(img.attr('id'));
						if (! (id && id[1] !== undefined)) return ;
						return id[1];
					};
					
					var current;
					var bindImages = function(imgs) {
						imgs.find('img').each(function() {
							$(this).click(function() {
								var id = getId($(this));
								if (current) current.removeClass('selected');
								$(this).addClass('selected');
								current = $(this);
								$('#mode_mediatheque').click();
							});
						});
					};
					
					var doSearch = function() {
						if (ajax) ajax.abort();
						
						mediatheque.empty();
						
						count = 0;
						ajax = null;
						evalScroll();
					};
					
					var curType;
					$('#nav').find('a').each(function() {
						if (! curType) curType = $(this);
						$(this).click(function(e) {
							curType.removeClass('selected');
							$(this).addClass('selected');
							curType = $(this);
							$('#type').val((new RegExp('^type-(.*)$')).exec($(this).attr('id'))[1]);
							doSearch();
							return false;
						});
					});
					$('#type').val('all');
					
					// Que fait cette fonction exactement ?
					var evalScroll = function() {
						if (ajax) return ;
						
						var scroll = mediatheque.scrollTop();
						var scrollMax = document.getElementById('mediatheque').scrollHeight;
						
						var error = function(e) {
							ajax = null;
							evalScroll();
						};
						
						if (scroll >= scrollMax - 2 * h) {
						
							ajax = $.ajax({
								url: '/admin/ajax/tinymce/ajax-images.php',
								data: $('#form').serialize() + '&size=medium&page=' + (count+1),
								type: 'post',
								dataType: 'json',
								success: function(response) {
									if (! response) return error();
									
									var i, img;
									var t = $('<div></div>');
									var images = response.images || [];
									var thumb = response.thumb;
									
									for (i in images) {
										img = images[i];
										t.append('<div class="preview"><img id="img-' + img.id + '" src="' + img_url + '/' + thumb.width + 'x' + thumb.height + '/' + img.id + '.' + thumb.format + '" alt="" width="' + thumb.width + '" height="' + thumb.height + '" /></div>');
									}
									bindImages(t);
									
									mediatheque.append(t);
									
									count++;
																		
									if( images.length && response.nbpages > count ){
										ajax = null;
										evalScroll();
									}
								},
								error: function(xhr, e) { return error(e); }
							});
							
						}
					};
					mediatheque.scroll(evalScroll);
					evalScroll();
					
					$('#form').submit(function() {
						var img = $('#mediatheque').find('img.selected');
						if (img.length) $('#media').val(getId(img));
						$.post($(this).attr('action'),'save=1&ajax=1&obj_type_id='+<?php print isset($_GET['obj_type_id']) && trim($_GET['obj_type_id']) ? $_GET['obj_type_id'] : '\'\''; ?>+'&'+$(this).serialize(), function(msg){
							$('.msg').remove() ; 
							$('.tabscontent').prepend( msg );
							
							window.parent.refresh_img_list(<?php print isset($_GET['obj_type_id']) && trim($_GET['obj_type_id']) ? $_GET['obj_type_id'] : 0; ?>);
						});
						return false; 
					});
					
					$('#src').keydown(function(e) {
						if (e.which == 13) {
							doSearch();
							return false;
						}
					});
					$('#search').click(function(e) {
						doSearch();
						return false;
					});
				});
			</script>
			
		<?php } elseif( $tab_add ) { ?>
			
			<div class="dropzone"><?php print _('Déplacer les fichiers ici'); ?> <br/><?php print _('ou'); ?> <br/><button type="button" name="search" value="<?php print _('Parcourir'); ?>"><?php print _('Parcourir'); ?></button></div>
			<form action="/admin/ajax/media/img_popup.php?classe=<?php print $_GET['classe'].$url_obj; ?>&tab=add" enctype="multipart/form-data" id="hiddenupload" method="post">
				<input type="file" name="files[]" multiple="multiple" />
				<table>
					<caption><?php print _('Ajout d\'images'); ?></caption>
					<tfoot>
						<tr><td colspan="2">
							<input type="submit" value="<?php print _('Enregistrer'); ?>" name="save">
						</td></tr>
					</tfoot>
					<tbody>
						<tr><th colspan="2"><?php print _('Images'); ?></th></tr>
						<tr>
							<td><label for="add"><?php print _('Ajouter une image :'); ?></label></td>
							<td><input type="file" style="width: auto;" name="img1"></td>
						</tr>
						<tr>
							<td><label for="add"><?php print _('Ajouter une image :'); ?></label></td>
							<td><input type="file" style="width: auto;" name="img2"></td>
						</tr>
						<tr>
							<td><label for="add"><?php print _('Ajouter une image :'); ?></label></td>
							<td><input type="file" style="width: auto;" name="img3"></td>
						</tr>
					</tbody>
				</table>
			</form>
			<div class="loadzone"></div>
			<script>
				 $(function(){
					<?php if( $close ){ ?>
						window.parent.refresh_img_list(<?php print isset($_GET['obj_type_id']) ? $_GET['obj_type_id'] : 0; ?>);
						window.parent.hidePopup();
					<?php } ?>
				});
			</script>	
			<!--[if !ie]>-->
			<script>
				$(function(){
					$.event.props.push('dataTransfer');
					$('.dropzone').bind( 'dragenter dragover', false).bind( 'drop', function( e ) {
						e.stopPropagation();
						e.preventDefault();
						$('.dropzone').hover();
						
						$.each( e.dataTransfer.files, function(index, file){
							upload_file( file );
						});
					}).click(function(){
						$('#hiddenupload [type=file][multiple]').click();
						return false;
					});
					$('[type=file]').change(function(e){
						$.each( e.target.files, function(index,file){
							upload_file( file );
						});
					});
				});
				
				var cpt=0;
				function upload_file( file ){
					
					var cpt_error=0;
					var data = new FormData();
					data.append('img', file);
					data.append('classe', <?php print $add_new_imgs ? '\'add-imgs\'' : $_GET['classe']; ?>);
					data.append('obj_type_id', <?php print !empty($_GET['obj_type_id']) ? $_GET['obj_type_id'] : '\'\''; ?>);
					data.append('obj[]', <?php print isset($data_obj[0]) ? $data_obj[0] : 0; ?>);
					data.append('obj[]', <?php print isset($data_obj[1]) ? $data_obj[1] : 0; ?>);
					data.append('obj[]', <?php print isset($data_obj[2]) ? $data_obj[2] : 0; ?>);
					data.append('cpt', cpt);
					
					$('.loadzone').append('<div id="up-'+cpt+'" class="img-images">'
										+ '		<img class="loader" src="/admin/images/loader2.gif" height="" width="" alt="<?php print _('Chargement en cours...') ?>"/>'
										+ '</div>');
					cpt_error = cpt;
					$.ajax({
						url: '/admin/ajax/media/img_upload.php',
						data: data,
						cache: false,
						contentType: false,
						processData: false,
						type: 'POST',
						dataType:'json',
						success: function(json){
							if( json.error ) alert( json.error );
							else{
								$('.loadzone .msg').remove();
								$('.loadzone').prepend( '<div class="msg success"><p><?php print addslashes( $msg_success ); ?></p></div>' );
							}
							
							$('#up-'+json.cpt).html( json.html );
							window.parent.refresh_img_list(<?php print isset($_GET['obj_type_id']) ? $_GET['obj_type_id'] : 0; ?>);
						},
						error: function(error){
							console.log(error);
							$('#up-'+cpt_error).html( "<?php print _('Erreur de transfert'); ?>" );
						}
					});

					cpt++;
				}
				</script>
			<!--<![endif]-->
			<!--[if lte IE 10]>
				<script>
					$(document).ready(function(){
						$('.dropzone, input[type=file]:first').css('display', 'none');
						$('#hiddenupload').show();
						$('#hiddenupload').css('height', 'auto');
						$('#hiddenupload').css('width', 'auto');
						$('#hiddenupload').css('position', 'relative');
						$('#hiddenupload').css('text-indent', '0px');
					});
				</script>
			<!--<![endif]-->
		<?php } ?>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>

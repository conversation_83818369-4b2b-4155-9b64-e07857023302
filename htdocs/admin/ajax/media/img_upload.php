<?php

/**	\file img_upload.php
 * 
 * 	Ce fichier permet le téléchargement d'un fichier image du poste client vers le serveur. Les paramètres à fournir sont les suivants :
 * 	- obj : identifiant de l'objet auquel l'image va être associée
 *  - classe : identifiant de la classe de l'objet, parmi les valeurs suivantes :
 * 		- add-imgs : ajoute l'image sans l'associer à aucune contenu
 * 		- CLS_PRODUCT : Produit
 * 		- CLS_USER : Compte
 * 		- CLS_NEWS : Actualité
 * 		- CLS_CMS : Page de contenu
 * 		- CLS_CATEGORY : Catégorie de produits
 * 		- CLS_CTR_MODELS : Modèle
 * 		- CLS_CTR_MKT : Comparateur de prix / Place de marché
 * 		- CLS_FAQ_CAT : Catégorie de la FAQ
 * 		- CLS_FAQ_QST : Question de la FAQ
 * 		- CLS_DOCUMENT : Document
 * 		- CLS_TYPE_DOCUMENT : Type de document
 * 
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ADD');

// Vérifie que l'utilisateur peut associer l'image au type de contenu demandé
if( isset($_POST['classe']) ){
	switch( $_POST['classe'] ){
		case CLS_PRODUCT:
			gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD');
		break;
		case CLS_CATEGORY:
			gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CAT');
		break;
		case CLS_CMS:
			gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CMS');
		break;
		case CLS_NEWS:
			gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ASSOC_NEWS');
		break;
		case CLS_USER:
			gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_ASSOC_USR');
		break;
	}
}


$json = array(); 
if( !isset( $_POST['obj'], $_POST['classe'] ) || !control_array_integer($_POST['obj'], false, true) ){
	$json['error']  = _('Paramètres manquants');
}else{

	$data_obj = array_pad($_POST['obj'], 3, 0);

	if( $_POST['classe'] != 'add-imgs' &&  !is_numeric( $_POST['classe']) ){
		$json['error']  = _('Paramètres manquants');
	}else{
		$cls_id = $_POST['classe'];
	}
}

if( !isset($json['error']) ){
	$id = false;
	// Permet d'utiliser le nouveau système d'image généralisé
	if (isset($_FILES['img'], $_POST['obj_type_id']) && $_POST['obj_type_id'] > 0) {
		$new_img_id = img_images_add($_FILES['img']['tmp_name'], $_FILES['img']['name'], true );

		if ($new_img_id && $new_img_id > 0) {
			if(!img_images_objects_add( $cls_id, $new_img_id, $_POST['obj_type_id'], $data_obj[0], $data_obj[1], $data_obj[2] )){
				$json['error'] = _("Cette image n'a pu être ajoutée ou existe déjà dans la librairie.");
			}else{
				$id = $new_img_id;
			}
		}

	}else if( isset($_FILES['img']) && $_FILES['img']['error']!=UPLOAD_ERR_NO_FILE ){
		// permet l'upload d'une image sur un objet
		$id = false;
		switch( $cls_id ){
			case CLS_PRODUCT: 
				$id = prd_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_USER: 
				$id = gu_images_main_upload( $data_obj[0], 'img' );
				break;
			case CLS_NEWS: 
				require_once('news.inc.php');
				$id = news_image_upload( $data_obj[0], 'img' );
				break;
			case CLS_CMS: 
				require_once('news.inc.php');
				$id = cms_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_CATEGORY: 
				$id = prd_cat_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_CTR_MODELS: 
				$id = ctr_images_upload( 'img', $data_obj[0] );
				break;
			case CLS_CTR_MKT: 
				$id = ctr_catalogs_images_upload( 'img', $data_obj[0], $data_obj[1] );
				break;
			case CLS_FAQ_CAT: 
				$id = faq_cat_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_FAQ_QST: 
				$id = faq_qst_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_DOCUMENT: 
				$id = doc_images_upload( $data_obj[0], 'img' );
				break;
			case CLS_TYPE_DOCUMENT: 
				$id = doc_types_images_upload( $data_obj[0], 'img' );
				break;
			case 'add-imgs':
				$id = img_images_upload( 'img' );
				break;
		}
	}	

	if( !$id ){
		if (!isset($json['error'])) {
			$json['error'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image secondaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}
	else if($id > 0){
		$size = $config['img_sizes']['medium'];
		$json['html'] = '<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$id.'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />';
	}
}

if( isset($_POST['cpt']) ) $json['cpt'] = $_POST['cpt'];

print json_encode( $json );
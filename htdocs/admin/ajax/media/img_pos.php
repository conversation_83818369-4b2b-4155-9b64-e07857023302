<?php
/**	\file img_pos.php
 * 
 *	Ce fichier permet la mise à jour des positions pour les images en fonction de la classe de l'objet
 *
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

if( !isset( $_GET['classe'] ) ) return false; 

if( !isset($_GET['obj'], $_GET['img'], $_GET['img_next'], $_GET['img_prev'], $_GET['position']) || !is_numeric($_GET['img']) /*|| (!is_numeric($_GET['img_next']) && !is_numeric($_GET['img_prev']))*/ || !is_numeric($_GET['position']) ){
	print _('Erreur paramètres');
	exit;
}

if( !is_array($_GET['obj']) || !sizeof($_GET['obj']) ){
	print _('Il manque des paramètres');
	exit;
}


$position = $_GET['position']; 

$drag_classe = false; 
switch( $_GET['classe'] ){
	case CLS_PRODUCT:
		if( !prd_products_exists( $_GET['obj'][0] ) ){
			print _('Produit non disponible');
			exit;
		}

		$drag_classe = DD_PRD_IMAGE; 
		
		$prd = prd_products_get_simple( $_GET['obj'][0] ); 
		if( !$prd || !ria_mysql_num_rows($prd) ){
			print _('Impossible de retrouver le produit');
			exit;
		}

		$prd = ria_mysql_fetch_array( $prd ); 

		// si position a zéro il faut changer l'image principal
		if( $position == 0 ){
			if( !prd_images_main_add_existing( $prd['id'], $_GET['img'] ) ){
				print _('Erreur lors de la mise à jour de l\'image principale');
				exit;
			}
			// retire l'image des secondaires
			prd_images_del($prd['id'], $_GET['img'] );
			
			$rimgs = prd_images_get( $prd['id'] );	
			if( $rimgs ){
				$new_secondary = ria_mysql_fetch_array( $rimgs );
				$_GET['img_next'] = $new_secondary['id'];
			}
			
			// met l'image principal précédente dans les images secondaires
			if( !prd_images_add_existing( $prd['id'], $prd['img_id'] ) ){
				print _('Erreur lors de la mise à jour de l\'image principale');
				exit;
			}
			
			$_GET['img'] = $prd['img_id'];
			
		}
		else if( $prd['img_id'] == $_GET['img']){ // si l'image principal à changé de position
			$rimgs = prd_images_get($prd['id'], 0, false, true);
			if( $rimgs && ria_mysql_num_rows($rimgs)){
				$new_principal = ria_mysql_fetch_array( $rimgs );

				// ajoute l'image en tant que principal
				if(!prd_images_main_add_existing( $prd['id'], $new_principal['id'] )){
					print _('Erreur lors de la mise à jour de l\'image principale');
					exit;
				}
				
				// retire l'image des secondaires
				prd_images_del($prd['id'], $new_principal['id']);
			}else{
				prd_images_main_del($prd['id']);
			}

			// ajoute l'image en tant que secondaire publié ou dé-publié
			if($position == -1){
				if(!prd_images_add_existing($prd['id'], $prd['img_id'], true, true, false)){
					print _('Erreur lors de la mise à jour de l\'image principale');
					exit;
				}
			}else{
				if(!prd_images_add_existing($prd['id'], $prd['img_id'])){
					print _('Erreur lors de la mise à jour de l\'image principale');
					exit;
				}
			}
		}

		if($position != -1){
			prd_images_set_publish($_GET['obj'][0], $_GET['img'], 1);
		}else{
			prd_images_set_publish($_GET['obj'][0], $_GET['img'], 0);
		}	
		break; 
	case CLS_NEWS: 
		if( !news_exists( $_GET['obj'][0] ) ){
			print _('Actualité non disponible');
			exit;
		}
		
		$drag_classe = DD_NWS_IMAGE; 
		break; 
	case CLS_CMS: 
		if( !cms_categories_exists( $_GET['obj'][0] ) ){
			print _('CMS non disponible');
			exit;
		}
		
		$drag_classe = DD_CMS_IMAGE; 
		break; 
	case CLS_CATEGORY: 
		if( !prd_categories_exists( $_GET['obj'][0] ) ){
			print _('Catégorie non disponible');
			exit;
		}
		
		$drag_classe = DD_CAT_IMAGE; 
		break;
	case CLS_CTR_MODELS:
		if( !ctr_models_exists($_GET['obj'][0]) ){
			print _('Modèle CTR/MKT non disponible');
			exit;
		}

		$drag_classe = DD_CTR_MODELS;
		break;
	case CLS_CTR_MKT:
		if( !prd_products_exists($_GET['obj'][0]) ){
			print _('Comparateurs / Places de marché non disponibles');
			exit;
		}

		if( !ctr_comparators_exists($_GET['obj'][1]) ){
			print _('Comparateurs / Places de marché non disponibles');
			exit;
		}

		$drag_classe = DD_CTR_MKT;
		break;
	case CLS_FAQ_CAT: 
		if( !faq_categories_exists( $_GET['obj'][0] ) ){
			print _('Catégorie non disponible');
			exit;
		}
		
		$drag_classe = DD_FAQ_CAT_IMAGE; 
		break;
	case CLS_FAQ_QST: 
		if( !faq_questions_exists( $_GET['obj'][0] ) ){
			print _('Question non disponible');
			exit;
		}
		
		$drag_classe = DD_FAQ_QST_IMAGE; 
		break;
	case CLS_STORE:
		$drag_classe = DD_STORE;

		break;
	case CLS_DOCUMENT: 
		if( !doc_documents_exists( $_GET['obj'][0] ) ){
			print _('Document non disponible');
			exit;
		}
		
		$drag_classe = DD_DOC_IMAGE; 
		break;
	case CLS_TYPE_DOCUMENT: 
		if( !doc_types_exists( $_GET['obj'][0] ) ){
			print _('Type de document non disponible');
			exit;
		}
		
		$drag_classe = DD_DOC_TYPE_IMAGE; 
		break;
	default:
		if ( !empty($_GET['imgClsObject']) && $_GET['imgClsObject'] == CLS_IMAGES_OBJECT) {
			
			if( !img_images_objects_exist(0, $_GET['obj'][0]) ){
				print _('Objet d\'image non disponible');
				exit;
			}else{
				$_GET['obj'][0] = $_GET;
			}

			$drag_classe = DD_IMAGES_OBJECT;
			break;
		}else{
			print _('Classe non implémentée');
		}
}




if($position != -1){
	$target = false; 
	$where = 'before';

	if( is_numeric( $_GET['img_next'] ) ){
		$target = $_GET['img_next'];
	} elseif( is_numeric( $_GET['img_prev'] ) ){
		$target = $_GET['img_prev'];
		$where = 'after';	
	}
	
	if( !obj_position_update( $drag_classe, $_GET['img'], $target, $where, $_GET['obj'][0], $drag_classe !== DD_STORE) ){
		print _('Une erreur est survenue lors du changement de position');
		exit;
	}
}


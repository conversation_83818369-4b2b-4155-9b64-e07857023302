<?php

	/**	\file ajax-import-mapping.php
	 * 	Cette page est utilisée pour gérer le mapping réalisé entre un fichier à importer et le schéma de données riaShop
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit de mettre à jour la position d'une question de la FAQ
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

	// Cette page doit obligatoire être appelée en Ajax
	if( !IS_AJAX ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}

require_once( 'imports.inc.php' );

if( isset( $_GET['map'], $_GET['col'], $_GET['cls_id'], $_GET['imp_id'] ) ){

	$backup = false;
	
	// Détermine si l'utilisateur a accès au import de type synchronisation
	$access_sync = false;
	if( $config['USER_RIASTUDIO'] ){
		$access_sync = null;
	}

	// Détermine si l'utilisateur a accès aux imports de type système
	$access_system = false;
	if( $config['USER_RIASTUDIO'] ){
		$access_system = null;
	}

	$rImport = ipt_imports_get($_GET['imp_id'], true, false, '', 0, 0, false, false, '', '', [], $access_system, -1, $access_sync);
	if ($rImport && ria_mysql_num_rows($rImport)) {
		$import = ria_mysql_fetch_assoc( $rImport );
		$backup = $import['backup'];

		// Récupère la sous classe de l'import
		$user_discount = false;
		if( $import['info'] != '' ){
			$import['info'] = json_decode( $import['info'], true );

			if( isset($import['info']['sub_class']) && $import['info']['sub_class'] == 'user_discount' ){
				$user_discount = true;
			}
		}	
	}

	// spécifique au champ avancé
	if( preg_match("/^FLD_[0-9]*/", $_GET['map']) ){
		$fld_post = explode('_', $_GET['map']);
		$_GET['map'] = $fld_post[0];
		$_GET['fld_id'] = $fld_post[1];
	}

	if( !ipt_schemas_code_exist( $_GET['map'] ) ){
		echo _('Le code n\'existe pas.');
		exit;
	}


	if( !$rSchema = ipt_schemas_get( 0, $_GET['map'] ) ){
		echo _('Aucun schema');
		exit;
	}

	$schema = ria_mysql_fetch_assoc( $rSchema );
	
	$rMapping = ipt_mapping_get( $_GET['imp_id'], $_GET['map'], $_GET['col'] );
	$is_alias = false;
	$map = null;
	if( $rMapping ){
		$map = ria_mysql_fetch_assoc( $rMapping );
	}else{
		$alias = ipt_mapping_gen_alias( $_GET['label'] );
		$rCode = ipt_mapping_get_by_alias( $alias, $_GET['cls_id'] );
		$map = null;

		if( $rCode ){
			$is_alias = true;
			$map = ria_mysql_fetch_assoc( $rCode );
		}
	}

	$data = array(
		'col'     => $_GET['col'],
		'desc'    => ($schema['code'] == 'USR_SIRET' ? '' :$schema['desc']),
		'options' => array()
	);
		
	$translate_option = false;
	if( $schema['is_translatable'] && count( $config['i18n_lng_used'] ) > 1 ){

		$selected = $config['i18n_lng'];

		if( !is_null( $map ) && $map['is_translatable'] ){
			$selected = $map['lang'];
		}

		$translate_option = array(
			'label'    => _('Langue :'),
			'type'     => 'select',
			'name'     => 'lang',
			'selected' => $selected,
			'value'    => getLangArray()
		);
	}

	if( isset( $_GET['fld_id'] ) ){
		if( !is_numeric( $_GET['fld_id'] ) || $_GET['fld_id'] < 0 ){
			echo _("le champ avancé n'existe pas");
			exit;
		}
		if( !fld_fields_exists( $_GET['fld_id'] ) ){
			echo _("le champ avancé n'existe pas");
			exit;
		}

		$fld_type = fld_fields_get_type( $_GET['fld_id'] );
		switch( $fld_type ){
			case 8:
				if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
					$vals = json_decode( $map['vals'], true );
				}else{
					$match = $schema['code'];

					if( $fld_type == 8 ){
						$match = 'bool';
					}
					switch( $match ){
						case 'bool' :
							$vals = array(
								'bool' => array( '' )
							);
							break;
					}
				}
				$data['options'][] = array(
					'label'        => _('Caractère de validation :'),
					'mandatory'    => true,
					'require_vals' => true,
					'type'         => 'text',
					'name'         => 'vals,bool',
					'value'        => implode(', ', $vals['bool'])
				);
				break;
			case 6:
			case 12:
				$data['options'][] = array(
					'label'     => _('Séparateur :'),
					'mandatory' => true,
					'type'      => 'text',
					'name'      => 'sep',
					'value'     => ( ( !is_null( $map ) && $map['separator'] ) ? $map['separator'] : '' )
				);
				break;
			default:
				if( $translate_option ){
					$data['options'][] = $translate_option;
				}
				break;
		}
		echo json_encode( $data );
		exit;
	}


	if( $schema['is_obj_id'] ){
		$code = array_map( 'strtolower', explode( '_', $schema['code'] ) );

		if( !in_array( $code[1], ipt_rows_get_id_types($schema['code']) ) ){
			$selected = '';

			if( !is_null( $map ) ){
				$selected = $map['id_type'];
			}
			$data['options'][] = array(
				'label'     => _('Type d\'identifiant :'),
				'mandatory'	=> true,
				'is_obj_id' => true,
				'type'      => 'select',
				'name'      => 'id-type',
				'selected'  => $selected,
				'value'     => displaySelectOptions( 'idType', $schema['code'] )
			);
		}
		$cls_user_multi_id = $config['tnt_yuto_essentiel'] ? $_GET['cls_id'] != CLS_USER : true;
		if( !in_array($schema['code'], ipt_is_identifier_relation() ) && $cls_user_multi_id  ){
			// Construction du message pour information ce que veut dire "Identifiant des [classe]"
			switch( $import['action'] ){
				case 'add':
					$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à ajouter dans [formule].');
					break;
				case 'del' :
					$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à supprimer dans [formule].');
				case 'upd' :
					$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à mettre à jour dans [formule].');
					break;
				case 'add/upd' :
					$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à ajouter ou à mettre à jour dans [formule].');
					break;
			}
			
			// On remplace dans le texte d'information l'action effectuée par l'import et si cela impactera Yuto ou RiaShop (Yuto = pur essentiel)
			$desc_id = str_replace(
				array('[objets]', '[formule]'),
				array(strtolower2(fld_classes_get_name($schema['cls_id'])), RegisterGCP::getPackage($config['tnt_id']) == 'essentiel' ? 'Yuto' : 'RiaShop'),
				$desc_id
			);

			$data['options'][] = array(
				'label'     => sprintf( _('Identifiant des %s :'), strtolower2(fld_classes_get_name($schema['cls_id'])) ),
				'desc_id'		=> $desc_id,
				'is_obj_id' => true,
				'type'      => 'radio',
				'name'      => 'is-obj',
				'value'     => $schema['code'] == 'USR_REF' ? 1 : 0
			);
		}

	}

	if( $translate_option ){
		$data['options'][] = $translate_option;
	}

	if( $schema['is_relation'] ){

		$selected = '';

		if( !is_null( $map ) && $map['is_relation'] ){
			if( !is_null( $map['rel_id_type'] ) ){
				$selected = $map['rel_id_type'];
			}
		}
		//chargement des relations
		$val = array(
			0 => ''
		);
		// Si la classe sujet a import est produit on charge les relations pour les produits
		// et les relations parent/enfants ou enfant/parent
		switch( $schema['cls_id'] ){
			case CLS_PRODUCT :
				$rPrdRelTypes = prd_relations_types_get();
				if( $rPrdRelTypes ){
					while( $row = ria_mysql_fetch_assoc( $rPrdRelTypes ) ){
						$val[$row['id']] = $row['name'];
					}
				}
				$val['child'] = _('Produits enfants');
				$val['parent'] = _('Produit parent');
				break;
			case CLS_USER :
				$val['responsable'] = _('Compte parent');
				$val['représentant'] = _('Représentant');
				break;
			default:
				$rRelTypes = rel_relations_types_get( 0, $schema['cls_id'] );

				if( $rRelTypes ){
					while( $row = ria_mysql_fetch_assoc( $rRelTypes ) ){
						$val[$row['id']] = $row['name'];
					}
				}
				break;
		}

		if( $schema['rel_require_action'] ){

			$data['options'][] = array(
				'label'     => _('Type d\'action :'),
				'mandatory' => true,
				'type'      => 'select',
				'name'      => 'rel-action',
				'selected'  => ( isset( $map['rel_action'] ) ? $map['rel_action'] : '' ),
				'value'     => displaySelectOptions( 'action', $schema['code'] )
			);
		}

		$selected = '';

		if( !is_null( $map ) && $map['rel_type'] ){
			$selected = $map['rel_type'];
		}


		switch( $schema['code'] ){
			case 'FLD' :
				$fld = array(0=>"");

				$r = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $_GET['cls_id'] );

				if( !$r || !ria_mysql_num_rows($r)){
					break;
				}

				if( !is_null( $map ) && $map['fld_id'] != 0 ){
					$selected = $map['fld_id'];
				}

				while( $s = ria_mysql_fetch_assoc( $r ) ){
					$fld[$s['id']] = $s['name'];
				}
				
				asort($fld,SORT_REGULAR);

				$data['options'][] = array(
					'label'     => _('Champ :'),
					'mandatory' => true,
					'type'      => 'select',
					'name'      => 'fld-id',
					'selected'  => $selected,
					'value'     => $fld
					
				);
				break;
			case 'PRD_CAT':
			case 'PRD_COLISAGE':
			case 'PRD_CANONICAL_LINK_PRD':
			case 'PRD_CANONICAL_LINK_CAT':
			case 'PRC_PRD_ID':
			case 'USR_SELLER':
			case 'USR_RELATION':
			case 'USR_HIEARCH_PARENT':
			case 'USR_HIEARCH_ENFANT':
			case 'USR_RELATION_PARENT':
			case 'USR_RELATION_ENFANT':
			case 'ORD_USR':
			case 'ORD_PRD_ID':
			case 'ORD_SELLER':
			case 'PRC_USR':
				break;	
			default :
				$data['options'][] = array(
					'label'     => _('Type de relation :'),
					'mandatory' => true,
					'type'      => 'select',
					'name'      => 'rel-type',
					'selected'  => $selected,
					'value'     => $val
				);
				break;
		}
		if( !in_array($schema['code'], array('FLD', 'PRD_COLISAGE')) ){

			if( !is_null( $map ) && $map['rel_id_type'] ){
				$selected = $map['rel_id_type'];
			}
			$data['options'][] = array(
				'label'     => _('Type d\'identifiant en relation :'),
				'mandatory' => true,
				'type'      => 'select',
				'name'      => 'rel-id',
				'selected'  => $selected,
				'value'     => displaySelectOptions( 'idType', $schema['code'] ),
				'separator'	=> ($schema['code']=='PRD_CANONICAL_LINK_CAT'? $map['separator']:'')
			);
		}


		if( $schema['code'] == 'PRD_CAT' ){
			$rCat = prd_categories_get();
			if( $rCat ){
				$selected = $map['cat'];
				$select = array(
					0 => ""
				);
				while( $row = ria_mysql_fetch_assoc( $rCat ) ){
					$select[$row['id']] = $row['name'];
				}
				$data['options'][] = array(
					'label'     => _('Catégorie de premier niveau :'),
					'mandatory' => false,
					'type'      => 'select',
					'selected'  => $selected,
					'name'      => 'cat-first',
					'value'     => $select
				);
			}
		}
	}

	if( $schema['rel_many_obj'] ){
		if(!$backup){
			if($schema['code']=='USR_PAYMENT'){
				$name='sepPayment';
			}else{
				$name='sep';
			}
			$data['options'][] = array(
				'label'     => _('Séparateur :'),
				'mandatory' => false,
				'type'      => 'text',
				'name'      => $name,
				'value'     => ( ( !is_null( $map ) && $map['separator'] ) ? $map['separator'] : '' )
			);
		}	
	}

	if( $schema['require_vals'] ){
		$vals = false;

		if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
			$vals = json_decode( $map['vals'], true );
		}else{
			$match = $schema['code'];
			if( $schema['data_type'] == 'bool' ){
				$match = 'bool';
			}
			switch( $match ){
				case 'bool' :
					$vals = array(
						'bool' => array( '' )
					);
					break;
				case 'PRD_LANG':
					$langs = getLangArray();
					foreach( $langs as $lng => $txt ){
						if( $txt === "" ){
							continue;
						}
						$vals[$lng] = '';
					}
					break;
			}
		}
		
		$mandatory = true;

		if( is_array($vals) ){
			foreach( $vals as $key => $value ){
				$data['options'][] = array(
					'label'        => ipt_mapping_get_label_by_key( $key ),
					'mandatory'    => $mandatory,
					'require_vals' => true,
					'type'         => 'text',
					'name'         => 'vals,'.$key,
					'value'        => is_array($value) ? implode( ', ', $value ) : $value
				);
				$mandatory = false;
			}
		}
	}

	if( $schema['is_unit'] ){
		$data['options'][] = array(
			'label'     => _('Unité'),
			'mandatory' => true,
			'name'      => 'unit',
			'type'      => 'select',
			'selected'  => ( isset( $map['unit'] ) ? $map['unit'] : '' ),
			'value'     => array_merge( array( 0 => '' ), ria_unites_get( $schema['unit_type'] ) )
		);
	}

	if( $schema['code'] == 'PRD_PRICE' ){
		$vals = json_decode($map['vals'], true);
		$tarif = array(''=>'', 'ttc'=>'TTC', 'ht'=> 'HT');
		$data['options'][] = array(
			'label'     => _('Tarif'),
			'mandatory' => false,
			'name'      => 'tarif',
			'type'      => 'select',
			'selected'  => ( isset( $vals['tarif'] ) ? $vals['tarif'] : 'ht' ),
			'value'     => $tarif
		);
		$rCat = prd_prices_categories_get();

		$selected = array(0=>"");

		if( $rCat && ria_mysql_num_rows($rCat) ){
			while( $cat = ria_mysql_fetch_assoc($rCat) ){
				$selected[$cat['id']] = $cat['name'];
			}
			$data['options'][] = array(
				'label'     => _('Catégorie tarifaire'),
				'mandatory' => false,
				'name'      => 'gu_catf',
				'type'      => 'select',
				'selected'  => ( isset( $vals['gu_catf'] ) ? $vals['gu_catf'] : ''),
				'value'     => $selected
			);
		}

		if( $config['USER_RIASTUDIO'] ){
			$data['options'][] = array(
				'label'	=> _('Prix en promotion'),
				'mandatory' => false,
				'name' => 'promo',
				'type' => 'checkbox',
				'value' => ( isset( $vals['promo']) ? 1 : 0 )
				);

			$data['options'][] = array(
				'label'	=> _('Inclure les promotions à 0 €'),
				'mandatory' => false,
				'name' => 'include-promo',
				'type' => 'checkbox',
				'value' => ( isset( $vals['include-promo']) ? 1 : 0 ),
				'show'	=> ( isset( $vals['promo']) ? 1 : 0 ),
				'title'	=> _('En cochant cette option, les tarifs promotionnels à zéro euro seront aussi importés')
				);
		}
	}

	if( $schema['code'] == 'PRD_STOCK' ){
		$rCat = prd_deposits_get();

		$selected = array(0=>"");

		if( $rCat && ria_mysql_num_rows($rCat) ){
			while( $cat = ria_mysql_fetch_assoc($rCat) ){
				$selected[$cat['id']] = $cat['name'];
			}
			$data['options'][] = array(
				'label'     => _('Dépots'),
				'mandatory' => false,
				'name'      => 'prd_dps',
				'type'      => 'select',
				'selected'  => ( isset( $map['vals'] ) ? $map['vals'] : '' ),
				'value'     => $selected
			);
		}
	}
	
	switch( $schema['code'] ){
		case'PRC_DISCOUNT_TYPE':
			if(!$backup){
				$fld = array(0=>_("Choisir un type de remise"));
				$r = prc_types_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove=true;
				$map_type = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$map_type = json_decode($map['vals']);
					$remove=false;
				}
					
				$data['options'][] = array(
							'label'     => _('Type de remise'),
							'mandatory' => true,
							'type'      => 'label',
							'name'      => 'PRC_DISCOUNT_TYPE'
						);

				foreach($map_type as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Type de remise'),
						'mandatory' => true,
						'type'      => 'select_input',
						'name'      => 'PRC_DISCOUNT_TYPE',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Type de remise'),
					'mandatory' => true,
					'type'      => 'href',
					'name'      => 'PRC_DISCOUNT_TYPE',
					'remove'	=> $remove
				);
			}
			break;
		case 'USR_ADR_TYPE_ID':
			if(!$backup){
				$fld = array(0=>_("Choisir un type d'adresse"));
				$r = gu_adr_types_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove=true;
				$map_adr_type = array(0 => '');		
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_adr_type = json_decode($map['vals']);
				}

				$data['options'][] = array(
								'label'     => _('Type d\'adresse'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'usr_adr_type'
							);

				foreach($map_adr_type as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Type d\'adresse'),
						'mandatory' => true,
						'type'      => 'select_input',
						'name'      => 'usr_adr_type',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Type d\'adresse'),
					'mandatory' => true,
					'type'      => 'href',
					'name'      => 'usr_adr_type',
					'remove'	=> $remove
				);
			}
			break;
		case 'USR_CIV':
			if(!$backup){
				$fld = array(0=>_("Choisir une civilité"));
				$r = gu_titles_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);
				$fld['other'] = _("Autre");

				$remove = true;
				$map_civ = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_civ = json_decode($map['vals']);
				}
					
				$data['options'][] = array(
							'label'     => _('Civilité'),
							'mandatory' => true,
							'type'      => 'label',
							'name'      => 'usr_civ'
						);

				foreach($map_civ as $id => $vals)
				{	
					$data['options'][] = array(
						'label'     => _('Civilité'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'usr_civ',
						'selected'  => ($id==4? 'other':$id),
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Civilité'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'usr_civ',
					'remove'	=> $remove
				);
			}
			break;
		case 'USR_PRF_ID':
			if(!$backup){
				$fld = array(0 => _("Choisir un droit d'accès"));
				$r = gu_profiles_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						if($s['id'] == 1){//bloque l'import de compte administrateur
							continue;
						}
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);
				$fld['other'] = _("Autre");

				$remove=true;
				$map_prf = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_prf = json_decode($map['vals']);
				}

				$data['options'][] = array(
					'label'     => _('Droits d\'accès'),
					'mandatory' => true,
					'type'      => 'label',
					'name'      => 'usr_prf_id'
				);

				foreach($map_prf as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Droits d\'accès'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'usr_prf_id',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Droits d\'accès'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'usr_prf_id',
					'remove' 	=> $remove
				);
			}
			break;
		case 'USR_PAYMENT':	
			if(!$backup){
				$fld = array(0 => _("Choisir un moyen de paiement"));
				$r = ord_payment_types_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);
				$fld['other'] = "Autre";

				$remove=true;
				$map_payment = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_payment = json_decode($map['vals']);
				}

				$data['options'][] = array(
								'label'     => _('Moyen de paiement'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'usr_payment'
							);

				foreach($map_payment as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Moyen de paiement'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'usr_payment',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Moyen de paiement'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'usr_payment',
					'remove' 	=> $remove
				);
			}
			break;
		case 'USR_CAT_ID':
			if(!$backup){
				$fld = array(0 => _("Choisir une categorie comptable"));
				$r = gu_accounting_categories_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);
				$fld['other'] = "Autre";

				$remove=true;
				$map_cat = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != ""){
					$remove=false;
					$map_cat = json_decode($map['vals']);
				}

				$data['options'][] = array(
							'label'     => _('Catégorie comptable'),
							'mandatory' => true,
							'type'      => 'label',
							'name'      => 'usr_cat_id'
						);

				foreach($map_cat as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Catégorie comptable'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'usr_cat_id',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Catégorie comptable'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'usr_cat_id',
					'remove'	=> $remove
				);
			}
			break;
		case 'USR_PRC_ID':
			if(!$backup){
				$fld = array(0=>_("Choisir une categorie tarifaire"));
				$r = prd_prices_categories_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);
				$fld['other'] = "Autre";

				$remove=true;
				$map_prc = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != ""){
					$remove=false;
					$map_prc = json_decode($map['vals']);
				}

				$data['options'][] = array(
							'label'     => _('Catégorie tarifaire'),
							'mandatory' => true,
							'type'      => 'label',
							'name'      => 'usr_prc_id'
						);

				foreach($map_prc as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Catégorie tarifaire'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'usr_prc_id',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Catégorie tarifaire'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'usr_prc_id',
					'remove'	=> $remove
				);
			}
			break;
		case 'PRC_USR_PRC':
			if(!$backup){
				$fld = array(0=>_("Choisir une categorie tarifaire"));
				$r = prd_prices_categories_get();
				if( $r ){
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove=true;
				$map_prc = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != ""){
					$remove=false;
					$map_prc = json_decode($map['vals']);
				}

				$data['options'][] = array(
							'label'     => _('Catégorie tarifaire'),
							'mandatory' => false,
							'type'      => 'label',
							'name'      => 'prc_usr_prc'
						);

				foreach($map_prc as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Catégorie tarifaire'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'prc_usr_prc',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}
	
				$data['options'][] = array(
					'label'     => _('Catégorie tarifaire'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'prc_usr_prc',
					'remove'	=> $remove
				);
			}
			break;
		case 'ORD_STATE':
			if(!$backup){
				$fld = array(0=>_("Choisir un état"));
				$r = ord_states_get( );
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove = true;
				$map_state = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_state = json_decode($map['vals']);
				}
					
				$data['options'][] = array(
							'label'     => _('Etat'),
							'mandatory' => false,
							'type'      => 'label',
							'name'      => 'ord_state'
						);

				foreach($map_state as $id => $vals)
				{	
					$data['options'][] = array(
						'label'     => _('Etat'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'ord_state',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Etat'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'ord_state',
					'remove'	=> $remove
				);
			}
			break;
		case 'ORD_SRV':
			if(!$backup){
				$fld = array(0=>_("Choisir un service"));
				$r = dlv_services_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove = true;
				$map_srv = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_srv = json_decode($map['vals']);
				}
					
				$data['options'][] = array(
							'label'     => _('Service'),
							'mandatory' => false,
							'type'      => 'label',
							'name'      => 'ord_srv'
						);

				foreach($map_srv as $id => $vals)
				{	
					$data['options'][] = array(
						'label'     => _('Service'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'ord_srv',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Service'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'ord_srv',
					'remove'	=> $remove
				);
			}
			break;
		case 'ORD_PAY':
			if(!$backup){
				$fld = array(0 => _("Choisir un moyen de paiement"));
				$r = ord_payment_types_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove=true;
				$map_payment = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_payment = json_decode($map['vals']);
				}

				$data['options'][] = array(
								'label'     => _('Moyen de paiement'),
								'mandatory' => false,
								'type'      => 'label',
								'name'      => 'ord_pay'
							);

				foreach($map_payment as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Moyen de paiement'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'ord_pay',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Moyen de paiement'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'ord_pay',
					'remove' 	=> $remove
				);
			}
			break;
		case 'ORD_CARD':
			if(!$backup){
				$fld = array(0 => _("Choisir un type de CB"));
				$r = ord_card_types_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove=true;
				$map_payment = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_payment = json_decode($map['vals']);
				}

				$data['options'][] = array(
								'label'     => _('Type de CB'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'ord_card'
							);

				foreach($map_payment as $id => $vals)
				{
					$data['options'][] = array(
						'label'     => _('Type de CB'),
						'mandatory' => true,
						'type'      => 'select_input',
						'name'      => 'ord_card',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Type de CB'),
					'mandatory' => true,
					'type'      => 'href',
					'name'      => 'ord_card',
					'remove' 	=> $remove
				);
			}
			break;
		case 'ORD_WST_ID':
			if(!$backup){
				$fld = array(0=>_("Choisir un site"));
				$r = wst_websites_get();
				if ($r) {
					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}
				}
				asort($fld);

				$remove = true;
				$map_srv = array(0 => '');
				if(isset($map['vals']) && $map['vals'] != "" ){
					$remove=false;
					$map_srv = json_decode($map['vals']);
				}

				$data['options'][] = array(
							'label'     => _('Site'),
							'mandatory' => false,
							'type'      => 'label',
							'name'      => 'ord_wst_id'
						);

				foreach($map_srv as $id => $vals)
				{	
					$data['options'][] = array(
						'label'     => _('Site'),
						'mandatory' => false,
						'type'      => 'select_input',
						'name'      => 'ord_wst_id',
						'selected'  => $id,
						'value'     => $fld,
						'valueInput' => $vals
					);
				}

				$data['options'][] = array(
					'label'     => _('Site'),
					'mandatory' => false,
					'type'      => 'href',
					'name'      => 'ord_wst_id',
					'remove'	=> $remove
				);
			}
			break;

			
	}

	if( in_array( $schema['code'], array( 'USR_ADR_TYPE_ID', 'USR_ADDRESS1', 'USR_ADDRESS2', 'USR_ZIP_CODE', 'USR_CITY', 'USR_COUNTRY', 'USR_LATITUDE', 'USR_LONGITUDE', 'USR_PHONE', 'USR_FAX',
	 		'USR_CELLPHONE', 'USR_PHONE_WORK', 'USR_SOCIETY', 'USR_FIRSTNAME', 'USR_LASTNAME', 'USR_CIV', 'USR_ADR_REF' )) ){
		
		if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
			$vals = json_decode( $map['vals'], true );
		}

		$data['options'][] = array(
			'label'	=> _('L\'adresse de facturation'),
			'mandatory' => false,
			'name' => 'inv_adr',
			'type' => 'checkbox',
			'value' => 0,
		);

		$data['options'][] = array(
			'label'	=> _('L\'adresse de livraison'),
			'mandatory' => false,
			'name' => 'dlv_adr',
			'type' => 'checkbox',
			'value' => 0,
		);
	}

	echo json_encode( $data );
	exit;
}

if( isset($_POST['schemas']) ){
	$dups = array();
	$comp = $_POST['schemas'];
	foreach($_POST['schemas'] as $k => $c){

		// Ne prend pas en compte l'option par défaut "Sélectionner une correspondance"
		if( $c == 'DEFAULT' ){
			continue;
		}

		if( in_array($c, ipt_multiple_cols()) ){
			continue;
		}
		unset($comp[$k]);
		if( in_array($c, $comp) ){
			$dups[$k] = $c;
		}
		$comp[$k] = $c;
	}
	echo json_encode($dups, JSON_FORCE_OBJECT );
}

if(isset($_GET['default'])){
	$rMapping = ipt_mapping_get( $_GET['imp_id'], $_GET['map'] );
	if(!$rMapping ){
		echo '0';
	}else{
		$map = ria_mysql_fetch_assoc( $rMapping );
		echo $map['vals'];
	}

}

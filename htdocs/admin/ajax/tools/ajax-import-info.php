<?php
	/**	\file ajax-import-info.php
	 * 	Cette page est utilisée pour récupérer l'état d'un import de données. Il s'agit uniquement de consultation,
	 * 	il n'y a pas de mise à jour.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'importer des données
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

if (!isset($_GET['imp'], $_GET['action']) || !is_numeric($_GET['imp'])) {
	exit;
}
$imp_id = $_GET['imp'];

require_once 'imports.inc.php';

$success = false;
$content = array();
switch($_GET['action']) {
	case 'get-state' :
		$r_report = ipt_reports_get(0, $imp_id);
		if( $r_report && ria_mysql_num_rows($r_report)) {
			$rep = ria_mysql_fetch_assoc($r_report);
			$state = ipt_reports_get_state($rep['id']);
		}else{
			$state = ipt_imports_get_state($imp_id);
		}
		if ($state) {
			$content['state'] = $state;
			$content['className'] = ipt_imports_state_css_class($state);
			$content['label'] = ipt_state_display($state);
			$success = true;
		}
		break;
}

header('Content-Type: application/json');
echo json_encode(array(
	'success' => $success,
	'content' => $content
));
exit;
<?php

	/**	\file ajax-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'une catégorie de questions de la FAQ.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours à le droit de mettre à jour la position d'un document
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('tools.faq.inc.php');

	$response = array('success' => faq_categories_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;


<?php
	
	/**	\file ajax-search.php
	 *
	 * 	Gestion des requêtes Ajax pour la partie recherche de produits sur les comparateurs de prix
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');
	require_once('view.admin.inc.php');
	
	$unique = array( 'CTR_SEARCH_PUBLISH', 'CTR_SEARCH_STOCK', 'CTR_SEARCH_EXPORT', 'CTR_SEARCH_DATE_FIRST', 'CTR_SEARCH_DATE_END' );
	$date1 = isset($_POST['date1']) && isdate($_POST['date1']) ? dateparse($_POST['date1']) : false;
	$date2 = isset($_POST['date2']) && isdate($_POST['date2']) ? dateparse($_POST['date2']) : false;
	
	$marketplace_exclude = array('CTR_SEARCH_COSTSALES', 'CTR_SEARCH_COST', 'CTR_SEARCH_CLICKS');
	
	$res = array();
	$res['type'] = 0;
	$res['data'] = array();
	$res['message'] = _("Une erreur inattendue s'est produite.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_POST['wst-id']) && is_numeric($_POST['wst-id']) && $_POST['wst-id'] ){
		$_SESSION['websitepicker'] = $_POST['wst-id'];
	}

	if( isset($_POST['reload-filter']) ) {
		
		$rflt = ctr_filters_get();
		if( $rflt ){
			$res['type'] = 1;
			$res['message'] = '';
			$res['data'] = array();
			
			while( $flt = ria_mysql_fetch_array($rflt) ){
				$res['data'][] = array(
					'id' => $flt['id'],
					'name' => $flt['name'],
					'desc' => $flt['comment']
				);
			}
		}
	
	} elseif( isset($_POST['upd-date']) ) {
		view_date_in_session( $date1, $date2 );
	} elseif( isset($_POST['upd-cdts']) ) {
		
		$exclude = array();
		if( isset($_POST['cdts']) ){
			foreach( $_POST['cdts'] as $cdt=>$val ){
				if( in_array($cdt, $unique) )
					$exclude[] = $cdt;
			}
		}
		
		$cdts = ctr_conditions_get( 0, '', $exclude );
		if( $cdts && ria_mysql_num_rows($cdts) ){
			while( $cdt = ria_mysql_fetch_array($cdts) ){
				if (in_array($cdt['code'], $marketplace_exclude) && $_POST['marketplace']) continue;
				
				$res['data'][] = array(
					'code' => $cdt['code'],
					'name' => $cdt['name'],
					'desc' => str_replace( '%CTRNAME%', $_POST['ctrname'], $cdt['desc'] )
				);
			}
		}
		
		$res['type'] = 1;
		$res['message'] = '';
	
	} elseif( isset($_POST['load-cdt']) ) {
		if( isset($_POST['refresh']) ) $_POST['conditions'] = $_POST['cdt'];
		$val = false;
		$sbl = false;
		if( isset($_POST['cdts'][$_POST['conditions'].'-'.$_POST['count']]) ){
			$val = $_POST['cdts'][ $_POST['conditions'].'-'.$_POST['count'] ];
		}
		if( isset($_POST['sbl'][$_POST['conditions'].'-'.$_POST['count']]) ){
			$sbl = $_POST['sbl'][ $_POST['conditions'].'-'.$_POST['count'] ];
		}
		
		$res['type'] = 1;
		$res['data'] = view_ctr_conditions( $_POST['count'], $_POST['conditions'], $val, $sbl );
		$res['message'] = '';
		
		$res['cdts'] = $_POST['cdt'];
	} elseif( isset($_POST['save-search']) ){
		
		if( !isset($_POST['sh-title']) || !isset($_POST['sh-desc']) ){
			$res['type'] = 0;
			$res['message'] = _('Une ou plusieurs informations sont manquantes.');
		} elseif( isset($_POST['sh-title']) && !trim($_POST['sh-title']) ){
			$res['type'] = 0;
			$res['message'] = _('Veuillez préciser un titre pour cette recherche.');
		} else {
			
			$ar_conditions = array();
			foreach( $_POST['cdts'] as $cdt=> $val ){
				$end = strpos($cdt, '-');
				$code = $end ? substr( $cdt, 0, $end ) : $cdt;
				$id = ctr_conditions_get_id_bycode( $code );
				$sbl = isset($_POST['sbl'][$cdt]) ? $_POST['sbl'][$cdt] : '=';
				
				$ar_conditions[] = array(
					'id' => $id,
					'symbol' => $sbl,
					'value' => $val
				);
			}
			
			if( !ctr_filters_add( $_POST['sh-title'], $_POST['sh-desc'], $ar_conditions, isset($_POST['marketplace']) ? $_POST['marketplace'] : false) ){
				$res['type'] = 0;
				$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre recherche.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			} else {
				$res['type'] = 1;
				$res['message'] = '';
			}
			
		}
	} elseif( isset($_POST['load-filter'], $_POST['filter']) ) {
		
		// récupère les conditions pour ce filtre
		$rcdt = ctr_filters_conditions_get( $_POST['filter'] );
		if( $rcdt && ria_mysql_num_rows($rcdt) ){
			$res['type'] = 1;
			$res['message']  = '';
			$count = 1;
			$res['data'] = '';
			while( $cdt = ria_mysql_fetch_array($rcdt) ){
				$res['data'] .= view_ctr_conditions( $count, $cdt['code'], $cdt['value'], $cdt['symbol'] );
				$count++;
			}
		} elseif( $_POST['filter']!='-1' ) {
			$res['type'] = 0;
			$res['message'] = _("Une erreur inattendue s'est produite lors du chargement de la recherche.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		} else {
			$res['type'] = 1;
			$res['message'] = '';
			$res['data'] = '';
		}
		
	} elseif( isset($_POST['load-new']) ){
		
		$data = view_ctr_conditions( $_POST['count'], $_POST['conditions'], false, false, false );
		if( trim($data) ){
			$res['type'] = 1;
			$res['message'] = '';
			$res['data'] = $data;
		}
		
	} elseif( isset($_POST['searchprd']) ){
		
		if( isset($_POST['cdts']) && is_array($_POST['cdts']) && sizeof($_POST['cdts']) ){
			$ar_conditions = array();
			
			foreach( $_POST['cdts'] as $key => $val ){
				
				$symbol = isset($_POST['sbl'][ $key ]) ? $_POST['sbl'][ $key ] : '=';
				
				$end = strpos( $key, '-' );
				$cdt = $end ? substr( $key, 0, $end ) : $key;
				
				$ar_conditions[] = array(
					'code' => $cdt,
					'symbol' => $symbol,
					'value' => $val
				);
			}
			
			$res['nb_result'] = 0;
			
			$marketplace = isset($_POST['marketplace']) && $_POST['marketplace']=='true' ? true : false;
			$rsearch = ctr_search_products( $_POST['ctrid'], 0, $date1, $date2, $ar_conditions, $marketplace );
			if( is_array($rsearch) && sizeof($rsearch) ){
				$res['nb_result'] = sizeof($rsearch);
				
				$_POST['page'] = isset($_POST['page']) && is_numeric($_POST['page']) && $_POST['page']>0 ? $_POST['page'] : 1;
				
				$i = ($_POST['page']-1)*75;
				while( true ){
					if( !isset($rsearch[$i]) ) break;
					$r = $rsearch[$i];
					
					$desc = html_strip_tags( $r['desc'] );
					$desc = mb_strlen( $desc, 'UTF-8' )>255 ? mb_substr( $desc, 0, 253, 'UTF-8' ).'...' : $desc;
					
					$thumbs = $config['img_sizes']['small'];
					// identifiant de l'image produit
					if( !$r['img_id'] ){
						$rimg = prd_images_get( $r['id'] );
						if( $rimg && ria_mysql_num_rows($rimg) ){
							$r['img_id'] = ria_mysql_result( $rimg, 0, 'id' );
						}
					}
					
					$imgsrc = $config['img_url'].'/'.$thumbs['dir'].'/'.( $r['img_id'] ? $r['img_id'] : 'default' ).'.'.$thumbs['format'];
					
					$title = trim($r['title']) ? $r['title'] : $r['name'];
					
					$ahref = htmlspecialchars( $title );
					$rcat = prd_products_categories_get( $r['id'] );
					if( $rcat && ria_mysql_num_rows($rcat) ){
						$ahref = '<a id="a-'.$r['id'].'" target="_blank" href="/admin/catalog/product.php?cat='.ria_mysql_result( $rcat, 0, 'cat' ).'&amp;prd='.$r['id'].'&tab='.( $marketplace ? 'marketplace' : 'comparators' ).'">'.$ahref.'</a>';
					}
					
					$res['data'][] = array(
						'id' => $r['id'],
						'ref' => $r['ref'],
						'is_sync' => $r['is_sync'],
						'name' => $ahref,
						'title' => $title,
						'desc' => trim($desc) ? $desc : '',
						'click' => number_format( $r['click'], 0, ',', ' ' ),
						'cost' => number_format( $r['cost'], 2, ',', ' ' ),
						'sales' => number_format( $r['sales'], 0, ',', ' ' ),
						'costsales' => $r['sales']>0 ? number_format( ($r['cost']/$r['sales']), 2, ',', ' ' ) : '0,00',
						'ca' => number_format( $r['ca'], 2, ',', ' ' ),
						'roi' => number_format( $r['roi'], 2, ',', ' ' ),
						'ca_ttc' => number_format( $r['ca_ttc'], 2, ',', ' ' ),
						'margin' => number_format( $r['margin'], 2, ',', ' ' ),
						'img' => '<img src="'.$imgsrc.'" height="'.$thumbs['height'].'" width="'.$thumbs['width'].'" title="'.htmlspecialchars( $title ).'" alt="'.htmlspecialchars( $title ).'" />',
						'export' => $r['export'],
						'transfo' => number_format( $r['transfo'], 2, ',', ' ' )
					);
					
					$i++;
				}
				
				$res['type'] = 1;
				$res['message'] = '';
			} else {
				$res['type'] = 1;
				$res['message'] = _("Aucun produit ne correspond à vos critères de recherche.");
			}
		} else {
				$res['type'] = 1;
				$res['message'] = _("Aucun produit ne correspond à vos critères de recherche.");
		}
	} elseif( isset($_POST['exportprd']) ){
		$error = false;
		
		$ar_ctr = array();
		if( $_POST['ctrid']>0 )
			$ar_ctr[] = $_POST['ctrid'];
		else {
			$rctr = ctr_comparators_get( 0, true, false, $_POST['marketplace']==1 );
			if( $rctr ){
				while( $ctr = ria_mysql_fetch_array($rctr) ){
					$ar_ctr[] = $ctr['id'];
				}
			}
		}
		
		if( isset($_POST['prds']) ){
			foreach( $ar_ctr as $ctr ){
				foreach( $_POST['prds'] as $prd ){
					if( !ctr_catalogs_activated($ctr, $prd) ){
						$error = true;
					}
				}
			}
		}
		
		if( $error ){
			$res['type'] = 0;
			$res['message'] = _("Une erreur inattendue s'est produite lors de l'activation de l'export des produits sélectionnés.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		} else {
			$res['type'] = 1;
			$res['message'] = '';
		}
	} elseif( isset($_POST['noexportprd']) ){
		$error = false;
		
		$ar_ctr = array();
		if( $_POST['ctrid']>0 )
			$ar_ctr[] = $_POST['ctrid'];
		else {
			$rctr = ctr_comparators_get( 0, true, false, $_POST['marketplace']==1 );
			if( $rctr ){
				while( $ctr = ria_mysql_fetch_array($rctr) ){
					$ar_ctr[] = $ctr['id'];
				}
			}
		}
		
		if( isset($_POST['prds']) ){
			foreach( $ar_ctr as $ctr ){
				foreach( $_POST['prds'] as $prd ){
					if( !ctr_catalogs_unactivated($ctr, $prd) ){
						$error = true;
					}
				}
			}
		}
		
		if( $error ){
			$res['type'] = 0;
			$res['message'] = _("Une erreur inattendue s'est produite lors de la désactivation de l'export des produits sélectionnés.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		} else {
			$res['type'] = 1;
			$res['message'] = '';
		}
	}
	
	// affiche le résulats Ajax
	header('Content-type: text/json');
	header('Content-type: application/json');
	
	print json_encode( $res );
	exit;
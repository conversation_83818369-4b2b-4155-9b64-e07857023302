<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	
	if( isset($_GET['term'], $_GET['ctr']) && mb_strlen($_GET['term'], 'UTF-8')>1 ){
	
		$rcat = ctr_categories_get( $_GET['ctr'], 0, '', 0, false, $_GET['term'] );
		
		$result = array();
		if( $rcat && ria_mysql_num_rows($rcat) ){
			$count = 0;
			while( $cat = ria_mysql_fetch_array($rcat) ){
				if( ($count++)>50 ) break;
				// catégorie parent
				$sep = '';
				$result[] = $sep.$cat['name'].' |'.$cat['parent'].'|'.$cat['id'];
			}
		}
		print json_encode($result);
	}


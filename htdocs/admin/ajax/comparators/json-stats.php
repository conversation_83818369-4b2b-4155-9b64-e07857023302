<?php

	/**	\file json-stats.php
	 * 	
	 *	Ce script fourni aux écrans de statistiques sur les comparateurs de prix et places de marché les statistiques
	 *	dont ils ont besoin pour fonctionner.
	 *
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	require_once('stats.inc.php');

	// Vérifie qu'il s'agit bien d'une requête Ajax. En cas de requête HTTP classique, redirige le navigateur vers la version adaptée
	if( empty($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
		$param = array('ctr='.$_GET['ctr']);
		if( isset($_GET['parent']) ) $param[] = 'parent='.$_GET['parent'];
		if( isset($_GET['date1']) ) $param[] = 'date1='.$_GET['date1'];
		if( isset($_GET['date2']) ) $param[] = 'date2='.$_GET['date2'];
		if( isset($_GET['marketplace']) ) $param[] = 'marketplace='.$_GET['marketplace'];
		header('Location: /admin/comparators/stats/index.php?'.implode('&',$param));
		exit;
	}

	// Extrait la période choisie depuis les paramètres reçus
	$date1 = isset($_GET['date1']) && isdate($_GET['date1']) ? dateparse($_GET['date1']) : false;
	$date2 = isset($_GET['date2']) && isdate($_GET['date2']) ? dateparse($_GET['date2']) : false;
	view_date_in_session( $date1, $date2 );

	$return = array();
	if( !isset($_GET['parent']) ) $_GET['parent'] = 0;

	// Charge les statistiques globales pour la période sélectionnée
	$stats = stats_comparators_get($_GET['ctr'], $_GET['parent'], 0, $date1, $date2);

	// Prépare le tableau de données qui sera retourné au script appellant
	// Ici, les statistiques globales (ligne Total du tableau)
	$return['globals'] = array(
		'clicks' => ria_number_format($stats['clicks']),
		'cost' => ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2),
		'sales' => ria_number_format($stats['sales']),
		'ca' => ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2),
		'ca_ttc' => ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2),
		'transfo' => ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2),
		'cost_sales' => ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2),
		'margin' => ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2),
		'roi' => ria_number_format($stats['roi'], NumberFormatter::PERCENT, 2)
	);

	// Charge les statistiques globales pour la période sélectionnée
	$stats = stats_comparators_get( $_GET['ctr'], $_GET['parent'], 0, $date1, $date2, false );

	// Prépare le tableau de données qui sera retourné au script appellant
	// Ici, les statistiques globales (ligne Total du tableau des produits)
	$return['globalsprd'] = array(
		'clicks' => ria_number_format($stats['clicks']),
		'cost' => ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2),
		'sales' => ria_number_format($stats['sales']),
		'ca' => ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2),
		'ca_ttc' => ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2),
		'transfo' => ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2),
		'cost_sales' => ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2),
		'margin' => ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2),
		'roi' => ria_number_format($stats['roi'], NumberFormatter::PERCENT, 2)
	);

	$return['cats'] = array();
	$return['prds'] = array();

	// Statistiques pour les catégories enfants
	$return['use_title'] = false;
	$rcat = prd_categories_get( 0, false, $_GET['parent'] );
	if( $_GET['parent']>0 && (!$rcat || !ria_mysql_num_rows($rcat)) ){
		$rcat = prd_categories_get( $_GET['parent'] );
		$return['use_title'] = true;
	}

	// Entête du tableau des stats (lien retour en arrière)
	$caption = '';
	if( $_GET['parent']<=0 ){
		$caption = _('Catalogue');
	}else {
		$name = '<a onclick="return updateStatsCtr(0);" href="index.php?ctr='.$_GET['ctr'].'&amp;parent=0">'._('Catalogue').'</a> &raquo; ';
		$last = false;
		$parents = prd_categories_parents_get($_GET['parent']);

		$cat = ria_mysql_fetch_array(prd_categories_get($_GET['parent']));
		while( $p = ria_mysql_fetch_array($parents) ){
			$name .= '<a onclick="return updateStatsCtr('.$p['id'].');" href="index.php?ctr='.$_GET['ctr'].'&amp;parent='.$p['id'].'">'.htmlspecialchars($p['title']).'</a> &raquo; ';
			$last = $p;
		}
		$caption = '<a onclick="return updateStatsCtr('.($last ? $last['id'] : 0).');" href="index.php?ctr='.$_GET['ctr'].'&amp;parent='.$last['id'].'">';
		$caption .= '<img style="border: medium none;" src="/admin/images/up.png" width="16" height="16" alt="'._('Remonter d\'un niveau').'" title="'._('Remonter d\'un niveau').'" />';
		$caption .= '</a> '.$name.htmlspecialchars($cat['title']);
	}
	$return['caption'] = $caption;

	// Pour chaque catégorie de produit, charge les statistiques du comparateur ou de la place de marché
	if( $rcat && ria_mysql_num_rows($rcat) ){
		while( $cat = ria_mysql_fetch_array($rcat) ){
			$stats = stats_comparators_get( $_GET['ctr'], $cat['id'], 0, $date1, $date2 );
			if( is_array($stats) && sizeof($stats) ){
				$return['cats'][] = array(
					'id' => $cat['id'],
					'name' => $cat['title'],
					'is_sync' => $cat['is_sync'],
					'clicks' => ria_number_format($stats['clicks']),
					'cost' => ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2),
					'sales' => ria_number_format($stats['sales']),
					'ca' => ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2),
					'ca_ttc' => ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2),
					'transfo' => ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2),
					'cost_sales' => ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2),
					'margin' =>  ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2),
					'roi' =>   ria_number_format($stats['roi'], NumberFormatter::PERCENT, 2),
				);
			}
		}
	}

	// Charge les statistiques pour les produits (seulement si la catégorie en contient)
	if( $_GET['parent']>0 ){

		$rprd = prd_products_get_simple( 0, '', false, $_GET['parent'], false, false, false, false, array('childs'=>true) );
		if( $rprd && ria_mysql_num_rows($rprd) ){

			while( $prd = ria_mysql_fetch_array($rprd) ){
				$stats = stats_comparators_get( $_GET['ctr'], 0, $prd['id'], $date1, $date2 );
				if( is_array($stats) && sizeof($stats) ){
					$return['prds'][] = array(
						'id' => $prd['id'],
						'name' => $prd['title'],
						'is_sync' => $prd['is_sync'],
						'clicks' => ria_number_format($stats['clicks']),
						'cost' => ria_number_format($stats['cost'], NumberFormatter::DECIMAL, 2),
						'sales' => ria_number_format($stats['sales']),
						'ca' => ria_number_format($stats['ca'], NumberFormatter::DECIMAL, 2),
						'ca_ttc' => ria_number_format($stats['ca-ttc'], NumberFormatter::DECIMAL, 2),
						'transfo' => ria_number_format($stats['transfo'], NumberFormatter::DECIMAL, 2),
						'cost_sales' => ria_number_format($stats['cost-sales'], NumberFormatter::DECIMAL, 2),
						'export' => ctr_catalogs_is_publish( $_GET['ctr'], $prd['id'] ) ? 1 : 0,
						'margin' =>  ria_number_format($stats['margin'], NumberFormatter::DECIMAL, 2),
						'roi' =>   ria_number_format($stats['roi'], NumberFormatter::DECIMAL, 2),
					);
				}
			}
		}

	}

	// Encode le tableau sous format JSON
	print json_encode( $return );

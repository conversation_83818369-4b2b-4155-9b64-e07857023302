<?php

	/**	\file ajax-comparators.php
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	
	if( isset($_GET['add-opt-group'], $_GET['code'], $_GET['name']) ){
		$params = ctr_catalogs_get_params( $_GET['ctr'], $_GET['prd'] );
		if( !isset($params['opt-grp']) ){
			$params['opt-grp'] = array();
		}
		
		if( !in_array($_GET['code'], $params['opt-grp']) ){
			$params['opt-grp'][ $_GET['code'] ] = $_GET['name'];
			if( ctr_catalogs_update_params($_GET['ctr'], $_GET['prd'], json_encode( $params )) ){
				print 'ok';
			}
		}else{
			print 'ok';
		}
		
		return;
	}
	
	if( isset($_GET['del-opt-group'], $_GET['code']) ){
		$params = ctr_catalogs_get_params( $_GET['ctr'], $_GET['prd'] );
		if( isset($params['opt-grp'][$_GET['code']]) ){
			unset($params['opt-grp'][$_GET['code']]);
			
			$opt_actived = array();
			if( isset($params['opt-grp']) && is_array($params['opt-grp']) ){
				$opt_actived = array_keys( $params['opt-grp'] );
			}
			
			if( ctr_catalogs_update_params($_GET['ctr'], $_GET['prd'], json_encode( $params )) ){
				$option = array(
					'weight' => _('Poids brut'), 
					'weight_net' => _('Poids net')
				);
				
				print '
					<option value=""></option>
					<optgroup label="'._('Informations sur le produit').'">
				';

				foreach( $option as $code=>$name ){
					if( in_array($code, $opt_actived) ){
						continue;
					}
					
					print '
						<option value="'.$code.'">'.htmlspecialchars( $name ).'</option>
					';
				}

				print '
									</optgroup>
				';

				$rmdl = fld_models_get( 0, 0, CLS_PRODUCT );
				if( $rmdl && ria_mysql_num_rows($rmdl) ){
					while( $mdl = ria_mysql_fetch_array($rmdl) ){
						$rfld = fld_fields_get( 0, 0, $mdl['id'] );
						
						if( !$rfld || !ria_mysql_num_rows($rfld) ){
							continue;
						}
						
						print '
							<optgroup label="'.htmlspecialchars( $mdl['name'] ).'">
						';
						
						while( $fld = ria_mysql_fetch_array($rfld) ){
							if( in_array($code, $opt_actived) ){
								continue;
							}
							
							print '
								<option value="'.$fld['id'].'">'.htmlspecialchars( $fld['name'] ).'</option>
							';
						}
						
						print '
							</optgroup>
						';
					}
				}
			}
		}
		
		return;
	}
	
	$html = '';
	if( isset($_GET['ctr'], $_GET['cat'], $_GET['prd']) ){
		// if( ctr_comparators_cat_fields_exists($_GET['ctr'], $_GET['cat']>0 ? $_GET['cat'] : 0) ){
			$params = ctr_catalogs_get_params( $_GET['ctr'], $_GET['prd'] );
			$ctr_name = ctr_comparators_get_name( $_GET['ctr'] );
			
			switch( $_GET['ctr'] ){
				case CTR_EBAY :
					require_once('comparators/ctr.ebay.inc.php');
					
					$ebay = new EBay();
					
					$multiSKU = null; $ref = ''; $fields = false;
					if( $_GET['cat']>0 ){
						$ref = ctr_categories_get_ref( CTR_EBAY, $_GET['cat'] );
						if( trim($ref)!='' ){
							$fields = $ebay->getCategorySpecifics( $ref );
							$multiSKU = $ebay->getAcceptedMultiSKU( $ref );
						}
					}
					
					print '
									<input type="hidden" name="ebay-cat" value="'.$ref.'" />
									<input type="hidden" name="ebay-multisku" value="'.$multiSKU.'" />
					';
					
					if( is_array($fields) && sizeof($fields) ){
						print '
									<p>'._('Vous trouverez ci-dessous toutes les informations pouvant être personnalisées pour le produit lors de son exportation vers eBay.').'</p>
									<hr />
						';
						
						$i = 0;
						foreach( $fields as $f ){
							$vals = isset($params[$f['name']]) ? explode( ';', $params[$f['name']] ) :  array();
							
							$show_other = false;
							if( isset($_POST['param-ebay'][$i]) ){
								$show_other = in_array('other', $_POST['param-ebay'][$i]) ? true : false;
							}elseif( isset($params[$f['name']]) ){
								$show_other = trim($params[$f['name']])!='' && !in_array($params[$f['name']], $f['values']) ? true  : false;
							}
							
							$msg = array();
							if( is_array($f['rules']['relations']) && sizeof($f['rules']['relations']) ){
								$tmp = '<span class="bold">';
								
								if( sizeof($f['rules']['relations'])>1 ){
									$tmp .= sprintf(_('Les champs "%s" doivent être renseignés.'), implode('", "', $f['rules']['relations']));
								}else{
									$tmp .= sprintf(_('Le champ "%s" doit être renseigné.'), implode('", "', $f['rules']['relations']));
								}
								
								$tmp .= '</span>';
								
								$msg[] = $tmp;
							}
							
							if( $f['mode']=='FreeText' && is_array($f['values']) && sizeof($f['values']) ){
								if( $f['rules']['maxlength']>0 ){
									if( $f['rules']['maxlength']>1 ){
										$msg[] = '<span class="opt-other" style="display: '.( $show_other ? 'block' : 'none' ).';">'.sprintf(_('En choisissant l\'option "Autre", vous avez la possibilité de renseigner jusqu\'à %d valeurs différentes séparées par un point-virgule, en tenant compte des valeurs déjà sélectionnées.</span>'), $f['rules']['maxlength']);
									}else{
										$msg[] = '<span class="opt-other" style="display: '.( $show_other ? 'block' : 'none' ).';">'._('En choisissant "Autre", la valeur saisie ne doit contenir aucun point-virgule.').'</span>';
									}
								}
							}
							
							print '
										<div class="elem">
											<input type="hidden" name="param-ebay-type['.$i.']" value="'.htmlspecialchars($f['name']).'" />
											<div class="label">
												<label for="param-ebay-'.$i.'">'.htmlspecialchars( $f['name'] ).'</label>
							';
							
							if( sizeof($msg) ){
								print '
												<sub>'.implode('<br />', $msg).'</sub>
								';
							}
							
							print '
											</div>
							';
							
							switch( $f['mode'] ){
								case 'FreeText':
									if( !is_array($f['values']) || !sizeof($f['values']) ){
										print '
											<input class="text" type="text" name="param-ebay['.$i.']" id="param-ebay-'.$i.'" value="" />
										';
										
										break;
									}
								case 'SelectionOnly' :
									$multiple = $f['rules']['maxlength']>1 ? 'multiple="multiple"' : '';
									$size = trim($multiple)!='' ? 'size="'.(ceil(sizeof($f['values'])/2)+2).'"' : '';
									
									print '
										<select name="param-ebay['.$i.'][]" id="param-ebay-'.$i.'" '.$multiple.' '.$size.'>
											<option value="">&nbsp;</option>
									';
									
									foreach( $f['values'] as $v ){
										$selected = '';
										if( isset($_POST['param-ebay'][$i]) ){
											$selected = in_array($v, $_POST['param-ebay'][$i]) ? 'selected="selected"' : '';
										}elseif( isset($params[$f['name']]) ){
											$selected = in_array($v, $vals) ? 'selected="selected"' : '';
										}
										
										print '
											<option '.$selected.' value="'.htmlspecialchars( $v ).'">'.htmlspecialchars( $v ).'</option>
										';
									}
									
									if( $f['mode']=='FreeText' ){
										$selected = $show_other ? 'selected="selected"' : '';
										
										print '
											<option '.$selected.' value="other">'._('Autre').'</option>
										';
									}
									
									print '
										</select>
									';
									
									if( $f['mode']=='FreeText' ){
										$other = '';
										if( isset($_POST['param-other-ebay'][$i]) ){
											$other = $_POST['param-other-ebay'][$i];
										}elseif( isset($params[$f['name']]) ){
											foreach( $vals as $v ){
												if( !in_array($v, $f['values']) ){
													$other .= (trim($other)!='' ? ';' : '' ).$v;
												}
											}
										}
										
										$class = trim($other)!='' ? 'show' : '';
										
										print '
											<div class="other '.$class.'">
												<input class="text" type="text" name="param-other-ebay['.$i.']" value="'.$other.'" />
											</div>
										';
									}
							}
							
							print '
											<div class="clear"></div>
										</div>
							';
							
							$i++;
						}
					}elseif( !$_GET['cat'] ){
						print '
									<p>'._('Vous devez choisir une catégorie afin de personnaliser l\'export du produit.').'</p>
						';
					}
					
					break;
				default :
					$have_fields = ctr_comparators_cat_fields_exists($_GET['ctr'], $_GET['cat'] ? $_GET['cat'] : 0);
					if( $have_fields ){
						$html .= '					<p>'.sprintf(_('Afin que nous puissions exporter votre produit vers %s, vous devez renseigner toutes les informations ci-dessous avec une <span class="mandatory">*</span>.'), $ctr_name).'</p>';
						$html .= '					<hr />';
						if( $_GET['cat'] ){
							$fields = ctr_cat_fields_get( $_GET['ctr'], $_GET['cat'] );
							if( $fields && ria_mysql_num_rows($fields) ){
								$count = 0;
								while( $field = ria_mysql_fetch_array($fields) ){
									$html .= '<div class="elem">';
									$html .= '<label for="attr-'.$_GET['ctr'].'-'.$count.'">'.$field['name'];
									$html .= '	'.( $field['mandatory'] ? '<span class="mandatory">*</span>' : '' ).' :';
									$html .= '	<br /><span class="desc">'.htmlspecialchars($field['desc']).'</span>';
									$html .= '</label>';
									
									$values = ctr_cat_field_values_get( $field['id'] );
									if( $values && ria_mysql_num_rows($values) ){
										$html .= '<select name="vals['.$_GET['ctr'].']['.$field['id'].']" id="attr-'.$_GET['ctr'].'-'.$count.'">';
										while( $val = ria_mysql_fetch_array($values) ){
											$selected = isset($params[$field['id']]) && $params[$field['id']]==$val['val'] ? 'selected="selected"' : '';
											$html .= '<option value="'.$val['id'].'" '.$selected.'>'.$val['val'].'</option>';
										}
										$html .= '</select>';
									} else {
										$html .= '<input class="text" type="text" name="vals['.$_GET['ctr'].']['.$field['id'].']" id="attr-'.$_GET['ctr'].'-'.$count.'" value="'.(isset($params[$field['id']]) ? $params[$field['id']] : '' ).'" />';
									}
									$html .= '<div class="clear"></div>';
									$html .= '</div>';
								}
							}
						} else {
							$html .= _('Vous devez choisir une catégorie afin de personnaliser l\'export du produit.');
						}
					}
					break;
			}
		// }
	}
	print $html;
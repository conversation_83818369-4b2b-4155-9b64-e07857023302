<?php

	/**	\file ajax-actions.php
	 * 
	 * 	Ce fichier permet l'intéraction en Ajax avec les fonctions du modèle des comparateurs de prix
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');
	
	$res = array();
	$res['type'] = 0;
	$res['message'] = _("Une erreur inattendue s'est produite.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	
	if( isset($_POST['useonlymdl']) ){
		$error = false; $data = '';

		if( !isset($_POST['ctr'], $_POST['mdl'], $_POST['prd']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes");
		}elseif( !ctr_models_comparators_apply($_POST['mdl'], $_POST['ctr']) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}else{
			$data = view_prd_export_data( $_POST['ctr'], $_POST['prd'] );
		}

		$res = array(
			'error' => $error,
			'data' => str_replace( '"', '\"', trim(preg_replace('/[\r\n\t]+/i', ' ', $data)) )
		);
	}elseif( isset($_POST['ctr'], $_POST['prd']) ){
		$marketplace = isset($_POST['marketplace']) ? $_POST['marketplace'] : null;
		
		$rctr = ctr_comparators_get( $_POST['ctr'], true, false, $marketplace );
		$_POST['ctr'] = array();
		if( $rctr && ria_mysql_num_rows($rctr) ){
			while( $ctr = ria_mysql_fetch_array($rctr) ){
				$_POST['ctr'][] = $ctr['id'];
			}
		} else {
			$res['type'] = 0;
			$res['message'] = _("Une erreur inattendue s'est produite.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			print json_encode( $res );
			exit;
		}
		
		foreach( $_POST['ctr'] as $ctr ){
			// désactivation de l'export d'un produit
			if( isset($_POST['unactivated']) ){
				if( ctr_catalogs_unactivated($ctr, $_POST['prd']) ){
					$res['type'] = 1;
					$res['message'] = "";
				} else {
					$res['type'] = 0;
					$res['message'] = _("Une erreur inattendue s'est produite lors de la désactivation de l'export du produit.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			
			// activation de l'export d'un produit
			if( isset($_POST['activated']) ){
				if( ctr_catalogs_activated($ctr, $_POST['prd']) ){
					$res['type'] = 1;
					$res['message'] = "";
				} else {
					$res['type'] = 0;
					$res['message'] = _("Une erreur inattendue s'est produite lors de l'activation de l'export du produit.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
		
		$_POST['ctr'] = $_POST['ctr'][0];
		// enregistre du titre et de la description personnalisée
		if( isset($_POST['save-info'], $_POST['title'], $_POST['desc']) ){
			$_POST['title'] = urldecode( $_POST['title'] );
			$_POST['desc'] = urldecode( $_POST['desc'] );
			$_POST['auctions'] = isset($_POST['auctions']) ? $_POST['auctions'] : '';
			
			$min = 0;
			// vérification du montant minimum
			if( ctr_comparators_auctions_used($_POST['ctr'], true) ){
				$cat = ctr_catalogs_get_categorie( $_POST['ctr'], $_POST['prd'], false );
				if( $cat ){
					$min = ctr_categories_get_min_auctions( $_POST['ctr'], $cat );
				}
			}
			
			$error = false;
			switch( $_POST['ctr'] ){
				case CTR_PRICEMINISTER :
					if( !isset($_POST['alias'], $_POST['attr']) || trim($_POST['alias'])=='-1' ){
						$error = true; $res['type'] = 0;
						$res['message'] = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.");
					} else {
						
						$ar_template = array( 'typeproduct'=>$_POST['alias'], 'template'=>array() );
						foreach( $_POST['attr'] as $key=>$val ){
							if( $_POST['mandatory'][$key] && in_array( trim($val), array('', '-1') ) ){
								$error = true;
								$res['type'] = 0;
								$res['message'] = sprintf(_("L'information \"%s\" pour PriceMinister est obligatoire."), $_POST['label'][$key]);
								break;
							}
							$ar_template['template'][ $key ] = $val;
						}
						
						if( !$error ){
							if( !ctr_catalogs_update_params( $_POST['ctr'], $_POST['prd'], json_encode( $ar_template )) ){
								$error = true;
								$res['type'] = 0;
								$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur PriceMinister.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
							}
						}
					}

					break;
				case CTR_CDISCOUNT :
					if( !isset($_POST['modelscdiscount'], $_POST['attr-cdiscount']) || trim($_POST['modelscdiscount'])=='-1' ){
						ctr_catalogs_update_params( $_POST['ctr'], $_POST['prd'] );
					} else {
						$ar_template = array( 'model'=>$_POST['modelscdiscount'], 'template'=>array() );
						foreach( $_POST['attr-cdiscount'] as $key=>$val ){
							$ar_template['template'][ $key ] = $val;
						}
					}
					
					if( isset($ar_template) && !ctr_catalogs_update_params( $_POST['ctr'], $_POST['prd'], json_encode( $ar_template )) ){
						$error = true; $res['type'] = 0;
						$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur CDiscount.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
					}
					break;
				case CTR_EBAY :
					if( isset($_POST['ebay-cat'], $_POST['param-ebay-type'], $_POST['param-ebay']) && trim($_POST['ebay-cat'])!='' ){
						require_once('comparators/ctr.ebay.inc.php');
						$ebay = new EBay();
						
						$params = ctr_catalogs_get_params( CTR_EBAY, $_POST['prd'] );
						$opt_grp = isset($params['opt-grp']) ? $params['opt-grp'] : false;
						
						$ar_params = array();
						$specifics = $ebay->getCategorySpecifics( $_POST['ebay-cat'] );
						
						foreach( $specifics as $s ){
							// Recherche le champ dans les POST
							$key = array_search( $s['name'], $_POST['param-ebay-type'] );
							
							// Si le champ est renseigné
							if( $key!==false ){
								if( !isset($_POST['param-ebay'][$key]) ){
									continue;
								}

								if( !is_array($_POST['param-ebay'][$key]) ){
									error_log(__FILE__.':'.__LINE__.' param-ebay '.$key.' pas un tableau');
									continue;
								}
								
								// Récupère le nombre de valeur maximale
								$maxlength = isset($s['rules']['maxlength']) ? $s['rules']['maxlength'] : 1;
								
								// Charge les valeurs dans un tableau
								$vals = array();
								foreach( $_POST['param-ebay'][$key] as $val ){
									if( $val=='other' ){
										$other = isset($_POST['param-other-ebay'][$key]) ? $_POST['param-other-ebay'][$key] : '';
										if( trim($other)!='' ){
											$vals = array_merge( $vals, explode(';', $other) );
										}
									}elseif( trim($val)!='' ){
										$vals[] = $val;
									}
								}
								
								if( !sizeof($vals) ){
									continue;
								}
								
								if( sizeof($vals)>$maxlength ){
									$error = true; $res['type'] = 0;
									$res['message'] = sprintf(_("Le nombre de valeur maximale pour \"%s\" est de %d"), $s['name'], $maxlength);
									break;
								}
								
								// Vérifier les relations entre champ
								if( is_array($s['rules']['relations']) && sizeof($s['rules']['relations']) ){
									foreach( $s['rules']['relations'] as $r ){
										$rkey = array_search( $r, $_POST['param-ebay-type'] );
										if( $rkey===false ){
											$error = true; $res['type'] = 0;
											$res['message'] = sprintf(_('L\'information "%s" ne peux être prise en compte car l\'information "%s" n\'est pas renseignée.'), $s['name'], $r);
											break;
										}else{
											$rvals = array();
											foreach( $_POST['param-ebay'][$rkey] as $val ){
												if( $val=='other' ){
													$other = isset($_POST['param-other-ebay'][$rkey]) ? $_POST['param-other-ebay'][$rkey] : '';
													if( trim($other)!='' ){
														$rvals = array_merge( $rvals, explode(';', $other) );
													}
												}elseif( trim($val)!='' ){
													$rvals[] = $val;
												}
											}
											
											if( !sizeof($rvals) ){
												$error = true; $res['type'] = 0;
												$res['message'] = sprintf(_('L\'information "%s" ne peux être prise en compte car l\'information "%s" n\'est pas renseignée.'), $s['name'], $r);
												break;
											}
										}
									}
								}
								
								$ar_params[ $s['name'] ] = implode( ';', $vals );
							}
						}
						
						if( !isset($error) ){
							if( $opt_grp ){
								$ar_params['opt-grp'] = $opt_grp;
							}
							
							if( isset($ar_params) && !ctr_catalogs_update_params( $_POST['ctr'], $_POST['prd'], json_encode( $ar_params )) ){
								$error = true; $res['type'] = 0;
								$res['message'] = sprintf(_("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur %s."), $ctr['name'])."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
							}
						}
					}
					break;
				default :
					if( isset($_POST['vals'][$_POST['ctr']]) && is_array($_POST['vals'][$_POST['ctr']]) && sizeof($_POST['vals'][$_POST['ctr']]) ){
						$ar_template = array();
						foreach( $_POST['vals'][$_POST['ctr']] as $key=>$val ){
							if( is_numeric($val) && $val>0 ){
								$rvalue = ctr_cat_field_values_get( $key, $val );
								if( $rvalue && ria_mysql_num_rows($rvalue) ){
									$val = ria_mysql_result( $rvalue, 0, 'val' );
								}
							}
							$ar_template[$key] = $val;
						}
						
						if( isset($ar_template) && !ctr_catalogs_update_params( $_POST['ctr'], $_POST['prd'], json_encode( $ar_template )) ){
							$error = true; $res['type'] = 0;
							$res['message'] = sprintf(_("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur %s."), $ctr['name'])."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème");
						}
					}
					break;
			}
			
			if( !$error && trim($_POST['auctions']) ){
				// vérification du montant minimum
				if( isset($min) ){
					$min = ctr_categories_get_min_auctions( $_POST['ctr'], $cat );
					if( $min>$_POST['auctions'] )
						$error = true;
				}
				
				if( $error && isset($min) ){
					$res['type'] = 0;
					$res['message'] = sprintf(_("Le montant de l'enchère ne peut être inférieur à %s €."), str_replace('.', ',', $min));
				} elseif( $error ) {
					$res['type'] = 0;
					$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'enchère.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			
			$res['amount'] = '0,00';
			if( !$error ){
				if( 
					ctr_catalogs_set_auctions( $_POST['ctr'], $_POST['prd'], $_POST['auctions']) 
					&& ctr_catalogs_set_prd_title($_POST['ctr'], $_POST['prd'], $_POST['title']) 
					&& ctr_catalogs_set_prd_desc($_POST['ctr'], $_POST['prd'], $_POST['desc']) 
				){
					$res['type'] = 1;
					$res['message'] = _("Les informations du produit ont été mises à jour.");
					$res['amount'] = str_replace('.', ',', trim($_POST['auctions']) ? $_POST['auctions'] : $min);
				} elseif( !$error ) {
					$res['type'] = 0;
					$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement du titre et de la description personnalisée.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
	} elseif( isset($_POST['ctr'], $_POST['cat']) ){
		
		// enregistrement de l'enchère personnalisée pour un produit
		if( isset($_POST['auctionsCat']) ){
			$_POST['auctionsCat'] = str_replace(',', '.', urldecode( $_POST['auctionsCat'] ) );
			
			$r = trim($_POST['auctionsCat']) ? false : true;
			
			if( trim($_POST['auctionsCat']) && (!is_numeric($_POST['auctionsCat']) || $_POST['auctionsCat']<0) ) {
				$res['type'] = 0;
				$res['message'] = _("Le montant de l'enchère doit être un nombre supérieur à 0.");
			} elseif( trim($_POST['auctionsCat']) ){
				// vérification du montant minimum
				if( ctr_comparators_auctions_used($_POST['ctr'], true) ){
					$family = ctr_prd_categories_get_family( $_POST['ctr'], $_POST['cat'] );
					if( $family ){
						$min = ctr_categories_get_min_auctions( $_POST['ctr'], $family );
						if( $min<=$_POST['auctionsCat'] )
							$r = true;
					} else {
						$r = true;
					}
				}
				
				if( !$r && isset($min) ) {
					$res['type'] = 0;
					$res['message'] = sprintf(_("Le montant de l'enchère ne peut être inférieur à %s €."), str_replace('.', ',', $min));
				} elseif( !$r ) {
					$res['type'] = 0;
					$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'enchère.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			
			if( $r && ctr_prd_categories_set_auctions( $_POST['ctr'], $_POST['cat'], $_POST['auctionsCat']) ) {
				$res['type'] = 1;
				$res['message'] = "";
				$res['amount'] = ctr_prd_categories_get_auctions( $_POST['ctr'], $_POST['cat'], false );
			} elseif( $r ) {
				$res['type'] = 0;
				$res['message'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'enchère.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
		
	}
	
	print json_encode( $res );
	exit;
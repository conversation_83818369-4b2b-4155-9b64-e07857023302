<?php 
	/** \file ajax-product-price-table-get.php
	 *	Récupère le tableau de "conditions tarifaires" /admin/catalog/product.php (onglet tarification)
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_VIEW');

	require_once('prices.admin.inc.php');
	require_once('view.admin.inc.php');
	require_once('products.inc.php');

	if( isset($_POST['prd_id']) && is_numeric($_POST['prd_id']) ){
		if(!isset($_POST['periode'])){
			$periode = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
		}
		else{
			$periode = intval($_POST['periode']);
			session_set_periodpicker_state($periode);
		}

		$ref = prd_products_get_ref($_POST['prd_id']);
		if($ref != false){
			product_price_table_get($ref, $_POST['prd_id'], $periode);
		}
	}
	

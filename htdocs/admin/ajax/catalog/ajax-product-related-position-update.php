<?php

	/**	\file ajax-product-related-position-update.php
	 *	Ce fichier est appelé en Ajax et permet la mise à jour de la position des articles liés à un produit. Cela permet
	 *	d'obtenir une liste d'articles liés avec un tri personnalisé.
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

	if( !isset($_POST['source'], $_POST['target'], $_POST['action']) ){
		error_log( __FILE__.':'.__LINE__.' error post product related position update' );
		return false;
	}
	
	if( isset($_GET['rel']) && is_numeric($_GET['rel']) ){
		$res = obj_position_update( DD_PRODUCT_RELATED, $_POST['source'], $_POST['target'], $_POST['action'], array('rel' => $_GET['rel'], 'prd' => $_GET['prd']));	
	}else{
		$res = obj_position_update( DD_PRODUCT_RELATED, $_POST['source'], $_POST['target'], $_POST['action'], array('prd' => $_GET['prd']));
	}
	
	$response = array( 'success' => $res );
	
	print json_encode( $response );
	exit;


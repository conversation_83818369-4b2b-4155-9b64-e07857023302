<?php

	/**	\file ajax-category.php
	 *	Cette page est appelée en Ajax pour gérer plusieurs actions sur les catégories de produits :
	 *	- Mettre à jour des colonnes à afficher pour la liste des produits
	 *	- Récupérer les champs avancés liés au modèle
	 *	- Ajouter un lien entre catégorie / promotion
	 *	- Suppression d'un lien entre catégorie / promotion
	 *	- Recharger la zone pour faire le lien entre une catégorie et une promotion
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG');

	require_once('fields.inc.php');
	require_once('prd/category-filters.inc.php');
	
	$json = array();
	
	// Mise à jour des colonnes à afficher pour la liste des produits
	if( isset($_GET['up-cols'], $_GET['col']) ){
		$ar_list_cols = isset($_SESSION['usr-admin-list-prds-cols']) ? $_SESSION['usr-admin-list-prds-cols'] : $config['admin-list-prds-cols'];
		
		if( !$_GET['up-cols'] ){
			if( $_GET['col']=='all' ){
				$ar_list_cols = array( 'ref', 'name', 'price_ht', 'price_ttc', 'publish', 'publish-site', 'fields-missing' );
			}elseif( $kid = array_search($_GET['col'], $ar_list_cols) ){
				$json = $kid;
				unset( $ar_list_cols[ $kid ] );
			}
		}else{
			if( $_GET['col']=='all' ){
				$ar_list_cols = array();
				foreach( $config['ar_cols_prd'] as $col ){
					$ar_list_cols[] = $col['code'];
				}
			}elseif( !in_array($_GET['col'], $ar_list_cols) ){
				$ar_list_cols[] = $_GET['col'];
			}
		}
		
		// Mise à jour de la variable de configuration pour l'utilisateur
		cfg_variable_users_add( 'admin-list-prds-cols', $_SESSION['usr_id'], implode(',', $ar_list_cols) );
		$json = $_SESSION['usr-admin-list-prds-cols'] = $ar_list_cols;
		load_list_cols_products();
	}
	
	// Récupère les champs avancés liés au modèle
	if( isset($_POST['mdl']) && isset($_POST['cat']) ){
		$rfld = fld_fields_get( 0, 0, $_POST['mdl'], 0, 0, 0, null, array(), false, array(), null, CLS_PRODUCT );
		
		if( $rfld && ria_mysql_num_rows($rfld) ){
			
			while( $fld = ria_mysql_fetch_array($rfld) ){
				$filters = prd_category_filters_get($_POST['cat'], $fld['id'] );
				if(!ria_mysql_num_rows($filters) ){
					$json[] = array( 'id' => $fld['id'], 'name' => $fld['name'] );
				}

			}
		}
	}

	// Ajout d'un lien entre catégorie / promotion
	if (isset($_POST['add-link-pmt'], $_POST['cat'], $_POST['cod'])) {
		prd_categories_codes_add( $_POST['cat'], $_POST['cod'] );
	}

	// Suppression d'un lien entre catégorie / promotion
	if (isset($_POST['del-link-pmt'], $_POST['cat'], $_POST['cod'])) {
		prd_categories_codes_del( $_POST['cat'], $_POST['cod'] );
	}

	// Recharge la zone pour faire le lien entre une catégorie et une promotion
	if (isset($_POST['add-link-pmt']) || isset($_POST['del-link-pmt'])) {
		require_once('categories.inc.php');
		require_once('view.admin.inc.php');
		
		$json = array(
			'count'	=> prd_categories_get_prd_count($_POST['cat']),
			'html'	=> view_admin_categories_codes($_POST['cat'])
		);
	}

	print json_encode( $json );
	exit;
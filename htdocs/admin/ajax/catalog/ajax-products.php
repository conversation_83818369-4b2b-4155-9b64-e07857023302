<?php

	/**	\file ajax-products.php
	 *	Ce fichier est appelé en Ajax et permet de récupérer une liste de produits en fonction de filtres optionnels passés en argument.
	 *
	 *	Pour récupérer une liste de produits, les arguments acceptés sont les suivants :
	 *		- id : Facultatif, identifiant d'un produit
	 *		- ref : Facultatif, référence d'un produit
	 *		- cat : Facultatif, identifiant d'une catégorie de produits
	 *		- brand : Facultatif, identifiant ou tableau d'identifiants de marque(s) de produits. La valeur spéciale \c all est acceptée pour ne pas filtrer par marque
	 *		- field : Facultatif, couple(s) champ/valeur de champs avancés sur lesquels filtrer la liste des produits.
	 *
	 * 	Certaines variables obligatoires vont avoir un impact sur la configuration du compte de l'utilisateur :
	 * 		- flt-unpublish : Obligatoire, booléen indiquant si seuls les produits publiés sont retournés (true) ou tous les produits (false)
	 * 		- flt-sleep : Obligatoire, booléen indiquant si les produits en sommeil sont masqués (true) ou non (false)
	 * 		- flt-catchilds : Obligatoire, booléen indiquant si les produits se trouvant dans des sous-catégories sont retournés (true) ou non (false)
	 * 		- flt-childs : Obligatoire, indique si les produits enfants seulement sont retournés (true) ou non (false)
	 * 
	 * 	Pour ne pas impacter la configuration de l'utilisateur, préciser le paramètre load-usr-config
	 * 
	 * 	Les paramètres de pagination suivants sont également acceptés :
	 * 		- rowstart : 1ère ligne de résultat à retourner (valeur par défaut : 0)
	 * 		- maxrows : nombre de lignes maximum à retourner (valeur par défaut : -1)
	 *
	 * 	Dans le contexte spécifique de la veille tarifaire, les paramètres suivants sont acceptés :
	 *		- add-selection : Obligatoire, booléen indiquant si le produit doit être ajouté ou retiré de la sélection
	 *		- prd : Obligatoire, identifiant du produit à ajouter ou retirer de la sélection
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');
	require_once('prd/category-filters.inc.php');

	$id = isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ? $_GET['id'] : 0;
	$ref = isset($_GEt['ref']) ? trim($_GET['ref']) : '';
	$cat = isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ? $_GET['cat'] : 0;
	$brand = isset($_GET['brand']) ? $_GET['brand'] : 0;
	$load_usr_config = isset($_GET['load-usr-config']) && $_GET['load-usr-config'];

	// Paramètres de pagination
	$rowstart = isset($_GET['rowstart']) && is_numeric($_GET['rowstart']) ? $_GET['rowstart'] : 0;
	$maxrows = isset($_GET['maxrows']) && is_numeric($_GET['maxrows']) ? $_GET['maxrows'] : -1;
	
	// Filtres liés au compte utilisateur
	if( $load_usr_config ){

		$unpublish = cfg_variable_users_get( 'admin_catalog_hide_source_unpublished', $_SESSION['usr_id'] );
		$hide_sleeping = cfg_variable_users_get( 'admin_hide_sleeping', $_SESSION['usr_id'] );
		$catchilds = cfg_variable_users_get( 'admin_show_catchilds', $_SESSION['usr_id'] );
		$childs = cfg_variable_users_get( 'admin_show_childs', $_SESSION['usr_id'] );

	}else{

		// Gestion du paramètre de publication des produits + sauvegarde en session et configuration compte client
		$unpublish = isset($_GET['flt-unpublish']) && $_GET['flt-unpublish'];
		cfg_variable_users_add( 'admin_catalog_hide_source_unpublished', $_SESSION['usr_id'], $unpublish ? 1 : 0);
		$_SESSION['usr_admin_catalog_hide_source_unpublished'] = $unpublish ? 1 : 0;
		
		// Gestion du paramètre d'affichage des produits en sommeil + sauvegarde en session et configuration compte client
		$hide_sleeping = isset($_GET['flt-sleep']) && $_GET['flt-sleep'];
		cfg_variable_users_add( 'admin_hide_sleeping', $_SESSION['usr_id'], $hide_sleeping ? 1 : 0);
		$_SESSION['usr_admin_hide_sleeping'] = $hide_sleeping ? 1 : 0;
		
		// Gestion du paramètre d'affichage des produits dans les catégories enfants + sauvegarde en session et configuration compte client
		$catchilds = isset($_GET['flt-catchilds']) && $_GET['flt-catchilds'];
		cfg_variable_users_add( 'admin_show_catchilds', $_SESSION['usr_id'], $catchilds ? 1 : 0);
		$_SESSION['usr_admin_show_catchilds'] = $catchilds ? 1 : 0;
		
		// Gestion du paramètre d'affichage des produits "Enfant seulement" + sauvegarde en session et configuration compte client
		$childs = isset($_GET['flt-childs']) && $_GET['flt-childs'];
		cfg_variable_users_add( 'admin_show_childs', $_SESSION['usr_id'], $childs ? 1 : 0);
		$_SESSION['usr_admin_show_childs'] = $childs ? 1 : 0;

		$childs = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
		$unpublish = isset($_SESSION['usr_admin_catalog_hide_source_unpublished']) ? $_SESSION['usr_admin_catalog_hide_source_unpublished'] : false;
		$hide_sleeping = !isset($_SESSION['usr_admin_hide_sleeping']) ? $config['admin_hide_sleeping'] : $_SESSION['usr_admin_hide_sleeping'];
		$catchilds = !isset($_SESSION['usr_admin_show_catchilds']) ? $config['admin_show_catchilds'] : $_SESSION['usr_admin_show_catchilds'];

	}
	
	if( isset($brand['all']) ){
		unset($brand['all']);
	}
	
	$fld = false; $check_field = array();
	if( isset($_GET['field']) && is_array($_GET['field']) ){
		foreach( $_GET['field'] as $f=>$vals ){
			foreach( $vals as $v ){
				$check_field[][$f] = $v;
				$fld[$f][] = $v;
			}
		}
	}
	
	// Récupère le tri effectué sur la liste des produits de cette catégorie
	$sort = prd_categories_sort_get( $_GET['cat'] );
	if( !is_array($sort) ){
		$sort = array ( 'type'=>$config['cat_sort_type'], 'dir'=>$config['cat_sort_dir'], 'is_recursive'=>0, 'inherited'=> $_GET['cat'] );
	}
	
	$or_between_val = isset($_GET['flt-val-or']) && $_GET['flt-val-or'] ? false : true;
	$or_between_fld = isset($_GET['flt-fld-or']) && $_GET['flt-fld-or'] ? false : true;
	
	$params = array(
		'brand' => $brand,
		'hide_sleeping' => $hide_sleeping,
		'childs' => $childs,
		'or_between_val' => $or_between_val,
		'or_between_fld' => $or_between_fld,
		'rowstart' => $rowstart,
		'maxrows' => $maxrows
	);
	
	// Liste des produits totale, produits des catégories enfants inclus si paramétré ainsi
	$total_products = prd_products_get_simple( 0, '', $unpublish, $cat, $catchilds, false, false, false, array('hide_sleeping' => $hide_sleeping, 'childs' => $childs, 'rowstart' => $rowstart, 'maxrows' => $maxrows ) );
	// Liste des produits affichés dans la liste (enfants directs de la catégorie)
	$products = prd_products_get_simple( 0, '', $unpublish, $cat, $catchilds, $fld, true, false, $params );
	
	$results = array(
		'checkBrand' => isset($_GET['brand']) ? $_GET['brand'] : 0,
		'checkField' => $check_field,
		'nbProducts' => $products ? ria_mysql_num_rows($products) : 0,
		'totalnbProducts' => $total_products ? ria_mysql_num_rows($total_products) : 0,
		'html' => view_products_list( $products, $sort, 0, array(), $rowstart )
	);
	
	print json_encode( $results );
	exit;
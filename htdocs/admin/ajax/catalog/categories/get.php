<?php

	/**	\file get.php
	 *	Cette page est appelée en Ajax pour charger une liste de catégories. Les paramètres GET acceptés sont les suivants :
	 *		- cat : facultatif, identifiant de la catégorie
	 *		- published : facultatif, restreindre la réponse aux catégories publiées (true) ou non (false, valeur par défaut)
	 *		- parent : facultatif, identifiant d'une catégorie parente sur laquelle filtrer le résultat
	 *
	 * 	Retourne un tableau associatif au format JSON décrivant les différentes catégories retournées avec les clés suivantes :
 	 *		- id : identifiant de la catégorie, ou tableau d'identifiants de catégories
 	 *		- name : désignation de la catégorie
 	 *		- title : désignation de la catégorie
 	 *		- url_alias : url de la catégorie dans le site publique
 	 *		- desc : description de la catéogrie
 	 *		- keywords : mots clés associés à la catégorie
 	 *		- parent_id : identifiant de la catégorie parente
 	 *		- products : nombre de produits publiés contenus dans cette catégorie
 	 *		- publish : booléen indiquant si la catégorie est publiée ou non
 	 *		- cnt_id : identifiant du résultat de moteur de recherche associé à la catégorie
 	 *		- pos : position d'affichage de la catégorie (ordre)
 	 *		- is_sync : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 	 *		- tag_title : nom de la page pour le référencement
 	 *		- tag_desc : description de la page pour le référencement
 	 *		- date_from : date de début d'affichage de la catégorie, au format jj/mm/aaaa
 	 *		- hour_from : heure de début d'affichage de la catégorie, au format hh:mm
 	 *		- date_to : date de fin d'affichage de la catégorie, au format jj/mm/aaaa
 	 *		- hour_to : heure de fin de la catégorie, au format hh:mm
 	 *		- date_from_en : date de début d'affichage au format en 
 	 *		- date_to_en : date de fin d'affichage au format en
 	 *		- first_publish : date de première publication
 	 *		- first_publish_en : date de première publication au format en
 	 *		- ref : référence de la catégorie
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG');

	require_once('categories.inc.php');

	header('Content-Type: application/json');
	
	// Charge la liste des catégories enfants
	$categories = prd_categories_get_array(
		isset($_GET['cat']) && $_GET['cat']!='null' ? $_GET['cat'] : 0,
		isset($_GET['published']) ? $_GET['published'] : false,
		isset($_GET['parent']) && $_GET['parent']!='null' ? $_GET['parent'] : 0
	);

	print json_encode( $categories );
	exit;
<?php

	/**	\file popup-brands-select.php
	 *	Cette page permet le choix d'une marque. Elle est utilisée à différents endroits dans le back-office :
	 *	- dans la gestion des droits d'accès
	 *	- dans les règles de négotiations pour les commerciaux
	 *	- dans la gestion des zones de livraison
	 *	- dans la gestion des champs avancés
	 *	- dans la gestion des promotions spéciales
	 *	- dans la gestion des programmes de fidélité
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_VIEW');

	$brd = 0;
	$current_brd = false;
	$is_ajax = false; 
	$limit_brd = 25;
	$root_brd = 0;
	
	if( isset( $_GET['brd'] ) && is_numeric( $_GET['brd'] ) ){
		$brd = $_GET['brd'];
	}
	if( isset( $_GET['brd'] ) && is_numeric( $_GET['brd'] ) ){
		$root_brd = $_GET['brd'];
	}

	if( $root_brd ){
		$rbrd = prd_brands_get( $root_brd );
		if( $rbrd && ria_mysql_num_rows($rbrd) ) {
			$current_brd = ria_mysql_fetch_array($rbrd);
		}
	}
	
	//si requete ajax on ne va pas plus loin
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}
	
	if( !$is_ajax ) { 
		define('ADMIN_PAGE_TITLE', _('Sélectionner une marque'));
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
		require_once('admin/skin/header.inc.php');
?>
<?php } ?>
	<form action="/ajax/catalog/popup-brands-select.php" method="get">
		<input type="hidden" name="in-field" id="in-field" value="<?php print ( isset($_GET['inField']) && $_GET['inField'] )?>" />
		<input type="hidden" name="input-id" id="input-id" value="<?php print isset($_GET['inputID']) ? $_GET['inputID'] : ''; ?>" />

		<?php 

		// affiche les types de documents
		print '<table class="checklist">';

		$rbrd = prd_brands_get();
		if( $rbrd && ria_mysql_num_rows($rbrd) ){

			$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1;
			$pages = ceil( ria_mysql_num_rows($rbrd) / $limit_brd );

			if( $rbrd ){
				print '
				<thead>
					<tr>
						<th>'._('Nom').'</th>
						<th></th>
					</tr>
				</thead>';
			
				$count = 0;
				print '	<tbody>';
				if( ria_mysql_num_rows($rbrd) ){
					ria_mysql_data_seek( $rbrd, ($page-1)*$limit_brd );
					
					while( $brd = ria_mysql_fetch_array($rbrd) ){
						if( $count >= $limit_brd ) break;
						
						print '	
						<tr>
							<td>
								<input class="radio" type="radio" name="docs" id="docs-'.$brd['id'].'" value="'.$brd['id'].'"  data-name="'.htmlspecialchars($brd['title']).'" />
								
								<label for="docs-'.$brd['id'].'">'.$brd['name'].'</label>
							</td>
							<td class="align-center"></td>
						</tr>
						';
						$count++;
					}
				} 

				if( $root_brd>0 ){
					print '	<tr>
								<td>
									<input class="radio" type="radio" name="docs" id="docs-'.$current_brd['id'].'" value="'.$current_brd['id'].'"  data-name="'.htmlspecialchars($current_brd['title']).'" />
									<label for="docs-'.$current_brd['id'].'">'._('Sélectionner ce contenu').' ('.htmlspecialchars($current_brd['title']).')</label>
								</td>
								<td class="align-center"></td>
							</tr>
					';
				}
				
				print '	</tbody>';
			}
		}
		
				
		print '	
			<tfoot>
				<tr id="pagination">
					<td class="page align-left">'.sprintf(_('Page %d/%d'), $page, $pages).'</td>
					<td class="pages">
		';
		for( $i= ( $page-5 < 1 ? 1 : $page-5) ; $i<=( $page+5 > $pages ? $pages : $page+5); $i++ ){
			if( $i==$page )
				print '		<b>'.$page.'</b>';
			else
				print '		<a name="selectbrd" data-id="'.$current_brd['id'].'" data-page="'.$i.'">'.$i.'</a>'; 
			
			if( $i<$pages )
				print ' | ';
		}
		print '		</td>
				</tr>
				<tr>
					<td colspan="2">
						<div class="pop-form-search">
							<input class="btn-action" type="button" name="selectbrd" id="selectbrd" value="'._('Sélectionner').'" />
							<input class="btn-action cancel" onclick="parent.hidePopup();" type="button" name="cancel" id="cancel" value="'._('Annuler').'" />
						</div>
					</td>
				</tr>
			</tfoot>
		</table>
		
		';
		
	?></form>
<?php 
	if( !$is_ajax ){ 
?>
		<script>
			$('document').ready(function(){
				var current_ajax_request = false; 
				
				$('a[name=selectbrd]').live('click', function(){
					$('.error').remove();
					page = 1; 
					if( $(this).attr('data-page') ) page = $(this).attr('data-page'); 

					var inputID = $('#input-id').val();
					var inField = $('#in-field').val();
					
					$.get( '/admin/ajax/catalog/popup-brands-select.php?p='+page+'&inField=' + inField + '&inputID=' + inputID, function(html){
						$('#popup-content').html(html);
					});
				});
				$('#selectbrd').live('click', function(){
					$('.error').remove();
					if( !$('input[type=radio]:checked').length ){
						$('.pop-form-search').before( '<div class="error"><?php print _('Veuillez sélectionner une marque.'); ?></div>' );
						return false;
					}
					var id = $('input[type=radio]:checked').val();
					var name = $('input[type=radio]:checked').attr('data-name');

					if( $('#in-field').val() == '1' ){
						window.parent.addObjectInField( id, name, $('#input-id').val() );
					}else{
				 		window.parent.parent_select_brd( id, name );
				 	}
					window.parent.hidePopup();
				});

			});
		</script>
<?php
		require_once('admin/skin/footer.inc.php');
	}
?>
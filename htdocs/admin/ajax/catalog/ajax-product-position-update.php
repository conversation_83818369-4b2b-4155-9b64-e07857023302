<?php

	/**	\file ajax-product-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un produit dans une liste de produits.
	 *	Cette liste peut être soit celle d'une catégorie de produits, soit celle d'une marque.
	 *
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 *
	 * 	Les paramètres suivants sont facultatifs (il faut utiliser soit l'un soit l'autre) :
	 * 	- cat : identifiant de la catégorie dans laquelle le produit se trouve et doit changer de position
	 * 	- brd : identifiant de la marque dans laquelle le produit se trouve et doit changer de position
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

	// Met à jour la position du produit dans une catégorie
if( isset($_GET['cat']) && !isset($_GET['brd']) ){
	
	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$cat = $_GET['cat'];
	$action = $_POST['action'];
	
	require_once('products.inc.php');
	
	$response = array('success' => prd_products_position_update($cat, $source, $target, $action));
	
	print json_encode($response);
	exit;

}elseif( isset($_GET['brd']) && !isset($_GET['cat']) ){ // Met à jour la position du produit dans une marque
	
	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$brd_id = $_GET['brd'];
	
	$action = $_POST['action'];
	
	require_once('products.inc.php');
	
	$response = array('success' => prd_products_position_brands_update($brd_id, $source, $target, $action));
	
	print json_encode($response);
	exit;
} 


<?php

	/**	\file json-permanent.php
	 * 
	 * 	Ce fichier est appelé en Ajax et retourne la liste des redirections permanentes qui correspondent aux filtres passés en argument :
	 * 	- wst : Facultatif, identififiant d'un website sur lequel filtrer le résultat
	 *  - lng : Facultatif, code langue sur lequel filtrer le résultat
	 * 	- filter : Facultatif, permet de filtrer les urls retournées sur un mot clé. La recherche est très simple, de type like '%motcle%'.
	 * 		Cette recherche portera aussi bien sur les urls internes que externes.
	 *  - page : Facultatif, numéro de la page de résultat à retourner
	 *  - term : Obligatoire, terme de recherche
	 * 
	 * 	L'utilisateur doit disposer du droit d'accès _RGH_ADMIN_CONFIG_REDIRECTION
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION');

	require_once('websites.inc.php');
	
	$for_page = 30;
	
	$_GET['wst'] = isset($_GET['wst']) ? $_GET['wst'] : 0;
	$_GET['lng'] = isset($_GET['lng']) ? $_GET['lng'] : 0;
	
	$_GET['wst'] = $_GET['wst']=='all' || !trim($_GET['wst']) ? 0 : $_GET['wst'];
	$_GET['lng'] = $_GET['lng']=='all' || !trim($_GET['lng']) ? 0 : $_GET['lng'];

	$_GET['filter'] = isset($_GET['filter']) ? $_GET['filter'] : '';
	$_GET['page'] = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page']>1 ? $_GET['page'] : 1;
	
	// récupère les sites + création d'un tableau de tous les sites
	$website = wst_websites_get();
	$ar_website = array();
	if( $_GET['wst']==0 ){
		if( $website && ria_mysql_num_rows($website) ){
			while( $web = ria_mysql_fetch_array($website) ){
				$ar_website[ $web['id'] ] = $web['name'];
			}
		}
	}
	
	// langue active pour un site
	$languages = array(); $ar_url_site = array();
	//if( $_GET['lng']==0 ){
		$rl = wst_websites_languages_get( $_GET['wst'] );
		if( $rl ){
			while( $l = ria_mysql_fetch_array($rl) ){
				$languages[ $l['lng_code'] ] = $l['name'];
				$ar_url_site[ $l['wst'].'-'.$l['lng_code'] ] = $l['url'];
			}
		}
	//}

	// récupère les redirections 301 publique
	$rred = rew_rewritemap_get( '', '', 301, trim($_GET['lng']) ? $_GET['lng'] : null, $_GET['wst'], $_GET['filter'] );
	
	$redirection['nombre'] = 0;
	$redirection['languages'] = $_GET['lng']==0 ? sizeof( $languages ) : 1;
	$redirection['websites'] = sizeof( $ar_website );
	$redirection['permanent'] = array();
	
	if( $rred ){
		$redirection['nombre'] = number_format(ria_mysql_num_rows( $rred ), 0, ',', ' ');
		
		if( $_GET['page']>1 && ( ($_GET['page']-1)*$for_page ) < ria_mysql_num_rows($rred) ){
			ria_mysql_data_seek( $rred, ($_GET['page']-1)*$for_page );
		}
		
		$count = 0;
		while( $red = ria_mysql_fetch_array($rred) ){
			if( ($count++)>($for_page-1) )
				break;
			
			$redirection['permanent'][] = array(
				'source' => $red['extern'],
				'dest' => $red['intern'],
				'website' => isset($ar_website[$red['wst_id']]) ? $ar_website[ $red['wst_id'] ] : '',
				'version' => isset($languages[$red['lng_code']]) ? $languages[ $red['lng_code'] ] : '',
				'url_site' => isset($ar_url_site[ $red['wst_id'].'-'.$red['lng_code'] ]) ? $ar_url_site[ $red['wst_id'].'-'.$red['lng_code'] ] : $config['site_url']
			);
		}
	}
	
	print json_encode( $redirection );

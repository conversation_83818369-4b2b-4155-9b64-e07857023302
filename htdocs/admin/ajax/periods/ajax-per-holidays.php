<?php
	require_once('periods.inc.php');
	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	// Permet de modifier les jours fériés sans rechargement de la page de configuration des expéditions.
	if( isset($_POST['year']) ){

		// Avant de sauvagarder les holidays, on vérifie que l'on à bien toutes les données necessaires
		$pob_id = 0;
		$cls_id = 0;
		$type_id = 0;
		$usr_id = 0;

		if (isset($_POST['hld-pob-id']) && !per_objects_exists($_POST['hld-pob-id'])) {
			if (!isset($_POST['cls-id'], $_POST['type-id'], $_POST['usr-id'])) {
				$error = true;
			}else{

				$cls_id = $_POST['cls-id'];
				$type_id = $_POST['type-id'];
				$usr_id = $_POST['usr-id'];
				$pob_id = per_objects_add($cls_id, $type_id, $usr_id);
		
				if (!$pob_id || $pob_id <= 0) {
					$error = true;
				}
			}

			
		}else{
			$pob_id = $_POST['hld-pob-id'];
		}

		// Début du résultat sous forme XML
		$xml .= '<result><head-year><![CDATA[';
		
		// Détermine l'entête du tableau, pour la navigation entre les années
		$xml .= '<th style="text-align:left;">';
		if( date('Y')!=$_POST['year'] ){
			$xml .= '<img class="hld-nav-year" title="'._('Année').' '.($_POST['year']-1).'" alt="'._('Année').' '.($_POST['year']-1).'" src="/admin/images/expeditions/feries_active_left.svg" onclick="holidays('.($_POST['year']-1).','.$pob_id.','.$type_id.','.$cls_id.','.$usr_id.')" />';
		}
		$xml .= '</th>';
		$xml .= '<th style="text-align:center">'._('Année').' '.$_POST['year'].'</th>';
		$xml .= '<th style="text-align:right">';
		if( $_POST['year']<2037 ){
			$xml .= '<img class="hld-nav-year" title="'._('Année').' '.($_POST['year']+1).'" alt="'._('Année').' '.($_POST['year']+1).'" src="/admin/images/expeditions/feries_active_right.svg" onclick="holidays('.($_POST['year']+1).','.$pob_id.','.$type_id.','.$cls_id.','.$usr_id.')" />';
		}
		$xml .= '</th>';
		$xml .= ']]></head-year>\n';

		// Récupère toutes les dates de jours fériés (pour l'année en cours de modification) où les expéditions ont lieu
		$rhld = per_holidays_get( $_POST['year'], true, $pob_id );
					
		// Construit un tableau de toutes les dates
		$current_holidays = array();
		if( $rhld!=false ){
			while( $hld = ria_mysql_fetch_array($rhld) ){
				$current_holidays[] = $hld['date'];
			}
		}
		
		$holidays = holidays($_POST['year']);
		// 	Construit le corps du tableau selon l'année passée en paramètre
		$xml .= '<body-year><![CDATA[';
		foreach( $holidays as $date=>$name ){
			$xml .= '	<tr>';
			$xml .= '		<td headers="hld-date" class="hld-date">'.dateformatcomplet( strtotime($date) ).'</td>';
			$xml .= '		<td headers="hld-name">'.$name.'</td>';
			$xml .= '		<td headers="hld-exp" class="hld-exp">';
			$xml .= '			<input type="radio" name="date['.$date.']" id="hld-exp-yes-'.$date.'" value="1" '.( in_array($date, $current_holidays) ? 'checked="checked"' : '' ).' /><label for="hld-exp-yes-'.$date.'">'._('Oui').'</label>';
			$xml .= '			<input type="radio" name="date['.$date.']" id="hld-exp-no-'.$date.'" value="0" '.( in_array($date, $current_holidays) ? '' : 'checked="checked"' ).' /><label for="hld-exp-no-'.$date.'">'._('Non').'</label>';
			$xml .= '		</td>';
			$xml .= '	</tr>';
		}
		$xml .= ']]></body-year>';
		
		// Fin du résultat sous forme XML
		$xml .= '</result>';
	}else if( isset($_POST['saveHolidays']) ){
		if( !isset($_POST['date']) ){
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._('Une erreur inattendue est survenue lors de l\'enregistrement des jours fériés.').'<br />'._('Veuillez réessayer ou prendre contact pour nous signaler l\'erreur')."</error>";
			$xml .= "</result>";
		} else {

			if (isset($_POST['hld-pob-id']) && !per_objects_exists($_POST['hld-pob-id'])) {
				if (!isset($_POST['cls-id'], $_POST['type-id'], $_POST['usr-id'])) {
					$error = true;
				}

				$pob_id = per_objects_add($_POST['cls-id'], $_POST['type-id'], $_POST['usr-id']);

				if (!$pob_id || $pob_id <= 0) {
					$error = true;
				}
			}else{
				$pob_id = $_POST['hld-pob-id'];
			}

			$error = false;
			foreach( $_POST['date'] as $date=>$exp ){
				
				if( !per_holidays_add($pob_id, $date, $exp) ){
					$error = true;
					break;
				}
			}
			if( $error ){
				$xml .= "<result type=\"0\">";
				$xml .= "	<error>"._("Une erreur inattendue est survenue lors de l'enregistrement des jours fériés.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
				$xml .= "</result>";
			}else{
				$xml .= "<result type=\"1\">";
				$xml .= "	<success>"._("les jours fériés ont bien été enregistrés")."</success>";
				$xml .= "</result>";
			}
		}
	}
	
	print $xml;

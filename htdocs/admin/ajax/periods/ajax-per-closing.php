<?php
	require_once('periods.inc.php');
	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';
	
	if( isset($_POST['delclosing']) ){

		if( !isset($_POST['closing']) || !per_events_del( $_POST['closing']) ){
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._("Une erreur inattendue est survenue lors de la suppression d'une fermeture exceptionnelle.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
			$xml .= "</result>";
		}else{
			$xml .= "<result type=\"1\">";
			$xml .= "<success>"._("La suppression de la fermeture exceptionnelle s'est correctement déroulée.")."</success>";
			$xml .= "</result>";
		}
		
	} elseif( isset($_POST['addclosing']) ){ // Gestion de l'ajout ou mise à jour après des vérifications

		if (!isset($_POST['closing-pob-id']) || !per_objects_exists($_POST['closing-pob-id'])) {
			if (!isset($_POST['cls-id'],$_POST['type-id'],$_POST['usr-id'])) {
				$xml .= "<result type=\"0\">";
				$xml .= "<error>"._("Une erreur inattendue est survenue.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
				$xml .= "</result>";
			}else{
				$cls_id = $_POST['cls-id'];
				$type_id = $_POST['type-id'];
				$usr_id = $_POST['usr-id'];
				$pob_id = per_objects_add($cls_id, $type_id, $usr_id);
				$xml .= $pob_id;
				if (!$pob_id || $pob_id<=0) {
					$xml .= "<result type=\"0\">";
					$xml .= "<error>"._("Une erreur inattendue est survenue lors de la suppression d'une fermeture exceptionnelle.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
					$xml .= "</result>";
				}
			}
		}else{
			$pob_id = $_POST['closing-pob-id'];
		}

		
		if( !isset($_POST['closing']) ){ 
			
			// L'identifiant de la fermeture est manquant
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._("Une erreur inattendue est survenue lors de l'enregistrement d'une fermeture exceptionnelle.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.")."</error>";
			$xml .= "</result>";
			
		} elseif( trim($_POST['name-period-'.$_POST['closing']])=='' ){

			// Le libellé attribué à la fermeture est vide
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle.")."<br />"._("Veuillez renseigner le champ \"Nom de la période\".")."</error>";
			$xml .= "</result>";
			
		} elseif( !isdate($_POST['start-period-'.$_POST['closing']]) || !isdate($_POST['end-period-'.$_POST['closing']]) ){
			
			// La date de début ou la date de fin n'est pas renseignée
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle.")."<br />"._("Veuillez renseigner une date de début et une date de fin.")."</error>";
			$xml .= "</result>";
			
		} elseif( !cmp_date(dateparse($_POST['end-period-'.$_POST['closing']]), dateparse($_POST['start-period-'.$_POST['closing']])) ){
			
			// La date de fin est antérieur à la date de début
			$xml .= "<result type=\"0\">";
			$xml .= "<error>"._("Une erreur est survenue lors de l'enregistrement d'une fermeture exceptionnelle.")."<br />"._("La date de fin ne peut être antérieur à la date de début.")."</error>";
			$xml .= "</result>";
			
		} elseif( $_POST['closing']==0 ){
			
			// L'identifiant est à 0, il s'agit donc de l'ajout d'une nouvelle fermeture
			$evt = per_events_add($pob_id, $_POST['name-period-0'], dateparse($_POST['start-period-0']).' 00:00', dateparse($_POST['end-period-0']).' 23:59');
			if( !$evt ){
				$xml .= "<result type=\"0\">";
				$xml .= "<error>"._("Une erreur inattendue est survenue lors de l'ajout d'une fermeture exceptionnelle.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
				$xml .= "</result>";
			} else {
				$xml .= "<result type=\"1\" add=\"1\" id=\"".$evt."\" name=\"".$_POST['name-period-0']."\" start=\"".$_POST['start-period-0']."\" end=\"".$_POST['end-period-0']."\">";
				$xml .= "<success>"._("L'ajout de la fermeture exceptionnelle s'est correctement déroulé.")."</success>";
				$xml .= "</result>";
			}
			
		} else {

			// L'identifiant est supérieur à 0, il s'agit donc d'une mise à jour
			$id = $_POST['closing'];
			$r = per_events_update($_POST['closing-pob-id'], $id, $_POST['name-period-'.$id], dateparse($_POST['start-period-'.$id]).' 00:00', dateparse($_POST['end-period-'.$id]).' 23:59');
			if( !$r ){
				$xml .= "<result type=\"0\">";
				$xml .= "<error>"._("Une erreur inattendue est survenue lors de la mise à jour d'une fermeture exceptionnelle.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
				$xml .= "</result>";
			} else {
				$xml .= "<result type=\"1\" add=\"0\" id=\"".$id."\" name=\"".$_POST['name-period-'.$id]."\" start=\"".$_POST['start-period-'.$id]."\" end=\"".$_POST['end-period-'.$id]."\">";
				$xml .= "<success>"._("La mise à jour de la fermeture exceptionnelle s'est correctement déroulée.")."</success>";
				$xml .= "</result>";
			}
		}
	}
	
	// Affichage de la réponse XML
	print $xml;

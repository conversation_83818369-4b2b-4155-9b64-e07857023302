<?php

	/**	\file search-class.php
	 *	Ce fichier est utilisé dans la gestion des autorisations ainsi que dans la gestion des négotiations pour effectuer
	 *	des recherches grâce au moteur de recherche.
	 *
	 * 	Les paramètres acceptés sont les suivants :
	 * 	- term : obligatoire, requête de recherche telle qu'entrée par l'utilisateur
	 * 	- cls_type : facultatif, identifiant d'une classe sur laquelle filtrer le résultat
	 *  - publish : facultatif, indiquer true s'il faut limiter les résultats aux seuls contenus publiés
	 * 
	 * 	L'accès à ce fichier est contrôlé par les droits d'accès suivants :
	 *  - _RGH_ADMIN_CATALOG_RIGHT
	 *  - _RGH_ADMIN_CUSTOMER_NEGOTIATION
	 */

	// Vérifie que l'utilisateur en cours peut utiliser les modules Autorisations sur le catalogue ou Négotiations
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_RIGHT_ADD');
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_RIGHT_EDIT');
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_NEGOTIATION_ADD');
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_NEGOTIATION_EDIT');

	header('Content-type: text/json');
	header('Content-type: application/json');
	
	// Vérifie que le paramètre term est bien fourni
	if( !isset($_GET['term']) ){
		exit;
	}

	$class = isset( $_GET['cls_type'] ) ? $_GET['cls_type'] : 'usr';
	$publish = isset($_GET['publish']) && $_GET['publish'] == 'true' ? true : false;
	$rsearch = search3( 1, $_GET['term'], 1, 10, $publish, false, 4, array($class) );
	
	$results = array();
	if( $rsearch && ria_mysql_num_rows($rsearch) ){
		$tmp = '';
		while( $res = ria_mysql_fetch_array($rsearch) ){
		
			switch( $class ){
				case 'usr': // compte client 
					$email = trim(gu_users_get_email( $res['tag'] ));
					$name = trim(gu_users_get_name( $res['tag'] ));
					if( $email == '' && $name == '' ) continue; 
					
					$results[] = array( 'id' => $res['tag'], 'label' => $email.' - '.$name, 'value' => $email );
					break;
				case 'brd': // marque
					$rbrd = prd_brands_get( $res['tag'] );
					if( !$rbrd || !ria_mysql_num_rows( $rbrd ) ) continue;
					$brd = ria_mysql_fetch_array( $rbrd );
					
					$results[] = array( 'id' => $res['tag'], 'label' => $brd['name'], 'value' => $brd['name'] );
					break;
				case 'prd-cat': // catégories
					$rcat = prd_categories_get( $res['tag'] );
					if( !$rcat || !ria_mysql_num_rows( $rcat ) ) continue;
					$cat = ria_mysql_fetch_array( $rcat );
					
					$parents_names = array();
					$rparents = prd_categories_parents_get($cat['id']);
					if( $rparents && ria_mysql_num_rows( $rparents ) ){
						while( $p = ria_mysql_fetch_array( $rparents ) ){
							$parents_names[] = $p['title'];
						}
					}
					$parents_names[] = $cat['title'];
					
					$results[] = array( 'id' => $res['tag'], 'label' => implode(' > ',$parents_names), 'value' => $cat['title'] );
					break;
				case 'prd': // produit
					$rprd = prd_products_get_simple( $res['tag'] );
					if( !$rprd || !ria_mysql_num_rows( $rprd ) ) continue;
					$prd = ria_mysql_fetch_array( $rprd );
					
					$results[] = array( 'id' => $res['tag'], 'label' => $prd['name'], 'value' => $prd['name'] );
					break;
			}
		}
	}
	
	print json_encode( $results );

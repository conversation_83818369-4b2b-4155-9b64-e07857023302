<?php
	require_once('websites.inc.php');
	require_once('cms.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS');
	
	if (!isset($_GET['website'])) {
		$website = ria_mysql_result(wst_websites_get(), 0);
		$_GET['website']= $website;
	}
	
	// ajout d'une catégorie
	if (isset($_POST['add'], $_POST['parent'])) {		
		header('Location: edit.php?parent='.$_POST['parent']);
		exit;
	}
	
	if (isset($_POST['del']) && isset($_POST['categ'])) {
		foreach($_POST['categ'] as $cat) {
			if (! cms_categories_del($cat)) $error = _('Une erreur est survenue lors de la suppression veuillez réessayer ou prendre contact avec nous.');
		}
	}
	
	if (isset($_GET['up'])) {
		if (! cms_categories_update_position($_GET['parent'], $_GET['up'], 'up')) $error = _(" Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		
		if (! isset($error)) {
			header('Location: /admin/tools/cms/index.php'.(isset($_GET['parent']) ? '#form-'.$_GET['parent'] : ''));
			exit;
		}
	}
	
	if (isset($_GET['down'])) {
		if (! cms_categories_update_position($_GET['parent'], $_GET['down'], 'down')) $error = _(" Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		if (! isset($error)) {
			header('Location: /admin/tools/cms/index.php'.(isset($_GET['parent']) ? '#form-'.$_GET['parent'] : ''));
			exit;
		}
	}
	
	// Bouton Archiver Sous catégories
	if(isset($_POST['archives']) && isset($_POST['categ']) ){
		foreach($_POST['categ'] as $c){
			if(!cms_archive($c)){
				$error = _("Une erreur est survenue lors de l'archivage de plusieurs catégories.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}
	// Bouton Restorer Sous catégories
	if(isset($_POST['unarchives']) && isset($_POST['categ']) ){
		foreach($_POST['categ'] as $c){
			if(!cms_unarchive($c)){
				$error = _("Une erreur est survenue lors de l'unarchivage de plusieurs catégories.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	$can_move = gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_MOVE');

	$colspan = $can_move? 4 : 3;

	define('ADMIN_PAGE_TITLE', _('Gestion de contenu').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Gestion de contenu'); ?></h2>
<div id="cms-categories" class="all-parents">
<?php 
	$rcat_racine = cms_categories_get( 0, false, false, 0, false, false, true, null, false, null, false );
	
	if( isset($rcat_racine) && ($pcount = ria_mysql_num_rows($rcat_racine)) ){
		$pi = 0;
		while($cat_racine = ria_mysql_fetch_array($rcat_racine)) {
		?>
			<div id="<?php echo 'category-' . $cat_racine['id']; ?>" class="ria-row-orderable">
				<div id="name-contenu">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_VIEW') ){ ?>
					<h3><a href="edit.php?cat=<?php print $cat_racine['id'];?>"><?php print htmlspecialchars($cat_racine['name']);?></a></h3>
					<?php }else{ ?>
					<h3><?php print htmlspecialchars( $cat_racine['name']); ?></h3>
					<?php }
					// On affiche le tri personnalisé seulement si le nombre d'items est supérieur à 1
					if( $can_move && $pcount > 1 ){ ?>
						<div class="ria-cell-move">
							<div class="ria-row-catchable" title="<?php print _('Déplacer'); ?>"></div>
						</div>
					<?php } ?>
					<div></div>
				</div>
				<form action="index.php" method="post" id="form-<?php print $cat_racine['id'];?>" >
				<table class="checklist ria-checklist-cms" id="table-gestion-contenu">
					<caption><?php print _('Liste des catégories'); ?></caption>
					<thead>
						<tr>
							<th id="cms-sel-<?php print $cat_racine['id'];?>"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
							<th id="cms-name-<?php print $cat_racine['id'];?>"><?php print _('Titre'); ?></th>
							<th id="cms-published-<?php print $cat_racine['id'];?>"><?php print _('Publiée'); ?>&nbsp;?</th>
							<?php if( $can_move ){ ?>
							<th id="cms-np-<?php print $cat_racine['id'];?>"></th>
							<?php } ?>
						</tr>
					</thead>
					<tbody>
						<?php
							$cat = cms_categories_get( 0, false, false, $cat_racine['id'], false, false, true, null, false, null, false );
							
							if( !ria_mysql_num_rows($cat) ){
								print '<tr><td colspan="'.$colspan.'">'._('Aucune catégorie').'</td></tr>';
							}else{
								$now = date('Y-m-d H:i:s');
								$current = 0; $count = ria_mysql_num_rows($cat);
								while( $r = ria_mysql_fetch_array($cat) ){
																		
									print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable'.($r['archived'] ? ' archived' : '').'">';
									//print '<td headers="cms-position-'.$cat_racine['id'].'">'.$r['pos'].'</td>';
									print '<td headers="cms-sel-'.$cat_racine['id'].'"><input type="checkbox" class="checkbox" name="categ[]" value="'.$r['id'].'" /></td>';
									if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_VIEW') ){ 									
										print '<td headers="cms-name-'.$cat_racine['id'].'"><a href="edit.php?cat='.$r['id'].'">'.$r['id'].' - '.htmlspecialchars($r['name']).'</a></td>';
									}else{
										print '<td headers="cms-name-'.$cat_racine['id'].'">'.$r['id'].' - '.htmlspecialchars($r['name']).'</td>';										
									}
									print '<td headers="cms-published-'.$cat_racine['id'].'">'.($r['date'] !== null && strtotime($r['date']) <= strtotime($now) ? 'Oui' : 'Non').'</td>';
									
									if( $can_move ){
										print '<td headers="cms-np-'.$cat_racine['id'].'" class="align-center ria-cell-move">';
										print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
										print '</td>';
									}
									
									print '</tr>';
									$current++;
								}
							}
						?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="<?php print $colspan; ?>" class="align-left">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){ ?>
							<input type="submit" name="archives" class="btn-del" value="<?php print _('Archiver'); ?>"/>
							<?php }
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_ADD') ){ ?>
							<input type="submit" name="unarchives" class="btn-del" value="<?php print _('Restaurer'); ?>"/>
							<?php } ?>
							<input type="hidden" name="parent" value="<?php print $cat_racine['id'];?>"/>
							<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){ ?>								
							<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return cmsConfirmDelList()" />
							<?php }
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_ADD') ){ ?>								
							<div class="float-right">
								<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
							</div>
							<?php } ?>
						</td>
					</tr>
				</tfoot>
			</table>
			</form>
		</div>
	<?php
			$pi++;
		}
	?>
		<p class="notice"><?php print _('Les gestions de contenus sur fond jaune sont archivés.'); ?></p>
	<?php
	}else{
	?>
		<p class="notice"><?php print _('Aucune page administrable n\'a été définie pour votre installation. Pour activer cette fonctionnalité, n\'hésitez pas à nous contacter.'); ?></p>
	<?php } ?>
</div>
<?php 
	require_once('admin/skin/footer.inc.php');
?>
<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS');

	require_once('cms.inc.php');
	require_once('views.inc.php');
	
	// Vérifie que le contenu demandé existe bien
	if( !(isset($_GET['rev']) && cms_categories_exists($_GET['rev'])) ){
		header('HTTP/1.0 501 Error');
		exit;
	}
	// récupère les infos de révision
	$rev = ria_mysql_fetch_array(rev_revisions_get(CLS_CMS, null, $_GET['rev']));
	
	// récupère le cms
	$cat = ria_mysql_fetch_array(cms_categories_get($_GET['rev'], false, false, -1, false, false, false, null, false, null, false));
	
	// traduction
	if ($_GET['lng'] != $config['i18n_lng']) {
		$tsk_cat = fld_translates_get( CLS_CMS, $cat['id'], $_GET['lng'], $cat, array(_FLD_CMS_NAME=>'name', _FLD_CMS_SHORT_DESC=>'short_desc', _FLD_CMS_DESC=>'desc', _FLD_CMS_TAG_TITLE=>'tag_title', _FLD_CMS_TAG_DESC=>'tag_desc', _FLD_CMS_TAG_KEYWORDS=>'keywords' ), true );
		$cat['name'] = $tsk_cat['name'];
		$cat['short_desc'] = $tsk_cat['short_desc'];
		$cat['desc'] = $tsk_cat['desc'];
		$cat['tag_title'] = $tsk_cat['tag_title'];
		$cat['tag_desc'] = $tsk_cat['tag_desc'];
		$cat['keywords'] = $tsk_cat['keywords'];
	}
	
	$date = preg_match('#^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$#', $rev['date_created'], $match);
	$date = $match[3].'/'.$match[2].'/'.$match[1].' à '.$match[4].':'.$match[5];
	
	$title = str_replace('#param[date]#', ria_date_format($date), _('Version du #param[date]#'));
	
	$user = ($rev['user_id']) ? ria_mysql_fetch_assoc(gu_users_get($rev['user_id'])) : null;
	
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _($title) );
	require_once('admin/skin/header.inc.php');
?>
	<div>
		<div>
			<div>
				<div>
					<table>
						<tr>
							<td style="font-weight: bold;"><?php print _('Titre'); ?>&nbsp;:</td>
							<td><?php print $cat['name']; ?></td>
						</tr>
						<tr>
							<td style="font-weight: bold;"><?php print _('Description courte'); ?>&nbsp;:</td>
							<td><?php print $cat['short_desc']; ?></td>
						</tr>
					</table>
				</div>
				<h3><?php print _('Description longue'); ?>&nbsp;:</h3>
				<div>
					<?php echo view_site_format_riawysiwyg($cat['desc'], false, true, false, false); ?>
				</div>
				<?php if( tnt_tenants_have_websites() ){ ?>
				<h3><?php print _('Référencement'); ?></h3>
				<div>
					<table>
						<tr>
							<td><?php print _('Titre'); ?>&nbsp;:</td>
							<td><?php print htmlspecialchars($cat['tag_title']); ?></td>
						</tr>
						<tr>
							<td><?php print _('Description :'); ?></td>
							<td><?php print htmlspecialchars($cat['tag_desc']); ?></td>
						</tr>
						<tr>
							<td><?php print _('Mots clés'); ?>&nbsp;:</td>
							<td><?php print htmlspecialchars($cat['keywords']); ?></td>
						</tr>
					</table>
				</div>
				<?php } 
				
					$cat_models = fld_models_get(0, $_GET['rev'], CLS_CMS);
					if ($cat_models !== false) {
						if (ria_mysql_num_rows($cat_models) > 0) {
								print '
								<h3>'._('Champs avancés').'&nbsp;:</h3>
									<table>';
									// Si un ou plusieurs modèles sont affectés à la catégorie, affiche leur liste ainsi que les champs qu'ils contiennent
									while ($m = ria_mysql_fetch_array($cat_models)) {
										print '
										<tr>
											<th colspan="2">
												<h3>'._('Modèle de saisie').'&nbsp;: '.htmlspecialchars($m['name']).'</h3>
											</th>
										</tr>';
										
										$fields = fld_fields_get(0, 0, $m['id'], 0, 0, $_GET['rev'], null, array(), false, array(), null, CLS_CMS, null, false, null, null, $_GET['lng']);
										$last_cat = '';
										while ($f = ria_mysql_fetch_array($fields)) {
											if ($f['cat_name'] != $last_cat) {
												print '<tr><th colspan="2"><h4>'.htmlspecialchars($f['cat_name']).'</h4></th></tr>';
												$last_cat = $f['cat_name'];
											}
											print '<tr>
												<td>
													<label for="fld'.$f['id'].'" '.( $f['is-sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '' ).'>'.htmlspecialchars($f['name']).' :</label>
												</td>
											<td>';
											print fld_fields_view($f);
											print '
												</td>
											</tr>';
										}
									}
								print '</table>
								</div>';
						}
					}
				?>
			</div>
		</div>
		<div style="background: #f0f0f0; border-top: solid 1px #e0e0e0; padding: 10px">
			<table>
				<tr>
					<td style="font-weight: bold;"><?php print _('Version'); ?>&nbsp;:</td>
					<td><?php print $title; ?></td>
				</tr>
				<tr>
					<td style="font-weight: bold;"><?php print _('Date de création :'); ?></td>
					<td><?php print ria_date_format($date); ?></td>
				</tr>
				<?php if ($user) { ?>
				<tr>
					<td style="font-weight: bold;"><?php print _('Modifié par'); ?>&nbsp;:</td>
					<td><?php print '<a href="/admin/customers/edit.php?usr='.$user['id'].'" target="_blank">'.$user['adr_firstname'].' '.$user['adr_lastname'].'</a>'; ?></td>
				</tr>
				<?php } ?>
				<tr>
					<td style="font-weight: bold;"><?php print _('Type de modification'); ?>&nbsp;:</td>
					<td><?php print ($rev['major'] ? 'Majeure' : 'Mineure'); ?></td>
				</tr>
				<?php if ($rev['comment']) { ?>
				<tr>
					<td style="font-weight: bold;"><?php print _('Commentaires'); ?>&nbsp;:</td>
					<td><?php print str_replace("\n", '<br />', htmlspecialchars($rev['comment'])); ?></td>
				</tr>
				<?php }?>
			</table>
		</div>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>

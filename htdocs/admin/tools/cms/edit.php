<?php

	/**	\file edit.php
	 *	Cette page permet la mise à jour d'un contenu "cms" et la création de sous-pages.
	 */

	require_once('websites.inc.php');
	require_once('cms.inc.php');
	require_once('revisions.inc.php');

	// Vérifie que l'utilisateur a bien accès à cette page
	if( isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS_VIEW');
	}elseif( !isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS_ADD');
	}
	
	// Vérifie que l'utilisateur a bien le droit de modifier un contenu
	if( isset($_GET['cat']) ){
		if( isset($_POST['save']) || isset($_POST['addmdl']) || isset($_POST['savefields']) || isset($_POST['delimg']) || isset($_POST['rev-delete']) || isset($_POST['save-ref'])){
			gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS_EDIT');
		}
	}
	
	// Vérifie que la catégorie demandée existe
	if( isset($_GET['cat']) && !cms_categories_exists($_GET['cat']) ){
		header('Location: /admin/tools/cms/index.php');
		exit;
	}
	
	if( !isset($_GET['parent']) && !isset($_GET['cat']) ){
		header('Location: index.php');
		exit;
	}elseif( isset($_GET['parent']) && !is_numeric($_GET['parent']) ){
		header('Location: index.php');
		exit;
	}elseif( isset($_GET['cat']) && (!is_numeric($_GET['cat']) || $_GET['cat']<=0) ){
		header('Location: index.php');
		exit;
	}
	
	/* Gestion des onglets */
	if( !isset($_GET['tab']) )
		$_GET['tab'] = 'general';
	$tab = $_GET['tab'];
	if( isset($_POST['tabReferencement']) ){
		$tab = 'ref';
	}elseif( isset($_POST['tabFields']) ){
		$tab = 'fields';
	}
	elseif( isset($_POST['tabRevision'])) {
		$tab = 'revision';
	}elseif( isset($_POST['tabImages']) || isset($_POST['addimg']) ){
		$tab = 'images';
	}
	
	if( isset($_GET['cat']) ){
		$rcms = cms_categories_get($_GET['cat'], false, false, -1, false, false, true, null, false, null, false);
		if( $rcms && ria_mysql_num_rows($rcms) ){
			$cat = ria_mysql_fetch_assoc( $rcms );
			if(!isset($_GET['parent'])){
				$_GET['parent'] = $cat['parent'];
			}
		}
	}
	
	// Déplacer vers le haut
	if( isset($_GET['up']) ){
		if( !cms_categories_update_position( isset($_GET['cat']) ? $_GET['cat'] : 0, $_GET['up'], 'up') )
			$error = _("Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		
		if( !isset($error) ){
			header('Location: /admin/tools/cms/edit.php?cat='.$_GET['cat'].'#liste');
			exit;
		}
	}
	
	// Déplacer vers le bas
	if( isset($_GET['down']) ){
		if( !cms_categories_update_position(isset($_GET['cat']) ? $_GET['cat'] : 0, $_GET['down'], 'down') )
			$error = _("Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		
		if( !isset($error) ){
			header('Location: /admin/tools/cms/edit.php?cat='.$_GET['cat'].'#liste');
			exit;
		}
	}
	
	// Bouton Ajouter
	if(isset($_POST['add'])){
		header('Location: edit.php?parent='.$_GET['cat']);
		exit;
	}
	
	// Bouton Annuler
	if(isset($_POST['cancel'])){
		header('Location: edit.php?cat='.$_GET['parent']);
		exit;
	}
	
	// Bouton Supprimer
	if(isset($_POST['del']) && isset($_GET['cat']) && cms_categories_exists($_GET['cat'])){
		$temp = ria_mysql_fetch_array(cms_categories_get($_GET['cat'], false, false, -1, false, false, true, null, false, null, false));
		$wst_id = $temp['wst_id'];
			
		if(cms_categories_del($_GET['cat'], $wst_id)){
			header('Location: index.php');
			exit;
		}
	}
	
	if(isset($_POST['delete']) && isset($_POST['categ']) ){
		foreach($_POST['categ'] as $c){
			
			if(!cms_categories_del($c)){
				$error = _('Une erreur est survenue lors de la suppression veuillez réessayer ou prendre contact avec nous.' );
			}
		}
	}
	
	// Bouton Archiver Sous catégories
	if(isset($_POST['archives']) && isset($_POST['categ']) ){
		foreach($_POST['categ'] as $c){
			if(!cms_archive($c)){
				$error = _("Une erreur est survenue lors de l'archivage de plusieurs catégories.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}
	// Bouton Restorer Sous catégories
	if(isset($_POST['unarchives']) && isset($_POST['categ']) ){
		foreach($_POST['categ'] as $c){
			if(!cms_unarchive($c)){
				$error = _("Une erreur est survenue lors de l'unarchivage de plusieurs catégories.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}
	
	
	// Bouton Archiver
	if( isset($_POST['archive']) && isset($_GET['cat']) ){
		if( !cms_archive($_GET['cat']) )
			$error = _("Une erreur inattendue s'est produite lors de l'archivage de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: edit.php?cat='.$_GET['cat'].'&tab='.$tab);
			exit;
		}
	}

	// Bouton Restaurer
	if( isset($_POST['restore']) && isset($_GET['cat']) ){
		if( !cms_unarchive($_GET['cat']) ){
			$error = _("Une erreur inattendue s'est produite lors de la restoration de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			header('Location: edit.php?cat='.$_GET['cat'].'&tab='.$tab);
			exit;
		}
	}
	
	// Bouton Enregistrer
	if( isset($_POST['save']) || (isset($_POST['desc-long']) && (isset($_POST['tabReferencement']) || isset($_POST['tabFields']) || isset($_POST['tabRevision']) || isset($_POST['tabImages']))) ){
		if( !isset($_POST['desc-long']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes. \nVeuillez réessayer ou prendre contact avec l'administrateur.");
			$tab = 'general';
		}elseif( !isset($_POST['name']) || trim($_POST['name'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes. \nVeuillez réessayer ou prendre contact avec l'administrateur.");
			$tab = 'general';
		}else{
			// retire les urls de maquettes dans la description
			$url = isset($_SERVER['SCRIPT_URI']) ? $_SERVER['SCRIPT_URI'] : '';
			$url = str_replace('/', '\/', $url); $url = str_replace('.', '\.', $url); $url = str_replace('?', '\?', $url);
			$_POST['desc-long'] = preg_replace( '/"'.$url.'([^"#]*)#([^"]*)"/', '"#$2"', $_POST['desc-long']);

			// Gère la publication
			if( isset($_POST['publish-opt']) ){
				if( $_POST['publish-opt']==1 ){
					$_POST['publish-date'] = '';
				}elseif( $_POST['publish-opt']==2 ){
					$_POST['publish-date'] = date('d/m/Y');
				}elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['publish-date']) ){
					$error = _("La date de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
				}
			}
			if( isset($_POST['to-opt']) ){
				if( $_POST['to-opt']==2 ){
					$_POST['publish_date_end'] = '';
				}elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['publish_date_end']) ){
					$error = _("La date de fin de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
				}
			}
			
			// Gère la création d'une nouvelle catégorie de contenus
			if( !(isset($_GET['cat']) && cms_categories_exists($_GET['cat'])) ){
				$id = cms_categories_add($_POST['name'], $_POST['desc-long'], false, $_POST['publish-date'], $_GET['parent'], $_POST['short_desc'], true, $_POST['publish_date_end']);
				if( !$id ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout de la catégorie\nVeuillez réessayer ou prendre contact avec l'administrateur. ");
				}else{
					header('Location: edit.php?cat='.$id.'&tab='.$tab);
					exit;
				}
			}// Gère la mise à jour d'une catégorie existante
			elseif( isset($_GET['cat'], $_POST['desc-long']) && cms_categories_exists($_GET['cat']) ){
				if( !isset($_POST['rev-major']) ){
					$error = _('Vous devez indiquer si la modification est mineure ou majeure.');
				}else{
					if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
						$cat = ria_mysql_fetch_array(cms_categories_get($_GET['cat'], false, false, -1, false, false, true, null, false, null, false));
						if($cat['parent']==0) {
							$_POST['name'] = $cat['name'];
							$_POST['publish-date'] = isset($_POST['publish-date']) ? $_POST['publish-date'] : '';
							$_POST['publish_date_end'] = isset($_POST['publish_date_end']) ? $_POST['publish_date_end'] : '';
						}
						
						if ($_POST['name'] != $cat['name'] || $_POST['desc-long'] != $cat['desc'] || $_POST['short_desc'] != $cat['short_desc']) {
							if( !cms_categories_revision_add($_GET['cat'], $_POST['rev-major'], $_POST['rev-comment']) ){
								$error = _('Une erreur est survenue lors de la création d\'une révision.');
							}elseif(!cms_categories_update($_GET['cat'], $_POST['name'], $_POST['desc-long'], $_POST['publish-date'], $_POST['short_desc'], $_POST['publish_date_end'])){
								$error = _("Une erreur inattendue s'est produite lors de la modification de la catégorie\nVeuillez réessayer ou prendre contact avec l'administrateur. ");
							}
						}
					} elseif( in_array($_GET['lng'], $config['i18n_lng_used']) ){
						if( !cms_categories_revision_add($_GET['cat'], $_POST['rev-major'], $_POST['rev-comment']) ){
							$error = _('Une erreur est survenue lors de la création d\'une révision.');
						}
						$values = array(
							_FLD_CMS_NAME=>$_POST['name'],
							_FLD_CMS_SHORT_DESC=>$_POST['short_desc'],
							_FLD_CMS_DESC=>$_POST['desc-long']!='<br>' ? $_POST['desc-long'] : ''
						);
						if( !fld_translates_add($_GET['cat'], $_GET['lng'], $values) ){
							$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}
				}
			}
			if(!isset($error) ){
				cms_categories_update_date_publish( $_GET['cat'], $_POST['publish-date'], $_POST['publish_date_end'] );
				if( $tab=='general' )
					$success = _("L'enregistrement de vos modifications à bien été pris en compte");
			}
		}
	}
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	/* Bontou Enregistrer Référencement */
	if (isset($_GET['cat'])) {
		view_admin_tab_referencement_actions( CLS_CMS, $_GET['cat'], $lng );
	}
	
	if(!isset($_GET['website'])){
		$website = ria_mysql_fetch_array( wst_websites_get() );
	}
	
	if(isset($_GET['mv']) && isset($_GET['page'])){
		cms_page_update_position($_GET['page'], $_GET['mv'], $website);
	}
	
	// Action sur l'onglet "Avancés"
	if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0 ){
		view_admin_tab_fields_actions( CLS_CMS, $_GET['cat'], $lng );
	}
	
	// Suppression révisions
	if (isset($_POST['rev-delete'])) {
		$tab = 'revision';
		if (! (isset($_POST['rev-check']) && is_array($_POST['rev-check']))) $error = _('Vous devez sélectionner au moins une révision.');
		elseif (! rev_revisions_del(CLS_CMS, $_GET['cat'], $_POST['rev-check'])) $error = _('Une erreur s\'est produite lors de la suppression.');
		else $success = (count($_POST['rev-check']) > 1) ? _('Les révisions ont été supprimées avec succès.') : _('La révision a été supprimée avec succès.');
	}
	
	// Restauration révision
	if (isset($_GET['restore']) && is_numeric($_GET['restore'])) {
		if (! cms_categories_revision_restore($_GET['cat'], $_GET['restore'])) $error = _('Une erreur s\'est produite lors de la restauration.');
		else {
			$_SESSION['cms_edit_success'] = _('La révision a été restaurée avec succès.');
			header('location: edit.php?cat='.$_GET['cat'].'&tab=revision&lng='.$_GET['lng']);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Gestion de contenu').' - '._('Outils') );
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo (isset($cat['name']) && trim($cat['name']) !='' ? 'Catégorie : '.htmlspecialchars($cat['name']) : _('Nouvelle catégorie')) ?></h2>

<?php
	if (isset($_SESSION['referencement_edit_success'])) {
		$success = $_SESSION['referencement_edit_success'];
		unset($_SESSION['referencement_edit_success']);
	}
	if (isset($_SESSION['referencement_edit_error'])) {
		$error = $_SESSION['referencement_edit_error'];
		unset($_SESSION['referencement_edit_error']);
	}
	
	if (isset($error)){
		print '<div class="error">'.$error.'</div>';
	}
	else if (isset($success)){
		print '<div class="error-success">'.$success.'</div>';
	}

	if(isset($_GET['cat'])){
		$cat = ria_mysql_fetch_array(cms_categories_get($_GET['cat'], false, false, -1, false, false, true, null, false, null, false));
	}

	// Affiche le menu de langue
	if( isset($_GET['cat']) && $_GET['cat']>0 ){
		print view_translate_menu( 'edit.php?parent='.( isset($_GET['parent']) ? $_GET['parent'] : 0 ).( isset($_GET['cat']) ? '&amp;cat='.$_GET['cat'] : '' ), $lng );
	}

	// Récupère les informations traduites
	if( $lng!=$config['i18n_lng'] ){
		$tsk_cat = fld_translates_get( CLS_CMS, $cat['id'], $lng, $cat, array(_FLD_CMS_NAME=>'name', _FLD_CMS_SHORT_DESC=>'short_desc', _FLD_CMS_DESC=>'desc', _FLD_CMS_TAG_TITLE=>'tag_title', _FLD_CMS_TAG_DESC=>'tag_desc', _FLD_CMS_TAG_KEYWORDS=>'keywords', _FLD_CMS_URL=>'url' ), true );
		$cat['name'] = $tsk_cat['name'];
		$cat['short_desc'] = $tsk_cat['short_desc'];
		$cat['desc'] = $tsk_cat['desc'];
		$cat['tag_title'] = $tsk_cat['tag_title'];
		$cat['tag_desc'] = $tsk_cat['tag_desc'];
		$cat['keywords'] = $tsk_cat['keywords'];
		
		$tsk_url = fld_object_values_get( array($_GET['cat']), _FLD_CMS_URL, $lng, false, true );
		$cat['cat_url'] = trim($tsk_url)!='' ? $tsk_url : $cat['url'];
		$cat['url_perso'] = fld_object_values_get( array($_GET['cat']), _FLD_CMS_URL_PERSO, $lng, false, true );
		$cat['url'] = trim($cat['url_perso'])!='' ? $cat['url_perso'] : $cat['cat_url'];
	}
?>
	
<form id="formulaire_cms" action="edit.php?parent=<?php print ( isset($_GET['parent']) ? $_GET['parent'] : 0 ); if(isset($_GET['cat'])) print '&amp;cat='.$_GET['cat']; ?>&amp;lng=<?php print $lng; ?>" method="post"  enctype="multipart/form-data" >
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if (isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']) {
			if( view_admin_show_tab_fields( CLS_CMS, $_GET['cat'] ) ){ ?>
			<li><input type="submit" name="tabFields" value="<?php print _('Avancé'); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
			<?php } ?>
			<li><input type="submit" name="tabImages" value="<?php print _('Images'); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
			<li><input type="submit" name="tabRevision" value="<?php print _('Révisions'); ?>" <?php if( $tab=='revision' ) print 'class="selected"'; ?> /></li>
			<?php if( tnt_tenants_have_websites() ){
				print isset($cat['is_widget']) && !$cat['is_widget'] ? '<li><input type="submit" name="tabReferencement" value="'._('Référencement').'" '.( $tab=='ref' ? 'class="selected"' : '' ).' /></li>' : ''; ?>
			<?php }
		} ?>
	</ul>
	<div id="tabpanel">
	<?php if( $tab=='general' ){ ?>
<table id="editcms">
	<caption><?php print _('Fiche catégorie'); ?></caption>
	<tbody>
			<tr>
				<td class="tdw-150"><label for="name"><span class="mandatory">*</span> <?php print _('Nom :'); ?></label></td>
				<td><input type="text" id="name" name="name" value="<?php isset($cat) ? print htmlspecialchars($cat['name']) : '' ;?>" /></td>
			</tr>
			<tr>
				<td><label for="short_desc"><?php print _('Description courte :'); ?></label></td>
				<td><textarea id="short_desc" class="short" name="short_desc" rows="5" cols="60"><?php isset($cat) ? print htmlspecialchars($cat['short_desc']) : '' ;?></textarea></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Contenu :'); ?></label></td>
				<td>
					<textarea class="tinymce" name="desc-long" id="desc-long" rows="10" cols="50"><?php isset($cat) ? print $cat['desc'] : '' ;?></textarea>
				</td>
			</tr>
			<tr>
				<td><label for="publish-date"><?php print _('Publier le :'); ?></label></td>
				<td>
					<input type="radio" class="radio" name="publish-opt" id="publish-opt-not" value="1" <?php if((isset($cat) && $cat['publish_date']==null) || !isset($cat)){ print 'checked="checked"';}?>  /> <label for="publish-opt-not"><?php print _('Ne pas publier pour l\'instant'); ?></label><br />
					<input type="radio" class="radio" name="publish-opt" id="publish-opt-now" value="2" /> <label for="publish-opt-now"><?php print _('Publier immédiatement'); ?></label><br />

					<input type="radio" class="radio" name="publish-opt" id="publish-opt-date" value="3"  <?php if(isset($cat) && $cat['publish_date']!=null){ print 'checked="checked"';}?>/> <label for="publish-opt-date"><?php print _('Publier le :'); ?></label> <input type="text" class="date" name="publish-date" id="publish-date" value="<?php if(isset($cat) && $cat['publish_date']!=null){ print $cat['publish_date']; }else {print '05/03/2009';}?>" />
				</td>
			</tr>
			<tr>
				<td><label for="to-opt-now"><?php print _('Publier jusqu\'au :'); ?></label></td>
				<td>
					<input type="radio" class="radio" name="to-opt" id="to-opt-now" value="2" <?php if(isset($cat) && !trim($cat['publish_date_end']) ) print 'checked="checked"'; ?> /> <label for="to-opt-now"><?php print _('Publier indéfiniment'); ?></label><br />
					<input type="radio" class="radio" name="to-opt" id="to-opt-date" value="3" <?php if(isset($cat) && trim($cat['publish_date_end']) ) print 'checked="checked"'; ?> />
					<label for="publish_date_end"><?php print _('Publier jusqu\'au :'); ?></label>
					<input type="text" class="date" name="publish_date_end" id="publish_date_end" value="<?php if(isset($cat) && $cat['publish_date_end']!=null){ print $cat['publish_date_end']; }?>" maxlength="10" />
				</td>
			</tr>
			<?php if( isset($cat['is_widget']) && !$cat['is_widget'] ){ ?>
				<tr>
					<td><?php print _('Adresse :'); ?></td>
					<td><a href="<?php isset($cat) ? print htmlspecialchars($cat['url']) : '' ;?>" target="_blank"><?php isset($cat) ? print htmlspecialchars($cat['url']) : '' ;?></a></td>
				</tr>
			<?php }
			if( isset($cat['id']) && $cat['id'] ){ ?>
				<tr>
					<th colspan="2"><?php print _('Segmentation'); ?></th>
				</tr>
				<tr>
					<td><?php print _('Segments'); ?></td>
					<td><?php
						$ar_ex_seg = array();
						
						require_once('segments.inc.php');
						print '	<input type="hidden" name="seg-obj-cls" id="seg-obj-cls" value="'.CLS_CMS.'" />
										<input type="hidden" name="seg-obj-id-0" id="seg-obj-id-0" value="'.$cat['id'].'" />
									
									<div class="seg-obj-infos">';
						$robject = seg_objects_get_segments( CLS_CMS, array($cat['id']) );
						if( $robject &&  ria_mysql_num_rows($robject) ){
							while( $obj = ria_mysql_fetch_array($robject) ){
								$ar_ex_seg[] = $obj['id'];
								print '	<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" title="'._('Retirer ce segment').'" alt="'._('Supprimer').'" />&nbsp;';
								print htmlspecialchars( $obj['name'] ).'<br />';
							}
						} else { print _('Aucune restriction liée aux segments.'); }
						print '	</div>';
					?></td>
				</tr>
				<tr>
					<td><label><?php print _('Ajouter :'); ?></label></td>
					<td>
						<select class="select-obj-seg" name="segment" id="segment">
							<option value="-1"><?php print _('Choisir un segment'); ?></option>
							<?php
								$rseg = seg_segments_get( 0, CLS_USER );
								if( $rseg && ria_mysql_num_rows($rseg) ){
									while( $seg = ria_mysql_fetch_array($rseg) ){
										if( in_array($seg['id'], $ar_ex_seg) ) continue;
										print '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
									} 
								}
							?>
						</select>
					</td>
				</tr>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2" class="npad">
					<?php if (isset($_GET['cat'])) { ?>
						<div class="rev-form">
							<table id="table-type-modif">
								<tr>
									<td><?php print _('Modification :'); ?></td>
									<td>
										<input type="radio" value="0" name="rev-major" id="rev-major-0" /> <label for="rev-major-0"><?php print _('Mineure'); ?></label>
										<input type="radio" value="1" name="rev-major" id="rev-major-1" checked="checked" /> <label for="rev-major-1"><?php print _('Majeure'); ?></label>
									</td>
								</tr>
								<tr>
									<td><label id="label-vertical-align" for="rev-comment"><?php print _('Commentaire sur cette révision :'); ?></label></td>
									<td><textarea name="rev-comment" id="rev-comment" cols="100" rows="1"></textarea></td>
								</tr>
							</table>
							<div></div>
						</div>
					<?php }
					print '<div id="footer-no-npad">'; ?>
					<input type="submit" name="save" id="save" class="float-right" value="<?php print _('Enregistrer'); ?>" />
					<?php
					if(isset($_GET['cat'])) {
						if( $cat['archived'] && gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_ADD') ){ ?>
						<input type="submit" name="restore" value="<?php print _('Restaurer'); ?>" />
					<?php }elseif(!$cat['archived'] && gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){ ?>
						<input type="submit" name="archive" value="<?php print _('Archiver'); ?>" />
					<?php } 
					}
					if (isset($_GET['cat']) && gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){
						print '<input type="submit" name="delete" class="btn-del" value="'._('Supprimer').'" />
						<input type="hidden" name="categ[]" value="'.$_GET['cat'].'" />';
					}
					print '</div>';
					?>
				</td>
			</tr>
		</tfoot>
	</table>
	<?php }elseif( $tab=='ref' ){
		print view_admin_tab_referencement(CLS_CMS, $_GET['cat'], $lng); 
	
	$hierarchy = cms_hierarchy_get( $_GET['cat'] );
		
	if( is_array($hierarchy) && sizeof($hierarchy) ){
		$cmshierarchy = array_shift( $hierarchy );
		$rparent = cms_categories_get( $cmshierarchy['id'], false, false, -1, false, false, true, null, false, null, false );
		if( $rparent && ria_mysql_num_rows($rparent) ){
			$url_p = ria_mysql_result( $rparent, 0, 'url' );
		}
	?>
	
	<h3><?php print _('Redirections'); ?></h3>
	<input type="hidden" name="cms" id="cms" value="<?php print $_GET['cat']; ?>" />
	<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />
	<input type="hidden" name="urlcms" id="urlcms" value="<?php print $cat['url']; ?>" />
	<p class="notice">
	<?php print _('Les urls ci-dessous redirigent l\'internaute vers cette page de contenu. Vous pouvez les utiliser pour la compatibilité avec un ancien site Internet ou pour la mise en place de raccourcis.'); ?> <span><?php print _('Une url ne peut être composée que de caractères aphanumériques (sauf les accents) ou bien des caractères \'-\' et \'/\'.'); ?></span>
	</p>
	<?php
		$rurl = rew_rewritemap_get( $cat['url'], '', 301, $lng );
		$ar_url = array();
		if( $rurl ){
			while( $url = ria_mysql_fetch_array($rurl) ){
				$ar_url[] = $url;
			}
		}
		
		$count = sizeof( $ar_url )+1;
	?>
	<table id="tb-redirection">
		<caption><?php print _('Redirections vers cette page de contenu'); ?></caption>
		<thead>
			<tr>
				<th id="url">URL</th>
				<th id="action"></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( sizeof( $ar_url )==0 ){	
					print '<tr id="no-url"><td headers="url" colspan="2">'._('Aucune URL de redirection enregistrée').'</td></tr>';
				} else {
					$count = 1;
					// while( $url = ria_mysql_fetch_array($rurl) ){
					foreach( $ar_url as $url ){
						print '	
										<tr id="url-'.$count.'">
											<td headers="url" class="td-url">'.$url['extern'].'</td>
											<td headers="action" class="td-action">
												<a class="edit-url button" title="' . _('Editer l\'URL') . '" onclick="editUrlCmsRedirection('.$count.', \''.$url['extern'].'\')">'._('Editer').'</a>
												<br /><a class="del edit button" onclick="delUrlCmsRedirection('.$count.', \''.$url['extern'].'\')">'._('Supprimer').'</a>
											</td>
										</tr>
						';
						$count++;
					}
				}
			?>
		</tbody>
		<tfoot <?php print ( sizeof( $ar_url )==0 ? '' : 'class="none"'); ?>>
			<tr>
				<td colspan="2">
					<input type="button" name="add-form-url" id="add-form-url" value="<?php print _('Ajouter'); ?>" title="<?php print _('Ajouter une redirection vers cette page de contenu'); ?>" onclick="addUrlCmsRedirection(<?php print $count; ?>)" />
				</td>
			</tr>
		</tfoot>
	</table>
	<?php } 
		}elseif( isset($_GET['cat']) && $_GET['cat']>0 && $tab=='fields' ){
			print view_admin_tab_fields( CLS_CMS, $_GET['cat'], $lng );
		} elseif ($tab == 'revision') {
		$rver = rev_revisions_get(CLS_CMS, $_GET['cat'], null, isset($_GET['major']) && $_GET['major'] !== '' ? $_GET['major'] : null);
		$rows = ria_mysql_num_rows($rver);
		
		// pagination
		$by_page = 10;
		if( !isset($_GET['page']) || !is_numeric($_GET['page']) )
			$_GET['page'] = 1;
		$pages = ceil( $rows / $by_page );
		if( $_GET['page']>$pages )
			$_GET['page'] = $pages;
		if( $_GET['page']>1 )
			ria_mysql_data_seek( $rver, ($_GET['page']-1)*$by_page );
		
		$major = (isset($_GET['major']) && is_numeric($_GET['major'])) ? $_GET['major'] : null;
?>
		
		<div id="div-select-type-modif">
			<select onchange="location.href=<?php print '\'?parent='.$_GET['parent'].'&cat='.$_GET['cat'].'&lng='.$_GET['lng'].'&tab=revision&major=\'+this.value'; ?>">
				<option value=""><?php print _('Tous les types de modification'); ?></option>
				<option value="0" <?php if ($major === '0') print ' selected="selected"'; ?>><?php print _('Mineure'); ?></option>
				<option value="1" <?php if ($major === '1') print ' selected="selected"'; ?>><?php print _('Majeure'); ?></option>
			</select>
			<div></div>
		</div>
		<?php
					$uri = '?tab=revision&parent='.$_GET['parent'].'&cat='.$_GET['cat'].'&lng='.$_GET['lng'].'&major='.$major;
					
					if( $pages>1 ){
						$page_start = $_GET['page']>5 ? $_GET['page']-5 : 1;
						$page_stop = $_GET['page']+5<$pages ? $_GET['page']+5 : $pages;

						$links = array();
						if( $_GET['page']>1 )
							$links[] = '<a href="'.$uri.'&page='.($_GET['page']-1).'">&laquo; '._('Page précédente').'</a>';

						for( $i=$page_start; $i<=$page_stop; $i++ )
							if( $i==$_GET['page'] )
								$links[] = '<b>'.$i.'</b>';
							else
								$links[] = '<a href="'.$uri.'&page='.$i.'">'.$i.'</a>';

						if( $_GET['page']<$pages )
							$links[] = '<a href="'.$uri.'&page='.($_GET['page']+1).'">'._('Page suivante').' &raquo;</a>';

					}
				?>
		<table id="table-liste-versions" class="checklist">
			<caption><?php print _('Liste des versions'); ?></caption>
		<thead>
			<tr>
				<th id="cms-ver-sel"><input id="revCheckAll" type="checkbox" class="checkbox" onclick="revCheckAll()" /></th>
				<th id="cms-ver-date"><?php print _('Version'); ?></th>
				<th id="cms-ver-type"><?php print _('Type de modification'); ?></th>
				<th id="cms-ver-user"><?php print _('Modifié par'); ?></th>
				<th id="cms-ver-up"></th>
			</tr>
		</thead>
		<tbody id="tbody-rev">
			<?php
				if (! $rows) {
					print '<tr><td colspan="5">'._('Aucune version').'</td></tr>';
				}
				else {
					$k = 0;
					while ($ver = ria_mysql_fetch_assoc($rver)) {
						if ($k++ >= $by_page) break;
						
						// cat
						$rcat = cms_categories_get($ver['id_X_0'], false, false, -1, false, false, false, null, false, null, false);
						if (! ($rcat && ria_mysql_num_rows($rcat))) continue;
						$cat = ria_mysql_fetch_assoc($rcat);
						
						// user
						$user = null;
						$user_id = $ver['user_id'];
						if ($user_id) {
							$ruser = gu_users_get($user_id);
							if ($ruser && ria_mysql_num_rows($ruser)) $user = ria_mysql_fetch_assoc($ruser);
						}
						
						$cur = ($ver['id_X_0'] == $_GET['cat']);
						
						$date = dateformat(dateheureunparse($ver['date_created'] === null ? date('Y-m-d H:i') : $ver['date_created']), true);
						
						print '	<tr>
											<td colspan="5" class="rev-td">
												<table>
													<tr>
														<td headers="cms-ver-sel">
															<input type="checkbox" class="checkbox" name="rev-check[]" value="'.$ver['id_X_0'].'"'.($cur ? ' disabled="disabled"' : '').' />
														</td>
														<td headers="cms-ver-date"><a class="js-open-revision iframe" id="ver-'.$ver['id_X_0'].'-title" href="popup-revision.php?rev='.$ver['id_X_0'].'&lng='.$_GET['lng'].'">'.str_replace('#param[date]#', $date, _('Version du #param[date]#')).'</a></td>
														<td headers="cms-ver-type">'.($ver['major'] ? _('Majeure') : _('Mineure')).'</td>
														<td headers="cms-ver-user">'.($user ? '<a href="/admin/customers/edit.php?usr='.$user['id'].'" target="_blank">'.htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname']).'</a>' : '').'</td>
														<td headers="cms-ver-up">'.($cur ? _('Version courante') : '<a id="ver-'.$ver['id_X_0'].'-restore" class="ver-restore edit" href="'.$uri.'&restore='.$ver['id_X_0'].'">'._('Restaurer').'</a>').'</td>
													</tr>';
									if ($ver['comment']) {
										print '<tr>
														<td></td>
														<td colspan="4">'.str_replace("\n", '<br />', htmlspecialchars($ver['comment'])).'</td>
													</tr>';
									}
								print '	</table>
											</td>
										</tr>';
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="5">
					<input type="hidden" name="parent" value="<?php print $_GET['cat'];?>"/>
					<input type="submit" name="rev-delete" class="btn-del" value="<?php print _('Supprimer'); ?>" />
				</td>
			</tr>
			<?php if( $pages>1 ){ ?>
			<tr id="pagination">
				<td colspan="2">
				<?php
					print _('Page').' '.$_GET['page'].'/'.$pages;
				?>
				</td>
				<td colspan="3">
				<?php		
					if( $pages>1 ){
						print implode(' | ',$links);
					}
				?>
				</td>
			</tr>
			<?php } ?>
		</tfoot>
		</table>
		
	<?php }elseif( $tab == 'images' ){
		print view_admin_img_table( CLS_CMS, $_GET['cat']);
	} ?>
</div>
</form>
<br/>

<?php 
	if(isset($_GET['cat']) && $config['cms_hierarchy_max_child'] > cms_hierarchy_length_get($_GET['cat']) ){
		// Charge les sous-catégories à afficher
		$cat = cms_categories_get(0,false,false,$_GET['cat'],false,false,true,null,false,null,false);
?>
	<form action="edit.php?<?php if(isset($_GET['cat'])) print 'cat='.$_GET['cat']; ?>" method="post" id="liste">
	<table id="cms-categories" class="checklist">
		<caption><?php print _('Liste des sous-catégories').' ('.( !ria_mysql_num_rows($cat) ? '0' : ria_mysql_num_rows($cat) ).')'; ?></caption>
	<thead>
		<tr>
			<th id="cms-sel-<?php print $_GET['cat'];?>"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="cms-name-<?php print $_GET['cat'];?>"><?php print _('Titre'); ?></th>
			<th id="cms-np-<?php print $_GET['cat'];?>"></th>
		</tr>
	</thead>
	<tbody>
		<?php
			if( !ria_mysql_num_rows($cat) ){
				print '<tr><td colspan="3">'._('Aucune sous catégorie').'</td></tr>';
			}else{
				$current = 0; $count = ria_mysql_num_rows($cat);
				while( $r = ria_mysql_fetch_array($cat) ){
					print '
						<tr id="line-' . $r['id'] . '" class="ria-row-orderable'.($r['archived'] ? ' archived' : '').'">
							<td headers="cms-sel-'.$_GET['cat'].'"><input type="checkbox" class="checkbox" name="categ[]" value="'.$r['id'].'" /></td>
							<td headers="cms-name-'.$_GET['cat'].'"><a href="edit.php?cat='.$r['id'].'">'.$r['id'].' - '.htmlspecialchars($r['name']).'</a></td>
							<td headers="cms-np-'.$_GET['cat'].'" class="align-center ria-cell-move">
								<div class="ria-row-catchable" title="'._('Déplacer').'"></div>
							</td>
						</tr>
					';
					$current++;
				}
			}
		?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="3" class="align-left">
				<input type="hidden" name="parent" value="<?php print $_GET['cat'];?>"/>
				<?php if( ria_mysql_num_rows($cat) ){
					if(  gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){ ?>
					<input type="submit" name="archives" class="btn-del" value="<?php print _('Archiver'); ?>"/>
					<?php }
					if(gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_ADD') ){ ?>				
					<input type="submit" name="unarchives" class="btn-del" value="<?php print _('Restaurer'); ?>"/>
					<?php }
					if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_DEL') ){ ?>
					<input type="submit" name="delete" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return cmsConfirmDelList()" />
					<?php }
				}
				if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_ADD') ){ ?>				
				<div class="float-right">
					<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
				</div>
				<?php } ?>
			</td>
		</tr>
	</tfoot>
	</table>
	</form>
	<p class="notice"><?php print _('Les gestions de contenus sur fond jaune sont archivées.'); ?></p>
<?php 
	}
?>

<script>
	// Disable tous les champs/boutons si on accède à cette page en lecture seul
    <?php if( isset($_GET['cat']) && !gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_EDIT') ){ ?>
		$(document).ready(function(){
			$('table').find('input, select, textarea').attr('disabled', 'disabled')
			$('table a.edit').remove();
			$('.edit-url').attr('onclick','').unbind('click');
		});
	<?php } ?>
	
	<?php if( isset($_GET['cat']) && $tab=='images' && !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CMS') ){ ?>
		$(document).ready(function(){
			$('#table-images').find('input, select, textarea').attr('disabled', 'disabled')
		});
	<?php } ?>
</script>

<script><!--
	var segID = false;
	var nbGroups = false;
	var nbGroupCdts = false;
	var segClsID = <?php print CLS_CMS; ?>;
--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
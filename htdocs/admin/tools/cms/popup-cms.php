<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS');

	$cms = 0;
	$current_cms = false;
	$is_ajax = false; 
	$limit_cms = 10;
	$root_cms = 0;
	$current_cms_parent = false;
	
	if( isset( $_GET['cms'] ) && is_numeric( $_GET['cms'] ) ){
		$cms = $_GET['cms'];
	}
	if( isset( $_GET['cms'] ) && is_numeric( $_GET['cms'] ) ){
		$root_cms = $_GET['cms'];
	}

	if( $root_cms ){
		$rcms = cms_categories_get( $root_cms, false, false, -1, false, false, true, null, false, null, false );
		if( $rcms && ria_mysql_num_rows($rcms) ) {
			$current_cms = ria_mysql_fetch_array($rcms);

			//retourne le parent 
			$rparent = cms_categories_get( $current_cms['parent'], false, false, -1, false, false, true, null, false, null, false );
			if( $rparent && ria_mysql_num_rows($rparent) ) {
				$current_cms_parent = ria_mysql_fetch_array($rparent);
			}
		}
	}
	
	//si requete ajax on ne va pas plus loin
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}
	
	if( !$is_ajax ) { 
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
		define('ADMIN_PAGE_TITLE', _('Sélection de contenus'));
		require_once('admin/skin/header.inc.php');
	}
?>
	<form action="/admin/orders/create/ajax/ncmd-customers-edit.php" method="get"> <?php 

		// affiche les types de documents
		print '<table class="checklist" cellspacing="0" cellpadding="0">';
		print '	<caption>';
		if( $root_cms ){
			print '	<a name="selectcms" data-id="'.$current_cms['parent'].'">';
			print '		<img width="16" height="16" title="'._('Remonter d\'un niveau').'" alt="'._('Remonter d\'un niveau').'" src="/admin/images/up.png">';
			print '	</a>';
			
			if( $current_cms_parent )
				print '	<a name="selectcms" data-id="'.$current_cms['parent'].'"">'.$current_cms_parent['name'].'</a> &gt;&gt; ';
			
			print $current_cms['name'];
		} else {
			print _('Catalogue');
		}
		print '	</caption>';

		print '	<col width="*" />';

		$rcms = cms_categories_get( 0, false, false, $cms, false, false, true, null, false, null, false );
		if( $rcms && ria_mysql_num_rows($rcms) ){

			$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1;
			$pages = ceil( ria_mysql_num_rows($rcms) / $limit_cms );

			if( $rcms ){
				print '	<thead>';
				print '		<tr>';
				print '			<th>'._('Nom').'</th>';
				print '			<th></th>';
				print '		</tr>';
				print '	</thead>';
				
				print '	<tfoot>';
				print '		<tr id="pagination">';
				print '			<td class="page">'._('Page').' '.$page.'/'.$pages.'</td>';
				print '			<td colspan="3" class="pages">';
				for( $i= ( $page-5 < 1 ? 1 : $page-5) ; $i<=( $page+5 > $pages ? $pages : $page+5); $i++ ){
					if( $i==$page )
						print '		<b>'.$page.'</b>';
					else
						print '		<a name="selectcms" data-id="'.$current_cms['id'].'" data-page="'.$i.'">'.$i.'</a>'; 
					
					if( $i<$pages )
						print ' | ';
				}
				print '			</td>';
				print '		</tr>';
				print '	</tfoot>';
			
				$count = 0;
				print '	<tbody>';
				if( ria_mysql_num_rows($rcms) ){
					ria_mysql_data_seek( $rcms, ($page-1)*$limit_cms );
					
					while( $cms = ria_mysql_fetch_array($rcms) ){
						if( $count >= $limit_cms ) break;
						
						print '	<tr>';
						print '		<td>';
						print '			<input class="radio" type="radio" name="docs" id="docs-'.$cms['id'].'" value="'.$cms['id'].'"  data-name="'.htmlspecialchars($cms['name']).'" />';

						$cmschild = cms_categories_get( 0, false, false, $cms['id'], false, false, true, null, false, null, false );
						if( $cmschild && ria_mysql_num_rows($cmschild) ){
							print '		<a name="selectcms" data-id="'.$cms['id'].'" data-page="'.$page.'">'.$cms['name'].'</a>'; 
						} else {
							print '		<label for="docs-'.$cms['id'].'">'.$cms['name'].'</label>';
						}

						print ' 	</td>';
						print ' 	<td align="center"></td>';
						print ' </tr>';
						$count++;
					}
				} 

				if( $root_cms>0 ){
					print '	<tr>';
					print ' 	<td>';
					print ' 		<input class="radio" type="radio" name="docs" id="docs-'.$current_cms['id'].'" value="'.$current_cms['id'].'"  data-name="'.htmlspecialchars($current_cms['name']).'" />';
					print ' 		<label for="docs-'.$current_cms['id'].'">'._('Sélectionner ce contenu').' ('.htmlspecialchars($current_cms['name']).')</label>';
					print ' 	</td>';
					print ' 	<td align="center"></td>';
					print ' </tr>';
				}
				
				print '	</tbody>';
			}
		}
		print '	</table>';

		print '	<div class="pop-form-search">';
		print ' 	<input class="btn-action" type="button" name="selectcms" id="selectcms" value="'._('Sélectionner').'" />';
		print ' 	<input class="btn-action cancel" onclick="parent.hidePopup();" type="button" name="cancel" id="cancel" value="'._('Annuler').'" />';
		print ' </div>';
		
	?></form>
<?php if( !$is_ajax ){ ?>
	<script><!--
		$('document').ready(function(){
			var current_ajax_request = false; 
			
			$('a[name=selectcms]').live('click', function(){
				$('.error').remove();
				page = 1; 
				if( $(this).attr('data-page') ) page = $(this).attr('data-page'); 
				
				$.get( '/admin/tools/cms/popup-cms.php?cms='+$(this).attr('data-id')+'&p='+page, function(html){
					$('#popup-content').html(html);
				});
			});
			$('#selectcms').live('click', function(){
				$('.error').remove();
				if( !$('input[type=radio]:checked').length ){
					$('.pop-form-search').before( '<div class="error">' + '<?php print _('Veuillez sélectionner un contenu.'); ?>' + '</div>' );
					return false;
				}
				var id = $('input[type=radio]:checked').val();
				var name = $('input[type=radio]:checked').attr('data-name');
			 	window.parent.parent_select_cms( id, name );
				window.parent.hidePopup();
			});

		});
	--></script>
<?php 
		require_once('admin/skin/footer.inc.php');
	} 
?>
<?php

	/**	\file category.php
	 *	Permet la mise à jour d'une fiche "Catégorie de questions" dans la FAQ
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_CATEG_EDIT');
	}elseif( !isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_CATEG_ADD');
	}

	require_once('tools.faq.inc.php');

	unset( $error );

	// Vérifie l'existance de la catégorie, si le type est valide et si elle est bien passée en paramètre, et si l'on n'est pas en train de créer une nouvelle catégorie.
	if (!isset($_GET['cat']) || !is_numeric($_GET['cat']) || !faq_categories_exists($_GET['cat'])) {
		if (!isset($_GET['name']) && !isset($_POST['name'])){
			header('Location: index.php');
			exit;
		}
	}
	
	/* Gestion des onglets */
	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}
	$tab = $_GET['tab'];
	if( isset($_POST['tabReferencement']) ){
		$tab = 'ref';
	}
	if( isset($_POST['tabImages']) ){
		$tab = 'images';
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( faq_questions_get_count($_GET['cat'])>0 ){
			$error = _("Par mesure de précaution, seules les catégories ne contenant plus de questions peuvent être supprimées.\nSi vous souhaitez réellement supprimer cette catégorie, veuillez tout d'abord supprimer les questions qu'elle contient.");
		}elseif( !faq_categories_del( $_GET['cat'] ) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur");
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Supprimer les questions
	if( isset($_POST['delqst']) ){
		if( !isset($_POST['qst']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
		}
		foreach( $_POST['qst'] as $q ){
			faq_questions_del($q);
		}
		header('Location: category.php?cat='.$_GET['cat']);
		exit;
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) || isset($_POST['add']) ){
		if( !isset($_GET['cat']) || !is_numeric($_GET['cat']) || !isset($_POST['name']) || !isset($_POST['desc']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
		}elseif( !trim($_POST['name']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.\nVeuillez vérifier. Les champs marqués d'une * sont obligatoires.");
		}elseif( $_GET['cat']>0 ){
			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
				if( !faq_categories_update( $_GET['cat'], $_POST['name'], $_POST['desc'] ) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur");
				}
			}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
				$values = array(
				_FLD_FAQ_CAT_NAME=>$_POST['name'],
				_FLD_FAQ_CAT_DESC=>$_POST['desc']
				);
				
				if( !fld_translates_add($_GET['cat'], $_GET['lng'], $values) ){
					$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}
		}elseif( !($_GET['cat'] = faq_categories_add( $_POST['name'], $_POST['desc'] )) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur");
		}
	}

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	// Bouton Enregistrer Référencement
	if( isset($_GET['cat']) ){
		view_admin_tab_referencement_actions( CLS_FAQ_CAT, $_GET['cat'], $lng );
	}

	// Bouton Ajouter (une question)
	if( !isset($error) && isset($_GET['cat']) && isset($_POST['add']) && isset($_POST['qstname']) ){
		header('Location: question.php?cat='.$_GET['cat'].'&name='.urlencode($_POST['qstname']));
		exit;
	}

	if( isset($_POST['orderby']) && isset($_GET['cat']) && is_numeric($_GET['cat']) && faq_categories_exists($_GET['cat'])  ){
		faq_questions_order_update( $_POST['order'], $_GET['cat'] );
		header('Location: category.php?cat='.$_GET['cat']);
		exit;
	}

	// Détermine si les catégories sont triées alphabétiquement ou non
	if( isset($_GET['cat']) && is_numeric($_GET['cat']) && faq_categories_exists($_GET['cat']) ) {
		$ordered = faq_questions_get_order($_GET['cat']);
	}

	// Détermine si l'utilisateur à le droit de déplacer les questions de la FAQ
	$can_move = gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_MOVE');

	// Charge la catégorie de questions
	$cat = array( 'id'=>0, 'name'=>'', 'desc'=>'', 'questions'=>0 );
	if( isset($_GET['name']) ){
		$cat['name'] = ucfirst(trim($_GET['name']));
	}
	if( isset($_GET['cat']) ){
		$cat = ria_mysql_fetch_array(faq_categories_get($_GET['cat']));
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_cat = fld_translates_get( CLS_FAQ_CAT, $cat['id'], $lng, $cat, array(_FLD_FAQ_CAT_NAME=>'name', _FLD_FAQ_CAT_DESC=>'desc' ), true );
			$cat['name'] = $tsk_cat['name'];
			$cat['desc'] = $tsk_cat['desc'];
		}
	}
	
	if( isset($_GET['cat']) ){
		$page_title = _('Catégorie ').htmlspecialchars( $cat['name'] );
	}else{
		$page_title = _('Créer une catégorie de questions');
	}
	
	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Foire Aux Questions').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>

	<?php
		// Affichage des messages d'erreur et de succès
		if (isset($_SESSION['referencement_edit_success'])) {
			$success = $_SESSION['referencement_edit_success'];
			unset($_SESSION['referencement_edit_success']);
		}
		if (isset($_SESSION['referencement_edit_error'])) {
			$error = $_SESSION['referencement_edit_error'];
			unset($_SESSION['referencement_edit_error']);
		}

		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	
		if( isset($success) ){
			print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
		}
    
    	// Affiche le menu de langue
		if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ){
			print view_translate_menu( 'category.php?cat='.$cat['id'], $lng );
		}
	?>

	<form action="category.php?cat=<?php print $cat['id']; ?>&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return validFaqCategoryForm(this)">
		<input type="hidden" name="questions" value="<?php print $cat['questions']; ?>" />
		<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ){ ?>
		<?php if( tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabReferencement" value="<?php print _('Référencement'); ?>" <?php if( $tab=='ref' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabImages" value="<?php print _('Images'); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">
		<?php if( $tab=='general' ){ ?>
		<table id="table-faq-categorie-general">
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
					<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return cancelFaqCategory();" />
					<?php if( $cat['id']>0 && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_DEL') ){ ?>
					<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return confirmFaqCategoryDel(this.form)" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td><label for="name"><span class="mandatory">*</span><?php print _('Intitulé :'); ?></label></td>
				<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($cat['name']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Description :'); ?></label></td>
				<td><textarea class="tinymce" name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($cat['desc']); ?></textarea></td>
			</tr>
		</tbody>
		</table>
		<?php }elseif( $tab=='ref' ){
			print view_admin_tab_referencement(CLS_FAQ_CAT, $_GET['cat'], $lng);
		?>			
		<?php } elseif( $tab=='images' ) {
			view_admin_img_table(CLS_FAQ_CAT, $cat['id']);
		}
		?>
	</div>
	<br/>
	<table id="faq_questions" class="checklist">
		<caption><?php print _('Liste des questions'); ?></caption>
	<thead>
		<tr>
			<th id="qst-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="qst-name"><?php print _('Intitulé de la question'); ?></th>
			<th id="qst-pub"><?php print _('Publier'); ?></th>
			<?php if( isset($ordered) && $ordered && $can_move ){ ?><th id="qst-pos"><?php print _('Déplacer'); ?></th><?php } ?>
		</tr>
	</thead>
	<tbody>
		<?php
			if( $cat['id']<=0 ){?>
				<tr><td colspan="<?php print isset($ordered) && $ordered && $can_move ? 4 : 3?>"><?php print _('Aucune question'); ?></td></tr> <?php
			}
			else{
				$questions = faq_questions_get(0,$cat['id']);
				if( !ria_mysql_num_rows($questions) ){?>
					<tr><td colspan="<?php print isset($ordered) && $ordered && $can_move ? 4 : 3?>"><?php print _('Aucune question'); ?></td></tr> <?php
				}
				else {
					$count = ria_mysql_num_rows($questions); $current = 0;
					while( $r = ria_mysql_fetch_array($questions) ){
						print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable">';
						print '<td headers="qst-sel"><input type="checkbox" class="checkbox" name="qst[]" value="'.$r['id'].'" /></td>';
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_EDIT') ){
							print '<td headers="qst-name"><a href="question.php?qst='.$r['id'].'&amp;cat='.$cat['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
						}else{
							print '<td headers="qst-name">'.htmlspecialchars($r['name']).'</td>';							
						}
						print '<td headers="qst-pub" class="align-center">'.($r['publish'] ? _('Oui') : _('Non')).'</td>';
						if( isset($ordered) && $ordered && $can_move){
							print '<td headers="qst-pos" class="ria-cell-move align-center">';
							print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
							print '</td>';
						}
						print '</tr>';
						$current++;
					}
				}
			}
		?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="<?php print isset($ordered) && $ordered && $can_move ? 4 : 3; ?>" class="align-left">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_DEL') ){ ?>
				<input type="submit" name="delqst" class="btn-del" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer les questions sélectionnées'); ?>" onclick="return confirmFaqQuestionDelList();" />
				<?php } ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_ADD') ){ ?>
				<div class="float-right">
					<label for="qstname"><?php print _('Ajouter une question :'); ?></label>
					<input type="text" name="qstname" id="qstname" maxlength="75" />
					<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
				</div>
				<?php } ?>
			</td>
		</tr>
		<?php if(isset($ordered) && $can_move){ ?>
		<tr><td colspan="<?php print $ordered ? 4 : 3; ?>" class="tfoot-grey align-left">
			<label><?php print _('Trier les questions par ordre :'); ?></label>
			<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php print _('Alphabétique'); ?></label>
			<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php print _('Personnalisé'); ?></label>
			<input type="submit" name="orderby" value="<?php print _('Appliquer'); ?>" />
		</td></tr>
		<?php } ?>
	</tfoot>
	</table>
	
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
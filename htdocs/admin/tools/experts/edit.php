<?php

	require_once('experts.inc.php');

	unset($error);

	// Vérifie l'identifiant passé en argument
	if( isset($_GET['ast']) && $_GET['ast']!=0 && !exp_assistants_exists($_GET['ast']) ){
		header('Location: index.php?1');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_GET['ast']) ){
		if( !exp_assistants_del($_GET['ast']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'assistant.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['name']) || !trim($_POST['name']) ){
			$error = _("Veuillez indiquer le titre de l'assistant.");
		}else{
			if( !isset($error) ){
				if( isset($_GET['ast']) && $_GET['ast']==0 ){
					if( !exp_assistants_add( $_POST['name'], $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'assistant.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}else{
					if( !exp_assistants_update( $_GET['ast'], $_POST['name'], $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'assistant.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}

			if( !isset($error) ){
				header('Location: index.php');
				exit;
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Assistants Experts').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Assistants Experts'); ?></h2>

	<?php
		$ast = array('id'=>0,'name'=>'','desc'=>'');

		if( isset($_GET['name']) )
			$ast['name'] = ucfirst(trim($_GET['name']));

		if( isset($_GET['ast']) && is_numeric($_GET['ast']) && $_GET['ast']>0 )
			$ast = ria_mysql_fetch_array(exp_assistants_get($_GET['ast']));

		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	?>

	<form action="edit.php?ast=<?php print $ast['id']; ?>" method="post" onsubmit="return astValidForm(this)">
	<table>
		<caption><?php print _('Fiche Assistant'); ?></caption>
	<tfoot>
		<tr><td colspan="2">
			<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
			<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return astCancelEdit()" />
			<?php if( $ast['id']>0 ){ ?>
			<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return astConfirmDel()" />
			<?php } ?>
		</td></tr>
	</tfoot>
	<tbody>
		<tr>
			<td><label for="name"><span class="mandatory">*</span> <?php print _('Titre de l\'assistant :'); ?></label></td>
			<td><input type="text" name="name" value="<?php print htmlspecialchars($ast['name']); ?>" maxlength="125" /></td>
		</tr>
		<tr>
			<td><label for="desc"><span class="mandatory">*</span> <?php print _('Description :'); ?></label></td>
			<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($ast['desc']); ?></textarea></td>
		</tr>
		<tr>
			<th colspan="2"><?php print _('Première question posée par l\'assistant'); ?></th>
		</tr>
		<tr>
			<td><label for="qst-name"><?php print _('Intitulé de la première question :'); ?></label></td>
			<td><input type="text" name="qst-name" id="qst-name" maxlength="45" /></td>
		</tr>
		<tr>
			<td><label for="qst-type"><?php print _('Type de question :'); ?></label></td>
			<td><select name="qst-type" id="qst-type">
				<option value=""><?php print _('Veuillez indiquer le type de réponse attendu'); ?></option>
				<?php
					$types = exp_questions_types_get();
					while( $t = ria_mysql_fetch_array($types) ){
						print '<option value="'.$t['id'].'">'.$t['name'].'</option>';
					}
				?>
			</select></td>
		</tr>
		<tr>
			<td><label for="qst-desc"><?php print _('Description de la première question :'); ?></label></td>
			<td><textarea name="qst-desc" id="qst-desc" cols="40" rows="8"></textarea></td>
		</tr>
		<tr>
			<td><label><?php print _('Réponses acceptées :'); ?></label></td>
			<td>
				<table style="width: 100%">
				<thead>
					<tr>
						<th><?php print _('Intitulé des réponses'); ?></th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td><?php print _('Intitulé de la première réponse'); ?></td>
					</tr>
				</tbody>
				</table>
			</td>
		</tr>
	</tbody>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
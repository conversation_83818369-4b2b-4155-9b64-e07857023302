<?php

/**	\file check-mapping.php
 * 
 * 	Ce fichier est chargé de réaliser un import configuré via le fichier mapping.php
 * 
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

// Cette page doit obligatoire être appelée en Ajax
if( !IS_AJAX ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

if( !isset($_POST['import']) && !(isset($_POST['save']) || isset($_POST['import'])) ){
	header('location: /admin');
	exit;
}

require_once('imports.inc.php');

$r_import = ipt_imports_get($_POST['imp_id'], false, false, '', 0, 0, false, false, '', '', array(), null, -1, null);

$import = null;
if ($r_import && ria_mysql_num_rows($r_import)) {
	$import = ria_mysql_fetch_assoc($r_import);

	// Récupère la sous classe de l'import
	if( $import['info'] != '' ){
		$import['info'] = json_decode( $import['info'], true );

		if( isset($import['info']['sub_class']) && $import['info']['sub_class'] == 'model' ){
			$orders_models = true;
		}
	}	
}

$errors = array();
$msg = _('Une erreur est survenue lors de l\'enregistrement. Merci de vérifier vos informations et réessayez.');
if( !isset($_POST['imp_id']) || !is_numeric($_POST['imp_id']) || $_POST['imp_id'] <= 0 ){
	$errors['all'][] = $msg;
	error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));	
}

/* execution de l'import */
if( isset($_POST['exec']) && $_POST['exec'] == true ){
	require_once('imports.inc.php');
	require_once('ria.queue.inc.php');
	$r_import = ipt_imports_get($_POST['imp_id'], false, false, '', 0, 0, false, false, '', '', array(), null, -1, null);
	if (!$r_import || !ria_mysql_num_rows($r_import)) {
		echo json_encode(array(
			'error'=> _('Impossible de traiter votre import.')
		));
		exit;
	}

	$import = ria_mysql_fetch_assoc($r_import);
	$state = $import['state'];
	$line_count = $import['line_count'];
	if( $state ){
		if ($import['period'] != '') {
			ipt_imports_set_state($_POST['imp_id'], 'pending');
			echo json_encode(array(
				'success'=> _('Votre import a bien été enregistré. Il sera exécuté selon votre configuration.')
			));
			exit;
		}
		if( $state != 'pending' ){
			echo json_encode(array(
				'error'=> _('Votre import n\'est pas en attente de traitement.')
			));
			exit;
		}

		$is_backup = 0;
		if( isset($_POST['is_backup']) && $_POST['is_backup'] !== 'false' ){
			$is_backup = 1;
		}
		try{
			// Ajoute l'import dans la file d'attente
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_IMPORT_EXEC, array(
				'imp_id' => $_POST['imp_id'],
				'is_backup' => $is_backup,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
		if( tnt_tenants_is_yuto_essentiel() ){
			$return = array(
				'success' => _('Votre demande d\'import a bien été prise en compte, elle sera traitée prochainement. Vous pouvez suivre sa progression sur cette page <a href="/admin/tools/imports/index.php">Imports</a>.'),
			);
		}else{
			$return = array(
				'success' => _('Votre demande d\'import a bien été prise en compte, elle sera traitée prochainement. Vous pouvez suivre sa progression sur cette page <a href="/admin/tools/imports/index.php">Outils • Imports</a>.'),
			);
		}
		echo json_encode($return);
		exit;
	}
	exit;
}

if( !isset($_POST['cls_id']) || !is_numeric($_POST['cls_id']) || $_POST['cls_id'] <= 0 ){
	$errors['all'][] = $msg;
	error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));
}

if( !isset($_POST['mapping']) || !is_array($_POST['mapping']) || count($_POST['mapping']) <= 0 ){
	$errors['all'][] = $msg;
	error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));
}

if( !empty($errors) ){
	echo json_encode($errors);
	exit;
}


$obj_id = false;
$line_id = false;

$payment = array();
$prc_id = array();
$cat_id = array();

foreach($_POST['mapping'] as $pos => $input){
	if( !isset($input['code']) || $input['code'] == '' || $input['code'] == 'DEFAULT' ){
		continue;
	}
	
	// Détermine si le code schéma est lié à une correspondance de valeurs (ex Civilité avec "Mme" dans le fichier correspond à "Madame" dans RiaShop)
	if( isset($input['code']) && in_array($input['code'], array('PRC_DISCOUNT_TYPE', 'USR_ADR_TYPE_ID', 'USR_CIV', 'USR_PRF_ID', 'USR_PAYMENT', 'USR_CAT_ID', 'USR_PRC_ID', 'PRC_USR_PRC', 'ORD_STATE', 'ORD_SRV', 'ORD_PAY', 'ORD_CARD', 'ORD_WST_ID')) ){
		// Contrôle de la présence d'au moins une correspondance de valeurs		
		$vals = isset($input['options']['vals']) ? $input['options']['vals'] : array();
		$vals_error = false;
		
		if( !is_array($vals) || !count($vals) ){
			$vals_error = true;
		}else{
			foreach( $vals as $key_val => $one_val ){
				if( trim($one_val) == '' ){
					$vals_error = true;
				}
			}
		}

		// Lève une erreur dans le cas où aucune valeur de correspondance n'a été saisie
		// L'import de pourra ni être sauvegardé ni exécuté
		if( $vals_error ){
			$errors[$pos] = _('Merci de renseigner vos correspondances.');
		}
	}

	// Détermine si le code schéma est lié à une données d'adresse postal, dans ce cas on contrôle que l'information pour savoir
	// s'il s'agit de l'adresse de livraison et/ou de facturation est renseigné
	if( isset($input['code']) && in_array($input['code'], array('USR_ADR_TYPE_ID', 'USR_CIV', 'USR_ADDRESS1', 'USR_ADDRESS2', 'USR_ZIP_CODE', 'USR_CITY', 'USR_COUNTRY', 'USR_LATITUDE', 'USR_LONGITUDE', 'USR_PHONE', 'USR_FAX', 'USR_CELLPHONE', 'USR_PHONE_WORK', 'USR_SOCIETY', 'USR_FIRSTNAME', 'USR_LASTNAME', 'USR_ADR_REF')) ){
		$one_adr_selected = false;

		if( isset($input['options']['inv_adr'])  ){
			$one_adr_selected = true;
		}
		if( isset($input['options']['dlv_adr'])  ){
			$one_adr_selected = true;
		}

		if( !$one_adr_selected ){
			$errors[$pos] = _('Merci de déterminer si cette information doit être utilisée sur l\'adresse de facturation et/ou sur l\'adresse de livraison');
		}
	}
	
	$cls_id = $_POST['cls_id'];

	if( $input['code'] == 'ORD_PRD_ID' ){
		$ord_prd_id = true;
	}

	if( $input['code'] == 'ORD_PRD_QTE' ){
		$ord_prd_qte = true;
	}

	if( $input['code'] == 'ORD_PRD_PRICE' ){
		$ord_prd_price = true;
	}
 
	// spécifique au champ avancé
	if( preg_match("/^FLD_[0-9]*/", $input['code']) ){
		$fld_post = explode('_', $input['code']);
		$_POST['mapping'][$pos]['code'] = $input['code'] = $fld_post[0];
		$_POST['mapping'][$pos]['options']['fld-id'] = $input['options']['fld-id'] = $fld_post[1];
	}

	if( in_array($input['code'], array('FLD', 'IMAGE')) ||  $input['code'] == 'BACKUP_LINE_ACTION' ){
		$cls_id = 0;
	}

	// Récupère le schéma pour la colonne choisie
	$r = ipt_schemas_get($cls_id, $input['code']);

	if( !$r ){
		$errors[$pos] = str_replace(
			'#param[nom]#',
			$input['name'],
			_('L\'association de données sélectionnée pour la colonne #param[nom]# est incorrecte. Merci de vérifier.')
		);
		continue;
	}
			
	// Charge le schéma pour la colonne choisie
	$schema = ria_mysql_fetch_assoc($r);

	$_POST['mapping'][$pos]['options']['is-obj'] = false;


	if( $schema['is_obj_id'] ){
		list($cls, $id_type) = array_map('strtolower',explode('_', $schema['code'], 2));

		if( !in_array($id_type, ipt_rows_get_id_types($schema['code'])) ){
			if( !isset( $input['options']['id-type']) || !in_array( $input['options']['id-type'], ipt_rows_get_id_types( $schema['code']) ) ){
				$errors[$pos] = str_replace(
					'#param[nom]#',
					$input['name'],
					_('Le type d\'identifiant n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
				);
			}else{
				$id_type = $input['options']['id-type'];
			}
		}else{
			$_POST['mapping'][$pos]['options']['id-type'] = $id_type;
		}
		switch( $schema['cls_id']){
			case CLS_PRICE:{
				if( isset($input['options']['is-obj']) ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id  = true;
				}else{
					$_POST['mapping'][$pos]['options']['is-obj'] = false;
				}
				break;
			}
			case CLS_USER:{
				$usr_condition = $config['tnt_yuto_essentiel'] ? isset($input['code']) && $input['code'] == 'USR_REF' : isset($input['options']['is-obj']);
				if( $usr_condition ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id  = true;
				}else{
					$_POST['mapping'][$pos]['options']['is-obj'] = false;
				}
				break;
			}
			case CLS_PRODUCT:{
				if( isset($input['options']['is-obj']) ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id  = true;
				}else{
					$_POST['mapping'][$pos]['options']['is-obj'] = false;
				}
				break;
			}
			case CLS_STOCK:{
				if( $input['code'] == 'STK_DPS_ID' ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id  = true;
				}
				break;
			}
			case CLS_CATEGORY:{
				if( $input['code'] == 'CAT_REF' || $input['code'] == 'CAT_ID' ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id  = true;
				}
				break;
			}
			case CLS_BRAND:{
				if( $input['code'] == 'BRD_REF' || $input['code'] == 'BRD_ID' ){
					$_POST['mapping'][$pos]['options']['is-obj'] = true;
					$obj_id = true;
				}
				break;
			}
			case CLS_ORDER:{
				if( isset($input['options']['is-obj']) ){
					if( $input['code'] == 'ORD_REF' || $input['code'] == 'ORD_ID' || $input['code'] == 'ORD_PIECE' ){
						$_POST['mapping'][$pos]['options']['is-obj'] = true;
						$obj_id = true;
					}
				}
				break;
			}
		}
	}else{
		$_POST['mapping'][$pos]['options']['id-type'] = null;
	}

	if( $schema['is_translatable'] && count($config['i18n_lng_used']) > 1 ){

		if( !isset( $input['options']['lang']) || !in_array( $input['options']['lang'], $config['i18n_lng_used'] ) ){
			$errors[$pos] = str_replace(
				'#param[nom]#',
				$input['name'],
				_('La langue utilisée pour la colonne #param[nom]# est incorrecte. Merci de vérifier.')
			);
		}
	}else{
		$_POST['mapping'][$pos]['options']['lang'] = $config['i18n_lng'];
	}
	// vérification des association
	if( $schema['is_relation'] ){
		//vérification si l'association a besoin d'une action oi non
		if( $schema['rel_require_action'] ){
			// si oui on vérifie que le type d'action est bien présent
			if( !isset( $input['options']['rel-action']) || !in_array( $input['options']['rel-action'], ipt_imports_get_import_actions( $schema['code'])) ){
				$errors[$pos] = str_replace(
					'#param[nom]#',
					$input['name'],
					_('L\'action sélectionnée pour la colonne #param[nom]# est incorrecte. Merci de vérifier.')
				);
			}
		}else{
			$_POST['mapping'][$pos]['options']['rel-action'] = null;
		}

		// suivant le type de code certain check personalisé serons réalisé ici
		switch( $schema['code'] ){
			// si c'est une catégorie on vérifie si le type d'identifiant est bon
			// pas besoin de type de relation ni d'identifiant de champ avancé
			case 'PRD_CAT' :
				if( !isset($input['options']['rel-id']) || !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])  ) ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Le type d\'identifiant pour la relation n\'est pas saisie ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
					);
				}

				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				break;
			case 'PRD_COLISAGE' :
				$_POST['mapping'][$pos]['options']['rel-id'] = null;
				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				break;
			// si c'est un champ avancé on vérifie si celui si existe
			// pas besoin de type d'identifiant en relation
			// ni de type de relation
			case 'FLD' :
				if( !isset( $input['options']['fld-id']) || $input['options']['fld-id'] == 0 ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Il faut renseigner un champ avancé pour la colonne #param[nom]#. Merci de vérifier.')
					);
				}elseif( $input['options']['fld-id'] != 0  && !fld_fields_exists( $input['options']['fld-id'] ) ){
					$errors[$pos] = _('Le champ avancé saisi n\'existe pas. Merci de vérifier.');
				}
				$_POST['mapping'][$pos]['options']['rel-id'] = null;
				$_POST['mapping'][$pos]['options']['rel-type'] = null;

				if( isset($input['options']['vals']) ){
					$first = true;
					$tmp = array();
					foreach( $input['options']['vals'] as $key => $value ){
						if( $first ){
							if(trim($value) == '' ){
								$errors[$pos] = str_replace(
									'#param[nom]#',
									$input['name'],
									_('Merci d\'indiquer les valeurs pour #param[nom]#')
								);
								break;
							}
							$first = false;
						}
						$tmp[$key] = array_map('trim',explode(',', $value) );
					}
					if( !isset($errors[$pos]) ){
						$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
					}
				}else{
					$_POST['mapping'][$pos]['options']['vals'] = '';
				}
				if( isset($input['options']['sep']) && trim($input['options']['sep']) == '' ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Il manque un séparateur pour #param[nom]#. Merci de vérifier.')
					);
				}
				break;
			case 'PRC_PRD_ID':
			case 'ORD_PRD_ID':
				if( !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])  ) ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Le type d\'identifiant des produits n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
					);
				}
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				break;
			case 'PRD_CANONICAL_LINK_PRD':
				if( !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])) ){
					if( $input['options']['rel-action'] != 'del') {
						$errors[$pos] = str_replace(
							'#param[nom]#',
							$input['name'],
							_('Le type d\'identifiant pour la relation n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
						);
					}else{
						$_POST['mapping'][$pos]['options']['rel-id']= null;
					}         
					
				}
				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				break;
			case 'PRD_CANONICAL_LINK_CAT':
				if( !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])) ){
					if( $input['options']['rel-action'] != 'del') {
						$errors[$pos] = str_replace(
							'#param[nom]#',
							$input['name'],
							_('Le type d\'identifiant pour la relation n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
						);
					}else{
						$_POST['mapping'][$pos]['options']['rel-id']= null;
					}         
					
				}
				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				break;
			// Si c'est une relation entre client il n'y a pas de type de relation
			case 'USR_RELATION':
			case 'USR_SELLER':
			case 'USR_HIEARCH_PARENT':
			case 'USR_HIEARCH_ENFANT':
			case 'USR_RELATION_PARENT':
			case 'USR_RELATION_ENFANT':
			case 'USR_RELATION_SOCIETY':
			case 'ORD_USR':
			case 'ORD_SELLER':
			case 'PRC_USR':
			case 'PRC_USR_PRC':
				if( !isset($input['options']['rel-id']) || !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])  ) ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Le type d\'identifiant pour la relation n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
					);
				}
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				$_POST['mapping'][$pos]['options']['rel-type'] = null;
				break;
			// par default on vérifie le type de relation et type de l'identifiant en relation
			default :
				if( !isset( $input['options']['rel-type']) ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Le type de relation utilisé pour la colonne #param[nom]# est incorrect. Merci de vérifier.')
					);
				}
				if( !isset($input['options']['rel-id']) || !in_array( $input['options']['rel-id'], ipt_rows_get_id_types( $schema['code'])  ) ){
					$errors[$pos] = str_replace(
						'#param[nom]#',
						$input['name'],
						_('Le type d\'identifiant pour la relation n\'est pas saisi ou ne correspond pas pour la colonne #param[nom]#. Merci de vérifier.')
					);
				}
				$_POST['mapping'][$pos]['options']['fld-id'] = 0;
				break;
		}
		
		// On vérifie si plusieurs plusieurs objet sont accepter si il y a un séparateur 
		// certaine valeur de type d'identifiant en relation comme parent ne sont obligé d'avoir de séparateur
			
	}else{
		// si ce n'est pas une relation on initialise les valeurs
		$_POST['mapping'][$pos]['options']['rel-type'] = null;
		$_POST['mapping'][$pos]['options']['fld-id'] = 0;
		$_POST['mapping'][$pos]['options']['cat-first'] = 0;
		$_POST['mapping'][$pos]['options']['rel-id'] = null;
		$_POST['mapping'][$pos]['options']['rel-action'] = null;
	}


	// vérification si le mapping a besoin de plusieurs champ a mappé
	if( isset($input['options']['vals']) ){
		switch($input['code']){
			case 'PRC_DISCOUNT_TYPE':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value ){
					$tmp[$key] = trim(strtolower($value));
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'PRC_USR_PRC':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value){
					$tmp[$key] = trim(strtolower($value));		
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_ADR_TYPE_ID':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value ){
					$tmp[$key] = trim(strtolower($value));
				}
				if( isset($input['options']['inv_adr'])  ){
					$tmp['inv_adr'] = 1;
				}
				if( isset($input['options']['dlv_adr'])  ){
					$tmp['dlv_adr'] = 1;
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_CIV':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value ){
					if($key == "other"){
						$tmp[4] = trim(strtolower($value));
					}else{
						$tmp[$key] = trim(strtolower($value));
					}
					
				}
				if( isset($input['options']['inv_adr']) ){
					$tmp['inv_adr'] = 1;
				}
				if( isset($input['options']['dlv_adr']) ){
					$tmp['dlv_adr'] = 1;
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_PRF_ID':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value){	
					$tmp[$key] = trim(strtolower($value));
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_CAT_ID':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value){
				$tmp[$key] = trim(strtolower($value));
						
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_PRC_ID':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value){
					$tmp[$key] = trim(strtolower($value));		
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'USR_PAYMENT':
				if( isset($_POST['mapping'][$pos]['options']['sepPayment'])){
					$_POST['mapping'][$pos]['options']['sep']=$_POST['mapping'][$pos]['options']['sepPayment'];
				}				
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value){
					$tmp[$key] = trim(strtolower($value));
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			case 'ORD_STATE':
			case 'ORD_PAY':
			case 'ORD_CARD':
			case 'ORD_SRV':
			case 'ORD_WST_ID':
				$tmp = array();
				foreach( $input['options']['vals'] as $key => $value ){
					$tmp[$key] = trim(strtolower($value));	
				}
				$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
				break;
			default:
				if( $schema['require_vals'] ){
					$first = true;
					$tmp = array();
					foreach( $input['options']['vals'] as $key => $value ){
						if( $first ){
							if( trim($value) == '' ){
								$errors[$pos] = _('Merci de renseigner le caractère de validation.');
								break;
							}
							$first = false;
						}
						if($schema['code'] === 'PRD_LANG'){
							$tmp[$key] = trim($value);
						}else{
							$tmp[$key] = array_map('trim',explode(',', $value) );
						}
					}
					if( !isset($errors[$pos]) ){
						$_POST['mapping'][$pos]['options']['vals'] = json_encode($tmp);
					}
				}
			break;
		}
	}
	
	// Traitement spécifique
	switch( $input['code'] ){
		case 'PRD_PRICE':
			$vals = array();
			if( isset($input['options']['gu_catf']) && $input['options']['gu_catf'] ){
				$vals['gu_catf'] =  $input['options']['gu_catf'];
			}else{
				$vals['gu_catf'] = '';
			}
			if( isset($input['options']['tarif']) ){
				if( trim($input['options']['tarif']) == '' ){
					$input['options']['tarif'] = 'ht';
				}
				$vals['tarif'] =  $input['options']['tarif'];
			}
			if( isset($input['options']['promo']) ){
				if( trim($input['options']['promo']) == '' ){
					$input['options']['promo'] = 'ht';
				}
				$vals['promo'] =  $input['options']['promo'];
				if(isset($input['options']['include-promo'])){
					$vals['include-promo'] = $input['options']['include-promo'];
				}
			}

			$_POST['mapping'][$pos]['options']['vals'] = json_encode($vals);
			break;
		case 'PRD_STOCK':
			if( isset($input['options']['prd_dps']) && $input['options']['prd_dps'] ){
				$_POST['mapping'][$pos]['options']['vals'] =  $input['options']['prd_dps'];
			}else{
				$_POST['mapping'][$pos]['options']['vals'] = prd_deposits_get_main();
			}
			break; 
		case 'USR_ADDRESS1': 
		case 'USR_ADDRESS2': 
		case 'USR_ZIP_CODE': 
		case 'USR_CITY': 
		case 'USR_COUNTRY': 
		case 'USR_LATITUDE': 
		case 'USR_LONGITUDE': 
		case 'USR_PHONE': 
		case 'USR_FAX': 
		case 'USR_CELLPHONE': 
		case 'USR_PHONE_WORK':
		case 'USR_SOCIETY':
		case 'USR_FIRSTNAME':
		case 'USR_LASTNAME':
		case 'USR_ADR_REF':
			$tmp = array();
			if( isset($input['options']['inv_adr']) ){
				$tmp['inv_adr'] = 1;
			}
			if( isset($input['options']['dlv_adr']) ){
				$tmp['dlv_adr'] = 2;
			}

			if( count($tmp) ){
				$_POST['mapping'][$pos]['options']['vals'] = json_encode( $tmp );
			}
			break;
		case 'PRC_DISCOUNT_VALUE':
			$_POST['mapping'][$pos]['options']['is-obj'] = true;
			$obj_id = true;
			break;

	}


	// on vérifie si le schéma a besoin d'unité ou non
	if( $schema['is_unit'] ){
		$unites = array_keys(ria_unites_get($schema['unit_type']));
		if( !isset( $input['options']['unit']) || !in_array( $input['options']['unit'], $unites) ){
			$errors[$pos] = 'Merci de saisir une unité valide.';
		}
	}else{
		$_POST['mapping'][$pos]['options']['unit'] = '';
	}

	if( !isset( $input['options']['cat-first']) ){
		$_POST['mapping'][$pos]['options']['cat-first'] = 0;
	}
}

//si aucun profil par défaut à été renseigné et qu'on demande un import
if(!is_null($import) && isset($_POST['default_prf']) && !$_POST['default_prf'] && $import['cls_id'] == CLS_USER && $import['action'] != 'del' && $import['action'] != 'upd' && isset($_POST['import']) ){
	$errors['all'][] = _('Veuillez renseigner un profil par défaut.');
}

// Si il n'y a aucun identifiant de ligne pour l'import de commande
if( $import['backup'] != false && $import['cls_id'] == CLS_ORDER && !$ord_prd_id ){
	$errors['all'][] = _('L\'association "Identifiant du produit" est obligatoire.');
}

// si il n'y a pas une colonne qui représente un identifiant d'objet
$auto_usr_ref = false;
if( !$obj_id ){
	// si pas de code client dans l'import et c'est un import de client et c'est un yuto essentiel
	// on ajoute automatiquemet le code client
	if( !is_null($import) && $config['tnt_yuto_essentiel'] && $import['cls_id'] == CLS_USER && $import['action'] == 'add'){
		$auto_usr_ref = true;
	}else{
		// Charge un tableau des classes accessible via l'import
		require_once('view.imports.inc.php');
		
		// Personnalisation du message d'erreur dans le cas où aucun identifiant d'objet n'a été sélection
		// Afin que le message soit plus clair pour l'import des comptes clients sur les Yuto Essentiel
		$text_error_id = _('L\'association doit contenir au moins une colonne contenant un identifiant de #param[class]#.');
		if( RegisterGCP::getPackage($config['tnt_id']) == 'essentiel' ){
			$text_error_id = _('L\'information "Code client", "Email" ou "Siret" n\'est pas présente comme correspondance, il s\'agit d\'une information obligatoire.');
		}

		$errors['all'][] = str_replace(
			'#param[class]#',
			strtolower2( view_import_get_name($import['cls_id'], isset($import['info']['sub_class']) ? $import['info']['sub_class'] : '') ),
			$text_error_id
		);
	}
}

if( !ipt_mapping_del($_POST['imp_id']) ){
	$errors['all'][] = $msg;
	error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));
}

// L'import sera sauvegardé même s'il y a des erreurs, permettant ainsi de le reprendre plus tard
foreach( $_POST['mapping'] as $pos => $input ){
	if( !isset($input['code']) || $input['code'] == ''){
		continue;
	}
	
	// On enregistre le fait d'avoir choisi "Ne pas importer"
	if( $input['code'] == 'DEFAULT' ){
		ipt_mapping_add($_POST['imp_id'], $pos, 'DEFAULT', 'Ne pas importer', ipt_mapping_gen_alias('Ne pas importer'));
		continue;
	}

	if(!isset($_POST['mapping'][$pos]['options']['vals'])){
		$_POST['mapping'][$pos]['options']['vals'] = '';
	}elseif( is_array($_POST['mapping'][$pos]['options']['vals']) ){
		$_POST['mapping'][$pos]['options']['vals'] = json_encode( $_POST['mapping'][$pos]['options']['vals'] );
	}

	// Initialise des informations dans le cas d'un enregistrement partiel
	if( !isset($_POST['mapping'][$pos]['options']['id-type']) ){
		$_POST['mapping'][$pos]['options']['id-type'] = null;
	}
	if( !isset($_POST['mapping'][$pos]['options']['rel-type']) ){
		$_POST['mapping'][$pos]['options']['rel-type'] = null;
	}
	if( !isset($_POST['mapping'][$pos]['options']['lang']) ){
		$_POST['mapping'][$pos]['options']['lang'] = '';
	}
	if( !isset($_POST['mapping'][$pos]['options']['sep']) ){
		$_POST['mapping'][$pos]['options']['sep'] = '';
	}
	if( !isset($_POST['mapping'][$pos]['options']['rel-action']) ){
		$_POST['mapping'][$pos]['options']['rel-action'] = null;
	}
	if( !isset($_POST['mapping'][$pos]['options']['rel-id']) ){
		$_POST['mapping'][$pos]['options']['rel-id'] = null;
	}

	if( !$id = ipt_mapping_add(
		$_POST['imp_id'],
		$pos,
		$input['code'],
		$input['name'],
		ipt_mapping_gen_alias( $input['name']),
		$_POST['mapping'][$pos]['options']['id-type'],
		$_POST['mapping'][$pos]['options']['rel-type'],
		$_POST['mapping'][$pos]['options']['lang'],
		$_POST['mapping'][$pos]['options']['sep'],
		$_POST['mapping'][$pos]['options']['rel-action'],
		$_POST['mapping'][$pos]['options']['rel-id'],
		0,
		$_POST['mapping'][$pos]['options']['fld-id'],
		$_POST['mapping'][$pos]['options']['vals'],
		$_POST['mapping'][$pos]['options']['unit'],
		$_POST['mapping'][$pos]['options']['cat-first'],
		$_POST['mapping'][$pos]['options']['is-obj']
	) ){
		$errors['all']['add'] = $msg;
		error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));
	}
}

// Enregistre le profil par défaut
if(isset($_POST['default_prf'])){
	if( !$id = ipt_mapping_add(
		$_POST['imp_id'],
		ipt_import_get_max_pos( $_POST['imp_id'] ) + 1,
		'USR_DEFAULT_PRF',
		_('Profil par défaut'),
		ipt_mapping_gen_alias( _('Profil par défaut')),
		'ref',
		null,
		'',
		'',
		null,
		null,
		0,
		0,
		$_POST['default_prf']
	) ){
		$errors['all']['add'] = _('Une erreur est survenue lors de l\'enregistrement du profil par défaut. Merci de vérifier vos informations et réessayez.');
	}
}

// Si aucune erreur n'est détectée, on enregistre l'ajout automatique du code client comme lien entre le fichier et RiaShop
// (sous condition : Yuto Essentiel et non défini par le mapping)
if( empty($error) ){
	if( $auto_usr_ref ){
		if( !$id = ipt_mapping_add(
			$_POST['imp_id'],
			ipt_import_get_max_pos( $_POST['imp_id'] ) + 1,
			'USR_REF',
			_('Code client'),
			ipt_mapping_gen_alias( _('Code client')),
			null, null, '', '', null, null, 0, 0, '', '', 0, true
		) ){
			$errors['all']['add'] = _('Une erreur est survenue lors de la configuration de l\'autogénération du code client. Merci de vérifier vos informations et réessayez.');
		}
	}

}

if( isset($_POST['import']) && !empty($errors) ){
	$errors['success'] = str_replace('#param[url]#', '/admin/tools/imports/index.php', _('Vos correspondances de données ont bien été sauvegardées, les indications en rouge ci-dessous vous indiquent les actions qu’il reste à effectuer. Retrouvez votre fichier dans la liste des <a href="#param[url]#">Imports en cours</a>.'));
	echo json_encode($errors);
	exit;
}

// changer l'état suivant le bouton cliqué
if( isset($_POST['import']) ){
	if( !ipt_imports_set_state( $_POST['imp_id'], 'pending' ) ){
		$errors['all']['upd'] = $msg;
		error_log(__FILE__.':'.__LINE__.'[Tenant '.$config['tnt_id'].']'.print_r($_SERVER, true).print_r($_POST, true));
	}
}

$return = array(
	'import' => false,
	'is_backup' => false,
);

if( isset($_POST['save']) ){
	$return['success'] = str_replace('#param[url]#', '/admin/tools/imports/index.php', _('Vos correspondances de données ont bien été sauvegardées, retrouvez votre fichier dans la liste des <a href="#param[url]#">Imports en cours</a>.'));
}elseif( isset($_POST['import']) ){
	$return['import'] = true;
	if( isset($_POST['backup']) ){
		$return['is_backup'] = true;
	}
}
echo json_encode($return);
exit;

<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT_BACKUP');

if( !strstr($_SESSION['usr_email'], '@riastudio.fr') && !strstr($_SESSION['usr_email'], '@kontinuum.fr') || strstr($_SESSION['usr_email'], '@yuto.com') ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}
require_once('imports.inc.php');


require_once('admin/skin/header.inc.php');
?>
<div class="backups">
	<h2><?php print _('Exécution d\'une copie de sauvegarde'); ?></h2>
	<form method="post" action="mapping.php" id="imp-backup">
		<label for="import-id"><?php print _('Sélectionnez l\'import pour lequel il faut restaurer la copie sauvegarde :'); ?></label>
		<select name="imp" id="import-id">
		<?php
			$r = ipt_imports_get();
			if( !$r ){ ?>
				<option value=""><?php print _('Aucun import pour le moment'); ?></option>
			<?php }else{
				while($imp = ria_mysql_fetch_assoc($r) ){

					$rBck = ipt_imports_get_backups($imp['id']);
					if( $rBck ){
						$bck = ria_mysql_fetch_assoc($rBck);
						?>
						<option value="<?php echo $bck['id']; ?>"><?php echo htmlspecialchars($imp['name']); ?></option>
					<?php
					}
				}
			}
		?>
		</select>
		<button type="submit" name="backup" value="true"><?php print _('Réaliser l\'association'); ?></button>
	</form>
</div>
<?php
require_once('admin/skin/footer.inc.php');
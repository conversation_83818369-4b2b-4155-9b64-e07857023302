<?php

/**	\file reports.php
 *	Cette page affiche la liste des rapports d'importation, et permet leur téléchargement.
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

require_once('imports.inc.php');

if( isset($_REQUEST['report']) && is_numeric($_REQUEST['report']) ){
    ipt_imports_gen_report($_REQUEST['report']);
}
$is_system = null;

$rReps = ipt_reports_get(0, 0, 0, 0, array(), $is_system);

$reports = array();
if( $rReps ){
	while( $rep = ria_mysql_fetch_assoc($rReps) ){
        $reports[] = $rep;
	}
}

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Rapports').' - '._('Imports').' - '._('Outils'));
require_once('admin/skin/header.inc.php');

print '
<div class="tools-import">
	<h2>'._('Liste des rapports d\'importation').' ('.sizeof($reports).')</h2>'; ?>
    <form method="post">
			<table id="imp-form" class="checklist imp-table ui-sortable tablesorter">
				<thead>
					<tr>
						<th class="align-left"><?php print _('Nom du fichier'); ?></th>
						<th class="align-left"><?php print _('Contenu'); ?></th>
						<th class="align-left"><?php print _('Action'); ?></th>
						<th class="align-left"><?php print _('Nombre de ligne(s)'); ?></th>
						<th class="align-left th-etat"><?php print _('État/Statut'); ?></th>
						<th class="align-left th-execution"><?php print _('Début et fin d\'exécution'); ?></th>
						<th class="align-left th-proprietaire"><?php print _('Propriétaire'); ?></th>
						<th class="align-left sorter-false"></th>
					</tr>
				</thead>
				<tbody>
				<?php if ( empty($reports) ) { ?>
					<tr>
						<td colspan="8"><?php print _('Aucun rapport disponible pour le moment'); ?></td>
					</tr>
				<?php }else{

						foreach( $reports as $rep ){
							$state = "";
							//Récupère l'état du rapport
							switch($rep['report_state']){
								case 'error' :
									$class = 'imp-err';
									$state = _('Erreur');
									break;
								case 'warning' :
									$class = 'imp-warning';
									$state = _('Attention');
									break;
								case 'finished' :
									$class = 'imp-success';
									$state = _('Finalisé');
									break;
								case 'processing' :
									$class = 'imp-processing';
									$state = _('En traitement');
									break;
								default :
									$class = '';
									break;
							}
					?>
					<tr>
						<td class="name-file">
							<a href="mapping.php?imp=<?php print $rep['imp_id']; ?>">
								<?php echo htmlspecialchars(ipt_filter_import_name($rep['name']))?>
							</a>
						</td>
						<td><?php echo ucfirst(fld_classes_get_name( $rep['cls_id'] ) )?></td>
						<td><?php echo ucfirst(ipt_action_display($rep['action']) ) ?></td>
						<td>
							<?php echo '<span class="bold">' . _('Nombre :') . ' </span>' . $rep['rep_rows_in_file'] . '<br/>
							<span class="bold">' . _('Traitées :') . ' </span>' . (!is_null($rep['rows_total']) ? $rep['rows_total'] : '-') . '<br/>';
							if( !is_null($rep['rows_errors']) && $rep['rows_errors'] != '0' ) {
								echo '
								<span class="bold">' .  _('Erreurs :') . ' </span>' . (!is_null($rep['rows_errors']) ? $rep['rows_errors'] : '-');
							} ?>
						</td>
						<td class="align-left <?php echo $class ?>"><?php echo $state ?></td>
						<td data-sort-value="<?php echo !is_null($rep['date_start_ipt']) ? $rep['date_start_ipt'] : '1999-12-31 00:00:00'?>">
							<?php echo '<span class="bold">' . _('Début :') . ' </span>' . (!is_null($rep['date_start_ipt']) ? ria_date_format($rep['date_start_ipt']) : '-') ?><br/><?php echo '<span class="bold">' . _('Fin :') . ' </span>' . (!is_null($rep['date_end_ipt']) ? ria_date_format($rep['date_end_ipt']) : '-') ?>
						</td>
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){ ?>
						<td><a href="/admin/customers/edit.php?usr=<?php echo $rep['owner_id'] ?>"><?php echo gu_users_get_name($rep['owner_id'])?></a></td>
						<?php }else{ ?>
						<td><?php echo gu_users_get_name($rep['owner_id'])?></td>
						<?php } ?>
						<td class="align-center"> <button type="submit" name="report" value="<?php echo $rep['id']?>"><?php print _('Rapport'); ?></button></td>
					</tr>
				<?php 	}
					} ?>
				</tbody>
				<?php if( sizeof($reports)>10 ){ ?>
				<tfoot>
					<tr id="pagination">
						<td class="align-left"><input type="text" class="pagedisplay" readonly="readonly" /></td>
						<td colspan="7">
							<a class="first" href="#">«« </a>
							<a class="prev" href="#">« <?php print _('Page précédente'); ?></a>
							<a class="next" href="#"><?php print _('Page suivante'); ?> »</a>
							<a class="last" href="#"> »»</a>
							<select class="pagesize">
								<option value="10">10</option>
								<option value="20">20</option>
								<option value="30" selected="selected">30</option>
								<option value="40" >40</option>
								<option value="40" >40</option>
								<option value="50" >50</option>
							</select>
						</td>
					</tr>
				</tfoot>
				<?php } ?>
			</table>
    </form>
</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>
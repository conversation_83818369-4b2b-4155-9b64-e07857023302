<?php

/**	\file index.php
 *	Cette page affiche la liste des imports déjà réalisés et permet d'en créer de nouveaux
 */

require_once('imports.inc.php');
require_once('view.imports.inc.php');

if( isset($_GET['testimport']) ){
	ria_mysql_query('update ipt_imports set imp_state = "pending" where imp_tnt_id = '.$config['tnt_id'].' and imp_id = '.$_GET['testimport']);
	var_dump( ipt_imports_exec($_GET['testimport']) );
	exit;
}

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

// Bouton Ajouter
if (isset($_POST['add'])) {
	header('location: import.php');
	exit;
}

$errors = false;

require_once('RegisterGCP.inc.php');

// Bouton Supprimer
if (isset($_POST['del'], $_POST['imp'])) {
	foreach ($_POST['imp'] as $imp_id) {
		ipt_view_import_del($imp_id);
	}

	$_SESSION['success-import-index'] = _('Votre import a bien été supprimé.');
	if( count($_POST['imp']) > 1 ){
		$_SESSION['success-import-index'] = _('Vos imports ont été supprimés.');
	}

	header('Location: /admin/tools/imports/index.php');
	exit;
}

if (isset($_POST['del_imp'])) {
	ipt_view_import_del($_POST['del_imp']);

	$_SESSION['success-import-index'] = _('Votre import a été supprimé.');
	header('Location: /admin/tools/imports/index.php');
	exit;
}

$is_system = false;
$is_sync = false;
if ($config['USER_RIASTUDIO']) {
	$is_system = null;
	$is_sync = null;
}

// Choix des contenus à importer
$classes = view_import_get_classes();
$ar_classes_ids = view_import_get_classes(true);

// Charge la liste des imports terminés
$r_import_finish = ipt_imports_get(0, false, false, '', 0, $ar_classes_ids, array('error', 'warning', 'finished'), false, '', '', array(), $is_system, 0, $is_sync);

// Charge la liste des imports en cours de réalisation
$r_import_create = ipt_imports_get(0, false, false, '', 0, $ar_classes_ids, array('create'), false, '', '', array(), $is_system, 0, $is_sync);

// Charge la liste des imports en cours de traitement
$r_import_processing = ipt_imports_get(0, false, false, '', 0, $ar_classes_ids, array('processing', 'pending'), false, '', '', array(), $is_system, 0, $is_sync);

$data = array(
	'imp-class'                => isset($_GET['imp-class']) ? $_GET['imp-class'] : '',
	'imp-file'                 => '',
	'imp-method'               => '',
	'imp-login'                => '',
	'imp-url'                  => '',
	'imp-password'             => '',
	'imp-separator'            => 'scolon',
	'imp-separator-other-text' => '',
	'imp-text-separator'       => '"',
	'imp-action'               => '',
	'imp-auto'                 => '0',
	'imp-period'               => '',
	'imp-period-value'         => '',
	'is_system'                => false,
	'is_sync'                  => false
);

// Taille maximal des fichiers acceptés pour l'import
$upload_mb = 64;

$is_yuto_essentiel = tnt_tenants_is_yuto_essentiel();

define('ADMIN_PAGE_TITLE', ($is_yuto_essentiel ? _('Imports de comptes clients') : _('Imports')) . ' - ' . _('Outils'));
require_once('admin/skin/header.inc.php');
?>

<div class="tools-import tools-import-customers">
	<h2><?php echo ($is_yuto_essentiel ? _('Imports de comptes clients') : _('Imports')); ?></h2>

	<?php
		if( isset($_SESSION['success-import-index']) ){
			print '<div class="success">'.nl2br( $_SESSION['success-import-index'] ).'</div>';
			unset($_SESSION['success-import-index']);
		}
	?>

	<div class="block-actions">
		<!-- start import -->
		<div class="block-action --start-import">
			<form method="post" enctype="multipart/form-data" class="imp-form" autocomplete="off">
				<h3 class="title"><?php echo _('Démarrer un import') ?></h3>

				<input type="hidden" name="imp-submit" value="sent">
				<?php
				// préselection pour Yuto essentiel
				if ($is_yuto_essentiel) { ?>
					<input type="hidden" name="imp-class" value="<?php print CLS_USER ?>">
				<?php } else { ?>

					<!-- Choix du modèle -->
					<div class="imp-form-fields">
						<label for="imp-class" class="imp-class"><span class="mandatory">*</span> <?php print _('Sélectionnez le contenu de votre import :'); ?></label>
						<select id="imp-class" name="imp-class" <?php print $is_yuto_essentiel ? 'disabled' : ''; ?>>
							<option value="" disabled <?php echo $data['imp-class'] === '' ? 'selected' : '' ?>><?php echo _('Choisir un contenu'); ?></option>
							<?php

							foreach ($classes as $one_classe) {
								if ($is_yuto_essentiel) {
									if ($one_classe['cls_id'] == CLS_USER) {
										$selected = 'selected="selected"';
									} else {
										continue;
									}
								}

								$selected = '';
								if ($data['imp-class'] == $one_classe['cls_id']) {
									$selected = 'selected="selected"';
								}

								print '
									<option value="' . $one_classe['cls_id'] . '" ' . (trim($one_classe['info']) != '' ? 'data-info="' . htmlspecialchars($one_classe['info']) . '"' : '') . ' ' . $selected . '>' . htmlspecialchars($one_classe['name']) . '</option>';
							}
							?>
						</select>
					</div>
				<?php } ?>
				<!-- Choix de la méthode -->
				<div>
					<label for="imp-method"><span class="mandatory">*</span> <?php print _('Mode de transmission :'); ?></label>
					<select name="imp-method" id="imp-method">
						<?php if (!$is_yuto_essentiel) { ?>
							<option value="" disabled selected><?php print _('Choisir une source'); ?></option>
							<option value="file" <?php echo ($data['imp-method'] == 'file' ? 'selected' : '') ?>><?php print _('à partir d\'un fichier'); ?></option>
							<option value="url" <?php echo ($data['imp-method'] == 'url' ? 'selected' : '') ?>><?php print _('à partir d\'une URL'); ?></option>
							<option value="ftp" <?php echo ($data['imp-method'] == 'ftp' ? 'selected' : '') ?>><?php print _('à partir d\'un serveur FTP'); ?></option>
						<?php } else { ?>
							<option value="file" selected><?php print _('à partir d\'un fichier'); ?></option>
						<?php }; ?>
					</select>
				</div>
				<!-- Contenu en fonction de la méthode de transmission choisie -->
				<div class="input-file-container">
					<?php if ($data['imp-method'] != '') {
						switch ($data['imp-method']) {
							case 'url': ?>
								<label for="imp-url" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> <?php print _('Url du fichier :'); ?></label>
								<input class="input-file" id="imp-file" type="text" name="imp-url" size="75" maxlength="255" value="<?php echo htmlspecialchars($data['imp-url']); ?>" autocomplete="off" />
							<?php
								break;
							case 'ftp': ?>
								<div>
									<label for="imp-url" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> <?php print _('Serveur FTP :'); ?></label>
									<input class="input-file" id="imp-file" type="text" name="imp-url" value="<?php echo htmlspecialchars($data['imp-url']); ?>" size="32" maxlength="255" />
								</div>
								<div>
									<label for="imp-login" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> <?php print _('Nom d\'utilisateur :'); ?></label>
									<input class="input-login" id="imp-login" type="text" name="imp-login" value="<?php echo htmlspecialchars($data['imp-login']); ?>" size="32" maxlength="45" autocomplete="off" />
								</div>
								<div>
									<label for="imp-password" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> <?php print _('Mot de passe :'); ?></label>
									<input class="input-password" id="imp-password" type="password" name="imp-password" value="<?php echo $data['imp-password'] ?>" size="32" maxlength="45" autocomplete="off" />
								</div>
								<div>
									<label for="imp-file" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> <?php print _('Chemin vers le fichier :'); ?></label>
									<input class="input-file" id="imp-file" type="text" name="imp-file" value="<?php echo htmlspecialchars($data['imp-file']); ?>" size="32" maxlength="255" />
									<p class="field-helper">64Mo max, formats XLS, XLSX ou CSV acceptés</p>
								</div>
					<?php
								break;
							case 'file':
								print import_file_drag_and_drop($upload_mb);
								break;
							default:

								break;
						}
					} elseif ($is_yuto_essentiel) {
						print import_file_drag_and_drop($upload_mb);
					}
					?>
					<!-- / -->
				</div>
				<!-- Type d'action -->
				<?php
				if ($is_yuto_essentiel) {
					$r_user = gu_users_get(0, '', '', array(PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER, PRF_SELLER, PRF_SUPPLIER, PRF_USER, PRF_CONTACT));

					if ($r_user && !ria_mysql_num_rows($r_user)) {
						print '<input type="hidden" name="imp-action" value="add" />';
					} else {
						print '<input type="hidden" name="imp-action" value="add/upd" />';
					}
				} else { ?>
					<div class="imp-form-fields">
						<label for="imp-action"><span class="mandatory">*</span> <?php print _('Choisissez le type d\'action qui va être réalisé par l\'import :'); ?></label>
						<select id="imp-action" name="imp-action">
							<option value="" disabled <?php echo $data['imp-action'] === '' ? 'selected' : '' ?>><?php print _('Choisir une action'); ?></option>
							<?php
							foreach (ipt_imports_get_import_actions() as $val) {
								$selected = '';
								if ($data['imp-action'] === $val) {
									$selected = 'selected';
								}
							?>
								<option value="<?php echo $val ?>" <?php echo $selected ?>><?php echo ipt_action_display($val) ?></option>
							<?php
							}
							?>
						</select>
					</div>
				<?php } ?>
				<!-- Réglages avancés -->
				<div class="advance-settings">
					<div class="imp-form-fields">
						<label for="sep-semi-colon"><span class="mandatory">*</span> <?php print _('Indiquez quel caractère ou suite de caractères délimite les colonnes de votre fichier :'); ?></label>
						<div class="imp-sep-list">
							<label class="check-label" for="sep-tab">
								<input type="radio" class="check-box" id="sep-tab" name="imp-separator" value="tab" <?php echo ($data['imp-separator'] == 'tab' ? 'checked="checked"' : '') ?> />
								<span class="check-span"><?php print _('Tabulation'); ?></span>
							</label>
							<label class="check-label" for="sep-comma">
								<input type="radio" class="check-box" id="sep-comma" name="imp-separator" value="comma" <?php echo ($data['imp-separator'] == 'comma' ? 'checked="checked"' : '') ?> />
								<span class="check-span"><?php print _('Virgule'); ?></span>
							</label>
							<label class="check-label" for="sep-semi-colon">
								<input type="radio" class="check-box" id="sep-semi-colon" name="imp-separator" value="scolon" <?php echo ($data['imp-separator'] == 'scolon' ? 'checked="checked"' : '') ?> />
								<span class="check-span"><?php print _('Point virgule'); ?></span>
							</label>
							<label class="check-label" for="sep-space">
								<input type="radio" class="check-box" id="sep-space" name="imp-separator" value="space" <?php echo ($data['imp-separator'] == 'space' ? 'checked="checked"' : '') ?> />
								<span class="check-span"><?php print _('Espace'); ?></span>
							</label>
							<label class="check-label" for="sep-other">
								<input type="radio" class="check-box" id="sep-other" name="imp-separator" value="other" <?php echo ($data['imp-separator'] == 'other' ? 'checked="checked"' : '') ?> />
								<span class="check-span"><?php print _('Autre :'); ?>
									<input type="text" name="imp-separator-other-text" class="text-separator" maxlength="5" value="<?php echo htmlspecialchars($data['imp-separator-other-text']) ?>"></span>
							</label>
						</div>
					</div>
					<div class="imp-form-fields">
						<label for="imp-text-separator"><?php print _('Indiquez quel caractère délimite les textes dans votre fichier :'); ?></label>
						<input type="text" id="imp-text-separator" name="imp-text-separator" class="text-separator" maxlength="5" value="<?php echo htmlspecialchars($data['imp-text-separator']) ?>" />
					</div>
				</div>

				<?php
				// Message d'erreur à la soumission
				$errors_cls = '';
				if (!empty($errors)) {
					$errors_cls = 'error';
				}
				?>
				<div class="notif <?php echo $errors_cls; ?>">
					<?php if (!empty($errors)) { ?>
						<?php foreach ($errors as $error) { ?>
							<<p>><?php echo $error; ?></<p>
						<?php } ?>
					<?php } ?>
				</div>

				<?php if ($config['USER_RIASTUDIO']) { ?>
					<!-- Réservé RIASTUDIO -->
					<div class="imp-form-fields notice" title="<?php print _('Ces options sont réservées aux utilisateurs de RiaStudio'); ?>">
						<label class="check-label" for="is_system">
							<input type="checkbox" class="check-box" id="is_system" name="is_system" value="1" <?php echo ($data['is_system'] ? 'checked="checked"' : '') ?> />
							<span class="check-span"><?php print _('Définir comme import système'); ?></span>
						</label>

						<br />

						<label class="check-label" for="is_sync">
							<input type="checkbox" class="check-box" id="is_sync" name="is_sync" value="1" <?php echo ($data['is_sync'] ? 'checked="checked"' : '') ?> />
							<span class="check-span"><?php print _('Définir comme import de synchronisation'); ?></span>
						</label>

						<div class="imp-auto">
							<br />
							<label class="check-label" for="imp-auto-y"><?php print _('Activer l\'import récurrent :'); ?></label>

							<input class="check-box" type="radio" id="imp-auto-y" name="imp-auto" value="1" />
							<label for="imp-auto-y" class="check-label"><span class="check-span"><?php print _('Oui'); ?></span></label>

							<input class="check-box" type="radio" id="imp-auto-n" name="imp-auto" value="0" checked="checked" />
							<label for="imp-auto-n" class="check-label"><span class="check-span"><?php print _('Non'); ?></span></label>
						</div>

						<div id="config-imp-periods" class="imp-periods">
							<label for="imp-period"><span class="mandatory">*</span> <?php print _('Importer :'); ?></label>
							<select name="imp-period" id="imp-period">
								<option value=""></option>
								<?php foreach (ipt_import_get_recurence() as $key => $text) { ?>
									<option value="<?php echo $key ?>" <?php echo ($key == $data['imp-period']) ? 'checked="checked"' : '' ?>><?php echo htmlspecialchars($text); ?></option>
								<?php } ?>
							</select>
							<div class="imp-period-values"></div>
						</div>

					</div>
				<?php } ?>

				<?php if (gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_ADD')) { ?>
					<button id="btn-import" name="" disabled title="<?php print _('Importer un nouveau fichier'); ?>"><?php echo _("Importer") ?></button>
				<?php } ?>
			</form>

		</div>
		<!-- /start import -->
		<!-- download-example -->
		<div class="block-action --download-example">
			<?php
			if ($is_yuto_essentiel) {
				print ria_view_example_import_file(CLS_USER);
			} else {
				print ria_view_example_import_file('default');
			}
			?>
		</div>
		<!-- /download-example -->
		<!-- file-prepare -->
		<div class="block-action --file-prepare">
			<?php
			if ($is_yuto_essentiel) {
				print ria_view_help_import_file(CLS_USER);
			} else {
				print ria_view_help_import_file('default');
			}
			?>
		</div>
		<!-- /file-prepare -->
	</div>

	<form method="post" class="registrered-imports-form"><?php
		if( $r_import_processing && ria_mysql_num_rows($r_import_processing) ){
			print '<h2>'._('Imports en cours').'</h2>'
				.'<div class="imports-unfinished-history">';

				while ($import = ria_mysql_fetch_assoc($r_import_processing)) {
					if (trim($import['info']) != '') {
						$import['info'] = json_decode($import['info'], true);
					}

					print view_imports_card($import, 'index');
				}

				print '</div>';
		}

		if( $r_import_create && ria_mysql_num_rows($r_import_create) ){
			print '<h2>'._('Imports sauvegardés').'</h2>'
				.'<div class="imports-unfinished-history">';

				while ($import = ria_mysql_fetch_assoc($r_import_create)) {
					if (trim($import['info']) != '') {
						$import['info'] = json_decode($import['info'], true);
					}

					print view_imports_card($import, 'index');
				}

				print '</div>';
		}

		if( $r_import_finish && ria_mysql_num_rows($r_import_finish) ){
			print '<h2>'._('Imports terminés').'</h2>'
				.'<div class="imports-unfinished-history">';

					while($import_finish = ria_mysql_fetch_assoc($r_import_finish)){
						print view_imports_card($import_finish, 'index');
					}

			print '</div>';
		}
	?></form>

	<?php if ($config['USER_RIASTUDIO']) {

		$r = ipt_imports_get(0, false, false, '', 0, 0, false, false, '', '', array(), null);
		$backups = array();
		if ($r) {
			while ($imp = ria_mysql_fetch_assoc($r)) {
				$rBck = ipt_imports_get_backups($imp['id']);

				if ($rBck) {
					$bck = ria_mysql_fetch_assoc($rBck);
					$backups[$bck['id']] = $imp['name'];
				}
			}
		}

		// L'interface de restauration n'apparaît que s'il y a des sauvegardes à restaurer et si l'utilisateur est autorisé a réaliser une restauration
		if (!empty($backups) && gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_BACKUP')) {
	?>
			<div class="backups">
				<h2><?php print _('Restauration d\'une sauvegarde'); ?></h2>
				<form method="get" action="mapping.php" id="imp-backup">
					<label for="import-id"><?php print _('Sélectionnez l\'import pour lequel il faut restaurer la sauvegarde :'); ?></label>
					<select name="imp" id="import-id">
						<?php if (empty($backups)) { ?>
							<option value=""><?php print _('Aucune sauvegarde pour le moment'); ?></option>
							<?php } else {
							foreach ($backups as $id => $name) { ?>
								<option value="<?php print $id ?>"><?php print htmlspecialchars($name); ?></option>
						<?php	}
						} ?>
					</select>
					<button type="submit" name="backup" value="true"><?php print _('Réaliser l\'association'); ?></button>
				</form>
			</div>
	<?php }
	} ?>
</div>

<?php
require_once('admin/skin/footer.inc.php');
?>
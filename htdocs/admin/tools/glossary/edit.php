<?php
	require_once('prd/reviews.inc.php');
	require_once('glossary.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_GLOSSARY_EDIT');

	// Détection de requete ajax
	$ajax = false;
	$xml_status = '';
	$xml_body = '';
	if (isset($_SERVER['HTTP_X_REQUESTED_WITH'])  
        && $_SERVER['HTTP_X_REQUESTED_WITH'] == "XMLHttpRequest") { 
		$ajax = true;
	}

	// Vérifie les paramètres de suivi pour la liste des avis
	if( !isset($_GET['comments-type']) )
		$_GET['comments-type'] = '';
	if( !isset($_GET['filter']) )
		$_GET['filter'] = 'all';
	if( !isset($_GET['page']) || !is_numeric($_GET['page']) )
		$_GET['page'] = 1;
	if( !isset($_GET['c']) )
		$_GET['c'] = '';

	
	// Bouton Annuler l'édition d'une définition
	if( isset($_POST['cancel-edit-word']) ){
		header('Location:  index.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter'].'&c='.$_GET['c']);
		exit;
	}
	
	// Bouton Supprimer une définition
	elseif( isset($_POST['delete-word']) ){
		require_once('glossary.inc.php');

		if( is_array($_REQUEST['word']) )
			foreach( $_REQUEST['word'] as $word )
				word_reviews_del($word);
		else
			word_reviews_del($_REQUEST['word']);
		// La suppression ajaxienne n'est pas implémenter sur la page
		if(!$ajax) {
			header('Location: index.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter'].'&c='.$_GET['c']);
			exit;
		}
	}

	// Bouton Enregistrer une définition
	if( isset($_POST['save-word']) ){
		require_once('glossary.inc.php');
		if( !isset($_POST['publish']) ) $_POST['publish'] = 0;
		if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
		  gsr_words_update($_POST['wrd-id'],$_POST['wrd-name'],$_POST['wrd-desc'],$_POST['publish'],isset($_POST['wrd-name-pl']) ? $_POST['wrd-name-pl'] : '');
		} elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
      $values = array(
      	_FLD_GSR_NAME=>$_POST['wrd-name'],
      	_FLD_GSR_NAME_PL=>$_POST['wrd-desc'],
      	_FLD_GSR_DESC=>( isset($_POST['wrd-name-pl']) ? $_POST['wrd-name-pl'] : '' )
      );
      
      if( !fld_translates_add($_POST['wrd-id'], $_GET['lng'], $values) )
      	$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
		header('Location: index.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter'].'&c='.$_GET['c']);
		exit;
	}

	define('ADMIN_PAGE_TITLE', _('Edition de définition').' - '._('Glossaire').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	$word = ria_mysql_fetch_array(gsr_words_get($_GET['wrd']));
	// Récupère les informations traduite
	if( $lng!=$config['i18n_lng'] ){
		$tsk_word = fld_translates_get( CLS_FAQ_CAT, $word['id'], $lng, $word, array(_FLD_GSR_NAME=>'name', _FLD_GSR_NAME_PL=>'name_pl', _FLD_GSR_DESC=>'desc' ), true );
		$word['name'] = $tsk_word['name'];
		$word['name_pl'] = $tsk_word['name_pl'];
		$word['desc'] = $tsk_word['desc'];
	}
	echo '<h2>' . _('Editer ') . htmlspecialchars($word['name']) . '</h2>';
	
  // Affiche le menu de langue
	print view_translate_menu( 'edit.php?action=add&amp;wrd='.$_GET['wrd'].'&amp;c='.$_GET['c'], $lng );
?>
<form action="edit.php?action=add&amp;wrd=<?php print $_GET['wrd']; ?>&amp;c=<?php print $_GET['c']; ?>&amp;lng=<?php print $lng; ?>" method="post">
		<input type="hidden" name="wrd-id" value="<?php print htmlspecialchars($word['id']); ?>" />
		<table id="table-glossary-edit">
				<caption><?php print _('Editer une définition'); ?></caption>
			<tbody>
				<tr>
					<td class="tdw-150"><label for="wrd-name"><?php print _('Nom :'); ?></label></td>
					<td><input type="text" name="wrd-name" id="wrd-name" value="<?php print htmlspecialchars($word['name']); ?>" maxlength="255" /></td>
				</tr>
				<tr>
					<td><label for="wrd-name-pl"><?php print _('Nom :'); ?> (<?php print _('au pluriel'); ?>)</label></td>
					<td><input type="text" name="wrd-name-pl" id="wrd-name-pl" value="<?php print htmlspecialchars($word['name_pl']); ?>" maxlength="255" /></td>
				</tr>
				<tr>
					<td><label for="wrd-desc"><?php print _('Définition :'); ?></label></td>
					<td><textarea class="wdr-desc" name="wrd-desc" id="wrd-desc"><?php print htmlspecialchars($word['desc']); ?></textarea></td>
				</tr>
				<tr>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_GLOSSARY_PUBLISH') ){ ?>
					<td><label for="publish"><?php print _('Validation :'); ?></label></td>
					<td><input type="checkbox" class="checkbox" name="publish" id="publish" value="1" <?php print $word['publish'] ? 'checked="checked"':''; ?> /> <label for="publish"><?php print _('Valider et publier cette définition sur le site'); ?></label></td>
					<?php } ?>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save-word" value="<?php print _('Enregistrer'); ?>" />
				</td></tr>
			</tfoot>
		</table>		
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php
	
	/**	\file index.php
	 *	Cette page affiche la liste des alertes de disponibilité
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ALERT');

	unset($error);
	

	// Suppression d'une ou plusieurs alertes de disponiiblités
	if( isset($_POST['delete']) ){
		if( isset($_POST['sub']) && is_array($_POST['sub']) ){
			foreach( $_POST['sub'] as $sub ){
				$sub = explode( '|', $sub );
				
				if( is_array($sub) && sizeof($sub) == 2 ){
					if( !gu_livr_alerts_del($sub[0], $sub[1]) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression.");
					}
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['del-alert-livr'] = true;
			header('Location: /admin/tools/livr-alerts/index.php');
			exit;
		}
	}

	// Période
	// Variable pour la mise en place des périodes
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d H:i:s');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if ( isset($_GET["date1"], $_GET["date2"]) ) {
		$date1 = ria_mysql_escape_string(dateheureparse($_GET["date1"]));
		$date2 = ria_mysql_escape_string(dateheureparse($_GET["date2"]));
	}

	// Bouton Exporter
	if( isset($_POST['export']) ){
		header('Location: export.php'.( isset($date1) ? '?'.$date1.( isset($date2) ? '&'.$date2 : '' ) : '' ));
		exit;
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_TOOL_ALERT_DEL');

	$colspan = ($checkbox ? 9 : 8 );

	define('ADMIN_PAGE_TITLE', _('Alertes de disponibilité').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');

	// Chargement des adresses
	$alerts = gu_livr_alerts_get(0, 0, 0, true, $date1, $date2);
	$count = ria_mysql_num_rows($alerts);

	$pages = ceil( ria_mysql_num_rows($alerts) / 50 );
	$page = isset($_GET['page']) && is_numeric($_GET['page']) ? $_GET['page'] : 1;
	if( $page<1 ) $page = 1; if( $page>$pages ) $page = $pages;
	if( $page>1 ) ria_mysql_data_seek( $alerts, ($page-1)*50 );

	//Titre h2
	print '<h2>'.sprintf( _('Alertes de disponibilité (%d)'), $count ).'</h2>';

	// Affichage des messages d'erreur et de succès
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars( $error ).'</div>';
	}elseif( isset($_SESSION['del-alert-livr']) ){
		print '<div class="success">'._('La suppression s\'est correctement déroulée.').'</div>';
		unset( $_SESSION['del-alert-livr'] );
	}

?>
<div class="stats-menu">
	<div id="riadatepicker"></div>
</div>

<form method="post" action="index.php">
	<input type="hidden" name="date1" id="date1" value="<?php print $date1; ?>" />
	<input type="hidden" name="date2" id="date2" value="<?php print $date2; ?>" />
	<table id="tb-alert-dispo" class="tablesorter checklist">
		<thead>
			<tr>
				<?php if ( $checkbox ){ ?>
				<th id="sel" class="sorter-false"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<?php } ?>
				<th id="prd-ref" class="sorter-text"><?php print _('Référence'); ?></th>
				<th id="prd-name"><?php print _('Désignation'); ?></th>
				<th id="brd-name"><?php print _('Marque'); ?></th>
				<th id="email"><?php print _('Adresse email'); ?></th>
				<th id="account"><?php print _('Compte client'); ?></th>
				<th id="date"><?php print _('En attente depuis le'); ?></th>
				<th id="restocking"><abbr title="<?php print _('Si l\'envoi de l\'alerte est fait à chaque remise en stock du produit'); ?>"><?php print _('À chaque remise en stock'); ?></abbr></th>
				<th id="last_date"><?php print _('Dernier envoi'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( !ria_mysql_num_rows($alerts) ){
					print '<tr><td colspan="'.$colspan.'">'._('Aucune adresse').'</td></tr>';
				}else{
					$count = 0;
					while( ($e = ria_mysql_fetch_array($alerts)) ){
						$rp = prd_products_get_simple( $e['id'] );
						
						$sync = $url = ''; $idCat = 0;
						if( $rp && ria_mysql_num_rows($rp) ){
							$p = ria_mysql_fetch_array( $rp );
							$sync = view_prd_is_sync( $p );
							$sync = trim($sync)!='' ? $sync.' ' : '';
							
							$rcat = prd_products_categories_get($e['id'], true);
							if( $rcat && ($cat=ria_mysql_fetch_array( $rcat )) ){
								$idCat = $cat['cat'];
							}
						}
						print '<tr>';
						if( $checkbox ){
						print '<td headers="sel"><input type="checkbox" class="checkbox" name="sub[]" value="'.$e['usr_email'].'|'.$e['id'].'" /></td>';
						}
						print '<td headers="prd-ref">';
						print $sync;
						if( $idCat>0 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') )
							print '<a href="/admin/catalog/product.php?cat='.$idCat.'&amp;prd='.$e['id'].'" target="_blank">';
						print htmlspecialchars($e['ref']);
						if( $idCat>0 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') )
							print '</a>';
						print '	</td>
								<td headers="prd-name">'.htmlspecialchars( $e['name'] ).'</td>
								<td headers="brd-name">'.( isset($p['brd_title']) ? htmlspecialchars( $p['brd_title'] ) : '' ).'</td>
								<td headers="email"><a href="mailto:'.$e['usr_email'].'">'.htmlspecialchars($e['usr_email']).'</a></td>
								<td headers="account">
						';
						if( $e['usr_id'] && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){
							print '<a href="../../customers/edit.php?usr='.$e['usr_id'].'">'._('Oui').'</a>';
						}elseif( $e['usr_id'] ){
							print _('Oui');
						}else{
							print _('Non');
						}
						print '	</td>
								<td headers="date">'.$e['date_created'].'</td>
								<td headers="restocking">'.( $e['restocking'] ? _('Oui') : _('Non') ).'</td>
								<td headers="last_date">'.( trim($e['date_notified_en']) != '' ? ria_date_format($e['date_notified_en']) : '' ).'</td>
							</tr>
						';
						$count++;
					}
				}
			?>
		</tbody>
		<?php if( $count>0 ){ ?>
		<tfoot>
			<tr>
				<td colspan="<?php print $colspan; ?>" class="align-left">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ALERT_DEL') ){ ?>
					<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" />
					<?php } ?>
					<input type="submit" name="export" value="<?php print _('Exporter'); ?>" />
				</td>
			</tr>
		</tfoot>
		<?php } ?>
	</table>
</form>

<script>
<!--
	function load_alerts(){
		window.location.href = 'index.php?date1=' + $('[name=date1]').val() + '&date2=' + $('[name=date2]').val();
	}
	<?php print view_date_initialized( 0, '', false, array('callback'=>'load_alerts') ); ?>
-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
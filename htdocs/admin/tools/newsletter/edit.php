<?php
	require_once('newsletter.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');
	
	// Vérifie l'identifiant passé en argument
	if( !isset($_GET['id']) || !isset( $_GET[ 'cat' ] ) || !nlr_subscribers_exists($_GET['id'], $_GET[ 'cat' ] ) ){
		header('Location: list.php');
		exit;
	}

	// Bouton Annuler
	if( isset( $_POST[ 'cancel' ] ) )
	{
		header('Location: list.php?oc='. $_GET[ 'cat' ]);
		exit;
	}
	
	/* Gestion des onglets */
	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}
	$tab = $_GET['tab'];
	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabFields']) ){
		$tab = 'fields';
	}
	
	// Modification
	if (isset($_POST['update'])) {
		if (! isset($_GET['id'], $_GET['cat'])) $error = _('Certaines informations n\'ont pas été transmises.');
		else{
			if ((isset($_POST['email']) && isemail($_POST['email']))){
				if (!newsletter_is_inscripted($_POST['email'], $_GET['cat'])){
					if (! nlr_subscribers_email_set($_GET['id'], $_POST['email'], $_GET['cat'])) $error = _('L\'email n\'a pas pu être modifié pour une raison inconnue.');
				}
			}
			if ((isset($_POST['phone']) && isphone($_POST['phone']))){
				if (!newsletter_is_inscripted($_POST['phone'], $_GET['cat'])){
					if (! nlr_subscribers_phone_set($_GET['id'], $_POST['phone'], $_GET['cat'])) $error = _('Le téléphone n\'a pas pu être modifié pour une raison inconnue.');
				}
			}
			
		}
	}
	
	$adr = ria_mysql_fetch_array( nlr_subscribers_get( NEWSLETTER_TYPE_ALL, $_GET['id'], '',  $_GET[ 'cat' ] ) );

	if( isset($_POST['uninscript']) ){
		if( nlr_subscribers_del_by_id($_GET['id'])){
			header('Location: list.php?oc='. $_POST[ 'cat_id' ]);
			exit;
		}else{
			$error = _("Une erreur inattendue s'est produite lors de la désinscription de cette adresse email");
		}
	}
	
	// Action sur l'onglet "Avancés"
	view_admin_tab_fields_actions( CLS_NLR_SUBSCRIBERS, $_GET['id'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );

	define('ADMIN_PAGE_TITLE', _('Newsletters').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Newsletters'); ?></h2>
<?php
	if( isset($error) && trim($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}
?>
<form action="edit.php?id=<?php print $adr['id']; ?>&amp;cat=<?php print $adr[ 'cat_id' ]; ?>&amp;tab=<?php print $tab; ?>" method="post">
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( view_admin_show_tab_fields( CLS_NLR_SUBSCRIBERS, $_GET['id'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php print _('Avancé'); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">
		<?php if( $tab=='general' ){ ?>
		<table>
			<caption><?php print _('Fiche de l\'adresse'); ?> <?php print htmlspecialchars($adr['email']); ?> ( <?php print _('Newsletter :'); ?> <?php print $adr[ 'cat' ]; ?> )</caption>
		<tfoot>
			<tr><td colspan="2">
				<?php if( $adr['inscript-confirmed'] && !$adr['uninscript-confirmed'] ){ ?>
				<input type="hidden" name="cat_id" value="<?php print $adr[ 'cat_id' ]; ?>" />
				<input type="submit" name="update" value="<?php print _('Modifier'); ?>" />
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_INSCRIPT') ){ ?>
				<input type="submit" name="uninscript" value="<?php print _('Désinscrire'); ?>" onclick="return nlr_uninscript()" />
				<?php } ?>
				<?php } ?>
				<input type="submit" name="cancel" value="<?php print _('Retour'); ?>" />
			</td></tr>
		</tfoot>
		<tbody>
			<tr><th colspan="2"><?php print _('Général'); ?></th></tr>
			<tr>
				<td><label for="email"><?php print _('Adresse email :'); ?></label></td>
				<td><input type="email" name="email" id="email" value="<?php print htmlspecialchars($adr['email']); ?>" maxlength="75" /></td>
			</tr>
			<tr>
				<td><label for="phone"><?php print _('Téléphone :'); ?></label></td>
				<td><input type="phone" name="phone" id="phone" value="<?php print htmlspecialchars($adr['phone']); ?>" maxlength="15" /></td>
			</tr>
			<tr>
				<td><label for="state"><?php print _('Etat :'); ?></label></td>
				<td>
					<select name="state" id="state" disabled="disabled">
						<?php
							if( $adr['uninscript-confirmed'] ){
								$adr['type'] = NEWSLETTER_TYPE_UNINSCRIPT;
							}elseif( $adr['uninscript-requested'] ){
								$adr['type'] = NEWSLETTER_TYPE_PRE_UNINSCRIPT;
							}elseif( $adr['inscript-confirmed'] ){
								$adr['type'] = NEWSLETTER_TYPE_INSCRIPT;
							}else{
								$adr['type'] = NEWSLETTER_TYPE_PRE_INSCRIPT;
							}
							foreach( $NEWSLETTER_TYPES as $key => $val ){
								print '<option value="'.$key.'" '.( $key==$adr['type'] ? 'selected="selected"' : '' ).'>'.$val.'</option>';
							}
						?>
					</select>
				</td>
			</tr>
			<tr>
				<td><?php print _('Newsletters :'); ?></td>
				<td>
					<?php 
						$user_cat = newsletter_users_categorie_get( $adr['email'] );
						$i = 0;
						while( $uc = ria_mysql_fetch_array($user_cat) )
						{
							if( !$i ){
								print $uc['cat'];
							}else{
								print ', '.$uc['cat'];
							}
							$i = 1;
						}
					?>
				</td>
			</tr>
			<tr><th colspan="2"><?php print _('Compte client'); ?></th></tr>
			<tr>
				<td><?php print _('Dispose d\'un compte client :'); ?></td>
				<td>
				<?php
					if( $adr['usr_id'] ){
						print '<a href="../../customers/edit.php?usr='.$adr['usr_id'].'">'._('Oui').'</a>';
					}else{
						print _('Non');
					}
				?>
				</td>
			</tr>
			<tr>
				<td><?php print _('Type de compte :'); ?></td>
				<td><?php print htmlspecialchars($adr['prf_name']); ?></td>
			</tr>
			<tr>
				<td><?php print _('Catégorie tarifaire :'); ?></td>
				<td><?php print htmlspecialchars($adr['prc_name']); ?></td>
			</tr>
			<tr>
				<td><?php print _('Nombre de commandes :'); ?></td>
				<td><?php print number_format($adr['usr_orders'],0,',',' '); ?></td>
			</tr>
			<tr><th colspan="2"><?php print _('Inscription'); ?></th></tr>
			<tr>
				<td><?php print _('Demandée le :'); ?></td>
				<td><?php print ria_date_format($adr['inscript-requested']) ?></td>
			</tr>
			<tr>
				<td><?php print _('Confirmée le :'); ?></td>
				<td><?php print ria_date_format($adr['inscript-confirmed']) ?></td>
			</tr>
			<tr>
				<td><?php print _('Etat :'); ?></td>
				<td>
					<?php
						if( $adr['inscript-confirmed'] )
							if( $adr['email-verified'] )
								print _('Confirmée, l\'adresse email a été vérifiée');
							else
								print _('Confirmée');
						else
							print _('A confirmer, l\'adresse email n\'a pas été vérifiée');
					?>
				</td>
			</tr>
			<tr><th colspan="2"><?php print _('Désinscription'); ?></th></tr>
			<tr>
				<td><?php print _('Demandée le :'); ?></td>
				<td><?php print $adr['uninscript-requested'] ? ria_date_format($adr['uninscript-requested']) : _('Jamais demandée'); ?></td>
			</tr>
			<tr>
				<td><?php print _('Confirmée le :'); ?></td>
				<td><?php print $adr['uninscript-confirmed'] ? ria_date_format($adr['uninscript-confirmed']) : _('Jamais confirmée'); ?></td>
			</tr>
			<tr>
				<td><?php print _('Etat :'); ?></td>
				<td>
					<?php
						if( !$adr['uninscript-requested'] ){
							print _('Désinscription jamais demandée');
						}elseif( $adr['uninscript-confirmed'] ){
							print _('Désinscription confirmée');
						}else{
							print _('Désinscription demandée mais jamais terminée');
						}
					?>
				</td>
			</tr>
			<?php 
				$stats = stats_origins_get( $adr['id'], CLS_NLR_SUBSCRIBERS );
				if( $stats && ria_mysql_num_rows($stats) ){
					$stat = ria_mysql_fetch_array( $stats );
					print '<tr><th colspan="2">'._('Origine de l\'inscription').'</th></tr>';
					print view_source_origin($stat, 'table');
				}
			?>
		</tbody>
		</table>
	<?php 
		} elseif( $tab=='fields' ){ 
			print view_admin_tab_fields( CLS_NLR_SUBSCRIBERS, $adr['id'], $config['i18n_lng'] );
		}
	?>
	</div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>

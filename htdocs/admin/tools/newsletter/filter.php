<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');

	require_once('newsletter.inc.php');
	require_once('users.inc.php');
	
	unset($success);
	
	$seg_id = -1;
	if( isset($_GET['seg_id']) && is_numeric($_GET['seg_id']) && $_GET['seg_id']>0 )
		$seg_id = $_GET['seg_id'];
		
	if( $seg_id>0 ){
		$rseg = seg_segments_get( $seg_id );
		if( $rseg && ria_mysql_num_rows($rseg) ){
			$success = str_replace(
				'#param[nom]#',
				htmlspecialchars(ria_mysql_result($rseg, 0, 'name')),
				_('Le segment "#param[nom]#" a bien été crée. Vous pouvez désormais l\'utiliser comme filtre sur cette page.')
			);
		}
	}



	// Vérification de $_GET['oc'] et récupération du nom de la newsletter
	$id_newsletter = false;

	if( isset($_GET['oc']) && is_numeric($_GET['oc'])){
		$id_newsletter = $_GET['oc'];
	}
	
	if( $id_newsletter!=false ){
		$cat = nlr_categorie_get( $id_newsletter );
		if( !$cat || !ria_mysql_num_rows($cat) ){
			header('Location: index.php');
			exit;
		} else {
			$c = ria_mysql_fetch_array( $cat );

			if ( $c != true ) {
				header('Location: index.php');
				exit;
			}
		}
	} else {
		header('Location: index.php');
		exit;
	}

	$title_cat = $c['cat'];

	define('ADMIN_PAGE_TITLE', _('Filtre avancé').' - '.$title_cat);
	require_once('admin/skin/header.inc.php');
	
?>
<h2><?php echo _('Filtre avancé') . ' : ' . htmlspecialchars($title_cat); ?></h2>

<?php
if( isset($success) ){
	print '<div class="success">'.nl2br($success).'</div>';
}
?>

<form method="get" action="list.php">
	<table>
		<tbody>
			<tr><th colspan="2"><?php print _('Général'); ?></th></tr>
			<tr>
				<td class="col130px"><label for="type"><?php print _('Etat :'); ?></label></td>
				<td>
					<select name="type" id="type">
					<?php
						foreach( $NEWSLETTER_TYPES as $key => $val )
							print '<option value="'.$key.'">'.htmlspecialchars( $val ).'</option>';
					?>
					</select>
				</td>
			</tr>
			<tr>
				<td><label for="oc"><?php print _('Newsletter :'); ?></label></td>
				<td>
					<select name="oc" id="oc">
						<?php
							$categs = nlr_categorie_get();
							while( $c = ria_mysql_fetch_array( $categs ) )
								print '<option value="'. $c[ 'id' ] .'" ' . ($title_cat == $c['cat'] ? 'selected=selected ': '') .  '>'. htmlspecialchars( $c[ 'cat' ] ) .'</option>';
						?>
					</select>
				</td>
			</tr>
			<tr>
				<td><label for="date-start"><?php print _('Inscrit après le :'); ?></label></td>
				<td>
					<input id="date-start" name="date-start" class="date datepicker valign-center" type="text" /> 00:00
				</td>
			</tr>
			<tr>
				<td><label for="date-end"><?php print _('Inscrit avant le :'); ?></label></td>
				<td>
					<input id="date-end" name="date-end" class="date datepicker valign-center" type="text" /> 23:59
				</td>
			</tr>
			<tr><th colspan="2"><?php print _('Segmentation'); ?></th></tr>
			
			<tr>
				<td><label for="seg_id"><?php print _('Segment :'); ?></label></td>
				
				<td>
			<?php
			$rseg = seg_segments_get( 0, CLS_USER );
			if( $rseg && ria_mysql_num_rows($rseg) ){
			?>
					<select name="seg_id" id="seg_id">
						<option value="0"><?php print _('Choisir un segment'); ?></option>
						<?php
						while( $seg = ria_mysql_fetch_array($rseg) ){
							print '<option '.( $seg['id']==$seg_id ? 'selected="selected"' : '' ).' value="'.$seg['id'].'">'.htmlspecialchars($seg['name']).'</option>';
						}
						?>
					</select>
					<br/>
			<?php
			}
			?><a href="/admin/config/fields/segments/segment.php?cls=2&amp;id=0&amp;dest=NEWSLETTER_FILTER"><?php print _('Créer un nouveau segment'); ?></a></td>
			</tr>
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" value="<?php print _('Filtrer'); ?>" />
				<input type="submit" value="<?php print _('Annuler'); ?>" onclick="return nlr_cancel()" />
			</td></tr>
		</tfoot>
	</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
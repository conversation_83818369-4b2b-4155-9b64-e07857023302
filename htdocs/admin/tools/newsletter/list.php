<?php

	/**	\file list.php
	 *	Cette page est chargée d'afficher la liste des inscriptions ou tentatives d'inscriptions à une newsletter
	 *	gérée par RiaShop.
	 *
	 *	Elle utilise les paramètres suivants :
	 *	- $_GET['oc'] : identifiant de la newsletter à afficher
	 *
	 *	Elle permet les actions suivantes :
	 *	- Recherche d'une adresse email
	 *	- Filtre sur l'avancement d'inscription
	 *	- Inscription manuelle d'une adresse email
	 *	- Export au format Excel
	 *	- Désinscription d'une adresse email
	 */

	require_once('newsletter.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');
	
	unset($error);

	// Initialise le paramètre $_GET['oc'] selon l'action réalisée pour arriver sur cette page
	{
		if( isset($_GET['ocp']) && $_GET['ocp'] ){
			$_GET['oc'] = $_GET['ocp'];
		}

		if( !isset($_POST['oc']) && !isset($_GET['oc']) ){
			$_GET['oc'] = 0;
		}elseif( !isset($_POST['oc']) && isset($_GET['oc']) ){
			$_GET['oc'] = $_GET['oc'];
		}elseif( !isset($_GET['oc']) || $_GET['oc'] != $_POST['oc'] ){
			$_GET['oc'] = $_POST['oc'];
		}
		
		if( $_GET['oc']!=0 ){
			$cat = nlr_categorie_get( $_GET[ 'oc' ] );
			if( ria_mysql_num_rows($cat) ){
				$c = ria_mysql_fetch_array( $cat );
			}	
		}
	}

	// Inscription manuelle d'une adresse email
	if( isset($_POST['add'],$_POST['email'], $_GET['oc']) ){
		// récupère le type de newsletter (email ou mobile)
		$oc_id = str_replace('c-', '', $_GET['oc']);
		if( !is_numeric($oc_id) || $oc_id <= 0 ){
			$error = _('La catégorie de newsletter n\'est pas reconnue.');
		}else{
			$r_cat = nlr_categorie_get($oc_id);
			if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
				$error = _('La catégorie de newsletter n\'est pas reconnue.');
			}else{
				$cat = ria_mysql_fetch_assoc($r_cat);
			}
		}

		if( !isset($error) ) {
			$add = false;
			
			// Gestion de l'inscription pour une newsletter par email
			if( $cat['type'] == 'email' ){
				if( !trim($_POST['email']) && isemail($_POST['email']) ){
					$error = _("Veuillez indiquer l'adresse email à inscrire");
				}elseif( newsletter_is_inscripted($_POST['email'], $oc_id) ){
					$error = _("Cette adresse email est déjà inscrite");
				}else{
					$add = nlr_subscribers_add($_POST['email'], $oc_id);
					if( !$add ){
						$error = str_replace('#param[numéro de téléphone]#', htmlspecialchars($_POST['email']), _('L\'inscription de l\'adresse #param[numéro de téléphone]# a échoué pour une raison inconnue.'));
					}
				}
			}

			// Gestion de l'inscription pour une newsletter par téléphone
			if( $cat['type'] == 'phone' ){
				if( !trim($_POST['email']) || !isphone($_POST['email']) ){
					$error = _("Veuillez indiquer un numéro de téléphone valide à inscrire.");
				}elseif( newsletter_is_inscripted($_POST['email'], $oc_id, 0, 0, 'phone') ){
					$error = _("Ce numéro de téléphone est déjà inscrit.");
				}else{
					$add = nlr_subscribers_add(null, $oc_id, 0, $_POST['email']);
					if( !$add ){
						$error = str_replace('#param[numéro de téléphone]#', htmlspecialchars($_POST['email']), _('L\'inscription du téléphone #param[numéro de téléphone]# a échoué pour une raison inconnue.'));
					}
				}
			}
			
			// Redirection si aucune erreur ne s'est produite lors de l'inscription
			if( !isset($error) ){
				if( isset( $_GET['oc'] ) ){
					header('Location: list.php?oc='. $_GET['oc'] );
					exit;
				}else{
					header('Location: list.php');
					exit;
				}
			}
		}
	}

	// Suppression manuelle d'une adresse email pour une newsletter
	if( isset($_POST['del'])){
		if(isset($_REQUEST['sub']) && is_array($_REQUEST['sub'])) {
			foreach( $_REQUEST['sub'] as $subs ) {
				$subscriber = ria_mysql_fetch_array(nlr_subscribers_get('', $subs, '', $_GET['oc']));
				switch ($subscriber['type_cat']){
					case 'phone' : nlr_subscribers_del( null, $_GET['oc'], 0, false, $subscriber['phone'] ); break;
					case 'email' : nlr_subscribers_del( $subscriber['email'], $_GET['oc'] ); break;
				}
			}
		} else if( isset($_REQUEST['sub'] ) ) {
			$subscriber = ria_mysql_fetch_array(nlr_subscribers_get('', $_REQUEST['sub'], '', $_GET['oc']));
			switch ($subscriber['type_cat']){
				case 'phone' : nlr_subscribers_del( null, $_GET['oc'], 0, false, $subscriber['phone'] ); break;
				case 'email' : nlr_subscribers_del( $subscriber['email'], $_GET['oc'] ); break;
			}
		}
		
		if( isset( $_GET['oc'] ) ) {
			header('Location: list.php?oc='. $_GET['oc'] );
		}else{
			header('Location: list.php');
		}
		exit;
	}

	// Bouton filtre avancé
	if( isset($_POST['filter']) ){
		header('Location: filter.php?oc='. $_GET['oc']);
		exit;
	}

	// Bouton Exporter
	if( isset($_POST['export']) ){
		$location = 'export.php';
		$loc_params = array();
		
		// Prépare les paramètres de l'export pour exporter ce qui est visible dans la liste en cours de consultation

		$_POST['type'] = isset($_POST['type']) ? str_replace('t-', '', $_POST['type']) : 0;
		if( isset($_POST['type']) && $_POST['type']!=NEWSLETTER_TYPE_ALL ){
			$loc_params[] = 'type='.urlencode( $_POST['type'] );
		}
		
		if( isset($_POST['date-start']) && $_POST['date-start'] ){
			$loc_params[] = 'date-start='.urlencode($_REQUEST['date-start']);
		}

		if( isset($_POST['date-end']) && $_POST['date-end'] ){
			$loc_params[] = 'date-end='.urlencode($_REQUEST['date-end']);
		}

		if( isset($_REQUEST['search-email']) ){
			$loc_params[] = 'search-email='.urlencode($_REQUEST['search-email']);
		}

		if( isset($_REQUEST['seg_id']) && is_numeric($_REQUEST['seg_id']) ){
			$loc_params[] = 'seg_id='.urlencode($_REQUEST['seg_id']);
		}

		if( isset($_GET['oc']) && is_numeric($_GET['oc']) && $_GET['oc'] ){
			$loc_params[] = 'oc='.$_GET['oc'];
		}

		if( count($loc_params) ){
			$location .= '?'.implode( '&', $loc_params );
		}

		header('Location: '.$location);
		exit;
	}

	// Titre de la page
	define('ADMIN_PAGE_TITLE', ( isset($c) ? $c['cat'].' - ' : ''  )._('Newsletters').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
	
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_EDIT') ){ ?>
		<h2><?php print _('Newsletter').' '; print isset($c) ? htmlspecialchars($c['cat']).' <a class="edit-cat" href="edit-cat.php?cat='.$c['id'].'">'._('Modifier cette newsletter').'</a>' : ''; ?></h2>
	<?php }else{ ?>
		<h2><?php print _('Newsletter'); ?> <?php print isset($c) ? htmlspecialchars($c['cat']) : ''; ?></h2>
	<?php } ?>

<?php
	if( !isset($_REQUEST['type']) || !is_numeric($_REQUEST['type']) ){
		$_REQUEST['type'] = NEWSLETTER_TYPE_ALL;
	}

	if( isset($error) && trim($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	// Valeurs par défaut pour le filtre avancé
	if( !isset($_REQUEST['search-email']) ){
		$_REQUEST['search-email'] = '';
	}

	$_GET['date-start'] = isset($_GET['date-start']) && $_GET['date-start'] ? $_GET['date-start'] : false;
	$_GET['date-end'] = isset($_GET['date-end']) && $_GET['date-end'] ? $_GET['date-end'] : false;
	$_GET['seg_id'] = isset($_GET['seg_id']) && is_numeric($_GET['seg_id']) ? $_GET['seg_id'] : 0;
	
	// Chargement des adresses
	$search_phone = isset($c) && $c['type']=='phone' ? $_REQUEST['search-email'] : '';
	$search_email = isset($c) && $c['type']=='email' ? $_REQUEST['search-email'] : '';
	$emails = nlr_subscribers_get( $_REQUEST['type'], 0, $search_email, $_GET['oc'], 0, $_GET['date-start'], $_GET['date-end'], $_GET['seg_id'], 0, false, $search_phone );
	
	$pages = ceil( ria_mysql_num_rows($emails) / 50 );
	$page = isset($_GET['p']) && is_numeric($_GET['p']) && $_GET['p']>0 ? $_GET['p'] : 1;

	if( $page<1 ){
		$page = 1; if( $page>$pages ) $page = $pages;
	}
	if( $page>1 ){
		ria_mysql_data_seek( $emails, ($page-1)*50 );
	}
	
	$js_params = $_GET['oc'].', -1, \''.$_GET['date-start'].'\', \''.$_GET['date-end'].'\', \''.$_GET['seg_id'].'\'';
	
	// Nombre de colonnes
	if( $_GET[ 'oc' ] == 0 ) { 
		$cols = 5; 
	} else { 
		$cols = 4; 
	}
?>
<form method="post" action="list.php?<?php print 'oc='.$_GET['oc']; ?>">
	<input type="hidden" name="date-start" id="date-start" value="<?php print isset($_REQUEST['date-start']) ? $_REQUEST['date-start'] : ''; ?>" />
	<input type="hidden" name="date-end" id="date-end" value="<?php print isset($_REQUEST['date-end']) ? $_REQUEST['date-end'] : ''; ?>" />
	<input type="hidden" name="seg_id" id="seg_id" value="<?php print isset($_REQUEST['seg_id']) ? $_REQUEST['seg_id'] : ''; ?>" />
	<table class="checklist large" id="table-list-newsletter">
		<caption><?php print _('Inscriptions à la newsletter'); ?> (<?php
			print ria_number_format(ria_mysql_num_rows($emails));
			switch( $_REQUEST['type'] ){
				case NEWSLETTER_TYPE_ALL:
					if( isset($c) && $c['type']=='phone' ){
						print ' '._('numéros de téléphone');
					}else{
						print ' '._('adresses emails');
					}
					break;
				case NEWSLETTER_TYPE_PRE_INSCRIPT:
					print ' '._('inscriptions non terminées');
					break;
				case NEWSLETTER_TYPE_INSCRIPT:
					print ' '._('inscrits à la newsletter');
					break;
				case NEWSLETTER_TYPE_PRE_UNINSCRIPT:
					print ' '._('désinscriptions non terminées');
					break;
				case NEWSLETTER_TYPE_UNINSCRIPT:
					print ' '._('désinscrits de la newsletter');
					break;
			}
		?>)</caption>
		<thead>
			<tr>
				<th colspan="<?php print ( $_GET[ 'oc' ] == 0  ? 5 : 4); ?>">
					<label for="search-email"><?php print isset($c) && $c['type']=='phone' ? _('Tél :') : _('Email :'); ?></label>
					<input type="text" name="search-email" id="search-email" maxlength="75" value="<?php if( isset($_REQUEST['search-email']) ) print htmlspecialchars($_REQUEST['search-email']); ?>" />
					<input type="submit" name="search" value="<?php print _('Chercher'); ?>" onclick="return nlrSwitchTablePage( <?php print '1, 0, 0, 0, '.$js_params; ?> )" />
				
					<div class="float-right">
						<select name="type" id="type" onchange="return nlrSwitchTablePage( <?php print '1, 0, -1, -1, '.$js_params; ?> )">
							<?php
								foreach( $NEWSLETTER_TYPES as $key => $val ){
									print '<option value="t-'.$key.'" '.( $key==$_REQUEST['type'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $val ).'</option>';
								}
							?>
						</select>

						<input type="submit" name="filter" value="<?php print _('Filtre avancé'); ?>" />
					</div>
				</th>
			</tr>
			<tr class="th-head-second">
				<th id="sel" data-label="<?php print _('Tout cocher'); ?> "> <input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
		<?php		
			if( !isset($c) || $c['type']=='' || $c['type']=='email' ){
				print '<th id="email-th" class="thead-none">' . _('Adresse email') . '</th>';
			}else{
				print '<th id="tel-th" class="thead-none">' . _('Téléphone') . '</th>';
			}
		?>
				<th id="state" class="thead-none"><?php print _('Etat'); ?></th>
				<th id="account" class="thead-none"><?php print _('Compte client'); ?></th>
				<?php if( $_GET[ 'oc' ] == 0 ){
					print '<th id="cat">'._('Newsletter').'</th>';
				} ?>
				<!--<th id="email-th"><?php //print _('Adresse email'); ?></th>
				<th id="state"><?php //print _('Etat'); ?></th>
				<th id="account"><?php //print _('Compte client'); ?></th>-->
			</tr>
		</thead>
		<tbody>
			<?php
				
				if( !ria_mysql_num_rows($emails) ){
					print '<tr><td colspan="' . $cols . '">'._('Aucune adresse').'</td></tr>';
				}else{
					$count = 0;
					while( ($e = ria_mysql_fetch_array($emails)) && $count<50 ){
						print '	<tr>
									<td headers="sel"><input type="checkbox" class="checkbox" name="sub[]" value="'.$e['id'].'" /></td>';
						if( !isset($c) || $c['type'] == '' || $c['type'] == 'email' ){
							print '<td headers="email" data-label="'._('E-mail : ').' "><a href="edit.php?id='.$e['id'].'&amp;cat='. $_GET[ 'oc' ] .'" title="'._('Afficher la fiche d\'inscription pour l\'adresse').' '.htmlspecialchars($e['email']).'">'.htmlspecialchars($e['email']).'</a></td>';
						}else{
							print '<td headers="phone" data-label="'._('Téléphone : ').' "><a href="edit.php?id='.$e['id'].'&amp;cat='. $_GET[ 'oc' ] .'" title="'._('Afficher la fiche d\'inscription pour l\'adresse').' '.htmlspecialchars($e['phone']).'">'.htmlspecialchars($e['phone']).'</a></td>';
						}
						print '<td headers="state" data-label="'._('Etat : ').' ">';
						if( $e['uninscript-confirmed'] ){
							print '<span class="nlr-uninscript">'._('Désinscrit depuis le').' '.ria_date_format($e['uninscript-confirmed']).'</span>';
						}elseif( $e['inscript-confirmed'] ){
							if( $e['uninscript-requested'] )
								print '<span class="nlr-pre-uninscript">'._('Inscrit depuis le').' '.ria_date_format($e['inscript-confirmed']).'</span>';
							else
								print '<span class="nlr-inscript">'._('Inscrit depuis le').' '.ria_date_format($e['inscript-confirmed']).'</span>';
						}else{
							print str_replace(
								'#param[date]#',
								ria_date_format($e['inscript-requested']),
								_('<span class="nlr-pre-inscript">Inscription initiée le #param[date]#, mais non terminée</span>')
							);
						}
						print '	</td>
								<td data-label="'._('Compte client : ').' ">'.( $e['usr_id'] ? '<a href="../../customers/edit.php?usr='.$e['usr_id'].'">'._('Oui').'</a>' : ''._('Non').'' ).'</td>';
						if( $_GET[ 'oc' ] == 0 )
							print '	<td>'. $e[ 'cat' ] .'</td>
								</tr>';
						$count++;
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2" class="align-left">
					<input type="submit" name="export" value="<?php print _('Exporter'); ?>" />
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_INSCRIPT') ){ ?>
					<input type="submit" name="del" value="<?php print _('Désinscrire'); ?>" onclick="return nlr_uninscript()" />
					<?php } ?>
				</td>
				<td colspan="<?php if( $_GET[ 'oc' ] == 0 ){ print '3'; } else { print '2'; } ?>">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_INSCRIPT') ){ ?>

					<label for="email"><?php print isset($c) && $c['type']=='phone' ? _('Téléphone :') : _('Email :'); ?></label>
					<?php
						if( isset($c) && $c['type']=='phone' ){
							print '<input type="text" name="email" id="email" maxlength="75" />';
						}else{
							print '<input type="email" name="email" id="email" maxlength="75" />';
						}
					?>
					<input type="submit" name="add" value="<?php print _('Inscrire'); ?>" />
					<?php } ?>
				</td>
			</tr>
			<?php if( $pages>1 ){ ?>
			<tr id="pagination">
				<td colspan="2" class="align-left"><?php print 'Page '.$page.'/'.$pages; ?></td>
				<td colspan="<?php if( $_GET[ 'oc' ] == 0 ){ print '3'; } else { print '2'; } ?>">
					<?php
						$links = array();
						if( $page>1 )
							$links[] = '<a onclick="return nlrSwitchTablePage( '.($page-1).', '.$pages.', '.$_REQUEST['type'].', '.( isset($_POST['oc']) ? $_POST['oc'] : 0 ).', '.$js_params.' )" href="#">&laquo; '._('Page précédente').'</a>';

						$page_start = ($page-3)>1 ? $page-3 : 1;
						$page_stop = ($page+3)<$pages ? $page+3 : $pages;

						for( $p=$page_start; $p<=$page_stop; $p++ ){
							if( $p==$page )
								$links[] = '<b>'.$p.'</b>';
							else
								$links[] = '<a onclick="return nlrSwitchTablePage( '.$p.', '.$pages.', '.$_REQUEST['type'].', '.( isset($_POST['oc']) ? $_POST['oc'] : 0 ).', '.$js_params.' )" href="#">'.$p.'</a>';
						}

						if( $page<$pages )
							$links[] = '<a onclick="return nlrSwitchTablePage( '.($page+1).', '.$pages.', '.$_REQUEST['type'].', '.( isset($_POST['oc']) ? $_POST['oc'] : 0 ).', '.$js_params.' )" href="#">'._('Page suivante').' &raquo;</a>';
						print implode(' | ',$links);
					?>
				</td>
			</tr>
			<?php } ?>
		</tfoot>
	</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
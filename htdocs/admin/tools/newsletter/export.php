<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');

	require_once('newsletter.inc.php');

	// Valeurs par défaut pour le filtre avancé
	if( !isset($_GET['type']) || !in_array($_GET['type'], array_keys($NEWSLETTER_TYPES)) ){
		$_GET['type'] = NEWSLETTER_TYPE_ALL;
	}
	if( !isset($_GET['oc']) || !is_numeric($_GET['oc']) || $_GET['oc'] < 0 ){
		$_GET['oc'] = 0;
	}
	if( !isset($_GET['date-start']) || !isdateheure($_GET['date-start']) ){
		$_GET['date-start'] = false;
	}
	if( !isset($_GET['date-end']) || !isdateheure($_GET['date-end']) ){
		$_GET['date-end'] = false;
	}
	if( !isset($_GET['seg_id']) || !is_numeric($_GET['seg_id']) || $_GET['seg_id'] < 0 ){
		$_GET['seg_id'] = 0;
	}
	if( !isset($_GET['search-email']) ){
		$_GET['search-email'] = '';
	}
	
	$fields = array(
		'title_name'			=> _('Civilité'),
		'firstname'				=> _('Prénom'),
		'lastname'				=> _('Nom'),
		'society'				=> _('Société'),
		'email'					=> _('Email'),
		'phone'					=> _('Téléphone'),
		'inscript-confirmed'	=> _('Date d\'inscription'),
		'code'					=> _('Code de désinscription'),
		'inscript-status'		=> _('Statut de l\'inscription')
	);
	
	// Chargement des adresses
	$emails = nlr_subscribers_get( $_GET['type'],0, $_GET['search-email'], $_GET['oc'], 0, $_GET['date-start'], $_GET['date-end'], $_GET['seg_id'] );
	
	// Chargement des champs avancés de la classe "Inscriptions à la newsletter"
	if( $field_advanced = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_NLR_SUBSCRIBERS ) ){
		while( $field = ria_mysql_fetch_assoc($field_advanced) ){
			$fields[ 'f'.$field['id'] ] = ria_csv_escape( $field['name'] );
		}
	}
	
	$rows = array();
	$rows[] = '"'.implode('";"', $fields).'"';
	
	while( $r = ria_mysql_fetch_assoc($emails) ){
		
		// charge les valeurs de champ avancé pour cet inscrit
		if( $field_advanced = fld_fields_get( 0, 0, 0, 0, 0, $r['id'], null, array(), false, array(), null, CLS_NLR_SUBSCRIBERS ) ){
			while( $fld = ria_mysql_fetch_assoc($field_advanced) ){
				if( trim($fld['obj_value']) ){
					$r[ 'f'.$fld['id'] ] = $fld['obj_value'];
				}
			}
		}
		
		// détermine le statut de l'inscription
		$r['inscript-status'] = $NEWSLETTER_TYPES[ NEWSLETTER_TYPE_ALL ];
		if( trim($r['uninscript-requested']) && !trim($r['uninscript-confirmed']) ){
			$r['inscript-status'] = $NEWSLETTER_TYPES[ NEWSLETTER_TYPE_PRE_UNINSCRIPT ];
		}elseif( trim($r['inscript-confirmed']) && !trim($r['uninscript-confirmed']) ){
			$r['inscript-status'] = $NEWSLETTER_TYPES[ NEWSLETTER_TYPE_INSCRIPT ];
		}elseif( !trim($r['inscript-confirmed']) ){
			$r['inscript-status'] = $NEWSLETTER_TYPES[ NEWSLETTER_TYPE_PRE_INSCRIPT ];
		}elseif( trim($r['uninscript-confirmed']) ){
			$r['inscript-status'] = $NEWSLETTER_TYPES[ NEWSLETTER_TYPE_UNINSCRIPT ];
		}
		
		// parsage de l'heure de l'inscription
		$r['inscript-confirmed'] = str_replace('à', '', $r['inscript-confirmed']);
		
		$elems = array();
		foreach( array_keys($fields) as $fld_name ){
			if( !isset($r[ $fld_name ]) ){
				$elems[] = '';
			}else{
				$elems[] = ria_csv_escape( $r[ $fld_name ] );
			}
		}
		
		$rows[] = '"'.implode('";"', $elems).'"';
	}

	// Détermine si l'on est sous Windows ou Mac
	$user_agent = strtolower( getenv('HTTP_USER_AGENT') );
	$is_mac = strstr($user_agent, 'macintosh');

	// Encodage des caractères selon l'OS utilisé
	foreach( $rows as $k_row => $row ){
		if( $is_mac ){
			$row = str_replace("\r\n", " ", $row);
			$row = preg_replace('/[ ]{2,}/', ' ', $row);

			$rows[$k_row] = iconv("UTF-8", "macintosh", $row);
		}else{
			$rows[$k_row] = iconv("UTF-8", "ISO-8859-1//TRANSLIT", $row);
		}
	}
	
	// Prépare l'entête du téléchargement selon l'OS utilisé
	if( !$is_mac ){
		header('Pragma: no-cache');
		header('Expires: 0');
		header('Content-disposition: attachment; filename="newsletter-'.date('Y-m-d').'.csv"');
	}else{
		header('Content-Description: File Transfer');
		header('Content-Type: application/octet-stream');
		header('Content-Disposition: attachment; filename="newsletter-'.date('Y-m-d').'.csv"');
		header('Expires: 0');
		header('Cache-Control: must-revalidate');
		header('Pragma: public');
	}

	print implode("\n", $rows);


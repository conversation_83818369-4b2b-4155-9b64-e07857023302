<?php

	/**	\file index.php
	 *	Cette page affiche la liste des newsletters.
	 */

	require_once('newsletter.inc.php');
	require_once('users.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');

	// Bouton Ajouter
	if( isset($_POST['submit_add_cat']) ){
		if( isset($_POST['add_cat']) && !trim($_POST['add_cat']) ){
			$error = _('Veuillez indiquer le nom de la liste à créer dans le champ Nom situé à côté du bouton Ajouter.');
		}elseif( isset($_POST['add_cat']) && trim($_POST['add_cat'])!='' ){
			$type_cat = (isset($_POST['add_cat_type'])) ? $_POST['add_cat_type'] : '';
			$rcat = nlr_categorie_add( ucfirst(trim($_POST['add_cat'])), null, $type_cat );
			if( !$rcat ){
				$error = _('La création de la catégorie a échoué pour une raison inconnue.');
			}else{
				$success = _('La catégorie demandée a été créée avec succès.');
			}
		}
	}

	// Bouton Supprimer
	if( isset($_REQUEST['cat-del']) && is_array($_REQUEST['cat-del']) ){
		foreach( $_REQUEST['cat-del'] as $cat ){
			nlr_categorie_delete( $cat);
		}
	}else if( isset($_REQUEST['cat-del'] ) ){
		nlr_categorie_delete( $_REQUEST[ 'cat-del' ] );
	}

	// Bouton Retour
	if( isset($_POST['retour']) ){
		header('Location: index.php');
		exit;
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Newsletters').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');

	// Charge la liste des catégories de newsletter
	$categs = nlr_categorie_get();
	$categs_count = ria_mysql_num_rows($categs);
?>
<h2><?php print _('Newsletters'); ?> (<?php print ria_mysql_num_rows($categs); ?>)</h2>

<?php
	// Affichage des messages d'erreur et de succès
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}
	if( isset($success) ){
		print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
	}
?>

<form method="post" action="index.php">
	<table id="class" class="checklist">
		<?php
			$categs = nlr_categorie_get();
		?>
		<thead>
			<tr>
				<th data-label="<?php print _('Tout cocher :'); ?> "><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="name" class="thead-none"><?php print _('Nom'); ?></th>
				<th id="type" class="thead-none"><?php print _('Type'); ?></th>
				<th id="nb-suscribers" class="align-right thead-none"><?php print _('Nombre d\'inscrits'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
			if( !$categs || !ria_mysql_num_rows($categs) ){
			?>
			<tr>
				<td colspan="4"><?php print _('Aucune newsletter'); ?></td>
			</tr>
			<?php
			}else{
				while( $c = ria_mysql_fetch_array($categs) ){
					$type = ($c['type'] == "")?_("email"):($c['type']=="email")?_("Email"):_("Téléphone");
			?>
			<tr>
				<td class="align-center">
					<input type="checkbox" value="<?php print htmlspecialchars($c['cat']); ?>" name="cat-del[]" class="checkbox" />
				</td>
				<td data-label="<?php print _('Nom :'); ?> ">
					<a class="add-cat" href="list.php?oc=<?php print $c['id']; ?>"><?php print htmlspecialchars($c['cat']); ?></a>
				</td>
				<td data-label="<?php print _(htmlspecialchars($type).' :'); ?> ">
					<a class="add-type-cat" href="list.php?oc=<?php print $c['id']; ?>"><?php print htmlspecialchars($type); ?></a>
				</td>
				<td class="align-right" data-label="<?php print _('Nombre d\'inscrits :'); ?> "><?php print ria_number_format(nlr_categorie_mail_count($c[ 'id' ])); ?></td>
			</tr>
			<?php
				}
			}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2" class="align-left">
				<?php if( $categs_count && gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_DEL') ){ ?>
					<input type="submit" class="submit" name="submit_delete_cat" value="<?php print _('Supprimer'); ?>" />
				<?php } ?>
				</td>
				<td colspan="2">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_ADD') ){ ?>
					<label for="add_cat"><?php print _('Nom'); ?></label>
					<input type="text" name="add_cat"/>
					<label for="add_cat"><?php print _('Type'); ?></label>
					<select name="add_cat_type" id="add_cat_type">
						<option value="email"><?php print _('Email'); ?></option>
						<option value="phone"><?php print _('Téléphone'); ?></option>
					</select>
					<input type="submit" class="submit" name="submit_add_cat" value="<?php print _('Ajouter'); ?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<?php

require_once('admin/skin/footer.inc.php');
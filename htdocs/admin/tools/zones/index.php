<?php

	/**	\file index.php
	 *	Cette page affiche la liste des zones d'actions (à définir)
	 */

	require_once('advertising.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ZONE');
	
	// Gère l'enregistrement en session de la langue active
	if( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
		$_SESSION['lng'] = $_GET['lng'];
	}
	
	// Enregistre le site sélectionné et recharge la page
	if( isset($_GET['wst']) ){
		if( $_GET['wst']=='all' ){ 
			$_SESSION['websitepicker'] = '0';
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
		header('Location: index.php' );
		exit;
	}
	
	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : 0;
	$plc_id 	= isset($_GET['plc_id']) && is_numeric($_GET['plc_id']) && $_GET['plc_id'] > 0 ? $_GET['plc_id'] : 0;
	$active 	= isset($_GET['active']) && is_numeric($_GET['active']) && $_GET['active'] >= 0 ? $_GET['active'] : false;
	if( $active===false ){
		$active = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
	}else{
		session_set_periodpicker_state($active);
	}

	$ar_empl = array();

	$r_plc = adv_places_get(0, _ADV_PLC_ACTION_ZONE);
	if( $r_plc ){
		while( $plc = ria_mysql_fetch_assoc($r_plc) ){
			$ar_empl[ $plc['id'] ] = $plc;
		}
	}

	if( !array_key_exists($plc_id, $ar_empl) ){
		$plc_id = 0;
	}
	
	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_POST['bnr']) && is_array($_POST['bnr']) ){
		foreach( $_POST['bnr'] as $b ){
			adv_banners_del( $b );
		}
		header('Location: index.php');
		exit;
	}

	// Bouton Enregistrer les positions
	if( isset($_POST['save']) && isset($_POST['bnr-pos']) && is_array($_POST['bnr-pos']) ){
		foreach( $_POST['bnr-pos'] as $id => $pos ){
			adv_action_zone_update_pos( $id, $pos );
		}
		header('Location: index.php');
		exit;
	}

	$can_move = gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_MOVE');

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_DEL');

	if( $plc_id === 0 && $can_move ){
		$colspan = 7;
	}elseif( $plc_id === 0 || $can_move){
		$colspan = 6;
	}else{
		$colspan = 5;
	}

	if( !$checkbox ){
		$colspan--;
	}

	define( 'ADMIN_PAGE_TITLE', _('Zones d\'actions') . ' - ' . _('Outils') );
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Zones d\'actions'); ?></h2>
	<p class="notice"><?php print _('Les Zones d\'actions sur fond vert sont actuellement publiées. En bleu, celles qui seront publiées ultérieurement.'); ?></p>
<?php
	$website = wst_websites_get();
	
	print '<div class="stats-menu">';
		require_once('view.translate.inc.php');
		
		// Récupère la langue active
		$lng = view_selected_language();
	
		print view_translate_menu( 'index.php', $lng, true );
		print view_websites_selector( $wst_id, true, 'riapicker', true );

		// Filtre par emplacement
		print '
			<div id="riaplaceadvertising" class="riapicker riapicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name">'._('Emplacements').'</span><br />
						<span class="view">'.( !array_key_exists( $plc_id, $ar_empl ) ? _('Tous les emplacements') : strcut($ar_empl[ $plc_id ]['name'], 40) ).'</span>
					</div>
					<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" alt="" class="fleche" /></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="e-0">'._('Tous les emplacements').'</a>
					<a class="selector-sep"></a>
		';

		foreach( $ar_empl as $one_emp ){
			print '<a name="e-'.$one_emp['id'].'">'.htmlspecialchars( $one_emp['name'] ).'</a>';
		}

		print '
				</div>
			</div>
		';

		// Filtre de période de publication
		view_state_periodpicker($active, false, 7);

		print '<div class="clear"></div>';
	print '</div>';


	// Charge la liste des zones d'action
	if( intval($active)==0 ){
		$opened = false;
	}else{
		$opened = intval($active);
	}

	// Charge la liste des zones d'action
	$action_zones = adv_action_zones_get( $plc_id, 0, $opened, $lng, $wst_id, false );
	
?>

	<form action="index.php" method="post">
	<table class="checklist" id="table-zones-actions">
	<thead>
		<tr>
			<?php if( $checkbox ){ ?>
			<th id="bnr-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<?php } ?>
			<th id="bnr-name"><?php print _('Nom de la zone'); ?></th>
			<th id="bnr-url"><?php print _('Url'); ?></th>
			<th id="bnr-from"><?php print _('Du'); ?></th>
			<th id="bnr-to"><?php print _('Au'); ?></th>
			<?php print $plc_id === 0 ? '<th id="bnr-emp">'._('Emplacement').'</th>' : ''; ?>
			<?php if( $can_move ){ ?>
			<th id="bnr-pos">
				<?php print _('Position'); ?>
			</th>
			<?php } ?>
		</tr>
	</thead>
	<tbody>
		<?php
			if( !$action_zones || !ria_mysql_num_rows($action_zones) ){
				print '<tr><td colspan="'.$colspan.'">'._('Aucune zones d\'actions').'</td></tr>';
			}else{
				while( $r = ria_mysql_fetch_array($action_zones) ){
					if( $lng!=$config['i18n_lng'] ){
						$tsk = fld_translates_get( CLS_BANNER, $r['id'], $lng, $r, array(_FLD_BNR_NAME=>'name', _FLD_BNR_URL=>'url'), true );
						$r['name'] = trim($tsk['name'])!='' ? $tsk['name'] : '[Non traduit] '.$r['name']; 
						$r['url'] = $tsk['url'];
					}
					print '<tr'.($r['active'] == 1 ? ' class="bg-color-green"' : ($r['active'] == 2 ? ' class="bg-color-blue"' : '')).'>';
					if( $checkbox ){
						print '<td headers="bnr-sel"><input type="checkbox" class="checkbox" name="bnr[]" value="'.$r['id'].'" /></td>';
					}
					if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_VIEW') ){
						print '<td headers="bnr-name"><a href="edit.php?id='.$r['id'].'&amp;lng='.$lng.'">'.htmlspecialchars($r['name']).'</a></td>';
					}else{
						print '<td headers="bnr-name">'.htmlspecialchars($r['name']).'</td>';						
					}
					print '
						<td headers="bnr-url">'.htmlspecialchars( $r['url'] ).'</td>
						<td headers="bnr-from">'.ria_date_format($r['date_from']).' à '.$r['hour_from'].'</td>
						<td headers="bnr-to">'.( $r['date_to'] ? ria_date_format($r['date_to']).' à '.$r['hour_to'] : '' ).'</td>
					';

					if( $plc_id === 0 ){
						$name_emp = array_key_exists( $r['plc_id'], $ar_empl ) ? $ar_empl[ $r['plc_id'] ]['name'] : '';
						print '<td headers="bnr-emp">'.htmlspecialchars( $name_emp ).'</td>';
					}
					if( $can_move ){
						print '<td headers="bnr-pos"><input  type="text" maxlength="16" class="number" value="'.($r['pos'] != '' ? $r['pos'] : '' ).'" name="bnr-pos['.$r['id'].']"/></td>';
					}
					print '</tr>';
				}
			}
		?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="<?php print $colspan; ?>">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_DEL') && ria_mysql_num_rows($action_zones)>0 ){ ?>
				<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer les zones sélectionnées'); ?>" onclick="return confirmBannerDelList();" />
				<?php } ?>
				<div style="float: right">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_ADD') ){ ?>
					<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
				<?php }
				if( ria_mysql_num_rows($action_zones)>0 ){ ?>
				<input type="submit" name="save" class="" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Enregistrer les positions'); ?>"/>
				<?php } ?>
				</div>
			</td>
		</tr>
	</tfoot>
	</table>
	</form>

<script><!--
		var js_plc_id = <?php print $plc_id ?>;
		var js_active = <?php print intval($active) ?>;
	--></script>
<?php require_once('admin/skin/footer.inc.php'); ?>

<?php

	require_once('advertising.inc.php');
	require_once('websites.inc.php');
	unset($error);

	// Vérifie que l'utilisateur a bien accès à la page
	if( isset($_GET['id']) && $_GET['id'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ZONE_VIEW');
	}elseif( !isset($_GET['id']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ZONE_ADD');
	}
	// Vérifie que l'utilisateur à bien le droit de modifier une zone d'action
	if( isset($_GET['id']) && $_GET['id'] != 0 ){
		if( isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['addmdl']) || isset($_POST['savefields']) ){
			gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ZONE_EDIT');
		}
	}
	
	// Vérifie l'identifiant passé en argument
	if( isset($_GET['id']) && $_GET['id']!=0 && !adv_banners_exists($_GET['id']) ){
		header('Location: index.php');
		exit;
	}
	
	// Récupère la langue active
	require_once('view.translate.inc.php');
	$lng = $_GET['lng'] = view_selected_language();
	
	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Gestion des onglets
	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}
	$tab = $_GET['tab'];
	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabFields']) ){
		$tab= 'fields';
	}
	
	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_GET['id']) ){
		if( !adv_banners_del($_GET['id']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la bannière.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			header('Location: index.php');
			exit;
		}
	}
	
	// Bouton Enregistrer
	if( isset($_POST['save']) || isset($_POST['save_stay']) ){
		$wst = wst_websites_get();
		if( !(($wst && ria_mysql_num_rows($wst)>1) || (is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1)) ){
			$_POST['language'] = array();
			
			while( $r = ria_mysql_fetch_assoc($wst) ){
				$_POST['language'][ $r['id'] ] = array();

				foreach( $config['i18n_lng_used'] as $one_lng ){
					$_POST['language'][ $r['id'] ][] = $one_lng;
				}
			}
		}

		if( !isset($_POST['name']) || trim($_POST['name'])=='' ){
			$error = _("Veuillez indiquer le titre de la bannière.");
		}else{
			if( !isset($_POST['from']) ) $_POST['from'] = '';

			if( isset($_POST['from-opt']) ){
				if( $_POST['from-opt']==2 )
					$_POST['date_from'] = date('d/m/Y');
				elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_from']) )
					$error = _("La date de début de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
				elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$_POST['hour_from']) )
					$error = _("L'heure de début de publication doit être saisie au format hh:mm.\nVeuillez réessayer");
			}

			if( isset($_POST['to-opt']) ){
				if( $_POST['to-opt']==2 ){
					$_POST['date_to'] = $_POST['hour_to'] = '';
				}
				elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_to']) )
					$error = _("La date de fin de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
				elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$_POST['hour_to']) )
					$error = _("L'heure de fin de publication doit être saisie au format hh:mm.\nVeuillez réessayer");
			}

			if( !isset($error) ){
                $choose = isset($_POST['choose-dest']) ? $_POST['choose-dest'] : 'url';
                $prd_id = 0; $url = '';
                if( $choose=='prd' ){
                    $prd_id = $_POST['prd_id'];
                } else {
                    $url = $_POST['url'];
                }
                
				if( isset($_GET['id']) && $_GET['id']==0 ){
					$_GET['id'] = adv_action_zone_add( $_POST['plc'], $_POST['name'], '', $url, $_POST['date_from'].' '.$_POST['hour_from'], $_POST['date_to'].' '.$_POST['hour_to'], $prd_id, $_POST['desc'] );
					if( !$_GET['id'] )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la bannière.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
					if(	!adv_action_zone_update( $_GET['id'], $_POST['plc'], $_POST['name'], '', $url, $_POST['date_from'].' '.$_POST['hour_from'], $_POST['date_to'].' '.$_POST['hour_to'], $prd_id, $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la bannière.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
					$old = ria_mysql_fetch_array( adv_action_zones_get( 0, $_GET['id'], false, false, false, false ) );
					$values = array(
						_FLD_BNR_NAME=>$_POST['name'],
						_FLD_BNR_NAME_ALT=>'',
						_FLD_BNR_URL=>$url,
                        _FLD_BNR_PRD=>($prd_id>0 ? $prd_id : '')
					);
					
					$url = trim($url)=='' ? $old['url'] : $url;
					$prd_id = !is_numeric($prd_id) || $prd_id<=0 ? $old['prd_id'] : $prd_id;
					
					if( !fld_translates_add($_GET['id'], $_GET['lng'], $values) )
						$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					elseif( !adv_action_zone_update( $_GET['id'], $_POST['plc'], $old['name'], '', $url, $_POST['date_from'].' '.$_POST['hour_from'], $_POST['date_to'].' '.$_POST['hour_to'], $prd_id, $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la bannière.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
				
				if( !isset($error) ) {
					adv_website_del( $_GET['id'] );
					
					if( isset($_POST['language']) && is_array($_POST['language']) && sizeof($_POST['language']) ){
						foreach( $_POST['language'] as $wst=>$langs ){
							foreach( $langs as $one_lng ){
								adv_websites_add( $_GET['id'], $wst, $one_lng );
							}
						}
					}
				}
			}

			if( !isset($error) ){
				if( !isset($_POST['save_stay']) ){
					header('Location: index.php?lng='.$_GET['lng']);
				}else{
					header('Location: edit.php?id='.$_GET['id']);
				}

				exit;
			}
		}
	}
	
	// Action sur l'onglet "Avancés"
	if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ){
		view_admin_tab_fields_actions( CLS_BANNER, $_GET['id'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );
	}
	
	$banner = array('id'=>0,'name'=>'','plc_id'=>'','alt'=>'', 'desc' => '', 'url'=>'','date_from'=>'','hour_from'=>'00:00','date_to'=>'','hour_to'=>'23:59','image'=>'','prd_id'=>0);

	if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ){
		$rbanner = adv_action_zones_get( 0, $_GET['id'], false, false, false, false );
		if( !$rbanner || !ria_mysql_num_rows($rbanner) ){
			header('Location: index.php?lng='.$_GET['lng']);
			exit;
		}
		$banner = ria_mysql_fetch_array($rbanner);
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_bnr = fld_translates_get( CLS_PRODUCT, $banner['id'], $lng, $banner, array(_FLD_BNR_NAME=>'name', _FLD_BNR_NAME_ALT=>'alt', _FLD_BNR_URL=>'url', _FLD_BNR_PRD=>'prd_id' ), true );
			$banner['name'] = $tsk_bnr['name'];
			$banner['alt'] = $tsk_bnr['alt'];
			$banner['url'] = $tsk_bnr['url'];
            $banner['prd_id'] = $tsk_bnr['prd_id'];
		}
	}

	if( $banner['id'] <= 0 ){
		$tab = 'general';
	}

	define('ADMIN_PAGE_TITLE', _('Zones d\'actions').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');

	if( isset($_POST['name']) ) $banner['name'] = ucfirst(trim($_POST['name']));
	if( isset($_POST['plc']) ) $banner['plc_id'] = $_POST['plc'];
	if( isset($_POST['url']) ) $banner['url'] = trim($_POST['url']);
	if( isset($_POST['date_from']) ) $banner['date_from'] = $_POST['date_from'];
	if( isset($_POST['hour_from']) ) $banner['hour_from'] = $_POST['hour_from'];
	if( isset($_POST['date_to']) ) $banner['date_to'] = $_POST['date_to'];
	if( isset($_POST['hour_to']) ) $banner['hour_to'] = $_POST['hour_to'];
	if( isset($_POST['desc']) ) $banner['desc'] = $_POST['desc'];
	if( isset($_POST['prd_id']) ) $banner['prd_id'] = $_POST['prd_id'];

	if( $banner['hour_to']=='' ) $banner['hour_to'] = '23:59';

	if( isset($error) )
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	
	
	if( isset($banner['name']) && trim($banner['name'] !='') ) {
		echo '<h2>' . _('Zone d\'action') . ' : ' . htmlspecialchars($banner['name']) . '</h2>';
	} else {
		echo '<h2>' . _('Créer une nouvelle zone d\'action') . '</h2>';
	}
	
	// Affiche le menu de langue
	if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ) {
		print view_translate_menu( 'edit.php?id='.$banner['id'], $lng );
	}
	?>

	<form action="edit.php?id=<?php print $banner['id']; ?>&amp;type=2&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return bannerValidForm(this)">
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="Général" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( $banner['id'] > 0 ) { ?>
			<li><input type="submit" name="tabFields" value="Avancé" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">
		<?php if( $tab=='general' ){ ?>
		<table id="table-zones-edit">
			<tbody>
				<tr><th colspan="2"><?php print _('Propriétés générales'); ?></th></tr>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php print _('Titre :'); ?></label></td>
					<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($banner['name']); ?>" maxlength="90" /></td>
				</tr>
				<tr>
					<td><label for="plc"><span class="mandatory">*</span> <?php print _('Emplacement :'); ?></label></td>
					<td><select name="plc" id="plc">
						<option value="-1"><?php print _('Sélectionnez un emplacement'); ?></option>
						
						<?php
							$places = adv_places_get(0, _ADV_PLC_ACTION_ZONE);
							if( !ria_mysql_num_rows($places) ){
								print '<option value="-1">'._('Aucun emplacement défini pour votre site').'</option>';
							}else{
								while( $p = ria_mysql_fetch_array($places) ){
									if( ria_mysql_num_rows( wst_websites_get() )>1 )
										print '<option value="'.$p['id'].'"'.( $banner['plc_id']==$p['id'] ? ' selected="selected"':'' ).' data-wst-id="'.$p['wst_id'].'">'.(trim($p['wst'])!='' ? htmlspecialchars($p['wst']).' - ' : '' ).htmlspecialchars($p['name']).'</option>';
									else
										print '<option value="'.$p['id'].'"'.( $banner['plc_id']==$p['id'] ? ' selected="selected"':'' ).'  data-wst-id="-1">'.htmlspecialchars($p['name']).'</option>';
								}
							}
						?>
					</select></td>
				</tr>
				<tr>
					<td><label for="from-opt-now"><span class="mandatory">*</span> <?php print _('Publier le :'); ?></label></td>
					<td>
						<input type="radio" class="radio" name="from-opt" id="from-opt-now" value="2" <?php if( !trim($banner['date_from']) ) print 'checked="checked"'; ?> />
						<label for="from-opt-now"><?php print _('Publier immédiatement'); ?></label><br />
						<input type="radio" class="radio" name="from-opt" id="from-opt-date" value="3" <?php if( trim($banner['date_from']) ) print 'checked="checked"'; ?> />
						<label for="date_from" onclick="clickPublishFrom(this.form)" onkeypress="clickPublishFrom(this.form)"><?php print _('Publier le :'); ?></label>
						<input type="text" class="date" name="date_from" id="date_from" onkeypress="clickPublishFrom(this.form)" value="<?php print htmlspecialchars($banner['date_from']); ?>" maxlength="10" />
						<label for="hour_from"><?php print _('à'); ?></label>
						<input type="text" class="hour" name="hour_from" id="hour_from" value="<?php print htmlspecialchars($banner['hour_from']); ?>" maxlength="5" />
					</td>
				</tr>
				<tr>
					<td><label for="to-opt-now"><?php print _('Publier jusqu\'au :'); ?></label></td>
					<td>
						<input type="radio" class="radio" name="to-opt" id="to-opt-now" value="2" <?php if( !trim($banner['date_to']) ) print 'checked="checked"'; ?> /> <label for="to-opt-now"><?php print _('Publier indéfiniment'); ?></label><br />
						<input type="radio" class="radio" name="to-opt" id="to-opt-date" value="3" <?php if( trim($banner['date_to']) ) print 'checked="checked"'; ?> />
						<label for="date_to" onclick="clickPublishTo(this.form)" onkeypress="clickPublishTo(this.form)"><?php print _('Publier jusqu\'au :'); ?></label>
						<input type="text" class="date" name="date_to" id="date_to" onkeypress="clickPublishTo(this.form)" value="<?php print htmlspecialchars($banner['date_to']); ?>" maxlength="10" />
						<label for="hour_to"><?php print _('à'); ?></label>
						<input type="text" class="hour" name="hour_to" id="hour_to" value="<?php print htmlspecialchars($banner['hour_to']); ?>" maxlength="5" />
					</td>
				</tr>
				<tr>
					<td><label for="desc"><?php print _('Description :'); ?></label></td>
					<td><textarea name="desc" id="desc" col="50" line="10"><?php print htmlspecialchars($banner['desc']); ?></textarea></td>
				</tr>
		        <tr>
		            <th colspan="2"><?php print _('Destination'); ?></th>
		        </tr>
		        <tr>
		            <td><label for="choose-dest-url"><?php print _('Vers :'); ?></label></td>
		            <td>
		                <?php $choose = $banner['prd_id'] ? 'prd' : 'url'; ?>
		                <input type="radio" name="choose-dest" id="choose-dest-url" value="url" <?php print $choose=='url' ? 'checked="checked"' : ''; ?> />
		                <label for="choose-dest-url"><?php print _('Une url'); ?></label>
		                <input type="radio" name="choose-dest" id="choose-dest-prd" value="prd" <?php print $choose=='prd' ? 'checked="checked"' : ''; ?> />
		                <label for="choose-dest-prd"><?php print _('Un produit'); ?></label>
		            </td>
		        </tr>
				<tr id="choose-url" <?php print $choose=='prd' ? 'class="none"' : ''; ?>>
					<td><label for="url"><?php print _('Url de destination :'); ?></label></td>
					<td><input type="text" name="url" id="url" value="<?php print htmlspecialchars($banner['url']); ?>" size="75" maxlength="255" /></td>
				</tr>
		        <tr id="choose-prd" <?php print $choose=='url' ? 'class="none"' : ''; ?>>
		            <td><label for="prd"><?php print _('Produit :'); ?></label></td>
		            <td><?php
		                $p['name'] = ''; $p['ref'] = '';
		                if( $banner['prd_id'] ){
		                    $rp = prd_products_get_simple( $banner['prd_id'] );
		                    if( $rp && ria_mysql_num_rows($rp) ){
		                        $p = ria_mysql_fetch_array( $rp );
		                        $name_prd = $p['name'];
		                    }
		                }
		                ?>
		                <input type="hidden" name="prd_id" id="prd_id" value="<?php print $banner['prd_id']; ?>" />
		                <input type="text" name="prd" id="prd" value="<?php print trim($p['ref'])!='' ? $p['ref'].' - '.htmlspecialchars($p['name']) : ''; ?>" />
		                <input type="button" name="search-prd" id="search-prd" value="Rechercher" />
		            </td>
		        </tr>
				<?php
					print view_admin_form_websites_languages( CLS_BANNER, $banner['id'] );

					if( $banner['id']>0 ){
				?>
				<tr>
					<th colspan="2"><?php print _('Segmentation'); ?></th>
				</tr>
				<tr>
					<td><?php print _('Segments'); ?></td>
					<td><?php
						$ar_ex_seg = array();
						
						require_once('segments.inc.php');
						print '<input type="hidden" name="seg-obj-cls" id="seg-obj-cls" value="'.CLS_BANNER.'" />';
						print '<input type="hidden" name="seg-obj-id-0" id="seg-obj-id-0" value="'.$banner['id'].'" />';
						
						print '<div class="seg-obj-infos">';
						$robject = seg_objects_get_segments( CLS_BANNER, array($banner['id']) );
						if( $robject &&  ria_mysql_num_rows($robject) ){
							while( $obj = ria_mysql_fetch_array($robject) ){
								$ar_ex_seg[] = $obj['id'];
								print '	<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" title="'._('Retirer ce segment').'" alt="'._('Supprimer').'" />&nbsp;';
								print htmlspecialchars( $obj['name'] ).'<br />';
							}
						} else { print _('Aucune restriction liée aux segments.'); }
						print '	</div>';
					?></td>
				</tr>
				<tr>
					<td><label><?php print _('Ajouter :'); ?></label></td>
					<td>
						<select class="select-obj-seg" name="segment" id="segment">
							<option value="-1"><?php print _('Choisir un segment'); ?></option>
							<?php
								$rseg = seg_segments_get( 0, CLS_USER );
								if( $rseg && ria_mysql_num_rows($rseg) ){
									while( $seg = ria_mysql_fetch_array($rseg) ){
										if( in_array($seg['id'], $ar_ex_seg) ) continue;
										print '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
									} 
								}
							?>
						</select>
					</td>
				</tr>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save_stay" name="save_stay" value="<?php print _('Enregistrer'); ?>" />
					<input name="save" id="save" value="<?php print _('Enregistrer et revenir à la liste'); ?>" type="submit" />
					<input type="submit" name="cancel" value="Annuler" onclick="return bannerCancelEdit(this.form)" />
					<?php if( $banner['id']>0 && gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_DEL') ){ ?>
					<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
		<?php 
			}elseif( $tab=='fields' ){ 
				print view_admin_tab_fields( CLS_BANNER, $banner['id'], $lng );
				?>
			<script src="/admin/js/fields.js"></script>
		<?php } ?>
	</div>
	</form>
	<script>
		<?php if( isset($_GET['id']) && $_GET['id'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_TOOL_ZONE_EDIT') ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
				$('input[onclick]').attr('onclick','').unbind('click');
			});

		<?php } ?>
	</script>
	<script><!--
		var segID = false;
		var nbGroups = false;
		var nbGroupCdts = false;
		var segClsID = <?php print CLS_BANNER; ?>;
	--></script>

	<?php
		require_once('admin/skin/footer.inc.php');
	?>
<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ERRATUM');

require_once('products.inc.php');

if( isset($_GET['prd_ref']) && $_GET['prd_ref']!='' ){
	$rprd = prd_products_get(0,$_GET['prd_ref']);
	$nb = ria_mysql_num_rows($rprd);
	
	/* En-tête pour une réponse xml */
	header('Content-Type: text/xml; charset=utf-8');
	
	/* Construction de la réponse */
	print '<root>';
	if( $nb>0 ){
		$prd = ria_mysql_fetch_array($rprd);
		print '<product name="'.$prd['name'].'" supplier_ref="'.$prd['supplier_ref']. '" />';
	} else {
		print '<error id="1" message="'._('La référence du produit n\'a pas été trouvée dans la base article.').'" />';
	}
	print '</root>';
}

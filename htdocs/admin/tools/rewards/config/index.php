<?php

	/**	\file index.php
	 *
	 *	Cette page permet la configuration du système de points de fidélité
	 *
	 */
	
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD_CONFIG');

	require_once('rewards.inc.php');
	require_once('stats.inc.php');
	require_once('websites.inc.php');
	
	$website = wst_websites_get();
	$profiles = gu_profiles_get();
	
	$wst_id = isset($_POST['wst_id']) ? $_POST['wst_id'] : ($website && ria_mysql_num_rows($website)>1 ? 0 : $config['wst_id']);
	$prf_id = isset($_POST['prf_id']) ? $_POST['prf_id'] : 0;

	$prf_id = isset($_SESSION['admin_rewards_prf']) ? $_SESSION['admin_rewards_prf'] : $prf_id;
	$wst_id = isset($_SESSION['admin_rewards_wst']) ? $_SESSION['admin_rewards_wst'] : $wst_id;
	
	// Gestion des onglets
	$_GET['tab'] = isset($_GET['tab']) ? $_GET['tab'] : 'general';
	$tab = $_GET['tab'];
	
	if( isset($_POST['tabProducts']) ){
		$tab = 'products';
	}elseif( isset($_POST['tabActions']) ){
		$tab = 'actions';
	}

	// $tab = isset($_SESSION['admin_rewards_tab']) ? $_SESSION['admin_rewards_tab'] : $tab;
	
	// Chargement de la configuration selon le site et le profil choisi
	$reward = array( 
		'id'=>0, 'ratio'=>array('amount'=>0, 'pts'=>0), 'convert'=>array('amount'=>0, 'pts'=>0), 'min-amount'=>0, 'days'=>0, 'exclu-promo'=>0, 'all-catalog'=>1, 'cumul-pts'=>1, 
		'type'=>'ttc', 'type-calc'=>'order', 'days_lost'=> 0, 'nb_pts'=>0, 'days_code'=>0, 'discount'=>'', 'discount_type'=>0, 'apply_on'=>'order', 'system'=>1,'system_ratio'=>1
	);
	
	// Si l'identifiant du site et du profil sont connus, charge la configuration
	if( $wst_id && $prf_id ){
		$rwd = rwd_rewards_get( 0, $prf_id, $wst_id );
		if( $rwd && ria_mysql_num_rows($rwd) ){
			$reward = ria_mysql_fetch_array( $rwd );
			$reward['ratio'] = rwd_rewards_ratio_formated( $reward['ratio'] );
			$reward['convert'] = rwd_rewards_ratio_formated( $reward['convert'], true );
		}
	}
	
	// Enregistrement d'une configuration
	if( isset($_POST['save-config']) ){
		// Contrôle des paramètres généraux
		if( !isset($_POST['amount'], $_POST['pts'], $_POST['calcul'], $_POST['type-calc'], $_POST['cumul-pts'], $_POST['days']) ){
			$error_code = 1;
		}else{
			$_POST['amount'] = str_replace(',', '.', $_POST['amount'] );
			
			if( !is_numeric($_POST['amount']) || $_POST['amount']<=0 ){
				$error_code = 2;
			} elseif( !is_numeric($_POST['pts']) || $_POST['pts']<=0 ){
				$error_code = 3;
			} else {
				if(!isset($_POST['system'])){
					$type = 1;
				}else{
					$type = $_POST['system'];
				}
	
				$_POST['min-amount'] = str_replace(',', '.', $_POST['min-amount']);
				
				if( $type==1 ){
					$_POST['c-amount'] = str_replace(',', '.', $_POST['c-amount'] );
					
					// Système par Points / Euros
					if( !isset($_POST['c-pts'], $_POST['c-amount'], $_POST['days_lost'], $_POST['min-amount']) ){
						$error_code = 4;
					} else {
						if( !is_numeric($_POST['c-pts']) || $_POST['c-pts']<=0 ){
							$error_code = 5;
						} elseif( !is_numeric($_POST['c-amount']) || $_POST['c-amount']<=0 ){
							$error_code = 6;
						} 
					}
				} else if($type == 2) {
					$_POST['remise'] = str_replace( ',', '.', $_POST['remise'] );
					
					// Système par code promotion
					if( !isset($_POST['nb-points'], $_POST['remise'], $_POST['discount-type'], $_POST['remise-on'], $_POST['days-limit-code'], $_POST['relance-code']) ){
						$error_code = 4;
					} else {
						if( !is_numeric($_POST['nb-points']) || $_POST['nb-points']<=0 ){
						$error_code = 7;
						} elseif( !is_numeric($_POST['remise']) || $_POST['remise']<=0 ){
							$error_code = 8;
						}
					}
				}
			}
		}
		
		if( !isset($error_code) ){
			$ratio 				= array( 'pts' => $_POST['pts'], 'amount' => $_POST['amount'] );
			$type_ratio 		= isset($_POST['system_ratio']) && $_POST['system_ratio'] == 2 ? 2 : 1;
			$type_amount 		= !isset($_POST['calcul']) || !in_array( $_POST['calcul'], array('ht', 'ttc') ) ? 'ttc' : $_POST['calcul'];
			$type_calc 			= !isset($_POST['type-calc']) || !in_array( $_POST['type-calc'], array('order', 'invoice') ) ? 'order' : $_POST['type-calc'];
			$cumul_pts			= isset($_POST['cumul-pts']) && $_POST['cumul-pts'] ? true : false;
			$days				= isset($_POST['days']) && is_numeric($_POST['days']) && $_POST['days']>0 ? $_POST['days'] : 0;
			$days_lost			= isset($_POST['days_lost']) && is_numeric($_POST['days_lost']) && $_POST['days_lost']>0 ? $_POST['days_lost'] : 0;
			$min_order			= isset($_POST['min-amount']) && is_numeric($_POST['min-amount']) && $_POST['min-amount']>0 ? $_POST['min-amount'] : 0;
			$discount_type 		= !isset($_POST['discount-type']) || !in_array( $_POST['discount-type'], array(0, 1) ) ? 0 : $_POST['discount-type'];
			$remise_on 			= !isset($_POST['remise-on']) || !in_array( $_POST['remise-on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line') ) ? 'order' : $_POST['remise-on'];
			$days_limit_code 	= isset($_POST['days-limit-code']) && is_numeric($_POST['days-limit-code']) && $_POST['days-limit-code']>0 ? $_POST['days-limit-code'] : 0;
			$relance_code 		= isset($_POST['relance-code']) && is_numeric($_POST['relance-code']) && $_POST['relance-code']>0 ? $_POST['relance-code'] : 0;
			
			$ar_convert = $ar_code = $ar_product = false;
			
			switch ($type) {
				case _RWD_SYSTEM_POINTS: {
					$ar_convert = array(
						'pts' 				=> $_POST['c-pts'],
						'amount' 			=> $_POST['c-amount'],
						'min_amount' 		=> $min_order,
						'days_before_lost' 	=> $days_lost
					);
					break;
				}
				case _RWD_SYSTEM_CODE: {
					$ar_code = array(
						'pts' 				=> $_POST['nb-points'],
						'discount' 			=> $_POST['remise'],
						'discount_type'		=> $discount_type,
						'apply_on' 			=> $remise_on,
						'days' 				=> $days_limit_code,
						'days_before_lost' 	=> $relance_code
					);
					break;
				}
				case _RWD_SYSTEM_PRODUCTS: {
					$ar_product = array('days_before_lost' => $days_lost);
					break;
				}
				case _RWD_SYSTEM_REMISE: {
					$ar_code = array(
						'pts' 				=> $_POST['nb-points'],
						'discount' 			=> $_POST['remise'],
						'discount_type'		=> $discount_type,
					);
					break;
				}
			}
			
			// Contrôle les différences entre la version Postée et celle enregistrée en base de données
			$diff = $reward['id']>0 ? false : true;
			if( $reward['id']>0 ){
				if( 
					$ratio['pts']!=$reward['ratio']['pts'] || $ratio['amount']!=$reward['ratio']['amount'] || $type!=$reward['system'] || $type_ratio!=$reward['system_ratio']
					|| $type_amount!=$reward['type'] || $type_calc!=$reward['type-calc'] || $cumul_pts!=$reward['cumul-pts'] || $days!=$reward['days']
				){
					$diff = true;
				} else {
					if( $type==1 ){
						print $_POST['c-pts'].'!='.$reward['convert']['pts'].' || '.$_POST['c-amount'].'!='.$reward['convert']['amount'].' || '.$min_order.'!='.$reward['min-amount'].' || '.$days_lost.'!='.$reward['days_lost'];
						if( 
						   $_POST['c-pts']!=$reward['convert']['pts'] || $_POST['c-amount']!=$reward['convert']['amount']
						   || $min_order!=$reward['min-amount'] || $days_lost!=$reward['days_lost']
						){
							$diff = true;
						}
					} else if( $type==2 ){
						if( 
						   $_POST['nb-points']!=$reward['nb_pts'] || $_POST['remise']!=$reward['discount'] || $discount_type!=$reward['discount_type'] 
						   || $remise_on!=$reward['apply_on'] || $days_limit_code!=$reward['days_code'] || $relance_code!=$reward['days_lost']
						){
							$diff = true;
						}
					}else {
						if( 
						   $_POST['nb-points']!=$reward['nb_pts'] || $_POST['remise']!=$reward['discount'] || $discount_type!=$reward['discount_type'] 
						   || $remise_on!=$reward['apply_on'] || $days_limit_code!=$reward['days_code'] || $relance_code!=$reward['days_lost']
						){
							$diff = true;
						}
					}
				}
			}
			// Si des modifications ont été apportées, enregistrement
			if( $diff ){
				if( !rwd_rewards_add($prf_id, $ratio, $days, $type_amount, $cumul_pts, $type_calc, $type, $wst_id, $ar_convert, $ar_code, $type_ratio, $ar_product) ){
					$error_code = 9;
				}
			}
		}
		
		if( isset($error_code) ){ // Conversion du code d'erreur en message d'erreur
			switch( $error_code ){
				case 1 :
					$error = _("Un ou plusieurs paramètres obligatoires sont manquants.");
					break;
				case 2 :
					$error = _("Le montant en euro pour le ratio doit être un numérique supérieur à 0.");
					break;
				case 3 :
					$error = _("Le nombre de points renseignés pour le ratio doit être un numérique supérieur à 0.");
					break;
				case 4 :
					$error = _("Une ou plusieurs informations obligatoires sont manquantes concernant la conversion des points de fidélité.");
					break;
				case 5 : 
					$error = _("Le nombre de points renseignés pour la conversion des points doit être un numérique supérieur à 0.");
					break;
				case 6 : 
					$error = _("Le montant en euro pour la conversion des points doit être un numérique supérieur à 0.");
					break;
				case 7 :
					$error = _("Le nombre de points pour la conversion en code promotion doit être un numérique supérieur à 0.");
					break;
				case 8 :
					$error = _("La réduction doit être un numérique supérieur à 0 (remise en % ou remise en valeur HT).");
					break;
				case 9 :
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					break;
			}
		} else {
			$_SESSION['save-config-rwd'] = true;
			header('Location: /admin/tools/rewards/config/index.php');
			exit;
		}
	}
	
	
	if( isset($_POST) ){
		$days_lost = $reward['days_lost'];
		
		$reward = array(
			'id' => $reward['id'],
			'exclu-promo' => $reward['exclu-promo'],
			'all-catalog' => $reward['all-catalog'],
			
			'ratio' => array(
				'amount' 	=> isset($_POST['amount']) && is_numeric($_POST['amount']) 	? $_POST['amount'] 	: $reward['ratio']['amount'], 
				'pts'		=> isset($_POST['pts']) && is_numeric($_POST['pts']) 	? $_POST['pts'] 	: $reward['ratio']['pts']
			),
			
			'convert' => array(
				'amount'	=> isset($_POST['c-amount']) && is_numeric($_POST['c-amount']) 	? $_POST['c-amount'] 	: $reward['convert']['amount'], 
				'pts'		=> isset($_POST['c-pts']) && is_numeric($_POST['c-pts']) 		? $_POST['c-pts'] 		: $reward['convert']['pts']
			),
			
			'min-amount'	=> isset($_POST['min-amount']) && is_numeric($_POST['min-amount']) 	? $_POST['min-amount'] 	: $reward['min-amount'],
			'days'			=> isset($_POST['days']) 		? $_POST['days']		: $reward['days'],
			'cumul-pts'		=> isset($_POST['cumul-pts'])	? $_POST['cumul-pts']	: $reward['cumul-pts'], 
			'type'			=> isset($_POST['calcul'])		? $_POST['calcul']		: $reward['type'],
			'type-calc'		=> isset($_POST['type-calc']) 	? $_POST['type-calc'] 	: $reward['type-calc'],
			
			'nb_pts'		=> isset($_POST['nb-points'])	? $_POST['nb-points']	: $reward['nb_pts'],
			'days_code'		=> isset($_POST['days-limit-code']) ? $_POST['days-limit-code'] : $reward['days_code'],
			'discount'		=> isset($_POST['remise']) 		? $_POST['remise'] 		: $reward['discount'],
			'discount_type'	=> isset($_POST['discount-type']) ? $_POST['discount-type'] : $reward['discount_type'],
			'apply_on'		=> isset($_POST['remise-on'])	? $_POST['remise-on']	: $reward['apply_on'],
			'system'		=> isset($_POST['system'])		? $_POST['system']		: $reward['system'],
			'system_ratio'	=> isset($_POST['system_ratio'])		? $_POST['system_ratio']		:  $reward['system_ratio']
		);
		
		if( $reward['system'] == 1){
			$reward['days_lost'] = isset($_POST['days_lost']) ? $_POST['days_lost'] : $days_lost;
		} else {
			$reward['days_lost'] = isset($_POST['relance-code']) ? $_POST['relance-code'] : $days_lost;
		}
	}
	
	// Annulation de la modification de la configuration
	if( isset($_POST['cancel-config']) ){
		$tab = 'general';
	}
	
	// Annulation du formulaire d'inclusion / d'exclusion de produits
	if( isset($_POST['cancel-products']) ){
		$tab = 'general';
	}

	define('ADMIN_PAGE_TITLE', _('Configuration').' - '._('Système de points de fidélité').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Configuration :')._(' Système de points de fidélité'); ?></h2>
<p class="notice"><?php print _('Vous pouvez paramétrer ici les principales informations indispensables au système de fidélité pour son fonctionnement.'); ?></p>
<div class="stats-menu">
	<?php  
		print view_websites_selector( $wst_id, false, 'riapicker', true, _('Veuillez sélectionner un site') );
		
		// Sélecteur de profil
		if( ria_mysql_num_rows($profiles)>1 ){
	?>
	<div id="riaprofilepicker" class="riapicker">
		<div class="selectorview">
			<div class="left">
				<span class="function_name">Profils</span><br/>
				<?php if( $prf_id ){ 
						$tp = ria_mysql_fetch_array(gu_profiles_get($prf_id));
						print '<span class="view">'.htmlspecialchars( $tp['name'] ).'</span>';
					} else { ?>
						<span class="view"><?php print _('Veuillez sélectionner un profil'); ?></span>
				<?php }?>
			</div>
			<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
			<div class="clear"></div>
		</div>
		<div class="selector">
			<?php 
				while( $p = ria_mysql_fetch_array($profiles) ){
					if( $p['id']!=PRF_ADMIN && $p['id']!=PRF_SELLER && $p['id']!=PRF_SUPPLIER ){
						print '<a name="p-'.$p['id'].'">'.htmlspecialchars( $p['name'] ).'</a>';
					}
				}
			?>
		</div>
	</div>
	<?php } ?>
	<div class="clear"></div>
</div>


<?php
	// Affichage des messages d'erreur
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	} elseif( isset($_SESSION['save-config-rwd']) && $_SESSION['save-config-rwd'] ){
		print '<div class="success">'._('L\'enregistrement s\'est correctement déroulé').'</div>';
		unset($_SESSION['save-config-rwd']);
	}
?>

<form action="index.php" id="form-rewards" method="post">
	<input type="hidden" name="wst_id" id="wst_id" value="<?php print $wst_id; ?>" />
	<input type="hidden" name="prf_id" id="prf_id" value="<?php print $prf_id; ?>" />
	<input type="hidden" name="rwd_id" id="rwd_id" value="<?php print $reward['id']; ?>" />
	<input type="hidden" name="tab" id="tab" value="<?php print $tab; ?>" />
	
	<?php
		$show_table = !( (ria_mysql_num_rows($website)>1 && !$wst_id) || !$prf_id );
		if( !$show_table ){
			print '<div class="notice">'._('Afin de paramétrer le système, vous devez commencer par sélectionner un site et un profil ci-dessus.').'</div>';
		}
	?>
	
	<ul class="tabstrip <?php print $show_table ? '' : 'none'; ?>">
		<li><input type="button" name="tabConfig" id="tabConfig" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php 
			if( view_functionnality_access('VIEW_REWARDS_PRODUCTS') ){
		?>
			<li>
				<input type="hidden" name="showTabProduct" value="1" />
				<input type="button" name="tabProducts" id="tabProducts" value="<?php print _('Produits'); ?>" <?php if( $tab=='products' ) print 'class="selected"'; ?> />
			</li>
		<?php 
			}
			if( view_functionnality_access('VIEW_REWARDS_ACTIONS') ){
		?>
			<li><input type="button" name="tabActions" id="tabActions" value="<?php print _('Actions'); ?>" <?php if( $tab=='actions' ) print 'class="selected"'; ?> /></li>
		<?php 
			}
			if( view_functionnality_access('VIEW_REWARDS_SPONSOR') ){
		?>
			<li><input type="button" name="tabSponsors" id="tabSponsors" value="<?php print _('Parrainage'); ?>" <?php if( $tab=='sponsors' ) print 'class="selected"'; ?> /></li>
		<?php 
			}
		?>
	</ul>
	
	<div id="tabpanel" class="<?php print $show_table ? 'block' : 'none'; ?>">
		<table id="tb-tabConfig" class="tb_rewards<?php print $tab=='general' ? ' inline-block' : ' none'; ?>" >
			<thead>
				<tr>
					<th id="params" colspan="2"><?php print _('Paramètres'); ?></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>
						<label for="system_ratio"><span class="mandatory">*</span> <?php print _('Système :'); ?></label>
					</td>
					<td>
						<select name="system_ratio" id="system_ratio">
							<option value="1" <?php print $reward['system_ratio']==1 ? 'selected="selected"' : ''; ?>><?php print _('Par Ratio'); ?></option>
							<option value="2" <?php print $reward['system_ratio']==2 ? 'selected="selected"' : ''; ?>><?php print _('Par Pallier'); ?> </option>
						</select>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label for="amount"><span class="mandatory">*</span> <span class="ratio_value"><?php print $reward['system_ratio']==2 ? _('Pallier :') : _('Ratio :'); ?></span></label>
					</td>
					<td>
						<input class="ratio number" type="text" name="amount" id="amount" value="<?php print ( isset($reward['ratio']['amount']) ? number_format( $reward['ratio']['amount'], 4, ',', ' ' ) : 0 ); ?>" />
						<label for="pts"><?php print _('euro(s) dépensé(s) rapporte(nt)'); ?></label>
						<input class="ratio number" type="text" name="pts" id="pts" value="<?php print ( isset($reward['ratio']['pts']) ? number_format( $reward['ratio']['pts'], 0, '', ' ' ) : 0); ?>" />
						<label for="pts"><?php print _('point(s)'); ?></label>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label for="calcul"><span class="mandatory">*</span> <?php print _('Calculer les points selon le montant :'); ?></label>
					</td>
					<td>
						<select name="calcul" id="calcul">
							<option value="ht" <?php print $reward['type']=='ht' ? 'selected="selected"' : ''; ?>><?php print _('HT'); ?></option>
							<option value="ttc" <?php print $reward['type']=='ttc' ? 'selected="selected"' : ''; ?>><?php print _('TTC'); ?></option>
						</select>&nbsp;<?php print _('de la commande'); ?>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label for="type-calc"><span class="mandatory">*</span> <?php print _('Calculer les points lors de :'); ?></label>
					</td>
					<td>
						<select name="type-calc" id="type-calc">
							<option value="order" <?php print $reward['type-calc']=='order' ? 'selected="selected"' : ''; ?>><?php print _('La commande'); ?></option>
							<option value="invoice" <?php print $reward['type-calc']=='invoice' ? 'selected="selected"' : ''; ?>><?php print _('La facturation'); ?></option>
						</select>&nbsp;<?php print _('du produit'); ?>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label for="days"><?php print _('Validité des points (en jours'); ?>) :</label>
					</td>
					<td>
						<input class="ratio val-param number" type="text" name="days" id="days" value="<?php print number_format( $reward['days'], 0, '', ' ' ); ?>" />
						<a class="help" href="#" onclick="return false;" title="<?php print _('Précisez le nombre de jours pendant lesquels les points de fidélité peuvent être utilisés.'); ?>"></a>
						<div class="clear">
							<sub><?php print _('Laissez vide ou mettre zéro pour aucune limite de validité.'); ?></sub>
						</div>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label class="tr-sys-product" for="days_lost"><?php print _('Relance perte de points (en jours) :'); ?></label>
						<label class="tr-sys-pts" for="days_lost"><?php print _('Relance perte de points (en jours) :'); ?></label>

						<label class="tr-sys-code" for="days_lost"><?php print _('Relance fin de validité (en jours) :'); ?></label>
					</td>
					<td>
						<input class="ratio val-param number" type="text" name="days_lost" id="days_lost" value="<?php print number_format( $reward['days_lost'], 0, ',', ' ' ); ?>" />
						<a class="help" href="#" onclick="return false;" title="<?php print _('Précisez le nombre de jours avant lesquels la relance automatique d\'alerte de perte de points doit être envoyée afin que l\'utilisateur soit prévenu que ses points de fidélité ne seront plus utilisables passé ce délai.'); ?>"></a>
						<div class="clear">
							<sub><?php print _('Laissez vide ou mettre zéro pour n\'envoyer aucune relance.'); ?></sub>
						</div>
					</td>
				</tr>
				<tr>
					<td headers="params">
						<label for="cumul-pts-y"><span class="mandatory">*</span> <?php print _('Cumuler les points :'); ?></label>
					</td>
					<td>
						<div class="float-left">
							<input class="radio" type="radio" name="cumul-pts" id="cumul-pts-y" value="1" <?php print $reward['cumul-pts'] ? 'checked="checked"' : ''; ?> />
							<label for="cumul-pts-y"><?php print _('Oui'); ?></label>
							<input class="radio" type="radio" name="cumul-pts" id="cumul-pts-n" value="0" <?php print $reward['cumul-pts'] ? '' : 'checked="checked"'; ?> />
							<label for="cumul-pts-n"><?php print _('Non'); ?></label>
						</div>
						<a class="help" href="#" onclick="return false;" title="<?php print _('Cochez si oui ou non les points de fidélité peuvent être cumulés. Exemple : Si non est coché alors lors du passage de sa première commande, l\'internaute recevra des points de fidélité car il s\'agit de la première, mais il ne recevra pas de points de fidélité calculés en fonction du montant de sa commande.'); ?>"></a>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php print _('Système de conversion'); ?></th>
				</tr>
				<tr>
					<td>
						<label for="system"><span class="mandatory">*</span> <?php print _('Système :'); ?></label>
					</td>
					<td>
						<select name="system" id="system">
							<option value="1" <?php print $reward['system']==1 ? 'selected="selected"' : ''; ?>><?php print _('Points / Euros'); ?></option>
							<option value="2" <?php print $reward['system']==2 ? 'selected="selected"' : ''; ?>><?php print _('Code promotion'); ?></option>
							<option value="3" <?php print $reward['system']==3 ? 'selected="selected"' : ''; ?>><?php print _('Catalogue produits'); ?></option>
							<option value="4" <?php print $reward['system']==4 ? 'selected="selected"' : ''; ?>><?php print _('Remise automatique'); ?></option>
						</select>
					</td>
				</tr>
				<tr class="tr-sys-pts">
					<td headers="params">
						<label for="c-pts"><span class="mandatory">*</span> <?php print _('Conversion Points/Euros :'); ?></label>
					</td>
					<td>
						<input class="ratio number" type="text" name="c-pts" id="c-pts" value="<?php print (isset( $reward['convert']['pts']) ? number_format( $reward['convert']['pts'], 0, '', ' ' ) : 0); ?>" />
						<label for="c-amount"><?php print _('point(s) utilisé(s) apporte(nt) une réduction de'); ?></label>
						<input class="ratio number" type="text" name="c-amount" id="c-amount" value="<?php print (isset( $reward['convert']['pts']) ? number_format( $reward['convert']['amount'], 4, ',', ' ' ) : 0); ?>" />
						<label for="c-amount"><?php print _('euro(s)'); ?></label>
					</td>
				</tr>
				<tr class="tr-sys-pts">
					<td headers="params">
						<label for="min-amount"><?php print _('Minimum de commande :'); ?></label>
					</td>
					<td>
						<input class="ratio val-param number" type="text" name="min-amount" id="min-amount" value="<?php print (isset( $reward['min-amount']) ? number_format( $reward['min-amount'], 2, ',', ' ' ) : 0); ?>" />
						<a class="help" href="#" onclick="return false;" title="<?php print _('Précisez le montant minimum (hors frais de port) que doit faire la commande d\'un internaute afin qu\'il ait le droit d\'utiliser ses points de fidélité déjà gagnés.'); ?>"></a>
						<div class="clear">
							<sub><?php print _('Laissez vide ou mettre zéro pour aucun minimum de commande.'); ?></sub>
						</div>
					</td>
				</tr>
				<tr class="tr-sys-code tr-sys-remise">
					<td headers="params">
						<label for="nb-points"><?php print _('Nombre de points'); ?></label>
					</td>
					<td>
						<input class="ratio val-param number" type="text" name="nb-points" id="nb-points" value="<?php print $reward['nb_pts']; ?>" /><span class="float-left">&nbsp;<?php print _('Points'); ?></span>
						<a href="#" class="help help-code" onclick="return false;" title=""></a>
					</td>
				</tr>
				<tr class="tr-sys-code tr-sys-remise">
					<td headers="params">
						<label for="remise"><?php print _('Appliquer une réduction de'); ?></label>
					</td>
					<td>
						<input type="text" name="remise" id="remise" class="ratio val-param number" maxlength="9" value="<?php print $reward['discount']; ?>" />&nbsp;
						<input type="radio" class="radio" name="discount-type" id="discount-type-0" value="0" <?php print $reward['discount_type']==0 ? 'checked="checked"' : ''; ?> />
						<label for="discount-type-0">€ <abbr title="Hors Taxes"><?php print _('HT'); ?></abbr></label>
						<input type="radio" class="radio" name="discount-type" id="discount-type-1" value="1" <?php print $reward['discount_type']==1 ? 'checked="checked"' : ''; ?> />
						<label for="discount-type-1">%</label>
						
						<div class="clear"></div>
						
						<div class="div-apply-on">
							<input type="radio" value="order" id="remise-on-order" name="remise-on" <?php print $reward['apply_on']=='order' ? 'checked="checked"' : ''; ?>/>
							<label for="remise-on-order"><?php print _('Sur toute la commande'); ?></label>
						</div>
						<div class="div-apply-on">
							<input type="radio" value="min-prd" id="remise-on-min-prd" name="remise-on" <?php print $reward['apply_on']=='min-prd' ? 'checked="checked"' : ''; ?>/>
							<label for="remise-on-min-prd"><?php print _('Sur le produit le moins cher'); ?></label>
						</div>
						<div class="div-apply-on">
							<input type="radio" value="max-prd" id="remise-on-max-prd" name="remise-on" <?php print $reward['apply_on']=='max-prd' ? 'checked="checked"' : ''; ?>/>
							<label for="remise-on-max-prd"><?php print _('Sur le produit le plus cher'); ?></label>
						</div>
						<div class="div-apply-on">
							<input type="radio" value="min-line" id="remise-on-min-line" name="remise-on" <?php print $reward['apply_on']=='min-line' ? 'checked="checked"' : ''; ?>/>
							<label for="remise-on-min-line"><?php print _('Sur la ligne de commande la moins chère'); ?></label>
						</div>
						<div class="div-apply-on">
							<input type="radio" value="max-line" id="remise-on-max-line" name="remise-on" <?php print $reward['apply_on']=='max-line' ? 'checked="checked"' : ''; ?>/>
							<label for="remise-on-max-line"><?php print _('Sur la ligne de commande la plus chère'); ?></label>
						</div>
					</td>
				</tr>
				<tr class="tr-sys-code">
					<td headers="params">
						<label for="days-limit-code"><?php print _('Validité de la promotion (en jours)'); ?></label>
					</td>
					<td>
						<input class="ratio val-param number" type="text" name="days-limit-code" id="days-limit-code" value="<?php print $reward['days_code']; ?>" />
						<div class="clear">
							<sub><?php print _('Laissez vide ou mettre zéro pour aucune limite de validité.'); ?></sub>
						</div>
					</td>
				</tr>

				<!-- TABLEAU D'AJOUT -->
				<tr class="tr-sys-product">
					<td colspan="2">

						<fieldset id="rwd-add-product">
							<div class="notice error-catalog none"></div>	
							<legend><?php print _('Ajouter un produit'); ?></legend>
							
							<input type="hidden" name="prd-elem-id" id="prd-elem-id" value="" />
							<input type="hidden" name="reward-products" id="reward-products" value="" />
							
							<div>
								<label class="inline rwd-add-rule-label" for="rwd-product-name"><span class="mandatory">*</span> <?php print _('Référence produit :'); ?></label>
								<input type="text" class="ref" maxlength="16" id="rwd-product-name" name="rwd-product-name" />
								<input type="button" name="rwd-product-btn" id="rwd-product-btn" class="button" value="<?php print _('Choisir'); ?>" />
							</div>
							<div>
								<label class="inline rwd-add-rule-label" for="rwd-product-points"><span class="mandatory">*</span> <?php print _('Points de fidélité :'); ?></label>
								<input type="text" class="ref" maxlength="16" id="rwd-product-points" name="rwd-product-points" />
							</div>
							<div>
								<label class="inline rwd-add-rule-label" for="highlighting-y"><?php print _('Produit mis en avant dans l\'alerte email :'); ?></label>
								<input class="radio" type="radio" name="is-highlighting" id="highlighting-y" value="1">
								<label class="radio" for="highlighting-y"><?php print _('Oui'); ?> </label>
								<input class="radio" type="radio" checked="checked" name="is-highlighting" id="highlighting-n" value="0">
								<label class="radio" for="highlighting-n"><?php print _('Non'); ?> </label> 
							</div>
							<br><br>
							<div class="div-pts-highlighting none">
								<label class="inline rwd-add-rule-label" for="pts-highlighting"><?php print _('Seuil de points de fidélité client minimal pour la mise en avant'); ?></label>
								<input type="text" class="ref" maxlength="16" id="pts-highlighting" name="pts-highlighting" />
								<br>
							</div>

							<div id="rwd-products-buttons">
								<input title="<?php print _('Inclure au catalogue'); ?>" onclick="return addRewardProducts( true );" type="submit" value="<?php print _('Inclure au catalogue'); ?>" name="rwd-product-include" class="button" />
							</div>

						</fieldset>
					</td>
				</tr>
				<tr class="tr-sys-product">
					<td colspan="2" id="rwd-list-products">
						<div class="notice"><?php print _('Aucun produit n\'est enregistré pour le moment.'); ?></div>
					</td>
				</tr>

				<!-- TABLEAU D'AJOUT -->

			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" value="<?php print _('Enregistrer'); ?>" name="save-config" />
						<input type="submit" value="<?php print _('Annuler'); ?>" name="cancel-config" />
					</td>
				</tr>
			</tfoot>
		</table>
		
		<table id="tb-tabProducts" class="tb_rewards checklist <?php print $tab=='products' ? 'inline-block' : 'none'; ?>" >
			<caption><?php print _('Règles d\'inclusion / d\'exclusion'); ?></caption>
			<tbody>
				<tr><td colspan="2">
					<input type="radio" checked="checked" value="1" id="rwd-all-catalog-1" name="rwd-all-catalog" class="radio" />
					<label for="rwd-all-catalog-1"><?php print _('Inclure tous les produits, sauf exceptions ci-dessous :'); ?></label><br />
					<input type="radio" value="0" id="rwd-all-catalog-0" name="rwd-all-catalog" class="radio" />
					<label for="rwd-all-catalog-0"><?php print _('Exclure tous les produits, sauf exceptions ci-dessous :'); ?></label>
				</td></tr>
				<tr><td colspan="2" id="rwd-list-rules">
					<div class="notice"><?php print _('Aucune exception n\'est enregistrée pour le moment.'); ?></div>
				</td></tr>
				<tr><td colspan="2">
	
					<fieldset id="rwd-add-rule">
						<legend><?php print _('Ajouter un ou plusieurs produits'); ?></legend>
						
						<input type="hidden" name="elem-type" id="elem-type" value="" />
						<input type="hidden" name="elem-id" id="elem-id" value="" />
						
						<div>
							<input disabled="disabled" type="radio" checked="checked" id="rwd-add-rule-prd" value="prd" name="rwd-add-rule" class="radio" />
							<label class="inline rwd-add-rule-label" for="rwd-prd-name"><?php print _('Référence produit :'); ?></label>
							<input type="text" class="ref" maxlength="16" id="rwd-prd-name" name="rwd-prd-name" />
							<input type="button" name="rwd-ref-select" id="rwd-ref-select" class="button" value="<?php print _('Choisir'); ?>" />
						</div>

						<div>
							<input disabled="disabled" type="radio" id="rwd-add-rule-cat" value="cat" name="rwd-add-rule" class="radio" />
							<label class="inline rwd-add-rule-label" for="rwd-add-rule-cat"><?php print _('Une catégorie de produits :'); ?></label>
							<input class="text" type="text" readonly="readonly" id="rwd-cat-name" name="rwd-cat-name" />
							<input type="button" name="rwd-cat-select" id="rwd-cat-select" class="button" value="<?php print _('Choisir'); ?>" />
						</div>

						<div>
							<input disabled="disabled" type="radio" id="rwd-add-rule-brd" value="brd" name="rwd-add-rule" class="radio" />
							<label class="inline rwd-add-rule-label" for="rwd-add-rule-brd"><?php print _('Une marque de produits :'); ?></label>
							<input class="text" type="text" readonly="readonly" id="rwd-brd-name" name="rwd-brd-name" />
							<input type="button" name="rwd-brd-select" id="rwd-brd-select" class="button" value="<?php print _('Choisir'); ?>" />
						</div>

						<div id="rwd-rules-buttons">
							<input title="<?php print _('Inclure dans le système de points de fidélité'); ?>" onclick="return addRulesProducts( true );" type="submit" value="<?php print _('Inclure dans le système de points de fidélité'); ?>" name="rwd-prd-include" class="button" />
							<input title="<?php print _('Exclure du système de points de fidélité'); ?>" onclick="return addRulesProducts( false );" type="submit" value="<?php print _('Exclure du système de points de fidélité'); ?>" name="rwd-prd-exclude" class="button" />
						</div>

					</fieldset>

				</td></tr>
                <tr><td colspan="2">
                    <fieldset>
                        <legend><?php print _('Règles supplémentaires'); ?></legend>
                        <input type="checkbox" name="include_pmt" id="include_pmt" value="1" <?php print $reward['exclu-promo'] ? '' : 'checked="checked"'; ?> />
                        <label for="include_pmt"><?php print _('Inclure les articles bénéficiant déjà d\'une promotion individuelle.'); ?></label>
                    </fieldset>
                </td></tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input onclick="saveRulesProducts();" type="button" value="<?php print _('Enregistrer'); ?>" name="save-products" />
					<input type="submit" value="<?php print _('Annuler'); ?>" name="cancel-products" />
				</td></tr>
			</tfoot>
		</table>
		
		<table id="tb-tabActions" class="tb_rewards checklist <?php print $tab=='actions' ? 'inline-block' : 'none'; ?>">
			<caption><?php print _('Gestion des points de fidélité'); ?></caption>
			<thead>
				<tr>
					<th id="actions"><?php print _('Action'); ?></th>
					<th id="desc"><?php print _('Description'); ?></th>
					<th id="rwa-params"><?php print _('Paramètres'); ?></th>
				</tr>
			</thead>
			<tbody><?php
				$rrwa = rwd_actions_get();
				if( $rrwa ){
					while( $rwa = ria_mysql_fetch_array($rrwa) ){
						print '	<tr>
									<td headers="actions">'.htmlspecialchars( $rwa['name'] ).'</td>
									<td headers="desc">'.nl2br( htmlspecialchars($rwa['desc']) ).'</td>
									<td headers="rwa-params">';
						if( $rwa['code']!='RWA_NTH_ORDER' ){
							print '		<span class="title-pts">'._('Points de fidélité').'</span>
										<div class="rwa-params">
											<div>
												<label for="rwa-usr-'.$rwa['id'].'">'._('Internaute').' : </label>
												<input class="radio" type="text" name="rwa-usr['.$rwa['id'].']" id="rwa-usr-'.$rwa['id'].'" value="" />
											</div>
											<div>
												<label for="rwa-sp-'.$rwa['id'].'">'._('Parrain').' : </label>
												<input class="radio" type="text" name="rwa-sp['.$rwa['id'].']" id="rwa-sp-'.$rwa['id'].'" value="" />
											</div>
										</div>';
						}
						print '		'.( rwd_actions_have_conditions($rwa['id']) ? '<a class="avanced" href="#'.$rwa['id'].'">'._('Paramètres avancés').'</a>' : '' );
						print '		</td>
								</tr>';
					}
				} ?>
			</tbody>
			<tfoot>
				<tr><td colspan="3">
					<input onclick="saveActions();" type="button" value="<?php print _('Enregistrer'); ?>" name="save-actions" />
				</td></tr>
			</tfoot>
		</table>
		
		<div id="msg-sponsor" class="notice none">
			<p><?php print _('Le parrain devra renseigner l\'adresse mail de son ou ses filleuls qui recevront un code promotion selon les paramêtres ci-dessous.'); ?></p>
			<p><?php print _('Lors que l\'un de ces codes est utilisé, le parrain peut :'); ?></p>
			<ul>
				<li><?php print _('Soit recevoir des points de fidélité'); ?></li>
				<li><?php print _('Soit recevoir une remise d\'une valeur déterminé par un pourcentage de la commande passée par son filleul'); ?></li>
			</ul>
		</div>

		<table id="tb-tabSponsors" class="tb_rewards checklist <?php print $tab=='sponsors' ? 'inline-block' : 'none'; ?>" >
			<caption><?php print _('Système de parrainage'); ?></caption>
			<tbody>
				<tr>
					<th colspan="2"><?php print _('Général'); ?></th>
				</tr>
				<tr>
					<td>
						<label for="active-sponsor-y"><span class="mandatory">*</span> <?php print _('Activer ce système :'); ?></label>
					</td>
					<td>
						<input type="radio" name="active-sponsor" id="active-sponsor-y" value="1" />
						<label for="active-sponsor-y"><?php print _('Oui'); ?></label>
						<input type="radio" name="active-sponsor" id="active-sponsor-n" value="0" />
						<label for="active-sponsor-n"><?php print _('Non'); ?></label>
					</td>
				</tr>
				<tr>
					<td>
						<label for="first_order"><?php print _('Première commande :'); ?></label>
					</td>
					<td>
						<input type="checkbox" name="first_order" id="first_order" value="1" />
						<label for="first_order"><?php print _('En activant cette option, le système de parrainage ne s\'appliquera que sur la première commande du filleul.'); ?></label>
					</td>
				</tr>
				<tr>
					<td>
						<label for="sponsor-limit"><?php print _('Nombre de parrainage(s) :'); ?></label>
					</td>
					<td>
						<input type="text" name="sponsor-limit" id="sponsor-limit" class="price" maxlength="8" value="" />
						<sub><?php print _('Nombre maximum de parrainage(s) donnant droit à des points fidélité, mettre zéro pour ne pas limiter leur nombre.'); ?></sub>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php print _('Pour le filleul'); ?></th>
				</tr>
				<tr>
					<td>
						<label for="discount"><span class="mandatory">*</span> <?php print _('Système :'); ?></label>
					</td>
					<td>
						<select name="filleul-system" id="filleul-system">
						 	<option value="0"><?php print _('Lien de parrainage'); ?></option>
                      		<option value="1"><?php print _('Réduction'); ?></option>
                        	<option value="2"><?php print _('Points'); ?></option>
                  		</select>
					</td>
				</tr>
				<tr class="tr-filleul-reduc">
					<td>
						<label for="discount"><span class="mandatory">*</span> <?php print _('Appliquer une réduction de :'); ?></label>
					</td>
					<td>
						<input type="text" name="discount" id="discount" class="price" maxlength="8" value="" />&nbsp;%
					</td>
				</tr>
				<tr class="tr-filleul-points">
					<td>
						<label for="points"><span class="mandatory">*</span> <?php print _('Offrir :'); ?></label>
					</td>
					<td>
						<input type="text" name="points" id="points" class="price" maxlength="8" value="" />&nbsp; <?php print _('point(s)'); ?>
						<sub><?php print _('Nombre de points de fidélité que reçoit le filleul lors de l\'utilisation du code promotion.'); ?></sub>
					</td>
				</tr>
				<tr  class="tr-filleul-day">
					<td>
						<label for="remise-day-valid"><span class="mandatory">*</span> <?php print _('Valable pendant :'); ?></label>
					</td>
					<td>
						<input type="text" name="remise-day-valid" id="remise-day-valid" class="price" maxlength="8" value="" />&nbsp; <?php print _('jours(s)'); ?>
						<sub><?php print _('Laissez vide ou mettre zéro pour aucune limite d’utilisation.'); ?></sub>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php print _('Pour le parrain'); ?></th>
				</tr>
				<tr>
					<td>
						<label for="sp-system"><span class="mandatory">*</span> <?php print _('Système :'); ?></label>
					</td>
					<td>
						<select name="sp-system" id="sp-system">
                        <option value="<?php print _RWD_SP_SYS_POINTS; ?>"><?php print _('Points de fidélité'); ?></option>
                        <option value="<?php print _RWD_SP_SYS_POURCENT; ?>"><?php print _('Pourcentage de la commande (code promotion)'); ?></option>
                    </select>
					</td>
				</tr>
				<tr class="sp-type-pourcent">
					<td>
						<label for="sp-ord-pourcent"><span class="mandatory">*</span> <?php print _('Pourcentage :'); ?></label>
					</td>
					<td>
						<input type="text" name="sp-ord-pourcent" id="sp-ord-pourcent" class="price" maxlength="8" value="" />&nbsp; %
						<sub><?php print _('Le montant de la réduction sera calculé selon ce pourcentage sur la base du montant de la commande du filleul.'); ?></sub>
					</td>
				</tr>
				<tr class="sp-type-pts">
					<td>
						<label for="sponsor-points"><span class="mandatory">*</span> <?php print _('Points de fidélité :'); ?></label>
					</td>
					<td>
						<input type="text" name="sponsor-points" id="sponsor-points" class="price" maxlength="8" value="" />&nbsp; <?php print _('points'); ?>
						<sub><?php print _('Nombre de points de fidélité que reçoit le parrain lors de l\'utilisation du code promotion.'); ?></sub>
					</td>
				</tr>
				<tr class="sp-type-pts">
					<td>
						<label for="cod-before-lost"><?php print _('Relance parrainage :'); ?></label>
					</td>
					<td>
						<input type="text" name="cod-before-lost" id="cod-before-lost" class="price" maxlength="8" value="0" />
						<sub><?php print _('Laissez vide ou mettre zéro pour aucune relance.'); ?></sub>
					</td>
				</tr>
				<tr class="">
					<td>
						<label for="sp-day-valid"><span class="mandatory">*</span> <?php print _('Valable pendant :'); ?></label>
					</td>
					<td>
						<input type="text" name="sp-day-valid" id="sp-day-valid" class="price" maxlength="8" value="0" />&nbsp; <?php print _('jours(s)'); ?>
						<sub><?php print _('Laissez vide ou mettre zéro pour aucune limite d’utilisation.'); ?></sub>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input onclick="saveSponsors();" type="button" value="<?php print _('Enregistrer'); ?>" name="save-sponsor" />
				</td></tr>
			</tfoot>
		</table>
		
	</div>	
	
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>

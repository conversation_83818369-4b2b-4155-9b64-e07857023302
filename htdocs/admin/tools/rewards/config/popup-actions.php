<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD_CONFIG');

	require_once('rewards.inc.php');
	
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _('Conditions sur une action').' - '._('Système de points de fidélité').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<form id="form-rewards" action="/admin/tools/rewards/popup-actions.php" method="get">
		<input type="hidden" name="rwa_id" id="rwa_id" value="<?php print $_GET['rwa']; ?>" />
		<input type="hidden" name="prf_id" id="prf_id" value="<?php print $_GET['prf']; ?>" />
		<input type="hidden" name="wst_id" id="wst_id" value="<?php print $_GET['wst']; ?>" />
		<?php
			if( !isset($_GET['rwa']) || !rwd_actions_exists($_GET['rwa']) ){
				print '<div class="error">' . _('Une erreur inattendue s\'est produite lors du chargement des paramètres avancés. <br />Veuillez réessayer ou prendre contact pour nous signaler le problème.') . '</div>';
			} else {
		?>
		<p><?php print _('Vous pouvez définir ici les conditions d\'obtention de points de fidélité pour l\'action suivante :'); ?> "<?php print rwd_actions_get_name( $_GET['rwa'] ); ?>".</p>
		<table class="tb_rewards checklist popup" style="display: inline-block;" id="tb-tabActions">
			<caption><?php print _('Conditions'); ?></caption>
			<col width="*" /><col width="475" />
			<thead>
				<tr>
					<th id="actions"><?php print _('Action'); ?></th>
					<th id="desc"><?php print _('Description'); ?></th>
				</tr>
			</thead>
			<tfoot>
				<tr><td colspan="2">
					<input type="button" name="save-actions" value="<?php print _('Enregistrer'); ?>" onclick="saveConditions();" />
					<input type="button" name="cancel-actions" id="cancel-actions" value="<?php print _('Annuler'); ?>" />
				</td></tr>
			</tfoot>
			<tbody><?php
				
				$rrwc = rwd_action_conditions_get( $_GET['rwa'] );
				if( $rrwc && ria_mysql_num_rows($rrwc) ){
					
					while( $rwc = ria_mysql_fetch_array($rrwc) ){
						if( $_GET['rwa']==3 && $rwc['code']=='RWC_NTH_ORDER_NB' ) continue;
						
						print '	<tr>';
						print '		<td headers="actions">';
						print '			<label for="config-'.$rwc['id'].( $rwc['type']==8 ?  '-y' : '' ).'">'.htmlspecialchars( $rwc['name'] ).'</label>';
						print '			<div class="clear"></div>';
						switch( $rwc['type'] ){
							case 8 : 
								print '	<input type="radio" value="1" id="config-'.$rwc['id'].'-y" name="config['.$rwc['id'].']" class="radio" />';
								print '	<label for="config-'.$rwc['id'].'-y">'._('Oui').'</label><br />';
								print '	<input type="radio" value="0" id="config-'.$rwc['id'].'-n" name="config['.$rwc['id'].']" class="radio" />';
								print '	<label for="config-'.$rwc['id'].'-n">'._('Non').'</label>';
								break;
							default :
								print '	<input class="radio" type="text" name="config['.$rwc['id'].']" id="config-'.$rwc['id'].'" value="" />';
								break;
						}
						print '		</td>';
						print '		<td>'.str_replace( "\r\n", "<br />", htmlspecialchars($rwc['desc']) ).'</td>';
						print '	</tr>';
					}
					
				}
				
			?></tbody>
		</table>
		
		<?php 
			if( in_array($_GET['rwa'], array(1, 2, 3)) ){ 
				$type = strtoupper( rwd_rewards_get_type_amount(0, $_GET['prf'], $_GET['wst']) );
				$title = $type=='TTC' ? _('Toutes Taxes Comprises') : _('Hors Taxes');
				
				print '	<div class="notice">';
				print str_replace(
					array('#param[titre]#', '#param[type]#'),
					array($title, $type),
					_('Les conditions sur le montant de la commande doivent être exprimées en <abbr title="#param[titre]#">#param[type]#</abbr> (hors frais de port).')
				);
				print '</div>';
			}

		if( $_GET['rwa']==3 ){ ?>
		<div id="landings">
			<p><?php print _('Cette action nécessite de définir différents paliers, pour chacun d\'entre eux vous aurez la possibilité de fixer le nombre de points de fidélité gagnés par l\'internaute et son parrain :'); ?></p>
			<table class="tb_rewards checklist popup" style="display: inline-block;" id="tb-landing">
				<caption><?php print _('Liste des paliers'); ?></caption>
				<col width="100" /><col width="250" /><col width="100" />
				<thead>
					<tr>
						<th id="nb-landing"><?php print _('Palier'); ?></th>
						<th id="points"><?php print _('Points de fidélité'); ?></th>
						<th id="del-landing"></th>
					</tr>
				</thead>
				<tfoot>
					<tr><td colspan="3">
						<input type="button" name="add-landing" value="<?php print _('Ajouter'); ?>" title="<?php print _('Ajouter un nouveau palier'); ?>" onclick="addLanding();" />
					</td></tr>
				</tfoot>
				<tbody><tr><td colspan="3"></td></tr></tbody>
			</table>
		</div>
		<?php }
		} ?>
	</form>
	<script><!--
		$(document).ready(function(){
			$('#cancel-actions').click(function(){
				parent.hidePopup();
			});
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
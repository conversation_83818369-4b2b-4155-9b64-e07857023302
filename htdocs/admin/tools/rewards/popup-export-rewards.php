<?php

	/**	\file popup-export-rewards.php
	 *	Ce fichier est utilisé pour l'exportation des informations relatives au système de fidélité.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD');

	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _('Export Fidélité'));
	require_once('admin/skin/header.inc.php');
?>

	<div id="export" class="rewards">
		<div class="loading-rewards" style="display: none">
		
		</div>
		<form action="?cat=0&amp;brd=0" method="post">
			<?php if(isset($_GET['usr']) && is_numeric($_GET['usr'])){ ?>
					<input type="hidden" name="usr" value="<?php echo $_GET['usr'] ?>">
			<?php } ?>
			<h2>
				<span style="float: left;"><?php print _('Gestion de l\'export'); ?></span>
				<div class="clear"></div>
			</h2>
			
			<div class="date">
				<label for="date_start">
					<?php print _('Date de début de la période :'); ?> 
				</label>
				<input id="date_start" type="text" class="datepicker date" name="date_start" autocomplete="off" style="width: 115px;" />
			</div>
			<div class="date">
				<label for="date_end">
					<?php print _('Date de fin de la période :'); ?> 
				</label>
				<input id="date_end" type="text" class="datepicker date" name="date_end" autocomplete="off" style="width: 115px;" />
			</div>
			<div class="export-action">
				<input class="btn-action" name="export" value="<?php print _('Télécharger'); ?>" type="submit" />
			</div>
			</form>
	</div>
	<script>
	$(document).ready(function(){
	
		// Parcours tous les champs de type date pour activer le datepicker
		$('input.datepicker').each(function(){
			var temp = this;
			
			// Implémente le sélecteur de date sur chacun d'entre eux.
			$(this).DatePicker({
				format:'d/m/Y',
				date: $(this).val(),
				current: $(this).val(),
				starts: 1,
				onChange: function(formated, dates){
					var date = $(temp).val();
					if(dates != 'Invalid Date'){
						$(temp).val(formated);
						$(temp).DatePickerHide();
					}
					if( $('#date_end').length > 0 && $(temp).attr('name') == 'date_start' ){
						$('#date_end').val( $('#date_start').val() );
					}
				}
			});
			
		});	

		$('.btn-action').click(function(e){
			$('.loading-rewards').show();

			var date_start = $("input[name='date_start']").val();
			var date_end = $("input[name='date_end']").val();
			var usr = $("input[name='usr']").val();
			var url = '/admin/tools/rewards/export-rewards.php?export=1';

			if(date_start && date_start != "" ){
				url += "&date_start="+date_start;
			}

			if(date_end && date_end != "" ){
				url += "&date_end="+date_end;
			}

			if(usr && usr != "" ){
				url += "&usr="+usr;
			}
			
			$('body').append('<div class="reward popup_ria_back_load"></div>');
			$('body').append('<div class="reward popup_ria_back_notice notice"><?php print _('Votre export est en cours de préparation, veuillez patienter...'); ?></div>');
			
			$.ajax({
				type 	: 'get',
				url 	: url,
				data 	: '',
				async 	: true,
				success : function(urlDownload){
					$('.popup_ria_back_notice').html('<?php print _('Votre export est prêt à être téléchargé :'); ?> <a href="#" class="download-file"><?php print _('Télécharger'); ?></a>');
					$('.download-file').click(function(e){

						var url = parent.window.location.href;
						parent.window.location.href = url + (url.match(/\?/) ? '&' : '?') + 'downloadexport=1';
						parent.hidePopup();

						return false;
					});
				}
			});
			return false;
		});

	});
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
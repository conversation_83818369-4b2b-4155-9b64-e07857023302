<?php 

	/** \file export-rewards.php
	 * 	Ce fichier génère un export au format CSV de l'historique des points de fidélité. Il accepte les paramètres suivants :
	 * 	- $usr : facultatif, identifiant d'un compte utilisateur sur lequel filtrer le résultat
	 * 	- $date_start : facultatif, date de début sur laquelle filtrer le résultat
	 * 	- $date_stop : facultatif, date de fin sur laquelle filtrer le résultat
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD');

	if( isset($_GET['export']) ){

		$usr = 0;
		$date_start = false;
		$date_end = false;

		if( isset($_GET['usr'])&& $_GET['usr'] != ""){
			$usr = $_GET['usr'];
		}
		if( isset($_GET['date_start'])&& $_GET['date_start'] != ""){
			$date_start = $_GET['date_start'];
		}
		if( isset($_GET['date_end'])&& $_GET['date_end'] != ""){
			$date_end = $_GET['date_end'];
		}

		rwd_export_stats( $usr, $date_start, $date_end);
	}

<?php

	/**	\file stats.php
	 *	Ce fichier affiche des statistiques sur les parrainages et points de fidélité,
	 *	ainsi que l'historique des attributions de points de fidélité.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD_STATS');
	
	require_once('stats.inc.php');

	// Gère le bouton exporter
	if (isset($_GET['downloadexport'])) {
		// Contrôle que le fichier est bien disponible
		if (file_exists($config['doc_dir'].'/export-rewards-'.$_SESSION['usr_id'].'.csv')) {
			header('Content-disposition: attachment; filename="export-produits.csv"');
			header('Content-type: application/octetstream; charset=utf-8');
			header('Pragma: no-cache');
			header('Expires: 0');

			echo "\xEF\xBB\xBF"; // BOM UTF-8
			readfile ($config['doc_dir'].'/export-rewards-'.$_SESSION['usr_id'].'.csv'); 
			exit;
		}else{
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}
	
	$nb_res_by_page = 50;
	
	$nb_result = stats_rewards_get_count();
	
	$page  = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page']>0 ? $_GET['page'] : 1;
	$pages = ceil( $nb_result / $nb_res_by_page );

	$rewards = stats_rewards_get( 0, 0, 0, 0, false, false, false, 0, false, 0, false, '', false, false, 0, ( $page-1 )*50, $nb_res_by_page );
	
	$pmin = $page - 5;
	$pmin = $pmin<1 ? 1 : $pmin;
	$pmax = $pmin + 9;
	$pmax = $pmax>$pages ? $pages : $pmax;

	define('ADMIN_PAGE_TITLE', _('Statistiques').' - '._('Système de points de fidélité').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Statistiques'); ?></h2>
	<?php
		print '
			<div class="stats-menu">
				'.view_rewards_get_global_stats().'
			</div>
		';
	?>
	<div>
		<input name="export" id="export-rewards" value="<?php print _('Exporter les statistiques'); ?>" title="<?php print _('Exporter les informations'); ?>" type="submit" <?php print ( !$rewards || !ria_mysql_num_rows($rewards) ? 'disabled="disabled"' : '' ); ?>>
    </div>
	<table id="stats-rewards" class="checklist large">
		<caption><?php print _('Historique des attributions de points de fidélité'); ?></caption>
		<thead class="thead-none">
			<tr>
				<th id="hdate"><?php print _('Date'); ?></th>
				<th id="husr"><?php print _('Client'); ?></th>
				<th id="hrwa"><?php print _('Action réalisée'); ?></th>
				<th id="hpts" class="align-right"><?php print _('Points gagnés / perdus'); ?></th>
				<th id="hconvert" class="align-right"><?php print _('Valeur'); ?></th>
				<th id="hlimit" class="align-center"><?php print _('Date limite d\'utilisation'); ?></th>
			</tr>
		</thead>
		<tbody><?php
			if( !$rewards || !ria_mysql_num_rows($rewards) ){
				print '	
					<tr>
						<td colspan="6">'._('Aucun historique n\'est disponible pour le moment.').'</td>
					</tr>
				';
			} else {
								
				$count = 0;
				$ar_users = array();
				while( $reward = ria_mysql_fetch_array($rewards) ){
					if( $count++>=$nb_res_by_page ){
						break;
					}
					
					$amount = 0;
					if( trim($reward['convert'])!='' ){
						$ar_convert = preg_split('/\//', $reward['convert']);
						$convert = $ar_convert[0] / $ar_convert[1];
						$amount = $reward['pts']*$convert;
					}
					
					// Récupère le compte client
					if( !isset($ar_users[$reward['usr']]) ){
						$rusr = gu_users_get( $reward['usr'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$ar_users[ $reward['usr'] ] = ria_mysql_fetch_array( $rusr );
						}
					}
					
					// Gestion des dates
					$date = ria_date_format( $reward['date_en'], false, true );
					$date_limit = trim($reward['date_limit_en']) != '' ? ria_date_format($reward['date_limit_en'], false, true) : '';
					
					print '	
						<tr class="'.( $amount<0 || $reward['pts']<0 ? 'negative' : 'positive' ).'">
							<td headers="hdate" data-label="'._('Date :').' ">'.$date.'</td>
							<td headers="husr" data-label="'._('Client :').' ">
					';
					
					if( isset($ar_users[ $reward['usr'] ]) ){
						print '
								'.view_usr_is_sync($ar_users[ $reward['usr'] ]).'
								<a target="_blank" href="/admin/customers/edit.php?usr='.$reward['usr'].'">'.
								htmlspecialchars( ( $ar_users[ $reward['usr'] ]['ref'] ? $ar_users[ $reward['usr'] ]['ref'].' - ' : '' ).$reward['lastname'].' '.$reward['firstname'].' '.$reward['society'])
								.'</a>
						';
					} else {
						print '
								'.htmlspecialchars( ( $ar_users[ $reward['usr'] ]['ref'] ? $ar_users[ $reward['usr'] ]['ref'].' - ' : '' ).$reward['lastname'].' '.$reward['firstname'].' '.$reward['society']).'
						';
					}
					
					if( $reward['godchild'] ){
						print '
								<br />(<span class="bold">'._('Parrainage de').' </span>
						';
						
						if( !isset($ar_users[$reward['godchild']]) ){
							$rusr = gu_users_get( $reward['godchild'] );
							if( $rusr && ria_mysql_num_rows($rusr) )
								$ar_users[ $reward['godchild'] ] = ria_mysql_fetch_array( $rusr );
						}
						
						if( isset($ar_users[$reward['godchild']]) ){
							$godchild = $ar_users[ $reward['godchild'] ];
							
							print '
								'.view_usr_is_sync($godchild).'
								<a target="_blank" href="/admin/customers/edit.php?usr='.$godchild['id'].'">
									'.htmlspecialchars( $godchild['title_name'].' '.$godchild['adr_lastname'].' '.$godchild['adr_firstname'] ).'
								</a>
							';
						}
							
						print '
							)
						';
					}
					
					print '		
							</td>
							<td headers="hrwa" data-label="'._('Action réalisée :').' ">
								'.htmlspecialchars($reward['rwa']>0 ? ($amount<0 ? $reward['contrary'] : $reward['name']) : $reward['name'] ).'
					';
					
					switch( $reward['cls_id'] ){
						case CLS_ORDER : 
							$rord = ord_orders_get( 0, $reward['obj_id_0'] );
							if( $rord && ria_mysql_num_rows($rord) ){
								$ord = ria_mysql_fetch_array( $rord );
								print '
									<br />'._('Commande').' : '.view_ord_is_sync( $ord ).' 
									<a href="/admin/orders/order.php?ord='.$ord['id'].'" title="'._('Afficher la fiche de cette commande').'">
										'.ord_orders_name( '', $ord['piece'], $ord['id'] ).'
									</a>
								';
							}
							break;
						case CLS_PMT_CODE :
							$rcod = pmt_codes_get( $reward['obj_id_0'] );
							if( $rcod && ria_mysql_num_rows($rcod) ){
								$cod = ria_mysql_fetch_array( $rcod );
								print '
									<br />'._('Promotion').' : 
									<a href="/admin/promotions/specials/edit.php?id='.$cod['id'].'&amp;type='.$cod['type'].'" title="'._('Afficher les informations sur cette promotion').'">
										'.$cod['code'].'
									</a>
								';
								
								if( trim($cod['date_stop']) ){
									print '
										<br />('._('valable jusqu\'au').' '.$cod['date_stop'].' '._('inclus').')
									';
								}
								
								print '
								';
							}
							break;
						case CLS_INVOICE :
							$rinv = ord_invoices_get( $reward['obj_id_0'] );
							if( $rinv && ria_mysql_num_rows($rinv) ){
								$inv = ria_mysql_fetch_array( $rinv );
								$name = trim($inv['ref'])!='' ? $inv['ref'] : $inv['id'];
								
								print '
									('._('Facture').' n°'.$name.')
								';
								
								$riord = ord_inv_orders_get( $inv['id'] );
								if( $riord && ria_mysql_num_rows($riord) ){
									$multi = ria_mysql_num_rows($riord)>1 ? true : false;
									
									if( $multi ){
										print '
											<br />'._('Liée aux commandes').' :
										';
									}else{
										print '
											<br />'._('Liée à la commande').' :
										';
									}
									
									$first = true;
									while( $iord = ria_mysql_fetch_array($riord) ){
										$ord = ria_mysql_fetch_array( ord_orders_get(0, $iord['id']) );
										
										print ( !$first ? ', ' : '' ).view_ord_is_sync( $ord ).'
											<a href="/admin/orders/order.php?ord='.$ord['id'].'" title="'._('Afficher la fiche de cette commande').'">
												'.ord_orders_name( '', $ord['piece'], $ord['id'] ).'
											</a>
										';
										
										$first = false;
									}
								}
							}
					}
					
					print '
							</td>
							<td headers="hpts" class="align-right" data-label="'._('Points gagnés / perdus :').' ">'.ria_number_format($reward['pts']).'</td>
							<td headers="hconvert" class="align-right" data-label="'._('Valeur :').' ">'.ria_number_format($amount, NumberFormatter::CURRENCY, 2).'</td>
							<td headers="hlimit" class="align-center" data-label="'._('Date limite d\'utilisation :').' ">'.$date_limit.'</td>
						</tr>
					';
				}
				
			}
		?>			
		</tbody>	
		<?php if( $pages>1 ){ ?>
		<tfoot>
			<tr>
				<td id="pagination" colspan="6">
					<?php 
						if( $pages>1 ){
							if( $page>1 )
								print '<a href="stats.php?page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page )
									print '<b>'.$page.'</b>';
								else
									print '<a href="stats.php?page='.$i.'">'.$i.'</a>';
								if( $i<$pmax )
									print ' | ';
							}
							if( $page<$pages )
								print ' | <a href="stats.php?page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
						}
					?>
				</td>
			</tr>
		</tfoot>
		<?php } ?>
	</table>
	
	<span class="rwd-legend">
		<span class="color active"></span>
		<?php print _('Points de fidélité actifs'); ?>
	</span>
	<span class="rwd-legend">
		<span class="color unactive"></span>
		<?php print _('Points de fidélité utilisés'); ?>
	</span>
	<div class="clear"></div>
<?php
	require_once('admin/skin/footer.inc.php');
?>
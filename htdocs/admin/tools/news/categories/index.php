<?php

/** \file index.php
 * Cette page affiche la liste des catégories d'actualités.
 */

require_once('news.categories.inc.php');

define('ADMIN_PAGE_TITLE', _('Catégories d\'actualités').' - '._('Outils'));

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_CATEG');

// Bouton Ajouter
if( isset($_POST['add']) && isset($_POST['name']) ){
	header('Location: edit.php?name='.urlencode(trim($_POST['name'])));
	exit;
}

// Bouton Supprimer
if( isset($_POST['del']) && isset($_POST['cat']) && is_array($_POST['cat']) ){
	foreach( $_POST['cat'] as $category_id ){
		news_categories_del($category_id);
	}

	header('Location: index.php');
	exit;
}

require_once('admin/skin/header.inc.php');

// Charge la liste des catégories d'actualités.
$r_categories = news_categories_get();

$categories_count = ria_mysql_num_rows($r_categories);

?>

<h2><?php print _('Catégories d\'actualités'); ?> (<?php print ria_number_format($categories_count); ?>)</h2>
<form action="index.php" method="post">
	<table class="checklist" id="table-categories-actualites">
		<thead>
			<tr>
				<th id="cat-sel">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th id="cat-name"><?php print _('Titre'); ?></th>
				<th id="news-count" class="align-right"><?php print _('Actualités'); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td colspan="3">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_CATEG_DEL') && $categories_count ){ ?>
						<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return newsConfirmDelList()">
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_CATEG_ADD') ){ ?>
						<div class="float-right">
							<label for="name"><?php print _('Ajouter une catégorie d\'actualités :'); ?></label>
							<input type="text" id="name" name="name" maxlength="75">
							<input type="submit" name="add" value="<?php print _('Ajouter'); ?>">
						</div>
					<?php } ?>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<?php if( $r_categories && ria_mysql_num_rows($r_categories) ){ ?>
				<?php while( $category = ria_mysql_fetch_array($r_categories) ){ ?>
					<tr>
						<td headers="cat-sel">
							<input type="checkbox" class="checkbox" name="cat[]" value="<?php print $category['id']; ?>">
						</td>
						<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_CATEG_EDIT') ){ ?>
							<td headers="cat-name">
								<a href="edit.php?cat=<?php print $category['id']; ?>"><?php print htmlspecialchars($category['name']); ?></a>
							</td>
						<?php }else{ ?>
							<td headers="cat-name"><?php print htmlspecialchars($category['name']); ?></td>
						<?php } ?>
						<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS') ){ ?>
							<td headers="news-count" class="align-right">
								<a href="../index.php?cat=<?php print $category['id']; ?>"><?php print ria_number_format($category['news']); ?></a>
							</td>
						<?php }else{ ?>
							<td headers="news-count" class="align-right"><?php print ria_number_format($category['news']); ?></td>
						<?php } ?>
					</tr>
				<?php } ?>
			<?php }else{ ?>
				<tr>
					<td colspan="3"><?php print _('Aucune catégorie d\'actualités'); ?></td>
				</tr>
			<?php } ?>
		</tbody>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');
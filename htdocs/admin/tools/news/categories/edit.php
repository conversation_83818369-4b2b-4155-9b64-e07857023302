<?php

	/**	\file edit.php
	 *	Cette page affiche et permet la modification d'une catégorie d'actualités
	 */

	require_once('news.categories.inc.php');

	if( isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_CATEG_EDIT');
	}else{
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_CATEG_ADD');
	}

	unset($error);

	// Vérifie l'identifiant passé en argument
	if( isset($_GET['cat']) && $_GET['cat']!=0 && !news_categories_exists($_GET['cat']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_GET['cat']) ){
		if( !news_categories_del($_GET['cat']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie d'actualités.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['name']) || !trim($_POST['name']) ){
			$error = _("Veuillez indiquer le nom de la catégorie d'actualités.");
		}else{
			if( !isset($error) ){
				if( isset($_GET['cat']) && $_GET['cat']==0 ){
					if( !news_categories_add( $_POST['name'], $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie d'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
					if( !news_categories_update( $_GET['cat'], $_POST['name'], $_POST['desc'] ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie d'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
				  $values = array(
					_FLD_NEWS_CAT_NAME=>$_POST['name'],
					_FLD_NEWS_CAT_DESC=>$_POST['desc']
				  );
				  
				  if( !fld_translates_add($_GET['cat'], $_GET['lng'], $values) )
					$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}

			if( !isset($error) ){
				header('Location: index.php');
				exit;
			}
		}
	}
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Charge la catégorie d'actualités
	$cat = array('id'=>0,'name'=>'','desc'=>'');
	if( isset($_GET['name']) ){
		$cat['name'] = ucfirst(trim($_GET['name']));
	}
	if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ){
		$cat = ria_mysql_fetch_array(news_categories_get($_GET['cat']));
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_cat = fld_translates_get( CLS_NEWS_CAT, $cat['id'], $lng, $cat, array(_FLD_NEWS_CAT_NAME=>'name', _FLD_NEWS_CAT_DESC=>'desc' ), true );
			$cat['name'] = $tsk_cat['name'];
			$cat['desc'] = $tsk_cat['desc'];
		}
	}
	
	// Défini le titre de la page
	if( trim($cat['name']) ){
		$page_title = _('Catégorie d\'actualités ').$cat['name'];
	}else{
		$page_title = _('Créer une catégorie d\'actualités');
	}
	
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Catégorie d\'actualités'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>

	<?php
		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
    	// Affiche le menu de langue
		if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 )
			print view_translate_menu( 'edit.php?cat='.$cat['id'], $lng );
	?>

	<form action="edit.php?cat=<?php print $cat['id']; ?>&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return newsCategoriesValidForm(this)">
	<table>
	<tfoot>
		<tr><td colspan="2">
			<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
			<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return newsCategoriesCancelEdit(this.form)" />
			<?php 
				if( $cat['id']>0 && gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_CATEG_DEL') ){
					print '
						<input type="submit" name="del" value="'._('Supprimer').'" onclick="return newsCategoriesConfirmDel()" />
					';
				}
			?>
		</td></tr>
	</tfoot>
	<tbody>
		<tr>
			<td><label for="name"><span class="mandatory">*</span> <?php print _('Titre :'); ?></label></td>
			<td><input type="text" name="name" value="<?php print htmlspecialchars($cat['name']); ?>" maxlength="125" /></td>
		</tr>
		<tr>
			<td><label for="desc"><?php print _('Description :'); ?></label></td>
			<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($cat['desc']); ?></textarea></td>
		</tr>
	</tbody>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
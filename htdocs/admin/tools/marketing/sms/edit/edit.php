<?php

// Vérifie que l'utilisateur en cours à bien le droit d'accéder à cette page
if( $_GET['cpg'] != 0 ){
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS_EDIT');
}elseif( $_GET['cpg'] == 0 ){
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS_ADD');
}

if( !isset( $config['marketing_is_active'] ) || !$config['marketing_is_active'] ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

if( !isset($_GET['cpg']) ){
	header('Location: /admin/tools/marketing/cpg/index.php');
	exit;
}

$tab = 'general';

$allowed_tabs = array(
	'general',
    'client',
	'stats'
);

if( isset($_GET['tab']) ){
	if( in_array($_GET['tab'], $allowed_tabs) ){
		$tab = $_GET['tab'];
	}
}

if( isset($_POST['cancel']) ){
	header('Location: /admin/tools/marketing/cpg/index.php');
	exit;
}

require_once( 'Marketing/models/Campaigns.inc.php' );
require_once( 'Marketing/TriggersManager.inc.php' );
require_once( 'Marketing/Channels.inc.php' );
require_once( 'Marketing/StatCampaign.inc.php' );
require_once( 'Marketing/SMS_Partners.inc.php' );

$Data = array(
	'title' => '',
	'desc' => '',
	'type' => '',
    'message' => '',
	'start-date' => '',
	'end-date' => '',
	'start-hour' => '',
	'end-hour' => '',
	'period' => false,
	'period_info' => false,
	'period_info_min' => false,
    'channel' => 0,
    'is_marketing' => true
);

// Ajout d'une campagne
$errors = array();
if( isset($_GET['cpg']) && isset($_POST['save']) ){

    // Titre de la campagne
    if( trim($_POST['title']) == '' ){
        $errors[] = _('Il manque le titre de la campagne.');
    }

	// message de la campagne
	if( !isset($_POST['message']) || trim($_POST['message']) == '' ){
		$errors[] = _('Il manque un message à envoyer.');
	}

	if( !isset($_POST['is_marketing']) ){
	    $Data['is_marketing'] = true;
	}else{
		$Data['is_marketing'] = false;
	}
	// Date de début
    $date_start = date('Y-m-d H:i:s');

	//date de fin
    $date_end = '2138-12-31 23:00:00';

	$period = false;
	$period_info = false;

    if( isset($_POST['period_type']) && $_POST['period_type'] == 'auto' ){
		if( isdate($_POST['period']) && is_numeric($_POST['period_info']) && is_numeric($_POST['period_info_min']) ){
		    $period = dateparse($_POST['period']);
			$period_info = $_POST['period_info'].':'.$_POST['period_info_min'];
			$date = new DateTime($period.' '.$period_info);
			$now = new DateTime('now');
			if( $date < $now ){
				$errors[] = _('La date ou l\'horaire saisie sont incorrectes.');
			}else{
                $diff = $date->diff($now);
				$hours = $diff->h + $diff->days*24;
                if( $hours<=0 ){
                    $errors[] = _('Il faut au moins une heure pour le traitement de votre campagne.');
				}
			}
		}
    }

	// si pas d'erreur on :
	//          - ajoute la campagne si cpg == 0
	//          - on met à jour la campagne si cpg != 0
	if( empty($errors ) ){
		if( $_GET['cpg'] == 0 ){
			$id = CampaignsManager::addCampaigns( $_POST['title'], $_POST['desc'], 'DIFF', $date_start, $date_end, $period, $period_info );
			if( $id ){
				$ch_id = Channels::addChannels( $config['wst_id'], 'SMS', $id, false, false, _SMS_PARTNER_OVH, false, $Data['is_marketing']);
				Channels::addChannelContent($ch_id, $_POST['message']);
				header( 'location: /admin/tools/marketing/sms/edit/edit.php?cpg='.$id );
			    exit;
            }else{
			    $errors[] = _('Une erreur est survenue lors de la création de votre campagne.');
            }
		}else{
			$rCamp = CampaignsManager::getCampaigns( $_GET['cpg'] );
			$camp = ria_mysql_fetch_assoc($rCamp);

			$ovh_update = false;
			if( $period != $camp['period'] || $period_info != $camp['period_info'] ){
				$ovh_update = true;
			}
			if( !CampaignsManager::updateCampaigns( $_GET['cpg'], $_POST['title'], $_POST['desc'], $camp['type'], $date_start, $date_end, $period, $period_info ) ){
				$errors[] = _('Une erreur est survenue lors de la mise a jours de votre campagne.');
			}else{
                $rChannel = Channels::getChannels(0, $_GET['cpg'], 0, 'SMS', _SMS_PARTNER_OVH);

				if( $rChannel ){
					$channel = ria_mysql_fetch_assoc($rChannel);
                    if( $channel['is_marketing'] != ($Data['is_marketing']?1:0)){
						Channels::updateChannels($channel['id'], $config['wst_id'], 'SMS',false, false, 0, 0, false, $Data['is_marketing']);
                    }
					if( $channel['content'] != $_POST['message'] ){
						Channels::addChannelContent($channel['id'], $_POST['message']);
						$ovh_update = true;
					}

					if( $ovh_update ){
						$oCamp = new Campaigns($camp);
						$oCamp->delDifferedSMS();
					}
				}
			}
		}
	}
}
$Executed = false;
if( !empty($errors ) && isset($_POST['title']) ){
    $Data = $_POST;
    if( !isset($_POST['is_marketing']) ){
        $Data['is_marketing'] = true;
	}else{
		$Data['is_marketing'] = false;
	}
}
elseif( $_GET['cpg'] != 0 ){
    $rCampaign = CampaignsManager::getCampaigns($_GET['cpg']);
    if( $rCampaign ){
        $Data = ria_mysql_fetch_assoc($rCampaign);
		try{
			$campaign = new Campaigns( $Data );
			$campaign->setCampaignUsers();
			$campaign->getUsersMobile();

			if( trim( $Data['date_executed'] ) != '' ){
				$dateExecuted = new DateTime($Data['date_executed']);
				$now = new DateTime();
				if( $dateExecuted < $now ){
				    $Executed = true;
                }
			}
		}catch(Exception $e){
			error_log($e);
		}
        if( trim($Data['period']) != '' ){
			$date = new DateTime($Data['period'].' '.$Data['period_info']);
			$Data['period'] = $date->format('d/m/Y');
			$Data['period_info'] = $date->format('H');
			$Data['period_info_min'] = $date->format('i');
		}else{
			$Data['period_info_min'] = '';
        }

		$rChannel = Channels::getChannels(0, $_GET['cpg'], 0, 'SMS', _SMS_PARTNER_OVH);
        if( $rChannel){
            $channel = ria_mysql_fetch_assoc($rChannel);
            $Data['message'] = $channel['content'];
            $Data['channel'] = $channel['id'];
			$Data['is_marketing'] = $channel['is_marketing'];
        }

    }else{
        $errors[] = _('La campagne n\'existe pas');
    }
}
//suppression
if( isset( $_POST['del'] ) ){
	$rCamp = CampaignsManager::getCampaigns( $_GET['cpg'] );
	$camp = ria_mysql_fetch_assoc($rCamp);
	$error = false;
	if( !CampaignsManager::delCampaigns( $_GET['cpg'] ) ){
		$error = true;
	}

	if( !Channels::delAllChannels( $_GET['cpg'] ) ){
		$error = true;
	}
	$oCamp = new Campaigns($camp);
	$oCamp->delDifferedSMS();
	
	if( !$error ){
		header( 'location: /admin/tools/marketing/sms/index.php?success=del' );
		exit;
	}
}

// Titre de la page
define( 'ADMIN_PAGE_TITLE', $Data['title'].' - '._('Campagnes SMS').' - '._('Campagnes Marketing') );
require_once( 'admin/skin/header.inc.php' );

?>
<h2><?php echo _('Campagne').' '.htmlspecialchars( ucfirst($Data['title']) ); ?></h2>
<?php if( !empty($errors) ){ ?>
    <div class="error">
        <?php foreach( $errors as $error ){ ?>
            <p><?php echo $error ?></p>
        <?php } ?>
    </div>
<?php }
if( trim($Data['period']) == ''  && $_GET['cpg'] != 0 && !$Executed && isset($campaign)){ ?>
    <div class="notice manuel-sending">
        <p><?php echo _("Votre campagne est en envoi manuel, vous pouvez l'exécuter en cliquant sur le bouton \"Envoyer\". Attention, l'envoi des SMS est irréversible et dois être réalisé entre 8h et 20h."); ?> </p>
    </div>
<?php }elseif($Executed){ ?>
    <div class="notice">
        <p><?php echo _("La campagne a déjà été exécutée, elle est donc non modifiable"); ?></p>
    </div>
    <?php } ?>
    <div class="notif">

    </div>
<div class="marketing">

    <ul class="tabstrip">
        <li><input onclick="return window.location.href='edit.php?tab=general&cpg=<?php echo $_GET['cpg'] ?>'" type="submit" name="tabGeneral" value="Général" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
        <?php if( isset($campaign) ){ ?>
        <li><input onclick="return window.location.href='edit.php?tab=client&cpg=<?php echo $_GET['cpg'] ?>'" type="submit" name="tabPrices" value="Clients" <?php if( $tab=='client' ) print 'class="selected"'; ?> /></li>
        <li><input onclick="return window.location.href='edit.php?tab=stats&cpg=<?php echo $_GET['cpg'] ?>'" type="submit" name="tabPrices" value="Statistiques" <?php if( $tab=='stats' ) print 'class="selected"'; ?> /></li>
        <?php } ?>
    </ul>
    <div id="tabpanel">
	<?php
		include_once(dirname(__FILE__).'/edit-'.$tab.'.php');
	?>
    </div>

</div>
<?php require_once( 'admin/skin/footer.inc.php' ); ?>
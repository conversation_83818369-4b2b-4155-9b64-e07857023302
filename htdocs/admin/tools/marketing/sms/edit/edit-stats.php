<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

	if( !isset($_GET['tab']) || !isset($_GET['cpg']) || $_GET['tab'] != 'stats' || $_GET['cpg'] == 0 || !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
		include_once(dirname(__FILE__).'/edit-general.php');
		exit;
	}
    require_once( 'Marketing/models/Campaigns.inc.php' );
    require_once( 'Marketing/StatCampaign.inc.php' );

	$oStats = new StatCampaign();

	$oStats->setCampaign($Data);

	$rStats = $oStats->getStats();
	$nb_result = $rStats ? ria_mysql_num_rows( $rStats ) : 0;
?>
<form action="edit.php?cpg=<?php echo $_GET['cpg'] ?>" method="post">
	<table class="checklist" id="table-stats-details-messages">
		<caption><?php echo _("Détails des messages"); ?> (<?php print $nb_result.' message'.( $nb_result > 1 ? 's' : '' ); ?>)</caption>
		<thead>
		<tr>
            <th id="hphone"><?php echo _("Numéro mobile"); ?></th>
            <th id="husr"><?php echo _("Client"); ?></th>
            <th id="hdate"><?php echo _("Date d'envoi"); ?></th>
        </tr>
		</thead>
		<tbody><?php
			if( !$rStats || !ria_mysql_num_rows($rStats) ){
				print '	
					<tr>
						<td colspan="3">' . _("Aucun historique n'est disponible pour le moment.") . '</td>
					</tr>
				';
			} else {
				while( $stat = ria_mysql_fetch_assoc($rStats) ){
				    // Gestion des dates
					$date = new DateTime($stat['date_sent']);
					if( $stat['usr_id'] == 0 ){
						$html_user = '-';
					}else{
						$r_user = gu_users_get( $stat['usr_id'] );

						// Utilisateur
						$html_user = 'Utilisateur supprimé';
						if( $r_user && ria_mysql_num_rows($r_user) ){
							$user = ria_mysql_fetch_assoc( $r_user );

							$html_user = '
								'.view_usr_is_sync( $user ).'
								<a target="_blank" href="/admin/customers/edit.php?usr='.$user['id'].'">'.htmlspecialchars($user['title_name'].' '.$user['adr_lastname'].' '.$user['adr_firstname']).'</a>
						';
						}
					}

					if( trim($stat['mobile']) == '' ){
					    $mobile_html = '-';
					}else{
						$mobile_html = htmlspecialchars($stat['mobile']);
					}

					print '	
								<tr>
									<td headers="hmobile" class="align-center">'.$mobile_html.'</td>
									<td headers="husr" class="align-center">'.$html_user.'</td>
									<td headers="hdate" class="align-center">'.$date->format('d/m/Y H:i:s').'</td>
								</tr>
							';
				}
			}
		?></tbody>
	</table>
</form>
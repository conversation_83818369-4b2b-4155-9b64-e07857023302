<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

if(!isset($config['marketing_is_active']) || !$config['marketing_is_active'] || $_GET['cpg'] == 0){
	include_once(dirname(__FILE__).'/edit-general.php');
	exit;
}
?>

<form action="" method="post">
	<input type="hidden" id="cpg_id" value="<?php echo $_GET['cpg']?>">
	<table id="table-client-regles">
		<caption><?php echo _("Règles d'inclusion / d'exclusion"); ?></caption>
		<tbody>
            <tr>
                <td colspan="2">
                    <div class="notice">
                       <?php echo _("Nombre de SMS prévu d'envoi :"); ?> <span class="customers-count-pmt"></span>
                    </div>
                </td>
            </tr>
			<tr>
				<td colspan="2">
					<div class="add-cpg-cdt">
						<fieldset class="sms-add-rule-user">
							<legend><?php echo _("Ajouter des règles"); ?></legend>
							<input type="hidden" id="value_usr" value="">
                            <div>
								<?php
								$rCpg = CampaignsManager::getCampaigns();

								if( $rCpg && ria_mysql_num_rows($rCpg) > 1 ){
									?>
                                    <div class="cpg-rule copy-cpg">
                                        <input disabled="disabled" type="radio" id="cpg-add-rule-copy" value="copy" name="cpg-add-rule" class="radio" />
                                        <label class="rwd-add-rule-label" for="cpg-add-rule-copy"><?php echo _("Copier la campagne :"); ?></label>
                                        <select name="cpg-copy-name" id="value_copy" class="cpg-input" data-rule="copy">
                                            <option value="-1"></option>
											<?php
											while( $s = ria_mysql_fetch_assoc($rCpg) ){
												if( $s['id'] == $_GET['cpg'] ){
													continue;
												}
												print '<option value="'.$s['id'].'">'.htmlspecialchars( $s['title'] ).'</option>';
											}
											?>
                                        </select>
                                        <button name="cpg-usr-select" id="cpg-usr-copy" class="button"><?php echo _("Copier"); ?></button>
                                    </div>
								<?php } ?>

                                <div class="cpg-rule">
                                    <input disabled="disabled" type="radio" id="cpg-add-rule-seg" value="seg" name="cpg-add-rule" class="radio" />
                                    <label class="rwd-add-rule-label" for="cpg-add-rule-seg"><?php echo _("Segment de clients :"); ?></label>
                                    <select name="cpg-seg-name" id="value_seg" class="cpg-input" data-rule="seg">
                                        <option value="-1"></option>
										<?php
										require_once('segments.inc.php');
										$segments = seg_segments_get( 0, CLS_USER );
										while( $s = ria_mysql_fetch_array($segments) ){
											print '<option value="'.$s['id'].'">'.htmlspecialchars($s['name']).'</option>';
										}
										?>
                                    </select>
                                </div>

                                <div class="cpg-rule" >
                                    <input disabled="disabled" type="radio" id="cpg-add-rule-usr" value="usr" name="cpg-add-rule" class="radio" />
                                    <label class="rwd-add-rule-label" for="cpg-add-rule-usr"><?php echo _("Compte client :"); ?></label>
                                    <input class="text cpg-input" type="text" readonly="readonly" id="cpg-usr-name" name="pmt-usr-name" data-rule="usr"/>
                                    <input type="button" name="cpg-usr-select" id="cpg-usr-select" class="button" value="<?php echo _("Choisir"); ?>" />
                                </div>

                                <div class="cpg-rule">
                                    <input disabled="disabled" type="radio" id="cpg-add-rule-mobile" value="mobile" name="cpg-add-rule" class="radio" />
                                    <label class="rwd-add-rule-label" for="cpg-add-rule-usr"><?php echo _("Numéro mobile :"); ?></label>
                                    <textarea class="text cpg-input" type="text" id="value_mobile" name="cpg-usr-mobile" data-rule="mobile" rows="10"></textarea>
                                </div>

                                <div class="pmt-rules-buttons">
                                    <button class="btn" name="cpg-usr-include" id="mkt-addin-rule"><?php echo _("Inclure dans la campagne"); ?> </button>
                                    <button class="btn" name="cpg-usr-exclude" id="mkt-addout-rule"><?php echo _("Exclure de la campagne"); ?> </button>
                                </div>
                            </div>
                        </fieldset>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2" class="cpg-spe-rules" id="cpg-list-rules-user">
					<table id="cpg-rules" class="cpg-rules">
						<thead>
						<tr>
							<th id="inc-check" class="col1">
								<input class="checkbox" id="del-all" onclick="checkAllClick(this)" name="checkall" type="checkbox">
							</th>
							<th id="inc-rule" class="col4"><?php echo _("Règles"); ?></th>
						</tr>
						</thead>
						<tfoot>
							<tr>
								<td colspan="2">
									<p class="btn-move notice">(<?php echo _("le symbôle"); ?> <strong>+</strong> <?php echo _("indique des comptes clients inclus dans la campagne, le symbôle"); ?> <strong>-</strong> <?php echo _("des comptes exclus."); ?>)</p>
								</td>
							</tr>
							
							<tr>
								<td colspan="2">
									<button id="cpg-del-rule" name="cpg-del-rule" class="button"><?php echo _("Supprimer"); ?></button>
								</td>
							</tr>
						</tfoot>
						<tbody>
						</tbody>
					</table>
				</td>
			</tr>
		</tbody>
	</table>
</form>

<?php

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
    gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

	if(!isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
		header('location: /admin/index.php');
		exit;
	}
?>
<form action="" method="post" id="cpg-info">
    <input type="hidden" name="cpg" value="<?php echo $_GET['cpg'] ?>">
    <input type="hidden" name="channel" value="<?php echo $Data['channel'] ?>">
    <table>
        <tbody>
            <tr>
                <td><span class="mandatory">*</span> <label for="title"><?php echo _('Titre :'); ?></label></td>
                <td><input type="text" id="title" name="title" value="<?php echo htmlspecialchars($Data['title'])?>"></td>
            </tr>
            <tr>
                <td><label for="description"><?php echo _('Description :'); ?></label></td>
                <td>
                    <textarea id="description" name="desc" cols="50" rows="5"><?php echo htmlspecialchars($Data['desc'])?></textarea>
                </td>
            </tr>
            <tr>
                <td>
                    <label for="period"><?php echo _("Envoi :"); ?></label>
                </td>
                <td>
                    <select id="period" name="period_type">
                        <?php
                            $options = array(''=>'', 'manuel' => _('Manuellement'), 'auto' => _('Automatiquement'));
						    $selected = 'manuel';
                            if( isset($Data['period']) && trim($Data['period']) != '' ){
                                $selected = 'auto';
                            }
                            foreach( $options as $val => $text ){
                                print '
                                <option value="' . $val . '" ' . ( $selected == $val ? 'selected' : '') . '>' . _($text) . '</option>';
                            } ?>
                    </select>
                    <div class="period-picker <?php echo trim($Data['period']) != '' ? '' : 'period-hide'?>">
                        <input type="text" class="date" name="period" value="<?php echo htmlspecialchars($Data['period'])?>"> à <select name="period_info" class="period-detail"> <?php 
                            for( $i=8; $i<20; $i++){ 
                                print '
                                <option value="' . $i . '" ' . ( $i == $Data['period_info'] ? 'selected' : '' ) . ' >' . ( strlen($i) == 1 ? '0'.$i : $i ) . '</option>'; 
                            } ?>
                        </select><span>h</span>
                        <select name="period_info_min" class="period-detail">
                            <?php 
                            for( $i=0; $i<60; $i++ ) { 
                                print '
                                <option value="' . $i . '" ' . ( $i == $Data['period_info_min'] ? 'selected' : '') . ' >' . ( strlen($i) == 1 ? '0'.$i : $i ) . ' </option>';
                            } ?>
                        </select>
                    </div>
                </td>
            </tr>
            <tr>
                <th colspan="2"><?php echo _("Message du SMS"); ?></th>
            </tr>
            <tr>
                <td colspan="2">
                    <textarea name="message" id="msg" cols="50" rows="10"><?php echo htmlspecialchars($Data['message']) ?></textarea>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <div>
                        <?php echo _("Nombre de caractères restants :"); ?> <strong><span id="smsLength"></span></strong>/<span id="smsMaxLength"></span>
                    </div>
                    <div>
                        <?php echo _("Nombre de SMS :"); ?> <strong><span id="smsCount"></span></strong>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="2" >
                    <label for="is_marketing" class="is_marketing">
                        <input type="checkbox" name="is_marketing" id="is_marketing" <?php echo (!$Data['is_marketing']?'checked':'')?> >
                        <span><strong><?php echo _("Je certifie sur l’honneur que le SMS n’est pas à caractère publicitaire et demande le retrait du message STOP.*"); ?></strong></span>
                    </label>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p class="mention">* <?php echo _("Un message de 11 caractères, tel que \"STOP: 31966\" sera ajouté à la fin de votre message."); ?></p>
                    <?php if( count($campaign->getUsersMobile()) > 0 ){ ?> 
                    <div class="exec-manuel">
                        <button id="exec-campaign"><?php echo _("Envoyer"); ?></button>
                        <p class="notice"><?php echo _("Veuillez garder cette page ouverte après avoir envoyé les SMS"); ?></p>
                    </div>
                    
                    <?php } ?>
                </td>
            </tr>
        </tbody>
		<?php if( !$Executed  ){?>
        <tfoot>
            <tr>
                <td colspan="2">
                <?php if( $_GET['cpg'] != 0  && gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_DEL') ){ ?>
                    <button name="del"><?php echo _("Supprimer"); ?></button>
                <?php } ?>
                    <button name="save"><?php echo _("Enregistrer"); ?></button>
                </td>
            </tr>
        </tfoot>
        <?php } ?>
    </table>
</form>

<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

// Vérifie que le module Marketing est activé
if( !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
	header( 'location: /admin/index.php' );
	exit;
}

require_once( 'Marketing/models/Campaigns.inc.php' );
require_once( 'Marketing/TriggersManager.inc.php' );
require_once( 'Marketing/Channels.inc.php' );
require_once( 'Marketing/StatCampaign.inc.php' );
require_once( 'Marketing/SMS_Partners.inc.php' );

// Ajout de campagne
if( isset($_POST['save-main']) ){
	header('location: new.php');
	exit;
}

// Suppression des campagnes sélectionnées
if( isset($_POST['del-main']) && isset($_POST['cpg']) ){
	$error = false;
	foreach( $_POST['cpg'] as $cpg ){

		if( !CampaignsManager::delCampaigns( $cpg ) ){
			$error = true;
			break;
		}

		if( !Channels::delAllChannels($cpg) ){
			$error = true;
			break;
		}
	}
	if($error){
		header('location: index.php?error=true');
		exit;
	}else{
		header('location: index.php?success=del');
		exit;
	}
}

// Titre de la page
define( 'ADMIN_PAGE_TITLE', _('Campagnes SMS').' - '._('Campagnes Marketing') );
require_once( 'admin/skin/header.inc.php' );

$res = CampaignsManager::getCampaigns();
$campaign_count = 0;
if( $res ){
	$campaign_count = ria_mysql_num_rows( $res );
}

?>

<h2><?php print _('Campagnes SMS'); ?> (<?php print number_format( $campaign_count, 0, ',', ' ' ); ?>)</h2>

<?php
if( isset( $_GET['success'] ) && $_GET['success'] === 'true' ){
	print '<div class="notice success">'._('La campagne a bien été enregistré').'</div>';
}
if( isset( $_GET['success'] ) && $_GET['success'] === 'del' ){
	print '<div class="notice success">'._('La campagne a bien été supprimé').'</div>';
}
if( isset($_GET['error']) ){
	print '<div class="notice error">'._('Une erreur est survenue lors de la suppression de la campagne').'</div>';
}

$quota = 0;
$quota_used = SMS_Partners::getAllUsedQuota();
$quota_to_use = 0;

$rPartners = SMS_Partners::getPartnersTenants($config['tnt_id'], 0, false);

if( $rPartners && ria_mysql_num_rows($rPartners) ){
	while( $ptn = ria_mysql_fetch_assoc($rPartners) ){
		$rQuota = SMS_Partners::getPartnersQuota( $config['tnt_id'], $ptn['ptn_id'] );
		if( $rQuota && ria_mysql_num_rows($rQuota) ){
			while( $qta = ria_mysql_fetch_assoc($rQuota) ){
				$quota += ($qta['qte']-$qta['qte_use']);
			}
		}
		
	}
}

ob_start();
	if( !$res ){
		print '<tr><td colspan="6">'._('Vous ne possédez aucune campagne pour le moment.').'</td></tr>';
	}else{
		while( $row = ria_mysql_fetch_assoc( $res ) ){
			try{
				$campaign = new Campaigns( $row );
				$campaign->setCampaignUsers();
				$stat = new StatCampaign();
				$stat->setCampaign($row);
				$messageCount = $stat->getCampaignMessageCount();
			}
			catch(Exception $e){
				print '<tr><td colspan="6">'._('Une erreur est survenue au chargement de la campagne').'</td></tr>';
				continue;
			}
			
			$user_count = count($campaign->getUsersMobile());
			$quota_to_use += $user_count;
			?>
			<tr>
				<td headers="cpg-id">
					<input type="checkbox" class="checkbox" name="cpg[]" value="<?php print $campaign->props['id'] ?>" />
				</td>
				<td headers="cpg-name">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_EDIT') ){ ?>
						<a href="edit/edit.php?cpg=<?php print $campaign->props['id'] ?>"><?php print htmlspecialchars( $campaign->props['title'] ); ?></a>
					<?php }else{ 
						print htmlspecialchars( $campaign->props['title'] );
					}?>
				</td>
				<td headers="cpg-start"><?php print ria_date_format( $campaign->props['date_start'] ) ?></td>
				<td headers="cpg-end"><?php print ria_date_format( $campaign->props['date_end'] ) ?></td>
				<td headers="cpg-send" class="align-right"><?php print $messageCount ?></td>
			</tr>
			<?php
		}
	}
$table_content = ob_get_clean();
?>
<div class="marketing">
<div class="stats-menu">
	<table id="table-synthese-order" class="orders">
		<caption><?php print _('Informations sur votre compte SMS'); ?></caption>
		<thead>
			<tr>
				<th id="hd-rewards-total"><?php print _('Solde'); ?></th>
				<th id="hd-rewards-used"><?php print _('Utilisés'); ?></th>
				<th id="hd-rewards-no-used"><?php print _('Envois prévus'); ?></th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td headers="hd-rewards-total"><?php print $quota ?>	</td>
				<td headers="hd-rewards-used"><?php print number_format($quota_used, 0) ?></td>
				<td headers="hd-rewards-no-used"><?php print $quota_to_use ?></td>
			</tr>
		</tbody>
	</table>
</div><br />

<form action="index.php" method="post">
	<table class="checklist" id="table-campagnes-sms">
		<thead>
			<tr>
				 <th id="cpg-id">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this)" />
				</th>
				<th id="cpg-name"><?php print _('Nom'); ?></th>
				<th id="cpg-start"><?php print _('Début'); ?></th>
				<th id="cpg-end"><?php print _('Fin'); ?></th>
				<th id="cpg-send"><?php print _('Messages envoyés'); ?></th>
			</tr>
		</thead>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_DEL') || gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_ADD') ){ ?>
		<tfoot>
		<tr>
			<td colspan="2" class="tdleft">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_DEL') ){ ?>
                <input type="submit" class="btn-del" name="del-main" value="<?php print _('Supprimer'); ?>" />
				<?php } ?>
            </td>
			<td colspan="3">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_SMS_ADD') ){ ?>
                <button><a href="edit/edit.php?tab=general&cpg=0" class="btn"><?php print _('Ajouter'); ?></a></button>
				<?php } ?>
            </td>
		</tr>
		</tfoot>
		<?php } ?>
		<tbody>
			<?php print $table_content ?>
		</tbody>
	</table>
</form>
</div>
<?php
	require_once( 'admin/skin/footer.inc.php' );
?>

@charset "UTF-8";
/* Global */
/* Bleu */
/* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières */
/* exemples d'utilisation : bg de niveau 2 dans les entêtes de tableaux */
/* exemples d'utilisation : infos */
/* exemples d'utilisation : infos border */
/* Gris */
/* exemples d'utilisation : couleurs utilisées principalement pour  les bordures des tableaux */
/* exemples d'utilisation : footer, rollover */
/* exemples d'utilisation : Couleur utilisée pour le custom du scroll */
/* exemples d'utilisation : Couleur utilisée pour le custom du scroll */
/* Vert */
/* exemples d'utilisation : en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) */
/* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) // fusionné avec $positive-hover (#e5ffe5) */
/* exemples d'utilisation : // valeur positive */
/* warning (Jaune) */
/* exemples d'utilisation : Filet de block de texte pour les WARNING // Anciennement $yellow-medium */
/* exemples d'utilisation : bg de block de texte pour les WARNING // Anciennement $yellow-light */
/* success */
/* exemples d'utilisation : Filet de block de texte pour les success */
/* exemples d'utilisation : bg de block de texte pour les success */
/* info / notes */
/* exemples d'utilisation : Filet de block de texte pour les notes */
/* exemples d'utilisation : bg de block de texte pour les notes */
/* danger */
/* exemples d'utilisation : Filet de block de texte pour les alertes */
/* exemples d'utilisation : bg de block de texte pour les alertes */
/* exemples d'utilisation : survol d'un élément coloré en $red-pale */
/* Autres */
/* ALERT VARIABLES */
:root {
  --mdc-theme-primary: $dark-color;
}

/* Ce Fichier est amené à disparaitre */
/* Ne rien ajouter ici. Si possible, supprimer/déplacer les éléments de ce fichier */
/* Titre */
h1 {
  font-size: 14px;
  padding: 5px;
  padding-left: 10px;
  background-color: #303030;
  height: 25px;
}

h1 a, h1 a:visited {
  color: #f8f8ff;
  text-decoration: none;
  font-family: Arial;
  font-size: 19px;
}

h1 a:hover {
  border-bottom: 1px solid #f8f8ff;
}

/* Zone de contenu */
#popup-content #site-content {
  margin: 0;
  min-height: 0;
  min-width: 0;
}

h3 {
  border-bottom: 1px solid #A9A9A9;
  font-size: 14px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding-bottom: 3px;
}

h4 {
  border-bottom: 1px solid #A9A9A9;
  font-size: 14px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding-bottom: 3px;
}

h1.no-border, h2.no-border, h3.no-border, h4.no-border {
  border: none;
}

dl {
  border-bottom: 1px solid #A9A9A9;
  padding: 10px 0;
}

ul {
  margin-top: 10px;
}

dl dt {
  margin-bottom: 3px;
  font-size: 14px;
  font-weight: 600;
}

hr {
  margin-bottom: 10px;
  margin-top: 10px;
  border-style: none;
  border-bottom: 1px solid #A9A9A9;
}

dl dt a {
  color: #232E63;
  text-decoration: none;
}

dl dt a:hover {
  text-decoration: underline;
}

li {
  margin: 3px 20px;
}

dl dd {
  margin: 3px 0;
}

p, ul {
  margin-bottom: 5px;
}

b {
  font-weight: 600;
  font-size: 1.4em;
}

address {
  font-style: normal;
  font-weight: 500;
}

option {
  font-weight: 500;
}

optgroup {
  font-weight: 600;
}

b, strong {
  font-weight: 600;
}

/* Choix de la société / locataire */
.accesstenant {
	text-align: center;
	padding: 10px 0 !important;
	margin: 0;
	margin-bottom: 0 !important;
}

.accesstenant strong {
	font-size: 1.2em;
}


/* Résultats du moteur de recherche */
#site-content h2#search-results-title {
  margin-bottom: 0px;
  border-bottom-style: none;
  padding-bottom: 5px;
}

#site-content span#search-results-infos {
  display: block;
  margin-bottom: 10px;
  border-bottom: 1px solid #A9A9A9;
  padding: 3px 0;
}

#site-content #search-results {
  padding-bottom: 10px;
  clear: both;
}

#site-content dl.search-result {
  clear: both;
  min-height: 105px;
}

#site-content dl.search-result dt {
  margin-bottom: 0px;
  font-size: 1em;
  border-bottom-style: none;
  padding-bottom: 0px;
}

#site-content dl.search-result dd.search-result-img {
  float: left;
  width: 88px;
  height: 80px;
  clear: both;
}

#site-content .search-result dd {
  padding-left: 0;
  margin-left: 0;
}

#site-content .search-result .search-result-url, #site-content .search-result .search-result-url a {
  font-size: 0.9em;
  text-decoration: none;
}

#site-content .search-result .search-result-url a:hover {
  text-decoration: underline;
}

#site-content dl.search-result .search-result-tabs {
  display: none;
}

#site-content dl.search-result:hover .search-result-tabs {
  display: block;
}

#site-content .search-result .search-result-tabs ul {
  list-style-type: none;
}

#site-content .search-result .search-result-tabs ul li {
  display: inline;
  margin-left: 0;
  margin-right: 10px;
}

#site-content .search-result .search-result-tabs ul li a {
  text-decoration: none;
}

#site-content .search-result .search-result-tabs ul li a:hover {
  text-decoration: underline;
}

#site-content .search-paginator {
  display: flex;
  justify-content: space-between;
}

#site-content .search-page {
  padding-top: 3px;
  float: left;
}

#site-content .search-nav {
  padding-top: 3px;
  float: right;
}

#site-content .search-nav a {
  text-decoration: none;
}

#site-content .search-nav a:hover {
  text-decoration: underline;
}

/* Tableaux */
#site-content table.table-options {
  min-width: 75%;
}

#popup-content table {
  width: 100%;
  margin: 1px;
}

@media (max-width: 767px) {
  #popup-content table {
    table-layout: fixed;
  }
}

#site-content table.nocaption {
  border: none;
}

#site-content table.nocaption tbody td {
  padding: 5px 0;
}

#site-content table caption a {
  color: #fff;
  text-decoration: none;
}

#site-content table caption a:hover {
  text-decoration: underline;
}

#site-content table table.tarif {
  margin-bottom: 0px;
  width: 600px;
}

#site-content table table.tarif tr {
  height: 20px;
}

#site-content table table.tableZone, #site-content table table.tablePrice {
  margin-bottom: 0px;
  margin-top: 0px;
  border-width: 0px;
  border-collapse: collapse;
}

#site-content table table.services {
  margin-top: 0px;
  margin-bottom: 0px;
  border-width: 0px;
}

#site-content table td, #popup-content table td {
  font-size: 12px;
}

#site-content table.list:not(#table-map-file) tbody td, #site-content table.checklist tbody td, #popup-content table.checklist tbody td, #site-content table.table-cgv tbody td {
  border-bottom: 1px solid #A9A9A9;
}

#site-content table.table-cgv {
  border-collapse: collapse;
}

#site-content table.list:not(#table-map-file) tbody td.td-nowrap, #site-content table.checklist tbody td.td-nowrap, #popup-content table tbody td.td-nowrap {
  white-space: nowrap;
}

#site-content table.checklist tbody tr:hover, #site-content .tb-kpi tr:hover, #site-content .sub-history tr:hover, #popup-content table.checklist tbody tr:hover {
  background-color: #EEEEEE;
}

#site-content table tfoot, #popup-content table tfoot,
#site-content table tfoot th, #popup-content table tfoot td {
  background-color: #fff;
}

#site-content table tfoot td.back-color-white {
  background-color: #fff;
}

#site-content table tfoot td.text-center {
  text-align: center;
}

#site-content table tbody input.datepicker {
  width: 130px;
}

#site-content table tbody select.zone {
  width: 195px;
  margin-right: 4px;
  display: block;
  float: left;
}

#site-content table tbody input.zone {
  width: 191px;
}

#site-content table tbody input.del-obj-pmt {
  width: 16px;
  border-style: none;
  vertical-align: text-top;
  padding-top: 2px;
  margin-left: 5px;
}

#site-content table tbody input[type=submit] {
  width: auto;
}

#site-content table tbody input#taxcode {
  width: 145px;
}

#site-content #siret, #popup-content #siret,
	#site-content #naf, #popup-content #naf {
		width: auto;
}

#site-content table tbody select.large,
#popup-content table tbody select.large {
  width: 500px;
}

#site-content #prd-parents {
  height: 100px;
}

#site-content table tbody select.multiple {
  height: 250px;
}

#site-content table tbody textarea#descZone {
  height: 150px;
}

#site-content table tbody textarea.referencing {
  height: 50px;
}

#site-content table tbody textarea#keywords, #site-content table tbody textarea.keywords {
  height: 260px;
}

#site-content table tbody textarea.ref-desc {
  height: 100px;
}

#site-content table tbody textarea.short,
#popup-content table tbody textarea#keywords, #popup-content table tbody textarea.short {
  height: 100px;
}

/* #site-content table tbody #ref, #site-content table tbody .ref, #popup-content table tbody #ref{
	width: 125px;
} */
#popup-content table tbody input.price, #popup-content table tbody select.price, #popup-content table tbody input.size, #popup-content table tbody input.number, #popup-content input.price,
#popup-content input.number,
#site-content table tbody input.price, #site-content table tbody select.price, #site-content table tbody input.size, #site-content table tbody input.number, #site-content input.price {
  width: 90px;
  text-align: right;
}

#site-content table tbody td.numeric, #site-content table tbody th.numeric, #site-content table tfoot th.numeric {
  text-align: right;
  white-space: nowrap;
}

#site-content table tbody input.code {
  width: 145px;
}

#popup-content div.error {
  color: #ff0000;
  padding: 8px;
  border: 1px solid #ff0000;
  margin: 8px;
}

.sale-type {
  padding: 3px !important;
}

#table-poids-dimentions, #table-services-livraison {
  width: 450px;
}

#table-ajout-regle {
  width: 600px;
}

#table-pricewatching {
  margin-top: 5px;
}

#table-concurrents {
  margin-top: 5px;
}

#tableau-nomenclature {
  width: 500px;
}

table#rwp-rewards {
  width: 600px;
}

/* Taille des th au cas par cas - pour remplacer les colsgroup */
/* Cols */
#produit-general {
  width: 175px;
}

#td-field-1 {
  width: 220px;
}

#td-field-2 {
  width: 400px;
}

#hd-rewards-total, #hd-rewards-ttc, #hd-rewards-used, #hd-rewards-used-ttc, #hd-rewards-no-used, #hd-rewards-no-used-ttc {
  width: 140px;
}

#prc_resume_name {
  width: 200px;
}

#prc_resume_ht, #prc_resume_ttc {
  width: 80px;
}

#sync {
  width: 35px;
}

#information {
  width: 400px;
}

#conditions-prc {
  width: 650px;
}

#action-prc {
  width: 105px;
}

#ord-del {
  width: 20px;
}

#ord-id {
  width: 100px;
}

#ord-tref {
  width: 300px;
}

#ord-date {
  width: 150px;
}

#ord-products {
  width: 150px;
}

#ord-pos {
  width: 95px;
}

#td-table-nomenclature-1 {
  width: 150px;
}

#td-table-nomenclature-compo-1 {
  width: 135px;
}

#td-table-nomenclature-compo-3 {
  width: 50px;
}

#td-table-nomenclature-compo-4 {
  width: 80px;
}

#td-table-nomenclature-compo-5 {
  width: 30px;
}

.list-prd-relations-rel-check, #rel-is-sync {
  width: 20px;
}

.list-prd-relations-rel-ref {
  width: 150px;
}

#rel-publish, #rel-publish-site {
  width: 235px;
}

#rel-ord-pos {
  width: 85px;
}

#td-table-images {
  width: 180px;
}

#doc-sel {
  width: 25px;
}

#doc-remp {
  width: 280px;
}

#td-stock-conditionnement {
  width: 145px;
}

#prd-colisage {
  width: 325px;
}

table #qte, table #res, table #res_web, table #com, table #prepa, table #mini, table #maxi {
  width: 75px;
}

#td-services-livraison {
  width: 395px;
}

#th-author {
  width: 300px;
}

#th-message {
  width: 568px;
}

#tb-redirection #url {
  width: 705px;
}

#tb-redirection #action {
  width: 50px;
}

#td-tbl-ctr-mdl-1 {
  width: 245px;
}

#td-tbl-ctr-mdl-2 {
  width: 586px;
}

#th-tbl-export-1 {
  width: 185px;
}

#th-tbl-export-2 {
  width: 645px;
}

#th-tbl-export-3 {
  width: 830px;
}

#td-tbl-export-alone {
  width: 845px;
}

#td-ajout-regle-1 {
  width: 150px;
}

#td-ajout-regle-2 {
  width: 300px;
}

#th-pricewatching-1 {
  width: 17px;
}

#th-pricewatching-2 {
  width: 300px;
}

#th-pricewatching-3 {
  width: 120px;
}

#th-concurrents-1, #th-concurrents-3, #th-concurrents-4, #th-concurrents-5 {
  width: 100px;
}

#th-concurrents-2 {
  width: 250px;
}

#td-tableau-nomenclature-1 {
  width: 330px;
}

#psidate {
  width: 180px;
}

#psi_label {
  width: 400px;
}

#brd-sel {
  width: 25px;
}

#brd-name {
  width: 275px;
}

#brd-url {
  width: 425px;
}

#brd-publish {
  width: 115px;
}

#brd-prd {
  width: 150px;
}

#td-avis-conso-1, #td-avis-conso-2 {
  width: 150px;
}

#td-no-ref-1 {
  width: 115px;
}

#td-no-ref-2 {
  width: 615px;
}

#td-no-ref-3 {
  /* #td-no-ref-1 + #td-no-ref-2 */
  width: 730px;
}

/* Codes promotions */
#popup-content .preview,
#site-content .preview {
  width: 152px;
  min-height: 152px;
  border: 1px solid #A9A9A9;
  text-align: center;
  float: left;
  margin-right: 5px;
  margin-bottom: 5px;
}

/* Gestion des listes et des tableaux */
tr.selected {
  background-color: #f0f0f0;
}

tr.deleted td {
  text-decoration: line-through;
  cursor: help;
}

tr.archived {
  background-color: #fffbcc;
}

.col-numeric {
  text-align: right;
}

.barre {
  text-decoration: line-through;
}

.pricewatching .negative {
  color: #ff0000;
}

.pricewatching .positive {
  color: #008000;
}

.checked {
  color: #008000;
}

.unchecked {
  color: #ff0000;
}

.waiting {
  color: #f5a623;
}

.warning {
  color: #ee9b1b;
}

.warning a {
  color: #232E63;
}

.mandatory {
  color: #ff0000;
}

.btn-add {
  float: right;
}

#site-content .center {
  display: block;
  margin: 5px auto;
}

#site-content input.readonly {
  border: 2px solid #fff;
}

#site-content sub {
  display: block;
}

/* Avis consommateur */
#lbl-note-1, #lbl-note-2, #lbl-note-3, #lbl-note-4, #lbl-note-5, #lbl-note-6, #lbl-note-7, #lbl-note-8, #lbl-note-9, #lbl-note-10 {
  background-repeat: no-repeat;
  padding-left: 70px;
}

#lbl-note-1 i, #lbl-note-2 i, #lbl-note-3 i, #lbl-note-4 i, #lbl-note-5 i, #lbl-note-6 i, #lbl-note-7 i, #lbl-note-8 i, #lbl-note-9 i, #lbl-note-10 i {
  display: none;
}

#lbl-note-1 {
  background-image: url(../images/reviews/notes/2.gif);
}

#lbl-note-2 {
  background-image: url(../images/reviews/notes/4.gif);
}

#lbl-note-3 {
  background-image: url(../images/reviews/notes/6.gif);
}

#lbl-note-4 {
  background-image: url(../images/reviews/notes/8.gif);
}

#lbl-note-5 {
  background-image: url(../images/reviews/notes/10.gif);
}

/* Statistiques */
.selector-menu {
  border-bottom: 1px solid #A9A9A9;
}

.moderation-menu {
  height: auto;
  margin-bottom: 10px;
}

#site-content img.export-xls, #site-content .gm-style img {
  border: none;
}

#bestseller-prd, #calls-report {
  width: 100%;
}

/* Configuration */
.ref-menu, .ref-menu2 {
  padding-bottom: 7px;
  border-bottom: 1px solid #f5a623;
  text-align: right;
  margin-bottom: 7px;
}

.ref-menu2 {
  border: none;
}

/* fieldsets */
#site-content fieldset {
  padding: 10px;
  display: block;
  border: 1px solid #A9A9A9;
}

#site-content fieldset div input.radio, #site-content fieldset div input.checkbox, #site-content #tabpanel-vertical div input.checkbox {
  width: auto;
}

#site-content #tabpanel-vertical div {
  padding: 3px;
}

#site-content fieldset div span.label, #site-content #tabpanel-vertical label, #site-content #tabpanel-vertical div span.label {
  display: inline-block;
  width: 158px;
  text-align: right;
  padding-right: 5px;
}

#site-content #tabpanel-vertical div label.inline {
  display: inline;
  float: none;
}

#site-content fieldset div.checkbox, #site-content #tabpanel-vertical div.checkbox {
  padding-left: 195px;
}

#site-content #tabpanel-vertical div input, #site-content #tabpanel-vertical div textarea {
  width: 345px;
}

#site-content #tabpanel-vertical div.actions input {
  width: auto;
}

#site-content #tabpanel-vertical div.actions {
  padding-left: 163px;
}

/* Newsletter */
.nlr-pre-inscript {
  color: GoldenRod;
}

.nlr-inscript {
  color: #008000;
}

.nlr-pre-uninscript {
  color: #f5a623;
}

.nlr-uninscript {
  color: #ff0000;
}

.edit-cat {
  padding-bottom: 5px;
  display: inline;
  font-size: 13px;
  margin-left: 5px;
  font-weight: 500;
  text-decoration: none;
  vertical-align: middle;
}

.edit-cat:hover {
  text-decoration: underline;
}

.edit-cat::before {
  content: '';
  background-image: url("/admin/dist/images/configuration_off.svg");
  background-position: left middle;
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
}

#display-cols-options.edit-cat::before {
  background-image: url("/admin/dist/images/configuration.svg");
}

p.note {
  margin-top: 6px;
  font-size: 0.9em;
  border: 1px solid #A9A9A9;
  color: #000;
  background-color: #FFFFE7;
  padding: 0.5em;
}

/* Systeme de listes de definition pliable/depliables */
dl.fold dt, dl.unfold dt {
  padding-left: 16px;
  background-repeat: no-repeat;
}

dl.fold dd, dl.unfold dd {
  padding-left: 32px;
}

dl.fold dt {
  background-image: url(../images/folding/closed.gif);
}

dl.unfold dt {
  background-image: url(../images/folding/opened.gif);
}

dl.fold dd {
  display: none;
}

dl.unfold dd {
  display: block;
}

.img-stat-search {
  border: medium none !important;
  vertical-align: middle;
  margin-right: 5px;
}

.img-stat-search:hover {
  cursor: pointer;
}

#error.errorjs {
  display: none;
  border-bottom: solid 1px #ff0000;
  position: fixed;
  padding: 10px;
  top: 0px;
  background-color: #ffdddd;
  width: 100%;
  text-align: center;
}

.popup_img {
  min-width: 0;
  background-image: none;
}

.popup_img .listes {
  display: none;
}

.popup_img .liste {
  border: solid 1px #EFEFEF;
}

.popup_img .listes .att {
  width: 100%;
  margin: 10px;
  border-bottom: solid 1px grey;
  padding-bottom: 10px;
}

.popup_img .tr_clicable .clicable {
  cursor: pointer;
}

.popup_img .tr_clicable th {
  border-bottom: solid 1px #000;
}

.popup_img .listes .att .leg {
  margin-left: 10px;
}

.popup_img .listes .att .leg ul {
  list-style-type: none;
}

.clear {
  clear: both;
}

.noborder {
  border: none !important;
}

.file {
  width: 250px !important;
}

#shadows {
  background-image: url(../images/shadows.png);
  display: none;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  z-index: 0;
}

#site-content table.statsearch td {
  padding-right: 10px;
}

#lst_search img.loader {
  border: none;
}

/* pour la popup de la page statistique / recherche */
#stat-popup-content, .popup-content {
  background-image: none;
  padding: 15px;
  min-width: 0;
}

.popup-content {
  padding: 0 15px;
}

#stat-popup-content table tfoot {
  height: 50px;
}

#site-content table .form-rep {
  text-align: center;
}

#site-content table .form-rep input, #site-content table .form-rep textarea {
  width: 350px;
}

#site-content #usr-contacts a, #site-content #usr-contacts a:visited {
  text-decoration: underline;
}

#site-content #usr-contacts .show-form-replay {
  display: block;
  margin-right: 50px;
  float: right;
  width: 90px;
}

#site-content #class tbody tr td input.submit {
  width: 100px;
}

#stat-popup-content table tr {
  height: 100px;
}

.wo-search-desc {
  color: #000;
  margin-bottom: 5px;
}

.wo-search-title {
  margin-bottom: 5px;
}

.wo-search-url {
  color: #3D50DF;
  font-size: 0.9em;
  text-decoration: none;
}

.wo-search-title a {
  font-size: 1em;
  font-weight: 600;
  text-decoration: none !important;
}

.wo-search-title a:hover {
  text-decoration: underline !important;
}

.stat-num {
  border: medium none;
  margin: 1px;
  padding: 1px;
  text-align: center;
}

.zone-clic {
  background-image: url(../images/stat-search-click.jpg);
  height: 60px;
  width: 60px;
}

.zone-no-clic {
  background-image: url(../images/stat-search-no-click.jpg);
  height: 60px;
  width: 60px;
}

.num-clic {
  height: 100%;
  padding-top: 15px;
  text-align: center;
  width: 100%;
}

.stat-info {
  padding-top: 10px;
  vertical-align: text-bottom;
}

.stat-img {
  text-align: center;
}

.cnt-hide {
  color: #A9A9A9 !important;
}

.cnt-masked {
  display: none;
}

.cnt-hide-url {
  color: #A9A9A9 !important;
  font-size: 0.9em;
  text-decoration: none;
}

.img-cnt-hide {
  opacity: 0.4;
  filter: alpha(opacity=40);
}

.del-search {
  float: left;
  font-size: 11px;
  margin-right: 25px;
  margin-top: 10px;
  color: #000 !important;
}

.del-all-search {
  font-size: 11px;
  margin-right: 25px;
  margin-top: 10px;
  color: #000 !important;
}

.search-no-publish {
  font-size: 11px;
  margin-right: 25px;
  margin-top: 10px;
  color: #000 !important;
}

.del-search:hover, .del-all-search:hover {
  cursor: pointer;
}

.border-unright {
  border-bottom: 2px solid;
  border-left: 1px solid;
  border-top: 1px solid;
}

.border-unleft-unright {
  border-bottom: 2px solid;
  border-top: 1px solid;
}

.border-unleft {
  border-bottom: 2px solid;
  border-right: 2px solid;
  border-top: 1px solid;
}

#count-suggest {
  float: left;
  margin-right: 15px;
}

#msg-suggest {
  margin: 15px 15px 25px 0px;
}

/* Formulaire ajout de suggestion */
#new label {
  float: left;
  width: 145px;
}

#new input {
  width: 300px;
}

#new select {
  width: 304px;
}

#new select optgroup option {
  margin-left: 15px;
}

#opt-sec {
  margin-top: 15px;
}

div.opt-sec {
  font-weight: 600;
  margin-bottom: 10px;
}

/* pour les commentaires sur les actualités */
#site-content table .rsp_reviews {
  margin-left: 10px;
  margin-bottom: 5px;
  padding: 3px;
  border: 1px solid #A9A9A9;
  font-style: italic;
  background-color: #f0f0f0;
}

.show_amount_diff {
  color: #ff0000;
}

.star_comment_tab_footer {
  font-size: 10px;
  font-weight: 500;
}

.left {
  text-align: left !important;
}

/* Autocompletion articles liés*/
.sct-select {
  width: 750px !important;
}

.sct-input {
  width: 300px !important;
}

.ac_results {
  box-shadow: 5px 5px 5px #A9A9A9;
  -moz-box-shadow: 5px 5px 5px #A9A9A9;
  -webkit-box-shadow: 5px 5px 5px #A9A9A9;
  min-width: 290px;
  background-color: #fff;
  border: 1px solid #A9A9A9;
  overflow: hidden;
  padding: 0;
  z-index: 99999;
  position: absolute;
}

.ac_results ul {
  width: 100%;
  max-height: 180px !important;
  list-style-position: outside;
  list-style: none;
  padding: 0;
  margin: 0;
}

.ac_results li {
  cursor: pointer;
  margin: 0px;
  padding: 2px 5px;
  cursor: default;
  display: block;
  font: menu;
  font-size: 12px;
  line-height: 16px;
  overflow: hidden;
}

.ac_loading {
  background-color: #fff;
}

.ac_odd {
  background-color: #eee;
}

.ac_over {
  background-color: #0A246A;
  color: #fff;
}

#site-content input.publish-cat {
  width: 20px;
  margin-right: 3px;
}

#site-content label[for=publish-y] {
  margin-right: 15px;
}

/* Statistique sur les poids des produits */
#site-content table.prd-weight {
  border-collapse: collapse;
}

#site-content table.prd-weight tbody tr td.stat-weight {
  text-align: right;
  vertical-align: middle;
}

#site-content table.prd-weight span.desc {
  display: block;
  font-style: italic;
  margin-top: 5px;
  text-decoration: none;
}

#site-content table.prd-weight tbody tr td input {
  text-align: right;
  width: 100px;
}

#site-content table thead th.th-first {
  font-weight: bold !important;
}

#site-content table thead th.th-first + th.th-first {
  border-left: 1px solid #A3A3A3;
}

#site-content table thead .head-first {
  border-bottom: 1px solid #A3A3A3;
  vertical-align: middle;
}

#site-content table thead th.th-second {
  border: 1px solid #929292;
  background-color: #e3e3e3;
  font-weight: 500;
}

#site-content table tfoot td {
  white-space: normal;
}

#site-content table tbody tr.right {
  text-align: right;
}

/* Statistique sur les produits sans articles liés */
#site-content #prd-no-related {
  border-collapse: collapse;
}

#prd-no-related tbody td {
  border-bottom: 1px solid #A9A9A9;
}

#site-content #no-redirection, #site-content #resolved {
  margin-right: 5px;
}

/* Tarification */
#site-content table.authorizations td.sync {
  padding: 0 10px 0 10px;
  border-right: 1px solid #A3A3A3;
}

#site-content table.authorizations td.action {
  border-left: 1px solid #A3A3A3;
}

#site-content span.info-bulle {
  color: #707070;
  font-size: 10px;
  cursor: help;
}

td.left {
  text-align: left;
}

td.right {
  text-align: right;
}

td.pos-center {
  text-align: center;
}

td.none-cdt {
  padding: 10px !important;
}

div.conditions {
  margin-bottom: 10px;
  padding-bottom: 10px;
  padding-left: 55px;
  clear: both;
}

#none-eco {
  margin-bottom: 20px;
}

/* Horaires d'expedition */
#form-expedition div.exp-menu {
  margin: 10px 0px 10px 55px;
}

#site-content li.exp-website {
  list-style-type: none;
  margin-bottom: 3px;
}

#site-content li.exp-website input {
  margin-right: 5px;
}

/* Jours Fériés pour les expéditions */
#tb-holidays tbody td {
  border-bottom: 1px solid #A9A9A9;
  padding: 5px;
}

#tb-holidays tbody td.hld-exp {
  text-align: center;
}

#tb-holidays tbody td label {
  margin-right: 10px;
  width: 20px;
}

#tb-holidays tbody td input[type=radio] {
  margin-right: 2px;
  width: 20px;
}

#tb-holidays img.hld-nav-year {
  border: medium none;
  height: 16px;
  width: 16px;
  cursor: pointer;
}

/* Fermetures exceptionnelles */
#tb-closing tbody input.action {
  margin-bottom: 5px;
}

#tb-closing tbody input[type=text] {
  width: 100%;
}

#site-content .authorizations input[type=submit],
#tb-closing tbody input[type=submit] {
  width: auto;
}

#tb-redirection tbody img.edit-url {
  border: medium none;
  cursor: pointer;
  width: 25px;
}

#tb-closing tbody div.save-load {
  display: none;
}

#tb-closing tbody div.save-load img {
  border: medium none;
}

/* Tarification */
#tbl-dispo-fld {
  margin-top: 10px;
}

/* Traduction */
#tb-translate tr td {
  padding: 5px;
}

#tb-translate tr td textarea {
  width: 100% !important;
  height: auto;
}

#tb-translate [id=exporter] {
  float: right;
  font-size: 12px;
  padding: 2px;
}

#tooltip {
  background-color: #fff;
  border: 1px solid #111111;
  padding: 5px;
  position: absolute;
  z-index: 3000;
  width: auto;
}

#actions-img {
  margin-top: 15px;
  border-bottom: 1px solid #A9A9A9;
  padding-bottom: 5px;
  margin-bottom: 20px;
}

#div-search-img {
  float: left;
}

.div-action-img {
  text-align: right;
}

#infos-img {
  box-shadow: 0 0 22px 6px #777777;
  background-color: #fff;
  display: none;
  height: 176px;
  padding: 10px;
  position: absolute;
  width: 370px;
  z-index: 9999;
  cursor: pointer;
}

#infos-img .name-img {
  display: block;
  font-weight: 600;
  margin-bottom: 3px;
}

#infos-img .img-used {
  height: 156px;
  position: relative;
  float: left;
  width: 184px;
}

#infos-img ul {
  margin-top: 2px;
}

#infos-img .more {
  bottom: 0;
  position: absolute;
}

#infos-img .title {
  color: #000;
  text-decoration: none;
}

#img-infos #media {
  margin-bottom: 5px;
}

@media (min-width: 768px) {
  #img-infos #media {
    margin-right: 5px;
    margin-bottom: 5px;
    float: left;
  }
}

#img-infos img {
  border: 1px solid #A9A9A9;
  margin-right: 10px;
  padding: 2px;
}

#img-infos .img-name {
  display: block;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 7px;
  text-transform: uppercase;
}

#img-infos ul {
  margin-top: 2px;
  float: left;
}

#img-infos li {
  list-style: none;
  margin: 0 !important;
}

#img-infos .linked-img {
  margin-bottom: 10px;
}

#pagination {
  margin-top: 15px;
  padding-top: 5px;
  text-align: right;
}

@media (min-width: 768px) {
  #site-content .elem-links {
    margin-left: 280px;
  }
}

#site-content .linked-img .name-links {
  height: 19px;
  border-bottom: 1px solid #A9A9A9;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
  padding-bottom: 3px;
}

#site-content .linked-img .name-links input[name="add"] {
  float: right;
}

#site-content .links .cnt-desc {
  color: #000;
  margin-bottom: 3px;
  margin-left: 18px;
}

#site-content .links .cnt-url {
  margin-left: 18px;
}

#site-content .links .cnt-infos {
  margin-bottom: 5px;
}

#site-content .links .cnt-infos .del-link {
  float: left;
  height: 1px;
  width: 18px;
}

#site-content .edit-link {
  background-image: url("/admin/images/edit.png");
  display: block;
  height: 16px;
  width: 16px;
}

#site-content .edit-link:hover,
#site-content .authorizations .del-link:hover,
#site-content .ncmd_cart .del-link:hover {
  text-decoration: none;
}

#site-content .links .cnt-url a {
  font-size: 0.9em;
}

#site-content .links-search {
  margin-top: 15px;
}

/* Popup générique d'association d'une image à un contenu (écran documents/images/popup.php) */
#popup-links-img {
  padding-top: 15px;
}

#popup-links-img h2 {
  border-bottom: 1px solid;
  margin-bottom: 10px;
  padding-bottom: 5px;
}

#popup-links-img .cnt-infos {
  margin-bottom: 10px;
}

#popup-links-img .cnt-checkbox {
  float: left;
  margin-right: 5px;
  padding-top: 2px;
}

#popup-links-img .cnt-desc {
  color: #000;
  margin-bottom: 5px;
  margin-left: 18px;
}

#popup-links-img .cnt-url {
  font-size: 0.8em;
  margin-left: 18px;
}

#popup-links-img .cnt-place {
  margin-left: 18px;
  margin-top: 5px;
}

#popup-links-img .links, #popup-links-img #actions {
  margin-left: 0 !important;
}

#popup-links-img .media {
  float: left;
}

#popup-links-img .suggestion {
  margin-top: 35px;
  display: none;
}

#popup-links-img #search-link {
  margin-bottom: 15px;
}

/* Popup historique des états de commande */
body#popup-content.popup-order-state-history {
  padding: 15px 0 0 0;
  overflow: hidden;
}

#new-file-media {
  margin-bottom: 5px;
}

#tabpanel #editcms {
  width: 100%;
}

#site-content table tfoot td#pagination {
  vertical-align: middle;
}

#site-content #sort-perso {
  background-image: none;
}

#site-content .sort-inherited {
  font-style: italic;
  font-size: 0.9em;
}

/* Sur la page d'accueil de la configuration des retours, composant permettant de sélectionner les catégories pour lesquelles les retours sont autorisés */
#site-content #returns-categories ul {
  margin: 0px;
}

#site-content #returns-categories ul li {
  margin: 0px;
}

#site-content #returns-categories label {
  font-size: 8pt;
  margin-left: 5px;
  position: relative;
  top: -2px;
}

.color-failure {
  color: #ff0000;
}

.und {
  display: none;
}

.rgt {
  text-align: right;
}

.mart20 {
  margin-top: 20px;
}

.marr30 {
  margin-right: 30px;
}

.wraper-hidden {
  overflow: hidden;
  width: 0px;
  height: 0px;
}

.flol {
  float: left;
}

.flor {
  float: right;
}

.clr {
  clear: both;
}

#site-content #content-config-returns .arrow {
  border: none;
}

#site-content table td.rev-td {
  padding: 0px;
}

#site-content table td.rev-td table {
  border: none;
  margin: 0px;
}

#site-content table td.rev-td table tr td {
  border: none;
}

/* Stock */
#usr-contacts .rep {
  border: 1px solid #A9A9A9;
  margin: 10px;
  padding: 5px;
}

#table-return-returned .motif-valid, #table-return-returned .state-valid, #table-return-returned .qte-valid {
  margin-bottom: 3px;
}

#table-return-returned .motif-valid label {
  vertical-align: top;
  height: 15px;
  display: block;
}

#table-return-returned .motif-valid textarea {
  height: 50px;
}

#table-return-returned .valid-prd {
  border: 1px solid #A9A9A9;
  padding: 5px;
  margin-bottom: 2px;
}

#table-return-returned .valid-prd input[type=submit] {
  width: 80px;
}

#site-content table .row_tarif_resume_default {
  font-size: 1em;
  font-weight: bold !important;
}

#site-content .nomenclatures a img {
  border: none;
}

#site-content #ref.ac_loading {
  background: url(/admin/images/loader_join_file.gif) no-repeat right top;
}

#site-content .nomenclatures label {
  padding: 0 10px 0 10px;
}

#site-content .nomenclatures #ref {
  width: 320px;
}

/* Popup Liste des sources d'une erreur 404 */
#stat-popup-errors {
  background: none repeat scroll 0 0 transparent;
  padding: 15px;
  width: auto;
  min-width: 100px;
}

#stat-popup-errors table {
  border: 1px solid #A3A3A3;
  clear: both;
  margin-bottom: 15px;
}

#stat-popup-errors table caption {
  background-color: #232E63;
  color: #fff;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
}

#stat-popup-errors table tbody tr:hover {
  background-color: #EEEEEE;
}

#stat-popup-errors table thead th {
  background-color: #D3D3D3;
  padding: 3px;
  text-align: center;
}

#stat-popup-errors table tbody th {
  border-bottom: 1px solid #A9A9A9;
  padding-left: 5px;
  text-align: left;
}

#stat-popup-errors table tbody td {
  border-bottom: 1px solid #A9A9A9;
  padding: 2px;
}

#stat-popup-errors table tbody td.right {
  text-align: right;
  padding-right: 5px;
}

/* Popup ajout d'une redirection permanente */
#popup-add-redirect {
  background: none repeat scroll 0 0 transparent;
  min-width: 100px;
  padding: 15px;
  width: auto;
}

#popup-add-redirect table {
  border: 1px solid #A3A3A3;
  width: 100%;
}

#popup-add-redirect table caption {
  background-color: #232E63;
  color: #fff;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
}

#popup-add-redirect table thead tr th {
  background-color: #D3D3D3;
  text-align: center;
}

#popup-add-redirect table tbody td {
  border-bottom: 1px solid #A9A9A9;
  padding: 2px;
}

#popup-add-redirect table tbody th {
  border-bottom: 1px solid #A9A9A9;
  padding-left: 5px;
  text-align: left;
}

#popup-add-redirect table tbody input[type=text] {
  width: 100%;
}

#popup-add-redirect table tbody select {
  width: 250px;
}

#popup-add-redirect table tfoot tr {
  background-color: #D3D3D3;
  padding: 3px;
  text-align: left;
}

#popup-add-redirect table tfoot input {
  font-size: 10px;
}

#popup-add-redirect .error {
  color: #000;
  background-color: #ffdddd;
  border: 1px solid #ff0000;
  margin: 1em 0;
  padding: 1em;
}

#popup-add-redirect .error-success {
  color: #000;
  background-color: #C2FFBF;
  border: 1px solid #008000;
  margin: 1em 0;
  padding: 1em;
}

#popup-content #choose-cat-ctr input[type=radio] {
  width: 15px;
}

#popup-content .ac_results {
  left: 666px !important;
}

/* Onglet Comparateurs des fiches Produit et Catégorie */
#prd-comparator table {
  border: 1px solid #A3A3A3;
  border-collapse: collapse;
  clear: both;
}

#prd-comparator table caption {
  background-color: #232E63;
  color: #fff;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
}

#prd-comparator #tbl-infoprd {
  width: 594px;
  margin-bottom: 5px;
}

#prd-comparator table thead tr th {
  background-color: #A9A9A9;
  padding: 3px;
}

#prd-comparator table tbody tr th.last, #prd-comparator table tbody tr td.last {
  border-bottom: medium none;
}

#prd-comparator table a {
  text-decoration: none;
}

#prd-comparator table a.active {
  color: #008000;
}

#prd-comparator table a.cancel {
  color: #ff0000;
}

#prd-comparator table a:hover {
  text-decoration: underline;
}

#prd-comparator input.save:hover {
  color: #232E63;
  background-color: #000;
}

#prd-comparator input.save {
  background-color: #232E63;
  border: medium none;
  border-radius: 7px 7px 7px 7px;
  color: #fff;
  font-weight: 600;
  padding: 3px;
  cursor: pointer;
  width: 100px;
}

#prd-comparator .info label {
  display: block;
}

#prd-comparator .info input.text, #prd-comparator .info textarea {
  margin-bottom: 7px;
  margin-top: 3px;
  width: 579px;
}

#prd-comparator .info textarea {
  height: 80px;
}

#prd-comparator .middle {
  vertical-align: middle;
}

#authorizations .actions,
#prd-comparator .actions {
  margin-bottom: 5px;
  text-align: right;
}

#prd-comparator .actions2 {
  margin-top: 5px;
}

#prd-comparator .period {
  font-style: italic;
  font-weight: 500;
}

#prd-comparator ul, #form-segment .ul-stats {
  margin: 0;
  padding: 0;
  list-style-position: inside;
  padding-left: 3px;
}

#prd-comparator input.qte {
  width: 50px !important;
  text-align: right !important;
}

#prd-comparator table .error {
  background-color: #ffdddd !important;
  border: 1px solid #ff0000 !important;
  color: #000 !important;
  margin: 1em 0 !important;
  padding: 1em !important;
}

/* Fiche Segment de comptes clients */
#form-segment .ul-stats {
  margin-bottom: 5px;
}

#form-segment .ul-stats li {
  margin: 0;
}

/* Image servant d'indicateur de chargement */
img.loader {
  border-style: none !important;
}

/** Recherche dans l'export des comparateurs de prix */
#ctr-search {
  border-radius: 10px 10px 10px 10px;
  box-shadow: 0 0 5px #A9A9A9;
  border: 1px solid #A9A9A9;
  padding: 15px;
  margin-top: 20px;
  width: 772px;
}

#ctr-search #zone-filters {
  text-align: center;
  border-bottom: 1px solid #A9A9A9;
  margin-bottom: 15px;
  padding-bottom: 15px;
}

#ctr-search #zone-filters select {
  border: 1px solid #A9A9A9;
  padding: 5px;
  width: 400px;
}

#ctr-search .actions {
  border-top: 1px solid #A9A9A9;
  margin-top: 15px;
  padding-top: 15px;
  text-align: right;
}

#ctr-search #save {
  float: left;
}

#ctr-search #filter {
  text-align: left;
}

#ctr-search #filter .flt {
  border: 1px solid #848484;
  margin-left: 20px;
  margin-right: 20px;
  padding: 10px;
}

#ctr-search .textarea_popup_content {
  display: none;
}

/* Moteur de recherche de produits pour envoi sur les comparateurs de prix */
#zone-conditions .elem {
  margin-bottom: 5px;
}

#zone-conditions .elem label.name-cdt, #zone-conditions .elem a.name-cdt {
  display: block;
  float: left;
  padding-top: 10px;
  text-align: right;
  width: 201px;
}

#zone-conditions .elem label.name-cdt-radio, #zone-conditions .elem a.name-cdt-radio {
  padding-top: 2px;
}

#zone-conditions .elem a.name-cdt {
  float: left;
  width: 48px;
}

#zone-conditions .elem .options a.show_popup {
  margin-top: 5px;
  float: left;
}

#cdt-select .options,
#zone-conditions .options {
  float: left;
  margin-left: 20px;
  margin-top: 5px;
}

#ctr-search #new-cdt {
  display: block;
  margin-top: 20px;
  float: right;
}

#ctr-search option {
  padding-left: 5px;
}

#filter select, #zone-conditions .options select {
  border: 1px solid #A9A9A9;
  padding: 5px;
  width: 250px;
}

#zone-conditions .options input.text,
#cdt-select .options input.text {
  border: 1px solid #A9A9A9;
  padding: 6px;
  width: 75px;
}

#zone-conditions .options input.search-cats,
#cdt-select .options input.search-cats {
  width: 395px;
  float: none;
}

#zone-conditions .options input.search-txt,
#cdt-select .options input.search-txt {
  width: 395px;
  float: none;
}

#cdt-select {
  border-right: 1px solid #848484;
  float: left;
  margin-top: 8px;
  width: 85%;
}

#cdt-select .options .search-txt,
#cdt-select .options .search-cats,
#cdt-select .options textarea,
#cdt-select .options select {
  margin-left: 160px;
}

#cdt-select .options textarea {
  width: 100%;
  height: 150px;
}

#cdt-actions {
  float: left;
  padding-left: 15px;
  text-align: center;
  width: 12%;
}

#cdt-actions a {
  display: block;
  margin-top: 5px;
}

#infosearch textarea {
  height: 75px;
}

#zone-conditions .options-radio {
  float: left;
  margin-right: 4px;
}

#ctr-sh-results {
  margin-top: 20px;
}

#ctr-sh-results img {
  border: none;
}

#ctr-sh-results .title, #ctr-sh-results .desc {
  display: block;
  margin-bottom: 5px;
}

#ctr-sh-results .price {
  font-weight: 600;
}

#ctr-sh-results .check {
  vertical-align: middle;
}

.pop-form-cancel {
  text-align: right;
}

#site-content .ncmd_qte {
  width: 50px;
  text-align: center;
}

#site-content .ncmd-cart a img {
  border: none;
}

#popup-content tbody td.odd,
#popup-content table.checklist tbody td.odd,
#site-content tbody td.odd,
#site-content table.checklist tbody td.odd {
  background-color: #EEEEEE;
}

#site-content .authorizations tr.odd td {
  background-color: #EEEEEE;
}

#type-calc {
  width: 120px !important;
}

/* Dans l'écran de configuration des points de fidélité (écran tools/rewards/config/index.php) */
#rwd-add-product label.radio {
  width: 20px !important;
  margin-left: 5px;
}

#rwd-add-product label {
  text-align: left !important;
  width: 175px !important;
}

#rwd-add-product input.text {
  width: 300px;
}

#rwd-products-buttons {
  text-align: right;
}

#rwd-add-rule label {
  text-align: left !important;
  width: 175px !important;
}

#rwd-add-rule input.text {
  width: 300px;
}

#rwd-rules-buttons {
  text-align: right;
}

#rwd-list-rules select {
  width: 100% !important;
}

#tb-tabActions .title-pts {
  border-bottom: 1px solid #A9A9A9;
  display: block;
  font-weight: 600;
}

#tb-tabActions .rwa-params {
  margin-top: 5px;
}

#tb-tabActions .rwa-params label {
  float: left;
  width: 83px;
}

#tb-tabActions .rwa-params div {
  margin-bottom: 3px;
}

#tb-tabActions .avanced {
  display: block;
  margin-bottom: 5px;
  margin-top: 5px;
  text-align: right;
}

#tb-tabActions, #tb-landing {
  width: auto !important;
  color: #000;
  border-collapse: collapse;
  margin-left: 8px !important;
}

#tb-tabActions.popup tbody tr td {
  border: 1px solid #A9A9A9;
  padding: 3px;
}

#tb-landing label {
  float: left;
  width: 85px;
}

#tb-landing .rwa-params div {
  margin: 5px;
}

#tb-landing .del-landing {
  vertical-align: middle;
  text-align: center;
}

#site-content table td.centertd {
  text-align: center;
}

#tb-landing .del-landing a {
  color: #ff0000;
}

#tb-landing input.text {
  width: 75px;
}

#site-content table tbody .input_auto input,
#site-content table tbody .input_auto textarea,
#site-content table tbody .input_auto select {
  width: auto;
}

#site-content table thead .sortified {
  padding-right: 18px;
}

#site-content .tooltip {
  display: none;
}

.tooltip-handle {
  cursor: default;
}

#ajax-indicator {
  float: left;
  padding-top: 10px;
}

#ajax-indicator img, #prd-conversion img, #pager img {
  border: medium none;
}

#site-content .pagedisplay {
  max-width: 124px;
}

.cnt-reward .rwd-pts {
  width: 50px !important;
  text-align: right;
}

#site-content #tb-synthese-order.synthese-rewards {
  float: none;
  margin-bottom: 10px;
}

.pourcent-volume {
  font-size: 0.7em;
  font-weight: 500;
}

/* Mise en forme du paramétrage des places de marchés sur les fiches produits */
.marketplace-mdl {
  border: 1px solid #A9A9A9;
  box-shadow: 3px 3px 8px #A9A9A9;
  margin: 10px 8px 15px 0;
  padding: 10px;
}

.marketplace-mdl label {
  display: inline-block;
  width: 70%;
  float: left;
}

.marketplace-mdl .label {
  float: left;
  width: 70%;
}

.marketplace-mdl .label label {
  width: 100%;
}

.marketplace-mdl .label sub {
  width: 95%;
}

.marketplace-mdl .bold {
  display: inline-block;
  font-weight: 600;
  margin-bottom: 3px;
}

.marketplace-mdl label .desc {
  display: block;
  font-size: 0.8em;
  font-style: italic;
  width: 90%;
}

.marketplace-mdl label.radio {
  float: none;
  margin: 5px;
  width: auto;
}

.marketplace-mdl .elem {
  border-bottom: 1px dotted #A9A9A9;
  margin-bottom: 5px;
  padding-bottom: 4px;
}

#prd-comparator .marketplace-mdl select, #prd-comparator .marketplace-mdl input.text {
  width: 29%;
}

#prd-comparator .marketplace-mdl .other {
  float: right;
  width: 30%;
  margin-top: 7px;
  display: none;
}

#prd-comparator .marketplace-mdl .other.show {
  display: block;
}

#prd-comparator .marketplace-mdl .other .text {
  width: 99%;
}

#prd-comparator .marketplace-mdl .del-option {
  width: 16px;
}

#prd-comparator .add-option label {
  width: auto;
}

#prd-comparator .add-option {
  text-align: center;
}

#prd-comparator .marketplace-mdl .add-option select {
  width: 50%;
}

.marketplace-mdl .add-option option {
  padding-bottom: 3px;
  padding-left: 15px;
}

.marketplace-mdl .group-option li {
  list-style: none;
}

.nowrap {
  white-space: nowrap;
}

/* css autocomplete ui */
.ui-autocomplete {
  width: 200px;
  background: #fff;
  list-style-type: none;
  box-shadow: 4px 2px 10px -5px #000;
  border-bottom: 1px solid #EEEEEE;
  border-right: 1px solid #EEEEEE;
  border-left: 1px solid #EEEEEE;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

.ui-autocomplete li {
  display: block;
  overflow: hidden;
}

.ui-autocomplete .ui-menu-item:nth-child(2n) {
  background: #EEEEEE;
}

.ui-autocomplete li a {
  padding: 3px 10px;
  display: block;
  color: #000;
}

.ui-autocomplete li a:hover {
  color: #fff;
  background: #232E63;
}

#popup-content table .tdleft,
#site-content table .tdleft {
  text-align: left;
}

#site-content .services_available {
  list-style: none;
  margin-top: 5px;
}

/* Redirections/Substitions dans le moteur de recherche */
#add-redir-search {
  color: #000;
}

#add-redir-search .redirection {
  margin-top: 15px;
}

#add-redir-search .elem {
  margin-top: 5px;
}

#add-redir-search .elem label {
  display: block;
  float: left;
  width: 160px;
}

#add-redir-search .elem input {
  width: 250px;
}

#add-redir-search #head-results {
  border-top: 1px solid #A9A9A9;
  padding-top: 6px;
  margin-top: 10px;
  font-size: 1.2em;
  font-weight: 600;
  display: none;
}

#add-redir-search #results {
  border: 1px solid #A9A9A9;
  box-shadow: 3px 3px 5px #A9A9A9;
  margin-top: 5px;
  padding: 10px;
  display: none;
}

#add-redir-search #results img {
  float: left;
  margin-right: 5px;
}

#add-redir-search .actions {
  text-align: right;
}

#site-content .ctr-stats .positive {
  color: #006000;
  font-weight: 600;
}

#site-content table td.row_check_ttc {
  vertical-align: middle;
}

.action-popup-stats-search {
  margin-top: 10px;
}

.action-popup-stats-search #form-reinit-stats {
  float: left;
  margin-right: 10px;
}

.action-popup-stats-search #form-filter {
  padding-top: 5px;
}

/* Statistiques de recherche */
#load-stats-search .message {
  background-color: #fff;
  color: #000;
  opacity: 1;
  padding: 15px;
  width: 100%;
}

#load-stats-search .message img {
  float: left;
}

#load-stats-search .message span {
  display: block;
  font-size: 1.2em;
  font-weight: 600;
  height: 30px;
  margin-left: 57px;
  margin-top: 10px;
}

.ord-mini-action {
  width: 150px !important;
  margin-right: 10px;
}

.ord-prd-actions {
  width: 50%;
  float: left;
  text-align: left;
}

.ord-actions {
  width: 50%;
  float: left;
  text-align: left;
  display: none;
}

.ord-history {
  width: 50%;
  float: right;
}

.ord-action-reward {
  text-align: right;
}

.batch-content {
  padding: 15px;
}

.batch-content .actions {
  padding: 10px 0;
  text-align: right;
}

.batch {
  width: 100%;
  height: 300px;
}

.sortable-img {
  list-style: none;
}

#sortable2, #sortable1 {
  min-height: 154px;
}

#sortable2 {
  margin-top: 0px !important;
}

#site-content .imgs-primary-lbl {
  height: 160px;
  padding-top: 10px;
}

/* Fiche produit, classification du produit dans les familles */
.td_classify {
  position: relative;
  display: block;
}

.td_classify .box_cly_canonical {
  position: absolute;
  left: -17px;
  margin-top: 1px;
}

/* Adresses ip filtrées */
#tnt-filters .blc-ip {
  float: left;
  text-align: center;
  width: 35px;
}

#tnt-filters .label-ip {
  float: left;
  font-weight: 600;
  line-height: 12px;
  margin: 0 6px;
}

#code-like {
  margin-top: 15px;
  font-weight: 600;
}

#code-list-like {
  font-family: monospace;
  font-size: 1.2em;
  letter-spacing: 1px;
}

#code-list-like li {
  float: left;
}

#filters-options {
  margin-bottom: 10px;
  display: none;
}

.items-list-filters .prd-filters input {
  display: block;
  float: left;
  margin-right: 5px;
}

.items-list-filters .prd-filters label {
  cursor: pointer !important;
  display: block;
  line-height: 22px;
}

.th-col-show {
  display: table-cell;
  white-space: nowrap;
}

.th-col-show.prd-desc {
  white-space: normal;
}

#sort-perso {
  padding-right: 10px;
}

.autoload_loader {
  background-color: #F4F4F4;
  margin: 5px 0;
  padding: 5px;
  text-align: center;
}

#site-content .autoload_loader img {
  border: none;
}

.input-del-item {
  display: none;
}

.no-click {
  background-color: #EEEEEE;
  border: 1px solid #A9A9A9;
  color: #000;
}

@-moz-document url-prefix() {
  .no-click {
    margin: 2px;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .no-click {
    margin: 1px;
  }
}

#site-content table td.td-check-del {
  text-align: center;
  vertical-align: middle;
}

[id=list-prd] {
  margin-top: 15px;
}

.prd-filters .selector {
  max-height: 350px;
}

#stats-tags .desc {
  display: block;
}

#stats-tags .ref-cly textarea {
  height: auto;
  margin-top: 0;
  margin-bottom: 2px;
  width: 100%;
  padding: 3px;
}

#stats-tags table {
  width: 100%;
}

#stats-tags .tr-ref-show {
  display: table-row;
}

#stats-tags .tr-ref-hide {
  display: none;
}

#stats-tags .readonly {
  background: none repeat scroll 0 0 #D9D9D9;
  border: medium none #EEEEEE;
  height: auto;
  margin: 5px 20px 5px 0;
  padding: 3px;
}

#stats-tags .bold {
  font-weight: 600;
}

#display-cols-options, .display-ord-products-options {
  margin-left: 0;
}

.rwd-legend {
  float: left;
  margin-right: 10px;
}

.rwd-legend .color {
  border: 1px solid #A9A9A9;
  display: block;
  float: left;
  height: 16px;
  margin-right: 5px;
  width: 25px;
}

.rwd-legend .active {
  background-color: #DDFFDD;
}

.rwd-legend .unactive {
  background-color: #ffdddd;
}

#stat-tag-double ul {
  margin-top: 0;
}

#stat-tag-double .cnt-parent {
  list-style: none outside none;
}

#stat-tag-double .cnt-parent .title {
  font-size: 1.1em;
  font-weight: 600;
}

#site-content table tbody input.del-obj-seg {
  width: 16px !important;
  border-style: none;
  vertical-align: text-top;
  padding-top: 2px;
  margin-left: 5px;
}

.websites-languages .language {
  margin-left: 18px;
}

#site-content table tbody input.fld_usr_browse_txt {
  width: 300px;
}

#site-content table tbody input.fld_usr_browse_btn {
  font-size: 10px;
  margin-left: 5px;
}

#site-content table tbody input.text-small {
  width: 200px;
}

#img-search .label {
  font-weight: 600;
  margin-top: 10px;
}

#site-content table tbody .fld-form-reference select {
  width: 100%;
}

#site-content table tbody .fld-form-reference .fld-val-actions {
  text-align: right;
  margin-top: 3px;
}

#site-content table tbody .fld-val-actions input {
  width: auto;
}

.list-filters {
  margin-bottom: 10px;
}

.list-filters .filter-label {
  margin-bottom: 5px;
  font-weight: 600;
}

.list-filters .filter {
  margin: 10px 0 0 10px;
}

.list-filters .btn-action {
  margin: 10px 0 20px 10px;
}

.ui-selectmenu-menu {
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}

.ui-selectmenu-menu .ui-menu {
  overflow: auto;
  overflow-x: hidden;
  padding-bottom: 1px;
}

.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
  font-size: 1em;
  font-weight: 600;
  line-height: 1.5;
  padding: 2px 0.4em;
  margin: 0.5em 0 0 0;
  height: auto;
  border: 0;
}

.ui-selectmenu-open {
  display: block;
}

.ui-selectmenu-text {
  display: block;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ui-selectmenu-button.ui-button {
  text-align: left;
  white-space: nowrap;
  width: 14em;
}

.ui-selectmenu-icon.ui-icon {
  float: right;
  margin-top: 0;
}

.ui-front {
  z-index: 100;
}

.ui-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
  outline: 0;
}

.ui-menu .ui-menu {
  position: absolute;
}

.ui-menu .ui-menu-item {
  margin: 0;
  cursor: pointer;
  list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
}

.ui-menu .ui-menu-item-wrapper {
  position: relative;
  padding: 3px 1em 3px .4em;
}

.ui-menu .ui-menu-divider {
  margin: 5px 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-width: 1px 0 0 0;
}

/* icon support */
.ui-menu-icons {
  position: relative;
}

.ui-menu-icons .ui-menu-item-wrapper {
  padding-left: 2em;
}

/* left-aligned */
.ui-menu .ui-icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: .2em;
  margin: auto 0;
}

/* right-aligned */
.ui-menu .ui-menu-icon {
  left: auto;
  right: 0;
}

.ui-button {
  padding: .4em 1em;
  display: inline-block;
  position: relative;
  line-height: normal;
  margin-right: .1em;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Support: IE <= 11 */
  overflow: visible;
}

.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
  text-decoration: none;
}

/* to make room for the icon, a width needs to be set here */
.ui-button-icon-only {
  width: 2em;
  box-sizing: border-box;
  text-indent: -9999px;
  white-space: nowrap;
}

.ui-widget {
  font-family: Arial,Helvetica,sans-serif;
  font-size: 1em;
}

.ui-widget .ui-widget {
  font-size: 1em;
}

.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
  font-family: Arial,Helvetica,sans-serif;
  font-size: 1em;
}

.ui-widget.ui-widget-content {
  border: 1px solid #c5c5c5;
}

.ui-widget-content {
  border: 1px solid #A9A9A9;
  background: #fff;
  color: #333333;
}

.ui-widget-content a {
  color: #333333;
}

.ui-widget-header {
  border: 1px solid #A9A9A9;
  background: #e9e9e9;
  color: #333333;
  font-weight: 600;
}

.ui-widget-header a {
  color: #333333;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  border: 1px solid #c5c5c5;
  background: #f6f6f6;
  font-weight: 500;
  color: #454545;
}

.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
  color: #454545;
  text-decoration: none;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
  border: 1px solid #A9A9A9;
  background: #ededed;
  font-weight: 500;
  color: #2b2b2b;
}

#site-content ul.no-style li {
  list-style: none;
}

#site-content .div_check_publish {
  margin-bottom: 5px;
}

#site-content .div_check_publish .label_check_publish {
  margin-left: 5px;
}

#site-content table button.previsualisation {
  margin-right: 5px;
}

#site-content table button.previsualisation a {
  color: inherit;
}

#site-content table button.previsualisation a:hover {
  text-decoration: none;
}

.page-load-block {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  background-color: #000;
  opacity: 0.5;
}

.popup_ria_back_load {
  position: fixed;
  width: 100%;
  height: 100%;
  display: block;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: #000;
  opacity: 0.6;
}

.popup_ria_back_load.abs {
  position: absolute;
}

#site-content div.pmt-list-discount {
  padding: 0px 20px !important;
}

table#config-email tbody td.designation {
  width: 260px;
}

table#config-email tbody input {
  width: 40px;
  text-align: right;
}

#site-content #product input.btn-action.active {
  background: #008000;
}

#site-content #product input.btn-action.inactive {
  background: #FE5511;
}

.price-watching {
  margin-top: 20px;
}

#site-content #products caption.sticky {
  position: fixed;
  z-index: 80;
  display: block;
  width: 1706px;
  top: 0;
}

#site-content #products thead.sticky {
  position: fixed;
  top: 22px;
  z-index: 70;
}

.ncmd_model_rights {
  margin-top: 10px;
}

[id="view_rewards_stats"] {
  margin-top: 10px;
}

.addLink {
  margin-left: 4px;
}

.menu-mobile {
  display: none;
}

#site-menu {
  height: auto;
  overflow: hidden;
}

html {
  height: 100%;
}

.is-menu-open {
  overflow: hidden;
  position: relative;
  height: 100%;
}

#site-menu.double-line #site-submenu {
  top: 160px;
}

#site-footer[id] {
  position: fixed;
  opacity: 1;
}

.page_timer {
  position: absolute;
  top: 8px;
  left: 110px;
  color: #f8f8ff;
}

#popup-content table tbody input.zipcode, #site-content table tbody input.zipcode {
  text-align: right;
  width: 90px;
}

select#zipcode {
  width: 90px !important;
  text-align: right !important;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid #A9A9A9;
  background: #cfd8e0;
  font-weight: 500;
  color: #212121;
}

.ui-helper-hidden-accessible {
  display: none;
}

#site-content table tfoot td.ord-prd-actions {
  text-align: left;
}

.ord-prd-edit-btn {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("/admin/images/edit.png");
  background-size: 100%;
}

#site-content table tbody .ord-prd-discount-select {
  width: 100px;
}

.ord-prd-edit-btn:hover {
  cursor: pointer;
}

#popup-content table tbody .inv-qte {
  width: 50px;
  text-align: center;
}

#popup-content table#inv-qte td {
  vertical-align: middle;
}

/* Référencement personnalisé */
#class-selector, #tag-selector {
  width: 200px;
}

#class-select {
  margin-bottom: 5px;
}

#tag-select {
  border-bottom: 1px solid #A9A9A9;
  padding-bottom: 10px;
}

#site-content table tbody textarea#sentence-editor {
  height: 96px;
}

#save-column {
  vertical-align: middle !important;
}

#edition-table {
  margin-top: 10px;
}

#variable-select {
  margin: 0px !important;
}

/* Graphiques rapports d'appels */
.graph-calls-semi-circle {
  width: 50%;
  float: left;
}

/* Tableau rapports d'appels */
#calls-report tfoot input#export-calls {
  float: left;
}

/* Objectifs représentant */
#site-content #goals tbody tr td {
  border: none;
  padding: 5px;
}

#site-content #goals input.actif {
  background-color: #008000;
  padding: 1px 2px;
  border: 1px solid #008000;
  color: #fff;
  font-weight: 600;
  width: 60px !important;
  text-align: center;
}

#site-content #goals input.inactif {
  background-color: #FE5511;
  padding: 1px;
  border: 1px solid #FE5511;
  color: #fff;
  font-weight: 600;
  width: 60px !important;
  text-align: center;
}

#site-content table#tb-goals .price {
  width: 107px;
}

table#tb-goals .obj-validate, table#tb-goals .obj-del {
  border: medium none;
  height: 16px;
  width: 16px;
  margin-left: 3px;
}

table#tb-goals input.obj-value, table#tb-goals input.obj-value-month, table#tb-goals input.obj-value-trimester, table#tb-goals input.obj-value-year {
  width: 80px;
  text-align: right;
  margin-top: 1px;
  margin-bottom: 1px;
}

.obj-value-input {
  font-weight: initial;
}

table#tb-goals .datepicker {
  width: 120px !important;
  margin-right: 3px;
}

table#tb-goals #name-period {
  width: 100% !important;
  margin-bottom: 3px;
  box-sizing: border-box;
}

table#tb-goals #save-new-period {
  cursor: pointer;
  font-size: 12px !important;
  padding: 5px;
}

table#tb-goals .seller-perf {
  text-align: right;
  padding-right: 10px !important;
}

.btn-state {
  padding: 1px 2px;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
}

.btn-state:hover {
  color: #deded9;
}

.btn-state--actif {
  background-color: #008000;
  border: 1px solid #008000;
}

.btn-state--inactif {
  background-color: #FE5511;
  border: 1px solid #FE5511;
}

#popup-content table.colisage-table {
  margin-bottom: 15px !important;
}

.ord-products-menu-cols {
  position: absolute;
}

.ord-products-menu-cols .cols {
  overflow-y: hidden !important;
  height: auto !important;
  display: block;
  padding: 0px;
}

.ord-prd-name-input {
  width: 100% !important;
  padding: 3px;
  box-sizing: border-box;
}

#ord-products tbody > tr:hover {
  background-color: #EEEEEE;
}

#ord-products tbody tr td textarea.ord-prd-name-input, #ord-products tbody tr td textarea.ord-prd-comment-input {
  height: auto;
  padding: 3px;
  box-sizing: border-box;
  width: 100%;
}

#ord-products tbody tr td input.ord-prd-input {
  box-sizing: border-box;
  height: 25px;
  padding: 3px;
  margin-bottom: 4px;
}

#ord-products tbody tr td {
  padding-bottom: 15px;
}

.ord-prd-nmc-row input.ord-prd-name-input {
  width: 50% !important;
}

.ord-duplicate-div {
  min-height: 40px;
}

.ord-duplicate-label {
  width: 100px;
  display: inline-block;
}

.popup-content .img-alt-form {
  margin-top: 30px;
}

.attempt_data pre {
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
}

table#typing-template-tab {
  margin-bottom: 10px !important;
}

.print-mail-active {
  margin-right: 5px;
}

.print-mail-label {
  float: left;
  padding-right: 5px;
}

.print-mail-input {
  width: 345px;
}

#site-content .colissimo-generate-label {
  margin-bottom: 4px;
}

#site-content .colissimo-generate-label-insurance {
  width: 50px;
  text-align: right;
  margin-left: 20px;
}

/* Fiche de commande (à remettre dans Layout) */
#table-une-commande .order-products-row > td {
  padding: 0;
}

#ord-products-articles {
  width: 100%;
  border-style: none !important;
  margin: 0 !important;
}

/* Prix HT, Qté, Prix TTC */
#ord-products-articles .align-right {
  white-space: nowrap;
}

#ord-products-articles tfoot th.align-right {
  padding-right: 10px;
}

.dlv_hour_plage {
  display: inline-block;
  padding: 10px;
  border-radius: 3px;
  margin: 2px;
  background: lightblue;
  border: 1px solid lightblue;
}

.dlv_hour_plage_select {
  border: 1px #ff0000 solid !important;
}

​ a[target="_blank"], .external-link {
  background-image: url("/admin/images/lien-externe.svg");
  background-repeat: no-repeat;
  background-position: right center;
}

​ a[target="_blank"]:after, .external-link:after {
  content: "\00a0\00a0\00a0\00a0";
}

/* Visuel permettant de matérialiser la période d’essai d'un abonnement Yuto Essentiel */
.information-offer {
  padding-right: 30px;
}

.information-offer h1 {
  font-weight: bold;
  margin-bottom: 30px;
}

.block-frieze {
  display: flex;
  padding-right: 30px;
  width: 600px;
}

.block-frieze > .part {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}

.block-frieze > .part:first-child > .frieze {
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  background: #6462aa;
}

.block-frieze > .part:last-child > .frieze {
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
  background: #6462aa;
  /* Old browsers */
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, #6462aa 0%, #00172E 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6462aa', endColorstr='#00172E',GradientType=1 );
  /* IE6-9 */
}

.block-frieze > .part > *:first-child {
  display: flex;
  align-items: center;
  font-size: 0.8em;
  padding: 5px;
}

.block-frieze > .part > *:first-child .icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  text-indent: -999999px;
  background-size: contain;
  background-repeat: no-repeat;
  overflow: hidden;
}

.block-frieze > .part > *:first-child .part-title {
  flex: 1 1 auto;
  text-align: center;
  font-size: 1.2rem;
  margin: 0;
  color: #fff;
}

.block-frieze > .part > .date {
  border-left: 2px solid  #6462aa;
  padding: 0 10px;
  margin: 15px 0 0 20px;
}

.block-frieze > .part > .date p {
  color: #6462aa;
  margin: 0;
}

.block-frieze > .part > .date p:first-child {
  font-weight: 600;
}

.icon-check {
  background-image: url(../../images/icon-circle-check.svg);
}

.icon-facturation {
  background-image: url(../../images/icon-circle-facturation.svg);
}

/* Abonnement Yuto Essentiel */
.yuto-notice {
  margin-top: 10px;
}

@-webkit-keyframes mdc-ripple-fg-radius-in {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
            transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
  }
  to {
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
            transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
  }
}

@keyframes mdc-ripple-fg-radius-in {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
            transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);
  }
  to {
    -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
            transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
  }
}

@-webkit-keyframes mdc-ripple-fg-opacity-in {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: 0;
  }
  to {
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
}

@keyframes mdc-ripple-fg-opacity-in {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: 0;
  }
  to {
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
}

@-webkit-keyframes mdc-ripple-fg-opacity-out {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
  to {
    opacity: 0;
  }
}

@keyframes mdc-ripple-fg-opacity-out {
  from {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    opacity: var(--mdc-ripple-fg-opacity, 0);
  }
  to {
    opacity: 0;
  }
}

.mdc-ripple-surface--test-edge-var-bug {
  --mdc-ripple-surface-test-edge-var: 1px solid #000;
  visibility: hidden;
}

.mdc-ripple-surface--test-edge-var-bug::before {
  border: var(--mdc-ripple-surface-test-edge-var);
}

.mdc-button {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 2.25rem;
  font-weight: 500;
  letter-spacing: 0.08929em;
  text-decoration: none;
  text-transform: uppercase;
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  padding: 0 8px 0 8px;
  display: inline-flex;
  position: relative;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 64px;
  height: 36px;
  border: none;
  outline: none;
  /* @alternate */
  line-height: inherit;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-appearance: none;
  overflow: hidden;
  vertical-align: middle;
  border-radius: 4px;
}

.mdc-button::before, .mdc-button::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-button::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-button.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-button.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-button.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-button.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-button.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-button::before, .mdc-button::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-button.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.mdc-button:active {
  outline: none;
}

.mdc-button:hover {
  cursor: pointer;
}

.mdc-button:disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.37);
  cursor: default;
  pointer-events: none;
}

.mdc-button.mdc-button--dense {
  border-radius: 4px;
}

.mdc-button:not(:disabled) {
  background-color: transparent;
}

.mdc-button:not(:disabled) {
  color: #232E63;
  /* @alternate */
  color: var(--mdc-theme-primary, #232E63);
}

.mdc-button::before, .mdc-button::after {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button::before, .mdc-button::after {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-button:hover::before {
  opacity: 0.04;
}

.mdc-button:not(.mdc-ripple-upgraded):focus::before, .mdc-button.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-button:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-button:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-button.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-button .mdc-button__icon {
  /* @noflip */
  margin-left: 0;
  /* @noflip */
  margin-right: 8px;
  display: inline-block;
  width: 18px;
  height: 18px;
  font-size: 18px;
  vertical-align: top;
}

[dir="rtl"] .mdc-button .mdc-button__icon, .mdc-button .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: 0;
}

.mdc-button__label + .mdc-button__icon {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: 0;
}

[dir="rtl"] .mdc-button__label + .mdc-button__icon, .mdc-button__label + .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 0;
  /* @noflip */
  margin-right: 8px;
}

svg.mdc-button__icon {
  fill: currentColor;
}

.mdc-button--raised .mdc-button__icon,
.mdc-button--unelevated .mdc-button__icon,
.mdc-button--outlined .mdc-button__icon {
  /* @noflip */
  margin-left: -4px;
  /* @noflip */
  margin-right: 8px;
}

[dir="rtl"] .mdc-button--raised .mdc-button__icon, .mdc-button--raised .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--unelevated .mdc-button__icon,
.mdc-button--unelevated .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--outlined .mdc-button__icon,
.mdc-button--outlined .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: -4px;
}

.mdc-button--raised .mdc-button__label + .mdc-button__icon,
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon,
.mdc-button--outlined .mdc-button__label + .mdc-button__icon {
  /* @noflip */
  margin-left: 8px;
  /* @noflip */
  margin-right: -4px;
}

[dir="rtl"] .mdc-button--raised .mdc-button__label + .mdc-button__icon, .mdc-button--raised .mdc-button__label + .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon,
.mdc-button--unelevated .mdc-button__label + .mdc-button__icon[dir="rtl"], [dir="rtl"]
.mdc-button--outlined .mdc-button__label + .mdc-button__icon,
.mdc-button--outlined .mdc-button__label + .mdc-button__icon[dir="rtl"] {
  /* @noflip */
  margin-left: -4px;
  /* @noflip */
  margin-right: 8px;
}

.mdc-button--raised,
.mdc-button--unelevated {
  padding: 0 16px 0 16px;
}

.mdc-button--raised:disabled,
.mdc-button--unelevated:disabled {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.37);
}

.mdc-button--raised:not(:disabled),
.mdc-button--unelevated:not(:disabled) {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button--raised:not(:disabled),
  .mdc-button--unelevated:not(:disabled) {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-button--raised:not(:disabled),
.mdc-button--unelevated:not(:disabled) {
  color: #fff;
  /* @alternate */
  color: var(--mdc-theme-on-primary, #fff);
}

.mdc-button--raised::before, .mdc-button--raised::after,
.mdc-button--unelevated::before,
.mdc-button--unelevated::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-button--raised::before, .mdc-button--raised::after,
  .mdc-button--unelevated::before,
  .mdc-button--unelevated::after {
    /* @alternate */
    background-color: var(--mdc-theme-on-primary, #fff);
  }
}

.mdc-button--raised:hover::before,
.mdc-button--unelevated:hover::before {
  opacity: 0.08;
}

.mdc-button--raised:not(.mdc-ripple-upgraded):focus::before, .mdc-button--raised.mdc-ripple-upgraded--background-focused::before,
.mdc-button--unelevated:not(.mdc-ripple-upgraded):focus::before,
.mdc-button--unelevated.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-button--raised:not(.mdc-ripple-upgraded)::after,
.mdc-button--unelevated:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-button--raised:not(.mdc-ripple-upgraded):active::after,
.mdc-button--unelevated:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-button--raised.mdc-ripple-upgraded,
.mdc-button--unelevated.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-button--raised {
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-button--raised:hover, .mdc-button--raised:focus {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.mdc-button--raised:active {
  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.mdc-button--raised:disabled {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
}

.mdc-button--outlined {
  border-style: solid;
  padding: 0 14px 0 14px;
  border-width: 2px;
}

.mdc-button--outlined:disabled {
  border-color: rgba(0, 0, 0, 0.37);
}

.mdc-button--outlined:not(:disabled) {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-button--dense {
  height: 32px;
  font-size: .8125rem;
}

@-webkit-keyframes mdc-checkbox-unchecked-checked-checkmark-path {
  0%,
  50% {
    stroke-dashoffset: 29.78334;
  }
  50% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes mdc-checkbox-unchecked-checked-checkmark-path {
  0%,
  50% {
    stroke-dashoffset: 29.78334;
  }
  50% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@-webkit-keyframes mdc-checkbox-unchecked-indeterminate-mixedmark {
  0%,
  68.2% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
  }
  68.2% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0, 1);
            animation-timing-function: cubic-bezier(0, 0, 0, 1);
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}

@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark {
  0%,
  68.2% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
  }
  68.2% {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0, 1);
            animation-timing-function: cubic-bezier(0, 0, 0, 1);
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}

@-webkit-keyframes mdc-checkbox-checked-unchecked-checkmark-path {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
    opacity: 1;
    stroke-dashoffset: 0;
  }
  to {
    opacity: 0;
    stroke-dashoffset: -29.78334;
  }
}

@keyframes mdc-checkbox-checked-unchecked-checkmark-path {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
            animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
    opacity: 1;
    stroke-dashoffset: 0;
  }
  to {
    opacity: 0;
    stroke-dashoffset: -29.78334;
  }
}

@-webkit-keyframes mdc-checkbox-checked-indeterminate-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-checked-indeterminate-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-checked-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
  }
}

@keyframes mdc-checkbox-indeterminate-checked-checkmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
  }
}

@-webkit-keyframes mdc-checkbox-checked-indeterminate-mixedmark {
  from {
    -webkit-animation-timing-function: mdc-animation-deceleration-curve-timing-function;
            animation-timing-function: mdc-animation-deceleration-curve-timing-function;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
}

@keyframes mdc-checkbox-checked-indeterminate-mixedmark {
  from {
    -webkit-animation-timing-function: mdc-animation-deceleration-curve-timing-function;
            animation-timing-function: mdc-animation-deceleration-curve-timing-function;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
    opacity: 0;
  }
  to {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-checked-mixedmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(315deg);
            transform: rotate(315deg);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-indeterminate-checked-mixedmark {
  from {
    -webkit-animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
            animation-timing-function: cubic-bezier(0.14, 0, 0, 1);
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 1;
  }
  to {
    -webkit-transform: rotate(315deg);
            transform: rotate(315deg);
    opacity: 0;
  }
}

@-webkit-keyframes mdc-checkbox-indeterminate-unchecked-mixedmark {
  0% {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    opacity: 1;
  }
  32.8%,
  100% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    opacity: 0;
  }
}

@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark {
  0% {
    -webkit-animation-timing-function: linear;
            animation-timing-function: linear;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    opacity: 1;
  }
  32.8%,
  100% {
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    opacity: 0;
  }
}

.mdc-checkbox {
  display: inline-block;
  position: relative;
  flex: 0 0 18px;
  box-sizing: content-box;
  width: 18px;
  height: 18px;
  padding: 11px;
  line-height: 0;
  white-space: nowrap;
  cursor: pointer;
  vertical-align: bottom;
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
}

.mdc-checkbox::before, .mdc-checkbox::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-checkbox::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-checkbox.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-checkbox.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-checkbox.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-checkbox.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-checkbox.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-checkbox::before, .mdc-checkbox::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-checkbox::before, .mdc-checkbox::after {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-checkbox:hover::before {
  opacity: 0.08;
}

.mdc-checkbox:not(.mdc-ripple-upgraded):focus::before, .mdc-checkbox.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-checkbox:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-checkbox:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-checkbox.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-checkbox::before, .mdc-checkbox::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-checkbox.mdc-ripple-upgraded::before, .mdc-checkbox.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-checkbox.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-checkbox__checkmark {
  color: #fff;
}

.mdc-checkbox__mixedmark {
  border-color: #fff;
}

.mdc-checkbox__background::before {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-checkbox__background::before {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate) ~ .mdc-checkbox__background {
  border-color: rgba(0, 0, 0, 0.54);
  background-color: transparent;
}

.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
  border-color: #fff;
  /* @alternate */
  border-color: var(--mdc-theme-secondary, #fff);
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-secondary, #fff);
}

@-webkit-keyframes mdc-checkbox-fade-in-background-0 {
  0% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
  50% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

@keyframes mdc-checkbox-fade-in-background-0 {
  0% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
  50% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

@-webkit-keyframes mdc-checkbox-fade-out-background-0 {
  0%, 80% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
  100% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
}

@keyframes mdc-checkbox-fade-out-background-0 {
  0%, 80% {
    border-color: #fff;
    /* @alternate */
    border-color: var(--mdc-theme-secondary, #fff);
    background-color: #fff;
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
  100% {
    border-color: rgba(0, 0, 0, 0.54);
    background-color: transparent;
  }
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background, .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
  -webkit-animation-name: mdc-checkbox-fade-in-background-0;
          animation-name: mdc-checkbox-fade-in-background-0;
}

.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background, .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
  -webkit-animation-name: mdc-checkbox-fade-out-background-0;
          animation-name: mdc-checkbox-fade-out-background-0;
}

.mdc-checkbox__native-control:disabled:not(:checked):not(:indeterminate) ~ .mdc-checkbox__background {
  border-color: rgba(0, 0, 0, 0.26);
}

.mdc-checkbox__native-control:disabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:disabled:indeterminate ~ .mdc-checkbox__background {
  border-color: transparent;
  background-color: rgba(0, 0, 0, 0.26);
}

@media screen and (-ms-high-contrast: active) {
  .mdc-checkbox__mixedmark {
    margin: 0 1px;
  }
}

.mdc-checkbox--disabled {
  cursor: default;
  pointer-events: none;
}

.mdc-checkbox__background {
  /* @noflip */
  left: 11px;
  /* @noflip */
  right: initial;
  display: inline-flex;
  position: absolute;
  top: 11px;
  bottom: 0;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 45%;
  height: 45%;
  transition: background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border: 2px solid currentColor;
  border-radius: 2px;
  background-color: transparent;
  pointer-events: none;
  will-change: background-color, border-color;
}

.mdc-checkbox[dir="rtl"] .mdc-checkbox__background,
[dir="rtl"] .mdc-checkbox .mdc-checkbox__background {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 11px;
}

.mdc-checkbox__checkmark {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  opacity: 0;
}

.mdc-checkbox--upgraded .mdc-checkbox__checkmark {
  opacity: 1;
}

.mdc-checkbox__checkmark-path {
  transition: stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  stroke: currentColor;
  stroke-width: 3.12px;
  stroke-dashoffset: 29.78334;
  stroke-dasharray: 29.78334;
}

.mdc-checkbox__mixedmark {
  width: 100%;
  height: 0;
  -webkit-transform: scaleX(0) rotate(0deg);
          transform: scaleX(0) rotate(0deg);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border-width: 1px;
  border-style: solid;
  opacity: 0;
}

.mdc-checkbox--upgraded .mdc-checkbox__background,
.mdc-checkbox--upgraded .mdc-checkbox__checkmark,
.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,
.mdc-checkbox--upgraded .mdc-checkbox__mixedmark {
  transition: none !important;
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background, .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background, .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background, .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background {
  -webkit-animation-duration: 180ms;
          animation-duration: 180ms;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path {
  -webkit-animation: 180ms linear 0s mdc-checkbox-unchecked-checked-checkmark-path;
          animation: 180ms linear 0s mdc-checkbox-unchecked-checked-checkmark-path;
  transition: none;
}

.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-unchecked-indeterminate-mixedmark;
          animation: 90ms linear 0s mdc-checkbox-unchecked-indeterminate-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-unchecked-checkmark-path;
          animation: 90ms linear 0s mdc-checkbox-checked-unchecked-checkmark-path;
  transition: none;
}

.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-checkmark;
          animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-checkmark;
  transition: none;
}

.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark {
  -webkit-animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-mixedmark;
          animation: 90ms linear 0s mdc-checkbox-checked-indeterminate-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark {
  -webkit-animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-checkmark;
          animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-checkmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark {
  -webkit-animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-mixedmark;
          animation: 500ms linear 0s mdc-checkbox-indeterminate-checked-mixedmark;
  transition: none;
}

.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark {
  -webkit-animation: 300ms linear 0s mdc-checkbox-indeterminate-unchecked-mixedmark;
          animation: 300ms linear 0s mdc-checkbox-indeterminate-unchecked-mixedmark;
  transition: none;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background {
  transition: border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1), background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1);
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path,
.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__checkmark-path {
  stroke-dashoffset: 0;
}

.mdc-checkbox__background::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0, 0);
          transform: scale(0, 0);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
  will-change: opacity, transform;
}

.mdc-ripple-upgraded--background-focused .mdc-checkbox__background::before {
  content: none;
}

.mdc-checkbox__native-control:focus ~ .mdc-checkbox__background::before {
  -webkit-transform: scale(2.75, 2.75);
          transform: scale(2.75, 2.75);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 80ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 80ms 0ms cubic-bezier(0, 0, 0.2, 1);
  opacity: 0.12;
}

.mdc-checkbox__native-control {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  opacity: 0;
  cursor: inherit;
}

.mdc-checkbox__native-control:disabled {
  cursor: default;
  pointer-events: none;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  transition: opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1), -webkit-transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);
  opacity: 1;
}

.mdc-checkbox__native-control:checked ~ .mdc-checkbox__background .mdc-checkbox__mixedmark {
  -webkit-transform: scaleX(1) rotate(-45deg);
          transform: scaleX(1) rotate(-45deg);
}

.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  transition: opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), -webkit-transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1);
  opacity: 0;
}

.mdc-checkbox__native-control:indeterminate ~ .mdc-checkbox__background .mdc-checkbox__mixedmark {
  -webkit-transform: scaleX(1) rotate(0deg);
          transform: scaleX(1) rotate(0deg);
  opacity: 1;
}

.mdc-floating-label {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
  position: absolute;
  /* @noflip */
  left: 0;
  /* @noflip */
  -webkit-transform-origin: left top;
          transform-origin: left top;
  transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  /* @alternate */
  line-height: 1.15rem;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: text;
  overflow: hidden;
  will-change: transform;
}

[dir="rtl"] .mdc-floating-label, .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  right: 0;
  /* @noflip */
  left: auto;
  /* @noflip */
  -webkit-transform-origin: right top;
          transform-origin: right top;
  /* @noflip */
  text-align: right;
}

.mdc-floating-label--float-above {
  cursor: auto;
}

.mdc-floating-label--float-above {
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
}

.mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-standard 250ms 1;
          animation: mdc-floating-label-shake-float-above-standard 250ms 1;
}

@-webkit-keyframes mdc-floating-label-shake-float-above-standard {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-standard {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-50%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-50%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-50%) scale(0.75);
  }
}

.mdc-form-field {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.01786em;
  text-decoration: inherit;
  text-transform: inherit;
  color: rgba(0, 0, 0, 0.87);
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87));
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.mdc-form-field > label {
  order: 0;
  /* @noflip */
  margin-right: auto;
  /* @noflip */
  padding-left: 4px;
}

[dir="rtl"] .mdc-form-field > label, .mdc-form-field[dir="rtl"] > label {
  /* @noflip */
  margin-left: auto;
  /* @noflip */
  padding-right: 4px;
}

.mdc-form-field--align-end > label {
  order: -1;
  /* @noflip */
  margin-left: auto;
  /* @noflip */
  padding-right: 4px;
}

[dir="rtl"] .mdc-form-field--align-end > label, .mdc-form-field--align-end[dir="rtl"] > label {
  /* @noflip */
  margin-right: auto;
  /* @noflip */
  padding-left: 4px;
}

.mdc-icon-button {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  width: 48px;
  height: 48px;
  padding: 12px;
  font-size: 24px;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  border: none;
  outline: none;
  background-color: transparent;
  fill: currentColor;
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.mdc-icon-button::before, .mdc-icon-button::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-button::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-icon-button.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-button.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-icon-button.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-icon-button.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-icon-button.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-button::before, .mdc-icon-button::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-icon-button.mdc-ripple-upgraded::before, .mdc-icon-button.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-button.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-button svg,
.mdc-icon-button img {
  width: 24px;
  height: 24px;
}

.mdc-icon-button:disabled {
  color: rgba(0, 0, 0, 0.38);
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38));
  cursor: default;
  pointer-events: none;
}

.mdc-icon-button::before, .mdc-icon-button::after {
  background-color: #000;
}

.mdc-icon-button:hover::before {
  opacity: 0.04;
}

.mdc-icon-button:not(.mdc-ripple-upgraded):focus::before, .mdc-icon-button.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-icon-button:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-icon-button:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-icon-button.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-icon-button__icon {
  display: inline-block;
}

.mdc-icon-button__icon.mdc-icon-button__icon--on {
  display: none;
}

.mdc-icon-button--on .mdc-icon-button__icon {
  display: none;
}

.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on {
  display: inline-block;
}

.mdc-icon-toggle {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  color: rgba(0, 0, 0, 0.87);
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-light, rgba(0, 0, 0, 0.87));
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 48px;
  height: 48px;
  padding: 12px;
  outline: none;
  font-size: 1.5rem;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* @alternate */
  will-change: initial;
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-toggle::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-icon-toggle.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-toggle.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-icon-toggle.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-icon-toggle.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-icon-toggle.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-icon-toggle.mdc-ripple-upgraded::before, .mdc-icon-toggle.mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-toggle.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-icon-toggle::before, .mdc-icon-toggle::after {
  background-color: black;
}

.mdc-icon-toggle:hover::before {
  opacity: 0.04;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded):focus::before, .mdc-icon-toggle.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-icon-toggle:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-icon-toggle.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-icon-toggle::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-icon-toggle--disabled {
  color: rgba(0, 0, 0, 0.38);
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38));
  pointer-events: none;
}

.mdc-line-ripple {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 180ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  z-index: 2;
}

.mdc-line-ripple--active {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
  opacity: 1;
}

.mdc-line-ripple--deactivating {
  opacity: 0;
}

.mdc-ripple-surface {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  position: relative;
  outline: none;
  overflow: hidden;
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-ripple-surface::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-ripple-surface.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-ripple-surface.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-ripple-surface.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-ripple-surface.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-ripple-surface.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  background-color: #000;
}

.mdc-ripple-surface:hover::before {
  opacity: 0.04;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-ripple-surface.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-ripple-surface::before, .mdc-ripple-surface::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-ripple-surface.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded] {
  overflow: visible;
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded]::before, .mdc-ripple-surface[data-mdc-ripple-is-unbounded]::after {
  top: calc(50% - 50%);
  /* @noflip */
  left: calc(50% - 50%);
  width: 100%;
  height: 100%;
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::before, .mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::after {
  top: var(--mdc-ripple-top, calc(50% - 50%));
  /* @noflip */
  left: var(--mdc-ripple-left, calc(50% - 50%));
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface[data-mdc-ripple-is-unbounded].mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-ripple-surface--primary::before, .mdc-ripple-surface--primary::after {
  background-color: #232E63;
}

@supports not (-ms-ime-align: auto) {
  .mdc-ripple-surface--primary::before, .mdc-ripple-surface--primary::after {
    /* @alternate */
    background-color: var(--mdc-theme-primary, #232E63);
  }
}

.mdc-ripple-surface--primary:hover::before {
  opacity: 0.04;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface--primary.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface--primary:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.16;
}

.mdc-ripple-surface--primary.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.16;
}

.mdc-ripple-surface--accent::before, .mdc-ripple-surface--accent::after {
  background-color: #fff;
}

@supports not (-ms-ime-align: auto) {
  .mdc-ripple-surface--accent::before, .mdc-ripple-surface--accent::after {
    /* @alternate */
    background-color: var(--mdc-theme-secondary, #fff);
  }
}

.mdc-ripple-surface--accent:hover::before {
  opacity: 0.08;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded):focus::before, .mdc-ripple-surface--accent.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.24;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded)::after {
  transition: opacity 150ms linear;
}

.mdc-ripple-surface--accent:not(.mdc-ripple-upgraded):active::after {
  transition-duration: 75ms;
  opacity: 0.32;
}

.mdc-ripple-surface--accent.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: 0.32;
}

.mdc-notched-outline {
  display: flex;
  position: absolute;
  right: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  height: 100%;
  /* @noflip */
  text-align: left;
  pointer-events: none;
}

[dir="rtl"] .mdc-notched-outline, .mdc-notched-outline[dir="rtl"] {
  /* @noflip */
  text-align: right;
}

.mdc-notched-outline__leading, .mdc-notched-outline__notch, .mdc-notched-outline__trailing {
  box-sizing: border-box;
  height: 100%;
  border-top: 1px solid;
  border-bottom: 1px solid;
  pointer-events: none;
}

.mdc-notched-outline__leading {
  /* @noflip */
  border-left: 1px solid;
  /* @noflip */
  border-right: none;
  width: 12px;
}

[dir="rtl"] .mdc-notched-outline__leading, .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-left: none;
  /* @noflip */
  border-right: 1px solid;
}

.mdc-notched-outline__trailing {
  /* @noflip */
  border-left: none;
  /* @noflip */
  border-right: 1px solid;
  flex-grow: 1;
}

[dir="rtl"] .mdc-notched-outline__trailing, .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-left: 1px solid;
  /* @noflip */
  border-right: none;
}

.mdc-notched-outline__notch {
  flex: 0 0 auto;
  width: auto;
  max-width: calc(100% - 12px * 2);
}

.mdc-notched-outline .mdc-floating-label {
  display: inline-block;
  position: relative;
  top: 17px;
  bottom: auto;
  max-width: 100%;
}

.mdc-notched-outline .mdc-floating-label--float-above {
  text-overflow: clip;
}

.mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  max-width: calc(100% / .75);
}

.mdc-notched-outline--notched .mdc-notched-outline__notch {
  /* @noflip */
  padding-left: 0;
  /* @noflip */
  padding-right: 8px;
  border-top: none;
}

[dir="rtl"] .mdc-notched-outline--notched .mdc-notched-outline__notch, .mdc-notched-outline--notched .mdc-notched-outline__notch[dir="rtl"] {
  /* @noflip */
  padding-left: 8px;
  /* @noflip */
  padding-right: 0;
}

.mdc-notched-outline--no-label .mdc-notched-outline__notch {
  padding: 0;
}

.mdc-text-field-helper-text {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.03333em;
  text-decoration: inherit;
  text-transform: inherit;
  display: block;
  margin-top: 0;
  /* @alternate */
  line-height: normal;
  margin: 0;
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  will-change: opacity;
}

.mdc-text-field-helper-text::before {
  display: inline-block;
  width: 0;
  height: 16px;
  content: "";
  vertical-align: 0;
}

.mdc-text-field-helper-text--persistent {
  transition: none;
  opacity: 1;
  will-change: initial;
}

.mdc-text-field--with-leading-icon .mdc-text-field__icon,
.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  position: absolute;
  bottom: 16px;
  cursor: pointer;
}

.mdc-text-field__icon:not([tabindex]),
.mdc-text-field__icon[tabindex="-1"] {
  cursor: default;
  pointer-events: none;
}

.mdc-text-field {
  --mdc-ripple-fg-size: 0;
  --mdc-ripple-left: 0;
  --mdc-ripple-top: 0;
  --mdc-ripple-fg-scale: 1;
  --mdc-ripple-fg-translate-end: 0;
  --mdc-ripple-fg-translate-start: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  will-change: transform, opacity;
  border-radius: 4px 4px 0 0;
  display: inline-flex;
  position: relative;
  box-sizing: border-box;
  height: 56px;
  overflow: hidden;
  will-change: opacity, transform, color;
}

.mdc-text-field::before, .mdc-text-field::after {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  content: "";
}

.mdc-text-field::before {
  transition: opacity 15ms linear, background-color 15ms linear;
  z-index: 1;
}

.mdc-text-field.mdc-ripple-upgraded::before {
  -webkit-transform: scale(var(--mdc-ripple-fg-scale, 1));
          transform: scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-text-field.mdc-ripple-upgraded::after {
  top: 0;
  /* @noflip */
  left: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.mdc-text-field.mdc-ripple-upgraded--unbounded::after {
  top: var(--mdc-ripple-top, 0);
  /* @noflip */
  left: var(--mdc-ripple-left, 0);
}

.mdc-text-field.mdc-ripple-upgraded--foreground-activation::after {
  -webkit-animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
          animation: 225ms mdc-ripple-fg-radius-in forwards, 75ms mdc-ripple-fg-opacity-in forwards;
}

.mdc-text-field.mdc-ripple-upgraded--foreground-deactivation::after {
  -webkit-animation: 150ms mdc-ripple-fg-opacity-out;
          animation: 150ms mdc-ripple-fg-opacity-out;
  -webkit-transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
          transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));
}

.mdc-text-field::before, .mdc-text-field::after {
  background-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field:hover::before {
  opacity: 0.04;
}

.mdc-text-field:not(.mdc-ripple-upgraded):focus::before, .mdc-text-field.mdc-ripple-upgraded--background-focused::before {
  transition-duration: 75ms;
  opacity: 0.12;
}

.mdc-text-field::before, .mdc-text-field::after {
  top: calc(50% - 100%);
  /* @noflip */
  left: calc(50% - 100%);
  width: 200%;
  height: 200%;
}

.mdc-text-field.mdc-ripple-upgraded::after {
  width: var(--mdc-ripple-fg-size, 100%);
  height: var(--mdc-ripple-fg-size, 100%);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input {
  color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field .mdc-text-field__input {
  caret-color: #232E63;
  /* @alternate */
  caret-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom-color: rgba(0, 0, 0, 0.42);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field .mdc-line-ripple {
  background-color: #232E63;
  /* @alternate */
  background-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--textarea) {
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.mdc-text-field:not(.mdc-text-field--disabled) + .mdc-text-field-helper-text {
  color: rgba(0, 0, 0, 0.6);
}

.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__icon {
  color: rgba(0, 0, 0, 0.54);
}

.mdc-text-field:not(.mdc-text-field--disabled) {
  background-color: whitesmoke;
}

.mdc-text-field .mdc-floating-label {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
  top: 18px;
  pointer-events: none;
}

[dir="rtl"] .mdc-text-field .mdc-floating-label, .mdc-text-field .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--textarea .mdc-floating-label {
  /* @noflip */
  left: 4px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-floating-label, .mdc-text-field--textarea .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 4px;
}

.mdc-text-field--outlined .mdc-floating-label {
  /* @noflip */
  left: 4px;
  /* @noflip */
  right: initial;
  top: 17px;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-floating-label, .mdc-text-field--outlined .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 4px;
}

.mdc-text-field--outlined--with-leading-icon .mdc-floating-label {
  /* @noflip */
  left: 36px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--outlined--with-leading-icon .mdc-floating-label, .mdc-text-field--outlined--with-leading-icon .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 36px;
}

.mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above {
  /* @noflip */
  left: 40px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above, .mdc-text-field--outlined--with-leading-icon .mdc-floating-label--float-above[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 40px;
}

.mdc-text-field__input {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
  align-self: flex-end;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px 16px 6px;
  transition: opacity 180ms cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  border-bottom: 1px solid;
  border-radius: 0;
  background: none;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.mdc-text-field__input::-webkit-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input::-moz-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input:-ms-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input::-ms-input-placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input::placeholder {
  transition: color 180ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.mdc-text-field__input:focus {
  outline: none;
}

.mdc-text-field__input:invalid {
  box-shadow: none;
}

.mdc-text-field__input:-webkit-autofill {
  z-index: auto !important;
}

.mdc-text-field__input:-webkit-autofill + .mdc-floating-label {
  -webkit-transform: translateY(-50%) scale(0.75);
          transform: translateY(-50%) scale(0.75);
  cursor: auto;
}

.mdc-text-field--outlined {
  border: none;
  overflow: visible;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.24);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--outlined .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
}

.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading, .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

[dir="rtl"] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing, .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

.mdc-text-field--outlined .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) scale(1);
          transform: translateY(-144%) scale(1);
}

.mdc-text-field--outlined .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) scale(0.75);
          transform: translateY(-130%) scale(0.75);
}

.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--outlined::before, .mdc-text-field--outlined::after {
  content: none;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--outlined .mdc-text-field__input {
  display: flex;
  padding: 12px 16px 14px;
  border: none !important;
  background-color: transparent;
  z-index: 1;
}

.mdc-text-field--outlined .mdc-text-field__icon {
  z-index: 2;
}

.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__trailing {
  border-width: 2px;
}

.mdc-text-field--outlined.mdc-text-field--disabled {
  background-color: transparent;
}

.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom: none;
}

.mdc-text-field--outlined.mdc-text-field--dense {
  height: 48px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-134%) scale(1);
          transform: translateY(-134%) scale(1);
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: 0.8rem;
}

.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-120%) scale(0.8);
          transform: translateY(-120%) scale(0.8);
}

.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-dense 250ms 1;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-text-field__input {
  padding: 12px 12px 7px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label {
  top: 14px;
}

.mdc-text-field--outlined.mdc-text-field--dense .mdc-text-field__icon {
  top: 12px;
}

.mdc-text-field--with-leading-icon .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-text-field__icon, .mdc-text-field--with-leading-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-text-field__input, .mdc-text-field--with-leading-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon .mdc-floating-label {
  /* @noflip */
  left: 48px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon .mdc-floating-label, .mdc-text-field--with-leading-icon .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) translateX(-32px) scale(1);
          transform: translateY(-144%) translateX(-32px) scale(1);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-144%) translateX(32px) scale(1);
          transform: translateY(-144%) translateX(32px) scale(1);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) translateX(-32px) scale(0.75);
          transform: translateY(-130%) translateX(-32px) scale(0.75);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"], [dir="rtl"]
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-130%) translateX(32px) scale(0.75);
          transform: translateY(-130%) translateX(32px) scale(0.75);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake, .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir="rtl"] .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl 250ms 1;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label {
  /* @noflip */
  left: 36px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 36px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-134%) translateX(-21px) scale(1);
          transform: translateY(-134%) translateX(-21px) scale(1);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-134%) translateX(21px) scale(1);
          transform: translateY(-134%) translateX(21px) scale(1);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: 0.8rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-120%) translateX(-21px) scale(0.8);
          transform: translateY(-120%) translateX(-21px) scale(0.8);
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"], [dir="rtl"]
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir="rtl"] {
  -webkit-transform: translateY(-120%) translateX(21px) scale(0.8);
          transform: translateY(-120%) translateX(21px) scale(0.8);
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense 250ms 1;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label--shake, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense[dir="rtl"] .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl 250ms 1;
}

.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label {
  /* @noflip */
  left: 32px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-text-field--dense .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 32px;
}

.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon .mdc-text-field__icon, .mdc-text-field--with-trailing-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon .mdc-text-field__input, .mdc-text-field--with-trailing-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 12px;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 16px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon, .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input {
  /* @noflip */
  padding-left: 16px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input, .mdc-text-field--with-trailing-icon.mdc-text-field--outlined .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon {
  /* @noflip */
  left: 16px;
  /* @noflip */
  right: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: auto;
  /* @noflip */
  right: 16px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon {
  /* @noflip */
  right: 12px;
  /* @noflip */
  left: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__icon ~ .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  right: auto;
  /* @noflip */
  left: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 48px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 48px;
  /* @noflip */
  padding-right: 48px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon,
.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  bottom: 16px;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 12px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 44px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label {
  /* @noflip */
  left: 44px;
  /* @noflip */
  right: initial;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label, .mdc-text-field--with-leading-icon.mdc-text-field--dense .mdc-floating-label[dir="rtl"] {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 44px;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: initial;
  /* @noflip */
  right: 12px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: initial;
}

.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 12px;
  /* @noflip */
  padding-right: 44px;
}

[dir="rtl"] .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon {
  /* @noflip */
  left: 12px;
  /* @noflip */
  right: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  left: auto;
  /* @noflip */
  right: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon {
  /* @noflip */
  right: 12px;
  /* @noflip */
  left: auto;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__icon ~ .mdc-text-field__icon[dir="rtl"] {
  /* @noflip */
  right: auto;
  /* @noflip */
  left: 12px;
}

.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 44px;
}

[dir="rtl"] .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input, .mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--dense .mdc-text-field__input[dir="rtl"] {
  /* @noflip */
  padding-left: 44px;
  /* @noflip */
  padding-right: 44px;
}

.mdc-text-field--dense .mdc-floating-label--float-above {
  -webkit-transform: translateY(-70%) scale(0.8);
          transform: translateY(-70%) scale(0.8);
}

.mdc-text-field--dense .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-dense 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-dense 250ms 1;
}

.mdc-text-field--dense .mdc-text-field__input {
  padding: 12px 12px 0;
}

.mdc-text-field--dense .mdc-floating-label {
  font-size: .813rem;
}

.mdc-text-field--dense .mdc-floating-label--float-above {
  font-size: .813rem;
}

.mdc-text-field__input:required ~ .mdc-floating-label::after,
.mdc-text-field__input:required ~ .mdc-notched-outline .mdc-floating-label::after {
  margin-left: 1px;
  content: "*";
}

.mdc-text-field--textarea {
  display: inline-flex;
  width: auto;
  height: auto;
  transition: none;
  overflow: visible;
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.24);
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.87);
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--textarea .mdc-floating-label--shake {
  -webkit-animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
          animation: mdc-floating-label-shake-float-above-text-field-outlined 250ms 1;
}

.mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading, .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__leading[dir="rtl"] {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

.mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing {
  /* @noflip */
  border-radius: 0 4px 4px 0;
}

[dir="rtl"] .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing, .mdc-text-field--textarea .mdc-notched-outline .mdc-notched-outline__trailing[dir="rtl"] {
  /* @noflip */
  border-radius: 4px 0 0 4px;
}

.mdc-text-field--textarea::before, .mdc-text-field--textarea::after {
  content: none;
}

.mdc-text-field--textarea:not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--textarea .mdc-floating-label--float-above {
  -webkit-transform: translateY(-144%) scale(1);
          transform: translateY(-144%) scale(1);
}

.mdc-text-field--textarea .mdc-floating-label--float-above {
  font-size: 0.75rem;
}

.mdc-text-field--textarea.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--textarea .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  -webkit-transform: translateY(-130%) scale(0.75);
          transform: translateY(-130%) scale(0.75);
}

.mdc-text-field--textarea.mdc-notched-outline--upgraded .mdc-floating-label--float-above,
.mdc-text-field--textarea .mdc-notched-outline--upgraded .mdc-floating-label--float-above {
  font-size: 1rem;
}

.mdc-text-field--textarea .mdc-text-field__input {
  align-self: auto;
  box-sizing: border-box;
  height: auto;
  margin: 8px 1px 1px 0;
  padding: 0 16px 16px;
  border: none;
}

.mdc-text-field--textarea .mdc-floating-label {
  top: 17px;
  bottom: auto;
  width: auto;
  pointer-events: none;
}

.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--focused .mdc-notched-outline__trailing {
  border-width: 2px;
}

.mdc-text-field--fullwidth {
  width: 100%;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea) {
  display: block;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea)::before, .mdc-text-field--fullwidth:not(.mdc-text-field--textarea)::after {
  content: none;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  background-color: transparent;
}

.mdc-text-field--fullwidth:not(.mdc-text-field--textarea) .mdc-text-field__input {
  padding: 0;
}

.mdc-text-field--fullwidth.mdc-text-field--textarea .mdc-text-field__input {
  resize: vertical;
}

.mdc-text-field--fullwidth.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--textarea) {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--dense + .mdc-text-field-helper-text {
  margin-bottom: 4px;
}

.mdc-text-field + .mdc-text-field-helper-text {
  margin-right: 12px;
  margin-left: 12px;
}

.mdc-text-field--outlined + .mdc-text-field-helper-text {
  margin-right: 16px;
  margin-left: 16px;
}

.mdc-form-field > .mdc-text-field + label {
  align-self: flex-start;
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: rgba(35, 46, 99, 0.87);
}

.mdc-text-field--focused .mdc-text-field__input:required ~ .mdc-floating-label::after,
.mdc-text-field--focused .mdc-text-field__input:required ~ .mdc-notched-outline .mdc-floating-label::after {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--focused + .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg) {
  opacity: 1;
}

.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #232E63;
  /* @alternate */
  border-color: var(--mdc-theme-primary, #232E63);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom-color: #b00020;
  /* @alternate */
  border-bottom-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple {
  background-color: #b00020;
  /* @alternate */
  background-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::-ms-input-placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--invalid + .mdc-text-field-helper-text--validation-msg {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid .mdc-text-field__input {
  caret-color: #b00020;
  /* @alternate */
  caret-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid.mdc-text-field--with-trailing-icon:not(.mdc-text-field--with-leading-icon):not(.mdc-text-field--disabled) .mdc-text-field__icon {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid.mdc-text-field--with-trailing-icon.mdc-text-field--with-leading-icon:not(.mdc-text-field--disabled) .mdc-text-field__icon ~ .mdc-text-field__icon {
  color: #b00020;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--invalid + .mdc-text-field-helper-text--validation-msg {
  opacity: 1;
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__input:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused) .mdc-text-field__icon:hover ~ .mdc-notched-outline .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #b00020;
  /* @alternate */
  border-color: var(--mdc-theme-error, #b00020);
}

.mdc-text-field--disabled {
  background-color: #fafafa;
  border-bottom: none;
  pointer-events: none;
}

.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--disabled .mdc-text-field__input {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-floating-label {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__input::placeholder {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled + .mdc-text-field-helper-text {
  color: rgba(0, 0, 0, 0.37);
}

.mdc-text-field--disabled .mdc-text-field__icon {
  color: rgba(0, 0, 0, 0.3);
}

.mdc-text-field--disabled:not(.mdc-text-field--textarea) {
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

.mdc-text-field--disabled .mdc-floating-label {
  cursor: default;
}

.mdc-text-field--textarea.mdc-text-field--disabled {
  background-color: transparent;
  background-color: #f9f9f9;
}

.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__leading,
.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__notch,
.mdc-text-field--textarea.mdc-text-field--disabled .mdc-notched-outline__trailing {
  border-color: rgba(0, 0, 0, 0.06);
}

.mdc-text-field--textarea.mdc-text-field--disabled .mdc-text-field__input {
  border-bottom: none;
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-70%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-70%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-70%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 0%)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 0%)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 0%)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense {
  0% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - 21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - 21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - 21px)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0)) translateY(-130%) scale(0.75);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
}

@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-dense-rtl {
  0% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(4% - -21px)) translateY(-120%) scale(0.8);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(-4% - -21px)) translateY(-120%) scale(0.8);
  }
  100% {
    -webkit-transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
            transform: translateX(calc(0 - -21px)) translateY(-120%) scale(0.8);
  }
}

@-webkit-keyframes mdc-floating-label-shake-float-above-textarea {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

@keyframes mdc-floating-label-shake-float-above-textarea {
  0% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
  33% {
    -webkit-animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
            animation-timing-function: cubic-bezier(0.5, 0, 0.70173, 0.49582);
    -webkit-transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(4% - 0%)) translateY(-130%) scale(0.75);
  }
  66% {
    -webkit-animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
            animation-timing-function: cubic-bezier(0.30244, 0.38135, 0.55, 0.95635);
    -webkit-transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(-4% - 0%)) translateY(-130%) scale(0.75);
  }
  100% {
    -webkit-transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
            transform: translateX(calc(0 - 0%)) translateY(-130%) scale(0.75);
  }
}

:root {
  --mdc-theme-primary: #232E63;
  --mdc-theme-secondary: #fff;
  --mdc-theme-background: #fff;
  --mdc-theme-surface: #fff;
  --mdc-theme-error: #b00020;
  --mdc-theme-on-primary: #fff;
  --mdc-theme-on-secondary: #fff;
  --mdc-theme-on-surface: #000;
  --mdc-theme-on-error: #fff;
  --mdc-theme-text-primary-on-background: rgba(0, 0, 0, 0.87);
  --mdc-theme-text-secondary-on-background: rgba(0, 0, 0, 0.54);
  --mdc-theme-text-hint-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-disabled-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-icon-on-background: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-primary-on-light: rgba(0, 0, 0, 0.87);
  --mdc-theme-text-secondary-on-light: rgba(0, 0, 0, 0.54);
  --mdc-theme-text-hint-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-disabled-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-icon-on-light: rgba(0, 0, 0, 0.38);
  --mdc-theme-text-primary-on-dark: white;
  --mdc-theme-text-secondary-on-dark: rgba(255, 255, 255, 0.7);
  --mdc-theme-text-hint-on-dark: rgba(255, 255, 255, 0.5);
  --mdc-theme-text-disabled-on-dark: rgba(255, 255, 255, 0.5);
  --mdc-theme-text-icon-on-dark: rgba(255, 255, 255, 0.5);
}

.mdc-theme--primary {
  color: #232E63 !important;
  /* @alternate */
  color: var(--mdc-theme-primary, #232E63) !important;
}

.mdc-theme--secondary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-secondary, #fff) !important;
}

.mdc-theme--background {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-background, #fff);
}

.mdc-theme--surface {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-theme-surface, #fff);
}

.mdc-theme--error {
  color: #b00020 !important;
  /* @alternate */
  color: var(--mdc-theme-error, #b00020) !important;
}

.mdc-theme--on-primary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-primary, #fff) !important;
}

.mdc-theme--on-secondary {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-secondary, #fff) !important;
}

.mdc-theme--on-surface {
  color: #000 !important;
  /* @alternate */
  color: var(--mdc-theme-on-surface, #000) !important;
}

.mdc-theme--on-error {
  color: #fff !important;
  /* @alternate */
  color: var(--mdc-theme-on-error, #fff) !important;
}

.mdc-theme--text-primary-on-background {
  color: rgba(0, 0, 0, 0.87) !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87)) !important;
}

.mdc-theme--text-secondary-on-background {
  color: rgba(0, 0, 0, 0.54) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-background, rgba(0, 0, 0, 0.54)) !important;
}

.mdc-theme--text-hint-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-disabled-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-icon-on-background {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-background, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-primary-on-light {
  color: rgba(0, 0, 0, 0.87) !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-light, rgba(0, 0, 0, 0.87)) !important;
}

.mdc-theme--text-secondary-on-light {
  color: rgba(0, 0, 0, 0.54) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-light, rgba(0, 0, 0, 0.54)) !important;
}

.mdc-theme--text-hint-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-disabled-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-icon-on-light {
  color: rgba(0, 0, 0, 0.38) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-light, rgba(0, 0, 0, 0.38)) !important;
}

.mdc-theme--text-primary-on-dark {
  color: white !important;
  /* @alternate */
  color: var(--mdc-theme-text-primary-on-dark, white) !important;
}

.mdc-theme--text-secondary-on-dark {
  color: rgba(255, 255, 255, 0.7) !important;
  /* @alternate */
  color: var(--mdc-theme-text-secondary-on-dark, rgba(255, 255, 255, 0.7)) !important;
}

.mdc-theme--text-hint-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-hint-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--text-disabled-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-disabled-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--text-icon-on-dark {
  color: rgba(255, 255, 255, 0.5) !important;
  /* @alternate */
  color: var(--mdc-theme-text-icon-on-dark, rgba(255, 255, 255, 0.5)) !important;
}

.mdc-theme--primary-bg {
  background-color: #232E63 !important;
  /* @alternate */
  background-color: var(--mdc-theme-primary, #232E63) !important;
}

.mdc-theme--secondary-bg {
  background-color: #fff !important;
  /* @alternate */
  background-color: var(--mdc-theme-secondary, #fff) !important;
}

.mdc-typography {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.mdc-typography--headline1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 6rem;
  line-height: 6rem;
  font-weight: 500;
  letter-spacing: -0.01562em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 3.75rem;
  line-height: 3.75rem;
  font-weight: 500;
  letter-spacing: -0.00833em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline3 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 3rem;
  line-height: 3.125rem;
  font-weight: 500;
  letter-spacing: normal;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline4 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 2.125rem;
  line-height: 2.5rem;
  font-weight: 500;
  letter-spacing: 0.00735em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline5 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: normal;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--headline6 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1.25rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: 0.0125em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--subtitle1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.75rem;
  font-weight: 500;
  letter-spacing: 0.00937em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--subtitle2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.375rem;
  font-weight: 500;
  letter-spacing: 0.00714em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--body1 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.03125em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--body2 {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.01786em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--caption {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  letter-spacing: 0.03333em;
  text-decoration: inherit;
  text-transform: inherit;
}

.mdc-typography--button {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.875rem;
  line-height: 2.25rem;
  font-weight: 500;
  letter-spacing: 0.08929em;
  text-decoration: none;
  text-transform: uppercase;
}

.mdc-typography--overline {
  font-family: "Montserrat", sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 0.75rem;
  line-height: 2rem;
  font-weight: 500;
  letter-spacing: 0.16667em;
  text-decoration: none;
  text-transform: uppercase;
}

/**
 * Général
 */
* {
  margin: 0px;
  padding: 0px;
}

@media (max-width: 767px) {
  html, body {
    -webkit-overflow-scrolling: touch !important;
    overflow: auto !important;
    height: 100% !important;
  }
}

body {
  min-height: 100vh;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #000;
  font-weight: 500;
  min-height: 100%;
  height: auto;
}

@media (min-width: 1024px) {
  body {
    display: flex;
    flex-direction: column;
  }
}

html {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
  font-weight: 400;
}

a {
  cursor: pointer;
  color: #3D50DF;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

a[target="_blank"] {
  position: relative;
  margin-right: 15px;
}

a[target="_blank"]:after {
  content: "";
  background-image: url("/admin/images/imports/lien-externe.svg");
  background-repeat: no-repeat;
  background-size: contain;
  position: absolute;
  width: 10px;
  height: 10px;
  right: -14px;
  top: calc(50% - 5px);
}

a[target="_blank"].button {
  padding-right: 35px;
}

a[target="_blank"].button:after {
  right: 20px;
}

#site-accessibility-menu {
  display: none !important;
}

#site-content-w {
  flex: 1 1 auto;
  overflow: hidden;
  display: flex;
}

/**
 * Firefox specific rule
 */
@-moz-document url-prefix() {
  body {
    font-weight: lighter !important;
  }
}

* {
  box-sizing: border-box;
}

/* Surcharge Police sur Google Maps */
.gm-style {
  font-family: 'Montserrat', sans-serif !important;
}

/* image de synchro */
img.sync {
  width: 16px;
  height: 16px;
  border: 0 !important;
  vertical-align: middle;
  margin-right: 2px;
}

h2 img.sync {
  border: 0 !important;
  width: 28px;
  height: 28px;
}

/* = Section En tête = */
.ria-admin-ui-header {
  display: flex;
  align-items: center;
  background-color: #232E63;
  color: #fff;
  padding: 18px 8px;
}

@media (min-width: 768px) {
  .ria-admin-ui-header {
    justify-content: space-between;
    padding: 18px 14px;
  }
}

/* == Colonne Bouton Menu == */
@media (min-width: 1024px) {
  .ria-admin-ui-toggle-menu {
    display: none;
  }
}

.ria-admin-ui-toggle-menu button {
  margin: 0;
  display: inline-block;
  border: 0 none;
  padding: 0;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  outline: none;
  background-color: transparent;
  border-radius: 5px;
}

.ria-admin-ui-toggle-menu button:hover {
  background-color: transparent;
}

.ria-admin-ui-toggle-menu button::before, .ria-admin-ui-toggle-menu button::after {
  display: none;
}

.ria-admin-ui-toggle-menu button img, .ria-admin-ui-toggle-menu button svg {
  width: 32px;
  height: 32px;
}

.ria-admin-ui-toggle-menu button:focus {
  box-shadow: none;
}

/* == Colonne Logo == */
.ria-admin-ui-logo-moto {
  padding-left: 8px;
}

@media (min-width: 768px) {
  .ria-admin-ui-logo-moto {
    padding-left: 15px;
    padding-right: 110px;
  }
}

/* === Widget Logo === */
.ria-admin-ui-logo {
  width: 92px;
}

.ria-admin-ui-logo img {
  height: 23px;
}

.ria-admin-ui-moto {
  font-size: 12px;
  font-weight: 400;
}

.ria-admin-ui-moto a, .ria-admin-ui-moto a:visited {
  color: #f8f8ff;
  text-decoration: none;
}

/* == Colonne Barre de recherche == */
.ria-admin-ui-searchbar {
  flex: 1 1 auto;
}

.ria-admin-ui-header .ria-admin-ui-searchbar {
  display: none;
}

@media (min-width: 768px) {
  .ria-admin-ui-header .ria-admin-ui-searchbar {
    display: block;
  }
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  height: 30px;
  background-color: #fff;
  border-radius: 20px;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input {
  padding-left: 24px;
  width: 100%;
  height: 100%;
  border: 0 none;
  max-width: none;
  font-size: 14px;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input:hover {
  border: 0 none;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
}

.ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__icon {
  background-color: transparent;
  border: 0 none;
  max-width: none;
  bottom: 3px;
}

.ria-admin-ui-searchbar .mdc-text-field--border {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.ria-admin-ui-searchbar .mdc-text-field--border.dark_border {
  border-color: #A9A9A9;
}

.ria-admin-ui-searchbar .mdc-text-field__icon {
  padding: 0;
  margin: 0;
}

.ria-admin-ui-searchbar .mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
  border-bottom: 0 none;
}

.ria-admin-ui-searchbar .mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
  border-bottom: 0 none;
}

/* == Colonne actions Mes options, Déconnexion == */
.ria-admin-ui-headactions {
  display: none;
}

@media (min-width: 1024px) {
  .ria-admin-ui-headactions {
    display: flex;
  }
}

.ria-admin-ui-headactions .mdc-button {
  height: 30px;
  font-size: 12px;
  font-weight: 400;
  padding: 0 20px;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: unset;
  position: relative;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active {
  color: #fff;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active::before, .ria-admin-ui-headactions .mdc-button.ria-button--active::after {
  position: absolute;
  width: auto;
  height: 100%;
  top: 0;
  right: 10px;
  left: 10px;
  bottom: 0;
  background-color: #5377FB;
  opacity: 1;
  border-radius: 5px;
  z-index: -1;
}

.ria-admin-ui-headactions .mdc-button.ria-button--active:hover {
  text-decoration: none;
}

/* Fil d'ariane, breadcrumbs emplacement en cours */
#site-location, .breadcrumb {
	font-size: 11px;
	margin-bottom: 15px;
	list-style-type: none;
}

.breadcrumb-item {
	display: inline;
	margin: 3px;
	margin-left: 0;
}

.breadcrumb-item::after {
	content: " >";
}
.breadcrumb-item.active::after {
	content: "";
}

/* Cache le h1 Riashop, et le timer */
h1, .page_timer {
  display: none !important;
}

.mdc-button:not(:disabled).ria-button--light {
  color: #fff;
}

.mdc-button:not(:disabled).ria-button--outline-light {
  color: #fff;
}

.mdc-button:not(:disabled).ria-button--outline-light:hover {
  background-color: #fff;
  color: #232E63;
  text-decoration: none;
}

.mdc-button:not(:disabled).ria-button--outline-dark:hover {
  background-color: #232E63;
  color: #fff;
  text-decoration: none;
}

.mdc-button:not(:disabled).ria-button--tti {
  text-transform: none;
}

.ria-button--outline-dark {
  border: 1px solid #232E63;
}

.ria-button--outline-light {
  border: 1px solid #fff;
}

.ria-button--bordered {
  border: 1px solid #fff;
}

/* Zone de contenu */
#site-content {
  margin-right: 10px;
  padding-top: 10px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  min-width: 0;
  min-height: 500px;
  flex: 1 1 auto;
}

@media (max-width: 767px) {
  #site-content {
    margin-left: 10px !important;
    height: auto !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  #site-content {
    margin-left: 20px !important;
    margin-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media (min-width: 1024px) {
  #site-content {
    margin-left: 200px;
    padding-left: 30px;
    margin-right: 30px;
    padding-top: 30px;
    padding-bottom: 30px;
    min-width: 726px;
    min-height: auto;
    border-left: 1px solid #A9A9A9;
  }
}

#site-content .mceToolbar .toolBarGroup, #site-content .defaultSkin .mceIframeContainer {
  box-shadow: none;
}

/* Copyright */
#site-footer {
  display: none;
}

#site-footer {
  display: block;
  font-size: 10px;
  padding: 2px;
  background-color: #DADCFF;
  color: #232E63;
  bottom: 0px;
  left: 0px;
  right: 0px;
  border-top: 1px solid #A9A9A9;
  z-index: 2;
}

@media (max-width: 1023px) {
  #site-footer {
    display: none;
  }
}

.ria-admin-ui-filters {
  display: flex;
  flex-wrap: wrap;
}

.ria-admin-ui-overview {
  margin: 0;
  clear: both;
}

.ria-admin-ui-actions {
  margin-top: 10px;
}

img {
  border-style: none;
  vertical-align: text-top;
}

/* images flèches */
.fleche {
  width: 16px;
  height: 8px;
}

.fleche-stats {
  width: 16px;
  height: 8px;
}

.fleche-move {
  width: 16px;
  height: 16px;
  border-style: medium none !important;
}

/* Lecture seule */
.readonly, input:-moz-read-only {
  color: #A9A9A9;
}
.readonly, input:read-only {
  color: #A9A9A9;
}

/* Boutons de suppression */
img.icon-del-cat {
  border: 0 !important;
}

a.del-link, .del-link a, #pmt-special input.cdt-grp-del:not(.button-del-group), input.input-icon-del {
  background-image: url("/admin/images/cut_inactive.svg");
  background-repeat: no-repeat;
  background-color: transparent !important;
  color: transparent !important;
  display: block;
  height: 23px;
  width: 23px;
  padding: 0;
  border: 0 !important;
  cursor: pointer;
}

a.del-link:hover, .del-link a:hover, #pmt-special input.cdt-grp-del:not(.button-del-group):hover, input.input-icon-del:hover {
  background-image: url("/admin/images/cut_rollover.svg");
  height: 23px;
  width: 23px;
}

/* Sélecteurs */
.riapicker .selectorview {
  position: relative;
}

.riapicker .selectorview .left {
  width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis !important;
}

.riapicker .selectorview a.btn {
  position: absolute;
  right: 5px;
}

.riapicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

/* Zone de contenu */
h2 {
  margin-bottom: 10px;
  font-size: 22px;
  padding-bottom: 4px;
  color: #232E63;
  word-wrap: break-word;
}

/**
 * Règles CSS générales pour la version imprimable
 */
@media screen {
  .print-only {
    display: none !important;
  }
}

@media print {
  @page {
    margin: 15mm 5mm;
  }
  html, body {
    background: #fff;
    overflow: visible !important;
  }
  .print-none {
    display: none !important;
  }
  .print-table {
    display: table !important;
  }
  .print-table thead {
    display: table-header-group !important;
  }
  .print-table tbody {
    display: table-row-group !important;
  }
  .print-table tfoot {
    display: table-footer-group !important;
  }
  .print-table tr {
    display: table-row !important;
  }
  .print-table td, .print-table th, .print-table th.thead-none {
    display: table-cell !important;
  }
  .print-table td.print-none, .print-table th.print-none, .print-table th.thead-none.print-none {
    display: none !important;
  }
  .print-table td[data-label]::before, .print-table th[data-label]::before {
    content: '' !important;
  }
  body {
    font-size: 11px;
  }
  #site-content #tabpanel {
    padding: 0;
    border-style: none;
  }
  #site-content {
    margin: 0;
    padding: 0;
  }
  #site-content table thead th {
    border-bottom: 1px solid #000;
  }
  #site-content table tbody th {
    border-bottom: 1px solid #A9A9A9;
  }
  a {
    color: #000;
    text-decoration: none;
  }
  [type="button"], [type="submit"], .button {
    display: none !important;
  }
  select, [type="text"], textarea {
    border: none !important;
    vertical-align: middle !important;
  }
  input, input:focus, select, select:focus, textarea, textarea:focus, .input-edit-success, .input-edit-success:focus {
    box-shadow: none !important;
    border: none;
  }
  textarea {
    resize: none !important;
    height: auto;
  }
}

/**
 * Classes d'aide générale
 */
.none {
  display: none !important;
}

.align-center {
  text-align: center !important;
}

.align-right {
  text-align: right !important;
}

.align-left {
  text-align: left !important;
}

.valign-center, #form-delivery-options dl > dd input {
  vertical-align: middle !important;
}

.width-auto {
  width: auto !important;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.float-none {
  float: none !important;
}

.no-border {
  border: 0 !important;
}

.color-red {
  color: #ff0000 !important;
}

.large {
  width: 100%;
}

.clear-right {
  clear: right;
}

.clear-left {
  clear: left;
}

.clear-both {
  clear: both;
}

.display-flex {
  display: flex;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.uppercase {
  text-transform: uppercase;
}

.bg-white {
  background-color: #fff !important;
}

.border-none {
  border: medium none;
}

.margin-top-10 {
  margin-top: 10px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-bottom-10 {
  padding-bottom: 10px !important;
}

.spacing-top {
  margin-top: 15px;
}

.div-padding-15 {
  padding: 15px;
}

.bold, .bold tr td, .bold td {
  font-weight: 600;
}

.hide {
  display: none;
}

.nonSelectionnable {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.info-fld {
  color: #A9A9A9;
  font-size: 10px;
  padding-left: 5px;
}

td.number {
  text-align: right !important;
  padding-right: 5px !important;
}

.hide-mobile {
  display: block;
}

@media (max-width: 1023px) {
  .hide-mobile {
    display: none !important;
  }
}

/* Personalisations des champs standards */
/* Inputs */
input[type="color"],
input[type="date"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="text"],
input[type="time"],
input[type="url"],
input[type="week"],
select,
textarea {
  background-color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  width: 400px;
  max-width: 400px;
  border: 1px solid #A9A9A9;
  border-radius: 6px;
  margin-top: 2px;
  margin-bottom: 2px;
  padding: 0 4px;
}

input[type="color"]  ,
input[type="date"]  ,
input[type="datetime-local"]  ,
input[type="email"]  ,
input[type="month"]  ,
input[type="number"]  ,
input[type="password"]  ,
input[type="search"]  ,
input[type="tel"]  ,
input[type="text"]  ,
input[type="time"]  ,
input[type="url"]  ,
input[type="week"]  ,
select  ,
textarea  {
  vertical-align: top;
}

@media (max-width: 767px) {
  input[type="color"],
  input[type="date"],
  input[type="datetime-local"],
  input[type="email"],
  input[type="month"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="text"],
  input[type="time"],
  input[type="url"],
  input[type="week"],
  select,
  textarea {
    width: 100%;
  }
}

thead input[type="color"], tfoot input[type="color"], thead
input[type="date"], tfoot
input[type="date"], thead
input[type="datetime-local"], tfoot
input[type="datetime-local"], thead
input[type="email"], tfoot
input[type="email"], thead
input[type="month"], tfoot
input[type="month"], thead
input[type="number"], tfoot
input[type="number"], thead
input[type="password"], tfoot
input[type="password"], thead
input[type="search"], tfoot
input[type="search"], thead
input[type="tel"], tfoot
input[type="tel"], thead
input[type="text"], tfoot
input[type="text"], thead
input[type="time"], tfoot
input[type="time"], thead
input[type="url"], tfoot
input[type="url"], thead
input[type="week"], tfoot
input[type="week"], thead
select, tfoot
select, thead
textarea, tfoot
textarea {
  width: auto;
}

input[type="color"]:hover,
input[type="date"]:hover,
input[type="datetime-local"]:hover,
input[type="email"]:hover,
input[type="month"]:hover,
input[type="number"]:hover,
input[type="password"]:hover,
input[type="search"]:hover,
input[type="tel"]:hover,
input[type="text"]:hover,
input[type="time"]:hover,
input[type="url"]:hover,
input[type="week"]:hover,
select:hover,
textarea:hover {
  border: 1px solid #232E63;
}

input[type="color"]:focus:not(.mdc-text-field__search-input),
input[type="date"]:focus:not(.mdc-text-field__search-input),
input[type="datetime-local"]:focus:not(.mdc-text-field__search-input),
input[type="email"]:focus:not(.mdc-text-field__search-input),
input[type="month"]:focus:not(.mdc-text-field__search-input),
input[type="number"]:focus:not(.mdc-text-field__search-input),
input[type="password"]:focus:not(.mdc-text-field__search-input),
input[type="search"]:focus:not(.mdc-text-field__search-input),
input[type="tel"]:focus:not(.mdc-text-field__search-input),
input[type="text"]:focus:not(.mdc-text-field__search-input),
input[type="time"]:focus:not(.mdc-text-field__search-input),
input[type="url"]:focus:not(.mdc-text-field__search-input),
input[type="week"]:focus:not(.mdc-text-field__search-input),
select:focus:not(.mdc-text-field__search-input),
textarea:focus:not(.mdc-text-field__search-input) {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="color"].input-edit-success,
input[type="date"].input-edit-success,
input[type="datetime-local"].input-edit-success,
input[type="email"].input-edit-success,
input[type="month"].input-edit-success,
input[type="number"].input-edit-success,
input[type="password"].input-edit-success,
input[type="search"].input-edit-success,
input[type="tel"].input-edit-success,
input[type="text"].input-edit-success,
input[type="time"].input-edit-success,
input[type="url"].input-edit-success,
input[type="week"].input-edit-success,
select.input-edit-success,
textarea.input-edit-success {
  border: 1px solid #7fcc7f;
  box-shadow: 0px 0px 3px #7fcc7f;
}

input[type="color"].input-edit-error,
input[type="date"].input-edit-error,
input[type="datetime-local"].input-edit-error,
input[type="email"].input-edit-error,
input[type="month"].input-edit-error,
input[type="number"].input-edit-error,
input[type="password"].input-edit-error,
input[type="search"].input-edit-error,
input[type="tel"].input-edit-error,
input[type="text"].input-edit-error,
input[type="time"].input-edit-error,
input[type="url"].input-edit-error,
input[type="week"].input-edit-error,
select.input-edit-error,
textarea.input-edit-error {
  border: 1px solid #f99494;
  box-shadow: 0px 0px 3px #f99494;
}

input[type="color"]:disabled,
input[type="date"]:disabled,
input[type="datetime-local"]:disabled,
input[type="email"]:disabled,
input[type="month"]:disabled,
input[type="number"]:disabled,
input[type="password"]:disabled,
input[type="search"]:disabled,
input[type="tel"]:disabled,
input[type="text"]:disabled,
input[type="time"]:disabled,
input[type="url"]:disabled,
input[type="week"]:disabled,
select:disabled,
textarea:disabled {
  background-color: #EEEEEE;
}

input[type="color"]:-moz-read-only,
input[type="date"]:-moz-read-only,
input[type="datetime-local"]:-moz-read-only,
input[type="email"]:-moz-read-only,
input[type="month"]:-moz-read-only,
input[type="number"]:-moz-read-only,
input[type="password"]:-moz-read-only,
input[type="search"]:-moz-read-only,
input[type="tel"]:-moz-read-only,
input[type="text"]:-moz-read-only,
input[type="time"]:-moz-read-only,
input[type="url"]:-moz-read-only,
input[type="week"]:-moz-read-only,
select:-moz-read-only,
textarea:-moz-read-only {
  background-color: #f8f8f8;
}

input[type="color"]:read-only,
input[type="date"]:read-only,
input[type="datetime-local"]:read-only,
input[type="email"]:read-only,
input[type="month"]:read-only,
input[type="number"]:read-only,
input[type="password"]:read-only,
input[type="search"]:read-only,
input[type="tel"]:read-only,
input[type="text"]:read-only,
input[type="time"]:read-only,
input[type="url"]:read-only,
input[type="week"]:read-only,
select:read-only,
textarea:read-only {
  background-color: #f8f8f8;
}

dd input[type="color"],
dd input[type="date"],
dd input[type="datetime-local"],
dd input[type="email"],
dd input[type="month"],
dd input[type="password"],
dd input[type="search"],
dd input[type="tel"],
dd input[type="text"],
dd input[type="time"],
dd input[type="url"],
dd input[type="week"],
dd select,
dd textarea {
  width: auto !important;
  max-width: 100%;
}

#devis-logo-disposition {
  width: auto !important;
}
#devis-font-size, #inv-font-size {
	width: 145px !important;
}

tr > td > input:not([type="submit"]):not([type="button"]):not([type="image"]):only-child,
div:not(#popup-content) tr > td > select:only-child {
  width: 100%;
}

label + input[type='text'], label + input[type='number'], dd select, input.small {
  vertical-align: middle !important;
}

/* #mdc-input:focus, #mdc-text-field__icon:focus, #ord-ref:focus {
 	border: none !important;
 	box-shadow: none !important;
 } */
input[type="file"]::-ms-value {
  background-color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  width: 100%;
  max-width: 400px;
  border: 1px solid #A9A9A9;
  border-radius: 6px;
  margin-top: 2px;
  margin-bottom: 2px;
  vertical-align: middle;
  padding: 0 4px;
}

input[type="file"]::-ms-value:hover {
  border: 1px solid #232E63;
}

input[type="file"]::-ms-value:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="file"]::-ms-value.input-edit-success {
  border: 1px solid #7fcc7f;
  box-shadow: 0px 0px 3px #7fcc7f;
}

input[type="file"]::-ms-value.input-edit-error {
  border: 1px solid #f99494;
  box-shadow: 0px 0px 3px #f99494;
}

input[type="file"]::-ms-value:read-only {
  background-color: white;
}

input[type="file"]::-ms-value:disabled {
  background-color: #EEEEEE;
}

input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not(.mdc-text-field__input), select:not([multiple]) {
  height: 24px;
}

input[type="file"]::-ms-value {
  height: 24px;
  box-sizing: border-box;
}

input[type="text"].date {
  min-width: auto;
}

/* Textarea */
textarea {
  padding: 4px;
  max-width: 100%;
}

textarea#desc {
  min-height: 100px;
}

/* Select */
select:-moz-read-only {
  background-color: white;
}
select:read-only {
  background-color: white;
}

select:disabled {
  background-color: #EEEEEE;
}

select:not([multiple]) {
  background-image: url("/admin/dist/images/input-select.svg");
  background-position: right -1px top -4px;
  background-repeat: no-repeat;
  background-size: 29px 29px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding-right: 30px !important;
}

select:not([multiple])::-ms-expand {
  display: none;
}

/* Boutons */
input[type="button"]:not(.cdt-grp-del),
input[type="reset"],
input[type="submit"],
button,
.button {
  display: inline-block;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  margin-top: 2px;
  margin-bottom: 2px;
  vertical-align: middle;
  min-height: 24px;
}

input[type="button"]:not(.cdt-grp-del):hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
button:hover,
.button:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none !important;
}

input[type="button"]:not(.cdt-grp-del):disabled,
input[type="reset"]:disabled,
input[type="submit"]:disabled,
button:disabled,
.button:disabled {
  cursor: default;
  background-color: #EEEEEE !important;
  color: #A9A9A9 !important;
  border-color: #A9A9A9 !important;
}

input[type="button"]:not(.cdt-grp-del):disabled:hover,
input[type="reset"]:disabled:hover,
input[type="submit"]:disabled:hover,
button:disabled:hover,
.button:disabled:hover {
  background-color: #EEEEEE;
  color: #A9A9A9;
  border-color: #A9A9A9;
}

input[type="button"]:not(.cdt-grp-del):focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button:focus,
.button:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="button"]:not(.cdt-grp-del).button-secondary,
input[type="reset"].button-secondary,
input[type="submit"].button-secondary,
button.button-secondary,
.button.button-secondary {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
}

/* Exception au focus pour les inputs du sous-menu de commandes */
#site-submenu .ria-admin-ui-searchbar input:focus, #site-submenu .ria-admin-ui-searchbar button:focus {
  border: none;
  box-shadow: none;
}

/* Number */
input[type="number"] {
  width: 50px;
}

/* Checkbox, Radio */
input[type="checkbox"],
input[type="radio"] {
  background-color: #fff;
  border: 1px solid #A9A9A9;
  vertical-align: middle;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 14px !important;
  height: 14px;
  display: inline-block;
}

input[type="checkbox"]:-moz-read-only,
input[type="radio"]:-moz-read-only {
  background-color: white;
}

input[type="checkbox"]:read-only,
input[type="radio"]:read-only {
  background-color: white;
}

input[type="checkbox"]:disabled,
input[type="radio"]:disabled {
  background-color: #EEEEEE;
}

input[type="checkbox"]:hover,
input[type="radio"]:hover {
  border: 1px solid #232E63;
}

input[type="checkbox"]:focus,
input[type="radio"]:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

input[type="checkbox"] {
  border-radius: 3px;
}

input[type="checkbox"]:checked {
  background-image: url("/admin/dist/images/checkbox-checked.svg");
}

input[type="checkbox"]:indeterminate {
  background-clip: content-box;
  padding: 2px;
  background-color: #5377FB;
}

input[type="radio"] {
  border-radius: 100%;
}

input[type="radio"]:checked {
  background-image: radial-gradient(ellipse at center, #5377FB 50%, #fff 50%);
}

/* == Input file == */
/* Surcharge pour moteur de rendu Webkit, Blink */
input[type="file"] {
  font-family: 'Montserrat', sans-serif;
}

input[type="file"]::-webkit-file-upload-button {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  -webkit-appearance: none;
          appearance: none;
  vertical-align: middle;
  min-height: 24px;
}

input[type="file"]::-webkit-file-upload-button:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none;
}

input[type="file"]::-webkit-file-upload-button:disabled {
  cursor: default;
  background-color: #EEEEEE;
}

input[type="file"]::-webkit-file-upload-button:disabled:hover {
  background-color: #EEEEEE;
  color: #232E63;
}

input[type="file"]::-webkit-file-upload-button:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

/* Surcharge pour moteur de rendu Edge */
input[type="file"]::-ms-browse {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #232E63;
  color: #232E63;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 6px;
  text-decoration: none;
  font-style: normal;
  appearance: none;
  vertical-align: middle;
  min-height: 24px;
}

input[type="file"]::-ms-browse:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none;
}

input[type="file"]::-ms-browse[disabled] {
  cursor: default;
  background-color: #EEEEEE;
}

input[type="file"]::-ms-browse[disabled]:hover {
  background-color: #EEEEEE;
  color: #232E63;
}

input[type="file"]::-ms-browse:focus {
  outline: 0 none;
  border: 1px solid #232E63;
  box-shadow: 0 0 1px 1px #5377FB;
}

#site-content fieldset div input:not(.cdt-grp-del),
#site-content fieldset div textarea,
#site-content #tabpanel-vertical div input,
#site-content #tabpanel-vertical div textarea {
  display: inline-block;
}

label {
  vertical-align: middle;
  cursor: default;
}

/* pour les éléments de type label > input + texteLabel + input */
.nested-inputs {
  display: flex;
  align-items: center;
  /* & > input:first-child {
		flex: 1 1 auto;
	} */
}

@media (max-width: 767px) {
  .nested-inputs {
    display: inline-block;
  }
}

.nested-inputs > * {
  margin-right: 5px;
}

.nested-inputs > input:last-child {
  margin-left: 5px;
}

input.icon-del-cat {
  vertical-align: middle;
  margin-top: -3px;
}

.cdt-grp-legend label {
  display: inline-block;
}

.cdt-grp-legend input {
  display: inline-block !important;
}

#site-content .links .cnt-infos .del-link {
  float: left;
  height: 23px;
  width: 23px;
  margin-right: 5px;
}

.little_input {
  width: 150px;
}

.auto_input {
  width: auto;
}

.rec-new-fld {
  width: 220px;
}

/* Tabstrip */
.tabstrip {
  list-style-type: none;
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #232E63;
}

.tabstrip li {
  margin: 0;
  padding: 0;
  border-top: 1px solid #232E63;
  border-bottom: 1px solid #232E63;
  border: 1px solid #232E63;
  background-color: #fff;
  border-top-right-radius: 10px;
  position: relative;
  margin-right: -10px;
  overflow: hidden;
  margin-bottom: -1px;
}

.tabstrip li:first-child > input[type="button"] {
  padding-left: 14px;
}

.tabstrip li > *,
.tabstrip li > input[type="button"] {
  border-style: none;
  background-color: transparent;
  margin: 0 !important;
  padding: 0 14px 0 24px;
  background-repeat: no-repeat;
  border-radius: 0;
  display: block;
  line-height: 24px;
  color: #232E63;
  width: 100%;
  font-size: 12px;
}

.tabstrip li > *:hover,
.tabstrip li > input[type="button"]:hover {
  background-color: #DADCFF;
  color: #232E63;
  text-decoration: none;
}

.tabstrip li > *.selected,
.tabstrip li > input[type="button"].selected {
  text-decoration: none;
  background-color: #5377FB;
  color: #fff;
  cursor: default;
}

.tabstrip li > *.selected:focus,
.tabstrip li > input[type="button"].selected:focus {
  background-color: #5377FB;
  color: #fff;
}

.tabstrip li > *:focus,
.tabstrip li > input[type="button"]:focus {
  border: 0 none;
  background-color: #DADCFF;
}

.tabstrip li.selected {
  background-color: #5377FB;
}

.tabstrip li.selected > * {
  color: #fff;
}

.tabstrip li.selected > *:hover {
  background-color: #5377FB;
  text-decoration: none;
}

.tabstrip li.selected > *:focus {
  border: 0 none;
  background-color: #5377FB;
}

.tabstrip li:nth-child(1) {
  z-index: 29;
}

.tabstrip li:nth-child(2) {
  z-index: 28;
}

.tabstrip li:nth-child(3) {
  z-index: 27;
}

.tabstrip li:nth-child(4) {
  z-index: 26;
}

.tabstrip li:nth-child(5) {
  z-index: 25;
}

.tabstrip li:nth-child(6) {
  z-index: 24;
}

.tabstrip li:nth-child(7) {
  z-index: 23;
}

.tabstrip li:nth-child(8) {
  z-index: 22;
}

.tabstrip li:nth-child(9) {
  z-index: 21;
}

.tabstrip li:nth-child(10) {
  z-index: 20;
}

.tabstrip li:nth-child(11) {
  z-index: 19;
}

.tabstrip li:nth-child(12) {
  z-index: 18;
}

.tabstrip li:nth-child(13) {
  z-index: 17;
}

.tabstrip li:nth-child(14) {
  z-index: 16;
}

.tabstrip li:nth-child(15) {
  z-index: 15;
}

.tabstrip li:nth-child(16) {
  z-index: 14;
}

.tabstrip li:nth-child(17) {
  z-index: 13;
}

.tabstrip li:nth-child(18) {
  z-index: 12;
}

.tabstrip li:nth-child(19) {
  z-index: 11;
}

.tabstrip li:nth-child(20) {
  z-index: 10;
}

.tabstrip li:nth-child(21) {
  z-index: 9;
}

.tabstrip li:nth-child(22) {
  z-index: 8;
}

.tabstrip li:nth-child(23) {
  z-index: 7;
}

.tabstrip li:nth-child(24) {
  z-index: 6;
}

.tabstrip li:nth-child(25) {
  z-index: 5;
}

.tabstrip li:nth-child(26) {
  z-index: 4;
}

.tabstrip li:nth-child(27) {
  z-index: 3;
}

.tabstrip li:nth-child(28) {
  z-index: 2;
}

.tabstrip li:nth-child(29) {
  z-index: 1;
}

.tabstrip li:nth-child(30) {
  z-index: 0;
}

.tabstrip li:first-child > * {
  padding-left: 14px;
}

.tabstrip-vertical {
  list-style-type: none;
  margin: 0px;
  padding: 0px;
}

.tabstrip-vertical li {
  margin: 0px;
  padding: 0px;
  border-top: 1px solid #232E63;
  border-bottom: 1px solid #232E63;
  border: 1px solid #232E63;
  background-color: #fff;
  overflow: hidden;
  margin-bottom: -1px;
}

.tabstrip-vertical li:nth-child(1) {
  z-index: 29;
}

.tabstrip-vertical li:nth-child(2) {
  z-index: 28;
}

.tabstrip-vertical li:nth-child(3) {
  z-index: 27;
}

.tabstrip-vertical li:nth-child(4) {
  z-index: 26;
}

.tabstrip-vertical li:nth-child(5) {
  z-index: 25;
}

.tabstrip-vertical li:nth-child(6) {
  z-index: 24;
}

.tabstrip-vertical li:nth-child(7) {
  z-index: 23;
}

.tabstrip-vertical li:nth-child(8) {
  z-index: 22;
}

.tabstrip-vertical li:nth-child(9) {
  z-index: 21;
}

.tabstrip-vertical li:nth-child(10) {
  z-index: 20;
}

.tabstrip-vertical li:nth-child(11) {
  z-index: 19;
}

.tabstrip-vertical li:nth-child(12) {
  z-index: 18;
}

.tabstrip-vertical li:nth-child(13) {
  z-index: 17;
}

.tabstrip-vertical li:nth-child(14) {
  z-index: 16;
}

.tabstrip-vertical li:nth-child(15) {
  z-index: 15;
}

.tabstrip-vertical li:nth-child(16) {
  z-index: 14;
}

.tabstrip-vertical li:nth-child(17) {
  z-index: 13;
}

.tabstrip-vertical li:nth-child(18) {
  z-index: 12;
}

.tabstrip-vertical li:nth-child(19) {
  z-index: 11;
}

.tabstrip-vertical li:nth-child(20) {
  z-index: 10;
}

.tabstrip-vertical li:nth-child(21) {
  z-index: 9;
}

.tabstrip-vertical li:nth-child(22) {
  z-index: 8;
}

.tabstrip-vertical li:nth-child(23) {
  z-index: 7;
}

.tabstrip-vertical li:nth-child(24) {
  z-index: 6;
}

.tabstrip-vertical li:nth-child(25) {
  z-index: 5;
}

.tabstrip-vertical li:nth-child(26) {
  z-index: 4;
}

.tabstrip-vertical li:nth-child(27) {
  z-index: 3;
}

.tabstrip-vertical li:nth-child(28) {
  z-index: 2;
}

.tabstrip-vertical li:nth-child(29) {
  z-index: 1;
}

.tabstrip-vertical li:nth-child(30) {
  z-index: 0;
}

.tabstrip-vertical li > * {
  border-style: none;
  background-color: transparent;
  margin: 0 0 -1px !important;
  padding: 6px 10px;
  background-repeat: no-repeat;
  border-radius: 0;
  display: block;
  color: #232E63;
  width: 100%;
  height: auto !important;
  text-align: left;
  margin-bottom: -1px;
}

.tabstrip-vertical li > *:hover {
  background-color: #DADCFF;
  color: #232E63;
  text-decoration: none;
}

.tabstrip-vertical li > *.selected {
  text-decoration: none;
  background-color: #5377FB;
  color: #fff;
}

.tabstrip-vertical li > *:focus {
  border: 0 none;
  background-color: #DADCFF;
}

#tabstrip-vertical-container {
  min-width: 65%;
}

@media (min-width: 1024px) {
  #tabstrip-vertical-container {
    display: flex;
  }
}

#popup-content.tabs .tabstrip li:first-child {
  margin-left: 10px;
}

#popup-content.tabs .tabscontent {
  padding: 10px 15px;
}

#tabpanel {
  border: 1px solid #232E63;
  border-top-style: none;
  padding: 5px;
  clear: both;
}

#tabpanel > .lng-menu {
  margin-top: 9px;
}

#tabpanel-vertical {
  margin-left: -1px;
  border: 1px solid #232E63;
  padding: 12px 12px 12px 32px;
}

@media (max-width: 767px) {
  #tabpanel-vertical {
    padding: 12px;
  }
}

#site-content #tabpanel-vertical p {
  margin-bottom: 12px;
}

/* fieldsets */
#site-content #tabpanel-vertical div input.checkbox {
  width: auto;
}

#site-content #tabpanel-vertical div {
  padding: 3px;
}

#site-content #tabpanel-vertical div sub {
  padding-left: 123px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div sub {
    padding-left: 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  #site-content #tabpanel-vertical div sub {
    padding-left: 0;
  }
}

#site-content #tabpanel-vertical label,
#site-content #tabpanel-vertical div span.label {
  display: inline-block;
  width: 118px;
  text-align: left;
  padding-right: 5px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical label,
  #site-content #tabpanel-vertical div span.label {
    display: block;
    width: 100%;
    text-align: left;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  #site-content #tabpanel-vertical label,
  #site-content #tabpanel-vertical div span.label {
    text-align: left;
  }
}

#site-content #tabpanel-vertical div label.inline {
  display: inline;
  float: none;
}

#site-content #tabpanel-vertical div.checkbox {
  padding-left: 195px;
}

#site-content #tabpanel-vertical div input, #site-content #tabpanel-vertical div textarea {
  width: 345px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div input, #site-content #tabpanel-vertical div textarea {
    width: 100%;
  }
}

#site-content #tabpanel-vertical div.actions input {
  width: auto;
}

#site-content #tabpanel-vertical textarea {
  display: block;
  width: 490px;
}

#site-content #tabpanel-vertical div.actions {
  padding-left: 123px;
}

@media (max-width: 767px) {
  #site-content #tabpanel-vertical div.actions {
    padding-left: 0;
  }
}

/* Ensembles des messages d'information, warning, erreurs et retour d'événements. */
.error {
  color: #ff0000;
  background-color: #ffdddd;
  border-color: #ff0000;
}

.notice-default {
  background-color: #fffbcc;
  border-color: #e6db55;
}

.success {
  color: #008000;
  background-color: #DDFFDD;
  border-color: #008000;
}

.success p {
  padding: 0 !important;
}

.error-success {
  color: #008000;
  background-color: #ebffeb;
  border-color: #008000;
}

.notice, .notice-default, .error, .error-success, .success {
  display: block;
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 5px;
  border-style: solid;
  border-width: 1px;
}

.notice > *:last-child, .notice-default > *:last-child, .error > *:last-child, .error-success > *:last-child, .success > *:last-child {
  margin-bottom: 0;
}

.notice b, .notice-default b, .error b, .error-success b, .success b {
  font-size: 600;
}

.notice label, .notice-default label, .error label, .error-success label, .success label {
  font-weight: 600;
}

.notice select, .notice-default select, .error select, .error-success select, .success select {
  width: auto;
  margin-left: 45px;
}

.notice .more, .notice-default .more, .error .more, .error-success .more, .success .more {
  display: block;
  text-decoration: underline;
}

.notice .more-info,
.notice .more-hide, .notice-default .more-info,
.notice-default .more-hide, .error .more-info,
.error .more-hide, .error-success .more-info,
.error-success .more-hide, .success .more-info,
.success .more-hide {
  display: none;
}

.notice.inline-block, .notice-default.inline-block, .error.inline-block, .error-success.inline-block, .success.inline-block {
  display: inline-block;
  margin: 0;
}

.notice {
  position: relative;
  background: #E3F3FC;
  border: 1px solid #ACE1FD;
  border-radius: 4px;
  color: #3D50DF;
  padding-left: 28px;
}

.notice:before {
  position: absolute;
  content: "";
  top: 50%;
  margin-top: -12px;
  left: 2px;
  width: 24px;
  height: 24px;
  background-image: url("/admin/images/info-light.svg");
  background-size: contain;
  background-repeat: no-repeat;
}

.notice a {
  text-decoration: underline;
}

.notice a:hover {
  text-decoration: none;
}

.notice.header:before {
  background: none;
}

.notice-msg {
  margin-top: 10px;
}

.notice-default p {
  text-align: left;
  margin-left: 165px;
}

table .error {
  color: #ff0000 !important;
  background-color: #fff !important;
  border-style: none;
  margin: 0;
  padding: 0 !important;
}

tr.error, th.error {
  display: table-row;
}

.load-ajax-opacity {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  z-index: 9998;
  display: none;
}

.message-ajax-opacity {
  display: none;
  z-index: 9999;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@media (max-width: 424px) {
  .notice-default .select-client {
    vertical-align: middle;
  }
  .notice-default .select-client, .notice-default p {
    margin-left: 0 !important;
  }
}

/**
 * CSS des fichiers catalog/nomenclature.php et catalog/edit-nomenclature.php
 */
#table-nomenclatures-variables {
  width: 500px;
}

#table-nomenclatures-variables .td-with-checkbox {
  width: 25px;
}

#table-nomenclatures-variables .td-without-checkbox {
  width: 400px;
}

#table-nomenclatures-variables tfoot label {
  margin-right: 0.5rem;
}

#table-propriete-emplacement #thname {
  width: 170px;
}

@media (max-width: 320px) {
  #table-propriete-emplacement #thname {
    font-size: 10px;
  }
}

#table-propriete-emplacement #td-input-name {
  width: 330px;
}

#table-produits-utilisables #td-produits-1 label, #table-produits-utilisables #td-produits-2 label {
  margin-right: 0.5rem;
}

#table-produits-utilisables #td-produits-1 {
  vertical-align: bottom;
  width: 170px;
}

#table-produits-utilisables #td-produits-2 {
  width: 330px;
}

#table-produits-utilisables select {
  max-width: 500px;
}

/**
 * CSS des fichiers ./catalog/discount/index.php
 * et ./view.admin.inc.php (fonction view_state_periodpicker)
 */
/* Tableau de Tarifs conditionnels pour le catalogue */
#lst-prices #sync {
  width: 35px;
}

#lst-prices #information {
  width: 400px;
}

#lst-prices #conditions-prc {
  width: 650px;
}

#lst-prices #action-prc {
  width: 110px;
}

/* Tableau TVA */
#grps-tva {
  width: 760px;
}

#grps-tva #th-grps-tva-1 {
  width: 670px;
}

#grps-tva #th-grps-tva-2 {
  width: 90px;
}

/**
 * CSS des fichiers ./catalog/authorizations/index.php
 * et ./view.admin.inc.php (fonction view_websites_selector)
 */
/* sélecteur de période pour les tarifs */
.riawebsitepicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

#table-authorizations {
  margin-top: 10px;
}

#table-new-autorization #td-new-autorization {
  width: 150px;
}

#table-conditions-authorization {
  width: 100%;
  max-width: 800px;
}

#table-conditions-authorization #rec-fld {
  width: 305px;
}

#table-conditions-authorization #rec-symbol {
  width: 180px;
}

#table-conditions-authorization #rec-none {
  width: 20px;
}

#table-conditions-authorization .tr-rec-new-fld td.td-widthout-et select.rec-new-fld {
  width: 100% !important;
  margin-left: 0;
}

#table-conditions-authorization .tr-rec-new-fld td.td-width-et {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#table-conditions-authorization .tr-rec-new-fld td.td-width-et select {
  width: 270px !important;
}

#table-conditions-authorization .span-valeur {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#table-conditions-authorization .span-valeur .input-value-condition {
  width: 160px !important;
}

/* Droit d'accès */
#tb-authorization-edit {
  width: 100%;
  max-width: 100%;
  /* RESPONSIVE */
}

#tb-authorization-edit thead th:first-child {
  width: 20px;
}

#tb-authorization-edit thead th:nth-child(3) {
  width: 200px;
}

#tb-authorization-edit thead th:nth-child(4) {
  width: 180px;
}

#tb-authorization-edit thead th:nth-child(5) {
  width: 250px;
}

#tb-authorization-edit thead th:last-child {
  width: 180px;
}

#tb-authorization-edit td.td-liste-et {
  width: 30px;
}

#tb-authorization-edit td.td-widthout-et select.rec-new-fld {
  text-align: left;
  margin-left: 0;
}

#tb-authorization-edit td.td-width-et {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#tb-authorization-edit td.td-width-et .et {
  margin-right: 10px;
}

#tb-authorization-edit td.td-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#tb-authorization-edit td.td-value input[type=text] {
  width: auto !important;
}

#tb-authorization-edit td.action {
  padding: 10px !important;
}

#tb-authorization-edit td.action input.action {
  margin: 5px 5px 5px 0 !important;
}

@media (max-width: 1024px) {
  #tb-authorization-edit tr.dataID td.action {
    border-left: 0;
  }
  #tb-authorization-edit tr.dataID td.sync {
    border-right: 0;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit caption, #tb-authorization-edit tbody, #tb-authorization-edit thead, #tb-authorization-edit tfoot {
    width: 100%;
  }
}

@media (max-width: 1024px) {
  #tb-authorization-edit tr {
    display: flex;
    flex-wrap: wrap;
  }
}

/**
 * CSS des tableaux en général
 */
/* Coupe le contenu de la colonne url pour chrome */
td[headers="bnr-url"] {
  /* These are technically the same, but use both */
  overflow-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  /* This is the dangerous one in WebKit, as it breaks things wherever */
  word-break: break-all;
  /* Instead use this non-standard one: */
  word-break: break-word;
}

#site-content .datepickerContainer table, #popup-content .datepickerContainer table {
  width: auto !important;
  min-width: 0;
}

#site-content table#table-synthese-order, #site-content table#table-synthese-search, #popup-content table#table-synthese-order, #popup-content table#table-synthese-search {
  margin-bottom: 8px;
}

#site-content table , #popup-content table {
  border: 1px solid #A9A9A9;
  margin-bottom: 15px;
  clear: both;
  border-spacing: 0;
  border-collapse: collapse;
  /* Classe pour avoir un sous entête de couleur */
}

@media (min-width: 768px) {
  #site-content table , #popup-content table {
    min-width: 625px;
  }
}

#site-content table  table, #popup-content table  table {
  margin-top: 15px;
}

#site-content table  th, #popup-content table  th {
  text-align: left;
}

#site-content table  #desc-long_parent table, #site-content table  .toolBarGroup table, #site-content table  .datepickerViewDays, #site-content table  #ord-products-articles, #popup-content table  #desc-long_parent table, #popup-content table  .toolBarGroup table, #popup-content table  .datepickerViewDays, #popup-content table  #ord-products-articles {
  min-width: 0 !important;
}

#site-content table .ria-admin-ui-overview, #popup-content table .ria-admin-ui-overview {
  margin-bottom: 0;
}

#site-content table  caption, #popup-content table  caption {
  color: #fff;
  background-color: #232E63;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  white-space: nowrap;
}

#site-content table  caption img, #popup-content table  caption img {
  background-color: #fff;
  padding: 1px;
}

#site-content table  thead, #site-content table  th, #site-content table  th.thead, #popup-content table  thead, #popup-content table  th, #popup-content table  th.thead {
  background-color: #DADCFF;
  color: #232E63;
  font-size: 13px;
}

#site-content table  thead a, #site-content table  tfoot, #site-content table  th a, #popup-content table  thead a, #popup-content table  tfoot, #popup-content table  th a {
  color: #232E63;
}

#site-content table  thead, #site-content table  th, #site-content table  th.thead, #site-content table  tfoot, #popup-content table  thead, #popup-content table  th, #popup-content table  th.thead, #popup-content table  tfoot {
  font-weight: 600;
}

#site-content table  td, #site-content table  th, #site-content table  caption, #site-content table  tfoot, #popup-content table  td, #popup-content table  th, #popup-content table  caption, #popup-content table  tfoot {
  padding: 5px 0 5px 10px;
}

#site-content table  caption, #popup-content table  caption {
  padding-right: 10px;
}

#site-content table  thead:not(input), #popup-content table  thead:not(input) {
  text-align: left;
}

#site-content table  thead #select, #popup-content table  thead #select {
  width: 25px;
}

#site-content table  tbody th, #popup-content table  tbody th {
  background: #DADCFF;
}

#site-content table  tbody tr:last-child td:not(.mceLast), #popup-content table  tbody tr:last-child td:not(.mceLast) {
  border-bottom: none;
}

#site-content table  tr, #popup-content table  tr {
  vertical-align: top;
}

#site-content table  td:last-child, #site-content table  th:not(.th-customers):last-child, #popup-content table  td:last-child, #popup-content table  th:not(.th-customers):last-child {
  padding-right: 10px;
}

#site-content table  tfoot, #popup-content table  tfoot {
  border-top: 1px solid #A9A9A9;
  padding: 0;
  text-align: right;
}

#site-content table  tfoot.bg-grey, #popup-content table  tfoot.bg-grey {
  background-color: #EEEEEE;
}

#site-content table  tfoot .tfoot-grey, #popup-content table  tfoot .tfoot-grey {
  text-align: left;
  background-color: #EEEEEE;
  padding: 5px 10px;
}

#site-content table  tfoot input.float-left, #popup-content table  tfoot input.float-left {
  margin-right: 3px;
}

#site-content table  tfoot input.float-right, #popup-content table  tfoot input.float-right {
  margin-left: 3px;
}

#site-content table  fieldset, #popup-content table  fieldset {
  padding: 5px 10px !important;
  /* min-width: 590px; */
  max-width: 100%;
  /* padding: 10px; */
  margin-top: 10px;
  /*&:not(.cdt-grp) div.cdt-psy-val {
                max-width: 180px;
            }*/
}

#site-content table  fieldset legend, #popup-content table  fieldset legend {
  font-weight: bold;
  margin-left: 10px;
}

#site-content table  fieldset input[type=text], #popup-content table  fieldset input[type=text] {
  vertical-align: baseline !important;
}

#site-content table  fieldset label, #popup-content table  fieldset label {
  margin-right: 3px;
}

#site-content table  fieldset div.cdt-config, #popup-content table  fieldset div.cdt-config {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

#site-content table  fieldset div.cdt-config .cdt-grp-list-cdt, #popup-content table  fieldset div.cdt-config .cdt-grp-list-cdt {
  display: flex;
  flex-direction: column;
}

#site-content table  fieldset div.cdt-config .cdt-grp-list-cdt .cdt-grp-message, #popup-content table  fieldset div.cdt-config .cdt-grp-list-cdt .cdt-grp-message {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#site-content table  fieldset div.cdt-config div.cdt-psy-val, #popup-content table  fieldset div.cdt-config div.cdt-psy-val {
  flex: 1 1 auto;
  flex-direction: column;
  position: relative;
  margin-left: 5px;
  margin-right: 5px;
}

#site-content table  fieldset div.cdt-config div.cdt-psy-val select.cdt-psy, #popup-content table  fieldset div.cdt-config div.cdt-psy-val select.cdt-psy {
  width: 200px;
}

#site-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-icon-del, #site-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-grp-del, #site-content table  fieldset div.cdt-config div.cdt-psy-val .input-icon-del, #popup-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-icon-del, #popup-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-grp-del, #popup-content table  fieldset div.cdt-config div.cdt-psy-val .input-icon-del {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

#site-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-same-qte input, #popup-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-same-qte input {
  display: inline;
  margin-left: 0;
}

#site-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-calculated select.cdt-calculated-cdt, #popup-content table  fieldset div.cdt-config div.cdt-psy-val .cdt-calculated select.cdt-calculated-cdt {
  margin-left: 5px;
}

#site-content table  fieldset div.cdt-config .div-cdt-value, #popup-content table  fieldset div.cdt-config .div-cdt-value {
  flex: 1 1 auto;
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  padding-right: 30px;
  position: relative !important;
}

#site-content table  fieldset div.cdt-config .div-cdt-value input.input-icon-del, #popup-content table  fieldset div.cdt-config .div-cdt-value input.input-icon-del {
  position: absolute !important;
  right: 0;
  width: 23px !important;
}

#site-content table  fieldset div.cdt-config .div-cdt-value .cdt-form-bool, #popup-content table  fieldset div.cdt-config .div-cdt-value .cdt-form-bool {
  height: 100%;
  display: flex;
  align-items: center;
}

#site-content table  fieldset div.cdt-config .cdt-select-second .div-website-cdt, #popup-content table  fieldset div.cdt-config .cdt-select-second .div-website-cdt {
  display: flex;
  align-items: center;
}

#site-content table  fieldset div.cdt-config .cdt-select-second .div-fields-cdt, #popup-content table  fieldset div.cdt-config .cdt-select-second .div-fields-cdt {
  display: flex;
  align-items: center;
}

#site-content table  fieldset div.cdt-config input.input-icon-del, #popup-content table  fieldset div.cdt-config input.input-icon-del {
  position: absolute !important;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 0;
  width: 23px !important;
}

#site-content table  fieldset .table-goupe-limites, #popup-content table  fieldset .table-goupe-limites {
  width: 100%;
  border: none;
  margin-bottom: 0;
  padding: 5px 0 5px 10px;
  /* tbody {
                    vertical-align: top;
                    text-align: left;
                    tr:nth-child(2n+0){
                        background-color:$grey-color;
                    }
                    td.td-cdt-config {
                        display: flex;
                        justify-content: space-between;
                        .cdt-grp-cdt {
                            width: 230px !important;
                        }
                        .cdt-psy-val
                        {
                            display: inline;
                            label {
                                text-align: left !important;
                            }
                        }
                        .cdt-grp-del {
                            align-self: center;
                        }
                    }
                } */
}

#site-content table  fieldset .table-goupe-limites tfoot, #popup-content table  fieldset .table-goupe-limites tfoot {
  border-top: 1px solid #A9A9A9;
}

#site-content table  .th-head-second th, #site-content table  .th-head-second td, #popup-content table  .th-head-second th, #popup-content table  .th-head-second td {
  background-color: #ecebff;
}

#site-content table  .head-second th, #popup-content table  .head-second th {
  background-color: #ecebff;
}

@media (min-width: 1024px) {
  #site-content #table-fiche-classe, #popup-content #table-fiche-classe {
    min-width: 768px;
  }
}

@media (min-width: 1400px) {
  #site-content #table-fiche-classe, #popup-content #table-fiche-classe {
    min-width: 1024px;
  }
}

#site-content #table-fiche-classe td:first-child, #popup-content #table-fiche-classe td:first-child {
  width: 20%;
}

#site-content #table-fiche-classe td:last-child, #popup-content #table-fiche-classe td:last-child {
  width: 80%;
}

#site-content #table-fiche-classe textarea, #popup-content #table-fiche-classe textarea {
  width: 100%;
  height: auto;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) {
  /* RESPONSIVE */
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 {
  width: 600px;
  max-width: 600px;
}

@media (max-width: 1024px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 {
    width: 100%;
  }
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr {
  width: auto;
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr {
    width: 100%;
  }
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr td:first-child {
  width: 180px;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr td #naf {
  width: 145px;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr td #taxcode {
  width: 145px;
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr td {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).w-600 tbody tr th {
    width: 100%;
  }
}

@media (max-width: 767px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    max-width: 100%;
  }
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) td[data-label]::before, #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) th[data-label]::before {
    margin-right: 3px;
  }
}

@media (max-width: 767px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) caption, #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tbody:not(.datepickerYears):not(.datepickerMonths):not(.datepickerDays):not(.datepickerWeek):not(.datepickerNotInMonth):not(.datepickerSaturday):not(.datepickerSunday), #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) thead, #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tfoot {
    width: 100%;
    display: block;
  }
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) caption {
  order: 0;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) thead {
  order: 1;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tbody {
  order: 2;
}

#site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tfoot {
  order: 3;
}

@media (max-width: 767px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tr {
    display: flex;
    flex-wrap: wrap;
  }
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tr th {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tr td {
    display: block;
    width: 100%;
    border-right: 0;
    padding-right: 10px;
  }
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) tr td:last-child {
    border-bottom: 0;
  }
}

@media (max-width: 768px) {
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers).datepickerViewDays tr {
    flex-wrap: nowrap;
  }
  #site-content table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers)#tb-synthese-order {
    border: 0;
  }
}

#site-content .datepickerContainer table {
  display: table !important;
  max-width: 100%;
}

#site-content .datepickerContainer table thead {
  display: table-header-group !important;
}

#site-content .datepickerContainer table tr {
  display: table-row !important;
}

#site-content .datepickerContainer table th, #site-content .datepickerContainer table td {
  display: table-cell !important;
  padding-right: 0 !important;
}

@media (max-width: 768px) {
  #site-content #tb-goals {
    display: table !important;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  #site-content #tb-goals tr {
    width: 100%;
    display: block !important;
  }
  #site-content #tb-goals tr th {
    width: 100%;
  }
}

#popup-content table {
  margin-bottom: 5px;
}

/* class checklist */
@media (max-width: 1023px) {
  #site-content .checklist,
  #site-content .fidelity {
    width: 100% !important;
  }
  #site-content .checklist tbody select, #site-content .checklist tbody input, #site-content .checklist tbody textarea,
  #site-content .fidelity tbody select,
  #site-content .fidelity tbody input,
  #site-content .fidelity tbody textarea {
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
  }
  #site-content .checklist th,
  #site-content .checklist td,
  #site-content .checklist tr,
  #site-content .checklist caption,
  #site-content .fidelity th,
  #site-content .fidelity td,
  #site-content .fidelity tr,
  #site-content .fidelity caption {
    display: block;
    width: 100%;
  }
  #site-content .checklist thead tr th.thead-none,
  #site-content .fidelity thead tr th.thead-none {
    display: none;
    width: 100% !important;
  }
  #site-content .checklist thead tr.head-filter th,
  #site-content .fidelity thead tr.head-filter th {
    display: table-cell;
  }
  #site-content .checklist tr:not(:last-child),
  #site-content .fidelity tr:not(:last-child) {
    border-bottom: 1px solid #A9A9A9;
    padding: 0;
  }
  #site-content .checklist td,
  #site-content .fidelity td {
    padding: 5px 10px;
    border-bottom: 0 none !important;
    overflow: hidden;
    text-align: left;
  }
  #site-content .checklist .align-center, #site-content .checklist .align-right, #site-content .checklist .number,
  #site-content .fidelity .align-center,
  #site-content .fidelity .align-right,
  #site-content .fidelity .number {
    text-align: left !important;
  }
  #site-content .checklist td[data-label]::before, #site-content .checklist th[data-label]::before,
  #site-content .fidelity td[data-label]::before,
  #site-content .fidelity th[data-label]::before {
    content: attr(data-label);
  }
  /* Mettre ici les tableaux checklist qui doivent responsive qu'en dessous de medium */
  #site-content #tab-stock.checklist thead,
  #site-content .fidelity thead {
    display: table-header-group;
  }
  #site-content #tab-stock.checklist tr,
  #site-content .fidelity tr {
    display: table-row;
  }
  #site-content #tab-stock.checklist th,
  #site-content #tab-stock.checklist td,
  #site-content .fidelity th,
  #site-content .fidelity td {
    display: table-cell;
  }
  #site-content #tab-stock.checklist caption,
  #site-content .fidelity caption {
    display: table-caption;
  }
}

@media (max-width: 1023px) and (max-width: 767px) {
  #site-content #tab-stock.checklist th,
  #site-content #tab-stock.checklist td,
  #site-content #tab-stock.checklist tr,
  #site-content #tab-stock.checklist caption,
  #site-content .fidelity th,
  #site-content .fidelity td,
  #site-content .fidelity tr,
  #site-content .fidelity caption {
    display: block;
  }
  #site-content #tab-stock.checklist thead,
  #site-content .fidelity thead {
    display: none;
  }
}

@media (max-width: 767px) {
  #popup-content {
    width: 100% !important;
  }
  #popup-content table tbody select, #popup-content table tbody input, #popup-content table tbody textarea {
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
  }
  #popup-content table th,
  #popup-content table td,
  #popup-content table tr,
  #popup-content table caption {
    display: block;
    width: 100%;
  }
  #popup-content table thead {
    display: none;
  }
  #popup-content table tr:not(:last-child) {
    border-bottom: 1px solid #A9A9A9;
    padding: 0;
  }
  #popup-content table td {
    padding: 5px 5px;
    border-bottom: 0 none !important;
    overflow: hidden;
    text-align: left;
  }
  #popup-content table tfoot td {
    display: flex;
    flex-wrap: wrap;
  }
  #popup-content table tfoot td * {
    margin-right: 2px;
  }
  #popup-content table:not(.checklist) tr {
    border-bottom: 0 none !important;
  }
  #popup-content table .fidelity tr:not(:last-child),
  #popup-content table .checklist tr:not(:last-child) {
    border-bottom: 1px solid #A9A9A9 !important;
  }
  #popup-content table td[data-label]::before, #popup-content table th[data-label]::before {
    content: attr(data-label);
  }
  #popup-content table .thead-none {
    display: none;
  }
  #popup-content table#table-languages {
    display: table !important;
    max-width: 100%;
  }
  #popup-content table#table-languages tr {
    width: 100%;
    display: table-row;
  }
  #popup-content table#table-languages tr th, #popup-content table#table-languages tr td {
    display: table-cell;
  }
  #popup-content table#table-languages tr td:first-child {
    width: 20px;
  }
  table .thead-none {
    display: none !important;
  }
}

@media (max-width: 767px) {
  #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > th,
  #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > td,
  #site-content #tabpanel > table > tbody > tr,
  #site-content table:not(#prf-rights)[summary] > tbody > tr > th,
  #site-content table:not(#prf-rights)[summary] > tbody > tr > td,
  #site-content table:not(#prf-rights)[summary] > tbody > tr,
  #site-content #tabpanel > table:not(#prf-rights) > tfoot > tr > th,
  #site-content #tabpanel > table > tfoot > tr > td,
  #site-content #tabpanel > table > tfoot > tr,
  #site-content table[summary] > tfoot > tr > th,
  #site-content table[summary] > tfoot > tr > td,
  #site-content table[summary] > tfoot > tr {
    width: 100%;
    box-sizing: border-box;
  }
}

/* TinyMCE - tableaux */
textarea.tinymce {
  width: 100%;
}

textarea.tinymce + span.mceEditor table {
  /* Entête avec toolbar */
}

textarea.tinymce + span.mceEditor table tr.mceFirst td.mceToolbar {
  width: 100% !important;
}

textarea.tinymce + span.mceEditor table tr.mceFirst td.mceToolbar table {
  height: 100%;
}

@media (max-width: 767px) {
  textarea.tinymce + span.mceEditor table tr {
    display: flex;
    flex-wrap: wrap;
  }
  textarea.tinymce + span.mceEditor table tr th {
    width: auto !important;
  }
  textarea.tinymce + span.mceEditor table tr th, textarea.tinymce + span.mceEditor table tr td {
    display: table-cell !important;
  }
  textarea.tinymce + span.mceEditor table tr th.mceIframeContainer, textarea.tinymce + span.mceEditor table tr td.mceIframeContainer {
    border-right: 1px solid #A9A9A9 !important;
  }
  textarea.tinymce + span.mceEditor table tr td {
    width: auto !important;
  }
  textarea.tinymce + span.mceEditor table tr td.mceIframeContainer:last-child {
    border-bottom: 1px solid #A9A9A9 !important;
  }
  textarea.tinymce + span.mceEditor table tr td.mceIframeContainer {
    width: 100% !important;
  }
}

textarea.tinymce + span.mceEditor .desc-long_tbl {
  width: 100%;
}

textarea.tinymce + span.mceEditor .mceLayout {
  max-width: 100%;
}

@media (max-width: 767px) {
  textarea.tinymce + span.mceEditor .mceLayout {
    height: auto !important;
  }
}

#popup-content table th, #popup-content table td {
  /* display: table-cell; */
  vertical-align: inherit;
}

/* Contient le titre de la table et les filtres/options/tri */
.table-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Tableau Jours et heures d'expédition dans Yuto > "Horaires d'activité" et Configuration > "Horaires d'expédition" */
#tb-day-exp.tb-day-exp2, #tb-day-exp.tb-day-exp1 {
  max-width: 1060px;
  width: 1060px;
  overflow-y: auto;
}

#tb-day-exp.tb-day-exp2 thead #period-exp, #tb-day-exp.tb-day-exp1 thead #period-exp {
  text-align: center;
}

#tb-day-exp.tb-day-exp2 tbody td, #tb-day-exp.tb-day-exp1 tbody td {
  border-bottom: 1px solid #EEEEEE;
  height: 35px;
  vertical-align: middle;
}

#tb-day-exp.tb-day-exp2 tbody td.reinit, #tb-day-exp.tb-day-exp1 tbody td.reinit {
  text-align: center;
}

#tb-day-exp.tb-day-exp2 tbody td.period, #tb-day-exp.tb-day-exp1 tbody td.period {
  color: #232E63;
  font-size: 10px;
  text-align: center;
}

#tb-day-exp.tb-day-exp2 tbody td div.hr-active,
#tb-day-exp.tb-day-exp2 tbody td div.hr-inactive, #tb-day-exp.tb-day-exp1 tbody td div.hr-active,
#tb-day-exp.tb-day-exp1 tbody td div.hr-inactive {
  float: left;
  height: 25px;
  margin-left: 1px;
  width: 20px;
}

#tb-day-exp.tb-day-exp2 tbody td div.hr-inactive, #tb-day-exp.tb-day-exp1 tbody td div.hr-inactive {
  background-image: url("/admin/images/expeditions/horaires_no.svg");
}

#tb-day-exp.tb-day-exp2 tbody td div.hr-active, #tb-day-exp.tb-day-exp1 tbody td div.hr-active {
  background-image: url("/admin/images/expeditions/horaires_yes.svg");
}

#tb-day-exp.tb-day-exp2 tbody td div.hr-active:hover, #tb-day-exp.tb-day-exp1 tbody td div.hr-active:hover {
  cursor: pointer;
}

#tb-day-exp.tb-day-exp2 input.hr-check, #tb-day-exp.tb-day-exp1 input.hr-check {
  display: none;
}

.container-shadow {
  width: 100%;
  position: relative;
}

.container-shadow.container-border {
  border: 1px solid #A9A9A9;
}

.container-shadow .bar-shadow {
  position: absolute;
  width: 20px;
  height: calc(100% - 16px);
  top: 0;
  z-index: 2;
}

.container-shadow .bar-shadow.shadow-left {
  left: 0;
  box-shadow: 15px 0px 10px -10px rgba(0, 0, 0, 0.25) inset;
}

.container-shadow .bar-shadow.shadow-right {
  right: 0;
  box-shadow: -15px 0px 10px -10px rgba(0, 0, 0, 0.25) inset;
}

.table-cols-changed {
  margin-bottom: -2px;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  z-index: 1;
}

#foot-products tbody, #foot-products tfoot {
  border-top: 0 !important;
}

#foot-products .tfoot-grey * {
  vertical-align: middle;
}

#foot-products .tfoot-grey label:not([for="sort-prd-inherited"]) {
  display: inline-block;
  width: 100%;
  max-width: 345px;
}

#foot-products .tfoot-grey select {
  width: 226px;
}

#tb-holidays #th-year th {
  background: #232E63;
  color: #fff;
  border-color: #232E63;
}

div.hr-inactive:hover {
  cursor: pointer;
}

#tb-redirection tbody img.edit-url {
  border: medium none;
  cursor: pointer;
  width: 20px;
  margin-bottom: 3px;
}

#site-content {
  /* Pour afficher les noms des colonnes en responsive */
}

#site-content .table-layout-large {
  /* RESPONSIVE */
}

@media (max-width: 1400px) {
  #site-content .table-layout-large {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table {
    display: table;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table caption, #site-content .table-layout-large table tbody, #site-content .table-layout-large table thead, #site-content .table-layout-large table tfoot {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #site-content .table-layout-large table tr {
    display: table-row;
  }
}

@media (max-width: 1024px) {
  #site-content #tb-holidays > tbody > tr {
    border-bottom: 1px solid #A9A9A9;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    flex-direction: column;
    border-bottom: none;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child label {
    padding: 3px;
    width: 100%;
  }
  #site-content #tb-holidays > tbody > tr > td:last-child label input {
    margin-right: 5px;
    vertical-align: middle;
  }
}

@media (max-width: 1023px) {
  #site-content td[data-label]::before, #site-content th[data-label]::before {
    content: attr(data-label);
    vertical-align: middle;
  }
  #site-content .thead-none {
    display: none;
  }
}

@media (max-width: 1023px) {
  #site-content, #popup-content {
    /* .table-responsive th + td {
            padding-left: 10px;
        } */
  }
  #site-content table.table-responsive,
  #site-content table.table-responsive.checklist,
  #site-content table.table-responsive thead,
  #site-content table.table-responsive tbody,
  #site-content table.table-responsive tr,
  #site-content table.table-responsive th,
  #site-content table.table-responsive td, #popup-content table.table-responsive,
  #popup-content table.table-responsive.checklist,
  #popup-content table.table-responsive thead,
  #popup-content table.table-responsive tbody,
  #popup-content table.table-responsive tr,
  #popup-content table.table-responsive th,
  #popup-content table.table-responsive td {
    display: block;
  }
  #site-content table.table-responsive thead, #popup-content table.table-responsive thead {
    display: none;
  }
  #site-content table.table-responsive tfoot, #popup-content table.table-responsive tfoot {
    display: flex;
    flex-direction: column;
    padding: 5px;
  }
  #site-content table.table-responsive tfoot > tr:first-child, #popup-content table.table-responsive tfoot > tr:first-child {
    display: flex;
    flex-wrap: wrap;
  }
  #site-content table.table-responsive tbody td,
  #site-content table.table-responsive.checklist tbody td[headers="select"],
  #site-content table.table-responsive.checklist tbody td[headers="desc"],
  #site-content table.table-responsive.checklist tbody td[headers="ref"], #popup-content table.table-responsive tbody td,
  #popup-content table.table-responsive.checklist tbody td[headers="select"],
  #popup-content table.table-responsive.checklist tbody td[headers="desc"],
  #popup-content table.table-responsive.checklist tbody td[headers="ref"] {
    padding: 5px 5px 5px 150px;
    width: 100%;
    position: relative;
    margin-top: -1px;
    background: #fff;
  }
  #site-content table.table-responsive tbody td:nth-child(odd), #popup-content table.table-responsive tbody td:nth-child(odd) {
    background-color: #EEEEEE;
  }
  #site-content table.table-responsive tbody td::before, #popup-content table.table-responsive tbody td::before {
    padding: 10px;
    content: attr(data-label);
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    bottom: 0;
    background-color: #DADCFF;
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  #site-content table.table-responsive tbody tr, #popup-content table.table-responsive tbody tr {
    /* margin-bottom: 1rem; */
    border-top: 1px solid #A9A9A9;
  }
}

#site-content .authorizations tr.odd {
  background-color: #EEEEEE;
}

#site-content .authorizations tr.odd td {
  background: none;
}

#table-une-commande tr {
  vertical-align: top;
}

#fields-classe-objects {
  width: 625px;
}

/* Ajout d'une ligne lorsqu'il faut séparer 2 tr dans un tfoot */
.ftoot-border-top {
  border-top: 1px solid #A9A9A9;
}

/* Tableaux de synthèse */
#table-synthese-order {
  width: 100%;
}

#table-synthese-order thead #hd-rewards-total, #table-synthese-order thead #hd-rewards-used {
  width: 150px;
}

#table-synthese-order thead #hd-rewards-no-used {
  width: 180px;
}

#table-synthese-order thead #hd-order-ht, #table-synthese-order thead #hd-order-ttc {
  width: 200px;
}

#table-synthese-order thead #hd-order-total {
  width: 130px;
}

#table-synthese-order thead #hd-order-conversion {
  width: 130px;
}

#table-synthese-order thead #hd-order-prds {
  width: 160px;
}

#table-synthese-order thead #hd-order-margin {
  width: 150px;
}

#table-synthese-order thead #hd-order-avg-ht {
  width: 120px;
}

#table-synthese-order thead #hd-order-avg-ttc {
  width: 130px;
}

#table-synthese-order thead .loader {
  width: 20px;
  height: 20px;
}

#table-synthese-order tbody td {
  padding-bottom: 5px;
}

#table-synthese-order tbody img.loader {
  width: 20px;
  height: 20px;
}

#site-content #table-synthese-order th, #site-content #table-synthese-order td, #site-content #table-synthese-search th, #site-content #table-synthese-search td {
  border-right: 1px solid #A9A9A9;
  padding-left: 10px;
  padding-right: 10px !important;
}

#site-content #table-synthese-order thead tr th, #site-content #table-synthese-search thead tr th {
  background-color: #fff;
  font-weight: 500;
  text-align: left;
}

#site-content #table-synthese-order tbody tr td, #site-content #table-synthese-search tbody tr td {
  border-bottom: 1px solid #A9A9A9;
  font-size: 1.4em;
  font-weight: 600;
  white-space: nowrap;
}

@media (min-width: 1024px) and (max-width: 1199px) {
  #site-content #table-synthese-order tbody tr td, #site-content #table-synthese-search tbody tr td {
    font-size: 1.1em;
  }
}

#site-content table#table-edit-personnalisation tbody th {
  background-color: #232E63;
  color: #fff;
}

#site-content table#table-edit-personnalisation textarea {
  resize: none;
}

/**
    Tablesorter
**/
#site-content .tablesorter th > a {
  position: relative;
  padding-right: 15px;
}

#site-content .tablesorter th > a:after {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -6px;
}

/* adatpe les notices à la taille des  tableaux */
.block-notice-container {
  display: flex;
  flex-direction: column;
  width: auto !important;
  /* taille max lorsque le contenu de la notice est plus grand que le tableau */
}

.block-notice-container * {
  max-width: 100%;
}

.block-notice-container .notice {
  display: inline-block;
}

.block-notice-container.relations-container {
  max-width: 896px;
}

.block-notice-container.referencement-container {
  max-width: 820px;
}

/* taille des colonnes */
.col1, .col3pourcents {
  width: 3%;
}

.col2, .col63pourcents5 {
  width: 63.5%;
}

.col3, .col33pourcents5 {
  width: 33.5%;
}

.col4, .col97pourcents {
  width: 97%;
}

.col20px {
  width: 20px !important;
  max-width: 20px !important;
}

.col-check {
  width: 25px;
}

th.checkall {
  width: 30px;
}

.col-sync {
  width: 28px;
}

.col40px {
  width: 40px !important;
}

.col60px {
  width: 60px !important;
}

.col70px {
  width: 70px;
}

.col75px {
  width: 75px;
}

.col80px {
  width: 80px;
}

.col90 {
  width: 90px;
}

.col100px {
  width: 100px !important;
}

.col105px {
  width: 105px !important;
}

.col110px {
  width: 110px !important;
}

.col120px {
  width: 120px !important;
}

.col125px {
  width: 125px !important;
}

.col130px {
  width: 130px;
}

.col140px {
  width: 140px !important;
}

.col145px {
  width: 145px;
}

.col150px {
  width: 150px;
}

.col170px {
  width: 170px;
}

.col180px {
  width: 180px;
}

.col200px {
  width: 200px !important;
}

.col230px {
  width: 230px !important;
}

.col300px {
  max-width: 300px;
}

.colMax300px {
  max-width: 300px !important;
}

.col308px {
  width: 308px;
}

.col335 {
  width: 335px;
}

.col-m {
  width: 460px;
}

.col-big-content {
  width: 620px;
}

/**
 * CSS des fichiers customers/new.php (en popup dans Catalogue > Relevés linéaires > "un relevé" > onglet "Relations" > ajouter > Créer un nouveau compte)
 */
/* tableaux dans la popup */
#popup-content #td-new-user-1 {
  width: 215px;
}

#popup-content #td-new-user-2 {
  width: 360px;
}

@media (max-width: 767px) {
  #popup-content #td-new-user-2 {
    width: 100%;
  }
}

#popup-content table#new-usr {
  width: 100%;
}

#popup-content table#new-usr tbody td {
  border-bottom: none;
}

/**
 * CSS des pages Clients > Tous les comptes
 */
/* tableau  de la liste des comptes dans ./customers/index.php */
#list-customers {
  max-width: 100%;
}

#list-customers thead .th-customers {
  width: 170px;
}

@media (max-width: 1400px) {
  #list-customers [headers="usr-ref"] {
    max-width: 70px !important;
  }
  #list-customers [headers="usr-prf"] {
    max-width: 80px !important;
  }
  #list-customers [headers="usr-ref"],
  #list-customers [headers="usr-prf"] {
    overflow-wrap: break-word;
  }
}

@media (max-width: 1023px) {
  #list-customers [headers="usr-ref"] {
    max-width: 100% !important;
  }
  #list-customers [headers="usr-prf"] {
    max-width: 100% !important;
  }
  #list-customers td {
    text-align: left !important;
  }
}

/* tableau des fiches clients, onglet général dans ./wiews/customers/tabs/general.php */
#table-fiche-client-general {
  width: 680px;
}

#table-fiche-client-general #td-fiche-client-general-1 {
  width: 215px;
}

#table-fiche-client-general #td-fiche-client-general-2 {
  width: 360px;
}

#table-fiche-client-general #usr-map {
  width: 100%;
  height: 275px;
  position: relative;
}

#table-fiche-client-general .ref {
  width: 180px;
}

/* Pop-up créer un avoir */
#select-discount-type {
  width: auto !important;
}

/* Comptes Clients, "Nom du client" > onglet "Commandes" */
@media (max-width: 767px) {
  .tb-remainders tbody tr:not(.head-second):not(:only-child):nth-child(odd) {
    background-color: #EEEEEE;
  }
  .tb-remainders tbody tr.show-orders {
    background-color: #fff !important;
  }
  .tb-remainders .align-right, .tb-remainders .numeric {
    text-align: left !important;
  }
}

/* tableau des fiches clients, onglet droits dans ./include/right.inc.php */
#rights {
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
  border-spacing: 0px;
}

#rights .th-rights {
  width: 50%;
  vertical-align: middle;
}

#rights .th-rights a {
  color: #3D50DF;
}

#rights ul {
  list-style-type: none;
  margin-left: 5px;
}

#rights ul ul {
  margin: 5px 20px;
}

#rights ul li {
  margin: 5px 0;
}

/* Comptes Clients, "Nom du client" > onglet "Adresses" */
#address-new #button-add-adresse {
  margin: 30px;
}

/* Comptes Clients, "Nom du client" > onglet "Adresses", "ajouter une adresse" : Tableau dans la popup ajouter une adresse */
#popup-content #table-fiche-client {
  width: 100%;
}

#popup-content #table-fiche-client tbody td {
  border-bottom: 0;
}

#popup-content #table-fiche-client tbody td #td-fiche-client-1 {
  width: 160px;
}

/* Comptes Clients, "Nom du client" > onglet "Disponibilité" */
#table-availability #prd-sel {
  width: 20px;
}

#table-availability #prd-ref {
  width: 100px;
}

#table-availability #date {
  width: 150px;
}

#table-availability #once {
  width: 200px;
}

#table-availability #last {
  width: 120px;
}

/* Comptes Clients, "Nom du client" > onglet "Favoris" */
#list-whishlist #wishlist-del {
  width: 20px;
}

#list-whishlist #wishlist-name {
  width: 300px;
}

#list-whishlist #wishlist-publish {
  width: 80px;
}

#list-whishlist #wishlist-prds {
  width: 100px;
}

/* Tableau popup Ajouter une liste personnalisée */
#table-new-wishliste {
  width: 520px;
}

#table-new-wishliste #td-new-wishliste-1 {
  width: 150px;
}

#table-new-wishliste #td-new-wishliste-2 {
  width: 300px;
}

/* Comptes Clients, "Nom du client" > onglet "Relations" */
.hierarchies .checkall {
  width: 20px;
}

.hierarchies .code-client {
  width: 100px;
}

.hierarchies .societe {
  width: 120px;
}

.hierarchies .nom, .hierarchies .adresse, .hierarchies .complements {
  width: 150px;
}

.hierarchies .email {
  width: 200px;
}

/* Comptes Clients, "Nom du client" > onglet "Images"  */
.sortable-img .preview {
  float: left;
  margin: 0 5px 5px 0;
  width: 152px;
  height: 152px;
}

#site-content .has-main-img .sortable-img .preview:first-child {
  display: block;
  float: none;
}

li.preview {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
}

li.preview [type="image"] {
  width: 30px;
  height: 30px;
}

/* Comptes Clients, "Nom du client" > onglet "Objectifs" */
#goals td {
  vertical-align: middle;
}

/* Comptes Clients, "Nom du client" > onglet "Objectifs" */
#tb-goals {
  width: 600px;
}

#tb-goals .hld-nav-year {
  border: medium none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
}

#tb-goals tbody th {
  background-color: #F4F7FB !important;
}

#table-objectifs #goal-month {
  width: 100px;
}

#table-objectifs #goal-turnover-ord, #table-objectifs #goal-turnover, #table-objectifs #goal-turnover-inv, #table-objectifs #goal-margin-now {
  width: 150px;
}

#table-objectifs #goal-turnover-facture, #table-objectifs #goal-margin-goal {
  width: 200px;
}

#stats-rewards #del-rewards {
  width: 20px;
}

#stats-rewards #husr {
  width: 290px;
}

#stats-rewards #edit {
  width: 290px;
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation */
.nocaption select, .nocaption input:not([type="checkbox"]) {
  width: auto !important;
}

.nocaption + .authorizations .little_input {
  width: auto !important;
}

/* Comptes clients > Gestion des droits d'accès > "un droit d'accès, onglet Droits */
/* Tableau des Droits d'accès des comptes clients */
#table-profiles #prf-name, #table-profiles #prf-pl-name, #table-profiles #prf-users {
  width: 200px;
}

#table-propriete #td-propriete-1 {
  width: 135px;
}

#table-propriete #td-propriete-2 {
  width: 360px;
}

#table-infos-generales-profiles #td-infos-gen-prof-1 {
  width: 135px;
}

#table-infos-generales-profiles #td-infos-gen-prof-2 {
  width: 400px;
}

#prf-rights {
  width: 100%;
  max-width: 425px;
}

#prf-rights .expansible th, #prf-rights .inexpansible th {
  background-position: right center;
  background-repeat: no-repeat;
}

#prf-rights .expansible th.checkall, #prf-rights .inexpansible th.checkall {
  background-image: none;
}

#prf-rights .expansible th {
  background-image: url("/admin/dist/images/down.svg");
  border-bottom: 1px solid #A9A9A9;
}

#prf-rights .inexpansible th {
  background-image: url("/admin/dist/images/up.svg");
}

#prf-rights input.checkright {
  margin-left: 5px;
}

#prf-rights label.reset-rgh {
  display: block;
  margin-bottom: 5px;
  text-align: left;
}

#prf-rights #reset-rgh {
  float: left;
  margin: 1px 5px 40px 3px;
}

#foot-products {
  width: 100%;
}

#editproduct {
  width: 100%;
}

#editproduct #icon-del-cat {
  width: 16px;
}

#editproduct #title {
  width: 100% !important;
  max-width: 100%;
}

/* Catégories */
#categories {
  width: 100%;
  max-width: 1100px;
}

@media (min-width: 1024px) {
  #categories #cat-sel {
    width: 25px;
  }
  #categories #cat-publish {
    width: 100px;
  }
  #categories #cat-products {
    width: 135px;
  }
}

/* Une catégorie */
.table-top + .items-list-filters {
  padding-right: 180px;
}

@media (max-width: 767px) {
  .table-top + .items-list-filters {
    padding-right: 0;
  }
}

#form-catalog-produits {
  position: relative;
  height: auto;
  min-height: 300px;
}

#form-catalog-produits .with-cols-options {
  position: absolute;
  right: 0;
  top: -25px;
}

@media (max-width: 1024px) {
  #form-catalog-produits .with-cols-options {
    display: inline-block;
    top: auto;
    position: relative;
    margin-bottom: 5px;
  }
}

/* Une catégorie, tableau de produits */
.list-cols-changed {
  border: 0 none !important;
  border-bottom: 1px solid #A9A9A9 !important;
  margin-bottom: 0 !important;
  width: 100%;
}

.list-cols-changed tr {
  position: relative;
}

.list-cols-changed #prd-selled, .list-cols-changed .number {
  padding-right: 5px !important;
  text-align: right;
}

.list-cols-changed .th-col-hide {
  display: none;
}

* + html .list-cols-changed tr, * + html .list-cols-changed .prd-col-nomove {
  position: static !important;
}

/* Catégories > un produit > "edition" > onglet "Général" */
#product #desc {
  width: 100%;
}

/* Une catégorie > "edition" > onglet "place de marché" */
#tbl-export thead tr td:first-child {
  width: 180px;
}

#tbl-export thead tr td:last-child {
  width: 650px;
  max-width: 100%;
}

/* Catalogue > une catégorie > un produit > edition, onglet "Général" */
#table-nomenclature, #table-nomenclature-composition {
  width: 600px;
}

#table-nomenclature #nomenclature_ref, #table-nomenclature-composition #nomenclature_ref {
  width: 300px;
}

@media (max-width: 767px) {
  #table-nomenclature #nomenclature_ref, #table-nomenclature-composition #nomenclature_ref {
    width: 100%;
  }
}

/* Catalogue > une catégorie > un produit > edition, onglet "Articles liés" */
.list-prd-relations {
  margin-top: 10px !important;
  max-width: 1247px;
}

@media (max-width: 1023px) {
  .list-prd-relations {
    display: flex;
    flex-direction: column;
  }
}

/* Catalogue > une catégorie > un produit > edition, onglet "Stocks" */
#table-reapprovisionnement input.datepicker, #table-reapprovisionnement input.schedule-qte {
  width: 100% !important;
}

.tb-stock-all {
  width: 100%;
  max-width: 700px;
}

.tab-stock input {
  width: 50px;
  text-align: right;
}

@media (max-width: 1023px) {
  .tab-stock td.align-right, .tab-stock th.align-right {
    padding-right: 5px !important;
  }
}

.schedule-table {
  text-align: center;
  width: 100%;
}

.schedule-table .schedule-date,
.schedule-table .schedule-qte {
  width: 100px;
}

@media (max-width: 767px) {
  .schedule-table thead {
    display: none;
  }
  .schedule-table tbody td {
    text-align: left;
  }
}

/* #products td.is-loading {
	background-image: url('/admin/images/ajax.gif');
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
} */
.search-customers-results tbody tr + tr {
  border-top: 1px solid #A9A9A9;
}

.list-to-move {
  margin-bottom: 10px;
}

/* Une catégorie > "edition" > onglet "Comparateurs" */
.mapping td {
  padding: 5px;
}

.mapping td.link-ctr label {
  color: #000;
  font-weight: 500;
  margin-top: 5px;
  display: inline-block;
}

.mapping input[type=radio] {
  margin-top: 5px;
  width: 15px !important;
}

.mapping .info-compl {
  margin-top: 5px;
}

.mapping .info-compl .underline {
  text-decoration: underline;
}

.mapping .diffctr {
  display: none;
}

.mapping .del, .mapping input.title, .mapping textarea.desc {
  display: none;
}

.mapping textarea.desc {
  width: 100%;
  height: 150px;
}

.mapping span.srg_desc {
  display: block;
  width: 95%;
  float: left;
}

/* Une catégorie > "edition" > onglet "Tarification" */
#pamp label, #taxes label {
  width: 107px;
  display: inline-block;
}

#pamp input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not([type="button"]):not([type="submit"]):not([type="reset"]), #pamp select, #taxes input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not([type="button"]):not([type="submit"]):not([type="reset"]), #taxes select {
  text-align: right;
  width: 110px;
}

#pamp input[type=button], #taxes input[type=button] {
  margin-left: 5px;
}

/* Une catégorie > "edition" > onglet "Fidèlité" */
#site-content input.hour {
  width: 44px !important;
}

/*  Une catégorie > un produit, onglet "Avis" */
#cntcontact {
  margin-top: 15px;
}

#cntcontact .td-author {
  border-bottom: 1px solid #A9A9A9;
  border-right: 1px solid #A9A9A9;
  padding: 20px;
}

#cntcontact .td-message {
  border-bottom: 1px solid #A9A9A9;
  padding: 20px;
  max-width: 610px;
  word-wrap: break-word;
}

#cntcontact .infos-compl {
  border-top: 1px solid #A9A9A9;
  margin-top: 20px;
  padding-top: 5px;
}

#cntcontact .content-action {
  border: 1px solid #A9A9A9;
  float: left;
  margin: 5px;
  padding: 5px;
  width: 370px;
}

#cntcontact .content-action-moderate {
  width: 195px;
}

#cntcontact .info-value {
  margin-left: 20px;
}

#cntcontact .name-action {
  display: block;
  margin-top: 5px;
}

#cntcontact textarea {
  height: 150px !important;
  margin: 5px 0 !important;
  width: 524px !important;
}

/*  Une catégorie > un produit, onglet "Référencement" */
#table-recherches-internes {
  width: 100%;
  max-width: 820px;
}

#table-recherches-internes #volume, #table-recherches-internes #website {
  width: 100px;
}

#table-recherches-internes #ctr, #table-recherches-internes #section {
  width: 65px;
}

#table-recherches-internes #types {
  width: 130px;
}

/* Catégories > "une catégorie" > "un produit", onglet "Avancé" */
/* Tableau des champs avancés */
.tb-champs-avances {
  width: 100%;
}

.tb-champs-avances .tr-champs:not(:first-child) {
  border-top: 1px solid #A9A9A9;
}

.tb-champs-avances .tr-champs .modele-saisie {
  margin-top: 4px;
}

.tb-champs-avances .tr-champs .del-link {
  margin-right: 5px;
}

@media (max-width: 767px) {
  .tb-champs-avances .tr-champs th {
    display: table-cell !important;
  }
}

.tb-champs-avances .bg-ghostwhite {
  background-color: #f8f8ff !important;
}

.tb-champs-avances .list-fields {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.tb-champs-avances .list-fields div {
  word-break: break-all;
}

/**
 * CSS de Commandes
 */
/* Tableau de la liste des commandes */
#table-liste-commandes {
  width: 100%;
}

#table-liste-commandes #ord-id, #table-liste-commandes #ord-state {
  width: 100px;
}

#table-liste-commandes #ord-date, #table-liste-commandes #ord-ht, #table-liste-commandes #ord-ht {
  width: 120px;
}

#table-liste-commandes #lst_orders #td-ord-id {
  width: 200px;
}

#table-liste-commandes #td-ord-id ul {
  list-style: none;
  margin: 10px 0 0 0;
}

@media (max-width: 768px) {
  #table-liste-commandes .align-right {
    text-align: left !important;
  }
}

#table-une-commande {
  width: 100%;
}

#table-une-commande .bloc-ghost {
  width: 180px;
  height: 0;
}

#table-une-commande tbody tr.order-products-row > td {
  padding-right: 0 !important;
}

#table-une-commande tbody tr.order-products-row td:first-child {
  width: 100%;
}

#table-une-commande tbody tr td:not(.multi-colspan):not(.td-engrenage):not(#adresses):first-child {
  width: 190px !important;
}

@media (max-width: 768px) {
  #table-une-commande tbody tr td:not(.multi-colspan):not(.td-engrenage):not(#adresses):first-child {
    width: 100% !important;
  }
}

@media (max-width: 767px) {
  #table-une-commande tbody tr #adresses.multi-colspan {
    padding-right: 0 !important;
  }
}

#table-une-commande #table-adresse-livraison {
  width: 100%;
}

#table-une-commande tfoot > tr:first-child > td:first-child {
  white-space: normal;
}

#table-une-commande tfoot > tr:last-child > td:first-child {
  white-space: nowrap;
}

.tb_invoice .multi-colspan span {
  display: inline-block;
}

.tb_invoice .multi-colspan span.title-detail {
  width: 190px;
}

.tb_invoice .first-col {
  width: 190px !important;
}

@media (max-width: 1023px) {
  .tb_invoice #ord-products-articles .td-prd-ref-name-1, .tb_invoice #ord-products-articles .padding-top-5 {
    display: block !important;
  }
  .tb_invoice .ord-prd-row td {
    width: 100% !important;
  }
}

#table-date-montant {
  min-width: 0 !important;
}

@media (max-width: 767px) {
  #table-date-montant {
    max-width: 270px;
  }
  #table-date-montant tr, #table-date-montant td {
    width: 100% !important;
  }
  #table-date-montant th {
    display: none;
  }
  #table-date-montant td {
    text-align: left !important;
  }
  #table-date-montant td::before {
    display: inline-block;
    font-weight: bold;
    margin-right: 10px;
  }
  #table-date-montant td:first-child::before {
    content: 'Date : ';
  }
  #table-date-montant td:last-child::before {
    content: 'Montant : ';
  }
}

/* table Acomptes */
#table-acompte {
  width: 100%;
}

/* Tableau colis */
@media (max-width: 767px) {
  #tb-colis tr, #tb-colis td {
    width: 100% !important;
  }
  #tb-colis th {
    display: none;
  }
  #tb-colis td::before {
    display: inline-block;
    font-weight: bold;
  }
  #tb-colis td:first-child::before {
    content: 'Bon de livraison : ';
  }
  #tb-colis td:nth-child(2)::before {
    content: 'Service de livraison : ';
  }
  #tb-colis td:nth-child(3)::before {
    content: 'Numéro de colis : ';
  }
  #tb-colis td:last-child::before {
    content: 'Etiquette : ';
  }
}

/* Adresses de facturation/livraison */
#ord-adresses-row td#adresses {
  padding: 0;
  width: 100%;
}

#ord-adresses-row td#adresses #ord-addresses {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

#ord-adresses-row td#adresses #ord-addresses .th-info-client, #ord-adresses-row td#adresses #ord-addresses .th-user-adress-facture, #ord-adresses-row td#adresses #ord-addresses .th-user-adress-livraison {
  background: #DADCFF;
  padding: 5px 0 5px 10px;
  color: #232E63;
  font-size: 13px;
  font-weight: 600;
}

#ord-adresses-row td#adresses #ord-addresses .th-user-adress-facture, #ord-adresses-row td#adresses #ord-addresses .th-user-adress-livraison {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .th-user-adress-facture, #ord-adresses-row td#adresses #ord-addresses .th-user-adress-livraison {
    width: 100%;
    flex: 0 1 auto;
  }
}

#ord-adresses-row td#adresses #ord-addresses .th-user-adress-facture {
  order: 1;
}

#ord-adresses-row td#adresses #ord-addresses .th-user-adress-livraison {
  order: 2;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .th-user-adress-livraison {
    order: 5;
  }
}

#ord-adresses-row td#adresses #ord-addresses .th-info-client {
  width: 100%;
}

#ord-adresses-row td#adresses #ord-addresses #ord-addresses-compte-client {
  width: 100%;
  order: 3;
  padding: 5px 0 5px 10px;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses #ord-addresses-compte-client {
    order: 2;
  }
}

#ord-adresses-row td#adresses #ord-addresses #ord-addresses-compte-client div {
  display: inline-block;
}

#ord-adresses-row td#adresses #ord-addresses .adresse-facturation {
  order: 4;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .adresse-facturation {
    order: 3;
  }
}

#ord-adresses-row td#adresses #ord-addresses .adresse-facturation, #ord-adresses-row td#adresses #ord-addresses .adresse-livraison {
  padding: 5px 0 5px 10px;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .adresse-facturation, #ord-adresses-row td#adresses #ord-addresses .adresse-livraison {
    width: 100%;
  }
}

#ord-adresses-row td#adresses #ord-addresses .adresse-livraison {
  order: 5;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .adresse-livraison {
    order: 6;
  }
}

#ord-adresses-row td#adresses #ord-addresses .details-adresse {
  width: 100%;
  display: flex;
}

#ord-adresses-row td#adresses #ord-addresses #details-supp-email {
  width: 100%;
  display: flex;
  order: 6;
  padding: 5px 0 5px 10px;
  margin-top: 5px;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses #details-supp-email {
    order: 4;
  }
}

#ord-adresses-row td#adresses #ord-addresses .title-detail {
  display: inline-block;
  width: 190px;
}

@media screen and (max-width: 767px) {
  #ord-adresses-row td#adresses #ord-addresses .title-detail {
    width: 100px;
  }
}

#ord-adresses-row td#adresses #ord-addresses .td-engrenage {
  padding-top: 6px !important;
}

.ord-prd-info {
  padding-top: 8px;
  padding-left: 3px;
}

/* Liste des articles */
#ord-products-articles {
  max-width: 100%;
  table-layout: fixed;
  /* le td de la dernière roue crantée */
  /* Responsive */
}

#ord-products-articles th .th-prd-comment {
  min-width: 150px;
}

#ord-products-articles tbody tr.ord-prd-row {
  padding-right: 10px;
}

#ord-products-articles tbody tr.ord-prd-row td.td-engrenage:first-child {
  width: 20px !important;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-price-input {
  width: 80px;
  text-align: right;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-qte-input,
#ord-products-articles tbody tr.ord-prd-row td .ord-prd-discount-input {
  width: 50px !important;
  text-align: right;
}

#ord-products-articles tbody tr.ord-prd-row td .edit-cat {
  margin-left: 0;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-comment-input, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-name-input {
  width: 100% !important;
  min-width: 150px;
  max-width: 100%;
  display: block;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name {
  width: 100%;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row {
  width: 100%;
  display: flex !important;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main .td-prd-ref-name-1, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row .td-prd-ref-name-1 {
  word-break: break-word;
  padding-right: 3px;
  width: 125px !important;
  display: inline-block;
  vertical-align: top;
}

@media (max-width: 1023px) {
  #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main .td-prd-ref-name-1, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row .td-prd-ref-name-1 {
    width: 100px !important;
  }
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main .td-prd-designation, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row .td-prd-designation {
  display: inline-block;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main .td-prd-designation .prd-designation-title, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row .td-prd-designation .prd-designation-title {
  display: flex;
  align-items: center;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row-main .td-prd-designation .prd-designation-title .ord-prd-qte, #ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row .td-prd-designation .prd-designation-title .ord-prd-qte {
  width: 15px !important;
  text-align: right;
}

#ord-products-articles tbody tr.ord-prd-row td .ord-prd-ref-name .ord-prd-nmc-row {
  padding-left: 15px !important;
  padding-top: 12px !important;
  font-size: 10px;
}

@media screen and (max-width: 1023px) {
  #ord-products-articles tbody tr.ord-prd-row td.td-remise {
    display: flex;
    flex-wrap: wrap;
  }
  #ord-products-articles tbody tr.ord-prd-row td.td-remise .ord-prd-discount-input {
    margin-right: 3px;
  }
}

#ord-products-articles .ord-prd-discount-select {
  width: 100px;
}

#ord-products-articles .last-td {
  padding-left: 5px !important;
}

@media screen and (max-width: 1119px) {
  #ord-products-articles th,
  #ord-products-articles td,
  #ord-products-articles tr,
  #ord-products-articles .td-prd-ref-name-1,
  #ord-products-articles .td-prd-designation {
    display: flex;
    flex-wrap: wrap;
    width: 100% !important;
  }
  #ord-products-articles .thead-none {
    display: none;
  }
  #ord-products-articles td[data-label]::before, #ord-products-articles th[data-label]::before {
    content: attr(data-label);
  }
  #ord-products-articles td,
  #ord-products-articles tr {
    width: 100% !important;
    box-sizing: border-box;
  }
  #ord-products-articles td {
    text-align: left;
  }
  #ord-products-articles td .ord-prd-name-input {
    max-width: 100%;
  }
  #ord-products-articles td.td-prd-prix-unitaire::before, #ord-products-articles td.td-prd-quantite::before, #ord-products-articles td.td-prd-remise::before, #ord-products-articles td.td-prd-total::before {
    font-weight: bold;
    display: inline-block;
  }
  #ord-products-articles td.td-prd-prix-unitaire::before {
    content: 'Prix Unitaire : ';
    padding-right: 5px;
  }
  #ord-products-articles td.td-prd-quantite::before {
    content: 'Quantité : ';
    padding-right: 5px;
  }
  #ord-products-articles td.td-prd-remise::before {
    content: 'Remise : ';
    padding-right: 5px;
  }
  #ord-products-articles td.td-prd-total::before {
    content: 'Total : ';
    padding-right: 5px;
  }
  #ord-products-articles td.align-right {
    text-align: inherit !important;
  }
  #ord-products-articles tbody tr:nth-child(even) {
    background-color: #EEEEEE;
  }
}

#table-retours, #order-models {
  width: 100%;
}

#table-retours #ord-id, #order-models #ord-id {
  width: 50px;
}

#table-retours #ord-date, #order-models #ord-date {
  width: 200px;
}

#table-retours #ord-products, #table-retours #ord-ht, #table-retours #ord-ttc, #order-models #ord-products, #order-models #ord-ht, #order-models #ord-ttc {
  width: 160px;
}

.th-150 {
  width: 150px;
}

#table-champs-personnalises {
  width: 100%;
}

#table-champs-personnalises tr {
  vertical-align: middle !important;
}

#ord-products {
  width: 100%;
  margin: 0 !important;
  border-style: none !important;
}

#ord-products thead tr th {
  background-color: #EEEEEE !important;
}

#ord-products thead tr th .th-ord-prod-20 {
  width: 20px;
}

#ord-products thead tr th .th-ord-prod-125 {
  width: 125px;
}

#ord-products thead tr th #ord-pos {
  width: 40px;
}

.th-ord-prod-100 {
  width: 100px;
}

.td-padding-0 {
  padding: 0;
}

/* Models */
#table-propriete-model {
  width: 100%;
}

#table-propriete-model #td-propr-model {
  width: 150px;
}

.table-articles {
  width: 100%;
}

.table-articles .th-art-w20 {
  width: 20px;
}

.table-articles .th-art-w150 {
  width: 150px;
}

.table-articles .th-art-w180 {
  width: 180px;
}

.table-articles #ord-pos {
  width: 40px;
}

#autorization {
  width: 100%;
}

#autorization .th-autor-mod-1 {
  width: 20px;
}

#autorization .th-autor-mod-2 {
  width: 200px;
}

#autorization.ncmd_model_rights {
  margin-top: 10px;
}

#popup-content #tb-popup-catalogue th:nth-child(2) {
  padding-left: 0;
}

#popup-content #tb-popup-catalogue tbody tr td:nth-child(2) {
  padding-left: 0;
}

/* popup ncmd-right */
#popup-content .ncmd_model_rights select.little_input {
  width: 320px;
}

#popup-content .ncmd_model_rights .col_second {
  display: flex;
}

#popup-content .ncmd_model_rights .col_second .little_input[type="text"],
#popup-content .ncmd_model_rights .col_second .little_input[name="choose_new_usr"] {
  width: auto;
  margin-left: 10px;
}

#popup-content .ncmd_model_rights .col_second select.little_input {
  width: 280px;
}

/* Retours */
form#form-return #td-form-return-1 {
  width: 200px;
}

form#form-return #td-form-return-2 {
  width: 300px;
}

form#form-return #td-form-return-2 input {
  width: 150px;
}

.list {
  width: 100%;
}

.list #ord-id, .list #ord-ht, .list #ord-ttc {
  width: 100px;
}

.list #ord-date, .list #ord-products {
  width: 150px;
}

#popup-content pre {
  background-color: #EEEEEE;
  padding: 15px;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin-bottom: 10px;
}

/**
 * Zone "Pipeline commercial"
 */
.sign-rate-block, .sign-date-block {
  min-width: 300px;
  max-width: 400px;
}

.sign-rate-block #text-sign-date, .sign-rate-block #text-sign-rate, .sign-date-block #text-sign-date, .sign-date-block #text-sign-rate {
  font-style: italic;
  margin-bottom: 0 !important;
  text-align: right;
}

.sign-rate-block #slider-sign-rate, .sign-rate-block #slider-sign-date, .sign-date-block #slider-sign-rate, .sign-date-block #slider-sign-date {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  display: block;
  height: 24px;
  margin-top: .25rem;
  overflow: hidden;
  width: 100%;
}

.sign-rate-block #slider-sign-rate:focus, .sign-rate-block #slider-sign-date:focus, .sign-date-block #slider-sign-rate:focus, .sign-date-block #slider-sign-date:focus {
  outline: none;
}

.sign-rate-block #slider-sign-rate::-webkit-slider-runnable-track, .sign-rate-block #slider-sign-date::-webkit-slider-runnable-track, .sign-date-block #slider-sign-rate::-webkit-slider-runnable-track, .sign-date-block #slider-sign-date::-webkit-slider-runnable-track {
  -webkit-appearance: none;
          appearance: none;
  background: #606F7B;
  border: 0 solid #000;
  border-radius: 1px;
  box-shadow: 0 0 0 #000;
  cursor: pointer;
  height: 5px;
  width: 100%;
}

.sign-rate-block #slider-sign-rate::-webkit-slider-thumb, .sign-rate-block #slider-sign-date::-webkit-slider-thumb, .sign-date-block #slider-sign-rate::-webkit-slider-thumb, .sign-date-block #slider-sign-date::-webkit-slider-thumb {
  -webkit-appearance: none;
          appearance: none;
  background: #DE751F;
  border: 0 solid #2497E3;
  border-radius: 50%;
  box-shadow: 0 0 0 #000;
  cursor: pointer;
  height: 18px;
  margin-top: -6.5px;
  width: 18px;
}

.sign-rate-block #slider-sign-rate::-moz-range-track, .sign-rate-block #slider-sign-date::-moz-range-track, .sign-date-block #slider-sign-rate::-moz-range-track, .sign-date-block #slider-sign-date::-moz-range-track {
  background: #606F7B;
  border: 0 solid #000;
  border-radius: 1px;
  box-shadow: 0 0 0 #000;
  cursor: pointer;
  height: 5px;
  width: 100%;
}

.sign-rate-block #slider-sign-rate::-moz-range-thumb, .sign-rate-block #slider-sign-date::-moz-range-thumb, .sign-date-block #slider-sign-rate::-moz-range-thumb, .sign-date-block #slider-sign-date::-moz-range-thumb {
  background: #DE751F;
  border: 0 solid #2497E3;
  border-radius: 50%;
  box-shadow: 0 0 0 #000;
  cursor: pointer;
  height: 18px;
  width: 18px;
}

.sign-rate-block #slider-sign-rate::-ms-track, .sign-rate-block #slider-sign-date::-ms-track, .sign-date-block #slider-sign-rate::-ms-track, .sign-date-block #slider-sign-date::-ms-track {
  background: transparent;
  border-color: transparent;
  color: transparent;
  cursor: pointer;
  height: 5px;
  width: 100%;
}

.sign-rate-block #slider-sign-rate::-ms-fill-lower, .sign-rate-block #slider-sign-date::-ms-fill-lower, .sign-date-block #slider-sign-rate::-ms-fill-lower, .sign-date-block #slider-sign-date::-ms-fill-lower {
  background: #DE751F;
  border: 0px solid #000;
  border-radius: 2px;
  box-shadow: 0px 0px 0px #000;
}

.sign-rate-block #slider-sign-rate::-ms-fill-upper, .sign-rate-block #slider-sign-date::-ms-fill-upper, .sign-date-block #slider-sign-rate::-ms-fill-upper, .sign-date-block #slider-sign-date::-ms-fill-upper {
  background: #606F7B;
  border: 0px solid #000;
  border-radius: 2px;
  box-shadow: 0px 0px 0px #000;
}

.sign-rate-block #slider-sign-rate::-ms-thumb, .sign-rate-block #slider-sign-date::-ms-thumb, .sign-date-block #slider-sign-rate::-ms-thumb, .sign-date-block #slider-sign-date::-ms-thumb {
  background: #DE751F;
  border: 0px solid #2497E3;
  border-radius: 50%;
  box-shadow: 0px 0px 0px #000;
  cursor: pointer;
  height: 18px;
  margin-top: 1px;
  width: 18px;
}

.sign-rate-block #slider-sign-rate:focus::-ms-fill-lower, .sign-rate-block #slider-sign-rate::-moz-range-progress, .sign-rate-block #slider-sign-date:focus::-ms-fill-lower, .sign-rate-block #slider-sign-date::-moz-range-progress, .sign-date-block #slider-sign-rate:focus::-ms-fill-lower, .sign-date-block #slider-sign-rate::-moz-range-progress, .sign-date-block #slider-sign-date:focus::-ms-fill-lower, .sign-date-block #slider-sign-date::-moz-range-progress {
  background: #DE751F;
}

.sign-rate-block #slider-sign-rate:focus::-ms-fill-upper, .sign-rate-block #slider-sign-rate::-moz-range-track, .sign-rate-block #slider-sign-date:focus::-ms-fill-upper, .sign-rate-block #slider-sign-date::-moz-range-track, .sign-date-block #slider-sign-rate:focus::-ms-fill-upper, .sign-date-block #slider-sign-rate::-moz-range-track, .sign-date-block #slider-sign-date:focus::-ms-fill-upper, .sign-date-block #slider-sign-date::-moz-range-track {
  background: #606F7B;
}

/**
 * Règles CSS pour la version imprimable
 */
@media print {
  #table-une-commande tbody tr > td:not(.multi-colspan):not(.td-engrenage):not(#adresses):first-child {
    width: 190px !important;
  }
  #table-une-commande td.large {
    width: auto;
  }
  #table-une-commande #ord-adresses-row td#adresses #ord-addresses .title-detail {
    width: 100px;
    margin-right: 3px;
  }
  #table-une-commande .bloc-ghost {
    width: 0;
  }
  #table-une-commande #ord-products-articles .th-prd-comment {
    width: 220px !important;
  }
  #table-une-commande #ord-products-articles .col80px {
    width: 80px !important;
    padding-right: 10px;
  }
  #table-une-commande #ord-products-articles .col100px {
    width: 100px !important;
  }
  #table-une-commande #ord-products-articles .td-prd-ref-name-1, #table-une-commande #ord-products-articles .td-remise.td-prd-remise {
    width: 100px !important;
  }
  #table-une-commande #ord-products-articles .ord-pos.col40px, #table-une-commande #ord-products-articles th:last-child {
    width: 0px;
    padding: 0 !important;
    padding-right: 0 !important;
  }
  #table-une-commande #ord-products-articles .ref-des .col125px {
    width: 100px !important;
  }
  #table-une-commande #ord-products-articles .ria-cell-move {
    padding: 0 !important;
  }
  #table-une-commande #ord-products-articles .ria-cell-move * {
    display: none !important;
  }
  #table-une-commande #ord-products-articles .discount {
    width: 135px !important;
    text-align: center !important;
  }
  #table-une-commande #ord-products-articles .ord-prd-row:last-of-type {
    border-bottom: 1px solid grey !important;
  }
  #table-une-commande #ord-products-articles tfoot {
    border-top: none;
  }
  #table-une-commande #ord-products-articles tfoot tr {
    display: flex !important;
    width: 100% !important;
  }
  #table-une-commande #ord-products-articles tfoot th.align-right, #table-une-commande #ord-products-articles tfoot td.multi-colspan {
    width: auto !important;
    display: inline-block !important;
    border: none;
  }
  #table-une-commande .ord-action-reward {
    text-align: left;
  }
  #table-champs-personnalises #model-pick {
    vertical-align: middle;
  }
  #table-champs-personnalises > tbody > tr > td, #table-une-commande > tbody > tr > td {
    width: auto !important;
    display: inline-block !important;
  }
  #table-champs-personnalises td, #table-champs-personnalises .ord-prd-row, #table-champs-personnalises #ord-products-articles tr, #table-une-commande td, #table-une-commande .ord-prd-row, #table-une-commande #ord-products-articles tr {
    page-break-inside: avoid;
  }
}

/**
 * CSS des tableaux des promotions
 */
/* Tableaux de Promotions > Promotions sur les produits */
/* Tableau Promotions */
#pmt-cdt {
  position: relative;
  max-width: 1266px;
}

#pmt-cdt .button-new-promotion {
  position: absolute;
  top: 0;
  right: 0;
}

@media (max-width: 1024px) {
  #pmt-cdt .button-new-promotion {
    position: static;
  }
}

.link-modif-groupe {
  width: 500px;
  max-width: 100%;
  display: inline-block;
}

.prc-tva-eco .information {
  width: 500px;
}

.prc-tva-eco .information .info select, .prc-tva-eco .information .info input:not([type="checkbox"]) {
  flex: 1 1 auto;
}

.prc-tva-eco .information .info input {
  margin-left: 5px;
}

.prc-tva-eco .information .info input[type=checkbox] {
  margin-bottom: 2px;
  margin-top: 3px;
}

.prc-tva-eco .information .info select {
  margin-bottom: 3px;
  margin-left: 5px;
  width: 110px;
}

.prc-tva-eco .info {
  display: flex;
  justify-content: flex-start;
  align-content: center;
  flex-wrap: wrap;
  vertical-align: middle;
  line-height: 20px;
}

.prc-tva-eco .info label, .prc-tva-eco .info .info-fld {
  font-weight: 600;
  height: 32px;
}

.prc-tva-eco .info label.lbl-info-cdt, .prc-tva-eco .info .info-fld.lbl-info-cdt {
  width: auto;
}

.prc-tva-eco .info label, .prc-tva-eco .info .info-fld {
  padding-top: 5px;
}

.prc-tva-eco .conditions-prc {
  width: 550px;
}

.prc-tva-eco .info label, .prc-tva-eco .info .bold {
  width: 110px;
}

.prc-tva-eco input.date {
  width: 80px;
}

.prc-tva-eco input.hour {
  width: 45px !important;
}

.prc-tva-eco input.prc-name {
  width: 225px;
}

.prc-tva-eco input[type=text] {
  width: 107px;
}

.prc-tva-eco div.val-fld-date {
  margin-bottom: 5px;
  margin-left: 11px;
}

.prc-tva-eco div.val-fld-date select {
  float: none;
  margin-right: 10px;
}

.prc-tva-eco div.save-load {
  clear: both;
  display: none;
  text-align: center;
}

.prc-tva-eco div.save-load img {
  border: none;
}

.prc-tva-eco div.error {
  background-color: #ffdddd;
  border: 1px solid #ff0000;
  color: #000;
  margin-bottom: 10px;
  margin-top: -5px;
  padding: 1em;
}

.prc-tva-eco div.error-success {
  margin-top: -5px;
}

.prc-tva-eco div.bold {
  border-bottom: 1px solid #A9A9A9;
  font-weight: 600 !important;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.prc-tva-eco select.sbl-select {
  margin-right: 5px;
}

.prc-tva-eco .info-bulle {
  padding: 0 3px;
}

.prc-tva-eco tbody {
  transition: opacity 0.5s;
}

.prc-tva-eco tbody td {
  border: 1px solid #A9A9A9;
}

.prc-tva-eco tbody td.conditions, .prc-tva-eco tbody td.action, .prc-tva-eco tbody td.information {
  padding: 15px 10px 10px 10px !important;
}

.prc-tva-eco tbody td.action {
  vertical-align: middle;
  text-align: center;
  width: auto !important;
}

@media (max-width: 768px) {
  .prc-tva-eco tbody td.action {
    width: 100% !important;
  }
}

.prc-tva-eco tbody td.cdt-center {
  padding: 10px;
  vertical-align: middle;
}

.prc-tva-eco tbody td.val select {
  width: 110px;
}

.prc-tva-eco tbody td.conditions img {
  border: medium none;
  float: left;
  margin-top: 5px;
}

.prc-tva-eco tbody td.conditions img.del-cdt {
  cursor: pointer;
  float: right;
  margin-left: 5px;
}

.prc-tva-eco tbody td.conditions img.del-cdt2 {
  margin-top: 5px;
}

.prc-tva-eco tbody td.conditions [type=radio] {
  margin-top: 8px;
  width: 25px;
}

.prc-tva-eco tbody td.conditions .date {
  width: 75px;
}

.prc-tva-eco tbody td.conditions input:not(.action) {
  width: 100px;
}

.prc-tva-eco tbody td.or-cdt {
  background-color: #E3E3E3;
  border: 1px solid #A9A9A9;
  font-weight: 600;
  padding: 5px;
  text-align: center;
}

.prc-tva-eco .display-flex {
  align-items: flex-start;
  border-bottom: 1px solid #A9A9A9;
  margin-bottom: 5px;
  padding-bottom: 5px;
}

.prc-tva-eco .display-flex .display-flex {
  flex: 1 1 auto;
  flex-wrap: wrap;
  align-content: stretch;
  border-bottom: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.prc-tva-eco .display-flex .display-flex .conditions-next {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 98%;
}

.prc-tva-eco .display-flex .display-flex .conditions-next > * {
  width: 50%;
}

.prc-tva-eco .display-flex .display-flex .fld {
  width: 98%;
  max-width: 100%;
}

.prc-tva-eco select:disabled, .prc-tva-eco input:disabled {
  color: #545454;
}

.prc-tva-eco .datepicker {
  width: 100px !important;
}

.prc-tva-eco .ac_back_shadow {
  background-image: none;
  background-repeat: no-repeat;
  min-height: 185px !important;
  min-width: 500px !important;
  padding-bottom: 3px;
  padding-right: 3px;
}

.prc-tva-eco .ac_results {
  background-color: #fff;
  border: 1px solid #A9A9A9;
  min-height: 180px !important;
  overflow: hidden;
  padding: 0;
  width: 495px !important;
  z-index: 99999;
}

@media (max-width: 1024px) {
  .prc-tva-eco tr {
    border-bottom: none !important;
  }
  .prc-tva-eco tr:nth-child(odd) {
    background-color: #EEEEEE;
  }
  .prc-tva-eco td {
    width: 100% !important;
  }
  .prc-tva-eco td input:not([type="checkbox"]) {
    width: auto !important;
  }
  .prc-tva-eco .information .info select {
    width: 110px !important;
  }
}

@media (max-width: 425px) {
  .prc-tva-eco .info {
    flex-wrap: wrap;
  }
  .prc-tva-eco .info label:not(.lbl-info-cdt) {
    width: 100% !important;
  }
  .prc-tva-eco .info label:not(.lbl-info-cdt) .lbl-info-cdt {
    width: auto !important;
  }
  .prc-tva-eco .info select, .prc-tva-eco .info input {
    margin: 2px 0;
  }
}

form#pmt-search {
  margin-bottom: 10px;
  /* Flex */
}

form#pmt-search .block-flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

form#pmt-search .block-flex-wrap .div-like {
  flex-wrap: nowrap !important;
}

form#pmt-search .block-flex-wrap .child-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  max-width: 415px;
}

form#pmt-search .block-flex-wrap .child-flex [for="date_start"] {
  padding-top: 0;
}

form#pmt-search label:first-child {
  width: 215px;
  padding-top: 5px;
}

form#pmt-search .left a {
  line-height: 35px;
}

form#pmt-search #like {
  margin-right: 5px;
}

form#pmt-search #search {
  width: 100%;
}

form#pmt-search select, form#pmt-search input[type=text] {
  width: auto;
  vertical-align: middle !important;
}

#groupspromotionpicker .selectorview a.btn img {
  width: 16px;
  height: 8px;
}

/* Tableau Nouvelle promotions */
form#frm-add-promo table#promo-info {
  width: 600px;
}

form#frm-add-promo table#promo-info .td-promo-info {
  width: 300px;
}

form#frm-add-promo table#promo-info .td-promo-info :first-child {
  float: left;
}

form#frm-add-promo div.notice {
  width: 595px;
  margin-bottom: 5px;
}

form#frm-add-promo table#tb-promo-remise thead tr th {
  width: 600px;
}

form#frm-add-promo table#tb-promo-remise td#grp-prd-promo {
  display: none;
}

form#frm-add-promo table#tb-promo-remise tfoot tr td input#add-prd-promo {
  float: left;
}

form#frm-add-promo table#tb-prd-promo #prd-ref {
  width: 300px;
}

form#frm-add-promo table#tb-prd-promo #prd-remise {
  width: 200px;
}

form#frm-add-promo table#tb-prd-promo #prd-remise-val {
  width: 100px;
}

form#frm-add-promo img.del {
  border: none;
  float: left;
  margin-right: 5px;
  margin-top: 3px;
  width: 10px;
  cursor: pointer;
}

/* codes promo */
.form-promo-specials {
  width: 825px;
}

.form-promo-specials table#table-codes-promo {
  width: 100%;
}

.form-promo-specials table#table-codes-promo #pmt-select {
  width: 25px;
}

.form-promo-specials table#table-codes-promo #pmt-code {
  width: 80px;
}

.form-promo-specials table#table-codes-promo #pmt-state {
  width: 145px;
}

.form-promo-specials table#table-codes-promo .opened {
  background-color: #ebffeb;
}

.form-promo-specials table#table-codes-promo .incoming {
  background-color: #F4F7FB;
}

.export-button {
  padding-bottom: 5px;
  margin-bottom: 0;
}

table#pmt-special {
  width: 100%;
}

table#pmt-special tbody .td-pmt-spec {
  width: 190px;
}

table#pmt-special .cdt-grp-rule-items {
  width: auto !important;
}

table#pmt-special * {
  vertical-align: middle;
}

table#pmt-special #gen-code {
  float: none;
}

table#pmt-special .pmt-list-service {
  list-style: none;
  margin: 0 0 10px 0 !important;
}

table#pmt-special .pmt-list-service li {
  margin: 0 !important;
}

table#pmt-special #pmt-cmt {
  width: 100%;
}

table#pmt-special fieldset.cdt-grp {
  padding: 10px 0 !important;
  margin: 10px 0;
}

table#pmt-special fieldset.cdt-grp > .cdt-intro {
  padding: 0 10px 10px 10px;
}

table#pmt-special fieldset.cdt-grp div.cdt-config {
  padding: 5px 30px 5px 10px;
}

table#pmt-special fieldset.cdt-grp div.cdt-config:nth-child(odd) {
  background-color: #EEEEEE;
}

table#pmt-special fieldset.cdt-grp div.cdt-config:last-child {
  border-bottom: 1px solid grey;
}

table#pmt-special fieldset.cdt-grp div.cdt-config .cdt-psy-val {
  position: static !important;
}

table#pmt-special fieldset.cdt-grp div.cdt-config .cdt-psy-val .cdt-grp-del {
  margin-right: 10px;
}

table#pmt-special fieldset.cdt-grp div.cdt-config .cdt-psy-val select {
  margin-top: 0;
}

table#pmt-special fieldset.cdt-grp input.btn-action-small {
  margin: 10px 10px 0 10px;
}

table#pmt-special input.btn-action-small {
  float: right;
}

table#pmt-special .cdt-grp-rule {
  width: auto !important;
}

table#pmt-special .button-del-group {
  margin: 10px 0 0 10px;
  float: left;
}

#tb-tabProducts, #tb-tabCustomers {
  width: 650px;
  min-width: 0 !important;
}

#tb-tabProducts fieldset#pmt-add-rule label, #tb-tabCustomers fieldset#pmt-add-rule label {
  display: inline-block;
  width: 165px;
  max-width: 100%;
}

#tb-tabProducts fieldset#pmt-add-rule input[type="text"], #tb-tabCustomers fieldset#pmt-add-rule input[type="text"] {
  width: auto !important;
}

#tb-tabProducts table {
  min-width: 0 !important;
}

#tb-tabProducts .pmt-rules select, #tb-tabProducts .pmt-rules [type="text"], #tb-tabProducts .pmt-rules td {
  vertical-align: middle !important;
}

@media (max-width: 1024px) {
  #tb-tabProducts .pmt-rules .tb-colisage {
    width: 100%;
    display: block !important;
    max-width: 100%;
  }
  #tb-tabProducts .pmt-rules .tb-colisage tr {
    display: block;
    border-bottom: 0 !important;
  }
  #tb-tabProducts .pmt-rules .tb-colisage tr td {
    display: inline-block;
  }
}

#tb-tabProducts .pmt-rules select {
  width: auto !important;
}

@media (max-width: 1023px) {
  #tb-tabProducts input[type="button"] {
    width: auto !important;
  }
}

div#tb-tabVariations table {
  width: 500px;
}

div#tb-tabVariations table th#var-del {
  width: 25px;
}

table.tb-tabStats {
  width: 775px;
}

table.tb-tabStats th {
  text-align: left;
}

table.tb-tabStats th:not(:first-child) {
  width: 90px;
}

#tb-tabSerie #pmt-select {
  width: 25px;
}

#tb-tabSerie #pmt-serie-etat, #tb-tabSerie #pmt-serie-cmd {
  width: 90px;
}

table.pmt-rules {
  width: 100%;
}

/**
 * CSS de la page Clients > Segments
 */
/* Tableau de Segmentation */
#obj-seg #seg-del {
  width: 25px;
}

#obj-seg #seg-name, #obj-seg #seg-desc, #obj-seg #seg-objects {
  width: 200px;
}

#obj-seg #type-pos {
  width: 75px;
}

@media (max-width: 1023px) {
  #obj-seg #seg-del {
    width: 100% !important;
  }
  #obj-seg .centertd {
    text-align: left !important;
  }
}

/* Tableau de Création d'un nouveau Segmentation */
#table-creation-new-segment #td-creat-new-segment {
  width: 140px;
}

#form-segment .riapicker .selectorview {
  height: 17px;
  padding: 2px;
  padding-right: 0;
  border: solid #848484 1px;
  width: 266px;
  height: 24px;
  border-radius: 6px;
  background-image: url("/admin/dist/images/input-select.svg");
  background-position: right -1px top -4px;
  background-repeat: no-repeat;
  background-size: 29px 29px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding-right: 30px !important;
}

#form-segment .riapicker .selectorview::-ms-expand {
  display: none;
}

/**
 * CSS de l'onglet configuration
 */
/* Information propriétaires */
table.w-600 tbody td:first-child {
  width: 175px;
}

@media (max-width: 1024px) {
  table.w-600 tbody td:first-child {
    width: auto;
  }
}

table.w-600 input {
  vertical-align: middle;
}

/* Contacts Propriétaire */
#table-config-contact #cnt-sel {
  width: 25px;
}

#table-config-contact #cnt-name {
  width: 250px;
}

#table-config-contact #cnt-email {
  width: 175px;
}

#table-config-contact #cnt-phone, #table-config-contact #cnt-fax {
  width: 115px;
}

#table-config-contact #cnt-types {
  width: 225px;
}

/* Types de contacts */
#table-config-type-contact {
  width: 650px;
}

#table-config-type-contact #type-sel {
  width: 25px;
}

#table-config-type-contact #type-name {
  width: 175px;
}

#table-config-type-contact #type-cnt {
  width: 100px;
}

#table-conf-contact #cnt-sel {
  width: 25px;
}

#table-conf-contact #cnt-name {
  width: 250px;
}

#table-conf-contact #cnt-email {
  width: 175px;
}

#table-conf-contact #cnt-phone, #table-conf-contact #cnt-fax {
  width: 115px;
}

#table-conf-contact #cnt-types {
  width: 225px;
}

/* Zones de livraison */
#table-config-zones #name, #table-config-services #name {
  width: 325px;
}

#table-config-zones #is-active, #table-config-services #is-active {
  width: 100px;
}

/* Services de livraison */
#table-conf-livr-service-edit tbody tr td.delais input.qte {
  width: 40px !important;
  text-align: right;
}

#table-conf-livr-service-edit tbody tr input[type="text"] {
  vertical-align: middle;
}

@media (max-width: 1023px) {
  #table-conf-livr-service-edit tbody tr .align-right, #table-conf-livr-service-edit tbody tr .align-center {
    text-align: left !important;
  }
}

#table-conf-livr-service-edit tr.th-head-second ~ tr:not(:last-child) {
  border-bottom: 1px solid #A9A9A9;
}

/* Magasins */
#list-stores #ref {
  width: 125px;
}

#list-stores #name {
  width: 225px;
}

#list-stores #address, #list-stores #contact {
  width: 275px;
}

#tb-day-exp {
  width: 625px;
  margin-bottom: 0 !important;
}

#tb-day-exp #th-width-day-exp th {
  width: 125px;
  background-color: #F4F7FB;
}

#tb-day-exp #th-width-day-exp th:first-child {
  width: 95px;
}

#tb-day-exp .form-control {
  width: 50px !important;
}

#hr-magasin-stat {
  margin-top: 50px;
  margin-bottom: 50px;
}

#edit-employee tr:first-child td:first-child {
  width: 150px;
}

.employee-image {
  min-height: 165px;
}

#site-content #employees {
  margin: 10px 0;
  list-style: none;
  font-size: 0;
}

#site-content #employees li {
  display: inline-block;
  vertical-align: middle;
  margin: 10px;
  font-size: 12px;
}

#site-content #employees .employee {
  min-height: 64px;
  min-width: 64px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

#site-content #employees .employee-info {
  padding: 10px;
}

#site-content #employees .employee-info .employee-edit {
  display: block;
  margin-top: 10px;
  text-align: center;
}

#site-content #employees .del-selected {
  box-shadow: #ABB2FF 0px 0px 10px, 0 3px 6px rgba(0, 0, 0, 0.23);
}

#site-content #employees .employee-image {
  margin: 15px auto;
  width: 150px;
  cursor: pointer;
}

#site-content #employees .employee-image img {
  margin-top: 15px;
}

#site-content #employees .employee-add {
  display: block;
  background: #e0e0e0;
  color: #0e90d2;
  border-radius: 50%;
  padding: 30px 15px;
  text-decoration: none;
}

#site-content #employees .employee-add:hover {
  background: #c0c0c0;
}

#site-content .list-shop-image li,
#popup-content .list-shop-image li {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  position: relative;
}

#site-content .list-shop-image li p,
#popup-content .list-shop-image li p {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  padding: 5px 0;
  text-align: center;
}

/* Services / postes */
#table-services thead th#cat-sel, #table-postes thead th#cat-sel {
  width: 20px;
}

#table-services thead th:last-child, #table-postes thead th:last-child {
  width: 175px;
}

#table-services-edit tbody th:first-child td:first-child, #table-poste-edit tbody th:first-child td:first-child {
  width: 100px;
}

/* Dépôts */
#table-deposits thead th#cat-sel {
  width: 25px;
}

#table-deposits thead th#cat-name {
  width: 425px;
}

#table-deposits thead th#cat-principal {
  width: 100px;
}

#table-services-edit tbody th:first-child td:first-child, #table-poste-edit tbody th:first-child td:first-child, #table-fiche-classe tbody th:first-child td:first-child {
  width: 100px;
}

/* Gestion des retours */
#table-reasons thead th:first-child {
  width: 25px;
}

#table-reasons thead th#reason-name {
  width: 525px;
}

#table-reasons thead th#cat-publish {
  width: 50px;
}

/* Structure des données > classes */
#table-config-classes #cls-check {
  width: 25px;
}

#table-config-classes #cls-name {
  width: 300px;
}

#table-config-classes #cls-nb-fld {
  width: 100px;
}

#table-config-classes #cls-nb-obj {
  width: 125px;
}

#table-fiche-classe tbody td:first-child td:last-child, #fields-classe-object tbody td:first-child td:last-child {
  width: 200px;
}

#table-fiche-classe tbody td #parent-name, #fields-classe-object tbody td #parent-name {
  width: 311px;
}

#fields-classe-object tbody td {
  vertical-align: middle;
}

#fields-classe-objects #obj-check {
  width: 25px;
}

#fields-classe-objects #obj-name {
  width: 220px;
}

#fields-classe-objects #obj-childs {
  width: 105px;
}

/* Models */
#models #name {
  width: 325px;
}

#models #fields, #models #objects {
  width: 125px;
}

#fields #fld-select {
  width: 25px;
}

#fields #fld-name, #fields #fld-type, #fields #fld-cat {
  width: 275px;
}

#fields #fld-pos {
  width: 100px;
}

/* Champs Personnalisés */
#table-champs-personnalises thead tr th {
  width: 300px;
}

.tb-new-custom-field {
  width: 670px;
}

/* Champs Presonnalisés / Unités de mesure */
#categories #name, #table-units #name {
  width: 325px;
}

#categories #pos, #categories #symbol, #table-units #pos, #table-units #symbol {
  width: 75px;
}

#categories tbody td:last-child:not(:only-child), #table-units tbody td:last-child:not(:only-child) {
  text-align: center;
}

/* Segments */
#obj-seg thead tr th {
  width: 200px;
}

#obj-seg thead tr th:first-child {
  width: 25px;
}

#obj-seg thead tr th:last-child {
  width: 75px;
}

@media (max-width: 1023px) {
  #obj-seg thead tr th:first-child {
    width: 100%;
  }
}

#form-segment tbody tr:first-child td:first-child {
  width: 140px;
}

/* CGV */
#site-content table#table-cgv #cgv-sel {
  width: 25px;
}

#site-content table#table-cgv #cgv-name {
  width: 450px;
}

#site-content table#table-cgv #cgv-pub {
  width: 175px;
}

#site-content table#table-cgv .current {
  font-weight: bold;
}

#site-content table#table-fiche-version {
  width: 768px;
}

#site-content table#table-fiche-version .publish > td + td {
  display: flex;
}

#site-content table#table-fiche-version .publish > td + td > * + * {
  margin-left: 15px;
}

#site-content table#cgv {
  width: 768px;
}

#site-content table#cgv #art-sel {
  width: 25px;
}

#site-content table#cgv .td-art-name {
  width: 450px;
}

#site-content table#cgv .td-art-move {
  width: 50px;
}

#table-cgv-edit-article tbody tr.first td:first-child {
  width: 150px;
}

/* Paramètres de tarification */
#table-param-cat-tarifaires #prc-sel, #table-param-cat-tarifaires #prd-sel, #table-produits-exclure #prc-sel, #table-produits-exclure #prd-sel {
  width: 25px;
  padding-top: 3px;
}

#table-param-cat-tarifaires #prc-name, #table-produits-exclure #prc-name {
  width: 400px;
}

#table-param-cat-tarifaires #prc-ttc, #table-produits-exclure #prc-ttc {
  width: 75px;
}

#table-param-cat-tarifaires #prc-users, #table-produits-exclure #prc-users {
  width: 100px;
}

#table-produits-exclure {
  width: 625px;
}

#table-cat-tarifaire-site td {
  width: 200px;
}

.prc-default-options {
  margin-top: 12px;
  margin-bottom: 12px;
}

#prc-default {
  margin-top: 12px;
}

#table-type-relations #rel-type-sel, #table-type-relations td:first-child {
  width: 25px;
}

#table-type-relations #rel-type-nom {
  width: 250px;
}

#table-type-relations #rel-type-nom-pluriel {
  width: 100px;
}

/* Adresses emails */
/* Catalogue */
#table-config-type-relation .tdleft {
  width: 145px;
}

/* Référencement */
#form-referencement-site #table-conf-referencement tbody tr:first-child th:first-child {
  width: 150px;
}

#form-referencement-site #table-conf-referencement tbody tr:first-child th:last-child {
  width: 300px;
}

#form-referencement-site .label-option {
  margin-right: 10px;
}

#form-referencement-site #custom-p {
  margin-top: 30px;
}

#conf-stats thead th:first-child {
  width: 20px;
}

#conf-stats thead th:last-child {
  width: 400px;
}

#table-edit-personnalisation tbody tr:last-child td {
  width: 200px;
}

#table-edit-personnalisation tbody tr:last-child td:first-child {
  width: 200px;
}

#class-select label, #tag-select label {
  width: 130px;
  display: inline-flex;
}

#edition-table {
  width: 705px;
  max-width: 100%;
}

#edition-table tbody {
  padding-right: 10px;
}

#edition-table tbody td {
  max-width: 100%;
}

#edition-table tbody td select#constant-selector {
  height: 100% !important;
  background-image: none !important;
  overflow: hidden;
  width: 200px !important;
}

#edition-table tbody td #sentence {
  width: 400px;
}

#edition-table tbody td #save-column {
  width: 50px;
}

#edition-table tbody td:last-child {
  width: 200px;
}

#sentences-table {
  margin-top: 10px;
}

#sentences-table tr {
  border-collapse: collapse;
}

#sentences-table tr td.sentence-text {
  width: 600px;
  border-right: 1px solid #A9A9A9;
  padding: 10px;
  vertical-align: middle;
}

@media (max-width: 1023px) {
  #sentences-table tr td.sentence-text {
    border-right: 0;
  }
}

#sentences-table tr td.td-action {
  width: 104px;
  text-align: center;
}

@media (max-width: 1023px) {
  #sentences-table tr:last-child {
    border-bottom: 0;
  }
}

/* Redirections */
#search-url {
  margin-bottom: 10px;
}

#lst-error-404 {
  width: 100%;
}

#lst-error-404 #url {
  width: 325px;
}

#lst-error-404 #count {
  width: 95px;
}

#lst-error-404 #site {
  width: 120px;
}

#lst-error-404 #lng {
  width: 95px;
}

#lst-error-404 tr {
  vertical-align: middle !important;
}

#lst-error-404 tr img {
  border: medium none;
  cursor: pointer;
  width: 16px;
  height: 16px;
  margin: 5px 0 0 4px;
  vertical-align: middle;
  display: inline-block;
}

@media (max-width: 1023px) {
  #lst-error-404 tr img {
    margin: 5px;
  }
}

#lst-error-404 tr input[type=text] {
  width: calc(100% - 20px);
  display: inline-block;
  float: left;
  vertical-align: middle;
}

#lst-error-404 tr.pos-center {
  text-align: center;
}

#lst-error-404 .error-resolved {
  vertical-align: middle;
  text-align: center;
}

#lst-redirections {
  width: 100%;
}

#lst-redirections #titleRedirection {
  margin-top: 5px;
}

#sh-redirection select#type {
  width: auto;
}

/* Horaires d'expéditions */
.tb-day-exp1 thead tr th#day-exp {
  width: 75px;
}

.tb-day-exp1 thead tr th#period-exp {
  width: 220px;
}

.tb-day-exp1 thead tr th#action-exp {
  width: 80px;
}

#tb-holidays thead tr#th-year th {
  width: 200px;
}

#tb-closing #clg-start, #tb-closing #clg-end {
  width: 130px;
}

#tb-closing #clg-action {
  width: 20px;
}

#tb-closing td.td-info, #tb-closing td.td-date {
  vertical-align: middle;
}

/* Traduction */
#div-orange {
  padding-bottom: 5px;
  margin-bottom: 10px;
}

#tb-translate #tsk_checked {
  width: 20px;
}

#tb-translate #tsl_context {
  width: 50px;
}

#tb-translate #tsl_original, #tb-translate #tsl_translate {
  width: 250px;
}

#tb-translate #action {
  width: 20px;
}

#tb-translate thead tr:last-child th {
  background-color: #F4F7FB;
}

/* modification des inputs trop grands */
dd input.col-numeric, input#min_amount, input#days_valid, select#apply_on {
  width: auto;
}

/* Règlements par virement */
#tb-transfer tbody#transfert-positif tr:first-child td:first-child {
  width: 180px;
}

#tb-transfer tbody#transfert-positif tr:first-child td:nth-child(2) {
  width: 500px;
}

#tb-transfer tbody #trf-delete {
  width: 20px;
}

#tb-transfer tbody #trf-describe {
  width: 400px;
}

/* Adresses IP Filtrées */
#tb-ip-filtrees #flt-check {
  width: 25px;
}

#tb-ip-filtrees #flt-name {
  width: 300px;
}

#tnt-filters tbody tr:first-child td:first-child {
  width: 150px;
}

#tnt-filters tbody tr:first-child td:nth-child(2) {
  width: 300px;
}

/* Cartes cadeaux */
#list-prd #check-del {
  width: 28px;
}

#list-prd #prd-title {
  width: 475px;
}

#list-prd #prd-amount {
  width: 100px;
}

/* Campagnes */
#site-content #table-dob tr.details td:first-child {
  width: 230px;
}

#site-content #table-dob tr.details td:nth-child(2) {
  width: 400px;
}

#site-content #table-dob tr td select#select-discount-type {
  width: 100px;
}

#site-content #table-dob tr td.pmt-off-srv {
  padding-top: 10px;
}

#site-content #table-dob tr td select {
  width: 200px;
}

#site-content #table-dob tr td fieldset {
  margin: 10px;
}

#site-content #table-dob tr td fieldset div input {
  width: 200px;
}

#site-content #table-dob tr ul {
  margin-top: 0px;
}

#site-content #table-dob tr ul.pmt-list-service li {
  list-style: none;
  margin: 0px;
}

#site-content #table-dob tr .pmt-list-choose {
  padding: 3px 10px !important;
}

#site-content #table-dob tr .pmt-list-discount {
  padding: 0 !important;
}

/* Instagram */
#formulaire_identifiants_instagram label {
  width: 135px;
  display: inline-block;
}

#formulaire_identifiants_instagram input {
  width: 170px;
}

/* Avis Vérifiés */
#formulaire_identifiants_avis_verifie label:first-child {
  width: 200px;
  display: inline-block;
}

.config-generation-pdf * {
  vertical-align: middle !important;
}

.config-generation-pdf .pdf-header label {
  display: inline-block;
  width: 80px;
}

.config-generation-pdf .pdf-logo label {
  display: inline-block;
  width: 155px;
}

.config-generation-pdf .config-row {
  margin-top: 15px;
}

.config-generation-pdf img {
  margin-bottom: 10px;
}

#config-pdf-quote {
  clear: both;
}

#config-pdf-quote .notice {
  margin: 0 0 1rem 0;
  padding: 1rem;
}

#config-pdf-quote .notice:first-of-type {
  margin-bottom: 1rem;
}

#config-pdf-quote .default-site-url {
  font-weight: 600;
}

#config-pdf-quote .flex-column {
  flex-direction: column;
}

#config-pdf-quote .form-group {
  align-items: center;
  display: flex;
}

#config-pdf-quote .form-group label {
  margin-right: 0.5rem;
}

#config-pdf-quote .logo-form-group {
  margin-top: 1rem;
}

#form-delivery-options dl .description {
  color: #606F7B;
  margin-bottom: 0.75rem;
}

#form-delivery-options dl .description p {
  margin: 0;
}

#form-delivery-options .buttons {
  margin-top: 1rem;
}

#form-delivery-options .buttons button:not(:last-of-type) {
  margin-right: 0.25rem;
}

/**
 * CSS de la catégorie Yuto
 */
/* Gestion de parc / Flotte d'appareils */
#tb-liste-fabricant #dev-checked {
  width: 25px;
}

#tb-liste-fabricant #dev-key {
  width: 355px;
}

#tb-liste-fabricant #dev-version {
  width: 75px;
}

#tb-liste-fabricant #dev-user {
  width: 200px;
}

#tb-liste-fabricant #dev-last-sync {
  width: 150px;
}

#tb-liste-fabricant #dev-last-location {
  width: 160px;
}

#tb-exec-sql .label {
  width: 125px;
}

.bg-pink {
  background-color: #ffe5e5;
}

/* Guide d'installation */
#liste-guide img {
  margin-left: 15px;
  margin-top: 10px;
}

@media (max-width: 768px) {
  #liste-guide img {
    margin: 10px auto;
  }
}

/* Horaires d'activité */
.tb-day-exp2 thead th {
  width: 95px;
}

.tb-day-exp2 thead th#day-exp {
  width: 75px;
}

.tb-day-exp2 thead th#period-exp {
  width: 220px;
}

.tb-day-exp2 thead th#action-exp {
  width: 180px !important;
}

#form-holidays thead th {
  width: 200px;
}

#tb-closing #clg-start, #tb-closing #clg-end {
  width: 170px;
}

#tb-closing #clg-action {
  width: 20px;
}

#yuto-notifications thead th:not(:first-child) {
  width: 175px;
}

/* Rapports d'appels */
#calls-report #reports-created {
  width: 250px;
}

#calls-report #reports-author, #calls-report #reports-dest {
  width: 300px;
}

/* Rapports de visites */
#tb-type-rapports thead th {
  width: 350px;
}

/* Rapports Présentation des produits */
#tb-reports-pres-produits thead th {
  width: 275px;
}

#tb-reports-pres-produits thead th#reports-id {
  width: 175px;
}

#tb-reports-pres-produits thead th#reports-created {
  width: 150px;
}

/* Chiffre d'affaires */
#tb-total-ca thead th {
  width: 150px;
}

#tb-total-ca thead th:first-child {
  width: 200px;
}

/* Temps de visite */
#tb-moyenne-rdv #avg_duration {
  width: 525px;
}

#tb-moyenne-rdv #avg_revenue, #tb-moyenne-rdv #avg_yield {
  width: 200px;
}

#t_spent_time thead th {
  width: 200px;
}

#t_spent_time thead th:first-child {
  width: 325px;
}

/* Palmarès */
#notif p {
  font-size: 12px;
  font-weight: bold;
  color: #666666;
  fill: #666666;
  margin-top: 100px;
  text-align: center;
}

.tb-kpi {
  border-collapse: collapse;
  display: inline-table;
  clear: none;
  margin-right: 15px;
}

@media (min-width: 768px) {
  .tb-kpi {
    width: 340px;
  }
}

@media (max-width: 768px) {
  .tb-kpi {
    width: 100%;
  }
}

.tb-kpi caption {
  box-sizing: border-box;
}

.tb-kpi tr td {
  border-bottom: 1px solid #A9A9A9;
}

.tb-kpi tr.show-seller {
  background-color: lightgrey;
}

.tb-kpi tr.show-seller:hover {
  background-color: #C7C7C7;
}

.tb-kpi tr.hidden-seller {
  display: none;
}

.tb-kpi tfoot a {
  display: block;
}

/* Configuration */
#tb-ovr-usr {
  margin-top: 10px;
  width: 850px;
}

#tb-ovr-usr #cls-check, #tb-ovr-usr #cls-nb-fld {
  width: 250px;
}

#tb-ovr-usr #cls-name {
  width: 350px;
}

#tb-ovr-usr .bg-green-color {
  background-color: #ebffeb;
}

.hour_select {
  width: auto;
}

/**
 * CSS de la catégirie Comparateurs
 */
#tb-ctr-cat thead tr th, #tb-ctr-prd thead tr th {
  width: 100px;
  text-align: right;
}

#tb-ctr-cat thead tr th#cat, #tb-ctr-prd thead tr th#cat {
  width: 200px;
  text-align: left;
}

#tb-ctr-cat thead tr th#cat-margin, #tb-ctr-cat thead tr th#prd-margin, #tb-ctr-prd thead tr th#cat-margin, #tb-ctr-prd thead tr th#prd-margin {
  width: 104px;
}

#tb-ctr-cat thead tr th#cat-roi, #tb-ctr-cat thead tr th#prd-roi, #tb-ctr-prd thead tr th#cat-roi, #tb-ctr-prd thead tr th#prd-roi {
  width: 84px;
}

#tb-ctr-cat thead tr th#prd-check, #tb-ctr-prd thead tr th#prd-check {
  width: 25px;
}

#tb-ctr-cat thead tr th#prd, #tb-ctr-prd thead tr th#prd {
  width: auto;
  text-align: left;
}

#tb-ctr-cat thead tr th#prd-export, #tb-ctr-prd thead tr th#prd-export {
  width: 75px;
}

/**
 * Rechercher des produits
 */
#zone-filters label + span {
  display: inline-block;
  vertical-align: middle;
}

#zone-conditions {
  margin-top: 5px;
}

#zone-conditions .options {
  margin-top: 0;
}

#zone-conditions .options a.del {
  display: inline-block;
  vertical-align: middle;
  width: 15px;
  height: 15px;
  background: url(../../images/del-cat.svg) center no-repeat;
  background-size: contain;
  border-radius: 50%;
}

.checklist input[name="save-export"] + * {
  margin-left: 10px;
}

/**
 * Place de marché
 */
#site-content .form-notice-container {
  width: 100%;
}

@media (min-width: 769px) {
  #site-content .form-notice-container {
    width: 720px;
  }
}

#site-content .form-notice-container > form table,
#site-content .form-notice-container > .notice {
  width: 100%;
}

/**
 * CSS de l'onglet Statistiques
 */
/* Meilleures ventes */
#bestseller-prd #best-ref {
  width: 190px;
}

#bestseller-prd #best-orders {
  width: 75px;
}

#bestseller-prd #best-by-ord {
  width: 130px;
}

#bestseller-prd #best-ca, #bestseller-prd #best-ca-ord, #bestseller-prd #best-gross-margin {
  width: 120px;
}

/* Ventes par catégories*/
#bestseller-bycat #best-orders {
  width: 75px;
}

#bestseller-bycat #best-by-ord {
  width: 145px;
}

#bestseller-bycat #best-ca, #bestseller-bycat #best-ca-ord {
  width: 120px;
}

/* Fidélité */
/* Recherches */
#table-synthese-search thead th {
  width: 145px;
}

#table_search {
  width: 100%;
}

#table_search #volume, #table_search #nbres, #table_search #avg {
  width: 100px;
}

#table_search #ctr {
  width: 120px;
}

#table_search #website {
  width: 130px;
}

/* Redirections */
#table_search {
  width: 100%;
}

#table_search .td-check {
  width: 25px;
}

#table_search #types {
  width: 85px;
}

#table_search #emp {
  width: 280px;
}

#table_search #website {
  width: 130px;
}

#table_search input.w80 {
  width: 80%;
}

/* Formulaire ajout de suggestion */
#form-add-suggest {
  margin-bottom: 15px;
  margin-top: 15px;
  border: 1px solid #A9A9A9;
  width: 500px;
}

#form-add-suggest #new-title {
  background-color: #232E63;
  color: #fff;
  font-weight: 600;
  padding: 5px 0 5px 10px;
}

#form-add-suggest #new-suggestion {
  padding: 10px 3px 10px 10px;
  width: 500px;
}

#form-add-suggest #new-suggestion > div:not(#opt-sec),
#form-add-suggest #new-suggestion #opt-sec > div {
  display: flex;
  align-items: center;
}

#form-add-suggest #new-suggestion > div:not(#opt-sec) > label,
#form-add-suggest #new-suggestion #opt-sec > div > label {
  width: 150px;
}

#form-add-suggest #new-suggestion input, #form-add-suggest #new-suggestion select {
  width: 200px;
  vertical-align: middle;
}

#form-add-suggest #new-foot {
  border-top: 1px solid #A9A9A9;
  padding: 10px 3px 10px 10px;
  text-align: right;
  width: 499px;
}

#form-add-suggest #new-foot input#add-suggest {
  margin-right: 10px;
}

/* Suggestions de recherche */
#table-suggest-search #active {
  width: 25px;
}

#table-suggest-search #results {
  width: 100px;
}

#table-suggest-search #search {
  width: 350px;
}

#table-suggest-search #site {
  width: 100px;
}

/* Poids des produits */
.prd-weight:not(#prd-conversion) thead tr:last-child th {
  width: 100px;
}

.prd-weight:not(#prd-conversion) thead tr:last-child th#w-cat, .prd-weight:not(#prd-conversion) thead tr:last-child th#wn-cat {
  width: 350px;
}

#prd-completing #desc {
  width: 450px;
}

/* Produits sans images */
#form-no-image {
  padding-top: 10px;
}

#form-no-image ul.tabstrip {
  margin-top: 10px;
}

#prd-no-image {
  width: 100%;
}

#prd-no-image thead th#prd-taux {
  width: 160px;
}

#prd-no-image thead th:last-child {
  text-align: right;
}

#prd-no-image * {
  vertical-align: middle !important;
}

.div-padding-10 {
  padding: 10px;
}

#prd-conversion {
  width: 100%;
  max-width: 100%;
}

#prd-conversion th.header {
  cursor: auto;
}

#prd-conversion th, #prd-conversion td, #prd-conversion th a {
  word-wrap: break-word;
}

#prd-conversion #w-ref {
  width: 50px;
}

#prd-conversion #w-name {
  width: 320px;
}

#prd-conversion #w-conversion {
  width: 200px;
}

#prd-conversion #w-margin_rate {
  width: 160px;
}

#prd-conversion #w-price_sell, #prd-conversion #w-price_purchase {
  width: 130px;
}

@media (min-width: 768px) and (max-width: 1229px) {
  #prd-conversion {
    table-layout: fixed;
  }
  #prd-conversion th, #prd-conversion td {
    width: auto !important;
  }
}

span#tooltip-shiftKey {
  margin-bottom: 10px;
}

span#tooltip-shiftKey span#t-s-1 {
  float: left;
  vertical-align: middle;
}

span#tooltip-shiftKey span#t-s-1 img {
  vertical-align: middle;
  border: 0 none;
  width: 48px;
  height: 48px;
}

span#t-s-2 {
  font-weight: bold;
  font-size: 40px;
  vertical-align: bottom;
}

.tb-stat-ref th {
  width: 32%;
}

.tb-stat-ref th:first-child {
  width: auto;
}

/* Référencement > Balises */
@media (max-width: 1023px) {
  .tb-stat-ref td, #table-edit-personnalisation td,
  .tb-stat-ref td, #table-referencement td {
    overflow: visible !important;
  }
}

.tb-stat-ref td:last-child .open, #table-edit-personnalisation td:last-child .open,
.tb-stat-ref td:last-child .open, #table-referencement td:last-child .open {
  right: 0px !important;
}

.riametasBox_wrap {
  position: relative;
}

.riametasBox_wrap, .riametasBox_wrap.padding {
  padding: 0 20px 0 0;
}

.riametasBox_wrap textarea {
  width: 100%;
}

.riametasBox_wrap .riametasbox {
  right: -20px;
  width: 50px;
  position: absolute;
  top: -1px;
  font-size: 0.8em;
}

.riametasBox_wrap .riametasbox.open {
  z-index: 20;
  width: 250px;
  right: -220px;
}

@media (max-width: 767px) {
  .riametasBox_wrap .riametasbox.open {
    right: 0px;
  }
}

.riametasBox_wrap .riametasbox.open .box-content {
  padding: 5px;
  font-size: 1em;
}

.riametasBox_wrap .riametasbox.meta-success .box-arrow {
  background: url(/admin/images/metabox/success-arrow.png) no-repeat;
}

.riametasBox_wrap .riametasbox.meta-success .box-content {
  color: #000;
  background-color: #DDFFDD;
  border: 1px solid #008000;
}

.riametasBox_wrap .riametasbox.meta-note .box-arrow {
  background: url(/admin/images/metabox/note-arrow.png) no-repeat;
}

.riametasBox_wrap .riametasbox.meta-note .box-content {
  background-color: #fffbcc;
  border: solid 1px #e6db55;
}

.riametasBox_wrap .riametasbox .box-arrow {
  width: 17px;
  height: 18px;
  padding: 10px 0 0 0;
  position: absolute;
  left: 0px;
  top: 2px;
  z-index: 10;
}

.riametasBox_wrap .riametasbox .box-content {
  border-radius: 5px;
  font-size: 0.9em;
  position: absolute;
  left: 11px;
  z-index: 9;
  cursor: pointer;
  padding: 3px 2px 2px 2px;
}

.riametasBox_wrap .riametasbox p {
  margin: 2px;
}

td:last-child .riametasBox_wrap .riametasbox.open {
  right: 0px;
}

/* Balises Meta Description en double */
#stat-tag-double {
  width: 870px;
}

#stat-tag-double #tag-descript {
  width: 240px;
}

#stat-tag-double #tag-object {
  width: 628px;
}

#stat-tag-double li.cnt-parent ul {
  list-style: none !important;
}

#stat-tag-double li.cnt-parent ul li {
  margin-left: 0 !important;
}

/* Veille tarifaire */
.checkboxes {
  display: block;
  padding-right: 10px;
}

.checkboxes .input {
  vertical-align: middle;
}

.checkboxes .span {
  vertical-align: middle;
}

@media (max-width: 1023px) {
  #stats-price-watching th.align-right, #stats-price-watching th.numeric, #stats-price-watching td.align-right, #stats-price-watching td.numeric {
    text-align: left !important;
  }
}

/**
 * CSS de l'onglet Outils
 */
/* Actualités */
form #table-tools-news {
  max-width: 830px;
  width: 100%;
}

form #table-tools-news #news-sel {
  width: 25px;
}

form #table-tools-news #news-name {
  width: 425px;
}

form #table-tools-news #news-pub-date {
  width: 75px;
}

form #table-tools-news #news-pub {
  width: 125px;
}

form #table-tools-news #news-pos {
  width: 120px;
}

#actu_reviews {
  width: 830px;
}

#actu_reviews input.submit {
  width: auto;
}

#actu_reviews textarea {
  height: 30px;
  width: 100%;
  font-family: Verdana, Arial;
  font-size: 1em;
}

#actu_reviews .cresp, #actu_reviews .cdesc {
  margin: 5px 0;
}

#actu_reviews .button {
  text-align: right;
}

#table-categories-actualites {
  width: 600px;
}

#table-categories-actualites #cat-sel {
  width: 25px;
}

#table-categories-actualites #cat-name {
  width: 425px;
}

#table-categories-actualites #news-count {
  width: 70px;
}

#class thead th:first-child {
  width: 25px;
}

#class thead #name, #class thead #nb-suscribers {
  width: 200px;
}

#class thead #type {
  width: 250px;
}

#table-list-newsletter #sel {
  width: 25px;
}

#table-list-newsletter #email-th, #table-list-newsletter #tel-th {
  width: 250px;
}

#table-list-newsletter #account, #table-list-newsletter #cat {
  width: 125px;
}

#popup-content #documents {
  margin: 4px 0 !important;
}

/* Campagne marketing */
#table-campagnes-sms #cpg-id {
  width: 25px;
}

#table-campagnes-sms #cpg-start, #table-campagnes-sms #cpg-end, #table-campagnes-sms #cpg-send {
  width: 150px;
}

#table-stats-details-messages {
  width: 600px;
}

#table-stats-details-messages #hphone, #table-stats-details-messages #hdate {
  width: 150px;
}

#table-stats-details-messages #husr {
  width: 290px;
}

.sms-add-rule-user .rwd-add-rule-label {
  display: inline-block;
  width: 130px;
}

.sms-add-rule-user input[type="text"] {
  max-width: 100%;
}

.marketing .cpg_message {
  margin: 10px;
}

.marketing .pmt-rules-buttons, .marketing .btn-action-small {
  text-align: right;
}

.marketing .pmt-rules-buttons {
  margin-top: 10px;
}

.marketing a.btn, .marketing a.btn:hover {
  color: buttontext;
  font-size: 11px;
  text-decoration: none;
}

.marketing .sms-spe-rules {
  text-align: left;
  border: none;
  padding: 5px;
}

.marketing table .sms-rules {
  width: 100%;
}

.marketing table textarea#msg {
  height: auto;
  width: 482px;
}

.marketing table textarea#description {
  height: auto;
}

.marketing .mention {
  font-size: 9px;
}

.marketing #period {
  width: 222px;
}

.marketing .is_marketing {
  display: block;
  max-width: 485px;
}

.marketing .is_marketing input {
  vertical-align: middle;
}

.marketing .is_marketing span {
  vertical-align: middle;
}

.marketing .period-picker {
  display: inline;
}

.marketing .period-hide {
  display: none;
}

.marketing .period-detail {
  width: 39px;
}

.marketing .btn {
  cursor: pointer;
}

.marketing .sent-0 {
  color: #ff0000;
}

.marketing .sent-1 {
  color: #008000;
}

.marketing .notice {
  margin-bottom: 0;
  margin-top: 5px;
}

/* Alertes de disponibilités */
#tb-alert-dispo {
  max-width: 100%;
}

#tb-alert-dispo #sel {
  width: 25px;
}

#tb-alert-dispo #prd-name, #tb-alert-dispo #brd-name {
  width: auto;
}

#tb-alert-dispo #account, #tb-alert-dispo #last_date, #tb-alert-dispo #date, #tb-alert-dispo #restocking {
  width: 100px;
}

#tb-alert-dispo #brd-name {
  min-width: 75px;
}

#tb-alert-dispo #restocking {
  min-width: 79px;
}

#tb-alert-dispo #last_date {
  min-width: 60px;
}

#tb-alert-dispo #date {
  min-width: 85px;
}

@media (max-width: 1400px) {
  #tb-alert-dispo [headers="email"] {
    max-width: 80px !important;
  }
  #tb-alert-dispo [headers="prd-name"] {
    max-width: 108px !important;
  }
  #tb-alert-dispo [headers="email"],
  #tb-alert-dispo [headers="prd-name"] {
    overflow-wrap: break-word;
  }
}

#faq_categories #faq-sel {
  width: 25px;
}

#faq_categories #faq-name {
  width: 400px;
}

#faq_categories #faq-qst {
  width: 50px;
}

#faq_categories #faq-qst-pub {
  width: 150px;
}

#table-faq-categorie-general {
  width: 100%;
}

#table-faq-categorie-general tbody tr:first-child td:first-child:not(.mceFirst) {
  width: 120px;
}

#faq_questions #qst-sel {
  width: 25px;
}

#faq_questions #qst-name {
  width: 400px;
}

#faq_questions #qst-pub {
  width: 60px;
}

/* Bannières et Zones d'actions */
#table-banners #bnr-sel, #table-zones-actions #bnr-sel {
  width: 25px;
}

#table-banners #bnr-name, #table-zones-actions #bnr-name {
  width: 325px;
}

#table-banners #bnr-url, #table-zones-actions #bnr-url {
  width: 225px;
}

#table-banners #bnr-from, #table-banners #bnr-to, #table-banners #bnr-emp, #table-zones-actions #bnr-from, #table-zones-actions #bnr-to, #table-zones-actions #bnr-emp {
  width: 125px;
}

#table-banners #bnr-pos, #table-zones-actions #bnr-pos {
  width: 120px;
}

#table-banners .bg-color-green, #table-zones-actions .bg-color-green {
  background-color: #ebffeb;
}

#table-banners .bg-color-blue, #table-zones-actions .bg-color-blue {
  background-color: #F4F7FB;
}

#table-modif-banners tbody td:first-child, #table-zones-edit tbody td:first-child {
  width: 130px;
}

#table-zones-edit input[type=text]#date_from, #table-zones-edit input[type=text]#date_to, #table-zones-edit select#plc {
  width: auto !important;
}

.bnr-preview img {
  max-width: 750px;
  width: 100%;
  height: auto;
}

/* Gestion de contenu */
div#name-contenu {
  border-bottom: solid 1px #c0c0c0;
  width: 530px;
  margin-bottom: 10px;
}

div#name-contenu h3 {
  display: inline;
  border: none;
}

div#name-contenu div.ria-cell-move {
  float: right;
}

div#name-contenu div:last-child {
  clear: right;
}

#table-gestion-contenu thead th:first-child, #cms-categories thead th:first-child {
  width: 25px;
}

#table-gestion-contenu thead th:nth-child(2), #cms-categories thead th:nth-child(2) {
  width: 400px;
}

#table-gestion-contenu thead th:nth-child(3), #cms-categories thead th:nth-child(3) {
  width: 75px;
}

/* CMS, glossaires, configuration de l'import */
#editcms td.tdw-150, #table-glossary-def td.tdw-150, #table-glossary-edit td.tdw-150, #mapping-general td.tdw-150 {
  width: 150px;
}

#tb-redirection {
  width: 100%;
  max-width: 820px;
  margin-top: 10px;
}

#tb-redirection #url {
  width: 705px;
}

#tb-redirection #action {
  width: 50px;
}

#tb-redirection img.edit-url {
  width: 25px;
  height: 25px;
}

#tb-redirection td {
  padding: 5px 10px;
  vertical-align: middle;
}

#tb-redirection td.td-url {
  padding-right: 10px;
  border-right: 1px solid #A9A9A9;
}

@media (max-width: 767px) {
  #tb-redirection td.td-url {
    border-right: 0;
  }
}

#tb-redirection td.td-url input[type="text"] {
  width: 100% !important;
  max-width: 100% !important;
  margin-right: 10px;
}

#tb-redirection .td-action, #tb-closing .td-action {
  text-align: center;
  width: 110px;
}

@media (max-width: 1023px) {
  #tb-redirection .td-action, #tb-closing .td-action {
    word-break: initial;
  }
}

#div-select-type-modif {
  width: 775px;
  padding-bottom: 5px;
}

#div-select-type-modif select {
  float: right;
}

#div-select-type-modif div:last-child {
  clear: right;
}

#table-liste-versions #cms-ver-sel {
  width: 25px;
}

#table-liste-versions #cms-ver-date {
  width: 350px;
}

#table-liste-versions #cms-ver-type {
  width: 175px;
}

#table-liste-versions #cms-ver-user {
  width: 100px;
}

#table-liste-versions #cms-ver-up {
  width: 125px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:first-child {
  width: 25px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(2) {
  width: 375px;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(3) {
  width: 150px;
  text-align: center;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:nth-child(4) {
  width: 100px;
  text-align: center;
}

#table-liste-versions tbody#tbody-rev td.rev-td table td:last-child {
  width: 125px;
  text-align: center;
}

#table-liste-versions tfoot tr#pagination td:first-child {
  text-align: left;
}

.npad {
  padding: 1px 0px 0px 0px !important;
}

div.rev-form {
  border-bottom: 1px solid #A9A9A9;
  background: #EEEEEE;
}

div.rev-form #table-type-modif {
  float: right;
}

div.rev-form #table-type-modif #label-vertical-align {
  vertical-align: top;
}

div.rev-form div:last-child {
  clear: right;
}

div.rev-form table {
  margin: 0px;
  border: none !important;
}

table.checklist .rev-form tbody td {
  border: none;
}

tfoot .rev-form td {
  text-align: left;
}

tfoot .rev-form textarea {
  width: 100%;
}

#footer-no-npad {
  padding: 5px 10px;
  text-align: left;
}

form#formulaire_cms .notice {
  max-width: 820px;
}

/* Erratums */
#table-erratums {
  width: 800px;
}

#table-erratums #err-sel {
  width: 25px;
}

#table-erratums #err-ref {
  width: 50px;
}

#table-erratums #err-name {
  width: 225px;
}

#table-erratums #err-desc {
  width: 325px;
}

#table-erratums #err-date {
  width: 100px;
}

/* Glossaires */
#table-glossary-def, #table-glossaire {
  width: 90%;
}

@media (max-width: 1023px) {
  #table-glossary-def, #table-glossaire {
    width: 100% !important;
  }
}

#table-glossaire .alphabet-list {
  word-spacing: 0.5rem;
}

#table-glossaire th:first-child {
  width: 25px;
}

#table-glossaire #name, #table-glossaire #name_pl {
  width: 145px;
}

#table-glossaire #etat {
  width: 55px;
}

#table-glossaire #date {
  width: 150px;
}

#table-glossaire tfoot td input.float-left {
  margin-right: 3px;
}

/* Outils > Parrainage / Points de fidélité > Configuration */
#site-content form#form-rewards ul.tabstrip li:first-child {
  width: 78px !important;
}

#tb-tabConfig tbody td:first-child {
  width: 275px;
}

#tb-tabConfig tbody td:last-child {
  width: 495px;
}

#tb-tabConfig tbody td #rwd-products-pt td:first-child {
  width: 25px;
}

#tb-tabConfig tbody td #rwd-products-pt td:last-child {
  width: 160px;
}

#tb-tabConfig input {
  vertical-align: middle !important;
}

#tb-tabConfig input.ratio {
  width: 65px !important;
}

#tb-tabConfig select {
  width: auto !important;
}

#tb-tabConfig input.val-param {
  float: left;
}

#tb-tabConfig a.help {
  background-image: url("/admin/images/questionmark.svg");
  display: block;
  float: left;
  height: 16px;
  margin-left: 5px;
  width: 16px;
}

#tb-tabConfig [type='text'] + a.help {
  margin-top: 6px;
}

#tb-tabActions #actions {
  width: 250px;
}

#tb-tabActions #desc {
  width: 400px;
}

#tb-tabActions #rwa-params {
  width: 275px;
}

#tb-tabSponsors tr.tr-filleul-reduc td:first-child {
  width: 210px;
}

#tb-tabSponsors tr.tr-filleul-reduc td:last-child {
  width: 400px;
}

#rwd-products-pt {
  width: 100%;
}

#rwd-products-pt td {
  vertical-align: center;
}

@media (min-width: 768px) {
  #rwd-products-pt .thead-none {
    display: table-cell !important;
  }
}

@media (max-width: 767px) {
  #rwd-products-pt .align-right {
    text-align: left !important;
  }
}

/* Statistiques */
#stats-rewards {
  margin-top: 10px;
}

#stats-rewards #hdate {
  width: 80px;
}

#stats-rewards #hlimit {
  width: 180px;
}

#stats-rewards #husr {
  width: 290px;
}

#stats-rewards #hpts {
  width: 170px;
}

#stats-rewards #hconvert {
  width: 70px;
}

#stats-rewards tr.positive {
  background-color: #DDFFDD;
}

#stats-rewards tr.positive:hover {
  background-color: #ebffeb !important;
}

#stats-rewards tr.negative {
  background-color: #ffdddd;
}

#stats-rewards tr.negative:hover {
  background-color: #ffe5e5 !important;
}

#stats-rewards .stgodchild {
  margin-top: 5px;
}

#stats-rewards .bold {
  font-weight: 600;
}

@media (max-width: 1023px) {
  #stats-rewards .align-center {
    text-align: left !important;
  }
}

/* Imports */
#table-imports thead th:first-child {
  width: 25px;
}

#table-imports thead th:nth-child(2) {
  width: 300px;
}

#table-imports thead th:nth-child(3) {
  width: 100px;
}

#table-imports thead th:nth-child(4), #table-imports thead th:nth-child(6) {
  width: 200px;
}

#table-imports thead th:nth-child(5) {
  width: 150px;
}

/* Imports */
#table-imports-unfinished thead th:first-child {
  width: 25px;
}

#table-imports-unfinished thead th:nth-child(2) {
  width: 300px;
}

#table-imports-unfinished thead th:nth-child(3) {
  width: 300px;
}

/* Imports */
.tools-import .error ul {
  margin-top: 0;
}

.tools-import .error ul li:only-child {
  list-style: none;
}

.tools-import .imp-form span, .tools-import .imp-form select, .tools-import .imp-form input[type="password"] {
  vertical-align: middle;
}

#imp-form {
  width: 100%;
  max-width: 100% !important;
  table-layout: fixed;
}

@media (max-width: 768px) {
  #imp-form tr {
    display: flex;
    flex-direction: column;
  }
}

#imp-form tr th.header > * {
  display: block;
  position: relative;
  padding-right: 14px;
}

#imp-form tr th.header > *::after {
  position: absolute;
  right: 3px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

#imp-form tr th.header.th-proprietaire .tablesorter-header-inner {
  min-width: 95px;
}

#imp-form tr th.header.th-execution .tablesorter-header-inner {
  min-width: 100px;
}

#imp-form tr th.header.th-etat .tablesorter-header-inner {
  min-width: 90px;
}

#imp-form tr input[type="text"]:-moz-read-only {
  background-color: transparent;
  border: 0;
}

#imp-form tr input[type="text"]:read-only {
  background-color: transparent;
  border: 0;
}

#imp-form tr input[type="text"]:-moz-read-only:focus {
  border: 0;
  box-shadow: none;
}

#imp-form tr input[type="text"]:read-only:focus {
  border: 0;
  box-shadow: none;
}

@media (max-width: 768px) {
  #imp-form td.align-right, #imp-form td.align-center {
    text-align: left !important;
  }
}

#imp-form td.name-file {
  word-wrap: break-word;
  -webkit-hyphens: auto;
      -ms-hyphens: auto;
          hyphens: auto;
}

input[name="imp-separator-other-text"],
#imp-separator-other-text {
  vertical-align: middle;
}

#imp-separator-other-text,
input[name="imp-separator-other-text"],
#imp-text-separator {
  width: 50px;
}

/* export */
@media (max-width: 1023px) {
  #table-finished-export .align-center {
    text-align: left !important;
  }
}

/**
 * CSS des Pop-up
 */
/* message "Chargement en cours" */
.popup_ria_back_notice {
  z-index: 9999;
  position: fixed;
  text-align: center;
  top: 50%;
  left: 50%;
  padding: 5px 25px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 90%;
  max-width: 400px;
}

body#popup-content {
  background-image: none;
  color: #000;
  min-width: 0;
  height: auto;
  min-height: 0px;
  padding: 15px;
}

body#popup-content.tabs {
  padding: 15px 0 0 0;
}

body#popup-content table caption a {
  color: #fff;
  text-decoration: none;
}

body#popup-content table caption a:hover {
  text-decoration: underline;
}

body#popup-content table caption ul {
  margin-left: 21px;
}

/* Popup Créer un lien vers un nouvel objet */
#popup-content #table-create-link-object #res-check, #popup-content #table-create-link-object tr:first-child td:first-child {
  width: 20px;
}

/* Produits offerts */
/* Popup rechercher un produit */
#tb-popup-catalogue {
  border-collapse: collapse;
  display: inline-table;
  clear: none;
  margin-right: 15px;
}

@media (min-width: 768px) {
  #tb-popup-catalogue {
    width: 100%;
  }
}

@media (max-width: 768px) {
  #tb-popup-catalogue {
    width: 300px;
  }
}

#tb-popup-catalogue caption {
  box-sizing: border-box;
}

#tb-popup-catalogue thead th:first-child {
  width: 20px;
}

#tb-popup-catalogue thead th:last-child {
  width: 30px;
}

#tb-popup-catalogue td {
  border-top: 0;
}

/* Popup "Rechercher un produit", ajax-product-select.php
Dans Configuration > Cartes cadeaux, au clic sur "Ajouter un produit" */
/*.lst-prd-table {

}*/
/* Popup Sélection un objet de rattachement */
#tb-popup-liste-obj-classe {
  width: 100%;
}

#tb-popup-liste-obj-classe thead th#obj-name {
  width: 225px !important;
}

#tb-popup-liste-obj-classe thead th#obj-childs {
  width: 140px;
}

/* Popup Importer depuis une autre promotion */
#pmt-specials-product-import {
  width: 100%;
}

/* Popup "Choisissez une famille du comparateur" popup-export-comparators.php
Dans Catalogue > La sélection Purebike > Edition, onglet "Place du marché, au clic sur Modifier puis Exporter */
#popup-content input.ui-autocomplete-input, #popup-content input#filter {
  vertical-align: middle !important;
}

#popup-content table#choose-cat-ctr tfoot input#save-choose {
  margin-right: 5px;
}

/* Popup "Choisir une catégorie", popup-catégories.php
Dans Catalogue > La sélection Purebike > Edition, onglet "Général", au clic sur "Ajouter une Catégorie" */
.tb-popup-choose-cat .riaCancelButton {
  margin: 2px;
}

/* Popup "Ajout rapide de produit",
Dans Catalogue > Relevés linéaires > "un relevé", onglet "Général", au clic sur "Ajout rapide" */
#result-table tr.error, #result-table tr.success {
  display: table-row;
}

/* Popup "Exporter la liste des clients", popup-export-customers.php
Dans Comptes clients, un comte client, onglet "Général", au clic sur "Exporter" */
#popup-content .cols {
  -webkit-columns: 4 auto;
     -moz-columns: 4 auto;
          columns: 4 auto;
}

@media (max-width: 767px) {
  #popup-content .cols {
    -webkit-columns: 2 auto;
       -moz-columns: 2 auto;
            columns: 2 auto;
  }
}

@media (max-width: 424px) {
  #popup-content .cols {
    -webkit-columns: 1 auto;
       -moz-columns: 1 auto;
            columns: 1 auto;
  }
}

/* Popup "Modification de l'adresse de facturation"
Dans Comptes clients, un comte client, onglet "Adresses", au clic sur "Modifier" */
/* #table-fiche-client {
} */
/* Popup "Edition du type", tab-medias.js
Dans Comptes clients, un comte client, onglet "Images", au clic sur "Editer" */
/* #type_form table {
} */
/* Popup "Ajouter des Images"
Dans Comptes clients, un comte client, onglet "Images", au clic sur "+" */
/* #mediatheque-container {
} */
/** top-table -> back button + search form **/
/** tfoot-cat ->add cart + btn choose & btn-cancel **/
#popup-content .top-table {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

#popup-content .tfoot-cat td > div {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

@media (max-width: 767px) {
  #popup-content .tfoot-cat td > div {
    flex-direction: column-reverse;
    width: 100%;
  }
}

@media (max-width: 767px) {
  #popup-content .tfoot-cat td > div > div {
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 767px) {
  #popup-content .tfoot-cat td > div .btn-wrapper {
    margin: 15px 0 0 0;
  }
}

#popup-content .tfoot-cat td > div .btn-wrapper > * + * {
  margin-left: 10px;
}

@media (max-width: 767px) {
  #popup-content .tfoot-cat td > div .btn-wrapper > * + * {
    margin: 0;
  }
}

#popup-content .tfoot-cat td > div .btn-wrapper input[type="button"] {
  float: none;
}

/* Configuration > Paramètres de tarification, au clic sur "Choisir" dans le tableau "Produits exclus", Popup "Sélectionner un produit" */
.lst-prd-table tr {
  vertical-align: middle !important;
}

.lst-prd-table .prd-more {
  display: none;
  position: absolute;
  min-width: 80px;
  min-height: 80px;
  background: #fff;
  box-shadow: 0 0 5px #EEEEEE;
  padding: 2px;
}

/* Configuration > Redirections > Erreurs 404, au clic sur la loupe, popup "Rechercher un contenu de substitution" */
#result-redirection #info:empty {
  display: none;
}

#result-redirection tr {
  height: auto !important;
}

@media (max-width: 767px) {
  #result-redirection tr:not(:last-child) {
    border-bottom: 1px solid #A9A9A9 !important;
    padding: 0;
  }
}

#result-redirection td {
  vertical-align: top;
  position: relative;
}

#result-redirection td.img {
  text-align: center;
}

@media (max-width: 767px) {
  #result-redirection td.img {
    text-align: left;
  }
}

#result-redirection td.redirection img {
  float: left;
  margin-right: 25px;
  width: 80px;
  height: 80px;
}

@media (max-width: 767px) {
  #result-redirection td.redirection img {
    float: none;
    margin-right: 0;
  }
}

#result-redirection td div {
  margin-bottom: 3px;
}

#result-redirection td div:last-child {
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media (max-width: 767px) {
  #result-redirection td div:last-child {
    position: static;
    top: auto;
    right: auto;
    -webkit-transform: none;
            transform: none;
  }
}

#result-redirection td div input {
  float: right;
  margin: 10px;
}

@media (max-width: 767px) {
  #result-redirection td div input {
    float: none !important;
    width: auto;
  }
}

.content-substitut {
  padding: 5px 85px !important;
}

@media (max-width: 767px) {
  .content-substitut {
    float: none;
    padding: 5px 10px !important;
  }
}

#lst-res-search {
  margin-top: 10px;
}

/* Médiathèque > Images, une image, au clic sur Ajouter, popup "Associer à un produit" */
#popup-links-img #pagination, .popup-order-state-history #pagination {
  background-color: #fff;
  border-top: 1px solid #A9A9A9;
  margin: 0;
  margin-top: 10px;
  padding: 10px;
}

#popup-links-img .cnt-checkbox, .popup-order-state-history .cnt-checkbox {
  padding-top: 0;
}

#popup-links-img .cnt-checkbox input[type='checkbox'], .popup-order-state-history .cnt-checkbox input[type='checkbox'] {
  margin-top: 0;
}

#popup-links-img .cnt-place select, .popup-order-state-history .cnt-place select {
  vertical-align: middle;
}

#popup-links-img #pagination {
  width: 100% !important;
}

/* Promotions > Codes promotions > un code promo, onglet Général, au clic sur "Modifier le pattern", popup "Modifier le pattern des codes promotions" */
.form-popup-pattern select, .form-popup-pattern input {
  width: auto;
  vertical-align: middle;
}

.form-popup-pattern #pmt-rules-buttons {
  margin-top: 15px;
}

.form-popup-pattern .elem label:first-child {
  width: 145px;
  display: inline-block;
}

.form-popup-pattern .elem input[type="checkbox"], .form-popup-pattern .elem #code-list-like {
  margin-left: 145px !important;
}

.form-popup-pattern #code-like {
  width: 145px;
  display: inline-block;
}

/* Catégories > une catégorie > un produit, onglet "Comparateur", au clic sur "Surcharger les informations pour ce comparateur de prix", popup "Surcharge des informations exportées" */
.tb-popup-edit-ctrmarket sub {
  vertical-align: middle;
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation, dans le tableau, en sélectionnant Marque, au clic sur Choisir */
.pop-form-search {
  text-align: right;
  margin-bottom: 10px;
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation, dans Contexte "utilisateur, au clic sur Choisir, popup "Sélectionner un compte client */
.search-customers-results tr {
  vertical-align: middle !important;
}

/* Modération > Avis consommateur sur un produit, au clic sur "ne plus approuver", popup "Désapprobation de message" */
#moderation-comment {
  height: 95px !important;
}

.dropzone:hover {
  border: 3px dotted #5377FB !important;
}

/* Popup de gestion d'abonnement Yuto */
.popup-yuto-subscribtion .package {
  padding-top: 10px;
  padding-left: 10px;
}

.popup-yuto-subscribtion .package span {
  font-style: italic;
  margin-right: 5px;
}

.popup-yuto-subscribtion .opt-package {
  padding-top: 5px;
  padding-left: 0;
}

#popup-content.popup-yuto-payment p {
  padding: 0;
}

/**
 * CSS de l'onglet Modération
 */
.tb-contact-moderation {
  max-width: 100%;
}

.tb-contact-moderation #th-author {
  width: 300px;
}

.tb-contact-moderation .td-author {
  width: 20%;
}

.tb-spam {
  width: 650px;
  max-width: 100%;
}

#tb-prd-a-moderer {
  width: 600px;
  max-width: 100%;
}

#tb-prd-a-moderer #prd-ref {
  width: 25px;
}

#td-mod-proprietes-prd {
  width: 100%;
}

#td-mod-proprietes-prd tr:first-child td:first-child {
  width: 200px;
}

#td-mod-demandes-modifications {
  width: 100%;
}

#td-mod-demandes-modifications #fur-sel {
  width: 25px;
}

#td-mod-demandes-modifications #fur-from, #td-mod-demandes-modifications #fur-field {
  width: 175px;
}

#td-mod-demandes-modifications #fur-previous, #td-mod-demandes-modifications #fur-next {
  width: 50%;
}

#td-mod-demandes-modifications #fur-date {
  width: 95px;
}

.bottom-answer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.bottom-answer .join-file + .btn-group {
  align-self: flex-end;
}

.join-file {
  border-left: 5px solid #A9A9A9;
  padding-left: 10px;
}

.new-rep {
  background-color: #EEEEEE;
  border: 1px solid #A9A9A9;
  margin: 10px auto;
  padding: 10px;
  width: 95%;
}

.new-rep input {
  float: right;
  margin-left: 5px;
  padding: 3px;
}

input.upload-file {
  float: left;
  width: 100%;
  margin: auto;
}

/**
 * CSS de Clients > Gestion des négociations
 */
/* tableaux dans Comptes clients • Gestion des négociations • Nouvelle règle de négociation */
#td-contexte {
  width: 150px;
}

/**
 * CSS des options
 */
/* Catalogue */
#tb-ovr-usr #name {
  width: 200px;
}

#tb-ovr-usr #desc {
  width: 350px;
}

#tb-ovr-usr #value {
  width: 150px;
}

#tb-ovr-usr td {
  border-bottom: 1px solid #A9A9A9;
  padding: 10px;
  vertical-align: middle;
}

#tb-ovr-usr label {
  margin-right: 10px;
  margin-left: 3px;
}

/* Mon compte */
.myaccount label {
  padding-right: 5px;
  display: block;
}

@media (min-width: 768px) {
  .myaccount label {
    float: left;
    width: 175px;
    text-align: right;
  }
}

.myaccount label.inline {
  display: inline;
  float: none;
}

@media (max-width: 767px) {
  .myaccount dd {
    margin-bottom: 8px;
  }
}

/* Modération */
.ria-admin-ui-actions + .notice {
  margin-top: 10px;
}

/**
 * CSS du fichier view.admin.inc.php
 */
/* Table Médiathèque */
.table-images-liees-mediatheque a {
  font-size: 0.9em;
}

#table-images {
  width: 100%;
  max-width: 860px;
}

/* Onglet Référencement */
#table-referencement {
  width: 100%;
}

#table-referencement .colMax300px {
  max-width: 300px !important;
}

#table-referencement textarea {
  width: 100% !important;
}

#table-referencement [type="text"] {
  width: 100%;
  max-width: 100%;
}

#table-referencement tbody {
  padding-right: 10px !important;
}

#table-referencement tfoot .rev-form + input[type='submit'] {
  margin: 10px 5px 10px 10px;
}

/**
 * CSS des blocks d'adresses
 * Dans Comptes clients > "un compte client"
 * Et Commandes > Toutes les commandes > Commande n°xxxxxxxx, Adresse de livraison/facturation "Modifier", dans la popup "Modifier cette adresse",
 * au clic sur "Voir le carnet d'adresse"
 */
address {
  float: left;
  width: 225px;
  margin: 12px;
  padding: 15px;
  border: 1px solid #A9A9A9;
  min-height: 13.3em;
  font-style: normal;
}

address:hover {
  background-color: #EEEEEE;
}

address span {
  display: block;
}

address span span {
  display: inline;
}

address a.button {
  margin: 12px 5px 5px 0;
  display: inline-block;
}

address .p-name {
  font-weight: 600;
}

.title-addresses {
  clear: both;
  margin-top: 10px;
  margin-left: 12px;
}

/**
 * CSS des tableaux de la Médiathèque
 */
#type-docs #type-sel {
  width: 25px;
}

#type-docs #type-name, #type-docs #type-desc {
  width: 175px;
}

#type-docs #type-documents {
  width: 100px;
}

#type-docs #type-pos-2 {
  width: 75px;
}

#documents {
  width: 100%;
  max-width: 860px;
}

#documents #doc-sel {
  width: 25px;
}

#documents #doc-file {
  width: 225px;
}

#documents #doc-remp {
  width: 300px;
}

#documents #type-pos {
  width: 75px;
}

#documents a:hover {
  text-decoration: none !important;
}

#site-content #documents * {
  vertical-align: middle;
}

#table-objets-associes {
  width: 465px;
}

#table-objets-associes #obj-sel {
  width: 12px;
}

#table-objets-associes #obj-name {
  width: 450px;
}

#lst-downloads .th-lst-dl-150 {
  width: 150px;
}

#lst-downloads .th-lst-dl-250 {
  width: 250px;
}

#lst-downloads .th-lst-dl-100 {
  width: 100px;
}

.seg-obj-infos .del-obj-seg {
  width: 16px;
  height: 13px;
}

#table-hosts th {
  width: 100px;
}

#table-hosts th:first-child {
  width: 150px;
}

form#table-list-hosts table {
  width: 390px !important;
}

#table-edit-hosts #td-edit-hosts-150 {
  width: 150px;
}

#table-edit-hosts #td-edit-hosts-350 {
  width: 350px;
}

#table-edit-hosts #list-channels .del-obj-seg {
  width: 16px;
  height: 13px;
}

/* Images */
.name-links input {
  margin-top: 10px !important;
}

.img-detail #list-img {
  padding: 10px 32px;
  margin-top: 30px;
  position: relative;
}

.img-detail #list-img .prev-img, .img-detail #list-img .next-img {
  position: absolute;
  top: 10px;
  left: 0px;
  height: 150px;
  width: 32px;
}

.img-detail #list-img .prev-img a, .img-detail #list-img .next-img a {
  background-image: url("/admin/images/media/before.gif");
  background-position: center center;
  background-repeat: no-repeat;
  display: block;
  height: 150px;
}

.img-detail #list-img .next-img {
  left: auto;
  right: 0px;
}

.img-detail #list-img .next-img a {
  background-image: url("/admin/images/media/after_inactive.svg");
}

.img-detail #list-img .next-img a:hover {
  background-image: url("/admin/images/media/after_hover.svg");
}

.img-detail #list-img .prev-img a {
  background-image: url("/admin/images/media/before_inactive.svg");
}

.img-detail #list-img .prev-img a:hover {
  background-image: url("/admin/images/media/before_hover.svg");
}

#img-infos img {
  min-width: 260px;
  min-height: 260px;
}

.name-img {
  word-wrap: break-word;
}

#infos-img .img-media {
  border: 1px solid #EEEEEE;
  float: left;
  height: 154px;
  margin-right: 10px;
  padding: 2px;
}

#infos-img .img-media img {
  border: medium none;
  display: block;
}

/* Non utilisés */
.img-images {
  list-style: none;
  display: inline-block;
  margin: 10px 5px;
  height: 156px;
  width: 156px;
}

.img-images .input-del-item {
  margin-bottom: 3px;
}

.img-images a {
  height: 150px;
  width: 150px;
  display: block;
  border: 1px solid #EEEEEE;
}

.img-images a img {
  padding: 2px;
}

img.item-deleted {
  border: 1px solid #ABB2FF;
  min-width: 150px;
  min-height: 150px;
}

.input-del-item {
  display: none !important;
}

#file + span.color-red {
  vertical-align: middle;
}

.error-file {
  margin-top: 10px;
  color: #ff0000;
}

/* Une image */
#media img {
  margin-bottom: 5px;
}

#new-media {
  border: 1px solid #EEEEEE;
  margin-top: 10px;
  padding: 10px;
  width: 300px;
  display: none;
  position: absolute;
  box-shadow: 0 0 5px #EEEEEE;
  background: #fff;
  z-index: 1;
}

/**
 * CSS des fichiers ./catalog/linear-raised/index.php
 * et ./catalog/linear-raised/section.php
 * et ./catalog/linear-raised/edit.php
 */
/* Tableau de Listes des sections d'assortiments */
#table-sections-assortiments {
  width: 536px;
}

#table-sections-assortiments #th-sections-1 {
  width: 25px;
}

#table-sections-assortiments #th-sections-2 {
  width: 400px;
}

/* Tableau de Liste d'assortiments produits <col width="25" /><col width="300" /><col width="100" /><col width="200" /><col width="150" />*/
#table-assortiments-produits #th-assortiments-1 {
  width: 25px;
}

#table-assortiments-produits #th-assortiments-2 {
  width: 300px;
}

#table-assortiments-produits #th-assortiments-3 {
  width: 150px;
}

#table-assortiments-produits #th-assortiments-4 {
  width: 200px;
}

#table-assortiments-produits #th-assortiments-5 {
  width: 150px;
}

/* Tableau Section */
#table-section #td-section-1 {
  width: 160px;
}

#table-section #td-section-2 {
  width: 410px;
}

/* Tableau Informations générales, dans l'onglet général */
#table-gestion-releves-infos-generales {
  width: 570px;
}

#table-gestion-releves-infos-generales table.pmt-rules {
  width: 100%;
}

#table-gestion-releves-infos-generales #td-general-1 {
  width: 30%;
}

#table-gestion-releves-infos-generales #td-general-2 {
  width: 70%;
}

/*Tableaux gestion des relations dans l'onglet relations*/
#table-comptes-clients {
  width: 560px;
}

#table-comptes-clients .table-responsables {
  width: 100%;
}

#table-comptes-clients .table-responsables #th-responsables-1 {
  width: 25px;
}

#table-comptes-clients .table-responsables #th-responsables-2 {
  width: 510px;
}

#site-content .forms-wrapper {
  display: inline-flex;
  flex-direction: column;
}

#site-content .forms-wrapper table {
  width: 100%;
}

/* tablessorter */
table.tablesorter thead tr th {
  vertical-align: middle !important;
}

table.tablesorter thead tr th.header {
  cursor: pointer;
}

table.tablesorter thead tr .header > * {
  display: block;
}

table.tablesorter thead tr .header > *::after {
  content: '';
  cursor: pointer;
  background-image: url("/admin/dist/images/up-down.svg");
  background-repeat: no-repeat;
  background-position: center right;
  background-size: 12px;
  display: inline-block;
  width: 12px;
  height: 12px;
  vertical-align: middle;
  margin-left: 2px;
}

table.tablesorter thead tr .headerSortUp > *::after {
  background-image: url("/admin/dist/images/up.svg");
}

table.tablesorter thead tr .headerSortDown > *::after {
  background-image: url("/admin/dist/images/down.svg");
}

table.tablesorter thead tr .headerSortDown,
table.tablesorter thead tr .headerSortUp {
  background-color: #c1c4ff !important;
}

.edit-cat {
  position: relative;
  display: inline-block;
  font-size: 13px;
  margin-left: 5px;
  padding: 0 0 0 20px;
  font-weight: 500;
  text-decoration: none;
  vertical-align: middle;
  line-height: 20px;
  height: 20px;
}

.edit-cat:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  background-image: url(/admin/dist/images/configuration_off.svg);
}

.with-cols-options {
  position: relative;
}

.with-cols-options #display-cols-options.edit-cat {
  padding: 0 0 0 24px;
  line-height: 16px;
}

.with-cols-options #display-cols-options.edit-cat::before {
  height: 16px;
  margin-top: -8px;
  background-image: url(/admin/dist/images/columns.svg);
}

.td-engrenage .menu-cols {
  display: none;
}

.menu-cols .cols {
  position: absolute;
  font-size: 13px;
  background-color: #fff;
  border: 1px solid #A9A9A9;
  height: 300px;
  overflow-y: scroll;
  padding: 2px;
  z-index: 99;
}

.with-cols-options .menu-cols .cols {
  right: 0;
  top: 20px;
}

.menu-cols .col {
  clear: both;
  color: #000;
  display: block;
  font-weight: 500;
  margin: 5px;
  position: static;
}

.menu-cols .separate {
  border-bottom: 1px solid #A9A9A9;
  height: 1px;
  margin: 10px auto;
  width: 87%;
}

/**
 * CSS de TinyMCE
 */
.mceLayout {
  max-width: 960px;
  margin-top: 0 !important;
  width: 100% !important;
}

/* Surcharge le tinymce */
#site-content .defaultSkin .mceIframeContainer {
  padding: 0 !important;
  overflow: hidden;
}

.defaultSkin table.mceToolbar,
.defaultSkin table.mceToolbar *,
.mceSplitButtonMenu,
.mceSplitButtonMenu *,
.mceListBoxMenu,
.mceListBoxMenu *,
.mceMenu,
.mceMenu *,
.mce_forecolor,
.mce_forecolor *,
[id^="mce"], [id^="mce"] * {
  box-sizing: content-box;
}

@media (max-width: 767px) {
  .mceToolbar tbody tr {
    display: flex;
    flex-wrap: wrap;
  }
}

/* Popup d'information a la connexion */
.ria-admin-ui-intro-wrapper {
  display: none;
}

.ria-admin-ui-intro {
  text-align: center;
  color: #232E63;
  padding: 22px;
}

@media (min-width: 1024px) {
  .ria-admin-ui-intro {
    padding: 30px;
  }
}

.ria-admin-ui-intro-media {
  border: 0 none;
  width: 150px;
  margin-bottom: 22px;
}

.ria-admin-ui-intro-title {
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 6px;
}

.ria-admin-ui-intro-caption {
  font-size: 13px;
  margin-bottom: 32px;
}

.ria-admin-ui-intro-caption.yuto-alert {
  margin: 32px 0;
  font-size: 17px;
}

.ria-admin-ui-intro-button {
  width: 156px;
  height: 42px !important;
  background-image: linear-gradient(to bottom, #3e58e4, #2439b7);
  color: #fff !important;
  border: 0 none !important;
}

.ria-admin-ui-intro-button:hover {
  background-image: linear-gradient(to bottom, #2845e1, #2032a2);
}

.ria-admin-ui-intro-button.yuto-alert {
  width: 230px;
  font-size: 1.2em;
}

/* Transforme le menu principal en barre d'outils */
#site-menu-search {
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  flex-basis: 0;
}

#site-menu-search #site-submenu {
  top: 140px;
}

/* Champs spécifiques à la liste des commandes */
.ria-admin-ui-menu #ord-ref, .ria-admin-ui-menu #ret-ref {
  max-width: 148px;
}

/* = Menu = */
/* Menu principal de l'admin */
.ria-admin-ui-menu {
  list-style-type: none;
  font-size: 12px;
  margin-bottom: 0;
  margin-top: 0;
  /* Sous menu (Contextuel), Submenu */
}

.ria-admin-ui-menu li, .ria-admin-ui-menu ul {
  margin: 0;
  padding: 0;
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu {
    border-bottom: 1px solid #A9A9A9;
    height: 66px;
    display: flex;
  }
}

.ria-admin-ui-menu > li {
  flex: 0 1 150px;
}

.ria-admin-ui-menu > li > .site-menu-btn {
  color: #222842;
  text-decoration: none;
  line-height: 44px;
  padding: 0 5px;
  display: block;
  font-size: 12px;
}

.ria-admin-ui-menu > li > .site-menu-btn:hover, .ria-admin-ui-menu > li > .site-menu-btn.tab-menu-active {
  background-color: #5377FB;
  color: #fff;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active) {
    border-color: #fff;
  }
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu > li > .site-menu-btn {
    white-space: nowrap;
    padding-top: 42px;
    padding-bottom: 11px;
    position: relative;
    line-height: 13px;
    text-align: center;
  }
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active) {
    border-color: #fff;
  }
  .ria-admin-ui-menu > li > .site-menu-btn:not(.tab-menu-active):hover {
    border-left: 1px solid #A9A9A9;
    border-right: 1px solid #A9A9A9;
  }
}

.ria-admin-ui-menu label {
  margin-bottom: 8px;
  display: block;
}

.ria-admin-ui-menu .ria-admin-ui-submenu {
  color: #232E63;
  display: block;
  list-style-type: circle;
  list-style-position: outside;
}

@media (min-width: 1024px) {
  .ria-admin-ui-menu .ria-admin-ui-submenu {
    position: absolute;
    left: 0px;
    width: 200px;
    margin-top: 1px;
    padding-bottom: 18px;
  }
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul {
  margin-bottom: 0;
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul li {
  position: relative;
  border-top: 1px solid #fff;
}

.ria-admin-ui-menu .ria-admin-ui-submenu ul li a::before, .ria-admin-ui-menu .ria-admin-ui-submenu ul li div::before, .ria-admin-ui-menu .ria-admin-ui-submenu ul li span::before {
  content: '\002022';
  font-size: 28px;
  line-height: 10px;
  vertical-align: top;
  margin-right: 4px;
  display: inline-block;
}

.ria-admin-ui-menu .ria-admin-ui-submenu > li {
  text-align: left;
  list-style-type: none;
  border-bottom: 1px solid #A9A9A9;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled {
  border-style: none;
  display: block;
  color: #232E63;
  padding: 10px 12px 6px;
  text-decoration: none;
  position: relative;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a.ria-admin-ui-searchbar,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div.ria-admin-ui-searchbar,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled.ria-admin-ui-searchbar {
  margin: 0;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a:hover,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div:hover,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled:hover {
  text-decoration: none;
  background-color: #DADCFF;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > a.ria-admin-ui-submenu--active,
.ria-admin-ui-menu .ria-admin-ui-submenu li > div.ria-admin-ui-submenu--active,
.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled.ria-admin-ui-submenu--active {
  background-color: #ABB2FF;
  color: #000;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > span.disabled {
  cursor: default;
  display: block;
}

.ria-admin-ui-menu .ria-admin-ui-submenu li > ul > li > span.disabled {
  display: block;
  color: #232E63;
  padding: 10px 12px 6px;
}

#site-menu-home {
  display: none;
}

.site-menu-disabled {
  opacity: 0.5;
}

.site-menu-btn-disabled {
  opacity: 0.5;
  cursor: default;
}

.ria-admin-ui-menubar {
  background-color: #fff;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menubar {
    position: absolute;
    z-index: 10000;
    width: 100%;
    width: 100vw;
    max-width: 260px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    background-image: none;
    height: auto;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
    transition: -webkit-transform 0.2s ease-out;
    transition: transform 0.2s ease-out;
    transition: transform 0.2s ease-out, -webkit-transform 0.2s ease-out;
    max-height: calc(100% - 74px);
    overflow-y: auto;
    padding-bottom: 10px;
  }
  .ria-admin-ui-menubar.is-open {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar {
  margin: 10px 12px;
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
  border: 1px solid #A9A9A9;
}

.ria-admin-ui-menubar .ria-admin-ui-searchbar .mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) .mdc-text-field__input {
  padding-left: 8px;
}

.ria-admin-ui-menubar .ria-admin-ui-headactions {
  display: none;
}

@media (max-width: 1023px) {
  .ria-admin-ui-menubar .ria-admin-ui-headactions {
    display: block;
  }
}

.ria-admin-ui-menubar .ria-admin-ui-headactions .mdc-button {
  padding: 0 12px;
  margin: 3px 0;
}

.ria-admin-ui-menubar .ria-admin-ui-headactions .ria-button--outline-dark {
  margin-left: 12px;
}

.ria-admin-ui-searchbar--top-menu {
  display: block;
}

@media (min-width: 1024px) {
  .ria-admin-ui-searchbar--top-menu {
    display: none;
  }
}

.ria-admin-ui-legals {
  font-size: 10px;
  color: #232E63;
  margin-top: 13px;
  padding: 0 12px;
}

@media (min-width: 1024px) {
  .ria-admin-ui-legals {
    display: none;
  }
}

.ria-sprite {
  background-repeat: no-repeat;
  display: block;
  position: absolute;
  top: 6px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 28px;
  height: 28px;
}

@media (max-width: 1023px) {
  .ria-sprite {
    position: static;
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: scale(0.7);
            transform: scale(0.7);
  }
}

.ria-sprite-catalog {
  background-image: url("/admin/dist/images/catalogue_off.svg");
}

.site-menu-btn:hover .ria-sprite-catalog, .tab-menu-active .ria-sprite-catalog {
  background-image: url("/admin/dist/images/catalogue.svg");
}

.ria-sprite-config {
  background-image: url("/admin/dist/images/configuration_off.svg");
}

.site-menu-btn:hover .ria-sprite-config, .tab-menu-active .ria-sprite-config {
  background-image: url("/admin/dist/images/configuration.svg");
}

.ria-sprite-customers {
  background-image: url("/admin/dist/images/clients_off.svg");
}

.site-menu-btn:hover .ria-sprite-customers, .tab-menu-active .ria-sprite-customers {
  background-image: url("/admin/dist/images/clients.svg");
}

.ria-sprite-media {
  background-image: url("/admin/dist/images/mediatheque_off.svg");
}

.site-menu-btn:hover .ria-sprite-media, .tab-menu-active .ria-sprite-media {
  background-image: url("/admin/dist/images/mediatheque.svg");
}

.ria-sprite-moderation {
  background-image: url("/admin/dist/images/moderation_off.svg");
}

.site-menu-btn:hover .ria-sprite-moderation, .tab-menu-active .ria-sprite-moderation {
  background-image: url("/admin/dist/images/moderation.svg");
}

.ria-sprite-order {
  background-image: url("/admin/dist/images/commandes_off.svg");
}

.site-menu-btn:hover .ria-sprite-order, .tab-menu-active .ria-sprite-order {
  background-image: url("/admin/dist/images/commandes.svg");
}

.ria-sprite-pda {
  background-image: url("/admin/dist/images/yuto_off.svg");
}

.site-menu-btn:hover .ria-sprite-pda, .tab-menu-active .ria-sprite-pda {
  background-image: url("/admin/dist/images/yuto.svg");
}

.ria-sprite-yuto {
  background-image: url("/admin/dist/images/yuto_off.svg");
}

.site-menu-btn:hover .ria-sprite-yuto, .tab-menu-active .ria-sprite-yuto {
  background-image: url("/admin/dist/images/yuto.svg");
}

.ria-sprite-promotions {
  background-image: url("/admin/dist/images/promotions_off.svg");
}

.site-menu-btn:hover .ria-sprite-promotions, .tab-menu-active .ria-sprite-promotions {
  background-image: url("/admin/dist/images/promotions.svg");
}

.ria-sprite-stats {
  background-image: url("/admin/dist/images/statistiques_off.svg");
}

.site-menu-btn:hover .ria-sprite-stats, .tab-menu-active .ria-sprite-stats {
  background-image: url("/admin/dist/images/statistiques.svg");
}

.ria-sprite-tools {
  background-image: url("/admin/dist/images/outils_off.svg");
}

.site-menu-btn:hover .ria-sprite-tools, .tab-menu-active .ria-sprite-tools {
  background-image: url("/admin/dist/images/outils.svg");
}

.ria-sprite-comparators {
  background-image: url("/admin/dist/images/comparateurs_off.svg");
}

.site-menu-btn:hover .ria-sprite-comparators, .tab-menu-active .ria-sprite-comparators {
  background-image: url("/admin/dist/images/comparateurs.svg");
}

.ria-admin-ui-menu #site-menu-options {
  flex-basis: 1px;
}

.ria-admin-ui-menu #site-menu-options .site-menu-btn {
  width: 1px;
  visibility: hidden;
  overflow: hidden;
  padding-left: 0;
  padding-right: 0;
  text-indent: -9999px;
}

.block-submenu {
  position: absolute;
  cursor: pointer;
  width: 24px;
  height: 24px;
  left: 188px;
  border-radius: 50%;
  margin-top: 20px;
  z-index: 100;
  background: url("/admin/images/fold.svg") no-repeat center center;
}

.block-submenu:hover {
  background: url("/admin/images/fold_hover.svg") no-repeat center center;
}

.block-submenu.animate-is-close {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.block-submenu.animate-is-open {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}

@media (max-width: 1023px) {
  .block-submenu {
    display: none;
  }
}

#site-submenu {
  margin-bottom: 18px;
}

#site-submenu::-webkit-scrollbar {
  width: 0;
}

#site-submenu::-webkit-scrollbar-thumb {
  padding-bottom: 18px !important;
  background-color: #b5b5be;
  border-radius: 3px;
}

#site-submenu::-webkit-scrollbar-thumb:hover {
  background-color: #7a869a;
}

@media (min-width: 1025px) {
  .fixedFold {
    position: fixed !important;
    overflow-y: auto;
    top: 0px;
  }
  .fixedTop {
    position: fixed !important;
    top: 0px;
  }
  .fixedBottom {
    position: fixed !important;
    overflow-y: auto;
    bottom: -18px;
  }
}

/**
 * CSS du flux d'activités
 */
/* Bouton flux */
.voile-opacit {
  overflow-y: hidden;
}

.img-flow {
  overflow: visible;
  margin-right: 20px;
  margin-left: 10px;
  width: 22px;
  height: 23px;
  background: transparent url("/admin/images/flow/flow.svg") no-repeat center center;
  background-size: contain;
  border-radius: 0;
  border: 0;
  position: relative;
}

.img-flow:hover {
  border-radius: 0;
  border: 0;
  background: transparent url("/admin/images/flow/flow_hover.svg") no-repeat center center;
  background-size: contain;
}

.img-flow:focus {
  box-shadow: none;
}

.img-flow .numbers_red {
  top: -10px;
  left: 10px;
}

.img-flow.icon-flow-responsive {
  margin-top: 10px;
  width: auto;
  text-align: left;
  background: transparent url("/admin/images/flow/flow_hover.svg") no-repeat left center;
  padding-left: 30px;
  background-size: contain;
  font-size: 12px;
}

.img-flow.icon-flow-responsive:hover {
  color: black;
  text-decoration: underline !important;
}

.img-flow.icon-flow-responsive .numbers_red {
  left: 15px;
  right: auto;
}

.text-flow {
  vertical-align: bottom;
}

.numbers_red {
  position: absolute;
  background-color: #ff6366;
  color: #ffffff;
  width: auto;
  min-width: 20px;
  display: inline-block;
  font-size: 8px;
  border-radius: 11px;
  padding: 6px 5px 5px 5px;
  text-align: center;
}

.flow {
  color: #002251;
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 10001;
  display: flex;
  top: 0;
  /* contenu du block de flux */
  /* supprimer le bloc si dessous lorsque le flux sera opérationnel */
  /* Text styles */
}

.flow * {
  margin: 0;
  padding: 0;
  vertical-align: middle;
  font-family: Rubik !important;
}

.flow .voile-opacit {
  width: 100%;
  height: 100vh;
  opacity: 0.4;
  background-color: #002251;
}

.flow .flow-content {
  width: auto;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  /* Entête du flux d'activité */
  /* Block de notifications */
}

.flow .flow-content .flow-header {
  position: relative;
  padding: 0 55px 24px 55px;
  background-color: #ffffff;
  /* flèche de retour */
}

@media (max-width: 767px) {
  .flow .flow-content .flow-header {
    padding: 0 20px 20px 45px;
  }
}

.flow .flow-content .flow-header.flow--filter-active {
  box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.08);
}

.flow .flow-content .flow-header.header-filter {
  margin-bottom: 0;
}

.flow .flow-content .flow-header .return {
  -o-object-fit: contain;
     object-fit: contain;
  background: url("/admin/images/flow/arrow/left.svg") no-repeat center center;
  position: fixed;
  right: 450px;
  top: 48px;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

@media (max-width: 767px) {
  .flow .flow-content .flow-header .return {
    right: auto;
    left: 5px;
  }
}

.flow .flow-content .flow-header .return.return--return-home-flow {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}

.flow .flow-content .flow-header .all-read {
  font-size: 12px;
  text-align: right;
  color: #7a869a;
}

.flow .flow-content .flow-header .filter-flow {
  position: relative;
  height: 50px;
  margin-top: 20px;
}

.flow .flow-content .flow-header .filter-flow .icon-filter {
  overflow: visible;
  background: url("/admin/images/flow/filters.svg") no-repeat center center;
  display: block;
  padding: 11px;
  margin-left: 12px;
  position: relative;
  text-align: center;
  float: right;
}

.flow .flow-content .flow-header .filter-flow .icon-filter .numbers_red {
  vertical-align: middle;
  padding: 6px 5px 5px 5px;
  top: -6px;
  left: 28px;
}

.flow .flow-content .flow-header .filter-flow .select {
  width: 87%;
  min-height: 40px;
  position: absolute;
  background: white url("/admin/images/flow/arrow/down.svg") no-repeat right 9px top 16px;
  border-radius: 6px;
  border: 1px solid #e2e2ea;
  padding-right: 10px;
  z-index: 10050;
}

.flow .flow-content .flow-header .filter-flow .select.select--select-open {
  box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.08);
}

.flow .flow-content .flow-header .filter-flow .select .selected--none {
  display: none;
}

.flow .flow-content .flow-header .filter-flow .select .filter-selected {
  padding: 0 15px 10px 15px;
  font-size: 15px;
  font-weight: bold;
  border-bottom: none;
  vertical-align: middle;
  padding-top: 10px;
  width: 100%;
  cursor: pointer;
}

.flow .flow-content .flow-header .filter-flow .select .all-options {
  border-top: 0;
  width: 100%;
  left: 0;
  padding: 5px 15px;
  max-height: 200px;
  overflow-y: scroll;
  margin-right: 10px;
  margin-bottom: 10px;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option {
  font-size: 15px;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option li button {
  width: 100%;
  text-align: left;
  padding: 8px 10px;
  font-size: 15px;
  font-weight: normal;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option li:hover button:not(.option--is-select) {
  border-radius: 6px;
  background-color: #f8f8fa;
  color: #002251;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option li:first-child {
  margin-top: 0;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option li:last-child {
  margin-bottom: 0 !important;
}

.flow .flow-content .flow-header .filter-flow .select .all-options .radio-option li .option--is-select {
  color: #b5b5be;
  cursor: initial;
}

@media (max-width: 424px) {
  .flow .flow-content .flow-header .filter-flow .select {
    width: 80%;
  }
}

@media (max-width: 424px) {
  .flow .flow-content .flow-header .filter-flow {
    position: static;
  }
  .flow .flow-content .flow-header .filter-flow .icon-filter {
    float: none;
    top: 40px;
    position: absolute;
    right: 20px;
  }
}

.flow .flow-content .flow-header .filters-active {
  display: flex;
  flex-wrap: wrap;
  margin: 27px 0 10px 0;
  padding-left: 15px;
}

.flow .flow-content .flow-header .filters-active span {
  width: 50%;
  font-size: 13px;
  margin-bottom: 10px;
}

.flow .flow-content .block-title, .flow .flow-content .block-filters-active {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 424px) {
  .flow .flow-content .block-title, .flow .flow-content .block-filters-active {
    flex-wrap: wrap;
    flex-direction: column;
    align-items: flex-start;
  }
  .flow .flow-content .block-title .delete-all, .flow .flow-content .block-filters-active .delete-all {
    order: -1;
    margin-bottom: 10px;
    text-align: right;
  }
}

.flow .flow-content .block-title {
  margin-top: 45px;
}

.flow .flow-content .block-filters-active {
  margin-top: 14px;
}

.flow .flow-content .notifications {
  margin: 0 10px 20px 0;
  padding: 0 39px 0 40px;
  max-height: 500px;
}

.flow .flow-content .notifications .block-notifications .block-notifications-title {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
  margin: 30px 0 0 15px;
}

.flow .flow-content .notifications .notification-flow {
  padding: 15px 14px 15px 45px;
}

.flow .flow-content .notifications .notification-flow .notif-content {
  font-size: 14px;
  line-height: 20px;
}

.flow .flow-content .notifications .notification-flow .notif-head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  margin-bottom: 5px;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-title {
  font-weight: 600;
  font-size: 14px;
  border-bottom: none;
  flex: 1 1 auto;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-title::before {
  width: 26px;
  height: 26px;
  position: absolute;
  left: -30px;
  top: -2px;
}

@media (max-width: 767px) {
  .flow .flow-content .notifications .notification-flow .notif-head .notif-title {
    margin-right: 15px;
  }
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-date {
  font-size: 12px;
  color: #7a869a;
  padding: 0 14px;
}

@media (max-width: 424px) {
  .flow .flow-content .notifications .notification-flow .notif-head .notif-date {
    padding: 0;
  }
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading {
  position: relative;
  /* on génère un second élément en :before pour la flèche */
  /* pas de contour durant le :focus */
  /* point signalant si la notification a été lue ou non */
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .notif-read {
  width: 14px;
  height: 14px;
  background: url("/admin/images/flow/radio/non_lu.svg") no-repeat center center;
  position: relative;
  overflow: visible;
  /* on génère un élément :after lors du survol et du focus :*/
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .notif-read.is-read {
  background: url("/admin/images/flow/radio/lu.svg") no-repeat center center;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .notif-read:hover:after, .flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .notif-read:focus:after {
  content: attr(aria-label);
  /* on affiche aria-label */
  position: absolute;
  bottom: -2.4em;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  /* on centre horizontalement  */
  z-index: 10031;
  /* pour s'afficher au dessus des éléments en position relative */
  white-space: nowrap;
  /* on interdit le retour à la ligne*/
  color: white;
  padding: 5px;
  border-radius: 5px;
  background-color: #002251;
  width: 90px;
  right: -40px;
  font-size: 12px;
  text-align: center;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading [aria-label]:hover:before,
.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading [aria-label]:focus:before {
  content: "▲";
  position: absolute;
  top: 0.1em;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  /* on centre horizontalement  */
  font-size: 20px;
  color: #002251;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading [aria-label]:focus {
  outline: none;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .message-read {
  position: absolute;
  color: #ffffff;
  padding: 5px;
  border-radius: 5px;
  background-color: #002251;
  width: 90px;
  right: -40px;
  font-size: 10px;
  text-align: center;
}

.flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading .message-read::before {
  position: absolute;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  content: url("/admin/images/flow/arrow/down.svg");
  top: -7px;
  right: 40px;
}

@media (max-width: 424px) {
  .flow .flow-content .notifications .notification-flow .notif-head .notif-event-reading {
    align-self: flex-end;
    position: absolute;
    top: 0;
  }
}

.flow .flow-content .notifications .notification-flow .notif-head .delete-all {
  text-align: right;
}

@media (max-width: 424px) {
  .flow .flow-content .notifications .notification-flow .notif-head {
    align-items: flex-start;
    flex-direction: column;
  }
}

.flow .flow-content .notifications .notification-flow.notif--technical-visit .notif-title::before {
  content: url("/admin/images/flow/box/report.svg");
}

.flow .flow-content .notifications .notification-flow.notif--technical-issue .notif-title::before {
  content: url("/admin/images/flow/box/technical.svg");
}

.flow .flow-content .notifications .notification-flow.notif--order .notif-title::before {
  content: url("/admin/images/flow/box/sale.svg");
}

.flow .flow-content .notifications .notification-flow.notif--opportunity .notif-title::before {
  content: url("/admin/images/flow/box/opportunity.svg");
}

.flow .flow-content .notifications .notification-flow.notif--messages-customers .notif-title::before {
  content: url("/admin/images/flow/box/messages_clients.svg");
}

.flow .flow-content .notifications .notification-flow.notif--missing-call .notif-title::before {
  content: url("/admin/images/flow/box/missing_call.svg");
}

.flow .flow-content .notifications .notification-flow.notif--call-in .notif-title::before {
  content: url("/admin/images/flow/box/call_in.svg");
}

.flow .flow-content .notifications .notification-flow.notif--call-out .notif-title::before {
  content: url("/admin/images/flow/box/call_out.svg");
}

.flow .flow-content .notifications .notification-flow.notif--add-contact .notif-title::before {
  content: url("/admin/images/flow/box/ajout_contact.svg");
}

.flow .flow-content .notifications .notification-flow.notif--modification-contact .notif-title::before {
  content: url("/admin/images/flow/box/maj_contact.svg");
}

.flow .flow-content .notifications .notification-flow .additional-informations {
  margin-top: 5px;
  font-size: 12px;
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--alert-messages {
  display: inline-block;
  width: auto;
  padding: 2px 5px;
  border-radius: 2px;
  background-color: #ff6366;
  color: #ffffff;
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--status-order {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
  font-size: 12px;
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--chances-signing p {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
  font-size: 12px;
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--chances-signing p::after {
  content: '80%';
  float: right;
}

@media (max-width: 424px) {
  .flow .flow-content .notifications .notification-flow .additional-informations.info--chances-signing p::after {
    float: none;
    display: block;
  }
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--chances-signing .chances {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background-color: #e2e2ea;
  position: relative;
  margin-top: 7px;
}

.flow .flow-content .notifications .notification-flow .additional-informations.info--chances-signing .chances .percentages {
  background-color: #5377fb;
  width: 80%;
  height: 6px;
  border-radius: 3px;
  position: absolute;
  left: 0;
}

.flow .flow-content .notifications .notification-flow:hover, .flow .flow-content .notifications .notification-flow.notif--up-to-date {
  background-color: #f8f8fa;
  border-radius: 5px;
}

.flow .flow-content .notifications .notification-flow.notif--up-to-date {
  margin-top: 2px;
  border-radius: 6px;
  border: dashed 1px #b1bcca;
  background-color: #f8fafb;
}

.flow .flow-content .notifications .notification-flow.notif--up-to-date .notif-title::before {
  content: "";
  display: inline-block;
  background-image: url(/admin/images/flow/check_gradient.svg);
  background-repeat: no-repeat;
  background-size: 22px;
}

.flow .flow-content .notifications .notification-flow .content-grey {
  font-size: 11px;
  color: #7a869a;
}

@media (max-width: 767px) {
  .flow .flow-content .notifications {
    padding: 0 20px 20px 45px;
  }
}

.flow .flow-content .filter-content {
  padding: 0 55px 10px 55px;
  overflow-y: scroll;
  margin-bottom: 100px;
  flex: 1 1 auto;
}

.flow .flow-content .filter-content .title-familly {
  color: #002251;
  font-size: 16px;
  font-weight: 500;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 34, 81, 0.08) !important;
  position: relative;
  margin-bottom: 20px;
}

.flow .flow-content .filter-content .title-familly::after {
  content: url("/admin/images/flow/arrow/down.svg");
  position: absolute;
  right: 0;
}

.flow .flow-content .filter-content .filter-familly {
  padding-top: 40px;
}

.flow .flow-content .filter-content .filter-familly:first-child {
  padding-top: 0 !important;
}

.flow .flow-content .filter-content .list-filters.types ul {
  border-bottom: 1px solid rgba(0, 34, 81, 0.08) !important;
  padding-bottom: 16px;
}

.flow .flow-content .filter-content .list-filters.types ul > li:first-child {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
}

.flow .flow-content .filter-content .list-filters.types ul.accordion {
  position: relative;
  cursor: pointer;
}

.flow .flow-content .filter-content .list-filters.types ul.accordion::after {
  content: url("/admin/images/flow/arrow/down.svg");
  position: absolute;
  right: 0;
  top: 0;
}

.flow .flow-content .filter-content .list-filters li {
  margin-top: 16px;
}

.flow .flow-content .footer-filter-flow {
  width: 500px;
  height: 94px;
  padding: 26px 55px;
  box-shadow: 0 5px 30px 0 rgba(0, 0, 0, 0.4);
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: nowrap;
}

.flow .flow-content .footer-filter-flow .delete-all {
  flex: 1 1 auto;
  text-align: left;
}

@media (max-width: 767px) {
  .flow .flow-content .footer-filter-flow {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 424px) {
  .flow .flow-content .footer-filter-flow {
    padding: 5px;
    flex-wrap: wrap;
  }
  .flow .flow-content .footer-filter-flow .delete-all {
    width: 100%;
    text-align: center;
  }
  .flow .flow-content .footer-filter-flow .custom-button {
    margin: 0;
  }
}

.flow .flow-content .title-grey {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
  padding: 9px 15px 7px 15px;
  width: 168px;
  height: 33px;
  border-radius: 6px;
  vertical-align: middle;
  background: #f8f8fa url("/admin/images/flow/arrow/down.svg") no-repeat right 10px top 14px;
  cursor: pointer;
}

.flow .flow-content .title-grey.title--active-filters-open {
  background: #ffffff url("/admin/images/flow/arrow/up.svg") no-repeat right 10px top 14px;
}

.flow .flow-content .title-grey.title--active-filters-open:hover {
  background-color: #f8f8fa;
}

.flow .flow-content .grey-uppercase {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
}

.flow .flow-content .delete-all {
  font-size: 12px;
  color: #0041c4;
}

.flow .home-flow, .flow .filter-flow-content {
  height: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
}

.flow .home-flow {
  width: 495px;
  margin: 10px 5px 10px 0;
}

.flow .filter-flow-content {
  width: 500px;
  margin-top: 10px;
}

.flow .searching {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  /* Icone de suppression */
}

.flow .searching .button-delete {
  background: url("/admin/images/flow/trash_bin.svg") no-repeat center center;
  margin-left: 5px;
}

.flow button {
  border: none;
}

.flow button:hover {
  background-color: transparent;
  color: #7a869a;
}

.flow button:focus {
  box-shadow: none;
}

.flow .custom-button {
  width: auto;
  font-size: 15px;
  text-align: center;
  color: #7a869a;
  border-radius: 15px;
}

.flow .custom-button:hover {
  background-color: #e2e2ea;
  border: solid 1px #e2e2ea;
}

.flow .load-more {
  border: solid 1px #e2e2ea;
  display: block;
  margin: auto;
  margin-top: 30px;
  margin-bottom: 40px;
  padding: 10px 40px;
  font-size: 16px;
}

.flow .load-more:hover {
  background-color: #0041c4;
  color: #ffffff;
  border: solid 1px #0041c4;
}

.flow .filter-valid {
  margin: 0 20px;
  background-color: #5377fb;
  color: #ffffff;
  padding: 10px 20px;
}

.flow .filter-valid:hover {
  background-color: #0041c4;
  color: #ffffff;
  border: 0;
}

.flow .filter-cancel {
  padding: 10px 20px;
  border: 0;
}

.flow .filter-cancel:hover {
  background-color: #f2f4f7;
  color: #7a869a;
  border: 0;
}

.flow .title {
  font-size: 22px;
  font-weight: bold;
  letter-spacing: normal;
  color: #002251;
}

@media (max-width: 767px) {
  .flow .title {
    margin-bottom: 5px;
  }
}

.flow .container-date {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}

.flow .container-date .date-filter {
  background: url("/admin/images/flow/calendar.svg") no-repeat right 15px center;
  padding-left: 15px;
  width: 49%;
}

.flow .container-date .date-filter:focus {
  border: 1px solid #f2f4f7 !important;
  background-color: #ffffff !important;
  color: #002251 !important;
  box-shadow: none !important;
  background: url("/admin/images/flow/calendar_navyblue.svg") no-repeat right 15px center;
  opacity: 1;
}

.flow .container-date .date-filter:focus::-webkit-input-placeholder {
  color: #002251 !important;
}

.flow .container-date .date-filter:focus::-moz-placeholder {
  color: #002251 !important;
}

.flow .container-date .date-filter:focus:-ms-input-placeholder {
  color: #002251 !important;
}

.flow .container-date .date-filter:focus::-ms-input-placeholder {
  color: #002251 !important;
}

.flow .container-date .date-filter:focus::placeholder {
  color: #002251 !important;
}

@media (max-width: 424px) {
  .flow .container-date {
    flex-direction: column;
  }
  .flow .container-date .date-filter {
    width: 100%;
  }
}

.flow .input {
  opacity: 0.75;
  border-radius: 10px;
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  background-color: #f2f4f7 !important;
  border: 1px solid #f2f4f7;
  height: 40px !important;
  font-size: 15px;
  font-weight: normal;
}

.flow .input:hover {
  border: 1px solid #002251;
  opacity: 1;
}

.flow .check-options {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.flow .check-options .button-option {
  color: #002251;
  border-radius: 20px;
  height: 40px;
  font-size: 15px;
  padding: 0 15px;
  background-color: #ffffff;
  background-position: right 15px center;
  background-repeat: no-repeat;
  border: 1px solid #e2e2ea;
  text-align: left;
  margin-right: 3px;
}

.flow .check-options .button-option:not(.btn-opt--unselect):hover, .flow .check-options .button-option.option--is-select {
  color: #ffffff;
}

.flow .check-options .button-option.btn-opt--unselect {
  opacity: 0.4;
  cursor: initial;
}

.flow .check-options .button-option.option-unread:not(.btn-opt--unselect):hover, .flow .check-options .button-option.option-unread.option--is-select {
  background-color: #5377fb;
}

.flow .check-options .button-option.option-messages:not(.btn-opt--unselect):hover, .flow .check-options .button-option.option-messages.option--is-select {
  background-color: #ff6366;
}

@media (max-width: 424px) {
  .flow .check-options {
    flex-direction: column;
  }
  .flow .check-options .button-option {
    text-align: center;
  }
  .flow .check-options .button-option.option-unread {
    margin-bottom: 3px;
  }
}

.flow [type="checkbox"] {
  background-color: #ffffff;
  border: 1px solid #b1bcca;
  margin-right: 10px;
  color: #ffffff !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 3px;
}

.flow [type="checkbox"]:checked {
  border: 1px solid #0041c4;
  background: url("/admin/images/flow/checkbox/checked.svg") center center;
}

.flow [type="checkbox"]:focus {
  border: 1px solid #0041c4;
  box-shadow: none !important;
}

.flow [type="checkbox"]:not(:checked) + label {
  color: #7a869a;
}

.flow [type="checkbox"]:checked + label {
  color: #000;
}

.flow [type="radio"] + label {
  color: #7a869a;
}

.flow [type="radio"] + label.date--personalize {
  color: #7a869a;
  font-size: 15px;
  font-weight: normal;
  text-transform: uppercase;
  border-bottom: 0;
}

.flow [type="radio"]:checked {
  background-image: radial-gradient(ellipse at center, #0041c4 50%, #ffffff 50%) !important;
}

.flow [type="radio"]:checked + label, .flow [type="radio"]:checked + label.date--personalize {
  color: #002251;
}

.flow .link-text:hover, .flow .text-blue:hover {
  text-decoration: underline !important;
}

.flow .link-icon {
  width: 40px;
  height: 40px;
}

.flow .link-icon:hover {
  border-radius: 10px;
  background-color: #e5ecf9 !important;
}

.flow .text-blue {
  color: #5377fb;
  vertical-align: bottom;
}

.flow .text-price {
  color: #002251;
  vertical-align: bottom;
  font-weight: bold;
  white-space: nowrap;
}

.flow ::-webkit-scrollbar {
  width: 6px;
}

.flow ::-webkit-scrollbar-thumb {
  padding-top: 10px !important;
  background-color: #b5b5be;
  border-radius: 3px;
}

.flow ::-webkit-scrollbar-thumb:hover {
  background-color: #7a869a;
}

.flow .search {
  width: 100%;
  max-width: 100%;
  background: url("/admin/images/flow/search.svg") no-repeat left 15px center;
  padding-left: 45px;
}

.flow .search::-webkit-input-placeholder {
  color: #002251 !important;
}

.flow .search::-moz-placeholder {
  color: #002251 !important;
}

.flow .search:-ms-input-placeholder {
  color: #002251 !important;
}

.flow .search::-ms-input-placeholder {
  color: #002251 !important;
}

.flow .search::placeholder {
  color: #002251 !important;
}

.flow .search:focus {
  border: 1px solid #f2f4f7 !important;
  background-color: #ffffff !important;
  box-shadow: none !important;
  background: url("/admin/images/flow/search_navyblue.svg") no-repeat left 15px center;
  opacity: 1;
}

.flow .search:focus::-webkit-input-placeholder {
  color: #002251 !important;
}

.flow .search:focus::-moz-placeholder {
  color: #002251 !important;
}

.flow .search:focus:-ms-input-placeholder {
  color: #002251 !important;
}

.flow .search:focus::-ms-input-placeholder {
  color: #002251 !important;
}

.flow .search:focus::placeholder {
  color: #002251 !important;
}

.flow ul {
  list-style: none;
}

.flow button {
  min-height: 0;
}

@media (max-width: 767px) {
  .voile-opacit {
    display: none;
  }
  .flow-content {
    width: 100% !important;
  }
  .home-flow, .filter-flow-content {
    width: 99% !important;
  }
}

/* Pop commande rapide */
.quick-order-step{
  margin:0.5em 0;
}

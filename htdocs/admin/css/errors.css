.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.block-submenu, #site-submenu {
	display: none !important;
}
#site-content {
	margin-left: 0 !important;
	border-left: 0 !important;
}
#site-content-w {
	background-color: aliceblue;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
  display: block;
}

.http-errors img {
  max-width: 100%;
  height: auto;
  vertical-align: top;
}

.gm-style img {
  max-width: none;
}

.accessibility {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.accessibility:focus {
  position: static;
  width: auto;
  height: auto;
}

.hidden {
  position: absolute;
  left: -9999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.clearfix:after {
  content: '';
  display: block;
  clear: both;
}

.resetButton,
button,
[type="button"],
[type="reset"],
[type="submit"],
.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  padding: 0;
  background: none;
  cursor: pointer;
}

.ellipsis {
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-uppercase {
  text-transform: uppercase;
}

h2 {
  font-family: inherit;
  font-weight: 700;
  margin: 0 0 0.5em;
  color: black;
  display: block !important;
  font-size: 40px;
  background-color: white;
}

#site-content-w form {
  margin: 0;
  padding: 0;
  border-style: none;
}

#site-content-w input[type="search"] {
  -webkit-transition: border-color 0.3s ease-in-out;
  -o-transition: border-color 0.3s ease-in-out;
  transition: border-color 0.3s ease-in-out;
  border: 1px solid #e6e6e6;
  border-radius: 25px;
  font-size: 16px;
  line-height: normal;
  background-color: transparent;
  width: 100%;
  min-width: 0;
  height: 40px;
  padding: 0 12px;
  -webkit-appearance: none;
}

#site-content-w input[type="search"]:focus {
  border-color: #5377fb;
  outline: none;
}

input[type="search"]::-webkit-input-placeholder {
  color: #7a869a;
  font-size: 14px;
}

input[type="search"]::-moz-placeholder {
  opacity: 1;
  color: #7a869a;
  font-size: 14px;
}

input[type="search"]:-moz-placeholder {
  color: #7a869a;
  font-size: 14px;
}
input[type="search"]:-ms-input-placeholder {
  color: #7a869a;
  font-size: 14px;
}

input[type="search"].placeholder {
  color: #7a869a;
  font-size: 14px;
}

input[type="search"] {
  -webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button {
  display: none;
}

.btn {
  color: #002251;
  background-color: transparent;
  padding: 10px 15px;
  font-size: 13px;
  line-height: 1.3;
  font-weight: 500;
  text-decoration: none;
}

.btn:hover {
  text-decoration: none;
}
button:hover, button:focus {
	background-color: transparent !important;
	border-style: none !important;
	box-shadow: none !important;
}

.container {
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
  width: 100%;
}

#wrapper {
  position: relative;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-height: 100vh;
  min-height: -webkit-calc(var(--vh, 1vh) * 100);
  min-height: calc(var(--vh, 1vh) * 100);
}

.wrapper-inner {
  min-height: 100vh;
  min-height: -webkit-calc(var(--vh, 1vh) * 100);
  min-height: calc(var(--vh, 1vh) * 100);
  display: -ms-grid;
  display: grid;
  -ms-grid-rows: auto 1fr auto;
  grid-template-rows: auto 1fr auto;
  -ms-grid-template-columns: minmax(0, 1fr);
  -ms-grid-columns: minmax(0, 1fr);
  grid-template-columns: minmax(0, 1fr);
}

.wrapper-inner #header {
  -ms-grid-row: 1;
  grid-row: 1;
}

.wrapper-inner #main {
  -ms-grid-row: 2;
  grid-row: 2;
}

.wrapper-inner #footer {
  -ms-grid-row: 3;
  grid-row: 3;
}

.bg-light {
  background-color: #f1f6ff;
}

.message-section {
  padding: 40px 0;
  color: #002251;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.714;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.message-section .container {
  width: 100%;
}

.message-section h2,
.message-section .h2 {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.125;
  margin: 0 0 15px;
}

.message-section .search-form {
  margin: 0 0 22px;
}

.message-section .search-form input[type=search] {
  padding-right: 50px;
  margin-bottom: 12px;
}

.message-section .search-form .title {
  display: block;
  font-weight: 500;
  margin: 0 0 6px;
}

.message-section .search-form [type=submit] {
  position: absolute;
  right: 0;
  top: -3px;
  height: 45px;
  width: 46px;
  background-image: url(../dist/images/search.svg);
  background-position: 50% 50%;
  background-size: 26px;
  background-repeat: no-repeat;
}

.message-section .search-form .input-holder {
  position: relative;
}

.message-block {
  max-width: 550px;
  margin: 0 auto;
  -webkit-box-shadow: 0 2px 15px rgba(57, 98, 139, 0.08);
  box-shadow: 0 2px 15px rgba(57, 98, 139, 0.08);
  border-radius: 4px;
  border: 1px solid #e2e2ea;
  background-color: #ffffff;
  text-align: center;
  padding: 38px 20px 28px;
}

.message-block .img-holder {
  margin: 0 0 39px;
}

.message-block .title-holder {
  margin: 0 0 25px;
}

.message-block .btn {
  margin: 0 0 19px;
}

.message-block .btn:last-child {
  margin-bottom: 0;
}

.btn {
  -webkit-transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  -o-transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  display: inline-block;
  vertical-align: top;
  text-align: center;
  border-radius: 4px;
}

.btn.btn-blue {
  background-color: #5377fb;
  color: #fff;
}

.btn.btn-blue:hover {
  background-color: #2150fa;
}

.btn.btn-link:hover {
  text-decoration: underline;
}

input[type="search"]::-moz-placeholder {
  opacity: 1;
}

@media (min-width: 480px) {
  .message-block {
    padding: 38px 30px 28px;
  }

  .btn {
    padding: 13px 30px;
    font-size: 14px;
  }
}

@media (min-width: 640px) {
  .message-block {
    padding: 38px 75px 28px;
  }
}

@media (min-width: 768px) {
  input[type="search"] {
    height: 30px;
    border-radius: 15px;
  }
}

#site-location {
  display: none;
}
.treeview span.tree-nod-name {
	display: block;
}
.italic{ font-style: italic; }
.smaller{ font-size: 11px; }
.bold{ font-weight: bold }
.justify{ text-align: justify; }
.red{ color: red; }
.barre{ text-decoration: line-through; }
.underline{ text-decoration: underline; }
a{
	text-decoration: none;
}
a:hover{
	text-decoration: underline;
}
hr{
	margin-left: 50px;
	margin-right: 50px;
}
span.stat-title{
	font-weight: 600;
}
span.stat-data{
	padding-left: 10px;
}
/* Entête des pages */
.comparator {
	-moz-border-radius:10px;
	border-radius:10px;
	-moz-box-shadow:0 0 5px #C4C4C4;
	-webkit-box-shadow:0 0 5px #C4C4C4;
	box-shadow:0 0 5px #C4C4C4;
	border:1px solid #C4C4C4;
	margin-bottom: 10px;
	padding:10px;
}
#comparators #name{
	font-size: 20px;
	margin-left: 20px;
}
#comparators  div.img{
	border:none;
	float:left;
	height:100px;
	width:200px;
}
#comparators img{
	border:none;
}
img.link-copy{
	cursor:pointer;
	margin-left:5px;
}
#comparators div.info-comparator{
	float:left;
	height:100px;
	line-height:25px;
	margin-left:40px;
	margin-right:50px;
	width:124px;
}
#comparators div.info-comparator a {
	display: block;
}
#comparators div.ctr-info{
	height:auto;
}
#comparators div.action{
	border-top:1px solid #C4C4C4;
	margin-top:10px;
	padding-top:10px;
	text-align:center;
	width:100%;
}
#comparators div.param-activ{
	float:right;
	width:auto;
}
#comparators div.action input.actif{
	background-color:green;
	padding: 1px 2px;
    border: 1px solid green;
    color: #fff;
    font-weight: 600;
}
#comparators div.action input.actif:hover, #comparators div.action input.inactif:hover{
	color:#deded9;
}
#comparators div.action input.inactif{
	background-color: #FE5511;
    padding: 1px;
    border: 1px solid #FE5511;
    color: #fff;
    font-weight: 600;
}
div.stat li{
	list-style-position:inside;
	list-style-type:circle;
}
/* Paramètres */
#ctr-params tbody td{
	border-bottom:1px solid #C4C4C4;
	padding:10px;
}
#ctr-params select{
	width:auto !important;
}
/* Catégories */
#treeview-cat div.grp-cat{
	float: left;
	width: 25%;
}
#diff-cat{
	list-style-type: circle;
	margin-left: 55px;
}
#cat-exp{
	margin: 50px 10% 40px 40px;
}
#family-tree{
float: left !important;
margin-left: 150px;
width: 40%;
}
/* Fiche sur une catégories de produits */
#block-card {
	float: left;
	margin-top: 10px;
	width: 44%;
}
#card{
	border: 1px solid #A3A3A3;
	/*float: left;
	margin-left: 40px;
	margin-top: 10px;
	position: relative;
	width: 50%;*/
}
#card.fixed {
    position: fixed;
    top: 50px;
}
#card-title{
	background-color: #232E63;
	color: white;
	font-weight: 600;
	padding: 3px;
}
#card-title .close {
    background-image: url("/admin/images/stats/close.gif");
    cursor: pointer;
    display: inline-block;
    float: right;
    height: 12px;
    width: 13px;
}
.card-title-ctr{
	font-size: 12px;
	font-weight: 500;
}
#card-content{
	padding-top: 10px;
}
#card-content #desc{
	border-bottom: 1px solid #ADADAD;
	padding: 0px 20px 0;
}
#card #card-content p.desc{
	margin-left: 15px;
}
#card #card-content #choose input{
	margin: 5px 5px 5px 10px;
}
#card #choose{
	padding: 5px 20px 5px;
}
#card #card-action, #treeview-prd #card-action{
	padding: 5px;
	background-color: #D3D3D3;
}
#card-action input[type=submit]{
	font-size: 11px;
}
#treeview-card {
	margin-left: 15px;
	margin-top: 10px;
}
#treeview-cat-card{
	border: 1px solid #A3A3A3;
	height: 250px;
	margin: 10px auto;
	overflow-y:auto;
	width: 90%;
}
#categories-tree{
	float: left;
	margin-left: 50px;
	width: 35%;
}
#familly-tree{
	margin-left: 51px;
}
.sub{ background-color: #A3A3A3; border: 1px dotted black; padding: 5px; }
#site-content .expandable {
	display: block;
	font-weight: 600;
}
#site-content .treeview span, #site-content .treeview label {
	cursor: pointer;
}
#site-content .treeview span:hover {
	color: #000000;
	text-decoration: underline;
}
#site-content .treeview label:hover {
	color: #000000;
}
#site-content .tree-nod-name.selected {
	background-color: #c4c4c4;
	border: 1px dotted #000000;
	padding: 5px;
}
#site-content .tree-nod-name {
	display: block;
}
#site-content [id=choose-new-cat] tbody td {
	vertical-align: middle;
}
.block-conf-ctr {
	margin-bottom: 15px;
}
.block-conf-ctr [id=budget] {
	text-align: right;
	width: 75px;
}
#comparators.all {
	margin-top: 10px;
}
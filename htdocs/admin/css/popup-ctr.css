.ac_results {
	box-shadow: 5px 5px 5px #C4C4C4;
	margin-left: -148px;
	min-width: 290px;
	background-color:white;
	border:1px solid #C4C4C4;
	overflow:hidden;
	padding:0;
	z-index:99999;
	position: absolute;
}
.ac_results ul {
	width: 100%;
	max-height:180px !important;
	list-style-position: outside;
	list-style: none;
	padding: 0;
	margin: 0;
}

.ac_results li {
	cursor:pointer;
	margin: 0px;
	padding: 2px 5px;
	cursor: default;
	display: block;
	font: menu;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
}
.ac_loading {
	background-color: white;
}
.ac_odd {
	background-color: #eee;
}
.ac_over {
	background-color: #0A246A;
	color: white;
}
.clear {
	clear: both;
}
.active {
	color: green !important;
}
.error {
    background-color: #FFEFEF;
    border: 1px solid #FFA2A2;
    color: #CC0000;
    height: auto;
    margin: 10px 0;
    padding: 8px 12px;
}
#popupctr {
    margin: -8px;
}
#popupctr #tabpanel {
    clear: both;
    padding: 5px;
}
#popupctr h2 {
    border-bottom: 1px solid #A9A9A9;
    font-size: 16px;
    margin-bottom: 10px;
    padding-bottom: 3px;
}
#popupctr .media {
	border: 1px solid #999999;
    box-shadow: 0 0 3px #999999;
    float: left;
    height: 260px;
    width: 260px;
}
#popupctr .infos {
	margin-left: 10px;
	float: left;
}
#popupctr table {
	border: 1px solid #A3A3A3;
	border-collapse: collapse;
    clear: both;
}
#popupctr table caption {
	background-color: #294573;
    color: white;
    font-weight: 600;
    padding: 3px;
    text-align: left;
    white-space: nowrap;
}
#popupctr #tbl-infoprd {
	width: 594px;
	margin-bottom: 5px;
}
#popupctr #tbl-export {
	width: 867px;
}
#popupctr table thead tr th {
	background-color: #C4C4C4;
    padding: 3px;
}
#popupctr table tfoot tr td {
	background-color: #C4C4C4;
    padding: 5px;
    text-align: right;
}
#popupctr table tfoot tr th {
	background-color: #C4C4C4;
    padding: 5px;
    text-align: right;
}
#popupctr table tbody tr th.last, #popupctr table tbody tr td.last {
	border-bottom: medium none;
}
#popupctr table tbody tr th, #popupctr table tbody tr td {
    border-bottom: 1px dotted #C4C4C4;
    border-left: 1px solid #C4C4C4;
	padding: 3px;
	vertical-align: top;
}
#popupctr table tbody tr td {
	vertical-align: middle;
}
#popupctr table tbody tr th {
	background-color: #E3E3E3;
	text-align: left;
}
#popupctr table a {
    color: blue;
    /* font-size: 13px; */
    font-weight: 500;
    text-decoration: none;
}
#popupctr table a.cancel {
	color: red;
}
#popupctr table a:hover {
    text-decoration: underline;
}
input.save:hover {
	color: #294573;
	background-color: #FFD061;
}
input.save {
	background-color: #294573;
    border: medium none;
    border-radius: 7px 7px 7px 7px;
    color: #FFFFFF;
    font-weight: 600;
    padding: 3px;
	cursor: pointer;
}
.info label {
	display: block;
}
.info input.text, .info textarea {
    margin-bottom: 7px;
    margin-top: 3px;
    width: 713px;
}
.info textarea {
    height: 80px;
}
.info .actions {
    margin-bottom: 5px;
    text-align: right;
}
#popupctr .period {
	font-style: italic;
    font-weight: 500;
}
ul {
	margin: 0;
	padding: 0;
	list-style-position: inside;
	padding-left: 3px;
}
.message-return {
	padding: 0 !important;
}
.success {
    background-color: #DDFFDD;
    border: 1px solid green;
    color: black;
	margin: 10px 0;
    padding: 1em;
}
#infosearch {
	margin-bottom: 15px;
	text-align: left;
}
#infosearch label {
    display: block;
    font-weight: 600;
}
#infosearch input, #infosearch textarea {
    border: 1px solid #C4C4C4;
    padding: 5px;
    width: 300px;
}

hr {
    border-bottom: 1px solid #A9A9A9;
    border-style: none none solid;
    margin-bottom: 10px;
    margin-top: 10px;
}
#templatepriceminister {
    border: 1px solid #C4C4C4;
    box-shadow: 3px 3px 8px #C4C4C4;
    margin: 10px 8px 15px 0;
    padding: 10px;
	display: none;
}
#templatepriceminister label { float: left; width: 50%; }
#templatepriceminister label.radio { float: none; margin: 5px; width: auto; }
#templatepriceminister .elem { border-bottom: 1px dotted #C4C4C4; margin-bottom: 5px; padding-bottom: 4px; }
#templatepriceminister select,#templatepriceminister input.text { width: 49% !important; }
.mandatory { color: red; }

.val-positif{
	color: #006000;
    font-weight: 600;
}
body {
	font-family: "verdana";
	font-size: 12px;
}
#mediatheque-container {
	border: solid 1px #ccc;
	margin-top: 10px;
}
#nav {
	font-size: 11px;
	width: 120px;
	float: left;
	padding: 5px;
	height: 380px;
}
#nav li {
	list-style: none inside none;
}
#nav ul:first-child {
	margin: 0px;
	padding: 0px;
}
#nav ul {
	padding-left: 20px;
}
#nav .dir {
	background-image: url(/admin/js/tinymce/plugins/advimage/img/dir-close.png);
	background-repeat: no-repeat;
	display: block;
	margin-bottom: 5px;
	padding-left: 20px;
	color: black;
	text-decoration: none;
}
#nav li li .dir {
	text-decoration: underline;
}
#nav .dir:hover, #nav .dir.selected {
	color: #2B6FB6;
}
#mediatheque {
	width: 815px;
	overflow-y: scroll;
	float: right;
	height: 400px;
}
#mediatheque img {
	cursor: pointer;
	-moz-transition: all 0.5s;
	-webkit-transition: all 0.5s;
	transition: all 0.5s;
	border: solid 1px transparent;
	float: left;
	position: relative;
	z-index: 1;
	margin: 1px;
}
#mediatheque img.selected {
	cursor: default;
	border-color: #4080c0;
	-moz-box-shadow: 0px 0px 10px #4080c0;
	-webkit-box-shadow: 0px 0px 10px #4080c0;
	box-shadow: 0px 0px 10px #4080c0;
	z-index: 2;
}
.button {
	background: #232E63;
	-moz-box-shadow: 0 20px 7px -10px rgba(200, 200, 200, 0.3) inset;
	-webkit-box-shadow: 0 20px 7px -10px rgba(200, 200, 200, 0.3) inset;
	box-shadow: 0 20px 7px -10px rgba(200, 200, 200, 0.3) inset;
	border: none;
	border-radius: 7px;
	color: white;
	cursor: pointer;
	font-family: verdana;
	font-size: 11px;
	font-weight: 600;
	padding: 4px 8px;
}
.button:hover {
	background: #FFD061;
	color: #232E63;
}
.padt {
	padding-top: 10px;
}
.errors, .success {
	padding: 10px;
	margin-bottom: 10px;
}
.errors {
	border: solid 1px #800000;
	background: #ff8080;
}
.success {
	border: solid 1px #008000;
	background: #80ff80;
}
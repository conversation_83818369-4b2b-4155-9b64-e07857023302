@charset "UTF-8";
/* couleurs utilisées principalement pour  les bordures des tableaux */
:root {
  --mdc-theme-primary: $dark-color;
}

/**
 * CSS des sélecteurs
 */
#rialanguagepicker,
#riadatepicker:not(:empty),
#riawebsitepicker,
#closingpicker,
#expeditionspicker,
#riaothersfilters,
#riawebsitepicker,
#div-filter-sort,
#div-filter-type,
.riapicker {
  display: inline-block;
  margin: 0 6px 8px 0;
}

#rialanguagepicker .selector,
#riadatepicker:not(:empty) .selector,
#riawebsitepicker .selector,
#closingpicker .selector,
#expeditionspicker .selector,
#riaothersfilters .selector,
#riawebsitepicker .selector,
#div-filter-sort .selector,
#div-filter-type .selector,
.riapicker .selector {
  display: none;
  position: absolute;
  width: 266px;
  float: right;
  background-color: white;
  border-left: solid 1px #A9A9A9;
  border-right: solid 1px #A9A9A9;
  border-bottom: solid 1px #A9A9A9;
  border-top: solid 1px #A9A9A9;
  text-align: left;
  display: none;
  overflow-y: auto;
  z-index: 9999;
}

#rialanguagepicker .selector a,
#riadatepicker:not(:empty) .selector a,
#riawebsitepicker .selector a,
#closingpicker .selector a,
#expeditionspicker .selector a,
#riaothersfilters .selector a,
#riawebsitepicker .selector a,
#div-filter-sort .selector a,
#div-filter-type .selector a,
.riapicker .selector a {
  display: block;
  padding: 5px;
  margin: 1px 0;
  color: black;
  text-decoration: none;
}

#rialanguagepicker .selector a.child,
#riadatepicker:not(:empty) .selector a.child,
#riawebsitepicker .selector a.child,
#closingpicker .selector a.child,
#expeditionspicker .selector a.child,
#riaothersfilters .selector a.child,
#riawebsitepicker .selector a.child,
#div-filter-sort .selector a.child,
#div-filter-type .selector a.child,
.riapicker .selector a.child {
  margin-left: 30px;
}

#rialanguagepicker .selector a:hover,
#riadatepicker:not(:empty) .selector a:hover,
#riawebsitepicker .selector a:hover,
#closingpicker .selector a:hover,
#expeditionspicker .selector a:hover,
#riaothersfilters .selector a:hover,
#riawebsitepicker .selector a:hover,
#div-filter-sort .selector a:hover,
#div-filter-type .selector a:hover,
.riapicker .selector a:hover {
  background-color: #DADCFF;
}

#rialanguagepicker .selectorview,
#riadatepicker:not(:empty) .selectorview,
#riawebsitepicker .selectorview,
#closingpicker .selectorview,
#expeditionspicker .selectorview,
#riaothersfilters .selectorview,
#riawebsitepicker .selectorview,
#div-filter-sort .selectorview,
#div-filter-type .selectorview,
.riapicker .selectorview {
  padding: 5px;
  border: solid 1px #A9A9A9;
  width: 266px;
  text-align: left;
  cursor: pointer;
  height: 42px;
}

#rialanguagepicker .selectorview div.left,
#riadatepicker:not(:empty) .selectorview div.left,
#riawebsitepicker .selectorview div.left,
#closingpicker .selectorview div.left,
#expeditionspicker .selectorview div.left,
#riaothersfilters .selectorview div.left,
#riawebsitepicker .selectorview div.left,
#div-filter-sort .selectorview div.left,
#div-filter-type .selectorview div.left,
.riapicker .selectorview div.left {
  float: left;
}

#rialanguagepicker .selectorview a,
#riadatepicker:not(:empty) .selectorview a,
#riawebsitepicker .selectorview a,
#closingpicker .selectorview a,
#expeditionspicker .selectorview a,
#riaothersfilters .selectorview a,
#riawebsitepicker .selectorview a,
#div-filter-sort .selectorview a,
#div-filter-type .selectorview a,
.riapicker .selectorview a {
  padding: 10px 0;
  float: right;
  display: block;
}

#rialanguagepicker .selectorview img,
#riadatepicker:not(:empty) .selectorview img,
#riawebsitepicker .selectorview img,
#closingpicker .selectorview img,
#expeditionspicker .selectorview img,
#riaothersfilters .selectorview img,
#riawebsitepicker .selectorview img,
#div-filter-sort .selectorview img,
#div-filter-type .selectorview img,
.riapicker .selectorview img {
  border: none !important;
}

#rialanguagepicker .selectorview span.function_name,
#riadatepicker:not(:empty) .selectorview span.function_name,
#riawebsitepicker .selectorview span.function_name,
#closingpicker .selectorview span.function_name,
#expeditionspicker .selectorview span.function_name,
#riaothersfilters .selectorview span.function_name,
#riawebsitepicker .selectorview span.function_name,
#div-filter-sort .selectorview span.function_name,
#div-filter-type .selectorview span.function_name,
.riapicker .selectorview span.function_name {
  color: #8E8E8E;
  font-size: 0.9em;
}

#div-filter-sort .riapicker,
#div-filter-type .riapicker {
  display: block;
  margin: 0;
}

#infobulle, #infobulle2 {
  background-color: #FFF6CF;
  border: 1px solid #DFD299;
  padding: 11px;
  width: 280px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

#infobulle a, a#infobulle {
  color: #000;
}

.stats-menu-wo-results #infobulle {
  display: none;
}

.riapicker.prd-filters .selector {
  max-height: 395px;
}

#riadatepicker .selectordate {
  position: absolute;
  width: 260px;
  border-left: solid 1px #A9A9A9;
  border-right: solid 1px #A9A9A9;
  border-bottom: solid 1px #A9A9A9;
  background-color: #F0F4FB;
}

#riadatepicker .selectordate .options {
  height: 195px;
}

div#action input {
  background-image: url(/admin/images/stats/btn.gif);
  padding: 10px;
  border: solid 1px #A9A9A9;
  color: black;
  float: left;
  cursor: pointer;
}

div#action input:hover {
  background-image: url(/admin/images/stats/btn_hover.gif);
}

#site-content div.loader {
  position: absolute;
  margin-top: 45px;
  margin-left: 5px;
}

#site-content div.loader img {
  border: none;
}

.selector-sep {
  border-top: 1px solid #A9A9A9;
  cursor: default;
  margin: 0 10px !important;
  padding: 0 !important;
}

.selector {
  max-height: 325px;
  overflow-y: scroll;
}

#selectorigins .selector {
  max-height: none;
}

@media (max-width: 1023px) {
  /* Surcharge Langue Selecteur */
  #rialanguagepicker, #riadatepicker, #riawebsitepicker, #closingpicker, #expeditionspicker, #riaothersfilters, .riapicker {
    float: none;
    margin-right: 0 !important;
    position: relative;
  }
  #rialanguagepicker .selectorview, #riadatepicker .selectorview, #riawebsitepicker .selectorview, #closingpicker .selectorview, #expeditionspicker .selectorview, #riaothersfilters .selectorview, .riapicker .selectorview {
    width: 100%;
  }
  #rialanguagepicker .selector, #riadatepicker .selector, #riawebsitepicker .selector, #closingpicker .selector, #expeditionspicker .selector, #riaothersfilters .selector, .riapicker .selector {
    width: 100% !important;
  }
}

#tabpanel > .stats-menu {
  margin-top: 9px;
}

#popup_ria .select-tenant .select-tenant-list {
  margin: 10px 20px;
}

#popup_ria .select-tenant li {
  margin: 0;
  list-style: none;
}

#popup_ria .select-tenant li:not(.hide) {
  border: 1px solid #232E63;
  border-top: 0px;
}

#popup_ria .select-tenant li.first {
  border-top: 1px solid #232E63;
}

#popup_ria .select-tenant li a {
  color: #232E63;
  display: block;
  padding: 10px 0;
  font-size: 1.2em;
  font-weight: 600;
}

#popup_ria .select-tenant li a:hover {
  border-color: #5377FB;
  background-color: #5377FB;
  color: #fff;
  text-decoration: none;
}

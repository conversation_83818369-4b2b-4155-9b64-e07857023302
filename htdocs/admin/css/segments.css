#form-segment .add-seg-cdt {
    margin: 10px 16px;
	text-align: right;
}
#form-segment .add-seg-cdt legend {
	text-align: left;
}
#form-segment .cdt-config select {
    border: 1px solid #848484;
    margin-right: 5px !important;
    padding: 2px;
}
#form-segment .cdt-config input {
    margin-right: 5px !important;
    padding: 2px 3px 3px 3px;
}
#form-segment .cdt-grp {
	min-width: 100%;
	max-width: 100%;
}
#form-segment .cdt-config {
	text-align: left;
}
#form-segment .cdt-config label + div {
    display: inline-block;
    float: none;
    vertical-align: middle;
}
#form-segment .cdt-config sub {
	width: 190px !important;
}
#form-segment .cdt-psy {
	width: 180px !important;
}
#form-segment .cdt-grp-legend {
	padding: 0px 5px;
}
#form-segment .cdt-grp-rule{
	width: 38px !important;
}
#form-segment .cdt-grp-rule-items {
	width: 140px !important;
}

#form-segment .cdt-source-cdt {
    width: 250px !important;
}
#form-segment .cdt-source-cdt option {
    padding-top: 2px;
    padding-left: 5px;
}
#form-segment .cdt-source-cdt optgroup {
    margin-top: 5px;
}
#form-segment .cdt-source-cdt optgroup option {
    padding-left: 15px;
}
#form-segment .cdt-grp-value {
	width: 165px !important;
    box-sizing: border-box;
}
#form-segment .cdt-grp-value-span {
	display: inline-block;
}
#form-segment .cdt-grp-prd-search{
    width: 161px !important;
    box-sizing: border-box;
}
#form-segment .cdt-form-bool {
	display: inline;
}
/* #form-segment .cdt-form-bool *{
	margin-top: 4px;
} */
#form-segment .cdt-form-bool label {
    float: left !important;
    text-align: left !important;
    width: auto !important;
}
#form-segment .cdt-form-bool input {
	float: left !important;
}
/* #form-segment .div-source-cdt label, 
#form-segment .div-website-cdt label, 
#form-segment .div-dates-cdt label, 
#form-segment .div-fields-cdt label {
    width: 95px;
} */
#form-segment .div-dates-cdt label.end {
    width: 20px;
}
#form-segment .div-dates-cdt .date-start {
    float: left;
}
#form-segment .div-website-cdt .cdt-website-cdt, #form-segment .div-fields-cdt .cdt-fields-cdt {
	width: 250px !important;
}
#form-segment .cdt-select-second, #form-segment .cdt-psy-val {
    clear: none !important;
    display: block;
    float: left;
    /* padding: 2px; */
    margin: 0;
}
#form-segment .seg-load-cdt {
    font-size: 1.7em;
    font-weight: 600;
    text-align: left;
}
#form-segment .seg-load-cdt img {
	border: medium none !important;
    float: left;
}
#form-segment .seg-load-cdt span {
    display: block;
    margin-bottom: 9px;
    margin-left: 65px;
    margin-right: 9px;
    padding-top: 9px;
}
#form-segment .cdt-separator-and {
    font-weight: 600;
    margin: 6px 0 -10px -10px;
    text-align: left;
}
#form-segment .cdt-separator-or {
    float: left;
    font-weight: 600;
    margin-right: 5px;
    padding: 6px 0;
    text-align: left;
    width: 23px;
}
#form-segment #tb-synthese-order {
    float: none;
    margin-bottom: 10px;
}
/* #form-segment .riapicker .selectorview {
    height: 17px;
    padding: 2px;
    padding-right: 0;
    border: solid rgb(132, 132, 132) 1px;
    width: 250px;
    height: 24px;
    border-radius: 6px;
    background-image: url('/admin/dist/images/input-select.svg');
	background-position: right -1px top -1px;
	background-repeat: no-repeat;
	background-size: 29px;
} */
#form-segment .riapicker .selectorview .btn {
    display: none;
}
#form-segment .cdt-select-second .clear {
    display: none;
}

#form-segment .riapicker .left {
    padding: 0;
}

#form-segment .riapicker .view {
    line-height: 22px;
}
#form-segment .riapicker .btn {
    padding-top: 5px;
}

#form-segment .selector {
    max-height: 200px;
}
#form-segment .selector .parent label{
    font-weight: 600;
    font-size: 1em;
}
#form-segment .selector label {
    display: initial;
    float: none;
}
#form-segment .selector label:hover {
    cursor: pointer;
}
#form-segment .selector a:hover{
    text-decoration: none;
}
#form-segment .check-hidden input {
    display: none;
}


.ui-selectmenu-menu.ui-front.ui-selectmenu-open {
    max-height: 250px;
    background: #fff;
    overflow-y: auto;
    overflow-x: hidden;
}
/* .cdt-psy-val {
    width: 195px;
} */
.cdt-select-second .ui-selectmenu-button.ui-button{
    width: 220px;
}
.cdt-select-second .cdt-grp-cdt + span.ui-button{
    width: 320px;
    margin: 0;
}
 .ui-selectmenu-button.ui-button {
    height: 16px;
    text-align: left;
    white-space: nowrap;
    background: #fff;
    /*box-sizing: border-box;*/
    padding: 2px 12px;
    color: #000;
    margin-right:0;
    border: solid rgb(132, 132, 132) 1px;
    background: url("/admin/images/stats/fleche.gif") no-repeat;
    background-position: right;
}
.ui-menu .ui-menu-item {
    margin: 0 11px;
}
.ui-widget {
    font-family: inherit;
}
#form-segment.clear{
    padding: 0;
}
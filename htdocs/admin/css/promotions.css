/* #pmt-special .cdt-grp-del {
	background-color: #FFFFFF; 
	color: #FF0000; 
	border: medium none;
} */

.cdt-grp-del.cdt-grp {
	width: 605px !important;
}
#prd-offert {
	width: 420px !important;
}
.reduc-buy {
	float: left;
	margin-right: 4px;
}
.reduc-free {
	margin-left: 150px;
}
.reduc-nbr {
	margin-top: 2px;
}
.qte-reduc {
	width: 30px !important;
}

.cdt-config {
	text-align: left;
}

.cdt-grp-legend {
	padding: 0px 5px;
}
.cdt-grp-cdt {
	border: 1px solid #848484;
	float: left;
    width: 220px !important;
	padding: 3px;
}
.cdt-grp-cdt option {
    /* width: 135px !important; */
	margin-left: 10px;
}
.cdt-grp-value {
	/* border: 1px solid #848484; */
    margin-left: 5px;
	/* width: 165px !important;
	padding: 3px; */
}
select.cdt-grp-value {
	/* width: 173px !important; */
}
.cdt-grp-prd-search{
	width: 311px !important;
}
#pmt-add-rule label.inline {
    text-align: left !important;
    width: 175px !important;
}
#pmt-label-ref2 {
	display: inline !important;
	float: none !important;
	width: 10px !important;
}
#pmt-add-rule input.text {
    width: 300px;
}
.pmt-rules-buttons {
    text-align: right;
}
.pmt-rules-prd-import, .pmt-rules-usr-import {
	float: left;
}
#site-content table tbody #pmt-list-rules-prd {
	padding-bottom: 10px;
}
#pmt-list-rules-user select {
	width: 100% !important;
}
#pmt-list-rules-prd .notice, #pmt-list-rules-user .notice {
	margin: 10px;
}
#pmt-prf-name, #pmt-seg-name {
	width: 303px !important;
}
#nlr {
	width: 195px !important;
}
.pmt-used {
	color: red;
}
.pmt-not-used {
	color: green;
}
.pmt-qte-gen {
	float: left;
}
#pmt-special {
    width: 665px;
}
#site-content table.pmt-special .readonly {
    background-color: #F0F0F0;
    border: 1px solid #AFAFAF;
    color: #6D6D6D;
    padding: 1px;
}
#stats-rewards .bold {
	display: inline;
}
#site-content #pmt-list-prd-offers select {
	width: 100%;
}
#site-content #pmt-list-prd-offers select option {
	padding: 1px;
}
#site-content #pmt-list-prd-offers fieldset label {
    line-height: 21px;
    margin-left: 25px;
    text-align: left;
    width: 77px;
}
#site-content  #pmt-list-prd-offers fieldset legend {
	font-weight: 600;
	padding: 0 10px;
}
#site-content #pmt-list-prd-offers fieldset div input.text {
	width: 150px;
}
#site-content #pmt-list-prd-offers #list-prd-offers label {
	float: left;
	line-height: 16px;
	margin: 0;
	width: auto;
}
#site-content #pmt-list-prd-offers #list-prd-offers input.text {
    float: left;
    margin: 0 5px;
    width: 150px;
}
#site-content #pmt-list-prd-offers #list-prd-offers input.qty {
	text-align: right;
	width: 35px;
}
#site-content #pmt-list-prd-offers #list-prd-offers .prd-pop {
	margin-left: 25px;
}

#site-content #pmt-list-prd-offers .error {
    background-color: #ffdddd !important;
    border: 1px solid red;
    color: #000000 !important;
    padding: 5px !important;
}
#site-content .cdt-config .cdt-same-qte label {
    float: none;
    text-align: left;
    width: auto;
}
#site-content .cdt-config .cdt-same-qte input {
    float: left;
    margin-left: 53px;
    margin-right: 5px;
}
#site-content table tbody td.pmt-spe-rules {
	border: none;
	padding: 5px;
}
#site-content table tbody td.pmt-spe-rules:hover {
	background-color: white;
}
#site-content table tbody td.with-border-top {
	border-top: 1px solid #ccc;
}
#site-content table tbody td.with-border-bottom {
	border-bottom: 1px solid #ccc;
}
#site-content #tb-tabProducts fieldset legend {
	padding: 0 5px;
}
#site-content table tbody #select-discount-type {
	width: 100px;
}

#site-content .tb_rewards .select-colisage {
	width: auto;
}
#site-content .tb-colisage {
	border: none;
	margin-top: 0px;
	margin-bottom: 0px;
}
#site-content .tb-colisage td {
	border: none;
	border-bottom: none !important;
	padding: 0px;
}
#site-content .tb-colisage td select {
	margin-left: 5px;
}
#site-content #discount-tva {
	width: 100px;
}

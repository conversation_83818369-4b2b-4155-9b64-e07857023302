body {
	background-image: none;
}
#site-content-w {
	overflow-x: auto;
}
#site-menu #site-menu-home, #site-menu #site-menu-options {
	display: none;
}
.page_timer {
	top: 44px;
	left: 60px;
	width: 170px;
	color: #fff;
}
#site-content dl.search-result:hover .search-result-tabs {
	display: none !important;
}
#rialanguagepicker,
 #riadatepicker,
 #riawebsitepicker,
 #closingpicker,
 #expeditionspicker,
 #riaothersfilters,
 #riawebsitepicker,
 #div-filter-sort,
 #div-filter-type,
 .riapicker {
	box-sizing: border-box;
	width: 100%;
	float: none;
}
#riadatepicker .selectorview, #riawebsitepicker .selectorview, #rialanguagepicker .selectorview, .riapicker .selectorview {
	box-sizing: border-box;
	width: 100% !important;
}
#site-content #tb-synthese-order {
	float: none !important;
}

/* Force table to not be like tables anymore */
#site-content table.list:not(#table-map-file) th, #site-content table.list:not(#table-map-file) td, #site-content table.list:not(#table-map-file) tr, #site-content table.list:not(#table-map-file) caption,
#site-content table.orders th, #site-content table.orders td, #site-content table.orders tr, #site-content table.ordert caption {
	display: block;
}
#site-content table.list, #site-content table.orders {
	width: 100%;
}

#site-content table.orders thead {
	display: none;
}
#site-content table.orders td {
	text-align: right;
	border-right: none;
}
#site-content table.orders td:before {
	width: 40%;
	font-weight: 500;
	font-size: 12px;
	padding-left: 4px;
	padding-top: 2px;
	text-align: left;
	float: left;
	max-width: 50%;
}
#site-content table.orders td[headers="hd-order-total"]:before {
	content:'Commandes : ';
}
#site-content table.orders td[headers="hd-order-ht"]:before {
	content:'Total HT : ';
}
#site-content table.orders td[headers="hd-order-prds"]:before {
	content:'Nombre de produits par commande : ';
}
#site-content table.orders td[headers="hd-order-ttc"]:before {
	content:'Total TTC : ';
}
#site-content table.orders td[headers="hd-order-avg-ht"]:before,
#site-content table.orders td[headers="hd-order-avg"]:before {
	content:'Panier moyen HT : ';
}
#site-content table.orders td[headers="hd-order-avg-ttc"]:before {
	content:'Panier moyen TTC : ';
}
#site-content table.orders-customers td[headers="hd-order-avg-ht"]:before {
	content:'CA moyen HT : ';
}
#site-content table.orders-customers td[headers="hd-order-avg-ttc"]:before {
	content:'CA moyen TTC : ';
}
#site-content table.orders td[headers="hd-order-margin"]:before {
	content:'Marge brute : ';
}
#site-content table.orders td[headers="hd-rewards-total"]:before {
	content:'Solde : ';
}
#site-content table.orders td[headers="hd-rewards-ttc"]:before {
	content:'Solde en TTC : ';
}
#site-content table.orders td[headers="hd-rewards-used"]:before {
	content:'Utilisés : ';
}
#site-content table.orders td[headers="hd-rewards-used-ttc"]:before {
	content:'Utilisés en TTC : ';
}
#site-content table.orders td[headers="hd-rewards-no-used"]:before {
	content:'Non utilisés : ';
}
#site-content table.orders td[headers="hd-rewards-no-used-ttc"]:before {
	content:'Non utilisés en TTC : ';
}
#site-content table.list:not(#table-map-file) thead, td[headers="ord-livr"] {
	display: none;
}
#site-content table.list:not(#table-map-file) tbody tr {
	padding: 10px;
	overflow: auto;
	zoom: 1;
}
#site-content table.list:not(#table-map-file) tbody tr:nth-child(2n){
	background: #eee;
}
#site-content table.list:not(#table-map-file) tbody td {
	border: 0 none;
}
#site-content table.list:not(#table-map-file) td {
	word-wrap: break-word;
}
#site-content table.list:not(#table-map-file) td[headers="ord-id"],
#site-content table.list:not(#table-map-file) td[headers="ord-date"],
#site-content table.list:not(#table-map-file) td[headers="ord-ht"],
#site-content table.list:not(#table-map-file) td[headers="ord-ttc"] {
	float: left;
	width: calc(50% - 4px) !important;
}
/* #site-content table.list:not(#table-map-file) td[headers="ord-id"], */
#site-content table.list:not(#table-map-file) td[headers="ord-date"] {
	height: 22px;
	line-height: 22px;
}

#site-content table.list:not(#table-map-file) td[headers="ord-inv"] {
	clear: both;
}
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_address1,
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_address2,
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_address3,
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_postal_code,
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_city,
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_country {
	display: none;
}
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] .inv_society {
	display: inline-block;
	margin-right: 10px;
}
#site-content table.list:not(#table-map-file) td[headers="ord-inv"] {
	margin-top: 6px;
}
#site-content table.list:not(#table-map-file) td[headers="ord-inv"]:before {
	content:'Adresse de facturation : ';
	display: block;
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-livr"] {
	margin-top: 6px;
}
#site-content table.list:not(#table-map-file) td[headers="ord-livr"]:before {
	content:'Adresse de livraison : ';
	display: block;
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-type-pay"] {
	margin-top: 6px;
}
#site-content table.list:not(#table-map-file) td[headers="ord-type-pay"]:before {
	content:'Moyen de paiement : ';
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-state"] {
	margin-top: 6px;
}
#site-content table.list:not(#table-map-file) td[headers="ord-state"]:before {
	content:'Etat/Statut : ';
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-ht"] {
	text-align: left;
}
#site-content table.list:not(#table-map-file) td[headers="ord-ht"]:before {
	content:'Total HT : ';
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-ttc"]:before {
	content:'Total TTC : ';
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) td[headers="ord-date"] {
	text-align: right;
}
#site-content table.list:not(#table-map-file) #orders-total-ht:before {
	content:'HT : ';
	font-weight: 600;
}
#site-content table.list:not(#table-map-file) #orders-total-ttc:before {
	content:'TTC : ';
	font-weight: 600;
}
.lng-menu, .moderation-menu {
	height: auto;
}
#site-content .moderation-menu .selectorview {
	width: auto !important;
}
#cntcontact {
	width: 100%;
}
#cntcontact th, #cntcontact td, #cntcontact tr, #cntcontact caption {
	display: block;
}
#cntcontact thead {
	display: none;
}
#cntcontact .td-author, #cntcontact .td-message {
	display: table-cell;
	padding: 15px;
	border-right: 0 none;
}
#cntcontact textarea {
	width: 100% !important;
}
.btn-del, .btn-move, #export-categories {
	float: none;
}
#site-content #pagination td:first-child {
	text-align: center !important;
}
#site-content #pagination td:last-child {
	text-align: center !important;
	line-height: 20px;
}
#site-content #pagination td:last-child a {
	padding: 5px 3px;
	line-height: 30px;
}
#site-content #pagination td:last-child a:first-child {
	font-size: 0;
}
#site-content #pagination td:last-child a:first-child:before {
	content: '«';
	font-size: 12px;
	padding: 5px 3px;
}
#site-content #pagination td:last-child a:last-child {
	font-size: 0;
}
#site-content #pagination td:last-child a:last-child:before {
	content: '»';
	font-size: 12px;
	padding: 5px 3px;
}
.th-col-hide {
	display: none !important;
}
#site-content table.fidelity td:before ,
#site-content table.checklist td:before {
	font-weight: 600;
}

/* Tableau Catégories */
#site-content table.checklist td[headers="cat-sel"],
#site-content table.checklist td[headers="cat-name"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="cat-publish"]:before {
	content: 'Publiée : ';
}
[headers="cat-products"]:before {
	content: 'Produits publiés : ';
}

/* Tableau Produits */
#site-content table.checklist td[headers="prd-sel"],
#site-content table.checklist td[headers="prd-is-sync"],
#site-content table.checklist td[headers="prd-ref"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="prd-barcode"]:before {
	content: 'Code-barres : ';
}
[headers="prd-title"]:before {
	content: 'Titre : ';
}
[headers="prd-price_ht"]:before {
	content: 'Prix HT : ';
}
[headers="prd-tva_rate"]:before {
	content: 'TVA : ';
}
[headers="prd-price_ttc"]:before {
	content: 'Prix TTC : ';
}
[headers="prd-promo"]:before {
	content: 'Prix en promotion : ';
}
[headers="prd-publish"]:before {
	content: 'Publié gescom ? : ';
}
[headers="prd-publish-site"]:before {
	content: 'Publié site ? : ';
}
[headers="prd-stock"]:before {
	content: 'Stock réel : ';
}
[headers="prd-weight"]:before {
	content: 'Poids Brut : ';
}
[headers="prd-weight_net"]:before {
	content: 'Poids Net : ';
}
[headers="prd-length"]:before {
	content: 'Longueur : ';
}
[headers="prd-width"]:before {
	content: 'Largeur : ';
}
[headers="prd-height"]:before {
	content: 'Hauteur : ';
}
[headers="prd-brd_title"]:before {
	content: 'Marque : ';
}
[headers="prd-childonly"]:before {
	content: 'seulement ? : ';
}
[headers="prd-follow_stock"]:before {
	content: 'stock ? : ';
}
[headers="prd-orderable"]:before {
	content: 'Commandable ? : ';
}
[headers="prd-countermark"]:before {
	content: 'Contremarque ? : ';
}
[headers="prd-new"]:before {
	content: 'Nouveauté ? : ';
}
[headers="prd-sleep"]:before {
	content: 'sommeil ? : ';
}
[headers="prd-selled"]:before {
	content: 'Nb ventes : ';
}
[headers="prd-date_created"]:before {
	content: 'Date de création : ';
}
[headers="prd-date_modified"]:before {
	content: 'Date de dernière modification : ';
}
[headers="prd-date_published"]:before {
	content: 'Date de  première publication : ';
}
[headers="prd-fields-missing"]:before {
	content: 'Champs manquants : ';
}

/* Tableau Marques */
#site-content table.checklist td[headers="brd-sel"],
#site-content table.checklist td[headers="brd-name"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="brd-url"]:before {
	content: 'Site Web : ';
}
[headers="brd-publish"]:before {
	content: 'Publiée : ';
}
[headers="brd-prd"]:before {
	content: 'Produits publiés : ';
}

/* Tableau Nomenclature variable */
#site-content table.checklist td[headers="checkbox"] {
	float: left;
	vertical-align: middle;
	border-bottom: 0 none;
}
#site-content table.checklist td[headers="name"] {
	display: block;
}

/* Tableau Relations */
#site-content table.checklist td[headers^="rel-check"],
#site-content table.checklist td[headers^="rel-is-sync"],
#site-content table.checklist td[headers^="rel-ref"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers^="rel-name"]:before {
	content: 'Désignation : ';
}
[headers^="rel-publish"]:before {
	content: 'Publié gescom : ';
}
[headers^="rel-publish-site"]:before {
	content: 'Publié site : ';
}

/* Tableau Comptes clients */
#site-content table.checklist td[headers="usr-sel"],
#site-content table.checklist td[headers="usr-name"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="usr-created"]:before {
	content: 'Date d\'inscription : ';
}
[headers="usr-last-visit"]:before {
	content: 'Dernière visite : ';
}
[headers="usr-prf"]:before {
	content: 'Profil : ';
}
[headers="usr-sellers"]:before {
	content: 'Représentant : ';
}
[headers="usr-orders"]:before {
	content: 'Commandes validées : ';
}
[headers="usr-orders-canceled"]:before {
	content: 'Commandes annulées : ';
}
[headers="usr-accounts"]:before {
	content: 'Comptes suivis : ';
}
[headers="usr-order-avg"]:before {
	content: 'Commandes/client : ';
}
[headers="usr-order-avg-ht-value"]:before {
	content: 'CA Moyen HT : ';
}
[headers="usr-orders-total-ht"]:before {
	content: 'CA Total HT : ';
}


/* Tableau Gestion des droits */
/* #tabpanel #prf-rights > tbody > tr,
#tabpanel #prf-rights > tfoot > tr {
	display: table-row;
} */
/* #tabpanel #prf-rights > tbody > tr > th,
#tabpanel #prf-rights > tbody > tr > td,
#tabpanel #prf-rights > tfoot > tr > th,
#tabpanel #prf-rights > tfoot > tr > td {
	display: table-cell;
	width: auto;
	text-align: left;
} */

/* Tableau Promotions */
#site-content table.prc-tva-eco tbody td {
	border-left: 0 none;
	border-right: 0 none;
}

/* Tableau Codes promotions */
#site-content table.checklist td[headers="pmt-select"],
#site-content table.checklist td[headers="pmt-label"],
#site-content table.checklist td[headers="pmt-code"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="pmt-start"]:before {
	content: 'Ouverture : ';
}
[headers="pmt-stop"]:before {
	content: 'Fermeture : ';
}
[headers="pmt-desc"]:before {
	content: 'Description : ';
}
[headers="pmt-state"]:before {
	content: 'État : ';
}

/* Tableau Actualités */
#site-content table.checklist td[headers="news-sel"],
#site-content table.checklist td[headers="news-name"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="news-pub-date"]:before {
	content: 'Publier le : ';
}
[headers="news-pub"]:before {
	content: 'Publiée : ';
}
[headers="news-pos"]:before {
	content: 'Position : ';
}

/* Tableau Alertes de disponibilité */
#site-content table.checklist td[headers="sel"],
#site-content table.checklist td[headers="ref"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="brd-name"]:before {
	content: 'Marque : ';
}
[headers="email"]:before {
	content: 'Adresse email : ';
}
[headers="account"]:before {
	content: 'Compte client : ';
}
[headers="date"]:before {
	content: 'En attente depuis le : ';
}
[headers="restocking"]:before {
	content: 'À chaque remise en stock : ';
}
[headers="account"]:before {
	content: 'Dernier envoi : ';
}

/* Tableau Bannières */
#site-content table.checklist td[headers="bnr-sel"],
#site-content table.checklist td[headers="bnr-name"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="bnr-url"]:before {
	content: 'Url : ';
}
[headers="bnr-from"]:before {
	content: 'Du : ';
}
[headers="bnr-to"]:before {
	content: 'Au : ';
}
[headers="bnr-emp"]:before {
	content: 'Emplacement : ';
}
[headers="bnr-pos"]:before {
	content: 'Position : ';
}

/* Tableau Cms */
#site-content table.checklist td[headers^="cms-sel"] {
	float: left;
	width: auto;
}
#site-content table.checklist td[headers^="cms-name"] {
	width: auto;
}
[headers^="cms-published"]:before {
	content: 'Publiée : ';
}

/* Tableau Cms Révision */
#site-content table.checklist td[headers="cms-ver-sel"] {
	float: left;
	width: auto;
}
#site-content table.checklist td[headers="cms-ver-date"] {
	width: auto;
}
[headers="cms-ver-type"]:before {
	content: 'Type de modification : ';
}
[headers="cms-ver-user"]:before {
	content: 'Modifié par : ';
}
/* Tableau Codes promotions */
#site-content table.checklist td[headers="inc-check"],
#site-content table.checklist td[headers="inc-rules"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}

/* Tableau Gestion de parc / Flotte d'appareils */
#site-content table.checklist td[headers="dev-checked"],
#site-content table.checklist td[headers="dev-active"],
#site-content table.checklist td[headers="dev-key"] {
	display: inline-block;
	vertical-align: middle;
	border-bottom: 0 none;
}
#site-content table.checklist td[headers="dev-version"] {
	border-bottom: 0 none;
}
[headers="dev-version"]:before {
	content: 'Version : ';
}
[headers="dev-user"]:before {
	content: 'Utilisateur : ';
}
[headers="dev-last-call"],
[headers="dev-last-sync"] {
	text-align: left;
}
[headers="dev-last-call"]:before {
	content: 'Dernier signal : ';
}
[headers="dev-last-sync"]:before {
	content: 'Dernière synchro : ';
}
[headers="dev-last-location"]:before {
	content: 'Localisation : ';
}
[headers="reports-id"]:before {
	content: 'Rapport n° : ';
}
[headers="reports-author"]:before {
	content: 'Auteur : ';
}
[headers="reports-user"]:before {
	content: 'Client : ';
}
[headers="reports-created"]:before {
	content: 'Créé le : ';
}

/* Tableau Zones de livraison */
#site-content table.checklist td[headers="select"],
#site-content table.checklist td[headers="ref"] {
	display: inline-block;
	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="is-active"]:before {
	content: 'Activée : ';
}

/* Tableau Magasin */
[headers="address"]:before {
	content: 'Adresse : ';
}

/* Tableau Dépôts */
[headers="cat-princial"]:before {
	content: 'Principal : ';
}

.list-cols-changed .prd-col-nomove {
	position: static !important;
}

#site-content img {
	max-width: 100%;
	height: auto;
}
div.action {
	padding-top: 8px;
}
#tabpanel {
	margin-top: 10px;
}
/* #tabpanel > table > tbody > tr, table[summary] > tbody > tr {
	padding: 10px 5px 0;
} */
/* #tabpanel > table > tbody > tr td:first-child label, table[summary] > tbody > tr td:first-child label {
	font-weight: 600;
} */
/* #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > th, #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > td, #site-content #tabpanel > table > tbody > tr,
#site-content table:not(#prf-rights):not(.w-600)[summary] > tbody > tr > th, #site-content table:not(#prf-rights):not(.w-600)[summary] > tbody > tr > td, table:not(#prf-rights):not(.w-600)[summary] > tbody > tr,
#site-content #tabpanel > table:not(#prf-rights) > tfoot > tr > th, #site-content #tabpanel > table > tfoot > tr > td, #site-content #tabpanel > table > tfoot > tr,
table[summary] > tfoot > tr > th, #site-content table:not(.w-600)[summary] > tfoot > tr > td, table:not(.w-600)[summary] > tfoot > tr{
	width: 100%;
	box-sizing: border-box;
} */
.stats-search-table[summary] td, .stats-search-table[summary] th {
	width: auto;
}
/* #tabpanel > table:not(#prf-rights), table:not(#prf-rights)[summary] {
	width: 100% !important;
} */
#site-content table caption, #popup-content table caption {
	white-space: normal;
}
#site-content table td {
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
}
#site-content table tbody input:not(input[type=checkbox]),
#site-content table tbody textarea,
#site-content table tbody select,
#popup-content table tbody input,
#popup-content table tbody textarea,
#popup-content table tbody select {
	max-width: 100%;
	/* width: 100%; */
	box-sizing: border-box;
}
#site-content table table {
	table-layout: fixed;
}
#site-content textarea.tinymce {
	width: 100%;
}
.mceLayout {
	width: 100% !important;
}
#site-content table tbody textarea#desc {
	width: 100%;
}
colgroup {
	display: none;
}
#popup-content colgroup {
	display: block;
}
#site-content #tabpanel {
	border: 0 none;
	padding: 0;
}

/* Surcharge button export (sur fiche magasin) */
#site-content .btn-export {
	padding: 4px 10px;
}

/* Select */
select {
	max-width: 100%;
}

/* Liens */
.th-col-show {
	white-space: normal;
}

#taxes input[type=button] {
	font-size: 12px;
	width: auto;
}

/* Onglet Fiche produit / Images ratachées */
#site-content .imgs-primary-lbl {
	height: auto;
}
#sortable2, #sortable1 {
	min-height: 0;
}

/* Onglet Fiche produit / Comparateurs / Place de marchés */
#prd-comparator .info input.text, #prd-comparator .info textarea {
	width: 100%;
}

#site-content table tbody select.large, #popup-content table tbody select.large {
	width: 100%;
}


.add-pmt-cdt {
	box-sizing: border-box;
}
.cdt-grp {
	width: 100% !important;
}
#pmt-cmt {
	width: 100% !important;
}
#site-content table tbody fieldset {
	box-sizing: border-box;
	width: 100%;
}

#popup-content table caption {
	display: table-caption;
}
/* Graphiques rapport d'appels */
.graph-calls-semi-circle {
	width: 100%;
	float: none;
}
#site-content dl.search-result {
	height: auto;
}
#site-menu #site-menu-search {
	width: auto;
	height: auto;
}

#site-content table.fidelity td[headers="customers"],
#site-content table.fidelity td[headers="val_last_year"],
#site-content table.fidelity td[headers="val_current_year"] {

	width: auto;
	vertical-align: middle;
	border-bottom: 0 none;
}
[headers="val_last_year"]:before {
	content: 'Année précédente : ';
}
[headers="val_current_year"]:before {
	content: 'Période courante : ';
}
#mediatheque-container {
	border: solid 1px #A9A9A9;
	margin-top: 10px;
}
#nav {
	font-size: 11px;
	width: 140px;
	float: left;
	padding: 10px;
	height: 380px;
}
#nav li {
	list-style: none inside none;
	margin: 0;
}
#nav ul:first-child {
	margin: 0px;
	padding: 0px;
}
#nav ul {
	padding-left: 20px;
	margin: 0;
}
#nav .dir {
	background-image: url(/admin/dist/images/dir-close.svg);
	background-repeat: no-repeat;
	background-size: 16px;
	background-position: left -2px;
	display: block;
	margin-bottom: 5px;
	padding-left: 20px;
}
#nav .dir.selected {
	font-weight: 600;
}
#src{
	width: 220px;
	padding: 3px 0;
}
#mediatheque {
	height: 400px;
	overflow-y: auto;
}
#mediatheque img {
	cursor: pointer;
	-moz-transition: all 0.5s;
	-webkit-transition: all 0.5s;
	transition: all 0.5s;
	border: solid 1px transparent;
	float: left;
	position: relative;
	z-index: 1;
	margin: 1px;
}
#mediatheque img.selected {
	cursor: default;
	border-color: #4080c0;
	-moz-box-shadow: 0px 0px 10px #4080c0;
	-webkit-box-shadow: 0px 0px 10px #4080c0;
	box-shadow: 0px 0px 10px #4080c0;
	z-index: 2;
}
.padt {
	padding-top: 10px;
}
.dropzone{
	cursor: pointer;
	text-align: center;
	padding: 50px 0;
	border: 3px dotted #999;
	margin: 3px;
}

.loadzone .img-images {
	float: left;
	height: 150px;
	margin: 3px;
	width: 150px;
	border: 1px solid #DEDEDE;
	padding: 2px;
	text-align:center;
}
.loadzone .img-images .loader{
	margin: 70px 0;
}
#hiddenupload{
	overflow:hidden; 
	width: 1px; 
	height: 1px;	
	position:absolute;
	top: 0px; 
	left: 0px;
	text-indent: -5000px;
}
#hiddenupload .hidden{
	display: none;
}
[id="site-accessibility-menu"], .print-none {
  display: none;
}

/*! normalize.css v4.1.1 | MIT License | github.com/necolas/normalize.css */
/**
 * 1. Change the default font family in all browsers (opinionated).
 * 2. Prevent adjustments of font size after orientation changes in IE and iOS.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/**
 * Remove the margin in all browsers (opinionated).
 */
body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 * 2. Add the correct display in IE.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
  /* 1 */
  display: block;
}

/**
 * Add the correct display in IE 9-.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
}

/**
 * Add the correct display in iOS 4-7.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Add the correct display in IE 10-.
 * 1. Add the correct display in IE.
 */
template,
[hidden] {
  display: none;
}

/* Links
   ========================================================================== */
/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
  background-color: transparent;
  /* 1 */
  -webkit-text-decoration-skip: objects;
  /* 2 */
}

/**
 * Remove the outline on focused links when they are also active or hovered
 * in all browsers (opinionated).
 */
a:active,
a:hover {
  outline-width: 0;
}

/* Text-level semantics
   ========================================================================== */
/**
 * 1. Remove the bottom border in Firefox 39-.
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  /* 2 */
}

/**
 * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
 */
b,
strong {
  font-weight: inherit;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * Add the correct font style in Android 4.3-.
 */
dfn {
  font-style: italic;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/**
 * Add the correct background and color in IE 9-.
 */
mark {
  background-color: #ff0;
  color: #000;
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10-.
 */
img {
  border-style: none;
}

/**
 * Hide the overflow in IE.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
 * Add the correct margin in IE 8.
 */
figure {
  margin: 1em 40px;
}

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/* Forms
   ========================================================================== */
/**
 * 1. Change font properties to `inherit` in all browsers (opinionated).
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
select,
textarea {
  font: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
 * Restore the font weight unset by the previous rule.
 */
optgroup {
  font-weight: bold;
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Change the border, margin, and padding in all browsers (opinionated).
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
 * Remove the default vertical scrollbar in IE.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on OS X.
 */
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * Correct the text style of placeholders in Chrome, Edge, and Safari.
 */
::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* ----------------------------- */
/* ==Forms                       */
/* ----------------------------- */
/* thanks to HTML5boilerplate,
* github.com/nathansmith/formalize and www.sitepen.com
*/
/* forms items */
form,
fieldset {
  border: none;
}

input,
button,
select,
label {
  font-family: inherit;
  font-size: inherit;
}

button,
input,
optgroup,
select,
textarea {
  color: #000;
}

label {
  vertical-align: middle;
  cursor: pointer;
}

legend {
  border: 0;
  white-space: normal;
}

textarea {
  min-height: 5em;
  vertical-align: top;
  font-family: inherit;
  font-size: inherit;
  resize: vertical;
}

select {
  -webkit-appearance: menulist-button;
}

/* if select styling bugs on WebKit */
/* select { -webkit-appearance: none; } */
/* 'x' appears on right of search input when text is entered. This removes it */
input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-results-button, input[type="search"]::-webkit-search-results-decoration {
  display: none;
}

::-webkit-input-placeholder {
  color: #777;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #777;
}

input[type="button"]:focus,
button:focus {
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* unstyled forms */
button.unstyled,
input[type="button"].unstyled,
input[type="submit"].unstyled,
input[type="reset"].unstyled {
  padding: 0;
  border: none;
  line-height: 1;
  text-align: left;
  background: none;
  border-radius: 0;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

button.unstyled:focus,
input[type="button"].unstyled:focus,
input[type="submit"].unstyled:focus,
input[type="reset"].unstyled:focus {
  box-shadow: none;
  outline: none;
}

/* ----------------------------- */
/* ==Base (basic styles)         */
/* ----------------------------- */
/* switching to border-box model for all elements */
html {
  box-sizing: border-box;
}

* {
  box-sizing: inherit;
}

html {
  /* set base font-size to equiv "10px", which is adapted to rem unit */
  font-size: 62.5%;
  /* IE9-IE11 math fixing. See http://bit.ly/1g4X0bX */
  /* thanks to @guardian, @victorbritopro and @eQRoeil */
  font-size: calc(1em * 0.625);
}

body {
  font-size: 1.6rem;
  background-color: #f1f6ff;
  font-family: "Montserrat", Helvetica, Arial, sans-serif !important;
  font-weight: 500;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: #f1f6ff;
}

ul,
ol {
  padding-left: 2em;
}

img {
  vertical-align: middle;
}

blockquote,
figure {
  margin-left: 0;
  margin-right: 0;
}

/* font-sizing for content */
p,
ul,
label,
textarea,
caption {
  margin-top: 0.75em;
  margin-bottom: 0;
  line-height: 1.5;
}

em {
  font-style: italic;
}

/* avoid margins on nested elements */
li p,
li ul,
li ol {
  margin-top: 0;
  margin-bottom: 0;
}

/* max values */
img
textarea,
input {
  max-width: 100%;
}

img {
  height: auto;
}

a {
  transition: all 0.35s ease;
}

.hidden {
  position: absolute;
  top: -20em;
  left: -200em;
}

.txtcenter {
  text-align: center;
}

.uppercase {
  text-transform: uppercase;
}

.man {
  margin: 0;
}

.typo1 {
  font-family: "Montserrat", Helvetica, Arial, sans-serif !important;
  font-weight: 500;
}

.typo2 {
  font-family: "Montserrat", Helvetica, Arial, sans-serif !important;
  font-weight: 600;
}

.color1 {
  color: #000;
}

.color2 {
  color: #fff;
}

.pale-grey {
  color: #f1f6ff;
}

.warm-blue {
  color: #3d50df;
}

.mouse {
  color: #7a869a;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.large-hidden {
  display: none;
}

body:focus {
  outline: 0;
}

body .dqpl-checkbox:focus, body .dqpl-radio:focus {
  border: none;
}

body .dqpl-skip-container.dqpl-child-focused {
  outline: 0;
}

body .dqpl-tile .dqpl-tile-header .dqpl-select .dqpl-combobox:focus {
  outline: 0;
}

/* Commun  */
@-webkit-keyframes rot {
  from {
    -webkit-transform: rotate(0deg) translate(-10px) rotate(0deg);
            transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg) translate(-10px) rotate(-360deg);
            transform: rotate(360deg) translate(-10px) rotate(-360deg);
  }
}
@keyframes rot {
  from {
    -webkit-transform: rotate(0deg) translate(-10px) rotate(0deg);
            transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg) translate(-10px) rotate(-360deg);
            transform: rotate(360deg) translate(-10px) rotate(-360deg);
  }
}

@-webkit-keyframes rot2 {
  from {
    -webkit-transform: rotate(0deg) translate(10px) rotate(0deg);
            transform: rotate(0deg) translate(10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg) translate(10px) rotate(-360deg);
            transform: rotate(360deg) translate(10px) rotate(-360deg);
  }
}

@keyframes rot2 {
  from {
    -webkit-transform: rotate(0deg) translate(10px) rotate(0deg);
            transform: rotate(0deg) translate(10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg) translate(10px) rotate(-360deg);
            transform: rotate(360deg) translate(10px) rotate(-360deg);
  }
}

@-webkit-keyframes rot3 {
  from {
    -webkit-transform: rotate(0deg) translate(-10px) rotate(0deg);
            transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-360deg) translate(-10px) rotate(360deg);
            transform: rotate(-360deg) translate(-10px) rotate(360deg);
  }
}

@keyframes rot3 {
  from {
    -webkit-transform: rotate(0deg) translate(-10px) rotate(0deg);
            transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-360deg) translate(-10px) rotate(360deg);
            transform: rotate(-360deg) translate(-10px) rotate(360deg);
  }
}

@-webkit-keyframes rot4 {
  from {
    -webkit-transform: rotate(0deg) translate(10px) rotate(0deg);
            transform: rotate(0deg) translate(10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-360deg) translate(10px) rotate(360deg);
            transform: rotate(-360deg) translate(10px) rotate(360deg);
  }
}

@keyframes rot4 {
  from {
    -webkit-transform: rotate(0deg) translate(10px) rotate(0deg);
            transform: rotate(0deg) translate(10px) rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-360deg) translate(10px) rotate(360deg);
            transform: rotate(-360deg) translate(10px) rotate(360deg);
  }
}

.pictos {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
}

.pictos img {
  position: absolute;
  -webkit-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
}

.commande-icon {
  top: 5.333%;
  left: 35.71%;
  -webkit-animation: rot 6s infinite linear;
          animation: rot 6s infinite linear;
}

.clients-icon {
  right: 32.14%;
  top: 8.444%;
  -webkit-animation: rot2 6s infinite linear;
          animation: rot2 6s infinite linear;
}

.sage-icon {
  top: 19.333%;
  right: 6%;
  -webkit-animation: rot3 6s infinite linear;
          animation: rot3 6s infinite linear;
}

.salesforce-icon {
  left: 8.857%;
  top: 28.777%;
  -webkit-animation: rot4 6s infinite linear;
          animation: rot4 6s infinite linear;
}

.mediatheque-icon {
  top: 33.333%;
  right: 23.785%;
  -webkit-animation: rot 6s infinite linear;
          animation: rot 6s infinite linear;
}

.couette-icon {
  left: 24.285%;
  top: 38.666%;
  -webkit-animation: rot2 6s infinite linear;
          animation: rot2 6s infinite linear;
}

.statistiques-icon {
  bottom: 25.333%;
  left: 17.642%;
  -webkit-animation: rot3 6s infinite linear;
          animation: rot3 6s infinite linear;
}

.outils-icon {
  right: 19.285%;
  bottom: 25.777%;
  -webkit-animation: rot4 6s infinite linear;
          animation: rot4 6s infinite linear;
}

.main-content {
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

.main-content a {
  outline: none;
}

.vertical-center {
  margin: auto;
  width: 100%;
}

/* End Commun */
/* Box centrale commune */
.central-box {
  width: 100%;
  max-width: 490px;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 15px 0 rgba(57, 98, 139, 0.08);
  border: solid 1px #e2e2ea;
  background: #fff;
  padding: 6rem 4rem 2rem;
  margin: auto;
  align-items: stretch;
  flex-direction: column;
  position: relative;
}

.central-box .shadow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f1f6ff;
  opacity: 0.4;
  display: none;
  z-index: 99999;
}

.central-box .shadow.show {
  display: flex;
  align-items: center;
}

.central-box .shadow img {
  margin: 0 auto;
  height: 45px;
  width: 45px;
}

.logos {
  justify-content: center;
}

.logos img + img {
  margin-left: 1rem;
  border-radius: 4px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
}

.main-title {
  font-size: 2.2rem;
  color: #002251;
  font-weight: 600;
  margin-top: 4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
  padding: 0;
}

.main-title a {
  color: #002251;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.main-title a:hover circle, .main-title a:focus circle {
  fill: #5377fb;
}

.main-title a:hover line, .main-title a:focus line {
  stroke: #f1f6ff;
}

.main-title svg {
  margin-right: 0.9rem;
}

.main-title circle, .main-title line {
  transition: all 0.35s ease;
}

.auth-text {
  color: #7a869a;
  font-size: 1.4rem;
  line-height: 1.36;
  margin-top: 1.4rem;
}

.auth-text.error {
  color: #ff6366;
}

form {
  margin-top: 2.6rem;
}

form button[type="submit"] {
  color: #fff;
  font-size: 1.4rem;
  line-height: 1.29;
  width: 190px;
  min-height: 44px;
  padding: 1.3rem 2rem;
  border: none;
  background: #5377fb;
  cursor: pointer;
  margin-left: 1rem;
  border-radius: 4px;
  -webkit-appearance: none;
  outline: none;
  transition: all 0.35s ease;
  white-space: initial;
  justify-content: center;
}

form button[type="submit"]:hover, form button[type="submit"]:focus {
  background: #3d50df;
  outline: none;
}

form input {
  height: 60px;
  width: 100%;
  border-radius: 4px;
  border: solid 1px #cfd4da;
  font-size: 1.4rem;
  line-height: 1.36;
  color: #7a869a;
  padding: 0 2rem;
  transition: all 0.35s ease;
  outline: none;
}

form input:hover, form input:focus {
  outline: none;
}

form input.password {
  padding-right: 5.5rem;
}

form input.email, form input.password {
  position: relative;
  z-index: 1;
  background: transparent;
}

form input.email:focus, form input.password:focus {
  box-shadow: inset 0 3px 2px 0 rgba(0, 0, 0, 0.1);
  border-color: #5377fb;
}

form input.email:hover, form input.password:hover {
  border-color: #5377fb;
}

form input.email:disabled, form input.password:disabled {
  background: #f1f1f5;
}

form input.email.error, form input.password.error {
  border-color: #ff6366;
  outline: none;
  box-shadow: inset 0 3px 2px 0 rgba(0, 0, 0, 0.1);
}

form input.email:focus, form input.email.error, form input.email.not-empty, form input.password:focus, form input.password.error, form input.password.not-empty {
  padding-top: 1.2rem;
}

form input.email:focus + .pseudo-label, form input.email.error + .pseudo-label, form input.email.not-empty + .pseudo-label, form input.password:focus + .pseudo-label, form input.password.error + .pseudo-label, form input.password.not-empty + .pseudo-label {
  font-size: 1.1rem;
  height: 14px;
  top: 11px;
}

form input.email:focus, form input.email.not-empty, form input.password:focus, form input.password.not-empty {
  color: #002251;
}

form input.email:focus + .pseudo-label, form input.email.not-empty + .pseudo-label, form input.password:focus + .pseudo-label, form input.password.not-empty + .pseudo-label {
  color: #5377fb;
}

form input.email.not-empty, form input.password.not-empty {
  background: #f1f6ff;
}

form input.email.not-empty.error, form input.password.not-empty.error {
  background: none;
}

form input.email.not-empty + .pseudo-label, form input.password.not-empty + .pseudo-label {
  z-index: 2;
}

form input.email.error + .pseudo-label, form input.password.error + .pseudo-label {
  color: #ff6366;
}

form input.email.error + .pseudo-label + .message, form input.password.error + .pseudo-label + .message {
  display: block;
}

.message {
  display: none;
  margin-top: 0.8rem;
  font-size: 1.1rem;
  color: #ff6366;
  padding: 0 2rem;
  text-align: left;
}

.avatar {
  align-items: center;
  justify-content: center;
  margin-top: 1.6rem;
  text-decoration: none;
}

.avatar + form {
  margin-top: 2.4rem;
}

.img-avatar {
  margin-right: 1.1rem;
}

.img-avatar img {
  border-radius: 50%;
  display: block;
  border: 3px solid #f1f1f5;
}

.mail-avatar {
  padding-bottom: 0.7rem;
  border-bottom: 1px solid #cfd4da;
  font-size: 1.4rem;
  line-height: 1.36;
  margin: 0;
}

.password-container {
  position: relative;
}

.eye {
  position: absolute;
  right: 18px;
  top: 18px;
  background: url("/admin/images/login/icons/invisible.svg") no-repeat center;
  background-size: contain;
  z-index: 1;
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.35s ease;
}

.eye.open {
  background: url("/admin/images/login/icons/visible.svg") no-repeat center;
}

.input-container {
  position: relative;
}

.input-container + .input-container {
  margin-top: 10px;
}

.pseudo-label {
  font-size: 1.4rem;
  position: absolute;
  left: 0;
  top: 0;
  text-align: left;
  padding: 0 2rem;
  height: 60px;
  width: 100%;
  display: flex;
  align-items: center;
  transition: all 0.35s ease;
}

.form-footer {
  margin-top: 2rem;
  align-items: stretch;
}

.form-footer-link {
  flex: 1;
  font-size: 1.4rem;
  line-height: 1.36;
  color: #5377fb;
  text-decoration: none;
  min-height: 44px;
  border-radius: 4px;
  transition: all 0.35s ease;
  background: #fff;
  align-items: center;
  justify-content: center;
  order: -1;
}

.form-footer-link:hover, .form-footer-link:focus {
  background: #f1f6ff;
}

.footer-links {
  margin-top: 5rem;
  justify-content: space-between;
}

.footer-links a, .footer-links > div {
  color: #002251;
  font-size: 1.3rem;
  line-height: 1.46;
  text-decoration: none;
  position: relative;
  margin-top: 1rem;
  text-align: left;
}

.footer-links a::after, .footer-links > div::after {
  content: '';
  left: 50%;
  width: 0;
  height: 1px;
  background: #5377fb;
  bottom: 0;
  transition: all 0.35s ease;
  position: absolute;
}

.footer-links a:hover, .footer-links a:focus, .footer-links > div:hover, .footer-links > div:focus {
  color: #5377fb;
}

.footer-links a:hover::after, .footer-links a:focus::after, .footer-links > div:hover::after, .footer-links > div:focus::after {
  left: 0;
  width: 100%;
}

.footer-links > div {
  cursor: pointer;
}

.footer-links > div:hover::after, .footer-links > div:focus::after {
  display: none;
}

.footer-links .cb_list {
  position: absolute;
  z-index: 1;
  min-width: 110px;
  width: auto;
  border-radius: 4px;
  box-shadow: 0 2px 15px 0 rgba(57, 98, 139, 0.08);
  border: solid 1px #e2e2ea;
  background-color: #fff;
  left: 50%;
  top: -62px;
  display: block;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  max-height: 190px;
  overflow: auto;
  padding: 1rem;
  list-style-type: none;
  display: none;
}

.footer-links .cb_list.open {
  display: block;
}

.footer-links .cb_list .cb_option {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  line-height: 1.46;
  color: #002251;
  border-radius: 4px;
  transition: all 0.35s ease;
  padding: 0;
}

.footer-links .cb_list .cb_option:last-child {
  margin-bottom: 1rem;
}

.footer-links .cb_list .cb_option.selected, .footer-links .cb_list .cb_option:hover, .footer-links .cb_list .cb_option:focus {
  background: #f1f6ff;
  color: #002251;
  outline: none;
}

.footer-links .cb_list .cb_option.selected::after, .footer-links .cb_list .cb_option:hover::after, .footer-links .cb_list .cb_option:focus::after {
  display: none;
}

.footer-links .cb_button {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  -webkit-appearance: none;
}

.footer-links .cb_button svg {
  margin-right: 0.5rem;
}

.footer-links .cb_button div {
  color: #002251;
  outline: none;
  transition: all 0.35s ease;
  position: relative;
}

.footer-links .cb_button div::after {
  content: '';
  left: 50%;
  width: 0;
  height: 1px;
  background: #5377fb;
  bottom: 0;
  transition: all 0.35s ease;
  position: absolute;
}

.footer-links .cb_button div:hover, .footer-links .cb_button div:focus {
  outline: none;
}

.footer-links .cb_button:hover, .footer-links .cb_button:focus {
  cursor: pointer;
}

.footer-links .cb_button:hover path, .footer-links .cb_button:focus path {
  fill: #5377fb;
}

.footer-links .cb_button:hover div, .footer-links .cb_button:focus div {
  color: #5377fb;
}

.footer-links .cb_button:hover div::after, .footer-links .cb_button:focus div::after {
  left: 0;
  width: 100%;
}

.footer-links .cb_button path {
  fill: #002251;
  transition: all 0.35s ease;
}

/* End Box centrale commune */
/* Footer */
.general-footer {
  padding-top: 2.6rem;
  font-size: 1.2rem;
}

.general-footer .baseline {
  margin-top: 0.8rem;
}

.general-footer p {
  line-height: 1.58;
}

.rocket {
  display: block;
  margin: 3rem auto 0;
  width: 30px;
}

.credits {
  margin-top: 0;
  justify-content: center;
  font-size: 1.2rem;
  line-height: 1.58;
  padding-bottom: 2rem;
}

.credits a {
  transition: all 0.35s ease;
  text-decoration: none;
  position: relative;
}

.credits a::after {
  content: '';
  left: 50%;
  width: 0;
  height: 1px;
  background: #5377fb;
  bottom: 0;
  transition: all 0.35s ease;
  position: absolute;
}

.credits a:hover, .credits a:focus {
  color: #5377fb;
}

.credits a:hover::after, .credits a:focus::after {
  left: 0;
  width: 100%;
}

/* End Footer */
@media screen and (max-width: 767px) {
  .main-content {
    padding-top: 3.5rem;
  }
}

@media screen and (max-width: 520px) {
  .main-title {
    font-size: 1.9rem;
  }
  .vertical-center {
    width: 100%;
  }
  .central-box {
    width: calc(100% - 4rem);
    padding: 7rem 2rem 2.5rem;
    height: auto;
    display: block;
  }
  .form-footer-link {
    order: 1;
    width: 100%;
    flex: inherit;
    margin-top: 1rem;
  }
  form button[type="submit"] {
    margin-left: 0;
    width: 100%;
  }
  .form-footer {
    width: 100%;
    display: block;
  }
  .footer-links {
    margin-top: 1.5rem;
  }
  .footer-links > div {
    width: 100%;
    margin-top: 0;
    display: flex;
    justify-content: center;
  }
  .img-avatar {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-right: 0;
  }
  .mail-avatar {
    margin-top: 0.7rem;
  }
  .credits {
    margin-top: 2.5rem;
  }
  .credits span {
    width: 100%;
    text-align: center;
  }
}

@media screen and (max-width: 320px) {
  body {
    min-width: 320px;
    overflow: auto;
  }
}

.sp-hidden {
  height: 0;
  width: 0;
  border: none;
}

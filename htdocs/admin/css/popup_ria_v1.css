@charset "UTF-8";
/* couleurs utilisées principalement pour  les bordures des tableaux */
:root {
  --mdc-theme-primary: $dark-color;
}

/**
** GLOBAL
**/
#popup_ria {
  background-color: white;
  display: none;
  height: 60%;
  left: 0;
  position: absolute;
  top: 0;
  width: 916px;
  min-width: 635px;
  z-index: 10000;
  border-radius: 10px;
}

#popup_ria.maxipopup {
  /* 	width: 881px;
 */
}

#popup_ria .content a {
  text-decoration: none;
  color: black;
}

#popup_ria .content a:hover {
  text-decoration: underline;
}

#popup_ria .content {
  background-color: white;
  height: 100%;
  padding-top: 24px;
  overflow: auto;
  position: absolute;
  top: 0;
  left: 0px;
  width: 100%;
  z-index: 1;
  border-radius: 10px;
  overflow: hidden;
}

#popup_ria .popup_ria_drag {
  width: 100%;
  height: 24px;
  background-color: #232E63;
  cursor: move;
  color: white;
  padding: 5px 0;
  position: absolute;
  z-index: 2;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

#popup_ria .popup_ria_drag .text {
  font-weight: 600;
  display: inline-block;
}

#popup_ria .popup_ria_drag .drag {
  display: inline-block;
  background-image: url("/admin/dist/images/move.svg");
  height: 7px;
  margin: 0 10px;
  width: 7px;
  vertical-align: middle;
}

#popup_ria .popup_ria_drag a {
  position: absolute;
  right: 0;
  top: 0;
}

#popup_ria .popup_ria_drag .close {
  background-image: url("/admin/dist/images/pop-up-close.svg");
  height: 24px;
  width: 24px;
}

#popup_ria .popup_ria_iframe {
  border: medium none;
  height: 100%;
  width: 100%;
}

#popup_ria_shadow {
  display: none;
  background-color: #222;
  height: 100%;
  left: 0;
  opacity: 0.6;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9998;
}

#popup-content .popup-info-desc ul {
  list-style: disc inside none;
  margin-left: 8px;
}

/* Responsive */
@media (max-width: 1023px) {
  .maxipopup {
    max-width: 100% !important;
    min-width: 0 !important;
  }
}

@media (max-width: 767px) {
  .maxipopup {
    position: fixed !important;
    top: 0 !important;
    bottom: auto !important;
    left: 0 !important;
    width: 100vw !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }
  #popup-content table {
    width: 100%;
  }
  #popup-content table caption {
    white-space: normal !important;
  }
  #popup_ria .content {
    width: 100vw;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 425px) {
  #popup-content table[summary] > tbody > tr > th,
  #popup-content table[summary] > tbody > tr > td,
  #popup-content table[summary] > tbody > tr,
  #popup-content table[summary] > tfoot > tr > th,
  #popup-content table[summary] > tfoot > tr > td,
  #popup-content table[summary] > tfoot > tr {
    width: 100%;
    box-sizing: border-box;
  }
}

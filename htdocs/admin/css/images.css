.autoassociated-image .filter {
	margin-top: 30px;
	margin-bottom: 60px;
}
.autoassociated-image .actions {
	margin-top: 50px;
}
.autoassociated-image .actions input {
	float: left;
}
.autoassociated-image .actions .pagination {
	text-align: right;
}
.autoassociated-image .auto-link {
	min-height: 305px;
	margin-top: 45px;
}
.autoassociated-image .auto-link.not-min-height {
	min-height: 0;
}
.autoassociated-image .auto-link.hidden {
	display: none;
}
.autoassociated-image .auto-link .name {
	display: block;
	font-size: 13px;
	font-weight: 600;
	margin-top: 2px;
	text-align: center;
	color: #000000;
	text-decoration: none;
}
.autoassociated-image .auto-link .media {
	float: left;
}
.autoassociated-image .auto-link .links {
	margin-left: 275px;
}
.autoassociated-image .auto-link .links h3 a {
    font-size: 12px;
    font-weight: 500;
}
.autoassociated-image .auto-link .cnt-place {
    margin-left: 20px;
    margin-top: 5px;
}
.autoassociated-image .auto-link #actions .linked {
    float: left;
}
.autoassociated-image .auto-link input[type=checkbox] {
    float: left;
    margin-right: 5px;
}
.autoassociated-image .wait {
	padding: 15px;
}
#site-content .autoassociated-image .wait img {
	border: medium none;
	float: left;
	margin-right: 10px;
}
.autoassociated-image .wait div {
	display: inline-block;
	padding-top: 15px;
	color: #8F8F8F;
}
#auto-links .media img {
	border: 1px solid #DDDDDD;
}
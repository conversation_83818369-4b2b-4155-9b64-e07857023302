.popup_img_zones .popup-content {
    padding: 0px;
    margin: 0px;
}

.popup_img_zones .clear {
	clear: both;
}

.popup_img_zones #popup_ria.form-container {
    width: calc(750px * 0.8);
    left: calc(750px * 0.1);
    top: calc(538px * 0.225);
    z-index: initial;
}

.popup_img_zones #popup_ria.form-container .popup_ria_drag {
    position: relative;
}

.popup_img_zones .row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100%;
    min-width: 100%;
}

.popup_img_zones .row.submit {
    text-align: right;
    position: absolute;
    bottom: 0px;
}

.popup_img_zones #popup_ria.form-container .zone-details .row.submit {
    margin-top: 30px;
}

.popup_img_zones .col.right {
    width: 100%;
    min-width: 100%;
    float: right;
}

.popup_img_zones .success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
  padding: 10px;
}

.popup_img_zones .resize-drag {
	color: #303030;
	font-size: 20px;
	font-family: sans-serif;
	border-radius: 8px;
	min-width: 40px;
	min-height: 40px;
	box-sizing: border-box;
	position: absolute;
	border: 5px dotted #303030;
    background-color: rgba(241, 241, 241, 0.5);
    overflow: hidden;
    white-space: nowrap;
    padding: 3px;
}

.popup_img_zones .resize-drag.selected {
	border-style: solid;
}

.popup_img_zones .resize-drag .title {
    font-weight: 600;
    font-size: 14px;
    padding: 3px;
}

.popup_img_zones .resize-drag .actions {
    text-align: center;
    position: absolute;
    top: 50%;
    width: 100%;
}

.popup_img_zones .resize-drag .actions a {
    text-decoration: none;
	color: #303030;
    font-size: 13px;
}

.popup_img_zones .resize-drag .actions a:hover {
    text-decoration: underline;
}

.popup_img_zones .resize-container {
	float: left;
	position: relative;
}


.popup_img_zones .zone-details {
    color: #000000;
	float: left;
    border-bottom: none;
	padding: 0px;
    width: 100%;
}

.popup_img_zones #popup_ria.form-container > .submit {
    border: 1px solid #a3a3a3;
}

.popup_img_zones .zone-details label {
	display: block;
    margin: 5px 10px;
}

.popup_img_zones .zone-details label input[type=text] {
    width: calc(100% - 214px);
}

.popup_img_zones .zone-details label #search {
    width: calc(100% - 292px);
}
.popup_img_zones .zone-details label #files {
    margin-right: 0px;
}

.popup_img_zones .zone-details label .href-target {
    width: calc(100% - 206px);
}

.popup_img_zones .zone-details label textarea {
    width: calc(100% - 208px);
}

.popup_img_zones .zone-details label span {
	display: inline-block;
	width: 200px;
}

.popup_img_zones div.close-zone {
    background-image: url(/admin/images/stats/close.gif);
    height: 13px;
    width: 13px;
    margin: 5px 10px;
    float: right;
}

.popup_img_zones iframe {
    overflow:hidden;
}
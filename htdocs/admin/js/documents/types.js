function validForm(frm){
	if( !trim(frm.name.value) ){
		alert(documentsTypesAlertDesignationObg);
		frm.name.focus();
		return false;
	}
}
function cancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function confirmDel(){
	return window.confirm(documentsTypesConfirmSuppression);
}
function confirmDelList(){
	return window.confirm(documentsTypesConfirmSuppressionMultiple);
}

function previewClick( preview ){
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#4574BF' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor!='';
	
	$('.edit-zones')
		.toggle($('.preview input:checked').length == 1)
		.off('click')
		.click(function () {
			var idStart = window.location.search.indexOf('&cat=') + 5;
			var idStop = window.location.search.indexOf('&', idStart);
			var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
			var $preview = $('.preview input:checked').parents('.preview:eq(0)');
			displayPopup(cmsDiplayPopupZonesCliquables, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=12&obj_id_0=' + id, null, 756, 602);
		});
	
	//Affichage du bouton suppirmer
	if( $('.preview input:checked').length >= 1 ){
		$('.delimg').show();
	}else{
		$('.delimg').hide();
	}
}
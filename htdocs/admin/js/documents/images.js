{ // Association automatique
	var browserMSIE = navigator.userAgent.match( /msie/i );
	var ajaxRequestAutoLink = false;

	var currentPage 	= $('.autoassociated-image #auto-links').data('current-page');
	var limitByPage 	= 20;
	var countAutoLink 	= $('.auto-link').length;

	var firstPushHisto	= true;

	$(document).ready(function(){
		if( ajaxRequestAutoLink ){
			ajaxRequestAutoLink.abort();
		}

		autoLink_loadImageForm( (currentPage-1) * limitByPage, limitByPage );

		if( !browserMSIE ){
			window.onpopstate = function(e) {
				if( e.state !== null ){
					if( ajaxRequestAutoLink ){
						ajaxRequestAutoLink.abort();
					}

					var numPage = e.state.page;

					var start = ( numPage - 1 ) * limitByPage;
					var end   = numPage * limitByPage;

					$('.auto-link:visible').slideUp();
					for( var i = start ; i < end ; i++ ){
						$('.auto-link').eq(i).slideDown();
					}

					// Chargement du contenu de linkage
					autoLink_loadImageForm( start, limitByPage );

					currentPage = numPage;
					autoLink_RefreshPagination( numPage );
				}
			};
		}
	}).on( 'click', '.autoassociated-image .pagination a', function(){
		if( firstPushHisto ){
			autoLink_pushHisto( currentPage );
			firstPushHisto = false;
		}

		var numPage = $(this).data('page');

		var start = ( numPage - 1 ) * limitByPage;
		var end   = numPage * limitByPage;

		$('.auto-link:visible').slideUp();
		for( var i = start ; i < end ; i++ ){
			$('.auto-link').eq(i).slideDown();
		}

		// Chargement du contenu de linkage
		autoLink_loadImageForm( start, limitByPage );

		currentPage = numPage;
		autoLink_RefreshPagination( numPage );

		autoLink_pushHisto( currentPage );
		return false;
	}).on( 'click', '.autoassociated-image .actions-links .save', function(){
		var index = $(this).parents('.auto-link').data('index');
		autoLink_saveLinks( index );
	}).on( 'click', '.autoassociated-image .cancel-save', function(){ // Bouton Annuler une association
		var index = $(this).parents('.auto-link').data('index');
		return autoLink_cancelSave( index );
	}).on( 'click', '.autoassociated-image .actions-links .masked', function(){
		var index = $(this).parents('.auto-link').data('index');
		autoLink_maskedImage( index );
	}).on( 'click', '.autoassociated-image .actions-links .delete', function(){
		var index = $(this).parents('.auto-link').data('index');
		autoLink_deleteImage( index );
	}).on( 'click', '.autoassociated-image .cancel-masked', function(){
		var index = $(this).parents('.auto-link').data('index');
		return autoLink_cancelMasked( index );
	}).on( 'click', '.autoassociated-image .actions-links .search', function(){
		var img_id = $(this).parents('.auto-link').data('img-id');
		displayPopup( 'Associer un produit', '', '/admin/documents/images/popup.php?class=1&image=' + img_id + '&autolink=1' );
	}).on( 'click', '.autoassociated-image [name=save-all]', function(){
		$('.error').remove();
		var paramToSave = '';

		$('.autoassociated-image .auto-link:visible:has(input[type=checkbox]:checked)').each(function(){
			paramToSave += ( $.trim(paramToSave) != '' ? '&' : '' ) + $.param( $(this).find('input:checked, select:has(:selected)').not('[type=button]') );
		});

		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'save-link=1&' + paramToSave,
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );
				if( json.res == '1' ){
					$('.autoassociated-image .auto-link:visible:has(input[type=checkbox]:checked)').remove();
					countAutoLink = $('.auto-link').length;

					for( var i = (currentPage - 1) * limitByPage ; i < ((currentPage - 1) * limitByPage) + limitByPage ; i++ ){
						$('.auto-link').eq(i).slideDown();
					}

					autoLink_loadImageForm( (currentPage - 1) * limitByPage, limitByPage );
					autoLink_RefreshPagination( currentPage );
				}else{
					$('.autoassociated-image .actions').before( '<div class="error">' + json.message + '</div>' );
				}
			}
		});

		return false;
	});

	var autoLink_RefreshPagination = function( page ){
		var pages 		= Math.ceil( countAutoLink / limitByPage );

		var pageStart 	= (page -3) > 0 ? (page -3) : 1;
		var pageEnd 	= (page + 3) <= pages ? (page + 3) : pages;

		var htmlPagination = '';

		htmlPagination += ( page > 1 ? '<a href="#" data-page="' + (page -1) + '">&laquo; ' + imagesPagePrec + '</a> | ' : '&nbsp;' );
		for( var i = pageStart ; i <= pageEnd ; i++ ){
			if( i == page ){
				htmlPagination += '<b>' + i + '</b>' + ( i == pageEnd ? '' : ' | ' );
			}else{
				htmlPagination += '<a href="#" data-page="' + i + '">' + i + '</a>' +( i == pageEnd ? '' : ' | ' );
			}
		}

		htmlPagination += ( page < pages ? ' | <a href="#" data-page="' + (page + 1) + '">' + imagesPageSuiv + ' &raquo;</a>' : '&nbsp;' );

		$('.autoassociated-image .pagination').html( htmlPagination );
	}

	var autoLink_loadImageForm = function( index, limit ){
		if( ajaxRequestAutoLink ){
			ajaxRequestAutoLink.abort();
		}

		var element = $('.auto-link').eq( index );
		if( typeof element == 'undefined' || !element.length ){
			return false;
		}

		var imageID = element.data('img-id');

		if( !element.find('.media').length ){
			var srcName = element.find('.src-name').html();

			ajaxRequestAutoLink = $.ajax({
				url : '/admin/documents/images/import/ajax-img-import.php',
				data: 'load-form=' + imageID + '&src-name=' + srcName,
				type: 'post',
				success: function( htmlLoad ){
					element.find('.wait').remove();
					element.find('h2').after( htmlLoad );

					if( limit > 1 ){
						autoLink_loadImageForm( index + 1, limit -1 );
					}

					element.addClass('not-min-height');
				}
			});
		}else{
			autoLink_loadImageForm( index + 1, limit -1 );
		}
	}

	var autoLink_maskedImage = function( index ){
		$('.error').remove();

		var element 	= $('.auto-link[data-index="' + index + '"]');
		if( typeof element == 'undefined' || !element.length ){
			return false;
		}

		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'masked-img=' + element.data('img-id'),
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );
				if( json.res == '1' ){
					element.find('.card-auto-link').slideUp().after(
						'<div class="success">' + imagesMsgImageNonVisible
						+ ' <a class="cancel-masked" href="#">' + productsAnnuler + '</a></div>'
					);

					// Affiche une image supplémentaire
					var newIndex = autoLink_getLastVisible() + 1;
					$('.auto-link').eq(newIndex).slideDown()
					autoLink_loadImageForm( newIndex, 1 );
					autoLink_updateCountImages('del', 1);
				}else{
					element.find('.actions-links').append( '<div class="error">' + json.message + '</div>' );
				}
			}
		});
	}

	var autoLink_deleteImage = function( index ){
		$('.error').remove();

		var element 	= $('.auto-link[data-index="' + index + '"]');
		if( typeof element == 'undefined' || !element.length ){
			return false;
		}
		if (!confirm(imagesConfirmSuppressionImage)){
			return false;
		}
		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'delete-img=' + element.data('img-id'),
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );
				if( json.res == '1' ){
					element.find('.card-auto-link').slideUp().after('<div class="success">' + imagesMsgSuccesSuppression + '</div>');

					// Affiche une image supplémentaire
					var newIndex = autoLink_getLastVisible() + 1;
					$('.auto-link').eq(newIndex).slideDown()
					autoLink_loadImageForm( newIndex, 1 );
					autoLink_updateCountImages('del', 1);
				}else{
					element.find('.actions-links').append( '<div class="error">' + json.message + '</div>' );
				}
			}
		});
	}

	var autoLink_saveLinks = function( index ){
		$('.error').remove();

		var element 	= $('.auto-link[data-index="' + index + '"]:has(input:checked)');
		if( typeof element == 'undefined' || !element.length ){
			return false;
		}

		var paramToSave = $.param( $(element).find('input:checked, select:has(:selected)').not('[type=button]') );

		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'save-link=1&' + paramToSave,
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );
				if( json.res == '1' ){
					element.find('.card-auto-link').slideUp().after(
						'<div class="success">' + imagesMsgSuccesEnregistrement
						+ ' <a class="cancel-save" href="#">' + productsAnnuler + '</a></div>'
					);

					// Affiche une image supplémentaire
					var newIndex = autoLink_getLastVisible() + 1;
					$('.auto-link').eq(newIndex).slideDown();
					autoLink_loadImageForm( newIndex, 1 );
					autoLink_updateCountImages('del', 1);
				}else{
					element.find('.actions-links').append( '<div class="error">' + json.message + '</div>' );
				}
			}
		});
	}

	var autoLink_getLastVisible = function(){
		return $('.auto-link:visible:last').data('index');
	}

	// Gère le lien Annuler sur l'action de Masquage
	var autoLink_cancelMasked = function( index ){
		$('.error').remove();

		var element 	= $('.auto-link[data-index="' + index + '"]');
		if( typeof element == 'undefined' || !element.length ){
			return false;
		}

		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'cancel-masked=' + element.data('img-id'),
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );

				if( json.res == '1' ){
					element.find('.success').slideUp().remove();
					element.find('.card-auto-link').slideToggle();

					autoLink_updateCountImages('add', 1);
				}
			}
		});

		return false;
	}

	// Permet l'annulation d'une association (écran d'association automatique)
	var autoLink_cancelSave = function( index ){
		$('.error').remove();

		var element 	= $('.auto-link[data-index="' + index + '"]');
		var paramToSave = $.param( $(element).find('input:checked, select:has(:selected)').not('[type=button]') );

		$.ajax({
			url: '/admin/documents/images/import/ajax-img-import.php',
			data: 'cancel-link=1&' + paramToSave,
			type: 'post',
			success: function( data ){
				var json = $.parseJSON( data );

				if( json.res == '1' ){
					element.find('.success').slideUp().remove();
					element.find('.card-auto-link').slideToggle();

					autoLink_updateCountImages('add', 1);
				}
			}
		});

		return false;
	}

	var autoLink_reloadAutoLinkImage = function( imageID, htmlLinks ){
		$('#links-' + imageID + ' .actions-links').before( htmlLinks );
		$('#links-' + imageID + ' .checking-all').removeClass('none');
		$('#links-' + imageID + ' .actions-links .save').removeClass('none');
		$('#links-' + imageID + ' .no-product').addClass('none');
		hidePopup();
		return false;
	}

	var autoLink_pushHisto = function( h_Page ){
		var actually = window.location.search;
		actually = actually.replace(/\&page=[0-9]+/i, '');

		var histoURL = actually + ( actually.match(/\?/i) ? '&' : '?' ) + 'page=' + h_Page;
		window.history.pushState( {url: '/admin/documents/images/import/index.php', page: h_Page}, '', histoURL );
	}

	var autoLink_updateCountImages = function( addOrDel, count ){
		var imgCount = parseInt( $('#site-content .count_img').html() );
		if( isNaN(imgCount) ){
			imgCount = 0;
		}

		if( addOrDel == 'add' ){
			imgCount += count;
		}else{
			imgCount -= count;
		}

		if( imgCount < 0 ){
			imgCount = 0;
		}

		$('#site-content .count_img').html( imgCount );
	}
}














// element de l'image
var ajaxCurrent = false;
var elemImage = false;
var timer = false;
var current_autoload = false;
var init_scroll = false;
var imgs = new Array();
var unused = false;

$(document).ready(
	function(){
		readyImages();

		unused = $('#is_unused')!='undefined' && $('#is_unused').val()==1 ? 1 : 0;
		if( $('.autoload').length ){
			$(window).scroll(function(){ update_scroll(); });
			$('#pagination').hide();
		}

		if( typeof $('.img-detail') != 'undefined' && $('.img-detail').length ){
			loadListImages();
			clickAddContentsImage();

			window.onresize = loadListImages;
		}
	}
).delegate(
	'#infos-img .head-img-info', 'click', function(){
		unused = $('#is_unused')!='undefined' && $('#is_unused').val()==1 ? 1 : 0;

		var idImg = $(this).find('img').attr('src');
		idImg = idImg.substring( idImg.lastIndexOf('/')+1, idImg.lastIndexOf('.') );
		window.location.href = '/admin/documents/images/edit.php?image='+idImg+'&unused='+unused;
	}
).delegate(
	'.img-images a', 'click', function(){
		if( unused ){
			var img = $(this).find('img');
			if( img.hasClass('item-deleted') ){
				img.removeClass('item-deleted').parents('.img-images').find('.input-del-item').removeAttr('checked');
			}else{
				img.addClass('item-deleted').parents('.img-images').find('.input-del-item').attr('checked', 'checked');
			}

			displayInputDeleted();
			return false;
		}
	}
).delegate(
	'.selectedall', 'click', function(){
		$('.img-images img').addClass('item-deleted').parents('.img-images').find('.input-del-item').attr('checked', 'checked');
		displayInputDeleted();
	}
).delegate(
	'.unselectedall', 'click', function(){
		$('.img-images img').removeClass('item-deleted').parents('.img-images').find('.input-del-item').removeAttr('checked');
		displayInputDeleted();
	}
).delegate(
	'#del-img', 'click', function(){
		if( !unused ){
			return false;
		}

		var show = parseInt( $('.input-del-item:checked').length ) > 0;
		if( !show ){
			return false;
		}

		return window.confirm("Les images sélectionnées vont être supprimées. Cette action est irréversible, souhaitez-vous continuer ?");
	}
).delegate(
	'#is_associated', 'click', function(){
		var checked = $(this).is(':checked');
		window.location.href = '/admin/documents/images/import/index.php?is_associated=' + ( checked ? 1 : 0 );
	}
).delegate(
	'.del-in-ass', 'click', function(){
		var imgID = parseInt( $(this).attr('class').replace('del-in-ass default img-', '') );
		if( !isNaN(imgID) ){
			var thisImage = $(this);
			$.get('/admin/ajax/documents/ajax-images.php?upd-associated=1&img=' + imgID, function(res){
				if( res=='ok' ){
					thisImage.parents('.auto-link').remove();
					if( $('.auto-link').length<=1 ){
						window.location.reload();
					}
				}
			});
		}

		return false;
	}
).delegate(
	'#add-imgs', 'click', function(){
		displayPopup('Ajouter une ou plusieurs images à votre médiathèque', '', '/admin/ajax/media/img_popup.php?classe=add_imgs' );
	}
);

function displayInputDeleted(){
	var show = parseInt( $('.input-del-item:checked').length ) > 0;

	if( show ){
		$('#del-img').removeClass('no-click');
	}else{
		$('#del-img').addClass('no-click');
	}
}

function update_scroll(){
	if( $('.autoload').length > 0 ){
		if( $(document).scrollTop() + $(window).height() >= ($(document).height()-400) ){

			if(init_scroll && current_autoload == false){
				const current_page = $('.autoload').attr('data-page') ? parseInt($('.autoload').attr('data-page')) : 1;
				const pages = $('.autoload').attr('data-pages');

				if( current_page < pages ){
					$('.autoload').after('<div class="autoload_loader"><img src="/admin/images/loader_join_file.gif" height="16" width="16" alt="Chargement"/> Chargement des images suivantes en cours...</div>');
					current_autoload = $.get( document.location.href, 'page='+(current_page+1), function( html ){
						$('.autoload').append( $(html).find('.autoload').html()  );
						$('.autoload').attr('data-page', current_page+1);
						readyImages();
						$('.autoload_loader').remove();
						current_autoload = false;
					});
				}
			}
			init_scroll = true;
		}
	}
}

function readyImages(){
	$('.img-images img').each(function(){
		// elemImage = $(this);

		var idImg = $(this).attr('src');
		idImg = idImg.substring( idImg.lastIndexOf('/')+1, idImg.lastIndexOf('.') );

		if( $.inArray(idImg, imgs)==-1 ){
			var elem = $(this);
			$(this).parent().hover(
				function(){
					timer = setTimeout(function(){
						loadInfoImageBlock( elem, idImg );
					}, 750);
				},
				function(){
					clearTimeout(timer);
				}
			);

			imgs.push( idImg );
		}
	});
}

function loadActionInfoImageBlock(){
	$('#infos-img').hover(
		function(){ },
		function(){
			$(this).hide();
		}
	)
}

function infoImageBlock( idImg ){
	var isUnused = $('#is_unused')!='undefined' && $('#is_unused').val()==1 ? 1 : 0;
	$.ajax({
		type: "POST",
		url: '/admin/documents/images/ajax-informations.php',
		data: 'image=' + idImg + '&unused=' + isUnused,
		async:false,
		success: function( txt ){
			$('#infos-img').html( txt );
			$('#infos-img').show();
			loadActionInfoImageBlock();
		},
		error: function(msg){ }
	});
}

function loadInfoImageBlock( blockImage, idImg ){
	// dimension du bloque information
	var widhtInfos = 350;
	var heightInfos = 156;

	// information sur la dimension de la fenêtre
	var widthDocument = $(document).width();

	// information sur le position et dimension de l'élément image
	var position = blockImage.position();
	var height = blockImage.parent().outerHeight(); // 6 = padding 2 et border 1
	var width = blockImage.parent().outerWidth();

	// calcule la position de l'élément information
	var topInformation = ( position.top + (height/2) ) - (heightInfos/2);
	var leftInformation = ( position.left + (width/2) ) - (widhtInfos/2);

	// création du bloc contenant les informations sur l'image
	infoImageBlock( idImg );

	// place le bloc
	$('#infos-img').css( 'top', topInformation-144 > 0 ? topInformation : 173 );
	$('#infos-img').css( 'left', leftInformation-169 > 0 ? leftInformation : 180 );

	if( $(document).width() > widthDocument ){
		$('#infos-img').css( 'left', leftInformation-119 );
	}
}

function searchImages(){
	var q = $('#search-img').val();
	if( q=='' ){
		alert('Veuillez saisir le nom ou une partie du nom de l\'image');
		return false;
	}

	if( ajaxCurrent ){
		ajaxCurrent.abort();
	}

	ajaxCurrent = $.ajax({
		type: "POST",
		url: 'ajax-informations.php',
		data: 'q=' + q,
		async: false,
		dataType: 'xml',
		success: function( xml ){
			$('#img-media').html( $(xml).find('images').text() );
			$('#pagination').html( $(xml).find('pagination').text() );
			readyImages();
		},
		error: function(msg){ },
		complete: function(){
			ajaxCurrent = false;
		}
	});
}

function loadListImages( min ){
	var idImg = $('#id-img').val();
	var width = $('#list-img').width();
	var nbImage = parseInt( width/160 )-1;
	var unused = $('#unused').val();

	$.ajax({
		type: "POST",
		url: '/admin/documents/images/ajax-informations.php',
		data: 'loadListImage=1&image='+idImg+'&nbimage='+nbImage+'&min='+( min>=0 ? min : -1) + '&unused=' + unused,
		async:false,
		success: function( html ){
			$('#list-img').html( html );
			readyImages();
			var margin = ($('#list-img').width()-30-(156*nbImage))/nbImage;
			$('#list-img .img-images').css( 'margin', '0 '+Math.floor(margin/2)+'px' );
		},
		error: function(msg){ }
	});
	return false;
}

function clickAddContentsImage(){
	var title = new Array();
	title[1] = 'Associer à un produit';
	title[3] = 'Associer à une catégorie';
	title[5] = 'Associer à une marque';
	title[6] = 'Associer à un magasin';
	title[11] = 'Associer à un contenu';
	title[14] = 'Associer à une actualité';

	$('.linked-img input[name=add]').click(function(){
		var elemClass = $(this).attr('id');
		elemClass = elemClass.substring(4);
		displayPopup( title[ elemClass ], '', 'popup.php?class='+elemClass+'&image='+$('#id-img').val() );
	});
}

function hidePopupImage(){
	hidePopup();

	$.ajax({
		type: "POST",
		url: '/admin/documents/images/ajax-informations.php',
		data: 'reloadImage='+$('#id-img').val(),
		async:false,
		success: function( html ){
			$('#img-infos .elem-links').html( html );
			clickAddContentsImage();
		},
		error: function(msg){ }
	});
}

function delImagesLink(cls, img, cnt){
	var noCnt = new Array();
	noCnt[1] = 'Cette image n\'est associée à aucun produit';
	noCnt[2] = 'Cette image n\'est associée à aucun client';
	noCnt[3] = 'Cette image n\'est associée à aucune catégorie';
	noCnt[5] = 'Cette image n\'est associée à aucune marque';
	noCnt[6] = 'Cette image n\'est associée à aucun magasin';
	noCnt[11] = 'Cette image n\'est associée à aucun contenu';
	noCnt[14] = 'Cette image n\'est associée à aucune actualité';

	$.ajax({
		type: "POST",
		url: '/admin/documents/images/ajax-informations.php',
		data: 'delImage='+img+'&idCnt='+cnt+'&class='+cls,
		async:false,
		dataType: 'xml',
		success: function( xml ){
			if( $(xml).find('result').attr('type')==1 ){
				var elemParent = $('#cnt-img-'+cls+'-'+cnt).parent();
				$('#cnt-img-'+cls+'-'+cnt).remove();
				if( !elemParent.find('.cnt-infos').length ){
					elemParent.html( noCnt[cls] );
				}
			}
		},
		error: function(msg){
		}
	});
	return false;
}

function displayNewMedia(){
	$('#new-media').show();
	return false;
}

function hideNewMedia(){
	$('#new-media').hide();
	return false;
}

function displayMoreLinks( img, cnt ){
	if( !$('#more-links-'+img+'-'+cnt).is(':visible') ){
		$('#more-links-'+img+'-'+cnt).show();
		$('#hide-more-links'+img+'-'+cnt).show();
		$('#show-more-links'+img+'-'+cnt).hide();
	} else {
		$('#more-links-'+img+'-'+cnt).hide();
		$('#hide-more-links'+img+'-'+cnt).hide();
		$('#show-more-links'+img+'-'+cnt).show();
	}
	return false;
}

function checkPrdImage( img, check ){
	if( check )
		$('#links-'+img+' input[type=checkbox]').attr('checked', 'checked');
	else
		$('#links-'+img+' input[type=checkbox]').removeAttr('checked');
}


var current_ajax_request = false;
$(document).ready(
	function(){
		if( typeof $('#documents') != 'undefined' && $('#documents').length ){
			riaSortable.create({
				'table'		:	$('#documents'),
				'url'		:	'/admin/ajax/documents/ajax-position-update.php'
			});
		}

		if( typeof $('#type-docs') != 'undefined' && $('#type-docs').length ){
			riaSortable.create({
				'table' : $('#type-docs'),
				'url' : '/admin/ajax/documents/ajax-type-position-update.php'
			});
		}

		$('.selectorlanguage .selectorview').click(function () {
			if ($('.selectorlanguage .selector').css('display') !== 'block') {
				$('.selectorlanguage .selector').show();
			} else {
				$('.selectorlanguage .selector').hide();
			}
		});
		
		$('.selectorlanguage .selector a').click(function () {
			$('.selectorlanguage .view').html($(this).html());
			var l = $(this).attr('name').substring($(this).attr('name').indexOf('-') + 1, $(this).attr('name').length);
			
			reloadLanguage(tab, l);
		});

		$("#filter-type").riaSelector({ 'title': 'Type de téléchargements' });	
	}
).delegate(
	'.check-all', 'click', function(){
			$($(this).parent().get(0)).find('input[type=checkbox]').attr('checked','checked');
			return false;
		}
).delegate(
	'.uncheck-all', 'click', function(){
			$($(this).parent().get(0)).find('input[type=checkbox]').removeAttr('checked');
			return false;
	}
).delegate(
	'.check-all-objects', 'click', function(){
		var selected = $(this).attr('id');

		if( $(this).is(':checked') ){
			$('.' + selected).attr('checked', 'checked');
		}else{
			$('.' + selected).removeAttr('checked');
		}
	}
).delegate(
	'#add-obj', 'click', function(){
		displayPopup( documentsDisplayPopupaddLienObjet, '', '/admin/documents/popup-add-object.php?doc=' + $('#doc-id').val() );
	}
).delegate(
	'a[name=selecttype]', 'click', function(){
		$('.error').remove();
		var page = 1; 
		if( $(this).attr('data-page') ){
			page = $(this).attr('data-page');
		}
		
		$.get( '/admin/documents/popup-docs.php?type='+$(this).attr('data-id')+'&p='+page, function(html){
			$('#popup-content').html(html);
		});
	}
).delegate(
	'#selectdoc', 'click', function(){
		$('.error').remove();
		if( !$('input[type=radio]:checked').length ){
			$('.pop-form-search').before( '<div class="error">' + documentErreurMsgSelectDocument + '</div>' );
			return false;
		}
		var id = $('input[type=radio]:checked').val();
		var name = $('input[type=radio]:checked').attr('data-name');
		
		window.parent.parent_select_docs( id, name );
		window.parent.hidePopup();
	}
).delegate(
	'#filter-type', 'change', function () {
		var type = $(this).val();

		reloadDownloads(type);
	}
);

function reloadObjects(){
		hidePopup();

		var url = window.location.href;
		if( !url.match(/&tab=/g) ){
			url = url + '&tab=objects';
		}
		window.location.href = url;
}

function reloadLanguage(tab, lng) {
	hidePopup();
	
	var url = window.location.href;
	if (lng!='') {//url.match(/&lng=/)
		
		url = url.replace(/(&lng=)[^\&]+/, '');
		url += '&lng=' + lng;
	}
	if (!url.match(/&tab=/g)) {
		url = url + '&tab=' + tab;
	}
	
	window.location.href = url;
}

function reloadDownloads(type) {
	var url = window.location.href;
	if (!url.match(/&tab=/g)) {
		url = url + '&tab=downloads';
	}

	if (type != '') {
		if (url.match(/&download_type=/)) {
			url = url.replace(/(&download_type=)[^\&]+/, '');
		}
		url += '&download_type='+type;
	}
	window.location.href = url;
}

function docSwitchTablePage( page, pages, type ){
	// Place le scroll en haut à gauche de l'écran
	var height = $("#site-content #documents tbody").height();
	$("#site-content #documents tbody").html('<tr><td colspan="4" style="height: '+height+'px; color: rgb(95, 95, 95); text-align: center; padding: 5px; padding-top: 15px;" id="load-json"><img style="border: medium none; width: 32px;" src="/admin/images/json-load.gif" alt=""><div style="vertical-align: middle; margin-top: 8px;">' + msgLoading + '</div></td></tr>');
	
	// Requête AJAX pour le changement de page
	$.ajax({
		type: "POST",
		url: '/admin/ajax/documents/json-documents.php',
		data: 'p='+page+'&type='+type+'&limit=25',
		dataType: 'json',
		async:true,
		success: function(msg){
			var html = '';
			
			// Créé le contenu du tableau
			for( var i=0 ; i<msg.length ; i++ ){
				html += '	<tr>';
				html += '		<td headers="doc-sel"><input type="checkbox" class="checkbox" name="doc[]" value="'+msg[i].id+'" /></td>';
				html += '		<td headers="doc-name"><a href="edit.php?doc='+msg[i].id+'&amp;type='+msg[i].type_id+'&amp;page='+page+'" title="' + documentsAfficherFiche + '">'+htmlspecialchars(msg[i].name)+'</a></td>';
				html += '		<td headers="doc-file"><a href="dl.php?doc='+msg[i].id+'">'+htmlspecialchars(msg[i].filename)+'</a></td>';
				html += '		<td headers="doc-remp"><input class="file" type="file" id="file-'+msg[i].id+'" name="file-'+msg[i].id+'"/><a onclick="resetFileInput(\'file-'+msg[i].id+'\')"><img class="noborder" src="/admin/images/del.svg" alt="Annuler" title="' + documentsAnnuler + '"/></a></td>';
				html += '	</tr>';
			}
			
			// Affiche le contenu
			$("#site-content #documents tbody").html(html);
			
			// Affichage de la pagination
			$("#pagination").html( switchPage(page, pages, 5, 5, 2, 3, 'docSwitchTablePage', ', '+type, 'index.php', '&type='+type) );
		},
		error: function(){
			return true;
		}
	});
	return false;
}

function newTabDocuments( clsID, objID0, objID1, objID2 ){
	var link = '/admin/documents/popup-add-document.php?doc=0&type=0&lng=fr&popup=1&cls=' + clsID + '&obj_0=' + objID0;
	if( parseInt(objID1) ){
		link += '&obj_1=' + objID1;
	}

	if( parseInt(objID2) ){
		link += '&obj_2=' + objID2;
	}

	displayPopup( documentNouveau, '', link, '', 992, 525 );
	return false;
}

function reloadTabDocuments(){
	hidePopup();
	window.location.href = window.location.href.replace( new RegExp("&tab=[a-z]+", "g"), '' ) + '&tab=documents';
	return false;
}

function cancelEdit(){
	var t = window.location.href.match( /type=[0-9]+/ );
	window.location.href = 'index.php?' + t;
}
function validForm(frm){
	if( typeof(frm) == "undefined" ){
		frm = document.getElementById('form-doc');
	}

	if( !trim(frm.name.value) ){
		alert(documentsAlertChampsNom);
		frm.name.focus();
		return false;
	}
	if( !frm.filename ){
		if( !trim(frm.file.value) ){
			if( typeof $('#is-default-lng') != 'undefined' && $('#is-default-lng').val() != '0' ){
				alert(documentAlertChampsFichier);
				frm.file.focus();
				return false;
			}
		}
	}
}
function confirmDel(){
	return window.confirm(documentsConfirmSupressionDocument);
}
function confirmDelList(){
	return window.confirm(documentsConfirmSupressionMultipleDocument);
}
function confirmDelTypesList(){
	return window.confirm(documentsConfirmSupressionTypeDocument);
}
function search_references(input, end){

	if( $("#"+input).val() != '') {
		if( inp!=input )
			$(".ac_results").remove();
		if( !passe || inp!=input){
			$("#"+input).autocomplete({
				source: "ajax-search-ref.php",
				delay:200,
			});
			passe = true;
			inp = input;
		if( end )
			window.scroll(0,10000);
		}
	}
	
	if( end )
		window.scroll(0,10000);
}
function updateCat( id, idParent, catname){
	$("#input-cat-id").val(id);
	$("#input-cat-parent-id").val(idParent);
	$("#input-cat-name").val(catname);
	hidePopup();
}
function doc_control_form_move(){
	if( !$('input[type=radio]:checked').length ){
		alert(documentSelectEmplacement);
		return false;
	}

	return true;
}

// fonctions qui permet de vérifier la taille du fichier téléchargé
function showFileSize() {
    var input, file, message;

    // (Can't use `typeof FileReader === "function"` because apparently
    // it comes back as "object" on some browsers. So just see if it's there
    // at all.)
    if (!window.FileReader) {
		message = 'Ce type de fichier n\'est pas encore pris en charge par ce navigateur.';
    }

    input = document.getElementById('file');
    if (!input) {
		message = 'Impossible de trouver le fichier à télécharger.';
    }
    else if (!input.files) {
        message = 'Ce navigateur ne semble pas supporter la propriété `files` des entrées de fichier';
    }
    else if (!input.files[0]) {
        message = 'Veuillez sélectionner un fichier avant de cliquer sur "Charger"';
    }
    else {
        file = input.files[0];
        message = file.size;
	}

	return message;
}

/** Convertit un poids exprimé en bytes vers un poids lisible par un utilisateur (Ko, Mo, Go)
 *	@param bytes Poids à convertir
 *	@return une chaîne de caractère contenant le poids dans une unité adaptée à la valeur
 */ 
function formatSizeUnits(bytes){
	if      (bytes >= 1073741824) { bytes = (bytes / 1073741824).toFixed(2) + " Go"; }
	else if (bytes >= 1048576)    { bytes = (bytes / 1048576).toFixed(2) + " Mo"; }
	else if (bytes >= 1024)       { bytes = (bytes / 1024).toFixed(2) + " Ko"; }
	else if (bytes > 1)           { bytes = bytes + " octets"; }
	else if (bytes == 1)          { bytes = bytes + " octet"; }
	else                          { bytes = "0 octets"; }

	return bytes;
}
 
$( document ).ready(function() {
	//vérification du fichier à télécharger
	$('#file').on("change", function(){
		var thisfile = showFileSize();
		var maxfile = $("#file").attr('data-size-max');
		if( $.isNumeric(thisfile) ){
			if( thisfile > maxfile ){

				thisfile = formatSizeUnits(thisfile);
				maxfile =  formatSizeUnits(maxfile);
				$( ".error-file" ).remove();
				$('[name=save]').prop('disabled', true);
				$('<p class="error-file">ATTENTION ! Le fichier que vous souhaitez télécharger est trop grand. Il est de '+thisfile+' et ne doit pas dépasser '+maxfile+'</p>').insertAfter($('span.color-red'));
			}else{
				$('[name=save]').prop('disabled', false);
				$( ".error-file" ).remove();
			}
		}else{
			$('<p class="error-file">ATTENTION ! '+thisfile+'</p>').insertAfter($('span.color-red'));
		}
	});
});

function previewClick( preview ){
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#4574BF' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor!='';
	
	$('.edit-zones')
		.toggle($('.preview input:checked').length == 1)
		.off('click')
		.click(function () {
			var idStart = window.location.search.indexOf('&cat=') + 5;
			var idStop = window.location.search.indexOf('&', idStart);
			var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
			var $preview = $('.preview input:checked').parents('.preview:eq(0)');
			displayPopup(cmsDiplayPopupZonesCliquables, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=17&obj_id_0=' + id, null, 756, 602);
		});
	
	//Affichage du bouton suppirmer
	if( $('.preview input:checked').length >= 1 ){
		$('.delimg').show();
	}else{
		$('.delimg').hide();
	}
}
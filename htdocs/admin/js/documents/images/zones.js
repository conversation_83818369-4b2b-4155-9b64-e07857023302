$(function(){
    $('body')
        .on('selectstart', function(event){event.preventDefault(); return false;})
        .on('keyup', function(event){
            if (event.which == 27) { // escape key
                $('.form-container').hide();
            }
        });

    $('[name="save-all-zone"]').click(function(){
        
        var editID = parseInt($form.find('[id="index"]').val());
        if (!isNaN(editID) && editID >= 0) {
            $form.find('input,select,textarea').each(function(idx, elt){
                var $elt = $(elt);
                zonesClick[editID][$elt.attr('id')] = $elt.val();
            });
        }
    
        var formData = new FormData();
        for (var zoneIdx in zonesClick) {
            for (var attrIdx in zonesClick[zoneIdx]) {
                formData.append('zone[' + zoneIdx + '][' + attrIdx + ']', zonesClick[zoneIdx][attrIdx]);
            }
        }

        $('.error,.success').text('').hide();
        
        var res = $.ajax({
            url: window.location.href,
            data: formData,
            processData: false,
            contentType: false,
            type: 'POST',
            done: function (data) {
                if (typeof {} != typeof data || data.error || !data.data || typeof {} != typeof data.data) {
                    $('.error').text(data.error || zonesErreurEnregistrementZonesImages).show();
                    return;
                }
                if (data.success) {
                    $('.success').text(data.success).show();
                }
            }
        });

        return res
            .done(function (data) {
                if (typeof {} != typeof data || data.error || !data.data || typeof {} != typeof data.data) {
                    $('.error').text(data.error || zonesErreurEnregistrementZonesImages).show();
                }

                if (data.success) {
                    $('.success').text(data.success).show();
                }
            })
            .error(function(){
                $('.error').text(zonesErreurEnregistrementZoneImage).show();
                return false;
            });
    });
    
    $('[name="add-new-zone"]').click(function(){
        saveZoneObject();
        clearZoneForm();
    
        var $container = $('.resize-container');
    
        var containerWidth = $container.innerWidth();
        var containerHeight = $container.innerHeight();
        var width = Math.min(containerWidth / 2, 200);
        var height = Math.min(containerHeight / 2, 200);
        
        zonesClick.push({
            index      : zonesClick.length,
            id      : '',
            title 	: 'Nouvelle zone',
            desc 	: '',
            search 	: '',
            href_type 	: '1',
            href_target 	: '_self',
            x		: Math.max(0, containerWidth/2 - width/2),
            y 		: Math.max(0, containerHeight/2 - height/2),
            width 	: width == 0 || containerWidth == 0 ? 0 : width,
            height 	: height == 0 || containerHeight == 0 ? 0 : height,
            delete 	: false
        });
    
        drawZoneClick(zonesClick.length - 1, zonesClick[zonesClick.length - 1]);
        $('.resize-drag').removeClass('selected');
        zonesClick[zonesClick.length - 1].elt.addClass('selected');
    
        refreshZoneForm(zonesClick.length - 1, zonesClick[zonesClick.length - 1]);
    });
    
    $('[name="save-zone"]').click(function(){
        saveZoneObject();
        $('.form-container').hide();
    });

    var onDeleteZone = function onDeleteZone(event) {
        event.preventDefault();
        
        var $form = $('.zone-details');
        var editID = parseInt($form.find('[name="zone[index]"]').val());
        
        var handleDelete = function (data) {
            if (data.error) {
                $('.error').text(data.error).show();
                return;
            }
            if (data === true || data.success) {
                $('.success').text(data.success).show();
                $('.resize-drag.selected').remove();
                clearZoneForm();
            }
        };
        if (!isNaN(editID) && editID >= 0) {
            if (!$form.find('[name="zone[id]"]').val()) {
                return handleDelete(true);
            }
    
            var formData = new FormData();
            for (var attrIdx in zonesClick[editID]) {
                formData.append('zone[' + attrIdx + ']', zonesClick[editID][attrIdx]);
            }
            formData.append('delete-zone', '1');
            
            $.ajax({
                url: window.location.href,
                data: formData,
                processData: false,
                contentType: false,
                type: 'POST'
            })
                .done(handleDelete)
                .error(function(){
                    $('.error').text(zonesErreurSuppressionZoneImage).show();
                })
    
        } else {
            handleDelete(true);
        }
    };
    
    $('.popup-content').on('click', '.delete-zone', onDeleteZone);
    $('.close-zone').click(function(event){
        event.preventDefault();
        $('.form-container').hide();
    });
    
    $(document).on('mousedown', '.resize-drag', function(){
        var $form = $('.zone-details');
    
        var editID = $(this).data('zone-id');
        var object = zonesClick[editID];
        var $this = $(this);
    
        var $selected = $('.resize-drag.selected:eq(0)');

        if ($selected.length) {
            $selected.removeClass('selected');
        }
        
        $this.addClass('selected');
        
        refreshZoneForm(editID, object);
        
        $form.show();
    });
    
    function saveZoneObject(){
        const $form = $('.zone-details');
        
	    $('.error').text('').hide();
        $('.success').text('').hide();
        var editID = $form.find('[id="index"]').val();
        
        if (!isNaN(editID) && (editID = Math.round(editID)) >= 0 && zonesClick[editID]) {
            $form.find('input,select,textarea').each(function(idx, elt){
                var $elt = $(elt);
                zonesClick[editID][$elt.attr('id')] = $elt.val();
            });

            if (!$form.find('[name="zone[title]"]').val().length) {
                return null;
            }
            
            var res = $.ajax({
                url: window.location.href,
                data: new FormData($form.get(0)),
                contentType: false,
                processData: false,
                type: 'POST'
            });
    
            return res
                .done(function (data) {
                    if (typeof {} != typeof data || data.error || !data.data || typeof {} != typeof data.data) {
                        $('.error').text(data.error || zonesErreurEnregistrementZoneImage).show();
                    }
    
                    if (data.success) {
                        $('.success').text(data.success).show();
                        var elt = zonesClick[editID].elt;
                        zonesClick[editID] = data.data;
                        zonesClick[editID].elt = elt;
                        refreshZoneForm(editID, zonesClick[editID]);
                        drawZoneClick(editID, zonesClick[editID]);
                    }
                })
                .error(function(){
                    $('.error').text(zonesErreurEnregistrementZoneImage).show();
                    return false;
                });
        }
    }

    var popupDragOn = false;
    var popupDragStartListener = function popupDragStartListener(event) {
        var $select = $('#href_type');
        var selectPos = $select.offset();
        var selectWidth = $select.outerWidth();

        if (selectPos.left <= event.pageX - event.dx && selectPos.left + selectWidth >= event.pageX - event.dy
            && selectPos.top <= event.pageY - event.dy && selectPos.top + selectWidth >= event.pageY - event.dy
        ) {
            popupDragOn = false;
            return;
        }

        popupDragOn = true;
    };

    var popupDragEndListener = function popupDragEndListener() {
        popupDragOn = false;
    };

    var popupDragMoveListener = function popupDragMoveListener(event) {
        if (!popupDragOn) {
            return;
        }
        var $target = $(event.target),
        // keep the dragged position in the data-x/data-y attributes
        x = (parseFloat($target.data('x')) || 0) + event.dx,
        y = (parseFloat($target.data('y')) || 0) + event.dy;
        
        // translate the element
        var top = parseFloat($target.css('top'));
        var left = parseFloat($target.css('left'));
        $target.css({'top': (top + event.dy) +'px', 'left': (left + event.dx) +'px'});

        // update the posiion attributes
        $target.data('x', x);
        $target.data('y', y);
    };
    window.popupDragMoveListener = popupDragMoveListener;
    
    var dragMoveListener = function dragMoveListener(event) {
        var target = event.target,
            $target = $(target),
            objectID = $target.data('zone-id');
            // keep the dragged position in the data-x/data-y attributes
    
        zonesClick[objectID].x = (parseFloat(zonesClick[objectID].x) || 0) + event.dx;
        zonesClick[objectID].y = (parseFloat(zonesClick[objectID].y) || 0) + event.dy;
    
        // translate the element
        var top = parseFloat($target.css('top'));
        var left = parseFloat($target.css('left'));
        $target.css({ 'top': (top + event.dy) + 'px', 'left': (left + event.dx) + 'px' });
    
        refreshZoneForm(objectID, zonesClick[objectID]);
    }

    interact('#popup_ria')
        .draggable({
            onstart: popupDragStartListener,
            onmove: popupDragMoveListener,
            onend: popupDragEndListener,
            restrict: {
                restriction: 'parent',
                elementRect: { top: 0, left: 0, bottom: 1, right: 1 }
            },
        })
    
    interact('.resize-drag')
    .draggable({
        onmove: dragMoveListener,
        restrict: {
            restriction: 'parent',
            elementRect: { top: 0, left: 0, bottom: 1, right: 1 }
        },
    })
    .on('dragend', function() {
        // Enregistre la zone en cours de modification
        saveZoneObject();
    })
    .resizable({
        // resize from all edges and corners
        edges: { left: true, right: true, bottom: true, top: true },
    
        // keep the edges inside the parent
        restrictEdges: {
        outer: 'parent',
        endOnly: true,
        },
    
        // minimum size
        restrictSize: {
        min: { width: 40, height: 40 },
        },
    
        inertia: true
    })
    .on('resizemove', function (event) {
        var target = event.target,
        $target = $(target),
        objectID = $target.data('zone-id');
        // keep the dragged position in the data-x/data-y attributes
    
        zonesClick[objectID].width = Math.max(85, event.rect.width);
        zonesClick[objectID].height = Math.max(55, event.rect.height);
        zonesClick[objectID].x = (parseFloat(zonesClick[objectID].x) || 0) + event.deltaRect.left;
        zonesClick[objectID].y = (parseFloat(zonesClick[objectID].y) || 0) + event.deltaRect.top;
    
        var showMoreActions = zonesClick[objectID].width > 85;

        var x = Math.round(zonesClick[objectID].x),
            y = Math.round(zonesClick[objectID].y);

        $target
            .css('width', event.rect.width)
            .css('height', event.rect.height)
            .css({
                'top': y + 'px',
                'left': x + 'px'
            })
            .find('.delete-zone')
                .toggle(showMoreActions)
            .end()
            .find('.separator')
                .toggle(showMoreActions);
    
        refreshZoneForm(objectID, zonesClick[objectID]);
    })
    .on('resizeend', function() {
        // Enregistre la zone en cours de modification
        saveZoneObject();
    });
    
    var refreshZoneForm = function(index, zoneClick) {
        if (!zoneClick) {
            zoneClick = zonesClick[index];
        }
        
        $form = $('.zone-details');
        $('.error').text('').hide();
        $('.success').text('').hide();
        $form.find('[name="zone[index]"]').val(index);
        for(var attr in zoneClick) {
            $form.find('[name="zone['+attr+']"]').val(zoneClick[attr]);
        }

        if (!zoneClick) {
            return;
        }
        
        $form.find('#search').val(zoneClick.obj_name);
        if (zoneClick.elt) zoneClick.elt.find('.title').text(zoneClick['title']);
    };
    
    var clearZoneForm = function(){
        $('.form-container').hide();
        $form = $('.zone-details');
        $form.get(0).reset();
    
        $form.find('input[type="hidden"]').each(function(){
            $(this).val('');
        });
    
        $('.search-result-list').children().remove().end();
    }
    
    var drawZoneClick = function(index, zoneClick){
        var $container = $('.resize-container');
        var $formContainer = $('.form-container');
        zoneClick.elt = zoneClick.elt || $('<div class="resize-drag"></div>').appendTo($container);

        var x = Math.round(zoneClick.x), 
            y = Math.round(zoneClick.y);
    
        zoneClick.elt
            .css({
                'top':  y + 'px',
                'left': x + 'px'
            })
            .css('width', Math.round(zoneClick.width))
            .css('height', Math.round(zoneClick.height))
            .data('zone-id', index);
        
        if (zoneClick.elt.children().length > 0) {
            return;
        }

        $('<span class="title"></span>')
            .text(zoneClick['title'])
            .appendTo(zoneClick.elt);

        var $actions = $('<div class="actions"></div>')
            .appendTo(zoneClick.elt);

        $('<a>Editer</a>')
            .appendTo($actions)
            .click(function(event){
                event.preventDefault();
                refreshZoneForm(index);
                $formContainer.show();
            })
            .after('<span class="separator"> | </span>');
        
        $('<a>' + zonesSuprrimer + '</a>')
            .appendTo($actions)
            .addClass('delete-zone');
    };
    
    refreshZoneForm(0, zonesClick[0]);
    
    for (var idx in zonesClick) {
        drawZoneClick(idx, zonesClick[idx]);
    }
    //*/
    
    $('.resize-drag:eq(0)').addClass('selected');

	window.openSelector = function openSelector( tab ){
        var url = false;
        var tabId = Math.round(tab);
		switch( tabId ){
			case 1 : // product
				url = '/admin/ajax/catalog/ajax-product-select.php?show_search=1&cnt_publish=0';
				break;
			case 3 : // product category
				url = '/admin/catalog/popup-categories.php';
				break;
			case 17 : // document
				url = '/admin/documents/popup-docs.php';
				break;
			case 11 : // CMS category
				url = '/admin/tools/cms/popup-cms.php';
				break;
			case 6 : // store
				url = '/admin/config/livraison/stores/popup-stores.php';
				break;
			default:
				break;
		}
		
		if (url) {
            $('#edit-link').hide();
            $('#popup_ria .content').hide();

            $('#choose-elem')
				.html('<iframe id="iframe-select" style="width: 100%; height: 100%" src="' + url + '"></iframe>')
				.css('width', '100%')
                .css('height', '100%')
                .css('margin-top', '10px');
		}
		
		return false;
	};

	window.hidePopup = function hidePopup() {
        $('#choose-elem')
        .html('')
        .css('width', '0px')
        .css('height', '0px');
        $('#popup_ria .content').show();
	}

	window.updateCat = window.updateCat || function updateCat (id, parent, title){
		$('#href').val('category:' + id);
		$('#title').val(title);
		$('#search').val(title);
	}

	window.parent_select_prd = function parent_select_prd(id, title, name) {
		$('#href').val('product:' + id);
		$('#search').val(title || name);
		$('#title').val(title || name);
	}
	
	window.parent_select_store = function parent_select_store(id, title) {
		$('#href').val('store:' + id);
		$('#search').val(title);
		$('#title').val(title);
	}

	window.parent_select_doc = function parent_select_doc(id) {
		$('#href').val('doc:' + id);
		$('#search').val(id);
	}

	window.parent_select_cms = function parent_select_cms(id, title) {
		$('#href').val('cms:' + id);
		$('#search').val(title);
		$('#title').val(title);
	}

	var activeAutocompleted = function activeAutocompleted( clsId ){
		var tab = null;
		switch (Math.round(clsId)) {
			case 1:
				tab = 'product_tab';
				break;
			case 3:
				tab = 'category_tab';
				break;
			case 6:
				tab = 'store_tab';
				break;
			case 11:
				tab = 'cms_tab';
				break;
				break;
			case 17:
				tab = 'document_tab';
				break;
 			default:
				return;
		}

		$('#search').autocomplete({
			selectFirst: true,
			source: '/admin/ajax/tinymce/ajax-links.php?search=1&tab=' + tab,
			minLength: 3,
			select: function(event, ui) {
				var idElem = ui.item.id;
				
				var url = '';
				switch( Math.round(clsId) ){
					case 1 :
						url = 'product:' + idElem;
						break;
					case 3 :
						url = 'category:' + idElem;
						break;
					case 17 :
						url = 'doc:' + idElem;
						break;
					case 11 :
						url = 'cms:' + idElem;
						break;
					case 6 :
						url = 'store:' + idElem;
						break;
				}
				
				$('#href').val( url );
				$('#title').val( $.trim(ui.item.label) );
			}
		});
	};

	var displayTabsLinks = function displayTabsLinks( tab ) {
        $('#search').val('');
		
		switch( Math.round(tab) ){
            case 1 :
			case 3 :
			case 5 :
			case 6 :
			case 11 :
                $('.href-label').hide();
                $('.search-label').show();
                break;
			default :
				$('.href-label').show();
				$('.search-label').hide();
				break;
		}
		
        $('#search')
            .off('click')
            .click(function(event){
                event.preventDefault();
                openSelector($('#href_type').val());
            });
		
		$('#tab option[value=' + tab + ']').attr('selected', 'selected');
		
		activeAutocompleted( tab );
	};
	
	$('#href_type').on('change', function(event){
		displayTabsLinks($(event.currentTarget).val());
	});

    activeAutocompleted( $('#href_type').val() );
});

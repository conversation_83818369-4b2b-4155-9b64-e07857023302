$(document).ready(function(){
	
	// Boutons "Cocher tout" / "Décocher tout" 
	$('#prf-rights .catrgh').click(function(){
		var idcat = $(this).val();
		if( $(this).is(':checked') ){
			$('#prf-rights .catrgh-'+idcat).attr('checked', 'checked');
		}else{
			$('#prf-rights .catrgh-'+idcat).removeAttr('checked');
		}
	});
	
	// Active / Désactive les boutons "Cocher tout" / "Décocher tout" depuis la liste des enfants
	$('#prf-rights .checkright').click(function(){
		var idcat = $(this).attr('class');
		idcat = idcat.substring( idcat.indexOf('-')+1 );
		var countRgh = $('#prf-rights .catrgh-'+idcat).length;
		var countRghCheck = $('#prf-rights .catrgh-'+idcat+':checked').length;
		if( countRgh!=countRghCheck ){
			$('#catrgh-'+idcat).removeAttr('checked');
		} else {
			$('#catrgh-'+idcat).attr('checked', 'checked');
		}
	});
	
	// Gère l'accordéon de catégories de droits
	$('#prf-rights .linecat').click(function(){
		var id = $(this).find('input.catrgh').val();
		if( id!=undefined ){
			if($(this).hasClass('inexpansible')) {
				$(this).removeClass('inexpansible');
				$(this).addClass('expansible');
				$('#prf-rights .linergh-'+id).hide();
			}else{
				$('#prf-rights .inexpansible').addClass('expansible');
				$('#prf-rights .inexpansible').removeClass('inexpansible');
				
				$(this).removeClass('expansible');
				$(this).addClass('inexpansible');
				$('#prf-rights .linergh').hide();
				$('#prf-rights .linergh-'+id).show();
			}
			
		}
	});

});
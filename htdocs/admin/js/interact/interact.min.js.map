{"version": 3, "sources": ["src/minHeader.js", "src/header.js", "index.js", "src/Eventable.js", "src/InteractEvent.js", "src/Interactable.js", "src/Interaction.js", "src/actions/base.js", "src/actions/drag.js", "src/actions/drop.js", "src/actions/gesture.js", "src/actions/resize.js", "src/autoScroll.js", "src/autoStart/InteractableMethods.js", "src/autoStart/base.js", "src/autoStart/drag.js", "src/autoStart/gesture.js", "src/autoStart/hold.js", "src/autoStart/resize.js", "src/defaultOptions.js", "src/index.js", "src/inertia.js", "src/interact.js", "src/interactablePreventDefault.js", "src/modifiers/base.js", "src/modifiers/restrict.js", "src/modifiers/restrictEdges.js", "src/modifiers/restrictSize.js", "src/modifiers/snap.js", "src/modifiers/snapSize.js", "src/pointerEvents/PointerEvent.js", "src/pointerEvents/base.js", "src/pointerEvents/holdRepeat.js", "src/pointerEvents/interactableTargets.js", "src/scope.js", "src/utils/Signals.js", "src/utils/arr.js", "src/utils/browser.js", "src/utils/clone.js", "src/utils/domObjects.js", "src/utils/domUtils.js", "src/utils/events.js", "src/utils/extend.js", "src/utils/getOriginXY.js", "src/utils/hypot.js", "src/utils/index.js", "src/utils/interactionFinder.js", "src/utils/is.js", "src/utils/isWindow.js", "src/utils/pointerExtend.js", "src/utils/pointerUtils.js", "src/utils/raf.js", "src/utils/rect.js", "src/utils/window.js"], "names": [], "mappings": ";AAAA,CCMA,SAAA,GAAA,GAAA,gBAAA,UAAA,mBAAA,QAAA,OAAA,QAAA,QAAA,IAAA,kBAAA,SAAA,OAAA,IAAA,UAAA,OAAA,CAAA,GAAA,EAAA,GAAA,mBAAA,QAAA,OAAA,mBAAA,QAAA,OAAA,mBAAA,MAAA,KAAA,KAAA,EAAA,SAAA,MAAA,WAAA,MAAA,SAAA,GAAA,EAAA,EAAA,GAAA,QAAA,GAAA,EAAA,GAAA,IAAA,EAAA,GAAA,CAAA,IAAA,EAAA,GAAA,CAAA,GAAA,GAAA,kBAAA,UAAA,OAAA,KAAA,GAAA,EAAA,MAAA,GAAA,GAAA,EAAA,IAAA,EAAA,MAAA,GAAA,GAAA,EAAA,IAAA,GAAA,GAAA,OAAA,uBAAA,EAAA,IAAA,MAAA,GAAA,KAAA,mBAAA,EAAA,GAAA,GAAA,EAAA,IAAA,WAAA,GAAA,GAAA,GAAA,KAAA,EAAA,QAAA,SAAA,GAAA,GAAA,GAAA,EAAA,GAAA,GAAA,EAAA,OAAA,GAAA,GAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,GAAA,MAAA,GAAA,GAAA,QAAA,IAAA,GAAA,GAAA,kBAAA,UAAA,QAAA,EAAA,EAAA,EAAA,EAAA,OAAA,IAAA,EAAA,EAAA,GAAA,OAAA,KAAA,GAAA,SAAA,EAAA,EAAA,GACA,YCCsB,oBAAX,QACT,EAAO,QAAU,SAAU,GAGzB,MAFA,GAAQ,sBAAsB,KAAK,GAE5B,EAAQ,gBAIjB,EAAO,QAAU,EAAQ,0LCd3B,QAAS,GAA2B,EAAO,GACzC,IAAA,GAAA,GAAA,EAAA,EAAuB,EAAvB,OAAA,IAAkC,CAAA,GAAA,EAAA,GAAX,EAAW,EAAA,IAAvB,GAAuB,CAChC,IAAI,EAAM,4BAA+B,KAEzC,GAAS,IANb,GAAM,GAAS,EAAQ,qBAUjB,aAEJ,QAAA,GAAa,GAAS,EAAA,KAAA,GACpB,KAAK,QAAU,KAAW,0BAG5B,cAAM,GACJ,GAAI,OAAA,GACE,EAAU,KAAO,EAAM,KACvB,EAAS,KAAK,QAGf,EAAY,KAAK,EAAM,QAC1B,EAA0B,EAAO,GAI/B,KAAK,IACP,KAAK,GAAS,IAIX,EAAM,oBAAsB,IAAW,EAAY,EAAO,EAAM,QACnE,EAA0B,EAAO,gBAIrC,YAAI,EAAW,GAET,KAAK,GACP,KAAK,GAAW,KAAK,GAGrB,KAAK,IAAc,gBAIvB,aAAK,EAAW,GAEd,GAAM,GAAY,KAAK,GACjB,EAAY,EAAW,EAAU,QAAQ,IAAa,GAE7C,IAAX,GACF,EAAU,OAAO,EAAO,IAGtB,GAAkC,IAArB,EAAU,SAAiB,KAC1C,KAAK,OAAa,SAKxB,GAAO,QAAU,2JC9DjB,GAAM,GAAc,EAAQ,kBACtB,EAAc,EAAQ,uBACtB,EAAc,EAAQ,oBACtB,EAAc,EAAQ,mBAAmB,MAEzC,aAEJ,QAAA,GAAa,EAAa,EAAO,EAAQ,EAAO,EAAS,GAAyB,GAAhB,GAAgB,UAAA,OAAA,OAAA,KAAA,UAAA,IAAA,UAAA,EAAA,GAAA,KAAA,EAChF,IAAM,GAAc,EAAY,OAC1B,GAAe,GAAU,EAAO,SAAW,GAAU,YACrD,EAAc,EAAY,EAAQ,EAAS,GAC3C,EAAwB,UAAV,EACd,EAAwB,QAAV,EACd,EAAc,EAAU,EAAY,YAAc,EAAY,UAC9D,EAAc,EAAY,SAEhC,GAAU,GAAW,EAAY,OAEjC,IAAM,GAAS,KAAW,EAAO,MAC3B,EAAS,KAAW,EAAO,OAEjC,GAAK,GAAK,EAAO,EACjB,EAAK,GAAK,EAAO,EAEjB,EAAO,GAAK,EAAO,EACnB,EAAO,GAAK,EAAO,EAEnB,KAAK,QAAgB,EAAM,QAC3B,KAAK,OAAgB,EAAM,OAC3B,KAAK,SAAgB,EAAM,SAC3B,KAAK,QAAgB,EAAM,QAC3B,KAAK,OAAgB,EAAM,OAC3B,KAAK,QAAgB,EAAM,QAC3B,KAAK,OAAgB,EACrB,KAAK,cAAgB,EACrB,KAAK,cAAgB,GAAW,KAChC,KAAK,OAAgB,EACrB,KAAK,KAAgB,GAAU,GAAS,IACxC,KAAK,YAAgB,EACrB,KAAK,aAAgB,EAErB,KAAK,GAAK,EAAW,EAAY,UAAU,EAAY,UAAU,OAAS,GACrD,EAAU,EAE/B,IAAM,IACJ,YAAA,EACA,MAAA,EACA,OAAA,EACA,MAAA,EACA,QAAA,EACA,QAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,SAAA,EACA,OAAA,EACA,YAAA,EACA,OAAQ,KAGV,GAAQ,KAAK,SAAU,GAEnB,GAEF,KAAK,MAAQ,EAAU,MACvB,KAAK,MAAQ,EAAU,MACvB,KAAK,QAAU,EAAU,QACzB,KAAK,QAAU,EAAU,UAGzB,KAAK,MAAY,EAAK,EACtB,KAAK,MAAY,EAAK,EACtB,KAAK,QAAY,EAAO,EACxB,KAAK,QAAY,EAAO,GAG1B,KAAK,GAAY,EAAY,YAAY,KAAK,EAAI,EAAO,EACzD,KAAK,GAAY,EAAY,YAAY,KAAK,EAAI,EAAO,EACzD,KAAK,SAAY,EAAY,YAAY,OAAO,EAAI,EAAO,EAC3D,KAAK,SAAY,EAAY,YAAY,OAAO,EAAI,EAAO,EAE3D,EAAQ,KAAK,YAAa,GAE1B,KAAK,UAAY,EAAO,UACxB,KAAK,GAAY,EAAY,aAAa,UAC1C,KAAK,SAAY,KAAK,UAAY,KAAK,GAGvC,KAAK,MAAQ,EAAY,aAAa,GAAa,MACnD,KAAK,UAAY,EAAY,aAAa,GAAa,GACvD,KAAK,UAAY,EAAY,aAAa,GAAa,GAEvD,KAAK,MAAS,GAAoB,iBAAV,EAA2B,KAAK,WAAa,KAErE,EAAQ,KAAK,MAAO,sBAGtB,oBACE,GAAM,GAAc,KAAK,WAEzB,IAAI,EAAY,UAAU,MAAQ,KAC3B,KAAK,UAAY,EAAY,UAAU,UAAY,IACxD,MAAO,KAGT,IAAI,GAAQ,IAAM,KAAK,MAAM,EAAY,UAAU,UAAW,EAAY,UAAU,WAAa,KAAK,EAGlG,GAAQ,IACV,GAAS,IAGX,IAAM,GAAO,OAAiB,GAAS,EAAQ,MACzC,EAAO,OAAiB,GAAS,EAAQ,MAEzC,GAAS,IAAS,OAAiB,GAAS,EAAS,KAG3D,QACE,GAAA,EACA,MAJa,GAAU,MAAgB,GAAS,EAAQ,MAKxD,KAAA,EACA,MAAA,EACA,MAAA,EACA,MAAO,EAAY,UAAU,MAC7B,UACE,EAAG,EAAY,UAAU,UACzB,EAAG,EAAY,UAAU,yBAK/B,wCAGA,oCACE,KAAK,4BAA8B,KAAK,oBAAqB,eAI/D,2BACE,KAAK,oBAAqB,OAI9B,GAAQ,GAAG,YAAa,SAAA,GAA0D,GAA9C,GAA8C,EAA9C,OAAQ,EAAsC,EAAtC,YAAa,EAAyB,EAAzB,SAAU,EAAe,EAAf,YAC3D,EAAY,EAAU,EAAS,EAAY,SAE7B,YAAhB,GACF,EAAO,GAAK,EAAO,QAAU,EAAU,QACvC,EAAO,GAAK,EAAO,QAAU,EAAU,UAGvC,EAAO,GAAK,EAAO,MAAQ,EAAU,MACrC,EAAO,GAAK,EAAO,MAAQ,EAAU,SAIzC,EAAc,QAAU,EAExB,EAAO,QAAU,4NChKjB,GAAM,GAAY,EAAQ,iBACpB,EAAY,EAAQ,cACpB,EAAY,EAAQ,kBACpB,EAAY,EAAQ,kBACpB,EAAY,EAAQ,kBACpB,EAAY,EAAQ,WACpB,EAAY,EAAQ,eACpB,EAAY,EAAQ,oBACpB,EAAY,EAAQ,mBAAmB,QAOtB,EAAQ,oBAJ7B,IAAA,eACA,IAAA,aACA,IAAA,YACA,IAAA,kBAEqB,EAAQ,kBAAvB,IAAA,YACe,EAAQ,eAAvB,IAAA,WACe,EAAQ,mBAAvB,IAAA,UAGR,GAAM,oBAEA,cAEJ,QAAA,GAAa,EAAQ,GAAS,EAAA,KAAA,GAC5B,EAAU,MAEV,KAAK,OAAW,EAChB,KAAK,OAAW,GAAI,GACpB,KAAK,SAAW,EAAQ,SAAW,EAAM,SACzC,KAAK,KAAW,EAAU,EAAY,GAAS,KAAK,SAAW,GAC/D,KAAK,KAAW,KAAK,KAAK,SAE1B,EAAQ,KAAK,OACX,OAAA,EACA,QAAA,EACA,aAAc,KACd,IAAK,KAAK,OAGZ,EAAM,YAAa,KAAK,KAAM,KAAK,MAEnC,EAAM,cAAc,KAAK,MAEzB,KAAK,IAAI,sBAGX,qBAAa,EAAQ,GACnB,GAAM,GAAW,KAAO,CAOxB,OALI,GAAG,SAAS,EAAO,WAAmB,KAAK,OAAO,EAAW,SAAmB,EAAO,SACvF,EAAG,SAAS,EAAO,UAAmB,KAAK,OAAO,EAAW,QAAmB,EAAO,QACvF,EAAG,SAAS,EAAO,SAAmB,KAAK,OAAO,EAAW,OAAmB,EAAO,OACvF,EAAG,SAAS,EAAO,kBAAmB,KAAK,OAAO,EAAW,gBAAmB,EAAO,gBAEpF,kBAGT,sBAAc,EAAQ,GAEpB,IAAK,GAAM,KAAU,GAEf,IAAU,GAAS,KAEjB,EAAG,OAAO,EAAQ,KAEpB,KAAK,QAAQ,GAAQ,GAAU,EAAM,KAAK,QAAQ,GAAQ,QAC1D,EAAO,KAAK,QAAQ,GAAQ,GAAS,EAAQ,IAEzC,EAAG,OAAO,EAAS,UAAU,KAAY,WAAa,GAAS,UAAU,KAC3E,KAAK,QAAQ,GAAQ,GAAQ,SAAsC,IAA5B,EAAQ,GAAQ,UAGlD,EAAG,KAAK,EAAQ,KAAY,EAAG,OAAO,EAAS,UAAU,IAChE,KAAK,QAAQ,GAAQ,GAAQ,QAAU,EAAQ,OAEpB,KAApB,EAAQ,KAEf,KAAK,QAAQ,GAAQ,GAAU,EAAQ,kBAa/C,iBAAS,GAOP,MANA,GAAU,GAAW,KAAK,OAEtB,EAAG,OAAO,KAAK,UAAa,EAAG,QAAQ,KACzC,EAAU,KAAK,SAAS,cAAc,KAAK,SAGtC,EAAe,gBAWxB,qBAAa,GACX,MAAI,GAAG,SAAS,IACd,KAAK,QAAU,EAER,MAGO,OAAZ,SACK,MAAK,QAAQ,QAEb,MAGF,KAAK,qBAGd,2BAAmB,EAAY,GAC7B,GAAI,EAAY,IAAa,EAAG,OAAO,GAAW,CAChD,KAAK,QAAQ,GAAc,CAE3B,KAAA,GAAA,GAAA,EAAA,EAAqB,EAAQ,MAA7B,OAAA,IAAoC,CAAA,GAAA,EAAA,GAAf,EAAQ,MAAO,EAAA,IAAzB,GAAyB,CAClC,MAAK,QAAQ,GAAQ,GAAc,EAGrC,MAAO,MAGT,MAAO,MAAK,QAAQ,gBAatB,gBAAQ,GACN,MAAO,MAAK,kBAAkB,SAAU,gBAW1C,qBAAa,GACX,MAAiB,SAAb,GAAoC,WAAb,GACzB,KAAK,QAAQ,YAAc,EAEpB,MAGF,KAAK,QAAQ,yBAStB,mBACE,MAAO,MAAK,sBAGd,mBAAW,GACT,MAAQ,MAAK,WAAa,EAAQ,eACvB,EAAa,KAAK,SAAU,gBAWzC,cAAM,GAGJ,MAFA,MAAK,OAAO,KAAK,GAEV,kBAGT,wBAAgB,EAAQ,EAAW,EAAU,GAK3C,GAJI,EAAG,OAAO,KAAyC,IAA3B,EAAU,OAAO,OAC3C,EAAY,EAAU,OAAO,MAAM,OAGjC,EAAG,MAAM,GAAY,CACvB,IAAA,GAAA,GAAA,EAAA,EAAmB,EAAnB,OAAA,IAA8B,CAAA,GAAA,EAAA,GAAX,EAAW,EAAA,IAAnB,GAAmB,CAC5B,MAAK,GAAQ,EAAM,EAAU,GAG/B,OAAO,EAGT,GAAI,EAAG,OAAO,GAAY,CACxB,IAAK,GAAM,KAAQ,GACjB,KAAK,GAAQ,EAAM,EAAU,GAAO,EAGtC,QAAO,gBAcX,YAAI,EAAW,EAAU,GACvB,MAAI,MAAK,eAAe,KAAM,EAAW,EAAU,GAC1C,MAGS,UAAd,IAAyB,EAAY,GAErC,EAAS,EAAa,WAAY,GACpC,KAAK,OAAO,GAAG,EAAW,GAGnB,EAAG,OAAO,KAAK,QACtB,EAAO,YAAY,KAAK,OAAQ,KAAK,SAAU,EAAW,EAAU,GAGpE,EAAO,IAAI,KAAK,OAAQ,EAAW,EAAU,GAGxC,mBAaT,aAAK,EAAW,EAAU,GACxB,MAAI,MAAK,eAAe,MAAO,EAAW,EAAU,GAC3C,MAGS,UAAd,IAAyB,EAAY,GAGrC,EAAS,EAAa,WAAY,GACpC,KAAK,OAAO,IAAI,EAAW,GAGpB,EAAG,OAAO,KAAK,QACtB,EAAO,eAAe,KAAK,OAAQ,KAAK,SAAU,EAAW,EAAU,GAIvE,EAAO,OAAO,KAAK,OAAQ,EAAW,EAAU,GAG3C,mBAST,aAAK,GACE,EAAG,OAAO,KACb,MAGF,KAAK,QAAU,EAAM,EAAS,KAE9B,IAAM,GAAa,EAAM,EAAS,UAElC,KAAK,GAAM,KAAc,GAAQ,WAAY,CAC3C,GAAM,GAAa,EAAQ,WAAW,EAEtC,MAAK,QAAQ,GAAc,EAAM,EAAS,IAE1C,KAAK,aAAa,EAAY,GAE9B,KAAK,GAAY,EAAQ,IAG3B,IAAA,GAAA,GAAA,EAAA,EAAsB,EAAa,gBAAnC,OAAA,IAAoD,CAAA,GAAA,EAAA,GAA9B,EAAa,gBAAiB,EAAA,IAAzC,GAAyC,CAClD,MAAK,QAAQ,GAAW,EAAS,KAAK,GAElC,IAAW,IACb,KAAK,GAAS,EAAQ,IAS1B,MALA,GAAQ,KAAK,OACX,QAAA,EACA,aAAc,OAGT,kBAST,iBAGE,GAFA,EAAO,OAAO,KAAK,OAAQ,OAEvB,EAAG,OAAO,KAAK,QAEjB,IAAK,GAAM,KAAQ,GAAO,gBAAiB,CACzC,GAAM,GAAY,EAAO,gBAAgB,EAErC,GAAU,UAAU,KAAO,KAAK,QAC7B,EAAU,SAAS,KAAO,KAAK,WAEpC,EAAU,UAAU,OAAO,EAAG,GAC9B,EAAU,SAAU,OAAO,EAAG,GAC9B,EAAU,UAAU,OAAO,EAAG,GAGzB,EAAU,UAAU,SACvB,EAAU,GAAQ,OAItB,EAAO,OAAO,KAAK,SAAU,EAAM,EAAO,kBAC1C,EAAO,OAAO,KAAK,SAAU,EAAM,EAAO,oBAAoB,OAIhE,GAAO,OAAO,KAAM,MAGtB,GAAQ,KAAK,SAAW,aAAc,OAEtC,EAAM,cAAc,OAAO,EAAM,cAAc,QAAQ,MAAO,EAG9D,KAAA,GAAA,GAAA,EAAA,GAA0B,EAAM,kBAAhC,OAAA,IAAoD,CAAA,GAAA,EAAA,IAA1B,EAAM,kBAAoB,EAAA,IAAzC,GAAyC,CAC9C,GAAY,SAAW,MAAQ,EAAY,gBAAkB,EAAY,SAC3E,EAAY,OAIhB,MAAO,GAAM,cAIjB,GAAM,cAAc,eAAiB,SAAyB,EAAQ,GACpE,EAAU,GAAW,EAAM,QAE3B,KAAK,GAAI,GAAI,EAAG,EAAI,KAAK,OAAQ,IAAK,CACpC,GAAM,GAAe,KAAK,EAE1B,IAAI,EAAa,SAAW,GAAU,EAAa,WAAa,EAC9D,MAAO,GAGX,OAAQ,GAGV,EAAM,cAAc,IAAM,SAA0B,EAAS,EAAS,GACpE,GAAM,GAAM,KAAK,KAAK,eAAe,EAAS,GAAW,EAAQ,SAEjE,OAAO,KAAQ,EAAG,OAAO,IAAY,GAAsB,EAAI,UAAU,IAAW,EAAM,MAG5F,EAAM,cAAc,aAAe,SAAU,EAAS,GACpD,IAAA,GAAA,GAAA,EAAA,EAA2B,KAA3B,OAAA,IAAiC,CAAA,GAAA,EAAA,GAAN,KAAM,EAAA,IAAtB,GAAsB,EAC3B,MAAA,EAYJ,KAVK,EAAG,OAAO,EAAa,QAErB,EAAG,QAAQ,IAAY,EAAgB,EAAS,EAAa,QAE9D,IAAY,EAAa,SAEzB,EAAa,UAAU,KAC3B,EAAM,EAAS,QAGL,KAAR,EACF,MAAO,KAMb,EAAa,WAAa,EAAM,cAEhC,EAAa,QAAU,EAEvB,EAAa,iBAAoB,cAAe,SAAU,iBAAkB,eAE5E,EAAO,QAAU,0XC5CjB,QAAS,GAAkB,GACzB,MAAQ,UAAU,GAChB,GAAM,GAAc,EAAM,eAAe,GADlB,EAEe,EAAM,gBAAgB,GAArD,EAFgB,EAAA,GAEH,EAFG,EAAA,GAGjB,IAEN,IAAI,EAAQ,eAAiB,QAAQ,KAAK,EAAM,MAAO,CACrD,GAAgB,GAAI,OAAO,SAE3B,KAAA,GAAA,GAAA,EAAA,EAA2B,EAAM,eAAjC,OAAA,IAAiD,CAAA,GAAA,EAAA,GAAtB,EAAM,eAAgB,EAAA,IAAtC,GAAsC,EACzC,EAAU,EACV,EAAc,EAAO,OAAO,EAAS,EAAM,KAAM,EAEvD,GAAQ,MAAM,EAAS,GAAe,GAAI,IAAc,YAAA,WAGvD,CACH,GAAI,IAAiB,CAErB,KAAK,EAAQ,sBAAwB,QAAQ,KAAK,EAAM,MAAO,CAE7D,IAAK,GAAI,GAAI,EAAG,EAAI,EAAM,aAAa,SAAW,EAAgB,IAChE,EAAuD,UAAtC,EAAM,aAAa,GAAG,aAA2B,EAAM,aAAa,GAAG,aAK1F,GAAiB,IACX,GAAI,OAAO,UAAY,EAAgB,KAEpB,IAApB,EAAM,UAGb,IAAK,EAAgB,CACnB,GAAI,GAAc,EAAO,OAAO,EAAO,EAAM,KAAM,EAE9C,KACH,EAAc,GAAI,IAAc,YAAA,KAGlC,EAAQ,MAAM,EAAO,KAIzB,IAAA,GAAA,GAAA,EAAA,EAAqC,EAArC,OAAA,IAA8C,CAAA,GAAA,GAAT,EAAS,GAAlC,EAAkC,EAAA,GAAzB,EAAyB,EAAA,EAC5C,GAAY,oBAAoB,EAAa,GAC7C,EAAY,GAAQ,EAAS,EAAO,EAAa,KAKvD,QAAS,GAAQ,GACf,IAAA,GAAA,GAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAC5C,GAAY,IAAI,GAChB,EAAQ,KAAK,UAAY,MAAA,EAAO,YAAA,KA0BpC,QAAS,GAAT,EAA+B,GAAY,GAAnB,GAAmB,EAAnB,IAChB,EAA4C,IAA9B,EAAW,QAAQ,OACnC,EAAO,IAAM,EAAO,MAGxB,KAAK,GAAM,KAAa,GAAM,gBAC5B,EAAY,EAAK,EAAW,EAAO,kBACnC,EAAY,EAAK,EAAW,EAAO,oBAAoB,EAGzD,KAAK,GAAM,KAAa,GACtB,EAAY,EAAK,EAAW,EAAU,GAAY,EAAQ,OAAU,SAAS,OAAU,IAvd3F,GAAM,GAAa,EAAQ,WACrB,EAAa,EAAQ,WACrB,EAAa,EAAQ,kBACrB,EAAa,EAAQ,mBACrB,EAAa,EAAQ,sBACrB,EAAa,EAAQ,6BACrB,EAAa,EAAQ,mBAAmB,MAExC,KACA,GACJ,cAAe,cAAe,YAC9B,gBAAiB,iBAIf,EAAgB,CAGpB,GAAM,eAsWN,KAAA,GApWM,cAEJ,QAAA,GAAA,GAA8B,GAAf,GAAe,EAAf,WAAe,GAAA,KAAA,GAC5B,KAAK,OAAgB,KACrB,KAAK,QAAgB,KAErB,KAAK,UACH,KAAO,KACP,KAAO,KACP,MAAO,MAIT,KAAK,YACL,KAAK,cACL,KAAK,eACL,KAAK,aAGL,KAAK,YACH,MAAa,EAAG,EAAG,EAAG,GACtB,QAAa,EAAG,EAAG,EAAG,GACtB,UAAW,GAGb,KAAK,WACH,MAAa,EAAG,EAAG,EAAG,GACtB,QAAa,EAAG,EAAG,EAAG,GACtB,UAAW,GAIb,KAAK,aACH,MAAa,EAAG,EAAG,EAAG,GACtB,QAAa,EAAG,EAAG,EAAG,GACtB,UAAW,GAIb,KAAK,cACH,MAAa,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,MAAO,GAC9C,QAAa,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,MAAO,GAC9C,UAAW,GAGb,KAAK,UAAc,KACnB,KAAK,eAEL,KAAK,aAAkB,KACvB,KAAK,gBAAkB,KAEvB,KAAK,UAAY,KAEjB,KAAK,eAAkB,EACvB,KAAK,iBAAkB,EACvB,KAAK,cAAkB,EACvB,KAAK,SAAkB,EAEvB,KAAK,YAAc,EAEnB,EAAQ,KAAK,MAAO,MAEpB,EAAM,aAAa,KAAK,yBAG1B,qBAAa,EAAS,EAAO,GAC3B,GAAM,GAAe,KAAK,cAAc,EAAS,GAAO,EAExD,GAAQ,KAAK,QACX,QAAA,EACA,MAAA,EACA,YAAA,EACA,aAAA,EACA,YAAa,oBAmCjB,eAAO,EAAQ,EAAQ,GACjB,KAAK,gBACD,KAAK,eACN,KAAK,WAAW,QAA0B,YAAhB,EAAO,KAAoB,EAAI,MAMtB,IAAtC,EAAM,aAAa,QAAQ,OAC7B,EAAM,aAAa,KAAK,MAG1B,EAAM,WAAW,KAAK,SAAU,GAChC,KAAK,OAAiB,EACtB,KAAK,QAAiB,EAEtB,EAAQ,KAAK,gBACX,YAAa,KACb,MAAO,KAAK,0BAIhB,qBAAa,EAAS,EAAO,GACtB,KAAK,aACR,KAAK,cAAc,GACnB,EAAM,UAAU,KAAK,UAAW,KAAK,UAGvC,IAAM,GAAiB,KAAK,UAAU,KAAK,IAAM,KAAK,WAAW,KAAK,GAC5C,KAAK,UAAU,KAAK,IAAM,KAAK,WAAW,KAAK,GAC/C,KAAK,UAAU,OAAO,IAAM,KAAK,WAAW,OAAO,GACnD,KAAK,UAAU,OAAO,IAAM,KAAK,WAAW,OAAO,EAEzE,MAAA,GACA,MAAA,EAGA,MAAK,gBAAkB,KAAK,kBAC9B,EAAK,KAAK,UAAU,OAAO,EAAI,KAAK,YAAY,OAAO,EACvD,EAAK,KAAK,UAAU,OAAO,EAAI,KAAK,YAAY,OAAO,EAEvD,KAAK,gBAAkB,EAAM,MAAM,EAAI,GAAM,EAAY,qBAG3D,IAAM,IACJ,QAAA,EACA,aAAc,KAAK,gBAAgB,GACnC,MAAA,EACA,YAAA,EACA,GAAA,EACA,GAAA,EACA,UAAW,EACX,YAAa,KACb,sBAAuB,KAAK,cAGzB,IAEH,EAAM,eAAe,KAAK,aAAc,KAAK,WAAY,KAAK,WAGhE,EAAQ,KAAK,OAAQ,GAEhB,IAEC,KAAK,eACP,KAAK,OAAO,GAGV,KAAK,iBACP,EAAM,WAAW,KAAK,WAAY,KAAK,yBAuB7C,gBAAQ,GACN,EAAY,EAAM,QAChB,QAAS,KAAK,SAAS,GACvB,MAAO,KAAK,UACZ,YAAa,KAAK,aAClB,YAAa,MACZ,OAEH,EAAQ,KAAK,qBAAsB,GAE9B,KAAK,eACR,EAAQ,KAAK,cAAe,GAG9B,KAAK,eAAgB,eAIvB,mBAAW,EAAS,EAAO,EAAa,GACtC,GAAM,GAAe,KAAK,gBAAgB,EAE1C,GAAQ,KAAK,WAAW,KAAK,EAAM,MAAO,SAAW,MACnD,QAAA,EACA,aAAA,EACA,MAAA,EACA,YAAA,EACA,eAAA,EACA,YAAa,OAGV,KAAK,YACR,KAAK,IAAI,GAGX,KAAK,eAAgB,EACrB,KAAK,cAAc,EAAS,gBAsB9B,aAAK,GACH,KAAK,SAAU,EAEf,EAAQ,GAAS,KAAK,UAElB,KAAK,eACP,EAAQ,KAAK,cACX,MAAA,EACA,YAAa,OAIjB,KAAK,OACL,KAAK,SAAU,eAGjB,yBACE,MAAO,MAAK,aAAc,KAAK,SAAS,KAAM,kBAGhD,uBACE,MAAO,MAAK,0BAId,gBACE,EAAQ,KAAK,QAAU,YAAa,OAEhC,KAAK,eACP,EAAQ,KAAK,eAAiB,YAAa,OAC3C,EAAQ,KAAK,QAAU,KAAK,SAAS,MAAQ,YAAa,QAG5D,KAAK,OAAS,KAAK,QAAU,KAE7B,KAAK,cAAe,EACpB,KAAK,SAAS,KAAO,KAAK,UAAY,kBAGxC,yBAAiB,GAEf,MAAyB,UAArB,KAAK,aAAgD,QAArB,KAAK,YAChC,EAGF,KAAK,WAAW,QAAQ,EAAM,aAAa,iBAGpD,uBAAe,EAAS,GAA0D,GAAnD,GAAmD,UAAA,OAAA,OAAA,KAAA,UAAA,GAAA,UAAA,GAA5C,GAAS,iBAAiB,KAAK,EAAM,MACnE,EAAK,EAAM,aAAa,GAC1B,EAAQ,KAAK,gBAAgB,EAoBjC,QAlBe,IAAX,IACF,EAAQ,KAAK,WAAW,OACxB,KAAK,WAAW,GAAS,GAGvB,GACF,EAAQ,KAAK,uBACX,QAAA,EACA,MAAA,EACA,KAAA,EACA,UAAW,EACX,aAAc,EACd,YAAa,OAIjB,KAAK,SAAS,GAAS,EAEhB,eAGT,uBAAe,EAAS,GACtB,GAAM,GAAQ,KAAK,gBAAgB,IAEpB,IAAX,IAEJ,EAAQ,KAAK,kBACX,QAAA,EACA,MAAA,EACA,aAAc,EACd,YAAa,OAGf,KAAK,SAAY,OAAO,EAAO,GAC/B,KAAK,WAAY,OAAO,EAAO,GAC/B,KAAK,YAAY,OAAO,EAAO,GAC/B,KAAK,UAAY,OAAO,EAAO,iBAGjC,6BAAqB,EAAQ,GAC3B,KAAK,aAAkB,EACvB,KAAK,gBAAkB,QAI3B,EAAA,EAAA,EAAqB,EAArB,OAAA,IAAkC,CAA7B,GAAM,GAAU,EAAV,EACT,GAAU,GAAU,EAAiB,GA6DvC,GAAM,MACA,EAAc,EAAQ,WAExB,GAAW,cACb,EAAU,EAAY,MAAU,EAAU,YAC1C,EAAU,EAAY,MAAU,EAAU,YAC1C,EAAU,EAAY,IAAU,EAAU,UAC1C,EAAU,EAAY,QAAU,EAAU,YAG1C,EAAU,UAAc,EAAU,YAClC,EAAU,UAAc,EAAU,YAClC,EAAU,QAAc,EAAU,UAElC,EAAU,WAAc,EAAU,YAClC,EAAU,UAAc,EAAU,YAClC,EAAU,SAAc,EAAU,UAClC,EAAU,YAAc,EAAU,WAGpC,EAAU,KAAO,EAiBjB,EAAQ,GAAG,sBAAuB,SAAA,GAAiF,GAA9E,GAA8E,EAA9E,YAAa,EAAiE,EAAjE,QAAS,EAAwD,EAAxD,UAAW,EAA6C,EAA7C,aAAc,EAA+B,EAA/B,MAAO,EAAwB,EAAxB,YAAa,EAAW,EAAX,IACtG,GAAY,WAAW,GAAgB,EACvC,EAAY,SAAS,GAAgB,EAEjC,IACF,EAAY,eAAgB,GAGzB,EAAY,gBACf,EAAM,UAAU,EAAY,YAAa,EAAY,UAErD,EAAM,WAAW,EAAY,UAAY,EAAY,aACrD,EAAM,WAAW,EAAY,WAAY,EAAY,aAErD,EAAY,UAA4B,EACxC,EAAY,UAAU,GAAkB,EAAY,UAAU,UAC9D,EAAY,YAAY,GAAgB,GAAe,GAAS,EAAM,gBAAgB,GAAO,GAC7F,EAAY,iBAA4B,EAExC,EAAM,cAAc,EAAY,YAAa,MAIjD,EAAM,QAAQ,GAAG,eAAmB,GACpC,EAAM,QAAQ,GAAG,kBAAmB,GAEpC,EAAY,qBAAuB,EACnC,EAAY,iBAAmB,EAC/B,EAAY,OAAS,EACrB,EAAY,QAAU,EACtB,EAAY,UAAY,EAExB,EAAM,mBAAqB,EAE3B,EAAO,QAAU,oLCpejB,SAAS,GAAc,EAAa,EAAO,EAAO,GAChD,GAAM,GAAa,EAAY,SAAS,KAElC,EAAW,GAAI,GAAc,EAAa,EAAO,EAAY,EAAO,EAAY,QAAS,KAAM,EAErG,GAAY,OAAO,KAAK,GACxB,EAAY,UAAY,EA/B1B,GAAM,GAAgB,EAAQ,kBACxB,EAAgB,EAAQ,oBAExB,GACJ,aAAA,EACA,SACA,cAGF,GAAY,QAAQ,GAAG,eAAgB,SAAA,GAAkC,GAAtB,GAAsB,EAAtB,YAAa,EAAS,EAAT,KAC9D,GAAY,cAAe,EAC3B,EAAa,EAAa,EAAO,WAGnC,EAAY,QAAQ,GAAG,cAAe,SAAA,GAA0C,GAA9B,GAA8B,EAA9B,WAIhD,IAHA,EAAa,EADiE,EAAjB,MAC5B,OAD6C,EAAV,SAI/D,EAAY,cAAiB,OAAO,IAG3C,EAAY,QAAQ,GAAG,aAAc,SAAA,GACnC,EADqE,EAAtB,YAAsB,EAAT,MAC3B,SAYnC,EAAO,QAAU,6EClCjB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,oBAEzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,kBACzB,EAAiB,EAAQ,qBAEzB,GACJ,UACE,SAAc,EACd,aAAc,KAEd,OAAY,KACZ,KAAY,KACZ,SAAY,KACZ,QAAY,KACZ,WAAY,KAEZ,UAAY,KACZ,SAAY,MAGd,QAAS,SAAU,EAAS,EAAO,GACjC,GAAM,GAAc,EAAa,QAAQ,IAEzC,OAAO,GAAY,SACb,KAAM,OAAQ,KAAgC,UAAzB,EAAY,SACV,EAAY,UACZ,EAAY,UACrC,MAGN,UAAW,WACT,MAAO,QAIX,GAAY,QAAQ,GAAG,qBAAsB,SAAA,GAA2B,GAAf,GAAe,EAAf,WACvD,IAAkC,SAA9B,EAAY,SAAS,KAAzB,CAEA,GAAM,GAAO,EAAY,SAAS,IAErB,OAAT,GACF,EAAY,UAAU,KAAK,EAAM,EAAY,YAAY,KAAK,EAC9D,EAAY,UAAU,OAAO,EAAI,EAAY,YAAY,OAAO,EAEhE,EAAY,aAAa,KAAK,MAAU,KAAK,IAAI,EAAY,aAAa,KAAK,IAC/E,EAAY,aAAa,OAAO,MAAQ,KAAK,IAAI,EAAY,aAAa,OAAO,IACjF,EAAY,aAAa,OAAO,GAAK,EACrC,EAAY,aAAa,KAAK,GAAO,GAErB,MAAT,IACP,EAAY,UAAU,KAAK,EAAM,EAAY,YAAY,KAAK,EAC9D,EAAY,UAAU,OAAO,EAAI,EAAY,YAAY,OAAO,EAEhE,EAAY,aAAa,KAAK,MAAU,KAAK,IAAI,EAAY,aAAa,KAAK,IAC/E,EAAY,aAAa,OAAO,MAAQ,KAAK,IAAI,EAAY,aAAa,OAAO,IACjF,EAAY,aAAa,OAAO,GAAK,EACrC,EAAY,aAAa,KAAK,GAAO,MAKzC,EAAc,QAAQ,GAAG,MAAO,SAAA,GAAmC,GAAvB,GAAuB,EAAvB,OAAQ,EAAe,EAAf,WAClD,IAAoB,aAAhB,EAAO,KAAX,CAEA,GAAM,GAAO,EAAY,SAAS,IAErB,OAAT,GACF,EAAO,MAAU,EAAY,YAAY,KAAK,EAC9C,EAAO,QAAU,EAAY,YAAY,OAAO,EAChD,EAAO,GAAK,GAEI,MAAT,IACP,EAAO,MAAU,EAAY,YAAY,KAAK,EAC9C,EAAO,QAAU,EAAY,YAAY,OAAO,EAChD,EAAO,GAAK,MAyChB,EAAa,UAAU,UAAY,SAAU,GAC3C,MAAI,GAAM,GAAG,OAAO,IAClB,KAAK,QAAQ,KAAK,SAA8B,IAApB,EAAQ,QACpC,KAAK,aAAa,OAAQ,GAC1B,KAAK,YAAY,OAAQ,GAErB,mBAAmB,KAAK,EAAQ,YAClC,KAAK,QAAQ,KAAK,SAAW,EAAQ,UAEnC,aAAa,KAAK,EAAQ,aAC5B,KAAK,QAAQ,KAAK,UAAY,EAAQ,WAGjC,MAGL,EAAM,GAAG,KAAK,IAChB,KAAK,QAAQ,KAAK,QAAU,EAEvB,IACH,KAAK,YAAc,KAAK,YAAc,KAAK,UAAY,MAGlD,MAGF,KAAK,QAAQ,MAGtB,EAAQ,KAAO,EACf,EAAQ,MAAM,KAAK,QACnB,EAAM,MAAM,EAAa,YACvB,YACA,WACA,mBACA,oBACA,YAEF,EAAQ,WAAW,KAAO,YAE1B,EAAe,KAAO,EAAK,SAE3B,EAAO,QAAU,iJClFjB,SAAS,GAAc,EAAa,GAKlC,IAAA,GAJM,MACA,KAGN,EAAA,EAAA,EAAsB,EAAM,cAA5B,OAAA,IAA2C,CAAA,GAAA,EAAA,GAArB,EAAM,cAAe,EAAA,IAAhC,GAAgC,CACzC,IAAK,EAAQ,QAAQ,KAAK,QAA1B,CAEA,GAAM,GAAS,EAAQ,QAAQ,KAAK,MAGpC,MAAK,EAAM,GAAG,QAAQ,IAAW,IAAW,GACpC,EAAM,GAAG,OAAO,KAChB,EAAM,gBAAgB,EAAS,IAUvC,IAAA,GAJM,GAAe,EAAM,GAAG,OAAO,EAAQ,QACzC,EAAQ,SAAS,iBAAiB,EAAQ,SACzC,EAAQ,QAEb,EAAA,EAAA,EAA6B,EAA7B,OAAA,IAA2C,CAAA,GAAA,EAAA,GAAd,EAAc,EAAA,IAAhC,GAAgC,CACrC,KAAmB,IACrB,EAAM,KAAK,GACX,EAAS,KAAK,MAKpB,OACE,SAAA,EACA,UAAW,GAIf,QAAS,GAAiB,EAAa,GAIrC,IAAK,GAHD,OAAA,GAGK,EAAI,EAAG,EAAI,EAAY,UAAU,OAAQ,IAAK,CACrD,GAAM,GAAU,EAAY,UAAU,GAChC,EAAiB,EAAY,SAAU,EAGzC,KAAmB,IAErB,EAAM,OAAS,EACf,EAAQ,KAAK,IAEf,EAAc,GAOlB,QAAS,GAAgB,EAAa,GAEpC,GAAM,GAAgB,EAAa,EAAa,EAEhD,GAAY,UAAY,EAAc,UACtC,EAAY,SAAY,EAAc,SACtC,EAAY,QAEZ,KAAK,GAAI,GAAI,EAAG,EAAI,EAAY,UAAU,OAAQ,IAChD,EAAY,MAAM,GAAK,EAAY,UAAU,GAAG,QAAQ,EAAY,SAAS,IAIjF,QAAS,GAAS,EAAW,EAAO,GAClC,GAAM,GAAc,EAAU,YACxB,IAEF,IACF,EAAe,EAAY,YAAa,EAI1C,KAAK,GAAI,GAAI,EAAG,EAAI,EAAY,YAAY,UAAU,OAAQ,IAAK,CACjE,GAAM,GAAiB,EAAY,YAAY,UAAU,GACnD,EAAiB,EAAY,YAAY,SAAU,GACnD,EAAiB,EAAY,YAAY,MAAU,EAEzD,GAAW,KAAK,EAAQ,UAAU,EAAW,EAAO,EAAY,OAAQ,EAAa,EAAgB,GACjG,EACA,MAIN,GAAM,GAAY,EAAM,sBAAsB,EAE9C,QACE,SAAU,EAAY,YAAY,UAAU,IAAc,KAC1D,QAAU,EAAY,YAAY,SAAU,IAAc,MAI9D,QAAS,GAAe,EAAa,EAAc,GACjD,GAAM,IACJ,MAAY,KACZ,MAAY,KACZ,SAAY,KACZ,WAAY,KACZ,KAAY,KACZ,KAAY,MAGR,GACJ,UAAA,EACA,YAAA,EACA,OAAe,EAAY,YAC3B,SAAe,EAAY,WAC3B,cAAe,EAAU,OACzB,UAAe,EAAU,aACzB,UAAe,EAAU,UAwD3B,OArDI,GAAY,cAAgB,EAAY,kBAEtC,EAAY,iBACd,EAAW,MAAQ,EAAM,QAAS,KAAM,aAAe,GAEvD,EAAU,UAAe,EAAW,MAAM,OAAW,EAAY,gBACjE,EAAU,aAAe,EAAW,MAAM,SAAW,EAAY,gBAG/D,EAAY,aACd,EAAW,OACT,UAAA,EACA,YAAA,EACA,OAAe,EAAY,YAC3B,SAAe,EAAY,WAC3B,cAAe,EAAU,OACzB,UAAe,EAAU,aACzB,UAAe,EAAU,UACzB,KAAe,aAGjB,EAAU,UAAY,EAAY,YAClC,EAAU,SAAW,EAAY,aAId,YAAnB,EAAU,MAAsB,EAAY,aAC9C,EAAW,KAAO,EAAM,QAAS,KAAM,QAAU,GAEjD,EAAU,SAAW,EAAY,WACjC,EAAU,cAAgB,EAAY,aAEjB,cAAnB,EAAU,OACZ,EAAW,SAAW,EAAM,QAAS,KAAM,gBAAkB,GAE7D,EAAW,SAAS,OAAW,KAC/B,EAAW,SAAS,SAAW,MAEV,YAAnB,EAAU,OACZ,EAAW,WAAa,EAAM,QAAS,KAAM,kBAAoB,GAEjE,EAAW,WAAW,OAAW,KACjC,EAAW,WAAW,SAAW,MAEZ,aAAnB,EAAU,MAAuB,EAAY,aAC/C,EAAW,KAAO,EAAM,QACtB,SAAe,EACf,KAAe,YACd,GAEH,EAAU,SAAW,EAAY,YAG5B,EAGT,QAAS,GAAgB,EAAa,GAAY,GAE9C,GAIE,EAJF,YACA,EAGE,EAHF,eACA,EAEE,EAFF,WACA,EACE,EADF,WAGE,GAAW,OAAS,EAAe,KAAK,EAAW,OACnD,EAAW,MAAa,EAAW,KAAK,EAAW,MACnD,EAAW,OAAa,EAAW,KAAK,EAAW,OACnD,EAAW,MAAa,EAAW,KAAK,EAAW,MACnD,EAAW,YACb,EAAgB,EAAa,EAAW,YAG1C,EAAY,eAAkB,EAC9B,EAAY,gBAAkB,EA9QhC,GAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,YAEzB,EAAiB,EAAQ,eACzB,EAAiB,EAAQ,oBAEzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,kBACzB,EAAiB,EAAQ,qBAEzB,GACJ,UACE,SAAS,EACT,OAAS,KACT,QAAS,YAIT,GAAc,CAElB,GAAY,QAAQ,GAAG,eAAgB,SAAA,GAAkC,GAAtB,GAAsB,EAAtB,YAAa,EAAS,EAAT,KAC9D,IAAkC,SAA9B,EAAY,SAAS,KAAzB,CAGA,EAAY,YAAY,aACxB,EAAY,YAAY,YACxB,EAAY,YAAY,SAExB,EAAY,WAAa,KAEpB,EAAY,aACf,EAAe,EAAY,YAAa,EAAY,QAGtD,IAAM,GAAY,EAAY,UACxB,EAAa,EAAc,EAAa,EAAO,EAEjD,GAAW,UACb,EAAgB,EAAY,YAAa,EAAW,aAIxD,EAAc,QAAQ,GAAG,MAAO,SAAA,GAA0C,GAA9B,GAA8B,EAA9B,YAAa,EAAiB,EAAjB,OAAQ,EAAS,EAAT,KAC/D,IAAoB,aAAhB,EAAO,MAAuC,YAAhB,EAAO,KAAzC,CAEA,GAAM,GAAmB,EAAY,QAC/B,EAAY,EACZ,EAAa,EAAQ,EAAW,EAAO,EAE7C,GAAY,WAAc,EAAW,SACrC,EAAY,YAAc,EAAW,QAErC,EAAY,WAAa,EAAc,EAAa,EAAO,MAG7D,EAAY,QAAQ,GAAG,cAAe,SAAA,GAA2B,GAAf,GAAe,EAAf,WACd,UAA9B,EAAY,SAAS,MAEzB,EAAe,EAAa,EAAY,cAG1C,EAAY,QAAQ,GAAG,aAAc,SAAA,GAA2B,GAAf,GAAe,EAAf,WACb,UAA9B,EAAY,SAAS,MACvB,EAAe,EAAa,EAAY,cAI5C,EAAY,QAAQ,GAAG,YAAa,SAAA,GAA2B,GAAf,GAAe,EAAf,WAC9C,GAAY,aACV,UAAW,KACX,SAAU,KACV,MAAO,MAGT,EAAY,WAAa,OA2P3B,EAAa,UAAU,SAAW,SAAU,GAC1C,MAAI,GAAM,GAAG,OAAO,IAClB,KAAK,QAAQ,KAAK,SAA8B,IAApB,EAAQ,QAEhC,EAAM,GAAG,SAAS,EAAQ,UAAqB,KAAK,OAAO,OAAmB,EAAQ,QACtF,EAAM,GAAG,SAAS,EAAQ,kBAAqB,KAAK,OAAO,eAAmB,EAAQ,gBACtF,EAAM,GAAG,SAAS,EAAQ,oBAAqB,KAAK,OAAO,iBAAmB,EAAQ,kBACtF,EAAM,GAAG,SAAS,EAAQ,eAAqB,KAAK,OAAO,YAAmB,EAAQ,aACtF,EAAM,GAAG,SAAS,EAAQ,eAAqB,KAAK,OAAO,YAAmB,EAAQ,aACtF,EAAM,GAAG,SAAS,EAAQ,cAAqB,KAAK,OAAO,WAAmB,EAAQ,YAEtF,qBAAqB,KAAK,EAAQ,SACpC,KAAK,QAAQ,KAAK,QAAU,EAAQ,QAE7B,EAAM,GAAG,OAAO,EAAQ,WAC/B,KAAK,QAAQ,KAAK,QAAU,KAAK,IAAI,KAAK,IAAI,EAAG,EAAQ,SAAU,IAEjE,UAAY,KACd,KAAK,QAAQ,KAAK,OAAS,EAAQ,QAEjC,WAAa,KACf,KAAK,QAAQ,KAAK,QAAU,EAAQ,SAI/B,MAGL,EAAM,GAAG,KAAK,IAChB,KAAK,QAAQ,KAAK,QAAU,EAEvB,IACH,KAAK,YAAc,KAAK,YAAc,KAAK,OACvC,KAAK,eAAiB,KAAK,iBAAmB,MAG7C,MAGF,KAAK,QAAQ,MAGtB,EAAa,UAAU,UAAY,SAAU,EAAW,EAAO,EAAW,EAAkB,EAAa,GACvG,GAAI,IAAU,CAId,MAAM,EAAO,GAAQ,KAAK,QAAQ,IAChC,QAAQ,KAAK,QAAQ,KAAK,SACtB,KAAK,QAAQ,KAAK,QAAQ,EAAW,EAAO,EAAS,KAAM,EAAa,EAAW,EAIzF,IAAM,GAAc,KAAK,QAAQ,KAAK,OAEtC,IAAoB,YAAhB,EAA2B,CAC7B,GAAM,GAAS,EAAM,YAAY,EAAW,EAAkB,QACxD,EAAO,EAAM,UAAU,EAE7B,GAAK,GAAK,EAAO,EACjB,EAAK,GAAK,EAAO,CAEjB,IAAM,GAAc,EAAK,EAAI,EAAK,MAAU,EAAK,EAAI,EAAK,MACpD,EAAc,EAAK,EAAI,EAAK,KAAU,EAAK,EAAI,EAAK,MAE1D,GAAU,GAAc,EAG1B,GAAM,GAAW,EAAU,QAAQ,EAEnC,IAAI,GAA4B,WAAhB,EAA0B,CACxC,GAAM,GAAK,EAAS,KAAO,EAAS,MAAS,EACvC,EAAK,EAAS,IAAO,EAAS,OAAS,CAE7C,GAAU,GAAM,EAAK,MAAQ,GAAM,EAAK,OAAS,GAAM,EAAK,KAAO,GAAM,EAAK,OAGhF,GAAI,GAAY,EAAM,GAAG,OAAO,GAAc,CAM5C,EALsB,KAAK,IAAI,EAAG,KAAK,IAAI,EAAK,MAAQ,EAAS,OAAU,KAAK,IAAI,EAAK,KAAM,EAAS,OAChF,KAAK,IAAI,EAAG,KAAK,IAAI,EAAK,OAAQ,EAAS,QAAU,KAAK,IAAI,EAAK,IAAM,EAAS,OAEtE,EAAS,MAAQ,EAAS,SAEpC,EAO5B,MAJI,MAAK,QAAQ,KAAK,UACpB,EAAU,KAAK,QAAQ,KAAK,QAAQ,EAAW,EAAO,EAAS,KAAM,EAAa,EAAW,IAGxF,GAGT,EAAa,QAAQ,GAAG,QAAS,SAAA,GAA4B,EAAhB,aAC9B,UAAS,KAGxB,EAAa,gBAAgB,KAAK,eAElC,EAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,WAAkB,KAC9B,EAAY,YAAkB,KAC9B,EAAY,eAAkB,KAC9B,EAAY,gBAAkB,KAC9B,EAAY,WAAkB,KAE9B,EAAY,aACV,aACA,YACA,YAKJ,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAA2B,GAAf,GAAe,EAAf,WACzC,GAAY,WAAa,EAAY,YACnC,EAAY,eAAiB,EAAY,gBAAkB,OAW/D,EAAS,YAAc,SAAU,GAC/B,MAAI,GAAM,GAAG,KAAK,IAKhB,EAAc,EAEP,GAEF,GAGT,EAAM,MAAM,EAAa,YACvB,YACA,YACA,eACA,iBACA,WACA,SAEF,EAAQ,WAAW,KAAO,WAE1B,EAAe,KAAO,EAAK,SAE3B,EAAO,QAAU,gLC9djB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,oBACzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,kBACzB,EAAiB,EAAQ,qBAEzB,GACJ,UACE,SAAU,EACV,OAAU,KACV,SAAU,MAGZ,QAAS,SAAU,EAAS,EAAO,EAAc,EAAS,GACxD,MAAI,GAAY,WAAW,QAAU,GAC1B,KAAM,WAGV,MAGT,UAAW,WACT,MAAO,IAIX,GAAc,QAAQ,GAAG,MAAO,SAAA,GAAmC,GAAvB,GAAuB,EAAvB,OAAQ,EAAe,EAAf,WAC9B,kBAAhB,EAAO,OACX,EAAO,GAAK,EAEZ,EAAY,QAAQ,cAAgB,EAAY,QAAQ,aAAe,EAAO,SAC9E,EAAY,QAAQ,WAAa,EAAY,QAAQ,UAAY,EAAO,MACxE,EAAY,QAAQ,MAAQ,KAG9B,EAAc,QAAQ,GAAG,MAAO,SAAA,GAAmC,GAAvB,GAAuB,EAAvB,OAAQ,EAAe,EAAf,WAC9B,iBAAhB,EAAO,OAEX,EAAO,GAAK,EAAO,MAAQ,EAAY,QAAQ,MAE/C,EAAY,OAAO,KAAK,GAExB,EAAY,QAAQ,UAAY,EAAO,MACvC,EAAY,QAAQ,aAAe,EAAO,SAEtC,EAAO,QAAU,EAAA,GACG,OAAjB,EAAO,WACU,KAAjB,EAAO,OACN,MAAM,EAAO,SAEnB,EAAY,QAAQ,MAAQ,EAAO,UA2BvC,EAAa,UAAU,WAAa,SAAU,GAC5C,MAAI,GAAM,GAAG,OAAO,IAClB,KAAK,QAAQ,QAAQ,SAA8B,IAApB,EAAQ,QACvC,KAAK,aAAa,UAAW,GAC7B,KAAK,YAAY,UAAW,GAErB,MAGL,EAAM,GAAG,KAAK,IAChB,KAAK,QAAQ,QAAQ,QAAU,EAE1B,IACH,KAAK,eAAiB,KAAK,eAAiB,KAAK,aAAe,MAG3D,MAGF,KAAK,QAAQ,SAGtB,EAAc,QAAQ,GAAG,YAAa,SAAA,GAAiF,GAArE,GAAqE,EAArE,YAAa,EAAwD,EAAxD,OAAQ,EAAgD,EAAhD,OAAQ,EAAwC,EAAxC,MAAO,EAAiC,EAAjC,SAAU,EAAuB,EAAvB,OAAQ,EAAe,EAAf,WACtG,IAAe,YAAX,EAAJ,CAEA,GAAM,GAAW,EAAY,QAE7B,GAAO,SAAW,EAAS,GAAI,EAAS,IAEpC,GACF,EAAO,SAAW,EAAM,cAAc,EAAU,GAChD,EAAO,IAAW,EAAM,UAAU,GAClC,EAAO,MAAW,EAClB,EAAO,GAAW,EAClB,EAAO,MAAW,EAAM,WAAW,MAAU,GAAW,GACxD,EAAO,GAAW,GAEX,GAAU,YAAiB,IAClC,EAAO,SAAW,EAAY,UAAU,SACxC,EAAO,IAAW,EAAY,UAAU,IACxC,EAAO,MAAW,EAAY,UAAU,MACxC,EAAO,GAAW,EAAO,MAAQ,EACjC,EAAO,MAAW,EAAY,UAAU,MACxC,EAAO,GAAW,EAAO,MAAQ,EAAY,QAAQ,aAGrD,EAAO,SAAW,EAAM,cAAc,EAAU,GAChD,EAAO,IAAW,EAAM,UAAU,GAClC,EAAO,MAAW,EAAO,SAAW,EAAY,QAAQ,cACxD,EAAO,MAAW,EAAM,WAAW,EAAU,EAAY,QAAQ,UAAW,GAE5E,EAAO,GAAK,EAAO,MAAQ,EAAY,QAAQ,UAC/C,EAAO,GAAK,EAAO,MAAQ,EAAY,QAAQ,cAInD,EAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,SACV,OAAS,EAAG,EAAG,EAAG,GAElB,cAAe,EACf,aAAe,EACf,SAAe,EAEf,MAAO,EAEP,WAAY,EACZ,UAAY,KAIhB,EAAQ,QAAU,EAClB,EAAQ,MAAM,KAAK,WACnB,EAAM,MAAM,EAAa,YACvB,eACA,cACA,eAEF,EAAQ,WAAW,QAAU,aAE7B,EAAe,QAAU,EAAQ,SAEjC,EAAO,QAAU,kJC8LjB,SAAS,GAAiB,EAAM,EAAO,EAAM,EAAS,EAAqB,EAAM,GAE/E,IAAK,EAAS,OAAO,CAGrB,KAAc,IAAV,EAAgB,CAElB,GAAM,GAAS,EAAM,GAAG,OAAO,EAAK,OAAS,EAAK,MAAS,EAAK,MAAS,EAAK,KACxE,EAAS,EAAM,GAAG,OAAO,EAAK,QAAS,EAAK,OAAS,EAAK,OAAS,EAAK,GAW9E,IATI,EAAQ,IACQ,SAAT,EAAoB,EAAO,QAClB,UAAT,IAAoB,EAAO,SAElC,EAAS,IACO,QAAT,EAAqB,EAAO,SACnB,WAAT,IAAqB,EAAO,QAG1B,SAAT,EAAqB,MAAO,GAAK,GAAM,GAAU,EAAG,EAAK,KAAM,EAAK,OAAU,CAClF,IAAa,QAAT,EAAqB,MAAO,GAAK,GAAM,GAAU,EAAG,EAAK,IAAM,EAAK,QAAU,CAElF,IAAa,UAAT,EAAqB,MAAO,GAAK,GAAM,GAAU,EAAG,EAAK,MAAQ,EAAK,MAAQ,CAClF,IAAa,WAAT,EAAqB,MAAO,GAAK,GAAM,GAAU,EAAG,EAAK,OAAQ,EAAK,KAAQ,EAIpF,QAAK,EAAM,GAAG,QAAQ,KAEf,EAAM,GAAG,QAAQ,GAEpB,IAAU,EAEV,EAAM,YAAY,EAAS,EAAO,IA/XxC,GAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,oBACzB,EAAiB,EAAQ,oBAEzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,kBACzB,EAAiB,EAAQ,qBAGzB,EAAgB,EAAQ,eAAiB,EAAQ,qBAAsB,GAAI,GAE3E,GACJ,UACE,SAAc,EACd,aAAc,KAEd,OAAY,KACZ,KAAY,KACZ,SAAY,KACZ,QAAY,KACZ,WAAY,KAEZ,QAAQ,EACR,qBAAqB,EACrB,KAAM,KAGN,OAAQ,IAMR,MAAO,KAMP,OAAQ,QAGV,QAAS,SAAU,EAAS,EAAO,EAAc,EAAS,EAAa,GACrE,IAAK,EAAQ,MAAO,KAEpB,IAAM,GAAO,EAAM,UAAW,EAAY,UAAU,MAC9C,EAAU,EAAa,OAE7B,IAAI,EAAQ,OAAO,QAAS,CAC1B,GAAM,GAAgB,EAAQ,OACxB,GAAgB,MAAM,EAAO,OAAO,EAAO,KAAK,EAAO,QAAQ,EAGrE,IAAI,EAAM,GAAG,OAAO,EAAc,OAAQ,CACxC,IAAK,GAAM,KAAQ,GACjB,EAAY,GAAQ,EAAgB,EACA,EAAc,MAAM,GACpB,EACA,EAAY,aACZ,EACA,EACA,EAAc,QAAU,EAM9D,IAHA,EAAY,KAAO,EAAY,OAAS,EAAY,MACpD,EAAY,IAAO,EAAY,MAAS,EAAY,OAEhD,EAAY,MAAQ,EAAY,OAAS,EAAY,KAAO,EAAY,OAC1E,OACE,KAAM,SACN,MAAO,OAIR,CACH,GAAM,GAAiC,MAAxB,EAAQ,OAAO,MAAgB,EAAK,EAAK,EAAK,MAAS,EAChE,EAAiC,MAAxB,EAAQ,OAAO,MAAgB,EAAK,EAAK,EAAK,OAAS,CAEtE,IAAI,GAAS,EACX,OACE,KAAM,SACN,MAAO,EAAO,IAAM,KAAO,EAAQ,IAAM,MAMjD,MAAO,OAGT,QAAU,EAAQ,OAChB,EAAI,WACJ,EAAI,WACJ,GAAI,YAEJ,IAAa,WACb,KAAa,WACb,OAAa,WACb,MAAa,WACb,QAAa,YACb,YAAa,YACb,SAAa,YACb,WAAa,cAEb,EAAI,YACJ,EAAI,YACJ,GAAI,cAEJ,IAAa,YACb,KAAa,YACb,OAAa,YACb,MAAa,YACb,QAAa,cACb,YAAa,cACb,SAAa,cACb,WAAa,eAGf,UAAW,SAAU,GACnB,GAAI,EAAO,KACT,MAAO,GAAO,QAAQ,EAAO,KAAO,EAAO,KAExC,IAAI,EAAO,MAAO,CAIrB,IAAK,GAHD,GAAY,GACV,GAAa,MAAO,SAAU,OAAQ,SAEnC,EAAI,EAAG,EAAI,EAAG,IACjB,EAAO,MAAM,EAAU,MACzB,GAAa,EAAU,GAI3B,OAAO,GAAO,QAAQ,KAM5B,GAAc,QAAQ,GAAG,MAAO,SAAA,GAAmC,GAAvB,GAAuB,EAAvB,OAAQ,EAAe,EAAf,WAClD,IAAoB,gBAAhB,EAAO,MAA2B,EAAY,SAAS,MAA3D,CAIA,GAAM,GAAY,EAAY,OAAO,QAAQ,EAAY,SACnD,EAAgB,EAAY,OAAO,QAAQ,MAQjD,IAAI,EAAc,QAAU,EAAc,oBAAqB,CAC7D,GAAM,GAAc,EAAM,UAAW,EAAY,SAAS,MAE1D,GAAY,IAAS,EAAY,KAAW,EAAY,OAAW,EAAY,OAC/E,EAAY,KAAS,EAAY,MAAW,EAAY,MAAW,EAAY,MAC/E,EAAY,OAAS,EAAY,QAAW,EAAY,QAAW,EAAY,IAC/E,EAAY,MAAS,EAAY,OAAW,EAAY,SAAW,EAAY,KAE/E,EAAY,SAAS,aAAe,MAGpC,GAAY,SAAS,aAAe,IAIlC,GAAc,sBAChB,EAAY,uBAAyB,EAAU,MAAQ,EAAU,QAGnE,EAAY,aACV,MAAY,EACZ,QAAY,EAAM,UAAW,GAC7B,SAAY,EAAM,UAAW,GAC7B,SAAY,EAAM,UAAW,GAC7B,OACE,KAAM,EAAG,MAAQ,EAAG,MAAQ,EAC5B,IAAM,EAAG,OAAQ,EAAG,OAAQ,IAIhC,EAAO,KAAO,EAAY,YAAY,SACtC,EAAO,UAAY,EAAY,YAAY,SAI7C,EAAc,QAAQ,GAAG,MAAO,SAAA,GAA0C,GAA9B,GAA8B,EAA9B,OAAQ,EAAsB,EAAtB,MAAO,EAAe,EAAf,WACzD,IAAc,SAAV,GAAqB,EAAY,SAAS,MAA9C,CAEA,GAAM,GAAgB,EAAY,OAAO,QAAQ,OAC3C,EAAS,EAAc,OACvB,EAAwB,eAAX,GAAsC,WAAX,EAE1C,EAAQ,EAAY,SAAS,MAE3B,EAAa,EAAY,YAAY,MACrC,EAAa,EAAY,YAAY,QACrC,EAAa,EAAY,YAAY,SACrC,EAAa,EAAY,YAAY,MACrC,EAAa,EAAM,OAAO,EAAY,YAAY,SAAU,GAC5D,EAAgB,EAElB,EAAK,EAAO,GACZ,EAAK,EAAO,EAEhB,IAAI,EAAc,qBAAuB,EAAc,OAAQ,CAE7D,GAAM,GAAmB,EAAc,oBACnC,EAAY,uBACZ,CAEJ,GAAQ,EAAY,SAAS,aAExB,EAAc,MAAQ,EAAc,QACjC,EAAc,OAAS,EAAc,IAC3C,GAAM,EAAK,EAEJ,EAAc,MAAQ,EAAc,MAAU,EAAK,EAAK,GACxD,EAAc,KAAQ,EAAc,UAAU,EAAK,EAAK,GASnE,GALI,EAAM,MAAU,EAAQ,KAAU,GAClC,EAAM,SAAU,EAAQ,QAAU,GAClC,EAAM,OAAU,EAAQ,MAAU,GAClC,EAAM,QAAU,EAAQ,OAAU,GAElC,GAIF,GAFA,EAAM,OAAO,EAAU,GAER,eAAX,EAAyB,CAE3B,GAAI,OAAA,EAEA,GAAS,IAAM,EAAS,SAC1B,EAAO,EAAS,IAEhB,EAAS,IAAM,EAAS,OACxB,EAAS,OAAS,GAEhB,EAAS,KAAO,EAAS,QAC3B,EAAO,EAAS,KAEhB,EAAS,KAAO,EAAS,MACzB,EAAS,MAAQ,QAMrB,GAAS,IAAS,KAAK,IAAI,EAAQ,IAAK,EAAM,QAC9C,EAAS,OAAS,KAAK,IAAI,EAAQ,OAAQ,EAAM,KACjD,EAAS,KAAS,KAAK,IAAI,EAAQ,KAAM,EAAM,OAC/C,EAAS,MAAS,KAAK,IAAI,EAAQ,MAAO,EAAM,KAGlD,GAAS,MAAS,EAAS,MAAS,EAAS,KAC7C,EAAS,OAAS,EAAS,OAAS,EAAS,GAE7C,KAAK,GAAM,KAAQ,GACjB,EAAM,GAAQ,EAAS,GAAQ,EAAS,EAG1C,GAAO,MAAQ,EAAY,SAAS,MACpC,EAAO,KAAO,EACd,EAAO,UAAY,KAgDrB,EAAa,UAAU,UAAY,SAAU,GAC3C,MAAI,GAAM,GAAG,OAAO,IAClB,KAAK,QAAQ,OAAO,SAA8B,IAApB,EAAQ,QACtC,KAAK,aAAa,SAAU,GAC5B,KAAK,YAAY,SAAU,GAEvB,eAAe,KAAK,EAAQ,MAC9B,KAAK,QAAQ,OAAO,KAAO,EAAQ,KAEX,OAAjB,EAAQ,OACf,KAAK,QAAQ,OAAO,KAAO,EAAe,OAAO,MAG/C,EAAM,GAAG,KAAK,EAAQ,qBACxB,KAAK,QAAQ,OAAO,oBAAsB,EAAQ,oBAE3C,EAAM,GAAG,KAAK,EAAQ,UAC7B,KAAK,QAAQ,OAAO,OAAS,EAAQ,QAGhC,MAEL,EAAM,GAAG,KAAK,IAChB,KAAK,QAAQ,OAAO,QAAU,EAEzB,IACH,KAAK,cAAgB,KAAK,cAAgB,KAAK,YAAc,MAGxD,MAEF,KAAK,QAAQ,QAuCtB,EAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,WAAa,OAG3B,EAAc,QAAQ,GAAG,YAAa,SAAA,GAA2C,GAA/B,GAA+B,EAA/B,YAAa,EAAkB,EAAlB,MAC9C,YADgE,EAAV,QACzC,EAAY,aAExB,EAAY,OAAO,QAEvB,OAAO,QACc,MAA3B,EAAY,WACd,EAAO,GAAK,EAAO,GAGnB,EAAO,GAAK,EAAO,GAErB,EAAO,KAAO,OAGd,EAAO,KAAO,EAAY,WAEK,MAA3B,EAAY,WACd,EAAO,GAAK,EAEsB,MAA3B,EAAY,aACnB,EAAO,GAAK,OAKlB,EAAQ,OAAS,EACjB,EAAQ,MAAM,KAAK,UACnB,EAAM,MAAM,EAAa,YACvB,cACA,aACA,qBACA,sBACA;AXtaF,0GWwaA,EAAQ,WAAW,OAAS,YAE5B,EAAe,OAAS,EAAO,SAE/B,EAAO,QAAU,wKC7ajB,IAAM,GAAiB,EAAQ,eACzB,EAAiB,EAAQ,kBAAkB,UAC3C,EAAiB,EAAQ,cACzB,EAAiB,EAAQ,oBACzB,EAAiB,EAAQ,iBACzB,EAAiB,EAAQ,oBAEzB,GACJ,UACE,SAAW,EACX,UAAW,KACX,OAAW,GACX,MAAW,KAGb,YAAa,KACb,EAAG,KACH,EAAG,EAAG,EAAG,EAET,aAAa,EACb,SAAU,EAEV,MAAO,SAAU,GACf,EAAW,aAAc,EACzB,EAAI,OAAO,EAAW,GAEtB,EAAW,YAAc,EACzB,EAAW,UAAW,GAAI,OAAO,UACjC,EAAW,EAAI,EAAI,QAAQ,EAAW,SAGxC,KAAM,WACJ,EAAW,aAAc,EACzB,EAAI,OAAO,EAAW,IAIxB,OAAQ,WACN,GAAM,GAAU,EAAW,YAAY,OAAO,QAAQ,EAAW,YAAY,SAAS,MAAM,WACtF,EAAY,EAAQ,WAAa,EAAU,EAAW,YAAY,SAClE,GAAM,GAAI,OAAO,UAEjB,GAAM,EAAM,EAAW,UAAY,IAEnC,EAAI,EAAQ,MAAQ,CAEtB,IAAK,IACH,EAAG,OAAO,GACZ,EAAU,SAAS,EAAW,EAAI,EAAG,EAAW,EAAI,GAE7C,IACP,EAAU,YAAc,EAAW,EAAI,EACvC,EAAU,WAAc,EAAW,EAAI,GAGzC,EAAW,SAAW,GAGpB,EAAW,cACb,EAAI,OAAO,EAAW,GACtB,EAAW,EAAI,EAAI,QAAQ,EAAW,UAG1C,MAAO,SAAU,EAAc,GAC7B,GAAM,GAAU,EAAa,OAE7B,OAAO,GAAQ,GAAY,YAAc,EAAQ,GAAY,WAAW,SAE1E,kBAAmB,SAAA,GAAoC,GAAxB,GAAwB,EAAxB,YAAa,EAAW,EAAX,OAC1C,IAAM,EAAY,eACT,EAAW,MAAM,EAAY,OAAQ,EAAY,SAAS,MADnE,CAKA,GAAI,EAAY,WAEd,YADA,EAAW,EAAI,EAAW,EAAI,EAIhC,IAAI,OAAA,GACA,MAAA,GACA,MAAA,GACA,MAAA,GAEE,EAAU,EAAY,OAAO,QAAQ,EAAY,SAAS,MAAM,WAChE,EAAY,EAAQ,WAAa,EAAU,EAAY,QAE7D,IAAI,EAAG,OAAO,GACZ,EAAS,EAAQ,QAAU,EAAW,OACtC,EAAS,EAAQ,QAAU,EAAW,OACtC,EAAS,EAAQ,QAAU,EAAU,WAAc,EAAW,OAC9D,EAAS,EAAQ,QAAU,EAAU,YAAc,EAAW,WAE3D,CACH,GAAM,GAAO,EAAS,qBAAqB,EAE3C,GAAS,EAAQ,QAAU,EAAK,KAAS,EAAW,OACpD,EAAS,EAAQ,QAAU,EAAK,IAAS,EAAW,OACpD,EAAS,EAAQ,QAAU,EAAK,MAAS,EAAW,OACpD,EAAS,EAAQ,QAAU,EAAK,OAAS,EAAW,OAGtD,EAAW,EAAK,EAAQ,EAAG,GAAO,EAAG,EACrC,EAAW,EAAK,EAAQ,EAAI,GAAM,EAAG,EAEhC,EAAW,cAEd,EAAW,OAAS,EAAQ,OAC5B,EAAW,MAAS,EAAQ,MAE5B,EAAW,MAAM,MAKvB,GAAY,QAAQ,GAAG,cAAe,WACpC,EAAW,SAGb,EAAY,QAAQ,GAAG,cAAe,EAAW,mBAEjD,EAAe,UAAU,WAAa,EAAW,SAEjD,EAAO,QAAU,yJC1HjB,IAAM,GAAe,EAAQ,mBACvB,EAAe,EAAQ,mBACvB,EAAe,EAAQ,eACvB,EAAe,EAAQ,uBAER,EAAQ,YAArB,IAAA,QAER,GAAa,UAAU,UAAY,SAAU,EAAS,EAAO,EAAa,GACxE,GAAM,GAAS,KAAK,qBAAqB,EAAS,EAAO,EAAa,EAEtE,OAAI,MAAK,QAAQ,cACR,KAAK,QAAQ,cAAc,EAAS,EAAO,EAAQ,KAAM,EAAS,GAGpE,GA+BT,EAAa,UAAU,WAAa,EAAS,SAAU,GACrD,MAAO,MAAK,kBAAkB,aAAc,IAC3C,qGA0BH,EAAa,UAAU,UAAY,EAAS,SAAU,GACpD,MAAO,MAAK,kBAAkB,YAAa,IAC1C,mGAEH,EAAa,UAAU,WAAa,SAAU,EAAY,EAAqB,GAC7E,SAAK,IAAe,EAAG,QAAQ,MAE3B,EAAG,OAAO,GACL,EAAS,YAAY,EAAS,EAAY,KAE1C,EAAG,QAAQ,IACX,EAAS,aAAa,EAAY,KAM7C,EAAa,UAAU,UAAY,SAAU,EAAW,EAAqB,GAC3E,OAAK,KAEA,EAAG,QAAQ,KAEZ,EAAG,OAAO,GACL,EAAS,YAAY,EAAS,EAAW,KAEzC,EAAG,QAAQ,IACX,EAAS,aAAa,EAAW,KAM5C,EAAa,UAAU,gBAAkB,SAAU,EAAS,EAAqB,GAC/E,OAAS,KAAK,WAAW,EAAQ,WAAY,EAAqB,IAC7D,KAAK,UAAU,EAAQ,UAAW,EAAqB,IAkC9D,EAAa,UAAU,cAAgB,SAAU,GAC/C,MAAI,GAAG,SAAS,IACd,KAAK,QAAQ,cAAgB,EAEtB,MAGO,OAAZ,SACK,MAAK,QAAQ,cAEb,MAGF,KAAK,QAAQ,eAUtB,EAAa,UAAU,YAAc,SAAU,GAC7C,MAAI,GAAG,KAAK,IACV,KAAK,QAAQ,YAAc,EAEpB,MAGQ,OAAb,SACK,MAAK,QAAQ,YAEb,MAGF,KAAK,QAAQ,aAGtB,EAAa,UAAU,qBAAuB,SAAU,EAAS,EAAO,EAAa,GAUnF,IAAA,GATM,GAAO,KAAK,QAAQ,GACpB,EAAU,EAAM,UACpB,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAG,IACF,EAAM,QACL,EAAS,KAEb,EAAA,EAAA,EAAyB,EAAQ,MAAjC,OAAA,IAAwC,CAAA,GAAA,EAAA,GAAf,EAAQ,MAAO,EAAA,IAA7B,GAA6B,CAEtC,MAAI,EAAY,gBACT,gBAAgB,KAAK,EAAY,cACqB,IAArD,EAAU,KAAK,QAAQ,GAAY,iBAI3C,EAAS,EAAQ,GAAY,QAAQ,EAAS,EAAO,KAAM,EAAS,EAAa,IAG/E,MAAO,uICzHb,SAAS,GAAgB,EAAQ,EAAc,EAAS,GACtD,MAAI,GAAM,GAAG,OAAO,IACb,EAAa,gBAAgB,EAAa,QAAQ,EAAO,MAAO,EAAS,IACzE,EAAa,QAAQ,EAAO,MAAM,SAClC,EAAuB,EAAc,EAAS,GAC5C,EAGF,KAGT,QAAS,GAAkB,EAAa,EAAS,EAAO,EAAS,EAAe,GAC9E,IAAK,GAAI,GAAI,EAAG,EAAM,EAAQ,OAAQ,EAAI,EAAK,IAAK,CAClD,GAAM,GAAQ,EAAQ,GAChB,EAAe,EAAc,GAC7B,EAAS,EAAe,EAAM,UAAU,EAAS,EAAO,EAAa,GAC7C,EACA,EACA,EAE9B,IAAI,EACF,OACE,OAAA,EACA,OAAQ,EACR,QAAS,GAKf,SAGF,QAAS,GAAe,EAAa,EAAS,EAAO,GAMnD,QAAS,GAAa,GACpB,EAAQ,KAAK,GACb,EAAc,KAAK,GAGrB,IAVA,GAAI,MACA,KAEA,EAAU,EAOP,EAAM,GAAG,QAAQ,IAAU,CAChC,KACA,KAEA,EAAM,cAAc,aAAa,EAAS,EAE1C,IAAM,GAAa,EAAiB,EAAa,EAAS,EAAO,EAAS,EAAe,EAEzF,IAAI,EAAW,SACT,EAAW,OAAO,QAAQ,EAAW,OAAO,MAAM,YACtD,MAAO,EAGT,GAAU,EAAM,WAAW,GAG7B,SAGF,QAAS,GAAS,EAAlB,GAA4D,GAA3B,GAA2B,EAA3B,OAAQ,EAAmB,EAAnB,OAAQ,EAAW,EAAX,OAW/C,IAVA,EAAS,MAEL,EAAY,QAAU,EAAY,OAAO,QAAQ,cACnD,EAAY,OAAO,KAAK,gBAAgB,MAAM,OAAS,IAGzD,EAAY,OAAS,EACrB,EAAY,QAAU,EACtB,EAAM,WAAW,EAAY,SAAU,GAEnC,GAAU,EAAO,QAAQ,YAAa,CACxC,GAAM,GAAS,EAAQ,EAAQ,EAAO,MAAM,UAAU,GAAU,EAChE,GAAY,OAAO,KAAK,gBAAgB,MAAM,OAAS,EAGzD,EAAQ,KAAK,YAAc,YAAa,IAW1C,QAAS,GAAwB,EAAc,EAAS,GACtD,GAAM,GAAU,EAAa,QACvB,EAAa,EAAQ,EAAO,MAAM,IAClC,EAAgB,EAAQ,EAAO,MAAM,cACvC,EAAqB,EACrB,EAAc,EACd,EAAqB,CAGzB,IAAM,GAAc,GAAiB,EAAU,gBAA/C,CAEA,IAAA,GAAA,GAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,EACtC,EAAc,EAAY,SAAS,IAEzC,IAAK,EAAY,cAAjB,CAIA,KAFA,GAE0B,EAAU,gBAClC,OAAO,CAGT,IAAI,EAAY,SAAW,EAA3B,CAIA,IAFA,GAAgB,IAAgB,EAAO,KAAM,IAE1B,EACjB,OAAO,CAGT,IAAI,EAAY,UAAY,IAC1B,IAEI,IAAgB,EAAO,MAAQ,GAAsB,GACvD,OAAO,IAKb,MAAO,GAAU,gBAAkB,GAjNrC,GAAM,GAAiB,EAAQ,eACzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,kBACzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,qBACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,oBAAoB,KAEnD,GAAQ,wBAER,IAAM,IACJ,QAAA,EACA,uBAAA,EAEA,gBAAiB,EAAA,EACjB,UACE,WACE,aAAa,EACb,IAAK,EAAA,EACL,cAAe,EACf,UAAY,KACZ,WAAY,KAIZ,aAAc,IAGlB,kBAAmB,SAAU,GAC3B,EAAM,OAAO,EAAO,SAAU,EAAU,SAAS,YAEnD,eAAA,EAIF,GAAY,QAAQ,GAAG,OAAQ,SAAA,GAAwD,GAA5C,GAA4C,EAA5C,YAAa,EAA+B,EAA/B,QAAS,EAAsB,EAAtB,MAAO,EAAe,EAAf,WACtE,KAAI,EAAY,cAAhB,CAGA,EAAQ,EADW,EAAc,EAAa,EAAS,EAAO,OAKhE,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAAwD,GAA5C,GAA4C,EAA5C,YAAa,EAA+B,EAA/B,QAAS,EAAsB,EAAtB,MAAO,EAAe,EAAf,WACtE,IAAgC,UAA5B,EAAY,cACT,EAAY,gBACZ,EAAY,cAFnB,CAKA,EAAQ,EADW,EAAc,EAAa,EAAS,EAAO,OAIhE,EAAY,QAAQ,GAAG,OAAQ,SAAU,GAAK,GACpC,GAAuB,EAAvB,YAAa,EAAU,EAAV,KAErB,IAAK,EAAY,gBACV,EAAY,eACX,EAAY,iBACZ,EAAY,SAAS,KAH7B,CAOA,EAAQ,KAAK,eAAgB,EAE7B,IAAM,GAAS,EAAY,MAEvB,GAAY,SAAS,MAAQ,IAE3B,EAAO,QAAQ,EAAY,SAAS,MAAM,cACtC,EAAuB,EAAQ,EAAY,QAAS,EAAY,UACtE,EAAY,KAAK,GAGjB,EAAY,MAAM,EAAY,SAAU,EAAQ,EAAY,aAwFlE,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAA2B,GAAf,GAAe,EAAf,YACnC,EAAS,EAAY,MAEvB,IAAU,EAAO,QAAQ,cAC3B,EAAO,KAAK,gBAAgB,MAAM,OAAS,MAyD/C,EAAS,gBAAkB,SAAU,GACnC,MAAI,GAAM,GAAG,OAAO,IAClB,EAAU,gBAAkB,EAErB,GAGF,EAAU,iBAGnB,EAAa,gBAAgB,KAAK,eAClC,EAAa,gBAAgB,KAAK,iBAClC,EAAa,gBAAgB,KAAK,cAClC,EAAa,gBAAgB,KAAK,aAElC,EAAe,KAAK,cAAgB,KACpC,EAAe,KAAK,aAAc,EAElC,EAAM,OAAO,EAAe,UAAW,EAAU,SAAS,WAE1D,EAAO,QAAU,sNChLjB,SAAS,GAAgB,EAAW,GAClC,IAAK,EAAgB,OAAO,CAE5B,IAAM,GAAW,EAAa,QAAQ,KAAK,SAE3C,OAAsB,OAAd,GAAmC,OAAb,GAAqB,IAAa,EAxElE,GAAM,GAAY,EAAQ,UACpB,EAAY,EAAQ,YACpB,EAAY,EAAQ,iBAEH,EAAQ,qBAAvB,IAAA,UAER,GAAU,kBAAkB,EAAQ,oBAEpC,EAAU,QAAQ,GAAG,eAAiB,SAAA,GAAgD,GAApC,GAAoC,EAApC,YAAa,EAAuB,EAAvB,YAAa,EAAU,EAAV,GAAI,EAAM,EAAN,EAC9E,IAAkC,SAA9B,EAAY,SAAS,KAAzB,CAGA,GAAM,GAAO,KAAK,IAAI,GAChB,EAAO,KAAK,IAAI,GAChB,EAAgB,EAAY,OAAO,QAAQ,KAC3C,EAAY,EAAc,UAC1B,EAAe,EAAO,EAAO,IAAM,EAAO,EAAO,IAAM,IAO7D,IALA,EAAY,SAAS,KAAkC,UAA3B,EAAc,SACtC,EAAY,GACZ,EAAc,SAGE,OAAhB,GAAsC,OAAd,GAAsB,IAAc,EAAa,CAE3E,EAAY,SAAS,KAAO,IA2B5B,KAxBA,GAAI,GAAU,EAER,EAAe,SAAU,GAC7B,GAAI,IAAiB,EAAY,OAAjC,CAEA,GAAM,GAAU,EAAY,OAAO,QAAQ,IAE3C,KAAK,EAAQ,aACN,EAAa,gBAAgB,EAAS,EAAS,GAAc,CAElE,GAAM,GAAS,EAAa,UAC1B,EAAY,YAAa,EAAY,UAAW,EAAa,EAE/D,IAAI,GACmB,SAAhB,EAAO,MACP,EAAe,EAAa,IAC5B,EAAU,eAAe,EAAQ,EAAc,EAAS,GAE7D,MAAO,MAMN,EAAG,QAAQ,IAAU,CAC1B,GAAM,GAAe,EAAM,cAAc,aAAa,EAAS,EAE/D,IAAI,EAAc,CAChB,EAAY,SAAS,KAAO,OAC5B,EAAY,OAAS,EACrB,EAAY,QAAU,CACtB,OAGF,EAAU,EAAW,kIC9D3B,GAAQ,UAAU,kBAAkB,EAAQ,8FCmC5C,SAAS,GAAiB,GACxB,GAAM,GAAa,EAAY,UAAY,EAAY,SAAS,IAEhE,KAAK,EAAc,MAAO,KAE1B,IAAM,GAAU,EAAY,OAAO,OAEnC,OAAO,GAAQ,GAAY,MAAQ,EAAQ,GAAY,MA1CzD,GAAM,GAAc,EAAQ,UACtB,EAAc,EAAQ,iBAE5B,GAAU,SAAS,UAAU,KAAO,EACpC,EAAU,SAAS,UAAU,MAAQ,EAErC,EAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,mBAAqB,OAGnC,EAAU,QAAQ,GAAG,WAAY,SAAA,GAA2B,GAAf,GAAe,EAAf,YACrC,EAAO,EAAgB,EAEzB,GAAO,IACT,EAAY,mBAAqB,WAAW,WAC1C,EAAY,MAAM,EAAY,SAAU,EAAY,OAAQ,EAAY,UACvE,MAIP,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAAsC,GAA1B,GAA0B,EAA1B,YAAa,EAAa,EAAb,SAClD,GAAY,kBAAoB,GAClC,aAAa,EAAY,sBAK7B,EAAU,QAAQ,GAAG,eAAgB,SAAA,GAA2B,GAAf,GAAe,EAAf,WAClC,GAAgB,GAElB,IACT,EAAY,SAAS,KAAO,QAchC,EAAO,SACL,gBAAA,sEC9CF,GAAQ,UAAU,kBAAkB,EAAQ,6FCA5C,GAAO,SACL,MACE,OAAgB,KAChB,eAAgB,OAChB,YAAgB,QAGlB,WACE,QAAU,EAAG,EAAG,EAAG,GAEnB,SACE,SAAmB,EACnB,WAAmB,GACnB,SAAmB,IACnB,SAAmB,GACnB,aAAmB,EACnB,kBAAmB,4CCbzB,GAAQ,aAGR,EAAQ,oBACR,EAAQ,wBAGR,EAAQ,wBACR,EAAQ,8BACR,EAAQ,uCAGR,EAAQ,oBAGR,EAAQ,qBACR,EAAQ,oBACR,EAAQ,kBACR,EAAQ,kBAGR,EAAQ,wBACR,EAAQ,6BACR,EAAQ,4BAGR,EAAQ,uBACR,EAAQ,sBACR,EAAQ,oBAGR,EAAQ,mCAGR,EAAQ,gBAGR,EAAO,QAAU,EAAQ,iiBC4IzB,SAAS,GAAa,EAAa,GACjC,GAAM,GAAiB,EAAY,OAAO,QAAQ,EAAY,SAAS,MAAM,QACvE,EAAS,EAAe,WACxB,GAAc,KAAK,IAAI,EAAe,SAAW,EAAO,IAAM,CAEpE,GAAO,GAAK,EAAY,UAAU,MAClC,EAAO,GAAK,EAAY,UAAU,MAClC,EAAO,GAAK,EAAO,WAAW,UAAY,IAC1C,EAAO,GAAK,EAAO,GAAK,EAExB,EAAO,WAAa,EAAO,IAAM,EAAO,IAAM,GAAc,EAC5D,EAAO,WAAa,EAAO,IAAM,EAAO,IAAM,GAAc,EAC5D,EAAO,GAAK,EAEZ,EAAO,UAAY,EAAS,EAAO,GACnC,EAAO,UAAY,EAAI,EAAe,SAAW,EAAO,GAG1D,QAAS,KACP,EAAoB,MACpB,EAAM,eAAe,KAAK,aAAc,KAAK,WAAY,KAAK,UAE9D,IAAM,GAAS,KAAK,cACd,EAAU,KAAK,OAAO,QAAQ,KAAK,SAAS,MAAM,QAClD,EAAS,EAAQ,WACjB,GAAI,GAAI,OAAO,UAAY,IAAO,EAAO,EAE/C,IAAI,EAAI,EAAO,GAAI,CAEjB,GAAM,GAAY,GAAK,KAAK,KAAK,EAAS,GAAK,EAAO,WAAa,EAAO,SAE1E,IAAI,EAAO,aAAe,EAAO,IAAM,EAAO,aAAe,EAAO,GAClE,EAAO,GAAK,EAAO,GAAK,EACxB,EAAO,GAAK,EAAO,GAAK,MAErB,CACH,GAAM,GAAY,EAAM,uBAAuB,EAAG,EACH,EAAO,GACP,EAAO,GACP,EAAO,WACP,EAAO,WACP,EAE/C,GAAO,GAAK,EAAU,EACtB,EAAO,GAAK,EAAU,EAGxB,KAAK,SAEL,EAAO,EAAI,EAAe,QAAQ,KAAK,uBAGvC,GAAO,GAAK,EAAO,WACnB,EAAO,GAAK,EAAO,WAEnB,KAAK,SACL,KAAK,IAAI,EAAO,YAChB,EAAO,QAAS,EAChB,KAAK,WAAa,IAGpB,GAAM,WAAW,KAAK,WAAY,KAAK,WAGzC,QAAS,KACP,EAAoB,KAEpB,IAAM,GAAS,KAAK,cACd,GAAI,GAAI,OAAO,UAAY,EAAO,GAClC,EAAW,KAAK,OAAO,QAAQ,KAAK,SAAS,MAAM,QAAQ,iBAE7D,GAAI,GACN,EAAO,GAAK,EAAM,YAAY,EAAG,EAAG,EAAO,GAAI,GAC/C,EAAO,GAAK,EAAM,YAAY,EAAG,EAAG,EAAO,GAAI,GAE/C,KAAK,YAAY,EAAO,WAAY,EAAO,YAE3C,EAAO,EAAI,EAAe,QAAQ,KAAK,uBAGvC,EAAO,GAAK,EAAO,GACnB,EAAO,GAAK,EAAO,GAEnB,KAAK,YAAY,EAAO,WAAY,EAAO,YAC3C,KAAK,IAAI,EAAO,YAEhB,EAAO,UACL,EAAO,QAAS,EAClB,KAAK,WAAa,MAItB,QAAS,GAAqB,GAC5B,GAAM,GAAS,EAAY,aAG3B,IAAK,EAAO,OAAZ,CAEA,GAAM,GAAW,EAAO,SAAS,KAC3B,EAAW,EAAO,SAAS,MAEjC,GAAM,UAAU,EAAY,YAC1B,MAAS,EAAO,EAAM,EAAO,GAC7B,MAAS,EAAO,EAAM,EAAO,GAC7B,QAAS,EAAS,EAAI,EAAO,GAC7B,QAAS,EAAS,EAAI,EAAO,OA7RjC,GAAM,GAAiB,EAAQ,mBACzB,EAAiB,EAAQ,iBACzB,EAAiB,EAAQ,oBACzB,EAAiB,EAAQ,WACzB,EAAiB,EAAQ,cAE/B,GAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,eACV,QAAa,EACb,WAAa,EACb,aAAa,EAEb,WAAY,KACZ,YAEA,GAAI,EAAG,GAAI,EACX,GAAI,EAAG,GAAI,EAEX,GAAI,EACJ,IAAK,EAAG,IAAK,EACb,SAAU,EAEV,UAAW,EACX,UAAW,EACX,EAAK,MAGP,EAAY,kBAAsB,WAAA,MAAM,GAAe,MAAM,IAC7D,EAAY,oBAAsB,WAAA,MAAM,GAAe,MAAM,MAG/D,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAAwD,GAA5C,GAA4C,EAA5C,YAAa,EAA+B,EAA/B,MAAO,EAAwB,EAAxB,QAAS,EAAe,EAAf,YAChE,EAAS,EAAY,aAG3B,IAAI,EAAO,OAIT,IAHA,GAAI,GAAU,EAGP,EAAM,GAAG,QAAQ,IAAU,CAGhC,GAAI,IAAY,EAAY,QAAS,CAEnC,EAAe,OAAO,EAAO,GAC7B,EAAO,QAAS,EAChB,EAAY,WAAa,KAGzB,EAAY,cAAc,GAC1B,EAAM,UAAU,EAAY,UAAW,EAAY,SAGnD,IAAM,IAAc,YAAA,EACpB,GAAY,QAAQ,KAAK,qBAAsB,GAC/C,EAAY,QAAQ,KAAK,gBAAsB,EAG/C,IAAM,GAAc,GAAI,GAAc,EACA,EACA,EAAY,SAAS,KACrB,gBACA,EAAY,QAElD,GAAY,OAAO,KAAK,GACxB,EAAY,UAAY,EACxB,EAAU,cAAc,EAAY,kBAEpC,EAAM,WAAW,EAAY,WAAY,EAAY,UACrD,OAGF,EAAU,EAAM,WAAW,MAKjC,EAAY,QAAQ,GAAG,KAAM,SAAA,GAAkC,GAAtB,GAAsB,EAAtB,YAAa,EAAS,EAAT,MAC9C,EAAS,EAAY,aAE3B,IAAK,EAAY,gBAAiB,EAAO,OAAzC,CAEA,GAAM,GAAS,EAAY,OACrB,EAAU,GAAU,EAAO,QAC3B,EAAiB,GAAW,EAAY,SAAS,MAAQ,EAAQ,EAAY,SAAS,MAAM,QAE5F,GAAM,GAAI,OAAO,UACjB,KACA,EAAO,EAAM,UAAW,EAAY,UAAU,MAC9C,EAAe,EAAY,aAAa,OAAO,MAEjD,GAAY,EACZ,MAAA,GAGE,EAAmB,GAAkB,EAAe,SACN,YAA9B,EAAY,SAAS,MACrB,IAAU,EAAO,WAEjC,EAAW,GACX,EAAM,EAAY,UAAU,UAAa,IAC1C,EAAe,EAAe,UAC9B,EAAe,EAAe,SAE7B,GACJ,YAAA,EACA,WAAY,EACZ,SAAA,EACA,QAAQ,EACR,gBAAgB,EAId,KAAoB,IACtB,EAAU,cAAc,GAExB,EAAiB,EAAU,OAAO,GAE9B,EAAe,YAAc,EAAe,SAC9C,GAAY,KAIV,GAAW,KAEjB,EAAM,WAAW,EAAO,SAAU,EAAY,WAE9C,EAAY,SAAS,GAAK,EAAO,WAC/B,GAAI,GAAc,EAAa,EAAO,EAAY,SAAS,KAAM,eAAgB,EAAY,SAE/F,EAAO,GAAK,EAEZ,EAAO,QAAS,EAChB,EAAO,YAAc,EAAe,YACpC,EAAY,WAAa,EAEzB,EAAO,KAAK,EAAO,YAEf,GACF,EAAO,IAAM,EAAY,aAAa,OAAO,GAC7C,EAAO,IAAM,EAAY,aAAa,OAAO,GAC7C,EAAO,GAAK,EAEZ,EAAY,EAAa,GAEzB,EAAM,OAAO,EAAM,EAAY,UAAU,MAEzC,EAAK,GAAK,EAAO,GACjB,EAAK,GAAK,EAAO,GAEjB,EAAU,cAAc,GAExB,EAAiB,EAAU,OAAO,GAElC,EAAO,YAAc,EAAe,GACpC,EAAO,YAAc,EAAe,GAEpC,EAAO,EAAI,EAAe,QAAQ,EAAY,qBAG9C,EAAO,WAAY,EACnB,EAAO,GAAK,EAAe,GAC3B,EAAO,GAAK,EAAe,GAE3B,EAAO,GAAK,EAAO,GAAK,EAExB,EAAO,EAAI,EAAe,QAAQ,EAAY,0BAIlD,EAAY,QAAQ,GAAG,cAAe,SAAA,GAA2B,GAAf,GAAe,EAAf,YAC1C,EAAS,EAAY,aAEvB,GAAO,SACT,EAAe,OAAO,EAAO,GAC7B,EAAO,QAAS,EAChB,EAAY,WAAa,sIC5I7B,SAAS,GAAU,EAAS,GAC1B,GAAI,GAAe,EAAM,cAAc,IAAI,EAAS,EAOpD,OALK,KACH,EAAe,GAAI,GAAa,EAAS,GACzC,EAAa,OAAO,OAAS,GAGxB,EA1CT,GAAM,GAAe,EAAQ,mBACvB,EAAe,EAAQ,kBACvB,EAAe,EAAQ,WACvB,EAAe,EAAQ,WACvB,EAAe,EAAQ,kBACvB,EAAe,EAAQ,iBAEvB,IAgDN,GAAS,MAAQ,SAAU,EAAS,GAClC,OAAoF,IAA7E,EAAM,cAAc,eAAe,EAAS,GAAW,EAAQ,UAcxE,EAAS,GAAK,SAAU,EAAM,EAAU,GAKtC,GAJI,EAAM,GAAG,OAAO,KAA+B,IAAtB,EAAK,OAAO,OACvC,EAAO,EAAK,OAAO,MAAM,OAGvB,EAAM,GAAG,MAAM,GAAO,CACxB,IAAA,GAAA,GAAA,EAAA,EAAwB,EAAxB,OAAA,IAA8B,CAAA,GAAA,EAAA,GAAN,EAAM,EAAA,IAAnB,GAAmB,CAC5B,GAAS,GAAG,EAAW,EAAU,GAGnC,MAAO,GAGT,GAAI,EAAM,GAAG,OAAO,GAAO,CACzB,IAAK,GAAM,KAAQ,GACjB,EAAS,GAAG,EAAM,EAAK,GAAO,EAGhC,OAAO,GAkBT,MAdI,GAAM,SAAS,EAAa,WAAY,GAErC,EAAa,GAIhB,EAAa,GAAM,KAAK,GAHxB,EAAa,IAAS,GAQxB,EAAO,IAAI,EAAM,SAAU,EAAM,GAAY,QAAA,IAGxC,GAeT,EAAS,IAAM,SAAU,EAAM,EAAU,GAKvC,GAJI,EAAM,GAAG,OAAO,KAA+B,IAAtB,EAAK,OAAO,OACvC,EAAO,EAAK,OAAO,MAAM,OAGvB,EAAM,GAAG,MAAM,GAAO,CACxB,IAAA,GAAA,GAAA,EAAA,EAAwB,EAAxB,OAAA,IAA8B,CAAA,GAAA,EAAA,GAAN,EAAM,EAAA,IAAnB,GAAmB,CAC5B,GAAS,IAAI,EAAW,EAAU,GAGpC,MAAO,GAGT,GAAI,EAAM,GAAG,OAAO,GAAO,CACzB,IAAK,GAAM,KAAQ,GACjB,EAAS,IAAI,EAAM,EAAK,GAAO,EAGjC,OAAO,GAGT,GAAK,EAAM,SAAS,EAAa,WAAY,GAGxC,CACH,GAAI,OAAA,EAEA,KAAQ,KAC+C,KAAnD,EAAQ,EAAa,GAAM,QAAQ,KACzC,EAAa,GAAM,OAAO,EAAO,OAPnC,GAAO,OAAO,EAAM,SAAU,EAAM,EAAU,EAWhD,OAAO,IAWT,EAAS,MAAQ,WACf,MAAO,IAIT,EAAS,kBAAqB,EAAM,eACpC,EAAS,aAAqB,EAAM,UACpC,EAAS,iBAAqB,EAAM,cACpC,EAAS,cAAqB,EAAM,WAEpC,EAAS,eAAuB,EAAM,eACtC,EAAS,qBAAuB,EAAM,qBACtC,EAAS,gBAAuB,EAAM,gBACtC,EAAS,QAAuB,EAAM,QAOtC,EAAS,cAAgB,WACvB,MAAO,GAAQ,eAQjB,EAAS,qBAAuB,WAC9B,MAAO,GAAQ,sBAWjB,EAAS,KAAO,SAAU,GACxB,IAAK,GAAI,GAAI,EAAM,aAAa,OAAS,EAAG,GAAK,EAAG,IAClD,EAAM,aAAa,GAAG,KAAK,EAG7B,OAAO,IAYT,EAAS,qBAAuB,SAAU,GACxC,MAAI,GAAM,GAAG,OAAO,IAClB,EAAY,qBAAuB,EAE5B,GAGF,EAAY,sBAGrB,EAAS,YAAiB,EAAM,YAChC,EAAS,eAAiB,EAAM,eAEhC,EAAM,SAAW,EAEjB,EAAO,QAAU,8IC7KjB,SAAS,GAAT,GAAqD,GAAtB,GAAsB,EAAtB,YAAa,EAAS,EAAT,KACtC,GAAY,QACd,EAAY,OAAO,uBAAuB,GArE9C,GAAM,GAAe,EAAQ,kBACvB,EAAe,EAAQ,iBACvB,EAAe,EAAQ,WACvB,EAAe,EAAQ,cACvB,EAAe,EAAQ,kBACvB,EAAe,EAAQ,qBAEa,EAAQ,oBAA1C,IAAA,aAAc,IAAA,eAYtB,GAAa,UAAU,eAAiB,SAAU,GAChD,MAAI,wBAAwB,KAAK,IAC/B,KAAK,QAAQ,eAAiB,EACvB,MAGL,EAAG,KAAK,IACV,KAAK,QAAQ,eAAiB,EAAU,SAAW,QAC5C,MAGF,KAAK,QAAQ,gBAGtB,EAAa,UAAU,uBAAyB,SAAU,GACxD,GAAM,GAAU,KAAK,QAAQ,cAE7B,IAAgB,UAAZ,EAEJ,MAAgB,WAAZ,MACF,GAAM,sBAQJ,EAAO,iBACN,sBAAsB,KAAK,EAAM,QAChC,EAAQ,OAKV,uCAAuC,KAAK,EAAM,OAKlD,EAAG,QAAQ,EAAM,SACd,EAAgB,EAAM,OAAQ,0EAIrC,EAAM,kBASR,KAAA,OAA2B,OAAQ,OAAQ,KAAM,UAAjD,EAAA,EAAA,EAAA,EAAA,OAAA,IAA4D,CAAvD,GAAM,GAAA,EAAA,EACT,GAAY,QAAQ,GAAG,EAAa,GAItC,EAAY,UAAU,UAAY,SAA4B,GAC5D,IAAA,GAAA,GAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAE5C,IAAI,EAAY,UACR,EAAY,UAAY,EAAM,QAC3B,EAAa,EAAY,QAAS,EAAM,SAGjD,WADA,GAAY,OAAO,uBAAuB,0KCmGhD,SAAS,GAAU,EAAS,EAAQ,GAClC,MAAQ,IAAW,EAAQ,UACf,IAAW,EAAQ,YAClB,GAAkB,EAAQ,SA3LzC,GAAM,GAAgB,EAAQ,oBACxB,EAAgB,EAAQ,kBACxB,EAAgB,EAAQ,mBAExB,GACJ,SAEA,WAAY,SAAU,GAAK,GACjB,GAAkC,EAAlC,YAAyB,EAAS,EAArB,WACb,EAAiC,EAAjC,OAAQ,EAAyB,EAAzB,QAAS,EAAgB,EAAhB,YACnB,EAAO,EAAO,QAAQ,EAExB,IACF,EAAY,KAAO,EAAK,EAAI,EAAK,KACjC,EAAY,IAAO,EAAK,EAAI,EAAK,IAEjC,EAAY,MAAS,EAAK,MAAS,EAAK,EACxC,EAAY,OAAS,EAAK,OAAS,EAAK,EAElC,SAAY,KAAS,EAAK,MAAS,EAAK,MAAS,EAAK,MACtD,UAAY,KAAS,EAAK,OAAS,EAAK,OAAS,EAAK,MAG5D,EAAY,KAAO,EAAY,IAAM,EAAY,MAAQ,EAAY,OAAS,EAGhF,EAAI,KAAO,EACX,EAAI,aAAe,EACnB,EAAI,QAAU,CAEd,KAAA,GAAA,GAAA,EAAA,EAA2B,EAAU,MAArC,OAAA,IAA4C,CAAA,GAAA,EAAA,GAAjB,EAAU,MAAO,EAAA,IAAjC,GAAiC,CAC1C,GAAI,QAAU,EAAO,QAAQ,EAAY,SAAS,MAAM,GAEnD,EAAI,UAIT,EAAY,gBAAgB,GAAgB,EAAU,GAAc,UAAU,MAIlF,OAAQ,SAAU,GAAK,GACb,GAAkD,EAAlD,YAAa,EAAqC,EAArC,SAAU,EAA2B,EAA3B,OAAQ,EAAmB,EAAnB,eACjC,GACJ,GAAI,EACJ,GAAI,EACJ,SAAS,EACT,QAAQ,EACR,YAAY,EAGd,GAAI,eAAiB,KAAW,EAAI,WAEpC,KAAA,GAAA,GAAA,EAAA,EAA2B,EAAU,MAArC,OAAA,IAA4C,CAAA,GAAA,EAAA,GAAjB,EAAU,MAAO,EAAA,IAAjC,GAAiC,EACpC,EAAW,EAAU,GACrB,EAAU,EAAY,OAAO,QAAQ,EAAY,SAAS,MAAM,EAEjE,GAAS,EAAS,EAAQ,KAE/B,EAAI,OAAS,EAAI,OAAS,EAAS,GACnC,EAAI,QAAU,EACd,EAAI,OAAS,EAAI,YAAY,gBAAgB,GAE7C,EAAS,IAAI,GAET,EAAI,OAAO,SACb,EAAI,eAAe,GAAK,EAAI,OAAO,GACnC,EAAI,eAAe,GAAK,EAAI,OAAO,GAEnC,EAAO,IAAM,EAAI,OAAO,GACxB,EAAO,IAAM,EAAI,OAAO,GAExB,EAAO,QAAS,IAUpB,MAFA,GAAO,YAAc,EAAI,SAAW,EAAO,QAAU,EAAI,OAAO,QAEzD,GAGT,cAAe,SAAU,GACvB,IAAA,GAAA,GAAA,EAAA,EAA2B,EAAU,MAArC,OAAA,IAA4C,CAAA,GAAA,EAAA,GAAjB,EAAU,MAAO,EAAA,IAAjC,GAAiC,EACpC,EAAS,EAAS,MAExB,GAAO,GAAK,EAAO,GAAK,EACxB,EAAO,UAAY,EAAO,UAAY,IACtC,EAAO,QAAS,EAChB,EAAO,SAAU,EAEjB,EAAS,GAAgB,EAG3B,MAAO,IAGT,MAAO,SAAA,EAA2B,GAAY,GAA3B,GAA2B,EAA3B,YACX,GACJ,YAAA,EACA,YAA4B,kBAAf,EACA,EAAY,UAAY,EAAY,aAAa,KAC9D,YAAa,EAAY,YACzB,SAAU,EAAY,iBACtB,QAAQ,EACR,gBAAgB,EAGlB,GAAU,WAAW,GACrB,EAAU,cAAc,EAAI,UAE5B,EAAI,WAAa,KAAW,EAAY,YAAY,MACpD,EAAY,eAAiB,EAAU,OAAO,IAGhD,WAAY,SAAA,GAA0D,GAA9C,GAA8C,EAA9C,YAAa,EAAiC,EAAjC,OAAQ,EAAyB,EAAzB,sBACrC,EAAiB,EAAU,QAC/B,YAAA,EACA,OAAA,EACA,WAAY,EAAY,UAAU,KAClC,SAAU,EAAY,iBACtB,gBAAgB,KAKb,EAAe,YAAc,IAChC,EAAY,eAAgB,GAG9B,EAAY,eAAiB,GAG/B,IAAK,SAAA,GACH,IAAA,GADe,GAAsB,EAAtB,YAAa,EAAS,EAAT,MAC5B,EAAA,EAAA,EAA2B,EAAU,MAArC,OAAA,IAA4C,CAAA,GAAA,EAAA,GAAjB,EAAU,MAAO,EAAA,IAAjC,GAAiC,CAI1C,IAAI,EAHY,EAAY,OAAO,QAAQ,EAAY,SAAS,MAAM,IAGhD,GAAM,GAAO,CAEjC,EAAY,QAAS,MAAA,EAAO,QAAQ,GACpC,UAKN,MAAO,SAAU,GAIf,IAAK,GAHG,GAAwB,EAAxB,OAAQ,EAAgB,EAAhB,YACV,EAAc,KAAW,GAEtB,EAAI,EAAG,EAAI,EAAU,MAAM,OAAQ,IAAK,CAC/C,GAAM,GAAe,EAAU,MAAM,EAGrC,IAFA,EAAY,QAAU,EAAY,OAAO,QAAQ,EAAY,SAAS,MAAM,GAEvE,EAAY,QAAjB,CAIA,GAAM,GAAW,EAAU,EAE3B,GAAY,OAAS,EAAY,iBAAiB,GAElD,EAAO,GAAgB,EAAS,aAAa,MAKnD,GAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,aAAqB,KAAM,EAAG,MAAO,EAAG,IAAK,EAAG,OAAQ,GACpE,EAAY,mBACZ,EAAY,iBAAmB,EAAU,kBACzC,EAAY,eAAmB,OAGjC,EAAY,QAAQ,GAAG,eAAiB,EAAU,OAClD,EAAY,QAAQ,GAAG,gBAAiB,EAAU,OAClD,EAAY,QAAQ,GAAG,qBAAsB,EAAU,YACvD,EAAY,QAAQ,GAAG,aAAc,EAAU,KAE/C,EAAc,QAAQ,GAAG,SAAU,EAAU,OAQ7C,EAAO,QAAU,mGC9FjB,SAAS,GAAoB,EAAO,EAAa,GAC/C,MAAI,GAAM,GAAG,SAAS,GACb,EAAM,gBAAgB,EAAO,EAAY,OAAQ,EAAY,SAAU,EAAK,EAAG,EAAK,EAAG,IAEvF,EAAM,gBAAgB,EAAO,EAAY,OAAQ,EAAY,SApGxE,GAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,qBAEzB,GACJ,UACE,SAAa,EACb,SAAa,EACb,YAAa,KACb,YAAa,MAGf,UAAW,SAAA,GAA0C,GAA9B,GAA8B,EAA9B,KAAM,EAAwB,EAAxB,YAAa,EAAW,EAAX,QAClC,EAAc,GAAW,EAAQ,YACjC,IAaN,OAXI,IAAQ,GACV,EAAO,KAAO,EAAY,KAAQ,EAAK,MAAS,EAAY,KAC5D,EAAO,IAAO,EAAY,IAAQ,EAAK,OAAS,EAAY,IAE5D,EAAO,MAAS,EAAY,MAAU,EAAK,OAAU,EAAI,EAAY,OACrE,EAAO,OAAS,EAAY,OAAU,EAAK,QAAU,EAAI,EAAY,SAGrE,EAAO,KAAO,EAAO,IAAM,EAAO,MAAQ,EAAO,OAAS,EAGrD,GAGT,IAAK,SAAA,GAA4D,GAAhD,GAAgD,EAAhD,eAAgB,EAAgC,EAAhC,YAAa,EAAmB,EAAnB,OAAQ,EAAW,EAAX,OACpD,KAAK,EAAW,MAAO,EAEvB,IAAM,GAAO,EAAO,aACd,EAAG,EAAO,EAAG,EAAG,EAAO,GACzB,EAAM,UAAW,GAEf,EAAc,EAAmB,EAAQ,YAAa,EAAa,EAEzE,KAAK,EAAe,MAAO,EAE3B,GAAO,GAAK,EACZ,EAAO,GAAK,EACZ,EAAO,QAAS,CAEhB,IAAM,GAAO,EACT,EAAY,EAAK,EACjB,EAAY,EAAK,EAEf,EAAS,EAAY,gBAAgB,QAKvC,MAAO,IAAe,KAAO,IAC/B,EAAY,KAAK,IAAI,KAAK,IAAI,EAAK,EAAI,EAAK,MAAS,EAAO,MAAQ,EAAK,GAAI,EAAK,EAAI,EAAO,MAC7F,EAAY,KAAK,IAAI,KAAK,IAAI,EAAK,EAAI,EAAK,OAAS,EAAO,OAAQ,EAAK,GAAI,EAAK,EAAI,EAAO,OAG7F,EAAY,KAAK,IAAI,KAAK,IAAI,EAAK,MAAS,EAAO,MAAQ,EAAK,GAAI,EAAK,KAAO,EAAO,MACvF,EAAY,KAAK,IAAI,KAAK,IAAI,EAAK,OAAS,EAAO,OAAQ,EAAK,GAAI,EAAK,IAAO,EAAO,MAGzF,EAAO,GAAK,EAAY,EAAK,EAC7B,EAAO,GAAK,EAAY,EAAK,EAE7B,EAAO,QAAU,EAAO,YAAc,GAAa,EAAO,YAAc,EACxE,EAAO,UAAY,EAAO,KAAM,EAAO,IAEvC,EAAO,UAAY,EACnB,EAAO,UAAY,GAGrB,aAAc,SAAA,GAAoD,GAAxC,GAAwC,EAAxC,KAAM,EAAkC,EAAlC,OAAQ,EAA0B,EAA1B,OAAQ,EAAkB,EAAlB,MAAO,EAAW,EAAX,QAC/C,EAAc,GAAW,EAAQ,WAEvC,IAAI,GAAW,EAAQ,UACJ,UAAV,IAAqB,IAAe,EAAO,SAE9C,EAAO,OAMT,MALA,GAAK,GAAK,EAAO,GACjB,EAAK,GAAK,EAAO,GACjB,EAAO,GAAK,EAAO,GACnB,EAAO,GAAK,EAAO,IAGjB,GAAI,EAAO,GACX,GAAI,EAAO,KAMnB,mBAAA,EAWF,GAAU,SAAW,EACrB,EAAU,MAAM,KAAK,YAErB,EAAe,UAAU,SAAW,EAAS,SAE7C,EAAO,QAAU,uFClGjB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,iBACzB,EAAiB,EAAQ,qBACzB,EAAiB,EAAQ,uBAEA,EAAQ,cAA/B,IAAA,mBAEF,GAAY,IAAM,EAAA,EAAU,KAAO,EAAA,EAAU,QAAS,EAAA,EAAU,OAAQ,EAAA,GACxE,GAAY,KAAM,EAAA,EAAU,MAAO,EAAA,EAAU,OAAS,EAAA,EAAU,MAAQ,EAAA,GAExE,GACJ,UACE,SAAS,EACT,SAAS,EACT,IAAK,KACL,IAAK,KACL,OAAQ,MAGV,UAAW,SAAA,GAAiD,GAArC,GAAqC,EAArC,YAAa,EAAwB,EAAxB,YAAa,EAAW,EAAX,OAC/C,KAAK,EACH,MAAO,GAAM,UAAW,EAG1B,IAAM,GAAS,EAAmB,EAAQ,OAAQ,EAAa,EAAY,YAAY,KAEvF,OAAI,IAEA,IAAQ,EAAY,IAAS,EAAO,EACpC,KAAQ,EAAY,KAAS,EAAO,EACpC,OAAQ,EAAY,OAAS,EAAO,EACpC,MAAQ,EAAY,MAAS,EAAO,GAIjC,GAGT,IAAK,SAAA,GAAoE,GAAxD,GAAwD,EAAxD,eAAgB,EAAwC,EAAxC,YAAa,EAA2B,EAA3B,OAAQ,EAAmB,EAAnB,OAAQ,EAAW,EAAX,QACtD,EAAQ,EAAY,SAAS,aAAe,EAAY,SAAS,KAEvE,IAAK,EAAY,eAAkB,EAAnC,CAIA,GAAM,GAAO,EAAO,aACd,EAAG,EAAO,EAAG,EAAG,EAAO,GACzB,EAAM,UAAW,GACf,EAAQ,EAAU,WAAW,EAAmB,EAAQ,MAAO,EAAa,KAAU,EACtF,EAAQ,EAAU,WAAW,EAAmB,EAAQ,MAAO,EAAa,KAAU,EAExF,EAAY,EAAK,EACjB,EAAY,EAAK,CAErB,GAAO,GAAK,EACZ,EAAO,GAAK,EACZ,EAAO,QAAS,EAEZ,EAAM,IACR,EAAY,KAAK,IAAI,KAAK,IAAI,EAAM,IAAS,EAAO,IAAQ,EAAK,GAAI,EAAM,IAAS,EAAO,KAEpF,EAAM,SACb,EAAY,KAAK,IAAI,KAAK,IAAI,EAAM,OAAS,EAAO,OAAQ,EAAK,GAAI,EAAM,OAAS,EAAO,SAEzF,EAAM,KACR,EAAY,KAAK,IAAI,KAAK,IAAI,EAAM,KAAS,EAAO,KAAQ,EAAK,GAAI,EAAM,KAAS,EAAO,MAEpF,EAAM,QACb,EAAY,KAAK,IAAI,KAAK,IAAI,EAAM,MAAS,EAAO,MAAQ,EAAK,GAAI,EAAM,MAAS,EAAO,QAG7F,EAAO,GAAK,EAAY,EAAK,EAC7B,EAAO,GAAK,EAAY,EAAK,EAE7B,EAAO,QAAU,EAAO,YAAc,GAAa,EAAO,YAAc,EACxE,EAAO,UAAY,EAAO,KAAM,EAAO,IAEvC,EAAO,UAAY,EACnB,EAAO,UAAY,IAGrB,aAAc,SAAA,GAAoD,GAAxC,GAAwC,EAAxC,KAAM,EAAkC,EAAlC,OAAQ,EAA0B,EAA1B,OAAQ,EAAkB,EAAlB,MAAO,EAAW,EAAX,OACrD,IAAI,GAAW,EAAQ,UACJ,UAAV,IAAqB,EAAO,SAE/B,EAAO,OAMT,MALA,GAAK,GAAK,EAAO,GACjB,EAAK,GAAK,EAAO,GACjB,EAAO,GAAK,EAAO,GACnB,EAAO,GAAK,EAAO,IAGjB,GAAI,EAAO,GACX,GAAI,EAAO,KAMnB,QAAA,EACA,QAAA,EACA,mBAAA,EAGF,GAAU,cAAgB,EAC1B,EAAU,MAAM,KAAK,iBAErB,EAAe,UAAU,cAAgB,EAAc,SACvD,EAAO,SAAS,cAAyB,EAAc,SAEvD,EAAO,QAAU,iJC/GjB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,mBACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,iBACzB,EAAiB,EAAQ,qBACzB,EAAiB,EAAQ,qBAEzB,GAAU,OAAQ,EAAA,EAAU,QAAS,EAAA,GACrC,GAAU,MAAQ,EAAA,EAAU,OAAS,EAAA,GAErC,GACJ,UACE,SAAS,EACT,SAAS,EACT,IAAK,KACL,IAAK,MAGP,UAAW,SAAA,GACT,MADoC,GAAf,YACF,aAGrB,IAAK,SAAU,GAAK,GACV,GAAyB,EAAzB,YAAa,EAAY,EAAZ,QACf,EAAQ,EAAY,SAAS,aAAe,EAAY,SAAS,KAEvE,IAAK,EAAY,eAAkB,EAAnC,CAIA,GAAM,GAAO,EAAU,WAAW,EAAY,YAAY,UAEpD,EAAU,EAAU,WAAW,EAAc,mBAAmB,EAAQ,IAAK,KAAiB,EAC9F,EAAU,EAAU,WAAW,EAAc,mBAAmB,EAAQ,IAAK,KAAiB,CAEpG,GAAI,SACF,QAAS,EAAQ,QACjB,QAAS,EAAQ,QACjB,MAAO,EAAM,UAAW,EAAc,SACtC,MAAO,EAAM,UAAW,EAAc,UAGpC,EAAM,KACR,EAAI,QAAQ,MAAM,IAAM,EAAK,OAAS,EAAQ,OAC9C,EAAI,QAAQ,MAAM,IAAM,EAAK,OAAS,EAAQ,QAEvC,EAAM,SACb,EAAI,QAAQ,MAAM,OAAS,EAAK,IAAM,EAAQ,OAC9C,EAAI,QAAQ,MAAM,OAAS,EAAK,IAAM,EAAQ,QAE5C,EAAM,MACR,EAAI,QAAQ,MAAM,KAAO,EAAK,MAAQ,EAAQ,MAC9C,EAAI,QAAQ,MAAM,KAAO,EAAK,MAAQ,EAAQ,OAEvC,EAAM,QACb,EAAI,QAAQ,MAAM,MAAQ,EAAK,KAAO,EAAQ,MAC9C,EAAI,QAAQ,MAAM,MAAQ,EAAK,KAAO,EAAQ,OAGhD,EAAc,IAAI,KAGpB,aAAc,EAAc,aAG9B,GAAU,aAAe,EACzB,EAAU,MAAM,KAAK,gBAErB,EAAe,UAAU,aAAe,EAAa,SACrD,EAAO,SAAS,aAAwB,EAAa,SAErD,EAAO,QAAU,sJClFjB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,eACzB,EAAiB,EAAQ,YACzB,EAAiB,EAAQ,qBAEzB,GACJ,UACE,SAAS,EACT,SAAS,EACT,MAAS,EAAA,EACT,QAAS,KACT,QAAS,KAET,eAAgB,MAGlB,UAAW,SAAA,GAA8E,GAAlE,GAAkE,EAAlE,YAAa,EAAqD,EAArD,aAAc,EAAuC,EAAvC,QAAS,EAA8B,EAA9B,KAAM,EAAwB,EAAxB,YAAa,EAAW,EAAX,QACtE,KACA,EAAgB,EAAM,SAAS,EAAM,gBAAgB,EAAQ,SAC7D,EAAS,GAAiB,EAAM,YAAY,EAAc,EAAS,EAAY,SAAS,KAC9F,GAAU,GAAW,EAAa,QAAQ,EAAY,SAAS,MAAM,QAErE,IAAI,OAAA,EAEJ,IAAuB,gBAAnB,EAAQ,OACV,GACE,EAAG,EAAY,YAAY,KAAK,EAAI,EAAO,EAC3C,EAAG,EAAY,YAAY,KAAK,EAAI,EAAO,OAGzC,CACJ,GAAM,GAAa,EAAM,gBAAgB,EAAQ,OAAQ,EAAc,GAAU,GAEjF,GAAa,EAAM,SAAS,KAAiB,EAAG,EAAG,EAAG,GAGxD,GAAI,GAAQ,EAAQ,gBAAkB,EAAQ,eAAe,OAC3D,IAAA,GAAA,GAAA,EAAA,EAA6C,EAAQ,eAArD,OAAA,IAAqE,CAAA,GAAA,EAAA,GAAxB,EAAQ,eAAgB,EAAA,IAAA,GAAA,EAArD,EAAqD,EAAxD,EAAiB,EAAuC,EAA1C,CACzB,GAAQ,MACN,EAAG,EAAY,KAAQ,EAAK,MAAS,EAAa,EAAW,EAC7D,EAAG,EAAY,IAAQ,EAAK,OAAS,EAAa,EAAW,QAKjE,GAAQ,KAAK,EAGf,OAAO,IAGT,IAAK,SAAA,GAA6E,GAAjE,GAAiE,EAAjE,YAAa,EAAoD,EAApD,eAAgB,EAAoC,EAApC,OAAQ,EAA4B,EAA5B,QAAiB,EAAW,EAAnB,OACvD,KACF,MAAA,GACA,MAAA,GACA,MAAA,EAEJ,IAAI,EAAO,YACT,GAAS,EAAG,EAAO,EAAG,EAAG,EAAO,OAE7B,CACH,GAAM,GAAS,EAAM,YAAY,EAAY,OAAQ,EAAY,QAAS,EAAY,SAAS,KAE/F,GAAO,EAAM,UAAW,GAExB,EAAK,GAAK,EAAO,EACjB,EAAK,GAAK,EAAO,EAGnB,EAAO,MAAQ,EAAK,EACpB,EAAO,MAAQ,EAAK,CAIpB,KAAA,GAFI,GAAM,EAAQ,QAAS,EAAQ,QAAQ,OAAS,EAEpD,EAAA,EAAA,EAAyC,EAAzC,OAAA,IAAkD,CAAA,GAAA,EAAA,GAAT,EAAS,EAIhD,KAAA,GAJgD,GAAA,EAAlC,EAAkC,EAArC,EAAe,EAAsB,EAAzB,EACjB,EAAY,EAAK,EAAI,EACrB,EAAY,EAAK,EAAI,EAE3B,EAAA,EAAA,GAA0B,EAAQ,aAAlC,OAAA,IAAkD,CAAA,GAAA,EAAA,IAAxB,EAAQ,aAAgB,EAAA,IAAvC,GAAuC,CAE9C,GADE,EAAM,GAAG,SAAS,GACX,EAAW,EAAW,EAAW,GAGjC,EAGN,GAEL,EAAQ,MACN,EAAG,EAAM,GAAG,OAAO,EAAO,GAAM,EAAO,EAAI,EAAW,EACtD,EAAG,EAAM,GAAG,OAAO,EAAO,GAAM,EAAO,EAAI,EAAW,EAEtD,MAAO,EAAM,GAAG,OAAO,EAAO,OAAQ,EAAO,MAAO,EAAQ,SAKlE,GAAM,IACJ,OAAQ,KACR,SAAS,EACT,SAAU,EACV,MAAO,EACP,GAAI,EACJ,GAAI,EAGN,KAAK,EAAI,EAAG,EAAM,EAAQ,OAAQ,EAAI,EAAK,IAAK,CAC9C,EAAS,EAAQ,EAEjB,IAAM,GAAQ,EAAO,MACf,EAAK,EAAO,EAAI,EAAK,EACrB,EAAK,EAAO,EAAI,EAAK,EACrB,EAAW,EAAM,MAAM,EAAI,GAC7B,EAAU,GAAY,CAItB,KAAU,EAAA,GAAY,EAAQ,SAAW,EAAQ,QAAU,EAAA,IAC7D,GAAU,GAGP,EAAQ,UAAW,EAEjB,EAAQ,SAAW,IAAU,EAAA,EAE9B,EAAW,EAAQ,EAAQ,SAAW,EAAQ,MAE7C,IAAU,EAAA,GAAY,EAAQ,QAAU,EAAA,GAE1C,EAAW,EAAQ,UAEhB,EAAQ,SAAW,EAAW,EAAQ,YAE5C,EAAQ,OAAS,EACjB,EAAQ,SAAW,EACnB,EAAQ,MAAQ,EAChB,EAAQ,QAAU,EAClB,EAAQ,GAAK,EACb,EAAQ,GAAK,EAEb,EAAO,MAAQ,GAInB,GAAI,OAAA,EAEA,GAAQ,QACV,EAAe,EAAO,YAAc,EAAQ,OAAO,GAAK,EAAO,YAAc,EAAQ,OAAO,EAE5F,EAAO,UAAY,EAAQ,OAAO,EAClC,EAAO,UAAY,EAAQ,OAAO,IAGlC,GAAc,EAEd,EAAO,UAAY,IACnB,EAAO,UAAY,KAGrB,EAAO,GAAK,EAAQ,GACpB,EAAO,GAAK,EAAQ,GAEpB,EAAO,QAAW,GAAgB,EAAQ,UAAY,EAAO,OAC7D,EAAO,OAAS,EAAQ,SAG1B,aAAc,SAAA,GAAoD,GAAxC,GAAwC,EAAxC,KAAM,EAAkC,EAAlC,OAAQ,EAA0B,EAA1B,OAAQ,EAAkB,EAAlB,MAAO,EAAW,EAAX,QAC/C,EAAiB,GAAW,EAAQ,cAE1C,IAAI,GAAW,EAAQ,UACJ,UAAV,IAAqB,IAAkB,EAAe,QAS7D,MAPI,GAAO,SACT,EAAK,GAAK,EAAO,GACjB,EAAK,GAAK,EAAO,GACjB,EAAO,GAAK,EAAO,GACnB,EAAO,GAAK,EAAO,KAInB,MAAS,EAAO,MAChB,OAAS,EAAO,OAChB,EAAS,EAAO,UAChB,EAAS,EAAO,UAChB,MAAS,EAAO,MAChB,MAAS,EAAO,MAChB,GAAS,EAAO,GAChB,GAAS,EAAO,KAMxB,GAAS,eAAiB,SAAU,GAClC,MAAO,UAAU,EAAG,GAClB,GAAM,GAAS,EAAK,SAClB,MAAS,EAAA,EACT,MAAS,EAAA,EACT,KAAS,EAAA,EACT,OAAS,EAAA,GAEP,EAAU,EACV,EAAU,CAEV,GAAM,GAAG,OAAO,EAAK,UACvB,EAAU,EAAK,OAAO,EACtB,EAAU,EAAK,OAAO,EAGxB,IAAM,GAAQ,KAAK,OAAO,EAAI,GAAW,EAAK,GACxC,EAAQ,KAAK,OAAO,EAAI,GAAW,EAAK,EAK9C,QACE,EAJW,KAAK,IAAI,EAAO,KAAM,KAAK,IAAI,EAAO,MAAQ,EAAQ,EAAK,EAAI,IAK1E,EAJW,KAAK,IAAI,EAAO,IAAM,KAAK,IAAI,EAAO,OAAQ,EAAQ,EAAK,EAAI,IAK1E,MAAO,EAAK,SAKlB,EAAU,KAAO,EACjB,EAAU,MAAM,KAAK,QAErB,EAAe,UAAU,KAAO,EAAK,SAErC,EAAO,QAAU,wGCjOjB,IAAM,GAAiB,EAAQ,UACzB,EAAiB,EAAQ,UACzB,EAAiB,EAAQ,qBACzB,EAAiB,EAAQ,qBACzB,EAAiB,EAAQ,aAEzB,GACJ,UACE,SAAS,EACT,SAAS,EACT,MAAS,EAAA,EACT,QAAS,KACT,QAAS,MAGX,UAAW,SAAU,GAAK,GAChB,GAAyB,EAAzB,YAAa,EAAY,EAAZ,QACf,EAAQ,EAAY,SAAS,KAEnC,IAAK,EAAL,CAEA,EAAI,SACF,iBACE,EAAG,EAAM,KAAM,EAAI,EACnB,EAAG,EAAM,IAAM,EAAI,IAErB,QAAU,EAAG,EAAG,EAAG,GACnB,OAAQ,OACR,MAAO,EAAQ,MAGjB,IAAM,GAAU,EAAK,UAAU,EAG/B,OAFA,GAAI,QAAU,EAEP,IAGT,IAAK,SAAU,GAAK,GACV,GAAiD,EAAjD,YAAa,EAAoC,EAApC,QAAS,EAA2B,EAA3B,OAAQ,EAAmB,EAAnB,eAChC,EAAO,EAAM,UAAW,GACxB,EAAY,EAAK,EAAI,EAAO,GAAG,EAC/B,EAAY,EAAK,EAAI,EAAO,GAAG,CAErC,GAAI,QAAU,EAAM,UAAW,GAC/B,EAAI,QAAQ,UAEZ,KAAA,GAAA,GAAA,EAAA,GAA0B,EAAQ,aAAlC,OAAA,IAAkD,CAAA,GAAA,EAAA,IAAxB,EAAQ,aAAgB,EAAA,IAAvC,GAAuC,EAC5C,MAAA,EAGF,GADE,EAAM,GAAG,SAAS,GACX,EAAW,EAAW,EAAW,GAGjC,EAGN,IAED,SAAW,IAAU,UAAY,KACnC,EAAO,EAAI,EAAO,MAClB,EAAO,EAAI,EAAO,QAGpB,EAAI,QAAQ,QAAQ,KAAK,IAG3B,EAAK,IAAI,IAGX,aAAc,SAAU,GAAK,GACnB,GAAY,EAAZ,OAER,GAAI,QAAU,EAAM,UAAW,GAC/B,EAAI,QAAQ,QAAU,EAAQ,QAC9B,EAAI,QAAQ,gBAAkB,MAE9B,EAAK,aAAa,IAItB,GAAU,SAAW,EACrB,EAAU,MAAM,KAAK,YAErB,EAAe,UAAU,SAAW,EAAS,SAC7C,EAAO,SAAS,SAAoB,EAAS,SAE7C,EAAO,QAAU,0NCzFjB,GAAM,GAAe,EAAQ,wBAE7B,GAAO,QAAP,WAEE,QAAA,GAAa,EAAM,EAAS,EAAO,EAAa,GAiB9C,GAjB2D,EAAA,KAAA,GAC3D,EAAa,cAAc,KAAM,GAE7B,IAAU,GACZ,EAAa,cAAc,KAAM,GAGnC,KAAK,YAAc,EAEnB,KAAK,WAAgB,GAAI,OAAO,UAChC,KAAK,cAAgB,EACrB,KAAK,KAAgB,EACrB,KAAK,UAAgB,EAAa,aAAa,GAC/C,KAAK,YAAgB,EAAa,eAAe,GACjD,KAAK,OAAgB,EACrB,KAAK,cAAgB,KAER,QAAT,EAAgB,CAClB,GAAM,GAAe,EAAY,gBAAgB,EACjD,MAAK,GAAK,KAAK,UAAY,EAAY,UAAU,EAEjD,IAAM,GAAW,KAAK,UAAY,EAAY,OAE9C,MAAK,UAAY,EAAY,SACK,cAA7B,EAAY,QAAQ,MACpB,EAAY,QAAQ,SAAW,KAAK,QACpC,EAAW,SAEA,cAAT,IACP,KAAK,GAAK,EAAQ,UAAY,EAAY,SA/BhD,MAAA,GAAA,UAmCE,eAnCF,SAAA,GAmC8C,GAAvB,GAAuB,EAA1B,EAAe,EAAW,EAAd,CAM5B,OALA,MAAK,OAAW,EAChB,KAAK,OAAW,EAChB,KAAK,SAAW,EAChB,KAAK,SAAW,EAET,MAzCX,EAAA,UA4CE,UA5CF,SAAA,GA4CyC,GAAvB,GAAuB,EAA1B,EAAe,EAAW,EAAd,CAMvB,OALA,MAAK,OAAW,EAChB,KAAK,OAAW,EAChB,KAAK,SAAW,EAChB,KAAK,SAAW,EAET,MAlDX,EAAA,UAsDE,eAtDF,WAuDI,KAAK,cAAc,kBAvDvB,EAAA,UA2DE,gBA3DF,WA4DI,KAAK,oBAAqB,GA5D9B,EAAA,UAgEE,yBAhEF,WAiEI,KAAK,4BAA8B,KAAK,oBAAqB,GAjEjE,oEC6BA,SAAS,GAAM,GAkBb,IAAK,GAhBH,GAIE,EAJF,YAAa,EAIX,EAJW,QAAS,EAIpB,EAJoB,MAAO,EAI3B,EAJ2B,YAFb,EAMd,EAHF,KAAA,MAHgB,KAAA,EAGT,EAAI,aAAa,KAHR,EAAA,EAMd,EAFF,QAAA,MAJgB,KAAA,EAIN,EAAoB,GAJd,EAAA,EAMd,EADF,aAAA,MALgB,KAAA,EAKD,GAAI,GAAa,EAAM,EAAS,EAAO,EAAa,GALnD,EAQZ,GACJ,YAAA,EACA,QAAA,EACA,MAAA,EACA,YAAA,EACA,QAAA,EACA,KAAA,EACA,aAAA,GAGO,EAAI,EAAG,EAAI,EAAQ,OAAQ,IAAK,CACvC,GAAM,GAAS,EAAQ,EAEvB,KAAK,GAAM,KAAQ,GAAO,UACxB,EAAa,GAAQ,EAAO,MAAM,EAGpC,IAAM,GAAS,EAAM,YAAY,EAAO,UAAW,EAAO,QAU1D,IARA,EAAa,eAAe,GAC5B,EAAa,UAAY,EAAO,UAChC,EAAa,cAAgB,EAAO,QAEpC,EAAO,UAAU,KAAK,GAEtB,EAAa,UAAU,GAEnB,EAAa,6BACT,EAAa,oBACT,EAAI,EAAK,EAAQ,QAAU,EAAQ,EAAI,GAAG,UAAY,EAAa,cAC7E,MAMJ,GAFA,EAAQ,KAAK,QAAS,GAET,QAAT,EAAgB,CAGlB,GAAM,GAAU,EAAa,OACzB,GACA,YAAA,EAAa,QAAA,EAAS,MAAA,EAAO,YAAA,EAC7B,KAAM,cAEN,CAEJ,GAAY,QAAU,EACtB,EAAY,QAAU,EAAQ,UAGhC,MAAO,GAGT,QAAS,GAAT,GAAkF,GAAlD,GAAkD,EAAlD,YAAa,EAAqC,EAArC,QAAS,EAA4B,EAA5B,MAAO,EAAqB,EAArB,YAAa,EAAQ,EAAR,KAClE,EAAe,EAAY,gBAAgB,EAGjD,IAAa,QAAT,IAAmB,EAAY,kBAE1B,EAAY,YAAY,IAAiB,EAAY,YAAY,KAAkB,GAC1F,QAeF,KAAA,GAZM,GAAO,EAAM,QAAQ,GACrB,GACJ,YAAA,EACA,QAAA,EACA,MAAA,EACA,YAAA,EACA,KAAA,EACA,KAAA,EACA,WACA,QAAS,MAGX,EAAA,EAAA,EAAsB,EAAtB,OAAA,IAA4B,CAAA,GAAA,EAAA,GAAN,EAAM,EAAA,IAAjB,GAAiB,CAC1B,GAAU,QAAU,EAEpB,EAAQ,KAAK,kBAAmB,GAQlC,MALa,SAAT,IACF,EAAU,QAAU,EAAU,QAAQ,OAAO,SAAA,GAAA,MAC3C,GAAO,UAAU,QAAQ,eAAiB,EAAY,WAAW,GAAc,YAG5E,EAAU,QA7HnB,GAAM,GAAe,EAAQ,kBACvB,EAAe,EAAQ,kBACvB,EAAe,EAAQ,YACvB,EAAe,EAAQ,qBACvB,EAAe,EAAQ,oBAAoB,MAE3C,GAAkB,OAAQ,KAAM,UAChC,GAAkB,OAAQ,KAAM,UAEhC,GACJ,aAAA,EACA,KAAA,EACA,oBAAA,EACA,QAAA,EACA,UACE,aAAc,IACd,WAAc,KACd,UAAc,KACd,QAAgB,EAAG,EAAG,EAAG,IAE3B,OACE,OACA,OACA,KACA,SACA,MACA,YACA,QAqGJ,GAAY,QAAQ,GAAG,sBAAuB,SAAA,GAAyC,GAA7B,GAA6B,EAA7B,YAAa,EAAgB,EAAhB,YACrE,GAAY,WAAW,IAAkB,SAAU,EAAA,EAAU,QAAS,QAGxE,EAAY,QAAQ,GAAG,iBAAkB,SAAA,GAAyC,GAA7B,GAA6B,EAA7B,YAAa,EAAgB,EAAhB,YAChE,GAAY,WAAW,OAAO,EAAc,KAG9C,EAAY,QAAQ,GAAG,OAAQ,SAAA;gCAAuE,GAA3D,GAA2D,EAA3D,YAAa,EAA8C,EAA9C,QAAS,EAAqC,EAArC,MAAO,EAA8B,EAA9B,YAAa,EAAiB,EAAjB,cAC7E,EAAe,EAAY,gBAAgB,EAE5C,IAAmB,EAAY,gBAAiB,EAAY,kBAC3D,EAAY,eACd,aAAa,EAAY,WAAW,GAAc,SAGpD,GACE,YAAA,EAAa,QAAA,EAAS,MAAA,EAAO,YAAA,EAC7B,KAAM,YAKZ,EAAY,QAAQ,GAAG,OAAQ,SAAA,GAc7B,IAAA,GAdyC,GAA0D,EAA1D,YAAa,EAA6C,EAA7C,QAAS,EAAoC,EAApC,MAAO,EAA6B,EAA7B,YAAa,EAAgB,EAAhB,aAC7E,EAAQ,EAAY,WAAW,GAC/B,EAAO,EAAM,QAAQ,GACrB,GACJ,YAAA,EACA,QAAA,EACA,MAAA,EACA,YAAA,EACA,KAAM,OACN,WACA,KAAA,EACA,QAAS,MAGX,EAAA,EAAA,EAAsB,EAAtB,OAAA,IAA4B,CAAA,GAAA,EAAA,GAAN,EAAM,EAAA,IAAjB,GAAiB,CAC1B,GAAU,QAAU,EAEpB,EAAQ,KAAK,kBAAmB,GAGlC,GAAK,EAAU,QAAQ,OAAvB,CAIA,IAAA,GAFI,GAAc,EAAA,EAElB,EAAA,EAAA,EAAqB,EAAU,QAA/B,OAAA,IAAwC,CAAA,GAAA,EAAA,GAAnB,EAAU,QAAS,EAAA,IAA7B,GAA6B,EAChC,EAAe,EAAO,UAAU,QAAQ,YAE1C,GAAe,IACjB,EAAc,GAIlB,EAAM,SAAW,EACjB,EAAM,QAAU,WAAW,WACzB,GACE,YAAA,EACA,YAAA,EACA,QAAA,EACA,MAAA,EACA,KAAM,UAEP,MAGL,EAAY,QAAQ,GAAG,KAAM,SAAA,GAAkD,GAA/C,GAA+C,EAA/C,YAAa,EAAkC,EAAlC,QAAS,EAAyB,EAAzB,MAAO,EAAkB,EAAlB,WACtD,GAAY,iBACf,GAAO,YAAA,EAAa,YAAA,EAAa,QAAA,EAAS,MAAA,EAAO,KAAM,SAI3D,KAAA,OAA0B,KAAM,UAAhC,EAAA,EAAA,EAAA,EAAA,OAAA,IAA2C,CAAtC,GAAM,GAAA,EAAA,EACT,GAAY,QAAQ,GAAG,EAAY,SAAA,GAAyC,GAA7B,GAA6B,EAA7B,YAAa,EAAgB,EAAhB,YACtD,GAAY,WAAW,IACzB,aAAa,EAAY,WAAW,GAAc,WAWxD,IAAK,GAAI,GAAI,EAAG,EAAI,EAAc,OAAQ,IACxC,EAAY,QAAQ,GAAG,EAAc,GAPvC,SAA+B,GAC7B,MAAO,UAAA,GAAwD,GAA5C,GAA4C,EAA5C,YAAa,EAA+B,EAA/B,QAAS,EAAsB,EAAtB,KACvC,IAAO,YAAA,EAAa,YADyC,EAAf,YACb,QAAA,EAAS,MAAA,EAAO,KAAA,MAKW,EAAa,IAG7E,GAAY,QAAQ,GAAG,MAAO,SAAU,GACtC,EAAY,QAAa,KACzB,EAAY,QAAa,EACzB,EAAY,gBAGd,EAAS,cAAgB,EAAc,SACvC,EAAO,QAAU,wICxNjB,SAAS,GAAT,GAAkC,GAAhB,GAAgB,EAAhB,YACU,UAAtB,EAAa,OAEjB,EAAa,OAAS,EAAa,OAAS,GAAK,GAGnD,QAAS,GAAT,GAAuE,GAAnD,GAAmD,EAAnD,YAAa,EAAsC,EAAtC,aAAc,EAAwB,EAAxB,YAAa,EAAW,EAAX,OAC1D,IAA0B,SAAtB,EAAa,MAAoB,EAAQ,OAA7C,CAGA,GAAM,GAAW,EAAQ,GAAG,UAAU,QAAQ,kBAG1C,IAAY,IAGhB,EAAY,mBAAqB,WAAW,WAC1C,EAAc,MACZ,YAAA,EACA,YAAA,EACA,KAAM,OACN,QAAS,EACT,MAAO,KAER,KAGL,QAAS,GAAT,GAAyC,GAAf,GAAe,EAAf,WAGpB,GAAY,qBACd,cAAc,EAAY,oBAC1B,EAAY,mBAAqB,MA1CrC,GAAM,GAAgB,EAAQ,UACxB,EAAgB,EAAQ,iBAE9B,GAAc,QAAQ,GAAG,MAAO,GAChC,EAAc,QAAQ,GAAG,QAAS,EAElC,KAAA,OAAsB,OAAQ,KAAM,SAAU,UAA9C,EAAA,EAAA,EAAA,EAAA,OAAA,IAAyD,CAApD,GAAM,GAAA,EAAA,EACT,GAAY,QAAQ,GAAG,EAAQ,GAwCjC,EAAc,SAAS,mBAAqB,EAC5C,EAAc,MAAM,KAAK,cAEzB,EAAO,SACL,MAAA,EACA,QAAA,EACA,cAAA,sECrDF,IAAM,GAAgB,EAAQ,UACxB,EAAgB,EAAQ,mBACxB,EAAgB,EAAQ,eACxB,EAAgB,EAAQ,YACxB,EAAgB,EAAQ,qBACR,EAAQ,gBAAtB,IAAA,KAER,GAAc,QAAQ,GAAG,kBAAmB,SAAA,GAAmD,GAAvC,GAAuC,EAAvC,QAAS,EAA8B,EAA9B,QAAS,EAAqB,EAArB,KAAM,EAAe,EAAf,WAC9E,GAAM,cAAc,aAAa,EAAS,SAAA,GACxC,GAAM,GAAY,EAAa,OACzB,EAAU,EAAU,OAEtB,GAAU,IACT,EAAG,QAAQ,IACX,EAAa,gBAAgB,EAAS,EAAS,IAElD,EAAQ,MACN,QAAA,EACA,UAAA,EACA,OAAS,aAAA,SAMjB,EAAa,QAAQ,GAAG,MAAO,SAAA,GAA4B,GAAhB,GAAgB,EAAhB,YACzC,GAAa,OAAO,QAAU,SAAU,GACtC,MAAO,GAAa,QAAQ,MAIhC,EAAa,QAAQ,GAAG,MAAO,SAAA,GAAqC,GAAzB,GAAyB,EAAzB,aAAc,EAAW,EAAX,OACvD,GAAO,EAAa,OAAO,QAAS,EAAc,UAClD,EAAO,EAAa,OAAO,QAAS,KAGtC,EAAM,EAAa,WAAY,EAAc,OAE7C,EAAa,UAAU,cAAgB,SAAU,GAG/C,MAFA,GAAO,KAAK,OAAO,QAAS,GAErB,KAGT,IAAM,GAAqB,EAAa,UAAU,iBAElD,GAAa,UAAU,kBAAoB,SAAU,EAAY,GAC/D,GAAM,GAAM,EAAmB,KAAK,KAAM,EAAY,EAMtD,OAJI,KAAQ,OACV,KAAK,OAAO,QAAQ,GAAc,GAG7B,GAGT,EAAa,gBAAgB,KAAK,2JCxDlC,IAAM,GAAU,EAAQ,WAClB,EAAU,EAAQ,kBAClB,EAAU,EAAQ,mBAAmB,QAErB,EAAQ,kBAAtB,IAAA,UAEF,GACJ,QAAA,EACA,OAAA,EACA,MAAA,EAGA,SAAU,EAAQ,sBAAsB,SAExC,aAEA,YAAa,SAAU,EAAK,GAE1B,GAAI,EAAM,SAAS,EAAM,UAAW,GAAQ,OAAO,CAEnD,GAAM,GAAO,EAAU,GAEvB,EAAM,UAAU,KAAK,GACrB,EAAO,UAAU,KAAK,GAIlB,IAAQ,EAAM,UAChB,EAAO,IAAI,EAAK,SAAU,EAAM,gBAGlC,EAAQ,KAAK,gBAAkB,IAAA,EAAK,IAAA,KAGtC,eAAgB,SAAU,EAAK,GAC7B,GAAM,GAAQ,EAAM,UAAU,QAAQ,EAEtC,GAAM,GAAO,EAAU,GAEvB,EAAO,OAAO,EAAK,SAAU,EAAM,gBAEnC,EAAM,UAAU,OAAO,EAAO,GAC9B,EAAO,UAAU,OAAO,EAAO,GAE/B,EAAQ,KAAK,mBAAqB,IAAA,EAAK,IAAA,KAGzC,eAAgB,WACd,EAAM,eAAe,KAAK,SAAU,OAIxC,GAAO,QAAU,0OCpDX,cACJ,QAAA,KAAe,EAAA,KAAA,GACb,KAAK,gCAKP,YAAI,EAAM,GACR,IAAK,KAAK,UAAU,GAElB,YADA,KAAK,UAAU,IAAS,GAI1B,MAAK,UAAU,GAAM,KAAK,gBAG5B,aAAK,EAAM,GACT,GAAK,KAAK,UAAU,GAApB,CAEA,GAAM,GAAQ,KAAK,UAAU,GAAM,QAAQ,IAE5B,IAAX,GACF,KAAK,UAAU,GAAM,OAAO,EAAO,iBAIvC,cAAM,EAAM,GACV,GAAM,GAAkB,KAAK,UAAU,EAEvC,IAAK,EAEL,IAAA,GAAA,GAAA,EAAA,EAAuB,EAAvB,OAAA,IAAwC,CAAA,GAAA,EAAA,GAAjB,EAAiB,EAAA,IAA7B,GAA6B,CACtC,KAA4B,IAAxB,EAAS,EAAK,GAChB,aAMR,GAAQ,IAAM,WACZ,MAAO,IAAI,IAGb,EAAO,QAAU,uCC3CjB,SAAS,GAAU,EAAO,GACxB,OAAkC,IAA3B,EAAM,QAAQ,GAGvB,QAAS,GAAO,EAAQ,GACtB,IAAA,GAAA,GAAA,EAAA,EAAmB,EAAnB,OAAA,IAA2B,CAAA,GAAA,EAAA,GAAR,EAAQ,EAAA,IAAhB,GAAgB,CACzB,GAAO,KAAK,GAGd,MAAO,GAGT,EAAO,SACL,SAAA,EACA,MAAA,+CCdiB,EAAQ,YAAnB,IAAA,OACF,EAAS,EAAQ,QACjB,EAAa,EAAQ,gBAErB,EAAU,EAAW,QACrB,EAAa,EAAO,UAEpB,GAEJ,iBAAmB,gBAAkB,IAAW,EAAG,SAAS,EAAO,gBAC7C,EAAW,mBAAoB,GAAO,eAG5D,uBAAwB,EAAW,aAEnC,MAAQ,iBAAiB,KAAK,EAAU,UAGxC,OAAS,iBAAiB,KAAK,EAAU,WAC7B,YAAY,KAAK,EAAU,YAEvC,MAAO,SAAS,KAAK,EAAU,WAG/B,wBAAyB,WAAa,GAAQ,UAC1C,UAAW,yBAA2B,GAAQ,UAC9C,wBAAyB,sBAAwB,GAAQ,UACzD,qBAAsB,oBAAsB,GAAQ,UACpD,mBAAoB,oBAExB,YAAc,EAAW,aACpB,EAAW,eAAiB,EAAO,gBAElC,GAAQ,cACR,KAAQ,gBACR,KAAQ,YACR,IAAQ,WACR,KAAQ,gBACR,OAAQ,oBAGR,GAAQ,YACR,KAAQ,cACR,KAAQ,cACR,IAAQ,aACR,KAAQ,cACR,OAAQ,iBAEV,KAGJ,WAAY,gBAAkB,GAAW,SAAU,aAAc,QAKnE,GAAQ,cAAuC,UAAtB,EAAU,SAC9B,EAAQ,eACR,EAAU,UAAU,MAAM,UAE/B,EAAO,QAAU,gFC5DjB,IAAM,GAAK,EAAQ,OAEnB,GAAO,QAAU,QAAS,GAAO,GAC/B,GAAM,KACN,KAAK,GAAM,KAAQ,GACb,EAAG,YAAY,EAAO,IACxB,EAAK,GAAQ,EAAM,EAAO,IAE1B,EAAK,GAAQ,EAAO,EAGxB,OAAO,kDCRT,SAAS,MAHT,GAAM,MACA,EAAM,EAAQ,YAAY,MAIhC,GAAW,SAAqB,EAAI,SACpC,EAAW,iBAAqB,EAAI,kBAAsB,EAC1D,EAAW,WAAqB,EAAI,YAAsB,EAC1D,EAAW,cAAqB,EAAI,eAAsB,EAC1D,EAAW,mBAAqB,EAAI,oBAAsB,EAC1D,EAAW,QAAqB,EAAI,SAAsB,EAC1D,EAAW,YAAqB,EAAI,aAAsB,EAAW,QAErE,EAAW,MAAe,EAAI,MAC9B,EAAW,MAAe,EAAI,OAAS,EACvC,EAAW,aAAgB,EAAI,cAAgB,EAAI,eAEnD,EAAO,QAAU,oDCjBjB,IAAM,GAAa,EAAQ,YACrB,EAAa,EAAQ,aACrB,EAAa,EAAQ,QACrB,EAAa,EAAQ,gBAErB,GACJ,aAAc,SAAU,EAAQ,GAC9B,KAAO,GAAO,CACZ,GAAI,IAAU,EACZ,OAAO,CAGT,GAAQ,EAAM,WAGhB,OAAO,GAGT,QAAS,SAAU,EAAS,GAC1B,KAAO,EAAG,QAAQ,IAAU,CAC1B,GAAI,EAAS,gBAAgB,EAAS,GAAa,MAAO,EAE1D,GAAU,EAAS,WAAW,GAGhC,MAAO,OAGT,WAAY,SAAU,GACpB,GAAI,GAAS,EAAK,UAElB,IAAI,EAAG,QAAQ,GAAS,CAEtB,MAAQ,EAAS,EAAO,OAAS,EAAG,QAAQ,KAI5C,MAAO,GAGT,MAAO,IAGT,gBAAiB,SAAU,EAAS,GAMlC,MAJI,GAAI,SAAW,EAAI,aACrB,EAAW,EAAS,QAAQ,YAAa,MAGpC,EAAQ,EAAQ,yBAAyB,IAIlD,sBAAuB,SAAU,GAC/B,GAAI,MACA,KACA,MAAA,GACA,EAAc,EAAS,GACvB,EAAQ,EAAa,GAAI,EACzB,MAAA,GACA,MAAA,GACA,MAAA,GACA,MAAA,EAEJ,KAAK,EAAI,EAAG,EAAI,EAAS,OAAQ,IAI/B,IAHA,EAAW,EAAS,KAGH,IAAa,EAI9B,GAAK,GAQL,GAAI,EAAS,aAAe,EAAS,cAIhC,GAAI,EAAY,aAAe,EAAS,cAAxC,CAML,IAAK,EAAmB,OAEtB,IADA,EAAS,EACF,EAAO,YAAc,EAAO,aAAe,EAAO,eACvD,EAAmB,QAAQ,GAC3B,EAAS,EAAO,UAMpB,IAAI,YAAuB,GAAW,aAC/B,YAAoB,GAAW,cAC7B,YAAoB,GAAW,eAAgB,CAEtD,GAAI,IAAa,EAAY,WAC3B,QAGF,GAAS,EAAS,oBAGlB,GAAS,CAKX,KAFA,KAEO,EAAO,aAAe,EAAO,eAClC,EAAgB,QAAQ,GACxB,EAAS,EAAO,UAMlB,KAHA,EAAI,EAGG,EAAgB,IAAM,EAAgB,KAAO,EAAmB,IACrE,GAGF,IAAM,IACJ,EAAgB,EAAI,GACpB,EAAgB,GAChB,EAAmB,GAKrB,KAFA,EAAQ,EAAQ,GAAG,UAEZ,GAAO,CACZ,GAAI,IAAU,EAAQ,GAAI,CACxB,EAAc,EACd,EAAQ,EACR,IAEA,OAEG,GAAI,IAAU,EAAQ,GACzB,KAGF,GAAQ,EAAM,qBA/Dd,GAAc,EACd,EAAQ,MAbR,GAAc,EACd,EAAQ,CA8EZ,OAAO,IAGT,YAAa,SAAU,EAAS,EAAU,GACxC,KAAO,EAAG,QAAQ,IAAU,CAC1B,GAAI,EAAS,gBAAgB,EAAS,GACpC,OAAO,CAKT,KAFA,EAAU,EAAS,WAAW,MAEd,EACd,MAAO,GAAS,gBAAgB,EAAS,GAI7C,OAAO,GAGT,iBAAkB,SAAU,GAC1B,MAAQ,aAAmB,GAAW,mBAClC,EAAQ,wBACR,GAGN,YAAa,SAAU,GAErB,MADA,GAAiB,GAAkB,EAAI,QAErC,EAAG,EAAe,SAAW,EAAe,SAAS,gBAAgB,WACrE,EAAG,EAAe,SAAW,EAAe,SAAS,gBAAgB,YAIzE,qBAAsB,SAAU,GAC9B,GAAM,GAAc,YAAmB,GAAW,WAC9C,EAAQ,wBACR,EAAQ,iBAAiB,EAE7B,OAAO,KACL,KAAQ,EAAW,KACnB,MAAQ,EAAW,MACnB,IAAQ,EAAW,IACnB,OAAQ,EAAW,OACnB,MAAQ,EAAW,OAAU,EAAW,MAAS,EAAW,KAC5D,OAAQ,EAAW,QAAU,EAAW,OAAS,EAAW,MAIhE,eAAgB,SAAU,GACxB,GAAM,GAAa,EAAS,qBAAqB,EAEjD,KAAK,EAAQ,QAAU,EAAY,CACjC,GAAM,GAAS,EAAS,YAAY,EAAI,UAAU,GAElD,GAAW,MAAU,EAAO,EAC5B,EAAW,OAAU,EAAO,EAC5B,EAAW,KAAU,EAAO,EAC5B,EAAW,QAAU,EAAO,EAG9B,MAAO,IAGT,QAAS,SAAU,GAGjB,IAFA,GAAM,MAEC,GACL,EAAK,KAAK,GACV,EAAU,EAAS,WAAW,EAGhC,OAAO,IAGT,YAAa,SAAA,GACX,QAAK,EAAG,OAAO,KAGf,EAAW,SAAS,cAAc,IAC3B,IAIX,GAAO,QAAU,+FC5MjB,SAAS,GAAK,EAAS,EAAM,EAAU,GACrC,GAAM,GAAU,EAAW,GACvB,EAAe,EAAS,QAAQ,GAChC,EAAS,EAAQ,EAEhB,KACH,GACE,UACA,UAAW,GAGb,EAAe,EAAS,KAAK,GAAW,EACxC,EAAQ,KAAK,IAGV,EAAO,OAAO,KACjB,EAAO,OAAO,MACd,EAAO,aAGJ,EAAS,EAAO,OAAO,GAAO,KACjC,EAAQ,iBAAiB,EAAM,EAAU,EAAiB,IAAY,EAAQ,SAC9E,EAAO,OAAO,GAAM,KAAK,IAI7B,QAAS,GAAQ,EAAS,EAAM,EAAU,GACxC,GAAM,GAAU,EAAW,GACrB,EAAe,EAAS,QAAQ,GAChC,EAAS,EAAQ,EAEvB,IAAK,GAAW,EAAO,OAIvB,GAAa,QAAT,EAAJ,CASA,GAAI,EAAO,OAAO,GAAO,CACvB,GAAM,GAAM,EAAO,OAAO,GAAM,MAEhC,IAAiB,QAAb,EAAoB,CACtB,IAAK,GAAI,GAAI,EAAG,EAAI,EAAK,IACvB,EAAO,EAAS,EAAM,EAAO,OAAO,GAAM,GAAI,EAEhD,QAGA,IAAK,GAAI,GAAI,EAAG,EAAI,EAAK,IACvB,GAAI,EAAO,OAAO,GAAM,KAAO,EAAU,CACvC,EAAQ,oBAAR,KAAiC,EAAQ,EAAU,EAAiB,IAAY,EAAQ,SACxF,EAAO,OAAO,GAAM,OAAO,EAAG,EAE9B,OAKF,EAAO,OAAO,IAAwC,IAA/B,EAAO,OAAO,GAAM,SAC7C,EAAO,OAAO,GAAQ,KACtB,EAAO,aAIN,EAAO,YACV,EAAQ,OAAO,EAAc,GAC7B,EAAS,OAAO,EAAc,QApC9B,KAAK,IAAQ,GAAO,OACd,EAAO,OAAO,eAAe,IAC/B,EAAO,EAAS,EAAM,OAsC9B,QAAS,GAAa,EAAU,EAAS,EAAM,EAAU,GACvD,GAAM,GAAU,EAAW,EAC3B,KAAK,EAAgB,GAAO,CAC1B,EAAgB,IACd,aACA,YACA,aAIF,KAAA,GAAA,GAAA,EAAA,EAAkB,EAAlB,OAAA,IAA6B,CAAxB,GAAM,GAAO,EAAP,EACT,GAAI,EAAK,EAAM,GACf,EAAI,EAAK,EAAM,GAAoB,IAIvC,GAAM,GAAY,EAAgB,GAC9B,MAAA,EAEJ,KAAK,EAAQ,EAAU,UAAU,OAAS,EAAG,GAAS,IAChD,EAAU,UAAU,KAAW,GAC5B,EAAU,SAAS,KAAW,GAFkB,MAO1C,IAAX,IACF,EAAQ,EAAU,UAAU,OAE5B,EAAU,UAAU,KAAK,GACzB,EAAU,SAAU,KAAK,GACzB,EAAU,UAAU,UAItB,EAAU,UAAU,GAAO,MAAM,IAAY,EAAQ,QAAS,EAAQ,UAGxE,QAAS,GAAgB,EAAU,EAAS,EAAM,EAAU,GAC1D,GAAM,GAAU,EAAW,GACrB,EAAY,EAAgB,GAC9B,GAAa,EACb,MAAA,EAEJ,IAAK,EAGL,IAAK,EAAQ,EAAU,UAAU,OAAS,EAAG,GAAS,EAAG,IAEvD,GAAI,EAAU,UAAU,KAAW,GAC5B,EAAU,SAAS,KAAW,EAAS,CAK5C,IAAK,GAHC,GAAY,EAAU,UAAU,GAG7B,EAAI,EAAU,OAAS,EAAG,GAAK,EAAG,IAAK,CAAA,GAAA,GACf,EAAU,GAAlC,EADuC,EAAA,GACnC,EADmC,EAAA,GAC1B,EAD0B,EAAA,EAI9C,IAAI,IAAO,GAAY,MAAc,EAAQ,SAAW,IAAY,EAAQ,QAAS,CAEnF,EAAU,OAAO,EAAG,GAIf,EAAU,SACb,EAAU,UAAU,OAAO,EAAO,GAClC,EAAU,SAAU,OAAO,EAAO,GAClC,EAAU,UAAU,OAAO,EAAO,GAGlC,EAAO,EAAS,EAAM,GACtB,EAAO,EAAS,EAAM,GAAoB,GAGrC,EAAU,UAAU,SACvB,EAAgB,GAAQ,OAK5B,GAAa,CACb,QAIJ,GAAI,EAAc,OAOxB,QAAS,GAAkB,EAAO,GAChC,GAAM,GAAU,EAAW,GACrB,KACA,EAAY,EAAgB,EAAM,MAHK,EAItB,EAAa,gBAAgB,GAA7C,EAJsC,EAAA,GAKzC,EAAU,CASd,KANA,EAAQ,EAAW,GAEnB,EAAU,cAAgB,EAC1B,EAAU,eAAiB,EAGpB,EAAG,QAAQ,IAAU,CAC1B,IAAK,GAAI,GAAI,EAAG,EAAI,EAAU,UAAU,OAAQ,IAAK,CACnD,GAAM,GAAW,EAAU,UAAU,GAC/B,EAAU,EAAU,SAAS,EAEnC,IAAI,EAAS,gBAAgB,EAAS,IAC/B,EAAS,aAAa,EAAS,IAC/B,EAAS,aAAa,EAAS,GAAU,CAE9C,GAAM,GAAY,EAAU,UAAU,EAEtC,GAAU,cAAgB,CAE1B,KAAK,GAAI,GAAI,EAAG,EAAI,EAAU,OAAQ,IAAK,CAAA,GAAA,GACV,EAAU,GAAlC,EADkC,EAAA,GAC9B,EAD8B,EAAA,GACrB,EADqB,EAAA,EAGrC,OAAc,EAAQ,SAAW,IAAY,EAAQ,SACvD,EAAG,KAMX,EAAU,EAAS,WAAW,IAIlC,QAAS,GAAoB,GAC3B,MAAO,GAAiB,KAAK,KAAM,GAAO,GAG5C,QAAS,KACP,KAAK,cAAc,iBAGrB,QAAS,GAAY,GACnB,MAAO,GAAG,OAAO,GAAQ,GAAU,QAAS,GA1P9C,GAAM,GAAe,EAAQ,QACvB,EAAe,EAAQ,cACvB,EAAe,EAAQ,kBACvB,EAAe,EAAQ,qBAER,EAAQ,YAArB,IAAA,SACa,EAAQ,SAArB,IAAA,SAEF,KACA,KASA,KACA,KAEA,EAAmB,WACvB,GAAI,IAAY,CAMhB,OAJA,GAAO,SAAS,cAAc,OAAO,iBAAiB,OAAQ,MAC5D,cAAiB,GAAY,KAGxB,IAiOT,GAAO,SACL,IAAA,EACA,OAAA,EAEA,YAAA,EACA,eAAA,EAEA,iBAAA,EACA,mBAAA,EACA,gBAAA,EACA,UAAA,EAEA,gBAAA,EAEA,UAAW,EACX,SAAU,mIC5QZ,GAAO,QAAU,SAAiB,EAAM,GACtC,IAAK,GAAM,KAAQ,GACjB,EAAK,GAAQ,EAAO,EAEtB,OAAO,gDCDL,EAAQ,UAFV,IAAA,gBACA,IAAA,QAGF,GAAO,QAAU,SAAU,EAAQ,EAAS,GAC1C,GAAM,GAAgB,EAAO,QAAQ,GAC/B,EAAe,GAAiB,EAAc,OAC9C,EAAS,GAAgB,EAAO,QAAQ,OAExC,EAAa,EAAgB,EAAQ,EAAQ,GAAU,GAAU,GAEvE,OAAO,GAAS,KAAiB,EAAG,EAAG,EAAG,oDCZ5C,GAAO,QAAU,SAAC,EAAG,GAAJ,MAAW,MAAK,KAAK,EAAI,EAAI,EAAI,yCCAlD,IAAM,GAAS,EAAQ,YACjB,EAAS,EAAQ,YAEjB,GACJ,SAAU,SAAU,EAAQ,GAC1B,GAAI,IAAS,CAEb,OAAO,YAML,MALK,KACH,EAAI,OAAO,QAAQ,KAAK,GACxB,GAAS,GAGJ,EAAO,MAAM,KAAM,aAK9B,iBAAkB,SAAU,EAAG,EAAI,EAAI,GACrC,GAAM,GAAK,EAAI,CACf,OAAO,GAAK,EAAK,EAAK,EAAI,EAAK,EAAI,EAAK,EAAI,EAAI,GAGlD,uBAAwB,SAAU,EAAQ,EAAQ,EAAK,EAAK,EAAM,EAAM,GACtE,OACE,EAAI,EAAM,iBAAiB,EAAU,EAAQ,EAAK,GAClD,EAAI,EAAM,iBAAiB,EAAU,EAAQ,EAAK,KAKtD,YAAa,SAAU,EAAG,EAAG,EAAG,GAE9B,MADA,IAAK,GACG,EAAI,GAAG,EAAE,GAAK,GAGxB,WAAY,SAAU,EAAM,GAK1B,MAJA,GAAK,KAAQ,EAAI,KACjB,EAAK,KAAQ,EAAI,KACjB,EAAK,MAAQ,EAAI,MAEV,GAGT,GAAa,EAAQ,QACrB,OAAa,EACb,MAAa,EAAQ,WACrB,YAAa,EAAQ,iBAGvB,GAAO,EAAO,EAAQ,UACtB,EAAO,EAAO,EAAQ,eACtB,EAAO,EAAO,EAAQ,mBACtB,EAAO,EAAO,EAAQ,WAEtB,EAAO,QAAU,uKCvDjB,IAAM,GAAU,EAAQ,YAClB,EAAU,EAAQ,WAElB,GACJ,aAAe,mBAAoB,aAAc,aAAc,QAE/D,OAAQ,SAAU,EAAS,EAAW,GAKpC,IAAA,GAJM,GAAc,EAAM,eAAe,GACnC,EAAY,EAAM,aAAa,GAC/B,GAAY,QAAA,EAAS,UAAA,EAAW,YAAA,EAAa,UAAA,EAAW,YAAA,GAE9D,EAAA,EAAA,EAAqB,EAAO,YAA5B,OAAA,IAAyC,CAAA,GAAA,EAAA,GAApB,EAAO,YAAa,EAAA,IAA9B,GAA8B,EACjC,EAAc,EAAO,GAAQ,EAEnC,IAAI,EACF,MAAO,KAMb,iBAAkB,SAAA,GAAmD,GAAvC,GAAuC,EAAvC,YAAa,EAA0B,EAA1B,UAAW,EAAe,EAAf,WACpD,KAAK,cAAc,KAAK,GACtB,MAAO,KAGT,KAAA,GAAA,GAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,EACxC,EAAU,CAEd,IAAI,EAAY,YAAc,EAAY,WAAW,aAC7C,EAAY,cAAgB,EAClC,KAAO,GAAS,CAEd,GAAI,IAAY,EAAY,QAC1B,MAAO,EAET,GAAU,EAAM,WAAW,IAKjC,MAAO,OAIT,WAAY,SAAA,GAAiD,GAArC,GAAqC,EAArC,UAAW,EAA0B,EAA1B,YAAa,EAAa,EAAb,SAC9C,IAAoB,UAAhB,GAA2C,QAAhB,EAC7B,MAAO,KAKT,KAAA,GAFI,OAAA,GAEJ,EAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAC5C,IAAI,EAAY,cAAgB,EAAa,CAE3C,GAAI,EAAY,aAAe,EAAM,SAAS,EAAY,WAAY,GAAc,QAGpF,IAAI,EAAY,cACd,MAAO,EAGC,KACR,EAAiB,IAOvB,GAAI,EACF,MAAO,EAMT,KAAA,GAAA,GAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAC5C,MAAI,EAAY,cAAgB,GAAiB,QAAQ,KAAK,IAAc,EAAY,YACtF,MAAO,GAIX,MAAO,OAIT,WAAY,SAAA,GACV,IAAA,GADsB,GAAa,EAAb,UACtB,EAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAC5C,IAAI,EAAM,SAAS,EAAY,WAAY,GACzC,MAAO,KAMb,KAAM,SAAA,GACJ,IAAA,GADgB,GAAe,EAAf,YAChB,EAAA,EAAA,EAA0B,EAAM,aAAhC,OAAA,IAA8C,CAAA,GAAA,EAAA,GAApB,EAAM,aAAc,EAAA,IAAnC,GAAmC,CAE5C,IAAsC,IAAlC,EAAY,WAAW,OAAc,CACvC,GAAM,GAAS,EAAY,MAG3B,IAAI,IAAW,EAAO,QAAQ,QAAQ,QACpC,aAIC,IAAI,EAAY,WAAW,QAAU,EACxC,QAGF,KAAK,EAAY,eAAkB,IAAgB,EAAY,YAC7D,MAAO,GAIX,MAAO,OAIX,GAAO,QAAU,kRCzHX,EAAa,EAAQ,YACrB,EAAa,EAAQ,cAErB,GACJ,MAAU,aAEV,OAAU,SAAA,GAAA,MAAS,KAAU,EAAI,QAAU,EAAS,IAEpD,QAAU,SAAA,GAAA,MAAS,GAAG,OAAO,IAA6B,KAAnB,EAAM,UAE7C,OAAU,SAAA,GAAA,QAAW,GAA2B,gBAAjB,KAAO,EAAP,YAAA,EAAO,KAEtC,SAAU,SAAA,GAAA,MAA0B,kBAAV,IAE1B,OAAU,SAAA,GAAA,MAA0B,gBAAV,IAE1B,KAAU,SAAA,GAAA,MAA0B,iBAAV,IAE1B,OAAU,SAAA,GAAA,MAA0B,gBAAV,IAE1B,QAAS,SAAA,GACP,IAAK,GAA2B,gBAAjB,KAAO,EAAP,YAAA,EAAO,IAAuB,OAAO,CAEpD,IAAM,GAAU,EAAI,UAAU,IAAU,EAAI,MAE5C,OAAQ,kBAAkB,KAAlB,EAA8B,EAAQ,UAC1C,YAAiB,GAAQ,QACN,IAAnB,EAAM,UAA4C,gBAAnB,GAAM,UAG3C,YAAa,SAAA,GAAA,MAAS,GAAG,OAAO,IAAqC,WAA3B,EAAM,YAAY,MAG9D,GAAG,MAAQ,SAAA,GAAA,MAAU,GAAG,OAAO,QACD,KAAjB,EAAM,QACd,EAAG,SAAS,EAAM,SAEvB,EAAO,QAAU,oECrCjB,GAAO,QAAU,SAAC,GAAD,SAAc,IAAS,EAAM,SAAY,YAAiB,GAAM,6CCAjF,SAAS,GAAe,EAAM,GAC5B,IAAK,GAAM,KAAQ,GAAQ,CACzB,GAAM,GAAkB,EAAO,QAAQ,gBACnC,GAAa,CAGjB,KAAK,GAAM,KAAU,GACnB,GAA6B,IAAzB,EAAK,QAAQ,IAAiB,EAAgB,GAAQ,KAAK,GAAO,CACpE,GAAa,CACb,OAIC,GAAsC,kBAAjB,GAAO,KAC/B,EAAK,GAAQ,EAAO,IAGxB,MAAO,GAGT,EAAc,iBACZ,OAAQ,kDAGV,EAAO,QAAU,uCCxBjB,IAAM,GAAgB,EAAQ,WACxB,EAAgB,EAAQ,aACxB,EAAgB,EAAQ,gBACxB,EAAgB,EAAQ,cACxB,EAAgB,EAAQ,gBACxB,EAAgB,EAAQ,QACxB,EAAgB,EAAQ,mBAExB,GACJ,WAAY,SAAU,EAAM,GAC1B,EAAK,KAAO,EAAK,SACjB,EAAK,KAAK,EAAI,EAAI,KAAK,EACvB,EAAK,KAAK,EAAI,EAAI,KAAK,EAEvB,EAAK,OAAS,EAAK,WACnB,EAAK,OAAO,EAAI,EAAI,OAAO,EAC3B,EAAK,OAAO,EAAI,EAAI,OAAO,EAE3B,EAAK,UAAY,EAAI,WAGvB,eAAgB,SAAU,EAAW,EAAM,GACzC,EAAU,KAAK,EAAO,EAAI,KAAK,EAAO,EAAK,KAAK,EAChD,EAAU,KAAK,EAAO,EAAI,KAAK,EAAO,EAAK,KAAK,EAChD,EAAU,OAAO,EAAK,EAAI,OAAO,EAAK,EAAK,OAAO,EAClD,EAAU,OAAO,EAAK,EAAI,OAAO,EAAK,EAAK,OAAO,EAClD,EAAU,UAAY,EAAI,UAAY,EAAK,SAG3C,IAAM,GAAK,KAAK,IAAI,EAAU,UAAY,IAAM,KAEhD,GAAU,KAAK,MAAU,EAAM,EAAU,KAAK,EAAG,EAAU,KAAK,GAAK,EACrE,EAAU,KAAK,GAAU,EAAU,KAAK,EAAI,EAC5C,EAAU,KAAK,GAAU,EAAU,KAAK,EAAI,EAE5C,EAAU,OAAO,MAAQ,EAAM,EAAU,OAAO,EAAG,EAAU,KAAK,GAAK,EACvE,EAAU,OAAO,GAAQ,EAAU,OAAO,EAAI,EAC9C,EAAU,OAAO,GAAQ,EAAU,OAAO,EAAI,GAGhD,gBAAiB,SAAW,GAC1B,MAAQ,aAAmB,GAAI,OAAS,YAAmB,GAAI,OAIjE,MAAO,SAAU,EAAM,EAAS,GAO9B,MANA,GAAK,MACL,EAAO,GAAQ,OAEf,EAAG,EAAI,EAAQ,EAAO,KACtB,EAAG,EAAI,EAAQ,EAAO,KAEf,GAGT,UAAW,SAAU,EAAS,GAc5B,MAbA,GAAO,MAGH,EAAQ,eAAiB,EAAa,gBAAgB,IACxD,EAAa,MAAM,SAAU,EAAS,GAEtC,EAAK,GAAK,OAAO,QACjB,EAAK,GAAK,OAAO,SAGjB,EAAa,MAAM,OAAQ,EAAS,GAG/B,GAGT,YAAa,SAAU,EAAS,GAW9B,MAVA,GAAS,MAEL,EAAQ,eAAiB,EAAa,gBAAgB,GAExD,EAAa,MAAM,SAAU,EAAS,GAGtC,EAAa,MAAM,SAAU,EAAS,GAGjC,GAGT,aAAc,SAAU,GACtB,MAAO,GAAG,OAAO,EAAQ,WAAY,EAAQ,UAAY,EAAQ,YAGnE,UAAW,SAAU,EAAW,EAAU,GACxC,GAAM,GAAW,EAAS,OAAS,EAChB,EAAa,eAAe,GAC5B,EAAS,GAEtB,IAEN,GAAa,UAAU,EAAS,GAChC,EAAU,KAAK,EAAI,EAAM,EACzB,EAAU,KAAK,EAAI,EAAM,EAEzB,EAAa,YAAY,EAAS,GAClC,EAAU,OAAO,EAAI,EAAM,EAC3B,EAAU,OAAO,EAAI,EAAM,EAE3B,EAAU,UAAY,EAAG,OAAO,GAAa,GAAW,GAAI,OAAO,WAGrE,cAAe,EAEf,aAAc,SAAU,GACtB,GAAM,KAyBN,OAtBI,GAAG,MAAM,IACX,EAAQ,GAAK,EAAM,GACnB,EAAQ,GAAK,EAAM,IAIA,aAAf,EAAM,KACqB,IAAzB,EAAM,QAAQ,QAChB,EAAQ,GAAK,EAAM,QAAQ,GAC3B,EAAQ,GAAK,EAAM,eAAe,IAEF,IAAzB,EAAM,QAAQ,SACrB,EAAQ,GAAK,EAAM,eAAe,GAClC,EAAQ,GAAK,EAAM,eAAe,KAIpC,EAAQ,GAAK,EAAM,QAAQ,GAC3B,EAAQ,GAAK,EAAM,QAAQ,IAIxB,GAGT,eAAgB,SAAU,GAUxB,IAAA,GATM,IACJ,MAAS,EACT,MAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,GAGX,EAAA,EAAA,EAAsB,EAAtB,OAAA,IAAgC,CAAA,GAAA,EAAA,GAAV,EAAU,EAAA,IAArB,GAAqB,CAC9B,KAAK,GAAM,KAAQ,GACjB,EAAQ,IAAS,EAAQ,GAG7B,IAAK,GAAM,KAAQ,GACjB,EAAQ,IAAS,EAAS,MAG5B,OAAO,IAGT,UAAW,SAAU,GACnB,GAAK,EAAM,QAAY,EAAM,SAAW,EAAM,QAAQ,OAAS,EAA/D,CAIA,GAAM,GAAU,EAAa,aAAa,GACpC,EAAO,KAAK,IAAI,EAAQ,GAAG,MAAO,EAAQ,GAAG,OAC7C,EAAO,KAAK,IAAI,EAAQ,GAAG,MAAO,EAAQ,GAAG,MAInD,QACE,EAAG,EACH,EAAG,EACH,KAAM,EACN,IAAK,EACL,MARW,KAAK,IAAI,EAAQ,GAAG,MAAO,EAAQ,GAAG,OAQnC,EACd,OARW,KAAK,IAAI,EAAQ,GAAG,MAAO,EAAQ,GAAG,OAQlC,KAInB,cAAe,SAAU,EAAO,GAC9B,GAAM,GAAU,EAAc,IACxB,EAAU,EAAc,IACxB,EAAU,EAAa,aAAa,GAGpC,EAAK,EAAQ,GAAG,GAAW,EAAQ,GAAG,GACtC,EAAK,EAAQ,GAAG,GAAW,EAAQ,GAAG,EAE5C,OAAO,GAAM,EAAI,IAGnB,WAAY,SAAU,EAAO,EAAW,GACtC,GAAM,GAAU,EAAc,IACxB,EAAU,EAAc,IACxB,EAAU,EAAa,aAAa,GACpC,EAAK,EAAQ,GAAG,GAAW,EAAQ,GAAG,GACtC,EAAK,EAAQ,GAAG,GAAW,EAAQ,GAAG,EAG5C,OAFc,KAAM,KAAK,MAAM,EAAK,GAAM,KAAK,IAKjD,eAAgB,SAAU,GACxB,MAAO,GAAG,OAAO,EAAQ,aACrB,EAAQ,YACR,EAAG,OAAO,EAAQ,kBACf,OAAW,GAAU,QAAS,MAAO,SAAS,EAAQ,aAGrD,QAAQ,KAAK,EAAQ,OAAS,YAAmB,GAAW,MAC1D,QACA,SAIZ,gBAAiB,SAAU,GACzB,GAAM,GAAO,EAAG,SAAS,EAAM,cAAgB,EAAM,eAAiB,EAAM,IAE5E,QACE,EAAS,iBAAiB,EAAO,EAAK,GAAK,EAAM,QACjD,EAAS,iBAAiB,EAAM,iBAKtC,GAAO,QAAU,mIC7NjB,KAAK,MAPc,EAAQ,YAAnB,IAAA,OAEF,GAAW,KAAM,MAAO,SAAU,KACpC,EAAW,EACX,MAAA,GACA,MAAA,GAEK,EAAI,EAAG,EAAI,EAAQ,SAAW,EAAO,sBAAuB,IACnE,EAAU,EAAO,EAAQ,GAAK,yBAC9B,EAAS,EAAO,EAAQ,GAAI,yBAA2B,EAAO,EAAQ,GAAK,8BAGxE,KACH,EAAU,SAAU,GAClB,GAAM,IAAW,GAAI,OAAO,UACtB,EAAa,KAAK,IAAI,EAAG,IAAM,EAAW,IAC1C,EAAK,WAAW,WAAc,EAAS,EAAW,IAClC,EAGtB,OADA,GAAW,EAAW,EACf,IAIN,IACH,EAAS,SAAU,GACjB,aAAa,KAIjB,EAAO,SACL,QAAA,EACA,OAAA,qDChCF,IAAM,GAAS,EAAQ,YACjB,EAAK,EAAQ,UAKf,EAAQ,cAHV,IAAA,QACA,IAAA,WACA,IAAA,eAGI,GACJ,sBAAuB,SAAU,EAAO,EAAc,GACpD,MAAK,GAAG,OAAO,GAKb,EADY,WAAV,EACM,EAAW,GAEF,SAAV,EACC,EAAa,QAAQ,GAGrB,EAAQ,EAAS,GAVlB,MAgBX,gBAAiB,SAAU,EAAO,EAAc,EAAS,GAWvD,MAVA,GAAQ,EAAU,sBAAsB,EAAO,EAAc,IAAY,EAErE,EAAG,SAAS,KACd,EAAQ,EAAM,MAAM,KAAM,IAGxB,EAAG,QAAQ,KACb,EAAQ,EAAe,IAGlB,GAGT,SAAU,SAAU,GAClB,MAAQ,KACN,EAAG,KAAO,GAAO,EAAK,EAAI,EAAK,KAC/B,EAAG,KAAO,GAAO,EAAK,EAAI,EAAK,MAInC,WAAY,SAAU,GAUpB,OATI,GAAU,QAAU,IAAQ,OAAS,KACvC,EAAO,KAAW,GAElB,EAAK,KAAS,EAAK,GAAK,EACxB,EAAK,IAAS,EAAK,GAAK,EACxB,EAAK,MAAS,EAAK,OAAY,EAAK,KAAO,EAAK,MAChD,EAAK,OAAS,EAAK,QAAY,EAAK,IAAM,EAAK,QAG1C,GAGT,WAAY,SAAU,GAUpB,OATI,GAAU,KAAO,IAAQ,KAAO,KAClC,EAAO,KAAW,GAElB,EAAK,EAAS,EAAK,MAAQ,EAC3B,EAAK,IAAS,EAAK,KAAQ,EAC3B,EAAK,MAAS,EAAK,OAAW,EAAK,MAAS,EAAK,EACjD,EAAK,OAAS,EAAK,QAAW,EAAK,OAAS,EAAK,GAG5C,GAIX,GAAO,QAAU,8ECxEjB,SAAS,GAAM,GAGb,EAAI,WAAa,CAGjB,IAAM,GAAK,EAAO,SAAS,eAAe,GAGtC,GAAG,gBAAkB,EAAO,UACF,kBAAhB,GAAO,MAChB,EAAO,KAAK,KAAQ,IAEvB,EAAS,EAAO,KAAK,IAGvB,EAAI,OAAS,EAnBf,GAAM,GAAM,EAAO,QACb,EAAW,EAAQ,aAqBH,oBAAX,SACT,EAAI,WAAa,GACjB,EAAI,eAAa,IAGjB,EAAK,QAGP,EAAI,UAAY,SAAoB,GAClC,GAAI,EAAS,GACX,MAAO,EAGT,IAAM,GAAY,EAAK,eAAiB,CAExC,OAAO,GAAS,aAAe,EAAS,cAAgB,EAAI,QAG9D,EAAI,KAAO", "file": "interact.min.js", "sourceRoot": "", "sourcesContent": ["/* interact.js v1.3.4 | https://raw.github.com/taye/interact.js/master/LICENSE */\n", "/**\n * interact.js v1.3.4\n *\n * Copyright (c) 2012-2018 <PERSON><PERSON> <<EMAIL>>\n * Released under the MIT License.\n * https://raw.github.com/taye/interact.js/master/LICENSE\n */\n", "/*\n * In a (windowless) server environment this file exports a factory function\n * that takes the window to use.\n *\n *     var interact = require('interact.js')(windowObject);\n *\n * See https://github.com/taye/interact.js/issues/187\n */\nif (typeof window === 'undefined') {\n  module.exports = function (window) {\n    require('./src/utils/window').init(window);\n\n    return require('./src/index');\n  };\n}\nelse {\n  module.exports = require('./src/index');\n}\n", "const extend = require('./utils/extend.js');\n\nfunction fireUntilImmediateStopped (event, listeners) {\n  for (const listener of listeners) {\n    if (event.immediatePropagationStopped) { break; }\n\n    listener(event);\n  }\n}\n\nclass Eventable {\n\n  constructor (options) {\n    this.options = extend({}, options || {});\n  }\n\n  fire (event) {\n    let listeners;\n    const onEvent = 'on' + event.type;\n    const global = this.global;\n\n    // Interactable#on() listeners\n    if ((listeners = this[event.type])) {\n      fireUntilImmediateStopped(event, listeners);\n    }\n\n    // interactable.onevent listener\n    if (this[onEvent]) {\n      this[onEvent](event);\n    }\n\n    // interact.on() listeners\n    if (!event.propagationStopped && global && (listeners = global[event.type]))  {\n      fireUntilImmediateStopped(event, listeners);\n    }\n  }\n\n  on (eventType, listener) {\n    // if this type of event was never bound\n    if (this[eventType]) {\n      this[eventType].push(listener);\n    }\n    else {\n      this[eventType] = [listener];\n    }\n  }\n\n  off (eventType, listener) {\n    // if it is an action event type\n    const eventList = this[eventType];\n    const index     = eventList? eventList.indexOf(listener) : -1;\n\n    if (index !== -1) {\n      eventList.splice(index, 1);\n    }\n\n    if (eventList && eventList.length === 0 || !listener) {\n      this[eventType] = undefined;\n    }\n  }\n}\n\nmodule.exports = Eventable;\n", "const extend      = require('./utils/extend');\nconst getOriginXY = require('./utils/getOriginXY');\nconst defaults    = require('./defaultOptions');\nconst signals     = require('./utils/Signals').new();\n\nclass InteractEvent {\n  /** */\n  constructor (interaction, event, action, phase, element, related, preEnd = false) {\n    const target      = interaction.target;\n    const deltaSource = (target && target.options || defaults).deltaSource;\n    const origin      = getOriginXY(target, element, action);\n    const starting    = phase === 'start';\n    const ending      = phase === 'end';\n    const coords      = starting? interaction.startCoords : interaction.curCoords;\n    const prevEvent   = interaction.prevEvent;\n\n    element = element || interaction.element;\n\n    const page   = extend({}, coords.page);\n    const client = extend({}, coords.client);\n\n    page.x -= origin.x;\n    page.y -= origin.y;\n\n    client.x -= origin.x;\n    client.y -= origin.y;\n\n    this.ctrlKey       = event.ctrlKey;\n    this.altKey        = event.altKey;\n    this.shiftKey      = event.shiftKey;\n    this.metaKey       = event.metaKey;\n    this.button        = event.button;\n    this.buttons       = event.buttons;\n    this.target        = element;\n    this.currentTarget = element;\n    this.relatedTarget = related || null;\n    this.preEnd        = preEnd;\n    this.type          = action + (phase || '');\n    this.interaction   = interaction;\n    this.interactable  = target;\n\n    this.t0 = starting ? interaction.downTimes[interaction.downTimes.length - 1]\n                       : prevEvent.t0;\n\n    const signalArg = {\n      interaction,\n      event,\n      action,\n      phase,\n      element,\n      related,\n      page,\n      client,\n      coords,\n      starting,\n      ending,\n      deltaSource,\n      iEvent: this,\n    };\n\n    signals.fire('set-xy', signalArg);\n\n    if (ending) {\n      // use previous coords when ending\n      this.pageX = prevEvent.pageX;\n      this.pageY = prevEvent.pageY;\n      this.clientX = prevEvent.clientX;\n      this.clientY = prevEvent.clientY;\n    }\n    else {\n      this.pageX     = page.x;\n      this.pageY     = page.y;\n      this.clientX   = client.x;\n      this.clientY   = client.y;\n    }\n\n    this.x0        = interaction.startCoords.page.x - origin.x;\n    this.y0        = interaction.startCoords.page.y - origin.y;\n    this.clientX0  = interaction.startCoords.client.x - origin.x;\n    this.clientY0  = interaction.startCoords.client.y - origin.y;\n\n    signals.fire('set-delta', signalArg);\n\n    this.timeStamp = coords.timeStamp;\n    this.dt        = interaction.pointerDelta.timeStamp;\n    this.duration  = this.timeStamp - this.t0;\n\n    // speed and velocity in pixels per second\n    this.speed = interaction.pointerDelta[deltaSource].speed;\n    this.velocityX = interaction.pointerDelta[deltaSource].vx;\n    this.velocityY = interaction.pointerDelta[deltaSource].vy;\n\n    this.swipe = (ending || phase === 'inertiastart')? this.getSwipe() : null;\n\n    signals.fire('new', signalArg);\n  }\n\n  getSwipe () {\n    const interaction = this.interaction;\n\n    if (interaction.prevEvent.speed < 600\n        || this.timeStamp - interaction.prevEvent.timeStamp > 150) {\n      return null;\n    }\n\n    let angle = 180 * Math.atan2(interaction.prevEvent.velocityY, interaction.prevEvent.velocityX) / Math.PI;\n    const overlap = 22.5;\n\n    if (angle < 0) {\n      angle += 360;\n    }\n\n    const left = 135 - overlap <= angle && angle < 225 + overlap;\n    const up   = 225 - overlap <= angle && angle < 315 + overlap;\n\n    const right = !left && (315 - overlap <= angle || angle <  45 + overlap);\n    const down  = !up   &&   45 - overlap <= angle && angle < 135 + overlap;\n\n    return {\n      up,\n      down,\n      left,\n      right,\n      angle,\n      speed: interaction.prevEvent.speed,\n      velocity: {\n        x: interaction.prevEvent.velocityX,\n        y: interaction.prevEvent.velocityY,\n      },\n    };\n  }\n\n  preventDefault () {}\n\n  /** */\n  stopImmediatePropagation () {\n    this.immediatePropagationStopped = this.propagationStopped = true;\n  }\n\n  /** */\n  stopPropagation () {\n    this.propagationStopped = true;\n  }\n}\n\nsignals.on('set-delta', function ({ iEvent, interaction, starting, deltaSource }) {\n  const prevEvent = starting? iEvent : interaction.prevEvent;\n\n  if (deltaSource === 'client') {\n    iEvent.dx = iEvent.clientX - prevEvent.clientX;\n    iEvent.dy = iEvent.clientY - prevEvent.clientY;\n  }\n  else {\n    iEvent.dx = iEvent.pageX - prevEvent.pageX;\n    iEvent.dy = iEvent.pageY - prevEvent.pageY;\n  }\n});\n\nInteractEvent.signals = signals;\n\nmodule.exports = InteractEvent;\n", "const clone     = require('./utils/clone');\nconst is        = require('./utils/is');\nconst events    = require('./utils/events');\nconst extend    = require('./utils/extend');\nconst actions   = require('./actions/base');\nconst scope     = require('./scope');\nconst Eventable = require('./Eventable');\nconst defaults  = require('./defaultOptions');\nconst signals   = require('./utils/Signals').new();\n\nconst {\n  getElementRect,\n  nodeContains,\n  trySelector,\n  matchesSelector,\n}                    = require('./utils/domUtils');\nconst { getWindow }  = require('./utils/window');\nconst { contains }   = require('./utils/arr');\nconst { wheelEvent } = require('./utils/browser');\n\n// all set interactables\nscope.interactables = [];\n\nclass Interactable {\n  /** */\n  constructor (target, options) {\n    options = options || {};\n\n    this.target   = target;\n    this.events   = new Eventable();\n    this._context = options.context || scope.document;\n    this._win     = getWindow(trySelector(target)? this._context : target);\n    this._doc     = this._win.document;\n\n    signals.fire('new', {\n      target,\n      options,\n      interactable: this,\n      win: this._win,\n    });\n\n    scope.addDocument( this._doc, this._win );\n\n    scope.interactables.push(this);\n\n    this.set(options);\n  }\n\n  setOnEvents (action, phases) {\n    const onAction = 'on' + action;\n\n    if (is.function(phases.onstart)       ) { this.events[onAction + 'start'        ] = phases.onstart         ; }\n    if (is.function(phases.onmove)        ) { this.events[onAction + 'move'         ] = phases.onmove          ; }\n    if (is.function(phases.onend)         ) { this.events[onAction + 'end'          ] = phases.onend           ; }\n    if (is.function(phases.oninertiastart)) { this.events[onAction + 'inertiastart' ] = phases.oninertiastart  ; }\n\n    return this;\n  }\n\n  setPerAction (action, options) {\n    // for all the default per-action options\n    for (const option in options) {\n      // if this option exists for this action\n      if (option in defaults[action]) {\n        // if the option in the options arg is an object value\n        if (is.object(options[option])) {\n          // duplicate the object and merge\n          this.options[action][option] = clone(this.options[action][option] || {});\n          extend(this.options[action][option], options[option]);\n\n          if (is.object(defaults.perAction[option]) && 'enabled' in defaults.perAction[option]) {\n            this.options[action][option].enabled = options[option].enabled === false? false : true;\n          }\n        }\n        else if (is.bool(options[option]) && is.object(defaults.perAction[option])) {\n          this.options[action][option].enabled = options[option];\n        }\n        else if (options[option] !== undefined) {\n          // or if it's not undefined, do a plain assignment\n          this.options[action][option] = options[option];\n        }\n      }\n    }\n  }\n\n  /**\n   * The default function to get an Interactables bounding rect. Can be\n   * overridden using {@link Interactable.rectChecker}.\n   *\n   * @param {Element} [element] The element to measure.\n   * @return {object} The object's bounding rectangle.\n   */\n  getRect (element) {\n    element = element || this.target;\n\n    if (is.string(this.target) && !(is.element(element))) {\n      element = this._context.querySelector(this.target);\n    }\n\n    return getElementRect(element);\n  }\n\n  /**\n   * Returns or sets the function used to calculate the interactable's\n   * element's rectangle\n   *\n   * @param {function} [checker] A function which returns this Interactable's\n   * bounding rectangle. See {@link Interactable.getRect}\n   * @return {function | object} The checker function or this Interactable\n   */\n  rectChecker (checker) {\n    if (is.function(checker)) {\n      this.getRect = checker;\n\n      return this;\n    }\n\n    if (checker === null) {\n      delete this.options.getRect;\n\n      return this;\n    }\n\n    return this.getRect;\n  }\n\n  _backCompatOption (optionName, newValue) {\n    if (trySelector(newValue) || is.object(newValue)) {\n      this.options[optionName] = newValue;\n\n      for (const action of actions.names) {\n        this.options[action][optionName] = newValue;\n      }\n\n      return this;\n    }\n\n    return this.options[optionName];\n  }\n\n  /**\n   * Gets or sets the origin of the Interactable's element.  The x and y\n   * of the origin will be subtracted from action event coordinates.\n   *\n   * @param {Element | object | string} [origin] An HTML or SVG Element whose\n   * rect will be used, an object eg. { x: 0, y: 0 } or string 'parent', 'self'\n   * or any CSS selector\n   *\n   * @return {object} The current origin or this Interactable\n   */\n  origin (newValue) {\n    return this._backCompatOption('origin', newValue);\n  }\n\n  /**\n   * Returns or sets the mouse coordinate types used to calculate the\n   * movement of the pointer.\n   *\n   * @param {string} [newValue] Use 'client' if you will be scrolling while\n   * interacting; Use 'page' if you want autoScroll to work\n   * @return {string | object} The current deltaSource or this Interactable\n   */\n  deltaSource (newValue) {\n    if (newValue === 'page' || newValue === 'client') {\n      this.options.deltaSource = newValue;\n\n      return this;\n    }\n\n    return this.options.deltaSource;\n  }\n\n  /**\n   * Gets the selector context Node of the Interactable. The default is\n   * `window.document`.\n   *\n   * @return {Node} The context Node of this Interactable\n   */\n  context () {\n    return this._context;\n  }\n\n  inContext (element) {\n    return (this._context === element.ownerDocument\n            || nodeContains(this._context, element));\n  }\n\n  /**\n   * Calls listeners for the given InteractEvent type bound globally\n   * and directly to this Interactable\n   *\n   * @param {InteractEvent} iEvent The InteractEvent object to be fired on this\n   * Interactable\n   * @return {Interactable} this Interactable\n   */\n  fire (iEvent) {\n    this.events.fire(iEvent);\n\n    return this;\n  }\n\n  _onOffMultiple (method, eventType, listener, options) {\n    if (is.string(eventType) && eventType.search(' ') !== -1) {\n      eventType = eventType.trim().split(/ +/);\n    }\n\n    if (is.array(eventType)) {\n      for (const type of eventType) {\n        this[method](type, listener, options);\n      }\n\n      return true;\n    }\n\n    if (is.object(eventType)) {\n      for (const prop in eventType) {\n        this[method](prop, eventType[prop], listener);\n      }\n\n      return true;\n    }\n  }\n\n  /**\n   * Binds a listener for an InteractEvent, pointerEvent or DOM event.\n   *\n   * @param {string | array | object} eventType  The types of events to listen\n   * for\n   * @param {function} listener   The function event (s)\n   * @param {object | boolean} [options]    options object or useCapture flag\n   * for addEventListener\n   * @return {object} This Interactable\n   */\n  on (eventType, listener, options) {\n    if (this._onOffMultiple('on', eventType, listener, options)) {\n      return this;\n    }\n\n    if (eventType === 'wheel') { eventType = wheelEvent; }\n\n    if (contains(Interactable.eventTypes, eventType)) {\n      this.events.on(eventType, listener);\n    }\n    // delegated event for selector\n    else if (is.string(this.target)) {\n      events.addDelegate(this.target, this._context, eventType, listener, options);\n    }\n    else {\n      events.add(this.target, eventType, listener, options);\n    }\n\n    return this;\n  }\n\n  /**\n   * Removes an InteractEvent, pointerEvent or DOM event listener\n   *\n   * @param {string | array | object} eventType The types of events that were\n   * listened for\n   * @param {function} listener The listener function to be removed\n   * @param {object | boolean} [options] options object or useCapture flag for\n   * removeEventListener\n   * @return {object} This Interactable\n   */\n  off (eventType, listener, options) {\n    if (this._onOffMultiple('off', eventType, listener, options)) {\n      return this;\n    }\n\n    if (eventType === 'wheel') { eventType = wheelEvent; }\n\n    // if it is an action event type\n    if (contains(Interactable.eventTypes, eventType)) {\n      this.events.off(eventType, listener);\n    }\n    // delegated event\n    else if (is.string(this.target)) {\n      events.removeDelegate(this.target, this._context, eventType, listener, options);\n    }\n    // remove listener from this Interatable's element\n    else {\n      events.remove(this.target, eventType, listener, options);\n    }\n\n    return this;\n  }\n\n  /**\n   * Reset the options of this Interactable\n   *\n   * @param {object} options The new settings to apply\n   * @return {object} This Interactable\n   */\n  set (options) {\n    if (!is.object(options)) {\n      options = {};\n    }\n\n    this.options = clone(defaults.base);\n\n    const perActions = clone(defaults.perAction);\n\n    for (const actionName in actions.methodDict) {\n      const methodName = actions.methodDict[actionName];\n\n      this.options[actionName] = clone(defaults[actionName]);\n\n      this.setPerAction(actionName, perActions);\n\n      this[methodName](options[actionName]);\n    }\n\n    for (const setting of Interactable.settingsMethods) {\n      this.options[setting] = defaults.base[setting];\n\n      if (setting in options) {\n        this[setting](options[setting]);\n      }\n    }\n\n    signals.fire('set', {\n      options,\n      interactable: this,\n    });\n\n    return this;\n  }\n\n  /**\n   * Remove this interactable from the list of interactables and remove it's\n   * action capabilities and event listeners\n   *\n   * @return {interact}\n   */\n  unset () {\n    events.remove(this.target, 'all');\n\n    if (is.string(this.target)) {\n      // remove delegated events\n      for (const type in events.delegatedEvents) {\n        const delegated = events.delegatedEvents[type];\n\n        if (delegated.selectors[0] === this.target\n            && delegated.contexts[0] === this._context) {\n\n          delegated.selectors.splice(0, 1);\n          delegated.contexts .splice(0, 1);\n          delegated.listeners.splice(0, 1);\n\n          // remove the arrays if they are empty\n          if (!delegated.selectors.length) {\n            delegated[type] = null;\n          }\n        }\n\n        events.remove(this._context, type, events.delegateListener);\n        events.remove(this._context, type, events.delegateUseCapture, true);\n      }\n    }\n    else {\n      events.remove(this, 'all');\n    }\n\n    signals.fire('unset', { interactable: this });\n\n    scope.interactables.splice(scope.interactables.indexOf(this), 1);\n\n    // Stop related interactions when an Interactable is unset\n    for (const interaction of scope.interactions || []) {\n      if (interaction.target === this && interaction.interacting() && !interaction._ending) {\n        interaction.stop();\n      }\n    }\n\n    return scope.interact;\n  }\n}\n\nscope.interactables.indexOfElement = function indexOfElement (target, context) {\n  context = context || scope.document;\n\n  for (let i = 0; i < this.length; i++) {\n    const interactable = this[i];\n\n    if (interactable.target === target && interactable._context === context) {\n      return i;\n    }\n  }\n  return -1;\n};\n\nscope.interactables.get = function interactableGet (element, options, dontCheckInContext) {\n  const ret = this[this.indexOfElement(element, options && options.context)];\n\n  return ret && (is.string(element) || dontCheckInContext || ret.inContext(element))? ret : null;\n};\n\nscope.interactables.forEachMatch = function (element, callback) {\n  for (const interactable of this) {\n    let ret;\n\n    if ((is.string(interactable.target)\n        // target is a selector and the element matches\n        ? (is.element(element) && matchesSelector(element, interactable.target))\n        // target is the element\n        : element === interactable.target)\n        // the element is in context\n      && (interactable.inContext(element))) {\n      ret = callback(interactable);\n    }\n\n    if (ret !== undefined) {\n      return ret;\n    }\n  }\n};\n\n// all interact.js eventTypes\nInteractable.eventTypes = scope.eventTypes = [];\n\nInteractable.signals = signals;\n\nInteractable.settingsMethods = [ 'deltaSource', 'origin', 'preventDefault', 'rectChecker' ];\n\nmodule.exports = Interactable;\n", "const scope      = require('./scope');\nconst utils      = require('./utils');\nconst events     = require('./utils/events');\nconst browser    = require('./utils/browser');\nconst domObjects = require('./utils/domObjects');\nconst finder     = require('./utils/interactionFinder');\nconst signals    = require('./utils/Signals').new();\n\nconst listeners   = {};\nconst methodNames = [\n  'pointerDown', 'pointerMove', 'pointerUp',\n  'updatePointer', 'removePointer',\n];\n\n// for ignoring browser's simulated mouse events\nlet prevTouchTime = 0;\n\n// all active and idle interactions\nscope.interactions = [];\n\nclass Interaction {\n  /** */\n  constructor ({ pointerType }) {\n    this.target        = null; // current interactable being interacted with\n    this.element       = null; // the target element of the interactable\n\n    this.prepared      = {     // action that's ready to be fired on next move event\n      name : null,\n      axis : null,\n      edges: null,\n    };\n\n    // keep track of added pointers\n    this.pointers    = [];\n    this.pointerIds  = [];\n    this.downTargets = [];\n    this.downTimes   = [];\n\n    // Previous native pointer move event coordinates\n    this.prevCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n    // current native pointer move event coordinates\n    this.curCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n\n    // Starting InteractEvent pointer coordinates\n    this.startCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n\n    // Change in coordinates and time of the pointer\n    this.pointerDelta = {\n      page     : { x: 0, y: 0, vx: 0, vy: 0, speed: 0 },\n      client   : { x: 0, y: 0, vx: 0, vy: 0, speed: 0 },\n      timeStamp: 0,\n    };\n\n    this.downEvent   = null;    // pointerdown/mousedown/touchstart event\n    this.downPointer = {};\n\n    this._eventTarget    = null;\n    this._curEventTarget = null;\n\n    this.prevEvent = null;      // previous action event\n\n    this.pointerIsDown   = false;\n    this.pointerWasMoved = false;\n    this._interacting    = false;\n    this._ending         = false;\n\n    this.pointerType = pointerType;\n\n    signals.fire('new', this);\n\n    scope.interactions.push(this);\n  }\n\n  pointerDown (pointer, event, eventTarget) {\n    const pointerIndex = this.updatePointer(pointer, event, true);\n\n    signals.fire('down', {\n      pointer,\n      event,\n      eventTarget,\n      pointerIndex,\n      interaction: this,\n    });\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable({\n   *     // disable the default drag start by down->move\n   *     manualStart: true\n   *   })\n   *   // start dragging after the user holds the pointer down\n   *   .on('hold', function (event) {\n   *     var interaction = event.interaction;\n   *\n   *     if (!interaction.interacting()) {\n   *       interaction.start({ name: 'drag' },\n   *                         event.interactable,\n   *                         event.currentTarget);\n   *     }\n   * });\n   * ```\n   *\n   * Start an action with the given Interactable and Element as tartgets. The\n   * action must be enabled for the target Interactable and an appropriate\n   * number of pointers must be held down - 1 for drag/resize, 2 for gesture.\n   *\n   * Use it with `interactable.<action>able({ manualStart: false })` to always\n   * [start actions manually](https://github.com/taye/interact.js/issues/114)\n   *\n   * @param {object} action   The action to be performed - drag, resize, etc.\n   * @param {Interactable} target  The Interactable to target\n   * @param {Element} element The DOM Element to target\n   * @return {object} interact\n   */\n  start (action, target, element) {\n    if (this.interacting()\n        || !this.pointerIsDown\n        || this.pointerIds.length < (action.name === 'gesture'? 2 : 1)) {\n      return;\n    }\n\n    // if this interaction had been removed after stopping\n    // add it back\n    if (scope.interactions.indexOf(this) === -1) {\n      scope.interactions.push(this);\n    }\n\n    utils.copyAction(this.prepared, action);\n    this.target         = target;\n    this.element        = element;\n\n    signals.fire('action-start', {\n      interaction: this,\n      event: this.downEvent,\n    });\n  }\n\n  pointerMove (pointer, event, eventTarget) {\n    if (!this.simulation) {\n      this.updatePointer(pointer);\n      utils.setCoords(this.curCoords, this.pointers);\n    }\n\n    const duplicateMove = (this.curCoords.page.x === this.prevCoords.page.x\n                           && this.curCoords.page.y === this.prevCoords.page.y\n                           && this.curCoords.client.x === this.prevCoords.client.x\n                           && this.curCoords.client.y === this.prevCoords.client.y);\n\n    let dx;\n    let dy;\n\n    // register movement greater than pointerMoveTolerance\n    if (this.pointerIsDown && !this.pointerWasMoved) {\n      dx = this.curCoords.client.x - this.startCoords.client.x;\n      dy = this.curCoords.client.y - this.startCoords.client.y;\n\n      this.pointerWasMoved = utils.hypot(dx, dy) > Interaction.pointerMoveTolerance;\n    }\n\n    const signalArg = {\n      pointer,\n      pointerIndex: this.getPointerIndex(pointer),\n      event,\n      eventTarget,\n      dx,\n      dy,\n      duplicate: duplicateMove,\n      interaction: this,\n      interactingBeforeMove: this.interacting(),\n    };\n\n    if (!duplicateMove) {\n      // set pointer coordinate, time changes and speeds\n      utils.setCoordDeltas(this.pointerDelta, this.prevCoords, this.curCoords);\n    }\n\n    signals.fire('move', signalArg);\n\n    if (!duplicateMove) {\n      // if interacting, fire an 'action-move' signal etc\n      if (this.interacting()) {\n        this.doMove(signalArg);\n      }\n\n      if (this.pointerWasMoved) {\n        utils.copyCoords(this.prevCoords, this.curCoords);\n      }\n    }\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('dragmove', function (event) {\n   *     if (someCondition) {\n   *       // change the snap settings\n   *       event.interactable.draggable({ snap: { targets: [] }});\n   *       // fire another move event with re-calculated snap\n   *       event.interaction.doMove();\n   *     }\n   *   });\n   * ```\n   *\n   * Force a move of the current action at the same coordinates. Useful if\n   * snap/restrict has been changed and you want a movement with the new\n   * settings.\n   */\n  doMove (signalArg) {\n    signalArg = utils.extend({\n      pointer: this.pointers[0],\n      event: this.prevEvent,\n      eventTarget: this._eventTarget,\n      interaction: this,\n    }, signalArg || {});\n\n    signals.fire('before-action-move', signalArg);\n\n    if (!this._dontFireMove) {\n      signals.fire('action-move', signalArg);\n    }\n\n    this._dontFireMove = false;\n  }\n\n  // End interact move events and stop auto-scroll unless simulation is running\n  pointerUp (pointer, event, eventTarget, curEventTarget) {\n    const pointerIndex = this.getPointerIndex(pointer);\n\n    signals.fire(/cancel$/i.test(event.type)? 'cancel' : 'up', {\n      pointer,\n      pointerIndex,\n      event,\n      eventTarget,\n      curEventTarget,\n      interaction: this,\n    });\n\n    if (!this.simulation) {\n      this.end(event);\n    }\n\n    this.pointerIsDown = false;\n    this.removePointer(pointer, event);\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('move', function (event) {\n   *     if (event.pageX > 1000) {\n   *       // end the current action\n   *       event.interaction.end();\n   *       // stop all further listeners from being called\n   *       event.stopImmediatePropagation();\n   *     }\n   *   });\n   * ```\n   *\n   * Stop the current action and fire an end event. Inertial movement does\n   * not happen.\n   *\n   * @param {PointerEvent} [event]\n   */\n  end (event) {\n    this._ending = true;\n\n    event = event || this.prevEvent;\n\n    if (this.interacting()) {\n      signals.fire('action-end', {\n        event,\n        interaction: this,\n      });\n    }\n\n    this.stop();\n    this._ending = false;\n  }\n\n  currentAction () {\n    return this._interacting? this.prepared.name: null;\n  }\n\n  interacting () {\n    return this._interacting;\n  }\n\n  /** */\n  stop () {\n    signals.fire('stop', { interaction: this });\n\n    if (this._interacting) {\n      signals.fire('stop-active', { interaction: this });\n      signals.fire('stop-' + this.prepared.name, { interaction: this });\n    }\n\n    this.target = this.element = null;\n\n    this._interacting = false;\n    this.prepared.name = this.prevEvent = null;\n  }\n\n  getPointerIndex (pointer) {\n    // mouse and pen interactions may have only one pointer\n    if (this.pointerType === 'mouse' || this.pointerType === 'pen') {\n      return 0;\n    }\n\n    return this.pointerIds.indexOf(utils.getPointerId(pointer));\n  }\n\n  updatePointer (pointer, event, down = event && /(down|start)$/i.test(event.type)) {\n    const id = utils.getPointerId(pointer);\n    let index = this.getPointerIndex(pointer);\n\n    if (index === -1) {\n      index = this.pointerIds.length;\n      this.pointerIds[index] = id;\n    }\n\n    if (down) {\n      signals.fire('update-pointer-down', {\n        pointer,\n        event,\n        down,\n        pointerId: id,\n        pointerIndex: index,\n        interaction: this,\n      });\n    }\n\n    this.pointers[index] = pointer;\n\n    return index;\n  }\n\n  removePointer (pointer, event) {\n    const index = this.getPointerIndex(pointer);\n\n    if (index === -1) { return; }\n\n    signals.fire('remove-pointer', {\n      pointer,\n      event,\n      pointerIndex: index,\n      interaction: this,\n    });\n\n    this.pointers   .splice(index, 1);\n    this.pointerIds .splice(index, 1);\n    this.downTargets.splice(index, 1);\n    this.downTimes  .splice(index, 1);\n  }\n\n  _updateEventTargets (target, currentTarget) {\n    this._eventTarget    = target;\n    this._curEventTarget = currentTarget;\n  }\n}\n\nfor (const method of methodNames) {\n  listeners[method] = doOnInteractions(method);\n}\n\nfunction doOnInteractions (method) {\n  return (function (event) {\n    const pointerType = utils.getPointerType(event);\n    const [eventTarget, curEventTarget] = utils.getEventTargets(event);\n    const matches = []; // [ [pointer, interaction], ...]\n\n    if (browser.supportsTouch && /touch/.test(event.type)) {\n      prevTouchTime = new Date().getTime();\n\n      for (const changedTouch of event.changedTouches) {\n        const pointer = changedTouch;\n        const interaction = finder.search(pointer, event.type, eventTarget);\n\n        matches.push([pointer, interaction || new Interaction({ pointerType })]);\n      }\n    }\n    else {\n      let invalidPointer = false;\n\n      if (!browser.supportsPointerEvent && /mouse/.test(event.type)) {\n        // ignore mouse events while touch interactions are active\n        for (let i = 0; i < scope.interactions.length && !invalidPointer; i++) {\n          invalidPointer = scope.interactions[i].pointerType !== 'mouse' && scope.interactions[i].pointerIsDown;\n        }\n\n        // try to ignore mouse events that are simulated by the browser\n        // after a touch event\n        invalidPointer = invalidPointer\n          || (new Date().getTime() - prevTouchTime < 500)\n          // on iOS and Firefox Mobile, MouseEvent.timeStamp is zero if simulated\n          || event.timeStamp === 0;\n      }\n\n      if (!invalidPointer) {\n        let interaction = finder.search(event, event.type, eventTarget);\n\n        if (!interaction) {\n          interaction = new Interaction({ pointerType });\n        }\n\n        matches.push([event, interaction]);\n      }\n    }\n\n    for (const [pointer, interaction] of matches) {\n      interaction._updateEventTargets(eventTarget, curEventTarget);\n      interaction[method](pointer, event, eventTarget, curEventTarget);\n    }\n  });\n}\n\nfunction endAll (event) {\n  for (const interaction of scope.interactions) {\n    interaction.end(event);\n    signals.fire('endall', { event, interaction });\n  }\n}\n\nconst docEvents = { /* 'eventType': listenerFunc */ };\nconst pEventTypes = browser.pEventTypes;\n\nif (domObjects.PointerEvent) {\n  docEvents[pEventTypes.down  ] = listeners.pointerDown;\n  docEvents[pEventTypes.move  ] = listeners.pointerMove;\n  docEvents[pEventTypes.up    ] = listeners.pointerUp;\n  docEvents[pEventTypes.cancel] = listeners.pointerUp;\n}\nelse {\n  docEvents.mousedown   = listeners.pointerDown;\n  docEvents.mousemove   = listeners.pointerMove;\n  docEvents.mouseup     = listeners.pointerUp;\n\n  docEvents.touchstart  = listeners.pointerDown;\n  docEvents.touchmove   = listeners.pointerMove;\n  docEvents.touchend    = listeners.pointerUp;\n  docEvents.touchcancel = listeners.pointerUp;\n}\n\ndocEvents.blur = endAll;\n\nfunction onDocSignal ({ doc }, signalName) {\n  const eventMethod = signalName.indexOf('add') === 0\n    ? events.add : events.remove;\n\n  // delegate event listener\n  for (const eventType in scope.delegatedEvents) {\n    eventMethod(doc, eventType, events.delegateListener);\n    eventMethod(doc, eventType, events.delegateUseCapture, true);\n  }\n\n  for (const eventType in docEvents) {\n    eventMethod(doc, eventType, docEvents[eventType], browser.isIOS ? { passive: false } : undefined);\n  }\n}\n\nsignals.on('update-pointer-down', ({ interaction, pointer, pointerId, pointerIndex, event, eventTarget, down }) => {\n  interaction.pointerIds[pointerIndex] = pointerId;\n  interaction.pointers[pointerIndex] = pointer;\n\n  if (down) {\n    interaction.pointerIsDown = true;\n  }\n\n  if (!interaction.interacting()) {\n    utils.setCoords(interaction.startCoords, interaction.pointers);\n\n    utils.copyCoords(interaction.curCoords , interaction.startCoords);\n    utils.copyCoords(interaction.prevCoords, interaction.startCoords);\n\n    interaction.downEvent                 = event;\n    interaction.downTimes[pointerIndex]   = interaction.curCoords.timeStamp;\n    interaction.downTargets[pointerIndex] = eventTarget || event && utils.getEventTargets(event)[0];\n    interaction.pointerWasMoved           = false;\n\n    utils.pointerExtend(interaction.downPointer, pointer);\n  }\n});\n\nscope.signals.on('add-document'   , onDocSignal);\nscope.signals.on('remove-document', onDocSignal);\n\nInteraction.pointerMoveTolerance = 1;\nInteraction.doOnInteractions = doOnInteractions;\nInteraction.endAll = endAll;\nInteraction.signals = signals;\nInteraction.docEvents = docEvents;\n\nscope.endAllInteractions = endAll;\n\nmodule.exports = Interaction;\n", "const Interaction   = require('../Interaction');\nconst InteractEvent = require('../InteractEvent');\n\nconst actions = {\n  firePrepared,\n  names: [],\n  methodDict: {},\n};\n\nInteraction.signals.on('action-start', function ({ interaction, event }) {\n  interaction._interacting = true;\n  firePrepared(interaction, event, 'start');\n});\n\nInteraction.signals.on('action-move', function ({ interaction, event, preEnd }) {\n  firePrepared(interaction, event, 'move', preEnd);\n\n  // if the action was ended in a listener\n  if (!interaction.interacting()) { return false; }\n});\n\nInteraction.signals.on('action-end', function ({ interaction, event }) {\n  firePrepared(interaction, event, 'end');\n});\n\nfunction firePrepared (interaction, event, phase, preEnd) {\n  const actionName = interaction.prepared.name;\n\n  const newEvent = new InteractEvent(interaction, event, actionName, phase, interaction.element, null, preEnd);\n\n  interaction.target.fire(newEvent);\n  interaction.prevEvent = newEvent;\n}\n\nmodule.exports = actions;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst drag = {\n  defaults: {\n    enabled     : false,\n    mouseButtons: null,\n\n    origin    : null,\n    snap      : null,\n    restrict  : null,\n    inertia   : null,\n    autoScroll: null,\n\n    startAxis : 'xy',\n    lockAxis  : 'xy',\n  },\n\n  checker: function (pointer, event, interactable) {\n    const dragOptions = interactable.options.drag;\n\n    return dragOptions.enabled\n      ? { name: 'drag', axis: (dragOptions.lockAxis === 'start'\n                               ? dragOptions.startAxis\n                               : dragOptions.lockAxis)}\n      : null;\n  },\n\n  getCursor: function () {\n    return 'move';\n  },\n};\n\nInteraction.signals.on('before-action-move', function ({ interaction }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  const axis = interaction.prepared.axis;\n\n  if (axis === 'x') {\n    interaction.curCoords.page.y   = interaction.startCoords.page.y;\n    interaction.curCoords.client.y = interaction.startCoords.client.y;\n\n    interaction.pointerDelta.page.speed   = Math.abs(interaction.pointerDelta.page.vx);\n    interaction.pointerDelta.client.speed = Math.abs(interaction.pointerDelta.client.vx);\n    interaction.pointerDelta.client.vy = 0;\n    interaction.pointerDelta.page.vy   = 0;\n  }\n  else if (axis === 'y') {\n    interaction.curCoords.page.x   = interaction.startCoords.page.x;\n    interaction.curCoords.client.x = interaction.startCoords.client.x;\n\n    interaction.pointerDelta.page.speed   = Math.abs(interaction.pointerDelta.page.vy);\n    interaction.pointerDelta.client.speed = Math.abs(interaction.pointerDelta.client.vy);\n    interaction.pointerDelta.client.vx = 0;\n    interaction.pointerDelta.page.vx   = 0;\n  }\n});\n\n// dragmove\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'dragmove') { return; }\n\n  const axis = interaction.prepared.axis;\n\n  if (axis === 'x') {\n    iEvent.pageY   = interaction.startCoords.page.y;\n    iEvent.clientY = interaction.startCoords.client.y;\n    iEvent.dy = 0;\n  }\n  else if (axis === 'y') {\n    iEvent.pageX   = interaction.startCoords.page.x;\n    iEvent.clientX = interaction.startCoords.client.x;\n    iEvent.dx = 0;\n  }\n});\n\n/**\n * ```js\n * interact(element).draggable({\n *     onstart: function (event) {},\n *     onmove : function (event) {},\n *     onend  : function (event) {},\n *\n *     // the axis in which the first movement must be\n *     // for the drag sequence to start\n *     // 'xy' by default - any direction\n *     startAxis: 'x' || 'y' || 'xy',\n *\n *     // 'xy' by default - don't restrict to one axis (move in any direction)\n *     // 'x' or 'y' to restrict movement to either axis\n *     // 'start' to restrict movement to the axis the drag started in\n *     lockAxis: 'x' || 'y' || 'xy' || 'start',\n *\n *     // max number of drags that can happen concurrently\n *     // with elements of this Interactable. Infinity by default\n *     max: Infinity,\n *\n *     // max number of drags that can target the same element+Interactable\n *     // 1 by default\n *     maxPerElement: 2\n * });\n *\n * var isDraggable = interact('element').draggable(); // true\n * ```\n *\n * Get or set whether drag actions can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on drag events (object makes the Interactable\n * draggable)\n * @return {boolean | Interactable} boolean indicating if this can be the\n * target of drag events, or this Interctable\n */\nInteractable.prototype.draggable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.drag.enabled = options.enabled === false? false: true;\n    this.setPerAction('drag', options);\n    this.setOnEvents('drag', options);\n\n    if (/^(xy|x|y|start)$/.test(options.lockAxis)) {\n      this.options.drag.lockAxis = options.lockAxis;\n    }\n    if (/^(xy|x|y)$/.test(options.startAxis)) {\n      this.options.drag.startAxis = options.startAxis;\n    }\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.drag.enabled = options;\n\n    if (!options) {\n      this.ondragstart = this.ondragstart = this.ondragend = null;\n    }\n\n    return this;\n  }\n\n  return this.options.drag;\n};\n\nactions.drag = drag;\nactions.names.push('drag');\nutils.merge(Interactable.eventTypes, [\n  'dragstart',\n  'dragmove',\n  'draginertiastart',\n  'draginertiaresume',\n  'dragend',\n]);\nactions.methodDict.drag = 'draggable';\n\ndefaultOptions.drag = drag.defaults;\n\nmodule.exports = drag;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst scope          = require('../scope');\n/** @lends module:interact */\nconst interact       = require('../interact');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst drop = {\n  defaults: {\n    enabled: false,\n    accept : null,\n    overlap: 'pointer',\n  },\n};\n\nlet dynamicDrop = false;\n\nInteraction.signals.on('action-start', function ({ interaction, event }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  // reset active dropzones\n  interaction.activeDrops.dropzones = [];\n  interaction.activeDrops.elements  = [];\n  interaction.activeDrops.rects     = [];\n\n  interaction.dropEvents = null;\n\n  if (!interaction.dynamicDrop) {\n    setActiveDrops(interaction.activeDrops, interaction.element);\n  }\n\n  const dragEvent = interaction.prevEvent;\n  const dropEvents = getDropEvents(interaction, event, dragEvent);\n\n  if (dropEvents.activate) {\n    fireActiveDrops(interaction.activeDrops, dropEvents.activate);\n  }\n});\n\nInteractEvent.signals.on('new', function ({ interaction, iEvent, event }) {\n  if (iEvent.type !== 'dragmove' && iEvent.type !== 'dragend') { return; }\n\n  const draggableElement = interaction.element;\n  const dragEvent = iEvent;\n  const dropResult = getDrop(dragEvent, event, draggableElement);\n\n  interaction.dropTarget  = dropResult.dropzone;\n  interaction.dropElement = dropResult.element;\n\n  interaction.dropEvents = getDropEvents(interaction, event, dragEvent);\n});\n\nInteraction.signals.on('action-move', function ({ interaction }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  fireDropEvents(interaction, interaction.dropEvents);\n});\n\nInteraction.signals.on('action-end', function ({ interaction }) {\n  if (interaction.prepared.name === 'drag') {\n    fireDropEvents(interaction, interaction.dropEvents);\n  }\n});\n\nInteraction.signals.on('stop-drag', function ({ interaction }) {\n  interaction.activeDrops = {\n    dropzones: null,\n    elements: null,\n    rects: null,\n  };\n\n  interaction.dropEvents = null;\n});\n\nfunction collectDrops (activeDrops, element) {\n  const drops = [];\n  const elements = [];\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (const current of scope.interactables) {\n    if (!current.options.drop.enabled) { continue; }\n\n    const accept = current.options.drop.accept;\n\n    // test the draggable element against the dropzone's accept setting\n    if ((utils.is.element(accept) && accept !== element)\n        || (utils.is.string(accept)\n        && !utils.matchesSelector(element, accept))) {\n\n      continue;\n    }\n\n    // query for new elements if necessary\n    const dropElements = utils.is.string(current.target)\n      ? current._context.querySelectorAll(current.target)\n      : [current.target];\n\n    for (const currentElement of dropElements) {\n      if (currentElement !== element) {\n        drops.push(current);\n        elements.push(currentElement);\n      }\n    }\n  }\n\n  return {\n    elements,\n    dropzones: drops,\n  };\n}\n\nfunction fireActiveDrops (activeDrops, event) {\n  let prevElement;\n\n  // loop through all active dropzones and trigger event\n  for (let i = 0; i < activeDrops.dropzones.length; i++) {\n    const current = activeDrops.dropzones[i];\n    const currentElement = activeDrops.elements [i];\n\n    // prevent trigger of duplicate events on same element\n    if (currentElement !== prevElement) {\n      // set current element as event target\n      event.target = currentElement;\n      current.fire(event);\n    }\n    prevElement = currentElement;\n  }\n}\n\n// Collect a new set of possible drops and save them in activeDrops.\n// setActiveDrops should always be called when a drag has just started or a\n// drag event happens while dynamicDrop is true\nfunction setActiveDrops (activeDrops, dragElement) {\n  // get dropzones and their elements that could receive the draggable\n  const possibleDrops = collectDrops(activeDrops, dragElement);\n\n  activeDrops.dropzones = possibleDrops.dropzones;\n  activeDrops.elements  = possibleDrops.elements;\n  activeDrops.rects     = [];\n\n  for (let i = 0; i < activeDrops.dropzones.length; i++) {\n    activeDrops.rects[i] = activeDrops.dropzones[i].getRect(activeDrops.elements[i]);\n  }\n}\n\nfunction getDrop (dragEvent, event, dragElement) {\n  const interaction = dragEvent.interaction;\n  const validDrops = [];\n\n  if (dynamicDrop) {\n    setActiveDrops(interaction.activeDrops, dragElement);\n  }\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (let j = 0; j < interaction.activeDrops.dropzones.length; j++) {\n    const current        = interaction.activeDrops.dropzones[j];\n    const currentElement = interaction.activeDrops.elements [j];\n    const rect           = interaction.activeDrops.rects    [j];\n\n    validDrops.push(current.dropCheck(dragEvent, event, interaction.target, dragElement, currentElement, rect)\n      ? currentElement\n      : null);\n  }\n\n  // get the most appropriate dropzone based on DOM depth and order\n  const dropIndex = utils.indexOfDeepestElement(validDrops);\n\n  return {\n    dropzone: interaction.activeDrops.dropzones[dropIndex] || null,\n    element : interaction.activeDrops.elements [dropIndex] || null,\n  };\n}\n\nfunction getDropEvents (interaction, pointerEvent, dragEvent) {\n  const dropEvents = {\n    enter     : null,\n    leave     : null,\n    activate  : null,\n    deactivate: null,\n    move      : null,\n    drop      : null,\n  };\n\n  const tmpl = {\n    dragEvent,\n    interaction,\n    target       : interaction.dropElement,\n    dropzone     : interaction.dropTarget,\n    relatedTarget: dragEvent.target,\n    draggable    : dragEvent.interactable,\n    timeStamp    : dragEvent.timeStamp,\n  };\n\n  if (interaction.dropElement !== interaction.prevDropElement) {\n    // if there was a prevDropTarget, create a dragleave event\n    if (interaction.prevDropTarget) {\n      dropEvents.leave = utils.extend({ type: 'dragleave' }, tmpl);\n\n      dragEvent.dragLeave    = dropEvents.leave.target   = interaction.prevDropElement;\n      dragEvent.prevDropzone = dropEvents.leave.dropzone = interaction.prevDropTarget;\n    }\n    // if the dropTarget is not null, create a dragenter event\n    if (interaction.dropTarget) {\n      dropEvents.enter = {\n        dragEvent,\n        interaction,\n        target       : interaction.dropElement,\n        dropzone     : interaction.dropTarget,\n        relatedTarget: dragEvent.target,\n        draggable    : dragEvent.interactable,\n        timeStamp    : dragEvent.timeStamp,\n        type         : 'dragenter',\n      };\n\n      dragEvent.dragEnter = interaction.dropElement;\n      dragEvent.dropzone = interaction.dropTarget;\n    }\n  }\n\n  if (dragEvent.type === 'dragend' && interaction.dropTarget) {\n    dropEvents.drop = utils.extend({ type: 'drop' }, tmpl);\n\n    dragEvent.dropzone = interaction.dropTarget;\n    dragEvent.relatedTarget = interaction.dropElement;\n  }\n  if (dragEvent.type === 'dragstart') {\n    dropEvents.activate = utils.extend({ type: 'dropactivate' }, tmpl);\n\n    dropEvents.activate.target   = null;\n    dropEvents.activate.dropzone = null;\n  }\n  if (dragEvent.type === 'dragend') {\n    dropEvents.deactivate = utils.extend({ type: 'dropdeactivate' }, tmpl);\n\n    dropEvents.deactivate.target   = null;\n    dropEvents.deactivate.dropzone = null;\n  }\n  if (dragEvent.type === 'dragmove' && interaction.dropTarget) {\n    dropEvents.move = utils.extend({\n      dragmove     : dragEvent,\n      type         : 'dropmove',\n    }, tmpl);\n\n    dragEvent.dropzone = interaction.dropTarget;\n  }\n\n  return dropEvents;\n}\n\nfunction fireDropEvents (interaction, dropEvents) {\n  const {\n    activeDrops,\n    prevDropTarget,\n    dropTarget,\n    dropElement,\n  } = interaction;\n\n  if (dropEvents.leave) { prevDropTarget.fire(dropEvents.leave); }\n  if (dropEvents.move ) {     dropTarget.fire(dropEvents.move ); }\n  if (dropEvents.enter) {     dropTarget.fire(dropEvents.enter); }\n  if (dropEvents.drop ) {     dropTarget.fire(dropEvents.drop ); }\n  if (dropEvents.deactivate) {\n    fireActiveDrops(activeDrops, dropEvents.deactivate);\n  }\n\n  interaction.prevDropTarget  = dropTarget;\n  interaction.prevDropElement = dropElement;\n}\n\n/**\n * ```js\n * interact(target)\n * .dropChecker(function(dragEvent,         // related dragmove or dragend event\n *                       event,             // TouchEvent/PointerEvent/MouseEvent\n *                       dropped,           // bool result of the default checker\n *                       dropzone,          // dropzone Interactable\n *                       dropElement,       // dropzone elemnt\n *                       draggable,         // draggable Interactable\n *                       draggableElement) {// draggable element\n *\n *   return dropped && event.target.hasAttribute('allow-drop');\n * }\n * ```\n *\n * ```js\n * interact('.drop').dropzone({\n *   accept: '.can-drop' || document.getElementById('single-drop'),\n *   overlap: 'pointer' || 'center' || zeroToOne\n * }\n * ```\n *\n * Returns or sets whether draggables can be dropped onto this target to\n * trigger drop events\n *\n * Dropzones can receive the following events:\n *  - `dropactivate` and `dropdeactivate` when an acceptable drag starts and ends\n *  - `dragenter` and `dragleave` when a draggable enters and leaves the dropzone\n *  - `dragmove` when a draggable that has entered the dropzone is moved\n *  - `drop` when a draggable is dropped into this dropzone\n *\n * Use the `accept` option to allow only elements that match the given CSS\n * selector or element. The value can be:\n *\n *  - **an Element** - only that element can be dropped into this dropzone.\n *  - **a string**, - the element being dragged must match it as a CSS selector.\n *  - **`null`** - accept options is cleared - it accepts any element.\n *\n * Use the `overlap` option to set how drops are checked for. The allowed\n * values are:\n *\n *   - `'pointer'`, the pointer must be over the dropzone (default)\n *   - `'center'`, the draggable element's center must be over the dropzone\n *   - a number from 0-1 which is the `(intersection area) / (draggable area)`.\n *   e.g. `0.5` for drop to happen when half of the area of the draggable is\n *   over the dropzone\n *\n * Use the `checker` option to specify a function to check if a dragged element\n * is over this Interactable.\n *\n * @param {boolean | object | null} [options] The new options to be set.\n * @return {boolean | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.dropzone = function (options) {\n  if (utils.is.object(options)) {\n    this.options.drop.enabled = options.enabled === false? false: true;\n\n    if (utils.is.function(options.ondrop)          ) { this.events.ondrop           = options.ondrop          ; }\n    if (utils.is.function(options.ondropactivate)  ) { this.events.ondropactivate   = options.ondropactivate  ; }\n    if (utils.is.function(options.ondropdeactivate)) { this.events.ondropdeactivate = options.ondropdeactivate; }\n    if (utils.is.function(options.ondragenter)     ) { this.events.ondragenter      = options.ondragenter     ; }\n    if (utils.is.function(options.ondragleave)     ) { this.events.ondragleave      = options.ondragleave     ; }\n    if (utils.is.function(options.ondropmove)      ) { this.events.ondropmove       = options.ondropmove      ; }\n\n    if (/^(pointer|center)$/.test(options.overlap)) {\n      this.options.drop.overlap = options.overlap;\n    }\n    else if (utils.is.number(options.overlap)) {\n      this.options.drop.overlap = Math.max(Math.min(1, options.overlap), 0);\n    }\n    if ('accept' in options) {\n      this.options.drop.accept = options.accept;\n    }\n    if ('checker' in options) {\n      this.options.drop.checker = options.checker;\n    }\n\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.drop.enabled = options;\n\n    if (!options) {\n      this.ondragenter = this.ondragleave = this.ondrop\n        = this.ondropactivate = this.ondropdeactivate = null;\n    }\n\n    return this;\n  }\n\n  return this.options.drop;\n};\n\nInteractable.prototype.dropCheck = function (dragEvent, event, draggable, draggableElement, dropElement, rect) {\n  let dropped = false;\n\n  // if the dropzone has no rect (eg. display: none)\n  // call the custom dropChecker or just return false\n  if (!(rect = rect || this.getRect(dropElement))) {\n    return (this.options.drop.checker\n      ? this.options.drop.checker(dragEvent, event, dropped, this, dropElement, draggable, draggableElement)\n      : false);\n  }\n\n  const dropOverlap = this.options.drop.overlap;\n\n  if (dropOverlap === 'pointer') {\n    const origin = utils.getOriginXY(draggable, draggableElement, 'drag');\n    const page = utils.getPageXY(dragEvent);\n\n    page.x += origin.x;\n    page.y += origin.y;\n\n    const horizontal = (page.x > rect.left) && (page.x < rect.right);\n    const vertical   = (page.y > rect.top ) && (page.y < rect.bottom);\n\n    dropped = horizontal && vertical;\n  }\n\n  const dragRect = draggable.getRect(draggableElement);\n\n  if (dragRect && dropOverlap === 'center') {\n    const cx = dragRect.left + dragRect.width  / 2;\n    const cy = dragRect.top  + dragRect.height / 2;\n\n    dropped = cx >= rect.left && cx <= rect.right && cy >= rect.top && cy <= rect.bottom;\n  }\n\n  if (dragRect && utils.is.number(dropOverlap)) {\n    const overlapArea  = (Math.max(0, Math.min(rect.right , dragRect.right ) - Math.max(rect.left, dragRect.left))\n                          * Math.max(0, Math.min(rect.bottom, dragRect.bottom) - Math.max(rect.top , dragRect.top )));\n\n    const overlapRatio = overlapArea / (dragRect.width * dragRect.height);\n\n    dropped = overlapRatio >= dropOverlap;\n  }\n\n  if (this.options.drop.checker) {\n    dropped = this.options.drop.checker(dragEvent, event, dropped, this, dropElement, draggable, draggableElement);\n  }\n\n  return dropped;\n};\n\nInteractable.signals.on('unset', function ({ interactable }) {\n  interactable.dropzone(false);\n});\n\nInteractable.settingsMethods.push('dropChecker');\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.dropTarget      = null; // the dropzone a drag target might be dropped into\n  interaction.dropElement     = null; // the element at the time of checking\n  interaction.prevDropTarget  = null; // the dropzone that was recently dragged away from\n  interaction.prevDropElement = null; // the element at the time of checking\n  interaction.dropEvents      = null; // the dropEvents related to the current drag event\n\n  interaction.activeDrops = {\n    dropzones: [],      // the dropzones that are mentioned below\n    elements : [],      // elements of dropzones that accept the target draggable\n    rects    : [],      // the rects of the elements mentioned above\n  };\n\n});\n\nInteraction.signals.on('stop', function ({ interaction }) {\n  interaction.dropTarget = interaction.dropElement =\n    interaction.prevDropTarget = interaction.prevDropElement = null;\n});\n\n/**\n * Returns or sets whether the dimensions of dropzone elements are calculated\n * on every dragmove or only on dragstart for the default dropChecker\n *\n * @param {boolean} [newValue] True to check on each move. False to check only\n * before start\n * @return {boolean | interact} The current setting or interact\n */\ninteract.dynamicDrop = function (newValue) {\n  if (utils.is.bool(newValue)) {\n    //if (dragging && dynamicDrop !== newValue && !newValue) {\n      //calcRects(dropzones);\n    //}\n\n    dynamicDrop = newValue;\n\n    return interact;\n  }\n  return dynamicDrop;\n};\n\nutils.merge(Interactable.eventTypes, [\n  'dragenter',\n  'dragleave',\n  'dropactivate',\n  'dropdeactivate',\n  'dropmove',\n  'drop',\n]);\nactions.methodDict.drop = 'dropzone';\n\ndefaultOptions.drop = drop.defaults;\n\nmodule.exports = drop;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst InteractEvent  = require('../InteractEvent');\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst gesture = {\n  defaults: {\n    enabled : false,\n    origin  : null,\n    restrict: null,\n  },\n\n  checker: function (pointer, event, interactable, element, interaction) {\n    if (interaction.pointerIds.length >= 2) {\n      return { name: 'gesture' };\n    }\n\n    return null;\n  },\n\n  getCursor: function () {\n    return '';\n  },\n};\n\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'gesturestart') { return; }\n  iEvent.ds = 0;\n\n  interaction.gesture.startDistance = interaction.gesture.prevDistance = iEvent.distance;\n  interaction.gesture.startAngle = interaction.gesture.prevAngle = iEvent.angle;\n  interaction.gesture.scale = 1;\n});\n\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'gesturemove') { return; }\n\n  iEvent.ds = iEvent.scale - interaction.gesture.scale;\n\n  interaction.target.fire(iEvent);\n\n  interaction.gesture.prevAngle = iEvent.angle;\n  interaction.gesture.prevDistance = iEvent.distance;\n\n  if (iEvent.scale !== Infinity\n      && iEvent.scale !== null\n      && iEvent.scale !== undefined\n      && !isNaN(iEvent.scale)) {\n\n    interaction.gesture.scale = iEvent.scale;\n  }\n});\n\n/**\n * ```js\n * interact(element).gesturable({\n *     onstart: function (event) {},\n *     onmove : function (event) {},\n *     onend  : function (event) {},\n *\n *     // limit multiple gestures.\n *     // See the explanation in {@link Interactable.draggable} example\n *     max: Infinity,\n *     maxPerElement: 1,\n * });\n *\n * var isGestureable = interact(element).gesturable();\n * ```\n *\n * Gets or sets whether multitouch gestures can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on gesture events (makes the Interactable gesturable)\n * @return {boolean | Interactable} A boolean indicating if this can be the\n * target of gesture events, or this Interactable\n */\nInteractable.prototype.gesturable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.gesture.enabled = options.enabled === false? false: true;\n    this.setPerAction('gesture', options);\n    this.setOnEvents('gesture', options);\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.gesture.enabled = options;\n\n    if (!options) {\n      this.ongesturestart = this.ongesturestart = this.ongestureend = null;\n    }\n\n    return this;\n  }\n\n  return this.options.gesture;\n};\n\nInteractEvent.signals.on('set-delta', function ({ interaction, iEvent, action, event, starting, ending, deltaSource }) {\n  if (action !== 'gesture') { return; }\n\n  const pointers = interaction.pointers;\n\n  iEvent.touches = [pointers[0], pointers[1]];\n\n  if (starting) {\n    iEvent.distance = utils.touchDistance(pointers, deltaSource);\n    iEvent.box      = utils.touchBBox(pointers);\n    iEvent.scale    = 1;\n    iEvent.ds       = 0;\n    iEvent.angle    = utils.touchAngle(pointers, undefined, deltaSource);\n    iEvent.da       = 0;\n  }\n  else if (ending || event instanceof InteractEvent) {\n    iEvent.distance = interaction.prevEvent.distance;\n    iEvent.box      = interaction.prevEvent.box;\n    iEvent.scale    = interaction.prevEvent.scale;\n    iEvent.ds       = iEvent.scale - 1;\n    iEvent.angle    = interaction.prevEvent.angle;\n    iEvent.da       = iEvent.angle - interaction.gesture.startAngle;\n  }\n  else {\n    iEvent.distance = utils.touchDistance(pointers, deltaSource);\n    iEvent.box      = utils.touchBBox(pointers);\n    iEvent.scale    = iEvent.distance / interaction.gesture.startDistance;\n    iEvent.angle    = utils.touchAngle(pointers, interaction.gesture.prevAngle, deltaSource);\n\n    iEvent.ds = iEvent.scale - interaction.gesture.prevScale;\n    iEvent.da = iEvent.angle - interaction.gesture.prevAngle;\n  }\n});\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.gesture = {\n    start: { x: 0, y: 0 },\n\n    startDistance: 0,   // distance between two touches of touchStart\n    prevDistance : 0,\n    distance     : 0,\n\n    scale: 1,           // gesture.distance / gesture.startDistance\n\n    startAngle: 0,      // angle of line joining two touches\n    prevAngle : 0,      // angle of the previous gesture event\n  };\n});\n\nactions.gesture = gesture;\nactions.names.push('gesture');\nutils.merge(Interactable.eventTypes, [\n  'gesturestart',\n  'gesturemove',\n  'gestureend',\n]);\nactions.methodDict.gesture = 'gesturable';\n\ndefaultOptions.gesture = gesture.defaults;\n\nmodule.exports = gesture;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst browser        = require('../utils/browser');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\n// Less Precision with touch input\nconst defaultMargin = browser.supportsTouch || browser.supportsPointerEvent? 20: 10;\n\nconst resize = {\n  defaults: {\n    enabled     : false,\n    mouseButtons: null,\n\n    origin    : null,\n    snap      : null,\n    restrict  : null,\n    inertia   : null,\n    autoScroll: null,\n\n    square: false,\n    preserveAspectRatio: false,\n    axis: 'xy',\n\n    // use default margin\n    margin: NaN,\n\n    // object with props left, right, top, bottom which are\n    // true/false values to resize when the pointer is over that edge,\n    // CSS selectors to match the handles for each direction\n    // or the Elements for each handle\n    edges: null,\n\n    // a value of 'none' will limit the resize rect to a minimum of 0x0\n    // 'negate' will alow the rect to have negative width/height\n    // 'reposition' will keep the width/height positive by swapping\n    // the top and bottom edges and/or swapping the left and right edges\n    invert: 'none',\n  },\n\n  checker: function (pointer, event, interactable, element, interaction, rect) {\n    if (!rect) { return null; }\n\n    const page = utils.extend({}, interaction.curCoords.page);\n    const options = interactable.options;\n\n    if (options.resize.enabled) {\n      const resizeOptions = options.resize;\n      const resizeEdges = { left: false, right: false, top: false, bottom: false };\n\n      // if using resize.edges\n      if (utils.is.object(resizeOptions.edges)) {\n        for (const edge in resizeEdges) {\n          resizeEdges[edge] = checkResizeEdge(edge,\n                                              resizeOptions.edges[edge],\n                                              page,\n                                              interaction._eventTarget,\n                                              element,\n                                              rect,\n                                              resizeOptions.margin || defaultMargin);\n        }\n\n        resizeEdges.left = resizeEdges.left && !resizeEdges.right;\n        resizeEdges.top  = resizeEdges.top  && !resizeEdges.bottom;\n\n        if (resizeEdges.left || resizeEdges.right || resizeEdges.top || resizeEdges.bottom) {\n          return {\n            name: 'resize',\n            edges: resizeEdges,\n          };\n        }\n      }\n      else {\n        const right  = options.resize.axis !== 'y' && page.x > (rect.right  - defaultMargin);\n        const bottom = options.resize.axis !== 'x' && page.y > (rect.bottom - defaultMargin);\n\n        if (right || bottom) {\n          return {\n            name: 'resize',\n            axes: (right? 'x' : '') + (bottom? 'y' : ''),\n          };\n        }\n      }\n    }\n\n    return null;\n  },\n\n  cursors: (browser.isIe9 ? {\n    x : 'e-resize',\n    y : 's-resize',\n    xy: 'se-resize',\n\n    top        : 'n-resize',\n    left       : 'w-resize',\n    bottom     : 's-resize',\n    right      : 'e-resize',\n    topleft    : 'se-resize',\n    bottomright: 'se-resize',\n    topright   : 'ne-resize',\n    bottomleft : 'ne-resize',\n  } : {\n    x : 'ew-resize',\n    y : 'ns-resize',\n    xy: 'nwse-resize',\n\n    top        : 'ns-resize',\n    left       : 'ew-resize',\n    bottom     : 'ns-resize',\n    right      : 'ew-resize',\n    topleft    : 'nwse-resize',\n    bottomright: 'nwse-resize',\n    topright   : 'nesw-resize',\n    bottomleft : 'nesw-resize',\n  }),\n\n  getCursor: function (action) {\n    if (action.axis) {\n      return resize.cursors[action.name + action.axis];\n    }\n    else if (action.edges) {\n      let cursorKey = '';\n      const edgeNames = ['top', 'bottom', 'left', 'right'];\n\n      for (let i = 0; i < 4; i++) {\n        if (action.edges[edgeNames[i]]) {\n          cursorKey += edgeNames[i];\n        }\n      }\n\n      return resize.cursors[cursorKey];\n    }\n  },\n};\n\n// resizestart\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'resizestart' || !interaction.prepared.edges) {\n    return;\n  }\n\n  const startRect = interaction.target.getRect(interaction.element);\n  const resizeOptions = interaction.target.options.resize;\n\n  /*\n   * When using the `resizable.square` or `resizable.preserveAspectRatio` options, resizing from one edge\n   * will affect another. E.g. with `resizable.square`, resizing to make the right edge larger will make\n   * the bottom edge larger by the same amount. We call these 'linked' edges. Any linked edges will depend\n   * on the active edges and the edge being interacted with.\n   */\n  if (resizeOptions.square || resizeOptions.preserveAspectRatio) {\n    const linkedEdges = utils.extend({}, interaction.prepared.edges);\n\n    linkedEdges.top    = linkedEdges.top    || (linkedEdges.left   && !linkedEdges.bottom);\n    linkedEdges.left   = linkedEdges.left   || (linkedEdges.top    && !linkedEdges.right );\n    linkedEdges.bottom = linkedEdges.bottom || (linkedEdges.right  && !linkedEdges.top   );\n    linkedEdges.right  = linkedEdges.right  || (linkedEdges.bottom && !linkedEdges.left  );\n\n    interaction.prepared._linkedEdges = linkedEdges;\n  }\n  else {\n    interaction.prepared._linkedEdges = null;\n  }\n\n  // if using `resizable.preserveAspectRatio` option, record aspect ratio at the start of the resize\n  if (resizeOptions.preserveAspectRatio) {\n    interaction.resizeStartAspectRatio = startRect.width / startRect.height;\n  }\n\n  interaction.resizeRects = {\n    start     : startRect,\n    current   : utils.extend({}, startRect),\n    inverted  : utils.extend({}, startRect),\n    previous  : utils.extend({}, startRect),\n    delta     : {\n      left: 0, right : 0, width : 0,\n      top : 0, bottom: 0, height: 0,\n    },\n  };\n\n  iEvent.rect = interaction.resizeRects.inverted;\n  iEvent.deltaRect = interaction.resizeRects.delta;\n});\n\n// resizemove\nInteractEvent.signals.on('new', function ({ iEvent, phase, interaction }) {\n  if (phase !== 'move' || !interaction.prepared.edges) { return; }\n\n  const resizeOptions = interaction.target.options.resize;\n  const invert = resizeOptions.invert;\n  const invertible = invert === 'reposition' || invert === 'negate';\n\n  let edges = interaction.prepared.edges;\n\n  const start      = interaction.resizeRects.start;\n  const current    = interaction.resizeRects.current;\n  const inverted   = interaction.resizeRects.inverted;\n  const delta      = interaction.resizeRects.delta;\n  const previous   = utils.extend(interaction.resizeRects.previous, inverted);\n  const originalEdges = edges;\n\n  let dx = iEvent.dx;\n  let dy = iEvent.dy;\n\n  if (resizeOptions.preserveAspectRatio || resizeOptions.square) {\n    // `resize.preserveAspectRatio` takes precedence over `resize.square`\n    const startAspectRatio = resizeOptions.preserveAspectRatio\n      ? interaction.resizeStartAspectRatio\n      : 1;\n\n    edges = interaction.prepared._linkedEdges;\n\n    if ((originalEdges.left && originalEdges.bottom)\n        || (originalEdges.right && originalEdges.top)) {\n      dy = -dx / startAspectRatio;\n    }\n    else if (originalEdges.left || originalEdges.right ) { dy = dx / startAspectRatio; }\n    else if (originalEdges.top  || originalEdges.bottom) { dx = dy * startAspectRatio; }\n  }\n\n  // update the 'current' rect without modifications\n  if (edges.top   ) { current.top    += dy; }\n  if (edges.bottom) { current.bottom += dy; }\n  if (edges.left  ) { current.left   += dx; }\n  if (edges.right ) { current.right  += dx; }\n\n  if (invertible) {\n    // if invertible, copy the current rect\n    utils.extend(inverted, current);\n\n    if (invert === 'reposition') {\n      // swap edge values if necessary to keep width/height positive\n      let swap;\n\n      if (inverted.top > inverted.bottom) {\n        swap = inverted.top;\n\n        inverted.top = inverted.bottom;\n        inverted.bottom = swap;\n      }\n      if (inverted.left > inverted.right) {\n        swap = inverted.left;\n\n        inverted.left = inverted.right;\n        inverted.right = swap;\n      }\n    }\n  }\n  else {\n    // if not invertible, restrict to minimum of 0x0 rect\n    inverted.top    = Math.min(current.top, start.bottom);\n    inverted.bottom = Math.max(current.bottom, start.top);\n    inverted.left   = Math.min(current.left, start.right);\n    inverted.right  = Math.max(current.right, start.left);\n  }\n\n  inverted.width  = inverted.right  - inverted.left;\n  inverted.height = inverted.bottom - inverted.top ;\n\n  for (const edge in inverted) {\n    delta[edge] = inverted[edge] - previous[edge];\n  }\n\n  iEvent.edges = interaction.prepared.edges;\n  iEvent.rect = inverted;\n  iEvent.deltaRect = delta;\n});\n\n/**\n * ```js\n * interact(element).resizable({\n *   onstart: function (event) {},\n *   onmove : function (event) {},\n *   onend  : function (event) {},\n *\n *   edges: {\n *     top   : true,       // Use pointer coords to check for resize.\n *     left  : false,      // Disable resizing from left edge.\n *     bottom: '.resize-s',// Resize if pointer target matches selector\n *     right : handleEl    // Resize if pointer target is the given Element\n *   },\n *\n *     // Width and height can be adjusted independently. When `true`, width and\n *     // height are adjusted at a 1:1 ratio.\n *     square: false,\n *\n *     // Width and height can be adjusted independently. When `true`, width and\n *     // height maintain the aspect ratio they had when resizing started.\n *     preserveAspectRatio: false,\n *\n *   // a value of 'none' will limit the resize rect to a minimum of 0x0\n *   // 'negate' will allow the rect to have negative width/height\n *   // 'reposition' will keep the width/height positive by swapping\n *   // the top and bottom edges and/or swapping the left and right edges\n *   invert: 'none' || 'negate' || 'reposition'\n *\n *   // limit multiple resizes.\n *   // See the explanation in the {@link Interactable.draggable} example\n *   max: Infinity,\n *   maxPerElement: 1,\n * });\n *\n * var isResizeable = interact(element).resizable();\n * ```\n *\n * Gets or sets whether resize actions can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on resize events (object makes the Interactable\n * resizable)\n * @return {boolean | Interactable} A boolean indicating if this can be the\n * target of resize elements, or this Interactable\n */\nInteractable.prototype.resizable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.resize.enabled = options.enabled === false? false: true;\n    this.setPerAction('resize', options);\n    this.setOnEvents('resize', options);\n\n    if (/^x$|^y$|^xy$/.test(options.axis)) {\n      this.options.resize.axis = options.axis;\n    }\n    else if (options.axis === null) {\n      this.options.resize.axis = defaultOptions.resize.axis;\n    }\n\n    if (utils.is.bool(options.preserveAspectRatio)) {\n      this.options.resize.preserveAspectRatio = options.preserveAspectRatio;\n    }\n    else if (utils.is.bool(options.square)) {\n      this.options.resize.square = options.square;\n    }\n\n    return this;\n  }\n  if (utils.is.bool(options)) {\n    this.options.resize.enabled = options;\n\n    if (!options) {\n      this.onresizestart = this.onresizestart = this.onresizeend = null;\n    }\n\n    return this;\n  }\n  return this.options.resize;\n};\n\nfunction checkResizeEdge (name, value, page, element, interactableElement, rect, margin) {\n  // false, '', undefined, null\n  if (!value) { return false; }\n\n  // true value, use pointer coords and element rect\n  if (value === true) {\n    // if dimensions are negative, \"switch\" edges\n    const width  = utils.is.number(rect.width )? rect.width  : rect.right  - rect.left;\n    const height = utils.is.number(rect.height)? rect.height : rect.bottom - rect.top ;\n\n    if (width < 0) {\n      if      (name === 'left' ) { name = 'right'; }\n      else if (name === 'right') { name = 'left' ; }\n    }\n    if (height < 0) {\n      if      (name === 'top'   ) { name = 'bottom'; }\n      else if (name === 'bottom') { name = 'top'   ; }\n    }\n\n    if (name === 'left'  ) { return page.x < ((width  >= 0? rect.left: rect.right ) + margin); }\n    if (name === 'top'   ) { return page.y < ((height >= 0? rect.top : rect.bottom) + margin); }\n\n    if (name === 'right' ) { return page.x > ((width  >= 0? rect.right : rect.left) - margin); }\n    if (name === 'bottom') { return page.y > ((height >= 0? rect.bottom: rect.top ) - margin); }\n  }\n\n  // the remaining checks require an element\n  if (!utils.is.element(element)) { return false; }\n\n  return utils.is.element(value)\n  // the value is an element to use as a resize handle\n    ? value === element\n    // otherwise check if element matches value as selector\n    : utils.matchesUpTo(element, value, interactableElement);\n}\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.resizeAxes = 'xy';\n});\n\nInteractEvent.signals.on('set-delta', function ({ interaction, iEvent, action }) {\n  if (action !== 'resize' || !interaction.resizeAxes) { return; }\n\n  const options = interaction.target.options;\n\n  if (options.resize.square) {\n    if (interaction.resizeAxes === 'y') {\n      iEvent.dx = iEvent.dy;\n    }\n    else {\n      iEvent.dy = iEvent.dx;\n    }\n    iEvent.axes = 'xy';\n  }\n  else {\n    iEvent.axes = interaction.resizeAxes;\n\n    if (interaction.resizeAxes === 'x') {\n      iEvent.dy = 0;\n    }\n    else if (interaction.resizeAxes === 'y') {\n      iEvent.dx = 0;\n    }\n  }\n});\n\nactions.resize = resize;\nactions.names.push('resize');\nutils.merge(Interactable.eventTypes, [\n  'resizestart',\n  'resizemove',\n  'resizeinertiastart',\n  'resizeinertiaresume',\n  'resizeend',\n]);\nactions.methodDict.resize = 'resizable';\n\ndefaultOptions.resize = resize.defaults;\n\nmodule.exports = resize;\n", "const raf            = require('./utils/raf');\nconst getWindow      = require('./utils/window').getWindow;\nconst is             = require('./utils/is');\nconst domUtils       = require('./utils/domUtils');\nconst Interaction    = require('./Interaction');\nconst defaultOptions = require('./defaultOptions');\n\nconst autoScroll = {\n  defaults: {\n    enabled  : false,\n    container: null,     // the item that is scrolled (Window or HTMLElement)\n    margin   : 60,\n    speed    : 300,      // the scroll speed in pixels per second\n  },\n\n  interaction: null,\n  i: null,    // the handle returned by window.setInterval\n  x: 0, y: 0, // Direction each pulse is to scroll in\n\n  isScrolling: false,\n  prevTime: 0,\n\n  start: function (interaction) {\n    autoScroll.isScrolling = true;\n    raf.cancel(autoScroll.i);\n\n    autoScroll.interaction = interaction;\n    autoScroll.prevTime = new Date().getTime();\n    autoScroll.i = raf.request(autoScroll.scroll);\n  },\n\n  stop: function () {\n    autoScroll.isScrolling = false;\n    raf.cancel(autoScroll.i);\n  },\n\n  // scroll the window by the values in scroll.x/y\n  scroll: function () {\n    const options = autoScroll.interaction.target.options[autoScroll.interaction.prepared.name].autoScroll;\n    const container = options.container || getWindow(autoScroll.interaction.element);\n    const now = new Date().getTime();\n    // change in time in seconds\n    const dt = (now - autoScroll.prevTime) / 1000;\n    // displacement\n    const s = options.speed * dt;\n\n    if (s >= 1) {\n      if (is.window(container)) {\n        container.scrollBy(autoScroll.x * s, autoScroll.y * s);\n      }\n      else if (container) {\n        container.scrollLeft += autoScroll.x * s;\n        container.scrollTop  += autoScroll.y * s;\n      }\n\n      autoScroll.prevTime = now;\n    }\n\n    if (autoScroll.isScrolling) {\n      raf.cancel(autoScroll.i);\n      autoScroll.i = raf.request(autoScroll.scroll);\n    }\n  },\n  check: function (interactable, actionName) {\n    const options = interactable.options;\n\n    return options[actionName].autoScroll && options[actionName].autoScroll.enabled;\n  },\n  onInteractionMove: function ({ interaction, pointer }) {\n    if (!(interaction.interacting()\n          && autoScroll.check(interaction.target, interaction.prepared.name))) {\n      return;\n    }\n\n    if (interaction.simulation) {\n      autoScroll.x = autoScroll.y = 0;\n      return;\n    }\n\n    let top;\n    let right;\n    let bottom;\n    let left;\n\n    const options = interaction.target.options[interaction.prepared.name].autoScroll;\n    const container = options.container || getWindow(interaction.element);\n\n    if (is.window(container)) {\n      left   = pointer.clientX < autoScroll.margin;\n      top    = pointer.clientY < autoScroll.margin;\n      right  = pointer.clientX > container.innerWidth  - autoScroll.margin;\n      bottom = pointer.clientY > container.innerHeight - autoScroll.margin;\n    }\n    else {\n      const rect = domUtils.getElementClientRect(container);\n\n      left   = pointer.clientX < rect.left   + autoScroll.margin;\n      top    = pointer.clientY < rect.top    + autoScroll.margin;\n      right  = pointer.clientX > rect.right  - autoScroll.margin;\n      bottom = pointer.clientY > rect.bottom - autoScroll.margin;\n    }\n\n    autoScroll.x = (right ? 1: left? -1: 0);\n    autoScroll.y = (bottom? 1:  top? -1: 0);\n\n    if (!autoScroll.isScrolling) {\n      // set the autoScroll properties to those of the target\n      autoScroll.margin = options.margin;\n      autoScroll.speed  = options.speed;\n\n      autoScroll.start(interaction);\n    }\n  },\n};\n\nInteraction.signals.on('stop-active', function () {\n  autoScroll.stop();\n});\n\nInteraction.signals.on('action-move', autoScroll.onInteractionMove);\n\ndefaultOptions.perAction.autoScroll = autoScroll.defaults;\n\nmodule.exports = autoScroll;\n", "/** @lends Interactable */\nconst Interactable = require('../Interactable');\nconst actions      = require('../actions/base');\nconst is           = require('../utils/is');\nconst domUtils     = require('../utils/domUtils');\n\nconst { warnOnce } = require('../utils');\n\nInteractable.prototype.getAction = function (pointer, event, interaction, element) {\n  const action = this.defaultActionChecker(pointer, event, interaction, element);\n\n  if (this.options.actionChecker) {\n    return this.options.actionChecker(pointer, event, action, this, element, interaction);\n  }\n\n  return action;\n};\n\n/**\n * ```js\n * interact(element, { ignoreFrom: document.getElementById('no-action') });\n * // or\n * interact(element).ignoreFrom('input, textarea, a');\n * ```\n * @deprecated\n * If the target of the `mousedown`, `pointerdown` or `touchstart` event or any\n * of it's parents match the given CSS selector or Element, no\n * drag/resize/gesture is started.\n *\n * Don't use this method. Instead set the `ignoreFrom` option for each action\n * or for `pointerEvents`\n *\n * @example\n * interact(targett)\n *   .draggable({\n *     ignoreFrom: 'input, textarea, a[href]'',\n *   })\n *   .pointerEvents({\n *     ignoreFrom: '[no-pointer]',\n *   });\n *\n * @param {string | Element | null} [newValue] a CSS selector string, an\n * Element or `null` to not ignore any elements\n * @return {string | Element | object} The current ignoreFrom value or this\n * Interactable\n */\nInteractable.prototype.ignoreFrom = warnOnce(function (newValue) {\n  return this._backCompatOption('ignoreFrom', newValue);\n}, 'Interactable.ignoreForm() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue}).');\n\n/**\n * ```js\n *\n * @deprecated\n * A drag/resize/gesture is started only If the target of the `mousedown`,\n * `pointerdown` or `touchstart` event or any of it's parents match the given\n * CSS selector or Element.\n *\n * Don't use this method. Instead set the `allowFrom` option for each action\n * or for `pointerEvents`\n *\n * @example\n * interact(targett)\n *   .resizable({\n *     allowFrom: '.resize-handle',\n *   .pointerEvents({\n *     allowFrom: '.handle',,\n *   });\n *\n * @param {string | Element | null} [newValue] a CSS selector string, an\n * Element or `null` to allow from any element\n * @return {string | Element | object} The current allowFrom value or this\n * Interactable\n */\nInteractable.prototype.allowFrom = warnOnce(function (newValue) {\n  return this._backCompatOption('allowFrom', newValue);\n}, 'Interactable.allowForm() has been deprecated. Use Interactble.draggable({allowFrom: newValue}).');\n\nInteractable.prototype.testIgnore = function (ignoreFrom, interactableElement, element) {\n  if (!ignoreFrom || !is.element(element)) { return false; }\n\n  if (is.string(ignoreFrom)) {\n    return domUtils.matchesUpTo(element, ignoreFrom, interactableElement);\n  }\n  else if (is.element(ignoreFrom)) {\n    return domUtils.nodeContains(ignoreFrom, element);\n  }\n\n  return false;\n};\n\nInteractable.prototype.testAllow = function (allowFrom, interactableElement, element) {\n  if (!allowFrom) { return true; }\n\n  if (!is.element(element)) { return false; }\n\n  if (is.string(allowFrom)) {\n    return domUtils.matchesUpTo(element, allowFrom, interactableElement);\n  }\n  else if (is.element(allowFrom)) {\n    return domUtils.nodeContains(allowFrom, element);\n  }\n\n  return false;\n};\n\nInteractable.prototype.testIgnoreAllow = function (options, interactableElement, eventTarget) {\n  return (!this.testIgnore(options.ignoreFrom, interactableElement, eventTarget)\n    && this.testAllow(options.allowFrom, interactableElement, eventTarget));\n};\n\n/**\n * ```js\n * interact('.resize-drag')\n *   .resizable(true)\n *   .draggable(true)\n *   .actionChecker(function (pointer, event, action, interactable, element, interaction) {\n *\n *   if (interact.matchesSelector(event.target, '.drag-handle') {\n *     // force drag with handle target\n *     action.name = drag;\n *   }\n *   else {\n *     // resize from the top and right edges\n *     action.name  = 'resize';\n *     action.edges = { top: true, right: true };\n *   }\n *\n *   return action;\n * });\n * ```\n *\n * Gets or sets the function used to check action to be performed on\n * pointerDown\n *\n * @param {function | null} [checker] A function which takes a pointer event,\n * defaultAction string, interactable, element and interaction as parameters\n * and returns an object with name property 'drag' 'resize' or 'gesture' and\n * optionally an `edges` object with boolean 'top', 'left', 'bottom' and right\n * props.\n * @return {Function | Interactable} The checker function or this Interactable\n */\nInteractable.prototype.actionChecker = function (checker) {\n  if (is.function(checker)) {\n    this.options.actionChecker = checker;\n\n    return this;\n  }\n\n  if (checker === null) {\n    delete this.options.actionChecker;\n\n    return this;\n  }\n\n  return this.options.actionChecker;\n};\n\n/**\n * Returns or sets whether the the cursor should be changed depending on the\n * action that would be performed if the mouse were pressed and dragged.\n *\n * @param {boolean} [newValue]\n * @return {boolean | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.styleCursor = function (newValue) {\n  if (is.bool(newValue)) {\n    this.options.styleCursor = newValue;\n\n    return this;\n  }\n\n  if (newValue === null) {\n    delete this.options.styleCursor;\n\n    return this;\n  }\n\n  return this.options.styleCursor;\n};\n\nInteractable.prototype.defaultActionChecker = function (pointer, event, interaction, element) {\n  const rect = this.getRect(element);\n  const buttons = event.buttons || ({\n    0: 1,\n    1: 4,\n    3: 8,\n    4: 16,\n  })[event.button];\n  let action = null;\n\n  for (const actionName of actions.names) {\n    // check mouseButton setting if the pointer is down\n    if (interaction.pointerIsDown\n        && /mouse|pointer/.test(interaction.pointerType)\n        && (buttons & this.options[actionName].mouseButtons) === 0) {\n      continue;\n    }\n\n    action = actions[actionName].checker(pointer, event, this, element, interaction, rect);\n\n    if (action) {\n      return action;\n    }\n  }\n};\n\n", "const interact       = require('../interact');\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst actions        = require('../actions/base');\nconst defaultOptions = require('../defaultOptions');\nconst scope          = require('../scope');\nconst utils          = require('../utils');\nconst signals        = require('../utils/Signals').new();\n\nrequire('./InteractableMethods');\n\nconst autoStart = {\n  signals,\n  withinInteractionLimit,\n  // Allow this many interactions to happen simultaneously\n  maxInteractions: Infinity,\n  defaults: {\n    perAction: {\n      manualStart: false,\n      max: Infinity,\n      maxPerElement: 1,\n      allowFrom:  null,\n      ignoreFrom: null,\n\n      // only allow left button by default\n      // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value\n      mouseButtons: 1,\n    },\n  },\n  setActionDefaults: function (action) {\n    utils.extend(action.defaults, autoStart.defaults.perAction);\n  },\n  validateAction,\n};\n\n// set cursor style on mousedown\nInteraction.signals.on('down', function ({ interaction, pointer, event, eventTarget }) {\n  if (interaction.interacting()) { return; }\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget);\n  prepare(interaction, actionInfo);\n});\n\n// set cursor style on mousemove\nInteraction.signals.on('move', function ({ interaction, pointer, event, eventTarget }) {\n  if (interaction.pointerType !== 'mouse'\n      || interaction.pointerIsDown\n      || interaction.interacting()) { return; }\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget);\n  prepare(interaction, actionInfo);\n});\n\nInteraction.signals.on('move', function (arg) {\n  const { interaction, event } = arg;\n\n  if (!interaction.pointerIsDown\n      || interaction.interacting()\n      || !interaction.pointerWasMoved\n      || !interaction.prepared.name) {\n    return;\n  }\n\n  signals.fire('before-start', arg);\n\n  const target = interaction.target;\n\n  if (interaction.prepared.name && target) {\n    // check manualStart and interaction limit\n    if (target.options[interaction.prepared.name].manualStart\n        || !withinInteractionLimit(target, interaction.element, interaction.prepared)) {\n      interaction.stop(event);\n    }\n    else {\n      interaction.start(interaction.prepared, target, interaction.element);\n    }\n  }\n});\n\n// Check if the current target supports the action.\n// If so, return the validated action. Otherwise, return null\nfunction validateAction (action, interactable, element, eventTarget) {\n  if (utils.is.object(action)\n      && interactable.testIgnoreAllow(interactable.options[action.name], element, eventTarget)\n      && interactable.options[action.name].enabled\n      && withinInteractionLimit(interactable, element, action)) {\n    return action;\n  }\n\n  return null;\n}\n\nfunction validateSelector (interaction, pointer, event, matches, matchElements, eventTarget) {\n  for (let i = 0, len = matches.length; i < len; i++) {\n    const match = matches[i];\n    const matchElement = matchElements[i];\n    const action = validateAction(match.getAction(pointer, event, interaction, matchElement),\n                                  match,\n                                  matchElement,\n                                  eventTarget);\n\n    if (action) {\n      return {\n        action,\n        target: match,\n        element: matchElement,\n      };\n    }\n  }\n\n  return {};\n}\n\nfunction getActionInfo (interaction, pointer, event, eventTarget) {\n  let matches = [];\n  let matchElements = [];\n\n  let element = eventTarget;\n\n  function pushMatches (interactable) {\n    matches.push(interactable);\n    matchElements.push(element);\n  }\n\n  while (utils.is.element(element)) {\n    matches = [];\n    matchElements = [];\n\n    scope.interactables.forEachMatch(element, pushMatches);\n\n    const actionInfo = validateSelector(interaction, pointer, event, matches, matchElements, eventTarget);\n\n    if (actionInfo.action\n      && !actionInfo.target.options[actionInfo.action.name].manualStart) {\n      return actionInfo;\n    }\n\n    element = utils.parentNode(element);\n  }\n\n  return {};\n}\n\nfunction prepare (interaction, { action, target, element }) {\n  action = action || {};\n\n  if (interaction.target && interaction.target.options.styleCursor) {\n    interaction.target._doc.documentElement.style.cursor = '';\n  }\n\n  interaction.target = target;\n  interaction.element = element;\n  utils.copyAction(interaction.prepared, action);\n\n  if (target && target.options.styleCursor) {\n    const cursor = action? actions[action.name].getCursor(action) : '';\n    interaction.target._doc.documentElement.style.cursor = cursor;\n  }\n\n  signals.fire('prepared', { interaction: interaction });\n}\n\nInteraction.signals.on('stop', function ({ interaction }) {\n  const target = interaction.target;\n\n  if (target && target.options.styleCursor) {\n    target._doc.documentElement.style.cursor = '';\n  }\n});\n\nfunction withinInteractionLimit (interactable, element, action) {\n  const options = interactable.options;\n  const maxActions = options[action.name].max;\n  const maxPerElement = options[action.name].maxPerElement;\n  let activeInteractions = 0;\n  let targetCount = 0;\n  let targetElementCount = 0;\n\n  // no actions if any of these values == 0\n  if (!(maxActions && maxPerElement && autoStart.maxInteractions)) { return; }\n\n  for (const interaction of scope.interactions) {\n    const otherAction = interaction.prepared.name;\n\n    if (!interaction.interacting()) { continue; }\n\n    activeInteractions++;\n\n    if (activeInteractions >= autoStart.maxInteractions) {\n      return false;\n    }\n\n    if (interaction.target !== interactable) { continue; }\n\n    targetCount += (otherAction === action.name)|0;\n\n    if (targetCount >= maxActions) {\n      return false;\n    }\n\n    if (interaction.element === element) {\n      targetElementCount++;\n\n      if (otherAction !== action.name || targetElementCount >= maxPerElement) {\n        return false;\n      }\n    }\n  }\n\n  return autoStart.maxInteractions > 0;\n}\n\n/**\n * Returns or sets the maximum number of concurrent interactions allowed.  By\n * default only 1 interaction is allowed at a time (for backwards\n * compatibility). To allow multiple interactions on the same Interactables and\n * elements, you need to enable it in the draggable, resizable and gesturable\n * `'max'` and `'maxPerElement'` options.\n *\n * @alias module:interact.maxInteractions\n *\n * @param {number} [newValue] Any number. newValue <= 0 means no interactions.\n */\ninteract.maxInteractions = function (newValue) {\n  if (utils.is.number(newValue)) {\n    autoStart.maxInteractions = newValue;\n\n    return interact;\n  }\n\n  return autoStart.maxInteractions;\n};\n\nInteractable.settingsMethods.push('styleCursor');\nInteractable.settingsMethods.push('actionChecker');\nInteractable.settingsMethods.push('ignoreFrom');\nInteractable.settingsMethods.push('allowFrom');\n\ndefaultOptions.base.actionChecker = null;\ndefaultOptions.base.styleCursor = true;\n\nutils.extend(defaultOptions.perAction, autoStart.defaults.perAction);\n\nmodule.exports = autoStart;\n", "const autoStart = require('./base');\nconst scope     = require('../scope');\nconst is        = require('../utils/is');\n\nconst { parentNode } = require('../utils/domUtils');\n\nautoStart.setActionDefaults(require('../actions/drag'));\n\nautoStart.signals.on('before-start',  function ({ interaction, eventTarget, dx, dy }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  // check if a drag is in the correct axis\n  const absX = Math.abs(dx);\n  const absY = Math.abs(dy);\n  const targetOptions = interaction.target.options.drag;\n  const startAxis = targetOptions.startAxis;\n  const currentAxis = (absX > absY ? 'x' : absX < absY ? 'y' : 'xy');\n\n  interaction.prepared.axis = targetOptions.lockAxis === 'start'\n    ? currentAxis[0] // always lock to one axis even if currentAxis === 'xy'\n    : targetOptions.lockAxis;\n\n  // if the movement isn't in the startAxis of the interactable\n  if (currentAxis !== 'xy' && startAxis !== 'xy' && startAxis !== currentAxis) {\n    // cancel the prepared action\n    interaction.prepared.name = null;\n\n    // then try to get a drag from another ineractable\n    let element = eventTarget;\n\n    const getDraggable = function (interactable) {\n      if (interactable === interaction.target) { return; }\n\n      const options = interaction.target.options.drag;\n\n      if (!options.manualStart\n          && interactable.testIgnoreAllow(options, element, eventTarget)) {\n\n        const action = interactable.getAction(\n          interaction.downPointer, interaction.downEvent, interaction, element);\n\n        if (action\n            && action.name === 'drag'\n            && checkStartAxis(currentAxis, interactable)\n            && autoStart.validateAction(action, interactable, element, eventTarget)) {\n\n          return interactable;\n        }\n      }\n    };\n\n    // check all interactables\n    while (is.element(element)) {\n      const interactable = scope.interactables.forEachMatch(element, getDraggable);\n\n      if (interactable) {\n        interaction.prepared.name = 'drag';\n        interaction.target = interactable;\n        interaction.element = element;\n        break;\n      }\n\n      element = parentNode(element);\n    }\n  }\n});\n\nfunction checkStartAxis (startAxis, interactable) {\n  if (!interactable) { return false; }\n\n  const thisAxis = interactable.options.drag.startAxis;\n\n  return (startAxis === 'xy' || thisAxis === 'xy' || thisAxis === startAxis);\n}\n", "require('./base').setActionDefaults(require('../actions/gesture'));\n", "const autoStart   = require('./base');\nconst Interaction = require('../Interaction');\n\nautoStart.defaults.perAction.hold = 0;\nautoStart.defaults.perAction.delay = 0;\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.autoStartHoldTimer = null;\n});\n\nautoStart.signals.on('prepared', function ({ interaction }) {\n  const hold = getHoldDuration(interaction);\n\n  if (hold > 0) {\n    interaction.autoStartHoldTimer = setTimeout(() => {\n      interaction.start(interaction.prepared, interaction.target, interaction.element);\n    }, hold);\n  }\n});\n\nInteraction.signals.on('move', function ({ interaction, duplicate }) {\n  if (interaction.pointerWasMoved && !duplicate) {\n    clearTimeout(interaction.autoStartHoldTimer);\n  }\n});\n\n// prevent regular down->move autoStart\nautoStart.signals.on('before-start', function ({ interaction }) {\n  const hold = getHoldDuration(interaction);\n\n  if (hold > 0) {\n    interaction.prepared.name = null;\n  }\n});\n\nfunction getHoldDuration (interaction) {\n  const actionName = interaction.prepared && interaction.prepared.name;\n\n  if (!actionName) { return null; }\n\n  const options = interaction.target.options;\n\n  return options[actionName].hold || options[actionName].delay;\n}\n\nmodule.exports = {\n  getHoldDuration,\n};\n", "require('./base').setActionDefaults(require('../actions/resize'));\n", "module.exports = {\n  base: {\n    accept        : null,\n    preventDefault: 'auto',\n    deltaSource   : 'page',\n  },\n\n  perAction: {\n    origin: { x: 0, y: 0 },\n\n    inertia: {\n      enabled          : false,\n      resistance       : 10,    // the lambda in exponential decay\n      minSpeed         : 100,   // target speed must be above this for inertia to start\n      endSpeed         : 10,    // the speed at which inertia is slow enough to stop\n      allowResume      : true,  // allow resuming an action in inertia phase\n      smoothEndDuration: 300,   // animate to snap/restrict endOnly if there's no inertia\n    },\n  },\n};\n", "/* browser entry point */\n\n// inertia\nrequire('./inertia');\n\n// modifiers\nrequire('./modifiers/snap');\nrequire('./modifiers/restrict');\n\n// pointerEvents\nrequire('./pointerEvents/base');\nrequire('./pointerEvents/holdRepeat');\nrequire('./pointerEvents/interactableTargets');\n\n// autoStart hold\nrequire('./autoStart/hold');\n\n// actions\nrequire('./actions/gesture');\nrequire('./actions/resize');\nrequire('./actions/drag');\nrequire('./actions/drop');\n\n// load these modifiers after resize is loaded\nrequire('./modifiers/snapSize');\nrequire('./modifiers/restrictEdges');\nrequire('./modifiers/restrictSize');\n\n// autoStart actions\nrequire('./autoStart/gesture');\nrequire('./autoStart/resize');\nrequire('./autoStart/drag');\n\n// Interactable preventDefault setting\nrequire('./interactablePreventDefault.js');\n\n// autoScroll\nrequire('./autoScroll');\n\n// export interact\nmodule.exports = require('./interact');\n", "const InteractEvent  = require('./InteractEvent');\nconst Interaction    = require('./Interaction');\nconst modifiers      = require('./modifiers/base');\nconst utils          = require('./utils');\nconst animationFrame = require('./utils/raf');\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.inertiaStatus = {\n    active     : false,\n    smoothEnd  : false,\n    allowResume: false,\n\n    startEvent: null,\n    upCoords  : {},\n\n    xe: 0, ye: 0,\n    sx: 0, sy: 0,\n\n    t0: 0,\n    vx0: 0, vys: 0,\n    duration: 0,\n\n    lambda_v0: 0,\n    one_ve_v0: 0,\n    i  : null,\n  };\n\n  interaction.boundInertiaFrame   = () => inertiaFrame  .apply(interaction);\n  interaction.boundSmoothEndFrame = () => smoothEndFrame.apply(interaction);\n});\n\nInteraction.signals.on('down', function ({ interaction, event, pointer, eventTarget }) {\n  const status = interaction.inertiaStatus;\n\n  // Check if the down event hits the current inertia target\n  if (status.active) {\n    let element = eventTarget;\n\n    // climb up the DOM tree from the event target\n    while (utils.is.element(element)) {\n\n      // if interaction element is the current inertia target element\n      if (element === interaction.element) {\n        // stop inertia\n        animationFrame.cancel(status.i);\n        status.active = false;\n        interaction.simulation = null;\n\n        // update pointers to the down event's coordinates\n        interaction.updatePointer(pointer);\n        utils.setCoords(interaction.curCoords, interaction.pointers);\n\n        // fire appropriate signals\n        const signalArg = { interaction };\n        Interaction.signals.fire('before-action-move', signalArg);\n        Interaction.signals.fire('action-resume'     , signalArg);\n\n        // fire a reume event\n        const resumeEvent = new InteractEvent(interaction,\n                                              event,\n                                              interaction.prepared.name,\n                                              'inertiaresume',\n                                              interaction.element);\n\n        interaction.target.fire(resumeEvent);\n        interaction.prevEvent = resumeEvent;\n        modifiers.resetStatuses(interaction.modifierStatuses);\n\n        utils.copyCoords(interaction.prevCoords, interaction.curCoords);\n        break;\n      }\n\n      element = utils.parentNode(element);\n    }\n  }\n});\n\nInteraction.signals.on('up', function ({ interaction, event }) {\n  const status = interaction.inertiaStatus;\n\n  if (!interaction.interacting() || status.active) { return; }\n\n  const target = interaction.target;\n  const options = target && target.options;\n  const inertiaOptions = options && interaction.prepared.name && options[interaction.prepared.name].inertia;\n\n  const now = new Date().getTime();\n  const statuses = {};\n  const page = utils.extend({}, interaction.curCoords.page);\n  const pointerSpeed = interaction.pointerDelta.client.speed;\n\n  let smoothEnd = false;\n  let modifierResult;\n\n  // check if inertia should be started\n  const inertiaPossible = (inertiaOptions && inertiaOptions.enabled\n                     && interaction.prepared.name !== 'gesture'\n                     && event !== status.startEvent);\n\n  const inertia = (inertiaPossible\n    && (now - interaction.curCoords.timeStamp) < 50\n    && pointerSpeed > inertiaOptions.minSpeed\n    && pointerSpeed > inertiaOptions.endSpeed);\n\n  const modifierArg = {\n    interaction,\n    pageCoords: page,\n    statuses,\n    preEnd: true,\n    requireEndOnly: true,\n  };\n\n  // smoothEnd\n  if (inertiaPossible && !inertia) {\n    modifiers.resetStatuses(statuses);\n\n    modifierResult = modifiers.setAll(modifierArg);\n\n    if (modifierResult.shouldMove && modifierResult.locked) {\n      smoothEnd = true;\n    }\n  }\n\n  if (!(inertia || smoothEnd)) { return; }\n\n  utils.copyCoords(status.upCoords, interaction.curCoords);\n\n  interaction.pointers[0] = status.startEvent =\n    new InteractEvent(interaction, event, interaction.prepared.name, 'inertiastart', interaction.element);\n\n  status.t0 = now;\n\n  status.active = true;\n  status.allowResume = inertiaOptions.allowResume;\n  interaction.simulation = status;\n\n  target.fire(status.startEvent);\n\n  if (inertia) {\n    status.vx0 = interaction.pointerDelta.client.vx;\n    status.vy0 = interaction.pointerDelta.client.vy;\n    status.v0 = pointerSpeed;\n\n    calcInertia(interaction, status);\n\n    utils.extend(page, interaction.curCoords.page);\n\n    page.x += status.xe;\n    page.y += status.ye;\n\n    modifiers.resetStatuses(statuses);\n\n    modifierResult = modifiers.setAll(modifierArg);\n\n    status.modifiedXe += modifierResult.dx;\n    status.modifiedYe += modifierResult.dy;\n\n    status.i = animationFrame.request(interaction.boundInertiaFrame);\n  }\n  else {\n    status.smoothEnd = true;\n    status.xe = modifierResult.dx;\n    status.ye = modifierResult.dy;\n\n    status.sx = status.sy = 0;\n\n    status.i = animationFrame.request(interaction.boundSmoothEndFrame);\n  }\n});\n\nInteraction.signals.on('stop-active', function ({ interaction }) {\n  const status = interaction.inertiaStatus;\n\n  if (status.active) {\n    animationFrame.cancel(status.i);\n    status.active = false;\n    interaction.simulation = null;\n  }\n});\n\nfunction calcInertia (interaction, status) {\n  const inertiaOptions = interaction.target.options[interaction.prepared.name].inertia;\n  const lambda = inertiaOptions.resistance;\n  const inertiaDur = -Math.log(inertiaOptions.endSpeed / status.v0) / lambda;\n\n  status.x0 = interaction.prevEvent.pageX;\n  status.y0 = interaction.prevEvent.pageY;\n  status.t0 = status.startEvent.timeStamp / 1000;\n  status.sx = status.sy = 0;\n\n  status.modifiedXe = status.xe = (status.vx0 - inertiaDur) / lambda;\n  status.modifiedYe = status.ye = (status.vy0 - inertiaDur) / lambda;\n  status.te = inertiaDur;\n\n  status.lambda_v0 = lambda / status.v0;\n  status.one_ve_v0 = 1 - inertiaOptions.endSpeed / status.v0;\n}\n\nfunction inertiaFrame () {\n  updateInertiaCoords(this);\n  utils.setCoordDeltas(this.pointerDelta, this.prevCoords, this.curCoords);\n\n  const status = this.inertiaStatus;\n  const options = this.target.options[this.prepared.name].inertia;\n  const lambda = options.resistance;\n  const t = new Date().getTime() / 1000 - status.t0;\n\n  if (t < status.te) {\n\n    const progress =  1 - (Math.exp(-lambda * t) - status.lambda_v0) / status.one_ve_v0;\n\n    if (status.modifiedXe === status.xe && status.modifiedYe === status.ye) {\n      status.sx = status.xe * progress;\n      status.sy = status.ye * progress;\n    }\n    else {\n      const quadPoint = utils.getQuadraticCurvePoint(0, 0,\n                                                     status.xe,\n                                                     status.ye,\n                                                     status.modifiedXe,\n                                                     status.modifiedYe,\n                                                     progress);\n\n      status.sx = quadPoint.x;\n      status.sy = quadPoint.y;\n    }\n\n    this.doMove();\n\n    status.i = animationFrame.request(this.boundInertiaFrame);\n  }\n  else {\n    status.sx = status.modifiedXe;\n    status.sy = status.modifiedYe;\n\n    this.doMove();\n    this.end(status.startEvent);\n    status.active = false;\n    this.simulation = null;\n  }\n\n  utils.copyCoords(this.prevCoords, this.curCoords);\n}\n\nfunction smoothEndFrame () {\n  updateInertiaCoords(this);\n\n  const status = this.inertiaStatus;\n  const t = new Date().getTime() - status.t0;\n  const duration = this.target.options[this.prepared.name].inertia.smoothEndDuration;\n\n  if (t < duration) {\n    status.sx = utils.easeOutQuad(t, 0, status.xe, duration);\n    status.sy = utils.easeOutQuad(t, 0, status.ye, duration);\n\n    this.pointerMove(status.startEvent, status.startEvent);\n\n    status.i = animationFrame.request(this.boundSmoothEndFrame);\n  }\n  else {\n    status.sx = status.xe;\n    status.sy = status.ye;\n\n    this.pointerMove(status.startEvent, status.startEvent);\n    this.end(status.startEvent);\n\n    status.smoothEnd =\n      status.active = false;\n    this.simulation = null;\n  }\n}\n\nfunction updateInertiaCoords (interaction) {\n  const status = interaction.inertiaStatus;\n\n  // return if inertia isn't running\n  if (!status.active) { return; }\n\n  const pageUp   = status.upCoords.page;\n  const clientUp = status.upCoords.client;\n\n  utils.setCoords(interaction.curCoords, [ {\n    pageX  : pageUp.x   + status.sx,\n    pageY  : pageUp.y   + status.sy,\n    clientX: clientUp.x + status.sx,\n    clientY: clientUp.y + status.sy,\n  } ]);\n}\n", "/** @module interact */\n\nconst browser      = require('./utils/browser');\nconst events       = require('./utils/events');\nconst utils        = require('./utils');\nconst scope        = require('./scope');\nconst Interactable = require('./Interactable');\nconst Interaction  = require('./Interaction');\n\nconst globalEvents = {};\n\n/**\n * ```js\n * interact('#draggable').draggable(true);\n *\n * var rectables = interact('rect');\n * rectables\n *   .gesturable(true)\n *   .on('gesturemove', function (event) {\n *       // ...\n *   });\n * ```\n *\n * The methods of this variable can be used to set elements as interactables\n * and also to change various default settings.\n *\n * Calling it as a function and passing an element or a valid CSS selector\n * string returns an Interactable object which has various methods to configure\n * it.\n *\n * @global\n *\n * @param {Element | string} element The HTML or SVG Element to interact with\n * or CSS selector\n * @return {Interactable}\n */\nfunction interact (element, options) {\n  let interactable = scope.interactables.get(element, options);\n\n  if (!interactable) {\n    interactable = new Interactable(element, options);\n    interactable.events.global = globalEvents;\n  }\n\n  return interactable;\n}\n\n/**\n * Check if an element or selector has been set with the {@link interact}\n * function\n *\n * @alias module:interact.isSet\n *\n * @param {Element} element The Element being searched for\n * @return {boolean} Indicates if the element or CSS selector was previously\n * passed to interact\n*/\ninteract.isSet = function (element, options) {\n  return scope.interactables.indexOfElement(element, options && options.context) !== -1;\n};\n\n/**\n * Add a global listener for an InteractEvent or adds a DOM event to `document`\n *\n * @alias module:interact.on\n *\n * @param {string | array | object} type The types of events to listen for\n * @param {function} listener The function event (s)\n * @param {object | boolean} [options] object or useCapture flag for\n * addEventListener\n * @return {object} interact\n */\ninteract.on = function (type, listener, options) {\n  if (utils.is.string(type) && type.search(' ') !== -1) {\n    type = type.trim().split(/ +/);\n  }\n\n  if (utils.is.array(type)) {\n    for (const eventType of type) {\n      interact.on(eventType, listener, options);\n    }\n\n    return interact;\n  }\n\n  if (utils.is.object(type)) {\n    for (const prop in type) {\n      interact.on(prop, type[prop], listener);\n    }\n\n    return interact;\n  }\n\n  // if it is an InteractEvent type, add listener to globalEvents\n  if (utils.contains(Interactable.eventTypes, type)) {\n    // if this type of event was never bound\n    if (!globalEvents[type]) {\n      globalEvents[type] = [listener];\n    }\n    else {\n      globalEvents[type].push(listener);\n    }\n  }\n  // If non InteractEvent type, addEventListener to document\n  else {\n    events.add(scope.document, type, listener, { options });\n  }\n\n  return interact;\n};\n\n/**\n * Removes a global InteractEvent listener or DOM event from `document`\n *\n * @alias module:interact.off\n *\n * @param {string | array | object} type The types of events that were listened\n * for\n * @param {function} listener The listener function to be removed\n * @param {object | boolean} options [options] object or useCapture flag for\n * removeEventListener\n * @return {object} interact\n */\ninteract.off = function (type, listener, options) {\n  if (utils.is.string(type) && type.search(' ') !== -1) {\n    type = type.trim().split(/ +/);\n  }\n\n  if (utils.is.array(type)) {\n    for (const eventType of type) {\n      interact.off(eventType, listener, options);\n    }\n\n    return interact;\n  }\n\n  if (utils.is.object(type)) {\n    for (const prop in type) {\n      interact.off(prop, type[prop], listener);\n    }\n\n    return interact;\n  }\n\n  if (!utils.contains(Interactable.eventTypes, type)) {\n    events.remove(scope.document, type, listener, options);\n  }\n  else {\n    let index;\n\n    if (type in globalEvents\n        && (index = globalEvents[type].indexOf(listener)) !== -1) {\n      globalEvents[type].splice(index, 1);\n    }\n  }\n\n  return interact;\n};\n\n/**\n * Returns an object which exposes internal data\n\n * @alias module:interact.debug\n *\n * @return {object} An object with properties that outline the current state\n * and expose internal functions and variables\n */\ninteract.debug = function () {\n  return scope;\n};\n\n// expose the functions used to calculate multi-touch properties\ninteract.getPointerAverage  = utils.pointerAverage;\ninteract.getTouchBBox       = utils.touchBBox;\ninteract.getTouchDistance   = utils.touchDistance;\ninteract.getTouchAngle      = utils.touchAngle;\n\ninteract.getElementRect       = utils.getElementRect;\ninteract.getElementClientRect = utils.getElementClientRect;\ninteract.matchesSelector      = utils.matchesSelector;\ninteract.closest              = utils.closest;\n\n/**\n * @alias module:interact.supportsTouch\n *\n * @return {boolean} Whether or not the browser supports touch input\n */\ninteract.supportsTouch = function () {\n  return browser.supportsTouch;\n};\n\n/**\n * @alias module:interact.supportsPointerEvent\n *\n * @return {boolean} Whether or not the browser supports PointerEvents\n */\ninteract.supportsPointerEvent = function () {\n  return browser.supportsPointerEvent;\n};\n\n/**\n * Cancels all interactions (end events are not fired)\n *\n * @alias module:interact.stop\n *\n * @param {Event} event An event on which to call preventDefault()\n * @return {object} interact\n */\ninteract.stop = function (event) {\n  for (let i = scope.interactions.length - 1; i >= 0; i--) {\n    scope.interactions[i].stop(event);\n  }\n\n  return interact;\n};\n\n/**\n * Returns or sets the distance the pointer must be moved before an action\n * sequence occurs. This also affects tolerance for tap events.\n *\n * @alias module:interact.pointerMoveTolerance\n *\n * @param {number} [newValue] The movement from the start position must be greater than this value\n * @return {interact | number}\n */\ninteract.pointerMoveTolerance = function (newValue) {\n  if (utils.is.number(newValue)) {\n    Interaction.pointerMoveTolerance = newValue;\n\n    return interact;\n  }\n\n  return Interaction.pointerMoveTolerance;\n};\n\ninteract.addDocument    = scope.addDocument;\ninteract.removeDocument = scope.removeDocument;\n\nscope.interact = interact;\n\nmodule.exports = interact;\n", "const Interactable = require('./Interactable');\nconst Interaction  = require('./Interaction');\nconst scope        = require('./scope');\nconst is           = require('./utils/is');\nconst events       = require('./utils/events');\nconst browser      = require('./utils/browser');\n\nconst { nodeContains, matchesSelector } = require('./utils/domUtils');\n\n/**\n * Returns or sets whether to prevent the browser's default behaviour in\n * response to pointer events. Can be set to:\n *  - `'always'` to always prevent\n *  - `'never'` to never prevent\n *  - `'auto'` to let interact.js try to determine what would be best\n *\n * @param {string} [newValue] `true`, `false` or `'auto'`\n * @return {string | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.preventDefault = function (newValue) {\n  if (/^(always|never|auto)$/.test(newValue)) {\n    this.options.preventDefault = newValue;\n    return this;\n  }\n\n  if (is.bool(newValue)) {\n    this.options.preventDefault = newValue? 'always' : 'never';\n    return this;\n  }\n\n  return this.options.preventDefault;\n};\n\nInteractable.prototype.checkAndPreventDefault = function (event) {\n  const setting = this.options.preventDefault;\n\n  if (setting === 'never') { return; }\n\n  if (setting === 'always') {\n    event.preventDefault();\n    return;\n  }\n\n  // setting === 'auto'\n\n  // don't preventDefault of touch{start,move} events if the browser supports passive\n  // events listeners. CSS touch-action and user-selecct should be used instead\n  if (events.supportsPassive\n    && /^touch(start|move)$/.test(event.type)\n    && !browser.isIOS) {\n    return;\n  }\n\n  // don't preventDefault of pointerdown events\n  if (/^(mouse|pointer|touch)*(down|start)/i.test(event.type)) {\n    return;\n  }\n\n  // don't preventDefault on editable elements\n  if (is.element(event.target)\n      && matchesSelector(event.target, 'input,select,textarea,[contenteditable=true],[contenteditable=true] *')) {\n    return;\n  }\n\n  event.preventDefault();\n};\n\nfunction onInteractionEvent ({ interaction, event }) {\n  if (interaction.target) {\n    interaction.target.checkAndPreventDefault(event);\n  }\n}\n\nfor (const eventSignal of ['down', 'move', 'up', 'cancel']) {\n  Interaction.signals.on(eventSignal, onInteractionEvent);\n}\n\n// prevent native HTML5 drag on interact.js target elements\nInteraction.docEvents.dragstart = function preventNativeDrag (event) {\n  for (const interaction of scope.interactions) {\n\n    if (interaction.element\n        && (interaction.element === event.target\n            || nodeContains(interaction.element, event.target))) {\n\n      interaction.target.checkAndPreventDefault(event);\n      return;\n    }\n  }\n};\n", "const InteractEvent = require('../InteractEvent');\nconst Interaction   = require('../Interaction');\nconst extend        = require('../utils/extend');\n\nconst modifiers = {\n  names: [],\n\n  setOffsets: function (arg) {\n    const { interaction, pageCoords: page } = arg;\n    const { target, element, startOffset } = interaction;\n    const rect = target.getRect(element);\n\n    if (rect) {\n      startOffset.left = page.x - rect.left;\n      startOffset.top  = page.y - rect.top;\n\n      startOffset.right  = rect.right  - page.x;\n      startOffset.bottom = rect.bottom - page.y;\n\n      if (!('width'  in rect)) { rect.width  = rect.right  - rect.left; }\n      if (!('height' in rect)) { rect.height = rect.bottom - rect.top ; }\n    }\n    else {\n      startOffset.left = startOffset.top = startOffset.right = startOffset.bottom = 0;\n    }\n\n    arg.rect = rect;\n    arg.interactable = target;\n    arg.element = element;\n\n    for (const modifierName of modifiers.names) {\n      arg.options = target.options[interaction.prepared.name][modifierName];\n\n      if (!arg.options) {\n        continue;\n      }\n\n      interaction.modifierOffsets[modifierName] = modifiers[modifierName].setOffset(arg);\n    }\n  },\n\n  setAll: function (arg) {\n    const { interaction, statuses, preEnd, requireEndOnly } = arg;\n    const result = {\n      dx: 0,\n      dy: 0,\n      changed: false,\n      locked: false,\n      shouldMove: true,\n    };\n\n    arg.modifiedCoords = extend({}, arg.pageCoords);\n\n    for (const modifierName of modifiers.names) {\n      const modifier = modifiers[modifierName];\n      const options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      if (!shouldDo(options, preEnd, requireEndOnly)) { continue; }\n\n      arg.status = arg.status = statuses[modifierName];\n      arg.options = options;\n      arg.offset = arg.interaction.modifierOffsets[modifierName];\n\n      modifier.set(arg);\n\n      if (arg.status.locked) {\n        arg.modifiedCoords.x += arg.status.dx;\n        arg.modifiedCoords.y += arg.status.dy;\n\n        result.dx += arg.status.dx;\n        result.dy += arg.status.dy;\n\n        result.locked = true;\n      }\n    }\n\n    // a move should be fired if:\n    //  - there are no modifiers enabled,\n    //  - no modifiers are \"locked\" i.e. have changed the pointer's coordinates, or\n    //  - the locked coords have changed since the last pointer move\n    result.shouldMove = !arg.status || !result.locked || arg.status.changed;\n\n    return result;\n  },\n\n  resetStatuses: function (statuses) {\n    for (const modifierName of modifiers.names) {\n      const status = statuses[modifierName] || {};\n\n      status.dx = status.dy = 0;\n      status.modifiedX = status.modifiedY = NaN;\n      status.locked = false;\n      status.changed = true;\n\n      statuses[modifierName] = status;\n    }\n\n    return statuses;\n  },\n\n  start: function ({ interaction }, signalName) {\n    const arg = {\n      interaction,\n      pageCoords: (signalName === 'action-resume' ?\n                   interaction.curCoords : interaction.startCoords).page,\n      startOffset: interaction.startOffset,\n      statuses: interaction.modifierStatuses,\n      preEnd: false,\n      requireEndOnly: false,\n    };\n\n    modifiers.setOffsets(arg);\n    modifiers.resetStatuses(arg.statuses);\n\n    arg.pageCoords = extend({}, interaction.startCoords.page);\n    interaction.modifierResult = modifiers.setAll(arg);\n  },\n\n  beforeMove: function ({ interaction, preEnd, interactingBeforeMove }) {\n    const modifierResult = modifiers.setAll({\n      interaction,\n      preEnd,\n      pageCoords: interaction.curCoords.page,\n      statuses: interaction.modifierStatuses,\n      requireEndOnly: false,\n    });\n\n    // don't fire an action move if a modifier would keep the event in the same\n    // cordinates as before\n    if (!modifierResult.shouldMove && interactingBeforeMove) {\n      interaction._dontFireMove = true;\n    }\n\n    interaction.modifierResult = modifierResult;\n  },\n\n  end: function ({ interaction, event }) {\n    for (const modifierName of modifiers.names) {\n      const options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      // if the endOnly option is true for any modifier\n      if (shouldDo(options, true, true)) {\n        // fire a move event at the modified coordinates\n        interaction.doMove({ event, preEnd: true });\n        break;\n      }\n    }\n  },\n\n  setXY: function (arg) {\n    const { iEvent, interaction } = arg;\n    const modifierArg = extend({}, arg);\n\n    for (let i = 0; i < modifiers.names.length; i++) {\n      const modifierName = modifiers.names[i];\n      modifierArg.options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      if (!modifierArg.options) {\n        continue;\n      }\n\n      const modifier = modifiers[modifierName];\n\n      modifierArg.status = interaction.modifierStatuses[modifierName];\n\n      iEvent[modifierName] = modifier.modifyCoords(modifierArg);\n    }\n  },\n};\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.startOffset      = { left: 0, right: 0, top: 0, bottom: 0 };\n  interaction.modifierOffsets  = {};\n  interaction.modifierStatuses = modifiers.resetStatuses({});\n  interaction.modifierResult   = null;\n});\n\nInteraction.signals.on('action-start' , modifiers.start);\nInteraction.signals.on('action-resume', modifiers.start);\nInteraction.signals.on('before-action-move', modifiers.beforeMove);\nInteraction.signals.on('action-end', modifiers.end);\n\nInteractEvent.signals.on('set-xy', modifiers.setXY);\n\nfunction shouldDo (options, preEnd, requireEndOnly) {\n  return (options && options.enabled\n          && (preEnd || !options.endOnly)\n          && (!requireEndOnly || options.endOnly));\n}\n\nmodule.exports = modifiers;\n", "const modifiers      = require('./base');\nconst utils          = require('../utils');\nconst defaultOptions = require('../defaultOptions');\n\nconst restrict = {\n  defaults: {\n    enabled    : false,\n    endOnly    : false,\n    restriction: null,\n    elementRect: null,\n  },\n\n  setOffset: function ({ rect, startOffset, options }) {\n    const elementRect = options && options.elementRect;\n    const offset = {};\n\n    if (rect && elementRect) {\n      offset.left = startOffset.left - (rect.width  * elementRect.left);\n      offset.top  = startOffset.top  - (rect.height * elementRect.top);\n\n      offset.right  = startOffset.right  - (rect.width  * (1 - elementRect.right));\n      offset.bottom = startOffset.bottom - (rect.height * (1 - elementRect.bottom));\n    }\n    else {\n      offset.left = offset.top = offset.right = offset.bottom = 0;\n    }\n\n    return offset;\n  },\n\n  set: function ({ modifiedCoords, interaction, status, options }) {\n    if (!options) { return status; }\n\n    const page = status.useStatusXY\n      ? { x: status.x, y: status.y }\n      : utils.extend({}, modifiedCoords);\n\n    const restriction = getRestrictionRect(options.restriction, interaction, page);\n\n    if (!restriction) { return status; }\n\n    status.dx = 0;\n    status.dy = 0;\n    status.locked = false;\n\n    const rect = restriction;\n    let modifiedX = page.x;\n    let modifiedY = page.y;\n\n    const offset = interaction.modifierOffsets.restrict;\n\n    // object is assumed to have\n    // x, y, width, height or\n    // left, top, right, bottom\n    if ('x' in restriction && 'y' in restriction) {\n      modifiedX = Math.max(Math.min(rect.x + rect.width  - offset.right , page.x), rect.x + offset.left);\n      modifiedY = Math.max(Math.min(rect.y + rect.height - offset.bottom, page.y), rect.y + offset.top );\n    }\n    else {\n      modifiedX = Math.max(Math.min(rect.right  - offset.right , page.x), rect.left + offset.left);\n      modifiedY = Math.max(Math.min(rect.bottom - offset.bottom, page.y), rect.top  + offset.top );\n    }\n\n    status.dx = modifiedX - page.x;\n    status.dy = modifiedY - page.y;\n\n    status.changed = status.modifiedX !== modifiedX || status.modifiedY !== modifiedY;\n    status.locked = !!(status.dx || status.dy);\n\n    status.modifiedX = modifiedX;\n    status.modifiedY = modifiedY;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    const elementRect = options && options.elementRect;\n\n    if (options && options.enabled\n        && !(phase === 'start' && elementRect && status.locked)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n\n        return {\n          dx: status.dx,\n          dy: status.dy,\n        };\n      }\n    }\n  },\n\n  getRestrictionRect,\n};\n\nfunction getRestrictionRect (value, interaction, page) {\n  if (utils.is.function(value)) {\n    return utils.resolveRectLike(value, interaction.target, interaction.element, [page.x, page.y, interaction]);\n  } else {\n    return utils.resolveRectLike(value, interaction.target, interaction.element);\n  }\n}\n\nmodifiers.restrict = restrict;\nmodifiers.names.push('restrict');\n\ndefaultOptions.perAction.restrict = restrict.defaults;\n\nmodule.exports = restrict;\n", "// This module adds the options.resize.restrictEdges setting which sets min and\n// max for the top, left, bottom and right edges of the target being resized.\n//\n// interact(target).resize({\n//   edges: { top: true, left: true },\n//   restrictEdges: {\n//     inner: { top: 200, left: 200, right: 400, bottom: 400 },\n//     outer: { top:   0, left:   0, right: 600, bottom: 600 },\n//   },\n// });\n\nconst modifiers      = require('./base');\nconst utils          = require('../utils');\nconst rectUtils      = require('../utils/rect');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\n\nconst { getRestrictionRect } = require('./restrict');\n\nconst noInner = { top: +Infinity, left: +Infinity, bottom: -Infinity, right: -Infinity };\nconst noOuter = { top: -Infinity, left: -Infinity, bottom: +Infinity, right: +Infinity };\n\nconst restrictEdges = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    min: null,\n    max: null,\n    offset: null,\n  },\n\n  setOffset: function ({ interaction, startOffset, options }) {\n    if (!options) {\n      return utils.extend({}, startOffset);\n    }\n\n    const offset = getRestrictionRect(options.offset, interaction, interaction.startCoords.page);\n\n    if (offset) {\n      return {\n        top:    startOffset.top    + offset.y,\n        left:   startOffset.left   + offset.x,\n        bottom: startOffset.bottom + offset.y,\n        right:  startOffset.right  + offset.x,\n      };\n    }\n\n    return startOffset;\n  },\n\n  set: function ({ modifiedCoords, interaction, status, offset, options }) {\n    const edges = interaction.prepared.linkedEdges || interaction.prepared.edges;\n\n    if (!interaction.interacting() || !edges) {\n      return;\n    }\n\n    const page = status.useStatusXY\n      ? { x: status.x, y: status.y }\n      : utils.extend({}, modifiedCoords);\n    const inner = rectUtils.xywhToTlbr(getRestrictionRect(options.inner, interaction, page)) || noInner;\n    const outer = rectUtils.xywhToTlbr(getRestrictionRect(options.outer, interaction, page)) || noOuter;\n\n    let modifiedX = page.x;\n    let modifiedY = page.y;\n\n    status.dx = 0;\n    status.dy = 0;\n    status.locked = false;\n\n    if (edges.top) {\n      modifiedY = Math.min(Math.max(outer.top    + offset.top,    page.y), inner.top    + offset.top);\n    }\n    else if (edges.bottom) {\n      modifiedY = Math.max(Math.min(outer.bottom - offset.bottom, page.y), inner.bottom - offset.bottom);\n    }\n    if (edges.left) {\n      modifiedX = Math.min(Math.max(outer.left   + offset.left,   page.x), inner.left   + offset.left);\n    }\n    else if (edges.right) {\n      modifiedX = Math.max(Math.min(outer.right  - offset.right,  page.x), inner.right  - offset.right);\n    }\n\n    status.dx = modifiedX - page.x;\n    status.dy = modifiedY - page.y;\n\n    status.changed = status.modifiedX !== modifiedX || status.modifiedY !== modifiedY;\n    status.locked = !!(status.dx || status.dy);\n\n    status.modifiedX = modifiedX;\n    status.modifiedY = modifiedY;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    if (options && options.enabled\n        && !(phase === 'start' && status.locked)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n\n        return {\n          dx: status.dx,\n          dy: status.dy,\n        };\n      }\n    }\n  },\n\n  noInner,\n  noOuter,\n  getRestrictionRect,\n};\n\nmodifiers.restrictEdges = restrictEdges;\nmodifiers.names.push('restrictEdges');\n\ndefaultOptions.perAction.restrictEdges = restrictEdges.defaults;\nresize.defaults.restrictEdges          = restrictEdges.defaults;\n\nmodule.exports = restrictEdges;\n", "// This module adds the options.resize.restrictSize setting which sets min and\n// max width and height for the target being resized.\n//\n// interact(target).resize({\n//   edges: { top: true, left: true },\n//   restrictSize: {\n//     min: { width: -600, height: -600 },\n//     max: { width:  600, height:  600 },\n//   },\n// });\n\nconst modifiers      = require('./base');\nconst restrictEdges  = require('./restrictEdges');\nconst utils          = require('../utils');\nconst rectUtils      = require('../utils/rect');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\n\nconst noMin = { width: -Infinity, height: -Infinity };\nconst noMax = { width: +Infinity, height: +Infinity };\n\nconst restrictSize = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    min: null,\n    max: null,\n  },\n\n  setOffset: function ({ interaction }) {\n    return interaction.startOffset;\n  },\n\n  set: function (arg) {\n    const { interaction, options } = arg;\n    const edges = interaction.prepared.linkedEdges || interaction.prepared.edges;\n\n    if (!interaction.interacting() || !edges) {\n      return;\n    }\n\n    const rect = rectUtils.xywhToTlbr(interaction.resizeRects.inverted);\n\n    const minSize = rectUtils.tlbrToXywh(restrictEdges.getRestrictionRect(options.min, interaction)) || noMin;\n    const maxSize = rectUtils.tlbrToXywh(restrictEdges.getRestrictionRect(options.max, interaction)) || noMax;\n\n    arg.options = {\n      enabled: options.enabled,\n      endOnly: options.endOnly,\n      inner: utils.extend({}, restrictEdges.noInner),\n      outer: utils.extend({}, restrictEdges.noOuter),\n    };\n\n    if (edges.top) {\n      arg.options.inner.top = rect.bottom - minSize.height;\n      arg.options.outer.top = rect.bottom - maxSize.height;\n    }\n    else if (edges.bottom) {\n      arg.options.inner.bottom = rect.top + minSize.height;\n      arg.options.outer.bottom = rect.top + maxSize.height;\n    }\n    if (edges.left) {\n      arg.options.inner.left = rect.right - minSize.width;\n      arg.options.outer.left = rect.right - maxSize.width;\n    }\n    else if (edges.right) {\n      arg.options.inner.right = rect.left + minSize.width;\n      arg.options.outer.right = rect.left + maxSize.width;\n    }\n\n    restrictEdges.set(arg);\n  },\n\n  modifyCoords: restrictEdges.modifyCoords,\n};\n\nmodifiers.restrictSize = restrictSize;\nmodifiers.names.push('restrictSize');\n\ndefaultOptions.perAction.restrictSize = restrictSize.defaults;\nresize.defaults.restrictSize          = restrictSize.defaults;\n\nmodule.exports = restrictSize;\n", "const modifiers      = require('./base');\nconst interact       = require('../interact');\nconst utils          = require('../utils');\nconst defaultOptions = require('../defaultOptions');\n\nconst snap = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    range  : Infinity,\n    targets: null,\n    offsets: null,\n\n    relativePoints: null,\n  },\n\n  setOffset: function ({ interaction, interactable, element, rect, startOffset, options }) {\n    const offsets = [];\n    const optionsOrigin = utils.rectToXY(utils.resolveRectLike(options.origin));\n    const origin = optionsOrigin || utils.getOriginXY(interactable, element, interaction.prepared.name);\n    options = options || interactable.options[interaction.prepared.name].snap || {};\n\n    let snapOffset;\n\n    if (options.offset === 'startCoords') {\n      snapOffset = {\n        x: interaction.startCoords.page.x - origin.x,\n        y: interaction.startCoords.page.y - origin.y,\n      };\n    }\n    else  {\n      const offsetRect = utils.resolveRectLike(options.offset, interactable, element, [interaction]);\n\n      snapOffset = utils.rectToXY(offsetRect) || { x: 0, y: 0 };\n    }\n\n    if (rect && options.relativePoints && options.relativePoints.length) {\n      for (const { x: relativeX, y: relativeY } of options.relativePoints) {\n        offsets.push({\n          x: startOffset.left - (rect.width  * relativeX) + snapOffset.x,\n          y: startOffset.top  - (rect.height * relativeY) + snapOffset.y,\n        });\n      }\n    }\n    else {\n      offsets.push(snapOffset);\n    }\n\n    return offsets;\n  },\n\n  set: function ({ interaction, modifiedCoords, status, options, offset: offsets }) {\n    const targets = [];\n    let target;\n    let page;\n    let i;\n\n    if (status.useStatusXY) {\n      page = { x: status.x, y: status.y };\n    }\n    else {\n      const origin = utils.getOriginXY(interaction.target, interaction.element, interaction.prepared.name);\n\n      page = utils.extend({}, modifiedCoords);\n\n      page.x -= origin.x;\n      page.y -= origin.y;\n    }\n\n    status.realX = page.x;\n    status.realY = page.y;\n\n    let len = options.targets? options.targets.length : 0;\n\n    for (const { x: offsetX, y: offsetY } of offsets) {\n      const relativeX = page.x - offsetX;\n      const relativeY = page.y - offsetY;\n\n      for (const snapTarget of (options.targets || [])) {\n        if (utils.is.function(snapTarget)) {\n          target = snapTarget(relativeX, relativeY, interaction);\n        }\n        else {\n          target = snapTarget;\n        }\n\n        if (!target) { continue; }\n\n        targets.push({\n          x: utils.is.number(target.x) ? (target.x + offsetX) : relativeX,\n          y: utils.is.number(target.y) ? (target.y + offsetY) : relativeY,\n\n          range: utils.is.number(target.range)? target.range: options.range,\n        });\n      }\n    }\n\n    const closest = {\n      target: null,\n      inRange: false,\n      distance: 0,\n      range: 0,\n      dx: 0,\n      dy: 0,\n    };\n\n    for (i = 0, len = targets.length; i < len; i++) {\n      target = targets[i];\n\n      const range = target.range;\n      const dx = target.x - page.x;\n      const dy = target.y - page.y;\n      const distance = utils.hypot(dx, dy);\n      let inRange = distance <= range;\n\n      // Infinite targets count as being out of range\n      // compared to non infinite ones that are in range\n      if (range === Infinity && closest.inRange && closest.range !== Infinity) {\n        inRange = false;\n      }\n\n      if (!closest.target || (inRange\n          // is the closest target in range?\n          ? (closest.inRange && range !== Infinity\n          // the pointer is relatively deeper in this target\n          ? distance / range < closest.distance / closest.range\n          // this target has Infinite range and the closest doesn't\n          : (range === Infinity && closest.range !== Infinity)\n          // OR this target is closer that the previous closest\n        || distance < closest.distance)\n          // The other is not in range and the pointer is closer to this target\n          : (!closest.inRange && distance < closest.distance))) {\n\n        closest.target = target;\n        closest.distance = distance;\n        closest.range = range;\n        closest.inRange = inRange;\n        closest.dx = dx;\n        closest.dy = dy;\n\n        status.range = range;\n      }\n    }\n\n    let snapChanged;\n\n    if (closest.target) {\n      snapChanged = (status.modifiedX !== closest.target.x || status.modifiedY !== closest.target.y);\n\n      status.modifiedX = closest.target.x;\n      status.modifiedY = closest.target.y;\n    }\n    else {\n      snapChanged = true;\n\n      status.modifiedX = NaN;\n      status.modifiedY = NaN;\n    }\n\n    status.dx = closest.dx;\n    status.dy = closest.dy;\n\n    status.changed = (snapChanged || (closest.inRange && !status.locked));\n    status.locked = closest.inRange;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    const relativePoints = options && options.relativePoints;\n\n    if (options && options.enabled\n        && !(phase === 'start' && relativePoints && relativePoints.length)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n      }\n\n      return {\n        range  : status.range,\n        locked : status.locked,\n        x      : status.modifiedX,\n        y      : status.modifiedY,\n        realX  : status.realX,\n        realY  : status.realY,\n        dx     : status.dx,\n        dy     : status.dy,\n      };\n    }\n  },\n};\n\ninteract.createSnapGrid = function (grid) {\n  return function (x, y) {\n    const limits = grid.limits || {\n      left  : -Infinity,\n      right :  Infinity,\n      top   : -Infinity,\n      bottom:  Infinity,\n    };\n    let offsetX = 0;\n    let offsetY = 0;\n\n    if (utils.is.object(grid.offset)) {\n      offsetX = grid.offset.x;\n      offsetY = grid.offset.y;\n    }\n\n    const gridx = Math.round((x - offsetX) / grid.x);\n    const gridy = Math.round((y - offsetY) / grid.y);\n\n    const newX = Math.max(limits.left, Math.min(limits.right , gridx * grid.x + offsetX));\n    const newY = Math.max(limits.top , Math.min(limits.bottom, gridy * grid.y + offsetY));\n\n    return {\n      x: newX,\n      y: newY,\n      range: grid.range,\n    };\n  };\n};\n\nmodifiers.snap = snap;\nmodifiers.names.push('snap');\n\ndefaultOptions.perAction.snap = snap.defaults;\n\nmodule.exports = snap;\n", "// This module allows snapping of the size of targets during resize\n// interactions.\n\nconst modifiers      = require('./base');\nconst snap           = require('./snap');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\nconst utils          = require('../utils/');\n\nconst snapSize = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    range  : Infinity,\n    targets: null,\n    offsets: null,\n  },\n\n  setOffset: function (arg) {\n    const { interaction, options } = arg;\n    const edges = interaction.prepared.edges;\n\n    if (!edges) { return; }\n\n    arg.options = {\n      relativePoints: [{\n        x: edges.left? 0 : 1,\n        y: edges.top ? 0 : 1,\n      }],\n      origin: { x: 0, y: 0 },\n      offset: 'self',\n      range: options.range,\n    };\n\n    const offsets = snap.setOffset(arg);\n    arg.options = options;\n\n    return offsets;\n  },\n\n  set: function (arg) {\n    const { interaction, options, offset, modifiedCoords } = arg;\n    const page = utils.extend({}, modifiedCoords);\n    const relativeX = page.x - offset[0].x;\n    const relativeY = page.y - offset[0].y;\n\n    arg.options = utils.extend({}, options);\n    arg.options.targets = [];\n\n    for (const snapTarget of (options.targets || [])) {\n      let target;\n\n      if (utils.is.function(snapTarget)) {\n        target = snapTarget(relativeX, relativeY, interaction);\n      }\n      else {\n        target = snapTarget;\n      }\n\n      if (!target) { continue; }\n\n      if ('width' in target && 'height' in target) {\n        target.x = target.width;\n        target.y = target.height;\n      }\n\n      arg.options.targets.push(target);\n    }\n\n    snap.set(arg);\n  },\n\n  modifyCoords: function (arg) {\n    const { options } = arg;\n\n    arg.options = utils.extend({}, options);\n    arg.options.enabled = options.enabled;\n    arg.options.relativePoints = [null];\n\n    snap.modifyCoords(arg);\n  },\n};\n\nmodifiers.snapSize = snapSize;\nmodifiers.names.push('snapSize');\n\ndefaultOptions.perAction.snapSize = snapSize.defaults;\nresize.defaults.snapSize          = snapSize.defaults;\n\nmodule.exports = snapSize;\n", "const pointerUtils = require('../utils/pointerUtils');\n\nmodule.exports = class PointerEvent {\n  /** */\n  constructor (type, pointer, event, eventTarget, interaction) {\n    pointerUtils.pointerExtend(this, event);\n\n    if (event !== pointer) {\n      pointerUtils.pointerExtend(this, pointer);\n    }\n\n    this.interaction = interaction;\n\n    this.timeStamp     = new Date().getTime();\n    this.originalEvent = event;\n    this.type          = type;\n    this.pointerId     = pointerUtils.getPointerId(pointer);\n    this.pointerType   = pointerUtils.getPointerType(pointer);\n    this.target        = eventTarget;\n    this.currentTarget = null;\n\n    if (type === 'tap') {\n      const pointerIndex = interaction.getPointerIndex(pointer);\n      this.dt = this.timeStamp - interaction.downTimes[pointerIndex];\n\n      const interval = this.timeStamp - interaction.tapTime;\n\n      this.double = !!(interaction.prevTap\n        && interaction.prevTap.type !== 'doubletap'\n        && interaction.prevTap.target === this.target\n        && interval < 500);\n    }\n    else if (type === 'doubletap') {\n      this.dt = pointer.timeStamp - interaction.tapTime;\n    }\n  }\n\n  subtractOrigin ({ x: originX, y: originY }) {\n    this.pageX   -= originX;\n    this.pageY   -= originY;\n    this.clientX -= originX;\n    this.clientY -= originY;\n\n    return this;\n  }\n\n  addOrigin ({ x: originX, y: originY }) {\n    this.pageX   += originX;\n    this.pageY   += originY;\n    this.clientX += originX;\n    this.clientY += originY;\n\n    return this;\n  }\n\n  /** */\n  preventDefault () {\n    this.originalEvent.preventDefault();\n  }\n\n  /** */\n  stopPropagation () {\n    this.propagationStopped = true;\n  }\n\n  /** */\n  stopImmediatePropagation () {\n    this.immediatePropagationStopped = this.propagationStopped = true;\n  }\n};\n", "const PointerEvent = require('./PointerEvent');\nconst Interaction  = require('../Interaction');\nconst utils        = require('../utils');\nconst defaults     = require('../defaultOptions');\nconst signals      = require('../utils/Signals').new();\n\nconst simpleSignals = [ 'down', 'up', 'cancel' ];\nconst simpleEvents  = [ 'down', 'up', 'cancel' ];\n\nconst pointerEvents = {\n  PointerEvent,\n  fire,\n  collectEventTargets,\n  signals,\n  defaults: {\n    holdDuration: 600,\n    ignoreFrom  : null,\n    allowFrom   : null,\n    origin      : { x: 0, y: 0 },\n  },\n  types: [\n    'down',\n    'move',\n    'up',\n    'cancel',\n    'tap',\n    'doubletap',\n    'hold',\n  ],\n};\n\nfunction fire (arg) {\n  const {\n    interaction, pointer, event, eventTarget,\n    type = arg.pointerEvent.type,\n    targets = collectEventTargets(arg),\n    pointerEvent = new PointerEvent(type, pointer, event, eventTarget, interaction),\n  } = arg;\n\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    targets,\n    type,\n    pointerEvent,\n  };\n\n  for (let i = 0; i < targets.length; i++) {\n    const target = targets[i];\n\n    for (const prop in target.props || {}) {\n      pointerEvent[prop] = target.props[prop];\n    }\n\n    const origin = utils.getOriginXY(target.eventable, target.element);\n\n    pointerEvent.subtractOrigin(origin);\n    pointerEvent.eventable = target.eventable;\n    pointerEvent.currentTarget = target.element;\n\n    target.eventable.fire(pointerEvent);\n\n    pointerEvent.addOrigin(origin);\n\n    if (pointerEvent.immediatePropagationStopped\n        || (pointerEvent.propagationStopped\n            && (i + 1) < targets.length && targets[i + 1].element !== pointerEvent.currentTarget)) {\n      break;\n    }\n  }\n\n  signals.fire('fired', signalArg);\n\n  if (type === 'tap') {\n    // if pointerEvent should make a double tap, create and fire a doubletap\n    // PointerEvent and use that as the prevTap\n    const prevTap = pointerEvent.double\n      ? fire({\n        interaction, pointer, event, eventTarget,\n        type: 'doubletap',\n      })\n      : pointerEvent;\n\n    interaction.prevTap = prevTap;\n    interaction.tapTime = prevTap.timeStamp;\n  }\n\n  return pointerEvent;\n}\n\nfunction collectEventTargets ({ interaction, pointer, event, eventTarget, type }) {\n  const pointerIndex = interaction.getPointerIndex(pointer);\n\n  // do not fire a tap event if the pointer was moved before being lifted\n  if (type === 'tap' && (interaction.pointerWasMoved\n      // or if the pointerup target is different to the pointerdown target\n      || !(interaction.downTargets[pointerIndex] && interaction.downTargets[pointerIndex] === eventTarget))) {\n    return [];\n  }\n\n  const path = utils.getPath(eventTarget);\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type,\n    path,\n    targets: [],\n    element: null,\n  };\n\n  for (const element of path) {\n    signalArg.element = element;\n\n    signals.fire('collect-targets', signalArg);\n  }\n\n  if (type === 'hold') {\n    signalArg.targets = signalArg.targets.filter(target =>\n      target.eventable.options.holdDuration === interaction.holdTimers[pointerIndex].duration);\n  }\n\n  return signalArg.targets;\n}\n\nInteraction.signals.on('update-pointer-down', function ({ interaction, pointerIndex }) {\n  interaction.holdTimers[pointerIndex] = { duration: Infinity, timeout: null };\n});\n\nInteraction.signals.on('remove-pointer', function ({ interaction, pointerIndex }) {\n  interaction.holdTimers.splice(pointerIndex, 1);\n});\n\nInteraction.signals.on('move', function ({ interaction, pointer, event, eventTarget, duplicateMove }) {\n  const pointerIndex = interaction.getPointerIndex(pointer);\n\n  if (!duplicateMove && (!interaction.pointerIsDown || interaction.pointerWasMoved)) {\n    if (interaction.pointerIsDown) {\n      clearTimeout(interaction.holdTimers[pointerIndex].timeout);\n    }\n\n    fire({\n      interaction, pointer, event, eventTarget,\n      type: 'move',\n    });\n  }\n});\n\nInteraction.signals.on('down', function ({ interaction, pointer, event, eventTarget, pointerIndex }) {\n  const timer = interaction.holdTimers[pointerIndex];\n  const path = utils.getPath(eventTarget);\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type: 'hold',\n    targets: [],\n    path,\n    element: null,\n  };\n\n  for (const element of path) {\n    signalArg.element = element;\n\n    signals.fire('collect-targets', signalArg);\n  }\n\n  if (!signalArg.targets.length) { return; }\n\n  let minDuration = Infinity;\n\n  for (const target of signalArg.targets) {\n    const holdDuration = target.eventable.options.holdDuration;\n\n    if (holdDuration < minDuration) {\n      minDuration = holdDuration;\n    }\n  }\n\n  timer.duration = minDuration;\n  timer.timeout = setTimeout(function () {\n    fire({\n      interaction,\n      eventTarget,\n      pointer,\n      event,\n      type: 'hold',\n    });\n  }, minDuration);\n});\n\nInteraction.signals.on('up', ({ interaction, pointer, event, eventTarget }) => {\n  if (!interaction.pointerWasMoved) {\n    fire({ interaction, eventTarget, pointer, event, type: 'tap' });\n  }\n});\n\nfor (const signalName of ['up', 'cancel']) {\n  Interaction.signals.on(signalName, function ({ interaction, pointerIndex }) {\n    if (interaction.holdTimers[pointerIndex]) {\n      clearTimeout(interaction.holdTimers[pointerIndex].timeout);\n    }\n  });\n}\n\nfunction createSignalListener (type) {\n  return function ({ interaction, pointer, event, eventTarget }) {\n    fire({ interaction, eventTarget, pointer, event, type });\n  };\n}\n\nfor (let i = 0; i < simpleSignals.length; i++) {\n  Interaction.signals.on(simpleSignals[i], createSignalListener(simpleEvents[i]));\n}\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.prevTap    = null;  // the most recent tap event on this interaction\n  interaction.tapTime    = 0;     // time of the most recent tap event\n  interaction.holdTimers = [];    // [{ duration, timeout }]\n});\n\ndefaults.pointerEvents = pointerEvents.defaults;\nmodule.exports = pointerEvents;\n", "const pointerEvents = require('./base');\nconst Interaction   = require('../Interaction');\n\npointerEvents.signals.on('new', onNew);\npointerEvents.signals.on('fired', onFired);\n\nfor (const signal of ['move', 'up', 'cancel', 'endall']) {\n  Interaction.signals.on(signal, endHoldRepeat);\n}\n\nfunction onNew ({ pointerEvent }) {\n  if (pointerEvent.type !== 'hold') { return; }\n\n  pointerEvent.count = (pointerEvent.count || 0) + 1;\n}\n\nfunction onFired ({ interaction, pointerEvent, eventTarget, targets }) {\n  if (pointerEvent.type !== 'hold' || !targets.length) { return; }\n\n  // get the repeat interval from the first eventable\n  const interval = targets[0].eventable.options.holdRepeatInterval;\n\n  // don't repeat if the interval is 0 or less\n  if (interval <= 0) { return; }\n\n  // set a timeout to fire the holdrepeat event\n  interaction.holdIntervalHandle = setTimeout(function () {\n    pointerEvents.fire({\n      interaction,\n      eventTarget,\n      type: 'hold',\n      pointer: pointerEvent,\n      event: pointerEvent,\n    });\n  }, interval);\n}\n\nfunction endHoldRepeat ({ interaction }) {\n  // set the interaction's holdStopTime property\n  // to stop further holdRepeat events\n  if (interaction.holdIntervalHandle) {\n    clearInterval(interaction.holdIntervalHandle);\n    interaction.holdIntervalHandle = null;\n  }\n}\n\n// don't repeat by default\npointerEvents.defaults.holdRepeatInterval = 0;\npointerEvents.types.push('holdrepeat');\n\nmodule.exports = {\n  onNew,\n  onFired,\n  endHoldRepeat,\n};\n", "const pointerEvents = require('./base');\nconst Interactable  = require('../Interactable');\nconst is            = require('../utils/is');\nconst scope         = require('../scope');\nconst extend        = require('../utils/extend');\nconst { merge }     = require('../utils/arr');\n\npointerEvents.signals.on('collect-targets', function ({ targets, element, type, eventTarget }) {\n  scope.interactables.forEachMatch(element, interactable => {\n    const eventable = interactable.events;\n    const options = eventable.options;\n\n    if (eventable[type]\n      && is.element(element)\n      && interactable.testIgnoreAllow(options, element, eventTarget)) {\n\n      targets.push({\n        element,\n        eventable,\n        props: { interactable },\n      });\n    }\n  });\n});\n\nInteractable.signals.on('new', function ({ interactable }) {\n  interactable.events.getRect = function (element) {\n    return interactable.getRect(element);\n  };\n});\n\nInteractable.signals.on('set', function ({ interactable, options }) {\n  extend(interactable.events.options, pointerEvents.defaults);\n  extend(interactable.events.options, options);\n});\n\nmerge(Interactable.eventTypes, pointerEvents.types);\n\nInteractable.prototype.pointerEvents = function (options) {\n  extend(this.events.options, options);\n\n  return this;\n};\n\nconst __backCompatOption = Interactable.prototype._backCompatOption;\n\nInteractable.prototype._backCompatOption = function (optionName, newValue) {\n  const ret = __backCompatOption.call(this, optionName, newValue);\n\n  if (ret === this) {\n    this.events.options[optionName] = newValue;\n  }\n\n  return ret;\n};\n\nInteractable.settingsMethods.push('pointerEvents');\n", "const utils   = require('./utils');\nconst events  = require('./utils/events');\nconst signals = require('./utils/Signals').new();\n\nconst { getWindow } = require('./utils/window');\n\nconst scope = {\n  signals,\n  events,\n  utils,\n\n  // main document\n  document: require('./utils/domObjects').document,\n  // all documents being listened to\n  documents: [],\n\n  addDocument: function (doc, win) {\n    // do nothing if document is already known\n    if (utils.contains(scope.documents, doc)) { return false; }\n\n    win = win || getWindow(doc);\n\n    scope.documents.push(doc);\n    events.documents.push(doc);\n\n    // don't add an unload event for the main document\n    // so that the page may be cached in browser history\n    if (doc !== scope.document) {\n      events.add(win, 'unload', scope.onWindowUnload);\n    }\n\n    signals.fire('add-document', { doc, win });\n  },\n\n  removeDocument: function (doc, win) {\n    const index = scope.documents.indexOf(doc);\n\n    win = win || getWindow(doc);\n\n    events.remove(win, 'unload', scope.onWindowUnload);\n\n    scope.documents.splice(index, 1);\n    events.documents.splice(index, 1);\n\n    signals.fire('remove-document', { win, doc });\n  },\n\n  onWindowUnload: function () {\n    scope.removeDocument(this.document, this);\n  },\n};\n\nmodule.exports = scope;\n", "class Signals {\n  constructor () {\n    this.listeners = {\n      // signalName: [listeners],\n    };\n  }\n\n  on (name, listener) {\n    if (!this.listeners[name]) {\n      this.listeners[name] = [listener];\n      return;\n    }\n\n    this.listeners[name].push(listener);\n  }\n\n  off (name, listener) {\n    if (!this.listeners[name]) { return; }\n\n    const index = this.listeners[name].indexOf(listener);\n\n    if (index !== -1) {\n      this.listeners[name].splice(index, 1);\n    }\n  }\n\n  fire (name, arg) {\n    const targetListeners = this.listeners[name];\n\n    if (!targetListeners) { return; }\n\n    for (const listener of targetListeners) {\n      if (listener(arg, name) === false) {\n        return;\n      }\n    }\n  }\n}\n\nSignals.new = function () {\n  return new Signals();\n};\n\nmodule.exports = Signals;\n", "function contains (array, target) {\n  return array.indexOf(target) !== -1;\n}\n\nfunction merge (target, source) {\n  for (const item of source) {\n    target.push(item);\n  }\n\n  return target;\n}\n\nmodule.exports = {\n  contains,\n  merge,\n};\n", "const { window } = require('./window');\nconst is     = require('./is');\nconst domObjects = require('./domObjects');\n\nconst Element = domObjects.Element;\nconst navigator  = window.navigator;\n\nconst browser = {\n  // Does the browser support touch input?\n  supportsTouch: !!(('ontouchstart' in window) || is.function(window.DocumentTouch)\n                     && domObjects.document instanceof window.DocumentTouch),\n\n  // Does the browser support PointerEvents\n  supportsPointerEvent: !!domObjects.PointerEvent,\n\n  isIOS: (/iP(hone|od|ad)/.test(navigator.platform)),\n\n  // scrolling doesn't change the result of getClientRects on iOS 7\n  isIOS7: (/iP(hone|od|ad)/.test(navigator.platform)\n           && /OS 7[^\\d]/.test(navigator.appVersion)),\n\n  isIe9: /MSIE 9/.test(navigator.userAgent),\n\n  // prefix matchesSelector\n  prefixedMatchesSelector: 'matches' in Element.prototype\n    ? 'matches': 'webkitMatchesSelector' in Element.prototype\n    ? 'webkitMatchesSelector': 'mozMatchesSelector' in Element.prototype\n    ? 'mozMatchesSelector': 'oMatchesSelector' in Element.prototype\n    ? 'oMatchesSelector': 'msMatchesSelector',\n\n  pEventTypes: (domObjects.PointerEvent\n    ? (domObjects.PointerEvent === window.MSPointerEvent\n      ? {\n        up:     'MSPointerUp',\n        down:   'MSPointerDown',\n        over:   'mouseover',\n        out:    'mouseout',\n        move:   'MSPointerMove',\n        cancel: 'MSPointerCancel',\n      }\n      : {\n        up:     'pointerup',\n        down:   'pointerdown',\n        over:   'pointerover',\n        out:    'pointerout',\n        move:   'pointermove',\n        cancel: 'pointercancel',\n      })\n    : null),\n\n  // because Webkit and Opera still use 'mousewheel' event type\n  wheelEvent: 'onmousewheel' in domObjects.document? 'mousewheel': 'wheel',\n\n};\n\n// Opera Mobile must be handled differently\nbrowser.isOperaMobile = (navigator.appName === 'Opera'\n  && browser.supportsTouch\n  && navigator.userAgent.match('Presto'));\n\nmodule.exports = browser;\n", "const is = require('./is');\n\nmodule.exports = function clone (source) {\n  const dest = {};\n  for (const prop in source) {\n    if (is.plainObject(source[prop])) {\n      dest[prop] = clone(source[prop]);\n    } else {\n      dest[prop] = source[prop];\n    }\n  }\n  return dest;\n};\n", "const domObjects = {};\nconst win = require('./window').window;\n\nfunction blank () {}\n\ndomObjects.document           = win.document;\ndomObjects.DocumentFragment   = win.DocumentFragment   || blank;\ndomObjects.SVGElement         = win.SVGElement         || blank;\ndomObjects.SVGSVGElement      = win.SVGSVGElement      || blank;\ndomObjects.SVGElementInstance = win.SVGElementInstance || blank;\ndomObjects.Element            = win.Element            || blank;\ndomObjects.HTMLElement        = win.HTMLElement        || domObjects.Element;\n\ndomObjects.Event        = win.Event;\ndomObjects.Touch        = win.Touch || blank;\ndomObjects.PointerEvent = (win.PointerEvent || win.MSPointerEvent);\n\nmodule.exports = domObjects;\n", "const win        = require('./window');\nconst browser    = require('./browser');\nconst is         = require('./is');\nconst domObjects = require('./domObjects');\n\nconst domUtils = {\n  nodeContains: function (parent, child) {\n    while (child) {\n      if (child === parent) {\n        return true;\n      }\n\n      child = child.parentNode;\n    }\n\n    return false;\n  },\n\n  closest: function (element, selector) {\n    while (is.element(element)) {\n      if (domUtils.matchesSelector(element, selector)) { return element; }\n\n      element = domUtils.parentNode(element);\n    }\n\n    return null;\n  },\n\n  parentNode: function (node) {\n    let parent = node.parentNode;\n\n    if (is.docFrag(parent)) {\n      // skip past #shado-root fragments\n      while ((parent = parent.host) && is.docFrag(parent)) {\n        continue;\n      }\n\n      return parent;\n    }\n\n    return parent;\n  },\n\n  matchesSelector: function (element, selector) {\n    // remove /deep/ from selectors if shadowDOM polyfill is used\n    if (win.window !== win.realWindow) {\n      selector = selector.replace(/\\/deep\\//g, ' ');\n    }\n\n    return element[browser.prefixedMatchesSelector](selector);\n  },\n\n  // Test for the element that's \"above\" all other qualifiers\n  indexOfDeepestElement: function (elements) {\n    let deepestZoneParents = [];\n    let dropzoneParents = [];\n    let dropzone;\n    let deepestZone = elements[0];\n    let index = deepestZone? 0: -1;\n    let parent;\n    let child;\n    let i;\n    let n;\n\n    for (i = 1; i < elements.length; i++) {\n      dropzone = elements[i];\n\n      // an element might belong to multiple selector dropzones\n      if (!dropzone || dropzone === deepestZone) {\n        continue;\n      }\n\n      if (!deepestZone) {\n        deepestZone = dropzone;\n        index = i;\n        continue;\n      }\n\n      // check if the deepest or current are document.documentElement or document.rootElement\n      // - if the current dropzone is, do nothing and continue\n      if (dropzone.parentNode === dropzone.ownerDocument) {\n        continue;\n      }\n      // - if deepest is, update with the current dropzone and continue to next\n      else if (deepestZone.parentNode === dropzone.ownerDocument) {\n        deepestZone = dropzone;\n        index = i;\n        continue;\n      }\n\n      if (!deepestZoneParents.length) {\n        parent = deepestZone;\n        while (parent.parentNode && parent.parentNode !== parent.ownerDocument) {\n          deepestZoneParents.unshift(parent);\n          parent = parent.parentNode;\n        }\n      }\n\n      // if this element is an svg element and the current deepest is\n      // an HTMLElement\n      if (deepestZone instanceof domObjects.HTMLElement\n          && dropzone instanceof domObjects.SVGElement\n          && !(dropzone instanceof domObjects.SVGSVGElement)) {\n\n        if (dropzone === deepestZone.parentNode) {\n          continue;\n        }\n\n        parent = dropzone.ownerSVGElement;\n      }\n      else {\n        parent = dropzone;\n      }\n\n      dropzoneParents = [];\n\n      while (parent.parentNode !== parent.ownerDocument) {\n        dropzoneParents.unshift(parent);\n        parent = parent.parentNode;\n      }\n\n      n = 0;\n\n      // get (position of last common ancestor) + 1\n      while (dropzoneParents[n] && dropzoneParents[n] === deepestZoneParents[n]) {\n        n++;\n      }\n\n      const parents = [\n        dropzoneParents[n - 1],\n        dropzoneParents[n],\n        deepestZoneParents[n],\n      ];\n\n      child = parents[0].lastChild;\n\n      while (child) {\n        if (child === parents[1]) {\n          deepestZone = dropzone;\n          index = i;\n          deepestZoneParents = [];\n\n          break;\n        }\n        else if (child === parents[2]) {\n          break;\n        }\n\n        child = child.previousSibling;\n      }\n    }\n\n    return index;\n  },\n\n  matchesUpTo: function (element, selector, limit) {\n    while (is.element(element)) {\n      if (domUtils.matchesSelector(element, selector)) {\n        return true;\n      }\n\n      element = domUtils.parentNode(element);\n\n      if (element === limit) {\n        return domUtils.matchesSelector(element, selector);\n      }\n    }\n\n    return false;\n  },\n\n  getActualElement: function (element) {\n    return (element instanceof domObjects.SVGElementInstance\n      ? element.correspondingUseElement\n      : element);\n  },\n\n  getScrollXY: function (relevantWindow) {\n    relevantWindow = relevantWindow || win.window;\n    return {\n      x: relevantWindow.scrollX || relevantWindow.document.documentElement.scrollLeft,\n      y: relevantWindow.scrollY || relevantWindow.document.documentElement.scrollTop,\n    };\n  },\n\n  getElementClientRect: function (element) {\n    const clientRect = (element instanceof domObjects.SVGElement\n      ? element.getBoundingClientRect()\n      : element.getClientRects()[0]);\n\n    return clientRect && {\n      left  : clientRect.left,\n      right : clientRect.right,\n      top   : clientRect.top,\n      bottom: clientRect.bottom,\n      width : clientRect.width  || clientRect.right  - clientRect.left,\n      height: clientRect.height || clientRect.bottom - clientRect.top,\n    };\n  },\n\n  getElementRect: function (element) {\n    const clientRect = domUtils.getElementClientRect(element);\n\n    if (!browser.isIOS7 && clientRect) {\n      const scroll = domUtils.getScrollXY(win.getWindow(element));\n\n      clientRect.left   += scroll.x;\n      clientRect.right  += scroll.x;\n      clientRect.top    += scroll.y;\n      clientRect.bottom += scroll.y;\n    }\n\n    return clientRect;\n  },\n\n  getPath: function (element) {\n    const path = [];\n\n    while (element) {\n      path.push(element);\n      element = domUtils.parentNode(element);\n    }\n\n    return path;\n  },\n\n  trySelector: value => {\n    if (!is.string(value)) { return false; }\n\n    // an exception will be raised if it is invalid\n    domObjects.document.querySelector(value);\n    return true;\n  },\n};\n\nmodule.exports = domUtils;\n", "const is           = require('./is');\nconst domUtils     = require('./domUtils');\nconst pointerUtils = require('./pointerUtils');\nconst pExtend      = require('./pointerExtend');\n\nconst { window }   = require('./window');\nconst { contains } = require('./arr');\n\nconst elements = [];\nconst targets  = [];\n\n// {\n//   type: {\n//     selectors: ['selector', ...],\n//     contexts : [document, ...],\n//     listeners: [[listener, capture, passive], ...]\n//   }\n//  }\nconst delegatedEvents = {};\nconst documents       = [];\n\nconst supportsOptions = (() => {\n  let supported = false;\n\n  window.document.createElement('div').addEventListener('test', null, {\n    get capture () { supported = true; },\n  });\n\n  return supported;\n})();\n\nfunction add (element, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  let elementIndex = elements.indexOf(element);\n  let target = targets[elementIndex];\n\n  if (!target) {\n    target = {\n      events: {},\n      typeCount: 0,\n    };\n\n    elementIndex = elements.push(element) - 1;\n    targets.push(target);\n  }\n\n  if (!target.events[type]) {\n    target.events[type] = [];\n    target.typeCount++;\n  }\n\n  if (!contains(target.events[type], listener)) {\n    element.addEventListener(type, listener, supportsOptions? options : !!options.capture);\n    target.events[type].push(listener);\n  }\n}\n\nfunction remove (element, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  const elementIndex = elements.indexOf(element);\n  const target = targets[elementIndex];\n\n  if (!target || !target.events) {\n    return;\n  }\n\n  if (type === 'all') {\n    for (type in target.events) {\n      if (target.events.hasOwnProperty(type)) {\n        remove(element, type, 'all');\n      }\n    }\n    return;\n  }\n\n  if (target.events[type]) {\n    const len = target.events[type].length;\n\n    if (listener === 'all') {\n      for (let i = 0; i < len; i++) {\n        remove(element, type, target.events[type][i], options);\n      }\n      return;\n    }\n    else {\n      for (let i = 0; i < len; i++) {\n        if (target.events[type][i] === listener) {\n          element.removeEventListener(`on${type}`, listener, supportsOptions? options : !!options.capture);\n          target.events[type].splice(i, 1);\n\n          break;\n        }\n      }\n    }\n\n    if (target.events[type] && target.events[type].length === 0) {\n      target.events[type] = null;\n      target.typeCount--;\n    }\n  }\n\n  if (!target.typeCount) {\n    targets.splice(elementIndex, 1);\n    elements.splice(elementIndex, 1);\n  }\n}\n\nfunction addDelegate (selector, context, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  if (!delegatedEvents[type]) {\n    delegatedEvents[type] = {\n      selectors: [],\n      contexts : [],\n      listeners: [],\n    };\n\n    // add delegate listener functions\n    for (const doc of documents) {\n      add(doc, type, delegateListener);\n      add(doc, type, delegateUseCapture, true);\n    }\n  }\n\n  const delegated = delegatedEvents[type];\n  let index;\n\n  for (index = delegated.selectors.length - 1; index >= 0; index--) {\n    if (delegated.selectors[index] === selector\n        && delegated.contexts[index] === context) {\n      break;\n    }\n  }\n\n  if (index === -1) {\n    index = delegated.selectors.length;\n\n    delegated.selectors.push(selector);\n    delegated.contexts .push(context);\n    delegated.listeners.push([]);\n  }\n\n  // keep listener and capture and passive flags\n  delegated.listeners[index].push([listener, !!options.capture, options.passive]);\n}\n\nfunction removeDelegate (selector, context, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  const delegated = delegatedEvents[type];\n  let matchFound = false;\n  let index;\n\n  if (!delegated) { return; }\n\n  // count from last index of delegated to 0\n  for (index = delegated.selectors.length - 1; index >= 0; index--) {\n    // look for matching selector and context Node\n    if (delegated.selectors[index] === selector\n        && delegated.contexts[index] === context) {\n\n      const listeners = delegated.listeners[index];\n\n      // each item of the listeners array is an array: [function, capture, passive]\n      for (let i = listeners.length - 1; i >= 0; i--) {\n        const [fn, capture, passive] = listeners[i];\n\n        // check if the listener functions and capture and passive flags match\n        if (fn === listener && capture === !!options.capture && passive === options.passive) {\n          // remove the listener from the array of listeners\n          listeners.splice(i, 1);\n\n          // if all listeners for this interactable have been removed\n          // remove the interactable from the delegated arrays\n          if (!listeners.length) {\n            delegated.selectors.splice(index, 1);\n            delegated.contexts .splice(index, 1);\n            delegated.listeners.splice(index, 1);\n\n            // remove delegate function from context\n            remove(context, type, delegateListener);\n            remove(context, type, delegateUseCapture, true);\n\n            // remove the arrays if they are empty\n            if (!delegated.selectors.length) {\n              delegatedEvents[type] = null;\n            }\n          }\n\n          // only remove one listener\n          matchFound = true;\n          break;\n        }\n      }\n\n      if (matchFound) { break; }\n    }\n  }\n}\n\n// bound to the interactable context when a DOM event\n// listener is added to a selector interactable\nfunction delegateListener (event, optionalArg) {\n  const options = getOptions(optionalArg);\n  const fakeEvent = {};\n  const delegated = delegatedEvents[event.type];\n  const [eventTarget] = (pointerUtils.getEventTargets(event));\n  let element = eventTarget;\n\n  // duplicate the event so that currentTarget can be changed\n  pExtend(fakeEvent, event);\n\n  fakeEvent.originalEvent = event;\n  fakeEvent.preventDefault = preventOriginalDefault;\n\n  // climb up document tree looking for selector matches\n  while (is.element(element)) {\n    for (let i = 0; i < delegated.selectors.length; i++) {\n      const selector = delegated.selectors[i];\n      const context = delegated.contexts[i];\n\n      if (domUtils.matchesSelector(element, selector)\n          && domUtils.nodeContains(context, eventTarget)\n          && domUtils.nodeContains(context, element)) {\n\n        const listeners = delegated.listeners[i];\n\n        fakeEvent.currentTarget = element;\n\n        for (let j = 0; j < listeners.length; j++) {\n          const [fn, capture, passive] = listeners[j];\n\n          if (capture === !!options.capture && passive === options.passive) {\n            fn(fakeEvent);\n          }\n        }\n      }\n    }\n\n    element = domUtils.parentNode(element);\n  }\n}\n\nfunction delegateUseCapture (event) {\n  return delegateListener.call(this, event, true);\n}\n\nfunction preventOriginalDefault () {\n  this.originalEvent.preventDefault();\n}\n\nfunction getOptions (param) {\n  return is.object(param)? param : { capture: param };\n}\n\nmodule.exports = {\n  add,\n  remove,\n\n  addDelegate,\n  removeDelegate,\n\n  delegateListener,\n  delegateUseCapture,\n  delegatedEvents,\n  documents,\n\n  supportsOptions,\n\n  _elements: elements,\n  _targets: targets,\n};\n", "module.exports = function extend (dest, source) {\n  for (const prop in source) {\n    dest[prop] = source[prop];\n  }\n  return dest;\n};\n", "const {\n  resolveRectLike,\n  rectToXY,\n} = require('./rect');\n\nmodule.exports = function (target, element, action) {\n  const actionOptions = target.options[action];\n  const actionOrigin = actionOptions && actionOptions.origin;\n  const origin = actionOrigin || target.options.origin;\n\n  const originRect = resolveRectLike(origin, target, element, [target && element]);\n\n  return rectToXY(originRect) || { x: 0, y: 0 };\n};\n", "module.exports = (x, y) =>  Math.sqrt(x * x + y * y);\n", "const extend = require('./extend');\nconst win    = require('./window');\n\nconst utils = {\n  warnOnce: function (method, message) {\n    let warned = false;\n\n    return function () {\n      if (!warned) {\n        win.window.console.warn(message);\n        warned = true;\n      }\n\n      return method.apply(this, arguments);\n    };\n  },\n\n  // http://stackoverflow.com/a/5634528/2280888\n  _getQBezierValue: function (t, p1, p2, p3) {\n    const iT = 1 - t;\n    return iT * iT * p1 + 2 * iT * t * p2 + t * t * p3;\n  },\n\n  getQuadraticCurvePoint: function (startX, startY, cpX, cpY, endX, endY, position) {\n    return {\n      x:  utils._getQBezierValue(position, startX, cpX, endX),\n      y:  utils._getQBezierValue(position, startY, cpY, endY),\n    };\n  },\n\n  // http://gizma.com/easing/\n  easeOutQuad: function (t, b, c, d) {\n    t /= d;\n    return -c * t*(t-2) + b;\n  },\n\n  copyAction: function (dest, src) {\n    dest.name  = src.name;\n    dest.axis  = src.axis;\n    dest.edges = src.edges;\n\n    return dest;\n  },\n\n  is         : require('./is'),\n  extend     : extend,\n  hypot      : require('./hypot'),\n  getOriginXY: require('./getOriginXY'),\n};\n\nextend(utils, require('./arr'));\nextend(utils, require('./domUtils'));\nextend(utils, require('./pointerUtils'));\nextend(utils, require('./rect'));\n\nmodule.exports = utils;\n", "const scope   = require('../scope');\nconst utils   = require('./index');\n\nconst finder = {\n  methodOrder: [ 'simulationResume', 'mouseOrPen', 'hasPointer', 'idle' ],\n\n  search: function (pointer, eventType, eventTarget) {\n    const pointerType = utils.getPointerType(pointer);\n    const pointerId = utils.getPointerId(pointer);\n    const details = { pointer, pointerId, pointerType, eventType, eventTarget };\n\n    for (const method of finder.methodOrder) {\n      const interaction = finder[method](details);\n\n      if (interaction) {\n        return interaction;\n      }\n    }\n  },\n\n  // try to resume simulation with a new pointer\n  simulationResume: function ({ pointerType, eventType, eventTarget }) {\n    if (!/down|start/i.test(eventType)) {\n      return null;\n    }\n\n    for (const interaction of scope.interactions) {\n      let element = eventTarget;\n\n      if (interaction.simulation && interaction.simulation.allowResume\n          && (interaction.pointerType === pointerType)) {\n        while (element) {\n          // if the element is the interaction element\n          if (element === interaction.element) {\n            return interaction;\n          }\n          element = utils.parentNode(element);\n        }\n      }\n    }\n\n    return null;\n  },\n\n  // if it's a mouse or pen interaction\n  mouseOrPen: function ({ pointerId, pointerType, eventType }) {\n    if (pointerType !== 'mouse' && pointerType !== 'pen') {\n      return null;\n    }\n\n    let firstNonActive;\n\n    for (const interaction of scope.interactions) {\n      if (interaction.pointerType === pointerType) {\n        // if it's a down event, skip interactions with running simulations\n        if (interaction.simulation && !utils.contains(interaction.pointerIds, pointerId)) { continue; }\n\n        // if the interaction is active, return it immediately\n        if (interaction.interacting()) {\n          return interaction;\n        }\n        // otherwise save it and look for another active interaction\n        else if (!firstNonActive) {\n          firstNonActive = interaction;\n        }\n      }\n    }\n\n    // if no active mouse interaction was found use the first inactive mouse\n    // interaction\n    if (firstNonActive) {\n      return firstNonActive;\n    }\n\n    // find any mouse or pen interaction.\n    // ignore the interaction if the eventType is a *down, and a simulation\n    // is active\n    for (const interaction of scope.interactions) {\n      if (interaction.pointerType === pointerType && !(/down/i.test(eventType) && interaction.simulation)) {\n        return interaction;\n      }\n    }\n\n    return null;\n  },\n\n  // get interaction that has this pointer\n  hasPointer: function ({ pointerId }) {\n    for (const interaction of scope.interactions) {\n      if (utils.contains(interaction.pointerIds, pointerId)) {\n        return interaction;\n      }\n    }\n  },\n\n  // get first idle interaction with a matching pointerType\n  idle: function ({ pointerType }) {\n    for (const interaction of scope.interactions) {\n      // if there's already a pointer held down\n      if (interaction.pointerIds.length === 1) {\n        const target = interaction.target;\n        // don't add this pointer if there is a target interactable and it\n        // isn't gesturable\n        if (target && !target.options.gesture.enabled) {\n          continue;\n        }\n      }\n      // maximum of 2 pointers per interaction\n      else if (interaction.pointerIds.length >= 2) {\n        continue;\n      }\n\n      if (!interaction.interacting() && (pointerType === interaction.pointerType)) {\n        return interaction;\n      }\n    }\n\n    return null;\n  },\n};\n\nmodule.exports = finder;\n", "const win        = require('./window');\nconst isWindow   = require('./isWindow');\n\nconst is = {\n  array   : () => {},\n\n  window  : thing => thing === win.window || isWindow(thing),\n\n  docFrag : thing => is.object(thing) && thing.nodeType === 11,\n\n  object  : thing => !!thing && (typeof thing === 'object'),\n\n  function: thing => typeof thing === 'function',\n\n  number  : thing => typeof thing === 'number'  ,\n\n  bool    : thing => typeof thing === 'boolean' ,\n\n  string  : thing => typeof thing === 'string'  ,\n\n  element: thing => {\n    if (!thing || (typeof thing !== 'object')) { return false; }\n\n    const _window = win.getWindow(thing) || win.window;\n\n    return (/object|function/.test(typeof _window.Element)\n      ? thing instanceof _window.Element //DOM2\n      : thing.nodeType === 1 && typeof thing.nodeName === 'string');\n  },\n\n  plainObject: thing => is.object(thing) && thing.constructor.name === 'Object',\n};\n\nis.array = thing => (is.object(thing)\n  && (typeof thing.length !== 'undefined')\n  && is.function(thing.splice));\n\nmodule.exports = is;\n", "module.exports = (thing) => !!(thing && thing.Window) && (thing instanceof thing.Window);\n", "function pointerExtend (dest, source) {\n  for (const prop in source) {\n    const prefixedPropREs = module.exports.prefixedPropREs;\n    let deprecated = false;\n\n    // skip deprecated prefixed properties\n    for (const vendor in prefixedPropREs) {\n      if (prop.indexOf(vendor) === 0 && prefixedPropREs[vendor].test(prop)) {\n        deprecated = true;\n        break;\n      }\n    }\n\n    if (!deprecated && typeof source[prop] !== 'function') {\n      dest[prop] = source[prop];\n    }\n  }\n  return dest;\n}\n\npointerExtend.prefixedPropREs = {\n  webkit: /(Movement[XY]|Radius[XY]|RotationAngle|Force)$/,\n};\n\nmodule.exports = pointerExtend;\n", "const hypot         = require('./hypot');\nconst browser       = require('./browser');\nconst dom           = require('./domObjects');\nconst domUtils      = require('./domUtils');\nconst domObjects    = require('./domObjects');\nconst is            = require('./is');\nconst pointerExtend = require('./pointerExtend');\n\nconst pointerUtils = {\n  copyCoords: function (dest, src) {\n    dest.page = dest.page || {};\n    dest.page.x = src.page.x;\n    dest.page.y = src.page.y;\n\n    dest.client = dest.client || {};\n    dest.client.x = src.client.x;\n    dest.client.y = src.client.y;\n\n    dest.timeStamp = src.timeStamp;\n  },\n\n  setCoordDeltas: function (targetObj, prev, cur) {\n    targetObj.page.x    = cur.page.x    - prev.page.x;\n    targetObj.page.y    = cur.page.y    - prev.page.y;\n    targetObj.client.x  = cur.client.x  - prev.client.x;\n    targetObj.client.y  = cur.client.y  - prev.client.y;\n    targetObj.timeStamp = cur.timeStamp - prev.timeStamp;\n\n    // set pointer velocity\n    const dt = Math.max(targetObj.timeStamp / 1000, 0.001);\n\n    targetObj.page.speed   = hypot(targetObj.page.x, targetObj.page.y) / dt;\n    targetObj.page.vx      = targetObj.page.x / dt;\n    targetObj.page.vy      = targetObj.page.y / dt;\n\n    targetObj.client.speed = hypot(targetObj.client.x, targetObj.page.y) / dt;\n    targetObj.client.vx    = targetObj.client.x / dt;\n    targetObj.client.vy    = targetObj.client.y / dt;\n  },\n\n  isNativePointer: function  (pointer) {\n    return (pointer instanceof dom.Event || pointer instanceof dom.Touch);\n  },\n\n  // Get specified X/Y coords for mouse or event.touches[0]\n  getXY: function (type, pointer, xy) {\n    xy = xy || {};\n    type = type || 'page';\n\n    xy.x = pointer[type + 'X'];\n    xy.y = pointer[type + 'Y'];\n\n    return xy;\n  },\n\n  getPageXY: function (pointer, page) {\n    page = page || {};\n\n    // Opera Mobile handles the viewport and scrolling oddly\n    if (browser.isOperaMobile && pointerUtils.isNativePointer(pointer)) {\n      pointerUtils.getXY('screen', pointer, page);\n\n      page.x += window.scrollX;\n      page.y += window.scrollY;\n    }\n    else {\n      pointerUtils.getXY('page', pointer, page);\n    }\n\n    return page;\n  },\n\n  getClientXY: function (pointer, client) {\n    client = client || {};\n\n    if (browser.isOperaMobile && pointerUtils.isNativePointer(pointer)) {\n      // Opera Mobile handles the viewport and scrolling oddly\n      pointerUtils.getXY('screen', pointer, client);\n    }\n    else {\n      pointerUtils.getXY('client', pointer, client);\n    }\n\n    return client;\n  },\n\n  getPointerId: function (pointer) {\n    return is.number(pointer.pointerId)? pointer.pointerId : pointer.identifier;\n  },\n\n  setCoords: function (targetObj, pointers, timeStamp) {\n    const pointer = (pointers.length > 1\n                     ? pointerUtils.pointerAverage(pointers)\n                     : pointers[0]);\n\n    const tmpXY = {};\n\n    pointerUtils.getPageXY(pointer, tmpXY);\n    targetObj.page.x = tmpXY.x;\n    targetObj.page.y = tmpXY.y;\n\n    pointerUtils.getClientXY(pointer, tmpXY);\n    targetObj.client.x = tmpXY.x;\n    targetObj.client.y = tmpXY.y;\n\n    targetObj.timeStamp = is.number(timeStamp) ? timeStamp :new Date().getTime();\n  },\n\n  pointerExtend: pointerExtend,\n\n  getTouchPair: function (event) {\n    const touches = [];\n\n    // array of touches is supplied\n    if (is.array(event)) {\n      touches[0] = event[0];\n      touches[1] = event[1];\n    }\n    // an event\n    else {\n      if (event.type === 'touchend') {\n        if (event.touches.length === 1) {\n          touches[0] = event.touches[0];\n          touches[1] = event.changedTouches[0];\n        }\n        else if (event.touches.length === 0) {\n          touches[0] = event.changedTouches[0];\n          touches[1] = event.changedTouches[1];\n        }\n      }\n      else {\n        touches[0] = event.touches[0];\n        touches[1] = event.touches[1];\n      }\n    }\n\n    return touches;\n  },\n\n  pointerAverage: function (pointers) {\n    const average = {\n      pageX  : 0,\n      pageY  : 0,\n      clientX: 0,\n      clientY: 0,\n      screenX: 0,\n      screenY: 0,\n    };\n\n    for (const pointer of pointers) {\n      for (const prop in average) {\n        average[prop] += pointer[prop];\n      }\n    }\n    for (const prop in average) {\n      average[prop] /= pointers.length;\n    }\n\n    return average;\n  },\n\n  touchBBox: function (event) {\n    if (!event.length && !(event.touches && event.touches.length > 1)) {\n      return;\n    }\n\n    const touches = pointerUtils.getTouchPair(event);\n    const minX = Math.min(touches[0].pageX, touches[1].pageX);\n    const minY = Math.min(touches[0].pageY, touches[1].pageY);\n    const maxX = Math.max(touches[0].pageX, touches[1].pageX);\n    const maxY = Math.max(touches[0].pageY, touches[1].pageY);\n\n    return {\n      x: minX,\n      y: minY,\n      left: minX,\n      top: minY,\n      width: maxX - minX,\n      height: maxY - minY,\n    };\n  },\n\n  touchDistance: function (event, deltaSource) {\n    const sourceX = deltaSource + 'X';\n    const sourceY = deltaSource + 'Y';\n    const touches = pointerUtils.getTouchPair(event);\n\n\n    const dx = touches[0][sourceX] - touches[1][sourceX];\n    const dy = touches[0][sourceY] - touches[1][sourceY];\n\n    return hypot(dx, dy);\n  },\n\n  touchAngle: function (event, prevAngle, deltaSource) {\n    const sourceX = deltaSource + 'X';\n    const sourceY = deltaSource + 'Y';\n    const touches = pointerUtils.getTouchPair(event);\n    const dx = touches[1][sourceX] - touches[0][sourceX];\n    const dy = touches[1][sourceY] - touches[0][sourceY];\n    const angle = 180 * Math.atan2(dy , dx) / Math.PI;\n\n    return  angle;\n  },\n\n  getPointerType: function (pointer) {\n    return is.string(pointer.pointerType)\n      ? pointer.pointerType\n      : is.number(pointer.pointerType)\n        ? [undefined, undefined,'touch', 'pen', 'mouse'][pointer.pointerType]\n          // if the PointerEvent API isn't available, then the \"pointer\" must\n          // be either a MouseEvent, TouchEvent, or Touch object\n          : /touch/.test(pointer.type) || pointer instanceof domObjects.Touch\n            ? 'touch'\n            : 'mouse';\n  },\n\n  // [ event.target, event.currentTarget ]\n  getEventTargets: function (event) {\n    const path = is.function(event.composedPath) ? event.composedPath() : event.path;\n\n    return [\n      domUtils.getActualElement(path ? path[0] : event.target),\n      domUtils.getActualElement(event.currentTarget),\n    ];\n  },\n};\n\nmodule.exports = pointerUtils;\n", "const { window } = require('./window');\n\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\nlet lastTime = 0;\nlet request;\nlet cancel;\n\nfor (let x = 0; x < vendors.length && !window.requestAnimationFrame; x++) {\n  request = window[vendors[x] + 'RequestAnimationFrame'];\n  cancel = window[vendors[x] +'CancelAnimationFrame'] || window[vendors[x] + 'CancelRequestAnimationFrame'];\n}\n\nif (!request) {\n  request = function (callback) {\n    const currTime = new Date().getTime();\n    const timeToCall = Math.max(0, 16 - (currTime - lastTime));\n    const id = setTimeout(function () { callback(currTime + timeToCall); },\n                          timeToCall);\n\n    lastTime = currTime + timeToCall;\n    return id;\n  };\n}\n\nif (!cancel) {\n  cancel = function (id) {\n    clearTimeout(id);\n  };\n}\n\nmodule.exports = {\n  request,\n  cancel,\n};\n", "const extend = require('./extend');\nconst is = require('./is');\nconst {\n  closest,\n  parentNode,\n  getElementRect,\n} = require('./domUtils');\n\nconst rectUtils = {\n  getStringOptionResult: function (value, interactable, element) {\n    if (!is.string(value)) {\n      return null;\n    }\n\n    if (value === 'parent') {\n      value = parentNode(element);\n    }\n    else if (value === 'self') {\n      value = interactable.getRect(element);\n    }\n    else {\n      value = closest(element, value);\n    }\n\n    return value;\n  },\n\n  resolveRectLike: function (value, interactable, element, functionArgs) {\n    value = rectUtils.getStringOptionResult(value, interactable, element) || value;\n\n    if (is.function(value)) {\n      value = value.apply(null, functionArgs);\n    }\n\n    if (is.element(value)) {\n      value = getElementRect(value);\n    }\n\n    return value;\n  },\n\n  rectToXY: function (rect) {\n    return  rect && {\n      x: 'x' in rect ? rect.x : rect.left,\n      y: 'y' in rect ? rect.y : rect.top,\n    };\n  },\n\n  xywhToTlbr: function (rect) {\n    if (rect && !('left' in rect && 'top' in rect)) {\n      rect = extend({}, rect);\n\n      rect.left   = rect.x || 0;\n      rect.top    = rect.y || 0;\n      rect.right  = rect.right   || (rect.left + rect.width);\n      rect.bottom = rect.bottom  || (rect.top + rect.height);\n    }\n\n    return rect;\n  },\n\n  tlbrToXywh: function (rect) {\n    if (rect && !('x' in rect && 'y' in rect)) {\n      rect = extend({}, rect);\n\n      rect.x      = rect.left || 0;\n      rect.top    = rect.top  || 0;\n      rect.width  = rect.width  || (rect.right  - rect.x);\n      rect.height = rect.height || (rect.bottom - rect.y);\n    }\n\n    return rect;\n  },\n};\n\nmodule.exports = rectUtils;\n", "const win = module.exports;\nconst isWindow = require('./isWindow');\n\nfunction init (window) {\n  // get wrapped window if using Shadow DOM polyfill\n\n  win.realWindow = window;\n\n  // create a TextNode\n  const el = window.document.createTextNode('');\n\n  // check if it's wrapped by a polyfill\n  if (el.ownerDocument !== window.document\n      && typeof window.wrap === 'function'\n    && window.wrap(el) === el) {\n    // use wrapped window\n    window = window.wrap(window);\n  }\n\n  win.window = window;\n}\n\nif (typeof window === 'undefined') {\n  win.window     = undefined;\n  win.realWindow = undefined;\n}\nelse {\n  init(window);\n}\n\nwin.getWindow = function getWindow (node) {\n  if (isWindow(node)) {\n    return node;\n  }\n\n  const rootNode = (node.ownerDocument || node);\n\n  return rootNode.defaultView || rootNode.parentWindow || win.window;\n};\n\nwin.init = init;\n"]}
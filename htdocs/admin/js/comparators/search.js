var countConditions = 4;

$(document).ready(function(){
	$('#riadatepicker .selector a').click(function(){
		if( $(this).attr('name') != 'perso' )
			updateSearchWidthSelector( true );
	});
	$('#riadatepicker #btn_submit').click(function(){
		updateSearchWidthSelector( true );
	});
	
	$('#selectctr .selectorview').click(function(){
		if($('#selectctr .selector').css('display')=='none'){
			$('#selectctr .selector').show();
		}else{
			$('#selectctr .selector').hide();
		}
	});
	$('#selectctr .selector a').click(function(){
		hidePopup();
		$('#selectctr .selectorview .left .view').html($(this).html());
		$('#selectctr .selector').hide();
		
		var ctrID = $(this).attr('name').substring(4);
		if( $('#ctr-id').length ) $('#ctr-id').val( ctrID );
		
		if( !$.browser.msie ){
			history.pushState({ path: this.path, ajax: true }, '', '/admin/comparators/search/index.php?ctr=' + ctrID);
		}
		
		$('#ctrid').val( ctrID );
		updateSearchWidthSelector( false );
	});
	
	// en cas de clic sur des champs de type textarea
	$('a[name=show_textarea]').live('click',function(){
		const id = $(this).attr('id').replace('code_','');
		displayPopup("Modifier", $('#textarea_'+id+'.textarea_popup_content').html(), '', false,950,450);
	});
	$('input[name=update_textarea]').live('click',function(){
		const val = $('#popup_ria textarea').val();
		const tmp = $('#popup_ria textarea').attr('id').split('-');
		const code = tmp[0];
		const count = tmp[1];
		$('#popup_ria content').html();
		$('#textarea_'+code+'-'+count+' textarea').html( val );
		hidePopup();
		
		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-search.php',
			data: 'marketplace=' + $('#marketplace').val() + '&load-cdt=1&refresh=1&cdt=' + code + '&count=' + count + '&' + $('#formctrsearch').serialize(),
			dataType: 'json',
			success: function( json ){
				if( json.type ){
					var el = $('#textarea_'+code+'-'+count+' textarea').parents('.elem');
					el.before( json.data );
					el.remove();
					searchProducts(1);
				}
			}
		});
		
	});
	
	// Export des données
	$('input[name=export]').live('click', function(){
		var reg=new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString().split(reg);
		
		var marketplace = ($('#marketplace').val() == 1);
		var date1 = dateparse(date[0]);
		var date2 = dateparse(date[1]);
		
		var sort = $('#sort').val();
		var dir = $('#dir').val();

		var data = '?marketplace=' + marketplace + '&searchprd=1&' + $('#formctrsearch').serialize() + '&date1=' + date1 + '&date2=' + date2 + '&sort=' + sort + '&dir=' + dir;
		var url = '/admin/comparators/search/download.php' + data;

		$('body').append('<div class="popup_ria_back_load"></div>');
		$('body').append('<div class="popup_ria_back_notice notice">' + catalogExportEnCours + '</div>');

		$.ajax({
			type 	: 'get',
			url 	: url,
			data 	: '',
			async 	: true,
			success : function(){
				$('.popup_ria_back_notice').html(comparatorSearchExportPret + ' : '+ '<a href="#" class="download-file">' + comparatorSearchTelecharger + '</a>');
			}
		});
		return false;
	});
	loadFilter();
}).delegate(
	'#search-checked', 'click', function(){
		if( !$('.check-all-prd:checked').length ){
			return false;
		}

		var ctr = parseInt( $('#ctr-id').val() );
		if( ctr<=0 ){
			var cmpMessage = $('#marketplace').val()==1 ? comparatorSearchConfirmActivationProduitMarche : comparatorSearchConfirmActivationProduitComparateur;
			if( !window.confirm(cmpMessage) ){
				return false;
			}
		}

		var data = '&ctr=' + $('#ctr-id').val();
		$('.check-all-prd:checked').each(function(){
			data += '&prd[]=' + $(this).val();
		});
		
		if( $('#marketplace').length )
			data += '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);
	
		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-actions.php',
			data: 'activated=1' + data,
			dataType: 'json',
			success: function(res){
				if( res.type=='0' ){
					$('#msg-' + $('#ctr-id').val()).html( '<div class="error">' + res.message + '</div>' );
				} else {
					searchProducts(1);
				}
			}
		});

		return false;
	}
).delegate(
	'#search-unchecked', 'click', function(){
		if( !$('.check-all-prd:checked').length ){
			return false;
		}

		var ctr = parseInt( $('#ctr-id').val() );
		if( ctr<=0 ){
			var cmpMessage = $('#marketplace').val()==1 ? comparatorSearchConfirmDesactivationProduitMarche : comparatorSearchConfirmDesactivationProduitComparateur;
			if( !window.confirm(cmpMessage) ){
				return false;
			}
		}
		
		var data = '&ctr=' + $('#ctr-id').val();
		$('.check-all-prd:checked').each(function(){
			data += '&prd[]=' + $(this).val();
		});
		
		if( $('#marketplace').length )
			data += '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);
		
		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-actions.php',
			data: 'unactivated=1' + data,
			dataType: 'json',
			success: function(res){
				if( res.type=='0' ){
					$('#msg-' + $('#ctr-id').val()).html( '<div class="error">' + res.message + '</div>' );
				} else {
					searchProducts(1);
				}
			}
		});

		return false;
	}
).delegate(
	'#riawebsitepicker.ctr-selector-search .selectorview', 'click', function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	}
).delegate(
	'#riawebsitepicker.ctr-selector-search .selector a', 'click', function(){
		hidePopup();
		
		var ctrID = $('#ctr-id').val();
		var wst = $(this).attr('name').replace('w-', '');
		
		if( !$.browser.msie ){
			history.pushState({ path: this.path, ajax: true }, '', '/admin/comparators/search/index.php?ctr=' + ctrID);
		}
		
		$('#wst-id').val( wst );
		updateSearchWidthSelector( false );
	}
).delegate(
	'.download-file', 'click', function(){
		var url = window.location.href;
		url = url + (url.match(/\?/) ? '&' : '?') + 'downloadexport=1';
		$('.popup_ria_back_load').hide();
		$('.popup_ria_back_notice').hide();
		window.location.href = url;
		return false;
	}
);

/** 
 * Cette fonction permet de mettre à jour les paramètres de trie pour l'export
 */
function sort(id){
	$('#sort').val(id);	
	
	//Atendre la mise a jour de la classe 
		setTimeout(function() {
        if($('#'+id).attr('class').includes('headerSortDown')){
			$('#dir').val('desc');
		}else{
			$('#dir').val('asc');
		}
    }, 500);
	
	return false;
}


function updateSearchWidthSelector( date ){
	delMessage();
	
	if( date ){
		// récupère la date
		var reg=new RegExp("[,]+", "g");
		var date = getRiaDate().toString().split(reg);
		var date1 = dateparse(date[0]);
		var date2 = dateparse(date[1]);
		
		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-search.php',
			data: 'marketplace=' + $('#marketplace').val() + '&upd-date=1&date1=' + date1 + '&date2=' + date2,
			dataType: 'json'
		});
	}
	
	if( $('#ctr-sh-results table').length ){
		searchProducts( 1 );
	}
	return false;
}
/**
 *	Cette fonctions supprime tout type de message (error, succes)
 */
function delMessage(){
	$('.error').remove();
	$('.success').remove();
}

/**
 *	Cette fonction permet de retirer une condition
 */
function delConditions( inPut ){
	delMessage();
	inPut.parents('.elem').remove();
	updateListConditions();
	countConditions--;
	return false;
}

/**
 *	Cette fonction permet de mettre à jour la liste déroulante des conditions possibles
 */
function updateListConditions(){
	delMessage();
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-search.php',
		data: 'marketplace=' + $('#marketplace').val() + '&upd-cdts=1&' + $('#formctrsearch').serialize(),
		dataType: 'json',
		success: function( json ){
			if( json.type ){
				var html = '';
				var cdt = false;
				
				html += '	<option value="-1">&nbsp;</option>';
				for( var i=0 ; i<json.data.length ; i++ ){
					cdt = json.data[ i ];
					html += '<option value="' + cdt.code + '" title="' + htmlspecialchars(cdt.desc) + '">' + htmlspecialchars(cdt.name) + '</option>';
				}
				
				$('#conditions').html( html );
				ini_autocomplete();
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet de charger une ancienne recherche
 */
function loadFilter(){
	delMessage();
	
	var flt = $('#filters').val();
	if( flt=='-1' || flt=='' )
		return false;
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-search.php',
		data: 'marketplace=' + $('#marketplace').val() + '&load-filter=1&filter=' + $('#filters').val(),
		dataType: 'json',
		success: function( json ){
			$('#zone-conditions .elem').remove();
			if( json.type ){
				$('#zone-conditions').prepend( json.data );
				searchProducts();
			} else {
				$('#zone-conditions').prepend( '<div class="error">' + json.message + '</div>' );
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet de recharger la liste des recherches enregistrées.
 */
function reloadListFilter(){
	delMessage();
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-search.php',
		data: 'marketplace=' + $('#marketplace').val() + '&reload-filter=1',
		dataType: 'json',
		success: function( json ){
			var options = '<option value="-1">&nbsp;</option>';
			if( json.type ){
				
				var flt = false;
				for( var i=0 ; i<json.data.length ; i++ ){
					flt = json.data[i];
					options += '<option title="' + htmlspecialchars(flt.desc) + '" value="' + flt.id + '">' + htmlspecialchars(flt.name) + '</option>';
				}
				
				$('#filters').html( options );
				
			}
		}
	});
}

function loadNewConditions(){
	delMessage();
	
	if( $('#cdt-select .options').length ){
		$('#cdt-select .options').remove();
	}
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-search.php',
		data: 'marketplace=' + $('#marketplace').val() + '&load-new=1&count=' + countConditions + '&' + $('#formctrsearch').serialize(),
		dataType: 'json',
		success: function( json ){
			if( json.type ){
				$('#filter #cdt-select').append( json.data );
				ini_autocomplete();
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet de charger le formulaire pour une nouvelle condition
 */
function selectedConditions(){
	delMessage();
	
	var condition = $('#conditions').val();
	if( condition!='-1' ){
		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-search.php',
			data: 'marketplace=' + $('#marketplace').val() + '&load-cdt=1&cdt=' + condition + '&count=' + countConditions + '&' + $('#formctrsearch').serialize(),
			dataType: 'json',
			success: function( json ){
				if( json.type ){
					$('#new-cdt').before( json.data );
					updateListConditions();
					hideAddConditions();
					countConditions++;
				}
			}
		});
	} else {
		$('#filter').prepend( '<div class="error">' + comparatorSearchSelectCondition + '</div>' );
	}
	return false;
}

/**
 *	Cette fonction permet de sauvegarder une recherche.
 */
function saveCtrSearch( save ){
	delMessage();
	if( !save ){
		var url = '/admin/comparators/search/popup-save-search.php';
		url += '?' + $('#formctrsearch').serialize() + '&marketplace=' + $('#marketplace').val();
		
		displayPopup( comparatorSearchDisplayPopupSaveRecherche, '', url, false, 335 );
		return false;
	} else {
		hidePopup();
		reloadListFilter();
	}
	return false;
}

/** 
 *	Cette fonction permet d'afficher la zone pour ajouter une condition.
 */
function showAddConditions(){
	delMessage();
	$('#infosearch').hide();
	$('#cdt-select .options').remove();
	updateListConditions();
	$('#filter').show();
	$('#new-cdt').hide();
	return false;
}

/**
 *	Cette fonction permet de masquer la zone pour ajouter une condition.
 */
function hideAddConditions(){
	delMessage();
	$('#infosearch').hide();
	$('#cdt-select .options').remove();
	updateListConditions();
	$('#filter').hide();
	$('#new-cdt').show();
	return false;
}

/**
 *	Cette fonction permet de lancer une recherche
 */
function searchProducts( page ){
	delMessage();

	//reset les inputs en hidden servant pour le tri
	if($('#sort').length && $('#dir').length ){
		$('#sort').val('');
		$('#dir').val('');
	}
	
	// récupère la période en cours d'application
	var reg=new RegExp("[,]+", "g");
	var date = getRiaDate();
	date = date.toString().split(reg);
	
	var marketplace = ($('#marketplace').val() == 1);

	var date1 = dateparse(date[0]);
	var date2 = dateparse(date[1]);
	
	var url = '#';
	var colspan = marketplace ? 5 : 9;
	var table = '';
	
	var tchecked = compartorSearchActiveExport + ( !marketplace ? compartorSearchComparateur :compartorSearchPlaceMarche ) + '.';
	var tunchecked = compartorSearchDesactiveExport + ( !marketplace ? compartorSearchComparateur : compartorSearchPlaceMarche ) + '.';
	
	table += '<table class="tablesorter checklist" id="tb-ctr-prd">';
	table += '	<caption>' + comparatorSearchProduits + '</caption>';
	table += '	<thead>';
	table += '		<tr>';
	table += '			<th id="prd-check" align="center"><input type="checkbox" name="check-all" id="check-all" value="" /></th>';
	table += '			<th onclick="return sort(this.id);" id="prd">' + compartorSearchDesigantion + '</th>';
	
	if (! marketplace) 
		table += '		<th id="prd-click" onclick="return sort(this.id);"><a href="' + url + '">' + compartorSearchClics + '</a></th><th id="prd-cost" onclick="return sort(this.id);" ><a href="' + url + '">' + compartorSearchCout + '</a></th>';
	
	table += '			<th id="prd-sales" onclick="return sort(this.id);"><a  href="' + url + '">' + compartorSearchVentes + '</a></th>';
	
	if (! marketplace){
		table += '		<th title="' + compartorSearchTauxTransformation + '" id="prd-transfo" onclick="return sort(this.id);">';
		table += '			<a href="' + url + '">' + conparatorSearchtransfo + '</a></th><th id="prd-cost-sales" class="col105px" onclick="return sort(this.id);"><a href="' + url + '">' + comparatorSearchCoutVente + '</a>';
		table += '		</th>';
	}
	
	table += '			<th title="' + comparatorSearchCAHT + '" id="prd-ca" onclick="return sort(this.id);"><a href="' + url + '">' + comparatorSearchCAHTSmall + '</a></th>';
	table += '			<th title="' + comparatorSearchCATTC + '" id="prd-ca-ttc" onclick="return sort(this.id);"><a href="' + url + '">' + comparatorSearchCATTCSmall + '</a></th>';
	table += '			<th title="' + comparatorSearchMarge + '" id="prd-margin" onclick="return sort(this.id);"><a href="' + url + '" >' + comparatorSearchMarge + '</a></th>';
	table += '			<th title="' + comparatorSearchROI + '" id="prd-roi" onclick="return sort(this.id);"><a href="' + url + '">' + comparatorSearchROISmall + '</a></th>';
	table += '			<th id="prd-export" onclick="return sort(this.id);"><a href="' + url + '" >' + comparatorSearchecporte + '</a></th>';
	table += '		</tr>';
	table += '	</thead>';
	table += '	<tbody><td align="left" headers="prd-check" colspan="' + ( 3+colspan ) + '" style="padding: 5px;"><img src="/admin/images/loader2.gif" />' + comparatorSearchChargementRecherche + '</td></tbody>';
	table += '	<tfoot>';
	table += '		<tr>';
	table += '			<td style="text-align: left;" colspan="3">';
	table += '				<input type="submit" name="checked" id="search-checked" value="' + comparatorSearchExporter + '" title="' + tchecked + '" />';
	table += '				<input type="submit" name="unchecked" id="search-unchecked" value="' + comparatorSearchNePasExporter + '" title="' + tunchecked + '" />';
	table += '			</td>';
	table += '			<td colspan="' + colspan + '" id="pagination">';
	table += '				<input type="submit" name="export" value="' + comparatorSearchTelecharger + '" title="' + comparatorSearchTelechargerCSV + '" />';
	table += '			</td>';
	table += '		</tr>';
	table += '	</tfoot>';
	table += '</table>';
	
	$('#ctr-sh-results').html( table );
	
	page = page!=undefined ? page : 1;
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-search.php',
		data: 'marketplace=' + marketplace + '&searchprd=1&' + $('#formctrsearch').serialize() + '&page=' + page + '&date1=' + date1 + '&date2=' + date2,
		dataType: 'json',
		success: function( json ){
			if( json.type ){
				var ctrID = $('#ctrid').val();
				if( $('#ctr-id').length ) $('#ctr-id').val( ctrID );
				
				$('#ctr-sh-results table tbody').html( '' );
				
				// affiche la liste des produits
				var p = false;
				if( json.data.length ){
					for( var i=0 ; i<json.data.length ; i++ ){
						p = json.data[i];
						
						var tbody = '	<tr class="right">';
						tbody += '		<td headers="prd-check" align="center">';
						tbody += '			<input class="check-all-prd" type="checkbox" name="check-all[]" id="check-all-' + p.id + '" value="' + p.id + '" />';
						tbody += '		</td>';
						tbody += '		<td align="left" headers="prd">';
						tbody += '			' + viewPrdIsSync(p.is_sync);
						tbody += '			<a href="/admin/catalog/product.php?cat=0&amp;prd=' + p.id + '" target="_blank">' + htmlspecialchars(p.title) + '</a>';
						tbody += '		</td>';
						
						if (! marketplace){
							tbody += '	<td headers="prd-click">' + p.click + '</td>';
							tbody += '	<td headers="prd-cost">' + p.cost + ' €</td>';
						}
						
						tbody += '		<td headers="prd-sales">' + p.sales + '</td>';
						
						if (! marketplace) {
							tbody += '	<td headers="prd-transfo">' + p.transfo + ' %</td>';
							tbody += '	<td headers="prd-cost-sales">' + p.costsales + ' €</td>';
						}
						
						tbody += '		<td headers="prd-ca">' + p.ca + ' €</td>';
						tbody += '		<td headers="prd-ca-ttc">' + p.ca_ttc + ' €</td>';
						tbody += '		<td headers="prd-margin">' + p.margin + ' €</td>';
						tbody += '		<td headers="prd-roi">' + magnify(p.roi, 'roi') + '</td>';
						tbody += '		<td headers="prd-export">';
						
						var actionOnclick = '';
						if( ctrID==0 ){
							actionOnclick = 'return showPopupExport( ' + p.id + ', ' + marketplace + ' );';
						}else if( p.export ){
							actionOnclick = 'return unactivatedProduct( $(this), '+ctrID+', true );';
						}else{
							actionOnclick = 'return activatedProduct( $(this), '+ctrID+', true );';
						}
						
						if( p.export == 'mix' ){
							tbody += '	 <a onclick="' + actionOnclick + '" name="prd-'+p.id+'" href="#" class="active">' + comparatorSearchPartiel + '</a>';
						}else if( p.export ){
							tbody += '	 <a onclick="' + actionOnclick + '" name="prd-'+p.id+'" href="#" class="active">' + comparatorSearchOui + '</a>';
						}else{
							tbody += '	 <a onclick="' + actionOnclick + '" name="prd-'+p.id+'" href="#" class="cancel">' + comparatorSearchNon + '</a>';
						}
						
						tbody += 		'</td>';
						tbody += '	</tr>';
						
						$('#ctr-sh-results table tbody').append( tbody );
					}
					
					$('#checkall').show();
					$('#checkall').click(function(){
						if( $(this).is(':checked') )
							$('#ctr-sh-results table tbody .checkprd').attr('checked', 'checked');
						else
							$('#ctr-sh-results table tbody .checkprd').removeAttr('checked');
					});
					
					if( ctrID>0 ){
						$('#ctr-sh-results table tbody tr td a').click(function(){
							// var url = '/admin/comparators/stats/js_product.php';
							// url += '?ctr=' + ctrID + '&prd=' + $(this).attr('name').substring(4) + '&visit=0&marketplace=' + $('#marketplace').val();
							// displayPopup( 'Choisissez une famille du comparateur', '', url, false,950,450);
							// return false;
						});
					}
					
					$('#ctr-sh-results table caption').append( ' (' + json['nb_result'] + ' produit' + ( json['nb_result']>1 ? 's' : '' ) + ')' );
					if( !marketplace ){
						$('#ctr-sh-results table').tablesorter({ 
							headers: {
								0: { sorter: false },
								2:{ sorter: "riaInteger" },
								3:{ sorter: "riaInteger" },
								4:{ sorter: "riaInteger" },
								5:{ sorter: "riaInteger" },
								6:{ sorter: "riaInteger" },
								7:{ sorter: "riaInteger" },
								8:{ sorter: "riaInteger" },
								9:{ sorter: "riaInteger" },
								10:{ sorter: "riaInteger" }
							},
							sortInitialOrder: "desc"
						});
					}else{
						$('#ctr-sh-results table').tablesorter({ 
							headers: {
								0: { sorter: false },
								2:{ sorter: "riaInteger" },
								3:{ sorter: "riaInteger" },
								4:{ sorter: "riaInteger" },
								5:{ sorter: "riaInteger" },
								6:{ sorter: "riaInteger" }
							},
							sortInitialOrder: "desc"
						});
					}
				} else {
					$('#ctr-sh-results table tbody').html( '<tr><td colspan="' + (3 + colspan) + '" style="padding: 5px;">' + comparatorSearchRechercheVide + '</td></tr>' );
				}
			} else {
				$('#zone-conditions').prepend( '<div class="error">' + json.message + '</div>' );
				$('#ctr-sh-results table tbody').html( '<tr><td colspan="' + (3 + colspan) + '" style="padding: 5px;">' + comparatorSearchRechercheVide + '</td></tr>' );
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet d'afficher une popup permettant de gérer l'export vers plusieurs places de marché / comparateurs de prix en même temps.
 */
function showPopupExport( prdID, market ){
	displayPopup( comparatorSearchDisplayPopupGestionExport, '', '/admin/comparators/search/popup-multiexport.php?prd=' + prdID + '&marketplace=' + ( market ? 1 : 0), '', 400, 400 );
	return false;
}

/**
*	Initialisation de l'autocomplétion pour les catégories 
*/
var cat_focus = false;
function ini_autocomplete(){

	$('.search-cats').focus(function(){
		cat_focus = this;
		var url = '/admin/catalog/popup-categories.php';
		if( $(this).prev().val() ){
			url += '?cat=' + $(this).prev().val();
		}
		displayPopup("Choisir une catégorie", '', url, false,950,450);
	});
	
}
function updateCat( id, idParent, catname){
	$(cat_focus).prev().val(id);	
	$(cat_focus).val(catname);
	hidePopup();
}
function close_cat_popup(){
	hidePopup();
}
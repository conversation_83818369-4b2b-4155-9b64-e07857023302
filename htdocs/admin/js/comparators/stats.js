var currentAjaxRequest = false;
var first = true;
$(document).ready(function(){
	loadSorter();

	$('#riadatepicker .selector a').click(function(){
		if( $(this).attr('name') != 'perso' )
			updateStatsCtr();
	});
	$('#riadatepicker #btn_submit').click(function(){
		updateStatsCtr();
	});

	reloadActionProducts();
	pushHistory( location.href, location.href.replace('admin/comparators/stats/index.php','/admin/ajax/comparators/json-stats.php'));

	if( !$.browser.msie ){
		$(window).bind('popstate', function(event) {
			if( currentAjaxRequest ) currentAjaxRequest.abort();

			if( event.originalEvent.state && event.originalEvent.state.ajax )
				reloadList( event.originalEvent.state.jsonurl );
			else if( !first ){
				$('#tb-ctr-prd tbody').html( '<tr><td colspan="10" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="' + msgLoading + '" width="16" height="16" /></td></tr>' );
				window.location = event.originalEvent.state.jsonurl;
			}
		});
	}
}).delegate(
	'#riawebsitepicker .selectorview', 'click', function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	}
).delegate(
	'#check-all', 'click', function(){
		if( $(this).is(':checked') ){
			$('.check-all-prd').attr('checked', 'checked');
		} else {
			$('.check-all-prd').removeAttr('checked', 'checked');
		}
	}
).delegate(
	'#checked', 'click', function(){
		if( !$('.check-all-prd:checked').length ){
			return false;
		}

		var cmpMessage = $('#marketplace').val()==1 ? comparatorStatsConfirmActiveProduitMarche : comparatorStatsConfirmActiveProduitComprateurt;
		if( !window.confirm(cmpMessage) ){
			return false;
		}

		var prds = new Array();
		var url = '&ctr=' + $('#ctr-id').val();
		$('.check-all-prd:checked').each(function(){
			url += '&prd[]=' + $(this).val();
			prds.push( $(this).val() );
		});

		if( $('#marketplace').length )
			url += '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);

		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-actions.php',
			data: 'activated=1' + url,
			dataType: 'json',
			success: function(res){
				if( res.type=='0' ){
					$('#msg-' + $('#ctr-id').val()).html( '<div class="error">' + res.message + '</div>' );
				} else {
					var p = false
					for( p in prds ){
						$('#check-all, #check-all-' + prds[p]).removeAttr('checked');
						var tdInput = $('#check-all-' + prds[p]).parents('tr').find('td[headers=prd-export]');
						tdInput.html( '<a onclick="return unactivatedProduct( $(this),' + $('#ctr-id').val() +', true );" href="#" name="prd-'+  prds[p] +'" class="active">Oui</a>' );
					}
				}
			}
		});

		return false;
	}
).delegate(
	'#unchecked', 'click', function(){
		if( !$('.check-all-prd:checked').length ){
			return false;
		}

		var cmpMessage = $('#marketplace').val()==1 ? comparatorStatsConfirmDesactiveProduitMarche : comparatorStatsConfirmDesactiveProduitCompareteur;
		if( !window.confirm(cmpMessage ) ){
			return false;
		}

		var prds = new Array();
		var url = '&ctr=' + $('#ctr-id').val();
		$('.check-all-prd:checked').each(function(){
			url += '&prd[]=' + $(this).val();
			prds.push( $(this).val() );
		});

		if( $('#marketplace').length )
			url += '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);

		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-actions.php',
			data: 'unactivated=1' + url,
			dataType: 'json',
			success: function(res){
				if( res.type=='0' ){
					$('#msg-' + $('#ctr-id').val()).html( '<div class="error">' + res.message + '</div>' );
				} else {
					var p = false
					for( p in prds ){
						$('#check-all, #check-all-' + prds[p]).removeAttr('checked');
						var tdInput = $('#check-all-' + prds[p]).parents('tr').find('td[headers=prd-export]');
						tdInput.html( '<a onclick="return activatedProduct( $(this),' + $('#ctr-id').val() +', true );" href="#" name="prd-'+ prds[p] +'" class="cancel">' + comparatorStatsNon + '</a>' );
					}
				}
			}
		});

		return false;
	}
);

function loadSorter(){
	if( !parseInt($('#marketplace').val()) ){
		$("#tb-ctr-cat").trigger("destroy").tablesorter({
			sortInitialOrder: "desc",
			headers: {
				1: { sorter: "riaInteger" },
				2: { sorter: "riaInteger" },
				3: { sorter: "riaInteger" },
				4: { sorter: "riaInteger" },
				5: { sorter: "riaInteger" },
				6: { sorter: "riaInteger" },
				7: { sorter: "riaInteger" },
				8: { sorter: "riaInteger" },
				9: { sorter: "riaInteger" }
			}
		});

		if( $('#tb-ctr-prd').length ){
			$('#tb-ctr-prd').trigger("destroy").tablesorter({
				headers: {
					0: { sorter: false },
					2: { sorter: "riaInteger" },
					3: { sorter: "riaInteger" },
					4: { sorter: "riaInteger" },
					5: { sorter: "riaInteger" },
					6: { sorter: "riaInteger" },
					7: { sorter: "riaInteger" },
					8: { sorter: "riaInteger" },
					9: { sorter: "riaInteger" },
					10: { sorter: "riaInteger" }
				},
				sortInitialOrder: "desc"
			});
		}
	}else{
		$("#tb-ctr-cat").trigger("destroy").tablesorter({
			sortInitialOrder: "desc",
			headers: {
				1: { sorter: "riaInteger" },
				2: { sorter: "riaInteger" },
				3: { sorter: "riaInteger" },
				4: { sorter: "riaInteger" },
				5: { sorter: "riaInteger" }
			}
		});

		if( $('#tb-ctr-prd').length ){
			$('#tb-ctr-prd').trigger("destroy").tablesorter({
				headers: {
					0: { sorter: false },
					2: { sorter: "riaInteger" },
					3: { sorter: "riaInteger" },
					4: { sorter: "riaInteger" },
					5: { sorter: "riaInteger" },
					6: { sorter: "riaInteger" }
				},
				sortInitialOrder: "desc"
			});
		}
	}
	return false;
}
function reloadActionProducts(){
	$('#tb-ctr-prd td[headers=prd] a').click(function(){
		// information sur le produit
		var href = $(this).attr('href');
		var prdID = href.substring( href.indexOf('prd=') + 4 );
		var prdTitle = $(this).html();
		var ctrName = $('#ctr-name').val();
		var catID = $('#cat-visit').val();

		// information sur le comparateur
		var ctrID = $('#ctr-id').val();

		// ouverture de la popup
		displayPopup( prdTitle + ' / ' + ctrName, '', '/admin/comparators/stats/js_product.php?prd=' + prdID + '&ctr=' + ctrID + '&visit=' + catID + '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0) );

		return false;
	});
}
function pushHistory( url, jsonurl ){
	if( !$.browser.msie ){
		history.pushState({ path: this.path, jsonurl: jsonurl, ajax: true }, '', url);
	}
}

/**	Cette fonction est utilisée dans le fichier comparators/search.js pour accentuer
 * 	la mise en forme des nombres de type "roi".
 *	@param {string} number Nombre à magnifier, sous forme textuelle
 *	@param {string} type Type d'indicateur numérique
 *	@return le nombre, éventuellement complété avec du code HTML renforçant sa visibilité pour l'utilisateur
 */
function magnify(number, type) {
	const numberTraitement = number.replace(',','.').replace(' ', '');
	var style =  '';
	if(type=='roi') {
		if(numberTraitement > 0) {
			style = 'color:#006000;font-weight:bold;';
		}
		return '<span style="'+style+'">'+number+' %</span>';
	}
	return number;
}
function updateStatsCtr( categorie ){
	var ctrID = $('#ctr-id').val();
	var catID = $('#cat-visit').val();

	if( categorie!=undefined ){
		catID = categorie;
		$('#cat-visit').val( categorie );
	}

	// récupère la date
	var reg=new RegExp("[,]+", "g");
	var date = getRiaDate().toString().split(reg);
	var date1 = dateparse(date[0]);
	var date2 = dateparse(date[1]);

	// mise à jour des stats
	var url = '/admin/comparators/stats/index.php?ctr=' + ctrID + '&parent=' + catID + '&date1=' + date1 + '&date2=' + date2 + '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);
	var jsonurl = '/admin/ajax/comparators/json-stats.php?ctr=' + ctrID + '&parent=' + catID + '&date1=' + date1 + '&date2=' + date2 + '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);
	pushHistory( url, jsonurl);
	reloadList( jsonurl );

	return false;
}
function reloadList( urlAlias ) {
	first = false;

	var catID = $('#cat-visit').val();
	var ctrID = $('#ctr-id').val();
	var marketplace = ($('#marketplace').val() == 1);

	$('#tb-ctr-cat tbody').html('<tr><td colspan="10" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="' + msgLoading + '" width="16" height="16" /></td></tr>');

	if( $('#tb-ctr-prd').length ){
		$('#tb-ctr-prd tbody').html('<tr><td colspan="10" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="' + msgLoading + '" width="16" height="16" /></td></tr>');
	}

	currentAjaxRequest = $.ajax({
		type: "GET",
		url: urlAlias,
		dataType: 'json',
		success: function(res){
			var globals = res.globals;
			var globalsprd = res.globalsprd;
			var cats = res.cats;
			var prds = res.prds;
			var tfoot = '';

			$('#tb-ctr-cat caption').html(res.caption);

			tfoot += '<tr class="right bold">';
			tfoot += '<td align="left" headers="cat">' + comparatorStatsTotaux + '</td>';

			if (!marketplace) {
				tfoot += '<td headers="cat-click">' + globals.clicks + '</td>';
				tfoot += '<td headers="cat-cost">' + globals.cost + '</td>';
			}

			tfoot += '<td headers="cat-sales">' + globals.sales + '</td>';

			if (!marketplace) {
				tfoot += '<td headers="cat-transfo">' + globals.transfo + '</td>';
				tfoot += '<td headers="cat-cost-sales">' + globals.cost_sales + '</td>';
			}

			tfoot += '<td headers="cat-ca">' + globals.ca + '</td>';
			tfoot += '<td headers="cat-ca-ttc">' + globals.ca_ttc + '</td>';
			tfoot += '<td headers="cat-margin">' + globals.margin + '</td>';
			tfoot += '<td headers="cat-roi">' + globals.roi + '</td>';
			tfoot += '</tr>';

			$('#tb-ctr-cat tfoot').html(tfoot);

			// stats par catégorie
			var tbody = ''; var cat = false;
			for( var i=0 ; i<cats.length ; i++ ){
				cat = htmlspecialchars(cats[i].name);
				if( !res.use_title )
					cat = '<a onclick="return updateStatsCtr(' + cats[i].id + ');" href="/admin/comparators/stats/index.php?ctr='+ctrID+'&amp;parent='+cats[i].id+'">' + htmlspecialchars(cats[i].name) + '</a>';

				tbody += '	<tr class="right">';
				tbody += '		<td align="left" headers="cat">';
				tbody += '			' + viewCatIsSync(cats[i].is_sync);
				tbody += '			' + cat;
				tbody += '		</td>';
				if (! marketplace) tbody += '		<td headers="cat-click">' + cats[i].clicks + '</td>';
				if (! marketplace) tbody += '		<td headers="cat-cost">' + cats[i].cost + '</td>';
				tbody += '		<td headers="cat-sales">' + cats[i].sales + '</td>';
				if (! marketplace) tbody += '		<td headers="cat-transfo">' + cats[i].transfo + '</td>';
				if (! marketplace) tbody += '		<td headers="cat-cost-sales">' + cats[i].cost_sales + '</td>';
				tbody += '		<td headers="cat-ca">' + cats[i].ca + '</td>';
				tbody += '		<td headers="cat-ca-ttc">' + cats[i].ca_ttc + '</td>';
				tbody += '		<td headers="cat-margin">' + cats[i].margin + '</td>';
				tbody += '		<td headers="cat-roi">' + cats[i].roi + '</td>';
				tbody += '	</tr>';
			}

			$('#tb-ctr-cat tbody').html(tbody);

			$('#tb-ctr-prd').remove();
			var table = ''; var tfoot = ''; var tbody = '';

			// si le tableau des produits n'existe pas, alors on le créer
			if( prds.length ){
				var url = '/admin/comparators/stats/index.php?ctr=' + ctrID + '&amp;parent=' + catID;

				table += '<table class="tablesorter checklist" id="tb-ctr-prd">';
				table += '	<caption>' + comparartorStatsProduit + '</caption>';
				table += '	<col width="10"><col width="*">';

				if (! marketplace)
					table += '	<col width="100"><col width="100"><col width="100"><col width="100">';

				table += '	<col width="100"><col width="100"><col width="100"><col width="104"><col width="84"><col width="75" />';
				table += '	<thead>';
				table += '		<tr>';
				table += '			<th id="prd-check" align="center"><input type="checkbox" name="check-all" id="check-all" value="" /></th>';
				table += '			<th id="prd">' + comparatorStatsDesignation + '</th>';

				if (! marketplace)
					table += '		<th id="prd-click"><a href="' + url + '">' + comparatorStatsClics + '</a></th><th id="prd-cost"><a href="' + url + '">' + comparatorStatsCout + '</a></th>';

				table += '			<th id="prd-sales"><a href="' + url + '">' + comparatorStatsVentes + '</a></th>';

				if (! marketplace){
					table += '		<th title="' + comparatorStatsTauxTransformation + '" id="prd-transfo">';
					table += '			<a href="' + url + '">' + comparatorStatshtransfo + '</a></th><th id="prd-cost-sales"><a href="' + url + '">' + comparatorStatshCoutVente + '</a>';
					table += '		</th>';
				}

				table += '			<th title="' + comparatorStatsCAHTTitle + '" id="prd-ca"><a href="' + url + '">' + comparatorStatsCAHT + '</a></th>';
				table += '			<th title="' + comparatorStatsCATTCTitle + '" id="prd-ca-ttc"><a href="' + url + '">' + comparatorStatsCATTC + '</a></th>';
				table += '			<th title="' + comparatorStatshMarge + '" id="prd-margin"><a href="' + url + '">' + comparatorStatshMarge + '</a></th>';
				table += '			<th title="' + comparatorStatshROI + '" id="prd-roi"><a href="' + url + '">' + comparatortatshROISmall + '</a></th>';
				table += '			<th id="prd-export"><a href="' + url + '">' + comparatortatshExporte + '</a></th>';
				table += '		</tr>';
				table += '	</thead>';
				table += '	<tfoot></tfoot>';
				table += '	<tbody></tbody>';
				table += '</table>';

				$('#tb-ctr-cat').after(table);

				var tchecked = comparartorStatsActiveExportProduit + ( !marketplace ? comparatorStatsComparateur : comparatorStatsPlaceMarche ) + '.';
				var tunchecked = comparartorStatsDesactiveExportProduit + ( !marketplace ? comparatorStatsComparateur : comparatorStatsPlaceMarche ) + '.';

				// stats globales
				tfoot += '	<tr class="right bold">';
				tfoot += '			<td colspan="2" headers="prd" style="text-align: left;">';
				tfoot += '				<input type="submit" name="checked" id="checked" value="' + comparatortatshExporter + '" title="' + tchecked + '" />';
				tfoot += '				<input type="submit" name="unchecked" id="unchecked" value="' + comparatorStatsNePasExporter + '" title="' + tunchecked + '" />';
				tfoot += '			</td>';
				// tfoot += '		<td align="left" headers="prd">Totaux * :</td>';

				if (! marketplace){
					tfoot += '	<td headers="prd-click">'+ comparatorStatsTotaux+ ' * : ' + ' : ' + globalsprd.clicks + '</td>';
					tfoot += '	<td headers="prd-cost">' + globalsprd.cost + ' €</td>';
				}

				tfoot += '		<td headers="prd-sales">' + ( marketplace ? comparatorStatsTotaux : '' ) + ' ' + globalsprd.sales + '</td>';

				if (! marketplace){
					tfoot += '	<td headers="prd-transfo">' + globalsprd.transfo + ' %</td>';
					tfoot += '	<td headers="prd-cost-sales">' + globalsprd.cost_sales + ' €</td>';
				}

				tfoot += '		<td headers="prd-ca">' + globalsprd.ca + '</td>';
				tfoot += '		<td headers="prd-ca-ttc">' + globalsprd.ca_ttc + '</td>';
				tfoot += '		<td headers="prd-margin">' + globalsprd.margin + '</td>';
				tfoot += '		<td headers="prd-roi">' + magnify(globalsprd.roi, 'roi') + '</td>';
				tfoot += '		<td headers="prd-export">&nbsp;</td>';
				tfoot += '	</tr>';

				var p = false;
				for( i=0 ; i<prds.length ; i++ ){
					p = prds[i];

					tbody += '	<tr class="right">';
					tbody += '		<td headers="prd-check" align="center">';
					tbody += '			<input class="check-all-prd" type="checkbox" name="check-all[]" id="check-all-' + p.id + '" value="' + p.id + '" />';
					tbody += '		</td>';
					tbody += '		<td align="left" headers="prd">';
					tbody += '			' + viewPrdIsSync(p.is_sync);
					tbody += '			<a href="/admin/catalog/product.php?cat=' + catID + '&amp;prd=' + p.id + '" target="_blank">' + htmlspecialchars(p.name) + '</a>';
					tbody += '		</td>';

					if (! marketplace){
						tbody += '	<td headers="prd-click">' + p.clicks + '</td>';
						tbody += '	<td headers="prd-cost">' + p.cost + '</td>';
					}

					tbody += '		<td headers="prd-sales">' + p.sales + '</td>';

					if (! marketplace) {
						tbody += '	<td headers="prd-transfo">' + p.transfo + '</td>';
						tbody += '	<td headers="prd-cost-sales">' + p.cost_sales + '</td>';
					}

					tbody += '		<td headers="prd-ca">' + p.ca + '</td>';
					tbody += '		<td headers="prd-ca-ttc">' + p.ca_ttc + '</td>';
					tbody += '		<td headers="prd-margin">' + p.margin + '</td>';
					tbody += '		<td headers="prd-roi">' + p.roi + '</td>';
					tbody += '		<td headers="prd-export">';

					if( p.export == 'mix' ){
						tbody += '	 <a onclick="return showPopupExport( ' + p.id + ', ' + marketplace + ' );" name="prd-'+p.id+'" href="#" class="active">' + comparatorStatsPartiel + '</a>';
					}else if( p.export ){
						tbody += '	 <a onclick="return unactivatedProduct( $(this), '+ctrID+', true );" name="prd-'+p.id+'" href="#" class="active">' + comparatorStatsOui + '</a>';
					}else{
						tbody += '	 <a onclick="return activatedProduct( $(this), '+ctrID+', true );" name="prd-'+p.id+'" href="#" class="cancel">' + comparatorStatsNon + '</a>';
					}

					tbody += 		'</td>';
					tbody += '	</tr>';
				}

				$('#tb-ctr-prd tfoot').html(tfoot);
				$('#tb-ctr-prd tbody').html(tbody);
				loadSorter();
			}

			reloadActionProducts();
			$('#tb-ctr-cat').trigger("update");
		},
		complete: function(){
			currentAjaxRequest = false;
		}
	});
}

$(document).ready(
	function() {
		$('#block-card').scrollToFixed();
	}
).delegate(
	'#card2 .close', 'click', function(){
		$('#card2').html('');
		$('#card').hide();
		$('#categories-tree .sub').removeClass('sub');
	}
);

function show_treeview_card()
{
	$("#error-success").attr('style','display:none');
	$("#treeview-card").css('display','block');
	$('.not_actived_prds').css('display', 'inline-block');
}

function hide_treeview_card()
{
	$("#treeview-cat2").find("ul").css('display','none');
	$("#treeview-cat2").find("li").replaceClass('collapsable','expandable');
	$("#treeview-cat2").find("div").replaceClass('hitarea collapsable-hitarea','hitarea expandable-hitarea ');
	$("#treeview-card").css('display','none');
	$('.not_actived_prds').hide();
}

function show_card(cat, ctr)
{
	$("#categories-tree").find("span").removeClass('sub');
	$("#spanprd-"+cat).addClass('sub');
	$("#card").hide();
	$("#card2").html("");
	
	$.ajax({
		type: "POST",
		url: '/admin/comparators/ajax-card.php',
		data: 'cat='+cat+'&ctr='+ctr+'&marketplace='+($('#marketplace').val() == 1 ? 1 : 0),
		async:false,
		success: function(msg){
			$(msg).appendTo("#card2");
			
			if ($('#exp-new-2').length) $('#exp-new').parent().hide();
			
			var exp = $('span.name.sub').html().indexOf('(rattachée') !== -1;
			if (! exp) $('#exp-new').parent().show();
			
			var found = false;
			$('#choose input[type="radio"]').each(function() {
				if ($(this).attr('checked')) {
					found = true;
					return false;
				}
			});
			if (! found) $('#' + (exp ? 'exp-new' : 'no-exp')).click().parent().show();
			$("#card").show();
		}
	});
	
	hide_treeview_card();
	$("#cat-link").val(cat);
}
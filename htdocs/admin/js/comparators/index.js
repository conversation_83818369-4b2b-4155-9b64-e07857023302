var tree;
var ctrID = 0;
var activeCat = false;

$(document).ready(
	function(){
		if( $('.treeview').length ){
			$('.treeview').treeview({
				animated: "fast",
				collapsed: true,
				unique: true
			});
		}
		
		if( typeof(reloadEBay)!='undefined' && reloadEBay ){
			setInterval("checkTokenEBay()", 5000);
		}
		
		ctrID = $('#ctr_id').val();
	}
).delegate(
	'#del', 'click', function(){
		if( !$('input.del-redir:checked').length ){
			return false;
		}
		
		return window.confirm("Vous êtes sur le point de supprimer une ou plusieurs redirections.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?");
	}
).delegate(
	'#checkall', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parents('table').find('input[type=checkbox]').attr('checked', 'checked');
		}else{
			$(this).parents('table').find('input[type=checkbox]').removeAttr('checked');
		}
	}
).delegate(
	'.choose-new-cat', 'click', function(){
		activeCat = $(this).parents('tr:first').find('input[type=hidden]').attr('id').replace('new-cat-', '');
		displayPopup('Choisissez une famille', '', '/admin/comparators/popup-choose-family.php?ctr=' + ctrID, '', 620, 450);
	}
).delegate(
	'.card-title-ctr', 'click', function(){
		$(this).parent().click();
	}
).delegate(
	'#ebay-add-srv', 'click', function(){
		displayPopup('Ajouter un service de livraison', '', '/admin/comparators/eBay/popup-add-shipping.php', '', 620, 293);
		return false;
	}
).delegate(
	'.checkboxall', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parents('table').find('tbody input[type=checkbox]').attr('checked', 'checked');
		}else{
			$(this).parents('table').find('tbody input[type=checkbox]').removeAttr('checked');
		}
	}
).delegate(
	'#ebay-del-srv', 'click', function(){
		var checked = $('#ebay-shipping').find('input[type=checkbox]:checked');
		if( !checked.length ){
			return false;
		}
		
		var param = '';
		checked.each(function(){
			if( $(this).val()!='on' ){
				param += '&del[]=' + $(this).val();
			}
		});
		
		$.get('/admin/comparators/eBay/ajax-ebay.php?delShipping=1' + param, function( data ){
			$('#ebay-shipping tbody').html( data );
		});
		
		return false;
	}
).delegate(
	'.log-access', 'click', function(){
		var ctr = $(this).attr('href').replace('#ctr-id=', '');
		displayPopup('Historique des accès', '', '/admin/comparators/popup-log-access.php?ctr_id=' + ctr, '', 420, 450);
		return false;
	}
).delegate(
	'#riawebsitepicker .selectorview', 'click', function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	}
).delegate(
	'#riawebsitepicker .selector a', 'click', function(){
		var wst = $(this).attr('name').replace('w-', '');
		var url = window.location.href.replace(new RegExp(/[\?&]wst=[0-9]+/g), '');
		url += ( url.match(/\?/g) ? '&' : '?' ) + 'wst=' + wst;

		window.location.href = url;
	}
).delegate(
	'#cat-exp input[type=submit]', 'click', function(){
		$('.error, .success').remove();
		$(this).after('<br /><div class="notice" style="float: left;"><img width="16" height="16" src="/admin/images/loader_join_file.gif" style="border:none;"> Sauvegarde en cours, veuillez patientez...</div>');

		var ctrID = $('#ctr_id').val();
		var catVisited = $('#cat-visit').val();
		var i = 1;
		var count = 1;
		
		var cats = '';
		var save = 'null';
		
		$.ajax({
			type: 'post',
			url: '/admin/comparators/ajax-comparators.php',
			data: 'saveallfilter=1&ctr=' + ctrID + '&delete=1',
			async: false
		});

		var checkTotal = $('#family-tree input[type=checkbox]:not(:checked)').length;
		$('#family-tree input[type=checkbox]:not(:checked)').each(function(){
			cats += '&cat[]=' + $(this).val();

			if( i>=4950 || count==checkTotal ){
				// Requête Ajax de sauvegarde
				$.ajax({
					type: 'post',
					url: '/admin/comparators/ajax-comparators.php',
					data: 'saveallfilter=1&ctr=' + ctrID + '&cat-visit=' + catVisited + cats,
					async: false,
					complete: function( result ){
						if( save=='null' && result.responseText=='ok' ){
							save = true;
						}else if( result.responseText=='ko' ){
							save = false;
						}
					}
				});

				cats = '';
				i = 1;
			}

			i++;
			count++;
		});

		if( save ){
			$('.notice').remove();
			$(this).after('<br /><div class="success" style="float:left;">L\'enregistrement s\'est correctement déroulé</div>');
		}else{
			$(this).after('<br /><div class="error" style="float:left;">Une erreur inattendue s\'est produite lors de l\'enregistrement.</div>');
		}

		return false;
	}
).delegate(
	'#family-tree input[type=checkbox]', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parent().find('ul li input[type=checkbox]').attr('checked', 'checked');
		}else{
			$(this).parent().find('ul li input[type=checkbox]').removeAttr('checked');
		}
	}
);

function choose_ctr_family( catID, catName ){
	$('#new-cat-' + activeCat).val( catID );
	$('#new-cat-' + activeCat).parent().find('.name-cat').html( catName );
	
	return hidePopup();
}
function reloadEBayShipping(){
	$.get('/admin/comparators/eBay/ajax-ebay.php?reloadShipping=1', function( data ){
		$('#ebay-shipping tbody').html( data );
		hidePopup();
	});
	
	return false;
}

function checkTokenEBay(){
	if( typeof(ctrID)!='undefined' && ctrID==15 ){
		$.get('/admin/comparators/eBay/check-ebay.php', function(data){
			if( data=='ok' ){
				window.location.href='/admin/comparators/params.php?ctr=15';
			}
		});
	}
}

function ctrSwitchActivation(ctr, active){
	$("div.error").each(function(){
		$(this).remove();
	});
	
	$.ajax({
		type: "POST",
		url: '/admin/comparators/ajax-comparators.php',
		data: 'ctr='+ctr+'&active='+( active ? 1 : 0 ),
		async:false,
		dataType: 'xml',
		success: function(xml){
			if( $(xml).find('result').attr('type') == '1' ){
				var elem = $('#comparator-' + ctr)
				
				elem.before( $(xml).find('code_html').text() ).remove();
			} else {
				$("#comparator-"+ctr).prepend( '<div class="error">'+$(xml).find('error').text()+'</div>' );
			}
		}
	});
	return false;
}

function saveLinkCategories( ctr ){
	// supprime tous les messages d'erreur
	$('.error').remove();
	
	var catCtr = $('#treeview-cat-card input[type=radio]:checked').attr('value');
	var catPrd = $('#cat-link').val();
	var catName = $('#spanprd-' + catPrd ).html();
	var choose = $('#choose input[type=radio]:checked').attr('value');
	
	var notActivedPrds = $('#not_actived_prds').is(':checked');
	
	var error = false;
	if( choose==1 && catCtr==undefined && has_ctr_categories)
		error = 'Afin d\'exporter les produits de la catégorie "' + catName + '", vous devez choisir une des familles du comparateur.';
	else {
		$.ajax({
			type: "POST",
			url: '/admin/comparators/ajax-save.php',
			data: 'savelink=1&ctr=' + ctr + '&catPrd=' + catPrd + '&catCtr=' + catCtr + '&choose=' + choose + '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0) + '&not_actived_prds=' + (notActivedPrds ? 1 : 0) ,
			dataType: 'xml',
			success: function(xml) {
				// Si la suppression a réussie
				if( $(xml).find('result').attr('type') == '1' ){
					$('#spanprd-' + catPrd + ' span.card-title-ctr').remove();
					
					$('#exp-new').parent()[choose >= 1 || $('#exp-new-2').length ? 'hide' : 'show']();
					
					if( choose<0 ){
						$('#spanprd-' + catPrd).parents('li:first').find('span.card-title-ctr').remove()
					} else if( choose>=1 ){
						var textCat = '<span class="card-title-ctr">(rattachée' + ((catName = $(xml).find('success').attr('catctr')) ? ' à ' + catName : '') + ')</span>';
						$('#spanprd-' + catPrd).append(  textCat );
						$('#spanprd-' + catPrd).parents('li:first').find('ul span.tree-nod-name').each( function(){
							if( !$(this).find('.card-title-ctr').length ){
								$(this).append(  textCat );
							}
						});
					}
				}
				$('label.name.sub').click();
				show_card( catPrd, ctr );
			}
		});				
	}
	
	if( error ){
		$('#card-action').before( 
			'<div class="error">' + error + '</div>'
		);
	} else {
	
	}
	
	return false;
}

function getScrollPosition(){
    return Array((document.documentElement && document.documentElement.scrollLeft) || window.pageXOffset || self.pageXOffset || document.body.scrollLeft,(document.documentElement && document.documentElement.scrollTop) || window.pageYOffset || self.pageYOffset || document.body.scrollTop);
}

function showDisabledFamily( ctrID, catID, classID ){
	displayPopup('Utilisation de la famille désactivée', '', '/admin/comparators/popup-details-family.php?ctr=' + ctrID + '&oldcat=' + catID + '&class=' + classID, '', 620, 450);
	return false;
}

function chooseNewFamilyDetails( ctrID, fmlID, objID, classID ){
	parent.displayPopup('Choisissez une famille', '', 'popup-choose-family.php?ctr=' + ctrID + '&family=' + fmlID + '&object=' + objID + '&class=' + classID + '&' + $('form').serialize(), '', 620, 450);
	return false;
}

function saveNewFamily( fmlID, details ){
	if( $.trim(details) != '' ){
		$('#fml-disabled-' + fmlID).find('sub').html( details );
	}else{
		$('#fml-disabled-' + fmlID).remove();

		var trLength = $('#choose-new-cat tbody tr').length;

		if( !trLength ){
			$('#choose-new-cat').parent().prev().remove();
			$('#choose-new-cat').parent().remove();
		}
	}

	hidePopup();
}
/**	\file segments.js
 *	Ce fichier gère l'intéractivité sur les pages de gestion des segments : pages de gestion des segments
 *	mais aussi onglets faisant appels aux segments (pages de gestion de contenu, bannières, promotions, etc...)
 */

var valCopy = '';
var symbolCopy = '';

$(document).ready(
	function(){
		if( typeof segID != 'undefined' ){
			if( segID==0 ){
				segGroupAdd( true );
			}
			
			var c = 0;
			$('.cdt-grp').each(function(){
				nbGroupCdts[ c ] = $(this).find('.cdt-config').length;
				c++;
			});
			$('input.datepicker').each(function(){
				var temp = this ;
				// Implémente le sélecteur de date sur chacun d'entre eux.
				$(this).DatePicker({
					format:'d/m/Y',
					date: $(this).val(),
					current: $(this).val(),
					starts: 1,
					onChange: function(formated, dates){
						if(dates != 'Invalid Date'){
							$(temp).val(formated);
							$(temp).DatePickerHide();
						}
					}
				});
				
			});
			$('div.datepicker').each(function(k,v){
				$(v).css('z-index', 99999);
			});
			
			loadSegment();
		}

		if( typeof $('#obj-seg') != 'undefined' && $('#obj-seg').length ){
			riaSortable.create({
				'table'	:	$('#obj-seg'),
				'url'	:	'/admin/ajax/segments/ajax-type-position-update.php'
			});
		}
		reloadSelectorOrigin();
		$('.riapicker .selector').each(function(){
			var active = true;

			if( window.location.href.indexOf("admin/catalog/edit.php") !== -1){
				active = false;
			}
			if (window.location.href.indexOf("admin/config/fields/segments/index.php") !== -1 ){
				active = false;
			}

			/*	Attention, sur la page admin/catalog/edit.php, onglet tarification,
			le code ci-dessous fait disparaître la période actuellement sélectionnée */
			if( active ){
				if(!$(this).parent().hasClass('dates')){
					reloadSelectorView($(this).find('a'));
				}
			}
		});
	}
).mouseup(
	function (e){
	    var container = $(".riapicker");
	    if( !$('#riawebsitepicker') && !$('#rialanguagepicker') && !container.is(e.target)  && container.has(e.target).length === 0 ){
	    	if(!$('.datepicker[style*="display: block"]').length){
	       		$('.selector').hide();
	    	}
	    }
	}
).delegate(
	'.select-dates-cdt', 'change', function(){
		setDate(this);
	}
).delegate(
	'.riapicker .selectorview', 'click', function(){
		var active = true;

		if( window.location.href.indexOf("admin/catalog/edit.php") !== -1){
			active = false;
		}
		if (window.location.href.indexOf("admin/config/fields/segments/index.php") !== -1 ){
			active = false;
		}
		/*	Attention, sur la page admin/catalog/edit.php, onglet tarification, 
		le code ci-dessous est déjà exécuté dans fields.js, le contenu du menu déroulant n'est pas affiché	*/
		if( active ){
			var $selector = $(this).parent().find('.selector');
			if($selector.css('display')=='none'){
				$(".selector").hide();
				$selector.show();
				$selector.position({ 
					my : "left top", 
					at: "left bottom" ,
					of: $(this),
					collision: 'flip'
				});
			}else{
				$selector.hide();
			}
		}
		
	}
).delegate(
	'.riapicker .selector a', 'click', function(){
		var active = true;

		if( window.location.href.indexOf("admin/catalog/edit.php") !== -1){
			active = false;
		}
		if (window.location.href.indexOf("admin/config/fields/segments/index.php") !== -1 ){
			active = false;
		}

		/*	Attention, sur la page admin/catalog/edit.php, onglet tarification, 
		le code ci-dessous fait toujours afficher "Toutes" dans le menu déroulant à la sélection */
		if( active ){
			var attrOrigin = $(this).attr('name');
			var $selector = $(this).closest('.selector');
			var $selectorview = $selector.parent().find('.selectorview').find('.view');
			if( !$selector.parent().hasClass('dates') ){
				if( attrOrigin=="1" || attrOrigin=="2" || attrOrigin=="3" ){
					$selectorview.html($(this).find('label').html());
					$(this).parent().find('input:checked').removeAttr('checked');
					$(this).find('input').attr({checked:'checked'});
					$(this).parent().hide();
				}else{
					$(this).parent().parent().find('input[value=1]').removeAttr('checked');
					$(this).parent().parent().find('input[value=2]').removeAttr('checked');
					$(this).parent().parent().find('input[value=3]').removeAttr('checked');
					reloadSelectorView(this);
				}
				if($selectorview.html()===""){
					$selectorview.html('Toutes');
					$(this).parent().parent().find('input[value=1]').attr({checked:'checked'});
				}
			}
		}
	}
).delegate(
	'.dates .selector a', 'click', function(){
		var date_code = $(this).attr('name');
		var $selector = $(this).closest('.selector');
		var $selectorview = $selector.parent().find('.selectorview').find('.view');
		$selector.parent().find('.cdt-dates').val(date_code);
		if( $(this).attr('name') === 'perso' ){
			$selectorview.html($(this).find('label').html());
		}else{	
			$selectorview.html($(this).html());
		}
		$selector.hide();
	}
).delegate(
	'a[name=perso]', 'mouseover', function(){
		var $selector = $(this).closest('.selector');
		var $selectorview = $selector.parent().find('.selectorview').find('.view');
		var date_code = $(this).attr('name');
		var label = $(this).find('label').html();
		if( label !== $selectorview.html() ){
			$selector.parent().find('.cdt-dates').val(date_code);
			$selectorview.html(label);
		}
	}
).delegate(
	'input[type=checkbox]', 'click', function(){
		var parent = $(this).parent();

		if( parent.hasClass('parent') ){
			var is_checked = parent.find('[type=checkbox]').is(':checked');
			
			if( is_checked ){
				parent.parent().find('.child [type=checkbox]').attr('checked', 'checked');
			}else{
				parent.parent().find('.child [type=checkbox]').removeAttr('checked');
			}
		}
		reloadSelectorOrigin();
	}
).delegate(
	'#save-main', 'click', function(){
		return segSave();
	}
).delegate(
	'#cdt-add-grp', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		segGroupAdd( true );
	}
).delegate(
	'.cdt-grp-del', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		// retire la règle entre groupe si la suppression se fait sur le premier groupe
		if( !$(this).parents('fieldset').prev().length && $(this).parents('fieldset').next().length ){
			$(this).parents('fieldset').next().find('.cdt-grp-rule').remove();
			
			// supprime le ET du groupe qui suit
			var next = $(this).parents('fieldset').next();
			if( !next.attr('class').match(/btn-action-small/) ){
				next.remove();
			}
		}
		
		// supprime le groupe et le ET
		$(this).parents('fieldset').prev().remove();
		$(this).parents('fieldset').remove();
		
		// met à jour le label du groupe
		var nbGrp = $('.add-seg-cdt fieldset').length;
		for( var i=0 ; i<nbGrp ; i++ ){
			$('.add-seg-cdt fieldset:eq(' + i + ') legend label').html( 'Groupe ' + (i+1) + ' - Valide');
		}		
		
		if( !$('.add-seg-cdt fieldset').length ){
			nbGroups = 0;
		}
	}
).delegate(
	'.cdt-search-cat', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var inputIdCat = $(this).parent().find('input[type=hidden]').attr('id');
		var inputIdName = $(this).parent().find('input[type=text]').attr('id');
		var parent = $(this).parent().find('input[type=hidden]').val();
		displayPopup( segmentsRechercheCategorie, '', '/admin/catalog/popup-categories.php?parent=' + parent + '&input_id_cat_id=' + inputIdCat + '&input_id_name=' + inputIdName );
	}
).delegate(
	'.cdt-search-prd', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var inputIdPrd = $(this).parent().find('input[type=hidden]').attr('id');
		var inputIdRef = $(this).parent().find('input[type=text]').attr('id');
		displayPopup( segmentsRechercheProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&input_id_prd_id=' + inputIdPrd + '&input_id_ref=' + inputIdRef );
	}
).delegate( // Bouton Rechercher (un compte client)
	'.cdt-search-client', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var prf = $(this).attr('class').match('cdt-search-representant')!=null ? 5 : 0;
		var inputIdUsr = $(this).parent().find('input[type=hidden]').attr('id');
		var inputIdEmail = $(this).parent().find('input[type=text]').attr('id');
		displayPopup( segmentsRechercheClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&prf=' + prf + '&input_id_usr_id=' + inputIdUsr + '&input_id_usr_email=' + inputIdEmail );
	}
).delegate(
	'.select-obj-seg', 'change', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var option = $(this).find('option:selected');
		if( option.val()=='-1' ) return false;
		var url = '/admin/ajax/segments/ajax-segments.php?addObjSeg=' + option.val();
		
		url += '&clsID=' + ($('#seg-obj-cls').length ? parseInt($('#seg-obj-cls').val())  : 0 );
		url += '&obj0=' + ($('#seg-obj-id-0').length ? parseInt($('#seg-obj-id-0').val()) : 0 );
		url += '&obj1=' + ($('#seg-obj-id-1').length ? parseInt($('#seg-obj-id-1').val()) : 0 );
		url += '&obj2=' + ($('#seg-obj-id-2').length ? parseInt($('#seg-obj-id-2').val()) : 0 );
		
		$.getJSON( url, function( result ){
			if( result==null ) return false;
			if( result.success ){
				$('.seg-obj-infos').html( result.data );
				$('.select-obj-seg option').removeAttr('selected');
				$('.select-obj-seg option').eq(0).attr('selected', 'selected');
			}
		});
		
		option.remove();
	}
).delegate(
	'.del-obj-seg', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var segID = $(this).attr('name').replace('del-seg-', '');
		var url = '/admin/ajax/segments/ajax-segments.php?delObjSeg=' + segID;
		
		url += '&clsID=' + ($('#seg-obj-cls').length ? parseInt($('#seg-obj-cls').val())  : 0 );
		url += '&obj0=' + ($('#seg-obj-id-0').length ? parseInt($('#seg-obj-id-0').val()) : 0 );
		url += '&obj1=' + ($('#seg-obj-id-1').length ? parseInt($('#seg-obj-id-1').val()) : 0 );
		url += '&obj2=' + ($('#seg-obj-id-2').length ? parseInt($('#seg-obj-id-2').val()) : 0 );
		
		$.getJSON( url, function( result ){
			if( result==null ) return;
			if( result.success ){
				$('.seg-obj-infos').html( result.data );
				$('.select-obj-seg').html( result.option );
			}
		});
		
		return false;
	}
).delegate(
	'.cdt-fields-cdt', 'change', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}

		var fldID = $(this).val();
		if( parseInt(fldID)>0 ){

			var attrID = $(this).attr('id').replace('cdt-fields-', '');
			var grp = attrID.substr(0, attrID.indexOf('-'))
			var cdt = attrID.substr(attrID.indexOf('-')+1)
			
			segCriterionSymbolsLoad( grp, cdt, $('#cdt-type-' + grp + '-' + cdt).val(), fldID );
			segCriterionFormLoad( grp, cdt, $('#cdt-type-' + grp + '-' + cdt).val(), fldID );
		}
	}
).delegate( // Zone de liste "Symbôle de comparaison"
	'.cdt-psy', 'change', function(){
		var symbol = $(this).val();
		var value = $(this).parent().parent().find('.cdt-grp-value-span');
		switch( symbol ){
			case "=''": // Pour les symbôles ='' et !='', la valeur doit être masquée
			case "!=''":
				value.hide();
				break;
			default: // Dans le cas contraire, la valeur est utile
				value.show();
		}
	}
);

// S'assure que le champ valeur n'est visible que si pertinent
$(document).ready(
	function (){
		$('.cdt-psy').trigger('change');
	}
);

function setDate(that){
	var name = $(that).val();
	var dateDiv = $(that).parent().find('.div-dates');
	var dateDays = $(that).parent().find('.div-days');

	switch(name){
		case 'dyn':
			dateDiv.hide();
			dateDays.show();
			break;
		case 'perso':
			dateDiv.show();
			dateDays.hide();
			break;
	}
}
function segSave(){
	var objDisabled = new Array();
	
	$('select:disabled').each(function(){
		$(this).removeAttr('disabled');
		objDisabled.push( $(this).attr('id') );
	});
	
	$('.error, .success').remove();
	$('.cdt-grp-cdt').each(function(){
		if($.trim($(this).val())==''){
			$(this).parents('.cdt-config').remove()
		}
	});
	
	var datas = $('#form-segment').serialize();
	
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'saveSeg=1&seg=' + segID + '&cls=' + segClsID + '&' + datas,
		type: 'post',
		dataType: 'JSON',
		async: false,
		success: function(returns){
			
			if( returns.success=='0' ){
				for( const obj in objDisabled ){
					$('#' + objDisabled[ obj ]).attr('disabled', 'disabled');
				}
				
				$('#form-segment').before( '<div class="error">' + returns.data + '</div>' );
				$('html, body').animate({  
					scrollTop:$('#site-content').offset().top  
				}, 'slow'); 
			} else {
				var to_news = $('#to_news').length ? $('#to_news').val()=='1' : false;
				
				var comp_url = '';
				if( to_news ){
					comp_url += '&dest=NEWSLETTER_FILTER';
				}
				if( window.location.href.indexOf('/customers/segments/')!==-1 ){
					window.location.href = '/admin/customers/segments/segment.php?id=' + returns.data + '&success=1' + comp_url;
				}else{						
					window.location.href = '/admin/config/fields/segments/segment.php?id=' + returns.data + '&success=1' + comp_url;
				}
			}
		}
	});
	
	return false;
}
/**	Cette fonction est appelée dans une popup de sélection de produit et va renseigner dans la page principale (appelante)
 *	le produit choisi par l'utilisateur.
 *	@param id Obligatoire, identifiant du produit sélectionné par l'utilisateur
 *	@param name Obligatoire, désignation du produit sélectionné par l'utilisateur
 *	@param catId Obligatoire, identifiant de la catégorie principale dans laquelle le produit est classé
 *	@param inputIdPrdId Obligatoire, identifiant HTML du champ caché qui doit recevoir l'identifiant du produit
 *	@param inputIdName Obligatoire, identifiant HTML du champ qui doit recevoir la désignation du produit
 *	@param inputIdRef Obligatoire, identifiant HTML du champ qui doit recevoir la référence du produit
 *	@param inputIdCatId Obligatoire, identifiant HTML du champ qui doit recevoir l'identifiant de la catégorie dans laquelle le produit est classé
 */
function parent_select_prd(id, name, ref, catId, inputIdPrdId, inputIdName, inputIdRef, inputIdCatId){
	if( inputIdPrdId!='undefined' && inputIdPrdId!='' )
		$('#'+inputIdPrdId).val(id);
	if( inputIdName!='undefined' && inputIdName!='' )
		$('#'+inputIdName).val(ref + ' - ' + name);
	if( inputIdRef!='undefined' && inputIdRef!='' )
		$('#'+inputIdRef).val(ref + ' - ' + name);
	if( inputIdCatId!='undefined' && inputIdCatId!='' )
		$('#'+inputIdCatId).val(catId);
}
/**	Cette fonction est appelée dans une popup de sélection de compte client et va renseigner dans la page principale (appelante)
 *	le compte client choisi par l'utilisateur.
 * @param {*} id Obligatoire, identifiant du compte client sélectionné
 * @param {*} email Obligatoire, adresse email du compte client sélectionné
 * @param {*} inputIdUsrId Obligatoire, identifiant HTML du champ caché qui va recevoir l'identifiant de l'utilisateur sélectionné
 * @param {*} inputIdUsrEmail Obligatoire, identifiant HTML du champ qui va recevoir l'adresse email de l'utilisateur sélectionné
 */
function parent_select_user( id, email, inputIdUsrId, inputIdUsrEmail ){
	if( inputIdUsrId!='undefined' && inputIdUsrId!='' )
		$('#'+inputIdUsrId).val( id );
	if( inputIdUsrEmail!='undefined' && inputIdUsrEmail!='' )
		$('#'+inputIdUsrEmail).val( email );
}
function updateCat( id, parent, name, inputIdCatId, inputIdName ){
	if( inputIdCatId!='undefined' && inputIdCatId!='' )
		$('#'+inputIdCatId).val( id );
	if( inputIdName!='undefined' && inputIdName!='' )
		$('#'+inputIdName).val( name );
}

/**
 *	Cette fonction permet de copier un groupe de condition d'un segment
 *	@param $seg Obligatoire, identifiant d'un segment
 *	@param $grp Obligatoire, identiifant d'un groupe
 */
function segGroupCopy( grp ){
	
	var group = $('#cdt-copy-' + grp).parents('fieldset');
	segGroupAdd( false );
	
	var grpID = nbGroups - 1;
	var cs = group.find('.cdt-grp-cdt');
	
	var c = false;
	for( var i=0 ; i<cs.length ; i++ ){
		c = cs.eq(0);
		segCriterionsAdd( grpID );
		
		valCopy = c.parents('.cdt-config').find('.div-cdt-value').eq(0);
		symbolCopy = c.parents('.cdt-config').find('.cdt-psy').val();
		
		$('#cdt-type-' + grpID + '-' + i + ' option[value=' + $('#cdt-type-' + grp + '-' + i + ' option:selected').val() + ']').attr('selected', 'selected').change();
		
		valCopy = '';
		symbolCopy = '';
		
	}
}

function segGroupAdd( addCriterion ){
	var newGrp = '';
	
	if( nbGroups>0 ){
		newGrp += '<div class="cdt-separator-and">ET</div>';
	}
	
	newGrp += '	<fieldset class="cdt-grp">';
	newGrp += '		<legend class="cdt-grp-legend">';
	newGrp += '		<label for="cdt-general-' + nbGroups + '">' + segmentsGroupe + ' ' + ($('.add-seg-cdt fieldset').length+1) +  ' - ' + segmentsAumoinsCritere + ' : </label>';
	newGrp += '		<input class="cdt-grp-del input-icon-del" type="button" name="del-grp-cdt" id="del-grp-cdt-' + nbGroups + '" value="x" />';
	newGrp += '		</legend>';
	newGrp += '		<div id="cdt-grp-list-cdt' + nbGroups + '" class="cdt-grp-list-cdt">';
	newGrp += '		</div>';
	// newGrp += '		<input type="button" title="Copier le groupe" name="cdt-copy" id="cdt-copy-' + nbGroups + '" value="Copier le groupe" class="btn-action-small" onclick="return segGroupCopy(' + nbGroups + ');" />';
	newGrp += '		<input type="button" onclick="return segCriterionsAdd(' + nbGroups + ');" class="btn-action-small" value="' + segmentAjouterCritere + '" id="cdt-add-' + nbGroups + '" name="cdt-add" title="' + segmentAjouterCritere + '" />';
	newGrp += '	</fieldset>';
	
	$('#cdt-add-grp').before( newGrp );
	nbGroupCdts[ nbGroups ] = 0;
	
	if( addCriterion ){
		segCriterionsAdd( nbGroups );
	}
	
	nbGroups++;
}

/**
 *	Cette fonction permet d'ajouter un critère de segmentation à un groupe de critères.
 *	@param grp Obligatoire, numéro du groupe
 */
function segCriterionsAdd( grp ){
	nbGroupCdts[ grp ] = $('#cdt-grp-list-cdt' + grp + ' .cdt-config').length;
	
	var nbCdt = nbGroupCdts[ grp ];
	var newCdt = '';

	newCdt += '		<div class="cdt-config">';
	
	if( nbCdt > 0 ){
		newCdt += '<div class="cdt-separator-or">OU</div>';
	} else {
		newCdt += '<div class="cdt-separator-or">&nbsp;</div>';
	}
	
	newCdt += '			<div class="cdt-select-second">';
	newCdt += '				<select id="cdt-type-' + grp + '-' + nbCdt + '" name="cdt-type[' + grp + '][' + nbCdt + ']" class="selectmenu cdt-grp-cdt">';
	newCdt += '					<option value=""></option>';
	newCdt += '				</select>';
	newCdt += '			</div>';
	newCdt += '			<div class="cdt-psy-val" style="display:none;">';
	newCdt += '				<select id="cdt-symbol-' + grp + '-' + nbCdt + '" name="cdt-symbol[' + grp + '][' + nbCdt + ']" class="selectmenu cdt-psy">';
	newCdt += '					<option value="-1"></option>';
	newCdt += '				</select>';
	newCdt += '			</div>';
	newCdt += '			<input type="button" onclick="return segCriterionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" title="' + segmentsSuppressionCritere + '" />';
	newCdt += '		</div>';
	newCdt += '		<div class="clear"></div>';

	$('#cdt-grp-list-cdt' + grp).append( newCdt );
	
	// chargement des critères et des symboles accessible pour le type de promotion en cours d'utilisation
	segCriterionsLoad( grp, nbCdt );
	// $('.selectmenu').selectmenu();
	// $('.selectmenu').selectmenu('destroy');
	// $('.selectmenu').selectmenu();
	nbGroupCdts[ grp ] = nbGroupCdts[ grp ] + 1;
	return false;
}

/**
 *	Cette foncton permet de charger les critères accessibles.
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 */
function segCriterionsLoad( grp, cdt ){
	if( !$('#cdt-type-' + grp + '-' + cdt).length ) return false;
	
	// $.getJSON('/admin/ajax/segments/ajax-segments.php?getCdt=1&cls=' + segClsID , function( cdts ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getCdt=1&cls=' + segClsID,
		dataType: 'JSON',
		async: false,
		success: function(cdts){
			var optionsCdt = '';
			
			if( cdts.length ){
				var c = false;
				var	pos = 1;
				var grp_name = '';
				
				for( var i=0 ; i<cdts.length ; i++ ){
					c = cdts[i];
					
					if( grp_name!=c.scg_name ){
						if( pos!=1 ){
							optionsCdt += '	</optgroup>';
						}
						optionsCdt += '		<optgroup label="' + c.scg_name + '">';
						grp_name = c.scg_name;
					}
						
					optionsCdt += '				<option title="' + c.desc + '" value="' + c.id + '">' + c.name + '</option>';
						
					if( grp_name!=c.scg_name || pos==cdts.length ){
						optionsCdt += '		</optgroup>';
					}
						
					pos++;
				
				}
				
				$('#cdt-type-' + grp + '-' + cdt).append( optionsCdt ).change(function(){
					if( parseInt($(this).val())==48 || parseInt($(this).val())==41 ){
						segCriterionsFieldsLoad( grp, cdt );
					}else{
						segCriterionWebsitesLoad( grp, cdt, $(this).val() );
						segCriterionOrderSourcesLoad( grp, cdt, $(this).val() );
						segCriterionDatesLoad( grp, cdt, $(this).val() );
						segCriterionSymbolsLoad( grp, cdt, $(this).val() );
						segCriterionFormLoad( grp, cdt, $(this).val() );	
					}
				});
			}
		}
	});
	
	return false;
}

/** Cette fonction permet de charger la liste des champs avancés
 *
 */
function segCriterionsFieldsLoad( grp, cdt ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getFields=1&cls=' + segClsID,
		dataType: 'JSON',
		async: false,
		success: function(fields){
			if( typeof fields != 'undefined' && fields.length ){
				if( $('#cdt-fields-' + grp + '-' + cdt).length ){
					$('#cdt-fields-' + grp + '-' + cdt).parent().remove();
				}
				
				var htmlFields  = '';
				if( fields!=null && fields.length ){
					var valCheck = valCopy!='' && valCopy.parents('.cdt-grp-list-cdt').find('.cdt-fields-cdt') ? valCopy.parents('.cdt-grp-list-cdt').find('.cdt-fields-cdt').val() : '';
					var f = false; 

					var optionFields = '<option ' + (valCheck==f.id ? 'selected="selected"' : '') + ' value="-1"></option>';
					for( var i=0 ; i<fields.length ; i++ ){
						f = fields[i];
						optionFields += '				<option ' + (valCheck==f.id ? 'selected="selected"' : '') + ' title="' + f.name + '" value="' + f.id + '">' + f.name + '</option>';
					}
					
					htmlFields += '<div class="div-fields-cdt">';
					htmlFields += '<label for="cdt-fields-' + grp + '-' + cdt + '">Champ :</label>';
					htmlFields += '<select class="selectmenu cdt-fields-cdt" name="cdt-fields[' + grp + '][' + cdt + ']" id="cdt-fields-' + grp + '-' + cdt + '">' + optionFields + '</select>';
					htmlFields += '</div>';
				} else {
					htmlFields += '<div class="div-fields-cdt" style="display:none">';
					htmlFields += '<select class="selectmenu cdt-fields-cdt" name="cdt-fields[' + grp + '][' + cdt + ']" id="cdt-fields-' + grp + '-' + cdt + '"><option value="-1" selected="selected"></option></select>';
					htmlFields += '</div>';
				}
				
				$('#cdt-type-' + grp + '-' + cdt).after( htmlFields );
				// $('.selectmenu').selectmenu();
			}
		}
	});
}

/** Cette fonction permet de charger la saisie d'une période de date
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 *	@param idCdt Obligatoire, identifiant du critère sélectionné
 */
function segCriterionDatesLoad( grp, cdt, idCdt ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getDatesAccept=1&cls=' + segClsID + '&cdt=' + idCdt,
		dataType: 'JSON',
		async: false,
		success: function(active){
			if( $('#cdt-dates-' + grp + '-' + cdt).length ){
				$('#cdt-dates-' + grp + '-' + cdt).parent().remove();
			}
			
			if( typeof active.res != 'undefined' && active.res.length && active.res=='1' ){
				var htmlDate  = '';
				htmlDate += '<div class="div-dates-cdt">';
				htmlDate += '	';
				htmlDate += '	<label for="cdt-dates-' + grp + '-' + cdt +'">' + segmentsPeriode + '</label><div class="riapicker items-list-filters dates" id="cdt-dates-' + grp + '-' + cdt +'">';
				htmlDate += '	<input type="hidden" class="cdt-dates" name="cdt-dates[' + grp + '][' + cdt +']" value="'+Object.keys(active.dates)[0]+'">';
				htmlDate += '		<div class="selectorview">';
				htmlDate += '			<div class="left">';
				htmlDate += '				<span class="view">'+active.dates.perso+'</span>';
				htmlDate += '		</div>';
				htmlDate += '		<a class="btn" name="btn">';
				htmlDate += '			<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />';
				htmlDate += '		</a>';
				htmlDate += '		<div class="clear"></div>';
				htmlDate += '	</div>';
				htmlDate += '	<div class="selector">';

				$.each(active.dates,function(k,v){
					if( k==='perso' ){
						htmlDate += '<a name="'+k+'">';
						htmlDate += '	<label for="sos-'+k+grp+cdt+'">'+v+'</label>';
						htmlDate += '	<div class="dates-days">';
						htmlDate += '		<input class="date datepicker date-start" type="text" name="dates-start['+grp+']['+cdt+']" id="cdt-dates-start-'+grp+'-'+cdt+'" value="" />';
						htmlDate += '		<span class="end" for="cdt-dates-end-'+grp+'-'+cdt+'">-</span>';
						htmlDate += '		<input class="date datepicker date-end" type="text" name="dates-end['+grp+']['+cdt+']" id="cdt-dates-end-'+grp+'-'+cdt+'" value="" />';
						htmlDate += '	</div>';
						htmlDate += '</a>';
					}else{
						htmlDate += '<a name="'+k+'">'+v+'</a>';
					}
				});

				htmlDate += '</div></div>';
				
				
				$('#cdt-type-' + grp + '-' + cdt).parent().append( htmlDate );

				$('#cdt-dates-start-' + grp + '-' + cdt + ', #cdt-dates-end-' + grp + '-' + cdt).each(function(){
					var temp = this ;
					
					// Implémente le sélecteur de date sur chacun d'entre eux.
					$(this).DatePicker({
						format:'d/m/Y',
						date: $(this).val(),
						current: $(this).val(),
						starts: 1,
						onChange: function(formated, dates){
							if(dates != 'Invalid Date'){
								$(temp).val(formated);
								$(temp).DatePickerHide();
							}
						}
					});

					$('div.datepicker').each(function(k,v){
						$(v).css('z-index', 99999);
					});
					
				});

			}
		}
	});
}

/** 
 *	Cette fonction permet de charger les types de commande
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 *	@param idCdt Obligatoire, identifiant du critère sélectionné
 */
function segCriterionOrderSourcesLoad( grp, cdt, idCdt ){
	// $.getJSON('/admin/ajax/segments/ajax-segments.php?getOrdSource=1&cls=' + segClsID + '&cdt=' + idCdt, function( ordSources ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getOrdSource=1&cls=' + segClsID + '&cdt=' + idCdt,
		dataType: 'JSON',
		async: false,
		success: function(ordSources){
			if( $('#cdt-source-' + grp + '-' + cdt).length ){
				$('#cdt-source-' + grp + '-' + cdt).parent().remove();
			}

			var htmlSource  = '';
			if( ordSources!==null){
				var old_opt, in_opt = false;
				var valCheck = valCopy!='' && valCopy.parents('.cdt-grp-list-cdt').find('.cdt-source-cdt') ? valCopy.parents('.cdt-grp-list-cdt').find('.cdt-source-cdt').val() : '';
				var c = false; var optionsOrdSource = '';
				var source_group=1;
				htmlSource += '<div class="div-source-cdt"><label for="cdt-source-'+ grp+'-'+cdt +'">' + segmentsCommande + '</label><div id="cdt-source-'+grp+'-'+cdt+'" class="riapicker items-list-filters">'
				+'<div class="selectorview">'
					+'<div class="left">'
						+'<span class="view"></span></div>'
						+'<a class="btn" name="btn">'
							+'<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />'
						+'</a>'
						+'<div class="clear"></div>'
					+'</div>'
					+'<div class="selector">';
				for( var i=0 ; i<ordSources.length ; i++ ){
					c = ordSources[i];

					if( c.group_id != 0 && c.group_id != old_opt ){
						if( in_opt ){
							optionsOrdSource += '</div>';
						}

						optionsOrdSource += '<div>'
							+'<a class="parent" name="gp-'+source_group + grp +cdt+'">'
							+'<input type="checkbox" id="gp-'+source_group + grp +cdt+'" value="gp-'+source_group + grp +cdt+'" />'
							+'<label for="gp-'+source_group + grp +cdt+'">'+c.group_name+'</label>'
							+'</a>';
						old_opt = c.group_id;
						source_group++;
						in_opt  = true;
					}

					optionsOrdSource += '<a class="'+((c.id==1||c.id==2||c.id==3)?'check-hidden':'child')+'" name="' + c.id + '">'
					+'<input type="checkbox" name="cdt-source[' + grp + '][' + cdt + '][]" id="sos-' + c.id + grp +cdt + '" value="' + c.id + '" ' + (valCheck==c.id ? 'checked="checked"' : '') + '/>'
							+'<label for="sos-' + c.id + grp +cdt + '">'+c.name+'</label>'
							+'</a>';
				}
				if( in_opt ){
					optionsOrdSource += '</div>';
				}
				
				htmlSource += optionsOrdSource;
				htmlSource+='</div></div></div>';
			} else {
				htmlSource += '	<div class="div-source-cdt" style="display:none">';
				htmlSource += '		<select class="selectmenu cdt-source-cdt" name="cdt-source[' + grp + '][' + cdt + ']" id="cdt-source-' + grp + '-' + cdt + '"><option value="-1" selected="selected"></option></select>';
				htmlSource += '	</div>';
			}
			
			$('#cdt-type-' + grp + '-' + cdt).after( htmlSource );
		}
	});
}

/** 
 *	Cette fonction permet de charger les types de commande
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 *	@param idCdt Obligatoire, identifiant du critère sélectionné
 */
function segCriterionWebsitesLoad( grp, cdt, idCdt ){
	// $.getJSON('/admin/ajax/segments/ajax-segments.php?getWebsite=1&cls=' + segClsID + '&cdt=' + idCdt, function( website ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getWebsite=1&cls=' + segClsID + '&cdt=' + idCdt,
		dataType: 'JSON',
		async: false,
		success: function(website){
			if( $('#cdt-website-' + grp + '-' + cdt).length ){
				$('#cdt-website-' + grp + '-' + cdt).parent().remove();
			}
			
			var htmlSource  = '';
			if( website!=null && website.length ){
				var valCheck = valCopy!='' && valCopy.parents('.cdt-grp-list-cdt').find('.cdt-website-cdt') ? valCopy.parents('.cdt-grp-list-cdt').find('.cdt-website-cdt').val() : '';
				
				var w = false; 
				var optionsWebsite = ' <option value="-1">' + segmentsTous + '</option>';
				for( var i=0 ; i<website.length ; i++ ){
					w = website[i];
					optionsWebsite += '				<option ' + (valCheck==w.id ? 'selected="selected"' : '') + ' value="' + w.id + '">' + w.name + '</option>';
				}
				
				htmlSource += '<div class="div-website-cdt" ' + ( website.length>1 ? '' : 'style="display:none"' ) + '>';
				htmlSource += '<label for="cdt-website-' + grp + '-' + cdt + '">Site :</label>';
				htmlSource += '<select class="selectmenu cdt-website-cdt" name="cdt-website[' + grp + '][' + cdt + ']" id="cdt-website-' + grp + '-' + cdt + '">' + optionsWebsite + '</select>';
				htmlSource += '</div>';
			} else {
				htmlSource += '<div class="div-website-cdt" style="display:none">';
				htmlSource += '<select class="selectmenu cdt-website-cdt" name="cdt-website[' + grp + '][' + cdt + ']" id="cdt-website-' + grp + '-' + cdt + '"><option value="-1" selected="selected"></option></select>';
				htmlSource += '</div>';
			}
			
			$('#cdt-type-' + grp + '-' + cdt).after( htmlSource );
			// $('.selectmenu').selectmenu();
		}
	});
}

/** Cette fonction permet de charger les symbôles disponibles pour un critère de segmentation.
 * 	La disponibilité d'un symbôle est fonction du type de champ (texte, liste, date, etc...)
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 *	@param idCdt Obligatoire, identifiant du critère sélectionné
 */
function segCriterionSymbolsLoad( grp, cdt, idCdt, field ){
	if( !$('#cdt-symbol-' + grp + '-' + cdt).length ) return false;
	
	// $.getJSON('/admin/ajax/segments/ajax-segments.php?getSymbols=1&cdt=' + idCdt, function( symbols ){
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getSymbols=1&cdt=' + idCdt + '&field=' + field,
		dataType: 'JSON',
		async: false,
		success: function(symbols){
			var optionsSymbols = '<option value="-1"></option>';
			
			if( symbols.length ){
				for( var i=0 ; i<symbols.length ; i++ ){
					var s = symbols[i];
					var symbolCheck = symbolCopy!='' ? symbolCopy : '=';
					optionsSymbols += '<option ' + (symbolCheck==s.symbol ? 'selected="selected"' : '' ) + ' value="' + s.symbol + '">' + s.desc + '</option>';
				}
				
			}
			
			$('#cdt-symbol-' + grp + '-' + cdt).html( optionsSymbols );
		}
	});
	
	return false;
}

/** 
 *	Cette fonction permet de charger un formulaire dépendant du critère (champ texte, liste des service de livraison ou moyen de paiement, booléen...).
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de critère
 *	@param idCdt Obligatoire, identifiant du critère sélectionné
 */
function segCriterionFormLoad( grp, cdt, idCdt, field ){
	if( !$('#cdt-symbol-' + grp + '-' + cdt).length ) return false;
	
	$.ajax({
		url: '/admin/ajax/segments/ajax-segments.php',
		data: 'getForm=1&cdt=' + idCdt + '&field=' + field,
		dataType: 'JSON',
		async: false,
		success: function(forms){
			var htmlForm = '<div class="div-cdt-value">';
			
			var valHidden = valCopy!='' && valCopy.find('input[type=hidden]').length ? valCopy.find('input[type=hidden]').val() : '';
			var htmlButtonRemove = '<input type="button" onclick="return segCriterionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" title="' + segmentsSupprimerCritere + '" />';
			
			htmlForm += '<input type="hidden" value="' + valHidden + '" id="cdt-prd-search-key-' + grp + '-' + cdt + '" name="cdt-search-key[' + grp + '][' + cdt + ']" class="btn-action-small" />';
			if( forms.type != null && forms.type.length ){
				switch( parseInt(forms.type) ){
					case 3 :
						var valCheck = valCopy!='' && valCopy.find('.cdt-grp-value').length ? valCopy.find('.cdt-grp-value').val() : '';
						
						htmlForm += '<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][' + cdt + ']" value="' + valCheck + '" /></span>';
						htmlForm += htmlButtonRemove;
						htmlForm += '<sub>' + segmentsSubValeurSaisiEntier + '</sub>';
						
						break;
					case 4 : 
						var valCheck = valCopy!='' && valCopy.find('.cdt-grp-value').length ? valCopy.find('.cdt-grp-value').val() : '';
						
						htmlForm += '<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][' + cdt + ']" value="' + valCheck + '" /></span>';
						htmlForm += htmlButtonRemove;
						htmlForm += '<sub>' + segmentsSubValeurSaisiEntierFlottant + '</sub>';
						
						break;
					case 8 :
						var valCheck = valCopy!='' && valCopy.find('input:checked').length ? valCopy.find('input:checked').val() : '';
						
						htmlForm += '<div class="cdt-form-bool">';
						htmlForm += '<input ' + (valCheck==1 ? 'checked="checked"' : '') + ' type="radio" id="cdt-value-' + grp + '-' + cdt + '-y" name="cdt-value[' + grp + '][' + cdt + ']" value="1" />';
						htmlForm += '<label for="cdt-value-' + grp + '-' + cdt + '-y">Oui</label>';
						htmlForm += '<input ' + (valCheck==0 ? 'checked="checked"' : '') + ' type="radio" id="cdt-value-' + grp + '-' + cdt + '-n" name="cdt-value[' + grp + '][' + cdt + ']" value="0" />';
						htmlForm += '<label for="cdt-value-' + grp + '-' + cdt + '-n">Non</label>';
						htmlForm += '</div>';
						htmlForm += htmlButtonRemove;
						htmlForm += '<div class="clear"></div>';
						
						break;
					case 10 :
						var valCheck = valCopy!='' && valCopy.find('.datepicker').length ? valCopy.find('.datepicker').val() : '';
						
						htmlForm += '<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-prd-search datepicker" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][' + cdt + ']" value="' + valCheck + '" /></span>';
						htmlForm += htmlButtonRemove;
						htmlForm += '<sub>' + segmentsSubFomatDate + '</sub>';
						
						break;
					case 1 :
					case 5 :
					case 6 :
					case 11 :
					case 12 :
						if( forms.data.length ){
							var valCheck = valCopy!='' && valCopy.find('select option:selected').length ? valCopy.find('select option:selected').val() : '';
							
							htmlForm += '<select class="selectmenu cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][' + cdt + ']">';
							for( var i=0 ; i<forms.data.length ; i++ ){
								var d = forms.data[i];
								htmlForm += '<option ' + (valCheck==d.id ? 'selected="selected"' : '') + ' value="' + d.id + '">' + d.name + '</option>';
							}
							htmlForm += '</select>';
							htmlForm += htmlButtonRemove;
							
							break;
						}
					default :
						var valCheck = valCopy!='' && valCopy.find('.cdt-grp-prd-search').length ? valCopy.find('select option:selected').val() : '';
						
						htmlForm += '<span class="cdt-grp-value-span"><input type="text" class="cdt-grp-prd-search" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][' + cdt + ']" value="' + valCheck + '" /></span/>';
						switch( parseInt(idCdt) ){
							case 23 :
								htmlForm += '<input type="button" value="Rechercher" id="cdt-search-' + grp + '-' + cdt + '" name="cdt-search" class="btn-action-small cdt-search-client" />';
								break;						
							case 35 :
								htmlForm += '<input type="button" value="Rechercher" id="cdt-search-' + grp + '-' + cdt + '" name="cdt-search" class="btn-action-small cdt-search-client cdt-search-representant" />';
								break;						
							case 13 : 
							case 15 : 
							case 33 : 
								htmlForm += '<input type="button" value="Rechercher" id="cdt-search-' + grp + '-' + cdt + '" name="cdt-search" class="btn-action-small cdt-search-cat" />';
								break;
							case 12 : 
							case 14 :
							case 32 :
								htmlForm += '<input type="button" value="Rechercher" id="cdt-search-' + grp + '-' + cdt + '" name="cdt-search" class="btn-action-small cdt-search-prd" />';
								break;
						}
						
						htmlForm += htmlButtonRemove;
						break;
				}
				
			}
			
			htmlForm += '</div><div class="clear"></div>';
			$('#cdt-symbol-' + grp + '-' + cdt).parent().nextAll().remove();
			$('#cdt-symbol-' + grp + '-' + cdt).parent().after( htmlForm );
			$('#cdt-symbol-' + grp + '-' + cdt).parent().css('display', 'block');
			
			$('#cdt-symbol-' + grp + '-' + cdt).removeAttr('disabled');
			
			switch( parseInt(forms.type) ){
				case 1 :
					if( parseInt(idCdt)!=1 ){
						break;
					}
				case 8 :
					$('#cdt-symbol-' + grp + '-' + cdt).attr('disabled', 'disabled').find('option').eq(1).attr('selected', 'selected');
					break;
				case 10 :
					$('input.datepicker').each(function(){
						var temp = this ;
						
						// Implémente le sélecteur de date sur chacun d'entre eux.
						$(this).DatePicker({
							format:'d/m/Y',
							date: $(this).val(),
							current: $(this).val(),
							starts: 1,
							onChange: function(formated, dates){
								if(dates != 'Invalid Date'){
									$(temp).val(formated);
									$(temp).DatePickerHide();
								}
							}
						});
						
					});	
					break;
				default :
					$('#cdt-value-' + grp + '-' + cdt + '-y').parents('cdt-psy-val')
						.find('.cdt-psy').removeAttr('disabled');
					break;
			}
		}
	});
	
	return false;
}

/** 
 *	Cette fonction permet de supprimer un critères d'un groupe.
 */
function segCriterionsRemove( cdt ){
	if( !cdt.parents('.cdt-config').prev().length && cdt.parents('.cdt-config').next().length ){
		// suppression du séparateur OR du groupe suivant si on supprime le premier groupe (ne fonctionne pas)
		cdt.parents('.cdt-config').next('.cdt-separator-or').remove();
	}
	
	var parent = cdt.parents('.cdt-grp-list-cdt');
	cdt.parents('.cdt-config').next().remove();
	cdt.parents('.cdt-config').remove();
	parent.find('.cdt-config').first().find('.cdt-separator-or').html('&nbsp;')
}

function loadSegment(){
	if( segID<=0 ) return;
	
	$('.cdt-grp-cdt').each(function(){
		$(this).change(function(){
			var attrID = $(this).attr('id').replace('cdt-type-', '');
			var grp = attrID.substring( 0, attrID.indexOf('-') );
			var cdt = attrID.substring( attrID.indexOf('-')+1 );
			
			segCriterionWebsitesLoad( grp, cdt, $(this).val() );
			segCriterionOrderSourcesLoad( grp, cdt, $(this).val() );
			segCriterionDatesLoad( grp, cdt, $(this).val() );
			segCriterionSymbolsLoad( grp, cdt, $(this).val() );
			segCriterionFormLoad( grp, cdt, $(this).val() );
		});
	});

}

function reloadSelectorOrigin(){

	$('.selector .parent').each(function(){
		$(this).find('[type=checkbox]').prop("indeterminate", false).removeAttr('checked');
		
		var total_check = $(this).parent().find('.child [type=checkbox]').length;
		var total_is_checked = $(this).parent().find('.child [type=checkbox]:checked').length
		

		if( total_is_checked > 0 ){
			if( total_check != total_is_checked ){
				$(this).find('[type=checkbox]').prop( "indeterminate", true );
			}else{
				$(this).find('[type=checkbox]').attr('checked', 'checked');
			}
		}
	});
}

function reloadSelectorView(that){

	var $selector = $(that).closest('.selector');
	var $selectorview = $selector.parent().find('.selectorview').find('.view');
	
	var checked = $(that).closest('.selector').find('input:checked');
	var tmp = [];

	checked.each(function(string,el){
		var parent = $(el).parent();
		if(!parent.hasClass('parent'))
			tmp.push(parent.find('label').html())
	})
	var html = tmp.join(', ');

	if( html.length > 28 ){
		html = html.substring(0, 28) + '...';
	}

	$selectorview.html(html);

}

$(document).on('selectmenuchange', '.selectmenu', function(event, ui){
	$(ui.item.element[0]).closest('.selectmenu').val(ui.item.value);
	$(ui.item.element[0]).closest('.selectmenu').trigger("change");
	
	$('.selectmenu').selectmenu();
	$('.selectmenu').selectmenu('destroy');
	$('.selectmenu').selectmenu();
	
});

var currentAjaxRequest = false;
var currentCat = false;
var segClsID = 0;
$(document).ready(
	function(){
		$('#select-discount-type').on('change', function(){
			if ($(this).val()=='0') {
				$('#line-discount-tva').show();
			}else{
				$('#line-discount-tva').hide();
			}
		});
		$('input.datepicker').each(function(){
			var temp = this ;
			
			// Implémente le sélecteur de date sur chacun d'entre eux.
			$(this).DatePicker({
				format:'d/m/Y',
				date: $(this).val(),
				current: $(this).val(),
				starts: 1,
				onChange: function(formated, dates){
					if(dates != 'Invalid Date'){
						$(temp).val(formated);
						$(temp).DatePickerHide();
					}
				}
			});
			
		});	
		
		var c = 0;
		$('.cdt-grp').each(function(){
			nbGroupCdts[ c ] = 2;
			c++;
		});
		pmtGroupsAdd();
		
		segClsID = 0;
		$('.pmt-special, #tabpanel .stats-menu').hide();
		var selected = $('input.selected');
		
		$('.selected').removeClass('selected');
		$('#pmt-special').show();
		
		if( !selected.length || $.trim(selected.attr('name'))=='' ){
			var location = window.location.href;
			if( location.match(/#tabGeneral/) ){
				pmtOngletActived( 'tabGeneral' );
			} else if( location.match(/#tabProducts/) ){
				pmtOngletActived( 'tabProducts' );
			} else if( location.match(/#tabCustomers/) ){
				pmtOngletActived( 'tabCustomers' );
			} else if( location.match(/#tabStats/) ){
				pmtOngletActived( 'tabStats' );
			} else if( location.match(/#tabSerie/) ){
				pmtOngletActived( 'tabSerie' );
			} else if( location.match(/#tabFields/) ){
				pmtOngletActived( 'tabFields' );
			} else if( location.match(/#tabVariations/) ){
				pmtOngletActived( 'tabVariations' );
			} else {
				pmtOngletActived( 'tabGeneral' );
			}
		} else {
			pmtOngletActived( selected.attr('name') );
		}
		
		
		$('#gen-new-code').attr('disabled', 'disabled').addClass('disabled');

		$('#pmt-specials-product-import,#pmt-specials-customer-import').each(function () {
			riaSortable.create({
				'table': $(this),
				'url': undefined
			});
		});
	}
).delegate(
	'#export-codes', 'click', function(){
		window.location.href = '/admin/promotions/specials/export-codes.php?type=' + typePromotion + '&' + $('#export-codes').parents('form').serialize();
		return false;
	}
).delegate(
	'input.saveAll', 'click', function(){
		var res = window.confirm(pmtSpecialsConfirmCreationChequiers);
		if( res ){
			pmtPromotionsSave( $(this) );
		}
		return false;
	}
).delegate(
	'.check-all', 'click', function(){
		$('.pmt-list-service input').attr('checked', 'checked');
		return false;
	}
).delegate(
	'.uncheck-all', 'click', function(){
		$('.pmt-list-service input').removeAttr('checked');
		return false;
	}
).delegate(
	'.check-all-wst', 'click', function(){
		$('.pmt-list-wst input').attr('checked', 'checked');
		return false;
	}
).delegate(
	'.uncheck-all-wst', 'click', function(){
		$('.pmt-list-wst input').removeAttr('checked');
		return false;
	}
).delegate(
	'#used-max', 'keyup', function(){
		var Nbr = $(this).val();
		
		$('.error').remove();
		$('#used-max-apply').removeAttr('checked');
		
		if( parseInt(Nbr)!='NaN' && Nbr>0 ){
			$('#used-max-apply').attr('checked', 'checked');
		} else if( $.trim(Nbr)!='' ) {
			$('#used-max-apply').before('<div class="error">' + pmtSpecialsErreurSaiseNumeric + '</div>');
		}
	}
).delegate(
	'.hour', 'keyup', function(){
		if( $(this).val().length==2 ){
			$(this).val( $(this).val() + ':' );
		}
	}
).delegate(
	'#gen-code', 'click', function(){
		return pmtGeneratedCode();
	}
).delegate(
	'#save, .tabstrip li', 'click', function(){
		return pmtPromotionsSave( $(this) );
	}
).delegate(
	'.tabstrip input', 'click', function(){
		$('.error, .success').remove();
		var name = $(this).attr('name');
		return pmtOngletActived( name );
	}
).delegate(
	'#pmt-qte-gen', 'keyup', function(){
		var qty = $(this).val();
		
		if( $.isNumeric(qty) ){
			$('#gen-new-code').removeAttr('disabled').removeClass('disabled');
		} else {
			$('#gen-new-code').attr('disabled', 'disabled').addClass('disabled');
		}
	}
).delegate(
	'#add-nb', 'click', function(){ 
		pmtReducProductsAdd(); 
	}
).delegate(
	'.pmt-delete', 'click', function(){
		if( !$(this).parent().next().length ){
			if( $(this).parent().prev().find('.pmt-delete').length ){
				$(this).parent().prev().find('.pmt-delete').before( '<input class="btn-action-small" type="button" name="add-nb" id="add-nb" value="+" />' );
			} else {
				$(this).parent().prev().append( '<input class="btn-action-small" type="button" name="add-nb" id="add-nb" value="+" />' );
			}
		}
		$(this).parent().remove();
	}
).delegate(
	'.cdt-grp-del', 'click', function(){
		// retire la règle entre groupe si la suppression se fait sur le premier groupe
		if( !$(this).parents('fieldset').prev().length && $(this).parents('fieldset').next().length ){
			$(this).parents('fieldset').next().find('.cdt-grp-rule').remove();
		}
		
		// supprime le groupe
		$(this).parents('fieldset').remove();
		
		// met à jour le label du groupe
		var nbGrp = $('.add-pmt-cdt fieldset').length;
		for( var i=0 ; i<nbGrp ; i++ ){
			$('.add-pmt-cdt fieldset:eq(' + i + ') label').html( 'Groupe ' + (i+1) + ' - Valide');
		}		
		
		if( !$('.add-pmt-cdt fieldset').length ){
			nbGroups = 0;
		}
	}
).delegate(
	'#prd-search', 'click', function(){
		displayPopup( 'Rechercher un produit', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&cat=' + currentCat );
	}
).delegate(
	'#export', 'click', function(){
		window.location.href = 'export-stats-promotions.php?cod=' + idCod;
	}
).delegate(
	'.select-obj-seg', 'change', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var option = $(this).find('option:selected');
		if( option.val()=='-1' ) return false;
		var url = '/admin/ajax/segments/ajax-segments.php?addObjSeg=' + option.val();
		
		url += '&clsID=' + ($('#seg-obj-cls').length ? parseInt($('#seg-obj-cls').val())  : 0 );
		url += '&obj0=' + ($('#seg-obj-id-0').length ? parseInt($('#seg-obj-id-0').val()) : 0 );
		url += '&obj1=' + ($('#seg-obj-id-1').length ? parseInt($('#seg-obj-id-1').val()) : 0 );
		url += '&obj2=' + ($('#seg-obj-id-2').length ? parseInt($('#seg-obj-id-2').val()) : 0 );
		
		$.getJSON( url, function( result ){
			if( result==null ) return false;
			if( result.success ){
				$('.seg-obj-infos').html( result.data );
				$('.select-obj-seg option').removeAttr('selected');
				$('.select-obj-seg option').eq(0).attr('selected', 'selected');
			}
		});
		
		option.remove();
	}
).delegate(
	'.del-obj-seg', 'click', function(){
		if( typeof segClsID == 'undefined' && segClsID>0 ){
			return false;
		}
		
		var segID = $(this).attr('name').replace('del-seg-', '');
		var url = '/admin/ajax/segments/ajax-segments.php?delObjSeg=' + segID;
		
		url += '&clsID=' + ($('#seg-obj-cls').length ? parseInt($('#seg-obj-cls').val())  : 0 );
		url += '&obj0=' + ($('#seg-obj-id-0').length ? parseInt($('#seg-obj-id-0').val()) : 0 );
		url += '&obj1=' + ($('#seg-obj-id-1').length ? parseInt($('#seg-obj-id-1').val()) : 0 );
		url += '&obj2=' + ($('#seg-obj-id-2').length ? parseInt($('#seg-obj-id-2').val()) : 0 );
		
		$.getJSON( url, function( result ){
			if( result==null ) return;
			if( result.success ){
				$('.seg-obj-infos').html( result.data );
				$('.select-obj-seg').html( result.option );
			}
		});
		
		return false;
	}
).delegate(
	'#gen-new-code', 'click', function(){
		$('.error, .success').remove();
		generatedPromotions( idCod, '' );
		reloadPromotionsSerie();
		
		$('#pmt-qte-gen').val( '' );
		return false;
	}
).delegate(
	'.upd-pattern', 'click', function(){
		displayPopup( pmtSpecialsModifieePatternPromo, '', '/admin/promotions/specials/popup-pattern.php', '', 625, 420 );
		return false;
	}
).delegate(
	'#selectoractivated .selectorview', 'click', function(){
		if($('#selectoractivated .selector').css('display')=='none'){
			$('#selectoractivated .selector').show();
		}else{
			$('#selectoractivated .selector').hide();
		}
	}
).delegate(
	'#selectoractivated .selector a', 'click', function(){
		var wstID = $('#wst_id').val();
		window.location.href = '/admin/promotions/specials/index.php?type=' + typePromotion + '&active=' + $(this).attr('name').replace('a-', '') + '&wst_id=' + wstID;
	}
).delegate(
	'#riawebsitepicker .selectorview', 'click', function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	}
).delegate(
	'#riawebsitepicker .selector a', 'click', function(){
		var active = $('#active').val();
		window.location.href = '/admin/promotions/specials/index.php?type=' + typePromotion + '&wst_id=' + $(this).attr('name').replace('w-', '') + '&active=' + active;
	}
).delegate(
	'.cdt-grp-cdt', 'change', function(){
		var tempID = $(this).attr('id').substring(9);
		var grp = tempID.substring( 0, tempID.indexOf('-') );
		var cdt = tempID.substring( tempID.indexOf('-')+1 );

		pmtConditionSymbolsLoad( grp, cdt, $(this).val() );
		pmtConditionFormLoad( grp, cdt, $(this).val() );
	}
).delegate('.pmt-rules-prd-import', 'click', importRulesProducts)
.delegate('.pmt-rules-usr-import', 'click', importRulesCustomers);

/**
 * Cette fonction permet l'affichage ou non du bouton d'export de code promo, le boutton s'affiche si au moins une promo est sélèctionné
 */
function pmtToggleExportAction(){
	if( $('#export-codes').parents('form').find('input:checked').length > 0 ){
		$('#export-codes').removeClass('none');
	}else{
		$('#export-codes').addClass('none');
	}
}
/**
 *	Cette fonction permet d'activer un onglet
 */
function pmtOngletActived( name ){		
		$('.pmt-special, #tabpanel .stats-menu').hide();
		$('.selected').removeClass('selected');
		$('.tabstrip input[name=' + name + ']').addClass('selected');
		
		segClsID = 0;
		switch( name ){
			case 'tabGeneral' :
				$('#pmt-special').show();
				break;
			case 'tabProducts' :
				$('#tb-tabProducts').show();
				pmtLoadAllCatalog();
				break;
			case 'tabCustomers' :
				segClsID = 36;
				$('#tb-tabCustomers').show();
				pmtLoadAllCustomers();
				break;
			case 'tabStats' :
				$('.tb-tabStats, #tabpanel .stats-menu').show();
				break;
			case 'tabSerie' :
				reloadPromotionsSerie( 1 );
				$('#tb-tabSerie').show();
				break;
			case 'tabFields' :
				$('#tb-tabFields').show();
				break;
			case 'tabVariations' :
				$('#tb-tabVariations').show();
				break;
		}
		
		window.location.hash  = name;
		return false;
}

/**
 *	Cette fonction permet de charger les informations de l'onglet "Clients"
 */
function pmtLoadAllCustomers(){
	
	// mise à jour de l'affichage pour savoir si tous les comptes client sont exclus ou inclus
	if( idCod ){
		$.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?getallcustomers=1&idCod=' + idCod, function( result ){
			$('input[name=pmt-all-customers]').removeAttr('checked')
			parseInt(result.all)==1 ? $('#all-customers-1').attr('checked', 'checked') : $('#all-customers-0').attr('checked', 'checked');
		});
	}
	
	$('#pmt-prf-name, #pmt-seg-name').change(function(){
		$('input[name=pmt-add-rule]').removeAttr('checked');
		
		$('#elem-id-user, #elem-type-user').val('');
		
		$(this).parent().find('input[name=pmt-add-rule]').attr('checked', 'checked');


		$('#elem-type-user').val('');
		if( $('input[name=pmt-add-rule]:checked').val() == 'prf' ){
			$('#elem-type-user').val( 'prf' );
			$('#elem-id-user').val( $('#pmt-prf-name').val() );
		}
		if( $('input[name=pmt-add-rule]:checked').val() == 'seg' ){
			$('#elem-type-user').val( 'seg' );
			$('#elem-id-user').val( $('#pmt-seg-name').val() );
		}
	});
	
	$('#pmt-usr-name').focus(function(){
		displayPopup( pmtSpecialsSelectCompteClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1' );
	});
	
	$('#pmt-usr-select').click(function(){ $('#pmt-usr-name').focus(); });
	loadRulesCustomers();
}

/** 
 *	Cette fonction permet de sauvegarder un règle d'inclusion ou d'exclusion pour l'onglet "Clients".
 */
function addRulesCustomers( include ){
	if( currentAjaxRequest ){
		currentAjaxRequest.abort();
	}

	var id = $('#elem-id-user').val();
	var type = $('#elem-type-user').val();
	
	if( !$.trim(type) || !$.trim(id) ){
		pmtMessage(pmtSpecialsChoisirTypeCompte, true);
		return false;
	}
	
	include = include ? 1 : 0;
	
	currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?addruleCustomers=1&idCod=' + idCod + '&type=' + type + '&cnt=' + id + '&include=' + include, function(){
		currentAjaxRequest = false;
		loadRulesCustomers();
		updateCountCustomers();
	});
	
	return false;
}

/**
 *	Cette fonction permet de charger la zone affichant les inclusions ou exclusion.
 */
function loadRulesCustomers(){
	currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?getincludecustomers=1&idCod=' + idCod, function( result ){
		var rules = rows = '';
		var type = cnts = cnt = false;
		
		for( type in result ){
			
			var cnts = result[type];

			for( cnt in cnts ){
				var c 	 = cnts[ cnt ];

				rows += '<tr>';
				rows += '	<td headers="inc-check"><input '+ (result['can-edit'] ? '' : 'disabled') +' name="del[]" class="checkbox" type="checkbox" id="u' + type + '-' + c.id + '" value="' + type + '-' + c.id + '"></td>'
				rows += '	<td headers="inc-rule" data-label="Exceptions : "><label for="u' + type + '-' + c.id + '">(' + c.include + ') ' + htmlspecialchars(c.name) + '</label></td>';
				rows += '</tr>';
			}
			
		}
		
		if( rows!='' ){
			rules += '<table id="pmt-rules" class="pmt-rules">';
			rules += '	<thead><tr>';
			rules += '		<th id="inc-check" data-label="Tout cocher : "><input '+ (result['can-edit'] ? '' : 'disabled') +' class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox"></th>';
			rules += '		<th id="inc-rule" class="thead-none" colspan="2">Exceptions</th>';
			rules += '	</tr></thead>';
			rules += '	<tbody>' + rows + '</tbody>';
			rules += '	<tfoot>';
			rules += '		<tr><td colspan="3">';
			rules += '			<sub class="btn-move">' + pmtSpecialsSubSymbole + '</sub>';
			rules += '			<input '+ (result['can-edit'] ? '' : 'disabled') +' onclick="return delRulesCustomers();" type="submit" value="' + pmtSpecialsSupprimer + '" name="pmt-del-rule" class="button" />';
			rules += '		</td></tr>';
			rules += '	</tfoot>';
			rules += '</table>';
			$('#pmt-list-rules-user').html( rules );
		} else {
			$('#pmt-list-rules-user').html( '<div class="notice">' + pmtSpecialsAucuneException + '</div>' );
		}
		
		currentAjaxRequest = false;
	});
	
	$('#pmt-add-rule-prf, #pmt-add-rule-usr').removeAttr('checked', 'checked');
	$('#pmt-prf-name option').removeAttr('selected');
	$('#pmt-usr-name').val( '' );
	return false;
}

function updateCountCustomers(){
	$.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?getcountcustomers=1&idCod=' + idCod, function( result ){
		$('.customers-count-pmt').html( result.count );
	});
}

/** 
 *	Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion.
 */
function delRulesCustomers(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	var dateCusDel = '';
	var i = 0;

	$('#pmt-rules [type="checkbox"]:checked').each(function(){
		
		var attr = $(this).val();
		var cnt = attr.substring( 4 );
		var type = attr.substring( 0, 3 );

		dateCusDel += '&type[' + i + ']=' + type + '&cnt[' + i + ']=' + cnt;
		i++;
	});

	if( $.trim(dateCusDel) != '' ){
		currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?delrulecustomers=1&idCod=' + idCod + dateCusDel, function(){
			currentAjaxRequest = false;
			loadRulesCustomers();
		});
	}
	
	return false;
}

/**
 *	Cette fonction permet de sauvegarder l'information "Tous le catalogue inclu / exclu".
 */
function saveRulesCustomers(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	var all = $('#all-customers-1').is(':checked') ? 1 : 0;

	currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?saveallcustomers=1&idCod=' + idCod + '&all=' + all, function( result ){
		if( result.done ){
			pmtMessage( pmtSpecialsInfoMaj, false );
		} else {
			pmtMessage( pmtSpecialsErreurEnregistremetnInfos, true );
		}
		currentAjaxRequest = false;
		updateCountCustomers();
	});
	
	return false;
}

function parent_select_user( id, email ){
	$('#pmt-add-rule-prf, #pmt-add-rule-usr').removeAttr('checked');
	$('#pmt-usr-name').val( email );
	$('#elem-id-user').val( id );
	$('#elem-type-user').val( 'usr' );
	$('#pmt-add-rule-usr').attr('checked', 'checked');
}
/** 
 * Cette fonction permet de charger les informations de l'onglet "Produits"
 */
function pmtLoadAllCatalog(){
	if( idCod ){
		// mise à jour de l'affichage pour savoir si tout le catalogue est inclu ou exclu
		$.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?getallcatalog=1&idCod=' + idCod, function( result ){
			$('#pmt-all-catalog-0, #pmt-all-catalog-1').removeAttr('checked');
			parseInt(result.all)==1 ? $('#pmt-all-catalog-1').attr('checked', 'checked') : $('#pmt-all-catalog-0').attr('checked', 'checked');
			parseInt(result.include_pmt)==1 ? $('#include_pmt').attr('checked', 'checked') : $('#include_pmt').removeAttr('checked');
			parseInt(result.only_destock)==1 ? $('#only_destock').attr('checked', 'checked') : $('#only_destock').removeAttr('checked');
		});
	}
	
	// ouverture de la popup de sélection selon le contenu recherché
	$('#pmt-add-rule input.text:not(#pmt-prd-name), #pmt-add-rule input.ref:not(#pmt-prd-name)').focus(function(){
		switch( $(this).attr('name') ){
			case 'pmt-prd-name' :
				displayPopup( pmtSpecialsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0' );
				break;
			case 'pmt-cat-name' :
				displayPopup( pmtSpecialsSelectCategorie, '', '/admin/catalog/popup-categories.php' );
				break;
			case 'pmt-brd-name' : 
				displayPopup( pmtSpecialsSelectMarque, '', '/admin/ajax/catalog/popup-brands-select.php' );
				break;
			case 'pmt-set1' :
			case 'pmt-set2' :
				$('#pmt-add-rule .radio').removeAttr('checked');
				$('#pmt-add-rule-set').attr('checked', 'checked');
				$('#elem-type-prd').val( 'set' );
				break;
		}
	});
	
	// affecte le focus à la bonne zone selon le contenu recherché
	$('#pmt-brd-select, #pmt-add-rule-brd').click(function(){ $('#pmt-brd-name').focus(); });
	$('#pmt-cat-select, #pmt-add-rule-cat').click(function(){ $('#pmt-cat-name').focus(); });
	$('#pmt-ref-select, #pmt-add-rule-prd').click(function(){ 
		return displayPopup( pmtSpecialsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0' ); 
	});
	
	$('#pmt-prd-name').focus(function(){
		$('#pmt-add-rule .radio').removeAttr('checked');
		$('#pmt-add-rule-prd').attr('checked', 'checked');
		$('#elem-type-prd').val('prd');
	}).focusout(function(){
		// $('#pmt-add-rule-prd').removeAttr('checked');
		// $('#elem-type').val('');
	});
	loadRulesProducts();
}

/**
 *	Cette fonction permet de sélectionner le produit offert.
 */
function parent_select_prd( id, name, ref, cat, input_id, input_name, input_ref ){
	currentCat = cat;
	if( $.trim(input_id)!='' ){
		$('#' + input_id).val(id);
		$('#' + input_name).val(name);
		$('#' + input_ref).val(ref);
		$('#pop-ref').val( ref );
	}else{
		return chooseElemProducts( id, ref + ' - ' + name, 'pmt-prd-name' );
	}
}

/**
 *	Cette fonction permet de gérer la sélection d'un contenu à inclure ou exclure dans le système depuis sa popup.
 *	@param id Obligatoire, identifiant du contenu
 *	@param name Obligatoire, nom du contenu
 *	@param zoneID Obligatoire, identifiant du champ input[type=text] où le nom doit être affiché
 */
function chooseElemProducts( id, name, zoneID ){
	$('#pmt-add-rule .radio').removeAttr('checked');
	$('#pmt-add-rule input[type=text]').val( '' );
	$('#elem-id-prd').val( '' );
	
	var type = false;
	switch( zoneID ){
		case 'pmt-brd-name': type = 'brd'; break;
		case 'pmt-cat-name': type = 'cat'; break;
		case 'pmt-prd-name': type = 'prd'; break;
	}
	$('#elem-type-prd').val( type );
	
	// selection du bouton radio
	$('#' + zoneID).parent().find('input[type=radio]').attr('checked', 'checked');
	
	// gestion de l'affichage + enregistrement de l'identifiant du contenu
	$('#elem-id-prd').val( id );
	$('#' + zoneID).val( name );
	
	hidePopup();
	return false;
}

/** 
 *	Cette fonction permet d'ajouter une règle d'inclusion ou d'exclusion.
 *	@param include Obligatoire, booléen indiquant s'il s'agit d'une inclusion (True) ou d'une exclusion (False)
 */
function addRulesProducts( include ){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();

	var id = $('#elem-id-prd').val();
	var type = $('#elem-type-prd').val();
	
	if( !$.trim(type) || (type!='set' && type!='prd'  && !$.trim(id)) ){
		pmtMessage(pmtSpecialsChoisirProduit, true);
		return false;
	}
	
	if( type=='set' && (!$.trim($('#pmt-set1').val()) || !$.trim($('#pmt-set2').val())) ){
		pmtMessage(pmtSpecialsRenseignerPlageReference, true);
		return false;
	}
	
	if( type=='set' ){
		id = $('#pmt-set1').val() + ';~;' + $('#pmt-set2').val();
	} else if( type=='prd' && $.trim(id)=='' ){
		id = 'ref-' + $('#pmt-prd-name').val();
	}
	
	include = include ? 1 : 0;
	
	currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?addrule=1&idCod=' + idCod + '&type=' + type + '&cnt=' + id + '&include=' + include, function(){
		currentAjaxRequest = false;
		loadRulesProducts();
	});
	
	return false;
}

/** 
 *	Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion.
 */
function delRulesProducts(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	var dataPrdDel = '';
	var i = 0;

	$('#pmt-rules [type="checkbox"]:checked').each(function(){
		
		var attr = $(this).val();
		var cnt = attr.substring( 4 );
		var type = attr.substring( 0, 3 );

		dataPrdDel += '&type[' + i + ']=' + type + '&cnt[' + i + ']=' + cnt;
		i++;
	});

	if( $.trim(dataPrdDel) != '' ){
		currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?delrule=1&idCod=' + idCod + dataPrdDel, function(){
			currentAjaxRequest = false;
			loadRulesProducts();
		});
	}
	
	return false;
}

function importRulesProducts(event) {
	event.preventDefault();

	displayPopup(
		pmtSpecialsImporteAutrePromo,
		'',
		'/admin/promotions/specials/import/products.php' + window.location.search,
		'(function(){loadRulesProducts(); hidePopup();})()'
	);
}

function importRulesCustomers(event) {
	event.preventDefault();

	displayPopup(
		pmtSpecialsImporteAutrePromo,
		'',
		'/admin/promotions/specials/import/customers.php' + window.location.search,
		'(function(){loadRulesCustomers(); hidePopup();})()'
	);
}

/**
 *	Cette fonction permet de charger la zone affichant les inclusions ou exclusion.
 */
function loadRulesProducts(){
	currentAjaxRequest = $.getJSON( '/admin/ajax/promotions/ajax-pmt-specials.php?getincludeprd=1&idCod=' + idCod, function( result ){
		var typePromotion = $('#type-promotion').val();

		var rules = rows = prds = '';
		var type = cnts = cnt = false;


		for( type in result ){
			
			cnts = result[type];
			
			for( cnt in cnts ){
				if( cnt == 'col' ){
					continue;
				}
				var c 	 = cnts[ cnt ];

				var prds = type != 'prd' ? '(' + c.prds + ' produit' + ( parseInt(c.prds)>1 ? 's' : '' ) + ')' : '';
				var colspan = typePromotion == 9 || typePromotion == 10 ? 1 : 2;

				rows += '<tr>';
				
				var include = 1;
				if( c.include == '-' ){
					include = 0;
				}

				if( type == 'prd' ){
					rows += '	<td headers="inc-check"><input '+ (result['can-edit'] ? '' : 'disabled') +' name="del[]" class="checkbox" type="checkbox" value="' + type + '-' + c.id + '"></td>'
					rows += '	<td colspan="' + colspan + '" headers="inc-rule" data-label="Exceptions : ">';
					rows +=	'		<a href="' + c.url + '">(' + c.include + ') ' + htmlspecialchars(c.name) + ' ' + prds + '</a>';
					if( cnts['col'] != null && cnts['col'].length && displayColisage){
						rows += '	<table class="tb-colisage" >';
						rows += '		<tbody id="'+c['id']+'">'
						if( c.prd_col.length ){
							rows += '		<tr>';
							rows += '			<td>' + pmtSpecialsConditionnement + '</td>';
							var first = true;
							for( var i=0; i<c.prd_col.length; i++ ){
								if( !first ){
									rows += '<tr>';
									rows +=	'	<td></td>';
								}
								rows += '		<td><input onclick="return unlinkPrdCol('+c.prd_col[i].pmt_id+', '+c.prd_col[i].prd_id+' ,'+c.prd_col[i].col_id+', '+include+');" type="image" src="../../images/del-cat.svg" width="16" height="16" class="icon-del-cat" /> '+c.prd_col[i].name;
								if(typeof c.prd_col[i].qte !== 'undefined') { 
									rows += ' (Qté: '+parseInt(c.prd_col[i].qte)+')';
								}
								rows += '</td>';
								rows += '	</tr>';
								first = false;
							}
						}else{
							rows += '		<tr>';
							rows += '			<td>' + pmtSpecialsConditionnement + '</td><td><span style="margin-left:5px">' + pmtSpecialsToutTypeConditionnement + '</span></td>';
						}
						rows +=	'			<tr>';
						rows += ' 		</tbody>'
						rows += '		<tbody>'
						rows += '			<tr>';
						rows += '				<td>Ajouter :</td>';
						rows += '				<td>';
						rows +='					<select id="colisage-'+c.id+'" class="select-colisage" onChange="return linkPrdCol('+c.id+', '+include+')">';
						rows += '						<option value="-1">' + pmtSpecialsChoisirConditionnement + '</option>'
						rows += '						<option value="0">' + pmtSpecialsunite + '</option>';
						for( var col in cnts['col'] ){
							rows += '					<option value="'+cnts['col'][col]['id']+'">' +cnts['col'][col]['name']+ ' ( Qté: '+parseInt(cnts['col'][col]['qte'])+')</option>';
						}
						rows += '					</select>';
						rows += '				</td>';
						rows += '			</tr>';
						rows += '		</tbody>';
						rows += '	</table>';
					}
					
					rows += '	</td>';
				}else if( type != 'set' ){
					rows += '	<td headers="inc-check"><input '+ (result['can-edit'] ? '' : 'disabled') +' name="del[]" class="checkbox" type="checkbox" value="' + type + '-' + c.id + '"></td>'
					rows += '	<td colspan="' + colspan + '" headers="inc-rule" data-label="Exceptions : "><a href="' + c.url + '">(' + c.include + ') ' + htmlspecialchars(c.name) + ' ' + prds + '</a></td>';
				}else{
					rows += '	<td headers="inc-check"><input '+ (result['can-edit'] ? '' : 'disabled') +' name="del[]" class="checkbox" type="checkbox" id="r' + type + '-' + c.id + '" value="' + type + '-' + c.id + '"></td>'
					rows += '	<td colspan="' + colspan + '" headers="inc-rule" data-label="Exceptions : "><label for="r' + type + '-' + c.id + '">(' + c.include + ') ' + htmlspecialchars(c.name) + ' ' + prds + '</label></td>';
				}

				if( typePromotion == 9 || typePromotion == 10 ){
					rows += '	<td headers="inc-perso" data-label="Réduction : ">';
					if (c.include != '-') {
						rows += '	<input '+ (result['can-edit'] ? '' : 'disabled') +' type="text" value="' + c.discount + '" maxlength="9" class="price" id="prd-discount-' + c.id + '-' + type + '" name="prd-discount[' + type + '][' + c.id + ']" />';
						rows += '	<select '+ (result['can-edit'] ? '' : 'disabled') +' name="prd-discount-type[' + type + '][' + c.id + ']" id="select-discount-type-' + c.id + '-' + type + '">';
						rows += '		<option value=""></option>';
						rows += '		<option ' + ( c.discountType == '0' ? 'selected="selected"' : '' ) + ' value="0">€ HT</option>';
						rows += '		<option ' + ( c.discountType == '1' ? 'selected="selected"' : '' ) + ' value="1">%</option>';
						rows += '		<option ' + ( c.discountType == '2' ? 'selected="selected"' : '' ) + ' value="2">' + pmtSpecialsuniteNouveauTarif + '</option>';
						rows += '	</select>';
					}
					rows += '</td>';
				}

				rows += '</tr>';
			}
			
		}
		
		if( rows != '' ){
			// rules = '	<select multiple="multiple" size="8" id="pmt-rules" name="pmt-rules[]">' + rules + '</select>';
			rules += '<table id="pmt-rules" class="pmt-rules">';
			rules += '	<thead><tr>';
			rules += '	<th id="inc-check" data-label="Tout cocher : "><input '+ (result['can-edit'] ? '' : 'disabled') +' class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox"></th>';
			
			if( typePromotion == 9 || typePromotion == 10 ){
				rules += '	<th id="inc-rule" class="thead-none">' + pmtSpecialsExceptions + '</th>';
				rules += '	<th id="inc-perso" class="thead-none">' + pmtSpecialsReductions + '</th>';
			}else{
				rules += '	<th id="inc-rule"  class="thead-none" colspan="2">' + pmtSpecialsExceptions + '</th>';
			}

			rules += '	</tr></thead>';
			rules += '	<tbody>' + rows + '</tbody>';
			rules += '	<tfoot><tr><td colspan="3"><sub class="btn-move">' + pmtSpecialsSubSymboleProduits + '</sub><input '+ (result['can-edit'] ? '' : 'disabled') +' onclick="return delRulesProducts();" type="submit" value="Supprimer" name="pmt-del-rule" class="button float-right" /></td></tr></tfoot>';
			rules += '</table>';
			
			$('#pmt-list-rules-prd').html( rules );
		} else {
			$('#pmt-list-rules-prd').html( '<div class="notice">' + pmtSpecialsAucuneException + '</div>' );
		}
		
		currentAjaxRequest = false;
	
	});
	return false;
}

/**
 *	Cette fonction permet de sauvegarder l'information "Tous le catalogue inclu / exclu".
 */
function saveRulesProducts(){
	$('.page-load-block').show();
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	var all = $('#pmt-all-catalog-1').is(':checked') ? 1 : 0;
	var includePmt = $('#include_pmt').is(':checked') ? 1 : 0;
	var includeDestock = $('#only_destock').is(':checked') ? 1 : 0;

	var arPOST = new Array();

	var i = 1;
	var tempPOST = '';
	$('[headers=inc-perso]').each(function(){
		if( i > 100 ){
			arPOST.push( tempPOST );
			i = 1;
			tempPOST = '';
		}

		tempPOST += '&' + $(this).find('input, select').serialize();
		i++;
	});

	if( i >= 1 ){
		arPOST.push( tempPOST );
	}

	var error = false;
	for( var post in arPOST ){
		currentAjaxRequest = $.ajax({
			url: '/admin/ajax/promotions/ajax-pmt-specials.php',
			data: 'saveallcatalog=1&idCod=' + idCod + '&all=' + all + '&include_pmt=' + includePmt + '&only_destock=' + includeDestock + arPOST[post],
			type: 'post',
			dataType: 'json',
			async: false,
			success: function( result ){
				if( !result.done ){
					error = true;
				}
			}
		});
	}

	if( !error ){
		pmtMessage( pmtSpecialsInfoMaj, false );
	}else{
		pmtMessage( pmtSpecialsErreurEnregistremetnInfos, true );
	}
	
	$('.page-load-block').hide();
	return false;
}

/** Fonction utilisé par la popup pour la sélection d'un élément à inclure ou exclure du système. */
function parent_select_brd( id, name ){ return chooseElemProducts( id, name, 'pmt-brd-name' ); }
function updateCat( id, idParent, name ){ return chooseElemProducts( id, name, 'pmt-cat-name' ); }
function close_cat_popup(){ hidePopup(); }

function pmtMessage( msg, error, input ){
	$('.success, .error').remove();
	
	if( msg==undefined )
		return false;
	
	input = input!=undefined ? input : 'tabpanel';
	
	$('#' + input).prepend( '<div class="' + ( error ? 'error' : 'success' ) + '">' + msg + '</div>' );
	$(window).scrollTop(0);
	return false;
}

/** Cette fonction permet de sauvegarder la promotion.
 *	@param inPut 
 */
function pmtPromotionsSave( inPut ){
	if( $('.tabstrip li input.selected').attr('name')=='tabGeneral' || (typePromotion==7 && idCod==0) ){
		$('.error, .success').remove();
		
		$('#list-prd-offers option').attr('selected', 'selected');
		var attrName = inPut.find('input').attr('name');
		var params = '?savePmt=1&idCod=' + idCod + '&typePromo=' + typePromotion + '&' + inPut.parents('form').serialize();
		$('#list-prd-offers option').removeAttr('selected');

		$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php' + params, 
			function( result ){
				if( !result ){
					return false;
				}
				
				if( result.error && result.error.length ){
					$('#pmt-special').before( '<div class="error">' + result.error + '</div>' );
					$('html').scrollTop(0);
				} else if( result.cod.length ){
					if( result.cod.length ){
						var location = '/admin/promotions/specials/edit.php?id=' + result.cod + '&type=' + typePromotion;
						
						if( attrName!=undefined ){
							var tab = 'general';
							if( attrName=='tabProducts' ){ tab = 'products'; }
							else if( attrName=='tabCustomers' ){ tab = 'customers'; }
							else if( attrName=='tabStats' ){ tab = 'stats'; }
							location += '&tab='+tab;
						}
						
						if( idCod==0 && typePromotion==2 ){ // Bons d'achat
							// Génération des bons d'achat
							generatedPromotions( result.cod, location );
						} else {
							window.location.href = location;
						}
					}
				}
			}
		);
		
		return false;
	}
	
	return true;
}

/**
 *	Cette fonction permet de générer une quantité de promotion.
 */
function generatedPromotions( parent, location ){
	var qty = $('#pmt-qte-gen').val();
	
	if( !$.isNumeric(qty) || qty<=0 ){
		$('#pmt-special').before( '<div class="error">' + pmtSpecialsErreurSaisieQuantite + '</div>');
		return false;
	}
	
	var params = '?genBA=1&parent=' + parent + '&qty=' + qty + '&typePromo=' + typePromotion;
	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php' + params, 
		function( result ){
			if( result.error && result.error.length ){
				$('#pmt-special').before( '<div class="error">' + result.error + '</div>' );
				$('html').scrollTop(0);
			} else if( $.trim(location)!='' ){
				window.location.href = location;
			} else {
				// Affichage d'un message de succès
				$('#pmt-special').before( '<div class="success">' + pmtSpecialsGenereCode + '</div>');
				pmtOngletActived( 'tabSerie' );
			}
		}
	);
	
	return false;
}

/**
 *	Cette fonction permet de rajouter une ligne de remise produit dégrassive.
 */
function pmtReducProductsAdd(){
	$('#add-nb').remove();
	
	var newReduc = '';
	newReduc += '					<div class="reduc-nbr">';
	newReduc += '						<input type="text" class="qte-reduc" name="get[]" id="get' + nbReducProduct + '" value="" />';
	newReduc += '						&nbsp;<label for="get' + nbReducProduct + '">' + pmtSpecialsSurLe + '</label>';
	newReduc += '						<input type="text" class="qte-reduc" name="nb[]" id="nb' + nbReducProduct + '" value="" />';
	newReduc += '						&nbsp;<label for="nb' + nbReducProduct + '">' + pmtSpecialsEme + '</label>';
	newReduc += '						<input class="btn-action-small" type="button" name="add-nb" id="add-nb" value="+" />';
	newReduc += '						<input class="pmt-delete input-icon-del" type="button" name="del-nb" id="del-nb' + nbReducProduct + '" value="x" />';
	newReduc += '					</div>';
	
	$('.reduc-free').append( newReduc );
	
	nbReducProduct++;
	return false;
}

/**
 *	Cette fonction permet d'ajouter un groupe de condition.
 */
function pmtGroupsAdd(){
	if( !$('#cdt-add-grp').length ) return false;
	
	$('#cdt-add-grp').click(function(){
		var newGrp = '';
		
		if( nbGroups>0 ){
			newGrp += '		<select id="cdt-between-' + nbGroups + '" name="cdt-between[' + nbGroups + ']" class="cdt-grp-rule">';
			newGrp += '			<option value="and">' + pmtSpecialsEt + '</option>';
			newGrp += '			<option value="or">' + pmtSpecialsOu + '</option>';
			newGrp += '		</select>';
		}

		newGrp += '	<fieldset class="cdt-grp">';
		newGrp += '		<legend class="cdt-grp-legend">';
		newGrp += '		<input class="cdt-grp-del input-icon-del" type="button" name="del-grp-cdt" id="del-grp-cdt-' + nbGroups + '" value="x" />';
		newGrp += '		<label for="cdt-general-' + nbGroups + '">' + pmtSpecialsGroupe + '' + ($('.add-pmt-cdt fieldset').length+1) + '</label>';
		newGrp += '		</legend>';
		newGrp += '		<div class="cdt-intro">';
		newGrp += '			<label for="cdt-general-' + nbGroups + '">' + pmtSpecialsValide + '</label>';
		newGrp += '			<select name="cdt-general[' + nbGroups + ']" id="cdt-general-' + nbGroups + '" class="cdt-grp-rule-items">';
		newGrp += '				<option value="all">' + pmtSpecialsToutesLes + '</option>';
		newGrp += '				<option value="any">' + pmtSpecialsAuMoins + '</option>';
		newGrp += '			</select>';
		newGrp += '			' + pmtSpecialsConsition;
		newGrp += '		</div>';
		newGrp += '		<div id="cdt-grp-list-cdt' + nbGroups + '" class="cdt-grp-list-cdt">';
		newGrp += '		</div>';
		newGrp += '		<input type="button" onclick="return pmtConditionsAdd(' + nbGroups + ');" class="btn-action-small" value="' + pmtSpecialsAjouterCondition + '" id="cdt-add-' + nbGroups + '" name="cdt-add" title="' + pmtSpecialsAjouterCondition + '" />';
		newGrp += '	</fieldset>';
		
		$(this).before( newGrp );
		nbGroupCdts[ nbGroups ] = 0;
		pmtConditionsAdd( nbGroups );
		
		nbGroups++;
	});
}

/**
 *	Cette fonction permet d'ajouter une condition à un groupe.
 *	@param grp Obligatoire, numéro du groupe
 */
function pmtConditionsAdd( grp ){
	var nbCdt = nbGroupCdts[ grp ];
	var newCdt = '';

	newCdt += '		<div class="cdt-config">';
	newCdt += '			<select id="cdt-type-' + grp + '-' + nbCdt + '" name="cdt-type[' + grp + '][]" class="cdt-grp-cdt">';
	newCdt += '				<option value=""></option>';
	newCdt += '			</select>';
	newCdt += '			<div class="cdt-psy-val" style="display:none;">';
	newCdt += '				<select id="cdt-symbol-' + grp + '-' + nbCdt + '" name="cdt-symbol[' + grp + '][]" class="cdt-psy">';
	newCdt += '					<option value="-1"></option>';
	newCdt += '				</select>';
	newCdt += '			</div>';
	newCdt += '		</div>';

	$('#cdt-grp-list-cdt' + grp).append( newCdt );
	
	// chargement des conditions et des symboles accessible pour le type de promotion en cours d'utilisation
	pmtConditionsLoad( grp, nbCdt );
	
	nbGroupCdts[ grp ] = nbGroupCdts[ grp ] + 1;
	return false;
}

/**
 *	Cette fonction permet de supprimer une condition
 *	@param cdt Obligatoire, objet condition
 */
function pmtConditionsRemove( cdt ){
	cdt.parents('.cdt-config').remove();
}

/**
 *	Cette foncton permet de charger les conditions accessibles pour le type de promotion en cours d'utilisation.
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de condition
 */
function pmtConditionsLoad( grp, cdt ){
	if( !$('#cdt-type-' + grp + '-' + cdt).length ) return false;
	
	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?getCdt=1&type=' + typePromotion , function( cdts ){
		var optionsCdt = '';
		
		if( cdts.length ){
			
			var c = false;
			var cls_id = 0;
			var	pos = 1;
			
			for( var i=0 ; i<cdts.length ; i++ ){
				c = cdts[i];
				
				if( cls_id!=c.cls ){
					if( pos!=1 ){
						optionsCdt += '	</optgroup>';
					}
					optionsCdt += '		<optgroup label="' + htmlspecialchars(c.cls_name) + '">';
					cls_id = c.cls;
				}
					
				optionsCdt += '				<option title="' + htmlspecialchars(c.desc) + '" value="' + c.id + '">' + htmlspecialchars(c.name) + '</option>';
					
				if( cls_id!=c.cls || pos==cdts.length ){
					optionsCdt += '		</optgroup>';
				}
					
				pos++;
			
			}
		}

		$('#cdt-type-' + grp + '-' + cdt).append( optionsCdt );
	});
	return false;
}

/**
 *	Cette foncton permet de charger les conditions accessibles pour le type de promotion en cours d'utilisation.
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de condition
 *	@param idCdt Obligatoire, identifiant de la condition sélectionnée
 */
function pmtConditionSymbolsLoad( grp, cdt, idCdt ){
	if( !$('#cdt-symbol-' + grp + '-' + cdt).length ) return false;
	
	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?getSymbols=1&cdt=' + idCdt, function( symbols ){
		var optionsSymbols = '';
		optionsSymbols += '<option value="-1"></option>';
		
		if( symbols.length ){
			
			var s = false;
			for( var i=0 ; i<symbols.length ; i++ ){
				s = symbols[i];
				
				optionsSymbols += '<option ' + ( s.dft==1 ? 'selected="selected"' : '' ) + ' value="' + s.symbol + '">' + s.desc + '</option>';
			}
			
		}
		
		$('#cdt-symbol-' + grp + '-' + cdt).html( optionsSymbols );
	});
	
	return false;
}

/** 
 *	Cette fonction permet de charger un formulaire dépendant de la condition (champ texte, liste des service de livraison ou moyen de paiement, booléen...).
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de condition
 *	@param idCdt Obligatoire, identifiant de la condition sélectionnée
 */
function pmtConditionFormLoad( grp, cdt, idCdt ){
	if( !$('#cdt-symbol-' + grp + '-' + cdt).length ) return false;
	
	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?getForm=1&cdt=' + idCdt, function( forms ){
		var htmlForm = '';
		
		if( forms.type.length ){
			switch( parseInt(forms.type) ){
				case 3 :
					htmlForm += '<input type="text" class="cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][]" />';
					htmlForm += ' <input type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" />';
					htmlForm += '<sub>' + pmtSpecialsSubValeurEntier + '</sub>';
					break;
				case 4 : 
					htmlForm += '<input type="text" class="cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][]" />';
					htmlForm += ' <input type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" />';
					htmlForm += '<sub>' + pmtSpecialsSubValeurEntierFlottant + '</sub>';
					break;
				case 5 :
					if( forms.data.length ){
						htmlForm += '<select class="cdt-grp-value" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][]">';
						for( var i=0 ; i<forms.data.length ; i++ ){
							var d = forms.data[i];
							htmlForm += '<option value="' + d.id + '">' + htmlspecialchars(d.name) + '</option>';
						}
						htmlForm += '</select>';
						htmlForm += ' <input type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" />';
						break;
					}
				default :
					htmlForm += '<input type="text" class="cdt-grp-prd-search" id="cdt-value-' + grp + '-' + cdt + '" name="cdt-value[' + grp + '][]" />';
					if( idCdt==8 ){
						htmlForm += '<input type="button" value="Rechercher" id="cdt-prd-search" name="cdt-prd-search" class="btn-action-small" />';
					}
					htmlForm += ' <input type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del input-icon-del" value="x" name="cdt-del" />';
					break;
			}
			
		}

		$('#cdt-symbol-' + grp + '-' + cdt).nextAll().remove();
		$('#cdt-symbol-' + grp + '-' + cdt).after( htmlForm );
		$('#cdt-symbol-' + grp + '-' + cdt).parent().css('display', 'inline-block');

		pmtConditionLoadInclusions( grp, cdt, idCdt );
		pmtConditionsLoadSameQty( grp, cdt, idCdt );
	});
	
	return false;
}

/**
 *	Cette fonction permet de charger au besoin la liste de choix pour l'option Tenir compte des règles d'inclusions
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de condition
 *	@param idCdt Obligatoire, identifiant de la condition sélectionnée
 */
function pmtConditionLoadInclusions( grp, cdt, idCdt ){
	var html = '';
	
	var intIDCdt = parseInt(idCdt);

	if( $.inArray(intIDCdt, [1, 2, 3, 4, 5, 9, 10, 11, 12, 13, 14])>=0 ){
		var label = pmtSpecialsCalcule;
		if( intIDCdt==9 ){
			label = pmtSpecialsCalculee;
		}

		var title = pmtSpecialsTitreRegleInclusion;
		
		html = ''
			+ 	'<div class="cdt-calculated">'
			+		'<label for="cdt-calculated-' + grp + '-' + cdt + '" title="' + title + '">' + label + ' :</label>'
			+		'<select id="cdt-calculated-' + grp + '-' + cdt + '" name="cdt-calculated[' + grp + '][' + cdt + ']" class="cdt-calculated-cdt">'
			+			'<option value="order">' + pmtSpecialsTouteCommande + '</option>'
			+			'<option value="rules">' + pmtSpecialsRegleInclusion + '</option>'
			+		'</select>'
			+	'</div>';
	}

	$('#cdt-type-' + grp + '-' + cdt).parent().find('sub').before( html );
}

/**
 *	Cette fonction permet de charger au besoin l'information complémentaire à une condition : "Même quantité sur chaque ligne de commande".
 *	@param grp Obligatoire, numéro du groupe
 *	@param cdt Obligatoire, numéro de condition
 *	@param idCdt Obligatoire, identifiant de la condition sélectionnée
 */
 function pmtConditionsLoadSameQty( grp, cdt, idCdt ){
 	var html  = '';

 	if( idCdt!=4 && idCdt != 3 ){
 		return false;
 	}

 	html = ''
 		 +	'<div class="cdt-same-qte">'
		 +		'<input type="checkbox" name="same-qty[' + grp + '][' + cdt + ']" id="same-qty-' + grp + '-' + cdt + '" value="1" />'
		 +		'<label for="same-qty-' + grp + '-' + cdt + '">' + pmtSpecialsMemeQuantite + '</label>'
		 +	'</div>';

	$('#cdt-calculated-' + grp + '-' + cdt).parent().after( html );
 }

/**
 *	Cette fonction permet de générer un code promotion aléatoirement.
 */
function pmtGeneratedCode(){
	$.get('/admin/ajax/promotions/ajax-pmt-specials.php?generated=1', function( code ){
		$('#code').val( code );
	});
}

/**
 *	Cette fonction permet de recharger le tableau contenant les promotions contenues dans une série.
 */
function reloadPromotionsSerie( page ){
	page = $.isNumeric(page) ? page : 1;
	
	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?relaodserie=1&idCod=' + idCod + '&type=' + typePromotion + '&page=' + page, 
		function( series ){
			$('#tb-tabSerie tbody').html( series.html );
			$('#pagination').html( series.pagination );
		}
	);
	
	return false;
}

/**
 *	Cette fonction permet d'ajout une orthographe différente pour un code promotion
 */
function saveVariations(){
	$('.error, .success').remove();
	var code = $('#new-variation').val();

	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?save-variations=1&idCod=' + idCod + '&code=' + code, 
		function( result ){
			if( result.error && result.error.length ){
				$('#tb-tabVariations').prepend( '<div class="error">' + result.error + '</div>' );
			}else{
				$('#tb-tabVariations table tbody').html( result.success )
			}
		}
	);
	
	return false;
}

/**
 *	Cette fonction permet de supprimer une ou plusieurs orthographes différentes pour un code promotion
 */
function delVariations(){
	$('.error, .success').remove();
	

	var variante = new Array();
	$('input[name="del-var[]"]:checked').each(function(){
		variante.push( $(this).val() );
	});

	if( !variante.length ){
		return false;
	}

	$.getJSON('/admin/ajax/promotions/ajax-pmt-specials.php?del-variations=1&idCod=' + idCod + '&var[]=' + variante.join('&var[]='), 
		function( result ){
			if( result.error && result.error.length ){
				$('#tb-tabVariations').prepend( '<div class="error">' + result.error + '</div>' );
			}else{
				$('#tb-tabVariations table tbody').html( result.success )
			}
		}
	);
	
	return false;
}


/**
 *	Cette fonction permet d'ajouter un produit à la liste de ceux offerts
 */
function addPrdOffers(){
	$('.error').remove();

	var prdID = $('#prd-offer-id').val();
	var prdRef = $('#prd-offer-ref').val();
	var prdName = $('#prd-offer-name').val();

	if( $.trim(prdID)=='' || $.trim(prdRef)=='' || $.trim(prdName)=='' ){
		var searchRef = $('#pop-ref').val();

		if( $.trim($(searchRef)!='') ){
			$.ajax({
				url: '/admin/ajax/promotions/ajax-pmt-specials.php',
				data: 'getinforef=1&ref=' + searchRef,
				type: 'get',
				dataType: 'json',
				async: false,
				success: function( infoPrd ){
					if( $.trim(infoPrd.ref)!='' ){
						prdID = infoPrd.id;
						prdRef = infoPrd.ref;
						prdName = infoPrd.name;
					}
				}
			});
		}
	}

	if( $.trim(prdID)!='' && $.trim(prdRef)!='' && $.trim(prdName)!='' ){
		if( !$('#prd-pop-' + prdID).length ){
			var html = ''
				+	'<div id="prd-pop-' + prdID + '" class="prd-pop">'
				+		'<label for="pop-ref-' + prdID + '">' + pmtSpecialsReference + '</label>'
				+		'<input class="text ref" type="text" name="list-pop-ref[]" value="' + addslashes( prdRef ) + '" />'
				+		'<label for="pop-qty-' + prdID + '">' + pmtSpecialsQteOfferte + '</label>'
				+		'<input class="text qty" type="text" name="list-pop-qty[]" value="1" />'
				+		'<input type="button" value="x" name="del-pop" class="del-pop input-icon-del" />'
				+	'</div>';

			$('#list-prd-offers .no-pop').remove();
			$('#list-prd-offers').append( html );
		}

		$('#pop-ref').val('');
		$('#prd-offer-id').val('');
		$('#prd-offer-ref').val('');
		$('#prd-offer-name').val('');
	}else if( $.trim(searchRef)!='' ){
		$('#pmt-list-prd-offers fieldset').append('<div class="error">' + pmtSpecialsLaReference + '"' + addslashes( searchRef ) + '"' + pmtSpecialsReferenceExclus + '</div>');
	}

	return false;
}

$(document).delegate(
	'#pop-ref-select', 'click', function(){
		displayPopup( pmtSpecialsRechercheProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&input_id_prd_id=prd-offer-id&input_id_name=prd-offer-name&input_id_ref=prd-offer-ref' );
	}
).delegate(
	'.del-pop', 'click', function(){
		$(this).parent().remove();

		if( !$('#list-prd-offers div').length ){
			$('#list-prd-offers').html( '<div class="no-pop prd-pop">' + pmtSpecialsAuncunProduitOffert + '</div>' );
		}

		return false;
	}
);

function linkPrdCol( prd, include ){
	if( $( '#colisage-'+prd+' option:selected' ).val() != -1 ){
		$.ajax({
			url: '/admin/ajax/promotions/ajax-pmt-specials.php',
			data: 'linkPrdCol=1&prd='+prd+'&col='+$( '#colisage-'+prd+' option:selected' ).val()+'&pmt='+$('#id-promotion').val()+'&include='+include,
			type: 'get',
			success : function( html ){
				$('table tbody#'+prd).html( html ) ;
			}
		})
		$('#colisage-'+prd).val("-1");
	}
}

function unlinkPrdCol( pmt, prd, col, include ){
	$.ajax({
		url: '/admin/ajax/promotions/ajax-pmt-specials.php',
		data: 'unlinkPrdCol=1&pmt='+pmt+'&prd='+prd+'&col='+col+'&include='+include,
		type: 'get',
		success : function(html){
			$('table tbody#'+prd).html( html ) ;
		}
	})
	return false;
}
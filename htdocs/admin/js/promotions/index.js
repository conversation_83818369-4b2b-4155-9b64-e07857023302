/**	\file promotions/index.js
 *	Fonctions utilitaires pour le traitement des promotions.
 *	Une partie de ces fonctions dépend de la disponibilité de la librairie string.js
 */


function initTypeOptionValeurType(element){

	const idCat = $(element).attr('id').replace('prc-remise-','');
	if( $(element).val() == 1 || $(element).val() == 3 ){
		$('#cat-remise-valeur-type-'+idCat).show();
	}else{
		$('#cat-remise-valeur-type-'+idCat).hide();
	}
	
}

$(document).ready( function(){
	$('select.prc-remise').each(function(){ initTypeOptionValeurType(this) });
	$('select.prc-remise').change(function(){ initTypeOptionValeurType( this ); });
	$('#remise').change(function(){
		if($(this).val() == 1 || $(this).val() == 3){
			$('#remise-valeur-type').show();
		}else{	
			$('#remise-valeur-type').hide();
		}
	});
	
	$('.valeur-select').change(function() {
		var id = (new RegExp('^valeur\-type\-([0-9]+)$')).exec($(this).attr('id'));
		if (! (id && id[1] !== undefined)) return ;
	});
}).delegate(
	'#periodpicker .selectorview', 'click', function(){
		if($('#periodpicker .selector').css('display')=='none'){
			$('#periodpicker .selector').show();
		}else{
			$('#periodpicker .selector').hide();
		}
	}
).delegate(
	'#groupspromotionpicker .selectorview', 'click', function(){
		if($('#groupspromotionpicker .selector').css('display')=='none'){
			$('#groupspromotionpicker .selector').show();
		}else{
			$('#groupspromotionpicker .selector').hide();
		}
	}
).delegate(
	'#periodpicker .selector a', 'click', function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = 'index.php?periode='+p;
	}
).delegate(
	'#groupspromotionpicker .selector a', 'click', function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = 'index.php?group='+p;
	}
);

function addCdtEditPromoProducts(){
	var nb_new = parseFloat( $("#nb-cdt-new").val() );
	if( $("#cdts select").length==0 ){
		$("#cdts").show();
		$("#cdt-header").show();
	}
	$("#cdts").append('<select class="fld" name="cdt-new['+nb_new+']" id="cdt-new-'+nb_new+'" onchange="javascript:cdtForm(0, '+nb_new+', true, false, false, true, true);">' + fldOpt + '</select><img class="del-cdt" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsSuppressionCondition + '" name="del" id="del-cdt-new-'+nb_new+'" onclick="javascript:delCdt(true, 0, '+nb_new+', true, true, false, 0);" /><div class="conditions-next" id="condition-new-'+nb_new+'"></div>');
	$("#nb-cdt-new").val(nb_new+1);
}

function addCdtPromoProducts(){
	if( $("#cdts select").length==0 ){
		$("#cdts").show();
		$("#cdt-header").show();
	}
	$("#cdts").append('<select class="fld" name="cdt-new['+nb_new+']" id="cdt-new-'+nb_new+'" onchange="javascript:cdtForm(0, '+nb_new+', true, false, false, true);">' + fldOpt + '</select><img class="del-cdt" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsSuppressionCondition + '" name="del" id="del-cdt-new-'+nb_new+'" onclick="javascript:delCdt(true, 0, '+nb_new+', true, true, false, 0);" /><div class="conditions-next" id="condition-new-'+nb_new+'"></div>');
	nb_new++;
}

function addCdt( pmt, newCdt ){
	if( newCdt ){
		$("#add-cdt-0").parent().find('>br').before('<div class="display-flex" id="js-new-condition-'+nb_new+'"><div class="display-flex" id="js-new-condition2-'+nb_new+'"><select class="fld" name="cdt-new['+nb_new+']" id="cdt-new-'+nb_new+'" onchange="javascript:cdtForm('+pmt+', '+nb_new+', true, false, false, true);">' + fldOpt + '</select><div class="conditions-next" id="condition-new-'+nb_new+'"></div></div><img class="del-cdt" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsSuppressionCondition + '" name="del" id="del-cdt-new-'+nb_new+'" onclick="javascript:delCdt(true, 0, '+nb_new+', true, true, false, 0);" /></div>');
		nb_new++;
	} else {
		var nb = $("#nb-cdt-"+pmt).val();
		$("#add-cdt-"+pmt).parent().find('>br').before('<div class="display-flex" id="js-new-condition-'+pmt+'-'+nb+'"><div class="display-flex" id="js-new-condition2-'+pmt+'-'+nb+'"><select class="fld" name="cdt-'+pmt+'['+nb+']" id="cdt-'+pmt+'-'+nb+'" onchange="javascript:cdtForm('+pmt+', '+nb+', false, false, false, true);">' + fldOpt + '</select><div class="conditions-next" id="condition-'+pmt+'-'+nb+'"></div></div><img class="del-cdt" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsSuppressionCondition + '" name="del" id="del-cdt-'+pmt+'-'+nb+'" onclick="javascript:delCdt(true, '+pmt+', '+nb+', false, true, false, '+nb+');" /></div>');
		nb++;
		$("#nb-cdt-"+pmt).val(nb);
	}
}

function search_references(input){
	if( $("#"+input).val() != '') {
		if( inp!=input ){
			$(".ac_results").remove();
		}
		if( !passe || inp!=input){
			
			if( $("#"+input).val()!='' ){
				$("#"+input).autocomplete({
					source: 'ajax-search-prd.php',
					select: function (event, ui) {
						event.preventDefault();
						var regex = /[^ - ]*/;
						var ref = $.trim(regex.exec( ui.item.label));
						if(ref){
							$("#"+input).val(ref);
						}
					},
				});
			}
			passe = true;
			inp = input;
		}
	}
}

function pmtConfirmDelList(){
	return window.confirm(promotionsConfirmSuppressionCodespromotions);
}

function addProductInPromotion(){
	$.ajax({
		type: "POST",
		url: '/admin/promotions/products/ajax-product-promotion.php',
		data: { 'ref' : $("#ref-prd-add").val() },
		dataType: 'xml',
		success: function(xml) {
			var idPrd = $(xml).find('id-prd').text();
			var refPrd = $(xml).find('ref-prd').text();
			var pricePrd = $(xml).find('price-prd').text();
			if(idPrd){
				if( !$("#prd-"+idPrd).length && !$("#prc-prd-"+idPrd).length ){
					var html = '';
					html += '	<tr id="prd-'+idPrd+'">';
					html += '		<td headers="prd-ref">';
					html += ' 			<img onclick="delPrdOfPromotion('+idPrd+')" class="del" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsRetirerProduit + '" />';
					if( $(xml).find('cat-prd').text()=='0' )
						html += refPrd;
					else
						html += '			<a href="/admin/catalog/product.php?cat='+$(xml).find('cat-prd').text()+'&prd='+idPrd+'" target="_bank">'+refPrd+'</a>';
					html += '<br /><span class="info-fld">' + promotionsPrixBaseHT + ' : '+pricePrd+' &euro;</span>';
					html += ' 		</td>';
					html += ' 		<td headers="prd-remise">';
					html += ' 			<select name="prd-remise-'+idPrd+'" id="prd-remise-'+idPrd+'">'+typeOption+'</select>';
					html += ' 			<select style="display : none;" name="prd-remise-valeur-type-'+idPrd+'" id="prd-remise-valeur-type-'+idPrd+'">'+typeOptionValeurType+'</select>';
					html += ' 		</td>';
					html += ' 		<td>';
					html += ' 			<input type="text" name="prd-remise-val-'+idPrd+'" id="nmae-remise-val-'+idPrd+'" value="" />';
					html += ' 		</td>';
					html += ' 	</tr>';
				} else {
					alert(promotionsPromoInclusProduit);
				}
			}else{
				alert('Produit non trouvé');
			}					
			$("#none-prd").hide();
			$("#tb-prd-promo tbody").append( html );
			$("#prd-remise-"+idPrd+" option[value=2]").attr("selected", "selected");
			$("#ref-prd-add").val('');
			
			$("#prd-remise-"+idPrd).change(function(){
				
				if($(this).val() == 1 || $(this).val() == 3){
					$('#prd-remise-valeur-type-'+idPrd).show();
				}else{	
					$('#prd-remise-valeur-type-'+idPrd).hide();
				}
			});
		}
	});
}
function addProductInGroups(){
	var ref = $("#ref-prd-add-1").val();
	$.ajax({
		type: "POST",
		url: '/admin/promotions/products/ajax-product-promotion.php',
		data: {'ref' : ref },
		dataType: 'xml',
		success: function(xml) {
			var idPrd = $(xml).find('id-prd').text();
			var refPrd = $(xml).find('ref-prd').text();
			var pricePrd = $(xml).find('price-prd').text();
			if(idPrd){
				if( !$("#prd-"+idPrd).length  && !$("#prc-prd-"+idPrd).length	 ){
					var html = '';
					html += '		<div id="prd-'+idPrd+'">';
					html += ' 			<img onclick="delPrdOfPromotion('+idPrd+')" class="del" src="/admin/images/del.svg" alt="Supprimer" title="' + promotionsRetirerProduit + '" />';
					if( $(xml).find('cat-prd').text()=='0' )
						html += refPrd;
					else
						html += '			<a href="/admin/catalog/product.php?cat='+$(xml).find('cat-prd').text()+'&prd='+idPrd+'" target="_bank">'+refPrd+'</a>';
					html += '<br /><span class="info-fld">' + promotionsPrixBaseHT+pricePrd+' &euro;</span>';
					html += ' 		</div>';
					
					$("#grp-none-prd").hide();
					$("#grp-prd-promo").append( html );
					$("#grp-prd-promo").show();
				} else {
					alert(promotionsPromoInclusProduit);
				}					
				$("#ref-prd-add-1").val('');
			}else{
				alert('Produit non trouvé');
			}
		}

	});
}

function addGroupInPromotion(){
	var idRemise = $("#remise").val();
	var idRemiseValeurType = $("#remise-valeur-type").val();

	if( $("#remise-val").val()!=='' ){
		$("#grp-prd-promo div").each(function(){
			var idPrd = $(this).attr('id').substring($(this).attr('id').indexOf('-',0)+1);
			var html = '';
			html += '	<tr id="prd-'+idPrd+'">';
			html += '		<td headers="prd-ref">';
			html += ' 			'+$(this).html();
			html += ' 		</td>';
			html += ' 		<td headers="prd-remise">';
			html += ' 			<select name="prd-remise-'+idPrd+'" id="prd-remise-'+idPrd+'">'+typeOption+'</select>';
			html += ' 			<select style="display : none;" name="prd-remise-valeur-type-'+idPrd+'" id="prd-remise-valeur-type-'+idPrd+'">'+typeOptionValeurType+'</select>';
			html += ' 		</td>';
			html += ' 		<td>';
			html += ' 			<input type="text" name="prd-remise-val-'+idPrd+'" id="prd-remise-val-'+idPrd+'" value="'+$("#remise-val").val()+'" />';
			html += ' 		</td>';
			html += ' 	</tr>';
			
			$("#none-prd").hide();
			$("#tb-prd-promo tbody").append( html );
			$("#prd-remise-"+idPrd+" option[value="+idRemise+"]").attr("selected", "selected");
			if(idRemise == 3){
				$("#prd-remise-valeur-type-"+idPrd).show();
				$("#prd-remise-valeur-type-"+idPrd+" option[value="+idRemiseValeurType+"]").attr("selected", "selected");
			}
			
			$("#prd-remise-"+idPrd).change(function(){
				if($(this).val() == 1 || $(this).val() == 3){
					$('#prd-remise-valeur-type-'+idPrd).show();
				}else{	
					$('#prd-remise-valeur-type-'+idPrd).hide();
				}
			});
		});
		$("#grp-cat-promo div").each(function(){
			var idCat = $(this).attr('id').substring($(this).attr('id').indexOf('-',0)+1);
			
			var html = '';
			html += '	<tr id="cat-'+idCat+'">';
			html += '		<td headers="cat-ref">';
			html += ' 			'+$(this).html();
			html += ' 		</td>';
			html += ' 		<td headers="cat-remise">';
			html += ' 			<select name="cat-remise-'+idCat+'" id="cat-remise-'+idCat+'">'+typeOption+'</select>';
			html += ' 			<select style="display : none;" name="cat-remise-valeur-type-'+idCat+'" id="cat-remise-valeur-type-'+idCat+'">'+typeOptionValeurType+'</select>';
			html += ' 		</td>';
			html += ' 		<td>';
			html += ' 			<input type="text" name="cat-remise-val-'+idCat+'" id="cat-remise-val-'+idCat+'" value="'+$("#remise-val").val()+'" />';
			html += ' 		</td>';
			html += ' 	</tr>';
			
			$("#none-cat").hide();
			$("#tb-cat-promo tbody").append( html );
			$("#cat-remise-"+idCat+" option[value="+idRemise+"]").attr("selected", "selected");
			if(idRemise == 3){
				$("#cat-remise-valeur-type-"+idCat).show();
				$("#cat-remise-valeur-type-"+idCat+" option[value="+idRemiseValeurType+"]").attr("selected", "selected");
			}
			
			$("#cat-remise-"+idCat).change(function(){
				if($(this).val() == 1 || $(this).val() == 3){
					$('#cat-remise-valeur-type-'+idCat).show();
				}else{	
					$('#cat-remise-valeur-type-'+idCat).hide();
				}
			});
		});
	} else {
		alert(promotionsAucuneRemiseSaisi);return false;
	}
	$("#remise-val").val( '' );
	$("#grp-prd-promo").html( '' );
	$("#grp-none-prd").show();
	$("#grp-cat-promo").html( '' );
	$("#grp-none-cat").show();
}
function delPrdOfPromotion(id){
	$("#prd-"+id).remove();
	if( $("#tb-prd-promo tbody tr").length==1 ){
		$("#none-prd").show();
	}
}
function delPrcOfPromotion(id){
	var res = delPrice(false, id, 0, true);
	if( res )
		$("#trprc-"+id).remove();
}
function delAllPrc(){
	$("#tb-prd-promo tbody tr").each(function(){
		if( $(this).attr('id').length ){
			$(this).remove();
		}
	});
	$("#none-prd").show();
}
function searchReferencePrd(input){
	$('.ac_results').remove();
	if( $("#"+input).val() != '') {
		$("#"+input).autocomplete({
			source: "ajax-search-prd.php",
			select: function (event, ui) {
				event.preventDefault();
				var regex = /[^ - ]*/;
				var ref = $.trim(regex.exec( ui.item.label));
				if(ref){
					$("#"+input).val(ref);
				}
			},
		});
	}

}
function verifPromotion(isNew){	

	if( $("#date-start").val()=="" || $("#date-end").val()=="" ){
		alert( promotionsAlertDateDebutFinFormatAndObg );
		return false;
	} 

	var dateStart = $("#date-start").val();
	var elemDateStart = dateStart.split('/');
	var dateEnd = $("#date-end").val();
	var elemDateEnd = dateEnd.split('/');
	
	if( !validDate(dateStart) || !validDate(dateEnd) ){
		alert( promotionsAlertDateDebutFinFormat );
		return false;
	}
	
	dateStart = Date.parse(elemDateStart[2]+'-'+elemDateStart[1]+'-'+elemDateStart[0]);
	dateEnd = Date.parse(elemDateEnd[2]+'-'+elemDateEnd[1]+'-'+elemDateEnd[0]);
	if( dateEnd<dateStart ){
		alert( promotionsAlertDateFinSupDateDebut )
		return false;
	}
	
	if( isNew ){
		if( $("#tb-prd-promo tbody tr").length<=1 && $("#tb-cat-promo tbody tr").length<=1  ){
			alert( promotionsAlertNonPromotion );
			return false;
		}
	} /* else {
		return window.confirm("Aucun produit n'est affecté à la promotion, elle va donc être supprimée.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?")
	} */
	
	var html = ''; var id = 0;
	$("#tb-prd-promo tbody tr").each(function(){
		if( $(this).attr('id') && $(this).attr('id').length ){
			id = $(this).attr('id').substring(4);
			
			if( $(this).attr('id').substr(0, 3)=='prc' ){
				html = '<input type="hidden" name="prc-id[]" id="prc-id-'+id+'" value="'+id+'" />';
				$("#prc-"+id).append( html );
			} else {
				html = '<input type="hidden" name="prd-id[]" id="prd-id-'+id+'" value="'+id+'" />';
				$("#prd-"+id).append( html );
			}
		}
	});
	
	var html = ''; var id = 0;
	$("#tb-cat-promo tbody tr").each(function(){
		if( $(this).attr('id') &&  $(this).attr('id').length ){
			id = $(this).attr('id').substring(4);
			
			if( $(this).attr('id').substr(0, 3)=='prc' ){
				html = '<input type="hidden" name="prc-id[]" id="prc-id-'+id+'" value="'+id+'" />';
				$("#prc-"+id).append( html );
			} else {
				html = '<input type="hidden" name="cat-id[]" id="cat-id-'+id+'" value="'+id+'" />';
				$("#cat-"+id).append( html );
			}
		}	
	});

}
function delGroups(){
	return window.confirm(promotionsConfirmSuppressionGroupePromotion);
}
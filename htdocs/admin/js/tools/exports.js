// Stocke le timer permettant de mettre à jour la barre de progression
// La barre de progression est mise à jour par la fonction reloadProgressBar
var ajaxCall = false;

$(document).ready(function(){
    $('.exp-processing').each(function(){
        updateProgressBar(parseInt($(this).attr('data-id')));
    });

    ajaxCall = setInterval(reloadProgressBar, 5000);
});

// Fonction permettant de recharger la barre de progression pour les exports en cours de traitement
var reloadProgressBar = function(){
    var exp = [];
    $('.exp-processing').each(function(){
        exp.push(parseInt($(this).attr('data-id')))
    });
    $('.exp-pending').each(function(){
        exp.push(parseInt($(this).attr('data-id')))
    });
    $.ajax({
        url: '/admin/ajax/tools/ajax-progress-update.php',
        data:  "exp="+JSON.stringify(exp),
        method: 'get',
        dataType: 'json',
        success: function(data){
            data = JSON.parse(data);
            for(var i in data) {
                if( data[i].class == 'exp-success' || data[i].class == 'exp-err' ){
                    $('#exp-'+i).remove();
                    $('#table-finished-export tbody').prepend(data[i].html);
                    
                    if( $('#table-unfinished-export tbody tr').length == 1 ){
                        $('#table-unfinished-export tbody #empty').show(); 
                        // Ne tente plus de mettre à jour les barres de progression si il n'y en a plus
                        clearInterval(ajaxCall);
                    }
                }else{
                    $('#state-'+i).removeClass();
                    $('#state-'+i).addClass(data[i].class);
                    $('#state-'+i).html(data[i].html);
                    updateProgressBar(i);
                }
            }
        },
    });
}

//Calcule de l'affichage des barre de progression
var updateProgressBar = function(exp_id){
	//Nombre de ligne total de l'import
	var maxProgression = parseInt($('#state-'+ exp_id +' .bar-progression').attr('data-progress-max'));
	//Taille maximal de la barre de progression
	var maxWidth = $('#state-'+ exp_id +' .progress-bar').width() - 4;
	//Nombre de ligne traité de l'import  
    var progression = parseInt($('#state-'+ exp_id +' .bar-progression').attr('data-progress'));
    
    var width = progression / maxProgression * maxWidth;
	$('#state-'+ exp_id +' .bar-progression').width(width+'px');
}
function confirmBannerDelList(){
	return window.confirm(bannersConfirmSupressionList);
}
function bannerValidForm(frm){
	return true;
	if( !trim(frm.name.value) ){
		alert(bannersAlertTitre);
		frm.name.focus();
		return false;
	}
	if( trim(frm.date_from.value) || frm.elements['from-opt'][1].checked ){
		if( !validDate(frm.date_from.value) ){
			alert(bannersAlertDateDebut);
			frm.date_from.focus();
			return false;
		}
		if( !validHour(frm.elements['hour_from'].value) ){
			alert(bannersAlertHeureDebut);
			frm.elements['hour_from'].focus();
			return false;
		}
	}
	if( trim(frm.date_to.value) || frm.elements['to-opt'][1].checked ){
		if( !validDate(frm.date_to.value) ){
			alert(bannersAlertDateFin);
			frm.date_to.focus();
			return false;
		}
		if( !validHour(frm.elements['hour_to'].value) ){
			alert(bannersAlertHeureFin);
			frm.elements['hour_to'].focus();
			return false;
		}
	}
	if( !trim(frm.alt.value) ){
		alert(bannersAlertChampsAlternatif);
		frm.alt.focus();
		return false;
	}
}
function clickPublishFrom(frm){
	frm.elements['from-opt'][1].checked = true;
}
function clickPublishTo(frm){
	frm.elements['to-opt'][1].checked = true;
}

var saveWstChecked = '';
var selectPlcID = 0;

$(document).ready(

	function(){
		$('input.datepicker').each(function(){
			var temp = this ;
			// Implémente le sélecteur de date sur chacun d'entre eux.
			$(this).DatePicker({
				format:'d/m/Y',
				date: $(this).val(),
				current: $(this).val(),
				starts: 1,
				onChange: function(formated, dates){
					if(dates != 'Invalid Date'){
						$(temp).val(formated);
						$(temp).DatePickerHide();
					}
				}
			});

		});

		$('#riawebsitepicker .selectorview').click(function(){
			if($('#riawebsitepicker .selector').css('display')=='none'){
				$('#riawebsitepicker .selector').show();
				$('#riadatepicker .selector').hide();
			}else{
				$('#riawebsitepicker .selector').hide();
			}
		});

		$('#riawebsitepicker .selector a').click( function(){
			window.location.href = 'index.php?wst='+$(this).attr('name');
		});

		$('#riaplaceadvertising .selectorview').click(function(){
			if($('#riaplaceadvertising .selector').css('display')=='none'){
				$('#riaplaceadvertising .selector').show();
				$('#riadatepicker .selector').hide();
			}else{
				$('#riaplaceadvertising .selector').hide();
			}
		});

		$('#riaplaceadvertising .selector a').click( function(){
			window.location.href = 'index.php?plc_id=' + $(this).attr('name').replace('e-', '') + '&active=' + js_active;
		});

		$('#periodpicker .selectorview').click(function(){
			if($('#periodpicker .selector').css('display')=='none'){
				$('#periodpicker .selector').show();
			}else{
				$('#periodpicker .selector').hide();
			}
		});
		$('#periodpicker .selector a').click( function(){
			window.location.href = 'index.php?active=' + $(this).attr('name').replace('p-', '') + '&plc_id=' + js_plc_id;
		});

		saveWstChecked = $('#list-websites [type=checkbox]:checked').attr('id')
		selectPlcID = $('#plc option:selected').val();
	}
).delegate(
	'input[name=choose-dest]', 'click', function(){
		var chooseType = $(this).val();

		if( chooseType == 'url' ){
			$('#choose-prd').hide();
			$('#choose-url').removeClass('none').show();
		} else {
			$('#choose-prd').removeClass('none').show();
			$('#choose-url').hide();
		}
	}
).delegate(
	'#search-prd, #prd', 'click', function(){
		displayPopup( bannersDisplayPopup, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&input_id_prd_id=prd_id&input_id_ref=prd&publish_search=false' );
	}
).delegate(
	'#plc', 'change', function(){
		if( $(this).val()==selectPlcID ){
			$('#' + saveWstChecked).attr('checked', 'checked');
		}

		checkPlace();
	}
);

function checkPlace(){
	var wstID = parseInt( $('#plc').find('option:selected').attr('data-wst-id') );

	$('#list-websites [type=checkbox]').removeAttr('checked');

	if( !isNaN(wstID) && wstID >= 0 ){
		if( wstID == 0 ){
			$('.list-wst-lng [type=checkbox]').removeAttr('checked', 'checked')
			$('.list-wst-lng').show();
		}else{
			$('.list-wst-lng').hide();
			if( $('#website-' + wstID).parent().find('.language [type=checkbox]').length > 1 ){
				$('#website-' + wstID).parent().show();
			}else{
				$('#website-' + wstID).parent().find('[type=checkbox]').attr('checked', 'checked');
				$('.websites-languages').hide();
			}
		}
	}
}
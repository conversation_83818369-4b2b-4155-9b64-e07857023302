var rwa = wst = prf = false;
$(document).ready(function(){
	loadConditions();
	if( $('#landings').length ){ loadLanding(); }
});

/**
 *	Cette fonction permet de charger les informations pour les conditions d'une action.
 */
function loadConditions(){
	if (typeof $('#rwa_id').val() == 'undefined') {return false;}
	if (typeof $('#prf_id').val() == 'undefined') {return false;}
	if (typeof $('#wst_id').val() == 'undefined') {return false;}
	
	rwa = $('#rwa_id').val();
	prf = $('#prf_id').val();
	wst = $('#wst_id').val();
	
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getinforwc=1&rwa=' + rwa + '&prf=' + prf + '&wst=' + wst, function(result){
		
		for( var r in result ){
			if( result[r]['type']!=8 ){
				$('#config-' + r).val( result[r]['val'] );
			} else {
				$('#config-' + r + '-y, #config-' + r + 'n').removeAttr('checked');
				if( result[r]['val']==0 ){
					$('#config-' + r + '-n').attr('checked', 'checked');
				}else{
					$('#config-' + r + '-y').attr('checked', 'checked');
				}
			}
		}
		
	});
	
	return false;
}

/**
 *	Cette fonction permet de sauvegarder les informations de condition d'une action
 */
function saveConditions(){
	prf = $('#prf_id').val();
	wst = $('#wst_id').val();
	
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?saverwc=1&prf=' + prf + '&wst=' + wst + '&' + $('#form-rewards').serialize(), function(result){
		
		if( result.done ){
			message( rwdActionsInformationMaj, false );
		} else {
			message( rwdActionsErreurEnregistrementActions, true );
		}
		
	});
	
	return false;
}

/**
 *	Cette fonction charge les palier pour l'action "Passage de la Nème commande".
 */
function loadLanding(){
	prf = $('#prf_id').val();
	wst = $('#wst_id').val();
	
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getlanding=1&prf=' + prf + '&wst=' + wst, function(result){
		
		var tbody = '';
		var rwc = false;
		if( result.length ){
			for( r in result ){
				
				rwc = result[r];
				rwc.pts = rwc.pts==null ? 0 : rwc.pts;
				rwc.sp_pts = rwc.sp_pts==null ? 0 : rwc.sp_pts;
				
				tbody += '	<tr id="tr-' + rwc.id + '">';
				tbody += '		<td headers="nb-landing" class="del-landing">' + rwc.val + '</td>';
				tbody += '		<td headers="points">';
				tbody += '			<div class="raw-params">';
				tbody += '				<div>' + rwdActionsInternaute + ' : '+ rwc.pts + ' ' + rwdActionsPoints + '</div>';
				tbody += '				<div>' + rwdActionsParrain + ' : ' + rwc.sp_pts + rwdActionsPoints + '</div>';
				tbody += '			</div>';
				tbody += '		</td>';
				tbody += '		<td headers="del-landing" class="del-landing">';
				tbody += '			<input onclick="return loadModifyLanding( ' + rwc.id + ' );" type="button" name="modify" id="modify-' + rwc.id + '" value="' + rwdActionsModifier + '" />';
				tbody += '			<a class="cancel" href="#" onclick="return delLanding( ' + rwc.id + ' );">' + rwdActionsSupprimer + '</a>';
				tbody += '		</td>';
				tbody += '	</tr>';
			}
		} else {
			tbody += '<tr id="no-landing"><td colspan="3">' + rwdActionsAucunPalier + '</td></tr>';
		}

		$('#tb-landing tbody').html( tbody );
	});
	
	return false;
}

/**
 *	Cette fonction permet de supprimer un palier.
 */
function delLanding( id ){
	message();
	
	if( id=='new' ){
		$('#new-landing').remove();
		return false;
	}
	
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?dellanding=1&cfg=' + id, function(result){
		
		if( result.done ){
			message( rwdActionsSuccesSuppressionPalier, false, 'landings' );
			loadLanding();
		} else {
			message( rwdActionsErreurSuppressionPalier, true, 'landings' );
		}

	});
	
	return false;
}
 
/**
 *	Cette fonction permet de proposer un formulaire permettant de créer un nouveau palier pour l'action "Passage de la Nème commande"/
 */
function addLanding(){
	message();
	
	if( $('#no-landing').length )
		$('#no-landing').remove();
	
	if( !$('#new-landing').length ){
		var newLanding = '';
		newLanding += '	<tr id="new-landing">';
		newLanding += '		<td headers="nb-landing" class="del-landing">';
		// newLanding += '			<label for="new-landing-nb">Palier :</label>';
		newLanding += '			<input type="text" class="text" name="new-landing-nb" id="new-landing-nb" value="" />';
		newLanding += '		</td>';
		newLanding += '		<td headers="points">';
		newLanding += '			<div class="raw-params">';
		newLanding += '				<div>';
		newLanding += '					<label for="rwa-usr-new">' + rwdActionsInternaute + '</label>';
		newLanding += '					<input class="text" type="text" name="rwa-usr[new]" id="rwa-usr-new" value="" />';
		newLanding += '				</div><div>';
		newLanding += '					<label for="rwa-sp-new">' + rwdActionsParrain + '</label>';
		newLanding += '					<input class="text" type="text" name="rwa-sp[new]" id="rwa-sp-new" value="" />';
		newLanding += '				</div>';
		newLanding += '			</div>';
		newLanding += '		</td>';
		newLanding += '		<td headers="del-landing" class="del-landing">';
		newLanding += '			<input type="button" name="save-new-landing" id="save-new-landing" onclick="return addNewLanding();" value="' + rwdActionsEnregistrer + '" />';
		newLanding += '			<a href="#" onclick="return delLanding( \'new\' );">' + rwdActionsAnnuler + '</a>';
		newLanding += '		</td>';
		newLanding += '	</tr>';
	
		$('#tb-landing tbody').append( newLanding );
	}
	
	return false;
}

/** 
 *	Cette fonction permet d'ajouter un palier.
*/
function addNewLanding(){
	rwa = $('#rwa_id').val();
	prf = $('#prf_id').val();
	wst = $('#wst_id').val();
	
	var landing = $('#new-landing-nb').val();
	var pts = $('#rwa-usr-new').val();
	var sp_pts = $('#rwa-sp-new').val();
	
	if( !$.trim( landing ) || !parseInt( landing ) || landing<=0 ){
		message( rwdActionsMsgPalierNumeriquePositif, true, 'landings' );
		return false;
	}

	if( $.trim( pts ) && (!parseInt( pts ) || pts<=0) ){
		message( rwdActionsMsgPointInternauteNumeriquePositif, true, 'landings' );
		return false;
	}

	if( $.trim( sp_pts ) && (!parseInt( sp_pts ) || sp_pts<=0) ){
		message( rwdActionsMsgPointParrainNumeriquePositif, true, 'landings' );
		return false;
	}

	if( !$.trim(pts) && !$.trim(sp_pts) ){
		message( rwdActionsMsgMinumumPrecisionPoint, true, 'landings' );
		return false;
	}
	
	var params = '?addnewlanding=1&rwa=' + rwa + '&prf=' + prf + '&wst=' + wst + '&landing=' + landing + '&pts=' + pts + '&sp_pts=' + sp_pts;
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php' + params, function(result){
		
		if( result.done ){
			message( rwdActionsMsgAjoutPalier, false, 'landings' );
			loadLanding();
		} else {
			message( rwdActionsErreurPalier, true, 'landings' );
		}
		
	});
	
	return false;
}

/**
 *	Cette fonction affiche le formulaire de modification d'un palier
 */
function loadModifyLanding( id ){
	loadLanding();
	
	rwc = false;
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getlanding=1&prf=' + prf + '&wst=' + wst + '&cfg=' + id, function(result){
		
		if( result.length ){
			rwc = result[0];
			
			rwc.pts = rwc.pts==null ? 0 : rwc.pts;
			rwc.sp_pts = rwc.sp_pts==null ? 0 : rwc.sp_pts;
			
			var tr = '';
			tr += '		<td headers="nb-landing" class="del-landing">';
			// tr += '			<label for="landing-nb-' + id + '">Palier :</label>';
			tr += '			<input type="text" id="landing-nb-' + id + '" name="landing-nb[' + id + ']" class="text" value="' + rwc.val + '" />';
			tr += '		</td>';
			tr += '		<td headers="points">';
			tr += '			<div class="raw-params">';
			tr += '				<div>';
			tr += '					<label for="rwa-usr-' + id + '">' + rwdActionsInternaute + '</label>';
			tr += '					<input class="text" type="text" name="rwa-usr[' + id + ']" id="rwa-usr-' + id + '" value="' + rwc.pts + '" />';
			tr += '				</div><div>';
			tr += '					<label for="rwa-sp-' + id + '">' + rwdActionsParrain + '</label>';
			tr += '					<input class="text" type="text" name="rwa-sp[' + id + ']" id="rwa-sp-' + id + '" value="' + rwc.sp_pts + '" />';
			tr += '				</div>';
			tr += '			</div>';
			tr += '		</td>';
			tr += '		<td headers="del-landing" class="del-landing">';
			tr += '			<input type="button" name="save" id="save-landing" value="' + rwdActionsEnregistrer + '" onclick="return updateLanding(' + id +');" />';
			tr += '			<a class="cancel" href="#" onclick="return loadLanding();">' + rwdActionsAnnuler + '</a>';
			tr += '		</td>';
			
			$('#tr-' + id).html( tr );
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet de mettre à jour un palier.
 */
function updateLanding( id ){
	message();
	
	var val = $('#landing-nb-' + id).val();
	var pts = $('#rwa-usr-' + id).val();
	var sp_pts = $('#rwa-sp-' + id).val();
	
	var params  = '?updatelanding=1&cfg=' + id + '&val=' + val + '&pts=' + pts + '&sp_pts=' + sp_pts;
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php' + params, function(result){
		
		if( result.done ){
			message( rwdActionsSuccesMajPalier, false, 'landings' );
			loadLanding();
		} else {
			message( rwdActionsErreurMajPalier, true, 'landings' );
		}
		
	});
	
	return false;
}
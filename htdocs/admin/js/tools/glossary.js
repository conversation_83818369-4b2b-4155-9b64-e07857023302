function trim(myString){
	return myString.replace(/^\s+/g,'').replace(/\s+$/g,'')
} 

$(document).ready(
	function(){
		// Gestions d'action aux claviers
		var isCtrl = false; 
		document.onkeyup=function(e){ 
			if(e.which == 17) isCtrl=false; 
		}
		document.onkeydown=function(e){ 
			if(e.which == 17) isCtrl=true; 
			if(e.which == 83 && isCtrl == true) { 
				$("#form-add input[type='submit']").click();
				return false;
			} 
		} 

		$("#form-add input[type='submit']").click(function(){
			if(!trim($("#form-add #wrd-name").val())) {
				$("#form-add #wrd-name").css('border-color','#f00');
				return false;
			}
			if(!trim($("#form-add #wrd-desc").val())) {
				$("#form-add #wrd-desc").css('border-color','#f00');
				return false;
			}
			return true;
		});
	}
);

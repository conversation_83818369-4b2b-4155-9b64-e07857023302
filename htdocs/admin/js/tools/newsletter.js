var currentAjaxRequest = false;
var hasPushHistory = false;

$(document).ready(function() {
	if( !$.browser.msie ){
		$(window).bind('popstate', function(event){
			if( !hasPushHistory ){
				return ; 
			}
			
			if( currentAjaxRequest ) currentAjaxRequest.abort();
			
			if( event.originalEvent.state && event.originalEvent.state.ajax ){
				var tmp = event.originalEvent.state;
				reloadList( tmp.jsonurl, tmp.page, tmp.pages, tmp.type, tmp.ocp, tmp.oc, tmp.email, tmp.dateStart, tmp.dateEnd, tmp.segId, false );
			}else {
				window.location = location.href;
			}				
		});	
	}

	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});
		
	});
});

$('#nlr-prd-select').on('click', function(){
	displayPopup(newsletterAjouterProduit, '', '/admin/ajax/catalog/ajax-product-select.php?cat='+$('#prd-ref-cat').val()+'&input_id_cat_id=prd-ref-cat&input_id_ref=prd-ref&multiselect=0&btn_text=Sélectionner', false, 800, 400);
});

$('#nlr-prd-consult-select').on('click', function(){
	displayPopup(newsletterAjouterProduit, '', '/admin/ajax/catalog/ajax-product-select.php?cat='+$('#prd_consult_cat').val()+'&input_id_cat_id=prd_consult_cat&input_id_prd_id=prd_consult&input_id_ref=prd_consult_ref&multiselect=0&btn_text=Sélectionner', false, 800, 400);
});

$('#nlr-prd-cancelled-select').on('click', function(){
	displayPopup(newsletterAjouterProduit, '', '/admin/ajax/catalog/ajax-product-select.php?cat='+$('#prd_cancelled_cat').val()+'&input_id_cat_id=prd_cancelled_cat&input_id_prd_id=prd_cancelled&input_id_ref=prd_cancelled_ref&multiselect=0&btn_text=Sélectionner', false, 800, 400);
});

function updateCat( id, idParent, catname, catType ){
	if( catType==2 ){
		$("#cat_consult").val(id);
		$("#cat_consult_parent").val(idParent);
		$("#nlr-cat-consult-name").val(catname);
		$("#nlr-add-rule-cat-consult").attr('checked', 'checked');
	}else if( catType==3 ){
		$("#cat_cancelled").val(id);
		$("#cat_cancelled_parent").val(idParent);
		$("#nlr-cat-cancelled-name").val(catname);
		$("#nlr-add-rule-cat-cancelled").attr('checked', 'checked');
	}else if( catType==1 ){
		$("#prd_cat").val(id);
		$("#prd_cat_parent").val(idParent);
		$("#nlr-cat-name").val(catname);
		$("#nlr-add-rule-cat").attr('checked', 'checked');
	}
	
	hidePopup();
}

function parent_select_prd(id, name, ref, catId, inputIdPrdId, inputIdName, inputIdRef, inputIdCatId){
	if( inputIdPrdId!='undefined' && inputIdPrdId!='' )
		$('#'+inputIdPrdId).val(id);
	if( inputIdName!='undefined' && inputIdName!='' )
		$('#'+inputIdName).val(name);
	if( inputIdRef!='undefined' && inputIdRef!='' )
		$('#'+inputIdRef).val(ref);
	if( inputIdCatId!='undefined' && inputIdCatId!='' )
		$('#'+inputIdCatId).val(catId);
}

function pushHistory( url, jsonurl, page, pages, type, ocp, oc, email, dateStart, dateEnd, segId ){
	if( !$.browser.msie ){
		hasPushHistory = true;
		history.pushState(
			{ 
				jsonurl: jsonurl, 
				ajax: true,
				page:page, 
				pages:pages, 
				type:type,
				ocp:ocp, 
				oc:oc, 
				email:email, 
				dateStart:dateStart, 
				dateEnd:dateEnd,
				segId:segId
			}, 
			'', 
			url
		);
	}
}

function nlrSwitchTablePage( page, pages, type, ocp, oc, email, dateStart, dateEnd, segId ){
	// Place le scroll en haut à gauche de l'écran
	swichPageLoad(5);
	
	if( (type==-1 && ocp==-1) || email==-1 ){
		type = $("#type").val();
		ocp = $("#ocp").val();
		if( email==-1 ){
			email = $("#search-email").val();
		}else{
			$("#search-email").val('');
		}
	}

	type = type.toString().replace('t-', '');

	reloadList( '/admin/ajax/newsletters/json-newsletters.php', page, pages, type, ocp, oc, email, dateStart, dateEnd, segId, true);
	return false;
}

function reloadList( url, page, pages, type, ocp, oc, email, dateStart, dateEnd, segId, pushToHistory ){

	$('#date-start').val( dateStart );
	$('#date-end').val( dateEnd );
	$('#seg_id').val( segId );

	var uri_data = 'p='+page+'&type='+type+'&ocp='+ocp+'&oc='+ocp+'&search-email='+email+'&limit=50&date-start='+dateStart+'&date-end='+dateEnd+'&seg_id='+segId;
	
	// Requête AJAX pour le changement de page
	currentAjaxRequest = $.ajax({
		type: "POST",
		url: url,
		data: uri_data,
		dataType: 'json',
		success: function(msg){
			if( pushToHistory ){
				pushHistory( '/admin/tools/newsletter/list.php?'+uri_data, url, page, pages, type, ocp, oc, email, dateStart, dateEnd, segId);
			}

			pages = Math.ceil( msg['nb_mails']/50 );
			var caption = newsletterInscriptionNewsletter+msg['nb_mails'].toLocaleString() + ' ';
			
			// Détermine le caption du tableau
			switch( type ){
				case '1' : 
					caption += msg['nb_mails']>1 ? newsletterInscriptionsNonTerminees : newsletterInscriptionNonTerminee ; break;
				case '2' : 
					caption += msg['nb_mails']>1 ? newsletterInscrits : newsletterInscrit; break;
				case '3' :
					caption += msg['nb_mails']>1 ? newsletterDesinscriptionsNonTerminees : newsletterDesinscriptionNonTerminee; break;
				case '4' :
					caption += msg['nb_mails']>1 ? newsletterDessincrits : newsletterDessincrit; break;
				default :
					caption += msg['nb_mails']>1 ? newsletterAdressesMails : newsletterAdresseMail; break;
			}
			
			caption += ')';
			$("#site-content table caption").html(caption);
			
			var html = '';
			
			if( msg['nb_mails']==0 ){
				html = '<tr><td colspan="4">' + newsletterAucuneAdresse + '</td></tr>';
			} else {
				var mail = '';
				// Créé le contenu du tableau
				for( var i=0 ; i<msg['mail'].length ; i++ ){
					// Récupère les informations sur l'adresse mail en cours
					mail = msg['mail'][i];
					
					html += '	<tr>';
					html += '		<td headers="sel"><input type="checkbox" class="checkbox" name="sub[]" value="'+mail.id+'" /></td>';
					if(mail.type_cat == "" || mail.type_cat == "email"){
						html += '		<td headers="email"><a href="edit.php?id='+mail.id+'&amp;cat='+ocp+'" title="' + newsletterAfficherFicheInscription+htmlspecialchars(mail.email)+'">'+htmlspecialchars(mail.email)+'</a></td>';
					}else{
						html += '		<td headers="phone">' + mail.phone + '</td>';
					}
					html += '		<td headers="state">';
						if( mail.uninscript_confirmed ){
							html += '	<span class="nlr-uninscript">' + newsletterDesincritDepuis+mail.uninscript_confirmed+'</span>';
						} else if( mail.inscript_confirmed ){
							if( mail.uninscript_requested )
								html += '<span class="nlr-pre-uninscript">' + newsletterInscritDepuis+mail.inscript_confirmed+'</span>';
							else
								html += '<span class="nlr-inscript">' + newsletterInscritDepuis+mail.inscript_confirmed+'</span>';
						} else {
							html += '	<span class="nlr-pre-inscript">' + newsletterDessincritDebute.replace('#param[date]#',mail.inscript_requested) + '</span>';
						}
					html += '		</td>';
					html += '		<td>'+( mail.usr_id ? '<a href="../../customers/edit.php?usr='+mail.usr_id+'">Oui</a>' : 'Non' )+'</td>';
					$("#cat").remove();
					if( ocp==0 ){
						$("#account").after('<th id="cat">Newsletter</th>');
						html += '	<td>'+mail.cat+'</td>';
					}
					html += '	</tr>';
				}
			}
			
			// Affiche le contenu
			$("#site-content table tbody").html(html);
			
			// Créé la pagination
			$("#pagination").html(switchPage(page, pages, 3, 3, 2, 3, 'nlrSwitchTablePage', ', '+type+', '+ocp+', '+ocp+', \''+email+'\', \''+dateStart+'\', \''+dateEnd+'\', \''+segId+'\'', 'list.php', ''));
			
		},
		error: function(){
			return true;
		}
	});
}

function nlr_cancel(){
	window.location.href='index.php';
	return false;
}

function nlr_uninscript(){
	return window.confirm(newsletterConfirmDesinscription);
}
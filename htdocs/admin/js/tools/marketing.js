$(document).ready(function(){
	var search = window.location.search.replace('?','').split('&').reduce(function(r,item){ var t = item.split('='); r[t[0]] = t[1]; return r;  },{});

	$('input.date').each(function(){
		var $that = $(this);
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$that.DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$that.val(formated);
					$that.DatePickerHide();
				}
			}
		});
	});
	var executeCampaign = function(e){
		e.preventDefault();
		$.getJSON("/admin/ajax/marketing/ajax-marketing.php?function=executeCampaign&cpg="+search.cpg, function(json){
			if( json.success ){
				$(".notif").removeClass("error").addClass("success").text(marketingSMSEnvoye);
			}else{
				$(".notif").removeClass("success").addClass("error").text(marketingErreurSMS);
			}
		});
	};

	$(".exec-manuel").on("click", "#exec-campaign", executeCampaign);

	if( search.tab != 'undefined' ){
		switch(search.tab){
			case 'client':
				mktUsers.init();
				break;
			case 'stats':break;
			case 'general':
			default:
				mktGeneral.init();
		}
	}
});

var mktUsers = {

	init: function() {
		this.cpg = $("#cpg_id").val();
		this.ajaxUrl = '/admin/ajax/marketing/ajax-marketing.php';
		this.usr_id = $("#usr_id").val();
		this.usr_email = $("#usr_email").val();
		this.currentAjaxRequest = false;
		this.IncludeCount = 0;
		this.ruleType = '';
		this.value = [];
		this.rules = [];
		this.uniqRules = {};
		this.DOMCaching();
		this.getRules();
		this.bindEvents();
	},

	DOMCaching: function(){
		this.$rulesContainer = $("#cpg-rules").find("tbody");
		this.$radioRules = $("input[type=radio][name=cpg-add-rule]");
		this.$includeCount = $(".customers-count-pmt");
		this.$usr_input = $('#value_usr');
	},

	getRules: function(){
		$.getJSON(this.ajaxUrl + '?function=getRules&cpg='+this.cpg, function(json){
			$.each(json,function(i,rule){
				this.pushRule(rule);
			}.bind(this));
			this.render();
		}.bind(this));
	},

	bindEvents: function(){
		$("#cpg-usr-select, #cpg-usr-name").on("click", this.searchUser );
		$(".cpg-input").on("change",this.selectRule.bind(this));
		$("#mkt-addin-rule").on("click", function(e){
			e.preventDefault();
			this.addRule(true);
		}.bind(this));
		$("#mkt-addout-rule").on("click", function(e){
			e.preventDefault();
			this.addRule(false);
		}.bind(this));

		$("#cpg-del-rule").on("click", this.delRules.bind(this));
		$("#cpg-usr-copy").on("click", this.copyRules.bind(this));
	},

	resetFields: function(){
		$("input[name=cpg-add-rule]:checked").parent().find('input').each(function(){
		});
		$("input[name=cpg-add-rule]:checked").parent().find("select").val("");
		$("input[name=cpg-add-rule]:checked").parent().find("textarea").val("");
		this.resetTypes();
	},
	selectRule: function(e){
		this.resetTypes();
		this.ruleType = $(e.target).data('rule');
		if( this.ruleType == 'usr' ){
			this.value = [this.$usr_input.val()];
		}else if( this.ruleType == 'mobile'){
			this.value = $(e.target).val().split('\n');
		}else{
			this.value= [$(e.target).val()];
		}
		this.$radioRules.get($(e.target).parent().index()).checked = true;
	},
	
	resetTypes: function(){
		$.each(this.$radioRules,function(k,v){
			v.checked = false;
		})
	},

	searchUser: function(){
		displayPopup( marketingRechercheClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&mobile=true&prf=' + 0 + '&input_id_usr_id=value_usr&input_id_usr_email=cpg-usr-name&callback=mktUsers.parent_select_user' );
	},

	parent_select_user: function ( id, email ){
		$('#cpg-add-rule-prf, #cpg-add-rule-usr').removeAttr('checked');
		this.$usr_input.val( id );
		$('#cpg-usr-name').val( email ).trigger("change");
		$('#elem-type-user').val( 'usr' );
		$('#cpg-add-rule-usr').attr('checked', 'checked');
	},

	addRule: function(include){
		var Include = include == true ? 1 : 0;
		if( this.ruleType == 'copy' ){
			return false;
		}
		$.each(this.value, function(i, value){
			var req = $.ajax({
				 url: this.ajaxUrl,
				 method: "get",
				 data: 'function=addRule&ruleType='+this.ruleType+'&value='+value+'&include='+Include+'&cpg='+this.cpg,
				 dataType:'json'
			 });

			 req.success(function(json){
				 if( json.value ){
					 this.pushRule(json);
					 this.render();
				 }
			 }.bind(this));
		}.bind(this));

		this.resetFields();
	},

	pushRule: function(rule){
		if( typeof this.uniqRules[rule.value+rule.type] == "undefined" ){
			var i = this.rules.push(rule)-1;
			this.uniqRules[rule.value+rule.type] = { include:rule.include, index:i};
		}else if( this.uniqRules[rule.value+rule.type].include != rule.include ){
			var i = this.uniqRules[rule.value+rule.type].index;
			this.rules[i] = rule;
		}
	},

	delRules: function(e){
		e.preventDefault();
		if( $(".checkbox").length == $(".checkbox:checked").length ){
			$.getJSON(this.ajaxUrl + '?function=delRules&cpg='+this.cpg, function(json){
				if( json.success == true ){
					this.rules.length = 0;
					this.render();
				}
			}.bind(this))
		}else{
			$(".checkbox:checked").each(function(k,el){
				if( $(el).attr("id") == "del-all" ){
					return true;
				}
				$.getJSON(this.ajaxUrl + '?function=delRule&cpg='+this.cpg+'&include='+$(el).data("include")+'&ruleType='+$(el).data("type")+'&value='+$(el).data("value"), function(json){
					if( json.success == true ){
						var index = $(el).parent().parent().index();
						delete this.uniqRules[$(el).data("value")+$(el).data("type")];
						this.rules.splice(index,1);
						this.render();
					}
				}.bind(this))
			}.bind(this));
		}
	},

	copyRules: function(e){
		e.preventDefault();
		$.getJSON(this.ajaxUrl+"?function=copyRules&cpg="+this.cpg+"&cpg_src="+this.value,function(json){
			if( json.success ){
				this.resetFields();
				this.getRules();
			}
		}.bind(this));
	},

	getIncludeCount: function(){
		$.getJSON(this.ajaxUrl+"?function=getIncludeCount&cpg="+this.cpg, function(json){
			if( json.count ){
				this.IncludeCount = json.count;
				this.$includeCount.text(this.IncludeCount);
				if( json.count > 0 ){
					$(".exec-manuel").html('<button id="exec-campaign">' + marketingEnvoyer + '</button><p class="notice">' + marketingGardePage + '</p>');
				}else{
					$(".exec-manuel").html("");
				}
			}
		}.bind(this))
	},

	render: function(){
		if( typeof this.rules == "undefined" ){
			return false;
		}
		this.getIncludeCount();
		this.$rulesContainer.html("");
		this.rules.map(function(rule){
			var label = $("<td>").append(
				$("<input>").attr({
					name:"del[]",
					id:rule.type+"-"+rule.value,
					type:"checkbox"
				})
					.addClass("checkbox")
					.val(rule.type+"-"+rule.value)
					.attr("data-type",rule.type)
					.attr("data-value",rule.value)
					.attr("data-include",rule.include)
			);
			var text = "("+(rule.include == true || rule.include == "1" ? "+" : "-" )+")"+ rule.label;
			var line = $("<tr>").append(label).append($("<td>").text(text));
			this.$rulesContainer.append(line);
			return rule;
		}, this);
		this.$includeCount.text(this.IncludeCount);
	}
};

var mktGeneral = {
	init: function(){
		this.cacheDom();
		this.sendStatus = '';
		this.bindEvents();
	},

	cacheDom: function(){
		this.$period = $(".period-picker");
		this.$select = $("#period");
		this.$form = $("#cpg-info");
		this.$period_input = $("input[name=period]");
		this.$container = $(".marketing");
		this.$errors = $(".error").length > 0 ?  $(".error") : $("<div>").addClass("error");
		this.$notice = $(".notice").length > 0 ?  $(".notice") : $("<div>").addClass("notice").append($("<p>").text(marketingCampagneManuel));
		this.$msg = $("#msg");
		this.$smsCounter = $("#sms-counter");
	},

	bindEvents: function(){
		this.$select.on("change", this.changeSendStatus.bind(this));
		this.$form.on("submit", function(){
			if( this.sendStatus == "auto" && this.$period_input.val().trim() == '' ){
				this.renderErrors([marketingSelectDateEnvoi]);
				return false;
			}
			return true;
		}.bind(this));
		this.$msg.smsArea()
	},

	renderErrors: function(errors){
		this.$errors.html("");
		$.each(errors, function(k,error){
			this.$errors.append($("<p>").text(error));
		}.bind(this));
		this.$container.before(this.$errors);
	},

	changeSendStatus: function(e){
		this.sendStatus = e.target.value;
		if( this.sendStatus === "auto" ){
			this.$period.removeClass("period-hide");
			this.$notice.hide();
		}else{
			this.$period.addClass("period-hide");
			this.$notice.show();
		}
		this.$container.before(this.$notice);
	},
};


// smsArea plugin
(function($){
$.fn.smsArea = function(options){

	var e = this,
		cutStrLength = 0,

		s = $.extend({

			cut: true,
			maxSmsNum: 3,
			interval: 400,
			isMarketingInput: $("#is_marketing"),
			isMarketing: $("#is_marketing").is(":checked"),
			counters: {
				message: $('#smsCount'),
				charMax: $('#smsMaxLength'),
				character: $('#smsLength')
			},

			lengths: {
				ascii: [160, 306, 459],
				unicode: [70, 128, 192]
			}
		}, options);

	s.isMarketingInput.change(function(){
		s.isMarketing = $(this).is(":checked");
		e.trigger("keyup");
	});


	e.keyup(function(){

		clearTimeout(this.timeout);
		this.timeout = setTimeout(function(){

			var
				smsType,
				smsLength = 0,
				smsCount = -1,
				charsLeft = 0,
				text = e.val(),
				isUnicode = false,
				valideChars = [
					'@','Δ',' ','0','¡','P','¿','p',
					'£','_','!','1','A','Q','a','q',
					'$','Φ','"','2','B','R','b','r',
					'¥','Γ','#','3','C','S','c','s',
					'è','Λ','¤','4','D','T','d','t',
					'é','Ω','%','5','E','U','e','u',
					'ù','Π','&','6','F','V','f','v',
					'ì','Ψ','\'','7','G','W','g','w',
					'ò','Σ','(','8','H','X','h','x',
					'Ç','Θ',')','9','I','Y','i','y',
					"\n",'Ξ','*',':','J','Z','j','z',
					'Ø',"\x1B",'+',';','K','Ä','k','ä',
					'ø','Æ',',','<','L','Ö','l','ö',
					"\r",'æ','-','=','M','Ñ','m','ñ',
					'Å','ß','.','>','N','Ü','n','ü',
					'å','É','/','?','O','§','o','à',
					'\n','[',']','\\','^','{','}','|',
					'€','~'
				];

			for(var charPos = 0; charPos < text.length; charPos++){
				smsLength += 1;
				//!isUnicode && text.charCodeAt(charPos) > 127 && text[charPos] != "€" && (isUnicode = true)
				if(valideChars.indexOf(text[charPos]) == -1 ){
					isUnicode = true;
				}
			}

			if(isUnicode){
				smsType = s.lengths.unicode;
			}else {
				smsType = s.lengths.ascii;
			}
			if( !s.isMarketing ){
				smsLength += 11;
			}
			for(var sCount = 0; sCount < s.maxSmsNum; sCount++){

				cutStrLength = smsType[sCount];
				if(smsLength <= smsType[sCount]){

					smsCount = sCount + 1;
					charsLeft = smsType[sCount] - smsLength;
					charsMax = smsType[sCount] / smsCount;
					break
				}
			}

			if(s.cut) e.val(text.substring(0, cutStrLength));
			smsCount == -1 && (smsCount = s.maxSmsNum, charsLeft = 0);

			s.counters.message.html(smsCount);
			s.counters.charMax.html(charsMax);
			s.counters.character.html(charsLeft);

		}, s.interval)
	}).keyup()
}}(jQuery));



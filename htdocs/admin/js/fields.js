var configFieldLanguage = '';

$(document).ready(
	function(){
		if( typeof $('#categories') != 'undefined' && $('#categories').length ){
			riaSortable.create({
				'table' : $('#categories'),
				'beforeUpdate' : function(param) {
					if ($('#line-' + param.source).attr('class') !== $('#line-' + param.target).attr('class')) {
						this.cancel();
						return false;
					}
				},
				'url' : '/admin/ajax/config/ajax-field-category-position-update.php'
			});
		}

		if( typeof $('table[data-cls]') != 'undefined' ){
			var url = getParamsInUrl(window.location.search.replace('?',''));
			riaSortable.create({
				'table'		:	$('table[data-cls]'),
				'url'		:	'/admin/ajax/fields/ajax-classes-fields-position-update.php?cls='+url.cls
			});
		}

		if( typeof $('#values') != 'undefined' && $('#values').length ){
			riaSortable.create({
				'table' : $('#values'),
				'url' : '/admin/ajax/config/ajax-field-value-position-update.php'
			});
		}

		if( typeof $('#fields') != 'undefined' && $('#fields').length ){
			riaSortable.create({
				'table' : $('#fields'),
				'url' : '/admin/ajax/config/ajax-field-position-update.php?mdl=' + $('#mdl-id').val()
			});
		}

		if( typeof $('#models') != 'undefined' && $('#models').length ){
			riaSortable.create({
				'table' : $('#models'),
				'beforeUpdate' : function(param) {
					if ($('#line-' + param.source).attr('class') !== $('#line-' + param.target).attr('class')) {
						this.cancel();
						return false;
					}
				},
				'url' : '/admin/ajax/config/ajax-field-model-position-update.php'
			});
		}

		$('#classtype, #type').change(function() {
			if($('#classtype').val() == 87 && $('#type').val() == 2){
				$('#edit_text').removeClass('none');
			}else{
				$('#edit_text').addClass('none');
			}
		});
	}

	
).delegate(
	'#parent-name, #search-obj-parent', 'click', function(){
		displayPopup( fieldsDiplayPopupSelectObjet, '', '/admin/config/fields/classes/popup-select-parent.php?cls=' + encodeURIComponent($('#cls_id').val()) + '&obj=' + encodeURIComponent($('#obj_id').val()) );
		return false;
	}
).delegate(
	'.riapicker .selectorview', 'click', function(){
		if($(this).parent().find('.selector').css('display')=='none'){
			$('.selector').hide();
			$(this).parent().find('.selector').show();
		}else{
			$(this).parent().find('.selector').hide();
		}
	}
).delegate(
	'#selectorclass .selector a', 'click', function(){
		$('#cls_id').val( $(this).attr('name').replace('cls-', '') );
		window.location.href = '/admin/config/fields/segments/index.php?cls=' + $('#cls_id').val();
	}
).delegate(
	'#selectclass .selector a', 'click', function(){
		$('#cls_id').val( $(this).attr('name').replace('cls-', '') );
		return reloadListFields();
	}
).delegate(
	'#selecttypes .selector a', 'click', function(){
		$('#type_id').val( $(this).attr('name').replace('type-', '') );
		return reloadListFields();
	}
).delegate(
	'#selectcategories .selector a', 'click', function(){
		$('#cat_id').val( $(this).attr('name').replace('cat-', '') );
		return reloadListFields();
	}
).delegate(
	'#classtype', 'change', function(){
		$.get('/admin/ajax/fields/ajax-fields.php?getcat=1&cls=' + $(this).val(), function( data ){
			$('#cat').html('<option value="">' + fieldsOptionSelectCategorie + '</option>' + data );
		});
	}
).delegate(
	'.fld_pointer_add_btn', 'click', function(){
		var clsID = $(this).parent().find('[name=pointer-cls-id]').val();
		var inputID = $(this).parents('td').find('select').attr('id');

		var title = '';
		var popup = '';

		switch( parseInt(clsID) ){
			case 1: {
				title = fieldsSelectProduit;
				popup = '/admin/ajax/catalog/ajax-product-select.php?add_field=1&input_id_prd_id=' + inputID;
				break;
			}
			case 3: {
				title = fieldsSelectCategorie;
				popup = '/admin/catalog/popup-categories.php?add_field=1&input_id_cat_id=' + inputID;
				break;
			}
			case 5: {
				title = fieldsSelectMarque;
				popup = '/admin/ajax/catalog/popup-brands-select.php?inputID=' + inputID + '&inField=1';
				break;
			}
			case 6: {
				title = fieldsSelectMagasin;
				popup = '/admin/config/livraison/stores/popup-stores.php?input_id_store_id=' + inputID + '&callback=fldSelectedStores&select=checkbox';
				break;
			}
		}

		displayPopup( title, '', popup );
		return false;
	}
).delegate(
	'.fld_pointer_delete_btn', 'click', function(){
		$(this).parents('td').find('select option:selected').remove();
	}
).delegate(
	'[name="savefields"], [name="savefields_stay"]', 'click', function(){
		$('.fld-form-reference select[multiple="multiple"] option:selected').removeAttr('selected');
		
		$('select[multiple]').each(function(){
			var vals = '';
			$(this).find('option').each(function(){
				vals += ( $.trim(vals) != '' ? ',' : '' ) + $(this).val();
			});

			var name = $(this).attr('name');
			$(this).before('<input type="hidden" name="' + name + '" value="' + vals + '" />');
		});
	}
).delegate(
	"#popup_ria_shadow", 'click', function(){
		if( typeof $('table#fields') != 'undefined' && $('table#fields').length ){
			window.parent.closeShadowbox();
		}
	}
).delegate( // Bouton Parcourir (onglet Avancé, rechercher un compte Utilisateur)
	'.fld_usr_browse_btn', 'click', function(){
		var input_id = $(this).parents('td').find('input[type=hidden]').attr('id');
		var input_email = $(this).parents('td').find('input[type=text]').attr('id');
		displayPopup( 'Sélectionner un compte', '', '/admin/ajax/orders/ncmd-customers-change.php?input_id_usr_id='+input_id+'&input_id_usr_email='+input_email+'&callback=default_select_user&no-add=1' );
	}
).delegate( // Champ texte (onglet Avancé, rechercher un compte Utilisateur)
	'.fld_usr_browse_txt', 'focus', function(){
		var input_id = $(this).parents('td').find('input[type=hidden]').attr('id');
		var input_email = $(this).parents('td').find('input[type=text]').attr('id');
		displayPopup( 'Sélectionner un compte', '', '/admin/ajax/orders/ncmd-customers-change.php?input_id_usr_id='+input_id+'&input_id_usr_email='+input_email+'&callback=default_select_user&no-add=1' );
	}
);

function fldSelectedStores( inputID, stores ){
	var arStores = new Array();
	$('#' + inputID).find('option').each(function(){
		arStores.push( $(this).val() );
	});

	var strHtml = '';
	for( const str in stores ){
		if( !inArray(stores[str].id, arStores) ){
			strHtml += '<option value="' + stores[str].id + '">' + stores[str].name + '</option>';
		}
	}

	$('#' + inputID).append( strHtml );
	hidePopup();
}

function addObjectInField( objID, objName, inputID ){
	var html = '<option value="' + objID + '">' + objName + '</option>';
	$('#' + inputID).append( html );
}

function reloadListFields(){
	var clsID = $('#cls_id').val();
	var catID = $('#cat_id').val();
	var typeID = $('#type_id').val();

	var url = '/admin/config/fields/fields/index.php';
	if( parseInt(clsID)>0 ){
		url += ( url.match(/\?/g) ? '&' : '?' ) + 'cls=' + clsID;
	}
	if( parseInt(catID)>0 ){
		url += ( url.match(/\?/g) ? '&' : '?' ) + 'cat=' + catID;
	}
	if( parseInt(typeID)>0 ){
		url += ( url.match(/\?/g) ? '&' : '?' ) + 'type=' + typeID;
	}
	
	window.location.href = url;
}

function addSelectParent( id, name ){
	$('#parent-id').val( id );
	$('#parent-name').val(name);
	hidePopup();
	$('.error').remove();
}
function displayNotice(){
	if( $('#site-content .notice .more-hide').is(':visible') ){
		$('#site-content .notice .more').show();
		$('#site-content .notice .more-hide').hide();
		$('#site-content .notice .more-point').show();
		$('#site-content .notice .more-info').hide();
	} else {
		$('#site-content .notice .more').hide();
		$('#site-content .notice .more-hide').show();
		$('#site-content .notice .more-point').hide();
		$('#site-content .notice .more-info').show();
	}
	
	return false;
}
/* Unités de mesure */
function fldUnitAdd(){
	window.location.href = 'edit.php';
	return false;
}
function fldUnitCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function fldUnitConfirmDel(){
	return window.confirm(fieldsConfirmSuppressionUniteMesure);
}
function fldUnitConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionUniteMesureMultiple);
}
function fldClassesConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionClassMultiple);
}
function fldObjectsConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionObjetMultiple);
}
function fldClasseConfirmDel(){
	return window.confirm(fieldsConfirmSuppressionClasse);
}
function fldObjectsConfirmDel(){
	return window.confirm(fieldsconfirmSuppressionObjet);
}
function fldFieldConfirmClear(){
	return window.confirm(fieldsConfirmMsgReinitialistaionUniteMesure);
}
function fldUnitValidForm(frm){
	if( !trim(frm.name.value) ){
		alert(fieldsConfirmNomUniteMesure);
		frm.name.focus();
		return false;
	}
	if( !trim(frm.symbol.value) ){
		alert(fieldsConfirmSymboleUniteMesure);
		frm.symbol.focus();
		return false;
	}
}

/* Catégories de champs */
function fldCatAdd(){
	window.location.href = 'edit.php';
	return false;
}
function fldCatCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function fldCatConfirmDel(){
	return window.confirm(fieldsConfirmSuppressionCategorie);
}
function fldCatConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionCategorieMultiple);
}
function fldCatValidForm(frm){
	if( !trim(frm.name.value) ){
		alert(fieldsAlertEditionCategorie);
		frm.name.focus();
		return false;
	}
}

/* Modèles de saisie */
function fldModelAdd(){
	window.location.href = 'edit.php';
	return false;
}
function fldModelCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function fldModelConfirmDel(){
	return window.confirm(fieldsConfirmSuppressionModeleSaisie);
}
function fldModelConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionModeleSaisieMultiple);
}
function fldModelValidForm(frm){
	if( !trim(frm.name.value) ){
		alert(fieldsAlertEditionModeleSaisie);
		frm.name.focus();
		return false;
	}
}

/**	Cette fonction réalise l'ouverture de la popup affichant la liste des champs que l'on peut ajouter à un modèle de saisie.
 *	@param mdl Obligatoire, Identifiant du modèle de saisie
 */
function fldModelAddField( mdl ){
	displayPopup(fieldsChoixChamps, '', '/admin/config/fields/models/fields.php?mdl=' + mdl, '', 800, 500 );
	return false;
}

function closeShadowbox(){
	hidePopup();
	$('#edit').submit();
}

/* Champs personnalisés */
function fldFieldCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function fldFieldConfirmDel(){
	return window.confirm(fieldsConfirmSuppressionChampsPersonnalise);
}

function fldFieldLoadValues() {
	var lng = $(this).attr('href').substring(1);

	configFieldLanguage = lng;

	$.ajax({
		type: "POST",
		url: "/admin/config/fields/fields/ajax-fields.php",
		data: "restrictedValue=1&lng="+lng+"&fld="+$('#fld-restrected').val(),
		async:false,
		success: function(res){ 
			$('#property-values select').html( res );
		}
	});
	return false;
}

function fldFieldConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionChampsPersonnaliseMultiple);
}
function fldFieldValidForm(frm){
	if( !trim(frm.name.value) ){
		alert(fieldsAlertEditionChampsPersonnaliseNom);
		frm.name.focus();
		return false;
	}
	if( !trim(frm.type.value) ){
		alert(fieldsAlertEditionChampsPersonnaliseType);
		frm.type.focus();
		return false;
	}
	if( !trim(frm.cat.value) ){
		alert(fieldsAlertEditionChampsPersonnaliseCategorie);
		frm.cat.focus();
		return false;
	}
	switch( frm.type.value ){
		case '1':
		case '2':
			if( frm.min.value!='' && !validInt(frm.min.value) ){
				alert(fieldsAlertMinEntier);
				frm.min.select();
				frm.min.focus();
				return false;
			}
			if( frm.max.value!='' && !validInt(frm.max.value) ){
				alert(fieldsAlertMaxEntier);
				frm.max.select();
				frm.max.focus();
				return false;
			}
			if( frm.min.value!='' && frm.max.value!='' ){
				if( parseInt(frm.min.value)>parseInt(frm.max.value) ){
					alert(fieldsAlertMinSupMaxEntier);
					frm.min.select();
					frm.min.focus();
					return false;
				}
			}
			break;
		case '3':
		case '4':
			if( frm.min.value!='' && !validFloat(frm.min.value) ){
				alert(fieldsAlertMinFlottant);
				frm.min.select();
				frm.min.focus();
				return false;
			}
			if( frm.max.value!='' && !validFloat(frm.max.value) ){
				alert(fieldsAlertMinFlottant);
				frm.max.select();
				frm.max.focus();
				return false;
			}
			if( frm.min.value!='' && frm.max.value!='' ){
				if( parseFFloat(frm.min.value)>parseFFloat(frm.max.value) ){
					alert(fieldsAlertMinSupMaxFlottant);
					frm.min.select();
					frm.min.focus();
					return false;
				}
			}
			if( frm.type.value=='4' ){
				if( frm.precision.value!='' && !validInt(frm.precision.value) ){
					alert(fieldsAlertPrecisionNonValide);
					frm.precision.select();
					frm.precision.focus();
					return false;
				}
			}
			break;
	}
}

var addValues =  new Array();

var shadowBoxValues;
function fldFieldModifyValues(fld, inConfig){
	addValues =  new Array();

	if (typeof inConfig == 'undefined') {
		inConfig = false;
	}

	displayPopup(fieldsModifValeurAutorise, '', '/admin/config/fields/fields/values.php?fld=' + fld, 'reloadFieldsValues(' + fld + ', ' + inConfig + ')');
	return false;
}

function reloadFieldsValues( fld, inConfig ){
	var valCheck = '';
	
	if( $('#fields-' + fld + ' input[type=checkbox]:checked').length ){
		$('#fields-' + fld + ' input[type=checkbox]:checked').each(function(){
			valCheck += '&val_check[]=' + $(this).val();
		});
	}else if( $('#fields-' + fld + ' option:selected').length ){
		valCheck += '&val_check[]=' + $('#fields-' + fld + ' option:selected').val();
	}
	
	if( addValues.length > 0 ){
		for( const v in addValues ){
			valCheck += '&val_check[]=' + addValues[v];
		}
	}

	var params  = '?reload-values=1&fld=' + fld + valCheck;
		params += '&in-config=' + (inConfig ? '1' : '0');
		params += $.trim(configFieldLanguage) != '' ? '&lng=' + configFieldLanguage : '';

	// Recharge les valeurs authorisées
	$.get('/admin/ajax/fields/ajax-fields.php' + params, function( result ){
		if (inConfig) {
			$('#values').html(result);
		}else{
			$('#fields-' + fld).html( result );
		}
	});
	
	hidePopup();
	return false;
}
function fldFieldTranslateValues(fld, inConfig){
	if (typeof inConfig == 'undefined') {
		inConfig = false;
	}

	displayPopup(fieldsTraductionValeur, '', '/admin/config/fields/fields/translate.php?fld=' + fld, 'reloadFieldsValues(' + fld + ', ' + inConfig + ')', 450, 450);
	return false;
}
function fldRestrictedValueConfirmDelList(){
	return window.confirm(fieldsConfirmSuppressionValeurAutorise);
}
function fldUpdateControls(frm){
	switch( frm.type.value ){
		case '1': // Texte court
		case '2': // Texte long
			document.getElementById('property-min').style.display = '';
			document.getElementById('property-max').style.display = '';
			document.getElementById('property-precision').style.display = 'none';
			document.getElementById('property-values').style.display = 'none';
			document.getElementById('property-unit').style.display = 'none';
			document.getElementById('lbl-min').innerHTML = fieldsLongeurMin + ' :';
			document.getElementById('lbl-max').innerHTML = fieldsLongeurMax + ' :';
			break;
		case '3': // Nombre entier
		case '4': // Nombre à virgule flottante
			document.getElementById('property-min').style.display = '';
			document.getElementById('property-max').style.display = '';
			document.getElementById('property-precision').style.display = frm.type.value==4 ? '' : 'none';
			document.getElementById('property-values').style.display = 'none';
			document.getElementById('property-unit').style.display = '';
			document.getElementById('lbl-min').innerHTML = fieldsValeurMin + ' :';
			document.getElementById('lbl-max').innerHTML = fieldsValeurMax + ' :';
			break;
		case '5': // Zone de liste à sélection unique
		case '6': // Zone de liste à sélection multiple
		case '12': // Zone de liste hiérarchique
			document.getElementById('property-min').style.display = 'none';
			document.getElementById('property-max').style.display = 'none';
			document.getElementById('property-precision').style.display = 'none';
			document.getElementById('property-values').style.display = '';
			document.getElementById('property-unit').style.display = '';
			frm.elements['upd-values'].focus();
			break;
		case '7':
		case '8':
		case '9':
		case '10':
			document.getElementById('property-min').style.display = 'none';
			document.getElementById('property-max').style.display = 'none';
			document.getElementById('property-precision').style.display = 'none';
			document.getElementById('property-values').style.display = 'none';
			document.getElementById('property-unit').style.display = 'none';
			break;
		default: // Aucun type sélectionné
			document.getElementById('property-min').style.display = 'none';
			document.getElementById('property-max').style.display = 'none';
			document.getElementById('property-precision').style.display = 'none';
			document.getElementById('property-values').style.display = 'none';
			document.getElementById('property-unit').style.display = 'none';
	}
}
/**	Cette fonction supprime un champ image après avoir demandé confirmation à l'utilisateur
 * 
 * @param {int} cls Identifiant de la classe
 * @param {int} fld Identifiant du champ avancé
 * @param {int} obj1 Identifiant de l'objet référencé (1ère partie obligatoire)
 * @param {int} obj2 Identifiant de l'objet référencé (2ème partie si clé multiple)
 * @param {int} obj3 Identifiant de l'objet référencé (2ème partie si clé multiple)
 */
function delImageField( cls, fld, obj1, obj2, obj3 ){
	if( !window.confirm(fieldsConfirmSuppressionImage))
		return false;
	
	$.ajax({
		type: "POST",
		url: '/admin/ajax/fields/ajax-fields.php',
		data: 'del-img-field=1&cls=' + cls + '&fld=' + fld + '&obj1=' + obj1 + '&obj2=' + obj2 + '&obj3=' + obj3,
		async:false,
		success: function( data ){
			if( data=='ok' ){
				$('#del-field-img-' + fld).parent().find('img').remove();
				$('#del-field-img-' + fld).remove();
			}
		},
		error: function(){
			alert(fieldsErreurSuppressionImage);
		}
	});
	return false;
}

$( document ).ready(function() {
	// dans Configuration > Avis Vérifiés, affiche ou cache les champs si "Avis vérifiés" est en activé ou non
	$('input[name="avis_verifie_activer"]').on('change', function() {
		if( $("input[name='avis_verifie_activer']:checked").val() == '1' ){
			$('.block-avis').removeClass('none');
		}else{
			$('.block-avis').addClass('none');
		}
	});
});
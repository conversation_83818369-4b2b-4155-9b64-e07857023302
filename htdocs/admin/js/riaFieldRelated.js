/**	RiaFieldRelated
 *	Transforme un champ texte destiné à renvoyer une liste d'ids
 *	en sélecteur. Le champ texte doit avoir la class fld-type-text
 *	ainsi qu'une class related-class-X où X est l'identifiant de la class d'objets
 *	Les traitements pour renvoyer les objets se fait dans /admin/ajax/fields/ajax-autocomplete-related.php
 */

var riaFieldRelated = {};

/**	Initialise les champs sélection multiple d'objets
 *	@param	container	Conteneur (Optionnel, $(document.body) par défaut)
 */
riaFieldRelated.init = function(container) {
	// charge le js
	var script = document.createElement('script');
	script.setAttribute('type', 'text/javascript');
	script.setAttribute('src', '/admin/js/jquery.tokenInput.js');
	document.getElementsByTagName('head')[0].appendChild(script);
	
	// charge le css
	var css = document.createElement('link');
	css.setAttribute('rel', 'stylesheet');
	css.setAttribute('type', 'text/css');
	css.setAttribute('href', '/admin/css/tokenInput.css');
	document.getElementsByTagName('head')[0].appendChild(css);
	
	if (! container) container = $(document.body);
	container.find('.fld-type-text').each(function() {	// Chaque fld-type-text
		var textBox = $(this);
		var r;
		if (r = (new RegExp('(^|\\s)related-class-(\\d+)(\\s|$)')).exec($(this).attr('class'))) {
			// le champ texte a une related-class
			var c = r[2];
			
			var url = '/admin/ajax/fields/ajax-autocomplete-related.php?cls=' + c;
			
			// Chargement ajax pour charger les valeurs courantes
			$.ajax({
				'url': url,
				'data': textBox.serialize().replace(textBox.attr('name'), 'v'),
				'type': 'get',
				'dataType': 'json',
				'success': function(response) {
					// Bind le widget avec tokenInput
					textBox.tokenInput(url, {
						'theme': 'ria',
						'prePopulate': response,
						'hintText': '',
						'noResultsText': riaFieldRElatedNoResult,
						'searchingText': riaFieldRElatedRecherche,
						'animateDropdown': false,
						'preventDuplicates': true
					});
				}
			});
		}
	});
};

$('#choose-channel').on( 'click', function(){
	var option = $(this).find(':selected');

	var chlID = parseInt( option.val() );
	var chlName = option.html();

	if( !isNaN(chlID) && chlID>0 ){
		var newChannel = ''
		+ 	'<div>'
		+ 		'<input type="hidden" name="channels[]" value="' + chlID + '" />'
		+ 		'<input width="16" type="image" height="13" title="' + mediaRetirerChaine + '" src="/admin/images/del-cat.png" name="del-pls" class="del-obj-seg" />'
		+		'&nbsp;<span>' + escapeHtml( chlName ) + '</span>'
		+ 	'</div>';

		if( !$('#list-channels div').length ){
			$('#list-channels').html(mediaNonPresenceChaine);
		}

		$('#list-channels').append( newChannel );
		option.remove();

		if( $(this).find('option').length<=1 ){
			$(this).hide();
		}
	}
});

$('#choose-playlist').on( 'click', function(){
	var option = $(this).find(':selected');

	var plsID = parseInt( option.val() );
	var plsName = option.html();

	if( !isNaN(plsID) && plsID>0 ){
		var newPlaylist = ''
		+ 	'<div>'
		+		'<input type="hidden" name="playlists[]" value="' + plsID + '" />'
		+ 		'<input width="16" type="image" height="13" title="' + mediaRetirerPlaylist + '" src="/admin/images/del-cat.png" name="del-pls" class="del-obj-seg" />'
		+		'&nbsp;<span>' + escapeHtml( plsName ) + '</span>'
		+ 	'</div>';

		if( !$('#list-playlists div').length ){
			$('#list-playlists').html(mediaNonPresencePlaylist);
		}

		$('#list-playlists').append( newPlaylist );
		option.remove();

		if( $(this).find('option').length<=1 ){
			$(this).hide();
		}
	}
});

$('#list-channels, #list-playlists').on('click', '.del-obj-seg', function(){
	var parentElem = $(this).parent();
	

	var elemID = parentElem.find('[type=hidden]').val();
	var elemName = parentElem.find('span').html();

	var select = parentElem.parents('td').find('select');
	select.append('<option value="' + escapeHtml( elemID ) + '">' + escapeHtml( elemName ) + '</option');
	
	select.find('option').sort(
		function(a,b){
			return a.text > b.text ? 1 : -1;
		}
	).remove().appendTo( '#' + select.attr('id') );

	select.find('option:eq(0)').attr('selected', 'selected');
	select.show();

	parentElem.remove();
	
	return false;
});

function confirmDeleteChannel(){
	return window.confirm(mediaConfirmSuppressionChaine);
}
function confirmDeletePlaylist(){
	return window.confirm(mediaConfirmSuppressionPlaylist);
}
function confirmDeleteMedia(){
	return window.confirm(mediaConfirmSuppressionMedia);
}
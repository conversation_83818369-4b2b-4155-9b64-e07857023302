/*
 *	Système de cases à cocher en cascade.
 *	Lorsque l'on coche le parent, les enfant sont cochés, etc...
 *
 */
function optionsTree(sId,unfold){
	if( document.getElementById ){
		try{
			this.opt = document.getElementById(sId);
			this.opt.obj = this;
			this.childs = new Array();
			this.opt.onclick = this.onclick;
			this.unfold = unfold ? true : false;
		}catch(e){
			//alert( sId + " not found" );
		}
	}
}
optionsTree.prototype.addChild = function(oChild){
	oChild.parent = this;
	this.childs[ this.childs.length ] = oChild;
}
optionsTree.prototype.onclick = function(e,noparent){
	if( this.obj.childs && this.obj.childs.length ){
		if( !this.checked ){
			var all_checked = true;
			for( var i=0; this.obj.childs && i<this.obj.childs.length && all_checked; i++ )
				all_checked = this.obj.childs[i].opt.checked;
			if( !all_checked )
				this.checked = true;
		}			
		for( var i=0; this.obj.childs && i<this.obj.childs.length; i++ ){
			this.obj.childs[i].opt.checked = this.checked;
			this.obj.childs[i].opt.onclick(e,true);
		}
		if( this.obj.unfold ){
			this.obj.toggleFolding();
		}
	}
	if( this.obj.parent && !noparent )
		this.obj.parent.childClick();
}
optionsTree.prototype.toggleFolding = function(){
	var p = this.opt.parentNode;
	while( p.parentNode && p.nodeName!='BODY' && p.nodeName!='DL' )
		p = p.parentNode;
	if( p && p.nodeName=='DL' ){
		p.className = this.opt.checked ? 'unfold' : 'fold';
	}
}
optionsTree.prototype.check = function(){
	for( var i=0; this.childs && i<this.childs.length; i++ ){
		if( this.childs[i].opt.checked ){
			this.opt.checked = true;
			if( this.unfold ) this.toggleFolding();
		}
	}
}
optionsTree.prototype.childClick = function(){
	var checked = false;
	for( var i=0; this.childs && i<this.childs.length; i++ )
		if( this.childs[i].opt.checked ){
			checked = true;
			break;
		}
	this.opt.checked = checked;
	if( this.parent )
		this.parent.childClick();
}
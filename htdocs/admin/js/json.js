
/**
 *	Cette fonction permet de charger l'image pour montrer si un compte utilisateur est synchronisé ou non
 *	@param isSync, Booléen pour savoir si le compte est synchronisé ou non
 */
function viewUsrIsSync( isSync ){
	return '<img class="sync" src="/admin/images/sync/'+( isSync ? 1 : 0 )+'.svg" title="'+( isSync ? jsonClientSyncGestionCommerciale : jsonClientBoutique )+'" alt="'+( isSync ? jsonClientSyncGestionCommerciale : jsonClientBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si une catégorie est synchronisée ou non
 *	@param isSync Obligatoire, Booléen pour savoir si la catégorie est synchronisée ou non
 */
function viewCatIsSync( isSync ){
	return '<img class="sync" src="/admin/images/sync/'+( isSync ? 1 : 0 )+'.svg" title="'+( isSync ? jsonCatSyncGestionCommerciale : jsonCatBoutique )+'" alt="'+( isSync ? jsonCatSyncGestionCommerciale : jsonCatBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si un produit est synchronisé ou non
 *	@param isSync, Booléen pour savoir si le produit est synchronisé ou non
 */
function viewPrdIsSync( isSync ){
	return '<img class="sync" src="/admin/images/sync/'+( isSync ? 1 : 0 )+'.svg" title="'+( isSync ? jsonProduitSyncGestionCommerciale : jsonProduitBoutique )+'" alt="'+( isSync ? jsonProduitSyncGestionCommerciale : jsonProduitBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si une relation est synchronisée ou non
 *	@param isSync, Booléen pour savoir si la relation est synchronisée ou non
 */
function viewHryIsSync( isSync ){
	return '<img class="sync" src="/admin/images/sync/'+( isSync ? 1 : 0 )+'.svg" title="'+( isSync ? jsonRelationSyncGestionCommerciale : jsonRelationBoutique )+'" alt="'+( isSync ? jsonRelationSyncGestionCommerciale : jsonRelationBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si un magasin est synchronisé ou non
 *	@param isSync, Booléen pour savoir si le magasin est synchronisé ou non
 */
function viewStrIsSync( isSync ){
	return '<img class="sync" src="/admin/images/sync/'+( isSync ? 1 : 0 )+'.svg" title="'+( isSync ? jsonMagasinSyncGestionCommerciale : jsonMagasinBoutique )+'" alt="'+( isSync ? jsonMagasinSyncGestionCommerciale : jsonMagasinBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si un panier est synchronisé ou non
 *	@param piece, Numéro de pièce du panier
 */
function viewCartIsSync( piece ){
	return '<img class="sync" src="/admin/images/sync/'+( piece ? 1 : 0 )+'.svg" title="'+( piece ? jsonPanierSyncGestionCommerciale : jsonPanierBoutique )+'" alt="'+( piece ? jsonPanierSyncGestionCommerciale : jsonPanierBoutique )+'" />';
}

/**
 *	Cette fonction permet de charger l'image pour montrer si une commande est synchronisée ou non
 *	@param piece, Numéro de pièce de la commande
 */
function viewOrdIsSync( piece ){
	return '<img class="sync" src="/admin/images/sync/'+( piece ? 1 : 0 )+'.svg" title="'+( piece ? jsonCommandeSyncGestionCommerciale : jsonCommandeBoutique )+'" alt="'+( piece ? jsonCommandeSyncGestionCommerciale : jsonCommandeBoutique )+'" />';
}

/**
 *	Cette fonction fait remonté l'écran en haut et affiche un indicateur de chargement.
 *	@param cols, Nombre de colonne dans une ligne de tableau
 */
function swichPageLoad(cols){
	var height = $("#site-content table tbody").height();
	/* Place le scroll en haut à gauche de l'écran et fait apparaît le chargement */
	$("#site-contentc form table tbody").html('<tr><td colspan="'+cols+'" style="height: '+height+'px; color: rgb(95, 95, 95); text-align: center; padding: 5px; padding-top: 15px;" id="load-json"><img style="border: medium none; width: 32px;" src="/admin/images/json-load.gif" alt=""><div style="vertical-align: middle; margin-top: 8px;">' + msgLoading + '</div></td></tr>');
	// window.scrollTo(0, 0);
}

/** Cette fonction permet de générer la pagination pour le changement de page
 *	@param page, Page où nous en sommes 
 *	@param pages, Nombre de pages maximum
 *	@param before, Nombre de page pouvant être cliquées pour revenir en arrière
 *	@param after, Nombre de page pouvant être cliquées pour aller en avant
 *	@param colsPage, Nombre de colonne pour le nombre de page (Page 1/345)
 *	@param colsSwitchPage, Nombre de colonne pour la pagination des pages (« Page précédente | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | Page suivante »)
 *	@param funct, Nom de la fonction ainsi que c'est paramètre
 *	@param paramFunct, Paramètre de la fonction, le numéro de page et le nombre de pages est automatiquement géré (il s'agit des deux premier paramètre) ex : " , '', 2 "
 *	@param url, page devant être rechargée si le javascript est désactivé
 *	@param $params, paramètres pouvant être passés à l'url, ce qui bien après le url.php?page=1& (le numéro de page est automatiquement géré). ex " fld=2&amp;mdl=309 "
 */
function switchPage(page, pages, before, after, colsPage, colsSwitchPage, funct, paramFunct, url, params){
	var html ='';

	page 	= parseInt( page );
	pages 	= parseInt( pages );
	before 	= parseInt( before );
	after 	= parseInt( after );

	if( isNaN(page) || isNaN(pages) || isNaN(before) || isNaN(after) ){
		return html;
	}

	if( page > pages ){
		page = pages;
	}

	// Bornes de la pagination
	const first = page-before>1 ? page-before : 1;
	const end = page+after<pages ? page+after : pages;
	
	// Nombre de page
	html = '<td '+( colsPage>0 ? 'colspan="'+colsPage+'"' : '' )+' style="text-align: left;">Page '+page+'/'+pages+'</td>';
	
	// Tableau contenant les liens des autres pages
	var links = new Array();
	var count = 0;
	if( page>1 ){
		links[count++] = '<a onclick="return '+funct+'( '+(page-1)+', '+pages+' '+paramFunct+' )" href="'+url+'?page='+(page-1)+'&amp;'+params+'">&laquo; ' + jsonPagePrec + '</a>';
	}

	for( var p=first; p<=end; p++ ){
		if( p==page ){
			links[count++] = '<b>'+p+'</b>';
		}else{
			links[count++] = '<a onclick="return '+funct+'( '+p+', '+pages+' '+paramFunct+')" href="'+url+'?page='+p+'&amp;'+params+'">'+p+'</a>';
		}
	}
	
	if( page<pages ){
		links[count++] = '<a onclick="return '+funct+'( '+(page+1)+', '+pages+' '+paramFunct+' )" href="'+url+'?page='+(page+1)+'&amp;'+params+'">' + jsonPageSuiv + ' &raquo;</a>';
	}
	
	// Pagination
	html += '<td '+( colsSwitchPage>0 ? 'colspan="'+colsSwitchPage+'"' : '' )+'>'+links.join(' | ')+'</td>';
	
	return html;
}

function splitParamsInURL( query ){
	if( typeof query == 'undefined' ){
		return false;
	}

	var query_string = {};
	var vars = query.split("&");

	for( var i = 0; i < vars.length ; i++ ){
		var pair = vars[i].split("=");
		
		pair[0] = pair[0].replace('-', '_');
		pair[0] = pair[0].replace('?', '');

		if( typeof query_string[ pair[0] ] === 'undefined' ){
			query_string[ pair[0] ] = decodeURIComponent(pair[1]);
		}else if( typeof query_string[ pair[0] ] === 'string' ){
			var arr = [ query_string[ pair[0] ],decodeURIComponent(pair[1]) ];
			query_string[ pair[0] ] = arr;
		}else{
			query_string[ pair[0] ].push( decodeURIComponent(pair[1]) );
		}
	}

	return query_string;
}
$(document).ready(
	function() {
		// Active le sélecteur de site
		if( typeof $('#riawebsitepicker') != 'undefined' && $('#riawebsitepicker').length ){
			$('#riawebsitepicker .selectorview').click(function(){
				if($('#riawebsitepicker .selector').css('display')=='none'){
					$('#riawebsitepicker .selector').show();
				}else{
					$('#riawebsitepicker .selector').hide();
				}
			});

			$('#riawebsitepicker .selector a').click(function(){
				$('#riawebsitepicker .selectorview .left .view').html($(this).html());
				$('#riawebsitepicker .selector').hide();
				window.location.href = '/admin/config/products/index.php?wst=' + $(this).attr('name').replace('w-', '');
			});
		}

		if( location.pathname == "/admin/catalog/product.php"){
			getInnerSearchResult();
		}

		$.fn.productDatepicker = function() {
			$(this).each(function () {
				var temp = this;

				// Implémente le sélecteur de date sur chacun d'entre eux.
				$(this).DatePicker({
					format: 'd/m/Y',
					date: $(this).val(),
					current: $(this).val(),
					starts: 1,
					onChange: function (formated, dates) {
						if (dates != 'Invalid Date') {
							$(temp).val(formated);
							$(temp).DatePickerHide();
						}
					}
				});

			});	
		};

		$('input.datepicker').productDatepicker();
	}
).delegate( // Bouton Prévisualiser (une fiche produit)
	'.previsualisation', "click", function(){
		var url = $('.previsualisation').data('url');
		window.open(url, "_blank");
	}
).delegate(
	'.add-linked', 'click', function(){
		if( $.trim( $(this).parent().find('.ref').val() )=='' ){
			var inputRef = $(this).parent().find('.ref').attr('id');
			var inputID = $(this).parent().find('.prd-id-hidden').attr('id');

			displayPopup(productsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=0&cnt_publish=0&input_id_ref=' + inputRef + '&input_id_prd_id=' + inputID );
			return false;
		}
	}
).delegate(
	'#del-mdl', 'click', function(){
		return window.confirm(productsConfirmSuppressionModelPerso);
	}
).delegate('.add_type', 'click', function() {
	displayPopup(productAddTypeRelation, '','/admin/config/catalogue/relations/edit.php?popup=1', 'location.reload()' );
});

// Mise à jour des tarifs (ht vers ttc)
function updPriceTTC( fld ){
	var tva = parseFloat( $('#prd-tva').val() );
	if( tva == -1) {
		tva = 1;
	}
	var value = parseFFloat( $(fld).val() );
	var field_id = $(fld).attr('data-field-ttc');
	var price_ttc = number_format(value * tva, 2, '.', '') ;
	$( field_id ).val( price_ttc);
}

// Mise à jour des tarifs (ttc vers ht)
function updPriceHT( fld ){
	var tva = parseFloat( $('#prd-tva').val() );
	if( tva == -1) {
		tva = 1;
	}
	var value = parseFFloat( $(fld).val() );
	var field_id = $(fld).attr('data-field-ht');
	var price_ht = number_format(value / tva, 2, '.', '');
	$( field_id ).val(price_ht);
}

// Applique le changement de tva
function updTVA(){
	$('#all-prices').find('input[type="number"]').each( function( id, el ){
		if( $(el).attr('data-prc-ttc')=="1" ){
			if( $(el).attr('data-field-ht') ){
				updPriceHT( el )
			}
		}else{
			if( $(el).attr('data-field-ttc') ){
				updPriceTTC( el )
			}			
		}
	});
}

function parent_select_prd( id, name, ref, catId, inputIdPrdId, inputIdName, inputIdRef ){
	$('#' + inputIdRef).val( ref + ' - ' + name );
	$('#' + inputIdPrdId).val( id );
	$('#' + inputIdName).val( name );
}

function parent_select_prds(data, action){
	var prd_data = data;
	var prd_id = $("#rel-source-id").val();
	var cat_id = $("#rel-cat-id").val();

	var type = 0;
	if (action == "prd-relations"){
		type = $("#rel-type").val();
	}

	var to_send = prd_data + '&relation_type=' + action + '&source=' + prd_id;

	if ($.isNumeric(type) && type > 0){
		to_send += '&type=' + type;
	}

	$.get( 'products/linked.php?prd='+prd_id+'&cat='+cat_id+'&'+to_send, function(data){
		var form_id = "#form-"+action;
		if ($.isNumeric(type) && type > 0){
			form_id += "-"+type;
		}

		$(form_id).html(data);
		$("#rel-type").remove();

		var ordered = parseInt($('input[name="rel-ordered"]', form_id).val());

		var sort_url;
		if( action  == 'prd-parents') {
			sort_url = '';
		} else if ( action == 'prd-childs') {
			sort_url = 'catalog/ajax-product-related-position-update.php?prd='+prd_id;
		} else {
			sort_url = 'catalog/ajax-product-related-position-update.php?prd='+prd_id+'&rel='+type;
		}

		if (ordered != -1)
			table_sortable($('table', form_id), sort_url);

		// Au clic sur le bouton Ajouter, on prépare les données à envoyer et on appelle le xml.php avec l'action associée.
		products_add_event(form_id, type, action);

		// Au clic sur le bouton supprimer, on efface les lignes du tableau cochées de la base de donnée (envoi à xml.php)
		products_del_event(form_id, type, action, prd_id, ordered, sort_url);
	});

	window.hidePopup();
}

function prdConfig(){
	// Récupère les informations du formulaire
	length = $("#length").val();

	// Vérifie l'information avant d'envoyer le formulaire
	if( !parseInt(length) || length<=0 || length>999 ){
		alert(productsAlertLongeurListeProduit);
		return false;
	}
	return true;
}

function displaySortDir(){
	var type = $('#prd-sort-type').val();
	if( type==1 || type==11 )
		$('#prd-sort-dir').hide();
	else
		$('#prd-sort-dir').show();
}

function confirmDel(){
	return window.confirm(productsConfirmSuppressionProduit);
}

// Gère la sélection/désélection des images.
function previewClick(preview) {
	// Change la couleur de la bordure de l'image.
	preview.style.borderColor = !preview.style.borderColor ? '#4574BF' : '';

	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor !== '';

	var prdIdStart = location.search.indexOf('prd=') + 4;
	var prdIdStop = location.search.indexOf('&', prdIdStart);

	var prdId = decodeURIComponent(location.search.substr(prdIdStart, prdIdStop - prdIdStart));
	var selectedImagesCount = $('.preview input:checked').length;

	$('[name=delimg]').toggle(selectedImagesCount >= 1);
	$('.edit-zones').toggle(selectedImagesCount === 1);
	$('.edit-alt').toggle(selectedImagesCount === 1);

	$('.edit-zones').click(function () {
		var $preview = $('.preview input:checked').parents('.preview:eq(0)');

		displayPopup(productsCliqueZone, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=1&obj_id_0=' + prdId, null, 756, 602);
	});

	$('.edit-alt').click(function () {
		var $preview = $('.preview input:checked').parents('.preview:eq(0)');

		displayPopup(productsAttrAlt, '', '/admin/documents/images/alt.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=1&obj_id[]=' + prdId);
	});
}

// Formate la référence pour qu'elle soit uniquement en majuscules
function prdFormatRef(ref){
	ref = trim(ref);
	return strtoupper(ref);
}

// Vérification des données saisie dans la fiche principale
function prdValidForm(frm){
	if( !trim(frm.ref.value) ){
		alert(productsAlertReference);
		frm.ref.focus();
		return false;
	}
	if( !trim(frm.name.value) ){
		alert(productsAlertNom);
		frm.name.focus();
		return false;
	}
}

var timeout = '';
function delays(){
	$('#error').fadeOut();
}
//fonction pour les nomenclatures 
function make_error(error){
	clearTimeout(timeout);
	$('#error').remove();
	$('body').append('<div id="error" class="errorjs">'+error+'</div>');
	$('#error').fadeIn(500,function(){
		timeout = setTimeout("delays()",10000);
	});
}
function opt_prd_add(){
	$('#error').fadeOut();
	
	var opt = $("#opt_prd").val();
	var ref = $("#prd_ref").val();
	
	if( ref != '' && opt != ''){
	
		$.ajax({
			type: "GET",
			url: 'xml.php',
			data: 'action=opt-prd-add&opt='+opt+'&ref='+encodeURIComponent(ref),
			async:false,
			success: function(xml){
				if($( xml ).find('error').length){
					var error = $( xml ).find('error');
					make_error(error.attr('message'));
				}
				else if($( xml ).find('success').length){
					$("#opt_child").children().remove();
					$( xml ).find('child').each(function(){
						$("#opt_child").append('<option value="'+$( this ).attr('id')+'">'+$( this ).find('ref').text()+' - '+$( this ).find('name').text()+'</option>');
					});
					$("#prd_ref").val('');
				}
			},
			error: function(){
				alert( productsErreurEnregistrementDemande );
			}
		}); 
	}
	return false;
}
function opt_prd_del(){
	$('#error').fadeOut();
	
	var childs = $("#opt_child").val();
	var opt = $("#opt_prd").val();
	
	var reg=new RegExp("[ ,;]+", "g");
	if(childs != null){
		var child = childs.toString().split(reg);
		
		if( child != null && child.length > 0 ){
			for(var i = 0 ; i < child.length; i++){
		
				
				if( child != '' && opt != ''){
					$.ajax({
						type: "GET",
						url: 'xml.php',
						data: 'action=opt-prd-del&opt='+opt+'&prd='+child[i],
						success: function(xml){
							if($( xml ).find('error').length){
								var error = $( xml ).find('error');
								make_error(error.attr('message'));
							}
							else if($( xml ).find('success').length){
								var response = $( xml ).find('success')
								$('#opt_child option[value="'+response.attr('id')+'"]').remove();
							}
						},
						error: function(){
							alert( productsErreurEnregistrementDemande );
						}
					}); 
				}
			}
		}
	}
	return false;
}
var submit = '';
function prd_nomenclature_add(){
	submit = 'add';
}
function prd_nomenclature_del(){
	submit = 'del';
}
function prd_nomenclature_opt(){
	$('#error').fadeOut();

	if(submit ==  'add'){
		var opt = $('#nomenclature_opt').val();
		var qte = $('#nomenclature_qte').val();
		var prd = $('#nomenclature_prd').val();
		
		if(opt !='' && qte !='' && prd !=''){
			$.ajax({
				type: "GET",
				url: 'xml.php',
				data: 'action=prd-nomenclature-add&opt='+opt+'&qte='+qte+'&prd='+prd,
				async:false,
				success: function(xml){
					if($( xml ).find('error').length){
						var error = $( xml ).find('error');
						make_error(error.attr('message'));
					}
					else if($( xml ).find('success').length){
						var opt = $( xml ).find('success')
						$('#prd_nomenclature option[value="'+opt.attr('id')+'"]').remove();
						$("#prd_nomenclature").append('<option value="'+opt.attr('id')+'">'+opt.attr('name')+' - ('+opt.attr('qte')+')</option>');
						$('#nomenclature_qte').val('');
					}
				},
				error: function(){
					alert( productsErreurEnregistrementDemande );
				}
			}); 
		}
		
	}
	else if(submit == 'del'){

		var opts = $("#prd_nomenclature").val();
		var prd = $('#nomenclature_prd').val();
		
		var reg=new RegExp("[ ,;]+", "g");
		if(opts != null){
			var opt = opts.toString().split(reg);
			
			if( opt != null && opt.length > 0 ){
				for(var i = 0 ; i < opt.length; i++){
			
					
					if( opt != '' && prd != ''){
						$.ajax({
							type: "GET",
							url: 'xml.php',
							data: 'action=prd-nomenclature-del&opt='+opt[i]+'&prd='+prd,
							async:false,
							success: function(xml){
								if($( xml ).find('error').length){
									var error = $( xml ).find('error');
									make_error(error.attr('message'));
								}
								else if($( xml ).find('success').length){
									var response = $( xml ).find('success')
									$('#prd_nomenclature option[value="'+response.attr('id')+'"]').remove();
								}
							},
							error: function(){
								alert( productsErreurEnregistrementDemande );
							}
						}); 
					}
				}
			}
		}
		
	}
	
	submit = '';
	return false;
}

// Permet d'éditer une URL simplifiée
function editUrlRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	var html = '';
	html += '	<td headers="url">';
	html += '		<input type="text" style="padding:5px;width:98%;" name="url-'+id+'" value="'+url+'" />';
	html += '		<input type="hidden" name="url_old-'+id+'" value="'+url+'" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="save-maj-url" value="' + productsEnregistrer + '" onclick="saveUrlRedirection('+id+')" />';
	html += '		<a class="del button" onclick="cancelEditUrlRedirection('+id+', \''+url+'\')">' + productsAnnuler + '</a>';
	html += '	</td>';
	
	$("#url-"+id).html( html );
}

// Permet d'annulée les modifications faites sur l'URL simplifiée
function cancelEditUrlRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	if( id>0 ){
		var html = '';
		
		html += '	<td headers="url">'+url+'</td>';
		html += '	<td headers="action" class="td-action">';
		html += '		<a class="edit-url button" title="Editer" onclick="editUrlRedirection('+id+', \''+url+'\')">' + productsEditer + '</a>';
		html += '		<br /><a class="del button" onclick="delUrlRedirection('+id+', \''+url+'\')">' + productsSupprimer + '</a>';
		html += '	</td>';
		$("#url-"+id).html( html );
	} else {
		$("#url-0").remove();
		if( $("#tb-redirection tbody tr").length==1 )
			$("#no-url").show();
	}
}

// Permet de supprimer une URL simplifiée avec un code de redirection 301 seulement
function delUrlRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	if( window.confirm(productsConfirmSupressionUrl) ){
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax-url-redirection.php',
			data: 'del=1&url='+encodeURIComponent(url),
			dataType: 'xml',
			success: function(xml) {
				
				// Si la suppression a réussie
				if( $(xml).find('result').attr('type') == '1' ){
					$("#tb-redirection").before("<div class=\"error-success\">" + productsSuccesSuppressionUrl + "</div>");
					$("#url-"+id).remove();
				} else{
					// Gestion des messages d'erreur
					$("#tb-redirection").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
					return false;
				}
				
			}
		});
		if( $("#tb-redirection tbody tr").length==2 )
			$("#no-url").show();
	}
}

// Permet d'afficher le formulaire d'ajout d'un url simplifiée (elle aura pour code de redirection 301)
var nb = 0;
function addUrlRedirection(count){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	$("#no-url").hide();
	$("#url-0").remove();
	var html = '';
	
	html += '<tr id="url-0">';
	html += '	<td headers="url">';
	html += '		<input type="text" style="padding:5px;width:98%;" name="url-0" value="" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="add-url" value="' + productsEnregistrer + '" onclick="saveUrlRedirection(0, '+(count+nb)+')" />';
	html += '		<a class="del button" onclick="cancelEditUrlRedirection(0)">' + productsAnnuler + '</a>';
	html += '	</td>';
	html += '</tr>';
	nb++;
	$("#tb-redirection tbody").append(html);
}

/** Cette fonction ouvre la popup permettant l'import des produits liés selon un type.
 *	@param type Obligatoire, identifiant du type de relation ou parents ou childs.
 *	@return L'action du formulaire est bloqué par cette fonction.
 */
function relationImport( prd, type ){
	displayPopup( productsImporterRelationsProduit, '', '/admin/catalog/popup-import-relations.php?type=' + type + '&prdsrc=' + prd );
	return false;
}

/** Cette fonction recharge l'onglet Relation d'une fiche produit.
 */
function reloadTabRelations(){
	var url = window.location.href.replace(new RegExp("(&tab=[a-z]+)", "g"), '');
	hidePopup();
	window.location.href = url + '&tab=linked';
	return false;
}
/** 
 *	Cette fonction permet de supprimer une offre de fidelite
 */
function delProductRewards(){

	$('#rwp-rewards input.checkbox:checked').each(function(){
		
		var id = $(this).val();

		$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?delprdreward=1&id=' + id , function(){
		});
		$("#rwd-"+id).remove();

	});

	if( $(".rwd-elem").length <= 0 ){

		$('#rwp-rewards tbody').html('<tr><td colspan="9">' + productsAucuneOffre + '</td></tr>');
	}
	
	return false;
}

/** cette fonction permet d'ajouter un produit a ma sélection */

function addProductToSelection(event){
	event.preventDefault();
	$.getJSON( '/admin/ajax/catalog/ajax-products.php?add-selection='+$(this).data('added')+'&prd='+$(this).data('prd'), function(json){
		if( json.return ){
			if( $(this).data('added') == false ){
				$(this).data('added',true);
				$(this).removeClass('inactive');
				$(this).addClass('active');
				$(this).val('Actif')
			}else{
				$(this).data('added',false);
				$(this).removeClass('active');
				$(this).addClass('inactive');
				$(this).val('Inactif')
			}
		}
	}.bind(this));
}

$(document).ready(
	function() {
		$('#published_checkbox').change(function(){
			var link = $('#published_checkbox').data('link-href');
			if(link != ""){
				window.location.href = link;
			}
		});
	}
);

function getInnerSearchResult(){
	var tab = $("#tabpanel").data('tab');
	if(tab === undefined || tab != 'ref'){
		return false;
	}
	var searchParams = new URLSearchParams(window.location.search);
	var prd = searchParams.get("prd");
	var cat = searchParams.get("cat");

	if( cat === null || prd === null ){
		return false;
	}

	$.get('ajax-prd-inner-search.php?prd=' + prd + '&cat='+ cat, function(res){
		var data = JSON.parse(res);
		if(data.success){
			$("#lst_search").html(data.content);
		}
	});
}

$(document).ready(function(){
	var riapickerForm = $('#riapicker-form');
	var timeout = false;
	$('.riapicker').each(function(k,el){
		const $riapicker = $(el)
		if( $riapicker.attr('id') == 'riadatepicker' ){
			return true;
		}
		var $selectorView = $('.selectorview',$riapicker);
		var $selectorList = $('.selector',$riapicker);
		var selectorName = $riapicker.data('selectorname');
		var defaultVal = $riapicker.data('defaultval');
		var viewTextLength = 30;

		$selectorView.click(function(){
			if($selectorList.css('display')=='none'){
				$('.riapicker .selector').hide();
				$selectorList.show();
			}else{
				$selectorList.hide();
			}
		});

		$( 'a', $selectorList ).click(function(e){
			
			timeout = false;
			var text = '';

			if( $('input[type=checkbox]', this).length == 0 ){
				text = $(this).text();
				$('input[type=checkbox]:checked', $selectorList ).each(function(k,v){
					$(v).prop('checked', false);
				});
				var regex = /^[a-z]*-[^\s]\d*$/g;
				var val = $(this).attr('name');
				if( regex.exec($(this).attr('name')) !== null ){
					val = val.split("-");
					val = val[1];
				}
				if( $('input[name='+selectorName+'][type=hidden]').length == 0 ){
					var input = $('<input>').attr({
						type: 'hidden',
						name: selectorName
					});
					riapickerForm.append(input);
				}else{
					var input = $('input[name='+selectorName+']');
				}
				input.val(val);
			}else{
				var is_checkbox = $(e.target).is('input[type=checkbox]');
				updateCheckbox($('input[type=checkbox]', this), is_checkbox);
				var labels = [];
				$('input[type=checkbox]:checked', $selectorList ).each(function(k,v){
					labels.push($(v).parent().find('label').text());
				});
				text = labels.join(', ');
			}
			if( text.length > viewTextLength ){
				text = text.substring(0,viewTextLength)+'...';
			}
			if( text.trim() == '' ){
				setDefault(defaultVal);
				return false;
			}
			$('.view', $selectorView ).text(text);

			timeout = setTimeout(function() {
				riapickerForm.submit();
				$selectorList.hide();
			}, 1600);
		});

		function updateCheckbox($checkbox, is_checkbox){
			var checkbox = $checkbox;
			var is_parent = checkbox.parent().hasClass('parent');
			var sub = checkbox.parent().parent();
			var is_checked = checkbox.is(':checked');
			var childs = $('.child input[type=checkbox]',sub);
			var childsCount = childs.length;
			if( !is_checkbox ){
				$checkbox.prop('checked',!is_checked);
				is_checked = checkbox.is(':checked');
			}

			if( is_parent ){
				childs.prop('checked', is_checked );
				return false;
			}

			var checked = $('.child input[type=checkbox]:checked',sub).length;
			if( checked > 0 && checked < childsCount ){
				$('.parent input[type=checkbox]', sub).prop("indeterminate", true );
			}else if( checked === 0  ){
				$('.parent input[type=checkbox]', sub).prop("checked", false );
				$('.parent input[type=checkbox]', sub).prop("indeterminate", false );
			}else if( checked === childsCount ){
				$('.parent input[type=checkbox]', sub).prop("checked", true );
				$('.parent input[type=checkbox]', sub).prop("indeterminate", false );
			}
			return false;
		}

		function setDefault(defaultVal){
			$( 'a[name='+defaultVal+']', $selectorList ).trigger('click');
		}
	});

});

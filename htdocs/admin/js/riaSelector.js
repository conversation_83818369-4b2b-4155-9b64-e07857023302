(function($) {
	jQuery.fn.extend({
		riaSelector: function(methodName, args) {
			if (typeof(methodName) !== 'string') {
				args = methodName;
				methodName = 'create';
			}
			var defaults = $.riaSelector.defaults[methodName];
			if (typeof(args) !== 'object') args = {};
			if (typeof(defaults) === 'object') $.extend(args, defaults);
			
			var r = this;
			this.each(function() {
				var select = $(this);
				
				if (methodName === 'create' && ! this.__riaSelector) this.__riaSelector = new $.riaSelector($.extend({}, args, {'select': select}));
				if ((t = this.__riaSelector[methodName](args)) !== this.__riaSelector) r = t;
			});
			return r;
		}
	});
	
	$.riaSelector = function(param) {
		this._opened = false;
		this._select = param.select;
		return this;
	};
	
	$.riaSelector.prototype = {
		/**	Change
		 */
		'change': function(arg) {
			this.getSelect().change(arg);
			return this;
		},
		
		/**	Ferme
		 */
		'close': function() {
			return this.setOpened(false);
		},
		
		/**	Crée et remplace le select
		 */
		'create': function(param) {
			var object = this;
			var select = this.getSelect();
			
			this.loadStyleSheets();
			
			// Crée le selector
			var r = '';
			r += '<div class="riapicker">';
				r += '<div class="selectorview">';
					r += '<div class="left">';
						r += '<span class="function_name">' + param.title + '</span><br/>';
						r += '<span class="view">' + select.find('option:selected').html() + '</span>';
					r += '</div>';
					r += '<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" /></a>';
					r += '<div class="clear"></div>';
				r += '</div>';
				r += '<div class="selector"></div>';
			r += '</div>';
			
			r = this._html = $(r);
			
			// Options
			var values = {};
			var selector = r.find('.selector');
			select.find('option').each(function() {
				var id = $(this).val();
				values[id] = $(this).html();
				
				var link = $('<a href="#">' + values[id] + '</a>');
				
				// Bind
				link.click(function() {
					object
						.setValue(id)
						.close()
					;
					return false;
				});
				selector.append(link);
			});
			
			// Bind
			r.find('.selectorview').click(function() {
				object.swapOpened();
			});
			
			object.change(function() {
				r.find('.view').html(values[object.getValue()]);
			});
			object.change();
			
			// Remplace la liste par le selector
			select.hide().after(r);
			
			return this;
		},
		
		/**	Renvoie le select
		 */
		'getSelect': function() {
			return this._select;
		},
		
		/**	Renvoie la valeur
		 */
		'getValue': function() {
			return this.getSelect().val();
		},
		
		/**	Renvoie l'élément html
		 */
		'html': function() {
			return this._html;
		},
		
		/**	Renvoie si ouvert
		 */
		'isOpened': function() {
			return this._opened;
		},
		
		/**	Charge les css
		 */
		'loadStyleSheets': function() {
			var src = 'admin/css/riadatepicker.css';
			var l = src.length;
			var found = false;
			$(document).find('head link[rel="stylesheet"]').each(function() {
				var href = this.href;
				if (href.substring(href.length-l, href.length) === src) {
					found = true;
					return false;
				}
			});
			if (! found) $(document).find('head').append('<link rel="stylesheet" type="text/css" href="/' + src + '" />');
			return this;
		},
		
		/**	Ouvre/Ferme
		 */
		'setOpened': function(opened) {
			this.html().find('.selector')[opened !== false ? 'show' : 'hide']();
			this._opened = opened;
			return this;
		},
		
		/**	Affecte la valeur
		 */
		'setValue': function(value) {
			var select = this.getSelect();
			var last = select.val();
			if (last !== value) select.val(value).change();
			return this;
		},
		
		/**	Permute l'ouverture
		 */
		'swapOpened': function() {
			return this.setOpened(! this.isOpened());
		}
	};
	
	$.riaSelector.defaults = {
	};
	
})(jQuery);

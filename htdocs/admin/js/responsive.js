$(document).ready(function() {
	var js_toggle_menu_mobile = $('.js-toggle-menu-mobile');
	if ( js_toggle_menu_mobile.length ) {
		var menu = $('.ria-admin-ui-menubar');

		$('#site-content').click(function(e){
			if ( $('.js-toggle-menu-mobile').hasClass('is-open') ) {
				e.preventDefault();
				$('.js-toggle-menu-mobile').click();
				return false;
			}
		});
		
		$('#site-content').wrap('<div id="site-content-w"></div>');
		$('#site-menu-exit .site-menu-btn').clone().appendTo('.menu-mobile').addClass('site-menu-exit');

		$('.js-toggle-menu-mobile').click(function(){
			menu.toggleClass('is-open');
			$(this).toggleClass('is-open');
			$('body,html').toggleClass('is-menu-open');
		});
	}
});
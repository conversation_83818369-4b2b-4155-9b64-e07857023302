var usrID = 0;
var showGwt = 0;

$(document).ready(function(){
	// Récupère l'identifiant de l'utilisateur en cours d'édition
	usrID = $('#usr_id').length ? $('#usr_id').val() : '';

	// Mise en forme automatique du numéro SIRET
	$("#siret").blur(function(){
		var siret = $("#siret").val();
		$('#siret').val( formatSiret(siret) );
	});

	// Gère l'évolution de l'interface en fonction du profil
	$('#usr-prf-id').change(function(){
		switch( parseInt($(this).val()) ){
			case 1: // Les moyens de paiement sont masqués car inutiles dans ces cas
			case 5:
			case 6:
				$('#row-payment-types').hide();
				break;
			default:
				// Les moyens de paiement sont affichés car utiles
				$('#row-payment-types').show();
		}
	});
	$('#usr-prf-id').trigger('change');

	// Active l'auto-complétion du code NAF
	$('#naf').autocomplete(
		{
			source: 		'/admin/ajax/default/ajax-naf.php',
			delay:			10,
			minLength:		1,
			select: function (event, ui) {
				event.preventDefault();
				var itemName = ui.item.label.substring( 0, ui.item.label.indexOf(' - ') );
				$('#naf').val( itemName );
			},
		}
	);

	// Onglet Messages uniquement
	$('#usr-contacts input.show-form-replay').click(function(){
		if( $(this).parent().parent().next().hasClass('rep-show') )
		{
			$(this).parent().parent().next().hide();
			$(this).parent().parent().next().removeClass('rep-show');
		}
		else
		{
			$(".rep-show").hide();
			$(".rep-show").removeClass('rep-show');
			$(this).parent().parent().next().show();
			$(this).parent().parent().next().addClass('rep-show');
		}
	});
	$('.form-rep').hide();

	// Filtre les messages selon leur type
	$('#ria_type_messages .selectorview').click(function(){
		if($('#ria_type_messages .selector').css('display')=='none'){
			$('#ria_type_messages .selector').show();
			$('#ria_type_messages .selector').hide(); $('#ria_reponse .selector').hide(); $('#ria_contacts .selector').hide();
		}else{
			$('#ria_type_messages .selector').hide();
		}
	});

	$('#ria_type_messages .selector a').click(function(){
		var type = $(this).attr('name').replace( 'type-', '' );
		var dir = $('#dir').val();

		window.location.href = '/admin/customers/edit.php?usr=' + $('#usr_id').val() + '&tab=contacts&dir=' + dir + ( $.trim(type) != '' ? '&type=' + type : '' );
	});

	$('#ria_sort .selectorview').click(function(){
		if($('#ria_sort .selector').css('display')=='none'){
			$('#ria_sort .selector').show();
			$('#ria_sort .selector').hide(); $('#ria_reponse .selector').hide(); $('#ria_contacts .selector').hide();
		}else{
			$('#ria_sort .selector').hide();
		}
	});

	$('#ria_sort .selector a').click(function(){
		var dir = $(this).attr('name').replace( 'dat-', '' );
		var type = $('#type').val();

		window.location.href = '/admin/customers/edit.php?usr=' + $('#usr_id').val() + '&tab=contacts&dir=' + dir + ( $.trim(type) != '' ? '&type=' + type : '' );
	});

}).delegate( // Lien Afficher toutes les commandes (onglet Commandes de la fiche client)
	'.show-orders', 'click', function(e){
		e.preventDefault();
		var type = $(this).attr('type');
		$('tr.order-'+type).removeClass('none');
		$('tr.show-orders.'+type).remove();
	}
).delegate( // Bouton Créer un avoir (fiche client)
	'.user-add-asset', 'click', function(){
		displayPopup('Ajouter un avoir', '', '/admin/customers/popup-add-promo.php?usr='+$(this).attr('data-usr'), '', 400, 200);
	}
).delegate(
		'.popup-adr', 'click', function(event){
			// On vérifie s'il y a le paramètre popup dans l'url
			var popup = $_GET('popup');

			if (popup === null) {
				event.preventDefault();

				// Affiche la popup de modification des adresses de livraison/facturation
				var title = customersDisplayPopupAddressEdit;
				if($(this).hasClass('dlv')){
					title = customersDisplayPopupAddressEditDlv;
				}
				if($(this).hasClass('inv')){
					title = customersDisplayPopupAddressEditInv;
				}
				if($(this).hasClass('add')){
					title = $(this).attr('title');
				}
				displayPopup( title, '', $(this).attr('href'));
			}
}).delegate(
	'input[name="lost-pwd"]', 'click', function(){
		if( parseInt($('#count_wst').val())>1 ){
			var email = $('#email').val();
			displayPopup(customersDisplayPopupMdpPerdu, '', 'ajax-usr-passwd.php?email='+email, false, 600, 400);
			return false;
		}
	}
).delegate(
	'.checklist [name=checkall]', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parents('table').find('tbody input[type="checkbox"]').attr('checked', 'checked');
		}else{
			$(this).parents('table').find('tbody input[type="checkbox"]').removeAttr('checked');
		}
	}
).delegate(
	'#add-wishlist', 'click', function(){
		return editWishList( 0 );
	}
).delegate(
	'#add-prd', 'click', function(){
		window.location.href= '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&cat=0&wishlist=1';
		return false;
	}
);

$(function(){
	if( typeof $('#select-new-relation') != 'undefined' && $('#select-new-relation').length ){
		$('#select-new-relation').autocomplete({
			source:'/admin/ajax/customers/search-customers.php',
			minLength: 2,
			select: function (event, ui) {
				event.preventDefault();

				var regex = /[^ - ]*/;
				var mail = $.trim(regex.exec( ui.item.label)[0]);
				$('#new-relation').val( mail );
				$('#select-new-relation').val( mail );
			},
		});
	}

	if( typeof $('#select-new-parent') != 'undefined' && $('#select-new-parent').length ){
		$('#select-new-parent').autocomplete({
			source: '/admin/ajax/customers/search-customers.php',
			minLength: 2,
			select: function (event, ui) {
				event.preventDefault();

				var regex = /[^ - ]*/;
				var mail = $.trim(regex.exec( ui.item.label)[0]);
				$('#new-parent').val( mail );
				$('#select-new-parent').val( mail );
			},
		});
	}

	if( typeof $('#select-usr-parent') != 'undefined' && $('#select-usr-parent').length ){
		$('#select-usr-parent').autocomplete({
			source: '/admin/ajax/customers/search-customers.php',
			minLength: 2,
			select: function (event, ui) {
				event.preventDefault();

				var regex = /[^ - ]*/;
				var mail = $.trim(regex.exec( ui.item.label)[0]);
				$('#select-usr-parent').val( mail );
			},
		});
	}
});

function editWishList( gwtID ){
	displayPopup( customersDisplayPopupEditListePerso, '', '/admin/customers/popup-wishlist-edit.php?usr=' + usrID + '&gwt=' + gwtID, '', 768, 425 );
	return false;
}

function showWishlist( gwtID, usrID ){
	showGwt = gwtID;
	displayPopup(customersDisplayPopupContenuListePerso, '', '/admin/customers/popup-wishlist-products.php?gwt=' + gwtID + '&usr=' + usrID, 'reloadBookmarks()');
	return false;
}

function wishlist_select_prd( prdID ){
	displayPopup(customersDisplayPopupContenuListePerso, '', '/admin/customers/popup-wishlist-products.php?add=' + prdID + '&gwt=' + showGwt + '&usr=' + usrID, 'reloadBookmarks()');
	return false;
}

function reloadBookmarks(){
	window.location.href = '/admin/customers/edit.php?usr=' + usrID + '&tab=bookmarks';
}

// Fais évoluer les champs affichés en fonction du type d'adresse
function switch_adr_type(frm){
	(frm.type.value!=2) ? $('#adr-civ').removeClass('none') : $('#adr-civ').addClass('none');
	(frm.type.value!=2) ? $('#adr-firstname').removeClass('none') : $('#adr-firstname').addClass('none');
	(frm.type.value!=2) ? $('#adr-lastname').removeClass('none') : $('#adr-lastname').addClass('none');
	(frm.type.value!=1) ? $('#adr-society').removeClass('none') : $('#adr-society').addClass('none');
	(frm.type.value!=1) ? $('#adr-siret').removeClass('none') : $('#adr-siret').addClass('none');
	(frm.type.value!=1) ? $('#usr-naf').removeClass('none') : $('#usr-naf').addClass('none');
	(frm.type.value!=2) ? $('#misc-dob').removeClass('none') : $('#misc-dob').addClass('none');
}

function showSelectSeller(frm, usrId){
	displayPopup(customersDisplayPopupSelectRepresentant, '', '/admin/customers/popup-select-user.php?action_type=seller&user=' + usrId);
	return false;
}

function showSelectCustomer(frm, sellerId){
	displayPopup(customersDisplayPopupAffecterClient, '', '/admin/customers/popup-select-user.php?action_type=user&user=' + sellerId);
	return false;
}

function validSellerChange(html){
	document.getElementById('seller-change-block').innerHTML = html;
}

// Détache le représentant associé à un compte client
function detachSeller(frm, usrId){
	if( window.confirm(customersconfimDetacherRepresentatn) ){
		$.ajax({
			type: "GET",
			url: '/admin/customers/ajax-detach-seller.php',
			data: 'usr=' + usrId,
			dataType: 'json',
			success: function(json) {

				if( json.html != undefined && json.html.length ){
					validSellerChange( json.html );
				}else if( json.error != undefined && json.error.length ){
					alert( json.error );
				}

			}
		});
	}

	return false;
}

/** Appel de la pop-up d'export des comptes clients */
function exportCustomersAccount(){

	// Extrait de la page en cours (index ou edit) les paramètres à transmettre à la popup
	var prf = $('input[name=prf]').val();
	if (typeof prf === 'undefined') {
		prf= 0;
	}
	var seg = $('input[name=seg]').val() || 0;
	var seller = $('input[name=seller]').val() || 0;
	var sort = $('input[name=sort]').val() || 'created';
	var dir = $('input[name=dir]').val() || 'desc';

	var selectedCustomers = [];
	$('[name="usr-id[]"]:checked, [name="usr_id"]').each(function () { selectedCustomers.push($(this).val()); });
	selectedCustomers = selectedCustomers.length > 0 ? '&usr_id[]=' + selectedCustomers.join('&usr_id[]=') : '';

	// Affiche la popup
	displayPopup(
		customersDisplayPopupExportClient,
		'',
		'/admin/customers/popup-export-customers.php?prf='+prf+'&seg='+seg+'&seller='+seller+'&sort='+sort+'&dir='+dir + selectedCustomers
	);

	return false;

}

/** Appel de la pop-up d'export des statistiques concernant la fidélité */
$("#export-rewards").click(function(){
	const usr = $(this).data("usr");
	const url = '/admin/tools/rewards/popup-export-rewards.php?usr='+usr;
	displayPopup( 'Export des statistiques', '', url, undefined, 700, 200);
	return false;
});

// Ouvre la popup Gestion des points de fidélité
$( ".edit-rewards" ).click(function() {
  	displayPopup(
		customersDisplayPopupGestionFidelite,
		'',
		'/admin/customers/popup-edit-rewards.php?usr='+$(this).data('usr')+'&amp;reward='+$(this).data('id')
	);
});

// Pagination Ajax de la liste des clients
$(document).delegate(
	"#user-container #pagination a", 'click',  function(e){
	if ($(this).attr('href').indexOf('index.php?') != -1) {
		e.preventDefault();
		getClientTable($(this).attr('href').replace('index.php?', ''), true);
	}
});

if(!$.browser.msie && location.pathname.indexOf('index.php') != -1){
	window.onpopstate = function(event) {
		var query = '';
		if (event.state!==null) {
			query = $.param(event.state);
		}
		getClientTable(query,false);
	};
}

function getClientTable(query, pushState){
	var search = location.search.substring(1);
	var data = {};
	if (search) {
		data = JSON.parse('{"' + decodeURI(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g,'":"') + '"}');
	}
	var option_data = {};
	if (query) {
		option_data = JSON.parse('{"' + decodeURI(query).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g,'":"') + '"}');
	}
	$.extend(data, option_data);
	$.ajax({
		url: 'index.php',
		data: data,
		method:'get',
		beforeSend: function() {
			$("#popup_ria_shadow").show();
	  	},
		success: function(html){
			$("#user-container").html(html);
			if(!$.browser.msie && pushState){
				window.history.pushState(data, "page", location.pathname+'?'+$.param(data));
			}
		}
	}).always(function(){
		$("#popup_ria_shadow").hide();
	})

	return false;
}

/** Met un numéro SIRET au format 000 000 000 00000
 *	@param {string} siret Obligatoire, numéro siret exprimé de façon compactée
 *	@return Le numéro Siret, au format 000 000 000 00000
 */
function formatSiret( siret ){

	siret = $.trim(siret);
	siret.replace( / /g, '' );
	if( siret!='' && siret.length==14 ){
		const str1 = siret.substring( 0, 3 );
		const str2 = siret.substring( 3, 6 );
		const str3 = siret.substring( 6, 9 );
		const str4 = siret.substring( 9, 14 );
		siret = str1 + ' ' + str2 + ' ' + str3 + ' ' + str4;
	}
	return siret;
}


// COPIER/COLLER de la fonction
// Gère la sélection/désélection des images
function previewClick(preview){
}

// fonction qui permet de récupérer une valeur GET (dans l'URL)
function $_GET(param) {
	var vars = {};
	window.location.href.replace( location.hash, '' ).replace(
		/[?&]+([^=&]+)=?([^&]*)?/gi, // regexp
		function( m, key, value ) { // callback
			vars[key] = value !== undefined ? value : '';
		}
	);

	if ( param ) {
		return vars[param] ? vars[param] : null;
	}
	return vars;
}
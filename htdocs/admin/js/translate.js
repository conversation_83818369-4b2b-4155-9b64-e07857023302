$(document).delegate(
	'#delalltsl', 'click', function(){
		if( $(this).is(':checked') ){
			$('#tb-translate tbody input[type=checkbox]').attr('checked', 'checked');
		}else{
			$('#tb-translate tbody input[type=checkbox]').removeAttr('checked');
		}
	}
);

$(document).delegate(
	'.btn-export', 'click', function(){
		displayPopup( translateExportChaine, '', '/admin/config/translate/popup-export.php', '', 400, 330 );
		return false;
	}
);

$(document).delegate(
	'#export-translates', 'click', function(){
		if( !$('#table-languages tbody input:checked').length ){
			alert(translateAlertSelectLangueEwport);
		}else{
			var param = '';
			$('#table-languages tbody input:checked').each(function(){
				param += ( $.trim(param)=='' ? '?' : '&' ) + 'lng[]=' + $(this).val(); 
			});

			parent.hidePopup();
			window.location.href="/admin/config/translate/export.php" + param;
		}

		return false;
	}
);

function refresh_lst_translate(page, context, lng){
	if( lng ){
		$('#begin').val('');
		$('#lng').val( lng );
	}
	
	var p = page ? page : 1;
	var l = lng ? lng : $('#lng').val();
	var c = context ? context : $('#context').val();
	var f = $('#begin').val()!='' ? $('#begin').val() : '';
	var isNotTsl = $('#is_not_tsl').is(':checked') ? 1 : 0;
	page = page>1 ? page : 1;
	
	var location = '/admin/config/translate/index.php?lng=' + l + '&context=' + c + '&filter=' + f + '&page=' + p + '&is_not_tsl=' + isNotTsl;
	$('#fromtranslate').attr( 'action', location );

	$.ajax({
		type: 'POST',
		url: '/admin/config/translate/ajax-translate.php',
		data: 'is_not_tsl='+isNotTsl+'&lng='+l+'&context='+c+'&page='+p+'&filter='+f,
		dataType: 'xml',
		async: true,
		success: function(xml){
			// Traduction
			$('#tb-translate tbody').html( '' );
			var i = 0;
			$(xml).find('translation').each(function(){
				var html = '<tr>';
				html += '<td headers="tsk_checked"><input type="checkbox" name="del[]" value="'+$(this).attr('md5')+'/#/'+$(this).attr('code')+'/#/'+$(this).attr('lng')+'" /></td>';
				html += '<td headers="tsl_context" style="text-transform:uppercase;">'+$(this).attr('context')+'</td>';
				html += '<td headers="tsl_original"><label for="tsl-'+$(this).attr('code')+$(this).attr('md5')+'">'+htmlspecialchars($(this).attr('original'))+'</label></td>';
				html += '<td headers="tsl_translate"><textarea onblur="return addTranslate(\''+$(this).attr('code')+'\', \''+$(this).attr('md5')+'\', '+i+');" rows="'+Math.ceil($(this).attr('original').length/30)+'" name="tsl['+$(this).attr('md5')+']" id="tsl-'+$(this).attr('code')+$(this).attr('md5')+'">'+htmlspecialchars($(this).text())+'</textarea><input type="hidden" name="old-tsl" id="old-'+$(this).attr('code')+$(this).attr('md5')+'" value="'+htmlspecialchars($(this).text())+'" /></td>';
				html += '<td headers="action" id="act-'+i+'"></td>';
				html += '</tr>';
				i++;
				$('#tb-translate tbody').append( html );
			});
			
			// Aucune traduction
			$(xml).find('notranslation').each(function(){
				$('#tb-translate tbody').append('<tr><td colspan="3">' + translateAucuneTraductionResultat + '</td></tr>');
			});
			
			// Pagination
			$('#pagination').html('');
			var pages = $(xml).find('nbpage').attr('nb');
			var pmin = page-5;
			if( pmin<1 )
				pmin = 1;
			var pmax = pmin+9;
			if( pmax>pages )
				pmax = pages;
			if(parseInt(pages) > 1){
				if( page>1 )
					$('#pagination').append( '<a title="Page '+(page-1)+'/'+pages+'" href="#" onclick="refresh_lst_translate('+(page-1)+')">&laquo; ' + translatePrecedent + '&nbsp;|&nbsp;</a>' );
				for(var i = 1; i <= parseInt(pages) ; i++){
					if(i >=pmin && i <= pmax ){
						if(page==i)
							$('#pagination').append('<b>'+i+'</b>');
						else
							$('#pagination').append('<a title="Pages '+i+'/'+pages+'" href="#" onclick="refresh_lst_translate('+i+')">'+i+'</a>');
						if( i<pmax )
							$('#pagination').append('&nbsp;|&nbsp;');
					}
				}
				if( page<pages )
					$('#pagination').append( '&nbsp;|&nbsp;<a title="Page '+(page+1)+'/'+pages+'" href="#" onclick="refresh_lst_translate('+(page+1)+')">' + translateSuivant + ' &raquo;</a>' );
			}
			
			$('#pagination').append('<div class="btn-del"><input type="submit" name="del-tsl" value="Supprimer" /></div>');

			// Nombre de résultats
			$('#nbTsl').html( $(xml).find('nbresult').attr('nbr') );
			
			// Langue
			$('ul.tabstrip li input').each(function(){
				$(this).removeAttr('class');
				if( $(this).attr('name')==l ){
					$(this).addClass('selected');
					$('#tsl_translate').html( $(this).val() );
				}
			});
		}
	});
}
function addTranslate( context, md5, pos ){
	var tsk = $('#tsl-'+context+md5).val();
	if( $('#old-'+context+md5).val()!=tsk ){
		$('#act-'+md5).html( '<img style="border:none;" id="load-'+pos+'" src="/admin/images/loader2.gif" />' );
		$.ajax({
			type: 'POST',
			url: '/admin/config/translate/ajax-translate.php',
			data: 'md5='+md5+'&lng='+$('#lng').val()+'&context='+context+'&tsl='+encodeURIComponent( tsk ),
			dataType: 'xml',
			async: true,
			success: function(xml){
				if( $(xml).find('result').attr('type') == '1' ){
					$('#load-'+pos).remove();
					$('#act-'+pos).html( '<img style="border:none;" id="load-'+pos+'" src="/admin/images/add-search.png" />' );
					$('#old-'+context+md5).val( tsk );
				} else {
					$('#load-'+pos).remove();
					$('#act-'+pos).html( '<img style="border:none;" id="load-'+pos+'" src="/admin/images/del-search.png" />' );
				}
			},
			error: function(){
				$('#load-'+pos).remove();
				$('#act-'+pos).html( '<img style="border:none;" id="load-'+pos+'" src="/admin/images/del-search.png" />' );
			}
		});
	}
}
$(document).ready(function(){
	$('#begin').keyup(function(){
		refresh_lst_translate(1);
	});
	$('#is_not_tsl').click(function(){
		refresh_lst_translate(1);
	});
	$('.context-menu #riawebsitepicker .selectorview').click(function(){
		if($('.context-menu #riawebsitepicker .selector').css('display')=='none'){
			$('.context-menu #riawebsitepicker .selector').show();
		}else{
			$('.context-menu #riawebsitepicker .selector').hide();
		}
	});
	$(".context-menu .selector a").click(function(){
		const language = $(this).attr('name');
		p = language.substring(language.indexOf('-')+1, language.length);
		$('#context').val( p );
		refresh_lst_translate(1, p);
		$('.context-menu #riawebsitepicker .selectorview .view').html( $(this).html() );
		$('.context-menu #riawebsitepicker .selector').hide();
	});
});

/**	Ouvre et ferme le menu contenant la liste des langues actives sur l'installation, pour gérer les traductions
 */
function langPickerCreate( url ){
	
	if($('#rialanguagepicker .selector').css('display')=='none'){
		$('#rialanguagepicker .selector').show();
	}else{
		$('#rialanguagepicker .selector').hide();
	}
	
	$('.selector a').click(function(){
		const language = $(this).attr('name');
		const lng = language.substring(language.indexOf('-')+1, language.length);
		if( url.match( new RegExp("[?]", "g")) ){
			url += '&lng='+lng;
		}else{
			url += '?lng='+lng;
		}
		window.location.href = url;
	});
}
var g_cb1 = null;  // set to active combobox for blur function

function isEmail(email) {
	var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
	return regex.test(email);
}

$(document).ready(function() {
  $(window).click(function() {
		if($('input.email').length){
			if($('input.email').val().length>0){
				$('input.email').addClass('not-empty');
			}
		}

		if($('input.password').length){
			if($('input.password').val().length>0){
				$('input.password').addClass('not-empty');
			}
		}
	});

	if($('input.email').length){
    $('input.email').addClass('not-empty');
    $('input.email').focus();
	}

	$('input.email, input.password').each(function(){
		if($(this).val().length>0){
			$(this).addClass('not-empty');
		}
	});

	$('input.email').focusout(function() {
		if(!isEmail($(this).val()) && $(this).val().length){
			$(this).addClass('error');
		}
		else{
			$(this).removeClass('error');
		}
	});

	$('input.email, input.password').change(function(){
		if($(this).val().length>0){
			$(this).addClass('not-empty');
		}
		else{
			$(this).removeClass('not-empty');
		}
	});
	
	g_cb1 = new combobox('cb1', false);
}); // end ready

$(document).on('click', '.eye', function(){
  $(this).toggleClass("open");
  
  var input = $(this).siblings('input.password');
  
  if (input.attr("type") == "password") {
    input.attr("type", "text");
  } else {
    input.attr("type", "password");
  }
});

// return to first step
$(document).on('click', '[id="content-login"] .main-title:not(.forgot) a', function(){
	$('.central-box .shadow').addClass('show');

	$.ajax({
		type: 'POST',
    url: '/admin/login.php',
    data: 'step=login&email=' + $('[name="email"]').val() + '&sp=' + $('[name="password"]').val(),
		success: function(result){
			$('[id="content-login"]').html(result);
      $('.central-box .shadow').removeClass('show');
      $('input.email').focus();
		}
	});

	return false;
});

// return to second step
$(document).on('click', '[id="content-login"] .main-title.forgot a', function(){
	$('.central-box .shadow').addClass('show');

	$.ajax({
		type: 'POST',
    url: '/admin/login.php',
    data: 'step=password&email=' + $('[name="email"]').val(),
		success: function(result){
			$('[id="content-login"]').html(result);
      $('.central-box .shadow').removeClass('show');
      $('input.email').focus();
		}
	});

	return false;
});

// next step : enter your password
$(document).on('submit', '#f-login', function(){
	if( $('[name="saml"]').length ){
		return true;
	}

	// control if the email is entered and correct
	if( !$('[id="email"]').val().length || !isEmail($('[id="email"]').val()) ){
		$('[id="email"]').addClass('error');
		return false;
	}else{
		$('[id="email"]').removeClass('error');
	}
	
	$('.central-box .shadow').addClass('show');
	
	$.ajax({
		type: 'POST',
		url: '/admin/login.php',
		data: 'step=password&email=' + $('[id="email"]').val() + '&sp=' + $('[id="sp"]').val(),
		success: function(result){
			if( result === 'saml' ){
				// La connexion doit être réalisée via SAML2
				$('#f-login').append('<input type="hidden" name="saml" value="1" />');
				$('#f-login').submit();
				return false;
			}else{
				$('[id="content-login"]').html(result);
				$('.central-box .shadow').removeClass('show');
				$('input.password, input.email').focus();
			}
		}
	});

	return false;
});

// last step : password is write, try connect
$(document).on('submit', '#f-login-2', function(){
  $('.central-box .shadow').addClass('show');
});

// to go forgot password
$(document).on('click', '.link-forgot', function(){
	$('.central-box .shadow').addClass('show');

	$.ajax({
		type: 'POST',
    url: '/admin/login.php',
    data: 'step=forgot&email=' + $('[name="email"]').val(),
		success: function(result){
			$('[id="content-login"]').html(result);
			$('.central-box .shadow').removeClass('show');
			$('input.email').focus();
		}
	});

	return false;
});

// add control before send reinit password mail
$(document).on('submit', '#f-send-pwd', function(){
	// control if the email is entered and correct
	if( !$('[id="email"]').val().length && !isEmail($('[id="email"]').val()) ){
		$('[id="email"]').addClass('error');
		return false;
	}else{
		$('[id="email"]').removeClass('error');
	}
});

// add control befose save new password
$(document).on('submit', '#f-reinit', function(){
	return checkFormReinit('all');
});

// reinit form to reinit password
$(document).on('focus', '#f-reinit [id="email"], #f-reinit [name="password"], #f-reinit [name="password2"]', function(){
	$(this).removeClass('error');
});

// check input for a reinit password
$(document).on('blur', '#f-reinit [id="email"]', function(){
	checkFormReinit('email');
});
$(document).on('blur', '#f-reinit [name="password"]', function(){
	checkFormReinit('pwd');
});
$(document).on('blur', '#f-reinit [name="password2"]', function(){
	checkFormReinit('conf');
});

// check form to reinit password
function checkFormReinit(ctrl){
	var no_error = true;

	// control if the email is entered and correct
	if( ctrl == 'all' || ctrl == 'email' ){
		if( !$('[id="email"]').val().length || !isEmail($('[id="email"]').val()) ){
			$('[id="email"]').addClass('error');
			no_error = false;
		}else{
			$('[id="email"]').removeClass('error');
		}
	}

	// control if the password is entered and correct
	if( ctrl == 'all' || ctrl == 'pwd' ){
		if( $.trim($('[name="password"]').val()) == '' ){
			$('[name="password"]').addClass('error');
			no_error = false;
		}else{
			$('[name="password"]').removeClass('error');
		}
	}

	// control if the confirm password is entered and correct
	if( ctrl == 'all' || ctrl == 'conf' ){
		if ($.trim($('[name="password2"]').val()) == '' || $('[name="password"]').val() != $('[name="password2"]').val() ){
			$('[name="password2"]').addClass('error');
			no_error = false;
		}else{
			$('[name="password2"]').removeClass('error');
		}
	}

	return no_error;
}

//
// keyCodes() is an object to contain keycodes needed for the application
//
function keyCodes() {
  // Define values for keycodes
  this.backspace  = 8;
  this.tab        = 9;
  this.enter      = 13;
  this.esc        = 27;

  this.space      = 32;
  this.pageup     = 33;
  this.pagedown   = 34;
  this.end        = 35;
  this.home       = 36;

  this.up         = 38;
  this.down       = 40;
  
  this.del        = 46;

} // end keyCodes

//
// Function combobox() is a class for an ARIA-enabled combobox widget
//
// @param (id string) id is the id of the div containing the combobox. Text input must have role="combobox".
//
// @param (editable boolean) editable is true if the edit box should be editable; false if read-only.
//
// @return N/A
//
function combobox(id, editable) {

  // Define the object properties

  this.$id = $('#' + id);  // The jQuery object of the div containing the combobox
  this.editable = editable;  // True if the edit box is editable
  this.keys = new keyCodes();

  // Store jQuery objects for the elements of the combobox
  this.$edit = $('#' + id + '-edit');  // The jQuery object of the edit box
  this.$button = $('#' + id + '-button');  // The jQuery object of the button
  this.$buttonImg = $('#' + id).find('img'); // The jQuery object of the button image
  this.$list = $('#' + id + '-list');  // The jQuery object of the option list
  this.$options = this.$list.find('li');  // An array of jQuery objects for the combobox options

  this.$selected; // the current value of the combobox
  this.$focused; // the currently selected option in the combo list
  this.timer = null; // stores the close list timer that is set when combo looses focus

  // Initalize the combobox
  this.init();

  // bind event handlers for the widget
  this.bindHandlers();

} // end combobox constructor


//
// Function init() is a member function to initialize the combobox elements. Hides the list
// and sets ARIA attributes
//
// @return N/A
//
combobox.prototype.init = function() {

  // Hide the list of options
  this.$list.hide().attr('aria-expanded', 'false');

  // If the edit box is to be readonly, aria-readonly must be defined as true
  if (this.editable == false) {
    this.$edit.attr('aria-readonly', 'true');
  }

  // Set initial value for the edit box
  this.$selected = this.$options.filter('.selected');

  if (this.$selected.length > 0) {
    this.$edit.html(this.$selected.text());
  }

} // end initCombo()

//
// Function bindHandlers() is a member function to bind event handlers for the combobox elements
//
// @return N/A
//
combobox.prototype.bindHandlers = function() {

  var thisObj = this;

  ///////////////// bind editbox handlers /////////////////////////

  this.$edit.keydown(function(e) {
    return thisObj.handleEditKeyDown($(this), e);
  });

  this.$edit.keypress(function(e) {
    return thisObj.handleEditKeyPress($(this), e);
  });

  this.$edit.blur(function(e) {
    return thisObj.handleComboBlur($(this), e);
  });

  ///////////////// bind handlers for the button /////////////////////////
  
  this.$button.click(function(e) {
    return thisObj.handleButtonClick($(this), e);
  });

  this.$button.mouseover(function(e) {
    return thisObj.handleButtonMouseOver($(this), e);
  });

  this.$button.mouseout(function(e) {
    return thisObj.handleButtonMouseOut($(this), e);
  });

  this.$button.mousedown(function(e) {
    return thisObj.handleButtonMouseDown($(this), e);
  });

  this.$button.mouseup(function(e) {
    return thisObj.handleButtonMouseUp($(this), e);
  });

  ///////////////// bind listbox handlers /////////////////////////

  this.$list.focus(function(e) {
    return thisObj.handleComboFocus($(this), e);
  });

  this.$list.blur(function(e) {
    return thisObj.handleComboBlur($(this), e);
  });

  ///////////////// bind list option handlers /////////////////////////

  this.$options.keydown(function(e) {
    return thisObj.handleOptionKeyDown($(this), e);
  });

  this.$options.keypress(function(e) {
    return thisObj.handleOptionKeyPress($(this), e);
  });

  this.$options.click(function(e) {
		window.location.href = '?lang=' + $(this).data('lang');
    return thisObj.handleOptionClick($(this), e);
  });

  this.$options.focus(function(e) {
    return thisObj.handleComboFocus($(this), e);
  });

  this.$options.blur(function(e) {
    return thisObj.handleComboBlur($(this), e);
  });

} // end bindHandlers()

//
// Function isOpen() is a member function to get the current state of the list box
//
// @return (boolean) returns true if list box is open; false if it is not
//
combobox.prototype.isOpen = function() {

  return this.$list.attr('aria-expanded') == 'true';

} // end isOpen

//
// Function closeList() is a member function to close the list box if it is open
//
// @param (restore booleam) restore is true if function should restore higlight to stored list selection
//
// @return N/A
//
combobox.prototype.closeList = function(restore) {

  var $curOption = this.$options.filter('.selected');

  if (restore == true) {
    $curOption = this.$selected;

    // remove the selected class from the other list items
    this.$options.removeClass('selected');

    // add selected class to the stored selection
    $curOption.addClass('selected');
  }

  this.$list.hide().attr('aria-expanded', 'false');

  // set focus on the edit box
  this.$edit.focus();

} // end closeList()

//
// Function openList() is a member function to open the list box if it is closed
//
// @param (restore booleam) restore is true if function should restore higlight to stored list selection
//
// @return N/A
//
combobox.prototype.openList = function(restore) {

  var $curOption = this.$options.filter('.selected');


  if (restore == true) {

    if (this.$selected.length == 0) {
      // select the first item
      this.selectOption(this.$options.first());
    }

    $curOption = this.$selected;

    // remove the selected class from the other list items
    this.$options.removeClass('selected');

    // add selected class to the stored selection
    $curOption.addClass('selected');
  }

  this.$list.show().attr('aria-expanded', 'true');

  // scroll to the currently selected option
  this.$list.scrollTop(this.calcOffset($curOption));

  // set focus on the selected item
  this.$selected.focus();

} // end openList();

//
// Function toggleList() is a member function to toggle the display of the combobox options.
//
// @param (restore booleam) restore is true if toggle should restore higlight to stored list selection
//
// Return N/A
//
combobox.prototype.toggleList = function(restore) {

  if (this.isOpen() == true) {

    this.closeList(restore);
  }
  else {
    this.openList(restore);
  }

} // end toggleList()

//
// Function selectOption() is a member function to select a new combobox option.
// The jQuery object for the new option is stored and the selected class is added
//
// @param ($id object) $id is the jQuery object of the new option to select
//
// @return N/A
//
combobox.prototype.selectOption = function($id) {

  // If there is a selected option, remove the selected class from it
  if (this.$selected.length > 0) {
    this.$selected.removeClass('selected');
  }
  
  // add the selected class to the new option
  $id.addClass('selected');

  // store the newly selected option
  this.$selected = $id;

  // update the edit box
  this.$edit.html($id.text());
  
} // end selectOption

//
// Function calcOffset() is a member function to calculate the pixel offset of a list option from the top
// of the list
//
// @param ($id obj) $id is the jQuery object of the option to scroll to
//
// @return (integer) returns the pixel offset of the option
//
combobox.prototype.calcOffset = function($id) {
  var offset = 0;
  var selectedNdx = this.$options.index($id);

  for (var ndx = 0; ndx < selectedNdx; ndx++) {
    offset += this.$options.eq(ndx).outerHeight();
  }

  return offset;

} // end calcOffset

//
// Function handleButtonClick() is a member function to consume button click events. This handler prevents
// clicks on the button from reloading the page. This could also be done by adding 'onclick="false";' to the
// button HTML markup.
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean)  returns false;
//
combobox.prototype.handleButtonClick = function($id,  e) {

  e.stopPropagation();
  return false;

} // end handleButtonClick();

//
// Function handleButtonMouseOver() is a member function to process button mouseover events
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean)  returns false;
//
combobox.prototype.handleButtonMouseOver = function($id,  e) {

  // change the button image to reflect the highlight state
  $id.find('img').attr('src', 'http://www.oaa-accessibility.org/media/examples/images/button-arrow-down-hl.png');

  e.stopPropagation();
  return false;

} // end handleButtonMouseOver();

//
// Function handleButtonMouseOut() is a member function to process button mouseout events
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean)  returns false;
//
combobox.prototype.handleButtonMouseOut = function($id,  e) {

  // reset image to normal state
  $id.find('img').attr('src', 'http://www.oaa-accessibility.org/media/examples/images/button-arrow-down.png');

  e.stopPropagation();
  return false;

} // end handleButtonMouseOut();

//
// Function handleButtonMouseDown() is a member function to process button mousedown events
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean)  returns false;
//
combobox.prototype.handleButtonMouseDown = function($id,  e) {

  // change the button image to reflect the pressed state
  $id.find('img').attr('src', 'http://www.oaa-accessibility.org/media/examples/images/button-arrow-down-pressed-hl.png');

  // toggle the display of the option list
  this.toggleList(true);

  e.stopPropagation();
  return false;

} // end handleButtonMouseDown();

//
// Function handleButtonMouseUp() is a member function to process button mouseup events
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean)  returns false;
//
combobox.prototype.handleButtonMouseUp = function($id,  e) {

  // reset button image
  $id.find('img').attr('src', 'http://www.oaa-accessibility.org/media/examples/images/button-arrow-down-hl.png');

  e.stopPropagation();
  return false;

} // end handleButtonMouseUp();

//
// Function handleOptionKeyDown() is a member function to process keydown events for
// the combobox
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns false if consuming; true if not processing
//
combobox.prototype.handleOptionKeyDown = function($id,  e) {

  var curNdx = this.$options.index($id);

  if (e.ctrlKey) {
    // do not process
    return true;
  }

  switch(e.keyCode) {
    case this.keys.tab: {
      // update and close the combobox

      if ($id.text() != this.$selected.text()) {

        // store the new selection
        this.selectOption($id);
      }

      // Close the option list
      this.closeList(false);

      // allow tab to propagate
      return true;
    }
    case this.keys.esc: {
      // Do not change combobox value

      // Close the option list
      this.closeList(true);

      e.stopPropagation();
      return false;
    }
    case this.keys.enter: {
      // change the combobox value

      if ($id.text() != this.$selected.text()) {

        // store the new selection
        this.selectOption($id);
      }

      // Close the option list
      this.closeList(false);

      e.stopPropagation();
      return false;
    }
    case this.keys.up: {
      
      if (e.altKey) {
        // alt+up toggles the list
        this.toggleList(true);

      }
      else {
        // move to the previous item in the list
      
        if (curNdx > 0) {
          var $prev = this.$options.eq(curNdx - 1);

          // remove the selected class from the current selection
          $id.removeClass('selected');

          // Add the selected class to the new selection
          $prev.addClass('selected');

          // scroll the list window to the new option
          this.$list.scrollTop(this.calcOffset($prev));

          // Set focus on the new item
          $prev.focus();
        }
      }

      e.stopPropagation();
      return false;
    }
    case this.keys.down: {

      if (e.altKey) {
        // alt+up toggles the list
        this.toggleList(true);
      }
      else {
        // move to the next item in the list
      
        if (curNdx < this.$options.length - 1) {
          var $next = this.$options.eq(curNdx + 1);

          // remove the selected from the current selection
          $id.removeClass('selected');

          // Add the selected class to the new selection
          $next.addClass('selected');

          // scroll the list window to the new option
          this.$list.scrollTop(this.calcOffset($next));

          // Set focus on the new item
          $next.focus();
        }
      }

      e.stopPropagation();
      return false;
    }
    case this.keys.home: {
      // select the first list item

      var $first = this.$options.first();

      // remove the selected class from the current selection
      this.$options.eq(curNdx).removeClass('selected');

      // Add the selected class to the new selection
      $first.addClass('selected');

      // scroll the list window to the new option
      this.$list.scrollTop(0);

      // set focus on the first item
      $first.focus();

      e.stopPropagation();
      return false;
    }
    case this.keys.end: {
      // select the last list item

      var $last = this.$options.last();

      // remove the selected class from the current selection
      this.$options.eq(curNdx).removeClass('selected');

      // Add the selected class to the new selection
      $last.addClass('selected');

      // scroll the list window to the new option
      this.$list.scrollTop(this.calcOffset($last));

      // set focus on last item
      $last.focus();

      e.stopPropagation();
      return false;
    }
  }

  return true;

} // end handleOptionKeyDown()

//
// Function handleOptionKeyPress() is a member function to process keypress events for
// the combobox. Needed for browsers that use keypress to manipulate the window
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns false if consuming; true if not processing
//
combobox.prototype.handleOptionKeyPress = function($id,  e) {

  if (e.altKey || e.ctrlKey || e.shiftKey) {
    // do not process
    return true;
  }

  switch(e.keyCode) {
    case this.keys.esc:
    case this.keys.enter:
    case this.keys.up:
    case this.keys.down:
    case this.keys.home:
    case this.keys.end: {
      e.stopPropagation();
      return false;
    }
  }

  return true;

} // end handleOptionKeyPress()

//
// Function handleEditKeyDown() is a member function to process keydown events for
// the edit box.
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @param (e object) e is the event object associated with the event
//
// @return (boolean) Returns false if consuming; true if not processing
//
combobox.prototype.handleEditKeyDown = function($id,  e) {

  var curNdx = this.$options.index(this.$selected);

  if (e.altKey && (e.keyCode == this.keys.up || e.keyCode == this.keys.down)) {

    this.toggleList(true);

    e.stopPropagation();
    return false;
  }

  switch (e.keyCode) {
    case this.keys.backspace:
    case this.keys.del: {
      this.$edit.html(this.$selected.text());

      e.stopPropagation();
      return false;
    }
    case this.keys.enter: {

      // toggle the option list
      this.toggleList(false);

      e.stopPropagation();
      return false;
    }
    case this.keys.up: {
      
      // move to the previous item in the list
      
      if (curNdx > 0) {
        var $prev = this.$options.eq(curNdx - 1);

        this.selectOption($prev);
      }

      e.stopPropagation();
      return false;
    }
    case this.keys.down: {

      // move to the next item in the list
      
      if (curNdx < this.$options.length - 1) {
        var $next = this.$options.eq(curNdx + 1);

        this.selectOption($next);
      }

      e.stopPropagation();
      return false;
    }
  }

  return true;

} // end handleEditKeyDown()

//
// Function handleEditKeyPress() is a member function to process keypress events for
// the edit box. Needed for browsers that use keypress events to manipulate the window.
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns false if consuming; true if not processing
//
combobox.prototype.handleEditKeyPress = function($id,  e) {

  if (e.altKey && (e.keyCode == this.keys.up || e.keyCode == this.keys.down)) {
    e.stopPropagation();
    return false;
  }

  switch(e.keyCode) {
    case this.keys.esc:
    case this.keys.enter: {

      e.stopPropagation();
      return false;
    }
  }

  return true;

} // end handleOptionKeyPress()

//
// Function handleOptionClick() is a member function to process click events for
// the combobox.
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns false
//
combobox.prototype.handleOptionClick = function($id, e) {

  // select the clicked item
  this.selectOption($id);

  // close the list
  this.closeList(false);

  e.stopPropagation();
  return false;  

} // end handleOptionClick()

//
// Function handleComboFocus() is a member function to process focus events for
// the list box
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns true
//
combobox.prototype.handleComboFocus = function() {

  if (this.timer != null) {
    window.clearTimeout(this.timer);
    this.timer = null;
  }

  return true;

} // end handleComboFocus()

//
// Function handleComboBlur() is a member function to process blur events for
// the combobox
//
// @param (e object) e is the event object associated with the event
//
// @param ($id object) $id is the jQuery object for the element firing the event
//
// @return (boolean) Returns true
//
combobox.prototype.handleComboBlur = function() {

  // store the currently selected value
  this.selectOption(this.$options.filter('.selected'));

  // close the list box
  if (this.isOpen() == true) {
    this.timer = window.setTimeout(function() {g_cb1.closeList(false);}, 40);
  }

  return true;

} // end handleComboBlur()

$(document).ready(function(){
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});

	$('#riawebsitepicker .selector a').click(function(){
		var wst = $(this).attr('name');		
		if (wst == 'w-0') {
			wst = 'all';
		}

		$('#riawebsitepicker .selectorview .left .view').html($(this).html());

		reload_lst_search_substitut(false, undefined, undefined, wst);
	});
})
	.delegate('#btn-add-redir-search', 'click', function () {
		addRedirectionSearch();
	})
	.delegate('.btn-upd-redir-search', 'click', function (event) {
		const $elt = $(event.target);
		var id = $elt.data('id');
		var wst = $elt.data('wst');
		addRedirectionSearch(id, wst);
	})
	.delegate('.all-redir-search', 'click', function (event) {
		var $elt = $(event.target);
		$elt.parents('table:eq(0)')
			.find('.check-redir-search')
			.prop('checked', $elt.is(':checked'));
	})
	.delegate('.check-redir-search,.all-redir-search', 'change', function(){
		$('.del-redir-search').toggle($('.check-redir-search:checked,.all-redir-search:checked').length > 0);
	})
	.delegate('#filter', 'keyup', function () {
		if (window.sstFilterInterval) clearInterval(window.sstFilterInterval);
		window.sstFilterInterval = setTimeout(reload_lst_search_substitut, 300);
	})
	.delegate('.sort-redir-search', 'click', function(event){
		var $elt = $(event.target);

		reload_lst_search_substitut(false, $elt.data('sort'), $elt.data('dir'));
	})
	.delegate('.del-redir-search', 'click', function (event) {
		var $elt = $(event.target);

		if (confirm($elt.data('confirm'))) {
			var $form = $elt.parents('form:eq(0)');
			$form.append('<input type="hidden" name="del-redir-search" value="1" />');
			$form.submit();
		}
	})
;

function reload_lst_search_substitut (shouldHidePopup, sort, dir, wstID) {
	if (shouldHidePopup) {
		hidePopup();
	}

	var $form = $('.search-substitut-form');

	var formData = new FormData($form.get(0));

	if (sort) {
		formData.append('sort', sort);
		formData.append('page', 1);
	}

	if (dir) {
		formData.append('dir', dir);
	}

	$('#riawebsitepicker .selector').hide();

	return $.ajax({
		'url' : window.location.href + (typeof wstID != 'undefined' ? (window.location.href.indexOf('?') == -1 ? '?' : '&' ) + 'wst=' + encodeURIComponent(wstID) : ''),
		'data': formData,
		processData: false,
		contentType: false,
		type: 'POST'
	})
		.done(function (response) {
			$form.replaceWith($(response));
		})
	;
}

function addRedirectionSearch( id, wst ){
	return displayPopup(
		'Ajouter une redirection de recherche', 
		'', 
		'/admin/stats/popup-redirection-search.php'
			+ (typeof id != 'undefined' ? '?substitut=' + encodeURIComponent(id) : '')
			+ (typeof wst != 'undefined' ? '&wst=' + encodeURIComponent(wst) : ''), 
		'reload_lst_search_substitut(true, \'date_modified\', \'desc\')'
	);
}

var activeMetas = new Array();

$(document).ready(
	function(){
		loadMetasInfos();

		if( typeof $('#tab_selected') != 'undefined' ){
			var autoSelected = $('#tab_selected').val();
			if( $.trim(autoSelected)!='' ){
				$('#tabpanel table').hide();
				$('.tabstrip input').removeClass('selected');
				
				$('input[name=' + autoSelected + ']').addClass('selected');
				$('#' + autoSelected).show();
			}
		}
	}
).delegate(
	'.tabstrip input', 'click', function(){
		$('#tabpanel table').hide();
		$('.tabstrip input').removeClass('selected');
		
		$(this).addClass('selected');
		$('#' + $(this).attr('name')).show();
		
		loadMetasInfos();

		$('#tab_selected').val( $(this).attr('name') );
		return false;
	}
);

/**
 * 	Cette fonction charge l'information de meta pour les textarea ne l'ayant pas encore et visible.
 */
function loadMetasInfos(){
	$('table:visible .tr-ref-show textarea').each(function(){
		var attrID = $(this).attr('id');
		if( activeMetas.indexOf(attrID)==-1 ){
			if( $('#data').val()=='title' ){
				$(this).riametas({ showFocus: false, padding: true, type : "title" });
			}else if( $('#data').val()=='desc' ){
				$(this).riametas({ showFocus: false, padding: true, type : "desc" });
			}

			activeMetas.push( attrID );
		}
	});
}
/**
 *	Cette fonction permet de naviguer entre chaque page de résultat du rapport.
 */
function movePage( tdPaginationID, page, limitForPage, pages ){
	var pmin = (page-5)<1 ? 1 : page-5;
	var pmax = (pmin+9)>pages ? pages : (pmin+9);
	var pagination = '';

	if( pages>1 ){
		if( page>1 ){
			pagination += '<a href="#" onclick="return movePage(\'' + tdPaginationID + '\', ' + (page-1) + ', ' + limitForPage + ', ' + pages + ');">&laquo; ' + statsReferencementsPrecedente + '</a> | ';
		}

		for( var i=pmin; i<=pmax; i++ ){
			if( i==page ){
				pagination += '<b>' + page + '</b>';
			}else{
				pagination += '<a href="#" onclick="return movePage(\'' + tdPaginationID + '\', ' + (i) + ', ' + limitForPage + ', ' + pages + ');">' + i + '</a>';
			}

			if( i<pmax ){
				pagination += ' | ';
			}
		}

		if( page<pages ){
			pagination += ' | <a href="#" onclick="return movePage(\'' + tdPaginationID + '\', ' + (page+1) + ', ' + limitForPage + ', ' + pages + ');">' + statsReferencementsSuivante + ' &raquo;</a>';
		}
	}

	$('#'+tdPaginationID).parents('table').find('tbody tr.tr-ref-show').removeClass().addClass('tr-ref-hide');
	for( var j=((page-1)*limitForPage-(page>1 ? 1 : 0)) ; j<(page*limitForPage-1) ; j++ ){
		$('#'+tdPaginationID).parents('table').find('tbody tr').eq( j+1 ).removeClass('tr-ref-hide').addClass('tr-ref-show');
	}

	$('#' + tdPaginationID).html( pagination );
	loadMetasInfos();
	return false;
}

/** 
 *	Cette fonction affiche la popup contenant les informations sur un contenu
 */
function showPopupInfo( clsID, objID ){
	var link = '/admin/ajax/popup-infos/';

	switch( clsID ){
		case 1 : {
			link += 'popup-products.php?prd=' + objID;
			break;
		}
		case 3 : {
			link += 'popup-category.php?cat=' + objID;
			break;
		}
		case 5 : {
			link += 'popup-brand.php?brd=' + objID;
			break;
		}
		case 6 : {
			link += 'popup-store.php?str=' + objID;
			break;
		}
		case 11 :{
			link += 'popup-cms.php?cms=' + objID;
			break;
		}
		case 13 :{
			link += 'popup-faq-category.php?cat=' + objID;
			break;
		}
		case 14 :{
			link += 'popup-news.php?news=' + objID;
			break;
		}
		case 16 :{
			link += 'popup-faq-question.php?qst=' + objID;
			break;
		}
	}

	displayPopup( statsReferencementsInformations, '', link, '', 450, 450 );
	return false;
}
Stats_reloadSelectorOrigin = function() {
	$('.riapicker:not(#select-users) .selector .parent').each(function() {
		$(this).find('[type=checkbox]').prop("indeterminate", false).removeAttr('checked');

		var total_check = $(this).parent().find('.child [type=checkbox]').length;
		var total_is_checked = $(this).parent().find('.child [type=checkbox]:checked').length

		if (total_is_checked > 0) {
			if (total_check != total_is_checked) {
				$(this).find('[type=checkbox]').prop("indeterminate", true);
			} else {
				$(this).find('[type=checkbox]').attr('checked', 'checked');
			}
		}
	});
}


var riadatepicker_upd_url = '';
var timer_ReloadData = false;
var is_bestsellers_prd = false;

if( location.pathname.indexOf('/admin/stats/bestsellers-cat.php') != -1 ){
	is_bestsellers_prd = true;
}

$(document).ready(
	function () {
		if (typeof $('#bestseller-bycat') != 'undefined' && $('#bestseller-bycat').length) {
			riadatepicker_upd_url = '/admin/stats/bestsellers-cat.php';

			if ($('#bestseller-bycat tbody tr').length > 1) {
				$('#bestseller-bycat').tablesorter({
					headers: {
						1: { sorter: "riaInteger" },
						2: { sorter: "riaInteger" },
						3: { sorter: "riaInteger" },
						4: { sorter: "riaInteger" },
						4: { sorter: "riaInteger" },
					},
					sortList: [
						[4, 1],
						[5, 1]
					]
				});
			}

			if (typeof $('#bestseller-prd') != 'undefined' && $('#bestseller-prd').length) {
				$('#bestseller-prd').tablesorter({
					headers: {
						2: { sorter: "riaInteger" },
						3: { sorter: "riaInteger" },
						4: { sorter: "riaInteger" },
						5: { sorter: "riaInteger" },
					},
					sortList: [
						[3, 1],
						[4, 1]
					]
				});
			}
		} else if (typeof $('#bestseller-prd') != 'undefined' && $('#bestseller-prd').length) {
			riadatepicker_upd_url = '/admin/stats/bestsellers-prd.php';

			if ($('#bestseller-prd tbody tr').length > 1) {
				$('#bestseller-prd').tablesorter({
					headers: {
						2: { sorter: "riaInteger" },
						3: { sorter: "riaInteger" },
						4: { sorter: "riaInteger" },
						5: { sorter: "riaInteger" },
					},
					sortList: [
						[4, 1],
						[5, 1]
					]
				});
			}
		}

		Stats_reloadSelectorOrigin();
	}
).delegate(
	'#riawebsitepicker .selectorview', 'click', function () {
		if ( $('#seach-wo-results').length == 0 && $('#bestseller-bycat').length == 0 && $('#bestseller-prd').length == 0)  {
			if ($("#riawebsitepicker .selector").css('display') == 'none')
				$("#riawebsitepicker .selector").show();
			else
				$("#riawebsitepicker .selector").hide();
		}
	}
).delegate(
	'.selector a', 'click', function () {
		if ( $('#seach-wo-results').length == 0  && $('#bestseller-bycat').length == 0 && $('#bestseller-prd').length == 0)  {
			var wst = $(this).attr('name').replace('w-', '');
			if (wst == 'all' || !wst)
				wst = 0;

			$(this).attr('href', 'search-suggestions.php?wst=' + wst);
		}
	}
).delegate(
	'#scc-checkbox', 'click', function () {
		$("input[name='scc[]']").each(function () {
			if ($("#scc-checkbox").is(':checked'))
				$(this).attr('checked', 'checked');
			else
				$(this).removeAttr('checked');
		});
	}
).delegate(
	'input[name=\'scc[]\']', 'click', function () {
		if (!$(this).is(':checked'))
			$("#scc-checkbox").removeAttr('checked');
	}
).delegate(
	'#new-suggest', 'click', function () {
		if ($("#form-add-suggest").hasClass('none'))
			$("#form-add-suggest").removeClass('none');
		else
			$("#form-add-suggest").addClass('none');
	}
).delegate(
	'#cancel-suggest', 'click', function () {
		$("#form-add-suggest").addClass('none');
	}
).delegate(
	'#before-visual', 'click', function () {
		if ($("#suggest").val() == "")
			alert(statsAlertChampSuggestion);
		else {
			show_search($("#suggest").val(), $("#seg").val(), -1, $("#section").val());
		}
	}
).delegate(
	'#selectorigins .selectorview', 'click', function () {
		$('#selectorigins .selector').toggle();
	}
).delegate(
	'#selectorigins .selector a', 'click', function () {
		var attrOrigin = $(this).attr('name');

		if (attrOrigin == 'all' || attrOrigin == 'noorigin' || attrOrigin == 'gescom' || attrOrigin == 'web') {
			$('#selectorigins .selectorview .left .view').html($(this).html());
			$('#selectorigins .selector').hide();
			$('#selectorigins input:checked').removeAttr('checked');

			$('#gescom').val(attrOrigin == 'gescom');
			$('#origin').val(attrOrigin);

			if (typeof $('#bestseller-bycat') != 'undefined' && $('#bestseller-bycat').length) {
				var action = '/admin/stats/bestsellers-cat.php?origin=' + $(this).attr('name') + '&parent=' + $('#parent_id').val();
				reloadBestsellerCat(action);
			}else if (typeof $('#bestseller-prd') != 'undefined' && $('#bestseller-prd').length) {
				if ($('form.riaFilters').length) {
					var data = $.reduce($('form.riaFilters input[type=hidden]'), function (data, el) {
						data[el.name] = el.value;
						return data;
					}, {});
					data['origin'] = $(this).attr('name');
					var action = '/admin/stats/bestsellers-prd.php?' + $.param(data);
				} else {
					// action = '/admin/stats/bestsellers-prd.php?' + origin;
					var action = '/admin/stats/bestsellers-prd.php?origin=' + $(this).attr('name');
				}
				reloadBestsellerPrd(action);
			} 
		}
	}
).delegate(
	'#selectorigins input[type=checkbox]', 'click', function () {
		var parent = $(this).parent();

		if (parent.hasClass('parent')) {
			var is_checked = parent.find('[type=checkbox]').is(':checked');

			if (is_checked) {
				parent.parent().find('.child [type=checkbox]').attr('checked', 'checked');
			} else {
				parent.parent().find('.child [type=checkbox]').removeAttr('checked');
			}
		}

		Stats_reloadSelectorOrigin();

		clearTimeout(timer_ReloadData);
		timer_ReloadData = setTimeout(function () {
			var origin = '';
			var label = '';

			$('#selectorigins input:checked').each(function () {
				origin += ($.trim(origin) != '' ? '&' : '') + 'origin[]=' + $(this).val();
				label += ($.trim(label) != '' ? ', ' : '') + $(this).parent().find('label').html();
			});

			if ($.trim(label) == '') {
				label = 'Tout le trafic';
			} else {
				if (label.length > 32) {
					label = label.substring(0, 32) + '...';
				}
			}
			$('#origin').val(origin);

			$('#selectorigins .view').html(label);
			if (typeof $('#bestseller-prd') != 'undefined' && $('#bestseller-prd').length && typeof $('#bestseller-bycat') != 'undefined' && $('#bestseller-bycat').length) {
				//	Lorsque les deux tableaux sont disponibles, on ne recharge qu'avec reloadBestsellerCat()
				//	La fonction se charge de recharger les deux tableaux
				$('#selectorigins .view').html(label);
				var action = '/admin/stats/bestsellers-cat.php' + '?parent=' + $('#parent_id').val();
				if ($.trim(origin) != '') {
					action = '/admin/stats/bestsellers-cat.php?' + origin + '&parent=' + $('#parent_id').val();
				}
				reloadBestsellerCat(action);
			}
			else if (typeof $('#bestseller-prd') != 'undefined' && $('#bestseller-prd').length) {
				var action = '/admin/stats/bestsellers-prd.php';
				if ($('form.riaFilters').length) {
					var data = $.reduce($('form.riaFilters input[type=hidden]'), function (data, el) {
						data[el.name] = el.value;
						return data;
					}, {});
					action = '/admin/stats/bestsellers-prd.php?' + $.param(data);
				} else if ($.trim(origin) != '') {
					action = '/admin/stats/bestsellers-prd.php?' + origin;
				}
				reloadBestsellerPrd(action);
			} else if (typeof $('#bestseller-bycat') != 'undefined' && $('#bestseller-bycat').length) {
				$('#selectorigins .view').html(label);
				var action = '/admin/stats/bestsellers-cat.php' + '?parent=' + $('#parent_id').val();
				if ($.trim(origin) != '') {
					action = '/admin/stats/bestsellers-cat.php?' + origin + '&parent=' + $('#parent_id').val();
				}
				reloadBestsellerCat(action);
			}
		}, 500);
	}
).delegate(
	'#selectseller .selectorview', 'click', function () {
		if ($('#selectseller .selector').css('display') == 'none') {
			$('#selectseller .selector').show();
		} else {
			$('#selectseller .selector').hide();
		}
	}
).delegate(
	'#selectseller .selector a', 'click', function () {
		$('#selectseller .selectorview .left .view').html($(this).html());
		$('#selectseller .selector').hide();

		$('#ord_seller_id').val($(this).attr('name'));

		if (refresh_graph_in_ajax) {
			reload_graph();
		} else if (refresh_in_ajax) {
			reload_lst_orders(1, true);
		} else if (is_bestsellers_prd) {
			reloadBestsellerPrd('/admin/stats/bestsellers-prd.php');
		}

		$('#seller').val($(this).attr('name').replace('seller-', ''));
		update_url();
	}
).delegate(
	'#select-users .selectorview', 'click', function () {
		$('#select-users .selector').toggle();
	}
).delegate(
	'#select-users input[type=checkbox]', 'click', function () {
		composeUrlToFilterUsers();
	}
).delegate(
	'#wosubstitut', 'click', function(){
		window.location = $('form#seach-wo-results').attr('action').replace(/&wosubstitut=\d/, '') + ($(this).prop('checked') ? '&wosubstitut=1' : '');
	}
);

function composeUrlToFilterUsers() {
	var params = {
		users: []
	};

	$('#select-users input[type=checkbox]').each(function (i, el) {
		params.users.push(el.value + '-' + (el.checked ? '1' : '0'));
	});

	reloadBestsellerCat('/admin/stats/bestsellers-cat.php?' + $.param(params));
}

function reloadBestsellerPrd( action ){

	$('#bestseller-prd tbody').css( 'opacity', '0.1' );

	$.get( action, function( data ){
		$('#bestseller-prd.checklist tbody').html(data);
		$('#bestseller-prd thead *').unbind('click');

		if( $('#bestseller-prd.checklist tbody tr').length>1 ){
			$('#bestseller-prd.checklist').tablesorter({
				headers: {
					2: { sorter: "riaInteger" },
					3: { sorter: "riaInteger" },
					4: { sorter: "riaInteger" },
					5: { sorter: "riaInteger" },
				},
				sortList: [
					[4, 1],
					[5, 1]
				]
			});
		}

		$('#bestseller-prd tbody').css( 'opacity', '1' );
	});
}

function reloadBestsellerCat( action ){
	// Ajout du filtre sur le représentant
	action += '&seller=' + $('#ord_seller_id').val();

	$('#bestseller-bycat tbody').css( 'opacity', '0.1' );
	$('#bestseller-prd tbody').css( 'opacity', '0.1' );

	$.get( action, function( data ){
		//	Attention, le html rechargé ici contient les DEUX tableaux
		$('.stats-content').html( data );

		//	On recharge les tablesorter pour les DEUX tableaux (s'ils existent)
		$('#bestseller-bycat thead *, #bestseller-prd thead *').unbind('click');

		if( $('#bestseller-bycat.checklist tbody tr').length>1 ){
			$('#bestseller-bycat.checklist').tablesorter({
				headers: {
					1: { sorter: "riaInteger" },
					2: { sorter: "riaInteger" },
					3: { sorter: "riaInteger" },
					4: { sorter: "riaInteger" },
				},
				sortList: [
					[3, 1],
					[4, 1]
				]
			});
		}

		if( $('#bestseller-prd.checklist tbody tr').length>1 ){
			$('#bestseller-prd.checklist').tablesorter({
				headers: {
					2: { sorter: "riaInteger" },
					3: { sorter: "riaInteger" },
					4: { sorter: "riaInteger" },
					5: { sorter: "riaInteger" },
				},
				sortList: [
					[4, 1],
					[5, 1]
				]
			});
		}

		$('#bestseller-bycat tbody').css( 'opacity', '1' );
		$('#bestseller-prd tbody').css( 'opacity', '1' );
	});
}

/**	Gère la pagination dans l'écran de gestion des poids (Statistiques > Poids des produits à compléter)
 * 
 * @param {*} page Page en cours de consultation 
 * @param {*} pages Nombre de pages du résultat
 */
function prdSwitchTablePage( page, pages ){
	// Place le scroll en haut à gauche de l'écran
	swichPageLoad(5);

	// Requête AJAX pour le changement de page
	$.ajax({
		type: "POST",
		url: '/admin/ajax/products/json-products.php',
		data: 'p='+page+'&no_weight=1&limit=30',
		dataType: 'json',
		async:true,
		success: function(msg){
			// Créé le contenu du tableau
			var html = title = desc = '';
			for( i=0 ; i<msg.length ; i++ ){
				title = desc = '';

				// Détermine le titre du produit
				title = msg[i].title ? msg[i].title : msg[i].name;

				// Coupe la description si elle est trop longue
				if( msg[i].desc ) desc = msg[i].desc;
				if( desc.length>105 )
						desc = desc.substr(0, 102)+'...';
				html += '	<tr>';
				html += '		<td headers="ref"><input type="hidden" name="'+msg[i].id+'[id]" value="'+msg[i].id+'" /><input type="hidden" name="'+msg[i].id+'[ref]" value="'+htmlspecialchars(msg[i].ref)+'" />'+htmlspecialchars(msg[i].ref)+'</td>';
				html += '		<td headers="desc"><span class="title">'+htmlspecialchars(title)+'</span><br /><span class="desc">'+htmlspecialchars(desc)+'</span></td>';
				html += '		<td headers="sells" class="stat-weight">'+(msg[i].selled>0 ? msg[i].selled : 0)+'</td>';
				html += '		<td headers="weight" class="stat-weight"><input type="text" name="'+msg[i].id+'[weight]" id="w-'+msg[i].id+'" value="'+( msg[i].weight ? msg[i].weight : '' )+'" /></td>';
				html += '		<td headers="weight-net" class="stat-weight"><input type="text" name="'+msg[i].id+'[weight-net]" id="wn-'+msg[i].id+'" value="'+( msg[i].weight_net ? msg[i].weight_net : '' )+'" /></td>';
				html += '	</tr>';
			}

			// Affiche le contenu
			$("#site-content table tbody").html(html);

			// Modifie la pagination
			$("#pagination").html( switchPage(page, pages, 5, 9, 0, 4, 'prdSwitchTablePage', '', 'prd-weight-fills.php', '') );
		},
		completed: function(){
			Stats_reloadSelectorOrigin();
		},
		error: function(){
			return true;
		}
	});
	return false;
}

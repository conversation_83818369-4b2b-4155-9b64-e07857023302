$(document).ready(function(){
	
	// Ouvre la popup d'ajout de redirection
	if( $('#btn-add-redir-search').length ){
		$('#btn-add-redir-search').click(function(){
			addRedirectionSearch();
		});
	}
	
	if( $('#filter').length ){
		var interval = false;
		$('#filter').live('keyup',function(){
			if( interval ) clearInterval( interval );
			interval = setTimeout('reload_lst()',300);
		});
	}
	
	// Assure le chargement initial du résultat de recherche
	if( $('.stats-search-table tbody').length ){
		$('.stats-search-table tfoot').html( '' );
		displayLoadMessage();
		
		$.ajax({
			type : 'post',
			url : '/admin/stats/ajax-action-search.php',
			data : 'getNbRes=1&seg=' + _Seg + '&search=' + _SearchUrlEncode + '&section=' + _Section + '&active=' + _ActiveNoPublish + '&scc=' + _Scc + '&cnt=' + _Cnt,
			dataType : 'html',
			async : false,
			success : function( data ){
				_NbRes = parseInt( data );
				displayLoadMessage();
				loadPageResults( 1 );
			}
		});
	}
	
	// Filtre "N'afficher que les résultats activés"
	if( $('#filter-active').length ){
		$('#filter-active').click(function(){
			return parent.show_search( _Search, _Seg,  _Scc, _Section, 0, $('#filter-active').is(':checked') ? 1 : 0 );
		});
	}
	
	if( $('.stats-search-table tfoot a').length ){
		actionHrefPagination();
	}
});

function reload_lst(){
	reload_lst_search(1);
}

function addRedirectionSearch(seg, search, lng, wst){
	var params = '';

	params += (seg ? '?seg='+encodeURIComponent(seg): '');
	params += (search ? (params == '' ? '?' : '&')+'old='+encodeURIComponent(search): '');
	params += (lng ? (params == '' ? '?' : '&')+'lng='+encodeURIComponent(lng): '');
	params += (wst ? (params == '' ? '?' : '&')+'wst='+encodeURIComponent(wst): '');
	
	return displayPopup( 
		statsSearchAjoutRedirectionRecherche, 
		'', 
		'/admin/stats/popup-redirection-search.php'+params
	);
}

function redirectionSearchPreview( inPut ) {
	var valSearch = inPut.prev().val();
	
	$('#results, #head-results').html ( '' );
	$('#results, #head-results').hide();
	
	if( $.trim(valSearch)=='' ){
		return false;
	}
	
	var seg = $('#engines').val() != '-1' ? $('#engines').val() : '-1';
	var lng = $('#search-lng').val();
	var urlParams = '?seg=' + seg + '&q=' + valSearch + '&lng=' + lng;
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/search/json-search.php' + urlParams,
		dataType: 'json',
		success: function( data ){
			var html = '';
			var url_img = false;
			
			if( data.search!=undefined && data.search.length ){
				for( var i = 0 ; i<data.search.length ; i++ ){
					s = data.search[i];
					
					url_img = data.url_site + s.img;
					
					html += '<div class="result">';
					html += '	<a target="_blank" href="' + s.url_alias + '">';
					html += '		<img width="80" height="80" title="' + s.name + '" alt="' + s.name + '" src="' + url_img + '" />';
					html += '	</a>';
					html += '	<div><a target="_blank" href="' + s.url_alias + '">' + s.name + '</a></div>';
					html += '	<div class="wo-search-desc" id="desc-765400">' + s.desc + '</div>';
					html += '</div>';
					html += '<div class="clear"></div>';
				}
				
				$('#results').html ( html );
			} else {
				$('#results').html ( 'Aucun résultat' );
			}
			
			$('#head-results').html ( '<p>' + statsSearch25PremiersResultats + '"' + valSearch + '"</p>' );
			$('#results, #head-results').show();
		}
	});

	return false;
}
		
function action_search(cnt, seg, scc, publish, all, score, clics, nbkey)
{
	publish = publish ? 1 : 0;
	$.ajax({
		type: "POST",
		url: 'ajax-action-search.php',
		data: 'cnt='+cnt+'&seg='+seg+'&scc='+scc+'&publish='+publish+(all ? '&all=yes' : '&all=no')+'&score='+score+'&clics='+clics+'&nbkey='+nbkey,
		async:false,
		success: function(){
			if( publish ){
				if( $("#add-all-search-"+cnt).css("display") == "none" || all){
					$("#tr-"+cnt).removeAttr('class');
					$("#name-"+cnt).removeAttr('class');
					$("#desc-"+cnt).removeAttr('class');
					$("#desc-"+cnt).addClass('wo-search-desc');
					$("#img-"+cnt).removeAttr('class');
					$("#alt-url-"+cnt).removeAttr('class');
				}
				$("#alt-url-"+cnt).addClass('wo-search-url');
				$("#del-search-"+cnt).show();
				$("#add-search-"+cnt).hide();
				if( all ){
					$("#del-all-search-"+cnt).show();
					$("#add-all-search-"+cnt).hide();
				}
			} else {
				$("#tr-"+cnt).addClass('cnt-hide');
				$("#name-"+cnt).addClass('cnt-hide');
				$("#desc-"+cnt).addClass('cnt-hide');
				$("#img-"+cnt).addClass('img-cnt-hide');
				$("#alt-url-"+cnt).removeAttr('class');
				$("#alt-url-"+cnt).addClass('cnt-hide-url');
				$("#del-search-"+cnt).hide();
				$("#add-search-"+cnt).show();
				if( all ){
					$("#del-all-search-"+cnt).hide();
					$("#add-all-search-"+cnt).show();
				}
			}
		},
		error: function(){
			alert( statsSearchAlertErreurEnregistrementDemande );
		}
	});
}

// Active les liens de pagination (un rechargement en ajax est réalisé au clic)
function actionHrefPagination(){
	$('.stats-search-table tfoot a').click(function(){
		var page = $(this).html();
		if( page=='Page suivante »' ){
			page = parseInt(_PageShow) + 1;
		} else if( page=='« Page précédente' ){
			page = parseInt(_PageShow) - 1;
		}
		
		_PageShow = page;
		loadPageResults( page );
		return false;
	});
}

// Crée les liens de pagination
function createPagination(){
	var pages = Math.ceil( _NbRes / _NbResByPage );
	var page = parseInt(_PageShow);
	var pagination = '';
	
	pagination += '	<tr>';
	pagination += '		<td colspan="2">Page ' + page + ' / ' + pages + '</td>';
	pagination += '		<td colspan="4" style="text-align:right;">';
	
	var url = 'js_search.php?seg=' + _Seg + '&amp;scc=' + _Scc + '&amp;search=' + _SearchUrlEncode + '&amp;section=' + _Section + '&amp;cnt=' + _Cnt + '&amp;active=1';
	var links = new Array();
	
	if( page>1 )
		links.push( '<a href="' + url + '&amp;page=' + (page-1) + '">&laquo; ' + statsSearchPagePrecedente + '</a>' );
	for( var j=page-5; j<=page+5; j++ ){
		if( j>=1 && j<=pages ){
			if( j==page ){
				links.push( '<strong>' + j + '</strong>' );
			}else{
				links.push( '<a href="' + url + '&amp;page=' + j + '">' + j + '</a>' );
			}
		}
	}
	if( page<pages )
		links.push( ' <a href="' + url + '&amp;page=' + (page+1) + '">' + statsSearchPageSuivante + ' &raquo;</a>' );
	
	pagination +=  links.join( ' | ' );
	pagination += '		</td>';
	pagination += '	</tr>';
	
	$('.stats-search-table tfoot').html( pagination );
	actionHrefPagination();
}

// Réalise le chargement des pages de résultat
function loadPageResults( page ){
	displayLoadMessage();
	if( parseInt(page)>parseInt(_MaxPageShow) ){
		_MaxPageShow = page;
		
		var resultsShow = $('.stats-search-table tbody tr:not(#load-stats-search)').length;
		
		// Récupère les résultats de la page demandé
		$.ajax({
			type : 'post',
			url : '/admin/stats/ajax-action-search.php',
			data : 'getResPage=1&seg=' + _Seg + '&search=' + _SearchUrlEncode + '&section=' + _Section + '&active=' + _ActiveNoPublish + '&page=' + page + '&scc=' + _Scc + '&cnt=' + _Cnt + '&nbres=' + resultsShow,
			dataType : 'html',
			async : false,
			success : function( data ){
				$('.stats-search-table tbody').append( data );
			}
		});
	}
	
	displayLoadMessage();
	var startElem = (page-1) * _NbResByPage;
	for( var i=startElem ; i<(startElem + _NbResByPage) ; i++ ){
		$('.stats-search-table tbody tr:eq(' + i + ')').show();
	}
	$('html, body').animate({scrollTop:0}, 'slow');
	createPagination();
}

// Affiche le message indicateur de chargement
function displayLoadMessage(){
	$('.stats-search-table tbody tr').hide();
	if( $('#load-stats-search').length ){
		$('#load-stats-search').remove();
		return false;
	}
	
	var trLoadMessage = '';
	trLoadMessage += '<tr id="load-stats-search">';
	trLoadMessage += '	<td colspan="2"></td><td>';
	trLoadMessage += '		<div class="message">';
	trLoadMessage += '			<img width="40" height="40" src="/admin/images/json-load.gif" title="" alt="">';
	trLoadMessage += '			<span>' + msgLoading + '</span>';
	trLoadMessage += '		</div>';
	trLoadMessage += '	</td>';
	trLoadMessage += '</tr>';
	$('.stats-search-table tbody').append( trLoadMessage )
}
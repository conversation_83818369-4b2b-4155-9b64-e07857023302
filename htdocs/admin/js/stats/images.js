$(document).ready(function() {
	$('#file').focus(function() {
		if (current) {
			current.removeClass('selected');
			current = null;
		}
		$('#mode_file').click();
	});
	
	var count = 0;
	var ajax;
	var mediatheque = $('#mediatheque');
	var h = mediatheque.height();
	
	var getId = function(img) {
		var id = (new RegExp('^.*-([0-9]+)$')).exec(img.attr('id'));
		if (! (id && id[1] !== undefined)) return ;
		return id[1];
	};
	
	var current;
	var bindImages = function(imgs) {
		imgs.find('img').each(function() {
			$(this).click(function() {
				if (current) current.removeClass('selected');
				$(this).addClass('selected');
				current = $(this);
				$('#mode_mediatheque').click();
			});
		});
	}
	
	var doSearch = function() {
		if (ajax) ajax.abort();
		
		mediatheque.empty();
		
		count = 0;
		ajax = null;
		evalScroll();
	};
	
	var curType;
	$('#nav').find('a').each(function() {
		if (! curType) curType = $(this);
		$(this).click(function() {
			curType.removeClass('selected');
			$(this).addClass('selected');
			curType = $(this);
			$('#type').val((new RegExp('^type-(.*)$')).exec($(this).attr('id'))[1]);
			doSearch();
			return false;
		});
	});
	$('#type').val('all');
	
	var evalScroll = function() {
		if (ajax) return ;
		
		var scroll = mediatheque.scrollTop();
		var scrollMax = document.getElementById('mediatheque').scrollHeight;
		
		var error = function() {
			ajax = null;
			evalScroll();
		};
		
		if (scroll >= scrollMax - 2 * h) {
			ajax = $.ajax({
				'url'		:	'/admin/ajax/tinymce/ajax-images.php',
				'data'		:	$('#form').serialize() + '&page=' + (count+1),
				'type'		:	'post',
				'dataType'	: 	'json',
				'success'	:	function(response) {
									if (! response) return error();
									
									var i, img;
									var t = $('<div></div>');
									var images = response.images || [];
									var thumb = response.thumb;
									
									for (i in images) {
										img = images[i];
										t.append($('<img id="img-' + img.id + '" src="/images/products/' + thumb.width + 'x' + thumb.height + '/' + img.id + '.' + thumb.format + '" alt="" width="' + thumb.width + '" height="' + thumb.height + '" />'));
									}
									bindImages(t);
									
									mediatheque.append(t);
									
									count++;
									
									if (images.length) {
										ajax = null;
										evalScroll();
									}
								},
				'error'		:	function(xhr, e) { return error(e); }
			});
		}
	};
	mediatheque.scroll(evalScroll);
	evalScroll();
	
	$('#form').submit(function() {
		if( $('#mode_mediatheque').attr('checked') ){
			var img = $('#mediatheque').find('img.selected');
			if (img.length) $('#media').val(getId(img));
		}
	});
	
	$('#src').keydown(function(e) {
		if( e.which==13 ){
			doSearch();
			return false;
		}
	});
	$('#search').click(function() {
		doSearch();
		return false;
	});
});

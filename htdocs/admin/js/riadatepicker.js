	// Initialise le sélecteur de période
	$(document).ready(function(){
		if( typeof autorefresh != 'undefined' ){
			$('#wosubstitut').change(function(){
				reload_lst_search(1);
			});

			init_RiaDatepicker();
			check_search( true );
		}
	});

	var parameters = new Array();
	var last_function = ''; // Contient l'intitulé de la période en cours (Aujourd'hui, Hier, etc...)
	
	// Permet le rafraichissement des listes
	function refresh_view(){
		var reg = new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString();
		date = date.split(reg);
		
		var options = {year: 'numeric', month: 'short', day: 'numeric' };
	
		var tmp = date[0].split('/');
		var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
		const formated_date_1 = event.toLocaleDateString(document.documentElement.lang, options);
		
		tmp = date[1].split('/');
		var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
		const formated_date_2 = event.toLocaleDateString(document.documentElement.lang, options);

		if( date[0] == date[1] ){
			$('#riadatepicker .view').html(formated_date_1);
		}else{
			$('#riadatepicker .view').html(riadatepickerDateFromTo.replace('#param[date1]#' ,formated_date_1).replace('#param[date2]#' ,formated_date_2))
		}
		$('#riadatepicker .function_name').html(last_function)
		
		$('#riadatepicker .selectordate').hide();
		$('#riadatepicker .selector').hide();
		
		
		if($('#date1').length > 0){	
			$('#date1').val(date[0]);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="date1" name="date1" value="'+date[0]+'"/>');
		}
		if($('#date2').length > 0){	
			$('#date2').val(date[1]);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="date2" name="date2" value="'+date[1]+'"/>');
		}
		if($('#last').length > 0){
			$('#last').val(last_function);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="last" name="last" value="'+last_function+'"/>');
		}
		if( refresh_graph_in_ajax ){
			reload_graph();
		}else if( refresh_in_ajax ){
			reload_lst_search(1);
		}
		callback_riadatepcker();
		update_url();
	}

	// Met à jour l'url avec les paramètres de date sélectionnés, pour que la fonction F5 fonctionne correctement et que le lien direct puisse être partagé
	function update_url(){
		var reg = new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString().split(reg);
		
		var wst = $('#wst').val();
		if( wst =='all' || !wst) wst = 0;
		wst = wst.toString().replace('w-','');
		
		var origin = '';
		$('#selectorigins input:checked').each(function(){
			origin += ( $.trim(origin)!='' ? '&' : '' ) + 'origin[]=' + $(this).val();
		});
		
		if( typeof riadatepicker_upd_url != 'undefined' ){
			var param = riadatepicker_upd_url.match(/\?/g) ? '&' : '?';
			if (origin.trim() == '' && $('#origin').length && $('#origin').val().trim() != '') {
				origin = $('#origin').val()
				param += origin + '&';
			}
			
			if( $.trim(riadatepicker_upd_url)!='' ){
				if( riadatepicker_upd_url=='/admin/stats/bestsellers-cat.php' ){
					reloadBestsellerCat( riadatepicker_upd_url + param + 'wst=' + wst + '&date1=' + date[0] + '&date2=' + date[1] + '&parent=' + $('#parent_id').val() );
				}else{
					if ($('form.riaFilters').length){
						var data = $.reduce($('form.riaFilters input[type=hidden]'),function(data, el){
							data[el.name] = el.value;
							return data;
						}, {});
						var action = riadatepicker_upd_url + '?' + $.param(data);
					}else{
						var action = riadatepicker_upd_url + param + 'wst=' + wst + '&date1=' + date[0] + '&date2=' + date[1];
					}
					$.get( action, function( data ){
						$('.checklist tbody').html(data);

						//	Immédiatement après la mise à jour du HTML, on doit redéfinir tablesorter
						$('#bestseller-prd thead *').unbind('click');
						$('#bestseller-prd').tablesorter({ 
							headers: {
								3: { sorter: "riaInteger" },
								4: { sorter: "riaInteger" },
								5: { sorter: "riaInteger" },
								6: { sorter: "riaInteger" },
								7: { sorter: "riaInteger" },
							},
							sortList: [
								[6, 1],
								[7, 1]
							] 
						});

					});
				}

				return false;
			}else{
				var lng = typeof $('#lng') !='undefined' ? $('#lng').val() : '';
				window.location.href = riadatepicker_upd_url + param + 'wst=' + wst + '&date1=' + date[0] + '&date2=' + date[1] + ($.trim(lng)!='' ? '&lng=' + lng : '');
			}
		}

		$('#site-content button.export-btn').click(function(){
			window.location.href = 'export-search-sylk.php?wst=' + wst + '&date1=' + date[0] + '&date2=' + date[1];
		});

	}

	// Fonction permettant l'initialisation de la sélection de date
	function init_RiaDatepicker(){
		if( autorefresh == false && refresh_in_ajax == true ){
			setInterval("check_search(false)",10000);
		}
		
		if( location.pathname.indexOf('/admin/stats/orders.php') == -1){
			$('#riawebsitepicker .selectorview').click(function(){
				if($('#riawebsitepicker .selector').css('display')=='none'){
					$('#riawebsitepicker .selector').show();
					$('#riadatepicker .selector').hide();
				}else{
					$('#riawebsitepicker .selector').hide();
				}
			});

			$('#riawebsitepicker .selector a').click(function(){
				if($('#wst').length > 0){
					$('#wst').val($(this).attr('name'));	
				}else{
					$('#riawebsitepicker').after('<input id="wst" type="hidden" name="wst" value="'+$(this).attr('name')+'"/>');
				}
				$('#riawebsitepicker .selectorview .left .view').html($(this).html());
				$('#riawebsitepicker .selector').hide();
				
				if( refresh_graph_in_ajax ){
					reload_graph();
				}else if( refresh_in_ajax ){
					reload_lst_search(1);
				}
				callback_riadatepcker();
				update_url();
			});
		}
	
		var html = '';
		html += '	<div class="selectorview">';
		html += '		<div class="left">';
		html += '			<span class="function_name"></span>';
		html += '			<br/><span class="view"></span>';
		html += '		</div>';
		html += '		<a class="btn" name="btn">';
		html += '			<img src="/admin/images/stats/fleche.gif" height="8" width="16"/>';
		html += '		</a>';
		html += '		<div class="clear"></div>';
		html += '	</div>';
		html += '	<div class="selector">';
		html += '		<a name="perso">' + riadatepickerPeriodePerso + '</a>';
		html += '		<a class="selector-sep" ></a>';
		html += '		<a name="today">' + riadatepickerAujourdhui + '</a>';
		html += '		<a name="yesterday">' + riadatepickerHier + '</a>';
		html += '		<a name="thisweek">' + riadatepicker7DerniersJours + '</a>';
		html += '		<a name="lastweek">' + riadatepickerSemaineDerniere + '</a>';
		html += '		<a name="last2week">' + riadatepicker14DerniersJours + '</a>';
		html += '		<a name="last30days">' + riadatepicker30DerniersJours + '</a>';
		html += '		<a name="thismonth">' + riadatepickerMoisCourant + '</a>';
		html += '		<a name="lastmonth">' + riadatepickerMoisDernier + '</a>';
		html += '		<a name="firstjanuary">' + riadatepicker1Janvier + '</a>';
		html += '		<a name="lastyear">' + riadatepickerAnneeDerniere + '</a>';
		html += '		<a class="selector-sep"></a>';
		html += '		<a name="all">' + riadatepickerToutePeriode + '</a>';
		html += '	</div>';
		html += '	<div class="selectordate">';
		html += '		<div class="options"></div>';
		html += '		<input id="btn_submit" type="button" name="valider" value="' + riadatepickerValider + '"/>';
		html += '		<input id="btn_cancel" type="button" name="annuler" value="' + riadatepickerAnnuler + '"/>';
		html += '	</div>';
		$('#riadatepicker').html( html );
		$('#riadatepicker .selectordate').hide();
		
		$('#riadatepicker #btn_cancel').click(function(){	$('#riadatepicker .selectordate').hide();	});
		$('#riadatepicker #btn_submit').click(function(){	refresh_view();	});
		
		$('#riadatepicker [name=all]').click(function(){	
			$('#riadatepicker .options').DatePickerSetDate([date_all1,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker .selectorview').click(function(){
			show_selector();
			$('#riadatepicker .selectordate').hide();
		});
		$('#riadatepicker [name=today]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate(date_today);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=yesterday]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate(date_yesterday, true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=perso]').click(function(){ 
			last_function = $(this).html();
			show_selector();
			$('#riadatepicker .selectordate').show();
		});
		$('#riadatepicker [name=thisweek]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_thisweek,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=lastweek]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_lastMonday,date_lastSunday], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=last2week]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_last2Monday,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=last30days]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_last30days,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=thismonth]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_dmonth, date_emonth], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=lastmonth]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate([date_dlastmonth, date_elastmonth], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=firstjanuary]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate([date_firstjanuary, date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=lastyear]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate([date_lastyear, date_elastyear], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker .view').html(init_view);
		$('#riadatepicker .function_name').html(init_action);
		$('#riadatepicker .options').DatePicker({
			flat: true,
			date: init_date,
			calendars: 1,
			format: 'd/m/Y',
			mode: 'range'
		});
	}
	
	
	// fonction permettant le rechargement du ou des graphiques passer par la variable refresh_graph_in_ajax
	function reload_graph(){
		if( refresh_graph_in_ajax ){
			var reg = new RegExp("[,]+", "g");
			var date = getRiaDate();
			date = date.toString().split(reg);
			
			var wst = $('#wst').val();
			if( wst =='all' || !wst) wst = 0;
			wst = wst.toString().replace('w-','');
			
			var date1 = dateparse(date[0]);
			var date2 = dateparse(date[1]);
			
			var arg;
			if( date1 == date2 )
				arg = 'day='+date1+'&wst='+wst;
			else{
				arg = 'date1='+date1+'&date2='+date2+'&wst='+wst;
			}
			
			// Fabien Adds
			var parameters_str = '';
			if( parameters.length > 0 ){
				parameters_str = '&'+parameters.join('&');
			}
			// Fabien Ends
	
			for(var i = 0; i < refresh_graph_in_ajax.length; i++){
				$('#'+refresh_graph_in_ajax[i]).html('<img src="'+refresh_graph_in_ajax[i]+'.php?'+arg+parameters_str+'" width="600" height="400" class="center" alt="Statistiques de consultation" />');
			}
		}
	}
	// Cette fonction permet de recuperer les variables passé dans l'url
	function extractUrlParams(){	
		var t = location.search.substring(1).split('&');
		var f = [];
		var tableau = /^(.*)\[(.*)\]$/;
		for( var i=0; i<t.length; i++ ){
			var x = t[ i ].split('=');
			if( tableau.test(x[0]) ){
				var cle = x[0].replace(tableau,'$1');
				var scle = x[0].replace(tableau,'$2');
				if( !f[cle] ){
					f[cle] = [];
				}
				if( scle.length ){
					f[cle].push([scle, x[1]]);
					//f[cle][scle]= x[1];
				} else {
					f[cle].push(x[1])
				}
			} else {
				f[x[0]]=x[1];
			}
		}
		return f;
	}
	
	/** Charge un composant ActiveX pour l'analyse de fichiers XML
	 * (spécifique Internet Explorer)
	 * @param xml document XML à analyser
	 * @return le document parsable
	*/
	function parseXml(xml) {  
		if (jQuery.browser.msie) {  
			var xmlDoc = new ActiveXObject("Microsoft.XMLDOM");  
			xmlDoc.loadXML(xml);  
			xml = xmlDoc;  
		}  
		return xml;  
    }  
	
	// fonction permettant le rechargement de la liste des recherches en ajax
	function reload_lst_search(page){
		var reg = new RegExp("[,]+", "g");
		var date = getRiaDate().toString().split(reg);
		var date1 = dateparse(date[0]);
		var date2 = dateparse(date[1]);
		var wst = $('#wst').val();
		var lng = $('#lng').val();

		if( wst=='all' || !wst ) wst = 0;
		wst = wst.toString().replace('w-','');
		
		if(autorefresh) {
				var get = extractUrlParams();
				var wosubstitut = '';
				if($('input#wosubstitut').is(':checked'))
					wosubstitut = '&wosubstitut=1';				
				
				var fct = last_function == '' ? (get['last'] ? get['last'] : riadatepickerPeriodePerso ) : last_function;
				
				// Modifie les paramètres de la page (provoque le rechargement)
				window.location.search = 'wst=' + wst + '&date1=' + date1 + '&date2=' + date2 + wosubstitut + '&last=' + fct + '&lng=' + lng;
		}
		if( !autorefresh && refresh_in_ajax ){
							
			$('#lst_search').fadeTo('fast',0.1, function(){
				$('#lst_search').html('<tr><td colspan="9" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="loader"/></td></tr>');
				$('#infobulle').fadeOut('normal', function(){
					$('#infobulle').remove();
				});
				
				$.ajax({
					type: "GET",
					url: 'js_lst_search.php',
					data: 'wst='+wst+'&date1='+date1+'&date2='+date2+'&page='+page+( $('#filter').val() != '' ? '&filter='+$('#filter').val() : '') + '&lng=' + lng,
					dataType: ($.browser.msie) ? "text" : "xml",
					async: $('#filter').val()!= '',
					success: function(msg){
						if( websites.length>0 ){
							if( wst>0 ){
								$('#website').remove();
							}else if( !$('#website').length ){
								$('#table_search_col').append('<th id="website" title="' + riadatepickerSiteRecherche + '">' + riadatepickersiteWeb + '</th>');
							}
						}
						$('#table_search col').remove();
						if( wst<=0 ){
							$('#table_search').prepend('<col width="130">');
						}
						$('#table_search').prepend('<col width="*" /><col width="*" /><col width="*" /><col width="100" /><col width="100" /><col width="100" /><col width="100" /><col width="*" />');
						var msg = parseXml(msg); 
						$('#lst_search').html('');
						$(msg).find('search').each(function(){
							$('#lst_search').append('<tr class="stat-search-info"><td class="stat-search-info"><img class="img-stat-search" src="/admin/images/stats/redirection.svg" alt="Créer une redirection" title="' + riadatepickerCreationRedirection + '" width="16" height="16" onclick="return addRedirectionSearch('+$(this).attr('seg')+', \''+addslashes($(this).text())+'\', \''+$(this).attr('lng')+'\', '+$(this).attr('wst')+');" /><a onclick="return show_search(\''+addslashes($(this).text())+'\''+($(this).attr('seg') ? ','+$(this).attr('seg') : '')+','+$(this).attr('scc')+''+($(this).attr('id_section')>0 ? ','+$(this).attr('id_section') : '')+')" href="/admin/search/index.php?q='+addslashes($(this).text())+'">'+($(this).text())+'</a></td><td class="stat-search-info">'+$(this).attr('section')+'</td><td class="stat-search-info">'+$(this).attr('types')+'</td><td align="right" class="stat-search-info">'+$(this).attr('volume')+'</td><td align="right" class="stat-search-info">'+$(this).attr('nbres')+'</td><td align="right" class="stat-search-info">'+$(this).attr('avg')+'</td><td class="stat-search-info" style="text-align:right;">'+$(this).attr('nbClick')+'</td><td class="stat-search-info">'+$(this).attr('search')+'</td>'+(wst<=0 && websites.length>0 ? '<td class="stat-search-info">'+websites[$(this).attr('site')]+'</td>' : '')+'</tr>');
						});
						$(msg).find('nosearch').each(function(){
							var nbcols = $('#table_search_col > *').length;
							$('#lst_search').append('<tr><td colspan=' + nbcols + '>' + riadatepickerAucuneRecherche + '</td></tr>');
							
						});
						$('.export-btn').remove();
						$('.td-actions').append('<button class="btn-move export-btn" onclick="window.location.href=\'export-search-sylk.php?wst='+wst+'&date1='+date1+'&date2='+date2+'\'">' + riadatepickerExporter + '</button>');
						
						if( !$(msg).find('number').length ){
							$('#pagination').html('');
						} else {
							$(msg).find('number').each(function(){
								$('#pagination').html('');
								if(parseInt($(this).attr('nbpage')) > 1){
									if( page > 1){//page précédente
										$('#pagination').append('<a href="#" onclick="reload_lst_search('+(page-1)+');">&laquo; ' + riadatepickerPrecedent + ' | </a> ');
									}
									for(var i = 1; i <= parseInt($(this).attr('nbpage')) ; i++){
										if(i > page-5 && i < page+5 ){
											if($(this).attr('actual')==i)
												$('#pagination').append('<b>'+i+'</b> | ');
											else
												$('#pagination').append('<a href="#" onclick="reload_lst_search('+i+');">'+i+'</a> | ');
										}
									}
									if( page < parseInt($(this).attr('nbpage')) ){//page suivante
										$('#pagination').append('<a href="#" onclick="reload_lst_search('+(page+1)+');">' + riadatepickerSuivant + ' &raquo;</a> ');
									}
								}
							});
						}
						
						$('#table-synthese-search tbody').html($(msg).find('synthese').text());
						
						
						check_search(true);
					},
					error: function(){
						alert( riadatepickerErreurEnregistrementDemande );
					}
				});
			});
			$('#lst_search').fadeTo('fast',1, function(){
				$('div.loader').remove();
			});
		}
	}
	
	// Permet de récupérer la date que l'utilisateur à demandé via le date picker
	function getRiaDate(){
		return $('#riadatepicker .options').DatePickerGetDate(true);
	}
	
	// Affichage du sélecteur de date 
	function show_selector(){
		if($('#riadatepicker .selector').css('display')=='none'){
			$('.selector').hide();
			$('#riadatepicker .selector').show();
		}else if($('#riadatepicker .selector').css('display')=='block'){
			$('#riadatepicker .selector').hide();
		}
	}
	
	// Fonction permettant de recalculer le nombre de volume de recherche faite
	function check_search( save ){
		if( refresh_in_ajax ){
			var reg = new RegExp("[,]+", "g");
			var date = getRiaDate();
			date = date.toString().split(reg);
			
			var wst = $('#wst').val();
			if( wst =='all' || !wst) wst = 0;
			wst = wst.toString().replace('w-','');
			
			var date1 = dateparse(date[0]);
			var date2 = dateparse(date[1]);
			
			// Requête ajax permettant de récuperer le nombre de recherches 
			$.ajax({
				type: 'GET',
				url: 'js_volume_search.php',
				data: 'wst='+wst+'&date1='+date1+'&date2='+date2,
				success: function(msg){
					if( $(msg).find('search').attr('volume') ){
						const cpt = $(msg).find('search').attr('volume');
						
						if( save ) nb_show_search = cpt;
											
						// Affichage du nombre de recherche supplémentaire si celui-ci est supérieur à celui présent
						if( cpt > nb_show_search ){
							if($('#infobulle').length>0){
								$('#infobulle').html('<a onclick="reload_lst_search(1)">'+riadatepickerRechercheSupplementaire.replace('#param[nb_recherche]#' ,(cpt-nb_show_search)) + '</a>');
							}else{
								$('.stats-menu .clear:last').before('<div id="infobulle"><a onclick="reload_lst_search(1)">'+riadatepickerRechercheSupplementaire.replace('#param[nb_recherche]#',(cpt-nb_show_search)) + '</a></div>');
							}
							$('#infobulle').fadeIn('normal');
						}
					}
				}
			});
		}
	}

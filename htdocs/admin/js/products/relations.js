/** \file relations.js
 *	Ce fichier ajoute des fonctionnalités intéractives à l'onglet Relations de la fiche Produit
 */

function table_sortable(el, url) {
	riaSortable.create({
		'table'	: el,
		'url'	: '/admin/ajax/' + url 
	});
}

function products_add_event(form, type, action){
	$('.add-rel, .sct-input', form).bind('click', function () {
		if ($('#prd-relations-1-ref').data('open') != undefined && !$(this).data('open')) {
			if( $(this).hasClass('sct-input') ){
				return false;
			}
		}

		$(form).append('<input type="hidden" id="rel-type" value="'+type+'"/>');
		
		displayPopup(relationsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?multiselect=false&orderable=false&show_search=1&cnt_publish=0&action=' + action );
		return false;
	});
}

function products_del_event(form, type, action, source, ordered, sort_url) {
	$('.del-rel', form).on('click', function () {
		var btn = $(this);
		
		var ids = [];
		var is_sync = false;
		$('tbody input[type=checkbox]:checked', form).each(function () {
			ids.push($(this).val());
			if ($(this).parents('tr:eq(0)').find('input#prd-is-sync').val() == 1){
				is_sync = true;
			}
		});
		
		if (is_sync){
			alert("Vous avez sélectionné un ou plusieurs produits dont les relations sont synchronisées.\nSeules les relations non synchronisées peuvent être supprimées.");
			return false;
		}

		if( ids.length == 0 ){
			if( action == 'prd-parents' ){
				alert(relationsAlertEditionParent);
			}else if( action == 'prd-childs' ){
				alert(relationsAlertEditionEnfant);
			}
			return false;
		}
		
		const confirmVal = window.confirm(relationsConfimSuppressionEnfant);
		if( !confirmVal ){
			return false;
		}

		btn.prop('disabled', true);

		var to_send;
		if( action == 'prd-parents' ){
			to_send = {
				'prd': source,
				'parents': ids
			};
		}else if ( action == 'prd-childs' ){
			to_send = {
				'prd': source,
				'childs': ids
			};
		}else{
			to_send = {
				'src' : source,
				'dst' : ids,
				'type' : type
			};
		}
		$.ajax({
			'url' : 'xml.php?action=' + action + '-del',
			'type': 'post',
			'data': to_send, 
			'dataType' : 'xml',
			'success': function (data) {
				switch( data.lastChild.nodeName ){
					case 'error':
						if( data.lastChild.getAttribute('message') ){
							alert( data.lastChild.getAttribute('message') );
						}else{
							alert( relationsAlertErreurSuppressionRelation );
						}
						break;
					case 'success':
						// La suppression dans la base de données à réussi, retire les articles de la liste
						$('tbody input[type=checkbox]:checked', form).parent().parent().remove();
						if( $('tbody tr', form).size() == 0 ){
							$('tbody', form).append('<tr><td colspan="' + ( ordered == 1 ? 7 : 6 ) + '">' +  relationsAucunArticle + ' ' + (action == 'prd-parents' ? relationsParent : (action == 'prd-childs') ? relationsEnfant : '') + '</td></tr>');
						}
						if (ordered != -1){
							table_sortable($('table', form), sort_url);
						}
						break;
					default:
						alert( relationsAlertReponseServeur );
						break;
				}

				$('.del-rel', form).prop('disabled', false);
			}
		});
	});
}

$('form').each(function () {
	var form = $(this);

	if ( typeof($('.list-prd-relations', form)) == 'undefined' || $('.list-prd-relations', form).size() == 0 ){
		return;
	}

	// Récupération des données du formulaire (action, type, source)
	var action = $('input[name="data-rel"]', form).val();
	var type = 0;
	var ordered = parseInt($('input[name="rel-ordered"]', form).val());
	var source = $('input[name=rel-source]', form).val();

	var sort_url;
	if( action.indexOf('prd-parents') == 0 ){
		action = 'prd-parents';
		sort_url = '';
	}else if( action.indexOf('prd-childs') == 0 ){
		action = 'prd-childs';
		sort_url = 'catalog/ajax-product-related-position-update.php?prd='+source;
	}else{
		action = action.split('-');
		type = action[action.length-1];
		action = 'prd-relations';
		sort_url = 'catalog/ajax-product-related-position-update.php?prd='+source+'&rel='+type;
	}

	if (ordered != -1){
		table_sortable($('table', form), sort_url);
	}

	// Au clic sur le bouton Ajouter, on prépare les données à envoyer et on appelle le xml.php avec l'action associée.
	products_add_event(form, type, action);

	// Au clic sur le bouton supprimer, on efface les lignes du tableau cochées de la base de donnée (envoi à xml.php)
	products_del_event(form, type, action, source, ordered, sort_url);
});
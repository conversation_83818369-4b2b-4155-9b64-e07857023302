// Gère la sélection/désélection des images
function previewClick(preview){
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#4574BF' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor!='';
	
	$('.edit-zones')
		.toggle($('.preview input:checked').length == 1)
		.off('click')
		.click(function() {
			var idStart = window.location.search.indexOf('&cat=') + 6;
			var idStop = window.location.search.indexOf('&', idStart);
			var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
			var $preview = $('.preview input:checked').parents('.preview:eq(0)');
			displayPopup(categoriesDisplayPopupZone, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=3&obj_id_0=' + id, null, 756, 602);
		});
	$('.edit-alt')
		.toggle($('.preview input:checked').length == 1);
	$('.delimg')
		.toggle($('.preview input:checked').length >= 1);
}

/** 
 *	Cette fonction permet de supprimer une offre de fidelite
 */
function delRewardCategories(){

	$('#rwc-rewards input.checkbox:checked').each(function(){
		
		var id = $(this).val();

		$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?delcatreward=1&id=' + id , function(){
		});
		$("#rwc-"+id).remove();

	});

	if( $(".rwc-elem").length <= 0 ){

		$('#rwc-rewards tbody').html('<tr><td colspan="9">' + categoriesOffreFidelite + '</td></tr>');
	}
	
	return false;
}
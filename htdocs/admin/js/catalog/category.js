// Permet d'éditer une URL simplifiée
function editUrlCategoryRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	var html = '';
	html += '	<td headers="url">';
	html += '		<input type="text" style="padding:5px;width:98%;" name="url-'+id+'" value="'+url+'" />';
	html += '		<input type="hidden" name="url_old-'+id+'" value="'+url+'" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="save-maj-url" value="' + categoryEnregistrer + '" onclick="saveCategoryUrlRedirection('+id+')" />';
	html += '		<a class="del button" onclick="canceleditUrlCategoryRedirection('+id+', \''+url+'\')">' + categoryAnnuler + '</a>';
	html += '	</td>';
	
	$("#url-"+id).html( html );
}

// Permet d'annulée les modifications faites sur l'URL simplifiée
function canceleditUrlCategoryRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	if( id>0 ){
		var html = '';
		
		html += '	<td headers="url">'+url+'</td>';
		html += '	<td headers="action" class="td-action">';
		
		html += '		<a class="button" onclick="editUrlCategoryRedirection('+id+', \''+url+'\')">Editer</a>';
		html += '		<br /><a class="del button" onclick="delUrlCategoryRedirection('+id+', \''+url+'\')">' + categorySupprimer + '</a>';
		html += '	</td>';
		$("#url-"+id).html( html );
	} else {
		$("#url-0").remove();
		if( $("#tb-redirection tbody tr").length==1 )
			$("#no-url").show();
	}
}

// Permet de supprimer une URL simplifiée avec un code de redirection 301 seulement
function delUrlCategoryRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	if( window.confirm(categoryConfirmSuppressionUrl) ){
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax_category_redirections.php',
			data: 'del=1&url='+encodeURIComponent(url),
			dataType: 'xml',
			success: function(xml) {
				
				// Si la suppression a réussie
				if( $(xml).find('result').attr('type') == '1' ){
					$("#tb-redirection").before("<div class=\"error-success\">" + categorySucceSupressionUrl + "</div>");
					$("#url-"+id).remove();
				} else{
					// Gestion des messages d'erreur
					$("#tb-redirection").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
					return false;
				}
				
			}
		});
		if( $("#tb-redirection tbody tr").length==2 )
			$("#no-url").show();
	}
}

// Permet d'afficher le formulaire d'ajout d'un url simplifiée (elle aura pour code de redirection 301)
var nb = 0;
function addUrlCategoryRedirection(count){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	$("#no-url").hide();
	$("#url-0").remove();
	var html = '';
	
	html += '<tr id="url-0">';
	html += '	<td headers="url">';
	html += '		<input type="text" style="padding:5px;width:98%;" name="url-0" value="" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="add-url" value="' + categoryEnregistrer + '" onclick="saveCategoryUrlRedirection(0, '+(count+nb)+')" />';
	html += '		<a class="del button" onclick="canceleditUrlCategoryRedirection(0)">' + categoryAnnuler + '</a>';
	html += '	</td>';
	html += '</tr>';
	nb++;
	$("#tb-redirection tbody").append(html);
}

function saveCategoryUrlRedirection( id, count ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	var action = id>0 ? 'save-maj-url=1' : 'add-url=1';
	// Requête AJAX d'ajout de redirection
	$.ajax({
		type: "POST",
		url: '/admin/catalog/ajax_category_redirections.php',
		data: action+'&url_id='+id+'&'+$("form#cat-form").serialize(),
		dataType: 'xml',
		success: function(xml) {
			// Si l'ajout a réussie
			if( $(xml).find('result').attr('type') == '1' ){
				if( id>0 ){
					canceleditUrlCategoryRedirection( id, $("input[name=url-"+id+"]").val() );
					$("#tb-redirection").before("<div class=\"error-success\">" + categoryMajUrl + "</div>");
				} else {
					$("#tb-redirection tbody").append('<tr id="url-'+count+'"></tr>');
					canceleditUrlCategoryRedirection( count, $('#url-0 input[type=text]').val() );
					$("#tb-redirection").before("<div class=\"error-success\">" + categoryAjoutUrl + "</div>");
					$("#url-0").remove();
				}
			} else{
				// Gestion des messages d'erreur
				$("#tb-redirection").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
				return false;
			}
			
		}
	});
}

function loadDatePicker(){
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					var old_val = $(temp).val();
					$(temp).val(formated);
					if(old_val!=formated) 
						$(temp).DatePickerHide();
				}
			}
		});
	});
} 

$(document).on('click', '[name="is_soldes"]', function(){
	if ($(this).is(':checked')) {
		$('.tr-publish, .tr-publish-date').hide();
	}else{
		$('.tr-publish, .tr-publish-date').show();
	}
});

$(document).on('change', '.select-obj-pmt', function(){
	var catID = $('[name=obj-pmt-id]').val();
	var codID = $(this).val();

	$.ajax({
		url: '/admin/ajax/catalog/ajax-category.php',
		data: 'add-link-pmt=1&cat=' + catID + '&cod=' + codID,
		type: 'post',
		dataType: 'json',
		success: function(json) {
			$('.cat-cod-link').remove();
			$('.cat-prd-publish').after(json.html);
			$('.cat-nb-prd').html(json.count);
		}
	});

	return false;
});

$(document).on('click', '.del-obj-pmt', function(){
	var catID = $('[name=obj-pmt-id]').val();
	var codID = $(this).val();

	$.ajax({
		url: '/admin/ajax/catalog/ajax-category.php',
		data: 'del-link-pmt=1&cat=' + catID + '&cod=' + codID,
		type: 'post',
		dataType: 'json',
		success: function(json) {
			$('.cat-cod-link').remove();
			$('.cat-prd-publish').after(json.html);
			$('.cat-nb-prd').html(json.count);
		}
	});

	return false;
});
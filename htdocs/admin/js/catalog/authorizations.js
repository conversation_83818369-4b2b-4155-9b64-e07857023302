var current_ajax = false; 
var current_cpt = null;
var current_key = null;

$(document).ready(function(){
	titleReplace();	
	
	// Permet de cacher les champs pour le select usr_fld_id
	$('select[name=usr_fld_id]').change(function(){ 	
		$('.rec-lbl').hide();
		$('.rec-value').hide();
		$('.rec-action').hide();

		var element = '.value-'+$('option[value='+$(this).val()+']').attr('id'); 
		if( $(element).length > 0 ){
			$(element).show();
			$('.rec-lbl').show();
		} 

		var element = '.action-'+$('option[value='+$(this).val()+']').attr('id'); 
		if( $(element).length > 0 ){
			$(element).show();
		} 
	}).change();
	
	$('.value-rec-usr, #choose-rec-usr').click(function(){
		displayPopup( authorizationsSelectCompteClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&callback=authorizations_select_user' );
	});

	// permet de recharger les valeurs possible supérieur / inférieur / .. 
	$('.rec-new-fld').live('change',function(){
		if( current_ajax ) current_ajax.abort();
		var element = $(this);
		var last = element.attr('name').lastIndexOf('-');

		var thisCurrentCpt = parseInt(element.attr('name').substr(last + 1));
		var thisCurrentKey = element.attr('name').replace('rec-new-fld-', '');
		thisCurrentKey = thisCurrentKey.substr(0, thisCurrentKey.lastIndexOf('-'));
		
		// si tout le catalogue alors on retire les lignes et on cache le bouton d'ajout
		if( $(element).val() == -1 ){ 	
			$('[name=add-rec-cnd]').hide(); 
			$(element).parents('table').find('tbody tr[data-id=0-0-0-0]:not(.first)').remove();
		}else{ 
			$('[name=add-rec-cnd]').show(); 
		}			
		
		current_ajax = $.get('/admin/catalog/authorizations/new.php?cpt=' + thisCurrentCpt + '&key=' + thisCurrentKey + '&fld_selected=' + $(element).val(), function (retour) {
			$(element).parents('tr').replaceWith( retour );
			init_autocomplete();			
		});

	}).change();
	
	// permet d'ajouter une ligne dans les conditions
	$('input[name=add-rec-cnd]').live('click',function(){
		if( current_ajax ) current_ajax.abort();
	
		var element = $('.ruline:last').find('.rec-new-fld');
		var last = element.attr('name').lastIndexOf('-');

		if (current_cpt === null) {
			current_cpt = parseInt( element.attr('name').substr(last + 1) ) + 1;
		}else{
			current_cpt = current_cpt + 1;
		}
		
		current_key = element.attr('name').replace('rec-new-fld-', '');
		current_key = current_key.substr(0, current_key.lastIndexOf('-'));
		
		current_ajax = $.get('/admin/catalog/authorizations/new.php?cpt='+current_cpt+'&key='+current_key+'&fld_selected=0', function(retour){
			element.parents('tr').after( retour );
			titleReplace();
			$('tr[data-id='+current_key+'] td').each(function(){
				if( parseInt( $(this).attr('rowspan') ) > 1 ){
					$(this).attr('rowspan',parseInt( $(this).attr('rowspan') )+1);
				}
			});
		});
		return false;
	});
	
	// permet de supprimer une ligne dans les conditions 
	$('.rec-new-delete').live('click',function(){
		current_key = $(this).parents('tr').attr('data-id');
		// on ne peux pas supprimer la derniere ligne 
		if( $('.authorizations tbody tr[data-id='+current_key+'].ruline').length > 1 ){
			// retire le "Et" de la premiere ligne 
			$(this).parents('tr').remove();
			
			// permet de conserver le tableau avec les bons rowspan
			$('tr[data-id='+current_key+'] td[rowspan]').each(function(){
				$(this).attr('rowspan', parseInt($(this).attr('rowspan'))-1);
			});
		}
		return false;
	});
	
	// Permet la modification d'un groupe de conditions
	$('[name=edit-link]').live('click',function(){
		do_line_action('edit', this);
		return false;
	});
	
	// permet l'autorisation de tous le catalogue
	$('[name=allcat]').live('click',function(){
		if( confirm(authorizationsConfirmAutorriseCatalog) ){
			do_action('allcat', $(this).parents('table').find('tr[data-id]:first td'));
		}
		return false;
	});
	
	// permet de supprimer un groupe
	$('[name=del-grp-ru]').live('click',function(){
		if( confirm(authorizationsConfirmSuppressionGroupe) ){
			do_action('delgrp', this);
		}
		return false;
	});	
	
	// permet l'enregistrement d'un groupe 
	$('input[name=save-rec]').live('click',function(){
		do_line_action('upd', this);
		$('[name="add-rec-cnd"]').parents('tr').remove();
		return false;
	});
	
	// permet l'enregistrement d'un nouveau groupe 
	$('input[name=save-new-rec]').live('click',function(){
		messageDuringSave();

		if( current_ajax ) current_ajax.abort();
		
		var form = $(this).parents('form');
		var usr_fld_id = $('select[name=usr_fld_id]').val();
		var usr_value = $('[name=usr_value_'+usr_fld_id+']').length > 0 ? $('[name=usr_value_'+usr_fld_id+']').val() : '';
		var usr_value_txt = $('input[name=usr_value_txt]').length > 0 ? $('input[name=usr_value_txt]').val() : '';
		
		current_ajax = $.ajax({
			url: form.attr('action'),
			data: 'act=add&'+form.serialize(),
			type: 'POST',
			success:function(retour){
				if( retour == '' ){
					window.location.href = 'new.php?add=ok&usr_fld_id='+usr_fld_id+'&usr_value='+usr_value+'&usr_value_txt='+usr_value_txt; // rechargement de la page si tous est bon
				}else{
					// affichage des erreurs d'ajout
					$('.error,.success').remove();
					$('#site-content').prepend( retour );
				}
				titleReplace();
				messageDuringSave();
			}
		});
		
		return false;
	});
	
	// permet d'annuler l'édition d'un groupe de conditions
	$('[name=cancel-rec]').live('click',function(){
		do_line_action('cancel', this);
		$('tr.add-rec-cnd').remove(); // Supprime le bouton Ajouter une condition
		return false;
	});	
	
	// autocomplétion pour la sélection de l'utilisateur
	$('input[name=usr_value_txt]').autocomplete({
		source: "/admin/ajax/search/search-class.php",
		minLength: 2,
		select: function( event, ui ) {	
			$(this).parents('tr').find('.val-autocomplete-usr').val(ui.item.id); 
		}
	});	
	
	//permet d'aller sur l'ajout d'un nouveau groupe à partir d'un contexte
	$('[name=catalog-rights-add]').live('click',function(){
		var usr_value_txt = $('input[name=usr_value_txt]').length > 0 ? $('input[name=usr_value_txt]').val() : '';
			
		var split = $(this).parents('table').find('tr[data-id]').attr('data-id').split('-');
		if( split.length == 4 ){
			var usr_fld_id = split[1];
			var usr_value = split[2];
				
			window.location.href = '/admin/catalog/authorizations/new.php?usr_fld_id='+usr_fld_id+'&usr_value='+usr_value+'&usr_value_txt='+usr_value_txt; 
			return false;
		}
	});
	
	// selector pour le site ce morceau de code serait peut être a mutualiser ..
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
			$('#riadatepicker .selector').hide();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	$('#riawebsitepicker .selector a').click( function(){
		window.location.href = 'index.php?wst='+$(this).attr('name');
	}); 

	if ($('input[name=use_catalog_restrictions_usr_price]:checked').val() == "0"){
		$('.catalog_restrictions_type_block').hide();
	} else {
		$('.catalog_restrictions_type_block').show();
	}
	
}).on('click', '.unclickable', function(){
	return false;
}).on('click', 'input[name=use_catalog_restrictions_usr_price]', function(){
	if ($(this).val() == "0"){
		$('.catalog_restrictions_type_block').hide();
	} else {
		$('.catalog_restrictions_type_block').show();
	}
});

function authorizations_select_user( id, email ){
	$('.value-rec-usr').val( email );
	$('[name=usr_value_1210').val( id );
}

var input_id = false;
function init_autocomplete(){
	// init datepicker
	$('.datepicker').each(function(){
		var temp = this;
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
			}
		});
	});
	// autocomplétion sur le type marques 
	$('.add-rule-prd').click(function(){ 
		return displayPopup( authorizationsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&input_id_prd_id='+$(this).data('input') ); 
	});
	$('.add-rule-cat').click(function(){ 
		input_id=$(this).data('input');
		return displayPopup( authorizationsSelectCategorie, '', '/admin/catalog/popup-categories.php?show_search=1&publish_search=0&input_id_cat_id='+$(this).data('input') ); 
	});
	$('.add-rule-brd').click(function(){ 
		input_id=$(this).data('input');
		return displayPopup( authorizationsSelectMarque, '', '/admin/ajax/catalog/popup-brands-select.php?show_search=1&publish_search=0&input_id_brd_id='+$(this).data('input') ); 
	});
	
	titleReplace();
}
function replace_element(element,retour){
	$('.error,.success').remove(); // supprime les précédents message d'erreur / succès
	
	if( retour.match('error') ){
		$('tr[data-id='+element.attr('data-id')+']:first').before( retour );
	}else{
		$('tr[data-id='+element.attr('data-id')+']').addClass('toremove');
		$(element).after( retour );
		$('tr.toremove').remove();
	}
}

/**
 *	Cette fonction permet de sélectionner le produit (retour de la popup).
 */
function parent_select_prd( id, name, ref, cat, input_id ){
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(ref);
}
function parent_select_brd( id, name ){ 
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(name);
	input_id=false;
}
function updateCat( id, idParent, name ){ 
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(name);
	input_id=false;
}


//effectue un line action 
function do_line_action( action, melement ){
	if (action == 'upd') {
		messageDuringSave();
	}

	if( current_ajax ) current_ajax.abort();
	
	var form = $(melement).parents('form');
	var element = $(melement).parents('tr');
	var split = element.attr('data-id').split('-');
	
	if( split.length == 4 ){
		var wst_id = split[0];
		var usr_fld_id = split[1] === "" ? '' : split[1];
		var usr_value = split[2] === "" ? -1 : split[2];
		var grp_id = split[3];

		current_ajax = $.ajax({
			url: '/admin/catalog/authorizations/new.php',
			data: 'act=' + action + '&wst_id=' + wst_id + '&usr_fld_id=' + usr_fld_id + '&usr_value=' + usr_value + '&grp_id=' + grp_id + '&' + form.serialize(),
			type: 'POST',
			success:function(retour){
				replace_element(element,retour);
				init_autocomplete();
				if (action == 'upd') {
					messageDuringSave();
				}
			}
		});
	}
}

// effectue les actions tel que delgrp et allcat
function do_action(action, melement){
	messageDuringSave();
	if( current_ajax ) current_ajax.abort();
	
	var element = $(melement).parents('tr');
	var split = element.attr('data-id').split('-');
	if( split.length == 4 ){
		var wst_id = split[0];
		var usr_fld_id = split[1] === "" ? -1 : split[1];
		var usr_value = split[2] === "" ? -1 : split[2];
		var grp_id = split[3];
		
		var data = 'wst_id='+wst_id+'&usr_fld_id='+usr_fld_id+'&usr_value='+usr_value+'&grp_id='+grp_id;
		if( window.location.pathname == '/admin/customers/edit.php' ){
			data += '&usr='+$('input[name=usr]').val()+'&tab=rights';
		}
		current_ajax = $.ajax({
			url: '/admin/catalog/authorizations/new.php',
			data: 'act='+action+'&'+data,
			type: 'POST',
			success:function(retour){
				if( retour == '' ){// rechargement de la page si tous est bon
					window.location.href = window.location.pathname+'?'+action+'=ok&'+data; 
				}else{// affichage des erreurs
					$('.error,.success').remove();
					$('#site-content').prepend( retour );
				}		
				titleReplace();
				messageDuringSave();
			}
		});
	}
}
function titleReplace(){

	$('tr[title].dataID').each(function(){
		if( $(this).attr('title') != '' ){
			$(this).attr('data-id',$(this).attr('title'));
			$(this).attr('title','');
		}
	});
}
function messageDuringSave(){
	if (typeof $('.notice') != 'undefined' && $.trim($('.notice').html()) != '' ){
		$('.notice').remove();
		$('.unclickable').removeClass('unclickable');
	}else{
		$('h3').after('<div class="notice">' + authorizationsEnregistrement + '</div>');
		$('[type=submit], a').addClass('unclickable');
	}
}
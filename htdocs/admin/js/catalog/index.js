var currentAjaxRequestCatalog = false;
var first = true;
$(document).ready(
	function() {

		if( parseInt(cat)>0 ){

			// La liste des produits bénéficie d'un chargement Ajax lorsque des filtres/options
			// sont utilisés (ex : Masquer les produits en sommeil)
			// Fonctionnalité disponible uniquement sur les pages catégories et sur tous les navigateurs hors Microsoft Internet Explorer
			if( !$.browser.msie ){
				$(window).bind('popstate', function(event) {
					if( currentAjaxRequestCatalog ){
						currentAjaxRequestCatalog.abort();
					}

					if( event.originalEvent.state.ajax ){
						reloadListProducts( false, event.originalEvent.state.jsonurl );
					}
				});
			}

		}

		// Lazy Loading sur la liste des produits
		$(window).scroll(function() {
			const lazy_load = $("#lazy-load-products");
			if( !lazy_load.length ){
				return false;
			}

			const rowstart = lazy_load.attr('data-rowstart');

			const top_of_screen = $(window).scrollTop();
			const top_of_element = lazy_load.offset().top;

			const bottom_of_element = lazy_load.offset().top + lazy_load.outerHeight();
			const bottom_of_screen = $(window).scrollTop() + $(window).innerHeight();

			// Si l'élément est affiché, charge en Ajax la suite des produits
			if( (bottom_of_screen > top_of_element) && (top_of_screen < bottom_of_element) ){

				var url_params = (brd!==0) ? 'brand=' + brd : 'cat=' + cat;
				url_params += '&rowstart=' + rowstart;
				url_params += '&maxrows=101';
				url_params += '&load-usr-config';

				$.ajax({
					type: 'GET',
					url: '/admin/ajax/catalog/ajax-products.php?' + url_params,
					dataType: 'json',
					success: function( data ){
						lazy_load.replaceWith( data.html );
						loadProductsTableSorter();
						inputsDisabled();
					},
				});

			}
		});


		// Active les fonctionnalités de tri personnalisé
		if( $('#categories').length ){
			riaSortable.create({
				'table'	:	$('#categories'),
				'url'	:	'/admin/ajax/catalog/ajax-position-update.php'
			});
		}

		if( $('#products').length ){
			loadProductsTableSorter();
		}
	}
).delegate(
	'.menu-cols input[type=checkbox]', 'click', function(){
		var checked = $(this).is(':checked');
		var col = $(this).val();

		if( checked ){
			$('#prd-' + col + ', td[headers=prd-' + col + ']').removeClass('th-col-hide').addClass('th-col-show');
			$(this).parent().addClass('checked');
			$.ajax({
				type: "GET",
				url: '/admin/ajax/catalog/ajax-category.php',
				data: 'up-cols=1&col=' + col,
				async: true,
				dataType: 'json',
				success: function(){
					verifWidthTable();
				}
			});
		}else{
			$('#prd-' + col + ', td[headers=prd-' + col + ']').removeClass('th-col-show').addClass('th-col-hide');
			$(this).parent().removeClass('checked');
			$.ajax({
				type: "GET",
				url: '/admin/ajax/catalog/ajax-category.php',
				data: 'up-cols=0&col=' + col,
				async: true,
				dataType: 'json',
				success: function(){
					verifWidthTable();
				}
			});
		}
	}
).delegate( // Bouton Exporter (les produits)
	'#export-products', 'click', function(){
		var url = (brd!==0) ? 'brd=' +brd : 'cat=' + cat;
		displayPopup(catalogDisplayPopupExportProduit, '', '/admin/catalog/popup-export-products.php?' + url);
		return false;
	}
).delegate( // Bouton Exporter (les catégories)
	'#export-categories', 'click', function () {
		var exclude_cat_ids = '';
		var all_checked = true;
		var cpt = 0;
		$("#categories tbody tr .checkbox").each(function(){
			if (!$(this).is(":checked")){
				if(cpt){
					exclude_cat_ids += '|';
				}
				exclude_cat_ids += $(this).val();
				cpt++;
			} else {
				all_checked = false;
			}
		});

		var url = 'cat=' + cat;
		if (!all_checked){
			url += '&exclude_cat_ids=' + exclude_cat_ids;
		}

		displayPopup(catalogDisplayPopupExportCategorie, '', '/admin/catalog/popup-export-categories.php?' + url);
		return false;
	}
).delegate(
	'.filter-all', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parents('.selector').find('input[type=checkbox]').attr('checked', 'checked');
		}else{
			$(this).parents('.selector').find('input[type=checkbox]').removeAttr('checked');
		}
	}
).delegate(
	'#filters-options input[type=checkbox]', 'click', function(){
		$('.selector').hide();
		reloadListProducts( true );
	}
).delegate(
	'.selector input[type=checkbox]', 'click', function(){
		if($(this).attr('class') != 'filter-all'){
			$(this).parents('.selector').find('.filter-all').removeAttr('checked');
		}
		reloadListProducts( true );

	}
).delegate(
	'.riapicker .selectorview', 'click', function(){
		var selector = $(this).parents('.riapicker').find('.selector');

		if( selector.css('display')=='none'){
			$('.selector').hide();
			selector.show();
		}else{
			selector.hide();
		}
	}
).delegate(
	'#display-filters-options', 'click', function(){
		if( $('#filters-options').is(':visible') ){
			$('#filters-options').hide();
		}else{
			$('#filters-options').show();
		}
		return false;
	}
).delegate(
	'#cols-all', 'click', function(){
		if( $(this).is(':checked') ){
			$('.menu-cols .col input:not(.all)').attr('checked', 'checked');
		}else{
			$('.menu-cols .col input:not(.all)').removeAttr('checked');
			$('#cols-ref, #cols-name, #cols-price_ht, #cols-price_ttc, #cols-publish-gescom, #cols-publish, #cols-fields-missing').attr('checked', 'checked');
		}

		showProductsCols();
	}
).delegate(
	'#display-cols-options', 'click', function(){
		if($('.menu-cols').hasClass('none')){
			$('.menu-cols').removeClass('none');
		}else{
			$('.menu-cols').addClass('none');
		}
		return false;
	}
).delegate('.download-file-referencing', 'click', function(){
	$('.popup_ria_back_notice').remove();
	$('.popup_ria_back_load').remove();
	$('.popup_ria_shadow').hide();
	var url = window.location.href;
	window.location.href = url + (url.match(/\?/) ? '&' : '?') + 'downloadexportreferencing=1';
}).delegate( '#js-move-categories', 'click', function(){

	// Modifie simplement le formulaire pour envoyer en POST les données à move.php
	this.form.action = 'move.php';

}).delegate( '#js-classify-categories', 'click', function(){

	// Modifie simplement le formulaire pour envoyer en POST les données à move.php
	this.form.action = 'move.php';

}).delegate( '#js-move-products', 'click', function(){

	// Modifie simplement le formulaire pour envoyer en POST les données à move.php
	this.form.action = 'move.php';

}).delegate( '#js-classify-products', 'click', function(){

	// Modifie simplement le formulaire pour envoyer en POST les données à move.php
	this.form.action = 'move.php';

});

function showProductsCols(){
	$('.menu-cols input').each(function(){
		var col = $(this).val().replace( 'cols-', '' );
		var checked = $(this).is(':checked');
		if( col != 'all' ){
			if( checked ){
				$('#prd-' + col + ', td[headers=prd-' + col + ']').removeClass('th-col-hide').addClass('th-col-show');
			}else{
				$('#prd-' + col + ', td[headers=prd-' + col + ']').removeClass('th-col-show').addClass('th-col-hide');
			}
		}
	});
}
function displaySortDir(){
	var type = $('#prd-sort-type').val();
	if( type==1 || type==11 ){
		$('#sort-prd-dir').hide();
	}else{
		$('#sort-prd-dir').show();
	}
}

function loadProductsTableSorter(){
	if (cat > 0) {
		riaSortable.create({
			'table': $('#products'),
			'url': '/admin/ajax/catalog/ajax-product-position-update.php?cat=' + cat
		});
	}
	if (brd>0) {
		riaSortable.create({
			'table': $('#products'),
			'url': '/admin/ajax/catalog/ajax-product-position-update.php?brd=' + brd
		});
	}

	$('#products.js-classified').tablesorter({
		dateFormat : "mmddyyyy",
		debug: false,
		headers: {
			0:{ sorter: false },
			1:{ sorter: false },
			6:{ sorter: 'riaInteger' },
			7:{ sorter: 'riaInteger' },
			8:{ sorter: 'riaInteger' },
			9:{ sorter: 'riaInteger' },
			12:{ sorter: 'riaInteger' },
			13:{ sorter: 'riaInteger' },
			14:{ sorter: 'riaInteger' },
			15:{ sorter: 'riaInteger' },
			16:{ sorter: 'riaInteger' },
			17:{ sorter: 'riaInteger' },
			25:{ sorter: 'riaInteger' },
			26:{ sorter: 'customDate' },
			27:{ sorter: 'customDate' },
			28:{ sorter: 'customDate' }
		}
	});

	$('#products.js-unclassified').tablesorter({
		dateFormat : "mmddyyyy",
		debug: false,
		headers: {
			0:{ sorter: false },
			1:{ sorter: false },
			8:{ sorter: 'riaInteger' },
			9:{ sorter: 'riaInteger' },
			10:{ sorter: 'riaInteger' },
			12:{ sorter: 'riaInteger' },
			13:{ sorter: 'riaInteger' },
			21:{ sorter: 'riaInteger' },
			22:{ sorter: 'customDate' },
			23:{ sorter: 'customDate' },
			24:{ sorter: 'customDate' }
		}
	});
}

function reloadListProducts( pushHisto, urlReload ){
	var cols = 0;
	$('#products thead th').each(function(){
		var colspan = parseInt($(this).attr('colspan')!=undefined ? $(this).attr('colspan') : 0);
		cols += colspan>1 ? colspan-1 : 1;
	});

	$('#products tbody').html(
		'<tr><td colspan="' + cols + '" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="' + msgLoading + '" width="16" height="16" /> ' + msgLoading + '</td></tr>'
	);

	if( !urlReload ){
		var params = $('.items-list-filters').serialize();

		if( !$('#flt-unpublish').is(':checked') ){
			params += ( $.trim(params)!='' ? '&' : '' ) + 'flt-unpublish=0';
		}
		if( !$('#flt-sleep').is(':checked') ){
			params += ( $.trim(params)!='' ? '&' : '' ) + 'flt-sleep=0';
		}
		if( !$('#flt-catchilds').is(':checked') ){
			params += ( $.trim(params)!='' ? '&' : '' ) + 'flt-catchilds=0';
		}
		if( !$('#flt-childs').is(':checked') ){
			params += ( $.trim(params)!='' ? '&' : '' ) + 'flt-childs=0';
		}

		var url = '/admin/catalog/index.php?cat=' + cat + ( $.trim(params)!='' ? '&' + params : '' );
		var urlAjax = '/admin/ajax/catalog/ajax-products.php?cat=' + cat + ( $.trim(params)!='' ? '&' + params : '' );

	}else{
		var urlAjax = urlReload;
	}

	if( currentAjaxRequestCatalog ){
		currentAjaxRequestCatalog.abort();
	}

	currentAjaxRequestCatalog = $.ajax({
		type: "GET",
		url: urlAjax,
		dataType: 'json',
		success: function( data ){
			$('#products tbody').html( data.html );
			$('#products caption .nb_products').html( data.nbProducts!=data.totalnbProducts ? data.nbProducts + '/' + data.totalnbProducts : data.totalnbProducts );
			loadProductsTableSorter();

			if( !urlAjax.match('flt-fld-or=1') ){ $('#flt-fld-or').removeAttr('checked'); }else{ $('#flt-fld-or').attr('checked', 'checked'); }
			if( !urlAjax.match('flt-val-or=1') ){ $('#flt-val-or').removeAttr('checked'); }else{ $('#flt-val-or').attr('checked', 'checked'); }
			if( urlAjax.match('flt-unpublish=0') || !urlAjax.match('flt-unpublish') ){ $('#flt-unpublish').removeAttr('checked'); }else{ $('#flt-unpublish').attr('checked', 'checked'); }
			if( urlAjax.match('flt-sleep=0') || !urlAjax.match('flt-sleep') ){ $('#flt-sleep').removeAttr('checked'); }else{ $('#flt-sleep').attr('checked', 'checked'); }
			if( urlAjax.match('flt-catchilds=0') || !urlAjax.match('flt-catchilds') ){ $('#flt-catchilds').removeAttr('checked'); }else{ $('#flt-catchilds').attr('checked', 'checked'); }
			if( urlAjax.match('flt-childs=0') || !urlAjax.match('flt-childs') ){ $('#flt-childs').removeAttr('checked'); }else{ $('#flt-childs').attr('checked', 'checked'); }

			// Mise à jour des filtres après un rechargement Ajax
			$('.prd-filters input[type=checkbox]').removeAttr('checked');
			if( data.checkBrand!=0 ){
				for( const b in data.checkBrand ){
					$('#b-' + data.checkBrand[b]).attr('checked', 'checked');
				}
			}
			if( data.checkField!=0 ){
				for( var i=0 ; i<data.checkField.length ; i++ ){
					var check = data.checkField[i];
					for( const f in check ){
						$('.flt-fld-' + f + ' input[value="' + check[f] + '"]').attr('checked', 'checked');
					}
				}
			}
			reloadFilters();
			verifWidthTable();
			compareSizeBlocks();
		},
		complete: function(){
			currentAjaxRequestCatalog = false;
			if( pushHisto ){
				if( first ){
					pushHistoryCatalog( '/admin/catalog/index.php?cat=' + cat, '/admin/ajax/catalog/ajax-products.php?cat=' + cat, true );
				}

				pushHistoryCatalog( url, urlAjax, true );
			}

			first = false;
		}
	});
}

function reloadFilters(){
	$('.prd-filters').each(function(){
		// Gestion des cases à cocher dans les filtres
		var nbCheckBox = $(this).find('input[type=checkbox]').length;
		var nbCheckBoxIsCheck = $(this).find('input[type=checkbox]:checked').length;


		if( nbCheckBoxIsCheck>=(nbCheckBox-1) ){
			$(this).find('.filter-all').attr('checked', 'checked');
		}

		var isBrand = !$(this).attr('class').match('flt-fld-');

		// Gestion du label de sélection par filtre
		var label = isBrand ? catalogLabelMarque : catalogLabelValeur;
		if( !$(this).find('.filter-all').is(':checked') ){
			label = '';
			if( $(this).find('input[type=checkbox]:checked').length ){
				$(this).find('input[type=checkbox]:checked').each(function(){
					label += ( $.trim(label)!='' ? ', ' : '' ) + $(this).next().html();
				});

				label = label.length>33 ? label.substring( 0, 30 ) + ' ...' : label;
			} else {
				label = isBrand ? catalogLabelMarque : catalogLabelValeur;
			}
		}else{
			label = $(this).find('.filter-all').next().html();
		}

		$(this).find('.view').html( label );
	});
}

function pushHistoryCatalog( url, jsonurl, isAjax ){
	if( !$.browser.msie ){
		history.pushState(
			{
				url : url,
				jsonurl: jsonurl,
				ajax: isAjax
			},
			'',
			url
		);
	}
}

// Bouton Exporter le référencement
function exportreferencing(cat) {
	if(cat == 0){
		var url = '/admin/catalog/export-referencing.php';
	}else{
		var url = '/admin/catalog/export-referencing.php?cat='+cat;
	}


	$('body').append('<div class="popup_ria_back_load"></div>');
	$('body').append('<div class="popup_ria_back_notice notice">' + catalogExportEnCours + '</div>');

	$.ajax({
		type 	: 'get',
		url 	: url,
		data 	: '',
		async 	: true,
		success : function(){
			$('.popup_ria_back_notice').html(catalogTelechargementPret +'<a href="#" class="download-file-referencing">' + catalogTelecharger + '</a>');
		}
	});
	return false;
}
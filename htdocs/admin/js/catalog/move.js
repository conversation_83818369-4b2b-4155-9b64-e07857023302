/** Charge la liste des catégories enfants de la catégorie passée en argument. Cette fonction
 *	est utilisée pour la navigation dans la page move.php, tableau choix de la destination.
 *
 * @param {int} cat_id Obligatoire, catégorie dont on souhaite afficher les enfants
 */
function displayChildCategories( cat_id ){

	const tblDest = $('#js-choose-dest');
	const tbody = tblDest.find( 'tbody' );
	const tfoot = tblDest.find( 'tfoot' );

	// Met le tableau en mode "Chargement"
	tbody.html(
		'<tr><td colspan="3" style="padding:5px;">' +
			'<img class="loader" src="/admin/images/stats/loader.gif" alt="' + msgLoading + '" width="16" height="16" /> ' + msgLoading +
		'</td></tr>'
	);
	tfoot.find( '.btn-move' ).remove();

	// Charge en Ajax les sous-catégories et les affiche dans le tableau de choix de la destination
	$.get( '/admin/ajax/catalog/categories/get.php?parent=' + cat_id, {}, function( data ){

		// Supprime le loader
		tbody.empty();

		if( data.length ){

			// Affiche la liste des sous-catégories
			data.forEach( (cat) => {

				// Les catégories ne peuvent pas être déplacées vers elle même
				var strDisabled = '';
				if( disabledCategories.includes( cat.id ) ){
					strDisabled = 'disabled="disabled"';
				}

				tbody.append(

					'<tr><td class="col-check">' +
						'<input type="radio" class="radio" name="dest" value="' + cat.id + '" ' + strDisabled + ' />' +
					'</td><td class="col-m">' +
						'<a onclick="displayChildCategories(' + cat.id + ')" title="' + strShowCategory + '">' +
						viewCatIsSync( cat.is_sync ) + ' ' + cat.title +
						'</a>' +
					'</td><td class="align-right" data-label="' + strProductsPublished + '">' +
						cat.products +
					'</td></tr>'

				);

			});

		}else{

			// Affiche un message d'information si aucune sous-catégorie
			tbody.html( '<tr><td colspan="3">' + strNoSubCategories + '</td></tr>' );

		}

		// Boutons Placer ici
		if( cat_id===null ){

			// Propose de placer les catégories ou produits à la racine du catalogue
			tbody.append(
				'<tr><td>' +
					'<input type="radio" class="radio" name="dest" value="null" id="dest-here" />' +
				'</td><td colspan="2">' +
					'<label for="dest-here">' + strMoveRoot + '</label>' +
				'</td></tr>'
			);

		}else if( disabledCategories.includes( cat_id ) ){

			tbody.append(
				'<tr><td>' +
				'</td><td colspan="2">' +
					'<label for="dest-here">' + strAlreadyHere + '</label>' +
				'</td></tr>'
			);

		}else{

			// Propose de placer dans la catégorie en cours de consultation
			tbody.append(
				'<tr><td>' +
					'<input type="radio" class="radio" name="dest" value="' + cat_id + '" id="dest-here" />' +
				'</td><td colspan="2">' +
					'<label for="dest-here">' + strMoveHere + '</label>' +
				'</td></tr>'
			);

		}

		// Bouton de navigation vers la catégorie parente
		$.get( '/admin/ajax/catalog/categories/get.php?cat=' + cat_id, {}, function( data ){

			// Le bouton permettant de monter d'un niveau ne peut pas s'afficher si on est à la racine
			if( cat_id===null ){
				return;
			}

			// Fourni un bouton permettant de remonter d'un niveau
			tfoot.find('td').prepend(
				'<input type="button" class="btn-move float-left" name="back" value="' + strGoBack + '" onclick="javascript:displayChildCategories( ' + data.parent_id + ' )" />'
			);

		});

	} );

	return false;
}
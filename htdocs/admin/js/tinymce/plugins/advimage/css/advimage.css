.clear {}
body { margin: 0 }
h2 { border-bottom: 2px solid #474747; color: #000000; font-size: 13px; margin: 0; padding: 5px 0 0; }
#src_list, #over_list, #out_list {width:280px;}
.mceActionPanel {margin-top:7px;}
.alignPreview {border:1px solid #000; width:140px; height:140px; overflow:hidden; padding:5px;}
.checkbox {border:0;}
.panel_wrapper div.current {height:305px;}
#prev {margin:0; border:1px solid #000; height: 190px; overflow:auto;}
#align, #classlist {width:150px;}
#width, #height {vertical-align:middle; width:50px; text-align:center;}
#persowidth, #persoheight, #vspace, #hspace, #border {vertical-align:middle; width:30px; text-align:center;}
#class_list {width:180px;}
input {width: 280px;}
#constrain, #onmousemovecheck {width:auto;}
#id, #dir, #lang, #usemap, #longdesc {width:200px;}
#media-images { margin-left: 151px; padding-top: 45px; }
#media-img { width: 20px; }
#select-img .img { text-decoration: none; }
#select-img .img img { box-shadow: 0 0 1px #C4C4C4; height: 80px; margin: 5px; width: 80px; border: none; }
#select-img .img img:hover { box-shadow: 0 0 5px #000; cursor: pointer; }
#search-img { background-color: #FFFFFF; padding-top: 8px; border-bottom: 1px solid #A1A1A1;  margin-left: 155px; padding-bottom: 8px; position: fixed; width: 100%; display: none; }

#search-img input { width: 124px }
#navigation { margin-left: 196px; display: none; }
.mceActionPanel .back, .mceActionPanel .next { display: block; float: left; }
.mceActionPanel .back { background-image: url("../img/back.png"); background-repeat: no-repeat; height: 32px; width: 32px;}
.mceActionPanel .next { background-image: url("../img/next.png"); background-repeat: no-repeat; height: 32px; width: 32px; }
#cancel-new { background-position: 0 -77px !important; }
#add-img { float: left; margin-left: 75px; margin-right: 5px; margin-top: 3px; width: auto; }
#add-image { padding: 8px; }
#add-image .actions { margin-top: 244px; text-align: right; }
#source { padding-top: 8px; position: fixed; float: left; width: 148px; height: 100%; border-right: 1px solid #808080; }
#source ul { margin: 0 0 0 15px; padding: 0; }
#source li { list-style: none inside none; }
#source .dir { background-image: url("../img/dir-close.png"); background-repeat: no-repeat; display: block; margin-bottom: 5px; padding-left: 20px; }
#source .selected { background-image: url("../img/dir-open.png"); color: #2B6FB6; }
#source .dir:hover { background-image: url("../img/dir-open.png"); color: #2B6FB6; }
#source .racine { display: block; font-size: 12px; font-weight: bold; text-decoration: none; }
.load-img { border: 1px solid #949392; color: #949392; padding: 5px; text-align: center; }
#edit-img { margin: 8px; }
#back-top-search{ /* width: 90%; */ position: absolute; opacity: 1; height: 13px; background-image: -moz-radial-gradient(center top , ellipse farthest-side, rgba(100, 100, 100, 0.7), transparent); border-top: 1px solid #A1A1A1; margin-top: 8px; display: none; }
#back-top-search2{ background-image: -moz-radial-gradient(center bottom , ellipse farthest-side, rgba(100, 100, 100, 0.9), transparent); display: none; height: 13px; opacity: 1; position: absolute; }
@media screen and (-webkit-min-device-pixel-ratio:0) {
    #back-top-search{ /* width: 90%; */ position: absolute; opacity: 1; height: 13px; background-image: -webkit-radial-gradient(center top , ellipse farthest-side, rgba(100, 100, 100, 0.7), transparent); border-top: 1px solid #A1A1A1; margin-top: 8px; display: none; }
    #back-top-search2{ background-image: -webkit-radial-gradient(center bottom , ellipse farthest-side, rgba(100, 100, 100, 0.9), transparent); display: none; height: 13px; opacity: 1; position: absolute; }
}
#persosize{ display: none; }
#btn-create-perso{ display: none; color: #2b6fb6; }
.error { background-color: #ffdddd; border: 1px solid red; margin: 0 0 5px; padding: 10px; }
<?php
	// Ajout d'une image
	if( isset($_FILES['add-img']) && sizeof($_FILES)>0 ){
		if( ($id = img_images_upload( 'add-img' )) ){
			header('Location: image.php?img='.$id);
			exit;
		}
	}
	
	$_GET['img'] = isset($_GET['img']) && is_numeric($_GET['img']) && $_GET['img'] ? $_GET['img'] : '';
	
	// information sur une image
	$image = array( 'id' => '', 'name' => '', 'url' => '' );
	if( $_GET['img']>0 ){
		$rimg = img_images_get( $_GET['img'] );
		if( $rimg && ria_mysql_num_rows($rimg) ){
			$img = ria_mysql_fetch_array( $rimg );
			
			$thumb = $config['img_sizes']['medium'];
			$image['url'] = $config['img_url'].'/'.$thumb['dir'].'/'.$img['id'].'.'.$thumb['format'];
			$image['name'] = $img['src_name'];
			$image['id'] = $img['id'];
			
		} else {
			$_GET['img'] = 0;
		}
	}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#advimage_dlg.dialog_title}</title>
	<script src="../../tiny_mce_popup.js"></script>
	<script src="../../utils/mctabs.js?1"></script>
	<script src="../../utils/form_utils.js"></script>
	<script src="../../utils/validate.js"></script>
	<script src="../../utils/editable_selects.js"></script>
	<script src="/admin/js/jquery.min.js"></script>
	<script src="js/image.js?9"></script>
	<link href="css/advimage.css?3" rel="stylesheet" type="text/css" />
</head>
<body id="advimage" style="display: none" role="application" aria-labelledby="app_title">
	<span id="app_title" style="display:none">{#advimage_dlg.dialog_title}</span>
	<form action="image.php" enctype="multipart/form-data" method="post">
		<div id="source" style="display: <?php print  $_GET['img'] ? 'none' : 'block'; ?>;">
			<ul>
				<li><a href="#" class="racine dir selected all" onclick="return ImageDialog.selectDirectory('all');">Médiathèques</a>
				<ul>
					<li><a href="#" class="dir prd" onclick="return ImageDialog.selectDirectory('prd');">Produits</a></li>
					<li><a href="#" class="dir prd-cat" onclick="return ImageDialog.selectDirectory('prd-cat');">Catégories</a></li>
					<li><a href="#" class="dir str" onclick="return ImageDialog.selectDirectory('str');">Magasins</a></li>
					<li><a href="#" class="dir cms" onclick="return ImageDialog.selectDirectory('cms');">Contenu</a></li>
					<li><a href="#" class="dir news" onclick="return ImageDialog.selectDirectory('news');">Actualité</a></li>
				</ul>
				</li>
			</ul>
		</div>
		<div class="clear"></div>
		<input type="hidden" name="imgid" id="imgid" value="<?php print $_GET['img']; ?>" />
		<div id="search-img">
			<label for="q-img">Rechercher une image : </label>
			<input type="text" value="" id="q-img" name="q-img">
			<input onclick="return ImageDialog.searchImages();" type="submit" id="sh-img" name="search" value="Rechercher" />
			<input onclick="return ImageDialog.newImage();" type="submit" id="new" name="new" value="Ajouter" title="Ajouter une nouvelle image" />
			<div id="back-top-search"></div>
			<div id="back-top-search2"></div>
		</div>
		<div id="media-images" style="display: <?php print  $_GET['img'] ? 'none' : 'block'; ?>;">
			<div id="select-img" style="display:none"></div>
			<div class="mceActionPanel">
				<div id="navigation"></div>
			</div>
		</div>
		<div id="add-image" style="display:none">
			<h2><?php print _('Ajouter une nouvelle image :'); ?></h2>
			<p><?php print _('Vous pouvez utiliser ce formulaire pour ajouter une nouvelle image qui se trouve sur votre espace de travail.'); ?></p>
			<input type="file" name="add-img" id="add-img" value="" />
			<input onclick="return ImageDialog.addNewImage();" class="save" type="submit" name="add" id="add" value="<?php print _('Enregistrer'); ?>" />
			<div class="actions">
				<input type="button" id="cancel-new" name="cancel" value="{#cancel}" onclick="ImageDialog.cancelNewImage();" />
			</div>
		</div>
		
		
		<div id="edit-img" style="display:  <?php print $_GET['img'] ? 'block' : 'none'; ?>;">
			<div class="tabs">
				<ul>
					<li id="general_tab" class="current" aria-controls="general_panel"><span><a onclick="ImageDialog.changeOrCreatePersoImage();" href="javascript:mcTabs.displayTab('general_tab','general_tab');" onmousedown="return false;">{#advimage_dlg.tab_general}</a></span></li>
					<li id="appearance_tab" aria-controls="appearance_panel"><span><a href="javascript:mcTabs.displayTab('appearance_tab','appearance_panel');" onmousedown="return false;">{#advimage_dlg.tab_appearance}</a></span></li>
					<li id="advanced_tab" aria-controls="advanced_panel" style="display: none;"><span><a href="javascript:mcTabs.displayTab('advanced_tab','advanced_panel');" onmousedown="return false;">{#advimage_dlg.tab_advanced}</a></span></li>
				</ul>
			</div>

			<div class="panel_wrapper">
				<div id="general_panel" class="panel current">
					<fieldset>
							<legend>{#advimage_dlg.general}</legend>

							<table role="presentation" class="properties">
								<tr>
									<td class="column1"><label id="srclabel" for="src">{#advimage_dlg.src}</label></td>
									<td colspan="2"><table role="presentation" border="0" cellspacing="0" cellpadding="0">
										<tr> 
											<td><input name="src" type="text" id="src" value="<?php print $image['url']; ?>" class="mceFocus" onchange="ImageDialog.showPreviewImage(this.value);" aria-required="true" /></td> 
											<td id="srcbrowsercontainer">&nbsp;</td>
										</tr>
									</table></td>
								</tr>
								<tr>
									<td><label for="src_list">{#advimage_dlg.image_list}</label></td>
									<td><select id="src_list" name="src_list" onchange="document.getElementById('src').value=this.options[this.selectedIndex].value;document.getElementById('alt').value=this.options[this.selectedIndex].text;document.getElementById('title').value=this.options[this.selectedIndex].text;ImageDialog.showPreviewImage(this.options[this.selectedIndex].value);"><option value=""></option></select></td>
								</tr>
								<tr> 
									<td class="column1"><label id="altlabel" for="alt">{#advimage_dlg.alt}</label></td> 
									<td colspan="2"><input id="alt" name="alt" type="text" value="" /></td> 
								</tr> 
								<tr> 
									<td class="column1"><label id="titlelabel" for="title">{#advimage_dlg.title}</label></td> 
									<td colspan="2"><input id="title" name="title" type="text" value="<?php print $image['name']; ?>" /></td> 
								</tr>
							</table>
					</fieldset>

					<fieldset>
						<legend>{#advimage_dlg.preview}</legend>
						<div id="prev"></div>
					</fieldset>
				</div>

				<div id="appearance_panel" class="panel">
					<fieldset>
						<legend>{#advimage_dlg.tab_appearance}</legend>

						<table role="presentation" border="0" cellpadding="4" cellspacing="0">
							<tr> 
								<td class="column1"><label id="alignlabel" for="align">{#advimage_dlg.align}</label></td> 
								<td><select id="align" name="align" onchange="ImageDialog.updateStyle('align');ImageDialog.changeAppearance();"> 
										<option value="">{#not_set}</option> 
										<option value="baseline">{#advimage_dlg.align_baseline}</option>
										<option value="top">{#advimage_dlg.align_top}</option>
										<option value="middle">{#advimage_dlg.align_middle}</option>
										<option value="bottom">{#advimage_dlg.align_bottom}</option>
										<option value="text-top">{#advimage_dlg.align_texttop}</option>
										<option value="text-bottom">{#advimage_dlg.align_textbottom}</option>
										<option value="left">{#advimage_dlg.align_left}</option>
										<option value="right">{#advimage_dlg.align_right}</option>
									</select> 
								</td>
								<td rowspan="6" valign="top">
									<div class="alignPreview">
										<img id="alignSampleImg" src="img/sample.gif" alt="{#advimage_dlg.example_img}" />
										Lorem ipsum, Dolor sit amet, consectetuer adipiscing loreum ipsum edipiscing elit, sed diam
										nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.Loreum ipsum
										edipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam
										erat volutpat.
									</div>
								</td>
							</tr>

							<tr role="group" aria-labelledby="widthlabel">
								<td class="column1"><label id="widthlabel" for="width">{#advimage_dlg.dimensions}</label></td>
								<td>
									<input type="hidden" name="width" id="width" value="150" onchange="ImageDialog.changeHeight();" />
									<input type="hidden" name="height" id="height" value="150" onchange="ImageDialog.changeHeight();" />
									<?php
											foreach( $config['img_sizes'] as $code=>$size ){
												$config['img_sizes'][$code]['score'] = (int) $size['width'] * (int) $size['height'];
											}
											
											$sizes = array_msort( $config['img_sizes'], array('score'=>SORT_ASC, 'width'=>SORT_ASC) );
									?>
									<select onchange="return ImageDialog.changeDimensions();" id="dimensions" name="dimensions"> 
										<option value="">{#not_set}</option>
										<?php
											$tmp_ar = array();
											foreach( $sizes as $code=>$size ){
												if( in_array($size['dir'], $tmp_ar) ){
													continue;
												}
												
												$label = $size['width'].'x'.$size['height'].' pixels';

												if (trim($size['name']) != '') {
													$label = $size['name'].' ('.$size['width'].'x'.$size['height'].' pixels)';
												}elseif (!$size['old_system']) {
													$label = $code.' ('.$size['width'].'x'.$size['height'].' pixels)';
												}

												$selected = $_GET['img'] && $code=='medium' ? ' selected="selected"' : '';
												
												print '
													<option id="'.$size['dir'].'" '.$selected.' value="'.$size['dir'].'|'.$size['format'].'">
														'.$label.'
													</option>
												';

												$tmp_ar[] = $size['dir'];
											}
										?>
										<option id="customized" value="customized">Personnalisées</option>
									</select> 
								</td>
							</tr>

							<tr>
								<td>&nbsp;</td>
								<td>
									<div id="persosize">
										<input type="text" class="number" value="" id="persowidth" name="persowidth" />
										&nbsp;x&nbsp;
										<input type="text" name="persoheight" id="persoheight" value="" class="number" />
										&nbsp;pixels
									</div>
								</td>
							</tr>

							<tr>
								<td class="column1"><label id="vspacelabel" for="vspace">{#advimage_dlg.vspace}</label></td> 
								<td><input name="vspace" type="text" id="vspace" value="" size="3" maxlength="3" class="number" onchange="ImageDialog.updateStyle('vspace');ImageDialog.changeAppearance();" onblur="ImageDialog.updateStyle('vspace');ImageDialog.changeAppearance();" />
								</td>
							</tr>

							<tr> 
								<td class="column1"><label id="hspacelabel" for="hspace">{#advimage_dlg.hspace}</label></td> 
								<td><input name="hspace" type="text" id="hspace" value="" size="3" maxlength="3" class="number" onchange="ImageDialog.updateStyle('hspace');ImageDialog.changeAppearance();" onblur="ImageDialog.updateStyle('hspace');ImageDialog.changeAppearance();" /></td> 
							</tr>

							<tr>
								<td class="column1"><label id="borderlabel" for="border">{#advimage_dlg.border}</label></td> 
								<td><input id="border" name="border" type="text" value="" size="3" maxlength="3" class="number" onchange="ImageDialog.updateStyle('border');ImageDialog.changeAppearance();" onblur="ImageDialog.updateStyle('border');ImageDialog.changeAppearance();" /></td> 
							</tr>

							<tr>
								<td><label for="class_list">{#class_name}</label></td>
								<td colspan="2"><select id="class_list" name="class_list" class="mceEditableSelect"><option value=""></option></select></td>
							</tr>

							<tr>
								<td class="column1"><label id="stylelabel" for="style">{#advimage_dlg.style}</label></td> 
								<td colspan="2"><input id="style" name="style" type="text" value="" onchange="ImageDialog.changeAppearance();" /></td> 
							</tr>
						</table>
					</fieldset>
				</div>

				<div id="advanced_panel" class="panel">
					<fieldset>
						<legend>{#advimage_dlg.swap_image}</legend>

						<input type="checkbox" id="onmousemovecheck" name="onmousemovecheck" class="checkbox" onclick="ImageDialog.setSwapImage(this.checked);" aria-controls="onmouseoversrc onmouseoutsrc" />
						<label id="onmousemovechecklabel" for="onmousemovecheck">{#advimage_dlg.alt_image}</label>

						<table role="presentation" border="0" cellpadding="4" cellspacing="0" width="100%">
								<tr>
									<td class="column1"><label id="onmouseoversrclabel" for="onmouseoversrc">{#advimage_dlg.mouseover}</label></td> 
									<td><table role="presentation" border="0" cellspacing="0" cellpadding="0"> 
										<tr> 
											<td><input id="onmouseoversrc" name="onmouseoversrc" type="text" value="" /></td> 
											<td id="onmouseoversrccontainer">&nbsp;</td>
										</tr>
									</table></td>
								</tr>
								<tr>
									<td><label for="over_list">{#advimage_dlg.image_list}</label></td>
									<td><select id="over_list" name="over_list" onchange="document.getElementById('onmouseoversrc').value=this.options[this.selectedIndex].value;"><option value=""></option></select></td>
								</tr>
								<tr> 
									<td class="column1"><label id="onmouseoutsrclabel" for="onmouseoutsrc">{#advimage_dlg.mouseout}</label></td> 
									<td class="column2"><table role="presentation" border="0" cellspacing="0" cellpadding="0"> 
										<tr> 
											<td><input id="onmouseoutsrc" name="onmouseoutsrc" type="text" value="" /></td> 
											<td id="onmouseoutsrccontainer">&nbsp;</td>
										</tr> 
									</table></td> 
								</tr>
								<tr>
									<td><label for="out_list">{#advimage_dlg.image_list}</label></td>
									<td><select id="out_list" name="out_list" onchange="document.getElementById('onmouseoutsrc').value=this.options[this.selectedIndex].value;"><option value=""></option></select></td>
								</tr>
						</table>
					</fieldset>

					<fieldset>
						<legend>{#advimage_dlg.misc}</legend>

						<table role="presentation" border="0" cellpadding="4" cellspacing="0">
							<tr>
								<td class="column1"><label id="idlabel" for="id">{#advimage_dlg.id}</label></td> 
								<td><input id="id" name="id" type="text" value="" /></td> 
							</tr>

							<tr>
								<td class="column1"><label id="dirlabel" for="dir">{#advimage_dlg.langdir}</label></td> 
								<td>
									<select id="dir" name="dir" onchange="ImageDialog.changeAppearance();"> 
											<option value="">{#not_set}</option> 
											<option value="ltr">{#advimage_dlg.ltr}</option> 
											<option value="rtl">{#advimage_dlg.rtl}</option> 
									</select>
								</td> 
							</tr>

							<tr>
								<td class="column1"><label id="langlabel" for="lang">{#advimage_dlg.langcode}</label></td> 
								<td>
									<input id="lang" name="lang" type="text" value="" />
								</td> 
							</tr>

							<tr>
								<td class="column1"><label id="usemaplabel" for="usemap">{#advimage_dlg.map}</label></td> 
								<td>
									<input id="usemap" name="usemap" type="text" value="" />
								</td> 
							</tr>

							<tr>
								<td class="column1"><label id="longdesclabel" for="longdesc">{#advimage_dlg.long_desc}</label></td>
								<td><table role="presentation" border="0" cellspacing="0" cellpadding="0">
										<tr>
											<td><input id="longdesc" name="longdesc" type="text" value="" /></td>
											<td id="longdesccontainer">&nbsp;</td>
										</tr>
								</table></td> 
							</tr>
						</table>
					</fieldset>
				</div>
			</div>

			<div class="mceActionPanel">
				<input type="submit" id="insert" name="insert" value="{#insert}" onclick="ImageDialog.insert(); return false;" />
				<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="ImageDialog.cancelSelectImage();" />
			</div>
		</div>
	</form>
	<script><?php
		if( isset($_GET['img']) && is_numeric($_GET['img']) && $_GET['img']>0 ){
			print '
				imageID = '.$_GET['img'].';
				activeScroll = false;
			';
		}
	?></script>
</body> 
</html>
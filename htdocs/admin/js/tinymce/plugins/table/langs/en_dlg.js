tinyMCE.addI18n('en.table_dlg',{"rules_border":"border","rules_box":"box","rules_vsides":"vsides","rules_rhs":"rhs","rules_lhs":"lhs","rules_hsides":"hsides","rules_below":"below","rules_above":"above","rules_void":"void",rules:"Rules","frame_all":"all","frame_cols":"cols","frame_rows":"rows","frame_groups":"groups","frame_none":"none",frame:"Frame",caption:"Table Caption","missing_scope":"Are you sure you want to continue without specifying a scope for this table header cell. Without it, it may be difficult for some users with disabilities to understand the content or data displayed of the table.","cell_limit":"You\'ve exceeded the maximum number of cells of {$cells}.","row_limit":"You\'ve exceeded the maximum number of rows of {$rows}.","col_limit":"You\'ve exceeded the maximum number of columns of {$cols}.",colgroup:"Col Group",rowgroup:"Row Group",scope:"Scope",tfoot:"Footer",tbody:"Body",thead:"Header","row_all":"Update All Rows in Table","row_even":"Update Even Rows in Table","row_odd":"Update Odd Rows in Table","row_row":"Update Current Row","cell_all":"Update All Cells in Table","cell_row":"Update All Cells in Row","cell_cell":"Update Current Cell",th:"Header",td:"Data",summary:"Summary",bgimage:"Background Image",rtl:"Right to Left",ltr:"Left to Right",mime:"Target MIME Type",langcode:"Language Code",langdir:"Language Direction",style:"Style",id:"ID","merge_cells_title":"Merge Table Cells",bgcolor:"Background Color",bordercolor:"Border Color","align_bottom":"Bottom","align_top":"Top",valign:"Vertical Alignment","cell_type":"Cell Type","cell_title":"Table Cell Properties","row_title":"Table Row Properties","align_middle":"Center","align_right":"Right","align_left":"Left","align_default":"Default",align:"Alignment",border:"Border",cellpadding:"Cell Padding",cellspacing:"Cell Spacing",rows:"Rows",cols:"Columns",height:"Height",width:"Width",title:"Insert/Edit Table",rowtype:"Row Type","advanced_props":"Advanced Properties","general_props":"General Properties","advanced_tab":"Advanced","general_tab":"General","cell_col":"Update all cells in column"});
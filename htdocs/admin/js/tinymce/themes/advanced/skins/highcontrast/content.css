body, td, pre { margin:8px;}
body.mceForceColors {background:#FFF; color:#000;}
h1 {font-size: 2em}
h2 {font-size: 1.5em}
h3 {font-size: 1.17em}
h4 {font-size: 1em}
h5 {font-size: .83em}
h6 {font-size: .75em}
.mceItemTable, .mceItemTable td, .mceItemTable th, .mceItemTable caption, .mceItemVisualAid {border: 1px dashed #BBB;}
a.mceItemAnchor {display:inline-block; width:11px !important; height:11px  !important; background:url(../default/img/items.gif) no-repeat 0 0;}
span.mceItemNbsp {background: #DDD}
td.mceSelected, th.mceSelected {background-color:#3399ff !important}
img {border:0;}
table, img, hr, .mceItemAnchor {cursor:default}
table td, table th {cursor:text}
ins {border-bottom:1px solid green; text-decoration: none; color:green}
del {color:red; text-decoration:line-through}
cite {border-bottom:1px dashed blue}
acronym {border-bottom:1px dotted #CCC; cursor:help}
abbr {border-bottom:1px dashed #CCC; cursor:help}

img:-moz-broken {-moz-force-broken-image-icon:1; width:24px; height:24px}
font[face=mceinline] {font-family:inherit !important}
*[contentEditable]:focus {outline:0}

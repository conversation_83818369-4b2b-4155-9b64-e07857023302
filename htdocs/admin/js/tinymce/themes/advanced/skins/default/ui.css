.defaultSkin table, .defaultSkin tbody, .defaultSkin a, .defaultSkin img, .defaultSkin tr, .defaultSkin div, .defaultSkin td, .defaultSkin iframe, .defaultSkin span, .defaultSkin *, .defaultSkin .mceText {border:0; margin:0; padding:0; background:transparent; white-space:nowrap; text-decoration:none; font-weight:normal; cursor:default; color:#000; vertical-align:baseline; width:auto; border-collapse:separate; text-align:left}
.defaultSkin a:hover, .defaultSkin a:link, .defaultSkin a:visited, .defaultSkin a:active {text-decoration:none; font-weight:normal; cursor:default; color:#000}
.defaultSkin table td {vertical-align:middle}
.defaultSkin table {direction:ltr;background:transparent}
.defaultSkin iframe {display:block;}
.defaultSkin .mceToolbar {}
.defaultSkin .mceLeft {text-align:left}
.defaultSkin .mceRight {text-align:right}
.defaultSkin .mceExternalToolbar {position:absolute; border:1px solid #CCC; border-bottom:0; display:none;}
.defaultSkin .mceExternalToolbar td.mceToolbar {padding-right:13px;}
.defaultSkin .mceExternalClose {position:absolute; top:3px; right:3px; width:7px; height:7px; background:url(../../img/icons.gif) -820px 0}
.defaultSkin table.mceLayout { border: medium none !important; }
.defaultSkin table.mceLayout tr.mceFirst td {border-top:1px solid #CCC}
.defaultSkin table.mceLayout tr.mceLast td { display: none !important }
.defaultSkin table.mceToolbar, .defaultSkin tr.mceFirst .mceToolbar tr td, .defaultSkin tr.mceLast .mceToolbar tr td {border:0 none !important; margin:0; padding:0;height: 25px;}
.defaultSkin td.mceToolbar { background-color: #FFFFFF; }
.defaultSkin .mceIframeContainer { background-color: #FFFFFF; border: 1px solid #949494; border-radius: 0 0 5px 5px; box-shadow: 0 2px 5px #949494; padding: 5px !important; width: 100%; }
.defaultSkin .mceStatusbar { background: none repeat scroll 0 0 transparent; border: 1px solid #949494 !important; border-radius: 0 0 5px 5px; color: #000000; display: block; font-family: 'MS Sans Serif',sans-serif,Verdana,Arial; font-size: 9pt; height: 20px; line-height: 16px; overflow: visible; padding: 3px !important; }
.defaultSkin .mceStatusbar div {float:left; margin:2px}
.defaultSkin .mceStatusbar a.mceResize {display:block; float:right; background:url(../../img/icons.gif) -800px 0; width:20px; height:20px; cursor:se-resize; outline:0}
.defaultSkin .mceStatusbar a:hover {text-decoration:underline}
.defaultSkin table.mceToolbar {margin-left:3px}
.defaultSkin span.mceIcon, .defaultSkin img.mceIcon {display:block; width:20px; height:20px}
.defaultSkin .mceIcon {background:url(../../img/icons.gif) no-repeat 20px 20px}
.defaultSkin td.mceCenter {text-align:center;}
.defaultSkin td.mceCenter table {margin:0 auto; text-align:left;}
.defaultSkin td.mceRight table {margin:0 0 0 auto;}
.defaultSkin .mceButton {display:block; border:1px solid #F0F0EE; width:20px; height:20px; margin-right:1px}
.defaultSkin a.mceButtonEnabled:hover {border:1px solid #0A246A; background-color:#B2BBD0}
.defaultSkin a.mceButtonActive, .defaultSkin a.mceButtonSelected {border:1px solid #0A246A; background-color:#C2CBE0}
.defaultSkin .mceButtonDisabled .mceIcon {opacity:0.3; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=30)}
.defaultSkin .mceButtonLabeled {width:auto}
.defaultSkin .mceButtonLabeled span.mceIcon {float:left}
.defaultSkin span.mceButtonLabel {display:block; font-size:10px; padding:4px 6px 0 22px; font-family:Tahoma,Verdana,Arial,Helvetica}
.defaultSkin .mceButtonDisabled .mceButtonLabel {color:#888}
.defaultSkin .mceSeparator {display:block; background:url(../../img/icons.gif) -180px 0; width:3px; height:20px; margin:2px 2px 0 4px}
.defaultSkin .mceListBox, .defaultSkin .mceListBox a {display:block}
.defaultSkin .mceListBox .mceText {padding-left:4px; width:73px; text-align:left; border:1px solid #CCC; border-right:0; background:#FFF; font-family:Tahoma,Verdana,Arial,Helvetica; font-size:11px; height:20px; line-height:20px; overflow:hidden}
.defaultSkin .mceListBox .mceOpen {width:9px; height:20px; background:url(../../img/icons.gif) -741px 0; margin-right:2px; border:1px solid #CCC;}
.defaultSkin table.mceListBoxEnabled:hover .mceText, .defaultSkin .mceListBoxHover .mceText, .defaultSkin .mceListBoxSelected .mceText {border:1px solid #A2ABC0; border-right:0; background:#FFF}
.defaultSkin table.mceListBoxEnabled:hover .mceOpen, .defaultSkin .mceListBoxHover .mceOpen, .defaultSkin .mceListBoxSelected .mceOpen {background-color:#FFF; border:1px solid #A2ABC0}
.defaultSkin .mceListBoxDisabled a.mceText {color:gray; background-color:transparent;}
.defaultSkin .mceListBoxMenu {overflow:auto; overflow-x:hidden}
.defaultSkin .mceOldBoxModel .mceListBox .mceText {height:22px}
.defaultSkin .mceOldBoxModel .mceListBox .mceOpen {width:11px; height:22px;}
.defaultSkin select.mceNativeListBox {font-family:'MS Sans Serif',sans-serif,Verdana,Arial; font-size:7pt; background:#F0F0EE; border:1px solid gray; margin-right:2px;}
.defaultSkin .mceSplitButton {width:32px; height:20px; direction:ltr}
.defaultSkin .mceSplitButton a, .defaultSkin .mceSplitButton span {height:20px; display:block}
.defaultSkin .mceSplitButton a.mceAction {width:20px; border:1px solid #F0F0EE; border-right:0;}
.defaultSkin .mceSplitButton span.mceAction {width:20px; background-image:url(../../img/icons.gif);}
.defaultSkin .mceSplitButton a.mceOpen {width:9px; background:url(../../img/icons.gif) -741px 0; border:1px solid #F0F0EE;}
.defaultSkin .mceSplitButton span.mceOpen {display:none}
.defaultSkin table.mceSplitButtonEnabled:hover a.mceAction, .defaultSkin .mceSplitButtonHover a.mceAction, .defaultSkin .mceSplitButtonSelected a.mceAction {border:1px solid #0A246A; border-right:0; background-color:#B2BBD0}
.defaultSkin table.mceSplitButtonEnabled:hover a.mceOpen, .defaultSkin .mceSplitButtonHover a.mceOpen, .defaultSkin .mceSplitButtonSelected a.mceOpen {background-color:#B2BBD0; border:1px solid #0A246A;}
.defaultSkin .mceSplitButtonDisabled .mceAction, .defaultSkin .mceSplitButtonDisabled a.mceOpen {opacity:0.3; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=30)}
.defaultSkin .mceSplitButtonActive a.mceAction {border:1px solid #0A246A; background-color:#C2CBE0}
.defaultSkin .mceSplitButtonActive a.mceOpen {border-left:0;}
.defaultSkin div.mceColorSplitMenu table {background:#FFF; border:1px solid gray}
.defaultSkin .mceColorSplitMenu td {padding:2px}
.defaultSkin .mceColorSplitMenu a {display:block; width:9px; height:9px; overflow:hidden; border:1px solid #808080}
.defaultSkin .mceColorSplitMenu td.mceMoreColors {padding:1px 3px 1px 1px}
.defaultSkin .mceColorSplitMenu a.mceMoreColors {width:100%; height:auto; text-align:center; font-family:Tahoma,Verdana,Arial,Helvetica; font-size:11px; line-height:20px; border:1px solid #FFF}
.defaultSkin .mceColorSplitMenu a.mceMoreColors:hover {border:1px solid #0A246A; background-color:#B6BDD2}
.defaultSkin a.mceMoreColors:hover {border:1px solid #0A246A}
.defaultSkin .mceColorPreview {margin-left:2px; width:16px; height:4px; overflow:hidden; background:#9a9b9a}
.defaultSkin .mce_forecolor span.mceAction, .defaultSkin .mce_backcolor span.mceAction {overflow:hidden; height:16px}
.defaultSkin .mceMenu {position:absolute; left:0; top:0; z-index:1000; border:1px solid #D4D0C8; direction:ltr}
.defaultSkin .mceNoIcons span.mceIcon {width:0;}
.defaultSkin .mceNoIcons a .mceText {padding-left:10px}
.defaultSkin .mceMenu table {background:#FFF}
.defaultSkin .mceMenu a, .defaultSkin .mceMenu span, .defaultSkin .mceMenu {display:block}
.defaultSkin .mceMenu td {height:20px}
.defaultSkin .mceMenu a {position:relative;padding:3px 0 4px 0}
.defaultSkin .mceMenu .mceText {position:relative; display:block; font-family:Tahoma,Verdana,Arial,Helvetica; color:#000; cursor:default; margin:0; padding:0 25px 0 25px; display:block}
.defaultSkin .mceMenu span.mceText, .defaultSkin .mceMenu .mcePreview {font-size:11px}
.defaultSkin .mceMenu pre.mceText {font-family:Monospace}
.defaultSkin .mceMenu .mceIcon {position:absolute; top:0; left:0; width:22px;}
.defaultSkin .mceMenu .mceMenuItemEnabled a:hover, .defaultSkin .mceMenu .mceMenuItemActive {background-color:#dbecf3}
.defaultSkin td.mceMenuItemSeparator {background:#DDD; height:1px}
.defaultSkin .mceMenuItemTitle a {border:0; background:#EEE; border-bottom:1px solid #DDD}
.defaultSkin .mceMenuItemTitle span.mceText {color:#000; font-weight:bold; padding-left:4px}
.defaultSkin .mceMenuItemDisabled .mceText {color:#888}
.defaultSkin .mceMenuItemSelected .mceIcon {background:url(img/menu_check.gif)}
.defaultSkin .mceNoIcons .mceMenuItemSelected a {background:url(img/menu_arrow.gif) no-repeat -6px center}
.defaultSkin .mceMenu span.mceMenuLine {display:none}
.defaultSkin .mceMenuItemSub a {background:url(img/menu_arrow.gif) no-repeat top right;}
.defaultSkin .mceMenuItem td, .defaultSkin .mceMenuItem th {line-height: normal}
.defaultSkin .mceBlocker {position:absolute; left:0; top:0; z-index:1000; opacity:0.5; -ms-filter:'alpha(opacity=50)'; filter:alpha(opacity=50); background:#FFF}
.defaultSkin .mceProgress {position:absolute; left:0; top:0; z-index:1001; background:url(img/progress.gif) no-repeat; width:32px; height:32px; margin:-16px 0 0 -16px}
.mceRtl .mceListBox .mceText {text-align: right; padding: 0 4px 0 0}
.mceRtl .mceMenuItem .mceText {text-align: right}
.defaultSkin .mce_formatPreview a {font-size:10px}
.defaultSkin .mce_p span.mceText {}
.defaultSkin .mce_address span.mceText {font-style:italic}
.defaultSkin .mce_pre span.mceText {font-family:monospace}
.defaultSkin .mce_h1 span.mceText {font-weight:bolder; font-size: 2em}
.defaultSkin .mce_h2 span.mceText {font-weight:bolder; font-size: 1.5em}
.defaultSkin .mce_h3 span.mceText {font-weight:bolder; font-size: 1.17em}
.defaultSkin .mce_h4 span.mceText {font-weight:bolder; font-size: 1em}
.defaultSkin .mce_h5 span.mceText {font-weight:bolder; font-size: .83em}
.defaultSkin .mce_h6 span.mceText {font-weight:bolder; font-size: .75em}
.defaultSkin span.mce_bold {background-position:0 0}
.defaultSkin span.mce_italic {background-position:-60px 0}
.defaultSkin span.mce_underline {background-position:-140px 0}
.defaultSkin span.mce_strikethrough {background-position:-120px 0}
.defaultSkin span.mce_undo {background-position:-160px 0}
.defaultSkin span.mce_redo {background-position:-100px 0}
.defaultSkin span.mce_cleanup {background-position:-40px 0}
.defaultSkin span.mce_bullist {background-position:-20px 0}
.defaultSkin span.mce_numlist {background-position:-80px 0}
.defaultSkin span.mce_justifyleft {background-position:-460px 0}
.defaultSkin span.mce_justifyright {background-position:-480px 0}
.defaultSkin span.mce_justifycenter {background-position:-420px 0}
.defaultSkin span.mce_justifyfull {background-position:-440px 0}
.defaultSkin span.mce_anchor {background-position:-200px 0}
.defaultSkin span.mce_indent {background-position:-400px 0}
.defaultSkin span.mce_outdent {background-position:-540px 0}
.defaultSkin span.mce_link {background-position:-500px 0}
.defaultSkin span.mce_unlink {background-position:-640px 0}
.defaultSkin span.mce_sub {background-position:-600px 0}
.defaultSkin span.mce_sup {background-position:-620px 0}
.defaultSkin span.mce_removeformat {background-position:-580px 0}
.defaultSkin span.mce_newdocument {background-position:-520px 0}
.defaultSkin span.mce_image {background-position:-380px 0}
.defaultSkin span.mce_help {background-position:-340px 0}
.defaultSkin span.mce_code {background-position:-260px 0}
.defaultSkin span.mce_hr {background-position:-360px 0}
.defaultSkin span.mce_visualaid {background-position:-660px 0}
.defaultSkin span.mce_charmap {background-position:-240px 0}
.defaultSkin span.mce_paste {background-position:-560px 0}
.defaultSkin span.mce_copy {background-position:-700px 0}
.defaultSkin span.mce_cut {background-position:-680px 0}
.defaultSkin span.mce_blockquote {background-position:-220px 0}
.defaultSkin .mce_forecolor span.mceAction {background-position:-720px 0}
.defaultSkin .mce_backcolor span.mceAction {background-position:-760px 0}
.defaultSkin span.mce_forecolorpicker {background-position:-720px 0}
.defaultSkin span.mce_backcolorpicker {background-position:-760px 0}
.defaultSkin span.mce_advhr {background-position:-0px -20px}
.defaultSkin span.mce_ltr {background-position:-20px -20px}
.defaultSkin span.mce_rtl {background-position:-40px -20px}
.defaultSkin span.mce_emotions {background-position:-60px -20px}
.defaultSkin span.mce_fullpage {background-position:-80px -20px}
.defaultSkin span.mce_fullscreen {background-position:-100px -20px}
.defaultSkin span.mce_iespell {background-position:-120px -20px}
.defaultSkin span.mce_insertdate {background-position:-140px -20px}
.defaultSkin span.mce_inserttime {background-position:-160px -20px}
.defaultSkin span.mce_absolute {background-position:-180px -20px}
.defaultSkin span.mce_backward {background-position:-200px -20px}
.defaultSkin span.mce_forward {background-position:-220px -20px}
.defaultSkin span.mce_insert_layer {background-position:-240px -20px}
.defaultSkin span.mce_insertlayer {background-position:-260px -20px}
.defaultSkin span.mce_movebackward {background-position:-280px -20px}
.defaultSkin span.mce_moveforward {background-position:-300px -20px}
.defaultSkin span.mce_media {background-position:-320px -20px}
.defaultSkin span.mce_nonbreaking {background-position:-340px -20px}
.defaultSkin span.mce_pastetext {background-position:-360px -20px}
.defaultSkin span.mce_pasteword {background-position:-380px -20px}
.defaultSkin span.mce_selectall {background-position:-400px -20px}
.defaultSkin span.mce_preview {background-position:-420px -20px}
.defaultSkin span.mce_print {background-position:-440px -20px}
.defaultSkin span.mce_cancel {background-position:-460px -20px}
.defaultSkin span.mce_save {background-position:-480px -20px}
.defaultSkin span.mce_replace {background-position:-500px -20px}
.defaultSkin span.mce_search {background-position:-520px -20px}
.defaultSkin span.mce_styleprops {background-position:-560px -20px}
.defaultSkin span.mce_table {background-position:-580px -20px}
.defaultSkin span.mce_cell_props {background-position:-600px -20px}
.defaultSkin span.mce_delete_table {background-position:-620px -20px}
.defaultSkin span.mce_delete_col {background-position:-640px -20px}
.defaultSkin span.mce_delete_row {background-position:-660px -20px}
.defaultSkin span.mce_col_after {background-position:-680px -20px}
.defaultSkin span.mce_col_before {background-position:-700px -20px}
.defaultSkin span.mce_row_after {background-position:-720px -20px}
.defaultSkin span.mce_row_before {background-position:-740px -20px}
.defaultSkin span.mce_merge_cells {background-position:-760px -20px}
.defaultSkin span.mce_table_props {background-position:-980px -20px}
.defaultSkin span.mce_row_props {background-position:-780px -20px}
.defaultSkin span.mce_split_cells {background-position:-800px -20px}
.defaultSkin span.mce_template {background-position:-820px -20px}
.defaultSkin span.mce_visualchars {background-position:-840px -20px}
.defaultSkin span.mce_abbr {background-position:-860px -20px}
.defaultSkin span.mce_acronym {background-position:-880px -20px}
.defaultSkin span.mce_attribs {background-position:-900px -20px}
.defaultSkin span.mce_cite {background-position:-920px -20px}
.defaultSkin span.mce_del {background-position:-940px -20px}
.defaultSkin span.mce_ins {background-position:-960px -20px}
.defaultSkin span.mce_pagebreak {background-position:0 -40px}
.defaultSkin span.mce_restoredraft {background-position:-20px -40px}
.defaultSkin span.mce_spellchecker {background-position:-540px -20px}
.defaultSkin span.mce_visualblocks {background-position: -40px -40px}
.mceEditor table td { padding: 0 !important; }
.mceEditor .mceToolbar, .mceEditor .mceListBox, .mceEditor .mceSplitButton { border: none !important; margin: 0 !important; padding: 0 !important; }
.mceEditor .mceToolbar *, .mceEditor .mceListBox *, .mceEditor .mceSplitButton * { padding: 0 !important; }
.mceToolbar .toolBarGroup { border: 1px solid #949494; border-bottom: none; border-radius: 5px 5px 0 0; box-shadow: 0 -1px 5px #949494; margin-bottom: 0; padding: 5px !important; }
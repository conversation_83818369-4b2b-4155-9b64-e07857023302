
var checkCascade = true;
var checkAllCascade = true;

function checkAll() {
	if (! checkAllCascade) return false;
	checkAllCascade = false;
	var t = true;
	$('#table-reasons tbody input[type="checkbox"]').each(function() { t = t && $(this).attr('checked'); });
	if ($('#reasons-check-all').attr('checked') != t) $('#reasons-check-all').click();
	checkAllCascade = true;
	return true;
}

$(document).ready(function() {
	$('#table-reasons tbody input[type="checkbox"]').each(function() {
		$(this).click(function() { return checkAll(); });
	});
	$('#reasons-check-all').click(function() {
		if (! checkCascade) return false;
		checkCascade = false;
		var checked = $(this).attr('checked');
		$('#table-reasons tbody input[type="checkbox"]').each(function() { if ($(this).attr('checked') != checked) $(this).click(); });
		checkCascade = true;
		return true;
	});
	$('#reasons-delete').click(function() {
		location.href = '?submit-delete=&amp;'+$('#form-reasons').serialize();
	});
	
	$('#returns-categories input[type="checkbox"]').each(function() {
		$(this).click(function() {
			var id = (new RegExp('^returns-cat-([0-9]+)$')).exec($(this).attr('id'));
			if (! (id && id.length > 1)) return true;
			id = id[1];
			
			var val = $(this).attr('checked');
			$('#returns-children-'+id+' input[type="checkbox"]').each(function() {
				if ($(this).attr('checked') != val) $(this).click();
			});
			return true;
		});
	});
	
	$('#returns-categories ul').treeview({'collapsed': true, 'animated': true});
	
	if( typeof $('#table-reasons') != 'undefined' && $('#table-reasons') ){
		riaSortable.create({
			'table'	:	$('#table-reasons'),
			'url'	:	'/admin/ajax/returns/ajax-reason-position-update.php'
		});
	}
});

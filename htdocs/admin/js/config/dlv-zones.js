var currentAjaxRequest = false;

$(document).ready(function(){
    dlvLoadAllCatalog();

    $('.dlv-zones').hide();
    var selected = $('input.selected');

    $('.selected').removeClass('selected');
    $('#dlv-zones').show();

    if( !selected.length || $.trim(selected.attr('name'))=='' ){
        var location = window.location.href;
        if( location.match(/#tabGeneral/) ){
            dlvOngletActived( 'tabGeneral' );
        } else if( location.match(/#tabProducts/) ){
            dlvOngletActived( 'tabProducts' );
        } else {
            dlvOngletActived( 'tabGeneral' );
        }
    } else {
        dlvOngletActived( selected.attr('name') );
    }

    $('.tabstrip input').click( function(){
		$('.error, .success').remove();
		var name = $(this).attr('name');
		return dlvOngletActived( name );
    });
})

/** Cette fonction permet de changer l'onglet actif
 *	@param {string} name Nom de l'onglet à activer (attribut name)
 *	@return {bool} toujours false
 */
function dlvOngletActived( name ){
    var location = window.location.href;

    $('.dlv-zones').hide();
    $('.selected').removeClass('selected');
    $('.tabstrip input[name=' + name + ']').addClass('selected');

    segClsID = 0;
    switch( name ){
        case 'tabGeneral' :
            $('#dlv-zones').parents('form:eq(0)').submit(function(){
                return zonesValidForm(this);
            });
            $('#check_tab').val('general');
            $('#dlv-zones').show();
            break;
        case 'tabProducts' :
            $('#dlv-zones').parents('form:eq(0)').off('submit');
            $('#check_tab').val('produits');
            $('#tb-tabProducts').show();
            dlvLoadAllCatalog();
            break;
    }

    location  = location.replace(/#tabGeneral/, '' );
    location  = location.replace(/#tabProducts/, '' );

    window.location.href = location + '#' + name;
    return false;
}

/**
 * Cette fonction permet de charger les informations de l'onglet "Produits"
 */
function dlvLoadAllCatalog() {
    // ouverture de la popup de sélection selon le contenu recherché
    $('#dlv-add-rule input.text:not(#dlv-prd-name), #dlv-add-rule input.ref:not(#dlv-prd-name)').focus(function () {
        switch ($(this).attr('name')) {
            case 'dlv-cat-name':
                displayPopup('Sélectionner une catégorie', '', '/admin/catalog/popup-categories.php');
                break;
            case 'dlv-brd-name':
                displayPopup('Sélectionner une marque', '', '/admin/ajax/catalog/popup-brands-select.php');
                break;
        }
    });

    // affecte le focus à la bonne zone selon le contenu recherché
    $('#dlv-brd-select, #dlv-add-rule-brd').click(function () { $('#dlv-brd-name').focus(); });
    $('#dlv-cat-select, #dlv-add-rule-cat').click(function () { $('#dlv-cat-name').focus(); });
    $('#dlv-ref-select, #dlv-add-rule-prd').click(function () {
        return displayPopup('Sélectionner un produit', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0');
    });

    $('#dlv-prd-name').focus(function () {
        $('#dlv-add-rule .radio').removeAttr('checked');
        $('#dlv-add-rule-prd').attr('checked', 'checked');
        $('#elem-type-prd').val('prd');
    });
    loadRulesProducts();
}

/**
 *	Cette fonction permet de charger la zone affichant les inclusions ou exclusions.
 */
function loadRulesProducts() {
    currentAjaxRequest = $.getJSON('/admin/ajax/delivery/ajax-dlv-zones.php?getincludeprd=1&zone=' + zone, function (result) {

		var rows = '';
		for( const type in result ) {

			const cnts = result[type];

			for( const cnt in cnts ){
				var c = cnts[cnt];

				var prds = type != 'prd' ? '(' + c.prds + ' produit' + (parseInt(c.prds) > 1 ? 's' : '') + ')' : '';

				rows += '<tr>';
				rows += '	<td headers="inc-check"><input name="del[]" class="checkbox" type="checkbox" value="' + type + '-' + c.id + '"></td>';
				rows += '	<td colspan="2" headers="inc-rule"><a href="' + c.url + '">(' + c.include + ') ' + htmlspecialchars(c.name) + ' ' + prds + '</a></td>';
				rows += '</tr>';
			}

		}

		var rules = '';
        if (rows != '') {
            rules += '<table id="dlv-rules" class="dlv-rules" cellpadding="0" cellspacing="0">';
            rules += '	<col class="col1" /><col class="col2" /><col class="col3" />';
            rules += '	<thead><tr>';
            rules += '	<th id="inc-check"><input class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox"></th>';
            rules += '	<th id="inc-rule" colspan="2">' + divZonesExceptions + '</th>';
            rules += '	</tr></thead>';
            rules += '	<tfoot><tr><td colspan="3"><sub class="btn-move">' + divZonesSymboleDescription + '</sub><input onclick="return delRulesProducts();" type="submit" value="' + divZonesSupprimer + '" name="dlv-del-rule" class="button" /></td></tr></tfoot>';
            rules += '	<tbody>' + rows + '</tbody>';
            rules += '</table>';

            $('#dlv-list-rules-prd').html(rules);
        } else {
            $('#dlv-list-rules-prd').html('<div class="notice">' + divZonesNoException + '</div>');
        }

        currentAjaxRequest = false;
    });

    return false;
}

/**
 *	Cette fonction permet d'ajouter une règle d'inclusion ou d'exclusion.
 *	@param include Obligatoire, booléen indiquant s'il s'agit d'une inclusion (True) ou d'une exclusion (False)
 */
function addRulesProducts(include) {
    if (currentAjaxRequest){
		currentAjaxRequest.abort();
	}

    var id = $('#elem-id-prd').val();
    var type = $('#elem-type-prd').val();

    if (!$.trim(type) || (type != 'prd' && !$.trim(id))) {
        dlvMessage(divZonesSelect, true);
        return false;
    }

    if (type == 'prd' && $.trim(id) == '') {
        id = 'ref-' + $('#dlv-prd-name').val();
    }

    include = include ? 1 : 0;

    currentAjaxRequest = $.getJSON('/admin/ajax/delivery/ajax-dlv-zones.php?addrule=1&zone=' + zone + '&type=' + type + '&cnt=' + id + '&include=' + include, function () {
        currentAjaxRequest = false;
        loadRulesProducts();
    });

    return false;
}

/**
 *	Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion.
 */
function delRulesProducts() {
    if (currentAjaxRequest)
        currentAjaxRequest.abort();

    var dataPrdDel = '';
    var i = 0;

    $('#dlv-rules [type="checkbox"]:checked').each(function () {

        var attr = $(this).val();
        var cnt = attr.substring(4);
        var type = attr.substring(0, 3);

        dataPrdDel += '&type[' + i + ']=' + type + '&cnt[' + i + ']=' + cnt;
        i++;
    });

    if ($.trim(dataPrdDel) != '') {
        currentAjaxRequest = $.getJSON('/admin/ajax/delivery/ajax-dlv-zones.php?delrule=1&zone=' + zone + dataPrdDel, function () {
            currentAjaxRequest = false;
            loadRulesProducts();
        });
    }

    return false;
}

/**
 *	Cette fonction permet de sauvegarder l'information "Tous le catalogue inclu / exclu".
 */
function saveRulesProducts(){
	$('.page-load-block').show();
	if( currentAjaxRequest ){
		currentAjaxRequest.abort();
	}

	var all = $('#dlv-all-catalog-1').is(':checked') ? 1 : 0;

	var error = false;
    currentAjaxRequest = $.ajax({
        url: '/admin/ajax/delivery/ajax-dlv-zones.php',
        data: 'saveallcatalog=1&zone=' + zone + '&all=' + all,
        type: 'post',
        dataType: 'json',
        async: false,
        success: function( result ){
            if( !result.done ){
                error = true;
            }
        }
    });

	if( !error ){
		dlvMessage( "Les informations ont bien été mises à jour.", false );
	}else{
		dlvMessage( "Une erreur inattendue s'est produite lors de l'enregistrement des informations.", true );
	}

	$('.page-load-block').hide();
	return false;
}

/**
 *	Cette fonction permet de gérer la sélection d'un contenu à inclure ou exclure dans le système depuis sa popup.
 *	@param id Obligatoire, identifiant du contenu
 *	@param name Obligatoire, nom du contenu
 *	@param zoneID Obligatoire, identifiant du champ input[type=text] où le nom doit être affiché
 */
function chooseElemProducts(id, name, zoneID) {
    $('#dlv-add-rule .radio').removeAttr('checked');
    $('#dlv-add-rule input[type=text]').val('');
    $('#elem-id-prd').val('');

    var type = false;
    switch (zoneID) {
        case 'dlv-brd-name': type = 'brd'; break;
        case 'dlv-cat-name': type = 'cat'; break;
        case 'dlv-prd-name': type = 'prd'; break;
    }
    $('#elem-type-prd').val(type);

    // selection du bouton radio
    $('#' + zoneID).parent().find('input[type=radio]').attr('checked', 'checked');

    // gestion de l'affichage + enregistrement de l'identifiant du contenu
    $('#elem-id-prd').val(id);
    $('#' + zoneID).val(name);

    hidePopup();
    return false;
}

/** Fonction utilisé par la popup pour la sélection d'un élément à inclure ou exclure du système. */
function parent_select_brd(id, name) { return chooseElemProducts(id, name, 'dlv-brd-name'); }
function updateCat(id, idParent, name) { return chooseElemProducts(id, name, 'dlv-cat-name'); }

/**
 *	Cette fonction permet de sélectionner le produit offert.
 */
function parent_select_prd(id, name, ref, cat, input_id, input_name, input_ref) {
    currentCat = cat;
    if ($.trim(input_id) != '') {
        $('#' + input_id).val(id);
        $('#' + input_name).val(name);
        $('#' + input_ref).val(ref);
        $('#pop-ref').val(ref);
    } else {
        return chooseElemProducts(id, ref + ' - ' + name, 'dlv-prd-name');
    }
}

function dlvMessage(msg, error, input) {
    $('.success, .error').remove();

    if (msg == undefined){
		return false;
	}

    input = input != undefined ? input : 'tabpanel';

    $('#' + input).prepend('<div class="' + (error ? 'error' : 'success') + '">' + msg + '</div>');
	$(window).scrollTop(0);

    return false;
}
$(document).ready(function(){
		// Permet d'afficher le sélectionneur de date pour les champs de type date
		loadDatePicker();
});

/**
 * Cette fonction permet de charger ou de recharger le sélectionneur de date pour les champs de type date
 */
function loadDatePicker(){
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});
		
	});	
	
	// Parcours tous les champs de type heure
	$("input.hourpicker").each(function(){
		$(this).autocomplete({
			source:["00:00", "00:30", "01:00","01:30", "02:00", "02:30", "03:00", "03:30","04:00", "04:30", "05:00", "05:30", "06:00","06:30", "07:00", "07:30", "08:00", "08:30","09:00", "09:30", "10:00", "10:30", "11:00","11:30", "12:00", "12:30", "13:00", "13:30","14:00", "14:30", "15:00", "15:30", "16:00","16:30", "17:00", "17:30", "18:00", "18:30","19:00", "19:30", "20:00", "20:30", "21:00","21:30", "22:00", "22:30", "23:00", "23:30"],
			minChars: 0,
			delay:200,
			max:15,
			width:75
		});
	});
}
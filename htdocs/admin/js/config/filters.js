$(document).ready(
	function(){
		$('.check-all').click(function(){
			$($(this).parent().get(0)).find('input[type=checkbox]').attr('checked','checked');
			return false;
		});
		$('.uncheck-all').click(function(){
			$($(this).parent().get(0)).find('input[type=checkbox]').removeAttr('checked');
			return false;
		}); 
		$('.blc-ip').keyup(
			function(event){
				if( event.which == 110 || event.which==59 ){
					$(this).val( $(this).val().replace('.', '') );
					var next = $(this).nextAll('input').first();
					if( next.length ){
						next.focus();
					}
				}
			}
		);
	}
);
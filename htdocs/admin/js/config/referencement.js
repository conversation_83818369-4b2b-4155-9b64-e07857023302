var cursor_start_position = 0;
var cursor_end_position = 0;
$(document).ready(function() {
	var sentence_cls_id = $("#class-selector").val();
	var sentence_tag = $("#tag-selector").val();

	// Binde les champs de référencement avec la fonctionnalité fournissant des indications sur la qualité du champ
	if( typeof $('#tag_desc') != 'undefined' && $('#tag_desc').length ){
		$('#tag_desc').riametas({ force: true, padding: true, type : "desc" });
		$('#tag_title').riametas({ force: true, padding: true, type : "title" });
		$('#tag_title_ref').riametas({ type : "title" });
		$('#tag_desc_ref').riametas({ type : "desc" });
	}
	
	// tag-title n'est utilisé que dans le fichier config/referencement/edit-tag.php, il serait bien de mutualiser vers tag_title
	if( typeof $('#tag-title') != 'undefined' && $('#tag-title').length ){
		$('#tag-title').riametas({ force: true, padding: true, type: "title" });
		$('#tag-desc').riametas({ force: true, padding: true, type: "desc" });
		$('#tag-title-def').riametas({ force : true , type : "title" });
		$('#tag-desc-def').riametas({ force : true , type : "desc" });
	}

	// Binde le sélecteur de site
	$("#riawebsitepicker").click(function(){
		$('#riawebsitepicker .selector a').each(function(){
			var wst = $(this).attr('name').replace('w-','');
			if( wst =='all' || !wst ){
				wst = 0;
			}
			if( typeof $('.ref-page-static') != 'undefined' && $('.ref-page-static').length ){
				$(this).attr( 'href', 'static.php?wst=' + wst );
			}else{
				$(this).attr( 'href','index.php?wst='+wst );
			}
		});
		
		// Gère l'ouverture / fermeture du sélecteur		
		if( $("#riawebsitepicker .selector").css('display')=='none'){
			$("#riawebsitepicker .selector").show();
		}else{
			$("#riawebsitepicker .selector").hide();
		}
	});

	$('textarea#sentence-editor').bind('input propertychange click', function() {
		cursor_start_position = $('textarea#sentence-editor').prop("selectionStart");
		cursor_end_position = $('textarea#sentence-editor').prop("selectionEnd");
	});

	$("#class-selector").change(function(){
		sentence_cls_id = $("#class-selector").val();
		setConstantsAndSentences(sentence_cls_id, sentence_tag);
	});
	$("#tag-selector").change(function(){
		sentence_tag = $("#tag-selector").val();
		setConstantsAndSentences(sentence_cls_id, sentence_tag);
	});

	if ($("#edition-table").length && ($("#class-selector").val() == 0 || $("#tag-selector").val() == 0)){
		$("#edition-table").hide();
	} else {
		setConstantsAndSentences(sentence_cls_id, sentence_tag);
	}

	$("#variable-select").click(function(){
		optionSelection(cursor_start_position);
	});
});

$(document).delegate('#tag-wst', 'change', function(){
	$.get('/admin/config/referencement/ajax-load-tags.php?get-lang=' + $(this).val(), function( data ){
		$('#tag-lang').parent().html( data );
	});
});

function confirmDel(){
	return window.confirm(referencementConfirmSuppressionMarque);
}
function confirmDelImg(){
	return window.confirm(referencementConfirmSuppressionImageMarque);
}
function optionSelection(){
	if ($("#constant-selector").val() != null){
		const constant_code = $("#constant-selector").val();
		var constant_name = '';
		$(".variable-option").each(function(){
			if ($(this).val() == constant_code){
				constant_name = '['+$(this).text()+']';
			}
		});
		const actual_text = $("#sentence-editor").val();
		const before_string = actual_text.substring(0, cursor_start_position);
		const after_string = actual_text.substring(cursor_end_position, actual_text.length);

		if (actual_text.indexOf(constant_name) == -1){
			$("#sentence-editor").val(before_string + constant_name + after_string);
		}
		cursor_start_position = $('textarea#sentence-editor').prop("selectionStart");
		cursor_end_position = $('textarea#sentence-editor').prop("selectionEnd");
	}
}

function setConstantsAndSentences(sentence_cls_id, sentence_tag){
	if ($("#class-selector").length && sentence_cls_id != 0 && $("#tag-selector").length && sentence_tag != 0 ){
		$.get('/admin/ajax/referencement/ajax-sentences.php?cls_id=' + sentence_cls_id + '&tag=' + sentence_tag + '&lng=' + $("input#lng_code").val(), function( res ){
			if (res.code == 100){
				$("#edition-table tbody tr td #constant-selector").html(res.response.html_constants);
				if (res.response.html_sentences == ''){
					if ($("#sentences-table").length){
						$("#sentences-table").remove();
					}
				} else {
					if ($("#sentences-table").length){
						$("#sentences-table").remove();
					}
					$("#edition-table").after(res.response.html_sentences);
				}
			}
			if (res.code == 400){
				//Erreur lors du chargement, affichage du bloc erreur correspondant
				if ($('#error').length) {
					$("#error").html(res.response);
				} else {
					$('.error').remove();
					$('.success').remove();
					$("#site-content h2").after('<div class="error" id="error">'+res.response+'</div>');
				}
			}
		}, "json");
		if ($("#edition-table").length){
			$("#edition-table").show();
		}
	} else {
		if ($("#edition-table tbody tr td #constant-selector").length){
			$("#edition-table tbody tr td #constant-selector").html("");
		}
		if ($("#edition-table").length){
			$("#edition-table").hide();
		}
		if ($("#sentences-table").length){
			$("#sentences-table").remove();
		}
	}
}

function editSentence(seo_id){
	if ($("#seo_id").length){
		$("#seo_id").val(seo_id);
	} else {
		$("form").prepend('<input type="hidden" id="seo_id" name="seo_id" value="'+seo_id+'" />');
	}
	const template = $("#sentence-"+seo_id+" .sentence-text").text();
	$("#sentence-editor").text(template);
}

function delSentence(seo_id){
	$.ajax({
		type: "get",
		url: '/admin/ajax/referencement/ajax-sentences.php',
		data: 'del=1&seo_id='+seo_id+'&cls_id=' + $("#class-selector").val() + '&tag=' + $("#tag-selector").val() + '&lng=' + $("input#lng_code").val(),
		dataType: 'json',
		success: function(res) {
			if (res.code == 100){
				setConstantsAndSentences($("#class-selector").val(), $("#tag-selector").val());
			}
			if (res.code == 400){
				//Erreur lors du chargement, affichage du bloc erreur correspondant
				if ($('#error').length) {
					$("#error").html(res.response);
				} else {
					$('.error').remove();
					$('.success').remove();
					$("#site-content h2").after('<div class="error" id="error">'+res.response+'</div>');
				}
			}
		} 
	});
	if ($("#seo_id").length){
		$("#seo_id").remove();
	}
	if ($("#sentence-editor").length){
		$("#sentence-editor").html("");
	}
}
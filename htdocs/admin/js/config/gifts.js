$(document).ready(
	function(){

	}
).delegate(
	'#add-prd', 'click', function(){
		displayPopup( giftDisplayPopupREchercheProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&cat=0' );
		return false;
	}
).delegate(
	'#del', 'click', function(){
		$('#list-prd input:checked').each(
			function(){
				$(this).parents('tbody tr').remove();
				$('.checkall').removeAttr('checked');
			}
		);
		
		if( !$('#list-prd tbody tr').length ){
			$('#list-prd tbody').html('<tr class="no-prd"><td colspan="3">' + giftAucunProduitCadeau + '</td></tr>');
		}
		
		reloadListProductsID( true );
	}
);

function parent_select_prd( prdID, prdName, prdRef ){
	
	$.getJSON('/admin/ajax/config/ajax-gifts.php?get-info-gift=1&prd='+prdID, function( info ){
		var line = '';
		
		line += '<tr>';
		line += '	<td class="td-check-del" headers="check-del">';
		line += '		<input type="checkbox" name="del[]" id="del' + prdRef + '" value="' + prdRef + '" />';
		line += '	</td>';
		line += '	<td headers="prd-title">';
		line += '		' + viewPrdIsSync( info.isSync ) + ' <a href="/admin/catalog/product.php?cat=0&amp;prd=' + prdID + '">' + prdName + '</a>';
		line += '	</td>';
		line += '	<td headers="prd-amount">';
		line += '		' + info.amount;
		line += '	</td>';
		line += '</tr>';
		
		$('#list-prd .no-prd').remove();
		$('#list-prd tbody').append( line );
		
		reloadListProductsID( false );
	});
}

function reloadListProductsID( deleted ){
	$('.error, .success').remove();

	var listPrdID = '';
	$('#list-prd tbody input[type=checkbox]').each(
		function(){
			listPrdID += ( $.trim(listPrdID)!='' ? ';' : '' ) + $(this).val();
		}
	);
	
	$('#products').val( listPrdID );

	// Enregistre les actions réalisés sur les produits
	$.get('/admin/ajax/config/ajax-gifts.php?save-list-prd=1&products=' + $('#products').val(), function( res ){
		var error = ''; var success = '';

		if( deleted ){
			if( res=='ok' ){
				success = giftMsgSuccesSupression;
			}else{
				error = giftMsgErreurSupression;
			}
		}else{
			if( res=='ok' ){
				success = giftMsgSuccesEnregistrement;
			}else{
				error = giftMsgErreurEnregistrement;
			}
		}

		if( $.trim(error)!='' ){
			$('#list-prd').before('<div class="error">' + error + '</div>');
		}else{
			$('#list-prd').before('<div class="success">' + success + '</div>');
		}
	})
}
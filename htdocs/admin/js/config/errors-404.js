var currentAjaxRequest = false;
var currentAutocomplete = false;

$(document).ready(function(){
	var browserMSIE = navigator.userAgent.match( /msie/i );
	if( !browserMSIE ){
		window.onpopstate = function(e) {
			if( e.state == null ){
				updateSelects();
				reload_errors(1,'',true);
			}else{
				updateSelects();
				reload_errors(1,e.state,true);
			}
		};
	}

	// mise en place du sélecteur de site
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')==='block'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	$("#riawebsitepicker .selector a").click(function(){
		var wst = $(this).attr('name');
		var w = wst.substring(wst.indexOf('-')+1, wst.length);
		$('#wst_id').val( w );
		$('#riawebsitepicker .view').html( $(this).html() );
		
		reload_errors(1);
		$('#riawebsitepicker .selector').hide();
		return false;
	});

	$('#riadatepicker .selector a').click(function(){
		reload_errors(1);
	});

	$('#rialanguagepicker .selectorview').click(function () {
		if ($('#rialanguagepicker .selector').css('display') !== 'block') {
			$('#rialanguagepicker .selector').show();
		} else {
			$('#rialanguagepicker .selector').hide();
		}
	});
	$('#rialanguagepicker .selector a').click(function () {
		$('#rialanguagepicker .view').html($(this).html());
		var l = $(this).attr('name').substring($(this).attr('name').indexOf('-') + 1, $(this).attr('name').length);

		$('#lng').val(l);

		reload_errors(1);
		$('#rialanguagepicker .selector').hide();
	});
	
	// mise en place du filtre sur le début des urls retournant une erreurs 404
	$('#filter-url').keyup(function(){ reload_errors(1)	});

	// activation/désactivation du filtre sur l'affichage des erreurs avec/sans redirection
	$("#no-redirection").change(function(){ reload_errors(1) });	
	$("#resolved").change(function(){ reload_errors(1) });	
	
	// filtre sur le type
	$('#filter-type')
		.change(function() {

			var page = 1;
			if( $('#filterType').val()==$('#filter-type').val() ){
				page = $('#page').val();
			}
			
			reload_errors( page ); 
			$('#filterType').val( $('#filter-type').val() ); 
		})
		.riaSelector({'title': 'Type de contenu'});

	// filtre sur l'occurrence
	$('#filter-sort')
		.change(function() {

			var page = 1;
			if( $('#filterSort').val()==$('#filter-sort').val() ){
				page = $('#page').val();
			}
			
			reload_errors( page ); 
			$('#filterSort').val( $('#filter-sort').val() ); 
		})
		.riaSelector({'title': 'Trier par :'});

	reload_errors(0,'',true);

});
$(document).delegate('#type', 'change', function(){ reload_search(1); });
$(document).delegate('#search', 'click', function(){ reload_search(1); return false; });
$(document).delegate('#control', 'click', function(){
	$('.message-ajax-opacity').html(errorsVerificationUrl).show();
	$('.load-ajax-opacity').show();

	var reg = new RegExp("[,]+", "g");
	var date = getRiaDate();
	date = date.toString();
	date = date.split(reg);

	var filter 			= $('#filter-url').val();
	var filterType 		= $('#filter-type').val();
	var wst 			= $('#wst_id').val();
	var noRedirection 	= $('#no-redirection').is(':checked');
	var resolved 		= $('#resolved').is(':checked');
	var date1 			= date[0];
	var date2 			= date[1];

	var params = 'filter=' + filter + '&wst=' + wst + '&no-redirection='
		+ (noRedirection ? 1 : 0) + '&page=' + page + '&filter-type=' + filterType
		+ '&resolved=' + (resolved ? 1 : 0) + '&date1=' + date1 + '&date2=' + date2;

	$.ajax({
		url 	 : '/admin/config/redirections/errors/ajax-control.php',
		data	 : params,
		method	 : 'get',
		dataType : 'json'
	}).done(function () {
		window.location.reload();
	});

	return false;

});

function reload_search( page ){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	var q = $('#q').val();
	var lng = $('#lng').val();
	var wst = $('#wst').val();
	var type = $('#type').val();
	
	$('#page').val( page );
	$('#lst-res-search').html('<img class="loader" src="/admin/images/stats/loader.gif" alt="loader" /> ' + msgLoading + '');
	
	currentAjaxRequest = $.ajax({
		type: "POST",
		url: '/admin/ajax/search/json-search.php',
		data: 'q=' + q + '&lng=' + lng + '&page=' + page + '&wst=' + wst + '&type=' + type,
		dataType: 'json',
		async:true,
		success: function(json){
			var count = 0;
			var links = new Array();
			var pages = Math.ceil( json['nombre']/25 );
			
			if( page>1 )
				links[count++] = '<a onclick="return reload_search(' + (page-1) + ')" href="js_redirection.php?page=' + (page-1) + '">&laquo;' + errorsPagePrec + '</a>';
			
			for( var p=1; p<=pages; p++ ){
				if( p==page ) links[count++] = '<b>'+p+'</b>';
				else links[count++] = '<a onclick="return reload_search(' + p + ')" href="js_redirection.php?page=' + p + '">'+p+'</a>';
				
				if( count>9 ) break;
			}
			
			if( page<pages )
				links[count] = '<a onclick="return reload_search(' + (page+1) + ')" href="js_redirection.php?page=' + (page+1) + '">' + errorsPageSuiv + '&raquo;</a>';
			
			var html = '<table id="result-redirection" class="checklist">';
    		html += '	<caption>' + errorsCaptionResultat + '('+ json['nombre'] + ')</caption>';
    		html += '	<thead class="thead-none">';
    		html += '		<tr><th id="info"></th></tr>';
    		html += '	</thead>';
    		html += '	<tbody>';
        
			if( json['nombre']==0 )
				html = '	<td colspan="4">' + errorsNoResult + '</td>';
			else if( json['search'].length ){
				
				for( var c=0 ; c<json['search'].length ; c++ ){
					var s = json['search'][c];
					var url = json['url_site'] + s.url;
					
					html += '<tr>';
                    html += '	<td class="redirection" headers="img" class="img">';
                    html += '		<a href="' + url + '" target="_bank">';
					html += '			<img src="' + s.img + '" width="80" height="80" alt="' + s.name + '" title="' + s.name + '" />';
					html += '		</a>';
                    
                    // Affichage du nom
                    html += '		<div><a href="' + url + '" target="_bank">' + s.name + '</a></div>';
                    
                    // Affichage de la description
                    html += '		<div>' + s.desc + '</div>';
                    
                    // Redirection
                    html += '		<div>' + s.type_name + ' &raquo; <a href="' + url + '" target="_bank">' + url + '</a></div>';
                    html += '		<div>';
					html += '			<input title="' + errorsContenuSubstitution + '" style="float:right;" type="button" name="choose"  onclick="select_redirection(\''+ s.url +'\')" value="' + errorsChoisir + '"/>';
					html += '		</div>';
                    html += '	</td>';
                    html += '</tr>';
				}
				
			}
       		
			html += '	</tbody>';
    		html += '	 <tfoot>';
    		html += '		<tr>';
    		html += '			<td style="text-align: right;">'+ links.join(' | ') + '</td>';
    		html += '		</tr>';
    		html += '	</tfoot>';
  			html += '</table>';
			
			$('#lst-res-search').html( html );
		},
		complete: function(){ currentAjaxRequest = false; }
	});
	return false;
}

function reload_errors( page, state, inAjax ){
	inAjax 	 = typeof inAjax != "undefined" && inAjax;
	state 	= typeof state == 'undefined' ? '' : state;

	page = page<=0 ? $('#page').val() : page;
	
	var reg=new RegExp("[,]+", "g");
	var date = getRiaDate();
	date = date.toString();
	date = date.split(reg);
	
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	if(state===""){
		var filter = $('#filter-url').val();
		var filterType = $('#filter-type').val();
		var wst = $('#wst_id').val();
		var noRedirection = $('#no-redirection').is(':checked');
		var resolved = $('#resolved').is(':checked');
		var date1 = date[0];
		var date2 = date[1];
		var sort = $('#filter-sort').val();
		var dateView = $('#riadatepicker .function_name').html();
		var lng = $('#lng').val();

		var dataAjax = 'filter=' + filter + '&wst=' + wst + '&no-redirection='
			+ (noRedirection ? 1 : 0 ) + '&page=' + page + '&filter-type=' + filterType
			+ '&resolved=' + (resolved ? 1 : 0 )	+ '&date1=' + date1 + '&date2=' + date2
			+ '&sort=' + sort + '&dateView='+dateView+'&lng='+lng;
	}else{
		var dataAjax = state.dataAjax;
	}

	if( date[0] == date[1] ){ 
		$('#riadatepicker .view').html(format_date(date[0]));
	}else{
		$('#riadatepicker .view').html('du '+format_date(date[0])+' au '+format_date(date[1]));
	}

	var colspan = $('#lst-error-404 tbody').attr('data-colspans');
	if( wst !=="0" && wst !== ""){
		if($('#lst-error-404 #site').length >0){
			var newColspan = colspan - 1
			$('#lst-error-404 #site').remove();
			/*$('colgroup').find('col').last().remove();*/
			$('#lst-error-404 tbody').attr('data-colspans', newColspan);
			$('#url-content').attr({colspan:newColspan});
		}
	}else{
		if($('#lst-error-404 #site').length >0){
			$('#lst-error-404 #site').show();
		}else{
			var newColspan = parseInt(colspan) + 1;
			$('#lst-error-404 #redirection').after('<th id="site">Site</th>');
			/*$('colgroup').find('col').last().after('<col width="95">');*/
			$('#lst-error-404 tbody').attr({'data-colspans': newColspan});
			$('#url-content').attr({colspan:newColspan});
			
		}
		$('#lst-error-404 #redirection').removeAttr('colspan');
	}

	$('#page').val( page );
	$('#filter').val( filter );
	$('#lst-error-404 tbody').html('<tr><td colspan="'+colspan+'" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="loader"/> ' + msgLoading + '</td></tr>');

	currentAjaxRequest = $.ajax({
		type: 'GET',
		url: '/admin/ajax/redirections/errors/json-errors.php',
		data: dataAjax,
		dataType: 'json',
		async:true,
		success: function(json){
			var html = '';
			var colspan = $('#lst-error-404 tbody').attr('data-colspans');
			if( json['page']!=undefined ){
				page = json['page'];
				$('#page').val( json['page'] );
			}
			
			if( json['nombre']==0 ){
				html = '<td colspan="'+colspan+'">' + errors404 + '</td>';
			}
			else if( json['errors'].length ){
				
				for( var c=0 ; c<json['errors'].length ; c++ ){
					var e = json['errors'][c];
					var redirection = e.redirection!=false ? e.redirection : '';
					var escUrl = e.url.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;');

					html += '	<tr>';
					html += '		<td headers="url" data-label="Adresse URL : "><a href="#" onclick="view_popup_info(event,\'' + escUrl + '\', \'' + e.wst + '\', \'' + e.lng + '\' )">' + escUrl + '</a></td>';
					html += '		<td headers="count" class="align-right" data-label="Occurences : ">' + e.count + '</td>';
					html += '		<td class="right" headers="redirection" data-label="Rediriger vers : ">';
					html += '			<input type="hidden" name="oldurl[' + (c+1) + ']" id="oldurl-' + (c+1) + '" value="' + e.hiddenurl + '" />';
					html += '			<input type="hidden" name="wsturl[' + (c+1) + ']" id="wsturl-' + (c+1) + '" value="' + e.wst + '" />';
					html += '			<input type="hidden" name="lngurl[' + (c+1) + ']" id="lngurl-' + (c+1) + '" value="' + e.lng + '" />';
					html += '			<input type="text" name="redirection[' + (c+1) + ']" id="redirection-' + (c+1) + '" value="' + redirection + '" />';
					html += '			<img onclick="javascript:search_redirection(' + (c+1) + ', ' + e.wst + ', \'' + e.lng + '\');" src="/admin/images/petite_loupe_active.svg" name="search" id="search-' + (c+1) + '" alt="Chercher" title="' + errorsRechercheContenu + '" />';
					html += '		</td>';
					if( json['websites']>1 && (wst=='all' || wst==0) ){
						html += '	<td headers="site" data-label="Site : ">' + e.wst_name + '</td>';
						/*colspan++;*/
					}
					if( json['languages']>1 ){
						html += '	<td headers="lng" data-label="Version : ">' + e.lng_name + '</td>';
						/*colspan++;*/
					}
					html += '	</tr>';
					$('#url-content').attr({colspan:colspan});
					$('#pagination').attr({colspan:colspan});
				}
				
			}
			
			var links = new Array();
			// var page = 1;
			var pages = Math.ceil( json['nombre']/25 );
			var minPage = page - 4;
			minPage = minPage>0 ? minPage : 1;
			
			var count = 0;
			if( page>1 ){
				links[count++] = '<a onclick="return reload_errors(' + (page-1) + ')" href="errors-404.php?page=' + (page-1) + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">' + errorsPagePrec + '</a>';
			}

			for( var p=minPage; p<=pages; p++ ){
				if( p==page ){
					links[count++] = '<b>'+p+'</b>';
				}else{
					links[count++] = '<a onclick="return reload_errors(' + p + ')" href="errors-404.php?page=' + p + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">'+p+'</a>';
				}
				if( count>9 ){
					break;
				}
			}
			
			if( page<pages ){
				links[count] = '<a onclick="return reload_errors(' + (page+1) + ')" href="errors-404.php?page=' + (page+1) + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">' + errorsPageSuiv + '</a>';
			}
			
			$('#lst-error-404 tbody').html( html );
			$('#lst-error-404 caption').html( errorsUrl404 + ' ('+ json['nombre'] + ')');
			$('#pagination').html( links.join(' | ') );

		},
		complete: function(){
			currentAjaxRequest = false;
			if( !inAjax ){
				window.history.pushState({dataAjax: dataAjax}, '', 'errors-404.php?' + dataAjax);
			}
		}
	});
	return false;
}

function search_redirection(id, wst, lng){
	var url = '/admin/config/redirections/errors/js_redirection.php?id='+id+'&wst='+wst+'&lng='+lng;
	displayPopup(errorsRechecheContenuSubstitution, '', url);	
	$('#popup_riawysiwyg').fadeIn();
	return false;
}

function select_redirection(url, id){
	$("#redirection-"+id).val(url);
	hidePopup();
}

function view_popup_info( event, url, wst, lng ){
	event.stopPropagation();
	event.preventDefault();
	var popup = '/admin/config/redirections/errors/js_infos.php?url='+encodeURIComponent(url)+'&wst='+wst+'&lng='+lng;
	displayPopup('Informations détaillées sur l\'erreur 404', '', popup);
}

function updateSelects(){
	var params = window.location.search.replace('?','')==="" ? "filter=&wst=&no-redirection=1&page=1&filter-type=pages&resolved=0&date1=05/07/2016&date2=12/07/2016&sort=date-desc&dateView=Les%207%20derniers%20jours":window.location.search.replace('?','');
	var getParams = getParamsInUrl(params);

	date = [getParams.date1,getParams.date2];
	
	$('#riadatepicker .function_name').html(getParams.dateView);

	$('#filter-sort').val(getParams.sort);
	switch(getParams.sort){
		case 'date-asc' : {
			var html = errorsPremiereOccurence;
			break;
		}
		case 'date-desc' : {
			var html = errorsDerniereOccurence;
			break;
		}
		case 'count-desc' : {
			var html = errorsNombreOccurence;
			break;
		}
	}
	$('#div-filter-sort .view').html(html);

	$('#filter-type').val(getParams.filterType);
	switch(getParams.filterType){
		case 'all' : {
			var html = errorsAll404;
			break;
		}
		case 'pages' : {
			var html =errorsPages;
			break;
		}
		case 'images' : {
			var html = errorsImages;
			break;
		}
		case 'others' : {
			var html = errorsAutres;
			break;
		}
	}
	$('#div-filter-type .view').html(html);

	getParams.noRedirection == "1" ? $('#no-redirection').attr("checked", true) : $('#no-redirection').attr("checked", false);
	getParams.resolved == "1" ? $('#resolved').attr("checked", true) : $('#resolved').attr("checked", false);
}

$(document).ready(
	function() {

		const minHeight = $(".menu-tabstrip").height();
		$("#tabpanel-vertical").css("min-height",minHeight);

		// sélecteur de site
		$('#riawebsitepicker .selectorview').click(function(){
			if($('#riawebsitepicker .selector').css('display')=='none'){
				$('#riawebsitepicker .selector').show();
			}else{
				$('#riawebsitepicker .selector').hide();
			}
		});
		$('#riawebsitepicker .selector a').click(function(){
			var wst = $(this).attr('name');
			wst = wst.substring(wst.indexOf('-')+1, wst.length);
			window.location.href = '/admin/config/owner/index.php?wst=' + wst;
		});

		// Active l'auto-complétion du code NAF
		$('#naf').autocomplete(
			{
				source: 		'/admin/ajax/default/ajax-naf.php',
				delay:			10,
				minLength:		1,
				select: function (event, ui) {
					event.preventDefault();
					var itemName = ui.item.label.substring( 0, ui.item.label.indexOf(' - ') );
					$('#naf').val( itemName );
				},
			}
		);

		$('.ac_results').css('height', '125px')
	}
);
function disableById( inputs ){
	for( var i=0; i<inputs.length; i++ )
		document.getElementById(inputs[i]).disabled = true;
}
function enableById( inputs ){
	for( var i=0; i<inputs.length; i++ )
		document.getElementById(inputs[i]).disabled = false;
}
function toggleState(){
	var societyInputs = new Array( 'name','address1','address2','zipcode','city','inscription','capital','publication','redaction','save','cancel' );
	var personInputs = new Array( 'firstname','lastname','address1-2','address2-2','zipcode-2','city-2','inscription-2','save-2','cancel-2' );
	if( document.getElementById('owner-type1').checked ){
		enableById( societyInputs );
		disableById( personInputs );
	}else{
		disableById( societyInputs );
		enableById( personInputs );
	}
}

function validOwnerForm(frm){
	if( document.getElementById('owner-type1').checked ){
		if( !trim(frm.name.value) ){
			alert(ownerAlertRaisonSociale);
			frm.name.focus();
			return false;
		}
	}else{
		if( !trim(frm.firstname.value) ){
			alert(ownerAlertPrenom);
			frm.firstname.focus();
			return false;
		}
		if( !trim(frm.lastname.value) ){
			alert(ownerAlertNom);
			frm.lastname.focus();
			return false;
		}
	}
	return true;
}

window.onload = function(){
	document.getElementById('owner-type1').onchange = document.getElementById('owner-type2').onchange = toggleState;
	toggleState();
}
$(document).ready(function(){
	$('input.datepicker').each(function(){
		var temp = this ;
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});

	});

	if( typeof $('#hd-type') != 'undefined' && $('#hd-type').length ){
		$('#ria_reponse .selectorview').click(function(){
			if($('#ria_reponse .selector').css('display')=='none'){
				$('#ria_reponse .selector').show();
				/*$('#ria_reponse .selector').hide();*/ $('#ria_contacts .selector').hide(); $('#riawebsitepicker .selector').hide(); $('#ria_sort .selector').hide();
			}else{
				$('#ria_reponse .selector').hide();
			}
		});
		$('#ria_contacts .selectorview').click(function(){
			if($('#ria_contacts .selector').css('display')=='none'){
				$('#ria_contacts .selector').show();
				/*$('#ria_contacts .selector').hide();*/ $('#ria_reponse .selector').hide(); $('#riawebsitepicker .selector').hide(); $('#ria_sort .selector').hide();
			}else{
				$('#ria_contacts .selector').hide();
			}
		});
		$('#ria_sort .selectorview').click(function(){
			if($('#ria_sort .selector').css('display')=='none'){
				$('#ria_sort .selector').show();
				/*$('#ria_sort .selector').hide();*/ $('#ria_contacts .selector').hide(); $('#riawebsitepicker .selector').hide(); $('#ria_reponse .selector').hide();
			}else{
				$('#ria_sort .selector').hide();
			}
		});
		$('#riawebsitepicker .selectorview').click(function(){
			if($('#riawebsitepicker .selector').css('display')=='none'){
				$('#riawebsitepicker .selector').show();
				/*$('#riawebsitepicker .selector').hide();*/ $('#ria_reponse .selector').hide(); $('#ria_contacts .selector').hide(); $('#ria_sort .selector').hide();
			}else{
				$('#riawebsitepicker .selector').hide();
			}
		});
	}

	$('#site-content .moderation-menu a:not(.btn)').click(function(){
		$(this).parents('.riapicker').find('.view').html( $(this).html() )
		$(this).parent().hide();

		var attrName = $(this).attr('name');
		var attr = attrName.substring(0, 3);
		var choose = attrName.substring(4);

		if( attrName.substring(0, 2)=='w-' ){
			attr = 'wst';
			choose = attrName.replace('w-', '');
		}

		switch( attr ){
			case 'rep' :
				$('#hd-reponse').val( choose=='w' ? 1 : (choose=='nw' ? 0 : -1) );
				break;
			case 'cnt' :
				$('#hd-contact').val( choose=='w' ? 1 : (choose=='nw' ? 0 : -1) );
				break;
			case 'dat' :
				$('#hd-sort').val( choose );
				break;
			case 'wst' :
				$('#hd-wst').val( choose );
				break;
		}

		loadMessages();
	});

	$('#exporter').click(function(){ $('#filter').show(); });
	$('#filter .check-all-col').click(function(){
		$('#filter input.col-filter').each(function(){ $(this).attr('checked','checked'); });
	});
	$('#filter .uncheck-all-col').click(function(){
		$('#filter input.col-filter').each(function(){ $(this).removeAttr('checked'); });
	});
	$('#filter .check-all-fld').click(function(){
		$('#filter input.fld-filter').each(function(){ $(this).attr('checked','checked'); });
	});
	$('#filter .uncheck-all-fld').click(function(){
		$('#filter input.fld-filter').each(function(){ $(this).removeAttr('checked'); });
	});

	window.onpopstate = function(e) {
	    var pageAjax = e.state ? e.state.page : 1;
	    var urlAjax = e.state ? e.state.url : '';
	    loadMessages( pageAjax, '', urlAjax );
	};
});

/**
 *	Permet de bloquer un adresse IP
 */
function block_ats_ip( id ){
	// return false;
	$.ajax({
		type: 'POST',
		url: '/admin/moderation/ajax-moderation.php',
		data: 'msg='+id+'&spam=1',
		success: function( res ){
			if( res==1 ){
				$('#message-'+id).remove();
				if( $('#cntcontact tbody tr').length==0 ){
					$('#cntcontact tbody').html( '<tr><td colspan="2">' + moderationNoMessageCritere + '</td></tr>' );
				}
			}
		},
		complete: function(){
			var p = parseInt( $('#hd-page').val() );
			if( isNaN(p) ){
				p = 1;
			}

			loadMessages( p, 0, false );
		}
	});
}

/**
 *	Permet de débloquer une adresse IP
 */
function unblock_ats_ip( id ){
	$.ajax({
		type: 'POST',
		url: '/admin/moderation/ajax-moderation.php',
		data: 'msg='+id+'&spam=0',
		success: function( res ){
			if( res==1 ){
				$('#message-'+id).remove();
				if( $('#cntcontact tbody tr').length==0 ){
					$('#cntcontact tbody').html( '<tr><td colspan="2">' + moderationNoMessageCritere + '</td></tr>' );
				}
			}
		}
	});
}

/**
 * Permet le rechargement des messages
 */
function loadMessages( page, last, saveInHistory ){
	if( typeof saveInHistory != 'undefined' && $.trim(saveInHistory) != '' && saveInHistory !== false ){
		urlAjax = saveInHistory.replace('moderation.php?', '');

		var params = splitParamsInURL( urlAjax );
		$('#hd-contact').val( params.have_usr );
		$('#hd-usr').val( params.usr );
		$('#hd-reponse').val( params.have_rep );
		$('#hd-sort').val( params.dir );
		$('#hd-type').val( params.type );
		$('#hd-wst').val( params.wst_id );
		$('#hd-str').val( params.str );
	}

	var p = page>0 ? page : 1;
	var haveUsr = $('#hd-contact').val();
	var usr = $('#hd-usr').val();
	var haveRep = $('#hd-reponse').val();
	var dir = $('#hd-sort').val();
	var type = $('#hd-type').val();
	var wst = $('#hd-wst').val();
	var str = $('#hd-str').val();

	$('#hd-page').val( p );

	var elemRiaRep = $('#ria_reponse .selectorview .view');
	if( haveRep == '0' ){
		elemRiaRep.html(moderationAttenteReponse);
	}else if( haveRep == '1' ){
		elemRiaRep.html(moderationReponseRecu);
	}else{
		elemRiaRep.html(moderationTousMessage);
	}

	var elemRiaUsr = $('#ria_contacts .selectorview .view');
	if( haveUsr == '0' ){
		elemRiaUsr.html(moderatiuoContactSansClient);
	}else if( haveUsr == '1' ){
		elemRiaUsr.html(moderationContactAvecClient);
	}else{
		elemRiaUsr.html(moderationTousMessage);
	}

	var urlAjax = 'p=' + p + '&wst='  +  wst  +  '&type=' + type + '&have-usr=' + haveUsr + '&have-rep=' + haveRep + '&dir=' + dir + '&str=' + str + '&usr=' + usr;
	$.ajax({
		type: "GET",
		url: '/admin/ajax/moderation/json-moderation.php',
		data: urlAjax,
		dataType: 'json',
		async:true,
		success: function(json){
			var html = '';
			if( json['nb_msg']==0 ){
				html = '<td colspan="2">' + moderationNoMessageCritere + '</td>';
			} else {
				// Créé le contenu du tableau
				var message = '',
					to = '',
					cc = '',
					infos = '',
					reply = '',
					docs = '',
					file = '',
					label = '';
				var pages = Math.ceil( json['nb_msg']/25 );

				for( var c=0 ; c<json['message'].length ; c++ ){
					if( c>=25 ){
						break;
					}
					message = json['message'][c];
					to = json['contact'][c]['to'] ? json['contact'][c]['to'] : '';
					cc = json['contact'][c]['cc'] ? json['contact'][c]['cc'] : '';
					infos = json['contact'][c]['infos'] ? json['contact'][c]['infos'] : '';
					docs = json['contact'][c]['docs'] ? json['contact'][c]['docs'] : '';
					reply = json['contact'][c]['reply'] ? json['contact'][c]['reply'] : '';

					html += '	<tr id="message-' + message.id + '">';
					html += '		<td headers="th-author" class="td-author">';

					if( message.ord_url!=undefined && message.ord_url.length ){
						html += '		<span class="bold">' + moderationDevisConcerne + ' : </span><br />';
						html += '		<a href="'+htmlspecialchars(message.ord_url)+'" target="_bank">N°'+htmlspecialchars(message.ord_id)+'</a><br /><br />';
					}

					if( message.prd_url!=undefined && message.prd_url.length ){
						html += '		<span class="bold">' + moderationProduitConcerne + ' :</span><br />'+viewPrdIsSync(message.prd_is_sync)+'&nbsp;';
						html += '		<a href="'+htmlspecialchars(message.prd_url)+'" target="_bank">'+htmlspecialchars(message.prd_title)+'</a><br /><br />';
					}

					html += '			<span class="bold">' + moderationEnvoyePar + ' :</span><br />' + viewUsrIsSync(message.is_sync) + ' ';
					if( message.usr_id>0 ){
						html += '		<a href="/admin/customers/edit.php?usr=' + message.usr_id + '" target="_bank">' + htmlspecialchars(message.firstname != null ? message.firstname : '') + ' ' + htmlspecialchars(message.lastname != null ? message.lastname : '') + ' ' + htmlspecialchars(message.society != null ? message.society : '') + '</a>';
						if( message.surnom!='' )
							html +=  '	<br />( Surnom : ' + htmlspecialchars(message.surnom) + ' )';
					} else if( message.firstname!='' || message.lastname!='' ){
						html += '		' + htmlspecialchars(message.firstname != null ? message.firstname : '')
							 + ' ' + htmlspecialchars(message.lastname != null ? message.lastname : '')
							 + ' ' + htmlspecialchars(message.society != null ? message.society : '')
							 + ' ' + htmlspecialchars('<' + message.email + '>') + '';
					}else{
						html += '		' + htmlspecialchars(message.email);
					}
					html += '			<br />';
					html += '			<em>' + moderationLe + message.date_created + '</em><br /><br/>';

					if( to !='' ){
						var htmlTo = '';
						for( i=0 ; i<to.length ; i++ ){
							if( to[i].id>0 ){
								if( to[i].type == 'user' ){
									var nameConcat = '';
									if(to[i].firstname == '' && to[i].lastname == ''){
										nameConcat = to[i].email;
									}else{
										nameConcat = to[i].firstname + ' ' + to[i].lastname + ' ' + to[i].society;
									}

									htmlTo += '<br />' + viewUsrIsSync(to[i].is_sync) + ' ';
									htmlTo += to[i].id ? '<a href="/admin/customers/edit.php?usr=' + to[i].id + '" target="_bank">' + htmlspecialchars(nameConcat) + '</a>' : htmlspecialchars(nameConcat);
								}else{
									htmlTo += '<br />' + viewStrIsSync(to[i].is_sync) + ' ';
									htmlTo += to[i].id ? '<a href="/admin/config/livraison/stores/edit.php?str=' + to[i].id + '" target="_bank">' + htmlspecialchars( to[i].name ) + '</a>' : htmlspecialchars( to[i].name );
								}
							} else if( to[i].email!='' ) {
								htmlTo += '<br /><a href="mailto:' + htmlspecialchars(to[i].email) + '">' + htmlspecialchars(to[i].email) + '</a>';
							}
						}

						if( htmlTo!='' ){
							html += '		<span class="bold">' + moderationDestinataire + ' : </span>'+htmlTo;
						}
					}

					if( cc !='' ){
						var htmlCc = '';
						for( i=0 ; i<cc.length ; i++ ){
							if( cc[i].id>0 ){
								if( cc[i].type == 'user' ){
									var nameConcat = '';
									if(cc[i].firstname == '' && cc[i].lastname == ''){
										nameConcat = cc[i].email;
									}else{
										nameConcat = cc[i].firstname + ' ' + cc[i].lastname + ' ' + cc[i].society;
									}

									htmlCc += '<br />' + viewUsrIsSync(cc[i].is_sync) + ' ';
									htmlCc += cc[i].id ? '<a href="/admin/customers/edit.php?usr=' + cc[i].id + '" target="_bank">' + htmlspecialchars(nameConcat) + '</a>' : htmlspecialchars(nameConcat);
								}else{
									htmlCc += '<br />' + viewStrIsSync(cc[i].is_sync) + ' ';
									htmlCc += cc[i].id ? '<a href="/admin/config/livraison/stores/edit.php?str=' + cc[i].id + '" target="_bank">' + htmlspecialchars( cc[i].name ) + '</a>' : htmlspecialchars( cc[i].name );
								}
							} else if( cc[i].email!='' ) {
								htmlCc += '<br /><a href="mailto:' + htmlspecialchars(cc[i].email) + '">' + htmlspecialchars(cc[i].email) + '</a>';
							}
						}

						if( htmlCc!='' )
							html += '		<br /><br /><span class="bold">' + moderationLeCopie + ' : </span>'+htmlCc;
					}

					if( json.moderate==1 ){
						var datePublish ='';

						html += '			<div id="moderate-' + message.id + '" class="moderate">';
						// Date de publication ou dépublication
						if( message.date_publish != '' ){
							datePublish = message.date_publish;
							if( message.usr_publish>0 ){
								datePublish += ' <br />Par ';
								datePublish += viewUsrIsSync( message.usr_publish_is_sync );
								datePublish += ' <a target="_blank" href="/admin/customers/edit.php?usr=' + message.usr_publish + '">' + htmlspecialchars(message.adr_firstname) + ' ' + htmlspecialchars(message.adr_lastname) + '</a>';
							}
						}

						// Information sur le statut du message
						switch( message.is_publish ){
							case '1' :
								html += '<span class="bold">' + moderation + ' :</span>';
								html += '<span class="info-publish">' + moderationApprouveLe;
								if( datePublish!='' )
									html += datePublish;
								else
									html += '<br />' + moderationPar;
								html += '<br /><a onclick="moderateMessage(' + message.id + ', false)" class="unchecked">' + moderationNePlusApprouve + '</a></span>';
								break;
							case '0' :
								html += '<span class="bold">' + moderation + ' :</span>';
								html += '<span class="info-publish">' + moderationRefuseLe;
								if( datePublish!='' )
									html += datePublish;
								else
									html += '<br />' + moderationPar;
								html += '<br /><a onclick="moderateMessage(' + message.id + ', true)" class="checked">' + moderationApprouver + '</a></span>';
								if (message.moderation_comment != ""){
									html += '<span class="bold">' + moderationCommentaireDesapprobation + ' : </span>';
									html += '</br /><span><em>'+message.moderation_comment+'</em></span>'
								}
								break;
							default :
								html += '<span class="bold">' + moderation +  ':</span>';
								html += '<span class="info-publish">';
								html += '' + moderationEnAttente + '' + message.date_created;
								html += '<br /><a onclick="moderateMessage( ' + message.id + ', true)" class="checked">Approuver</a> | <a onclick="moderateMessage( ' + message.id + ', false)" class="unchecked">' + moderationDesapprouver + '</a>';
								html += '</span>';
								break;
								html += '<span class="bold">' + moderation + '</span>';
								html += '<span class="info-publish"><a onclick="moderateMessage( 44481, true)" class="checked">Approuver</a> | <a onclick="moderateMessage( 44481, false)" class="unchecked">' + moderationDesapprouver + '</a></span>';
								break;
						}
						html += '			</div>';
					}
					html += '		</td>';
					html += '		<td headers="th-message" class="td-message">';
					html += '			<span class="bold">' + moderationSujet + ' : ' + htmlspecialchars(message.subject) + '</span><br />';
					if( json.tenant==8 ){
						html += message.body.replace( new RegExp('\\\*', 'g'), '<br />*');
					}else if( json.tenant==9 ){
						html += message.body.replace( new RegExp("\\n", 'g'), '<br />&nbsp;&nbsp;');
					}else{
						html += '			' + message.body + '<br />';
					}

					if( message.note>0 ){
						html += '	 	<br /><span class="bold italic">' + moderationNote + '</span> : ' + message.note;
					}
					if( message.note_dlv>0 ){
						html += '	 	<br /><span class="bold italic">' + moderationNoteLivraison + '</span> : ' + message.note_dlv;
					}
					if( message.note_pkg>0 ){
						html += '	 	<br /><span class="bold italic">' + moderationNoteEmballage + '</span> : ' + message.note_pkg;
					}

					if( docs!='' ){
						label = docs.length>1 ? moderationPiecesJointes : moderationPieceJointe ;
						html += '<br /><span class="bold italic">' + label + '</span> : ';
						for( var i=0 ; i<docs.length ; i++ ){
							html += (i>0 ? ', ' : '') + docs[i];
						}

					}

					// Informations complémentaires
					if( infos.length ) {
						var first = true; var div=false;
						for( var i=0 ; i< infos.length ; i++ ){
							if( infos[i].value!='' && first ){
								html += '			<div class="infos-compl"><span class="bold">' + moderationInfoComplementaire + ' : </span><br/>';
								first = false; div = true;
							}
							if( infos[i].value!='' ){
								html += '				<span class="bold italic info-value">'+ htmlspecialchars(infos[i].name) +'</span> : '+ infos[i].value +'<br/>';
							}
						}
						if( div ){
							html += '				</div>';
						}
					}

					// Réponse(s)
					html += '	<div id="reponse-' + message.id + '">';
					if( reply!='' ){
						if( reply.length>1 ){
							html += '<br />';
							html +=	'<a id="show-lst-rep-' + message.id + '" onclick="show_rep(' + message.id + ')">' + moderationAfficherReponses + '</a>';
							html +=	'<a id="hide-lst-rep-' + message.id + '" onclick="hide_rep(' + message.id + ')" class="none">' + moderationMasquerReponses + '</a>';
						} else {
							html += '<br />';
							html +=	'<a id="show-lst-rep-' + message.id + '" onclick="show_rep(' + message.id + ')">' + moderationAfficherReponse + '</a>';
							html +=	'<a id="hide-lst-rep-' + message.id + '" onclick="hide_rep(' + message.id + ')" class="none">' + moderationMasquerReponse + '</a>';
						}

						html += '	<div id="lst-rep-' + message.id + '" class="none">';
						for( var r=0 ; r<reply.length ; r++ ){
							file = reply[r]['file'] ? reply[r]['file'] : '';
							html += '	<div class="rep">';
							html += '		<span class="bold">' + moderationVotreReponse + ' : </span><br /><em>' + moderationLe+reply[r].date_created+'</em>';
							html += '		<br /><br />'+reply[r].body;

							// Pièce jointe aux réponses
							if( file!='' ){
								html += '	<div>';
								html += '		<br />' + moderationPiecesJointes + ' :<ul>';
								for( var f=0 ; f<file.length ; f++ ){
									html += '	<li><a href="/admin/customers/dl.php?file='+file[f].id+'">'+htmlspecialchars(file[f].name)+'</a> <span class="size-file">('+file[f].size+')</span></li>';
								}
								html += '		</ul>';
								html += '	</div>';
							}
							html += '	</div>';
						}
						html += '		<div class="clear"></div>';
						html += '	</div>';
					}
					html += '	</div>';

					// Formulaire pour répondre
					html += '		<a name="rep-'+message.id+'"></a>';
					html += '			' + message.origins;
					html += '			<div id="action-'+message.id+'" class="action">';

					if( message.type != 'DIRECT_CONTACT' ){
						html += '				<input type="button" id="button-rep-' + message.id + '" class="show-form-replay" value="' + moderationRepondre + '" onclick="show_form_rep(\'' + message.id + '\')" />';
					}

					if( message.spam_id == 0 ){
						html += '			<input type="button" id="button-spam-' + message.id + '" class="show-form-spam" value="' + moderationSignalerSpam + '" onclick="block_ats_ip(' + message.id + ')" />';
					}else{
						html += '			<input type="button" id="button-spam-' + message.id + '" class="show-form-spam" value="' + moderationRetirerSpam + '" onclick="unblock_ats_ip(' + message.id + ')" />';
					}
					html += '			</div>';
					html += '			<div class="clear"></div>';
					html += '		<div id="form-rep-' + message.id + '" style="display:none" class="new-rep">';
					html += '			<form action="/admin/moderation/moderation.php" id="form-contact-'+message.id+'" method="post" enctype="multipart/form-data">';
					html += '			<input type="hidden" name="tab-file" id="tab-file-'+message.id+'" value="" />';
					html += '			<input type="hidden" name="msg" value="'+message.id+'" />';
					html += '			<span class="title-new-rep">' + moderationReponse + ' : </span>';
					html += '			<div class="clear"></div>';
					html += '			<div class="message"><textarea name="reponce-message" cols="67" rows="10" placeholder="' + moderationSaisirReponse + '"></textarea></div>';
					html += '			<div class="bottom-answer">';
					html += '				<a id="link-join-file-' + message.id + '" onclick="show_join(' + message.id + ')">' + moderationAttacherFichier + '</a>';
					html += '				<div id="div-file-' + message.id + '" class="join-file" style="display:none;">';
					html += '					<span class="title-new-rep">' + moderationPiecesJointes +'  : </span>';
					html += '					<div id="join-file-'+message.id+'"></div>';
					html += '					';
					html += '				</div>';
					// html += '			<div class="clear"></div>';
					html += '				<div class="btn-group">';
					html += '					<input type="button" name="cancel-rep" value="' + moderationAnnuler + '" onclick="hide_form_rep(\'' + message.id + '\')" />';
					html += '					<input class="submit-rep" value="' + moderationEnvoyer + '" type="submit" name="submit-rep" onclick="return sendReponse('+message.id+');" />';
					html += '				</div>';
					html += '			</div>';
					html +=	'			</form>';
					html += '		</div>';
					html += '		<div class="clear"></div>';
					html += '	</td>';
					html += ' </tr>';

				}
			}
			// Affiche le contenu
			$("#cntcontact tbody").html(html);

			// Créé la pagination
			$("#pagination").html(switchPage(p, pages, 5, 5, 0, 0, 'loadMessages', '', '', ''));

			return false;
		},
		error: function(){
			return true;
		},
		complete: function(){
			if( typeof saveInHistory == 'undefined' ){
				var targetUrl = location.pathname + '?page='+p;
				if( usr !== undefined ){
					targetUrl += '&usr=' + usr + '&tab=contacts';
				}else{
					targetUrl += '&type='+type+'&wst_id=' + wst + '&have-usr='+haveUsr+'&have-rep='+haveRep+'&dir='+dir+'&str='+str;
				}
				window.history.pushState({url: "" + targetUrl + "", page: "" + p + ""}, '', targetUrl);
			}
		}
	});
	return false;
}

/**
 *	Permet de publier ou dépublier un message
 */
function moderateMessage( msg, publish, comment ){
	// Masque les messages de succès et d'erreur actuellement affichés
	removeMessages();

	if (comment == undefined){
		comment = "";
	}
	if (!publish && comment == ""){
		displayPopup(moderationDesapprobationMessage, '', '/admin/moderation/popup-moderation-message.php?msg_id='+msg, '', 600, 250);
	} else {
		$.ajax({
			type: "POST",
			url: '/admin/moderation/ajax-moderation.php',
			data: 'msg='+msg+'&publish='+(publish ? 1 : 0)+'&comment='+comment,
			dataType: 'text',
			async:true,
			success: function(txt){
				if( txt ){
					$('#moderate-'+msg).html( txt );
				}
			}
		});
	}
}

/**
 *	Permet de répondre à un message
 */
function sendReponse( msg ){
	// Masque les messages de succès et d'erreur actuellement affichés
	removeMessages();

	// Vérifie qu'une réponse a été saisie dans le champ prévu à cet effet
	var $respMessage = $('#form-rep-'+msg+' textarea').get()[0];
	if( $.trim($respMessage.value)=='' ){
		alert(moderationSaisirMsgRepondre);
		return false;
	}

	// Construit la requête Ajax permettant d'envoyer la réponse au serveur
	var query = $('#form-rep-'+msg).find('input').map(
		function(i, input ){
			return input.name + '=' + input.value;
		}).get().join('&');

	// Envoie la requête et traite la réponse du serveur
	$.ajax({
		type: "POST",
		url: '/admin/moderation/ajax-moderation.php',
		data: query + '&' + $respMessage.name + '=' + $respMessage.value,
		dataType: 'text',
		async:true,
		success: function(txt){
			var html = '';

			if( txt!='' && txt!='0' ){ // L'envoi du message a fonctionné
				html = '<div class="error-success">' + moderationMsgEnvoye + '</div>';
				$('#reponse-'+msg).html( txt );
				// Réinitialise le formulaire de réponse
				$('#tab-file-'+msg).val(''); $('#join-file-'+msg).html('');
				$('#form-contact-'+msg+' textarea').val('');
				$('#div-file-'+msg).addClass('none'); $('#link-join-file-'+msg).removeClass('none');
				$('#form-rep-'+msg).addClass('none'); $('#button-rep-'+msg).removeClass('none');
			} else{
				html = '<div class="error">' + moderationErreruEnvoiMsg + '</div>';
			}

			$('#form-contact-'+msg).parents('td').prepend( html );
		}
	});
	return false;
}

function reloadAnswers( msgID ){
	$.ajax({
		type: "POST",
		url: '/admin/moderation/ajax-moderation.php',
		data: 'reload-answers=1&msg_id=' + msgID,
		dataType: 'text',
		async:true,
		success: function( html ){
			$('#reponse-' + msgID).html( html );
		}
	});

	hidePopup();
	return false;
}

if( typeof $('.list-adr-email input[type=text]') != 'undefined' && $('.list-adr-email input[type=text]').length ){
	var nbAdr = $('.list-adr-email input[type=text]').length;
	var data = new Array();
	var files;

	function prepareUpload( event ){
		files = event.target.files;
		uploadFiles( event );
	}

	function uploadFiles( event ){
		event.stopPropagation();
		event.preventDefault();

		var data = new FormData();
		$.each(files, function( key, value ){
			data.append(key, value);
		});

		$.ajax({
			url: '/admin/moderation/popup-contact.php?upload_files=1&files',
			type: 'POST',
			data: data,
			cache: false,
			dataType: 'json',
			processData: false,
			contentType: false,
			success: function( data ){
				updateListFiles( data );
			}
		});
	}

	function initAutoComplete( input ){
		input.autocomplete({
			source:data,
			select: function() {
				input.val('ui.item.value');
			}
		});
	}

	function updateListFiles( data ){
		var hFiles = '';

		$.each( data, function(key, file){
			hFiles += '<div class="attach"><span class="name">' + file.name + '</span> <span class="weight">(' + file.size + ')</span><span class="del"></span></div>';
		});

		$('.list-file').html( hFiles );
		return false;
	}

	$(document).ready(function(){
		$('#send').click(function(){
			$('.error, .success').remove();
		});

		if( $('.error, .success').length ){
			$('input, select, textarea').focus(function(){
				$('.error, .success').remove();
			});
		}
		// Charge les donnees du select dans la variable data
		opts = $('#list-mail option');
		for( var i = 0; i < opts.length; i++ ){
			if( i > 0 ){
				data.push({
					value : $(opts.get(i)).attr('value'),
					label : $(opts.get(i)).text()
				});
			}
		}

		$('#subject').keypress(function( event ){
			if( event.keyCode == '13' ){
				$('#message').focus();
				return false;
			}
		});

		$('.list-adr-email input[type=text]').each(function(){
			initAutoComplete( $(this) );
		});

		$('.fileupload').each(function (){
			var self = $(this);
			$('input[type=file]', self).change(function (){
				$('.filename', self).text($(this).val());
			});
		});

		$('input[type=file]').on('change', prepareUpload);
	}).on('keydown', '.list-adr-email .line-header [type=text]', function( evt ){
		var evt = evt || window.event;

		if( evt.keyCode == '13' ){
			if( $.trim( $(this).val() ) == '' ){
				$('#subject').focus();
				return false;
			}

			if( $(this).attr('id') == $('.list-adr-email .line-header [type=text]:last').attr('id') ){
				var html = $(this).parent().html();
				html = html.replace( $(this).attr('id'), 'email-' + nbAdr );

				$(this).parent().after( '<div class="line-header">' + html + '</div>' );

				$('#email-' + nbAdr).val('');
				$('#email-' + nbAdr).parent().find('option:selected').removeAttr('selected');
				initAutoComplete( $('#email-' + nbAdr) );
				nbAdr++;
			}

			$(this).parent().next().find('input').focus();
			return false;
		}else if( evt.keyCode == '8' ){
			if( $.trim($(this).val()) == '' ){
				if( $(this).attr('id') != $('.list-adr-email .line-header [type=text]:first').attr('id') ){
					$(this).parent().prev().find('input').focus();
					$(this).parent().remove();
					return false;
				}
			}
		}else{
			if( $.trim($(this).val()) != '' ){
				if( !$(this).parent().find('option:selected').length || $.trim($('#type-adr-1').find('option:selected').val()) == '' ){
					$(this).parent().find('option[value=to]').attr('selected', 'selected');
				}
			}
		}
	}).on( 'click', '.list-file .del', function(){
		var nameFile = $(this).parent().find('.name').html();
		if( $.trim(nameFile) != '' ){
			$.ajax({
				url: '/admin/moderation/popup-contact.php?del-file=' + nameFile,
				type: 'GET',
				dataType: 'json',
				success: function( data ){
					updateListFiles( data );
				}
			});
		}
	});
}
<?php

	require_once('http.inc.php');

	// Autorise la mise en cache pour une semaine car le fichier est coûteux (poids / temps de génération)
	http_cache_control( 604800 );

	header('Content-Type: text/javascript');

	// Variables de traduction globales
	print 'var msgLoading = "'. _('Chargement en cours...') .'";';

	{ // Traductions pour le fichier catalog/index.js
		print '
			const strShowCategory	= "'._('Afficher le contenu de cette catégorie').'";
			const strProductsPublished = "' . _('Produits publiés :') . '";
			const strNoSubCategories = "' . _('Aucune sous-catégorie') . '";
			const strMoveRoot = "' . _('Placer ici, à la racine du catalogue') . '";
			const strMoveHere = "' . _('Placer ici, dans cette catégorie') . '";
			const strGoBack = "'. _('Revenir à la catégorie parente') .'";
			const strAlreadyHere = "' . _('Les produits ou catégories à déplacer se trouvent déjà dans cette catégorie') . '";
		';
	}

	{ //Variable de langage pour le fichier tab-medias.js
		print '
			const tabMediaConfirmSupImg      = "'._('Etes vous sûr(e) de vouloir supprimer les images sélectionnées ?\n\nNote : cliquez sur les images pour les sélectionner. Un cadre rouge apparaît autour des images sélectionnées.').'";
			const tabMediaAlertSelectionImg  = "'._('Veuillez séléctionner les images que vous souhaitez supprimer.').'";
			const tabMediaConfirmSupTypeImg  = "'._('Souhaitez vous vraiment supprimer ce type d\'image ? Ceci entraînera la suppression de la globalité des contenus associés à ce type.').'";
			const tabMediaCaptionImageLiee   = "'._('Images liées').'";
			const tabMediaLibelleTypeImg     = "'._('Libellé du type d\'image').'";
			const tabMediaMsgErreurParam     = "'._('Erreur de paramètres.').'";
			const tabMediaMsgErreurTitre     = "'._('Veuillez renseigner le titre d\'un type d\'image.').'";
			const tabMediaMsgErreurTypeImg   = "'._('Le type d\'image ne doit pas dépasser 45 caractères.').'";
			const tabMediasAucuneImagePublie = "'._('Aucune image publiée').'";
		';
	}

	{//Variable de langage pour le fichier ria-sortable.js
		print '
			const riaSortableErreurTable                = "'._('Le nombre de tables doit être égal à 1 !').'";
			const riaSortableAlertErreurEnregistremzent = "'._('Une erreur inattendue est survenue lors de l\'enregistrement de la position. Veuillez réessayer ou prendre contact avec nous.').'";
		';
	}

	{//Variable de langage pour le fichier yuto/index.js
		print '
			const fdvDisplayPopupExportRapport    = "'._('Exporter les rapports d\'appels').'";
			const fdvDisplayPopuplocalisation     = "'._('Dernière localisation connue').'";
			const fdvConfirmValideSuppression    = "'._('Vous êtes sur le point de supprimer les appareils sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const fdvDisplayPopupChoixComptClient = "'._('Choisir un compte client ').'";
			const fdvConfirmSuppression           = "'._('Vous êtes sur le point de supprimer cet appareil.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
		';
	}

	{//Variable de langage pour le fichier periode.js
		print '
			const periodeA                  = "'._('à').'";
			const periodeAucunHoraire       = "'._('Aucun horaire').'";
			const periodeTouteJournee       = "'._('Toute la journée').'";
			const periodeEnregistrer        = "'._('Enregistrer').'";
			const periodeEnregistrerModif   = "'._('Enregistrer les modifications').'";
			const periodeAnnnuleModif       = "'._('Annuler les modifications').'";
			const periodeEditer             = "'._('Editer').'";
			const periodeEditerInformations = "'._('Editer les informations concernant cette fermeture exceptionnelle').'";
			const periodeSupprimer          = "'._('Supprimer').'";
			const periodeSupprimerFermeture = "'._('Supprimer cette fermeture exceptionnelle').'";
			const periodeConfirmSuppression = "'._('Vous êtes sur le point de supprimer une fermeture exceptionnelle.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ? ').'";
		';
	}


	{//Variable de langage pour le fichier yuto/prize.js
		print '
			const prizeErreurChargement    = "'._('Une erreur est survenue lors du chargement du palmarès').'";
			const prizeAfficheRepresentant = "'._('Afficher tous les représentants').'";
		';
	}

	{//Variable de langage pour le fichier assistant.js
		print '
			const assistantConfirmSupression         = "'._('Vous êtes sur le point de supprimer cet assistant.\nEtes vous sûr de vouloir continuer ?').'";
			const assistantAlertTitre                 = "'._('Veuillez indiquer le titre de l\'assistant.').'";
		';
	}


	{//Variable de langage pour le fichier tools/banners.js
		print '
			const bannersConfirmSupressionList               = "'._('Vous êtes sur le point de supprimer les bannières sélectionnées.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const bannersConfirmSupression                   = "'._('Vous êtes sur le point de supprimer cette bannière.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const bannersAlertTitre                          = "'._('Le nom de la bannière est obligatoire et ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const bannersAlertDateDebut                      = "'._('La date de début de publication de la bannière n\'est pas dans un format reconnu.\nVeuillez utiliser une date au format jj/mm/aaaa.').'";
			const bannersAlertHeureDebut                     = "'._('L\'heure de début de publication de la bannière n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.') . 		'";
			const bannersAlertDateFin                        = "'._('La date de fin de publication de la bannière n\'est pas dans un format reconnu.\nVeuillez utiliser une date au format jj/mm/aaaa.').'";
			const bannersAlertHeureFin                       = "'._('L\'heure de fin de publication de la bannière n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.').' 		";
			const bannersAlertChampsAlternatif               = "'._('Le texte alternatif de la bannière est obligatoire et ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const bannersDisplayPopup                        = "'._('Rechercher un produit').'";
		';
	}


	{//Variable de langage pour le fichier catalog-comparator.js
		print '
			const catalogComparatorDesactiverexport            = "'._('Désactiver son export').'";
			const catalogComparatorActiverexport               = "'._('Activer son export').'";
			const catalogComparatorNon                         = "'._('Non').'";
			const catalogComparatorEnregistrer                 = "'._('Enregister').'";
			const catalogComparatorAnnuler                     = "'._('Annuler').'";
			const catalogComparatorHtmlEnchere                 = "'._('Aucune enchère directe<br> #param[tarif]# € récupéré selon son export.').'";
			const catalogComparatorDisplayPopupChoixFamille    = "'._('Choisissez une famille du comparateur').'";
			const catalogComparatorModifier                    = "'._('Modifier').'";
			const catalogComparatorDisplayPopupSurchargeInfos  = "'._('Surcharge des informations exportées').'";
			const catalogComparatorConfirmSuppressionSurcharge = "'._('Attention, cette action supprimera la surcharge directe pouvant être en place sur le titre ou la description. \nSouhaitez-vous poursuivre ?').'";
			const catalogComparatorDisplayPopupInfoProduit     = "'._('Saisissez l\'information pour les produits').'";
			const catalogComparatorRemoveOption    			 = "'._('Retirer cette option').'";
		';
	}


	{//Variable de langage pour le fichier catalog/index.js
		print '
			const catalogDisplayPopupExportProduit   = "'._('Exporter les produits').'";
			const catalogDisplayPopupExportCategorie = "'._('Exporter les catégories').'";
			const catalogLabelMarque                 = "'._('Aucune marque sélectionnée').'";
			const catalogLabelValeur                 = "'._('Aucune valeur sélectionnée').'";
			const catalogExportEnCours               = "'._('Votre export est en cours de préparation, veuillez patienter...').'";
			const catalogTelechargementPret          = "'._('Votre export est prêt à être téléchargé').' :";
			const catalogTelecharger                 = "'._('Télécharger').'";
		';
	}


	{//Variable de langage pour le fichier catalog/categories.js
		print '
			const categoriesDisplayPopupZone = "'._('Définir des zones cliquables').'";
			const categoriesOffreFidelite    = "'._('Aucune offre de fidélité').'";
		';
	}


	{//Variable de langage pour le fichier catalog/category.js
		print '
			const categoryEnregistrer           = "'._('Enregistrer').'";
			const categoryAnnuler               = "'._('Annuler').'";
			const categorySupprimer             = "'._('Supprimer').'";
			const categoryConfirmSuppressionUrl = "'._('Vous êtes sur le point de supprimer Une URL de redirection.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ? ').'";
			const categorySucceSupressionUrl    = "'._('La suppression de l\'url de redirection s\'est correctement passée.').'";
			const categoryMajUrl                = "'._('La mise à jour de l\'url de redirection s\'est correctement déroulée.').'";
			const categoryAjoutUrl              = "'._('L\'ajout de l\'url de redirection s\'est correctement déroulé.').'";
		';
	}

	{//Variable de langage pour le fichier config/cgv.js
		print '
			const cgvConfirmSupressionListe                 = "'._('Vous êtes sur le point de supprimer les versions sélectionnées.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?\n\nNote : les versions déjà publiées seront conservées pour des raisons légales.').'";
			const cgvConfirmSupression                      = "'._('Vous êtes sur le point de supprimer cette version.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const cgvAlertNomVide                           = "'._('Le nom de la version ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const cgvAlertPublication                       = "'._('Une fois publiée, cette version sera visible sur le site et ne pourra plus être modifiée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const cgvAlertSuppressionArticle                = "'._('Vous êtes sur le point de supprimer cet article.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const cgvAlertListeSuppressionArticle           = "'._('Vous êtes sur le point de supprimer les articles sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const cgvAlertTitre                             = "'._('Le titre de l\'article ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const cgvAlertContenu                           = "'._('Le contenu de l\'article ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
		';
	}


	{//Variable de langage pour le fichier tools/cms.js
		print '
			const cmsConfirmRestaurer           = "'._('Êtes-vous sûr de vouloir restaurer la révision suivante').'";
			const cmsAlertSuppressionImage      = "'._('Une erreur à eu lieu, veuillez réessayer ou prenez contact avec nous.').'";
			const cmsSupprimer                  = "'._('Supprimer').'";
			const cmsDiplayPopupZonesCliquables = "'._('Définir des zones cliquables').'";
			const cmsAnnuler                    = "'._('Annuler').'";
			const cmsEnregistrer                = "'._('Enregistrer').'";
			const cmsAlertSuppressionUrl        = "'._('Vous êtes sur le point de supprimer Une URL de redirection.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) devouloi r continuer ? ').'";
			const cmsSuccesSuppressionUrl       = "'._('La suppression de l\'url de redirection s\'est correctement passée.').'";
			const cmsSuccesMajUrl               = "'._('La mise à jour de l\'url de redirection s\'est correctement déroulée.').'";
			const cmsSuccesAjoutUrl             = "'._('L\'ajout de l\'url de redirection s\'est correctement déroulé.').'";
		';
	}


	{//Variable de langage pour le fichier comparators/search.js
		print '
			const comparatorSearchExportPret                              = "'._('Votre export est prêt à être téléchargé').'";
			const comparatorSearchConfirmActivationProduitMarche         = "'._('Attention ! Cette action va activer les produits sélectionnés sur toutes les places de marché. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorSearchConfirmActivationProduitComparateur    = ". '._('Attention ! Cette action va activer les produits sélectionnés sur tous les comparateurs de prix. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorSearchConfirmDesactivationProduitMarche      = "'._('Attention ! Cette action va désactiver les produits sélectionnés sur toutes les places de marché. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorSearchConfirmDesactivationProduitComparateur = ". '._('Attention ! Cette action va désactiver les produits sélectionnés sur tous les comparateurs de prix. Êtes-vous sur de vouloir continuer ? ').'";
			const comparatorSearchSelectCondition                         = "'._('Veuillez sélectionner une condition.').'";
			const comparatorSearchDisplayPopupSaveRecherche               = "'._('Sauvegarder votre recherche').'";
			const compartorSearchActiveExport                             = "'._('Activer l\'exportation de ces produits vers ').'";
			const compartorSearchComparateur                              = "'._('le comparateur de prix').'";
			const compartorSearchPlaceMarche                              = "'._('la place de marché').'";
			const compartorSearchDesactiveExport                          = "'._('Désactiver l\'exportation de ces produits vers ').'";
			const comparatorSearchProduits                                = "'._('Produits').'";
			const compartorSearchDesigantion                              = "'._('Désignation').'";
			const compartorSearchClics                                    = "'._('Clics').'";
			const compartorSearchCout                                     = "'._('Coût').'";
			const compartorSearchVentes                                   = "'._('Ventes').'";
			const compartorSearchTauxTransformation                       = "'._('Taux de transformation').'";
			const conparatorSearchtransfo                                 = "'._('Transfo.').'";
			const comparatorSearchCoutVente                               = "'._('Coût/Vente').'";
			const comparatorSearchCAHT                                    = "'._('Chiffre d\'affaires Hors Taxes').'";
			const comparatorSearchCATTC                                   = "'._('Chiffre d\'affaires Toutes Taxes Comprises').'";
			const comparatorSearchCAHTSmall                               = "'._('CA HT').'";
			const comparatorSearchCATTCSmall                              = "'._('CA TTC').'";
			const comparatorSearchMarge                                   = "'._('Marge').'";
			const comparatorSearchROI                                     = "'._('Retour sur investissement').'";
			const comparatorSearchROISmall                                = "'._('ROI').'";
			const comparatorSearchecporte                                 = "'._('Exporté').'";
			const comparatorSearchExporter                                = "'._('Exporter').'";
			const comparatorSearchNePasExporter                           = "'._('Ne plus exporter').'";
			const comparatorSearchTelecharger                             = "'._('Télécharger').'";
			const comparatorSearchTelechargerCSV                          = "'._('Télécharger ce tableau au format CSV').'";
			const comparatorSearchChargementRecherche                     = "'._('Recherche en cours, veuillez patienter...').'";
			const comparatorSearchOui                                     = "'._('Oui').'";
			const comparatorSearchNon                                     = "'._('Non').'";
			const comparatorSearchPartiel                                 = "'._('Partiel').'";
			const comparatorSearchRechercheVide                           = "'._('Aucun produit ne correspond à vos critères de recherche').'";
			const comparatorSearchDisplayPopupGestionExport               = "'._('Gestion de l\'export').'";
		';
	}


	{//Variable de langage pour le fichier comparators/stats.js
		print '
			const comparatorStatsConfirmActiveProduitMarche   = "'._('Attention ! Cette action va activer les produits sélectionnés sur toutes les places de marché. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorStatsConfirmActiveProduitComprateurt   = "'._('Attention ! Cette action va activer les produits sélectionnés sur tous les comparateurs de prix. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorStatsConfirmDesactiveProduitMarche = "'._('Attention ! Cette action va désactiver les produits sélectionnés sur toutes les places de marché. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorStatsConfirmDesactiveProduitCompareteur = "'._('Attention ! Cette action va désactiver les produits sélectionnés sur tous les comparateurs de prix. Êtes-vous sur de vouloir continuer ?').'";
			const comparatorStatsTotaux                   = "'._('Totaux').'";
			const comparartorStatsProduit                 = "'._('Produits').'";
			const comparatorStatsDesignation              = "'._('Désignation').'";
			const comparatorStatsClics                    = "'._('Clics').'";
			const comparatorStatsCout                     = "'._('Coût').'";
			const comparatorStatsVentes                   = "'._('Ventes').'";
			const comparatorStatsTauxTransformation       = "'._('Taux de transformation').'";
			const comparatorStatshtransfo                 = "'._('Transfo.').'";
			const comparatorStatshCoutVente               = "'._('Coût/Vente').'";
			const comparatorStatsCAHTTitle                = "'._('Chiffre d\'affaires Hors Taxes').'";
			const comparatorStatsCATTCTitle               = "'._('Chiffre d\'affaires Toutes Taxes Comprises').'";
			const comparatorStatsCAHT                     = "'._('CA HT').'";
			const comparatorStatsCATTC                    = "'._('CA TTC').'";
			const comparatorStatshMarge                   = "'._('Marge').'";
			const comparatorStatshROI                     = "'._('Retour sur investissement').'";
			const comparatortatshROISmall                 = "'._('ROI').'";
			const comparatortatshExporte                  = "'._('Exporté').'";
			const comparatortatshExporter                 = "'._('Exporter').'";
			const comparatorStatsPlaceMarche              = "'._('la place de marché').'";
			const comparatorStatsComparateur              = "'._('le comparateur de prix').'";
			const comparartorStatsActiveExportProduit     = "'._('Activer l\'exportation de ces produits vers ').'";
			const comparartorStatsDesactiveExportProduit  = "'._('Désactiver l\'exportation de ces produits vers ').'";
			const comparatorStatsNePasExporter            = "'._('Ne pas exporter').'";
			const comparatorStatsOui                      = "'._('Oui').'";
			const comparatorStatsNon                      = "'._('Non').'";
			const comparatorStatsPartiel                  = "'._('Partiel').'";
		';
	}


	{//Variable de langage pour le fichier customers/index.js
		print '
			const customersDisplayPopupMdpPerdu           = "'._('Mot de passe perdu').' - '._('choix du modèle de notification').'";
			const customersDisplayPopupAddressEdit        = "'._('Modification d\'une adresse').'";
			const customersDisplayPopupAddressEditDlv     = "'._('Modification de l\'adresse de livraison').'";
			const customersDisplayPopupAddressEditInv     = "'._('Modification de l\'adresse de facturation').'";
			const customersDisplayPopupEditListePerso     = "'._('Ajouter une liste personnalisée').'";
			const customersDisplayPopupContenuListePerso  = "'._('Contenu de la liste personnalisée').'";
			const customersDisplayPopupSelectRepresentant = "'._('Sélection d\'un représentant').'";
			const customersDisplayPopupAffecterClient     = "'._('Choix d\'un client à affecter à ce représentant').'";
			const customersconfimDetacherRepresentatn     = "'._('Êtes-vous sûr de vouloir détacher le représentant associé à ce client ?').'";
			const customersDisplayPopupExportClient       = "'._('Exporter la liste des clients').'";
			const customersDisplayPopupGestionFidelite    = "'._('Gestion des points de fidélité').'";
		';
	}


	{//Variable de langage pour le fichier default.js
		print '
			const defaultConfirnSuppressionProduitCmd = "'._('Êtes-vous sûr de vouloir supprimer les produits suivants de la commande ?').'";
		';
	}

	{//Variable de langage pour le fichier div-zones.js
		print '
			const divZonesExceptions         = "'._('Exceptions').'";
			const divZonesSymboleDescription = "'._('(le symbole <tt>+</tt> indique des produits inclus dans la zone, le symbôle <tt>-</tt> des produits exclus.)').'";
			const divZonesSupprimer          = "'._('Supprimer').'";
			const divZonesNoException        = "'._('Aucune exception n\'est enregistrée pour le moment.').'";
			const divZonesSelect             = "'._('Veuillez choisir un produit, une catégorie ou une marque.').'";
		';
	}


	{//Variable de langage pour le fichier documents/types.js
		print '
			const documentsTypesAlertDesignationObg                = "'._('La désignation du type de document est obligatoire.\nVeuillez la renseigner.').'";
			const documentsTypesConfirmSuppression                 = "'._('Vous êtes sur le point de supprimer ce type de document.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const documentsTypesConfirmSuppressionMultiple         = "'._('Vous êtes sur le point de supprimer ce type de document.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier documents/index.js
		print '
			const documentsDisplayPopupaddLienObjet                   = "'._('Créer un lien vers un nouvel objet').'";
			const documentErreurMsgSelectDocument                     = "'._('Veuillez sélectionner un document.').'";
			const documentsAfficherFiche                              = "'._('Afficher la fiche de ce document').'";
			const documentsAnnuler                                    = "'._('Annuler').'";
			const documentNouveau                                     = "'._('Nouveau document').'";
			const documentsAlertChampsNom                             = "'._('Veuillez indiquer le nom du document').'";
			const documentAlertChampsFichier                          = "'._('Veuillez sélectionner le fichier à télécharger.').'";
			const documentsConfirmSupressionDocument                 = "'._('Vous êtes sur le point de supprimer ce document.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const documentsConfirmSupressionMultipleDocument         = "'._('Vous êtes sur le point de supprimer un ou plusieurs documents.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const documentsConfirmSupressionTypeDocument             = "'._('Vous êtes sur le point de supprimer un ou plusieurs types de document.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const documentSelectEmplacement                           = "'._('Veuillez sélectionner un nouvel emplacement.').'";
		';
	}


	{//Variable de langage pour le fichier erratum.js
		print '
			const erratumconfirmSuppressionMultiple         = "'._('Vous êtes sur le point de supprimer les erratums sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const erratumconfirmSuppression                 = "'._('Vous êtes sur le point de supprimer cet erratum.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const erratumAlertRefProduit                    = "'._('Veuillez indiquer la référence du produit concerné par l\'erratum.').'";
			const erratumAlertCommetnaire                   = "'._('Veuillez indiquer votre commentaire.').'";
		';
	}


	{//Variable de langage pour le fichier config/errors-404.js
		print '
			const errorsVerificationUrl             = "'._('Vérification des urls en cours, merci de patientez...').'";
			const errorsPagePrec                    = "'._('Page précédente').'";
			const errorsPageSuiv                    = "'._('Page suivante').'";
			const errorsCaptionResultat             = "'._('Liste des résultats').'";
			const errorsNoResult                    = "'._('Aucun résultat n\'a été trouvé.').'";
			const errorsContenuSubstitution         = "'._('Cliquez dessus pour choisir ce contenu comme substitution.').'";
			const errorsChoisir                     = "'._('Choisir').'";
			const errors404                         = "'._('Aucune erreur 404 ne correspond à votre recherche').'";
			const errorsRechercheContenu            = "'._('Rechercher un contenu sur le site pouvant faire office de redirection').'";
			const errorsUrl404                      = "'._('URLs ayant généré une ou plusieurs erreurs 404').'";
			const errorsRechecheContenuSubstitution = "'._('Rechercher un contenu de substitution').'";
			const errorsPremiereOccurence           = "'._('Première occurence').'";
			const errorsDerniereOccurence           = "'._('Dernière occurence').'";
			const errorsNombreOccurence             = "'._('Nombre d\'occurence').'";
			const errorsAll404                      = "'._('Toutes les erreurs 404').'";
			const errorsPages                       = "'._('Pages').'";
			const errorsImages                      = "'._('Images').'";
			const errorsAutres                      = "'._('Autres').'";
		';
	}


	{//Variable de langage pour le fichier expedition.js
		print '
			const expeditionsAucuneExp                   = "'._('Aucune expédition').'";
			const expeditionsTouteJournee                = "'._('Toute la journée').'";
			const expeditionsEnregistrer                 = "'._('Enregistrer').'";
			const expeditionsAnnuler                     = "'._('Annuler').'";
			const expeditionsSupprimer                   = "'._('Supprimer').'";
			const expeditionsEnregistrerModif            = "'._('Enregistrer les modifications').'";
			const expeditionsAnnulerModif                = "'._('Enregistrer les modifications').'";
			const expeditionsEditerFermeture             = "'._('Editer les informations concernant cette fermeture exceptionnelle').'";
			const expeditionsSupprimerFermeture          = "'._('Supprimer cette fermeture exceptionnelle').'";
			const expeditionsConfirmSuppressionFermeture = "'._('Vous êtes sur le point de supprimer une fermeture exceptionnelle.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier tools/faq.js
		print '
			const faqAlertChampsCategorie                     = "'._('Le nom de la catégorie ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const faqAlertChampsIntitule                      = "'._('L\'intitulé de la question ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const faqAlertChampsReponse                       = "'._('La réponse à la question ne peut pas être laissé vide.\nVeuillez l\'indiquer.').'";
			const faqAlertSuppressionCategorie                = "'._('Par mesure de précaution, seules les catégories ne contenant plus de questions peuvent être supprimées.\nSi vous souhaitez réellement supprimer cette catégorie, veuillez tout d\'abord supprimer les questions qu\'elle contient.').'";
			const faqConfirmSuppressionCategorieListe         = "'._('Vous êtes sur le point de supprimer les catégories sélectionnées.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?\n\nNote : par mesure de précaution, seules les catégories vides seront supprimées.').'";
			const faqConfirmsuppressionQuestionListe          = "'._('Vous êtes sur le point de supprimer les questions sélectionnés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const faqConfirmsuppressionQuestion               = "'._('Vous êtes sur le point de supprimer cette question.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier fields.js
		print '
			const fieldsDiplayPopupSelectObjet                       = "'._('Sélection un objet de rattachement').'";
			const fieldsOptionSelectCategorie                        = "'._('Veuillez sélectionner la catégorie de classement').'";
			const fieldsSelectProduit                                = "'._('Selectionner un produit').'";
			const fieldsSelectCategorie                              = "'._('Sélectionner une catégorie').'";
			const fieldsSelectMarque                                 = "'._('Sélectionner une marque').'";
			const fieldsSelectMagasin                                = "'._('Sélectionner un magasin').'";
			const fieldsConfirmSuppressionUniteMesure                = "'._('Vous êtes sur le point de supprimer cette unité de mesure.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionUniteMesureMultiple        = "'._('Vous êtes sur le point de supprimer une ou plusieurs unités de mesure.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionClassMultiple              = "'._('Vous êtes sur le point de supprimer une ou plusieurs classes.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionObjetMultiple              = "'._('Vous êtes sur le point de supprimer un ou plusieurs objets.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionClasse                     = "'._('Vous êtes sur le point de supprimer cette classe.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsconfirmSuppressionObjet                      = "'._('Vous êtes sur le point de supprimer cet object.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmMsgReinitialistaionUniteMesure     = "'._('Etes vous sur de vouloir effacer les valeurs stockées pour les champs sélectionnés ?').'";
			const fieldsConfirmNomUniteMesure                        = "'._('Veuillez indiquer le nom de l\'unité de mesure.') .'"
			const fieldsConfirmSymboleUniteMesure                    = "'._('Veuillez indiquer le symbole de l\'unité de mesure.') .'"
			const fieldsConfirmSuppressionCategorie                  = "'._('Vous êtes sur le point de supprimer cette catégorie.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionCategorieMultiple          = "'._('Vous êtes sur le point de supprimer une ou plusieurs catégories.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsAlertEditionCategorie                        = "'._('Veuillez indiquer le nom de la catégorie.').'";
			const fieldsConfirmSuppressionModeleSaisie               = "'._('Vous êtes sur le point de supprimer ce modèle de saisie.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsConfirmSuppressionModeleSaisieMultiple       = "'._('Vous êtes sur le point de supprimer un ou plusieurs modèles.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsAlertEditionModeleSaisie                     = "'._('Veuillez indiquer le nom du modèle de saisie.').'";
			const fieldsChoixChamps                                  = "'._('Choisir un ou plusieurs champs').'";
			const fieldsConfirmSuppressionChampsPersonnalise         = "'._('Vous êtes sur le point de supprimer ce champ personnalisé.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ? ').'";
			const fieldsConfirmSuppressionChampsPersonnaliseMultiple = "'._('Vous êtes sur le point de supprimer un ou plusieurs champs personnalisés.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsAlertEditionChampsPersonnaliseNom            = "'._('Veuillez indiquer le nom du champ personnalisé.').'";
			const fieldsAlertEditionChampsPersonnaliseType           = "'._('Veuillez indiquer le type du champ personnalisé.').'";
			const fieldsAlertEditionChampsPersonnaliseCategorie      = "'._('Veuillez indiquer la catégorie du champ personnalisé.').'";
			const fieldsAlertMinEntier                               = "'._('La longueur minimum n\'est pas reconnue comme valide.\nVeuillez indiquer un nombre entier dans ce champ.').'";
			const fieldsAlertMaxEntier                               = "'._('La longueur maximum n\'est pas reconnue comme valide.\nVeuillez indiquer un nombre entier dans ce champ.').'";
			const fieldsAlertMinSupMaxEntier                         = "'._('La longueur minimum est supérieure à la longueur maximum.\nVeuillez indiquer une limite minimum qui soit inférieure à la limite maximum.').'";
			const fieldsAlertMinFlottant                             = "'._('La valeur minimum n\'est pas reconnue comme valide.\nVeuillez indiquer un nombre à virgule flottante dans ce champ.').'";
			const fieldsAlertMinSupMaxFlottant                       = "'._('La valeur minimum est supérieure à la valeur maximum.\nVeuillez indiquer une limite minimum qui soit inférieure à la limite maximum.').'";
			const fieldsAlertPrecisionNonValide                      = "'._('La précision n\'est pas reconnue comme valide.\nVeuillez indiquer un nombre entier dans ce champ.').'";
			const fieldsModifValeurAutorise                          = "'._('Modifier les valeurs autorisées').'";
			const fieldsTraductionValeur                             = "'._('Traduction des valeurs').'";
			const fieldsConfirmSuppressionValeurAutorise             = "'._('Vous êtes sur le point de supprimer un ou plusieurs valeurs autorisées.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const fieldsLongeurMin                                   = "'._('Longueur minimum').'";
			const fieldsLongeurMax                                   = "'._('Longueur maximum').'";
			const fieldsValeurMin                                    = "'._('Valeur minimum').'";
			const fieldsValeurMax                                    = "'._('Valeur maximum').'";
			const fieldsConfirmSuppressionImage                      = "'._('Êtes-vous sûr de vouloir supprimer cette image ?').'";
			const fieldsErreurSuppressionImage                       = "'._('Une erreur inattendue s\'est produite lors de la suppression de l\'image. \nVeuillez réessayer ou bien prendre contact avec l\'administrateur').'";
		';
	}


	{//Variable de langage pour le fichier gift.js
		print '
			const giftDisplayPopupREchercheProduit = "'._('Rechercher un produit').'";
			const giftAucunProduitCadeau           = "'._('Aucun produit n\'est identifié comme étant une carte cadeau').'";
			const giftMsgSuccesSupression          = "'._('La suppression s\'est correctement déroulée.').'";
			const giftMsgErreurSupression          = "'._('Une erreur inattendue s\'est prouite lors de la suppression.').'";
			const giftMsgSuccesEnregistrement      = "'._('L\'enregistrement s\'est correctement déroulé.').'";
			const giftMsgErreurEnregistrement      = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement.').'";
		';
	}

	{//Variable de langage pour le fichier documents/images.js
		print '
			const imagesPagePrec                = "'._('Page précédente ').'";
			const imagesPageSuiv                = "'._('Page suivante').'";
			const imagesMsgImageNonVisible      = "'._('L\'image ne sera plus visible dans l\'association automatique.').'";
			const imagesConfirmSuppressionImage = "'._('Vous êtes sur le point de supprimer définitivement cette image, voulez-vous continuez ?').'";
			const imagesMsgSuccesSuppression    = "'._('L\'image a bien été supprimée.').'";
			const imagesMsgSuccesEnregistrement = "'._('L\'enregistrement s\'est correctement déroulé.').'";
		';
	}

	$upload_mb = 64;
	{//Variable de langage pour le fichier tools/imports.js
		print '
			const importsMasquerOption              = "'._('Masquer les options avancées').'";
			const importsAfficherOption             = "'._('Afficher les options avancées').'";
			const importsSeparateur                 = "'._('Séparateur').' :";
			const importsUrlFichier                 = "'._('Url du fichier').'";
			const importsServeurFTP                 = "'._('Serveur FTP').'";
			const importsNomUtilisatreur            = "'._('Nom d\'utilisateur').'";
			const importsMdp                        = "'._('Mot de passe').'";
			const importsCheminFichier              = "'._('Chemin vers le fichier').'";
			const importsSelectionFichier           = "'._('Sélectionnez un fichier').'";
			const importsErreurContenu              = "'._('le contenu de votre import').'";
			const importsErreurMethodeRecuperation  = "'._('la méthode de récupération du fichier').'";
			const importsErreurNomFichier           = "'._('un nom de fichier').'";
			const importsErreurUrl                  = "'._('une url').'";
			const importsErreurUtilisateur          = "'._('un nom d\'utilisateur').'";
			const importsErreurMdp                  = "'._('un mot de passe').'";
			const importsErreurFichierImporter      = "'._('un fichier à importer').'";
			const importsErreurPeriodeRecurrence    = "'._('une période de récurrence d\'importation').'";
			const importsErreurSeprateur            = "'._('un séparateur de colonne').'";
			const importsErreurCaracteresSeparateur = "'._('un caractère séparateur de colonne.').'";
			const importsErreurActionImport         = "'._('une action à réaliser lors de l\'import').'";
			const importsTransfertEnCours           = "'._('Transfert de votre fichier en cours...').'";
			const importsTransfertTermine           = "'._('Transfert terminé. Préparation de l\'import...').'";
			const importsErreurTelechargement       = "'._('Une erreur est survenue lors du téléchargement de votre fichier').'";
			const importsManque                     = "'._('Il manque').'";
			const importsErreurAssociationUtilise   = "'._('Cette association est déjà utilisée pour une autre colonne. Merci de vérifier.').'";
			const importsA                          = "'._('à').'";
			const importsLe                         = "'._('le').'";
			const importsLundi                      = "'._('Lundi').'";
			const importsMardi                      = "'._('Mardi').'";
			const importsMercredi                   = "'._('Mercredi').'";
			const importsJeudi                      = "'._('Jeudi').'";
			const importsVendredi                   = "'._('Vendredi').'";
			const importsSamedi                     = "'._('Samedi').'";
			const importsDimanche                   = "'._('Dimanche').'";
			const importDragAndDropText             = "'._('Déposez votre fichier ci-dessous par un simple glissé-déposé').'";
			const importEnCours             		  	= "'._('Import en cours').'";
			const importSuccess               	  	= "'._('Import terminé').'";
			const importError             		  	= "'._('Erreur pendant l\'import').'";
			const importChooseAction				= "'._('Choisir une action').'";
			const importMaxSize             		= "' . str_replace('[taille max]', $upload_mb, _('[taille max]Mo max, formats XLS, XLSX ou CSV acceptés')).'";
			const importLinkVals					= "'._('correspond à').'";
			const importLinkFile					= "'._('dans votre fichier').'";
			const importUseTo						= "'._('Est utilisé pour :').'";
			const importUncomplete					= "'._('Il vous reste [nb] champ avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importUncompletePlural			= "'._('Il vous reste [nb] champs avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importNotSelected					= "'._('Il vous reste [nb] champ avec une correspondance à établir avant de pouvoir importer votre fichier').'";
			const importNotSelectedPlural			= "'._('Il vous reste [nb] champs avec une correspondance à établir avant de pouvoir importer votre fichier').'";
			const importUncompleteComp				= "'._('Vous ne pouvez pas encore importer votre fichier').'";
			const importUncompleteAll1				= "'._('Il vous reste [nb1] champs avec une correspondance à établir<br />et [nb2] champs avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importUncompleteAll2				= "'._('Il vous reste [nb1] champs avec une correspondance à établir<br />et [nb2] champ avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importUncompleteAll3				= "'._('Il vous reste [nb1] champ avec une correspondance à établir<br />et [nb2] champs avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importUncompleteAll4				= "'._('Il vous reste [nb1] champ avec une correspondance à établir<br />et [nb2] champ avec une action à effectuer avant de pouvoir importer votre fichier').'";
			const importMappingIsComplete			= "'._('Toutes les correspondances sont faites, vous pouvez importer votre fichier !').'";
			const importAddValCorrespondence		= "'.('Ajouter une correspondance').'";
		';
	}


	{//Variable de langage pour le fichier imstagram.js
		print '
			const instagramRegenererToken = "'._('Regénérer le token').'";
		';
	}


	{//Variable de langage pour le fichier json.js
		print '
			const jsonClientSyncGestionCommerciale    = "'._('Ce compte client est synchronisé avec votre gestion commerciale').'";
			const jsonClientBoutique                  = "'._('Ce compte client n\'existe que dans votre boutique en ligne').'";
			const jsonCatSyncGestionCommerciale    	= "'._('Cette catégorie est synchronisée avec votre gestion commerciale').'";
			const jsonCatBoutique                  	= "'._('Cette catégorie n\'existe que dans votre boutique en ligne').'";
			const jsonProduitSyncGestionCommerciale   = "'._('Ce produit est synchronisé avec votre gestion commerciale').'";
			const jsonProduitBoutique                 = "'._('Ce produit n\'existe que dans votre boutique en ligne').'";
			const jsonRelationSyncGestionCommerciale  = "'._('Cette relation est synchronisée avec votre gestion commerciale').'";
			const jsonRelationBoutique                = "'._('Cette relation n\'existe que dans votre boutique en ligne').'";
			const jsonMagasinSyncGestionCommerciale   = "'._('Ce magasin est synchronisé avec votre gestion commerciale').'";
			const jsonMagasinBoutique                 = "'._('Ce magasin n\'existe que dans votre boutique en ligne').'";
			const jsonPanierSyncGestionCommerciale    = "'._('Ce panier est synchronisé avec votre gestion commerciale').'";
			const jsonPanierBoutique                  = "'._('Ce panier n\'existe que dans votre boutique en ligne').'";
			const jsonCommandeSyncGestionCommerciale  = "'._('Cette commande est synchronisée avec votre gestion commerciale').'";
			const jsonCommandeBoutique                = "'._('Cette commande n\'existe que dans votre boutique en ligne').'";
			const jsonPagePrec                        = "'._('Page précédente').'";
			const jsonPageSuiv                        = "'._('Page suivante').'";
		';
	}


	{//Variable de langage pour le fichier config/livraison.js
		print '
			const livraisonQte                                          = "'._('une quantité supérieure ou égale à').'";
			const livraisonWeight                                       = "'._('un poids brut supérieur ou égal à').'";
			const livraisonWeightNet                                    = "'._('un poids net supérieur ou égal à').'";
			const livraisonHT                                           = "'._('un montant HT supérieur ou égal à').'";
			const livraisonTTC                                          = "'._('un montant TTC supérieur ou égal à').'";
			const livraisonSelectMagasin                                = "'._('Veuillez sélectionner un magasin.').'";
			const livraisonAlertNomZoneLivraison                        = "'._('Veuillez indiquer le nom de la zone de livraison.').'";
			const livraisonConfirmSuppressionZoneLivraison              = "'._('Vous êtes sur le point de supprimer cette zone de livraison.\nCette opération est irréversible et ne pourra pas être annulée.\nEes vou s sûr de vouloir continuer ? ').'";
			const livraisonConfirmSuppressionDepotStock                 = "'._('Vous êtes sur le point de supprimer ce dépôt.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr devouloi r continuer ? ').'";
			const livraisonConfirmSuppressionZonesLivraisons            = "'._('Vous êtes sur le point de supprimer une ou plusieurs zones de livraison.\nCette opération est irréversible et ne pourra as êtr e annulée.\nEtes vous sûr de vouloir continuer ? ').'";
			const livraisonAlertNomServiceLivraison                     = "'._('Veuillez indiquer le nom du service de livraison.').'";
			const livraisonConfirmotificationEditionLivraison           = "'._('Le message de notification que vous avez indiqué ne contient pas la valeur spéciale %nom%.\nLe nom et l\'adresse du site intrnet d u service de livraison ne seront donc pas disponibles.\nEtes vous sûr de vouloir continuer ? ').'";
			const livraisonConfirmSuppressionServiceLivraison           = "'._('Vous êtes sur le point de supprimer ce service de livraison.\nCette opération est irréversible et ne pourra pas être annulée.\nEes vou s sûr de vouloir continuer ? ').'";
			const livraisonConfirmSuppressionServicesLivraisons         = "'._('Vous êtes sur le point de supprimer un ou plusieurs services de livraison.\nCette opération est irréversible et ne pourra as êtr e annulée.\nEtes vous sûr de vouloir continuer ? ').'";
			const livraisonAlertLargeurColis                            = "'._('Veuillez indiquer la largeur du colis').'";
			const livraisonAlertLargeurColisInvalide                    = "' . addslashes(_('Veuillez indiquer un nombre entier valide dans le champ « Largeur »')).'";
			const livraisonAlertLargeurColisSupZero                     = "'._('La largeur du colis doit être supérieure à 0').'";
			const livraisonAlertLongeurColis                            = "'._('Veuillez indiquer la longueur du colis').'";
			const livraisonAlertLongeurColisInvalide                    = "' . addslashes(_('Veuillez indiquer un nombre entier valide dans le champ « Longueur »')).'";
			const livraisonAlertLongeurColisSupZero                     = "'._('La longueur du colis doit être supérieure à 0').'";
			const livraisonAlertHauteurColis                            = "'._('Veuillez indiquer la hauteur du colis').'";
			const livraisonAlertHauteurColisInvalide                    = "' . addslashes(_('Veuillez indiquer un nombre entier valide dans le champ « Hauteur »')).'";
			const livraisonAlertHauteurColisSupZero                     = "'._('La hauteur du colis doit être supérieure à 0').'";
			const livraisonAlertPrixColisInvalide                       = "' . addslashes(_('Veuillez indiquer un prix valide dans le champ « Prix »')).'";
			const livraisonAlertPrixColisSupZero                        = "'._('Le prix du colis doit être supérieur à 0').'";
			const livraisonConfirmSuppressionColis                      = "'._('Vous êtes sur le point de supprimer ce colis.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr devouloi r continuer ? ').'";
			const livraisonConfirmSupressionColisMulti                  = "'._('Vous êtes sur le point de supprimer un ou plusieurs colis.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const livraisonAlertNomMagasin                              = "'._('Le nom du magasin est obligatoire pour pouvoir l\'enregistrer.\nVeuillez l\'indiquer.').'";
			const livraisonAlertCategorieImage                          = "'._('Veuillez sélectionner au moins une catégorie qui décrive le contenu de l\'image.').'";
			const livraisonConfirmSuppressionImageMagasin               = "'._('Vous êtes sur le point de supprimer une ou plusieurs images associées à ce magasin.\nCette opération est irréversible et ne porra pa s être annulée.\nEtes vous sûr de vouloir continuer ? ').'";
			const livraisonDisplayPopupModifierSecteur                  = "'._('Editer la liste des secteurs').'";
			const LivraisonAucunSecteur                                 = "'._('Aucun secteur').'";
			const livraisonConfirmSuppressionMagasins                   = "'._('Vous êtes sur le point de supprimer un ou plusieurs magasins.\nCette opération est irréversible et ne pourra pas être annulée.\nEes vou s sûr de vouloir continuer ? ').'";
			const livraisonConfirmSuppressionMagasin                    = "'._('Vous êtes sur le point de supprimer ce magasin.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr devouloi r continuer ? ').'";
			const livraisonEditionMagasinErreur                         = "'._('Une erreur à eu lieu, veuillez réessayer ou prend contact avec nous.').'";
			const livraisonFerme                                        = "'._('Fermé').'";
			const livraisonTouteJournee                                 = "'._('Toute la journée').'";
			const lirvaisonGestionPlageHoraire                  		  = "'._('Gestion des plages horaires').'";
		';
	}


	{//Variable de langage pour le fichier tools/marketing.js
		print '
			const marketingSMSEnvoye       = "'._('Les SMS ont bien été envoyés').'";
			const marketingErreurSMS       = "'._('Une erreur est survenue durant l\'envoi des SMS.').'";
			const marketingRechercheClient = "'._('Rechercher un client').'";
			const marketingGardePage       = "'._('Veuillez garder cette page ouvert après avoir envoyé les SMS').'";
			const marketingEnvoyer         = "'._('Envoyer').'";
			const marketingCampagneManuel  = "'._('Votre campagne est en envoi manuel, vous pouvez l\'exécuter en cliquant sur le boutton \"Envoyer\". Attention l\'envoi des sms est irréversible et dois être réaliser entre 8h et 20h.').'";
			const marketingSelectDateEnvoi = "'._('Veuillez sélèctionner une date d\'envoi.').'";
		';
	}


	{//Variable de langage pour le fichier media.js
		print '
			const mediaRetirerChaine                      = "'._('Retirer cette chaine').'";
			const mediaNonPresenceChaine                  = "'._('Présent dans aucune chaine pour l\'instant').'";
			const mediaRetirerPlaylist                    = "'._('Retirer cette playlist').'";
			const mediaNonPresencePlaylist                = "'._('Présent dans aucune playlist pour l\'instant').'";
			const mediaConfirmSuppressionChaine           = "'._('Vous êtes sur le point de supprimer cette chaine.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) devouloi r continuer ? ').'";
			const mediaConfirmSuppressionPlaylist         = "'._('Vous êtes sur le point de supprimer cette playlist.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) devouloi r continuer ? ').'";
			const mediaConfirmSuppressionMedia            = "'._('Vous êtes sur le point de supprimer ce média.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ? ').'";
		';
	}


	{//Variable de langage pour le fichier metas.js
		print '
			const metasNbCaracteres              = "'._('Nombre de caractères').'";
			const metasNbMots                    = "'._('Nombre de mots').'";
			const metasInformations              = "'._('Information').'";
			const metasTitreCourt                = "'._('Le titre est trop court, ajoutez du texte.').'";
			const metasTitreLongeurCorrect       = "'._('Le titre a une longueur correcte pour le référencement').'";
			const metasTitreLong                 = "'._('Le titre est trop long, il apparaîtra coupé dans les résultats de recherche, nous vous conseillons une longueur maximale de 70 caractères.').'";
			const metasDescriptionCourt          = "'._('La description est trop courte, ajoutez du texte').'";
			const metasDescriptionLongeurCorrect = "'._('La description a une longueur correcte pour le référencement').'";
			const metasDescriptionLong           = "'._('La description est trop longue, elle apparaîtra coupée dans les résultats de recherche, nous vous conseillons une longueur maximale de 156 caractères.').'";
			const metasMotCleCourt               = "'._('Le nombre de mots clés est insuffisant, ajoutez des mots clés').'";
			const metasMotCleLongeurCorrect      = "'._('Les mot clés ont une longueur correcte pour le référencement').'";
			const metasMotCleLong                = "'._('Les mot clés sont trop long, nous vous conseillons une longueur maximale de 1000 caractères., veuillez en supprimer.').'";
		';
	}


	{//Variable de langage pour le fichier moderation/index.js
		print '
			const moderationNoMessageCritere          = "'._('Aucun message ne correspond aux critères de sélection.').'";
			const moderationAttenteReponse            = "'._('Messages en attente de réponse').'";
			const moderationReponseRecu               = "'._('Messages ayant reçu une réponse').'";
			const moderationTousMessage               = "'._('Tous les messages').'";
			const moderatiuoContactSansClient         = "'._('Contacts ne disposant pas d\'un compte client').'";
			const moderationContactAvecClient         = "'._('Contacts disposant d\'un compte client').'";
			const moderationDevisConcerne             = "'._('Devis concerné').'";
			const moderationProduitConcerne           = "'._('Produit concerné').'";
			const moderationEnvoyePar                 = "'._('Envoyé par').'";
			const moderationLe                        = "'._('Le').'";
			const moderationDestinataire              = "'._('Destinataire').'";
			const moderationLeCopie                   = "'._('En copie').'";
			const moderation                          = "'._('Modération').'";
			const moderationApprouveLe                = "'._('Approuvé le ').'";
			const moderationNePlusApprouve            = "'._('Ne plus approuver').'";
			const moderationRefuseLe                  = "'._('Refusé le ').'";
			const moderationApprouver                 = "'._('Approuver').'";
			const moderationCommentaireDesapprobation = "'._('Commentaire sur la désapprobation').'";
			const moderationEnAttente                 = "'._('En attente depuis le ').'";
			const moderationPar                       = "'._('Par ').'";
			const moderationDesapprouver              = "'._('Désapprouver').'";
			const moderationSujet                     = "'._('Sujet').'";
			const moderationNote                      = "'._('Note').'";
			const moderationNoteLivraison             = "'._('Note sur la livraison').'";
			const moderationNoteEmballage             = "'._('Note sur l\'emballage').'";
			const moderationPiecesJointes             = "'._('Pièces jointes').'";
			const moderationPieceJointe               = "'._('Pièce jointe').'";
			const moderationInfoComplementaire        = "'._('Informations complémentaires').'";
			const moderationAfficherReponses          = "'._('Afficher les réponses').'";
			const moderationMasquerReponses           = "'._('Masquer les réponses').'";
			const moderationAfficherReponse           = "'._('Afficher la réponse').'";
			const moderationMasquerReponse            = "'._('Masquer la réponse').'";
			const moderationVotreReponse              = "'._('Votre réponse').'";
			const moderationRepondre                  = "'._('Répondre').'";
			const moderationSignalerSpam              = "'._('Signaler comme spam').'";
			const moderationRetirerSpam               = "'._('Retirer des spams').'";
			const moderationReponse                   = "'._('Réponse').'";
			const moderationSaisirReponse             = "'._('Saisissez votre réponse').'";
			const moderationAttacherFichier           = "'._('Attacher des fichiers à votre réponse').'";
			const moderationAnnuler                   = "'._('Annuler').'";
			const moderationEnvoyer                   = "'._('Envoyer').'";
			const moderationDesapprobationMessage     = "'._('Désapprobation de message').'";
			const moderationSaisirMsgRepondre         = "'._('Veuillez saisir un message de réponse').'";
			const moderationMsgEnvoye                 = "'._('Votre message a correctement été envoyé.').'";
			const moderationErreruEnvoiMsg            = "'._('Une erreur inattendue est survenue lors de l\'envoi de votre message.').'";
		';
	}


	{//Variable de langage pour le fichier tools/news.js
		print '
			const newsValider                              = "'._('Valider').'";
			const newsAnnuler                              = "'._('Annuler').'";
			const newsDemande                              = "'._('Demande').'";
			const newsNon                                  = "'._('Non').'";
			const newsConfirmSuppressionActualite          = "'._('Vous êtes sur le point de supprimer cet actualité.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir cntinue r ? ').'";
			const newsConfirmSuppressionActualites         = "'._('Vous êtes sur le point de supprimer une ou plusieurs actualités.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vou sûr d e vouloir continuer ? ').'";
			const newsAlertTitreActualite                  = "'._('Veuillez indiquer le titre de l\'actualité.').'";
			const newsDatePublicationActualite             = "'._('Veuillez indiquer la date de publication souhaitée de l\'actualité.').'";
			const newsAlertFormatDatePublicationActualite  = "'._('La date de publication de l\'actualité n\'est pas dans un format reconnu.\nVeuillez utiliser une date au format jj/mm/aaaa.').'";
			const newsDefinirCliqueZone                    = "'._('Définir des zones cliquables').'";
		';
	}


	{//Variable de langage pour le fichier tools/newsletter.js
		print'
			const newsletterAjouterProduit                = "'._('Ajouter un produit').'";
			const newsletterInscriptionNewsletter         = "'._('Inscriptions à la newsletter (').'";
			const newsletterInscriptionsNonTerminees      = "'._('inscriptions non terminées').'";
			const newsletterInscriptionNonTerminee        = "'._('inscription non terminée').'";
			const newsletterInscrits                      = "'._('inscrits à la newsletter').'";
			const newsletterInscrit                       = "'._('inscrit à la newsletter').'";
			const newsletterDesinscriptionsNonTerminees   = "'._('désinscriptions non terminées').'";
			const newsletterDesinscriptionNonTerminee     = "'._('désinscription non terminée').'";
			const newsletterDessincrits                   = "'._('désinscrits de la newsletter').'";
			const newsletterDessincrit                    = "'._('désinscrit de la newsletter').'";
			const newsletterAdressesMails                 = "'._('adresses emails').'";
			const newsletterAdresseMail                   = "'._('adresse email').'";
			const newsletterAucuneAdresse                 = "'._('Aucune adresse').'";
			const newsletterAfficherFicheInscription      = "'._('Afficher la fiche d\'inscription pour l\'adresse ').'";
			const newsletterDesincritDepuis               = "'._('Désinscrit depuis le ').'";
			const newsletterInscritDepuis                 = "'._('Inscrit depuis le ').'";
			const newsletterDessincritDebute              = "'._('Inscription initiée le #param[date]# mais non terminée').'";
			const newsletterConfirmDesinscription         = "'._('Etes vous sûr(e) de vouloir désinscrire cette adresse de la newsletter ?').'";
		';
	}


	{//Variable de langage pour le fichier orders/attempts.js
		print '
			const orderAttemptsHistorique = "'._('Historique des paiements').'";
		';
	}


	{//Variable de langage pour le fichier orders/brands.js
		print '
			const orderBrandsCliqueZone = "'._('Définir des zones cliquables').'";
		';
	}


	{//Variable de langage pour le fichier orders/create.js
		print '
			const orderCreateSelectClient               = "'._('Sélection d\'un client').'";
			const orderCreateAdresseLivraison           = "'._('Adresse de livraison').'";
			const orderCreateSelectProduit              = "'._('Sélection de produit').'";
			const orderCreateAjoutProduit               = "'._('Ajout de produit par lot').'";
			const orderCreateConfirmSuppressionProduits = "'._('Souhaitez vous vraiment supprimer les produits sélectionnés ?').'";
			const orderCreateSelectConditionnement      = "'._('Sélection des conditionnements').'";
		';
	}


	{//Variable de langage pour le fichier orders/models.js
		print '
			const orderModelsAutorisationModele = "'._('Autorisations du modèle').'";
			const orderModelsEnvoiEnCours       = "'._('Envoie en cours, veuillez patienter...').'";
		';
	}



	{//Variable de langage pour le fichier orders/modify.js
		print '
			const orderModifySlelectProduit                    = "'._('Sélectionner un produit').'";
			const orderModifySelectCommande                    = "'._('Sélectionner un modèle de commande').'";
			const orderModifySelectServiceLivraison            = "'._('Sélectionner un service de livraison').'";
			const orderModifySelectNomenclature                = "'._('Sélection de la nomenclature').'";
			const orderModifySlectConditionnement              = "'._('Sélection des conditionnements').'";
			const orderModifyErreurAssignationServiceLivraison = "'._('Une erreur est survenue lors de l\'assignation du service de livraison.\nVeuillez verifier que le service choisi est disponible pour les produits de la commande').'";
		';
	}

	{//Variable de langage pour le fichier orders/order.js
		print '
			const orderSelectClient            = "'._('Sélectionner un compte client').'";
			const orderEditerProduitFacture    = "'._('Editer produit facturé').'";
			const orderErreurModifCompteClient = "'._('Une erreur est survenue lors de la modification du compte client rattaché à cette commande.').'";
		';
	}

	{//Variable de langage pour le fichier orders/index.js
		print '
			const btnDupliquer                       = "'._('Dupliquer').'";
			const ordersCommandes                    = "'._('Commandes').'";
			const ordersTouitesCommandes             = "'._('Toutes les commandes').'";
			const ordersPeriodePerso                 = "'._('Période personnalisée').'";
			const ordersAujourdhui                   = "'._('Aujourd\'hui').'";
			const ordersHier                         = "'._('Hier').'";
			const orders7DerniersJours               = "'._('Les 7 derniers jours').'";
			const ordersSemaineDerniere              = "'._('La semaine dernière').'";
			const orders14DerniersJours              = "'._('Les 14 derniers jours').'";
			const orders30DerniersJours              = "'._('Les 30 derniers jours').'";
			const ordersMoisCourant                  = "'._('Ce mois-ci').'";
			const ordersMoisDernier                  = "'._('Le mois dernier').'";
			const orders1Janvier                     = "'._('Depuis le 1er janvier').'";
			const ordersAnneeDerniere                = "'._('L\'année dernière').'";
			const ordersToutePeriode                 = "'._('Toute la période').'";
			const ordersValider                      = "'._('Valider').'";
			const ordersAnnuler                      = "'._('Annuler').'";
			const ordersAfficherFicheCommande        = "'._('Afficher la fiche de cette commande').'";
			const ordersAcuneCommandeCritere         = "'._('Aucune commande ne correspond à vos critères de sélection.').'";
			const ordersPrecedent                    = "'._('Précédent').'";
			const ordersSuivantt                     = "'._('Suivant').'";
			const ordersAlertDeconnecter             = "'._('Vous avez été déconnecté(e). Veuillez vous authentifier à nouveau.').'";
			const ordersErreurEnregistrementCommande = "'._('Une erreur inattendue est survenue lors de l\'enregistrement de votre demande.\nVeuillez réessayer ou prendre contact avec nous pour résoudre cette ereur.  ').'";
			const oderscommandesSupplementaire                         = "'._('Il y a #param[nb_commande]# commande(s) supplémentaire(s)').'";
		';
	}


	{//Variable de langage pour le fichier config/owner.js
		print '
			const ownerAlertRaisonSociale                 = "'._('La raison sociale du propriétaire de la boutique est obligatoire.\nVeuillez l\'indiquer.').'";
			const ownerAlertPrenom                        = "'._('Le prénom de la personne physique propriétaire de la boutique est obligatoire.\nVeuillez l\'indiquer.').'";
			const ownerAlertNom                           = "'._('Le nom de famille de la personne physique propriétaire de la boutique est obligatoire.\nVeuillez l\'indiquer.').'";
		';
	}


	{//Variable de langage pour le fichier config/paiements.js
		print '
			const paiementsConfirmSuppressionInfoBancaire         = "'._('Vous êtes sur le point de supprimer ces informations bancaires.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes ous sû r(e) de vouloir continuer ? ').'";
		';
	}

	{//Variable de langage pour le fichier config/extranet.js
		print '
			const submitError         = "'._('Une erreur est survenue lors de l\'enregistrement des données.').'";
		';
	}

	{//Variable de langage pour le fichier promotions/specials.js
		print'
			const pmtSpecialsConfirmCreationChequiers  = "'._('Cette action va créer tous les chéquiers qui respecteront les mêmes restrictions et conditions d\'application. \nSouhaitez-vous lancer leur création maintenant ?').'";
			const pmtSpecialsErreurSaiseNumeric        = "'._('Veuillez saisir un numéric supérieur à 0.').'";
			const pmtSpecialsModifieePatternPromo      = "'._('Modifier le pattern des codes promotions').'";
			const pmtSpecialsSelectCompteClient        = "'._('Sélectionner un compte client').'";
			const pmtSpecialsChoisirTypeCompte         = "'._('Veuillez choisir le type de compte ou directement un compte.').'";
			const pmtSpecialsSubSymbole                = "'._('(le symbôle <tt>+</tt> indique des comptes clients inclus dans la promotion, le symbôle <tt>-</tt> des comptes exclus.)').'";
			const pmtSpecialsSupprimer                 = "'._('Supprimer').'";
			const pmtSpecialsAucuneException           = "'._('Aucune exception n\'est enregistrée pour le moment.').'";
			const pmtSpecialsInfoMaj                   = "'._('Les informations ont bien été mises à jour.').'";
			const pmtSpecialsErreurEnregistremetnInfos = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement des informations.').'";
			const pmtSpecialsSelectProduit             = "'._('Sélectionner un produit').'";
			const pmtSpecialsSelectCategorie           = "'._('Sélectionner une catégorie').'";
			const pmtSpecialsSelectMarque              = "'._('Sélectionner une marque').'";
			const pmtSpecialsChoisirProduit            = "'._('Veuillez choisir un produit, une plage de références, une catégorie ou une marque.').'";
			const pmtSpecialsRenseignerPlageReference  = "'._('Veuillez renseigner une plage de références valide.').'";
			const pmtSpecialsImporteAutrePromo         = "'._('Importer depuis une autre promotion').'";
			const pmtSpecialsConditionnement           = "'._('Conditionnement : ').'";
			const pmtSpecialsToutTypeConditionnement   = "'._('Tout type de conditionnement').'";
			const pmtSpecialsChoisirConditionnement    = "'._('Choisir un conditionnement').'";
			const pmtSpecialsunite                     = "'._('A l\'unité').'";
			const pmtSpecialsuniteNouveauTarif         = "'._('Nouveau tarif').'";
			const pmtSpecialsExceptions                = "'._('Exceptions').'";
			const pmtSpecialsReductions                = "'._('Réduction').'";
			const pmtSpecialsSubSymboleProduits        = "'._('(le symbole <tt>+</tt> indique des produits inclus dans la promotion, le symbôle <tt>-</tt> des produits exclus.)').'";
			const pmtSpecialsErreurSaisieQuantite      = "'._('Merci de saisir une quantité supérieure à zéro.').'";
			const pmtSpecialsGenereCode                = "'._('La génération des nouveaux codes s\'est correctement déroulée.').'";
			const pmtSpecialsSurLe                     = "'._('% sur le ').'";
			const pmtSpecialsEme                       = "'._('ème').'";
			const pmtSpecialsEt                        = "'._('Et').'";
			const pmtSpecialsOu                        = "'._('Ou').'";
			const pmtSpecialsGroupe                    = "'._('Groupe ').'";
			const pmtSpecialsValide                    = "'._('Le groupe valide').'";
			const pmtSpecialsToutesLes                 = "'._('Toutes les').'";
			const pmtSpecialsAuMoins                   = "'._('au moins l\'une de').'";
			const pmtSpecialsConsition                 = "'._('conditions ci-dessous :').'";
			const pmtSpecialsAjouterCondition          = "'._('Ajouter une condition').'";
			const pmtSpecialsSubValeurEntier           = "'._('La valeur saisie doit être un nombre entier').'";
			const pmtSpecialsSubValeurEntierFlottant   = "'._('La valeur saisie doit être un nombre entier, les nombres à virgule sont aussi acceptés.').'";
			const pmtSpecialsCalcule                   = "'._('Calculé sur').'";
			const pmtSpecialsCalculee                  = "'._('Calculée sur').'";
			const pmtSpecialsTitreRegleInclusion       = "'._('Vous pouvez tenir compte des articles présents dans toutes la commandes ou seulements respectant les règles d\'inclusions / exclusions définies pour cette promotion.').'";
			const pmtSpecialsTouteCommande             = "'._('Toute la commande').'";
			const pmtSpecialsRegleInclusion            = "'._('Règles d\'inclusions / exclusions').'";
			const pmtSpecialsMemeQuantite              = "'._('Même quantité sur chaque ligne de commande').'";
			const pmtSpecialsAuncunProduitOffert       = "'._('Aucun produit offert pour le moment').'";
			const pmtSpecialsReference                 = "'._('Référence').'";
			const pmtSpecialsQteOfferte                = "'._('Quantité offerte').'";
			const pmtSpecialsLaReference               = "'._('La référence ').'";
			const pmtSpecialsReferenceExclus           = "'._(' ne fait plus ou pas partie de vos références.').'";
			const pmtSpecialsRechercheProduit          = "'._('Rechercher un produit').'";
		';
	}


	{//Variable de langage pour le fichier popup-ria.js
		print '
			const popupRiaRechercheDe = "'._('Recherche de').'";
		';
	}

	{ // Google Maps
		print '
			const gmReference  = "'._('Référence').'";
			const gmRef        = "'._('Réf').'";
			const gmMore       = "'._('Plus...').'";
		';
	}

	{//Variable de langage pour le fichier stats/prd-conversion.js
		print '
			const prdConversion = "'._('Période personnalisée').'";
		';
	}

	{//Variable de langage pour le fichier popup-duplicate-parent-to-child.js
		print '
			const prdCopyWaiting = "'._('Copie des informations en cours, veuillez patienter...').'";
		';
	}


	{//Variable de langage pour le fichier catalog/price-tva-conditions.js
		print '
			const priceTvaConditionConfirmSuppressionTarif              = "'._('Vous êtes sur le point de supprimer une condition sur ce tarif.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionConfirmSuppressionListe              = "'._('Vous êtes sur le point de supprimer une condition de la liste.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionErreurSuppressionCondition           = "'._('Une erreur inattendue s\'est produite lors de la suppression de la condition.\nVeuillez réessayer ou prendre contact avec l\'administrateur.').'";
			const priceTvaConditionConfirmSuppressionTarifConditionnel  = "'._('Vous êtes sur le point de supprimer ce tarif conditionnel.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionSuccesSuppressionTarif               = "'._('La suppression du tarif s\'est correctement déroulée.').'";
			const priceTvaConditionErreruValeurSaveTarif                = "'._('Veuillez renseigner une valeur pour pouvoir sauvegarder le tarif conditionnel. <br />Cette valeur doit être un nombre supérieur à 0.').'";
			const priceTvaConditionSuccesEnregistrementTarif            = "'._('L\'enregistrement du tarif s\'est correctement déroulé.').'";
			const priceTvaConditionConfirmSuppressionOnlyConditionListe = "'._('Vous êtes sur le point de supprimer la seule condition présente dans cette liste. \nEn acceptant de le faire, la liste elle-même sera supprimée. Cette opération est irréversible et ne pourra pas être annulée. \nEtes-vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionConfirmSuppressionListeCondition     = "'._('Vous êtes sur le point de supprimer cette liste de conditions d\'exonération de la TVA.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionSuccesSuppressionListeCondition      = "'._('La suppression de la liste de conditions s\'est correctement déroulée.').'";
			const priceTvaConditionSuccesEnregistrementListeCondition   = "'._('L\'enregistrement de la liste des conditions pour l\'exonération de la TVA s\'est correctement déroulé.').'";
			const priceTvaConditionSuccesMajListeCondition              = "'._('La mise à jour de la liste des conditions pour l\'exonération de la TVA s\'est correctement déroulée.').'";
			const priceTvaConditionConfirmSuppressionOnlyConditionEcotaxe   = "'._('Vous êtes sur le point de supprimer cette liste de conditions d\'exonération de l\'écotaxe.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const priceTvaConditionSuccesSuppressionListeEcotaxe        = "'._('L\'enregistrement de la liste des conditions pour l\'exonération de l\'écotaxe s\'est correctement déroulé.').'";
			const priceTvaConditionSuccesEnregistrementListeEcotaxe     = "'._('La mise à jour de la liste des conditions pour l\'exonération de l\'écotaxe s\'est correctement déroulée.').'";
			const priceTvaConditionSuccesEnregistrementPromo            = "'._('L\'enregistrement de la promotion s\'est correctement déroulé.').'";
			const priceTvaConditionOu                                   = "'._('OU').'";
		';
	}


	{//Variable de langage pour le fichier stats/price-watching.js
		print '
			const priceWatchingTelechargement = "'._('Votre fichier est cours de téléchargement, merci de bien vouloir patienter...').'";
		';
	}


	{//Variable de langage pour le fichier price-categories.js
		print '
			const priceCategoriesAlertChampNom                      = "'._('Veuillez indiquer le nom de la catégorie').'";
			const priceWatchingConfirmSuppressionCategories         = "'._('Vous êtes sur le point de supprimer une ou plusieurs catégories tarifaires.\nCette opération entraînera également la suppression de tous les tarifs enregistrés pour ces catégories.\n\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
			const priceWatchingConfirmSuppressionCategorie          = "'._('Vous êtes sur le point de supprimer une ou plusieurs exclusion.\n\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier products.js
		print '
			const productsSelectProduit                              = "'._('Sélectionnez un produit').'";
			const productsConfirmSuppressionModelPerso               = "'._('Les prochains exports de l\'article seront réalisés directement avec ses informations ou bien avec la surcharge mise en place.\nCette action est irréversible, êtes-vous sûr(e) de vouloir continuer ? ').'";
			const productsAlertLongeurListeProduit                   = "'._('La valeur saisie pour la longueur des listes de produits est incorrecte. \nCette valeur doit être numérique et comprise entre 1 et 99. ') .  '";
			const productsConfirmSuppressionProduit                  = "'._('Vous êtes sur le point de supprimer ce produit.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) devouloi r continuer ? ').'";
			const productsCliqueZone                                 = "'._('Définir des zones cliquables').'";
			const productsAttrAlt                                    = "'._('Définir l\'attribut ALT pour ce produit').'";
			const productsAlertReference                             = "'._('Veuillez indiquer la référence du produit.').'";
			const productsAlertNom                                   = "'._('Veuillez indiquer le nom du produit.').'";
			const productsAlertChampStockActuel                      = "'._('Veuillez indiquer le stock actuel du produit par un nombre entier valide.\nLes valeurs négatives ne sont pas autorisées dans ce champ. ').'";
			const productsAlertChampStockReserve                     = "'._('Veuillez indiquer le stock réservé du produit par un nombre entier valide.\nLes valeurs négatives ne sont pas autorisées dans ce cham. ') .  '";
			const productsAlertChampQuantiteCommnde                  = "'._('Veuillez indiquer la quantité commandée par un nombre entier valide.\nLes valeurs négatives ne sont pas autorisées dans ce champ.').'";
			const productsAlertChampDateLivraison                    = "'._('La date de livraison saisie n\'est pas reconnue comme valide.\nVeuillez indiquer une date au format jj/mm/aaaa.').'";
			const productsValeurDateDebut                            = "'._('Valeur inférieur à date de début').'";
			const productsAlertDateDebutPromo                        = "'._('Veuillez indiquer la date à laquelle débute la promotion.').'";
			const productsAlertDateDebutinvalide                     = "'._('La date de début n\'est pas reconnue.\nVeuillez la vérifier.').'";
			const productsAlertHeureDebutPromotionInvalide           = "'._('L\'heure de début de promotion n\'est pas reconnue.\nVeuillez la vérifier.').'";
			const productsAlertDateFinPromo                          = "'._('Veuillez indiquer la date à laquelle la promotion prend fin.').'";
			const productsAlertDateFininvalide                       = "'._('La date de fin n\'est pas reconnue.\nVeuillez la vérifier.').'";
			const productsAlertHeureFinPromotionInvalide             = "'._('L\'heure de fin de promotion n\'est pas reconnue.\nVeuillez la vérifier.').'";
			const productsAlertRemiseAccordee                        = "'._('Veuillez indiquer la remise accordée lors de la promotion.').'";
			const productsAlertRemiseAccordeeInvalide                = "'._('La remise accordée durant la promotion n\'est pas reconnue.\nVeuillez la vérifier.').'";
			const productsAlertDateFinPremotionAnterieur             = "'._('La date de fin de promotion est antérieure à la date début.\nVeuillez vérifier.').'";
			const productConfirmSuppressionPromotion                 = "'._('Vous êtes sur le point de supprimer cette promotion.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sr(e) d e vouloir continuer ? ').'";
			const productsErreurEnregistrementDemande                = "'._('Une erreur inattendue est survenue lors de l\'enregistrement de votre demande.\nVeuillez réessayer ou prendre contact avec nous pour ésoudr e cette erreur. ').'";
			const productsEnregistrer                                = "'._('Enregistrer').'";
			const productsAnnuler                                    = "'._('Annuler').'";
			const productsEditer                               	   = "'._('Editer').'";
			const productsSupprimer                                  = "'._('Supprimer').'";
			const productsConfirmSupressionUrl                       = "'._('Vous êtes sur le point de supprimer Une URL de redirection.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes ous sû r(e) de vouloir continuer ? ').'";
			const productsSuccesSuppressionUrl                       = "'._('La suppression de l\'url de redirection s\'est correctement passée.').'";
			const productsImporterRelationsProduit                   = "'._('Importer les relations d\'un autre produit').'";
			const productsAucuneOffre                                = "'._('Aucune offre de fidélité').'";
			const productAddTypeRelation            			       = "'._('Ajout un nouveau type de relation').'";
		';
	}


	{//Variable de langage pour le fichier promotions/index.js
		print '
			const promotionsSuppressionCondition                        = "'._('Supprimer cette condition').'";
			const promotionsSupprimer                                   = "'._('Supprimer').'";
			const promotionsFormatHeureInvalide                         = "'._('Format invalide (requiert hh:mm)').'";
			const promotionsChampObg                                    = "'._('Champ obligatoire vide').'";
			const promotionsFormatInvalide                              = "'._('Format invalide').'";
			const promotionsFormatInvalideNumerique                     = "'._('Format invalide (comprix entre 1 et 100)').'";
			const promotionsValeurAnterieurADateDateHeureDebut          = "'._('Valeur inférieur à Date de début/Heure de début').'";
			const promotionsAlertFiltreDate                             = "'._('La date que vous avez saisi n\'est pas reconnue.\nVeuillez entrer une date au format jj/mm/aaaa.').'";
			const promotionsConfirmSuppressionCodespromotions           = "'._('Vous êtes sur le point de supprimer un ou plusieurs codes promotions.\nEtes-vous sûr(e) de vouloir continuer ?\n\nCette opération est irréversible et ne pourra pas être annulée.').'";
			const promotionsConfirmSuppressionGroupesPromotions         = "'._('Vous êtes sur le point de supprimer un ou plusieurs groupes de promotions.\nEtes-vous sûr(e) de vouloir continuer ?\n\nCette opération est irréversible et ne pourra pas être annulée.').'";
			const promotionsConfirmSuppressionCodePromotion             = "'._('Vous êtes sur le point de supprimer ce code promotion.\nEtes-vous sûr(e) de vouloir continuer ?').'";
			const promotionsPromotionEdition                            = "'._('Formulaire Promotion Edition').'";
			const promotionsChampCodePromo                              = "'._('Champ Code promotion').'";
			const promotionsChampDateDebut                              = "'._('Champ Date de début').'";
			const promotionsChampsHeureDebut                            = "'._('Champ Heure de début').'";
			const promotionsChampDateFin                                = "'._('Champ Date de fin').'";
			const promotionsChampsHeureFin                              = "'._('Champ Heure de fin').'";
			const promotionsChampDateHeureFin                           = "'._('Champ Date de fin/Heure de fin').'";
			const promotionsAlertCodePromo                              = "'._('Veuillez indiquer le code promotion.').'";
			const promotionsAlertDateDebut                              = "'._('Veuillez indiquer la date de début de validité du code promotion.').'";
			const promotionsAlertDateDebutInvalide                      = "'._('La date saisie pour le début de validité du code promotion n\'est pas reconnue comme valide.\nVeuillez entrer une date au format jj/mm/aaaa dans ce champ.').'";
			const promotionsAlertHeureDebutInvalide                     = "'._('L\'heure de début de validité du code promotion n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.').'";
			const promotionsAlertDateFin                                = "'._('Veuillez indiquer la date de fin de validité du code promotion.').'";
			const promotionsAlertDateFinInvalid                         = "'._('La date saisie pour la fin de validité du code promotion n\'est pas reconnue comme valide.\nVeuillez entrer une date au format jj/mm/aaaa dans ce champ.').'";
			const promotionsAlertHeureFinInvalide                       = "'._('L\'heure de fin de validité du code promotion n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.').'";
			const promotionsAlertDateHeureFinAnterieur                  = "'._('La date et heure de fin de la promotion doit être supérieure à la date et heure de début.').'";
			const promotionsCheckboxReductionPort                       = "'._('Checkbox réduction/frais de port offert').'";
			const promotionsChampsMttReduction                          = "'._('Champ Montant réduction').'";
			const promotionsChampsPourcentageReduction                  = "'._('Champ Pourcentage réduction').'";
			const promotionsChampsMttMinCommande                        = "'._('Champ Montant minimal de commande').'";
			const promotionsChampsNbMaxutilisation                      = "'._('Champ Nombre maximum d\'utilisations').'";
			const promotionsSelectMinBeneficeOffert                     = "'._('Veuillez sélectionner au minimum un bénéfice offert par la promotion').'";
			const promotionsAlertSelectMinBeneficeOffert                = "'._('Veuillez sélectionner au minimum un bénéfice offert par la promotion :\nréduction ou offre des frais de port.').'";
			const promotionsAlertMttReduction                           = "'._('Le montant de réduction octroyé via l\'utilisation du code promotion n\'est pas reconnu comme valide.\nVeuillez entrer un montant valide dans ce champ.').'";
			const promotionsAlertPourcentageReduction                   = "'._('Le pourcentage de réduction octroyé via l\'utilisation du code promotion n\'est pas reconnu comme valide.\nVeuillez entrer un pourcentage valide dans ce champ.').'";
			const promotionsAlertPourcentageReductionInvalide           = "'._('Le pourcentage de réduction octroyé via l\'utilisation du code promotion doit être compris entre 1 et 100%').'";
			const promotionsAlertMttMinValide                           = "'._('Le montant minimal de commande indiqué n\'est pas reconnu comme valide.\nVeuillez indiquer un montant valide dans ce champ.').'";
			const promotionsChoixMarque                                 = "'._('Choisir une marque').'";
			const promotionsChoixCategorie                              = "'._('Choisir une catégorie').'";
			const promotionsVerifReferenceProduit                       = "'._('Vérifier une référence produit').'";
			const promotionsVerifReferenceClient                        = "'._('Vérifier une référence client').'";
			const promotionsChoixutilisatuer                            = "'._('Choisir un utilisateur').'";
			const promotionsChampCompteClient                           = "'._('Champ Compte client').'";
			const promotionsChampTypeRegle                              = "'._('Champ Type de règle').'";
			const promotionsAlertChampCompteClient                      = "'._('Veuillez sélectionnez le compte client concerné.').'";
			const promotionsAlertChampTypeRegle                         = "'._('Veuillez sélectionner le type de règle à ajouter.').'";
			const promotionsGroupePromotionEdition                      = "'._('Formulaire Groupe Promotions Edition').'";
			const promotionsChampNom                                    = "'._('Champ Nom').'";
			const promotionsAlertNomGourpePromo                         = "'._('Veuillez indiquer le nom du groupe de promotions.').'";
			const promotionsAlertDateDebutGroupePromo                   = "'._('Veuillez indiquer la date de début de validité du groupe de promotions.').'";
			const promotionsAlertDateDebutGoupePromoInvalide            = "'._('La date saisie pour le début de validité du groupe de promotions n\'est pas reconnue comme valide.\nVeuillez entrer une date au format jj/mm/aaaa dans ce champ.').'";
			const promotionsAlertHeureDebutGroupePromoInvalide          = "'._('L\'heure de début de validité du groupe de promotions n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.').'";
			const promotionsAlertDateFinGroupePromo                     = "'._('Veuillez indiquer la date de fin de validité du groupe de promotions.').'";
			const promotionsAlertDateFinGroupePromoInvalide             = "'._('La date saisie pour la fin de validité du groupe de promotions n\'est pas reconnue comme valide.\nVeuillez entrer une date au format jj/mm/aaaa dans ce champ.').'";
			const promotionsAlertHeureFinGroupePromo                    = "'._('L\'heure de fin de validité du groupe de promotions n\'est pas reconnue comme valide.\nVeuillez entrer une heure au format hh:mm dans ce champ.').'";
			const promotionsRetirerProduit                              = "'._('Retirer le produit').'";
			const promotionsRetirerCategorie                            = "'._('Retirer la catégorie').'";
			const promotionsPrixBaseHT                                  = "'._('Prix de base HT').'";
			const promotionsPromoInclusProduit                          = "'._('Ce produit est déjà inclut dans la promotion.').'";
			const promotionsProduits                                    = "'._('produits').'";
			const promotionsPromoInclusCategorie                        = "'._('Cette catégorie est déjà incluse dans la promotion.').'";
			const promotionsAucuneRemiseSaisi                           = "'._('Aucun valeur de remise n\'est saisi').'";
			const promotionsAlertDateDebutFinFormatAndObg               = "' . addslashes(_('Les champs date de début et de fin sont obligatoires et doivent être au format JJ/MM/AAAA.')).'";
			const promotionsAlertDateDebutFinFormat                     = "'._('La date de début et de fin doivent être au format JJ/MM/AAAA.').'";
			const promotionsAlertDateFinSupDateDebut                    = "'._('La date de fin ne peut être antérieure à la date de début.').'";
			const promotionsAlertNonPromotion                           = "'._('Aucun produit/aucune catégorie n\'est affecté à cette promotion.').'";
			const promotionsConfirmSuppressionGroupePromotion           = "'._('Vous êtes sur le point de supprimer ce groupe de promotions.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
		';
	}
		

	{//Variable de langage pour le fichier redirection.js
		print '
			const redirectionAjoutPermanent    = "'._('Ajouter une redirection permanente').'";
			const redirectionAucuneRedirection = "'._('Aucune redirection permanente ne correspond à votre recherche').'";
			const redirectionPrec              = "'._('Page précédente').'";
			const redirectionSuiv              = "'._('Page suivante').'";
			const redirectionPermanente        = "'._('Redirections permanentes').'";
		';
	}


	{//Variable de langage pour le fichier config/referencement.js
		print '
			const referencementConfirmSuppressionMarque      = "'._('Vous êtes sur le point de supprimer cette marque.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
			const referencementConfirmSuppressionImageMarque = "'._('Vous êtes sur le point de supprimer l\'image associée à cette marque.\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr(e) de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier relations.js
		print'
			const relationsCreerLienObjet = "'._('Créer un lien vers un nouvel objet').'";
		';
	}


	{//Variable de langage pour le fichier orders/return.js
		print '
			const returnConfirmSuppressionProduitBonRetour = "'._('Vous êtes sur le point de supprimer un ou plusieurs produits du bon de retour.\n\nCette opération est irréversible et ne pourra pas être annulée.\nEtes vous sûr de vouloir continuer ?').'";
		';
	}


	{//Variable de langage pour le fichier orders/returns.js
		print '
			const returnsPeriodePerso    = "'._('Période personnalisée').'";
			const returnsAujourdhui      = "'._('Aujourd\'hui').'";
			const returnsHier            = "'._('Hier').'";
			const returns7DerniersJours  = "'._('Les 7 derniers jours').'";
			const returnsSemaineDerniere = "'._('La semaine dernière').'";
			const returns14DerniersJours = "'._('Les 14 derniers jours').'";
			const returnsMoisCourant     = "'._('Ce mois-ci').'";
			const returnsMoisDernier     = "'._('Le mois dernier').'";
			const returns1Janvier        = "'._('Depuis le 1er janvier').'";
			const returnsToutePeriode    = "'._('Toute la période').'";
			const returnsValider         = "'._('Valider').'";
			const returnsAnnuler         = "'._('Annuler').'";
		';
	}


	{//Variable de langage pour le fichier tools/rewards.js
		print '
			const rewardsConfirmModifSystemeConversion    = "'._('Vous avez choisi de modifier le système de conversion \"Points/Euro\" par \"Code promotion\". \nCette action va générer automatiquemet des codes promotions dans la limite des points de fidélité encore disponibles, cela peut demander quelques minutes. \nÊtes-vous sur de vouloir continuer ?').'";
			const rewardsTittreGenereCodePromo            = "'._('Une fois ce nombre atteint, le système génèrera automatiquement un code promotion selon vos paramètres.').'";
			const rewardsTitreRemisePanier                = "'._('Une fois ce nombre atteint, le système appliquera automatiquement la remise au panier en cours.').'";
			const rewardsInfosMaj                         = "'._('Les informations ont bien été mises à jour.').'";
			const rewardsErreurParrainage                 = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement des informations sur le système de parrainage.').'";
			const rewardsParamtragesAvances               = "'._('Paramètres avancés').'";
			const rewardsErreurEnregistrementActions      = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement des informations sur les actions.').'";
			const rewardsSelectProduit                    = "'._('Sélectionner un produit').'";
			const rewardsSelectCategorie                  = "'._('Sélectionner une catégorie').'";
			const rewardsSelectMarque                     = "'._('Sélectionner une marque').'";
			const rewardsChoixProduitAdd                  = "'._('Veuillez choisir un produit à ajouter').'";
			const rewardsChoixPointFidelite               = "'._('Veuillez saisir un nombre de points de fidélité').'";
			const rewardsReferences                       = "'._('Référence').'";
			const rewardsProduits                         = "'._('Désignation').'";
			const rewardsPoints                           = "'._('Points').'";
			const rewardsAlerteMail                       = "'._('Alerte email').'";
			const rewardsRegleAffichage	                = "'._('Règles d\'affichage').'";
			const rewardsPointfideliteMail                = "'._('Nombre de points nécessaires pour voir le produit').'";
			const rewardsSupprimer                        = "'._('Supprimer').'";
			const rewardsAucunProduitEnregistre           = "'._('Aucun produit n\'est enregistrée pour le moment.').'";
			const rewardsChoixProduitCategorieMarque      = "'._('Veuillez choisir un produit, une catégorie ou une marque.').'";
			const rewardsProduit                          = "'._('produit').'";
			const rewardsSubProduitInclusFidelite         = "'._('(le symbole <tt>+</tt> indique des produits inclus dans le système de points de fidélité, le symbôle <tt>-</tt> des produits exclus.)').'";
			const rewardsAunceException                   = "'._('Aucune exception n\'est enregistrée pour le moment.').'";
			const rewardsErreurEnregistrementInformations = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement des informations.').'";
			const rewardsActions                          = "'._('Actions').'";
			const rewardsParrainage                       = "'._('Parrainage').'";
			const rewardsExportStat                       = "'._('Export des statistiques').'";
		';
	}


	{//Variable de langage pour le fichier riadatepicker.js
		print '
			const riadatepickerPeriodePerso                = "'._('Période personnalisée').'";
			const riadatepickerAujourdhui                  = "'._('Aujourd\'hui').'";
			const riadatepickerHier                        = "'._('Hier').'";
			const riadatepicker7DerniersJours              = "'._('Les 7 derniers jours').'";
			const riadatepickerSemaineDerniere             = "'._('La semaine dernière').'";
			const riadatepicker14DerniersJours             = "'._('Les 14 derniers jours').'";
			const riadatepicker30DerniersJours             = "'._('Les 30 derniers jours').'";
			const riadatepickerMoisCourant                 = "'._('Ce mois-ci').'";
			const riadatepickerMoisDernier                 = "'._('Le mois dernier').'";
			const riadatepicker1Janvier                    = "'._('Depuis le 1er janvier').'";
			const riadatepickerToutePeriode                = "'._('Toute la période').'";
			const riadatepickerAnneeDerniere               = "'._('L\'année dernière').'";
			const riadatepickerValider                     = "'._('Valider').'";
			const riadatepickerAnnuler                     = "'._('Annuler').'";
			const riadatepickerSiteRecherche               = "'._('Sites sur lesquels les recherches sont effectuées').'";
			const riadatepickersiteWeb                     = "'._('Site web').'";
			const riadatepickerCreationRedirection         = "'._('Créer une redirection à partir de cette recherche').'";
			const riadatepickerAucuneRecherche             = "'._('Aucune recherche correspondant à vos critères.').'";
			const riadatepickerExporter                    = "'._('Exporter').'";
			const riadatepickerPrecedent                   = "'._('Précédent').'";
			const riadatepickerSuivant                     = "'._('Suivant').'";
			const riadatepickerErreurEnregistrementDemande = "'._('Une erreur inattendue est survenue lors de l\'enregistrement de votre demande.\nVeuillez réessayer ou prendre contact avec nous por résoudre cette erreur. ').'";
			const riadatepickerRechercheSupplementaire     = "'._('Il y a #param[nb_recherche]# recherche(s) supplémentaire(s)').'";
			const riadatepickerDateFromTo					 = "'._('du #param[date1]# au #param[date2]#').'";
		';
	}


	{//Variable de langage pour le fichier riaFieldRElated.js
		print'
			const riaFieldRElatedNoResult  = "'._('Aucun résultat').'";
			const riaFieldRElatedRecherche = "'._('Recherche ...').'";
		';
	}


	{//Variable de langage pour le fichier tools/rwd-actions.js
		print '
			const rwdActionsInformationMaj                     = "'._('Les informations ont bien été mises à jour.').'";
			const rwdActionsErreurEnregistrementActions        = "'._('Une erreur inattendue s\'est produite lors de l\'enregistrement des informations sur les actions.').'";
			const rwdActionsInternaute                         = "'._('Internaute').'";
			const rwdActionsParrain                            = "'._('Parrain').'";
			const rwdActionsPoints                             = "'._('points').'";
			const rwdActionsModifier                           = "'._('Modifier').'";
			const rwdActionsSupprimer                          = "'._('Supprimer').'";
			const rwdActionsAucunPalier                        = "'._('Aucun palier n\'est pour le moment défini.').'";
			const rwdActionsSuccesSuppressionPalier            = "'._('La suppression du palier s\'est correctement déroulée.').'";
			const rwdActionsErreurSuppressionPalier            = "'._('Une erreur inattendue s\'est produite lors de la suppression du palier.').'";
			const rwdActionsEnregistrer                        = "'._('Enregistrer').'";
			const rwdActionsAnnuler                            = "'._('Annuler').'";
			const rwdActionsMsgPalierNumeriquePositif          = "'._('L\'information du palier doit être un numérique supérieur à zéro.').'";
			const rwdActionsMsgPointInternauteNumeriquePositif = "'._('L\'information du nombre de points pour l\'internaute doit être un numérique supérieur à zéro.').'";
			const rwdActionsMsgPointParrainNumeriquePositif    = "'._('L\'information du nombre de points pour le parrain doit être un numérique supérieur à zéro.').'";
			const rwdActionsMsgMinumumPrecisionPoint           = "'._('Vous devez au minimum préciser un nombre de points de fidélité pour l\'internaute ou son parrain.').'";
			const rwdActionsMsgAjoutPalier                     = "'._('L\'ajout du palier s\'est correctement déroulé.').'";
			const rwdActionsErreurPalier                       = "'._('Une erreur inattendue s\'est produite lors de l\'ajout du palier.').'";
			const rwdActionsSuccesMajPalier                    = "'._('La mise à jour du palier s\'est correctement déroulée.').'";
			const rwdActionsErreurMajPalier                    = "'._('Une erreur inattendue s\'est produite lors de la mise à jour du palier.').'";
		';
	}


	{//Variable de langage pour le fichier segments.js
		print '
			const segmentsRechercheCategorie           = "'._('Rechercher une catégorie').'";
			const segmentsRechercheProduit             = "'._('Rechercher un produit').'";
			const segmentsRechercheClient              = "'._('Rechercher un client').'";
			const segmentsGroupe                       = "'._('Groupe').'";
			const segmentsAumoinsCritere               = "'._('Valide au moins un des critères suivants').'";
			const segmentAjouterCritere                = "'._('Ajouter un critère').'";
			const segmentsSuppressionCritere           = "'._('Supprimer ce critère').'";
			const segmentsPeriode                      = "'._('Période :').'";
			const segmentsCommande                     = "'._('Commande').' :";
			const segmentsTous                         = "'._('Tous').'";
			const segmentsSupprimerCritere             = "'._('Supprimer ce critère').'";
			const segmentsSubValeurSaisiEntier         = "'._('La valeur saisie doit être un nombre entier').'";
			const segmentsSubValeurSaisiEntierFlottant = "'._('La valeur saisie doit être un nombre entier ou décimal.').'";
			const segmentsSubFomatDate                 = "'._('La valeur saisie doit être une date au format JJ/MM/AAAA (exemple : 31/12/1972).').'";
		';
	}

	{//Variable de langage pour le fichier stats-referencements.js
		print '
			const statsReferencementsPrecedente   = "'._('Page précédente').'";
			const statsReferencementsSuivante     = "'._('Page suivante').'";
			const statsReferencementsInformations = "'._('Informations').'";
		';
	}


	{//Variable de langage pour le fichier stats/search.js
		print '
			const statsSearchAjoutRedirectionRecherche        = "'._('Ajouter une redirection de recherche').'";
			const statsSearch25PremiersResultats              = "'._('Les 25 premiers résultats pour la recherche ').'";
			const statsSearchAlertErreurEnregistrementDemande = "'._('Une erreur inattendue est survenue lors de l\'enregistrement de votre demande.\nVeuillez réessayer ou prendre contact avec nous por résoude cett e erreur. ').'";
			const statsSearchPagePrecedente                   = "'._('Page précédente').'";
			const statsSearchPageSuivante                     = "'._('Page suivante').'";
		';
	}


	{//Variable de langage pour le fichier stats/index.js
		print '
			const statsAlertChampSuggestion  = "' . addslashes(_('Vous devez renseigner le champ Suggestion pour pouvoir la pré-visualiser.')).'";
			const statsAjoutResultatManuelle = "'._('Ajouter un résultat manuellement').'";
		';
	}


	{//Variable de langage pour le fichier translate.js
		print '
			const translateExportChaine             = "'._('Export des chaînes à traduire').'";
			const translateAlertSelectLangueEwport  = "'._('Merci de sélectionner une ou plusieurs langues qui devront être présentes dans l\'export').'";
			const translateAucuneTraductionResultat = "'._('Aucune traduction ne correspond aux différents filtres').'";
			const translatePrecedent                = "'._('Page précédente').'";
			const translateSuivant                  = "'._('Page suivante').'";
		';
	}

	{//Variable de langage pour le fichier catalog/authorizations.js
		print '
			const authorizationsSelectCompteClient       = "'._('Sélectionner un compte client').'";
			const authorizationsConfirmAutorriseCatalog  = "'._('Souhaitez vous vraiment autoriser tout le catalogue ?').'";
			const authorizationsConfirmSuppressionGroupe = "'._('Etes vous sur de vouloir supprimer ce groupe ?').'";
			const authorizationsSelectProduit            = "'._('Sélectionner un produit').'";
			const authorizationsSelectCategorie          = "'._('Sélectionner une catégorie').'";
			const authorizationsSelectMarque             = "'._('Sélectionner une marque').'";
			const authorizationsEnregistrement           = "'._('Enregistrement en cours...').'";
		';
	}


	{//Variable de langage pour le fichier customers/negociations.js
		print '
			const negociationsSelectClient    = "'._('Sélectionner un compte client').'";
			const negociationsSelectProduit   = "'._('Sélectionner un produit').'";
			const negociationsSelectCategorie = "'._('Sélectionner une catégorie').'";
			const negociationsSelectMarque    = "'._('Sélectionner une marque').'";
			const negociationsEnregistrement  = "'._('Enregistrement en cours...').'";
		';
	}


	{//Variable de langage pour le fichier documents/images/zones.js
		print '
			const zonesErreurEnregistrementZonesImages = "'._('Erreur lors de l\'enregistrement des zones d\'image').'";
			const zonesErreurEnregistrementZoneImage   = "'._('Erreur lors de l\'enregistrement de la zone d\'image').'";
			const zonesErreurSuppressionZoneImage      = "'._('Erreur lors de la suppression de la zone d\'image').'";
			const zonesSuprrimer                       = "'._('Supprimer').'";
		';
	}


	{//Variable de langage pour le fichier graph/prd-views-ordered.js
		print '
			const graphOrderedAucuneDonnees = "'._('Il n\'y a aucune donnée à afficher sur cette période').'";
		';
	}


	{//Variable de langage pour le fichier graph/prd-views-prd-cat.js
		print '
			const graphPrdCatAucuneDonnees = "'._('Il n\'y a aucune donnée à afficher').'";
		';
	}

	{//Variable de langage pour le fichier graph/graph-stats-gender.js
		print '
			const graphGenderNumber 	= "'._('Nombre').'";
			const graphGenderSociety  = "'._('Société').'";
			const graphGenderWoman	= "'._('Femme').'";
			const graphGenderMan		= "'._('Homme').'";
		';
	}

	{//Variable de langage pour le fichier products/relations.js
		print '
			const relationsSelectProduit                          = "'._('Sélectionnez un produit').'";		
			const relationsAlertEditionParent                     = "'._('Veuillez sélectionner un ou plusieurs produits parents à supprimer.\n\nNote : Seule la relation parent/enfant est supprimée, le produt parent n\'est pas supprimé.').'";
			const relationsAlertEditionEnfant                     = "'._('Veuillez sélectionner un ou plusieurs produits enfants à supprimer.\n\nNote : Seule la relation parent/enfant est supprimée, le produt enfant n\'est pas supprimé.').'";
			const relationsConfimSuppressionEnfant       = "'._('Vous êtes sur le point de supprimer une ou plusieurs relations parent/enfant.\nCette opération ne pourra pas être annulée.\nEtes vous sûr() de vouloir continuer ? ').'";
			const relationsAlertErreurSuppressionRelation         = "'._('Une erreur inattendue s\'est produite lors de la suppression de la relation.\nVeuillez réessayer.').'";
			const relationsAucunArticle                           = "'._('Aucun article').'";
			const relationsParent                                 = "'._('parent').'";
			const relationsEnfant                                 = "'._('enfant').'";
			const relationsAlertReponseServeur                    = "'._('La réponse du serveur n\'a pas été comprise.\nNous vous remercions de bien vouloir nous avertir de cette erreur.').'";
		';
	}

	{//Variable de langage pour le fichier fdv/devices/popup-add-licence.php
		print '
			const subscriptionMonthly												= "'._('Je souhaite ajouter #param[licences]# licence supplémentaire à mon abonnement mensuel en cours.<br>Un prélèvement au prorata de votre abonnement en cours sera effectué après validation de votre licence supplémentaire, pour un montant de #param[prorataHT]# € HT (#param[prorata]# € TTC).').'";
			const subscriptionMonthlyPlural									= "'._('Je souhaite ajouter #param[licences]# licences supplémentaires à mon abonnement mensuel en cours.<br>Un prélèvement au prorata de votre abonnement en cours sera effectué après validation de vos licences supplémentaires, pour un montant de #param[prorataHT]# € HT (#param[prorata]# € TTC).').'";
			const subscriptionYearly												= "'._('Je souhaite ajouter #param[licences]# licence supplémentaire à mon abonnement annuel en cours.<br>Un prélèvement au prorata de votre abonnement en cours sera effectué après validation de votre licence supplémentaire, pour un montant de #param[prorataHT]# € HT (#param[prorata]# € TTC).').'";
			const subscriptionYearlyPlural									= "'._('Je souhaite ajouter #param[licences]# licences supplémentaires à mon abonnement annuel en cours.<br>Un prélèvement au prorata de votre abonnement en cours sera effectué après validation de vos licences supplémentaires, pour un montant de #param[prorataHT]# € HT (#param[prorata]# € TTC).').'";

			const reduceSubscriptionYearly									= "'._('Je souhaite réduire le nombre de licences dans mon abonnement annuel.<br>Le nombre de licences actives et le tarif applicable de votre abonnement resteront en vigueur jusqu\'à la fin de votre période de facture en cours, le #param[date_1]#. A partir du #param[date_2]# et après validation de votre demande, votre abonnement comptera #param[licences]# licence pour un montant de #param[priceHT]# € HT (#param[price]# € TTC).').'";
			const reduceSubscriptionYearlyPlural									= "'._('Je souhaite réduire le nombre de licences dans mon abonnement annuel.<br>Le nombre de licences actives et le tarif applicable de votre abonnement resteront en vigueur jusqu\'à la fin de votre période de facture en cours, le #param[date_1]#. A partir du #param[date_2]# et après validation de votre demande, votre abonnement comptera #param[licences]# licences pour un montant de #param[priceHT]# € HT (#param[price]# € TTC).').'";
			const reduceSubscriptionMonthly									= "'._('Je souhaite réduire le nombre de licences dans mon abonnement mensuel.<br>Le nombre de licences actives et le tarif applicable de votre abonnement resteront en vigueur jusqu\'à la fin de votre période de facture en cours, le #param[date_1]#. A partir du #param[date_2]# et après validation de votre demande, votre abonnement comptera #param[licences]# licence pour un montant de #param[priceHT]# € HT (#param[price]# € TTC).').'";
			const reduceSubscriptionMonthlyPlural									= "'._('Je souhaite réduire le nombre de licences dans mon abonnement mensuel.<br>Le nombre de licences actives et le tarif applicable de votre abonnement resteront en vigueur jusqu\'à la fin de votre période de facture en cours, le #param[date_1]#. A partir du #param[date_2]# et après validation de votre demande, votre abonnement comptera #param[licences]# licences pour un montant de #param[priceHT]# € HT (#param[price]# € TTC).').'";
			
			const subscriptionMonthlyTesting								= "'._('Je souhaite ajouter #param[licences]# licence supplémentaire à mon abonnement mensuel en cours).<br>Le montant de votre abonnement passera à #param[tarifHT]# € HT (#param[tarif]# € TTC). Vous ne serez débité qu\'à l\'issue de votre période d\'essai de 14 jours.').'";
			const subscriptionMonthlyTestingPlural					= "'._('Je souhaite ajouter #param[licences]# licences supplémentaires à mon abonnement mensuel en cours.<br>Le montant de votre abonnement passera à #param[tarifHT]# € HT (#param[tarif]# € TTC). Vous ne serez débité qu\'à l\'issue de votre période d\'essai de 14 jours.').'";
			const subscriptionYearlyTesting									= "'._('Je souhaite ajouter #param[licences]# licence supplémentaire à mon abonnement annuel en cours.<br>Le montant de votre abonnement passera à #param[tarifHT]# € HT (#param[tarif]# € TTC). Vous ne serez débité qu\'à l\'issue de votre période d\'essai de 14 jours.').'";
			const subscriptionYearlyTestingPlural						= "'._('Je souhaite ajouter #param[licences]# licences supplémentaires à mon abonnement annuel en cours.<br>Le montant de votre abonnement passera à #param[tarifHT]# € HT (#param[tarif]# € TTC). Vous ne serez débité qu\'à l\'issue de votre période d\'essai de 14 jours.').'";
			
			const monthPrice																= "'._('Total : #param[tarifHT]# € HT (#param[tarif]# € TTC) / mois').'";
			const yearPrice																	= "'._('Total : #param[tarifHT]# € HT (#param[tarif]# € TTC) / an').'";
			const reactivateSubscriptionMonthly							= "'._('Je souhaite réactiver mon abonnement Yuto #param[package]# avec #param[licences]# licence.<br>Un prélèvement d\'un montant de #param[priceHT]# € HT (#param[price]# € TTC) sera effectué après validation.').'";
			const reactivateSubscriptionMonthlyPlural				= "'._('Je souhaite réactiver mon abonnement Yuto #param[package]# avec #param[licences]# licences.<br>Un prélèvement d\'un montant de #param[priceHT]# € HT (#param[price]# € TTC) sera effectué après validation.').'";
			const activateSubscriptionMonthly                     = "'._('Je souhaite activer mon abonnement Yuto #param[package]# avec #param[licences]# licence.<br>Un prélèvement d\'un montant de #param[priceHT]# € HT (#param[price]# € TTC) sera effectué après validation de votre paiement.').'";
			const activateSubscriptionMonthlyPlural               = "'._('Je souhaite activer mon abonnement Yuto #param[package]# avec #param[licences]# licences.<br>Un prélèvement d\'un montant de #param[priceHT]# € HT (#param[price]# € TTC) sera effectué après validation de votre paiement.').'";
			const yutoPaymentAbo														="'._('Votre abonnement est en cours d\'enregistrement, veuillez patienter...').'";
			const yutoOptionMultiDeviceMonth								= "'._('#param[tarifHT]# € HT (#param[tarifTTC]# € TTC) / mois / licence').'"
			const yutoOptionMultiDeviceYear 								= "'._('#param[tarifHT]# € HT (#param[tarifTTC]# € TTC) / an / licence').'"
			const yutoBackOfficeMonth												= "'._('Licence plateforme incluant l\'utilisation du back-office multi-administrateurs et ses évolutions : #param[price ht]# € HT (#param[price ttc]# € TTC) / mois').'";
			const yutoBackOfficeYear												= "'._('Licence plateforme incluant l\'utilisation du back-office multi-administrateurs et ses évolutions : #param[price ht]# € HT (#param[price ttc]# € TTC) / an').'";
		';
	}


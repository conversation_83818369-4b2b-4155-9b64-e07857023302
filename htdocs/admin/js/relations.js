/** \file relations.js
 *	Ce fichier permet la gestion du code javascript pour la relation entre les objets
 */

// Bouton Ajouter (une relation)
// Affiche une popup permettant de recherche la cible de la relation
$(document).delegate(
	'[name=rel-add]', 'click', function(){

		var virtualform = $(this).parents('.virtual-form');

		displayPopup( relationsCreerLienObjet, '', '/admin/ajax/relations/popup-add-object.php?' + $('input', virtualform).serialize() );
		return false;
	}
);

// Bouton Supprimer (une relation)
$(document).delegate(
	'[name=rel-del]', 'click', function(){
		$(this).parents('form').submit();
	}
);

// Cette fonction est appellée depuis la popup de recherche, elle doit rafraichir les tableaux de l'onglet.
function reloadObjects(){
	var src_cls = $('[name=reload_src_cls').val();
	var src_0 = $('[name=reload_src_0').val();
	var src_1 = $('[name=reload_src_1').val();
	var src_2 = $('[name=reload_src_2').val();

	$.ajax({
		url:'/admin/relations/edit.php?src_cls='+src_cls+'&src_0='+src_0+'&src_1='+src_1+'&src_2='+src_2,
		success:function(html){
			$('#tabpanel').html(html);
		}
	});
}
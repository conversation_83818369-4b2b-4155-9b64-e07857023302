$(document).ready(function(){
	
	$(".del-ord-prd").hide();

	// Fonction de rechargement des produits, sur la fiche d'édition de commande
	window.reloadOrdPrds = function($row, formData, refreshOtherWidgets) {
		if (!$row.length) {
			return;
		}

		var params = {
			url: '/admin/orders/order/products.php?ord=' + encodeURIComponent($row.data('ord')) 
				+ ($row.data('ordChild') ? '&ord-child=' + encodeURIComponent($row.data('ordChild')) : '')
		};

		if (formData) {
			params.type = 'POST';
			params.data = formData;
			params.processData = false;
			params.contentType = false;
		}
		return $.ajax(params)
			.done(function(data){
				$row.html(data);

				if( $('#ord-products-articles').length ){
					riaSortable.create({
						'table': $('#ord-products-articles'),
						'url': '/admin/ajax/orders/ajax-order-products-position-update.php?ord=' + $row.data('ord')
					});
				}
			})
			.then(function(){
				if (refreshOtherWidgets === false) {
					return;
				}

				$('.order-products-row[data-ord="' + $row.data('ord') + '"]').each(function(){
					var $otherRow = $(this);
					if ($row.data('ord') != $otherRow.data('ord') || $row.data('ordChild') == $otherRow.data('ordChild')) {
						return;
					}

					reloadOrdPrds($otherRow, undefined, false);
				});
			});
	};

}).delegate( '#upd-ord-usr', 'click', function(e){
		e.preventDefault();
		displayPopup( orderSelectClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&upd-ord-usr=1&ord='+$('input#ord-id').val(), '', 916, 557 );
	}
).delegate('.download-devis-pdf', 'click', function(e){ // Bouton "Imprimer le devis"
		e.preventDefault();
		var $elt = $(e.currentTarget);
		if ($elt.data('href')) {
			window.location = $elt.data('href');
		}
	}
).delegate('input.ord-prd-id, input#checkall', 'click', function(){
	var found = false;
	$('input.ord-prd-id').each(function(){
		if ($(this).prop('checked')){
			$(".del-ord-prd").show();
			found = true;
		}
		if (found){
			return;
		}
	})

	if(!found && $(".del-ord-prd").is(':visible')){
		$(".del-ord-prd").hide();
	}
}).delegate( // Champ Référence (de commande)
	'#ord-reference', 'focusout', function(event){
		event.preventDefault();

		var $elt = $(this);
		
		$.get('/admin/ajax/orders/ajax-order-set-ref.php?ord_id='+$("#ord-id").val()+'&new_ref='+$elt.val(), function(res){
			if (res.code == '400'){
				$elt.addClass('input-edit-error');
				if ($('#error').length) {
					$("#error").html(res.message);
				} else {
					$('.error').remove();
					$('.success').remove();
					$("#site-content h2").after('<div class="error" id="error">'+res.message+'</div>');
				}
			} else if(res.code == '100') {
				$elt.addClass('input-edit-success');
			}
		}, 'json');
	}
).delegate( // Lien Historique des états de la commande
	'.order-states-history-link', 'click', function (event) {
		event.preventDefault();
		var $elt = $(event.target);
		displayPopup($elt.attr('title'), '', $elt.attr('href'));
}).delegate( // Bouton Imprimer le devis (commande)
	'.print-pdf-btn', 'click', function(e){
		e.preventDefault();
		var $elt = $(event.target);
		var type = $elt.data('type');
		var ord_id = $elt.data('ord');
		var inv_id = $elt.data('inv');

		displayPopup(
			'Impression' + (type == 'devis' ? ' du devis' : (type == 'invoice' ? ' de la facture' : '')), 
			'', 
			'/admin/orders/popup-print.php?type='+encodeURIComponent(type)
				+ '&ord_id=' + ord_id
				+ (inv_id ? '&inv_id=' + encodeURIComponent(inv_id) : ''), 
			''
		);
	}
).delegate( // Zone de liste "Etat de commande", permettant la modification de l'état
	'select#newstate', 'change', function(){
		if ($('select#newstate option:selected').is("#ord_status_devis")){
			$("input[name=need_sync]").val("1");
		} else if ($('select#newstate option:selected').is("#ord_status_devis_in_progress")){
			$("input[name=need_sync]").val("0");
		}
	}
).delegate( // Bouton Supprimer (un produit, sur la fiche d'édition de commande)
'.del-ord-prd', 'click', function(event){
	event.preventDefault();
	var $elt = $(this);
	var $row = $elt.parents('table').find('.order-products-row');
	var $form = $(this).parents('form:eq(0)');
	var formData = new FormData($form.get(0));
	formData.append('del-ord-prd', '1');

	if (!$row.length || !$form.length) {
		return;
	}

	var ordPrds = [];
	$('.ord-prd-id:checked').each(function(){
		ordPrds.push($(this).data('name'));
	});
	
	var nb_products = $('.ord-prd-id').length;

	if (ordPrds.length && confirm(defaultConfirnSuppressionProduitCmd + ordPrds.join(', '))) {
		reloadOrdPrds($row, formData)

		if (ordPrds.length == nb_products){
			reloadOrdDlvServices($("input#ord-id").val(), "NULL");
		}
	}
	$(this).hide();
}
).delegate( // Champ de saisie de produit (sur la fiche d'édition de commande)
	'.ord-prd-input', 'change', function(event){
		event.preventDefault();
		var $elt = $(this);

		if ($elt.hasClass("readonly")){
			return;
		}

		var ord = $("#ord-id").val();

		var uniq_id = $elt.closest('tr.ord-prd-row').find('td.ord-prd-info input.uniqid').val();

		var prd_id = $('input[name="prd['+uniq_id+']"').val();
		var line_id = $('input[name="line['+uniq_id+']"').val();
		var child_id = $('input[name="child-line['+uniq_id+']"').val();

		var value='';
		if ($elt.hasClass("ord-prd-qte-input")){
			value = 'qte';
		} else if ($elt.hasClass("ord-prd-price-input")){
			value = 'price';
		} else if ($elt.hasClass("ord-prd-name-input")){
			value = 'name';
		} else if ($elt.hasClass("ord-prd-comment-input")){
			value = 'comment';
		} else if ($elt.hasClass("ord-prd-discount-input")){
			value = 'discount';
		} else if ($elt.hasClass("ord-prd-discount-select")){
			value = 'discount';
		}

		$(".input-edit-success").removeClass("input-edit-success");

		
		if ($elt.hasClass("ord-prd-name-input") || $elt.hasClass("ord-prd-comment-input") ){
			if ($elt.hasClass("ord-prd-name-input") && $.trim($elt.val()) == ""){
				$elt.addClass('input-edit-error');
				return;
			}
			// Permet d'encoder pour l'UTF8 certain caractère ( par exemple pour notre cas le "+" était remplacer par un espace)
			$.ajax({
				type: 'post',
				url: '/admin/ajax/orders/order-product-update.php',
				data: 'prd_id=' + prd_id + '&ord_id=' + ord + '&line_id=' + line_id + '&content=' + encodeURIComponent($elt.val()) + '&content_type=' + value,
				dataType: 'json',
				success: function(res){
					if (res.code == 100){
						$elt.removeClass("input-edit-error");
						$elt.addClass('input-edit-success');
					}
					if (res.code == 400){
						//Erreur lors de l'ajout, affichage du bloc correspondant
						if ($('#error-prd-add').length) {
							$("#error-prd-add").html(res.response);
						} else {
							$('.error').remove();
							$('.success').remove();
							$("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
						}
					}
				}
			});
		} else if ($elt.hasClass("ord-prd-price-input") || $elt.hasClass("ord-prd-qte-input") || $elt.hasClass("ord-prd-discount-input") || $elt.hasClass("ord-prd-discount-select")  ) {
			if ($elt.val().indexOf(',') != -1){
				$(this).val($elt.val().replace(',', '.'));
			}

			// Permet de mettre une valeur à zéro pour les champs prix,quantité et remise si celui-ci est vide
			if ($elt.val() == ''){
				$elt.val(0);
			}

			if (!$.isNumeric($elt.val())){
				$(this).addClass('input-edit-error');
				return;
			}

			// Le contrôle de la remise est faite lors de la saisie de la valeur de remise ou du type de remise
			var elementDiscount = null;
			if ($elt.hasClass("ord-prd-discount-input")){
				elementDiscount = $elt;
			}else if( $elt.hasClass('ord-prd-discount-select') ){
				elementDiscount = $elt.parent().find('.ord-prd-discount-input');
			}

			if( elementDiscount !== null ){
				var discount = parseFloat( elementDiscount.val() );
				
				// Contrôle la saisie sur la remise accordée à une ligne de commande
				if (elementDiscount.parent().find('.ord-prd-discount-select').val() == 0 ){
					// En euros
					// Contrôle que la valeur est bien comprise en 0 et le total ht de la ligne de commande
					if( isNaN(discount) || discount < 0 || discount > elementDiscount.parents('tr:eq(0)').find(".ord-prd-total-ht").val() ){
						elementDiscount.addClass('input-edit-error');
						return;
					}
				}else{
					// En pourcentage
					// Contrôle que la valeur est bien comprise en 0 et 100
					if( isNaN(discount) || discount < 0 || discount > 100 ){
						elementDiscount.addClass('input-edit-error');
						return;
					}
				}
			}

			$(this).parents('td:eq(0)').append('<input type="hidden" name="ord-prd['+prd_id+']['+line_id+']['+child_id+'][is_modified]" value="'+value+'" />');

			var $form = $(this).parents('form:eq(0)');
			var formData = new FormData($form.get(0));
			var $row = $elt.parents('.order-products-row');

			reloadOrdPrds($row, formData);
		} 
	}
);

$('#attach').click(function(e){
	if( !$.trim( $('#pmt-code').val() ) ){
		e.preventDefault();
	}
});

$('#save-newstate').click(function(e){
	if( $('#newstate').val() == 19 && $('#popup-invoice').length){
		e.preventDefault();
		displayPopup( orderEditerProduitFacture, '', 'popup-ord-inv-prd.php?ord='+$('input#ord-id').val() );
	}
});

$(document).ready(function () {
	// Vérifie si le bloc "Pipeline commercial" est présent.
	if (!$('.sign-rate-block').length) {
		return;
	}

	var select_sign_date = $('#select-sign-date'),
		slider_sign_date = $('#slider-sign-date'),
		select_sign_rate = $('#select-sign-rate'),
		slider_sign_rate = $('#slider-sign-rate'),
		update_sign_date_text = function () {
			$('#text-sign-date').html(signDateOptions[select_sign_date.val()].label);
		},
		update_sign_rate_text = function () {
			$('#text-sign-rate').html(signRateOptions[select_sign_rate.val()]);
		};

	select_sign_rate.change(function () {
		slider_sign_rate.val($(this).val());

		update_sign_rate_text();
	});

	select_sign_date.change(function () {
		slider_sign_date.val($(this).val());

		update_sign_date_text();
	});

	slider_sign_date.change(function () {
		select_sign_date.val($(this).val());

		update_sign_date_text();
	});

	slider_sign_rate.change(function () {
		select_sign_rate.val($(this).val());

		update_sign_rate_text();
	});

	$('#select-sign-date, #slider-sign-date, #select-sign-rate, #slider-sign-rate').change(function (e) {
		var select = e.target.id.includes('date') ? select_sign_date : select_sign_rate;

		$.post(location.href, {sign_rate: select_sign_rate.val(), sign_date: select_sign_date.val()})
			.done(function (data, status) {
				if (status !== 'success') {
					return;
				}

				select.addClass('input-edit-success');

				setTimeout(function () {
					select.removeClass('input-edit-success');
				}, 1500);
			});
	});

	slider_sign_date.val(select_sign_date.val());
	slider_sign_rate.val(select_sign_rate.val());

	update_sign_date_text();
	update_sign_rate_text();
});

/**	Cette fonction permet la mise à jour du bloc d'identification d'un client sur la page de commande, en Ajax.
 *	Elle fait appel au fichier /admin/orders/order/update-user.php qui génère le code HTML à intégrer à la page.
 */
function update_order_user( id, ord ){
	hidePopup();
	$.ajax({
		url : '/admin/orders/order/update-user.php?usr='+id+'&ord='+ord,
		success:function(html){
			if( html.trim() != 'error' ){
				$('#ord-addresses').html(html);
			}else{
				$('.error').remove();
				$('#site-content h2').after('<div class="error">' + orderErreurModifCompteClient + '</div>')
			}
		}
	});
}
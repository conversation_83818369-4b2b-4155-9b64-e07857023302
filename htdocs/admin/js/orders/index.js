/**	\file orders/index.js
 *	Ce fichier est utilisé sur la liste des commandes et gère le rechargement Ajax de la page.
 */
// Cette variable détermine si la liste des commandes est en cours de rechargement ou non
var ordersReloadInProgress;
var allOriginIndeterminate = false;
var timerReloadOrder = false;
var ajaxReloadOrder = false;

$(document).ready(
	function(){
		init_RiaDatepicker();
		// Actualise l'entête avec les statistiques de commande. La liste des commandes
		// est déjà remplie côté serveur et n'a pas besoin d'être actualisée en Ajax
		reload_lst_orders( 1, false, true );
	}
).delegate( // Sélecteur d'origine
	'#selectorigins .selectorview', 'click', function(){
		if($('#selectorigins .selector').css('display')=='none'){
			$('#selectorigins .selector').show();
		}else{
			$('#selectorigins .selector').hide();
		}
	}
).delegate( // Sélecteur d'origine
	'#selectorigins .selector a', 'click', function(){
		var attrOrigin = $(this).attr('name');

		if( attrOrigin=='all' || attrOrigin=='noorigin' || attrOrigin=='gescom' || attrOrigin=='web' ){
			$('[name="origin[]"]:checked').removeAttr('checked');
			$('#selectorigins .selectorview .left .view').html($(this).html());
			$('#selectorigins .selector').hide();
			
			var originSelect = $(this).attr('name');

			if (refresh_in_ajax){
				var first = true;
				$('input[type=hidden]').each(function(){
					if( $(this).attr('name')=='origin[]' ){
						if( first ){
							$(this).val( originSelect );
						}else{
							$(this).val('');
						}

						first = false;
					}
				});


				// Recharge les tableaux de statistiques par devise et la liste des commandes
				reload_lst_orders(1); 
			}

			update_url();
		}
	}
).delegate( // Sélecteur d'origine
	'#selectorigins input[type=checkbox]', 'click', function(){
		var parent = $(this).parent();

		if( parent.hasClass('parent') ){
			var is_checked = parent.find('[type=checkbox]').is(':checked');
			
			if( is_checked ){
				parent.parent().find('.child [type=checkbox]').attr('checked', 'checked');
			}else{
				parent.parent().find('.child [type=checkbox]').removeAttr('checked');
			}
		}

		reloadSelectorOrigin();
		clearTimeout( timerReloadOrder );
		timerReloadOrder = setTimeout(function(){
			// Recharge les tableaux de statistiques par devise et la liste des commandes
			reload_lst_orders(1);
		}, 1000);
	}
).delegate( // Filtre sur le mode de règlement
	'#selectpaytype .selectorview', 'click', function(){
		if($('#selectpaytype .selector').css('display')=='none'){
			$('#selectpaytype .selector').show();
		}else{
			$('#selectpaytype .selector').hide();
		}
	}
).delegate( // Filtre sur le mode de règlement
	'#selectpaytype .selector a', 'click', function(){
		$('#selectpaytype .selectorview .left .view').html($(this).html());
		$('#selectpaytype .selector').hide();
			
		$('#ord_pay_id').val( $(this).attr('name') );

		if (refresh_in_ajax){
			// Recharge les tableaux de statistiques par devise et la liste des commandes 
			reload_lst_orders(1);
		}

		update_url();
	}
).delegate( // Filtre sur le choix du dépôt
	'#selectdeposit .selectorview', 'click', function(){
		if($('#selectdeposit .selector').css('display')=='none'){
			$('#selectdeposit .selector').show();
		}else{
			$('#selectdeposit .selector').hide();
		}
	}
).delegate( // Filtre sur le choix du dépôt
	'#selectdeposit .selector a', 'click', function(){
		$('#selectdeposit .selectorview .left .view').html($(this).html());
		$('#selectdeposit .selector').hide();
			
		$('#ord_dps_id').val( $(this).attr('name') );

		if (refresh_in_ajax){
			// Recharge les tableaux de statistiques par devise et la liste des commandes 
			reload_lst_orders(1);
		}

		update_url();
	}
).delegate( // Filtre sur le représentant
	'#selectseller .selectorview', 'click', function(){
		if($('#selectseller .selector').css('display')=='none'){
			$('#selectseller .selector').show();
		}else{
			$('#selectseller .selector').hide();
		}
	}
).delegate( // Filtre sur le représentant
	'#selectseller .selector a', 'click', function(){
		$('#selectseller .selectorview .left .view').html($(this).html());
		$('#selectseller .selector').hide();
			
		$('#ord_seller_id').val( $(this).attr('name') );

		if (refresh_in_ajax){
			// Recharge les tableaux de statistiques par devise et la liste des commandes 
			reload_lst_orders(1);
		}

		update_url();
	}
).delegate( // Les boutons Dupliquer n'apparaissent qu'au survol de la ligne, pour réduire la charge mentale
	'#table-liste-commandes tbody tr', 'mouseenter', function(){
		if( $(window).width()>767 ){
			$( this ).find( '.ord-actions' ).show();
		}
	}
).delegate( // Les boutons Dupliquer n'apparaissent qu'au survol de la ligne, pour réduire la charge mentale
	'#table-liste-commandes tbody tr', 'mouseleave', function(){
		$( this ).find( '.ord-actions' ).hide();
	}
);

var parameters = new Array();
var last_function = '';

/** Cette fonction permet le rafraichissement des listes suite à la sélection d'une période différente
 */
function refresh_view(){
	var reg=new RegExp("[,]+", "g");
	var date = getRiaDate();
	date = date.toString();
	date = date.split(reg);
	
	var options = {year: 'numeric', month: 'short', day: 'numeric' };
	
	var tmp = date[0].split('/');
	var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
	var formated_date_1 = event.toLocaleDateString(document.documentElement.lang, options);
	
	tmp = date[1].split('/');
	var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
	var formated_date_2 = event.toLocaleDateString(document.documentElement.lang, options);

	if( date[0] == date[1] ){ 
		$('#riadatepicker .view').html(formated_date_1);
	}else{
		$('#riadatepicker .view').html(riadatepickerDateFromTo.replace('#param[date1]#' ,formated_date_1).replace('#param[date2]#' ,formated_date_2))
	}
	
	$('#riadatepicker .function_name').html(last_function)
	
	$('#riadatepicker .selectordate').hide();
	$('#riadatepicker .selector').hide();
	
	
	if( $('#date1').length > 0 ){	
		$('#date1').val(date[0]);	
	}else{
		$('#riadatepicker').after('<input type="hidden" id="date1" name="date1" value="'+date[0]+'"/>');
	}
	
	if( $('#date2').length > 0 ){
		$('#date2').val(date[1]);	
	}else{
		$('#riadatepicker').after('<input type="hidden" id="date2" name="date2" value="'+date[1]+'"/>');
	}
	
	if( $('#last').length > 0 ){
		$('#last').val(last_function);	
	}else{
		$('#riadatepicker').after('<input type="hidden" id="last" name="last" value="'+last_function+'"/>');
	}
	
	if( refresh_in_ajax ){
		// Recharge les tableaux de statistiques par devise et la liste des commandes
		reload_lst_orders(1);
	}else{
		var tempURL = $('#tabpanel form').attr('action');

		tempURL = removeParam( 'date1', tempURL );
		tempURL = removeParam( 'date2', tempURL );
		tempURL += ( tempURL.match(/\?/, 'g') ? '&' : '?' ) + 'date1=' + date[0] + '&date2=' + date[1];
		window.location.href = tempURL;
	}
	
	update_url();
}

/**	Cette fonction met à jour les liens de pagination avec la nouvelle période sélectionnée
 *	dans le sélecteur de période.
 */
function update_url(){
	var reg=new RegExp("[,]+", "g");
	var date = getRiaDate();
	date = date.toString().split(reg);
	
	$('#pagination a').each(function(){
		var reg=new RegExp("(state=[0-9])(&page=[0-9]).*", "g");
		var url = $(this).attr('href').replace(reg,'$1&amp;$2');
		
		$(this).attr('href',url+'&date1='+date[0]+'&date2='+date[1]);
	});
}

/** Fonction permettant l'initialisation du sélecteur de période
 */
function init_RiaDatepicker(){
	if( typeof autorefresh == 'undefined' ){
		return false;
	}
	
	// Si une recherche de commande est effectuée, on n'affiche pas la popup de nouvelle commande
	if( autorefresh==false && refresh_in_ajax==true && load_popup_new_orders==true ){
		if( !$('#infobulle').is('visible') ){
			setInterval("check_orders()", 60000);
		}
	}
	
	// Sélecteur de site
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});

	$('#riawebsitepicker .selector a').click(function(){
		if( $(this).attr('name') == 'w-0' ){
			$('#riawebsitepicker .selector a [type=checkbox]').attr('checked', 'checked');
		}
		
		var viewHtml = ''; 
		var allChecked = true;
		$('#riawebsitepicker .selector [type=checkbox]').each(function(){
			if( $(this).is(':checked') ){
				var val = $(this).parent().find('label').html();
				
				if( $.trim(val) != '' ){
					viewHtml += ( $.trim(viewHtml) != '' ? ', ' : '' ) + val;
				}
			}else{
				allChecked = false;
			}
		});

		if( allChecked ){
			$('#riawebsitepicker .selectorview .left .view').html('Tous les sites');
		}else if( $.trim(viewHtml) != '' ){
			if( viewHtml.length > 33 ){
				viewHtml = viewHtml.substring(0, 33) + '...';
			}

			$('#riawebsitepicker .selectorview .left .view').html( viewHtml );
		}

		clearTimeout( timerReloadOrder );
		timerReloadOrder = setTimeout(function(){

			// Recharge les tableaux de statistiques par devise et la liste des commandes
			reload_lst_orders(1);

		}, 1000);
	});
{
	var html = '';
	html += '	<div class="selectorview">';
	html += '		<div class="left">';
	html += '			<span class="function_name"></span>';
	html += '			<br/><span class="view"></span>';
	html += '		</div>';
	html += '		<a class="btn" name="btn">';
	html += '			<img src="/admin/images/stats/fleche.gif" height="8" width="16"/>';
	html += '		</a>';
	html += '		<div class="clear"></div>';
	html += '	</div>';
	html += '	<div class="selector">';
	html += '		<a name="perso">' + ordersPeriodePerso + '</a>';
	html += '		<a class="selector-sep" ></a>';
	html += '		<a name="today">' + ordersAujourdhui + '</a>';
	html += '		<a name="yesterday">' + ordersHier + '</a>';
	html += '		<a name="thisweek">' + orders7DerniersJours + '</a>';
	html += '		<a name="lastweek">' + ordersSemaineDerniere + '</a>';
	html += '		<a name="last2week">' + orders14DerniersJours + '</a>';
	html += '		<a name="last30days">' + orders30DerniersJours + '</a>';
	html += '		<a name="thismonth">' + ordersMoisCourant + '</a>';
	html += '		<a name="lastmonth">' + ordersMoisDernier + '</a>';
	html += '		<a name="firstjanuary">' + orders1Janvier + '</a>';
	html += '		<a name="lastyear">' + ordersAnneeDerniere + '</a>';
	html += '		<a class="selector-sep"></a>';
	html += '		<a name="all">' + ordersToutePeriode + '</a>';
	html += '	</div>';
	html += '	<div class="selectordate">';
	html += '		<div class="options"></div>';
	html += '		<input id="btn_submit" type="button" name="valider" value="' + ordersValider + '"/>';
	html += '		<input id="btn_cancel" type="button" name="annuler" value="' + ordersAnnuler + '"/>';
	html += '	</div>';
	$('#riadatepicker').html( html );
	$('#riadatepicker .selectordate').hide();
	
	$('#riadatepicker #btn_cancel').click(function(){	$('#riadatepicker .selectordate').hide();	});
	$('#riadatepicker #btn_submit').click(function(){	refresh_view();	});
	
	$('#riadatepicker [name=all]').click(function(){	
		$('#riadatepicker .options').DatePickerSetDate([date_all1,date_today], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker .selectorview').click(function(){
		show_selector();
		$('#riadatepicker .selectordate').hide();
	});
	$('#riadatepicker [name=today]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate(date_today);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=yesterday]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate(date_yesterday, true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=perso]').click(function(){ 
		last_function = $(this).html();
		show_selector();
		$('#riadatepicker .selectordate').show();
	});
	$('#riadatepicker [name=thisweek]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate([date_thisweek,date_today], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=lastweek]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate([date_lastMonday,date_lastSunday], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=last2week]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate([date_last2Monday,date_today], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=last30days]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate([date_last30days,date_today], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=thismonth]').click(function(){ 
		$('#riadatepicker .options').DatePickerSetDate([date_dmonth, date_emonth], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=lastmonth]').click(function(){
		$('#riadatepicker .options').DatePickerSetDate([date_dlastmonth, date_elastmonth], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=firstjanuary]').click(function(){
		$('#riadatepicker .options').DatePickerSetDate([date_firstjanuary, date_today], true);
		last_function = $(this).html();
		refresh_view();
	});
	$('#riadatepicker [name=lastyear]').click(function(){
		$('#riadatepicker .options').DatePickerSetDate([date_lastyear, date_elastyear], true);
		last_function = $(this).html();
		refresh_view();
	});

	$('#riadatepicker .view').html(init_view);
	$('#riadatepicker .function_name').html(init_action);
	$('#riadatepicker .options').DatePicker({
		flat: true,
		date: init_date,
		calendars: 1,
		format: 'd/m/Y',
		mode: 'range'
	});
}
}

/** Cette fonction permet de récupérer les variables passées dans l'url
 */
function extractUrlParams(){	
	var t = location.order.substring(1).split('&');
	var f = [];
	for (var i=0; i<t.length; i++){
		var x = t[ i ].split('=');
		f[x[0]]=x[1];
	}
	return f;
}	   

/**	Cette fonction va recharger en Ajax la liste des commandes, 
 *	en tenant compte des filtres de date et de source sélectionnés
 *	@param page Numéro de la page en cours de consultation
 *	@param showOriginSelector Le sélecteur d'origine doit-il être affiché ou non
 *	@param onlyStats Si true, seules les statistiques seront actualisées
 */
function reload_lst_orders(page, showOriginSelector, onlyStats){

	// Empêche deux chargements asynchrones en parallèle
	if( ajaxReloadOrder ){
		ajaxReloadOrder.abort();
	}

	if (typeof onlyStats == 'undefined') {
		onlyStats = false;
	}

	ordersReloadInProgress = true; // Empêchera l'infobulle d'apparaître si l'on est déjà en cours de rechargement
	
	var reg = new RegExp("[,]+", "g");
	
	var date = getRiaDate();
	date = date.toString().split(reg);
	var date1 = dateparse(date[0]);
	var date2 = dateparse(date[1]);
	
	var state = $('#state-h').val();
	if( state =='all' || !state) state = 0;
	state = state.toString().replace('w-','');

	var origin = '';

	if ($('[name="origin[]"]:checked').length) {
		$('[name="origin[]"]:checked').each(function(){
			origin += '&origin[]=' + $(this).val();
		});
	} else if ($.trim($('[type="hidden"][name="origin[]"]').val()) != '') {
		origin += '&origin[]=' + $('[type="hidden"][name="origin[]"]').val();
	}
	
	var pmtID = 0;
	if( typeof $('#pmt_id') != 'undefined' && $('#pmt_id').length ){
		pmtID = $('#pmt_id').val();
	}

	var param_wst = '';
	$('#riawebsitepicker .selector [type=checkbox]:checked').each(function(){
		var w = $(this).val();
		param_wst += '&wst[]=' + w;
	});

	var ordPayID = $('#ord_pay_id').val().replace('pay-', '');
	var ordDepositID = $('#ord_dps_id').val().replace('dps-', '');
	var ordSellerID = $('#ord_seller_id').val().replace('seller-', '');
	var ordMdlID = $('#ord_mdl_id').val();
	var ordFldID = $('#ord_fld_id').val();

	var ordRef = $('#ord_sch_ref').val();
	var ordPiece = $('#ord_sch_piece').val();

	if( autorefresh ){
		var get = extractUrlParams();
		
		var fct = last_function == '' ? (get['last'] ? get['last'] : ordersAujourdhui ) : last_function;
		
		var params = '?state=' + state + '&date1=' + date1 + '&date2=' + date2 + '&last=' + fct + param_wst + origin + '&pay=' + ordPayID + '&seller=' + ordSellerID + (!isNaN(pmtID) ? '&pmt_id=' + pmtID : '') + '&mdl=' + ordMdlID + '&fld=' + ordFldID + '&ref=' + ordRef + '&piece=' + ordPiece + '&dps=' + ordDepositID;
		window.location.href = window.location.href.replace(/(.*\.php).*/g,'$1') + params;
	}
	if( !autorefresh && refresh_in_ajax ){
		
		$('#lst_orders').fadeTo('fast', (onlyStats ? 1 : 0.1), function(){
		
			// Remplace les anciennes statistiques de commandes par des loaders (toutes devises)
			$('.table-synthese-order tbody td').html('<img src="/admin/images/loader2.gif" class="loader" title="' + msgLoading + '" />');

			// Remplace l'ancienne liste des commandes par un loader
			if (!onlyStats) {
				$('#lst_orders').html('<tr><td colspan="9" style="padding:5px;"><img class="loader" src="/admin/images/loader2.gif" title="' + msgLoading + '" /></td></tr>');
			}

			// Masque la pagination
			$('#pagination').hide();

			// Masque les boutons d'export
			$('#table-liste-commandes .js-btn-export').hide();

			// Masque la notification de nouvelle commande
			$('#infobulle').fadeOut('normal', function(){
				$('#infobulle').remove();
			});

			// Charge la nouvelle liste de commandes à afficher
			ajaxReloadOrder = $.ajax({
				type: "GET",
				url: 'js_lst_orders.php',
				data: 'state=' + state + '&date1=' + date1 + '&date2=' + date2 + '&page=' + page + origin + param_wst + '&pay=' + ordPayID + '&seller=' + ordSellerID + (!isNaN(pmtID) ? '&pmt_id=' + pmtID : '') + '&only_stats=' + (onlyStats ? '1' : '0') + '&mdl=' + ordMdlID + '&fld=' + ordFldID + '&ref=' + ordRef + '&piece=' + ordPiece+ '&dps=' + ordDepositID,
				dataType: 'xml',
				async: true,
				success: function(msg){
					// Gère en premier lieu le tableau de synthèse (Nb de commandes, CA, Marge brute)
					$(msg).find('synthese').each(function(){
						var currency = $(this).attr('currency');
						$('.table-synthese-order[data-currency*='+currency+'] tbody').html( $(this).text() );
						$('#orders-total-ht-' + currency + ' span').html( $(this).attr('total_ht') );
						$('#orders-total-ttc-' + currency + ' span').html( $(this).attr('total_ttc') );
					});
					
					// Masque la colonne de marge brute si non disponible
					if( $('#hd-order-margin').length && !$('#table-synthese-order tbody td[headers=hd-order-margin]').length ){
						$('#hd-order-margin').remove();
					} else if( !$('#hd-order-margin').length && $('#table-synthese-order tbody td[headers=hd-order-margin]').length ){
						$('#hd-order-avg-ttc').after( '<th id="hd-order-margin">Marge brute</th>' );
					}
					$('#state').html( $(msg).find('html_select').text() );

					// Met à jour la liste des commandes
					if (!onlyStats) {
						$('#lst_orders').html('');
						var html = '';

						$(msg).find('order').each(function(){

							const id = $(this).attr('id');
							const piece = $(this).attr('piece');

							var ord_id;
							if( id == piece ){
								ord_id = piece;
							}else if( piece !== "" ){
								ord_id = piece+' ('+id+')';
							}else{
								ord_id = id;
							}

							html += '<tr>';
							html += '<td headers="ord-id" id="td-ord-id" style="width: 200px">';
							html += $(this).text();
							html += ' <a title="' + ordersAfficherFicheCommande + '" href="order.php?ord='+id+'&amp;state=0">'+ord_id+'</a>';
							html += ' <br />';
							html += ' <ul>';
							html += '	<li class="ord-actions">';
							html += '		<a id="ord-duplicate" class="button" style="display: none">' + btnDupliquer + '</a>';
							html += '		<input type="hidden" name="ord-id" value="' + id + '" />';
							html += '	</li>';
							html += ' </ul>';
							html += '</td>';
							html += '<td headers="ord-date">'+$(this).attr('date')+'</td>';
							html += '<td headers="ord-inv">'+$(this).attr('adr_inv')+'</td>';
							html += '<td headers="ord-livr">'+$(this).attr('adr_livr')+'</td>';
							html += '<td headers="ord-type-pay">'+$(this).attr('pay_name')+'</td>';
							html += '<td headers="ord-state">'+$(this).attr('state')+'</td>';
							html += '<td align="right" headers="ord-ht">'+$(this).attr('ht')+'</td>';
							html += '<td align="right" headers="ord-ttc">'+$(this).attr('ttc')+'</td>';
							html += '</tr>'
						});
						$('#lst_orders').append( html );
						
						// Mise à jour de la sélection d'origine
						if( $(msg).find('choose_origin').length ){
							$('#selectorigins').html( $(msg).find('choose_origin').text() );
							if( showOriginSelector ){
								$('#selectorigins .selector').show();
							}
						}

						// Actualise le nombre de commandes actuellement présent dans la liste, pour permetre à la détection
						// de nouvelle commande de fonctionner correctement
						nb_show_orders = $(msg).find('number').attr('count');
						
						// Si aucune commande, affiche un message spécifique
						$(msg).find('noorder').each(function(){
							$('#lst_orders').append('<tr><td style="padding:5px;" colspan="8">' + ordersAcuneCommandeCritere + '</td></tr>');
						});
						
						// Gère la pagination
						$(msg).find('number').each(function(){
							var pages = '';
							if( parseInt($(this).attr('nbpage'))>1 ){
								if( page>1 ){ //page précédente
									pages += '<a href="#" onclick="reload_lst_orders('+(page-1)+');">&laquo; ' + ordersPrecedent + ' | </a> ';
								}
								for( var i=1; i <= parseInt($(this).attr('nbpage')); i++ ){
									if( i > page-5 && i < page+5 ){
										if( $(this).attr('actual')==i ){
											pages += '<b>'+i+'</b> | ';
										}else{
											pages += '<a href="#" onclick="reload_lst_orders('+i+');">'+i+'</a> | ';
										}
									}
								}
								if( page<parseInt( $(this).attr('nbpage') ) ){//page suivante
									pages += '<a href="#" onclick="reload_lst_orders('+(page+1)+');">' + ordersSuivantt + ' &raquo;</a> ';
								}
							}
							// La pagination n'apparaît que s'il y a plusieurs pages, sinon elle est masquée
							if( $(this).attr('nbpage')>1 ){
								$('#pagination').html('<td colspan="2" style="text-align: left">Page '+page+'/'+$(this).attr('nbpage')+'</td><td colspan="6">'+ pages + '</td>');
								$('#pagination').show();
							}else{
								$('#pagination').hide();
							}
						});
					}

					// Le bouton Exporter apparaît uniquement s'il y a des commandes.
					if (nb_show_orders <= 0) {
						$('#table-liste-commandes .js-btn-export').hide();
					} else {
						$('#table-liste-commandes .js-btn-export').show();
					}

					// Affiche la pagination si le nombre de commande dépasse 24
					if( nb_show_orders >= 25 ){
						$('#pagination').show();
					}
					
					ordersReloadInProgress = false; // Chargement terminé, l'info bulle peut de nouveau être utilisée
				},
				complete: function(){
					reloadSelectorOrigin();
					compareSizeBlocks();
				},
				error: function( xhr ){
                    if( xhr.status==401 ){
                        alert( ordersAlertDeconnecter );
						window.location.reload( window.location.href );
                    }else{
                        alert( ordersErreurEnregistrementCommande );
                    }
					ordersReloadInProgress = false; // Chargement terminé, l'info bulle peut de nouveau être utilisée
				}
			});
		});
		// Masque l'indicateur de chargement
		$('#lst_orders').fadeTo('fast',1, function(){
			$('div.loader').remove();
		});
	}
	// Masque la notification de nouvelle commande
	if( $('#infobulle').length ){
		$('#infobulle').remove();
	}
}

/** Permet la récupération de la date que l'utilisateur à choisi
 *	dans le sélecteur de période
 */
function getRiaDate(){
	return $('#riadatepicker .options').DatePickerGetDate(true);
}

/** Gère l'affichage du sélecteur de date 
 */
function show_selector(){
	if($('#riadatepicker .selector').css('display')=='none'){
		$('#riadatepicker .selector').show();
		$('#riawebsitepicker .selector').hide();
	}else if($('#riadatepicker .selector').css('display')=='block'){
		$('#riadatepicker .selector').hide();
	}
}

/** Cette fonction est lancée à intervalle régulier pour détecter la présence
 *	de nouvelles commandes et en informer l'utilisateur le cas échéant
 */
function check_orders(){
	// S'il s'agit d'une page de résultat de recherche de pièce, la popup n'est pas disponible
	var ordPiece = $('#ord_sch_piece').val();
	if ($.trim(ordPiece) != "") {
		return false;
	}
	
	// Si la liste des commandes est déjà en cours de rechargement, il n'est pas nécessaire
	// de contrôler la présence de nouvelles commandes.
	if( ordersReloadInProgress ){
		return;
	}

	if( refresh_in_ajax ){
		var reg=new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString().split(reg);
		
		var state = $('#state-h').val();
		if( state =='all' || !state) state = 0;
		state = state.toString().replace('w-','');
		
		var date1 = dateparse(date[0]);
		var date2 = dateparse(date[1]);
		
		var ordPayID = $('#ord_pay_id').val().replace('pay-', '');
		var ordDepositID = $('#ord_dps_id').val().replace('dps-', '');
		var ordSellerID = $('#ord_seller_id').val().replace('seller-', '');
		var ordMdlID = $('#ord_mdl_id').val();
		var ordFldID = $('#ord_fld_id').val();
		
		var pmtID = 0;
		if( typeof $('#pmt_id') != 'undefined' && $('#pmt_id').length ){
			pmtID = $('#pmt_id').val();
		}
		
		// Requête ajax permettant de récupérer le nombre de commandes 
		$.ajax({
			type: "GET",
			url: 'js_volume_orders.php',
			data: 'state='+state+'&date1='+date1+'&date2='+date2+'&pay='+ordPayID + '&seller=' + ordSellerID + (!isNaN(pmtID) ? '&pmt_id=' + pmtID : '') + '&mdl=' + ordMdlID + '&fld=' + ordFldID + '&dps=' + ordDepositID,
			success: function(msg){
				if($(msg).find('order').attr('volume')){
					const cpt = $(msg).find('order').attr('volume');
																
					// Affichage du nombre de commande(s) supplémentaire(s) s'il est supérieur au nombre de commandes actuellement affichées
					if( cpt > nb_show_orders ){
						var posXRiaDatePicker = $('#riadatepicker').position().left;
						var riaDatePickerWidth = $('#riadatepicker').width();
						var infobulleWidth = 280;
						var posXSynthese = $('#table-synthese-order').position().left;
						
						var text = oderscommandesSupplementaire.replace('#param[nb_commande]#',(cpt-nb_show_orders));
						if($('#infobulle').length == 0) {
							if( (posXRiaDatePicker+riaDatePickerWidth+infobulleWidth) < posXSynthese ){
								$('#table-synthese-order').before('<a id="infobulle" onclick="reload_lst_orders(1)" >'+text+'</a>');
							}else{
								$('.stats-menu .clear:last').before('<a id="infobulle" style="margin-top:5px;" onclick="reload_lst_orders(1)" >'+text+'</a>');
							}
							$('#infobulle').fadeIn('normal');
						}else{
							$('#infobulle').text(text);
						}
					}else{
						// Si l'infobulle est affichée, la masque
						$('#infobulle').remove();
					}
				}
			},
			complete: function(){
				reloadSelectorOrigin();
				compareSizeBlocks();
			}
		});
	}
}

/** Active / Désactive les origines utilisées pour filtrer la liste des commandes dans le sélecteur d'origine
 * 
 */
function reloadSelectorOrigin(){

	$('.selector .parent').each(function(){
		$(this).find('[type=checkbox]').prop("indeterminate", false).removeAttr('checked');
		
		var total_check = $(this).parent().find('.child [type=checkbox]').length;
		var total_is_checked = $(this).parent().find('.child [type=checkbox]:checked').length

		if( total_is_checked > 0 ){
			if( total_check != total_is_checked ){
				$(this).find('[type=checkbox]').prop( "indeterminate", true );
			}else{
				$(this).find('[type=checkbox]').attr('checked', 'checked');
			}
		}
	});
}
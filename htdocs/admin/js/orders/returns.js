	$(document).ready(function(){
		if( typeof $('#riadatepicker') != 'undefined' && $('#riadatepicker').length ){
			init_RiaDatepicker();
		}
	});

	var parameters = new Array();
	var last_function = '';
	
	//permet le rafraichissement des listes
	function refresh_view(){
		var reg=new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString();
		date = date.split(reg);

		var options = {year: 'numeric', month: 'short', day: 'numeric' };
	
		const tmp = date[0].split('/');
		var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
		const formated_date_1 = event.toLocaleDateString(document.documentElement.lang, options);
		
		tmp = date[1].split('/');
		var event = new Date(Date.UTC(tmp[2], tmp[1]-1, tmp[0], 0, 0, 0));
		const formated_date_2 = event.toLocaleDateString(document.documentElement.lang, options);

		
		if( date[0] == date[1] ){ 
			$('#riadatepicker .view').html(formated_date_1);
		}else{
			$('#riadatepicker .view').html(riadatepickerDateFromTo.replace('#param[date1]#' ,formated_date_1).replace('#param[date2]#' ,formated_date_2))
		}
		
		$('#riadatepicker .function_name').html(last_function)
		
		$('#riadatepicker .selectordate').hide();
		$('#riadatepicker .selector').hide();
		
		if($('#date1').length > 0){	
			$('#date1').val(date[0]);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="date1" name="date1" value="'+date[0]+'"/>');
		}
		
		if($('#date2').length > 0){	
			$('#date2').val(date[1]);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="date2" name="date2" value="'+date[1]+'"/>');
		}
		
		if($('#last').length > 0){	
			$('#last').val(last_function);	
		}else{
			$('#riadatepicker').after('<input type="hidden" id="last" name="last" value="'+last_function+'"/>');
		}
		
		update_url();
	}
	function update_url(param){
		if (! param) param = {};
		
		var reg=new RegExp("[,]+", "g");
		var date = getRiaDate();
		date = date.toString().split(reg);
		
		var get = extractUrlParams();
		if (param.state !== undefined) get.state = param.state;
		else if (! get.state) get.state = 0;
		
		if (date[0].substring(0, 3) === 'NaN') date[0] = $('#date-0').val();
		if (date[1].substring(0, 3) === 'NaN') date[1] = $('#date-1').val();
		if (! last_function) last_function = $('#period').val();
		
		location.href = 'returns.php?state='+get.state+'&date1='+date[0]+'&date2='+date[1]+'&period='+last_function;
	}
	//function permettant l'initialisation de la sélection de date
	function init_RiaDatepicker(){
		$('#riawebsitepicker .selectorview').click(function(){
			if($('#riawebsitepicker .selector').css('display')=='none'){
				$('#riawebsitepicker .selector').show();
				$('#riadatepicker .selector').hide();
			}else{
				$('#riawebsitepicker .selector').hide();
			}
		});
		$('#riawebsitepicker .selector a').click(function(){
			$('#riawebsitepicker .selectorview .left .view').html($(this).html());
			$('#riawebsitepicker .selector').hide();
			update_url();
		});
	
		var html = '';
		html += '	<div class="selectorview">';
		html += '		<div class="left">';
		html += '			<span class="function_name"></span>';
		html += '			<br/><span class="view"></span>';
		html += '		</div>';
		html += '		<a class="btn" name="btn">';
		html += '			<img src="/admin/images/stats/fleche.gif" height="8" width="16"/>';
		html += '		</a>';
		html += '		<div class="clear"></div>';
		html += '	</div>';
		html += '	<div class="selector">';
		html += '		<a name="perso">' + returnsPeriodePerso + '</a>';
		html += '		<a class="selector-sep" ></a>';
		html += '		<a name="today">' + returnsAujourdhui + '</a>';
		html += '		<a name="yesterday">' + returnsHier + '</a>';
		html += '		<a name="thisweek">' + returns7DerniersJours + '</a>';
		html += '		<a name="lastweek">' + returnsSemaineDerniere + '</a>';
		html += '		<a name="last2week">' + returns14DerniersJours + '</a>';
		html += '		<a name="thismonth">' + returnsMoisCourant + '</a>';
		html += '		<a name="lastmonth">' + returnsMoisDernier + '</a>';
		html += '		<a name="firstjanuary">' + returns1Janvier + '</a>';
		html += '		<a class="selector-sep"></a>';
		html += '		<a name="all">' + returnsToutePeriode + '</a>';
		html += '	</div>';
		html += '	<div class="selectordate">';
		html += '		<div class="options"></div>';
		html += '		<input id="btn_submit" type="button" name="valider" value="' + returnsValider + '"/>';
		html += '		<input id="btn_cancel" type="button" name="annuler" value="' + returnsAnnuler + '"/>';
		html += '	</div>';
		$('#riadatepicker').html( html );

		$('#riadatepicker .selectordate').hide();
		
		$('#riadatepicker #btn_cancel').click(function(){	$('#riadatepicker .selectordate').hide();	});
		$('#riadatepicker #btn_submit').click(function(){	refresh_view();	});
		
		$('#riadatepicker [name=all]').click(function(){	
			$('#riadatepicker .options').DatePickerSetDate([date_all1,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker .selectorview').click(function(){
			show_selector();
			$('#riadatepicker .selectordate').hide();
		});
		$('#riadatepicker [name=today]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate(date_today);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=yesterday]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate(date_yesterday, true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=perso]').click(function(){ 
			last_function = $(this).html();
			show_selector();
			$('#riadatepicker .selectordate').show();
		});
		$('#riadatepicker [name=thisweek]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_thisweek,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=lastweek]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_lastMonday,date_lastSunday], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=last2week]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_last2Monday,date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=thismonth]').click(function(){ 
			$('#riadatepicker .options').DatePickerSetDate([date_dmonth, date_emonth], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=lastmonth]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate([date_dlastmonth, date_elastmonth], true);
			last_function = $(this).html();
			refresh_view();
		});
		$('#riadatepicker [name=firstjanuary]').click(function(){
			$('#riadatepicker .options').DatePickerSetDate([date_firstjanuary, date_today], true);
			last_function = $(this).html();
			refresh_view();
		});
		
		$('#riadatepicker .view').html(init_view);
		$('#riadatepicker .function_name').html(init_action);
		const date_save = init_date.slice();
		$('#riadatepicker .options').DatePicker({
			flat: true,
			date: init_date,
			calendars: 1,
			format: 'd/m/Y',
			mode: 'range'
		});
		init_date = date_save.slice();
	}
	
	// Cette fonction permet de recuperer les variables passé dans l'url
	function extractUrlParams(){
		var t = String(document.location);
		var p = t.indexOf('?');
		if (p > -1) t = t.substring(p+1, t.length);
		t = t.split('&');
		var i, r = {};
		for (i in t) {
			t[i] = t[i].split('=');
			r[t[i][0]] = t[i][1];
		}
		return r;
	}
	
	// permet de recuperer la date que l'utilisateur à demandé
	function getRiaDate(){
		return $('#riadatepicker .options').DatePickerGetDate(true);
	}
	
	// afficahge du selecteur de date 
	function show_selector(){
		if($('#riadatepicker .selector').css('display')=='none'){
			$('#riadatepicker .selector').show();
			$('#riawebsitepicker .selector').hide();
		}else if($('#riadatepicker .selector').css('display')=='block'){
			$('#riadatepicker .selector').hide();
		}
	}

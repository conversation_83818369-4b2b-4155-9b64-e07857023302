
// check-all
var checkCascade = true;
var checkAllCascade = true;

var timerSel = 0;
function checkSel() {
	clearTimeout(timerSel);
	timerSel = setTimeout(function() {
		var count = 0;
		var check;
		$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { if ($(this).attr('checked')) { count++; check = $(this); } });
		var current_state = $('#current-state').val();
		
		if (current_state == 1) $('#return-returned-check-mode').removeAttr('disabled');
		$('#return-returned-check-reason, #return-returned-check-message, #return-returned-check-state').removeAttr('disabled');
		
		if (current_state == 1) $('#return-returned-check-mode').removeAttr('disabled');
		$('#return-returned-check-motif').removeAttr('disabled');
		$('#return-returned-check-reason, #return-returned-check-message, #return-returned-check-state').removeAttr('disabled');
		
		setTimeout(function() {
			if ($('#return-returned-check-reason').attr('checked') != (count == 1)) $('#return-returned-check-reason').click();
			if ($('#return-returned-check-message').attr('checked') != (count == 1)) $('#return-returned-check-message').click();
			if ($('#return-returned-check-mode').attr('checked') != (count == 1)) $('#return-returned-check-mode').click();
			if ($('#return-returned-check-state').attr('checked') != (count >= 1)) $('#return-returned-check-state').click();
			if ($('#return-returned-check-motif').attr('checked') != (count == 1)) $('#return-returned-check-motif').click();
			
			if (count == 0) $('#return-returned-check-reason, #return-returned-check-message, #return-returned-check-mode, #return-returned-check-state, #return-returned-check-motif').attr('disabled', 'disabled');
			
			if (count == 1) {
				var id = new RegExp('^return-returned-check-([0-9]+)$').exec(check.attr('id'))[1];
				var reason = $('#return-returned-reason-'+id).val();
				
				$('#reason-returned-'+((reason !== '') ? reason : 'other')).attr('checked', 'checked');
				$('#return-returned-message').val($('#return-returned-message-'+id).text());
				$('#mode-returned-'+$('#return-returned-mode-'+id).val()).attr('checked', 'checked');
				$('#return-returned-state').val($('#return-returned-state-'+id).val());
				$('#return-returned-motif').val($('#return-returned-motif-'+id).text());
			}
			
			$('#return-returned-up')[(count && current_state != 6) ? 'removeAttr' : 'attr']('disabled', 'disabled');
			$('#return-returned-del')[(count && current_state == 1) ? 'removeAttr' : 'attr']('disabled', 'disabled');
			
			evalMotifEnabled();
		}, 50);
	}, 1);
}

function checkAll() {
	if (! checkAllCascade) return false;
	checkAllCascade = false;
	var t = true;
	$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { t = t && $(this).attr('checked'); });
	if ($('#return-returned-check-all').attr('checked') != t) $('#return-returned-check-all').click();
	checkAllCascade = true;
	checkSel();
	return true;
}

function evalMotifEnabled() {
	var o = $('#return-returned-check-motif');
	var displayed = ($('#return-returned-state').val() == 5 && $('#return-returned-check-state'));
	$('#return-tr-motif')[(displayed) ? 'show' : 'hide']();
	
	setTimeout(function() {
		$('#return-returned-motif')[(o.attr('checked') && displayed) ? 'removeAttr' : 'attr']('disabled', 'disabled');
		if (o.attr('checked')) $('#return-returned-motif').focus();
	}, 1);
}

$(document).ready(function() {
	// check-all
	$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() {
		$(this).click(function() { return checkAll(); });
	});
	$('#return-returned-check-all').click(function() {
		if (! checkCascade) return false;
		checkCascade = false;
		var checked = $(this).attr('checked');
		$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { if ($(this).attr('checked') != checked) $(this).click(); });
		checkCascade = true;
		checkSel();
		return true;
	});
	
	// disable/enable
	$('#return-returned-check-reason').click(function() {
		var o = $(this);
		setTimeout(function() { var check = o.attr('checked'); $('#return-returned-reasons input[type="radio"]').each(function() { $(this)[check ? 'removeAttr' : 'attr']('disabled', 'disabled'); }); }, 1);
	});
	$('#return-returned-check-message').click(function() { var o = $(this); setTimeout(function() { $('#return-returned-message')[o.attr('checked') ? 'removeAttr' : 'attr']('disabled', 'disabled'); if (o.attr('checked')) $('#return-returned-message').focus(); }, 1); });
	$('#return-returned-check-mode').click(function() {
		var o = $(this);
		setTimeout(function() { var check = o.attr('checked'); $('#return-returned-modes input[type="radio"]').each(function() { $(this)[check ? 'removeAttr' : 'attr']('disabled', 'disabled'); }); }, 1);
	});
	$('#return-returned-check-state').click(function() {
		var o = $(this);
		setTimeout(function() {
			$('#return-returned-state')[o.attr('checked') ? 'removeAttr' : 'attr']('disabled', 'disabled');
		}, 1);
	});
	
	$('#return-returned-check-motif').click(evalMotifEnabled);
	$('#return-returned-state').change(evalMotifEnabled);
	
	$('#form-return').submit(function() {
		var state = $('#return-state').val();
		if (state == $('#current-state').val()) return true;
		return true;
	});
	
	checkSel();
});

$(document).ready(function(){

	if( typeof $('#brands') != 'undefined' && $('#brands').length ){
		riaSortable.create({
			'table'	: $('#brands'),
			'url'	: '/admin/ajax/products/prd-brands-position-update.php'
		});
	}

	
	function previewClick(preview, editZones){
		if (!editZones) {
			return;
		}
		
		var idStart = window.location.search.indexOf('&brd=') + 6; 
		var idStop = window.location.search.indexOf('&', idStart);
		var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
		displayPopup(orderBrandsCliqueZone, '', '/admin/documents/images/zones.php?image=' + $(preview).attr('id').replace(/^img/, '') + '&cls_id=5&obj_id_0=' + id, null, 756, 602);
	}

	$('.preview').click(function(event) {previewClick(event.currentTarget, $(this).hasClass('edit-zones'))});
});
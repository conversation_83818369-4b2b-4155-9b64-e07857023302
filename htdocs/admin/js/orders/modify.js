$(document).ready(function(){
    var ord = $("input#ord-id").val();

    if( $('#ord-products-articles').length ){
        riaSortable.create({
            'table': $('#ord-products-articles'),
            'url': '/admin/ajax/orders/ajax-order-products-position-update.php?ord=' + ord
        });
    }
})
.delegate(
    '#dlv-service-link', 'click', function(e){
        var url = 'ord_id='+$('input#ord-id').val();
        e.preventDefault();
        displayPopup('Sélectionner un service de livraison', '', '/admin/ajax/delivery/popup-delivery-services.php?'+url, '', 800);
    }
).delegate(
	'.display-ord-products-options', 'click', function(){
        var $elt = $(this).prop('id');
	    $('.menu-cols').each(function(){
            if ($elt != $(this).parent('td').children('.display-ord-products-options').prop('id')){
                $(this).hide();
            }
        });

        if ($(this).parent('td').children('.menu-cols').is(":visible")){
		    $(this).parent('td').children('.menu-cols').hide();
        } else {
		    $(this).parent('td').children('.menu-cols').show();
        }

		return false;
	}
).delegate(
    '.prd-add', 'click', function(event){
        event.preventDefault();
        var $row = $(this).parents('tr:eq(0)');
        if ($row.hasClass('ord-prd-row')){
            var $info = $row.children(".ord-prd-info");
            var uniqid = $info.children(".uniqid").val();
            var id = $info.children("input[name='prd["+uniqid+"]']").val();
            var line = $info.children("input[name='line["+uniqid+"]']").val();
            if(!$("input#target_id").length){
                $(this).parents('tbody:eq(0)').prepend('<input type="hidden" name="target[id]" id="target_id" value="'+id+'" />');
            } else {
                $("input#target_id").val(id);
            }
            if(!$("input#target_line").length){
                $(this).parents('tbody:eq(0)').prepend('<input type="hidden" name="target[line]" id="target_line" value="'+line+'" />');
            } else {
                $("input#target_line").val(line);
            }
        } else {
            if($("input#target_id").length){
                $("input#target_id").remove();
            }
            if($("input#target_line").length){
                $("input#target_line").remove();
            }
        }

        displayPopup('Sélectionner un produit', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0', '', 916);
    }
).delegate(
    '.prd-add-spacing', 'click', function(){
        var $row = $(this).parents('tr:eq(0)');
        if ($row.hasClass('ord-prd-row')){
            var $info = $row.children(".ord-prd-info");
            var uniqid = $info.children(".uniqid").val();
            var id = $info.children("input[name='prd["+uniqid+"]']").val();
            var line = $info.children("input[name='line["+uniqid+"]']").val();
        } 

        var ord = $("#ord-id").val();

        var target = "";
        if (id != undefined){
            target += "&target[id]="+id;
        }
        if (target != "" && line != undefined){
            target += "&target[line]="+line;
        }

        $.ajax({
            type: 'post',
            url: '/admin/ajax/orders/order-product-add.php',
            data: 'prd_id=0' + '&ord_id=' + ord + target,
            dataType: 'json',
            success: function(res){
                if (res.code == 100){
                    //Traitement de l'ajout de l'article effectué directement dans l'url, rafraichissement des produits
                    var $elt = $('.prd-add-spacing');
                    var srv_id = "NULL";
                    if ($("#ord-srv").length){
                        srv_id = $("#ord-srv").val();
                    }
                    var $row = $elt.parents('table').find('.order-products-row');
                    var $form = $('.prd-add-spacing').parents('form:eq(0)');
                    var formData = new FormData($form.get(0));
    
                    if (!$row.length || !$form.length) {
                        return;
                    }
                    
                    if (!$('.ord-prd-id').length) {
                        reloadOrdDlvServices(ord, srv_id);
                    }
                    reloadOrdPrds($row, formData);
                }
                if (res.code == 400){
                    //Erreur lors de l'ajout, affichage du bloc correspondant
                    if ($('#error-prd-add').length) {
                        $("#error-prd-add").html(res.response);
                    } else {
                        $('.error').remove();
                        $('.success').remove();
                        $("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
                    }
                }
            }
        });
    }
).delegate(
    '.prd-add-model', 'click', function(event){
        var $row = $(this).parents('tr:eq(0)');        
        if ($row.hasClass('ord-prd-row')){
            var $info = $row.children(".ord-prd-info");
            var uniqid = $info.children(".uniqid").val();
            var id = $info.children("input[name='prd["+uniqid+"]']").val();
            var line = $info.children("input[name='line["+uniqid+"]']").val();
            if(!$("input#target_id").length){
                $(this).parents('tbody:eq(0)').prepend('<input type="hidden" name="target[id]" id="target_id" value="'+id+'" />');
            } else {
                $("input#target_id").val(id);
            }
            if(!$("input#target_line").length){
                $(this).parents('tbody:eq(0)').prepend('<input type="hidden" name="target[line]" id="target_line" value="'+line+'" />');
            } else {
                $("input#target_line").val(line);
            }
        } else {
            if($("input#target_id").length){
                $("input#target_id").remove();
            }
            if($("input#target_line").length){
                $("input#target_line").remove();
            }
        }

        var ord = $("#ord-id").val();
        event.preventDefault();
        var url = 'ord_id='+ord+'&mdl_id=0';
        displayPopup('Sélectionner un modèle de commande', '', '/admin/ajax/orders/popup-order-models-selector.php?'+url, '', 916);
    }
).delegate(
    '.del-prd', 'click', function(){
        $(this).parents('.ord-prd-row').find('.checkbox').prop("checked", true);

        var $row = $(this).parents('table').find('.order-products-row');
        var $form = $('.prd-add-spacing').parents('form:eq(0)');
        var formData = new FormData($form.get(0));
		formData.append('del-ord-prd', '1');

        if (!$row.length || !$form.length) {
            return;
        }
        
        var ordPrds = [];
		$('.ord-prd-id:checked').each(function(){
            if ($(this).data('id')){
                ordPrds.push($(this).data('name'));
            } else {
                ordPrds.push("Interligne");
            }
		});

		var nb_products = $('.ord-prd-id').length;

		if (ordPrds.length && confirm('Êtes-vous sûr de vouloir supprimer les éléments suivants de la commande ? ' + ordPrds.join(', '))) {
			reloadOrdPrds($row, formData)

			if (ordPrds.length == nb_products){
				reloadOrdDlvServices($("input#ord-id").val(), "NULL");
			}
		} else {
            $(this).parents('.ord-prd-row').find('.checkbox').prop("checked", false);
        }
    }
).mouseup(function(e) {
    var container = $(".menu-cols");

    // if the target of the click isn't the container nor a descendant of the container
    if (!container.is(e.target) && container.has(e.target).length === 0 && !(e.target.classList.value.indexOf('display-ord-products-options') >= 0)){
        container.hide();
    }

})
.delegate(
    '.bl-dlv-service-link', 'click', function(e){
        var url = 'bl_id=' + encodeURIComponent($(e.currentTarget).data('bl'));
        e.preventDefault();
        displayPopup('Sélectionner un service de livraison', '', '/admin/ajax/delivery/popup-delivery-services.php?'+url, '', 800);
    }
)
.delegate(
    '.colissimo-generate-label', 'click', function(e){
        var $elt = $(e.currentTarget);
        var ord_id = $elt.data('ordId');
        var bl_id = $elt.data('blId');
        var colis = $elt.data('colis');
        var insurance = $elt.parent().find('.colissimo-generate-label-insurance').val();

        if (!ord_id || !bl_id) {
            return;
        }

        var params = '?ord_id=' + encodeURIComponent(ord_id) + '&bl_id=' + encodeURIComponent(bl_id);
        if (colis) {
            params += '&colis=' + encodeURIComponent(colis);
        }

        if (typeof insurance != 'undefined') {
            params += '&insurance=' + encodeURIComponent(insurance);
        }

        $elt
            .hide()
            .before('<img class="colissimo-generate-label-loader" alt="" src="/admin/images/loader2.gif" style="border: none" />')
            .parent()
                .find('.error,.success')
                    .remove();

        $.getJSON('/admin/ajax/delivery/colissimo-generate-label.php' + params)
            .done(function(data){
                $elt.parent().find('.colissimo-generate-label-loader').remove();
                $elt.show();

                if (data.hasOwnProperty('error')) {
                    $elt.before($('<span class="error"></span>').text(data.error));
                } else {
                    reloadOrdDlvServices();
                }
            })
            .error(function(){

                $elt.parent().find('.colissimo-generate-label-loader').remove();
                $elt.show();

                $elt.before($('<span class="error"></span>').text('Erreur lors de la génération de l\'étiquette'));
            });
    }
);

/** Cette fonction permet d'ajouter un produit à une commande en cours de modification.
 *	@param id Obligatoire, identifiant du produit à ajouter à la commande en cours de modification
 */
function parent_select_prd( id ){
    var ord = $("input#ord-id").val();
    var target = "";
    if ($("#target_id").length){
        target += "&target[id]="+$("#target_id").val();
    }
    if (target !== "" && $("#target_line").length){
        target += "&target[line]="+$("#target_line").val();
    }
    var srv_id = "NULL";
    if ($("#ord-srv").length){
        srv_id = $("#ord-srv").val();
    }
    $.ajax({
        type: 'post',
        url: '/admin/ajax/orders/order-product-add.php',
        data: 'prd_id=' + id + '&ord_id=' + ord + target,
        dataType: 'json',
        success: function(res){
			switch( res.code ){
				case "100": // Traitement de l'ajout de l'article effectué directement dans l'url, rafraichissement des produits

					var $elt = $('.prd-add');
					var $row = $elt.parents('table').find('.order-products-row');
					var $form = $('.prd-add').parents('form:eq(0)');
					var formData = new FormData($form.get(0));

					if (!$row.length || !$form.length) {
						return;
					}
					
					if (!$('.ord-prd-id').length) {
						reloadOrdDlvServices(ord, srv_id);
					}
					reloadOrdPrds($row, formData);
				
					break;
				case "200":
					
					 if (res.response.nomenclature == 1){
						// Popup nomenclature
						var url = 'prd_id=' + id;
						displayPopup(orderModifySelectNomenclature, '', '/admin/ajax/products/popup-nomenclature-selector.php?' + url, '', 500);
						return false;
					} else {
						// Popup conditionnement
						var url = 'prd_id=' + id;
						displayPopup(orderModifySlectConditionnement, '', '/admin/ajax/products/popup-colisage-selector.php?' + url, '', 916);
						return false;
					}
				
					break;
				case "400": // Erreur lors de l'ajout, affichage du bloc correspondant
				
					// Affiche le message d'erreur reçu du serveur
					if( $('#error-prd-add').length ){
						$("#error-prd-add").html(res.response);
					}else{
						$('.error').remove();
						$('.success').remove();
						$("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
					}
					
					// S'assure que le message d'erreur est bien visible de l'utilisateur
					$('html, body').animate({
						scrollTop: $("#error-prd-add").offset().top
					}, 2000);
				
					break;
			}
        }
    });
}

/**
 *	Cette fonction permet de sélectionner le conditionnement.
 */
function parent_select_colisage( prd_ids, colisage_ids, colisage_qtes ){
    var ord = $("input#ord-id").val();

    var target = "";
    if ($("#target_id").length){
        target += "&target[id]="+$("#target_id").val();
    }
    if (target !== "" && $("#target_line").length){
        target += "&target[line]="+$("#target_line").val();
    }

    var srv_id = "NULL";
    if ($("#ord-srv").length){
        srv_id = $("#ord-srv").val();
    }
    var prd_id = prd_ids.split(",").pop();

    $.ajax({
        type: 'post',
        url: '/admin/ajax/orders/order-product-add-with-colisages.php',
        data: 'prd_id=' + prd_id + '&ord_id=' + ord + '&colisage_ids=\'' + colisage_ids + '\'' + '&colisage_qtes=\'' + colisage_qtes + '\'' + target,
        dataType: 'json',
        success: function(res){
            if (res.code == 100) {
                //Traitement de l'ajout de l'article effectué directement dans l'url, rafraichissement des produits
                var $elt = $('.prd-add');
                var $row = $elt.parents('table').find('.order-products-row');
                var $form = $('.prd-add').parents('form:eq(0)');
                var formData = new FormData($form.get(0));

                if (!$row.length || !$form.length) {
                    return;
                }

                if (!$('.ord-prd-id').length) {
                    reloadOrdDlvServices(ord, srv_id);
                }
                reloadOrdPrds($row, formData);
            }
            if (res.code == 400){
                if ($('#error-prd-add').length) {
                    $("#error-prd-add").html(res.response);
                } else {
                    $('.error').remove();
                    $('.success').remove();
                    $("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
                }
            }
        }
    });
}

/**
 *	Cette fonction permet de sélectionner la nomenclature.
 */
function parent_select_nomenclature( prd_id, childs_ids, nomenclature_ids ){
    var ord = $("input#ord-id").val();

    var target = "";
    if ($("#target_id").length){
        target += "&target[id]="+$("#target_id").val();
    }
    if (target !== "" && $("#target_line").length){
        target += "&target[line]="+$("#target_line").val();
    }

    var srv_id = "NULL";
    if ($("#ord-srv").length){
        srv_id = $("#ord-srv").val();
    }
    $.ajax({
        type: 'post',
        url: '/admin/ajax/orders/order-product-add-with-nomenclature.php',
        data: 'prd_id=' + prd_id + '&ord_id=' + ord + '&childs_ids=\'' + childs_ids + '\'' + '&nomenclature_ids=\'' + nomenclature_ids + '\'' + target,
        dataType: 'json',
        success: function(res){
            if (res.code == 100) {
                //Traitement de l'ajout de l'article effectué directement dans l'url, rafraichissement des produits
                var $elt = $('.prd-add');
                var $row = $elt.parents('table').find('.order-products-row');
                var $form = $('.prd-add').parents('form:eq(0)');
                var formData = new FormData($form.get(0));

                if (!$row.length || !$form.length) {
                    return;
                }

                if (!$('.ord-prd-id').length) {
                    reloadOrdDlvServices(ord, srv_id);
                }
                reloadOrdPrds($row, formData);
            }
            if (res.code == 400){
                if ($('#error-prd-add').length) {
                    $("#error-prd-add").html(res.response);
                } else {
                    $('.error').remove();
                    $('.success').remove();
                    $("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
                }
            }
        }
    });
}

/**
 *	Cette fonction permet de sélectionner des produits via les modèles de commande.
 */
function parent_select_products_by_model( model_id, prd_ids, prd_qtes, prd_lines){
    var ord = $("input#ord-id").val();

    var target = "";
    if ($("#target_id").length){
        target += "&target[id]="+$("#target_id").val();
    }
    if (target !== "" && $("#target_line").length){
        target += "&target[line]="+$("#target_line").val();
    }

    var srv_id = "NULL";
    if ($("#ord-srv").length){
        srv_id = $("#ord-srv").val();
    }
    $.ajax({
        type: 'post',
        url: '/admin/ajax/orders/order-product-add-with-models.php',
        data: 'mdl_id=' + model_id + '&ord_id=' + ord + '&prd_ids=\'' + prd_ids + '\'' + '&prd_qtes=\'' + prd_qtes + '&prd_lines=\'' + prd_lines + '\'' + target,
        dataType: 'json',
        success: function(res){
            if (res.code == 100) {
                //Traitement de l'ajout de l'article effectué directement dans l'url, rafraichissement des produits
                var $elt = $('.prd-add');
                var $row = $elt.parents('table').find('.order-products-row');
                var $form = $('.prd-add').parents('form:eq(0)');
                var formData = new FormData($form.get(0));

                if (!$row.length || !$form.length) {
                    return;
                }

                if (!$('.ord-prd-id').length) {
                    reloadOrdDlvServices(ord, srv_id);
                }
                reloadOrdPrds($row, formData);
            }
            if (res.code == 400){
                if ($('#error-prd-add').length) {
                    $("#error-prd-add").html(res.response);
                } else {
                    $('.error').remove();
                    $('.success').remove();
                    $("#site-content h2").after('<div class="error" id="error-prd-add">'+res.response+'</div>');
                }
            }
        }
    });
}

function parent_select_delivery_service(srv_id, bl_id){
    reloadOrdDlvServices(undefined, srv_id, bl_id);
}

function reloadOrdDlvServices(ord, srv_id, bl_id){
    if ('undefined' === typeof ord) {
        ord = $('#ord-id').val();
    }

    var data = 'ord_id=' + encodeURIComponent(ord);
    if ('undefined' != typeof srv_id) {
        data += '&srv_id=' + encodeURIComponent(srv_id);
    }

    var changeBlService = typeof bl_id != 'undefined';
    if (changeBlService) {
        if (bl_id !== null) {
            data += '&bl_id=' + encodeURIComponent(bl_id);
        }
    } else {
        var prev_srv_id = "NULL";
        if ($("#ord-srv").length){
            prev_srv_id = $("#ord-srv").val();
        }
    }

    $.ajax({
        type: changeBlService && bl_id !== null || prev_srv_id != srv_id ? 'post' : 'get',
        url: '/admin/orders/order/delivery.php',
        data: data,
        success: function(html){
            if( html.trim() != 'error' ){
                $('.error').remove();
                $('.success').remove();
                $(".delivery-block, #delivery-row").remove();
                if($("#delivery-header").length) {
                    $("#delivery-header")
                        .after(html)
                        .remove();
                } else {
                    if (!$("input#result-text").length){
                        $('#ord-adresses-row').after(html);
                    } else {
                        $("input#result-text").replaceWith(html);
                    }
                }

                if($("input#result-text").length && (changeBlService || prev_srv_id != srv_id)) {
                    $('#site-content h2').after($('<div class="success"></div>').text($("input#result-text").val()));
                }
            }else{
                $('.error').remove();
                $('.success').remove();
                $('#site-content h2').after('<div class="error">' + orderModifyErreurAssignationServiceLivraison + '</div>');
            }
        }
    });
}
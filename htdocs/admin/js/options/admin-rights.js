/** \file options/admin-rights.js
 *  Active des fonctionnalités complémentaires sur les écrans de gestion des droits d'accès au back-office.
 *  Appelé uniquement par ce fichier : /options/rights.php
 */
$(document).ready(function(){

    //Permet de cocher les checkbox correspondant aux droits autorisés pour ce site
    var usr = $('#usr-id').length ? $('#usr-id').val() : 0;

    var urlAjax = "/admin/options/check-rights.php?usr="+usr;
    let searchParams = new URLSearchParams(window.location.search)
    if (searchParams.has("wst_id")) {
        urlAjax += "&wst_id=" + searchParams.get("wst_id");
    }
    
    $.ajax( urlAjax )
    .done(function(data) {
        var rights = JSON.parse(data);
        for(var i in rights){
            set_checked($('input#'+rights[i]));
        }

        //Permet de disabled les droits qui dépendent d'un autre droit non coché
        $('input:not(:checked)').each(function(){
            check_dependency($(this).attr('id'));
        });

        verif_parent();//Permet de mettre en état indeterminé les droits dont leurs enfants ne sont pas tous cochés
    });

    // Fonction appelée lors du clic sur Tout décocher
    $(".uncheck_all").click(function(e){
        e.preventDefault();
        
        var module = $(this).attr('module');
        set_unchecked( $('input[module="'+module+'"]'));
    });

    // Fonction appelée lors du clic sur Tout cocher
    $(".check_all").click(function(e){
        e.preventDefault();

        var module = $(this).attr('module');
        
        $('input[module="'+module+'"][depend-on]:disabled').each(function(){
            var depend_on = $(this).attr('depend-on');

            var rights = depend_on.split(' ');  

            var rights_checked = 0;
            for(var i= 0; i < rights.length; i++){
                if($('#'+rights[i]).attr('module') == module){
                    rights_checked ++;
                }
            }

            if( ($(this).attr('depend-on-all') == 1 && rights_checked == rights.length) || ($(this).attr('depend-on-all') == 0 && rights_checked != 0) ){
                $(this).prop('disabled', false);
                enable_child($('input[parent="'+$(this).attr('id')+'"]'));
            }
        });

        set_checked($('input[module="'+module+'"]'));
    });


    $("input").click(function(){
        var id = $(this).attr('id');

        if(this.checked){
            check_child(id);
        }else{
            uncheck_child(id);
        }

        check_dependency(id);//Permet de disable/enable les droits qui dépendent du droit "id"

        verif_parent();//Permet de mettre en état indeterminé les droits dont leurs enfants ne sont pas tous cochés
    });

    // Permet de disable/enable les droits dépendant du droit passé en paramètre
    function check_dependency(right){

        $('[depend-on~="'+right+'"]').each(function(){
            var depend_on = $(this).attr('depend-on');

            var rights = depend_on.split(' ');

            $(this).prop('disabled', false);
            $(this).next().attr('title', $(this).attr('desc'));
            enable_child($('input[parent="'+$(this).attr('id')+'"]'));          
            
            var rights_checked = 0;
            var title = 'Ce droit dépend de';
            for(var i= 0; i < rights.length; i++)
            {
                if($('#'+rights[i]).prop('checked') || $('#'+rights[i]).prop('indeterminate')){
                    rights_checked ++;
                }else{
                    title = title + ' "' + $('#'+rights[i]).next().text() + '",';
                }
            }

            title = title.substr(0, title.length-1);

            if(rights_checked == 0 || (rights_checked<rights.length && $(this).attr('depend-on-all') == 1)){
                $(this).prop('disabled', true);
                $(this).next().attr('title', title);
                set_unchecked($(this));
                disable_child($('input[parent="'+$(this).attr('id')+'"]'), title);
            }

            //Permet de disable les droits parent dont tous leurs droits enfants sont disable
            parent = $(this).attr('parent');
            if(parent){
                var total_check = $('input[parent="'+parent+'"]:enabled').length;
                if(!total_check){
                    $('#'+parent).prop('disabled', true);
                    set_unchecked($('#'+parent));
                }else{
                    $('#'+parent).prop('disabled', false);
                }
            }
            
        });
    }

    // Permet de mettre les checkbox des droits ayant des enfants dans l'état indeterminate si l'un d'eux est décoché
    function verif_parent(){
        var max_depth = $('#max-depth').val();

        // Il faut commencer par vérifier les checkbox les plus profondes dans l'arborescence
        for (var i = max_depth; i >= 0; i--) { 
            $('.has_child_'+i).each(function(){
                var id = $(this).attr('id');

                var total_check = $('input[parent="'+id+'"]:enabled').length;
                var total_is_checked = $('input[parent="'+id+'"]:checked').length;
                var indeterminate = $('input[parent="'+id+'"]:indeterminate').length;
               
                total_is_checked = total_is_checked - indeterminate;
             
                if( total_is_checked > 0 ){
                    if( total_check != total_is_checked ){
                        set_indeterminate($(this));
                    }else{
                        set_checked($(this));
                    }
                }else if(indeterminate > 0){
                    set_indeterminate($(this));
                }else{
                    set_unchecked($(this));
                }
            });
        }
    }

    function set_checked(input){
        input.each(function(){
            if(!$(this).prop('disabled') ){
                $(this).prop( "indeterminate", false );
                $(this).attr('checked', true);
                check_dependency($(this).attr('id'));
            }
        });  
    }

    function set_unchecked(input){
        input.each(function(){
            $(this).prop( "indeterminate", false );
            $(this).attr('checked', false);
            check_dependency($(this).attr('id'));
        }); 
    }

    function set_indeterminate(input){
        input.each(function(){
            if(!$(this).prop('disabled')){
                $(this).attr('checked', false);
                $(this).prop( "indeterminate", true );
                check_dependency($(this).attr('id'));
            }
        }); 
    }

    function uncheck_child(id){
        if(typeof id !== 'undefined'){
            set_unchecked( $('input[parent="'+id+'"]')); //Décoche les enfants
            
            $('input[parent="'+id+'"]').each(function(){ //Décoche les enfants des enfants
                uncheck_child($(this).attr('id'));
            });
        }    
    }

    function check_child(id){
        if(typeof id !== 'undefined'){
            set_checked( $('input[parent="'+id+'"]')); //Coche les enfants

            $('input[parent="'+id+'"]').each(function(){ //Coche les enfants des enfants
                check_child($(this).attr('id'));
            });
        }
    }

    function enable_child(inputs){
        inputs.each(function(){
            $(this).prop('disabled', false);
            $(this).next().attr('title', $(this).attr('desc'));
            enable_child($('input[parent="'+$(this).attr('id')+'"]'));//enable les enfants des enfants
        });
    }

    function disable_child(inputs, title){
        inputs.each(function(){
            $(this).prop('disabled', true);
            $(this).next().attr('title', title);
            set_unchecked($(this));
            disable_child($('input[parent="'+$(this).attr('id')+'"]'), title);//disable les enfants des enfants
        });
    }

    $('#import').click(function(){
        set_unchecked($('input:checkbox'));
        var wst = $('#wst').val();
        var tnt = $( "#wst option:selected" ).attr('tnt');
        if(wst && tnt){
            $.ajax( "/admin/options/check-rights.php?wst="+wst+"&tnt="+tnt )
            .done(function(data) {
                var rights = JSON.parse(data);
                
                for(var i in rights){
                    $('input#'+rights[i]).prop('disabled', false);
                    set_checked($('input#'+rights[i]));
                }
        
                verif_parent();//Permet de mettre en état indeterminé les droits dont leurs enfants ne sont pas tous cochés
            });
        }
    });

    $('.check_all_rights').click(function(e){
        e.preventDefault();
        $('input:checkbox').prop('disabled', false);
        $('input:checkbox').prop('checked', true);
    });

    $('.uncheck_all_rights').click(function(e){
        e.preventDefault();
        set_unchecked($('input:checkbox'));
    });

    //Permet de check les checkbox dans l'état indeterminate juste avant l'envoi du formulaire
    $('#form_rights').submit(function() {
        $('input:indeterminate').each(function(){
            $(this).prop('checked', true);
        });
        return true;
    });

    $('#form-edit-user').submit(function(){
        $('#rights input:indeterminate').each(function(){
            $(this).prop('checked', true);
        });
        return true;
    });

    // Bloc de tri par website
    // Start
	$('#riawebsitepicker .selectorview').click(function(){
        if($('#riawebsitepicker .selector').css('display')=='none'){
            $('#riawebsitepicker .selector').show();
        }else {
            $('#riawebsitepicker .selector').hide();
        }
    });

    $('#riawebsitepicker .selector a').click(function(){
        // Récupère le numéro derrière le - dans le name qui est sous la forme w-75
        const wst_id = $(this).attr('name').split("-")[1]; // Passage du wst_id pour recharger la page
        document.location.href = "/admin/options/rights.php?wst_id=" + wst_id;
    });
    // End

});
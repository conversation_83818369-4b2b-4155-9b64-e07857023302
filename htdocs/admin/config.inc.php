<?php
	/// Numéro de version des fichiers CSS pour forcer le rechargement
	define( 'ADMIN_ASSET', 'v-64');

	require_once('http.inc.php');

	// Protection contre les attaques de type CSRF
	http_check_referrer();

	// Fichier de configuration de l'interface d'administration.
	require_once($_SERVER['DOCUMENT_ROOT'].'/config.inc.php');

	{ // Intialisation de l'environnment pour la gestion de l'internationalisation
		$accepted_languages = array('fr_FR', 'en_GB', 'de_DE');

		// Le choix de la langue se fait dans cette priorité :
		//		1 - Si l'administrateur est connecté on essaye de récupérer l'information dans usr_lng_code ($_SESSION['lang']);
		//		2 - Si non renseigné on récupère la langue du navigateur
		//		3 - Si la langue du navigateur n'est pas supporté, on prendre alors l'anglais

		$lang = 'en_GB';

		if( isset($_SESSION['lang']) && !empty($_SESSION['lang']) && in_array($_SESSION['lang'], $accepted_languages) ){
			$lang = $_SESSION['lang'];
		}elseif( !empty($_SERVER['HTTP_ACCEPT_LANGUAGE']) ){
			// Version longue de la langue du navigateur
			$lang_browser_long = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 5);

			if( in_array($lang_browser_long, $accepted_languages) ){
				$lang = $lang_browser_long;
			}else{
				// Version courte de la langue du navigateur
				$lang_browser = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);

				switch( $lang_browser ){
					case 'fr':
						$lang = 'fr_FR';
						break;
					case 'en':
						$lang = 'en_GB';
						break;
					case 'de':
						$lang = 'de_DE';
						break;
				}
			}
		}

		$_SESSION['lang'] = $lang;

		putenv('LC_MESSAGES='.$lang.'.utf8');

		setlocale(LC_MESSAGES, $lang.'.utf8');

		bindtextdomain('riashop', __DIR__.'/_locale');
		textdomain('riashop');
		bind_textdomain_codeset('riashop','UTF-8');
	}

	// N'autorise l'accès à l'admin qu'aux seuls administrateurs.
	if( $_SERVER['PHP_SELF'] != $config['site_uri'].'/admin/login.php' ){
		// Fonction "Se souvenir de moi".
		if( !isset($_SESSION['usr_id']) && isset($_COOKIE['remember']) ){
			gu_users_reconnect();
		}

		$profiles = array(PRF_ADMIN);
		if( ria_array_get($config, 'seller_admin_access', false) ){
			$profiles[] = PRF_SELLER;
		}

		// Si l'utilisateur n'est pas connecté :
		if( !isset($_SESSION['usr_id']) || !isset($_SESSION['usr_prf_id']) || !in_array($_SESSION['usr_prf_id'], $profiles) ){
			if( isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH']=='XMLHttpRequest' ){
				// Si la requête est de type Ajax, la redirection vers la page de connexion ne servira à rien.
				// Envoie plutôt une réponse qui permettra d'informer l'absence de connexion
				header( 'HTTP/1.0 401 Unauthorized' );
				header( 'Content-Type: application/xml' );
				echo '<?xml version="1.0" encoding="utf8"?>';
				echo '<error code="401" msg="'._('Votre session est terminée, vous avez été déconnecté(e). Veuillez vous authentifier à nouveau.').'" />';
				exit;
			}else{
				// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
				header('Location: '.$config['site_uri'].'/admin/login.php?dest='.urlencode($_SERVER['REQUEST_URI']));
				exit;
			}
		}

		if( isset($config['msg_types_used']) && (is_array($config['msg_types_used']) && sizeof($config['msg_types_used'])) ){
			$config['msg_types_used'][] = 'DIRECT_CONTACT';
		}
		$config['USER_RIASTUDIO'] = false;
		if( $_SESSION['usr_tnt_id'] == 0 && (strstr($_SESSION['usr_email'], '@riastudio.fr') || strstr($_SESSION['usr_email'], '@kontinuum.fr') || strstr($_SESSION['usr_email'], '@yuto.com')) ){
			$config['USER_RIASTUDIO'] = true;
		}

		if( $_SESSION['usr_prf_id'] == PRF_SELLER ){
			$_SESSION['ord_seller_id'] = $_SESSION['usr_seller_id'];
			if( isset($_GET['sellers']) ){
				$_GET['sellers'] = $_SESSION['ord_seller_id'];
			}
		}

		if( gu_users_is_tenant_linked($_SESSION['usr_id'], $_SESSION['usr_email']) && !in_array($config['tnt_id'], array(16,1)) ){
			$config['price_watching_active'] = false;
		}

		
		$config['admin_submenu_state'] = 1; // Par défaut le sous-menu est ouvert
		
		// On récupère les variables de config 'admin-list-prds-cols' et 'admin_submenu_paneling_state'
		$code_override = array('admin-list-prds-cols', 'admin_submenu_paneling_state');
		$r_val = cfg_overrides_get(0, array(), $code_override, $_SESSION['usr_id'] );
		$nb_overrides = $r_val ? mysql_num_rows($r_val) : 0;
		if( $nb_overrides ){
			while( $val = mysql_fetch_assoc($r_val) ){
				if( $val['code'] == 'usr-admin-list-prds-cols' ){
					$_SESSION['usr-admin-list-prds-cols'] = explode(',', ria_mysql_result( $val, 0, 'value' ) );
				}else if( $val['code'] == 'admin_submenu_paneling_state' ){
					// Si on a un état de sous-menu enregistré, on le récupère
					$config['admin_submenu_state'] = $val['value'];
				}
			}
			mysql_data_seek( $r_val, 0 );
		}
		load_list_cols_products();

		require_once('view.admin.inc.php');

		// vérifier les variables de période
		if( isset($_SESSION['datepicker_period']) ){
			$period = $_SESSION['datepicker_period'];
			$date1 = isset($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : date('d/m/Y');
			$date2 = isset($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : date('d/m/Y');

			if( $date1==$date2 && $date1==date('d/m/Y', strtotime('-1 day')) ){
				$period = $_SESSION['datepicker_period'] = 'Hier';
			} else {
				switch( $period ){
					case 'Aujourd\'hui' :
						$date1 = $date2 = date( 'd/m/Y' ); break;
					case 'Hier' :
						$date1 = $date2 = date( 'd/m/Y', strtotime('-1 day') ); break;
					case 'Les 7 derniers jours' :
						$date1 = date( 'd/m/Y', strtotime('-7 day') ); $date2 = date( 'd/m/Y' ); break;
					case 'La semaine dernière' :
						$date1 = date( 'd/m/Y', strtotime('-2 Monday') ); $date2 = date( 'd/m/Y', strtotime('last Sunday') ); break;
					case 'Les 14 derniers jours' :
						$date1 = date( 'd/m/Y', strtotime('-14 day') ); $date2 = date( 'd/m/Y' ); break;
					case 'Ce mois-ci' :
						$date1 = date( '01/m/Y' ); $date2 = date( date( 't', mktime(0, 0, 0, date('m'), 1, date('Y'))).'/m/Y' ); break;
					case 'Le mois dernier' :
						$d = date('d');
						if( $d==31 ){
							$date1 = date( '01/m/Y', strtotime('-1 month')-86400 );
							$date2 = date( date( 't', mktime(0, 0, 0, date('m', strtotime('-1 month')-86400), 1, date('Y', strtotime('-1 month')-86400))).'/m/Y', strtotime('-1 month')-86400 );
						} else {
							$date1 = date( '01/m/Y', strtotime('-1 month') );
							$date2 = date( date( 't', mktime(0, 0, 0, date('m', strtotime('-1 month')), 1, date('Y', strtotime('-1 month')))).'/m/Y', strtotime('-1 month') );
						}
						break;
					case 'Depuis le 1er janvier' :
						$date1 = date( '01/01/Y' ); $date2 = date( 'd/m/Y' ); break;
				}
			}

			$_SESSION['datepicker_date1'] = $date1;
			$_SESSION['datepicker_date2'] = $date2;
		} else {
			unset( $_SESSION['datepicker_date1'] );
			unset( $_SESSION['datepicker_date2'] );
		}
	}
	// Ajout d'une constante permettant de savoir qu'on est dans l'administration
	define('CONTEXT_IS_ADMIN', true);

	// Définit la devise monétaire du client à partir de la catégorie tarifaire par défaut.
	if( $prc_id = $config['default_prc_id'] ){
		$prc = ria_mysql_fetch_assoc(
			prd_prices_categories_get($prc_id)
		);

		$_SESSION['admin_currency'] = $prc['money_code'];
	}

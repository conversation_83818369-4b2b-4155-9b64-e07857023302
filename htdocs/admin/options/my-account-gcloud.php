<?php

	/**	\file my-account.php
	 *	Cette page permet la mise à jour des informations personnelles du compte ainsi que la mise à jour de son mot de passe.
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_ACCOUNT');

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Paramètre du formulaire lié à Yuto VEL
	$params_form = '';
	if( isset($_GET['subscribtion']) ){
		$params_form .= (trim($params_form) != '' ? '&amp;' : '').'subscribtion=1';
	}
	if( isset($_GET['type']) ){
		$params_form .= (trim($params_form) != '' ? '&amp;' : '').'type='.$_GET['type'];
	}
	if( isset($_GET['licences']) ){
		$params_form .= (trim($params_form) != '' ? '&amp;' : '').'licences='.$_GET['licences'];
	}
	if( isset($_GET['step_max']) ){
		$params_form .= (trim($params_form) != '' ? '&amp;' : '').'step_max='.$_GET['step_max'];
	}
	if( isset($_GET['opt-multi-device']) ){
		$params_form .= '&amp;opt-multi-device='.$_GET['opt-multi-device'];
	}

	// Charge les informations du compte
	$user = ria_mysql_fetch_assoc(gu_users_get($_SESSION['usr_id'], '', '', $config['admin_access_profiles']));

	// Charge l'adresse postal du compte
	$adr = ria_mysql_fetch_assoc(gu_adresses_get($user['id'], $user['adr_invoices']));

	// Déclanche l'enregistre des informations dans le process d'abonnement Yuto
	if( isset($_POST['save-sub']) ){
		$_POST['save'] = 1;
	}

	// Gestion des comptes créé pour Yuto VEL
	if( $admin_account->getLastname() == 'nc' ){
		$admin_account->setLastname('')
			->setFirstname('')
			->setCivility(0);

		$adr['society']  = '';
		$adr['siret'] 	 = '';
		$adr['address1'] = '';
		$adr['zipcode']  = '';
		$adr['city']	 = '';
	}

	$error = array();

	// Enregistrement des informations sur le compte administrateur
	if( isset($_POST['save']) ){
		if( isset($_POST['dob']) ){
			if( !isdate($_POST['dob']) ){
				$birthday = '';
			}else{
				$birthday = dateparse($_POST['dob']);
			}
		}

		if( isset($_POST['password1']) && trim($_POST['password1']) != '' ){
			if( !isset($_POST['password2']) || $_POST['password2'] != $_POST['password1'] ){
				$error[] = _('Les deux mots de passe saisis sont différents.') . '<br />' . _('Veuillez les saisir à nouveau.');
			}
		}

		if( !isset($_POST['firstname'], $_POST['lastname'], $_POST['society'], $_POST['siret'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city']) ){
			$error[] = _('Une ou plusieurs informations obligatoires sont manquantes.');
		}

		if( !count($error) ){
			if( !isset($_POST['title']) || trim($_POST['title']) == '' ){
				$error[] = _('Merci de renseigner votre civilité.');
			}
			if( trim($_POST['firstname']) == '' ){
				$error[] = _('Merci de renseigner votre prénom.');
			}
			if( trim($_POST['lastname']) == ''){
				$error[] = _('Merci de renseigner votre nom de famille.');
			}
			if( trim($_POST['society']) == '' ){
				$error[] = _('Merci de renseigner le nom de votre société.');
			}

			if( trim($_POST['siret']) == '' ){
				$error[] = _('Merci de renseigner votre SIRET.');
			}

			if( !validSIRET($_POST['siret']) ){
				$error[] = _('Votre SIRET est invalide.');
			}
			if( trim($_POST['address1']) == '' || !iszipcode($_POST['zipcode']) || trim($_POST['city']) == '' ){
				$error[] = _('Merci de renseigner votre adresse postale.');
			}
			if( trim($_POST['email']) == '' ){
				$error[] = _('Merci de renseigner votre adresse email.');
			}
		}

		if( !count($error) ){
			$last_email = $admin_account->getEmail();

			$admin_account->setCivility($_POST['title'])
				->setFirstname($_POST['firstname'])
				->setLastname($_POST['lastname'])
				->setPhone($_POST['phone'])
				->setMobile($_POST['mobile'])
				->setEmail($_POST['email']);

			// Mise à jour du fax seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($_POST['fax']) ){
				$admin_account->setFax($_POST['fax']);
			}

			// Mise à jour du numéro en journée seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($_POST['phone-work']) ){
				$admin_account->setWork($_POST['phone-work']);
			}

			// Mise à jour de la langue seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($_POST['language']) ){
				$admin_account->setLang($_POST['language']);
			}

			// Mise à jour de la date d'anniversaire seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($birthday) ){
				$admin_account->setBirthday($birthday);
			}

			// Mise à jour du mot de passe seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($_POST['password1']) && trim($_POST['password1']) != '' ){
				$admin_account->setPassword($_POST['password1']);
			}

			$result = $admin_account->save();

			// Mise à jour de la langue de l'interface seulement si on est pas dans le processus d'inscription à Yuto Essentiel
			if( isset($_POST['language']) && in_array($_POST['language'], array('fr_FR', 'en_GB', 'de_DE')) ){
				$_SESSION['lang'] = $_POST['language'];
			}

			if( $result === -1 ){
				$error[] = _('Une erreur est survenue lors de la mise à jour de votre compte administrateur.').'<br />'._('(Code : ERR00001)');
			}else{
				// Mise à jour du compte administrateur chez le locataire
				gu_users_update_email($user['id'], $_POST['email']);
				gu_adresses_update($user['id'], $adr['id'], $adr['type_id'], $_POST['title'], $_POST['firstname'], $_POST['lastname'], $_POST['society'], $_POST['siret'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], $adr['country'], $_POST['phone'], (isset($_POST['fax']) ? $_POST['fax'] : $adr['fax']), $_POST['mobile'], (isset($_POST['phone-work']) ? $_POST['phone-work'] : $adr['phone_work']), false, null, null, null, $adr['address3'], null, null);
			}
		}

		// Si l'on dispose d'un abonnement Yuto (en période d'essai ou non), alors on met à jour les informations du compte sur RiaStudio
		$subscribtion = dev_subscribtions_yuto_get(true);
		if( is_array($subscribtion) && count($subscribtion) ){
			$user_sub = $subscribtion['usr_id'];

			if( is_numeric($user_sub) && $user_sub > 0 ){
				$old_config = $config;
				$old_ria_db_connect = $ria_db_connect;

				RegisterGCPConnection::init(52, true);

				// Récupère l'adresse du compte sur RiaStudio
				$adr_sub = ria_mysql_fetch_assoc(gu_adresses_get($user_sub));

				// Mise à jour du compte sur RiaStudio
				gu_users_update_email($user_sub, $_POST['email']);
				gu_adresses_update($user_sub, $adr_sub['id'], $adr_sub['type_id'], $_POST['title'], $_POST['firstname'], $_POST['lastname'], $_POST['society'], $_POST['siret'], $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], $adr_sub['country'], $_POST['phone'], (isset($_POST['fax']) ? $_POST['fax'] : $adr_sub['fax']), $_POST['mobile'], (isset($_POST['phone-work']) ? $_POST['phone-work'] : $adr_sub['phone_work']), false, null, null, null, $adr_sub['address3'], null, null);

				$config = $old_config;
				$ria_db_connect = $old_ria_db_connect;
			}
		}

		if( !count($error) ){
			if( !isset($_GET['subscribtion']) ){
				$_SESSION['account_edit_success'] = 1;
				header('Location: /options/my-account.php');
			}else{
				header('Location: /options/popup-payment.php?'.str_replace('&amp;', '&', $params_form));
			}

			exit;
		}
	}

	if (!isset( $_SESSION['lang'])) {
		 $_SESSION['lang'] = 'fr';
	}

	if( isset($_GET['subscribtion']) ){
		define('ADMIN_HEAD_POPUP', '1');
		define('ADMIN_ID_BODY', 'popup-content');
	}

	// Chargement des informations sur les différents forfait disponible sur Yuto
	$package = RegisterGCP::getPackage($config['tnt_id']);

	$data = array(
		'civility'      => isset($_POST['title'])    	? $_POST['title']    	: $admin_account->getCivility(),
		'firstname'     => isset($_POST['firstname'])   ? $_POST['firstname']   : $admin_account->getFirstname(),
		'lastname'      => isset($_POST['lastname'])    ? $_POST['lastname']    : $admin_account->getLastname(),
		'dob'		=> isset($_POST['dob'])		? dateparse($_POST['dob'])		: $admin_account->getBirthday(),
		'society' 	=> isset($_POST['society']) 	? $_POST['society'] 	: $adr['society'],
		'siret' 	=> isset($_POST['siret']) 	? $_POST['siret']	: $adr['siret'],
		'address1' 	=> isset($_POST['address1']) 	? $_POST['address1'] 	: $adr['address1'],
		'address2' 	=> isset($_POST['address2']) 	? $_POST['address2'] 	: $adr['address2'],
		'zipcode' 	=> isset($_POST['zipcode']) 	? $_POST['zipcode'] 	: $adr['zipcode'],
		'city' 		=> isset($_POST['city']) 	? $_POST['city'] 	: $adr['city'],
	);

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Mon compte') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Mon compte') . ' - ' . _('Mes options'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php
	if( isset($_GET['subscribtion']) ){
		print _('Étape 2 sur 3 : Mon compte');
	}else{
		print _('Mon compte');
	}
?></h2>
<?php
	if( isset($_SESSION['account_edit_success']) ){
		echo '<div class="success">'._('Les informations de votre compte ont été mises à jour avec succès.').'</div>';
		unset($_SESSION['account_edit_success']);
	}

	if( count($error) ){
		print '<div class="error">'.nl2br(implode('<br />', $error)).'</div>';
	}
?>

<form class="myaccount" id="form-edit-user" action="my-account.php<?php print trim($params_form) != '' ? '?'.$params_form : ''; ?>" method="post">
	<?php
		if( isset($_GET['subscribtion']) ){
			if( $package == 'business' ){
				print _('Compléter les informations de votre compte pour finaliser votre abonnement à Yuto Business.');
			}else{
				print _('Compléter les informations de votre compte pour finaliser votre abonnement à Yuto Essentiel.');
			}

			print '<br /><br />';
		}
	?>

	<script><!--
			$('#form-edit-user').attr('autocomplete','off');
			$(document).ready(function(){
				$('#password1').val('');
				$('#password2').val('');
			});
	--></script>

	<dl>
		<dt><?php print _('Mes informations personnelles'); ?></dt>
		<dd><label><span class="mandatory">*</span> <?php print _('Civilité :'); ?></label>
			<?php
				$titles = gu_titles_get();
				while( $r = ria_mysql_fetch_array($titles) ){
					print '<input type="radio" class="radio" name="title" id="title-'.$r['id'].'" value="'.$r['id'].'" '.( $data['civility'] == $r['id'] ? ' checked="checked"':'').'/> ';
					print '<label class="inline" for="title-'.$r['id'].'">'.htmlspecialchars(_($r['name'])).'</label> ';
				}
			?>
		</dd>
		<dd><label for="firstname"><span class="mandatory">*</span> <?php print _('Mon prénom :'); ?></label><input type="text" class="large" name="firstname" id="firstname" value="<?php print htmlspecialchars($data['firstname']); ?>" maxlength="75" /></dd>
		<dd><label for="lastname"><span class="mandatory">*</span> <?php print _('Mon nom de famille :'); ?></label><input type="text" class="large" name="lastname" id="lastname" value="<?php print htmlspecialchars($data['lastname']); ?>" maxlength="75" /></dd>
		<?php if( !isset($_GET['subscribtion']) ){ ?>
			<dd><label for="dob"><?php print _('Ma date de naissance :'); ?></label><input type="text" id="dob" name="dob" value="<?php print isdate($data['dob']) ? date('d/m/Y', strtotime($data['dob'])) : ''; ?>"></dd>
		<?php } ?>
	</dl>

	<dl>
		<dt><?php print _('Ma société'); ?></dt>
		<dd>
                        <label for="society"><span class="mandatory">*</span> <?php print _('Ma société :'); ?></label>
                        <input type="text" class="large" name="society" id="society" value="<?php print htmlspecialchars($data['society']); ?>" maxlength="75" />
                </dd>
                <dd>
                        <label for="siret"><span class="mandatory">*</span> <?php print _('Mon SIRET :'); ?></label>
                        <input type="text" class="large" name="siret" id="siret" value="<?php print htmlspecialchars($data['siret']); ?>" maxlength="75" />
                </dd>
                <dd>
                        <label for="address1"><span class="mandatory">*</span> <?php print _('Adresse :'); ?></label>
                        <input type="text" class="large" name="address1" id="address1" value="<?php print htmlspecialchars($data['address1']); ?>" />
                </dd>
                <dd>
                        <label for="address2"><?php print _('Complément d\'adresse :'); ?></label>
                        <input type="text" class="large" name="address2" id="address2" value="<?php print htmlspecialchars($data['address2']); ?>" />
                </dd>
                <dd>
                        <label for="zipcode"><span class="mandatory">*</span> <?php print _('Code postal :'); ?></label>
                        <input type="text" name="zipcode" id="zipcode" value="<?php print htmlspecialchars($data['zipcode']); ?>" />
                </dd>
                <dd>
                        <label for="city"><span class="mandatory">*</span> <?php print _('Ville :'); ?></label>
                        <input type="text" class="large" name="city" id="city" value="<?php print htmlspecialchars($data['city']); ?>" />
                </dd>
        </dl>

	<dl>
		<dt><?php print _('Pour me contacter'); ?></dt>
		<dd><label for="email"><span class="mandatory">*</span> <?php print _('Mon adresse email :'); ?></label><input type="email" class="large" id="email" name="email" value="<?php print htmlspecialchars($admin_account->getEmail()); ?>" /></dd>
		<dd><label for="tel"><?php print _('Téléphone :'); ?></label><input type="text" id="phone" name="phone" value="<?php print htmlspecialchars($admin_account->getPhone()); ?>" /></dd>
		<dd><label for="mobile"><?php print _('Portable :'); ?></label><input type="text" id="mobile" name="mobile" value="<?php print htmlspecialchars($admin_account->getMobile()); ?>" /></dd>

		<?php if( !isset($_GET['subscribtion']) ){ ?>
			<dd><label for="fax"><?php print _('Fax :'); ?></label><input type="text" id="fax" name="fax" value="<?php print htmlspecialchars($admin_account->getFax()); ?>" /></dd>
			<dd><label for="phone-work"><?php print _('Ligne directe :'); ?></label><input type="text" id="phone-work" name="phone-work" value="<?php print htmlspecialchars($admin_account->getWork()); ?>" /></dd>
		<?php } ?>
	</dl>

	<?php if( !isset($_GET['subscribtion']) ){ ?>
		<dl>
			<dt><?php print _('Mon mot de passe'); ?></dt>
			<dd><label for="password1"><?php print _('Nouveau mot de passe :'); ?></label><input type="password" class="text" name="password1" id="password1" maxlength="16" autocomplete="new-password" /></dd>
			<dd><label for="password2"><?php print _('Confirmation :'); ?></label><input type="password" class="text" name="password2" id="password2" maxlength="16" autocomplete="new-password" /></dd>
		</dl>

		<dl>
			<dt><?php print _('Langue'); ?></dt>
	        	<dd><label for="language"><?php print _('Choix de la langue :'); ?></label></dd>
	        	<dd><select id="language" name="language">
        		    	<option value="fr_FR" <?php print  $admin_account->getLang() == 'fr_FR' ? 'selected="selected"' : ''; ?>><?php print _('Français'); ?></option>
				<option value="de_DE" <?php print  $admin_account->getLang() == 'de_DE' ? 'selected="selected"' : ''; ?>><?php print _('Allemand'); ?></option>
				<option value="en_GB" <?php print  $admin_account->getLang() == 'en_GB' ? 'selected="selected"' : ''; ?>><?php print _('Anglais'); ?></option>
		        </select></dd>
		</dl>
	<?php } ?>

	<div class="ria-admin-ui-actions">
		<?php if( isset($_GET['subscribtion']) ){ ?>
			<input type="submit" name="save-sub" value="<?php print _('Valider mes informations personnelles'); ?>" />
		<?php }else{ ?>
			<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Enregistrer les modifications'); ?>" />
			<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" title="<?php print _('Annuler les modifications'); ?>" />
		<?php } ?>
	</div>

</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>

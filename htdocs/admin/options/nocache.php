<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_CACHE');

	if( !isset($_SESSION['usr_id']) || !isset($_SESSION['usr_prf_id']) || $_SESSION['usr_prf_id']!=PRF_ADMIN ){
		header('Location: /admin/options/index.php');
		exit;
	}

	if(isset($_POST['nocache'])){
		$_SESSION['unactive_memcached'] = strtotime( '+15 minutes' );
		header('Location: /admin/options/nocache.php');
		exit;
	}

	if(isset($_POST['cache'])){
		unset($_SESSION['unactive_memcached']);
		header('Location: /admin/options/nocache.php');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Désactiver le cache') );

	// Défini le titre de la page
	define('ADMIN_PAGE', _('Désactiver le cache'));
	require_once('admin/skin/header.inc.php');
?>

<h2><?php print _('Désactivation du cache'); ?></h2>
<p><?php print _('Vous avez la possibilité de désactiver le cache mémoire sur votre site pour une durée de 15 minutes, en cliquant sur le bouton ci-dessous.'); ?></p>

<?php if ( !isset($_SESSION['unactive_memcached']) || time() > $_SESSION['unactive_memcached'] ) { ?>
	<form action="/admin/options/nocache.php" method="post">
		<input id="btn-nocache" name="nocache" value="<?php print _('Désactiver le cache'); ?>" type="submit"/>
	</form>
<?php }else{ ?>
	<p> Votre cache est désactivé jusqu'à <?php print date( 'd/m/Y H:i:s', $_SESSION['unactive_memcached'] ); ?></p>
	<form action="/admin/options/nocache.php" method="post">
		<input id="btn-nocache" name="cache" value="<?php print _('Activer le cache'); ?>" type="submit"/>
	</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
?>
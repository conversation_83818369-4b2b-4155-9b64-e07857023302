<?php
    /**	\file subscription.php
     *	Cette page permet la gestion de l'abonnement à Yuto.
     */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_SUBSCRIPTION');

	require_once('admin/get-filters.php');

	// Charge le type d'abonnement Yuto
	$package = RegisterGCP::getPackage($config['tnt_id']);

	// Récupère les informations sur l'abonnement en cours
	$subscription_info = dev_subscribtions_yuto_get();
	if( $subscription_info['in_testing'] ){
		$subscription_info['date_end_testing'] = $subscription_info['date_end'];
	}

	// On regarde si un abonnement à venir existe, cela voudra dire que le client à déjà activer son abonnement Yuto
	$sub_future = dev_subscribtions_yuto_get(false, true);

	// Demande de désabonnement à Yuto
	if( isset($_POST['unsubscribe']) ){
		$cfg_emails = cfg_emails_yuto_get();

		if( is_array($sub_future) && count($sub_future) ){
			$date_end = $sub_future['date_end'];
			$type_period = $sub_future['type'] == 'monthly' ? 'd\'engagement mensuelle' : 'd\'engagement annuelle';
		}else{
			$date_end = $subscription_info['date_end'];

			if( $subscription_info['in_testing'] ){
				$type_period = 'd\'essai gratuite';
			}else{
				$type_period = $subscription_info['type'] == 'monthly' ? 'd\'engagement mensuelle' : 'd\'engagement annuelle';
			}
		}

		// Envoie du mail au client
		$email = new Email();
		$email->setFrom($cfg_emails['vel-yuto-notif']['from']);
		$email->setTo($_SESSION['usr_email']);

		if( trim($cfg_emails['vel-yuto-notif']['bcc']) != '' ){
			$email->addBcc($cfg_emails['vel-yuto-notif']['bcc']);
		}

		$email->setSubject('Votre demande de désabonnement à Yuto '.ucfirst($package));

		$html = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/unsubscription.html');
		$html = str_replace('[date]', $date_end, $html);
		$html = str_replace('[type_periode]', $type_period, $html);
		$html = str_replace('[export-data]', 'https://app.riashop.fr/customers/index.php', $html);
		$html = str_replace('[package]', ucfirst($package), $html);
		$html = str_replace('[utm_campaign]', 'vel_'.$package, $html);

		$email->addHtml($html);
		$email->send();

		dev_subscribtions_set_date_unsubscribtion_yuto(false);

		$_SESSION['riashop']['my-account'] = _('Votre demande d\'annulation d\'abonnement à Yuto a été prise en compte. Vous pouvez à tout moment annuler votre demande de désabonnement.');
		header('Location: /admin/options/subscription.php');
		exit;
	}

	// Annulation de la demande de désabonnement à Yuto
	if( isset($_POST['subscribe']) ){

		$cfg_emails = cfg_emails_yuto_get();

		// Envoie du mail au client
		{
			$email = new Email();
			$email->setFrom($cfg_emails['vel-yuto-notif']['from']);
			$email->setTo($_SESSION['usr_email']);

			if( trim($cfg_emails['vel-yuto-notif']['bcc']) != '' ){
				$email->addBcc($cfg_emails['vel-yuto-notif']['bcc']);
			}

			$email->setSubject('Votre demande de réinscription à Yuto '.ucfirst($package));

			$html = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/cancel-unsubscribe.html');
			$html = str_replace('[package]', ucfirst($package), $html);
			$html = str_replace('[url_support]', ($package == 'business' ? 'https://support.riashop.fr/yuto-crm/aide-application-yuto/premiers-pas-application-yuto/' : 'https://support.riashop.fr/yuto-crm/aide-administration-yuto/premiers-pas-administration-yuto/'), $html);
			$html = str_replace('[utm_campaign]', 'vel_'.$package, $html);
			$email->addHtml($html);
			$email->send();
		}

		dev_subscribtions_set_date_unsubscribtion_yuto( true );

		$_SESSION['riashop']['my-account'] = _('Votre demande de réabonnement à Yuto a été prise en compte.');

		header('Location: /admin/options/subscription.php');
		exit;
	}

	$date_end = $type = $unsubscribe = $in_testing = false;


	// Détermine la date de désinscription en fonction de l'abonnement en cours ou du future abonnement (si celui en cours est en période d'essai)
	if( is_array($sub_future) && count($sub_future) ){
		$date_end = $sub_future['date_end'];
		$in_testing = $sub_future['in_testing'];
		$type = $sub_future['type'];
		$unsubscribe = $sub_future['date_unsubscribe'];
	}else{
		$in_testing = $subscription_info['in_testing'];
		if( $in_testing ){
			$date_end = $subscription_info['date_end_testing'];
		}else{
			$date_end = $subscription_info['date_end'];
		}

		$type = $subscription_info['type'];
		$unsubscribe = $subscription_info['date_unsubscribe'];
	}

	$next_sub = new Datetime(dateparse($subscription_info['date_end']));
	$next_sub->modify('+ 1 day');


	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Mon abonnement') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Mon abonnement') . ' - ' . _('Mes options'));
	require_once('admin/skin/header.inc.php');
?>

	<h2><?php echo str_replace('#param[formule]#', ucfirst($package), _('Mon abonnement Yuto #param[formule]#')); ?></h2>

	<?php
		if( isset($_SESSION['success_pay_abo']) ){
			// Affiche une confirmation d'abonnement et propose la prochaine étape
			print
				'<div class="success">'
					.'<p>'._('Votre demande d\'abonnement a bien été prise en compte, retrouvez toutes les informations ci-dessous.').'</p>'
					.'<p>'._('<strong>Prochaine étape :</strong> utilisez l\'outil d\'import de données pour retrouver toutes vos informations sur Yuto : <a href="/admin/tools/imports/index.php">accéder à l\'outil d\'import</a>.').'</p>'
				.'</div>'
			;
			unset($_SESSION['success_pay_abo']);
		}

		if( isset($_SESSION['riashop']['my-account']) ){
			print '<div class="success">'.$_SESSION['riashop']['my-account'].'</div>';
			unset($_SESSION['riashop']['my-account']);
		}

		if( $subscription_info ){
	?>
		<dl>
			<dt><?php print _('Offre actuelle'); ?></dt>
			<dd>&nbsp;</dd>

			<?php if( $subscription_info['in_testing'] ){ ?>
				<dd><strong><?php print str_replace('#param[formule]#', ucfirst($package), _("Tout Yuto #param[formule]# gratuit pendant 14 jours !")); ?></strong></dd>
				<?php print view_admin_show_test_period(); ?>
				<br><br>
				<dd><p><?php print str_replace(
					'#param[date fin abonnement]#',
					'<strong>'.ria_date_format($subscription_info['date_end_testing']).'</strong>',
					_('Vous êtes actuellement en période d\'essai gratuite jusqu\'au #param[date fin abonnement]#.')
				); ?></p></dd>
				<dd><?php print $subscription_info['qte'] == 1? _('Nombre de licence active : ') : _('Nombre de licences actives : '); ?><strong><?php print $subscription_info['qte']; ?></strong></dd>
				<dd>&nbsp;</dd>

				<?php
					if( is_array($sub_future) && count($sub_future) ){
						$text = _('Vous avez activé votre abonnement mensuel, qui débutera à l\'issue de votre période d\'essai le #param[date]#.');
						if( $sub_future['type'] == 'yearly' ){
							$text = _('Vous avez activé votre abonnement annuel, qui débutera à l\'issue de votre période d\'essai le #param[date]#.');
						}

						print '
							<dd>
								'.str_replace('#param[date]#', '<strong>'.$sub_future['date_start'].'</strong>', $text).'
							</dd>
							<dd>
						';

						if( $sub_future['qte'] == 1 ){
							print _('Nombre de licence souscrite : ');
						}else{
							print _('Nombre de licences souscrites : ');
						}

						print '
								<strong>'.($sub_future['qte'] - $sub_future['opt_multi_devices']).'</strong>
						';

						if( $sub_future['opt_multi_devices'] ){
							if( $sub_future['opt_multi_devices'] > 1 ){
								print '<span>'.str_replace('#param[nombre de licence]#', $sub_future['opt_multi_devices'], _('(dont #param[nombre de licence]# licences avec l\'option "Multi-devices")')).'</span>';
							}else{
								print '<span>'.str_replace('#param[nombre de licence]#', $sub_future['opt_multi_devices'], _('(dont #param[nombre de licence]# licence avec l\'option "Multi-devices")')).'</span>';
							}
						}

						print '
							</dd>
							<dd>
								'.sprintf( _('Montant de mon abonnement : <strong>%s€ HT</strong> (<strong>%s€ TTC</strong>)'), ria_number_format($sub_future['cost_ht'], NumberFormatter::DECIMAL, 2), ria_number_format($sub_future['cost'], NumberFormatter::DECIMAL, 2) ).'
							</dd>
							<dd class="yuto-notice">
								<i>'.sprintf( _('Le tarif applicable de votre abonnement restera en vigueur jusqu\'à la fin de votre période de facture en cours, le %s.'), $sub_future['date_end']).'</i>
							</dd>
						';
					}else{
						print '
							<dd>
								<p>'.str_replace('#param[formule]#', ucfirst($package), _('Activer votre abonnement pour continuer à utiliser Yuto #param[formule]# :')).'</p>
								<input type="button" value="'.str_replace('#param[formule]#', ucfirst($package), _('Activer mon abonnement Yuto #param[formule]#')).'" onclick="displayPopup(\''.str_replace('#param[formule]#', ucfirst($package), _('Activation de mon abonnement Yuto #param[formule]#')).'\', \'\', \'/admin/options/popup-reactivate-subscription.php?activation=1\', \'\');return false;" />
							</dd>
						';
					}
			}else{
				$text = _('Vous avez souscrit un <strong>abonnement mensuel</strong>.');
				if( $subscription_info['type'] == 'yearly' ){
					$text = _('Vous avez souscrit un <strong>abonnement annuel</strong>.');
				}

				?><dd><?php print $text; ?></dd>
				<dd><?php print $subscription_info['qte'] == 1? _('Nombre de licence active : ') : _('Nombre de licences actives : '); ?><strong><?php print $subscription_info['qte']; ?></strong></dd>
				<dd><?php print sprintf( _('Montant de mon abonnement : <strong>%s€ HT</strong> (<strong>%s€ TTC</strong>)'), ria_number_format($subscription_info['cost_ht'], NumberFormatter::DECIMAL, 2), ria_number_format($subscription_info['cost'], NumberFormatter::DECIMAL, 2) ); ?></dd>

				<?php
					if( trim($subscription_info['date_unsubscribe']) == '' ){
						if( is_numeric($subscription_info['next_qte']) && $subscription_info['next_qte'] > 0 && $subscription_info['next_qte'] < $subscription_info['qte'] ){
							print '<dd><strong>'.str_replace(
								array('#param[qte]#', '#param[ht]#', '#param[ttc]#', '#param[date]#'),
								array(
									number_format($subscription_info['next_qte'], 0, ',', ' '),
									number_format($subscription_info['next_ht'], 2, ',', ' '),
									number_format($subscription_info['next_ttc'], 2, ',', ' '),
									$next_sub->format('d/m/Y'),
								),
								$subscription_info['next_qte'] > 1 ? _('Votre abonnement comptera #param[qte]# licences pour un montant de #param[ht]# € HT (#param[ttc]# € TTC) à partir du #param[date]#.') : _('Votre abonnement comptera #param[qte]# licence pour un montant de #param[ht]# € HT (#param[ttc]# € TTC) à partir du #param[date]#.')
							).'</strong></dd>';
						}
				?>
					<dd>&nbsp;</dd><dd>
						<input type="button" value="<?php print _('Mettre à jour mon abonnement'); ?>" onclick="displayPopup('Mon abonnement', '', '/admin/options/popup-add-licence.php', 'closePopup();');return false;" />
					</dd>
				<?php } ?>
				<dd class="yuto-notice"><i><?php print sprintf( _('Le tarif applicable de votre abonnement restera en vigueur jusqu\'à la fin de votre période de facture en cours, le %s.'), $subscription_info['date_end']); ?></i></dd>
			<?php } ?>
		</dl>
	<?php }else{
		$last_subscription = dev_subscribtions_yuto_get( true );

		// Affichage d'un message selon si le dernier abonnement était celui avec la période d'essai, sur un abonnement mensuel ou annuel
		if( is_numeric($last_subscription['testing']) && $last_subscription['testing'] > 0 ){
			print _('Votre abonnement s\'est arrêté à la fin de votre période d\'essai le ').ria_date_format($last_subscription['date_end']).'.';
		}elseif( $last_subscription['type'] == 'monthly' ){
			print _('Votre abonnement s\'est arrêté à la fin de votre période d\'engagement mensuelle le ').ria_date_format($last_subscription['date_end']).'.';
		}else{
			print _('Votre abonnement s\'est arrêté à la fin de votre période d\'engagement annuelle le ').ria_date_format($last_subscription['date_end']).'.';
		}

		?>
		<dl>
			<dd>
				<input type="button" value="<?php print str_replace('#param[formule]#', ucfirst($package), _('Réactiver mon abonnement Yuto #param[formule]#')); ?>" onclick="displayPopup('Mes licences', '', '/admin/options/popup-reactivate-subscription.php', 'closePopup();');return false;" />
			</dd>
			<dd>&nbsp;</dd>
		</dl>
	<?php } ?>
	<dl>
		<dd>&nbsp;</dd>
		<dt><?php print _('Utilisation des licences'); ?></dt>
		<dd>&nbsp;</dd>
		<dd>
			<div class="stats-menu">
				<div id="riadatepicker"></div>
				<div class="clear"></div>
			</div>
		</dd>
		<?php
			view_import_highcharts();

			$highcharts_show_title = false;
			require_once( 'admin/highcharts/graph-dev-subscriptions.php' );
		?>
		<script><!--
			var urlHighcharts = '/admin/fdv/stats/subscriptions.php';
			var riadatepicker_upd_url = '';
			<?php view_date_initialized( 0, '', false, array() ); ?>
		--></script>
	</dl>

	<?php
		$sub_del = is_array($sub_future) && count($sub_future) ? $sub_future : $subscription_info;
		if( is_array($sub_del) && count($sub_del) && !$sub_del['in_testing'] ){
			print '
				<dl><form action="/admin/options/subscription.php" method="post">
			';

			// Si abonné
			if( !$sub_del['date_unsubscribe'] ){
				print '
					<dd>
						<input type="submit" name="unsubscribe" value="'._('Suspendre mon abonnement Yuto').'" />
					</dd>
					<p>'.str_replace('#param[date]#', ria_date_format($date_end), _('L’annulation sera effective à la fin de votre période de facturation en cours, le #param[date]#. Nous vous enverrons un e-mail de confirmation de votre annulation et vous pourrez réactiver votre abonnement à tout moment.')).'</p>
				';
			}else{ // Si désabonné
				print '
					<dd>'._('Vous avez fait une demande de désabonnement le ').ria_date_format($unsubscribe, true).'.
				';

				if( $type == 'monthly' ){
					print _('Votre abonnement s’arrêtera à la fin de votre période d\'engagement mensuelle le ').ria_date_format($date_end).'.';
				}else{
					print _('Votre abonnement s’arrêtera à la fin de votre période d\'engagement annuelle le ').ria_date_format($date_end).'.';
				}

				print '
					</dd>
					<dd>
						<input type="submit" name="subscribe" value="'._('Annuler ma demande de désabonnement').'" />
					</dd>
				';
			}

			print '
				</form></dl>
			';
		}
	 ?>

	<script>
		// Fonction appelé lors de la fermeture des popup "popup-add-licence" et "popup-reactivate-subscription"
		var closePopup = function(){
			hidePopup();
			location.reload();
		}
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>

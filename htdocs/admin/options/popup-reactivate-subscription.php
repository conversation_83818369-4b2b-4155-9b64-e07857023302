<?php

/** \file popup-reactivate-subscribtion.php
 *  Cette page permet de s'abonner ou de se réabonner à Yuto
 *  Elle permet de gérer aussi bien le premier abonnement que la réabonnement.
 *  Si le client a déjà enregistré une carte CB et que celle-ci est toujours valide, l'enregitrement n'est qu'en une page.
 *  Dans le cas où les informations du compte sont incomplète ou qu'aucune carte CB n'est connue, alors l'enregistrement est fait en 2 ou 3 étapes
 */

require_once('email.inc.php');
require_once('PaymentExternal/Payplug_old.inc.php');

// Chargement des informations sur les différents forfait disponible sur Yuto
$package = RegisterGCP::getPackage($config['tnt_id']);

// On récup_re les informations sur le prix des abonnements
$prd_packages = dev_package_get_products($package);
if (!is_array($prd_packages) || !count($prd_packages)) {
	print '<div class="error">'._('Une erreur est survenue lors du chargement. Veuillez réessayer ou prendre contact pour nous signaler ce problème.').'</div>';
	exit;
}

// Si la popup est ouverte pour une activation d'abonnement ou une réactivation
$activation = isset($_GET['activation']);

$is_retour_post = false;

$user_sub = false;
$print_CB = false;

// Récupère les informations sur l'abonnement
$subscription_info = dev_subscribtions_yuto_get(true);

// Charge les informations du compte depuis RiaStudio
{
	$old_config = $config;
	$old_ria_db_connect = $ria_db_connect;

	// Charge la configuration de RiaStudio
	RegisterGCPConnection::init(52, true);

	// Charge les données du compte
	$r_user_sub = gu_users_get($subscription_info['usr_id']);
	if ($r_user_sub && ria_mysql_num_rows($r_user_sub)) {
		$user_sub = ria_mysql_fetch_assoc($r_user_sub);
	}

	// On regarde si une empreinte bancaire existe déjà pour ce client et qu'elle est toujours valide
	$r_have_cb = gu_users_payment_credentials_get($user_sub['id'], 'PAYPLUG');
	$print_CB = $r_have_cb && ria_mysql_num_rows($r_have_cb);

	$config = $old_config;
	$ria_db_connect = $old_ria_db_connect;
}

// Activation de l'abonnement Yuto Essentiel
if (isset($_POST['activation'])) {
	// Données sur le choix de l'abonnement
	$data = http_build_query(array(
		'type' => $_POST['sub_type'],
		'licences' => $_POST['nb_licence'],
		'step_max' => $_POST['step_max'],
		'use-print' => isset($_POST['use-print']) ? $_POST['use-print'] : 0,
	));

	// Si des informations sont manquantes sur le compte client alors on envoi sur le formulaire "Mes Options > Mon compte"
	// Les informations de commande d'abonnement sont passées en paramètres
	if (trim($user_sub['society']) == '' || trim($user_sub['adr_firstname']) == '' || trim($user_sub['adr_lastname']) == '') {
		header('Location: /options/my-account.php?subscribtion=1&'.$data);
	} else {
		header('Location: /options/popup-payment.php?'.$data);
	}
	exit;
}

define('ADMIN_PAGE_TITLE', _('Mes licences'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
define('ADMIN_CLASS_BODY', 'popup-yuto-subscribtion');

require_once('admin/skin/header.inc.php');
?>
<div class="msg">
	<?php if (isset($error) && count($error)) { ?>
		<div class="error">
			<?php echo nl2br(implode('<br >', $error)); ?>
		</div>
	<?php } elseif ($is_retour_post) { ?>
		<script>
			$(document).ready(function(){
				parent.$('.load-ajax-opacity').show()
				parent.closePopup();
			});
		</script>
		<div class="success">
			<?php print _('Votre demande de réabonnement à Yuto a été prise en compte, actualisation de vos informations en cours...'); ?>
		</div>
	<?php } ?>
</div>

<?php if( !$is_retour_post ){ ?>
<form action="popup-reactivate-subscription.php" method="post">
	<input type="hidden" name="prorata" id="prorata" value="">

	<h2><?php
			$step_max = trim($user_sub['society']) == '' || trim($user_sub['adr_firstname']) == '' || trim($user_sub['adr_lastname']) == '' ? 3 : 2;

			if ($activation) {
				print str_replace('#param[etape]#', $step_max, _('Étape 1 sur #param[etape]# : Mon offre'));
			} else {
				if (!$print_CB) {
					if ($package == 'essentiel') {
						print str_replace('#param[etape]#', $step_max, _('Étape 1 sur #param[etape]# : Réactivation de mon abonnement Yuto Essentiel'));
					} else {
						print str_replace('#param[etape]#', $step_max, _('Étape 1 sur #param[etape]# : Réactivation de mon abonnement Yuto Business'));
					}
				} else {
					if ($package == 'essentiel') {
						print _('Réactivation de mon abonnement Yuto Essentiel');
					} else {
						print _('Réactivation de mon abonnement Yuto Business');
					}
				}
			}
			?></h2>

	<input type="hidden" name="step_max" value="<?php print $step_max; ?>" />

	<label><?php print _('Type d\'abonnement : '); ?></label>
	<?php
	print '
		<div class="package">
			<input '.(!isset($_POST['sub_type']) || $_POST['sub_type'] == 'month' ? 'checked="checked"' : '').' type="radio" name="sub_type" id="sub_type_month" value="month" />
			<label for="sub_type_month">'._('Règlement mensuel').' : <span><span id="price_abo_month"></span> / '._('mois').' / '._('licence').'</span></label>&nbsp;
		</div>
		<div class="package">
			<input '.(isset($_POST['sub_type']) && $_POST['sub_type'] == 'year' ? 'checked="checked"' : '').' type="radio" name="sub_type" id="sub_type_year" value="year" />
			<label for="sub_type_year">'._('Règlement annuel').' : <span><span id="price_abo_year"></span> / '._('an').' / '._('licence').'</span> <strong>'.($package == 'essentiel' ? _('(2 mois d\'économie)') : _('(60,00 € d\'économie)')).'</strong></label>
		</div>

		<br>
		<label for="nb_licence">'._('Nombre de licences souhaitées :').' </label><input type="number" name="nb_licence" id="nb_licence" min="1" max="99" value="'.(isset($_POST['nb_licence']) ? $_POST['nb_licence'] : '1').'"/>
	';

	if( $package == 'business' ){
		print '
			<strong>'._('(coût de la licence dégressif à partir de 10 licences)'). '</strong>
			<br /><br />
			<div id="notice-bo"></div>
		';
	}

	print '
		<br /><br />
		<label id="price"></label>
		<br /><br />
		<div id="notice"></div>
	';

	if (!$activation && $print_CB) {
		print '
			<input type="checkbox" id="cgv" name="cgv" required>
			<input type="hidden" name="use-print" value="1" />
			<label for="cgv">&nbsp;'._('J\'accepte les ').'<a target="_blank" href="https://start.yuto.fr/conditions-generales-de-vente/">'._('Conditions générales de vente').'</a>.</label>
			<br /><br />
			<input type="submit" value="'._('J\'accepte le paiement de mon abonnement').'" name="activation" />
		';
	} else {
		print '<input type="submit" value="'._('Valider mon offre').'" name="activation" />';
	}

	print '
		<div style="font-style: italic;">Vous pourrez à tout moment modifier le nombre de licences souscrites dans votre abonnement.</div>
	';
	?>
</form>

<div class="load-ajax-opacity"></div>

<script>
	// Lors de la réactivation, on affiche une opacité pour éviter le clic multiple
	$('[name="reactivate"]').on('click', function(){
		if( $('[id="cgv"]:checked').length == 1 ){
			$('.load-ajax-opacity').show();
		}
	});

	// Variable global contenant les informations sur le forfait Yuto (essentiel ou business)
	var typePackage = '<?php print ucfirst($package); ?>';
	var packages = <?php print json_encode($prd_packages); ?>;
	var activation = <?php print $activation ? 'true' : 'false'; ?>;

	/** Cette fonction permet de gérer les informations dynamiquement en fonction de la quantité saisie (notice + totaux)
	 *
	 */
	var showNotice = function() {
		// Initialise les textes
		var orderInfos, orderTotal, backOffice = '';

		// Initialise les informations sur l'hébergement
		var hosting = {
			price_ht: 0,
			price_ttc: 0
		};

		// Récupère le nombre de licence choisi
		var licenceCount = $('#nb_licence').val();

		// Détermine le message de commande selon le nombre de licence choisi et si l'on réactive un abonnement ou bien s'il s'agit de la premier fois
		if (licenceCount == 1) {
			orderInfos = activation ? activateSubscriptionMonthly : reactivateSubscriptionMonthly;
		} else {
			orderInfos = activation ? activateSubscriptionMonthlyPlural : reactivateSubscriptionMonthlyPlural;
		}

		var priceHT_Month  	= packages.mensuel.price_ht;
		var priceTTC_Month 	= packages.mensuel.price_ttc;
		var priceHT_Year  	= packages.annuel.price_ht;
		var priceTTC_Year 	= packages.annuel.price_ttc;

		// Réalisation de traitement spécifique sur la formule Business
		if (typePackage == 'Business') {
			// Détermine le cout pour l'hébergement lors d'un forfait Business
			for (h in packages.hosting) {
				if (licenceCount >= parseInt(h)) {
					hosting = packages.hosting[h];
				}
			}

			// Modification du prix HT et TTC à partir d'une quantité de 10
			if (licenceCount >= 10) {
				priceHT_Year = packages.annuel.price_ht_10;
				priceTTC_Year = packages.annuel.price_ttc_10;
				priceHT_Month = packages.mensuel.price_ht_10;
				priceTTC_Month = packages.mensuel.price_ttc_10;
			}
		}

		// Converti tous les montants en float pour permettre leur addition lors du calcul des totaux ci-dessous
		priceHT_Year = parseFloat(priceHT_Year);
		priceTTC_Year = parseFloat(priceTTC_Year);
		priceHT_Month = parseFloat(priceHT_Month);
		priceTTC_Month = parseFloat(priceTTC_Month);
		hosting.price_ht = parseFloat(hosting.price_ht);
		hosting.price_ttc = parseFloat(hosting.price_ttc);

		// Remplace dans l'interface le prix des abonnements
		if( priceHT_Month != priceTTC_Month ){
			$('[id="price_abo_month"]').html(number_format(priceHT_Month, 2, ',', ' ') + ' € HT (' + number_format(priceTTC_Month, 2, ',', ' ') + ' € TTC)');
		}else{
			$('[id="price_abo_month"]').html(number_format(priceHT_Month, 2, ',', ' ') + ' € HT');
		}

		if( priceHT_Year != priceTTC_Year ){
			$('[id="price_abo_year"]').html(number_format(priceHT_Year, 2, ',', ' ') + ' € HT (' + number_format(priceTTC_Year, 2, ',', ' ') + ' € TTC)');
		}else{
			$('[id="price_abo_year"]').html(number_format(priceHT_Year, 2, ',', ' ') + ' € HT');
		}

		// Remplace les informations suivantes dans les textes : nombre de licence, prix ht, prix ttc, total ht, total ttc
		if ($('#sub_type_year').is(':checked')) { // Dans le cas d'un abonnement annuel
			// Texte sur les informations de commande
			orderInfos = orderInfos.replace('#param[licences]#', number_format(licenceCount, 0));
			orderInfos = orderInfos.replace('#param[priceHT]#', number_format(licenceCount * (priceHT_Year) + (hosting.price_ht * 12), 2, ',', ' '));
			orderInfos = orderInfos.replace('#param[price]#', number_format(licenceCount * (priceTTC_Year) + (hosting.price_ttc * 12), 2, ',', ' '));
			orderInfos = orderInfos.replace('#param[package]#', typePackage);

			// Texte sur le total de la commande

			orderTotal = yearPrice.replace('#param[tarifHT]#', number_format((licenceCount * (priceHT_Year)) + (hosting.price_ht * 12), 2, ',', ' '));
			orderTotal = orderTotal.replace('#param[tarif]#', number_format((licenceCount * (priceTTC_Year)) + (hosting.price_ttc * 12), 2, ',', ' '));

			// Texte pour le backoffice
			if (typePackage == 'Business') {
				backOffice = yutoBackOfficeYear.replace('#param[price ht]#', '<i>' + number_format((hosting.price_ht * 12), 2, ',', ' ') + '</i>');
				backOffice = backOffice.replace('#param[price ttc]#', '<i>' + number_format((hosting.price_ttc * 12), 2, ',', ' ') + '</i>');

				backOffice = backOffice.replace(' (<i>' + number_format((hosting.price_ht * 12), 2, ',', ' ') + '</i> € TTC)', '');
			}

			orderTotal = orderTotal.replace(' (' + number_format((licenceCount * (priceHT_Year)) + (hosting.price_ht * 12), 2, ',', ' ') + ' € TTC)', '');
			orderInfos = orderInfos.replace(' (' + number_format(licenceCount * (priceHT_Year) + (hosting.price_ht * 12), 2, ',', ' ') + ' € TTC)', '');
		} else { // Dans le cas d'un abonnement mensuel
			// Texte sur les informations de commande
			orderInfos = orderInfos.replace('#param[licences]#', number_format(licenceCount, 0));
			orderInfos = orderInfos.replace('#param[priceHT]#', number_format((licenceCount * (priceHT_Month)) + hosting.price_ht, 2, ',', ' '));
			orderInfos = orderInfos.replace('#param[price]#', number_format((licenceCount * (priceTTC_Month)) + hosting.price_ttc, 2, ',', ' '));
			orderInfos = orderInfos.replace('#param[package]#', typePackage);

			// Texte sur le total de la commande
			orderTotal = monthPrice.replace('#param[tarifHT]#', number_format((licenceCount * (priceHT_Month)) + hosting.price_ht, 2, ',', ' '));
			orderTotal = orderTotal.replace('#param[tarif]#', number_format((licenceCount * (priceTTC_Month)) + hosting.price_ttc, 2, ',', ' '));

			// Texte pour le backoffice
			if (typePackage == 'Business') {
				backOffice = yutoBackOfficeMonth.replace('#param[price ht]#', '<i>' + number_format(hosting.price_ht, 2, ',', ' ') + '</i>');
				backOffice = backOffice.replace('#param[price ttc]#', '<i>' + number_format(hosting.price_ttc, 2, ',', ' ') + '</i>');

				backOffice = backOffice.replace(' (<i>' + number_format(hosting.price_ht, 2, ',', ' ') + '</i> € TTC)', '');
			}

			orderTotal = orderTotal.replace(' (' + number_format((licenceCount * (priceHT_Month)) + hosting.price_ht, 2, ',', ' ') + ' € TTC)', '');
			orderInfos = orderInfos.replace(' (' + number_format((licenceCount * (priceHT_Month)) + hosting.price_ht, 2, ',', ' ') + ' € TTC)', '');
		}

		// Remplace les textes sur l'interface de l'utilisateur
		$('#notice').html(orderInfos).addClass('notice');
		$('#price').html('<strong>' + orderTotal + '</strong>');
		if( $('#notice-bo').length ){
			$('#notice-bo').html(backOffice);
		}
	}

	// Mise à des informations de la popup (Notice + totaux)
	showNotice();

	// Recharge les textes au changement de quantité de licences souhaité
	$('#nb_licence').on('change', function() {
		// Contrôle que la quantité de licence saisie est un entier entre 1 et 99
		var qty = parseInt($(this).val());

		if (isNaN(qty)) {
			$(this).val(1);
		} else if (qty < 1) {
			$(this).val(1);
		} else if (qty > 99) {
			$(this).val(99);
		}

		// Mise à des informations de la popup (Notice + totaux)
		showNotice();
	});

	// Recharge les texte au changement du type de forfait (mensuel / annuel)
	$('input[type=radio][name=sub_type]').change(function() {
		// Mise à des informations de la popup (Notice + totaux)
		showNotice();
	});

	// Ajout d'un loader lors du clique sur "Valider mon offre"
	$('[name="activation"]').click(function() {
		if( $('[id="cgv"]:checked').length == 1 ){
			$('body').append('<div class="popup_ria_back_load abs"></div><div class="popup_ria_back_notice notice">' + msgLoading + '</div>');
			$('.popup_ria_back_load, .popup_ria_back_notice').show();
		}
	});
</script>

<?php } ?>
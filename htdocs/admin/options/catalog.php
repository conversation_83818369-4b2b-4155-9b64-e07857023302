<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_CATALOG');

	if( isset($_POST['save']) ){
		foreach( $_POST['active'] as $code=>$value ){
			if( !cfg_variable_users_add($code, $_SESSION['usr_id'], $value) )
				$error = _("Une erreur inattendue est survenue lors de l'enregistrement de vos paramètres. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			else
				$_SESSION['usr_'.$code] = $value;
		}

		if( !isset($error) ){
			header('Location: catalog.php');
			exit;
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Catalogue') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Catalogue') . ' - ' . _('Mes options'));
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}
?>
<h2><?php echo _('Catalogue'); ?></h2>

<form action="catalog.php" method="post">
	<?php
		$ovr_usr = array();
		$rval = cfg_overrides_get(0, array(), '', $_SESSION['usr_id'] );
		// Récupère la value
		if( $rval!=false && ria_mysql_num_rows($rval)>0 ){
			while( $val = ria_mysql_fetch_array($rval) )
				$ovr_usr[$val['code']] = $val['value'];
		}
	?>
	<div class="block-notice-container">
	<p class="notice"><?php print _('Avec les paramètres ci-dessous, vous pouvez configurer l\'affichage des produits dans l\'espace d\'administration.'); ?></p>

	<table id="tb-ovr-usr">
		<caption><?php print _('Vos paramètres pour l\'affichage des listes de produits'); ?></caption>
		<thead>
			<tr>
				<th id="name"><?php print _('Paramètre'); ?></th>
				<th id="desc"><?php print _('Description'); ?></th>
				<th id="value" class="align-center"><?php print _('Activer'); ?></th>
			</tr>
		</thead>
		<tbody>
		<?php
			// Récupère les variables pouvant être modifier par un utilisateur.
			$ovrs = cfg_variables_get(array('admin_catalog_hide_source_unpublished', 'admin_hide_sleeping', 'admin_show_childs'));

			if( $ovrs!=false && ria_mysql_num_rows($ovrs)>0 ){
				while( $ovr = ria_mysql_fetch_array($ovrs) ){
					print '	<tr>
								<td headers="name">'.$ovr['name'].'</td>
								<td headers="desc">'.$ovr['desc'].'</td>
								<td headers="value" class="align-center">';
					if( array_key_exists($ovr['code'], $ovr_usr) ){
						print '	<input type="radio" name="active['.$ovr['code'].']" id="active-'.$ovr['code'].'-1" value="1" '.( $ovr_usr[$ovr['code']] ? 'checked="checked"' : '' ).' /><label for="active-'.$ovr['code'].'-1">'._('Oui').'</label>
								<input type="radio" name="active['.$ovr['code'].']" id="active-'.$ovr['code'].'-0" value="0" '.( $ovr_usr[$ovr['code']] ? '' : 'checked="checked"' ).' /><label for="active-'.$ovr['code'].'-0">'._('Non').'</label>';
					} else {
						print '	<input type="radio" name="active['.$ovr['code'].']" id="active-'.$ovr['code'].'-1" value="1" '.( $ovr['default'] ? 'checked="checked"' : '' ).' /><label for="active-'.$ovr['code'].'-1">'._('Oui').'</label>
								<input type="radio" name="active['.$ovr['code'].']" id="active-'.$ovr['code'].'-0" value="0" '.( $ovr['default'] ? '' : 'checked="checked"' ).' /><label for="active-'.$ovr['code'].'-0">'._('Non').'</label>';
					}
					print '		</td>
							</tr>';
				}
			}else{
				print _('Aucun paramétrage disponible');
			}
		?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3"><input type="submit" name="save" id="save" value="<?php print _('Enregistrer'); ?>" /></td>
			</tr>
		</tfoot>
	</table>
</div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
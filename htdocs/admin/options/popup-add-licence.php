<?php
	/** \file popup-add-licence.php
	 * 	Ce fichier permet de gérer l'ajout de nouvelles licences à un abonnement Yuto en cours
	 */

	require_once('email.inc.php');
	require_once('PaymentExternal/Payplug_old.inc.php');

	$is_retour_post = false;
	$error = false;

	// Chargement des informations sur le produit correspondant à l'abonnement choisi (depuis RiaStudio)
	$package = RegisterGCP::getPackage($config['tnt_id']);
	$prd_packages = dev_package_get_products($package);

	// Chargement de l'abonnement en cours
	$subscription_info = dev_subscribtions_yuto_get();

	// Calcul du prorata entre aujourd'hui et la date de fin de l'abonnement en cours
	$start  = new DateTime();
	$start2 = new DateTime(dateparse($subscription_info['date_start']));
	$end = $end2 = new DateTime(dateparse($subscription_info['date_end']));

	// Calcul de la date après la fin de l'abonnement
	$end2->modify('+ 1 day');
	$subscription_info['date_end_tomorrow'] = $end->format('d/m/Y');

	$interval = $start->diff($end);
	$interval2 = $start2->diff($end);
	$prorata = $interval->format('%a') / $interval2->format('%a');

	// Calcul de la date de début de la prochaine mensualité
	$next_sub = new Datetime(dateparse($subscription_info['date_end']));
	$next_sub->modify('+ 1 day');

	// Ajout de licences
	if( isset($_POST['more_licence']) ){
		$is_retour_post = true;

		// Contrôle que la quantité saisie est bien un entier supérieur à zéro
		if( !$_POST['nb_licence'] || $_POST['nb_licence'] <= 0 ){
			$error = _("Veuillez choisir une quantité de licence valide.");
		}

		// Contrôle qu'une modification du nombre de licence a été faite
		if( $_POST['nb_licence'] == $subscription_info['qte'] ){
			$error = _("La quantité de licence n'a pas changé.");
		}

		// Contrôle que les CGV sont cochés
		if( !isset($_POST['cgv']) || !$_POST['cgv'] ){
			$error = _("Veuillez accepter les conditions générales de vente.");
		}

		if( !$error ){
			// Charge les configurations mail Yuto VEL
			$cfg_emails = cfg_emails_yuto_get();

			// Charge les informations de tarifs en fonction du type d'abonnement : mensuel ou annuel
			$tmp_package = $prd_packages[$subscription_info['type'] == 'yearly' ? 'annuel' : 'mensuel'];

			// Détermine le prix d'une licence, sachant que celui-ci peut-être dégressif
			$price_ht = $tmp_package['price_ht'];
			$price_ttc = $tmp_package['price_ttc'];

			// Applique le tarif dégressif pour la version Business
			if( $package == 'business' ){
				if( $_POST['nb_licence'] >= 10 ){
					$price_ht = $tmp_package['price_ht_10'];
					$price_ttc = $tmp_package['price_ttc_10'];
				}
			}

			// Détermine le coût pour l'hébergement lors d'un forfait Business
			$hosting = false;
			foreach( $prd_packages['hosting'] as $qte=>$data ){
				if( $_POST['nb_licence'] >= $qte ){
					$hosting = $prd_packages['hosting'][$qte];
				}
			}

			// Un paiement est réalisé seulement si le nombre de licence a augmenté
			if( $_POST['nb_licence'] > $subscription_info['qte'] ){
				// Calcul le nombre de nouvelle licence
				$new_licence = $_POST['nb_licence'] - $subscription_info['qte'];

				// Calcul le montant du prorata
				$prorata_ht  = $price_ht * $new_licence * $prorata;
				$prorata_ttc = $price_ttc * $new_licence * $prorata;

				// Applique un surcoût lié à l'hébergement pour la version Business
				// Seulement si le nombre de licence actuel est inférieur à 10 et qu'après le nombre total est supérieur à 10
				if( $package == 'business' ){
					if( $subscription_info['qte'] < 10 && $_POST['nb_licence'] >= 10 ){
						if( is_array($hosting) && ria_array_key_exists(array('price_ht', 'price_ttc'), $hosting) ){
							// Applique le coût pour l'hébergement au prorata
							$prorata_ht  += ($hosting['price_ht'] - 100) * ($subscription_info['type'] == 'yearly' ? 12 : 1) * $prorata;
							$prorata_ttc += ($hosting['price_ttc'] - 120) * ($subscription_info['type'] == 'yearly' ? 12 : 1) * $prorata;
						}
					}
				}

				// Si le prorata est supérieur à zéro, un paiement sera alors réalisé
				if($prorata_ttc > 0 ){
					$tmp_config = $config;
					$old_ria_db_connect = $ria_db_connect;

					// Réalise une connexion sur le maria de RiaStudio
					RegisterGCPConnection::init(52, true);

					// Réalise un paiement du prorata avec PayPlug
					try{
						$payplug = new Payplug();
						$payplug->createSimplePayment( false, false, false, true, round($prorata_ttc, 2), $subscription_info['usr_id'], array('info' => 'Prorata de l\'ajout de '.$new_licence.' licence(s)', 'ord_id' => $subscription_info['ord_id']) );
					}catch (Exception $e) {
						$email = new Email();
						$email->setFrom( $cfg_emails['ord-owner']['from'] );
						$email->setTo( $cfg_emails['ord-owner']['to'] );

						if( $cfg_emails['ord-owner']['cc'] ){
							$email->addCc( $cfg_emails['ord-owner']['cc'] );
						}

						if( $cfg_emails['ord-owner']['bcc'] ){
							$email->addBcc( $cfg_emails['ord-owner']['bcc'] );
						}

						$email->setSubject('Ajout de licence Yuto - Erreur lors du paiement');
						$email->addParagraph('Une erreur est survenue sur le paiement lors de l\'ajout de licences supplémentaire pour le client : '.htmlspecialchars(tnt_tenants_get_name($config['tnt_id']).'.'));
						$email->addParagraph('Nombre de licence supplémentaire : '.$_POST['nb_licence']);
						$email->addParagraph('Erreur retourné par le paiement : ');
						$email->addParagraph($e);
						$email->send();

						$error = _('Une erreur est survenue lors du paiement, veuillez prendre contact avec nous à l’adresse <EMAIL>.');
					}

					$config = $tmp_config;
					$ria_db_connect = $old_ria_db_connect;
				}
			}
		}

		if( !$error ){
			// Mise à jour de l'abonnement courant
			// Si le client demande plus de licence : l'abonnement est immédiatement impacté
			// Si le client demande moins de licence : les informations sont enregistrées pour une prise en compte lors du renouvellement

			$new_price_ht  = $_POST['nb_licence'] * $price_ht;
			$new_price_ttc = $_POST['nb_licence'] * $price_ttc;

			if( $package == 'business' ){
				if (is_array($hosting) && ria_array_key_exists(array('price_ht', 'price_ttc'), $hosting)) {
					// Applique le coût pour l'hébergement
					$new_price_ht  += $hosting['price_ht'] * ($subscription_info['type'] == 'yearly' ? 12 : 1);
					$new_price_ttc += $hosting['price_ttc'] * ($subscription_info['type'] == 'yearly' ? 12 : 1);
				}
			}

			if( $_POST['nb_licence'] > $subscription_info['qte']){
				// Mise à jour de l'abonnement dans le cas où plusieurs de licences ont été activées
				if( !dev_subscribtions_upd( $subscription_info['id'], $_POST['nb_licence'], dateparse($subscription_info['date_start']), dateparse($subscription_info['date_end']), $new_price_ttc, $new_price_ht, null, null, null) ){
					$error = _('Une erreur est survenue lors de la mise à jour de la quantité de licence de l\'abonnement.');
				}
			}else{
				// Mise à jour de l'abonnement dans le cas où des licences sont désactivées
				if( !dev_subscribtions_upd( $subscription_info['id'], $subscription_info['qte'], dateparse($subscription_info['date_start']), dateparse($subscription_info['date_end']), false, false, $_POST['nb_licence'], $new_price_ttc, $new_price_ht) ){
					$error = _('Une erreur est survenue lors de la mise à jour de la quantité de licence de l\'abonnement.');
				}
			}

			// Envoie du mail de confirmation au client
			$email = new Email();
			$email->setFrom($cfg_emails['vel-yuto-notif']['from']);
			$email->setTo($_SESSION['usr_email']);

			if( trim($cfg_emails['vel-yuto-notif']['bcc']) != '' ){
				$email->addBcc($cfg_emails['vel-yuto-notif']['bcc']);
			}

			if( $_POST['nb_licence'] > $subscription_info['qte']){
				// Confirmation des modification par e-mail dans le cas où plusieurs de licences ont été activées
				$email->setSubject('Votre demande de nouvelles licences Yuto '.ucfirst($package));

				$nb_licences = number_format(($_POST['nb_licence'] - $subscription_info['qte']), 0);
				if( ($_POST['nb_licence'] - $subscription_info['qte']) > 1 ){
					$nb_licences .= ' licences supplémentaires';
				}else{
					$nb_licences .= ' licence supplémentaire';
				}

				$html = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/licence-confirmation.html');
				$html = str_replace('[site_url]', 'http://inscription-yuto.recette.riashop.fr', $html);
				$html = str_replace('[ord_count]', $nb_licences, $html);
				$html = str_replace('[package]', ($subscription_info['type'] == 'yearly' ? 'annuel' : 'mensuel'), $html);
				$html = str_replace('[date]', $subscription_info['date_end'], $html);
				$html = str_replace('[price_HT]', ria_number_format($new_price_ht, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[price]', ria_number_format($new_price_ttc, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[price_2_HT]', ria_number_format($prorata_ht, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[price_2]', ria_number_format($prorata_ttc, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[utm_campaign]', 'vel_'.$package, $html);


				$html = str_replace('Un prélèvement au prorata de votre abonnement en cours sera effectué pour un montant de 0 € TTC.', '', $html);
				$html = str_replace('Un prélèvement au prorata de votre abonnement en cours sera effectué pour un montant de 0,00 € TTC.', '', $html);
			}else{
				// Confirmation des modification par e-mail dans le cas où des licences sont désactivées
				$email->setSubject('Modification de votre abonnement Yuto '.ucfirst($package));

				$html = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/licence-remove.html');

				$html = str_replace('[licence]', $_POST['nb_licence'].' '.($_POST['nb_licence'] > 1 ? 'licences' : 'licence'), $html);
				$html = str_replace('[priceHT]', ria_number_format($new_price_ht, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[price]', ria_number_format($new_price_ttc, NumberFormatter::DECIMAL, 2), $html);
				$html = str_replace('[date_1]', $next_sub->format('d/m/Y'), $html);
				$html = str_replace('[date_2]', $subscription_info['date_end'], $html);
				$html = str_replace('[url-subscription]', 'https://app.riashop.fr/options/subscription.php', $html);
				$html = str_replace('[utm_campaign]', 'vel_'.$package, $html);
			}

			$email->addHtml($html);
			$email->send();
		}
	}

	define('ADMIN_PAGE_TITLE', _('Mes licences'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	print '
		<div class="msg">
	';

	if( $is_retour_post && $error){
		print '
				<div class="error">'.nl2br($error).'</div>
		';
	}elseif($is_retour_post){
		print '
				<div class="success">'.str_replace('#param[email]#', $_SESSION['usr_email'], _('Un e-mail de confirmation de votre demande vous a été envoyé à #param[email]#.')).'</div>
		';
	}

	print '
		</div>

		<form method="post">
			<input type="hidden" name="prorata" id="prorata" value="">

			<h2>'._('Mettre à jour mon abonnement').'</h2>

			<label for="nb_licence">'._('Nombre de licences souhaitées :').' </label><input type="number" name="nb_licence" id="nb_licence" min="1" value="'.(isset($_POST['nb_licence']) ? $_POST['nb_licence'] : $subscription_info['qte'] ).'"/>
			<label id="price"></label>
			<br>
	';

	if( $subscription_info['qte'] == 1 ){
		print '<i>'.sprintf( _('Vous avez actuellement %s licence active.'), $subscription_info['qte'] ).'</i>';
	}else{
		print '<i>'.sprintf( _('Vous avez actuellement %s licences actives.'), $subscription_info['qte'] ).'</i>';
	}

	if( $package == 'business' ){
		print '<strong> '._('(coût de la licence dégressif à partir de 10 licences)').'</strong>';
	}

	if( $package == 'business' ){
		print '
			<div id="notice-bo"></div>
		';
	}

	print '
			<br>
			<br>
			<div id="notice"></div>
			<input type="checkbox" id="cgv" name="cgv" required>
			<label for="cgv">&nbsp;'._('J\'accepte les <a target="_blank" href="https://start.yuto.fr/conditions-generales-de-vente/">Conditions générales de vente</a>.').'</label>
			<br>
			<br>

			<input type="submit" value="'._('J\'accepte le paiement de mes nouvelles licences').'" name="more_licence"/>
		</form>
	'; ?>

	<script>
		// Version d'abonnement en cours : essentiel ou business
		var typePackage = '<?php print ucfirst($package); ?>';

		// Information sur les tarifs de l'abonnement actuel
		var packages = <?php print json_encode($prd_packages); ?>;

		// Prorata en fonction du nombre de jours restant sur l'abonnement en cours
		var prorata = <?php print $prorata; ?>;

		// Information sur l'abonnement en cours
		var abo = <?php print json_encode($subscription_info); ?>;

		// Variable contenant le nombre de licence actuel
		var licenceNow = <?php print $subscription_info['qte']; ?>;

		var showNotice = function(){
			var nb_licence = parseInt( $('#nb_licence').val() );
			var textPrice, textNotice, backOffice = '';

			// Initialise les informations sur l'hébergement
			var hosting = false;

			var priceHT_Month  	= packages.mensuel.price_ht;
			var priceTTC_Month 	= packages.mensuel.price_ttc;
			var priceHT_Year  	= packages.annuel.price_ht;
			var priceTTC_Year 	= packages.annuel.price_ttc;

			// Réalisation de traitement spécifique sur la formule Business
			if (typePackage == 'Business') {
				// Détermine le cout pour l'hébergement lors d'un forfait Business
				for (h in packages.hosting) {
					if (nb_licence >= parseInt(h)) {
						hosting = packages.hosting[h];
					}
				}

				// Modification du prix HT et TTC à partir d'une quantité de 10
				if (nb_licence >= 10) {
					priceHT_Year = packages.annuel.price_ht_10;
					priceTTC_Year = packages.annuel.price_ttc_10;
					priceHT_Month = packages.mensuel.price_ht_10;
					priceTTC_Month = packages.mensuel.price_ttc_10;
				}
			}

			if( hosting === false ){
				hosting = {
					'price_ht'  : 0,
					'price_ttc' : 0,
				};
			}

			// Converti tous les montants en float pour permettre leur addition lors du calcul des totaux ci-dessous
			hosting.price_ht = parseFloat(hosting.price_ht);
			hosting.price_ttc = parseFloat(hosting.price_ttc);

			// Gestion du texte sur le prix
			if( abo.type == 'yearly' ){
				textPrice = yearPrice.replace('#param[tarifHT]#', number_format((priceHT_Year * nb_licence), 2, ',', ' '));
				textPrice = textPrice.replace('#param[tarif]#', number_format((priceTTC_Year * nb_licence), 2, ',', ' '));

				// Détermine le texte contenant les informations pour le paiement des nouvelles licences
				if( nb_licence != abo.qte ){
					if( nb_licence > abo.qte ){
						textNotice = (nb_licence - abo.qte) > 1 ? subscriptionYearlyPlural : subscriptionYearly;

						// Montant du prorata à payer
						var prorataHT  = priceHT_Year  * (nb_licence - abo.qte) * prorata;
						var prorataTTC = priceTTC_Year * (nb_licence - abo.qte) * prorata;

						// Le cout de l'hébergement augemente à partir de 10 licences
						if( licenceNow < 10 && nb_licence >= 10 ){
							prorataHT += (hosting.price_ht - 100) * 12 * prorata;
							prorataTTC += (hosting.price_ttc - 120) * 12 * prorata;
						}

						// Gestion des valeurs dynamique dans le texte sur les informations pour le paiement
						textNotice = textNotice.replace('#param[licences]#', 	(nb_licence - abo.qte));
						textNotice = textNotice.replace('#param[prorataHT]#', number_format(prorataHT, 2, ',', ' '));
						textNotice = textNotice.replace('#param[prorata]#', 	number_format(prorataTTC, 2, ',', ' '));
					}else{
						textNotice = nb_licence > 1 ? reduceSubscriptionYearlyPlural :  reduceSubscriptionYearly;

						// Gestion des valeurs dynamique dans le texte sur les informations pour le paiement
						textNotice = textNotice.replace('#param[licences]#', nb_licence).replace('#param[licences]#', nb_licence);
						textNotice = textNotice.replace('#param[date_1]#', abo.date_end);
						textNotice = textNotice.replace('#param[date_2]#', abo.date_end_tomorrow);
						textNotice = textNotice.replace('#param[priceHT]#', number_format((nb_licence * priceHT_Year) + (hosting.price_ht * 12), 2, ',', ' '));
						textNotice = textNotice.replace('#param[price]#', number_format((nb_licence * priceTTC_Year) + (hosting.price_ttc * 12), 2, ',', ' '));
					}
				}

				// Texte pour le backoffice
				if (typePackage == 'Business') {
					backOffice = yutoBackOfficeYear.replace('#param[price ht]#', number_format((hosting.price_ht * 12), 2, ',', ' '));
					backOffice = backOffice.replace('#param[price ttc]#', number_format((hosting.price_ttc * 12), 2, ',', ' '));
				}
			}else{
				textPrice = monthPrice.replace('#param[tarifHT]#', number_format((priceHT_Month * nb_licence), 2, ',', ' '));
				textPrice = textPrice.replace('#param[tarif]#', number_format((priceTTC_Month * nb_licence), 2, ',', ' '));

				// Détermine le texte contenant les informations pour le paiement des nouvelles licences
				if( nb_licence != abo.qte ){
					if( nb_licence >= abo.qte ){
						textNotice = (nb_licence - abo.qte) > 1 ? subscriptionMonthlyPlural : subscriptionMonthly;

						// Montant du prorata à payer
						var prorataHT  = priceHT_Month  * (nb_licence - abo.qte) * prorata;
						var prorataTTC = priceTTC_Month * (nb_licence - abo.qte) * prorata;

						// Le cout de l'hébergement augemente à partir de 10 licences
						if( licenceNow < 10 && nb_licence >= 10 ){
							prorataHT += (hosting.price_ht - 100) * prorata;
							prorataTTC += (hosting.price_ttc - 120) * prorata;
						}

						// Gestion des valeurs dynamique dans le texte sur les informations pour le paiement
						textNotice = textNotice.replace('#param[licences]#', 	(nb_licence - abo.qte));
						textNotice = textNotice.replace('#param[prorataHT]#', number_format(prorataHT, 2, ',', ' '));
						textNotice = textNotice.replace('#param[prorata]#',		number_format(prorataTTC, 2, ',', ' '));
					}else{
						textNotice = nb_licence > 1 ? reduceSubscriptionMonthlyPlural :  reduceSubscriptionMonthly;

						// Gestion des valeurs dynamique dans le texte sur les informations pour le paiement
						textNotice = textNotice.replace('#param[licences]#', nb_licence).replace('#param[licences]#', nb_licence);
						textNotice = textNotice.replace('#param[date_1]#', abo.date_end);
						textNotice = textNotice.replace('#param[date_2]#', abo.date_end_tomorrow);
						textNotice = textNotice.replace('#param[priceHT]#', number_format((nb_licence * priceHT_Month) + hosting.price_ht, 2, ',', ' '));
						textNotice = textNotice.replace('#param[price]#', number_format((nb_licence * priceTTC_Month) + hosting.price_ttc, 2, ',', ' '));
					}
				}

				// Texte pour le backoffice
				if (typePackage == 'Business') {
					backOffice = yutoBackOfficeMonth.replace('#param[price ht]#', number_format(hosting.price_ht, 2, ',', ' '));
					backOffice = backOffice.replace('#param[price ttc]#', number_format(hosting.price_ttc, 2, ',', ' '));
				}
			}

			// Affichage des informations sur le paiement
			$('[id="notice"]').html( textNotice );
			if( $.trim(textNotice) != '' ){
				$('[id="notice"]').addClass('notice');
			}else{
				$('[id="notice"]').html('').removeClass('notice');
			}

			// Affichage du total
			$('[id="price"]').html( textPrice );

			// Affiche les informations sur le coût d'hébergement
			if( $.trim(backOffice) != '' ){
				$('#notice-bo').html('<br /><br />' + backOffice);
			}
		}

		$('[id="nb_licence"]').change(function(){
			showNotice();
		});

		showNotice();
	</script>

<?php
    /**	\file subscription-history.php
	 *	Cette page permet la visualisation de l'historique des abonnements Yuto ainsi que des factures.
	 */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_SUBSCRIPTION');

    require_once('Pdf/pdf.inc.php');
    if( isset($_GET['inv'], $_GET['ord']) ){
        $old_config = $config;
        $old_ria_db_connect = $ria_db_connect;

        // Réalise une connexion à RiaStudio
        RegisterGCPConnection::init(52, true);
		try{
			generate_invoice($_GET['ord'], $_GET['inv'], true, null/*, $ord, false, false, $data*/);
		}catch(Exception $e){
			$error = $e->getMessage();
		}

        $config = $old_config;
        $old_ria_db_connect = $ria_db_connect;
    }

    // Récupère les informations sur l'abonnement en cours
    $subscription_info = false;

    $sub = dev_subscribtions_yuto_get();
    if( is_array($sub) && count($sub) ){
	    if( $sub['in_testing'] ){
		    // Si l'abonnement actuel est un abonnement de test, on regarde si un futur abonnement actif est prit
		    $sub_f = dev_subscribtions_yuto_get(false, true);
		    if( is_array($sub_f) && count($sub_f) ){
			    $subscription_info = $sub_f;
		    }
	    }else{
		    $subscription_info = $sub;
	    }
    }

    // Historique des abonnements
    $r_sub = dev_subscribtions_get(null, array('date_start'=>'desc'));

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Mon historique d\'abonnements') );

	// Défini le titre de la page
    define('ADMIN_PAGE_TITLE', _('Mon historique d\'abonnements').' - '._('Mes options'));
    require_once('admin/skin/header.inc.php')
?>

    <h2><?php echo _('Mon historique d\'abonnements'); ?></h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br($error).'</div>';
		}
	?>

	<div class="notice"><?php
		if( !$subscription_info ){
			if( is_array($sub) && count($sub) ){
				print _('Vous êtes actuellement en période d’essai. Rendez-vous dans la rubrique <a href="/admin/options/subscription.php">"Mon abonnement"</a> pour en savoir plus.');
			}else{
				print _('Aucun abonnement en cours. <a href="#" onclick="displayPopup(\'Mes licences\', \'\', \'/admin/options/popup-reactivate-subscription.php\');return false;">Activez votre abonnement pour profiter pleinement de Yuto</a>.');
			}
		}elseif( $subscription_info['type'] == 'monthly' ){
			print _('Vous avez souscrit à un <strong>abonnement mensuel</strong>. Rendez vous dans la rubrique "<a href="/admin/options/subscription.php">Mon abonnement</a>" pour en savoir plus.');
		}else{
			print _('Vous avez souscrit à un <strong>abonnement annuel</strong>. Rendez vous dans la rubrique "<a href="/admin/options/subscription.php">Mon abonnement</a>" pour en savoir plus.');
		}
	?></div>

	<?php if( $r_sub && ria_mysql_num_rows($r_sub) ){ ?>
	    <table class="list sub-history">
    	    <thead>
        	    <tr>
                	<th class="align-center"><?php print _('Date de début d\'engagement'); ?></th>
	                <th class="align-center"><?php print _('Date de fin d\'engagement'); ?></th>
    	            <th class="align-center"><?php print _('Moyen de paiement'); ?></th>
        	        <th class="align-center"><?php print _('Total H.T.'); ?></th>
            	    <th class="align-center"><?php print _('Total T.T.C.'); ?></th>
                	<th class="align-center"><?php print _('Nombre de licence'); ?></th>
	                <th class="align-center"><?php print _('Facture'); ?></th>
	            </tr>
	        </thead>
			<tbody><?php
				// Charge la configuration propre à RiaStudio
				{
					$old_config = $config;
					$old_ria_db_connect = $ria_db_connect;

					RegisterGCPConnection::init(52, true);
					$riastudio_config = $config;
					$riastudio_db_connect = $ria_db_connect;

					$config = $old_config;
					$ria_db_connect = $old_ria_db_connect;
				}

				while( $sub = ria_mysql_fetch_assoc($r_sub) ){
					print '<tr>
        	            <td class="align-center">'.$sub['date_start'].'</td>
						<td class="align-center">'.$sub['date_end'].'</td>
					';

					if( is_numeric($sub['testing']) && $sub['testing'] > 0 ){
						print '<td class="align-center" colspan="3">'.str_replace('#param[nombre de jours]#', $sub['testing'], _('Période d\'essai de #param[nombre de jours]# jours')).'</td>';
					}else{
						print '
	                    	<td class="align-center">'._('Carte Bleue').'</td>
		                    <td class="align-center">'.number_format($sub['cost_ht'], 2, ',', ' ').' €</td>
    		                <td class="align-center">'.number_format($sub['cost'], 2, ',', ' ').' €</td>
						';
					}

					print '
        	            <td class="align-center">'.$sub['qte'].'</td>
						<td class="align-center">
					';

					if( !is_numeric($sub['testing']) || $sub['testing'] <= 0 ){
						// On recherche la facture lié à la commande
						$ar_invoices = false;

						if( is_numeric($sub['ord_id']) && $sub['ord_id'] > 0 ){
							$old_config = $config;
							$old_ria_db_connect = $ria_db_connect;

							$config = $ria_db_connect = false;
							// Charge la configuration de RiaStudio afin de récupérer la facture liée à l'abonnement
							$config = $riastudio_config;
							$ria_db_connect = $riastudio_db_connect;

							// Récupère la facture liée à l'abonnement
							$ar_invoices = ord_orders_get_invoices_list($sub['ord_id']);

							$config = $old_config;
							$ria_db_connect = $old_ria_db_connect;
						}

						if( is_array($ar_invoices) && count($ar_invoices) ){
								foreach( $ar_invoices as $one_invoice ){
										print '<a href="subscription-history.php?ord='.$sub['ord_id'].'&amp;inv='.$one_invoice['id'].'">'._('Télécharger').'</a>';
										break;
								}
						}else{
							print _('Bientôt disponible');
						}
					}else{
							print '-';
					}

					print '</td>
		       	        </tr>
					';
				}
		?></tbody>
    </table>

<?php
	}

	require_once('admin/skin/footer.inc.php');
?>

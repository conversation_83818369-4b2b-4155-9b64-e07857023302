<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_EDIT');

	require_once('documents.inc.php');

	if( !isset($_GET['srctype']) || !doc_types_exists($_GET['srctype']) ){
		header('Location: /admin/documents/index.php?type=0');
		exit;
	}

	if( !isset($_GET['doc']) || !is_array($_GET['doc']) || !sizeof($_GET['doc']) ){
		header('Location: /admin/documents/index.php?type='.$_GET['srctype']);
		exit;
	}

	$rdoc = doc_documents_get( $_GET['doc'] );
	if( !$rdoc || !ria_mysql_num_rows($rdoc) ){
		header('Location: /admin/documents/index.php?type='.$_GET['srctype']);
		exit;
	}

	if( isset($_POST['move']) ){
		if( !doc_documents_move($_GET['doc'], $_POST['dest']) ){
			$error = _("Une erreur inattendue s'est produite lors du déplace du ou des documents. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			header('Location: /admin/documents/index.php?type='.$_POST['dest']);
			exit;
		}
	}

	$parent = isset($_GET['type']) && doc_types_exists( $_GET['type'] ) ? $_GET['type'] : null;

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php?type=0' );

	$parent = doc_types_get_parents_array( $_GET['type'], 'desc' );
	if( is_array($parent) && sizeof($parent) ){
		foreach( $parent as $one_t ){
			$t = ria_mysql_fetch_array( doc_types_get($one_t) );
			Breadcrumbs::add( $t['name'], '/admin/documents/index.php?type='.$t['id'] );
		}
	}

	$type = ria_mysql_fetch_array(doc_types_get($_GET['type']));
	Breadcrumbs::add( $type['name'], '/admin/documents/index.php?type='.$type['id'] )
		->push( _('Déplacer un ou plusieurs documents') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Déplacer un ou plusieurs documents'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>' . _("Déplacer un ou plusieurs documents") . '</h2>
	';

	if( isset($error) ){
		print '
			<div class="error">'.nl2br($error).'</div>
		';
	}

	print '
		<p>' . _("Naviguez jusqu'à l'emplacement désiré à l'aide des liens présents sur les noms de type de documents. Pour déposer les documents sélectionnés dans le type de documents souhaité, cochez l'option présente devant son nom puis cliquez sur le bouton « Déplacer ».") . '</p>
		<p>' . _("Les documents suivants seront déplacés :") . '</p>
		<ul>
	';

	while( $doc = ria_mysql_fetch_assoc($rdoc) ){
		print '
			<li>'.htmlspecialchars( $doc['name'] ).'</li>
		';
	}

	print '
		</ul>
	';

	$link = '/admin/documents/move.php?srctype='.$_GET['srctype'];
	foreach( $_GET['doc'] as $one_doc ){
		$link .= '&amp;doc[]='.$one_doc;
	}

	$rtype = doc_types_get( 0, false, true, false, false, false, $parent );

	print '
		<form action="'.$link.'" method="post">
			<table>
				<col width="17" /><col width="300" /><col width="120" />
				<caption>' . _("Choisissez un nouvel emplacement") . '</caption>
				<thead>
					<tr>
						<th></th>
						<th id="type-name">' . _("Désignation") . '</th>
						<th id="type="products">' . _("Documents") . '</th>
					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="3">

							<input name="move" value="' . _("Déplacer") . '" onclick="return doc_control_form_move();" type="submit">
							<input name="cancel" value="' . _("Annuler") . '" type="submit">
						</td>
					</tr>
				</tfoot>
				<tbody>
	';

	while( $type = ria_mysql_fetch_assoc($rtype) ){
		print '
				<tr>
					<td>
						<input class="radio" name="dest" value="'.$type['id'].'" id="dest-'.$type['id'].'" type="radio" />
					</td>
					<td>
		';

		$rchild = doc_types_get( 0, false, true, false, false, false, $type['id'] );
		if( !$rchild || !ria_mysql_num_rows($rchild) ){
			print '
						<label for="dest-'.$type['id'].'">'.htmlspecialchars( $type['name'] ).'</label>
			';
		}else{
			print '
						<a href="'.$link.'&amp;type='.$type['id'].'" title="' . _("Afficher les sous-types") . '">'.htmlspecialchars( $type['name'] ).'</a>
			';
		}

		print '
					</td>
					<td align="right">'.number_format( $type['docs'], 0 , '', ' ' ).'</td>
				</tr>
		';
	}

	if( is_numeric($parent) && $parent>0 ){
		$parent = ria_mysql_fetch_assoc( doc_types_get($parent) );

		print '
					<tr>
						<td>
							<input class="radio" name="dest" value="'.$parent['id'].'" id="dest-here" type="radio" />
						</td>
						<td colspan="2">
							<label for="dest-here">' . _("Placer ici, dans") . ' "'.htmlspecialchars( $parent['name'] ).'"</label>
						</td>
					</tr>
		';
	}

	print '
				</tbody>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');

<?php

	/**	\file index.php
	 *
	 *	Ce fichier est la page d'accueil de la gestion des images de l'ensemble de la plateforme (produits, documents, etc...).
	 *	Cette page propose les fonctionnalités suivantes :
	 *	- Afficher les images de la médiathèque
	 *	- Permet leur recherche par mots clé
	 *	- Exporter les images au format CSV
	 *	- Ajouter une ou plusieurs images
	 */

	require_once('images.inc.php');

	$unused = isset($_GET['unused']) ? true : false;

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	if( $unused ){
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_UNUSED');
	}else{
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');
	}
	
	// Suppression des images de façon définitive
	if( isset($_POST['del-img-id']) ){
		if( is_array($_POST['del-img-id']) && sizeof($_POST['del-img-id']) ){
			foreach( $_POST['del-img-id'] as $img ){
				if( !is_numeric($img) || $img<=0 ){
					continue;
				}
				
				if( !img_images_del($img, true) ){
					$error = str_replace("#param[num_image]#", $img, _("Une erreur inattendue s'est produite lors de la suppression de l'image n°#param[num_image]#. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.")) ;
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/documents/images/index.php?page=1&unused=1');
			exit;
		}
	}
	
	// récupère les images
	$rimg = img_images_get( 0, '', '', '', isset($_POST['search-img']) ? $_POST['search-img'] : '', ($unused ? 0 : null), false, false, true, null, array('date_modified'=>'desc') );

	// export des images dans un fichier csv
	if( isset($_POST['export-img']) ){
		require_once('export-csv.php');
		exit;
	}
	
	$limit_img = 70;
	
	// dimensions des images
	$size = $config['img_sizes']['medium'];
	$size_high = $config['img_sizes']['high'];
	
	$count_img = $rimg ? ria_mysql_num_rows($rimg) : 0;
	$pages = ceil( $count_img / $limit_img );
	
	if( !isset($_GET['page']) || !is_numeric($_GET['page']) ){
		$page = 1;
	}elseif( isset($_GET['page']) && $_GET['page']>$pages ){
		$page = $pages;
	}else{
		$page = $_GET['page']>0 ? $_GET['page'] : 1;
	}
	
	$page_start = $page>5 ? $page-5 : 1;
	$page_stop = $page+5<$pages ? $page+5 : $pages;

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Images'), '/admin/documents/images/index.php' );

	if( isset($_GET['unused']) ){
		Breadcrumbs::add( _('Non utilisées') );
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Images') . ' - ' . _('Médiathèque'));
	require_once('admin/skin/header.inc.php');

	// Affiche le titre de la page
	print '<h2>';
	if( isset($_POST['search-img']) ){
		print _('Recherche d\'images');
	}else{
		print ria_number_format($count_img).($count_img > 1 ? _(' images') : _(' image')).($unused ? _(' non utilisée').($count_img > 1 ? 's' : '') : '');
	}
	print '</h2>';
	
	print '
		<form action="/admin/documents/images/index.php?page='.$page.( $unused ? '&amp;unused=1' : '' ).'" method="post">
			<input type="hidden" name="is_unused" id="is_unused" value="'.( $unused ? 1 : 0 ).'" />
			<input type="hidden" name="limit_for_page" id="limit_for_page" value="'.$limit_img.'" />
			<div id="actions-img">';
	if( $count_img>0 || isset($_POST['search-img']) ){
		print '
				<div id="div-search-img">
					'._('Rechercher une image :').'
					<input type="text" name="search-img" id="search-img" value="'.( isset($_POST['search-img']) ? $_POST['search-img'] : '' ).'" placeholder="Recherche" />
					<input type="submit" name="btn-search-img" id="btn-search-img" value="' . _("Chercher") . '" onclick="return searchImages();" />
				</div>';
	}
	
	if( !$unused ){
		print '<div class="div-action-img">';
		if( $count_img>0 ){
			print '<input type="submit" name="export-img" id="export-img" value="' . _("Exporter") . '" title="' . _("Exporter les images rattachées à un produit") . '" /> ';
		}
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ADD') ){
			print '<input type="button" name="add-imgs" id="add-imgs" value="' . _("Ajouter une image") . '" title="' . _("Ajouter une ou plusieurs images dans votre médiathèqe.") . '" />';
		}
		print '</div>';
	}elseif( $count_img>0 ) {
		print '
				<div class="div-action-img">
					<a class="selectedall" href="#">' . _("Sélectionner toutes les images") . '</a>&nbsp;|&nbsp;<a class="unselectedall" href="#">' . _("Désélectionner toutes les images") . '</a>
					<input class="no-click" type="submit" name="del-img" value="' . _("Supprimer") . '" id="del-img" />
				</div>
		';
	}
	
	print '</div>';

	// Message d'erreur si aucune image
	if( $count_img<=0 ){
		print '<div class="notice">';
		if( $unused ){
			print _('Toutes les images sont utilisées');
		}elseif( isset($_POST['search-img']) ){
			print _('Aucune image ne correspond à votre recherche.');
		}else{
			print _('Aucune image n\'est pour le moment enregistrée.');
		}
		print '</div>';
	}
	
	if( $count_img>0 ){
		print '
				<div id="img-media">
		';
		
		if( $rimg && ria_mysql_num_rows($rimg) ){
			ria_mysql_data_seek( $rimg, ($page-1)*$limit_img );
			
			print '
					<ul  class="autoload listing" data-page="'.$page.'" data-pages="'.$pages.'">
			';
			
			$delete_imgs = isset($_POST['del-img-id']) && is_array($_POST['del-img-id']) ? $_POST['del-img-id'] : array();

			$count = 0;
			while( $img = ria_mysql_fetch_array($rimg) ){
				if( $count>=$limit_img )
					break;
				
				print '
						<li class="img-images">
				';
				
				$checked = $class = '';
				if( $unused ){
					if( in_array($img['id'], $delete_imgs) ){
						$class = 'class="item-deleted"';
						$checked = 'checked="checked"';
					}
					
					print '
							<input '.$checked.' class="input-del-item" type="checkbox" name="del-img-id[]" value="'.$img['id'].'" />
					';
				}
				
				print '
							<a href="edit.php?image='.$img['id'].( $unused ? '&amp;unused=1' : '' ).'" >
								<img '.$class.' src="'.$config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$img['id'].'.'.$size['format'].'" alt="" width="'.$size['width'].'" height="'.$size['height'].'" />
							</a>
						</li>
				';
				
				$count++;
			}
			
			print '
					</ul>
			';
		}
		
		print '	
				</div>
			</form>
			<div class="clear"></div>
			<div id="pagination">
				'.( $page>1 ? '<a href="/admin/documents/images/index.php?page='.($page-1).'">&laquo; ' . _("Page précédente") . '</a> | ' : '&nbsp;' ).'
		';
		
		for( $i=$page_start ; $i<=$page_stop ; $i++ ){
			if( $i==$page ){
				print '<b>'.$i.'</b>'.( $i==$page_stop ? '' : ' | ' );
			}else{
				print '		<a href="/admin/documents/images/index.php?page='.$i.'">'.$i.'</a>'.( $i==$page_stop ? '' : ' | ' );
			}
		}
		
		print '
				'.( $page<$pages ? ' | <a href="/admin/documents/images/index.php?page='.($page+1).'">' . _("Page suivante") . ' &raquo;</a>' : '&nbsp;' ).'
			</div>
			<div id="infos-img"><div>&nbsp;</div></div>
		';
	}

	require_once('admin/skin/footer.inc.php');

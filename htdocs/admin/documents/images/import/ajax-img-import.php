<?php

	/**	\file ajax-img-import.php
	 *
	 * 	Ce fichier fourni les services ajax liées à la page Médiathèque > Association automatique :
	 *
	 *	- load-form : recherche de correspondances
	 *	- save-link : enregistrement d'une association
	 */

	require_once('images.inc.php');
	require_once('products.inc.php');
	require_once('prd/images.inc.php');

	// Si load-form est précisé, recherche de correspondances
	if( isset($_POST['load-form']) ){
		$img_id = is_numeric($_POST['load-form']) && $_POST['load-form'] > 0 ? $_POST['load-form'] : false;
		$src_name = isset($_POST['src-name']) ? $_POST['src-name'] : '';

		print view_images_get_autolinks( $img_id, $src_name );
	}

	// Si save-link, enregistrement de l'association
	if( isset($_POST['save-link']) ){
		if( isset($_POST['add-links']) && is_array($_POST['add-links']) && sizeof($_POST['add-links']) ){
			$ar_refresh_count 			= array();
			$all_main_sets 				= array();
			$all_secondary_prd_recalc 	= array();

			foreach( $_POST['add-links'] as $img=>$cnts ){
				// contenus auxquels l'image doit être rattachée
				foreach( $cnts as $type=>$ids ){
					foreach( $ids as $id=>$cnt ){
						if( $_POST['position-links'][ $img ][ $type ][ $cnt ]=='main' ){
							// Ajout de l'image principale du produit
							$tmp = prd_images_main_add_existing( $cnt, $img, false, false );

							if( $tmp === false ){
								$error = "Une erreur inattendue s'est produite lors de la sauvegarde de l'image principale".$cnt."-".$img.".\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.";
							}else{
								if( is_array($tmp) && sizeof($tmp) ){
									$ar_refresh_count = array_merge( $ar_refresh_count, $tmp );
								}else{
									$ar_refresh_count[] = $img;
								}

								// par cette ligne, on s'assure que, si une image est mise explicitement sur le parent, elle ne sera pas écrasée par une image provenant d'un enfant
								// mais uniquement sur la "session" en cours, si l'image du parent avait été mise plus tôt, elle sera écrasée tout de même
								$all_main_sets[] = $cnt;

								// si variable de configuration activée, chargement du ou des parent(s)
								if( isset($config['set_image_on_parent']) && $config['set_image_on_parent'] ){
									if( $rparent = prd_parents_get( $cnt, false, false ) ){
										while( $parent = ria_mysql_fetch_array($rparent) ){
											// on teste si une image n'a pas déjà été mise sur le parent
											if( !in_array( $parent['id'], $all_main_sets ) ){
												// assigne l'image sur le parent
												if( prd_images_main_add_existing($parent['id'], $img, false, false) ){
													$all_main_sets[] = $parent['id'];
												}
											}
										}
									}
								}
							}
						}else{
							// Ajout d'une image secondaire du produit
							$tmp = prd_images_add_existing( $cnt, $img, true, false );

							if( $tmp === false ){
								$error = "Une erreur inattendue s'est produite lors de la sauvegarde de l'image secondaire.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.";
							}elseif( $config['img_sync_with_prd_secondary'] && !in_array($cnt, $all_secondary_prd_recalc) ){
								if( is_array($tmp) && sizeof($tmp) ){
									$ar_refresh_count = array_merge( $ar_refresh_count, $tmp );
								}else{
									$ar_refresh_count[] = $img;
								}

								// produit dont on devra recalculer la position des images secondaires
								$all_secondary_prd_recalc[] = $cnt;
							}
						}
					}
				}
			}

			if( sizeof($ar_refresh_count) ){
				$ar_refresh_count = array_unique( $ar_refresh_count );
				img_images_count_update( $ar_refresh_count );
			}

			// recalcul de la position de toutes les images secondaires de tous les produits
			foreach( $all_secondary_prd_recalc as $pid ){
				prd_images_rebuild_order( $cnt );
			}
		}

		if( !isset($error) ){
			print json_encode( array('res'=>1) );
		}else{
			print json_encode( array('res'=>0, 'message'=>_($error)) );
		}
	}

	// Bouton Annuler (une association automatique)
	if( isset($_POST['cancel-link']) ){
		if( isset($_POST['add-links']) && is_array($_POST['add-links']) && sizeof($_POST['add-links']) ){
			foreach( $_POST['add-links'] as $img=>$cnts ){
				// contenus auxquels l'image doit être détachée
				foreach( $cnts as $type=>$ids ){
					foreach( $ids as $id=>$cnt ){
						if( $_POST['position-links'][ $img ][ $type ][ $cnt ]=='main' ){
							// Retirer l'image principale du produit
							prd_images_main_del( $cnt );
							prd_images_del( $cnt, $img );
						}else{
							// Retirer l'image secondaire du produit
							prd_images_del( $cnt, $img );
						}
					}
				}

				img_images_set_is_associated( $img, false );
			}
		}

		print json_encode( array('res'=> 1) );
	}

	// Bouton Masquer
	if( isset($_POST['masked-img']) ){
		if( !img_images_set_is_masked($_POST['masked-img'], true) ){
			$error = _("Une erreur inattendue s'est produite lors du retrait de l'association automatique de cette image.");
		}

		if( !isset($error) ){
			print json_encode( array('res'=>1) );
		}else{
			print json_encode( array('res'=>0, 'message'=>_($error)) );
		}
	}

	// Lien Annuler (pour le bouton Masquer)
	if( isset($_POST['cancel-masked']) ){
		if( !img_images_set_is_masked($_POST['cancel-masked'], false) ){
			$error = _("Une erreur inattendue s'est produite lors de la réaction de cette image dans l'association automatique.");
		}

		if( !isset($error) ){
			print json_encode( array('res'=>1) );
		}else{
			print json_encode( array('res'=>0, 'message'=>_($error)) );
		}
	}

	if( isset($_POST['delete-img']) ){
		if( !img_images_del($_POST['delete-img'], true) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'image.");
		}

		if( !isset($error) ){
			print json_encode( array('res'=>1) );
		}else{
			print json_encode( array('res'=>0, 'message'=>_($error)) );
		}
	}

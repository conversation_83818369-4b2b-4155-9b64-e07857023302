<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

	require_once('images.inc.php');
	
	if( !isset($_GET['image']) || !img_images_exists($_GET['image']) ){
		header('HTTP/1.0 404 Not Found');
		include('errors/404.php');
		exit;
	}
	
	
	$filesource = img_images_get_filesource( $_GET['image'] );
	
	$img = ria_mysql_fetch_array( img_images_get( $_GET['image'] ) );
	
	header('Content-Type: application/x-force-download');
	header('Content-Disposition: attachment; filename="'.(isset($_GET['filename']) ? $_GET['filename'] : str_replace(array('.jpg','.png','.gif','.tif','.bmp'), '', strtolower($filesource)).'.'.($img['type']?$img['type']:'jpg')).'"');
	ob_clean();
	flush();
	readfile( $config['img_dir'].'/source/'.$filesource );
	

<?php
	require_once('documents.inc.php');
	unset($error);

	// Vérifie l'existance du type de document (mode édition uniquement)
	if( isset($_GET['type']) && $_GET['type']!=0 && !doc_types_exists($_GET['type']) ){
		header('Location: index.php');
		exit;
	}

	if( !isset($_GET['tab']) ) $_GET['tab'] = '';
	if( isset($_POST['tabFields']) || $_GET['tab']=='fields' ){
		$tab = 'fields';
	}elseif( isset($_POST['tabImages']) || $_GET['tab']=='images' ){
		$tab = 'images';
	}else{
		$tab = 'general';
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		doc_types_del($_GET['type']);
		header('Location: index.php');
		exit;
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		$parent = isset($_GET['parent']) ? $_GET['parent'] : 0;

		// Vérifie que toutes les informations obligatoires sont disponibles
		if( !isset($_POST['name'],$_POST['desc']) || !trim($_POST['name']) ){
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.\nVeuillez vérifier. Les champs marqués d'une * sont obligatoires.");
		}elseif( $_GET['type']==0 ){
			$_GET['type'] = doc_types_add( addslashes($_POST['name']), $_POST['desc'], $parent );
			if( !$_GET['type'] )
				$error = 1; // Utilise le message par défaut
		}elseif( $_GET['type']>0 ){
			$parent = $_GET['type'];

			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
				if( !doc_types_update($_GET['type'],$_POST['name'],$_POST['desc']) )
					$error = 1; // Utilise le message par défaut
			} elseif( in_array($_GET['lng'], $config['i18n_lng_used']) ){
				$values = array(
					_FLD_DOC_TYPE_NAME=>$_POST['name'],
					_FLD_DOC_TYPE_DESC=>$_POST['desc']
				);

				if( !fld_translates_add($_GET['type'], $_GET['lng'], $values) ){
					$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}
		}

		if( isset($error) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du type de document.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			header('Location: /admin/documents/index.php?type='.$parent);
		}
	}

	// Action sur l'onglet "Avancés"
	if( isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0 ){
		view_admin_tab_fields_actions( CLS_TYPE_DOCUMENT, $_GET['type'], $config['i18n_lng'] );
	}

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Types de documents'), '/admin/documents/index.php?type=0' );

	$type = array('id'=>0, 'name'=>'', 'desc'=>'', 'docs'=>0, 'docs_childs'=>0);
	if( isset($_GET['type']) && $_GET['type']>0 ){
		$type = ria_mysql_fetch_array(doc_types_get( $_GET['type'], true ));
		$type['docs_childs'] = doc_types_get_docs_count( $_GET['type'] );
		// Récupère les informations traduites
		if( $lng!=$config['i18n_lng'] ){
			$tsk_type = fld_translates_get( CLS_PRODUCT, $type['id'], $lng, $type, array(_FLD_DOC_TYPE_NAME=>'name', _FLD_DOC_TYPE_DESC=>'desc' ), true );
			$type['name'] = $tsk_type['name'];
			$type['desc'] = $tsk_type['desc'];
		}
	}

	if( isset($type['name']) && trim($type['name'])!='' ) {
		$title_type = $type['name'];
	}else{
		$title_type = _("Nouveau type de document");
	}

	Breadcrumbs::add( $title_type );

	define('ADMIN_PAGE_TITLE', _('Edition') . ' - ' . _('Types de documents') . ' - ' . _('Nouveau type de document'));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php print htmlspecialchars($title_type); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	// Affiche le menu de langue
	if( isset($_GET['type']) && $_GET['type']>0 ) {
		print view_translate_menu( 'edit.php?type='.$type['id'], $lng );
	}
?>

<form action="edit.php?type=<?php print $type['id']; ?>&amp;parent=<?php print isset($_GET['parent']) ? $_GET['parent'] : 0; ?>&amp;lng=<?php print $lng; ?>" method="post" <?php  if( $tab == 'general' ){ print 'onsubmit="return validForm(this)"'; } ?>>
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php echo _("Général"); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( view_admin_show_tab_fields( CLS_TYPE_DOCUMENT, isset($_GET['type']) ? $_GET['type'] : 0 ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php echo _("Avancé"); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabImages" value="<?php echo _("Images"); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
	</ul>
	<div id="tabpanel">
	<?php if( $tab == 'general' ){?>
		<table>
		<tbody>
			<tr>
				<td><label for="name"><span class="mandatory">*</span> <?php print _('Désignation :');?></label></td>
				<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($type['name']); ?>" maxlength="45" /></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Description :');?></label></td>
				<td><textarea name="desc" id="desc" cols="50" rows="5"><?php print htmlspecialchars($type['desc']); ?></textarea></td>
			</tr>
			<tr>
				<td><?php print _('Documents :');?></td>
				<td><a href="../index.php?type=<?php print $type['id']; ?>"><?php print $type['docs'] ? $type['docs']._(' documents') : _('Aucun document'); ?></a><?php print ( $type['docs_childs'] - $type['docs'] ) > 0 ? ' ('.( $type['docs_childs'] - $type['docs'] )._(' document(s) sur les sous-types)') : ''; ?></td>
			</tr>
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
				<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return cancelEdit()" />
				<?php if( $type['id'] && gu_user_is_authorized('_RGH_ADMIN_DOCS_TYPE_DEL') ){ ?>
				<input type="submit" name="delete" value="<?php echo _("Supprimer"); ?>" onclick="return confirmDel();" />
				<?php } ?>
			</td></tr>
		</tfoot>
		</table>

	<?php
		}elseif( $tab=='images' ){
			print view_admin_img_table( CLS_TYPE_DOCUMENT, $type['id']);
		}elseif( $tab == 'fields' ){
			print view_admin_tab_fields( CLS_TYPE_DOCUMENT, $type['id'], $config['i18n_lng'] );
		}
	?>
	</div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
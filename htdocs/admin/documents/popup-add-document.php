<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_ADD');

	require_once('documents.inc.php');

	$tab = 'media';
	if( isset($_GET['tab']) ){
		$tab = $_GET['tab'];
	}

	if( isset($_POST['tabMedia']) ){
		$tab = 'media';
	}elseif( isset($_POST['tabAdd']) ){
		$tab = 'add';
	}

	$objs = array();
	$action = 'popup-add-document.php';
	if( isset($_GET['cls']) ){
		$action .= (strstr($action, '?') ? '&amp;' : '?').'cls='.$_GET['cls'];
	}

	if( isset($_GET['obj_0']) ){
		$objs[] = $_GET['obj_0'];
		$action .= (strstr($action, '?') ? '&amp;' : '?').'obj_0='.$_GET['obj_0'];
	}

	if( isset($_GET['obj_1']) ){
		$objs[] = $_GET['obj_1'];
		$action .= (strstr($action, '?') ? '&amp;' : '?').'obj_1='.$_GET['obj_1'];
	}

	if( isset($_GET['obj_2']) ){
		$objs[] = $_GET['obj_2'];
		$action .= (strstr($action, '?') ? '&amp;' : '?').'obj_2='.$_GET['obj_2'];
	}

	if( isset($_GET['type']) && $_GET['type'] ){
		$action .= (strstr($action, '?') ? '&amp;' : '?').'type='.$_GET['type'];
	}

	// Cette action permet de rattacher un document existant à un contenu
	if( isset($_POST['select']) ){
		if( isset($_POST['doc']) && is_array($_POST['doc']) && sizeof($_POST['doc']) ){
			foreach( $_POST['doc'] as $d ){
				doc_objects_add( $d, $_GET['cls'], $objs );
			}
		}
	}

	$wst = wst_websites_get();
	if(ria_mysql_num_rows($wst) > 1 && !isset($_POST['websites'])){
		$_POST['websites'] = array(0);
	}
	else if(!isset($_POST['websites'])){
		$_POST['websites'] = array($config['wst_id']);
	}
	
	if( sizeof($config['i18n_lng_used'])<=1 ){
		if( !isset($_POST['language']) ){
			$_POST['language'] = $config['i18n_lng_used'];
		}
	}
	
	// Cette action permet de créer un nouveau document puis de le rattacher au contenu
	if( isset($_POST['save']) ){
		// Array ( [save] => Enregistrer [name] => [filename] => [type] => 54 [desc] => )
		$tab = 'add';

		if( !isset($_POST['name'], $_POST['desc'], $_POST['type'], $_POST['websites'], $_POST['language']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['name'])=='' || !$_POST['type'] ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		}elseif( !isset($_FILES['file']['tmp_name']) || trim($_FILES['file']['tmp_name'])=='' ){
			$error = _("Veuillez choisir un fichier correspondant à ce document.");
		}else{
			$id = doc_documents_upload($_POST['type'], 'file', $_POST['name'], $_POST['desc'], $_POST['websites'], $_POST['language']);

			if( !$id ){
				$error = _("Une erreur inattendue s'est produite lors de l'ajout du document. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}elseif( !doc_objects_add( $id, $_GET['cls'], $objs) ){
				$error = _("Une erreur inattendue s'est produite lors du rattachement du document. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Document') . ' - ' . _('Médiathèque'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'tabs');
	require_once('admin/skin/header.inc.php');
?>
	<form id="form-doc" class="ffff" action="<?php print preg_replace('/(?|&amp;)type=[0-9]+/', '', $action); ?>" method="post" enctype="multipart/form-data">
		<ul class="tabstrip">		
			<li <?php print $tab=='media' ? 'class="selected"' : ''; ?>>
				<a href="<?php print preg_replace('/(?|&amp;)tab=[a-zA-z]+/', '', $action); ?>&amp;tab=media"><?php echo _("Médiathèque"); ?></a>
			</li>	
			<li <?php print $tab=='add' ? 'class="selected"' : ''; ?>>
				<a href="<?php print preg_replace('/(?|&amp;)tab=[a-zA-Z]+/', '', $action); ?>&amp;tab=add"><?php echo _("Ajouter"); ?></a>
			</li>	
		</ul>

		<div class="tabscontent"><?php
			if( $tab=='media' ){
				$rtype = doc_types_get( 0, false, false );
				$count = $rtype ? ria_mysql_num_rows( $rtype ) : 0;

				print '
					<div id="mediatheque-container">
						<div id="nav" style="width: 145px; overflow-y: auto; margin-right: 4px;">
				';

				if( $rtype && ria_mysql_num_rows($rtype) ){
					print '
							<ul>
								<li>
									<a href="#" class="racine dir all" id="type-all"><strong>' . _("Médiathèques") . '</strong></a>
									<ul>
					';

					while( $type = ria_mysql_fetch_array($rtype) ){
						print '
										<li>
											<a class="dir'.( isset($_GET['type']) && $_GET['type']==$type['id'] ? ' selected' : '' ).'" href="'.preg_replace('/(?|&amp;)type=[0-9]+/', '', $action).'&amp;type='.$type['id'].'">
												'.htmlspecialchars( $type['name'] ).'
											</a>
										</li>
						';
					}

					print '
									</ul>
								</li>
							</ul>
					';
				}

				print '
						</div>
						<div id="mediatheque" style="height: auto; width: 750px;">
				';

					$rdocs = doc_documents_get( 0, isset($_GET['type']) && $_GET['type'] ? $_GET['type'] : 0, null );
					$count = $rdocs ? ria_mysql_num_rows( $rdocs ) : 0;

					// Calcule le nombre de pages
					$limit_for_page = 18;
					$pages = ceil($count / $limit_for_page);
					if( $pages==0 ) $pages = 1;

					// Détermine la page en cours de consultation
					$page = 1;
					if( isset($_GET['page']) && is_numeric($_GET['page']) ){
						if( $_GET['page']>0 && $_GET['page']<=$pages ){
							$page = $_GET['page'];
						}
					}

					// Détermine les limites inférieures et supérieures pour l'affichage des pages
					$pmin = ($page-5)<1 ? 1 : ($page-5);
					$pmax = ($pmin+9)>$pages ? $pages : ($pmin+9);

					print '
						<table id="documents" class="checklist ui-sortable">
							<caption>' . _("Liste des documents") . ' ('.$count.'  document'.($count>1 ? 's' : '' ).')</caption>
							<thead>
								<tr>
									<th id="doc-sel">
										<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
									</th>
									<th id="doc-name">' . _("Désignation") . '</th>
									<th id="doc-file">' . _("Fichier") . '</th>
								</tr>
							</thead>
							<tbody>
					';

					if( !$rdocs || !ria_mysql_num_rows($rdocs) ){
						print '
								<tr>
									<td colspan="2">' . _("Aucun document pour ce type") . '</td>
								</tr>
						';
					}else{
						ria_mysql_data_seek( $rdocs, ($page-1)*$limit_for_page );

						$i = 1;
						while( $doc = ria_mysql_fetch_array($rdocs) ){
							if( $i>$limit_for_page ){
								break;
							}

							print '
								<tr class="ria-row-orderable" id="line-'.$doc['id'].'">
									<td headers="doc-sel" class="pos-center">
										<input type="checkbox" value="'.$doc['id'].'" name="doc[]" id="doc-'.$doc['id'].'" class="checkbox" />
									</td>
									<td headers="doc-name">
										<label for="doc-'.$doc['id'].'">'.htmlspecialchars( $doc['name'] ).'</label>
									</td>
									<td headers="doc-file">
										<a href="/admin/documents/dl.php?doc='.$doc['id'].'">'.htmlspecialchars($doc['filename']).'</a>
									</td>
								</tr>
							';

							$i++;
						}
					}
					print '
							</tbody>';
							if( $pages>1 ){
								print '
										<tfoot>
											<tr id="pagination">
												<td class="page" colspan="3">
													<div style="float: right;">
								';
		
								if( $page>1 ){
									print '<a href="'.preg_replace('/(?|&amp;)page=[0-9]+/', '', $action).( (strstr($action, '?') ? '&amp;' : '?').'page='.($page-1) ).'">&laquo; ' . _("Page précédente") . '</a> | ';
								}
		
								for( $i=$pmin; $i<=$pmax; $i++ ){
									if( $i==$page ){
										print '<b>'.$page.'</b>';
									}else{
										print '<a href="'.preg_replace('/(?|&amp;)page=[0-9]+/', '', $action).( (strstr($action, '?') ? '&amp;' : '?').'page='.($i) ).'">'.$i.'</a>';
									}
		
									if( $i<$pmax ){
										print ' | ';
									}
								}
		
								if( $page<$pages ){
									print ' | <a href="'.preg_replace('/(?|&amp;)page=[0-9]+/', '', $action).( (strstr($action, '?') ? '&amp;' : '?').'page='.($page+1) ).'">' . _("Page suivante") . ' &raquo;</a>';
								}
								print '
												</div>
											</td>
										</tr>
									</tfoot>';
								}
							print '
						</table>
					';

				print '

						</div>
						<div class="clear"></div>
					</div>
					<div class="padt">
						<input type="submit" value="' . _("Sélectionner") . '" class="button" name="select" style="padding: 3px 7px" />
					</div>
				';
			}elseif( $tab=='add' ){
				if( isset($error) ){
					print '<div class="error">'.nl2br( $error ).'</div>';
				}

				print '
						<table>
							<caption>' . _("Propriétés du document") .'</caption>
						<tfoot>
							<tr><td colspan="2">
								<input type="submit" name="save" value="' . _("Enregistrer") . '" onclick="return validForm(document.getElementById(\'form-doc\'))" />
							</td></tr>
						</tfoot>
						<tbody>
							<tr>
								<td><label for="name"><span class="mandatory">*</span>' . _(" Désignation :") . '</label></td>
								<td><input type="text" name="name" id="name" value="'.( isset($_POST['name']) ? $_POST['name'] : '' ).'" maxlength="45" /></td>
							</tr>
							<tr>
								<td><label for="filename"><span class="mandatory">*</span> ' . _("Fichier :") . '</label></td>
								<td><input type="file" id="file" name="file"></td>
							</tr>
							<tr>
								<td><label for="type"><span class="mandatory">*</span> ' . _("Type :") . '</label></td>
								<td>
									<select name="type" id="type">
				';

				$types = doc_types_get();
				while( $t = ria_mysql_fetch_array($types) ){
					$selected = isset($_POST['type']) && $_POST['type']==$t['id'] ? 'selected="selected"' : '';

					print '
										<option value="'.$t['id'].'" '.$selected.'>'.htmlspecialchars( $t['name'] ).'</option>
					';
				}

				print '
									</select>
								</td>
							</tr>
							
							<tr>
								<td><label for="desc">' . _('Description :') . '</label></td>
								<td><textarea name="desc" id="desc" rows="10" cols="50">'.( isset($_POST['desc']) ? $_POST['desc'] : '' ).'</textarea></td>
							</tr>
				';

				$wst = wst_websites_get( 0, false, null, true );
				if(ria_mysql_num_rows($wst) > 1) {
					print '
							<tr>
								<td><label>' . _('Sur les sites :') . '</label></td>
								<td>
									<a href="#" class="check-all">Tous</a> | <a href="#" class="uncheck-all">Aucun</a><br/>
					';
					
					while( $r = ria_mysql_fetch_array($wst) ){
						print '
									<input type="checkbox" class="checkbox" name="websites[]" id="website-'.$r['id'].'" value="'.$r['id'].'" />
									<label for="website-'.$r['id'].'">'.htmlspecialchars( $r['name'] ).'</label><br/>
						';
					}
					
					print '
								</td>
							</tr>
					';
				}
				
				if( is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1 ){
					print '
							<tr>
								<td><label>' . _("Versions :") . '</label></td>
								<td>
									<a href="#" class="check-all">' . _("Cocher tout") . '</a> | <a href="#" class="uncheck-all">' . _("Décocher tout") . '</a><br/>
					';
					
					foreach( $config['i18n_lng_used'] as $lng ){
						print '
									<input type="checkbox" class="checkbox" name="language[]" id="language-'.$lng.'" value="'.$lng.'" />
									<label for="language-'.$lng.'"> '.i18n_languages_get_name($lng).'</label>
									<br />
						';
					}
					
					print '
								</td>
							</tr>
					';
				}

				print '
						</tbody>
						</table>
				';
			}
		?></div>
	</form>
	<script><!--
		var segClsID = <?php print CLS_DOCUMENT; ?>;
		var passe = false
		var inp = '';

		<?php if( isset($_POST['select']) || (isset($_POST['save']) && !isset($error)) ){ ?>
			parent.reloadTabDocuments();
		<?php } ?>
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
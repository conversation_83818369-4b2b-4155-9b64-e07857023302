<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	if( isset($_GET['type']) && $_GET['type'] == 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_TYPE');
	}
	elseif( isset($_GET['type']) && $_GET['type'] ){
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_TYPE');
	}

	if( !isset($_GET['type']) ){
		header('Location: /admin/documents/index.php?type=0');
		exit;
	}

	require_once('documents.inc.php');

	$type_id = isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] ? $_GET['type'] : 0;

	$type = array( 'id'=>0, 'name'=>_('Types de documents') );
	if( $type_id && doc_types_exists($type_id) ){
		$type = ria_mysql_fetch_array( doc_types_get($type_id, true) );
	}

	// Mise à jour du mode de tri des types de documents
	if( isset($_POST['orderbytypes']) ){
		doc_types_order_update( 0, $_POST['order-type'] );
	}

	if( isset($_POST['add']) ){
		header('Location: /admin/documents/types/edit.php?parent='.$type['id']);
		exit;
	}

	// Mise à jour du mode de tri des documents
	if( isset($_POST['orderby']) ){
		doc_documents_order_update( $type['id'], $_POST['order'] );
		header('Location: index.php?type='.$type['id']);
		exit;
	}

	/* Déplacement des types de documents dans le tri personnalisé */
	if( isset($_GET['dw']) ){ // modification de la position via les fleche
		if(isset($_GET['type_doc'])){ // Modification de la position du type de documents
			$res = doc_type_documents_move_down($_GET['dw']);
		}else{ // Modification de la position du document
			doc_documents_move_down( $_GET['dw'] );
		}
	}
	if( isset($_GET['up']) ){ // modification de la position via les fleche
		if(isset($_GET['type_doc'])){ // Modification de la position du type de documents
			doc_type_documents_move_up($_GET['up']);
		}else{ // Modification de la position du document
			doc_documents_move_up( $_GET['up'] );
		}
	}

	// Suppression d'un type de documents
	if( isset($_POST['deltype']) ){
		/* Suppression d'un ou plusieurs types */
		if( isset($_POST['type']) && is_array($_POST['type']) ){
			foreach( $_POST['type'] as $p ){
				doc_types_del($p);
			}
			header('Location: index.php?type='.$type['id']);
			exit;
		}
	}

	/* Suppression */
	if( isset($_POST['del']) ){
		/* Suppression d'un ou plusieurs types */
		if( isset($_POST['doc']) && is_numeric($_POST['doc']) ) $_POST['doc'] = array($_POST['doc']);
		if( isset($_POST['doc']) && is_array($_POST['doc']) ){
			foreach( $_POST['doc'] as $p ){
				doc_documents_del($p);
			}
			header('Location: index.php?type='.$type['id']);
			exit;
		}
	}

	/* Enregistrement des fichiers */
	if( isset($_POST['save']) ){
		foreach($_FILES as $name => $val){
			$na = explode('-', $name);

			$file_lng = isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ? $_GET['lng'] : false;
			if(is_numeric($na[1]) && doc_documents_exists($na[1])){
				doc_documents_update_file( $na[1], 'file-'.$na[1], $file_lng );
			}
		}
	}

	if( isset($_POST['addDoc'], $_GET['type']) ){
		header('Location: /admin/documents/edit.php?type='.$_GET['type'].'&doc=0');
		exit;
	}

	if( isset($_POST['movedoc']) ){
		if( !isset($_POST['doc']) || !is_array($_POST['doc']) || !sizeof($_POST['doc']) ){
			header('Locaiton: /admin/documents/index.php?type='.$_GET['type']);
			exit;
		}

		$url = '/admin/documents/move.php?srctype='.$_GET['type'];
		foreach( $_POST['doc'] as $one_doc ){
			$url .= '&doc[]='.$one_doc;
		}

		header('Location: '.$url);
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php?type=0' );

	$parent = doc_types_get_parents_array( $_GET['type'], 'desc' );
	if( is_array($parent) && sizeof($parent) ){
		foreach( $parent as $one_t ){
			$t = ria_mysql_fetch_array( doc_types_get($one_t) );
			Breadcrumbs::add( $t['name'], '/admin/documents/index.php?type='.$t['id'] );
		}
	}

	Breadcrumbs::add( $type['name'] );

	define('ADMIN_PAGE_TITLE', htmlspecialchars( $type['name'] ));
	require_once('admin/skin/header.inc.php');
?>

<h2><?php
	print htmlspecialchars( $type['name'] );
	if( $type['id']>0 && gu_user_is_authorized('_RGH_ADMIN_DOCS_TYPE_EDIT') ){
		print '
			<a class="edit-cat" href="/admin/documents/types/edit.php?type='.$type['id'].'">'  ._("Modifier ce type de document") . '</a>
		';
	}
?></h2>

<?php
	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	$documents = false;
	if( $type['id']>0 ){
		$documents = doc_documents_get( 0, $type['id'], null, '', false );
	}

	$count = $documents ? ria_mysql_num_rows($documents) : 0;
	print '<div class="stats-menu">';
	print view_translate_menu( 'index.php?type='.$_GET['type'], $lng );
	print '</div>';
?>
<form action="index.php?type=<?php print $type['id']; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data">

<?php
	$rtype = doc_types_get( 0, true, true, false, false, false, $type['id'] );
	if( $rtype ){
		$ordered = doc_types_order_get();

		if( isset($type['id']) ){
			if( $type['id']>0 || ria_mysql_num_rows($rtype)>0 ){
				if( $_GET['type']>0 ){
					$title_type = ria_number_format(ria_mysql_num_rows($rtype)).' sous-type'.(ria_mysql_num_rows($rtype) > 1 ? 's' : '').' de documents';
				}else{
					$title_type = ria_number_format(ria_mysql_num_rows($rtype)).' type'.(ria_mysql_num_rows($rtype) > 1 ? 's' : '').' de documents';
				}
			}else{
				if( $_GET['type']>0 ){
					$title_type = _('Aucun sous-type de documents');
				}else{
					$title_type = _('Aucun type de documents');
				}
			}
		}

	    print '
	    	<table id="type-docs" class="checklist">
	    		<caption>'.$title_type.'</caption>
	    		<thead>
	    			<tr>
	    ';

		if( $ordered ){
			print '
						<th id="type-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
						<th id="type-name">' . _("Désignation") . '</th>
						<th id="type-desc">' . _("Description") . '</th>
						<th id="type-documents" class="align-right">' . _("Documents") . '</th>
						<th id="type-pos-2">' . _("Déplacer") . '</th>
			';
		} else {
			print '
						<th id="type-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
						<th id="type-name"><a href="index.php?sort=name&amp;dir='.( isset($_GET['sort']) && $_GET['sort']=='name' ? $dir : 'asc' ).'">' . _("Désignation") . '</a></th>
						<th id="type-desc"><a href="index.php?sort=desc&amp;dir='.( isset($_GET['sort']) && $_GET['sort']=='desc' ? $dir : 'asc' ).'">' . _("Description") . '</a></th>
						<th id="type-documents" class="align-right"><a href="index.php?sort=docs&amp;dir='.( isset($_GET['sort']) && $_GET['sort']=='docs' ? $dir : 'asc' ).'">' . _("Documents") . '</a></th>
			';
		}

			print '
	    			</tr>
	    		</thead>
	    		<tbody>
	    	';

			if( ria_mysql_num_rows($rtype)==0 ){
				print '<tr><td colspan="'.( $ordered ? 5 : 4 ).'">' . _("Aucun type de document") . '</td></tr>';
			}else{
				while( $r = ria_mysql_fetch_array($rtype) ){
					if( $lng!=$config['i18n_lng'] ){
						$tsk = fld_translates_get( CLS_TYPE_DOCUMENT, $r['id'], $lng, $r, array(_FLD_DOC_TYPE_NAME=>'name', _FLD_DOC_TYPE_DESC=>'desc' ), true );
						$r['name'] = trim($tsk['name'])!='' ? $tsk['name'] : '[Non traduit] '.$r['name'];
						$r['desc'] = $tsk['desc'];
					}

					if( strlen($r['desc'])>255 ){
						$r['desc'] = substr($r['desc'],0,252).'...';
					}

					print '
						<tr id="line-' . $r['id'] . '" class="ria-row-orderable">
							<td headers="type-sel"><input type="checkbox" class="checkbox" name="type[]" value="'.$r['id'].'" /></td>
							<td headers="type-name">
								<a href="/admin/documents/index.php?type='.$r['id'].'" title="' . _("Afficher la fiche de ce type de document") . '">'.htmlspecialchars($r['name']).'</a>
							</td>
							<td headers="type-desc">
								'.htmlspecialchars($r['desc']).'
							</td>
							<td headers="type-docs" class="align-right">
								'.ria_number_format(doc_types_get_docs_count($r['id'])).'
							</td>
					';

					if( $ordered ){
						print '
							<td headers="type-pos-2" class="align-center ria-cell-move">
								<div class="ria-row-catchable" title="'._('Déplacer').'"></div>
							</td>
						';
					}

					print '
						</tr>
					';
				}
			}

	    	print '
	    		</tbody>
	    		<tfoot>
					<tr>
						<td colspan="'.( $ordered ? 3 : 2 ).'" class="align-left">
			';
			if( ria_mysql_num_rows($rtype) && gu_user_is_authorized('_RGH_ADMIN_DOCS_TYPE_DEL') ){
				print '		<input type="submit" name="deltype" value="' . _("Supprimer") . '" onclick="return confirmDelTypesList()" />';
			}
			print '
						</td>
						<td colspan="2" class="align-right">';
			if( gu_user_is_authorized('_RGH_ADMIN_DOCS_TYPE_ADD') ){
				print '			<input type="submit" name="add" value="' . _("Ajouter") . '" />';
			}
			print		'</td>
					</tr>
			';
			if( ria_mysql_num_rows($rtype)>1 ){
				print '
					<tr>
						<td colspan="'.( $ordered ? 5 : 4 ).'"  class="align-left tfoot-grey">
							<label>'._('Trier ces types par ordre :').'</label>
							<input type="radio" class="radio" name="order-type" id="order-type-0" value="0" '.( !$ordered ? 'checked="checked"' : '' ).' /> <label for="order-type-0">' . _("Alphabétique") . '</label>
							<input type="radio" class="radio" name="order-type" id="order-type-1" value="1" '.( $ordered ? 'checked="checked"' : '' ).' /> <label for="order-type-1">' . _("Personnalisé") . '</label>
							<input type="submit" name="orderbytypes" value="' . _("Appliquer") . '" />
						</td>
					</tr>
				';
			}
			print '
	    		</tfoot>
	    	</table>
	    ';
	}

	if( $type_id>0 ){
	$ordered = doc_documents_order_get( $type['id'] );
?>

<table id="documents" class="checklist">
	<caption><?php print $count ? ria_number_format($count)._(' documents') : _('Aucun document'); ?></caption>
	<thead>
		<tr>
			<th id="doc-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="doc-name"><?php echo _("Désignation"); ?></th>
			<th id="doc-file"><?php echo _("Fichier"); ?></th>
			<th id="doc-remp"><?php echo _("Remplacement"); ?></th>
			<?php if($ordered) { ?>
			<th id="type-pos"><?php echo _("Déplacer"); ?></th>
			<?php } ?>
		</tr>
	</thead>
	<?php

		// Calcule le nombre de pages
		$pages = ceil($count / 25);
		if( $pages==0 ) $pages = 1;

		// Détermine la page en cours de consultation
		$page = 1;
		if( isset($_GET['page']) && is_numeric($_GET['page']) ){
			if( $_GET['page']>0 && $_GET['page']<=$pages )
				$page = $_GET['page'];
		}

		// Détermine les limites inférieures et supérieures pour l'affichage des pages
		$pmin = $page-5;
		if( $pmin<1 )
			$pmin = 1;
		$pmax = $pmin+9;
		if( $pmax>$pages )
			$pmax = $pages;

	?>

	<tbody>
		<?php
			if( $count==0 ){
				print '<tr><td colspan="'.( $ordered ? 5 : 4 ).'">' . _("Aucun document") . '</td></tr>';
			}else{
				ria_mysql_data_seek( $documents, ($page-1)*25 );
				$lcount = 0;
				while( ($r = ria_mysql_fetch_array($documents)) && $lcount<25 ){
					if( $lng!=$config['i18n_lng'] ){
						$tsk = fld_translates_get( CLS_DOCUMENT, $r['id'], $lng, $r, array(_FLD_DOC_NAME=>'name', _FLD_DOC_FILENAME=>'filename' ), true );
						$r['name'] = trim($tsk['name'])!='' ? $tsk['name'] : '[Non traduit] '.$r['name'];
						$r['filename'] = trim($tsk['filename'])!='' ? $tsk['filename'] : '[Non traduit] '.$r['filename'];
					}

					print '<tr  id="line-' . $r['id'] . '" class="ria-row-orderable">';

					print '		<td headers="doc-sel"><input type="checkbox" class="checkbox" name="doc[]" value="'.$r['id'].'" /></td>';
					if( gu_user_is_authorized('_RGH_ADMIN_DOCS_EDIT') ){
						print '	<td headers="doc-name"><a href="edit.php?doc='.$r['id'].'&amp;type='.$r['type_id'].'&amp;page='.$page.'" title="' . _("Afficher la fiche de ce document") . '">'.htmlspecialchars($r['name']).'</a></td>';
					}else{
						print '	<td headers="doc-name">'.htmlspecialchars($r['name']).'</td>';
					}
					print '		<td headers="doc-file"><a href="dl.php?doc='.$r['id'].'">'.htmlspecialchars($r['filename']).'</a></td>';
					print '		<td headers="doc-remp">';
					if( gu_user_is_authorized('_RGH_ADMIN_DOCS_EDIT') ){
						print		'<input class="file" type="file" id="file-'.$r['id'].'" name="file-'.$r['id'].'"/><a onclick="resetFileInput(\'file-'.$r['id'].'\')"><img class="noborder" src="/admin/images/del.svg" alt="Annuler" title="' . _("Annuler") . '"/></a></td>';
					}
					if( $ordered ){
						print '
							<td headers="type-pos" class="align-center ria-cell-move">
								<div class="ria-row-catchable" title="'._('Déplacer').'"></div>
							</td>
						';
					}

					print '</tr>';

					$lcount++;
				}
			}
		?>
	</tbody>
	<tfoot>
		<?php if( $pages>1 ){ ?>
		<tr id="pagination">
			<td colspan="2" class="page align-left">Page <?php print $page ?>/<?php print $pages; ?></td>
			<td colspan="<?php print $ordered ? 3 : 2; ?>" class="pages">
				<?php
					if( $pages>1 ){
						if( $page>1 )
							print '<a onclick="return docSwitchTablePage( '.($page-1).', '.$pages.', '.$type['id'].' )" href="index.php?page='.($page-1).'&amp;type='.$type['id'].'">&laquo; ' . _("Page précédente") . '</a> | ';
						for( $i=$pmin; $i<=$pmax; $i++ ){
							if( $i==$page )
								print '<b>'.$page.'</b>';
							else
								print '<a onclick="return docSwitchTablePage( '.$i.', '.$pages.', '.$type['id'].' )" href="index.php?page='.$i.'&amp;type='.$type['id'].'">'.$i.'</a>';
							if( $i<$pmax )
								print ' | ';
						}
						if( $page<$pages )
							print ' | <a onclick="return docSwitchTablePage( '.($page+1).', '.$pages.', '.$type['id'].' )" href="index.php?page='.($page+1).'&amp;type='.$type['id'].'">' . _("Page suivante") . ' &raquo;</a>';
					}
				?>
			</td>
		</tr>
		<?php } ?>
		<tr><td colspan="2" class="align-left">
			<?php if( $count>0 ){
				if( gu_user_is_authorized('_RGH_ADMIN_DOCS_DEL') ){ ?>
			<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return confirmDelList()" />
			<?php } ?>
			<input type="submit" name="movedoc" value="<?php echo _("Déplacer"); ?>" />
			<?php } ?>
		</td><td colspan="<?php print $ordered ? 3 : 2; ?>" class="align-right">
			<?php if( $count>0 ){ ?>
			<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>"/>
			<?php }
			if( gu_user_is_authorized('_RGH_ADMIN_DOCS_ADD') ){ ?>
			<input type="submit" value="<?php echo _("Ajouter"); ?>" name="addDoc" id="addDoc" />
			<?php } ?>
		</td></tr>
		<?php if( $count>1 ){ ?>
		<tr class="tfoot-grey">
			<td colspan="<?php print $ordered ? 5 : 4; ?>">
				<label><?php echo _("Trier ces documents par ordre :"); ?></label>
				<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php echo _("Alphabétique"); ?></label>
				<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php echo _("Personnalisé"); ?></label>
				<input type="submit" name="orderby" value="<?php echo _("Appliquer"); ?>" />
			</td>
		</tr>
		<?php } ?>
	</tfoot>
</table>
<?php } ?>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once('medias.inc.php');
	
	if( !isset($_GET['hst']) || !doc_hosts_exists($_GET['hst']) ){
		header('Location: /admin/documents/medias/hosts.php');
		exit;
	}

	$host = ria_mysql_fetch_assoc( doc_hosts_get($_GET['hst']) );
	$chl_id = isset($_GET['chl']) && is_numeric($_GET['chl']) && $_GET['chl'] ? $_GET['chl'] : 0;
	
	$title = 'Nouvelle chaine';
	$channel = array(
		'id' => 0, 'name' => '', 'desc' => '', 'import_id' => '', 'url' => '', 'date_publish' => ''
	);

	if( doc_channels_exists($chl_id, $host['id']) ){
		$channel = ria_mysql_fetch_assoc( doc_channels_get(0, $chl_id) );
		$title = 'Chaine '. $channel['name'];
	}elseif( $chl_id>0 ){
		$_POST['cancel'] = true;
	}

	if( isset($_POST['save']) ){
		if( !isset($_POST['name'], $_POST['desc'], $_POST['import_id'], $_POST['url'], $_POST['publish']) ){
			$error = "Une ou plusieurs informations sont manquantes";
		}elseif( trim($_POST['name'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes. \nMerci de renseigner tous les champs notés avec un <span class=\"mandatory\">*</span>");
		}else{
			if( $chl_id>0 ){
				if( !doc_channels_update($chl_id, $_POST['name'], $_POST['desc'], $_POST['import_id'], $_POST['url'], ($_POST['publish'] ? true : false)) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la chaine. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}else{
					$_SESSION['save-channel'] = true;
				}
			}else{
				$chl_id = doc_channels_add( $host['id'], $_POST['name'], $_POST['desc'], $_POST['url'], $_POST['import_id'], ($_POST['publish'] ? true : false) );
				
				if( !$chl_id ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout de la chaine. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['save-channel'] = true;
			header('Location: /admin/documents/medias/channels/edit.php?hst='.$host['id'].'&chl='.$chl_id);
			exit;
		}
	}

	if( isset($_POST['delete']) ){
		if( !doc_channels_del($chl_id) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la chaine. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			header('Location: /admin/documents/medias/channels/index.php?hst='.$host['id']);
			exit;
		}
	}

	if( isset($_POST['cancel']) ){
		header('Location: /admin/documents/medias/channels/index.php?hst='.$host['id']);
		exit;
	}

	if( sizeof($_POST) ){
		$channel = array(
			'id' 			=> isset($_POST['id']) ? $_POST['id'] : 0,
			'name' 			=> isset($_POST['name']) ? $_POST['name'] : '',
			'desc' 			=> isset($_POST['desc']) ? $_POST['desc'] : '',
			'import_id' 	=> isset($_POST['import_id']) ? $_POST['import_id'] : '',
			'url' 			=> isset($_POST['url']) ? $_POST['url'] : '',
			'date_publish' 	=> isset($_POST['publish']) && $_POST['publish'] ? date('Y-m-d H:i:s') : false,
			'chl_id' 		=> isset($_POST['chl_id']) ? $_POST['chl_id'] : $chl_id
		);
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Hébergeurs externes'), '/admin/documents/medias/hosts.php' )
		->push( $host['name'], '/admin/documents/medias/index.php?hst='.$host['id'] )
		->push( $title );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', htmlspecialchars( $host['name'] ).' - ' . _('Hébergeurs'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $title ).'</h2>
	';

	if( isset($error) ){
		print '
			<div class="error">'.nl2br($error).'</div>
		';
	}elseif( isset($_SESSION['save-channel']) ){
		unset( $_SESSION['save-channel'] );
		print '
			<div class="success">' . _("Les informations ont bien été mises à jour.") . '</div>
		';
	}

	print '
		<form action="/admin/documents/medias/channels/edit.php?hst='.$host['id'].'&amp;chl='.$channel['id'].'" method="post">
			<table>
				<caption>'.htmlspecialchars( $title ).'</caption>
				<col width="150" /><col width="350" />
				<thead>
					<tr>
						<th colspan="2">Général</th>

					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" value="' . _("Enregistrer") . '" name="save" />
							'.( $chl_id>0 ? '<input type="submit" value="' . _("Supprimer") . '" name="delete" onclick="return confirmDeleteChannel();" />' : '' ).'
							<input type="submit" value="' . _("Annuler") . '" name="cancel" />
						</td>
					</tr>
				</tfoot>
				<tbody>
					<tr>
						<td>
							<span class="mandatory">*</span>
							<label for="name">' . _('Désignation :') . '</label>
						</td>
						<td>
							<input type="text" name="name" value="'.$channel['name'].'" />
						</td>
					</tr>
					<tr>
						<td><label for="desc">' . _('Description :') . '</label></td>
						<td>
							<textarea class="tinymce" name="desc" id="desc" cols="40" rows="5">'.htmlspecialchars( $channel['desc'] ).'</textarea>
						</td>
					</tr>
					<tr>
						<td><label for="import_id">' . _("Identifiant d'import :") . '</label></td>
						<td>
							<input type="text" name="import_id" value="'.$channel['import_id'].'" />
						</td>
					</tr>
					<tr>
						<td><label for="url">' . _("URL :") . '</label></td>
						<td>
							<input type="text" name="url" value="'.$channel['url'].'" />
						</td>
					</tr>
					<tr>
						<td><label for="name">' . _("Publiée :") . '</label></td>
						<td>
							<input type="radio" '.( trim($channel['date_publish'])!='' ? 'checked="checked"' : '' ).' value="1" id="publish-true" name="publish" class="publish-cat" /><label for="publish-true">Oui</label>
							<input type="radio" '.( trim($channel['date_publish'])=='' ? 'checked="checked"' : '' ).' value="0" id="publish-false" name="publish" class="publish-cat" /><label for="publish-false">Non</label>
						</td>
					</tr>

				</tbody>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');

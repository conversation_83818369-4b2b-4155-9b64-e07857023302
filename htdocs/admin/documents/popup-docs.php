<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	$type = 0;
	$current_type = false;
	$is_ajax = false; 
	$limit_docs = 15;
	
	if( isset( $_GET['type'] ) && is_numeric( $_GET['type'] ) ){
		$type = $_GET['type'];
	}


	if( $type ){
		$rtype = doc_types_get( $type );
		if( $rtype && ria_mysql_num_rows($rtype) ) {
			$current_type = ria_mysql_fetch_array($rtype);
		}
	}

	//si requete ajax on ne va pas plus loin
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}
	
	if( !$is_ajax ) { 
		define('ADMIN_PAGE_TITLE', _('Sélection de documents'));
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
		require_once('admin/skin/header.inc.php');
	}
 ?>
	<form action="/admin/orders/create/ajax/ncmd-customers-edit.php" method="get"> <?php 

		// affiche les types de documents
		print '<table class="checklist" cellspacing="0" cellpadding="0">
				<col width="*" />
				<col width="30" />';
				

		print '	<caption>' . _("Type de documents") . '</caption>
				<thead>
					<tr>
						<th>' . _("Nom") . '</th>
						<th>' . _("Type") . '</th>
						
					</tr>
				</thead>
				<tbody>';


		$rtype = doc_types_get( 0, false, false );
		if( $rtype && ria_mysql_num_rows($rtype) ){

			while( $r = ria_mysql_fetch_array($rtype) ){
				print '	<tr>
							<td><a name="selecttype" data-id="'.$r['id'].'">'.$r['name'].'</a></td>
							<td>'.$r['docs'].'</td>
						</tr>';
			}
		}
		else print '<tr><td colspan="2">.</td></tr>';
		print '</tbody></table><br/>';

		// affiche les Documents
		if( $type ){
			$rdoc = doc_documents_get( 0, $type, false, '', false );
			
			$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1;
			$pages = ceil( ria_mysql_num_rows($rdoc) / $limit_docs );
			
			if( $rdoc && ria_mysql_num_rows($rdoc) ){
				ria_mysql_data_seek( $rdoc, ($page-1)*$limit_docs );
				
				print '<table class="checklist" cellspacing="0" cellpadding="0">
						<col width="*" />
						<col width="100" />
						<col width="100" />';
				print '	<caption>' . _("Documents") . '</caption>';
				print '	<thead>
							<tr>
								<th>' . _("Nom") . '</th>
								<th>' . _("Taille") . '</th>
								<th></th>
							</tr>
						</thead>
						<tfoot>
								<tr id="pagination">
									<td class="page">' . _('Page ').$page.'/'.$pages.'</td>
									<td colspan="3" class="pages">';
									for( $i= ( $page-5 < 1 ? 1 : $page-5) ; $i<=( $page+5 > $pages ? $pages : $page+5); $i++ ){
										if( $i==$page )
											print '<b>'.$page.'</b>';
										else
											print '<a name="selecttype" data-id="'.$current_type['id'].'" data-page="'.$i.'">'.$i.'</a>'; 
										if( $i<$pages )
											print ' | ';
									}
				print '			</td>
							</tr>
						</tfoot>
						<tbody>';
					$count = 0;
					while( $doc = ria_mysql_fetch_array($rdoc) ){
						if( $count >= $limit_docs ) break;
						print '	<tr>
									<td>
										<input class="radio" type="radio" name="docs" id="docs-'.$doc['id'].'" value="'.$doc['id'].'"  data-name="'.htmlspecialchars($doc['name']).'" />
										<label for="docs-'.$doc['id'].'">'.$doc['name'].'</label>
									</td>
									<td align="right">'.number_format($doc['size']/1000,2,',',' ').' Ko</td>
									<td align="center"></td>
								</tr>';
						$count++;
					}
				print '	<tbody>
					</table>';
			}
		}
		print '<div class="pop-form-search">';
		if( isset($rdoc) && $rdoc && ria_mysql_num_rows($rdoc) ){
			print ' <input class="btn-action" type="button" name="selectdoc" id="selectdoc" value="' . _("Sélectionner") . '" />';
		}
		print '
			<input class="btn-action cancel" onclick="parent.hidePopup();" type="button" name="cancel" id="cancel" value="' . _("Annuler") . '" />
		</div>';
	?></form>
<?php 
	if( !$is_ajax ){
		require_once('admin/skin/footer.inc.php');
	}
?>
<?php
	define('ADMIN_PAGE_TITLE', _('Statistiques') .' - Yuto');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_STATS');
	
	require_once('admin/skin/header.inc.php');
?>

<h2><?php print _('Statistiques').' - Yuto'; ?></h2>
<dl>
	<dt><a href="subscriptions.php"><?php print _('Utilisation')?></a></dt>
	<dd><?php print _('Cet écran permet de suivre l\'utilisation des tablettes Yuto')?></dd>
</dl>

<?php if( $config["devices_is_yuto"]===true ){ ?>
	<?php if( !tnt_tenants_is_yuto_essentiel()){ ?>
		<dl>
			<dt><a href="pipeline.php"><?php print _('Chiffre d\'affaires')?></a></dt>
			<dd><?php print _('Cet écran permet de suivre l\'évolution du chiffre d\'affaires')?></dd>
		</dl>
	<?php } ?>
	<dl>
		<dt><a href="report-time.php"><?php print _('Rapports de visite')?></a></dt>
		<dd><?php print _('Cet écran permet de consulter les statistiques sur les rapports de visite')?></dd>
	</dl>
	<dl>
		<dt><a href="spent-time.php"><?php print _('Temps de visite')?></a></dt>
		<dd><?php print _('Cet écran permet de consulter les temps de visite')?></dd>
	</dl>
<?php } ?>
<?php
	//print view_index_menu('_MENU_FDV_STATS');

	require_once('admin/skin/footer.inc.php');
?>

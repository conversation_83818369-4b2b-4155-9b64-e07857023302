<?php

	/**	\file spent-time.php
	 *	Cette page affichage un rapport sur les temps de visite réalisés par les commerciaux chez les clients, croisés avec le chiffre d'affaires généré.
	 *	Un rapport équivalent existe dans Yuto.
	 */

	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	if( !gu_user_is_authorized('_RGH_ADMIN_FDV_STATS_SPENT_TIME') && !gu_user_is_authorized('_RGH_ADMIN_STATS_YUTO') ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}
	
	define('ADMIN_PAGE_TITLE', _('Statistiques') . ' - ' . _('Temps de visite'));
	require_once('admin/skin/header.inc.php');

	if (isset($_GET["seller_id"])) {
		$_SESSION["ord_seller_id"] = $_GET["seller_id"];
	}
?>
<h2><?php print _('Temps de visite')?></h2>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<?php print view_sellers_selector(); ?>
		<div class="clear"></div>
	</div>

	<?php view_import_highcharts(); ?>	
	<?php 
		require_once( 'admin/highcharts/graph-dev-spent-time.php' );
	?>
	<script><!--
		var urlHighcharts = '/admin/fdv/stats/spent-time.php';
		var riadatepicker_upd_url = '';
		<?php view_date_initialized( 0, '', false, array() ); ?>

		$(document).ready(function(){

			var urlHighcharts = '/admin/fdv/stats/pipeline.php';
		
			<?php 
			if (isset($_GET["seller_id"]) && is_numeric($_GET["seller_id"])) {
				print 'seller_id = "&seller_id='.$_GET["seller_id"].'";';
			}else{
				print 'seller_id = "";';
			}
			?>

			$('#selectseller .selectorview,#selectseller .selector').mouseup(function(){
				if($('#selectseller .selector').css('display')=='none'){
					$('#selectseller .selector').show();
				}else{
					$('#selectseller .selector').hide();
				}
			});
			
		}).delegate('#selectseller a', 'click', function(){
				seller_id = 'seller_id='+$(this).attr('name').replace('seller-','');
				window.location = 'spent-time.php?'+seller_id;
			}
		);
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>

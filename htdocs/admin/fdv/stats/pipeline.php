<?php

	/**	\file pipeline.php
	 * 
	 *	Cette page affiche le chiffre d'affaires réalisé sur la plateforme sous forme de graphique et de tableaux.
	 *
	 *	Plusieurs filtres sont disponibles :
	 *	- Période
	 *	- Représentant
	 *	- Type de pièce (commandes, marge, facture)
	 */

	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_STATS_PIPELINE');

	if (isset($_GET["seller_id"])) {
		$_SESSION["ord_seller_id"] = $_GET["seller_id"];
	}

	if (isset($_GET["only_seller_orders"])) {
		$_SESSION["only_seller_orders"] = $_GET["only_seller_orders"] == 'true' ? true : false;
	}

	$ca = 'ord_ca_all';
	if (isset($_GET["CA"])) {
		if( !in_array($_GET["CA"], array('ord_ca_direct', 'ord_ca_indirect', 'ord_ca', 'ord_ca_all','inv_ca'))){
			$_GET["CA"] = $ca;
		}
		$ca = $_SESSION["CA"] = $_GET["CA"];
	}

	// Récupération des comptes administrateurs et commercials
	$sellers = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER) );

	// Charge un tableau des commerciaux pour le pipeline en y incluant les administrateurs (sauf les super-admin)
	$commercial = array();
	if( $sellers ){
		while( $seller = ria_mysql_fetch_assoc($sellers) ){
			// Exclusion des super-administrateurs
			if( !is_numeric($seller['tenant']) || $seller['tenant'] <= 0 ){
				continue;
			}

			$commercial[] = $seller;
		}
	}
	
	$display_direct_marge = isset($config['admin_pipeline_marge_direct']) && $config['admin_pipeline_marge_direct'];

	if( $display_direct_marge){
		$filters = array("seller_id" => 0, "only_seller_orders" => true, "direct" => null);
		if (isset($_SESSION["ord_seller_id"])) {
			$filters["seller_id"] = $_SESSION["ord_seller_id"];
		}
		if (isset($_SESSION["only_seller_orders"])) {
			$filters["only_seller_orders"] = $_SESSION["only_seller_orders"];
		}

		$CA = 'ord_ca_all';
		if (isset($_SESSION["CA"])) {
			$CA = $_SESSION["CA"];
		}else{
			$_SESSION["CA"] = $CA;
		}

		$date1 = isset($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : date('Y-m-d');
		$date2 = isset($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : date('Y-m-d');
		switch($CA) {
			case 'inv_ca':
				$filters['direct'] = null;
				$filters['col'] = 'completed';
				$invoice = stats_graphs_get_datas( 'invoice', dateparse($date1), dateparse($date2), $filters, -1, 0, false);
				$stats = array($invoice);
				break;
			case 'ord_ca_direct':
				$filters['direct'] = true;
				$filters['col'] = 'marge';
				$direct = stats_graphs_get_datas( 'invoice', dateparse($date1), dateparse($date2), $filters, -1, 0, false);
				$stats = array($direct);
				break;
			case 'ord_ca_indirect':
				$filters['direct'] = false;
				$filters['col'] = 'marge';
				$indirect = stats_graphs_get_datas( 'invoice', dateparse($date1), dateparse($date2), $filters, -1, 0, false);
				$stats = array($indirect);
				break;
			case 'ord_ca_all':
			default:
				$filters1 = $filters;
				$filters1['direct'] = true;
				$filters1['col'] = 'marge';
				$direct = stats_graphs_get_datas( 'invoice', dateparse($date1), dateparse($date2), $filters1, -1, 0, false);

				$filters2 = $filters;
				$filters2['direct'] = false;
				$filters2['col'] = 'marge';
				$indirect = stats_graphs_get_datas( 'invoice', dateparse($date1), dateparse($date2), $filters2, -1, 0, false);
				$stats = array($direct, $indirect);
				break;
		}

		$data = array();
		foreach ($commercial as $value){
			$r_direct = stats_invoices_ca(
				'completed',
				dateparse($date1),
				dateparse($date2),
				'',
				false,
				$value['seller_id'],
				true,
				0,
				0,
				true,
				false
			);
			$marge = array(
				'direct' => 0,
				'indirect' => 0,
			);
			$ca = array(
				'direct' => 0,
				'indirect' => 0,
			);
			if ($r_direct && ria_mysql_num_rows($r_direct)) {
				while( $direct = ria_mysql_fetch_assoc($r_direct)) {
					$marge['direct'] += $direct['marge'];
					$ca['direct'] += $direct['completed'];
				}
			}
			$direct = ria_mysql_fetch_assoc($r_direct);
			$debug = false;

			$r_indirect = stats_invoices_ca(
				'completed',
				dateparse($date1),
				dateparse($date2),
				'',
				false,
				$value['seller_id'],
				true,
				0,
				0,
				false,
				false
			);

			if ($r_indirect && ria_mysql_num_rows($r_indirect)) {
				while( $indirect = ria_mysql_fetch_assoc($r_indirect)) {
					$marge['indirect'] += $indirect['marge'];
					$ca['indirect'] += $indirect['completed'];
				}
			}

			$data[$value['id']] = array(
				'marge' => $marge,
				'ca' => $ca
			);
		}

	}

	if (isset($_POST['export'])) {
		$rows = array('Commercial;CA direct;Ca indirect;CA total;Marge brute directe;Marge brute indirecte;Marge brute totale');
		foreach ($commercial as $value){
			$name = '';
			if( $value['type_id']==1 ){
				$name = $value['adr_lastname'].', '.$value['adr_firstname'];
			}elseif( $value['type_id']==2 ){
				$name = $value['society'];
			}else{
				if( $value['society']!='' ) {
					$name = $value['society'].', ';
				}
				$name .= $value['adr_lastname'].', '.$value['adr_firstname'];
			}
			$rows[] = $name .';'
			.($data[$value['id']]['ca']['direct']).';'
			.($data[$value['id']]['ca']['indirect']).';'
			.(($data[$value['id']]['ca']['direct'])+($data[$value['id']]['ca']['indirect'])).';'
			.($data[$value['id']]['marge']['direct']).';'
			.($data[$value['id']]['marge']['indirect']).';'
			.(($data[$value['id']]['marge']['direct'])+($data[$value['id']]['marge']['indirect'])).'';
		}

		$path = tempnam(sys_get_temp_dir(), 'CA-');

		file_put_contents($path, implode("\n", $rows));

		header('Content-Type: application/x-force-download; charset=utf-8');
		header('Content-Disposition: attachment; filename="CA-yuto.csv"');
		header('Content-Length: '.filesize($path));

		ob_clean();
		flush();
		readfile($path);

		exit;
	}
	define('ADMIN_PAGE_TITLE', _('Statistiques') . ' - ' . _('Pipeline'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Chiffre d\'affaires'); ?></h2>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<?php print view_sellers_selector( false, true ); ?>
		<?php if( $display_direct_marge) {print view_ca_selector($CA);} ?>
		<div class="clear"></div>
	
		<div class="div-padding-15">

		<?php 
		if (isset($_SESSION['only_seller_orders']) && $_SESSION['only_seller_orders'] == "true") {
			print '<input type="checkbox" name="only_seller_orders" checked="checked" id="only_seller_orders">';
		}else{
			print '<input type="checkbox" name="only_seller_orders" id="only_seller_orders">';
		}

		 ?>
			<label for="only_seller_orders"><?php print _('Afficher uniquement les commandes des commerciaux'); ?></label>
		</div>
	</div>

	<?php view_import_highcharts(); ?>	
	<?php 
		if( $display_direct_marge){
			require_once( 'admin/highcharts/graph-dev-pipeline-direct.php' );
		}else{
			require_once( 'admin/highcharts/graph-dev-pipeline.php' );
		}
	?>
	<?php 
	if( $display_direct_marge ){
	// Gestion du taux de commission
		$taux = isset($config['taux_commission']) ? $config['taux_commission'] : 0;
	?>
	<form method="post">
		<table id="tb-total-ca">
			<caption><?php print _('Chiffre d\'affaires par commercial'); ?></caption>
			<thead>
				<tr>
					<th><?php print _('Commercial'); ?></th>
					<th class="align-right"><?php print _('Chiffre d’affaires direct'); ?></th>
					<th class="align-right"><?php print _('Chiffre d’affaires indirect'); ?></th>
					<th class="align-right"><?php print _('Chiffre d’affaires total'); ?></th>
					<th class="align-right"><?php print _('Marge brute directe'); ?></th>
					<th class="align-right"><?php print _('Marge brute indirecte'); ?></th>
					<th class="align-right"><?php print _('Marge brute totale'); ?></th>
					<th class="align-right"><?php print _('Commissions'); ?></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td class="align-right" colspan="8">
						<input type="submit" name="export" value="<?php print _('Exporter'); ?>" />
					</td>
				</tr>
			</tfoot>
			<tbody>
				<?php if( !sizeof($commercial) ){ ?>
					<tr><td colspan="8"><?php print _('Aucune donnée disponible'); ?></td></tr>
				<?php }else{ ?>
					<?php foreach( $commercial as $value ){ ?>
						<?php
							$name = '';
							if( $value['type_id']==1 ){
								$name = $value['adr_lastname'].', '.$value['adr_firstname'];
							}elseif( $value['type_id']==2 ){
								$name = $value['society'];
							}else{
								if( $value['society']!='' ) {
									$name = $value['society'].', ';
								}
								$name .= $value['adr_lastname'].', '.$value['adr_firstname'];
							}
							$total = ($data[$value['id']]['ca']['direct'])+($data[$value['id']]['ca']['indirect']);
						?>
						<tr>
							<td><?php echo '<a href="/admin/customers/edit.php?usr='.$value['id'].'">'.htmlspecialchars($name).'</a>'; ?></td>
							<td class="align-right"><?php echo number_format(($data[$value['id']]['ca']['direct']), 2, ', ', ' ') ?> €</td>
							<td class="align-right"><?php echo number_format(($data[$value['id']]['ca']['indirect']), 2, ', ', ' ') ?> €</td>
							<td class="align-right"><?php echo number_format($total,2,', ', ' ')?> €</td>
							<td class="align-right"><?php echo number_format(($data[$value['id']]['marge']['direct']), 2, ', ', ' ') ?> €</td>
							<td class="align-right"><?php echo number_format(($data[$value['id']]['marge']['indirect']), 2, ', ', ' ') ?> €</td>
							<td class="align-right"><?php echo number_format(($data[$value['id']]['marge']['direct'])+($data[$value['id']]['marge']['indirect']), 2, ', ', ' ') ?> €</td>
							<td class="align-right"><?php echo ria_number_french( ($data[$value['id']]['marge']['direct'] + $data[$value['id']]['marge']['indirect']) * $taux / 100); ?> €</td>
						</tr>
					<?php } ?>
				<?php } ?>
			</tbody>
		</table>
	</form>
	<?php }?>
	<script><!--
		var riadatepicker_upd_url = '';
		<?php view_date_initialized( 0, '', false, array() ); ?>
		
		$(document).ready(function(){

			var urlHighcharts = '/admin/fdv/stats/pipeline.php';
		
			<?php 

			if (isset($_GET["seller_id"]) && is_numeric($_GET["seller_id"])) {
				print 'seller_id = "&seller_id='.$_GET["seller_id"].'";';
			}else{
				print 'seller_id = "";';
			}

			if (isset($_GET["CA"]) ) {
				print 'CA = "&CA='.$_GET["CA"].'";';
			}else{
				print 'CA = "";';
			}

			if (isset($_GET["only_seller_orders"])) {
				if ( $_GET["only_seller_orders"] == "true") {
					print 'only_seller_orders = "&only_seller_orders=true";';
				}else{
					print 'only_seller_orders = "&only_seller_orders=false";';
				}
			}else{
				print 'only_seller_orders = "";';
			}

			?>

			$('#selectseller .selectorview,#selectseller .selector').mouseup(function(){
				if($('#selectseller .selector').css('display')=='none'){
					$('#selectseller .selector').show();
				}else{
					$('#selectseller .selector').hide();
				}
			});

			$('#selectCA .selectorview,#selectCA .selector').mouseup(function(){
				if($('#selectCA .selector').css('display')=='none'){
					$('#selectCA .selector').show();
				}else{
					$('#selectCA .selector').hide();
				}
			});
		}).delegate('#selectseller .selector a', 'click', function(){
				seller_id = '&seller_id='+$(this).attr('name').replace('seller-','');
				window.location = 'pipeline.php?'+seller_id+only_seller_orders+CA;
			}
		).delegate('#selectCA .selector a', 'click', function(){
				CA = '&CA='+$(this).attr('name').replace('seller-','');
				window.location = 'pipeline.php?'+seller_id+only_seller_orders+CA;
			}
		).delegate('#only_seller_orders', 'change', function(){
				only_seller_orders = '&only_seller_orders='+this.checked;
				window.location = 'pipeline.php?'+only_seller_orders+seller_id+CA;
		}
		);
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
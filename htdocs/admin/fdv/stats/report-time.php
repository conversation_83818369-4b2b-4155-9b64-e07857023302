<?php

	/**	\file report-time.php
	 * 
	 * 	Cette page affiche un graphique ainsi qu'un tableau de données sur la répartition du temps des commerciaux. Ce rapport
	 * 	est calculé à partir des rapports de visite.
	 * 
	 */

	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	if( !gu_user_is_authorized('_RGH_ADMIN_FDV_STATS_REPORT_TIME') ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}

	define('ADMIN_PAGE_TITLE', _('Statistiques') . ' - ' . _('Rapport de visite'));

	require_once('admin/skin/header.inc.php');

	$_SESSION['ord_seller_id'] = 0;

	if( isset($_GET['seller_id']) ){
		$_SESSION['ord_seller_id'] = $_GET['seller_id'];
	}

?>
<h2><?php print _('Rapport de visite')?></h2>
<div class="stats-menu">
	<div id="riadatepicker"></div>
	<?php print view_sellers_selector( true ); ?>
	<div class="clear"></div>
</div>
<?php
	view_import_highcharts();
	require_once('admin/highcharts/graph-dev-report-time.php');
?>
<script>
	var riadatepicker_upd_url = '';
	<?php view_date_initialized(0, '', array('report-time'), array()); ?>

	$(document).ready(function() {
		var urlHighcharts = '/admin/fdv/stats/report-time.php';

		<?php print isset($_GET['seller_id']) && is_numeric($_GET['seller_id']) ? 'seller_id = "&seller_id='.$_GET["seller_id"].'"' : 'seller_id = ""'; ?>;

		$('#selectseller .selectorview, #selectseller .selector').mouseup(function() {
			if($('#selectseller .selector').css('display') === 'none'){
				$('#selectseller .selector').show();
			}else{
				$('#selectseller .selector').hide();
			}
		});
	}).delegate('#selectseller a', 'click', function() {
		seller_id = 'seller_id='+$(this).attr('name').replace('seller-', '');

		window.location = 'report-time.php?'+seller_id;
	});
</script>
<?php

require_once('admin/skin/footer.inc.php');
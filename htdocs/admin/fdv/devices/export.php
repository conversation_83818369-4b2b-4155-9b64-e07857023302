<?php 
    global $config;

    require_once('excel/PHPExcel.php');
    $alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

    // Création de l'objet PHPExcel
    $objPHPExcel = new PHPExcel();

    // Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
        ->setLastModifiedBy("riaStudio")
        ->setTitle(_("Export Gestion de parc / Flotte d'appareils"))
        ->setSubject(_("Export des appareils"))
        ->setDescription(_("Export des appareils"))
        ->setKeywords("export des appareils")
        ->setCategory("");

    // Création du fichier
    $objWorksheet = $objPHPExcel->getActiveSheet();

    // 1° feuille : Contient l'entête des commande
    $objWorksheet->setTitle(_('Liste des appareils'));

	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

	// Les administrateurs ne voient que les devices administrateurs ou utilisateurs, mais pas les devices super-administrateurs
    $listDevices = dev_devices_get( 0, $author, '', -1, '=', false, false, null, false );

    if (!$listDevices) {
		print _('Erreur dans la récupération de la liste des appareils');
		exit;
    }
    $letter = 'A';

    $objWorksheet->setCellValue($letter++.'1', _('Fabricant, modèle'));
    $objWorksheet->setCellValue($letter++.'1', _('Version'));
    $objWorksheet->setCellValue($letter++.'1', _('Utilisateur'));
    $objWorksheet->setCellValue($letter++.'1', _('Dernière synchro'));
    $line = 1;


    foreach(range('A',$letter) as $columnID) {
        $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)
            ->setAutoSize(true);
    }

    while($device = ria_mysql_fetch_assoc($listDevices)){
        $device_name = (isset($device['brand']) || isset($device['model'])) ? $device['brand'].' '.$device['model'] : $device['key'];
        $user_name = '';
        $ruser = gu_users_get( $device['usr_id'] );
        if( $ruser && ria_mysql_num_rows($ruser) ){
            $user = ria_mysql_fetch_assoc( $ruser );

            $user_name = trim( htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society']) );
        }
        $synchro = ( $device['date_last_sync'] ? date('d/m/Y à H:i', strtotime($device['date_last_sync'])) : _('En attente') );
        $letter = 'A';

        $objWorksheet->setCellValue($letter++.++$line, htmlspecialchars(ucfirst($device_name)));
        $objWorksheet->setCellValue($letter++.$line, $device['version']);
        $objWorksheet->setCellValue($letter++.$line, $user_name);
        $objWorksheet->setCellValue($letter++.$line, $synchro);
    }

    $objPHPExcel->setActiveSheetIndex(0);
    $style_hearder = array(
                        'font' => array(
                            'bold' => TRUE
                        ),
                        'alignment' => array(
                            'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                            'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER
                        ),
                        'borders' => array(
                            'allborders' => array(
                                'style' => PHPExcel_Style_Border::BORDER_THIN,
                                'color' => array('rgb' => '000000')
                            )
                        )                  
                         );
    $style_body = array(
                        'borders' => array(
                            'allborders' => array(
                                'style' => PHPExcel_Style_Border::BORDER_THIN,
                                'color' => array('rgb' => '000000')
                            )
                        )                  
                         );
    $objPHPExcel->getActiveSheet()->getStyle('A1:D1')->applyFromArray($style_hearder);
    $objPHPExcel->getActiveSheet()->getStyle('A2:D'.$line)->applyFromArray($style_body);

	

    // Ecrit le fichier et le sauvegarde
    // Redirect output to a clientâ??s web browser (Excel5)
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="flotte-appareils-'.$_SESSION['usr_id'].'.xls"');
    header('Cache-Control: max-age=0');

    // Ecrit le fichier et le sauvegarde
    $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
    $objWriter->save('php://output');
    exit;
    

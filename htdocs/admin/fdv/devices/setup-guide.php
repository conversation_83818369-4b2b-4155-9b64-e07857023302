<?php

	/** \file setup-guide.php
	 * 	Cette page sert de guide d'installation de l'application Yuto sur Smartphones et Tablettes.
	 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_FDV_DEVICE_GUIDE');

require_once('devices.inc.php');

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Guide d\'installation') . ' - Yuto');
require_once('admin/skin/header.inc.php');

?>

	<h2><?php print _('Guide d\'installation'); ?></h2>
	<ol id="liste-guide">
		<li>
			<?php print _('<b>Recherchez</b> et <b>installez</b> l\'application Yuto depuis le Store de votre téléphone ou de votre tablette.'); ?><br />
			<a target="_blank" rel="noopener noreferrer" href="https://play.google.com/store/apps/details?id=com.yuto&hl=fr"><img width="152" height="45" src="/admin/images/yuto/fr/btn-store-google.png" alt="<?php print _('Disponible sur Google Play') ?>"></a>
			<a target="_blank" rel="noopener noreferrer" href="https://apps.apple.com/fr/app/yuto-le-crm-mobile-et-fut%C3%A9/id1238674449"><img width="152" height="45" src="/admin/images/yuto/fr/btn-store-apple.png" alt="<?php print _('Télécharger dans l\'App Store') ?>"></a>
		</li>
		<li>
			<?php print _('<b>Ouvrez l\'application</b> pour accéder à la page de connexion.'); ?>
		</li>
		<li>
			<?php printf( _('Dans le champ <b>"Votre clé d\'utilisation"</b>, renseignez le code suivant <b id="token">%s</b> puis validez.'), dev_devices_get_tenant_token($config["tnt_id"]) ); ?>
			<img width="300" height="156" src="/admin/images/yuto/fr/input-token.png" alt="<?php print _('Votre clé d\'utilisation')?>">
		</li>
		<li>
			<?php if( RegisterGCP::onGcloud() && RegisterGCP::getPackage($config['tnt_id'])!='legacy' ){ ?>
				<?php print _('Saisissez ensuite vos <b>identifiants de connexion</b> : il s\'agit de l\'adresse email et du mot de passe que vous avez créés lors de votre inscription, puis cliquez sur le bouton <b>"Me connecter"</b>.'); ?>
			<?php }else{ ?>
				<?php print _('Saisissez ensuite vos <b>identifiants de connexion</b>, puis cliquez sur le bouton <b>"Me connecter"</b>.'); ?>
			<?php } ?>
			<img width="300" height="195" src="/admin/images/yuto/fr/login.png" alt="<?php print _('Me connecter')?>">
		</li>
		
		<?php if( !RegisterGCP::onGcloud() || RegisterGCP::getPackage($config['tnt_id'])=='legacy' ){ ?>
			<li>
				<?php print _('Pour toute première connexion, une <b>demande d\'activation de l\'appareil</b> est envoyée à l\'administrateur. <b>Rendez-vous sur <a href="index.php">cette page</a></b> pour activer les appareils.'); ?>
			</li>
			<li>
				<?php print _('Enfin, cliquez sur le bouton <b>"Synchroniser Yuto"</b> pour démarrer la synchronisation initiale des données entre RiaShop et l\'application Yuto.'); ?>
				<img width="300" height="287" src="/admin/images/yuto/fr/validation.png" alt="<?php print _('Synchroniser Yuto')?>">
			</li>
		<?php } ?>
	</ol>
	<p>
		<?php print _('<b>Félicitations</b>, vous êtes connectés à votre compte Yuto et prêts à gérer votre activité commerciale depuis votre appareil mobile !'); ?>
	</p>
	<?php if( RegisterGCP::onGcloud() && RegisterGCP::getPackage($config['tnt_id']) == 'essentiel' ){ ?>
		<div class="notice">
			<?php print _('Découvrez maintenant comment importer vos contacts : <a href="https://support.riashop.fr/aide/importer-contacts-dans-yuto/?utm_source=riashop&utm_medium=setup-guide&utm_campaign=support&utm_term=lien_voir_tutoriel" target="_blank">Voir le tutoriel</a>'); ?>
		</div>
	<?php }elseif( RegisterGCP::onGcloud() && RegisterGCP::getPackage($config['tnt_id']) == 'business' ){ ?>
		<div class="notice">
			<?php print _('Découvrez maintenant comment importer vos contacts : <a href="https://support.riashop.fr/aide/importer-contacts-dans-yuto/?utm_source=riashop&utm_medium=setup-guide&utm_campaign=support&utm_term=lien_voir_tutoriel" target="_blank">Voir le tutoriel</a>'); ?>
		</div>
	<?php } ?>
	
	<style>
		ol#liste-guide b, #site-content p b {
			font-size: 1.1em;
		}
		ol#liste-guide li, #site-content p {
			padding-bottom: 30px;
			line-height: 2em;
		}
		ol#liste-guide li > img {
			display: block;
			box-shadow: 1px 1px 4px #ccc;
		}
		#token {
			background-color: #E2F3FB;
			padding: 5px;
			font-size: 1.4em !important;
		}
		a[target="_blank"] {
			position: relative;
			margin-right: 0;
		}
		a[target="_blank"]:after {
			background-image: none;
		}
	</style>
<?php
 	require_once('admin/skin/footer.inc.php');

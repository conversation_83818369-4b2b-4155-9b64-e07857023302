<?php
	/**	\file edit.php
	 *	Cette page affiche la fiche d'une tablette/smartphone demandant ou ayant obtenu
	 *	un accès à Yuto.
	 */

	
	require_once('devices.inc.php');

	// Contrôle des paramètres d'entrée
	if( !isset($_GET['id']) || !is_numeric($_GET['id']) || $_GET['id'] <= 0 ){
		$device = array(
			'id' => 0, 'key' => '', 'version' => '', 'loc_lat' => '', 'loc_lng'=> '', 'usr_id' => 0, 'is_active' => null
		);
	}else{
		$rdevice = dev_devices_get( $_GET['id'] );
		if( !$rdevice || !ria_mysql_num_rows($rdevice) ){
			header('Location: /admin/fdv/index.php');
			exit;
		}
	
		$device = ria_mysql_fetch_assoc( $rdevice );
	}

	unset($error);
	require_once('devices.inc.php');

	// Boutons Enregistrer ou Activer
	if( isset($_REQUEST['save']) || isset($_GET['activate']) ){

		if( !isset($_REQUEST['activate']) || !$_REQUEST['activate'] ){
			if( !dev_devices_deactivate($device['id']) ){
				$error = _("Une erreur inattendue s'est produite lors de la désactivation des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
			}
		}else{
			$active = false;

			if( !gu_users_is_tenant_linked($device['usr_id']) ){
				$active = true;
			}elseif( !dev_devices_is_activated($device['id']) ){
				// Récupère le nombre d'appareils maximum autorisés
				$max_devices = dev_subscribtions_get_max();

				// Récupère le nombre d'appareil actuellement activé
				$ractived = dev_devices_get( 0, 0, '', -1, '=', false, false, true, true );
				$total_devices = $ractived ? ria_mysql_num_rows( $ractived ) : 0;
				
				if( $max_devices>0 && $max_devices<=$total_devices ){
					$error = sprintf(_("Vous avez atteint le maximum de %d appareils autorisés par votre abonnement.")."\n"._("Merci de prendre contact avec nous."), $max_devices);
					$_POST['activate'] = false;
				}else{
					$active = true;
				}
			}
			
			if( $active && !dev_devices_activate($device['id']) ){
				$error = _("Une erreur inattendue s'est produite lors de l'activation des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
			}
		}

		if( !isset($error) ){
			$_SESSION['success-edit-device'] = true;
			header('Location: /admin/fdv/devices/edit.php?id='.$device['id']);
			exit;
		}
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		if( is_numeric($device['id']) && $device['id'] ){
			if( !dev_devices_del($device['id']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
			}

			if( !isset($error) ){
				$_SESSION['success-delete-device'] = 1;
				header('Location: /admin/fdv/devices/index.php');
				exit;
			}
		}
	}

	if( sizeof($_POST) ){
		if( isset($_POST['user-id']) ){ $device['usr_id'] = $_POST['user-id']; }
		if( isset($_POST['activate']) ){ $device['is_active'] = $_POST['activate']; }
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', sprintf(_('Appareil %s - Gestion de parc / Flotte d\'appareils - Yuto'), htmlspecialchars($device['key'])));
	require_once('admin/skin/header.inc.php');

	// Utilisateur de l'appareil
	$new_device  = true;
	$device_name = 'Nouvel appareil';
	$user_name 	 = '<a href="#" onclick="return selectDeviceUser();">'._('Rattacher à un compte client').'</a>';

	if( is_numeric($device['id']) && $device['id'] ){
		$new_device = false;
		$device_name = 'Appareil '.htmlspecialchars($device['key']);
	
		if( is_numeric($device['usr_id']) && $device['usr_id'] ){
			$ruser = gu_users_get( $device['usr_id'] );
			if( $ruser && ria_mysql_num_rows($ruser) ){
				$user = ria_mysql_fetch_assoc( $ruser );

				$user_name = '
					<a href="/admin/customers/edit.php?usr='.$user['id'].'">'.view_usr_is_sync( $user ).' '.trim( htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society']) ).'</a>
				';
			}
		}
	}

	// Message spécial lorsque l'on force la mise à jour du paramétrage
	if( isset($_SESSION['success-edit-device']) && $_SESSION['success-edit-device'] ){
		$success = _('Les informations sur cet appareil ont correctement été mises à jour.');
		unset( $_SESSION['success-edit-device'] );
	}
	
	?>
		<h2><?php print view_devices_is_actived( $device ).' '.htmlspecialchars( $device_name ); ?></h2>
		<?php 
		// Affichage des messages d'erreur et de succès
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
		if( isset($success) ){
			print '<div class="success">'.nl2br( $success ).'</div>';
		}
		 ?>
		<form action="/admin/fdv/devices/edit.php?id=<?php print $device['id']; ?>" method="post">

					<table class="checklist">
						<col width="170" /><col width="450" />
						<tfoot>
							<tr>
								<td colspan="2">
									<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Enregistrer les modifications'); ?>" />
									<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" title="<?php print _('Annuler les modifications'); ?>" />
									<input onclick="return confirmDeleteDevice();" type="submit" name="delete" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer cet appareil'); ?>" />
								</td>
							</tr>
						</tfoot>
						<tbody>
							<tr>
								<th colspan="2"><?php print _('Général'); ?></th>
							</tr>
							<tr>
								<td>
									<label for="key"><?php print _('Identifiant :'); ?></label>
								</td>
								<td>
									<?php print htmlspecialchars($device['key']); ?>
								</td>
							</tr>
							<tr>
								<td>
									<span class="mandatory">*</span>
									<?php print _('Utilisateur :'); ?>
								</td>
								<td>
									<input type="hidden" name="user-id" id="user-id" value="<?php print $device['usr_id']; ?>" />
									<?php print $user_name; ?>
								</td>
							</tr>
							<tr>
								<td>
									<label for="activate-true"><span class="mandatory">*</span> <?php print _('Activé :'); ?></label>
								</td>
								<td>
									<input type="radio" name="activate" id="activate-true"  <?php print ( $device['is_active'] ? 'checked="checked"' : '' ); ?> value="1" />
									<label for="activate-true"><?php print _('Oui'); ?></label> 
									<input type="radio" name="activate" id="activate-false" <?php print ( !$device['is_active'] ? 'checked="checked"' : '' ); ?> value="0" />
									<label for="activate-false"><?php print _('Non'); ?></label>
								</td>
							</tr>
							<tr>
								<td>
									<label for="version"><?php print _('Version :'); ?></label>
								</td>
								<td>
									<?php print htmlspecialchars($device['version']); ?>
								</td>
							</tr>
					<?php

					if( !$new_device ){

						$device['date_last_call'] = $device['date_last_call'] == null ? $device['date_created_en'] : $device['date_last_call'];
										
						$signal_alert_class = "alive";
						if( strtotime($device['date_last_call']." +4 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$signal_alert_class = "dead";
						}else if( strtotime($device['date_last_call']." +1 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$signal_alert_class = "lost";
						} 

						print '
									<tr>
										<th colspan="2">'._('Informations complémentaires').'</th>
									</tr>
									<tr>
										<td>'._('Dernier signal').' :</td>
										<td class="signal-alert '.$signal_alert_class.'">'.date('d/m/Y à H:i', strtotime($device['date_last_call'])).'</td>
									</tr>
									<tr>
										<td>'._('Dernière localisation').' :</td>
										<td>
						';

						if( trim($device['loc_lat']) != '' && trim($device['loc_lng']) != '' ){
							print '
											<a href="#" onclick="return showDeviceLocation('.$device['loc_lat'].', '.$device['loc_lng'].', '.$device['loc_accuracy'].',\''.$device['date_last_call'].'\');">'._('Voir la localisation').'</a>
											('._('relevée le').' '.$device['loc_date'].')
							';
						}else{
							print 			_('Inconnue');
						}

						print '
										</td>
									</tr>
						';
					}

					print '
								</tbody>
							</table>
					';

					if( !$new_device ){
						$rlast_sync = dev_devices_tasks_get( $device['id'], 0, true );
						if( $rlast_sync && ria_mysql_num_rows($rlast_sync) ){
							print '
								<table class="checklist">
									<caption>'._('Dernières synchronisations').'</caption>
									<col width="*" /><col width="200" /><col width="200" />
									<thead>
										<tr>
											<th id="last-sync-cls">'._('Classe').'</th>
											<th id="last-sync-date">'._('Date d\'appel').'</th>
											<th id="last-sync-date-obj" title="'._('Date de dernière mise à jour de l\'objet le plus récemment synchronisé').'">'._('Date dernière objet').'</th>
											<th id="last-sync-count-obj">'._('Objet à synchroniser').'</th>
											<th id="time">'._('Temps de génération').'</th>
										</tr>
									</thead>
									<tbody>
							';

							while( $last_sync = ria_mysql_fetch_assoc($rlast_sync) ){

								$start = time();
								$count = 0;
								$date_last_obj = $last_sync['date_last_obj'];
								if( !isdateheure($last_sync['date_last_obj']) ){
									$date_last_obj = false;
								}
								dev_devices_get_sync_objects($device['id'],$last_sync["cls_id"], $count, $date_last_obj, true );

								print '
										<tr>
											<td headers="last-sync-cls">'.htmlspecialchars( $last_sync['cls_name'] ).'</td>
											<td align="center" headers="last-sync-date">'.htmlspecialchars( $last_sync['date_fr_last_sync'] ).'</td>
											<td align="center" headers="last-sync-date-obj">'.htmlspecialchars( $last_sync['date_fr_last_obj'] ).'</td>
											<td align="center" headers="last-sync-count-obj">'.$count.'</td>
											<td align="center" headers="time">'.( round((time()-$start) ,2) ).'s</td>
										</tr>
								';
							}
						}
					}

		print '			<tbody>
					</table>
			';
			

	print '
		</form>
	';

	require_once('admin/skin/footer.inc.php');
?>
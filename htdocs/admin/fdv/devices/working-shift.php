<?php

	/**	\file working-shift.php
	 *	Cette page permet la configuration des horaires d'activité pour le suivi des smartphones et tablettes Yuto
	 */

	require_once( 'periods.inc.php' );
	require_once( 'delivery.inc.php' );
	require_once( 'users.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_DEVICE_HOUR');

	$_SESSION['ord_seller_id'] = 0;
	$seller_usr_id = 0;
	if (isset($_GET["seller_id"]) && $_GET["seller_id"] > 0 ) {
		$rusr = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $_GET["seller_id"]);
		if ($rusr && ria_mysql_num_rows($rusr) > 0 ) {
			$seller_usr_id = ria_mysql_result($rusr, 0, 'id');
			if (gu_users_exists($seller_usr_id)) {
				$_SESSION['ord_seller_id'] = $_GET["seller_id"];
			}else{
				$seller_usr_id = null;
			}
		}
	}

	$robject = per_objects_get(CLS_USER, $seller_usr_id );
	$pob_id = 0;
	// Récuperation de l'objet
	if ($robject && ria_mysql_num_rows($robject) > 0) {
		$pob_id = ria_mysql_result($robject, 0, 'id');
	}


	if (isset($_POST["save-holidays"]) && isset($_POST['hol-date'])) {
		foreach( $_POST['hol-date'] as $date=>$exp ){
			if( !per_holidays_add($pob_id, $date, $exp) ){
				$error = _("Une erreur inattendue est survenue lors de l'enregistrement d'une période de fermeture.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur code 1");
				break;
			}
		}
	}

	if( isset($_POST['save-exp']) ){
		
		if (!$pob_id) {
			$pob_id = per_objects_add(CLS_USER, PER_USER_SHIFT, $seller_usr_id);
		}

		
		// Supprime les périodes déjà enregistrées
		if( !per_periods_del($pob_id) ){
			$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'activité.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur code 1");
		}
		
		// Parcour tous les jours de la semaine
		if( isset($_POST['hr']) && !isset($error) ){
			foreach( $_POST['hr'] as $day=>$hours ){
			
				// Parcour chaque heure pour trouver les périodes d'horaires
				$temp = $count = $start = $end = 0;
				$first = true;
				foreach( $hours as $key=>$hour ){
					if( $first ) {
						$start = $hour; 
						$temp = $hour; 
						$count++;
						$first = false;
					}
					
					$h = $hour - $temp;
					if( $h>1 ){ // Il s'agit d'une autre période
						
						// On enregistre la période
						$end = $end>0 ? $end : $start;
						
						if( !per_periods_add($pob_id, $day, $start, $end) ){
							$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur code 2");
						}
						$start = $hour;
						$temp = $hour;
						$count = 0;
					} else {
						// Il s'agit de la même périod
						$end = $hour;
						$temp = $hour;
						$count++;
					}
					$end = $hour;
				}
				
				// On enregistre la dernière période
				$r = per_periods_add($pob_id, $day, $start, $end);
				if( !$r ){
					$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur code 3");
				}
			}
		}
		if( !isset($error) )
			$success = _("L'enregistrement des horaires s'est correctement déroulé.");
	}
	
	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Horaires d\'activité - Gestion de parc - Yuto'));
	require_once('admin/skin/header.inc.php');
?>

	<h2><?php print _('Horaires d\'activité'); ?></h2>
	<p>
		<?php print _('Les paramètres ci-dessous sont utilisés pour définir les horaires d\'activité de vos commerciaux de terrain.').'<br/>'._('La configuration renseignée ci-dessous impacte notamment le tracking des appareils.').'<br/>'._('En dehors des horaires d\'activité les appareils ne sont plus suivis.'); ?>
	</p>

<?php 
	print view_sellers_selector();
?>	
<div class="clear"></div>
	<h3><?php print _('Jours et heures d\'activité en semaine normale'); ?></h3>
	<form id="form-expedition" action="working-shift.php?seller_id=<?php print $_SESSION['ord_seller_id']; ?>" method="post">
		<div class="table-layout-large">
			<table id="tb-day-exp" class="tb-day-exp2">
				<thead>
					<tr>
						<th id="day-exp"><?php print _('Jour'); ?></th>
						<th id="period-exp"><?php print _('Période'); ?></th>
						<th id="midnight"><?php print _('Minuit'); ?></th>
						<th id="hr-four"><?php print _('4h00'); ?></th>
						<th id="hr-eight"><?php print _('8h00'); ?></th>
						<th id="noon"><?php print _('Midi'); ?></th>
						<th id="hr-sixteen"><?php print _('16h00'); ?></th>
						<th id="hr-twenty"><?php print _('20h00'); ?></th>
						<th id="action-exp"></th>
					</tr>
				</thead>
				<tbody>
					<?php 
						
						$hourly = array();

						// Récupère toutes les horaires d'suivi
						$robject = per_periods_get($pob_id);
						
						if( $robject!==false ){
							while( $object = ria_mysql_fetch_array($robject) ){
								// Récupère le nom du jour de la semaine de cette période
								$day = dlv_day_get_name($object['day_id']);
								
								// Information sur les périodes
								if( !isset($lbl_period[$day]) ) $lbl_period[$day] = '';
								$lbl_period[$day] .= $object['start'];
								if( $object['end']!=$object['start'] ){
									$end = ($object['int-end']+1)<10 ? '0'.($object['int-end']+1).':00' : ($object['int-end']+1).':00';
									$lbl_period[$day] .= '&nbsp;à&nbsp;'.$end.' ';
								} else {
									$end = ($object['int-end']+1)<10 ? '0'.($object['int-start']+1).':00' : ($object['int-start']+1).':00';
									$lbl_period[$day] .= '&nbsp;à&nbsp;'.$end.' ';
								}
								
								// Retrouve toute les heures contenus dans les pédiodes et les insert dans un tableau
								for( $i=$object['int-start'] ; $i<=$object['int-end'] ; $i++ ){
									$hourly[$day][] = $i;
								}
							}
						}
						
						$days = array( _('Lundi'), _('Mardi'), _('Mercredi'), _('Jeudi'), _('Vendredi'), _('Samedi'), _('Dimanche') );
						$id_days = array( _('Lundi')=>'monday', _('Mardi')=>'thuesday', _('Mercredi')=>'wednesday', _('Jeudi')=>'thursday', _('Vendredi')=>'friday', _('Samedi')=>'saturday', _('Dimanche')=>'sunday' );
						
						$count = 0;
						foreach( $days as $key=>$day ){
							print '	<tr>';
							print '		<td headers="day-exp">'.$day.'</td>';
							print '		<td headers="period-exp" class="period" id="period-'.$id_days[$day].'">'.( isset($lbl_period[$id_days[$day]]) ? $lbl_period[$id_days[$day]] : _('Aucun suivi') ).'</td>';
							print '		<td headers="midnight">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(0, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(0, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(1, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(1, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(2, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(2, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(3, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(3, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-four">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(4, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(4, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(5, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(5, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(6, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(6, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(7, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(7, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-eight">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(8, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(8, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(9, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(9, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(10, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(10, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(11, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(11, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="noon">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(12, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(12, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(13, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(13, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(14, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(14, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(15, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(15, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-sixteen">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(16, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(16, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(17, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(17, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(18, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(18, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(19, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(19, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td><td headers="hr-twenty">';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(20, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(20, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(21, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(21, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(22, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(22, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '			<div class="'.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(23, $hourly[$id_days[$day]]) ? 'hr-active' : 'hr-inactive' ).'" onclick="selectedHourly(\''.$id_days[$day].'\', '.$count.')" title="'.$count.'h"><input class="hr-check" type="checkbox" name="hr['.$id_days[$day].'][]" id="hr-'.$id_days[$day].'-'.$count.'" '.( sizeof($hourly)>0 && isset($hourly[$id_days[$day]]) && in_array(23, $hourly[$id_days[$day]]) ? 'checked="checked"' : '' ).' value="'.($count++).'"/></div>';
							print '		</td>
										<td headers="action-exp" class="reinit">
											<a class="del button" onclick="reset(\''.$id_days[$day].'\')">'._('Tout supprimer').'</a>
										</td>
									</tr>';
							$count = 0;
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="9">
							<input type="submit" name="save-exp" id="save-exp" value="<?php print _('Enregistrer'); ?>" />
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
	</form>
	
	<h3><?php print _('Jours fériés'); ?></h3>
	<p>
		<?php print _('Si vous souhaitez maintenir les suivis un jour férié, sélectionnez les journées ci-dessous.'); ?> 
	</p>
	<form id="form-holidays" action="working-shift.php?seller_id=<?php print $_SESSION['ord_seller_id']; ?>" method="post">
		<!-- <div class="table-layout-large"> -->
			<table id="tb-holidays">
				<thead>
					<tr id="th-year">
						<th></th>
						<th class="align-center"><?php printf( _('Année %s'), date('Y')); ?></th>
						<th class="align-right"><img class="hld-nav-year" src="/admin/images/expeditions/feries_active_right.svg" onclick="holidays(<?php print date('Y')+1; ?>,<?php print $pob_id; ?>,<?php print CLS_USER; ?>,<?php print PER_USER_SHIFT; ?>,<?php print $seller_usr_id; ?>)" alt="<?php printf( _('Année %s'), date('Y')+1); ?>" title="<?php printf( _('Année %s'), date('Y')+1); ?>" /></th>
					</tr>
					<tr>
						<th id="hld-date"><?php print _('Date'); ?></th>
						<th id="hld-name"><?php print _('Fête'); ?></th>
						<th id="hld-exp"><?php print _('Activité'); ?></th>
					</tr>
				</thead>
				<tbody>
					<?php
						// Récupère toutes les dates de jours fériés (pour l'année en cours de modification) où les suivis ont lieu
						$rhld = per_holidays_get( date('Y'), true, $pob_id);
						
						// Construit un tableau de toutes les dates
						$current_holidays = array();
						if( $rhld!=false ){
							while( $hld = ria_mysql_fetch_array($rhld) ){
								$current_holidays[] = $hld['date'];
							}
						}
						
						$holidays = holidays(date('Y'));
						foreach( $holidays as $date=>$name ){
							print '	<tr>
										<td headers="hld-date" class="hld-date">'.dateformatcomplet( strtotime($date) ).'</td>
										<td headers="hld-name">'.htmlspecialchars( $name ).'</td>
										<td headers="hld-exp" class="hld-exp">
											<label for="hld-exp-yes-'.$date.'">
												<input type="radio" name="date['.$date.']" id="hld-exp-yes-'.$date.'" value="1" '.( in_array($date, $current_holidays) ? 'checked="checked"' : '' ).' />
												'._('Oui').'
											</label>
											<label for="hld-exp-no-'.$date.'">
												<input type="radio" name="date['.$date.']" id="hld-exp-no-'.$date.'" value="0" '.( in_array($date, $current_holidays) ? '' : 'checked="checked"' ).' />'._('Non').'
											</label>
										</td>
									</tr>';
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="3">
							<input type="hidden" name="type-id" id="type-id" value="<?php print PER_USER_SHIFT; ?>" />
							<input type="hidden" name="cls-id" id="cls-id" value="<?php print CLS_USER; ?>" />
							<input type="hidden" name="usr-id" id="usr-id" value="<?php print $seller_usr_id; ?>" />
							<input type="hidden" name="hld-pob-id" id="hld-pob-id" value="<?php print $pob_id; ?>" />
							<input type="button" name="save-hld" id="save-hld" value="<?php print _('Enregistrer'); ?>" onclick="holidays_save();" />
						</td>
					</tr>
				</tfoot>
			</table>
		<!-- </div> -->
	</form>
	<h3><?php print _('Périodes de fermeture exceptionnelles'); ?></h3>
	<form id="form-closing" action="working-shift.php" method="post">
		<?php
			// Récupère les dates de fermeture exceptionnelle
			$rclosing = per_events_get($pob_id, 0, $period);
			if( ria_mysql_num_rows($rclosing)>10 ){
		?>
		<div class="closing-menu">
			<div id="closingpicker" data-href="working-shift.php" data-usr-id="<?php print $_SESSION['ord_seller_id']; ?>">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _('Fermetures'); ?></span><br/>
						<?php
							$period = isset($_GET['period']) && ( $_GET['period']>=0 || $_GET['period']<3 ) ? $_GET['period'] : 0;

							switch($period){
								case 0 :
									print '<span class="view">'._('Toutes').'</span>';
									break;
								case 1 :
									print '<span class="view">'._('Actuelles').'</span>';
									break;
								case 2 :
									print '<span class="view">'._('&Agrave; venir').'</span>';
									break;
								default :
									print '<span class="view">'._('Toutes').'</span>';
									break;
							}
						?>
					</div>
					<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="p-0"><?php print _('Toutes'); ?></a>
					<a name="p-1"><?php print _('Actuelles'); ?></a>
					<a name="p-2"><?php print _('&Agrave; venir'); ?></a>
				</div>
			</div>
			<div class="clear"></div>
		</div>
		<?php } ?>
		<!-- <div class="table-layout-large"> -->
			<table id="tb-closing">
				<thead>
					<tr>
						<th id="clg-name"><?php print _('Nom de la période'); ?></th>
						<th id="clg-start"><?php print _('Date de début (incluse)'); ?></th>
						<th id="clg-end"><?php print _('Date de fin (incluse)'); ?></th>
						<th id="clg-action"></th>
					</tr>
				</thead>
				<tbody>
					<?php						
						if( $rclosing==false || ria_mysql_num_rows($rclosing)==0 ){
							// Affiche un message si aucune fermeture n'existe
							print '	<tr id="none-closing">
										<td colspan="4">'._('Aucune fermeture exceptionnelle n\'est enregistrée pour le moment').'</td>
									</tr>';
						} else {
							// Affichage des fermetures exceptionnelle déjà enregistré
							while( $closing = ria_mysql_fetch_array($rclosing) ){
								print '	<tr id="closing-'.$closing['id'].'">
											<td headers="clg-name" class="td-info">'.htmlspecialchars( $closing['name'] ).'</td>
											<td headers="clg-start" class="td-date">'.ria_date_format($closing['start']).'</td>
											<td headers="clg-end" class="td-date">'.ria_date_format($closing['end']).'</td>
											<td headers="clg-action" class="td-action">
												<a class="edit-closing button" title="'._('Editer les informations concernant cette fermeture exceptionnelle').'" onclick="closing_edit('.$closing['id'].', \''.addslashes(htmlspecialchars($closing['name'])).'\', \''.$closing['start'].'\', \''.$closing['end'].'\');">'._('Editer').'</a>
												<div id="save-load-'.$closing['id'].'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>
												<br /><a class="del button" onclick="closing_del('.$closing['id'].');">'._('Supprimer').'</a>
											</td>
										</tr>
								';
							}
						}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="4" class="align-right">
							<input type="hidden" name="cls-id" id="cls-id" value="<?php print CLS_USER; ?>" />
							<input type="hidden" name="usr-id" id="usr-id" value="<?php print $seller_usr_id; ?>" />
							<input type="hidden" name="type-id" id="type-id" value="<?php print PER_USER_SHIFT; ?>" />
							<input type="hidden" name="closing-pob-id" id="closing-pob-id" value="<?php print $pob_id; ?>" />
							<input type="button" name="add-closing" id="add-closing" value="<?php print _('Nouvelle Période'); ?>" title="<?php print _('Ajouter une nouvelle période de fermeture exceptionnelle'); ?>" onclick="closing_add();" />
						</td>
					</tr>
				</tfoot>
			</table>
		<!-- </div> -->
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
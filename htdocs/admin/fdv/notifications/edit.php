<?php
	require_once('notifications.inc.php');

	$current_notification = isset( $_GET['nt_id'] ) && $_GET['nt_id'] ? $_GET['nt_id'] : 0;
	$type_models = array();
	$type = array();
	if( $current_notification !== 0 ){
		$notification = nt_notifications_get($current_notification);
		
		if( !$notification ){
			header('Location: index.php');
			exit;
		}
	
	}

	if( !isset($notification['_id']) ){
		$notification['_id'] = 0;
	}
	if( !isset($notification['nt_author_id']) ){
		$notification['nt_author_id'] = 0;
	}
	if( !isset($notification['nt_title']) ){
		$notification['nt_title'] = isset($_POST['title']) ? $_POST['title'] : '';
	}
	if( !isset($notification['nt_title']) ){
		$notification['nt_title'] = isset($_POST['title']) ? $_POST['title'] : '';
	}
	if( !isset($notification['nt_desc']) ){
		$notification['nt_desc'] = isset($_POST['desc']) ? $_POST['desc'] : '';
	}
	if( !isset($notification['nt_state']) ){
		$notification['nt_state'] = NT_STATE_DRAFT;
	}
	if( !isset($notification['users']) ){
		$notification['users'] = isset($_POST['usr']) ? $_POST['usr'] : array();
	}

	if (isset($_POST['cancel'])) {
		header('Location: index.php');
		exit;
	}
	if (isset($_POST['del']) ) {

		if( $_GET['nt_id'] ){

			// vérification de l'état de la notification, celle envoyé ne peuvent être supprimé
			$notification = nt_notifications_get($_GET['nt_id']);
			if( isset($notification['nt_state']) && $notification['nt_state'] != NT_STATE_DRAFT){
				$error[] = _("La notification n'est plus modifiable car elle est envoyée.");
			}

			if( isset($_POST['del']) ){
				if( !nt_notifications_del($_GET['nt_id']) ){
					$error[] = _("La notification n'a pas été correctement supprimée.");
				}
			}

		}
		header('Location: index.php');
		exit;
	}

	$error = array();
	if (isset($_POST['save'])) {

		if( trim($_POST['title']) == '') {
			$error[] = _("Le titre de la notification ne peut être vide.");
		}
		if( trim($_POST['desc']) == '') {
			$error[] = _("La description de la notification ne peut être vide.");
		}
		if( !isset($_POST['usr']) || !sizeof($_POST['usr'])) {
			$error[] = _("Veuillez sélectionner des destinataires avant de poursuivre.");
		}

		if(!sizeof($error)){
			if( $_GET['nt_id'] ){

				$nt_id = $_GET['nt_id'];

				if( !nt_notification_exists($nt_id) ){
					$error[] = _("La notification n'existe pas.");
				}

				$notification = nt_notifications_get($nt_id);
				if( $notification  ){
					if( isset($notification['nt_state']) && $notification['nt_state'] != NT_STATE_DRAFT){
						$error[] = _("La notification n'est plus modifiable car elle est envoyée.");
					}
				}

				if( !sizeof($error) ){

					if( !nt_notifications_upd( $nt_id, NT_TYPE_MANUAL, $_SESSION['usr_id'], $_POST['title'], $_POST['desc'] ) ){
						$error[] = _("Erreur lors de la mise à jour de la notification.");
					}

					// mise à jour 
					if( !sizeof($error) && !nt_notifications_set_users($nt_id, $_POST['usr']) ){
						$error[] = _("Erreur lors de la mise à jour des destinataires");
					}
				}

			}else{

				// ajout de la notif 
				$nt_id = nt_notifications_add( NT_TYPE_MANUAL, $_SESSION['usr_id'], $_POST['title'], $_POST['desc'] ); 

				if( !$nt_id ){
					$error[] = _("Erreur lors de la création de la notification.");
				}else{
					// ajout des destinataires à la notifications 
					foreach( $_POST['usr'] as $i => $usr ){
						if( !is_numeric($usr) ){
							unset($_POST['usr'][$i]);
							continue;
						}
					}

					if( !nt_notifications_set_users($nt_id, $_POST['usr']) ){
						$error[] = _("Erreur lors de la mise à jour des destinataires");
					}

				}
			}

			if( !sizeof($error) ){
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_DEVICE_NOTIFICATION_SEND, array('id'=>$nt_id));
				header('Location: index.php');
				exit;
			}
		}
	}

// charge la liste des tablettes active
$rdev = dev_devices_get(0, 0, '', -1, '=', false, false, true, false, 0, false, true);
if( !ria_mysql_num_rows($rdev) ){
	$error[] = _('Pour utiliser cette fonctionnalité, vous devez installer Yuto sur un ou plusieurs appareils.');
}

// Titre de la page
$page_title = trim($notification['nt_title']) ? 'Notification '.$notification['nt_title'] : _('Nouvelle notification');
define('ADMIN_PAGE_TITLE', $page_title . ' - ' . _('Notifications') .' - Yuto');
require_once('admin/skin/header.inc.php');

// Affiche les messages de succès ou d'erreur
if( sizeof($error) ){
	print '<div class="error">'.nl2br( implode('<br/>', $error) ).'</div>';
}elseif( isset($success) ){
	print '<div class="success">'.$success.'</div>';
}

?>

<h2><?php print htmlspecialchars( $page_title ); ?></h2>

<form action="edit.php?nt_id=<?php print $notification['_id']; ?>" method="post">
	<table>
	<tbody>
		<tr>
			<td><span class="mandatory">*</span> <label for="name"><?php print _('Titre :'); ?></label></td>
			<td>
				<input type="text" name="title" id="title" <?php print $notification['nt_state']!=NT_STATE_DRAFT ? 'disabled="disabled"': '' ?> maxlength="75" value="<?php print htmlspecialchars($notification['nt_title']); ?>" />
			</td>
		</tr>
		<tr>
			<td><span class="mandatory">*</span> <label for="name"><?php print _('Description :'); ?></label></td>
			<td>
				<textarea type="text" name="desc" id="desc" <?php print $notification['nt_state']!=NT_STATE_DRAFT ? 'disabled="disabled"': '' ?> ><?php print htmlspecialchars($notification['nt_desc']); ?></textarea>
			</td>
		</tr>
		<tr> 
			<td><?php print _('Destinataires :'); ?></td>
			<td>
				<?php if( $notification['nt_state']==NT_STATE_DRAFT  ){ ?>
				<a href="#" class="check-all"><?php print _('Cocher tout'); ?></a> | <a href="#" class="uncheck-all"><?php print _('Décocher tout'); ?></a>
				<?php } ?>
				<?php 
					$usr_ids = array();

					// Affiche la liste des tablettes actives
					if( $rdev && ria_mysql_num_rows($rdev) ){
						while( $dev = ria_mysql_fetch_assoc($rdev)){
							$usr_ids[] = $dev['usr_id'];
						}
						// charge la liste des utilisateurs 
						$rusr = gu_users_get($usr_ids);
						if( $rusr && ria_mysql_num_rows($rusr) ){
							while( $usr = ria_mysql_fetch_assoc($rusr) ){
								if($usr['tenant'] > 0){
									$is_checked = isset($notification['users']) && is_array($notification['users']) && in_array($usr['id'], $notification['users']);
									?>
									<div>
										<input type="checkbox" id="usr-<?php print $usr['id']; ?>" name="usr[]" value="<?php print $usr['id']; ?>" <?php print $notification['nt_state']!=NT_STATE_DRAFT ? 'disabled="disabled"': '' ?> <?php print $is_checked ? 'checked="checked"':''?>/> 
										<label for="usr-<?php print $usr['id']; ?>"><?php print htmlspecialchars( $usr['adr_firstname'].' '.$usr['adr_lastname'] ); ?></label>
									</div>
									<?php
								}
							}
						}
					}
				?>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr><td colspan="2">
			<?php if( $notification['nt_state']==NT_STATE_DRAFT  ){
					if( $notification['_id'] ){ ?>
					<input type="submit" name="save" value="<?php print _('Envoyer')?>" />
				<?php }else{ ?>
					<input type="submit" name="save" value="<?php print _('Ajouter')?>" />
				<?php } ?>
					<input type="submit" name="cancel" value="<?php print _('Annuler')?>"/>
				<?php if( $notification['_id'] ){ ?>
					<input type="submit" name="del" value="<?php print _('Supprimer')?>" onclick="return confirm('<?php print _('Voulez-vous vraiment effectuer cette suppression ?')?>')" />
				<?php }
				}else{ ?>
				<input type="submit" name="cancel" value="<?php print _('Annuler')?>"/>
			<?php } ?>
		</td></tr>
	</tfoot>
	</table>
</form>

<?php
	require_once('admin/skin/footer.inc.php');
?>
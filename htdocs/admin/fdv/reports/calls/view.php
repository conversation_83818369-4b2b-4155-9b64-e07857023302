<?php

	/**`\file view.php
	 *	Cette page affiche un rapport d'appel.
	 */

    //require_once('reports.inc.php');
	require_once('strings.inc.php');

    $current_call = isset( $_GET['id'] ) && $_GET['id'] ? $_GET['id'] : 0;
   
    $call = array(); 
	if( $current_call !== 0 ){
        $call = gcl_calls_get_by_view($current_call);
    }
    
    if( !$call || !sizeof($call)){
        header('Location: index.php');
        exit;
    }

    $author_id = $call['gcl_author_id'];
    if ($call['gcl_type'] != 2){
        $author_id = $call['gcl_usr_dst'];
    }
    $author = array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );
    $r_author = gu_users_get($author_id);
	if( $r_author && ria_mysql_num_rows($r_author) ){
		$author = ria_mysql_fetch_assoc( $r_author );
	}

    $dest_id = $call['gcl_usr_dst'];
    if ($call['gcl_type'] != 2){
        $dest_id = $call['gcl_author_id'];
    }
    $comment = $call['gcl_comment'] != '' ? $call['gcl_comment'] : '';
    $dest = array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );
	$r_dest = gu_users_get( $dest_id );
	if( $r_dest && ria_mysql_num_rows($r_dest) ){
		$dest = ria_mysql_fetch_assoc( $r_dest );
    }

	// Bouton Annuler
	if (isset($_POST['cancel'])) {
		header('Location: index.php');
		exit;
    }

    // Bouton Enregistrer
    if( isset( $_POST['save'] ) ){
        
        if( isset( $_POST['comments'] ) && ( $commenttmp = trim( strip_tags( $_POST['comments'] ) ) ) != '' ){
            $call_post = CouchDB::create()->get( CLS_CALLS, $current_call );
            $call_post['gcl_comment'] = $commenttmp;
            
            if( !CouchDB::create()->update( CLS_CALLS, $current_call, $call_post ) ){
                $error = 'Le rapport d\'appel n\'a pas pu être enregistré';
            }else{
                $comment = $commenttmp;
                $success = 'Le rapport d\'appel a bien été enregistré';
            }
        }
    }

	// Défini le titre de la page
	$page_title = _('Appel du ').date(_('d/m/Y à H:i'), strtotime($call['gcl_date_created']));
    define('ADMIN_PAGE_TITLE', $page_title . ' - ' . _('Rapport d\'appel') . ' - Yuto');
    require_once('admin/skin/header.inc.php');

?>

<h2><?php print htmlspecialchars( $page_title ); ?></h2>

<?php
if( isset( $error ) ){
	print '<div class="error">'.nl2br( htmlspecialchars( $error ) ).'</div>';
}elseif( isset( $success ) ){
	print '<div class="error-success">'.nl2br( htmlspecialchars( $success ) ).'</div>';
}
?>

<form action="view.php?id=<?php print $current_call; ?>" method="post">
	<table>
    <col width="225" /> <col width="250" />
	<tfoot>
		<tr>
            <td colspan="2">
                <input type="submit" name="save" value="<?php print _('Enregistrer') ?>" />
                <input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
            </td>
        </tr>
	</tfoot>
	<tbody>
        <tr>
            <th colspan="2"><?php print _('Informations générales')?></th>
        </tr>
		<tr>
			<td><?php print _('Auteur :'); ?></td>
            <td><?php
                $author_name = gu_users_get_name($author_id); 
                $author_name = $author_name != '' ? $author_name : _('Compte client n°').' '.$call['gcl_author_id'];
                if( $author == array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 ) ){
                    print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars($author_name).'</span>';
                }else{
                    print '<a href="/admin/customers/edit.php?usr='.$call['gcl_author_id'].'">';
                    print view_usr_is_sync( $author ).'&nbsp;'.htmlspecialchars( $author_name );
                    print '</a>';					
                }
            ?></td>
		</tr>
		<tr>
			<td><?php print _('Destinataire :')?></td>
            <td><?php
                $dest_name = gu_users_get_name($dest_id); 
                $dest_name = $dest_name != '' ? $dest_name : _('Compte client n°').' '.$dest_id;
                if( $dest == array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 ) ){
                    print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars($dest_name).'</span>';
                }else{
                    print '<a href="/admin/customers/edit.php?usr='.$dest_id.'">';
                    print view_usr_is_sync( $dest ).'&nbsp;'.htmlspecialchars( $dest_name );
                    print '</a>';					
                }
            ?></td>
		</tr>
        <tr>
			<td><?php print _('Numéro appelé :'); ?></td>
            <td><?php print $call['gcl_phone'] ?></td>
		</tr>
        <tr>
			<td><?php print _('Durée d\'appel :'); ?></td>
            <td><?php 
                $duree = $call['gcl_duration'];
                $heures = floor($duree/60/60);
                $minutes = floor($duree/60) - $heures*60;
                $secondes = floor($duree) - $minutes*60 - $heures*60*60;
                $call_time = '';
                if ($heures!=0){
                    $call_time .= $heures.' '.($heures > 1 ? _('heures') : _('heure')).' ';
                }
                if ($minutes!=0){
                    $call_time .= $minutes.' '.($minutes > 1 ? _('minutes') : _('minute')).' ';
                }
                $call_time .= $secondes.' '.($secondes > 1 ? _('secondes') : _('seconde'));
                print $call_time;
            ?></td>
		</tr>
		<tr> 
			<td><?php print _('Date de l\'appel :'); ?></td>
			<td><?php print date('d/m/Y à H:i', strtotime($call['gcl_date_created'])); ?></td>
        </tr>
        <tr>
            <td><?php print _('Commentaires :');?></td>
            <td>
                <textarea name="comments" id="comments" rows="15" cols="40"><?php print $comment;?></textarea>
            </td>
        <?php /*
            if ($call['gcl_comment'] != ''){ 
                print '<tr>';
                    print '<td>'._('Commentaires :').'</td>';
                    print '<td>'.htmlspecialchars( $call['gcl_comment'] ).'</td>';
                print '</tr>';
            }
        */?>

        <?php 
            if (sizeof($call['_fields'])){ 
                print '<tr>';
                    print '<th colspan="2">'._('Informations complémentaires').'</th>';
                print '</tr>';
                foreach ($call['_fields'] as $key => $field) {
                    print '<tr>';
                        print '<td>'.$field['fld_name'].'</td>';
                        if ($field['fld_type_id'] != FLD_TYPE_SELECT_HIERARCHY){
                            print '<td>'.htmlspecialchars( $field['value'] ).'</td>';
                        } else {
                            print '<td>'.htmlspecialchars( $field['value_name'] ).'</td>';
                        }
                    print '</tr>';
                }
            } 
        ?>
	</tbody>
	</table>
</form>

<?php
	require_once('admin/skin/footer.inc.php');
?>
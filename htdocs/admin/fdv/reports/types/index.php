<?php

	/**	\file index.php
	 *	Cette page affiche la liste des types de rapports sous deux formes :
	 *	- un graphique highcharts
	 *	- un tableau de données
	 */

	require_once('reports.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_REPORT');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: /admin/fdv/reports/types/edit.php');
		exit;
	}

	$rtype = rp_types_get();
	$count_types = $rtype ? ria_mysql_num_rows( $rtype ) : 0;
	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;
	$type = isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0 ? $_GET['type'] : 0;
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if ( isset($_GET["date1"], $_GET["date2"]) ) {
		$date1 = ria_mysql_escape_string(dateheureparse($_GET["date1"]));
		$date2 = ria_mysql_escape_string(dateheureparse($_GET["date2"]));
	}

	// Bouton Exporter
	if( isset($_POST['export']) ){
		// Gestion de l'export avec les données de sélecteur
		header('Location: /admin/fdv/reports/export.php?type=0'.'&author='.$_POST['author'].'&date1='.$date1.'&date2='.$date2);
		exit;
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Types de rapports') . ' - ' . _('Rapports de visite') . ' - Yuto');
	require_once('admin/skin/header.inc.php');
?>

<h2><?php print _('Rapports de visite') ?> (<?php print ria_number_format($count_types); ?> types)</h2>
<?php
	// Affiche le sélecteur d'Auteur de rapport
	$rall_author = rp_reports_get_all_author( 0, array( 'adr_firstname' => 'asc', 'adr_lastname' => 'desc' ) );
	if( $rall_author && ria_mysql_num_rows($rall_author) ){
?>
		<div class="stats-menu">
			<div id="riadatepicker"></div>
			<div class="riapicker" id="select-fdv-report-visite-author">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _('Auteur')?></span>
						<br/><span class="view"><?php
							if( $author <= 0 ){
								print _('Tous les auteurs');
							}else{
								$select_author 	= array( 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );

								$r_select_author = gu_users_get( $author );
								if( $r_select_author && ria_mysql_num_rows($r_select_author) ){
									$select_author = ria_mysql_fetch_assoc( $r_select_author );
								}

								print trim( $select_author['adr_firstname'].' '.$select_author['adr_lastname'].' '.$select_author['society'] );
							}
						?></span>
					</div>
					<a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" class="fleche" /></a>
					<div class="clear"></div>
				</div>
				<div class="selector" style="display: none;">
					<a name="author-0"><?php print _('Tous les auteurs')?></a>
					<?php
						while( $all_author = ria_mysql_fetch_assoc($rall_author) ){
							print '<a name="author-'.$all_author['id'].'">'.htmlspecialchars( trim($all_author['firstname'].' '.$all_author['lastname'].' '.$all_author['society']) ).'</a>';
						}
					?>
				</div>
			</div>
			<div class="clear"></div>
		</div>
<?php } ?>
 <!-- graphiques -->
    <?php
        view_import_highcharts();
        require_once( 'admin/highcharts/graph-reports-global.php' );
    ?>

<form method="POST">
	<input type="hidden" name="author" id="author" value="<?php print $author; ?>" />
	<input type="hidden" name="date1" id="date1" value="<?php print $date1; ?>" />
	<input type="hidden" name="date2" id="date2" value="<?php print $date2; ?>" />
	<table id="tb-type-rapports" class="checklist ui-sortable">
		<thead>
			<tr>
				<th id="reports-type-name"><?php print _('Type') ?></th>
				<th id="reports-type-reports" class="align-right"><?php print _('Rapports') ?></th>
			</tr>
		</thead>
		<tbody><?php
			if( $rtype ){
				while( $type = ria_mysql_fetch_assoc($rtype) ){
					$report = rp_reports_get( 0, $type['id'], 0, $author, $date1, $date2 );
					$count_reports = $report ? ria_mysql_num_rows( $report ) : 0;
					print '
						<tr>
							<td headers="reports-type-name">
								<a href="/admin/fdv/reports/index.php?type='.$type['id'].'">'.htmlspecialchars( $type['name'] ).'</a>
							</td>
							<td headers="reports-type-reports" class="align-right">
								'.ria_number_format($count_reports).'
							</td>
						</tr>
					';
				}
			}
		?></tbody>
		<tfoot>
			<tr>
				<td class="align-left">
					<input type="submit" name="export" id="export" title="<?php print _('Exporter les rapports') ?>" value="<?php print _('Exporter les rapports') ?>" />
				</td>
				<td class="align-right">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_FDV_REPORT_ADD') ){ ?>
					<input type="submit" name="add" id="add" title="<?php print _('Ajouter un type de rapport')?>" value="<?php print _('Ajouter un type de rapport')?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<script>
<!--
	function load_reports(){
		window.location.href = 'index.php?type=<?php //print $_GET["type"]; ?>&date1=' + $('[name=date1]').val() + '&author=<?php //print $author; ?>&date2=' + $('[name=date2]').val();
	}
	<?php print view_date_initialized( 0, '', false, array('callback'=>'load_reports') ); ?>
-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
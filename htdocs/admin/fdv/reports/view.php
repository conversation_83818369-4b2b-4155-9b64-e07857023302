<?php

/**	\file view.php
 *	Cette page permet l'affichage d'un rapport de visite.
 *	Elle accepte les paramètres suivants :
 *	- _GET['rp'] : numérique, identifiant du rapport de visite à afficher
 */

require_once('reports.inc.php');
require_once('categories.inc.php');
require_once('products.inc.php');
require_once('cfg.variables.inc.php');

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_FDV_REPORT');

// L'identifiant du rapport est obligatoire pour afficher cet écran
if (!isset($_GET['rp']) || !is_numeric($_GET['rp']) || $_GET['rp'] <= 0) {
	header('Location: /admin/fdv/reports/index.php');
	exit;
}

// Chargement du rapport
$rreport = rp_reports_get($_GET['rp']);
if (!$rreport || !ria_mysql_num_rows($rreport)) {
	header('Location: /admin/fdv/reports/index.php');
	exit;
}

$report = ria_mysql_fetch_assoc($rreport);

// Gère l'enregistrement des modifications sur le rapport
if (isset($_POST['save']) && isset($_POST['type']) && isset($_POST['comments'])) {
	$upd = rp_reports_upd($_GET['rp'], $_POST['type'], $report['usr_id'], $report['author_id'], $_POST['comments']);
	if ($upd) {
		$success = _("Vos modifications ont correctement été enregistrées.");
		$report['type_id'] = intval($_POST['type']);
		$report['comments'] = $_POST['comments'];
	} else {
		$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de vos modifications.");
	}
}

// Charge et contrôle le type de rapport
$rtype = rp_types_get($report['type_id']);
if (!$rtype || !ria_mysql_num_rows($rtype)) {
	header('Location: /admin/fdv/reports/types/index.php');
	exit;
}

$type = ria_mysql_fetch_assoc($rtype);

// Bouton Supprimer
if (isset($_POST['delete'])) {
	if (rp_reports_del($_GET['rp'])) {
		header('Location: /admin/fdv/reports/index.php?type=' . $type['id']);
		exit;
	} else {
		$error = _("La suppression de ce rapport de visite a échoué pour une raison inconnue.");
	}
}

// Gère le bouton Annuler
if (isset($_POST['cancel'])) {
	header('Location: /admin/fdv/reports/index.php?type=' . $type['id']);
	exit;
}

// Action sur l'onglet "Avancés"
view_admin_tab_fields_actions(CLS_REPORT, $report['id'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']));

$r_author = array(
	'id' => $report['author_id'],
	'name' => $report['author_name'],
	'is_sync' => $report['author_is_sync'],
	'is_deleted' => $report['author_is_deleted']
);

$r_user = array(
	'id' => $report['usr_id'],
	'name' => $report['usr_name'],
	'is_sync' => $report['usr_is_sync'],
	'is_deleted' => $report['usr_is_deleted']
);

$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

$title = str_replace('#param[numero de rapport]#', $report['id'], _('Rapport n°#param[numero de rapport]#')) . ' - ' . htmlspecialchars($type['name']) . ' - ' . _('Rapports') . ' - Yuto';
define('ADMIN_PAGE_TITLE', $title);
require_once('admin/skin/header.inc.php');

// Note applicable par type de rapport
$rtype_notes = rp_report_notes_get($report['id']);

// Objets liés au rapport
$robject = rp_report_objects_get($report['id']);

$colspan = $author ? 2 : 3;

// Détermine l'onglet en cours de consultation
if (!isset($_GET['tab'])) $_GET['tab'] = '';
$tab = 'general';
if (isset($_POST['tabFields']) || $_GET['tab'] == 'fields') {
	$tab = 'fields';
}
?>

<h2><?php print htmlspecialchars(_('Rapport n°') . $report['id']); ?></h2>

<?php
if (isset($error)) {
	print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
} elseif (isset($success)) {
	print '<div class="error-success">' . nl2br(htmlspecialchars($success)) . '</div>';
} elseif (isset($notice)) {
	print '<div class="notice">' . nl2br($notice) . '</div>';
}
?>

<form action="view.php?rp=<?php print htmlspecialchars($report['id']); ?>" method="post" enctype="multipart/form-data">

	<?php if (view_admin_show_tab_fields(CLS_REPORT, $report['id'])) { ?>
		<ul class="tabstrip">
			<li><input type="submit" name="tabGeneral" value="<?php print _('Général') ?>" <?php if ($tab == 'general') print 'class="selected"'; ?> /></li>
			<?php if (view_admin_show_tab_fields(CLS_REPORT, $report['id'])) { ?>
				<li><input type="submit" name="tabFields" value="<?php print _('Avancé') ?>" <?php if ($tab == 'fields') print 'class="selected"'; ?> /></li>
			<?php } ?>
		</ul>
		<div id="tabpanel">
		<?php } ?>
		<?php if ($tab == 'general') { ?>
			<table class="checklist ui-sortable">
				<tbody>
					<tr>
						<th colspan="2"><?php print _('Informations générales'); ?></th>
					</tr>
					<tr>
						<td><label for="type"><?php print _('Type de rapport :'); ?></label></td>
						<td><select name="type" id="type"><?php
							$rtypes = rp_types_get();
							while ($t = ria_mysql_fetch_array($rtypes)) {
								print '<option value="' . $t['id'] . '"' . ($t['id'] == $report['type_id'] ? ' selected="selected"' : '') . '>' . htmlspecialchars($t['name']) . '</option>';
							}
							?></select></td>
					</tr>
					<tr>
						<td><?php print _('Auteur :'); ?></td>
						<td><?php
								print ($r_author['id'] > 0 && !$r_author['is_deleted'] ? '<a href="/admin/customers/edit.php?usr=' . $r_author['id'] . '">' : '') . '
						' . view_usr_is_sync($r_author) . ' ' . htmlspecialchars( $r_author['name'] ) . '
						' . ($r_author['id'] > 0 && !$r_author['is_deleted'] ? '</a>' : '');
								?></td>
					</tr>
					<tr>
						<td><?php print _('Client :'); ?></td>
						<td><?php
								print ($r_user['id'] > 0 && !$r_user['is_deleted'] ? '<a href="/admin/customers/edit.php?usr=' . $r_user['id'] . '">' : '') . '
						' . view_usr_is_sync($r_user) . ' ' . htmlspecialchars( $r_user['name'] ) . '
						' . ($r_user['id'] > 0 && !$r_user['is_deleted'] ? '</a>' : '');
								?></td>
					</tr>
					<tr>
						<td><?php print _('Créé le :'); ?></td>
						<td><?php print $report['date_created']; ?></td>
					</tr>
					<tr>
						<td><label for="comments"><?php print _('Commentaires :'); ?></label></td>
						<td><textarea name="comments" id="comments" rows="15" cols="40"><?php print trim($report['comments']) != '' ? htmlspecialchars($report['comments']) : ''; ?></textarea></td>
					</tr>
					<?php if ($rtype_notes && ria_mysql_num_rows($rtype_notes)) { ?>
						<tr>
							<th colspan="2"><?php print _('Notes') ?></th>
						</tr>

						<?php while ($type_notes = ria_mysql_fetch_assoc($rtype_notes)) { ?>
							<tr>
								<td><?php print htmlspecialchars($type_notes['note_name']); ?> :</td>
								<td><?php
										print $type_notes['value'] . '/10 ' . (trim($type_notes['comments']) != '' ? '<i>(' . htmlspecialchars($type_notes['comments']) . ')</i>' : '');
										?></td>
							</tr>
						<?php } ?>
					<?php } ?>

					<?php
					if ($robject && ria_mysql_num_rows($robject)) {
						$first = false;
						$last_cls_id = false;

						$objects = array();

						while ($object = ria_mysql_fetch_assoc($robject)) {
							$objects[$object["cls_id"]][] = $object;
						}

						foreach ($objects as $cls_id => $object) {


							print '
								<tr>
									<th colspan="2">' . fld_classes_get_name($cls_id) . '</th>
								</tr>
							';

							switch ($cls_id) {
								case CLS_CATEGORY:
									foreach ($object as $obj) {
										$rcat = prd_categories_get($obj['obj_id_0']);

										if ($rcat && ria_mysql_num_rows($rcat)) {
											$cat = ria_mysql_fetch_assoc($rcat);
											print '<tr><td colspan="2"><a href="/admin/catalog/index.php?cat=' . $cat['id'] . '">' . view_cat_is_sync($cat) . ' ' . htmlspecialchars($cat['name']) . '</a></td></tr>';
										}
									}
									break;
								case CLS_PRODUCT:
									foreach ($object as $obj) {
										$rprd = prd_products_get_simple($obj['obj_id_0']);

										if ($rprd && ria_mysql_num_rows($rprd)) {
											$prd = ria_mysql_fetch_assoc($rprd);
											if ($prd['ref']) {
												$prd['name'] = $prd['ref'] . ' - ' . $prd['name'];
											}
											print '<tr><td colspan="2"><a href="/admin/catalog/product.php?cat=0&prd=' . $prd['id'] . '">' . view_cat_is_sync($prd) . ' ' . htmlspecialchars($prd['name']) . '</a></td></tr>';
										}
									}
									break;
								case CLS_CHECKIN:

									$obj = $object[0];
									$rrck = rp_checkin_get($obj['obj_id_0']);
									if (!$rrck) {
										$tmp_html = '';
										continue;
									}
									$rck = ria_mysql_fetch_assoc($rrck);
									static $i = 0;

									// Affichage de la durée
									print '	<tr>
												<td>' . _('Durée :') . '</td>
												<td>' . convert_second_to_readable_delay(strtotime($rck["date_end_en"]) - strtotime($rck["date_start_en"])) . '</td>
											</tr>';

									// Affichage de la localisation sur une carte Google Maps (uniquement si renseigné)
									if ($rck['latitude'] != 0 || $rck['longitude'] != 0) {
										print '	<tr style="height:250px;">
												<td colspan="2" id="map' . $i . '"></td>
											</tr>';

										ob_start();
										?>

									<script>
										var signal_alert_class;

										function initMap() {
											var myLatlng = new google.maps.LatLng(<?php print $rck["latitude"] . ',' . $rck["longitude"] ?>);

											var mapOptions = {
												zoom: 11,
												center: myLatlng,
											};

											var googleMap = new google.maps.Map(document.getElementById('map<?php print $i; ?>'), mapOptions);

											var marker = new google.maps.Marker({
												icon: '/admin/images/fdv/icon_alive.png',
												position: myLatlng,
												title: ""
											});

											marker.setMap(googleMap);
										}
									</script>
									<?php
									$i++;
								}

								break;
						}
					}
				}

				?>
				</tbody>
				<tfoot>
					<tr>
						<td class="align-left col150px">
							<a href="/admin/fdv/reports/download.php?id=<?php print $report['id']; ?>" class="button" target="_blank"><?php print _('Imprimer'); ?></a>
						</td>
						<td class="align-left col-m">
							<button type="submit" name="save"><?php print _('Enregistrer'); ?></button>
							<button type="submit" name="cancel"><?php print _('Annuler'); ?></button>
							<button type="submit" name="delete" onclick="return confirmDel();"><?php print _('Supprimer'); ?></button>
						</td>
					</tr>
				</tfoot>
			</table>
		<?php } elseif ($tab == 'fields') {
			print view_admin_tab_fields(CLS_REPORT, $report['id'], $config['i18n_lng']);
		} ?>
		<?php if (view_admin_show_tab_fields(CLS_REPORT, $report['id'])) { ?>
		</div>
	<?php } ?>
</form>
<script>
	<!--
	function confirmDel() {
		return window.confirm("<?php print _("Vous êtes sur le point de supprimer ce rapport de visite."); ?>" + "\n" + "<?php print _("Cette opération est irréversible et ne pourra pas être annulée."); ?>" + "\n" + "<?php print _("Etes-vous sûr(e) de vouloir continuer ?"); ?>");
	}
	-->
</script>
<?php
require_once('admin/skin/footer.inc.php');
?>
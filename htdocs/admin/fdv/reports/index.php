<?php

	/**	\file index.php
	 *	Cette page affiche les statistiques sur les types de rapports, ou sur un type de rapport en particulier. 
	 *	Elle est composée d'un graphique et d'une liste sous forme de tableaux.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_REPORT');

	// Le type de rapport est un paramètre obligatoire pour afficher cette page
	if( !isset($_GET['type']) || !is_numeric($_GET['type']) || $_GET['type'] <= 0 ){
		header('Location: /admin/fdv/reports/types/index.php');
		exit;
	}

	// Gère l'export au format CSV/Excel
	if( isset($_REQUEST["export"]) ){
		header('Location: export.php?type='.$_REQUEST['type'].'&author='.$_POST['author']);
		exit;
	}

	require_once('reports.inc.php');
	require_once('strings.inc.php');

	// Chargement et contrôle du type de rapport demandé
	$rtype = rp_types_get( $_GET['type'] );
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		header('Location: /admin/fdv/reports/types/index.php');
		exit;
	}

	// Période
	// Variable pour la mise en place des périodes
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if ( isset($_GET["date1"], $_GET["date2"]) ) {
		$date1 = ria_mysql_escape_string(dateheureparse($_GET["date1"]));
		$date2 = ria_mysql_escape_string(dateheureparse($_GET["date2"]));
	}

	$type = ria_mysql_fetch_assoc( $rtype );

	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

	$rreport = rp_reports_get( 0, $_GET['type'], 0, $author, $date1, $date2);
	$count_report = $rreport ? ria_mysql_num_rows( $rreport ) : 0;

	// Calcule le nombre de pages
	$nb_per_page = 25;
	$pages = ceil($count_report / $nb_per_page);

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Rapports') . ' - '. $type['name'] . ' - ' . _('Rapports de visite') . ' - Yuto');
	require_once('admin/skin/header.inc.php');

	$colspan = $author ? 4 : 5;
?>

<h2>
<?php
print 'Rapports '.htmlspecialchars( $type['name'] ).' ('.ria_number_format($count_report).')';

if( gu_user_is_authorized('_RGH_ADMIN_FDV_REPORT_EDIT') ){
	print '<a href="/admin/fdv/reports/types/edit.php?type='.$type['id'].'" class="edit-cat">'._('Modifier ce type').'</a>';
}
?>
</h2>

<?php
	// Affiche le sélecteur d'Auteur de rapport
	$rall_author = rp_reports_get_all_author( $type['id'], array( 'adr_firstname' => 'asc', 'adr_lastname' => 'desc' ) );
	if( $rall_author && ria_mysql_num_rows($rall_author) ){
?>
		<div class="stats-menu">
			<div id="riadatepicker"></div>
			<div class="riapicker" id="select-fdv-report-author">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _('Auteur')?></span>
						<br/><span class="view"><?php
							if( $author <= 0 ){
								print _('Tous les auteurs');
							}else{
								$select_author 	= array( 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );

								$r_select_author = gu_users_get( $author );
								if( $r_select_author && ria_mysql_num_rows($r_select_author) ){
									$select_author = ria_mysql_fetch_assoc( $r_select_author );
								}

								print trim( $select_author['adr_firstname'].' '.$select_author['adr_lastname'].' '.$select_author['society'] );
							}
						?></span>
					</div>
					<a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" class="fleche" /></a>
					<div class="clear"></div>
				</div>
				<div class="selector" display="none">
					<a name="author-0"><?php print _('Tous les auteurs')?></a>
					<?php
						while( $all_author = ria_mysql_fetch_assoc($rall_author) ){
							print '<a name="author-'.$all_author['id'].'">'.htmlspecialchars( trim($all_author['firstname'].' '.$all_author['lastname'].' '.$all_author['society']) ).'</a>';
						}
					?>
				</div>
			</div>
			<div class="clear"></div>
		</div>
<?php } ?>

<?php
	// Graphique
	view_import_highcharts();
	require_once( 'admin/highcharts/graph-reports-global.php' );
?>

<form action="export.php?type=<?php print htmlspecialchars($_REQUEST['type']); ?>" method="post">

	<input type="hidden" name="author" value="<?php print $author; ?>" />
	<input type="hidden" name="date1" id="date1" value="<?php print $date1; ?>" />
	<input type="hidden" name="date2" id="date2" value="<?php print $date2; ?>" />
	<input type="hidden" name="type-id" id="type-id" value="<?php print $type['id']; ?>" />

	<table id="tb-reports-pres-produits" class="checklist ui-sortable">
		<thead>
			<tr>
				<th id="reports-id"><?php print _('Rapport n°')?></th>
				<?php if( !$author ){ ?>
					<th id="reports-author"><?php print _('Auteur')?></th>
				<?php } ?>
				<th id="reports-user"><?php print _('Client')?></th>
				<th id="reports-created"><?php print _('Créé le')?></th>
				<th id="reports-duration"><?php print _('Durée')?></th>
			</tr>
		</thead>
		<?php

			// Détermine la page en cours de consultation
			$page = 1;
			if( isset($_GET['page']) && is_numeric($_GET['page']) ){
				if( $_GET['page']>0 && $_GET['page']<=$pages )
					$page = $_GET['page'];
			}

			//Détermine les limites inférieures et supérieures pour l'affichage des pages
			$pmin = $page-5;
			if( $pmin<1 )
				$pmin = 1;
			$pmax = $pmin+9;
			if( $pmax>$pages )
				$pmax = $pages;
		?>
		<tbody><?php

			if( !$count_report ){
				print '
					<tr><td colspan="'.$colspan.'">'.sprintf(_('Aucun rapport n\'a encore été saisi%s'), ( $author ? _(' par cette personne') : '' )).'.</td></tr>
				';
			}elseif( ria_mysql_num_rows($rreport)>=($page-1)*$nb_per_page ){
				ria_mysql_data_seek( $rreport, ($page-1)*$nb_per_page );
				$lcount = 0;
				while( ($report = ria_mysql_fetch_assoc($rreport)) && $lcount<$nb_per_page ){
					// Récupération de la durée d'un rapport, durée => classe CLS_CHECK IN
					// On récupère les rapport avec un check in
					$object = rp_report_objects_get($report['id'],CLS_CHECKIN);
					if( ria_mysql_num_rows($object) > 0 ){
						$obj = ria_mysql_fetch_assoc($object);
						// On récupère la durée
						$rrck = rp_checkin_get( $obj['obj_id_0'] );
						$rck = ria_mysql_fetch_assoc($rrck);
						// On convertit le temps pour une meilleur lisibilité ( ex : time => x minutes )
						$duree = convert_second_to_readable_delay(strtotime($rck["date_end_en"])-strtotime($rck["date_start_en"]));
					}else{
						// si le rapport ne possède pas de check in
						$duree = _('Aucune durée');
					}

					print '
						<tr>
							<td headers="reports-id">
								<a href="/admin/fdv/reports/view.php?type='.$type['id'].'&amp;rp='.$report['id'].'">'.str_pad( $report['id'], '6', '0', STR_PAD_LEFT ).'</a>
							</td>
					';

					// La colonne Auteur n'apparaît que si le filtre Auteur est vide.
					if( !$author ){

						$a = array(
							'id' => $report['author_id'],
							'name' => $report['author_name'],
							'is_sync' => $report['author_is_sync'],
							'is_deleted' => $report['author_is_deleted']
						);

						print '
							<td headers="reports-author">
								'.( $a['id'] > 0 && !$a['is_deleted'] ? '<a href="/admin/customers/edit.php?usr='.$a['id'].'">' : '' ).'
								'.view_usr_is_sync( $a ).' '.htmlspecialchars( $a['name'] ).'
								'.( $a['id'] > 0 && !$a['is_deleted'] ? '</a>' : '' ).'
							</td>
						';
					}

					$u = array(
						'id' => $report['usr_id'],
						'name' => $report['usr_name'],
						'is_sync' => $report['usr_is_sync'],
						'is_deleted' => $report['usr_is_deleted']
					);

					print '
							<td headers="reports-user">
								'.( $u['id'] > 0 && !$u['is_deleted'] ? '<a href="/admin/customers/edit.php?usr='.$u['id'].'">' : '' ).'
								'.view_usr_is_sync( $u ).' '.htmlspecialchars( $u['name'] ).'
								'.( $u['id'] > 0 && !$u['is_deleted'] ? '</a>' : '' ).'
							</td>
							<td headers="reports-created">'.ria_date_format($report['date_created']).'</td>
							<td headers="reports-duration" data-label="' . _('Durée :') . ' ">'.$duree.'</td>
						</tr>
					';
					$lcount++;
				}
			}
		?></tbody>
		
		<tfoot>
			<?php if( $pages>1 ){ ?>
				<tr id="pagination">
					<td colspan="2" class="page align-left"><?php printf(_('Page %d/%d'), $page, $pages); ?></td>
					<td colspan="<?php print $colspan-2 ?>" class="pages">
						<?php
							if( $pages>1 ){
								if( $page>1 )
									print '<a href="index.php?type='.$_GET["type"].'&page='.($page-1).'">'._('&laquo; Page précédente').'</a> | ';
								for( $i=$pmin; $i<=$pmax; $i++ ){
									if( $i==$page )
										print '<b>'.$page.'</b>';
									else
										print '<a href="index.php?type='.$_GET["type"].'&page='.$i.'">'.$i.'</a>';
									if( $i<$pmax )
										print ' | ';
								}
								if( $page<$pages )
									print ' | <a href="index.php?type='.$_GET["type"].'&page='.($page+1).'">'._('Page suivante &raquo;').'</a>';
							}
						?>
					</td>
				</tr>
			<?php } ?>
			<tr>
			<?php 
			if( $count_report ){
				print '
					<td colspan="'.($colspan-1).'" class="align-left">
						<input type="submit" name="export" title="' . _('Exporter les rapports') . '" id="export" value="'._('Exporter').'" />
					</td>
				';
			} 
 			

 			// Possibilité d'ajouter un rapport
			print '
				<td '.(( $count_report )?'':'colspan="'. $colspan .'"').'" class="align-right">
					<a href="/admin/fdv/reports/edit.php?type=' . $type['id'] . '" title="' . _('Ajouter rapport') . '" class="button" name="add_report" id="add_report">' . _('Ajouter rapport') . '</a>
				</td>
			';
			?>
			</tr>
		</tfoot>
		
	</table>
</form>
<script>
<!--
	function load_reports(){
		window.location.href = 'index.php?type=<?php print $_GET["type"]; ?>&date1=' + $('[name=date1]').val() + '&author=<?php print $author; ?>&date2=' + $('[name=date2]').val();
	}
	<?php print view_date_initialized( 0, '', false, array('callback'=>'load_reports') ); ?>
-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>

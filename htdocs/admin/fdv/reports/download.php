<?php

/** \file download.php
 * Fichier permettant de télécharger un rapport de visite au format PDF.
 */

require_once('fpdf/fpdf.php');
require_once('reports.inc.php');
require_once('Pdf/ReportPdf.php');
define('EURO', chr(128));

if( empty($_GET['id']) ){
	header('HTTP/1.0 422 Unprocessable Entity');
	header('Location: /admin');
}

try {
	$pdf = new ReportPdf($_GET['id']);

	$pdf->build();

	$pdf->download();
} catch (Exception $e) {
	header('HTTP/1.0 422 Unprocessable Entity');
	header('Location: /admin');
}

<?php

	/**	\file popup-ajout-rapport.php
	 *	Ce fichier est utilisé pour l'ajout d'un rapport.
	 */
    $errors = "";
    $succes = false;
    $type_rp = 0;
    if(isset($_POST['add_report'])){ // Ajout du rapport

        //Verification des données obligatoire
        if( !isset($_POST['type']) || $_POST['type'] == 0 || !isset($_POST['report_customer']) || $_POST['report_customer'] == 0 ){
            //Récupération des valeur pour mettre à jour le formulaire si il y a une erreur
            $_GET['user'] = ( isset($_POST['report_customer']) ) ? $_POST['report_customer'] : 0;
            $type_rp = ( isset($_POST['type']) ) ? $_POST['type'] : 0;

            $errors = 'Des données obligatoire sont manquantes veuillez remplir, les champs obligatoire.';
        }

        if($errors == ""){
            $add_report = rp_reports_add( $_POST['type'], $_POST['report_customer'], $_SESSION['usr_id'], false, date('d/m/Y H:i:s'));
            if(!$add_report){
                $errors = 'Une erreur est survenue, l\'ajout du rapport n\'a pas pu ce faire';
                $_GET['user'] = $_POST['report_customer'];
            }else{
                $succes = true;
?>
            <script>
                window.parent.location.reload();
            </script>
<?php                
            }
        }
    }

    // Initialisation de la variable
    $usr  = ( isset($_GET['user']) && $_GET['user'] != 0 ) ? $_GET['user'] : 0;

    define('ADMIN_PAGE_TITLE', _('Export des rapports d\'appels') .' - '.view_admin_display_yuto_or_fdv());
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
    require_once('admin/skin/header.inc.php');
?>
<h3><?php print _('Ajout d\'un rapport'); ?></h3>
<?php
    if($errors){
        print '<div class="error">' . $errors . '</div>';
    }

    if($succes){
        print '<div class="success">' . _('Rapport ajouté') . '</div>';
    }
?>
<form action="/admin/fdv/reports/popup_ajout_rapport.php" method="POST">
    <table>
        <tbody>
            <tr>
                <td><?php print _('Client'); ?> <span class="mandatory">*</span></td>
                <td>
                    <input type="hidden" id="ord-user-duplicate" name="report_customer" value="<?php print $usr; ?>" />
<?php
                    // on gère l'affichage du client si un client est choisit
                    if( $usr != 0 ){ 
                        $r_user = gu_users_get($_GET['user']);
                        if ($r_user && ria_mysql_num_rows($r_user)){
                            $usr = ria_mysql_fetch_assoc($r_user);
                        }
                        
                        if( !$r_user ){
                            $ref = str_pad( $_GET['user'], 8, '0', STR_PAD_LEFT );
                            print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars( $ref ).'</span>';
                        }else{
                            if( !$usr['ref'] ){
                                $usr['ref'] = str_pad( $_GET['user'], 8, '0', STR_PAD_LEFT );
                            }
                            print '<a href="/admin/customers/edit.php?usr='.$_GET['user'].'" target="_blank">';
                            print view_usr_is_sync( $usr ).' '.htmlspecialchars( $usr['ref'] ).' '.htmlspecialchars( gu_users_get_name($_GET['user']) );
                            print '</a>  -  ';
                        }
                    }
?>                    
                    <a href="#" id="upd-ord-usr"><?php print ( $usr ? _('Modifier') : _('Sélectionner un compte client') ); ?></a>
                </td>
            </tr>
            <tr>
                <td><?php print _('Type'); ?> <span class="mandatory">*</span></td>
                <td>
                    <select name="type" id="type">
                        <option value=""><?php print _('Sélectionner un type</option>'); ?></option>
<?php
                    $rtype = rp_types_get();
                    if( $rtype ){
                        while( $type = ria_mysql_fetch_assoc($rtype) ){
                            print '<option value="' . $type['id'] . '" ' . ( ( $type_rp == $type['id'] ) ? 'selected' : '' ) . '>' . $type['name'] . '</option>';
                        }
                    }
?>                         
                    </select>
                </td>
            </tr>

        </tbody>
    </table>
    <button type="submit" name="add_report"><?php print _('Ajouter'); ?></button>
</form>


<script>
    $(document).ready(function(){
        
    }).delegate( '#upd-ord-usr', 'click', function(e){
        e.preventDefault();

        var ref_input = $("#ord-ref-duplicate").val();

        window.parent.displayPopup( 'Sélectionner un compte client', '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&add_type=1', '', 916, 557 );
    });
</script>

<?php
	require_once('admin/skin/footer.inc.php');
<?php
	/**	\file index.php
	 *	Cette page permet la mise à jour des variables de configuration de la solution Yuto
	 *	Elle affiche l'ensemble des paramètres éditables, classés par catégorie, et permet leur modification.
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_CONFIG');

	// Tableau de variable de configuration à ignorer
	$cfg_to_ignore = array('fdv_call_mdl','fdv_prd_sell_weight_default_col','fdv_prd_sell_weight_allow_decimal','fdv_ord_pipe_sign_delay_cols','fdv_ord_blocs_order');
	if( !gu_users_admin_rights_used('_MDL_ORDERS') ){
		array_push( $cfg_to_ignore, 'fdv_can_negotiate_price_unit', 'fdv_can_negotiate_price_discount', 'fdv_stats_only_seller_orders', 'fdv_goal_order', 'fdv_usr_locked_can_order', 
						'fdv_usr_prospect_ord_month', 'fdv_usr_show_stats', 'fdv_usr_turnover_start', 'fdv_usr_ord_states' );
	}
	if( !gu_users_admin_rights_used('_MDL_CATALOG') ){
		array_push( $cfg_to_ignore, 'fdv_show_product_stock_table' );
	}
	if( tnt_tenants_is_yuto_essentiel() ){
		array_push( $cfg_to_ignore, 'fdv_can_negotiate', 'fdv_can_create_user', 'fdv_can_only_edit_user', 'fdv_can_create_contact', 'fdv_usr_rib_edtable', 'fdv_usr_show_delayed', 'fdv_usr_show_models', 'fdv_usr_show_marge', 'fdv_usr_edit_models', 'fdv_usr_edit_prc', 'fdv_usr_locked_can_valid_order', 'fdv_usr_show_loans', 'fdv_usr_list_show_devis', 'fdv_usr_turnover_range_start', 'fdv_usr_turnover_range_end', 'fdv_can_negotiate', 'fdv_pvc_active' );
	}

	// Charge la liste des websites de type FDV/Yuto
	$rwst = wst_websites_get(0, false, null, false, _WST_TYPE_FDV);
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}
	$wst = ria_mysql_fetch_assoc($rwst);

	// Charge la liste des représentants
	$seller_id = $seller_usr_id = 0;
	if( isset($_GET['seller_id']) && $_GET['seller_id']>0 ){
		
		// Contrôle que l'utilisateur en cours ou demandé peut accéder à cette page
		$rusr = gu_users_get( 0, '', '', array( PRF_SELLER, PRF_ADMIN ), '', 0, '', false, false, $_GET['seller_id'] );
		if( !$rusr || !ria_mysql_num_rows($rusr) ){
			header('HTTP/1.0 403 Forbidden');
			exit;
		}

		$usr = ria_mysql_fetch_assoc($rusr);
		$seller_usr_id = $usr["id"];
		$seller_id = $usr["seller_id"];
		$_SESSION["ord_seller_id"] = $usr['seller_id'];
	}else{
		$_SESSION["ord_seller_id"] = false;
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Configuration') .' - Yuto');
	require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Configuration'); ?></h2>

<?php 
	// Gère l'enregistrement des variables de configuration
	if (isset($_POST["save_cfg"],$_POST["cfg_values"]) && $wst['id'] != "0") {

		foreach ($_POST["cfg_values"] as $cfg_key => $cfg_value) {
			if( in_array($cfg_key, $cfg_to_ignore)){
				continue;
			}

			if (is_array($cfg_value)) {
				$val = implode(",",array_keys($cfg_value));
			} else {
				$val = $cfg_value;
			}

			$rvar_def = cfg_variables_get( $cfg_key );
			$var_def = ria_mysql_fetch_assoc($rvar_def);

			if($seller_usr_id>0 && cfg_overrides_exists($cfg_key, $config["tnt_id"], $wst['id'], 0)){ // sur le website
				$default_var = cfg_overrides_get_value($cfg_key, $wst['id'], 0);
			}else if(cfg_overrides_exists($cfg_key, $config["tnt_id"], 0, 0)){ // sans website
				$default_var = cfg_overrides_get_value($cfg_key, 0, 0);
			}else{
				$default_var = $var_def["default"]; // valeur par défaut tout tenant
			}

			$val = trim($val);
			$default_var = trim($default_var);
			$is_equals =  $val == $default_var;

//print $cfg_key." = ".$val.':'.$default_var.':'."<br/>";
			// dans le cas ou le type du champs est selection l'égalité peut être à revoir 
			if( $var_def["type"] ==  FLD_TYPE_SELECT_MULTIPLE){
				$d = explode(',', $default_var);
				$v = explode(',', $val);

				$is_equals = sizeof(array_diff($d, $v))==0 && sizeof(array_diff($v, $d))==0;
			}

			if( $is_equals ){
				cfg_overrides_del_value($cfg_key, $wst['id'], $seller_usr_id); // suppression de la variable
			}else{
				if( $cfg_key=='fdv_sync_user_rule' && $config[$cfg_key] == $val ){
					continue;
				}
				
				cfg_overrides_set_value($cfg_key, $val, $wst['id'], $seller_usr_id);
				
			}

		}

		?>
		<div id="popup-content">
			<p class="notice"><?php print _('Les modifications ont bien été appliquées et seront effectives dans un délai maximum de 24H.'); ?></p>
		</div>
		<?php
	}
	
	// Récupération de toutes la variable avec le préfix "fdv_"
	$cfg_r = cfg_variables_get('', array(), '(fdv_|default_dps_id)');
	
	$cfg_all = array();
	$cfg_cat = '';

	// On trie dans un second sous-tableau multidimensionnel les variables par catégories qui suis le préfix fdv_ ( Ex: fdv_prd ) 
	while ($cfg_line = ria_mysql_fetch_assoc($cfg_r)) {
		
		$cfg_cat = explode("_", $cfg_line["code"]);
		$cfg_cat = $cfg_cat[1];

		if (array_key_exists($cfg_cat, $cfg_all )) {
			array_push($cfg_all[$cfg_cat],$cfg_line);
		}else{
			$cfg_all[$cfg_cat] = array();
			array_push($cfg_all[$cfg_cat],$cfg_line);
		}
	}

	ksort($cfg_all);

// Affiche le sélecteur de réprésentant, qui permet d'avoir des configurations différentes par représentant
print view_sellers_selector( false, true ); 

$form_action = '/admin/fdv/config/index.php';
if( $seller_id>0 ){
	$form_action .= '?seller_id='.$seller_id;
}
?>
<form action="<?php print $form_action; ?>" method="post">

	<br class="clear-both" />
	<table id="tb-ovr-usr" class="checklist">
		<thead>
			<tr>
				<th id="cls-check"><?php print _('Nom'); ?></th>
				<th id="cls-nb-fld"><?php print _('Valeur'); ?></th>
				<th id="cls-name"><?php print _('Description'); ?></th>
			</tr>
		</thead>
		<tbody><?php
			foreach( $cfg_all as $cfg_categorie => $values ){

				// Ne pas afficher les variable ignorées
				foreach( $values as $key => $value ){
					if( in_array(trim($value['code']), $cfg_to_ignore) || $value['name'] == 'DEPRECIE' ){
						unset($values[$key]);
					}
				}
					
				// Les catégories ne contenant aucune variable ne sont pas affichées
				if( sizeof($values)==0 ){
					continue;
				}

				// Les catégories qui ne sont pas adapté ne sont pas affichées
				if( !gu_users_admin_rights_used('_MDL_ORDERS') && in_array($cfg_categorie, array('ca', 'currency', 'dlv', 'dps', 'intervention', 'invoices', 'nego', 'ord')) ){
					continue;
				}
				if( !gu_users_admin_rights_used('_MDL_CATALOG') && in_array($cfg_categorie, array('cadencier', 'catalog', 'prd', 'restrictions', 'use')) ){
					continue;
				}
				if( tnt_tenants_is_yuto_essentiel() && in_array( $cfg_categorie, array('cms', 'doc', 'goal', 'user', 'workflow') )){
					continue;
				}
			
				// On parcours toutes les catégories
				switch( $cfg_categorie ){
					case 'login': print '<tr><th colspan="3">'._('Connexion').'</th></tr>'; break;
					case 'ord': print '<tr><th colspan="3">'._('Commandes').'</th></tr>'; break;
					case 'prd': print '<tr><th colspan="3">'._('Produits').'</th></tr>'; break;
					case 'usr':
					case 'user': print '<tr><th colspan="3">'._('Utilisateurs').'</th></tr>'; break;
					case 'can': print '<tr><th colspan="3">'._('Autorisations').'</th></tr>'; break;
					case 'doc': print '<tr><th colspan="3">'._('Documents').'</th></tr>'; break;
					case 'cadencier': print '<tr><th colspan="3">'._('Cadencier').'</th></tr>'; break;
					case 'catalog': print '<tr><th colspan="3">'._('Catalogue').'</th></tr>'; break;
					case 'show': print '<tr><th colspan="3">'._('Affichage').'</th></tr>'; break;
					case 'dps': print '<tr><th colspan="3">'._('Dépôts').'</th></tr>'; break;
					case 'cms': print '<tr><th colspan="3">'._('Gestion de contenu').'</th></tr>'; break;
					case 'ca': print '<tr><th colspan="3">'._('Chiffres d\'affaire').'</th></tr>'; break;
					case 'time': print '<tr><th colspan="3">'._('Temps').'</th></tr>'; break;
					case 'nego': print '<tr><th colspan="3">'._('Règles de négociations').'</th></tr>'; break;
					case 'workflow': print '<tr><th colspan="3">'._('Demandes de modérations').'</th></tr>'; break;
					case 'report': print '<tr><th colspan="3">'._('Rapports de visite').'</th></tr>'; break;
					case 'goal': print '<tr><th colspan="3">'._('Objectifs').'</th></tr>'; break;
					case 'call': print '<tr><th colspan="3">'._('Appels').'</th></tr>'; break;
					case 'palmares': print '<tr><th colspan="3">'._('Palmarès').'</th></tr>'; break;
					case 'raised': print '<tr><th colspan="3">'._('Relevé linéaire').'</th></tr>'; break;
					case 'dlv': print '<tr><th colspan="3">'._('Livraisons').'</th></tr>'; break;
					case 'search': print '<tr><th colspan="3">'._('Recherche').'</th></tr>'; break;
					case 'restrictions': print '<tr><th colspan="3">'._('Restrictions').'</th></tr>'; break;
					default: print '<tr><th colspan="3">'._('Divers').'</th></tr>'; break;
				}
				// Pour chaque catégorie, on parcours toutes les variables

				foreach( $values as $key => $value ){

					// Modification d'affichage pour les Riashop Yuto Essentiel
					if( tnt_tenants_is_yuto_essentiel() ){
						$value['desc'] = str_replace('fdv', 'Yuto Essentiel', $value['desc']);
					}
					
					$disabled = '';
				
					// Récupère l'existence d'une d'une valeur par défault de la variable
					$cfg_ovr_exist = false; // Indique si une overrides existe pour un code
					$seller_ovr = false; // Indique si une valeur est surahcgé pour un representant
					
					if($seller_usr_id>0 && cfg_overrides_exists($value["code"], $config["tnt_id"], $wst['id'], 0)){
						$default_var = cfg_overrides_get_value($value["code"], $wst['id'], 0);
					}else if( cfg_overrides_exists($value["code"], $config["tnt_id"], 0, 0)){
						$default_var = cfg_overrides_get_value($value["code"], 0, 0);
					}else{
						$rvar_def = cfg_variables_get( $value["code"] );
						$var_def = ria_mysql_fetch_assoc($rvar_def);
						$default_var = $var_def["default"];
					}

					//print $value["code"].'=>'.$default_var.'<br/>';

					if (cfg_overrides_exists($value["code"], $config["tnt_id"], $wst['id'], $seller_usr_id)) { // surcharge sur le site
						$cfg_ovr = cfg_overrides_get_value($value["code"],$wst['id'], $seller_usr_id);
						if($default_var != $cfg_ovr){
							$seller_ovr = true;
						}
						$cfg_ovr_exist = true;
						
					}else{
						$cfg_ovr = $default_var;
					}

					if($seller_ovr){
						print '<tr class="bg-green-color">';
					}else{
						print '<tr>';
					}
					
					if (isset($_GET["debug"]) && $_GET["debug"]) {
						print '<td>'.htmlspecialchars( $value["code"] ).'</td>';
					}else{
						print '<td>'.htmlspecialchars( $value["name"] ).'</td>';
					}
					
					
					print '	<td>
								<input type="hidden" name="cfg_values['.$value["code"].']" value="">';
					switch ($value["type"]) {
						
						case FLD_TYPE_TEXTAREA:

							print '<textarea '.$disabled.' name="cfg_values['.$value["code"].']">';
								if ($cfg_ovr_exist) {
									print $cfg_ovr;
								} else {
									print $default_var;
								}
							print '</textarea>';
							
							break;
							
						case FLD_TYPE_DATE:
							
							print '<div id="riadatepicker"></div>';

							if ($cfg_ovr_exist) {
								print '<input class="datepicker" onclick="show_selector()" type="date" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$cfg_ovr.'">';
							} else {
								print '<input class="datepicker" onclick="show_selector()" type="date" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$default_var.'" placeholder="'.$default_var.'">';
							}

						break;
						case FLD_TYPE_TEXT:
							
							switch ($value["code"]) {
							
								case 'fdv_cadencier_period':

									$period = array('day'=>_('Jour'),'week'=>_('Semaine'),'month'=>_('Mois'),'twomonth'=>_('2 mois'),'year'=>_('Année'));
									print '<select '.$disabled.' name="cfg_values['.$value["code"].']">';
										foreach ($period as $k => $v) {
											print '<option value="'.$k.'" ';
												print $cfg_ovr == $k ? 'selected' : '';
											print '>'.$v.'</option>';
										}

									print '</select>';
									
									break;
								
								default:
									if ($cfg_ovr_exist) {
										print '<input type="text" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$cfg_ovr.'" />';
									} else {
										print '<input type="text" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$default_var.'" placeholder="'.$default_var.'" />';
									}
									break;
							}

							break;

						case FLD_TYPE_INT:
						case FLD_TYPE_FLOAT: 
							
							switch ($value["code"]) {
								case 'fdv_cadencier_cat_root':

									$prd_cats = prd_categories_get( 0, true, 0);
									if ($prd_cats == false) {
										$prd_cats = cms_categories_get( 0,0);
									}
									if (ria_mysql_num_rows($prd_cats) > 0) {
										print '<select '.$disabled.' name="cfg_values['.$value["code"].']">
													<option value=""></option>';
											while ($cat = ria_mysql_fetch_assoc($prd_cats)) {
												print '<option value="'.$cat["id"].'"';
												if ($cat["id"] == $cfg_ovr ) {
													print ' selected ';
												}
												print '>'.htmlspecialchars( $cat["name"] ).'</option>';
											}	
												
										print '</select>';
									}else{
										print _('Aucune catégorie de gestion de contenu trouvée.');
									}

										
									break;
								case 'fdv_cms_root':

									$cms_cats = cms_categories_get( 0, $wst['id']);
									if ($cms_cats == false) {
										$cms_cats = cms_categories_get( 0,0);
									}
									if ($cms_cats && ria_mysql_num_rows($cms_cats) > 0) {
										print '<select '.$disabled.' name="cfg_values['.$value["code"].']">
													<option value="0"></option>';
											while ($cat = ria_mysql_fetch_assoc($cms_cats)) {
												print '<option value="'.$cat["id"].'"';
												if ($cat["id"] == $cfg_ovr ) {
													print ' selected ';
												}
												print '>'.htmlspecialchars( $cat["name"] ).'</option>';
											}	
												
										print '</select>';
									}else{
										print _('Aucune catégorie de gestion de contenu trouvée.');
									}

										
									break;
								
								case 'default_dps_id':

									$rdps = prd_deposits_get();	
									if ($rdps && ria_mysql_num_rows($rdps) > 0) {
										print '<select '.$disabled.' name="cfg_values['.$value["code"].']">
													<option value="0"></option>';
											while ($dps = ria_mysql_fetch_assoc($rdps)) {
												print '	<option value="'.$dps["id"].'"';
												if ($dps["id"] == $cfg_ovr ) {
													print ' selected ';
												}
												print '>'.htmlspecialchars( $dps["name"] ).'</option>';
											}	
												
										print '</select>';
									}else{
										print _('Aucun dépôt trouvé.');
									}

										
									break;
								
								default:
									if ($cfg_ovr_exist) {
										print '<input type="number" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$cfg_ovr.'">';
									} else {
										print '<input type="number" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$default_var.'" placeholder="'.$default_var.'">';
									}
									break;
							}

							break;

						case FLD_TYPE_SELECT_MULTIPLE:
							
							switch ($value["code"]) {


								case 'fdv_ord_mdl_filters':
								case 'fdv_prd_mdl_filters':
								case 'fdv_usr_mdl_filters':
								case 'fdv_cadencier_states':
								case 'fdv_ord_state_hide':
								case 'fdv_dps_allowed':
								case 'fdv_dps_excluded':
								case 'fdv_ord_rco_exclude':
								case 'fdv_usr_contacts_profiles':
								case 'fdv_prd_doc_type_exclude':
								case 'fdv_doc_types':
								case 'fdv_usr_show_models':
								case 'fdv_prd_show_models':
								case 'fdv_prd_list_mdl_show':
								case 'fdv_usr_ord_states':
								case 'fdv_usr_edit_models':
								case 'fdv_usr_contact_edit_models':
								case 'fdv_ord_edit_models':
								case 'fdv_prd_offers_mdl_filters':
								case 'fdv_intervention_edit_models':
								case 'fdv_intervention_edit_models_always_editable':
								case 'fdv_intervention_edit_models_readonly':
								case 'fdv_intervention_list_mdl_show':
								case 'fdv_ord_edit_models_always_editable':
								case 'fdv_ord_installment_pay_allowed':
								case 'fdv_linear_offers_edit_models':
								case 'fdv_prd_stock_agregate':

									if ($cfg_ovr_exist) {
										$cfg_ord_states = explode(",", $cfg_ovr);
									} else{
										$cfg_ord_states = explode(",", $default_var);
									}

									if ($value["code"] == 'fdv_cadencier_states' || $value["code"] == 'fdv_ord_state_hide' || $value["code"] == 'fdv_usr_ord_states') {
										$all_fld = ord_states_get();	
									} elseif ($value["code"] == 'fdv_dps_allowed' || $value["code"] == 'fdv_prd_stock_agregate' || $value["code"] == 'fdv_dps_excluded'){
										$all_fld = prd_deposits_get();	
									} elseif ($value["code"] == 'fdv_ord_rco_exclude'){
										$all_fld = gu_risk_code_get();
									} elseif ($value["code"] == 'fdv_usr_contacts_profiles'){
										$all_fld = gu_profiles_get();
									} elseif ($value["code"] == 'fdv_prd_doc_type_exclude'){
										$all_fld = doc_types_get();
									} elseif ($value["code"] == 'fdv_doc_types'){
										$all_fld = doc_types_get();
									} elseif ($value["code"] == 'fdv_ord_mdl_filters'){
										$all_fld = fld_models_get(0, 0, CLS_ORDER);
									} elseif ($value["code"] == 'fdv_prd_show_models' || $value["code"] == 'fdv_prd_list_mdl_show' || $value["code"] == 'fdv_prd_mdl_filters' || $value["code"] == 'fdv_prd_offers_mdl_filters'){
										$all_fld = fld_models_get(0, 0, CLS_PRODUCT);
									} elseif ($value["code"] == 'fdv_linear_offers_edit_models' ){
										$all_fld = fld_models_get(0, 0, CLS_LINEAR_OFFERS);
									} elseif ($value["code"] == 'fdv_usr_mdl_filters'){
										$all_fld = fld_models_get(0, 0, CLS_USER);
									} elseif ($value["code"] == 'fdv_usr_show_models' || $value["code"] == 'fdv_usr_edit_models' || $value["code"] == 'fdv_usr_contact_edit_models' ){
										$all_fld = fld_models_get(0, 0, CLS_USER);
									} elseif ($value["code"] == 'fdv_ord_edit_models_always_editable' || $value["code"] == 'fdv_ord_edit_models' || 
										$value["code"] == 'fdv_intervention_edit_models' || $value["code"] == 'fdv_intervention_edit_models_always_editable' || $value["code"] == 'fdv_intervention_edit_models_readonly' || $value["code"] == 'fdv_intervention_list_mdl_show' ){
										$all_fld = fld_models_get(0, 0, CLS_ORDER);
									} elseif ($value["code"] == 'fdv_ord_installment_pay_allowed' ){
										$all_fld = ord_payment_types_get();
									}
									
									if ($all_fld && ria_mysql_num_rows($all_fld) > 0) {
										while ($fld = ria_mysql_fetch_assoc($all_fld)) {

											print '<label><input ';
											if (array_search($fld["id"],$cfg_ord_states) !== false) {
												print 'checked="true" ';
											}
											print 'type="checkbox" '.$disabled.' name="cfg_values['.$value["code"].']['.$fld["id"].']"> '.htmlspecialchars( $fld["name"] ).'</label>
													<br>';
										}	
									}else{

										if ($value["code"] == 'fdv_cadencier_states' || $value["code"] == 'fdv_ord_state_hide') {
											print _('Aucun état de commande trouvé.');
										} elseif ($value["code"] == 'fdv_dps_allowed' || $value["code"] == 'fdv_prd_stock_agregate'){
											print _('Aucun dépôt trouvé.');
										} elseif ($value["code"] == 'fdv_ord_rco_exclude'){
											print _('Aucun code risque trouvé.');
										} elseif ($value["code"] == 'fdv_usr_contacts_profiles'){
											print _('Aucun profil utilisateur trouvé.');
										} elseif ($value["code"] == 'fdv_doc_types'){
											print _('Aucun type de documents trouvé.');
										} elseif ($value["code"] == 'fdv_usr_show_models' 
											|| $value["code"] == 'fdv_prd_show_models' 
											|| $value["code"] == 'fdv_usr_edit_models' 
											|| $value["code"] == 'fdv_ord_edit_models' ){
											print _('Aucun modèle trouvé.');
										}
									}
									break;

								case 'fdv_cadencier_cols': // Colonnes du cadencier. 3 sont personnalisables.
								
									if ($cfg_ovr_exist) {
										$cfg_cadencier_cols = explode(",", $cfg_ovr);
									} else{
										$cfg_cadencier_cols = explode(",", $default_var);
									}

									$aoc = array(
										'qte_n_2' => _('Période n-2'),
										'qte_n_1' => _('Période n-1'),
										'qte_n' => _('Période n'),
										'bl_qte_n_2' => _('Bon de livraison période n-2'),
										'bl_qte_n_1' => _('Bon de livraison période  n-1'),
										'bl_qte_n' => _('Bon de livraison période  n'),
										'price_last_sell' => _('Dernier prix de vente'),
										'price_base' => _('Prix de base'),
										'bl_last_qte' => _('Bon de livraison dernière quantité')
									);
									foreach ($aoc as $aoc_key => $aoc_value) {
										print '<label><input ';
										if (array_search($aoc_key,$cfg_cadencier_cols) !== false) {
											print 'checked="checked" ';
										}
										print 'type="checkbox" '.$disabled.' name="cfg_values[fdv_cadencier_cols]['.$aoc_key.']"> '.$aoc_value.'</label>
												<br>';
										
									}
									break;
								case 'fdv_ord_installment_numbers': // nombre d'échéance dispo
								
									if ($cfg_ovr_exist) {
										$cfg_cadencier_cols = explode(",", $cfg_ovr);
									} else{
										$cfg_cadencier_cols = explode(",", $default_var);
									}

									$aoc = array(
										'1' => _('1 fois'),
										'2' => _('2 fois'),
										'3' => _('3 fois'),
										'4' => _('4 fois'),
										'5' => _('5 fois'),
										'6' => _('6 fois'),
										'8' => _('8 fois'),
										'10' => _('10 fois'),
									);
									foreach ($aoc as $aoc_key => $aoc_value) {
										print '<label><input ';
										if (array_search($aoc_key,$cfg_cadencier_cols) !== false) {
											print 'checked="checked" ';
										}
										print 'type="checkbox" '.$disabled.' name="cfg_values[fdv_ord_installment_numbers]['.$aoc_key.']"> '.$aoc_value.'</label>
												<br>';
										
									}
									break;
								case 'fdv_ord_tva_allowed': 
								
									if ($cfg_ovr_exist) {
										$cfg_tva_code = explode(",", $cfg_ovr);
									} else{
										$cfg_tva_code = explode(",", $default_var);
									}
									foreach( $cfg_tva_code as $k => $c ){
										if( is_numeric($c) ){
											$cfg_tva_code[$k] = number_format($c, 4);
										}
									}

									$tvas = prd_tvas_get();
									while ($tva = ria_mysql_fetch_assoc($tvas)) {
										$rate = $tva["rate"];

										print '<label><input ';
										if (array_search($rate,$cfg_tva_code) !== false) {
											print 'checked="checked" ';
										}
										print 'type="checkbox" '.$disabled.' name="cfg_values[fdv_ord_tva_allowed]['.$rate.']"> '.number_format($tva["name"],2).' %</label>
											<br>';
									}	
									
									break;
								case 'fdv_negotiate_price_discount_allowed': 
								case 'fdv_ord_blocs_order': 
								
									if ($cfg_ovr_exist) {
										print '<input type="text" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$cfg_ovr.'">';
									} else {
										print '<input type="text" '.$disabled.' name="cfg_values['.$value["code"].']" value="'.$default_var.'" placeholder="'.$default_var.'">';
									}
									
									break;
							}
							break;

						case FLD_TYPE_BOOLEAN_YES_NO: // Variables de type Booléen
							
							if ($cfg_ovr_exist) {
								$cfg_bool = $cfg_ovr;
							} else {
								$cfg_bool = $default_var;
							}
							
							if ($cfg_bool == true || $cfg_bool == "true" || $cfg_bool == 1) {
								print '	<label><input '.$disabled.' type="radio" checked="checked" name="cfg_values['.$value["code"].']" value="1" id=""> '._('Oui').'</label> ';
								print '	<label><input '.$disabled.' type="radio" name="cfg_values['.$value["code"].']" value="0"> '._('Non').'</label>';
							} else {
								print '	<label><input '.$disabled.' type="radio" name="cfg_values['.$value["code"].']" value="1" id=""> '._('Oui').'</label>
										<label><input '.$disabled.' type="radio" checked="checked" name="cfg_values['.$value["code"].']" value="0"> '._('Non').'</label>';
							}
							
							
							break;

						default:
							print _('Aucune variable de configuration trouvée.');
							break;
					}
					print '</td>';

					// colonnes description
					print '<td>'.nl2br($value["desc"]).'</td>';
				}
			}
		?></tbody> 
		<tfoot>
			<tr>
				<td colspan="3">
					<input type="hidden" name="save_cfg">
					<input type="submit" title="<?php print _('Sauvegarder les configurations'); ?>" value="<?php print _('Enregistrer'); ?>" class="btn-save" name="save" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<script>
<!--
	function loadDatePicker(){
		// Parcours tous les champs de type date
		$('input.datepicker').each(function(){
			var temp = this;
			// Implémente le sélecteur de date sur chacun d'entre eux.
			$(this).DatePicker({
				
				date: $(this).val(),
				current: $(this).val(),
				starts: 1,
				onChange: function(formated, dates){
					var date = $(temp).val();
					if(dates != 'Invalid Date'){
						var old_val = $(temp).val();
						$(temp).val(formated);
						if(old_val!=formated) 
							$(temp).DatePickerHide();
					}
				}
			});
		});
	}
	
	$(document).ready(
		loadDatePicker
	);
//-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>

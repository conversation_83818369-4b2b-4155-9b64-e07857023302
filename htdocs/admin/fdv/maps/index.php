<?php 

/**	\file index.php
 *
 *	Cet écran est chargé d'afficher l'ensemble des suspects, prospects et clients sur une carte intéractive Google Maps. Il est l'équivalent du module "Cartographie"
 *	dans l'application Yuto.
 *	
 */

gu_if_authorized_else_403('_RGH_ADMIN_FDV_MAP');


require_once('users.inc.php');

define('ADMIN_PAGE_TITLE', _('Cartographie') . ' - Yuto');
require_once('admin/skin/header.inc.php');

// Charge la liste des clients et prospects particuliers et professionnels
$rcustomers = gu_users_get( 0, '', '', array( 2, 3 ) );
$customers = array();
while( $customer = ria_mysql_fetch_assoc($rcustomers) ){
	
	if( $customer['longitude']!=0 || $customer['latitude']!=0 ){
		if( !$customer['is_locked'] ){
			$customers[] = array(
				'id' => $customer['id'],
				'ref' => $customer['ref'],
				'name' => $customer['society'] ? $customer['society'] : $customer['title_name'].' '.$customer['adr_firstname'].' '.$customer['adr_lastname'],
				'zipcode' => $customer['zipcode'],
				'city' => $customer['city'],
				'latitude' => $customer['latitude'],
				'longitude' => $customer['longitude']
			);
		}
	}
}

 ?>
	<style type="text/css">
	<!--
		#site-content {
			position:relative;

		}
		#map {
			right:0;
			top:100px;
			bottom:0;
			left:0;
			position:absolute;
		 }
	//-->
	</style>
		
	<h2><?php print _('Cartographie des clients et prospects'); ?></h2>
	<?php 
		if( isset($config['fdv_location_active']) && !$config['fdv_location_active'] ){
			?>
			<div class="error"><?php print _('La géolocalisation des appareils est désactivée, il est donc impossible de consulter cette carte.'); ?></div>
			<?php
		}else{
	?>

    <div id="map"></div>
    <script>
	<!--
     	var customers = <?php print str_replace('\'', '\\\'', json_encode($customers)); ?>;
	 	var signal_alert_class;

	 	function initMap() {
			
			var googleMap = new google.maps.Map(document.getElementById('map'));

			var zoneMarqueurs = new google.maps.LatLngBounds();
			
			var marqueurs = new Array();
			var infowindow = new google.maps.InfoWindow({
				content: msgLoading
			});

			for( var i = 0, I = customers.length; i < I; i++ ) {

				var customer = customers[i];
				var latitude = customer["latitude"],								
					longitude = customer["longitude"];	
				
				if( longitude!=null && latitude!=null ){

					var optionsMarqueur = {
						map: googleMap,
						position: new google.maps.LatLng( latitude, longitude ),
						icon:'/admin/images/fdv/icon_alive.png',
					};
					signal_alert_class = "alive";

					var marqueur = new google.maps.Marker( optionsMarqueur );
					marqueur.html = '<div id="content">'+
										'<a href="/admin/customers/edit.php?usr='+customer['id']+'"><h2>'+customer['name']+'</h2></a>'+
										'<ul style="list-style-type:none;">'+
											'<li><abbr title="Référence">Réf</abbr> : ' + customer['ref'] + '</li>' +
											'<li>' + customer['zipcode'] + ' ' + customer['city'] + '</li>' +
											'<li><a href="/admin/customers/edit.php?usr='+customer['id']+'">Plus...</a></li>'+
										'</ul>'+
							      	'</div>';

					google.maps.event.addListener(marqueur, 'click', function () {
						infowindow.setContent(this.html);
						infowindow.open(map, this);
					});

					zoneMarqueurs.extend( marqueur.getPosition() );
					
					marqueurs.push( marqueur );
				}
			}

			// Add a marker clusterer to manage the markers.
			var markerCluster = new MarkerClusterer( googleMap, marqueurs, {imagePath: '/admin/images/gmaps/m'} );
			
			googleMap.fitBounds( zoneMarqueurs );
	 	}
	//-->
    </script>
	<script src="/admin/js/gmaps/markerclusterer.js"></script>

	<?php }
 		require_once('admin/skin/footer.inc.php');
 	?>

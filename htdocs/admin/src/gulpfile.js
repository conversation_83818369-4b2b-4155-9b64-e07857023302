var gulp        = require('gulp');
var sass        = require('gulp-sass');
var postcss     = require('gulp-postcss');
var rename      = require('gulp-rename');
var concat      = require('gulp-concat');

var destCss = '../css';

gulp.task('sass', function() {
    return gulp.src("sass/*.scss")
        .pipe(sass({
            outputStyle: 'compact',
            includePaths: [
                '.',
                'node_modules'
                ]
            }).on('error', sass.logError))
        .pipe(postcss([ require('autoprefixer')]))
        .pipe(gulp.dest(destCss))
});

gulp.task('opti-login',['sass'], function(){
    return gulp.src([destCss+"/login-v1.css",destCss+"/css/animate.css" ])
        .pipe(concat('login-opti.css')) // On réunit les deux fichiers login-v1.css et animate.css et on le nomme login-opti.css
        .pipe(rename({
            suffix: '.min'
        }))
        .pipe(gulp.dest(destCss));
});

gulp.task('default', ['sass', 'opti-login']);

gulp.task('watch', function () {
    gulp.watch("sass/**/*.scss", ['sass', 'opti-login']);
});


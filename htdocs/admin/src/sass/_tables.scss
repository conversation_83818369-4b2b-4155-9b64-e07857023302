/**
 * CSS des tableaux en général
 */

/* Coupe le contenu de la colonne url pour chrome */
td[headers="bnr-url"] {
	/* These are technically the same, but use both */
  overflow-wrap: break-word;
  word-wrap: break-word;

  -ms-word-break: break-all;
  /* This is the dangerous one in WebKit, as it breaks things wherever */
  word-break: break-all;
  /* Instead use this non-standard one: */
  word-break: break-word;
}

#site-content, #popup-content {
    .datepickerContainer table {
        width: auto !important;
        min-width: 0;
    }
    table#table-synthese-order, table#table-synthese-search {
        margin-bottom: 8px;
    }
    table:not(textarea.tinymce + span.mceEditor) {
        border: 1px solid $grey-medium-color;
        margin-bottom: 15px;
        clear: both;
        border-spacing : 0;
        border-collapse : collapse;
        @include media('>=medium') {
            min-width: 625px;
        }
        table {
            margin-top: 15px;
        }
        th {
            text-align: left;
        }
        #desc-long_parent table, .toolBarGroup table, .datepickerViewDays, #ord-products-articles {
            min-width: 0 !important;
        }
        &.ria-admin-ui-overview {
            margin-bottom: 0;
        }

        caption {
            background-color: $dark-color;
            color: white;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            white-space: nowrap;
            img {
                background-color: #fff;
	            padding: 1px;
            }
        }
        thead, th, th.thead {
            background-color: $light-color;
            color: $dark-color;
            font-size: 13px;
        }
        
        thead a, tfoot, th a {
            color: $dark-color;
        }
        
        thead, th, th.thead, tfoot {
            font-weight: 600;
        }
        td, th, caption, tfoot {
            padding: 5px 0 5px 10px;
        }
        caption {
            padding-right: 10px;
        }
        thead {
            &:not(input) {
                text-align: left; 
            }
            #select {
                width: 25px;
            }
        }
        tbody {
            th {
                background: $light-color;
            }
            tr {
                &:last-child{
                    td:not(.mceLast) {
                        border-bottom: none;
                    }
                }
            } 

        }
        tr {
            vertical-align: top;
        }
        td:last-child, th:not(.th-customers):last-child{
            padding-right: 10px;
        }
        tfoot {
            border-top: 1px solid $grey-medium-color;
            padding: 0;
            text-align: right;
            &.bg-grey {
                background-color: $grey-color;
                
            }
            .tfoot-grey{
                text-align: left;
                background-color: $grey-color;
                padding: 5px 10px;
            }
            input {
                &.float-left {
                    margin-right: 3px;
                }
                &.float-right {
                    margin-left: 3px;
                }
            }
        }
        fieldset {
            padding: 5px 10px !important;
            /* min-width: 590px; */
            max-width: 100%;
            /* padding: 10px; */
            margin-top: 10px;
            /*&:not(.cdt-grp) div.cdt-psy-val {
                max-width: 180px;
            }*/
            legend {
                font-weight: bold;
                margin-left: 10px;
            }
            input[type=text] {
                vertical-align: baseline !important;
            }
            label {
                margin-right: 3px;
            }
            div.cdt-config {
                display: flex;
                flex-wrap: wrap;
                position: relative;
                .cdt-grp-list-cdt {
                    display: flex;
                    flex-direction: column;
                    .cdt-grp-message {
                        display: flex;
                        flex-wrap: wrap;
                        width: 100%;
                    }
                }
                div.cdt-psy-val {
                    flex: 1 1 auto;
                    flex-direction: column;
                    position: relative;
                    margin-left: 5px;
                    margin-right: 5px;
                    // display: none;
                    
                    select.cdt-psy {
                        width: 200px;
                    }
                    .cdt-icon-del, .cdt-grp-del, .input-icon-del {
                        display: inline-block;
                        position: absolute;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                    .cdt-same-qte input {
                        display: inline;
                        margin-left: 0;
                    }
                    .cdt-calculated select.cdt-calculated-cdt {
                        margin-left: 5px;
                    }
                }
                .div-cdt-value {
                    flex: 1 1 auto;
                    flex-direction: column;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    padding-right: 30px;
                    position: relative !important;
                    input.input-icon-del{
                        position: absolute !important;
                        right: 0;
                        width: 23px !important;
                    }
                    .cdt-form-bool {
                        height: 100%;
                        display: flex;
                        align-items: center;
                    }
                }
                .cdt-select-second {
                    .div-website-cdt {
                        display: flex;
                        align-items: center;
                    }
                    .div-fields-cdt {
                        display: flex;
                        align-items: center;
                    }
                }
                input.input-icon-del{
                    position: absolute !important;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 0;
                    width: 23px !important;
                }
            }
            .table-goupe-limites {
                width: 100%;
                border: none;
                margin-bottom: 0;
                padding: 5px 0 5px 10px;
                tfoot {
                    border-top: 1px solid $grey-medium-color;
                }
                /* tbody {
                    vertical-align: top;
                    text-align: left;
                    tr:nth-child(2n+0){
                        background-color:$grey-color;
                    }
                    td.td-cdt-config {
                        display: flex;
                        justify-content: space-between;
                        .cdt-grp-cdt {
                            width: 230px !important;
                        }
                        .cdt-psy-val
                        {
                            display: inline;
                            label {
                                text-align: left !important;
                            }
                        }
                        .cdt-grp-del {
                            align-self: center;
                        }
                    }
                } */
            }
    
        }
        /* Classe pour avoir un sous entête de couleur */ 
        .th-head-second {
            th, td {
                background-color: $bg-second-thead-color;
            }
        } 
        .head-second th { 
            background-color: $bg-second-thead-color;
        }
    }
    #table-fiche-classe {
        @include media('>=large') {
            min-width: 768px;
        }
        @include media('>=xlarge') { 
            min-width: 1024px;
        }
        td:first-child {
            width: 20%;
        }
        td:last-child {
            width: 80%;
        }
        textarea {
            width: 100%;
            height: auto;
        }
    }
}

#site-content {
    table:not(.tb-kpi):not(#prf-rights):not(#tb-day-exp):not(.orders-customers) {
        &.w-600 {
            width: 600px;
            max-width: 600px;
            @include media('<=large') {
                width: 100%;
            }
            tbody {
                tr {
                    width: auto;
                    @include media('<=medium') {
                        width: 100%;
                    }
                    td {
                        &:first-child {
                            width: 180px;
                        }
                        #naf {
                            // width: 60px;
                            width: 145px;
                        }
                        #taxcode{
                            width: 145px;
                        }
                        @include media('<=medium') {
                            width: 100%;
                        }
                    }
                    @include media('<=medium') {
                        th {
                            width: 100%;
                        }
                    }
                }
            }
        }

        /* RESPONSIVE */
        
        @include media('<medium') {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            max-width: 100%;
            td, th{
                &[data-label]::before {
                    margin-right: 3px;
                }
            }
        }

        caption, tbody:not(.datepickerYears):not(.datepickerMonths):not(.datepickerDays):not(.datepickerWeek):not(.datepickerNotInMonth):not(.datepickerSaturday):not(.datepickerSunday), thead, tfoot {
            @include media('<medium') {
                width: 100%;
                display: block;
            }
        }

        caption {order: 0;}
        thead {order: 1;}
        tbody {order: 2;}
        tfoot {order: 3;}

        tr {
            @include media('<medium') {
                display: flex;
                flex-wrap: wrap;
                th {
                    display: flex;
                    flex-wrap: wrap;
                    width: 100%;
                }
                td {
                    display: block;
                    width: 100%;
                    border-right: 0;
                    padding-right: 10px;
                    &:last-child {
                        border-bottom: 0;
                    }
                }
            }
        }
        @include media('<=medium') {
            &.datepickerViewDays tr {
                flex-wrap: nowrap;
            }
            &#tb-synthese-order {
                border: 0;
            }
        }
        
    }
    .datepickerContainer table {
        display: table !important;
        max-width: 100%;
        thead {
            display: table-header-group !important;
        }
        tr {
            display: table-row !important;
        }
        th, td {
            display: table-cell !important;
            padding-right: 0 !important;
        }

    }
    #tb-goals{
        
        @include media('<=medium') {
            display: table !important;
            max-width: 100%;
            
        }
        @include media('<=medium') {
            tr {
                width: 100%;
                display: block !important;
                th {
                    width: 100%;
                }
            }
        }
    }
}
#popup-content {
    table {
        margin-bottom: 5px;
    }
}
/* class checklist */ 
@include media('<large') {
    #site-content .checklist ,
    #site-content .fidelity {
        width: 100% !important;
        tbody {
            select, input, textarea {
                max-width: 100%;
                width: 100%;
                box-sizing: border-box;
            }
        }
        th,
        td,
        tr,
        caption {
            display: block;
            width: 100%;
        }
        thead tr th {
            &.thead-none {
                display: none;
                width: 100% !important;
            }
        }
        thead tr.head-filter th{
            display: table-cell;
        }
        tr:not(:last-child) {
            border-bottom: 1px solid $grey-medium-color;
            padding: 0;
        }
        td {
            padding: 5px 10px;
            border-bottom: 0 none !important;
            overflow: hidden;
            text-align: left;
        }
        .align-center, .align-right, .number {
            text-align: left !important;
        }
        td, th{
            &[data-label]::before {
                content: attr(data-label);
            }
        }
    }
    /* Mettre ici les tableaux checklist qui doivent responsive qu'en dessous de medium */ 
    #site-content #tab-stock.checklist ,
    #site-content .fidelity {
        thead {
            display: table-header-group;
        }
        tr {
            display: table-row;
        }
        th,
        td {
            display: table-cell;
        }
        caption {
            display: table-caption;
        }
        @include media('<medium') {
            th,
            td,
            tr,
            caption {
                display: block;
            }
            thead {
                display: none;
            }
        }
    }
}
@include media('<medium') {
    #popup-content {
        width: 100% !important;
        table {
            tbody {
                select, input, textarea {
                    max-width: 100%;
                    width: 100%;
                    box-sizing: border-box;
                }
                
            }
            th,
            td,
            tr,
            caption {
                display: block;
                width: 100%;
            }
            thead {
                display: none;
            }
            tr:not(:last-child) {
                border-bottom: 1px solid $grey-medium-color;
                padding: 0;
            }
            td {
                padding: 5px 5px;
                border-bottom: 0 none !important;
                overflow: hidden;
                text-align: left;
            }
            tfoot td {
                display: flex;
                flex-wrap: wrap;
                & * {
                    margin-right: 2px;
                }
            }        
            &:not(.checklist) tr {
                border-bottom: 0 none !important;
            }
            .fidelity tr:not(:last-child) ,
            .checklist tr:not(:last-child) {
                border-bottom: 1px solid $grey-medium-color !important;
            }
            td, th{
                &[data-label]::before {
                    content: attr(data-label);
                }
            }
            .thead-none {
                display: none;
            }
            &#table-languages {
                display: table !important;
                max-width: 100%;
                tr {
                    width: 100%;
                    display: table-row;
                    th, td {
                        display: table-cell;
                    }
                    td:first-child {
                        width: 20px;
                    }
                }
            }
        }
        
    }

    table {
        .thead-none {
            display: none !important;
        }
    }
}

@include media('<medium') {
    #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > th, 
    #site-content #tabpanel > table:not(#prf-rights) > tbody > tr > td, 
    #site-content #tabpanel > table > tbody > tr,
    #site-content table:not(#prf-rights)[summary] > tbody > tr > th, 
    #site-content table:not(#prf-rights)[summary] > tbody > tr > td, 
    #site-content table:not(#prf-rights)[summary] > tbody > tr,
    #site-content #tabpanel > table:not(#prf-rights) > tfoot > tr > th, 
    #site-content #tabpanel > table > tfoot > tr > td, 
    #site-content #tabpanel > table > tfoot > tr, 
    #site-content table[summary] > tfoot > tr > th, 
    #site-content table[summary] > tfoot > tr > td, 
    #site-content table[summary] > tfoot > tr{
        width: 100%;
        box-sizing: border-box;
    }
}

/* TinyMCE - tableaux */ 
textarea.tinymce {
    width: 100%;
}
textarea.tinymce + span.mceEditor {
    table {
        /* Entête avec toolbar */ 
        tr.mceFirst {
            td {
                &.mceToolbar {
                    width: 100% !important;
                    table {
                        height: 100%;
                    }
                }
            }
        }
        
        tr {
            @include media('<medium') {
                display: flex;
                flex-wrap: wrap;
                th {
                    width: auto !important;
                }
                th, td {
                    display: table-cell !important;
                    &.mceIframeContainer {
                        border-right: 1px solid #949494 !important;
                    }
                }td {
                    width: auto !important;
                    &.mceIframeContainer:last-child {
                        border-bottom: 1px solid #949494 !important;
                    }
                }
                td.mceIframeContainer {
                    width: 100% !important;
                }
            }
        }
    }
    .desc-long_tbl {
        width: 100%
    }
    .mceLayout {
        max-width: 100%;
        @include media('<medium') {
            height: auto !important;
        }
    }
}

#popup-content table {
    th, td {
        /* display: table-cell; */
        vertical-align: inherit; 
    }
}

/* Contient le titre de la table et les filtres/options/tri */
.table-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Tableau Jours et heures d'expédition dans Yuto > "Horaires d'activité" et Configuration > "Horaires d'expédition" */
#tb-day-exp {
    &.tb-day-exp2, &.tb-day-exp1 {
        max-width: 1060px;
        width: 1060px;
        overflow-y: auto;
        thead {
            #period-exp {
                text-align: center;
            }
        }
        tbody {
            td {
                border-bottom: 1px solid $grey-color;
                height: 35px;
                vertical-align: middle;
        
                &.reinit{
                    text-align: center;
                }
                &.period{
                    color: $dark-color;
                    font-size: 10px;
                    text-align: center;
                }
                div.hr-active,
                div.hr-inactive {
                    float: left;
                    height: 25px;
                    margin-left: 1px;
                    width: 20px;
                }
                div.hr-inactive{
                    background-image: url("/admin/images/expeditions/horaires_no.svg");
                    
                }
                div.hr-active{
                    background-image: url("/admin/images/expeditions/horaires_yes.svg");
                    &:hover {
                        cursor: pointer;
                    }
                }
            }
        }
        input.hr-check{
            display:none;
        }
    }
}

// ombre à droite et à gauche d'un tableau, lorsqu'il a un scroll horizontal
.container-shadow{
    width: 100%;
    // height: 72px;
    position: relative;
    &.container-border{
        border: 1px solid $grey-medium-color;
    }
    .bar-shadow {
        position: absolute;
        width: 20px;
        height: calc(100% - 16px); 
        top: 0;
        z-index: 2;
        &.shadow-left {
            left: 0;
            box-shadow: 15px 0px 10px -10px rgba(0, 0, 0, 0.25) inset;
        }
        &.shadow-right {
            right: 0;
            box-shadow: -15px 0px 10px -10px rgba(0, 0, 0, 0.25) inset;
        }
    }
}
.table-cols-changed {
    margin-bottom: -2px;
    width: 100%;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 1;

}    


#foot-products{
    tbody, tfoot{
        border-top: 0 !important;
    }
    .tfoot-grey {
        * {
            vertical-align: middle;
        }
        label:not([for="sort-prd-inherited"]) {
            display: inline-block;
            width: 100%;
            max-width: 345px;
        }
        select {
            width: 226px;
        }

    }
}

#tb-holidays #th-year th {
    background: $dark-color;
    color: $white;
    border-color: $dark-color; 

}

div.hr-inactive:hover{
    cursor:pointer;
}

#tb-redirection tbody img.edit-url {
    border: medium none;
    cursor: pointer;
    width: 20px;
    margin-bottom: 3px;
}

#site-content {
    .table-layout-large {
        @include media('<=xlarge') {
            overflow-x: auto;
        }

        /* RESPONSIVE */
        table {
            @include media('<=medium') {
                display: table;
            }

            caption, tbody, thead, tfoot {
                @include media('<=medium') {
                    width: 100%;
                }
            }
            tr {
                @include media('<=medium') {
                    display: table-row;
                }
            }
        }
    }
    @include media('<=large') {
        #tb-holidays {
            & > tbody > tr {
                border-bottom: 1px solid $grey-medium-color;
                & > td:last-child {
                    display: flex;
                    justify-content: flex-end;
                    height: 100%;
                    flex-direction: column;
                    border-bottom: none;
                    label {
                        padding: 3px;
                        width: 100%;

                        input { margin-right: 5px; vertical-align: middle; }
                    }
                }
                
            }
        }
    }
    /* Pour afficher les noms des colonnes en responsive */ 
    @include media('<large') {
        td, th{
            &[data-label]::before {
                content: attr(data-label);
                vertical-align: middle;
            }
        }
        .thead-none {
            display: none;
        }
    }
    
}

@include media('<large') {
    #site-content, #popup-content {
        table.table-responsive, 
        table.table-responsive.checklist, 
        table.table-responsive thead, 
        table.table-responsive tbody, 
        table.table-responsive tr, 
        table.table-responsive th, 
        table.table-responsive td {
            display: block;
        }

        table.table-responsive thead {
            display: none;
        }
        table.table-responsive tfoot {
            display: flex;
            flex-direction: column;
            padding: 5px;

            & > tr:first-child {
                display: flex;
                flex-wrap: wrap;
            }
        }

        table.table-responsive tbody td ,
        table.table-responsive.checklist tbody td[headers="select"] ,
        table.table-responsive.checklist tbody td[headers="desc"] ,
        table.table-responsive.checklist tbody td[headers="ref"] {
            padding: 5px 5px 5px 150px;
            width: 100%;
            position: relative;
            margin-top: -1px;
            background: $white;
        }

        table.table-responsive tbody td:nth-child(odd) {
            background-color: $grey-color;
        }

        table.table-responsive tbody td::before {
            padding: 10px;
            content: attr(data-label);
            position: absolute;
            top: 0;
            left: 0;
            width: 100px;
            bottom: 0;
            background-color: $light-color;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        table.table-responsive tbody tr {
            /* margin-bottom: 1rem; */
            border-top: 1px solid $grey-medium-color; 
        }

        /* .table-responsive th + td {
            padding-left: 10px;
        } */
    }
}

#site-content .authorizations tr.odd {
    background-color: $grey-color;
    td {
        background: none;
    }
}

#table-une-commande tr {
    vertical-align: top;
}
#fields-classe-objects {
    width: 625px;
}

/* Tableau des champs avancés */ 
.tb-champs-avances {
    .tr-champs { 
        &:not(:first-child) {
            border-top: 1px solid $grey-medium-color;
        }
        .modele-saisie {
            margin-top: 4px;
        }
        .del-link {
            margin-right: 5px;
        }
        @include media('<medium') {
            th {
                display: table-cell !important;
            }
        }
    }
    .bg-ghostwhite {
        background-color: ghostwhite !important;
    }
}

/* Ajout d'une ligne lorsqu'il faut séparer 2 tr dans un tfoot */ 
.ftoot-border-top {
    border-top: 1px solid $grey-medium-color;
}


/* Tableaux de synthèse */ 
#table-synthese-order {
    width: 100%;
    thead {
        #hd-rewards-total, #hd-rewards-used {
            width: 150px;
        }
        #hd-rewards-no-used {
            width: 180px;
        }
        #hd-order-ht, #hd-order-ttc {
            width: 200px;
        }
        #hd-order-total {
            width: 130px;
        }
        #hd-order-conversion {
            width: 130px;
        }
        #hd-order-prds {
            width: 160px;
        }
        #hd-order-margin {
            width: 150px;
        }
        #hd-order-avg-ht {
            width: 120px;
        }
        #hd-order-avg-ttc {
            width: 130px;
        }
        .loader {
            width: 20px;
            height: 20px;
        }
    }
    tbody {
        td {
            padding-bottom: 5px;
        }
        img.loader{
            width: 20px;
            height: 20px;
        }
    }
}

#site-content {
    #table-synthese-order, #table-synthese-search {
        th, td {
            border-right: 1px solid $grey-medium-color;
            padding-left: 10px;
            padding-right: 10px !important;
        }
        thead tr th {
            background-color: $white;
            font-weight: 500;
            text-align: left;
        }
        tbody tr td{
            border-bottom: 1px solid $grey-medium-color;
            font-size: 1.4em;
            font-weight: 600;
            white-space: nowrap;
            @include media('>=large','<1200px') {
                font-size: 1.1em;
            }
        }
    }
}

#site-content table#table-edit-personnalisation {
    tbody th {
        background-color: $dark-color;
        color: $white;
    }
    textarea {
        resize: none;
    }
}


/**
    Tablesorter
**/
#site-content .tablesorter {
    th > a {
        position: relative;
        padding-right: 15px;

        &:after {
            position: absolute;
            top: 50%;
            right: 0;
            margin-top: -6px;
        }
    }    
}

/* adatpe les notices à la taille des  tableaux */ 
.block-notice-container {
    display: flex;
    flex-direction: column;
    width: auto !important;
    * {
        max-width: 100%;
    }
    .notice {
        display: inline-block;
    }

    /* taille max lorsque le contenu de la notice est plus grand que le tableau */
    &.relations-container {
        max-width : 896px;
    }
    &.referencement-container {
        max-width: 820px;
    }
}
  
/* taille des colonnes */ 
.col1, .col3pourcents {
	width: 3%;
}
.col2, .col63pourcents5 {
	width: 63.5%;
}
.col3, .col33pourcents5 {
	width: 33.5%;
}
.col4, .col97pourcents {
	width: 97%;
}
.col20px {
    width: 20px !important;
    max-width: 20px !important;
}
.col-check {
    width: 25px;
}
th.checkall {
    width: 30px;
}
.col-sync {
    width: 28px;
}
.col40px {
    width: 40px !important;
}
.col60px {
    width: 60px !important;
}
.col70px {
    width: 70px;
}
.col75px {
    width: 75px;
}
.col80px {
    width: 80px;
}
.col90 {
    width: 90px;
}
.col100px {
    width: 100px !important;
}
.col105px {
    width: 105px !important;
}
.col110px {
    width: 110px !important;
}
.col120px {
    width: 120px !important;
}
.col125px {
    width: 125px !important;
}
.col130px {
    width: 130px;
}
.col140px {
    width: 140px !important;
}
.col145px {
    width: 145px;
}
.col150px {
    width: 150px;
}
.col170px {
    width: 170px;
}
.col180px {
    width: 180px;
}
.col200px {
    width: 200px !important;
}
.col230px {
    width: 230px !important;
}
.col300px {
    max-width: 300px;
}
.colMax300px {
    max-width: 300px !important;
}
.col308px {
	width: 308px;
}
.col335 {
	width: 335px;
}
.col-m {
	width: 460px;
}
.col-big-content {
	width: 620px;
}
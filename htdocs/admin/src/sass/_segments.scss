/**
 * CSS de la page Clients > Segments 
 */
 
/* Tableau de Segmentation */
#obj-seg {
    #seg-del {
        width: 25px;
    }
    #seg-name, #seg-desc, #seg-objects {
        width: 200px;
    }
    #type-pos {
        width: 75px;
    }
    @include media('<large') {
        #seg-del {
            width: 100% !important;
        }
        .centertd {
            text-align: left !important;
        }
    }
}

/* Tableau de Création d'un nouveau Segmentation */
#table-creation-new-segment {
    #td-creat-new-segment {
        width: 140px;
    }
}

#form-segment .riapicker .selectorview {
    height: 17px;
    padding: 2px;
    padding-right: 0;
    border: solid rgb(132, 132, 132) 1px;
    width: 266px;
    height: 24px;
    border-radius: 6px;
    background-image: url('/admin/dist/images/input-select.svg');
    background-position: right -1px top -4px;
    background-repeat: no-repeat;
    background-size: 29px 29px;
    appearance: none;
    padding-right: 30px !important;
    &::-ms-expand {
        display: none;
		}
}
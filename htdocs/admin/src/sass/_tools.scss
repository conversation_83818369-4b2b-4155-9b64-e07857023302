/**
 * CSS de l'onglet Outils
 */

/* Actualités */
form #table-tools-news {
    max-width: 830px;
    width: 100%;
    #news-sel {
        width: 25px;
    }
    #news-name {
        width: 425px;
    }
    #news-pub-date {
        width: 75px;
    }
    #news-pub {
        width: 125px;
    }
    #news-pos {
        width: 120px;
    }
}

#actu_reviews {
    width: 830px;
    input.submit{
        width: auto;
    }
    textarea{
        height: 30px;
        width: 100%;
        font-family: Verdana, Arial;
        font-size:1em;
    }
    .cresp, .cdesc{
        margin: 5px 0;
    }
    .button{
        text-align: right;
    }
}
#table-categories-actualites {
    width: 600px;
    #cat-sel {
        width: 25px;
    }
    #cat-name {
        width: 425px;
    }
    #news-count {
        width: 70px;
    }
}

#class {
    thead{
        th:first-child {
            width: 25px;
        }
        #name, #nb-suscribers {
            width: 200px;
        }
        #type {
            width: 250px;
        }
    } 
}

#table-list-newsletter {
    #sel {
        width: 25px;
    }
    #email-th, #tel-th {
        width: 250px;
    }
    #account, #cat {
        width: 125px;
    }
}

#popup-content #documents {
    margin: 4px 0 !important; 

}

/* Campagne marketing */ 

#table-campagnes-sms {
    #cpg-id {
        width: 25px;
    }
    #cpg-start, #cpg-end, #cpg-send {
        width: 150px;
    }
}

#table-stats-details-messages {
    width: 600px;
    #hphone, #hdate {
        width: 150px;
    }
    #husr {
        width: 290px;
    }
}

.sms-add-rule-user {
    .rwd-add-rule-label {
        display: inline-block;
        width: 130px;
    }
    input[type="text"] {
        max-width: 100%;
    }
}

.marketing {
    .cpg_message {
        margin: 10px;
    }
    .pmt-rules-buttons, .btn-action-small {
        text-align: right;
    }
    .pmt-rules-buttons {
        margin-top: 10px;
    }
    a.btn, a.btn:hover{
        color: buttontext;
        font-size: 11px;
        text-decoration: none;
    }
    .sms-spe-rules {
        text-align: left;
        border: none;
        padding: 5px;
    }
    table {
        .sms-rules {
            width: 100%;
        }
        textarea {
            &#msg{
                height: auto;
                width: 482px;
            }
            &#description{
                height: auto;
            }
        }
    } 
    .mention{
        font-size: 9px;
    }
    #period {
        width: 222px;
    }
    .is_marketing{
        display: block;
        max-width: 485px;
        input {
            vertical-align: middle;
        }
        span{
            vertical-align: middle;
        }
    }
    .period-picker {
        display: inline;
    }
    .period-hide{
        display: none;
    }
    .period-detail{
        width: 39px;
    }
    .btn {
        cursor: pointer;
    }
    .sent-0 {
        color: $red;
    }
    .sent-1 {
	color: $green;
}
    .notice {
        margin-bottom: 0;
        margin-top: 5px;
    }
}

/* Alertes de disponibilités */ 

#tb-alert-dispo {
    max-width: 100%;

    #sel {
        width: 25px;
    }
    #prd-name, #brd-name {
        width: auto;
    }
    #account, #last_date, #date, #restocking {
        width: 100px;
    }
    #brd-name {
        min-width: 75px;
    }
    #restocking {
        min-width: 79px;
    }
    #last_date {
        min-width: 60px;
    }
    #date {
        min-width: 85px;
    }
    @include media('<=xlarge') {
        [headers="email"] {
            max-width: 80px !important;
        }
        [headers="prd-name"] {
            max-width: 108px !important;
        }
        [headers="email"],
        [headers="prd-name"] {
            overflow-wrap: break-word;
        }
    }
}

#faq_categories {
    #faq-sel {
        width: 25px;
    }
	#faq-name {
        width: 400px;
    }
    #faq-qst {
        width: 50px;
    }
    #faq-qst-pub {
        width: 150px;
    }
}

#table-faq-categorie-general {
    width: 100%;
    tbody {
        tr:first-child td:first-child:not(.mceFirst) {
            width: 120px;
        }
    }
}

#faq_questions {
    #qst-sel {
        width: 25px;
    }
    #qst-name {
        width: 400px;
    }
    #qst-pub {
        width: 60px;
    }
}

/* Bannières et Zones d'actions */
#table-banners, #table-zones-actions {
    #bnr-sel {
        width: 25px;
    }
    #bnr-name {
        width: 325px;
    }
    #bnr-url {
        width: 225px;
    }
    #bnr-from, #bnr-to, #bnr-emp {
        width: 125px;
    }
    #bnr-pos {
        width: 120px;
    }
    .bg-color-green {
        background-color: $bg-green-color;
    }
    .bg-color-blue {
        background-color: $bg-blue-color;
    }
}

#table-modif-banners, #table-zones-edit {
    tbody td:first-child {
        width: 130px;
    }
}

#table-zones-edit {
    input[type=text]#date_from, input[type=text]#date_to, select#plc {
        width: auto !important;
    }
}

.bnr-preview img {
    max-width: 750px;
    width: 100%;
	height: auto;
}

/* Gestion de contenu */ 

div#name-contenu {
    border-bottom: solid 1px #c0c0c0;
    width: 530px;
    margin-bottom: 10px;
    h3 {
        display: inline;
        border: none;
    }
    div.ria-cell-move {
        float: right;
    }
    div:last-child {
        clear: right;
    }
}
#table-gestion-contenu, #cms-categories {
    thead th {
        &:first-child {
            width: 25px;
        }
        &:nth-child(2) {
            width: 400px;
        }
        &:nth-child(3) {
            width: 75px;
        }
    } 
}

/* CMS, glossaires, configuration de l'import */ 
#editcms, #table-glossary-def, #table-glossary-edit, #mapping-general {
    td.tdw-150 {
        width: 150px;
    }
}

#tb-redirection {
    width: 100%;
    max-width: 820px;
	margin-top: 10px;
    #url {
        width: 705px;
    }
    #action {
        width: 50px;
    }
    img.edit-url {
        width: 25px;
        height: 25px;
    }
    td {
        padding: 5px 10px;
        vertical-align: middle;
        &.td-url {
            padding-right: 10px;
            border-right: 1px solid $grey-medium-color;
            @include media('<medium') {
                border-right: 0;
            }
            input[type="text"] {
                width: 100% !important;
                max-width: 100% !important;
                margin-right: 10px;
            }
        }
    }
}

#tb-redirection, #tb-closing {
    .td-action{
        text-align: center;
        width: 110px;
        @include media('<large') {
            word-break: initial;
        }
    }
} 

#div-select-type-modif {
    width: 775px;
    padding-bottom: 5px;
    select {
        float: right;
    }
    div:last-child {
        clear: right;
    }
}

#table-liste-versions {
    #cms-ver-sel {
        width: 25px;
    }
    #cms-ver-date {
        width: 350px;
    }
    #cms-ver-type {
        width: 175px;
    }
    #cms-ver-user {
        width: 100px;
    }
    #cms-ver-up {
        width: 125px;
    }
    tbody#tbody-rev td.rev-td table td{
        &:first-child {
            width: 25px;
        }  
        &:nth-child(2) {
            width: 375px;
        } 
        &:nth-child(3) {
            width: 150px;
            text-align: center;
        }
        &:nth-child(4) {
            width: 100px;
            text-align: center;
        }
        &:last-child {
            width: 125px;
            text-align: center;
        }
    }
    tfoot {
        tr#pagination td:first-child{
            text-align: left;
        }
    }
}
.npad { 
    padding: 1px 0px 0px 0px !important; 
}
div.rev-form {
    border-bottom: 1px solid $grey-medium-color;
    background: #F3F3F3;
    #table-type-modif {
        float: right;
        #label-vertical-align {
            vertical-align: top;
        }
    }
    div:last-child {
        clear: right;
    }
    table {
        margin: 0px;
        border: none !important;
    }
}

table.checklist .rev-form tbody td {
	border: none;
}
tfoot .rev-form {
    td {
        text-align: left;
    }
    textarea {
        width: 100%;
    }
} 

#footer-no-npad {
    padding: 5px 10px;
    text-align: left;
}

form#formulaire_cms .notice {
    max-width: 820px;
}

/* Erratums */ 

#table-erratums {
    width: 800px;
    #err-sel {
        width: 25px;
    }
    #err-ref {
        width: 50px;
    }
    #err-name {
        width: 225px;
    }
    #err-desc {
        width: 325px;
    }
    #err-date {
        width: 100px;
    }
}

/* Glossaires */
#table-glossary-def, #table-glossaire {
    width: 90%;
    @include media('<large') {
        width: 100% !important;
    }   
}
#table-glossaire {
    .alphabet-list {
        word-spacing: 0.5rem;
    }
    th:first-child {
        width: 25px;
    }
    #name, #name_pl {
        width: 145px;
    }
    #etat {
        width: 55px;
    }
    #date {
        width: 150px;
    }
    tfoot td {
        input.float-left {
            margin-right: 3px;
        }
    }
} 

/* Outils > Parrainage / Points de fidélité > Configuration */
#site-content form#form-rewards ul.tabstrip li:first-child{ 
    width: 78px !important;
}
#tb-tabConfig {
    tbody td {
        &:first-child {
            width: 275px;  
        } 
        &:last-child {
            width: 495px;  
        }
        #rwd-products-pt td {
            &:first-child {
                width: 25px;
            }
            
            &:last-child {
                width: 160px;  
            }
        }
    }
    input {
        vertical-align: middle !important;
    }
    input.ratio {
        width: 65px !important;
    }
    select {
        width: auto !important;
    }
    input.val-param {
        float: left;
    }
    a.help {
        background-image: url("/admin/images/questionmark.svg");
        display: block;
        float: left;
        height: 16px;
        margin-left: 5px;
        width: 16px;
    }
    [type='text'] + a.help {
        margin-top: 6px;
    }
} 

#tb-tabActions {
    #actions {
        width: 250px;
    }
    #desc {
        width: 400px;
    }
    #rwa-params {
        width: 275px;
    }
}

#tb-tabSponsors {
    tr.tr-filleul-reduc td{
        &:first-child {
            width: 210px;
        }
        &:last-child {
            width: 400px;
        }
    }
}

#rwd-products-pt{
	width: 100%;
    td{
        vertical-align: center;
    }
    @include media('>=medium') {
        .thead-none{
            display: table-cell !important;
        }
    }
    @include media('<medium') {
        .align-right {
            text-align: left !important;
        }
    }
}
/* Statistiques */ 
#stats-rewards {
    margin-top: 10px;
    #hdate{
        width: 80px;
    } 
    #hlimit {
        width: 180px;
    }
    #husr {
        width: 290px;
    }
    #hpts {
        width: 170px;
    }
    #hconvert {
        width: 70px;
    }

    tr {
        &.positive {
            background-color: $positive;
            &:hover {
                background-color: $positive-hover !important;
            }
        }
        &.negative {
            background-color: $negative;
            &:hover {
                background-color: $negative-hover !important;
            }
        }
    }
    .stgodchild {
        margin-top: 5px;
    }
    .bold {
        font-weight: 600;
    }
    @include media('<large') {
        .align-center {
            text-align: left !important;
        }
    }
}

/* Imports */ 
#table-imports {
    thead th {
        &:first-child {
            width: 25px;
        }
        &:nth-child(2) {
            width: 300px;
        }
        &:nth-child(3) {
            width: 100px;
        }
        &:nth-child(4), &:nth-child(6) {
            width: 200px;
        }
        &:nth-child(5) {
            width: 150px;
        }
    }
}

/* Imports */
#table-imports-unfinished{
    thead th {
        &:first-child {
            width: 25px;
        }
        &:nth-child(2) {
            width: 300px;
        }
        &:nth-child(3) {
            width: 300px;
        }
    }
}

/* Imports */
// #table-map-file {
//     thead th{
//         &:nth-child(-n+2) {
//             width: 300px;
//         }
//     }
// }
.tools-import {
    .error {
        ul {
            margin-top: 0;
            li:only-child {
                list-style: none;
            }
        }
    }
    .imp-form {
        // .imp-form-fields {
        //     label {
        //         width: 360px;
        //         max-width: 100%;
        //         display: inline-block;
        //         vertical-align: middle;
        //         &.check-label {
        //             width: auto;
        //         }
        //     }
        //     select {
        //         width: 256px;
        //     }
        // }
        span, select, input[type="password"] {
            vertical-align: middle;
        }
    }
}

#imp-form {
    width: 100%; 
    max-width: 100% !important;
    table-layout: fixed;
    tr {
        @include media('<=medium') {
            display: flex;
            flex-direction: column;
        }
        th.header {
            & > * {
                display: block;
                position: relative;
                padding-right: 14px;
                &::after {
                    position: absolute;
                    right: 3px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
            &.th-proprietaire .tablesorter-header-inner{
                min-width: 95px;
            }
            &.th-execution .tablesorter-header-inner{
                min-width: 100px;
            }
            &.th-etat .tablesorter-header-inner{
                min-width: 90px;
            }
        }
        input[type="text"]:read-only {
            background-color: transparent;
            border : 0;
            &:focus {
                border : 0;
                box-shadow: none;
            }
        }
    } 
    td {
        &.align-right, &.align-center {
            @include media('<=medium') {
                text-align: left !important;
            }
        }
        &.name-file {
            word-wrap: break-word;  
            hyphens: auto;
        }
    } 
}

// import > Configuration de l'import
input[name="imp-separator-other-text"],
#imp-separator-other-text {
    vertical-align: middle;
}
#imp-separator-other-text,
input[name="imp-separator-other-text"],
#imp-text-separator {
	width: 50px;
}

/* export */
#table-finished-export {
    @include media("<large"){
        .align-center {
            text-align: left !important;
        }
    }
}
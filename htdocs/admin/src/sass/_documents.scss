/**
 * CSS des tableaux de la Médiathèque
 */

#type-docs {
    #type-sel {
        width: 25px;
    }
    #type-name, #type-desc {
        width: 175px;
    }
    #type-documents {
        width: 100px;
    }
    #type-pos-2 {
        width: 75px;
    }
}

#documents {
    width: 100%;
    max-width: 860px;
    #doc-sel {
        width: 25px;
    }
    #doc-file {
        width: 225px;
    }
    #doc-remp {
        width: 300px;
    }
    #type-pos {
        width: 75px;
    }
    a:hover {
        text-decoration: none !important;
    }
}
#site-content #documents * {
    vertical-align: middle;
}

#table-objets-associes {
    width: 465px;
    #obj-sel {
        width: 12px;
    }
    #obj-name {
        width: 450px;
    }
}

#lst-downloads {
    .th-lst-dl-150 {
        width: 150px;
    }
    .th-lst-dl-250 {
        width: 250px;
    }
    .th-lst-dl-100 {
        width: 100px;
    }
}

.seg-obj-infos {
    .del-obj-seg {
        width: 16px;
        height: 13px;
    }
}

#table-hosts {
    th {
        width: 100px;
        &:first-child {
            width: 150px;
        }
    }
}

form#table-list-hosts {
    table {
        width: 390px !important;
    }
}

#table-edit-hosts {
    #td-edit-hosts-150 {
        width: 150px;
    }
    #td-edit-hosts-350 {
        width: 350px;
    }
    #list-channels {
        .del-obj-seg {
            width: 16px;
            height: 13px;
        }
    }
}

/* Images */ 
.name-links input{
    margin-top: 10px !important; 
}
.img-detail #list-img{
    padding: 10px 32px;
    margin-top: 30px;
    position: relative;
    .prev-img, .next-img {
        position: absolute;
        top:10px;
        left: 0px;
        height: 150px;
        width: 32px;
        a {
            background-image: url("/admin/images/media/before.gif");
            background-position: center center;
            background-repeat: no-repeat;
            display: block;
            height: 150px;
        }
    }
    .next-img{	
        left: auto;
        right: 0px;
        a{
            background-image: url("/admin/images/media/after_inactive.svg");
            &:hover {
                background-image: url("/admin/images/media/after_hover.svg");
            }
        }
    }
    .prev-img a {
        background-image: url("/admin/images/media/before_inactive.svg");
        &:hover {
            background-image: url("/admin/images/media/before_hover.svg");
        }
    }
}

#img-infos img {
    min-width: 260px;
    min-height: 260px;
}

.name-img {
    word-wrap: break-word;
}

#infos-img {
    .img-media {
        border: 1px solid #DDDDDD;
        float: left;
        height: 154px;
        margin-right: 10px;
        padding: 2px;
        img {
            border: medium none;
            display: block;
        }
    }
}

/* Non utilisés */ 
.img-images {
    list-style: none;
    display: inline-block;
    margin: 10px 5px;
    height: 156px;
    width: 156px;
    .input-del-item {
        margin-bottom: 3px;
    }
    a {
        height: 150px;
        width: 150px;
        display: block;
        border: 1px solid $grey-color;
        img {
            padding: 2px;
        }
    }
} 
img.item-deleted {
    border: 1px solid $medium-light-color;
    min-width: 150px;
    min-height: 150px;
}
.input-del-item {
    display: none !important;
}

#file + span.color-red {
    vertical-align: middle;
}

.error-file {
    margin-top: 10px;
    color: red;
}

/* Une image */ 
#media img {
    margin-bottom: 5px;
}

#new-media {
    border: 1px solid $grey-color;
    margin-top: 10px;
    padding: 10px;
    width: 300px;
    display: none;
    position: absolute;
    box-shadow: 0 0 5px $grey-color;
    background: $white;
    z-index: 1;
}
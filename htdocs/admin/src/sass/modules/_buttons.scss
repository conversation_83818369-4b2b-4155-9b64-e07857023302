//Réglages de certains boutons cas particuliers
.edit-cat {
	position: relative;
    display: inline-block;
    font-size: 13px;
    margin-left: 5px;
    padding: 0 0 0 20px;
    font-weight: 500;
    text-decoration: none;
    vertical-align: middle;
    line-height: 20px;
    height: 20px;
    &:before {
    	content: '';
    	position: absolute;
    	top: 50%;
    	left: 0;
    	width: 20px;
    	height: 20px;
    	margin-top: -10px;
    	background-image: url(/admin/dist/images/configuration_off.svg);

    }
}

.with-cols-options {
    position: relative;
    #display-cols-options.edit-cat {
        padding: 0 0 0 24px;
        line-height: 16px;

        &::before {
            height: 16px;
            margin-top: -8px;
            background-image: url(/admin/dist/images/columns.svg);
        }
    }
}

.menu-cols {
    .td-engrenage & {
        display: none;
    }
    .cols {
        position: absolute;
        font-size: 13px;
        background-color: $white;
        border: 1px solid $grey-medium-color;
        height: 300px;
        overflow-y: scroll;
        padding: 2px;
        z-index: 99;
    }
    .with-cols-options & .cols {
        right: 0;
        top: 20px;
    }
    .col {
        clear: both;
        color: $black;
        display: block;
        font-weight: 500;
        margin: 5px;
        position: static;
    }
    .separate {
        border-bottom: 1px solid $grey-medium-color;
        height: 1px;
        margin: 10px auto;
        width: 87%;
    }
}
/**
 * CSS des tableaux des promotions
 */

/* Tableaux de Promotions > Promotions sur les produits */ 

/* Tableau Promotions */
#pmt-cdt {
    position: relative;
    max-width: 1266px;
    .button-new-promotion {
        position: absolute;
        top: 0;
        right: 0;
        @include media('<=large') {
            position: static;
        }
    }
}
.link-modif-groupe {
    width: 500px;
    max-width: 100%;
    display: inline-block;
}
.prc-tva-eco {
    .information {
        width: 500px;
        .info {
            select, input:not([type="checkbox"]) {
                flex: 1 1 auto;
            }
            input {
                margin-left:5px;
                &[type=checkbox]{
                    margin-bottom: 2px;
                    margin-top: 3px;
                }
            }
            select{
                margin-bottom: 3px;
                margin-left: 5px;
                width: 110px;
            }
        }
    } 

    .info {
        display: flex;
        justify-content: flex-start;
        align-content: center;
        flex-wrap: wrap;
        vertical-align: middle;
        line-height: 20px;
        label, .info-fld {
            font-weight: 600;
            height: 32px;
            &.lbl-info-cdt{
                width: auto;
            }
        }
        label, .info-fld {
            padding-top: 5px;
        }
    }
    .conditions-prc {
        width: 550px;
    }
    .info label, .info .bold {
        width: 110px;
    }
    input {
        &.date{
            width:80px;
        }
        &.hour{
            width:45px !important;
        }
        &.prc-name{
            width:225px;
        }
        &[type=text]{
            width:107px;
        }
    }
    div {
        &.val-fld-date {
            margin-bottom: 5px;
            margin-left: 11px;
            select{
                float: none;
                margin-right: 10px;
            }
        }
        &.save-load {
            clear: both;
            display: none;
            text-align: center;
            img{
                border: none;
            }
        }
        &.error{
            background-color: #FFDDDD;
            border: 1px solid red;
            color: black;
            margin-bottom: 10px;
            margin-top: -5px;
            padding: 1em;
        } 
        &.error-success{
            margin-top: -5px;
        }
        &.bold {
            border-bottom: 1px solid #A9A9A9;
            font-weight: 600 !important;
            margin-bottom: 10px;
            padding-bottom: 10px;
        }
    }
    select.sbl-select{
        margin-right: 5px;
    }
    .info-bulle {
        padding: 0 3px;
    }
    tbody {
        transition: opacity 0.5s;
        td {
            border: 1px solid #A3A3A3;
            &.conditions, &.action, &.information{
                padding: 15px 10px 10px 10px !important;
            }
            &.action{
                vertical-align: middle;
                text-align: center;
                width: auto !important;
                
                @include media('<=medium') {
                    width: 100% !important;
                }
            }
            &.cdt-center{
                padding: 10px;
                vertical-align: middle;
            }
            &.val select{
                width: 110px;
            }
            &.conditions {
                img {
                    border: medium none;
                    float: left;
                    margin-top: 5px;
                    &.del-cdt{
                        cursor: pointer;
                        float: right;
                        margin-left: 5px;
                    }
                    &.del-cdt2{
                        margin-top: 5px;
                    }
                } 
                [type=radio]{
                    margin-top:8px;
                    width:25px;
                }
                .date{
                    width: 75px;
                }
                input:not(.action){
                    width: 100px;
                }
            } 
            &.or-cdt{
                background-color:#E3E3E3;
                border:1px solid #C4C4C4;
                font-weight: 600;
                padding:5px;
                text-align:center;
            }
        }
    }
    .display-flex {
        align-items: flex-start;
        border-bottom: 1px solid $grey-medium-color;
        margin-bottom: 5px;
        padding-bottom: 5px;
        .display-flex {
            flex: 1 1 auto;
            flex-wrap: wrap;
            align-content: stretch;
            border-bottom: none;
            margin-bottom: 0px;
            padding-bottom: 0px;
            .conditions-next {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                width: 98%;
                & > * {
                    width: 50%;
                }
            }
            .fld {
                width: 98%;
                max-width: 100%;
            }
            
        } 
    }
    select, input{
        &:disabled {
            color:#545454;
        }
    }
    .datepicker {
        width: 100px !important;
    }
    .ac_back_shadow{
        background-image:none;
        background-repeat:no-repeat;
        min-height:185px !important;
        min-width:500px !important;
        padding-bottom:3px;
        padding-right:3px;
    }
    .ac_results {
        background-color:white;
        border:1px solid #C4C4C4;
        min-height:180px !important;
        overflow:hidden;
        padding:0;
        width:495px !important;
        z-index:99999;
    }
    
    @include media('<=large') {
        tr {
            border-bottom: none !important; 
            &:nth-child(odd) {
                background-color: $grey-color;
            }
        }
        td {
            width: 100% !important;
            input:not([type="checkbox"]) {
                width: auto !important;
            }
        }
        .information .info select {
            width: 110px !important;
        }
    }
    @include media('<=smallmedium') {
        
        .info {
            flex-wrap: wrap;
            label:not(.lbl-info-cdt) {
                width: 100% !important;
                .lbl-info-cdt {
                    width: auto !important;
                }
            }
            select, input {
                margin: 2px 0;
            }
        }
         
    }
}


form#pmt-search {
    margin-bottom: 10px;
    /* Flex */ 
    .block-flex-wrap {
        display: flex;
        flex-wrap: wrap;
        .div-like {
            flex-wrap: nowrap !important;
        }
        .child-flex {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 100%;
            max-width: 415px;
            [for="date_start"] {
                padding-top: 0;
            }
        }
    }
    label:first-child {
        width: 215px;
        padding-top: 5px;
        
    }
    .left a {
        line-height:35px;
    }
    #like {
        margin-right: 5px;
    }
    #search {
        width: 100%;
    }
    select, input[type=text] {
        width: auto;
        vertical-align: middle !important;
    }
}

#groupspromotionpicker .selectorview{
    a.btn img {
        width: 16px;
        height: 8px;
    }
}

/* Tableau Nouvelle promotions */
form#frm-add-promo {
    table#promo-info {
        width: 600px;
        .td-promo-info {
            width: 300px;
            :first-child {
                float: left;
            }
        }
    }
    div.notice {
        width: 595px;
        margin-bottom: 5px;
    }
    table#tb-promo-remise {
        thead tr th {
            width: 600px;
        }
        td#grp-prd-promo {
            display: none;
        }
        tfoot {
            tr td input#add-prd-promo {
                float: left;
            }
        }
    }
    table#tb-prd-promo {
        #prd-ref {
            width: 300px;
        }
        #prd-remise {
            width: 200px;
        }
        #prd-remise-val {
            width: 100px;
        }
    }
    img.del{
        border:none;
        float:left;
        margin-right:5px;
        margin-top:3px;
        width:10px;
        cursor:pointer;
    }
}

/* codes promo */
.form-promo-specials {
    width: 825px;
    table#table-codes-promo {
        width: 100%;
        #pmt-select {
            width: 25px;
        }
        #pmt-code {
            width: 80px;
        }
        #pmt-state {
            width: 145px;
        }
        .opened {
            background-color: $bg-green-color;
        }
        .incoming {
            background-color: $bg-blue-color;
        }
    }
}

.export-button {
    padding-bottom: 5px;
    margin-bottom: 0;
}

table#pmt-special {
    width: 100%;
    tbody{
        .td-pmt-spec {
            width: 190px;
        }
    }
    .cdt-grp-rule-items {
        width: auto !important;
    }
    * {
        vertical-align: middle;
    }
    #gen-code {
        float: none;
    }
    .pmt-list-service {
        list-style : none;
        margin: 0 0 10px 0 !important;

    }
    .pmt-list-service li {
        margin: 0 !important
    }
    #pmt-cmt {
        width: 100%;
    }
    fieldset.cdt-grp {
        padding: 10px 0 !important;
        margin: 10px 0;
        & > .cdt-intro {
            padding: 0 10px 10px 10px; 
        }
        div.cdt-config {
            padding: 5px 30px 5px 10px;
            &:nth-child(odd) {
                background-color: $grey-color;
            }
            &:last-child {
                border-bottom: 1px solid grey;
            }
            .cdt-psy-val {
                position: static !important;
                .cdt-grp-del {
                    margin-right: 10px;
                }
                select {
                    margin-top: 0;
                }
            }
        }
        input.btn-action-small {
            margin: 10px 10px 0 10px;
        }
    } 
    
    input.btn-action-small {
        float: right;
    }
    .cdt-grp-rule{
        width: auto !important;
    }
    .button-del-group {
        margin: 10px 0 0 10px;
        float: left;
    }
}
#tb-tabProducts, #tb-tabCustomers {
    width: 650px;
    min-width: 0 !important;
    fieldset#pmt-add-rule {
        label {
            display: inline-block;
            width: 165px;
            max-width: 100%;
        }
        input[type="text"] {
            width: auto !important;
        }
    }
}
#tb-tabProducts {
    table {
        min-width: 0 !important;
    }
    .pmt-rules {
        select, [type="text"], td {
            vertical-align: middle !important;
        }
        @include media('<=large') {
            .tb-colisage {
                width: 100%;
                display: block !important;
                max-width: 100%;
                tr {
                    display: block;
                    border-bottom : 0 !important;
                    td {
                        display: inline-block;
                    }
                }
            }
        }
        
        select {
            width: auto !important;
        }
    }
    @include media('<large') {
        input[type="button"] {
            width: auto !important;
        }
    }
}

div#tb-tabVariations table {
    width: 500px;
    th#var-del {
        width: 25px;
    }
}
table.tb-tabStats {
    width: 775px;
    th {
        text-align: left;
        &:not(:first-child) {
            width: 90px;
        }
    } 
}
#tb-tabSerie {
    #pmt-select {
        width: 25px;
    }
    #pmt-serie-etat, #pmt-serie-cmd {
        width: 90px;
    }
}

table.pmt-rules {
	width: 100%;
}	
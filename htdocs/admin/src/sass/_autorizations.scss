/**
 * CSS des fichiers ./catalog/authorizations/index.php 
 * et ./view.admin.inc.php (fonction view_websites_selector)
 */
 
 /* sélecteur de période pour les tarifs */
 .riawebsitepicker .selectorview {
    a.btn img {
        width: 16px;
        height: 8px;
    }
}

#table-authorizations {
    margin-top: 10px;
}

#table-new-autorization {
    #td-new-autorization {
        width: 150px;
    }
}
			
#table-conditions-authorization {
    width: 100%;
    max-width: 800px;
    #rec-fld {
        width: 305px;
    }
    #rec-symbol {
        width: 180px;
    }
    #rec-none {
        width: 20px;
    }
    .tr-rec-new-fld {
        td.td-widthout-et select.rec-new-fld {
            width: 100% !important;
            margin-left: 0;
        }
        td.td-width-et{
            display: flex;
            justify-content: space-between;
            align-items: center;
            select {
                width: 270px !important;
            }
        }
    }
    .span-valeur {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .input-value-condition {
            width: 160px !important;
        }
    }
}


/* Dr<PERSON> d'accès */ 
#tb-authorization-edit {
    width: 100%;
    max-width: 100%;
	thead th {
		&:first-child {
			width: 20px;
		}
		&:nth-child(3) {
			width: 200px;
		}
		&:nth-child(4) {
			width: 180px;
		}
		&:nth-child(5) {
			width: 250px;
		}
		&:last-child {
			width: 180px;
		}
    }
    td {
        &.td-liste-et {
            width: 30px;
        }
        &.td-widthout-et {
            select.rec-new-fld {
                text-align: left;
                margin-left: 0;
            }
        } 
        &.td-width-et{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .et {
                margin-right: 10px;
            }
        }
        &.td-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
            input[type=text] {
                width: auto !important;
            }
        }
        &.action {
            padding: 10px !important;
            input.action {
                margin: 5px 5px 5px 0 !important;
            }
        }
    }
    
    @include media('<=large') {
        tr.dataID td {
            &.action {
                border-left: 0;
            }
            &.sync {
                border-right: 0;
            }
        } 
    }

    /* RESPONSIVE */
        
    @include media('<=large') {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
    }

    caption, tbody, thead, tfoot {
        @include media('<=large') {
            width: 100%;
        }
    }

    tr {
        @include media('<=large') {
            display: flex;
            flex-wrap: wrap;
        }
    }
}
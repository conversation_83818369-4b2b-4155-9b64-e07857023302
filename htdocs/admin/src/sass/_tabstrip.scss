/* Tabstrip */
.tabstrip {
	list-style-type: none;
	margin: 0px;
	padding: 0px;
	display: flex;
	flex-wrap: wrap;
	border-bottom: 1px solid $dark-color;
	li {
		margin: 0;
		padding: 0;
		border-top: 1px solid $dark-color;
		border-bottom: 1px solid $dark-color;
		border: 1px solid $dark-color;
		background-color: #fff;
		border-top-right-radius: 10px;
		position: relative;
		margin-right: -10px;
		overflow: hidden;
		margin-bottom: -1px;
		&:first-child > input[type="button"]{
			padding-left: 14px;
		}
		> *, 
		> input[type="button"] {
			border-style: none;
			background-color: transparent;
			margin: 0 !important;
			padding: 0 14px 0 24px;
			background-repeat: no-repeat;
			border-radius: 0;
			display: block;
			line-height: 24px;
			color: $dark-color;
			width: 100%;
			font-size: 12px;
			&:hover {
				background-color: $light-color;
				color: $dark-color;
				text-decoration: none;
			}
			&.selected {
				text-decoration: none;
				background-color: $medium-color;
				color: #ffffff;
				cursor: default;
				&:focus {
					background-color: $medium-color;
					color: #ffffff;
				}
			}
			&:focus {
				border: 0 none;
				background-color: $light-color;
			}
		}
		&.selected {
			background-color: $medium-color;
			> * {
				color: #ffffff;
				&:hover {
					background-color: $medium-color;
					text-decoration: none;
				}
				&:focus {
					border: 0 none;
					background-color: $medium-color;
				}
			}
		}
		@for $i from 1 through 30 {
			&:nth-child(#{$i}) {
					z-index: 30-$i;
			}
		}
		&:first-child {
			> * {
				padding-left: 14px;
			}
		}
	}
}
.tabstrip-vertical {
	list-style-type: none;
	margin: 0px;
	padding: 0px;
	li {
		margin: 0px;
		padding: 0px;
		border-top: 1px solid $dark-color;
		border-bottom: 1px solid $dark-color;
		border: 1px solid $dark-color;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: -1px;
		@for $i from 1 through 30 {
			&:nth-child(#{$i}) {
					z-index: 30-$i;
			}
		}
		> * {
			border-style: none;
			background-color: transparent;
			margin: 0 0 -1px !important;
			padding: 6px 10px;
			background-repeat: no-repeat;
			border-radius: 0;
			display: block;
			color: $dark-color;
			width: 100%;
			height: auto !important;
			text-align: left;
			margin-bottom: -1px;
			&:hover {
				background-color: $light-color;
				color: $dark-color;
				text-decoration: none;
			}
			&.selected {
				text-decoration: none;
				background-color: $medium-color;
				color: #ffffff;
			}
			&:focus {
				border: 0 none;
				background-color: $light-color;
			}
		}
	}
}
#tabstrip-vertical-container {
	min-width: 65%;
	@include media('>=large') {
		display: flex;
	}
}
#popup-content.tabs .tabstrip li:first-child {
	margin-left: 10px;
}
#popup-content.tabs .tabscontent {
	padding: 10px 15px;
}
 #tabpanel {
	border: 1px solid $dark-color;
	border-top-style: none;
	padding: 5px;
	clear: both;
	& > .lng-menu {
		margin-top: 9px;
	}
}
#tabpanel-vertical {
	@include media('<medium') {
		padding: 12px;
	}
	margin-left: -1px;
	border: 1px solid $dark-color;
	padding: 12px 12px 12px 32px;
}
#site-content #tabpanel-vertical p {
	margin-bottom: 12px;
}



/* fieldsets */

#site-content #tabpanel-vertical div input.checkbox {
	width: auto;
}
#site-content #tabpanel-vertical div {
	padding: 3px;
	sub {
		@include media('<medium') {
			padding-left: 0
		}
		@include media('>medium','<=large') {
			padding-left: 0
		}
        padding-left: 123px;
    }
}
#site-content #tabpanel-vertical label, 
#site-content #tabpanel-vertical div span.label {
	@include media('<medium') {
			display: block;
			width: 100%;
			text-align: left;
	}
	@include media('>medium','<=large') {
			text-align: left;
		}
	display: inline-block;
	width: 118px;
	text-align: left;
	padding-right: 5px;
}
#site-content #tabpanel-vertical div label.inline {
	display: inline;
	float: none;
}
#site-content #tabpanel-vertical div.checkbox {
	padding-left: 195px;
}
#site-content #tabpanel-vertical div input, 
#site-content #tabpanel-vertical div textarea {
	@include media('<medium') {
			width: 100%;	
	}
	width: 345px;
}
#site-content #tabpanel-vertical div.actions input {
	width: auto;
}
#site-content #tabpanel-vertical textarea {
	display: block;
	width: 490px;
}
#site-content #tabpanel-vertical div.actions {
	@include media('<medium') {
		padding-left: 0
	}
	padding-left: 123px;
}

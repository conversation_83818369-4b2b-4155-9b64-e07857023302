/**
 * CSS de l'onglet Statistiques
 */

/* Meilleures ventes */  
#bestseller-prd{
    #best-ref {
        width: 190px;
    }
    #best-orders {
        width: 75px;
    }
    #best-by-ord {
        width: 130px;
    }
    #best-ca, #best-ca-ord, #best-gross-margin {
        width: 120px;
    }
}

/* Ventes par catégories*/  
#bestseller-bycat {
   #best-orders {
       width: 75px;
   }
   #best-by-ord {
       width: 145px;
   }
   #best-ca, #best-ca-ord {
       width: 120px;
   }
}

/* Fidélité */ 
.fidelity {
    #val_last_year, #val_current_year {
        // width: 150px;
    }
}

/* Recherches */ 
#table-synthese-search {
    thead th{
        width: 145px;
    }
}

#table_search {
    width: 100%;
    #volume, #nbres, #avg {
        width: 100px;
    }
    #ctr {
        width: 120px;
    }
    #website {
        width: 130px;
    }
}

/* Redirections */ 
#table_search {
    width: 100%;
    .td-check {
        width: 25px;
    }
    #types {
        width: 85px;
    }
    #emp {
        width: 280px;
    }
    #website {
        width: 130px;
    }
    input.w80 {
        width: 80%;
    } 
}

/* Formulaire ajout de suggestion */
#form-add-suggest{
    margin-bottom: 15px;
    margin-top: 15px;
    border: 1px solid $grey-medium-color;
    width: 500px;
    #new-title{
        background-color: #232E63;
        color: white;
        font-weight: 600;
        padding: 5px 0 5px 10px;
        
    }
    #new-suggestion {
        padding: 10px 3px 10px 10px;
        width: 500px;
        & > div:not(#opt-sec),
        #opt-sec > div {
            display: flex;
            align-items: center;
            & > label {
                width: 150px;
            }
        }
        input, select {
            width: 200px;
            vertical-align: middle;
        }
    }
    #new-foot{
        border-top: 1px solid $grey-medium-color;
        padding: 10px 3px 10px 10px;
        text-align: right;
        width: 499px;
        input#add-suggest {
            margin-right: 10px;
        }
    }
}

/* Suggestions de recherche */
#table-suggest-search {
    #active {
        width: 25px;
    }
    #results {
        width: 100px;
    }
    #search {
        width: 350px;
    }
    #site {
        width: 100px;
    }
} 

/* Poids des produits */ 
.prd-weight:not(#prd-conversion) {
    thead tr:last-child th{
        width: 100px;
        &#w-cat, &#wn-cat {
            width: 350px;
        }
    }
}
#prd-completing {
    #desc {
        width: 450px;
    }
}

/* Produits sans images */
#form-no-image {
    padding-top: 10px;
    ul.tabstrip {
        margin-top: 10px;
    }
}

#prd-no-image {
    width: 100%;
    thead th {
        &#prd-taux {
            width: 160px;
        }
        &:last-child {
            text-align: right;
        }
    }  
    * {
        vertical-align: middle !important;
    }
} 

.div-padding-10 {
    padding: 10px;
}

#prd-conversion {
    width: 100%;
    max-width: 100%;
    th.header {
        cursor: auto;
    }
    th, td, th a {
        word-wrap: break-word;
    }
    #w-ref {
        width: 50px;
    }
    #w-name {
        width: 320px;
    }
    #w-conversion {
        width: 200px;
    }
    #w-margin_rate {
        width: 160px;
    }
    #w-price_sell, #w-price_purchase {
        width: 130px;
    }
    @include media('>=medium','<mlarge') {
        table-layout: fixed;
        th, td {
            width: auto !important;
        }
    }
}
span#tooltip-shiftKey {
    margin-bottom: 10px;
    span#t-s-1 {
        float: left;
        vertical-align: middle;
        img {
            vertical-align: middle;
            border: 0 none;
            width: 48px;
            height: 48px;
        }
    }
}
span#t-s-2 {
    font-weight: bold;
    font-size: 40px;
    vertical-align: bottom;
}

.tb-stat-ref {
    th {
        width: 32%;
        &:first-child {
            width: auto;
        }
    
    }
}
/* Référencement > Balises */ 
.tb-stat-ref, #table-edit-personnalisation ,
.tb-stat-ref, #table-referencement {
    td {
        @include media('<large') {
            overflow : visible !important;
        }
        &:last-child {
            .open {
                right: 0px !important;
            }
        }
    } 
}
.riametasBox_wrap{
    position: relative;
    &, &.padding {
        padding: 0 20px 0 0;
    }
    textarea {
        width: 100%;
    }
    .riametasbox{
        right: -20px;
        width: 50px;
        position: absolute;
        top: -1px;
        font-size: 0.8em;
        // right: -40px;
        &.open {
            z-index: 20;
            width: 250px;
            right: -220px;
            @include media('<medium') {
                right: 0px;
            }
            .box-content{
                padding: 5px;
                font-size: 1em;
            }
        }
        &.meta-success {
            .box-arrow{
                background: url(/admin/images/metabox/success-arrow.png) no-repeat;
            }
            .box-content{
                color: black; 
                background-color: #ddffdd;
                border: 1px solid #008000;
            }
        }
        
        &.meta-note {
            .box-arrow{
                background: url(/admin/images/metabox/note-arrow.png) no-repeat;
            }
            .box-content {
                background-color: $yellow-light;
                border: solid 1px $yellow-medium;
            } 
        }
        .box-arrow {
            width: 17px;
            height: 18px;
            padding: 10px 0 0 0;
            position: absolute;
            left: 0px;
            top: 2px;
            z-index: 10;
        }
        .box-content {
            -moz-border-radius: 5px;
            -webkit-border-radius: 5px;
            border-radius: 5px;
            font-size: 0.9em;
            position: absolute;
            left: 11px;
            z-index: 9;
            cursor: pointer;
            padding: 3px 2px 2px 2px;
        }
        p {
            margin: 2px;
        }
    }

    td:last-child & {
        .riametasbox{
            &.open {
                right: 0px;
            }
        }
    }
}
/* Balises Meta Description en double */ 
#stat-tag-double {
    width: 870px;
    #tag-descript {
        width: 240px;
    }
    #tag-object {
        width: 628px;
    }
    li.cnt-parent ul {
        list-style: none !important;
        li {
            margin-left: 0 !important;
        }
    }
}

/* Veille tarifaire */ 

.checkboxes {
	display: block;
    padding-right: 10px;
    .input {
        vertical-align: middle;
    }
    .span {
        vertical-align: middle;
    }
}
#stats-price-watching {
    th {
        // width: 30px;
        // &:first-child {
        //     width: 50px;
        // }
        // &:nth-child(2){
        //     width: 300px;
        // }
        // &:nth-child(3){
        //     width: 100px;
        // }
        // &:nth-child(4){
        //     width: 90px;
        // }
        // &:nth-child(5){
        //     width: 70px;
        // }
    }
    @include media('<large') {
        th, td {
            &.align-right, &.numeric {
                text-align: left !important;
            }
        }
    }
}
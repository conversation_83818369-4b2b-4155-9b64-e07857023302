/**
 * CSS de la catégorie <PERSON>
 */

/* Gestion de parc / Flotte d'appareils */ 
#tb-liste-fabricant {
    #dev-checked {
        width: 25px;
    }
    #dev-key {
        width: 355px;
    }
    #dev-version {
        width: 75px;
    }
    #dev-user {
        width: 200px;
    }
    #dev-last-sync {
        width: 150px;
    }
    #dev-last-location {
        width: 160px;
    }
}

#tb-exec-sql {
    .label {
        width: 125px;
    }
}

.bg-pink {
    background-color: #ffe5e5;
}

/* Guide d'installation */ 
#liste-guide img {
    margin-left: 15px;
    margin-top: 10px;
    @include media('<=medium') {
        margin: 10px auto;
    }
}

/* Horaires d'activité */ 
.tb-day-exp2 {
    thead th {
        width: 95px;
        &#day-exp {
            width: 75px;
        }
        &#period-exp {
            width: 220px;
        }
        &#action-exp {
            width: 180px !important;
        }
    }
}

#form-holidays {
    thead th {
        width: 200px;
    }
}

#tb-closing {
    #clg-start, #clg-end {
        width: 170px;
    }
    #clg-action {
        width: 20px;
    }
}

#yuto-notifications {
    thead th:not(:first-child) {
        width: 175px;
    }
}

/* Rapports d'appels */ 
#calls-report {
    #reports-created {
        width: 250px;
    }
    #reports-author, #reports-dest {
        width: 300px;
    }
}

/* Rapports de visites */ 
#tb-type-rapports {
    thead th {
        width: 350px;
    }
}

/* Rapports Présentation des produits */ 
#tb-reports-pres-produits {
    thead th {
        width: 275px;
        &#reports-id {
            width: 175px;
        }
        &#reports-created {
            width: 150px;
        }
    }
}

/* Chiffre d'affaires */ 
#tb-total-ca {
    thead th {
        width: 150px;
        &:first-child {
            width: 200px;
        }
    }
}

/* Temps de visite */ 
#tb-moyenne-rdv {
    #avg_duration {
        width: 525px;
    }
    #avg_revenue, #avg_yield {
        width: 200px;
    }
}

#t_spent_time {
    thead th {
        width: 200px;
        &:first-child {
            width: 325px;
        }
    }
}

/* Palmarès */ 
#notif p {
    font-size: 12px;
    font-weight: bold;
    color: #666666;
    fill: #666666;
    margin-top: 100px;
    text-align: center;
}

.tb-kpi {
    @include media('>=medium') {
        width: 340px;
    }
    @include media('<=medium') {
        width: 100%;
    }
    border-collapse: collapse;
    display: inline-table;
	clear : none;
    margin-right : 15px;
    caption {
        box-sizing: border-box;
    }
    tr {
        td {
            border-bottom: 1px solid #ccc;
        }
        &.show-seller {
            background-color: lightgrey;
            &:hover {
            	background-color: #C7C7C7;
            }
        }
        &.hidden-seller {
            display: none;
        }
    }
    tfoot a {
        display: block;
    }
}

/* Configuration */ 
#tb-ovr-usr {
    margin-top: 10px;
    width: 850px;
    #cls-check, #cls-nb-fld {
        width: 250px;
    }
    #cls-name {
        width: 350px;
    }
    .bg-green-color {
        background-color: $bg-green-color;
    }
}

.hour_select {
    width: auto;
}
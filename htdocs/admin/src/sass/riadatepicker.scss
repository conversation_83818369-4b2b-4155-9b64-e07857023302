@import "node_modules/include-media/dist/_include-media.scss";
@import "variables";

/**
 * CSS des sélecteurs
 */

 #rialanguagepicker, 
 #riadatepicker:not(:empty), 
 #riawebsitepicker,
 #closingpicker, 
 #expeditionspicker, 
 #riaothersfilters, 
 #riawebsitepicker, 
 #div-filter-sort, 
 #div-filter-type, 
 .riapicker {
    display: inline-block;
    margin: 0 6px 8px 0;
    .selector {
        display: none;
        position: absolute;
        width: 266px;
        float: right;
        background-color: white;
        border-left: solid 1px #A9A9A9;
        border-right: solid 1px #A9A9A9;
        border-bottom: solid 1px #A9A9A9;
        border-top: solid 1px #A9A9A9;
        text-align: left;
        display: none;
        overflow-y: auto;
        z-index: 9999;
        a {
            display: block;
            padding: 5px;
            margin: 1px 0;
            color: black;
            text-decoration: none;
            &.child {
                margin-left: 30px;
            }
            &:hover{
                background-color: #DADCFF;
            }
        }
    }
    .selectorview {
        padding: 5px;
        border: solid 1px #A9A9A9;
        width: 266px;
        text-align: left;
        cursor: pointer;
        height: 42px; 
        div.left{
            float: left;
        }
        a {
            padding: 10px 0;
            float: right;
            display: block;
        }
        img{
            border : none !important;
        }
        span.function_name {
            color: $grey-dark-color;
            font-size: 0.9em;
        }
    }
}
#div-filter-sort, 
#div-filter-type{
 .riapicker {
     display: block;
     margin: 0
 }
}

#infobulle, #infobulle2{
    background-color: #FFF6CF;
    border: 1px solid #DFD299;
    padding: 11px;
    width: 280px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#infobulle a, a#infobulle {
	color: $black;
}

.stats-menu-wo-results #infobulle {
	display: none;
}

.riapicker.prd-filters .selector {
	max-height: 395px;
}

#riadatepicker .selectordate{
	position: absolute;
	width: 260px;
	border-left: solid 1px #A9A9A9;
	border-right: solid 1px #A9A9A9;
	border-bottom: solid 1px #A9A9A9;
    background-color: #F0F4FB;
    .options{
        height: 195px;
    }
}

div#action input{
	background-image: url(/admin/images/stats/btn.gif);
	padding: 10px;
	border: solid 1px #A9A9A9;
	color: black;
	float: left;
    cursor: pointer;
    &:hover{
        background-image: url(/admin/images/stats/btn_hover.gif);
    }
}
 
#site-content div.loader {
	position: absolute;
	margin-top: 45px;
    margin-left: 5px;
    img{
        border: none;
    }
}

.selector-sep {
    border-top: 1px solid #A9A9A9;
    cursor: default;
    margin: 0 10px !important;
    padding: 0 !important;
}

.selector {
	max-height: 325px;
	overflow-y: scroll;
}

#selectorigins .selector {
	max-height: none;
}

@include media('<large') {
    /* Surcharge Langue Selecteur */
    #rialanguagepicker, #riadatepicker, #riawebsitepicker, #closingpicker, #expeditionspicker, #riaothersfilters, .riapicker {
        float: none;
        margin-right: 0 !important;
        position: relative;
        .selectorview {
            width: 100%;
        }
        .selector {
            width: 100% !important;
        }
    }
}


//espace entre le tabpanel et rialanguagepicker
#tabpanel > .stats-menu {
    margin-top: 9px;
}

#popup_ria {
    .select-tenant {
        .select-tenant-list{
            margin: 10px 20px;
            // border-top: 1px solid #232E63;
        }
        li {
            margin: 0;
            list-style: none;
            &:not(.hide) {
                border: 1px solid #232E63;
                border-top: 0px;
            }
            &.first  {
                border-top: 1px solid #232E63;
            }
            a {
                color: #232E63;
                display: block;
                padding: 10px 0;
                font-size: 1.2em;
                font-weight: 600;
                &:hover {
                    border-color: #5377FB;
                    background-color: #5377FB;
                    color: #fff;
                    text-decoration: none;
                }
            }
        }
    }
}
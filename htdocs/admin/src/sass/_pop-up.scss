/**
 * CSS des Pop-up
 */

/* message "Chargement en cours" */ 
.popup_ria_back_notice {
    z-index: 9999;
    position: fixed;
    text-align: center;
    top: 50%;
    left: 50%;
    padding: 5px 25px;
    transform: translateX(-50%);
    width: 90%;
    max-width: 400px;
}


body#popup-content {
	background-image: none;
	color: #000000;
	min-width: 0;
	height: auto;
	min-height: 0px;
    padding: 15px;
    &.tabs {
        padding: 15px 0 0 0;
    }
    table caption {
        a {
            color: white;
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
        }
        ul {
            margin-left: 21px;
        }
    }
}

 /* Popup Créer un lien vers un nouvel objet */ 
 #popup-content {
     #table-create-link-object {
        #res-check, tr:first-child td:first-child {
            width: 20px;
        }
    }
 }


/* Produits offerts */ 
/* Popup rechercher un produit */ 
#tb-popup-catalogue {
    @include media('>=medium') {
        width: 100%;
    }
    @include media('<=medium') {
        width: 300px;
    }
    border-collapse: collapse;
    display: inline-table;
    clear : none;
    margin-right : 15px;
    caption {
        box-sizing: border-box;
    }
    thead th {
        &:first-child {
            width: 20px;
        }
        &:last-child {
            width: 30px;
        }
    }
    td {
        border-top: 0;
    }
}

/* Popup "Rechercher un produit", ajax-product-select.php 
Dans Configuration > Cartes cadeaux, au clic sur "Ajouter un produit" */
 /*.lst-prd-table {
   
}*/

/* Popup Sélection un objet de rattachement */
#tb-popup-liste-obj-classe {
    width: 100%;
    thead th {
        &#obj-name{
            width: 225px !important;
        }
        &#obj-childs {
            width: 140px;
        }
    }
}

/* Popup Importer depuis une autre promotion */ 
#pmt-specials-product-import {
    width: 100%;
}

/* Popup "Choisissez une famille du comparateur" popup-export-comparators.php
Dans Catalogue > La sélection Purebike > Edition, onglet "Place du marché, au clic sur Modifier puis Exporter */ 
#popup-content {
    input.ui-autocomplete-input, input#filter {
        vertical-align: middle !important;
    }
    table#choose-cat-ctr {
        tfoot {
            input#save-choose {
                margin-right: 5px;
            }
        }
    }
}

/* Popup "Choisir une catégorie", popup-catégories.php
Dans Catalogue > La sélection Purebike > Edition, onglet "Général", au clic sur "Ajouter une Catégorie" */ 
.tb-popup-choose-cat {
    .riaCancelButton {
        margin: 2px;
    }
}

/* Popup "Ajout rapide de produit", 
Dans Catalogue > Relevés linéaires > "un relevé", onglet "Général", au clic sur "Ajout rapide" */
#result-table {
    tr {
        &.error, &.success {
            display: table-row;
        }
    }
}

/* Popup "Exporter la liste des clients", popup-export-customers.php
Dans Comptes clients, un comte client, onglet "Général", au clic sur "Exporter" */ 
#popup-content {
    .cols {
        columns: 4 auto;
        @include media('<medium') {
            columns: 2 auto;
        }
        @include media('<smallmedium') {
            columns: 1 auto;
        }
    }
    
}

/* Popup "Modification de l'adresse de facturation"
Dans Comptes clients, un comte client, onglet "Adresses", au clic sur "Modifier" */ 
/* #table-fiche-client {
} */ 

/* Popup "Edition du type", tab-medias.js
Dans Comptes clients, un comte client, onglet "Images", au clic sur "Editer" */ 
/* #type_form table {
} */ 

/* Popup "Ajouter des Images"
Dans Comptes clients, un comte client, onglet "Images", au clic sur "+" */ 
/* #mediatheque-container {
} */ 



/** top-table -> back button + search form **/
/** tfoot-cat ->add cart + btn choose & btn-cancel **/
#popup-content {
    .top-table {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .tfoot-cat {
        td > div{
            display: flex;
            flex-direction: row-reverse;
            justify-content: space-between;

            @include media('<medium') {
                flex-direction: column-reverse;
                width: 100%;
            }

            & > div {
                @include media('<medium') {
                    display: flex;
                    flex-direction: column;
                }
            }

            .btn-wrapper {
                @include media('<medium') {
                    margin: 15px 0 0 0;
                }
                & > * + * {
                    margin-left: 10px;
                    @include media('<medium') {
                        margin: 0;
                    }
                }
                input[type="button"]{
                    float: none;
                }
            }
        }
    }
}


/* Configuration > Paramètres de tarification, au clic sur "Choisir" dans le tableau "Produits exclus", Popup "Sélectionner un produit" */
.lst-prd-table {
    tr {
        vertical-align: middle !important;
    }
    .prd-more{
        display: none; 
        position: absolute;
        min-width: 80px;
        min-height: 80px;
        background: $white;  
        box-shadow: 0 0 5px $grey-color;
        padding: 2px;
    }
} 

/* Configuration > Redirections > Erreurs 404, au clic sur la loupe, popup "Rechercher un contenu de substitution" */
#result-redirection {
    #info:empty {
        display: none;
    }
    tr {
        height: auto !important;
        &:not(:last-child) {
            @include media('<medium') {
                border-bottom: 1px solid $grey-medium-color !important;
                padding: 0;
            }
        }
    }
    td {
        vertical-align: top;
        position: relative;
        &.img {
            text-align: center;
            @include media('<medium') {
                text-align: left;
            }
        }
        &.redirection {
            img {
                float: left;
                margin-right: 25px;
                width: 80px;
                height: 80px;
                @include media('<medium') {
                    float: none;
                    margin-right: 0;
                }
            }
        } 
        div {
            margin-bottom: 3px;
            &:last-child {
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                @include media('<medium') {
                    position : static;
                    top : auto;
                    right : auto;
                    transform : none;
                } 
            }
            input {
                float: right;
                margin: 10px;
                @include media('<medium') {
                    float: none !important;
                    width: auto;
                } 
            }
        }
    }
}

.content-substitut {
    padding: 5px 85px !important;
    @include media('<medium') {
        float: none;
        padding: 5px 10px !important;
    }
}

#lst-res-search {
    margin-top: 10px;
}


/* Médiathèque > Images, une image, au clic sur Ajouter, popup "Associer à un produit" */ 
#popup-links-img, .popup-order-state-history {
    #pagination {
        background-color: $white;
        border-top: 1px solid $grey-medium-color;
        margin: 0;
        margin-top: 10px;
        padding: 10px;
    }
    .cnt-checkbox {
        padding-top: 0;
        input[type='checkbox'] {
            margin-top: 0;
        }
    }
    .cnt-place select {
        vertical-align: middle;
    }
}

#popup-links-img {
    #pagination {
        width: 100% !important;
    }
}

/* Promotions > Codes promotions > un code promo, onglet Général, au clic sur "Modifier le pattern", popup "Modifier le pattern des codes promotions" */ 
.form-popup-pattern {
    select, input {
        width: auto;
        vertical-align: middle;
    }
    #pmt-rules-buttons {
        margin-top: 15px;
    }
    .elem {
        & label:first-child {
            width: 145px;
            display: inline-block;
        }
        input[type="checkbox"], #code-list-like {
            margin-left: 145px !important;
        }
    }
    #code-like {
        width: 145px;
        display: inline-block;
    }
}

/* Catégories > une catégorie > un produit, onglet "Comparateur", au clic sur "Surcharger les informations pour ce comparateur de prix", popup "Surcharge des informations exportées" */ 
.tb-popup-edit-ctrmarket {
    sub {
        vertical-align: middle;
    }
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation, dans le tableau, en sélectionnant Marque, au clic sur Choisir */ 
.pop-form-search {
    text-align: right;
    margin-bottom: 10px;
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation, dans Contexte "utilisateur, au clic sur Choisir, popup "Sélectionner un compte client */
.search-customers-results {
    tr {
        vertical-align: middle !important;
    }
}

/* Modération > Avis consommateur sur un produit, au clic sur "ne plus approuver", popup "Désapprobation de message" */ 
#moderation-comment {
    height: 95px !important;
}

.dropzone:hover{
	border: 3px dotted $medium-color !important;
}

/* Popup de gestion d'abonnement Yuto */
.popup-yuto-subscribtion .package {
	padding-top: 10px;
	padding-left: 10px;
}

.popup-yuto-subscribtion .package span {
	font-style: italic;
	margin-right: 5px;
}
.popup-yuto-subscribtion .opt-package {
    padding-top: 5px;
    padding-left: 0;
}

#popup-content.popup-yuto-payment p {
	padding: 0;
}

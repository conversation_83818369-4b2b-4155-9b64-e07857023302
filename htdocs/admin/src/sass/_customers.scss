/**
 * CSS des pages Clients > Tous les comptes
 */

/* tableau  de la liste des comptes dans ./customers/index.php */
#list-customers {
    max-width: 100%;
    thead{
        .th-customers {
            width: 170px;
        }
    }    
    @include media('<=xlarge') {
        [headers="usr-ref"] {
            max-width: 70px !important;
        }
        [headers="usr-prf"] {
            max-width: 80px !important;
        }
        [headers="usr-ref"],
        [headers="usr-prf"] {
            overflow-wrap: break-word;
        }
    }
    @include media('<large') {
        [headers="usr-ref"] {
            max-width: 100% !important;
        }
        [headers="usr-prf"] {
            max-width: 100% !important;
        }
        td {
            text-align: left !important;
        }
    }
}

/* tableau des fiches clients, onglet général dans ./wiews/customers/tabs/general.php */
#table-fiche-client-general {
    width: 680px;
    #td-fiche-client-general-1 {
        width: 215px;
    }
    #td-fiche-client-general-2 {
        width: 360px;
    }
    #usr-map {
        width: 100%;
        height: 275px;
        position: relative;
    }
    .ref {
        width: 180px;
    }
}

/* Pop-up créer un avoir */
#select-discount-type {
    width: auto !important;
}

/* Comptes Clients, "Nom du client" > onglet "Commandes" */
.tb-remainders {
    @include media('<medium') {
        tbody tr{
            &:not(.head-second):not(:only-child):nth-child(odd){
                background-color: $grey-color;
            }
            &.show-orders {
                background-color: $white !important;
            } 
        }
        
        .align-right, .numeric {
            text-align: left !important;
        }
    }
}

/* tableau des fiches clients, onglet droits dans ./include/right.inc.php */
#rights {
    width: 100%;
	margin-top: 15px;
	margin-bottom: 15px;
    border-spacing: 0px;
    .th-rights{
        width: 50%;
        vertical-align: middle;
        a {
            color: #3D50DF;
        }
    }
    ul{
        list-style-type: none;
        margin-left: 5px;
        ul{ 
            margin: 5px 20px;
        }
        li {
            margin: 5px 0;
        }
    }
}

/* Comptes Clients, "Nom du client" > onglet "Adresses" */
#address-new {
    #button-add-adresse {
        margin: 30px;
    }
}

/* Comptes Clients, "Nom du client" > onglet "Adresses", "ajouter une adresse" : Tableau dans la popup ajouter une adresse */
#popup-content #table-fiche-client {
    width: 100%;
    tbody {
        td {
            border-bottom: 0;
            #td-fiche-client-1 {
                width: 160px;
            }
        }
    }
} 

/* Comptes Clients, "Nom du client" > onglet "Disponibilité" */
#table-availability {
    #prd-sel {
        width: 20px;
    }
    #prd-ref {
        width: 100px;
    }
    #date {
        width: 150px;
    }
    #once {
        width: 200px;
    }
    #last {
        width: 120px;
    }
}

/* Comptes Clients, "Nom du client" > onglet "Favoris" */
#list-whishlist {
    #wishlist-del {
        width: 20px;
    }
    #wishlist-name {
        width: 300px;
    }
    #wishlist-publish {
        width: 80px;
    }
    #wishlist-prds {
        width: 100px;
    }
}

/* Tableau popup Ajouter une liste personnalisée */
#table-new-wishliste {
    width: 520px;
    #td-new-wishliste-1 {
        width: 150px;
    }
    #td-new-wishliste-2 {
        width: 300px;
    }
}

/* Comptes Clients, "Nom du client" > onglet "Relations" */
.hierarchies {
    .checkall {
        width: 20px;
    }
    .code-client {
        width: 100px;
    }
    .societe {
        width: 120px;
    }
    .nom, .adresse, .complements {
        width: 150px;
    }
    .email {
        width: 200px;
    }
}

#form-edit-user .notice {
    width: 896px;
}

/* Comptes Clients, "Nom du client" > onglet "Images"  */ 

.sortable-img .preview {
	float: left;
	margin: 0 5px 5px 0;
	width: 152px; 
	height: 152px;
}	
#site-content .has-main-img .sortable-img .preview:first-child{
	display: block;
	float: none;
}	
li.preview {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    [type="image"] {
        width: 30px;
        height: 30px;

    }
}

/* Comptes Clients, "Nom du client" > onglet "Objectifs" */ 
#goals {
    td {
        vertical-align: middle;
    }
}
/* Comptes Clients, "Nom du client" > onglet "Objectifs" */
#tb-goals {
    width: 600px;
    .hld-nav-year{
        border: medium none; 
        height: 16px; 
        width: 16px;
        border-radius: 50%;
        cursor: pointer;
    }
    tbody th {
        background-color: $bg-blue-color !important;
    }
}

#table-objectifs {
    #goal-month {
        width: 100px;
    }
    #goal-turnover-ord, #goal-turnover, #goal-turnover-inv, #goal-margin-now{
        width: 150px;
    }
    #goal-turnover-facture, #goal-margin-goal {
        width: 200px;
    }
}

#stats-rewards {
    #del-rewards {
        width: 20px;
    }
    #husr {
        width: 290px;
    }
    #edit {
        width: 290px;
    }
}

/* Comptes clients > Gestion des négociations > Nouvelle règle de négociation */
.nocaption {
    select, input:not([type="checkbox"]) {
        width: auto !important;
    }
}

.nocaption + .authorizations {
    .little_input {
        width: auto !important;
    }
}

/* Comptes clients > Gestion des droits d'accès > "un droit d'accès, onglet Droits */ 
 
/* Tableau des Droits d'accès des comptes clients */
#table-profiles {
    #prf-name, #prf-pl-name, #prf-users {
        width: 200px;
    }
}

#table-propriete {
    #td-propriete-1 {
        width: 135px;
    }
    #td-propriete-2 {
        width: 360px;
    }
}

#table-infos-generales-profiles {
    #td-infos-gen-prof-1 {
        width: 135px;
    }
    #td-infos-gen-prof-2 {
        width: 400px;
    }
}

#prf-rights {
    width: 100%;
    max-width: 425px;
    .expansible, .inexpansible {
        th {
            background-position: right center;
            background-repeat: no-repeat;
            &.checkall {
                background-image: none;
            }
        }
    }
    .expansible th {
        background-image: url('/admin/dist/images/down.svg');
        border-bottom: 1px solid $grey-medium-color;
    }
    .inexpansible th {
        background-image: url('/admin/dist/images/up.svg');
    }
    input.checkright {
        margin-left: 5px;
    }
    label.reset-rgh {
        display: block;
        margin-bottom: 5px;
        text-align: left;
    }
    #reset-rgh {
        float: left;
        margin: 1px 5px 40px 3px;
    }
}
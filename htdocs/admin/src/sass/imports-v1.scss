@import "node_modules/include-media/dist/_include-media.scss";
@import "variables";
@import "animate";
@import "modules/progress-bar";

/** imports/index.php **/
#table-map-file {
  position: relative;
}
.tools-import-customers {
  max-width: 1200px;
  p {
    line-height: 19px;
    color: #232E63;
    padding: 0 5px;
    margin: 0;
  }
  & > .block-actions {
    display: flex;
    @include media('<medium'){
      flex-direction: column;
    }
    & > div:first-child {
      margin-left: 0;
    }
    & > div:nth-child(3) {
      margin-right: 0;
    }
  }
  // block-action global
  .block-action {
    width: calc(100% / 3);
    margin: 10px 10px 20px 10px;
    padding: 20px;
    border: 1px solid $medium-light-color;
    border-radius: 4px;
    box-shadow: 0 2px 10px 0 #d2d2d2;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    @include media('<medium'){
      width: 100%;
      margin: 10px 0;
    }

    .title {
      font-size: 17px;
      color: $dark-color;
      border-bottom: none;
      padding-left: 45px;
      margin-top: 0;
      position: relative;

      &:before {
        position: absolute;
        content: "";
        left: 0;
        top: 50%;
        margin-top: -18px;
        width: 35px;
        height: 35px;
        background-repeat: no-repeat;
        background-size: contain;
      }
    }

    &.--start-import .title:before {
      background-image: url('/admin/images/imports/import.svg');
    }
    &.--download-example .title:before {
      background-image: url('/admin/images/imports/download.svg');
    }
    &.--file-prepare .title:before {
      background-image: url('/admin/images/imports/table.svg');
    }

    &.--start-import {
      display: block;
      position: relative;

      form {
        height: 100%;
        display: block;
        position: relative;
        padding-bottom: 48px;

        .input-file-container {
          flex: 1 1 auto;
        }

        & > div:first-child {
          margin-bottom: 20px;
        }

        select,
        input[type="password"] ,
        input[type="text"] {
          width: 100%;
        }

        label {
          color: #7b82a1;
        }

        button {
          position: absolute;
          bottom: 0;
          margin-left: auto;
          width: 100px;
          margin-right: auto;
          left: 0;
          right: 0;
        }

      }
    }

    &.--download-example {
      .title + p {
        flex: 1 1 auto;
      }
    }
    &.--file-prepare {
      ul {
        flex: 1 1 auto;
        margin-top: 0;
      }
    }

    &.--download-example,
    &.--file-prepare {
      align-content: space-between;
      button,
      .button {
        align-self: center;
      }
    }
    button,
    .button {
      margin-top: 15px;
      padding: 10px 20px;
      font-size: 12px;
      text-align: center;

    }
    a.button[target="_blank"] {
      padding-right: 35px;
      margin-right: 0;
    }
    .field-helper {
      color: $grey-dark-color;
    }
  }
  .imports-unfinished-history {
    margin-bottom: 20px;
  }

  // import unfinished
  .imports-unfinished-file {
    display: flex;
    flex-direction: column;
    padding: 5px 0;

    h3 {
      border-bottom: none;
      display: block;
      width: 100%;
      margin: 0;
      padding-bottom: 10px;
    }

    .alldata {
      width: 100%;
    }

    .unfinished-columns {
      width: 19%;
      display: inline-block;
      vertical-align: middle;

      @include media('<mlarge'){
        width: 32%;
      }
      @include media('<medium'){
        width: 100%;
      }

      &.state {
        padding-right: 20px;
        @include media('<mlarge'){
          padding-right: 0;
        }
        @include media('<medium'){
          padding-right: 0;
        }
      }

      .imp-name {
        word-break: break-all;
      }
    }
  }

  .unfinished-file-delete {
    position: absolute;
    right: 10px;
    top: calc(50% - 12px);
  }
}

#table-imports {
  .imp-success {
    color: $success;
  }
  .imp-warning {
    color: $warning;
  }
  .imp-err {
    color: $error;
  }
}

.advance-settings {
  background-color: rgba($light-color, .5);
  border: 1px solid $medium-dark-color;
  border-radius: 5px;
  display: none;
  margin-top: 20px;
  padding: 0 20px;
}
.imp-form-fields {
  margin: 10px 0;
}
.imp-sep-list .check-label {
  display: block;
}
.check-label {
  margin-top: 5px;
}
.input-file-container div {
  margin-bottom: 10px;
}

/** imports/import.php**/

.table {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
}
.table-map-head,
.table-map-section,
.table-map-row {
  display: flex;
  @include media('<medium'){
    flex-direction: column;
  }
}
.table-map-cell {
  padding: 14px 10px;

  select,
  input:not([type="radio"]) {
    @include media('<1200px'){
      /* width: 100%; */
    }
  }

  & > .table-map-cell {
    padding: 0;
  }

  &.--cell-save {
    max-width: 180px;
    margin-right: 60px;
    @include media('<large'){
      margin-right: 15px;
    }
  }
  &.--cell-cancel {
    flex: 0 1 auto;
  }
  &.--cell-import {
    text-align: right;
    align-items: flex-end;
    flex: 1 1 auto;
  }
  &.--column-name {
    font-weight: 600;
    word-wrap: break-word;
  }
  &.--column-preview {

  }
  &.--column-destination {
    position: relative;
    .info-click {
      display: none;
      width: 24px;
      height: 24px;
      position: absolute;
      left: 10px;
      top: 14px;
      background-image: url('/admin/images/imports/info-light.svg');
      background-repeat: no-repeat;
      background-size: contain;
      cursor: pointer;

      @include media('<medium'){
        top: 16px;
        left: -20px;
      }

      &:hover {
        background-image: url('/admin/images/info-dark.svg');
      }
    }
    &.--has-notice {
      .info-click { display: block}
      .option-notice {
        position: absolute;
        display: none;
        z-index: 10;
        top: 0;
        left: -210px;
        width: 420px;
        background: $white;
        padding: 20px;
        box-shadow: 0 2px 20px 0 rgba(35, 46, 99, 0.4);
        cursor: help;
        @include media('<medium'){
          position: static;
          width: 100%;
        }
      }

    }
  }
  &.--column-action {
    width: 100%;
    display: flex;
    padding-top: 0;
    @include media('<medium'){
      flex-direction: column;
    }

    .--column-action-info {
      width: 50%;
      padding-left: 60px;
      @include media('<1200px'){
        width: 40%;
      }
      @include media('<medium'){
        width: 100%;
        padding-left: 0;
      }
    }
    .--column-action-select {
      width: 50%;
      padding-left: 50px;
      @include media('<1200px'){
        width: 60%;
      }
      @include media('<medium'){
        width: 100%;
        padding-left: 0;
      }

      .map-option {
        position: relative;

        .option-notice {
          top: -15px;
        }
      }

      .info-click {
        position: relative;
        left: 0;
        top: -4px;
        float: left;
      }
    }
  }
}
.table-map-section {
  flex-direction: column;
  margin-bottom: 25px;
}
.table-map-title {
  padding: 5px 12px;
  background: $light-color;
  border:1px solid $grey-medium-color;
  border-bottom: none;
  font-weight: bold;
  font-size: 16px;
  span {
    font-weight: normal;
  }
}
.table-map-head ,
.table-map-row:not(.--row-bottom-button) {
  flex-wrap: wrap;
  padding-left: 60px;
  padding-right: 20px;
  & > div {
    flex: 1 1 auto;
    width: calc(50%/2);
    @include media('<1200px'){
      width: calc(40%/2);
    }
    @include media('<medium'){
      width: 100%;
    }
  }
  & > div:nth-child(3) {
    flex: 2 1 auto;
    padding-left: 50px;
    width: 50%;
    @include media('<1200px'){
      width: 60%;
    }
    @include media('<medium'){
      width: 100%;
      padding-left: 10px;
    }
  }

}
.table-map-head  {
  position: static;
  background: $dark-color;
  color: $white;
  font-size: 14px;

  &.fixed-header {
    position: fixed;
    top: 0;
    z-index: 10;
  }
}
.table-map-row {
  border: 1px solid $grey-medium-color;
  & + .table-map-row {
    border-top: none;
  }

  &.--row-bottom-button {
    padding-left: 12px;
    & > div {
      width: auto;
    }
  }
  &.--valid {
    background-image: url('/admin/images/imports/alerte-ok-bleue.svg');
    background-repeat: no-repeat;
    background-position: left 20px top 10px;
  }
  &.--warning {
    background-image: url('/admin/images/imports/warning.svg');
    background-repeat: no-repeat;
    background-position: left 20px top 10px;
  }

  &.--linked {
    select:not([multiple]){
      background-image: url('/admin/images/input-select-alt.svg');
    }
  }

  &.--row-bottom-button {
    .table-map-cell {
      input {
        @include media('<1200px'){
          width: auto;
        }
      }
    }
  }

  &.--no-import {
    background: $grey-color;
  }
}

.map-form .map-option label {
  display: inline-block;
  // *display: inline;
  /* for IE7*/
  zoom: 1;
  /* for IE7*/
  // float: left;
  // text-align: right;
  width: auto;
  padding-right: 10px;
}

/** DRAG & DROP **/
.box__uploading,
.box__success,
.box__error {
  display: none;
}
.box.has-advanced-upload {
  background-color: #e2f3fc;
  outline: 2px dashed #4aa3ff;
  // outline-offset: -10px;

  .box__dragndrop {
    display: inline;
  }
}
.box.is-dragover {
  background-color: $medium-light-color;
  label {
    color: #fff;
  }
}
.box.is-uploading .box__input {
  visibility: none;
}
.box.is-uploading .box__uploading {
  display: block;
}
.box__file {
	width: 0.1px;
	height: 0.1px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
  z-index: -1;
  & + label {
    font-size: 12px;
    color: #8e8e8e;
    padding: 20px;
    display: block;
    text-align: center;
    font-weight: normal;
    cursor: pointer;
  }
  &:focus + label,
  &+ label:hover {
    text-decoration: underline;
  }
}

#table-imports {
  width: 100%;
}

#map-form {

	max-width: 1200px;

  .select_mapping {
    max-width: 100%;
  }
  .map-options-client {
    display: block;

    @include media('<467px') {
      margin-bottom: 10px;
    }

    .input-client {
      display: inline-block;
      width: 49%;
      max-width: 173px;

      @include media('<520px') {
        width: 100%;
        max-width: none;
      }
    }

    .buttonAddDell {
      @include media('<520px') {
        margin: 0 auto;
        width: 24px;
        display: block;
      }
    }
  }

  .map-option {
    input, select {
      width: 100%;
    }
  }

  // override import originel
  .map-options-client {
    height: auto;
    margin-left: 0 !important;
    clear: both;
    select {
      display: block;
      width: 100%;
      margin-top: 8px;
    }
    & > a {
      display: block;
      margin: 5px 0 15px 0;
    }
  }
  .map-options-client p {
    display: inline-block;
    margin-right: 0;
    margin-left: 0;
    padding: 0 5px;

    @include media('<520px'){
      display: block;
      width: 100%;
      text-align: center;
    }
  }
  .sepPayment {
    width: auto !important;
  }

}

.alert {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  align-items: flex-end;
  padding: 13px;
  @include media('<medium'){
    flex-direction: column;
    align-items: stretch;
  }

  &.--finished,
  &.--success {
    background-color: $alert-success-alt;
    border: 1px solid $alert-success;
    border-left: 7px solid $alert-success;
  }

  &.--warning {
    background-color: $alert-warning-alt;
    border: 1px solid $alert-warning;
    border-left: 7px solid $alert-warning;
  }

  &.--error,
  &.--danger {
    background-color: $alert-danger-alt;
    border: 1px solid $alert-danger;
    border-left: 7px solid $alert-danger;
  }

  &.--create,
  &.--pending,
  &.--processing,
  &.--info {
    background-color: $alert-info-alt;
    border: 1px solid $alert-info;
    border-left: 7px solid $alert-info;
  }

}

input[type="submit"].btn-delete ,
.btn-delete {
  background-image: url('/admin/images/supprimer.svg');
  background-color: transparent;
  background-size: contain;
  text-indent: -9999999px;
  border: none;
  width: 24px;
  height: 24px;


  &:hover {
    background-color: transparent;
    background-image: url('/admin/images/supprimer-hover.svg')
  }

}

.imp-auto {
  display: none;
}

#config-imp-periods {
  &.imp-periods {
    display: none;
    margin-top: 3px;
    select {
      margin: 0;
      width: auto;
    }

    .imp-period-values {
      margin-top: 3px;

      label {
        width: 73px;
        display: inline-block;
        text-align: right;
        padding-right: 3px;
      }
    }
  }
}

.btn-import-first, #table-map-file .btn-import-first {
  color: #fff;
  background-color: #5377FB;
  border-color: #5377FB;

  &:hover {
    background-color: #3d50df;
    border-color: #3d50df;
  }
}
/**
 * CSS de l'onglet configuration
 */

/* Information propriétaires */
table.w-600 {
    tbody td:first-child {
        width: 175px;
        @include media('<=large') {
            width: auto;
        }
    }
    input {
        vertical-align: middle;
    }
}

/* Contacts Propriétaire */
#table-config-contact {
    #cnt-sel {
        width: 25px;
    }
    #cnt-name {
        width: 250px;
    }
    #cnt-email {
        width: 175px;
    }
    #cnt-phone, #cnt-fax {
        width: 115px;
    }
    #cnt-types {
        width: 225px;
    }
}

/* Types de contacts */
#table-config-type-contact {
    width: 650px;
    #type-sel {
        width: 25px;
    }
    #type-name {
        width: 175px;
    }
    #type-cnt {
        width: 100px;
    }
}

#table-conf-contact {
    #cnt-sel {
        width: 25px;
    }
    #cnt-name {
        width: 250px;
    }
    #cnt-email {
        width: 175px;
    }
    #cnt-phone, #cnt-fax {
        width: 115px;
    }
    #cnt-types {
        width: 225px;
    }
}

/* Zones de livraison */
#table-config-zones, #table-config-services {
    #name {
        width: 325px;
    }
    #is-active {
        width: 100px;
    }
}

/* Services de livraison */
#table-conf-livr-service-edit {
    tbody tr {
        td.delais {
            input.qte {
                width: 40px !important;
                text-align: right;
            }
        }
        input[type="text"]{
            vertical-align: middle;
        }
        @include media('<large') {
            .align-right, .align-center {
                text-align: left !important;
            }
        }
    }
    tr.th-head-second ~ tr:not(:last-child) {
        border-bottom: 1px solid $grey-medium-color;
    }
}

/* Magasins */
#list-stores {
    #ref {
        width: 125px;
    }
    #name {
        width: 225px;
    }
    #address, #contact {
        width: 275px;
    }
}

#tb-day-exp {
    width: 625px;
    margin-bottom: 0 !important;
    #th-width-day-exp th{
        width: 125px;
        background-color: $bg-blue-color;
        &:first-child {
            width: 95px;
        }
    }
    .form-control{
        width: 50px !important;
    }
}

#hr-magasin-stat {
    margin-top: 50px;
    margin-bottom:50px;
}

#edit-employee {
    tr:first-child td:first-child {
        width: 150px;
    }
}
.employee-image {
    min-height: 165px;
}

#site-content #employees {
	margin: 10px 0;
	list-style: none;
    font-size: 0;
    li {
        display: inline-block;
        vertical-align: middle;
        margin: 10px;
        font-size: 12px;
    }
    .employee {
        min-height: 64px;
        min-width: 64px;
        box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    }
    .employee-info {
        padding: 10px;
        .employee-edit {
            display: block;
            margin-top: 10px;
            text-align: center;
        }
    }
    .del-selected {
        box-shadow: #ABB2FF 0px 0px 10px, 0 3px 6px rgba(0,0,0,0.23);
    }
    .employee-image{
        margin: 15px auto;
        width: 150px;
        cursor: pointer;
        img {
            margin-top: 15px;
        }
    }
    .employee-add {
        display: block;
        background: #e0e0e0;
        color: #0e90d2;
        border-radius: 50%;
        padding: 30px 15px;
        text-decoration: none;
        &:hover {
            background: #c0c0c0;
        }
    }
}

#site-content,
#popup-content {
    .list-shop-image {
        li {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
            position: relative;

            p {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                padding: 5px 0;
                text-align: center;
            }
        }
    }
}



/* Services / postes */
#table-services, #table-postes {
    thead th{
        &#cat-sel {
            width: 20px;
        }
        &:last-child {
            width: 175px;
        }
    }
}

#table-services-edit, #table-poste-edit {
    tbody th {
        &:first-child td:first-child {
            width: 100px;
        }
    }
}

/* Dépôts */
#table-deposits {
    thead th{
        &#cat-sel {
            width: 25px;
        }
        &#cat-name {
            width: 425px;
        }
        &#cat-principal {
            width: 100px;
        }
    }
}

#table-services-edit, #table-poste-edit, #table-fiche-classe {
    tbody th {
        &:first-child td:first-child {
            width: 100px;
        }
    }
}

/* Gestion des retours */
#table-reasons {
    thead th {
        &:first-child {
            width: 25px;
        }
        &#reason-name {
            width: 525px;
        }
        &#cat-publish {
            width: 50px;
        }
    }
}

/* Structure des données > classes */
#table-config-classes {
    #cls-check {
        width: 25px;
    }
    #cls-name {
        width: 300px;
    }
    #cls-nb-fld {
        width: 100px;
    }
    #cls-nb-obj {
        width: 125px;
    }
}

#table-fiche-classe, #fields-classe-object {
    tbody td {
        &:first-child td:last-child {
            width: 200px;
        }
        #parent-name {
            width: 311px;
        }
    }
}

#fields-classe-object {
    tbody td {
        vertical-align: middle;
    }
}

#fields-classe-objects {
    #obj-check {
        width: 25px;
    }
    #obj-name {
        width: 220px;
    }
    #obj-childs {
        width: 105px;
    }
}

/* Models */
#models {
    #name {
        width: 325px;
    }
    #fields, #objects {
        width: 125px;
    }
}

#fields {
    #fld-select {
        width: 25px;
    }
    #fld-name, #fld-type, #fld-cat {
        width: 275px;
    }
    #fld-pos {
        width: 100px;
    }
}

/* Champs Personnalisés */
#table-champs-personnalises {
    thead tr th {
        width: 300px;
    }
}

.tb-new-custom-field {
    width: 670px;
}

/* Champs Presonnalisés / Unités de mesure */
#categories, #table-units {
    #name {
        width: 325px;
    }
    #pos, #symbol {
        width: 75px;
    }
    tbody td:last-child:not(:only-child) {
        text-align: center;
    }
}

/* Segments */
#obj-seg {
    thead tr th{
        width: 200px;
        &:first-child {
            width: 25px;
        }
        &:last-child {
            width: 75px;
        }
        
        @include media('<large') {
            &:first-child {
                width: 100%;
            }
        }
    }
}

#form-segment {
    tbody tr:first-child td{
        &:first-child {
            width: 140px;
        }
    }
}

/* CGV */
#site-content table#table-cgv {
    #cgv-sel {
        width: 25px;
    }
    #cgv-name {
        width: 450px;
    }
    #cgv-pub {
        width: 175px;
    }
    .current {
        font-weight: bold;
    }
}
#site-content table#table-fiche-version{
    width: 768px;
    .publish {
        & > td + td {
            display: flex;
            & > * + * {
                margin-left: 15px;
            }
        }
    }
}

#site-content table#cgv {
    width: 768px;
    #art-sel {
        width: 25px;
    }
    .td-art-name {
        width: 450px;
    }
    .td-art-move {
        width: 50px;
    }
}

#table-cgv-edit-article {
    tbody tr.first td:first-child {
        width: 150px;
    }
}

/* Paramètres de tarification */
#table-param-cat-tarifaires, #table-produits-exclure {
    #prc-sel, #prd-sel {
        width: 25px;
        padding-top: 3px;
    }
    #prc-name {
        width: 400px;
    }
    #prc-ttc {
        width: 75px;
    }
    #prc-users {
        width: 100px;
    }
}
#table-produits-exclure {
	width: 625px;
}

#table-cat-tarifaire-site {
    td {
        width: 200px;
    }
}

.prc-default-options {
    margin-top: 12px;
    margin-bottom: 12px
}

#prc-default {
    margin-top: 12px;
}

#table-type-relations {
    #rel-type-sel, td:first-child{
        width: 25px;
    }
    #rel-type-nom {
        width: 250px;
    }
    #rel-type-nom-pluriel {
        width: 100px;
    }
}

/* Adresses emails */

/* Catalogue */
#table-config-type-relation {
    .tdleft {
        width: 145px;
    }
}

/* Référencement */
#form-referencement-site {
    #table-conf-referencement {
        tbody tr:first-child {
            th:first-child {
                width: 150px;
            }
            th:last-child {
                width: 300px;
            }
        }
    }
    .label-option {
        margin-right: 10px;
    }
    #custom-p {
        margin-top: 30px;
    }
}

#conf-stats {
    thead th {
        &:first-child {
            width: 20px;
        }
        &:last-child {
            width: 400px;
        }
    }
}

#table-edit-personnalisation {
    tbody tr:last-child td {
        width: 200px;
        &:first-child {
            width: 200px;
        }
    }
}

#class-select, #tag-select {
    label {
        width: 130px;
        display: inline-flex;
    }
}

#edition-table {
    width: 705px;
    max-width: 100%;
    tbody {
        padding-right: 10px;
        td {
            max-width: 100%;
            select#constant-selector {
                height: 100% !important;
                background-image: none !important;
                overflow: hidden;
                width: 200px !important;
            }
            #sentence {
                width: 400px;
            }
            #save-column {
                width: 50px;
            }
            &:last-child {
                width: 200px;
            }
        }
    }
}

#sentences-table {
    margin-top:10px;
    tr {
        border-collapse: collapse;
        td {
            &.sentence-text {
                width: 600px;
                border-right:1px solid #CCCCCC;
                padding:10px;
                vertical-align:middle;
                @include media('<large') {
                    border-right: 0;
                }
            }
            &.td-action {
                width: 104px;
                text-align:center;
            }
        }
        &:last-child {
            @include media('<large') {
                border-bottom: 0;
            }
        }
    }

}

/* Redirections */
#search-url {
    margin-bottom: 10px;
}
#lst-error-404 {
    width: 100%;
    #url {
        width: 325px;
    }
    #count {
        width: 95px;
    }
    #site {
        width: 120px;
    }
    #lng {
        width: 95px;
    }
    tr {
        vertical-align: middle !important;
        img{
            border:medium none;
            cursor:pointer;
            width:16px;
            height: 16px;
            margin: 5px 0 0 4px;
            @include media('<large') {
                margin: 5px;
            }
            vertical-align: middle;
            display: inline-block;
        }
        input[type=text]{
            width: calc(100% - 20px);
            display: inline-block;
            float: left;
            vertical-align: middle;
        }
        &.pos-center{
            text-align: center;
        }
    }
    .error-resolved {
        vertical-align: middle;
        text-align: center;
    }
}

#lst-redirections {
    width: 100%;
    #titleRedirection {
        margin-top: 5px;
    }
}

#sh-redirection select#type {
    width: auto;
}

/* Horaires d'expéditions */
.tb-day-exp1 {
    thead tr th {
        &#day-exp {
            width: 75px;
        }
        &#period-exp {
            width: 220px;
        }
        &#action-exp {
           width: 80px;
        }
    }
}

#tb-holidays {
    thead tr#th-year th {
        width: 200px;
    }
}

#tb-closing {
    #clg-start, #clg-end {
        width: 130px;
    }
    #clg-action {
        width: 20px;
    }
    td.td-info, td.td-date{
        vertical-align: middle;
    }
}

/* Traduction */
#div-orange {
    padding-bottom: 5px;
    margin-bottom: 10px;
}

#tb-translate{
    #tsk_checked {
        width: 20px;
    }
    #tsl_context {
        width: 50px;
    }
    #tsl_original, #tsl_translate {
        width: 250px;
    }
    #action {
        width: 20px;
    }
    thead {
        tr:last-child th {
            background-color: $bg-blue-color;
        }
    }
}

/* modification des inputs trop grands */
dd input.col-numeric, input#min_amount, input#days_valid, select#apply_on {
    width: auto;
}

/* Règlements par virement */
#tb-transfer {
    tbody {
        &#transfert-positif tr:first-child td {
            &:first-child {
                width: 180px;
            }
            &:nth-child(2) {
                width: 500px;
            }
        }
        #trf-delete {
            width: 20px;
        }
        #trf-describe {
            width: 400px;
        }
    }
}

/* Adresses IP Filtrées */
#tb-ip-filtrees {
    #flt-check {
        width: 25px;
    }
    #flt-name {
        width: 300px;
    }
}

#tnt-filters {
    tbody tr:first-child td {
        &:first-child {
            width: 150px;
        }
        &:nth-child(2) {
            width: 300px;
        }
    }
}

/* Cartes cadeaux */
#list-prd {
    #check-del {
        width: 28px;
    }
    #prd-title {
        width: 475px;
    }
    #prd-amount {
        width: 100px;
    }
}

/* Campagnes */
#site-content #table-dob {
    tr {
        &.details td {
            &:first-child {
                width: 230px;
            }
            &:nth-child(2) {
                width: 400px;
            }
        }
        td {
            select#select-discount-type {
                width: 100px;
            }
            &.pmt-off-srv{
                padding-top: 10px;
            }
            select {
                width: 200px;
            }
            fieldset{
                margin: 10px;
                div input{
                    width: 200px;
                }
            }
        }

        ul{
            margin-top: 0px;

        }
        ul.pmt-list-service  li{
            list-style:none;
            margin:0px;
        }
        .pmt-list-choose{
            padding: 3px 10px !important;
        }

        // th {
        //     background: $dark-color;
        //     color:$white;
        // }

        .pmt-list-discount {
            padding: 0 !important;
        }
    }
}

/* Instagram */
#formulaire_identifiants_instagram {
    label {
        width: 135px;
        display: inline-block;
    }
    input {
        width: 170px;
    }
}

/* Avis Vérifiés */
#formulaire_identifiants_avis_verifie {
    label:first-child {
        width: 200px;
        display: inline-block;
    }
}

.config-generation-pdf {
    & * {
        vertical-align: middle !important;
    }
    .pdf-header {
        label {
            display: inline-block;
            width: 80px;
        }
    }
    .pdf-logo {
        label {
            display: inline-block;
            width: 155px;
        }
    }
    .config-row {
        margin-top: 15px;
    }
    img {
        margin-bottom: 10px;
    }
}

#config-pdf-quote {
    clear: both;

    .notice {
        margin: 0 0 1rem 0;
        padding: 1rem;

        &:first-of-type {
            margin-bottom: 1rem;
        }
    }

    .default-site-url {
        font-weight: 600;
    }

    .flex-column {
        flex-direction: column;
    }

    .form-group {
        align-items: center;
        display: flex;

        label {
            margin-right: 0.5rem;
        }
    }

    .logo-form-group {
        margin-top: 1rem;
    }
}

#form-delivery-options {
    dl {
        .description {
            color: #606F7B;
            margin-bottom: 0.75rem;

            p {
                margin: 0;
            }
        }

        > dd input {
            @extend .valign-center;
        }
    }

    .buttons {
        margin-top: 1rem;

        button:not(:last-of-type) {
            margin-right: 0.25rem;
        }
    }
}

/* style pour la page config extranet */
.cfg-extranet{
    p {
        line-height: 19px;
        color: #232E63;
        margin: 0;
    }

    select{
        width: 100%;
    }

    .head-title{
        display: flex;
        align-items: stretch;
        justify-content: space-between;

    }
    
    .domain-row{
        input, select {
            width: 47%;
            display: inline-block;
        }
        span{
            line-height: 25px;
        }
    }

    .domain-button{
        background-color:#5377FB;
        border-color:#5377FB;
        align-self: center;
        color:#fff;
        margin: 0px 0px;
        padding:10px 40px 10px 20px;

        &:hover{
            background-color:#3d50df;
            border-color:#3d50df;
        }
    }
    .current-logo{
        border: 1px solid #A9A9A9;
        padding: 5px;
        border-radius:5px;
        text-align: center;
        margin-bottom: 5px;
    }
    .action-box-info{
        color:#A9A9A9;
        text-align: center;
        padding: 4px 0;
        margin-bottom: 10px;
    }

    .box{
        margin: 10px 0;
    }

  .block-actions {
    display: flex;
    @include media('<medium'){
      flex-direction: column;
    }
    & > div:first-child {
      margin-left: 0;
    }
    & > div:nth-child(3) {
      margin-right: 0;
    }
  }

  // block-action global
  .block-action {
    width: calc(100% / 3);
    margin: 10px 10px 20px 10px;
    padding: 20px;
    border: 1px solid $medium-light-color;
    border-radius: 4px;
    box-shadow: 0 2px 10px 0 #d2d2d2;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    @include media('<medium'){
      width: 100%;
      margin: 10px 0;
    }

    h4{
        border-bottom: 0px;
    }
    .title {
      font-size: 17px;
      color: $dark-color;
      border-bottom: none;
      padding-left: 45px;
      margin-top: 0;
      position: relative;

      &:before {
        position: absolute;
        content: "";
        left: 0;
        top: 50%;
        margin-top: -18px;
        width: 35px;
        height: 35px;
        background-repeat: no-repeat;
        background-size: contain;
      }
    }

    &.--logo .title:before {
      background-image: url('/admin/images/config/import.svg');
    }
    &.--social .title:before {
      background-image: url('/admin/images/config/reseaux.svg');
    }
    &.--color .title:before {
      background-image: url('/admin/images/config/couleurs.svg');
    }
    &.--publication .title:before {
      background-image: url('/admin/images/config/publication.svg');
    }
    &.--logo {
      display: block;
      position: relative;

      form {
        height: 100%;
        display: block;
        position: relative;
        padding-bottom: 48px;

        .input-file-container {
          flex: 1 1 auto;
        }

        & > div:first-child {
          margin-bottom: 20px;
        }

        select,
        input[type="password"] ,
        input[type="text"] {
          width: 100%;
        }

        label {
          color: #7b82a1;
        }

        button {
          position: absolute;
          bottom: 0;
          margin-left: auto;
          width: 100px;
          margin-right: auto;
          left: 0;
          right: 0;
        }

      }
    }
    
    .color-group{
        border-radius: 5px;
        display:none;
        overflow: hidden;
        margin-top: 10px;

        &.color-selected{
            display:block;
        }
        .color-bar{
            height: 25px; 
            width: 100%;
        }
    }
    .row-social{
        display: flex;
        padding: 5px 0px;
        vertical-align: middle;
        align-items: center;

        .input-social{
            padding: 2px 10px; 
            width: 100%;
            display: inline-block;
            height: 27px!important;
            max-width: 600px;
            line-height: 27px;
        }

        .ico{
            height: 25px; 
            width: 25px; 
            overflow: hidden;
            background-size:25px 25px; 
            background-repeat: no-repeat;
            display: inline-block;
            margin:0 10px 0 0;
            border-radius: 4px;
        }
        
        &.row-facebook .ico{
            background-image: url('/admin/images/config/facebook.svg');
        }
        &.row-instagram .ico{
            background-image: url('/admin/images/config/instagram.svg');
        }
        &.row-linkedin .ico{
            background-image: url('/admin/images/config/linkedin.svg');
        }
        &.row-flickr .ico{
            background-image: url('/admin/images/config/flickr.svg');
        }
        &.row-pinterest .ico{
            background-image: url('/admin/images/config/pinterest.svg');
        }
        &.row-twitter .ico{
            background-image: url('/admin/images/config/twitter.svg');
        }
        &.row-viadeo .ico{
            background-image: url('/admin/images/config/viadeo.svg');
        }
        &.row-youtube .ico{
            background-image: url('/admin/images/config/youtube.svg');
        }
    }

    button, .button {
      margin-top: 15px;
      padding: 10px 20px;
      font-size: 12px;
      text-align: center;

    }
    a.button[target="_blank"] {
      padding-right: 35px;
      margin-right: 0;
    }
    .field-helper {
      color: $grey-medium-color;
    }
    .box__uploading,
    .box__success,
    .box__error {
      display: none;
    }
    .box.has-advanced-upload {
      background-color: $duck-egg-blue;
      outline: 2px dashed #4aa3ff;

      .box__dragndrop {
        display: inline;
      }
    }
    .box.is-dragover {
      background-color: $medium-light-color;
      label {
        color: $white;
      }
    }
    .box.is-uploading .box__input {
      visibility: none;
    }
    .box.is-uploading .box__uploading {
      display: block;
    }
    .box__file {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
      z-index: -1;
      & + label {
        font-size: 12px;
        color: $grey-medium-color;
        padding: 20px;
        display: block;
        text-align: center;
        font-weight: normal;
        cursor: pointer;
      }
      &:focus + label,
      &+ label:hover {
        text-decoration: underline;
      }
    }
  }


}
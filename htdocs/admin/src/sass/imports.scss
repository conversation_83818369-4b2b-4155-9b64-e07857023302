@import "node_modules/include-media/dist/_include-media.scss";
@import "variables";

/**
 * CSS des imports
 * Outil > imports
 */
 
 /* styles de base si JS est activé */
.imp-form{
    margin-top: 10px;
  }
  .imp-form-fields {
    margin: 10px 0;
  }
  .check-label {
    margin-top: 5px;
  }
  
  .advance-settings {
    background-color: #f3f4ff;
    border: 1px solid #5250b3;
    border-radius: 5px;
    display: none;
    margin-top: 20px;
    padding: 0 20px;
  }
  
  .imp-sep-list .check-label{
    display: block;
  }
  .check-label .check-box {
    vertical-align: middle;
    margin-right: 5px;
  }
  .check-label .check-span {
    vertical-align: middle;
  }
  
  .imp-period-values {
    display: inline-block;
  }
  
  .text-separator {
    max-width: 36px;
  }
  .map-header {
    list-style: none;
    background-color: #232E63;
    color: white;
    margin-bottom: 0 !important;
  }
  .map-header .map-head {
    display: inline-block;
    font-weight: 600;
  }
  
  tbody .imp-err {
    color: red;
  }
  tbody .imp-warning {
    color: orange;
  }
  tbody .imp-processing {
    color: blue;
  }
  tbody .imp-success {
    color: green;
  }
  .input-file-container div{
    margin-bottom: 10px;
  }
  .imp-checkall {
    margin-left: 4px;
  }
  /*.input-file-trigger {
    display: block;
    padding: 25px 45px;
    background: #232E63;
    color: #fff;
    font-size: 1em;
    transition: all .4s;
    cursor: pointer;
    text-align: center;
  }
  .input-file {
    position: absolute;
    top: 0; left: 0;
    width: 225px;
    padding: 14px 0;
    opacity: 0;
    cursor: pointer;
  }
   
  !* quelques styles d'interactions *!
  .input-file:hover + .input-file-trigger,
  .input-file:focus + .input-file-trigger,
  .input-file-trigger:hover,
  .input-file-trigger:focus {
    background: #34495E;
  }
   
  !* styles du retour visuel *!
  .file-return {
    margin: 0;
  }
  .file-return:not(:empty) {
    margin: 1em 0;
  }
  .file-return {
    font-style: italic;
    font-size: .9em;
    font-weight: 600;
    text-align: center;
    position: absolute;
    top: 14px;
    right: -434px;
  }
  !* on complète l'information d'un contenu textuel
     uniquement lorsque le paragraphe n'est pas vide *!
  .file-return:not(:empty):before {
    content: "Fichier séléctionné : ";
    font-style: normal;
    font-weight: 500;
  }*/
  .map-form {
    overflow-x: auto;
    margin-bottom: 50px;
  }
  .map-form .map-row {
    margin-top: 15px;
    position: relative;
    padding: 10px;
    min-height: 50px;
  }
  .loading{
    opacity: 0.2;
  }
  .map-form .map-menu {
    margin-top: 20px;
    float: right;
  }
  .map-form .map-select {
    display: flex;
    flex-wrap:  wrap;
    align-items: top;
    min-width: 600px;
  }
  
  .select_mapping {
    max-width: 290px;
  }

  .map-form .map-select label {
    display: inline-block;
    width: 300px;
    position: relative;
    padding-top: 6px;
    font-weight: 600;
  }
  .map-form .map-select label .right-arrow {
    display: block;
    background-size: cover;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 10px;
    top: 0;
  }
  #site-content .map-form .map-select select {
    display: inline-block;
    vertical-align: top;
    width: auto;
  }
  .map-form .map-select .map-error,
  .map-form .map-select .map-preview {
    width: 100%;
  }
  
  .map-form .map-select .notice {
    white-space: pre-line;
    margin-top: 10px;
  }
  .map-form .map-options {
    display: inline-block;
  }
  .map-form .clear {
    clear: both;
  }
  .map-form .map-option {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  #site-content .map-form .map-option input:not([type="radio"]),
  #site-content .map-form .map-option select {
    margin-bottom: 5px;
    *display: inline;     /* for IE7*/
    zoom:1;              /* for IE7*/
    vertical-align:middle;
    width: 100%;
  }
  .map-form .map-option label{
    display:inline-block;
    *display: inline;     /* for IE7*/
    zoom:1;              /* for IE7*/
    float: left;
    text-align: right;
    width: auto;
    padding-right: 10px;
  }
  .map-form .option-sep {
    max-width: 50px;
  }
  table.tablesorter thead tr th {
    text-align: right;
  }
  .map-form .map-option {
    margin-bottom: 10px;
  }
  .map-preview {
    margin-top: 5px;
    position: relative;
  }
  .map-preview-list {
    border: 1px solid #a3a3a3;
    border-bottom: none;
    list-style: none;
    display: none;
    /*position: absolute;*/
    background-color: #fff;
    z-index: 2;
  }
  .map-preview-show {
    display: block;
  }
  #site-content .map-preview-list .map-preview-item{
    border-bottom: 1px solid #a3a3a3;
    margin: 0;
  }
  #site-content .map-preview-list .map-preview-item .map-preview-number{
    padding: 0 5px;
    background-color: #eee;
    border-right: 1px solid #a3a3a3;
    margin: 0px 5px 0px 0px;
    display: inline-block;
    width: 20px;
  }
  #site-content .map-preview-list .map-preview-item:first-child{
    background-color: #efefef;
    padding-left: 25px;
  }
  
  .file-transfert-progress .chart {
      position:relative;
      margin:80px;
      width:220px; height:220px;
  }
  .file-transfert-progress .chart canvas {
      display: block;
      position:absolute;
      top:0;
      left:0;
  }
  .file-transfert-progress .chart span {
      color:#555;
      display:block;
      line-height:220px;
      text-align:center;
      width:220px;
      font-family:sans-serif;
      font-size:40px;
      font-weight:100;
      margin-left:5px;
  }
  
  .map-options-client{
      height:24px;
      margin-left:15px !important;
  }
  
  .map-options-client select{
      float:left;
      width:auto !important;
      
  }
  
  .map-options-client p{
      float:left;
      margin-right:3px;
      margin-left:3px;
      
  }
  
  .map-options-client input{
      float:left;
      width:auto !important;
  }
  
  .buttonAddDell{
      margin-left:3px;	
  }
  
  .sepPayment{
      width:143px !important;
  }
  
  
  #table-imports-unfinished strong{
    font-weight: 1000;
  }
  
  .imp-checkbox{
    width: 25px;
  }
  
  .imp-desc{
    min-width: 230px;
  }
  
//   @media screen and (max-width: 767px) {
//       #imp-file {
//           width: 97%;
//       }
//       .map-form .map-select {
//           width: auto;
//       }
//       .map-form .map-options {
//           margin: 0;
//           width: auto;
//       }
//       .map-form .map-option {
//           width: auto;
//       }
//       .map-form .map-row {
//           margin-top: 0;
//       }
//       #mapping-general {
//           width: 100%;
//       }
//       #site-content .map-form .map-select select {
//           margin-left: 0;
//       }
//   }
  
//   @media screen and (min-width: 1024px) and (max-width : 1180px ){
//     .map-form .map-option {
//       flex-direction: column;
//       align-items: stretch;
//     }
//     .map-form .map-option label {
//       text-align: left;
//     }
//     .map-form .map-option select, 
//     .map-form .map-option input:not([type="radio"]) {
//       width: 100% !important;
//     }
//   }
  
//   @media screen and (max-width : 600px ){ 
//     .map-form .map-select {
//       margin: 0;
//     }
//     .map-form .map-option {
//       flex-direction: column;
//       align-items: stretch;
//     }
//     .map-form .map-option label {
//       text-align: left;
//     }
//     .map-form .map-option select, 
//     .map-form .map-option input {
//       width: 100% !important;
//     }
  
//   }

#table-map-file { 
  * {
    vertical-align: top;
  }
  th:nth-child(-n+2) {
      width: 300px !important;
      min-width: 300px !important;
  }
  
  tr, tr.map-row {
      border-bottom: 1px solid $grey-medium-color;
  }
}

/* Responsive */
@include media('<sxlarge') {
    #table-map-file {
        thead, 
        tbody, 
        tr, 
        th, 
        td,
        ::before{
            display: block;
            width: 100%;
        }
        .map-select {
            display: flex;
            flex-direction: column !important;
            margin: 0;
        }
        .thead-none {
            display: none !important;
        }
        [data-thead]::before {
            content: attr(data-thead);
            margin-top: 10px;
        }
    }
    .map-form .map-row, .option-notice {
        margin-top: 0 !important;
    }
}

@media (max-width: 650px) {
  .map-form .map-select {
    min-width: 0;
  }
  .map-form .map-select label {
    width: auto;
  }
}

@media (max-width: 350px) {
  #site-content .map-form .map-select select {
    width: 270px !important;
  }
}
$breakpoints: (small: 320px, smallmedium: 425px, medium: 768px, large: 1024px, sxlarge: 1120px, mlarge: 1230px, xlarge: 1400px);

/* Global */
$white: #fff;
$black: #000;

/* Bleu */
$dark-color: #232E63; // exemples d'utilisation : H2, caption
$medium-dark-color: #3D50DF; // exemples d'utilisation : Liens hypertextes (bleu "fluo")
$medium-color: #5377FB; // exemples d'utilisation :  couleur de fond bleue lorsqu'un onglet est sélectionné et la couleur de fond de l'onglet sélectionné dans les menus
$medium-light-color: #ABB2FF; // exemples d'utilisation : entêtes de niveau 2 actives/triées
$light-color: #DADCFF; // exemples d'utilisation : entêtes de niveau 2
$bg-blue-color: #F4F7FB; /* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières */
$bg-second-thead-color: #ecebff; /* exemples d'utilisation : bg de niveau 2 dans les entêtes de tableaux */
$foam-blue: #E3F3FC; /* exemples d'utilisation : infos */
$anakiwa-blue: #ACE1FD; /* exemples d'utilisation : infos border */

/* Gris */
$grey-medium-color: #A9A9A9; /* exemples d'utilisation : couleurs utilisées principalement pour  les bordures des tableaux */
$grey-color: #EEEEEE; /* exemples d'utilisation : footer, rollover */
$blue-grey: #7a869a; /* exemples d'utilisation : Couleur utilisée pour le custom du scroll */
$light-grey-blue: #b5b5be; /* exemples d'utilisation : Couleur utilisée pour le custom du scroll */

/* Vert */
$green: #008000; /* exemples d'utilisation : en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) */
$green-light: #ebffeb; /* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) // fusionné avec $positive-hover (#e5ffe5) */
$positive: #DDFFDD; /* exemples d'utilisation : // valeur positive */

/* warning (Jaune) */
$dull-yellow : #e6db55; /* exemples d'utilisation : Filet de block de texte pour les WARNING // Anciennement $yellow-medium */
$dull-yellow-pale : #fffbcc; /* exemples d'utilisation : bg de block de texte pour les WARNING // Anciennement $yellow-light */

/* success */
$aqua-marine: #50e3c2; /* exemples d'utilisation : Filet de block de texte pour les success */
$aqua-marine-pale: #dcf9f3; /* exemples d'utilisation : bg de block de texte pour les success */

/* info / notes */
$sky-blue : #73ceff; /* exemples d'utilisation : Filet de block de texte pour les notes */
$duck-egg-blue : #e2f3fc; /* exemples d'utilisation : bg de block de texte pour les notes */

/* danger */
$red: #ff0000; /* exemples d'utilisation : Filet de block de texte pour les alertes */
$red-pale: #ffdddd; /* exemples d'utilisation : bg de block de texte pour les alertes */
$red-pale-hover: #ffe5e5; /* exemples d'utilisation : survol d'un élément coloré en $red-pale */

/* Autres */
$warning: #f5a623;
$inactive: #FE5511;
$ghostwhite: #f8f8ff; // à supprimer si possible
$success: $green;
$error: $red;
$info: $foam-blue;
$info-border: $anakiwa-blue;

/* ALERT VARIABLES */
$alert-info: $sky-blue;
$alert-info-alt: $duck-egg-blue;
$alert-warning: $dull-yellow;
$alert-warning-alt: $dull-yellow-pale;
$alert-success: $aqua-marine;
$alert-success-alt: $aqua-marine-pale;
$alert-danger: $red;
$alert-danger-alt: $red-pale;

:root {
  --mdc-theme-primary: $dark-color;
}

// Breadcrumbs (Bootstrap 4)

$breadcrumb-font-size:              null !default;

$breadcrumb-padding-y:              .75rem !default;
$breadcrumb-padding-x:              1rem !default;
$breadcrumb-item-padding:           .5rem !default;

$breadcrumb-margin-bottom:          1rem !default;

$breadcrumb-bg:                     white !default;
$breadcrumb-divider-color:          black !default;
$breadcrumb-active-color:           black !default;
$breadcrumb-divider:                quote("•") !default;

$breadcrumb-border-radius:          0 !default;

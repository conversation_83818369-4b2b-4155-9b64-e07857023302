/* <PERSON> est amené à disparaitre */
/* Ne rien ajouter ici. Si possible, supprimer/déplacer les éléments de ce fichier */

/* Titre */
h1 {
	font-size: 14px;
	padding: 5px;
	padding-left: 10px;
	background-color: #303030;
}
h1 a, h1 a:visited {
	color: ghostwhite;
	text-decoration: none;
	font-family: Arial;
	font-size: 19px;
}
h1 a:hover {
	border-bottom: 1px solid ghostwhite;
}

/* Zone de contenu */
#popup-content #site-content{
    margin: 0;
    min-height: 0;
    min-width: 0;
}
body.nolayout{
	background:none;
	width: auto;
	min-width: 0;
}
.nolayout #site-content{
	margin: 0;
	min-width: 0;
	padding: 15Px;
}
h3{
	border-bottom:1px solid #A9A9A9;
	font-size:14px;
	margin-bottom:10px;
	margin-top:15px;
	padding-bottom:3px;
}

h4{
	border-bottom:1px solid #A9A9A9;
	font-size:14px;
	margin-bottom:10px;
	margin-top:15px;
	padding-bottom:3px;
}
h1, h2, h3, h4 {
	&.no-border {
		border: none;
	}
}
dl {
	border-bottom: 1px solid #A9A9A9;
	padding: 10px 0;
}
ul {
	margin-top: 10px;
}
dl dt {
	margin-bottom: 3px;
	font-size: 14px;
	font-weight: 600;
}
hr {
	margin-bottom: 10px; margin-top: 10px;
	border-style: none;
	border-bottom: 1px solid #A9A9A9;
}
dl dt a {
	color: #232E63;
	text-decoration: none;
}
dl dt a:hover {
	text-decoration: underline;
}
li {
	margin: 3px 20px;
}
dl dd {
	margin: 3px 0;
}
p, ul {
	margin-bottom: 5px;
}
b {
	font-weight: 600;
	font-size: 1.4em;
}
address {
	font-style: normal;
	font-weight: 500;
}
option {
	font-weight: 500;
}
optgroup {
	font-weight: 600;
}
b, strong {
	font-weight: 600;
}
/* Résultats du moteur de recherche */
#site-content h2#search-results-title {
	margin-bottom: 0px;
	border-bottom-style: none;
	padding-bottom: 5px;
}
#site-content #type-result {
	padding: 1px 2px;
}
#site-content span#search-results-infos {
	display: block;
	margin-bottom: 10px;
	border-bottom: 1px solid #A9A9A9;
	padding: 3px 0;
}
#site-content #search-results {
	padding-bottom: 10px;
	clear: both;
}
#site-content dl.search-result {
	clear: both;
	min-height: 105px;
}
#site-content dl.search-result dt {
	margin-bottom: 0px; font-size: 1em;
	border-bottom-style: none; padding-bottom: 0px;
}
#site-content dl.search-result dd.search-result-img {
	float: left;
	width: 88px; height: 80px;
	clear: both;
}
#site-content .search-result dd {
	padding-left: 0; margin-left: 0;
}
#site-content .search-result .search-result-url, #site-content .search-result .search-result-url a {
	font-size: 0.9em; text-decoration: none;
}
#site-content .search-result .search-result-url a:hover {
	text-decoration: underline;
}
#site-content dl.search-result .search-result-tabs {
	display:none;
}
#site-content dl.search-result:hover .search-result-tabs {
	display:block;
}
#site-content .search-result .search-result-tabs ul {
	list-style-type:none;
}
#site-content .search-result .search-result-tabs ul li {
	display:inline;
	margin-left:0;
	margin-right:10px;
}
#site-content .search-result .search-result-tabs ul li a{
	text-decoration:none;
}
#site-content .search-result .search-result-tabs ul li a:hover {
	text-decoration:underline;
}
#site-content .search-paginator {
	display: flex;
	justify-content: space-between;
}
#site-content .search-page {
	padding-top: 3px;
	float: left;
}
#site-content .search-nav {
	padding-top: 3px;
	float: right;
}
#site-content .search-nav a {
	text-decoration: none;
}
#site-content .search-nav a:hover {
	text-decoration: underline;
}

/* Tableaux */
#site-content table.table-options{
	min-width: 75%;
}
#popup-content table {
	width: 100%;
	margin: 1px;
	@include media('<medium'){
		table-layout: fixed;
	}
}
#site-content table.nocaption {
	border: none;
}
#site-content table.nocaption tbody td{
	padding: 5px 0;
}
#site-content table caption a {
	color: white;
	text-decoration: none;
}
#site-content table caption a:hover {
	text-decoration: underline;
}
#site-content table table.tarif{
	margin-bottom: 0px;
	width: 600px;
}
#site-content table table.tarif tr{
    height: 20px;
}
#site-content table table.tableZone, #site-content table table.tablePrice{
	margin-bottom: 0px;
	margin-top: 0px;
	border-width:0px;
	border-collapse:collapse;
}
#site-content table table.services{
	margin-top: 0px;
	margin-bottom: 0px;
	border-width:0px;
}
#site-content table td, #popup-content table td {
	font-size: 12px;
}
#site-content table.list:not(#table-map-file) tbody td, #site-content table.checklist tbody td, #popup-content table.checklist tbody td, #site-content table.table-cgv tbody td {
	border-bottom: 1px solid #A9A9A9;
}
#site-content table.table-cgv {
	border-collapse: collapse;
}

#site-content table.list:not(#table-map-file) tbody td.td-nowrap, #site-content table.checklist tbody td.td-nowrap, #popup-content table tbody td.td-nowrap {
	white-space: nowrap;
}
#site-content table.checklist tbody tr:hover, #site-content .tb-kpi tr:hover, #site-content .sub-history tr:hover, #popup-content table.checklist tbody tr:hover {
	background-color: #f3f3f3;
}
#site-content table tfoot,#popup-content table tfoot,
#site-content table tfoot th, #popup-content table tfoot td {
	background-color: white;
}
#site-content table tfoot td.back-color-white {
	background-color: white;
}
#site-content table tfoot td.text-center {
	text-align: center;
}
#site-content table tbody input.datepicker {
	width: 130px;
}
/* #site-content table tbody input#barcode {
	width: auto;
} */
#site-content table tbody select.zone {
	width: 195px;
	margin-right: 4px;
	display:block;
	float:left;
}
#site-content table tbody input.zone {
	width: 191px;
}
#site-content table tbody input.del-obj-pmt {
	width: 16px;
	border-style: none;
	vertical-align: text-top;
	padding-top: 2px;
	margin-left: 5px;
}
#site-content table tbody input[type=submit] {
	width: auto;
}

#site-content table tbody input#taxcode {
	width: 145px;
}
#site-content #siret, #popup-content #siret,
#site-content #naf, #popup-content #naf
{
	width:auto;
}
#site-content table tbody select.large,
#popup-content table tbody select.large {
	width: 500px;
}
#site-content #prd-parents {
	height: 100px;
}
#site-content table tbody select.multiple {
	height: 250px;
}
#site-content table tbody textarea#descZone{
	height:150px;
}
#site-content table tbody textarea.referencing{
	height: 50px;
}
#site-content table tbody textarea#keywords,#site-content table tbody textarea.keywords {
	height: 260px;
}
#site-content table tbody textarea.ref-desc{
	height: 100px;
}
#site-content table tbody textarea.short,
#popup-content table tbody textarea#keywords, #popup-content table tbody textarea.short{
	height: 100px;
}
/* #site-content table tbody #ref, #site-content table tbody .ref, #popup-content table tbody #ref{
	width: 125px;
} */
#popup-content table tbody input.price, #popup-content table tbody select.price, #popup-content table tbody input.size, #popup-content table tbody input.number, #popup-content input.price,
#popup-content input.number,
#site-content table tbody input.price, #site-content table tbody select.price, #site-content table tbody input.size, #site-content table tbody input.number, #site-content input.price {
	width: 90px; text-align: right;
}
#site-content table tbody td.numeric, #site-content table tbody th.numeric, #site-content table tfoot th.numeric {
	text-align: right; white-space: nowrap;
}
#site-content table tbody input.code {
	width: 145px;
}
#popup-content #frm-pmt-test-prd, #popup-content #frm-pmt-test-usr, #popup-content #frm-pmt-usr{
	background-color:#CFCFCF;
	color:#232E63;
	margin:15px;
	padding:8px;
}
#popup-content div.error, #popup-content .pmt-result-exclude {
	color: red;
	padding: 8px;
	border: 1px solid red;
	margin: 8px;
}
#popup-content .pmt-result-include {
	color: $green;
	padding: 8px;
	border: 1px solid $green;
	margin: 8px;
}
.sale-type{
	padding:3px !important;
}
#table-poids-dimentions, #table-services-livraison {
	width: 450px;
}
#table-ajout-regle {
	width: 600px;
}
#table-pricewatching {
	margin-top: 5px;
}
#table-concurrents {
	margin-top: 5px;
}
#tableau-nomenclature {
	width: 500px;
}
table#rwp-rewards {
	width: 600px;
}
/* Taille des th au cas par cas - pour remplacer les colsgroup */
/* Cols */
#produit-general{
	width: 175px;
}
#td-field-1 {
	width: 220px;
}
#td-field-2 {
	width: 400px;
}
#hd-rewards-total, #hd-rewards-ttc, #hd-rewards-used, #hd-rewards-used-ttc, #hd-rewards-no-used, #hd-rewards-no-used-ttc{
	width: 140px;
}
#prc_resume_name{
	width: 200px;
}
#prc_resume_ht, #prc_resume_ttc {
	width: 80px;
}
#sync {
	width: 35px;
}
#information {
	width: 400px;
}
#conditions-prc {
	width: 650px;
}
#action-prc {
	width: 105px;
}
#ord-del{
	width: 20px;
}
#ord-id{
	width: 100px;
}
#ord-tref{
	width: 300px;
}
#ord-date{
	width: 150px;
}
#ord-products{
	width: 150px;
}
#ord-pos{
	width: 95px;
}
#td-table-nomenclature-1 {
	width: 150px;
}
#td-table-nomenclature-compo-1 {
	width: 135px;
}
#td-table-nomenclature-compo-3 {
	width: 50px;
}
#td-table-nomenclature-compo-4 {
	width: 80px;
}
#td-table-nomenclature-compo-5 {
	width: 30px;
}
.list-prd-relations-rel-check, #rel-is-sync {
	width: 20px;
}
.list-prd-relations-rel-ref {
	width: 150px;
}
#rel-publish, #rel-publish-site {
	width: 235px;
}
#rel-ord-pos {
	width: 85px;
}
#td-table-images {
	width: 180px;
}
#doc-sel {
	width: 25px;
}
#doc-remp {
	width: 280px;
}
#td-stock-conditionnement {
	width: 145px;
}
#prd-colisage {
	width: 325px;
}
table #qte, table #res, table #res_web, table #com, table #prepa, table #mini, table #maxi {
	width: 75px;
}
#td-services-livraison {
	width: 395px;
}
#th-author {
	width: 300px;
}
#th-message {
	width: 568px;
}
#tb-redirection #url {
	width: 705px;
}
#tb-redirection #action {
	width: 50px;
}
#td-tbl-ctr-mdl-1 {
	width: 245px;
}
#td-tbl-ctr-mdl-2 {
	width: 586px;
}
#th-tbl-export-1 {
	width: 185px;
}
#th-tbl-export-2 {
	width: 645px;
}
#th-tbl-export-3 {
	width: 830px;
}
#td-tbl-export-alone{
	width: 845px;
}
#td-ajout-regle-1 {
	width: 150px;
}
#td-ajout-regle-2 {
	width: 300px;
}
#th-pricewatching-1{
	width: 17px;
}
#th-pricewatching-2{
	width: 300px;
}
#th-pricewatching-3{
	width: 120px;
}
#th-concurrents-1, #th-concurrents-3, #th-concurrents-4, #th-concurrents-5 {
	width: 100px;
}
#th-concurrents-2{
	width: 250px;
}
#td-tableau-nomenclature-1 {
	width: 150px;
}
#td-tableau-nomenclature-1 {
	width: 330px;
}
#psidate {
	width: 180px;
}
#psi_label {
	width: 400px;
}
#brd-sel {
	width: 25px;
}
#brd-name {
	width: 275px;
}
#brd-url {
	width: 425px;
}
#brd-publish {
	width: 115px;
}
#brd-prd {
	width: 150px;
}
#td-avis-conso-1, #td-avis-conso-2 {
	width: 150px;
}
#td-no-ref-1 {
	width: 115px;
}
#td-no-ref-2 {
	width: 615px;
}
#td-no-ref-3 { /* #td-no-ref-1 + #td-no-ref-2 */
	width: 730px;
}
/* Codes promotions */
#popup-content .preview,
#site-content .preview {
	width: 152px; min-height: 152px; border: 1px solid #cccccc;
	text-align: center;
	float: left;
	margin-right: 5px; margin-bottom: 5px;
}
/* #site-content table tbody fieldset div {
	padding: 3px 0px;
}
#site-content table tbody #pmt-products, #site-content table tbody #pmt-customers {
	width: 608px;
}
#site-content #pmt-rules-buttons {
	text-align: right;
}
#site-content div.buttons {
	text-align: right;
	padding-top: 3px; padding-right: 3px;
}

#popup-content .preview,
#site-content .preview {
	width: 152px; min-height: 152px; border: 1px solid #cccccc;
	text-align: center;
	float: left;
	margin-right: 5px; margin-bottom: 5px;
}
#lst-pmt .bold{
	font-weight: 600;
}
#site-content table.prc-tva-eco td.information input.prd-ref{
	width:226px;
}
#frm-add-promo div.info label{
	float:left;
	font-weight: 600;
	text-align:left;
	width:210px;
}
#frm-add-promo div.info  input{
	width:200px;
}
#frm-add-promo div.info  input.date{
	width: 100px;
}
#site-content input.date {
	width: 82px !important;
}
#site-content input.qte {
	width: 42px !important;
}
#frm-add-promo table tbody div.conditions-next{
	margin-top:5px;
}
#frm-add-promo table tbody img.del-cdt{
	border:medium none;
	margin-left:5px;
	width:12px;
}
#tb-prd-promo tbody input, #frm-add-promo tbody select{
	width:169px !important;
}
#tb-prd-promo input.ref{
	width:145px !important;
}

#tb-promo-remise #remise-valeur-type{
	display : none;
} */

/* Gestion des listes et des tableaux */
tr.selected {
	background-color: #f0f0f0;
}
tr.deleted td {
	text-decoration: line-through;
	cursor: help;
}
tr.archived{
	background-color: $yellow-light;
}
.col-numeric{
	text-align: right;
}
.barre{
  text-decoration: line-through;
}

.pricewatching .negative {
	color: red;
}
.pricewatching .positive {
	color: $green;
}
.checked {
	color:$green;
}
.unchecked {
	color:red;
}
.waiting {
	color:orange;
}
.warning {
	color: #ee9b1b;
}
.warning a {
	color: #232E63;
}
.mandatory {
	color: #ff0000;
}

.btn-add {
	float: right;
}
#site-content .center {
	display: block;
	margin: 5px auto;
}
#site-content input.readonly {
	border: 2px solid white;
}
#site-content sub {
	display: block;
}

/* Avis consommateur */
#lbl-note-1, #lbl-note-2, #lbl-note-3, #lbl-note-4, #lbl-note-5, #lbl-note-6, #lbl-note-7, #lbl-note-8, #lbl-note-9, #lbl-note-10 {
	background-repeat: no-repeat;
	padding-left: 70px;
}
#lbl-note-1 i, #lbl-note-2 i, #lbl-note-3 i, #lbl-note-4 i, #lbl-note-5 i, #lbl-note-6 i, #lbl-note-7 i, #lbl-note-8 i, #lbl-note-9 i, #lbl-note-10 i {
	display: none;
}
#lbl-note-1 {
	background-image: url(../images/reviews/notes/2.gif);
}
#lbl-note-2 {
	background-image: url(../images/reviews/notes/4.gif);
}
#lbl-note-3 {
	background-image: url(../images/reviews/notes/6.gif);
}
#lbl-note-4 {
	background-image: url(../images/reviews/notes/8.gif);
}
#lbl-note-5 {
	background-image: url(../images/reviews/notes/10.gif);
}

/* Statistiques */
.selector-menu {
	border-bottom: 1px solid #A9A9A9;
}
.moderation-menu {
	height: auto;
  	margin-bottom: 10px;
}
/* #site-content img {
	border: 1px solid black;
} */
#site-content img.export-xls, #site-content .gm-style img {
	border: none;
}
#bestseller-prd, #calls-report {
	width: 100%;
}
/* Configuration */
.ref-menu, .ref-menu2{
	padding-bottom: 7px;
	border-bottom: 1px solid orange;
	text-align: right;
	margin-bottom: 7px;
}
.ref-menu2{
	border: none;
}
/* fieldsets */
#site-content fieldset {
	padding: 10px;
	display: block;
	border: 1px solid #A9A9A9;
}
/* #site-content fieldset, #site-content .fieldset {
	min-width: 545px;
	max-width: 100%;
	margin: 3px auto;
} */
#site-content fieldset div input.radio, #site-content fieldset div input.checkbox, #site-content #tabpanel-vertical div input.checkbox {
	width: auto;
}
#site-content #tabpanel-vertical div {
	padding: 3px;
}
#site-content fieldset div span.label, #site-content #tabpanel-vertical label, #site-content #tabpanel-vertical div span.label {
	display: inline-block;
	width: 158px;
	text-align: right;
	padding-right: 5px;
}
#site-content #tabpanel-vertical div label.inline {
	display: inline;
	float: none;
}
#site-content fieldset div.checkbox, #site-content #tabpanel-vertical div.checkbox {
	padding-left: 195px;
}
#site-content #tabpanel-vertical div input, #site-content #tabpanel-vertical div textarea {
	width: 345px;
}
#site-content #tabpanel-vertical div.actions input {
	width: auto;
}
#site-content #tabpanel-vertical div.actions {
	padding-left: 163px;
}

/* Newsletter */
.nlr-pre-inscript {
	color: GoldenRod;
}
.nlr-inscript {
	color: $green;
}
.nlr-pre-uninscript {
	color: orange;
}
.nlr-uninscript {
	color: red;
}

.edit-cat {
	padding-bottom: 5px;
	display: inline;
	font-size: 13px;
	margin-left: 5px;
	font-weight: 500;
	text-decoration: none;
	vertical-align: middle;
}
.edit-cat:hover {
	text-decoration: underline;
}
.edit-cat::before {
	content: '';
	background-image: url('/admin/dist/images/configuration_off.svg');
	background-position: left middle;
	background-repeat: no-repeat;
	width: 20px;
	height: 20px;
	display: inline-block;
	vertical-align: middle;
}
#display-cols-options.edit-cat::before {
	background-image: url('/admin/dist/images/configuration.svg');
}
p.note {
	margin-top: 6px;
	font-size: 0.9em;
	border: 1px solid #ccc;
	color: black; background-color: #FFFFE7;
	padding: 0.5em;
}

/* Systeme de listes de definition pliable/depliables */
dl.fold dt, dl.unfold dt {
	padding-left: 16px;
	background-repeat: no-repeat;
}
dl.fold dd, dl.unfold dd {
	padding-left: 32px;
}
dl.fold dt {
	background-image: url(../images/folding/closed.gif);
}
dl.unfold dt {
	background-image: url(../images/folding/opened.gif);
}
dl.fold dd {
	display: none;
}
dl.unfold dd {
	display: block;
}
.img-stat-search{
	border: medium none !important;
	vertical-align: middle;
	margin-right: 5px;
}
.img-stat-search:hover{
	cursor: pointer;
}
#error.errorjs{
	display:none;border-bottom:solid 1px red;position:fixed;padding:10px;top:0px;background-color:#FFDDDD;width:100%; text-align:center;
}
.popup_img {
	min-width: 0;
	background-image: none;
}
.popup_img .listes{
	display: none;
}
.popup_img .liste{
	border: solid 1px #EFEFEF;
}
.popup_img .listes .att{
	width:100%;
	margin: 10px;
	border-bottom: solid 1px grey;
	padding-bottom: 10px;
}
.popup_img .tr_clicable .clicable{
	cursor:pointer;
}
.popup_img .tr_clicable th{
	border-bottom: solid 1px black;
}
.popup_img .listes .att .img{
}
.popup_img .listes .att .leg{
	margin-left: 10px;
}
.popup_img .listes .att .leg ul{
	list-style-type: none;
}
.clear{
	clear: both;
}
.noborder{
	border: none !important;
}
.file{
	width: 250px !important;
}

/* pour la fenetre de duplication d'un produit */
#popup_import{
	position: absolute;
	margin-left: auto;
	margin-right: auto;
	top: 200px;
	height: 250px;
	width: 300px;
	display: none;
	background-color: white;
	border: 1px solid black;
	padding: 20px;
	z-index:99999;
}
#popup_import #dp_choise input{
	margin-right: 5px;
}
#popup_import .dp_choise{
	display: none;
}
#popup_import #dp_choise{
	display: none;
	margin-top: 10px;
}
#popup_import #dp_result{
	padding-top: 20px;
	padding-bottom: 20px;
}
#popup_import #content{
	height: 170px;
}
#popup_import .action{
	text-align: right;
}
#shadows{
	background-image: url(../images/shadows.png);
	display: none;
	width: 100%;
	position: absolute;
	top: 0px;
	left: 0px;
	height: 100%;
	z-index:0;
}
#site-content table.statsearch td{
	padding-right: 10px;
}
#lst_search img.loader{
	border:none;
}
/* pour la popup de la page statistique / recherche */
#statsearch_popup{
	position : absolute; 
	min-width: 500px;
	background-color: white;
	top: 0px;
	left: 0px;
	display: none;
	border: 1px solid #59749F;
}
#statsearch_popup.maxipopup{
	width: 881px;
}
#stat-popup-content, .popup-content{
	background-image:none;
	padding:15px;
	min-width:0;
}
.popup-content{
	padding: 0 15px;
}
#stat-popup-content table tfoot{
	height:50px;
}
#statsearch_popup .content div {
	padding-bottom: 4px;
}
#statsearch_popup .content a {
	text-decoration: none;
	color: black;
}
#statsearch_popup .content a:hover{
	text-decoration: underline;
}
#statsearch_popup .content{
	overflow: auto;
	border: 3px solid #232E63;
}
#statsearch_popup .statsearch_drag{
	width: 100%;
	height: 12px;
	background-color: #232E63;
	cursor: move;
	color: white;
	padding: 5px 0;
}
#statsearch_popup .statsearch_drag div {
	float: left;
}
#statsearch_popup .statsearch_drag .text{
	font-weight: 600;
}
#statsearch_popup .statsearch_drag .drag{
	background-image: url(/admin/images/stats/drag.gif);
	height: 13px;
	margin: 0 10px;
	width: 13px;
}
#statsearch_popup .statsearch_drag .close {
	background-image: url(/admin/images/stats/close.gif);
	height: 13px;
	width: 13px;
	margin: 0 10px;
	float: right;
}
#site-content table .form-rep{
	text-align:center;
}
#site-content table .form-rep input, #site-content table .form-rep textarea{
	width:350px;
}
#site-content #usr-contacts a,#site-content #usr-contacts a:visited{
	text-decoration:underline;
}
#site-content #usr-contacts .reply-view{
	padding-left:40px;
	padding-top:10px;
	padding-bottom:10px;
}
#site-content #usr-contacts .show-form-replay{
	display:block;
	margin-right:50px;
	float:right;
	width:90px;
}
#site-content #list_keyword{
}
#site-content .form-keywords h3{
	padding-bottom:10px;
	padding-top:10px;
}
#site-content .form-keywords select{
	display:block;
}
#site-content .form-keywords input, #site-content .form-keywords select{
	width:200px;
}
#site-content .form-keywords select{
	height:200px;
}
#site-content #class tbody tr td input.submit{
	width:100px;
}
#site-content .input-image-delete{
	width:16px;
	height:16px;
}
#stat-popup-content table tr{
	height:100px;
}
.wo-search-desc{
	color:black;
	margin-bottom:5px;
}
.wo-search-title{
	margin-bottom:5px;
}
.wo-search-url{
	color: #3D50DF;
	font-size:0.9em;
	text-decoration:none;
}
.wo-search-title a{
	font-size:1em;
	font-weight: 600;
	text-decoration:none !important;
}
.wo-search-title a:hover{
	text-decoration:underline !important;
}
.add-res-wo-search p{
	border-bottom:1px solid #C4C4C4;
	font-size:13px;
	font-weight: 600;
	padding-bottom:3px;
}
.add-res-wo-search label{
	display:block;
	margin-bottom:2px;
	margin-top:5px;
}
.add-res-wo-search textarea, .add-res-wo-search input{
	width:100% !important;
}
.add-res-wo-search .action{
	margin-top:10px;
	text-align:right;
}
.add-res-wo-search .action input{
	width:auto !important;
	font-size:11px;
}
.stat-search-iframe{
	border:none medium;
	height:100%;
	width:100%;
}
.stat-search-iframe table{

}
.stats-iframe{
	height:550px !important;
	width:875px !important;
}
.stats-seache-table{
	width:100%;
}
.stat-search-page{
	text-align:right;
}
.stat-num{
	border:medium none;
	margin:1px;
	padding:1px;
	text-align:center;
}
.zone-clic{
	background-image:url(../images/stat-search-click.jpg);
	height:60px;
	width:60px;
}
.zone-no-clic{
	background-image:url(../images/stat-search-no-click.jpg);
	height:60px;
	width:60px;
}
.num-clic{
	height:100%;
	padding-top:15px;
	text-align:center;
	width:100%;
}
.stat-info{
	padding-top:10px;
	vertical-align:text-bottom;
}
.stat-img{
	text-align:center;
}
.cnt-hide{
	color:#C4C4C4 !important;
}
.cnt-masked {
	display: none;
}
.cnt-hide-url{
	color:#C4C4C4 !important;
	font-size:0.9em;
	text-decoration:none;
}
.img-cnt-hide{
    opacity : 0.4;
    filter : alpha(opacity=40);
}
.del-search{
	float:left;
	font-size:11px;
	margin-right:25px;
	margin-top:10px;
	color:black !important;
}
.del-all-search{
	font-size:11px;
	margin-right:25px;
	margin-top:10px;
	color:black !important;
}
.search-no-publish{
	font-size:11px;
	margin-right:25px;
	margin-top:10px;
	color:black !important;
}
.del-search:hover, .del-all-search:hover{
	cursor:pointer;
}
.border-unright{
	border-bottom:2px solid;
	border-left:1px solid;
	border-top:1px solid;
}
.border-unleft-unright{
	border-bottom:2px solid;
	border-top:1px solid;
}
.border-unleft{
	border-bottom:2px solid;
	border-right:2px solid;
	border-top:1px solid;
}
#count-suggest{
	float:left;
	margin-right:15px;
}
#msg-suggest{
	margin:15px 15px 25px 0px;
}
/* #table-suggest-search  td{
	border-bottom:1px solid #CCCCCC;
	padding:6px !important;
} */
/* #table-suggest-search  tr:hover{
	background-color:#F4F4F4;
}
#table_search  tr.stat-search-info:hover{
	background-color:#F4F4F4;
} */
/* Formulaire ajout de suggestion */
#new label{
	float:left;
	width:145px;
}
#new input{
	width:300px;
}
#new select{
	width:304px;
}
#new select optgroup option{
	margin-left:15px;
}
#opt-sec{
	margin-top:15px;
}
div.opt-sec{
	font-weight: 600;
	margin-bottom:10px;
}

/* pour les commentaires sur les actualités */
#site-content table .rsp_reviews{
	margin-left:10px;
	margin-bottom: 5px;
	padding:3px;
	border:1px solid #ccc;
	font-style: italic;
	background-color:#f0f0f0;
}
.show_amount_diff{
	color: red;
}

.star_comment_tab_footer{
	font-size: 10px;
	font-weight: 500;
}

.left{
	text-align:left !important;
}
/* Autocompletion articles liés*/
.sct-select{
	width:750px !important;
}
.sct-input{
	width:300px !important;
}
.ac_results {
	box-shadow: 5px 5px 5px #C4C4C4;
	-moz-box-shadow: 5px 5px 5px #C4C4C4;
	-webkit-box-shadow: 5px 5px 5px #C4C4C4;
	min-width: 290px;
	background-color:white;
	border:1px solid #C4C4C4;
	overflow:hidden;
	padding:0;
	z-index:99999;
	position: absolute;
}
.ac_results ul {
	width: 100%;
	max-height:180px !important;
	list-style-position: outside;
	list-style: none;
	padding: 0;
	margin: 0;
}

.ac_results li {
	cursor:pointer;
	margin: 0px;
	padding: 2px 5px;
	cursor: default;
	display: block;
	font: menu;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
}
.ac_loading {
	background-color: white;
}
.ac_odd {
	background-color: #eee;
}
.ac_over {
	background-color: #0A246A;
	color: white;
}
#site-content input.publish-cat{
	width:20px;
	margin-right: 3px;
}
#site-content label[for=publish-y]{
	margin-right: 15px;
}
/* Statistique sur les poids des produits */
#site-content table.prd-weight{
	border-collapse:collapse;
}
/* #site-content table.prd-weight tbody tr td{
	border-right:1px solid #A3A3A3;
	border-top:1px solid #A3A3A3;
} */
#site-content table.prd-weight tbody tr td.stat-weight{
	text-align:right;
	vertical-align:middle;
}
#site-content table.prd-weight span.desc{
	display:block;
	font-style:italic;
	margin-top:5px;
	text-decoration:none;
}
#site-content table.prd-weight tbody tr td input{
	text-align:right;
	width:100px;
}
#site-content table thead th.th-first{
	font-weight:bold !important;
	& + th.th-first {
		border-left: 1px solid #A3A3A3;
	}
}
#site-content table thead .head-first{
	border-bottom:1px solid #A3A3A3;
	vertical-align:middle;
}
#site-content table thead th.th-second{
	border:1px solid #929292;
	background-color:#e3e3e3;
	font-weight:500;
}
#site-content table tfoot td{
	white-space:normal;
}
#site-content table tbody tr.right {
	text-align: right;
}
/* Statistique sur les produits sans articles liés */
#site-content #prd-no-related{
	border-collapse:collapse;
}
#prd-no-related tbody td{
	border-bottom:1px solid #C4C4C4;
	/* padding:3px; */
}

#site-content #no-redirection, #site-content #resolved{
	margin-right:5px;
}
/* Tarification */
#site-content table.authorizations td.sync{
	padding:0 10px 0 10px;
}

#site-content table.authorizations td.sync{
	border-right:1px solid #A3A3A3;
}
#site-content table.authorizations td.action{
	border-left:1px solid #A3A3A3;
}

#site-content span.info-bulle{
	color: #707070;
	font-size:10px;
	cursor:help;
}

td.left{
	text-align:left;
}
td.right{
	text-align:right;
}
td.pos-center{
	text-align:center;
}
td.none-cdt{
	padding:10px !important;
}

div.conditions{
	margin-bottom:10px;
	padding-bottom:10px;
	padding-left:55px;
	clear:both;
}
#none-eco{
	margin-bottom:20px;
}


/* Horaires d'expedition */
#form-expedition div.exp-menu{
	margin:10px 0px 10px 55px;
}

/* #site-content a.del{
	font-size:11px;
	color:red;
} */
#site-content li.exp-website{
	list-style-type:none;
	margin-bottom:3px;
}
#site-content li.exp-website input{
	margin-right:5px;
}
/* Jours Fériés pour les expéditions */
#tb-holidays tbody td{
	border-bottom:1px solid #C4C4C4;
	padding:5px;
}
#tb-holidays tbody td.hld-exp{
	text-align:center;
}
#tb-holidays tbody td label{
	margin-right:10px;
	width:20px;
}
#tb-holidays tbody td input[type=radio]{
	margin-right:2px;
	width:20px;
}
#tb-holidays img.hld-nav-year{
	border:medium none;
	height: 16px; width:16px;
	cursor: pointer;
}
/* Fermetures exceptionnelles */
#tb-closing tbody input.action{
	margin-bottom:5px;
}
#tb-closing tbody input[type=text]{
	width:100%;
}
#site-content .authorizations input[type=submit],
#tb-closing tbody input[type=submit]{
	width:auto;
}
#tb-redirection tbody img.edit-url{
	border:medium none;
	cursor:pointer;
	width:25px;
}
#tb-closing tbody div.save-load{
	display:none;
}
#tb-closing tbody div.save-load img{
	border:medium none;
}
/* Tarification */
#tbl-dispo-fld{
	margin-top: 10px;
}
/* Traduction */
#tb-translate tr td{
	padding: 5px;
}

#tb-translate tr td textarea{
	width: 100% !important;
	height:auto;
}
#tb-translate [id=exporter] {
    float: right;
    font-size: 12px;
    padding: 2px;
}
#tooltip {
	background-color: #FFFFFF;
    border: 1px solid #111111;
    padding: 5px;
    position: absolute;
    z-index: 3000;
}
#media-image #pagination {
	border-top: 1px solid #A9A9A9;
    margin-top: 20px;
    padding-top: 10px;
}
#media-image #before-page {
    float: left;
    width: 20%;
}
#media-image #number-page {
    float: left;
    text-align: center;
    width: 60%;
}
#media-image #next-page {
    text-align: right;
}
#media-image #pagination a {
	text-decoration: none;
}
#media-image #pagination a:hover {
	text-decoration: underline;
}
#actions-img {
	margin-top: 15px;
	border-bottom: 1px solid #C4C4C4;
	padding-bottom: 5px;
	margin-bottom: 20px;
}
#div-search-img {
	float: left;
}
.div-action-img {
	text-align: right;
}
#infos-img{
	-moz-box-shadow: 0 0 22px 6px #777777;
	-webkit-box-shadow: 0 0 22px 6px #777777;
	box-shadow: 0 0 22px 6px #777777;
	background-color: white;
	background-color: white;
	display: none;
	height: 176px;
	padding: 10px;
	position: absolute;
	width: 370px;
	z-index: 9999;
	cursor: pointer;
}
#infos-img .name-img {
    display: block;
    font-weight: 600;
    margin-bottom: 3px;
}
#infos-img .img-used {
	height: 156px;
	position: relative;
	float: left;
	width: 184px;
}
#infos-img ul {
    margin-top: 2px;
}
#infos-img .more {
    bottom: 0;
    position: absolute;
}
#infos-img .title {
    color: #000000;
    text-decoration: none;
}
#img-infos #media {
	margin-bottom: 5px;
}
@media (min-width: 768px) {
	#img-infos #media {
		margin-right: 5px;
		margin-bottom: 5px;
		float: left;
	}
}
#img-infos img {
    border: 1px solid #A9A9A9;
    margin-right: 10px;
    padding: 2px;
}
#img-infos .img-name {
    display: block;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 7px;
    text-transform: uppercase;
}
#img-infos ul {
  margin-top: 2px;
	float: left;
}

#img-infos li {
	list-style: none;
	margin: 0 !important;
}
#img-infos .linked-img {
	margin-bottom: 10px;
}
#pagination {
    margin-top: 15px;
    padding-top: 5px;
    text-align: right;
}
#pagination.pagination2 {
    border: medium none;
    margin: 0;
}
@media (min-width: 768px) {
	#site-content .elem-links {
    margin-left: 280px;
	}
}
#site-content .linked-img .name-links {
	height:19px;
	border-bottom: 1px solid #C4C4C4;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 5px;
	padding-bottom: 3px;
}
#site-content .linked-img .name-links input[name="add"] {
	float: right;
}
#site-content .links .cnt-desc {
    color: #000000;
    margin-bottom: 3px;
    margin-left: 18px;
}
#site-content .links .cnt-url {
    margin-left: 18px;
}
#site-content .links .cnt-infos {
	margin-bottom: 5px;
}
#site-content .links .cnt-infos .del-link {
	float: left;
    height: 1px;
    width: 18px;
}
#site-content .edit-link {
	background-image: url("/admin/images/edit.png");
    display: block;
    height: 16px;
    width: 16px;
}
#site-content .edit-link:hover,
#site-content .authorizations .del-link:hover,
#site-content .ncmd_cart .del-link:hover{
	text-decoration : none;
}
#site-content .links .cnt-url a{
    font-size: 0.9em;
}
#site-content .links-search {
	margin-top: 15px;
}

#popup-links-img {
	padding-top: 15px;
}
#popup-links-img h2 {
	border-bottom: 1px solid;
    margin-bottom: 10px;
    padding-bottom: 5px;
}
#popup-links-img .cnt-infos {
    margin-bottom: 10px;
}
#popup-links-img .cnt-checkbox {
    float: left;
    margin-right: 5px;
    padding-top: 2px;
}
#popup-links-img .cnt-desc {
    color: #000000;
    margin-bottom: 5px;
    margin-left: 18px;
}
#popup-links-img .cnt-url {
    font-size: 0.8em;
    margin-left: 18px;
}
#popup-links-img .cnt-place {
    margin-left: 18px;
    margin-top: 5px;
}
#popup-links-img .links, #popup-links-img #actions {
	margin-left: 0 !important;
}
#popup-links-img .media {
	float: left;
}
#popup-links-img .suggestion {
	margin-top: 35px;
	display: none;
}
#popup-links-img #search-link {
	margin-bottom: 15px;
}

body#popup-content.popup-order-state-history {
	padding: 15px 0 0 0;
	overflow: hidden;
}
#new-file-media {
	margin-bottom: 5px;
}
#tabpanel #editcms {
	width: 100%;
}
#site-content #search-wo-result1, #site-content #search-wo-result2{
	float: right;
}
#site-content table tfoot td#pagination {
	vertical-align: middle;
}

/* autoc */
.autoc_results {
	background-color:white;
	border:1px solid #C4C4C4;
	overflow:auto;
	padding:0;
	z-index:99999;
	max-height: 300px;
}
.autoc_results ul li a span {
	color: #2020e0;
	display: block;
	text-decoration: none;
	padding-left: 20px;
	padding: 2px 5px 2px 20px;
	line-height: 16px;
}
.autoc_results ul li a:hover span {
	background: #000080;
	color: white;
	text-decoration: underline;
}
.autoc_results ul {
	width: 100%;
	list-style-position: outside;
	list-style: none;
	padding: 0;
	margin: 0;
}
.autoc_results strong {
	display: block;
	margin: 4px;
}
.autoc_results li {
	cursor: pointer;
	margin: 0px;
	cursor: default;
	display: block;
	font: menu;
	font-size: 12px;
	overflow: hidden;
}
.autoc_loading		{ background: white url('indicator.gif') right center no-repeat; }
.autoc_odd			{ background-color: #eee; }
.autoc_over			{ background-color: #0A246A; color: white; }
.autoc_back_shadow	{ box-shadow: 2px 2px 2px rgba(0,0,0,0.5); }

#site-content #sort-perso {
	background-image: none;
}
#site-content .sort-inherited {
	font-style: italic;
	font-size: 0.9em;
}
#site-content #returns-categories ul {
	margin: 0px;
}
#site-content #returns-categories ul li {
	margin: 0px;
}
#site-content #returns-categories label {
	font-size: 8pt;
	margin-left: 5px;
	position: relative;
	top: -2px;
}
.vsml	{ font-size: 7.5pt; }
.lineth { text-decoration: line-through; }
.color-success	{ color: #008000; }
.color-failure	{ color: red; }
.und	{ display: none; }
.rgt	{ text-align: right; }
.mart20	{ margin-top: 20px; }
.marr30	{ margin-right: 30px; }
.wraper-hidden {
	overflow: hidden;
	width: 0px;
	height: 0px;
}
.flol	{ float: left; }
.flor	{ float: right; }
.clr	{ clear: both; }

#site-content #content-config-returns .arrow {
	border: none;
}

#body-orders-return-update {
	background-image: none;
	min-width: 0px;
}
#body-orders-return-update #site-content {
	margin: 0px;
	padding: 0px;
	min-width: 0px;
	min-height: 0px;
}
#body-orders-return-update #site-content #table-return-returned	{ margin: 0px; }
#body-orders-return-update #site-content input	{ width: auto; }

#site-content table td.rev-td {
	padding: 0px;
}
#site-content table td.rev-td table {
	border: none;
	margin: 0px;
}
#site-content table td.rev-td table tr td {
	border: none;
}

/* Stock */
#usr-contacts .rep {
    border: 1px solid #C4C4C4;
    margin: 10px;
    padding: 5px;
}
#return-returned-state-container {
	padding-top: 10px;
}
#table-return-returned .motif-valid, #table-return-returned .state-valid, #table-return-returned .qte-valid{
	margin-bottom: 3px;
}
#table-return-returned .motif-valid label{
	vertical-align: top;
	height: 15px;
	display: block;
}
#table-return-returned .motif-valid textarea{
	height: 50px;
}
#table-return-returned .valid-prd{
	border: 1px solid #C4C4C4;
	padding: 5px;
	margin-bottom: 2px;
}
#table-return-returned .valid-prd input[type=submit]{
	width: 80px;
}
#site-content table .row_tarif_resume_default {
	font-size: 1em; font-weight: bold !important;
}
#site-content .nomenclatures a img{
	border: none;
}
#site-content #ref.ac_loading{
	background:url(/admin/images/loader_join_file.gif) no-repeat right top;
}
#site-content .nomenclatures label{
	padding: 0 10px 0 10px;
}
#site-content .nomenclatures #ref {
	width: 320px;
}
#stat-popup-errors {
	background: none repeat scroll 0 0 transparent;
	padding: 15px;
	width: auto;
	min-width: 100px;
}
#stat-popup-errors table{
	border: 1px solid #A3A3A3;
	clear: both;
	margin-bottom: 15px;
}
#stat-popup-errors table caption{
	background-color: #232E63;
	color: white;
	font-weight: 600;
	text-align: left;
	white-space: nowrap;
}
#stat-popup-errors table tbody tr:hover {
	background-color: #F3F3F3;
}
#stat-popup-errors table thead th {
    background-color: #D3D3D3;
    padding: 3px;
	text-align: center;
}
#stat-popup-errors table tbody th {
    border-bottom: 1px solid #C4C4C4;
    padding-left: 5px;
    text-align: left;
}
#stat-popup-errors table tbody td {
	border-bottom: 1px solid #C4C4C4;
	padding: 2px;
}
#stat-popup-errors table tbody td.right {
	text-align: right;
	padding-right: 5px;
}
#popup-add-redirect{
    background: none repeat scroll 0 0 transparent;
    min-width: 100px;
    padding: 15px;
    width: auto;
}
#popup-add-redirect table {
	border: 1px solid #A3A3A3;
	width: 100%;
}
#popup-add-redirect table caption {
    background-color: #232E63;
    color: white;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
}
#popup-add-redirect table thead tr th {
    background-color: #D3D3D3;
    text-align: center;
}
#popup-add-redirect table tbody td {
    border-bottom: 1px solid #C4C4C4;
    padding: 2px;
}
#popup-add-redirect table tbody th {
    border-bottom: 1px solid #C4C4C4;
    padding-left: 5px;
    text-align: left;
}
#popup-add-redirect table tbody input[type=text] {
	width: 100%;
}
#popup-add-redirect table tbody select {
	width: 250px;
}
#popup-add-redirect table tfoot tr {
    background-color: #D3D3D3;
    padding: 3px;
    text-align: left;
}
#popup-add-redirect table tfoot input {
    font-size: 10px;
}
#popup-add-redirect .error {
	color: black; background-color: #ffdddd;
	border: 1px solid red;
	margin: 1em 0; padding: 1em;
}
#popup-add-redirect .error-success {
	color: black; background-color: #C2FFBF;
	border: 1px solid $green;
	margin: 1em 0; padding: 1em;
}
#popup-content #choose-cat-ctr input[type=radio] {
	width: 15px;
}
#popup-content .ac_results{
	left : 666px !important;
}

#prd-comparator table {
	border: 1px solid #A3A3A3;
	border-collapse: collapse;
    clear: both;
}
#prd-comparator table caption {
	background-color: #232E63;
    color: white;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
}
#prd-comparator #tbl-infoprd {
	width: 594px;
	margin-bottom: 5px;
}
#prd-comparator table thead tr th {
	background-color: #C4C4C4;
    padding: 3px;
}
#prd-comparator table tbody tr th.last, #prd-comparator table tbody tr td.last {
	border-bottom: medium none;
}
#prd-comparator table a {
	text-decoration: none;
}
#prd-comparator table a.active {
	color: $green;
}
#prd-comparator table a.cancel {
	color: red;
}
#prd-comparator table a:hover {
    text-decoration: underline;
}
#prd-comparator input.save:hover {
	color: #232E63;
	background-color: black;
}
#prd-comparator input.save {
	background-color: #232E63;
	border: medium none;
	border-radius: 7px 7px 7px 7px;
	color: #FFFFFF;
	font-weight: 600;
	padding: 3px;
	cursor: pointer;
	width: 100px;
}
#prd-comparator .info label {
	display: block;
}
#prd-comparator .info input.text, #prd-comparator .info textarea {
    margin-bottom: 7px;
    margin-top: 3px;
    width: 579px;
}
#prd-comparator .info textarea {
    height: 80px;
}
#prd-comparator .middle {
	vertical-align: middle;
}
#authorizations .actions ,
#prd-comparator .actions {
    margin-bottom: 5px;
    text-align: right;
}
#prd-comparator .actions2 {
    margin-top: 5px;
}
#prd-comparator .period {
	font-style: italic;
    font-weight: 500;
}
#prd-comparator ul, #form-segment .ul-stats {
	margin: 0;
	padding: 0;
	list-style-position: inside;
	padding-left: 3px;
}
#form-segment .ul-stats {
	margin-bottom: 5px;
}
#form-segment .ul-stats li {
	margin: 0;
}
#prd-comparator input.qte {
	width: 50px !important;
	text-align: right !important;
}
#prd-comparator table .error {
    background-color: #FFDDDD !important;
    border: 1px solid red !important;
    color: black !important;
    margin: 1em 0 !important;
    padding: 1em !important;
}
img.loader {
	border-style: none ! important;
}
/** Recherche dans l'export des comparateurs de prix */
#ctr-search {
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0 0 5px #C4C4C4;
	border: 1px solid #C4C4C4;
    padding: 15px;
	margin-top: 20px;
    width: 772px;
}
#ctr-search #zone-filters {
	text-align: center;
	border-bottom: 1px solid #C4C4C4;
    margin-bottom: 15px;
    padding-bottom: 15px;
}
#ctr-search #zone-filters select {
    border: 1px solid #C4C4C4;
    padding: 5px;
    width: 400px;
}
#ctr-search .actions {
	border-top: 1px solid #C4C4C4;
    margin-top: 15px;
    padding-top: 15px;
    text-align: right;
}
#ctr-search #save {
	float: left;
}
#ctr-search #filter {
	text-align: left;
}
#ctr-search #filter .flt {
	border: 1px solid #848484;
    margin-left: 20px;
    margin-right: 20px;
    padding: 10px;
}
#ctr-search .textarea_popup_content{
	display: none;
}
#zone-conditions .elem {
	margin-bottom: 5px;
}
#zone-conditions .elem label.name-cdt, #zone-conditions .elem a.name-cdt{
    display: block;
    float: left;
    padding-top: 10px;
    text-align: right;
    width: 201px
}
#zone-conditions .elem label.name-cdt-radio, #zone-conditions .elem a.name-cdt-radio{
	padding-top: 2px;
}
#zone-conditions .elem a.name-cdt{
	float: left;
	width: 48px;
}
#zone-conditions .elem .options a.show_popup{
	margin-top: 5px;
	float: left;
}
#cdt-select  .options ,
#zone-conditions  .options {
    float: left;
    margin-left: 20px;
    margin-top: 5px;
}
#ctr-search #new-cdt {
	display: block;
    margin-top: 20px;
	float: right;
}
#ctr-search option {
	padding-left: 5px;
}
#filter select, #zone-conditions  .options select {
	border: 1px solid #C4C4C4;
    padding: 5px;
    width: 250px;
}
#zone-conditions  .options input.text,
#cdt-select .options input.text {
    border: 1px solid #C4C4C4;
    padding: 6px;
    width: 75px;
}
#zone-conditions  .options input.search-cats,
#cdt-select .options input.search-cats {
	width: 395px;
	float: none;
}
#zone-conditions  .options input.search-txt,
#cdt-select .options input.search-txt {
	width: 395px;
	float: none;
}
#cdt-select {
	border-right: 1px solid #848484;
    float: left;
    margin-top: 8px;
    width: 85%;
}
#cdt-select .options .search-txt,
#cdt-select .options .search-cats,
#cdt-select .options textarea,
#cdt-select .options select {
	margin-left: 160px;
}
#cdt-select .options textarea{
	width: 100%;
	height: 150px;
}
#cdt-actions {
    float: left;
    padding-left: 15px;
    text-align: center;
    width: 12%;
}
#cdt-actions a {
	display: block;
	margin-top: 5px;
}
#infosearch textarea{
    height: 75px;
}
#zone-conditions .options-radio {
	float: left;
	margin-right: 4px;
}
#ctr-sh-results {
	margin-top: 20px;
}
#ctr-sh-results img {
	border: none;
}
#ctr-sh-results .title, #ctr-sh-results .desc {
	display: block;
    margin-bottom: 5px;
}
#ctr-sh-results .price {
	font-weight: 600;
}
#ctr-sh-results .check {
	vertical-align: middle;
}
.pop-form-cancel{
	text-align: right;
}
#site-content .ncmd_qte{
	width: 50px;
	text-align: center;
}
#site-content .ncmd-cart a img{
	border: none;
}
#popup-content tbody td.odd,
#popup-content table.checklist tbody td.odd,
#site-content  tbody td.odd,
#site-content table.checklist tbody td.odd{
	background-color:#f3f3f3;
}
#site-content .authorizations tr.odd td{
	background-color:#F3F3F3;
}

#type-calc { width: 120px !important; }
#site-content #statu-calc {
	width: 100px;
}

#rwd-add-product label.radio{
	width: 20px !important;
	margin-left: 5px;
}

#rwd-add-product label {
    text-align: left !important;
    width: 175px !important;
}
#rwd-add-product input.text {
    width: 300px;
}

#rwd-products-buttons{
    text-align: right;
}

#rwd-add-rule label {
    text-align: left !important;
    width: 175px !important;
}
#rwd-add-rule input.text {
    width: 300px;
}
#rwd-rules-buttons{
    text-align: right;
}
#rwd-list-rules select {
	width: 100% !important;
}
#tb-tabActions .title-pts {
    border-bottom: 1px solid #C4C4C4;
    display: block;
    font-weight: 600;
}
#tb-tabActions .rwa-params {
	margin-top: 5px;
}
#tb-tabActions .rwa-params label {
    float: left;
    width: 83px;
}
#tb-tabActions .rwa-params div {
	margin-bottom: 3px;
}
#tb-tabActions .avanced {
    display: block;
    margin-bottom: 5px;
    margin-top: 5px;
    text-align: right;
}
#tb-tabActions, #tb-landing {
	width: auto !important;
	color: #000000;
	border-collapse: collapse;
	margin-left: 8px !important;
}
#tb-tabActions.popup tbody tr td {
	border: 1px solid #CCCCCC;
	padding: 3px;
}
#tb-landing label {
    float: left;
    width: 85px;
}
#tb-landing .rwa-params div {
	margin: 5px;
}
#tb-landing .del-landing{
	vertical-align: middle;
	text-align: center;
}
#site-content table td.centertd{
	text-align: center;
}
#tb-landing .del-landing a{
	color: red;
}
#tb-landing input.text{
	width: 75px;
}
#site-content table tbody .input_auto input,
#site-content table tbody .input_auto textarea,
#site-content table tbody .input_auto select{
	width: auto;
}
#site-content table thead .sortified {
	padding-right: 18px;
}
#site-content .tooltip {
	display: none;
}
.tooltip-handle {
	cursor: default;
}
#tooltip {
	width: auto;
}
#ajax-indicator {
	float: left;
	padding-top:10px;
}
#ajax-indicator img, #prd-conversion img, #pager img{
	border:medium none;
}
#site-content .pagedisplay {
	max-width: 124px;
}
.cnt-reward .rwd-pts {
	width: 50px !important;
	text-align: right;
}
#site-content #tb-synthese-order.synthese-rewards {
	float: none;
	margin-bottom: 10px;
}
.pourcent-volume {
	font-size: 0.7em;
	font-weight: 500;
}
/* Mise en forme du paramétrage des places de marchés sur les fiches produits */
.marketplace-mdl {
    border: 1px solid #C4C4C4;
    box-shadow: 3px 3px 8px #C4C4C4;
    margin: 10px 8px 15px 0;
    padding: 10px;
}
.marketplace-mdl label {
	display: inline-block;
	width: 70%;
	float: left;
}
.marketplace-mdl .label {
	float: left;
	width: 70%;
}
.marketplace-mdl .label label {
	width: 100%;
}
.marketplace-mdl .label sub {
	width: 95%;
}
.marketplace-mdl .bold {
	display: inline-block;
    font-weight: 600;
    margin-bottom: 3px;
}
.marketplace-mdl label .desc {
	display: block;
	font-size: 0.8em;
	font-style: italic;
	width: 90%;
}
.marketplace-mdl label.radio {
	float: none;
	margin: 5px;
	width: auto;
}
.marketplace-mdl .elem {
	border-bottom: 1px dotted #C4C4C4;
	margin-bottom: 5px;
	padding-bottom: 4px;
}
#prd-comparator .marketplace-mdl select,#prd-comparator .marketplace-mdl input.text {
	width: 29%;
}
#prd-comparator .marketplace-mdl .other {
	float: right;
	width: 30%;
	margin-top: 7px;
	display: none;
}
#prd-comparator .marketplace-mdl .other.show {
	display: block;
}
#prd-comparator .marketplace-mdl .other .text {
	width: 99%;
}
#prd-comparator .marketplace-mdl .del-option {
	width: 16px;
}
#prd-comparator .add-option label {
	width: auto;
}
#prd-comparator .add-option {
	text-align: center;
}
#prd-comparator .marketplace-mdl .add-option select {
	width: 50%;
}
.marketplace-mdl .add-option option {
    padding-bottom: 3px;
    padding-left: 15px;
}
.marketplace-mdl .group-option li {
	list-style: none;
}
.nowrap {
	white-space: nowrap;
}
#models-cdiscount img {
	border: none;
}
/* css autocomplete ui */
.ui-autocomplete{
	width: 200px;
	background:#fff;
	list-style-type: none;
	box-shadow: 4px 2px 10px -5px #000;
	border-bottom: 1px solid #F1F1F1;
	border-right: 1px solid #F1F1F1;
	border-left: 1px solid #F1F1F1;
}
.ui-autocomplete li{
	display: block;
	overflow: hidden;
}
.ui-autocomplete .ui-menu-item:nth-child(2n){
	background: #F1F1F1;
}
.ui-autocomplete li a{
	padding: 3px 10px;
	display: block;
	color:#000;
}
.ui-autocomplete li a:hover{
	color:#fff;
	background:#232E63;
}
#popup-content table .tdleft,
#site-content table .tdleft{
	text-align: left;
}
#site-content .services_available,
#site-content .free-shipping-services ul{
	list-style: none;
	margin-top: 5px;
}
#add-redir-search {
	color: black;
}
#add-redir-search .redirection {
    margin-top: 15px;
}
#add-redir-search .elem {
    margin-top: 5px;
}
#add-redir-search .elem label {
    display: block;
    float: left;
    width: 160px;
}
#add-redir-search .elem input {
    width: 250px;
}
#add-redir-search #head-results {
    border-top: 1px solid #C4C4C4;
    padding-top: 6px;
	margin-top: 10px;
	font-size: 1.2em;
	font-weight: 600;
	display: none;
}
#add-redir-search #results {
    border: 1px solid #C4C4C4;
    box-shadow: 3px 3px 5px #C4C4C4;
    margin-top: 5px;
    padding: 10px;
	display: none;
}
#add-redir-search #results .result {
}
#add-redir-search #results img {
    float: left;
    margin-right: 5px;
}
#add-redir-search .actions {
	text-align: right;
}
#site-content .ctr-stats .positive {
	color:#006000;
	font-weight: 600;
}
#site-content table td.row_check_ttc {
	vertical-align: middle;
}
#site-content select.select-prc-default{
	width: 200px;
	margin-right: 10px;
}
.prdoptions{
	display:none;
	padding: 5px;
	margin: 3px 0;
	background: #F1F1F1;
	border :1px solid #D3D3D3;
}
.action-popup-stats-search {
	margin-top: 10px;
}
.action-popup-stats-search #form-reinit-stats {
	float: left;
	margin-right: 10px;
}
.action-popup-stats-search #form-filter {
	padding-top: 5px;
}
#load-stats-search .message {
    background-color: #FFFFFF;
    color: black;
    opacity: 1;
    padding: 15px;
	width: 100%;
}
#load-stats-search .message img { float: left; }
#load-stats-search .message span {
    display: block;
    font-size: 1.2em;
    font-weight: 600;
    height: 30px;
    margin-left: 57px;
    margin-top: 10px;
}
.ord-mini-action {
	width: 150px !important;
	margin-right: 10px;
}
.ord-prd-actions {
	width: 50%;
	float: left;
    text-align: left;
}
.ord-actions {
	width: 50%;
	float: left;
	text-align: left;
	display: none;
}
.ord-history {
	width: 50%;
	float: right;
}
.ord-action-reward {
	text-align: right;
}
.batch-content{
	padding: 15px;
}
.batch-content .actions{
	padding: 10px 0;
	text-align: right;
}
.batch{
	width: 100%;
	height: 300px;
}
.sortable-img{
	list-style: none;
}
#sortable2, #sortable1{
	min-height:154px;
}
#sortable2{
	margin-top:0px!important;
}
#site-content .imgs-primary-lbl{
	height: 160px;
	padding-top: 10px;
}
#site-content table tbody .news-str-select{
	width: 600px;
	display: block;
	clear: both;
	margin-bottom: 5px;
}
#site-content table tbody .news-str-del{
	width: 120px;
	float: left;
}
#site-content table tbody .news-str-add{
	width: 336px;
	float: right;
}
#site-content table tbody .news-str-add #str-search {
	width: 200px;
}
.td_classify{
	position: relative;
	display: block;
}
.td_classify .box_cly_canonical{
	position: absolute;
	left: -17px;
	margin-top: 1px;
}
#tnt-filters .blc-ip {
    float: left;
    text-align: center;
    width: 35px;
}
#tnt-filters .label-ip {
    float: left;
    font-weight: 600;
    line-height: 12px;
    margin: 0 6px;
}
#code-like {
	margin-top: 15px;
	font-weight: 600;
}
#code-list-like {
    font-family: monospace;
    font-size: 1.2em;
    letter-spacing: 1px;
}
#code-list-like li {
    float: left;
}
#filters-options {
	margin-bottom: 10px;
	display: none;
}
.items-list-filters .prd-filters input {
    display: block;
    float: left;
    margin-right: 5px;
}
.items-list-filters .prd-filters label {
    cursor: pointer !important;
    display: block;
    line-height: 22px;
}

.th-col-show {
    display: table-cell;
    white-space: nowrap;
}.th-col-show.prd-desc {
    white-space: normal;
}
#sort-perso{
	padding-right: 10px;
}

.autoload_loader {
    background-color: #F4F4F4;
    margin: 5px 0;
    padding: 5px;
    text-align: center;
}
#site-content .autoload_loader img {
	border: none;
}

.input-del-item {
	display: none;
}
.no-click {
    background-color: #F1F1F1;
    border: 1px solid #C4C4C4;
    color: #000000;
}
@-moz-document url-prefix() {
	.no-click {
		margin: 2px;
	}
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
	.no-click {
		margin: 1px;
	}
}
#site-content table td.td-check-del {
	text-align: center;
	vertical-align: middle;
}
[id=list-prd]{
	margin-top: 15px;
}
.prd-filters .selector{
	max-height: 350px;
}
#stats-tags .desc {
	display: block;
}
#stats-tags .ref-cly textarea {
	height: auto;
	margin-top: 0;
	margin-bottom: 2px;
	width: 100%;
	padding: 3px;
}
#stats-tags table {
	width: 100%;
}
#stats-tags .tr-ref-show{
	display: table-row;
}
#stats-tags .tr-ref-hide{
	display: none;
}
#stats-tags .readonly{
	background: none repeat scroll 0 0 #D9D9D9;
	border: medium none #F1F1F1;
	height: auto;
	margin: 5px 20px 5px 0;
	padding: 3px;
}
#stats-tags .bold{
	font-weight: 600;
}
#display-cols-options, .display-ord-products-options {
	margin-left: 0;
}
.rwd-legend {
	float: left;
	margin-right: 10px;
}
.rwd-legend .color {
	border: 1px solid #C4C4C4;
	display: block;
	float: left;
	height: 16px;
	margin-right: 5px;
	width: 25px;
}
.rwd-legend .active {
	background-color: #DDFFDD;
}
.rwd-legend .unactive {
	background-color: #FFDDDD;
}
#stat-tag-double ul {
	margin-top: 0;
}
#stat-tag-double .cnt-parent {
	list-style: none outside none;
}
#stat-tag-double .cnt-parent .title {
	font-size: 1.1em;
	font-weight: 600;
}
#site-content table tbody input.del-obj-seg {
	width: 16px !important;
	border-style: none;
	vertical-align: text-top;
	padding-top: 2px;
	margin-left: 5px;
}
.websites-languages .language {
	margin-left: 18px;
}
#site-content table tbody input.fld_usr_browse_txt {
	width: 300px;
}
#site-content table tbody input.fld_usr_browse_btn {
	font-size: 10px;
	margin-left: 5px;
}
#site-content table tbody input.text-small {
	width: 200px;
}
#img-search .label {
	font-weight: 600;
	margin-top: 10px;
}
#img-autoassociated .filter {
	float: left;
}
#img-autoassociated .actions {
	background-color: #F0F4FB;
	border: 1px solid #C4C4C4;
	padding: 10px;
	text-align: right;
}
#img-autoassociated .pagination2 {
	padding-bottom: 20px;
}
#img-autoassociated .links {
	box-shadow: 0px 0px 5px #C4C4C4;
	border: 1px solid #C4C4C4;
	padding: 10px;
	border-radius: 5px;
}
#img-autoassociated .links h3 {
	margin-top: 0;
}
#img-autoassociated .links hr {
	margin-top: 10px;
}
#img-autoassociated .actions-links {
	margin: 20px 0px 10px;
}
#img-autoassociated .actions-links input[type=button] {
	float: left;
}
#site-content table tbody .fld-form-reference select {
	width: 100%;
}
#site-content table tbody .fld-form-reference .fld-val-actions {
	text-align: right;
	margin-top: 3px;
}
#site-content table tbody .fld-val-actions input {
	width: auto;
}
.list-filters {
	margin-bottom: 10px;
}
.list-filters .filter-label {
	margin-bottom: 5px;
	font-weight: 600;
}
.list-filters .filter {
	margin: 10px 0 0 10px;
}
.list-filters .btn-action {
	margin: 10px 0 20px 10px;
}
.invisible-table-row{
	display: none !important;
}


.ui-selectmenu-menu {
	padding: 0;
	margin: 0;
	position: absolute;
	top: 0;
	left: 0;
	display: none;
}
.ui-selectmenu-menu .ui-menu {
	overflow: auto;
	overflow-x: hidden;
	padding-bottom: 1px;
}
.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
	font-size: 1em;
	font-weight: 600;
	line-height: 1.5;
	padding: 2px 0.4em;
	margin: 0.5em 0 0 0;
	height: auto;
	border: 0;
}
.ui-selectmenu-open {
	display: block;
}
.ui-selectmenu-text {
	display: block;
	margin-right: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
}
.ui-selectmenu-button.ui-button {
	text-align: left;
	white-space: nowrap;
	width: 14em;
}
.ui-selectmenu-icon.ui-icon {
	float: right;
	margin-top: 0;
}
.ui-front {
	z-index: 100;
}
.ui-menu {
	list-style: none;
	padding: 0;
	margin: 0;
	display: block;
	outline: 0;
}
.ui-menu .ui-menu {
	position: absolute;
}
.ui-menu .ui-menu-item {
	margin: 0;
	cursor: pointer;
	list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
}
.ui-menu .ui-menu-item-wrapper {
	position: relative;
	padding: 3px 1em 3px .4em;
}
.ui-menu .ui-menu-divider {
	margin: 5px 0;
	height: 0;
	font-size: 0;
	line-height: 0;
	border-width: 1px 0 0 0;
}
/* icon support */
.ui-menu-icons {
	position: relative;
}
.ui-menu-icons .ui-menu-item-wrapper {
	padding-left: 2em;
}

/* left-aligned */
.ui-menu .ui-icon {
	position: absolute;
	top: 0;
	bottom: 0;
	left: .2em;
	margin: auto 0;
}

/* right-aligned */
.ui-menu .ui-menu-icon {
	left: auto;
	right: 0;
}
.ui-button {
	padding: .4em 1em;
	display: inline-block;
	position: relative;
	line-height: normal;
	margin-right: .1em;
	cursor: pointer;
	vertical-align: middle;
	text-align: center;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* Support: IE <= 11 */
	overflow: visible;
}

.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
	text-decoration: none;
}

/* to make room for the icon, a width needs to be set here */
.ui-button-icon-only {
	width: 2em;
	box-sizing: border-box;
	text-indent: -9999px;
	white-space: nowrap;
}
.ui-widget {
	font-family: Arial,Helvetica,sans-serif;
	font-size: 1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Arial,Helvetica,sans-serif;
	font-size: 1em;
}
.ui-widget.ui-widget-content {
	border: 1px solid #c5c5c5;
}
.ui-widget-content {
	border: 1px solid #dddddd;
	background: #ffffff;
	color: #333333;
}
.ui-widget-content a {
	color: #333333;
}
.ui-widget-header {
	border: 1px solid #dddddd;
	background: #e9e9e9;
	color: #333333;
	font-weight: 600;
}
.ui-widget-header a {
	color: #333333;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,

/* We use html here because we need a greater specificity to make sure disabled
works properly when clicked or hovered */
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
	border: 1px solid #c5c5c5;
	background: #f6f6f6;
	font-weight: 500;
	color: #454545;
}
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
	color: #454545;
	text-decoration: none;
}
.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
	border: 1px solid #cccccc;
	background: #ededed;
	font-weight: 500;
	color: #2b2b2b;
}
#site-content ul.no-style li {
	list-style: none;
}
#site-content .div_check_publish{
	margin-bottom: 5px;
}
#site-content .div_check_publish .label_check_publish{
	margin-left: 5px;
}
#site-content table button.previsualisation{
	margin-right: 5px;
}
#site-content table button.previsualisation a{
	color: inherit;
}
#site-content table button.previsualisation a:hover{
	text-decoration: none;
}
.page-load-block {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	z-index: 9999;
	background-color: black;
	opacity: 0.5;
}

.popup_ria_iframe_load.notice {
	margin: 10px;
}
.popup_ria_back_load {
	position: fixed;
	width: 100%;
	height: 100%;
	display: block;
	top: 0;
	left: 0;
	z-index: 9999;
	background-color: black;
	opacity: 0.6;
}
.popup_ria_back_load.abs {
	position: absolute;
}

#site-content div.pmt-list-discount{
	padding: 0px 20px !important;
}

table#config-email tbody td.designation{
	width: 260px;
}

table#config-email tbody input{
	width: 40px;
	text-align:right;
}

#site-content #product input.btn-action.active {
	background: $green;
}

#site-content #product input.btn-action.inactive {
	background: darkorange;
}
.price-watching {
	margin-top: 20px;
}
#site-content #products caption.sticky {
	position: fixed;
	z-index: 80;
	display: block;
	width: 1706px;
	top: 0;
}
#site-content #products thead.sticky {
	 position: fixed;
	 top:22px;
	 z-index: 70;
 }

.ncmd_model_rights {
	margin-top: 10px;
}

[id="view_rewards_stats"]{
    margin-top: 10px;
}

.addLink {
    margin-left: 4px;
}


.menu-mobile {
	display: none;
}
#site-menu {
	height: auto;
	overflow: hidden;
}
html {
	height: 100%;
}
.is-menu-open {
	overflow: hidden;
	position: relative;
	height: 100%;
}

#site-menu.double-line #site-submenu {
	top: 160px;
}
#site-content-w {
	overflow: hidden;
}
#site-footer[id] {
	position: fixed;
	opacity: 1;
}
h1 {
	height: 25px;
}
.page_timer {
	position: absolute;
	top: 8px;
	left: 110px;
	color: ghostwhite;
}

#popup-content table tbody input.zipcode, #site-content table tbody input.zipcode{
	text-align: right;
	width: 90px;
}

select#zipcode{
	width:90px !important;
	text-align:right !important;
}

.ui-autocomplete {
	max-height: 200px;
	overflow-y: auto;
	overflow-x: hidden;
} 

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid #aaaaaa;
    background: #cfd8e0;
    font-weight: 500;
    color: #212121;
}

.ui-helper-hidden-accessible {
	display: none;
}

#site-content table tfoot td.ord-prd-actions {
	text-align: left;
}

.ord-prd-edit-btn {
	display: inline-block;
	width: 16px;
	height: 16px;
	background-image: url('/admin/images/edit.png');
	background-size: 100%;
}

#site-content table tbody .ord-prd-discount-select {
	width: 100px;
// 	margin-bottom: 5px;
// 	height: 25px;
}

.ord-prd-edit-btn:hover {
	cursor: pointer;
}

#popup-content table tbody .inv-qte{
	width:50px;
	text-align:center;
}
#popup-content table#inv-qte td{
	vertical-align: middle;
}

/* Référencement personnalisé */
#class-selector, #tag-selector {
	width: 200px;
}

#class-select {
	margin-bottom: 5px;
}

#tag-select{
	border-bottom: 1px solid #A9A9A9;
	padding-bottom: 10px;
}

#site-content table tbody textarea#sentence-editor {
	height: 96px;
}

#save-column {
	vertical-align: middle !important;
}

#site-content table tbody #actual-sentences {
	width: 100%;
}

#edition-table {
	margin-top: 10px;
}

#variable-select {
	margin: 0px !important;
}

/* Graphiques rapports d'appels */
.graph-calls-semi-circle {
	width: 50%;
	float: left;
}

/* Tableau rapports d'appels */
#calls-report tfoot input#export-calls {
	float: left;
}


/* Objectifs représentant */
#site-content #goals tbody tr td {
	border : none;
	padding: 5px;
}
#site-content #goals input.actif {
    background-color: $green;
    padding: 1px 2px;
    border: 1px solid $green;
    color: #fff;
	font-weight: 600;
	width: 60px !important;
	text-align: center;
}

#site-content #goals input.inactif {
    background-color: #FE5511;
    padding: 1px;
    border: 1px solid #FE5511;
    color: #fff;
	font-weight: 600;
	width: 60px !important;
	text-align: center;
}

#popup-content #tb-add-period {
	border: none;
	width: auto;
}

#popup-content #tb-add-period td {
	border: none;
}

#popup-content #tb-add-period input {
	width: 150px;
}

#site-content table#tb-goals .price {
	width: 107px;
}
table#tb-goals .obj-validate, table#tb-goals .obj-del{
	border: medium none;
	height: 16px;
	width: 16px;
	margin-left: 3px;
}
table#tb-goals input.obj-value, table#tb-goals input.obj-value-month, table#tb-goals input.obj-value-trimester, table#tb-goals input.obj-value-year{
	width: 80px;
	text-align: right;
	margin-top: 1px;
	margin-bottom: 1px;
}
.obj-value-input {
	font-weight: initial;
}
table#tb-goals .datepicker {
	width: 120px ! important;
	margin-right: 3px
}
table#tb-goals #name-period {
	width: 100% !important;
	margin-bottom: 3px;
	box-sizing: border-box;
}
table#tb-goals #save-new-period {
	cursor: pointer;
	font-size: 12px !important;
	padding: 5px;
}
table#tb-goals .seller-perf {
	text-align: right;
	padding-right: 10px !important;

}
.btn-state {
	padding: 1px 2px;
	color: #fff;
	font-weight: 600;
	cursor: pointer;
}
.btn-state:hover{
	color:#deded9;
}

.btn-state--actif {
    background-color: $green;
    border: 1px solid $green;
}

.btn-state--inactif {
    background-color: #FE5511;
    border: 1px solid #FE5511;
}

#popup-content table.colisage-table {
	margin-bottom: 15px !important;
}

.ord-products-menu-cols {
	position: absolute;
}

.ord-products-menu-cols .cols{
	overflow-y: hidden !important;
	height: auto !important;
	display: block;
	padding: 0px;
}

.ord-prd-name-input, .ord-prd-spacing-input {
	width: 100% !important;
	padding: 3px;
	box-sizing: border-box;
}

#ord-products tbody>tr:hover {
	background-color: #f3f3f3;
}

#ord-products tbody tr td textarea.ord-prd-name-input, #ord-products tbody tr td textarea.ord-prd-comment-input {
	height: auto;
	padding: 3px;
	box-sizing: border-box;
	width: 100%;
}

#ord-products tbody tr td input.ord-prd-input {
	box-sizing: border-box;
	height: 25px;
	padding: 3px;
}

#ord-products tbody tr td input.ord-prd-input {
	height: 25px;
	margin-bottom: 4px;
}

#ord-products tbody tr td {
	padding-bottom: 15px;
}


.ord-prd-nmc-row input.ord-prd-name-input {
	width: 50% !important;
}

.ord-duplicate-div {
	min-height: 40px;
}

.ord-duplicate-label {
	width: 100px;
	display: inline-block;
}

.popup-content .img-alt-form {
	margin-top: 30px;
}

.attempt_data pre {
	word-break: break-all;
	white-space: pre-wrap;
	width: 100%;
}

table#typing-template-tab {
	margin-bottom: 10px !important;
}

.print-mail-active {
	margin-right: 5px;
}

.print-mail-label {
    float: left;
    padding-right: 5px;
}

.print-mail-input {
	width: 345px;
}

#site-content .colissimo-generate-label {
	margin-bottom: 4px;
}

#site-content .colissimo-generate-label-insurance {
	width: 50px;
	text-align: right;
	margin-left: 20px;
}

 /* Fiche de commande (à remettre dans Layout) */
#table-une-commande .order-products-row > td {
	padding: 0;
}
#ord-products-articles {
	width: 100%;
	border-style: none !important;
	margin: 0 !important;
}
/* Prix HT, Qté, Prix TTC */
#ord-products-articles .align-right {
	white-space: nowrap;
}
#ord-products-articles tfoot th.align-right {
	padding-right: 10px;
}

.dlv_hour_plage{
  display: inline-block;
	padding: 10px;
	border-radius: 3px;
	margin: 2px;
	background: lightblue;
	border: 1px solid lightblue;
}
.dlv_hour_plage_select{
	border: 1px red solid !important;
}

​
a[target="_blank"], .external-link {
  background-image: url('/admin/images/lien-externe.svg');
  background-repeat: no-repeat;
  background-position: right center;
  &:after {
    content:"\00a0\00a0\00a0\00a0";
  }
}


/* Visuel permettant de matérialiser la période d’essai d'un abonnement Yuto Essentiel */
.information-offer {
	padding-right: 30px;

	h1 {
	  font-weight: bold;
	  margin-bottom: 30px;
	}
}


.block-frieze {
	display: flex;
	padding-right: 30px;
	width: 600px;

	& > .part {
	  display: flex;
	  flex-direction: column;
	  flex: 1 1 auto;

	  &:first-child > .frieze {
		border-top-left-radius: 25px;
		border-bottom-left-radius: 25px;
		background: #6462aa;
	  }
	  &:last-child > .frieze {
		border-top-right-radius: 25px;
		border-bottom-right-radius: 25px;
		background: #6462aa; /* Old browsers */
		background: -moz-linear-gradient(left, #6462aa 0%, #00172E 100%); /* FF3.6-15 */
		background: -webkit-linear-gradient(left, #6462aa 0%,#00172E 100%); /* Chrome10-25,Safari5.1-6 */
		background: linear-gradient(to right, #6462aa 0%,#00172E 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6462aa', endColorstr='#00172E',GradientType=1 ); /* IE6-9 */
	  }
	  & > *:first-child {
		display: flex;
		align-items: center;
		font-size: 0.8em;
		padding: 5px;
		.icon {
		  width: 35px;
		  height: 35px;
		  border-radius: 50%;
		  text-indent: -999999px;
		  background-size: contain;
		  background-repeat: no-repeat;
		  overflow: hidden;
		}
		 .part-title {
		   flex: 1 1 auto;
		   text-align: center;
		   font-size: 1.2rem;
		   margin: 0;
		   color: #fff;
		 }
	  }
	  & > .date {
		border-left: 2px solid  #6462aa;
		padding: 0 10px;
		margin: 15px 0 0 20px;
	  }
	  & > .date p {
		color:  #6462aa;
		margin: 0;

		&:first-child {
		  font-weight: 600;
		}
	  }
	}
}

.icon-check { background-image: url(../../images/icon-circle-check.svg) }
.icon-facturation {background-image: url(../../images/icon-circle-facturation.svg) }



/* Abonnement Yuto Essentiel */
.yuto-notice{
	margin-top: 10px;
}


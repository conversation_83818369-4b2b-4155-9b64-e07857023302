<?php

	/** \file popup-pattern.php
	 *	Cette popup s'affiche au clic sur le lien "Modifier le pattern" que l'on peut trouver sur les pages d'édition de promotions spéciales.
	 *	Elle permet de gérer le pattern sur lequel le code promotion sera généré automatiquement par le système.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');

	require_once('promotions.inc.php');
	
	$code_var = array( 'promo_generated_length', 'promo_generated_number', 'promo_generated_upper', 'promo_generated_lower', 'promo_generated_similar', 'promo_generated_prefixe', 'promo_generated_suffixe' );
	
	// Mise à jour des paramètres
	if( isset($_POST) && sizeof($_POST) ){
		$number  = isset($_POST['number'])  ? 1 : 0;
		$lower   = isset($_POST['letter'])   ? 1 : 0;
		$upper   = isset($_POST['letter'])   ? 1 : 0;
		$similar = isset($_POST['similar']) ? 1 : 0;
		$length  = isset($_POST['length'])  ? $_POST['length'] : 13;
		$prefixe = isset($_POST['prefixe']) && trim($_POST['prefixe'])!='' ? $_POST['prefixe'] : '';
		$suffixe = isset($_POST['suffixe']) && trim($_POST['suffixe'])!='' ? $_POST['suffixe'] : '';
		
		$can_test = true;
		if( !$number && !$lower && !$upper ){
			$error = _("Vos codes promotions doivent au minimum être constitué de chiffres ou de lettres.");
			$can_test = false;
		}elseif( preg_match('/[^0-9a-z\-_]/i', $prefixe) ){
			$error = _("Le préfixe ne doit être consitué que de chiffres, de lettre ou des caractères \"-\" ou \"_\".");
			$can_test = false;
		}elseif( preg_match('/[^0-9a-z\-_]/i', $suffixe) ){
			$error = _("Le suffixe ne doit être consitué que de chiffres, de lettre ou des caractères \"-\" ou \"_\".");
			$can_test = false;
		}else{
			if( isset($_POST['save']) ){
				if(    !cfg_overrides_set_value( 'promo_generated_length', $length, 0)
					|| !cfg_overrides_set_value( 'promo_generated_number', $number, 0)
					|| !cfg_overrides_set_value( 'promo_generated_upper', $upper, 0)
					|| !cfg_overrides_set_value( 'promo_generated_lower', $lower, 0)
					|| !cfg_overrides_set_value( 'promo_generated_similar', $similar, 0)
					|| !cfg_overrides_set_value( 'promo_generated_prefixe', $prefixe, 0)
					|| !cfg_overrides_set_value( 'promo_generated_suffixe', $suffixe, 0)
				){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}else{
					$config['promo_generated_length'] 	= $length;
					$config['promo_generated_number'] 	= $number;
					$config['promo_generated_upper'] 	= $upper;
					$config['promo_generated_lower'] 	= $lower; 
					$config['promo_generated_similar'] 	= $similar;
					$config['promo_generated_prefixe'] 	= $prefixe;
					$config['promo_generated_suffixe'] 	= $suffixe;
					
					header('Location: /admin/promotions/specials/popup-pattern.php');
					exit;
				}
			}
		}
	}
	
	$var = array( 
		'promo_generated_length' 	=> 13, 
		'promo_generated_number' 	=> 1, 
		'promo_generated_upper' 	=> 1, 
		'promo_generated_lower' 	=> 1, 
		'promo_generated_similar' 	=> 1,
		'promo_generated_prefixe' 	=> '',
		'promo_generated_suffixe' 	=> '' 
	);

	if( isset($_POST) && sizeof($_POST) ){
		$var = array( 
			'promo_generated_length' 	=> isset($_POST['length'])  ? $_POST['length'] : 13, 
			'promo_generated_number' 	=> isset($_POST['number'])  ? 1 : 0, 
			'promo_generated_upper' 	=> isset($_POST['letter'])   ? 1 : 0, 
			'promo_generated_lower' 	=> isset($_POST['letter'])   ? 1 : 0, 
			'promo_generated_similar' 	=> isset($_POST['similar']) ? 1 : 0,
			'promo_generated_prefixe' 	=> isset($_POST['prefixe']) && trim($_POST['prefixe'])!='' ? $_POST['prefixe'] : '',
			'promo_generated_suffixe' 	=> isset($_POST['suffixe']) && trim($_POST['suffixe'])!='' ? $_POST['suffixe'] : '' 
		);
	}else{
		$rc = cfg_overrides_get( 0, array(), $code_var );
		if( $rc && ria_mysql_num_rows($rc)==7 ){
			while( $c = ria_mysql_fetch_array($rc) ){
				$var[ $c['code'] ] = $c['value'];
			}
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Modifier le pattern des codes promotions') . ' - '  . _('Promotions'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<div id="site-content">
		<form action="/admin/promotions/specials/popup-pattern.php" method="post" class="form-popup-pattern">
			<h2><?php echo _("Sélectionnez les options de vos codes promotions"); ?></h2>
			<?php
				if( isset($error) ){
					print '
						<div class="error">'.nl2br( $error ).'</div>
					';
				}
			?>
			
			<div class="elem">
				<label for="length"><?php echo _("Nombre de caractères :"); ?></label>
				<select name="length" id="length"><?php
					for( $i=4 ; $i<=13 ; $i++ ){
						$selected = $var['promo_generated_length']==$i ? 'selected="selected"' : '';
						print '<option value="'.$i.'" '.$selected.'>'.$i.'</option>';
					}
				?></select>
				<label for="length"><?php echo _("caractères"); ?> <span style="font-size: 0.75em;"><?php echo _("(Hors préfixe et suffixe)"); ?></span></label>
			</div>
			
			<div class="elem">
				<input <?php print $var['promo_generated_number'] ? 'checked="checked"' : ''; ?> type="checkbox" name="number" id="number" value="1" />
				<label for="number"><?php echo _("Avec des chiffres (0, 1, 2, 3, 4, 5, 6, 7, 8, 9)"); ?></label>
			</div>
			
			<div class="elem">
				<input <?php print $var['promo_generated_lower'] || $var['promo_generated_upper'] ? 'checked="checked"' : ''; ?> type="checkbox" name="letter" id="letter" value="1" />
				<label for="letter"><?php echo _("Avec des lettres (A, B, C, D, E, F, G, H ... X, Y, Z)"); ?></label>
			</div>
			
			<!-- div class="elem">
				<input <?php // print $var['promo_generated_lower'] ? 'checked="checked"' : ''; ?> type="checkbox" name="lower" id="lower" value="1" />
				<label for="lower">Avec des lettres minuscules (a, b, c, d, e, f, g, h ... x, y, z) </label>
			</div>
			
			<div class="elem">
				<input <?php // print $var['promo_generated_upper'] ? 'checked="checked"' : ''; ?> type="checkbox" name="upper" id="upper" value="1" />
				<label for="upper">Avec des lettres majuscules (A, B, C, D, E, F, G, H ... X, Y, Z)</label>
			</div -->
			
			<div class="elem">
				<input <?php print $var['promo_generated_similar'] ? 'checked="checked"' : ''; ?> type="checkbox" name="similar" id="similar" value="1" />
				<label for="similar"><?php echo _("Ne pas inclure les caractères similaires (0, O, I, 1, l)"); ?></label>
			</div>
			
			<div class="elem">
				<label for="prefixe"><?php echo _("Préfixe automatique :"); ?></label>
				<input type="text" name="prefixe" id="prefixe" value="<?php print $var['promo_generated_prefixe'] && trim($var['promo_generated_prefixe'])!='' ? $var['promo_generated_prefixe'] : ''; ?>" />
			</div>
			
			<div class="elem">
				<label for="suffixe"><?php echo _("Suffixe automatique :"); ?></label>
				<input type="text" name="suffixe" id="suffixe" value="<?php print $var['promo_generated_suffixe'] && trim($var['promo_generated_suffixe'])!='' ? $var['promo_generated_suffixe'] : ''; ?>" />
			</div>
			
			<?php
				if( isset($_POST['test']) && $can_test ){
					print '
						<div id="code-like">'  ._("Échantillon") . '</div>
						<ul id="code-list-like">
					';
					
					for( $i=0 ; $i<12 ; $i++ ){
						print '
							<li>'.pmt_codes_generated( $var ).'</li>
						';
					}
					
					print '
						</ul>
						<div class="clear"></div>
					';
				}
			?>
			<div id="pmt-rules-buttons">
				<input class="btn-action float-right" type="submit" name="save" id="save" value="<?php echo _("Enregistrer"); ?>" />
				<input class="btn-action float-left" type="submit" name="test" id="test" value="<?php echo _("Échantillon"); ?>" title="<?php echo _("Générer un échantillon de codes promotion"); ?>" />
			</div>
		</form>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>
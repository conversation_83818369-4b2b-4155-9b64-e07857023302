<?php

	/**	\file index.php
	 *	Cette page affiche la liste des promotions spéciales et permet leur gestion (ajout / modification / suppression)
	 */

	require_once('promotions.inc.php');

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( $_GET['type'] == _PMT_TYPE_SOLDES ){
		// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE');
	}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
		// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD');
	}else{
		// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');
	}

	if( !isset($_GET['type']) || !is_numeric($_GET['type']) || $_GET['type']<=0 ){
		header('Location: /admin/promotions/index.php');
		exit;
	}

	$rtype = pmt_types_get( $_GET['type'], false );
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		header('Location: /admin/promotions/index.php');
		exit;
	}

	$type = ria_mysql_fetch_array( $rtype );

	// Export des cartes cadeaux
	if (isset($_POST['export-pmt-gifts'])) {
		$param = '';

		if (isset($_POST['pmt']) && is_array($_POST['pmt'])) {
			foreach ($_POST['pmt'] as $pmt_id) {
				$param .= (trim($param) != '' ? '&' : '?').'pmt[]='.$pmt_id;
			}
		}

		header('Location: /admin/promotions/specials/export-gifts.php'.$param);
		exit;
	}

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: /admin/promotions/specials/edit.php?id=0&type='.$_GET['type']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_POST['pmt']) && is_array($_POST['pmt']) ){
		foreach( $_POST['pmt'] as $p )
			pmt_codes_del($p);
		header('Location: /admin/promotions/specials/index.php?type='.$_GET['type']);
		exit;
	}
	$active = isset($_GET['active']) ? $_GET['active'] : false;
	if($active === false){
		$active = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
	}
	else{
		session_set_periodpicker_state($active);
	}

	// Filtre sur le site
	$wst_id = 0;
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}
	$wst_id = isset($_GET['wst_id']) ? $_GET['wst_id'] : $wst_id;
	$wst_id = is_numeric($wst_id) && $wst_id>0 ? $wst_id : 0;

	$website_selector = view_websites_selector( $wst_id, true, 'riapicker', true );
	if( trim($website_selector) == '' ){
		$wst_id = 0;
	}

	if ( $_GET['type']== _PMT_TYPE_SOLDES ){
		$can_add = gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_ADD');
		$can_view = gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_VIEW');
		$can_del = gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_DEL');
	}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
		$can_add = gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_ADD');
		$can_view = gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_VIEW');
		$can_del = gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_DEL');
	}else{
		$can_add = gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_ADD');
		$can_view = gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_VIEW');
		$can_del = gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_DEL');
	}

	// Calcul du nombre de colspan
	$checkbox = $can_del;
	$colspan = 7;
	if( in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS)) ){
		$colspan--;
	}

	if( in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ) {
		$colspan--;
	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Promotions'), '/admin/promotions/index.php' );

	if( isset($_GET['type']) && ($type = ria_mysql_fetch_array( pmt_types_get($_GET['type'], false) )) && isset($_GET['id']) ){
		Breadcrumbs::add( $type['name'], '/admin/promotions/specials/index.php?type='.$type['id'] );
	}


	define('ADMIN_PAGE_TITLE', _( $type['name'] ). ' - '._('Promotions'));
	require_once('admin/skin/header.inc.php');

	//	On gère les codes pré-existants en les adaptant pour view_promo_state_periodpicker()
	$opened = false;
	if( $active==='1' ){
		$opened = true;
	}elseif( $active==='0' ){
		$opened = false;
	}elseif( $active==='2' ){
		$opened = -1;
	}elseif( $active==='-2' ){ //	code "en attente" (futurs ?)
		$opened = -2;
	}else{ // On peut directement envoyer les valeurs, ce ne sont pas des codes pré-existants
		$opened = intval($active);
	}

	// Charge la liste des promotions
	$promotions = pmt_codes_get( null, null, $opened, $_GET['type'], false, in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ? 0 : null, null, null, null, false, false, false, $wst_id );
	$promotions_count = ria_mysql_num_rows($promotions);
?>
<h2><?php print htmlspecialchars(_($type['name'])).' ('.ria_number_format($promotions_count).')'; ?></h2>
<div class="stats-menu">
	<?php
		print $website_selector;
		view_state_periodpicker($active, "selectoractivated", 7); ?>
	<div class="clear"></div>
</div>

<form action="index.php?type=<?php print $type['id']; ?>" method="post" class="form-promo-specials">
<input type="hidden" name="wst_id" id="wst_id" value="<?php print $wst_id; ?>" />
<input type="hidden" name="active" id="active" value="<?php print $active; ?>" />

<?php
	if( $_GET['type']==_PMT_TYPE_REMISE ){
		print '<p>' . _("Les promotions de type \"REMISES\" sont, à l'inverse des autres promotions, non cumulables entre elles. Cela veut dire, que seule la remise la plus avantageuse sera appliquée sur une commande.") . '</p>';
	}
	if( $_GET['type']==_PMT_TYPE_SOLDES ){
		print '<p>' . _("Les promotions de type \"SOLDES\" sont, à l'inverse des autres promotions, non cumulables entre elles. Cela veut dire, que seule la remise la plus avantageuse sera appliquée sur une commande.") . '</p>';
	}
?>
<p class="notice"><?php echo str_replace("#param[nom]#", strtolower(htmlspecialchars($type['name'])), _("Les #param[nom]# sur fond vert sont actuellement publiés. En bleu sont ceux qui seront publiés ultérieurement.")) ; ?></p>

<table id="table-codes-promo" class="checklist">
	<thead>
		<tr>
			<?php if( $checkbox ){ ?>
				<th id="pmt-select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<?php }
				if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS)) ){
					print '<th id="pmt-label" class="col180px">Libellé</th>';
				}

				if( !in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ) {
					print '<th id="pmt-code">Code</th>';
				}
			?>
			<th id="pmt-start"><?php echo _("Ouverture"); ?></th>
			<th id="pmt-stop"><?php echo _("Fermeture"); ?></th>
			<th id="pmt-desc"><?php echo _("Description"); ?></th>
			<th id="pmt-state"><?php echo _("Etat"); ?></th>
		</tr>
	</thead>
	<tbody>
		<?php
			if( ria_mysql_num_rows($promotions)==0 ){
				print '
					<tr>
						<td colspan="'.$colspan.'">
							' . _("Aucune promotion de type :") . ' '.htmlspecialchars($type['name']).'
						</td>
					</tr>
				';
			}else{
				while( $r = ria_mysql_fetch_array($promotions) ){
					print '
						<tr class="'.($r['state'] == "opened" ? 'opened' : ($r['state'] == "incoming" ? 'incoming' : '')).'">
					';
					if( $checkbox ){
						print '
							<td headers="pmt-select">
								<input type="checkbox" class="checkbox" name="pmt[]" value="'.$r['id'].'" />
							</td>';
					}

					if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS)) ){
						print '
							<td headers="pmt-label">';
						if( $can_view ){
						print ' <a href="edit.php?id='.$r['id'].'&amp;type='.$_GET['type'].'">
									'.( $_GET['type']==_PMT_TYPE_CHEEKBOOK ? _('Chéquier n°') : '' ).'
									'.htmlspecialchars( $r['name'] ).'
								</a>';
						}else{
							print ( $_GET['type']==_PMT_TYPE_CHEEKBOOK ? _('Chéquier n°') : '' ).' '.htmlspecialchars( $r['name'] );
						}
						print '
							</td>
						';
					}

					if( !in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ){
						print '
							<td headers="pmt-code">';
						if( $can_view ){
							print '	<a href="edit.php?id='.$r['id'].'&amp;type='.$_GET['type'].'">
									'.htmlspecialchars( $r['code'] ).'
								</a>';
						}else{
							print $r['code'];
						}
						print '
							</td>
						';
					}

					print '
							<td headers="pmt-start">'.ria_date_format($r['date_start']).'</td>
							<td headers="pmt-stop">'.ria_date_format($r['date_stop']).'</td>
							<td headers="pmt-desc">'.htmlspecialchars( pmt_codes_describe($r)).( $r['type']!=_PMT_TYPE_REWARD ? (trim(pmt_codes_describe($r))!='' ? '<br />' : '').htmlspecialchars( $r['desc'] ) : '' ).'</td>
							<td headers="pmt-state" class="pmt-state">
					';

					if( !in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ){
						switch( $r['state'] ){
							case 'incoming':
								print _('En attente'); break;
							case 'opened':
								print _('En cours'); break;
							case 'closed':
								print _('Terminée'); break;
						}
					} else {
						$stats = pmt_codes_parents_get_stats( $r['id'] );
						print $stats['nb_cod_used'].' / '.$stats['nb_cod'].' utilisé'.( $stats['nb_cod_used']>1 ? 's' : '' );
					}

					print '
							</td>
						</tr>
					';
				}
			}
		?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="<?php print $colspan; ?>">
				<?php
				if( $promotions_count ){
					if( in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ){
						$title = $_GET['type']==_PMT_TYPE_BA ? 'bons d\'achat' : 'chéquiers';
						print '
							<input type="submit" name="export" id="export-codes" class="btn-del" value="' . _("Exporter") . '" title="' . _("Exporter les codes promotions contenus dans les '.$title.' sélectionnés.") . '" />
						';
					}
					if( $can_del ){ ?>
					<input type="submit" name="del" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les codes promotions sélectionnés"); ?>" onclick="return pmtConfirmDelList()" />
					<?php
						}
						if ($_GET['type'] == _PMT_TYPE_GIFTS) { ?>
							<input type="submit" name="export-pmt-gifts" class="btn-del" value="<?php echo _("Exporter"); ?>" title="<?php echo _("Exporter les cartes cadeaux sélectionnées (si aucune sélection, toutes les cartes seront exportés"); ?>" /><?php
						}
				}
				if( $can_add ){ ?>
					<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un code promotion"); ?>" />
				<?php } ?>
			</td>
		</tr>
	</tfoot>
</table>
</form>
	<script><!--
		var typePromotion = <?php print $type['id']; ?>;
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
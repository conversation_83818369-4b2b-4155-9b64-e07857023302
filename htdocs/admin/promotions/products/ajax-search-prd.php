<?php
	/** \file ajax-search-prd.php
	 * 	Ce fichier permet de retourner une liste de produits pour l'auto-complétion utilisé lors de la création d'une promotion sur produits.
	 * 	\param $_GET['q'] correspond au texte recherché
	 */
	require_once( 'products.inc.php' );
	require_once( 'strings.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_EDIT');

	$results = array();
	$term = isset($_GET['term']) ? $_GET['term'] : ( isset($_GET['q']) ? $_GET['q'] : '' );
	if( trim($term)!='' ){
		$search = search3( 1, $term, 1, 30, false, false, 6, array('prd') );
		if( $search && ria_mysql_num_rows($search) ){
			while( $r_prd = ria_mysql_fetch_array($search) ){
				$prd = ria_mysql_fetch_array(prd_products_get($r_prd['tag'],'',0,false,0,0,-1,false,false,false,false,false,false,false,false,false,false,true,false,false,null,null));
				$name = $prd['title']!='' ? $prd['title'] :$prd['name'];
				if( $name != '' ){
					$results[] = $prd['ref'].' - '.htmlspecialchars($name);
				}
			}
		}
	}

	print json_encode($results);
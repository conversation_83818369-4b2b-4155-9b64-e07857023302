<?php
	/** \file edit.php
	 * 	Ce fichier permet d'éditer un groupe de promotions sur produits.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');

	require_once('products.inc.php');
	require_once('prices.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à accès pour intervenir sur les promotions sur les produits
	if( !gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_EDIT') && gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_VIEW') ){
		header('Location: /admin/promotions/index.php');
		exit;
	}

	if( !isset($_GET['group']) ){
		header('Location: index.php');
		exit;
	}
	$groups = prc_promotion_groups_get($_GET['group']);
	if( !$groups || !ria_mysql_num_rows($groups) ){
		header('Location: index.php');
		exit;
	} else {
		$groups = ria_mysql_fetch_array($groups);
	}

	$r_types = prc_types_get();
	$type_option = '';
	if( $r_types!==false && ria_mysql_num_rows($r_types)>0 ){
		while( $type = ria_mysql_fetch_array($r_types) ){
			$type_option .= '		<option value="'.$type['id'].'" >'.htmlspecialchars( $type['name'] ).'</option>';
		}
	}

	// Récupère les conditions possibles
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);

	$fld_opt = '<option value="-1">' . _("Choisissez un champ personnalisé") . '</option>';
	// Construit les option des select pour l'ajout d'une condition, le select sera construit lors de l'appel pour avoir l'identifiant de la promotions
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			if( $fld['cls_id']!=CLS_PRODUCT ){
				$fld_opt .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
			}
		}

		// Retourne au premier index dans les tableaux MySQL
		ria_mysql_data_seek( $r_fld, 0 );
	}

	if( isset($_POST['save-pmt']) ){
		if( !isset($_POST['prc-prd']) && !isset($_POST['prd-id']) ){
			$prices = prc_prices_get( 0, 0, false, false, false, false, false, false, false,  null, null, 1, 0, false, null, array(), array(), null, true, $_GET['group'] );
			if( $prices!=false ){
				while( $price = ria_mysql_fetch_array($prices) ){
					if( !prc_price_conditions_del( $price['id'] ) || !prc_prices_del($price['id']) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
					}
				}
			}

			if( !isset($error) && !prc_promotion_groups_del($_GET['group']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}

			if( !isset($error) ){
				header('Location: index.php');
				exit;
			}
		} else {
			$_POST['qte-max'] = $_POST['qte-max']=='' || in_array($_POST['qte-max'], array('&#8734;', '∞')) ? false : $_POST['qte-max'];
			$grp = $_GET['group'];
			if( trim($_POST['name'])!='' ){
				$priceminister_key = isset($_POST['priceminister_key']) && is_numeric($_POST['priceminister_key']) && $_POST['priceminister_key'] > 0 ? $_POST['priceminister_key'] : 0;

				if( !prc_promotion_groups_update($grp, $_POST['name'], $_POST['date-start'].' '.$_POST['hour-start'], $_POST['date-end'].' '.$_POST['hour-end'], $_POST['qte-min'], $_POST['qte-max'], false, $priceminister_key) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}

			// Supprime les conditions sur le groupe
			if( !prc_group_conditions_del($grp) ){
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}

			if( isset($_POST['prc-prd']) && (!is_array($_POST['prc-prd']) || !sizeof($_POST['prc-prd'])) ){
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}

			if( !isset($error) ){
				$id_for_cdt = array();
				if( isset($_POST['prc-prd']) && is_array($_POST['prc-prd']) ){
					foreach( $_POST['prc-prd'] as $prc=>$prd ){
						if( !prc_prices_set_date_activation($prc, $_POST['date-start'].' '.$_POST['hour-start'], $_POST['date-end'].' '.$_POST['hour-end'])
						|| !prc_prices_set_qte_min( $prc, $_POST['qte-min'] )
						|| !prc_prices_set_qte_max( $prc, $_POST['qte-max'] )
						|| !prc_prices_set_name( $prc, $_POST['name'] )
						|| !prc_price_conditions_del($prc)
						){
							$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}
						$id_for_cdt[] = $prc;
					}
				}

				if( isset($_POST['prd-id']) && sizeof($_POST['prd-id'])>0 ){
					foreach( $_POST['prd-id'] as $prd ){
						$value = $_POST['prd-remise-val-'.$prd];

						if (($_POST['prd-remise-'.$prd] == 1 || $_POST['prd-remise-'.$prd] == 3)
							&& (
									( isset($_POST['prd-remise-valeur-type-'.$prd]) && $_POST['prd-remise-valeur-type-'.$prd] == 2 )
									||
									( isset($_POST['cat-remise-valeur-type-'.$prd]) && $_POST['cat-remise-valeur-type-'.$prd] == 2 )
								)
							) {
							$rprd = prd_products_get($prd);
							if ($rprd && ($p = ria_mysql_fetch_assoc($rprd)) && $p['tva_rate']) $value /= $p['tva_rate'];
						}

						if( !$prc = prc_prices_add($_POST['prd-remise-'.$prd], $value, $_POST['date-start'].' '.$_POST['hour-start'], $_POST['date-end'].' '.$_POST['hour-end'], $_POST['qte-min'], $prd, 0, false, false, $_POST['name'], array(), $_POST['qte-max'], true, $grp) ){
							$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}
						$id_for_cdt[] = $prc;
					}
				}

				if( !isset($error) && sizeof($id_for_cdt)>0 && isset($_POST['cdt-new']) ){
					$add_cdt_group = false;
					$count_cdt = sizeof($_POST['cdt-new']);
					foreach( $id_for_cdt as $prc ){
						$cdt = $_POST['cdt-new'];
						$sbl = isset($_POST['sbl-new']) ? $_POST['sbl-new'] : '=';
						$val = isset($_POST['val-cdt-new']) ? $_POST['val-cdt-new'] : false;

						foreach( $_POST['cdt-new'] as $key=>$fld ){
							if( $fld>0 ){
								// On récupère le nom et le type du champ personalisé
								$fld_name = fld_fields_get_name($fld);
								$fld_type = fld_fields_get_type($fld);

								$sb = '';
								if( $fld_type==FLD_TYPE_DATE && (isset($_POST['choose-date-'.$id]) && $_POST['choose-date-'.$id]>0 || isset($_POST['choose-date-new']) && $_POST['choose-date-new']>0) ){
									// Il s'agit d'un champ personnalisé de type date, le traitement est différent
									$chooseDate = isset($_POST['choose-date-new']) ? $_POST['choose-date-new'] : $_POST['choose-date-'.$id];
									switch( $chooseDate ){
										case 1 :
											if( $_POST['val-cdt-date-new-'.$chooseDate]==-1 )
												$error = ( $count_cdt>1 ? _("Une erreur s'est produite lors de la mise à jour des conditions. \nVeuillez sélectionner une des valeurs suivantes : Aujourd'hui, Cette semaine, Ce mois-ci.") : _("Une erreur s'est produite lors de la mise à jour de la condition. \nVeuillez sélectionner une des valeurs suivantes : Aujourd'hui, Cette semaine, Ce mois-ci.") ) ;
										case 2 :
											$sb = $_POST['sbl-date-new-'.$_POST['choose-date-new']];
											$value = $_POST['val-cdt-date-new-'.$_POST['choose-date-new']];
											break;
										case 3 :
											$sb = "><";
											$value = $_POST['val-cdt-date-new-3'].';'.$_POST['val-cdt-date-new-3-2'];
											break;
									}

									if( $value=='' || $value==';' )
										$error = $count_cdt>1 ? _("Une erreur s'est produite lors de la mise à jour des conditions. \nAucune valeur n'a été sélectionnée ou définie.") : _("Une erreur s'est produite lors de la mise à jour de la condition. \nAucune valeur n'a été sélectionnée ou définie.") ;
										;
								} elseif( $fld_type==FLD_TYPE_DATE && !isset($_POST['choose-date-'.$id]) && !isset($_POST['choose-date-new']) ){
									$error = $error = $count_cdt>1 ? _("Une erreur s'est produite lors de la mise à jour des conditions. \nAucune valeur n'a été sélectionnée ou définie.") : _("Une erreur s'est produite lors de la mise à jour de la condition. \nAucune valeur n'a été sélectionnée ou définie.");
								} else {
									// Il s'agit d'un champ personnalisé autre que le type date, le traitement est le même.
									$sb = isset($sbl[$key]) ? $sbl[$key] : '=';
									if( !isset($val[$key]) )
										$value = false;
									else
										$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-new-2'][$key] : $val[$key];
								}

								if( !isset($error) && $sb!=='' ){
									// On vérifie si la condition est valable
									switch( (int)prc_conditions_is_valid($fld, $sb, $value) ){
										case FLD_NOT_EXISTS :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />Le champ personnalisé #param[nom_champs]# ne peut être utilisé.")) : str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />Le champ personnalisé #param[nom_champs]# ne peut être utilisé."));
											break;
										case INVALID_SYMBOL_FOR_FIELD :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />L'opérateut #param[nom_champs]# ne peut être utilisé pour ce champ personnalisé.")) : str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />L'opérateut #param[nom_champs]# ne peut être utilisé pour ce champ personnalisé.")) ;
											break;
										case WRONG_SIZE_OF_VALUE :
											$error =  $count_cdt>1 ? _("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />L'opérateur \"Est compris entre\" nécessite deux valeurs.") : _("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />L'opérateur \"Est compris entre\" nécessite deux valeurs.");
											break;
										case VALUE_NOT_NUMERIC :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />Le champ #param[nom_champs]# nécessite une valeur de type numérique.")) : str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />Le champ #param[nom_champs]# nécessite une valeur de type numérique.") );
											break;
										case VALUE_NOT_INTEGER :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />Le champ #param[nom_champs]# nécessite un chiffre entier.")) : str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />Le champ #param[nom_champs]# nécessite un chiffre entier."));
											break;
										case SELECT_VALUE_NOT_EXISTS :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champs]#.")) : str_replace("#param[nom_champs]#", $fld_name,_("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champs]#.") );
											break;
										case VALUE_NOT_BOOLEAN :
											$error = $count_cdt>1 ? str_replace("#param[nom_champs]#", $fld_name, _("Une erreur s'est produite lors de la mise à jour des conditions de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champs]#, veuillez choisir entre \"oui\" ou \"non\".")) : str_replace("#param[nom_champs]#", $fld_name, _("Une erreur s'est produite lors de la mise à jour de la condition de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champs]#, veuillez choisir entre \"oui\" ou \"non\".") );
											break;
										default :  // la condition est valide
											if( !prc_price_conditions_add( $prc, $fld, $value, $sb ) )
												$error = $count_cdt>1 ? _("Une erreur inattendue s'est produite lors de la mise à jour des conditions de la promotion.<br />Veuillez réessayer ou prendre contact avec l'administrateur.") : _("Une erreur inattendue s'est produite lors de la mise à jour de la condition de la promotion.<br />Veuillez réessayer ou prendre contact avec l'administrateur.");
											if( $grp>0 && !$add_cdt_group ){
												if( !prc_group_conditions_add( $grp, $fld, $value, $sb ) )
													$error = $count_cdt>1 ? _("Une erreur inattendue s'est produite lors de la mise à jour des conditions de la promotion. \nVeuillez réessayer ou prendre contact avec l'administrateur.") : _("Une erreur inattendue s'est produite lors de la mise à jour de la condition de la promotion. \nVeuillez réessayer ou prendre contact avec l'administrateur.") ;
											}
											break;
									}
								}
							}
						}
						$add_cdt_group = true;
					}
				}
			}

			if( !isset($error) && isset($_POST['prc-prd'], $_POST['prc-remise'], $_POST['prc-remise-val']) ){
				// mise à jours des valeurs
				foreach( $_POST['prc-prd'] as $prc=>$prd ){
					$date_start = $_POST['date-start'].' '.$_POST['hour-start'];
					$date_end = $_POST['date-end'].' '.$_POST['hour-end'];

					$value = $_POST['prc-remise-val'][$prc];

					if (($_POST['prc-remise'][$prc] == 1 || $_POST['prc-remise'][$prc] == 3) && isset($_POST['prc-remise-valeur-type'][$prc]) && $_POST['prc-remise-valeur-type'][$prc] == 2) {
						$rprd = prd_products_get($prd);
						if ($rprd && ($p = ria_mysql_fetch_assoc($rprd)) && $p['tva_rate']) $value /= $p['tva_rate'];
					}

					if( !prc_prices_update($prc, $_POST['prc-remise'][$prc], $value, $date_start, $date_end, $_POST['qte-min'], $prd, 0, true, '', $_POST['qte-max']) ){
						$error = _("Une erreur inattendue s'est produite lors de la mise à jour des remises sur les produits.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
					}
				}
			}

			if( !isset($error) ){
				header('Location: index.php?group='.$_GET['group']);
				exit;
			}
		}
	}

	// Bouton Supprimer
	if( isset($_POST['del-pmt']) ){
		$prices = prc_prices_get( 0, 0, false, false, false, false, false, false, false,  null, null, 1, 0, false, null, array(), array(), null, true, $_GET['group'] );
		if( $prices!=false ){
			while( $price = ria_mysql_fetch_array($prices) ){
				if( !prc_price_conditions_del( $price['id'] ) || !prc_prices_del($price['id']) )
					$error = _("Une erreur inattendue s'est produite lors de la suppression de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}

		if( !isset($error) && !prc_promotion_groups_del($_GET['group']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}

		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}

	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Promotions'), '/admin/promotions/index.php' )
		->push( _('Promotions sur les produits'), '/admin/promotions/products/index.php' )
		->push( _('Nouvelle promotion') );

	define('ADMIN_PAGE_TITLE', _('Edition') . ' - ' . _('Promotions sur les produits') . ' - ' . _('Promotions'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("Nouvelle promotion"); ?></h2>
<p>	<?php echo _("Ce formulaire vous permettra de remettre à niveau toutes les promotions de produit dans le groupe"); ?> "<?php print htmlspecialchars( $groups['name'] ); ?>".
	<br /><?php echo _("Toutes les conditions (ajoutées, modifiées ou supprimées) ou les modifications apportées, après la création du groupe, seront annulées."); ?></p>
<?php
	if( isset($error) )
		print '<div class="error">'.nl2br( $error) .'</div>';
?>
<form id="frm-add-promo" method="post" action="edit.php?group=<?php print $_GET['group']; ?>">
	<table id="promo-info">
		<caption><?php echo _("Informations"); ?></caption>
		<col width="300" /><col width="300" />
		<thead>
			<tr><th colspan="2"></th></tr>
		</thead>
		<tfoot>
			<tr>
				<td style="float:left">
					<input onclick="addCdtEditPromoProducts()" type="button" name="add-cdt" id="add-cdt" value="<?php echo _("Ajouter une condition"); ?>" />
				</td>
				<td>
					<input type="submit" name="save-pmt" id="save-pmt" value="<?php echo _("Enregistrer"); ?>" onclick="return verifPromotion()" />
					<input type="submit" name="del-pmt" id="del-pmt" value="<?php echo _("Supprimer"); ?>" onclick="return delGroups()" />
					<input type="button" onclick="window.location.href='index.php';" name="cancel-pmt" id="cancel-pmt" value="<?php echo _("Annuler"); ?>" />
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td><label for="date-start"><span class="mandatory">*</span> <?php echo _("Date de début :"); ?></label></td>
				<td>
					<input class="datepicker date" type="text" name="date-start" id="date-start" value="<?php print $groups['date-start']; ?>" />
					<label for="hour-start"><?php echo _("à"); ?></label>
					<input type="text" maxlength="5" value="00:00" id="hour-start" name="hour-start" class="hour hourpicker ac_input" />
				</td>
			</tr>
			<tr>
				<td><label for="date-end"><span class="mandatory">*</span> <?php echo _("Date de fin :"); ?></label></td>
				<td>
					<input class="datepicker date" type="text" name="date-end" id="date-end" value="<?php print $groups['date-stop']; ?>" />
					<label for="hour-end"><?php echo _("à"); ?></label>
					<input type="text" maxlength="5" value="23:59" id="hour-end" name="hour-end" class="hour hourpicker ac_input" />
				</td>
			</tr>
			<tr>
				<td><label for="qte-min"><?php echo _("Pour une quantité minimale de :"); ?></label></td>
				<td><input class="qte" type="text" name="qte-min" id="qte-min" value="<?php print $groups['qte-min']; ?>" /></td>
			</tr>
			<tr>
				<td><label for="qte-max"><?php echo _("Pour une quantité maximale de :"); ?></label></td>
				<td><input class="qte" type="text" name="qte-max" id="qte-max" value="<?php print $groups['qte-max']>0 ? $groups['qte-max'] : '&#8734;'; ?>" /></td>
			</tr>
			<tr>
				<td><label for="name"><?php echo _("Nom :"); ?></label></td>
				<td><input type="text" name="name" id="name" value="<?php print $groups['name']; ?>" /></td>
			</tr>
			<?php if( ctr_comparator_tenants_used(CTR_PRICEMINISTER) ){ ?>
				<tr>
					<td><label for="priceminister_key"><?php echo _("Campagne Priceminister :"); ?></label></td>
					<td><input type="text" name="priceminister_key" id="priceminister_key" value="<?php print $groups['priceminister_key']; ?>" /></td>
				</tr>
			<?php } ?>
			<tr id="cdt-header">
				<th colspan="2"><?php echo _("Conditions"); ?></th>
			</tr>
			<tr><?php
				print '<td id="cdts" colspan="2">'.prc_conditions_view( $groups['id'], TERMS_PROMO ).'</td>';
			?></tr>
		</tbody>
	</table>

	<div class="notice" style="width:595px;margin-bottom:5px;"><?php echo _("Ce tableau permet d'ajouter plusieurs produits en même temps, avec la même remise, dans cette promotion."); ?></div>
	<table id="tb-promo-remise">
		<caption><?php echo _("Groupe de produit"); ?></caption>
		<col width="600" />
		<thead>
			<tr>
				<th><?php echo _("Remise du groupe"); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td>
					<input onclick="addGroupInPromotion()" style="float:left" type="button" name="add-prd-promo" id="add-prd-promo" value="<?php echo _("Ajout à la promotion"); ?>" title="<?php echo _("Ajouter le groupe de produits à la promotion"); ?>" />
					<label for="ref-prd-add-1"><?php echo _("Référence :"); ?></label>
					<input onkeyup="searchReferencePrd('ref-prd-add-1')" type="text" name="ref-prd-add-1" id="ref-prd-add-1" value="" />
					<input type="button" name="add-prd" id="add-prd-grp" value="<?php echo _("Ajouter"); ?>" onclick="addProductInGroups()"/>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td>
					<select name="remise" id="remise">
						<option value="2"><?php echo _("Remise en %"); ?></option>
						<option value="3"><?php echo _("Remise en valeur"); ?></option>
					</select>
					<select name="remise-valeur-type" id="remise-valeur-type">
						<option value="1"><?php echo _("HT"); ?></option>
						<option value="2"><?php echo _("TTC"); ?></option>
					</select>
					<input type="text" name="remise-val" id="remise-val" value="" />
				</td>
			</tr>
			<tr>
				<th><?php echo _("Produits"); ?></th>
			</tr>
			<tr id="grp-none-prd">
				<td colspan="2"><?php echo _("Aucun produit"); ?></td>
			</tr>
			<tr>
				<td id="grp-prd-promo" style="display:none"></td>
			</tr>
		</tbody>
	</table>

	<div class="notice" style="width:595px;margin-bottom:5px;"><?php echo _("Ce tableau contient tous les produits ajoutés dans la promotion, il permet aussi d'ajouter ou modifier une remise, produit par produit. La promotion doit contenir au minimum un produit, dans le cas contraire, le groupe de promotions sera supprimé lors de son enregistrement."); ?></div>
	<table id="tb-prd-promo">
		<caption><?php echo _("Produit(s)"); ?></caption>
		<col width="300"/><col width="200"/><col width="100"/>
		<thead>
			<tr>
				<th id="prd-ref"><?php echo _('Référence'); ?></th>
				<th id="prd-remise"><?php echo _('Remise'); ?></th>
				<th id="prd-remise-val"><?php echo _('Valeur'); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td style="float:left">
					<input type="button" name="del-all-prd" id="del-all-prd" onclick="delAllPrc()" value="<?php echo _("Vider"); ?>" />
				</td>
				<td colspan="2">
					<label for="ref-prd-add"><?php print _('Référence :'); ?></label>
					<input onkeyup="searchReferencePrd('ref-prd-add')" type="text" name="ref-prd-add" id="ref-prd-add" value="" />
					<input style="padding:2px;" type="button" name="add-prd" id="add-prd" value="<?php echo _("Ajouter"); ?>" onclick="addProductInPromotion()"/>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<?php
				$prices = prc_prices_get( 0, 0, false, false, false, false, false, false, false, null, null, 1, 0, false, null, array(), array(), null, true, $groups['id'] );
				if( $prices!=false && ria_mysql_num_rows($prices)>0 ){
					$r_types = prc_types_get();
					while( $price = ria_mysql_fetch_array($prices) ){
						$price_bare_ht = '';
						if( $rprc = prd_products_get_price($price['prd']) ){
							if( $prc = ria_mysql_fetch_array($rprc) )
								$price_bare_ht = $prc['price_ht'];
						}

						if( $config['tnt_id'] == 39 ){
							$stock = null;

							$rp = prd_products_get_simple( $price['prd'] );
							if( $rp && ria_mysql_num_rows($rp) ){
								$p = ria_mysql_fetch_assoc( $rp );
								$stock = $p['stock'];
							}
						}

						print '	<tr id="trprc-'.$price['id'].'">';
						print '		<td headers="prc-ref">';
						$rcat = prd_products_categories_get($price['prd'], true);
						print '				<div id="prc-'.$price['id'].'">';
						print '					<input type="hidden" name="prc-prd['.$price['id'].']" id="prc-prd-'.$price['prd'].'" value="'.$price['prd'].'" />';
						print '					<img onclick="delPrcOfPromotion('.$price['id'].')" class="del" src="/admin/images/del.svg" alt="'._('Supprimer').'" title="' . _("Retirer le produit") . '" />';
												if( $rcat!=false && ria_mysql_num_rows($rcat)>0 ){
													$cat = ria_mysql_fetch_array($rcat);
													print '<a href="/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$price['prd'].'" target="_bank">'.prd_products_get_ref($price['prd']).'</a>';
												} else {
													print '<cat-prd><![CDATA[0]]></cat-prd>';
												}
						print '					<br /><span class="info-fld">Prix de base HT : '.number_format($price_bare_ht, 2, '.', ' ').' &euro;</span>';

						if( $config['tnt_id'] == 39 && $stock !== null ){
							print '					<br /><span class="info-fld">'.( $stock > 0 ? _('En stock (').$stock.')' : _('En rupture') ).'</span>';
						}

						print '				</div>';
						print ' 		</td>';
						print ' 		<td headers="prc-remise">';
						print ' 			<select class="prc-remise" name="prc-remise['.$price['id'].']" id="prc-remise-'.$price['id'].'">';
						if( $r_types!==false && ria_mysql_num_rows($r_types)>0 ){
							while( $type = ria_mysql_fetch_array($r_types) )
								print '		<option value="'.$type['id'].'" '.( $type['id']==$price['type'] ? 'selected="selected"' : '' ).'>'.$type['name'].'</option>';
						}
						print '				</select>';
						print '				<select id="cat-remise-valeur-type-'.$price['id'].'" name="prc-remise-valeur-type['.$price['id'].']">
												<option value="1">' . _("HT") . '</option>
												<option value="2">' . _("TTC") . '</option>
											</select>';
						print ' 		</td>';
						print ' 		<td>';
						print ' 			<input type="text" name="prc-remise-val['.$price['id'].']" id="prc-remise-val-'.$price['id'].'" value="'.$price['value'].'" />';
						print ' 		</td>';
						print ' 	</tr>';
						ria_mysql_data_seek( $r_types, 0 );
					}
				}
				print '<tr>
						<td id="none-prd" colspan="3" '.( !$prices || !ria_mysql_num_rows($prices) ? '' : 'style="display:none"' ).'>' . _("Aucun produit lié à cette promotion") . '</td>
					</tr>';
			?>
		</tbody>
	</table>
</form>

<script><!--
	var typeOption = '<?php print $type_option; ?>';
	var typeOptionValeurType = '<option value="1">HT</option><option value="2">TTC</option>';
	var fldOpt = '<?php print $fld_opt; ?>';
--></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php
	/** \file ajax-product-promotion.php
	 * 	Ce fichier charge un XML contenant la liste des produits publiés correspondant à une liste de références de produits passés en paramètre.
	 * 	\param $_POST['ref'] Chaine contenant une liste des références séparés par un tiret
	 * 	Ce XML est utilisé pour gérer l'ajout d'une même promotion sur plusieurs produits en même temps.
	 * 	
	 * 	Retourne le XML et pour chaque catégorie : 
	 * 		id-prd : identifiant
	 * 		ref-prd : référence
	 * 		price-prd : prix public du produit
	 * 		cat-prd : identifiant de la première catégorie publiée où est classé le produit (peut être égale à 0 = aucune catégorie publiée trouvée)
	 */
	require_once( 'products.inc.php' );
	
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_VIEW');

	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';
	$error = false;
	
	if( isset($_POST['ref']) ){
		
		$pos = strpos($_POST['ref'], ' -');
		if( $pos ){
			$_POST['ref'] = substr( $_POST['ref'], 0, $pos );
		}

		$rprd = prd_products_get( 0, trim($_POST['ref']), 0, false, 0, 0, -1, false, false, false, false, false, false, false, false, false, false, false, false, false, null, null );
		
		if( $rprd!=false && ria_mysql_num_rows($rprd)>0 ){
			
			$prd = ria_mysql_fetch_array($rprd);
			$price_bare_ht = '';
			if( $rprc = prd_products_get_price($prd['id']) ){
				if( $prc = ria_mysql_fetch_array($rprc) )
					$price_bare_ht = $prc['price_ht'];
			}
			
			$xml .= '<result type="1">';
			$xml .= '<id-prd><![CDATA['.$prd['id'].']]></id-prd>';
			$xml .= '<ref-prd><![CDATA['.$prd['ref'].']]></ref-prd>';
			$xml .= '<price-prd><![CDATA['.number_format($price_bare_ht, 2, '.', ' ').']]></price-prd>';
			
			$rcat = prd_products_categories_get($prd['id'], true);
			if( $rcat!=false && ria_mysql_num_rows($rcat)>0 ){
				$cat = ria_mysql_fetch_array($rcat);
				$xml .= '<cat-prd><![CDATA['.$cat['cat'].']]></cat-prd>';
			} else {
				$xml .= '<cat-prd><![CDATA[0]]></cat-prd>';
			}
			
			$xml .= '</result>';
		} else {
			$xml .= '<result type="0"><error><![CDATA[';
			$xml .= ']]></error>\n</result>';
		}
		
	}
	
	print $xml;
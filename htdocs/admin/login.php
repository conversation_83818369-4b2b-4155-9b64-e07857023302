<?php
/**	\file login.php
 *	Cette page contient à la fois :
*	- le formulaire de connexion au back-office RiaShop
*	- le formulaire "mot de passe oublié"
*	- le formulaire "nouveau mot de passe"
*	Elle accepte les paramètres suivants :
*	- $_GET['dest'] : url de destination post connexion. Attention à bien contrôler ce paramètre pour se protéger des XSS
*/

require_once('Administrator.inc.php');
require_once('users.inc.php');
require_once('SAML.inc.php');

// Initialise une connexion SAML
if( isset($_POST['saml']) && $_POST['saml'] ){
	$sp_config = RiaSaml::getInstance()->codeByRegex($_POST['email']);
	if( $sp_config === false ){
		http_403();
	}

	// L'internaute est renvoyé vers la page gérant la connexion à Okta
	header('Location: /saml/'.urlencode($sp_config).'-okta.php');
	exit;
}

// Applique le choix de la langue
if( isset($_GET['lang']) ){
	$_SESSION['lang'] = $_GET['lang'];
	header('Location: /admin/login.php');
	exit;
}

if( isset($_GET['dest']) ){
	$_SESSION['login_dest'] = urldecode($_GET['dest']);
}

// Détermine l'étape de connexion
if( !isset($_POST['step']) || $_POST['step'] == 'login' ){
	$_SESSION['step_login'] = [ 'step' => 'login', 'email' => '' ];
}

// Connexion à l'administration
if( isset($_POST['login'], $_POST['email'], $_POST['password']) ){
	// L'utilisateur a fourni ses informations de connexion, tente de le connecter
	// (uniquement s'il s'agit d'un administrateur)
	try{
		$profiles = array(PRF_ADMIN);

		// L'accès à l'administration peut-être donné aux revendeurs en activant une variable de configuration "seller_admin_access"
		if( isset($config) && ria_array_get($config, 'seller_admin_access', false) ){
			$profiles[] = PRF_SELLER;
		}

		if( !gu_users_login($_POST['email'], $_POST['password'], true, $profiles, '', '', true, array(), 0, true) ){ // Tente de le logger
			if( isset($_POST['email']) && trim($_POST['email']) != '' ){
				$_SESSION['step_login'] = [ 'step' => 'password', 'email' => $_POST['email'] ];
			}

			$error = _("Login ou mot de passe incorrect");
		}else{
			// Gère la conservation de la destination (uniquement si l'url est locale, pour éviter une redirection vers un autre site)
			if( isset($_SESSION['login_dest']) && substr($_SESSION['login_dest'],0,strlen($config['site_uri']))==$config['site_uri'] ){
				$dest = $_SESSION['login_dest'];
				unset($_SESSION['login_dest']);
			}else{
				$dest = 'index.php';
			}

			// Supprime les éléments de la session qui ont servi à la connexion
			unset($_SESSION['step_login']);

			header( 'Location: '.$dest );
			exit;
		}
	}catch(\Login\LoginAttemptException $e) {
		if( isset($_POST['email']) && trim($_POST['email']) != '' ){
			$_SESSION['step_login'] = [ 'step' => 'password', 'email' => $_POST['email'] ];
		}

		$error = true;
	}
}

// Envoi la demande de ré-initialisation du mot de passe
if( isset($_POST['pwd']) ){
	// Gère l'envoi de l'email "Mot de passe oublié", qui permet de retrouver l'accès à son compte
	if( !( isset($_POST['email']) && trim($_POST['email']) ) ){
		$error = _('Vous devez renseigner votre email.');
	}else{
		$res = false;

		if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
			$res = Administrator::sendLostPassword($_POST['email']);
		}else{
			$profiles = array(PRF_ADMIN);
			if( ria_array_get($config, 'seller_admin_access', false) ){
				$profiles[] = PRF_SELLER;
			}
			$res = gu_users_send_lostpassword($_POST['email'], $profiles, true);
		}

		if( $res ){
			$success = _('Vous recevrez d\'ici quelques minutes un email contenant les instructions qui vous permettront de retrouver l\'accès à votre compte Administrateur (pensez à regarder dans vos spams).');
		}
	}
}

// Réinitilise le mot de passe
if( isset($_POST['save-new-pwd'])) {
	// Gère l'enregistrement d'un nouveau mot de passe, suite à l'utilisation de la fonction "Mot de passe oublié"
	try{
		if( !( isset($_POST['email']) && trim($_POST['email']) ) ){
			throw new Exception(_('Vous devez renseigner votre email.'));
		}

		if( !( isset($_POST['password']) && trim($_POST['password']) ) ){
			throw new Exception(_('Vous devez renseigner votre nouveau mot de passe.'));
		}

		if( !( isset($_POST['password2']) && $_POST['password']==$_POST['password2'] ) ){
			throw new Exception(_('Vous avez fait une erreur en recopiant votre mot de passe.'));
		}

		if( !gu_valid_password($_POST['password'], 32) ){
			throw new Exception($config['password_error_message']);
		}

		if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
			// Environnement administration unifiée
			// Réinitialisation du mot de passe
			$res = Administrator::reinitPassword($_POST['email'], $_POST['password'], $_GET['p']);
			if( $res === -1 ){
				throw new Exception(_('Votre session pour la réinitialisation du mot de passe a expirée.'));
			}

			if( !$res ){
				throw new Exception(_('Une erreur inattendue s\'est produite lors de l\'enregistrement de votre nouveau mot de passe.'));
			}
		}else{
			$r_user = gu_users_get(0, $_POST['email']);
			if (!$r_user || !ria_mysql_num_rows($r_user)) {
				throw new Exception(_('L\'adresse email saisie ne correspond pas à l\'adresse email enregistrée pour votre compte.'));
			}

			$user = ria_mysql_fetch_assoc($r_user);

			$token_unlock = md5($user['email'].$user['password'].$user['date_password'].'unlock');
			$token_generic = md5($user['email'].$user['password'].$user['date_password']);

			if ($token_unlock != $_GET['p'] && $token_generic != $_GET['p']) {
				throw new Exception(_('Votre session pour la réinitialisation du mot de passe a expirée.'));
			}

			if( !gu_users_update_password($user['id'], $_POST['password']) ){
				throw new Exception(_('Une erreur inattendue s\'est produite lors de l\'enregistrement de votre nouveau mot de passe.'));
			}

			if ($token_unlock == $_GET['p'] && $user['prf_id'] == PRF_ADMIN && !$user['can_login']) {
				gu_users_set_can_login($user['id'], true);
			}
		}

		$success = _('Votre mot de passe a été modifié avec succès.');
		$newPwdStep = 0;
	}catch(Exception $e){
		$error = $e->getMessage();
	}
}

// Activation de l'étape de "Mot de passe"
if( isset($_POST['step']) && $_POST['step'] == 'password' ){
	if( RegisterGCP::onGcloud() ){
		// Contrôle pour savoir s'il s'agit d'une connexion SAML 2
		$sp_config = RiaSaml::getInstance()->codeByRegex($_POST['email']);
		if( $sp_config !== false ){
			print 'saml';
			exit;
		}
	}

	$_SESSION['step_login'] = [ 'step' => 'login', 'email' => isset($_POST['email']) ? $_POST['email'] : '' ];

	if( isset($_POST['email']) && trim($_POST['email']) != '' ){
		$ctrl_user = false;
		// Contrôle que le compte existe bien
		if( RegisterGCP::onGcloud() && getenv('oneriashop') !== false ){
			if( Administrator::inRegister( $_POST['email'] ) ){
				$ctrl_user = true;
			}
		}else{
			$ctrl_user = gu_users_exists(0, array(PRF_ADMIN, PRF_SELLER), $_POST['email'] );
		}

		if( $ctrl_user ){
			$_SESSION['step_login'] = [ 'step' => 'password', 'email' => $_POST['email'] ];
		}else{
			$error = 'Compte RiaShop introuvable';
		}
	}
}

// Activation de l'étape "Mot de passe oublié ?"
if( isset($_POST['step']) && $_POST['step'] == 'forgot' ){
	$_SESSION['step_login']['step'] = 'forgot';
}

// Activation de l'étape "Ré-initialisation du mot de passe"
if( isset($_GET['new-pwd']) && $_GET['new-pwd'] == 2 ){
	// Contrôle que le token de ré-initialisation est bien présent
	if( !isset($_GET['p']) || trim($_GET['p']) == '' ){
		header('Location: /admin/index.php');
		exit;
	}

	$_SESSION['step_login']['step'] = 'reinit';
}

// $_SESSION['step_login'] = ['step' => 'password', 'email' => '<EMAIL>'];

if( !IS_AJAX ){
	// Définit le titre de la page
	define('ADMIN_PAGE_TITLE', _('Connexion'));
	define('ADMIN_HEAD_LOGIN', true);
	require_once('admin/skin/header.inc.php');

	print ''
		.'<div class="pictos">'
			.'<img src="/admin/images/login/icons/commande.svg" alt="Commande" class="commande-icon" />'
			.'<img src="/admin/images/login/icons/clients.svg" alt="Clients" class="clients-icon" />'
			.'<img src="/admin/images/login/icons/sage.svg" alt="Sage" class="sage-icon" />'
			.'<img src="/admin/images/login/icons/salesforce.svg" alt="Salesforce" class="salesforce-icon" />'
			.'<img src="/admin/images/login/icons/mediatheque.svg" alt="Médiathèque" class="mediatheque-icon" />'
			.'<img src="/admin/images/login/icons/couette-bleue.svg" alt="Couette" class="couette-icon" />'
			.'<img src="/admin/images/login/icons/flottants/statistiques.svg" alt="Statistiques" class="statistiques-icon" />'
			.'<img src="/admin/images/login/icons/outils.svg" alt="Outils" class="outils-icon" />'
		.'</div>'

		.'<div class="main-content flex">'
			.'<div class="vertical-center">'
				.'<div class="central-box txtcenter flex">'
					.'<div class="shadow">'
						.'<img alt="" src="/admin/images/login/loader.gif" width="128" heigh="128" />'
					.'</div>'
					.'<div class="logos flex">'
						.'<img src="/admin/images/login/logos/RS.svg" alt="RiaStudio" class="riastudio-logo">'
						.'<img src="/admin/images/login/logos/yuto.svg" alt="Yuto" class="yuto-logo">'
					.'</div>'

					.'<div id="content-login">';
}

if( $_SESSION['step_login']['step'] == 'login' ){
	print '<h1 class="main-title">'._('Bienvenue').'</h1>';

	if( isset($error) ){
		print '<p class="auth-text error">'.nl2br( $error ).'</p>';
	}

	print '<p class="auth-text">'._('Merci de vous authentifier pour accéder à notre plateforme de performance commerciale').'</p>'
			.'<form id="f-login" action="/admin/login.php" method="post" novalidate>'
				.'<div class="input-container">'
					.'<input type="email" name="email" class="email" id="email" aria-label="'._('Votre adresse email').'" value="'.( isset($_POST['email']) ? $_POST['email'] : '' ).'" />'
					.'<div class="pseudo-label mouse">'._('Votre adresse email').'*</div>'
					.'<p class="message">'._('Le format de l’adresse semble incorrect, veuillez réessayer.').'</p>'
					.'<input class="sp-hidden" type="password" name="sp" id="sp" value="'.( isset($_POST['sp']) ? $_POST['sp'] : '' ).'" />'
				.'</div>'
				.'<div class="form-footer flex">'
					.'<button type="submit" name="button-mail" class="flex">'._('Suivant').'</button>'
					.'<a href="mailto:<EMAIL>" class="form-footer-link flex" title="'._('Contactez-nous pour tout problème de connexion').'">'._('Problème de connexion ?').'</a>'
				.'</div>'
			.'</form>';
}elseif( $_SESSION['step_login']['step'] == 'password' ){
	print '<div class="main-title" title="'._('Retour à la page de bienvenue').'">'
				.'<a href="#">'
					.'<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'
							.'<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">'
									.'<circle fill="#F1F1F5" transform="translate(15.000000, 15.000000) scale(-1, 1) rotate(90.000000) translate(-15.000000, -15.000000) " cx="15" cy="15" r="15"></circle>'
									.'<g transform="translate(14.111111, 15.500000) scale(-1, -1) translate(-14.111111, -15.500000) translate(11.111111, 10.000000)" stroke="#7A869A" stroke-linecap="round" stroke-width="2">'
											.'<line x1="3.05555556" y1="4.69488536" x2="3.05555556" y2="11.3344559" transform="translate(3.055556, 8.014671) rotate(-315.000000) translate(-3.055556, -8.014671) "></line>'
											.'<line x1="3.05555556" y1="-1.30651046e-12" x2="3.05555556" y2="6.63957055" transform="translate(3.055556, 3.319785) rotate(-585.000000) translate(-3.055556, -3.319785) "></line>'
									.'</g>'
							.'</g>'
					.'</svg>'
					.'<span>'._('Connexion').'</span>'
				.'</a>'
			.'</div>'

			.'<div class="avatar flex">'
				.'<div class="img-avatar">'
					.'<img src="/admin/images/login/icons/avatar_default.svg" alt="Avatar" class="avatar-img">'
				.'</div>'

				.'<p class="mail-avatar mouse">'.htmlspecialchars( $_SESSION['step_login']['email'] ).'</p>'
			.'</div>'

			.'<form id="f-login-2" action="/admin/login.php" method="post" novalidate>'
				.'<div class="password-container input-container">'
					.'<input type="hidden" name="email" value="'.htmlspecialchars( $_SESSION['step_login']['email'] ).'" />'
					.'<input type="password" name="password" class="password'.( isset($error) ? ' error' : '' ).'" value="'.( isset($_POST['sp']) ? $_POST['sp'] : '' ).'" id="password" aria-label="'._('Votre mot de passe').'">'
					.'<div class="pseudo-label mouse">'._('Votre mot de passe').'*</div>'
					.'<p class="message">'._('Le mot de passe semble incorrect, veuillez réessayer.').'</p>'
					.'<span class="eye"></span>'
				.'</div>'
				.'<div class="form-footer flex">'
					.'<button type="submit" name="login" class="flex">'._('Connexion').'</button>'
					.'<a href="forgot-password.php" class="form-footer-link link-forgot flex" title="'._('Faire une demande de mot de passe oublié').'">'._('Mot de passe oublié ?').'</a>'
				.'</div>'
			.'</form>';
}elseif( $_SESSION['step_login']['step'] == 'forgot' ){
	print '<div class="main-title forgot">'
				.'<a href="#" title="'._('Retour à la page de connexion').'">'
					.'<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'
					    .'<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">'
					        .'<circle fill="#F1F1F5" transform="translate(15.000000, 15.000000) scale(-1, 1) rotate(90.000000) translate(-15.000000, -15.000000) " cx="15" cy="15" r="15"></circle>'
					        .'<g transform="translate(14.111111, 15.500000) scale(-1, -1) translate(-14.111111, -15.500000) translate(11.111111, 10.000000)" stroke="#7A869A" stroke-linecap="round" stroke-width="2">'
					            .'<line x1="3.05555556" y1="4.69488536" x2="3.05555556" y2="11.3344559" transform="translate(3.055556, 8.014671) rotate(-315.000000) translate(-3.055556, -8.014671) "></line>'
					            .'<line x1="3.05555556" y1="-1.30651046e-12" x2="3.05555556" y2="6.63957055" transform="translate(3.055556, 3.319785) rotate(-585.000000) translate(-3.055556, -3.319785) "></line>'
					        .'</g>'
					    .'</g>'
					.'</svg>'
					.'<span>'._('Mot de passe oublié').'</span>'
				.'</a>'
			.'</div>';

	if( isset($success) ){
		print '<p class="auth-text">'.nl2br( $success ).'</p>'
			.'<input type="hidden" name="email" value="'.( isset($_POST['email']) ? $_POST['email'] : '' ).'" />';
	}else{
		print '<p class="auth-text">'._('Saisissez votre adresse email pour réinitialiser votre mot de passe').'</p>'
				.'<form id="f-send-pwd" action="#" method="post" novalidate>'
					.'<input type="hidden" name="step" value="forgot" />'
					.'<div class="input-container">'
						.'<input type="email" name="email" class="email'.( isset($error) ? ' error' : '' ).'" value="'.( isset($_POST['email']) ? $_POST['email'] : '' ).'" id="email" aria-label="'._('Votre adresse email').'">'
						.'<div class="pseudo-label mouse">'._('Votre adresse email').'*</div>'
						.'<p class="message">'._('Le format de l’adresse semble incorrect, veuillez réessayer.').'</p>'
					.'</div>'
					.'<div class="form-footer flex">'
						.'<button type="submit" name="pwd" class="flex">'._('Envoyer').'</button>'
						.'<a href="mailto:<EMAIL>" class="form-footer-link flex" title="'._('Contactez-nous pour tout problème de connexion').'">'._('Problème de connexion ?').'</a>'
					.'</div>'
				.'</form>';
	}
}elseif( $_SESSION['step_login']['step'] == 'reinit' ){
	print '<div class="main-title forgot">'
				.'<a href="#"  title="'._('Retour à la page de connexion').'">'
					.'<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'
					    .'<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">'
					        .'<circle fill="#F1F1F5" transform="translate(15.000000, 15.000000) scale(-1, 1) rotate(90.000000) translate(-15.000000, -15.000000) " cx="15" cy="15" r="15"></circle>'
					        .'<g transform="translate(14.111111, 15.500000) scale(-1, -1) translate(-14.111111, -15.500000) translate(11.111111, 10.000000)" stroke="#7A869A" stroke-linecap="round" stroke-width="2">'
					            .'<line x1="3.05555556" y1="4.69488536" x2="3.05555556" y2="11.3344559" transform="translate(3.055556, 8.014671) rotate(-315.000000) translate(-3.055556, -8.014671) "></line>'
					            .'<line x1="3.05555556" y1="-1.30651046e-12" x2="3.05555556" y2="6.63957055" transform="translate(3.055556, 3.319785) rotate(-585.000000) translate(-3.055556, -3.319785) "></line>'
					        .'</g>'
					    .'</g>'
					.'</svg>'
					.'<span>'._('Nouveau mot de passe').'</span>'
				.'</a>'
			.'</div>';

	if( isset($error) ){
		print '<p class="auth-text error">'.nl2br( $error ).'</p>';
	}

	if( isset($success) ){
		print '<p class="auth-text">'.nl2br( $success ).'</p>';
	}else{
		print '<p class="auth-text">'._('Réinitialisez votre mot de passe en saisissant votre adresse email ainsi que votre nouveau mot de passe.').'</p>'
				.'<form id="f-reinit" action="#" method="post" novalidate autocomplete="off">'
					.'<input type="hidden" name="p" value="'.htmlspecialchars( $_GET['p'] ).'" />'
					.'<div class="input-container">'
						.'<input autocomplete="new-password" type="email" name="email" value="'.(isset($_POST['email']) ? $_POST['email'] : '' ).'" class="email" id="email" aria-label="'._('Votre adresse email').'">'
						.'<div class="pseudo-label mouse">'._('Votre adresse email').'*</div>'
						.'<p class="message">'._('Le format de l’adresse semble incorrect, veuillez réessayer.').'</p>'
					.'</div>'
					.'<div class="input-container">'
						.'<input autocomplete="new-password" type="password" name="password" value="'.(isset($_POST['password']) ? $_POST['password'] : '' ).'" class="password" id="password" aria-label="'._('Votre nouveau mot de passe').'">'
						.'<div class="pseudo-label mouse">'._('Votre nouveau mot de passe').'*</div>'
						.'<p class="message">'._('Le mot de passe semble incorrect, veuillez réessayer.').'</p>'
					.'</div>'
					.'<div class="input-container">'
						.'<input autocomplete="new-password" type="password" name="password2" value="'.(isset($_POST['password2']) ? $_POST['password2'] : '' ).'" class="password" id="password2" aria-label="'._('Confirmation du nouveau mot de passe').'">'
						.'<div class="pseudo-label mouse">'._('Confirmation du nouveau mot de passe').'*</div>'
						.'<p class="message">'._('La confirmation ne semble pas correspondre à votre mot de passe, veuillez réessayer.').'</p>'
					.'</div>'
					.'<div class="form-footer flex">'
						.'<button type="submit" name="save-new-pwd" class="flex">'._('Enregistrer').'</button>'
						.'<a href="mailto:<EMAIL>" class="form-footer-link flex" title="'._('Contactez-nous pour tout problème de connexion').'">'._('Problème de connexion ?').'</a>'
					.'</div>'
				.'</form>';
	}
}

if( !IS_AJAX ){
	print '</div>'
				.'<div class="footer-links flex">'
					.'<div id="cb1" class="cb">'
						.'<div class="cb_label">'
							.'<label id="cb1-label" for="cb1-edit" class="hidden">'._('Langue :').'</label>'
						.'</div>'
						.'<div id="cb1-button-label" class="hidden">'._('Ouvrir la liste des choix de langue').'</div>'
						.'<button id="cb1-button" class="cb_button flex" aria-labelledby="cb1-button-label" aria-controls="cb1-list" tabindex="-1">'
							.'<svg width="10px" height="6px" viewBox="0 0 10 6" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'
								.'<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">'
									.'<g transform="translate(-1.000000, 0.000000)" fill="#002251">'
										.'<path d="M2.89705627,0 L9.10294373,0 C9.76568542,-3.4378828e-16 10.3029437,0.5372583 10.3029437,1.2 C10.3029437,1.51825979 10.1765155,1.82348448 9.95147186,2.04852814 L6.84852814,5.15147186 C6.37989899,5.62010101 5.62010101,5.62010101 5.15147186,5.15147186 L2.04852814,2.04852814 C1.57989899,1.57989899 1.57989899,0.820101013 2.04852814,0.351471863 C2.27357179,0.126428208 2.57879649,5.02552584e-16 2.89705627,0 Z"></path>'
									.'</g>'
								.'</g>'
							.'</svg>'
							.'<div id="cb1-edit" class="cb_edit" type="text" role="combobox" aria-labelledby="cb1-label" aria-autocomplete="none" aria-readonly="true" aria-owns="cb1-list" tabindex="0">Français</div>'
						.'</button>'

						.'<ul id="cb1-list" class="cb_list" tabindex="-1" aria-expanded="true" role="listbox">';

							$ar_lang = array(
								'de_DE' => 'Deutsch',
								'en_GB' => 'English',
								'fr_FR' => 'Français',
								// 'it_IT' => 'Italiano',
								// 'pt_PT' => 'Português',
								// 'es_ES' => 'Spanish',
							);

							foreach( $ar_lang as $code=>$lang ){
								print '<li data-lang="'.htmlspecialchars( $code ).'" id="cb1-opt1" role="option" class="cb_option '.( $code == $_SESSION['lang'] ? 'selected' : '' ).'" tabindex="-1">'.htmlspecialchars( $lang ).'</li>';
							}

						print '</ul>'
					.'</div>'

					.'<a href="https://legal.riashop.fr/politique-de-confidentialite-des-donnees-personnelles/" target="_blank" title="'._('Voir la politique de confidentialité de RiaShop').'">'._('Confidentialité').'</a>'
					.'<a href="https://legal.riashop.fr/la-securite-de-vos-donnees-est-notre-priorite/" target="_blank" title="'._('Voir la politique de sécurité de RiaShop').'">'._('Sécurité').'</a>'
					.'<a href="https://legal.riashop.fr/conditions-generales-d-utilisation-du-service-riashop/" target="_blank" title="'._('Voir les CGU de RiaShop').'">'._('CGU').'</a>'
				.'</div>'
			.'</div>'

			.'<footer class="general-footer txtcenter">'
				.'<img src="/admin/images/login/logos/riashop.svg" alt="RiaShop" class="riashop-logo">'
				.'<p class="baseline dark-blue">Powerful solutions for successful stories</p>'
				.'<img src="/admin/images/login/icons/<EMAIL>" alt="Fusée" class="rocket">'
			.'</footer>'
		.'</div>'
		.'<p class="credits mouse flex">© 2005/2020 -&nbsp;<a href="https://www.riastudio.fr/" title="'._('Visiter le site de RiaStudio').'" target="_blank" class="mouse">RiaStudio SAS</a>&nbsp;- <span>'._('Tous droits réservés').'</span></p>'
	.'</div>';

	require_once('admin/skin/footer.inc.php');
}

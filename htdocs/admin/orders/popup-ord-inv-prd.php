<?php

    /**	\file popup-ord-inv-prd.php
	 *	Cette popup permet de définir quels sont les produits à facturer et en quelle quantité pour une commande.
	 *	Elle est accessible lors de la sélection de l'état partiellement facturée d'une commande
	 */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    require_once('define.inc.php');
    require_once('orders.inc.php');
    require_once('ord.invoices.inc.php');
    require_once('products.inc.php');

    $error = false;
    //Sauvegarde
    if( isset($_POST['save-main']) ){
        $passe = false;
        $state_invoice = true;
        //Vérifie les quantités saisies
        foreach( $_POST['inv-qte'] as $prd => $qte ){
            if( !is_numeric($qte) ){
                $error = sprintf(_('La quantité saisie pour le produit %s est incorrecte.'), prd_products_get_name($prd));
            }
            $prd_qte[$prd] = $qte;
            if( $_POST['qte-max'][$prd] < $qte || $qte < 0 ){
                $error = sprintf(_('La quantité saisie pour le produit %s est incorrecte.'), prd_products_get_name($prd));
            }
            if( $qte != $_POST['qte-max'][$prd] ){
                $state_invoice = false;
            }
            if( $qte > 0 ){
                $passe = true;
            }
        }

        if( !$passe ){
            $error = _("Aucune quantité saisie.");
        }

        //Création de la nouvelle facture
        if( !$error ){
            $rorder = ord_orders_get( 0, $_GET['ord'] );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				$error = _("Le chargement de la commande a échoué pour une raison inconnue.");
			}else{
				$order = ria_mysql_fetch_array( $rorder );

                //Ajout de la nouvelle facture
                $inv = ord_invoices_add( $order['user'], date("d/m/Y") );
                if( !$inv ){
                    $error = _("La création de la facture a échoué pour une raison inconnue");
                }else{
                    foreach( $_POST['inv-qte'] as $prd => $qte ){
                        if( $qte > 0){
                            $r_product = ord_products_get( $order['id'], false, $prd );
                            if( !$r_product || !ria_mysql_num_rows($r_product) ){
                                $error = _("Le chargement des produits de la commande a échoué pour une raison inconnue.");					
                            }else{
                                $product = ria_mysql_fetch_assoc($r_product);
                                ord_inv_products_add( $inv, $product['id'], $product['line'], $product['ref'], $product['name'], $product['price_ht'], $qte, $product['tva_rate'], $order['id']);
                            }
                        }
                    }
                  
                }
            }
        }

        if( !$error ){
            //Change l'état de la commande à "partiellement facturée" ou facturée si tout les produits on été facturés
            if( $state_invoice ){
                if( !ord_orders_update_status( $_GET['ord'], _STATE_INVOICE ) ){
                    $error = _("La modification du statut de la commande a échouée pour une raison inconnue.");
                }
            }else{
                if( !ord_orders_update_status( $_GET['ord'], _STATE_SUPP_PARTIEL_INV ) ){
                    $error = _("La modification du statut de la commande a échouée pour une raison inconnue.");
                }
            }
            
        }

        if( !$error ){
            print '	<script>
                        window.parent.location.reload();
                        window.parent.hidePopup();
                    </script>';
        }
    }

    define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _('Produits facturés'));
    define('ADMIN_NO_MOBILE_STYLE', true);
    require_once('admin/skin/header.inc.php');

    if( $error ){
        print '<div class="error">'.$error.'</div>';
    }

    ?>
    <div id="tabscontent">
        <h2>Définir les produits facturés</h2>
        <form method='POST' action="/admin/orders/popup-ord-inv-prd.php?ord=<?php print $_GET['ord']; ?>">
            <table id="inv-qte">
                <caption>Définir les produits facturés</caption>
                <colgroup><col width="150"></col><col width="*"></col><col width="300"></col></colgroup>
                <thead>
                    <tr>    
                        <th>Référence</th>
                        <th>Désignation</th>
                        <th>Quantité</th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <td colspan="3">
                            <input type="submit" name="save-main" id="save-main" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications')?>" />
                            <input type="submit" name="cancel-main" id="cancel-main" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications')?>" />
                        </td>
                    </tr>
                </tfoot>
                <tbody>
                    <?php
                        $qte_inv = array();
                        
                        //Récupère les factures lié à la commande
                        $r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $_GET['ord'], false, false, true );
                        if( $r_inv ){
                            while( $inv = ria_mysql_fetch_assoc($r_inv)){    
                                //Récupère la quantité des produits déja facturés
                                $r_inv_product = ord_inv_products_get( $inv['id']);
                                if( $r_inv_product ){
                                    while( $inv_product = ria_mysql_fetch_assoc($r_inv_product) ){
                                        if( isset($qte_inv[$inv_product['id']]) ){
                                            $qte_inv[$inv_product['id']] = $qte_inv[$inv_product['id']] + $inv_product['qte']; 
                                        }else{
                                            $qte_inv[$inv_product['id']] = $inv_product['qte']; 
                                        }
                                    }
                                }
                            }
                        }
                            
                        //Récupère les produits de la commande
                        $r_product = ord_products_get( $_GET['ord'] );
                        if( $r_product && ria_mysql_num_rows($r_product) ){
                            while( $product = ria_mysql_fetch_assoc($r_product) ){ 
                                $max = isset($qte_inv[$product['id']])? $product['qte'] - $qte_inv[$product['id']] : $product['qte']; ?>
                                <tr>
                                    <td><?php print $product['id']; ?></td>
                                    <td><?php print $product['name']; ?></td>
                                    <td align="center">
                                    <?php if( $max > 0 ){ ?>
                                        <input type="number" max="<?php print $max; ?>" class="inv-qte" id="inv-qte[<?php print $product['id']; ?>]" name="inv-qte[<?php print $product['id']; ?>]"  value="<?php print isset($prd_qte[$product['id']])? $prd_qte[$product['id']] : '0'; ?>"></br>
                                    <?php } ?>
                                        Commandée :<?php print $product['qte']; ?></br>
                                        Déja facturé :<?php print isset($qte_inv[$product['id']])? $qte_inv[$product['id']] : '0'; ?>
                                    </td>
                                    <input type="hidden" name="qte-max[<?php print $product['id']; ?>]" value="<?php print $max ?>"
                                </tr><?php
                            }
                        }else{
                            print '<tr><td colspan="3">' . _("Aucun produit trouvé pour cette commande") . '</td></tr>';
                        }

                    ?>
                   
                </tbody>
            </table>
        </form>
    </div>

    <script>
		//Bouton annuler
		$('#cancel-main').click(function(e){
            e.preventDefault();
            window.parent.location.reload();
			window.parent.hidePopup();
		});
	</script>
    <?php
	require_once('admin/skin/footer.inc.php');
?>
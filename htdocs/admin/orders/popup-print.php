<?php

	/**	\file popup-print.php
	 *	Ce fichier est une popup qui permet de paramétrer l'impression d'une pièce de vente. Pour l'instant,
	 *	seule l'impression des devis et des factures est supportée. Les autres types de pièces ne le sont pas encore.
	 *
	 *	<PERSON>ur fonctionner, cette popup à besoin des paramètres suivants :
	 *	 - type : Obligatoire, le type de pièce à imprimer. Les valeurs acceptées sont les suivantes : devis, invoice.
	 *	 - ord_id : Obligatoire, identifiant de la pièce de vente à imprimer
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	// Le paramètre type (de pièce) est obligatoire
    if (!isset($_GET['type']) 
        || ($_GET['type'] == 'devis' && !gu_user_is_authorized('_RGH_ADMIN_ORDER_DL_DEVIS'))
        || ($_GET['type'] == 'invoice' 
            && (!isset($_GET['inv_id']) || intval($_GET['inv_id']) <= 0) 
            && !gu_user_is_authorized('_RGH_ADMIN_ORDER_DL_INVOICE'))
    ) {
        header('Location: /admin/orders/orders.php');
        exit;
    }

    $is_ajax = false;
	
	// Le paramètre ord_id est obligatoire
    if (!isset($_GET['ord_id']) || intval($_GET['ord_id']) <= 0) {
        print '<div class="error">' . _('Un ou plusieurs paramètres obligatoires sont manquants pour le bon fonctionnement de cet écran.') . '</div>';
        exit;
    }

	// Charge la pièce de vente
    $r_order = ord_orders_get(0, $_GET['ord_id']);
    if (!$r_order || !ria_mysql_num_rows($r_order)){
        print '<div class="error">' . _('Une erreur est survenue lors de la récupération des informations sur la commande.') . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.") . '</div>';
        exit;
    }
    $order = ria_mysql_fetch_assoc($r_order);

    if (!$email = gu_users_get_email($order['user'])){
        $email = '';
    }

    $config_prefix = '';
    if ($_GET['type'] == 'devis') {
        $config_prefix = 'pdf_generation_devis_';
    } elseif ($_GET['type'] == 'invoice') {
        $config_prefix = 'pdf_generation_inv_';
    }

    $font_sizes = array(8,9,10,12,14,16,18,20,24);
    
    $data = array(
        'ref' => '',
        'prd_img' => isset($config[$config_prefix . 'prd_img']) && $config[$config_prefix . 'prd_img'],
        'prd_reduce' => isset($config[$config_prefix . 'prd_reduce']) && $config[$config_prefix . 'prd_reduce'],
        'prd_barcode' => isset($config[$config_prefix . 'prd_barcode']) && $config[$config_prefix . 'prd_barcode'],
        'prd_reftruncated' => isset($config[$config_prefix . 'prd_reftruncated']) && $config[$config_prefix . 'prd_reftruncated'],
        'header' => isset($config[$config_prefix . 'header']) && $config[$config_prefix . 'header'],
        'header_content' => isset($config[$config_prefix . 'header_content']) ? $config[$config_prefix . 'header_content'] : '',
        'footer' => isset($config[$config_prefix . 'footer']) && $config[$config_prefix . 'footer'],
        'footer_content' => isset($config[$config_prefix . 'footer_content']) ? $config[$config_prefix . 'footer_content'] : '',
        'display_payment' => isset($config[$config_prefix . 'display_payment']) && $config[$config_prefix . 'display_payment'],
        'display_dlv_address' => isset($config[$config_prefix . 'display_dlv_address']) && $config[$config_prefix . 'display_dlv_address'],
        'font_size' => isset($config[$config_prefix . 'font_size']) ? intval($config[$config_prefix . 'font_size']) : 12,
        'mail_active' => false,
        'mail_from' => $_SESSION['usr_email'],
        'mail_to' => $email,
        'mail_cc' => '',
        'mail_bcc' => ''
    );

	// Bouton Imprimer
    if (isset($_POST['print'])){
        if (!isset($_POST['ref']) || $_POST['ref'] == ""){
            $error = _("Veuillez saisir le champ référence.");
        } elseif (!ord_orders_ref_update($_GET['ord_id'], $_POST['ref'])){
            $error = _("Une erreur est survenue lors de la mise à jour de la référence de la commande.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
        }
        if (!isset($error)) {
            $data['ref'] = $_POST['ref'];
        }
        if (isset($_POST['prd-img'])) {
            $data['prd_img'] = intval($_POST['prd-img']) == 1;
        }
        if (isset($_POST['prd-reduce'])) {
            $data['prd_reduce'] = intval($_POST['prd-reduce']) == 1;
        }
        if (isset($_POST['prd-barcode'])) {
            $data['prd_barcode'] = intval($_POST['prd-barcode']) == 1;
        }
        if (isset($_POST['prd-reftruncated'])) {
            $data['prd_reftruncated'] = intval($_POST['prd-reftruncated']) == 1;
        }
        if (isset($_POST['header'])) {
            $data['header'] = intval($_POST['header']) == 1;
        }
        if ($data['header'] && isset($_POST['header-content'])) {
            $data['header_content'] = $_POST['header-content'];
        }
        if (isset($_POST['footer'])) {
            $data['footer'] = intval($_POST['footer']) == 1;
        }
        if ($data['footer'] && isset($_POST['footer-content'])) {
            $data['footer_content'] = $_POST['footer-content'];
        }
        if (isset($_POST['display-payment'])) {
            $data['display_payment'] = intval($_POST['display-payment']) == 1;
        }
        if (isset($_POST['display-dlv-address'])) {
            $data['display_dlv_address'] = intval($_POST['display-dlv-address']) == 1;
        }
        if (isset($_POST['font-size']) && in_array(intval($_POST['font-size']), $font_sizes)) {
            $data['font_size'] = intval($_POST['font-size']);
        }
        if (isset($_POST['mail-active'])) {
            $mail_active = true;
        }
        if (isset($_POST['mail-from'])) {
            $mail_from = $_POST['mail-from'];
        }
        if (trim($mail_from) && !isemail($mail_from)) {
            $error = 'L\'adresse d\'expéditeur saisie n\'est pas valide.';
        }
        if (isset($_POST['mail-to'])) {
            $mail_to = $_POST['mail-to'];
        }
        if (trim($mail_to) && !isemail($mail_to)) {
            $error = 'L\'adresse de destination saisie n\'est pas valide.';
        }
        if (isset($_POST['mail-cc'])) {
            $mail_cc = $_POST['mail-cc'];
        }
        if (isset($_POST['mail-bcc'])) {
            $mail_bcc = $_POST['mail-bcc'];
        }

        if(!isset($error)){
            require_once('Pdf/pdf.inc.php');

            if (!isset($_POST['mail-active']) || !$_POST['mail-active']) {
                $ord = null;
                if ($_GET['type'] == 'devis') {
                    generate_devis($_GET['ord_id'], true, null, $ord, false, false, $data);
                } elseif ($_GET['type'] == 'invoice') {
                    generate_invoice($_GET['ord_id'], $_GET['inv_id'], true, null, $ord, false, false, $data);
                }
                exit;
            } else {
                try {

                    if ($_GET['type'] == 'devis') {
                        send_devis(
                            $_GET['ord_id'],
                            true, 
                            null, 
                            $ord, 
                            $data,
                            false,
                            isset($_POST['mail-from']) && trim($_POST['mail-from']) 
                                ? $_POST['mail-from'] 
                                : false,
                            isset($_POST['mail-to']) && trim($_POST['mail-to']) 
                                ? $_POST['mail-to'] 
                                : false, 
                            isset($_POST['mail-cc']) && trim($_POST['mail-cc']) 
                                ? implode(',', explode(PHP_EOL, $_POST['mail-cc'])) 
                                : false, 
                            isset($_POST['mail-bcc']) && trim($_POST['mail-bcc']) 
                                ? implode(',', explode(PHP_EOL, $_POST['mail-cc'])) 
                                : false
                        );
                    } elseif ($_GET['type'] == 'invoice') {
                        send_invoice(
                            $_GET['ord_id'],
                            $_GET['inv_id'],
                            true,
                            null,
                            $ord,
                            $data,
                            false,
                            isset($_POST['mail-from']) && trim($_POST['mail-from'])
                                ? $_POST['mail-from']
                                : false,
                            isset($_POST['mail-to']) && trim($_POST['mail-to'])
                                ? $_POST['mail-to']
                                : false,
                            isset($_POST['mail-cc']) && trim($_POST['mail-cc'])
                                ? implode(',', explode(PHP_EOL, $_POST['mail-cc']))
                                : false,
                            isset($_POST['mail-bcc']) && trim($_POST['mail-bcc'])
                                ? implode(',', explode(PHP_EOL, $_POST['mail-cc']))
                                : false
                        );
                    }

                    $_SESSION['print-mail-send-success'] = 1;

                    header('Location: ' . $_SERVER['REQUEST_URI']);
                    exit;
                } catch (Exception $e) {
                    if ($e->getCode() > 0) {
                        $error = $e->getMessage();
                    } else {
                        $error = 'Erreur lors de l\'envoi par mail du document';
                    }
                }
            }
        }
    }

    // Si requête Ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Sélection du type d\'impression'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }

    if (isset($error)){
        print '<div class="error">'.htmlspecialchars($error).'</div>';
    }

    if (isset($_SESSION['print-mail-send-success'])) {
        print '<div class="success">' . _('PDF envoyé avec succès.') . '</div>';
        unset($_SESSION['print-mail-send-success']);
    }
?>
<form class="config-print-pdf" method="post" enctype="multipart/form-data">
    <input type="hidden" name="type" value="<?php print htmlspecialchars($_GET['type']); ?>" />
    <div class="config-row">
        <h2><?php print _('Configuration du fichier PDF') ?></h2>

        <h3><?php print _('Entête') ?></h3>

        <div>
			<span class="mandatory">*</span>
            <label for="ref"><?php print _('Référence :') ?></label>
            <input type="text" id="ref" name="ref" value="<?php print ord_orders_get_ref($_GET['ord_id']); ?>" />
        </div>
        <br />
        
        <?php if (isset($config[$config_prefix . 'display_dlv_address'])) { ?>
            <div>
                <label><?php print _('Souhaitez-vous afficher l\'adresse de livraison ?') ?></label>
                <label for="display-dlv-address-yes">
                    <input type="radio" id="display-dlv-address-yes" value="1" name="display-dlv-address"<?php print $data['display_dlv_address'] ? ' checked="checked"' : '' ?>/>
                    <?php print _('Oui'); ?>
                </label>
                <label for="display-dlv-address-no">
                    <input type="radio" id="display-dlv-address-no" value="0" name="display-dlv-address"<?php print $data['display_dlv_address'] ? '' : ' checked="checked"' ?>/>
                    <?php print _('Non'); ?>
                </label>
            </div>
        <?php } ?>
            
        <?php if (isset($config[$config_prefix . 'font_size']) 
            || isset($config[$config_prefix . 'prd_img'])
            || isset($config[$config_prefix . 'prd_reduce'])
            || isset($config[$config_prefix . 'prd_barcode'])
            || isset($config[$config_prefix . 'prd_reftruncated'])
            || (isset($config[$config_prefix . 'header']) && isset($config[$config_prefix . 'header_content']))
            || (isset($config[$config_prefix . 'footer']) && isset($config[$config_prefix . 'footer_content']))
        ) { ?>
        <h3><?php print _('Contenu') ?></h3>
        
            <?php if (isset($config[$config_prefix . 'font_size'])) { ?>
        <div>
            <label for="font-size"><?php print _('Taille de la police :') ?></label>
            <select id="font-size" name="font-size">
                <?php
                foreach ($font_sizes as $font_size) {
                    print '<option value="' . $font_size . '"' . ($data['font_size'] == $font_size ? ' selected="selected"' : '') . '>'. $font_size .'</option>';
                }
                ?>
            </select>
        </div>
        <br />
            <?php } ?>

            <?php if (isset($config[$config_prefix . 'prd_img'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher l\'image du produit ?') ?></label>
            <label for="prd-img-yes">
                <input type="radio" id="prd-img-yes" value="1" name="prd-img"<?php print $data['prd_img'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="prd-img-no">
                <input type="radio" id="prd-img-no" value="0" name="prd-img"<?php print $data['prd_img'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'prd_reduce'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher la remise appliquée au produit ?') ?></label>
            <label for="prd-reduce-yes">
                <input type="radio" id="prd-reduce-yes" value="1" name="prd-reduce"<?php print $data['prd_reduce'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="prd-reduce-no">
                <input type="radio" id="prd-reduce-no" value="0" name="prd-reduce"<?php print $data['prd_reduce'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'prd_barcode'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher code barre du produit ?') ?></label>
            <label for="prd-barcode-yes">
                <input type="radio" id="prd-barcode-yes" value="1" name="prd-barcode"<?php print $data['prd_barcode'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="prd-barcode-no">
                <input type="radio" id="prd-barcode-no" value="0" name="prd-barcode"<?php print $data['prd_barcode'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'prd_reftruncated'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous tronquer la référence du produit, si celle ci est trop longue ?') ?></label>
            <label for="prd-reftruncated-yes">
                <input type="radio" id="prd-reftruncated-yes" value="1" name="prd-reftruncated"<?php print $data['prd_reftruncated'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="prd-reftruncated-no">
                <input type="radio" id="prd-reftruncated-no" value="0" name="prd-reftruncated"<?php print $data['prd_reftruncated'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'header']) && isset($config[$config_prefix . 'header_content'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher un texte en début de document ?') ?></label>
            <label for="header-yes">
                <input type="radio" id="header-yes" value="1" name="header"<?php print $data['header'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="header-no">
                <input type="radio" id="header-no" value="0" name="header"<?php print $data['header'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
            <br />
            <textarea id="header-content" class="header-content" name="header-content" rows="5" cols="100" 
                placeholder="Renseignez ici le texte de début..."
                <?php print $data['header'] ? '' : ' style="display:none;"'; ?>
            ><?php 
                print htmlspecialchars($data['header_content']);
            ?></textarea>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'footer']) && isset($config[$config_prefix . 'footer_content'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher un texte en fin de document ?') ?></label>
            <label for="footer-yes">
                <input type="radio" id="footer-yes" value="1" name="footer"<?php print $data['footer'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="footer-no">
                <input type="radio" id="footer-no" value="0" name="footer"<?php print $data['footer'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
            <br />
            <textarea id="footer-content" class="footer-content" name="footer-content" rows="5" cols="100" 
                placeholder="Renseignez ici le texte de fin..."
                <?php print $data['footer'] ? '' : ' style="display:none;"'; ?>
            ><?php 
                print htmlspecialchars($data['footer_content']);
            ?></textarea>
        </div>
        <br />
            <?php } ?>
            <?php if (isset($config[$config_prefix . 'display_payment'])) { ?>
        <div>
            <label><?php print _('Souhaitez-vous afficher le mode de paiement utilisé, s\'il est renseigné ?') ?></label>
            <label for="display-payment-yes">
                <input type="radio" id="display-payment-yes" value="1" name="display-payment"<?php print $data['display_payment'] ? ' checked="checked"' : '' ?>/>
                <?php print _('Oui') ?>
            </label>
            <label for="display-payment-no">
                <input type="radio" id="display-payment-no" value="0" name="display-payment"<?php print $data['display_payment'] ? '' : ' checked="checked"' ?>/>
                <?php print _('Non') ?>
            </label>
        </div>
            <?php } ?>
        <?php } ?>

        <h3><input type="checkbox" class="print-mail-active" name="mail-active" <?php $data['mail_active'] ? ' checked="checked"' : '' ?> /><?php print _('Envoi par mail') ?></h3>
        <div>
            <label class="print-mail-label" for="mail-from"><span class="mandatory">*</span> <?php print _('Expéditeur :'); ?></label>
            <input class="print-mail-input" type="text" name="mail-from" value="<?php print htmlspecialchars($data['mail_from']);  ?>" />
        </div>
        <br />
        <div>
            <label class="print-mail-label" for="mail-to"><span class="mandatory">*</span> <?php print _('Destinataire :'); ?></label>
            <input class="print-mail-input" type="text" name="mail-to" value="<?php print htmlspecialchars($data['mail_to']) ?>" />
        </div>
        <br />
        <!-- <div>
            <label class="print-mail-label" for="mail-cc"><?php print _('Copie visible à :') ?></label>
            <textarea class="print-mail-input" cols="40" rows="7" name="mail-cc"><?php print htmlspecialchars($data['mail_cc']) ?></textarea>
        </div>
        <br />
        <div>
            <label class="print-mail-label" for="mail-bcc"><?php print _('Copie cachée à :') ?></label>
            <textarea class="print-mail-input" cols="40" rows="7" name="mail-bcc"><?php print htmlspecialchars($data['mail_bcc']); ?></textarea>
        </div> -->
    </div>
    <div class="ria-admin-ui-actions">
        <input type="submit" name="print" value="<?php print _('Valider'); ?>" />
    </div>
</form>

<script>
$(document).ready(
	function(){
        ;
    }
)
.delegate( // Accordéon sur le header
	'.config-print-pdf input[name=header]', 'click', function(event){
		$(".header-content").toggle( $(event.target).val() );
	}
)
.delegate( // Accordéon sur le footer
	'.config-print-pdf input[name=footer]', 'click', function (event) {
		$(".footer-content").toggle( $(event.target).val() );
	}
)
</script>
<?php

	/**	\file models.php
	 *	Cette page affiche la liste des Paniers modèles et permet leur gestion (ajout / modification / suppression)
	 */

	unset($error);

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL');

	require_once('fields.inc.php');
	require_once('orders.inc.php');

	// Bouton Ajouter
	if( isset($_POST['add-model']) ){
		header('Location: /admin/orders/model.php?ord=new');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del-models'], $_POST['del_model']) && is_array($_POST['del_model']) ){
		foreach( $_POST['del_model'] as $model ){
			$state = ord_orders_get_state($model);
			if( $state==_STATE_MODEL ){
				if( !ord_models_del($model) ){
					$error = _('Une erreur est survenue lors de la suppression d\'un modèle.');
				}
			}
		}
		if( !isset($error) ){
			header('Location: /admin/orders/models.php');
			exit;
		}
	}

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order']) ){
		ord_models_order_update( $_POST['order'] );
		header('Location: models.php');
		exit;
	}

	$orders = ord_orders_get_with_adresses( 0, 0, _STATE_MODEL);
	$ordered = ord_models_order_get();

	$orders_count = ria_mysql_num_rows($orders);

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Modèles de paniers') . ' - ' . _('Commandes'));
	require_once('admin/skin/header.inc.php');

	$state = ria_mysql_fetch_assoc( ord_states_get(_STATE_MODEL) );
	print '<h2 id="title-state">'.ucfirst(mb_strtolower($state['name_plural'], 'UTF-8')).'</h2>';


	// Calcul des paramètres de pagination
	if( !$ordered) {
		$by_page = 25;
		if( !isset($_GET['page']) || !is_numeric($_GET['page']) )
			$_GET['page'] = 1;
		$pages = ceil( $orders_count / $by_page );
		if( $_GET['page']>$pages )
			$_GET['page'] = $pages;
		if( $_GET['page']>1 )
			ria_mysql_data_seek( $orders, ($_GET['page']-1)*$by_page );
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_DEL');

	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
?>

<form action="models.php" method="post">
<table id="order-models" class="list checklist" cellpadding="0" cellspacing="0">
<thead>
	<tr>
		<?php if( $checkbox ){ ?>
			<th id="ord-del">
				<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
			</th>
		<?php } ?>
		<th id="ord-id"><?php print _('Numéro')?></th>
		<th id="ord-tref"><?php print _('Référence')?></th>
		<th id="ord-comment"><?php print _('Commentaires')?></th>
		<th id="ord-date" class="align-right"><?php print _('Date')?></th>
		<th id="ord-products" class="align-right"><?php print _('Produits')?></th>
		<?php if( $ordered ){ ?>
			<th id="ord-pos"><?php print _('Déplacer')?></th>
		<?php } ?>
	</tr>
</thead>
<tfoot>
	<tr>
		<td class="tdleft" colspan="4">
			<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_DEL') && ria_mysql_num_rows($orders) ){ ?>
			<input onclick="return confirm('<?php print _('Souhaitez vous vraiment supprimer ces modèles ?')?>')" type="submit" name="del-models" value="<?php print _('Supprimer')?>"/>
			<?php } ?>
		</td>
		<td colspan="<?php print $ordered ? 3 : 2; ?>">
			<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_ADD') ){ ?>
			<input type="submit" value="<?php print _('Créer un nouveau modèle')?>" name="add-model" />
			<?php } ?>
		</td>
	</tr>
	<?php if( ria_mysql_num_rows($orders)>1 ){ ?>
	<tr>
		<td colspan="<?php print $ordered ? 7 : 6; ?>" class="tfoot-grey">
			<label><?php print _('Trier ces catégories par ordre :')?></label>
			<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php print _('Alphabétique')?></label>
			<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php print _('Personnalisé')?></label>
			<input type="submit" name="orderby" value="<?php print _('Appliquer')?>" />
		</td>
	</tr>
	<?php } ?>
</tfoot>
<tbody>
<?php

	// Affichage des commandes
	if( !ria_mysql_num_rows($orders) ){
		print '<tr><td colspan="6">'._('Aucun modèle de paniers').'</td></tr>';
	}else{
		$current = 0;
		$count = ria_mysql_num_rows($orders);
		while( $r = ria_mysql_fetch_assoc($orders) ){
			if( $r!=null ){
				print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable">';
				if( $checkbox ){
					print '<td headers="ord-del"><input type="checkbox" name="del_model[]" value="'.$r['id'].'" /></td>';
				}
				if( gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_EDIT') ){
					print '<td headers="ord-id"><a href="model.php?ord='.$r['id'].'" title="'._('Afficher la fiche de ce modèle').'">'.ord_orders_name('',$r['piece'],$r['id']).'</a></td>';
				}else{
					print '<td headers="ord-id">'.ord_orders_name('',$r['piece'],$r['id']).'</td>';
				}
				print '<td headers="ord-tref">'.htmlspecialchars($r['ref']).'</td>';
				print '<td headers="ord-comment">'.htmlspecialchars($r['comments']).'</td>';
				print '<td headers="ord-date" class="align-right">'.ria_date_format($r['date']).'</td>';
				print '<td headers="ord-products" class="align-right">'.ria_number_format($r['products']).'</td>';
				if( $ordered ){
					print '<td headers="ord-pos" class="ria-cell-move"></td>';
				}
				print '</tr>';
			}
			$current++;
		}
	}
?>
</tbody>
</table>
<div class="notice">
	<p><?php print _('Les paniers modèles vous permettent de faire des suggestions de produits à commander ensemble (comme, par exemple, les éléments d’un produit composite ou kit à assembler soi-même). Vous pouvez restreindre la liste des clients qui ont accès à ces modèles via plusieurs critères (catégorie tarifaire, comptable, etc.).')?></p>

    <p><?php print _('Lorsqu’un client ajoute le modèle à son panier, tous les produits sont ajoutés au tarif négocié pour le client le cas échéant. Votre client peut ajouter plusieurs modèles à son panier, et peut également y inclure des produits supplémentaires non compris dans le modèle.')?></p>

    <p><?php print _('Pour faire fonctionner vos paniers modèles, une interface spécifique au niveau de votre site est requise (l’interface d’administration ne se suffit pas à elle-même).')?></p>
</div>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
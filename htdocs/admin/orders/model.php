<?php
	require_once('orders.inc.php');
	require_once('ord.models.inc.php');
	require_once('ord.returns.inc.php');
	require_once('delivery.inc.php');
	require_once('promotions.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['ord']) && $_GET['ord']=='new' ){
		gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL_ADD');
	}else{
		gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL_EDIT');
	}

	unset($error);

	$types = array( 'prc' => _('Catégorie tarifaire'),
					'prf' => _('Profil / droit d\'accès') ,
					'cac' => _('Catégorie comptable') ,
					'cnt' => _('Pays d\'origine du client') ,
					'usr' => _('Compte client')  );


	// Vérifie les paramètres d'entrée
	$_SESSION['admin_ord_id'] = isset( $_GET['ord'] ) ? $_GET['ord'] : ( isset( $_SESSION['admin_ord_id'] ) ? $_SESSION['admin_ord_id'] : false ) ;

	if( !$_SESSION['admin_ord_id'] || !ord_orders_exists($_SESSION['admin_ord_id']) ){

		$return = true;

		// si nouvelle commande
		if( isset( $_GET['ord'] ) && $_GET['ord'] == 'new' ){
			$_SESSION['admin_ord_id'] = false;
			$return = false;
		}

		if( $return ){
			header('Location: models.php');
			exit;
		}
	}

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order'], $_GET['ord']) ){
		ord_models_products_order_update( $_GET['ord'], $_POST['order'] );
		header('Location: model.php?ord='.$_GET['ord']);
		exit;
	}

	//création de la commande
	if( isset( $_POST['create'] ) ){
		$wst = isset($_POST['website']) && is_numeric($_POST['website']) && $_POST['website']>0 ? $_POST['website'] : false;
		if( $ord_id = ord_models_add( $_POST['ref'], $_POST['desc'], $_POST['date'] != '' ? $_POST['date'] : false, $wst, isset($_POST['allowed']) ? true : false ) ){
			header('Location: /admin/orders/model.php?ord='.$ord_id);
			exit;
		}
		else{
			$error = _('Une erreur est survenue lors de la création de votre modèle.');
		}
	}


	// mise à jour de la commande
	if( isset( $_POST['update'], $_GET['ord'] ) && is_numeric( $_GET['ord'] ) ){
		$error = false;

		if( !ord_orders_ref_update( $_GET['ord'], $_POST['ref'] ) )
			$error = true;
		if( !ord_orders_comments_update( $_GET['ord'], $_POST['desc'] ) )
			$error = true;
		if( isset($_POST['website']) ){
			if( is_numeric($_POST['website']) && $_POST['website']>0 ){
				if( !ord_orders_set_website( $_GET['ord'], $_POST['website'] ) )
					$error = true;
			}
		}

		if( !$error ){
			header('Location: /admin/orders/model.php?ord='.$_GET['ord'].'&upd=ok');
			exit;
		}
		else{
			$error = _('Une erreur est survenue lors de la mise à jour de votre modèle.');
		}
	}

	// suppression du modèle
	if( isset($_POST['del'], $_GET['ord']) && is_numeric($_GET['ord']) ){
		if( !ord_orders_state_update( $_GET['ord'], _STATE_CANCEL_MERCHAND, '', false ) )
			$error = _('Une erreur est survenue lors de la suppression de votre modèle.');
		else{
			ord_orders_unmask($_GET['ord'], true, true);

			header('Location: /admin/orders/models.php');
			exit;
		}
	}

	$order = false;
	$rorder = ord_orders_get( 0, $_SESSION['admin_ord_id'] );


	if( $rorder && ria_mysql_num_rows( $rorder ) ){
		$order = ria_mysql_fetch_array( $rorder );
	}

	// traite toutes les actions sur le panier variable $order obligatoire
	require_once('view.orders.inc.php');


	unset($no_error);

	if( $order ){
		// notification du panier modèle au utilisateur
		if( isset( $_POST['sendNotif'], $_POST['rights']) && is_array($_POST['rights']) ){
			$to_all =  isset($_POST['allow-to-all']);

			$users = ord_models_get_users_list($_SESSION['admin_ord_id'], $_POST['rights'] );

			if( is_array($users) && !empty($users) ){
				$response = ord_models_notify($order, $users);
				if(!$response){
					$error = _('Certains utilisateurs n\'ont pas été notifiés.');
				}elseif( is_array($response) ){
					$error = _('Certains utilisateurs n\'ont pas été notifiés.');
				}elseif( is_numeric($response) ){
					if ($response > 1){
						$no_error = $response._(' utilisateurs ont été notifiés');
					}else{
						$no_error = $response._(' utilisateur a été notifié');
					}
				}
			}
			if( isset($error) ){
				echo $error;
			}else{
				echo json_encode(array('success'=> $no_error));
			}
			if( IS_AJAX ){
				exit;
			}
		}
	}

	if( $order ){
		$ord = ria_mysql_fetch_array(ord_orders_get_with_adresses(0,$order['id']));
		$num = $ord['piece']!='' ? $ord['piece'] : str_pad( $ord['id'], 8, '0', STR_PAD_LEFT );
	}

	$rules = array();
	$rrights = ord_models_users_get( $order['id'] );
	$all = false;
	if( $rrights && ria_mysql_num_rows($rrights) > 0) {
		while( $rights = ria_mysql_fetch_array( $rrights ) ){
			if(  $rights['type'] == 'all' ){
				$all = $rights['is_allowed'] ? true : false;
				continue;
			}

			switch( $rights['type'] ) {
				case 'usr' :
					$r_usr = gu_users_get($rights['object']);
					if ($r_usr && ria_mysql_num_rows($r_usr)) {
						$usr = ria_mysql_fetch_assoc( $r_usr );

						$usr_name = ' '.htmlspecialchars($usr['ref']);
						if( trim($usr['adr_firstname'])!='' || trim($usr['adr_lastname'])!='' ){
							$usr_name .= ' - '.trim(htmlspecialchars($usr['adr_firstname'].' '.$usr['adr_lastname']));
						}
						

						if( trim($usr['society'])!='' ){
							$usr_name .= ' - '.htmlspecialchars($usr['society']);
						}
						if( trim($usr['email'])!='' ){
							$usr_name .= ' &lt;'.htmlspecialchars($usr['email']).'&gt;';
						}

						$rights['text'] = view_usr_is_sync( $usr );
						
						$rights['text'] .= '<a href="/admin/customers/edit.php?usr='.$usr['id'].'">'.$usr_name.'</a>';

					}
					break;
				case 'prf' :
					$rprf = gu_profiles_get( $rights['object'] );
					if( $rprf ){
						$prf = ria_mysql_fetch_array( $rprf );
						$rights['text'] = $prf['name'];
					}
					break;
				case 'cnt' :
					$cnt_name = sys_countries_get_name( $rights['object'] );
					if( $cnt_name ){
						$rights['text'] = $cnt_name;
					}
					break;
				case 'cac' :
					$rcac = gu_accounting_categories_get( $rights['object'] );
					if( $rcac ){
						$cac = ria_mysql_fetch_array( $rcac );
						$rights['text'] = $cac['name'];
					}
					break;
				case 'prc' :
					$rprc = prd_prices_categories_get( $rights['object'] );
					if( $rprc ){
						$prc = ria_mysql_fetch_array( $rprc );
						$rights['text'] = $prc['name'];
					}
					break;
				case 'fld' :
					$fields = fld_fields_get($rights['object']);
					$field = ria_mysql_fetch_assoc($fields);
					switch($field['type_id']) {
						case FLD_TYPE_BOOLEAN_YES_NO:{
							if($rights['fld_value'] == "1"){
								$rights['text'] = _('Oui');
							}else{
								$rights['text'] = _('Non');
							}
							break;
						}
						case FLD_TYPE_SELECT_HIERARCHY:
							$val = fld_restricted_values_get_name($rights['fld_value']);
							if (trim($val) == '') {
								$val = 'nc.';
							}
							$rights['text'] = htmlspecialchars($val);
							break;
						default: {
							$rights['text'] = htmlspecialchars($rights['fld_value']);
							break;
						}
							
					}
					break;
			}

			$rules[] = $rights;
		}
	}
	// vérification si la varialbe email existe
	$tnt_can_notify = false;
	$r_cfg_email = cfg_emails_get('notify-ord-models');
	if( $r_cfg_email && ria_mysql_num_rows($r_cfg_email) ){
		$tnt_can_notify = true;
	}
	if( !IS_AJAX ){
		define('ADMIN_PAGE_TITLE', sprintf(_('Modèle %s - Commandes'), ( $order ? $num : '' )));
		require_once('admin/skin/header.inc.php');
?>
<div id="ncmd_content">
<?php } 
if ( !isset($ord)) {
	print '<h2>' . _('Créer un nouveau modèle') . '</h2>';
} else {
	print '<h2>' . _('Modèle n°').str_pad( $ord['id'], 8, '0', STR_PAD_LEFT ) . '</h2>';
} ?>


<div class="notif">
<?php
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
	if( isset($no_error) ){
		print '<div class="error-success">'.htmlspecialchars($no_error).'</div>';
	}
	if( isset($_GET['upd']) ){
		print '<div class="error-success">'._('Enregistrement effectué').'</div>';
	}
?>
</div>
<form action="model.php?ord=<?php print $order ? $order['id'] : 'new'; ?>" method="post">
<table id="table-propriete-model">
	<thead>
		<tr>
			<th colspan="2"><?php print _('Propriétés générales')?></th>
		</tr>
	</thead>
	<tbody>
		<?php if( $order ) { ?>
			<tr>
				<td class="valign-center"><?php print _('Numéro :'); ?></td><td><?php print str_pad( $ord['id'], 8, '0', STR_PAD_LEFT ); ?></td>
			</tr>
		<?php } ?>
		<tr>
			<td id="td-propr-model" class="valign-center"><?php print _('Date :'); ?></td><td>
				<?php if( $order ){ ?>
					<?php print $ord['date']; ?>
				<?php }else{ ?>
					<input type="text" class="date datepicker" name="date" value="<?php print date('d/m/Y'); ?>"/>
				<?php } ?>
			</td>
		</tr>
		<tr>
			<td class="valign-center"><?php print _('Référence :'); ?></td><td><input type="text" name="ref" value="<?php print $order ? htmlspecialchars($ord['ref']) : false ?>"/></td>
		</tr>
		<tr>
			<td><?php print _('Description :'); ?></td><td><textarea class="ref-desc" name="desc" cols="40" rows="3"><?php print $order ? htmlspecialchars( $ord['comments'] ) : false; ?></textarea></td>
		</tr>
		<?php
				$wst = wst_websites_get();
				if(ria_mysql_num_rows($wst) > 1) {
				?>
		<tr>
			<td class="valign-center"><label><?php print _('Sites :'); ?></label></td>
			<td>
				<select name="website">
					<?php if( $order ){
							$rmyweb = wst_websites_get($ord['wst_id']);
							if( $rmyweb && ria_mysql_num_rows($rmyweb) ){
								$myweb = ria_mysql_fetch_array($rmyweb);
								?>
								<option value="<?php print $myweb['id']; ?>"><?php print htmlspecialchars($myweb['name']); ?></option>
							<?php
							}
						}else{
						?>
						<option value="-1" selected="selected"><?php print _('Tous')?></option>
						<?php
						while( $r = ria_mysql_fetch_array($wst) ){ ?>
							<option value="<?php print $r['id']; ?>"><?php print htmlspecialchars($r['name']); ?></option>
						<?php
						}
					}
					?>
				</select>
			</td>
		</tr>
		<?php } ?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="2">
				<?php if( !$order ){ ?>
					<input type="submit" name="create" value="<?php print _('Créer le modèle')?>" title="<?php print _('Enregistrer les modifications et revenir à la liste'); ?>" />
				<?php }else{ ?>
					<input type="submit" name="update" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications et revenir à la liste'); ?>" />
					<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_DEL') ){ ?>
					<input type="submit" onclick="return confirm('<?php print _('Voulez-vous vraiment supprimer ce modèle ?'); ?>');" name="delete" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer ce modèle et revenir à la liste'); ?>" />
					<?php } ?>
				<?php } ?>
			</td>
		</tr>
	</tfoot>
</table>


<?php if( $order ){ 
	print ncmd_show_cart( $ord['id'], false, true );
	?>
	<table id="autorization" class="ncmd_model_rights checklist">
		<caption><?php print _('Autorisations')?></caption>
		<tbody>
			<tr>
				<td colspan="4" class="allow-to-all" >
					<input type="checkbox" disabled="disabled" class="lock" name="allow-to-all" id="allow-to-all" value="1" <?php print $all ? 'checked="checked"':'';?> />
					<label for="allow-to-all"><?php print _('Tous les comptes clients, à l’exception des comptes ci-dessous :'); ?></label>
				</td>
			</tr>
			<tr>
				<th class="th-autor-mod-1" data-label="<?php print _('Tout cocher :'); ?> ">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this)">
				</th>
				<th class="th-autor-mod-2 thead-none"><?php print _('Type')?></th>
				<th class="thead-none"><?php print _('Valeur')?></th>
				<th class="th-autor-mod-2 thead-none"><?php print _('Autorisation')?></th>
			</tr>
			<?php if( !empty($rules) ){ ?>
				<?php foreach ($rules as $rule) { ?>
					<tr>
						<td><input type="checkbox" name="rights[]" class="checkbox" value="<?php echo $rule['id'] ?>"></td>
						<td data-label="<?php print _('Type :'); ?> "><?php 
							if($rule['type'] == "fld"){
								$rfld = fld_fields_get( $rule['object'] );
								if( $rfld && ria_mysql_num_rows($rfld)){
									$fld = ria_mysql_fetch_array( $rfld );
									echo htmlspecialchars($fld['name']);
								}else{
									echo htmlspecialchars($rule['object']);
								}
							}else{
								echo htmlspecialchars($types[ $rule['type'] ]);
							}
						?></td>
						<td data-label="<?php print _('Valeur :'); ?> "><?php echo htmlspecialchars($rule['text']); ?></td>
						<td data-label="<?php print _('Autorisation :'); ?> "><?php echo ($rule['is_allowed'] ? _('Oui') : _('Non'))?></td>
					</tr>
				<?php } ?>
			<?php }else{ ?>
				<tr><td colspan="4"><?php print _('Aucune autorisation')?></td></tr>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<?php if( !$all && $tnt_can_notify ){ ?>
					<input type="submit" name="sendNotif" value="<?php print _('Envoyer notification')?>" class="btn-del" id="send-notif">
					<?php } ?>
				</td>
				<td colspan="2" class="align-right">
					<input type="submit" name="update-rights" value="<?php print _('Modifier')?>" />
				</td>
			</tr>
		</tfoot>
	</table>

<?php } ?>
</form>

<?php
	if( !IS_AJAX ){
		print '
			</div>
			<script><!--
				var ncmd_url_sucharge = \'/admin/orders/model.php\';
			--></script>
		';

		require_once('admin/skin/footer.inc.php');
	}
 ?>
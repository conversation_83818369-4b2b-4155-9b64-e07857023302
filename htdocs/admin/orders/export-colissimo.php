<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$state = isset($_GET['state']) && is_numeric($_GET['state']) && $_GET['state'] ? $_GET['state'] : 0;
	
	if( !isset($orders) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	if( !isset($config['export_shipment_active']) || !$config['export_shipment_active'] ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	// Récupère les informations sur l'utilisation de Colissimo
	$r_type_relay = dlv_relay_types_used_get( SO_COLISSIMO_TYPE );
	if( !$r_type_relay || !ria_mysql_num_rows($r_type_relay) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	$type_relay = ria_mysql_fetch_assoc( $r_type_relay );
	
	/*
		0  = Type d'enregistrement 3 1
		1  = Référence du colis pour l’expéditeur 35 4
		2  = Date d’expédition 8 39
		3  = Code produit 4 47
		4  = Code expéditeur 6 51
		5  = Code destinataire 17 57
		6  = Nom du destinataire 35 74
		7  = Adresse 1 du destinataire 35 109
		8  = Adresse 2 du destinataire 35 144
		9  = Adresse 3 du destinataire 35 179
		10 = Code postal du destinataire 9 214
		11 = Commune du destinataire 35 223
		12 = Code pays du destinataire 3 258
		13 = Instruction de livraison 70 261
		14 = Filler 12 331
		15 = Poids 15 343
		16 = Montant CRBT 15 358
		17 = Filler 3 373
		18 = Montant ADV (assurance) 15 376
		19 = Filler 3 391
		20 = Non mécanisable 1 394
		21 = Filler 17 395
		22 = N° téléphone (filaire) 25 412
		23 = Filler 4 437
		24 = Recommandation 1 441
		25 = Avis de réception 1 442
		26 = FTD 1 443
		27 = Instruction de non livraison 1 444
		28 = Adresse E-mail du destinataire 75 445
		29 = Nature envoi 1 520
		30 = Numéro de licence 25 521
		31 = Numéro de certificat 25 546
		32 = Numéro de facture 25 571
		33 = Référence en douane 4 596
		34 = Adresse 4 du destinataire 35 600
		35 = Numéro d’affaire 35 635
		36 = Numéro de commande 35 670
		37 = Filler 73 705
		38 = Civilité du destinataire 1 778 
		39 = Prénom du destinataire 29 779
		40 = Raison sociale du destinataire
		41 = Portable du destinataire 10 846
		42 = Code porte 1 de l’adresse du destinataire 8 856
		43 = Code porte 2 de l’adresse du destinataire 8 864
		44 = Interphone de l’adresse du destinataire 30 872
		45 = Code Point Retrait 6 902
		46 = Filler 1 908
		47 = Nom commercial de l’expéditeur 38 909
		48 = Filler 30 947
		49 = Code avoir/promotion 15 977
		50 = Filler 75 992
		51 = Code réseau 3 1067
		52 = Portable international 20 1070
		53 = Nom destinataire retour 35 1090
		54 = Adresse 1 retour 35 1125
		55 = Adresse 2 retour 35 1160
		56 = Adresse 3 retour 35 1195
		57 = Adresse 4 retour 35 1230
		58 = Code postal retour 5 1265
		59 = Commune retour 35 1270
		60 = Longueur du colis en mm 4 1305
		61 = Largeur du colis en mm 4 1309
		62 = Hauteur du colis en mm 4 1313
		63 = Langue de notification 2 1317
		64 = Identifiant point IPC 35 1350
		65 = Nom du point de retrait Inter 35 1385
		66 = Adresse ligne 1 point de retrait Inter 35 1420 
		67 = Adresse ligne 2 point de retrait Inter 35 1455
		68 = Adresse ligne 3 point de retrait Inter 35 1490
		69 = Adresse ligne 4 point de retrait Inter 35 1525
		70 = Code postal point de retrait Inter 9 1534
		71 = Ville point de retrait Inter 35 1569
		72 = Code pays point de retrait Inter 2 1571
		73 = Numéro d’abonné point de retrait Inter 35 1606
	*/
	require_once('strings.inc.php');
	require_once('orders.inc.php');

	$ar_ord_ids = array();
	$ar_lines 	= array();

	while( $order = ria_mysql_fetch_assoc($orders) ){
		// Exclusion des commandes livrées en magasin
		if( is_numeric($order['str_id']) && $order['str_id'] > 0 ){
			$ar_ord_ids[] = $order['id'];
			continue;
		}

		// Exclusion des commandes livrées par un autre service que Colissimo
		if( !in_array($order['srv_id'], $config['export_shipment_colissimo']) ){
			continue;
		}

		$r_ord_adr = ord_orders_get_with_adresses(0, $order['id']);
		if (!$r_ord_adr || !ria_mysql_num_rows($r_ord_adr)) {
			continue;
		}

		$order_format = ord_orders_shipment_formatted( ria_mysql_fetch_assoc($r_ord_adr), 35 );
		if( !ria_array_key_exists(array('firstname', 'addr1', 'addr2', 'addr3', 'zipcode', 'city', 'ref_order', 'user_id', 'addr3', 'country', 'dlv-notes', 'weight_order', 'assurance', 'phone', 'email', 'society', 'mobile', 'rly_ref', 'rly_country'), $order_format) ){
			continue;
		}

		$ar_product = array();

		$r_prod = ord_products_get( $order['id'] );
		if( $r_prod ){
			while( $prod = ria_mysql_fetch_assoc($r_prod) ){
				if( prd_products_is_port($prod['ref']) ){
					continue;
				}

				$ar_product[] = $prod;
			}
		}
		
		if( trim($order_format['rly_ref']) != '' ){
			$type_delivery = dlv_relays_get_type( $order_format['rly_id'] );
			
			if( trim($type_delivery) == '' ){
				$type_delivery = 'BPR';
			}
		}else{
			switch( $order_format['country'] ){
				case 'FR' : $type_delivery = 'DOM'; break;
				case 'WF' : $type_delivery = 'CDS'; break;
				case 'TF' : $type_delivery = 'CDS'; break;
				case 'PM' : $type_delivery = 'CDS'; break;
				case 'RE' : $type_delivery = 'CDS'; break;
				case 'PF' : $type_delivery = 'CDS'; break;
				case 'NC' : $type_delivery = 'CDS'; break;
				case 'YT' : $type_delivery = 'CDS'; break;
				case 'MQ' : $type_delivery = 'CDS'; break;
				case 'GF' : $type_delivery = 'CDS'; break;
				case 'GP' : $type_delivery = 'CDS'; break;
				case 'LU' : $type_delivery = 'DOM'; break;
				case 'DE' : $type_delivery = 'DOM'; break;
				case 'BE' : $type_delivery = 'DOM'; break;
				case 'NL' : $type_delivery = 'DOM'; break;
				default   : $type_delivery = 'COLI'; break;
			}		
		}

	    switch( $order_format['title_id'] ){
			case 1 : $civility = 2; break;
			case 2 : $civility = 3; break;
			case 3 : $civility = 4; break;
			default : $civility = 0; break;
		}

		$order_format['lastname'] 	= strtoupper2( $order_format['lastname'] );
		$order_format['firstname'] 	= strtoupper2( $order_format['firstname'] );
		$order_format['addr1'] 		= strtoupper2( $order_format['addr1'] );
		$order_format['addr2'] 		= strtoupper2( $order_format['addr2'] );
		$order_format['zipcode'] 	= strtoupper2( $order_format['zipcode'] );
		$order_format['city'] 		= strtoupper2( $order_format['city'] );

		$tmp_line = array();

		// Type d'enregistrement
		$tmp_line[1] = strcut_colissimo( 'EXP', 3, ' ', STR_PAD_RIGHT );
		// Référence du colis pour l’expéditeur
		$tmp_line[4] = strcut_colissimo( $order_format['ref_order'], 35, ' ', STR_PAD_RIGHT );
		// Date d’expédition
		$tmp_line[39] = strcut_colissimo( '', 8, ' ', STR_PAD_RIGHT );
		
		// Code produit
		$tmp_line[47] = strcut_colissimo( $type_delivery, 4, ' ', STR_PAD_RIGHT );
		// Code expéditeur
		$tmp_line[51] = strcut_colissimo( $type_relay['login'], 6, ' ', STR_PAD_RIGHT );
		// Code destinataire
		$tmp_line[57] = strcut_colissimo( $order_format['user_id'], 17, ' ', STR_PAD_RIGHT );

		// Nom du destinataire
		$tmp_line[74] = strcut_colissimo( $order_format['lastname'], 35, ' ', STR_PAD_RIGHT );
		// Adresse 1 du destinataire : Numéro et libellé de voie 35
		$tmp_line[109] = strcut_colissimo( $order_format['addr1'], 35, ' ', STR_PAD_RIGHT );
		// Adresse 2 du destinataire : Etage, couloir, escalier, appartement
		$tmp_line[144] = strcut_colissimo( $order_format['addr2'], 35, ' ', STR_PAD_RIGHT );
		// Adresse 3 du destinataire : Entrée, bâtiment, immeuble,résidence 35
		$tmp_line[179] = strcut_colissimo( $order_format['addr3'], 35, ' ', STR_PAD_RIGHT );
		// Code postal du destinataire 9
		$tmp_line[215] = strcut_colissimo( $order_format['zipcode'], 9, ' ', STR_PAD_RIGHT );
		// Commune du destinataire 35
		$tmp_line[223] = strcut_colissimo( $order_format['city'], 35, ' ', STR_PAD_RIGHT );
		// Code pays du destinataire 3
		$tmp_line[258] = strcut_colissimo( $order_format['country'], 3, ' ', STR_PAD_RIGHT );
		
		// Instruction de livraison 70
		$tmp_line[261] = strcut_colissimo( $order_format['dlv-notes'], 70, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 12
		$tmp_line[331] = strcut_colissimo( '', 12, ' ', STR_PAD_RIGHT );
		// Poids 15
		$tmp_line[343] = strcut_colissimo( $order_format['weight_order'], 15, ' ', STR_PAD_RIGHT );
		// Montant CRBT 15
		$tmp_line[358] = strcut_colissimo( '0.00', 15, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 3
		$tmp_line[373] = strcut_colissimo( '', 3, ' ', STR_PAD_RIGHT );
		// Montant ADV (assurance) 15
		$tmp_line[376] = strcut_colissimo( $order_format['assurance'], 15, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 3
		$tmp_line[391] = strcut_colissimo( '', 3, ' ', STR_PAD_RIGHT );
		// Non mécanisable 1
		$tmp_line[394] = strcut_colissimo( 'N', 1, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 17
		$tmp_line[395] = strcut_colissimo( '', 17, ' ', STR_PAD_RIGHT );
		// N° téléphone (filaire) 25
		$tmp_line[412] = strcut_colissimo( $order_format['phone'], 25, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 4
		$tmp_line[437] = strcut_colissimo( '', 4, ' ', STR_PAD_RIGHT );
		// Recommandation 1
		$tmp_line[441] = strcut_colissimo( '0', 1, ' ', STR_PAD_RIGHT );
		// Avis de réception 1
		$tmp_line[442] = strcut_colissimo( 'O', 1, ' ', STR_PAD_RIGHT );
		// FTD (Franc de taxes et de droits) 1
		$tmp_line[443] = strcut_colissimo( 'N', 1, ' ', STR_PAD_RIGHT );
		// Instruction de non livraison (Type de retour) 1
		$tmp_line[444] = strcut_colissimo( '2', 1, ' ', STR_PAD_RIGHT );
		// Adresse E-mail du destinataire 75
		$tmp_line[445] = strcut_colissimo( $order_format['email'], 75, ' ', STR_PAD_RIGHT );
		// Nature envoi 1
		$tmp_line[520] = strcut_colissimo( '4', 1, ' ', STR_PAD_RIGHT );
		// Numéro de licence 25
		$tmp_line[521] = strcut_colissimo( '', 25, ' ', STR_PAD_RIGHT );
		// Numéro de certificat 25
		$tmp_line[546] = strcut_colissimo( '', 25, ' ', STR_PAD_RIGHT );
		// Numéro de facture 25
		$tmp_line[571] = strcut_colissimo( '', 25, ' ', STR_PAD_RIGHT );
		// Référence en douane (Identifiant de l’expéditeur auprès des douanes) 4
		$tmp_line[596] = strcut_colissimo( '', 4, ' ', STR_PAD_RIGHT );
		// Adresse 4 du destinataire : Lieu dit ou autre mention 35
		$tmp_line[600] = strcut_colissimo( '', 35, ' ', STR_PAD_RIGHT );
		// Numéro d’affaire 35
		$tmp_line[635] = strcut_colissimo( '', 35, ' ', STR_PAD_RIGHT );
		// Numéro de commande 35
		$tmp_line[670] = strcut_colissimo( $order_format['ref_order'], 35, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 35
		$tmp_line[705] = strcut_colissimo( '', 35, ' ', STR_PAD_RIGHT );
		// BIC 11
		$tmp_line[740] = strcut_colissimo( '', 11, ' ', STR_PAD_RIGHT );
		// IBAN 27
		$tmp_line[751] = strcut_colissimo( '', 27, ' ', STR_PAD_RIGHT );

		// Civilité du destinataire 1
	    $tmp_line[778] = strcut_colissimo( $civility, 1, ' ', STR_PAD_RIGHT );
		// Prénom du destinataire 29
	    $tmp_line[779] = strcut_colissimo( $order_format['firstname'], 29, ' ', STR_PAD_RIGHT );
		// Raison sociale du destinataire 38
	    $tmp_line[808] = strcut_colissimo( $order_format['society'], 38, ' ', STR_PAD_RIGHT );
		// Portable du destinataire 10
	    $tmp_line[846] = strcut_colissimo( (trim($order_format['mobile']) != '' ? $order_format['mobile'] : ''), 10, ' ', STR_PAD_RIGHT );
		// Code porte 1 de l’adresse du destinataire 8
	    $tmp_line[856] = strcut_colissimo( '', 8, ' ', STR_PAD_RIGHT );
		// Code porte 2 de l’adresse du destinataire 8
	    $tmp_line[864] = strcut_colissimo( '', 8, ' ', STR_PAD_RIGHT );
		// Interphone de l’adresse du destinataire 30
	    $tmp_line[872] = strcut_colissimo( '', 30, ' ', STR_PAD_RIGHT );
		// Code point de retrait 6
	    $tmp_line[902] = strcut_colissimo( $order_format['rly_ref'], 6, ' ', STR_PAD_RIGHT );
		// Filler (zone nonutilisée)1
	    $tmp_line[908] = strcut_colissimo( '', 1, ' ', STR_PAD_RIGHT );
		// Nom commercial de l’expéditeur 38
	    $tmp_line[909] = strcut_colissimo( $config['export_shipment_owner'], 38, ' ', STR_PAD_RIGHT );
		// Filler (zone non utilisée) 30
	    $tmp_line[947] = strcut_colissimo( '', 30, ' ', STR_PAD_RIGHT );
		// Code avoir/promotion 15
	    $tmp_line[977] = strcut_colissimo( '', 90, ' ', STR_PAD_RIGHT );
		
		$ar_reseau = array(
			'DE-CMT' => 'R03',
			'ES-CMT' => 'R03',
			'GB-CMT' => 'R03',
			'LU-CMT' => 'R03',
			'NL-CMT' => 'R03',
			'BE-BDP' => 'R12',
			'BE-CMT' => 'R12',
			'DE-BDP' => 'X00',
			'DE-PCS' => 'X00',
			'ES-BDP' => 'X00',
			'NL-BDP' => 'X00'
		);

		$code_reseau = array_key_exists( $order_format['rly_country'].'-'.$type_delivery, $ar_reseau ) ? $ar_reseau[ $order_format['rly_country'].'-'.$type_delivery ] : '';
		
		// Code réseau 3 1067
	    $tmp_line[1067] = strcut_colissimo( $code_reseau, 283, ' ', STR_PAD_RIGHT );
		
		$tmp_line[1350] = strcut_colissimo( $order_format['rly_ref'], 256, ' ', STR_PAD_RIGHT );

		$ar_lines[] = $tmp_line;
		$ar_ord_ids[] = $order['id'];

		if( sizeof($ar_ord_ids) == 50 ){
			break;
		}
	}

	if( sizeof($ar_lines) ){
		header("content-type: application/octet-stream");
		header("Content-Disposition: attachment; filename=export-colissimo-".date('dmY').".txt");
		header("Pragma: no-cache");
		header("Expires: 0");

		foreach( $ar_lines as $one_line ){
			print implode( '', $one_line )."\r\n";
		}

		exit;
	}else{
		$_SESSION['export_shipment_no_order'] = "Aucune commande à exporter pour Colissimo";
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

<?php

/** \file export-invoice.php
 * 
 *  Ce fichier est chargé d'exporter une facture au format PDF. Les paramètres obligatoires sont les suivants :
 *  - ord : identifiant de la commande d'origine
 *  - inv : identifiant de la facture
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

require_once('Pdf/pdf.inc.php');

$is_admin = substr($_SERVER['REQUEST_URI'], 0, 7) == '/admin/';

if( $is_admin ){
    gu_if_authorized_else_403('_RGH_ADMIN_ORDER_DL_INVOICE');
}

// Vérifie les paramètres ord et inv
$ord_id = isset($_GET['ord']) ? intval($_GET['ord']) : null;
$inv_id = isset($_GET['inv']) ? intval($_GET['inv']) : null;
if (!$ord_id || !$inv_id) {
    header('Location: /admin/orders/orders.php');
    exit;
}

try {
    generate_invoice($ord_id, $inv_id);
} catch (Exception $e) {
    header('Location: /admin/orders/order.php?ord=' . $ord_id);
    exit;
}
<?php

/**	\file delivery.php
 *	Ce fichier est utilisé en include par la page htdocs/admin/orders/order.php, et en Ajax
 *	par le fichier /admin/orders/order/delivery.php
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_ORDER');


$error = false;
$srv_id = isset($_POST['srv_id']) ? $_POST['srv_id'] : (isset($_GET['srv_id']) ? $_GET['srv_id'] : null);
$ord_id = isset($_POST['ord_id']) ? $_POST['ord_id'] : (isset($_GET['ord_id']) ? $_GET['ord_id'] : null);
if (!empty($_POST) && !is_null($srv_id) && !is_null($ord_id) ){
    if (isset($_POST['bl_id'])) {
        if (!$res = ord_bl_set_srv_id($_POST['bl_id'], $_POST['srv_id'])) {
            $error = true;
        }
    } else {
        if (!$res = ord_orders_set_dlv_service($ord_id, $srv_id)){
            $error = true;
        }
    }
}

if ((!isset($ord) || !$ord) && !is_null($ord_id)) {
    $ord = ria_mysql_fetch_array(ord_orders_get_masked($ord_id, true));
}

if (!isset($ord) || !$ord) {
    header('Location: /admin/orders/orders.php');
    exit;
}

if (ord_products_get_all_qte($ord['id'])){
    if ($error){
        print 'error';
        exit;
    }

    $dlv_service = '';

    // Calcule le service de livraison utilisé
    if( $ord['srv_id'] && ($rsrv=dlv_services_get($ord['srv_id'])) && ($srv=ria_mysql_fetch_array($rsrv)) || trim($ord['piece']) == "" ){
        $dlv_service .= '<tr id="delivery-row">';
            $dlv_service .= '<td><label>'._('Livraison assurée par :').'</label></td>';
            if( $ord['srv_id'] && isset($srv) && $srv ){
                $dlv_service .= '<td><a href="/admin/config/livraison/services/edit.php?srv='.$ord['srv_id'].'" target="_blank">'
                            .htmlspecialchars($srv['name'])
                        .'</a>'
                        .(trim($ord['piece'])=="" ? ' <a id="dlv-service-link" class="print-none">'._('Modifier').'</a>' : '' )
                    .'</td>
                    <input type="hidden" id="ord-srv" value="'.$ord['srv_id'].'"/>';
            }else{
                $dlv_service .= '   <td><a id="dlv-service-link" class="print-none">'._('Sélectionner un service').'</a></td>
                                    <input type="hidden" id="ord-srv" value="NULL"/>';
            }
            $dlv_service .= isset($res) && $res ? '<input type="hidden" id="result-text" value="'._("L'ajout du service de livraison s'est correctement déroulé.").'" />' : '';
        $dlv_service .= '</tr>';
    }

    // Affichage du commentaire de livraison
    if( trim($ord['dlv-notes']) != '' ){
        $dlv_service .= '
            <tr>
                <td>'._('Commentaire :').'</td>
                <td>'.nl2br( htmlspecialchars( $ord['dlv-notes'] ) ).'</td>
            </tr>
        ';
    }

    // Calcule la liste des colis utilisés pour l'expédition
    $colisTableHtml = '';

    $doc_type_id = 0;
    $r_doc_type = doc_types_get(0, false, true, false, false, false, null, false, 'Etiquette Colissimo');
    if ($r_doc_type && ria_mysql_num_rows($r_doc_type)) {
        $doc_type_id = ria_mysql_result($r_doc_type, 0, 'id');
    }

    $can_see_labels = gu_user_is_authorized('_RGH_ADMIN_BL_GENERATE_LABEL');
    $can_edit_labels = gu_user_is_authorized('_RGH_ADMIN_BL_GENERATE_LABEL_EDIT');

    if( $bl = ord_orders_bl_get($ord['id']) ){
        while( $b = ria_mysql_fetch_array($bl) ){
            $colis = ord_bl_colis_get($b['id']);
            if( $b['srv_id'] ){
                $srv = ria_mysql_fetch_array(dlv_services_get($b['srv_id']));
                foreach( $colis as $c ){
                    $colisHtml = '<tr><td>';

                    $ref = $b['piece'];
                    if (!trim($ref)) {
                        $ref = $b['ref'];
                    }

                    if (!trim($ref)) {
                        $ref = $b['id'];
                    }

                    $colisHtml .= htmlspecialchars($ref);

                    $colisHtml .= '</td><td>';

                    $colisHtml .= '<a href="/admin/config/livraison/services/edit.php?srv=' . $b['srv_id'] . '" target="_blank">'
                            . htmlspecialchars($srv['name'])
                        . '</a>'
                        . (trim($b['piece']) == ''
                            ? ' <a class="bl-dlv-service-link print-none" data-bl="' . htmlspecialchars($b['id']) . '">'
                                . _('Modifier')
                                . '</a>'
                            : ''
                        );

                    $colisHtml .= '</td><td>';

                    if ($srv['url-colis']) {
                        $colisHtml .= '<a href="'.$srv['url-colis'].$c.'" target="_blank">'.htmlspecialchars( $c ).'</a>';
                    } else {
                        $colisHtml .= htmlspecialchars($c);
                    }

                    $colisHtml .= '</td>';

                    if ($can_see_labels) {
                        $colisHtml .= '<td>';
                        $has_label = false;
                        $r_bl_prd = ord_bl_products_get($b['id'], 0, $c);
                        $no_doc = true;
                        if ($r_bl_prd && ria_mysql_num_rows($r_bl_prd)) {
                            while ($bl_prd = ria_mysql_fetch_assoc($r_bl_prd)) {
                                $r_docs = doc_objects_get(0, CLS_BL_PRODUCT, $bl_prd['id'], $doc_type_id);
                                if ($r_docs && ria_mysql_num_rows($r_docs)) {
                                    $has_label = true;
                                    $doc = ria_mysql_fetch_assoc($r_docs);
                                    if ($can_see_labels) {
                                        $colisHtml .= '<a href="/admin/documents/dl.php?doc=' . urlencode($doc['doc_id']) . '">'
                                            . _('Télécharger l\'étiquette')
                                            . '</a>';
                                    }
                                    $no_doc = false;
                                    break;
                                }
                            }
                        }
                        if ($no_doc) {
                            $colisHtml .= '&nbsp;';
                        }

                        $colisHtml .= '</td>';
                    }

                    if ($can_edit_labels) {
                        $colisHtml .= '<td>';

                        $srv_type = false;
                        if (intval($srv['type_id']) > 0) {
                            $r_srv_type = dlv_services_types_get($srv['type_id']);
                            if ($r_srv_type && ria_mysql_num_rows($r_srv_type)) {
                                $srv_type = ria_mysql_fetch_assoc($r_srv_type);
                            }
                        }

                        if ($srv_type
                            && isset($srv_type['group'])
                            && isset($srv_type['ref'])
                            && strtoupper($srv_type['group']) == 'COLISSIMO'
                            && trim($srv_type['ref'])
                        ) {
                            $colisHtml .= '<input type="button" class="colissimo-generate-label"'
                                . ' data-ord-id="' . htmlspecialchars($ord['id']). '"'
                                . ' data-bl-id="' . htmlspecialchars($b['id']). '"'
                                . ' data-colis="' . htmlspecialchars($c). '"'
                                . ' value="' . _($has_label ? 'Nouvelle étiquette' : 'Générer l\'étiquette') . '"'
                                . '/>';
                            $colisHtml .= '<br />';
                            $colisHtml .= '<label class="colissimo-generate-label-label" for="colissimo-generate-label-'. htmlspecialchars($ord['id']) . '-' . htmlspecialchars($b['id']) . '-' . htmlspecialchars($c) .'-insurance">' . _('Assurance');
                            $colisHtml .= '<input type="text" id="colissimo-generate-label-'. htmlspecialchars($ord['id']) . '-' . htmlspecialchars($b['id']) . '-' . htmlspecialchars($c) . '-insurance" class="colissimo-generate-label-insurance" /> €';
                            $colisHtml .= '</label>';
                        } else {
                            $colisHtml .= '&nbsp;';
                        }

                        $colisHtml .= '</td>';
                    }

                    $colisHtml .= '</tr>';

                    $colisTableHtml .= $colisHtml;
                }
            }
        }

        if ('' != $colisTableHtml) {
            $blLabel = _('Bon de livraison');
            $colisServiceLabel = _('Service de livraison');
            $colisLabel = _('Colis');
            $labelsHtml = '';
            if ($can_see_labels) {
                $colisLabelLabel = _('Etiquette');
                $labelsHtml = <<<TPL_COLIS_TABLE_LABELS
                    <th colspan="2">${colisLabelLabel}</th>
TPL_COLIS_TABLE_LABELS
                ;
            }

            $colisTableHtml = <<<TPL_COLIS_TABLE
            <table id="tb-colis">
                <thead>
                    <th>${blLabel}</th>
                    <th>${colisServiceLabel}</th>
                    <th>${colisLabel}</th>
                    ${labelsHtml}
                </thead>
                <tbody>
                    ${colisTableHtml}
                </tbody>
            </table>
TPL_COLIS_TABLE
            ;
        }
    }

    // Affiche le bloc livraison s'il contient des informations
    if( isset($dlv_service) || '' != $colisTableHtml ){

        print '<tr id="delivery-header"><th colspan="2">'._('Livraison').'</th></tr>';

        // Affiche le service de livraison utilisé
        if (isset($dlv_service) && $dlv_service) {
            print $dlv_service;
        }

        // Affichage des numéros de colis, seulement s'il y en a
        if( '' != $colisTableHtml ){
            print '<tr class="delivery-block"><td>'._('Colis').' :</td><td>'.$colisTableHtml.'</td></tr>';
        }
        if (isset($res) && $res ) {
            print '<input class="delivery-block" type="hidden" id="result-text" value="'._("L'ajout du service de livraison s'est correctement déroulé.").'" />';
        }
    }
}


<?php

	/**	\file update-user.php
	 *	Ce fichier est utilisé en include par la page htdocs/admin/orders/order.php, et en Ajax
	 *	par le fichier htdocs/admin/js/orders/order.js
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $error = false;

    // Charge la commande (on peut se permettre de charger les commandes masquées à ce stade)
    $ord = ria_mysql_fetch_array(ord_orders_get_masked( $_GET['ord'], true ));

	// Si un compte utilisateur est demandé, charge le compte
    if( isset($_GET['usr']) && $ord['user'] != $_GET['usr'] ){
        $rusr = gu_users_get($_GET['usr']);
        $ref = gu_users_get_ref($_GET['usr']);
        if( !$ref ){
            $ref = str_pad( $_GET['usr'], 8, '0', STR_PAD_LEFT );
        }
	    if( $rusr && ria_mysql_num_rows($rusr) ){
            $usr = ria_mysql_fetch_array($rusr);

            if( !ord_orders_update_user($_GET['ord'], $_GET['usr']) ){
               $error = true;
            }  
            $ord = ria_mysql_fetch_array(ord_orders_get_masked( $_GET['ord'], true ));
        }
    }else{ // Prépare un compte utilisateur vide
        $usr = array();
        $rusr = gu_users_get( $ord['user'] );
        if( $rusr && ria_mysql_num_rows($rusr) ){
            $usr = ria_mysql_fetch_array($rusr);
        }
        $ref = gu_users_get_ref($ord['user']);
    }

    // Charge l'adresse de livraison
    $dlv_address = ord_delivery_address( $ord );
	// Si pas d'erreur
    if( !$error ){
?>
    <div id="ord-addresses">

        <?php if( !$ord['user'] ){ ?>
            <div class="th-info-client"><?php print _('Informations client'); ?></div>
        <?php } ?>

        <div id="ord-addresses-compte-client">
            <span class="title-detail"><?php print _('Compte client :'); ?></span>
            <span id="compte-client-name">
                <?php
                if( $ord['user'] ){
                    $ref = gu_users_get_ref($ord['user']);
                    if( $ref===false ){
                        $ref = str_pad( $ord['user'], 8, '0', STR_PAD_LEFT );
                        print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars( $ref ).'</span>';
                    }else{
                        if( !$ref ){
                            $ref = str_pad( $ord['user'], 8, '0', STR_PAD_LEFT );
                        }
                        print '<a href="/admin/customers/edit.php?usr='.$ord['user'].'">';
                        print view_usr_is_sync( $usr ).'&nbsp;'.htmlspecialchars( $ref );
                        print '</a>';	
                        //print view_usr_contact( 'Contacter ce client', $usr['email'] );
                    }
                }
                if( !$ord['piece'] ){
                    print ' <a href="#" id="upd-ord-usr" class="print-none">'.( $ord['user'] ? _('Modifier') : _('Sélectionner un compte client')).'</a>';
                }
                ?>
            </span>
        </div>
    <?php if ($ord['user']) {
            include('addresses.php');
        } 
    }else{
        print _('Une erreur inattendue est survenue');
    }
?>
    </div>
   

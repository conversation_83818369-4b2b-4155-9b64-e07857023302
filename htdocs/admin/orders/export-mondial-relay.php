<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$state = isset($_GET['state']) && is_numeric($_GET['state']) && $_GET['state'] ? $_GET['state'] : 0;

	if( !isset($orders) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	if( !isset($config['export_shipment_active']) || !$config['export_shipment_active'] ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}
	
	/*
		1  = N° de Client
		2  = Référence de l'expédition
		3  = Adresse de livraison (Nom du client final)
		4  = Adresse du destinataire (Complément du nom)
		5  = Adresse du destinataire (Numéro + Rue)
		6  = Adresse du destinataire (Complément d'adresse)
		7  = Ville du destinataire
		8  = Code Postal du destinataire O Format dépendant du pays, pour la France
		9  = Pays du destinataire
		10 = Téléphone 1
		11 = Téléphone 2
		12 = Adresse email
		13 = Type Collecte
		14 = ID Relais Collecte
		15 = Code Pays Collecte
		16 = Type Livraison
		17 = ID Relais de Livraison
		18 = Code Pays du Relais de Livraison
		19 = Mode de Livraison O Liste de valeurs prédéfinies
		20 = Code Langue du Destinataire
		21 = Nombre de colis
		22 = Poids
		23 = Longueur
		24 = Volume
		25 = Valeur de l'expédition
		26 = Devise de la valeur de l'expédition
		27 = Assurances
		28 = Montant CRT (Contre remboursement)
		29 = Devise CRT
		30 = Instructions de Livraison
		31 = Top Avisage
		32 = Top Reprise à Domicile
		33 = Temps de Montage
		34 = Top Rendez-Vous
		35 = Article 01
		36 = Article 02
		37 = Article 03
		38 = Article 04
		39 = Article 05
		40 = Article 06
		41 = Article 07
		42 = Article 08
		43 = Article 09
		44 = Article 10
	*/
	
	require_once('strings.inc.php');
	require_once('orders.inc.php');

	$ar_ord_ids = array();
	$ar_lines 	= array();

	while( $order = ria_mysql_fetch_assoc($orders) ){
		// Exclusion des commandes livrées en magasin
		if( is_numeric($order['str_id']) && $order['str_id'] > 0 ){
			$ar_ord_ids[] = $order['id'];
			continue;
		}

		// Exclusion des commandes livrées par un autre service que Chronopost
		if( !in_array($order['srv_id'], $config['export_shipment_mondial_relay']) ){
			continue;
		}

		$r_ord_adr = ord_orders_get_with_adresses(0, $order['id']);
		if (!$r_ord_adr || !ria_mysql_num_rows($r_ord_adr)) {
			continue;
		}

		$order_format = ord_orders_shipment_formatted( ria_mysql_fetch_assoc($r_ord_adr), 32 );
		if( !ria_array_key_exists(array('firstname', 'addr1', 'addr2', 'addr3', 'zipcode', 'city', 'ref_order', 'user_id', 'addr3', 'country', 'dlv-notes', 'weight_order', 'assurance', 'phone', 'email', 'society', 'mobile', 'rly_ref', 'rly_country'), $order_format) ){
			continue;
		}

		$ar_product = array();

		$r_prod = ord_products_get( $order['id'] );
		if( $r_prod ){
			while( $prod = ria_mysql_fetch_assoc($r_prod) ){
				if( prd_products_is_port($prod['ref']) ){
					continue;
				}

				$ar_product[] = $prod;
			}
		}

		$tmp_line = array();
		
		$tmp_line[0]  = strcut( $order_format['user_id'], 9, '' );
		$tmp_line[1]  = strcut( $order_format['ref_order'], 16, '' );
		$tmp_line[2]  = strcut( trim($order_format['lastname'].' '.$order_format['firstname']), 32, '' );
		$tmp_line[3]  = '';
		$tmp_line[4]  = $order_format['addr1'];
		$tmp_line[5]  = $order_format['addr2'];
		$tmp_line[6]  = strcut( $order_format['city'], 25, '' );
		$tmp_line[7]  = strcut( $order_format['zipcode'], 10, '' );
		$tmp_line[8]  = strcut( $order_format['country'], 2, '' );
		$tmp_line[9]  = strcut( $order_format['phone'], 13, '' );
		$tmp_line[10] = strcut( $order_format['mobile'], 13, '' );
		$tmp_line[11] = strcut( $order_format['email'], 70, '' );
		$tmp_line[12] = 'A';
		$tmp_line[13] = '';
		$tmp_line[14] = '';
		$tmp_line[15] = 'R';
		$tmp_line[16] = strcut( $order_format['rly_ref'], 6, '' );
		$tmp_line[17] = strcut( $order_format['rly_country'], 2, '' );
		$tmp_line[18] = '24R';
		$tmp_line[19] = strcut( $order_format['country'], 2, '' );
		$tmp_line[20] = '1';
		$tmp_line[21] = strcut( round($order_format['weight_order']), 7, '' );
		$tmp_line[22] = '';
		$tmp_line[23] = '';
		$tmp_line[24] = strcut( round($order_format['total_ttc'] * 100), 7, '' );
		$tmp_line[25] = 'EUR';
		$tmp_line[26] = trim( $order_format['assurance'] ) != '' ? $order_format['assurance'] : '0';
		$tmp_line[27] = '0';
		$tmp_line[28] = 'EUR';
		$tmp_line[29] = '';
		$tmp_line[30] = '0';
		$tmp_line[31] = '0';
		$tmp_line[32] = '0';
		$tmp_line[33] = '';

		for( $i=34 ; $i<44 ; $i++ ){
			$tmp_line[ $i ] = array_key_exists( ($i-34), $ar_product ) ? $ar_product[ ($i-34) ]['title'] : '';
		}

		$ar_lines[] = $tmp_line;
		$ar_ord_ids[] = $order['id'];

		if( sizeof($ar_ord_ids) == 50 ){
			break;
		}
	}
	
	if( sizeof($ar_lines) ){
		header("Content-type: text/csv");
		header("Content-Disposition: attachment; filename=export-mondial-relay-".date('dmY').".csv");
		header("Pragma: no-cache");
		header("Expires: 0");

		foreach( $ar_lines as $one_line ){
			$line = '';
			$first = true;

			foreach( $one_line as $col ){
				if( !$first ){
					$line .= ';';
				}

				$line .= utf8_decode( str_replace(';', '', $col) );

				$first = false;
			}

			print $line."\n";
		}

		exit;
	}else{
		$_SESSION['export_shipment_no_order'] = "Aucune commande à exporter pour Mondial Relay";
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

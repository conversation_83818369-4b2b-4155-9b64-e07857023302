<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	global $config;
	
	if( !isset($orders) )
		exit;
	
	require_once('excel/PHPExcel.php');
	$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	
	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle(_("Export des commandes"))
								 ->setSubject(_("Export des commandes"))
								 ->setDescription(_("Export des commandes"))
								 ->setKeywords("export commandes")
								 ->setCategory("");
	
	// Création du fichier
	$objWorksheet = $objPHPExcel->getActiveSheet();
	
	// 1° feuille : Contient l'entête des commande
	$objWorksheet->setTitle(_('Commandes'));

	// 2° feuille : Contient les lignes de commandes
	$objWorksheet2 = $objPHPExcel->createSheet();
	$objWorksheet2->setTitle(_('Lignes de commandes'));
	
	// 1° feuille : Nom des colonnes
	$objWorksheet->setCellValue('A1', _('Référence Internet'));
	$objWorksheet->mergeCells('A1:A2');
	$objWorksheet->getColumnDimension('A')->setWidth(18);
	
	$objWorksheet->setCellValue('B1', _('Référence GESCOM'));
	$objWorksheet->mergeCells('B1:B2');
	$objWorksheet->getColumnDimension('B')->setWidth(18);
	$objWorksheet->getColumnDimension('A')->setWidth(18);

	$objWorksheet->setCellValue('C1', _('Référence commande'));
	$objWorksheet->mergeCells('C1:C2');
	$objWorksheet->getColumnDimension('C')->setWidth(18);

	$objWorksheet->setCellValue('D1', _('Référence représentant'));
	$objWorksheet->mergeCells('D1:D2');
	$objWorksheet->getColumnDimension('D')->setWidth(18);
	
	$objWorksheet->setCellValue('E1', _('Référence compte client'));
	$objWorksheet->mergeCells('E1:E2');
	$objWorksheet->getColumnDimension('E')->setWidth(18);
	
	$objWorksheet->setCellValue('F1', _('Date'));
	$objWorksheet->mergeCells('F1:F2');
	$objWorksheet->getColumnDimension('F')->setWidth(18);
	
	$objWorksheet->setCellValue('G1', _('Représentant de la commande'));
	$objWorksheet->mergeCells('G1:H1');

	$objWorksheet->setCellValue('G2', _('Nom'));
	$objWorksheet->getColumnDimension('G')->setWidth(25);

	$objWorksheet->setCellValue('H2', _('Prénom'));
	$objWorksheet->getColumnDimension('H')->setWidth(25);

	$objWorksheet->setCellValue('I1', _('Adresse de facturation'));
	$objWorksheet->mergeCells('I1:R1');

	$objWorksheet->setCellValue('I2', _('Société'));
	$objWorksheet->getColumnDimension('I')->setWidth(25);

	$objWorksheet->setCellValue('J2', _('Civilité'));
	$objWorksheet->getColumnDimension('J')->setWidth(15);

	$objWorksheet->setCellValue('K2', _('Nom'));
	$objWorksheet->getColumnDimension('K')->setWidth(25);

	$objWorksheet->setCellValue('L2', _('Prénom'));
	$objWorksheet->getColumnDimension('L')->setWidth(25);

	$objWorksheet->setCellValue('M2', _('Adresse'));
	$objWorksheet->mergeCells('M2:N2');
	$objWorksheet->getColumnDimension('M')->setWidth(35);
	$objWorksheet->getColumnDimension('N')->setWidth(35);

	$objWorksheet->setCellValue('O2', _('Code postal'));
	$objWorksheet->getColumnDimension('O')->setWidth(15);

	$objWorksheet->setCellValue('P2', _('Ville'));
	$objWorksheet->getColumnDimension('P')->setWidth(25);

	$objWorksheet->setCellValue('Q2', _('Pays'));
	$objWorksheet->getColumnDimension('Q')->setWidth(25);
	
	$objWorksheet->setCellValue('R2', _('Email'));
	$objWorksheet->getColumnDimension('R')->setWidth(35);
	
	$objWorksheet->setCellValue('S1', _('Adresse de livraison'));
	$objWorksheet->mergeCells('S1:AA1');

	$objWorksheet->setCellValue('S2', _('Société'));
	$objWorksheet->getColumnDimension('S')->setWidth(25);

	$objWorksheet->setCellValue('T2', _('Civilité'));
	$objWorksheet->getColumnDimension('T')->setWidth(15);

	$objWorksheet->setCellValue('U2', _('Nom'));
	$objWorksheet->getColumnDimension('U')->setWidth(25);

	$objWorksheet->setCellValue('V2', _('Prénom'));
	$objWorksheet->getColumnDimension('V')->setWidth(25);

	$objWorksheet->setCellValue('W2', _('Adresse'));
	$objWorksheet->mergeCells('W2:X2');
	$objWorksheet->getColumnDimension('W')->setWidth(35);
	$objWorksheet->getColumnDimension('X')->setWidth(35);

	$objWorksheet->setCellValue('Y2', _('Code postal'));
	$objWorksheet->getColumnDimension('Y')->setWidth(15);
	
	$objWorksheet->setCellValue('Z2', _('Ville'));
	$objWorksheet->getColumnDimension('Z')->setWidth(25);
	
	$objWorksheet->setCellValue('AA2', _('Pays'));
	$objWorksheet->getColumnDimension('AA')->setWidth(25);

	if( !isset($_GET['state']) || !$_GET['state'] ) {
		$objWorksheet->setCellValue('AB1', _('Statut'));
		$objWorksheet->mergeCells('AB1:AB2');
	}
	
	$objWorksheet->setCellValue('AC1', _('Moyen de paiement'));
	$objWorksheet->getColumnDimension('AC')->setWidth(25);
	$objWorksheet->mergeCells('AC1:AC2');

	$objWorksheet->setCellValue('AD1', _('Relance panier ?'));
	$objWorksheet->getColumnDimension('AD')->setWidth(15);
	$objWorksheet->mergeCells('AD1:AD2');
	
	$objWorksheet->setCellValue('AE1', _('Total HT'));
	$objWorksheet->getColumnDimension('AE')->setWidth(15);
	$objWorksheet->mergeCells('AE1:AE2');
	
	$objWorksheet->setCellValue('AF1', _('Total TTC'));
	$objWorksheet->getColumnDimension('AF')->setWidth(15);
	$objWorksheet->mergeCells('AF1:AF2');

	// 1° feuille : Si l'on connait le status des commandes, on masque la colonne U
	$objWorksheet->getColumnDimension('AB')->setWidth( isset($_GET['state']) && $_GET['state']>0 ? 0 : 23 );
	
	// 2° feuille : Nom des colonnes
	$objWorksheet2->setCellValue('A1', _('Référence Internet'));
	$objWorksheet2->getColumnDimension('A')->setWidth(18);
	
	$objWorksheet2->setCellValue('B1', _('Référence GESCOM'));
	$objWorksheet2->getColumnDimension('B')->setWidth(18);
	
	$objWorksheet2->setCellValue('C1', _('Référence'));
	$objWorksheet2->getColumnDimension('C')->setWidth(12);

	$objWorksheet2->setCellValue('D1', _('Désignation'));
	$objWorksheet2->getColumnDimension('D')->setWidth(80);

	$objWorksheet2->setCellValue('E1', _('Marque'));
	$objWorksheet2->getColumnDimension('E')->setWidth(12);
	
	$objWorksheet2->setCellValue('F1', _('Prix HT'));
	$objWorksheet2->getColumnDimension('F')->setWidth(11);
	
	$objWorksheet2->setCellValue('G1', _('Quantité'));
	$objWorksheet2->getColumnDimension('G')->setWidth(11);
	
	$objWorksheet2->setCellValue('H1', _('Total HT'));
	$objWorksheet2->getColumnDimension('H')->setWidth(13);
	
	// Police sur l'entête des colonnes
	$objWorksheet->getStyle('A1:AF2')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:AF2')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:AF2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet->getStyle('A1:AF2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet->getStyle('A1:AF2')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
	
	$objWorksheet2->getStyle('A1:H1')->getFont()->setBold(true);
	$objWorksheet2->getStyle('A1:H1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet2->getStyle('A1:H1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet2->getStyle('A1:H1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet2->getStyle('A1:H1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

	$ar_payments = ord_payment_types_get_array();
	$ar_states = ord_states_get_array();
	
	// Enregistre les entêtes de commandes
	$line = 3; $firstline = 3; 
	$lineprd = 2; $firstprdline = 2;
	while( $order = ria_mysql_fetch_array($orders) ){

		// Récupère l'information du champ avancé 'Commande relancé par email'
		$notified = fld_object_values_get($order['id'], _FLD_ORD_CART_NOTIFY);
		if( in_array($notified, array('Oui', 'oui', '1')) ){
			$notified = 'Oui';
		}else{
			$notified = 'Non';
		}

		$objWorksheet->setCellValue('A'.$line, $order['id'] );
		$objWorksheet->setCellValue('B'.$line, $order['piece'] );
		$objWorksheet->setCellValue('C'.$line, $order['ref'] );
		$objWorksheet->setCellValue('D'.$line, $order['seller_id'] );
		$objWorksheet->setCellValue('E'.$line, $order['usr_id'] );
		$objWorksheet->setCellValue('F'.$line, $order['date'] );

		$ord_adr = ord_orders_address_load($order);

		//Représentant
		if ($order['seller_id']){
			$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $order['seller_id']);

			if ($r_seller && ria_mysql_num_rows($r_seller)){
				$seller = ria_mysql_fetch_assoc($r_seller);
				
				$objWorksheet->setCellValue('G' . $line, $seller['adr_lastname']);
				$objWorksheet->setCellValue('H' . $line, $seller['adr_firstname']);
			}
		}

		// Adresse de facuration
		$adr_inv = array();
		if( $ord_adr['invoice']['type_id']==1 ){
			$objWorksheet->setCellValue('J' . $line, $ord_adr['invoice']['title_name']);
			$objWorksheet->setCellValue('K' . $line, $ord_adr['invoice']['lastname']);
			$objWorksheet->setCellValue('L' . $line, $ord_adr['invoice']['firstname']);
		}else{
			$objWorksheet->setCellValue('I' . $line, $ord_adr['invoice']['society']);
		}
		
		$objWorksheet->setCellValue('M' . $line, $ord_adr['invoice']['address1']);
		$objWorksheet->setCellValue('N' . $line, $ord_adr['invoice']['address2']);
		$objWorksheet->setCellValue('O' . $line, $ord_adr['invoice']['postal_code']);
		$objWorksheet->setCellValue('P' . $line, $ord_adr['invoice']['city']);
		$objWorksheet->setCellValue('Q' . $line, $ord_adr['invoice']['country']);
		
		if (isset($order['usr_id'])) {
			$email = gu_users_get_email($order['usr_id']);
			if ($email) {
				$objWorksheet->setCellValue('R' . $line, $email);
			}
		}
		
		// Adresse de livraison
		$objWorksheet->setCellValue('S'.$line, $ord_adr['delivery']['society'] );
		$objWorksheet->setCellValue('T'.$line, $ord_adr['delivery']['title_name'] );
		$objWorksheet->setCellValue('U'.$line, $ord_adr['delivery']['lastname'] );
		$objWorksheet->setCellValue('V'.$line, $ord_adr['delivery']['firstname'] );
		$objWorksheet->setCellValue('W'.$line, $ord_adr['delivery']['address1'] );
		$objWorksheet->setCellValue('X'.$line, $ord_adr['delivery']['address2'] );
		$objWorksheet->setCellValue('Y'.$line, $ord_adr['delivery']['postal_code'] );
		$objWorksheet->setCellValue('Z'.$line, $ord_adr['delivery']['city'] );
		$objWorksheet->setCellValue('AA'.$line, $ord_adr['delivery']['country'] );
		
		$objWorksheet->setCellValue('AB'.$line, $ar_states[$order['state_id']]['name'] );
		$objWorksheet->setCellValue('AC'.$line, ($order['pay_id'] ? $ar_payments[$order['pay_id']]['name'] : '') );
		$objWorksheet->setCellValue('AD'.$line, $notified );
		$objWorksheet->setCellValue('AE'.$line, number_format( $order['total_ht'], 2, '.', '' ) );
		$objWorksheet->setCellValue('AF'.$line, number_format( $order['total_ttc'], 2, '.', '' ) );
	
		// Enregistre les lignes de commandes
		$rpord = ord_products_get( $order['id'] );
		if( $rpord && ria_mysql_num_rows($rpord) ){
			while( $pord = ria_mysql_fetch_array($rpord) ){
				
				$objWorksheet2->setCellValue('A'.$lineprd, $order['id']);
				$objWorksheet2->setCellValue('B'.$lineprd, $order['piece']);
				$objWorksheet2->setCellValue('C'.$lineprd, $pord['ref']);
				$objWorksheet2->setCellValue('D'.$lineprd, $pord['title']);
				$objWorksheet2->setCellValue('E'.$lineprd, $pord['brd_name']);
				$objWorksheet2->setCellValue('F'.$lineprd, number_format( $pord['price_ht'], 2, '.', '' ));
				$objWorksheet2->setCellValue('G'.$lineprd, number_format( $pord['qte'], 0, '', ' ' ));
				$objWorksheet2->setCellValue('H'.$lineprd, number_format( $pord['total_ht'], 2, '.', '' ));
				$lineprd++;
				
			}
		}
		
		$objWorksheet->getStyle('F'.$line)->getAlignment()->setWrapText(true);
		$objWorksheet->getStyle('G'.$line)->getAlignment()->setWrapText(true);
		
		$line++;
	}
	
	// Mise en place des totaux finaux
	$objWorksheet->mergeCells('A'.$line.':AD'.$line);
	$objWorksheet->setCellValue('A'.$line, _('Total').' : ' );
	$objWorksheet->setCellValue('AE'.$line, '=SUM(AE' . $firstline. ':AE'.($line-1).')' );
	$objWorksheet->setCellValue('AF'.$line, '=SUM(AF' . $firstline . ':AF'.($line-1).')' );
	$objWorksheet->getStyle('A'.$line.':AD'.$line)->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
	$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->setBold(true);
	$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A'.$line.':AB'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
	$objWorksheet->getStyle('A'.$line.':AB'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
	
	$lineprd--; $line--;
	
	// Gestion des alignements
	$objWorksheet->getStyle('A' . $firstline . ':AF'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
	$objWorksheet->getStyle('A' . $firstline . ':F'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet->getStyle('AE' . $firstline . ':AE'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
	$objWorksheet->getStyle('AF' . $firstline . ':AF'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
	
	$objWorksheet2->getStyle('A' . $firstprdline . ':H'.$lineprd)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
	$objWorksheet2->getStyle('A' . $firstprdline . ':B'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet2->getStyle('C' . $firstprdline . ':E'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
	$objWorksheet2->getStyle('F' . $firstprdline . ':H'.$lineprd)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

	// Format de données
	$objWorksheet->getStyle('AE' . $firstline . ':AE'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');
	$objWorksheet->getStyle('AF' . $firstline . ':AF'.($line + 1))->getNumberFormat()->setFormatCode('#,##0.00 €');

	$objWorksheet2->getStyle('F' . $firstprdline . ':F'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
	$objWorksheet2->getStyle('H' . $firstprdline . ':H'.$lineprd)->getNumberFormat()->setFormatCode('#,##0.00 €');
	
	// Bordure
	$objWorksheet->getStyle('A1:AF'.$line)->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
	$objWorksheet2->getStyle('A1:H'.$lineprd)->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);

	$objPHPExcel->setActiveSheetIndex(0);

	// Redirect output to a clientâ??s web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="commandes-'.date('dmY').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;



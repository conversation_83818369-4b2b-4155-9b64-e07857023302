<?php

	/**	\file invoice.php
	 *	Cette page est chargée d'afficher une facture. Elle a été créée à l'origine pour les besoins de Pierre Oteiza
	 *	dans le cadre de son système de fidélité. Le problème rencontré était que les factures remontant des caisses
	 *	ne pouvaient pas être visualisées ce qui rendait difficile les diagnostics.
	 *	Cet écran ne permet que la visualisation de la facture, aucune modification.
	 *	Il a été créé à partir du fichier order.php 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	require_once('orders.inc.php');
	require_once('ord.returns.inc.php');
	require_once('delivery.inc.php');
	require_once('promotions.inc.php');
	require_once('fields.inc.php');
	require_once('strings.inc.php');

	// L'identifiant de la pièce est obligatoire et il faut disposer d'un accès à cette page
	if( !isset($_GET['inv']) || !is_numeric($_GET['inv']) ){
		header('Location: /admin/orders/index.php');
		exit;
	}

	// Charge la facture
	$rinv = ord_invoices_get( $_GET['inv'] );
	if( !ria_mysql_num_rows($rinv) ){
		header('Location: /admin/orders/index.php');
		exit;
	}
	$inv = ria_mysql_fetch_array( $rinv );
	
	unset( $error, $no_error );
	
	// Rattachement à un modèle de saisie
	/*if( !$ctr_validation && isset($_POST['addmdl']) && isset($_POST['model-pick']) && is_numeric($_POST['model-pick']) && $_POST['model-pick']>0 ){
		$res = fld_object_models_add( $_GET['ord'],$_POST['model-pick'] );
		if( $res===false ){
			$error = sprintf(_("L'ajout du modèle de saisie à la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}*/
	
	// Détachement d'un modèle de saisie
	/*if( !$ctr_validation && isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		$res = fld_object_models_del( $_GET['ord'],$_GET['delmdl'] );
		if( $res===false ){
			$error = sprintf(_("La suppression du modèle de saisie pour la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}*/

	// Sauvegarde des champs avancés
	/*if( !$ctr_validation && isset($_POST['savefields']) ){
		$fields = fld_fields_get( 0,0,-2,0,0,0,null,array(),false,array(),null,CLS_ORDER );
		$notify_port = false; // Par défaut aucune notification est envoyé
		while( $f = ria_mysql_fetch_array($fields) ){
			if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) )
				$_POST['fld'.$f['id']] = '';
			if( isset($_POST['fld'.$f['id']]) ){
				$value = $_POST['fld'.$f['id']];
				fld_object_values_set( $_GET['ord'], $f['id'], $value );

				//Verification si un champs avancé lié au frais de port est modifier
				if( $f['id'] == FLD_PRODUCT_FDP_1|| $f['id'] == FLD_PRODUCT_FDP_2 ){
					$notify_port = true;
				}
			}
		}
		if(gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_PORT') && $notify_port){
			$res = ord_order_notify_port($_GET['ord']);
			if(!$res){
				$error = sprintf(_("Erreur lors de l'envoi de la notification."), $_GET['ord']);
			}

		}
		header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
		exit;
	}*/
		
	// Défini le titre de la page
	$num = $inv['piece']!='' ? $inv['piece'] : str_pad( $inv['id'], 8, '0', STR_PAD_LEFT );
	define('ADMIN_PAGE_TITLE', sprintf(_('Facture %s - Commandes'), $num ));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php print view_ord_is_sync($inv); printf( ' '._('Pièce n°%d du %s'), str_pad( $inv['id'], 8, '0', STR_PAD_LEFT ), ria_date_format($inv['date'])); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
	if( isset($no_error) ){
		print '<div class="error-success">'.htmlspecialchars($no_error).'</div>';
	}

	$usr = array();
	$prc = array('money_code' => '');
	$rusr = gu_users_get($inv['usr_id']);
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_array($rusr);
	}
	
?>
<table id="table-une-commande" class="tb_invoice">
<thead>
	<tr>
		<th colspan="2" class="multi-colspan"><?php print _('Propriétés générales'); ?></th>
	</tr>
</thead>
<tbody>
	<?php if (!trim($inv['piece'])) { ?>
		<tr>
			<td class="multi-colspan" colspan="2">
				<span class="title-detail"><?php print _('Référence :'); ?></span>
				<span><?php print trim($inv['ref']) ? htmlspecialchars($inv['ref']) : ''; ?></span>
			</td>
		</tr>
	<?php } ?>
	<?php if( trim($inv['piece']) ){ ?>
		<tr>
			<td  class="multi-colspan" colspan="2">
				<span class="title-detail"><?php print _('Numéro de pièce :')?></span>
				<span><?php print view_ord_is_sync( $inv ).'&nbsp;'.htmlspecialchars($inv['piece']); ?></span>
			</td>
		</tr>
	<?php }	?>
	<?php if( trim($inv['ref']) ){ ?>
		<tr>
			<td class="multi-colspan" colspan="2">
				<span class="title-detail"><?php print _('Référence :')?></span>
				<span><?php print htmlspecialchars($inv['ref']); ?></span>
			</td>
		</tr>
	<?php }	?>
	<tr id="ord-adresses-row">
		<td colspan="2" id="adresses">
			<div id="ord-addresses">

				<div id="ord-addresses-compte-client">
					<span class="title-detail"><?php print _('Compte client :'); ?></span>
					<span id="compte-client-name">
						<?php
						if( $inv['usr_id'] ){
							$ref = gu_users_get_ref($inv['usr_id']);
							if( $ref===false ){
								$ref = str_pad( $inv['usr_id'], 8, '0', STR_PAD_LEFT );
								print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars( $ref ).'</span>';
							}else{
								if( !$ref ){
									$ref = str_pad( $inv['usr_id'], 8, '0', STR_PAD_LEFT );
								}
								print '<a href="/admin/customers/edit.php?usr='.$inv['usr_id'].'">';
								print view_usr_is_sync( $usr ).'&nbsp;'.htmlspecialchars( $ref );
								print '</a>';	
							}
						}
						?>
					</span>
				</div>

			</div>
		</td>
	</tr>
	<tr>
		<th colspan="2" class="multi-colspan"><?php print _('Articles'); ?></th>
	</tr>
	<tr class="order-products-row" data-ord="<?php print htmlspecialchars($inv['id']) ?>">
		
		<td colspan="2" class="multi-colspan">
		<table id="ord-products-articles">
			<thead class="th-head-second thead-none">
				<tr>
				<?php if (!$inv['piece']) { ?>
					<th class="col20px"></th>
					<th class="col20px"><?php if (!$inv['piece']) { ?><input type="checkbox" id="checkall"/><?php } ?></th>
				<?php } ?>
					<th class="col125px"><?php print _('Référence'); ?></th>
					<th class="th-prd-comment"><?php print _('Désignation'); ?></th>
					<th class="col100px align-right"><?php print _('Prix Unitaire'); ?></th>
					<th class="col80px align-right"><?php print _('Quantité'); ?></th>
					<th class="col80px align-right"><?php print _('Total'); ?></th>
				</tr>
			</thead>
			<tbody>
			<?php
				// Affiche la liste des produits contenus dans cette pièce
				$r_inv_prd = ord_inv_products_get( $inv['id'] );
				while( $prd = ria_mysql_fetch_assoc($r_inv_prd) ){
					print '<tr class="ord-prd-row">';
					if( $prd['id']!=0 ){

						$categories = prd_products_categories_get($prd['id']);
						$is_colisage = prd_colisage_classify_exists($prd['id']);
						$colisage_id = 0;
						if ($is_colisage && $colisage_id = fld_object_values_get(array($inv['id'], $prd['id'], $prd['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
							$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
							$colisage = ria_mysql_fetch_assoc($r_colisage);
						} else {
							$is_colisage = false;
						}

						print '     <td colspan="2" class="multi-colspan">
										<div class="ord-prd-ref-name">
											<div class="ord-prd-nmc-row-main">';

						// Affiche la référence du produit
						print '<span class="td-prd-ref-name-1">';
						if( $cat = ria_mysql_fetch_array($categories) ) {
							print '     <a href="/admin/catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a>';
						} else {
							print htmlspecialchars($prd['ref']);
						}
						print '</span>';

						// Désignation du produit et commentaires éventuels
						print '<span id="padding-top-5">
								<span class="ord-prd-name-txt">'.htmlspecialchars($prd['name']).' '.($is_colisage ? ' - '.htmlspecialchars($colisage['name']).' ('.parseInt($colisage['qte']).')' : '') .'</span>';
						print '</span>';

						print ' </div>';

						print '
							</div>
						</td>'; //Fin div ord-prd-ref-name

						// Prix unitaire
						print ' <td class="align-right td-prd-prix-unitaire">
									<span class="ord-prd-price-txt">'.ria_number_format($prd['price_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</span>';
						print ' </td>';

						// Quantité
						print '<td class="align-right td-prd-quantite">
									<span class="ord-prd-qte-txt">'.ria_number_format( $prd['qte'] ).'</span>';
						print ' </td>';

						// Total HT de la ligne
						print '<td class="align-right td-prd-total">'.ria_number_format($prd['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>';

					}else{
						
						// Commentaires
						print '<td colspan="5">'.nl2br( htmlspecialchars($prd['notes']) ).'</td>';
					
					}
					print '</tr>';
				}
			?>
			</tbody>
			<tfoot>
				<tr>
					<th colspan="3" class="align-right"><?php print _('Total HT :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($inv['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
				<tr>
					<th colspan="3" class="align-right"><?php print _('TVA :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($inv['total_ttc']-$inv['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
				<tr>
					<th colspan="3" class="align-right"><?php print _('Total TTC :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($inv['total_ttc'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
			</tfoot>
		</table>
	</td>


	</tr>
</tbody>
</table>

<?php

require_once('admin/skin/footer.inc.php');
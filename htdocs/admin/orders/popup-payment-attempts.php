<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

    $couchDb_ar_payment_attempts = ord_payment_couchdb_get( $_GET['ord'] );

    define('ADMIN_PAGE_TITLE', _('Historique des paiements').' - '._('Commandes'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
    <table id="list-payment-attempts" style="margin-top: 7px;" max-width="100%">
        <caption><?php print _('Liste des paiements'); ?></caption>
        <thead>
            <?php 
                if (is_array($couchDb_ar_payment_attempts) && sizeof($couchDb_ar_payment_attempts)){ 
                    print ' <th class="col150px">'._('Date').'</th>
                            <th>'._('Code de retour').'</th>
                    ';
                    if ($config['USER_RIASTUDIO']) { 
                        print '<th class="col150px"></th>';
                    }
                } 
            ?> 
        </thead>
        <tbody>
            <?php 
                if (is_array($couchDb_ar_payment_attempts) && sizeof($couchDb_ar_payment_attempts)){
                    $cpt = 0;
                    foreach ($couchDb_ar_payment_attempts as $attempt => $content) {
                        print '<tr>
                            <td>'.date('d/m/Y à H:i', $content['date']).'</td>
                            <td>'.(isset($content['code_id']) && isset($content['code_name']) ? '['.$content['code_id'].'] - '.$content['code_name'] : _('Tentative d\'accès')).'</td>
                        ';
                            if ($config['USER_RIASTUDIO']){
                                print '<td style="text-align: right">';
                                    print '<a class="attempts_details" id="attempts_details-'.$cpt.'">'._('Voir les détails').'</a>';
                                    print '<a class="attempts_reduct" id="attempts_reduct-'.$cpt.'">'._('Réduire').'</a>';
                                print '</td>';
                            }
                        print ' </tr>
                                <tr>
                                    <td class="attempt_data" id="attempt_data-'.$cpt.'" colspan="'.($config['USER_RIASTUDIO'] ? 3 : 2).'">
                        ';
                                if(is_array($content['data'])){
                                    print '<pre>'.print_r($content['data'], true).'</pre>';
                                } else {
                                    print nl2br($content['data']);
                                }
                            print '
                                    </td>
                                </tr>
                        ';
                        $cpt++;
                    }
                } else {
                    print ' <tr>
                                <td colspan="'.($config['USER_RIASTUDIO'] ? 3 : 2).'">'._('Aucune tentative de paiement n\'a été effectuée ou enregistrée pour cette commande').'</td>
                            </tr>';
                }
            ?>
        </tbody>
    </table>

	<script src="/admin/js/jquery.min.js"></script>
	<script><!--
		$(document).ready(function(){
            $(".attempt_data").hide();
            $(".attempts_reduct").hide();

            $(".attempts_details").on('click', function(){
                var id = $(this).attr('id').substring($(this).attr('id').indexOf('-')+1, $(this).attr('id').length);
                $(".attempt_data").hide();
                $(".attempts_reduct").hide();
                $(".attempts_details").show();
                $("#attempt_data-"+id).show();
                $("#attempts_details-"+id).hide();
                $("#attempts_reduct-"+id).show();
            });

            $(".attempts_reduct").on('click', function(){
                var id = $(this).attr('id').substring($(this).attr('id').indexOf('-')+1, $(this).attr('id').length);
                $("#attempt_data-"+id).hide();
                $("#attempts_details-"+id).show();
                $("#attempts_reduct-"+id).hide();
            });
        });
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
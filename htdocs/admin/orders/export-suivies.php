<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$state = isset($_GET['state']) && is_numeric($_GET['state']) && $_GET['state'] ? $_GET['state'] : 0;

	if( !isset($orders) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	if( !isset($config['export_shipment_active']) || !$config['export_shipment_active'] ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	// Récupère les informations sur l'utilisation de Lettre de suivie
	if( !isset($config['export_shipment_colissimo']) || empty($config['export_shipment_suivies']) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	/*
	 	0 = Référence interne ; 25 caractères alphanumériques max
        1 = Raison sociale ; 38 caractères alphanumériques max (Obligatoire pour une entreprise)
        2 = Libellé Civilité ; Liste de valeurs ci-dessous (Uniquement pour un particulier, sinon vide)
        3 = Nom ; 17 caractères alphanumériques max (Obligatoire pour un particulier)
        4 = Prénom ; 10 caractères alphanumériques max (Uniquement pour un particulier, sinon vide)
        5 = Identité / Service ou N° Escalier / Etage ; 38 caractères alphanumériques max
        6 = Bâtiment / Immeuble ; 38 caractères alphanumériques max
        7 = N° de voie ; soit 4 caractères numériques max, soit 3 caractères numériques max suivi d'un espace et d'une lettre
        8 = Nom de voie ; 32 caractères alphanumériques max (Obligatoire)
        9 = Lieu-dit / Boite Postale ; 38 caractères alphanumériques max
        10 = Code Postal / Code Cedex ; Exactement 5 caractères alphanumériques (Obligatoire)
        11 = Ville/Libellé Cedex; 32 caractères alphanumériques max
        12 = Niveau de garantie ; Liste de valeurs : R1 - R2 - R3 (Obligatoire si recommandé)
        13 = Poids en gramme ; 4 caractères numériques max (Obligatoire en affranchissement Courrier Entreprise)
        14 = Valeur déclarée en euro ; 4 caractères numériques max (Obligatoire si VD)

	*/
	require_once('strings.inc.php');
	require_once('orders.inc.php');

	$ar_ord_ids = array();
	$ar_lines 	= array();

	$headers = array(
		_('Référence interne'),
		_('Raison sociale'),
		_('Libellé Civilité'),
		_('Nom'),
		_('Prénom'),
		_('Identité / Service ou N° Escalier / Etage'),
		_('Bâtiment / Immeuble'),
		_('N° de voie'),
		_('Nom de voie'),
		_('Lieu-dit / Boite Postale'),
		_('Code Postal / Code Cedex'),
		_('Ville/Libellé Cede'),
		_('Niveau de garantie'),
		_('Poids en gramme'),
		_('Valeur déclarée en euro')
		);

	while( $order = ria_mysql_fetch_assoc($orders) ){
		// Exclusion des commandes livrées en magasin
		if( is_numeric($order['str_id']) && $order['str_id'] > 0 ){
			$ar_ord_ids[] = $order['id'];
			continue;
		}

		// Exclusion des commandes livrées par un autre service que Lettre de suivies
		if( !in_array($order['srv_id'], $config['export_shipment_suivies']) ){
			continue;
		}

		$r_ord_adr = ord_orders_get_with_adresses(0, $order['id']);
		if (!$r_ord_adr || !ria_mysql_num_rows($r_ord_adr)) {
			continue;
		}

		$order_format = ord_orders_shipment_formatted( ria_mysql_fetch_assoc($r_ord_adr), 35 );
		if( !ria_array_key_exists(array('title_id', 'firstname', 'addr1', 'addr2', 'addr3', 'zipcode', 'city', 'ref_order', 'user_id', 'addr3', 'country', 'dlv-notes', 'weight_order', 'assurance', 'phone', 'email', 'society', 'mobile', 'rly_ref', 'rly_country'), $order_format) ){
			continue;
		}
		$r_titles = gu_titles_get($order_format['title_id']);
		if( $r_titles && ria_mysql_num_rows($r_titles) && in_array($order_format['title_id'], array(1,2,3)) ){
			$titles = ria_mysql_fetch_assoc($r_titles);
			$title = $titles['abr'];
		}else{
			$title = 'M. ou Mme';
		}
		$tmp_line = array();
		
		$tmp_line[0]  = substr( $order_format['ref_order'], 0, 25);
		$tmp_line[1]  = '';
		$tmp_line[2]  = substr( $title, 0, 50);
		$tmp_line[3]  = substr( $order_format['lastname'], 0, 17);
		$tmp_line[4]  = substr( $order_format['firstname'], 0, 10 );
		$tmp_line[5]  = substr( $order_format['society'], 0, 38);
		$tmp_line[6]  = '';
		$tmp_line[7]  = '';
		$tmp_line[8]  = strtoupper2( $order_format['addr1'] );
		$tmp_line[9]  = strtoupper2( $order_format['addr2'] );
		$tmp_line[10] = str_pad(substr( $order_format['zipcode'], 0, 5),5,'0',STR_PAD_LEFT);
		$tmp_line[11] = substr( $order_format['city'], 0, 32);
		$tmp_line[12] = '';
		$tmp_line[13] = $order_format['weight_order'];
		$tmp_line[14] = '';

		$ar_lines[] = $tmp_line;
		$ar_ord_ids[] = $order['id'];
	}

	if( sizeof($ar_lines) ){
		header("Content-type: text/csv");
		header("Content-Disposition: attachment; filename=export-suivies-".date('dmY').".csv");
		header("Pragma: no-cache");
		header("Expires: 0");

		print utf8_decode( implode(';', $headers) )."\n";
		foreach( $ar_lines as $one_line ){
			$line = '';
			$first = true;

			foreach( $one_line as $col ){
				if( !$first ){
					$line .= ';';
				}

				$line .= utf8_decode( str_replace(';', '', $col) );

				$first = false;
			}

			print $line."\n";
		}

		exit;
	}else{
		$_SESSION['export_shipment_no_order'] = _("Aucune commande à exporter pour Lettre de suivies");
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}
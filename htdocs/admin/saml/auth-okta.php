<?php
	/** \file auth-okta.php
	 * 	Ce fihcier gère le endpoint des applications Okta permettant la connexion à RiaShop ou à Yuto.
	 * 	Il est inclut dans chaque fichier lié à une application et attend deux variables devant être défini dans les fichiers l'incluant
	 * 		- $c : Obligatoire, code d'application
	 * 		- $for_yuto : Optionnel, s'il s'agit de la connexion à Yuto ou non
	 */
	require_once('tenants.inc.php');
	require_once('http.inc.php');
	require_once('SAML.inc.php');

	// Contrôle qu'un code est fourni
	if( !isset($c) ){
		http_403();
	}

	// Détermine si l'appel est réalisé pour l'application Yuto ou pour l'accès à l'administration
	$for_yuto = isset($for_yuto) ? $for_yuto : false;

	// Inclusion de la librairie SimpleSAMLphp
	require_once('/apps/simplesamlphp/lib/_autoload.php');

	// Récupère la configuration SAML du code fourni
	$sp_config = RiaSaml::getInstance()->getConfig( $c, $for_yuto );
	if( $sp_config === false ){
		http_403();
	}

	// Initialise un objet SimpleSAMLphp permettant les connexions en SAML
	$auth = new SimpleSAML_Auth_Simple($sp_config);
	
	// Contrôle que la connexion a bien été établi
	if( !$auth->isAuthenticated() ){
		// Si ce n'est pas le cas, on renvoi sur la plateforme de connexion passant par SAML
		$auth->requireAuth();
		exit;
	}
	
	$attrs = $auth->getAttributes();
	if( !isset($attrs['Email'][0], $attrs['FirstName'][0], $attrs['LastName'][0]) ){
		http_403();
	}
	
	// Connexion du compte
	SimpleSAML_Session::getSessionFromRequest()->cleanup();
	RiaSaml::getInstance()->login($c, $attrs['Email'][0], $attrs['FirstName'][0], $attrs['LastName'][0]);

	// Contrôle que le compte est bien conencté
	if( !$admin_account->isConnected()){
		http_403();
	}

	// Récupère les informations sur le compte client
	$r_user = gu_users_get( 0, $admin_account->getEmail(), '', array(PRF_ADMIN, PRF_SELLER) );
	if( !$r_user || !ria_mysql_num_rows($r_user) ){
		http_403();
	}

	$user = ria_mysql_fetch_assoc( $r_user );

	// Création d'un token d'authentification pour une durée de 10 minutes
	$token = md5(uniqid().$auth->getAuthData('saml:sp:SessionIndex'));
	if( !gu_users_tokens_add($user['id'], $token, 10) ){
		htto_403();
	}
	
	// Si l'appel est réalisé pour l'administration, on renvoi vers la page d'accueil
	if( !$for_yuto ){
		header('Location: /');
		exit;
	}else{
		// Si l'appel est réalisé pour l'application Yuto, un token d'authentification sera renvoyé
		
		// Création d'un token d'authentification pour une durée de 10 minutes
	        $token = md5(uniqid().$auth->getAuthData('saml:sp:SessionIndex'));
		if( !gu_users_tokens_add($user['id'], $token, 10) ){
                	htto_403();
	        }

		$end = new DateTime();
        	$end->modify('+10 minutes');
		
		// Le token est envoyé en entête de la réponse
		header('Cache-Control: max-age=600');
		header('Expires: '.$end->format('D, d M Y H:i:s \G\M\T'));
		header('Okta-auth: '.$token);
		exit;
	}

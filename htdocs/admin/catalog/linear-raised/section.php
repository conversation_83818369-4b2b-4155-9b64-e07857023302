<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_list_sections;

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_LINEAR_RAISED_CONFIG');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
	header('Location: index.php');
	exit;
}

$error = $success = null;
if (isset($_POST['save'], $_POST['title'])) {
	try{
		if (!prw_followed_list_sections::update($_GET['id'], $_POST['title'])) {
			throw new Exception(_("Erreur sur la requête sql de sauvegarde."));
		}else{
			$success = _('Mise à jour effectuée.');
		}
	}catch(Exception $e) {
		error_log('['.$config['tnt_id'] .'-'. __FILE__.':'.__LINE__.']'.$e->getMessage());
		$error = _('Une erreur est survenue lors de la sauvegarde de votre section.');
	}
}

if (isset($_POST['add'])) {
	if (!isset($_POST['name']) || !trim($_POST['name'])) {
		$error = _("Vous devez saisir un libellé");
	}
	if (!isset($_POST['type']) || !is_numeric($_POST['type'])) {
		$error = _("Vous devez choisir un type d'assortiment");
	}

	if (isset($_POST['name'], $_POST['type'])) {
		$list['name'] = $_POST['name'];
		$list['type'] = $_POST['type'];
	}

	if (is_null($error)) {
		try{
			$id = prw_followed_lists::add($list['name'], $list['type'], false, $_GET['id']);
			if (!$id) {
				throw new Exception(_("Une erreur est survenue lors de la création de votre assortiment. Veuillez prendre contacte avec nous."));
			}else{
				$_SESSION['flash-success'] = str_replace("#param[nom_assortiment]#", "<strong>".htmlspecialchars($list['name'])."</strong>", _("L'assortiment #param[nom_assortiment]# a été créé avec succès. Vous pouvez commencer à paramétrer votre catalogue produits." ));
				header('Location: /admin/catalog/linear-raised/edit.php?id='.$id);
				exit;
			}
		}catch(Exception $e) {
			$error = $e->getMessage();
		}
	}

}

if (isset($_POST['del'], $_POST['list'])) {
	if (!empty($_POST['list'])) {
		foreach ($_POST['list'] as $id) {
			if(!prw_followed_lists::delete($id)) {
				$error = _('Une erreur est survenue lors de la suppression de l\'assortiment.');
			}else{
				prw_followed_products::delete($id);
				prw_followed_users::delete($id);
			}
		}
	}
}
$types = array_flip(prw_followed_lists::$types);
$r_list_sections = prw_followed_list_sections::get($_GET['id']);

if (!$r_list_sections || !ria_mysql_num_rows($r_list_sections)) {
	header('Location: index.php');
	exit;
}
$r_followed_lists = prw_followed_lists::get(null, null, null, $_GET['id']);

$section = ria_mysql_fetch_assoc($r_list_sections);

if (isset($_POST['del-sec'])) {
	try{
		if (!prw_followed_list_sections::delete($_GET['id'])) {
			throw new Exception(_("Erreur sur la requête sql de suppression."));
		}else{
			$_SESSION['flash-success'] = sprintf(_('Section "%s" a été supprimé.'),htmlspecialchars($section['title']));
			header('Location: /admin/catalog/linear-raised/');
			exit;
		}
	}catch(Exception $e) {
		error_log('['.$config['tnt_id'] .'-'. __FILE__.':'.__LINE__.']'.$e->getMessage());
		$error = _('Une erreur est survenue lors de la sauvegarde de votre section.');
	}
}

define('ADMIN_PAGE_TITLE', _('Section').' : '.$section['title']);
require_once('admin/skin/header.inc.php');
?>
<?php if (!is_null($error)){ ?>
	<div class="error">
		<?php echo $error ?>
	</div>
<?php } ?>

<?php if (!is_null($success)){ ?>
	<div class="success">
		<?php echo $success ?>
	</div>
<?php } ?>
<h2><?php echo htmlspecialchars($section['title']) ?></h2>
<form method="post">
	<input type="hidden" name="id" id="id" value="<?php echo $section['id']?>">
	<table id="table-section">
		<caption>
			<?php echo _('Section') ?>
		</caption>
		<tbody>
			<tr>
				<td id="td-section-1">
					<label for="title"><?php echo _('Titre'); ?></label>
				</td>
				<td id="td-section-2">
					<input type="text" name="title" id="title" value="<?php echo $section['title'];?>">
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" id="del" name="del-sec" value="<?php echo _('Supprimer la section') ?>" class="float-left" />
					<input type="submit" id="save" name="save" data-id="<?php echo $section['id'] ?>" value="<?php echo _('Sauvegarder') ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<form method="post">
		<table class="checklist" id="table-assortiments-produits">
			<caption>
				<?php echo _('Liste d\'assortiments produits') ?>
			</caption>
			<thead>
				<th id="th-assortiments-1"><input type="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="th-assortiments-2"><?php echo _('Nom de l\'assortiment'); ?></th>
				<th id="th-assortiments-3"><?php echo _('Type d\'assortiment'); ?></th>
				<th id="th-assortiments-4"><?php echo _('Activer'); ?></th>
				<th id="th-assortiments-5"><?php echo _('Date d\'ajout'); ?></th>
			</thead>
			<tbody>
				<?php if( !$r_followed_lists ) {?>
					<tr>
						<td colspan="5"><?php echo _('Aucun assortiment pour le moment'); ?></td>
					</tr>
				<?php }else{ ?>
				<?php 	while( $list = ria_mysql_fetch_assoc($r_followed_lists) ){ ?>
					<tr>
						<td>
							<input type="checkbox" name="list[]" value="<?php echo $list['id'] ?>" />
						</td>
						<td>
							<a href="/admin/catalog/linear-raised/edit.php?id=<?php echo $list['id']?>">
								<?php echo htmlspecialchars($list['name']) ?>
							</a>
						</td>
						<td>
							<?php echo ucfirst($types[$list['type']]) ?>
						</td>
						<td>
							<?php echo $list['is_published'] ? _('Oui') : _('Non'); ?>
						</td>
						<td>
							<?php echo ria_date_format($list['date_created']) ?>
						</td>
					</tr>
				<?php	} ?>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="5">
						<input type="submit" name="del" value="<?php echo _('Supprimer'); ?>" class="float-left" <?php if( !$r_followed_lists ) {?>disabled <?php }?>/>
						<div class="float-right">
							<label for="name"><?php print _('Ajouter un assortiment :'); ?></label>
							<input type="text" name="name" id="name" maxlength="75" />
							<select name="type" id="type">
								<?php foreach($types as $i => $type) {?>
									<option value="<?php echo $i?>">
										<?php echo ucfirst($type)?>
									</option>
								<?php } ?>
							</select>
							<input type="submit" name="add" value="<?php echo _('Ajouter') ?>" />
						</div>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
require_once('admin/skin/footer.inc.php');
?>
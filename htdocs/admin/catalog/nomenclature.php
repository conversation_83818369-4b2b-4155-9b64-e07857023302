<?php 
	require_once('prd/nomenclatures.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_NOMENCLATURE');

	unset($error);
	
	// Suppression d'option(s)
	if(isset($_POST['delete']) && isset($_POST['del']) && is_array($_POST['del'])){
		foreach($_POST['del'] as $del){
			if(!prd_options_del($del)){
				$error = _("Une erreur inattendue s'est produite lors de la suppression d'un emplacement.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}
	
	// Pour l'ajout d'une option
	if( isset($_POST['add-opt']) && isset($_POST['name-opt']) ){
		$optname = trim($_POST['name-opt']);
		if( $optname=='' ){
			$error = _("Le nom de l'emplacement n'a pas été reconnue.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			$option = prd_options_add( $optname );
			if( !is_numeric($option) || $option<=0 ){
				$error = _("Une erreur inattendue s'est produite lors de l'ajout de emplacement.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				header('Location: edit-nomenclature.php?opt='.$option);
				exit;
			}
		}
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CATALOG_NOMENCLATURE_VAR_DEL');

	$colspan = $checkbox? 2 : 1;

	define('ADMIN_PAGE_TITLE', _('Nomenclatures variables') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');
	
	// Charge la liste des options de nomenclature
	$opts = prd_options_get();
?>
	<h2><?php print _('Nomenclatures variables')?></h2>

	<form action="/admin/catalog/nomenclature.php" method="post">
		<table class="checklist" id="table-nomenclatures-variables">
		<thead>
			<tr>
				<th colspan="<?php print $colspan; ?>" id="name"><?php print _('Nom de l\'emplacement')?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td colspan="<?php print $colspan; ?>">
					<?php if( $opts && ria_mysql_num_rows($opts)>0 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_NOMENCLATURE_VAR_DEL') ){ ?>
						<input class="btn-move float-left button" type="submit" name="delete" value="<?php print _('Supprimer')?>"/>
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_NOMENCLATURE_VAR_ADD') ){ ?>
						<label for="opt"><?php print _('Nouvel emplacement :'); ?></label><input type="text" class="ref" name="name-opt" id="opt" value=""/>
						<input type="submit" class="button" name="add-opt" value="<?php print _('Ajouter')?>"/>
					<?php } ?>
				</td>
			</tr>
		</tfoot>
		<tbody>
		<?php	
			if( !$opts || ria_mysql_num_rows($opts) <= 0 ){
				print '<tr><td colspan="'.$colspan.'">'._('Aucune nomenclature variable').'</td></tr>';
			}else{
				while( $p = ria_mysql_fetch_array($opts) ){ ?>
					<tr> 
					<?php if( $checkbox ){ ?>
						<td headers="checkbox" class="td-with-checkbox"><input type="checkbox" name="del[]" value="<?php print $p['id']; ?>"/></td>
					<?php } 
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_NOMENCLATURE_VAR_EDIT') ){ ?>
						<td headers="name" class="td-without-checkbox"><a href="/admin/catalog/edit-nomenclature.php?opt=<?php print $p['id']; ?>"><?php print htmlspecialchars($p['name']); ?></a></td>
					<?php }else{ ?>
						<td headers="name" class="td-without-checkbox"><?php print htmlspecialchars($p['name']); ?></td>
					<?php } ?>
					</tr> <?php
				}
			}
		?>
		</tbody>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
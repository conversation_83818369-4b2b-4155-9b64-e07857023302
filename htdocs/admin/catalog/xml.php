<?php

/**	\file xml.php
 *	Ce fichier est appelé en Ajax et permet les actions suivantes sur les produits (onglet Relations et Nomenclatures) :
 *	- prd-relations-add : Ajout d'une relation produit
 *	- prd-relations-del : Suppression d'une relation produit
 *	- prd-parents-add : Ajout d'une relation parent
 *	- prd-parents-del : Suppression d'une relation parent
 *	- prd-childs-add : Ajout d'un enfant
 *	- prd-childs-del : Suppression d'un enfant
 *	- opt-add
 *	- opt-del
 *	- opt-prd-add
 *	- opt-prd-del
 *	- opt-prd-list
 *	- prd-nomenclature-add
 *	- prd-nomenclature-del
 */

// Vérifie que l'utilisateur peut administrer les relations d'un produit
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

require_once( 'prd/relations.inc.php');
require_once( 'prd/nomenclatures.inc.php');
require_once( 'strings.inc.php' );

if( !isset($_GET['action']) ){ exit; }

header('Content-Type: text/xml');
echo '<?xml version="1.0" encoding="utf-8"?>', "\n";

switch( $_GET['action'] ){
	case 'prd-relations-add':
		if( !isset($_POST['src'],$_POST['dst'],$_POST['type']) ) return;
		
		$rel_product = false;
		$dst_ref = strtoupper2(trim($_POST['dst']));
		
		$cats = prd_products_categories_get_array( $_POST['src'] );
		if( is_array($cats) && sizeof($cats) ){
			$cat = $cats[0];
			$parents = prd_categories_parents_get_array( $cat );
			if( is_array($parents) && sizeof($parents) ){
				$cat = $parents[0];
			}

			$rel_product = prd_products_get_simple( isset($_POST['dst-id']) && is_numeric($_POST['dst-id']) && $_POST['dst-id'] ? $_POST['dst-id'] : 0, $dst_ref, false, $cat, true );
		}

		if( !$rel_product || !ria_mysql_num_rows($rel_product) ){
			$rel_product = prd_products_get_simple( isset($_POST['dst-id']) && is_numeric($_POST['dst-id']) && $_POST['dst-id'] ? $_POST['dst-id'] : 0, $dst_ref );
		}

		if( !ria_mysql_num_rows($rel_product) ){
			print '<error message="'.sprintf(_('La référence %s n\'a pas été trouvée'), $dst_ref).'" />';
		}else{
			$rel = ria_mysql_fetch_array( $rel_product );
			if( prd_relations_add( $_POST['src'], $rel['id'], $_POST['type'] ) ){
				print '<success id="'.$rel['id'].'" ref="'.$rel['ref'].'" title="'.xmlentities($rel['name']).'" publish="'.$rel['publish'].'" publish_cat="'.$rel['publish_cat'].'" is_sync="0" />';
			}elseif( $_POST['src']==$rel['id'] ){
				print '<error message="'.sprintf(_('La référence %s ne peut pas être en relation avec elle même.'), $dst_ref).'" />';
			}else{
				print '<error message="'.sprintf(_('L\'ajout de la référence %s a échoué pour une raison inconnue.'), $dst_ref).'" />';
			}
		}
		break;
	case 'prd-relations-del':
		if( !isset($_POST['src'],$_POST['dst'],$_POST['type']) ) return;
		
		if( is_array($_POST['dst']) ){
			$error = false;
			foreach( $_POST['dst'] as $dst ){
				if( !prd_relations_del( $_POST['src'], $dst, $_POST['type'] ) ){
					$error = true;
				}
			}
			if( !$error ){
				print '<success />';
			}else{
				print '<error message="'._('La suppression de la référence a échouée pour une raison inconnue.').'" />';
			}
		} else {
			if( prd_relations_del( $_POST['src'], $_POST['dst'], $_POST['type'] ) ){
				print '<success />';
			}else{
				print '<error message="'._('La suppression de la référence a échouée pour une raison inconnue.').'" />';
			}
		}
		
		break;
	case 'prd-parents-add':
		if( !isset($_POST['parent'],$_POST['child']) ) return;
		$_POST['parent'] = strtoupper2(trim($_POST['parent']));
		
		$parent = false;
		
		$cats = prd_products_categories_get_array( $_POST['child'] );
		if( is_array($cats) && sizeof($cats) ){
			$cat = $cats[0];
			$parents = prd_categories_parents_get_array( $cat );
			if( is_array($parents) && sizeof($parents) ){
				$cat = $parents[0];
			}

			$parent = prd_products_get_simple( isset($_POST['parent-id']) && is_numeric($_POST['parent-id']) && $_POST['parent-id'] ? $_POST['parent-id'] : 0, $_POST['parent'], false, $cat, true );
		}

		if( !$parent || !ria_mysql_num_rows($parent) ){
			$parent = prd_products_get_simple( isset($_POST['parent-id']) && is_numeric($_POST['parent-id']) && $_POST['parent-id'] ? $_POST['parent-id'] : 0, $_POST['parent'] );
		}

		if( !ria_mysql_num_rows($parent) ){
			print '<error message="'.sprintf(_('La référence %s n\'a pas été trouvée.'), $_POST['parent']).'" />';
		}else{
			$p = ria_mysql_fetch_array($parent);
			if( $p['id']==$_POST['child'] ){
				print '<error message="'._('Le produit ne peut pas être son propre enfant.').'" />';
			}elseif( prd_hierarchy_add( $p['id'], $_POST['child'] ) ){
				print '<success id="'.$p['id'].'" ref="'.$p['ref'].'" title="'.xmlentities($p['title']).'" publish="'.$p['publish'].'" publish_cat="'.$p['publish_cat'].'" is_sync="'.$p['is_sync'].'" />';
			}else{
				print '<error message="'.sprintf(_('L\'ajout de la référence %s a échoué pour une raison inconnue.'), $_POST['parent']).'" />';
			}
		}
		break;
	case 'prd-parents-del':
		if( !isset($_POST['prd'],$_POST['parents']) ) return;
		
		$is_error = false;
		$error_messages = array();
		foreach( $_POST['parents'] as $p ){
			if (!prd_hierarchy_del( $p, $_POST['prd'] ) ){
				$error_messages[] = sprintf(_('La suppression de la référence %s a échoué pour une raison inconnue.'), $p);
			}
		}
		
		if (count($error_messages)){
			print '<error message="'.implode("\n", $error_messages).'" />';
		} else {
			print '<success />';
		}
		
		break;
	case 'prd-childs-add':
		if( !isset($_POST['parent'],$_POST['child']) ) return;
		$_POST['child'] = strtoupper2(trim($_POST['child']));
		
		$child = false;
		
		$cats = prd_products_categories_get_array( $_POST['parent'] );
		if( is_array($cats) && sizeof($cats) ){
			$cat = $cats[0];
			$parents = prd_categories_parents_get_array( $cat );
			if( is_array($parents) && sizeof($parents) ){
				$cat = $parents[0];
			}

			$child = prd_products_get_simple( isset($_POST['child-id']) && is_numeric($_POST['child-id']) && $_POST['child-id'] ? $_POST['child-id'] : 0, $_POST['child'], false, $cat, true );
		}

		if( !$child || !ria_mysql_num_rows($child) ){
			$child = prd_products_get_simple( isset($_POST['child-id']) && is_numeric($_POST['child-id']) && $_POST['child-id'] ? $_POST['child-id'] : 0, $_POST['child'] );
		}
		

		if( !ria_mysql_num_rows($child) ){
			print '<error message="'.sprintf(_('La référence %s n\'a pas été trouvée.'), $_POST['child']).'" />';
		}else{
			$c = ria_mysql_fetch_array($child);
			if( $_POST['parent']==$c['id'] ){
				print '<error message="'._('Le produit ne peut pas être son propre enfant.').'" />';
			}elseif( prd_hierarchy_add( $_POST['parent'], $c['id'] ) ){
				print '<success id="'.$c['id'].'" ref="'.$c['ref'].'" title="'.xmlentities($c['name']).'" publish="'.$c['publish'].'" publish_cat="'.$c['publish_cat'].'" is_sync="'.$c['is_sync'].'" />';
			}else{
				print '<error message="'.sprintf(_('L\'ajout de la référence %s a échoué pour une raison inconnue.'), $_POST['child']).'" />';
			}
		}
		break;
	case 'prd-childs-del':
		if( !isset($_POST['prd'],$_POST['childs']) ) return;
		
		$is_error = false;
		$error_messages = array();
		foreach( $_POST['childs'] as $c ){
			if (!prd_hierarchy_del( $_POST['prd'], $c ) ){
				$error_messages[] = sprintf(_('La suppression de la référence %s a échoué pour une raison inconnue.'), $c);
			}
		}
		
		if (count($error_messages)){
			print '<error message="'.implode("\n", $error_messages).'" />';
		} else {
			print '<success />';
		}
		
		break;
	case 'opt-add':
		if( !isset( $_GET['nom']) ) return;
		
		if( !prd_options_add($_GET['nom']) ){
			print '<error message="'._('L\'ajout de l\'option a échoué pour une raison inconnue').'"/>';
		}else{
			$id = ria_mysql_insert_id();
			print '<success id="'.$id.'" title="'.$_GET['nom'].'" />';
		}
	
		break;
	case 'opt-del':
		if( !isset( $_GET['id']) ) return;
		
		if( !prd_options_del($_GET['id']) ){
			print '<error message="'._('La suppression de l\'option a échouée pour une raison inconnue').'"/>';
		}else{
			print '<success id="'.$_GET['id'].'"/>';
		}
		break;
	case 'opt-prd-add':
		if( !isset( $_GET['opt'], $_GET['ref']) ) return;
		
		$rprd = prd_products_get_simple( 0, $_GET['ref'] );
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$product = ria_mysql_fetch_array( $rprd );
		}else{
			print '<error message="'.sprintf(_('Impossible de charger la référence %s.'), $_GET['ref']).'"/>';
			return;
		}
		
		if( prd_options_products_exists($_GET['opt'], $product['id']) ){
			print '<error message="'.sprintf(_('La référence %s est déjà présente pour cette option'), $_GET['ref']).'"/>';
		}elseif( !prd_options_products_add( $_GET['opt'], $product['id'] ) ){
			print '<error message="'._('L\'ajout du produit dans l\'option a échoué pour une raison inconnue').'"/>';
		}else{
			print '<success>'."\n";
			if( $childs = prd_options_products_get($_GET['opt']) ){
				while( $child = ria_mysql_fetch_array($childs) ){
					print '<child id="'.$child['prd'].'">
								<ref><![CDATA['.htmlspecialchars($child['prd-ref']).']]></ref>
								<name><![CDATA['.htmlspecialchars($child['prd-name']).']]></name>
							</child>'."\n";
				}
			}
			print '</success>'."\n";
		}
		break;
	case 'opt-prd-list':
		if( !isset( $_GET['opt']) ) return;
		
		print '<success>'."\n";
		if( $childs = prd_options_products_get($_GET['opt']) ){
			while($child = ria_mysql_fetch_array($childs)){
				print '<child id="'.$child['prd'].'">
							<ref><![CDATA['.htmlspecialchars($child['prd-ref']).']]></ref>
							<name><![CDATA['.htmlspecialchars($child['prd-name']).']]></name>
						</child>'."\n";
			}
		}
		
		print '</success>'."\n";
		
		break;
	case 'opt-prd-del':
		if( !isset( $_GET['opt'], $_GET['prd'] ) ) return;
		
		if( !prd_options_products_del($_GET['opt'], $_GET['prd']) ){
			print '<error message="'._('La suppression du produit de l\'option a échoué pour une raison inconnue').'"/>';
		}else{
			print '<success id="'.$_GET['prd'].'"/>';
		}
		break;
	case 'prd-nomenclature-add':
		if( !isset( $_GET['opt'], $_GET['prd'], $_GET['qte'] ) ) return;
		
		if(!is_numeric($_GET['qte'])){
			print '<error message="'._('Veuillez vérifier la quantité').'"/>';
		}
		else if( !prd_nomenclatures_options_add($_GET['prd'], $_GET['opt'], $_GET['qte']) ){
			print '<error message="'._('L\'ajout de l\'option a échoué pour une raison inconnue').'"/>';
		}
		else{
			$temp = ria_mysql_fetch_array(prd_options_get($_GET['opt']));
			
			print '<success id="'.$_GET['opt'].'" name="'.$temp['name'].'" qte="'.$_GET['qte'].'"/>';
		}
		break;
	case 'prd-nomenclature-del':
		if( !isset( $_GET['opt'], $_GET['prd']) ) return;
		
		if(!prd_nomenclatures_options_del($_GET['prd'], $_GET['opt'])){
			print '<error message="'._('La suppression de l\'option a échouée pour une raison inconnue').'"/>';
		}
		else{
			print '<success id="'.$_GET['opt'].'"/>';
		}
		
		break;
	default:
		print '<error />';
}




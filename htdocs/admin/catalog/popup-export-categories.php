<?php

	/**	\file popup-export-categories.php
	 *	Ce fichier est utilisé pour l'exportation de l'arborescence des catégories. Il est affiché dans le back-office sous forme de popup.
	 * 	Il permet de paramétrer l'export (format, colonnes), l'export effectif étant réalisé par le fichier export-categories.php.
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG');

	if( !isset($_GET['export']) && !isset($_GET['cat'])){
		$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations.");
	}

    $default = array( 'title' );

    $cat = isset($_GET['cat']) && prd_categories_exists($_GET['cat']) ? $_GET['cat'] : 0;
    $exclude_cat_ids = isset($_GET['exclude_cat_ids']) ? $_GET['exclude_cat_ids'] : '';
    $link = 'popup-export-categories.php?cat='.$cat;
    
    if(isset($_POST['export'])){
        if( !isset($_POST['col-filter']) || !is_array($_POST['col-filter']) || !sizeof($_POST['col-filter']) ||
            !isset($_POST['fld-filter']) || !is_array($_POST['fld-filter']) || !sizeof($_POST['fld-filter']) ){
            $error = _("Veuillez sélectionner une ou plusieurs informations à inclure dans cet export");
        } else {
            $columns['cols'] = isset($_POST['col-filter']) && is_array($_POST['col-filter']) ? $_POST['col-filter'] : array();
    
            $catchilds = isset($_POST['catchilds']) ? true : false;
        }
    
        if( !isset($error)){
            $success = true;
        }
    }

    // Exécute un export catégories
    if( isset($_POST['export']) && !isset($error)){
        include('export-categories.php?cat='.$cat);
        exit;
    }

    $thumb = '';

    if (sizeof($_POST)){
        $image_adv_fields = fld_fields_get(0,0,0,FLD_TYPE_IMAGE,0,0,null,array(),false,array(),null,CLS_CATEGORY);
        $ar_images_fields = array();
        while($img_fld = ria_mysql_fetch_assoc($image_adv_fields)){
            $ar_images_fields[] = $img_fld;
        }
        $thumb = array();
        foreach($ar_images_fields as $key=>$image_field){
            $thumb[$image_field['id']] = isset($_POST['size-img'][$image_field['id']]) ? $_POST['size-img'][$image_field['id']] : '';
        }
    }
    
    
    define('ADMIN_PAGE_TITLE', _('Export des catégories') . ' - ' . _('Catalogue'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<div id="export">
        <?php 
            if( isset($g_error) ){ 
                print '<div class="error">'.nl2br( $g_error ).'</div>';
            } else {
                print '
                    <form action="'.$link.'" method="post">
                ';

                if( isset($error) ){
                    print '<div class="error">'.nl2br( $error ).'</div>';
                } elseif( isset($success) ){
                    print '<div class="success">'._('L\'enregistrement s\'est correctement déroulé.').'</div>';
                }
		?>
			<h2>
				<span style="float: left;"><?php print _('Gestion de l\'export')?></span>
				<div class="export-action">
					<input class="btn-action" type="submit" name="export" value="<?php print _('Télécharger')?>" />
				</div>
				<div class="clear"></div>
			</h2>
            <div class="check-catchilds">
				<input type="checkbox" name="for_excel" id="for_excel" value="1" />
				<label for="for_excel"><?php print _('Exporter pour Excel')?></label>
				<div class="clear"></div>
				<input type="checkbox" name="for_mac" id="for_mac" value="1" />
				<label for="for_mac"><?php print _('Exporter pour Excel (Mac)')?></label>
				<div class="clear"></div>
			</div>
			
			<div class="check-catchilds">
                <input type="checkbox" name="catchilds" id="catchilds" value="1" checked="checked" />
                <label for="catchilds"><?php print _('Télécharger avec l\'arborescence')?></label>
                <div class="clear"></div>
            </div>

			<span class="part-export">
				<?php print _('Informations :'); ?> 
				<a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
			</span>
			<div class="clear"></div>
			
			<?php
				$ar_cols = array(
                    'id' => _('Identifiant') ,
                    'title' => _('Désignation'),
                    'desc' => _('Description'),
                    'products' => _('Nbr de produits'),
                    'publish' => _('Publiée'),
                    'is_sync' => _('Synchronisée')
                );

                $adv_fields = fld_fields_get(0,0,0,0,0,0,null,array(),false,array(),null,CLS_CATEGORY);

                                				
				print '
					<div class="cols">
				';
				
				$i = 0;
				foreach( $ar_cols as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
                                        
                    print '
                        <div class="elems">
                            <input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
                            <label for="col-'.$key.'">'.htmlspecialchars( $name ).'</label>
                        </div>
                    ';
                }
                				
				print '
					</div>
                ';
            ?>
                <span class="part-export">
                    <?php print _('Informations complémentaires :'); ?> 
                    <a href="#" class="check-all-fld"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-fld"><?php print _('Décocher tout')?></a>
                </span>
                <div class="clear"></div>
            <?php
                
                print '
                    <div class="cols">
                ';
				
                $ar_fields = array();
                $ar_image_fields = array();

                while ($fld = ria_mysql_fetch_assoc($adv_fields)){
                    if ($fld['type_id'] == FLD_TYPE_IMAGE){
                        $ar_image_fields[$fld['id']] = $fld['name'];
                    } else {
                        $ar_fields[$fld['id']] = $fld['name'];
                    }
                }

				foreach( $ar_fields as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
                                        
                    print '
                        <div class="elems">
                            <input type="checkbox" '.$checked.' value="'.$name.'" id="fld-'.$key.'" name="fld-filter['.$key.']" class="fld-filter" />
                            <label for="fld-'.$key.'">'.htmlspecialchars( $name ).'</label>
                        </div>
                    ';
                }

                foreach( $ar_image_fields as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
                    $sizes='';    
                    if( trim($sizes)=='' ){
                        foreach( $config['img_sizes'] as $code=>$size ){
                            $config['img_sizes'][$code]['score'] = (int) $size['width'] * (int) $size['height'];
                        }
                        
                        $tsizes = array_msort( $config['img_sizes'], array('score'=>SORT_ASC, 'width'=>SORT_ASC) );
                        foreach( $tsizes as $s ){
                            $selected = '';
                            if( isset($thumb[$key]) && $thumb[$key]==$s['width'].'x'.$s['height'] ){
                                $selected = 'selected="selected"';
                            }
                            $sizes .= '<option '.$selected.' value="'.$s['width'].'x'.$s['height'].'">'.$s['width'].'x'.$s['height'].' '._('pixels').'</option>';
                        }
                    }

                    print '
                        <div class="elems">
                            <input type="checkbox" '.$checked.' value="'.$name.'" id="fld-'.$key.'" name="fld-filter['.$key.']" class="fld-filter image_field" />
                            <label for="fld-'.$key.'">'.htmlspecialchars( $name ).'</label>
                            <div class="size-images">
                                <label for="size-'.$key.'">'._('Taille').' : </label>
                                <select name="size-img['.$key.']" id="size-'.$key.'">.'.$sizes.'</select>
                            </div>
                        </div>
                    ';
                }
				
				print '
					</div>
                ';


                
			?>
			
			<div class="clear"></div>
			
			<div class="export-action">
				<h2></h2>
				<input class="btn-action" type="submit" name="export" value="<?php print _('Télécharger')?>" />
			</div>
		</form>
		<?php } ?>
	</div>
	<script src="/admin/js/jquery.min.js"></script>
	<script>
		$(document).ready(
			function(){
				$('.check-all-col').click(function(){
					$('.col-filter').attr('checked', 'checked');
					return false;
				});
				$('.uncheck-all-col').click(function(){
					$('.col-filter').removeAttr('checked');
					return false;
                });
                
                $('.check-all-fld').click(function(){
					$('.fld-filter').attr('checked', 'checked');
					return false;
				});
				$('.uncheck-all-fld').click(function(){
					$('.fld-filter').removeAttr('checked');
					return false;
				});
				
				$('input[name=export]').click(function(){
                    var cols = '';
                    var heads = '';
                    var img_size = '';
                    
					$('.col-filter:checked').each(function(){
						var p = $(this).attr('name').replace('col-filter[', '').replace(']', '');
						var v = $(this).val();
                        heads += ($.trim(heads) != '' ? '|' : '') + p;
                        cols += ($.trim(cols) != '' ? '|' : '') + v;
                    });

                    $('.fld-filter:checked').each(function(){
						var p = $(this).attr('name').replace('fld-filter[', '').replace(']', '');
						var v = $(this).val();
                        heads += ($.trim(heads) != '' ? '|' : '') + p;
                        cols += ($.trim(cols) != '' ? '|' : '') + v;
                        if( $(this).hasClass('image_field') ){
                            var select_name = $(this).parent().find('select').attr('name').replace('size-img[', '').replace(']', '');
							img_size += '&'+select_name+'=';
							img_size += $(this).parent().find('select').val();
						}
                    });

                    if(cols != ''){
                        var url = '/admin/catalog/export-categories.php?cat=<?php print $cat; ?>&cols=' + cols + '&heads=' + heads + img_size;

                        var exclude_cat_ids = "<?php print $exclude_cat_ids; ?>";

                        if (exclude_cat_ids != ''){
                            url += '&exclude_cat_ids=' + exclude_cat_ids;
                        }

                        if ($('[name=catchilds]:checked').length) {
                            url += '&catchilds=1';
                        }
                        if ($('[name=for_excel]:checked').length) {
						    url += '&for_excel=1';
                        }

                        if ($('[name=for_mac]:checked').length) {
                            url += '&for_mac=1';
                        }

                        $('body').append('<div class="popup_ria_back_load"></div>');
                        $('body').append('<div class="popup_ria_back_notice notice"><?php print _('Votre export est en cours de préparation, veuillez patienter...')?></div>');

                        $.ajax({
                            type 	: 'get',
                            url 	: url,
                            data 	: '',
                            async 	: true,
                            success : function(urlDownload){
                                $('.popup_ria_back_notice').html("<?php print('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href=\'#\' id=\'link-exports\'> Outils > Exports</a>.'); ?>");
                            }
                        });
                        
                        return false;
                    }
				});
			}
		).delegate(
			'#link-exports', 'click', function(){
				parent.window.location.href = "/admin/tools/exports/index.php";
				parent.hidePopup();
			}
		);
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	if( !isset($_GET['prd']) || !is_numeric($_GET['prd']) ){
		print _('Il manque des paramètres');
	}	
	
	$tab_add = $tab_media = $close = false; 

	if( !isset($_GET['tab']) ) $_GET['tab'] = 'media';
	
	switch( $_GET['tab'] ){
		case "add":
			$tab_add = true;
			break;
		default:
			$tab_media = true;
			break;
	}
	
	if( isset($_FILES['img1']) ){
	
		$img_names = array('img1','img2','img3');
		// permet l'upload d'une image sur un produit
		foreach( $img_names as $name ){
			if( isset($_FILES[$name]) && $_FILES[$name]['error']!=UPLOAD_ERR_NO_FILE ){
				if( !($id=prd_images_upload( $_GET['prd'], $name )) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image secondaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}	
		}
		$close = true;
		
	}
	
	if (isset($_POST['save'])) {
		
		if (! (isset($_POST['media']) && $_POST['media'])) $error = _('Vous devez sélectionner une image');
		else {
			if (! prd_images_add_existing($_GET['prd'], $_POST['media'])) $error = _('Une erreur inattendue s\'est produite lors de la mise à jour de l\'image.');
			else $success = _('L\'image a été ajoutée avec succès.');
		}
		
		if( isset($_POST['ajax']) && IS_AJAX){		
			if (isset($error)) echo '<div class="msg errors">' . $error . '</div>';
			if (isset($success)) echo '<div class="msg success">' . $success . '</div>';
			exit;
		}
	}
	
	define('ADMIN_PAGE_TITLE', _('Ajouter des images'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'tabs');
	require_once('admin/skin/header.inc.php');

	if( $tab_media ){
?>
		<form id="form" action="popup-image.php?prd=<?php print $_GET['prd'] ?>" method="post" enctype="multipart/form-data">
			<input id="media" name="media" type="hidden" />
			<input id="type" name="type" type="hidden" />
			<input type="hidden" name="images" />
			<?php
				if (isset($error)) echo '<div class="msg errors">' . $error . '</div>';
				if (isset($success)) echo '<div class="msg success">' . $success . '</div>';
			?>
			<div style="float: right">
				<input id="src" name="q" type="text" />
				<input id="search" name="search" type="submit" class="button" value="<?php print _('Rechercher')?>" />
			</div>
				<div class="clear"></div>
			<div id="mediatheque-container">
				<div id="nav">
					<ul>
						<li>
							<a id="type-all" class="racine dir all selected" href="#"><strong><?php print _('Médiathèques')?></strong></a>
							<ul>
								<li><a id="type-prd" class="dir prd" href="#"><?php print _('Produits')?></a></li>
								<li><a id="type-prd-cat" class="dir prd-cat" href="#"><?php print _('Catégories')?></a></li>
								<li><a id="type-str" class="dir str" href="#"><?php print _('Magasins')?></a></li>
								<li><a id="type-cms" class="dir cms" href="#"><?php print _('Contenu')?></a></li>
								<li><a id="type-news" class="dir news" href="#"><?php print _('Actualité')?></a></li>
							</ul>
						</li>
					</ul>
				</div>
				<div id="mediatheque"></div>
				<div class="clear"></div>
			</div>
			<div class="padt">
				<input name="save" type="submit" class="button" value="<?php print _('Ajouter')?>" />
			</div>
		</form>
		<script>
			$(document).ready(function() {
				$('#file').focus(function() {
					if (current) {
						current.removeClass('selected');
						current = null;
					}
					$('#mode_file').click();
				});
				
				var count = 0;
				var ajax;
				var mediatheque = $('#mediatheque');
				var h = mediatheque.height();
				
				var getId = function(img) {
					var id = (new RegExp('^.*-([0-9]+)$')).exec(img.attr('id'));
					if (! (id && id[1] !== undefined)) return ;
					return id[1];
				};
				
				var current;
				var bindImages = function(imgs) {
					imgs.find('img').each(function() {
						$(this).click(function() {
							var id = getId($(this));
							if (current) current.removeClass('selected');
							$(this).addClass('selected');
							current = $(this);
							$('#mode_mediatheque').click();
						});
					});
				}
				
				var doSearch = function() {
					if (ajax) ajax.abort();
					
					mediatheque.empty();
					
					count = 0;
					ajax = null;
					evalScroll();
				};
				
				var curType;
				$('#nav').find('a').each(function() {
					if (! curType) curType = $(this);
					$(this).click(function(e) {
						curType.removeClass('selected');
						$(this).addClass('selected');
						curType = $(this);
						$('#type').val((new RegExp('^type-(.*)$')).exec($(this).attr('id'))[1]);
						doSearch();
						return false;
					});
				});
				$('#type').val('all');
				
				var evalScroll = function() {
					if (ajax) return ;
					
					var scroll = mediatheque.scrollTop();
					var scrollMax = document.getElementById('mediatheque').scrollHeight;
					
					var error = function(e) {
						ajax = null;
						evalScroll();
					};
					
					if (scroll >= scrollMax - 2 * h) {
					
						ajax = $.ajax({
							'url'		:	'/admin/ajax/tinymce/ajax-images.php',
							'data'		:	$('#form').serialize() + '&size=medium&page=' + (count+1),
							'type'		:	'post',
							'dataType'	: 	'json',
							'success'	:	function(response) {
												if (! response) return error();
												
												var i, img;
												var t = $('<div></div>');
												var images = response.images || [];
												var thumb = response.thumb;
												
												for (i in images) {
													img = images[i];
													t.append('<div class="preview"><img id="img-' + img.id + '" src="/images/products/' + thumb.width + 'x' + thumb.height + '/' + img.id + '.' + thumb.format + '" alt="" width="' + thumb.width + '" height="' + thumb.height + '" /></div>');
												}
												bindImages(t);
												
												mediatheque.append(t);
												
												count++;
												
												if (images.length) {
													ajax = null;
													evalScroll();
												}
											},
							'error'		:	function(xhr, e) { return error(e); }
						});
						
					}
				};
				mediatheque.scroll(evalScroll);
				evalScroll();
				
				$('#form').submit(function() {
					var img = $('#mediatheque').find('img.selected');
					if (img.length) $('#media').val(getId(img));
					$.post($(this).attr('action'),'save=1&ajax=1&'+$(this).serialize(), function(msg){
						$('.msg').remove() ; 
						$('.tabscontent').prepend( msg );
						
						window.parent.refresh_img_list();
					});
					return false; 
				});
				
				$('#src').keydown(function(e) {
					if (e.which == 13) {
						doSearch();
						return false;
					}
				});
				$('#search').click(function(e) {
					doSearch();
					return false;
				});
			});
		</script>
		
	<?php } elseif( $tab_add ) { ?>
		
		<div class="dropzone"><?php print _('Déplacer les fichiers ici')?> <br/><?php print _('ou')?> <br/><input type="button" name="search" value="<?php print _('Parcourir')?>"/></div>
		<form action="popup-image.php?prd=<?php print $_GET['prd'] ?>&tab=add" enctype="multipart/form-data" id="hiddenupload" method="post">
			<input class="hidden" type="file" name="files[]" multiple="multiple" />
			<table>
				<caption><?php print _('Ajout d\'images')?></caption>
				<tfoot>
					<tr><td colspan="2">
						<input type="submit" value="<?php print _('Enregistrer')?>" name="saveimg">
					</td></tr>
				</tfoot>
				<tbody>
					<tr><th colspan="2"><?php print _('Images')?></th></tr>
					<tr>
						<td><label for="add"><?php print _('Ajouter une image :')?></label></td>
						<td><input type="file" style="width: auto;" name="img1"></td>
					</tr>
					<tr>
						<td><label for="add"><?php print _('Ajouter une image :')?></label></td>
						<td><input type="file" style="width: auto;" name="img2"></td>
					</tr>
					<tr>
						<td><label for="add"><?php print _('Ajouter une image :')?></label></td>
						<td><input type="file" style="width: auto;" name="img3"></td>
					</tr>
				</tbody>
			</table>
		</form>
		<div class="loadzone"></div>
		<script>
			 $(function(){
				<?php if( $close ){ ?>
					window.parent.refresh_img_list();
					window.parent.hidePopup();
				<?php } ?>
			});
		</script>	
		<!--[if !ie]>-->
		<script>
			$(function(){
				$.event.props.push('dataTransfer');
				$('.dropzone').bind( 'dragenter dragover', false).bind( 'drop', function( e ) {
					e.stopPropagation();
					e.preventDefault();
					$('.dropzone').hover();
					
					$.each( e.dataTransfer.files, function(index, file){
						upload_file( file );
					});
				}).click(function(){
					$('#hiddenupload [type=file][multiple]').click();
					return false;
				});
				$('[type=file]').change(function(e){
					$.each( e.target.files, function(index,file){
						upload_file( file );
					});
				});
			});
			
			var cpt=0;
			function upload_file( file ){
				var cpt_error=0;
				var data = new FormData();
				data.append('img', file);
				data.append('prd', <?php print $_GET['prd'] ?>);
				data.append('cpt', cpt);
				
				$('.loadzone').append('<div id="up-'+cpt+'" class="img-images">'
									+ '		<img class="loader" src="/admin/images/loader2.gif" height="" width="" alt="<?php print _('Chargement en cours...')?>"/>'
									+ '</div>');
				cpt_error = cpt; 
				$.ajax({
					url: '/admin/ajax/catalog/upload-img.php',
					data: data,
					cache: false,
					contentType: false,
					processData: false,
					type: 'POST',
					dataType:'json',
					success: function(json){
						if( json.error ) alert( json.error );
						else{
							$('.loadzone .msg').remove();
							$('.loadzone').prepend( '<div class="msg success"><p><?php print _('Les images ci-dessous ont été automatiquement ajoutées à la fiche produit.')?></p></div>' );
						}
						
						$('#up-'+json.cpt).html( json.html );
						
						window.parent.refresh_img_list();
					},
					error: function(){
						$('#up-'+cpt_error).html( '<?php print _('Erreur de transfert')?>' );
					}
				});

				cpt++;
			}
			</script>
		<!--<![endif]-->
		<!--[if lte IE 10]>
			<script>
				$(document).ready(function(){
					$('.dropzone').hide();
					$('#hiddenupload').show();
				});
			</script>
		<!--<![endif]-->
<?php 
	}

	require_once('admin/skin/footer.inc.php');
?>
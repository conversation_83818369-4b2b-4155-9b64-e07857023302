<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_DUPLICATE');

require_once('prd/relations.inc.php');

// Bouton Importer
$error = false;
$is_retour_post = isset($_POST['import']) && isset($_POST['dp_prd']);
if( $is_retour_post ){
    if( $_POST['dp_prd'] == null ){
        $error = _('Une erreur inattendue s\'est produite lors de la copie des données.').'<br>'._('Veuillez réessayer ou prendre contact avec l\'administrateur.');
    }else{
        $price = (isset($_POST['dp_price']) ? true : false);
        $fields = (isset($_POST['dp_fields']) ? true : false);
        $desc = (isset($_POST['dp_desc']) ? true : false);
		$images = (isset($_POST['dp_imgs']) ? true : false);
		$title = (isset($_POST['dp_title']) ? true : false);
		$classify = (isset($_POST['dp_classify']) ? true : false);
		$canonical = (isset($_POST['dp_canonical']) ? true : false);
		$parent = (isset($_POST['parent']) ? true : false);
		$child = (isset($_POST['dp_child']) ? true : false);

		$relation = array();
		if( isset($_POST['dp_relation']) ){
			foreach( $_POST['dp_relation'] as $id_relation => $val ){
				$relation[] = $id_relation;
			}
		}
        if( !prd_products_duplicate($_GET['prd'], $_POST['dp_prd'], $price, false, $fields, $desc, $images, $classify, $title, $canonical, $parent, $child, $relation) ){
            $error = _('Une erreur inattendue s\'est produite lors de la copie des données.').'<br>'._('Veuillez réessayer ou prendre contact avec l\'administrateur.');
        }
    }
}
define('ADMIN_PAGE_TITLE', _('Importer des données'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');
?>
<?php if( $is_retour_post && !$error ){ ?>
	<script>
		parent.window.location.reload();
	</script>
<?php } ?>
<div id="export">
	<h2><?php print _('Importer des données à partir d\'un autre produit')?></h2>
	<div class="msg">
		<?php if( $is_retour_post && $error){ ?>
			<div class="error">
				<?php echo $error; ?>
			</div>
		<?php }elseif($is_retour_post){ ?>
			<div class="success">
				<?php print _('L\'import s\'est correctement déroulé, vous serez redirigé prochainement.')?>
			</div>
		<?php } ?>
	</div>
	<form action="#" method="post">
	    <label for="dp_reference"><?php print _('Référence :'); ?></label>
	    <input type="text" size="15" value="" id="dp_reference" name="dp_reference"/>
	    <input type="submit" class="btn-action" value="<?php print _('Chercher')?>" onclick="return dp_submit();"/>
	</form>

	<form method="post" onsubmit="dp_loding( true )">
	    <div id="content">
	        <div id="dp_result" style="margin-top:10px;">

	        </div>

	        <div id="dp_choise" style="display: none;">
	        	<input type="hidden" name="dp_prd" value="" id="dp_prd"/>
		        <div class="part-export">
		        	<?php print _('Informations :'); ?>
					<a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
		        </div>

	        	<div class="elems">
	        		<input type="checkbox" class="chx" name="dp_desc" id="dp_desc"/>
	        		<label for="dp_desc"><?php print _('Les descriptions')?></label>
				</div>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_title" id="dp_title"/>
					<label for="dp_title"><?php print _('Les titres'); ?></label>
				</div>
	        	<div class="elems">
	        		<input type="checkbox" class="chx" name="dp_price" id="dp_price" />
	        		<label for="dp_price"><?php print _('Les prix')?></label>
	        	</div>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_parent" id="dp_parent"/>
					<label for="dp_parent"><?php print _('Les articles parents')?></label>
				</div>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_child" id="dp_child"/>
					<label for="dp_child"><?php print _('Les articles enfants')?></label>
				</div>
	        	<div class="elems">
	        		<input type="checkbox" class="chx" name="dp_fields" id="dp_fields"/>
	        		<label for="dp_fields"><?php print _('Les caractéristiques avancées')?></label>
	        	</div>
	        	<div class="elems">
	        		<input type="checkbox" class="chx" name="dp_imgs" id="dp_imgs"/>
	        		<label for="dp_imgs"><?php print _('Les images')?></label>
				</div>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_classify" id="dp_classify"/>
					<label for="dp_classify"><?php print _('Le classement')?></label>
				</div>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_canonical" id="dp_canonical"/>
					<label for="dp_canonical"><?php print _('Le lien canonique');?></label>
				</div><?php 
				$r_relation = prd_relations_types_get();
				while( $relation = ria_mysql_fetch_assoc($r_relation) ){ ?>
				<div class="elems">
					<input type="checkbox" class="chx" name="dp_relation[<?php print $relation['id']; ?>]" id="dp_<?php print $relation['id']; ?>"/>
					<label for="dp_<?php print $relation['id']; ?>"><?php print _('Les articles liés de type ').'"'.htmlspecialchars($relation['name_plural']).'"'; ?></label>
				</div>
				<?php }
				?>
	        </div>
	    </div>
	    <div class="export-action">
	    	<h2></h2>
	        <input class="dp_choise btn-action" type="submit" value="<?php print _('Importer')?>" name="import" onclick="return checkSubmit()"/>
	        <input type="button" class="btn-action" onclick="parent.hidePopup();" value="<?php print _('Annuler')?>"/>
	    </div>
	</form>
</div>

<script>
		function dp_submit() {
            var search = $('#dp_reference').val();

            if (search != '') {
            	removeMsgs();
            	dp_loding(true);
                $.ajax({
                    type: "POST",
                    url: 'duplicate.php',
                    data: 'search=' + search,
                    async: false,
                    success: function (msg) {
                    	dp_loding(false);
                        if ($(msg).find("noresult").attr('id') == 0) {
                            $('#dp_result').html( '<?php print _('Aucun résultat'); ?>' );
                            $('#dp_choise').hide();
                            $('.dp_choise').hide();
                        }
                        else {
                            $('#dp_prd').val($(msg).find("result").attr('id'));
                            $('#dp_result').html($(msg).find("result").text());
                        }

                    },
                    error: function (msg) {
                        alert("<?php print _('Une erreur inattendue est survenue lors de l\'enregistrement de votre demande.').'\n'._('Veuillez réessayer ou prendre contact avec nous pour résoudre cette erreur.')?>");
                    }
                });
            }else{
            	$('.msg').html($('<div>').addClass('notice').text('<?php print _('Veuillez saisir une recherche.')?>') );
            }

            return false;
        }
        function dp_loding( isLoading ){
        	if( isLoading ){
	        	parent.$('#popup_ria .content').prepend(
					  '<div class="popup_ria_back_load abs"></div>'
					+ '<div class="popup_ria_back_notice notice"><?php print _('Chargement en cours, veuillez patienter.')?></div>'
				);
        	}else{
        		parent.$('.popup_ria_back_load, .popup_ria_back_notice').remove();
        	}
        }
        function checkall() {
            if ($('#dp_choise input.chx').attr('checked')) {
                $('#dp_choise input.chx').removeAttr('checked');
            }
            else {
                $('#dp_choise input.chx').attr('checked', 'checked');
            }
        }
        function removeMsgs(){
        	$('.msg').html('');
        }

        function checkSubmit(){
        	if( $('#dp_prd').val() == '' ){
        		$('.msg').html($('<div>').addClass('notice').text('<?php print _('Veuillez sélectionner un produit.')?>') );
        		return false;
        	}
        	if( !$('#dp_choise div input.chx:checked').length ){
        		$('.msg').html($('<div>').addClass('notice').text('<?php print _('Veuillez cocher des informations à importer.')?>') );
        		return false;
        	}
        	return true;
        }

        function dup_alert(ref) {
            return confirm("<?php print _('Êtes-vous sûr de vouloir continuer ?').'\n'._('Cela va écraser les données présentes sur la référence')?> " + ref);
        }
        $('.check-all-col').on('click', function(e){
        	e.preventDefault();
        	$('#dp_choise input.chx').attr('checked', 'checked');
        });
        $('.uncheck-all-col').on('click', function(e){
        	e.preventDefault();
        	$('#dp_choise input.chx').removeAttr('checked');
        })
        $('#dp_result').on('click', 'a', function(e){
        	e.preventDefault();
        	var $anchor = $(this);
        	$('#dp_prd').val($anchor.data('id'));
        	$('#dp_result tr').each(function(k,tr){
        		$(tr).css({backgroundColor:"#fff"})
        	});
        	$anchor.parent().parent().css({backgroundColor:"#ececec"});
        	$('#dp_choise').show();
        })
</script>
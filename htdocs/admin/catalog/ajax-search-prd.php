<?php

	/**	\file ajax-search-prd.php
	 *	
	 *	Ce fichier fournit l'auto-complétion dans l'onglet Articles liés de la fiche produit, ainsi que
	 *	dans les promotions sur les produits.
	 *
	 * 	@warning un fichier identique existe dans le dossier admin/promotions/products.
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	//gu_if_authorized_else_403('_RGH_ADMIN_CATALOG');

	require_once( 'products.inc.php' );
	require_once( 'strings.inc.php' );

	// Le nombre de suggestions envoyées est limitée côté serveur pour des questions de performances
	define( 'MAX_SUGGEST', 10 );

	$results = array();
	$count = 0; // Nombre de suggestions envoyées
	if( isset($_GET['term']) && trim($_GET['term'])!='' ){
		$search = search3( 1, $_GET['term'], 1, 30, false, false, 6, array('prd') );
		if( $search && ria_mysql_num_rows($search) ){
			$last_prd_id = ''; // Dédoublonne les résultats (#5583)
			while( ($r_prd = ria_mysql_fetch_array($search)) && $count<=MAX_SUGGEST ){
				$prd = ria_mysql_fetch_array( prd_products_get_simple($r_prd['tag']) );
				$name = $prd['title']!='' ? $prd['title'] : $prd['name'];
				if( $name != '' ){
					if( $prd['id']!=$last_prd_id ){
						$results[] = array(
							'label' => $prd['ref'].' - '.htmlspecialchars($name),
							'value' => $prd['ref'].' - '.htmlspecialchars($name)
						);
						$count++;
						//$print .= $prd['ref'].' - '.htmlspecialchars($name).'|'.$prd['ref'].' - '.$name." \n";
						$last_prd_id = $prd['id'];
					}
				}
			}
		} else {
			$r_prd = prd_products_get_like($_GET['term']);
			if( $r_prd!=false && ria_mysql_num_rows($r_prd)>0 ){
				$last_prd_id = ''; // Dédoublonne les résultats (#5583)
				while( ($prd = ria_mysql_fetch_array($r_prd)) && $count<=MAX_SUGGEST ){
					$name = $prd['title']!='' ? $prd['title'] : $prd['name'];
					if( $name != '' ){
						if( $prd['id']!=$last_prd_id ){
							$results[] = array(
								'label' => $prd['ref'].' - '.htmlspecialchars($name),
								'value' => $prd['ref'].' - '.htmlspecialchars($name)
							);
							$count++;
							//$print .= $prd['ref'].' - '.htmlspecialchars($name).'|'.$prd['ref'].' - '.$name." \n";
							$last_prd_id = $prd['id'];
						}
					}
				}
			}
		}
	}

	print json_encode( $results );
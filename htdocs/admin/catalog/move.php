<?php

	/**	\file move.php
	 * 
	 * 	Ce fichier permet de déplacer des produits ou des catégories au sein du catalogue.
	 * 
	 * 	Les droits associés sont les suivants :
	 * 	- _RGH_ADMIN_CATALOG_CATEG_MOVE
	 *  - _RGH_ADMIN_CATALOG_PRODUCT_CLASSIFY
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_CLASSIFY');

	require_once('categories.inc.php');
	require_once('products.inc.php');

	unset( $error );
	$classify = isset($_POST['classify']) && $_POST['classify'];

	// Assure la compatibilité d'arguments entre index.php et move.php
	if( isset($_POST['prd']) ){
		$_POST['products'] = $_POST['prd'];
	}

	// Vérifie qu'il y a des produits ou des catégories à déplacer
	if( !isset($_POST['categs']) && !isset($_POST['products']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie que la catégorie est fournie
	if( isset($_POST['cat']) ){
		if( !prd_categories_exists($_POST['cat']) ){
			unset($_POST['cat']);
		}
	}

	// Vérifie qu'une destination est définie et correcte
	if( isset($_POST['dest']) && !is_numeric($_POST['dest']) && $_POST['dest']!='null' ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		if( isset($_POST['srccat']) && is_numeric($_POST['srccat']) ){
			if( $_POST['srccat']==0 ){
				header('Location: index.php?new=1');
			}else{
				header('Location: index.php?cat='.$_POST['srccat']);
			}
		}else{
			header('Location: index.php');
		}
		exit;
	}

	// Déplacement de catégories
	if( isset($_POST['move-default']) || isset($_POST['move-nourl']) || isset($_POST['move_keep_url']) ){
		if( !isset($_POST['dest']) ){
			$error = _("Veuillez cocher la case située devant la destination voulue.");
		}else{
			$error = array();
			if( $_POST['dest']=='null' ){
				$_POST['dest'] = NULL;
			}
			
			if( isset($_POST['categs']) ){
				foreach( $_POST['categs'] as $c ){
					if( $classify ){
						$_POST['products'] = array();

						$rp = prd_products_get_simple( 0, '', false, $c, true, false, false, false, array('childs'=>true) );
						if( $rp ){
							while( $p = ria_mysql_fetch_assoc($rp) ){
								$_POST['products'][] = $p['id'];
							}
						}
					}else{
						if(isset($_POST['move_keep_url'])){
							prd_categories_move( $c, $_POST['dest'], 1 );
						}else{
							prd_categories_move( $c, $_POST['dest'] );
						}

						if( isset($_POST['products']) ){
							unset( $_POST['products'] );
						}
					}
				}
			}

			if( isset($_POST['products']) && is_array($_POST['products']) && sizeof($_POST['products']) ){
				foreach( $_POST['products'] as $key=>$p ){
					// vérifier que le produit n'est pas déjà présent dans la catégorie
					$rcly = prd_classify_get( false, $p, $_POST['dest'] );
					if( $rcly && ria_mysql_num_rows($rcly) ){
						if( $rprd = prd_products_get_simple($p) ){
							$prd = ria_mysql_fetch_array( $rprd );
							$error[] = $prd['ref'].' - '.$prd['name'];
						}
					}else{
						if( $classify ){
							prd_products_add_to_cat( $p, $_POST['dest'] );
							unset($_POST['products'][$key]);
						}else{
							prd_products_categories_set( $p, $_POST['dest'], $_POST['srccat'], false, (isset($_POST['move-nourl']) ? false : true) );
							unset($_POST['products'][$key]);
						}
					}
				}
			}
			
			if( !sizeof($error) ){
				header('Location: index.php?cat='.$_POST['dest']);
				exit;
			}
		}
	}

	$title = _('Déplacer des catégories ou des produits');
	if( $classify ){
		$title = _("Classer des catégories ou des produits");
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title.' - '._('Catalogue'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $title ); ?></h2>

	<?php
		if( isset($error) ){
			if( is_array($error) ){
				if( sizeof($error) ){
					print '<div class="error">';
					if( sizeof($error)>1 ){
						print _('Les produits suivants n\'ont pas été déplacés car ils sont déjà présents dans la catégorie').' :';
					}else{
						print _('Le produit suivant n\'a pas été déplacé car il est déjà présent dans la catégorie').' :';
					}
					
					print '<ul>';
					foreach( $error as $e ){
						print '<li>'.htmlspecialchars( $e ).'</li>';
					}
					print '
						</ul>
					</div>';
				}
			}elseif( trim($error)!='' ){
				print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
			}
		}
	?>

	<?php if( $classify ){ ?>
		<p class="notice"><?php print _('Naviguez jusqu\'à l\'emplacement désiré à l\'aide des liens présents sur le nom de catégories. Pour déposer les catégories ou produits sélectionnés dans la catégorie souhaitée, cochez l\'option présente devant son nom puis cliquez sur le bouton &laquo; Classer &raquo; (les classements actuels des articles seront conservés).')?></p>
	<?php }else{ ?>
		<p class="notice"><?php print _('Naviguez jusqu\'à l\'emplacement désiré à l\'aide des liens présents sur le nom de catégories. Pour déposer les catégories ou produits sélectionnés dans la catégorie souhaitée, cochez l\'option présente devant son nom puis cliquez sur le bouton &laquo; Déplacer &raquo;.')?></p>
	<?php } 

?>
<form action="move.php" method="post">

	<?php

		// Assure le transfert de la variable srccat
		if( isset($_POST['srccat']) ){
			print '<input type="hidden" name="srccat" value="'.$_POST['srccat'].'" />';
		}

		// Assure le transfert de la variable classify
		if( isset($_POST['classify']) || isset($_GET['classify']) ){
			print '<input type="hidden" name="classify" value="1" />';
		}

		// Reprends ici les catégories à déplacer pour les passer en POST
		if( isset($_POST['categs']) && is_array($_POST['categs']) ){
			foreach( $_POST['categs'] as $cat ){
				print '<input type="hidden" name="categs[]" value="'.$cat.'" />';
			}
		}

		// Reprends ici les produits à déplacer pour les passer en POST
		if( isset($_POST['products']) && is_array($_POST['products']) ){
			foreach( $_POST['products'] as $prd ){
				print '<input type="hidden" name="products[]" value="'.$prd.'" />';
			}
		}

	?>

	<table id="js-choose-dest">
		<caption><?php print _('Choisissez un nouvel emplacement')?></caption>
		<thead class="thead-none">
			<tr>
				<th class="col-check">&nbsp;</th>
				<th class="col-m"><?php print _('Désignation')?></th>
				<th class="align-right"><?php print _('Produits publiés')?></th>
			</tr>
		</thead>
		<tbody>
			<?php

				if( !isset($_POST['cat']) ) $_POST['cat'] = 0;

				$categories = prd_categories_get(0,false,$_POST['cat']);
				if( !ria_mysql_num_rows($categories) ){
					print '<tr><td colspan="3">'._('Aucune sous-catégorie').'</td></tr>';
				}else{
					if( !isset($_POST['categs']) ){
						$disable = array();
					}else{
						$disable = $_POST['categs'];
						foreach( $_POST['categs'] as $c ){
							$p = prd_categories_hierarchy_get($c);
							while( $r = ria_mysql_fetch_array($p) ){
								$disable[] = $r['hry'];
							}
						}
					}
					while( $r = ria_mysql_fetch_array($categories) ){
						print '<tr><td class="col-check"><input type="radio" class="radio" name="dest" value="'.$r['id'].'" '.( in_array($r['id'],$disable) ? 'disabled="disabled"' : '' ).' /></td>';
						print '<td><a onclick="displayChildCategories('.$r['id'].')" title="'._('Afficher le contenu de cette catégorie').'">'.view_cat_is_sync($r).' '.htmlspecialchars($r['title']).'</a></td><td class="align-right" data-label="'._('Produits publiés :').'">'.$r['products'].'</td></tr>';
					}
				}

				// Bouton placer ici
				if( $_POST['cat']==0 ){
					print '<tr><td><input type="radio" class="radio" name="dest" value="null" id="dest-here" /></td><td colspan="2"><label for="dest-here">'._('Placer ici, à la racine du catalogue').'</label></td></tr>';
				}else{
					$cat = ria_mysql_fetch_array(prd_categories_get($_POST['cat']));
					print '<tr><td><input type="radio" class="radio" name="dest" value="'.$_POST['cat'].'" id="dest-here" /></td><td colspan="2"><label for="dest-here">'.sprintf( _('Placer ici, dans la catégorie %s'), htmlspecialchars($cat['title']) ).'</label></td></tr>';
				}

			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3">
					<?php 
					// Bouton retour à la catégorie parente
					if(isset($_POST['cat']) && $_POST['cat']>0){
						$cat = prd_categories_get ($_POST['cat']);
						if(ria_mysql_num_rows($cat)){
							$cat = ria_mysql_fetch_array($cat);
						}
						print '<input type="button" class="btn-move" name="back" value="'._('Revenir à la catégorie parente').'" onclick="window.location.href=\'move.php?'.($cat['parent_id'] != '' ? 'cat='.$cat['parent_id'] : '').'\'" />';
					}
					?>
						<input type="submit" name="move-default" value="<?php print $classify ? _('Classer') : _('Déplacer'); ?>" onclick="return validForm(this.form)"/>
					<?php if(!isset($_POST['products'])){ ?>	
						<input type="submit" name="move_keep_url" value="<?php print _('Déplacer (conserver les urls)')?>" onclick="return validForm(this.form)"/>
					<?php }
					if( !$classify && isset($_POST['products']) && is_array($_POST['products']) ){ ?>
						<input type="submit" name="move-nourl" value="<?php print _('Déplacer (conserver les urls)')?>" onclick="return validForm(this.form)" title="<?php print _('Le produit sera déplacé, mais ses urls seront conservées. Cette action permet de limiter l\'impacte qu\'aura le déplacement d\'une fiche produit sur les moteurs de recherche.')?>" />
					<?php } ?>
					<input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<?php
	// Affiche la liste des catégories qui seront déplacées
	if( isset($_POST['categs']) ){
		print '	<p>'._('Les catégories suivantes seront déplacées :').'</p>
				<ul class="list-to-move no-style">';
		foreach( $_POST['categs'] as $c ){
			if( $rcat = prd_categories_get($c) ){
				$cat = ria_mysql_fetch_array($rcat);
				print '<li>'.view_cat_is_sync($cat).' '.htmlspecialchars($cat['title']).'</li>';
			}
		}
		print '</ul>';
	}else{ // Affiche la liste des produits qui seront déplacés
		if( isset($_POST['products']) ){
			print '	<p>'.sprintf(_('Les produits suivants seront %s'), $classify ? _('classés') : _('déplacés') ).' :</p>
					<ul class="list-to-move no-style">';
			foreach( $_POST['products'] as $p ){
				if( $rprd = prd_products_get_simple($p) ){
					$prd = ria_mysql_fetch_array($rprd);
					print '<li>'.view_prd_is_sync($prd).' '.htmlspecialchars( $prd['ref'].' '.$prd['name'] ).'</li>';
				}
			}
			print '</ul>';
		}
	}
?>

	<script>
	<?php

		// Expose la liste des catégories désactivées à Javascript
		$disable = array();

		// On peut pas déplacer vers l'emplacement actuel
		if( isset($_POST['srccat']) ){
			$disable[] = $_POST['srccat'];
		}

		if( isset($_POST['categs']) ){
			foreach( $_POST['categs'] as $c ){
				$disable[] = $c;
				$p = prd_categories_hierarchy_get($c);
				while( $r = ria_mysql_fetch_array($p) ){
					$disable[] = $r['hry'];
				}
			}
		}

		print 'const disabledCategories = ['.implode( ',', $disable ).'];'
		
	?>

	function validForm(frm){
		// Vérifie qu'une destination à été sélectionnée
		for( var i=0; i<frm.elements.length; i++ ){
			if( frm.elements[i].name=='dest' ){
				if( frm.elements[i].checked ){
					return true;
				}
			}
		}
		alert("<?php print _('Veuillez cocher la case située devant la destination voulue.')?>");
		return false;
	}
	</script>

<?php
	require_once('admin/skin/footer.inc.php');
<?php

	/**	\file popup-duplicate-parent-to-child.php
	 *
	 * 	Cette popup permet la recopie des données d'un produit parent sur ses enfants. Elle fait gagner un temps
	 * 	précieux aux utilisateurs.
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');


define('ADMIN_PAGE_TITLE', _('Recopier les informations du parent sur les enfants'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');

$error = false;

// Bouton copier
$is_retour_post = isset($_POST['copy']);
if ($is_retour_post) {
    $_GET['prd'] = $_POST['prd'];

    // Récupère les articles enfants du produit
    $childs = prd_products_get_simple(0, '', false, 0, false, false, false, false, array('parent' => $_GET['prd'], 'childs' => true));
    if( !$childs && !ria_mysql_num_rows($childs) ){
        $error = _('Une erreur inattendue est survenue lors de la copie des informations sur les articles enfants.');
    }

    if( !isset($_POST['dp']) || !is_array($_POST['dp']) || !count($_POST['dp']) ){
        $error = _('Veuillez cocher au moins une case.');
    }

    if( !$error ){

        // Charge les informations du produit parent
        $parent_prd = ria_mysql_fetch_array( prd_products_get_simple($_GET['prd']) );

        // Charge les conditionnements du parent
        if( array_key_exists('col', $_POST['dp']) ){
            $rcol = prd_colisage_classify_get( 0, $_GET['prd'] );
            $colisages = array();
            while( $c = ria_mysql_fetch_assoc($rcol) ){
                $colisages[] = $c;
            }
        }

        // Charge les titre en langue étrangère du produit parent
        if( array_key_exists('title', $_POST['dp']) ){
            foreach( $config['i18n_lng_used'] as $lang ){
                if( $lang == $config['i18n_lng'] ){
                    continue;
                }
                $fld_title_parent = $parent_prd['is_sync'] ? _FLD_PRD_TITLE : _FLD_PRD_NAME;
                $parent_prd_title[$lang] = fld_object_values_get( $_GET['prd'], $fld_title_parent, $lang, false, true, false, true );
            }
        }

		// Charge les descriptions courte en langue étrangère du produit parent
        if( array_key_exists('short_desc', $_POST['dp']) ){
			$parent_prd_short_desc = array();
            foreach( $config['i18n_lng_used'] as $lang ){
                if( $lang == $config['i18n_lng'] ){
                    continue;
                }
                $parent_prd_short_desc[$lang] = fld_object_values_get( $_GET['prd'], _FLD_PRD_DESC, $lang, false, true, false, true );
            }
        }

		// Charge les descriptions longues en langue étrangère du produit parent
        if( array_key_exists('long_desc', $_POST['dp']) ){
			$parent_prd_long_desc = array();
            foreach( $config['i18n_lng_used'] as $lang ){
                if( $lang == $config['i18n_lng'] ){
                    continue;
                }
                $parent_prd_long_desc[$lang] = fld_object_values_get( $_GET['prd'], _FLD_PRD_DESC_LG, $lang, false, true, false, true );
            }
        }

        // Charge le classement du produit parent
        if( array_key_exists('classify', $_POST['dp']) ){
            $cat = prd_products_categories_get( $_GET['prd'] );
            $parent_classify = array();
            while( $c = ria_mysql_fetch_array($cat) ){
                $parent_classify[] = $c['cat'];
            }

        }

        // Charge le lien canonique du produit parent
        if( array_key_exists('canonical', $_POST['dp']) ){
            $parent_canonical = prd_classify_get_canonical_cat( $_GET['prd'] );
        }

        while( $child = ria_mysql_fetch_array($childs) ){

            // Recopie les conditionnements vers les enfants
            if( array_key_exists('col', $_POST['dp']) ){
                foreach( $colisages as $col ){
                    $ref = trim($col['cly_prd_ref']) != '' ? $col['cly_prd_ref'] : null;
                    $barcode = trim($col['cly_prd_barcode']) != '' ? $col['cly_prd_barcode'] : null;
                    if( !prd_colisage_classify_add($col['col_id'], $child['id'], $ref, $barcode, $col['is_default']) ){
                        $error = _('Une erreur inattendue est survenue lors de la copie des conditionnements sur les articles enfants.');
                    }
                }
            }

            // Recopie le titre vers les enfants
            if( array_key_exists('title', $_POST['dp']) ){
                if( $child['is_sync'] ){
                    prd_products_update_title( $child['id'], $parent_prd['title'] );
                }else{
                    prd_products_update_name( $child['id'], $parent_prd['title'] );
                }

                // Recopie le titre en langue étrangère
                $fld_title = $child['is_sync'] ? _FLD_PRD_TITLE : _FLD_PRD_NAME;
                foreach( $parent_prd_title as $lang => $title ){
                    if( !fld_object_values_set($child['id'], $fld_title, $title, $lang) ){
                        $error = _("Une erreur est survenue lors de la copie des titres sur les produits enfants.");
                    }
                }
            }

            // Recopie la description courte vers les enfants
            if( array_key_exists('short_desc', $_POST['dp']) ){
                if( !prd_products_update_desc( $child['id'], $parent_prd['desc']) ){
                    $error = _("Une erreur est survenue lors de la copie de la description courtes sur les produits enfants.");
                }

                // Recopie la description courte en langue étrangère
                foreach( $parent_prd_short_desc as $lang => $short_desc ){
                    if( !fld_object_values_set($child['id'], _FLD_PRD_DESC, $short_desc, $lang) ){
                        $error = _("Une erreur est survenue lors de la copie de description courte sur les produits enfants.");
                    }
                }
            }

            // Recopie la description longue vers les enfants
            if( array_key_exists('long_desc', $_POST['dp']) ){
                if( !prd_products_update_desc_long( $child['id'], $parent_prd['desc-long']) ){
                    $error = _("Une erreur est survenue lors de la copie de la description longue sur les produits enfants.");
                }

                // Recopie la description longue en lange étrangère
                foreach( $parent_prd_long_desc as $lang => $long_desc ){
                    if( !fld_object_values_set($child['id'], _FLD_PRD_DESC_LG, $long_desc, $lang) ){
                        $error = _("Une erreur est survenue lors de la copie de la description longue sur les produits enfants.");
                    }
                }
            }

            // Recopie l'image principal vers les enfants
            if( array_key_exists('main_img', $_POST['dp']) ){
                if( !prd_images_main_add_existing($child['id'], $parent_prd['img_id']) ){
                    $error = _("Une erreur est survenue lors de la copie de l'image principal.");
                }
            }

            // Recopie le classement vers les enfants
            if( array_key_exists('classify', $_POST['dp']) ){
                if( sizeof($parent_classify) ){
                    if( !prd_products_categories_set($child['id'], $parent_classify) ){
                        $error = _("Une erreur est survenue lors de la copie du classement.");
                    }
                }
            }

            // Recopie le lien canonique vers les enfants
            if( array_key_exists('canonical', $_POST['dp']) ){
                if( !prd_classify_exists($parent_canonical, $child['id']) ){
                    if( !prd_products_categories_set($child['id'], $parent_canonical) ){
                        $error = _('Une erreur inattendue est survenue lors de la copie du lien canonique.');
                    }
                }
                if( !prd_classify_set_canonical($child['id'], $parent_canonical) ){
                    $error = _('Une erreur inattendue est survenue lors de la copie du lien canonique.');
                }
            }
        }

        // Recopie les relations vers les enfants
        if( array_key_exists('rel', $_POST['dp']) ){
            if( !prd_relations_copy_to_childs($_GET['prd']) ){
                $error = _('Une erreur inattendue est survenue lors de la copie des relations sur les articles enfants.');
            }
        }

        // Recopie les images vers les enfants
        if( array_key_exists('img', $_POST['dp']) ){
            if( !prd_images_copy_to_childs($_GET['prd']) ){
                $error = _('Une erreur inattendue est survenue lors de la copie des images sur les articles enfants.');
            }
        }
    }
}

?>
<form method="post">
    <div id="export">
        <h2><?php print _('Recopier les informations du parent sur les enfants')?></h2>
        <div class="msg">
		<?php if( $is_retour_post && $error){ ?>
			<div class="error">
				<?php echo $error; ?>
			</div>
		<?php }elseif($is_retour_post){ ?>
			<div class="success">
				<?php print _('La copie s\'est correctement déroulé.')?>
			</div>
		<?php } ?>
	    </div>
        <div id="dp_choise">
            <input type="hidden" name="prd" value="<?php print $_GET['prd'] ?>" id="dp_prd"/>
            <div class="part-export">
                <?php print _('Informations :'); ?>
                <a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[col]" id="dp_col"/>
                <label for="dp_col"><?php print _('Les conditionnements')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[rel]" id="dp_rel"/>
                <label for="dp_rel"><?php print _('Les relations')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[title]" id="dp_title"/>
                <label for="dp_title"><?php print _('Le titre')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[short_desc]" id="dp_short_desc"/>
                <label for="dp_short_desc"><?php print _('La description courte')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[long_desc]" id="dp_long_desc"/>
                <label for="dp_long_desc"><?php print _('La description longue')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[img]" id="dp_img"/>
                <label for="dp_img"><?php print _('Toutes les images (principale et secondaires)')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[main_img]" id="dp_main_img"/>
                <label for="dp_main_img"><?php print _('L\'image principale uniquement')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[classify]" id="dp_classify"/>
                <label for="dp_classify"><?php print _('Les classements')?></label>
            </div>

            <div class="elems">
                <input type="checkbox" class="chx" name="dp[canonical]" id="dp_canonical"/>
                <label for="dp_canonical"><?php print _('Le lien canonique')?></label>
            </div>

            <div class="export-action">
                <h2></h2>
                <input class="dp_choise btn-action" type="submit" value="<?php print _('Copier')?>" name="copy"/>
                <input type="button" class="btn-action" onclick="parent.hidePopup();" value="<?php print _('Annuler')?>"/>
            </div>
        </div>
    </div>
</form>

<script>
    // check all
    $('.check-all-col').on('click', function(e){
       	e.preventDefault();
       	$('#dp_choise input.chx').attr('checked', 'checked');
    });

    // uncheck all
    $('.uncheck-all-col').on('click', function(e){
        e.preventDefault();
        $('#dp_choise input.chx').removeAttr('checked');
    })

    // Affiche un bandeau gris le temps de l'enregistrement de la copie
    $('[name="copy"]').on('click', function(){
        $('.message-ajax-opacity').html(prdCopyWaiting);
        $('.load-ajax-opacity, .message-ajax-opacity').show();
    });
</script>

<?php
    require_once('admin/skin/footer.inc.php');
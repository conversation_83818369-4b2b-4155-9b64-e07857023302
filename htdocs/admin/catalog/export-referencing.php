<?php
	/** \file export-referencing.php
	 * 	Ce fichier gère l'export du référence des produits.
	 * 	$_GET['cat'] peut être fournie à l'appel pour n'exporter les produits que de cette catégorie (sous catégorie inclues)
	 */
	
	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');


	$fields = array(
		_('Identifiant du produit'),
		_('Référence du produit'),
		_('Désignation du produit'),
		_('Classification'),
	);

	foreach($config['i18n_lng_used'] as $key => $value){
		$value = strtoupper($value);
		
		$fields = array_merge($fields, array(
			_('Titre automatique').' ('.$value.')' ,
			_('Titre personnalisé').' ('.$value.')' ,
			_('Description automatique').' ('.$value.')' ,
			_('Description personnalisée').' ('.$value.')' ,
			_('Mots-clés automatique').' ('.$value.')' ,
			_('Mots-clés personnalisé').' ('.$value.')'
		));
	}

	$rows[] = $fields;
	if(isset($_GET['cat'])){
		$cats = prd_categories_childs_get_array($_GET['cat']);
		$cats[] = $_GET['cat'];

	}else{
		$cats = array();
		$rCategories =  prd_categories_get_all();
		while($categorie = ria_mysql_fetch_assoc($rCategories)){
				$cats[] = $categorie['id'];
		}
	}

	if($cats[0] == ""){
		unset($cats[0]);
	}

	foreach($cats as $key => $cat){
		$products  = prd_products_get(0, '', 0, false, $cat);
		
		while($p = ria_mysql_fetch_assoc($products)){
			$data_row = array();
			$classify = prd_classify_get(false, $p['id'], $cat);
			$c = ria_mysql_fetch_assoc($classify);

			$rparent = prd_categories_parents_get($cat);
		    $url_classify = '';
            if ($rparent && ria_mysql_num_rows($rparent)) {
                while ($parent = ria_mysql_fetch_array($rparent))
                    $url_classify .= ($url_classify != '' ? ' > ' : '') . $parent['title'];
            }
            $url_classify .= ($url_classify != '' ? ' > ' : '') . prd_categories_get_name($cat);

			$data_row[] = $p['id'];
			$data_row[] = $p['ref'];
			$data_row[] = $p['name'];
			$data_row[] = $url_classify;
			
			foreach($config['i18n_lng_used'] as $key => $value){
				i18n::setLang($value);
				$data_row[] = page_obj_title(CLS_PRODUCT, array($p['id'], $cat), false);
				$data_row[] = str_replace("\n", " ",$c['tag_title']);
				$data_row[] = page_obj_desc(CLS_PRODUCT, array($p['id'], $cat), false);
				$data_row[] = str_replace("\n", " ",$c['tag_desc']);
				$data_row[] = page_obj_key(CLS_PRODUCT, array($p['id'], $cat), false);
				$data_row[] = str_replace("\n", " ",$c['keywords']);
			}
			
			i18n::setLang( $config['i18n_lng'] );
			$rows[] =$data_row ;
		}
	}
	

	$file_csv = $config['doc_dir'].'/export-referencing-'.$_SESSION['usr_id'].'.csv';
	$handle = fopen($file_csv, 'w');
	foreach($rows as $row){
		fputcsv($handle, $row, ";");
	}

	fclose($handle);
	exit;
<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_NOMENCLATURE');

	require_once('prd/nomenclatures.inc.php');

	unset($error);
	
	if(!isset($_GET['opt'])){
		header('Location: nomenclature.php');
		exit;
	}
	
	$ropt = prd_options_get( $_GET['opt'] );
	if( !$ropt || !ria_mysql_num_rows($ropt) ){
		header('Location: nomenclature.php');
		exit;
	}
	
	$opt = ria_mysql_fetch_array( $ropt );
	
	if( isset($_POST['back']) ){
		header('Location: nomenclature.php');
		exit;
	}
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	if( $lng != $config['i18n_lng'] ){
		$opt = i18n::getTranslation( CLS_PRD_NOM_OPTION, $opt, true, $lng );
	}

	//Enregistrement de l'option
	if( isset($_POST['save']) && isset($_POST['name']) ){
		if( trim($lng) == '' || $lng == $config['i18n_lng'] ){
			if( !prd_options_upd( $_GET['opt'], $_POST['name'] ) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}else{
			$values = array(
				_FLD_NMT_OPT_NAME 		=> $_POST['name']
			);

			// Traduction de l'option
			if( !fld_translates_add($opt['id'], $_GET['lng'], $values) ){
				$error = _("Une erreur inattendue s'est produite lors de la traduction de cette option.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
		}

		if( !isset($error) ){
			header('Location: edit-nomenclature.php?opt='.$opt['id'].( trim($lng) != '' && $lng != $config['i18n_lng'] ? '&lng='.$lng : '' ));
			exit;
		}
	}
	
	//enregistrement d'une categ
	if( isset($_POST['add']) && isset($_POST['alt-cat-id']) && is_numeric($_POST['alt-cat-id']) && $_POST['alt-cat-id']>0 ){
		if( $prds = prd_products_get_simple( 0, '', true, $_POST['alt-cat-id'], false, false, false, false, array('childs'=>true) ) ){
			while( $prd = ria_mysql_fetch_array($prds) ){
				if( !prd_options_products_add($_GET['opt'],$prd['id']) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					break;
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Nomenclatures variables') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Nomenclatures variables'); print ' - '.$opt['name']; ?></h2>
	
	<?php
		// Affiche le menu de langue
		print view_translate_menu( '/admin/catalog/edit-nomenclature.php?opt='.$opt['id'], $lng );

		if(isset($error)){
			print '<div class="error">'.$error.'</div>';
		}
	?>
	
	<form action="/admin/catalog/edit-nomenclature.php?opt=<?php print $opt['id']; ?><?php print $lng != $config['i18n_lng'] ? '&amp;lng='.$lng : ''; ?>" method="post">
	<table id="table-propriete-emplacement">
		<caption><?php print _('Propriété de l\'emplacement')?></caption>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="back" value="<?php print _('Retour')?>"/>
					<input type="submit" name="save" value="<?php print _('Enregistrer')?>"/>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td id="thname"><label for="name"><?php print _('Nom de l\'emplacement :')?></label></td>
				<td headers="thname" id="td-input-name"><input type="text" name="name" id="name" value="<?php print $opt['name']; ?>"/></td>
			</tr>
		</tbody>
	</table>
	</form>
	
	<table id="table-produits-utilisables">
		<caption><?php print _('Liste des produits utilisables dans cet emplacement')?></caption>
		<col width="150"/>
		<col width="330"/>
		<tfoot>
			<tr>
				<td id="td-produits-1" class="align-left">	
					<form action="/admin/catalog/nomenclature.php" method="post" onsubmit="return opt_prd_del()">
						<input type="submit" class="btn-del" name="del" value="<?php print _('Supprimer')?>"/>
					</form>
				</td>
				<td id="td-produits-2" class="align-right">
				
					<form action="/admin/catalog/nomenclature.php" method="post" onsubmit="return opt_prd_add()">
						<input type="hidden" id="opt_prd" name="opt_prd" value="<?php print $_GET['opt'];?>"/>
												
						<label for="prd_ref"><?php print _('Référence :'); ?></label>
						<input type="text" class="ref" name="prd_ref" id="prd_ref" value=""/>
						<input type="submit" class="button" name="add" value="<?php print _('Ajouter')?>"/>
						<br/>
					</form>
						
					<form action="/admin/catalog/edit-nomenclature.php?opt=<?php print $_GET['opt']; ?>" method="post">
						<label for="prd_cat"><?php print _('Catégorie :'); ?></label>
						<input type="hidden" name="alt-cat-id" id="alt-cat-id" value=""/>
						<input type="hidden" name="alt-cat-parent-id" id="alt-cat-parent-id" value=""/>
						<input type="text" class="ref" name="alt-cat-name" id="alt-cat-name" value=""/>
						<input class="button" type="button" onclick="cat_choose(this.form)" value="<?php print _('Parcourir')?>"/>
						<input type="submit" class="button" name="add" value="<?php print _('Ajouter')?>"/>
						<br/>
						
					</form>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td colspan="2">
					<select name="opt_child[]" id="opt_child" multiple="multiple" size="20">
						<option><?php print _('Aucun pour le moment')?></option>
						<?php
							$opts = prd_options_products_get($_GET['opt']);
							while( $p = ria_mysql_fetch_array($opts) ){
								print '<option value="'.$p['prd'].'">'.$p['prd-ref'].' - '.htmlspecialchars($p['prd-name']).'</option>';
							}
						?>
					</select>
				</td>
			</tr>
		</tbody>
	</table>

	<script>
		function cat_choose(frm){
			var url = 'popup-categories.php';
			if( $("#alt-cat-id").val() ){
				url += '?cat=' + $("#alt-cat-id").val();
				url += '&parent=' + $("#alt-cat-parent-id").val();
			}
			displayPopup('<?php print _('Choisir une catégorie')?>', '', url);	
			return false;
		}
		function updateCat( id, idParent, catname){
			$("#alt-cat-id").val(id);
			$("#alt-cat-parent-id").val(idParent);
			$("#alt-cat-name").val(catname);
			hidePopup();
		}
		function close_cat_popup(){
			hidePopup();
		}
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
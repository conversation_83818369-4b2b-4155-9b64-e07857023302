<?php

	/**	\file export-products.php
	 *	Ce fichier exporte une liste de produits en fonction de critères choisis par l'utilisateur dans la popup
	 *	popup-export-products.php.
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once('exports.inc.php');

	// Récupère les paramètres d'export
	$cat = 0;
	if( isset($_GET['cat']) && is_numeric($_GET['cat']) ){
		$cat = (int) $_GET['cat'];
	}
	$brand = isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd']>0 ? $_GET['brd'] : 0;

	$cols = isset($_GET['cols']) && trim($_GET['cols']) != '' ? explode('|', $_GET['cols']) : false;
	$flds = isset($_GET['flds']) && trim($_GET['flds']) != '' ? explode('|', $_GET['flds']) : false;
	
	$no_html = isset($_GET['export-no-html']);

	if (is_array($cols) && count($cols)) {
		$cols = array_combine( $cols, $cols );
	}
	
	if (is_array($flds) && count($flds)) {
		$flds = array_combine( $flds, $flds );
	}

	$catchilds = isset($_GET['catchilds']) && $_GET['catchilds'] ? true : false;
	$childonly = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
	if (isset($_GET['childonly']) && $_GET['childonly']) {
		$childonly= true;
	}

	$lngs = isset($_GET['lng']) ? $_GET['lng'] : array('fr');
	$for_excel = isset($_GET['for_excel']) && $_GET['for_excel'] ? true : false;
	$for_mac = isset($_GET['for_mac']) && $_GET['for_mac'] ? true : false;
	
	$thumb = array( 'main'=>$config['img_sizes']['high'], 'second'=> $config['img_sizes']['high'] );

	$ar_img = array( 'main'=>'img_main', 'second'=>'img_second' );
	foreach( $ar_img as $key=>$type ){
		if( !isset($_GET['size-img'][$type]) ){
			continue;
		}

		$sizes = preg_split( '/x/i', $_GET['size-img'][$type] );
		$sizes = !is_array($sizes) || sizeof($sizes)!=2 ? array(260,260) : $sizes;

		foreach( $config['img_sizes'] as $is ){
			if( $is['width']==$sizes[0] && $is['height']==$sizes[1] ){
				$thumb[$key] = $is;
			}
		}
	}
	$file_name = 'export-catalog-'.$_SESSION['usr_id'].'.csv';
	$file_csv = $config['doc_dir'].'/'.$file_name;

	$exp_id = exp_exports_add( CLS_PRODUCT, $file_csv, $file_name );
	try{
		// Ajoute l'import dans la file d'attente
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
			'wst_id' => $config['wst_id'],
			'cls_id' => CLS_PRODUCT,
			'exp_id' => $exp_id,
			'cat' => $cat,
			'catchilds' => $catchilds,
			'cols' => $cols,
			'flds' => $flds,
			'thumb' => $thumb,
			'childonly' => $childonly,
			'no_html' => $no_html,
			'brand' => $brand,
			'lngs' => $lngs,
			'for_excel' => $for_excel,
			'for_mac' => $for_mac
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

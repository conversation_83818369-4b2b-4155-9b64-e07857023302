<?php

	/**	\file popup-colisage.php
	 *	Cette popup affiche et permet la modification (ajout/suppression) des colisages disponibles
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('prd/colisage.inc.php');
	require_once('products.inc.php');
	
	if( !isset($_GET['prd']) || !prd_products_exists($_GET['prd']) ){
		$_GET['prd'] = 0;
	}
	
	define('ADMIN_PAGE_TITLE', _('Conditionnements') . ' - ' . _('Catalogue'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	
	require_once('admin/skin/header.inc.php');
?>
	<form action="#" method="post" id="frmcolisage">
		<table>
			<caption><?php print _('Conditionnements')?></caption>
			<thead>
				<tr>
					<th id="col-del" class="col-check"></th>
					<th id="col-name"><?php print _('Désignation')?></th>
					<th id="col-qte" class="col75px align-right"><?php print _('Quantité')?></th>
				</tr>
			</thead>
			<tbody><?php
				$colisage = prd_colisage_types_get( 0, false, array('col_name'=>'asc', 'col_qte'=>'asc') );
				if( ria_mysql_num_rows($colisage)>0 ){
					while( $col = ria_mysql_fetch_array($colisage) ){
						$col_qte = number_format($col['qte'], 4, ',', ' ');
						$col_qte = str_replace(',0000', '', $col_qte);
						
						print '	<tr>
									<td headers="col-del"><input type="checkbox" '.($col['is_sync'] ? 'disabled="disabled"' : '').' name="col[]" id="col-'.$col['id'].'" value="'.$col['id'].'" /></td>
									<td headers="col-name">'.view_colisage_is_sync($col).'&nbsp;'.htmlspecialchars( $col['name'] ).'</td>
									<td headers="col-qte" class="align-right">'.$col_qte.'</td>
								</tr>';
					}
				} else {
					print '<tr><td colspan="3">'._('Aucun conditionnement').'</td></tr>';
				}
			?></tbody>
			<tfoot>
				<tr><td colspan="3" class="align-right">
					<input type="submit" name="del" id="del" value="<?php print _('Supprimer')?>" onclick="return delColisage();" class="float-left" />
					<label for="desc"><?php print _('Désignation :')?></label> <input type="text" name="desc" id="desc" value="" />
					<label for="qte"><?php print _('Quantité :')?>&nbsp;</label><input type="text" name="qte" id="qte" value="" />
					<input type="submit" name="add-col" id="add-col" value="<?php print _('Ajouter')?>" onclick="return addColisage();" />
				</td></tr>
			</tfoot>
		</table>
	</form>
	<script>
		function addColisage(){
			$.ajax({
				type: "POST",
				url: '/admin/catalog/ajax-colisage.php',
				data: 'add=1&prd=<?php print $_GET['prd']; ?>&desc='+$("#desc").val()+'&qte='+$("#qte").val(),
				dataType: 'xml',
				async:false,
				success: function(xml){
					if( $(xml).find('result').attr('type') == '1' ){
						parent.refreshColisage(<?php print $_GET['prd']; ?>);
						parent.closeColisage();
					}else
						alert( $(xml).find('msg').text() );
				},
				error: function(res){
					alert("1 <?php print _('Une erreur inattendue s\'est produite lors de l\'ajout du conditionnement.').'\n'._('Veuillez réessayer ou bien prendre contact avec l\'administrateur')?>");
					return false;
				}
			});
		}
		function delColisage(){
			$.ajax({
				type: "POST",
				url: '/admin/catalog/ajax-colisage.php',
				data: 'del=1&prd=0&'+$("#frmcolisage").serialize(),
				dataType: 'xml',
				async:false,
				success: function(xml){
					if( $(xml).find('result').attr('type') == '1' )
						parent.refreshColisage(<?php print $_GET['prd']; ?>);
					else
						alert( $(xml).find('msg').text() );
				},
				error: function(res){
					alert("1 <?php print _('Une erreur inattendue s\'est produite lors de l\'ajout du conditionnement.').'\n'._('Veuillez réessayer ou bien prendre contact avec l\'administrateur')?>");
					return false;
				}
			});
		}
		function colisageCancel(){
			if( $("#lastCat").val()!=="0" )
				history.back();
			else{
				parent.close_cat_popup();
			}
		}
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
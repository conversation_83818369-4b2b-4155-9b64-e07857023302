<?php

/**	\file price-watching-followed.php
 *
 *	Cette page affiche la liste des produits suivis par la veille tarifaire
 *
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
if( !isset($config['price_watching_active']) || !$config['price_watching_active'] ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

if( !isset( $_GET['srccat'] ) ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

require_once( 'PriceWatching/models/prw_competitors.inc.php' );
require_once 'PriceWatching/prw.watchingAssistant.inc.php';
$prw_competitors = new prw_competitors();
$competitors = $prw_competitors->prw_competitors_getActive( false );
if( !$competitors ){
	$error[] = _('Il n\'y a pas de concurrent activé.');
}
if( ( isset( $_POST['watch'] ) || isset( $_POST['stop-watch'] ) ) && !isset( $_POST['cpt'] ) ){
	$error[] = _('Il faut sélectionner un ou plusieurs concurrents');
} else if( isset( $_POST['watch'] ) && isset( $_POST['cpt'] ) && is_array( $_POST['cpt'] ) ){

	$pwa = new WatchingAssistant();

	if( isset( $_GET['categs'] ) && is_array( $_GET['categs'] ) ){
		$pwa->setProductCategorieInfo( $_GET['categs'] );
	}
	if( isset( $_GET['products'] ) && is_array( $_GET['products'] ) ){
		$pwa->setProductsInfo( $_GET['products'] );
	}
	$cpts = $_POST['cpt'];
	$pr = $pwa->watchSelectedProducts( $cpts );

	$is_selection = false;
	$success = _("Vous surveillez")."  ";
	$success .= ( isset( $_GET['categs'] ) ) ? _("ces catégories.") : _("ces produits.");

} else if( isset( $_POST['stop-watch'] ) && isset( $_POST['cpt'] ) && is_array( $_POST['cpt'] ) ){
	$pwa = new WatchingAssistant();

	if( isset( $_GET['categs'] ) && is_array( $_GET['categs'] ) ){
		$pwa->setProductCategorieInfo( $_GET['categs'] );
	}
	if( isset( $_GET['products'] ) && is_array( $_GET['products'] ) ){
		$pwa->setProductsInfo( $_GET['products'] );
	}


	$cpts = $_POST['cpt'];
	$pr = $pwa->cancelSelectedProducts( $cpts );

	$is_selection = false;

	$success = _("Vous ne surveillez plus")." ";
	$success .= ( isset( $_GET['categs'] ) ) ? _("ces catégories.") : _("ces produits.");
}
$pwa = new WatchingAssistant();
if( isset( $_GET['products'] ) ){
//    $pwa->setProductsInfo($prd['id']);
	foreach( $_GET['products'] as $p ){
		if( $rprd = prd_products_get_simple( $p ) ){
			$prd = ria_mysql_fetch_array( $rprd );
			if( $pwa->hasChildrens( $prd['id'] ) ){
				$notice[] = '<li>' . view_prd_is_sync( $prd ) . ' <a href="product.php?cat=' . $_GET['srccat'] . '&prd=' . $prd['id'] . '&lng=fr&tab=pricewatching">' .htmlspecialchars( $prd['ref'] . ' ' . $prd['name'] ). '</a></li>';
			}
		}
	}
}

if( isset( $_GET['categs'] ) ){
	$title = _('Surveiller des catégories');
} else{
	$title = _('Surveiller des produits');
}

define( 'ADMIN_PAGE_TITLE', $title . ' - '._('Catalogue') );
require_once( 'admin/skin/header.inc.php' );
?>
<h2><?php print $title; ?></h2>

<?php
if( isset( $error ) ){
	if( is_array( $error ) ){
		if( sizeof( $error ) ){
			print '<div class="error">';
			if( sizeof( $error ) > 1 ){
				print _('Les produits suivants n\'ont pas été déplacés car ils sont déjà présents dans la catégorie').' :';
			} else{
				print _('Le produit suivant ne peut être surveillé car les erreurs suivantes sont survenues').' :';
			}

			print '<ul>';
			foreach( $error as $e ){
				print '<li>' . htmlspecialchars( $e ) . '</li>';
			}
			print '</ul>';
			print '</div>';
		}
	} elseif( trim( $error ) != '' ){
		print '<div class="error">' . nl2br( htmlspecialchars( $error ) ) . '</div>';
	}
}
if( isset( $success ) ){
	echo '<div class="error-success">' . nl2br( htmlspecialchars( $success ) ) . '</div>';
}
if( isset( $notice ) ){
	if( is_array( $notice ) ){
		echo '<div class="notice"><p>'._('Les produits suivants ne peuvent pas être surveillés car ils ne possèdent pas de code-barres.').'</p><p>'._('Cependant ils possèdent des produits enfants qui peuvent être surveillés').' :</p><ul>';
		foreach( $notice as $n ){
			echo $n;
		}
		echo '</ul></div>';
	}
}
?>
<p><?php printf(_('Vérifier que vous avez bien sélectionné les %s que vous voulez surveillez.'), isset( $_GET['products'] ) ? _("produits") : _("catégories"));?></p>
<p><?php print _('Puis sélectionnez le ou les concurrents que vous voulez surveiller ou ne plus surveiller.')?></p>
<?php if( isset( $_GET['categs'] ) ){ ?>
	<p><?php print _('Si parmi les catégories choisies il y a des produits parents avec des enfants, ils seront automatiquement surveillés.')?></p>
<?php } ?>


<?php
// Affiche la liste des catégories qui seront surveillés
if( $competitors ){
if( isset( $_GET['categs'] ) ){
	print '<p>'._('Les catégories suivantes seront surveillées').' :</p>';
	print '<ul>';
	foreach( $_GET['categs'] as $c ){
		if( $rcat = prd_categories_get( $c ) ){
			$cat = ria_mysql_fetch_array( $rcat );
			print '<li>' . view_cat_is_sync( $cat ) . ' ' . htmlspecialchars( $cat['title'] ) . '</li>';
		}
	}
	print '</ul>';
} else{ // Affiche la liste des produits qui seront surveillés
	if( isset( $_GET['products'] ) ){

		print '<p>'._('Les produits suivants seront surveillés').' :</p>';
		print '<ul>';
		foreach( $_GET['products'] as $p ){
			if( $rprd = prd_products_get_simple( $p ) ){
				$prd = ria_mysql_fetch_array( $rprd );
				if( !$pwa->hasChildrens( $prd['id'] ) ){
					$li = '<li>' . view_prd_is_sync( $prd ) . ' ' .htmlspecialchars( $prd['ref'] . ' ' . $prd['name'] );
					$follow = array();
					foreach( $competitors as $c ){
						if( $pwa->getDisable( $prd['id'], $c['id'] ) == "0" ){
							$follow[] = $c['name'];
						}
					}

					if( !empty( $follow ) ){
						$li .= ' '._('Surveillé sur').' : ' . implode( ', ', $follow );
					}
					$li .= '</li>';
					echo $li;
				}
			}
		}
		print '</ul>';
	}
}
$watch = '';
$watch .= '?srccat=' . $_GET['srccat'];
if( isset( $_GET['categs'] ) && is_array( $_GET['categs'] ) ){
	foreach( $_GET['categs'] as $c )
		if( is_numeric( $c ) )
			$watch .= '&categs[]=' . $c;
} else{
	foreach( $_GET['products'] as $p )
		if( is_numeric( $p ) )
			$watch .= '&products[]=' . $p;
}
?>
<form action="price-watching-followed.php<?php print $watch ?>" method="post">
	<table style="margin-top: 5px;">
		<caption><?php print _('Liste des concurrents')?></caption>
		<col width="17"/>
		<col width="300"/>
		<col width="120"/>
		<thead>
		<tr>
			<th>&nbsp;</th>
			<th><?php print _('Désignation')?></th>
			<th><?php print _('Langue')?></th>
		</tr>
		</thead>
		<tfoot>
		<tr>
			<td colspan="3">
				<input type="submit" name="watch" value="<?php print _('Surveiller')?>" onclick="return validForm(this.form)"/>
				<input type="submit" name="stop-watch" value="<?php print _('Ne plus surveiller')?>"/>
			</td>
		</tr>
		</tfoot>
		<tbody>
		<?php
		foreach( $competitors as $c ){ ?>
			<?php if( $c['id'] !== "3" ){ ?>
				<tr>
					<td><input type="checkbox" class="radio" name="cpt[]" value="<?php print $c['id'] ?>"/></td>
					<td><a href="<?php print $c['url'] ?>"
						   title="<?php print _('Afficher la page de la concurrence')?>"><?php print htmlspecialchars( $c['name'] ) ?></a></td>
					<td align="right"><?php print $c['lng_code'] ?></td>
				</tr>
			<?php } ?>
		<?php }
		} ?>
		</tbody>
	</table>
</form>

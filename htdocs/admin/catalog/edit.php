<?php

	/**	\file edit.php
	 *	Cette page permet la mise à jour d'une catégorie de produits
	 */

	require_once('categories.inc.php');
	require_once('products.inc.php');
	require_once('cat.images.inc.php');
	require_once('prd/category-filters.inc.php');
	require_once('fields.inc.php');
	require_once('prices.inc.php');
	require_once('rewards.inc.php');

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG_VIEW');

	// Vérifie l'existance de la catégorie
	if( !isset($_GET['cat']) || !prd_categories_exists($_GET['cat']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie que l'utilisateur à bien le droit de modifier / supprimer une catégorie
	if( isset($_POST['delete']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG_DEL');
	}
	if( isset($_POST['save']) || isset($_POST['savefields']) || isset($_POST['add-filter']) || isset($_POST['delfilter']) || isset($_POST['save-ref']) || 
		isset($_POST['save-ctr']) || isset($_POST['save-rwd-cat']) || isset($_POST['delimg']) || isset($_POST['deldocs']) || isset($_POST['savedocs']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG_EDIT');
	}

	
	unset($error);

	$url_p = ria_mysql_fetch_array(cfg_urls_get($config['wst_id'], CLS_CATEGORY));
	$url_p = $url_p['url'];
	

	// Chargement de la catégorie
	$cat = ria_mysql_fetch_array(prd_categories_get($_GET['cat']));

	//Vérifie si la catégorie peut être modifiée/supprimée par l'utilisateur
	$read_only=false;
	if($_SESSION['usr_tnt_id']!=0){
		if(isset($config["read_only_categ"]) && $config["read_only_categ"]!=""){
			if(isset($config["read_only_categ"][0]) && $config["read_only_categ"][0] == "all"){
				$read_only=true;
			}else{
				$read_only_categ=$config["read_only_categ"];
				if(is_array($read_only_categ)){
					if(in_array($_GET['cat'], $read_only_categ)){
						$read_only=true;
					}
				}
			}
		}
	}

	// Gestion des onglets
	if( !isset($_GET['tab']) )
		$_GET['tab'] = 'general';
	$tab = $_GET['tab'];
	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabPrices']) ){
		$tab = 'prices';
	}elseif( isset($_POST['tabImages']) || isset($_POST['addimg']) ){
		$tab = 'images';
	}elseif( isset($_POST['tabFilters']) ){
		$tab = 'filters';
	}elseif( isset($_POST['tabReferencement']) ){
		$tab = 'ref';
	}elseif( isset($_POST['tabFields']) ){
		$tab= 'fields';
	}elseif( isset($_POST['tabComparators']) ){
		$tab='comparators';
	}elseif( isset($_POST['tabMarketplace']) ){
		$tab='marketplace';
	}elseif( isset($_POST['tabRewards']) ){
		$tab='rewards';
	}elseif( isset($_POST['tabDocuments']) ){
		$tab = 'documents';
	}

	if( $tab=='documents' ){
		$tab = 'images';
	}
	
	if( $tab=='rewards' && !gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD') ){
		$tab = 'general';
	}


	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php?cat='.$_GET['cat']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		if( !$cat['is_sync'] ){
			prd_categories_del($_GET['cat']);
			header('Location: index.php?cat='.$_GET['cat']);
			exit;
		}
	}
	
	// Bouton Enregistrer et Enregistrer et revenir à la liste
	if( isset($_POST['save']) || isset($_POST['save_stay']) ){
		if( !$cat['is_sync'] ) $_POST['title'] = '';
		
		if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
			if( !isset($_POST['name'],$_POST['title'],$_POST['desc']) ){
				$error = _("Une ou plusieurs informations obligatoires sont manquantes ou invalides.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}else{
				if( isset($_POST['from-opt']) ){
					if( $_POST['from-opt']==2 ){
						$_POST['date_from'] = date('d/m/Y');
					}elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_from']) ){
						$error = _("La date de début de publication doit être saisie au format jj/mm/aaaa.")."\n"._("Veuillez réessayer.");
					}elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$_POST['hour_from']) ){
						$error = _("L'heure de début de publication doit être saisie au format hh:mm.")."\n"._("Veuillez réessayer");
					}
				}

				if( isset($_POST['to-opt']) ){
					if( $_POST['to-opt']==2 ){
						$_POST['date_to'] = $_POST['hour_to'] = '';
					}elseif( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_to']) ){
						$error = _("La date de fin de publication doit être saisie au format jj/mm/aaaa.")."\n"._("Veuillez réessayer.");
					}elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$_POST['hour_to']) ){
						$error = _("L'heure de fin de publication doit être saisie au format hh:mm.")."\n"._("Veuillez réessayer");
					}
				}

				if( !isset($error) && isset($_POST['index']) ){
					if( $cat['no_index']==$_POST['index'] ){
						if( !prd_categories_set_index($_GET['cat'], $_POST['index']) ){
							$error = _("Une erreur inattendue s'est produite lors de la mise à jour de l'information d'indexation de la catégorie.");
						}
					}
				}

				if( !isset($error) ){
					if( $cat['is_sync'] ){ 
						$_POST['name'] = $cat['name']; 
						$publish = $cat['publish'];
					} else {
						$publish = $_POST['publish'];
					}
					if( $cat['parent_id']=='' ) $cat['parent_id'] = 0;

					$is_soldes = isset($_POST['is_soldes']) ? true : false;

					if( prd_categories_update($_GET['cat'],$_POST['name'],$_POST['title'],$_POST['desc'],$cat['parent_id'], $publish, $_POST['date_from'].' '.$_POST['hour_from'], $_POST['date_to'].' '.$_POST['hour_to'], $is_soldes) ){
						if( !isset($_POST['tabImages']) && !isset($_POST['save_stay']) ){
							header('Location: index.php?cat='.$_GET['cat']);
							exit;
						}else{
							$_SESSION['edit_cat_success'] = _("Vos modifications ont été enregistrées avec succès.");
						}
					}else{
						$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
			}
		} elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ) {
			$publish = $cat['is_sync'] ? $cat['publish'] :  $_POST['publish'];
			if( $publish ) prd_categories_publish($_GET['cat']);  else prd_categories_unpublish($_GET['cat']);
			
			$values = array(
				_FLD_CAT_DESC=>$_POST['desc']
			);
			
			$values[ _FLD_CAT_TITLE ] = $_POST['title'];
			$values[ _FLD_CAT_NAME ] = $cat['is_sync'] ? $cat['name'] : $_POST['name'];
			
			// valeurs actuelles
			$current_name = fld_object_values_get( $cat['id'], _FLD_CAT_NAME, $_GET['lng'], false, true );
			$current_title = fld_object_values_get( $cat['id'], _FLD_CAT_TITLE, $_GET['lng'], false, true );
			
			// pas de modifications
			if( $values[ _FLD_CAT_TITLE ] == $current_title ){
				unset($values[ _FLD_CAT_TITLE ]);
			}
			if( $values[ _FLD_CAT_NAME ] == $current_name ){
				unset($values[ _FLD_CAT_NAME ]);
			}
			
			if( !fld_translates_add($_GET['cat'], $_GET['lng'], $values) ){
				$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/catalog/edit.php?cat='.$cat['id']);
			exit;
		}
	}
	
	// Bouton Enregistrer Référencement
	view_admin_tab_referencement_actions( CLS_CATEGORY, $_GET['cat'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );


	// Ajout d'un filtre
	if( isset($_POST['add-filter']) && isset($_POST['ss-family-1']) ){
		if( isset($_POST['mdl']) && $_POST['mdl']=='brand' ){
			if( !fld_object_values_set($_GET['cat'], _FLD_CAT_FILTER_BRD, 'Oui') ){
				$error = _("Une erreur s'est produite lors de l'ajout du filtre");
			}
		} elseif( !prd_category_filters_add($_GET['cat'], $_POST['ss-family-1']) ){
			$error = _("Une erreur s'est produite lors de l'ajout du filtre");
		}
		$tab = 'filters';
	}
	
	// Supression d'un ou plusieurs filtres
	if( isset($_POST['delfilter']) ){
		if( isset($_POST['fld']) && is_array($_POST['fld']) ){
			foreach( $_POST['fld'] as $f ){
				if( $f=='brand' ){
					fld_object_values_set( $_GET['cat'], _FLD_CAT_FILTER_BRD, '' );
				} else {
					prd_category_filters_del( $_GET['cat'], $f );
				}
			}
			$tab = 'filters';
		}
	}
	
	// Action sur l'onglet "Avancés"
	view_admin_tab_fields_actions( CLS_CATEGORY, $_GET['cat'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );
	
	// Enregistrer le mapping des catégories de comparateurs de prix
	if( isset($_POST['save-ctr']) ){
		$tab = isset($_POST['is_marketplace']) && $_POST['is_marketplace'] ? 'marketplace' : 'comparators';
		if( isset($_POST['chooseexp']) ){
			foreach( $_POST['chooseexp'] as $ctr=>$choose ){
				if( !in_array($choose, array('0', '-1')) )
					continue;
				
				if( !ctr_prd_categories_del($ctr, $_GET['cat'], $choose==0 ? false : true) ){
					$error = _("Une erreur inattendue s'est produits lors de l'enregistrement.")."\n"._("Veuillez ressayer ou prendre contact pour nous signaler le problème.");
					break;
				}
			}
		}
		
		// enregistre aussi les enchères
		if( isset($_POST['amount']) ){
			foreach( $_POST['amount'] as $ctr=>$amount ){
				
				$amount = str_replace( ',', '.', $amount );
				
				// récupère le minimum de l'enchère
				$family = ctr_prd_categories_get_family( $ctr, $_GET['cat'] );
				if( $family ){
					$min = ctr_categories_get_min_auctions( $ctr, $family );
					if( $min>$amount )
						$error = str_replace(
							"#param[comparator_name]#", 
							ctr_comparators_get_name($ctr), 
							_("Le minimum de l'enchère pour le comparateur \"#param[comparator_name]#\" n'est pas respecté.")
						);
				}

				if( !isset($error) ){
					if( !ctr_prd_categories_set_auctions( $ctr, $_GET['cat'], $amount) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des enchères.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/catalog/edit.php?cat='.$_GET['cat'].'&tab='.$tab);
			exit;
		}
	}
	
	// Enregistrement des points de fidélité
	if( isset($_POST['save-rwd-cat'], $_POST['pts'],$_POST['prf-id'])){

		$date_start = null;
		$date_end = null;
  		$heure_start = isset($_POST['hour_date_start'])  && $_POST['hour_date_start'] != "" ? $_POST['hour_date_start'] : "00:00";
    	$heure_end = isset($_POST['hour_date_start']) && $_POST['hour_date_start'] != ""  ? $_POST['hour_date_end'] : "00:00";

	    if( isset($_POST['date_start']) ){
	        if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_start']) ){
	            $error = _("La date de début de publication doit être saisie au format jj/mm/aaaa.")."\n"._("Veuillez réessayer.");
	        }elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$heure_start)){
	            $error = _("L'heure de début de publication doit être saisie au format hh:mm.")."\n"._("Veuillez réessayer");
	        }else{
	            $date_start = $_POST['date_start'].' '. $heure_start;
	        }       
	    }

	    if(isset($_POST['date_end'])){
	        if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$_POST['date_end']) ){
	            $error = _("La date de début de publication doit être saisie au format jj/mm/aaaa.")."\n"._("Veuillez réessayer.");
	        }elseif( !preg_match('/^[0-9]{1,2}:[0-9]{2}$/',$heure_end)){
	            $error = _("L'heure de début de publication doit être saisie au format hh:mm.")."\n"._("Veuillez réessayer");
	        }else{
	            $date_end = $_POST['date_end'].' '.$heure_end;
	        }
	    }

		if( $date_end && !$date_start ){
			$date_start = date('d/m/Y');
			$date_start .= " 00:00";
		}

		$prf = $_POST['prf-id'];
		$points = $_POST['pts'];
		$euro = str_replace( ',', '.', $_POST['amount'] );
		$ratio = array( 'amount'=>$euro, 'pts'=>$points );
		if( !isset($error) ){
			if( !rwd_cat_rewards_add($_GET['cat'], $prf, $ratio, $date_start, $date_end)){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des ratios.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}
		
	// Réalisation des actions sur l'onglets Documents
	view_admin_tab_documents_actions( CLS_CATEGORY );

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	define('ADMIN_PAGE_TITLE', htmlspecialchars($cat['title']).' - '._('Catalogue'));
	require_once('admin/skin/header.inc.php');

	print '<h2>'.view_cat_is_sync($cat)._('Catégorie').' '.htmlspecialchars($cat['title']).'</h2>';
	if (isset($_SESSION['referencement_edit_success'])) {
		$success = $_SESSION['referencement_edit_success'];
		unset($_SESSION['referencement_edit_success']);
	}
	if (isset($_SESSION['edit_cat_success'])) {
		$success = $_SESSION['edit_cat_success'];
		unset($_SESSION['edit_cat_success']);
	}
	if (isset($_SESSION['referencement_edit_error'])) {
		$error = $_SESSION['referencement_edit_error'];
		unset($_SESSION['referencement_edit_error']);
	}

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	if( isset($success) ){
		print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
	}
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	// Récupère les informations traduite
	if( $lng!=$config['i18n_lng'] ){
		$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], $lng, $cat, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title', _FLD_CAT_DESC=>'desc'), true );
		$cat['name'] = $tsk_cat['name'];
		$cat['title'] = $tsk_cat['title'];
		$cat['desc'] = $tsk_cat['desc'];
		
		$tsk_url = fld_object_values_get( array($_GET['cat']), _FLD_CAT_URL, $lng, false, true );
		$cat['cat_url_alias'] = trim($tsk_url)!='' ? $tsk_url : $cat['url_alias'];
		$cat['url_perso'] = fld_object_values_get( array($_GET['cat']), _FLD_CAT_URL_PERSO, $lng, false, true );
		$cat['url_alias'] = trim($cat['url_perso'])!='' ? $cat['url_perso'] : $cat['cat_url_alias'];
	}
?>

<form id="cat-form" action="edit.php?cat=<?php print $_GET['cat']; ?>&amp;tab=<?php print $tab; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data">

	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général')?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( view_admin_show_tab_fields( CLS_CATEGORY, $_GET['cat'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php print _('Avancé')?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabPrices" value="<?php print _('Tarification')?>" <?php if( $tab=='prices' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabImages" value="<?php print _('Médiathèque')?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabFilters" value="<?php print _('Filtres')?>" <?php if( $tab=='filters' ) print 'class="selected"'; ?> /></li>
		<?php if( tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabReferencement" value="<?php print _('Référencement')?>" <?php if($tab=='ref') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php
			if( isset($config['ctr_active']) && $config['ctr_active'] ){
				
				// L'onglet Places de marché est affiché uniquement si l'utilisateur y est autorisé et si des comparateurs sont actifs
				if( gu_user_is_authorized('_RGH_ADMIN_COMPARATOR') ){
					$r_ctr = ctr_comparators_get( 0, true, false, false );

					if( $r_ctr && ria_mysql_num_rows($r_ctr) ){
						print '
							<li><input type="submit" name="tabComparators" value="'._('Comparateurs').'" '.( $tab=='comparators' ? 'class="selected"' : '' ).' /></li>
						';
					}
				}

				// L'onglet Places de marché est affiché uniquement si l'utilisateur y est autorisé et si des comparateurs sont actifs
				if( gu_user_is_authorized('_RGH_ADMIN_MARKET_PLACE') ){
					$r_mtk = ctr_comparators_get( 0, true, false, true );

					if( $r_mtk && ria_mysql_num_rows($r_mtk) ){
						print '
							<li><input type="submit" name="tabMarketplace" value="'._('Places de marché').'" '.( $tab=='marketplace' ? 'class="selected medium"' : 'class="medium"' ).' /></li>
						';
					}
				}
			}

			// L'onglet Fidélité n'est affiché que si le module est activé
			if( gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD') ){
				print '
					<li><input type="submit" name="tabRewards" value="'._('Fidélité').'" '.( $tab=='rewards' ? 'class="selected"' : '' ).' /></li>
				';
			}
		?>
	</ul>
	<div id="tabpanel">

<?php if( $tab=='general' ){
 
	// Affiche le menu de langue
	print view_translate_menu( 'edit.php?cat='.$_GET['cat'].'&amp;tab=general', $lng );
	
?>

<?php if ($cat['is_sync']) { ?>
	<div class="notice"><?php print _('Les champs grisés sont synchronisés avec votre ERP : ils ne sont pas modifiables directement dans RiaShop'); ?></div>
<?php } ?>

<table>
	<tbody>
		<tr><th colspan="2"><?php print _('Général')?></th></tr>
		<?php if( $cat['id'] ){ ?>
        <tr>
            <td><?php print _('Référence RiaShop :'); ?></td>
            <td><?php print $cat['id']; ?></td>
        </tr>
		<?php 
		}
		if( $cat['ref'] ){ ?>
        <tr>
            <td><label for="ref" title="<?php print _('Référence de cette catégorie dans votre système d\'information')?>"><?php print _('Référence ERP :'); ?></label></td>
            <td><input type="text" name="ref" id="ref" value="<?php print $cat['ref']; ?>" maxlength="30" disabled="disabled" /></td>
        </tr>
		<?php } ?>
		<tr>
			<td class="col140px"><label for="name"><?php print _('Nom :'); ?></label></td>
			<td><input <?php print $read_only ? 'disabled' : ''; ?> type="text" name="name" id="name" value="<?php print htmlspecialchars($cat['name']); ?>" maxlength="75" <?php if( $cat['is_sync'] ) print 'readonly="readonly"' ?> /></td>
		</tr>
		<?php if( $cat['is_sync'] ){ ?>
		<tr>
			<td><label for="title"><?php print _('Titre :'); ?></label></td>
			<td><input <?php print $read_only ? 'disabled' : ''; ?> type="text" name="title" id="title" value="<?php if($cat['name']!=$cat['title']) print htmlspecialchars($cat['title']); ?>" maxlength="75" /></td>
		</tr>
		<?php } ?>
		<tr>
			<td><label for="desc"><?php print _('Description :'); ?><br /><sub>(<?php print _('Par défaut, cette description sera également utilisée pour le référencement')?>)</sub></label></td>
			<td><textarea class="tinymce" name="desc" id="desc" cols="40" rows="5"><?php print htmlspecialchars($cat['desc']); ?></textarea></td>
		</tr>
		<?php if ( gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE') ){ ?>
		<tr>
			<td><label for="is_soldes"><?php print _('Soldes :'); ?></label></td>
			<td>
				<input <?php print $cat['is_soldes'] ? 'checked="checked"' : ''; ?> type="checkbox" name="is_soldes" id="is_soldes" value="1" />
				<label for="is_soldes"><?php print _('Cette catégorie est réservée aux soldes')?></label>
			</td>
		</tr>
		<?php } ?>
		<tr><th colspan="2"><?php print _('Publication')?></th></tr>
		<tr>
			<td><label for="publish-true"><?php print _('Publiée :'); ?></label></td>
			<td>
				<input <?php print $read_only ? 'disabled' : ''; ?> class="publish-cat" type="radio" name="publish" id="publish-true" value="1" <?php print $cat['is_sync'] ? 'disabled="disabled"' : '' ?>  <?php print $cat['publish'] ? 'checked="checked"' : ''; ?> /><label for="publish-true"><?php print _('Oui')?></label> 
				<input <?php print $read_only ? 'disabled' : ''; ?> class="publish-cat" type="radio" name="publish" id="publish-false" value="0" <?php print $cat['is_sync'] ? 'disabled="disabled"' : '' ?> <?php print $cat['publish'] ? '' : 'checked="checked"'; ?> /><label for="publish-false"><?php print _('Non')?></label>
			</td>
		</tr>
		<tr class="tr-publish-date date-start" <?php print $cat['is_soldes'] ? 'style="display:none;"' : ''; ?>>
			<td><label for="from-opt-now"><?php print _('Publier le :'); ?></label></td>
			<td>
				<input <?php print $read_only ? 'disabled' : ''; ?> type="radio" class="radio" name="from-opt" id="from-opt-now" value="2" <?php if( !trim($cat['date_from']) ) print 'checked="checked"'; ?> />
				<label for="from-opt-now"><?php print _('Publier immédiatement')?></label><br />
				<input <?php print $read_only ? 'disabled' : ''; ?> type="radio" class="radio" name="from-opt" id="from-opt-date" value="3" <?php if( trim($cat['date_from']) ) print 'checked="checked"'; ?> />
				<label for="date_from" onclick="clickPublishFrom(this.form)" onkeypress="clickPublishFrom(this.form)"><?php print _('Publier le :'); ?></label>

				<input <?php print $read_only ? 'disabled' : ''; ?> type="text" class="date datepicker" name="date_from" id="date_from" onkeypress="clickPublishFrom(this.form)" value="<?php print htmlspecialchars($cat['date_from']); ?>" maxlength="10" />
				<label for="hour_from"><?php print _('à')?></label>
				<input <?php print $read_only ? 'disabled' : ''; ?> type="text" class="hour" name="hour_from" id="hour_from" value="<?php print htmlspecialchars($cat['hour_from']); ?>" maxlength="5" />
			</td>
		</tr>
		<tr class="tr-publish-date date-end" <?php print $cat['is_soldes'] ? 'style="display:none;"' : ''; ?>>
			<td><label for="to-opt-now"><?php print _('Publier jusqu\'au :'); ?></label></td>
			<td>
				<input <?php print $read_only ? 'disabled' : ''; ?> type="radio" class="radio" name="to-opt" id="to-opt-now" value="2" <?php if( !trim($cat['date_to']) ) print 'checked="checked"'; ?> /> <label for="to-opt-now"><?php print _('Publier indéfiniment')?></label><br />
				<input <?php print $read_only ? 'disabled' : ''; ?> type="radio" class="radio" name="to-opt" id="to-opt-date" value="3" <?php if( trim($cat['date_to']) ) print 'checked="checked"'; ?> />
				<label for="date_to" onclick="clickPublishTo(this.form)" onkeypress="clickPublishTo(this.form)"><?php print _('Publier jusqu\'au :'); ?></label>

				<input <?php print $read_only ? 'disabled' : ''; ?> type="text" class="date datepicker" name="date_to" id="date_to" onkeypress="clickPublishTo(this.form)" value="<?php print htmlspecialchars($cat['date_to']); ?>" maxlength="10" />
				<label for="hour_to"><?php print _('à')?></label>
				<input <?php print $read_only ? 'disabled' : ''; ?> type="text" class="hour" name="hour_to" id="hour_to" value="<?php print htmlspecialchars($cat['hour_to']); ?>" maxlength="5" />
			</td>
		</tr>
		<tr class="cat-prd-publish">
			<td><label><?php print _('Produits publiés :'); ?></label></td>
			<td class="cat-nb-prd"><?php print number_format( $cat['products'], 0, ',', ' ' ); ?></td>
		</tr>
		<?php if( tnt_tenants_have_websites() ){ ?>
		<tr><th colspan="2"><?php print _('Indexation')?></th></tr>
		<tr>
			<td>
				<label for="index-true"><?php print _('Indexer :'); ?></label>
			</td>
			<td>
				<?php
					$is_index = prd_categories_is_index( $cat['id'], false );
					$is_index_arbo = prd_categories_is_index( $cat['id'] );
				?>
				<input <?php print $read_only ? 'disabled' : ''; ?> class="publish-cat" type="radio" name="index" id="index-true" value="1"  <?php print $is_index  ? ' checked="checked"' : ''; ?> /><label for="index-true"><?php print _('Oui')?></label> 
				<input <?php print $read_only ? 'disabled' : ''; ?> class="publish-cat" type="radio" name="index" id="index-false" value="0" <?php print !$is_index ? ' checked="checked"' : ''; ?> /><label for="index-false"><?php print _('Non')?></label>
				<?php if ($is_index && !$is_index_arbo) { ?>
					<div class="notice"><?php printf(_('Pour indexer la sous-catégorie "%s", vous devez activer cette fonctionnalité dans son arborescence.'), htmlspecialchars($cat['name']))?></div>
				<?php } ?>
				<sub><?php print _('En indexant cette catégorie, celle-ci, ainsi que les produits qu\'elle contient, seront visibles dans le moteur de recherche et le sitemap.')?></sub>
			</td>
		</tr>
		<?php }
			print view_admin_categories_codes( $cat['id'] );

			if( $config['tnt_id'] == 39 && isset($cat['id']) && $cat['id'] ){
		?>
			<tr>
				<th colspan="2"><?php print _('Segmentation')?></th>
			</tr>
			<tr>
				<td><?php print _('Segments')?></td>
				<td><?php
					$ar_ex_seg = array();
					
					require_once('segments.inc.php');
					print '	<input type="hidden" name="seg-obj-cls" id="seg-obj-cls" value="'.CLS_CATEGORY.'" />
							<input type="hidden" name="seg-obj-id-0" id="seg-obj-id-0" value="'.$cat['id'].'" />
							
							<div class="seg-obj-infos">
					';
					$robject = seg_objects_get_segments( CLS_CATEGORY, array($cat['id']) );
					if( $robject &&  ria_mysql_num_rows($robject) ){
						while( $obj = ria_mysql_fetch_array($robject) ){
							$ar_ex_seg[] = $obj['id'];
							print '	<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" title="'._('Retirer ce segment').'" alt="'._('Supprimer').'" />&nbsp;';
							print htmlspecialchars( $obj['name'] ).'<br />';
						}
					} else { 
						print _('Aucune restriction liée aux segments.'); 
					}
					print '	</div>';
				?></td>
			</tr>
			<tr>
				<td><label><?php print _('Ajouter :'); ?></label></td>
				<td>
					<select class="select-obj-seg" name="segment" id="segment">
						<option value="-1"><?php print _('Choisir un segment')?></option>
						<?php
							$rseg = seg_segments_get( 0, CLS_USER );
							if( $rseg && ria_mysql_num_rows($rseg) ){
								while( $seg = ria_mysql_fetch_array($rseg) ){
									if( in_array($seg['id'], $ar_ex_seg) ) continue;
									print '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
								} 
							}
						?>
					</select>
				</td>
			</tr>
		<?php } ?>
	</tbody>
	<tfoot>
		<tr><td colspan="2">
			<?php if (!$read_only) { ?>
			<input type="submit" name="save_stay" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications et revenir à la liste des catégories')?>" />
			<input type="submit" name="save" id="save" value="<?php print _('Enregistrer et revenir à la liste'); ?>"/>
			<?php } ?>
			<input type="submit" name="cancel" value="<?php print _('Annuler')?>" onclick="window.location.href='index.php?cat=<?php print $_GET['cat']; ?>';" title="<?php print _('Annuler les modifications')?>" />
			<?php 
			if (!$read_only) {
				if( !$cat['is_sync'] && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_DEL') ){ ?>
			<input type="submit" name="delete" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer cette catégorie')?>" />
			<?php 
				}
			} ?>
		</td></tr>
	</tfoot>
</table>
	
<?php }elseif( $tab=='prices' ){ ?>
	<h3><?php print _('Conditions tarifaires')?></h3>
	<div class="prices-menu">
		<?php 
			$periode = isset($_GET['periode']) && ( $_GET['periode']>=0 || $_GET['periode']<7 ) ? $_GET['periode'] : false;
			if($periode === false){
				$periode = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
			} 
			view_state_periodpicker($periode, "riaselectorperiod", 6, _('Période pour les tarifs') );
		?>
		<div class="clear"></div>
	</div>
	
	<input type="hidden" name="id-price" id="id-price" value="" />
	<input type="hidden" name="id-cdt-del" id="id-cdt-del" value="" />
	<input type="hidden" name="id-price-del" id="id-price-del" value="" />
	<table class="prc-tva-eco" id="lst-prices">
		<caption><?php print _('Tarifs conditionnels pour la catégorie').' : '.htmlspecialchars($cat['title']); ?></caption>
		<thead>
			<tr>
				<th id="sync"></th>
				<th id="informations"><?php print _('Informations')?></th>
				<th id="conditions-prc"><?php print _('Conditions')?></th>
				<th id="action-prc"></th>
			</tr>
		</thead>
		<tbody>
			<?php print prc_prices_list_view(0, $_GET['cat'], $periode); ?>
			<tr id="price-new"><td class="no-border" colspan="4"></td></tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="4" style="text-align:right;">
					<input type="button" name="prices-add" id="prices-add" value="<?php print _('Nouveau Tarif'); ?>" onclick="newForm('price', 'cat-form', '<?php print $_GET['cat']; ?>', '0');" />
				</td>
			</tr>
		</tfoot>
		
	</table>
	<span class="info-fld">* <?php print _(' Ce champ est obligatoire. Pour un nouveau tarif, la valeur doit être supérieure à 0, pour une remise en pourcentage, la valeur doit être inférieure ou égale à 100.')?></span>
	<br />
	<span class="info-fld">** : <?php print _('En décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.')?> <a href="/admin/config/prices/index.php" target="_bank"><?php print _('Plus d\'informations sur les priorités')?></a></span>
<?php }elseif( $tab=='images' ){
		view_admin_img_table(CLS_CATEGORY, $cat['id']);
		
		print view_admin_tab_documents_show( CLS_CATEGORY, $cat['id'] );
}elseif( $tab=='filters' ){?>
	<script>
		function loadFilters(){
			var mdlID = $('#mdl').val();
			var catID = $('#catID').val();
			$.ajax({
				type: "POST",
				url: '/admin/ajax/catalog/ajax-category.php',
				data: 'mdl=' + mdlID + '&cat=' + catID,
				dataType: 'json',
				success: function( fields ){
					var html = '';
					if( fields.length ){
						
						var field = false;
						
						for( var i = 0 ; i < fields.length ; i++ ){
							field = fields[ i ]; 
							
							html += '<option value="' + field.id + '">' + field.name + '</option>';
						}
					}
						
					$('#ssfamily-1,#add-filter').show();
					if( html=='' ){ 
						$('#ssfamily-1 select').hide(); 
					} else { 
						$('#ss-family-1').html( html );
						$('#ssfamily-1 select').show() 
					}
					
					if( !mdlID ){
						$('#add-filter').hide();
					}
				}
			});
			return false;
		}
	</script>
	<?php
		$flt_brd = fld_object_values_get( $_GET['cat'], _FLD_CAT_FILTER_BRD, '', false, true );
	?>
	<table>
		<caption><?php print _('Catégorie').' '.htmlspecialchars($cat['title']); ?></caption>
		<tbody>
			<tr><th colspan="2"><?php print _('Filtres associés')?></th></tr>
			<tr>
				<td><label><?php print _('Ajouter un filtre :'); ?></label></td>
				<td>
					<input type="hidden" name="catID" value="<?php print $_GET['cat']; ?>" id="catID" /> 
					<select name="mdl" id="mdl" onchange="return loadFilters();">
						<option value=""></option>
						<?php 
							if( $flt_brd!='Oui' ){
								print '	<option value="brand">'._('Par marque').'</option>';
							}
							
							$model = fld_models_get( 0,0,CLS_PRODUCT );
							if( $model && ria_mysql_num_rows($model) ){
								print '<optgroup label="'._('Choisissez un modèle de saisie').'">';
								
								while( $mdl = ria_mysql_fetch_array($model) ){
									$fld_list = fld_fields_get( 0, 0, $mdl['id'], 0, 0, 0, null, array(), false, array(), null, CLS_PRODUCT );
									$count_filters=0;
									
									while($fld = ria_mysql_fetch_array($fld_list) ){
										$filters = prd_category_filters_get($_GET['cat'], $fld['id'] );
										
										if( $filters && ria_mysql_num_rows($filters) ){
											$count_filters++;
											$filters_array[]=$filters['fld_id'];
										}
									}
									
									if( ria_mysql_num_rows($fld_list)!=$count_filters ){
										print '<option value="'.$mdl['id'].'">'.htmlspecialchars($mdl['name']).'</option>';
									}
								}

								print '</optgroup>';
							}

							$rfld_orphan = fld_fields_get( 0, 0, -1, 0, 0, 0, null, array(), false, array(), null, CLS_PRODUCT );
							if( $rfld_orphan && ria_mysql_num_rows($rfld_orphan) ){
								print '<option value="-1">'._('Champs orphelins').'</option>'; 
							}
						?>
					</select>
				</td>
			</tr>
			<tr>
				<td></td>
				<td>
					<div id="ssfamily-1" style="display:none">
						<select name="ss-family-1" id="ss-family-1">
							<option></option>											
						</select> 
						<input type="submit" name="add-filter" id="add-filter" value="<?php print _('Ajouter')?>" />
				</div>
				</td>
			</tr>
			<tr>
				<td><label><?php print _('Filtres associés :'); ?></label></td><td>
				<?php 
					$filters = prd_category_filters_get($_GET['cat']);
					if( !ria_mysql_num_rows($filters) && !$flt_brd ){
						print _('Aucun filtre associé');
					}else{
						if( $flt_brd ){
							print '<div><input class="checkbox" type="checkbox" value="brand" name="fld[]" id="fld-'._FLD_CAT_FILTER_BRD.'" /> <label for="fld-'._FLD_CAT_FILTER_BRD.'">'._('Par marque').'</label></div>';
						}
						
						if( $filters ){
							while( $f = ria_mysql_fetch_array($filters) )
							{	
								print '<div><input class="checkbox" type="checkbox" value="'.$f['fld_id'].'" name="fld[]" id="fld-'.$f['fld_id'].'" /> <label for="fld-'.$f['fld_id'].'">'.htmlspecialchars($f['fld_name']).'</label></div>';
							}
						}
						
						if( ria_mysql_num_rows($filters) || $flt_brd){
							print '	<hr style="margin: 2px 0" />
									<div><input type="checkbox" class="checkbox" id="check-all" onclick="checkAllClick(this)" title="Tout sélectionner" /> <label for="check-all">'._('Tout sélectionner').'</label></div>';
						}
					}
				?>
				</td>
			</tr>	
		</tbody>
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="delfilter" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer les filtres sélectionnés')?>" />
			</td></tr>
		</tfoot>
	</table>
	
<?php }elseif( $tab=='ref' ){
	// Affiche le menu de langue
	$website = ria_mysql_fetch_array( wst_websites_get($config['wst_id']) );
	
	print view_translate_menu( 'edit.php?cat='.$_GET['cat'].'&tab=ref', $lng );
	
	// Récupère les informations traduite
	if( $lng!=$config['i18n_lng'] ){
		$cat['name'] = $cat['is_sync'] ? $cat['name'] : fld_object_values_get($_GET['cat'], _FLD_CAT_NAME, $lng, false, true);
		$cat['title'] = fld_object_values_get($_GET['cat'], _FLD_CAT_TITLE, $lng, false, true);
		$cat['desc'] = fld_object_values_get($_GET['cat'], _FLD_CAT_DESC, $lng, false, true);
	}
?>
	<?php print view_admin_tab_referencement(CLS_CATEGORY, $_GET['cat'], $lng); ?>
	
	<h3><?php print _('Redirections')?></h3>
	<input type="hidden" name="cat" id="cat" value="<?php print $_GET['cat']; ?>" />
	<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />
	<input type="hidden" name="urlcat" id="urlcat" value="<?php print $cat['url_alias']; ?>" />

	<div class="block-notice-container referencement-container">
		<p class="notice">
		<?php print _('Les URLs ci-dessous redirigent l\'internaute vers cette fiche catégorie. Vous pouvez les utiliser pour la compatibilité avec un ancien site Internet ou pour la mise en place de raccourcis.')?>
		<span><?php print _('Une url ne peut être composée que de caractères alphanumériques (sauf les accents) ou bien des caractères \'-\' et \'/\'.')?></span>
		</p>
		<?php
			$rurl = rew_rewritemap_get( $cat['url_alias'], '', 301, $lng );
			$ar_url = array();
			if( $rurl ){
				while( $url = ria_mysql_fetch_array($rurl) ){
					$ar_url[] = $url;
				}
			}
			
			$count = sizeof( $ar_url )+1;
		?>
		<table id="tb-redirection" class="checklist">
			<caption><?php print _('Redirections vers cette catégorie')?></caption>
			<thead>
				<tr>
					<th id="url"><?php print _('URL')?></th>
					<th id="action"></th>
				</tr>
			</thead>
			<tbody>
				<?php
					print '<tr id="no-url" '.( sizeof( $ar_url )==0 ? '' : 'style="display:none"' ).'><td headers="url" colspan="2">'._('Aucune URL de redirection enregistrée').'</td></tr>';
					if( sizeof( $ar_url )==0 ){
					} else {
						$count = 1;
						// while( $url = ria_mysql_fetch_array($rurl) ){
						foreach( $ar_url as $url ){
							print '	<tr id="url-'.$count.'">
										<td headers="url">'.$url['extern'].'</td>
										<td headers="action" class="td-action">
							';
							if($url['extern'] == $cat['url_alias']) {
								print '	<span class="sort-inherited">'._('Aucune modification possible').'</span>';
							} else {
								print '	<a class="button" onclick="editUrlCategoryRedirection('.$count.', \''.$url['extern'].'\')" />'._('Editer').'<a/>
										<a class="del edit button" onclick="delUrlCategoryRedirection('.$count.', \''.$url['extern'].'\')">'._('Supprimer').'</a>
								';
							}
							print '	</td>
								</tr>
							';
							$count++;
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="button" name="add-form-url" id="add-form-url" value="<?php print _('Ajouter')?>" title="<?php print _('Ajouter une redirection vers la fiche de cette catégorie')?>" onclick="addUrlCategoryRedirection(<?php print $count; ?>)" />
					</td>
				</tr>
			</tfoot>
		</table>
	</div>
<?php 
	}elseif( $tab=='fields' ){
		print view_admin_tab_fields( CLS_CATEGORY, $_GET['cat'], $lng, 'edit.php?cat='.$_GET['cat'].'&amp;tab=fields' );
	}elseif( $tab=='comparators' || $tab=='marketplace' ){
		require_once('comparators/ctr.cdiscount.inc.php');
		require_once('comparators/ctr.amazon.inc.php');
		
		if( $tab!='marketplace' ){
			print view_websites_selector( $_SESSION['websitepicker'], false, 'ctr-category prd-catalog' ); 
		}
	?>
	<p class="notice"><?php print $tab!='marketplace' ? _('À partir d\'ici, vous avez la possibilité de gérer l\'export de cette catégorie vers les différents comparateurs de prix disponibles dans RiaShop') : _('À partir d\'ici, vous avez la possibilité de gérer l\'export de cette catégorie vers les différentes places de marché disponibles dans RiaShop'); ?>  :</p>
	<input type="hidden" name="prdcat" id="prdcat" value="<?php print $_GET['cat']; ?>" />
	<input type="hidden" name="is_marketplace" value="<?php print $tab!='marketplace' ? 0 : 1; ?>" />
	<div id="prd-comparator"><?php
		// Charge la liste des comparateurs
		$rctr = ctr_comparators_get( 0, true, false, $tab!='marketplace' ? false : true );
		print '	<table id="tbl-export" class="checklist mapping">
					<caption>'._('Correspondance avec les comparateurs de prix').'</caption>
					<tbody>';
		if( $rctr && ria_mysql_num_rows($rctr) ){
			while( $ctr = ria_mysql_fetch_array($rctr) ){
				// S'il s'agit d'un partenaire CDiscount, on vérifie son export est personnalisable
				if( in_array($ctr['id'], ctr_cdiscount_partners_get_ria_id()) ){
					if( ctr_cdiscount_partners_use_catalog($ctr['id']) ){
						continue;
					}
				}

				// S'il s'agit d'une place de marché d'Amazon, on vérifie que son export est personnalisable
				if( in_array($ctr['id'], ctr_amazon_get_marketplace(true)) ){
					if( !in_array($ctr['id'], ctr_amazon_get_marketplace()) ){
						continue;
					}
				}

				print '	<tr><th colspan="2">'.htmlspecialchars( $ctr['name'] ).'</th></tr>';
				// Catégorie liée
				$cat_id = 0; $is_disabled = false;
				$rcat = ctr_prd_categories_get( $ctr['id'], $_GET['cat'] );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					$cat_id = ria_mysql_result( $rcat, 0, 'id' );
					$is_disabled = ria_mysql_result( $rcat, 0, 'is_disabled' );
				}
				
				// Nombre de produits
				$prd_publish = $prd_export = 0;
				$rp = prd_products_get_simple( 0, '', true, $_GET['cat'], true, false, false, false, array('orderable'=>true, 'childs'=>true) );
				if( $rp ) $prd_publish = ria_mysql_num_rows( $rp );
				$rp = ctr_catalogs_get( $ctr['id'], 0, $_GET['cat'], true );
				if( $rp ) $prd_export = ria_mysql_num_rows( $rp );
				
				if( $is_disabled ){
					print '	<tr>
								<td style="border: none;" colspan="2">
									<div class="error ctr-no-cat">'.sprintf(_('La catégorie actuellement utilisée a été supprimée de %s. Ceci peut empêcher le produit d’apparaître dans %s. Nous vous conseillons de modifier cette catégorie et de la remplacer par une autre.'), htmlspecialchars( $ctr['name'] ), htmlspecialchars( $ctr['name'] ) ).'</div>
								</td>
							</tr>';
					
				}
				
				print '	<tr>
							<td><label>'._('Actuellement lié à').' :</label></td>
							<td>';

				$rcat = ctr_categories_get( $ctr['id'], 0, '', 1 );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					print '		<span class="ctr_cat">';
					
					if( $cat_id>0 ){
						print ctr_categories_export( $ctr['id'], $cat_id, 2, '>' );
					}elseif( $ctr['marketplace'] ){
						print _('Actuellement lié à aucune famille de la place de marché');
					}else{
						print _('Actuellement lié à aucune famille du comparateur');
					}

					print '		</span>
								<a class="edit" onclick="return modifiedCtrCat('.$ctr['id'].');" href="#">'._('Modifier').'</a>';
				}else{
					print '		<span class="ctr_cat">';
					if( $ctr['marketplace'] ){
						print _('Aucune famille disponible pour cette place de marché.');
					}else{
						print _('Aucune famille disponible pour ce comparateur de prix.');
					}
				}

				print '			<div class="info-compl">
									<span>'._('Nombre de produits publiés').' :</span> '.$prd_publish;
				print '				<br /><span>'._('Nombre de produits exportés').' :</span> '.$prd_export;
				print '				'.( ($prd_publish-$prd_export)>0 ? '&nbsp;-&nbsp;<a href="/admin/catalog/index.php?cat='.$_GET['cat'].'&amp;ctr='.$ctr['id'].'">'._('Voir les produits non exportés').'</a>' : '' );
				print '			</div>
							</td>
						</tr>
						<tr class="diffctr" id="diffctr-'.$ctr['id'].'">
							<td>
								<label for="noexp-'.$ctr['id'].'">'.($ctr['marketplace'] ? _('Famille de la place de marché') : _('Famille du comparateur') ).' :</label>
							</td>
							<td class="link-ctr">
								<span>'._('Rattachement possible').' :</span>
								<br /><input type="radio" name="chooseexp['.$ctr['id'].']" id="noexp-'.$ctr['id'].'" value="0" />
								<label for="noexp-'.$ctr['id'].'">'._('Ne pas exporter').'</label>
								<br /><input type="radio" name="chooseexp['.$ctr['id'].']" id="noexpchild-'.$ctr['id'].'" value="-1" />
								<label for="noexpchild-'.$ctr['id'].'">'._('Ne pas exporter (enfants y compris)').'</label>
								<br /><input type="radio" name="chooseexp['.$ctr['id'].']" id="expnew-'.$ctr['id'].'" value="1" />
								<label for="expnew-'.$ctr['id'].'">'._('Exporter').'</label>
							</td>
						</tr>';
				// affiche la gestion des enchères si le comparateur le permet
				if( ctr_comparators_auctions_used($ctr['id']) ){
					$direct = ctr_prd_categories_get_auctions( $ctr['id'], $_GET['cat'] );
					
					$auctions = $direct;
					if( !$direct )
						$auctions = ctr_prd_categories_get_auctions( $ctr['id'], $_GET['cat'], false );
					
					$auctions = number_format( $auctions, 2, ',', ' ' );
					
					print '		<tr>
									<td>'._('Enchère :').'</td>
									<td id="td-auctions-'.$ctr['id'].'">
										<input type="text" class="qte" name="amount['.$ctr['id'].']" id="amount-'.$ctr['id'].'" value="'.$auctions.'" />';
					$family = ctr_prd_categories_get_family( $ctr['id'], $_GET['cat'] );
					if( $family ){
						$min = ctr_categories_get_min_auctions( $ctr['id'], $family );
						if( $min>0 ){
							print '		<span style="font-size: 11px">('._('Minimum').' = '.number_format( $min, 2, ',', ' ' ).' €)</span>';
						}
					}
					print '			</td>
								</tr>';
				}
			}
		} else {
			print '<tr><td>'._('Aucun comparateur de prix n\'est activé.').'</td></tr>';
		}
		print '	</tbody>
				<tfoot>
					<tr>
						<td colspan="2" align="right">
							<input type="submit" name="save-ctr" id="save-ctr" value="'._('Enregistrer').'" />
						</td>
					</tr>
				</tfoot>
			</table>';
	?></div>
<?php
		} elseif( $tab=='rewards' ){
			$profiles = gu_profiles_get();
 ?>
	<p class="notice"><?php print _('À partir d\'ici, vous avez la possibilité de définir un nombre de points de fidélité qui seront gagnés lors de la commande d\'un produit de cette catégorie. Le nombre de points de fidélité pourra dépendre du profil du client.')?> <br /><?php print _('Si aucun nombre de points n\'est défini alors les points gagnés lors de la commande d\'un produit de cette catégorie seront calculés par rapport au ratio défini dans l\'outil de "<a target="_blank" href="/admin/tools/rewards/index.php">Fidélité</a>"')?>.</p>

	<table class="checklist">
		<caption><?php print _('Ajouter des points de fidélité')?></caption>
		<tbody>
			<tr>
				<td class="valign-center">
					<label for="rwd-name"><span class="mandatory">*</span> <?php print _('Profil :'); ?></label>
				</td>
				<td>
					<select name="prf-id">
					<?php
						if($profiles && ria_mysql_num_rows($profiles)){
							while($prf = ria_mysql_fetch_assoc($profiles)){
								?>
										<option value="<?php echo $prf['id'] ?>"><?php echo $prf['name'] ?></option>
								<?php
							}
						}
					?>
					</select>
				</td>
			</tr>
			<tr>
				<td class="valign-center">
					<label for="rwd-pts"><?php print _('Date de début')?> <span class="mandatory"></span> :</label>
				</td>
				<td>
					<input type="text" class="datepicker date" name="date_start" id="rwd-date-start" value="" />
					<label for="hour_from_start"><?php print _('à')?></label>
					<input type="text" class="hour" name="hour_date_start" id="hour_date_start" value="" maxlength="5" />
				</td>
			</tr>
			<tr>
				<td class="valign-center">
					<label for="rwd-limit"><?php print _('Date de fin')?> <span class="mandatory"></span> :</label>
				</td>
				<td>
					<input type="text" class="datepicker date" name="date_end" id="rwd-date-end" value="" />
					<label for="hour_from_end"><?php print _('à')?></label>
					<input type="text" class="hour" name="hour_date_end" id="hour_date_end" value="" maxlength="5" />
				</td>
			</tr>
	        <tr>
                <td headers="params"  class="valign-center">
                    <label for="amount"><span class="mandatory">*</span> <?php print _('Ratio Euros/Points :'); ?></label>
                </td>
                <td>
                    <input class="ratio number" type="text" name="amount" id="amount" value="" />
                    <label for="pts"><?php print _('euro(s) dépensé(s) rapporte(nt)')?></label>
                    <input class="ratio number" type="text" name="pts" id="pts" value="" />
                    <label for="pts"><?php print _('point(s)')?></label>
                </td>
            </tr>
            <tr>
   				<table id="rwc-rewards" class="rwc-rewards">
					<thead>
						<tr>
							<th id="inc-check" class="col1"><input class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox"></th>
							<th id="inc-produit" class="col2"><?php print _('Profil')?></th>
							<th id="inc-date-debut" ><?php print _('Date de début')?></th>
							<th id="inc-date-fin" ><?php print _('Date de fin')?></th>
							<th id="inc-point" ><?php print _('Ratio')?></th>
						</tr>
					</thead>
			        <tbody>
                        <?php
                            $res_rwd = rwd_rewards_get_cat( $_GET['cat'] );
                            if( !$res_rwd || !ria_mysql_num_rows($res_rwd) ){
                                print '<tr><td colspan="5">'._('Aucune offre de fidélité').'</td></tr>';
                            } else {
                                while( $rwd = ria_mysql_fetch_array($res_rwd) ){
									print ' <tr class="rwc-elem" id="rwc-'.$rwd['id'].'">
												<td headers="inc-check"><input name="del[]" class="checkbox" type="checkbox" id="r-'.$rwd['id'].'" value="'.$rwd['id'].'"></td>
												<td>
													<label>'.gu_profiles_get_name($rwd['prf_id']).'</label>
												</td>
												<td>
													<label>'.ria_date_format($rwd['date_start']).'</label>
												</td>
												<td>
													<label>'.ria_date_format($rwd['date_end']).'</label>
												</td>
												<td>
													<label>'.$rwd['ratio'].'</label>
												</td>
											</tr>';          
                                }
                            }
                        ?>
                    </tbody>
					<tfoot>
						<tr>
							<td colspan="5">
								<input onclick="return delRewardCategories();" type="button" value="<?php print _('Supprimer')?>" name="rwc-del-reward" class="button" />
							</td>
						</tr>
					</tfoot>
				</table>
            </tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save-rwd-cat" id="save-rwd-cat" value="<?php print _('Enregistrer')?>" />
				</td>
			</tr>
		</tfoot>
	</table>


			<?php } ?>
        </div>
</form>
<script>
		
	// Disable tous les champs/boutons si on accède à cette page en lecture seul
	<?php if( !gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_EDIT') || (!gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRICE_CATEG') && $tab=='prices') ){ ?>
		$(document).ready(function(){
			$('table').find('input, select, textarea').attr('disabled', 'disabled');
			$('table a.edit').remove();
			$('.prc-tva-eco a').remove();
			$('.del-cdt').remove();
			$('.edit-url').attr('onclick','').unbind('click');
			$('input[onclick]').attr('onclick','').unbind('click');
		});
	<?php } 
	
	if( !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CAT') && $tab=='images' ){ ?>
		$(document).ready(function(){
			$('#table-images').find('input, select, textarea').attr('disabled', 'disabled');
		});
	<?php } ?>

	<?php
		$filters = prd_category_filters_get($_GET['cat']);
		$exclude_filters = array();
		if( ria_mysql_num_rows($filters) ){
			while( $filter = ria_mysql_fetch_array($filters) ){
				$exclude_filters[] = $filter['fld_id'];
			}
		}	
		$models = fld_models_get( 0,0,CLS_PRODUCT );
		$countModel = 0;
		$countField = 0;
		$json = array();
		while( $model = ria_mysql_fetch_array($models) ){
			$countModel++;
			$fields = fld_fields_get(0, 0, $model['id'], 0, 0, 0, null, array(), false, $exclude_filters,null,CLS_PRODUCT);
			while( $field = ria_mysql_fetch_array($fields) ) $json[] = array(array('fld_id' => $field['id'], 'fld_name' => $field['name'], 'mdl_id' => $model['id']));
		}
	?>
	
	var modelsArray = <?php echo json_encode($json); ?>;
	
	function display(parentList, childList, childDiv){

		var parent = document.getElementById(parentList); 
		var child = document.getElementById(childList); 
		var chldDiv = document.getElementById(childDiv);
		
		var i = 1;
		child.options[0] = new Option('', '');
		for ( var nbFld=0; nbFld<child.options.length; nbFld++ )
		{
			child.options.length = 0;
		}
		for ( var nbMdl=0; nbMdl<modelsArray.length; nbMdl++ )
		{ 	
			if (modelsArray[nbMdl]['mdl_id'] == parent.value){
				child.options[i-1] = new Option(modelsArray[nbMdl]['fld_name'], modelsArray[nbMdl]['fld_id']);
				i++;
			}
		} 
		if (i>1){
			chldDiv.style.display = 'inherit';
		}
	}
	
	function hidden(childDiv){
		var chldDiv = document.getElementById(childDiv);
		var parent = document.getElementById('mdl'); 
		if( parent.options[parent.selectedIndex].value == "" ){
			chldDiv.style.display = 'none';
		}
	}
	
	<?php 
	if( $tab=='prices' ){ 
	// Récupère les conditions possibles
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);

	$fld_opt = '<option value="-1">'._('Choisissez un champ personnalisé').'</option>';
	// Construit les option des select pour l'ajout d'une condition, le selecte sera construit lors de l'appel pour avoir l'identifiant du tarifs
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		while( $fld = ria_mysql_fetch_array($r_fld) )
			$fld_opt .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
		
		// Retourne au premier index dans les tableaux MySQL
		ria_mysql_data_seek( $r_fld, 0 );
	}
	?>	
	// Gestion du sélectionneur de période
	$(document).ready(function(){
		$("#riaselectorperiod .selector a").click(function(){
			periode = $(this).attr('name');
			p = periode.substring(periode.indexOf('-')+1, periode.length);
			window.location.href = 'edit.php?cat=<?php print $_GET['cat']; ?>&tab=prices&periode='+p;
		});
		
	});
	// Ajout d'une condition
	nb_new = 0;
	function addCdt( prc, newCdt, tvaB, ecotaxe ){
		tva = tvaB ? '-tva' : '';
		if( newCdt ){
			$("#add-cdt-0").parent().find('>br').before('<select class="fld" name="cdt-new['+nb_new+']" id="cdt-new-'+nb_new+'" onchange="cdtForm('+prc+', '+nb_new+', true, '+( tvaB ? 'true' : 'false' )+', false, true);"><?php print $fld_opt; ?></select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer')?>" title="<?php print _('Supprimer cette condition')?>" name="del" id="del-cdt-new-'+nb_new+'" onclick="delCdt('+( tvaB ? 'false' : 'true' )+', 0, '+nb_new+', true, true, false, 0);" /><div class="conditions-next" id="condition-new-'+nb_new+'"></div>');
			nb_new++;
		} else {
			var nb = $("#nb-cdt"+tva+"-"+prc).val();
			$("#add-cdt"+tva+"-"+prc).parent().find('>br').before('<select class="fld" name="cdt'+tva+'-'+prc+'['+nb+']" id="cdt'+tva+'-'+prc+'-'+nb+'" onchange="cdtForm('+prc+', '+nb+', false, '+( tvaB ? 'true' : 'false' )+', false, true);"><?php print $fld_opt; ?></select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer')?>" title="<?php print _('Supprimer cette condition')?>" name="del" id="del-cdt'+tva+'-'+prc+'-'+nb+'" onclick="delCdt('+( tvaB ? 'false' : 'true' )+', '+prc+', '+nb+', false, true, false, '+nb+');" /><div class="conditions-next" id="condition'+tva+'-'+prc+'-'+nb+'"></div>');
			nb++;
			$("#nb-cdt"+tva+"-"+prc).val(nb);
		}
	}
	function saveTva(id){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();
		
		$.ajax({
			type: 'POST',      
			url: 'ajax-new-price-tva.php',
			data: 'savePrdTva=1&tva='+$("#cat-tva").val()+'&idTva='+id+'&cat='+<?php print $_GET['cat']; ?>,     
			dataType: 'xml',
			success: function(xml) {
				if( $(xml).find('result').attr('type') == '1' ){
					id = $(xml).find('result').attr('idTva');
					$("#save-tva").replaceWith('<input type="button" name="save-tva" id="save-tva" value="<?php print _('Enregistrer')?>" onclick="javasript:saveTva('+id+');"/>');
					$("#taxes").before("<div class=\"error-success\"><?php print _('L\'enregistrement de la TVA pour cette catégorie s\'est correctement déroulé.')?></div>");
				} else{
					$("#taxes").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
				}
			}
		});
	}
	<?php } ?>

	$(document).ready(function() {
		loadDatePicker();
		if( $('#tag_desc').length ){
			$('#tag_desc').riametas({ padding: true, type : "desc" });
			$('#tag_title').riametas({ padding: true, type : "title" });
			$('#tag_title_ref').riametas({ type : "title" });
			$('#tag_desc_ref').riametas({ type : "desc" });
		}
		});

   <?php view_date_initialized( false, '', false, array('refresh_in_ajax'=>false) ); ?>

   var segClsID = <?php print CLS_NEWS; ?>;
</script>

<?php
	require_once('admin/skin/footer.inc.php');

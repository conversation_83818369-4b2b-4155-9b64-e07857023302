<?php
	/// Numéro de version des fichiers CSS pour forcer le rechargement
	define( 'ADMIN_ASSET', 'v-64');

	require_once('http.inc.php');

	// Tableau contenant des URLs pouvant être exclut du contrôle du referer (appelée par des outils externe)
	$exclude_ctrl_referer = [ '^\/simplesaml\/', '^\/saml2\/', '^\/saml2-asc\/', '^\/saml2-metadata\/' ];
	
	// On regarde si l'URL appelé est exclue du contrôle de referer
	$ctrl_referer = true;
	foreach( $exclude_ctrl_referer as $one_regex ){
		if( preg_match('/'.$one_regex.'/', $_SERVER['PHP_SELF']) ){
			$ctrl_referer = false;
			break;
		}
	}

	// Protection contre les attaques de type CSRF
	if( $ctrl_referer ){
		http_check_referrer();
	}
	
	$_SERVER['SCRIPT_URL'] = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : $_SERVER['SCRIPT_NAME'];
	require_once('db.inc.php');
	require_once('RegisterGCP.inc.php');
	require_once('Administrator.inc.php');

	{ // Intialisation de l'environnment pour la gestion de l'internationalisation
		$accepted_languages = array('fr_FR', 'en_GB', 'de_DE');

		// Le choix de la langue se fait dans cette priorité :
		//		1 - Si l'administrateur est connecté on essaye de récupérer l'information dans usr_lng_code ($_SESSION['lang']);
		//		2 - Si non renseigné on récupère la langue du navigateur
		//		3 - Si la langue du navigateur n'est pas supporté, on prendre alors l'anglais

		$lang = 'en_GB';

		if( isset($_SESSION['lang']) && !empty($_SESSION['lang']) && in_array($_SESSION['lang'], $accepted_languages) ){
			$lang = $_SESSION['lang'];
		}elseif( !empty($_SERVER['HTTP_ACCEPT_LANGUAGE']) ){
			// Version longue de la langue du navigateur
			$lang_browser_long = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 5);

			if( in_array($lang_browser_long, $accepted_languages) ){
				$lang = $lang_browser_long;
			}else{
				// Version courte de la langue du navigateur
				$lang_browser = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);

				switch( $lang_browser ){
					case 'fr':
						$lang = 'fr_FR';
						break;
					case 'en':
						$lang = 'en_GB';
						break;
					case 'de':
						$lang = 'de_DE';
						break;
				}
			}
		}

		$_SESSION['lang'] = $lang;

		putenv('LC_MESSAGES='.$lang.'.utf8');

		setlocale(LC_MESSAGES, $lang.'.utf8');

		bindtextdomain('riashop', __DIR__.'/_locale');
		textdomain('riashop');
		bind_textdomain_codeset('riashop','UTF-8');
	}

	if (!isset($_SERVER['PHP_SELF'])) {
		$_SERVER['PHP_SELF'] = '';
	}
	$admin_account = new Administrator();

	if( isset($_GET['_id']) ){
		$_SESSION['admin_account_id'] = $_GET['_id'];
	}
	
	if( isset($_SESSION['admin_account_id']) ){
		if( !$admin_account->loadFromId($_SESSION['admin_account_id']) ){

			// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
			$admin_account->disconnect();
			header('Location: /login.php');
			exit;
		}
		// Enregistrement du choix d'un tenant sur l'administration mutualisée
		if( isset($_GET['get-tenant-admin']) ){
			if( $admin_account->hasAccess($_GET['get-tenant-admin']) ){
				// Informe RiaShop qu'un tenant a bien été choisi
				$admin_account->setTenantSelected($_GET['get-tenant-admin'])->save();

				// Réinitaliser les filtres
				foreach( ['ar_websitepicker', 'websitepicker', 'ord_pay_id', 'ord_dps_id', 'ord_seller_id'] as $one_session_filter ){
					if( isset($_SESSION[ $one_session_filter ]) ){
						unset( $_SESSION[ $one_session_filter ] );
					}
				}
			}

			header('Location: /');
			exit;
		}

		// Déselectionne le tenant du compte administrateur sur l'administration mutualisée
		if( isset($_GET['unset-tenant-admin']) ){
			$admin_account->setTenantSelected(0)->save();
			header('Location: /');
			exit;
		}

		if( isset($_SESSION['admin_account_tnt']) ){
			$admin_account->setTenantSelected($_SESSION['admin_account_tnt']);
		}
	}
	
	// URL accessible sans connexion
	// TODO : Charger les URLs SAML en fonction du registre, plutôt que de les mettre en dur
	$ar_nologin_urls = [
		'/login.php', '/admin/login.php',
		'/exit.php', '/admin/exit.php',
		
		// Connexion Okta de DEV (via SAML2)
		'/simplesaml/module.php/saml/sp/saml2-acs.php/94at35hkmg',
		'/simplesaml/module.php/saml/sp/metadata.php/94at35hkmg',
		'/saml/94at35hkmg-okta.php',
		'/simplesaml/module.php/saml/sp/saml2-acs.php/ql0WeymwJr',
		'/simplesaml/module.php/saml/sp/metadata.php/ql0WeymwJr',
		'/saml/ql0WeymwJr-okta.php'
	];

	if( !in_array($_SERVER['PHP_SELF'], $ar_nologin_urls) ){
		// Si l'utilisateur n'est pas connecté :
		if (!$admin_account->isConnected()) {
			if( isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH']=='XMLHttpRequest' ){
				// Si la requête est de type Ajax, la redirection vers la page de connexion ne servira à rien.
				// Envoie plutôt une réponse qui permettra d'informer l'absence de connexion
				header( 'HTTP/1.0 401 Unauthorized' );
				header( 'Content-Type: application/xml' );
				echo '<?xml version="1.0" encoding="utf8"?>';
				echo '<error code="401" msg="'._('Votre session est terminée, vous avez été déconnecté(e). Veuillez vous authentifier à nouveau.').'" />';
				exit;
			}else{
				$admin_account->disconnect();
				// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
				header('Location: /login.php?dest='.urlencode($_SERVER['REQUEST_URI']));
				exit;
			}
		}

		if( !$admin_account->getTenantSelected() ){
			if( !isset($_GET['get-tenant-admin']) && $_SERVER['PHP_SELF'] != '/index.php' ){
				header('Location: /');
				exit;
			}

			// On récupère le premier locataire auquel le compte administrateur a accès
			$connect_tnt = $admin_account->getOneAccess();
		}else{
			// Contrôle que l'administrateur connecté à bien accès au tenant en SESSION
			if( $admin_account->hasAccess($admin_account->getTenantSelected()) ){
				$connect_tnt = $admin_account->getTenantSelected();
			}
		}

		// Réalise la connexion à la base de données
		if( isset($connect_tnt) && is_numeric($connect_tnt) && $connect_tnt > 0 ){
			$ar_connections = RegisterGCP::create()->getConnections($connect_tnt);
			if( is_array($ar_connections) && count($ar_connections) ){
				require_once('cfg.variables.inc.php');
				require_once('tenants.inc.php');

				// Réalise la connexion au bon serveur de données
				$connection = array_shift($ar_connections);
        		RegisterGCPConnection::connect($connection);

				// Initialise le tenant et le website par défaut
				$config['tnt_id'] = $connect_tnt; //$admin_account->getTenantSelected();
				$config['wst_id'] = tnt_tenants_get_website_default($config['tnt_id']);

				// Charge les variables de configuration pour le site par défaut
				require_once('cfg.variables.inc.php');
				cfg_variables_load($config);
				cfg_images_load($config);
				cfg_products_load($config);

				$config['admin_access_profiles'] = array(PRF_ADMIN);
				if( ria_array_get($config, 'seller_admin_access', false) ){
					$config['admin_access_profiles'][] = PRF_SELLER;
				}

				// Informe RiaShop qu'un tenant a bien été choisi
				$_SESSION['success_init_tenant'] = true;

				// Identification des super-admins
				$config['USER_RIASTUDIO'] = false;
				if( strstr($admin_account->getEmail(), '@riastudio.fr') || strstr($admin_account->getEmail(), '@kontinuum.fr') || strstr($admin_account->getEmail(), '@yuto.com') ){
					$config['USER_RIASTUDIO'] = true;
				}

				// Récupère le compte client lié à cet adresse mail
				try{
					$admin_account->reloadUser();
				}catch(RuntimeException $e){
					gu_users_disconnect();
					$admin_account->disconnect();
					// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
					header('Location: /login.php?dest='.urlencode($_SERVER['REQUEST_URI']));
					exit;
				}

				if( $admin_account->getTenantSelected() && ria_array_get($config, 'seller_admin_access', false) && $_SESSION['usr_prf_id'] == PRF_SELLER ){
					$_SESSION['ord_seller_id'] = $_SESSION['usr_seller_id'];
					if( isset($_GET['sellers']) ){
						$_GET['sellers'] = $_SESSION['ord_seller_id'];
					}
				}

				if( isset($config['msg_types_used']) && (is_array($config['msg_types_used']) && sizeof($config['msg_types_used'])) ){
					$config['msg_types_used'][] = 'DIRECT_CONTACT';
				}

				$config['admin_submenu_state'] = 1; // Par défaut le sous-menu est ouvert

				// Chargement de configuration lié au compte administrateur (bdd)
				if( isset($_SESSION['usr_id']) ){
					if( gu_users_is_tenant_linked($_SESSION['usr_id'], $_SESSION['usr_email']) && !in_array($config['tnt_id'], array(16,1)) ){
						$config['price_watching_active'] = false;
					}

					// On récupère les variables de config 'admin-list-prds-cols' et 'admin_submenu_paneling_state'
					$code_override = array('admin-list-prds-cols', 'admin_submenu_paneling_state');
					$r_val = cfg_overrides_get(0, array(), $code_override, $_SESSION['usr_id'] );
					$nb_overrides = $r_val ? mysql_num_rows($r_val) : 0;
					if( $nb_overrides ){
						while( $val = mysql_fetch_assoc($r_val) ){
							if( $val['code'] == 'usr-admin-list-prds-cols' ){
								$_SESSION['usr-admin-list-prds-cols'] = explode(',', ria_mysql_result( $val, 0, 'value' ) );
							}else if( $val['code'] == 'admin_submenu_paneling_state' ){
								// Si on a un état de sous-menu enregistré, on le récupère
								$config['admin_submenu_state'] = $val['value'];
							}
						}
						mysql_data_seek( $r_val, 0 );
					}
					load_list_cols_products();
				}
				
				require_once('view.admin.inc.php');

				// vérifier les variables de période
				if( isset($_SESSION['datepicker_period']) ){
					$period = $_SESSION['datepicker_period'];
					$date1 = isset($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : date('d/m/Y');
					$date2 = isset($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : date('d/m/Y');

					if( $date1==$date2 && $date1==date('d/m/Y', strtotime('-1 day')) ){
						$period = $_SESSION['datepicker_period'] = 'Hier';
					} else {
						switch( $period ){
							case 'Aujourd\'hui' :
								$date1 = $date2 = date( 'd/m/Y' ); break;
							case 'Hier' :
								$date1 = $date2 = date( 'd/m/Y', strtotime('-1 day') ); break;
							case 'Les 7 derniers jours' :
								$date1 = date( 'd/m/Y', strtotime('-7 day') ); $date2 = date( 'd/m/Y' ); break;
							case 'La semaine dernière' :
								$date1 = date( 'd/m/Y', strtotime('-2 Monday') ); $date2 = date( 'd/m/Y', strtotime('last Sunday') ); break;
							case 'Les 14 derniers jours' :
								$date1 = date( 'd/m/Y', strtotime('-14 day') ); $date2 = date( 'd/m/Y' ); break;
							case 'Ce mois-ci' :
								$date1 = date( '01/m/Y' ); $date2 = date( date( 't', mktime(0, 0, 0, date('m'), 1, date('Y'))).'/m/Y' ); break;
							case 'Le mois dernier' :
								$d = date('d');
								if( $d==31 ){
									$date1 = date( '01/m/Y', strtotime('-1 month')-86400 );
									$date2 = date( date( 't', mktime(0, 0, 0, date('m', strtotime('-1 month')-86400), 1, date('Y', strtotime('-1 month')-86400))).'/m/Y', strtotime('-1 month')-86400 );
								} else {
									$date1 = date( '01/m/Y', strtotime('-1 month') );
									$date2 = date( date( 't', mktime(0, 0, 0, date('m', strtotime('-1 month')), 1, date('Y', strtotime('-1 month')))).'/m/Y', strtotime('-1 month') );
								}
								break;
							case 'Depuis le 1er janvier' :
								$date1 = date( '01/01/Y' ); $date2 = date( 'd/m/Y' ); break;
						}
					}

					$_SESSION['datepicker_date1'] = $date1;
					$_SESSION['datepicker_date2'] = $date2;
				} else {
					unset( $_SESSION['datepicker_date1'] );
					unset( $_SESSION['datepicker_date2'] );
				}
			}
			// Ajout d'une constante permettant de savoir qu'on est dans l'administration
			define('CONTEXT_IS_ADMIN', true);

			// Définit la devise monétaire du client à partir de la catégorie tarifaire par défaut.
			if( $prc_id = $config['default_prc_id'] ){
				$prc = ria_mysql_fetch_assoc(
					prd_prices_categories_get($prc_id)
				);

				$_SESSION['admin_currency'] = $prc['money_code'];
			}
		}else{
			if( isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH']=='XMLHttpRequest' ){
					// Si la requête est de type Ajax, la redirection vers la page de connexion ne servira à rien.
					// Envoie plutôt une réponse qui permettra d'informer l'absence de connexion
					header( 'HTTP/1.0 401 Unauthorized' );
					header( 'Content-Type: application/xml' );
					echo '<?xml version="1.0" encoding="utf8"?>';
					echo '<error code="401" msg="Votre session est terminée, vous avez été déconnecté(e). Veuillez vous authentifier à nouveau." />';
					exit;
			}else{
					$admin_account->disconnect();
					// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
					header('Location: /login.php?dest='.urlencode($_SERVER['REQUEST_URI']));
					exit;
			}
		}
	}


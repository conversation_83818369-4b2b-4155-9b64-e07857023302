<?php 

/**	\file js_volume_search.php
 * 	
 * 	Ce fichier retourne le nombre de recherches correspondant aux filtres passés en paramètre :
 * 	- wst : identifiant du website
 *  - date1 : date de début de période
 *  - date2 : date de fin de période
 * 
 * 	Il est utilisé pour afficher une notification à l'utilisateur lorsque de nouvelles recherches sont disponibles.
 * 
 * 	La réponse est retournée au format XML, sous la forme suivante :
 * 	\code
 *  <?xml version="1.0" encoding="utf8"?>
 * 	<success>
 * 		<search volume="0" />
 *  </success>
 * 	\endcode
 * 
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');

if( isset($_GET['wst'], $_GET['date1'], $_GET['date2']) ){
	$xml = '<?xml version="1.0" encoding="utf8"?>
			<success>';
	$stats = search_log_count_volume($_GET['wst'], $_GET['date1'], $_GET['date2']);
	
	$xml.= '<search volume="'.$stats.'">';
	
	
	$xml .= '</success>';
	
	print $xml;
}

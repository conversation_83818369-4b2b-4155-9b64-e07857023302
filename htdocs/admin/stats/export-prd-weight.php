<?php

	/**	\file export-prd-weight.php
	 * 
	 * 	Ce fichier réalise l'exportation au format Microsoft Excel de la liste des produits dont les poids bruts ou nets ne sont pas renseignés.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_WEIGHT');

	require_once( 'excel/PHPExcel.php' );


	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle(_("Export des poids des produits"))
								 ->setSubject(_("Export des poids des produits"))
								 ->setDescription(_("Export des poids des produits"))
								 ->setKeywords(_("export poids produits"))
								 ->setCategory("");
	
	// Création du fichier Excel
	$obj = $objPHPExcel->getActiveSheet();
	
	if( isset($nb_prd, $no_sleep) ){ // Première feuille : Taux de remplissage
		
		$obj->setTitle(_("Taux de remplissage"));
		
		// Entâªte du tableau des poids brut
			$obj->mergeCells('B2:G2');
			$obj->setCellValue('B2', _('Poids Bruts'));
			$obj->mergeCells('B3:C3');
			$obj->setCellValue('B3', _('Articles'));
			$obj->mergeCells('D3:E3');
			$obj->setCellValue('D3', _('Renseigné'));
			$obj->mergeCells('F3:G3');
			$obj->setCellValue('F3', _('Non renseigné'));
			$obj->setCellValue('B4', _('Catégorie'));
			$obj->setCellValue('C4', _('Nb'));
			$obj->setCellValue('D4', _('Nb'));
			$obj->setCellValue('E4', '%');
			$obj->setCellValue('F4', _('Nb'));
			$obj->setCellValue('G4', '%');
			
			//Style sur l'entâªte du tableau
			$obj->getStyle('B2:G3')->getFont()->setBold(true);
			$obj->getStyle('B2:G4')->getFont()->setSize(11);
			$obj->getStyle('B2')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
			$obj->getStyle('B2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
			$obj->getStyle('B3:G3')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
			$obj->getStyle('B4:G4')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('DDDDDD');
			$obj->getStyle('C3:G4')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
			
			// Applique des formats aux cellules C5:G7
			// Sépareteur de millième
			$obj->getStyle('C5:C7')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );	
			$obj->getStyle('D5:D7')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			$obj->getStyle('F5:F7')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			
			// Pourcentage
			$obj->getStyle('E5:E7')->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 ) );
			$obj->getStyle('G5:G7')->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 ) );
			
			// Première ligne : Tous les produits
			$obj->setCellValue('B5', _('Tous'));
			$obj->setCellValue('C5', $nb_prd);
			$obj->setCellValue('D5', $w_fills);
			
			// Deuxième ligne : Non en sommeil
			$obj->setCellValue('B6', _('Non en sommeil'));
			$obj->setCellValue('C6', $no_sleep);
			$obj->setCellValue('D6', $no_sleep_w);
			
			// Troisième ligne : Publiés sur le site marchand et non en sommeil
			$obj->setCellValue('B7', _('Publiés sur le site marchand et non en sommeil'));
			$obj->setCellValue('C7', $publish);
			$obj->setCellValue('D7', $publish_w);
			
			// Applique des formules aux cellules E5:G7
			for( $i=5 ; $i<8 ; $i++ ){
				$obj->setCellValue('E'.$i, '=(D'.$i.'/C'.$i.')');
				$obj->setCellValue('F'.$i, '=(C'.$i.'-D'.$i.')');
				$obj->setCellValue('G'.$i, '=(F'.$i.'/C'.$i.')');
			}
			
			// Bordure du tableau
			$obj->getStyle('B2:G7')->getBorders()->applyFromArray(array(
				'allborders' => array( 
					'style' => PHPExcel_Style_Border::BORDER_THIN, 
					'color' => array( 'rgb' => '808080') 
				)
			));
			
		// Entâªte du tableau des poids net
			$obj->mergeCells('B10:G10');
			$obj->setCellValue('B10', _('Poids Nets'));
			$obj->mergeCells('B11:C11');
			$obj->setCellValue('B11', _('Articles'));
			$obj->mergeCells('D11:E11');
			$obj->setCellValue('D11', _('Renseigné'));
			$obj->mergeCells('F11:G11');
			$obj->setCellValue('F11', _('Non renseigné'));
			$obj->setCellValue('B12', _('Catégorie'));
			$obj->setCellValue('C12', _('Nb'));
			$obj->setCellValue('D12', _('Nb'));
			$obj->setCellValue('E12', '%');
			$obj->setCellValue('F12', _('Nb'));
			$obj->setCellValue('G12', '%');
			
			//Style sur l'entâªte du tableau
			$obj->getStyle('B10:G11')->getFont()->setBold(true);
			$obj->getStyle('B10:G12')->getFont()->setSize(11);
			$obj->getStyle('B10')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
			$obj->getStyle('B10')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
			$obj->getStyle('B11:G11')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
			$obj->getStyle('B12:G12')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('DDDDDD');
			$obj->getStyle('C11:G12')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

			// Applique des formats aux cellules C5:G7
			// Sépareteur de millième
			$obj->getStyle('C13:C15')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );	
			$obj->getStyle('D13:D15')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			$obj->getStyle('F13:F15')->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			
			// Pourcentage
			$obj->getStyle('E13:E15')->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 ) );
			$obj->getStyle('G13:G15')->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 ) );
			
			// Première ligne : Tous les produits
			$obj->setCellValue('B13', _('Tous'));
			$obj->setCellValue('C13', $nb_prd);
			$obj->setCellValue('D13', $wn_fills);
			
			// Deuxième ligne : Non en sommeil
			$obj->setCellValue('B14', _('Non en sommeil'));
			$obj->setCellValue('C14', $no_sleep);
			$obj->setCellValue('D14', $no_sleep_wn);
			
			// Troisième ligne : Publiés sur le site marchand et non en sommeil
			$obj->setCellValue('B15', _('Publiés sur le site marchand et non en sommeil'));
			$obj->setCellValue('C15', $publish);
			$obj->setCellValue('D15', $publish_wn);
			
			// Applique des formules aux cellules E13:G15
			for( $i=13 ; $i<16 ; $i++ ){
				$obj->setCellValue('E'.$i, '=(D'.$i.'/C'.$i.')');
				$obj->setCellValue('F'.$i, '=(C'.$i.'-D'.$i.')');
				$obj->setCellValue('G'.$i, '=(F'.$i.'/C'.$i.')');
			}

			// Bordure du tableau
			$obj->getStyle('B10:G15')->getBorders()->applyFromArray(array(
				'allborders' => array( 
					'style' => PHPExcel_Style_Border::BORDER_THIN, 
					'color' => array( 'rgb' => '808080') 
				)
			));

		// Largueur des cellules
		$obj->getColumnDimension('B')->setWidth(45);
		$obj->getColumnDimension('C')->setWidth(15);
		$obj->getColumnDimension('D')->setWidth(15);
		$obj->getColumnDimension('E')->setWidth(15);
		$obj->getColumnDimension('F')->setWidth(15);
		$obj->getColumnDimension('G')->setWidth(15);
			
	} elseif( isset($r_prd) ){ // Deuxième feuille : Poids â  compléter
		
		$obj->setTitle(_("Poids â  compléter"));
		
		// Entâªte du tableau : Produits publiés sans poids, par nombre de ventes décroissant
		$obj->mergeCells('A1:E1');
		$obj->setCellValue('A1', _('Produits publiés sans poids, par nombre de ventes décroissant'));
		$obj->setCellValue('A2', _('Référence'));
		$obj->setCellValue('B2', _('Désignation'));
		$obj->setCellValue('C2', _('Ventes'));
		$obj->setCellValue('D2', _('Poids Brut'));
		$obj->setCellValue('E2', _('Poids Net'));
		
		//Style sur l'entâªte du tableau
		$obj->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
		$obj->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
		$obj->getStyle('A1:E2')->getFont()->setBold(true);
		$obj->getStyle('A1:E1')->getFont()->setSize(12);
		$obj->getStyle('A2:E2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
		$obj->getStyle('A2:E2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		
		// Ligne de départ pour l'enregistrement des produits
		$ligne = 3;
		while( $prd = ria_mysql_fetch_array($r_prd) ){
		
			// Titre et description du produit
			$title = $prd['title'] ? $prd['title'] : $prd['name'];
			$desc = $prd['desc'];
			if( strlen($desc)>105 )
					$desc = substr( $desc, 0, 102 ).'...';
			
			// Information sur le produits
			$obj->setCellValue('A'.$ligne, $prd['ref']);
			//$obj->setCellValue('B'.$ligne, $title);
			$obj->setCellValueExplicit('B'.$ligne, iconv("utf-8", "UTF-8//IGNORE", $title),PHPExcel_Cell_DataType::TYPE_STRING);
			$obj->setCellValue('C'.$ligne, $prd['selled']);
			$obj->setCellValue('D'.$ligne, $prd['weight']);
			$obj->setCellValue('E'.$ligne, $prd['weight_net']);
			
			// Passe â  la ligne suivante
			$ligne++;
		}
			
		// Applique des formats aux cellules B4:F(nombre de produits exportés)
		// Général aux référence (mâªme si il s'agit de nombre)
		$obj->getStyle('A1:B'.($ligne-1))->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_GENERAL ) );
		$obj->getStyle('A3:E'.($ligne-1))->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);

		// Sépareteur de millième
		$obj->getStyle('C3:C'.($ligne-1))->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );	
		$obj->getStyle('D3:D'.($ligne-1))->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
		$obj->getStyle('E3:Z'.($ligne-1))->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			
		// Permettre l'affichage de la description sur plusieurs lignes
		$obj->getStyle('B3:B'.($ligne-1))->getAlignment()->setWrapText(true);
		
		// Alignement des cellules d'informations du produit
		$obj->getStyle('A3:A'.($ligne-1))->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

		// Bordure du tableau
		$obj->getStyle('A1:E'.($ligne-1))->getBorders()->applyFromArray(array(
			'allborders' => array( 
				'style' => PHPExcel_Style_Border::BORDER_THIN, 
				'color' => array( 'rgb' => '808080') 
			)
		));
		
		// Largueur des cellules
		$obj->getColumnDimension('A')->setWidth(25);
		$obj->getColumnDimension('B')->setWidth(65);
		$obj->getColumnDimension('C')->setWidth(15);
		$obj->getColumnDimension('D')->setWidth(15);
		$obj->getColumnDimension('E')->setWidth(15);
	}
	
	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'._('poids-produits').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;


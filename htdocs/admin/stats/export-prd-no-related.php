<?php

	/**	\file export-prd-no-related.php
	 * 
	 * 	Ce fichier réalise l'exportation au format Microsoft Excel de la liste des produits sans article lié.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_NO_RELATED');

	global $config;
	require_once( 'excel/PHPExcel.php' );

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Détermine les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle(_("Export des produits sans article lié"))
								 ->setSubject(_("Export des produits sans article lié"))
								 ->setDescription(_("Export des produits sans article lié"))
								 ->setKeywords(_("export produits sans article lié"))
								 ->setCategory("");
	
	// Création du document
	$objPHPExcel->getActiveSheet()->setSheetState(PHPExcel_Worksheet::SHEETSTATE_VERYHIDDEN);
	
	// Création de la feuille Excel
	$objWorksheet = $objPHPExcel->createSheet();
	$objWorksheet->setTitle(_('Produits sans article lié'));
	
	// Détermine le nom des colonnes
	$objWorksheet->setCellValue('A1', _('Référence'));
	$objWorksheet->setCellValue('B1', _('Désignation'));
	$objWorksheet->setCellValue('C1', _('Prix HT'));
	$objWorksheet->setCellValue('D1', _('Prix TTC'));
	$objWorksheet->setCellValue('E1', _('Volume de ventes'));
	
	// Police de l'entâªte du tableau
	$objWorksheet->getStyle('A1:E1')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:E1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:E1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');

	// Intégration des données sur les produits
	if( $rprd!=false ){
		$line = 2;
		while( $prd = ria_mysql_fetch_array($rprd) ){
			$objWorksheet->setCellValue('A'.$line, $prd['ref']);
			$objWorksheet->setCellValue('B'.$line, $prd['title']);
			$objWorksheet->setCellValue('C'.$line, $prd['price_ht']);
			$objWorksheet->setCellValue('D'.$line, $prd['price_ttc']);
			$objWorksheet->setCellValue('E'.$line, $prd['selled']);
			$line++;
		}
	}
	
	// Applique un alignement sur le haut â  toutes les cellules
	$objWorksheet->getStyle('A1:E'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);

	// Configure les formats des cellules (chaine de caractères, décimal ...)
	$objWorksheet->getStyle('C2:D'.$line)->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0.00' ) );	
	
	// Fixe la largeur des colonnes
	$objWorksheet->getColumnDimension('A')->setWidth(15);
	$objWorksheet->getColumnDimension('B')->setWidth(60);
	$objWorksheet->getColumnDimension('C')->setWidth(15);
	$objWorksheet->getColumnDimension('D')->setWidth(15);
	$objWorksheet->getColumnDimension('E')->setWidth(15);

	// Met des bordures en place
	$objWorksheet->getStyle('A1:E'.($line-1))->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
	
	// Confirgure l'ouverture d'Excel sur la case A1
	$objPHPExcel->setActiveSheetIndex(1);


	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'._('produits-sans-article-lie').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;


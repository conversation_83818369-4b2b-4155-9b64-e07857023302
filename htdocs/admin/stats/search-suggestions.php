<?php

	/**	\file search-suggestions.php
	 *	Cette page affiche la liste des suggestions de recherche. Ce sont des propositions pour alimenter un menu d'auto-complétion
	 *	dans le moteur de recherche interne.
	 */

	require_once( 'search.inc.php' );
	require_once( 'news.categories.inc.php' );
	require_once( 'websites.inc.php' );
	require_once( 'tenants.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH_SUGGESTION');

	// Filtre sur un site en particulier
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all')
			$_SESSION['websitepicker'] = false;
		else
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
	}
	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;

	// Par défaut, on arrive sur le jour en cours
	if( !isset($_GET['day']) && !isset($_GET['week']) && !isset($_GET['month']) && !isset($_GET['year']) ){
		$_GET['day'] = date('Y-m-d');
	}

	$website = wst_websites_get();

	if( !isset($_GET['active']) ){
		$_GET['active'] = null;
	}

	$limit = 0;
	if( !isset($_GET['limit']) ){
		$limit = ceil( search_caches_suggest_count($wst_id)*0.2);
	}

	// Bouton Enregistrer
	if( isset($_POST['save'], $_POST['lst-suggest']) ){
		if( !search_caches_update_suggest(isset($_POST['scc']) ? $_POST['scc'] : 0, true, $wst_id, explode(',',$_POST['lst-suggest'])) ){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des suggestions.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		}else{
			$success = _("L'enregistrement des suggestions s'est correctement déroulé");
		}
	}

	// Bouton Nouvelle suggestion
	if( isset($_POST['add-suggest']) ){
		if( !isset($_POST['suggest']) || !trim($_POST['suggest']) ){
			$error = _("Un ou plusieurs paramètres sont manquants pour permettre l'enregistrement.\nVeuillez vérifier ou prendre contact pour nous signaler l'erreur.");
		}else{
			$section = false;
			$types = search_engine_types_get($_POST['seg']);

			if( isset($_POST['section']) && $_POST['section']>0 )
				$section = $_POST['section'];

			if( search_caches_exists_for_search($_POST['seg'], $section, $types, $_POST['suggest'], true, false) ) {
				$error = _("La suggestion existe déjà.");
			} else {
				$res = search3($_POST['seg'], $_POST['suggest'], 1, 0, true, $section, 3);
				if( $res == -1 )
					$error = _("Votre suggestion retourne aucun résultat et en conséquence elle ne n'a pas ajoutée");
				else{
					search_caches_update_suggest($res, true);
					$success = _("Votre suggestion a bien été rajoutée et activée");
				}
			}
		}
	}

	// Requête pour récupérer les suggestions
	$r_suggest = search_caches_get( $config['tnt_id'], 0, 0, 0, null, null, null, $_GET['active'], $wst_id, true, '', array( 'results'=>'desc', 'name'=>'asc' ), $limit );

	$suggest_count = ria_mysql_num_rows($r_suggest) ? ria_mysql_num_rows($r_suggest) :  1;

	// Calcule le nombre de pages
	$pages = ceil($suggest_count / 50);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages ){
			$page = $_GET['page'];
		}
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-5;
	if( $pmin<1 ){
		$pmin = 1;
	}
	$pmax = $pmin+9;
	if( $pmax>$pages ){
		$pmax = $pages;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Recherches'), '/admin/stats/search.php' )
		->push( _('Suggestions de recherches') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Suggestions').' - '._('Recherches').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
		<a name="header"></a>
		<h2><?php print _('Suggestions de recherches'); ?></h2>
		<?php
			print view_websites_selector( $wst_id, true, '', false );

			if( isset($error) ){
				print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
			}elseif( isset($success) ){
				print '<div class="error-success">'.nl2br(htmlspecialchars($success)).'</div>';
			}
		?>
		<?php
			$wst = wst_websites_get();
			$seg = search_engines_get(0,$wst_id);

			// Détermine le nombre de colonne il y a dans le tableau
			$colspan = 6;
			if( ria_mysql_num_rows($seg)>1 && ria_mysql_num_rows($wst)>1 && $wst_id==0 )
				$colspan = 7;
			elseif( ria_mysql_num_rows($seg)>1 || ( ria_mysql_num_rows($wst)>1 && $wst_id==0) )
				$colspan = 6;

		?>
		<table id="count-suggest">
			<caption><?php print _('À ce jour'); ?></caption>
			<tbody>
				<?php
					$nb_active = search_caches_suggest_count( $wst_id, true );
					$nb_inactive = search_caches_suggest_count( $wst_id, false );
				?>
				<tr>
					<td><a href="search-suggestions.php?wst=<?php print $wst_id; ?>&amp;active=1&amp;limit=0"><?php print $nb_active > 1 ? ria_number_format($nb_active).' <span class="checked">'._('Activés').'</span>' : ria_number_format($nb_active).' <span class="checked">'._('Activé').'</span>'; ?></a> | </td>
					<td><a href="search-suggestions.php?wst=<?php print $wst_id; ?>&amp;active=0&amp;limit=0"><?php print $nb_inactive > 1 ? ria_number_format($nb_inactive).' <span class="waiting">'._('Désactivés').'</span>' : ria_number_format($nb_inactive).' <span class="waiting">'._('Désactivé').'</span>'; ?></a> | </td>
					<td><a href="search-suggestions.php?wst=<?php print $wst_id; ?>&amp;limit=0"><?php print ria_number_format($nb_active + $nb_inactive); ?><span class="all"> <?php print _('Tous'); ?></span></a></td>
				</tr>
			</tbody>
		</table>
		<div id="msg-suggest">
			<?php print _('Voici la liste des suggestions de recherches qui comportent le plus de résultats.'); ?>
			<br/><?php print _('Cette liste est limitée à 20 % des recherches, vous pouvez afficher toutes les suggestions en cliquant sur : '); ?>
			<a href="search-suggestions.php?wst=<?php print $wst_id ?>&amp;limit=0&amp;active=0"><?php print _('Toutes les suggestions'); ?></a>
		</div>
		<form id="suggest-search" method="post" title="<?php print _('Suggestions de recherche'); ?>" action="search-suggestions.php?wst=<?php print $wst_id; ?>&amp;active=0&amp;page=<?php print $page; ?>&amp;limit=<?php print $limit; ?>">
			<div id="form-add-suggest" class="none">
				<div id="new-title"><?php print _('Ajouter une suggestion'); ?></div>
				<div id="new-suggestion">
					<div>
						<label for="suggest"><?php print _('Suggestion'); ?></label>
						<input type="text" name="suggest" id="suggest" value="" />
						<img id="before-visual" class="img-stat-search" src="/admin/images/petite_loupe_active.svg" name="loupe" alt="<?php print _('Pré-visualiser'); ?>" title="<?php print _('Pré-visualisation de la recherche'); ?>" style="margin-left: 5px" />
					</div>
					<?php
						$r_seg = search_engines_get();
						if( ria_mysql_num_rows($r_seg)>1 ){
							print '<div>
								<label for="seg">'._('Emplacement').'</label>
								<select name="seg" id="seg">
							';

							if( ria_mysql_num_rows($wst)>1 ){

								while( $w = ria_mysql_fetch_array($wst) ){

									print '<optgroup label="'.$w['name'].'">';
										$r_seg = search_engines_get(0, $w['id']);
										while( $s = ria_mysql_fetch_array($r_seg) )
											print '<option value="'.$s['id'].'">'.$s['name'].'</option>';
									print '</optgroup>';

								}

							} else {
								while( $s = ria_mysql_fetch_array($seg) )
									print '<option value="'.$s['id'].'">'.$s['name'].'</option>';
							}

							print '</select>
							</div>';
						} else {
							$s = ria_mysql_fetch_array($r_seg);
							print '<input type="hidden" name="seg" id="seg" value="'.$s['id'].'" />';
						}
					?>
					<div id="opt-sec">
						<div class="opt-sec"><?php print _('Options facultatives'); ?></div>
						<?php
							$r_section = prd_categories_get(0,true);
							if( ria_mysql_num_rows($r_section)>0 ){

								print '<div>
									<label for="section">'._('Section').'</label>
									<select name="section" id="section">
										<option value="0" selected="selected">'._('Toutes').'</option>
								';
										while( $section = ria_mysql_fetch_array($r_section) )
											print '<option value="'.$section['id'].'">'.$section['name'].'</option>';
								print '</select>
								</div>';
							}
						?>
					</div>
				</div>
				<div id="new-foot">
					<input type="submit" name="add-suggest" id="add-suggest" value="<?php print _('Ajouter'); ?>" />
					<input type="button" name="cancel-suggest" id="cancel-suggest" value="<?php print _('Annuler'); ?>" />
				</div>
			</div>
			<table id="table-suggest-search">
				<thead>
					<tr>
						<th colspan="<?php print $colspan; ?>" class="align-left"><input type="button" id="new-suggest" value="<?php print _('Nouvelle suggestion'); ?>" /></th>
					</tr>
					<tr>
						<th id="active" title="<?php print _('Activer / Désactiver toutes les suggestions'); ?>"><input type="checkbox" id="scc-checkbox" value="" /></th>
						<th id="name" title="<?php print _('Nom de la suggestion utilisé pour la recherche'); ?>"><?php print _('Suggestion'); ?></th>
						<th id="sections" title="<?php print _('Sections utilisées pour cette recherche'); ?>"><?php print _('Sections'); ?></th>
						<th id="types" title="<?php print _('Filtres utilisés pour cette recherche'); ?>"><?php print _('Filtres'); ?></th>
						<th id="results" class="align-right" title="<?php print _('Nombre de résultats pour cette recherche'); ?>"><?php print _('Résultats'); ?></th>
						<?php print ria_mysql_num_rows($seg)>1 ? '<th id="search" title="'._('Moteur de recherche').'">'._('Emplacement de la recherche').'</th>' : ''; ?>
						<?php print ria_mysql_num_rows($wst)>1 && $wst_id==0 ? '<th id="site" title="'._('Sites sur lesquels les recherches sont effectuées').'">'._('Site web').'</th>' : ''; ?>
					</tr>
				</thead>
				<tbody>
						<?php
							$s = array();

							if( ria_mysql_num_rows($r_suggest)==0 ){
								$r_suggest = search_caches_get( $config['tnt_id'], 0, 0, 0, null, null, null, null, $wst_id, true, '', array( 'results'=>'desc', 'name'=>'asc' ), $limit );
							}

							if( $r_suggest && ria_mysql_num_rows($r_suggest)>0 ){

								ria_mysql_data_seek( $r_suggest, ($page-1)*50 );

								$count = 0;
								while( $suggest = ria_mysql_fetch_array($r_suggest) ){

									if( $count>=50 ){
										break;
									}

									$s[] = $suggest['id'];
									$website = ria_mysql_fetch_array(wst_websites_get($suggest['wst_id']));

									// Détermine les types de recherches
									$types = search_log_get_types($suggest['id']);

									// Détermine la catégorie de recherches
									$section = _('Toutes');
									if( $suggest['section']!==null ){
										$r_section = ria_mysql_fetch_array( prd_categories_get($suggest['section']) );
										$section = $r_section['name'];
									}

									print ' <tr>
												<td headers="active" title="'._('Activer / Désactiver cette suggestion').'"><input type="checkbox" name="scc[]" id="scc-'.$suggest['id'].'" value="'.$suggest['id'].'" '.($suggest['suggest'] ? 'checked="checked"' : '').'/></td>
												<td headers="name">'.htmlspecialchars( urldecode($suggest['term']) ).'</td>
												<td headers="sections">'.$section.'</td>
												<td headers="types">'.$types.'</td>
												<td headers="results" class="align-right">'.ria_number_format($suggest['results']).'</td>';
									print 		ria_mysql_num_rows($seg)>1 ?'<td headers="search">'.htmlspecialchars( $suggest['seg_name'] ).'</td>' : '';
									print 		ria_mysql_num_rows($wst)>1 && $wst_id==0 ?'<td headers="site">'.htmlspecialchars( $website['name'] ).'</td>' : '';
									print ' </tr>';

									$count++;
								}

							}else{
								print '<tr><td colspan="'.$colspan.'">'._('Aucune suggestion de recherche').'</td></tr>';
							}
						?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2" class="align-left">
							<input type="submit" name="save" id="save" value="<?php print _('Enregistrer') ?>" />
						</td>
						<td id="pagination" colspan="<?php print $colspan-2; ?>">
							<?php
								if( $pages>1 ){
									if( $page>1 )
										print '<a href="search-suggestions.php?wst='.$wst_id.'&amp;active='.$_GET['active'].'&amp;limit='.$limit.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
									for( $i=$pmin; $i<=$pmax; $i++ ){
										if( $i==$page )
											print '<b>'.$page.'</b>';
										else
											print '<a href="search-suggestions.php?wst='.$wst_id.'&amp;active='.$_GET['active'].'&amp;limit='.$limit.'&amp;page='.$i.'">'.$i.'</a>';
										if( $i<$pmax )
											print ' | ';
									}
									if( $page<$pages )
										print ' | <a href="search-suggestions.php?wst='.$wst_id.'&amp;active='.$_GET['active'].'&amp;limit='.$limit.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
								}
							?>
						</td>
					</tr>
				</tfoot>
			</table>
			<input type="hidden" name="lst-suggest" id="lst-suggest" value="<?php print implode(',',$s); ?>" />
		</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>

<?php

	/**	\file orders.php
	 *
	 * 	Cette page affiche des statistiques sur le nombre de commandes enregistrées ainsi que sur le chiffre d'affaires réalisé.
	 *
	 *	Ces données peuvent être filtrées selon :
	 *	- une période de temps
	 *	- une origine de commande
	 *	- un mode de règlement
	 *	- un représentant
	 */

	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_ORDER');


	if( isset($_GET['origin']) ){
		$_SESSION['origin'] = $_GET['origin'];
	}
	if( isset($_GET['wst-id']) ){
		$_SESSION['ar_websitepicker'] = $wst_id = $_GET['wst-id'];
	}elseif( isset($_GET['wst_id']) ){
		$_SESSION['websitepicker'] = $wst_id = $_GET['wst_id'];
		unset($_SESSION['ar_websitepicker']);
	}
	if( isset($_GET['seller_id']) ){
		$_SESSION['ord_seller_id'] = $_GET['seller_id'];
	}
	if( isset($_GET['pay_id']) ){
		$_SESSION['ord_pay_id'] = $_GET['pay_id'];
	}
	if( isset($_GET['dps_id']) ){
		$_SESSION['ord_dps_id'] = $_GET['dps_id'];
	}
	if( isset($_GET['store-id']) ){
		if( !is_array($_GET['store-id']) ){
			$_SESSION['ord_store_id'] = array($_GET['store-id']);
		}else{
			$_SESSION['ord_store_id'] = $_GET['store-id'];
		}
	}else{
		$_SESSION['ord_store_id'] = array();
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Commandes') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Commandes').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Statistiques de commandes'); ?></h2>
	<div class="stats-menu">
		<form method="get" action="" id="riapicker-form">
			<div id="riadatepicker" class="riapicker"></div>
			<?php
				// Variables pour la mise en place des périodes
				$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
				$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
				$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
				print view_all_selectors( (isset($wst_id) ? $wst_id : 0), true, 'riapicker', true, 'Tous les sites', false, true );

				if( isset($date1, $date2) ){
					print '<input type="hidden" name="date1" id="date1" value="'.$date1.'"/>';
					print '<input type="hidden" name="date2" id="date2" value="'.$date2.'"/>';
					if (isset($_GET['last'])){
						print '<input type="hidden" name="last" id="last" value="'.$date1.'"/>';
					}
				}

				$date_start = $date1;
				$date_end = $date2;
			?>
			<input type="hidden" name="ord_website" id="ord_website" value="<?php print isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : '0'; ?>" />
			<input type="hidden" name="pay_id" id="ord_pay_id" value="<?php print isset($_SESSION['ord_pay_id']) ? $_SESSION['ord_pay_id'] : ''; ?>" />
			<input type="hidden" name="dps_id" id="ord_dps_id" value="<?php print isset($_SESSION['ord_dps_id']) ? $_SESSION['ord_dps_id'] : ''; ?>" />
			<input type="hidden" name="seller_id" id="ord_seller_id" value="<?php print isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0; ?>" />
			<input type="hidden" name="stores" id="ord_store_id" value="0" />
			<div class="clear"></div>
		</form>
	</div>

	<?php view_import_highcharts(); ?>
	<?php
		require_once( 'admin/highcharts/graph-invoices.php' );
		require_once( 'admin/highcharts/graph-orders-ca.php' );
		require_once( 'admin/highcharts/graph-orders.php' );
	?>
	<script src="/admin/js/riaSelectors.js?1"></script>
	<script>
		var urlHighcharts = '/admin/stats/orders.php';
		var riadatepicker_upd_url = '';
		<?php view_date_initialized( 0, '', false, array() ); ?>
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
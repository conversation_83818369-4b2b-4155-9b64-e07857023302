<?php
	require_once('admin/get-filters.php');
	require_once('stats.inc.php');
	require_once('categories.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PAYMENT');

	// Paramètre de recherche
	$filter = array();
	if( isset($_GET['prd']) ){
		$filter['prd'] = $_GET['prd'];
	}elseif( isset($_GET['cat']) ){
		$filter['cat'] = $_GET['cat'];
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Moyens de paiement') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Moyens de paiement').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Statistiques des moyens de paiement'); ?></h2>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<?php print view_websites_selector( (isset($wst_id) ? $wst_id : 0), true, 'riapicker', true, 'Tous les sites', false, true ); ?>
		<div class="clear"></div>
	</div>

	<?php view_import_highcharts(); ?>
	<div id="graph-payments"><?php print _('Chargement en cours...'); ?></div>

	<input type="hidden" name="date1" id="date1" value="<?php print htmlspecialchars( $date1 ); ?>" />
	<input type="hidden" name="date2" id="date2" value="<?php print htmlspecialchars( $date2 ); ?>" />

	<script><!--
		$(function(){
			$('#graph-payments').highcharts({
				chart: {
					plotBackgroundColor: null,
					plotBorderWidth: null,
					plotShadow: false
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'moyens-de-paiement'
				},
				title: {
					text: ''
				},
				tooltip: {
					pointFormat: '<b>{point.percentage:.1f}%</b>'
				},
				plotOptions: {
					pie: {
						allowPointSelect: true,
						cursor: 'pointer',
						dataLabels: {
							enabled: true,
							color: '#000000',
							connectorColor: '#000000',
							format: '<b>{point.name}</b>: {point.percentage:.1f} %'
						}
					}
				},
				series: [{
					type: 'pie',
					name: 'Moyens de paiement',
					data: [
					<?php
						// Récupération des statistiques
						$payments = stats_graphs_get_datas( 'payments', $date1, $date2, $filter, -1, $wst_id );
						$first = true;
						foreach( $payments as $key=>$val ){
							if( !$first ){
								print ', ';
							}

							print '[\''.$key.'\', '.$val.']';
							$first = false;
						}
					?>
					]
				}]
			});
		});
	--></script>

	<script><!--
		var urlHighcharts = '/admin/stats/payments.php';
		<?php view_date_initialized( 0, '', array('graph-prd-views','graph-prd-ordered'), array('autoload'=>true) ); ?>
		$(document).ready(function(){
			$('.selector a:not([name="perso"])').mouseup(function(){
				setTimeout(function(){
				date1 = $('#date1').val();
				date2 = $('#date2').val();
				//date1 = date1.substr(6,4) + '-' + date1.substr(3,2) + '-' + date1.substr(0,2);
				//date2 = date2.substr(6,4) + '-' + date2.substr(3,2) + '-' + date2.substr(0,2);
				window.location='payments.php?date1='+date1+'&date2='+date2;
				},50);
			});
			$('#btn_submit').mouseup(function(){
				setTimeout(function(){
				date1 = $('#date1').val();
				date2 = $('#date2').val();
				//date1 = date1.substr(6,4) + '-' + date1.substr(3,2) + '-' + date1.substr(0,2);
				//date2 = date2.substr(6,4) + '-' + date2.substr(3,2) + '-' + date2.substr(0,2);
				window.location='payments.php?date1='+date1+'&date2='+date2;
				},50);
			});
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
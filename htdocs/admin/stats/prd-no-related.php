<?php

	/**	\file prd-no-related.php
	 *	Cette page affiche la liste des produits qui n'ont aucun article lié. Le but
	 *	est d'inciter nos clients à lier un maximum de produits au sein de leur catalogue
	 *	pour favoriser le cross-selling.
	 */

	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_NO_RELATED');

	// Détermine le trie à réaliser
	$dir = 'desc';
	if( isset($_GET['dir']) )
		$dir = $_GET['dir'];

	$sort = 'selled';
	if( isset($_GET['dir'], $_GET['sort']) ){
		$sort = $_GET['sort'];
	}

	$rsort = array( $sort => $dir );

	// Récupère les produits
	$rprd = prd_products_get(0, '', 0, true, 0, 0, -1, false, false, false, false, false, false, $rsort, false, false, false, false, false, false, null, null, false, true, false, false, false, null, false, false, true);

	// Partie réservé à la demande d'exportation
	if( isset($_POST['export']) ){
		require_once('export-prd-no-related.php');
		exit;
	}

	$prd_count = $rprd!=false ? ria_mysql_num_rows($rprd) :  1;

	// Calcule le nombre de pages
	$pages = ceil($prd_count / 35);
	if( $pages==0 ) $pages = 1;

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages )
			$page = $_GET['page'];
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-5;
	if( $pmin<1 ){
		$pmin = 1;
	}
	$pmax = $pmin+9;
	if( $pmax>$pages ){
		$pmax = $pages;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Produits sans articles liés') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Produits sans articles liés').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Produits sans articles liés'); ?> (<?php print ria_number_format($prd_count); ?>)</h2>
	<form action="prd-no-related.php" method="post">
		<table id="prd-no-related" class="tablesorter">
			<thead>
				<tr>
					<th id="prd-ref" class="header<?php print $sort == 'ref' ? ($dir=='asc' ? ' headerSortUp' : ' headerSortDown') : ''; ?>"><a href="prd-no-related.php?<?php print 'page='.$page.'&amp;sort=ref&amp;dir='.($dir=='asc' ? 'desc' : 'asc'); ?>"><?php print _('Référence'); ?></a></th>
					<th id="prd-name" class="header<?php print $sort == 'name' ? ($dir=='asc' ? ' headerSortUp' : ' headerSortDown') : ''; ?>"><a href="prd-no-related.php?<?php print 'page='.$page.'&amp;sort=name&amp;dir='.($dir=='asc' ? 'desc' : 'asc'); ?>"><?php print _('Désignation'); ?></a></th>
					<th id="prd-ht" class="align-right header<?php print $sort == 'price' ? ($dir=='asc' ? ' headerSortUp' : ' headerSortDown') : ''; ?>"><a href="prd-no-related.php?<?php print 'page='.$page.'&amp;sort=price&amp;dir='.($dir=='asc' ? 'desc' : 'asc'); ?>"><?php print _('Prix'); ?> <abbr title="Hors Taxes"><?php print _('HT'); ?></abbr></a></th>
					<th id ="prd-ttc" class="align-right"><?php print _('Prix'); ?> <abbr title="Toutes Taxes Comprises"><?php print _('TTC'); ?></abbr></th>
					<th id="prd-selled" class="align-right header<?php print $sort == 'selled' ? ($dir=='asc' ? ' headerSortUp' : ' headerSortDown') : ''; ?>"><a href="prd-no-related.php?<?php print 'page='.$page.'&amp;sort=selled&amp;dir='.($dir=='asc' ? 'desc' : 'asc'); ?>"><?php print _('Volume de ventes'); ?></a></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td style="text-align:left"><input type="submit" name="export" id="export" value="<?php print _('Exporter'); ?>" /></td>
					<td colspan="4" style="text-align:right">
					<?php
						if( $pages>1 ){
							if( $page>1 )
								print '<a href="prd-no-related.php?page='.($page-1).'&amp;sort='.$sort.'&amp;dir='.$dir.'">&laquo; '._('Page précédente').'</a> | ';
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page )
									print '<b>'.$page.'</b>';
								else
									print '<a href="prd-no-related.php?page='.$i.'&amp;sort='.$sort.'&amp;dir='.$dir.'">'.$i.'</a>';
								if( $i<$pmax )
									print ' | ';
							}
							if( $page<$pages )
								print ' | <a href="prd-no-related.php?page='.($page+1).'&amp;sort='.$sort.'&amp;dir='.$dir.'">'._('Page suivante').' &raquo;</a>';
						}else{
							print '<b>1</b>';
						}
					?>
					</td>
				</tr>
			</tfoot>
			<tbody>
			<?php
				// On affiche les produits
				if( $rprd!=false ){
					if( $page>1 )
						ria_mysql_data_seek( $rprd, ($page-1)*35 );
					$count = 0;
					while( $prd = ria_mysql_fetch_array($rprd) ){
						$count++;
						$rcat = prd_products_categories_get($prd['id'], true);
						print '	<tr>';
						if( $rcat!=false && ria_mysql_num_rows($rcat)>0 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
							$cat = ria_mysql_fetch_array($rcat);
							print '		<td headers="prd-ref">'.view_prd_is_sync($prd).' <a href="/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$prd['id'].'&amp;tab=linked" target="_bank">'.htmlspecialchars($prd['ref']).'</a></td>';
							print '		<td headers="prd-name"><a href="/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$prd['id'].'&amp;tab=linked" target="_bank">'.htmlspecialchars($prd['name']).'</a></td>';
						} else {
							print '		<td headers="prd-ref">'.htmlspecialchars($prd['ref']).'</td>';
							print '		<td headers="prd-name">'.htmlspecialchars($prd['name']).'</td>';
						}
						print '		<td headers="prd-ht" class="right">'.ria_number_format($prd['price_ht'], NumberFormatter::CURRENCY, 2).'</td>';
						print '		<td headers="prd-ttc" class="right">'.ria_number_format($prd['price_ttc'], NumberFormatter::CURRENCY, 2).'</td>';
						print '		<td headers="prd-selled" class="right">'.ria_number_format($prd['selled']).'</td>';
						print '	</tr>';

						if( $count==35 )
							break;
					}
				}
			?>
			</tbody>
		</table>
	</form>
<?php

require_once('admin/skin/footer.inc.php');
<?php

	/**	\file prd-weight-fills.php
	 *	Cette page affiche la liste des produits pour lesquels le poids est manquant. Ils sont classés
	 *	par nombre de ventes décroissant, pour que les plus importants soient traités en premier.
	 */

	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_WEIGHT_FILL');

	// Sauvegarde les poids des produits
	if( isset($_POST['save']) ){

		// Permet d'informer l'utilisateur une erreur sur un produits
		$terror = array();
		foreach($_POST as $post){
			if( is_array($post) ){
				if( ($post['weight']!=="" && !is_numeric($post['weight'])) || ($post['weight-net']!=="" && !is_numeric($post['weight-net'])) || ($post['weight']>0 && $post['weight-net']>0 && $post['weight']<$post['weight-net']) ){
					$terror[] = $post['ref'];
				}else{
					prd_products_update_weight($post['id'], $post['weight'], $post['weight-net']);
				}
			}
		}

		if( sizeof($terror) ){
			$error = _("Une erreur est survenue lors de l'enregistrement ").(sizeof($terror)>1 ? _('des produits suivants') : _('du produit suivant'))." : \n "._('Référence')." - ";
			$error .= implode("\n "._('Référence - '), $terror);
		}
	}

	// Récupère les produits dont le poids brut ou le poids net n'est pas renseigné
	$r_prd = prd_products_get_simple( 0, '', false, 0, false, false, false, array("selled"=>"desc", "name"=>"asc"), array( 'orderable'=>true, 'follow_stock'=>true, 'no_weight'=>true, 'childs' => true) );

	// Bouton Exporter
	if( isset($_POST['export']) ){
		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export-prd-weight.php');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Poids des produits'), '/admin/stats/prd-weight.php' )
		->push( _('Poids des produits à compléter') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Poids des produits à compléter').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');

	// Pagination
	$results_count = ria_mysql_num_rows($r_prd);
	$pages = ceil($results_count/30);

?>
	<h2><?php print _('Poids des produits à compléter'); ?> (<?php print ria_number_format($results_count).' '.( $results_count>1 ? _('produits') : _('produit') ) ?>)</h2>

	<?php
		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}

		// Détermine la page en cours de consultation
		$page = 1;
		if( isset($_GET['page']) && is_numeric($_GET['page']) ){
			if( $_GET['page']>0 && $_GET['page']<=$pages )
				$page = $_GET['page'];
		}

		// Détermine les limites inférieures et supérieures pour l'affichage des pages
		$pmin = $page-5;
		if( $pmin<1 )
			$pmin = 1;
		$pmax = $pmin+9;
		if( $pmax>$pages )
			$pmax = $pages;
	?>

	<form action="prd-weight-fills.php" method="post">
		<table class="prd-weight checklist table-responsive" id="prd-completing">
			<thead>
				<tr>
					<th id="ref"><?php print _('Référence'); ?></th>
					<th id="desc"><?php print _('Désignation'); ?></th>
					<th id="sells" class="align-right"><?php print _('Ventes'); ?></th>
					<th id="weight" class="align-right"><?php print _('Poids Brut'); ?></th>
					<th id="weight-net" class="align-right"><?php print _('Poids Net'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php

					if( $r_prd==false || ria_mysql_num_rows($r_prd)==0 ){
						print '<tr><td colspan="5">'._('Aucun produit avec un poids brut ou net vide.').'</td></tr>';
					} else {

						// Prépare l'affichage des résultats selon la page en cours
						ria_mysql_data_seek( $r_prd, ($page-1)*30 );
						$count = 0;

						while( $prd = ria_mysql_fetch_array($r_prd) ){

							// On affiche que 30 produits par page
							if( $count>30 ){
								break;
							}

							// Titre et description du produit
							$title = $prd['title'] ? $prd['title'] : $prd['name'];
							$desc = $prd['desc'];
							if( strlen($desc)>105 ){
								$desc = substr( $desc, 0, 102 ).'...';
							}

							// Information sur le produits
							print ' <tr>
										<td headers="ref"><input type="hidden" name="'.$prd['id'].'[id]" value="'.$prd['id'].'" /><input type="hidden" name="'.$prd['id'].'[ref]" value="'.$prd['ref'].'" /><a href="/admin/catalog/product.php?cat=0&amp;prd='.$prd['id'].'">'.htmlspecialchars( $prd['ref'] ).'</a></td>
										<td headers="desc"><span class="title">'.htmlspecialchars( $title ).'</span><br /><span class="desc">'.htmlspecialchars( $desc ).'</span></td>
										<td headers="sells" class="stat-weight">'.ria_number_format($prd['selled'] ?: 0).'</td>
										<td headers="weight" class="stat-weight"><input type="text" name="'.$prd['id'].'[weight]" id="w-'.$prd['id'].'" value="'.$prd['weight'].'" /></td>
										<td headers="weight-net" class="stat-weight"><input type="text" name="'.$prd['id'].'[weight-net]" id="wn-'.$prd['id'].'" value="'.$prd['weight_net'].'" /></td>
									</tr>';

							$count++;
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr>
					<td class="align-left">
						<form action="prd-weight-fills.php" method="post">
							<button title="<?php print _('Exporter la liste des produits dont le poids (net et/ou brut) n\'est pas renseigné.'); ?>" class="btn-export" name="export"><?php print _('Exporter'); ?></button>
						</form>
					</td>
					<td colspan="4"><input type="submit" name="save" id="save" value="<?php print _('Enregistrer'); ?>" /></td>
				</tr>
				<?php if( $pages>1 ){ ?>
				<tr id="pagination">
					<td class="align-left"><?php print $page.'/'.$pages; ?></td>
					<td colspan="4">
						<?php
							if( $pages>1 ){
								if( $page>1 )
									print '<a onclick="return prdSwitchTablePage( '.($page-1).', '.$pages.' )" href="prd-weight-fills.php?page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
								for( $i=$pmin; $i<=$pmax; $i++ ){
									if( $i==$page )
										print '<b>'.$page.'</b>';
									else
										print '<a onclick="return prdSwitchTablePage( '.$i.', '.$pages.' )" href="prd-weight-fills.php?page='.$i.'">'.$i.'</a>';
									if( $i<$pmax )
										print ' | ';
								}
								if( $page<$pages )
									print ' | <a onclick="return prdSwitchTablePage( '.($page+1).', '.$pages.' )" href="prd-weight-fills.php?page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
							}

						?>
					</td>
				</tr>
				<?php } ?>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php
	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_WEIGHT');

	// Récupère les produits dont le poids brut ou le poids net n'est pas renseigné. Ces produits doivent être commandables et suivis en stock
	$r_prd = prd_products_get_simple(0, '', false, 0, false, false, false, array('name' => 'asc'), array('orderable' => true, 'childs' => true));

	$nb_prd = 0;
	$w_fills = 0;
	$wn_fills = 0;
	$no_sleep = 0;
	$no_sleep_w = 0;
	$no_sleep_wn = 0;
	$publish = 0;
	$publish_w = 0;
	$publish_wn = 0;

	while( $prd = ria_mysql_fetch_array($r_prd) ){
		if( !in_array($prd['ref'], $config['dlv_prd_references']) ){
			$nb_prd++;
			if( $prd['weight'] )
				$w_fills++;
			if( $prd['weight_net'] )
				$wn_fills++;
			if( !$prd['sleep'] )
				$no_sleep++;
			if( !$prd['sleep'] && $prd['weight'] )
				$no_sleep_w++;
			if( !$prd['sleep'] && $prd['weight_net'] )
				$no_sleep_wn++;
			if( $prd['publish'] && $prd['publish_cat'] && !$prd['sleep'] )
				$publish++;
			if( $prd['publish'] && $prd['publish_cat'] && !$prd['sleep'] && $prd['weight'] )
				$publish_w++;
			if( $prd['publish'] && $prd['publish_cat'] && !$prd['sleep'] && $prd['weight_net'] )
				$publish_wn++;
		}
	}

	// Export Excel
	if( isset($_POST['export']) ){
		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export-prd-weight.php');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Poids des produits'), '/admin/stats/prd-weight.php' );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Poids des produits').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Poids des produits'); ?></h2>

	<form action="prd-weight.php" method="post">
		<p><?php print _('Vous pouvez exporter les poids des produits dans un format Excel :'); ?> <button class="btn-export" name="export"><?php print _('Exporter'); ?></button></p>
	</form>
	<form action="prd-weight.php" method="post" class="table-layout-large">
		<table class="prd-weight">
			<caption><?php print _('Poids Brut'); ?></caption>
			<thead>
				<tr>
					<th colspan="2" class="align-left th-first"><?php print _('Articles'); ?></th>
					<th colspan="2" class="th-first"><?php print _('Renseigné'); ?></th>
					<th colspan="2" class="th-first"><?php print _('Non Renseigné'); ?></th>
				</tr>
				<tr>
					<th id="w-cat" class="align-left th-second"><?php print _('Catégorie'); ?></th>
					<th id="w-tt" class="align-right th-second"><?php print _('Nb'); ?></th>
					<th id="w-tt-fills" class="align-right th-second"><?php print _('Nb'); ?></th>
					<th id="w-pct-fills" class="align-right th-second">%</th>
					<th id="w-tt-no-fills" class="align-right th-second"><?php print _('Nb'); ?></th>
					<th id="w-pct-no-fills" class="align-right th-second">%</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td headers="w-cat"><?php print _('Tous'); ?></td>
					<td headers="w-tt" class="stat-weight"><?php print ria_number_format($nb_prd); ?></td>
					<td headers="w-tt-fills" class="stat-weight"><?php print ria_number_format($w_fills); ?></td>
					<td headers="w-pct-fills" class="stat-weight"><?php print ria_number_format(($nb_prd > 0 ? ($w_fills / $nb_prd) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="w-tt-no-fills" class="stat-weight"><?php print ria_number_format($nb_prd - $w_fills); ?></td>
					<td headers="w-pct-no-fills" class="stat-weight"><?php print ria_number_format(($nb_prd > 0 ? (($nb_prd - $w_fills) / $nb_prd) : 0), NumberFormatter::PERCENT, 2); ?></td>
				</tr>
				<tr>
					<td headers="w-cat"><?php print _('Non en sommeil'); ?></td>
					<td headers="w-tt" class="stat-weight"><?php print ria_number_format($no_sleep); ?></td>
					<td headers="w-tt-fills" class="stat-weight"><?php print ria_number_format($no_sleep_w); ?></td>
					<td headers="w-pct-fills" class="stat-weight"><?php print ria_number_format(($no_sleep > 0 ? ($no_sleep_w / $no_sleep) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="w-tt-no-fills" class="stat-weight"><?php print ria_number_format($no_sleep-$no_sleep_w); ?></td>
					<td headers="w-pct-no-fills" class="stat-weight"><?php print ria_number_format($no_sleep > 0 ? (($no_sleep-$no_sleep_w) / $no_sleep) : 0, NumberFormatter::PERCENT, 2); ?></td>
				</tr>
				<tr>
					<td headers="w-cat"><?php print _('Publiés sur le site marchand et non en sommeil'); ?></td>
					<td headers="w-tt" class="stat-weight"><?php print ria_number_format($publish); ?></td>
					<td headers="w-tt-fills" class="stat-weight"><?php print ria_number_format($publish_w); ?></td>
					<td headers="w-pct-fills" class="stat-weight"><?php print ria_number_format(($publish > 0 ? ($publish_w / $publish) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="w-tt-no-fills" class="stat-weight"><?php print ria_number_format($publish - $publish_w); ?></td>
					<td headers="w-pct-no-fills" class="stat-weight"><?php print ria_number_format($publish > 0 ? (($publish - $publish_w) / $publish) : 0, NumberFormatter::PERCENT, 2); ?></td>
				</tr>
			</tbody>
		</table>

		<table class="prd-weight" id="prd-weight-net">
			<caption><?php print _('Poids Net'); ?></caption>
			<thead>
				<tr>
					<th colspan="2" class="align-left th-first"><?pHp print _('Articles'); ?></th>
					<th colspan="2" class="th-first"><?pHp print _('Renseigné'); ?></th>
					<th colspan="2" class="th-first"><?pHp print _('Non Renseigné'); ?></th>
				</tr>
				<tr>
					<th id="wn-cat" class="align-left th-second"><?pHp print _('Catégorie'); ?></th>
					<th id="wn-tt" class="align-right th-second"><?pHp print _('Nb'); ?></th>
					<th id="wn-tt-fills" class="align-right th-second"><?pHp print _('Nb'); ?></th>
					<th id="wn-pct-fills" class="align-right th-second">%</th>
					<th id="wn-tt-not-fills" class="align-right th-second"><?pHp print _('Nb'); ?></th>
					<th id="wn-pct-not-fills" class="align-right th-second">%</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td headers="wn-cat"><?php print _('Tous'); ?></td>
					<td headers="wn-tt" class="stat-weight"><?php print ria_number_format($nb_prd); ?></td>
					<td headers="wn-tt-fills" class="stat-weight"><?php print ria_number_format($wn_fills); ?></td>
					<td headers="wn-pct-fills" class="stat-weight"><?php print ria_number_format(($nb_prd > 0 ? ($wn_fills/$nb_prd) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="wn-tt-no-fills" class="stat-weight"><?php print ria_number_format($nb_prd-$wn_fills); ?></td>
					<td headers="wn-pct-no-fills" class="stat-weight"><?php print ria_number_format($nb_prd > 0 ? (($nb_prd - $wn_fills) / $nb_prd) : 0, NumberFormatter::PERCENT); ?></td>
				</tr>
				<tr>
					<td headers="wn-cat"><?php print _('Non en sommeil'); ?></td>
					<td headers="wn-tt" class="stat-weight"><?php print ria_number_format($no_sleep); ?></td>
					<td headers="wn-tt-fills" class="stat-weight"><?php print ria_number_format($no_sleep_wn); ?></td>
					<td headers="wn-pct-fills" class="stat-weight"><?php print ria_number_format(($no_sleep > 0 ? ($no_sleep_wn / $no_sleep) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="wn-tt-no-fills" class="stat-weight"><?php print ria_number_format($no_sleep-$no_sleep_wn); ?></td>
					<td headers="wn-pct-no-fills" class="stat-weight"><?php print ria_number_format(($no_sleep > 0 ? (($no_sleep - $no_sleep_wn) / $no_sleep) : 0), NumberFormatter::PERCENT, 2); ?></td>
				</tr>
				<tr>
					<td headers="wn-cat"><?php print _('Publiés sur le site marchand et non en sommeil'); ?></td>
					<td headers="wn-tt" class="stat-weight"><?php print ria_number_format($publish); ?></td>
					<td headers="wn-tt-fills" class="stat-weight"><?php print ria_number_format($publish_wn); ?></td>
					<td headers="wn-pct-fills" class="stat-weight"><?php print ria_number_format(($publish > 0 ? ($publish_wn / $publish) : 0), NumberFormatter::PERCENT, 2); ?></td>
					<td headers="wn-tt-no-fills" class="stat-weight"><?php print ria_number_format($publish-$publish_wn); ?></td>
					<td headers="wn-pct-no-fills" class="stat-weight"><?php print ria_number_format($publish > 0 ? (($publish - $publish_wn) / $publish ) : 0, NumberFormatter::PERCENT, 2); ?></td>
				</tr>
			</tbody>
		</table>
	</form>
<?php

require_once('admin/skin/footer.inc.php');
<?php
use Riashop\PriceWatching\models\LinearRaised\Stats\OffersRaised;

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_LINEAR_RAISED');

$error = _("Une erreur est survenue lors du chargement des données. Veuillez réessayer ou prendre contact avec l'administrateur.");
$depts = $customers = array();
try {
	if (isset($_GET['fr_depts']) && trim($_GET['fr_depts']) != '') {
		$_SESSION['fr_depts'] = $_GET['fr_depts'];
	}else{
		if (isset($_SESSION['fr_depts']) && trim($_SESSION['fr_depts']) != '') {
			$_GET['fr_depts'] = $_SESSION['fr_depts'];
		}
	}

	if (isset($_GET['sellers'])) {
		$_SESSION['ord_seller_id'] = $_GET['sellers'];
	}else{
		if (isset($_SESSION['ord_seller_id']) && ($_SESSION['ord_seller_id'])) {
			$_GET['sellers'] = $_SESSION['ord_seller_id'];
		}
	}

	if (isset($_GET['date2']) && isdateheure($_GET['date2']) && isset($_GET['date1']) && isdateheure($_GET['date1']) ) {
		view_date_in_session($_GET['date1'], $_GET['date2']);
	}

	if (isset($_SESSION['datepicker_date1'])) {
		$_GET['date1'] = $_SESSION['datepicker_date1'];
	}else{
		$_GET['date1'] = $_SESSION['datepicker_date1'] = date('d/m/Y');
	}

	if (isset($_SESSION['datepicker_date2'])) {
		$_GET['date2'] = $_SESSION['datepicker_date2'];
	}else{
		$_GET['date2'] = $_SESSION['datepicker_date2'] = date('d/m/Y');
	}

	$OffersRaised = new OffersRaised();
	$OffersRaised->setModeDashboard();

	if (isset($_GET['sort'])) {
		$OffersRaised->sortBy($_GET['sort']);
	}

	if (isset($_GET['date1'])&& isdateheure($_GET['date1'])) {
		$date1 = new DateTime(dateheureparse($_GET['date1']));
		$OffersRaised->forPeriodStart($date1);
	}
	if (isset($_GET['date2'])&& isdateheure($_GET['date2'])) {
		$date2 = new DateTime(dateheureparse($_GET['date2']));
		$OffersRaised->forPeriodEnd($date2);
	}

	if (isset($_GET['sellers']) && $_GET['sellers'] > 0) {
		$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $_GET['sellers']);
		if ($r_seller && ria_mysql_num_rows($r_seller)) {
			$seller = ria_mysql_fetch_assoc($r_seller);
			$OffersRaised->withSellers($seller['id']);
			$result = gu_users_seller_customers_get($seller['id']);
			if( $result ){
				$customers = $result;
			}
		}
	}

	if (isset($_GET['fr_depts']) && trim($_GET['fr_depts']) != '' && is_numeric($_GET['fr_depts']) && $_GET['fr_depts'] != 0) {
		$r_ids = gu_users_get_id_by_dept($_GET['fr_depts'], array(PRF_CUST_PRO));
		if ($r_ids && ria_mysql_num_rows($r_ids)) {
			while ($user = ria_mysql_fetch_assoc($r_ids)) {
				$depts[] = $user['id'];
			}
		}
	}
	if( !empty($depts) ){
		$customers = array_diff($depts, $customers);
		if( empty($customers) ){
			$error = null;
			throw new Exception("Aucun compte client pour le département :".$_GET['fr_depts']);
		}
	}
	if( !empty($customers) ){
		$OffersRaised->withCustomers($customers);
	}

	$r = $OffersRaised->queryDashboard();
	$centrals = ria_mysql_fetch_assoc_all($r);
	$error = null;
} catch (Exception $e) {
	error_log($e);
}


$total_stores = $stores = 0;

$filter_data = array(
	'sort' => ria_array_get($_GET, 'sort', array()),
	'sellers' => ria_array_get($_GET, 'sellers', 0),
	'fr_depts' =>  ria_array_get($_GET, 'fr_depts', 0),
	'date1' =>  ria_array_get($_GET, 'date1', null),
	'date2' =>  ria_array_get($_GET, 'date2', null),
);

$query = $filter_data;
unset($query['sort']);
ob_start();
?>
<table class="checklist tablesorter">
	<thead>
		<tr>
			<th class="header<?php echo isset($filter_data['sort']['central']) ? ($filter_data['sort']['central'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'central', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="central" title="<?php echo _('Nom de la centrale')?>">
				<span><?php echo _('Centrale d\'achat')?></span>
			</th>
			<th class="col120px header<?php echo isset($filter_data['sort']['total_stores']) ? ($filter_data['sort']['total_stores'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'total_stores', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="total_stores" title="<?php echo _('Nombre de magasins rattachés à cette centrale')?>">
				<span><?php echo _('Magasins')?></span>
			</th>
			<th class="col120px header<?php echo isset($filter_data['sort']['stores']) ? ($filter_data['sort']['stores'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'stores', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="stores" title="<?php echo _('Nombre de magasins ayant été relevés')?>">
				<span><?php echo _('Relevés')?></span>
			</th>
			<th class="col120px header<?php echo isset($filter_data['sort']['cover']) ? ($filter_data['sort']['cover'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'cover', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="cover" title="<?php echo _('% des magasins ayant été relevés')?>">
				<span><?php echo _('Couverture')?></span>
			</th>
		</tr>
	</thead>
	<tbody>
		<?php if (empty($centrals)) { ?>
			<tr>
				<td colspan="4"><?php print _('Aucune centrale'); ?></td>
			</tr>
		<?php }else{
			foreach ($centrals as $central) {
				$total_stores += $central['total_stores'];
				$stores += $central['stores'];
				$query['central'] = $central['central'];
				?>
				<tr>
					<td><a href="/admin/stats/linear-raised.php?<?php echo http_build_query($query)?>"><?php echo htmlspecialchars($central['central'])?></a></td>
					<td class="number"><?php echo ria_number_format($central['total_stores'])?></td>
					<td class="number"><?php echo ria_number_format($central['stores'])?></td>
					<td class="number"><?php echo ria_number_format($central['cover'], NumberFormatter::PERCENT, 2)?></td>
				</tr>
			<?php } ?>
		<?php } ?>
	</tbody>
	<?php if (!empty($centrals)) { ?>
		<tfoot>
			<tr>
				<td><?php echo _('Total : ')?></td>
				<td class="number"><?php echo ria_number_format($total_stores)?></td>
				<td class="number"><?php echo ria_number_format($stores) ?></td>
				<td class="number"><?php echo ria_number_format($total_stores > 0 ? $stores / $total_stores : 0, NumberFormatter::PERCENT, 2) ?></td>
			</tr>
		</tfoot>
	<?php } ?>
</table>
<?php
$table = ob_get_clean();

if (IS_AJAX) {
	$response = array('table' => $table, 'error' => $error);
	echo json_encode($response);
	exit;
}
?>

<?php

// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Statistiques'), '/admin/stats/index.php' )
	->push( _('Relevés de linéaires'), '/admin/stats/linear-raised/index.php' )
	->push( _('Tableau de bord') );

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Tableau de bord des relevés de linéaires'));
require_once('admin/skin/header.inc.php');

?>
<?php if (!is_null($error)) {?>
	<div class="flash_message error"><?php echo htmlspecialchars( $error ); ?></div>
<?php }else{ ?>
	<div class="flash_message">
	</div>
<?php } ?>
<h2><?php echo _('Tableau de bord')?></h2>
<div>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<input type="hidden" name="date1" id="date1" />
		<input type="hidden" name="date2" id="date2" />
		<?php echo view_sellers_selector();?>
		<?php echo view_users_fr_depts_selector(array(PRF_CUST_PRO));?>
		<div class="clear"></div>
	</div>
	<div class="ajax-notice">
			<?php // echo $notice ?>
	</div>
	<div class="ajax-container">
			<?php echo $table ?>
	</div>
</div>
<script>
	<?php view_date_initialized( 0, '', false, array() ); ?>
	var container =  $('.ajax-container');
	var is_pop_state = false;
	var modified = false;
	var data = JSON.parse('<?php echo json_encode($filter_data)?>');
	var initialState = $.extend({},data);

	var delay = (function(delay){
		var out = delay;
		return function(callback, timeout) {
			clearTimeout(out);
			out = setTimeout(callback, timeout);
		}
	})(null);

	container.on('click', '.checklist thead th.header', function (e) {
		var sort = {};
		sort[$(this).data('filter')] = $(this).data('dir');
		modified = true;
		data.sort = sort;
		is_sorting = true;
		fetch(data)
		if ($(this).data('dir') === 'asc') {
			$(this).data('dir', 'desc');
		}else{
			$(this).data('dir', 'asc');
		}
	})

	$('.riapicker').on('click', function(){
		if($('.selector', $(this)).css('display')=='none'){
			$('.selector').hide();
			$('.selector',  $(this)).show();
		}else{
			$('.selector',  $(this)).hide();
		}
	})

	$('#selectseller .selector a').on('click', function(){
		$('#selectseller .view').text($(this).text());
		$('.selector', $(this)).hide();
		var value = $(this).attr('name').replace('seller-', '');
		isModified('sellers', value);
		data.sellers = value;
		fetch(data);
	});

	$('#select_fr_depts .selector a').on('click', function(){
		$('#select_fr_depts .view').text($(this).text());
		$('.selector', $(this)).hide();
		var value = $(this).attr('name').replace('fr_dept-', '');
		isModified('fr_depts', value);
		data.fr_depts = value;
		fetch(data);
	});

	$('#date1').on('change', function(){
		isModified('date1', $(this).val());
		data.date1 = $(this).val();
		fetch(data);
	})

	$('#date2').on('change', function(){
		data.date1 = $(this).val();
		fetch(data);
	})

	$('#riadatepicker').on('click', '.selector a', function(){
		if ($(this).attr('name') == 'perso') {
			$("#btn_submit").on('click', function(){
				update_dates();
			});
			return;
		}

		update_dates();
	});
	function isModified(key, value) {
		return data[key] != value;
	}
	function update_dates () {
		delay(function () {
			if (!isModified('date1', $('#date1').val())){
				isModified('date2', $('#date2').val());
			}
			data.date1 = $('#date1').val();
			data.date2 = $('#date2').val();
			fetch(data);
		}, 10);
	}

	window.onpopstate = function(event) {
		modified = true;
		is_pop_state = true;
		if (event.state == null) {
			fetch(initialState);
		}else{
			fetch(event.state);
		}
	};

	function fetch(data) {
		if (modified) {
			delay(function(){
				$('#popup_ria_shadow').show();
				$.ajax({
					url: '/admin/stats/linear-raised/dashboard.php',
					data: $.extend({}, {action: 'stats'}, data),
				}).done(function(response){
					var json = JSON.parse(response);
					if (json.error == null) {
						$('.flash_message').removeClass('error').text('');
					}else{
						$('.flash_message').text(json.error).addClass('error');
					}
					container.html(json.table);
				}).always(function(){
					if (typeof window.history !== 'undefined' && !is_pop_state) {
						history.pushState(data, "<?php echo ADMIN_PAGE_TITLE ?>", location.pathname+ '?'+$.param(data));
					}
					modified = false;
					is_pop_state = false;
					$('#popup_ria_shadow').hide();
				});
			}, 800);
		}
	}
</script>
<?php
require_once('admin/skin/footer.inc.php');

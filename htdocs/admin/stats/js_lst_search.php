<?php

	/**	\file js_lst_search.php
	 *	Cette page est appelée en Ajax par la page /admin/stats/search.php pour rafraîchir les statistiques de recherche.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');

 
header('Content-type: application/xml');

if( isset($_GET['wst'], $_GET['date1'], $_GET['date2'])){
	
	$xml = '<?xml version="1.0" encoding="utf8"?>
			<success>';
	
	$stats = search_log_get($_GET['wst'], $_GET['date1'], $_GET['date2'], false, false, array(), 0, ( isset($_GET['filter']) ? $_GET['filter'] : ''), '', false);
	
	require_once('view.admin.inc.php');
	view_date_in_session($_GET['date1'], $_GET['date2'], $_GET['wst']);
	
	// Ajout de la synthèses des stats 
	$volumes = $average_results = $statscount = 0;
	$statscount = ria_mysql_num_rows($stats);
	if(ria_mysql_num_rows($stats)>0) {
		$volumes = 0;
		$searchs_results = array();
		while($stat = ria_mysql_fetch_array($stats))
		{
			$volumes += $stat['volume'];
			if(isset($searchs_results[$stat['results']])) {
				$searchs_results[$stat['results']] = $searchs_results[$stat['results']] + $stat['volume'];
				
			}
			else {
				$searchs_results[$stat['results']] = $stat['volume'];
			}
			
		}
	
		$a = $b =0;
		foreach($searchs_results as $key => $val)
		{
			$a += $key*$val;
			$b += $val; 
		}
		if($b != 0)
			$average_results = round($a/$b);
		ria_mysql_data_seek($stats,0);
		ria_mysql_data_seek($stats,0);
	}
	
	$htmlSynthese = '';
	$htmlSynthese.= '<tr>';
	$htmlSynthese.= '<td headers="hd-search-total">'.number_format($volumes,0,"."," ").'</td>';
	$htmlSynthese.= '<td headers="hd-search-unique">'.number_format($statscount,0,"."," ").'</td>';
	$htmlSynthese.= '<td headers="hd-search-wo">'.(isset($searchs_results[0]) ? number_format($searchs_results[0],0,"."," ") : 0).'</td>';
	$htmlSynthese.= '<td headers="hd-search-average">'.number_format($average_results,0,"."," ").'</td>';
	$htmlSynthese.= '</tr>';
	$xml .= '<synthese><![CDATA[';
	$xml .= $htmlSynthese;
	$xml .= ']]></synthese>';
	
	if(!ria_mysql_num_rows($stats)){
		$xml .= '<nosearch/>';
	}else {
		$p = 1;
		isset($_GET['page']) ? $p = $_GET['page'] : $p = 1;
		$xml .= '<number nbpage="'.ceil(ria_mysql_num_rows($stats)/25).'" actual="'.$p.'"/>';

		ria_mysql_data_seek( $stats, ($p-1)*25 );
		
		$count = 0;
		while($s = ria_mysql_fetch_array($stats)){
		
			if($count > 24) continue;

			$s['nb_click'] = search_clickthroughs_get_count( $s['seg'], $s['scc'] );
			
			// Détermine les types de recherches
			$types = search_log_get_types($s['scc']);
			
			// Détermine la catégorie de recherches
			$section = 'Toutes';
			$id_section = '';
			if( $s['section']!==null ){
				$r_section = ria_mysql_fetch_array( prd_categories_get($s['section']) );
				$section = $r_section['name'];
				$id_section = $r_section['id'];
			}
	
			$xml.= '<search 
				volume="'.number_format($s['volume'],0,"."," ").'" 
				nbres="'.number_format($s['results'],0,"."," ").'" 
				'.( $_GET['wst']<=0 ? ' site="'.$s['wst_id'].'"' : '' ).'
				avg="'.str_replace(",00","",number_format($s['avg_page'],2,","," ")).'"
				scc="'.$s['scc'].'"
				seg="'.$s['seg'].'"
				section="'.$section.'"
				id_section="'.$id_section.'"
				types="'.$types.'"
				nbClick="'.str_replace(",00","",number_format($s['nb_click']/($s['volume'] > 0 ? $s['volume'] : 1),2,","," ")).'"
				search="'.$s['seg_name'].'"
				lng="'.$s['lng'].'"
				wst="'.$s['wst_id'].'"
			><![CDATA['.htmlspecialchars( $s['search'] ).']]></search>';
		
			$count ++;
		}
	}
	$xml .= '</success>';
	
	print $xml;
}

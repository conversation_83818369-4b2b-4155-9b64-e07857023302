<?php

	/**	\file search-substitut.php
	 *	Cette page liste les redirections de recherche configurées. Ces redirections permettent
	 *	de rediriger un internaute ayant fait une recherche sans résultat à cause d'un problème d'orthographe ou de synonyme
	 *	vers une recherche avec des résultats.
	 */

	require_once('search.inc.php');
	require_once('websites.inc.php');
	require_once('tenants.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT');

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Charge la liste des sites gérés depuis ce back-office (détermine si on a besoin d'un sélecteur)
	$websites = wst_websites_get();

	if( ria_mysql_num_rows($websites)>1 ){
		if( isset($_GET['wst']) ){
			if( $_GET['wst']=='all' ){
				$_SESSION['websitepicker'] = false;
			}else{
				$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
			}
		}
	}

	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;
	$filter = isset($_POST['filter']) && trim($_POST['filter']) ? $_POST['filter'] : false;

	$columns = array();

	$columns[] = array(
		'id' => 'sst-search',
		'code' => 'search',
		'name' => _('Recherche'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);
	$columns[] = array(
		'id' => 'sst-wst-name',
		'code' => 'wst_name',
		'name' => _('Site web'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);
	$columns[] = array(
		'id' => 'sst-seg-name',
		'code' => 'seg_name',
		'name' => _('Emplacement de la recherche'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);
	$columns[] = array(
		'id' => 'sst-substitut',
		'code' => 'substitut',
		'name' => _('Remplacée par'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);
	$columns[] = array(
		'id' => 'sst-created',
		'code' => 'date_created',
		'name' => _('Créée le'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);
	$columns[] = array(
		'id' => 'sst-modified',
		'code' => 'date_modified',
		'name' => _('Modifiée le'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);

	$sort = isset($_POST['sort']) ? $_POST['sort'] : (isset($_GET['sort']) ? $_GET['sort'] : 'search');
	$dir = isset($_POST['dir']) ? $_POST['dir'] : (isset($_GET['dir']) ? $_GET['dir'] : 'asc');

	// Bouton Supprimer
	if (isset($_POST['del-redir-search']) && isset($_POST['substituts']) && is_array($_POST['substituts'])) {

		if (!gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_DEL')) {
			$error = _('Vous n\'êtes pas autorisé à supprimer une redirection de recherche.');
		} else {
			$res = true;
			foreach ($_POST['substituts'] as $subs) {
				$res &= search_substitut_del('', $lng, $wst_id === false ? 0 : $wst_id, 0, $_POST['substituts']);
			}
			if (!$res) {
				$error = _('Une erreur est survenue lors de la suppression des redirections de recherche sélectionnées.');
			}
		}

		if (!isset($error)) {
			header('Location: /admin/stats/search-substitut.php');
			exit;
		}
	}

	// recherche des resultats
	$results = search_substitut_get(false === $filter ? false : $filter . '%', $lng, $sort, $dir, $wst_id ? $wst_id : 0);

	$count = ria_mysql_num_rows($results) ? ria_mysql_num_rows($results) : 0;

	// Calcule le nombre de pages
	$max_results = 25;
	$pages = ceil($count / $max_results);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 && $_GET['page'] <= $pages ){
		$page = intval($_GET['page']);
	}
	if (isset($_POST['page']) && is_numeric($_POST['page']) && $_POST['page'] > 0 && $_POST['page'] <= $pages) {
		$page = intval($_POST['page']);
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-5;
	if( $pmin<1 )
		$pmin = 1;
	$pmax = $pmin+9;
	if( $pmax>$pages )
		$pmax = $pages;

	$colspan = sizeof($columns);
	if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_DEL')) {
		$colspan++;
	}
	if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_UPD')) {
		$colspan++;
	}


	if (!IS_AJAX) {
		// Fil d'ariane
		Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
			->push( _('Statistiques'), '/admin/stats/index.php' )
			->push( _('Recherches'), '/admin/stats/search.php' )
			->push( _('Redirections de recherche') );

		// Défini le titre de la page
		define('ADMIN_PAGE_TITLE', _('Redirections de recherche').' - '._('Statistiques'));
		require_once('admin/skin/header.inc.php');
		?>
		<h2><?php print _('Redirections de recherche'); ?> (<?php print ria_number_format($count) ?>)</h2>
		<?php
		// Affichage des messages d'erreur
		if (isset($error)) {
			print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
		}
		?>
		<div class="stats-menu">
			<?php
				print view_websites_selector( $wst_id, true, 'riapicker', true );
				print view_translate_menu( '/admin/stats/search-substitut.php', $lng, true ).
					'   <input type="hidden" name="sort" value="'.htmlspecialchars($sort).'"/>
						<input type="hidden" name="dir" value="'.htmlspecialchars($dir).'"/>';
			?>

			<div class="clear"></div>
		</div>
		<?php
	}
	?>
<form method="post" class="search-substitut-form">
	<table class="checklist statsearch tablesorter" id="table_search">
		<thead>
			<tr class="head-filter">
				<th class="head-first" colspan="<?php print $colspan; ?>">
					<label for="filter"><?php print _('Filtrer (débutant par) :'); ?> </label><input type="text" name="filter" id="filter" value="<?php print false === $filter ? '' : htmlspecialchars($filter) ?>" />
				</th>
			</tr>
			<tr id="table_search_col">
				<?php if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_DEL')) { ?>
				<th class="td-check"><input type="checkbox" class="all-redir-search" name="all-redir-search" /></th>
				<?php }

				foreach ($columns as $column) {
					$class = '';
					$dataDir = 'desc';
					if ($column['sortable']) {
						$class .= 'header';
						$dirlink = $sort==$column['code'] && $dir=='asc' ? 'desc' : 'asc';
						if ($sort == $column['code']) {
							$class .= $dir == 'desc' ? ' headerSortDown' : ' headerSortUp';
							$dataDir = $dir == 'desc' ? 'asc' : 'desc';
						}
					}
					print '<th id="' . $column['id'] . '" class="' . $class . '" title="' . htmlspecialchars($column['title']) . '">';
					if ($column['sortable']) {
						print '<a class="sort-redir-search" href="search-substitut.php?sort='.$column['code'].'&amp;dir='.$dirlink.'">';
					}
					print htmlspecialchars($column['name']);
					if ($column['sortable']) {
						print '</a>';
					}
					print '</th>';
				}
				?>
				<?php if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_UPD')) { ?>
				<th>&nbsp;</th>
				<?php } ?>
			</tr>
		</thead>
		<tbody id="lst_search">
		<?php

			if( ria_mysql_num_rows($results) ){

				ria_mysql_data_seek( $results, ($page-1)*$max_results );

				$i = 0;
				while( $result = ria_mysql_fetch_array($results) ){
					if($i >= $max_results) break;

					print '<tr>';
					if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_DEL')) {
						print '<td><input type="checkbox" class="check-redir-search" name="substituts[]" value="' . htmlspecialchars($result['id']) . '" /></td>';
					}

					foreach( $columns as $column ){
						if( $column['code'] == 'date_modified' || $column['code'] == 'date_created' ){
							print ' <td>'. htmlspecialchars(ria_date_format($result[$column['code']])) .'</td>';
						}else{
							print ' <td>' . htmlspecialchars($result[$column['code']]). '</td>';
						}
					}

					if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_UPD')) {
						print '<td>
							<input type="button" class="btn-upd-redir-search"
								data-id="' . htmlspecialchars($result['id']) . '"
								data-wst="' . htmlspecialchars($result['wst_id']) . '"
								value="' . _('Modifier') . '"
							/>
						</td>';
					}
					print '</tr>';

					$i++;
				}
			}else{
				?>
				<tr><td colspan="<?php print $colspan; ?>"><?php print _('Aucun résultat'); ?></td></tr>
		<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2" class="td-actions align-left">
					<?php if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_ADD')) { ?>
					<input type="button" name="add-redir-search" id="btn-add-redir-search" value="<?php print _('Ajouter'); ?>" title="<?php print _('Ajouter une redirection de recherche'); ?>" />
					<?php }
						if (gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_DEL')) { ?>
					<input type="button" class="del-redir-search none" name="del-redir-search"
						value="<?php print _('Supprimer'); ?>"
						title="<?php print _('Supprimer les redirections de recherche sélectionnées'); ?>"
						data-confirm="<?php print _('Êtes-vous sûr de vouloir supprimer les redirections sélectionnées ?') ?>"
						/>
					<?php } ?>
				</td>
				<td id="pagination" colspan="<?php print $colspan-2; ?>">
					<?php
						if( $pages>1 ){
							if( $page>1 ){
								print '<a href="search-substitut.php?page='.($page-1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'&amp;sort='.$sort.'&amp;dir='.$dir.'">&laquo; '._('Page précédente').'</a> | ';
							}
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page ){
									print '<b>'.$page.'</b>';
								}else{
									print '<a href="search-substitut.php?page='.$i.(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'&amp;sort='.$sort.'&amp;dir='.$dir.'">'.$i.'</a>';
								}
								if( $i<$pmax ){
									print ' | ';
								}
							}
							if( $page<$pages ){
								print ' | <a href="search-substitut.php?page='.($page+1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'&amp;sort='.$sort.'&amp;dir='.$dir.'">'._('Page suivante').' &raquo;</a>';
							}
						}
					?>
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<?php

if (!IS_AJAX) {
	require_once('admin/skin/footer.inc.php');
}
?>

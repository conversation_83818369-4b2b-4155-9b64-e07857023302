<?php
use Riashop\PriceWatching\models\LinearRaised\prw_offers;
use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;
use Riashop\PriceWatching\models\LinearRaised\Stats\OffersRaised;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\PriceWatching\models\LinearRaised\LinearRaisedGetter;

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_LINEAR_RAISED');

$error = _("Une erreur est survenue lors du chargement des données. Veuillez réessayer ou prendre contact avec l'administrateur.");

$is_sorting_request = isset($_GET['is_sorting']) && $_GET['is_sorting'] == 'true';
try{
	if (isset($_GET['central']) && trim($_GET['central']) != '') {
		$_SESSION['central_id'] = $_GET['central'];
	}else{
		if (isset($_SESSION['central_id']) && trim($_SESSION['central_id']) != '') {
			$_GET['central'] = $_SESSION['central_id'];
		}
	}

	if (isset($_GET['fr_depts']) && trim($_GET['fr_depts']) != '') {
		$_SESSION['fr_depts'] = $_GET['fr_depts'];
	}else{
		if (isset($_SESSION['fr_depts']) && trim($_SESSION['fr_depts']) != '') {
			$_GET['fr_depts'] = $_SESSION['fr_depts'];
		}
	}

	if (isset($_GET['sellers'])) {
		$_SESSION['ord_seller_id'] = $_GET['sellers'];
	}else{
		if (isset($_SESSION['ord_seller_id']) && ($_SESSION['ord_seller_id'])) {
			$_GET['sellers'] = $_SESSION['ord_seller_id'];
		}
	}

	if (isset($_GET['date2']) && isdateheure($_GET['date2']) && isset($_GET['date1']) && isdateheure($_GET['date1']) ) {
		view_date_in_session($_GET['date1'], $_GET['date2']);
	}

	if (isset($_SESSION['datepicker_date1'])) {
		$_GET['date1'] = $_SESSION['datepicker_date1'];
	}else{
		$_GET['date1'] = $_SESSION['datepicker_date1'] = date('d/m/Y');
	}

	if (isset($_SESSION['datepicker_date2'])) {
		$_GET['date2'] = $_SESSION['datepicker_date2'];
	}else{
		$_GET['date2'] = $_SESSION['datepicker_date2'] = date('d/m/Y');
	}


	$total_survey = $user_count = 0;
	$depts = $customers = array();

	//initialisation de la requète
	$per_pages = 50;

	$OffersRaised = new OffersRaised();
	$LinearRaisedGetter = new LinearRaisedGetter;
	$LinearRaisedGetter->withDistinctUsers(true);

	if (isset($_GET['sort'])) {
		$OffersRaised->sortBy($_GET['sort']);
	}

	if (isset($_GET['ref']) && trim($_GET['ref'])) {
		$OffersRaised->productRefLike(strtolower2($_GET['ref']));
	}

	$OffersRaised->paginate(ria_array_get($_GET,'page', 1), $per_pages);

	if (isset($_GET['date1'])&& isdateheure($_GET['date1'])) {
		$date1 = new DateTime(dateheureparse($_GET['date1']));
		$LinearRaisedGetter->from($date1);
		$OffersRaised->forPeriodStart($date1);
	}
	if (isset($_GET['date2'])&& isdateheure($_GET['date2'])) {
		$date2 = new DateTime(dateheureparse($_GET['date2']));
		$LinearRaisedGetter->to($date2);
		$OffersRaised->forPeriodEnd($date2);
	}

	if (isset($_GET['sellers']) && $_GET['sellers'] > 0) {
		$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $_GET['sellers']);
		if ($r_seller && ria_mysql_num_rows($r_seller)) {
			$seller = ria_mysql_fetch_assoc($r_seller);
			$OffersRaised->withSellers($seller['id']);
			$LinearRaisedGetter->withAuthorId($seller['id']);
			$result = gu_users_seller_customers_get($seller['id'], false, array(), $_SESSION['usr_prf_id'] == PRF_ADMIN);
			if( $result ){
				$customers = $result;
			}
		}
	}
	if (isset($_GET['central']) && trim($_GET['central']) != '' && $_GET['central'] != 0) {
		$OffersRaised->withCentral($_GET['central']);
		$LinearRaisedGetter->withCentral($_GET['central']);
	}

	if (isset($_GET['fr_depts']) && trim($_GET['fr_depts']) != '' && is_numeric($_GET['fr_depts']) && $_GET['fr_depts'] != 0) {
		$r_ids = gu_users_get_id_by_dept($_GET['fr_depts'], array(PRF_CUST_PRO));
		if ($r_ids && ria_mysql_num_rows($r_ids)) {
			while ($user = ria_mysql_fetch_assoc($r_ids)) {
				$depts[] = $user['id'];
			}
		}
	}
	if( !empty($depts) ){
		$customers = array_diff($depts, $customers);
		if( empty($customers) ){
			$error = null;
			throw new Exception("aucun compte client pour le département :".$_GET['fr_depts']);
		}
	}
	if( !empty($customers) ){
		$LinearRaisedGetter->withUserId($customers);
		$OffersRaised->withCustomers($customers);
	}

	// Filtre les relevés linéaires sur des champs avancés liés aux clients
	if( isset($_GET['selector_fld']) && is_array($_GET['selector_fld']) ){
		$OffersRaised->withUserFields( $_GET['selector_fld'] );
		$LinearRaisedGetter->withUserFields( $_GET['selector_fld'] );
	}

	if( !$is_sorting_request ){
		$r_plr = $LinearRaisedGetter->query();
		if( $r_plr && ria_mysql_num_rows($r_plr) ){
			$result_survey = ria_mysql_fetch_assoc($r_plr);
			$total_survey = $result_survey['count'];
		}

		$r_users = $OffersRaised->queryTotalUsers();
		if( $r_users && ria_mysql_num_rows($r_users) ){
			$result_userscount = ria_mysql_fetch_assoc($r_users);
			$user_count = $result_userscount['count'];
		}
	}

	$r = $OffersRaised->query();

	$count = $OffersRaised->count();
	$prd_offers = ria_mysql_fetch_assoc_all($r);
	$error = null;
}catch(Exception $e) {
	error_log($e);
	$prd_offers = array();
	$count = 0;
	$total_survey = 0;
}

$page = ria_array_get($_GET, 'page', 1);
$controler = 'OffersRaised';
$max_levels = 0;
$colspan = 8;
$pages = $count ? ceil( $count / $per_pages ) : 1;
$filter_data = array(
	'sort' => ria_array_get($_GET, 'sort', array()),
	'sellers' => ria_array_get($_GET, 'sellers', 0),
	'central' =>  ria_array_get($_GET, 'central', 0),
	'fr_depts' =>  ria_array_get($_GET, 'fr_depts', 0),
	'customers' => ria_array_get($_GET, 'customers', 0),
	'date1' =>  ria_array_get($_GET, 'date1', null),
	'date2' =>  ria_array_get($_GET, 'date2', null),
	'page' => ria_array_get($_GET, 'page', 1),
	'ref' => ria_array_get($_GET, 'ref', null),
);

if( isset($config['linear_raised_fld_filters']) && is_array($config['linear_raised_fld_filters']) ){
	foreach( $config['linear_raised_fld_filters'] as $one_fld ){
		$filter_data['selector_fld['.$one_fld.']'] = isset($_GET['selector_fld'][ $one_fld ]) ? $_GET['selector_fld'][ $one_fld ] : null;
	}
}

$url = '/admin/stats/linear-raised.php?' . http_build_query($filter_data);
$notice = '';
if( !$is_sorting_request ){
	$ratio_survey_store = $user_count > 0 ? ria_number_format($total_survey / $user_count, NumberFormatter::PERCENT, 2) : ria_number_format(0, NumberFormatter::PERCENT, 2);
	$url_seller = '';
	if (isset($_GET['sellers']) && $_GET['sellers'] > 0) {
		$url_seller = ' <a href="/admin/customers/index.php?seller='.htmlspecialchars($_GET['sellers']).'">'._('Liste des magasins pour ce représentant').'</a>';
	}
	ob_start();
	?>
	<div class="notice">
		<?php echo sprintf(_('%d magasins relevés sur un total de %d magasins (%s)'), $total_survey, $user_count, $ratio_survey_store). $url_seller?>
	</div>
	<?php
	$notice = ob_get_clean();
}
ob_start();
?>
 <table class="checklist tablesorter large">
	<thead>
		<tr>
			<th class="align-right" colspan="<?php echo $colspan?>">
				<label for="ref"><?php echo _('Référence contenant :'); ?></label>
				<input type="text" name="ref" id="ref" value="<?php echo ria_array_get($filter_data, 'ref', '')?>">
			</th>
		</tr>
		<tr>
			<th class="header<?php echo isset($filter_data['sort']['rank']) ? (ria_array_get($filter_data['sort'], 'rank', 'desc') == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'rank', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="rank">
				<span><?php echo _('#')?></span>
			</th>
			<th class="header<?php echo isset($filter_data['sort']['ref']) ? ($filter_data['sort']['ref'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'ref', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="ref">
				<span><?php echo _('Référence')?></span>
			</th>
			<th class="header<?php echo isset($filter_data['sort']['name']) ? ($filter_data['sort']['name'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'name', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="name">
				<span><?php echo _('Désignation')?></span>
			</th>
			<th class="col150px header<?php echo isset($filter_data['sort']['store']) ? ($filter_data['sort']['store'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'store', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="store">
				<span><?php echo _('Présence magasin')?></span>
			</th>
			<th class="col150px header <?php echo isset($filter_data['sort']['facings']) ? ($filter_data['sort']['facings'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'facings', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="facings">
				<span><?php echo _('Facings')?></span>
			</th>
			<th class="col150px header<?php echo isset($filter_data['sort']['avg_price']) ? ($filter_data['sort']['avg_price'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'avg_price', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="avg_price" title="<?php echo _('Prix moyen constaté')?>">
				<span><?php echo _('PMC')?></span>
			</th>
			<th class="col150px header<?php echo isset($filter_data['sort']['dn']) ? ($filter_data['sort']['dn'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'dn', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="dn" title="<?php echo _('Distribution Numérique')?>">
				<span><?php echo _('DN')?></span>
			</th>
			<th class="col150px header<?php echo isset($filter_data['sort']['out_of_stock']) ? ($filter_data['sort']['out_of_stock'] == 'desc' ? ' headerSortDown' : ' headerSortUp' ) : ''; ?>"
				data-dir="<?php echo ria_array_get($filter_data['sort'], 'out_of_stock', 'desc') == 'desc' ? 'asc' : 'desc';?>"
				data-filter="out_of_stock" title="<?php echo _('Taux de rupture')?>">
				<span><?php echo _('Rupture')?></span>
			</th>
		</tr>
	</thead>
	<tbody>
		<tr class="template none">
			<td class="rank align-center"></td>
			<td class="syncref">
				<img class="sync fleche-move" title="<?php echo _("Ce produit est synchronisé avec votre gestion commerciale"); ?>" alt="<?php print _('Ce produit est synchronisé avec votre gestion commerciale')?>">
				<span class="ref"></span>
			</td>
			<td class="name">
				<a href="" class="link"></a>
			</td>
			<td class="count align-center"></td>
			<td class="facings align-center"></td>
			<td class="level align-center"></td>
		</tr>
		<?php if (empty($prd_offers)) { ?>
			<tr>
				<td colspan="<?php echo $colspan?>"><?php print _('Aucun relevé de linéaire'); ?></td>
			</tr>
		<?php }else{
			foreach ($prd_offers as $line) {?>
				<tr>
					<td class="align-center">
						<?php print htmlspecialchars( intval($line['rank']) >  0 ? $line['rank'] : '' ); ?>
					</td>
					<td class="nowrap">
						<?php print view_prd_is_sync($line).' '.htmlspecialchars( $line['ref'] ); ?>
					</td>
					<td>
						<a href="/admin/catalog/product.php?cat=0&prd=<?php echo $line['prd_id']; ?>">
							<?php echo htmlspecialchars($line['name']); ?>
						</a>
					</td>
					<td class="align-center">
						<?php echo $line['store']; ?>
					</td>
					<td class="align-center">
						<?php echo $line['facings']; ?>
					</td>
					<td class="number">
						<?php echo ($line['avg_price'] > 0 ? ria_number_format($line['avg_price'], NumberFormatter::CURRENCY, 2) : _('Non renseigné')) ?>
					</td>
					<td class="number" title="<?php print
					sprintf(_('Produit trouvé %d fois dans %d magasins relevés'),$line['store'], $line['store_total'])?>">
						<?php echo ria_number_format($line['dn'], NumberFormatter::PERCENT, 2) ?>
					</td>
					<td class="number">
						<?php echo ria_number_format($line['out_of_stock'], NumberFormatter::PERCENT, 2) ?>
					</td>
				</tr>
				<?php
				}
			} ?>
	</tbody>
	<?php if( $pages > 1) { ?>
		<tfoot>
			<tr>
				<td class="align-left" colspan="2"><?php print 'Page '.$page.' / <span class="page">'.$pages.'</span>'; ?></td>
				<td colspan="<?php echo $colspan -2 ?>">
					<?php
						if( $count>0 ){
							$url = $url.( strstr($url, '?') ? '&amp;' : '?').'page=';

							$links = array();
							if( $page>1 )
								$links[] = '<a href="'.$url.($page-1).'" class="page" data-page="'.($page-1).'">&laquo; '._('Page précédente').'</a>';
							for( $i=$page-5; $i<$page+5; $i++ )
								if( $i>=1 && $i<=$pages ){
									if( $i==$page )
										$links[] = '<b>'.$i.'</b>';
									else
										$links[] = '<a href="'.$url.$i.'" class="page" data-page="'.$i.'">'.$i.'</a>';
								}
							if( $page<$pages )
								$links[] = '<a href="'.$url.($page+1).'" class="page" data-page="'.($page+1).'">'._('Page suivante').' &raquo;</a>';

							print implode(' | ',$links);
						}
					?>
				</td>
			</tr>
		</tfoot>
	<?php } ?>
</table>
<?php
$table = ob_get_clean();

if (IS_AJAX) {
	$response = array('table' => $table, 'count' => $count, 'error' => $error);

	if( !$is_sorting_request ){
		$response['notice'] = $notice;
	}

	echo json_encode($response);
	exit;
}

// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Statistiques'), '/admin/stats/index.php' )
	->push( _('Relevés de linéaires'), '/admin/stats/linear-raised/index.php' )
	->push( _('Statistiques DN') );

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Relevés de linéaires').' - '._('Statistiques') );
require_once('admin/skin/header.inc.php');
?>
<style type="text/css">
	table, tr, tbody td {
		border: 1px solid #ccc;
		border-collapse: collapse;
	}
	.header {
		cursor: pointer;
	}
	.header:hover {
		text-decoration: underline;
	}
	.ajax-container {
		overflow-y: auto;
	}
</style>

<?php if (!is_null($error)) {?>
	<div class="flash_message error"><?php echo htmlspecialchars( $error ); ?></div>
<?php }else{ ?>
	<div class="flash_message">
	</div>
<?php } ?>
<h2><?php echo _('Relevés de linéaires')?> (<span id="plr_count"><?php print ria_number_format($count); ?></span>)</h2>

<div>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<input type="hidden" name="date1" id="date1" />
		<input type="hidden" name="date2" id="date2" />
		<?php
			echo view_sellers_selector();
			echo view_centrals_selector();
			echo view_users_fr_depts_selector(array(PRF_CUST_PRO));

			// Charge les filtres sur des champs avancés
			if( isset($config['linear_raised_fld_filters']) && is_array($config['linear_raised_fld_filters']) ){
				foreach( $config['linear_raised_fld_filters'] as $one_fld ){
					// Récupère les valeurs distincts de ces champs
					$ar_vals = fld_object_values_get_distinct( $one_fld );
					if( !count($ar_vals) ){
						continue;
					}

					$fld_name = fld_fields_get_name( $one_fld );

					$selected = 'Aucun filtre';
					if( isset($_GET['selector_fld'][ $one_fld ]) && trim($_GET['selector_fld'][ $one_fld ]) != '' ){
						$selected = urldecode( $_GET['selector_fld'][ $one_fld ] );
					}

					print '<div data-fld="'.$one_fld.'" class="riapicker selector_by_fld" id="selector_fld_'.$one_fld.'">'
						.'<div class="selectorview">'
							.'<div class="left">'
								.'<span class="function_name">'.htmlspecialchars( $fld_name ).'</span>'
								.'<br><span class="view">'.htmlspecialchars( $selected ).'</span>'
							.'</div>'
							.'<a name="btn" class="btn">'
								.'<img src="/admin/images/stats/fleche.gif?1" width="16" height="8" />'
							.'</a>'
							.'<div class="clear"></div>'
						.'</div>'
						.'<div class="selector">'
							.'<a name="">Aucun filtre</a>';

							foreach( $ar_vals as $one_val ){
								print '<a name="'.urlencode( $one_val ).'">'.htmlspecialchars( $one_val ).'</a>';
							}

					print '</div>'
					.'</div>';
				}
			}
		?>
		<div class="clear"></div>
	</div>
	<div class="ajax-notice">
			<?php echo $notice ?>
	</div>
	<div class="ajax-container">
			<?php echo $table ?>
	</div>
	<div class="notice">Pour des questions de pertinence, les pourcentages de détention numérique (DN) sont calculés à partir du nombre de magasins ayant fait l'objet d'un relevé, et pas en fonction du nombre total de magasins.</div>
</div>
<script>
	<?php view_date_initialized( 0, '', false, array() ); ?>
	var container =  $('.ajax-container');
	var noticeContainer =  $('.ajax-notice');
	var total_count =  $('#plr_count');
	var $template = $('.template', $table).clone();
	$('.template').remove();
	var $table = $('.checklist');
	var modified = false;
	var is_pop_state = false;
	var data = JSON.parse('<?php echo json_encode($filter_data)?>');
	var is_sorting = false;
	var initialState = $.extend({},data);
	var focus = false;

	var delay = (function(delay){
		var out = delay;
		return function(callback, timeout) {
			clearTimeout(out);
			out = setTimeout(callback, timeout);
		}
	})(null);
	container.on('click', '.checklist thead th.header', function (e) {
		var sort = {};
		sort[$(this).data('filter')] = $(this).data('dir');
		modified = true;
		data.sort = sort;
		is_sorting = true;
		fetch(data, render)
		if ($(this).data('dir') === 'asc') {
			$(this).data('dir', 'desc');
		}else{
			$(this).data('dir', 'asc');
		}
	}).on('click', '.page', function (e) {
		var page = $(this).data('page');
		isModified('page', page);
		data.page = page;
		fetch(data, render)
		if ($(this).data('dir') === 'asc') {
			$(this).data('dir', 'desc');
		}else{
			$(this).data('dir', 'asc');
		}
		return false;
	}).on('keyup', '#ref', function(){
		isModified('ref', $(this).val());
		focus = true;
		data.ref = $(this).val();
		fetch(data, render);
	});

	$('.riapicker').on('click', function(){
		if($('.selector', $(this)).css('display')=='none'){
			$('.selector').hide();
			$('.selector',  $(this)).show();
		}else{
			$('.selector',  $(this)).hide();
		}
	});

	$('#selectseller .selector a').on('click', function(){
		$('#selectseller .view').text($(this).text());
		$('.selector', $(this)).hide();
		var value = $(this).attr('name').replace('seller-', '');
		isModified('sellers', value);
		data.sellers = value;
		fetch(data, render);
	});

	$('.selector_by_fld .selector a').on('click', function(){
		$(this).parents('.selector_by_fld').find('.view').text($(this).text());
		$('.selector', $(this)).hide();
		var fld = $(this).parents('.selector_by_fld').data('fld');
		var value = $(this).attr('name');

		isModified('selector_fld[' + fld + ']', value);
		data['selector_fld[' + fld + ']'] = value;
		fetch(data, render);
	});

	$('#select_central .selector a').on('click', function(){
		$('#select_central .view').text($(this).text());
		$('.selector', $(this)).hide();
		var value = $(this).attr('name').replace('central-', '');
		isModified('central', value);
		data.central = value;
		fetch(data, render);
	});

	$('#select_fr_depts .selector a').on('click', function(){
		$('#select_fr_depts .view').text($(this).text());
		$('.selector', $(this)).hide();
		var value = $(this).attr('name').replace('fr_dept-', '');
		isModified('fr_depts', value);
		data.fr_depts = value;
		fetch(data, render);
	});

	$('#date1').on('change', function(){
		isModified('date1', $(this).val());
		data.date1 = $(this).val();
		fetch(data, render);
	})

	$('#date2').on('change', function(){
		data.date1 = $(this).val();
		fetch(data, render);
	})

	$('#riadatepicker').on('click', '.selector a', function(){
		if ($(this).attr('name') == 'perso') {
			$("#btn_submit").on('click', function(){
				update_dates();
			});
			return;
		}

		update_dates();
	});
	function isModified(key, value) {
		// stock l'import pour savoir si les filtres ont changés afin de lancer la mise à jour des données en Ajax
		modified = data[key] != value;
		return modified;
	}
	function update_dates () {
		delay(function () {
			if (!isModified('date1', $('#date1').val())){
				isModified('date2', $('#date2').val());
			}
			data.date1 = $('#date1').val();
			data.date2 = $('#date2').val();
			fetch(data, render);
		}, 10);
	}

	window.onpopstate = function(event) {
		modified = true;
		is_pop_state = true;
		if (event.state == null) {
			fetch(initialState, render);
		}else{
			fetch(event.state, render);
		}
	};

	function render(json) {
		if (json.result) {
			$('tbody tr', $table).remove();
			var max = 0;
			if (json.content.results > 0) {
				$.each(json.content.items, function(i, item) {
					var $tr = $template.clone();
					$('.rank', $tr).text(item.rank);
					$('.syncref .sync', $tr).attr('src', '/admin/images/sync/' + (item.is_sync ? '1' : '0') +'.svg');
					$('.syncref .ref', $tr).text(item.ref);
					$('.name .link', $tr).attr('src', '/admin/catalog/product.php?cat=0&prd='+item.id);
					$('.name .link', $tr).text(item.name);
					$('.count', $tr).text(item.count);
					$('.facings', $tr).text(item.facings);
					i = 1;
					while (typeof item['level_'+i] !== "undefined") {

						var $level = $('.level', $tr).clone();
						$level.removeClass('level');
						var $level_part = $('.level', $tr).clone();
						$level_part.removeClass('level');
						$level.text(item['level_'+i] > 0 ? item['level_'+i] : '');
						$level_part.text(
							item['part_level_'+i] > 0 ? parseFloat(item['part_level_'+i]).toFixed(2).toString().replace(".", ",") : ''
						);
						$tr.append($level);
						$tr.append($level_part);
						i++;
					}
					max = i-1;
					$('.level', $tr).remove();
					$('tbody', $table).append($tr.show());
				});
			}else{
				$('tbody', $table).append(
					$('<tr>').append($('<td>').attr('colspan', '4').text('Aucun relevé de saisie'))
				);
			}
			if ($('thead .level').length > max) {
				$('thead .level').each(function(i, th){
					if ((i+1) > max ) {
						th.remove();
					}
				})
				$('thead .part_level').each(function(i, th){
					if ((i+1) > max ) {
						th.remove();
					}
				})
			}else if ($('thead .level').length < max){
				var i = $('thead .level').length+1;
				for (i; i <= max; i++) {
					$('thead tr', $table).append(
						$('<th>').addClass('level').addClass('header')
							.text('Niveau '+ i)
							.data('filter', 'level_'+i)
							.data('dir', 'asc')
					)
					$('thead tr', $table).append(
						$('<th>').addClass('part_level').addClass('header')
							.text('Part niveau '+ i)
							.data('filter', 'part_level_'+i)
							.data('dir', 'asc')
					)
				}
			}
		}
	}

	function fetch(data, done) {
		if (modified) {
			delay(function(){
				$('#popup_ria_shadow').show();
				$.ajax({
					url: '/admin/stats/linear-raised.php',
					data: $.extend({}, {action: 'stats', is_sorting: is_sorting}, data),
				}).done(function(response){
					var json = JSON.parse(response);
					if (json.error == null) {
						$('.flash_message').removeClass('error').text('');
					}else{
						$('.flash_message').text(json.error).addClass('error');
					}
					total_count.text(json.count);
					if( !is_sorting ){
						noticeContainer.html(json.notice);
					}
					container.html(json.table);
				}).always(function(){
					//reset focus
					if (focus) {
						$('#ref').focus();
						var val = $('#ref').val();
						$('#ref').val('');
						$('#ref').val(val);
					}
					if (typeof window.history !== 'undefined' && !is_pop_state) {
						history.pushState(data, "<?php echo ADMIN_PAGE_TITLE ?>", location.pathname+ '?'+$.param(data));
					}
					modified = false;
					is_pop_state = false;
					focus = false;
					is_sorting = false;
					$('#popup_ria_shadow').hide();
				});
			}, 800);
		}
	}
</script>


<?php
require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file export-search-sylk.php
	 * 	Ce fichier exporte les données de l'écran Statistiques de recherche au format Sylk
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');

	require_once('search.inc.php');
	require_once('websites.inc.php');
	require_once('tenants.inc.php');

	// Par défaut, on arrive sur le jour en cours
	if( !isset($_GET['day']) && !isset($_GET['week']) && !isset($_GET['month']) && !isset($_GET['year']) ){
		$_GET['day'] = date('Y-m-d');
	}

	// Charge la liste des sites
	$websites = wst_websites_get();
	
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all') 
			$_SESSION['websitepicker'] = false;
		else
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
	}
	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;
	
	// Rechercher les logs
	$date1 = date('Y-m-d');
	$date2 = false;
	
	if(isset($_GET['date1']) && isset($_GET['date2'])){
		$date1 = dateparse($_GET['date1']);
		$date2 = dateparse($_GET['date2']);
	}
	
	$stats = search_log_get($wst_id, $date1, $date2);
	
	$stats_count = ria_mysql_num_rows($stats) ? ria_mysql_num_rows($stats) :  1;
	
	$volumes = $average_results = $statscount = 0;
	$statscount = ria_mysql_num_rows($stats);
	if(ria_mysql_num_rows($stats)) {
		$volumes = 0;
		$searchs_results = array();
		while($stat = ria_mysql_fetch_array($stats))
		{
			$volumes += $stat['volume'];
			if(isset($searchs_results[$stat['results']])) {
				$searchs_results[$stat['results']] = $searchs_results[$stat['results']] + $stat['volume'];
				
			}
			else {
				$searchs_results[$stat['results']] = $stat['volume'];
			}
			
		}
	
		$a = $b =0;
		foreach($searchs_results as $key => $val)
		{
			$a += $key*$val;
			$b += $val; 
		}
		if($b != 0)
			$average_results = round($a/$b);
		//$searchs_results_count = array_count_values($searchs);
		ria_mysql_data_seek($stats,0);
	}
	
	define("FORMAT_REEL",   1); // #,##0.00
	define("FORMAT_ENTIER", 2); // #,##0
	define("FORMAT_TEXTE",  3); // @
	
	$cfg_formats[FORMAT_ENTIER] = "FF0";
	$cfg_formats[FORMAT_REEL]   = "FF2";
	$cfg_formats[FORMAT_TEXTE]  = "FG0";
	
	
	// d?finition des diff?rentes colonnes de donn?es
	// ------------------------------------------------------------------------
	$champs = array(
		//	   champ			en-tête				format         	alignement  largeur
		array( _('Recherche'),		_('Recherche'),		FORMAT_TEXTE,	'L',        20 ),
		array( _('Sections'),		_('Sections'),			FORMAT_TEXTE,	'L',        20 ),
		array( _('Filtre'),		_('Filtre'),			FORMAT_TEXTE,	'L',        20 ),
		array( _('Volume'),		_('Volume'), 			FORMAT_ENTIER,	'L',        20 ),
		array( _('Nb résultats'),	_('Nb résultats'),		FORMAT_ENTIER,	'L',        10 ),
		array( _('Moy. pages'),	_('Moy. pages'),		FORMAT_ENTIER,	'L',        10 ),
		array( _('Moy. clics'),	_('Moy. clics'),		FORMAT_ENTIER,	'L',        10 ),
		array( _('Emplacement'),	_('Emplacement'),		FORMAT_TEXTE,	'L',        10 ),
	);

	if( !$wst_id && ria_mysql_num_rows($websites)>1 ){ 
		$champs[] = array(_('Site web'),_('Site web'),	FORMAT_TEXTE, 	'L',        20);
	}
	
	// en-t?te HTTP
    // --------------------------------------------------------------------
	$file = _('statistiques-recherches-');
	$file .= $date1 ? date('d-m-Y', strtotime($date1) ) : '';
	$file .= $date2 && $date1!=$date2 ? '-'.date('d-m-Y', strtotime($date2) ) : '';
	
	header('Content-disposition: attachment; filename="'.$file.'.slk"');
	header('Content-type: application/octetstream');
	header('Pragma: no-cache');
	header('Expires: 0');
	
	// en-tete du fichier SYLK
	$slk = "ID;PExport\n"; // ID;Pappli
	$slk .= "\n";
	
	// formats
	$slk .= "P;PGeneral\n";      
	$slk .= "P;P#,##0.00\n";       // P;Pformat_1 (reels)
	$slk .= "P;P#,##0\n";          // P;Pformat_2 (entiers)
	$slk .= "P;P@\n";              // P;Pformat_3 (textes)
	$slk .= "\n";
	
	// polices
	$slk .= "P;EArial;M200\n";
	$slk .= "P;EArial;M200\n";
	$slk .= "P;EArial;M200\n";
	$slk .= "P;FArial;M200;SB\n";
	$slk .= "\n";
	
	for ($cpt = 0; $cpt < sizeof($champs); $cpt++)
	{
		$num_format[$cpt] = $champs[$cpt][2];
		$format[$cpt] = $cfg_formats[$num_format[$cpt]].$champs[$cpt][3];
	}
	
	// largeurs des colonnes
	// --------------------------------------------------------------------
	for ($cpt = 1; $cpt <= sizeof($champs); $cpt++)
	{
		// F;Wcoldeb colfin largeur
		$slk .= "F;W".$cpt." ".$cpt." ".$champs[$cpt-1][4]."\n";
	}
	$slk .= "F;W".$cpt." 256 8\n"; // F;Wcoldeb colfin largeur
	$slk .= "\n";

	// en-tete des colonnes (en gras --> SDM4)
	// --------------------------------------------------------------------
	for ($cpt = 1; $cpt <= sizeof($champs); $cpt++)
	{
		$slk .= "F;SDM4;FG0C;".($cpt == 1 ? "Y1;" : "")."X".$cpt."\n";
		$slk .= "C;N;K\"".$champs[$cpt-1][1]."\"\n";
	}
	$slk .= "\n";
	
	$ligne=2;
	
	if( ria_mysql_num_rows($stats) ){
		while( $stat = ria_mysql_fetch_array($stats) ){
			$i = 0;
			
			foreach( $champs as $one_champ ){
				$val_slk = '';

				switch( $one_champ[0] ){
					case 'Recherche': {
						$val_slk = $stat['search'];
						break;
					}
					case 'Sections': {
						$val_slk = _('Toutes');
						if( $stat['section']!==null ){
							$r_section = ria_mysql_fetch_array( prd_categories_get($stat['section']) );
							$val_slk = $r_section['name'];
						}
			
						break;
					}
					case 'Filtre': {
						$val_slk = search_log_get_types( $stat['scc'] );
						break;
					}
					case 'Volume': {
						$val_slk = $stat['volume'];
						break;
					}
					case 'Nb résultats': {
						$val_slk = $stat['results'];
						break;
					}
					case 'Moy. pages': {
						$val_slk = $stat['avg_page'];
						break;
					}
					case 'Moy. clics': {
						$val_slk = $stat['nb_click'] / ( $stat['volume'] > 0 ? $stat['volume'] : 1 );
						break;
					}
					case 'Emplacement': {
						$val_slk = $stat['seg_name'];
						break;
					}
					case 'Site web': {
						$website = ria_mysql_fetch_array( wst_websites_get($stat['wst_id']) );
						$val_slk = $website['name'];
						break;
					}
				}

				$slk .= "F;P".$num_format[ $i ].";".$format[ $i ];
				$slk .= ";Y".$ligne.";X".( $i + 1 )."\n";
				$slk .= "C;N;K\"".str_replace( ';', ';;', $val_slk )."\"\n";
				$slk .= "\n";

				$i++;
			}

			$ligne++;
		}
	}
	$slk .= "E\n";
	
	print utf8_decode( $slk );


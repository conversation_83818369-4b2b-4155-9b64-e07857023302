<?php

	/**	\file double.php
	 *	Cette page liste des balises de référencement en double (title ou description)
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_META_DOUBLE');

	$data = isset($_REQUEST['data']) && in_array($_REQUEST['data'], array('title', 'desc')) ? $_REQUEST['data'] : false;

	$title = _('Balises Meta Description en double');
	$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Meta Description également utilisée sur une autre page du site.');
	$rdouble = key_meta_description_get_multiple();

	// Pagination
	$limit_for_page = 35;
	$stats_count = $rdouble ? ria_mysql_num_rows( $rdouble ) : 0;

	$page  = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page']>0 ? $_GET['page'] : 1;
	$pages = ceil( $stats_count / $limit_for_page );
	$pmin = ($page-5)<1 ? 1 : $page-5;
	$pmax = ($pmin+9)>$pages ? $pages : ($pmin+9);

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Référencement'), '/admin/stats/referencement/index.php' )
		->push( $title );

	// Titre de la page
	define('ADMIN_PAGE_TITLE', $title.' - '._('Référencement').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $title ).' ('.number_format( $stats_count, 0, ',', ' ' ).')</h2>
		<div class="notice">'.htmlspecialchars( $desc ).'</div>
	';

	// Affichage des messages d'erreur
	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}

	print '
		<form action="" method="post">
			<input type="hidden" name="tab_selected" id="tab_selected" value="'.( isset($_GET['tab']) ? $_GET['tab'] : '' ).'" />
	';

	if( $data===false ){
		print '
			<div class="error">'._('Une ou plusieurs informations permettant de charger le rapport sont manquantes.').'</div>
		';
	}elseif( $rdouble && ria_mysql_num_rows($rdouble) ){
		print '
			<table id="stat-tag-double" class="checklist">
				<thead>
					<tr>
						<th id="tag-descript">'._('Description').'</th>
						<th id="tag-object">'._('Utilisée sur').'</th>
					</tr>
				</thead>
				<tbody>
		';

		if( (($page-1)*$limit_for_page)<=$stats_count ){
			ria_mysql_data_seek( $rdouble, ($page-1)*$limit_for_page );
		}

		$count = 0;
		while( $double = ria_mysql_fetch_array($rdouble) ){
			if( $count>=$limit_for_page ){
				break;
			}

			$count++;

			// Récupère les contenus publiés utilisant cette meta description
			$rcnt = key_meta_description_get_objects( $double['tag_desc'], true );

			$ar_cnt = array();
			if( $rcnt && ria_mysql_num_rows($rcnt) ){
				while( $cnt = ria_mysql_fetch_array($rcnt) ){
					$is_sync = $url = $label = '';
					$tmp = 1;
					switch( $cnt['type'] ){
						case 'cly': {
							$rp = prd_products_get_simple( $cnt['obj_id_1'] );
							if( !$rp || !ria_mysql_num_rows($rp) ){
								continue;
							}

							$p = ria_mysql_fetch_array( $rp );

							$is_sync = view_prd_is_sync( $p );
							$label = prd_categories_get_ariane( $cnt['obj_id_0'] ).' > '.$p['name'];
							$url = '/admin/catalog/product.php?cat='.$cnt['obj_id_0'].'&amp;prd='.$cnt['obj_id_1'].'&amp;tab=ref';
							break;
						}
						case 'cat': {
							$rc = prd_categories_get( $cnt['obj_id_0'] );
							if( !$rc || !ria_mysql_num_rows($rc) ){
								continue;
							}

							$c = ria_mysql_fetch_array( $rc );

							$is_sync = view_cat_is_sync( $c );
							$label = $c['name'];
							$url = '/admin/catalog/edit.php?cat='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'brd': {
							$rb = prd_brands_get( $cnt['obj_id_0'] );
							if( !$rb || !ria_mysql_num_rows($rb) ){
								continue;
							}

							$b = ria_mysql_fetch_array( $rb );

							$is_sync = view_brd_is_sync( $b );
							$label = $b['name'];
							$url = '/admin/catalog/brands/edit.php?brd='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'str': {
							$rs = dlv_stores_get( $cnt['obj_id_0'] );
							if( !$rs || !ria_mysql_num_rows($rs) ){
								continue;
							}

							$s = ria_mysql_fetch_array( $rs );

							$is_sync = view_str_is_sync( $s );
							$label = $s['name'];
							$url = '/admin/config/livraison/stores/edit.php?str='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'cms': {
							$rc = cms_categories_get( $cnt['obj_id_0'] );
							if( !$rc || !ria_mysql_num_rows($rc) ){
								continue;
							}

							$c = ria_mysql_fetch_array( $rc );

							$parents = cms_hierarchy_get( $cnt['obj_id_0'] );
							if( is_array($parents) && sizeof($parents) ){
								foreach( $parents as $parent ){
									$label .= ( trim($label)!='' ? ' > ' : '' ).$parent['name'];
								}
							}

							$is_sync = '';
							$label .= ( trim($label)!='' ? ' > ' : '' ).$c['name'];
							$url = '/admin/tools/cms/edit.php?cat='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'news': {
							$rn = news_get( $cnt['obj_id_0'] );
							if( !$rn || !ria_mysql_num_rows($rn) ){
								continue;
							}

							$n = ria_mysql_fetch_array( $rn );

							$is_sync = '';
							$label = $n['name'];
							$url = '/admin/tools/news/edit.php?news='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'faq-cat': {
							$rfc = faq_categories_get( $cnt['obj_id_0'] );
							if( !$rfc || !ria_mysql_num_rows($rfc) ){
								continue;
							}

							$fc = ria_mysql_fetch_array( $rfc );

							$is_sync = '';
							$label = $fc['name'];
							$url = '/admin/tools/faq/category.php?cat='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
						case 'faq-qst': {
							$rfq = faq_questions_get( $cnt['obj_id_0'] );
							if( !$rfq || !ria_mysql_num_rows($rfq) ){
								continue;
							}

							$fq = ria_mysql_fetch_array( $rfq );

							$is_sync = '';
							$label = $fq['name'];
							$url = '/admin/tools/faq/question.php?qst='.$cnt['obj_id_1'].'&cat='.$cnt['obj_id_0'].'&amp;tab=ref';
							break;
						}
					}

					if( trim($label)!='' ){
						if( !isset($ar_cnt[ $cnt['type'] ]) ){
							$ar_cnt[ $cnt['type'] ] = array(
								'title' => '',
								'cnts' => array()
							);

							switch( $cnt['type'] ){
								case 'cly' : $ar_cnt[ $cnt['type'] ]['title'] = _('Produits'); break;
								case 'cat' : $ar_cnt[ $cnt['type'] ]['title'] = _('Catégories'); break;
								case 'brd' : $ar_cnt[ $cnt['type'] ]['title'] = _('Marques'); break;
								case 'str' : $ar_cnt[ $cnt['type'] ]['title'] = _('Magasins'); break;
								case 'cms' : $ar_cnt[ $cnt['type'] ]['title'] = _('Pages de contenu'); break;
								case 'news' : $ar_cnt[ $cnt['type'] ]['title'] = _('Actualités'); break;
								case 'faq-cat' : $ar_cnt[ $cnt['type'] ]['title'] = _('Catégorie FAQ'); break;
								case 'faq-qst' : $ar_cnt[ $cnt['type'] ]['title'] = _('Question FAQ'); break;
							}
						}

						$ar_cnt[ $cnt['type'] ]['cnts'][] = array(
							'is_sync' => $is_sync,
							'label' => $label,
							'url' => $url
						);
					}
				}
			}

			if( !sizeof($ar_cnt) ){
				continue;
			}

			print '
					<tr>
						<td headers="tag-descript">
							'.htmlspecialchars( $double['tag_desc'] ).'
						</td>
						<td>
							<ul>
			';

			foreach( $ar_cnt as $type=>$cnts ){
				print '
								<li class="cnt-parent">
									<span class="title">'.$cnts['title'].'</span>
									<ul>
				';

				foreach( $cnts['cnts'] as $cnt ){
					print '
										<li class="cnt-childs"><a href="'.$cnt['url'].'">'.$cnt['is_sync'].' '.htmlspecialchars( $cnt['label'] ).'</a>
					';
				}

				print '
									</ul>
								</li>
				';
			}

			print '
							</ul>
						</td>
					</tr>
			';
		}

		print '
				</tbody>
				<tfoot>
					<tr>
						<td id="pagination" colspan="2">
			';

			if( $pages>1 ){
				if( $page>1 )
					print '<a href="/admin/stats/referencement/double.php?data='.$data.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
				for( $i=$pmin; $i<=$pmax; $i++ ){
					if( $i==$page )
						print '<b>'.$page.'</b>';
					else
						print '<a href="/admin/stats/referencement/double.php?data='.$data.'&amp;page='.($i).'">'.$i.'</a>';
					if( $i<$pmax )
						print ' | ';
				}
				if( $page<$pages )
					print ' | <a href="/admin/stats/referencement/double.php?data='.$data.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
			}

			print '
						</td>
					</tr>
				</tfoot>
			</table>
		';
	}else{
		print '<div class="notice">'._('Aucun contenu ne correspond à ce rapport.').'</div>';
	}

	print '
		</form>
	';

	require_once('admin/skin/footer.inc.php');

<?php

	/**	\page referencement.php
	 *	Cette page est utilisée pour mettre à jour les balises Title et Meta Description en fonction des critères suivants :
	 *	- Balises Title non surchargées
	 *	- Balises Title trop courtes
	 *	- Balises Title trop longues
	 *	- Balises Meta Description non surchargées
	 *	- Balises Meta Description trop courtes
	 *	- Balises Meta Description trop longues
	 *	- Balises Meta Description en double
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( $_GET['data'] == 'title' && $_GET['ctrl'] == 'empty' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_TITLE_EMPTY');
	}elseif( $_GET['data'] == 'title' && $_GET['ctrl'] == 'min' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_TITLE_MIN');
	}elseif( $_GET['data'] == 'title' && $_GET['ctrl'] == 'max' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_TITLE_MAX');
	}elseif( $_GET['data'] == 'desc' && $_GET['ctrl'] == 'empty' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_META_EMPTY');
	}elseif( $_GET['data'] == 'desc' && $_GET['ctrl'] == 'min' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_META_MIN');
	}elseif( $_GET['data'] == 'desc' && $_GET['ctrl'] == 'max' ){
		gu_if_authorized_else_403('_RGH_ADMIN_STATS_REF_META_MAX');
	}

	$data = isset($_REQUEST['data']) && in_array($_REQUEST['data'], array('title', 'desc')) ? $_REQUEST['data'] : false;
	$ctrl = isset($_REQUEST['ctrl']) && in_array($_REQUEST['ctrl'], array('empty', 'min', 'max')) ? $_REQUEST['ctrl'] : false;

	$textarea_rows = $data=='title' ? 4 : 6;
	$title = _('Référencement');
	$desc = '';
	if( $data!==false && $ctrl!==false ){

		// Enregistre la personnalisation des balises TITLE ou META DESCRIPTION
		if( isset($_POST['save-ref']) ){
			if( isset($_POST['ref']) && is_array($_POST['ref']) && sizeof($_POST['ref']) ){
				$res = true;

				foreach( $_POST['ref'] as $cls_id=>$objs ){
					foreach( $objs as $key=>$val ){
						$tmp_res = true;

						switch( $data ){
							case 'title' : {
								switch( $cls_id ){
									case CLS_PRODUCT :
										$key = preg_split( '/\-/', $key );
										if( is_array($key) && sizeof($key)==2 ){
											$tmp_res = prd_classify_update_referencing_tag_title( $key[0], $key[1], $val );
										}
										break;
									case CLS_CATEGORY :
										$tmp_res = prd_categories_update_referencing_tag_title( $key, $val );
										break;
									case CLS_BRAND :
										$tmp_res = prd_brands_update_referencing_tag_title( $key, $val );
										break;
									case CLS_STORE :
										$tmp_res = dlv_stores_update_referencing_tag_title( $key, $val );
										break;
									case CLS_CMS :
										$tmp_res = cms_categories_update_referencing_tag_title( $key, $val );
										break;
									case CLS_NEWS :
										$tmp_res = news_update_referencing_tag_title( $key, $val );
										break;
									case CLS_FAQ_CAT :
										$tmp_res = faq_categories_update_referencing_tag_title( $key, $val );
										break;
									case CLS_FAQ_QST :
										$tmp_res = faq_questions_update_referencing_tag_title( $key, $val );
										break;
								}
								break;
							}
							case 'desc' :{
								switch( $cls_id ){
									case CLS_PRODUCT :
										$key = preg_split( '/\-/', $key );
										if( is_array($key) && sizeof($key)==2 ){
											$tmp_res = prd_classify_update_referencing_tag_desc( $key[0], $key[1], $val );
										}
										break;
									case CLS_CATEGORY :
										$tmp_res = prd_categories_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_BRAND :
										$tmp_res = prd_brands_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_STORE :
										$tmp_res = dlv_stores_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_CMS :
										$tmp_res = cms_categories_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_NEWS :
										$tmp_res = news_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_FAQ_CAT :
										$tmp_res = faq_categories_update_referencing_tag_desc( $key, $val );
										break;
									case CLS_FAQ_QST :
										$tmp_res = faq_questions_update_referencing_tag_desc( $key, $val );
										break;
								}
								break;
							}
						}

						$res = $res && $tmp_res;
					}
				}
			}

			if( !$res ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}

			if( !isset($error) ){
				$tab_selected = isset($_POST['tab_selected']) ? $_POST['tab_selected'] : '';
				header('Location: /admin/stats/referencement/referencement.php?data='.$data.'&ctrl='.$ctrl.'&tab='.$tab_selected);
				exit;
			}
		}

		// Chargement du titre du rapport
		switch( $data ){
			case 'title' :
				switch( $ctrl ){
					case 'empty' :
						$title = _('Balises Title non surchargées');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Title vide et pour lesquels la balise Title est calculée automatiquement.');
						break;
					case 'min' :
						$title = _('Balises Title trop courtes');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Title qui ne satisfait pas aux critères de limite minimum pour les balises Title.');
						break;
					case 'max' :
						$title = _('Balises Title trop longues');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Title qui ne satisfait pas aux critères de limite maximum pour les balises Title.');
						break;
				}
				break;
			case 'desc' :
				switch( $ctrl ){
					case 'empty' :
						$title = _('Balises Meta Description non surchargées');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Meta Description vide et pour lesquels la balise meta description est calculée automatiquement.');
						break;
					case 'min' :
						$title = _('Balises Meta Description trop courtes');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Meta Description qui ne satisfait pas aux critères de limite minimum pour les balises Meta Description.');
						break;
					case 'max' :
						$title = _('Balises Meta Description trop longues');
						$desc = _('Ce rapport contient la liste des produits, catégories, actualités, etc... qui possèdent une balise Meta Description qui ne satisfait pas aux critères de limite maximum pour les balises Meta Description.');
						break;
				}
				break;
		}

		// Liste des classes dont le référencement peut être personnalisé
		$ar_class = array( CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND, CLS_STORE, CLS_CMS, CLS_NEWS, CLS_FAQ_CAT, CLS_FAQ_QST );

		// Tableau contenant les contenus répondant aux critères du rapport
		$ar_cnt = array();

		foreach( $ar_class as $class){
			$cnt_title = ''; $ar_tmp = array();

			switch( $class ){
				case CLS_PRODUCT : {
					// Charge les produits publiés se trouvant dans des catégories publiées
					$rcly = prd_classify_get( false, 0, 0, 0, false, true );

					$ar_prds = array();
					$ar_prd_ids = array();
					if ($rcly && ria_mysql_num_rows($rcly)) {
						while ($cly = ria_mysql_fetch_assoc($rcly)) {
							$ar_prd_ids[ $cly['prd'] ] = $cly['prd'];
						}

						ria_mysql_data_seek( $rcly, 0 );
					}

					if (count($ar_prd_ids)) {
						$r_prd = prd_products_get_simple( $ar_prd_ids );
						if ($r_prd) {
							while ($prd = ria_mysql_fetch_assoc($r_prd)) {
								$ar_prds[ $prd['id'] ] = array( 'id' => $prd['id'], 'ref' => $prd['ref'], 'name' => $prd['name'], 'desc' => $prd['desc'], 'desc-long' => strcut( html_revert_wysiwyg($prd['desc-long']), 150 ), 'is_sync' => $prd['is_sync'] );
							}
						}
					}

					if ($rcly && ria_mysql_num_rows($rcly)) {
						while( $cly = ria_mysql_fetch_array($rcly) ){
							if (!array_key_exists($cly['prd'], $ar_prds)) {
								continue;
							}

							$test = test_value_meta( $data=='title' ? $cly['tag_title'] : $cly['tag_desc'], $data, $ctrl );
							if( $test ){
								if( !isset($ar_tmp[ $cly['prd'] ]) ){
									$p = $ar_prds[ $cly['prd'] ];

									$ar_tmp[ $cly['prd'] ] = array(
										'is_sync' => view_prd_is_sync( $p ),
										'ref'  => $p['ref'],
										'name' => $p['name'],
										'desc' => trim($p['desc'])!='' ? $p['desc'] : $p['desc-long'],
										'link' => '/admin/catalog/product.php?cat=0&amp;prd='.$p['id'],
										'tag'  => $data=='title' ? $cly['tag_title'] : $cly['tag_desc'],
										'cats' => array()
									);
								}

								$ar_tmp[ $cly['prd'] ]['cats'][] = $cly['cat'];
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_PRODUCT ] = array(
							'title' => _('Produits'),
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_CATEGORY : {
					$rc = prd_categories_get( 0, true );
					if( $rc && ria_mysql_num_rows($rc) ){
						while( $c = ria_mysql_fetch_array($rc) ){
							$test = test_value_meta( $data=='title' ? $c['tag_title'] : $c['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ $c['id'] ] = array(
									'is_sync' => view_cat_is_sync( $c ),
									'name' => $c['name'],
									'desc' => $c['desc'],
									'link' => '/admin/catalog/edit.php?cat='.$c['id'],
									'tag'  => $data=='title' ? $c['tag_title'] : $c['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_CATEGORY ] = array(
							'title' => _('Catégories'),
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_BRAND : {
					$rb = prd_brands_get( 0, true );
					if( $rb && ria_mysql_num_rows($rb) ){
						while( $b = ria_mysql_fetch_array($rb) ){
							$test = test_value_meta( $data=='title' ? $b['tag_title'] : $b['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ $b['id'] ] = array(
									'is_sync' => view_brd_is_sync( $b ),
									'name' => $b['name'],
									'desc' => $b['desc'],
									'link' => '/admin/catalog/brands/edit.php?brd='.$b['id'],
									'tag'  => $data=='title' ? $b['tag_title'] : $b['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_BRAND ] = array(
							'title' => _('Marques'),
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_STORE : {
					$rs = dlv_stores_get();
					if( $rs && ria_mysql_num_rows($rs) ){
						while( $s = ria_mysql_fetch_array($rs) ){
							$test = test_value_meta( $data=='title' ? $s['tag_title'] : $s['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ $s['id'] ] = array(
									'is_sync' => view_str_is_sync( $s ),
									'name' => $s['name'],
									'desc' => $s['desc'],
									'link' => '/admin/config/livraison/stores/edit.php?str='.$s['id'],
									'tag'  => $data=='title' ? $s['tag_title'] : $s['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_STORE ] = array(
							'title' => _('Magasins'),
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_CMS : {
					$rc = cms_categories_get( 0, false, true, -1, false, false, true, null, false, null, false );
					if( $rc && ria_mysql_num_rows($rc) ){
						while( $c = ria_mysql_fetch_array($rc) ){
							$test = test_value_meta( $data=='title' ? $c['tag_title'] : $c['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ $c['id'] ] = array(
									'is_sync' => '',
									'name' => $c['name'],
									'desc' => $c['desc'],
									'link' => '/admin/tools/cms/edit.php?cat='.$c['id'],
									'tag'  => $data=='title' ? $c['tag_title'] : $c['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_CMS ] = array(
							'title' => 'CMS',
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_NEWS : {
					$rn = news_get( 0, true, false, 0, 0, false, false );
					if( $rn && ria_mysql_num_rows($rn) ){
						while( $n = ria_mysql_fetch_array($rn) ){
							$test = test_value_meta( $data=='title' ? $n['tag_title'] : $n['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ $n['id'] ] = array(
									'is_sync' => '',
									'name' => $n['name'],
									'desc' => $n['desc'],
									'link' => '/admin/tools/news/edit.php?news='.$n['id'],
									'tag'  => $data=='title' ? $n['tag_title'] : $n['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_NEWS ] = array(
							'title' => _('Actualités'),
							'data' => $ar_tmp
						);
					}
					break;
				}
				case CLS_FAQ_QST :
				case CLS_FAQ_CAT : {
					$rfaq = $class==CLS_FAQ_QST ? faq_questions_get( 0, 0, true ) : faq_categories_get( 0, true );
					if( $rfaq && ria_mysql_num_rows($rfaq) ){
						while( $faq = ria_mysql_fetch_array($rfaq) ){
							$test = test_value_meta( $data=='title' ? $faq['tag_title'] : $faq['tag_desc'], $data, $ctrl );
							if( $test ){
								$ar_tmp[ ($class==CLS_FAQ_QST ? 'qst-' : 'cat-').$faq['id'] ] = array(
									'is_sync' => $class==CLS_FAQ_QST ? 'Question : ' : 'Catégorie : ',
									'name' => $faq['name'],
									'desc' => $faq['desc'],
									'link' => $class==CLS_FAQ_QST ? '/admin/tools/faq/question.php?qst='.$faq['id'].'&amp;cat='.$faq['cat_id'] : '/admin/tools/faq/category.php?cat='.$faq['id'],
									'tag'  => $data=='title' ? $faq['tag_title'] : $faq['tag_desc']
								);
							}
						}
					}

					if( sizeof($ar_tmp) ){
						$ar_cnt[ CLS_FAQ_CAT ] = array(
							'title' => _('FAQ'),
							'data' => isset($ar_cnt[CLS_FAQ_CAT]['data']) ? array_merge($ar_cnt[CLS_FAQ_CAT]['data'], $ar_tmp) : $ar_tmp
						);
					}
					break;
				}
			}
		}
	}

	function test_value_meta( $string, $data, $ctrl ){
		$string = html_revert_wysiwyg( $string );
		$test = true;

		switch( $data ){
			case 'title' :
				switch( $ctrl ){
					case 'empty' :
						$test = trim($string)=='';
						break;
					case 'min' :
						if( mb_strlen( trim($string), 'UTF8' )==0 ){
							return false;
						}

						$test = mb_strlen( trim($string), 'UTF8' ) < 30;
						break;
					case 'max' :
						if( mb_strlen( trim($string), 'UTF8' )==0 ){
							return false;
						}

						$test = mb_strlen( trim($string), 'UTF8' ) > 70;
						break;
				}
				break;
			case 'desc' :
				switch( $ctrl ){
					case 'empty' :
						$test = trim($string)=='';
						break;
					case 'min' :
						if( mb_strlen( trim($string), 'UTF8' )==0 ){
							return false;
						}

						$test = mb_strlen( trim($string), 'UTF8' ) < 70;
						break;
					case 'max' :
						if( mb_strlen( trim($string), 'UTF8' )==0 ){
							return false;
						}

						$test = mb_strlen( trim($string), 'UTF8' ) > 300;
						break;
				}
		}

		return $test;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Référencement'), '/admin/stats/referencement/index.php' )
		->push( $title );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title . ' - '. _('Référencement').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $title ).'</h2>
		<p class="notice">'.htmlspecialchars( $desc ).'</p>
	';

	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}

	print '
		<form action="" method="post">
			<input type="hidden" name="data" id="data" value="'.$data.'" />
			<input type="hidden" name="ctrl" id="ctrl" value="'.$ctrl.'" />
			<input type="hidden" name="tab_selected" id="tab_selected" value="'.( isset($_GET['tab']) ? $_GET['tab'] : '' ).'" />
	';

	if( $data===false || $ctrl===false ){
		print '
			<div class="error">'._('Une ou plusieurs informations permettant de charger le rapport sont manquantes.').'</div>
		';
	}elseif( sizeof($ar_cnt) ){
		print '
			<div id="stats-tags">
				<ul class="tabstrip">
		';

		$first = true;
		foreach( $ar_cnt as $cls_id=>$class ){
			$selected = $first ? 'class="selected"' : '';
			$stats_count = sizeof( $class['data'] );


			print '
					<li><input type="submit" '.$selected.' value="'.htmlspecialchars( $class['title'] ).' ('.number_format( $stats_count, 0, '.', ' ' ).')" name="tabClass'.$cls_id.'" /></li>
			';

			$first = false;
		}

		print '
				</ul>
				<div id="tabpanel">
		';

		$first = true;
		foreach( $ar_cnt as $cls_id=>$class ){
			$display = $first ? 'table' : 'none';
			$first = false;

			// Gestion de la pagination
			$limit_for_page = 35;
			$stats_count = sizeof( $class['data'] );

			$page  = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;
			$pages = ceil( $stats_count / $limit_for_page );

			if ($page > $pages) {
				$page = $pages;
			}

			$pmin = ($page-5)<1 ? 1 : $page-5;
			$pmax = ($pmin+9)>$pages ? $pages : ($pmin+9);

			print '
					<table  id="tabClass'.$cls_id.'" class="tb-stat-ref checklist" style="display:'.$display.'">
						<thead>
							<tr>
								<th id="col-name-'.$cls_id.'">'.htmlspecialchars( $class['title'] ).'</th>
								<th id="col-tag-'.$cls_id.'">'._(sprintf('Balise %s',( $data=='title' ? 'Title' : 'Meta Description' ))).'</th>
								<th id="col-tag-auto-'.$cls_id.'">'._('Version automatique').'</th>
							</tr>
						</thead>
						<tbody>
			';

			if( !sizeof($ar_cnt) ){
				print '
							<tr>
								<td colspan="3">'._('Aucun contenu ne rentre dans de ce rapport').'</td>
							</tr>
				';
			}else{
				$i = 1;

				$class['data'] = array_slice( $class['data'], (($page-1) * $limit_for_page), $limit_for_page, true );
				foreach( $class['data'] as $id=>$info ){
					if ($i > $limit_for_page) {
						break;
					}

					$tmp = $cls_id;
					if( $tmp==CLS_FAQ_CAT ){
						if( strstr($id, 'qst-') ){
							$tmp = CLS_FAQ_QST;
						}
					}

					print '
							<tr class="tr-ref-'.( $i<=$limit_for_page ? 'show' : 'hide' ).'">
								<td headers="col-name-'.$cls_id.'">
									<a href="'.$info['link'].'" onclick="return showPopupInfo('.$tmp.', '.str_replace(array('cat-', 'qst-'), '', $id).')">
										'.( $cls_id==CLS_PRODUCT ? $info['is_sync'].' '.htmlspecialchars( $info['ref'] ).' ' : '' ).htmlspecialchars( $info['name'] ).'
									</a>
									<br /><span class="desc">'.htmlspecialchars( html_revert_wysiwyg( strlen($info['desc'])>100 ? strcut($info['desc'], 97) : $info['desc']) ).'</span>
								</td>
					';

					if( $cls_id==CLS_PRODUCT ){
						print '
								<td headers="col-tag-'.$cls_id.'" class="td-stats-tag">
									<strong>'._('Classements').' :</strong>
						';

						$ar_categs = array();
						$rcly = prd_classify_get( false, $id, 0, 0, false, true );
						if( $rcly && ria_mysql_num_rows($rcly) ){
							while( $cly = ria_mysql_fetch_array($rcly) ){
								if( in_array($cly['cat'], $info['cats']) ){
									$ariane = prd_categories_get_ariane( $cly['cat'] );

									if( isset($_POST['ref'][$cls_id][$cly['cat'].'-'.$id]) ){
										$info['tag'] = $_POST['ref'][$cls_id][$cly['cat'].'-'.$id];
									}

									$auto = $data=='title' ? page_obj_title( $cls_id, array($id, $cly['cat']), false ) : page_obj_desc( $cls_id, array($id, $cly['cat']), false );

									print '
										<div class="ref-cly">
											<label for="ref'.$cls_id.'-'.$cly['cat'].'-'.$id.'">'.htmlspecialchars( $ariane ).'</label>
											<textarea id="ref'.$cls_id.'-'.$cly['cat'].'-'.$id.'" name="ref['.$cls_id.']['.$cly['cat'].'-'.$id.']" class="'.$data.'" rows="'.$textarea_rows.'" cols="50">'.$info['tag'].'</textarea>
										</div>
									';

									$ar_categs[ $cly['cat'] ] = array( 'ariane'=>$ariane, 'auto'=>$auto );
								}
							}
						}

						print '
								</td>
								<td headers="col-tag-auto-'.$cls_id.'" class="td-stats-tag">
									<strong>'._('Classements').' :</strong>
						';

						foreach( $ar_categs as $k=>$c ){
							print '
									<div class="ref-cly">
										<label for="ref-auto-'.$cls_id.'-'.$k.'-'.$id.'">'.htmlspecialchars( $ariane ).'</label>
										<textarea id="ref-auto-'.$cls_id.'-'.$k.'-'.$id.'" name="ref-auto" class="'.$data.' readonly" rows="'.$textarea_rows.'" cols="50" readonly="readonly">'.htmlspecialchars($c['auto']).'</textarea>
									</div>
							';
						}

						print '
								</td>
						';
					}else{
						$tmp = $cls_id;
						if( $tmp==CLS_FAQ_CAT ){
							if( strstr($id, 'qst-') ){
								$tmp = CLS_FAQ_QST;
								$id = str_replace( 'qst-', '', $id );
							}else{
								$id = str_replace( 'cat-', '', $id );
							}
						}

						if( isset($_POST['ref'][$tmp][$id]) ){
							$info['tag'] = $_POST['ref'][$tmp][$id];
						}

						$auto = $data=='title' ? page_obj_title( $cls_id, array($id), false ) : page_obj_desc( $cls_id, array($id), false );

						print '
								<td headers="col-tag-'.$cls_id.'" class="td-stats-tag ref-cly">
									<textarea id="ref'.$tmp.'-'.$id.'" name="ref['.$tmp.']['.$id.']" class="'.$data.'" rows="'.$textarea_rows.'" cols="50">'.$info['tag'].'</textarea>
								</td>
								<td headers="col-tag-auto-'.$cls_id.'" class="td-stats-tag ref-cly">
									<textarea id="ref-auto'.$tmp.'-'.$id.'" name="ref-auto" class="'.$data.' readonly" rows="'.$textarea_rows.'" cols="50" readonly="readonly">'.htmlspecialchars( $auto ).'</textarea>
								</td>
						';
					}

					print '
							</tr>
					';

					$i++;
				}
			}

			print '
						</tbody>
						<tfoot>
							<tr>
								<td id="pagination'.$cls_id.'" colspan="3">
			';

			if( $pages>1 ){
				$url_page = '/admin/stats/referencement/referencement.php?data='.$_GET['data'].'&amp;ctrl='.$_GET['ctrl'];

				if( $page>1 )
					print '<a href="'.$url_page.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
				for( $i=$pmin; $i<=$pmax; $i++ ){
					if( $i==$page )
						print '<b>'.$page.'</b>';
					else
						print '<a href="'.$url_page.'&amp;page='.($i).'">'.$i.'</a>';
					if( $i<$pmax )
						print ' | ';
				}
				if( $page<$pages )
					print ' | <a href="'.$url_page.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
			}

			print '
								</td>
							</tr>
							<tr>
								<td colspan="3">
									<input type="submit" name="save-ref" value="'._('Enregistrer').'" />
								</td>
							</tr>
						</tfoot>
					</table>
			';
		}

		print '
				</div>
			</div>
		';
	}else{
		print '<div class="notice">'._('Aucun contenu ne correspond à ce rapport.').'</div>';
	}

	print '
		</form>
	';

	require_once('admin/skin/footer.inc.php');

<?php

	/**	\file search.php
	 *	Cette page affiche les statistiques d'utilisation du moteur de recherche interne. Elle dispose de différents filtres et tris,
	 *	et permet également l'exportation des données au format Sylk.
	 */

	require_once('search.inc.php');
	require_once('websites.inc.php');
	require_once('tenants.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');

	// Par défaut, on arrive sur le jour en cours
	if( !isset($_GET['day']) && !isset($_GET['week']) && !isset($_GET['month']) && !isset($_GET['year']) ){
		$_GET['day'] = date('Y-m-d');
	}

	// Charge la liste des sites
	$websites = wst_websites_get();

	if( isset($_GET['wst']) ){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = false;
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
	}
	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Rechercher les logs
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';

	$stats = search_log_get( $wst_id, $date1, $date2, false, false, array(), 0, '', $lng, false );
	$stats_count = ria_mysql_num_rows($stats) ? ria_mysql_num_rows($stats) :  1;

	$volumes = $average_results = $statscount = 0;
	$statscount = ria_mysql_num_rows($stats);
	if( ria_mysql_num_rows($stats) ){
		$volumes = 0;
		$searchs_results = array();
		while( $stat = ria_mysql_fetch_array($stats) ){
			$volumes += $stat['volume'];
			if( isset($searchs_results[$stat['results']]) ){
				$searchs_results[$stat['results']] = $searchs_results[$stat['results']] + $stat['volume'];
			}else{
				$searchs_results[$stat['results']] = $stat['volume'];
			}
		}

		$a = $b =0;
		foreach( $searchs_results as $key=>$val ){
			$a += $key*$val;
			$b += $val;
		}
		if( $b!=0 ){
			$average_results = round($a/$b);
		}
		//$searchs_results_count = array_count_values($searchs);
		ria_mysql_data_seek($stats,0);
	}

	// Calcule le nombre de pages
	$pages = ceil($stats_count / 25);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages ){
			$page = $_GET['page'];
		}
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-5;
	if( $pmin<1 ){
		$pmin = 1;
	}
	$pmax = $pmin+9;
	if( $pmax>$pages ){
		$pmax = $pages;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Recherche') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Recherche').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Statistiques de recherches'); ?></h2>
<div class="stats-menu">
	<div id="riadatepicker"></div>
	<?php
		print view_websites_selector( $wst_id, true, '', true );
		print view_translate_menu( '/admin/stats/search.php', $lng, true );

		if(isset($_GET['date1'], $_GET['date2'], $_GET['last'])){
			print '
				<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>
				<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>
				<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>
			';
		}

		print '<input type="hidden" name="wst" id="wst" value="'.( ria_mysql_num_rows($websites)<=1 ? $config['wst_id'] : (isset($_GET['wst']) ? $_GET['wst'] : 0) ).'"/>';
		print '<input type="hidden" name="lng" id="lng" value="'.$lng.'"/>';
	?>

	<div id="tb-synthese-search">
		<table id="table-synthese-search" class="large">
			<caption></caption>
			<thead>
				<tr>
					<th id="hd-search-total"><?php print _('Recherches'); ?></th>
					<th id="hd-search-unique"><?php print _('Recherches distinctes'); ?></th>
					<th id="hd-search-wo"><?php print _('Sans résultat'); ?></th>
					<th id="hd-search-average"><?php print _('Nb. résultats moyen'); ?></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td><strong><?php print ria_number_format($volumes); ?></strong></td>
					<td><strong><?php print ria_number_format($statscount); ?></strong></td>
					<td><strong><?php print ria_number_format(isset($searchs_results[0]) ? $searchs_results[0] : 0); ?></strong></td>
					<td><strong><?php print ria_number_format($average_results); ?></strong></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="clear"></div>
</div>
<?php

	$seg = search_engines_get( 0, $wst_id );

	$colspan = 7;
	if( ria_mysql_num_rows($seg)>0 ){
		$colspan += 1;
	}
	if( !$wst_id && ria_mysql_num_rows($websites)>1 ){
		$colspan += 1;
	}
?>
<table class="checklist statsearch" id="table_search">
	<thead>
		<tr>
			<td class="head-first td-actions align-left" colspan="<?php print $colspan-3; ?>">
				<?php if( $stats_count ){ ?>
				<?php print  '<input type="button" class="btn-move export-btn" onclick="window.location.href=\'export-search-sylk.php?'.(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'\'" value="'._('Exporter').'" />'; ?>
				<?php } ?>
			</td>
			<th class="head-first align-right" colspan="3">
				<label for="filter"><?php print _('Filtrer (débutant par) :'); ?> </label><input type="text" name="filter" id="filter" value="" />
			</th>
		</tr>
		<tr id="table_search_col">
			<th id="search-str" title="<?php print _('Recherches effectuées'); ?>"><?php print _('Recherche'); ?></th>
			<th id="section" title="<?php print _('Sections utilisées pour cette recherche'); ?>"><?php print _('Sections'); ?></th>
			<th id="types" title="<?php print _('Filtres utilisés pour cette recherche'); ?>"><?php print _('Filtre'); ?></th>
			<th id="volume" class="align-right" title="<?php print _('Volume des recherches'); ?>"><?php print _('Volume'); ?></th>
			<th id="nbres" class="align-right" title="<?php print _('Nombre de résultats pour cette recherche'); ?>"><?php print _('Nb résultats'); ?></th>
			<th id="avg" class="align-right" title="<?php print _('Nombre moyen de pages de résultat consultées pour cette recherche'); ?>"><?php print _('Moy. pages'); ?></th>
			<th id="ctr" class="align-right" title="<?php print _('Nombre moyen de clics générés par cette recherche'); ?>"><?php print _('Moy. clics (90 derniers jours)'); ?></th>
			<?php
			print ria_mysql_num_rows($seg)>0 ? '<th id="search" title="'._('Moteur de recherche').'">'._('Emplacement de la recherche').'</th>' : '';
			print $wst_id<=0 && ria_mysql_num_rows($websites)>1 ? '<th id="website" title="'._('Sites sur lesquels les recherches sont effectuées').'">'._('Site web').'</th>' : ''; ?>
		</tr>
	</thead>
	<tbody id="lst_search">
	<?php

		if( ria_mysql_num_rows($stats) ){

			ria_mysql_data_seek( $stats, ($page-1)*25 );

			$count = 0;
			$count_volume = 0;
			while( $stat = ria_mysql_fetch_array($stats) ){
				if($count > 24) break;

				// Détermine les types de recherches
				$types = search_log_get_types($stat['scc']);

				// Détermine la catégorie de recherches
				$section = _('Toutes');
				if( $stat['section']!==null ){
					$r_section = ria_mysql_fetch_array( prd_categories_get($stat['section']) );
					$section = $r_section['name'];
				}

				$stat['nb_click'] = search_clickthroughs_get_count( $stat['seg'], $stat['scc'] );

				print '	<tr class="stat-search-info">
							<td headers="search-str" class="stat-search-info">
								<img class="img-stat-search" src="/admin/images/stats/redirection.svg" alt="'._('Créer une redirection').'" title="'._('Créer une redirection à partir de cette recherche').'" width="16" height="16" onclick="return addRedirectionSearch('.$stat['seg'].', \''.htmlspecialchars(addslashes(urldecode($stat['search']))).'\', \''.$stat['lng'].'\', '.$stat['wst_id'].');" />
								<a onclick="return show_search(\''.htmlspecialchars(addslashes(urldecode($stat['search']))).'\','.$stat['seg'].','.$stat['scc'].''.($stat['section']>0 ? ','.$stat['section'] : '').')"  href="/admin/search/index.php?q='.urlencode($stat['search']).'">'.htmlspecialchars((urldecode($stat['search']))).'</a>
							</td>
							<td headers="section" class="stat-search-info">'.$section.'</td>
							<td headers="types" class="stat-search-info">'.$types.'</td>
							<td headers="volume" class="stat-search-info right">'.ria_number_format($stat['volume']).'</td>
							<td headers="nbres" class="stat-search-info right">'.ria_number_format($stat['results']).'</td>
							<td headers="avg" class="stat-search-info right">'.str_replace(array(',00', '.00'), '',ria_number_format($stat['avg_page'], NumberFormatter::DECIMAL, 2)).'</td>
							<td headers="ctr" class="stat-search-info right">'.str_replace(array(',00', '.00'), '',ria_number_format($stat['nb_click'] / ($stat['volume'] > 0 ? $stat['volume'] : 1), NumberFormatter::DECIMAL, 2)).'</td>
				';

				print ria_mysql_num_rows($seg)>0 ?'<td headers="search" class="stat-search-info">'.htmlspecialchars( $stat['seg_name'] ).'</td>' : '';
				if( !$wst_id && ria_mysql_num_rows($websites)>1 ){
					$website = ria_mysql_fetch_array(wst_websites_get($stat['wst_id']));
					print '	<td headers="website" class="stat-search-info">'.htmlspecialchars( $website['name'] ).'</td>';
				}

				print '	</tr>';
				$count ++;
			}

		}else{
	?>
			<tr><td colspan="<?php print $colspan; ?>"><?php print _('Aucun résultat pour la période sélectionnée'); ?></td></tr>
	<?php } ?>
	</tbody>
	<tfoot>
		<tr>
			<td id="pagination" colspan="<?php print $colspan; ?>">
				<?php
					print '<div class="page float-left">'._('Page').' '.$page.' / '.$pages.'</div>
					<div class="float-right">';
					if( $pages>1 ){
						if( $page>1 ){
							print '<a href="search.php?page='.($page-1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'">&laquo; '._('Page précédente').'</a> | ';
						}
						for( $i=$pmin; $i<=$pmax; $i++ ){
							if( $i==$page ){
								print '<b>'.$page.'</b>';
							}else{
								print '<a href="search.php?page='.$i.(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'">'.$i.'</a>';
							}
							if( $i<$pmax ){
								print ' | ';
							}
						}
						if( $page<$pages ){
							print ' | <a href="search.php?page='.($page+1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'">'._('Page suivante').' &raquo;</a>';
						}
					}
					print '</div>';
				?>
			</td>
		</tr>
		<tr>
			<td class="align-left td-actions" colspan="<?php print $colspan; ?>">
				<?php if( $stats_count ){ ?>
				<?php print  '<input type="button" class="export-btn" onclick="window.location.href=\'export-search-sylk.php?'.(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').'\'" value="'._('Exporter').'" />'; ?>
				<?php } ?>
				<input type="button" name="add-redir-search" id="btn-add-redir-search" value="<?php print _('Ajouter une redirection'); ?>" title="<?php print _('Ajouter une redirection de recherche'); ?>" />
			</td>
		</tr>
	</tfoot>
</table>
<script>
   <?php view_date_initialized( search_log_count_volume($wst_id, $date1, $date2), '/admin/stats/search.php', false, array('refresh_in_ajax'=>true) ); ?>
</script>
<?php
	require_once('admin/skin/footer.inc.php');
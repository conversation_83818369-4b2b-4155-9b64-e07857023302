<?php

	/**	\file ajax-action-search.php
	 *
	 *	Gère les tâches réalisées en ajax dans la popup de prévisualisation des résultats de recherche.
	 *	Prend en charge les actions suivantes :
	 *	 - Obtenir la liste des résultats pour une recherche donnée
	 *	 - Obtenir le nombre de résultats pour une recherche donnée
	 *	 - Masquer un résultat dans une recherche
	 *	 - Masquer un résultat dans toutes les recherches
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');
	
	require_once( 'search.inc.php' );

	if( isset($_POST['cnt'], $_POST['seg'], $_POST['scc'], $_POST['all'], $_POST['publish']) ){
		
		// Gère les actions de publication / dépublication d'un résultat dans les recherches
		$_POST['score'] = isset($_POST['score']) ? $_POST['score'] : 0;
		$_POST['clics'] = isset($_POST['clics']) ? $_POST['clics'] : 0;
		$_POST['nbkey'] = isset($_POST['nbkey']) ? $_POST['nbkey'] : 0;
		
		if( $_POST['all']=='no' ){ // Seulement dans ce résultat
			
			if( $_POST['publish']==1 ){
				$res = search_results_publish($_POST['cnt'], $_POST['scc'], $_POST['seg'], $_POST['score'], $_POST['clics'], $_POST['nbkey'], false);
			}else{
				$res = search_results_unpublish($_POST['cnt'], $_POST['scc'], $_POST['seg'], $_POST['score'], $_POST['clics'], $_POST['nbkey'], false);
			}
			return $res;
		
		} else { // Dans tous les résultats
		
			if( $_POST['publish']==1 ){
				$res = search_contents_publish($_POST['cnt']);
			}else{
				$res = search_contents_unpublish($_POST['cnt']);
			}
			return $res;
		
		}
	
	} elseif( isset($_POST['getNbRes']) ){
	
		// Calcule le nombre de résultats retournés par une recherche donnée
		$results = search3( $_POST['seg'], urldecode($_POST['search']), 1, 0, $_POST['active'] ? true : false, $_POST['section']>0 ? $_POST['section'] : false, 7);
		$ar_results = array();
		
		if( $results ){
			$ar_masked = array();
			while( $r = ria_mysql_fetch_array($results) ){
				if( $_POST['active'] ){
					$publish = true;
					if( $r['type_code']=='prd' ){
						$rp = prd_products_get_simple( $r['tag'] );
						$publish = !$rp || !ria_mysql_num_rows($rp) ? false : ria_mysql_result( $rp, 0, 'publish' );
					}
					
					if( !isset($ar_masked[ $r['type_code'] ]) ){
						$ar_masked[ $r['type_code'] ] = array();
					}
					
					if( $r['hide'] || !$publish ){
						continue;
					}
					
					if( in_array($r['tag'], $ar_masked[ $r['type_code'] ]) ){
						if( !$r['hide'] ){
							search_results_unpublish($r['id'], $_POST['scc'], $_POST['seg'], $r['score'], $r['click'], $r['nbkey']);
							if( $_POST['active'] ) {
								continue;
							}
						}
					}
					$ar_masked[ $r['type_code'] ][] = $r['tag'];
				}
				
				$ar_results[] = $r;
			}
		}
		
		print sizeof($ar_results);
	} elseif( isset($_POST['getResPage']) ){
	
		// Lance une recherche et fourni ses résultats
		$results = search3( $_POST['seg'], urldecode($_POST['search']), 1, 0, $_POST['active'] ? true : false, $_POST['section']>0 ? $_POST['section'] : false, 7);
		$ar_results = array();
		
		if( $results && ria_mysql_num_rows($results) ){
			$ar_masked = array();
			
			while( $r = ria_mysql_fetch_array($results) ){
				if( $_POST['active'] ){
					$publish = true;
					if( $r['type_code']=='prd' ){
						$rp = prd_products_get_simple( $r['tag'] );
						$publish = !$rp || !ria_mysql_num_rows($rp) ? false : ria_mysql_result( $rp, 0, 'publish' );
					}
					
					if( !isset($ar_masked[ $r['type_code'] ]) ){
						$ar_masked[ $r['type_code'] ] = array();
					}
					
					if( $r['hide'] || !$publish ){
						continue;
					}
					
					if( in_array($r['tag'], $ar_masked[ $r['type_code'] ]) ){
						if( !$r['hide'] ){
							search_results_unpublish($r['id'], $_POST['scc'], $_POST['seg'], $r['score'], $r['click'], $r['nbkey']);
							if( $_POST['active'] ) {
								continue;
							}
						}
					}
					$ar_masked[ $r['type_code'] ][] = $r['tag'];
				}
				
				$ar_results[] = $r;
			}
		}
		
		if( !is_array($ar_results) || sizeof($ar_results)<=0 ){
			print _('Aucun résultat');
		} else {
			$page = isset($_POST['page']) ? $_POST['page'] : 1;
			
			$res = array_slice( $ar_results , 0 );
			
			$count = 1;
			$isize = $config['img_sizes']['small'];
			foreach( $res as $r ){
				if( $count<=$_POST['nbres'] ){
					$count++;
					continue;
				}
				if( $count>($config['prd_list_length']*$page) ){
					break;
				}
				
				$desc = $r['desc'];
				$desc = substr( $desc, 0, 102 );
				$desc = substr( $desc, 0, strrpos($desc,' ') );
				$desc = $desc." ...";	
				
				// On vérifie s'il y a une image, sinon on affiche une image par défaut
				$url_img = '/admin/images/default.jpg';
				if( is_numeric($r['img_id']) && $r['img_id'] > 0 ){
					$url_img = $config['img_url'].'/'.$isize['dir'].'/'.$r['img_id'].'.'.$isize['format'];
					if( !ria_file_exists_byurl($url_img) ){
						$url_img = view_admin_get_img_default('small');
					}
				}else{
					$url_img = view_admin_get_img_default('small');
				}
				
				$publish = true; $link = '';
				switch( $r['type_code'] ){
					case 'prd':
						$link .= _('Catalogue').' &raquo; ';
						preg_match( '/cat=([0-9]+)/', $r['alt_url'], $matches );
						$cat = $matches[1];
						$parents = prd_categories_parents_get( $cat );
						while( $p = ria_mysql_fetch_array($parents) ){
							$link .= $p['title'].' &raquo; ';
						}
						$rcat = prd_categories_get( $cat );
						if( ria_mysql_num_rows($rcat) ){
							$cat = ria_mysql_fetch_array($rcat);
							$link .= $cat['title'].' &raquo; ';
						}
						$prd = ria_mysql_fetch_array( prd_products_get_simple( $r['tag'] ) );
						$publish = $prd['publish'];
						$link .= $prd['name'];
						break;
					case 'usr':
						$link .= _('Clients').' &raquo; ';
						$link .= $r['name'];
						break;
					case 'dlv-str':
						$link .= _('Magasins').' &raquo; ';
						$link .= $r['name'];
						break;
					case 'ord':
						$link .= _('Commandes').' &raquo; ';
						$link .= $r['name'];
						break;
					default:
						$link .= $r['alt_url'].' - '.$r['type_name'];
				}
				
				$class = '';
				$hide_res = $r['hide'] || !$r['publish'] || !$r['publish_system'] || !$publish;
				
				if( $count>$config['prd_list_length'] ){
					$class .= ' cnt-masked';
				}
				if( $hide_res ){
					$class .= ' cnt-hide';
				}
				
				print '<tr'.( trim($class) ? ' class="'.trim($class).'"' : '').' id="tr-'.$r['id'].'">
					<td '.( $_POST['cnt']==$r['id'] ? 'class="border-unright"' : '' ).'><div class="stat-num">#'.($count).'</div>
				';
				
				print $r['click']>0 ? '<div class="zone-clic" title="'._('Nombre de clics pour ce produit dans cette recherche').'"><div class="num-clic">'.($r['click']>1 ? $r['click'].' clics' : $r['click'].' clic').'</div></div>' : '<div class="zone-no-clic" title="'._('Nombre de clics pour ce produit dans cette recherche').'"><div class="num-clic">0 clic</div></div>';
				print '</td><td class="stat-img'.( $_POST['cnt']==$r['id'] ? ' border-unleft-unright' : '' ).'">
						<a href="'.$r['alt_url'].'" target="_blank">
							<img id="img-'.$r['id'].'" '.($hide_res ? 'class="img-cnt-hide"' : '').' src="'.$url_img.'" width="'.$isize['width'].'" height="'.$isize['height'].'" alt="'.htmlspecialchars(urldecode($r['name'])).'" title="'.htmlspecialchars(urldecode($r['name'])).'" />
						</a>
					</td>
					<td class="stat-info'.( $_POST['cnt']==$r['id'] ? ' border-unleft' : '' ).'">
						<div><a id="name-'.$r['id'].'" '.($hide_res ? 'class="cnt-hide"' : '').' href="'.$r['alt_url'].'" target="_blank">'.$r['id'].' - '.urldecode($r['name']).'</a></div>
						<div id="desc-'.$r['id'].'" '.($hide_res ? 'class="cnt-hide"' : 'class="wo-search-desc"').'>'.$desc.'</div>
						<div><a id="alt-url-'.$r['id'].'" '.($hide_res ? 'class="cnt-hide-url"' : 'class="wo-search-url"').' href="'.$r['alt_url'].'" target="_blank">
							'.$link.'
						</a></div>';
						if( $publish ){
							
							$title = $alt = _('Une fois désactivé, ce résultat n\'apparaîtra plus pour cette recherche');
							$onclick = 'action_search( '.$r['id'].', '.$r['seg'].', '.$r['scc'].', false, false, '.$r['score'].', '.$r['click'].', '.$r['nbkey'].' )';
							
							print '	<div style="display:'.(!$r['hide'] ? 'block' : 'none').';" class="del-search" id="del-search-'.$r['id'].'" onclick="'.$onclick.'">
										<img height="12" width="12" src="../images/del-cat.svg" name="del-search" title="'.$title.'" alt="'.$alt.'" /> ';
							print 		_('Désactiver dans ce résultat');
							print '	</div>';
							
							$title = $alt = _('Une fois réactivé, ce résultat apparaîtra de nouveau dans cette recherche');
							$onclick = 'action_search( '.$r['id'].', '.$r['seg'].', '.$r['scc'].', true, false, '.$r['score'].', '.$r['click'].', '.$r['nbkey'].' )';
							
							print '	<div style="display:'.(!$r['hide'] ? 'none' : 'block').';" class="del-search" id="add-search-'.$r['id'].'" onclick="'.$onclick.'">
										<img height="12" width="12" src="../images/add-search.png" name="add-search" title="'.$title.'" alt="'.$alt.'" />';
							print 		_('Réactiver dans ce résultat');
							print '	</div>';

							$title = $alt = _('Une fois désactivé, ce résultat n\'apparaîtra plus pour aucune recherche');
							$onclick = 'action_search( '.$r['id'].', '.$r['seg'].', '.$r['scc'].', false, true, '.$r['score'].', '.$r['click'].', '.$r['nbkey'].' )';
							
							print '	<div style="display:'.($r['publish'] && $r['publish_system'] && $publish ? 'block' : 'none').';" class="del-all-search" id="del-all-search-'.$r['id'].'" onclick="'.$onclick.'">
										<img height="12" width="12" src="../images/del-cat.svg" name="del-search" title="'.$title.'" alt="'.$alt.'" /> ';
							print 		_('Désactiver dans tous les résultats');
							print '	</div>';
							
							$title = $alt = _('Une fois réactivé, ce résultat apparaîtra de nouveau dans toutes les recherches');
							$onclick = 'action_search( '.$r['id'].', '.$r['seg'].', '.$r['scc'].', true, true, '.$r['score'].', '.$r['click'].', '.$r['nbkey'].' )';
							
							print '	<div style="display:'.($r['publish'] && $r['publish_system'] && $publish  ? 'none' : 'block').';" class="del-all-search" id="add-all-search-'.$r['id'].'"onclick="'.$onclick.'">
										<img height="12" width="12" src="../images/add-search.png" name="del-search" title="'.$title.'" alt="'.$alt.'" />';
							print 		_('Réactiver dans tous les résultats');
							print '	</div>';
						} else {
							print '<div class="search-no-publish">'._('Ce produit est actuellement dépublié.').'</div>';
						}
						print '</td>
				</tr>';
				
				$count++;
			}
		}
	}
	
	exit;
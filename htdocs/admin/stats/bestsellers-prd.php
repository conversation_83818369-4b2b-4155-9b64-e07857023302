<?php

	/** \file bestsellers-prd.php
	 *
	 * 	Cette page affiche les meilleures ventes par produits. Il s'agit d'un tableau contenant tous les produits les plus vendus,
	 *	triée par défaut sur le chiffre d'affaires total décroissant.
	 *
	 */

	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_BESTSELLER_PRD');

	// Par défaut, on arrive sur le jour en cours
	if( isset($_GET['date1'], $_GET['date2']) && isdate($_GET['date1']) && isdate($_GET['date2']) ){
		view_date_in_session($_GET['date1'], $_GET['date2']);
	}

	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	// Filtre sur l'origine de la commande
	$_SESSION['origin'] = isset($_GET['origin']) ? $_GET['origin'] : ( !IS_AJAX && isset($_SESSION['origin']) ? $_SESSION['origin'] : false );

	$wst_id = 0;
	if( isset($_GET['wst']) ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	$params = view_origins_get_params();
	$origin = $params['origin'];
	$query_origin = '';
	if ($origin && is_array($_SESSION['origin'])) {
		$query_origin = http_build_query(array('origin'=>$_SESSION['origin']));
	}
	$gescom = $params['gescom'];

	if ($gescom) {
		$query_origin = 'gescom';
	}
	$is_web = $params['is_web'];
	if( $is_web ){
		$query_origin = 'web';
	}
	// Gère le filtre par représentant
	if( isset($_GET['seller']) ){
		$_GET['seller'] = str_replace( 'seller-', '', $_GET['seller'] );
		if( is_numeric($_GET['seller']) ){
			$_SESSION['ord_seller_id'] = $_GET['seller'];
		}
	}
	$seller_id = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;
	if( !IS_AJAX ){

		// Fil d'ariane
		Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
			->push( _('Statistiques'), '/admin/stats/index.php' )
			->push( _('Commandes'), '/admin/stats/orders.php' )
			->push( _('Meilleures ventes') );

		// Défini le titre de la page
		define('ADMIN_PAGE_TITLE', _('Meilleures ventes').' - '._('Commandes').' - '._('Statistiques'));
		require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Meilleures ventes'); ?></h2>

	<div class="stats-menu">
		<form action="" class="riaFilters">
			<div id="riadatepicker"></div>
			<input type="hidden" name="last" id="last" value="">
			<input type="hidden" name="date1" id="date1" value="<?php echo $date1?>">
			<input type="hidden" name="date2" id="date2" value="<?php echo $date2?>">
			<?php print view_websites_selector( $wst_id, true, 'riapicker', true ); ?>
			<input type="hidden" name="wst" id="wst" value="<?php echo $wst_id?>">
			<input type="hidden" name="origin" id="origin" value="<?php echo $query_origin?>">
			<input type="hidden" name="gescom" id="gescom" value="<?php echo $gescom?>">
			<input type="hidden" name="seller" id="seller" value="<?php echo $seller_id?>">
			<?php
				// Le sélecteur d'origine de commande n'est pertinent que s'il y a un ou plusieurs sites
				if( tnt_tenants_have_websites() ){
					print view_origins_selector();
				}
				print view_sellers_selector();
			?>
		</form>
		<div class="clear"></div>
	</div>

	<p>
		<?php print _('Vous trouverez ci-dessous les produits identifiés comme étant les meilleures ventes :'); ?>
		<button  onclick="window.location.href='/admin/stats/export-bestsellers-prd.php'" name="export" class="btn-export"><?php print _('Exporter'); ?></button>
	</p>
	<input type="hidden" name="best-wst-id" value="<?php print $wst_id; ?>" />
	<input type="hidden" name="ord_seller_id" id="ord_seller_id" value="<?php print $seller_id; ?>" />
	<table id="bestseller-prd" class="checklist tablesorter">
		<caption><?php print _('Meilleurs ventes'); ?></caption>
		<thead>
			<tr>
				<th id="best-ref"><?php print _('Référence'); ?></th>
				<th id="best-name"><?php print _('Désignation'); ?></th>
				<th id="best-orders" class="align-right"><?php print _('Ventes'); ?></th>
				<th id="best-by-ord" class="align-right"><?php print _('Par commande'); ?></th>
				<th id="best-gross-margin" class="align-right"><abbr title="<?php print _('Marge brute dégagée sur ce produit'); ?>"><?php print _('Marge brute'); ?></abbr></th>
				<th id="best-ca" class="align-right"><abbr title="<?php print _('Chiffre d\'affaires pour le produit'); ?>"><?php print _('CA'); ?></abbr></th>
				<th id="best-ca-ord" class="align-right"><abbr title="<?php print _('Chiffre d\'affaires des commandes contenant ce produit'); ?>"><?php print _('CA Total'); ?></abbr></th>
			</tr>
		</thead>
		<tbody><?php
}
			$rbest = stats_bestsellers_get( $date1, $date2, 100, $origin, $gescom, $wst_id, false, 0, 0, false, 0, 0, $seller_id );
			if( !$rbest || !ria_mysql_num_rows($rbest) ){
				print '
					<tr>
						<td colspan="7">'._('Aucune donnée ne permet de charger les meilleurs ventes pour le moment.').'</td>
					</tr>
				';
			}else{
				while( $best = ria_mysql_fetch_array($rbest) ){
					$link = '/admin/catalog/product.php?cat=0&amp;prd='.$best['id'];

					print '
						<tr>';
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
					print '	<td headers="best-ref"><a href="'.$link.'">'.view_prd_is_sync($best).' '.htmlspecialchars( $best['ref'] ).'</a></td>
							<td headers="best-name"><a href="'.$link.'">'.htmlspecialchars( $best['name'] ).'</a></td>';
					}else{
					print '	<td headers="best-ref">'.view_prd_is_sync($best).' '.htmlspecialchars( $best['ref'] ).'</td>
						    <td headers="best-name">'.htmlspecialchars( $best['name'] ).'</td>';
					}
					print ' <td headers="best-orders" class="right">'.ria_number_format($best['orders']).'</td>
							<td headers="best-by-ord" class="right">'.ria_number_format($best['qte'] / $best['orders']).'</td>
							<td headers="best-gross-margin" class="right">'.ria_number_format($best['margin'], NumberFormatter::CURRENCY, 2).'</td>
							<td headers="best-ca" class="right">'.ria_number_format($best['total_ht'], NumberFormatter::CURRENCY, 2).'</td>
							<td headers="best-ca-ord" class="right">'.ria_number_format($best['ord_total_ht'], NumberFormatter::CURRENCY, 2).'</td>
						</tr>
					';
				}
			}
	if( !IS_AJAX ){
?>
		</tbody>
	</table>

	<div class="notice">
		<ul>
			<?php
			print '	<li>' . _('Ventes : Nombre de commandes contenant cet article') . '</li>
					<li>' . _('Par commande : Quantité moyenne par commande') . '</li>
					<li>' . _('Marge brute : Marge brute dégagée grâce aux ventes de ce produit') . '</li>
					<li>' . _('CA : Chiffre d\'affaires généré par ce produit') . '</li>
					<li>' . _('CA Total : Chiffre d\'affaires généré par les commandes contenant ce produit') . '</li>';
			?>
		</ul>
	</div>

	<script><!--
		var is_bestsellers_prd = true;
		<?php view_date_initialized( 0, '/admin/stats/bestsellers-prd.php', false, array('refresh_in_ajax'=>false) ); ?>
	--></script>

<?php
		require_once('admin/skin/footer.inc.php');
	}
?>
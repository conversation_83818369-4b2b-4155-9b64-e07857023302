<?php

	/**	\file export-prd-conversion.php
	 * 
	 * 	Ce fichier est chargé d'exporter les données sur les taux de conversion des fiches produit.
	 * 
	 */

	require_once( 'excel/PHPExcel.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_CONVERSION');

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();
	
	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle( _("Export des performances des fiches produits") )
								 ->setSubject( _("Export des performances des fiches produits") )
								 ->setDescription( _("Export des performances des fiches produits") )
								 ->setKeywords( _("export performances fiches produits") )
								 ->setCategory("");
	
	// Création du fichier Excel
	$obj = $objPHPExcel->getActiveSheet();
	
	// Première feuille : Taux de remplissage
	$obj->setTitle(_("Performances fiches produits"));

	// Entete du tableau des poids brut
	$countStartX = $countX = 1;
	$countStartY = $countY = 1;
	$alphabet = array('','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD');
	$results_count = $results && ria_mysql_num_rows($results) ? ria_mysql_num_rows($results) : 0;
	$obj->setCellValue($alphabet[$countStartX].$countStartY, _('Conversions'));
	$countY++;
	foreach($columns as $key => $val) { 
		$obj->setCellValue($alphabet[$countX].$countY, $val['name']);
		$obj->getColumnDimension($alphabet[$countX])->setWidth($val['widthE']);
		if($results_count > 0) {
			if($val['type'] == 'float') {
				$obj->getStyle($alphabet[$countX].($countY+1).':'.$alphabet[$countX].($countY+1+$results_count))->getNumberFormat()->applyFromArray(	array( 'code' => PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00 ) );
			}
			if($val['type'] == 'int') {
				$obj->getStyle($alphabet[$countX].($countY+1).':'.$alphabet[$countX].($countY+1+$results_count))->getNumberFormat()->applyFromArray(	array( 'code' => '# ##0' ) );
			}
		}
		$countX++;
	}
	$countX--;
	$obj->mergeCells($alphabet[$countStartX].$countStartY.':'.$alphabet[$countX].$countStartY);
	$obj->getStyle($alphabet[$countStartX].$countStartY.':'.$alphabet[$countX].$countY)->getFont()->setBold(true);
	$obj->getStyle($alphabet[$countStartX].$countStartY)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$obj->getStyle($alphabet[$countStartX].$countStartY)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
	$obj->getStyle($alphabet[$countStartX].$countY.':'.$alphabet[$countX].$countY)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('DDDDDD');
	
	if($results && ria_mysql_num_rows($results)) {
		while($res = ria_mysql_fetch_array($results)) {
			$countX = 0;
			$countY++;
			foreach($columns as $key => $val) { 
				$countX++;
				$obj->setCellValue($alphabet[$countX].$countY, $res[$key] ? $res[$key] : 0);
			}
		}
	}

	$obj->getStyle($alphabet[$countStartX].$countStartY.':'.$alphabet[$countX].$countY)->getBorders()->applyFromArray(array(
			'allborders' => array( 
				'style' => PHPExcel_Style_Border::BORDER_THIN, 
				'color' => array( 'rgb' => '808080') 
			)
		));
	
	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'._('performances-des-fiches-produits').'.xls"');
	header('Cache-Control: max-age=0');
	
	// Ecrit le fichier et le sauvegarde
	$objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
	$objWriter->save('php://output');
	exit;

<?php

	/**	\file search-wo-results.php
	 *
	 *	Cette page affiche la liste des recherches effectuées dans le moteur de recherche interne et qui n'ont donné aucun résultat.
	 */

	require_once('search.inc.php');
	require_once('websites.inc.php');
	require_once('tenants.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH_NO_RESULT');

	// Charge la liste des sites gérés depuis ce back-office (détermine si on a besoin d'un sélecteur)
	$websites = wst_websites_get();

	if( ria_mysql_num_rows($websites)>1 ){
		if( isset($_GET['wst']) ){
			if( $_GET['wst']=='all' ){
				$_SESSION['websitepicker'] = false;
			}else{
				$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
			}
		}
	}

	// Par défaut, on arrive sur le jour en cours
	if( isset($_GET['date1']) && isdate($_GET['date1']) ){ $_SESSION['datepicker_date1'] = $_GET['date1']; }
	if( isset($_GET['date2']) && isdate($_GET['date2']) ){ $_SESSION['datepicker_date2'] = $_GET['date2']; }

	$wst_id = isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : false;

	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';

	// Par défaut, on arrive sur le jour en cours
	if( !isset($_GET['day']) && !isset($_GET['week']) && !isset($_GET['month']) && !isset($_GET['year']) )
		$_GET['day'] = date('Y-m-d');

	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Bouton Exporter
	if( isset($_POST['search-wo-result']) ){
		header('Location: export-wo-result.php?wst='.$wst_id);
		exit;
	}

	// Détermine si le log doit être limité aux seules recherches n'ayant pas de remplacement
	$wosubstitut = false;
	if( isset($_REQUEST['wosubstitut']) ){
		$wosubstitut = true;
	}

	if( isset($_POST['save']) && isset($_POST['search']) && is_array($_POST['search']) ){
		foreach( $_POST['search'] as $search => $wst_subsitutes ){
			if( !is_array($wst_subsitutes) ){
				continue;
			}

			foreach( $wst_subsitutes as $search_wst_id => $seg_subsitutes ){
				if( !is_array($seg_subsitutes) ){
					continue;
				}

				foreach( $seg_subsitutes as $search_seg_id => $search_substitute ){
					// Si aucune redirection de définie, on supprimera celle ayant été ajoutée précédement
					// Sinon on ajoutera ou on mettra à jour la redirection
					if( trim($search_substitute) == '' || search_substitut_exists($search, $lng, $search_wst_id, $search_seg_id, 0, $search_substitute) ){
						if( !search_substitut_update($search, $search_substitute, $lng, $search_wst_id, $search_seg_id) ){
							$error = str_replace(
								'#param[recherche]#',
								$search,
								_("Une erreur est survenue lors de la mise à jour de la redirection pour la recherche \"#param[recherche]#\".")
							);
						}
					}else{
						if( !search_substitut_add($search, $search_substitute, $lng, $search_wst_id, $search_seg_id) ){
							$error = str_replace(
								'#param[recherche]#',
								$search,
								_("Une erreur est survenue lors de l'ajout de la redirection pour la recherche \"#param[recherche]#\".")
							);
						}
					}
				}
			}
		}

		if( !isset($error) ){
			if( !isset($_SESSION['search-wo-results-success'])){
				$_SESSION['search-wo-results-success'] = array();
			}
			$_SESSION['search-wo-results-success'][] = _("L'enregistrement de vos modifications à bien été pris en compte");
			header('Location:' . $_SERVER['REQUEST_URI']);
			exit;
		}
	}

	// Charge la liste des recherches vides survenues dans la période sélectionnée
	$stats = search_log_get( $wst_id, $date1, $date2, true, $wosubstitut, array(), 0, '', $lng, false );

	$stats_count = ria_mysql_num_rows($stats) ? ria_mysql_num_rows($stats) :  0;

	// Calcule le nombre de pages
	$pages = ceil($stats_count / 25);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages )
			$page = $_GET['page'];
	}

	if( $stats_count ){
		ria_mysql_data_seek( $stats, ($page-1)*25 );
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-5;
	if( $pmin<1 ){
		$pmin = 1;
	}
	$pmax = $pmin+9;
	if( $pmax>$pages ){
		$pmax = $pages;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Recherches'), '/admin/stats/search.php' )
		->push( _('Recherches sans résultat') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Recherches sans résultat').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Recherches sans résultat'); ?> (<?php print ria_number_format($stats_count); ?>)</h2>
<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br( htmlspecialchars($error) ).'</div>';
	}
	if (isset($_SESSION['search-wo-results-success'])){
		foreach( $_SESSION['search-wo-results-success'] as $success ){
			print '<div class="error-success">'.htmlspecialchars( $success ).'</div>';
		}
		unset($_SESSION['search-wo-results-success']);
	}
?>
<div class="stats-menu stats-menu-wo-results">
	<div id="riadatepicker"></div>
	<?php
		print view_websites_selector( $wst_id, true, '', true );
		print view_translate_menu( '/admin/stats/search-wo-results.php', $lng, true );
	?>
	<div id="riaothersfilters"><input type="checkbox" value="1" name="wosubstitut" id="wosubstitut" <?php print isset($_REQUEST['wosubstitut']) ? 'checked="checked"' : '' ?>/><label for="wosubstitut">&nbsp;<?php print _('Limiter aux recherches sans remplacement'); ?></label></div>
	<?php
		if(isset($_GET['date1'], $_GET['date2'], $_GET['last'])){
			print '<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>';
			print '<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>';
			print '<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>';
		}
		if(isset($_GET['wst'])){
			print '<input type="hidden" name="wst" id="wst" value="'.$_GET['wst'].'"/>';
		}
	?>

	<div class="clear"></div>

</div>
<?php
	$othersfilters = (isset($_REQUEST['wosubstitut']) ? '&amp;wosubstitut=1' : '').(isset($_REQUEST['last']) ? '&amp;last='.$_REQUEST['last'] : '');
	$wst = wst_websites_get(); ?>
	<form id="seach-wo-results" action="/admin/stats/search-wo-results.php<?php print '?page='.($page).(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']: '').(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1'].(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2'] : ''): '').$othersfilters; ?>&amp;lng=<?php print $lng; ?>" method="post">
		<input type="hidden" name="add-res-id" id="add-res-id" value="" />
		<input type="hidden" name="scc-id" id="scc-id" value="" />
		<input type="hidden" name="seg-id" id="seg-id" value="" />
		<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />
		<?php
			$wst = wst_websites_get();
			$seg = search_engines_get(0,$wst_id);

			$colspan = 5;
			if( ria_mysql_num_rows($seg)>1 ) {
				$colspan++;
			}
			if( !$wst_id && ria_mysql_num_rows($wst)>1 ) {
				$colspan++;
			}
		?>
		<table class="checklist statsearch" id="table_search">
			<thead>
				<tr>
					<th id="search" title="<?php print _('Recherches sans resultats'); ?>"><?php print _('Recherche'); ?></th>
					<th id="section" title="<?php print _('Sections utilisées pour cette recherche'); ?>"><?php print _('Sections'); ?></th>
					<th id="types" title="<?php print _('Filtres utilisés pour cette recherche'); ?>"><?php print _('Filtres'); ?></th>
					<th id="volume" class="align-right" title="<?php print _('Volume des recherches'); ?>"><?php print _('Volume'); ?></th>
					<?php 	print ria_mysql_num_rows($seg)>1 ? '<th id="emp" title="'._('Moteur de recherche').'">'._('Emplacement de la recherche').'</th>' : '';
							print !$wst_id && ria_mysql_num_rows($wst)>1 ? '<th id="website" title="'._('Sites sur lesquels les recherches sont effectuées').'">'._('Site web').'</th>' : '' ?>
					<th id="replace" title="<?php print _('Rediriger la recherche sans résultat vers la recherche contenue dans cette colonne'); ?>"><?php print _('Remplacer par'); ?></th>
				</tr>
			</thead>

			<?php
			if( $stats_count > 0 ){
				print '<tbody id="lst_search">';
				$count = 0;
				$count_volume = 0;
				while( $stat = ria_mysql_fetch_array($stats) ){

					if($count > 24) break;

					$section = 'Toutes';
					$types = search_log_get_types($stat['scc']);

					print '	<tr>
								<td headers="search" class="stat-search-info">'.htmlspecialchars(urldecode($stat['search'])).'</td>
								<td headers="section" class="stat-search-info">'.htmlspecialchars($section).'</td>
								<td headers="types" class="stat-search-info">'.htmlspecialchars($types).'</td>
								<td headers="volume" class="stat-search-info align-right">'.ria_number_format($stat['volume']).'</td>';

					print ria_mysql_num_rows($seg)>1 ?'<td headers="emp" class="stat-search-info">'.htmlspecialchars($stat['seg_name']).'</td>' : '';
					if( !$wst_id && ria_mysql_num_rows($wst)>1 ){
						$website = ria_mysql_fetch_array(wst_websites_get($stat['wst_id']));
						print '	<td headers="website" class="stat-search-info">'.htmlspecialchars($website['name']).'</td>';
					}

					print '
						<td headers="replace" class="align-right">
							<input type="text"
								name="search['.htmlspecialchars($stat['search']).']['.htmlspecialchars($stat['wst_id']).']['.htmlspecialchars($stat['seg']).']"
								id="replace-'.$count.'"
								value="'.htmlspecialchars(
									isset(
										$_POST['search'],
										$_POST['search'][$stat['search']],
										$_POST['search'][$stat['search']][$stat['wst_id']],
										$_POST['search'][$stat['search']][$stat['wst_id']][$stat['seg']]
									)
										? $_POST['search'][$stat['search']][$stat['wst_id']][$stat['seg']]
										: $stat['substitut']
								).'"
								class="w80"
								/>
							<img onclick="return show_search('.$count.','.str_replace('"','',json_encode($stat['seg'])).',0)"
								class="img-stat-search"
								src="/admin/images/petite_loupe_active.svg"
								name="loupe"
								title="'._('Pré-visualisation de la recherche').'"
								alt="'._('Pré-visualisation').'"
								/>
						</td>
					</tr>';
					$count ++;
				}
				print '</tbody>';
			}else{ ?>
				<tbody id="lst_search">
					<tr><td colspan="<?php print $colspan; ?>"><?php print _('Aucun résultat pour la période sélectionnée'); ?></td></tr>
				</tbody>
			<?php }	?>
			<tfoot>
				<tr>
					<td id="pagination" colspan="<?php print $colspan; ?>">
						<?php
							if( $pages>1 ){
								print '<div class="page float-left">'._('Page').' '.$page.' / '.$pages.'</div>';
								if( $page>1 ){

									print '<a href="search-wo-results.php?page='.($page-1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').$othersfilters.'">&laquo; '._('Préc').'</a> | ';
								}
								print '<div class="float-right">';
								for( $i=$pmin; $i<=$pmax; $i++ ){
									if( $i==$page )
										print '<b>'.$page.'</b>';
									else
										print '<a href="search-wo-results.php?page='.$i.(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').$othersfilters.'">'.$i.'</a>';
									if( $i<$pmax )
										print ' | ';
								}
								if( $page<$pages ){
									print ' | <a href="search-wo-results.php?page='.($page+1).(isset($_GET['date1']) ? '&amp;date1='.$_GET['date1']:'').(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']:'').(isset($_GET['last']) ? '&amp;last='.$_GET['last']:'').(isset($_GET['wst']) ? '&amp;wst='.$_GET['wst']:'').$othersfilters.'">'._('Suiv').' &raquo;</a>';
								}
								print '</div>';
							}

						?>
					</td>
				</tr>
				<tr>
					<td colspan="<?php print $colspan; ?>">
						<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>"/>
						<input type="submit" name="search-wo-result" class="export-btn" value="<?php print _('Exporter'); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>

	</form>
	<div id="popup_ria" class="maxipopup"><div class="popup_ria_drag"></div><div class="content"></div></div>

	<script><!--
		<?php view_date_initialized( 0, '', false, array('autoload'=>true) ); ?>
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file prd-no-image.php
	 *	Cette page affiche la liste des produits sans image (principale ou secondaire).
	 */

	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_NO_IMG');

	// tab
	$tabs = array('tabMain', 'tabSub');
	if (isset($_GET['tab'])) $_GET[$_GET['tab']] = true;
	foreach ($tabs as $t) if (isset($_GET[$t])) { $tab = $t; break; }
	if (! isset($tab)) $tab = 'tabMain';

	$filters = array(
		'all'			=>	_('Tous les produits'),
		'published'		=>	_('Produits publiés'),
		'unpublished'	=>	_('Produits non publiés'),
		'orderable'		=>	_('Produits commandables')
	);
	$filter = isset($_GET['filter']) && isset($filters[$_GET['filter']]) ? $_GET['filter'] : 'published';

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Produits sans image liée') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Produits sans image liée').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Produits sans image'); ?></h2>
	<p class="notice">
		<?php print _('Astuce : Pour trier sur plusieurs colonnes, cliquez sur la première, puis sur la seconde tout en appuyant sur la'); ?> <em id="shiftKey" class="tooltip-handle"><?php print _('touche Majuscule'); ?></em>
		<span id="tooltip-shiftKey" class="tooltip">
			<span id="t-s-1">
				<img src="/admin/images/keyboard/key_shift.png" alt="<?php print _('Touche Majuscule'); ?>"/>
				<span id="t-s-2">+</span>
				<img src="/admin/images/keyboard/mighty_mouse.png" alt="<?php print _('Souris'); ?>"/>
			</span>
		</span>
	.</p>
	<form action="prd-no-image.php" method="get" id="form-no-image">
		<select id="filter-products" name="filter-products" class="float-left">
			<?php foreach ($filters as $key => $value) echo '<option value="' . htmlspecialchars($key) . '"' . ($filter === $key ? ' selected="selected"' : '') . '>' . htmlspecialchars($value) . '</option>'; ?>
		</select>
		<div class="clear-left"></div>
		<ul class="tabstrip">
			<li><input type="submit" name="tabMain" value="<?php print _('Principale'); ?>" <?php if( $tab=='tabMain' ) print 'class="selected"'; ?> /></li>
			<li><input type="submit" name="tabSub" value="<?php print _('Secondaire'); ?>" <?php if( $tab=='tabSub' ) print 'class="selected"'; ?> /></li>
		</ul>
		<div id="tabpanel">
			<table id="prd-no-image" class="tablesorter checklist">
				<caption><?php print _( sprintf('Produits sans image %s', ($tab === 'tabMain' ? 'principale' : 'secondaire'))); ?><span id="caption-count"></span></caption>
				<thead>
					<tr>
						<?php
							$cols = array(
								'ref' => array('name' => _('Référence'), 'sortable' => true),
								'name' => array('name' => _('Désignation'), 'sortable' => true),
								'hits' => array('name' => _('Impressions'), 'class' => 'align-right', 'sortable' => true),
								'sells' => array('name' => _('Ventes'), 'class' => 'align-right', 'sortable' => true),
								'taux' => array('name' => _('Taux de conversion'), 'class' => 'align-right', 'sortable' => true),
								'link' => array('name' => '', 'class' => 'sorter-false', 'sortable' => false),
							);
							foreach ($cols as $id => $col) {
								$t = '<th id="prd-' . $id . '" '.(isset($col['class']) ? 'class="'.$col['class'].'"' : '').'>';
								$t .= htmlspecialchars( $col['name'] );
								$t .= '</th>';
								echo $t;
							}
						?>
					</tr>
				</thead>
				<tbody><tr><td colspan="6"></td></tr></tbody>
				<tfoot>
					<tr id="pagination">
						<td colspan="3"></td>
						<td colspan="3"></td>
					</tr>
					<tr>
						<td colspan="6">
							<button type="button" class="btn-move btn-export" name="export" id="export"><?php print _('Exporter'); ?></button>
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
	</form>

	<script><!--
		$(document).ready(function() {
			// Ajout un parser pour gérer les nombres a virgules
			$.tablesorter.addParser({
			  id: 'commaDigit',
			  is: function(s, table) {
				var c = table.config;
				return $.tablesorter.isDigit(s.replace(/,| /g, ''), c);
			  },
			  format: function(s) {
				return $.tablesorter.formatFloat(s.replace(/,| /g, ''));
			  },
			  type: 'numeric'
			});

			// Tooltip
			$('.tooltip-handle').tooltip({
				delay: 0,
				showURL: false,
				track: true,
				bodyHandler: function() {
					return $('#tooltip-'+$(this).attr('id')).html();
				}
			});

			var onloadHandler = function() {
				// Popup
				$('.popup').each(function() {
					$(this).click(function() {
						var href = $(this).attr('href');
						var p = href.indexOf('?');
						displayPopup('Images', 'contenu', 'popup-image.php' + href.substring(p, href.length) + '&type=<?php echo $tab; ?>', undefined, 1000, 600);
						return false;
					});
				});

				// Tablesorter
				$('#prd-no-image')
					.tablesorter()
					.tablesorterPager({
						container: $( "#pagination" ),
						size: 100,
						output: '{page}/{totalPages}'
					})
				;
			};

			var query = function( page ){
				page = typeof page != 'undefined' && !isNaN(page) ? page : 1;

				var tbody = $('#prd-no-image').find('tbody');

				tbody.html('<tr><td colspan="6"><?php print _('Chargement des données en cours ...'); ?><img alt="" src="/admin/images/ajax.gif" /></td></tr>');

				// Chargement des lignes en ajax
				$.ajax({
					'url'		:	'ajax-prd-no-image.php?2',
					'data'		:	'tab=<?php echo $tab; ?>&page=' + page + '&filter=' + $('#filter-products').val(),
					'type'		:	'post',
					'dataType'	:	'json',
					'success'	:	function(response) {
										if (response.error) {
											var error;
											switch (response.error) {
												default	:	error = 'Cette fonctionnalité est temporairement indisponible.';
											}
											tbody.html('<tr><td colspan="6"><div class="div-padding-10">' + error + '</div></td></tr>');
											return;
										}
										$('#caption-count').html(response.count);
										tbody.html(response.content);
										$('#pagination').html(response.pager);
										onloadHandler();
										if (typeof compareSizeBlocks != 'undefined') {
											compareSizeBlocks();
										}
									}
				});
			};

			$(document).on('click', '#pagination a', function(){
				var page = parseInt( $(this).data('page') );
				if( isNaN(page) ){
					page = 1;
				}

				query( page );
				return false;
			});

			$('#filter-products')
				.change(query)
				.riaSelector({'title': 'Filtre sur les produits'})
			;

			// Export des données
			$('#export').click(function(){
				location.href = 'ajax-prd-no-image.php?2&tab=<?php echo $tab; ?>&filter=' + $('#filter-products').val() + '&export=1';
			});

			query();
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
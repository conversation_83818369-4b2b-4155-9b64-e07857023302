<?php

	/**	\file prd-conversion.php
	 * 	Cette page affiche un tableau d'analyse sur le taux de conversion des fiches produits. Pour que la boutique en ligne
	 * 	soit performante, il faut pouvoir identifier les pages qui ne convertissent pas pour les améliorer.
	 * 
	 * 	Nos installations pouvant être multi-sources (sites web, places de marché, gestion commerciale), il est à noter que
	 * 	seules les ventes site doivent être prises en compte.
	 * 
	 * 	Les filtres suivants sont disponibles :
	 * 	- Site web (uniquement si plusieurs)
	 *  - Période
	 */

	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_CONVERSION');

	// Tri par défaut : taux de conversion décroissant
	if( !isset($_GET['tri']) || !is_array($_GET['tri']) ){
		$_GET['tri'] = array('conversion' => 'desc');
	}

	// Website
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = false;
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
	}
	$wst_id = isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : false;

	// Dates
	if( isset($_GET['date1'], $_GET['date2'])  ){
		view_date_in_session($_GET['date1'], $_GET['date2']);
	}

	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';

	// Ajax
	$ajax = false;
	if (isset($_SERVER['HTTP_X_REQUESTED_WITH'])
		&& $_SERVER['HTTP_X_REQUESTED_WITH'] == "XMLHttpRequest") {
		$_GET['ajax'] = 'on';
		$ajax = true;
	}
	$results = false;

	if( $ajax || isset($_GET['export']) || isset($_POST['export']) ){
		$results = stats_view_products_conversion( $_GET['tri'], $date1, $date2, $wst_id );
	}

	$columns = array(
		'ref' =>		array('name' => _('Référence'),
									'type' => 'string',
									'width' => '50',
									'widthE' => '12'),
		'name' =>		array('name' => _('Désignation'),
									'type' => 'string',
									'width' => '320',
									'widthE' => '45'),
		'hits' =>			array('name' => _('Impressions'),
									'type' => 'int',
									'width' => '*',
									'widthE' => '12'),
		'sells' =>			array('name' => _('Ventes'),
									'type' => 'int',
									'width' => '*',
									'widthE' => '8'),
		'conversion' =>		array('name' => _('Taux de conversion (%)'),
									'type' => 'float',
									'width' => '130',
									'widthE' => '22'),
		'price_sell' =>		array('name' => _('Prix de vente'),
									'type' => 'float',
									'width' => '80',
									'widthE' => '13'),
		'price_purchase' =>	array('name' => _('Prix d\'achat'),
									'type' => 'float',
									'width' => '80',
									'widthE' => '12'),
		'margin' =>			array('name' => _('Marge brute'),
									'type' => 'float',
									'width' => '*',
									'widthE' => '7'),
		'margin_rate' =>	array('name' => _('Taux de marge (%)'),
									'type' => 'float',
									'width' => '100',
									'widthE' => '18')
	);

	// Export Excel
	if( isset($_POST['export']) || isset($_GET['export']) ){
		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export-prd-conversion.php');
		exit;
	}

	if(!$ajax) {

		// Fil d'ariane
		Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
			->push( _('Statistiques'), '/admin/stats/index.php' )
			->push( _('Performances des fiches produits') );

		// Défini le titre de la page
		define('ADMIN_PAGE_TITLE', _('Performances des fiches produits').' - '._('Statistiques'));
		require_once('admin/skin/header.inc.php');
?>
	<form id="form-export" action="prd-conversion.php" method="get">

		<h2>
			<?php print _('Performances des fiches produits'); ?>
			<input type="submit" class="btn-export float-right" name="export" value="<?php print _('Exporter'); ?>" title="<?php print _('Exporter le rapport au format Excel') ?>" /></p>
		</h2>

		<?php
			if( isset($_GET['tri']) && is_array($_GET['tri']) ){
				foreach( $_GET['tri'] as $col => $dir ){
					print '<input type="hidden" name="tri['.htmlspecialchars( $col ).']" value="'.htmlspecialchars( $dir ).'" />';
				}
			}
		?>

		<div class="stats-menu">
			<?php
				print view_websites_selector( $wst_id, true, '', true );
			?>

			<div id="riadatepicker"></div>

			<?php
				if(isset($_GET['date1'], $_GET['date2'], $_GET['last'])){
					print '	<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>
							<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>
							<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>';
				}
				if(isset($_GET['wst'])){
					print '<input type="hidden" name="wst" id="wst" value="'.$_GET['wst'].'"/>';
				}
			?>
			<div id="ajax-indicator" style="display: none"><img src="/admin/images/ajax.gif" alt="" class="fleche-move"/></div>
			<div class="clear"></div>
		</div>
	
	</form>
	<p class="notice hide-mobile">
		<?php print _('Astuce : Pour trier sur plusieurs colonnes, cliquez sur la première, puis sur la seconde tout en appuyant sur la'); ?> <em id="shiftKey" class="tooltip-handle"><?php print _('touche Majuscule'); ?></em>
		<span id="tooltip-shiftKey" class="tooltip">
			<span id="t-s-1">
				<img src="/admin/images/keyboard/key_shift.png" alt="<?php print _('Touche Majuscule'); ?>"/>
				<span id="t-s-2">+</span>
				<img src="/admin/images/keyboard/mighty_mouse.png" alt="<?php print _('Souris'); ?>"/>
			</span>
		</span>
	.</p>
	<div id="site-content-main">
	<?php } ?>

		<table id="prd-conversion" class="checklist prd-weight tablesorter">
			<thead class="thead-none">
				<tr>
					<?php foreach($columns as $key => $val) { ?>
					<th id="w-<?php print $key ?>" class="header sortified <?php print (isset($_GET['tri'][$key]) && $_GET['tri'][$key] == 'asc' ? ' headerSortUp' : (isset($_GET['tri'][$key]) && $_GET['tri'][$key] == 'desc' ? ' headerSortDown' : '')) ?>"><a href="?<?php print 'tri['.$key.']='.(isset($_GET['tri'][$key]) && $_GET['tri'][$key] == 'asc' ? 'desc' : 'asc') ?>"><?php print $val['name'] ?></a></th>
					<?php }
					?>
				</tr>
			</thead>
			<tbody><?php
				if($results && ria_mysql_num_rows($results)) {
					while($res = ria_mysql_fetch_array($results)) {
						$cats = prd_products_categories_get_array( $res['id'] );
						$url = '#';
						if( is_array($cats) && sizeof($cats) ) {
							$url = '/admin/catalog/product.php?cat='.$cats[0].'&amp;prd='.$res['id'];
						}

						print '
							<tr>
						';

						foreach($columns as $key => $val) {
							print '
								<td headers="w-'.$key.'" '.( $val['type'] == 'float' || $val['type'] == 'int' ? ' class="col-numeric"' : '' ).' data-label="'.$val['name'].' : ">
									'.($key == 'ref' && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ? '<a href="'.$url.'" target="_blank">': '').($val['type'] == 'float' ?  ria_number_format($res[$key], NumberFormatter::DECIMAL, 2) : htmlspecialchars($res[$key])).($key == 'ref' ? '</a>' : '').'
								</td>
							';
						}

						print '
							</tr>
						';

						unset($url);
					}
				} else {
					print '
						<tr>
							<td colspan="'.count($columns).'">
								'._('Chargement en cours des données').' <img src="/admin/images/ajax.gif" alt="" class="fleche-move"/>
							</td>
						</tr>
					';
				}
			?></tbody>
			<?php if( false && $results && ria_mysql_num_rows($results)) { ?>
			<tfoot>
				<tr id="pagination">
					<td class="align-left"><?php print _('Page'); ?> <input type="text" class="pagedisplay"></td>
					<td colspan="8">
						<a class="first" href="#">«« </a>
						<a class="prev" href="#">« <?php print _('Page précédente'); ?></a>
						<a class="next" href="#"><?php print _('Page suivante'); ?> »</a>
						<a class="last" href="#"> »»</a>
						<select class="pagesize none">
							<option value="100" selected="selected">100</option>
							<option value="200">200</option>
						</select>
					</td>
				</tr>
			</tfoot>
			<?php } ?>
		</table>

	<?php if(!$ajax) { ?>
		</div>

		<script><!-- 
		<?php
			view_date_initialized( 0, '/admin/stats/prd-conversion.php', false,
				array(
					'autoload'=>true,
					'autorefresh' => true,
					'callback' => "function(){
						if(autorefresh) {
							var href = calcHref()
							var path = href;
							history.pushState({ path: path }, '', href);
							getAjaxData(href);
						}
				}"));
		?> --></script>
<?php

require_once('admin/skin/footer.inc.php'); }
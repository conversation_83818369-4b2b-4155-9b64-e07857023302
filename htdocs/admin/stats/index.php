<?php

	/**	\file index.php
	 *
	 * 	Ce fichier sert de page d'accueil à la section Statistiques du back-office RiaShop. Il serait bon de le remplacer par un tableau de bord.
	 *
	 */

	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_MDL_STATS');

	// Par défaut, on arrive sur le jour en cours
	if( !isset($_GET['day']) && !isset($_GET['week']) && !isset($_GET['month']) && !isset($_GET['year']) ){
		$_GET['day'] = date('Y-m-d');
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Statistiques'); ?></h2>
	<p><?php print _('Les statistiques permettent de connaître l\'activité de votre boutique en ligne.'); ?></p>

<?php
	if( !tnt_tenants_is_yuto_essentiel() ){
		print view_index_menu('_MDL_STATS');
	}else{ ?>
		<dl>
			<dt><a href="/admin/stats/customers/index.php"><?php print _('Clients')?></a></dt>
			<dd><?php print _('Cette section contient des rapports de statistiques sur les clients.')?></dd>
		</dl>
		<dl>
			<dt><a href="/admin/fdv/stats/subscriptions.php"><?php print _('Utilisation')?></a></dt>
			<dd><?php print _('Cet écran permet de suivre l\'utilisation des tablettes Yuto.')?></dd>
		</dl>
		<dl>
			<dt><a href="/admin/fdv/stats/report-time.php"><?php print _('Rapports de visite')?></a></dt>
			<dd><?php print _('Cet écran permet de consulter les statistiques sur les rapports de visite.')?></dd>
		</dl>
		<dl>
			<dt><a href="/admin/fdv/stats/spent-time.php"><?php print _('Temps de visite')?></a></dt>
			<dd><?php print _('Cet écran permet de consulter les temps de visite.')?></dd>
		</dl>
	<?php }

	require_once('admin/skin/footer.inc.php');
?>

<?php


/**	\file price-watching.php
 *
 * 	Cette page affiche les données issues de notre système de veille tarifaire (Amazon uniquement pour l'instant).
 *
 */

require_once('admin/get-filters.php');
require_once('stats.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRICE_WATCHING');

if (isset($_GET['downloadexport'])) {
	// Contrôle que le fichier est bien disponible
	if (file_exists($config['doc_dir'].'/export-price-watching-'.$_SESSION['usr_id'].'.csv')) {
		header('Content-disposition: attachment; filename="export-price-watching.csv"');
		header('Content-type: application/octetstream; charset=utf-8');
		header('Pragma: no-cache');
		header('Expires: 0');

		echo "\xEF\xBB\xBF"; // BOM UTF-8
		readfile ($config['doc_dir'].'/export-price-watching-'.$_SESSION['usr_id'].'.csv');
		exit;
	}else{
		$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
	}
}


require_once('PriceWatching/models/prw_followed_products.inc.php');
require_once('PriceWatching/models/prw_offers.inc.php');
require_once('PriceWatching/models/prw_competitors.inc.php');

// initialisation de la variable $colspan
$colspan = 5;

// chargement des models
$prw_competitors = new prw_competitors();
$competitors = $prw_competitors->prw_competitors_getActive();
if( !$competitors ){
	$competitors = array();
}
$prw_followed_products = new prw_followed_products();

$is_selection = false;
if( isset($_GET['sel'])){
	$is_selection = true;
}

$ar_followed = array();
$cpt_offers = array();

$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':admin:price-watching:get-all';
if ($get = $memcached->get($key_memcached)) {
	$ar_followed = $get != 'none' ? $get : array();
}else{
	$followed = $prw_followed_products->prw_followed_products_getAll_enable_group_by_id($is_selection);

	if (is_array($followed) && count($followed)) {
		foreach ($followed as $value) {
			$ar_followed[ $value['prd_id'] ] = $value;

			foreach($competitors as $cpt){
				$ar_followed[ $value['prd_id'] ][ 'prc-price-'.$cpt['id'] ] = 0;
				$ar_followed[ $value['prd_id'] ][ 'prc-promo-'.$cpt['id'] ] = 0;
				$ar_followed[ $value['prd_id'] ][ 'prc-ecart-'.$cpt['id'] ] = null;
			}
		}

		$prw_offers = new prw_offers();
		$last_offers = $prw_offers->get_offer_getLast( 0, PRW_CLIENT );
		if ($last_offers) {
			foreach ($last_offers as $prd_id=>$value) {
				if (!array_key_exists($prd_id, $ar_followed)) {
					continue;
				}

				$ar_followed[ $prd_id ][ 'prc-price-'.PRW_CLIENT ] = $value['ListingPrice'];
				$ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ] = is_numeric($value['promo_price']) ? $value['promo_price'] : 0;
			}
		}

		foreach($competitors as $cpt ){
			if ($cpt['id'] == PRW_CLIENT) {
				continue;
			}
			$last_offers_cpt = $prw_offers->get_offer_getLast( 0, $cpt['id'] );

			if (is_array($last_offers_cpt)) {
				foreach ($last_offers_cpt as $prd_id=>$value) {
					if (!array_key_exists($prd_id, $ar_followed)) {
						continue;
					}

					$prc_cli = $ar_followed[ $prd_id ][ 'prc-price-'.PRW_CLIENT ];
					if (is_numeric($ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ]) && $ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ] > 0) {
						$prc_cli = $ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ];
					}

					$ar_followed[ $prd_id ][ 'url-'.$cpt['id'] ]        = $value['url'];
					$ar_followed[ $prd_id ][ 'prc-price-'.$cpt['id'] ]  = $value['ListingPrice'];
					$ar_followed[ $prd_id ][ 'prc-ecart-'.$cpt['id'] ]  = $prc_cli - $value['ListingPrice'];
				}
			}
		}

	}

	$memcached->set( $key_memcached, (count($ar_followed) ? $ar_followed : 'none'), 60 * 60 );
}

if (!isset($_GET['mask-unavailable']) || !isset($_GET['mask-unupdated'])) {
	foreach ($ar_followed as $key => $value) {
		$can_del = false;

		if (!isset($_GET['mask-unavailable'])) {
			$can_del = true;

			foreach($competitors as $cpt ){
				if ($cpt['id'] == PRW_CLIENT) {
					continue;
				}

				if ($ar_followed[ $key ][ 'prc-price-'.$cpt['id'] ] != 0) {
					$can_del = false;
				}
			}
		}

		if (!isset($_GET['mask-unupdated'])) {
			if (trim($ar_followed[ $key ]['last_check']) == '') {
				$can_del = true;
			}
		}elseif (!isset($_GET['mask-unavailable']) && trim($ar_followed[ $key ]['last_check']) == '') {
			$can_del = false;
		}

		if ($can_del) {
			unset($ar_followed[ $key ]);
		}
	}
}

$page  = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? $_GET['page'] : 1;
$count = is_array($ar_followed) ? count($ar_followed) : 0;
$pages = $count ? ceil( $count / 30 ) : 1;

if ($page > $pages) {
	$page = $pages;
}


/*
	$ar_followed = array(
		[117109] => Array(
			[tnt_id] => 16
			[cpt_id] => 1
			[prd_id] => 117109
			[ref] => 70200116
			[name] => ADDITIF REEFKALK 500G
			[is_sync] => 1
			[disable] => 0
			[last_check] => 2017-03-24 05:24:39
			[prc-price-1] => 21.30
			[prc-promo-1] => 0
			[prc-ecart-1] => 0
			[prc-price-2] => 22.60
			[prc-promo-2] => 0
			[prc-ecart-2] => 0
		)
	),
	....
*/

$ar_sort  = array('', '');
$dir_sort = '';
$new_dir = 'asc';

if (isset($_GET['sort']) && strstr($_GET['sort'], '|')) {
	$ar_sort = preg_split( '/\|/i', $_GET['sort'] );

	if (count($ar_sort) != 2) {
		$ar_sort = array('', '');
	}

	if ($ar_sort[1] == 'asc') {
		$new_dir = 'desc';
	}
}

$url = '/admin/stats/price-watching.php';

$dir_class = $ar_sort[1] != 'asc' ? 'headerSortDown' : 'headerSortUp';

// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Statistiques'), '/admin/stats/index.php' )
	->push( _('Veille tarifaire') );

// Défini  le titre de la page
define('ADMIN_PAGE_TITLE', _('Produits').' - '._('Veille tarifaire'));
require_once('admin/skin/header.inc.php');

if ($ar_sort[0] != '') {
	$ar_followed = array_msort( $ar_followed, array($ar_sort[0]=>($ar_sort[1]=='asc' ? SORT_ASC : SORT_DESC)) );
}
?>
<h2><?php print _('Veille tarifaire'); ?></h2>
<div class="pricewatching">
	<?php if (empty($competitors)) {?>
		<div class="notice"><?php echo _("Le système de veille tarifaire n'a pas encore été configuré. Si vous souhaitez l'utiliser, merci de vous rapprocher de nous."); ?></div>
	<?php } ?>
	<form action="/admin/stats/price-watching.php" method="get">
		<p>
			<?php print _('Vous trouverez ci-dessous tous les produits que vous surveillez :'); ?>
			<a href="#" class="btn-export button" id="export"><?php print _('Exporter'); ?></a>
		</p>
		<div>
			<label for="mask-unavailable" class="checkboxes">
				<input class="input" type="checkbox" id="mask-unavailable" name="mask-unavailable" title="<?php print _('Masquer ou afficher les produits indisponibles'); ?>" value="1" <?php print isset($_GET['mask-unavailable']) ? 'checked="checked"' : ''; ?>/>
				<span class="span"><?php print _('Afficher les produits dont le tarif est indisponible'); ?></span>
			</label>
		</div>
		<div>
			<label for="mask-unupdated" class="checkboxes">
				<input class="input" type="checkbox" id="mask-unupdated" name="mask-unupdated" title="<?php print _('Masquer ou afficher les produits en cours d\'actualisation'); ?>" value="1" <?php print isset($_GET['mask-unupdated']) ? 'checked="checked"' : ''; ?>/>
				<span class="span"><?php print _('Afficher les produits dont le tarif est en cours d’actualisation'); ?></span>
			</label>
		</div>
	</form>
	<table id="stats-price-watching" class="table-cols-changed checklist large price-watching tablesorter">
		<caption><?php print _('Liste des produits surveillés'); ?> (<?php print ria_number_format($count); ?>)</caption>
		<thead>
		<tr>
			<th class="header <?php print $ar_sort[0] == 'ref' ? $dir_class : ''; print ( !isset($ar_sort[0]) || $ar_sort[0]=='' ? 'headerSortDown' : '' ) ?>">
				<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?'); ?>sort=ref|<?php print ( $ar_sort[0] == 'ref' ? $new_dir : 'asc' ).( isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '' ).( isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : ''); ?>"><?php print _('Référence'); ?></a>
			</th>
			<th class="header <?php print $ar_sort[0] == 'name' ? $dir_class : ''; ?>">
			   <a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?'); ?>sort=name|<?php print ( $ar_sort[0] == 'name' ? $new_dir : 'asc' ).( isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '' ).( isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : '' ); ?>"><?php print _('Désignation'); ?></a>
			</th>
			<th class="align-right header <?php print $ar_sort[0] == 'last_check' ? $dir_class : ''; ?>">
				<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?'); ?>sort=last_check|<?php print ( $ar_sort[0] == 'last_check' ? $new_dir : 'asc' ).( isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '' ).( isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : '' ); ?>"><?php print _('Dernière actualisation'); ?></a>
			</th>
			<th class="align-right header <?php print $ar_sort[0] == 'prc-promo-'.PRW_CLIENT ? $dir_class : ''; ?>">
				<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?').'sort=prc-promo-'.PRW_CLIENT.'|'.( $ar_sort[0] == 'prc-promo-'.PRW_CLIENT ?  : 'asc' ).( isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '' ).( isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : '' ); ?>"><?php print _('Votre prix promo TTC'); ?></a>
			</th>
			<th class="align-right header <?php print $ar_sort[0] == 'prc-price-'.PRW_CLIENT ? $dir_class : ''; ?>">
				<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?').'sort=prc-price-'.PRW_CLIENT.'|'.( $ar_sort[0] == 'prc-price-'.PRW_CLIENT ? $new_dir : 'asc' ).(isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '').(isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : ''); ?>"><?php print _('Votre prix TTC'); ?></a>
			</th>
			<?php
				foreach ($competitors as $cpt){
					if ($cpt['id'] == PRW_CLIENT) {
						continue;
					}
					$colspan+=2;
			?>
				<th class="align-right" colspan="2"><?php print $cpt['name'] ?></th>
			<?php
				}
			?>
		</tr>
		<?php
			$need_subrowhead = false;
			foreach ($competitors as $cpt){
				if ($cpt['id'] == PRW_CLIENT) {
					continue;
				}
				$need_subrowhead = true;
			}
		?>
		<?php if( $need_subrowhead ) { ?>
			<tr class="th-head-second">
				<th colspan="5"></th>
				<?php
					foreach ($competitors as $cpt){
						if ($cpt['id'] == PRW_CLIENT) {
							continue;
						}
				?>
					<th class="align-right header <?php print $ar_sort[0] == 'prc-price-'.$cpt['id'] ? $dir_class : ''; ?>">
						<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?').'sort=prc-price-'.$cpt['id'].'|'.($ar_sort[0] == 'prc-price-'.$cpt['id'] ? $new_dir : 'asc').(isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '').(isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : ''); ?>"><?php print _('Prix TTC'); ?></a>
					</th>
					<th class="align-right header <?php print $ar_sort[0] == 'prc-ecart-'.$cpt['id'] ? $dir_class : ''; ?>">
						<a href="<?php print $url.( strstr($url, '?') ? '&amp;' : '?').'sort=prc-ecart-'.$cpt['id'].'|'.($ar_sort[0] == 'prc-ecart-'.$cpt['id'] ? $new_dir : 'asc').(isset($_GET['mask-unupdated']) ? '&amp;mask-unupdated=1' : '').(isset($_GET['mask-unavailable']) ? '&amp;mask-unavailable=1' : ''); ?>"><?php print _('Écart'); ?></a>
					</th>
				<?php
					}
				?>
			</tr>
		<?php } ?>
		</thead>
		<tbody>
			<?php if (is_array($ar_followed) && count($ar_followed)) {
					$ar_followed = array_slice($ar_followed, ($page-1) * 30, 30 );
					foreach ($ar_followed as $fld) { ?>
						<tr>
							<td><?php print view_prd_is_sync($fld).' '.htmlspecialchars( $fld['ref'] ); ?></td>
							<td data-label="<?php print _('Désignation :'); ?> ">
								<a href="/admin/catalog/product.php?cat=0&amp;prd=<?php print $fld['prd_id']; ?>&amp;lng=fr&amp;tab=pricewatching"><?php print htmlspecialchars( $fld['name'] ); ?></a>
							</td>
							<td class="number" data-label="<?php print _('Dernière actualisation :'); ?> "><?php print ria_date_format($fld['last_check']) ?></td>
							<td class="numeric" data-label="<?php print _('Votre prix promo TTC :'); ?> "><?php print ( isset($fld['prc-promo-'.PRW_CLIENT] ) ? ria_number_format($fld['prc-promo-'.PRW_CLIENT], NumberFormatter::CURRENCY, 2) : '' ); ?></td>
							<td class="numeric" data-label="<?php print _('Votre prix TTC :'); ?> "><?php print ( isset($fld['prc-price-'.PRW_CLIENT] ) ? ria_number_format($fld['prc-price-'.PRW_CLIENT], NumberFormatter::CURRENCY, 2) : '' ); ?></td>

							<?php
								foreach ($competitors as $cpt){
									if ($cpt['id'] == PRW_CLIENT) {
										continue;
									}
									?>
									<td class="numeric" data-label="<?php print $cpt['name']._(' prix TTC :'); ?> ">
										<?php if (is_numeric($fld['prc-price-'.$cpt['id']]) && $fld['prc-price-'.$cpt['id']] > 0) { ?>
											<a href="<?php print $fld['url-'.$cpt['id']] ?>" target="_blank"><?php print str_replace('.',',',$fld['prc-price-'.$cpt['id']]) ?> €</a>
										<?php
											} elseif (empty($fld['last_check'])) {
												print _('En cours d\'actualisation');
											} else {
												print _('Non disponible');
											}
										?>
									</td>
									<?php
										if (!is_null($fld['prc-ecart-'.$cpt['id']])) {
											if ($fld['prc-ecart-'.$cpt['id']] > 0) {
												echo '<td class="numeric negative align-right"  data-label="'.$cpt['name']._(' écart :').' ">+' . number_format( $fld['prc-ecart-'.$cpt['id']], 2, ',', ' ' ) . ' €</td>';
											}elseif ($fld['prc-ecart-'.$cpt['id']] < 0) {
												echo '<td class="numeric positive align-right"  data-label="'.$cpt['name']._(' écart :').' ">-' . number_format( abs($fld['prc-ecart-'.$cpt['id']]), 2, ',', ' ' ) . ' €</td>';
											}else{
												echo '<td class="numeric align-right"  data-label="'.$cpt['name']._(' écart :').' "> 0,00 €</td>';
											}
										}elseif (empty($fld['last_check'])) {
											echo '<td class="waiting align-right" data-name="unupdated"  data-label="'.$cpt['name']._(' écart :').' ">'._('En cours d\'actualisation').'</td>';
										}else{
											echo '<td class="align-right" data-name="unavailabled"  data-label="'.$cpt['name']._(' écart :').' ">'._('Non disponible').'</td>';
										}

								} ?>
						</tr>
				<?php }
				} else { ?>
					<tr>
						<td colspan="<?php print $colspan ?>"><?php print _('Aucun produit surveillé'); ?></td>
					</tr>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td class="align-left" colspan="2"><?php print 'Page '.$page.' / '.$pages; ?></td>
				<td colspan="<?php print $colspan-2 ?>" class="align-right"><?php
					if( $count>0 ){
						if (isset($_GET['sort'])) {
							$url = $url.( strstr($url, '?') ? '&amp;' : '?').'sort='.$_GET['sort'];
						}

						$url = $url.( strstr($url, '?') ? '&amp;' : '?').'page=';

						$links = array();
						if( $page>1 )
							$links[] = '<a href="'.$url.($page-1).'">&laquo; '._('Page précédente').'</a>';
						for( $i=$page-5; $i<$page+5; $i++ )
							if( $i>=1 && $i<=$pages ){
								if( $i==$page )
									$links[] = '<b>'.$i.'</b>';
								else
									$links[] = '<a href="'.$url.$i.'">'.$i.'</a>';
							}
						if( $page<$pages )
							$links[] = '<a href="'.$url.($page+1).'">'._('Page suivante').' &raquo;</a>';

						print implode(' | ',$links);
					}
				?></td>
			</tr>
		</tfoot>
	</table>
</div>

<?php
require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file bestsellers-cat.php
	 *
	 * 	Le fichier affiche et permet la navigation dans les statistiques de vente par catégorie de produits.
	 * 	Elle dispose d'une fonction d'export de données réalisée par le fichier export-bestsellers-cat.php.
	 *
	 */

	require_once ('stats.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_BESTSELLER_CAT');

	// Par défaut, on arrive sur le jour en cours
	if (isset($_GET['date1'], $_GET['date2']) && isdate($_GET['date1']) && isdate($_GET['date2'])) {
		view_date_in_session($_GET['date1'], $_GET['date2']);
	}

	// Filtre sur la période
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse($_SESSION['datepicker_date1']) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse($_SESSION['datepicker_date2']) : date('Y-m-d');
	$date2 = strtotime($date2) < strtotime($date1) ? $date1 : $date2;

	// Filtre sur l'origine de la commande
	$_SESSION['origin'] = isset($_GET['origin']) ? $_GET['origin'] : (IS_AJAX ? false : isset($_SESSION['origin']) ? $_SESSION['origin'] : false);

	/* Filtre sur les utilisateurs.
	 *
	 * Valeur de "stats_users":
	 * 		- $_GET['users'] si défini et à une valeur
	 * 		- Garde la même valeur si défini
	 * 		- Un tableau vide si "stats_users" n'est pas défini
	 */
	if (isset($_GET['users'])) {
		$_SESSION['stats_users'] = array();

		foreach($_GET['users'] as $user) {
			list($id, $active) = explode('-', $user);

			$_SESSION['stats_users'][] = compact('id', 'active');
		}
	}

	if (!isset($_SESSION['stats_users'])) {
		$_SESSION['stats_users'] = array();
	}

	$users = $_SESSION['stats_users'];

	// Filtre sur le site d'origine
	$wst_id = 0;

	if(isset($_GET['wst'])) {
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	if(isset($_SESSION['websitepicker'])) {
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	$parent = isset($_GET['parent']) && is_numeric($_GET['parent']) && $_GET['parent']>0 ? $_GET['parent'] : 0;
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

	// Gère le filtre par représentant
	if( isset($_GET['seller']) ){
		$_GET['seller'] = str_replace( 'seller-', '', $_GET['seller'] );
		if( is_numeric($_GET['seller']) ){
			$_SESSION['ord_seller_id'] = $_GET['seller'];
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Commandes'), '/admin/stats/orders.php' )
		->push( _('Ventes par catégorie') );

	$seller_id = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0; ?>

	<?php if (!IS_AJAX) : ?>


		<?php define('ADMIN_PAGE_TITLE', _('Ventes par catégorie') . ' - ' . _('Commandes') . ' - ' . _('Statistiques')); ?>
		<?php require_once('admin/skin/header.inc.php'); ?>

		<h2><?php print _('Ventes par catégorie'); ?></h2>
		<div class="stats-menu">
			<div id="riadatepicker">
				<?php print view_websites_selector($wst_id, true, 'riapicker', true); ?>
			</div>
			<?php if (tnt_tenants_have_websites()) : ?>
				<?php print view_origins_selector(); ?>
			<?php endif; ?>
			<?php print view_sellers_selector(); ?>
			<div id="select-users" class="riapicker" data-defaultVal="user-0" data-selectorName="user_ids">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _('Utilisateurs'); ?></span>
						<br>
						<span class="view"><?php print _('Tous les utilisateurs'); ?></span>
					</div>
					<a name="btn" class="btn">
						<img src="/admin/images/stats/fleche.gif" alt="" height="8" width="16">
					</a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="user-0"><?php print _('Tous les utilisateurs'); ?></a>
					<a class="selector-sep"></a>
					<?php if ($users) : ?>
						<?php foreach ($users as $user) : ?>
							<?php $u = ria_mysql_fetch_array(gu_users_get($user['id'])); ?>
							<a class="parent" name="user-<?php print $user['id']; ?>">
								<input type="checkbox" id="user-<?php print $user['id']; ?>" name="users[]" value="<?php print $user['id']; ?>" <?php print $user['active'] ? 'checked' : ''; ?>>
								<label for="user-<?php print $user['id']; ?>">
									<?php print implode(', ', array_filter(array(
										trim(htmlspecialchars($u['society'])),
										trim(htmlspecialchars($u['adr_lastname'])),
										trim(htmlspecialchars($u['adr_firstname']))
									))); ?>
								</label>
							</a>
						<?php endforeach; ?>
					<?php endif; ?>
					<?php if (!empty($users)) : ?>
						<a id="selector-sep-users" class="selector-sep"></a>
					<?php endif; ?>
					<a id="user-search"><?php print _('Sélectionnez un nouveau client'); ?></a>
				</div>
			</div>
			<div class="clear"></div>
		</div>
		<p>
			<?php print _('Vous trouverez ci-dessous les catégories identifiées comme étant les meilleures ventes selon les produits qu\'elles contiennent :'); ?>
			<button  onclick="window.location.href='/admin/stats/export-bestsellers-cat.php?parent=<?php print $parent; ?>'" name="export" class="btn-export">
				<?php print _('Exporter'); ?>
			</button>
		</p>
		<input type="hidden" id="parent_id" name="parent_id" value="<?php print $parent; ?>" />
		<input type="hidden" id="ord_seller_id" name="ord_seller_id" value="<?php print $seller_id; ?>" />
		<div class="stats-content">
	<?php endif; ?>

	<?php
	$tbody = '';

	$total = array(
		'orders' => 0,
		'by-ord' => 0,
		'margin' => 0,
		'ca' => 0,
		'ca-ord' => 0
	);

	$active_users_ids = array();
	$active_users = array_filter($users, function ($user) {
		return $user['active'];
	});

	foreach( $active_users as $user ){
		$active_users_ids[] = $user['id'];
	}

	$rtotal = stats_categories_get_orders($parent, $date1, $date2, $origin, $gescom, $wst_id, $seller_id, '', 'completed', $active_users_ids);
	if( $rtotal && ria_mysql_num_rows($rtotal) ){
		$r = ria_mysql_fetch_array($rtotal);
		$total['orders'] += $r['orders'];
		$total['margin'] += $r['margin'];
		$total['by-ord'] += $r['orders'] > 0 ? $r['qte'] / $r['orders'] : 0;
		$total['ca'] += $r['total_ht'];
		$total['ca-ord'] += $r['ord_total_ht'];
	}

	// La notice indiquant que des différences peuvent apparaître entre lignes et total n'est pertinente que s'il y a plusieurs lignes.
	// Elle n'est donc affichée que dans ce cas de figure.
	$show_notice = false;

	$rcat = prd_categories_get(0, false, $parent);

	if ($rcat && ria_mysql_num_rows($rcat)) {
		while ($cat = ria_mysql_fetch_array($rcat)) {
			$stat = array(
				'qte' => 0,
				'margin' => 0,
				'total_ht' => 0,
				'orders' => 0,
				'ord_total_ht' => 0
			);

			$rstat = stats_categories_get_orders($cat['id'], $date1, $date2, $origin, $gescom, $wst_id, $seller_id, '', 'completed', $active_users_ids);

			if ($rstat && ria_mysql_num_rows($rstat)) {
				$stat = ria_mysql_fetch_array($rstat);
			}

			$tbody.= '
					<tr>
						<td headers="best-name">
							<a href="/admin/stats/bestsellers-cat.php?parent=' . $cat['id'] . '">' . view_cat_is_sync($cat) . ' ' . htmlspecialchars($cat['name']) . '</a>
						</td>
						<td headers="best-orders" class="right">' . ria_number_format($stat['orders']) . '</td>
						<td headers="best-by-ord" class="right">' . ria_number_format($stat['orders'] > 0 ? $stat['qte'] / $stat['orders'] : 0) . '</td>
						<td headers="best-gross-margin" class="right">' . ria_number_format($stat['margin'], NumberFormatter::CURRENCY, 2) . '</td>
						<td headers="best-ca" class="right">' . ria_number_format($stat['total_ht'], NumberFormatter::CURRENCY, 2) . '</td>
						<td headers="best-ca-ord" class="right">' . ria_number_format($stat['ord_total_ht'], NumberFormatter::CURRENCY, 2) . '</td>
					</tr>
				';
		}

		$show_notice = ria_mysql_num_rows($rcat) > 1;
	} else {
		$cat = ria_mysql_fetch_array( prd_categories_get($parent) );

		$stat = array(
			'qte' => 0,
			'margin' => 0,
			'total_ht' => 0,
			'orders' => 0,
			'ord_total_ht' => 0
		);

		$rstat = stats_categories_get_orders($cat['id'], $date1, $date2, $origin, $gescom, $wst_id, $seller_id, '', 'completed', $active_users_ids);

		if ($rstat && ria_mysql_num_rows($rstat)){
			$stat = ria_mysql_fetch_array($rstat);
		}

		$tbody .= '
			<tr">
				<td headers="best-name">
					'.view_cat_is_sync($cat).' '.htmlspecialchars( $cat['name'] ).'
				</td>
				<td headers="best-orders" class="right">'.ria_number_format($stat['orders']).'</td>
				<td headers="best-by-ord" class="right">'.ria_number_format($stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0).'</td>
				<td headers="best-gross-margin" class="right">'.ria_number_format($stat['margin'], NumberFormatter::CURRENCY, 2).'</td>
				<td headers="best-ca" class="right">'.ria_number_format($stat['total_ht'], NumberFormatter::CURRENCY, 2).'</td>
				<td headers="best-ca-ord" class="right">'.ria_number_format($stat['ord_total_ht'], NumberFormatter::CURRENCY, 2).'</td>
			</tr>
		';
	}

	$title_cat = '';
	if( $parent<=0 ){
		$title_cat .= _('Catalogue');
	}else{
		$name = '<a href="?parent=0">'._('Catalogue').'</a> &raquo; ';
		$last = false;

		$parents = prd_categories_parents_get($parent);
		$cat = ria_mysql_fetch_array(prd_categories_get($parent));
		while( $p = ria_mysql_fetch_array($parents) ){
			$name .= '<a href="?parent='.$p['id'].'">'.htmlspecialchars($p['title']).'</a> &raquo; ';
			$last = $p;
		}

		$title_cat .= '<a href="?parent='.$last['id'].'">';
		$title_cat .= '<img style="border: medium none;" src="/admin/images/up.png" width="16" height="16" alt="'._('Remonter d\'un niveau').'" title="'._('Remonter d\'un niveau').'" />';
		$title_cat .= '</a> '.$name.htmlspecialchars($cat['title']);
	}

	print '
		<table id="bestseller-bycat" class="checklist tablesorter">
			<caption>'.$title_cat.'</caption>
			<thead>
				<tr>
					<th id="best-name">'._('Désignation').'</th>
					<th id="best-orders" class="align-right col75px">'._('Ventes').'</th>
					<th id="best-by-ord" class="align-right col130px"><abbr title="'._('Nombre de produits par commande').'">'._('Par commandes').'</abbr></th>
					<th id="best-gross-margin" class="align-right col130px"><abbr title="'._('Marge brute dégagée par cette catégorie').'">'._('Marge brute').'</abbr></th>
					<th id="best-ca" class="align-right col120px"><abbr title="'._('Chiffre d\'affaires pour cette catégorie"').'>'._('CA').'</abbr></th>
					<th id="best-ca-ord" class="align-right col120px"><abbr title="'._('Chiffre d\'affaires des commandes contenant au moins des produits de cette catégorie').'">'._('CA Total').'</abbr></th>
				</tr>
			</thead>
			<tbody>'.$tbody.'</tbody>
			<tfoot>
				<tr class="right bold">
					<td headers="best-name">'._('Totaux').' * :</td>
					<td headers="best-orders" class="right">'.ria_number_format($total['orders']).'</td>
					<td headers="best-by-ord" class="right">'.ria_number_format($total['by-ord']).'</td>
					<td headers="best-gross-margin" class="right">'.ria_number_format($total['margin'], NumberFormatter::CURRENCY, 2).'</td>
					<td headers="best-ca" class="right">'.ria_number_format($total['ca'], NumberFormatter::CURRENCY, 2).'</td>
					<td headers="best-ca-ord" class="right">'.ria_number_format($total['ca-ord'], NumberFormatter::CURRENCY, 2).'</td>
				</tr>
			</tfoot>
		</table>
	';

	if( $show_notice ){
		print '
			<span class="notice">* '._('Des différences peuvent apparaître entre le total et la somme de chaque ligne. Ces différences sont provoquées par les produits classés dans plusieurs catégories').'</span>
		';
	}

	if( $parent>0 && gu_user_is_authorized('_RGH_ADMIN_STATS_BESTSELLER_PRD') ){
		$rprd = prd_products_get_simple( 0, '', false, $parent, false, false, false, false);
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$tbody = '';
			$total = array( 'orders'=>0, 'by-ord'=>0, 'ca'=>0, 'ca-ord'=>$total['ca-ord'] );

			while( $prd = ria_mysql_fetch_array($rprd) ){
				if( prd_products_is_parent($prd['id']) ){
					$rChilds =  prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs'=>true, 'parent'=>$prd['id']));
					if( !$rChilds ){
						continue;
					}
					while( $child = ria_mysql_fetch_assoc($rChilds) ){

						$link = '/admin/catalog/product.php?cat=0&amp;prd='.$child['id'];

		 				$stat = array( 'qte'=>0, 'margin'=>0, 'total_ht'=>0, 'orders'=>0, 'ord_total_ht'=>0 );
						$rstat = stats_bestsellers_get( $date1, $date2, 1, $origin, $gescom, $wst_id, false, $child['id'], 0, false, 0, 0, $seller_id, $active_users_ids );
						if( $rstat && ria_mysql_num_rows($rstat) ){
							$stat = ria_mysql_fetch_array( $rstat );
						}

						$tbody .= '
								<tr>';
						if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
						$tbody .= ' <td headers="best-ref"><a href="'.$link.'">'.view_prd_is_sync($child).' '.htmlspecialchars( $child['ref'] ).'</a></td>
									<td headers="best-name"><a href="'.$link.'">'.htmlspecialchars( $child['title'] ).'</a></td>';
						}else{
						$tbody .= ' <td headers="best-ref">'.view_prd_is_sync($child).' '.htmlspecialchars( $child['ref'] ).'</td>
									<td headers="best-name">'.htmlspecialchars( $child['title'] ).'</td>';
						}
						$tbody .= ' <td headers="best-orders" class="right">'.ria_number_format($stat['orders']).'</td>
									<td headers="best-by-ord" class="right">'.ria_number_format($stat['orders'] > 0 ? ($stat['qte'] / $stat['orders']) : 0).'</td>
									<td headers="best-ca" class="right">'.ria_number_format($stat['total_ht'], NumberFormatter::CURRENCY, 2).'</td>
									<td headers="best-ca-ord" class="right">'.ria_number_format($stat['ord_total_ht'], NumberFormatter::CURRENCY, 2).'</td>
								</tr>
						';

						$total['orders'] += $stat['orders'];
						$total['by-ord'] += $stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0;
						$total['ca'] += $stat['total_ht'];
						//$total['ca-ord'] += $stat['ord_total_ht'];
					}
				}else{
					$link = '/admin/catalog/product.php?cat='.$parent.'&amp;prd='.$prd['id'];

	 				$stat = array( 'qte'=>0, 'margin'=>0, 'total_ht'=>0, 'orders'=>0, 'ord_total_ht'=>0 );
					$rstat = stats_bestsellers_get( $date1, $date2, 1, $origin, $gescom, $wst_id, false, $prd['id'], 0, false, 0, 0, $seller_id, $active_users_ids );
					if( $rstat && ria_mysql_num_rows($rstat) ){
						$stat = ria_mysql_fetch_array( $rstat );
					}

					$tbody .= '
							<tr>';
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW
					') ){
					$tbody .= ' <td headers="best-ref"><a href="'.$link.'">'.view_prd_is_sync($prd).' '.htmlspecialchars( $prd['ref'] ).'</a></td>
								<td headers="best-name"><a href="'.$link.'">'.htmlspecialchars( $prd['name'] ).'</a></td>';
					}else{
					$tbody .= ' <td headers="best-ref">'.view_prd_is_sync($prd).' '.htmlspecialchars( $prd['ref'] ).'</td>
								<td headers="best-name">'.htmlspecialchars( $prd['name'] ).'</td>';
					}
					$tbody .= ' <td headers="best-orders" class="right">'.ria_number_format($stat['orders']).'</td>
								<td headers="best-by-ord" class="right">'.ria_number_format($stat['orders'] > 0 ? ($stat['qte'] / $stat['orders']) : 0).'</td>
								<td headers="best-ca" class="right">'.ria_number_format($stat['total_ht'], NumberFormatter::CURRENCY, 2).'</td>
								<td headers="best-ca-ord" class="right">'.ria_number_format($stat['ord_total_ht'], NumberFormatter::CURRENCY, 2).'</td>
							</tr>
					';

					$total['orders'] += $stat['orders'];
					$total['by-ord'] += $stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0;
					$total['ca'] += $stat['total_ht'];
					//$total['ca-ord'] += $stat['ord_total_ht'];
				}
			}

			{ // Affichage

					print '
					<table id="bestseller-prd" class="checklist tablesorter">
						<caption>'._('Produits').'</caption>
						<col width="190" /><col width="*" /><col width="75" /><col width="130" /><col width="120" /><col width="120" />
						<thead>
							<tr>
								<th id="best-ref">'._('Référence').'</th>
								<th id="best-name">'._('Désignation').'</th>
								<th id="best-orders" class="align-right">'._('Ventes').'</th>
								<th id="best-by-ord" class="align-right">'._('Par commandes').'</th>
								<th id="best-ca" class="align-right"><abbr title="'._('Chiffre d\'affaires pour le produit').'">'._('CA').'</abbr></th>
								<th id="best-ca-ord" class="align-right"><abbr title="'._('Chiffre d\'affaires des commandes contenant ce produit').'">'._('CA Total').'</abbr></th>
							</tr>
						</thead>
						<tfoot>
							<tr class="right bold">
								<td colspan="2" headers="best-name">'._('Totaux').' :</td>
								<td headers="best-orders" class="right">'.ria_number_format($total['orders']).'</td>
								<td headers="best-by-ord" class="right">'.ria_number_format($total['by-ord']).'</td>
								<td headers="best-ca" class="right">'.ria_number_format($total['ca'], NumberFormatter::CURRENCY, 2).'</td>
								<td headers="best-ca-ord" class="right">'.ria_number_format($total['ca-ord'], NumberFormatter::CURRENCY, 2).'</td>
							</tr>
						</tfoot>
						<tbody>'.$tbody.'</tbody>
					</table>
				';
			}
		}
	}
?>
	<div class="notice">
		<ul>
			<li><?php print _('Ventes : Nombre de commandes contenant cet article'); ?></li>
			<li><?php print _('Par commande : Quantité moyenne par commande'); ?></li>
			<li><?php print _('CA : Chiffre d\'affaires généré par ce produit'); ?></li>
			<li><?php print _('CA Total : Chiffre d\'affaires généré par les commandes contenant ce produit'); ?></li>
		</ul>
	</div>
	<script>
		function parent_select_user(id, email, name) {
			if ($('#user-' + id).length > 0) return;

			var usersCount = $('#select-users .parent').length;

			var el = ' \
				<a class="parent" name="user-' + id + '"> \
					<input type="checkbox" id="user-' + id + '" name="users[]" value="' + id + '" checked> \
					<label for="user-' + id + '">' + name + '</label> \
				</a> \
			';

			if (usersCount === 0) {
				$('#user-search').before(el + '<a id="selector-sep-users" class="selector-sep"></a>');
			} else {
				$('#selector-sep-users').before(el);
			}

			composeUrlToFilterUsers();
		}

		$(document).ready(function () {
			$('#user-search').click(function () {
				displayPopup(pmtSpecialsSelectCompteClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&get-all-name=1');
			});

			$('[name="user-0"]').click(function () {
				$('#select-users input:checkbox').prop('checked', false);

				composeUrlToFilterUsers();
			});
		});
	</script>
	<?php if (!IS_AJAX) : ?>
		</div>
		<script>
			<?php print view_date_initialized(0, '/admin/stats/bestsellers-cat.php', false, array('refresh_in_ajax' => false)); ?>
		</script>
		<?php require_once('admin/skin/footer.inc.php'); ?>
	<?php endif; ?>

<?php
	if (isset($_POST['save'])) {
		$typeImg = (isset($_POST['typeImg'])) ? $_POST['typeImg'] : null;
		if( !in_array($typeImg, array('tabMain', 'tabSub')) ){
			$error = _('Vous devez choisir si l\'image est principale ou secondaire.');
		}else{
			$mode = (isset($_POST['mode'])) ? $_POST['mode'] : null;
			if( !in_array($mode, array('mediatheque', 'file')) ){
				$error = _('Vous devez sélectionner un mode d\'ajout');
			}else{
				if( $mode==='mediatheque' ){
					// Médiathèque
					if( !(isset($_POST['media']) && $_POST['media']) ){
						$error = _('Vous devez sélectionner une image');
					}else{
						$f = 'prd_images' . ($typeImg === 'tabMain' ? '_main' : '') . '_add_existing';
						if( !$f($_GET['prd'], $_POST['media']) ){
							$error = _('Une erreur inattendue s\'est produite lors de la mise à jour de l\'image.');
						}else{
							$success = _('L\'image a été ajoutée avec succès.');
						}
					}
				}elseif( $mode==='file' ){
					// Ajout d'une image
					if( !(isset($_FILES['file']) && $_FILES['file']['error'] != UPLOAD_ERR_NO_FILE) ){
						$error = _('Vous devez sélectionner un fichier.');
					}else{
						$f = 'prd_images' . ($typeImg === 'tabMain' ? '_main' : '') . '_upload';
						if( !$f($_GET['prd'], 'file') ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image principale.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}else{
							$success = _('L\'image a été ajoutée avec succès.');
						}
					}
				}
			}
		}
	}
	
	$typeImg = isset($_GET['type']) ? $_GET['type'] : 0;
	if( isset($_POST['typeImg']) ){
		$typeImg = $_POST['typeImg'];
	}

	define('ADMIN_PAGE_TITLE', _('Ajouter des images'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
		<form id="form" action="" method="post" enctype="multipart/form-data">
			<input type="hidden" name="images" />
			<?php
				if( isset($error) ){
					echo '<div class="errors">' . $error . '</div>';
				}
				if( isset($success) ){
					echo '<div class="success">' . $success . '</div>';
				}
			?>
			<div style="float: left">
				<input id="mode_mediatheque" name="mode" type="radio" value="mediatheque" checked="checked" />
				<label for="mode_mediatheque"><?php print _('Sélectionner une image depuis la médiathèque'); ?></label>
			</div>
			<div style="float: right; width: 525px; text-align: right;">
				<input id="src" name="q" type="text" />
				<input id="search" name="search" type="submit" value="<?php print _('Rechercher'); ?>" />
			</div>
			<div style="clear: both"></div>
			<div id="mediatheque-container">
				<div id="nav">
					<ul>
						<li>
							<a id="type-all" class="racine dir all selected" href="#"><strong><?php print _('Médiathèques'); ?></strong></a>
							<ul>
								<li><a id="type-prd" class="dir prd" href="#"><?php print _('Produits'); ?></a></li>
								<li><a id="type-prd-cat" class="dir prd-cat" href="#"><?php print _('Catégories'); ?></a></li>
								<li><a id="type-str" class="dir str" href="#"><?php print _('Magasins'); ?></a></li>
								<li><a id="type-cms" class="dir cms" href="#"><?php print _('Contenu'); ?></a></li>
								<li><a id="type-news" class="dir news" href="#"><?php print _('Actualité'); ?></a></li>
							</ul>
						</li>
					</ul>
				</div>
				<div id="mediatheque"></div>
				<div style="clear: both"></div>
			</div>
			<input id="media" name="media" type="hidden" />
			<input id="type" name="type" type="hidden" />
			<div class="padt">
				<input id="mode_file" name="mode" type="radio" value="file" />
				<label for="mode_file"><?php print _('Ajouter une image :'); ?></label>
				<input id="file" name="file" type="file" />
			</div>
			
			<div class="padt">
				<input id="type_main" name="typeImg" type="radio" value="tabMain"<?php if ($typeImg === 'tabMain') echo ' checked="checked"'; ?> />
				<label for="type_main"><?php print _('Image principale'); ?></label>
				
				<input id="type_secondary" name="typeImg" type="radio" value="tabSub"<?php if ($typeImg === 'tabSub') echo ' checked="checked"'; ?> />
				<label for="type_secondary"><?php print _('Image secondaire'); ?></label>
			</div>
			<div class="padt">
				<input name="save" type="submit" value="<?php print _('Enregistrer'); ?>" />
			</div>
		</form>
		
		<script src="/admin/js/jquery.min.js"></script>
		<script>
		</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('search.inc.php');
	require_once('view.ctr.inc.php');
	
	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;
	
	$txt = '';
	if( isset($_POST['search']) || isset($_POST['brands']) || isset($_POST['promo']) || isset($_POST['min_price']) || isset($_POST['max_price']) ){
		
		// Création de l'url des pages suivantes ainsi que des paramètres à prd_products_get()
		$href = '?ctr='.$_GET['ctr'];
		if( isset($_GET['search']) && $_GET['search']!="" ){ $href .= '&search='.$_GET['search']; }
		if( isset($_GET['promo']) ){ $promo=1; $href .= '&promo=1'; } else { $promo=0; }
		if( isset($_GET['export']) ){ $export=1; $href .= '&export=1'; } else { $export=0; }
		if( isset($_GET['brands']) ){ $brand=$_GET['brands']; $href .= '&brands='.$_GET['brands']; } else { $brand=0; }
		if( isset($_GET['min-price']) && is_numeric($_GET['min-price']) ){ $min_price=$_GET['min-price']; $href .= '&min-price='.$_GET['min-price']; } else { $min_price=0; }
		if( isset($_GET['max-price']) && is_numeric($_GET['max-price']) ){ $max_price=$_GET['max-price']; $href .= '&max-price='.$_GET['max-price']; } else { $max_price=0; }

		// Tableau de produits, $ids va servir à ne pas mettre deux fois le même produit
		$r_prd = array();
		$ids = array();
		
		// Création du tableau de produits
		if( isset($_GET['search']) && $_GET['search']!="" ){ // Si une recherche de mots clés est demandée
		
			$results = search3( 1, $_GET['search'], 1, $config['prd_search_length'], true, false, 6, array('prd') );

			$prd_published = 0;
			while( $rt = ria_mysql_fetch_array($results) ){
			
				if( !in_array($rt['tag'],$ids) ){
					$ids[] = $rt['tag'];
					$p = ria_mysql_fetch_array(prd_products_get($rt['tag'],'',$brand,true,0,0,-1,true,false,false,false,$promo,true,false,false,false,false,false,false,false,null,true,false,true,false,false,false,null,$min_price,$max_price));
					$have = true;
					if( $p && $p['orderable'] && $p['publish'] ){
						if( $export && !ctr_catalogs_is_publish($_GET['ctr'], $p['id']) ){ $have = false; }
						if( ctr_catalogs_is_publish($_GET['ctr'],$p['id']) )
							$prd_published++;
							
						if( $have && $p['orderable'] && $p['publish'] ){
							if( $p['stock']>0 ) {
								$r_prd[] = $p;
							}
						}
					}
					
				}
				
			}
			
		} elseif( $export ){ // Si seul les produits en exportation sont demandés
		
			$r_ctl = ctr_catalogs_get($_GET['ctr']);
			while( $ctl = ria_mysql_fetch_array($r_ctl) ){
				$r_prd[] = ria_mysql_fetch_array( prd_products_get($ctl['prd_id']) );
			}
		
		} else { // Si rien n'est précisé, on affiche les produits à exporter.
			$results = prd_products_get(0,'',$brand,true,0,0,-1,true,false,false,false,$promo,true,false,false,false,false,false,false,false,null,true,false,true,false,false,false,null,$min_price,$max_price);
			
			while( $p = ria_mysql_fetch_array($results) ){
				if( !ctr_catalogs_is_publish($_GET['ctr'], $p['id']) )
					$r_prd[] = $p;
			}

		}

		// Calcule le nombre de résultat et de pages
		$results_count = sizeof($r_prd);
		$pages = ceil($results_count / 10);

		if( $results_count==0 ){ // Aucun résultat
			$txt .=  '<span>Aucun résultat</span>';
			if( !isset($_GET['search']) || $_GET['search']=="" )
				$txt .= '<br /><span>'._('Merci de bien vouloir spécifier une recherche').'</span>';
		}else{

			// Définie le numéro de pages
			if( !isset($_GET['page']) ){
				$page = 1;
			}else{
				if( !is_numeric($_GET['page']) )
					$page = 1;
				elseif( $_GET['page']<1 )
					$page = 1;
				elseif( $_GET['page']>$pages )
					$page = $pages;
				else
					$page = $_GET['page'];
			}
			
			// Personnalisation du message sur le nombre de résultat
			if( isset($_GET['search']) && $_GET['search']!="" )
				$txt .=  '<span>'.sprintf(_('%d résultats pour <b>%s</b>'), $results_count, htmlspecialchars($_GET['search'])).'</span>';
			else
				$txt .= '<span>'.sprintf(_('%d résultats'), $results_count);
				
			// Création du tableau des produits
			$txt .=  '
				<table class="products">
					<col width="5%" /><col width="5%" /><col width="*" /><col width="20%" /><col width="5%" /><col width="20%" />
				<thead>
					<tr>
						<th id="check"><input type="checkbox" name="checkbox" id="checkbox" onclick="checkedBox();" /></th>
						<th id="image">'._('Photo').'</th>
						<th id="name">'._('Désignation').'</th>
						<th id="cat">'._('Catégorie').'</th>
						<th id="dispo">'._('Stock').'</th>
						<th id="price-prd">'._('Prix').'</th>
					</tr>
				</thead>
				<tbody>
			';

			$count = 0;

			// Si on n'est pas sur la page 1, on se déplace dans le tableau
			if( $page>1 )
				$r_prd = array_slice( $r_prd, ($page-1)*10, 10);
				
			foreach( $r_prd as $prd ){
				
				if($count==10){
					break;
				}
				$count++;
				
				if( ctr_catalogs_is_publish($_GET['ctr'], $prd['id']) ){
					$txt .=  '<tr id="tr-prd-'.$prd['id'].'" class="checked">';
					$txt .=  '<td class="check" header="check">
						<input type="checkbox" name="prd[]" id="prd-'.$prd['id'].'" value="'.$prd['id'].'" checked="checked" />
					</td>';
				} else {
					$txt .=  '<tr id="tr-prd-'.$prd['id'].'">';
					$txt .=  '<td class="check" header="check"><input type="checkbox" name="prd[]" id="prd-'.$prd['id'].'" value="'.$prd['id'].'" /></td>';
				}
				
				// Affichage de l'image
				$width = $config['img_sizes']['small']['width'];
				$height = $config['img_sizes']['small']['height'];
				$src = $config['img_url'].'/'.$width.'x'.$height.'/' .( $prd['img_id'] ? $prd['img_id'].'.jpg' : 'default.gif' );
				$txt .=  '<td headers="image" class="photo">';
					$src = $src.'/'.$width.'x'.$height.'/' .( $prd['img_id'] ? $prd['img_id'].'.jpg' : 'default.gif' );
					$title = htmlspecialchars( $prd['brd_title'] ? $prd['name'].' '.$prd['brd_title'] : $prd['name'] );
					$txt .=  '	<img src="'.$src.'" width="70" height="70" alt="'.$title.'" title="'.$title.'" />';
				$txt .=  '</td>';

				// Affichage de la désignation
				$desc = $prd['desc'];
				if( strlen($desc)>105 )
						$desc = substr( $desc, 0, 102 ).'...';
				$txt .=  '<td headers="name" class="name">'.$prd['name'].'<br /><br />
					<span class="italic"><span class="smaller">'.htmlspecialchars($desc).'</span></span>
				</td>';
				
				// Affichage de la catégorie de rattachement
				$txt .= '<td header="cat">';
					$cat = ctr_cat_product_get($_GET['ctr'],$prd['id']);
					$c = ria_mysql_fetch_array(prd_products_categories_get($prd['id']));
					if( !$cat ){
						$txt .= '<span class="red">'._('Il existe un conflit de rattachement.').'<br /><a onclick="conflit('.$prd['id'].');">'._('Résoudre').'</a></span>';
					}else{
						if( $cat['statut']=='ctr' ){
							$txt .= $c['name'].'<br/>'._('liée à').' :<br /><span class="bold">'.$cat['name'].'</span>';
						} elseif( $cat['statut']=='cat' ){
							$txt .= $c['name'];
						} elseif( $cat['statut']=='prd' ){
							$txt .= _('Produit lié à').' :<br /><span class="bold">'.$cat['name'].'</span>';
						}
					}
				$txt .= '</td>';

				// Affichage des stocks
				$txt .=  '<td headers="dispo" class="stocks">'.$prd['stock'].'</td>';

				// Affichage du prix
				$txt .=  '<td headers="price-prd" class="prices">';
					if( $prd['orderable'] ){
						$txt .=  '	<div class="price price-ttc"><span class="lbl-price"><span class="price">';
						//promotions
						$pmt = prc_promotions_get( $prd['id'] );
						if($pmt && ria_mysql_num_rows($pmt) ){
							$pmt = ria_mysql_fetch_array($pmt);
							if( $pmt['price_ht']<=0 )
								unset($pmt);
						}else{
							unset($pmt);
						}
						// Affichage du prix
						if( $prd['orderable'] ){
							/*Prix promo*/
							if( isset($pmt) ){
								$txt .=  number_format($pmt['price_ttc'],2,',',' ');
								$txt .=  ' &euro;</span></span><br />';
								$txt .=  '<span class="smaller barre gris">';
								$txt .=  number_format($prd['price_ttc'],2,',',' ');
								$txt .=  ' &euro;</span>';
							}
							/*Prix non promo*/
							else {
								$txt .=  number_format($prd['price_ttc'],2,',',' ');
								$txt .=  ' &euro;</span></span>';
							}
						}	
						$txt .=  '	</div>'."\n";
					}
				$txt .=  '</td></tr>';
			}
			
			$txt .=  '</tbody>';
			
			// Affichage de la navigation
			if( $results_count>0 ){
				$txt .=  '<tfoot>';
					$txt .=  '<th colspan="2">
						<div class="search-nav-box">';
							$txt .=  '<div class="search-page">Page '.$page.' / '.$pages.'</div>
						</div>';
					$txt .=  '</th>
					<th colspan="4">
						<div class="search-nav">';
							$links = array();
							if( $page>1 )
								$links[] = '<a href="link-products.php'.$href.'&page='.($page-1).'">&laquo; '._('Page précédente').'</a>';
							for( $i=$page-5; $i<=$page+5; $i++ )
								if( $i>=1 && $i<=$pages )
									if( $i==$page ){
										$links[] = '<strong>'.$i.'</strong>';
									}else{
										$links[] = '<a href="link-products.php'.$href.'&page='.$i.'">'.$i.'</a>';
									}
							if( $page<$pages )
								$links[] = ' <a href="link-products.php'.$href.'&page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
							$txt .=  implode(' | ',$links);
						$txt .=  '</div>';
					$txt .=  '</th>';
				$txt .=  '</tfoot>';
			}
			$txt .= '</table>';
		}
	}

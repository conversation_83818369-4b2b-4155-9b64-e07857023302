<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('products.inc.php');
	require_once('comparators.inc.php');
	require_once('stats.inc.php');
	require_once('view.admin.inc.php');
	
	// chargement du comparateur
	if( !isset($_GET['ctr']) || !($ctr = ria_mysql_fetch_array( ctr_comparators_get( $_GET['ctr'], true, false, null) )) ){
		$error = _("Une erreur s'est produite lors du chargement des informations sur le comparateur de prix.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler ce problème");
		exit;
	}
	
	$filter = isset($_POST['filter']) && trim($_POST['filter'])!='' ? $_POST['filter'] : false;
	$marketplace = $ctr['marketplace'];

	$url = '/admin/comparators/popup-choose-family.php?ctr='.$_GET['ctr'];
	if( $marketplace ){
		$url .= '&marketplace=1';
	}else{
		$url .= '&marketplace=0';
	}

	// &family=26875&object=130446&class=1&new-cat[130446]=22805&new-cat[142014]=0
	if( isset($_GET['family']) ){
		$url .= '&family='.$_GET['family'];
	}
	if( isset($_GET['object']) ){
		$url .= '&object='.$_GET['object'];
	}
	if( isset($_GET['class']) ){
		$url .='&class='.$_GET['class'];
	}
	if( isset($_GET['new-cat']) && is_array($_GET['new-cat']) ){
		foreach( $_GET['new-cat'] as $key_one_new=>$one_new ){
			$url .= '&new-cat['.$key_one_new.']='.$one_new;
		}
	}

	$_GET['cat'] = isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ? $_GET['cat'] : 0;
	$parent = '';
	if( $_GET['cat']>0 && $filter==false ){
		$rparent = ctr_categories_get( $_GET['ctr'], $_GET['cat'] );
		if( $rparent && ria_mysql_num_rows($rparent) ){
			$parent = ria_mysql_fetch_array( $rparent );
		}
	}

	define('ADMIN_PAGE_TITLE', _('Choix d\'une famille'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<div id="popupctr">
		<form action="<?php print $url; ?>" method="post">
			<div id="tabpanel">
				<h2><?php printf($marketplace ? _('Choisissez une famille de la place de marché %s') : _('Choisissez une famille du comparateur %s'), htmlspecialchars( $ctr['name'] ) ); ?></h2>
				<table class="checklist" id="choose-cat-ctr">
					<caption>
						<?php if( $parent!='' ){ ?>
						<a href="<?php print $url.'&amp;cat='.$parent['parent']; ?>">
							<img width="16" height="16" title="<?php print _('Remonter d\'un niveau'); ?>" alt="<?php print _('Remonter d\'un niveau'); ?>" src="/admin/images/up.png">
						</a><?php
							print htmlspecialchars( $parent['name'] );
						} else { ?>
							<?php printf($marketplace ? _('Choisissez une famille de la place de marché "%s"') : _('Choisissez une famille du comparateur "%s"'), htmlspecialchars( $ctr['name'] ) ); ?>
						<?php } ?>
					</caption>
					<thead>
						<tr>
							<th class="align-right" id="cat-name">
								<label for="filter"><?php print _('Rechercher une catégorie :'); ?><label>
								<input type="text" name="filter" id="filter" value="<?php print $filter; ?>" />
							</th>
						</tr><tr>
							<th id="cat-name"><?php print _('Désignation'); ?></th>
						</tr>
					</thead>
					<tbody><?php
						$pselected = false;
						if( is_array($parent) && sizeof($parent) ){
							$pselected = in_array($ctr['id'], $config['ctr_free']) || $parent['cpc']>0 || $marketplace;
							$pselected = $pselected && !in_array($ctr['id'], $config['ctr_last_level']);
						}
						
						$rcat = ctr_categories_get( $ctr['id'], 0, $filter===false ? $_GET['cat'] : '', true, false, $filter );
						if( !$rcat || !ria_mysql_num_rows($rcat) ){
							if( $pselected && $_GET['cat']>0 ){
								print '	<tr>
											<td>
												<input checked="checked" type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
												<label for="category">'._('Sélectionner cette famille').'</label>
											</td>
										</tr>';
							}else{
								print '<tr><td>'._('Aucune famille n\'a été trouvée.').'</td></tr>';
							}
						} else {
							while( $cat = ria_mysql_fetch_array($rcat) ){
								$selected = in_array($ctr['id'], $config['ctr_free']) || $cat['cpc']>0 || $marketplace;
								$havechilds = ctr_categories_have_childs($ctr['id'], $cat['id']);
								
								if( !$selected && !$havechilds ){
									continue;
								}
								
								$selected = $selected && (!in_array($ctr['id'], $config['ctr_last_level']) || !$havechilds);
								
								$attr = '';
								if( isset($_GET['pre_select']) && $_GET['pre_select']==$cat['id'] ){
									$attr .= ' checked="checked"';
								}
								
								if( !$selected ){
									$attr .= ' disabled="disabled"';
									$attr .= ' title="'._('Vous ne pouvez choisir cette catégorie directement. Veuillez sélectionner une sous-catégorie.').'"';
								}
								
								print '	<tr>
											<td>
												<input'.$attr.' type="radio" name="category" id="category-'.$cat['id'].'" value="'.$cat['id'].'" />';
								if( $havechilds ){
									print '		<a href="'.$url.'&amp;cat='.$cat['id'].'">'.$cat['name'].'</a>';
								}else{
									print '		<label for="category-'.$cat['id'].'">'.$cat['name'].'</label>';
								}
								print '		</td>
										</tr>';
							}
							
							if( $pselected && $_GET['cat']>0 ){
								print '	<tr>
											<td>
												<input type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
												<label for="category">'._('Sélectionner cette famille').'</label>
											</td>
										</tr>';
							}
						}
					?></tbody>
					<tfoot>
						<tr>
							<td>
								<input class="save" type="submit" name="save-choose" id="save-choose" value="<?php print _('Choisir'); ?>" />
								<a id="cancel" class="cancel"><?php print _('Annuler'); ?></a>
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</form>
<?php 
	if( isset($_POST['category']) ){
		$name = ctr_categories_get_name( $_GET['ctr'], $_POST['category'] );
		print '<script>parent.choose_ctr_family('.$_POST['category'].', \''.addslashes( $name ).'\');</script>';
	}
?>

	<script><!--
		<?php 
		
		if( isset($_GET['object']) && is_numeric($_GET['object']) ){
			$url_cancel = $url_details = 'popup-details-family.php?ctr='.$_GET['ctr'].'&oldcat='.$_GET['family'].'&class='.$_GET['class'].'&object='.$_GET['object'];

			if( isset($_GET['new-cat']) && is_array($_GET['new-cat']) ){
				foreach( $_GET['new-cat'] as $key_one_new=>$one_new ){
					if( !is_numeric($one_new) || $one_new<=0 ){
					    continue;
					}

					if( $key_one_new == $_GET['object'] ){
						$url_cancel .= '&new-cat['.$key_one_new.']='.$one_new;
						continue;
					}

					$url_details .= '&new-cat['.$key_one_new.']='.$one_new;
					$url_cancel .= '&new-cat['.$key_one_new.']='.$one_new;
				}
			}
			?>

			$(document).delegate('#save-choose', 'click', function(){
				if( !$('#filter').is(':focus') ){
					var choose = $('[name=category]:checked').val();
					var urlDetails = '<?php print $url_details; ?>&new-cat[<?php print $_GET['object']; ?>]=' + choose;
					parent.displayPopup('<?php print _('Choisissez une famille'); ?>', '', urlDetails, '', 620, 450);
					return false;
				}
			});

			$('#cancel').click(function(){
				parent.displayPopup('<?php print _('Choisissez une famille'); ?>', '', '<?php print $url_cancel; ?>', '', 620, 450);
				return false;
			});
	
		<?php }else{ ?>
	
			$('#cancel').click(function(){
				parent.hidePopup();
			});
	
		<?php } ?>
	--></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>

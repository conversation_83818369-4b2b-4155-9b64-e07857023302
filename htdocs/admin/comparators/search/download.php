<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	if (!isset($_GET['marketplace'])){
		print _("Il manque des paramètres");
		exit;
	}
	$marketplace = $_GET['marketplace']=='true';
	
	$ar_conditions = array();
	$date1 = isset($_GET['date1']) && isdate($_GET['date1']) ? dateparse($_GET['date1']) : false;
	$date2 = isset($_GET['date2']) && isdate($_GET['date2']) ? dateparse($_GET['date2']) : false;
	
	foreach( $_GET['cdts'] as $key => $val ){
		
		$symbol = isset($_GET['sbl'][ $key ]) ? $_GET['sbl'][ $key ] : '=';
		
		$end = strpos( $key, '-' );
		$cdt = $end ? substr( $key, 0, $end ) : $key;
		
		$ar_conditions[] = array(
			'code' => $cdt,
			'symbol' => $symbol,
			'value' => $val
		);
	}

	//ligne du .csv
	$rows=array();
	
	// Lance la recherche
	$rsearch = ctr_search_products( $_GET['ctrid'], 0, $date1, $date2, $ar_conditions, $marketplace=='true' );
	
	// Affiche l'entête des données
	if( !$marketplace ){
		$header_row = array(
			"prd-ref" => _("Référence"),
			"prd" => _("Désignation"),
			"prd-click" => _("Clics"),
			"prd-cost" => _("Coût"),
			"prd-sales" => _("Ventes"),
			"prd-transfo" => _("Transfo."),
			"prd-cost-sales" => _("Coût/Vente"),
			"prd-ca" => _("CA HT"),
			"prd-ca-ttc" => _("CA TTC"),
			"prd-margin" => _("Marge"),
			"prd-roi" => _("ROI"),
			"prd-export" => _("Exporté"));
	}else{
		$header_row = array(
			"prd-ref" => _("Référence"),
			"prd" => _("Désignation"),
			"prd-sales" => _("Ventes"),
			"prd-ca" => _("CA HT"),
			"prd-ca-ttc" => _("CA TTC"),
			"prd-margin" => _("Marge"),
			"prd-roi" => _("ROI"),
			"prd-export" => _("Exporté"));
	}

	// Charge les données dans le fichier CSV
	if( is_array($rsearch) && sizeof($rsearch) ){
		$i = 0;
		while( true ){
			if( !isset($rsearch[$i]) ){
				break;
			}
			
			$r = $rsearch[$i];
			
			$title = trim($r['title']) ? $r['title'] : $r['name'];
			
			if( !$marketplace ){
				$data_row=array(
					"prd-ref" => $r['ref'],
					"prd" => $title,
					"prd-click" => number_format( $r['click'], 0, ',', ' ' ),
					"prd-cost" => number_format( $r['cost'], 2, ',', ' ' ),
					"prd-sales" => number_format( $r['sales'], 0, ',', ' ' ),
					"prd-transfo" => number_format( $r['transfo'], 2, ',', ' ' ),
					"prd-cost-sales" => $r['sales']>0 ? number_format( ($r['cost']/$r['sales']), 2, ',', ' ' ) : '0,00',
					"prd-ca" => number_format( $r['ca'], 2, ',', ' ' ),
					"prd-ca-ttc" => number_format( $r['ca_ttc'], 2, ',', ' ' ),
					"prd-margin" => number_format( $r['margin'], 2, ',', ' ' ),
					"prd-roi" => number_format( $r['roi'], 2, ',', ' ' ),
					"prd-export" => $r['export'] ? ($r['export']=='mix' ? _('Partiellement') : _('Oui')) : _('Non'));
			}else{
				$data_row=array(
					"prd-ref" => $r['ref'],
					"prd" => $title,
					"prd-sales" => number_format( $r['sales'], 0, ',', ' ' ),
					"prd-ca" => number_format( $r['ca'], 2, ',', ' ' ),
					"prd-ca-ttc" => number_format( $r['ca_ttc'], 2, ',', ' ' ),
					"prd-margin" => number_format( $r['margin'], 2, ',', ' ' ),
					"prd-roi" => number_format( $r['roi'], 2, ',', ' ' ),
					"prd-export" => $r['export'] ? ($r['export']=='mix' ? _('Partiellement') : _('Oui')) : _('Non')
				);
			}

			$rows[] = $data_row;
			$i++;
		}
		 
		//Trie le tableau
		if($_GET['sort']!='' && $_GET['dir']!=''){
			switch($_GET['sort']){
				case 'prd-click':
				case 'prd-cost':
				case 'prd-sales':
				case 'prd-transfo':
				case 'prd-cost-sales':
				case 'prd-ca':
				case 'prd-ca-ttc':
				case 'prd-margin':
				case 'prd-roi':
					foreach ($rows as $key => $row) {
						$rang[$key]  = str_replace(' ', '', $row[$_GET['sort']]);
					}
					if($_GET['dir']=='asc'){
						array_multisort($rang, SORT_ASC, SORT_NUMERIC, $rows);
					}else{
						array_multisort($rang, SORT_DESC, SORT_NUMERIC, $rows);
					}
					break;
				default:
					foreach ($rows as $key => $row) {
						$rang[$key]  = strtolower($row[$_GET['sort']]);
					}
					if($_GET['dir']=='asc'){
						array_multisort($rang, SORT_ASC, $rows);
					}else{
						array_multisort($rang, SORT_DESC, $rows);
					}
					break;
			}		
		}
	
		//Ajoute l'entête au debut du tableau
		array_unshift($rows, $header_row);

		$file_csv = $config['doc_dir'].'/recherche-stats-'.( $marketplace ? 'marketplaces' : 'comparateurs' ).'.csv';
		$handle = fopen($file_csv, 'w');

		foreach($rows as $line){
			fputcsv($handle, $line, ';');
		}	

		fclose($handle);
	}

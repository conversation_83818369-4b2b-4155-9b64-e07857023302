<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');

	if( !isset($_GET['prd'], $_GET['marketplace']) ){
		$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations générales.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	}

	$rctr = ctr_comparators_get( 0, true, false, isset($_GET['marketplace']) && $_GET['marketplace'] ? true : false );

	if( isset($_POST['save-export']) ){
		if( $rctr && ria_mysql_num_rows($rctr) ){
			$ar_ctr_active = isset($_POST['ctr-export']) && is_array($_POST['ctr-export']) ? $_POST['ctr-export'] : array();

			while( $ctr = ria_mysql_fetch_array($rctr) ){
				if( in_array($ctr['id'], $ar_ctr_active) ){
					if( !ctr_catalogs_activated($ctr['id'], $_GET['prd']) ){
						$error = sprintf(_("Une erreur inattendue s'est produite lors de la activation du produit sur \"%s\""), $ctr['name'])."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}
				}else{
					if( !ctr_catalogs_unactivated($ctr['id'], $_GET['prd']) ){
						$error = sprintf(_("Une erreur inattendue s'est produite lors de la désactivation du produit sur \"%s\""), $ctr['name'])."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}
				}
			}

			ria_mysql_data_seek( $rctr, 0 );
		}

		if( !isset($error) ){
			$_SESSION['save-multiexport'] = true;
			header('Location: /admin/comparators/search/popup-multiexport.php?prd='.$_GET['prd'].'&marketplace='.$_GET['marketplace']);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Export des produits') . ' - ' . _('Comparateurs'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
		if( isset($_SESSION['save-multiexport']) ){
			print '<div class="success">'._('L\'enregistrement s\'est correctement déroulé.').'</div>';
		}
?>

	<form action="/admin/comparators/search/popup-multiexport.php?prd=<?php print $_GET['prd']; ?>&amp;marketplace=<?php print $_GET['marketplace']; ?>" method="post">
		<p><?php print $_GET['marketplace'] ? _('Vous pouvez à partir d\'ici choisir vers quels comparateurs de prix le produit doit être exporté') : _('Vous pouvez à partir d\'ici choisir vers quelles places de marché le produit doit être exporté'); ?></p>

		<table class="checklist" cellpadding="0" cellspacing="0">
			<caption><?php print !$_GET['marketplace'] ? _('Liste des comparateurs de prix') : _('Liste des places de marché'); ?></caption>
			<col widh="300" /><col width="75" />
			<thead>
				<tr>
					<th id="ctr"><?php print _('Exporter sur'); ?></th>
					<th align="center" id="export"><input type="checkbox" name="checkall" id="checkall" /></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" name="save-export" value="<?php print _('Enregistrer'); ?>" />
						<input type="button" name="cancel" value="<?php print _('Annuler'); ?>" />
					</td>
				</tr>
			</tfoot>
			<tbody><?php
				if( !$rctr || !ria_mysql_num_rows($rctr) ){
					print '
						<tr>
							<td colspan="2">'.($_GET['marketplace'] ? _('Aucune place de marché n\'est activée') : _('Aucun comparateur de prix n\'est activé')).'</td>
						</tr>
					';
				}else{
					while( $ctr = ria_mysql_fetch_array($rctr) ){
						$checked = ctr_catalogs_is_publish($ctr['id'], $_GET['prd']) ? 'checked="checked"' : '';
						print '
							<tr>
								<td headers="ctr">
									<label for="ctr-export-'.$ctr['id'].'">'.htmlspecialchars( $ctr['name'] ).'</label>
								</td>
								<td align="center" headers="export">
									<input type="checkbox" '.$checked.' name="ctr-export[]" id="ctr-export-'.$ctr['id'].'" value="'.$ctr['id'].'" />
								</td>
							</tr>
						';
					}
				}
			?></tbody>
		</table>
	</form>

	<script><!--
		$(document).delegate(
			'#checkall', 'click', function(){
				if( $(this).is(':checked') ){
					$(this).parents('table').find('tbody input').attr('checked', 'checked');
				}else{
					$(this).parents('table').find('tbody input').removeAttr('checked');
				}
			}
		).delegate(
			'table tbody input', 'click', function(){
				if( $('table tbody input').not(':checked').length ){
					$('#checkall').removeAttr('checked');
				}else{
					$('#checkall').attr('checked', 'checked');
				}
			}
		);
	--></script>

<?php }
	require_once('admin/skin/footer.inc.php');
?>
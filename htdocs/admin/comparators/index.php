<?php
	require_once('comparators.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	$title = '';
	$desc = '';
	if( gu_users_admin_rights_used('_MENU_COMPARATORS') && gu_users_admin_rights_used('_MENU_MARKETPLACE') ){
		$title = _('Comparateurs de prix / Places de marché');
		$desc = _('Exportez votre catalogue sur les comparateurs de prix et places de marché.');
	}else if( gu_users_admin_rights_used('_MENU_COMPARATORS') ){
		$title = _('Comparateurs de prix');
		$desc = _('Exportez votre catalogue sur les comparateurs de prix.');
	}else if( gu_users_admin_rights_used('_MENU_MARKETPLACE') ){
		$title = _('Places de marché');
		$desc = _('Exportez votre catalogue sur les places de marché.');
	}

	define('ADMIN_PAGE_TITLE', _('Comparateurs et places de marché'));
	require_once('admin/skin/header.inc.php');
?>
			
	<h2><?php print $title; ?></h2>
	<p id="exp" class="justify"><?php print $desc; ?></p>

<?php 

	print view_index_menu('_MDL_COMPARATORS');

	require_once('admin/skin/footer.inc.php');
?>
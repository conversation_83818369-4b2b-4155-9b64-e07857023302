<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');

	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}

	// Vérifie l'existance du comparateur de prix
	if( !isset($_GET['ctr']) || !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: /admin/comparators/index.php');
		exit;
	}

	//Récupère le nom du comparateur
	$ctr = '';
	if( isset($_GET['ctr']) ){
		$rctr = ctr_comparators_get($_GET['ctr'], true, false, $marketplace);
		if (! ria_mysql_num_rows($rctr)) {
			$marketplace = true;
			$rctr = ctr_comparators_get($_GET['ctr'], true, false, $marketplace);
		}
		$ctr = ria_mysql_result($rctr,0,'name');
	}

	$_GET['parent'] = isset($_GET['parent']) && is_numeric($_GET['parent']) && $_GET['parent']>0 ? $_GET['parent'] : 0;
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	// stats généraux
	$stats = stats_comparators_get( $_GET['ctr'], $_GET['parent'], 0, $date1, $date2 );
	$volume = 0;
	if( is_array($stats) && sizeof($stats ) ){
		$volume = $stats['clicks'];
	}

	define('ADMIN_PAGE_TITLE', sprintf( $marketplace ? _('Statistiques de la place de marché') . ' %s - ' . _('Places de marché') : _('Statistiques du comparateur') . ' %s - ' . _('Comparateurs de prix'), htmlspecialchars( $ctr ) ) );
	require_once('admin/skin/header.inc.php');

	// Aucun comparateur de prix est sélectionné
	if( !isset($_GET['ctr']) ){
?>
			<h2><?php print $marketplace ? _('Statistiques des places de marché') : _('Statistiques des comparateurs de prix'); ?></h2>
			<p id="exp" class="justify">
				<?php print $marketplace ? _('Vous avez la possibilité de consulter les statistiques de ventes réalisées grâce aux différentes places de marché') : _('Vous avez la possibilité de consulter les statistiques de ventes réalisées grâce aux différents comparateurs de prix'); ?>.
				<?php print $marketplace ? _('Pour cela, choisissez une place de marché ci-dessous :') : _('Pour cela, choisissez un comparateur de prix ci-dessous :'); ?><br />
			</p>
			<ul id="diff-cat">
				<?php
					$r_ctr = ctr_comparators_get();
					while( $ctr = ria_mysql_fetch_array($r_ctr) ){
						print '<li><a href="index.php?ctr='.$ctr['id'].'">'.$ctr['name'].'</a></li>';
					}
				?>
			</ul>
		<?php } else { // Un comparateur de prix est sélectionné ?>
			<div id="comparators">
				<?php
					require_once('view.ctr.inc.php');
					print view_ctr_header($_GET['ctr'], $marketplace);
				?>
			</div>
			<input type="hidden" name="ctr-id" id="ctr-id" value="<?php print $_GET['ctr']; ?>" />
			<input type="hidden" name="ctr-name" id="ctr-name" value="<?php print $ctr; ?>" />
			<input type="hidden" name="marketplace" id="marketplace" value="<?php print $marketplace; ?>" />
			<form action="index.php?ctr=<?php print $_GET['ctr']; ?>" method="post">
				<input type="hidden" name="cat-visit" id="cat-visit" value="<?php print $_GET['parent']; ?>" />
				<h2><?php print $marketplace ? _('Statistiques de la place de marché :') : _('Statistiques du comparateur :'); ?></h2>
				<div class="stats-menu">
					<div id="riadatepicker"></div>
					<?php
						if( !$marketplace ){
							print view_websites_selector( $_SESSION['websitepicker'], false, '', true );
						}
					?>
					<div class="clear"></div>
				</div>
				<table id="tb-ctr-cat" class="tablesorter checklist ctr-stats">
					<caption><?php
						if( $_GET['parent']<=0 ){
							print _('Catalogue');
						}else{
							$name = '<a onclick="return updateStatsCtr(0);" href="index.php?ctr='.$_GET['ctr'].'&amp;parent=0">'._('Catalogue').'</a> &raquo; ';
							$last = false;
							$parents = prd_categories_parents_get($_GET['parent']);

							$cat = ria_mysql_fetch_array(prd_categories_get($_GET['parent']));
							while( $p = ria_mysql_fetch_array($parents) ){
								$name .= '<a onclick="return updateStatsCtr('.$p['id'].');" href="index.php?ctr='.$_GET['ctr'].'&amp;parent='.$p['id'].'">'.htmlspecialchars($p['title']).'</a> &raquo; ';
								$last = $p;
							}
							print '	<a onclick="return updateStatsCtr('.($last ? $last['id'] : 0).');" href="index.php?ctr='.$_GET['ctr'].'&amp;parent='.$last['id'].'">
										<img class="fleche-move" src="/admin/images/up.png" alt="'._('Remonter d\'un niveau').'" title="'._('Remonter d\'un niveau').'" />
									</a> '.$name.htmlspecialchars($cat['title']);
						}
					?></caption>
					<thead>
						<tr>
							<th id="cat"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Catégorie'); ?></a></th>
							<?php if (! $marketplace) { ?><th id="cat-click"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Clics'); ?></a></th><?php } ?>
							<?php if (! $marketplace) { ?><th id="cat-cost"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Coût'); ?></a></th><?php } ?>
							<th id="cat-sales"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>">Ventes</a></th>
							<?php if (! $marketplace) { ?><th id="cat-transfo" title="<?php print _('Taux de transformation'); ?>"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Transfo.'); ?></a></th><?php } ?>
							<?php if (! $marketplace) { ?><th id="cat-cost-sales"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Coût/Vente'); ?></a></th><?php } ?>
							<th id="cat-ca" title="<?php print _('Chiffre d\'affaires Hors Taxes'); ?>"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('CA HT'); ?></a></th>
							<th id="cat-ca-ttc" title="<?php print _('Chiffre d\'affaires Toutes Taxes Comprises'); ?>"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('CA TTC'); ?></a></th>
							<th id="cat-margin" title="<?php print _('Marge'); ?>"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('Marge'); ?></a></th>
							<th id="cat-roi" title="<?php print _('Retour sur investissement'); ?>"><a href="index.php?ctr=<?php print $_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : ''); ?>"><?php print _('ROI'); ?></a></th>
						</tr>
					</thead>
					<tbody><?php

						$use_title = false;
						$rcat = prd_categories_get( 0, false, $_GET['parent'] );
						if( $_GET['parent']>0 && (!$rcat || !ria_mysql_num_rows($rcat)) ){
							$rcat = prd_categories_get( $_GET['parent'] );
							$use_title = true;
						}

						if( !$rcat || !ria_mysql_num_rows($rcat) ){
							print '	<tr>
										<td colspan="' . (11-$marketplace) . '">'._('Aucune statistique n\'est disponible.').'</td>
									</tr>';
						} else {
							// Pour chaque catégorie de produit enfant, affiche les indicateurs de performance associés
							while( $cat = ria_mysql_fetch_array($rcat) ){
								$stats = stats_comparators_get( $_GET['ctr'], $cat['id'], 0, $date1, $date2 );
								$roi = number_format($stats['roi'], 2, ',', ' ');
								$title = $cat['title'];
								if( !$use_title ){
									$title = '<a onclick="return updateStatsCtr('.$cat['id'].');" href="/admin/comparators/stats/index.php?ctr='.$_GET['ctr'].'&amp;parent='.$cat['id'].'">'.$cat['title'].'</a>';
								}

								print '	<tr class="right">
											<td headers="cat" class="align-left">'.view_cat_is_sync($cat).' '.$title.'</td>';
								if (! $marketplace) print '		<td headers="cat-click">'.ria_number_format($stats['clicks']).'</td>';
								if (! $marketplace) print '		<td headers="cat-cost">'.ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2).'</td>';
								print '		<td headers="cat-sales">'.ria_number_format($stats['sales']).'</td>';
								if (! $marketplace) print '		<td headers="cat-transfo">'.ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2).'</td>';
								if (! $marketplace) print '		<td headers="cat-cost-sales">'.ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2).'</td>';
								print '		<td headers="cat-ca">'.ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2).'</td>
											<td headers="cat-ca-ttc">'.ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2).'</td>
											<td headers="cat-margin">'.ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2).'</td>
											<td '.( $roi>0 ? 'class="positive"' : '' ).' headers="cat-roi">'.$roi.' %</td>
										</tr>';
							}
						}

					?></tbody>
					<tfoot><?php
						if( !is_array($stats) || !sizeof($stats) ){
							print '<tr><td colspan="' . (11-$marketplace) . '"></td></tr>';
						} else {
							$roi = number_format($stats['roi'], 2, ',', ' ');

							print '	<tr class="right bold">
										<td headers="cat" class="align-left">'._('Totaux').' * :</td>';
							if (! $marketplace) print '		<td headers="cat-click">'.ria_number_format($stats['clicks']).'</td>';
							if (! $marketplace) print '		<td headers="cat-cost">'.ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2).'</td>';
							print '		<td headers="cat-sales">'.ria_number_format($stats['sales']).'</td>';
							if (! $marketplace) print '		<td headers="cat-transfo">'.ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2).'</td>';
							if (! $marketplace) print '		<td headers="cat-cost-sales">'.ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2).'</td>';
							print '		<td headers="cat-ca">'.ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="cat-ca-ttc">'.ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="cat-margin">'.ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2).'</td>
										<td '.( $roi>0 ? 'class="positive"' : '' ).' headers="cat-roi">'.$roi.' %</td>
									</tr>';
						}
					?></tfoot>
				</table>

				<?php
					if( $_GET['parent']>0 ){
						$rprd = prd_products_get_simple( 0, '', false, $_GET['parent'], false, false, false, false, array('childs'=>true) );
						if( $rprd && ria_mysql_num_rows($rprd) ){
							$url = '/admin/comparators/stats/index.php?ctr='.$_GET['ctr'].( $_GET['parent']>0 ? '&amp;parent='.$_GET['parent'] : '');
				?>
				<table id="tb-ctr-prd" class="tablesorter checklist ctr-stats">
					<caption><?php print _('Produits'); ?></caption>
					<thead>
						<tr>
							<th id="prd-check" class="align-center"><input type="checkbox" name="check-all" id="check-all" value="" /></th>
							<th id="prd"><?php print _('Désignation'); ?></th>
							<?php
								if( !$marketplace ) print '<th id="prd-click"><a href="#">Clics</a></th>';
								if( !$marketplace ) print '<th id="prd-cost"><a href="#">Coût</a></th>'; ?>
							<th id="prd-sales"><a href="#"><?php print _('Ventes'); ?></a></th>
							<?php
								if( !$marketplace ) print '<th id="prd-transfo" title="Taux de transformation"><a href="#">Transfo.</a></th>';
								if( !$marketplace ) print '<th id="prd-cost-sales"><a href="#">Coût/Vente</a></th>'; ?>
							<th id="prd-ca" title="<?php print _('Chiffre d\'affaires Hors Taxes'); ?>"><a href="#"><?php print _('CA HT'); ?></a></th>
							<th id="prd-ca-ttc" title="<?php print _('Chiffre d\'affaires Toutes Taxes Comprises'); ?>"><a href="#"><?php print _('CA TTC'); ?></a></th>
							<th id="prd-margin"><a href="#"><?php print _('Marge'); ?></a></th>
							<th id="prd-roi" title="<?php print _('Retour sur investissement'); ?>"><a href="#"><?php print _('ROI'); ?></a></th>
							<th id="prd-export"><a href="#"><?php print _('Exporté'); ?></a></th>
						</tr>
					</thead>
					<tfoot><?php
						$stats = stats_comparators_get( $_GET['ctr'], $_GET['parent'], 0, $date1, $date2, false );
						if( !is_array($stats) || !sizeof($stats) ){
							print '<tr><td colspan="' . (11-$marketplace) . '"></td></tr>';
						} else {
							$roi = number_format($stats['roi'], 2, ',', ' ');
							$tchecked = $marketplace ? _('Activer l\'exportation de ces produits vers la place de marché.') : _('Activer l\'exportation de ces produits vers le comparateur de prix.');
							$tunchecked = $marketplace ? _('Désactiver l\'exportation de ces produits vers la place de marché.') : _('Désactiver l\'exportation de ces produits vers le comparateur de prix.');

							print '	<tr class="right bold">
										<td colspan="2" headers="prd" class="align-left">
											<input type="submit" name="checked" id="checked" value="'._('Exporter').'" title="'.$tchecked.'" />
											<input type="submit" name="unchecked" id="unchecked" value="'._('Ne pas exporter').'" title="'.$tunchecked.'" />
										</td>';
							if( !$marketplace ) print '		<td headers="prd-click">'._('Totaux').' : '.ria_number_format($stats['clicks']).'</td>';
							if( !$marketplace ) print '		<td headers="prd-cost">'.ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2).'</td>';
							print '		<td headers="prd-sales">'.( $marketplace ? ''._('Totaux').' :' : '' ).' '.ria_number_format($stats['sales']).'</td>';
							if( !$marketplace ) print '		<td headers="prd-transfo">'.ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2).'</td>';
							if( !$marketplace ) print '		<td headers="prd-cost-sales">'.ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2).'</td>';
							print '		<td headers="prd-ca">'.ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="prd-ca-ttc">'.ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="prd-margin">'.ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2).'</td>
										<td '.( $roi>0 ? 'class="positive"' : '' ).' headers="prd-roi">'.$roi.' %</td>
										<td headers="prd-export">&nbsp;</td>
									</tr>';
						}
					?></tfoot>
					<tbody><?php

						if( !$rprd || !ria_mysql_num_rows($rprd) ){
							print '	<tr>
										<td colspan="' . (11-$marketplace) . '">'._('Aucune statistique n\'est disponible.').'</td>
									</tr>';
						} else {
							while( $prd = ria_mysql_fetch_array($rprd) ){
								$stats = stats_comparators_get( $_GET['ctr'], 0, $prd['id'], $date1, $date2 );
								$roi = number_format($stats['roi'], 2, ',', ' ');
								$title = view_prd_is_sync($prd).' <a target="_blank" href="/admin/catalog/product.php?cat='.$_GET['parent'].'&amp;prd='.$prd['id'].'">'.htmlspecialchars( $prd['title'] ).'</a>';
								$export = ctr_catalogs_is_publish( $_GET['ctr'], $prd['id'] );

								print '	<tr class="right">
											<td headers="prd-check" class="align-center"><input class="check-all-prd" type="checkbox" name="check-all[]" id="check-all-'.$prd['id'].'" value="'.$prd['id'].'" /></td>
											<td headers="prd" class="align-left">'.htmlspecialchars( $title ).'</td>';
								if( !$marketplace ) print '		<td headers="prd-click">'.ria_number_format($stats['clicks']).'</td>';
								if( !$marketplace ) print '		<td headers="prd-cost">'.ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2).'</td>';
								print '		<td headers="prd-sales">'.ria_number_format($stats['sales']).'</td>';
								if( !$marketplace ) print '		<td headers="prd-transfo">'.ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2).'</td>';
								if( !$marketplace ) print '		<td headers="prd-cost-sales">'.ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2).'</td>';
								print '		<td headers="prd-ca">'.ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2).'</td>
											<td headers="prd-ca-ttc">'.ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2).'</td>
											<td headers="prd-margin">'.ria_number_format($stats['margin'], NumberFormatter::CURRENCY, 2).'</td>
											<td '.( $roi>0 ? 'class="positive"' : '' ).' headers="prd-roi">'.$roi.' %</td>
											<td headers="prd-export">';
								if( $export ){
									print '		<a onclick="return unactivatedProduct( $(this), '.$_GET['ctr'].', true );" name="prd-'.$prd['id'].'" href="#" class="active">'._('Oui').'</a>';
								} else {
									print '		<a onclick="return activatedProduct( $(this), '.$_GET['ctr'].', true );" name="prd-'.$prd['id'].'" href="#" class="cancel">'._('Non').'</a>';
								}
								print 		'</td>
										</tr>';
							}
						}

					?></tbody>
				</table>
				<?php
						}
					}
				?>
				<span class="notice">* <?php print _('Des différences peuvent apparaître entre le total et la somme de chaque ligne.');
				print _(' Ces différences sont provoquées par les produits classés dans plusieurs catégories.'); ?></span>
			</form>
		<?php
	}
?>

<script><!--
	<?php view_date_initialized($volume, '/admin/comparators/stats/index.php', false, array()); ?>
--></script>

<?php

require_once('admin/skin/footer.inc.php');
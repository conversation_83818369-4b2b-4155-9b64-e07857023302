<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('products.inc.php');
	require_once('comparators.inc.php');
	require_once('stats.inc.php');
	require_once('view.admin.inc.php');
	
	// Chargement du produit
	if( !isset($_GET['prd']) || !($product = ria_mysql_fetch_array( prd_products_get_simple( $_GET['prd'] ) )) ){
		print _("Une erreur s'est produite lors du chargement des informations sur le produit.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler ce problème");
		exit;
	}
	
	// Chargement du comparateur
	if( !isset($_GET['ctr']) || !($ctr = ria_mysql_fetch_array( ctr_comparators_get( $_GET['ctr'], true, false, null) )) ){
		print _("Une erreur s'est produite lors du chargement des informations sur le comparateur de prix.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler ce problème");
		exit;
	}
	
	$filter = isset($_GET['filter']) && trim($_GET['filter'])!='' ? $_GET['filter'] : false;
	$marketplace = $ctr['marketplace'];
	$url = '/admin/comparators/stats/js_product.php?prd='.$_GET['prd'].'&ctr='.$_GET['ctr'];
	
	if( $marketplace ){
		$url .= '&marketplace=1';
	}else{
		$url .= '&marketplace=0';
	}
	
	if( isset($_GET['tab']) ){
		$url .= '&tab=1';
	}
	
	if( isset($_GET['cat']) ){
		$url .= '&cat='.$_GET['cat'];
	}
	
	if( trim($filter)=='' ){
		if( isset($_GET['save-choose'], $_GET['category']) ){
			if( ctr_catalogs_update_categorie($_GET['ctr'], $_GET['prd'], $_GET['category']) ){
				header( 'Location: '.$url.'&save-choose=1' );
				exit;
			}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la nouvelle catégorie d'exportation du produit.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	} else {
		unset($_GET['save-choose']);
	}
	
	if( isset($_GET['save-choose']) || isset($_GET['cancel']) ){
		if( !isset($_GET['tab']) ){
			unset( $_GET['cat'] );
		}
	}
	
	// Categorie liée
	$is_disabled = false;
	$cat_id = ctr_catalogs_get_categorie($ctr['id'], $_GET['prd']);
	if( ctr_categories_get_disabled($ctr['id'], $cat_id) ){
		$cat_id = 0;
	}
	
	$endurl = '';
	// Description du produit
	$desc = $product['desc'].' - '.html_strip_tags( $product['desc-long'] );
	$desc = strlen( $desc )>255 ? substr( $desc, 0, 253 ).'...' : $desc;
	
	if( !isset($_GET['cat']) ){

		// Tarif du produit
		$price_ht = $price_ttc = 0;
		$before_ht = $before_ttc = 0;
		
		$id_prd_price = $product['id'];
		if( prd_products_is_parent($product['id']) ){
			$rchild = prd_childs_get( $product['id'], false, false, array('price'=>'asc') );
			if( $rchild && ria_mysql_num_rows($rchild) ){
				$id_prd_price = ria_mysql_result( $rchild, 0, 'id' );
			}
		}
		
		$rprc = prd_products_get_price( $id_prd_price );
		if( $rprc && ria_mysql_num_rows($rprc) ){
			$prc = ria_mysql_fetch_array( $rprc );
			$price_ht = $prc['price_ht'];
			$price_ttc = $prc['price_ttc'];
		}
		
		// promotion sur le produit
		$promo = prc_promotions_get( $id_prd_price );
		if( is_array($promo) && sizeof($promo) ){
			$before_ht = $price_ht;
			$before_ttc = $price_ttc;
			$price_ht = $promo['price_ht'];
			$price_ttc = $promo['price_ttc'];
		}

		if( $cat_id<=0 ){
			
			// récupère les catégories publiés du produit
			$rcly = prd_products_categories_get( $_GET['prd'], true );
			
			if( $rcly && ria_mysql_num_rows($rcly) ){
			
				while( $cly = ria_mysql_fetch_array($rcly) ){
					// vérifie si la catégorie est liée à une catégorie comparteur
					$tmp = ctr_prd_categories_get( $ctr['id'], $cly['cat'] );
					if( $tmp && ria_mysql_num_rows($tmp) ){
						$cat_id = ria_mysql_result( $tmp, 0, 'id' );
						$is_disabled = ria_mysql_result( $tmp, 0, 'is_disabled' );
						break;
					}
					
				}
			}
		}elseif( ctr_categories_get_disabled($ctr['id'], $cat_id) ){
			$is_disabled = true;
		}
	} else {
		// identifiant d'une famille parente
		$_GET['cat'] = isset($_GET['cat'], $_GET['ctr']) && ctr_categories_exists($_GET['cat'], $_GET['ctr']) && !ctr_categories_get_disabled($_GET['ctr'], $_GET['cat']) ? $_GET['cat'] : 0;
		
		$parent = '';
		if( $_GET['cat']>0 && $filter==false ){
			$rparent = ctr_categories_get( $_GET['ctr'], $_GET['cat'] );
			if( $rparent && ria_mysql_num_rows($rparent) ){
				$parent = ria_mysql_fetch_array( $rparent );
			}
		}
	}
	
	// statistique du produit dans le comparateur
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	$stats = stats_comparators_get( $_GET['ctr'], 0, $product['id'], $date1, $date2 );

	define('ADMIN_PAGE_TITLE', _('Informations produit') . ' - ' . _('Comparateurs'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>
	<div id="popupctr">
		<form action="js_product.php?prd=<?php print $_GET['prd']; ?>&amp;ctr=<?php print $_GET['ctr']; ?>">
			<input type="hidden" name="prd" id="prd" value="<?php print $_GET['prd']; ?>" />
			<input type="hidden" name="ctr" id="ctr" value="<?php print $_GET['ctr']; ?>" />
			<?php
				if( isset($_GET['cat']) ){
					print '
	                	<input type="hidden" name="cat" value="'.$_GET['cat'].'"  />
					';
				}
				if( isset($_GET['tab']) ){
					print '
	                	<input type="hidden" name="tab" value="'.$_GET['tab'].'"  />
					';
				}
			?>
			<?php if( !isset($_GET['cat']) ){ ?>
				<div class="media"><?php
					$thumbs = $config['img_sizes']['high'];
					$srcimg = $config['img_url'].'/'.$thumbs['dir'].'/'.( $product['img_id'] ? $product['img_id'] : 'default' ).'.'.$thumbs['format'];
					print '<img src="'.$srcimg.'" width="'.$thumbs['width'].'" height="'.$thumbs['height'].'" alt="'.$product['title'].'" />';
				?></div>
				<div class="infos">
					<table id="tbl-infoprd">
						<caption><?php print _('Informations générales'); ?></caption>
						<tbody>
							<tr>
								<th><?php print _('Référence :'); ?></th>
								<td><?php print view_prd_is_sync($product).' <a target="_blank" href="/admin/catalog/product.php?cat=0&amp;prd='.$_GET['prd'].'">'.htmlspecialchars( $product['ref'] ).'</a>'; ?></td>
							</tr>
							<tr>
								<th><?php print _('Désignation :'); ?></th>
								<td><?php print htmlspecialchars( $product['title'] ); ?></td>
							</tr>
							<tr>
								<th><?php print _('Description :'); ?></th>
								<td><?php print htmlspecialchars( $desc ); ?></td>
							</tr>
							<tr>
								<th><?php print _('Tarif :'); ?></th>
								<td><?php
									printf( $id_prd_price!=$product['id'] ? _('À partir de %d € HT') : _('%d € HT'), number_format( $price_ht, 2, ',', ' ' ) );
									if( $before_ht>0 ){
										printf( _(' au lieu de %d € HT'), number_format( $before_ht, 2, ',', ' ' ) );
									}
									print ' / '.sprintf( $id_prd_price!=$product['id'] ? _('À partir de %d € TTC') : _('%d € TTC'), number_format( $price_ttc, 2, ',', ' ' ) );
									if( $before_ttc>0 ){
										printf( _(' au lieu de %d € TTC'), number_format( $before_ttc, 2, ',', ' ' ) );
									}
								?></td>
							</tr>
							<tr>
								<th class="last">
									<?php print _('Statistiques :'); ?> 
									<br /><span class="period"><?php 
										if( !isset($_SESSION['datepicker_period']) ){
											print '(du '.date('d/m/Y').')';
										} else {
											print ' (du '.$_SESSION['datepicker_date1'];
											if( $_SESSION['datepicker_date2']!=$_SESSION['datepicker_date1'] )
												print ' au '.$_SESSION['datepicker_date2'];
											print ')';
										}
									?></span>
								</th>
								<td class="last"><?php 
									$stats['clicks'] = isset($stats['clicks']) ? number_format($stats['clicks'], 0, ',', ' ') : 0;
									$stats['cost'] = isset($stats['cost']) ? number_format($stats['cost'], 2, ',', ' ') : '0,00';
									$stats['sales'] = isset($stats['sales']) ? number_format($stats['sales'], 0, ',', ' ') : 0;
									$stats['cost-sales'] = isset($stats['cost-sales']) ? number_format($stats['cost-sales'], 2, ',', ' ') : '0,00';
									$stats['ca'] = isset($stats['ca']) ? number_format($stats['ca'], 2, ',', ' ') : '0,00';
									$stats['ca-ttc'] = isset($stats['ca-ttc']) ? number_format($stats['ca-ttc'], 2, ',', ' ') : '0,00';
									
									$stats['margin'] = isset($stats['margin']) ? number_format($stats['margin'], 2, ',', ' ') : '0,00';
									$stats['transfo'] = isset($stats['transfo']) ? number_format($stats['transfo'], 2, ',', ' ') : '0,00';
										
									print '	<ul>';
									if (! $marketplace) print '		<li>Clics : '.$stats['clicks'].'</li>';
									if (! $marketplace) print '		<li>Coût : '.$stats['cost'].' €</li>';
									print '		<li>'._('Vente(s)').' : '.$stats['sales'].'</li>';
									if (! $marketplace) print '		<li>'._('Coût par vente').' : '.$stats['cost-sales'].' €</li>';
									print '		<li>'._('Chiffre d\'affaires généré').' : '.$stats['ca'].' € HT / '.$stats['ca-ttc'].' € TTC</li>
												<li>'._('Taux de transformation').' : '.$stats['transfo'].'%</li>
												<li>'._('Marge').' : '.$stats['margin'].' €</li>
												<li>'._('ROI').' : <span class="'.($stats['roi'] > 0 ? 'val-positif':'val-negatif').'">'.number_format($stats['roi'], 2, ',', ' ').'%</span></li>
											</ul>';
								?></td>
							</tr>
						</tbody>
						<tfoot><tr><th colspan="2"></th></tr></tfoot>
					</table>
				</div>
				
				<div class="clear"></div>
				<div class="export">
					<?php 
						require_once('view.ctr.inc.php');
						view_prd_export( $_GET['ctr'], $ctr['name'], $_GET['prd'], $cat_id );
					?>
				</div>
			<?php } else { // !isset($_GET['cat']) ?>
					<div class="margin-bottom-10"><?php 
					
					// Bouton retour
					if( $parent!='' || isset($_GET['filter']) ){ ?>
						<a href="/admin/comparators/stats/js_product.php?<?php print 'ctr='.$ctr['id'].'&amp;prd='.$_GET['prd'].( $parent!='' ? '&amp;cat='.$parent['parent'] : '&amp;cat='.$_GET['cat'] ).(isset($_GET['tab']) ? '&amp;tab=1' : ''); ?>" class="float-left button">
							<?php print _('Retour'); ?>
						</a><?php
					} ?>
						<!-- Recherche -->
						<div class="align-right">
							<label for="filter"><?php print _('Rechercher une catégorie :'); ?></label>
							<input type="text" name="filter" id="filter" value="" />
							<input type="submit" name="search" value="<?php print _('Chercher'); ?>" />
						</div>
					</div>
					<?php 
					// Si on a effectué une recherche
					if( isset($_GET['filter']) && trim($_GET['filter'])!='' ){
						print '<h3>'._('Résultats de la recherche :').' '.($_GET['filter']).'</h3>';
					} ?>
					<table class="checklist" id="choose-cat-ctr">
						<caption>
							<?php if( $parent!='' ){ 
								print htmlspecialchars( $parent['name'] );
							} else {
								printf( $marketplace ? _('Choisissez une famille de la place de marché %s') : _('Choisissez une famille du comparateur %s'), htmlspecialchars( $ctr['name'] ) ); ?>
							<?php } ?>
						</caption>
						<thead>
							<tr>
								<th id="cat-name"><?php print _('Désignation'); ?></th>
							</tr>
						</thead>
						<tbody><?php
							$pselected = false;
							if( is_array($parent) && sizeof($parent) ){
								$pselected = in_array($ctr['id'], $config['ctr_free']) || $parent['cpc']>0 || $marketplace;
								$pselected = $pselected && !in_array($ctr['id'], $config['ctr_last_level']);
							}
							
							$rcat = ctr_categories_get( $ctr['id'], 0, $filter===false ? $_GET['cat'] : '', 0, false, $filter );
							if( !$rcat || !ria_mysql_num_rows($rcat) ){
								if( $pselected && $_GET['cat']>0 ){
									print '	<tr>
												<td>
													<input checked="checked" type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
													<label for="category">'._('Sélectionner cette famille').'</label>
												</td>
											</tr>';
								}else{
									print '<tr><td>'._('Aucune famille n\'a été trouvée. Assurez-vous qu’il s\'agisse d’une famille du comparateur Google Shopping.').'</td></tr>';
								}
							} else {
								$url = '/admin/comparators/stats/js_product.php?ctr='.$ctr['id'].'&amp;marketplace=' . ($marketplace ? 1 : 0);
								if( isset($_GET['prd']) ){
									$url .= '&amp;prd='.$_GET['prd'];
								}
								if( isset($_GET['tab']) ){
									$url .= '&amp;tab=1';
								}
								
								while( $cat = ria_mysql_fetch_array($rcat) ){
									$selected = in_array($ctr['id'], $config['ctr_free']) || $cat['cpc']>0 || $marketplace;
									$havechilds = ctr_categories_have_childs($ctr['id'], $cat['id']);
									
									if( !$selected && !$havechilds ){
										continue;
									}
									
									$selected = $selected && (!in_array($ctr['id'], $config['ctr_last_level']) || !$havechilds);
									
									$attr = '';
									if( isset($_GET['pre_select']) && $_GET['pre_select']==$cat['id'] ){
										$attr .= ' checked="checked"';
									}
									
									if( !$selected ){
										$attr .= ' disabled="disabled"';
										$attr .= ' title="'._('Vous ne pouvez choisir cette catégorie directement. Veuillez sélectionner une sous-catégorie.').'"';
									}
									
									print '	<tr>
												<td>
													<input'.$attr.' type="radio" name="category" id="category-'.$cat['id'].'" value="'.$cat['id'].'" />';
									if( $havechilds ){
										print '		<a href="'.$url.'&amp;cat='.$cat['id'].'">'.$cat['name'].'</a>';
									}else{
										print '		<label for="category-'.$cat['id'].'">'.$cat['name'].'</label>';
									}
									print '		</td>
											</tr>';
								}
								
								if( $pselected && $_GET['cat']>0 ){
									print '	<tr>
												<td>
													<input type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
													<label for="category">'._('Sélectionner cette famille').'</label>
												</td>
											</tr>';
								}
							}
						?></tbody>
						<tfoot class="js-input-disabled none">
							<tr>
								<td>
									<input class="save" type="submit" name="save-choose" id="save-choose" value="<?php print _('Choisir'); ?>" />
								</td>
							</tr>
						</tfoot>
					</table>
			<?php } ?>
		</form>
	</div>
	<script>
		<?php 
			if( $filter==false && isset($_GET['tab']) && (isset($_GET['cancel']) || isset($_GET['save-choose'])) ){ 
				$export  = '<span class="ctr_cat">';
				$export .= ctr_categories_export( $_GET['ctr'], $cat_id, 2, '>' );
				$export .= '</span>';
				$export .= '<input type="hidden" name="del-ctr-family" value="'.$_GET['ctr'].'" />';
				$export .= '<input type="image" formaction="/admin/catalog/product.php?cat=0&amp;prd='.$_GET['prd'].'&amp;ctr='.$_GET['ctr'].'" name="del-link-family" src="../images/del-cat.svg" title="'._('Supprimer le lien').'" class="icon-del-cat" />';

				print 'parent.hidePopupPrd('.$_GET['ctr'].', \''.( trim($export) ? addslashes($export) : '' ).'\', '.( is_numeric($cat_id) && $cat_id ? $cat_id : 0 ).');';
			}
		?>
		$( document ).ready(function() {
			$('.js-input-disabled.none').parents('form').each(function(){
				var form = $(this);
				if( form.find('tbody input[name="category"]:checked').length ){
					// form.find('.js-input-disabled').prop("disabled", false);
					form.find('.js-input-disabled').removeClass('none');
				}else{
					// form.find('.js-input-disabled').prop("disabled", true);
					form.find('.js-input-disabled').addClass('none');
				}

				$(this).find('input[name="category"]').change(function(){
					if (form.find('tbody input[name="category"]:checked').length ){
						// form.find('.js-input-disabled').prop("disabled", false);
						form.find('.js-input-disabled').removeClass('none');
					}else{
						// form.find('.js-input-disabled').prop("disabled", true);
						form.find('.js-input-disabled').addClass('none');
					}
				});
			});
		
		});
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
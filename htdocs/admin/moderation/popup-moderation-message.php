<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

    $is_ajax = false;
    if (!isset($_GET['msg_id'])){
        $error = _('Il manque des paramètres');
    }

    if (!isset($error)) {
        $r_msg = messages_get( 0 , '', 0, $_GET['msg_id'] );
        if (!$r_msg || !ria_mysql_num_rows($r_msg)){
            $error = _('Une erreur est survenue lors de la récupération des informations sur le message.')."<br />"._('Veuillez réessayer ou prendre contact pour signaler l\'erreur');
        } else {
            $msg = ria_mysql_fetch_assoc($r_msg);
            $author_name = htmlspecialchars($msg['firstname'] != '' ? $msg['firstname'] : '') 
            . htmlspecialchars($msg['lastname'] != '' ? ' '.$msg['lastname'] : '') 
            . htmlspecialchars($msg['society'] != '' ? ' '.$msg['society'] : '') 
            . ' ' . htmlspecialchars('<' . $msg['email'] . '>');
        }
    }


    //si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Désapprobation de message'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
        print '<div class="error">'.nl2br($error).'</div>';
    } elseif(!isset($error) && ria_mysql_num_rows($r_msg)) {
        print '<div class="notice">'.sprintf(_('Vous venez de désapprouver le message de %s.'), $author_name)."<br />"._('Vous pouvez détailler ici les raisons de cette décision.')."<br />"._('Ce commentaire ne sera visible que dans la modération de RiaShop.').'</div>';
    }
?>

<form method="post" action="popup-moderation-message.php">
    <textarea id="moderation-comment" rows="3" cols="20" placeholder="<?php print _('Détaillez ici les raison de votre désapprobation...'); ?>" maxlength="150"></textarea>
    <input type="submit" name="save_moderation" value="<?php print _('Enregistrer'); ?>" />
    <sub><?php print _('150 caractères max.'); ?></sub>
</form>

<?php 
    if( !$is_ajax ){
?>
    <script><!--
        var msg_id = <?php print $_GET['msg_id']; ?>;
        $(document).ready(function(){
            $('.page_timer').hide();
            $('input[name=save_moderation]').click(function(e){
                e.preventDefault();
                var comment = $('#moderation-comment').val();

                window.parent.moderateMessage( msg_id, false, comment );

                window.parent.hidePopup();
            })
        });
    --></script>

<?php
        require_once('admin/skin/footer.inc.php'); 
    }
?>
<?php

	/**	\file moderation.php
	 *	Cette page affiche la liste des messages reçus par l'intermédiaire du site et permet leur modération.
	 */

	require_once( 'messages.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	$have_usr = !isset($_GET['have-usr']) || trim($_GET['have-usr'])=='' || $_GET['have-usr']>1 || $_GET['have-usr']<0 ? null : $_GET['have-usr'];
	$have_rep = !isset($_GET['have-rep']) || trim($_GET['have-rep'])=='' || $_GET['have-rep']>1 || $_GET['have-rep']<0 ? null : $_GET['have-rep'];

	// Tri
	$sort = isset($_POST['sort']) && trim($_POST['sort'])!='' ? $_POST['sort'] : 'cnt_date_created';
	$dir = isset($_POST['dir']) && in_array($_POST['dir'], array('asc', 'desc')) ? $_POST['dir'] : 'desc';

	// Si le type de message n'est pas reconnu, on retourne sur la page d'accueil de la section Modération
	if( !isset($_GET['type']) || !gu_messages_types_exists($_GET['type']) ){
		header('Location: /admin/moderation/index.php');
		exit;
	}

	$type = ria_mysql_fetch_assoc( gu_messages_types_get( 0, $_GET['type']) );
	$str_id = isset($_GET['str']) && is_numeric($_GET['str']) && $_GET['str'] ? $_GET['str'] : 0;
	$rvw_id = isset($_GET['rvw']) && is_numeric($_GET['rvw']) && $_GET['rvw'] ? $_GET['rvw'] : 0;

	if (!$rvw_id) {
		$rvw_id = isset($_GET['cnt']) && is_numeric($_GET['cnt']) && $_GET['cnt'] ? $_GET['cnt'] : 0;
	}

	// Filtre de site
	$wst_id = 0;
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}
	$wst_id = isset($_GET['wst_id']) ? $_GET['wst_id'] : $wst_id;
	$wst_id = is_numeric($wst_id) && $wst_id>0 ? $wst_id : 0;

	// Bouton Exporter
	if( isset($_POST['export']) ){
		$date_start = isset($_POST['date-start']) && trim($_POST['date-start'])!='' ? $_POST['date-start'] : false;
		$date_end = isset($_POST['date-end']) && trim($_POST['date-end'])!='' ? $_POST['date-end'] : false;
		$rmsg = messages_get( 0, $type['code'], 0, $rvw_id, 0, false, true, false, 0, 0, $date_start, $date_end, $have_rep, $have_usr, array('cnt_date_created'=>$dir), false, null, false, '', $wst_id, $str_id );

		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export_moderation.php');
		exit;
	}

	// Construit le titre de la page
	$title_page = 'Messages de type "'.htmlspecialchars($type['name-pl']).'"';
	if( $str_id ){
		$name_store = dlv_stores_get_name( $str_id );
		if( trim($name_store)!='' ){
			$title_page .= ' pour le magasin "'.htmlspecialchars($name_store).'"';
		}
	}

	// Recherche les messages
	$rmsg = messages_get( 0, $type['code'], 0, $rvw_id, 0, false, true, false, 0, 0, false, false, $have_rep, $have_usr, array('cnt_date_created'=>$dir), false, null, false, '', $wst_id, $str_id );
	$msg_count = ria_mysql_num_rows( $rmsg );

	// Prépare la pagination
	$page = $pages = 0;
	if( $rmsg ){
		$pages = ceil(ria_mysql_num_rows($rmsg) / 25);

		if( !isset($_GET['page']) ){
			$page = 1;
		}else{
			if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
				$page = 1;
			}elseif( $_GET['page']>$pages ){
				$page = $pages;
			}else{
				$page = $_GET['page'];
			}
		}

		$pages = $pages>0 ? $pages : 1;
	}

	$ar_fld = array();
	$ar_msg_id = array();

	if( $rmsg ){
		while( $msg = ria_mysql_fetch_assoc($rmsg) ){
			$ar_msg_id[] = $msg['id'];
		}

		$ar_fld = gu_messages_get_fields( $ar_msg_id );
	}

	if( $rmsg && ria_mysql_num_rows($rmsg) ){
		ria_mysql_data_seek( $rmsg, 0 );
	}

	$messages = false;
	while( $msg = ria_mysql_fetch_assoc($rmsg) ){
		$messages[]=$msg;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Modération'), '/admin/moderation/index.php' )
		->push( $type['name-pl'] );

	// Titre de la page
	define('ADMIN_PAGE_TITLE', $type['name-pl'].' - ' . _('Modération'));
	require_once('admin/skin/header.inc.php');
?>
		<h2><?php print htmlspecialchars( $type['name-pl'] ); ?> (<?php print ria_number_format($msg_count) ?>)</h2>

		<input type="hidden" name="hd-page" id="hd-page" value="<?php print $page; ?>" />
		<input type="hidden" name="hd-wst" id="hd-wst" value="<?php print $wst_id; ?>" />
		<input type="hidden" name="hd-type" id="hd-type" value="<?php print $_GET['type']; ?>" />
		<input type="hidden" name="hd-reponse" id="hd-reponse" value="<?php print $have_rep; ?>" />
		<input type="hidden" name="hd-contact" id="hd-contact" value="<?php print $have_usr; ?>" />
		<input type="hidden" name="hd-sort" id="hd-sort" value="<?php print $dir; ?>" />
		<input type="hidden" name="hd-str" id="hd-str" value="<?php print $str_id; ?>" />
		<input type="hidden" name="rvw" id="rvw" value="<?php print isset($_GET['rvw']) ? $_GET['rvw'] : 0; ?>" />
		<div class="moderation-menu">
			<?php
				print view_websites_selector( $wst_id, true, 'riapicker', true );
			?>
			<div id="ria_reponse" class="riapicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Gestion des réponses"); ?></span>
						<br /><span class="view"><?php
							if( $have_rep === null ){
								print _('Tous les messages');
							}elseif( $have_rep == '1' ){
								print _('Messages ayant reçu une réponse');
							}else{
								print _('Messages en attente de réponse');
							}
						?></span>
					</div>
					<a class="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="rep-all"><?php echo _("Tous les messages"); ?></a>
					<a name="rep-nw"><?php echo _("Messages en attente de réponse"); ?></a>
					<a name="rep-w"><?php echo _("Messages ayant reçu une réponse"); ?></a>
				</div>
			</div>

			<div id="ria_contacts" class="riapicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Gestion des messages"); ?></span>
						<br /><span class="view"><?php
							if( $have_usr === null ){
								print _('Tous les messages');
							}elseif( $have_usr == '1' ){
								print _('Contacts disposant d\'un compte client');
							}else{
								print _('Contacts ne disposant pas d\'un compte client');
							}
						?></span>
					</div>
					<a class="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="cnt-all"><?php echo _("Tous les messages"); ?></a>
					<a name="cnt-w"><?php echo _("Contacts disposant d'un compte client"); ?></a>
					<a name="cnt-nw"><?php echo _("Contacts ne disposant pas d'un compte client"); ?></a>
				</div>
			</div>

			<div id="ria_sort" class="riapicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _("Tri des messages"); ?></span>
						<br /><span class="view"><?php
							if( $dir == 'asc' ){
								print _('Trier par date de réception croissante');
							}else{
								print _('Trier par date de réception décroissante');
							}
						?></span>
					</div>
					<a class="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="dat-asc"><?php echo _("Trier par date de réception croissante"); ?></a>
					<a name="dat-desc"><?php echo _("Trier par date de réception décroissante"); ?></a>
				</div>
			</div>
		</div>

		<div id="export-excel">
			<div id="filter">
				<span class="bold"><?php echo _("Gestion de l'exportation :"); ?></span><br /><br />
				<form action="moderation.php?type=<?php print $_GET['type']; ?>" method="post">
					<label for="date-start"><?php echo _("Du"); ?></label>&nbsp;<input class="datepicker date" type="text" name="date-start" id="date-start" value="" />
					<label for="date-end"><?php echo _("Au"); ?></label>&nbsp;<input class="datepicker date" type="text" name="date-end" id="date-end" value="" /><br /><br />
					<label for="extension"><span class="bold"><?php echo _("Format du fichier :"); ?></span></label>
					<select name="extension" id="extension">
						<option value="xls"><?php echo _("Microsoft Excel 97 à 2003 (.xls)"); ?></option>
						<option value="xlsx"><?php echo _("Microsoft Excel 2007 à 2010 (.xlsx)"); ?></option>
					</select><br /><br />
					<?php
						$colonnes = array(
							'date'=>array('Date',18), 'firstname'=>array('Prénom',25), 'lastname'=>array('Nom',25), 'society'=>array('Société',25), 'phone'=>array('Téléphone',15), 'email'=>array('Email',30), 'subject'=>array('Sujet',40), 'body'=>array('Corps du message',45), 'email_to'=>array('Destinataire',40), 'email_cc'=>array('En copie',40), 'note'=>array('Note',10)
						);
						print '	<span class="bold">' . _('Informations :') . '</span> <a class="check-all-col">' . _("Cocher tout") . '</a> | <a class="uncheck-all-col">' . _("Décocher tout") . '</a>
								<div id="colonnes">';
							$i = 0;
							print '<div class="col-filter">';
							foreach( $colonnes as $k=>$col ){
								if( $i>1 && $i%3==0 )
									print '</div><div class="col-filter">';
								print '<div>
											<input type="hidden" name="col-width['.$k.']" id="col-widht'.$k.'" value="'.$col[1].'" />
											<input type="checkbox" class="col-filter" name="col-filter['.$k.']" id="col-filter'.$k.'" value="'.$col[0].'" checked="checked" /><label for="col-filter'.$k.'">'.$col[0].'</label>
										</div>';
								$i++;
							}
							print '	</div>
								</div>';

						$i=0; $j = 1;
						$fields_libs = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_MESSAGE );
						if( $fields_libs && ria_mysql_num_rows($fields_libs) ){
							print '	<div class="clear"></div>
									<span class="bold">' . _("Informations Complémentaires :") . ' </span><a class="check-all-fld">' . _("Cocher tout") . '</a> | <a class="uncheck-all-fld">' . _("Décocher tout") . '</a>
									<div id="fields">
										<div class="col-filter">';
							while( $fld = ria_mysql_fetch_assoc($fields_libs) ){
								if( is_array($ar_fld) && in_array($fld['id'], $ar_fld) ){
									if( $i>1 && $i%5==0 ){
										print '</div>';

										if( $j%5 == 0 ){
											print '<div class="clear"></div>';
										}

										print '<div class="col-filter">';
										$j++;
									}
									print '	<div><input type="checkbox" class="fld-filter" name="fld-filter['.$fld['id'].']" id="fld'.$fld['id'].'" value="'.$fld['name'].'" /><label for="fld'.$fld['id'].'">'.$fld['name'].'</label></div>';
									$i++;
								}
							}
							print '		</div>
									</div>';
						}

					?>
					<div class="clear"></div>
					<div>
						<input type="button" class="btn-export" name="close-export" id="close-export" value="<?php echo _("Fermer"); ?>" onclick="$('#filter').hide();" />
						<button type="submit" class="btn-export" name="export" value="execute" onclick="return $('#filter').hide();"><?php echo _("Exporter tous les messages"); ?></button>
					</div>
					<div class="clear"></div>
				</form>
			</div>
		</div>
<?php
	$file = str_replace(__DIR__.'/', '', __FILE__);
	require __DIR__.'/view_contact.php';
	require_once('admin/skin/footer.inc.php');
?>
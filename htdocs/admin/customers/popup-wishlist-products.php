<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	require_once('gu.bookmarks.inc.php');
	if( !isset($_GET['gwt'], $_GET['usr']) || !gu_wishlists_exists($_GET['gwt'], $_GET['usr']) ){
		$error = _("La liste personnalisée donnée en paramètre n'existe pas.");
	}

	// Ajout d'un produit passée en paramètre
	if( isset($_GET['add']) && is_numeric($_GET['add']) && $_GET['add']>0 ){
		if( !gu_wishlists_products_add($_GET['add'], $_GET['gwt']) ){
			$error = _("Une erreur inattendue s'est produite lors de l'ajout du produit.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}

		unset( $_GET['add'] );
	}

	// Suppression d'un ou plusieurs produits
	if( isset($_POST['delete']) ){
		if( isset($_POST['prd-del']) && is_array($_POST['prd-del']) && sizeof($_POST['prd-del']) ){
			foreach( $_POST['prd-del'] as $prd ){
				if( !gu_wishlists_products_del($_GET['gwt'], $prd) ){
					$error = _("Une erreur inattendue s'est produite lors de suppression des produits.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', 'value');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
	
	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}else{
?>
	<form action="popup-wishlist-products.php?gwt=<?php print $_GET['gwt']; ?>&amp;usr=<?php print $_GET['usr']; ?>" method="post">
		<p><?php print _('Vous trouverez ci-dessous la liste des produits contenus dans cette liste personnalisée.')?></p>
		<table class="checklist" cellspacing="0" cellpadding="0">
			<caption><?php print _('Liste des produits')?></caption>
			<col width="25" /><col width="*" />
			<thead>
				<tr>
					<th id="prd-del"><input type="checkbox" name="checkall" value="" /></th>
					<th id="prd-name"><?php print _('Désignation')?></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="2">
						<div style="float: left">
							<input class="checkbox" type="submit" name="delete" value="<?php print _('Supprimer')?>" />
						</div>

						<input type="submit" name="add-prd" id="add-prd" value="<?php print _('Ajouter un produit')?>" />
					</td>
				</tr>
			</tfoot>
			<tbody><?php
				$rprd = gu_wishlists_products_get( false, $_GET['gwt'] );
				if( !$rprd || !ria_mysql_num_rows($rprd) ){
					print '
						<tr>
							<td colspan="2">'._('Aucun produit n\'est présent dans cette liste personnalisée').'</td>
						</tr>
					';
				}else{
					while( $prd = ria_mysql_fetch_array($rprd) ){
						print '
							<tr>
								<td class="td-check-del" headers="prd-del">
									<input type="checkbox" name="prd-del[]" value="'.$prd['prd_id'].'" />
								</td>
								<td headers="prd-name">'.view_prd_is_sync($prd).' '.htmlspecialchars($prd['name']).'</td>
							</tr>
						';
					}
				}
			?></tbody>
		</table>
	</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
?>

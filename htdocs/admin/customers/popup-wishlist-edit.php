<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	require_once('gu.bookmarks.inc.php');
	if( !isset($_GET['usr']) || !gu_users_exists($_GET['usr']) ){
		$error_tab = _("Le compte client passé en paramètre est inconnu.");
	}

	$gwt_id = isset($_GET['gwt']) ? $_GET['gwt'] : 0;

	// Récupère les informations sur la wishlist, vide s'il s'agit d'une création
	$gwt = array( 'id'=>0, 'name'=>'', 'desc'=>'', 'publish'=>0 );
	if( $gwt_id>0 ){
		if( !gu_wishlists_exists($gwt_id, $_GET['usr']) ){
			$error_tab = _("La liste personnalisée donnée en paramètre n'existe pas.");
		}else{
			$gwt = ria_mysql_fetch_array( gu_wishlists_get($_GET['gwt']) );
		}
	}

	// Enregistre les informations sur la wishlist
	if( isset($_POST['save']) ){
		if( !isset($_POST['gwt-name'], $_POST['gwt-desc']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['gwt-name'])=='' ){
			$error = _("Veuillez renseigner une désignation pour cette liste personnalisée.");
		}else{
			if( $gwt['id']==0 ){
				if( !gu_wishlists_add($_GET['usr'], $_POST['gwt-name'], trim($_POST['gwt-desc'])!='' ? $_POST['gwt-desc'] : null, $_POST['gwt-publish'] ? true : false) ){
					$error = _("Une erreur inattendue s'est produite lors de la création de la liste personnalisée.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}else{
				if( !gu_wishlists_update($gwt['id'], $_POST['gwt-name'], trim($_POST['gwt-desc'])!='' ? $_POST['gwt-desc'] : null, $_POST['gwt-publish'] ? true : false) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour de la liste personnalisée.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}
	}

	// Supprime la wishlist
	if( isset($_POST['delete']) ){
		if( !gu_wishlists_del($gwt['id']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la liste personnalisée.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}

	if( sizeof($_POST)>1 ){
		$gwt = array(
			'id' => $gwt['id'],
			'name' => isset($_POST['gwt-name']) ? $_POST['gwt-name'] : $gwt['name'],
			'desc' => isset($_POST['gwt-desc']) ? $_POST['gwt-desc'] : $gwt['desc'],
			'publish' => isset($_POST['gwt-publish']) ? $_POST['gwt-publish'] : $gwt['publish']
		);
	}

	define('ADMIN_PAGE_TITLE', _('Edition d\'une liste personnalisée') . ' - ' . _('Clients'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error_tab) ){
		print '<div class="error">'.nl2br( $error_tab ).'</div>';
	}else{ 
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
?>
	<form action="/admin/customers/popup-wishlist-edit.php?gwt=<?php print $_GET['gwt']; ?>&amp;usr=<?php print $_GET['usr']; ?>" method="post">
		<?php
			if( $gwt['id']==0 ){
				print '<p class="notice">'._('Vous pouvez à partir d\'ici ajouter une nouvelle liste personnalisée qui sera automatiquement rattachée au compte client.').'</p>';
			}else{
				print '<p class="notice">'._('Vous pouvez à partir d\'ici éditer les informations de la liste personnalisée.').'</p>';
			}
		?>
		<table id="table-new-wishliste">
			<caption><?php
				if( $gwt['id']>0 ){
					print htmlspecialchars( $gwt['name'] );
				}else{
					print _('Nouvelle liste personnalisée');
				}
			?></caption>
			<tbody>
				<tr>
					<td id="td-new-wishliste-1">
						<span class="mandatory">*</span>
						<label for="gwt-name"><?php print _('Désignation :'); ?></label>
					</td>
					<td id="td-new-wishliste-2">
						<input type="text" name="gwt-name" id="gwt-name" value="<?php print $gwt['name']; ?>" />
					</td>
				</tr>
				<tr>
					<td>
						<label for="gwt-desc"><?php print _('Description :')?></label>
					</td>
					<td>
						<textarea name="gwt-desc" id="gwt-desc"><?php print $gwt['desc']; ?></textarea>
					</td>
				</tr>
				<tr>
					<td>
						<label for="gwt-publish-yes"><?php print _('Publiée :'); ?></label>
					</td>
					<td>
						<input <?php print $gwt['publish'] ? 'checked="checked"' : ''; ?> type="radio" name="gwt-publish" id="gwt-publish-yes" value="1" />
						<label for="gwt-publish-yes"><?php print _('Oui')?></label>
						<input <?php print !$gwt['publish'] ? 'checked="checked"' : ''; ?> type="radio" name="gwt-publish" id="gwt-publish-no" value="0" />
						<label for="gwt-publish-no"><?php print _('Non')?></label>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2" class="align-right">
						<input type="submit" name="save" value="<?php print _('Enregistrer')?>" />
						<?php if( $gwt['id']>0 ){
							print '
								<input type="submit" name="delete" value="'._('Supprimer').'" />
							';
						} ?>
						<input type="button" name="cancel" class="float-none" value="<?php print _('Annuler')?>" onclick="return parent.hidePopup();" />
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
	<?php } ?>
	<script><!-- <?php 
		if( (isset($_POST['save']) || isset($_POST['delete'])) && !isset($error) ){
			print '
				parent.reloadBookmarks();
			';
		}
	?> --></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
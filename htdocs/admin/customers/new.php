<?php

	/**	\file new.php
	 *	Cette page permet la création d'un nouveau compte (client, administrateur, représentant, ...)
	 */

	require_once('sys.countries.inc.php');
	require_once('sys.naf.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( !isset($_GET['prf']) || $_GET['prf']!=1 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_ADD');
	}else{ // $_GET['prf']==1
		gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_ADD_ADMIN');
	}

	// Paramètre d'état dans l'enchaînement d'écrans avec ncmd-customers-change.php
	$_GET['no-add'] = isset($_GET['no-add']) && $_GET['no-add'] ? 1 : 0;

	// Contrôle du Mot de passe
	if( isset($_POST['password1'],$_POST['password2'])){
		if (strlen($_POST['password1']) != 0 ){
			if( (strlen($_POST['password1']) >0 ) && (strlen($_POST['password1']) ==0)){
				$error = 5;
			}elseif ( $_POST['password1'] != $_POST['password2']){
				$error = 7;
			}elseif( !gu_valid_password($_POST['password1']) ){
				$error = 17;
			}
		}elseif(!$config['password_user_generate']) {
			$error = 18;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save-main']) ){
		if( isset($_POST['type'],$_POST['address1']) ){
			// Vérifie que tous les champs demandés ont été envoyés
			if( $_POST['type']==1 ){ // Particulier
				if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname']) || $_POST['title'] == "" || $_POST['firstname'] == "" ||  $_POST['lastname']== ""){
					$error = 1;
				}
			}elseif( $_POST['type']==2 ){ // Professionnel
				if( !isset($_POST['society']) )
					$error = 1;
			}elseif( $_POST['type']==3 ){
				if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society']) )
					$error = 1;
			}
			if( !isset($error) && ( !isset($_POST['usr-prf-id']) || $_POST['usr-prf-id']=='' ) ){
				$error = 1;
			}

			if( !isset($error) && (!isset($_POST['country']) || !trim($_POST['country'])) )
					$error = 1;
			if( !isset($error) && (!isset($_POST['email']) || !trim($_POST['email'])) )
					$error = 1;

			// Vérifie que les informations fournies sont valides
			if( !isset($error) ){
				if( $_POST['type']==1 ){ // Particulier
					if( !gu_titles_exists($_POST['title']) ){
						$error = 2;
					}
				}elseif( $_POST['type']==2 ){ // Professionnel
					if( !trim($_POST['society']) )
						$error = 2;
				}elseif( $_POST['type']==3 ){
					if( !gu_titles_exists($_POST['title']) || !trim($_POST['society']) ){
						$error = 2;
					}
				}
			}

			// Ajoute le nouveau client
			if( !isset($error) ){
				// La variable $_POST['select-usr-parent'] n'est pas disponible pour Yuto Essentiel
				if( !isset($_POST['select-usr-parent']) ){
					$_POST['select-usr-parent'] = '';
				}

				$_POST['usr-parent'] = isset($_POST['usr-parent']) && $_POST['usr-parent']!='' ? $_POST['usr-parent'] : $_POST['select-usr-parent'];
				$end = stripos( $_POST['usr-parent'], '-' );
				$email = trim( $end ? substr($_POST['usr-parent'], 0, $end) : $_POST['usr-parent'] );

				if( !gu_users_exists(0,0,$_POST['email'],'',true) ){
					if( trim($email) ){
						if( $email==$_POST['email'] ){
							$error = 11;
						}elseif( !gu_users_exists(0, 0, $email) ){
							$error = 12;
						}else{
							$parent = ria_mysql_fetch_array( gu_users_get(0, $email) );
							if( $parent['parent_id'] ){ // le compte parent ne doit pas être un compte enfant
								$error = 13;
							}
						}
					}
					if( !isset($error) ){

						$id = gu_users_add($_POST['email'], isset($_POST['password1']) ? $_POST['password1'] : '', $_POST['usr-prf-id'], '', false, 0, 0, isset($parent['id']) ? $parent['id'] : null);
						if( !$id ){
							$error = 3;
						}else{
							{ // Gestion des droits
								// Les droits appliqués sont les mêmes que ce accessible pas le compte qui créé le compte
								$rights = gu_users_rights_get_array($_SESSION['usr_id'], false, true);
								if (is_array($rights) && count($rights)) {
									$ar_rights_save = array();
									foreach ($rights as $one_rgh) {
										$ar_rights_save[ $one_rgh] = 'Oui';
									}

									gu_users_rights_add($id, $ar_rights_save);
								}
							}

							// Code NAF
							if( !isset($error) && isset($_POST['naf']) ){
								if( !gu_users_set_naf( $id, $_POST['naf'] ) ){
									$error = 20;
								}
							}

							// Site Web
							if( !isset($error) ){
								$set_website = gu_users_set_website( $id, $_POST['website'] );
								if( !$set_website ){
									$error = _('La mise à jour du site web du compte a échoué pour une raison inconnue.');
								}
							}

							// si profil vendeur, on utilise le usr_id comme seller_id
							if( $_POST['usr-prf-id'] == PRF_SELLER ){
								if( !gu_users_set_seller_id( $id, $id ) ){
									error_log( __FILE__.':'.__LINE__.' '._('Impossible d\'assigner un seller_id à').' '.$id );
								}
							}
							$adr = gu_adresses_add($id, $_POST['type'], isset($_POST['title']) ? $_POST['title'] : 'null', $_POST['firstname'], $_POST['lastname'], $_POST['society'], $_POST['siret'], $_POST['address1'], $_POST['address2'],$_POST['zipcode'], $_POST['city'],$_POST['country'], $_POST['tel'], $_POST['fax'], $_POST['mobile'], $_POST['phone-work'], '', $_POST['email'], null, $_POST['address3']);
							if( !$adr ){
								$error = 3;
							}else{
								if( !gu_users_address_set($id,$adr) ) {
									$error = 3;
								}elseif( isset($_POST['paiements']) ){
									foreach ($_POST['paiements'] as $key => $value) {
										if( !gu_users_payment_types_add($id,$value,0,0,0) ){
											$error = 9;
										}
									}
								}
							}

							// Si le profil est administrateur, on rajoute ce compte dans le registre GCP des administrateur
							// Seulement dans l'environnement de l'administration mutualisée
							if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
								if( $_POST['usr-prf-id'] == PRF_ADMIN ){
									require_once('Administrator.inc.php');

									$n_admin = new Administrator();

									if( isset($_POST['title']) ){
										$n_admin->setCivility($_POST['title']);
									}

									if( isset($_POST['password1']) ){
										$n_admin->setPassword($_POST['password1']);
									}

									if( isset($_POST['dob']) ){
										$n_admin->setBirthday($_POST['dob']);
									}

									$n_admin->setFirstname($_POST['firstname'])
											->setLastname($_POST['lastname'])
											->setEmail($_POST['email'])
											->setPhone($_POST['tel'])
											->setFax($_POST['fax'])
											->setMobile($_POST['mobile'])
											->setWork($_POST['phone-work'])
											->setLang('fr_FR')
											->setWelcome(true);

									// Enregistre le compte administrateur dans le registre
									if( $n_admin->save(false) !== true ){
										$error = 19;
									}
								}
							}
						}
					}
				}else{
					$error = 4;
				}
			}

			// Commentaires
			if( !isset($error) && isset($id) && $id>0 ){
				if( trim($_POST['notes'])!='' ){
					fld_object_notes_add( CLS_USER, $id, _('Commentaire fiche client'), $_POST['notes'] );
				}
			}

			// Date de naissance
			if( !isset($error) && isset($id) && $id>0 ){
				if( isset($_POST['dob']) ){
					if( !gu_users_set_date_of_birth($id, $_POST['dob']) ){
						$error = 15;
					}
				}
			}

			// Code client
			if( !isset($error) && isset($id) && $id>0 ){
				if( isset($_POST['ref']) && strlen($_POST['ref'])<=17 ){
					if( trim($_POST['ref']) != '' && gu_users_exists(0, 0, '', $_POST['ref']) ){
						$error = 14;
					}elseif( !gu_users_update_ref( $id, $_POST['ref'] ) ){
						$error = 8;
					}
				}
			}

			// Définit le message d'erreur en fonction du code utilisé
			if( isset($error) ){
				switch( $error ){
					case 1:
						$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
						break;
					case 2:
						$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une <span class=\"mandatory\">*</span> sont obligatoires.");
						break;
					case 3:
						$error = _("Une erreur inattendue s'est produite lors de l'ajout de l'adresse de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 4:
						$error = _("L'adresse email utilisée existe déjà dans la base de données.")."\n"._("La création de l'utilisateur a échoué.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 5:
						$error = _("Veuillez confirmer le nouveau mot de passe.");
						break;
					case 6:
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre nouveau mot de passe.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
						break;
					case 7:
						$error = _("Les deux valeurs saisies pour votre nouveau mot de passe diffèrent.")."\n"._("Veuillez les saisir de nouveau.");
						break;
					case 8:
						$error = _("Une erreur inattendue s'est produite lors de la modification du code client de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 9:
						$error = _("Une erreur inattendue s'est produite lors de l'ajout des moyens de paiement de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 10:
						$error = _("Une erreur inattendue s'est produite lors de l'envoi du mail à l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 11:
						$error = _("L'adresse mail du compte référent ne peux pas être la même que celle du nouveau compte.");
						break;
					case 12:
						$error = sprintf(_("Le compte référent &lt;%s&gt; n'existe pas."), $_POST['usr-parent']);
						break;
					case 13:
						$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=relation\">%s</a>&gt; n'est pas un compte référent."), $parent['id'], $email) ;
						break;
					case 14 :
						$error = _("Ce code client est déjà attribué à un autre compte, veuillez en choisir un autre.");
						break;
					case 15 :
						$error = _("Une erreur est survenue lors de la modification de la date de naissance.");
						break;
					case 16:
						$error = _('Le mot de passe doit contenir un minimum de 6 caractères pour être accepté.');
						break;
					case 17:
						$error = $config['password_error_message'];
						break;
					case 18:
						$error = _('Vous devez saisir un mot de passe.');
						break;
					case 19:
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du compte.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler (Code : ERR0002).");
						break;
					case 20:
						$error = _("Le code NAF saisi n'est pas reconnu.");
						break;
				}
			}else{
				if( isset($_GET['popup']) ){
					print '
					<script>';
						if (isset($_GET['upd-ord-usr'])){
							print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'&usr='.$id.'&upd-ord-usr=1&ord='.$_GET['ord'].'"';
						}elseif (isset($_GET['ord-duplicate'])){
							print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'&usr='.$id.'&ord-duplicate=1&ord='.$_GET['ord'].'&ref_input='.$_GET['ref_input'].'"';
						}else{
							// Ajour des données pour l'ajout du compte dans le rapport de visite
							print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'&usr='.$id.((isset($_GET['callback'])) ? '&callback=' . $_GET['callback'] : '').'"';
						}
					print '</script>';
				}else{
					header('Location: edit.php?usr='.( isset($parent['id']) ? $parent['id'].'&tab=relation' : $id));
					exit;
				}
			}
		}
	}

	Breadcrumbs::root( _('Acccueil'), '/admin/index.php' )
		->push( _('Tous les comptes'), '/admin/customers/index.php' )
		->push( _('Créer un nouveau compte') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Créer un nouveau compte') . ' - ' . _('Comptes clients'));
	if( !isset($_GET['popup']) ){
		require_once('admin/skin/header.inc.php');
	}else{
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
		require_once('admin/skin/header.inc.php');
	}
?>
<h2><?php print _('Créer un nouveau compte'); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}

	$usr = array(
		'ref'=> isset($_POST['ref']) ? $_POST['ref'] : '',
		'type'=> isset($_POST['type']) ? $_POST['type'] : '',
		'title'=> isset($_POST['title']) ? $_POST['title'] : '',
		'firstname'=> isset($_POST['firstname']) ? $_POST['firstname'] : '',
		'lastname'=> isset($_POST['lastname']) ? $_POST['lastname'] : '',
		'society'=> isset($_POST['society']) ? $_POST['society'] : '',
		'siret'=> isset($_POST['siret']) ? $_POST['siret'] : '',
		'naf'=> isset($_POST['naf']) ? $_POST['naf'] : '',
		'email'=> isset($_POST['email']) ? $_POST['email'] : '',
		'dps_id'=> isset($_POST['dps']) ? $_POST['dps'] : '',
		'address1'=> isset($_POST['address1']) ? $_POST['address1'] : '',
		'address2'=> isset($_POST['address2']) ? $_POST['address2'] : '',
		'address3'=> isset($_POST['address3']) ? $_POST['address3'] : '',
		'zipcode'=> isset($_POST['zipcode']) ? $_POST['zipcode'] : '',
		'city'=> isset($_POST['city']) ? $_POST['city'] : '',
		'country'=> isset($_POST['country']) ? $_POST['country'] : 'FRANCE',
		'email'=> isset($_POST['email']) ? $_POST['email'] : '',
		'tel'=> isset($_POST['tel']) ? $_POST['tel'] : '',
		'fax'=> isset($_POST['fax']) ? $_POST['fax'] : '',
		'website'=> isset($_POST['website']) ? $_POST['website'] : '',
		/*'latitude'=> isset($_POST['latitude']) ? $_POST['latitude'] : '',
		'longitude'=> isset($_POST['longitude']) ? $_POST['longitude'] : '',*/
		'mobile'=> isset($_POST['mobile']) ? $_POST['mobile'] : '',
		'phone-work'=> isset($_POST['phone-work']) ? $_POST['phone-work'] : '',
		'usr-prf-id'=> isset($_POST['usr-prf-id']) ? $_POST['usr-prf-id'] : (isset($_GET['prf']) ? $_GET['prf'] : ''),
		'usr-prc-id'=> isset($_POST['usr-prc-id']) ? $_POST['usr-prc-id'] : '',
		'notes' => isset($_POST['notes']) ? $_POST['notes'] : '',
		'dob' => isset($_POST['dob']) ? $_POST['dob'] : '',
	);
?>

<form id="form-edit-user" action="new.php?<?php print isset($_GET['popup']) ? 'popup=1' : ''; ?><?php print isset($_GET['upd-ord-usr']) ? '&upd-ord-usr=1&ord='.$_GET['ord'] : ''; ?><?php print isset($_GET['ord-duplicate']) ? '&ord-duplicate=1&ord='.$_GET['ord'].'&ref_input='.$_GET['ref_input'] : ''; ?><?php print isset($_GET['no-add']) && $_GET['no-add'] ? '&no-add=1' : ''; ?> <?php print isset($_GET['callback']) ? '&callback='.$_GET['callback'] : ''; ?>" method="post" autocomplete="off">
	<table id="new-usr">
		<tbody>
			<tr><th colspan="2"><?php print _('Propriétés générales'); ?></th></tr>
			<tr>
				<td id="td-new-user-1"><label for="ref"><?php print _('Code client :'); ?></label></td>
				<td id="td-new-user-2"><input type="text" class="ref" name="ref" id="ref" value="<?php print htmlspecialchars($usr['ref']); ?>" maxlength="17" autocomplete="off" /></td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label for="type"><?php print _('Type d\'adresse :'); ?></label></td>
				<td>
					<select name="type" id="type" onchange="switch_adr_type(this.form)" onkeyup="switch_adr_type(this.form)">
					<?php
						$types = gu_adr_types_get();
						while( $t = ria_mysql_fetch_array($types) ){
							print '<option value="'.$t['id'].'"'.( $t['id']==$usr['type'] ? ' selected="selected"':'' ).'>'.htmlspecialchars($t['name']).'</option>';
						}
					?>
					</select>
				</td>
			</tr>
			<tr id="adr-civ">
				<td><span class="mandatory">*</span> <label><?php print _('Civilité :'); ?></label></td>
				<td>
				<?php
					$titles = gu_titles_get();
					while( $r = ria_mysql_fetch_array($titles) ){
						print '<input type="radio" class="radio" name="title" id="title-'.$r['id'].'" value="'.$r['id'].'"  '.( $usr['title']==$r['id'] ? ' checked="checked"':'' ).' /> ';
						print '<label class="inline" for="title-'.$r['id'].'">'.htmlspecialchars($r['name']).'</label> ';
					}
				?>
				</td>
			</tr>
			<tr id="adr-firstname">
				<td><span class="mandatory">*</span> <label for="firstname"><?php print _('Prénom :'); ?></label></td>
				<td><input type="text" name="firstname" id="firstname" value="<?php print htmlspecialchars($usr['firstname']) ?>" maxlength="75" /></td>
			</tr>
			<tr id="adr-lastname">
				<td><span class="mandatory">*</span> <label for="lastname"><?php print _('Nom de famille :'); ?></label></td>
				<td><input type="text" name="lastname" id="lastname" value="<?php print htmlspecialchars($usr['lastname']); ?>" maxlength="75" /></td>
			</tr>
			<tr id="adr-society" class="none">
				<td><span class="mandatory">*</span> <label for="society"><?php print _('Société :'); ?></label></td>
				<td><input type="text" name="society" id="society" value="<?php print htmlspecialchars($usr['society']); ?>" maxlength="75" autocomplete="off" /></td>
			</tr>
			<tr id="adr-siret" class="none">
				<td><label for="siret"><?php print _('SIRET :'); ?></label></td>
				<td><input type="text" name="siret" id="siret" value="<?php print htmlspecialchars($usr['siret']); ?>" size="17" maxlength="17" /></td>
			</tr>
			<tr id="usr-naf" class="none">
				<td><label for="naf"><?php print _('Code NAF :'); ?></label></td>
				<td><input type="text" name="naf" id="naf" value="<?php print htmlspecialchars($usr['naf']); ?>" size="7" maxlength="7" /></td>
			</tr>
			<tr>
				<td><label for="address1"><?php print _('No et rue :'); ?></label></td>
				<td><input type="text" name="address1" id="address1" maxlength="75" value="<?php print htmlspecialchars($usr['address1']); ?>" autocomplete="off" /></td>
			</tr>
			<tr>
				<td><label for="address2"><?php print _('Complément :'); ?></label></td>
				<td><input type="text" name="address2" id="address2" maxlength="75" value="<?php print htmlspecialchars($usr['address2']); ?>" autocomplete="off" /></td>
			</tr>
			<tr>
				<td><label for="address3"></label></td>
				<td><input type="text" name="address3" id="address3" maxlength="75" value="<?php print htmlspecialchars($usr['address3']); ?>" /></td>
			</tr>
			<tr>
				<td><label for="zipcode"><?php print _('Code postal :'); ?></label></td>
				<td><input type="text" name="zipcode" id="zipcode" class="zipcode" maxlength="9" value="<?php print htmlspecialchars($usr['zipcode']); ?>" autocomplete="off" /></td>
			</tr>
			<tr>
				<td><label for="city"><?php print _('Ville :'); ?></label></td>
				<td><input type="text" name="city" id="city" maxlength="75" value="<?php print htmlspecialchars($usr['city']); ?>" autocomplete="off" /></td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label for="country"><?php print _('Pays :'); ?></label></td>
				<td>
					<select name="country" id="country">
						<option value=""></option>
						<?php
							$countries = sys_countries_get();
							while( $c = ria_mysql_fetch_array($countries) ){
								print '<option value="'.htmlspecialchars($c['name']).'" '.( strtoupper($usr['country'])==strtoupper($c['name']) ? ' selected="selected"':'' ).'>'.htmlspecialchars($c['name']).'</option>';
							}
						?>
					</select>
				</td>
			</tr>
			<tr><th colspan="2"><?php print _('Contact')?></th></tr>
			<tr><td><span class="mandatory">*</span> <label for="email"><?php print _('Email :'); ?></label></td><td><input type="email" id="email" name="email" value="<?php print htmlspecialchars($usr['email']); ?>" autocomplete="new-password" /></td></tr>
			<tr><td><label for="tel"><?php print _('Téléphone :'); ?></label></td><td><input type="text" id="tel" name="tel" value="<?php print htmlspecialchars($usr['tel']); ?>" autocomplete="new-password" /></td></tr>
			<tr><td><label for="fax"><?php print _('Fax :'); ?></label></td><td><input type="text" id="fax" name="fax" value="<?php print htmlspecialchars($usr['fax']); ?>" autocomplete="new-password" /></td></tr>
			<tr><td><label for="mobile"><?php print _('Portable :'); ?></label></td><td><input type="text" id="mobile" name="mobile" value="<?php print htmlspecialchars($usr['mobile']); ?>" autocomplete="new-password" /></td></tr>
			<tr><td><label for="phone-work"><?php print _('Téléphone en journée :'); ?></label></td><td><input type="text" id="phone-work" name="phone-work" value="<?php print htmlspecialchars($usr['phone-work']); ?>" autocomplete="new-password" /></td></tr>
			<tr><td><label for="website"><?php print _('Site web :'); ?></label></td><td><input type="text" id="website" name="website" value="<?php ( $usr['website']!='' ? print htmlspecialchars($usr['website']) : '' ); ?>" autocomplete="new-password" /></td></tr>
			<tr><th colspan="2"><?php print _('Autorisations')?></th></tr>
			<tr><td><span class="mandatory">*</span> <label for="usr-prf-id"><?php print _('Droits d\'accès :'); ?></label></td><td>
				<select name="usr-prf-id" id="usr-prf-id">
					<option value=""><?php print _('Veuillez choisir un profil'); ?></option>
				<?php
					$profiles = gu_profiles_get();
					while( $r = ria_mysql_fetch_array($profiles) ){
						if( !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN') && $r['id'] == PRF_ADMIN ){
							continue;
						}
						if( !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') && $r['id'] != PRF_ADMIN ){
							continue;
						}
						print '<option value="'.$r['id'].'"'.( $usr['usr-prf-id']==$r['id'] ? ' selected="selected"':'' ).'>'.htmlspecialchars($r['name']).'</option>';
					}
				?>
				</select>
			</td></tr>
			<?php if( !tnt_tenants_is_yuto_essentiel() ){ ?>
				<tr><td><label for="usr-prc-id"><?php print _('Catégorie tarifaire :'); ?></label></td><td>
					<select name="usr-prc-id" id="usr-prc-id">
						<option value=""><?php print _('Veuillez sélectionner une catégorie tarifaire')?></option>
						<?php
							$categories = prd_prices_categories_get();
							if( $categories && ria_mysql_num_rows($categories) ){
								while( $r = ria_mysql_fetch_array($categories) ){
									print '<option value="'.$r['id'].'"'.( $usr['usr-prc-id']==$r['id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($r['name']).'</option>';
								}
							}
						?>
					</select>
				</td></tr>
				<tr id="row-payment-types"><td><?php print _('Moyen de paiements :'); ?></td><td>
				<?php
					$pay = ord_payment_types_get();
					if( !ria_mysql_num_rows($pay) ){
						print '<span class="error">'._('Aucun moyen de paiement autorisé').'</span>';
					}else{
						while( $res=ria_mysql_fetch_array($pay) ){
							if( RegisterGCP::onGcloud() && in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){
								if( !in_array($res['id'], [_PAY_CB, _PAY_CHEQUE, _PAY_VIREMENT, _PAY_COMPTE]) ){
									continue;
								}
							}

							if (isset($_POST['paiements'])) {
								$checked = in_array($res['id'], $_POST['paiements']);
							}else{
								$checked = is_array($config['default_usr_payments']) && in_array($res['id'], $config['default_usr_payments']);
							}
							print '<input type="checkbox" name="paiements[]" id="opt-'.$res['id'].'" value="'.$res['id'].'" '.($checked ? 'checked="checked"' : '').' />&nbsp;<label for="opt-'.$res['id'].'" class="inline">'.htmlspecialchars( $res['name'] ).'</label><br />';
						}
					}
				?>
				</td></tr>
				<tr><td><label for="dps"><?php print _('Dépôt principal :'); ?></label></td><td>
					<select name="dps" id="dps" disabled="disabled">
						<option value=""><?php print _('Aucun dépôt principal')?></option>
					<?php
						$deposits = prd_deposits_get();
						while( $dps = ria_mysql_fetch_array($deposits) ){
							print '<option value="'.$dps['id'].'"'.( $dps['id']==$usr['dps_id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars($dps['name']).'</option>';
						}
					?>
					</select>
				</td></tr>
			<?php } ?>
			<?php if( $usr['usr-prf-id']==PRF_SELLER || $usr['usr-prf-id']==PRF_ADMIN || tnt_tenants_have_websites() ){ ?>
            <tr><th colspan="2"><?php print _('Mot de passe')?></th></tr>
            <tr><td><label for="password1"><?php print _('Nouveau mot de passe :'); ?></label></td><td><input type="password" class="text" name="password1" id="password1" maxlength="32" autocomplete="new-password" /></td></tr>
            <tr><td><label for="password2"><?php print _('Confirmation :'); ?></label></td><td><input type="password" class="text" name="password2" id="password2" maxlength="32" autocomplete="new-password" /></td></tr>
			<?php } ?>
			<?php if( !tnt_tenants_is_yuto_essentiel() ){ ?>
				<tr><th colspan="2"><?php print _('Compte référent')?></th></tr>
				<tr>
					<td><label for="usr-parent"><?php print _('Email compte référent :'); ?></label></td>
					<td><?php
						$email = isset($_GET['parent']) && gu_users_exists($_GET['parent']) ? gu_users_get_email($_GET['parent']) : (isset($_POST['usr-parent']) ? $_POST['usr-parent'] : '');
						print '	<input type="text" name="select-usr-parent" id="select-usr-parent" value="'.$email.'" />';
						print '	<input type="hidden" name="usr-parent" id="usr-parent" value="" />';
					?></td>
				</tr>
			<?php } ?>
			<tr><th colspan="2"><?php print _('Divers')?></th></tr>
			<tr><td><label for="notes"><?php print _('Commentaires :'); ?></label></td><td><textarea name="notes"><?php
					print htmlspecialchars( $usr['notes'] );
			?></textarea></td></tr>
			<tr id="misc-dob"><td><label for="dob"><?php print _('Date de naissance :'); ?></label></td><td><input type="text" id="dob" name="dob" value="<?php print htmlspecialchars($usr['dob']); ?>"></td></tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save-main" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications')?>" />
					<input type="submit" name="cancel" id="cancel" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications')?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<script><!--
	// Bouton annuler
	$('#cancel').click(function(e){
		e.preventDefault();
		<?php
		if( !isset($_GET['popup']) ){
			print 'document.location.href="index.php'.(isset($_GET['prf'])? '?prf='.$_GET['prf'].'"' : '"');
		}elseif (isset($_GET['upd-ord-usr'])){
			print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'&upd-ord-usr=1&ord='.$_GET['ord'].'"';
		}elseif (isset($_GET['ord-duplicate'])){
			print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'&ord-duplicate=1&ord='.$_GET['ord'].'&ref_input='.$_GET['ref_input'].'"';
		}else{
			print 'document.location.href="/admin/ajax/orders/ncmd-customers-change.php?no-add='.$_GET['no-add'].'"';
		}?>
	});
	$(document).ready(function(){
		switch_adr_type( document.getElementById('form-edit-user') );
		$('#password1').val('');
	});
//--></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>

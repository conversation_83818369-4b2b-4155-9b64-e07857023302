<?php
    require_once('goals.inc.php');

    if( isset($_GET['activate'], $_GET['goal'], $_GET['usr']) ){
        if (obj_objectifs_set_active($_GET['usr'], $_GET['goal'], $_GET['activate'])) {
            print 'success';
            exit;
        } else {
            print 'error';
            exit;
        }
    }
    
    if( isset($_GET['reload'], $_GET['year'], $_GET['usr'], $_GET['goal'])){
        print view_goals( $_GET['usr'], $_GET['goal'], $_GET['year'] );
        exit;
    }

    if( isset($_GET['new'], $_GET['usr'], $_GET['obj'], $_GET['name'], $_GET['start'], $_GET['end']) ){
        $r_obj = obj_objectifs_get_kpi($_GET['obj']);
        if (!$r_obj || !ria_mysql_num_rows($r_obj)) {
           print 'error';
           exit;
        }

        $obj = ria_mysql_fetch_assoc($r_obj);
        $pref_sufixe = $sufixe = '';
        switch ($obj['unite']) {
            case 'second' : $sufixe = ' s'; break;
            case 'euro' :   $pref_sufixe = $sufixe = ' €'; break;
        }

        $id = obj_periods_add( $_GET['usr'], $_GET['obj'], 'perso', dateparse($_GET['start']), dateparse($_GET['end']), 0 , $_GET['name'] );

        $perf = stats_kpi_get($_GET['usr'], dateparse($_GET['start']), dateparse($_GET['end']), array($_GET['obj']));
        $perf = $perf[$_GET['obj']];
        
        if( !$perf ){
            $perf = '--';
        }else{
            // Affichage des temps sauvegardés en seconde
            if ($obj['unite'] == 'second') {
                $perf = str_replace(array(' ', ','), array('', '.'), $perf);
                $perf = ria_seconds_to_time($perf, 6, '+ de ');

                if (trim($perf) == '') {
                    $perf = 0;
                }
            } elseif ($obj['unite'] == 'euro') {
                $perf = number_format($perf, 2, ',', ' ');
            }
        }

        if( $id ){
            print '
                <tr id="'.$id.'">
                    <td>
                        '.htmlspecialchars($_GET['name']).' (du '.$_GET['start'].' au '.$_GET['end']. ')
                        <a href="#" data-id="'.$id.'" class="del obj-del">Supprimer</a>
                    </td>
                    <td class="seller-perf">'.$perf.$pref_sufixe.'</td>
                    <td>
                        <span class="obj-value-input">
                            <input class="obj-value price" name="perso['.$id.']" type="text" value="" placeholder="--" />'.$sufixe.'
                        </span>
                    </td>
                </tr>';
           exit;
        }else{
           print 'error';
           exit;
       }
    }

    if( isset($_GET['remove'], $_GET['id']) ){
        if( obj_periods_del($_GET['id']) ){
            print 'success';
            exit;
        }else{
            print 'error';
            exit;
        }
    }

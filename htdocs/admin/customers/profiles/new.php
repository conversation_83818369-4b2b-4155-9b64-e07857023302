<?php
	require_once('profiles.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_RIGHT_ADD');

	if( isset($_POST['save-main']) ){
		if( !isset($_POST['name'], $_POST['pl-name']) || !trim($_POST['name']) || !trim($_POST['pl-name']) )
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		else{
			$new = gu_profiles_add( $_POST['name'], $_POST['pl-name'], $_POST['desc'] );

			if( !$new )
				$error = _("Une erreur inattendue s'est produite lors de l'ajout du droit d'accès.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");

			if( !isset($error) ){
				header('Location: /admin/customers/profiles/edit.php?prf='.$new);
				exit;
			}
		}
	}

	if( isset($_POST['cancel']) ){
		header('Location: /admin/customers/profiles/index.php');
		exit;
	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Comptes clients'), '/admin/customers/index.php' )
		->push( _('Gestion des droits d\'accès'), '/admin/customers/profiles/index.php' )
		->push( _('Nouveau droit d\'accès') );

	define('ADMIN_PAGE_TITLE', _('Nouveau droit d\'accès') . ' - ' . _('Gestion des droits d\'accès') . ' - ' . _('Comptes clients'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Nouveau droit d\'accès')?></h2>
<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
?>
<p><?php print _('À partir de ce formulaire, vous avez la possibilité de créer un droit d\'accès pouvant être rattaché aux comptes clients.')?></p>
<form action="new.php" method="post">
		<table id="table-propriete">
		<tfoot>
			<tr><td colspan="2">
				<input type="submit" name="save-main" value="<?php print _('Enregistrer')?>" />
				<input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
			</td></tr>
		</tfoot>
		<tbody>
			<tr>
				<td id="td-propriete-1"><label for="name"><span class="mandatory">*</span> <?php print _('Intitulé :'); ?></label></td>
				<td id="td-propriete-2"><input type="text" name="name" id="name" value="<?php print isset($_POST['name']) ? $_POST['name'] : ''; ?>" maxlength="75" /></td>
			</tr>
			<tr>
				<td><label for="pl-name"><span class="mandatory">*</span> <?php print _('Intitulé au pluriel :'); ?></label></td>
				<td><input type="text" name="pl-name" id="pl-name" value="<?php print isset($_POST['pl-name']) ? $_POST['pl-name'] : ''; ?>" maxlength="75" /></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Description :'); ?></label></td>
				<td><textarea name="desc" id="desc" cols="50" rows="10"><?php print isset($_POST['desc']) ? $_POST['desc'] : ''; ?></textarea></td>
			</tr>
		</tbody>
		</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
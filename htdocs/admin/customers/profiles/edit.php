<?php

	/**	\file edit.php
	 *	Ce fichier permet la mise à jour d'un profil utilisateur. Elle est composée de deux onglets :
	 *	1. Propriétés générales
	 *	2. Droits d'accès
	 */
 
	// unset($error);
	require_once('profiles.inc.php');
	require_once('rights.inc.php');

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_RIGHT_VIEW');

	// Vérifie que l'utilisateur à bien le droit de modifier un droit d'accès
	if( isset($_POST['save-main']) || isset($_POST['save-rights']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_RIGHT_EDIT');
	}
	
	// Vérifie que le profil demandé existe bien, sinon renvoie à la liste des profils
	if( isset($_GET['id']) ){
		$_GET['prf'] = $_GET['id'];
	}
	if( !isset($_GET['prf']) || !gu_profiles_exists($_GET['prf']) ){
		header('Location: /admin/customers/profiles/index.php');
		exit;
	}
	
	// Chargement du profil demandé
	$rprf = gu_profiles_get( $_GET['prf'] );
	if( !$rprf || !ria_mysql_num_rows($rprf) ){
		header('Location: /admin/customers/profiles/index.php');
		exit;
	}
	$prf = ria_mysql_fetch_array( $rprf );
	
	$tab = 'general';
	$_GET['tab'] = isset($_GET['tab']) ? $_GET['tab'] : '';
	if( isset($_POST['tabRights']) || $_GET['tab']=='rights' ){
		$tab = 'rights';
	}
	if( isset($_POST['cancel']) ){
		header('Location: /admin/customers/profiles/index.php');
		exit;
	}
	
	require_once('view.translate.inc.php');
	$_GET['lng'] = isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng'];
	$lng = view_selected_language();
	
	$tenant_link = gu_profiles_is_tenant_linked( $_GET['prf'] );
	if( $lng!=$config['i18n_lng'] ){
		$tenant_link = true;
	}
	
	// Bouton Enregistrer (Propriétés générales)
	if( isset($_POST['save-main']) ){
		if( $tenant_link ){
			if( !isset($_POST['name'], $_POST['pl-name']) || !trim($_POST['name']) || !trim($_POST['pl-name']) ){
				$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.");
			}else{
				if($_POST['typeDisplay']=='HT'){
					$display_price_ttc = false;
				}else{
					$display_price_ttc = true;
				}
				
				if( $lng!=$config['i18n_lng'] ){
					$values = array(
						_FLD_PRF_NAME => $_POST['name'], 
						_FLD_PRF_NAME_PL => $_POST['pl-name'], 
						_FLD_PRF_DESC => $_POST['desc']
					);
					
					if( !fld_translates_add( $_GET['prf'], $lng, $values) ){
						$error = _("Une erreur inattendue s'est produite lors de la traduction des informations générales.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
					}
				}elseif( !gu_profiles_update( $_GET['prf'], $_POST['name'], $_POST['pl-name'], $_POST['desc'], strtolower2($_POST['typeDisplay'])) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour des informations générales.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/customers/profiles/edit.php?prf='.$_GET['prf'].'&lng='.$lng);
			exit;
		}
	}
	
	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		if( $tenant_link ){
			$success = gu_profiles_del( $_GET['prf'] );
			if( $success ){
				header('Location: /admin/customers/profiles/index.php?del=1');
				exit;
			}else{
				$error = _('La suppression a échoué pour une raison inconnue. Veuillez réessayer ou prendre contact pour nous signaler l\'erreur.');
			}
		}
	}
	
	// Bouton Enregistrer (droits d'accès)
	if( isset($_POST['save-rights']) ){
		$tab = 'rights';
		if( !isset($_POST['rghprf']) ){
			if( !gu_profiles_rights_del($_GET['prf'], false, isset($_POST['reset-rgh']) ? true : false) )
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour des droits.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
		} else {
			if( !gu_profiles_rights_add($_GET['prf'], $_POST['rghprf'], isset($_POST['reset-rgh']) ? true : false) )
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour des droits.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
		}
		
		if( !isset($error) ){
			header('Location: /admin/customers/profiles/edit.php?prf='.$_GET['prf'].'&tab=rights');
			exit;
		}
	}
	
	// Récupère la langue et la traduction
	if( $lng!=$config['i18n_lng'] ){
		$tsk = fld_translates_get( CLS_PROFIL, $prf['id'], $lng, $prf, array(_FLD_PRF_NAME=>'name', _FLD_PRF_NAME_PL=>'pl_name', _FLD_PRF_DESC=>'desc'), true );
		$prf['name'] = $tsk['name'];
		$prf['pl_name'] = $tsk['pl_name'];
		$prf['desc'] = $tsk['desc'];
	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Clients'), '/admin/customers/index.php' )
		->push( _('Gestion des droits d\'accès'), '/admin/customers/profiles/index.php' )
		->push( isset($prf['name']) ? $prf['name'] : _('Nouveau droit d\'accès') );

	define('ADMIN_PAGE_TITLE', _('Edition') . ' - ' . _('Gestion des droits d\'accès') . ' - ' . _('Comptes clients'));
	require_once('admin/skin/header.inc.php');
	
	print view_translate_menu( 'edit.php?id='.$prf['id'], $lng );
?>

<h2><?php print _('Gestion des droits d\'accès') . ' : ' . htmlspecialchars( $prf['name'] ); ?></h2>
<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
?>
<form action="edit.php?prf=<?php print $_GET['prf']; ?>&amp;lng=<?php print $lng; ?>" id="form-edit-user" method="post" enctype="multipart/form-data">
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général')?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( $_GET['prf'] != 1 ){ ?>
		<li><input type="submit" name="tabRights" value="<?php print _('Droits')?>" <?php if( $tab=='rights' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	
	<div id="tabpanel">
		<?php if( $tab=='general' ){ 
			// Contenu de l'onglet "Général"
			// print ord_orders_notify(483254);
			if( !$tenant_link ){
		?>
			<p class="notice">
				<?php printf(_('Les informations générales du droit d\'accès %s ne peuvent pas être modifiées car ce droit d\'accès est requis par le système. Vous avez toutefois la possibilité de gérer ses droits.'),$prf['name'])?>
			</p>
		<?php } ?>
			<table id="table-infos-generales-profiles">
				<tbody>
					<tr>
						<td id="td-infos-gen-prof-1"><label for="name"><span class="mandatory">*</span> <?php print _('Intitulé :'); ?></label></td>
						<td id="td-infos-gen-prof-2">
							<?php if( !$tenant_link ){ ?>
								<!-- Si on est en lecture seule, on n'affiche pas les inputs et on met le texte en grisé -->
								<span class="readonly">
									<?php if( isset($_POST['name']) ){
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseigné"
										print $_POST['name']!=trim('') ? htmlspecialchars($_POST['name']) : _('Non renseigné');
									}else{
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseignée"
										print $prf['name']!=trim('') ? htmlspecialchars($prf['name']) : _('Non renseigné');
									} ?>
								</span>
								<!-- Sinon on affiche les inputs -->
							<?php }else{ ?>
								<input type="text" name="name" id="name" value="<?php print isset($_POST['name']) ? htmlspecialchars($_POST['name']) : htmlspecialchars($prf['name']); ?>" maxlength="75" /><?php
							} ?>
						</td>
					</tr>
					<tr>
						<td><label for="pl-name"><span class="mandatory">*</span> <?php print _('Intitulé au pluriel :'); ?></label></td>
						<td>
							<?php if( !$tenant_link ){ ?>
								<!-- Si on est en lecture seule, on n'affiche pas les inputs et on met le texte en grisé -->
								<span class="readonly">
									<?php if( isset($_POST['pl-name']) ){
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseigné"
										print $_POST['pl-name']!=trim('') ? htmlspecialchars($_POST['pl-name']) : _('Non renseigné');
									}else{
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseigné"
										print $prf['pl_name']!=trim('') ? htmlspecialchars($prf['pl_name']) : _('Non renseigné');
									} ?>
								</span>
								<!-- Sinon on affiche les inputs -->
							<?php }else{ ?>
								<input type="text" name="pl-name" id="pl-name" value="<?php print isset($_POST['pl-name']) ? htmlspecialchars($_POST['pl-name']) : htmlspecialchars($prf['pl_name']); ?>" maxlength="75" /><?php
							} ?>
						</td>
					</tr>
					<tr>
						<td><label for="desc"><?php print _('Description :'); ?></label></td>
						<td>
							<?php if( !$tenant_link ){ ?>
								<!-- Si on est en lecture seule, on n'affiche pas le textarea et on met le texte en grisé -->
								<span class="readonly">
									<?php if( isset($_POST['desc']) ){
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseigné"
										print $_POST['desc']!=trim('') ? htmlspecialchars($_POST['desc']) : _('Non renseignée');
									}else{
										// On vérifie s'il y a du contenu dans la variable, sinon on précise "Non renseigné"
										print $prf['desc']!=trim('') ? htmlspecialchars($prf['desc']) : _('Non renseignée');
									} ?>
								</span>
								<!-- Sinon on affiche le textarea -->
							<?php }else{ ?>
								<textarea name="desc" id="desc" rows="10" cols="50" <?php print !$tenant_link ? 'readonly="readonly"' : ''; ?>><?php print isset($_POST['desc']) ? htmlspecialchars($_POST['desc']) : htmlspecialchars($prf['desc']); ?></textarea><?php
							} ?>
						</td>
					</tr>
					<tr>
						<td><?php print _('Affichage par défaut des prix :'); ?></td>
						<td>
							<input <?php print ($prf['display_prices']=='ttc')? 'checked' : '' ?> type="radio" id="typeDisplayTTC" name="typeDisplay" value="TTC" <?php print !$tenant_link ? 'disabled' : ''; ?>> <label for="typeDisplayTTC"><?php print _('TTC')?></label>
							<input <?php print ($prf['display_prices']=='ht')? 'checked' : '' ?> type="radio" id="typeDisplayHT" name="typeDisplay" value="HT" <?php print !$tenant_link ? 'disabled' : ''; ?>> <label for="typeDisplayHT"><?php print _('HT')?></label>
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr><td colspan="2">
						<input type="submit" name="save-main" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Sauvegarder les modifications'); ?>"/>
						<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" title="<?php print _('Annuler les modifications et revenir à la liste'); ?>" />
						<?php if( $prf['users']==0 && $tenant_link ){ ?>
							<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer ce profil utilisateur'); ?>" />
						<?php }elseif( $prf['users']>0 && $tenant_link ){ ?>
							<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" title="<?php print _('La suppression d\'un profil est impossible tant qu\'un ou plusieurs utilisateurs y sont associés'); ?>" disabled="disabled" />
						<?php } ?>
					</td></tr>
				</tfoot>
			</table>
		<?php }elseif( $tab=='rights' ){ 
			// Contenu de l'onglet "Droits" ?>
			<p class="notice"><?php print _('À partir d\'ici, vous pouvez définir les droits qui sont accessibles par défaut aux comptes clients qui sont rattachés à ce droit d\'accès.')?></p>
			<table id="prf-rights">
				<tbody><?php
				
					$ar_rights = array();
					if( isset($error, $_POST['rghprf']) ){
						$ar_rights = $_POST['rghprf'];
					}else{
						$ar_rights = gu_profiles_rights_get_array( $_GET['prf'], false );
					}
					
					$rcat = gu_categories_rights_get();
					if( $rcat && ria_mysql_num_rows($rcat) ){
					
						$first = true;
						while( $cat = ria_mysql_fetch_array($rcat) ){
							
							$rghs = gu_rights_get( 0, '', $cat['id'] );
							if( $rghs && ria_mysql_num_rows($rghs) ){
								$count_rgh = gu_categories_rights_count_rights( $cat['id'] );
								$count_rgh_prf = gu_profiles_rights_count( $_GET['prf'], $cat['id'] );
								
								print '	<tr class="linecat '.( $first ? 'inexpansible' : 'expansible' ).'">
											<th class="checkall">
												<input class="catrgh" type="checkbox" name="catrgh[]" id="catrgh-'.$cat['id'].'" value="'.$cat['id'].'"'.( $count_rgh==$count_rgh_prf ? ' checked="checked"' : '' ).' />
											</th>
											<th class="td-prf-rights-2">'.$cat['name'].'</th>
										</tr>';
								while( $rgh = ria_mysql_fetch_array($rghs) ){
									print '	<tr class="linergh linergh-'.$cat['id'].'"'.( $first ? '' : ' style="display: none;"' ).'>
												<td>
													<input class="checkright catrgh-'.$cat['id'].'" type="checkbox" name="rghprf[]" id="rghprf-'.$rgh['id'].'" value="'.$rgh['id'].'"'.( in_array($rgh['id'], $ar_rights) ? ' checked="checked"' : '' ).' />
												</td>
												<td>
													<label for="rghprf-'.$rgh['id'].'" title="'.htmlspecialchars($rgh['name']." - ".$rgh['desc']).'">'.$rgh['name'].'</label>
												</td>
											</tr>';
								}
								$first = false;
							}
						}
					
					} else {
						print '<tr><td colspan="2" class="td-prf-rights-3">&nbsp;</td></tr>';
					}
				?></tbody>
				<tfoot>
					<tr><td colspan="2">
						<input type="checkbox" name="reset-rgh" id="reset-rgh" />
						<label class="reset-rgh" for="reset-rgh"><?php print _('En cochant cette case, la personnalisation des droits des comptes clients rattachés à ce droit d\'accès sera réinitialisée. Si aucune personnalisation existe, les droits des comptes clients seront les mêmes que ce droit d\'accès.')?></label>
						<input type="submit" name="save-rights" value="<?php print _('Enregistrer')?>" />
						<input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
					</td></tr>
				</tfoot>
			</table>
		<?php } ?>
	</div>
</form>

<?php if( $tab=='general' && (!$tenant_link || !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_RIGHT_EDIT')) ){ ?>
<script><!--
	// Disable tous les champs/boutons si on accède à cette page en lecture seule
	$(document).ready(function(){
		$('table').find('input, select, textarea').attr('disabled', 'disabled');
		$('#save-main').attr('disabled', 'disabled');
	});
//--></script>
<?php }
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	Cette page affiche la liste des segments, et permet les actions suivantes :
	 *	- Ajout d'un segment
	 *	- Suppression d'un ou plusieurs segments
	 *	- Duplication d'un segment
	 *	- Export des segments
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_SEGMENT');

	unset($error);

	// Dans la section "Comptes clients", seule la classe CLS_USER est acceptée (Compte client / Utilisateur)
	$_GET['cls'] = CLS_USER;
	$cls_name = fld_classes_get_name( $_GET['cls'] );

	// Ajout d'un segment
	if( isset($_POST['add-seg']) ){
		header('Location: /admin/customers/segments/segment.php?cls='.$_GET['cls'].'&id=0');
		exit;
	}

	// Suppression d'un ou plusieurs segment(s)
	if( isset($_POST['del-seg']) ){
		if( isset($_POST['seg']) && is_array($_POST['seg']) && sizeof($_POST['seg']) ){
			foreach( $_POST['seg'] as $seg_id ){
				if( !seg_segments_del($seg_id) ){
					$error = _("Une erreur inattendue est survenue pendant la suppression du segment sélectionné.")."\n"._("Merci de prendre contact avec nous pour nous signaler l'erreur.");
					break;
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['segment_delete_success'] = true;
			header('Location: /admin/customers/segments/index.php');
			exit;
		}
	}

	// Duplication d'un segment
	if( isset($_POST['copy']) ){
		if( isset($_POST['seg']) && is_array($_POST['seg']) && sizeof($_POST['seg']) ){
			foreach( $_POST['seg'] as $seg_id ){
				if( !seg_segments_duplicate($seg_id) ){
					$error = _("Une erreur inattendue est survenue pendant la copie du segment.")."\n"._("Merci de prendre contact avec nous pour nous signaler l'erreur.");
					break;
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['segment_duplicate_success'] = true;
			header('Location: /admin/customers/segments/index.php');
			exit;
		}
	}

	// Charge la liste des segments
	$rseg = seg_segments_get( 0, $_GET['cls'] );
	$segments_count = ria_mysql_num_rows($rseg);

	// Export des segments
	if( isset($_POST['export']) ){
		set_include_path(get_include_path() . PATH_SEPARATOR . '.');
		require_once('export-segments.php');
		exit;
	}

	// Nombre de jours avant de forcer la mise à jour d'un segment
	$DAYS_REFRESH = 1;

	$can_move = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_MOVE');

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_DEL') || gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_DUPLICATE');

	$is_yuto_essentiel = tnt_tenants_is_yuto_essentiel();
	$add_colspan = $is_yuto_essentiel ? 0 : 1;

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( $is_yuto_essentiel ? _('Comptes') : _('Comptes clients'), '/admin/customers/index.php' )
		->push( _('Segments'), '/admin/customers/segments/index.php' );

	define('ADMIN_PAGE_TITLE', _('Segments') . ' - ' . _('Comptes clients'));
	require_once('admin/skin/header.inc.php');
?>

<form action="/admin/customers/segments/index.php?cls=<?php print $_GET['cls']; ?>" method="post">

<h2>
	<?php print sprintf( _('Segments (%d)'), $segments_count ); ?>

	<input type="submit" name="export" id="export" value="<?php print _('Exporter')?>" title="<?php print _('Exporter la liste des segments au format Excel')?>" class="float-right" style="margin-left: 5px" />
	<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_ADD') ){ ?>
		<input type="submit" name="add-seg" id="add-seg-header" value="<?php print _('Nouveau segment')?>" title="<?php print _('Créer un nouveau segment'); ?>" class="float-right" />
	<?php } ?>

</h2>
<?php
	// Affichage des messages d'erreur et de succès
	if( isset($error) ){
		print '
			<div class="error">'.nl2br($error).'</div>
		';
	}elseif( isset($_SESSION['segment_delete_success']) ){
		print '
			<div class="success">'._('Les segments sélectionnés ont été supprimés.').'</div>
		';

		unset( $_SESSION['segment_delete_success'] );
	}elseif( isset($_SESSION['segment_duplicate_success']) ){
		print '
			<div class="success">'._('La duplication s\'est correctement déroulée, vous retrouvez ci-dessous les copies.').'</div>
		';

		unset( $_SESSION['segment_duplicate_success'] );
	}
?>
	<input type="hidden" name="cls_id" id="cls_id" value="<?php print $_GET['cls']; ?>" />
	<table id="obj-seg" class="checklist" style="width: 100%">
		<thead>
			<tr>
				<?php if( $checkbox ){ ?>
				<th class="col-check" data-label="<?php print _('Tout cocher :'); ?> ">
					<input type="checkbox" id="check-all-del" name="check-all-del" onclick="checkAllClick(this)" />
				</th>
				<?php } ?>
				<th id="seg-name" class="thead-none"><?php print _('Nom')?></th>
				<th id="seg-desc" class="thead-none"><?php print _('Description')?></th>
				<?php if( $_GET['cls'] != CLS_USER || !tnt_tenants_is_yuto_essentiel() ){ ?>
				<th id="seg-objects" class="thead-none"><?php print _('Objet(s) rattaché(s)')?></th>
				<?php } ?>
				<?php if( $can_move ){ ?>
				<th id="type-pos" class="thead-none"><?php print _('Déplacer')?></th>
				<?php } ?>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td class="tdleft" colspan="<?php print $checkbox? 2 + $add_colspan : 1 + $add_colspan; ?>">
				<?php if( $segments_count ){ ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_DEL') ){ ?>
						<input type="submit" name="del-seg" id="del-seg" value="<?php print _('Supprimer')?>" onclick="return window.confirm('<?php print _('Êtes-vous sûr(e) de vouloir supprimer les segments sélectionnés ?')?>')" />
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_DUPLICATE') ){ ?>
						<input type="submit" name="copy" value="<?php print _('Dupliquer')?>" title="<?php print _('Dupliquer le segment.')?>" />
					<?php } ?>
					<input type="submit" name="export" id="export" value="<?php print _('Exporter')?>" title="<?php print _('Exporter la liste des segments au format Excel')?>" />
				<?php } ?>
				</td>
				<td colspan="<?php print $can_move ? 2 : 1; ?>">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_ADD') ){ ?>
						<input type="submit" name="add-seg" id="add-seg" value="<?php print _('Nouveau segment')?>" title="<?php print _('Créer un nouveau segment'); ?>" />
					<?php } ?>
				</td>
			</tr>
		</tfoot>
		<tbody><?php

			if( $rseg && ria_mysql_num_rows($rseg) ){
				$count = ria_mysql_num_rows($rseg);
				while( $seg = ria_mysql_fetch_array($rseg) ){
					print '	<tr  id="line-'.$seg['id'].'" class="ria-row-orderable">';
					if( $checkbox ){
						print '		<td class="centertd">';
						print '			<input type="hidden" id="seg-id-'.$seg['id'].'" name="seg-id-'.$seg['id'].'" />';
						print '			<input type="checkbox" id="seg-'.$seg['id'].'" name="seg[]" value="'.$seg['id'].'" />';
						print '		</td>';
					}
					print '		<td data-label="'._('Nom :').' ">';
					if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_SEGMENT_EDIT') ){
						print '		<a href="/admin/customers/segments/segment.php?id='.$seg['id'].'">'.htmlspecialchars($seg['name']).'</a>';
					}else{
						print '		'.htmlspecialchars($seg['name']);
					}
					print '		</td>';
					print '		<td data-label="'._('Description :').' ">'.( trim($seg['desc']) ? htmlspecialchars($seg['desc']) : '&nbsp;' ).'</td>';

					// Ne pas afficher la colonne "Objets rattachés" pour les Riashop Yuto Essentiel
					if( !tnt_tenants_is_yuto_essentiel() ){
						print '	<td data-label="'._('Objet(s) rattaché(s) :').' ">';
						if( $_GET['cls'] == CLS_USER ){
							print '	<a href="/admin/customers/index.php?seg='.$seg['id'].'">'.ria_number_format($seg['objects'], NumberFormatter::DECIMAL).' '.($seg['objects']>1 ? _('clients rattachés') : _('client rattaché')).'</a>';
						}elseif( $_GET['cls'] == CLS_STORE ){
							print '	<a href="/admin/config/livraison/stores/index.php?seg='.$seg['id'].'">'.ria_number_format($seg['objects'], NumberFormatter::DECIMAL).' '.($seg['objects']>1 ? _('magasins rattachés') : _('magasin rattaché')).'</a>';
						}else{
							print '	'.ria_number_format($seg['objects'], NumberFormatter::DECIMAL).' '.($seg['objects'] > 1 ? _('objets rattachés') : _('objet rattaché'));
						}
							print '	</td>';
					}

					if( $can_move ){
						print '<td headers="type-pos" align="center" class="ria-cell-move">';
						print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
						print '</td>';
					}

					print '	</tr>';
				}
			} else {
				?>
			<tr>
				<td colspan="<?php print $can_move ? 4 + $add_colspan : 3 + $add_colspan; ?>"><?php print _('Aucun segment n\'a été trouvé.')?></td>
			</tr>
				<?php
			}
		?></tbody>
	</table>
	<?php if( $_GET['cls'] == CLS_USER && !tnt_tenants_is_yuto_essentiel() ){ ?>
	<div class="notice">
		<p><?php print _('Les segments vous permettent de créer une sélection dynamique de vos comptes clients en fonction de critères de votre choix. Les segments ainsi créés pourront ensuite être exportés ou utilisés dans les domaines suivants :'); ?></p>
		<ul>
			<li><?php print _('Création de codes promotion')?></li>
			<li><?php print _('Affichage sélectif de bannières, d\'actualités ou de contenus')?></li>
			<li><?php printf(_('Accès à des documents%s'), $config['use_catalog_restrictions'] ? _(' ou à certaines parties du catalogue') : ''); ?></li>
			<li><?php print _('Calcul des points de fidélité')?></li>
		</ul>
		<p><?php print _('Vous pouvez définir un ordre de priorité entre les segments, via la fonction "Déplacer". Ce tri nous permet de savoir lequel doit être utilisé en priorité lors d\'un conflit (ex : un segment peut bénéficier d\'un code promotion et l\'autre non, et l\'un de vos clients respecte les critères pour les deux).')?></p>
	</div>
	<?php } ?>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php

	/**	\file export-prices.php
	  *	Ce fichier est chargée d'exporter les tarifs d'un client au format Microsoft Excel.
 	  */

	require_once('users.inc.php');
	require_once('products.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

 	header('Cache-Control: private, no-store');
 	header('Pragma: cache');

	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment; filename="tarifs.xls"');

	$usr = isset($_GET['usr']) && is_numeric($_GET['usr']) ? $_GET['usr'] : $_SESSION['usr_id'];

	$mode = isset($_GET['mode']) ? $_GET['mode'] : 'references';
	if( $mode!='references' && $mode!='catalogue' ) $mode = 'references';

	print '
		<table>
		<thead>
			<tr>
				<th colspan="3">'._('TARIFS NET').'</th>
			</tr>
			<tr>
				<th>'._('Référence').'</th>
				<th>'._('Désignation').'</th>
				<th>'._('Prix Unitaire HT').'</th>
			</tr>
		</thead>
		<tbody>
	';

	// Sauvegarde l'environnement de l'utilisateur en cours
	$current_usr = isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : '';
	$current_disc = isset($_SESSION['usr_discount']) ? $_SESSION['usr_discount'] : '';
	$current_profile = isset($_SESSION['usr_prf_id']) ? $_SESSION['usr_prf_id'] : '';
	$current_prc = isset($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : '';

	// Remplace l'environnement de l'utilisateur par celui du client cible, pour que l'extraction du tarif soit correcte
	$user = ria_mysql_fetch_array(gu_users_get($usr));
	$_SESSION['usr_id'] = $user['id'];
	$_SESSION['usr_discount'] = $user['discount'];
	$_SESSION['usr_prf_id'] = $user['prf_id'];
	$_SESSION['usr_prc_id'] = $user['prc_id'];

	if( $mode=='references' ){

		$products = prd_products_get(0,'',0,true,0,0,-1,false,false,false,false,false,true,array('ref'=>'asc'));

		while( $p = ria_mysql_fetch_array($products) ){
			if( substr($p['ref'],0,2)!='RG' ){
				print '<tr>';
				print '<td>\''.$p['ref'].'</td>';
				print '<td>'.$p['name'].'</td>';
				print '<td>'.number_format($p['price_ht'],2,',',' ').'</td>';
				print '</tr>';
			}
		}

	}else{

		$categories = prd_categories_get(0,true);
		while( $cp = ria_mysql_fetch_array($categories) ){

			$childs = prd_categories_get(0,true,$cp['id']);
			while( $c = ria_mysql_fetch_array($childs) ){
				print '<tr>';
				print '<th colspan="3" align="left">'.$cp['title'].' - '.$c['title'].'</th>';
				print '</tr>';

				$products = prd_products_get(0,'',0,true,$c['id'],0,-1,false,false,false,false,false,true);

				while( $p = ria_mysql_fetch_array($products) ){
					if( substr($p['ref'],0,2)!='RG' ){
						print '<tr>';
						print '<td>'.$p['ref'].'</td>';
						print '<td>'.$p['name'].'</td>';
						print '<td>'.number_format($p['price_ht'],2,',',' ').'</td>';
						print '</tr>';
					}
				}
			}

		}

	}

	// Restore l'environnement de l'utilisateur en cours
	if ($current_usr != ''){
		$_SESSION['usr_id'] = $current_usr;
	}
	if ($current_disc != ''){
		$_SESSION['usr_discount'] = $current_disc;
	}
	if ($current_profile != ''){
		$_SESSION['usr_prf_id'] = $current_profile;
	}
	if ($current_prc != ''){
		$_SESSION['usr_prc_id'] = $current_prc;
	}

	print '
		</tbody>
		</table>
	';



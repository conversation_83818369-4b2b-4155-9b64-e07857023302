<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_NEGOTIATION_EDIT');

if (isset($_POST["add"])) {
	if ( isset($_POST["usr_fld_id"]) && $_POST["usr_fld_id"] > 0) {
		header('Location: /admin/customers/negotiations/new.php?usr_id='.$_POST["usr_fld_id"]);
	}else{
		header('Location: /admin/customers/negotiations/new.php');
	}
	exit;
}elseif (!isset($_GET["usr_id"]) && !isset($_GET["fld_id"]) && !isset($_REQUEST["gtr_id"])) {
	header('Location: /admin/customers/negotiations/index.php');
	exit;
}


require_once('devices.inc.php');
require_once('users.inc.php');
require_once('profiles.inc.php');
require_once('fields.inc.php');
require_once('prd/restrictions.admin.inc.php');

$saved = null;
$added = null;
$deleted = null;
$error_msg = null;
$gtr_id = 0;

if ( isset($_REQUEST["gtr_id"]) && $_REQUEST["gtr_id"] > 0 ) {
	$gtr_id = $_REQUEST["gtr_id"];
}


if ( isset($_GET["action"]) ) {

	if($_GET["action"] == "del_gtr"){
		$deleted = false;
		if (isset($_GET["gtr_id"]) || isset($_GET["usr_id"])) {
			if (gu_trading_rules_del( isset($_GET["gtr_id"]) ? $_GET["gtr_id"] : null, isset($_GET["usr_id"]) ? $_GET["usr_id"] : null)) {
				header('Location: /admin/customers/negotiations/index.php');
				exit;
			}
		}
	}else{
		// contrôle de price_value
		if ( !isset( $_POST["price_value"]) ) {
			$error_msg = _('La valeur de la réduction est obligatoire.').'<br>';
		}elseif( !isset($_POST["price_type"]) ){
			$error_msg = _('La selection du type de réduction est obligatoire.').'<br>';
		}elseif(is_numeric($_POST["price_value"])){
			$price_value = str_replace( array(",", ".", "%", " "), array( "", "", "", ""), $_POST["price_value"]);
		}else{
			$error_msg = _('La valeur de la réduction doit être un nombre décimal.').'<br>';
		}

		// contrôle discount
		if ( isset($_GET["action"]) ) {
			if ( isset($_POST["usr_id"], $_POST["rec_new_fld"], $_POST["gtr_value"], $_POST["price_fld_id"]) ) {

				if ($_POST["rec_new_fld"] < 0) {
					$rec_new_fld = null;
				}else{
					$rec_new_fld = $_POST["rec_new_fld"] ;
				}

				if ( $_POST["price_type"] == "percent" && !$error_msg) {
					$price_value = ( $price_value / 100 ) + 1;
				}

				if ( $_GET["action"] == "update_gtr") {
					$saved = false;
					if ( $gtr_id > 0 && !$error_msg ) {
						if ( gu_trading_rules_upd( $_POST["gtr_id"], $_POST["usr_id"], $rec_new_fld, $_POST["gtr_value"], $_POST["price_fld_id"], $price_value, $_POST["price_type"] ) ) {
							$saved = true;
						}
					}
				}else if( $_GET["action"] == "add_gtr" && !$error_msg){
					$added = false;
					if ( !$error_msg ) {
						$new_gtr_id = gu_trading_rules_add( $_POST["usr_id"], $rec_new_fld, $_POST["gtr_value"], $_POST["price_fld_id"], $price_value, $_POST["price_type"] );
						if ($new_gtr_id) {
							$gtr_id = $new_gtr_id;
							$added = true;
						}
					}
				}
			}
		}
	}
}
// Récupération du nom de l'utilisateur
if (isset($_REQUEST["usr_id"]) && $_REQUEST["usr_id"] != 0) {
	$ruser = gu_users_get($_REQUEST["usr_id"]);
	$user = ria_mysql_fetch_assoc($ruser);
	$user_title = sprintf(_('L\'utilisateur est %s %s'), $user["adr_firstname"], $user["adr_lastname"]);
}else{
	$user_title = _('Tous les utilisateurs');
}

// Si une action s'est correctement déroulé, on rédirige vers le contexte
if( ( $saved || $added || $deleted ) ){
	if ( isset($_REQUEST["usr_id"]) && is_numeric($_REQUEST["usr_id"]) && $_REQUEST["usr_id"] >= 0  ) {
		header('Location: /admin/customers/negotiations/edit.php?usr_id='.$_REQUEST["usr_id"]);
		exit;
	}else{
		header('Location: /admin/customers/negotiations/edit.php?usr_id=0');
		exit;
	}
}

Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( $is_yuto_essentiel ? _('Comptes') : _('Comptes clients'), '/admin/customers/index.php' )
	->push( _('Gestion des négociations'), '/admin/customers/negotiations/index.php' )
	->push( $user_title );


define('ADMIN_PAGE_TITLE', _('Gestion des négociations') . ' - ' . _('Comptes clients'));
require_once('admin/skin/header.inc.php');
require_once('./functions.php');

print '<h2>' . htmlspecialchars($user_title) . '</h2>';

// On test si une erreur est survenue, on affiche la nature de l'erreur
if($saved === false){
	print '<div class="notice error">'.$error_msg._('Les modifications n\'ont pas été enregistrées.').'</div>';
}else if($added === false){
	print '<div class="notice error">'.$error_msg._('L\'ajout de la règle a échouée.').'</div>';
}else if($deleted === false){
	print '<div class="notice error">'.$error_msg._('La règle n\'a pas pu être suprimée.').'</div>';
}


?>
<?php
if ( isset($_GET['gtr_id']) && $_GET['gtr_id'] > 0 ) {
	?>

	<table class="nocaption large">
		<tbody>
			<tr>
				<td id="td-contexte"><?php print _('Contexte :'); ?></td>
				<td>
					<select name="usr_fld_id" class="valign-center">

						<option id="rec-all" value="-1"><?php print _('Tous les utilisateurs')?></option>
						<option id="rec-usr" value="<?php print _FLD_USR_ID; ?>" <?php print isset($_GET['usr_id']) && $_GET['usr_id'] > 0 ? 'selected="selected"':'' ?>><?php print _('Utilisateur')?></option>
					</select>
					<span class="rec-lbl"><?php print _('égal à')?></span>
					<!-- client spécifique (moteur de recherche avec autocomplétion sur un champ texte) (si _FLD_USR_ID) -->
					<?php
					$email = '';
					$usr_id = '';
					if ( isset($_GET['usr_id']) && gu_users_exists($_GET['usr_id']) ) {
						$email = gu_users_get_email($_GET['usr_id']);
						$usr_id = $_GET['usr_id'];
					}
					 ?>
					<input class="rec-value value-rec-usr" type="text" name="usr_value_txt" value="<?php print $email; ?>"/>
					<input class="rec-action action-rec-usr" type="button" name="choose-rec-usr" id="choose-rec-usr" value="<?php print _('Choisir')?>" />
					<input class="val-autocomplete-usr" type="hidden" name="usr_id" value="<?php print $usr_id; ?>"/>
				</td>
			</tr>
		</tbody>
	</table>

	<?php
} ?>

<form action="edit.php?action=update_gtr" method="POST">
	<table class="checklist large">
		<?php
			print '<caption>'._('Conditions d\'accès aux règles de négociation').' : '.$user_title.' </caption>';
		?>
		<thead>
			<tr>
				<th id="td-contexte"><?php print _('Contexte')?></th>
				<th colspan="3"><?php print _('Règle')?></th>
			</tr>
		</thead>
		<tbody>
		<?php
		// Si un REQUEST gtr_id est présent alors un passe en mode édition
		if (  $gtr_id > 0  ) {
			// Recupération des règles de négociations
			$trading_rules = gu_trading_rules_get( -1, null, null, null, 0, false, $gtr_id, false);
			while ($tr = ria_mysql_fetch_assoc($trading_rules)) {
				print gu_trading_rules_form_view($tr["fld_id"], $tr["usr_id"], $tr["id"], $tr["price_fld_id"], $tr["price_value"], $tr["price_type"], view_formatted_value($tr), $tr['value'], false);
			}

		}else{
			$trading_rules = gu_trading_rules_get( $_REQUEST["usr_id"], null, null, null, 0, true, null, false);
			if ($trading_rules != null && ria_mysql_num_rows($trading_rules) > 0) {
				while ($tr = ria_mysql_fetch_assoc($trading_rules)) {
					print gu_trading_rules_form_view($tr["fld_id"], $tr["usr_id"], $tr["id"], $tr["price_fld_id"], $tr["price_value"], $tr["price_type"], view_formatted_value($tr), $tr['value'], true);
				}
			}

		}
		?>
		</tbody>
		<tfoot>
			<?php
				if ( $gtr_id != null ) {
					print '	<tr>
								<td colspan="4"><input type="submit" name="action_gtr" value="'._('Enregistrer').'" title="'._('Enregistrer les modifications et revenir à la liste').'" /></td>
							</tr>';
				}else{
					print '<tr><td colspan="4">';
					if ( isset($_GET["usr_id"]) && $_GET["usr_id"] > 0) {
						print ' <input type="hidden" name="usr_fld_id" value="'.$_GET["usr_id"].'">';
					}
					print '<input type="submit" name="add" value="'._('Ajouter').'" title="'._('Enregistrer les modifications et revenir à la liste').'" />
					</td></tr>';
				}
			 ?>
		</tfoot>
	</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
<?php 

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	if( !isset($_GET['msg']) ){
		exit;
	}
	
	$success = false;
	if( isset($_FILES) && sizeof($_FILES)>0 ){
		foreach($_FILES as $name => $file){
			if($file['error'] != UPLOAD_ERR_NO_FILE){
				$success = messages_files_upload($name);
			}
		}
	}
	
	define('ADMIN_PAGE_TITLE', 'value');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup_img');
	require_once('admin/skin/header.inc.php');
	
	if( isset($_GET['msg']) ){ 
		if( isset($success) )
			print '<script>parent.add_file("'.$success.'","'.$_GET['msg'].'"); </script>';
		
	}
?>
	<form id="cnt_add_file" action="join-file.php?msg=<?php print $_GET['msg']; ?>" method="post"  enctype="multipart/form-data">
		<input type="hidden" name="temp" value="passe2" />
		<div id="lstfile">
			<div class="input">
				<input class="upload-file" type="file" name="file0" onchange="submit_form()" />
			</div>
			<img class="loader" name="loader" src="/admin/images/loader_join_file.gif" style="display:none" alt="" />
			<div class="clear"></div>
		</div>		
	</form>
	<script>
		function submit_form(){
			$(".loader").show();
			$("#cnt_add_file").submit();
		}
	</script>
<?php
	require_once('admin/skin/footer.inc.php');	
?>
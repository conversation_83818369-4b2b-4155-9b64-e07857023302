<?php
	/** \page api-websites-index Sites internets
	 *		- \subpage api-websites-index-get
	 *		- \subpage api-websites-index-add
	 *		- \subpage api-websites-index-upd
	 *		- \subpage api-websites-index-del
	 *
	 *	\page api-websites-index-get Lister les sites internets
	 *	\code
	 *		php index.php --module website --action get
	 *	\endcode
	 *
	 *	Cette fonction retourne des sites internets.
	 *		\param \-\-wst_id Optionnel, identifiant d'un site internet
	 *		\param \-\-sort Optionnel, tri appliqué
	 *		\param \-\-tnt_id Optionnel, identifiant d'un tenant
	 *		\param \-\-wty_id Optionnel, identifiant d'un type de site
	 *
	 *	\return Liste des sites internets contenant les colonnes :
	 *		- tnt_id : Identifiant du locataire
	 *		- id : Identifiant du site
	 *		- name : Nom du site
	 *		- desc : Description du site
	 *		- ga_key : Code de tracking Google Analytics
	 * 		- gm_key : Clé Google Maps.
	 * 		- date-created : Date de création.
	 *		- site_title : contenu de la balise title
	 *		- meta_desc : contenu de la balise meta description
	 *		- meta_kwd : contenu de la balise meta keywords
	 *		- type_id : Identifiant du type de site
	 *		- type_name : Nom du type de site
	 *		- ord-detach : Détermine si le panier doit être détaché
	 *		- is_default : Détermine s'il s'agit du site principal du locataire (celui permettant généralement l'accès à l'interface d'administration, et surtout celui chargé par le fichier de synchronisation)
	 *
	 *	\page api-websites-index-add Ajouter un site internet
	 *	\code
	 *		php index.php --module website --action add --type_id={type_id} --name={name} [--desc={desc}]
	 *	\endcode
	 *
	 * 	Cette fonction permet de créer un nouveau site internet.
	 * 			\param \-\-tnt_id Obligatoire, identifiant d'un tenant
	 * 			\param \-\-type_id Obligatoire, identifiant d'un type de site
	 * 			\param \-\-name Obligatoire, nom donné au site
	 * 			\param \-\-desc Optionnel, description donnée au site
	 *
	 * 	\return Un tableau contenant :
	 * 			- id : identifiant du nouveau site
	 *
	 *	\page api-websites-index-upd Modifier un site internet
	 *	\code
	 *		php index.php --module website --action upd --wst_id={wst_id} --type_id={type_id} --name={name} [--desc={desc}]
	 *	\endcode
	 *
	 * 	Cette fonction permet de créer un nouveau site internet.
	 * 			\param \-\-wst_id Obligatoire, identifiant d'un site
	 * 			\param \-\-type_id Obligatoire, identifiant d'un type de site
	 * 			\param \-\-name Obligatoire, nom donné au site
	 * 			\param \-\-desc Optionnel, description donnée au site
	 *
	 * 	\return Un tableau contenant :
	 * 			- id : identifiant du nouveau site
	 *
	 *	\page api-websites-index-del Suppression un site internet
	 *	\code
	 *		php index.php --module website --action del
	 *	\endcode
	 *
	 * 	Cette fonction permet de supprimer un site internet.
	 * 			\param \-\-wst_id Obligatoire, identifiant du site internet
	 */

	require_once('tenants.inc.php');
	require_once('websites.inc.php');

	switch($api_action){
		case 'get': {
			if (!array_key_exists('wst_id', $api_params)) {
				$api_params['wst_id'] = 0;
			}

			if (!array_key_exists('sort', $api_params)) {
				$api_params['sort'] = false;
			}

			if (!array_key_exists('tnt_id', $api_params)) {
				$api_params['tnt_id'] = null;
			}

			if (!array_key_exists('wty_id', $api_params)) {
				$api_params['wty_id'] = false;
			}

			$r_website = wst_websites_get( $api_params['wst_id'], $api_params['sort'], $api_params['tnt_id'], false, $api_params['wty_id'] );

			if ($r_website && ria_mysql_num_rows($r_website)) {
				$api_result = true;

				while ($website = ria_mysql_fetch_assoc($r_website)) {
					$api_content[] = $website;
				}
			}

			break;
		}
		case 'add' : {
			if (
				!ria_array_key_exists(array('tnt_id', 'type_id', 'name'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['type_id']) || $api_params['type_id'] <= 0
				|| trim($api_params['name']) == ''
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('desc', $api_params)) {
				$api_params['desc'] = '';
			}

			$res = wst_websites_add($api_params['tnt_id'], $api_params['type_id'], $api_params['name'], $api_params['desc']);
			if (!$res) {
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la création du site internet : '.implode(' | ', $api_params));
				die(1);
			}

			$api_result = true;
			$api_content = array('id' => $res);

			break;
		}
		case 'upd' : {
			if (
				!ria_array_key_exists(array('wst_id', 'type_id', 'name'), $api_params)
				|| !is_numeric($api_params['wst_id']) || $api_params['wst_id'] <= 0
				|| !is_numeric($api_params['type_id']) || $api_params['type_id'] <= 0
				|| trim($api_params['name']) == ''
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('desc', $api_params)) {
				$api_params['desc'] = '';
			}var_dump($api_params['desc']);

			$res = wst_websites_update($api_params['wst_id'], $api_params['type_id'], $api_params['name'], $api_params['desc']);
			if (!$res) {
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la mise à jour du site internet : '.implode(' | ', $api_params));
				die(1);
			}

			$api_result = true;
			$api_content = array('id' => $res);

			break;
		}
		case 'del' : {
			if (!array_key_exists('wst_id', $api_params)) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (wst_websites_del($api_params['wst_id'])) {
				$api_result = true;
			}

			break;
		}
	}
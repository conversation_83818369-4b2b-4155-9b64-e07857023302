<?php

/** \page api-administrator-index Administrateurs
 *		- \subpage api-administrator-index-get
 *		- \subpage api-administrator-index-add
 *		- \subpage api-administrator-index-upd
 *		- \subpage api-administrator-index-del
 *
 *	\page api-administrator-index-get Lister les administrateurs
 *	\code
 *		php index.php --module administrator --action get --tnt_id={tnt_id}
 *	\endcode
 *
 *	Cette fonction retourne les informations de tous les administrateur. 
 *	Il est possible de récupérer les informations d'un administrateur en particulier en précisant son identifiant.
 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
 *		\param \-\-usr_id Optionnel, identifiant d'un administrateur
 *		\param \-\-sort Optionnel, applique un tri sur le résultat
 *	
 *	\return Liste des administrateurs contenant les colonnes :
 *		- id : Identifiant de l'utilisateur dans la base de données (ou tableau)
 *		- ref : code client dans le logiciel sage
 *		- title_id : identifiant de civilité
 *		- title_name : civilité de l'utilisateur (<PERSON>, <PERSON>, Mademoiselle)
 *		- adr_firstname : prénom de l'utilisateur
 *		- adr_lastname : nom de l'utilisateur
 *		- society : nom de la société (professionnels uniquement)
 *		- siret : numéro de siret
 *		- phone : numéro de téléphone
 *		- mobile : numéro de téléphone portable
 *		- fax : numéro de fax
 *		- work : numéro de téléphone dans la journée (travail)
 *		- address1 : première partie de l'adresse
 *		- address2 : deuxième partie de l'adresse
 *		- address3 : troisième partie de l'adresse
 *		- zipcode : code postal
 *		- city : ville de l'adresse de facturation
 *		- country : pays de l'adresse de facturation
 *		- cnt_code: code pays de l'adresse de facturation à 2 caractères
 *		- country_state : état / province de l'adresse
 *		- email : Adresse email de l'utilisateur
 *		- last_login : date/heure de dernière connexion de l'utilisateur
 *
 *	\page api-administrator-index-add Ajouter un administrateur
 *	\code
 *		php index.php --module administrator --action add --tnt_id={tnt_id} --title={title} --firstname={firstname} --lastname={lastname} --email={email}
 *	\endcode
 *
 *	Cette fonction permet d'ajouter un compte administrateur
 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
 *		\param \-\-title Obligatoire, civilité de l'administrateur
 *		\param \-\-email Obligatoire, adresse mail de l'administrateur
 *		\param \-\-firstname Obligatoire, prénom de l'administrateur
 *		\param \-\-lastname Obligatoire, nom de l'administrateur
 *		\param \-\-society Optionnel, société de l'administrateur
 *		\param \-\-siret Optionnel, siret de l'administrateur
 *		\param \-\-address1 Optionnel, numéro et nom de rue
 *		\param \-\-address2 Optionnel, complément d'adresse
 *		\param \-\-address3 Optionnel, complément d'adresse
 *		\param \-\-zipcode Optionnel, code postal
 *		\param \-\-city Optionnel, ville 
 *		\param \-\-country Optionnel, pays de l'adresse
 *		\param \-\-phone Optionnel, téléhpne de l'administrateur
 *		\param \-\-fax Optionnel, fax de l'administrateur
 *		\param \-\-mobile Optionnel, numéro de mobile de l'administrateur
 *		\param \-\-work Optionnel, numéro en journée de l'administrateur
 *		\param \-\-usr_ref Optionnel, référence de l'administrateur
 *		\param \-\-is_sync Optionnel, compte synchronisée (à false par défaut)
 *
 *
 *	\page api-administrator-index-upd Modifier un administrateur
 *	\code
 *		php index.php --module administrator --action upd --tnt_id={tnt_id} --usr_id={usr_id} --title={title} --firstname={firstname} --lastname={lastname} --email={email} --society={society} --siret={siret} --address1={address1} --zipcode={zipcode} --city={city} --country={country}
 *	\endcode
 *
 *	Cette fonction permet de mettre à jour un compte administrateur
 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
 *		\param \-\-usr_id Obligatoire, identifiant du compte administrateur
 *		\param \-\-title Obligatoire, civilité de l'administrateur
 *		\param \-\-firstname Obligatoire, prénom de l'administrateur
 *		\param \-\-lastname Obligatoire, nom de l'administrateur
 *		\param \-\-email Obligatoire, adresse mail de l'administrateur
 *		\param \-\-society Obligatoire, société de l'administrateur
 *		\param \-\-siret Obligatoire, siret de l'administrateur
 *		\param \-\-address1 Obligatoire, numéro et nom de rue
 *		\param \-\-zipcode Obligatoire, code postal
 *		\param \-\-city Obligatoire, ville 
 *		\param \-\-country Obligatoire, pays de l'adresse
 *		\param \-\-address2 Optionnel, complément d'adresse
 *		\param \-\-address3 Optionnel, complément d'adresse
 *		\param \-\-phone Optionnel, téléhpne de l'administrateur
 *		\param \-\-fax Optionnel, fax de l'administrateur
 *		\param \-\-mobile Optionnel, numéro de mobile de l'administrateur
 *		\param \-\-work Optionnel, numéro en journée de l'administrateur
 *		\param \-\-usr_ref Optionnel, référence de l'administrateur
 *		\param \-\-is_sync Optionnel, compte synchronisée (à false par défaut)
 *
 *	\page api-administrator-index-del Supprimer un administrateur
 *	\code
 *		php index.php --module administrator --action del --tnt_id={tnt_id} --usr_id={usr_id}
 *	\endcode
 *
 *	Cette fonction permet de supprimer un compte administrateur
 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
 *		\param \-\-usr_id Optionnel, identifiant d'un administrateur
 */

	require_once('users.inc.php');

	switch($api_action){
		case 'get': {
			if (
				!array_key_exists('tnt_id', $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			$sort = $dir = false;
			if (array_key_exists('sort', $api_params) && is_array($api_params['sort']) && count($api_params['sort'] == 2)) {
				foreach ($api_params['sort'] as $key => $value) {
					$sort = $key;
					$dir  = $value;
					break;
				}
			}

			if (!array_key_exists('usr_id', $api_params)) {
				$api_params['usr_id'] = 0;
			}
			
			$config = array( 'tnt_id' => $api_params['tnt_id'], 'i18n_lng' => 'fr' );
			$r_admin = gu_users_get($api_params['usr_id'], '', '', PRF_ADMIN, '', 0, '', $sort, $dir);
			
			if ($r_admin) {
				while ($admin = ria_mysql_fetch_assoc($r_admin)) {
					$api_content[] = array(
						'id' => $admin['id'],
						'ref' => $admin['ref'],
						'title_id' => $admin['title_id'],
						'title_name' => $admin['title_name'],
						'firstname' => $admin['adr_firstname'],
						'lastname' => $admin['adr_lastname'],
						'society' => $admin['society'],
						'siret' => $admin['siret'],
						'phone' => $admin['phone'],
						'mobile' => $admin['mobile'],
						'fax' => $admin['fax'],
						'work' => $admin['work'],
						'address1' => $admin['address1'],
						'address2' => $admin['address2'],
						'address3' => $admin['address3'],
						'zipcode' => $admin['zipcode'],
						'city' => $admin['city'],
						'country' => $admin['country'],
						'cnt_code' => $admin['cnt_code'],
						'country_state' => $admin['country_state'],
						'email' => $admin['email'],
						'last_login' => $admin['last_login']
					);
				}

				$api_result = true;
			}
			break;
		}
		case 'add' : {
			if (
				!ria_array_key_exists(array('tnt_id', 'title', 'firstname', 'lastname'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['title']) || $api_params['title'] <= 0
				|| trim($api_params['email']) == ''
				|| trim($api_params['firstname']) == ''
				|| trim($api_params['lastname']) == ''
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('password', $api_params)) {
				$api_params['password'] = null;
			}

			if (!array_key_exists('society', $api_params)) {
				$api_params['society'] = '';
			}

			if (!array_key_exists('siret', $api_params)) {
				$api_params['siret'] = '';
			}

			if (!array_key_exists('address1', $api_params)) {
				$api_params['address1'] = '';
			}

			if (!array_key_exists('address2', $api_params)) {
				$api_params['address2'] = '';
			}

			if (!array_key_exists('zipcode', $api_params)) {
				$api_params['zipcode'] = '';
			}

			if (!array_key_exists('city', $api_params)) {
				$api_params['city'] = '';
			}

			if (!array_key_exists('country', $api_params)) {
				$api_params['country'] = '';
			}

			if (!array_key_exists('phone', $api_params)) {
				$api_params['phone'] = '';
			}

			if (!array_key_exists('fax', $api_params)) {
				$api_params['fax'] = '';
			}

			if (!array_key_exists('mobile', $api_params)) {
				$api_params['mobile'] = '';
			}

			if (!array_key_exists('work', $api_params)) {
				$api_params['work'] = '';
			}

			if (!array_key_exists('address3', $api_params)) {
				$api_params['address3'] = '';
			}

			if (!array_key_exists('usr_ref', $api_params)) {
				$api_params['usr_ref'] = '';
			}

			if (!array_key_exists('is_sync', $api_params)) {
				$api_params['is_sync'] = false;
			}else{
				$api_params['is_sync'] = api_monitoring_var_boolean( $api_params['is_sync'] );
			}

			$config = array( 'tnt_id' => $api_params['tnt_id'], 'wst_id' => tnt_tenants_get_website_default($api_params['tnt_id']), 'i18n_lng' => 'fr', 'notify_new_user' => false );
			
			$res = gu_users_add_with_adresse($api_params['email'], 3, $api_params['title'], $api_params['firstname'], $api_params['lastname'], $api_params['password'], PRF_ADMIN, $api_params['usr_ref'], $api_params['is_sync'], 0, 0, null, false, $api_params['society'], $api_params['siret'], $api_params['address1'], $api_params['address2'], $api_params['zipcode'], $api_params['city'], $api_params['country'], $api_params['phone'], $api_params['fax'], $api_params['mobile'], $api_params['work'], '', '', true, $api_params['address3'], '');
			var_dump($res);
			if ($res) {
				$api_content = array(
					'id' => $res
				);

				$api_result = true;
			}

			break;
		}
		case 'upd' : {
			if (
				!ria_array_key_exists(array('tnt_id', 'usr_id', 'title', 'firstname', 'lastname'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['usr_id']) || $api_params['usr_id'] <= 0
				|| !is_numeric($api_params['title']) || $api_params['title'] <= 0
				|| trim($api_params['email']) == ''
				|| trim($api_params['firstname']) == ''
				|| trim($api_params['lastname']) == ''
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('password', $api_params)) {
				$api_params['password'] = null;
			}

			if (!array_key_exists('society', $api_params)) {
				$api_params['society'] = '';
			}

			if (!array_key_exists('siret', $api_params)) {
				$api_params['siret'] = '';
			}

			if (!array_key_exists('address1', $api_params)) {
				$api_params['address1'] = '';
			}

			if (!array_key_exists('address2', $api_params)) {
				$api_params['address2'] = '';
			}

			if (!array_key_exists('zipcode', $api_params)) {
				$api_params['zipcode'] = '';
			}

			if (!array_key_exists('city', $api_params)) {
				$api_params['city'] = '';
			}

			if (!array_key_exists('country', $api_params)) {
				$api_params['country'] = '';
			}

			if (!array_key_exists('phone', $api_params)) {
				$api_params['phone'] = '';
			}

			if (!array_key_exists('fax', $api_params)) {
				$api_params['fax'] = '';
			}

			if (!array_key_exists('mobile', $api_params)) {
				$api_params['mobile'] = '';
			}

			if (!array_key_exists('work', $api_params)) {
				$api_params['work'] = '';
			}

			if (!array_key_exists('address3', $api_params)) {
				$api_params['address3'] = '';
			}

			if (!array_key_exists('usr_ref', $api_params)) {
				$api_params['usr_ref'] = '';
			}

			if (!array_key_exists('is_sync', $api_params)) {
				$api_params['is_sync'] = false;
			}else{
				$api_params['is_sync'] = api_monitoring_var_boolean( $api_params['is_sync'] );
			}

			$config = array( 'tnt_id' => $api_params['tnt_id'], 'wst_id' => tnt_tenants_get_website_default($api_params['tnt_id']), 'i18n_lng' => 'fr', 'notify_new_user' => false );
			
			if ($api_params['password'] !== null) {
				if (!gu_users_update($api_params['usr_id'], $api_params['email'], $api_params['password'])) {
					break;
				}
			}else{
				if (!gu_users_update_email($api_params['usr_id'], $api_params['email'])) {
					break;
				}
			}
			
			$adr = gu_users_get_adr_invoices($api_params['usr_id']);
			if (!is_numeric($adr) || $adr <= 0) {
				break;
			}

			if (!gu_adresses_update($api_params['usr_id'], $adr, 3, $api_params['title'], $api_params['firstname'], $api_params['lastname'], $api_params['society'], $api_params['siret'], $api_params['address1'], $api_params['address2'], $api_params['zipcode'], $api_params['city'], $api_params['country'], $api_params['phone'], $api_params['fax'], $api_params['mobile'], $api_params['work'], false, null, null, null, $api_params['address3'], null, null)) {
				break;
			}

			$api_result = true;
			break;
		}
		case 'del' : {
			if (
				!ria_array_key_exists(array('tnt_id', 'usr_id'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['usr_id']) || $api_params['usr_id'] <= 0
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			$config = array( 'tnt_id' => $api_params['tnt_id'], 'wst_id' => tnt_tenants_get_website_default($api_params['tnt_id']), 'i18n_lng' => 'fr' );

			if (gu_users_exists($api_params['usr_id'], PRF_ADMIN)) {
				if (gu_users_del($api_params['usr_id'])) {
					$api_result = true;
				}
			}

			break;
		}
	}
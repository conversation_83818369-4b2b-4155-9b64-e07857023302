<?php

/** \page api-tenants-index Tenants
 *		- \subpage api-tenants-index-get
 *		- \subpage api-tenants-index-add
 *		- \subpage api-tenants-index-upd
 *		- \subpage api-tenants-index-del
 *		- \subpage api-tenants-index-set-leased
 *		- \subpage api-tenants-index-get-sub-devices
 *
 *	\page api-tenants-index-get Lister les tenants
 *	\code
 *		php index.php --module tenant --action get
 *	\endcode
 *
 *	Cette fonction retourne des tenants.
 *		\param \-\-tnt_id Optionnel, identifiant d'un tenant
 *		\param \-\-sync_only Optionnel, par défaut à false, mettre true pour récupérer que les tenants ayant une synchronisation
 *		\param \-\-sort Optionnel, tri appliqué (ex. "name/asc" ou "name/asc|sync/desc")
 *		\param \-\-is_leased Optionnel, ....
 *	
 *	\return Liste des tenants contenant les colonnes :
 *		- id, identifiant du locataire
 *		- name, nom du locataire
 *		- address1, adresse du locataire
 *		- address2, complément d'adresse du locataire
 *		- zipcode, code postal du locataire
 *		- city, ville du locataire
 *		- phone, téléphone du locataire
 *		- fax, fax du locataire
 *		- email, email du locataire
 *		- date-created, date de création du locataire
 *		- date-modified, date de dernière modification du locataire
 *		- last-sync, date de dernière synchronisation du locataire
 *		- token, Clé d'utilisation de RiaShopSync
 *		- wst_default, identifiant du site principal
 *		- sync_update, détermine si le locataire bénéficie des MAJ de RiaShopSync
 *		- sync_version, numéro de version de RiaShopSync actuellement installée
 *		- sync_reboot, détermine si un redémarrage du logiciel de synchronisation a été demandé
 *
 *	\page api-tenants-index-add Ajouter un tenant
 *	\code
 *		php index.php --module tenant --action add --name={name} --address1={address1} --zipcode={zipcode} --city={city}
 *	\endcode
 *
 * 	Cette fonction permet de créer un nouveau tenant.
 * 		\param \-\-name Obligatoire, nom du tenant
 * 		\param \-\-address1 Obligatoire, adresse du tenant
 * 		\param \-\-zipcode Obligatoire, code postal du tenant
 * 		\param \-\-city Obligatoire, ville du tenant
 * 		\param \-\-address2 Optionnel, complément d'adresse
 * 		\param \-\-email Optionnel, email de contact
 * 		\param \-\-phone Optionnel, numéro de téléphone
 * 		\param \-\-fax Optionnel, numéro de fax
 * 		\param \-\-use_sync Optionnel, utilise une synchronisation
 * 
 * 	\return Un tableau contenant :
 * 		- id : identifiant du nouveau tenant
 * 
 *	\page api-tenants-index-upd Modifier un tenant
 *	\code
 *		php index.php --module tenant --action upd --tnt_id={id} --name={name}
 *	\endcode
 *
 * 	Cette fonction permet de mettre à jour un tenant.
 * 		\param \-\-tnt_id Obligatoire, identifiant du tenant
 * 		\param \-\-name Optionnel, nom du tenant
 * 		\param \-\-address1 Optionnel, adresse du tenant
 * 		\param \-\-zipcode Optionnel, code postal du tenant
 * 		\param \-\-city Optionnel, ville du tenant
 * 		\param \-\-address2 Optionnel, complément d'adresse
 * 		\param \-\-email Optionnel, email de contact
 * 		\param \-\-phone Optionnel, numéro de téléphone
 * 		\param \-\-fax Optionnel, numéro de fax
 * 		\param \-\-use_sync Optionnel, utilise une synchronisation
 *
 *	\page api-tenants-index-del Supprimer un tenant
 *	\code
 *		php index.php --module tenant --action del --tnt_id={id}
 *	\endcode
 *
 * 	Cette fonction permet de supprimer un tenant.
 * 		\param \-\-tnt_id Obligatoire, identifiant du tenant
 *
 *	\page api-tenants-index-set-leased Modifier l'engagement
 *	\code
 *		php index.php --module tenant --action set_leased --tnt_id={id} --is_leased={is_leased}
 *	\endcode
 *
 * 	Cette fonction permet de dire si un tenant est sous engagement ou non.
 * 		\param \-\-tnt_id Obligatoire, identifiant du tenant
 * 		\param \-\-is_leased Obligatoire, si oui ou non le tenant est sous engagement
 *
 *	\page api-tenants-index-get-sub-devices Informations sur les tablettes
 *	\code
 *		php index.php --module tenant --action get_sub_devices --tnt_id={id}
 *	\endcode
 *
 * 	Cette fonction permet de récupérer les informations sur les tablettes.
 * 	Deux informations seront retournées : 
 * 		- used : nombre de tablettes actives
 * 		- max : nombre de tablettes autorisés au moment de l'appel
 * 	
 * 		\param \-\-tnt_id Obligatoire, identifiant du tenant
 */

	require_once('tenants.inc.php');
	require_once('devices.inc.php');

	switch($api_action){
		case 'get': {
			if (!array_key_exists('tnt_id', $api_params)) {
				$api_params['tnt_id'] = 0;
			}
			
			if (!array_key_exists('sync_only', $api_params)) {
				$api_params['sync_only'] = false;
			}
			
			if (!array_key_exists('sort', $api_params) || !is_array($api_params['sort']) || !count($api_params['sort'])) {
				$api_params['sort'] = false;
			}
			
			if (!array_key_exists('is_leased', $api_params)) {
				$api_params['is_leased'] = null;
			}else{
				$api_params['is_leased'] = api_monitoring_var_boolean($api_params['is_leased']);
			}

			$r_tenant = tnt_tenants_get($api_params['tnt_id'], $api_params['sync_only'], $api_params['sort'], $api_params['is_leased']);
			if ($r_tenant) {
				$api_result = true;
				
				while ($tenant = ria_mysql_fetch_assoc($r_tenant)) {
					$api_content[] = $tenant;
				}
			}
			break;
		}
		case 'add' : {
			if (!ria_array_key_exists(array('name', 'address1', 'zipcode', 'city'), $api_params)) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (trim($api_params['name']) == '' || trim($api_params['address1']) == '' || trim($api_params['zipcode']) == '' || trim($api_params['city']) == '') {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('address2', $api_params)) {
				$api_params['address2'] = '';
			}
			if (!array_key_exists('email', $api_params)) {
				$api_params['email'] = '';
			}
			if (!array_key_exists('phone', $api_params)) {
				$api_params['phone'] = '';
			}
			if (!array_key_exists('fax', $api_params)) {
				$api_params['fax'] = '';
			}
			if (!array_key_exists('use_sync', $api_params)) {
				$api_params['use_sync'] = true;
			}else{
				$api_params['use_sync'] = api_monitoring_var_boolean($api_params['use_sync']);
			}
			
			$res = tnt_tenants_add($api_params['name'], $api_params['address1'], $api_params['address2'], $api_params['zipcode'], $api_params['city'], $api_params['email'], $api_params['phone'], $api_params['fax'], $api_params['use_sync']);

			if (!$res) {
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la création du tenant : '.implode(' | ', $api_params));
				die(1);
			}

			$api_result = true;
			$api_content = array('id' => $res);

			break;
		}
		case 'upd' : {
			if (!array_key_exists('tnt_id', $api_params) || !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}
			if (!array_key_exists('name', $api_params) || trim($api_params['name']) == '') {
				$api_params['name'] = null;
			}

			if (!array_key_exists('address1', $api_params)) {
				$api_params['address1'] = null;
			}

			if (!array_key_exists('zipcode', $api_params)) {
				$api_params['zipcode'] = null;
			}

			if (!array_key_exists('city', $api_params)) {
				$api_params['city'] = null;
			}

			if (!array_key_exists('address2', $api_params)) {
				$api_params['address2'] = null;
			}

			if (!array_key_exists('email', $api_params)) {
				$api_params['email'] = null;
			}

			if (!array_key_exists('phone', $api_params)) {
				$api_params['phone'] = null;
			}

			if (!array_key_exists('fax', $api_params)) {
				$api_params['fax'] = null;
			}

			if (!array_key_exists('use_sync', $api_params)) {
				$api_params['use_sync'] = null;
			}else{
				$api_params['use_sync'] = api_monitoring_var_boolean($api_params['use_sync']);
			}
			
			$res = tnt_tenants_update($api_params['tnt_id'], $api_params['name'], $api_params['address1'], $api_params['address2'], $api_params['zipcode'], $api_params['city'], $api_params['phone'], $api_params['fax'], $api_params['email'], $api_params['use_sync']);

			if (!$res) {
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la mise à jour du tenant : '.implode(' | ', $api_params));
				die(1);
			}

			$api_result = true;
			break;
		}
		case 'del' : {
			if (!array_key_exists('tnt_id', $api_params) || !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0) {
				error_log(__FILE__.':'.__LINE__.' => Paramètre obligatoire omis ou faux');
				die(1);
			}

			if (!tnt_tenant_del($api_params['tnt_id'])) {
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la suppression du tenant : '.implode(' | ', $api_params));
				die(1);
			}

			$api_result = true;
			break;
		}
		case 'set_leased': {
			if (
				!array_key_exists('tnt_id', $api_params) 
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !array_key_exists('is_leased', $api_params)
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}
			
			if (tnt_tenants_set_is_leased($api_params['tnt_id'], api_monitoring_var_boolean($api_params['is_leased']))) {
				$api_result = true;
			}

			break;
		}
		case 'get_sub_devices' : {
			if (!array_key_exists('tnt_id', $api_params) || !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0) {
				error_log(__FILE__.':'.__LINE__.' => Paramètre obligatoire omis ou faux');
				die(1);
			}

			$config = array( 'tnt_id' => $api_params['tnt_id'] );

			$r_used = dev_devices_get(0, 0, '', -1, '=', false, false, true, true, 0, false, false);
			
			$api_content = array(
				'used' => ($r_used ? ria_mysql_num_rows($r_used) : 0),
				'max' => dev_subscribtions_get_max()
			);
			
			$api_result = true;
			break;
		}
	}
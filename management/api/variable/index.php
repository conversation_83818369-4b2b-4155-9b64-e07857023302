<?php
	/** \page api-variables-index Variables de configuration
	 *		- \subpage api-variables-index-get
	 *
	 *	\page api-variables-index-get Lister les variables de configuration
	 *	\code
	 *		php index.php --module variable --action get [--code={code}] [--is_system={bool}]
	 *	\endcode
	 *
	 *	Cette fonction retourne toutes les variables de configuration présentes dans RiaShop avec leur valeur par défaut.
	 *		\param \-\-code Optionnel, code d'une variable
	 *		\param \-\-is_system Optionnel, si oui ou non on récupère les variables systèmes (null par défaut, toutes les variables)
	 *	
	 *	\return Liste des variables contenant les colonnes :
	 *				- code : Code de la variable (identifiant)
	 *				- name : Nom de la variable
	 *				- desc : Description de la variable
	 *				- default : Valeur par défaut de la variable
	 *				- type : Identifiant du type de données (fld_types)
	 *				- parent_code : Code de la variable parente
	 *				- cls_id : classe d'objet liée à cette variable
	 *				- obj_cls_id : classe d'objets liée à la classe liée à la variable (ex. Modèle lié aux produits)
	 */
	require_once('cfg.variables.inc.php');

	switch($api_action){
		case 'get': {
			if (!array_key_exists('code', $api_params)) {
				$api_params['code'] = '';
			}

			if (!array_key_exists('is_system', $api_params)) {
				$api_params['is_system'] = null;
			}else{
				$api_params['is_system'] = api_monitoring_var_boolean($api_params['is_system']);
			}

			$r_var = cfg_variables_get($api_params['code'], array(), false, null);
			if ($r_var) {
				while ($var = ria_mysql_fetch_assoc($r_var)) {
					$api_content[] = $var;
				}
			}

			$api_result = true;
			break;
		}
	}
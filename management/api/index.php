<?php
	/** \mainpage Introduction
	*
	*	Cette documentation décrit le fonctionnement de l'API de Monitoring des différentes installations RiaShop.
	*
	*	Le fonctionnement typique se fait via le passage de deux paramètres :
	*		- \c module : module RiaShop sur lequel une action doit être effectué (produits, commandes, clients, etc...)
	*		- \c action : type d'action à réaliser sur un ou plusieurs éléments du module (ajout, suppression, mise à jour, récupération)
	*
	*	La ligne de commande prend la forme php index.php --module {module} --action {action}.
	*/

	set_include_path(dirname(__FILE__) . '/../../include/');

	require('strings.inc.php');

	{ // Initialisation des variables
		$api_result = false;
		$api_module = $api_action = '';
		$api_params = $api_content = array();

		$longopts = array(
			'module:', 'action:',
			'tnt_id::', 'sync_only::', 'sort::', 'is_leased::', 
			'email::', 'phone::', 'fax::', 'mobile::', 'work::', 'use_sync::', 
			'name::', 'address1::', 'address2::', 'address3::', 'zipcode::', 'city::', 'country::', 
			'society::', 'siret::',
			'wst_id::', 'wty_id::', 'type_id::', 'desc::', 
			'code::', 'value::',
			'usr_id::', 'title::', 'firstname::', 'lastname::', 
			'usr_ref::', 'is_sync::'
		);
	}

	{ // Récupération des paramètres
		$ar_params = getopt( '', $longopts );
		
		if (!ria_array_key_exists(array('module', 'action'), $ar_params)) {
			die('1');
		}
		
		$api_module = $ar_params['module']; 
		unset($ar_params['module']);
		$api_action = $ar_params['action']; 
		unset($ar_params['action']);

		$api_params = $ar_params;

		if (array_key_exists('sort', $api_params)) {
			$sort = false;

			$temp_sort = preg_split('/\|/i', $api_params['sort']);
			
			if (is_array($temp_sort)) {
				foreach ($temp_sort as $temp) {
					$temp = preg_split('/\//i', $temp);
					
					if (is_array($temp) && count($temp) == 2 && in_array($temp[1], array('asc', 'desc'))) {
						$sort[$temp[0]] = $temp[1];
					}
				}
			}

			$api_params['sort'] = $sort;
		}
	}
	
	$file = dirname(__FILE__).'/'.$api_module.'/index.php';
	if (!file_exists($file)) {
		die('1');
	}

	include($file);

	$json = array(
		'result' => $api_result,
		'content' => $api_content
	);

	print json_encode( $json );

	function api_monitoring_var_boolean($var){
		switch (true) {
			case $var === 'true':
				$var = true;
				break;
			case $var === 'false':
				$var = false;
				break;
			case $var === 'null':
				$var = null;
				break;
		}

		return $var;
	}

{"name": "simplesamlphp/simplesamlphp-module-metarefresh", "description": "The metarefresh module will download and parse metadata documents and store them locally", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "metarefresh"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\metarefresh\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-metarefresh/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-metarefresh"}}
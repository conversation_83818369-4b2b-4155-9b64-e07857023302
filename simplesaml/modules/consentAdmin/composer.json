{"name": "simplesamlphp/simplesamlphp-module-consentadmin", "description": "A module that allows users to manage their consent", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "<PERSON><PERSON><PERSON>"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-consent": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "extra": {"ssp-mixedcase-module-name": "consent<PERSON>dmin"}, "support": {"issues": "https://github.com/simplesamlphp/simplesamlphp-module-consentadmin/issues", "source": "https://github.com/simplesamlphp/simplesamlphp-module-consentadmin"}}
{% set pagetitle = '{multiauth:multiauth:select_source_header}'|trans %}
{% extends "base.twig" %}

{% block content %}
    <h2>{{ '{multiauth:multiauth:select_source_header}'| trans }}</h2>
    <p>{{ '{multiauth:multiauth:select_source_text}'| trans }}</p>

    <form action="{{ selfUrl|escape('html') }}" method="get">
        <input type="hidden" name="AuthState" value="{{ authstate|escape('html') }} ">
        <ul>
        {% for key, source in sources %}
            {% set name = ('src-' ~ source.source64) %}
            {% set button = ('button-' ~ source.source) %}
	    <li class="{{ source.css_class|escape('html') }} authsource">
            <input type="submit" name="{{ name|escape('html') }}" id="{{ button|escape('html') }}" value="{{ source.text|escape('html') }}"{%- if source.source == preferred %} autofocus{% endif -%}>
            {% if source.help %}
              <p>{{ source.help|escape('html') }}</p>
            {% endif %}
            </li>
        {% endfor %}
        </ul>
    </form>
{% endblock %}


#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: zh_Hant_TW\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{cron:cron:cron_report_title}"
msgstr "排程報告"

msgid "{cron:cron:ran_text}"
msgstr "排程任務執行於"

msgid "{cron:cron:cron_suggestion}"
msgstr "這是建議的 crontab 檔案："

msgid "{cron:cron:run_text}"
msgstr "執行排程："

msgid "{cron:cron:cron_execution}"
msgstr "點此執行排程任務："

msgid "{cron:cron:cron_info}"
msgstr "Cron 在 Unix 系統中，可用來執行例行性任務。"

msgid "{cron:cron:cron_result_title}"
msgstr "這是排程工作執行結果："

msgid "{cron:cron:cron_header}"
msgstr "排程結果頁面"

msgid "Cron is a way to run things regularly on unix systems."
msgstr "Cron 在 Unix 系統中，可用來執行例行性任務。"

msgid "Run cron:"
msgstr "執行排程："

msgid "Cron ran at"
msgstr "排程任務執行於"

msgid "Cron report"
msgstr "排程報告"

msgid "Here is a suggestion for a crontab file:"
msgstr "這是建議的 crontab 檔案："

msgid "Click here to run the cron jobs:"
msgstr "點此執行排程任務："

msgid "Here are the result for the cron job execution:"
msgstr "這是排程工作執行結果："

msgid "Cron result page"
msgstr "排程結果頁面"


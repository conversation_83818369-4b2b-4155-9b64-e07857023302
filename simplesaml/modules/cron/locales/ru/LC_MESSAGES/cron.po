
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ru\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{cron:cron:cron_report_title}"
msgstr "Отчёт Cron"

msgid "{cron:cron:ran_text}"
msgstr "Cron запущен в "

msgid "{cron:cron:cron_suggestion}"
msgstr "Указания для файла crontab:"

msgid "{cron:cron:run_text}"
msgstr "Запустить Cron"

msgid "{cron:cron:cron_execution}"
msgstr "Нажмите, чтобы выполнить задания Cron:"

msgid "{cron:cron:cron_info}"
msgstr "Cron это способ регулярно выполнять задания на системах UNIX."

msgid "{cron:cron:cron_result_title}"
msgstr "Результат выполнения заданий Cron:"

msgid "{cron:cron:cron_header}"
msgstr "Страница результата Cron"

msgid "Cron is a way to run things regularly on unix systems."
msgstr "Cron это способ регулярно выполнять задания на системах UNIX."

msgid "Run cron:"
msgstr "Запустить Cron"

msgid "Cron ran at"
msgstr "Cron запущен в "

msgid "Cron report"
msgstr "Отчёт Cron"

msgid "Here is a suggestion for a crontab file:"
msgstr "Указания для файла crontab:"

msgid "Click here to run the cron jobs:"
msgstr "Нажмите, чтобы выполнить задания Cron:"

msgid "Here are the result for the cron job execution:"
msgstr "Результат выполнения заданий Cron:"

msgid "Cron result page"
msgstr "Страница результата Cron"


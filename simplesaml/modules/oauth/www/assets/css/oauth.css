table.formtable {
    width: 100%;
}
table.formtable tr td.name {
    text-align: right;
    vertical-align: top;
    padding-right: .6em;
}
table.formtable tr td.value {
    text-align: left;
    padding: 0px;
}
table.formtable tr td.header {
    padding-left: 5px;
    padding-top: 8px;
    font-weight: bold;
    font-size: 110%;
}

table.formtable tr td input,table.formtable tr td textarea {
    width: 90%;
    border: 1px solid #bbb;
    margin: 2px 5px;
    padding: 2px 4px;
}

table.metalist {
    border: 1px solid #aaa;
    border-collapse: collapse;
}
table.metalist tr td {
    padding: 2px 5px;
}
table.metalist tr.even td {
    background: #e5e5e5;
}

@media all {
    div#content {
        margin: .4em ! important;
    }

    form {
        display: inline;
    }

    ul.tabset_tabs {
        margin: 0px;
        padding: 0px;
        list-style: none;
    }

    ul.tabset_tabs li {
        background: none;
        color: #222;
        display: inline-block;
        padding: 10px 15px;
        cursor: pointer;
    }

    ul.tabset_tabs li.current {
        background: #ededed;
        color: #222;
    }

    .tabset_content {
        display: none;
        background: #ededed;
        padding: 15px;
    }

    .tabset_content.current {
        display: inherit;
    }

    #graph img {
        max-width: 77%;
        height: auto;
    }
    #table img {
        max-width: 77%;
        height: auto;
    }
}

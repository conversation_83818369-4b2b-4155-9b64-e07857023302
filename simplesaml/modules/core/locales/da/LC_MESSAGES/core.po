
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: da\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHPinfo"

msgid "{core:no_state:report_text}"
msgstr ""
"Hvis dette problem fortsætter, kan du rapportere det til "
"systemadministratoren."

msgid "{core:no_state:cause_backforward}"
msgstr "Brug frem- og tilbage-knappen i browseren."

msgid "{core:no_metadata:not_found_for}"
msgstr "Vi kan ikke finde metadata for denne forbindelse:"

msgid "{core:frontpage:link_shib13example}"
msgstr ""
"Shibboleth 1.3 SP eksempel - test indlogning med Shibboleth 1.3 via din "
"IdP"

msgid "{core:no_state:suggestions}"
msgstr "Løsningsforslag til problemet:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Login som administrator"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"Vi har opdaget at det kun er få sekunder siden du sidst autentificerede "
"dig op mod denne service. Vi antager derfor at der er et problem med "
"services."

msgid "{core:frontpage:link_doc_sp}"
msgstr "SimpleSAMLphp som en tjenesteudbyder (SP)"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Hosted SAML 2.0 tjenesteudbyder metadata (automatisk genereret)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID Provider side - Alpha version (testkode)"

msgid "{core:frontpage:link_doc_install}"
msgstr "Installationsvejledning for SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Diagnosticér hostnavn, port og protokol"

msgid "{core:no_state:suggestion_goback}"
msgstr "Gå tilbage til forrige side og prøv igen."

msgid "{core:no_state:causes}"
msgstr "Fejlen kan være forårsaget af:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Hosted SAML 2.0 identitetsudbyder metadata (automatisk genereret)"

msgid "{core:frontpage:optional}"
msgstr "Valgfrit"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "Hosted Shibboleth 1.3 tjenesteudbyder metadata (automatisk genereret)"

msgid "{core:frontpage:doc_header}"
msgstr "Dokumentation"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "SimpleSAMLphp Avancerede Indstillinger"

msgid "{core:frontpage:required_ldap}"
msgstr "Påkrævet for LDAP"

msgid "{core:frontpage:warnings_secretsalt}"
msgstr ""
"<strong>Opsætningen benytter standard 'secret salt'</strong> - sørg for "
"at ændre standard indstillingen for 'secretsalt' i simpleSAML opsætningen"
" i produktionssystemer. [<a href=\"https://simplesamlphp.org/docs/stable"
"/simplesamlphp-install\">Læs mere om SimpleSAMLphp opsætning.</a> ]"

msgid "{core:frontpage:authtest}"
msgstr "Test konfigureret autentificeringskilde"

msgid "{core:frontpage:link_meta_overview}"
msgstr "Oversigt over metadata for din installation. Check metadatafilerne her"

msgid "{core:frontpage:configuration}"
msgstr "Konfiguration"

msgid "{core:frontpage:welcome}"
msgstr "Velkommen"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "Konfigurer en Shibboleth 1.3 SP til at virke mod en SimpleSAMLphp IdP"

msgid "{core:no_state:header}"
msgstr "Tilstandsinformation forsvundet"

msgid "{core:frontpage:metadata_header}"
msgstr "Metadata"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "SimpleSAMLphp Konfiguration og Vedligeholdelse"

msgid "{core:frontpage:link_configcheck}"
msgstr "SimpleSAMLphp konfigurationscheck"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp installationsside"

msgid "{core:no_cookie:header}"
msgstr "Mangler cookie"

msgid "{core:frontpage:warnings}"
msgstr "Advarsler"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML til SimpleSAMLphp metadata oversætter"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "Slet mit valg i listen af identitetsudbydere"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Du er logget ind som administrator"

msgid "{core:frontpage:auth}"
msgstr "Autentificering"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Hvis du har modtaget denne fejlbesked efter at have klikket på et lilnk, "
"skal du rappoterer fejlen til ejeren af siden. "

msgid "{core:no_state:description}"
msgstr "Tilstandsinformation for igangværende request kan ikke findes"

msgid "{core:frontpage:show_metadata}"
msgstr "Vis metadata"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Luk din browser og prøv igen."

msgid "{core:short_sso_interval:warning_header}"
msgstr "For kort interval mellem single sign on hændelse."

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Tillykke</strong>, du har nu installeret SimpleSAMLphp. Dette er "
"startsiden til installationen, hvor du vil finde eksempler, diagnostik, "
"metadata og links til relevant dokumentation."

msgid "{core:no_metadata:header}"
msgstr "Metadata er ikke fundt"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "Hosted Shibboleth 1.3 identitetsudbyder metadata (automatisk genereret)"

msgid "{core:frontpage:required}"
msgstr "Påkrævet"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Der er sandsynligvis en konfigurationsfejl hos enten servicen eller "
"identitetsudbyderen."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"Længden af query parametrene er begrænset af PHPs Suhosin udvidelse. "
"Forøg suhosin.get.max_value_length option til mindst 2048 bytes."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Du benytter ikke HTTPS</strong>-krypteret kommunikation med "
"brugeren. SimpleSAMLphp vil fungere uden problemer med HTTP alene, men "
"hvis du anvender systemet i produktionssystemer, anbefales det stærkt at "
"benytte sikker kommunikation i form af HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">læs mere i dokumentet: SimpleSAMLphp maintenance</a> ] "

msgid "{core:frontpage:federation}"
msgstr "Føderation"

msgid "{core:frontpage:required_radius}"
msgstr "Påkrævet for RADIUS"

msgid "{core:no_state:cause_openbrowser}"
msgstr "Åben browseren med faner fra sidste session."

msgid "{core:frontpage:checkphp}"
msgstr "Checker din PHP-installation"

msgid "{core:frontpage:link_doc_idp}"
msgstr "SimpleSAMLphp som en identitetsudbyder (IdP)"

msgid "{core:no_state:report_header}"
msgstr "Rapporter denne fejl"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP eksempel - test indlogning med SAML 2.0 via din IdP"

msgid "{core:no_state:cause_nocookie}"
msgstr "Cookies kan være deaktiveret i browseren."

msgid "{core:frontpage:about_header}"
msgstr "Om SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"Yes, det er cool! Hvor kan jeg læse mere om det? Gå til <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp's hjemmeside</a>"

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Hvis du er udvikler, så har du et metadata-konfigurationsproblem. Tjek at"
" metadata er konfigurerede korrekt både på service-siden og "
"identitetsudbyder-siden."

msgid "{core:no_cookie:retry}"
msgstr "Forsøg igen"

msgid "{core:frontpage:useful_links_header}"
msgstr "Nyttige links"

msgid "{core:frontpage:metadata}"
msgstr "Metadata"

msgid "{core:frontpage:recommended}"
msgstr "Anbefalet"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp som en IdP for Google Apps for Education"

msgid "{core:frontpage:tools}"
msgstr "Værktøjer"

msgid "{core:short_sso_interval:retry}"
msgstr "Login igen"

msgid "{core:no_cookie:description}"
msgstr ""
"Det ser ud til at du har slået cookies fra i din browser. Tjek dine "
"browserindstillinger og prøv igen."

msgid "{core:frontpage:deprecated}"
msgstr "Under udfasning"

msgid "You are logged in as administrator"
msgstr "Du er logget ind som administrator"

msgid "Go back to the previous page and try again."
msgstr "Gå tilbage til forrige side og prøv igen."

msgid "If this problem persists, you can report it to the system administrators."
msgstr ""
"Hvis dette problem fortsætter, kan du rapportere det til "
"systemadministratoren."

msgid "Welcome"
msgstr "Velkommen"

msgid "SimpleSAMLphp configuration check"
msgstr "SimpleSAMLphp konfigurationscheck"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "Oversigt over metadata for din installation. Check metadatafilerne her"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML til SimpleSAMLphp metadata oversætter"

msgid "Required"
msgstr "Påkrævet"

msgid "Warnings"
msgstr "Advarsler"

msgid "Documentation"
msgstr "Dokumentation"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 tjenesteudbyder metadata (automatisk genereret)"

msgid "PHP info"
msgstr "PHPinfo"

msgid "About SimpleSAMLphp"
msgstr "Om SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 tjenesteudbyder metadata (automatisk genereret)"

msgid "Retry login"
msgstr "Login igen"

msgid "Required for LDAP"
msgstr "Påkrævet for LDAP"

msgid "Close the web browser, and try again."
msgstr "Luk din browser og prøv igen."

msgid "Federation"
msgstr "Føderation"

msgid "We were unable to locate the state information for the current request."
msgstr "Tilstandsinformation for igangværende request kan ikke findes"

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "Slet mit valg i listen af identitetsudbydere"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Der er sandsynligvis en konfigurationsfejl hos enten servicen eller "
"identitetsudbyderen."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "Konfigurer en Shibboleth 1.3 SP til at virke mod en SimpleSAMLphp IdP"

msgid "Using the back and forward buttons in the web browser."
msgstr "Brug frem- og tilbage-knappen i browseren."

msgid "Metadata not found"
msgstr "Metadata er ikke fundt"

msgid "Missing cookie"
msgstr "Mangler cookie"

msgid "Cookies may be disabled in the web browser."
msgstr "Cookies kan være deaktiveret i browseren."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "Åben browseren med faner fra sidste session."

msgid "Tools"
msgstr "Værktøjer"

msgid "Test configured authentication sources "
msgstr "Test konfigureret autentificeringskilde"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Det ser ud til at du har slået cookies fra i din browser. Tjek dine "
"browserindstillinger og prøv igen."

msgid "Installing SimpleSAMLphp"
msgstr "Installationsvejledning for SimpleSAMLphp"

msgid "Deprecated"
msgstr "Under udfasning"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Tillykke</strong>, du har nu installeret SimpleSAMLphp. Dette er "
"startsiden til installationen, hvor du vil finde eksempler, diagnostik, "
"metadata og links til relevant dokumentation."

msgid "This error may be caused by:"
msgstr "Fejlen kan være forårsaget af:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Du benytter ikke HTTPS</strong>-krypteret kommunikation med "
"brugeren. SimpleSAMLphp vil fungere uden problemer med HTTP alene, men "
"hvis du anvender systemet i produktionssystemer, anbefales det stærkt at "
"benytte sikker kommunikation i form af HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">læs mere i dokumentet: SimpleSAMLphp maintenance</a> ] "

msgid "Metadata"
msgstr "Metadata"

msgid "Retry"
msgstr "Forsøg igen"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "SimpleSAMLphp Konfiguration og Vedligeholdelse"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Diagnosticér hostnavn, port og protokol"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Hvis du har modtaget denne fejlbesked efter at have klikket på et lilnk, "
"skal du rappoterer fejlen til ejeren af siden. "

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "SimpleSAMLphp som en identitetsudbyder (IdP)"

msgid "Optional"
msgstr "Valgfrit"

msgid "Suggestions for resolving this problem:"
msgstr "Løsningsforslag til problemet:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"Yes, det er cool! Hvor kan jeg læse mere om det? Gå til <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp's hjemmeside</a>"

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr ""
"Shibboleth 1.3 SP eksempel - test indlogning med Shibboleth 1.3 via din "
"IdP"

msgid "Authentication"
msgstr "Autentificering"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp installationsside"

msgid "Show metadata"
msgstr "Vis metadata"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp som en IdP for Google Apps for Education"

msgid "State information lost"
msgstr "Tilstandsinformation forsvundet"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 identitetsudbyder metadata (automatisk genereret)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID Provider side - Alpha version (testkode)"

msgid "Required for Radius"
msgstr "Påkrævet for RADIUS"

msgid "We were unable to locate the metadata for the entity:"
msgstr "Vi kan ikke finde metadata for denne forbindelse:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP eksempel - test indlogning med SAML 2.0 via din IdP"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "SimpleSAMLphp som en tjenesteudbyder (SP)"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"Vi har opdaget at det kun er få sekunder siden du sidst autentificerede "
"dig op mod denne service. Vi antager derfor at der er et problem med "
"services."

msgid "Recommended"
msgstr "Anbefalet"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Hvis du er udvikler, så har du et metadata-konfigurationsproblem. Tjek at"
" metadata er konfigurerede korrekt både på service-siden og "
"identitetsudbyder-siden."

msgid "SimpleSAMLphp Advanced Features"
msgstr "SimpleSAMLphp Avancerede Indstillinger"

msgid "Too short interval between single sign on events."
msgstr "For kort interval mellem single sign on hændelse."

msgid "Checking your PHP installation"
msgstr "Checker din PHP-installation"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"Længden af query parametrene er begrænset af PHPs Suhosin udvidelse. "
"Forøg suhosin.get.max_value_length option til mindst 2048 bytes."

msgid "Useful links for your installation"
msgstr "Nyttige links"

msgid "Configuration"
msgstr "Konfiguration"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 identitetsudbyder metadata (automatisk genereret)"

msgid ""
"<strong>The configuration uses the default secret salt</strong> - make "
"sure you modify the default 'secretsalt' option in the simpleSAML "
"configuration in production environments. [<a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-install\">Read"
" more about SimpleSAMLphp configuration</a> ]"
msgstr ""
"<strong>Opsætningen benytter standard 'secret salt'</strong> - sørg for "
"at ændre standard indstillingen for 'secretsalt' i simpleSAML opsætningen"
" i produktionssystemer. [<a href=\"https://simplesamlphp.org/docs/stable"
"/simplesamlphp-install\">Læs mere om SimpleSAMLphp opsætning.</a> ]"

msgid "Login as administrator"
msgstr "Login som administrator"

msgid "Report this error"
msgstr "Rapporter denne fejl"


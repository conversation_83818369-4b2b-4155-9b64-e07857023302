
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: sr\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "Informacije o PHP instalaciji"

msgid "{core:no_state:report_text}"
msgstr ""
"Ako se ova greška bude i dalje pojavl<PERSON>, možete je prijaviti "
"administratorima."

msgid "{core:no_state:cause_backforward}"
msgstr ""
"Korišćenjem tastera za prethodnu (back) i sledeću (forward) stranicu u "
"web pretraživaču."

msgid "{core:no_metadata:not_found_for}"
msgstr "Ne mogu pronaći metapodatke za entitet:"

msgid "{core:frontpage:link_shib13example}"
msgstr ""
"Shibboleth 1.3 SP primer - testirajte autentifikaciju kroz vaš Shib "
"Davalac Servisa"

msgid "{core:no_state:suggestions}"
msgstr "Preporuke za rešavanje ovog problema:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Prijavite se kao administrator"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"Prošlo tek nekoliko sekundi otkad ste se zadnji put autentifikovali za "
"pristup ovoj aplikaciji te stoga pretpostavljamo da se javio problem kod "
"davaoca servisa."

msgid "{core:frontpage:link_doc_sp}"
msgstr ""
"Kako iskoristiti SimpleSAMLphp kao autentifikacioni modul kod Davaoca "
"Servisa"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Metapodaci za lokalni SAML 2.0 Davalac Servisa (automatski generisani)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "Davalac OpenID digitalnih identiteta - razvojna verzija (test)"

msgid "{core:frontpage:link_doc_install}"
msgstr "Instalacija SimpleSAMLphp-a"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Dijagnostika vezana za naziv servera (hostname), port i protokol "

msgid "{core:no_state:suggestion_goback}"
msgstr "Vratite se na prethodnu stranicu i pokušajte ponovo."

msgid "{core:no_state:causes}"
msgstr "Ova greška može biti uzrokovana:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Metapodaci za lokalni SAML 2.0 Davalac Identiteta (automatski generisani) "

msgid "{core:frontpage:optional}"
msgstr "Opciono"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr ""
"Metapodaci za lokalni Shibboleth 1.3 Davalac Servisa (automatski "
"generisani)"

msgid "{core:frontpage:doc_header}"
msgstr "Dokumentacija"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "Napredne mogućnosti SimpleSAMLphp-a"

msgid "{core:frontpage:required_ldap}"
msgstr "Obavezno za LDAP"

msgid "{core:frontpage:authtest}"
msgstr "Testirajte podešene autentifikacione servise"

msgid "{core:frontpage:link_meta_overview}"
msgstr ""
"Pregled metapodataka o vašoj instalaciji. Proverite ispravnost sadržaja "
"fajlova sa metapodacima."

msgid "{core:frontpage:configuration}"
msgstr "Podešavanja"

msgid "{core:frontpage:welcome}"
msgstr "Dobrodošli"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "Podesite Shibboleth 1.3 SP za rad sa SimpleSAMLphp Davaocem Identeteta"

msgid "{core:no_state:header}"
msgstr "Informacije o stanju su izgubljene"

msgid "{core:frontpage:metadata_header}"
msgstr "Medapodaci"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "Održavanje i podešavanja SimpleSAMLphp-a"

msgid "{core:frontpage:link_configcheck}"
msgstr "Provera SimpleSAMLphp podešavanja"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp instalaciona stranica"

msgid "{core:no_cookie:header}"
msgstr "Nedostaje kolačić (cookie)"

msgid "{core:frontpage:warnings}"
msgstr "Upozorenja"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "Pretvaranje metapodataka iz XML formata u SimpleSAMLphp format"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr ""
"Poništi moj izbor za Davaoca Identiteta u servisima za pronalaženje "
"Davaoca Identiteta "

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Prijavljeni ste kao administrator"

msgid "{core:frontpage:auth}"
msgstr "Autentifikacija"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Ukoliko se ova greška pojavila nakon što ste sledili link na nekoj web "
"stranici, onda biste grešku trebali prijaviti vlasniku navedene stranice."

msgid "{core:no_state:description}"
msgstr "Ne možemo pronaći informacije o stanju aktuelnog zahteva."

msgid "{core:frontpage:show_metadata}"
msgstr "Prikaži metapodatke"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Zatvorite web pretraživač i pokušajte ponovo."

msgid "{core:short_sso_interval:warning_header}"
msgstr "Prekratak interval između uzastopnih SSO prijava."

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Čestitamo</strong>, uspešno ste instalirali SimpleSAMLphp. Ovo je"
" početna stranica na kojoj možete pronaći primere, dijagnostiku, "
"metapodatke i linkove na relevantnu dokumentaciju."

msgid "{core:no_metadata:header}"
msgstr "Metapodaci nisu pronađeni"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr ""
"Metapodaci za lokalni Shibboleth 1.3 Davalac Identiteta (automatski "
"generisani)"

msgid "{core:frontpage:required}"
msgstr "Obavezno"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Najverojatnije je problem u podešavanjima davaoca servisa ili davaoca "
"identiteta."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"Veličina polja s parametrima ograničena je Suhosin PHP ekstenzijom. "
"Podesite da vrednost parametra suhosin.get.max_value_length bude najmanje"
" 2048 bajtova."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Ne koristite HTTPS</strong> - kriptovanu komunikaciju s "
"korisnikom. HTTP se može koristiti za potrebe testiranja, ali u "
"produkcionom okruženju trebali biste koristiti HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Pročitajte više o SimpleSAMLphp podešavanjima</a> ]"

msgid "{core:frontpage:federation}"
msgstr "Federacija"

msgid "{core:frontpage:required_radius}"
msgstr "Obavezno za Radius"

msgid "{core:no_state:cause_openbrowser}"
msgstr "Otvaranjem web pretraživača sa stranicama sačuvanim iz prethodne sesije."

msgid "{core:frontpage:checkphp}"
msgstr "Provera vaše PHP instalacije"

msgid "{core:frontpage:link_doc_idp}"
msgstr "Kako iskoristiti SimpleSAMLphp za implementaciju Davaoca Identiteta"

msgid "{core:no_state:report_header}"
msgstr "Prijavite ovu grešku"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP primer - testirajte autentifikaciju kroz vaš Davalac Identieta"

msgid "{core:no_state:cause_nocookie}"
msgstr ""
"Moguće da je podrška za kolačiće (\"cookies\") isključena u web "
"pretraživaču."

msgid "{core:frontpage:about_header}"
msgstr "O SimpleSAMLphp-u"

msgid "{core:frontpage:about_text}"
msgstr ""
"SimpleSAMLphp je veoma kul, gde mogu da pročitam više o njemu? Više "
"informacija o SimpleSAMLphp-u možete pronaći na <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp stranici Feide "
"RnD bloga</a>."

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Postoji greška sa podešavanjima metapodataka. Ukoliko ste administrator "
"sistema, proverite da li metapodaci ispravno uneseni na strani davaoca "
"servisa i davaoca identiteta."

msgid "{core:no_cookie:retry}"
msgstr "Pokušaj ponovo"

msgid "{core:frontpage:useful_links_header}"
msgstr "Korisni linkovi"

msgid "{core:frontpage:metadata}"
msgstr "Metapodaci"

msgid "{core:frontpage:recommended}"
msgstr "Preporučeno"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp kao autentifikacioni servis za Google Apps for Education"

msgid "{core:frontpage:tools}"
msgstr "Alati"

msgid "{core:short_sso_interval:retry}"
msgstr "Pokušaj se prijaviti ponovo"

msgid "{core:no_cookie:description}"
msgstr ""
"Izgleda da ste onemogućili kolačiće (cookies) u vašem web pretraživaču. "
"Molimo proverite podešavanja vašeg web pretraživača i pokušajte ponovo."

msgid "{core:frontpage:deprecated}"
msgstr "Zastarelo"

msgid "You are logged in as administrator"
msgstr "Prijavljeni ste kao administrator"

msgid "Go back to the previous page and try again."
msgstr "Vratite se na prethodnu stranicu i pokušajte ponovo."

msgid "If this problem persists, you can report it to the system administrators."
msgstr ""
"Ako se ova greška bude i dalje pojavljivala, možete je prijaviti "
"administratorima."

msgid "Welcome"
msgstr "Dobrodošli"

msgid "SimpleSAMLphp configuration check"
msgstr "Provera SimpleSAMLphp podešavanja"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr ""
"Pregled metapodataka o vašoj instalaciji. Proverite ispravnost sadržaja "
"fajlova sa metapodacima."

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "Pretvaranje metapodataka iz XML formata u SimpleSAMLphp format"

msgid "Required"
msgstr "Obavezno"

msgid "Warnings"
msgstr "Upozorenja"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr ""
"Metapodaci za lokalni Shibboleth 1.3 Davalac Servisa (automatski "
"generisani)"

msgid "PHP info"
msgstr "Informacije o PHP instalaciji"

msgid "About SimpleSAMLphp"
msgstr "O SimpleSAMLphp-u"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Metapodaci za lokalni SAML 2.0 Davalac Servisa (automatski generisani)"

msgid "Retry login"
msgstr "Pokušaj se prijaviti ponovo"

msgid "Required for LDAP"
msgstr "Obavezno za LDAP"

msgid "Close the web browser, and try again."
msgstr "Zatvorite web pretraživač i pokušajte ponovo."

msgid "Federation"
msgstr "Federacija"

msgid "We were unable to locate the state information for the current request."
msgstr "Ne možemo pronaći informacije o stanju aktuelnog zahteva."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr ""
"Poništi moj izbor za Davaoca Identiteta u servisima za pronalaženje "
"Davaoca Identiteta "

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Najverojatnije je problem u podešavanjima davaoca servisa ili davaoca "
"identiteta."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "Podesite Shibboleth 1.3 SP za rad sa SimpleSAMLphp Davaocem Identeteta"

msgid "Using the back and forward buttons in the web browser."
msgstr ""
"Korišćenjem tastera za prethodnu (back) i sledeću (forward) stranicu u "
"web pretraživaču."

msgid "Metadata not found"
msgstr "Metapodaci nisu pronađeni"

msgid "Missing cookie"
msgstr "Nedostaje kolačić (cookie)"

msgid "Cookies may be disabled in the web browser."
msgstr ""
"Moguće da je podrška za kolačiće (\"cookies\") isključena u web "
"pretraživaču."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "Otvaranjem web pretraživača sa stranicama sačuvanim iz prethodne sesije."

msgid "Tools"
msgstr "Alati"

msgid "Test configured authentication sources "
msgstr "Testirajte podešene autentifikacione servise"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Izgleda da ste onemogućili kolačiće (cookies) u vašem web pretraživaču. "
"Molimo proverite podešavanja vašeg web pretraživača i pokušajte ponovo."

msgid "Installing SimpleSAMLphp"
msgstr "Instalacija SimpleSAMLphp-a"

msgid "Deprecated"
msgstr "Zastarelo"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Čestitamo</strong>, uspešno ste instalirali SimpleSAMLphp. Ovo je"
" početna stranica na kojoj možete pronaći primere, dijagnostiku, "
"metapodatke i linkove na relevantnu dokumentaciju."

msgid "This error may be caused by:"
msgstr "Ova greška može biti uzrokovana:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Ne koristite HTTPS</strong> - kriptovanu komunikaciju s "
"korisnikom. HTTP se može koristiti za potrebe testiranja, ali u "
"produkcionom okruženju trebali biste koristiti HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Pročitajte više o SimpleSAMLphp podešavanjima</a> ]"

msgid "Metadata"
msgstr "Medapodaci"

msgid "Retry"
msgstr "Pokušaj ponovo"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "Održavanje i podešavanja SimpleSAMLphp-a"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Dijagnostika vezana za naziv servera (hostname), port i protokol "

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Ukoliko se ova greška pojavila nakon što ste sledili link na nekoj web "
"stranici, onda biste grešku trebali prijaviti vlasniku navedene stranice."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "Kako iskoristiti SimpleSAMLphp za implementaciju Davaoca Identiteta"

msgid "Optional"
msgstr "Opciono"

msgid "Suggestions for resolving this problem:"
msgstr "Preporuke za rešavanje ovog problema:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"SimpleSAMLphp je veoma kul, gde mogu da pročitam više o njemu? Više "
"informacija o SimpleSAMLphp-u možete pronaći na <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp stranici Feide "
"RnD bloga</a>."

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr ""
"Shibboleth 1.3 SP primer - testirajte autentifikaciju kroz vaš Shib "
"Davalac Servisa"

msgid "Authentication"
msgstr "Autentifikacija"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp instalaciona stranica"

msgid "Show metadata"
msgstr "Prikaži metapodatke"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp kao autentifikacioni servis za Google Apps for Education"

msgid "State information lost"
msgstr "Informacije o stanju su izgubljene"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Metapodaci za lokalni SAML 2.0 Davalac Identiteta (automatski generisani) "

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "Davalac OpenID digitalnih identiteta - razvojna verzija (test)"

msgid "Required for Radius"
msgstr "Obavezno za Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "Ne mogu pronaći metapodatke za entitet:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP primer - testirajte autentifikaciju kroz vaš Davalac Identieta"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr ""
"Kako iskoristiti SimpleSAMLphp kao autentifikacioni modul kod Davaoca "
"Servisa"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"Prošlo tek nekoliko sekundi otkad ste se zadnji put autentifikovali za "
"pristup ovoj aplikaciji te stoga pretpostavljamo da se javio problem kod "
"davaoca servisa."

msgid "Recommended"
msgstr "Preporučeno"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Postoji greška sa podešavanjima metapodataka. Ukoliko ste administrator "
"sistema, proverite da li metapodaci ispravno uneseni na strani davaoca "
"servisa i davaoca identiteta."

msgid "SimpleSAMLphp Advanced Features"
msgstr "Napredne mogućnosti SimpleSAMLphp-a"

msgid "Too short interval between single sign on events."
msgstr "Prekratak interval između uzastopnih SSO prijava."

msgid "Checking your PHP installation"
msgstr "Provera vaše PHP instalacije"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"Veličina polja s parametrima ograničena je Suhosin PHP ekstenzijom. "
"Podesite da vrednost parametra suhosin.get.max_value_length bude najmanje"
" 2048 bajtova."

msgid "Useful links for your installation"
msgstr "Korisni linkovi"

msgid "Configuration"
msgstr "Podešavanja"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr ""
"Metapodaci za lokalni Shibboleth 1.3 Davalac Identiteta (automatski "
"generisani)"

msgid "Login as administrator"
msgstr "Prijavite se kao administrator"

msgid "Report this error"
msgstr "Prijavite ovu grešku"


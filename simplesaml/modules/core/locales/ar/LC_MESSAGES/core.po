
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ar\n"
"Language-Team: \n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n>=3 "
"&& n<=10 ? 3 : n>=11 && n<=99 ? 4 : 5)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "معلومات PHP"

msgid "{core:no_state:report_text}"
msgstr "اذا استمرت هذه المشكلة بالحدوث، رجاءا بلغ إدارة الموقع"

msgid "{core:no_state:cause_backforward}"
msgstr "استخدام أزرار الرجوع للخلف و الامام بمتصفحك"

msgid "{core:no_metadata:not_found_for}"
msgstr "لا يمكننا تحديد موقع البيانات الوصفية لهذه الجزئية"

msgid "{core:frontpage:link_shib13example}"
msgstr "مثال Shibboleth 1.3 SP- اختبر تسجيل الدخول مستخدماً هوية Shib IdP"

msgid "{core:no_state:suggestions}"
msgstr "اقتراحات لحل المشكلة "

msgid "{core:frontpage:login_as_admin}"
msgstr "سجل دخول المشرف"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"يبدو انك قد قمت بتصديق الدخول عدة مرات لمقدم هذه الخدمة خلال الثواني "
"القليلة الماضية مما يقودنا للاعتقاد بوجود مشكلة ما بهذا ال SP "

msgid "{core:frontpage:link_doc_sp}"
msgstr "استخدام SimpleSAMLphp كمقدم خدمة"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "البيانات الوصفية/الميتاداتا لمقدم SAML 2.0 الضيف (تم تجهيزها اتوماتيكياً)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "مقدم خدمة OpenID اصدار ألفا- اختبر الرمز"

msgid "{core:frontpage:link_doc_install}"
msgstr "ركب SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "تشخيص اسم المضيف، المنفذ، الطريقة"

msgid "{core:no_state:suggestion_goback}"
msgstr "ارجع للصفحة السابقة و حاول مرة اخري"

msgid "{core:no_state:causes}"
msgstr "سبب حدوث هذا الخطأ قد يكون:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr ""
"البيانات الوصفية/الميتاداتا لهوية مقدم SAML 2.0 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "{core:frontpage:optional}"
msgstr "اختياري"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr ""
"البيانات الوصفية/الميتاداتا لمقدم Shibboleth 1.3 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "{core:frontpage:doc_header}"
msgstr "الكتيبات التعريفية"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "تطبيقات SimpleSAMLphp المتقدمة"

msgid "{core:frontpage:required_ldap}"
msgstr "مطلوب ل LDAP"

msgid "{core:frontpage:authtest}"
msgstr "اختبر ترتيب مصادر التوثيق"

msgid "{core:frontpage:link_meta_overview}"
msgstr "منظر بياناتك الوصفية/الميتاداتا. شخص/ عالج ملفات الميتاداتا خاصتك"

msgid "{core:frontpage:configuration}"
msgstr "الترتيب"

msgid "{core:frontpage:welcome}"
msgstr "مرحباً"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "رتب Shibboleth 1.3 SP للعمل مع SimpleSAMLphp IdP"

msgid "{core:no_state:header}"
msgstr "حدد المعلومات المفقودة"

msgid "{core:frontpage:metadata_header}"
msgstr "البيانات الوصفية/الميتاداتا "

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "ترتيب و صيانة SimpleSAMLphp"

msgid "{core:frontpage:link_configcheck}"
msgstr "مراجعة ترتيب SimpleSAMLphp"

msgid "{core:frontpage:page_title}"
msgstr "صفحة تركيب SimpleSAMLphp"

msgid "{core:no_cookie:header}"
msgstr "ألكوكيز المفقودة"

msgid "{core:frontpage:warnings}"
msgstr "تحذير"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "حول البيانات الوصفية من صيغة XML الي SimpleSAMLphp"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "احذف خياراتي ل IdP بمستكشف خدمات IdP"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "لقد سجلت الدخول كمشرف علي الموقعح"

msgid "{core:frontpage:auth}"
msgstr " التوثيق"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"ان تعرضت لهذا الاشكال بعيد اتباعك  لرابط بموقع ما, ينبغي عليك الابلاغ عن "
"هذا الاشكال لمالك  الموقع المعني"

msgid "{core:no_state:description}"
msgstr "لم نستطع تحديد المعلومات المفقودة للطلب الحالي"

msgid "{core:frontpage:show_metadata}"
msgstr "اظهر البيانات الوصفية/ الميتاداتا"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "اغلق الموقع ثم حاول مرة اخري"

msgid "{core:short_sso_interval:warning_header}"
msgstr "فترات قصيرة جداً بين محاولات تسجيل الدخول الموحد "

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Congratulations</strong>, لقد قمت بإنزال SimpleSAMLphp بنجاح. هذه"
" هي صفحة البداية حيث ستجد روابط لاختبار الأمثلة، التشخيص، البيانات "
"الوصفية/الميتاداتا و الكتيبات التعريفية"

msgid "{core:no_metadata:header}"
msgstr "البيانات الوصفية مفقودة"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr ""
"البيانات الوصفية/الميتاداتا لهوية مقدم Shibboleth 1.3 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "{core:frontpage:required}"
msgstr "مطلوب"

msgid "{core:no_metadata:config_problem}"
msgstr "من الارجح ان هذا الاشكال نابع اما من  مشكلة بالمخدم أو مشكلة بمحدد الهوية"

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"طول السؤال محدد بواسطة PHP Suhosin. قم رجاءاًً بزيادة الخيار "
"Suhosin.get.max_value_length ل ٢٠٤٨ بايتس علي الاقل"

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>انت لا تستخدم HTTPS</strong> - محادثة مشفرة مع المستخدم. HTTP جيد"
" للاختبارات لكن في بيئة النظام المبدئي ينبغي استخدام HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">read more about SimpleSAMLphp maintenance</a>]"

msgid "{core:frontpage:federation}"
msgstr "الدخول الموحد"

msgid "{core:frontpage:required_radius}"
msgstr "مطلوب ل Radius"

msgid "{core:no_state:cause_openbrowser}"
msgstr "فتح متصفحك مستخدما معلومات محفوظة من المرة السابقة"

msgid "{core:frontpage:checkphp}"
msgstr "تاكد من إنزال PHP"

msgid "{core:frontpage:link_doc_idp}"
msgstr "استخدام SimpleSAMLphp كمقدم هوية"

msgid "{core:no_state:report_header}"
msgstr "بلغ عن هذا الخطأ "

msgid "{core:frontpage:link_saml2example}"
msgstr "مثال SAML 2.0 SP- اختبر تسجيل الدخول مستخدماً هوية IdP"

msgid "{core:no_state:cause_nocookie}"
msgstr "الكوكيز غير منشطة بمتصفحك"

msgid "{core:frontpage:about_header}"
msgstr "عن SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"للمزيد عن SimpleSAMLphp اذهب الي<a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp بمدونة Feide RnD"
" </a> over at <a href=\"http://uninett.no\">UNINETT</a>"

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
" و كانت (Single Sign-On) ان كنت مبرمجاً تعمل علي توفير حل  لتوثيق دخول "
"لمرة واحدة  لديك مشكلة بادخال البيانات الوصفية, تأكد من أن أدخال البيانات"
" الوصفية صحيح بكل من محدد الهوية و المخدم  "

msgid "{core:no_cookie:retry}"
msgstr "اعد المحاولة"

msgid "{core:frontpage:useful_links_header}"
msgstr "روابط مفيدة للإنزال "

msgid "{core:frontpage:metadata}"
msgstr "البيانات الوصفية/ الميتاداتا "

msgid "{core:frontpage:recommended}"
msgstr "موصي به"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "استخدام SimpleSAMLphp ك IdP لتطبيقات قوقل التعليمية"

msgid "{core:frontpage:tools}"
msgstr "الأدوات "

msgid "{core:short_sso_interval:retry}"
msgstr "اعد تسجيل الدخول"

msgid "{core:no_cookie:description}"
msgstr ""
"يبدو انك قد عطلت الكوكيز بمتصفحك. قم رجاءا بمراجعة إعدادات متصفحك ثم حاول"
" مرة اخري"

msgid "{core:frontpage:deprecated}"
msgstr "استنكار"

msgid "You are logged in as administrator"
msgstr "لقد سجلت الدخول كمشرف علي الموقعح"

msgid "Go back to the previous page and try again."
msgstr "ارجع للصفحة السابقة و حاول مرة اخري"

msgid "If this problem persists, you can report it to the system administrators."
msgstr "اذا استمرت هذه المشكلة بالحدوث، رجاءا بلغ إدارة الموقع"

msgid "Welcome"
msgstr "مرحباً"

msgid "SimpleSAMLphp configuration check"
msgstr "مراجعة ترتيب SimpleSAMLphp"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "منظر بياناتك الوصفية/الميتاداتا. شخص/ عالج ملفات الميتاداتا خاصتك"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "حول البيانات الوصفية من صيغة XML الي SimpleSAMLphp"

msgid "Required"
msgstr "مطلوب"

msgid "Warnings"
msgstr "تحذير"

msgid "Documentation"
msgstr "الكتيبات التعريفية"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr ""
"البيانات الوصفية/الميتاداتا لمقدم Shibboleth 1.3 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "PHP info"
msgstr "معلومات PHP"

msgid "About SimpleSAMLphp"
msgstr "عن SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "البيانات الوصفية/الميتاداتا لمقدم SAML 2.0 الضيف (تم تجهيزها اتوماتيكياً)"

msgid "Retry login"
msgstr "اعد تسجيل الدخول"

msgid "Required for LDAP"
msgstr "مطلوب ل LDAP"

msgid "Close the web browser, and try again."
msgstr "اغلق الموقع ثم حاول مرة اخري"

msgid "Federation"
msgstr "الدخول الموحد"

msgid "We were unable to locate the state information for the current request."
msgstr "لم نستطع تحديد المعلومات المفقودة للطلب الحالي"

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "احذف خياراتي ل IdP بمستكشف خدمات IdP"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr "من الارجح ان هذا الاشكال نابع اما من  مشكلة بالمخدم أو مشكلة بمحدد الهوية"

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "رتب Shibboleth 1.3 SP للعمل مع SimpleSAMLphp IdP"

msgid "Using the back and forward buttons in the web browser."
msgstr "استخدام أزرار الرجوع للخلف و الامام بمتصفحك"

msgid "Metadata not found"
msgstr "البيانات الوصفية مفقودة"

msgid "Missing cookie"
msgstr "ألكوكيز المفقودة"

msgid "Cookies may be disabled in the web browser."
msgstr "الكوكيز غير منشطة بمتصفحك"

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "فتح متصفحك مستخدما معلومات محفوظة من المرة السابقة"

msgid "Tools"
msgstr "الأدوات "

msgid "Test configured authentication sources "
msgstr "اختبر ترتيب مصادر التوثيق"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"يبدو انك قد عطلت الكوكيز بمتصفحك. قم رجاءا بمراجعة إعدادات متصفحك ثم حاول"
" مرة اخري"

msgid "Installing SimpleSAMLphp"
msgstr "ركب SimpleSAMLphp"

msgid "Deprecated"
msgstr "استنكار"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Congratulations</strong>, لقد قمت بإنزال SimpleSAMLphp بنجاح. هذه"
" هي صفحة البداية حيث ستجد روابط لاختبار الأمثلة، التشخيص، البيانات "
"الوصفية/الميتاداتا و الكتيبات التعريفية"

msgid "This error may be caused by:"
msgstr "سبب حدوث هذا الخطأ قد يكون:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>انت لا تستخدم HTTPS</strong> - محادثة مشفرة مع المستخدم. HTTP جيد"
" للاختبارات لكن في بيئة النظام المبدئي ينبغي استخدام HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">read more about SimpleSAMLphp maintenance</a>]"

msgid "Metadata"
msgstr "البيانات الوصفية/الميتاداتا "

msgid "Retry"
msgstr "اعد المحاولة"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "ترتيب و صيانة SimpleSAMLphp"

msgid "Diagnostics on hostname, port and protocol"
msgstr "تشخيص اسم المضيف، المنفذ، الطريقة"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"ان تعرضت لهذا الاشكال بعيد اتباعك  لرابط بموقع ما, ينبغي عليك الابلاغ عن "
"هذا الاشكال لمالك  الموقع المعني"

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "استخدام SimpleSAMLphp كمقدم هوية"

msgid "Optional"
msgstr "اختياري"

msgid "Suggestions for resolving this problem:"
msgstr "اقتراحات لحل المشكلة "

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"للمزيد عن SimpleSAMLphp اذهب الي<a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp بمدونة Feide RnD"
" </a> over at <a href=\"http://uninett.no\">UNINETT</a>"

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr "مثال Shibboleth 1.3 SP- اختبر تسجيل الدخول مستخدماً هوية Shib IdP"

msgid "Authentication"
msgstr " التوثيق"

msgid "SimpleSAMLphp installation page"
msgstr "صفحة تركيب SimpleSAMLphp"

msgid "Show metadata"
msgstr "اظهر البيانات الوصفية/ الميتاداتا"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "استخدام SimpleSAMLphp ك IdP لتطبيقات قوقل التعليمية"

msgid "State information lost"
msgstr "حدد المعلومات المفقودة"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr ""
"البيانات الوصفية/الميتاداتا لهوية مقدم SAML 2.0 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "مقدم خدمة OpenID اصدار ألفا- اختبر الرمز"

msgid "Required for Radius"
msgstr "مطلوب ل Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "لا يمكننا تحديد موقع البيانات الوصفية لهذه الجزئية"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "مثال SAML 2.0 SP- اختبر تسجيل الدخول مستخدماً هوية IdP"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "استخدام SimpleSAMLphp كمقدم خدمة"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"يبدو انك قد قمت بتصديق الدخول عدة مرات لمقدم هذه الخدمة خلال الثواني "
"القليلة الماضية مما يقودنا للاعتقاد بوجود مشكلة ما بهذا ال SP "

msgid "Recommended"
msgstr "موصي به"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
" و كانت (Single Sign-On) ان كنت مبرمجاً تعمل علي توفير حل  لتوثيق دخول "
"لمرة واحدة  لديك مشكلة بادخال البيانات الوصفية, تأكد من أن أدخال البيانات"
" الوصفية صحيح بكل من محدد الهوية و المخدم  "

msgid "SimpleSAMLphp Advanced Features"
msgstr "تطبيقات SimpleSAMLphp المتقدمة"

msgid "Too short interval between single sign on events."
msgstr "فترات قصيرة جداً بين محاولات تسجيل الدخول الموحد "

msgid "Checking your PHP installation"
msgstr "تاكد من إنزال PHP"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"طول السؤال محدد بواسطة PHP Suhosin. قم رجاءاًً بزيادة الخيار "
"Suhosin.get.max_value_length ل ٢٠٤٨ بايتس علي الاقل"

msgid "Useful links for your installation"
msgstr "روابط مفيدة للإنزال "

msgid "Configuration"
msgstr "الترتيب"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr ""
"البيانات الوصفية/الميتاداتا لهوية مقدم Shibboleth 1.3 الضيف (تم تجهيزها "
"اتوماتيكياً)"

msgid "Login as administrator"
msgstr "سجل دخول المشرف"

msgid "Report this error"
msgstr "بلغ عن هذا الخطأ "


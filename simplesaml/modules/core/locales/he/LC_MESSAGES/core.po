
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: he\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "מידע PHP"

msgid "{core:no_state:report_text}"
msgstr "אם הבעייה ממשיכה, אתה יכול לדווח עליה למנהל המערכת."

msgid "{core:no_state:cause_backforward}"
msgstr "שימוש בכפתורי הבא והקודם בדפדפן."

msgid "{core:no_metadata:not_found_for}"
msgstr "לא הצלחנו לאתר מטא-מידע עבור הישות:"

msgid "{core:frontpage:link_shib13example}"
msgstr ""
"דוגמא לס\"ש מסוג Shibboleth 1.3 - בוחן כניסה למערכת דרך ס\"ז מסוג - "
"Shibboleth"

msgid "{core:no_state:suggestions}"
msgstr "הצעות לפתרון הבעייה הנוכחית:"

msgid "{core:frontpage:login_as_admin}"
msgstr "כנס כמנהל מערכת"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"גילינו שעברו רק מספר שניות מאז שהיזדהת בפעם האחרונה עם ספק השרות הזה, "
"ולכן אנחנו מניחים שישנה בעייה עם ס\"הש."

msgid "{core:frontpage:link_doc_sp}"
msgstr "שימוש ב SimpleSAMLphp כספק שרותים"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "מטא-הנתונים של ספק השירותים מסוג SAML 2.0 המאורח (נוצר אוטומטית)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "אתר ספק OpenID - גירסת אלפא (קוד בדיקה("

msgid "{core:frontpage:link_doc_install}"
msgstr "התקנת SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "איבחון על שם מחשב, פורט ופרוטוקול"

msgid "{core:no_state:suggestion_goback}"
msgstr "חזור לדף הקודם ונסה שוב."

msgid "{core:no_state:causes}"
msgstr "יכול להיות שהשגיאה נגרמה על-ידי:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "מטא-הנתונים של ספק הזהויות מסוג SAML 2.0 המאורח (נוצר אוטומטית)"

msgid "{core:frontpage:optional}"
msgstr "נתון לבחירה"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "מטא-הנתונים של ספק השירותים מסוג Shibboleth 1.3 המאורח (נוצר אוטומטית)"

msgid "{core:frontpage:doc_header}"
msgstr "תיעוד"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "תכונות מתקדמות של SimpleSAMLphp"

msgid "{core:frontpage:required_ldap}"
msgstr "דרוש עבור LDAP"

msgid "{core:frontpage:authtest}"
msgstr "בחן את הגדרות מקורות ההיזדהות"

msgid "{core:frontpage:link_meta_overview}"
msgstr "סקירת מטא-נתונים להתקנה שלך. אבחן את קבצי מטא-הנתונים שלך"

msgid "{core:frontpage:configuration}"
msgstr "הגדרות"

msgid "{core:frontpage:welcome}"
msgstr "ברוך-הבא"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "הגדר ס\"ש מסוג Shibboleth 1.3 כך שיעבוד עם ס\"ז של SimpleSAMLphp"

msgid "{core:no_state:header}"
msgstr "מידע המצב אבד"

msgid "{core:frontpage:metadata_header}"
msgstr "מטא-נתונים"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "תחזוקה והגדרות של SimpleSAMLphp"

msgid "{core:frontpage:link_configcheck}"
msgstr "בדיקת הגדרות SimpleSAMLphp "

msgid "{core:frontpage:page_title}"
msgstr "דף ההתקנה של SimpleSAMLphp"

msgid "{core:no_cookie:header}"
msgstr "חסרה עוגייה"

msgid "{core:frontpage:warnings}"
msgstr "אזהרות"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "ממיר XML למטא-מידע של SimpleSAMLphp"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "מחק את בחירת ס\"ז משירות האיתור לס\"ז"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "אתה מחובר כמנהל מערכת"

msgid "{core:frontpage:auth}"
msgstr "אימות"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"אם אתה משתמש שקיבל שגיאה זו לאחר לחיצה על קישור באתר, כדי שתדווח על "
"השגיאה לבעלי האתר."

msgid "{core:no_state:description}"
msgstr "לא הצלחנו לאתר את מידע המצב לבקשה הנוכחית."

msgid "{core:frontpage:show_metadata}"
msgstr "הראה מטא-נתונים"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "סגור את הדפדפן, ונסה שוב."

msgid "{core:short_sso_interval:warning_header}"
msgstr "פרק זמן קצר מידי בין ארועי כניסה יחידה."

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>מזל טוב</strong>,SimpleSAMLphp. התקנת הבהצלחה את זה דף ההתחלה של "
"ההתקנה שלך, היכן שתמצא קישורים לדוגמאות לבדיקה, אבחון, מטא-נתונים ואפילו "
"קישורים לתעוד המתאים. "

msgid "{core:no_metadata:header}"
msgstr "מטא-מידע לא נמצא"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "מטא-הנתונים של ספק השזהויות מסוג Shibboleth 1.3 המאורח (נוצר אוטומטית)"

msgid "{core:frontpage:required}"
msgstr "דרוש"

msgid "{core:no_metadata:config_problem}"
msgstr "זו, ככל הנראה, בעייה בהגדרות של ספק הזהות או ספק השירות."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"אורך הפרמטרים בבקשה מוגבל ע\"י PHP Suhosin extension. אנא הגדל את ערך "
"המשתנה suhosin.get.max_value_length ללפחות 2048 בתים."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>אתה לא משתמש ב- HTTPS </strong> - התקשרות מוצפנת עם המשתמש. HTTP "
"עובד בסדר למטרות בדיקה, אולם למערכות אמיתיות, כדי להשתמש ה HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\"> קרא עוד על תחזוק SimpleSAMLphp </a> ]"

msgid "{core:frontpage:federation}"
msgstr "איחוד"

msgid "{core:frontpage:required_radius}"
msgstr "דרוש עבור Radius"

msgid "{core:no_state:cause_openbrowser}"
msgstr "פתיחת הדפדפן עם לשוניות שנשמרו משימוש הקודם."

msgid "{core:frontpage:checkphp}"
msgstr "בודק את התקנת ה- PHP שלך"

msgid "{core:frontpage:link_doc_idp}"
msgstr "שימוש ב SimpleSAMLphp כספק זהויות"

msgid "{core:no_state:report_header}"
msgstr "דווח על השגיאה הנוכחית"

msgid "{core:frontpage:link_saml2example}"
msgstr "דוגמא לס\"ש מסוג SAML 2.0 - בחן כניסה למערכת דרך הס\"ז שלך"

msgid "{core:no_state:cause_nocookie}"
msgstr "תמיכה בעוגיות מבוטלת בדפדפן"

msgid "{core:frontpage:about_header}"
msgstr "אודות SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"ה-SimpleSAMLphp הזה הוא מה זה מגניב, איםה אני יכול לקרוא יותר עליו?אתה "
"יכול למצוא מידע נוסף ב- <a href=\"http://rnd.feide.no/simplesamlphp\"> "
"SimpleSAMLphp ב Feide בבלוג הפיתוח של שנמצא ב- <a "
"href=\"http://uninett.no\">UNINETT</a>. "

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"אם אתה מפתח שפורש פיתרון התחברות יחידה, יש לך בעייה עם הגדרות המטא-מידע. "
"בדוק שהמטא-מידע מוגדר נכון בספקי הזהות והשרות."

msgid "{core:no_cookie:retry}"
msgstr "נסה שנית"

msgid "{core:frontpage:useful_links_header}"
msgstr "קישורים שימושיים להתקנה שלך"

msgid "{core:frontpage:metadata}"
msgstr "מטא-נתונים"

msgid "{core:frontpage:recommended}"
msgstr "רצוי"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp כס\"ז לשירותי גוגל להשכלה"

msgid "{core:frontpage:tools}"
msgstr "כלים"

msgid "{core:short_sso_interval:retry}"
msgstr "נסה שוב להתחבר"

msgid "{core:no_cookie:description}"
msgstr ""
"נראה שכיבית את העוגיות בדפדפן שלךץ אנא בדוק את ההגדרות בדפדפן שלך, ונסה "
"שנית. "

msgid "{core:frontpage:deprecated}"
msgstr "פג תוקף"

msgid "You are logged in as administrator"
msgstr "אתה מחובר כמנהל מערכת"

msgid "Go back to the previous page and try again."
msgstr "חזור לדף הקודם ונסה שוב."

msgid "If this problem persists, you can report it to the system administrators."
msgstr "אם הבעייה ממשיכה, אתה יכול לדווח עליה למנהל המערכת."

msgid "Welcome"
msgstr "ברוך-הבא"

msgid "SimpleSAMLphp configuration check"
msgstr "בדיקת הגדרות SimpleSAMLphp "

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "סקירת מטא-נתונים להתקנה שלך. אבחן את קבצי מטא-הנתונים שלך"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "ממיר XML למטא-מידע של SimpleSAMLphp"

msgid "Required"
msgstr "דרוש"

msgid "Warnings"
msgstr "אזהרות"

msgid "Documentation"
msgstr "תיעוד"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "מטא-הנתונים של ספק השירותים מסוג Shibboleth 1.3 המאורח (נוצר אוטומטית)"

msgid "PHP info"
msgstr "מידע PHP"

msgid "About SimpleSAMLphp"
msgstr "אודות SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "מטא-הנתונים של ספק השירותים מסוג SAML 2.0 המאורח (נוצר אוטומטית)"

msgid "Retry login"
msgstr "נסה שוב להתחבר"

msgid "Required for LDAP"
msgstr "דרוש עבור LDAP"

msgid "Close the web browser, and try again."
msgstr "סגור את הדפדפן, ונסה שוב."

msgid "Federation"
msgstr "איחוד"

msgid "We were unable to locate the state information for the current request."
msgstr "לא הצלחנו לאתר את מידע המצב לבקשה הנוכחית."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "מחק את בחירת ס\"ז משירות האיתור לס\"ז"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr "זו, ככל הנראה, בעייה בהגדרות של ספק הזהות או ספק השירות."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "הגדר ס\"ש מסוג Shibboleth 1.3 כך שיעבוד עם ס\"ז של SimpleSAMLphp"

msgid "Using the back and forward buttons in the web browser."
msgstr "שימוש בכפתורי הבא והקודם בדפדפן."

msgid "Metadata not found"
msgstr "מטא-מידע לא נמצא"

msgid "Missing cookie"
msgstr "חסרה עוגייה"

msgid "Cookies may be disabled in the web browser."
msgstr "תמיכה בעוגיות מבוטלת בדפדפן"

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "פתיחת הדפדפן עם לשוניות שנשמרו משימוש הקודם."

msgid "Tools"
msgstr "כלים"

msgid "Test configured authentication sources "
msgstr "בחן את הגדרות מקורות ההיזדהות"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"נראה שכיבית את העוגיות בדפדפן שלךץ אנא בדוק את ההגדרות בדפדפן שלך, ונסה "
"שנית. "

msgid "Installing SimpleSAMLphp"
msgstr "התקנת SimpleSAMLphp"

msgid "Deprecated"
msgstr "פג תוקף"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>מזל טוב</strong>,SimpleSAMLphp. התקנת הבהצלחה את זה דף ההתחלה של "
"ההתקנה שלך, היכן שתמצא קישורים לדוגמאות לבדיקה, אבחון, מטא-נתונים ואפילו "
"קישורים לתעוד המתאים. "

msgid "This error may be caused by:"
msgstr "יכול להיות שהשגיאה נגרמה על-ידי:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>אתה לא משתמש ב- HTTPS </strong> - התקשרות מוצפנת עם המשתמש. HTTP "
"עובד בסדר למטרות בדיקה, אולם למערכות אמיתיות, כדי להשתמש ה HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\"> קרא עוד על תחזוק SimpleSAMLphp </a> ]"

msgid "Metadata"
msgstr "מטא-נתונים"

msgid "Retry"
msgstr "נסה שנית"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "תחזוקה והגדרות של SimpleSAMLphp"

msgid "Diagnostics on hostname, port and protocol"
msgstr "איבחון על שם מחשב, פורט ופרוטוקול"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"אם אתה משתמש שקיבל שגיאה זו לאחר לחיצה על קישור באתר, כדי שתדווח על "
"השגיאה לבעלי האתר."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "שימוש ב SimpleSAMLphp כספק זהויות"

msgid "Optional"
msgstr "נתון לבחירה"

msgid "Suggestions for resolving this problem:"
msgstr "הצעות לפתרון הבעייה הנוכחית:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"ה-SimpleSAMLphp הזה הוא מה זה מגניב, איםה אני יכול לקרוא יותר עליו?אתה "
"יכול למצוא מידע נוסף ב- <a href=\"http://rnd.feide.no/simplesamlphp\"> "
"SimpleSAMLphp ב Feide בבלוג הפיתוח של שנמצא ב- <a "
"href=\"http://uninett.no\">UNINETT</a>. "

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr ""
"דוגמא לס\"ש מסוג Shibboleth 1.3 - בוחן כניסה למערכת דרך ס\"ז מסוג - "
"Shibboleth"

msgid "Authentication"
msgstr "אימות"

msgid "SimpleSAMLphp installation page"
msgstr "דף ההתקנה של SimpleSAMLphp"

msgid "Show metadata"
msgstr "הראה מטא-נתונים"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp כס\"ז לשירותי גוגל להשכלה"

msgid "State information lost"
msgstr "מידע המצב אבד"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "מטא-הנתונים של ספק הזהויות מסוג SAML 2.0 המאורח (נוצר אוטומטית)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "אתר ספק OpenID - גירסת אלפא (קוד בדיקה("

msgid "Required for Radius"
msgstr "דרוש עבור Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "לא הצלחנו לאתר מטא-מידע עבור הישות:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "דוגמא לס\"ש מסוג SAML 2.0 - בחן כניסה למערכת דרך הס\"ז שלך"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "שימוש ב SimpleSAMLphp כספק שרותים"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"גילינו שעברו רק מספר שניות מאז שהיזדהת בפעם האחרונה עם ספק השרות הזה, "
"ולכן אנחנו מניחים שישנה בעייה עם ס\"הש."

msgid "Recommended"
msgstr "רצוי"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"אם אתה מפתח שפורש פיתרון התחברות יחידה, יש לך בעייה עם הגדרות המטא-מידע. "
"בדוק שהמטא-מידע מוגדר נכון בספקי הזהות והשרות."

msgid "SimpleSAMLphp Advanced Features"
msgstr "תכונות מתקדמות של SimpleSAMLphp"

msgid "Too short interval between single sign on events."
msgstr "פרק זמן קצר מידי בין ארועי כניסה יחידה."

msgid "Checking your PHP installation"
msgstr "בודק את התקנת ה- PHP שלך"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"אורך הפרמטרים בבקשה מוגבל ע\"י PHP Suhosin extension. אנא הגדל את ערך "
"המשתנה suhosin.get.max_value_length ללפחות 2048 בתים."

msgid "Useful links for your installation"
msgstr "קישורים שימושיים להתקנה שלך"

msgid "Configuration"
msgstr "הגדרות"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "מטא-הנתונים של ספק השזהויות מסוג Shibboleth 1.3 המאורח (נוצר אוטומטית)"

msgid "Login as administrator"
msgstr "כנס כמנהל מערכת"

msgid "Report this error"
msgstr "דווח על השגיאה הנוכחית"


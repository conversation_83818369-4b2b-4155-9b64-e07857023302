
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: nl\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHP info"

msgid "{core:no_state:report_text}"
msgstr "If dit probleem behoud, dan kun je het melden aan de systeem beheerders."

msgid "{core:no_state:cause_backforward}"
msgstr "Gebruik van de 'Volgende'- en 'Terug'-knoppen in de web browser."

msgid "{core:no_metadata:not_found_for}"
msgstr "De metadata voor de volgende entity kon niet gevonden worden:"

msgid "{core:frontpage:link_shib13example}"
msgstr "Shibboleth 1.3 SP voorbeeld - test inloggen via je Shibboleth 1.3 IdP"

msgid "{core:no_state:suggestions}"
msgstr "Suggesties om dit probleem op te lossen:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Login als beheerder"

msgid "{core:frontpage:logout}"
msgstr "Uitloggen"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"We hebben waargenomen dat u slechts een paar seconden geleden al "
"aangemeld bent bij deze serviceprovider, daarom nemen we aan dat er een "
"probleem is met deze SP."

msgid "{core:frontpage:link_doc_sp}"
msgstr "Gebruik SimpleSAMLphp als Service Provider"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Lokale SAML 2.0 Service Provider Metadata (automatisch gegenereerd)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID Provider site - Alpha versie (test code)"

msgid "{core:frontpage:link_doc_install}"
msgstr "Installatie van SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Inspectie op hostnaam, poort en protocol"

msgid "{core:no_state:suggestion_badlink}"
msgstr "Controleer of u de juiste link gebruikt om de website te benaderen."

msgid "{core:no_state:suggestion_goback}"
msgstr "Ga terug naar de vorige pagina, en probeer opnieuw."

msgid "{core:no_state:causes}"
msgstr "Deze error is waarschijnlijk veroorzaakt door:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Lokale SAML 2.0 Identity Provider Metadata (automatisch gegenereerd)"

msgid "{core:frontpage:optional}"
msgstr "Optioneel"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "Lokale Shibboleth 1.3 Service Provider Metadata (automatisch gegenereerd)"

msgid "{core:frontpage:doc_header}"
msgstr "Documentatie"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "SimpleSAMLphp Geavanceerde Functies"

msgid "{core:frontpage:required_ldap}"
msgstr "Vereist voor LDAP"

msgid "{core:frontpage:warnings_secretsalt}"
msgstr ""
"<strong>De configuratie bevat de sandaard secret salt</strong> - verander"
" altijd de 'secretsalt'-optie in de simpleSAML-configuratie voor "
"productieomgevingen. [<a href=\"https://simplesamlphp.org/docs/stable"
"/simplesamlphp-install\">Lees meer over het configureren van "
"SimpleSAMLphp</a> ]"

msgid "{core:frontpage:authtest}"
msgstr "Test geconfigureerde authenticatiebronnen"

msgid "{core:frontpage:link_meta_overview}"
msgstr "Meta data overzicht voor jouw installatie. Inspecteer je metadata files"

msgid "{core:frontpage:configuration}"
msgstr "Configuratie"

msgid "{core:frontpage:welcome}"
msgstr "Welkom"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "Configureer een Shibboleth 1.3 SP voor de SimpleSAMLphp IdP"

msgid "{core:no_state:header}"
msgstr "Toestand informatie verloren"

msgid "{core:frontpage:metadata_header}"
msgstr "Metadata"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "SimpleSAMLphp Onderhoud en Configuratie"

msgid "{core:frontpage:link_configcheck}"
msgstr "SimpleSAMLphp configuratie check"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp installatiepagina"

msgid "{core:no_cookie:header}"
msgstr "Cookie ontbreekt"

msgid "{core:frontpage:warnings}"
msgstr "Waarschuwingen"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML naar SimpleSAMLphp metadata vertaling"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "Verwijder IDP keuzes uit de IdP discovery service"

msgid "{core:frontpage:warnings_outdated}"
msgstr ""
"Deze installatie van SimpleSAMLphp is verouderd. Het is aan te raden zo "
"snel mogelijk te upgraden naar <a href=\"%LATEST_URL%\">de meest recente "
"versie</a>."

msgid "{core:frontpage:warnings_curlmissing}"
msgstr "PHP cURL-extensie ontbreekt. Kan niet controleren op updates voor SimpleSAMLphp."

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Je bent ingelogd als beheerder"

msgid "{core:frontpage:auth}"
msgstr "Authenticatie"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Als u een eindgebruiker bent die deze foutmelding kreeg na het volgen van"
" een link op een site, dan kunt u deze fout melden bij de eigenaar van "
"die site."

msgid "{core:no_state:description}"
msgstr ""
"Wij waren niet in staat om de toestand informatie te vinden voor het "
"huidige verzoek."

msgid "{core:frontpage:show_metadata}"
msgstr "Toon metadata"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Sluit de web browser, en probeer opnieuw."

msgid "{core:short_sso_interval:warning_header}"
msgstr "Te kort interval tussen single sign on pogingen"

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Gefeliciteerd</strong>, SimpleSAMLphp is succesvol geïnstalleerd."
" Dit is de startpagina voor SimpleSAMLphp, waar je links kan vinden naar "
"voorbeelden, test-pagina's, analyses, metadata en ook naar relevante "
"documentatie."

msgid "{core:no_metadata:header}"
msgstr "Metadata niet gevonden"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "Lokale Shibboleth 1.3 Identity Provider Metadata (automatisch gegenereerd)"

msgid "{core:frontpage:required}"
msgstr "Vereist"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Dit is waarschijnlijk een configuratieprobleem bij ofwel de "
"serviceprovider ofwel de identiteitsverstrekker."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"De lengte van de query-parameters wordt beperkt door PHP's Suhosin-"
"extensie. Gelieve de optie suhosin.get.max_value_length te verhogen tot "
"tenminste 2048 bytes."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Je gebruikt geen HTTPS</strong> - versleutelde communicatie met "
"de gebruiker. SimpleSAMLphp werkt prima op HTTP voor testdoeleinden, maar"
" als je SimpleSAMLphp in een productieomgeving gaat gebruiken, zou je dat"
" moeten doen over HTTPS. [ <a href=\"http://rnd.feide.no/content"
"/simplesamlphp-maintenance-and-configuration\">lees meer over "
"SimpleSAMLphp-onderhoud</a> ]"

msgid "{core:frontpage:federation}"
msgstr "Federatie"

msgid "{core:frontpage:required_radius}"
msgstr "Vereist voor Radius"

msgid "{core:no_state:cause_badlink}"
msgstr "Verkeerde link gebruikt, bijvoorbeeld een bookmark (bladwijzer)."

msgid "{core:no_state:cause_openbrowser}"
msgstr "Web browser geopend met tabs opgeslagen van de vorige sessie."

msgid "{core:frontpage:checkphp}"
msgstr "Test de PHP-installatie"

msgid "{core:frontpage:link_doc_idp}"
msgstr "Gebruik SimpleSAMLphp als Identity Provider"

msgid "{core:no_state:report_header}"
msgstr "Meld deze error"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP voorbeeld - test inloggen via je IdP"

msgid "{core:no_state:cause_nocookie}"
msgstr "Cookies kunnen uitgeschakeld zijn in de web browser."

msgid "{core:frontpage:about_header}"
msgstr "Over SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"Hé, dat SimpleSAMLphp dingetje is vet cool, waar kan ik er meer over "
"vinden? Je kan meer informatie over <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp</a> vinden op de"
" Feide RnD blog van <a href=\"http://uninett.no\">UNINETT</a>."

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Als u een ontwikkelaar bent die een single sign-on oplossing aan het "
"implementeren is, heeft u een probleem met de metadataconfiguratie. "
"Controleer of de metadata correct is geconfigureerd zowel bij de "
"identiteitsverstrekker als bij de service provider."

msgid "{core:no_cookie:retry}"
msgstr "Opnieuw"

msgid "{core:frontpage:useful_links_header}"
msgstr "Nuttige links"

msgid "{core:frontpage:metadata}"
msgstr "Metadata"

msgid "{core:frontpage:recommended}"
msgstr "Aanbevolen"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp als een Identity Provider voor Google Apps for Education"

msgid "{core:frontpage:tools}"
msgstr "Gereedschap"

msgid "{core:short_sso_interval:retry}"
msgstr "Inloggen opnieuw proberen"

msgid "{core:no_cookie:description}"
msgstr ""
"Het ziet er naaruit dat cookies zijn uitgeschakeld in uw browser. "
"Controleer de browserinstellingen en probeer het opnieuw."

msgid "{core:frontpage:deprecated}"
msgstr "Verouderd"

msgid "You are logged in as administrator"
msgstr "Je bent ingelogd als beheerder"

msgid "Go back to the previous page and try again."
msgstr "Ga terug naar de vorige pagina, en probeer opnieuw."

msgid "If this problem persists, you can report it to the system administrators."
msgstr "If dit probleem behoud, dan kun je het melden aan de systeem beheerders."

msgid "Welcome"
msgstr "Welkom"

msgid "SimpleSAMLphp configuration check"
msgstr "SimpleSAMLphp configuratie check"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "Meta data overzicht voor jouw installatie. Inspecteer je metadata files"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML naar SimpleSAMLphp metadata vertaling"

msgid "Required"
msgstr "Vereist"

msgid "Warnings"
msgstr "Waarschuwingen"

msgid "Documentation"
msgstr "Documentatie"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "Lokale Shibboleth 1.3 Service Provider Metadata (automatisch gegenereerd)"

msgid "PHP info"
msgstr "PHP info"

msgid "About SimpleSAMLphp"
msgstr "Over SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Lokale SAML 2.0 Service Provider Metadata (automatisch gegenereerd)"

msgid "Retry login"
msgstr "Inloggen opnieuw proberen"

msgid "Required for LDAP"
msgstr "Vereist voor LDAP"

msgid "Close the web browser, and try again."
msgstr "Sluit de web browser, en probeer opnieuw."

msgid "Federation"
msgstr "Federatie"

msgid "We were unable to locate the state information for the current request."
msgstr ""
"Wij waren niet in staat om de toestand informatie te vinden voor het "
"huidige verzoek."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "Verwijder IDP keuzes uit de IdP discovery service"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Dit is waarschijnlijk een configuratieprobleem bij ofwel de "
"serviceprovider ofwel de identiteitsverstrekker."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "Configureer een Shibboleth 1.3 SP voor de SimpleSAMLphp IdP"

msgid "Using the back and forward buttons in the web browser."
msgstr "Gebruik van de 'Volgende'- en 'Terug'-knoppen in de web browser."

msgid ""
"You are running an outdated version of SimpleSAMLphp. Please update to <a"
" href=\"%LATEST_URL%\">the latest version</a> as soon as possible."
msgstr ""
"Deze installatie van SimpleSAMLphp is verouderd. Het is aan te raden zo "
"snel mogelijk te upgraden naar <a href=\"%LATEST_URL%\">de meest recente "
"versie</a>."

msgid "Metadata not found"
msgstr "Metadata niet gevonden"

msgid "Missing cookie"
msgstr "Cookie ontbreekt"

msgid "Cookies may be disabled in the web browser."
msgstr "Cookies kunnen uitgeschakeld zijn in de web browser."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "Web browser geopend met tabs opgeslagen van de vorige sessie."

msgid "Tools"
msgstr "Gereedschap"

msgid "Test configured authentication sources "
msgstr "Test geconfigureerde authenticatiebronnen"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Het ziet er naaruit dat cookies zijn uitgeschakeld in uw browser. "
"Controleer de browserinstellingen en probeer het opnieuw."

msgid "Installing SimpleSAMLphp"
msgstr "Installatie van SimpleSAMLphp"

msgid "Deprecated"
msgstr "Verouderd"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Gefeliciteerd</strong>, SimpleSAMLphp is succesvol geïnstalleerd."
" Dit is de startpagina voor SimpleSAMLphp, waar je links kan vinden naar "
"voorbeelden, test-pagina's, analyses, metadata en ook naar relevante "
"documentatie."

msgid "This error may be caused by:"
msgstr "Deze error is waarschijnlijk veroorzaakt door:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Je gebruikt geen HTTPS</strong> - versleutelde communicatie met "
"de gebruiker. SimpleSAMLphp werkt prima op HTTP voor testdoeleinden, maar"
" als je SimpleSAMLphp in een productieomgeving gaat gebruiken, zou je dat"
" moeten doen over HTTPS. [ <a href=\"http://rnd.feide.no/content"
"/simplesamlphp-maintenance-and-configuration\">lees meer over "
"SimpleSAMLphp-onderhoud</a> ]"

msgid "Metadata"
msgstr "Metadata"

msgid "Retry"
msgstr "Opnieuw"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "SimpleSAMLphp Onderhoud en Configuratie"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Inspectie op hostnaam, poort en protocol"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Als u een eindgebruiker bent die deze foutmelding kreeg na het volgen van"
" een link op een site, dan kunt u deze fout melden bij de eigenaar van "
"die site."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "Gebruik SimpleSAMLphp als Identity Provider"

msgid "Optional"
msgstr "Optioneel"

msgid "Suggestions for resolving this problem:"
msgstr "Suggesties om dit probleem op te lossen:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"Hé, dat SimpleSAMLphp dingetje is vet cool, waar kan ik er meer over "
"vinden? Je kan meer informatie over <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp</a> vinden op de"
" Feide RnD blog van <a href=\"http://uninett.no\">UNINETT</a>."

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr "Shibboleth 1.3 SP voorbeeld - test inloggen via je Shibboleth 1.3 IdP"

msgid "Authentication"
msgstr "Authenticatie"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp installatiepagina"

msgid "Show metadata"
msgstr "Toon metadata"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp als een Identity Provider voor Google Apps for Education"

msgid "State information lost"
msgstr "Toestand informatie verloren"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Lokale SAML 2.0 Identity Provider Metadata (automatisch gegenereerd)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID Provider site - Alpha versie (test code)"

msgid "Required for Radius"
msgstr "Vereist voor Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "De metadata voor de volgende entity kon niet gevonden worden:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP voorbeeld - test inloggen via je IdP"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "Gebruik SimpleSAMLphp als Service Provider"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"We hebben waargenomen dat u slechts een paar seconden geleden al "
"aangemeld bent bij deze serviceprovider, daarom nemen we aan dat er een "
"probleem is met deze SP."

msgid "Recommended"
msgstr "Aanbevolen"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Als u een ontwikkelaar bent die een single sign-on oplossing aan het "
"implementeren is, heeft u een probleem met de metadataconfiguratie. "
"Controleer of de metadata correct is geconfigureerd zowel bij de "
"identiteitsverstrekker als bij de service provider."

msgid "SimpleSAMLphp Advanced Features"
msgstr "SimpleSAMLphp Geavanceerde Functies"

msgid "Too short interval between single sign on events."
msgstr "Te kort interval tussen single sign on pogingen"

msgid "Checking your PHP installation"
msgstr "Test de PHP-installatie"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"De lengte van de query-parameters wordt beperkt door PHP's Suhosin-"
"extensie. Gelieve de optie suhosin.get.max_value_length te verhogen tot "
"tenminste 2048 bytes."

msgid "Useful links for your installation"
msgstr "Nuttige links"

msgid "Configuration"
msgstr "Configuratie"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "Lokale Shibboleth 1.3 Identity Provider Metadata (automatisch gegenereerd)"

msgid ""
"<strong>The configuration uses the default secret salt</strong> - make "
"sure you modify the default 'secretsalt' option in the simpleSAML "
"configuration in production environments. [<a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-install\">Read"
" more about SimpleSAMLphp configuration</a> ]"
msgstr ""
"<strong>De configuratie bevat de sandaard secret salt</strong> - verander"
" altijd de 'secretsalt'-optie in de simpleSAML-configuratie voor "
"productieomgevingen. [<a href=\"https://simplesamlphp.org/docs/stable"
"/simplesamlphp-install\">Lees meer over het configureren van "
"SimpleSAMLphp</a> ]"

msgid "Login as administrator"
msgstr "Login als beheerder"

msgid "Report this error"
msgstr "Meld deze error"


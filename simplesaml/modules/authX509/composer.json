{"name": "simplesamlphp/simplesamlphp-module-authx509", "description": "A module that is able to authenticate users based on X509 client certificates", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "X509"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "Jo<PERSON><PERSON>@surfnet.nl"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\authX509\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.5", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.7", "phpunit/phpunit": "~4.8.36"}, "extra": {"ssp-mixedcase-module-name": "authX509"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-authx509/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-authx509"}}
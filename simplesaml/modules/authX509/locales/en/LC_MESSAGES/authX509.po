
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: en\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{authX509:X509warning:proceed}"
msgstr "Proceed"

msgid "{authX509:X509warning:renew}"
msgstr "Please renew your certificate in time."

msgid "{authX509:X509error:certificate_text}"
msgstr "X509 certificate authentication is required to access this service."

msgid "{authX509:X509warning:renew_url}"
msgstr "Please  <a href='%renewurl%'>renew</a> your certificate in time."

msgid "{authX509:X509warning:warning_header}"
msgstr "Your certificate is about to expire."

msgid "{authX509:X509error:certificate_header}"
msgstr "X509 certificate authentication"

msgid "{authX509:X509warning:warning}"
msgstr "Your certificate will expire in %daysleft% days."

msgid "Please renew your certificate in time."
msgstr "Please renew your certificate in time."

#, python-format
msgid "Your certificate will expire in %daysleft% days."
msgstr "Your certificate will expire in %daysleft% days."

msgid "X509 certificate authentication"
msgstr "X509 certificate authentication"

#, python-format
msgid "Please <a href='%renewurl%'>renew your certificate</a> in time."
msgstr "Please <a href='%renewurl%'>renew your certificate</a> in time."

msgid "Proceed"
msgstr "Proceed"

msgid "X509 certificate authentication is required to access this service."
msgstr "X509 certificate authentication is required to access this service."

msgid "Your certificate is about to expire."
msgstr "Your certificate is about to expire."


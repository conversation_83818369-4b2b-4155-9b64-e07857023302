
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: el\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{authX509:X509warning:proceed}"
msgstr "Συνέχεια"

msgid "{authX509:X509warning:renew}"
msgstr "Παρακαλείστε να προχωρήσετε σε ανανέωση του πιστοποιητικού σας έγκαιρα."

msgid "{authX509:X509error:certificate_text}"
msgstr "Η πρόσβαση στην υπηρεσία απαιτεί ταυτοποίηση μέσω πιστοποιητικού X.509"

msgid "{authX509:X509warning:renew_url}"
msgstr ""
"Παρακαλείστε να προχωρήσετε σε <a href=‘%renewurl%’>ανανέωση</a> του "
"πιστοποιητικού σας έγκαιρα."

msgid "{authX509:X509warning:warning_header}"
msgstr "Η ισχύς του πιστοποιητικού σας πρόκειται να λήξει."

msgid "{authX509:X509error:certificate_header}"
msgstr "Ταυτοποίηση μέσω πιστοποιητικού X.509"

msgid "{authX509:X509warning:warning}"
msgstr "Η ισχύς του πιστοποιητικού σας θα λήξει σε %daysleft%."

msgid "Please renew your certificate in time."
msgstr "Παρακαλείστε να προχωρήσετε σε ανανέωση του πιστοποιητικού σας έγκαιρα."

#, python-format
msgid "Your certificate will expire in %daysleft% days."
msgstr "Η ισχύς του πιστοποιητικού σας θα λήξει σε %daysleft%."

msgid "X509 certificate authentication"
msgstr "Ταυτοποίηση μέσω πιστοποιητικού X.509"

#, python-format
msgid "Please <a href='%renewurl%'>renew your certificate</a> in time."
msgstr ""
"Παρακαλείστε να προχωρήσετε σε <a href=‘%renewurl%’>ανανέωση</a> του "
"πιστοποιητικού σας έγκαιρα."

msgid "Proceed"
msgstr "Συνέχεια"

msgid "X509 certificate authentication is required to access this service."
msgstr "Η πρόσβαση στην υπηρεσία απαιτεί ταυτοποίηση μέσω πιστοποιητικού X.509"

msgid "Your certificate is about to expire."
msgstr "Η ισχύς του πιστοποιητικού σας πρόκειται να λήξει."


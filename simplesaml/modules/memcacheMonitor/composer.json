{"name": "simplesamlphp/simplesamlphp-module-memcachemonitor", "description": "A module that is able display usage statistics of a memcache(d) store", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "memcachemonitor"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "~0.0.6"}, "extra": {"ssp-mixedcase-module-name": "memcacheMonitor"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-memcachemonitor/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-memcachemonitor"}}
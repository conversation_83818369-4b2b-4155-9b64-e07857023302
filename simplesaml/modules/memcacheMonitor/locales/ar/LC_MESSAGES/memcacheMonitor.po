
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ar\n"
"Language-Team: \n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n>=3 "
"&& n<=10 ? 3 : n>=11 && n<=99 ? 4 : 5)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{memcacheMonitor:memcachestat:total_connections}"
msgstr "العدد الكلي للاتصالات"

msgid "{memcacheMonitor:memcachestat:version}"
msgstr "الاصدار"

msgid "{memcacheMonitor:memcachestat:get_hits}"
msgstr "العدد الكلي لأوامر GET الناجحة"

msgid "{memcacheMonitor:memcachestat:curr_items}"
msgstr "عدد البنود الحالية"

msgid "{memcacheMonitor:memcachestat:uptime}"
msgstr "الاستعداد "

msgid "{memcacheMonitor:memcachestat:limit_maxbytes}"
msgstr "المساحة الكلية المتوفرة للتخزين"

msgid "{memcacheMonitor:memcachestat:curr_connections}"
msgstr "الاتصالات المفتوحة حالياً"

msgid "{memcacheMonitor:memcachestat:rusage_system}"
msgstr "عدد ثواني ال CPU (زمن النظام)"

msgid "{memcacheMonitor:memcachestat:rusage_user}"
msgstr "عدد ثواني ال CPU (زمن المستخدم)"

msgid "{memcacheMonitor:memcachestat:pid}"
msgstr "معرف العملية"

msgid "{memcacheMonitor:memcachestat:cmd_get}"
msgstr "عدد أوامر GET الكلي"

msgid "{memcacheMonitor:memcachestat:bytes_read}"
msgstr "عدد البايتس الداخلة للمشغل"

msgid "{memcacheMonitor:memcachestat:time}"
msgstr "الوقت حالياً"

msgid "{memcacheMonitor:memcachestat:get_misses}"
msgstr "العدد الكلي لأوامر GET الفاشلة"

msgid "{memcacheMonitor:memcachestat:bytes_written}"
msgstr "عدد البايتس المكتوبة بواسطة المشغل"

msgid "{memcacheMonitor:memcachestat:connection_structures}"
msgstr "الشكل التركيبي للاتصالات"

msgid "{memcacheMonitor:memcachestat:cmd_set}"
msgstr "عدد أوامر SET الكلي"

msgid "{memcacheMonitor:memcachestat:total_items}"
msgstr "العدد الكلي للبنود"

msgid "{memcacheMonitor:memcachestat:bytes}"
msgstr "ًعدد البايت المستخدمة حاليا"

msgid "Current time"
msgstr "الوقت حالياً"

msgid "Total items ever"
msgstr "العدد الكلي للبنود"

msgid "Bytes written by the server"
msgstr "عدد البايتس المكتوبة بواسطة المشغل"

msgid "Uptime"
msgstr "الاستعداد "

msgid "Current open connections"
msgstr "الاتصالات المفتوحة حالياً"

msgid "Total storage avail"
msgstr "المساحة الكلية المتوفرة للتخزين"

msgid "Version"
msgstr "الاصدار"

msgid "Total GET commands (failed)"
msgstr "العدد الكلي لأوامر GET الفاشلة"

msgid "Total SET commands"
msgstr "عدد أوامر SET الكلي"

msgid "Connection structures"
msgstr "الشكل التركيبي للاتصالات"

msgid "Total GET commands (success)"
msgstr "العدد الكلي لأوامر GET الناجحة"

msgid "Total bytes in use currently"
msgstr "ًعدد البايت المستخدمة حاليا"

msgid "Total GET commands"
msgstr "عدد أوامر GET الكلي"

msgid "Bytes in to the server"
msgstr "عدد البايتس الداخلة للمشغل"

msgid "Process ID"
msgstr "معرف العملية"

msgid "Currently number of items"
msgstr "عدد البنود الحالية"

msgid "CPU Seconds (User)"
msgstr "عدد ثواني ال CPU (زمن المستخدم)"

msgid "CPU Seconds (System)"
msgstr "عدد ثواني ال CPU (زمن النظام)"

msgid "Total connections"
msgstr "العدد الكلي للاتصالات"


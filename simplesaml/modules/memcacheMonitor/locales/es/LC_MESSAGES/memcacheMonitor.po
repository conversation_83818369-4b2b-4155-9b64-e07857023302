
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: es\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{memcacheMonitor:memcachestat:total_connections}"
msgstr "Total de conexiones"

msgid "{memcacheMonitor:memcachestat:version}"
msgstr "Versión"

msgid "{memcacheMonitor:memcachestat:get_hits}"
msgstr "Total de comandos GET (exitosos)"

msgid "{memcacheMonitor:memcachestat:curr_items}"
msgstr "Número actual de ítems"

msgid "{memcacheMonitor:memcachestat:uptime}"
msgstr "Tiempo transcurrido"

msgid "{memcacheMonitor:memcachestat:limit_maxbytes}"
msgstr "Almacenamiento total aprovechado"

msgid "{memcacheMonitor:memcachestat:curr_connections}"
msgstr "Conexiones abiertas actualmente"

msgid "{memcacheMonitor:memcachestat:rusage_system}"
msgstr "Segundos de CPU (Sistema)"

msgid "{memcacheMonitor:memcachestat:rusage_user}"
msgstr "Segundos de CPU (Usuario)"

msgid "{memcacheMonitor:memcachestat:pid}"
msgstr "Id de Proceso"

msgid "{memcacheMonitor:memcachestat:cmd_get}"
msgstr "Total de comandos GET"

msgid "{memcacheMonitor:memcachestat:bytes_read}"
msgstr "Bytes en el servidor"

msgid "{memcacheMonitor:memcachestat:time}"
msgstr "Tiempo actual"

msgid "{memcacheMonitor:memcachestat:get_misses}"
msgstr "Total de comandos GET (fallidos)"

msgid "{memcacheMonitor:memcachestat:bytes_written}"
msgstr "Bytes escritos por el servidor"

msgid "{memcacheMonitor:memcachestat:connection_structures}"
msgstr "Estructuras de conexión"

msgid "{memcacheMonitor:memcachestat:cmd_set}"
msgstr "Total de comandos SET"

msgid "{memcacheMonitor:memcachestat:total_items}"
msgstr "Número total de ítems"

msgid "{memcacheMonitor:memcachestat:bytes}"
msgstr "Cantidad total de bytes en uso actualmente"

msgid "Current time"
msgstr "Tiempo actual"

msgid "Total items ever"
msgstr "Número total de ítems"

msgid "Bytes written by the server"
msgstr "Bytes escritos por el servidor"

msgid "Uptime"
msgstr "Tiempo transcurrido"

msgid "Current open connections"
msgstr "Conexiones abiertas actualmente"

msgid "Total storage avail"
msgstr "Almacenamiento total aprovechado"

msgid "Version"
msgstr "Versión"

msgid "Total GET commands (failed)"
msgstr "Total de comandos GET (fallidos)"

msgid "Total SET commands"
msgstr "Total de comandos SET"

msgid "Connection structures"
msgstr "Estructuras de conexión"

msgid "Total GET commands (success)"
msgstr "Total de comandos GET (exitosos)"

msgid "Total bytes in use currently"
msgstr "Cantidad total de bytes en uso actualmente"

msgid "Total GET commands"
msgstr "Total de comandos GET"

msgid "Bytes in to the server"
msgstr "Bytes en el servidor"

msgid "Process ID"
msgstr "Id de Proceso"

msgid "Currently number of items"
msgstr "Número actual de ítems"

msgid "CPU Seconds (User)"
msgstr "Segundos de CPU (Usuario)"

msgid "CPU Seconds (System)"
msgstr "Segundos de CPU (Sistema)"

msgid "Total connections"
msgstr "Total de conexiones"

msgid "{memcacheMonitor:memcachestat:link_memcacheMonitor}"
msgstr "Estadísticas de memcache"

msgid "Memcache statistics"
msgstr "Estadísticas de memcache"


#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ja\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{memcacheMonitor:memcachestat:total_connections}"
msgstr "累計コネクション数"

msgid "{memcacheMonitor:memcachestat:version}"
msgstr "バージョン"

msgid "{memcacheMonitor:memcachestat:get_hits}"
msgstr "GETコマンド成功回数"

msgid "{memcacheMonitor:memcachestat:curr_items}"
msgstr "現在アイテム数"

msgid "{memcacheMonitor:memcachestat:uptime}"
msgstr "起動時間"

msgid "{memcacheMonitor:memcachestat:limit_maxbytes}"
msgstr "最大容量"

msgid "{memcacheMonitor:memcachestat:curr_connections}"
msgstr "現在のコネクション数"

msgid "{memcacheMonitor:memcachestat:rusage_system}"
msgstr "CPU時間(System)"

msgid "{memcacheMonitor:memcachestat:rusage_user}"
msgstr "CPU時間(User)"

msgid "{memcacheMonitor:memcachestat:pid}"
msgstr "pid"

msgid "{memcacheMonitor:memcachestat:cmd_get}"
msgstr "GETコマンド実行回数"

msgid "{memcacheMonitor:memcachestat:bytes_read}"
msgstr "受信データ量"

msgid "{memcacheMonitor:memcachestat:time}"
msgstr "現在時刻"

msgid "{memcacheMonitor:memcachestat:get_misses}"
msgstr "GETコマンド失敗回数"

msgid "{memcacheMonitor:memcachestat:bytes_written}"
msgstr "送信データ量"

msgid "{memcacheMonitor:memcachestat:connection_structures}"
msgstr "コネクション構造体数"

msgid "{memcacheMonitor:memcachestat:cmd_set}"
msgstr "SETコマンド実行回数"

msgid "{memcacheMonitor:memcachestat:total_items}"
msgstr "累計アイテム数"

msgid "{memcacheMonitor:memcachestat:bytes}"
msgstr "使用メモリ"

msgid "Current time"
msgstr "現在時刻"

msgid "Total items ever"
msgstr "累計アイテム数"

msgid "Bytes written by the server"
msgstr "送信データ量"

msgid "Uptime"
msgstr "起動時間"

msgid "Current open connections"
msgstr "現在のコネクション数"

msgid "Total storage avail"
msgstr "最大容量"

msgid "Version"
msgstr "バージョン"

msgid "Total GET commands (failed)"
msgstr "GETコマンド失敗回数"

msgid "Total SET commands"
msgstr "SETコマンド実行回数"

msgid "Connection structures"
msgstr "コネクション構造体数"

msgid "Total GET commands (success)"
msgstr "GETコマンド成功回数"

msgid "Total bytes in use currently"
msgstr "使用メモリ"

msgid "Total GET commands"
msgstr "GETコマンド実行回数"

msgid "Bytes in to the server"
msgstr "受信データ量"

msgid "Process ID"
msgstr "pid"

msgid "Currently number of items"
msgstr "現在アイテム数"

msgid "CPU Seconds (User)"
msgstr "CPU時間(User)"

msgid "CPU Seconds (System)"
msgstr "CPU時間(System)"

msgid "Total connections"
msgstr "累計コネクション数"


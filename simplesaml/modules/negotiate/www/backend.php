<?php

/**
 * Provide a URL for the module to statically link to.
 *
 * <AUTHOR> University of Oslo.
 *         <<EMAIL>>
 * @package SimpleSAMLphp
 */

$state = \SimpleSAML\Auth\State::loadState(
    $_REQUEST['AuthState'],
    \SimpleSAML\Module\negotiate\Auth\Source\Negotiate::STAGEID
);
\SimpleSAML\Logger::debug('backend - fallback: ' . $state['LogoutState']['negotiate:backend']);

\SimpleSAML\Module\negotiate\Auth\Source\Negotiate::fallBack($state);

exit;

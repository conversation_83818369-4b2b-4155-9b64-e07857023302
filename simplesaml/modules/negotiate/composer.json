{"name": "simplesamlphp/simplesamlphp-module-negotiate", "description": "The Negotiate module implements Microsofts Kerberos SPNEGO mechanism", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "negotiate"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\negotiate\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "suggest": {"ext-krb5": "Needed in case the SimpleSAMLphp negotiate module is used"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-negotiate/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-negotiate"}}
{"name": "simplesamlphp/simplesamlphp-module-radius", "description": "A module that is able perform authentication against a RADIUS server", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "radius"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\radius\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.7", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-radius/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-radius"}}
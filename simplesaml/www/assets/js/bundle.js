!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=10)}([function(e,t,n){(function(t){e.exports=t.jQuery=n(3)}).call(this,n(1))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var r,i,o;i=function(e){var t,n=[],r=Object.keys,i={},o={},s=/^(no-?highlight|plain|text)$/i,a=/\blang(?:uage)?-([\w-]+)\b/i,u=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,l="</span>",c={classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0},p="of and for in not or if then".split(" ");function f(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function h(e){return e.nodeName.toLowerCase()}function d(e){return s.test(e)}function g(e){var t,n={},r=Array.prototype.slice.call(arguments,1);for(t in e)n[t]=e[t];return r.forEach((function(e){for(t in e)n[t]=e[t]})),n}function v(e){var t=[];return function e(n,r){for(var i=n.firstChild;i;i=i.nextSibling)3===i.nodeType?r+=i.nodeValue.length:1===i.nodeType&&(t.push({event:"start",offset:r,node:i}),r=e(i,r),h(i).match(/br|hr|img|input/)||t.push({event:"stop",offset:r,node:i}));return r}(e,0),t}function y(e){return e.variants&&!e.cached_variants&&(e.cached_variants=e.variants.map((function(t){return g(e,{variants:null},t)}))),e.cached_variants?e.cached_variants:function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(e)?[g(e,{starts:e.starts?g(e.starts):null})]:[e]}function m(e){if(t&&!e.langApiRestored){for(var n in e.langApiRestored=!0,t)e[n]&&(e[t[n]]=e[n]);(e.contains||[]).concat(e.variants||[]).forEach(m)}}function b(e,t){return t?Number(t):(n=e,-1!=p.indexOf(n.toLowerCase())?0:1);var n}function w(e){function t(e){return e&&e.source||e}function n(n,r){return new RegExp(t(n),"m"+(e.case_insensitive?"i":"")+(r?"g":""))}function i(e){var r,i,o={},s=[],a={},u=1;function l(e,t){o[u]=e,s.push([e,t]),u+=function(e){return new RegExp(e.toString()+"|").exec("").length-1}(t)+1}for(var c=0;c<e.contains.length;c++)l(i=e.contains[c],i.beginKeywords?"\\.?(?:"+i.begin+")\\.?":i.begin);e.terminator_end&&l("end",e.terminator_end),e.illegal&&l("illegal",e.illegal);var p=s.map((function(e){return e[1]}));return r=n(function(e,n){for(var r=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./,i=0,o="",s=0;s<e.length;s++){var a=i+=1,u=t(e[s]);for(s>0&&(o+=n),o+="(";u.length>0;){var l=r.exec(u);if(null==l){o+=u;break}o+=u.substring(0,l.index),u=u.substring(l.index+l[0].length),"\\"==l[0][0]&&l[1]?o+="\\"+String(Number(l[1])+a):(o+=l[0],"("==l[0]&&i++)}o+=")"}return o}(p,"|"),!0),a.lastIndex=0,a.exec=function(t){var n;if(0===s.length)return null;r.lastIndex=a.lastIndex;var i=r.exec(t);if(!i)return null;for(var u=0;u<i.length;u++)if(null!=i[u]&&null!=o[""+u]){n=o[""+u];break}return"string"==typeof n?(i.type=n,i.extra=[e.illegal,e.terminator_end]):(i.type="begin",i.rule=n),i},a}!function o(s,a){s.compiled||(s.compiled=!0,s.keywords=s.keywords||s.beginKeywords,s.keywords&&(s.keywords=function(e,t){var n={};return"string"==typeof e?i("keyword",e):r(e).forEach((function(t){i(t,e[t])})),n;function i(e,r){t&&(r=r.toLowerCase()),r.split(" ").forEach((function(t){var r=t.split("|");n[r[0]]=[e,b(r[0],r[1])]}))}}(s.keywords,e.case_insensitive)),s.lexemesRe=n(s.lexemes||/\w+/,!0),a&&(s.beginKeywords&&(s.begin="\\b("+s.beginKeywords.split(" ").join("|")+")\\b"),s.begin||(s.begin=/\B|\b/),s.beginRe=n(s.begin),s.endSameAsBegin&&(s.end=s.begin),s.end||s.endsWithParent||(s.end=/\B|\b/),s.end&&(s.endRe=n(s.end)),s.terminator_end=t(s.end)||"",s.endsWithParent&&a.terminator_end&&(s.terminator_end+=(s.end?"|":"")+a.terminator_end)),s.illegal&&(s.illegalRe=n(s.illegal)),null==s.relevance&&(s.relevance=1),s.contains||(s.contains=[]),s.contains=Array.prototype.concat.apply([],s.contains.map((function(e){return y("self"===e?s:e)}))),s.contains.forEach((function(e){o(e,s)})),s.starts&&o(s.starts,a),s.terminators=i(s))}(e)}function x(e,t,n,r){function o(e,t){var n=v.case_insensitive?t[0].toLowerCase():t[0];return e.keywords.hasOwnProperty(n)&&e.keywords[n]}function s(e,t,n,r){if(!n&&""===t)return"";if(!e)return t;var i='<span class="'+(r?"":c.classPrefix);return(i+=e+'">')+t+(n?"":l)}function a(){C+=null!=m.subLanguage?function(){var e="string"==typeof m.subLanguage;if(e&&!i[m.subLanguage])return f(T);var t=e?x(m.subLanguage,T,!0,b[m.subLanguage]):O(T,m.subLanguage.length?m.subLanguage:void 0);return m.relevance>0&&(S+=t.relevance),e&&(b[m.subLanguage]=t.top),s(t.language,t.value,!1,!0)}():function(){var e,t,n,r;if(!m.keywords)return f(T);for(r="",t=0,m.lexemesRe.lastIndex=0,n=m.lexemesRe.exec(T);n;)r+=f(T.substring(t,n.index)),(e=o(m,n))?(S+=e[1],r+=s(e[0],f(n[0]))):r+=f(n[0]),t=m.lexemesRe.lastIndex,n=m.lexemesRe.exec(T);return r+f(T.substr(t))}(),T=""}function u(e){C+=e.className?s(e.className,"",!0):"",m=Object.create(e,{parent:{value:m}})}function p(e){var t=e[0],n=e.rule;return n&&n.endSameAsBegin&&(n.endRe=function(e){return new RegExp(e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")}(t)),n.skip?T+=t:(n.excludeBegin&&(T+=t),a(),n.returnBegin||n.excludeBegin||(T=t)),u(n),n.returnBegin?0:t.length}function h(e){var t=e[0],n=function e(t,n){if(function(e,t){var n=e&&e.exec(t);return n&&0===n.index}(t.endRe,n)){for(;t.endsParent&&t.parent;)t=t.parent;return t}if(t.endsWithParent)return e(t.parent,n)}(m,t);if(n){var r=m;r.skip?T+=t:(r.returnEnd||r.excludeEnd||(T+=t),a(),r.excludeEnd&&(T=t));do{m.className&&(C+=l),m.skip||m.subLanguage||(S+=m.relevance),m=m.parent}while(m!==n.parent);return n.starts&&(n.endSameAsBegin&&(n.starts.endRe=n.endRe),u(n.starts)),r.returnEnd?0:t.length}}var d={};function g(e,r){var i=r&&r[0];if(T+=e,null==i)return a(),0;if("begin"==d.type&&"end"==r.type&&d.index==r.index&&""===i)return T+=t.slice(r.index,r.index+1),1;if(d=r,"begin"===r.type)return p(r);if("illegal"===r.type&&!n)throw new Error('Illegal lexeme "'+i+'" for mode "'+(m.className||"<unnamed>")+'"');if("end"===r.type){var o=h(r);if(null!=o)return o}return T+=i,i.length}var v=E(e);if(!v)throw new Error('Unknown language: "'+e+'"');w(v);var y,m=r||v,b={},C="";for(y=m;y!==v;y=y.parent)y.className&&(C=s(y.className,"",!0)+C);var T="",S=0;try{for(var j,_,N=0;m.terminators.lastIndex=N,j=m.terminators.exec(t);)_=g(t.substring(N,j.index),j),N=j.index+_;for(g(t.substr(N)),y=m;y.parent;y=y.parent)y.className&&(C+=l);return{relevance:S,value:C,illegal:!1,language:e,top:m}}catch(e){if(e.message&&-1!==e.message.indexOf("Illegal"))return{illegal:!0,relevance:0,value:f(t)};throw e}}function O(e,t){t=t||c.languages||r(i);var n={relevance:0,value:f(e)},o=n;return t.filter(E).filter(j).forEach((function(t){var r=x(t,e,!1);r.language=t,r.relevance>o.relevance&&(o=r),r.relevance>n.relevance&&(o=n,n=r)})),o.language&&(n.second_best=o),n}function C(e){return c.tabReplace||c.useBR?e.replace(u,(function(e,t){return c.useBR&&"\n"===e?"<br>":c.tabReplace?t.replace(/\t/g,c.tabReplace):""})):e}function T(e){var t,r,i,s,u,l=function(e){var t,n,r,i,o=e.className+" ";if(o+=e.parentNode?e.parentNode.className:"",n=a.exec(o))return E(n[1])?n[1]:"no-highlight";for(t=0,r=(o=o.split(/\s+/)).length;t<r;t++)if(d(i=o[t])||E(i))return i}(e);d(l)||(c.useBR?(t=document.createElementNS("http://www.w3.org/1999/xhtml","div")).innerHTML=e.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n"):t=e,u=t.textContent,i=l?x(l,u,!0):O(u),(r=v(t)).length&&((s=document.createElementNS("http://www.w3.org/1999/xhtml","div")).innerHTML=i.value,i.value=function(e,t,r){var i=0,o="",s=[];function a(){return e.length&&t.length?e[0].offset!==t[0].offset?e[0].offset<t[0].offset?e:t:"start"===t[0].event?e:t:e.length?e:t}function u(e){o+="<"+h(e)+n.map.call(e.attributes,(function(e){return" "+e.nodeName+'="'+f(e.value).replace('"',"&quot;")+'"'})).join("")+">"}function l(e){o+="</"+h(e)+">"}function c(e){("start"===e.event?u:l)(e.node)}for(;e.length||t.length;){var p=a();if(o+=f(r.substring(i,p[0].offset)),i=p[0].offset,p===e){s.reverse().forEach(l);do{c(p.splice(0,1)[0]),p=a()}while(p===e&&p.length&&p[0].offset===i);s.reverse().forEach(u)}else"start"===p[0].event?s.push(p[0].node):s.pop(),c(p.splice(0,1)[0])}return o+f(r.substr(i))}(r,v(s),u)),i.value=C(i.value),e.innerHTML=i.value,e.className=function(e,t,n){var r=t?o[t]:n,i=[e.trim()];return e.match(/\bhljs\b/)||i.push("hljs"),-1===e.indexOf(r)&&i.push(r),i.join(" ").trim()}(e.className,l,i.language),e.result={language:i.language,re:i.relevance},i.second_best&&(e.second_best={language:i.second_best.language,re:i.second_best.relevance}))}function S(){if(!S.called){S.called=!0;var e=document.querySelectorAll("pre code");n.forEach.call(e,T)}}function E(e){return e=(e||"").toLowerCase(),i[e]||i[o[e]]}function j(e){var t=E(e);return t&&!t.disableAutodetect}return e.highlight=x,e.highlightAuto=O,e.fixMarkup=C,e.highlightBlock=T,e.configure=function(e){c=g(c,e)},e.initHighlighting=S,e.initHighlightingOnLoad=function(){addEventListener("DOMContentLoaded",S,!1),addEventListener("load",S,!1)},e.registerLanguage=function(t,n){var r=i[t]=n(e);m(r),r.rawDefinition=n.bind(null,e),r.aliases&&r.aliases.forEach((function(e){o[e]=t}))},e.listLanguages=function(){return r(i)},e.getLanguage=E,e.autoDetection=j,e.inherit=g,e.IDENT_RE="[a-zA-Z]\\w*",e.UNDERSCORE_IDENT_RE="[a-zA-Z_]\\w*",e.NUMBER_RE="\\b\\d+(\\.\\d+)?",e.C_NUMBER_RE="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",e.BINARY_NUMBER_RE="\\b(0b[01]+)",e.RE_STARTERS_RE="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",e.BACKSLASH_ESCAPE={begin:"\\\\[\\s\\S]",relevance:0},e.APOS_STRING_MODE={className:"string",begin:"'",end:"'",illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]},e.QUOTE_STRING_MODE={className:"string",begin:'"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]},e.PHRASAL_WORDS_MODE={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},e.COMMENT=function(t,n,r){var i=e.inherit({className:"comment",begin:t,end:n,contains:[]},r||{});return i.contains.push(e.PHRASAL_WORDS_MODE),i.contains.push({className:"doctag",begin:"(?:TODO|FIXME|NOTE|BUG|XXX):",relevance:0}),i},e.C_LINE_COMMENT_MODE=e.COMMENT("//","$"),e.C_BLOCK_COMMENT_MODE=e.COMMENT("/\\*","\\*/"),e.HASH_COMMENT_MODE=e.COMMENT("#","$"),e.NUMBER_MODE={className:"number",begin:e.NUMBER_RE,relevance:0},e.C_NUMBER_MODE={className:"number",begin:e.C_NUMBER_RE,relevance:0},e.BINARY_NUMBER_MODE={className:"number",begin:e.BINARY_NUMBER_RE,relevance:0},e.CSS_NUMBER_MODE={className:"number",begin:e.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},e.REGEXP_MODE={className:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[e.BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[e.BACKSLASH_ESCAPE]}]},e.TITLE_MODE={className:"title",begin:e.IDENT_RE,relevance:0},e.UNDERSCORE_TITLE_MODE={className:"title",begin:e.UNDERSCORE_IDENT_RE,relevance:0},e.METHOD_GUARD={begin:"\\.\\s*"+e.UNDERSCORE_IDENT_RE,relevance:0},e},o="object"==typeof window&&window||"object"==typeof self&&self,t.nodeType?o&&(o.hljs=i({}),void 0===(r=function(){return o.hljs}.apply(t,[]))||(e.exports=r)):i(t)},function(e,t,n){(function(t){e.exports=t.$=n(4)}).call(this,n(1))},function(e,t,n){var r;
/*!
 * jQuery JavaScript Library v3.4.1
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2019-05-01T21:04Z
 */!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(n,i){"use strict";var o=[],s=n.document,a=Object.getPrototypeOf,u=o.slice,l=o.concat,c=o.push,p=o.indexOf,f={},h=f.toString,d=f.hasOwnProperty,g=d.toString,v=g.call(Object),y={},m=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},b=function(e){return null!=e&&e===e.window},w={type:!0,src:!0,nonce:!0,noModule:!0};function x(e,t,n){var r,i,o=(n=n||s).createElement("script");if(o.text=e,t)for(r in w)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function O(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?f[h.call(e)]||"object":typeof e}var C=function(e,t){return new C.fn.init(e,t)},T=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;function S(e){var t=!!e&&"length"in e&&e.length,n=O(e);return!m(e)&&!b(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}C.fn=C.prototype={jquery:"3.4.1",constructor:C,length:0,toArray:function(){return u.call(this)},get:function(e){return null==e?u.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=C.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return C.each(this,e)},map:function(e){return this.pushStack(C.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(u.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:o.sort,splice:o.splice},C.extend=C.fn.extend=function(){var e,t,n,r,i,o,s=arguments[0]||{},a=1,u=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[a]||{},a++),"object"==typeof s||m(s)||(s={}),a===u&&(s=this,a--);a<u;a++)if(null!=(e=arguments[a]))for(t in e)r=e[t],"__proto__"!==t&&s!==r&&(l&&r&&(C.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[t],o=i&&!Array.isArray(n)?[]:i||C.isPlainObject(n)?n:{},i=!1,s[t]=C.extend(l,o,r)):void 0!==r&&(s[t]=r));return s},C.extend({expando:"jQuery"+("3.4.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==h.call(e))&&(!(t=a(e))||"function"==typeof(n=d.call(t,"constructor")&&t.constructor)&&g.call(n)===v)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t){x(e,{nonce:t&&t.nonce})},each:function(e,t){var n,r=0;if(S(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(T,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(S(Object(e))?C.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:p.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!==s&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,s=[];if(S(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&s.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&s.push(i);return l.apply([],s)},guid:1,support:y}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=o[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){f["[object "+t+"]"]=t.toLowerCase()}));var E=
/*!
 * Sizzle CSS Selector Engine v2.3.4
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://js.foundation/
 *
 * Date: 2019-04-08
 */
function(e){var t,n,r,i,o,s,a,u,l,c,p,f,h,d,g,v,y,m,b,w="sizzle"+1*new Date,x=e.document,O=0,C=0,T=ue(),S=ue(),E=ue(),j=ue(),_=function(e,t){return e===t&&(p=!0),0},N={}.hasOwnProperty,A=[],k=A.pop,I=A.push,P=A.push,M=A.slice,D=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},R="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",L="[\\x20\\t\\r\\n\\f]",$="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",F="\\["+L+"*("+$+")(?:"+L+"*([*^$|!~]?=)"+L+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$+"))|)"+L+"*\\]",q=":("+$+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+F+")*)|.*)\\)|)",H=new RegExp(L+"+","g"),B=new RegExp("^"+L+"+|((?:^|[^\\\\])(?:\\\\.)*)"+L+"+$","g"),z=new RegExp("^"+L+"*,"+L+"*"),W=new RegExp("^"+L+"*([>+~]|"+L+")"+L+"*"),U=new RegExp(L+"|>"),V=new RegExp(q),G=new RegExp("^"+$+"$"),K={ID:new RegExp("^#("+$+")"),CLASS:new RegExp("^\\.("+$+")"),TAG:new RegExp("^("+$+"|[*])"),ATTR:new RegExp("^"+F),PSEUDO:new RegExp("^"+q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+L+"*(even|odd|(([+-]|)(\\d*)n|)"+L+"*(?:([+-]|)"+L+"*(\\d+)|))"+L+"*\\)|)","i"),bool:new RegExp("^(?:"+R+")$","i"),needsContext:new RegExp("^"+L+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+L+"*((?:-\\d)?\\d*)"+L+"*\\)|)(?=[^-]|$)","i")},X=/HTML$/i,Q=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,J=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\([\\da-f]{1,6}"+L+"?|("+L+")|.)","ig"),ne=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ie=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},oe=function(){f()},se=we((function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()}),{dir:"parentNode",next:"legend"});try{P.apply(A=M.call(x.childNodes),x.childNodes),A[x.childNodes.length].nodeType}catch(e){P={apply:A.length?function(e,t){I.apply(e,M.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function ae(e,t,r,i){var o,a,l,c,p,d,y,m=t&&t.ownerDocument,O=t?t.nodeType:9;if(r=r||[],"string"!=typeof e||!e||1!==O&&9!==O&&11!==O)return r;if(!i&&((t?t.ownerDocument||t:x)!==h&&f(t),t=t||h,g)){if(11!==O&&(p=Z.exec(e)))if(o=p[1]){if(9===O){if(!(l=t.getElementById(o)))return r;if(l.id===o)return r.push(l),r}else if(m&&(l=m.getElementById(o))&&b(t,l)&&l.id===o)return r.push(l),r}else{if(p[2])return P.apply(r,t.getElementsByTagName(e)),r;if((o=p[3])&&n.getElementsByClassName&&t.getElementsByClassName)return P.apply(r,t.getElementsByClassName(o)),r}if(n.qsa&&!j[e+" "]&&(!v||!v.test(e))&&(1!==O||"object"!==t.nodeName.toLowerCase())){if(y=e,m=t,1===O&&U.test(e)){for((c=t.getAttribute("id"))?c=c.replace(re,ie):t.setAttribute("id",c=w),a=(d=s(e)).length;a--;)d[a]="#"+c+" "+be(d[a]);y=d.join(","),m=ee.test(e)&&ye(t.parentNode)||t}try{return P.apply(r,m.querySelectorAll(y)),r}catch(t){j(e,!0)}finally{c===w&&t.removeAttribute("id")}}}return u(e.replace(B,"$1"),t,r,i)}function ue(){var e=[];return function t(n,i){return e.push(n+" ")>r.cacheLength&&delete t[e.shift()],t[n+" "]=i}}function le(e){return e[w]=!0,e}function ce(e){var t=h.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function pe(e,t){for(var n=e.split("|"),i=n.length;i--;)r.attrHandle[n[i]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function de(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function ge(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&se(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ve(e){return le((function(t){return t=+t,le((function(n,r){for(var i,o=e([],n.length,t),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))}))}))}function ye(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in n=ae.support={},o=ae.isXML=function(e){var t=e.namespaceURI,n=(e.ownerDocument||e).documentElement;return!X.test(t||n&&n.nodeName||"HTML")},f=ae.setDocument=function(e){var t,i,s=e?e.ownerDocument||e:x;return s!==h&&9===s.nodeType&&s.documentElement?(d=(h=s).documentElement,g=!o(h),x!==h&&(i=h.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",oe,!1):i.attachEvent&&i.attachEvent("onunload",oe)),n.attributes=ce((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=ce((function(e){return e.appendChild(h.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=J.test(h.getElementsByClassName),n.getById=ce((function(e){return d.appendChild(e).id=w,!h.getElementsByName||!h.getElementsByName(w).length})),n.getById?(r.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},r.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}}):(r.filter.ID=function(e){var t=e.replace(te,ne);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},r.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),r.find.TAG=n.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},r.find.CLASS=n.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&g)return t.getElementsByClassName(e)},y=[],v=[],(n.qsa=J.test(h.querySelectorAll))&&(ce((function(e){d.appendChild(e).innerHTML="<a id='"+w+"'></a><select id='"+w+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]="+L+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+L+"*(?:value|"+R+")"),e.querySelectorAll("[id~="+w+"-]").length||v.push("~="),e.querySelectorAll(":checked").length||v.push(":checked"),e.querySelectorAll("a#"+w+"+*").length||v.push(".#.+[+~]")})),ce((function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=h.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+L+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&v.push(":enabled",":disabled"),d.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")}))),(n.matchesSelector=J.test(m=d.matches||d.webkitMatchesSelector||d.mozMatchesSelector||d.oMatchesSelector||d.msMatchesSelector))&&ce((function(e){n.disconnectedMatch=m.call(e,"*"),m.call(e,"[s!='']:x"),y.push("!=",q)})),v=v.length&&new RegExp(v.join("|")),y=y.length&&new RegExp(y.join("|")),t=J.test(d.compareDocumentPosition),b=t||J.test(d.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},_=t?function(e,t){if(e===t)return p=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(1&(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===r?e===h||e.ownerDocument===x&&b(x,e)?-1:t===h||t.ownerDocument===x&&b(x,t)?1:c?D(c,e)-D(c,t):0:4&r?-1:1)}:function(e,t){if(e===t)return p=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!i||!o)return e===h?-1:t===h?1:i?-1:o?1:c?D(c,e)-D(c,t):0;if(i===o)return fe(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?fe(s[r],a[r]):s[r]===x?-1:a[r]===x?1:0},h):h},ae.matches=function(e,t){return ae(e,null,null,t)},ae.matchesSelector=function(e,t){if((e.ownerDocument||e)!==h&&f(e),n.matchesSelector&&g&&!j[t+" "]&&(!y||!y.test(t))&&(!v||!v.test(t)))try{var r=m.call(e,t);if(r||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(e){j(t,!0)}return ae(t,h,null,[e]).length>0},ae.contains=function(e,t){return(e.ownerDocument||e)!==h&&f(e),b(e,t)},ae.attr=function(e,t){(e.ownerDocument||e)!==h&&f(e);var i=r.attrHandle[t.toLowerCase()],o=i&&N.call(r.attrHandle,t.toLowerCase())?i(e,t,!g):void 0;return void 0!==o?o:n.attributes||!g?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},ae.escape=function(e){return(e+"").replace(re,ie)},ae.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ae.uniqueSort=function(e){var t,r=[],i=0,o=0;if(p=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(_),p){for(;t=e[o++];)t===e[o]&&(i=r.push(o));for(;i--;)e.splice(r[i],1)}return c=null,e},i=ae.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=i(t);return n},(r=ae.selectors={cacheLength:50,createPseudo:le,match:K,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ae.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ae.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return K.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&V.test(n)&&(t=s(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=T[e+" "];return t||(t=new RegExp("(^|"+L+")"+e+"("+L+"|$)"))&&T(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=ae.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(H," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,p,f,h,d,g=o!==s?"nextSibling":"previousSibling",v=t.parentNode,y=a&&t.nodeName.toLowerCase(),m=!u&&!a,b=!1;if(v){if(o){for(;g;){for(f=t;f=f[g];)if(a?f.nodeName.toLowerCase()===y:1===f.nodeType)return!1;d=g="only"===e&&!d&&"nextSibling"}return!0}if(d=[s?v.firstChild:v.lastChild],s&&m){for(b=(h=(l=(c=(p=(f=v)[w]||(f[w]={}))[f.uniqueID]||(p[f.uniqueID]={}))[e]||[])[0]===O&&l[1])&&l[2],f=h&&v.childNodes[h];f=++h&&f&&f[g]||(b=h=0)||d.pop();)if(1===f.nodeType&&++b&&f===t){c[e]=[O,h,b];break}}else if(m&&(b=h=(l=(c=(p=(f=t)[w]||(f[w]={}))[f.uniqueID]||(p[f.uniqueID]={}))[e]||[])[0]===O&&l[1]),!1===b)for(;(f=++h&&f&&f[g]||(b=h=0)||d.pop())&&((a?f.nodeName.toLowerCase()!==y:1!==f.nodeType)||!++b||(m&&((c=(p=f[w]||(f[w]={}))[f.uniqueID]||(p[f.uniqueID]={}))[e]=[O,b]),f!==t)););return(b-=i)===r||b%r==0&&b/r>=0}}},PSEUDO:function(e,t){var n,i=r.pseudos[e]||r.setFilters[e.toLowerCase()]||ae.error("unsupported pseudo: "+e);return i[w]?i(t):i.length>1?(n=[e,e,"",t],r.setFilters.hasOwnProperty(e.toLowerCase())?le((function(e,n){for(var r,o=i(e,t),s=o.length;s--;)e[r=D(e,o[s])]=!(n[r]=o[s])})):function(e){return i(e,0,n)}):i}},pseudos:{not:le((function(e){var t=[],n=[],r=a(e.replace(B,"$1"));return r[w]?le((function(e,t,n,i){for(var o,s=r(e,null,i,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:le((function(e){return function(t){return ae(e,t).length>0}})),contains:le((function(e){return e=e.replace(te,ne),function(t){return(t.textContent||i(t)).indexOf(e)>-1}})),lang:le((function(e){return G.test(e||"")||ae.error("unsupported lang: "+e),e=e.replace(te,ne).toLowerCase(),function(t){var n;do{if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===d},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!r.pseudos.empty(e)},header:function(e){return Y.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ve((function(){return[0]})),last:ve((function(e,t){return[t-1]})),eq:ve((function(e,t,n){return[n<0?n+t:n]})),even:ve((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:ve((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:ve((function(e,t,n){for(var r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e})),gt:ve((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}}).pseudos.nth=r.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[t]=he(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=de(t);function me(){}function be(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function we(e,t,n){var r=t.dir,i=t.next,o=i||r,s=n&&"parentNode"===o,a=C++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||s)return e(t,n,i);return!1}:function(t,n,u){var l,c,p,f=[O,a];if(u){for(;t=t[r];)if((1===t.nodeType||s)&&e(t,n,u))return!0}else for(;t=t[r];)if(1===t.nodeType||s)if(c=(p=t[w]||(t[w]={}))[t.uniqueID]||(p[t.uniqueID]={}),i&&i===t.nodeName.toLowerCase())t=t[r]||t;else{if((l=c[o])&&l[0]===O&&l[1]===a)return f[2]=l[2];if(c[o]=f,f[2]=e(t,n,u))return!0}return!1}}function xe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function Oe(e,t,n,r,i){for(var o,s=[],a=0,u=e.length,l=null!=t;a<u;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),l&&t.push(a)));return s}function Ce(e,t,n,r,i,o){return r&&!r[w]&&(r=Ce(r)),i&&!i[w]&&(i=Ce(i,o)),le((function(o,s,a,u){var l,c,p,f=[],h=[],d=s.length,g=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)ae(e,t[r],n);return n}(t||"*",a.nodeType?[a]:a,[]),v=!e||!o&&t?g:Oe(g,f,e,a,u),y=n?i||(o?e:d||r)?[]:s:v;if(n&&n(v,y,a,u),r)for(l=Oe(y,h),r(l,[],a,u),c=l.length;c--;)(p=l[c])&&(y[h[c]]=!(v[h[c]]=p));if(o){if(i||e){if(i){for(l=[],c=y.length;c--;)(p=y[c])&&l.push(v[c]=p);i(null,y=[],l,u)}for(c=y.length;c--;)(p=y[c])&&(l=i?D(o,p):f[c])>-1&&(o[l]=!(s[l]=p))}}else y=Oe(y===s?y.splice(d,y.length):y),i?i(null,s,y,u):P.apply(s,y)}))}function Te(e){for(var t,n,i,o=e.length,s=r.relative[e[0].type],a=s||r.relative[" "],u=s?1:0,c=we((function(e){return e===t}),a,!0),p=we((function(e){return D(t,e)>-1}),a,!0),f=[function(e,n,r){var i=!s&&(r||n!==l)||((t=n).nodeType?c(e,n,r):p(e,n,r));return t=null,i}];u<o;u++)if(n=r.relative[e[u].type])f=[we(xe(f),n)];else{if((n=r.filter[e[u].type].apply(null,e[u].matches))[w]){for(i=++u;i<o&&!r.relative[e[i].type];i++);return Ce(u>1&&xe(f),u>1&&be(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(B,"$1"),n,u<i&&Te(e.slice(u,i)),i<o&&Te(e=e.slice(i)),i<o&&be(e))}f.push(n)}return xe(f)}return me.prototype=r.filters=r.pseudos,r.setFilters=new me,s=ae.tokenize=function(e,t){var n,i,o,s,a,u,l,c=S[e+" "];if(c)return t?0:c.slice(0);for(a=e,u=[],l=r.preFilter;a;){for(s in n&&!(i=z.exec(a))||(i&&(a=a.slice(i[0].length)||a),u.push(o=[])),n=!1,(i=W.exec(a))&&(n=i.shift(),o.push({value:n,type:i[0].replace(B," ")}),a=a.slice(n.length)),r.filter)!(i=K[s].exec(a))||l[s]&&!(i=l[s](i))||(n=i.shift(),o.push({value:n,type:s,matches:i}),a=a.slice(n.length));if(!n)break}return t?a.length:a?ae.error(e):S(e,u).slice(0)},a=ae.compile=function(e,t){var n,i=[],o=[],a=E[e+" "];if(!a){for(t||(t=s(e)),n=t.length;n--;)(a=Te(t[n]))[w]?i.push(a):o.push(a);(a=E(e,function(e,t){var n=t.length>0,i=e.length>0,o=function(o,s,a,u,c){var p,d,v,y=0,m="0",b=o&&[],w=[],x=l,C=o||i&&r.find.TAG("*",c),T=O+=null==x?1:Math.random()||.1,S=C.length;for(c&&(l=s===h||s||c);m!==S&&null!=(p=C[m]);m++){if(i&&p){for(d=0,s||p.ownerDocument===h||(f(p),a=!g);v=e[d++];)if(v(p,s||h,a)){u.push(p);break}c&&(O=T)}n&&((p=!v&&p)&&y--,o&&b.push(p))}if(y+=m,n&&m!==y){for(d=0;v=t[d++];)v(b,w,s,a);if(o){if(y>0)for(;m--;)b[m]||w[m]||(w[m]=k.call(u));w=Oe(w)}P.apply(u,w),c&&!o&&w.length>0&&y+t.length>1&&ae.uniqueSort(u)}return c&&(O=T,l=x),b};return n?le(o):o}(o,i))).selector=e}return a},u=ae.select=function(e,t,n,i){var o,u,l,c,p,f="function"==typeof e&&e,h=!i&&s(e=f.selector||e);if(n=n||[],1===h.length){if((u=h[0]=h[0].slice(0)).length>2&&"ID"===(l=u[0]).type&&9===t.nodeType&&g&&r.relative[u[1].type]){if(!(t=(r.find.ID(l.matches[0].replace(te,ne),t)||[])[0]))return n;f&&(t=t.parentNode),e=e.slice(u.shift().value.length)}for(o=K.needsContext.test(e)?0:u.length;o--&&(l=u[o],!r.relative[c=l.type]);)if((p=r.find[c])&&(i=p(l.matches[0].replace(te,ne),ee.test(u[0].type)&&ye(t.parentNode)||t))){if(u.splice(o,1),!(e=i.length&&be(u)))return P.apply(n,i),n;break}}return(f||a(e,h))(i,t,!g,n,!t||ee.test(e)&&ye(t.parentNode)||t),n},n.sortStable=w.split("").sort(_).join("")===w,n.detectDuplicates=!!p,f(),n.sortDetached=ce((function(e){return 1&e.compareDocumentPosition(h.createElement("fieldset"))})),ce((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||pe("type|href|height|width",(function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&ce((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||pe("value",(function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue})),ce((function(e){return null==e.getAttribute("disabled")}))||pe(R,(function(e,t,n){var r;if(!n)return!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null})),ae}(n);C.find=E,C.expr=E.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=E.uniqueSort,C.text=E.getText,C.isXMLDoc=E.isXML,C.contains=E.contains,C.escapeSelector=E.escape;var j=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&C(e).is(n))break;r.push(e)}return r},_=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},N=C.expr.match.needsContext;function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var k=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function I(e,t,n){return m(t)?C.grep(e,(function(e,r){return!!t.call(e,r,e)!==n})):t.nodeType?C.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?C.grep(e,(function(e){return p.call(t,e)>-1!==n})):C.filter(t,e,n)}C.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?C.find.matchesSelector(r,e)?[r]:[]:C.find.matches(e,C.grep(t,(function(e){return 1===e.nodeType})))},C.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(C(e).filter((function(){for(t=0;t<r;t++)if(C.contains(i[t],this))return!0})));for(n=this.pushStack([]),t=0;t<r;t++)C.find(e,i[t],n);return r>1?C.uniqueSort(n):n},filter:function(e){return this.pushStack(I(this,e||[],!1))},not:function(e){return this.pushStack(I(this,e||[],!0))},is:function(e){return!!I(this,"string"==typeof e&&N.test(e)?C(e):e||[],!1).length}});var P,M=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||P,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:M.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:s,!0)),k.test(r[1])&&C.isPlainObject(t))for(r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=s.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this)}).prototype=C.fn,P=C(s);var D=/^(?:parents|prev(?:Until|All))/,R={children:!0,contents:!0,next:!0,prev:!0};function L(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0}))},closest:function(e,t){var n,r=0,i=this.length,o=[],s="string"!=typeof e&&C(e);if(!N.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&C.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?C.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?p.call(C(e),this[0]):p.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return j(e,"parentNode")},parentsUntil:function(e,t,n){return j(e,"parentNode",n)},next:function(e){return L(e,"nextSibling")},prev:function(e){return L(e,"previousSibling")},nextAll:function(e){return j(e,"nextSibling")},prevAll:function(e){return j(e,"previousSibling")},nextUntil:function(e,t,n){return j(e,"nextSibling",n)},prevUntil:function(e,t,n){return j(e,"previousSibling",n)},siblings:function(e){return _((e.parentNode||{}).firstChild,e)},children:function(e){return _(e.firstChild)},contents:function(e){return void 0!==e.contentDocument?e.contentDocument:(A(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},(function(e,t){C.fn[e]=function(n,r){var i=C.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=C.filter(r,i)),this.length>1&&(R[e]||C.uniqueSort(i),D.test(e)&&i.reverse()),this.pushStack(i)}}));var $=/[^\x20\t\r\n\f]+/g;function F(e){return e}function q(e){throw e}function H(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return C.each(e.match($)||[],(function(e,n){t[n]=!0})),t}(e):C.extend({},e);var t,n,r,i,o=[],s=[],a=-1,u=function(){for(i=i||e.once,r=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){C.each(n,(function(n,r){m(r)?e.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==O(r)&&t(r)}))}(arguments),n&&!t&&u()),this},remove:function(){return C.each(arguments,(function(e,t){for(var n;(n=C.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(e){return e?C.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},C.extend({Deferred:function(e){var t=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return C.Deferred((function(n){C.each(t,(function(t,r){var i=m(e[r[4]])&&e[r[4]];o[r[1]]((function(){var e=i&&i.apply(this,arguments);e&&m(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)}))})),e=null})).promise()},then:function(e,r,i){var o=0;function s(e,t,r,i){return function(){var a=this,u=arguments,l=function(){var n,l;if(!(e<o)){if((n=r.apply(a,u))===t.promise())throw new TypeError("Thenable self-resolution");l=n&&("object"==typeof n||"function"==typeof n)&&n.then,m(l)?i?l.call(n,s(o,t,F,i),s(o,t,q,i)):(o++,l.call(n,s(o,t,F,i),s(o,t,q,i),s(o,t,F,t.notifyWith))):(r!==F&&(a=void 0,u=[n]),(i||t.resolveWith)(a,u))}},c=i?l:function(){try{l()}catch(n){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(n,c.stackTrace),e+1>=o&&(r!==q&&(a=void 0,u=[n]),t.rejectWith(a,u))}};e?c():(C.Deferred.getStackHook&&(c.stackTrace=C.Deferred.getStackHook()),n.setTimeout(c))}}return C.Deferred((function(n){t[0][3].add(s(0,n,m(i)?i:F,n.notifyWith)),t[1][3].add(s(0,n,m(e)?e:F)),t[2][3].add(s(0,n,m(r)?r:q))})).promise()},promise:function(e){return null!=e?C.extend(e,i):i}},o={};return C.each(t,(function(e,n){var s=n[2],a=n[5];i[n[1]]=s.add,a&&s.add((function(){r=a}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),s.add(n[3].fire),o[n[0]]=function(){return o[n[0]+"With"](this===o?void 0:this,arguments),this},o[n[0]+"With"]=s.fireWith})),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=u.call(arguments),o=C.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?u.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(H(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||m(i[n]&&i[n].then)))return o.then();for(;n--;)H(i[n],s(n),o.reject);return o.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(e,t){n.console&&n.console.warn&&e&&B.test(e.name)&&n.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){n.setTimeout((function(){throw e}))};var z=C.Deferred();function W(){s.removeEventListener("DOMContentLoaded",W),n.removeEventListener("load",W),C.ready()}C.fn.ready=function(e){return z.then(e).catch((function(e){C.readyException(e)})),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0,!0!==e&&--C.readyWait>0||z.resolveWith(s,[C]))}}),C.ready.then=z.then,"complete"===s.readyState||"loading"!==s.readyState&&!s.documentElement.doScroll?n.setTimeout(C.ready):(s.addEventListener("DOMContentLoaded",W),n.addEventListener("load",W));var U=function(e,t,n,r,i,o,s){var a=0,u=e.length,l=null==n;if("object"===O(n))for(a in i=!0,n)U(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,m(r)||(s=!0),l&&(s?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(C(e),n)})),t))for(;a<u;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:l?t.call(e):u?t(e[0],n):o},V=/^-ms-/,G=/-([a-z])/g;function K(e,t){return t.toUpperCase()}function X(e){return e.replace(V,"ms-").replace(G,K)}var Q=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Y(){this.expando=C.expando+Y.uid++}Y.uid=1,Y.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Q(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[X(t)]=n;else for(r in t)i[X(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(X):(t=X(t))in r?[t]:t.match($)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||C.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!C.isEmptyObject(t)}};var J=new Y,Z=new Y,ee=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,te=/[A-Z]/g;function ne(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(te,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ee.test(e)?JSON.parse(e):e)}(n)}catch(e){}Z.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return Z.hasData(e)||J.hasData(e)},data:function(e,t,n){return Z.access(e,t,n)},removeData:function(e,t){Z.remove(e,t)},_data:function(e,t,n){return J.access(e,t,n)},_removeData:function(e,t){J.remove(e,t)}}),C.fn.extend({data:function(e,t){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(i=Z.get(o),1===o.nodeType&&!J.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=X(r.slice(5)),ne(o,r,i[r]));J.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each((function(){Z.set(this,e)})):U(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=Z.get(o,e))?n:void 0!==(n=ne(o,e))?n:void 0;this.each((function(){Z.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){Z.remove(this,e)}))}}),C.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=J.get(e,t),n&&(!r||Array.isArray(n)?r=J.access(e,t,C.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),r=n.length,i=n.shift(),o=C._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,(function(){C.dequeue(e,t)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return J.get(e,n)||J.access(e,n,{empty:C.Callbacks("once memory").add((function(){J.remove(e,[t+"queue",n])}))})}}),C.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?C.queue(this[0],e):void 0===t?this:this.each((function(){var n=C.queue(this,e,t);C._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&C.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){C.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=C.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=J.get(o[s],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var re=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ie=new RegExp("^(?:([+-])=|)("+re+")([a-z%]*)$","i"),oe=["Top","Right","Bottom","Left"],se=s.documentElement,ae=function(e){return C.contains(e.ownerDocument,e)},ue={composed:!0};se.getRootNode&&(ae=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(ue)===e.ownerDocument});var le=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ae(e)&&"none"===C.css(e,"display")},ce=function(e,t,n,r){var i,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];for(o in i=n.apply(e,r||[]),t)e.style[o]=s[o];return i};function pe(e,t,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return C.css(e,t,"")},u=a(),l=n&&n[3]||(C.cssNumber[t]?"":"px"),c=e.nodeType&&(C.cssNumber[t]||"px"!==l&&+u)&&ie.exec(C.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;s--;)C.style(e,t,c+l),(1-o)*(1-(o=a()/u||.5))<=0&&(s=0),c/=o;c*=2,C.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var fe={};function he(e){var t,n=e.ownerDocument,r=e.nodeName,i=fe[r];return i||(t=n.body.appendChild(n.createElement(r)),i=C.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),fe[r]=i,i)}function de(e,t){for(var n,r,i=[],o=0,s=e.length;o<s;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=J.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&le(r)&&(i[o]=he(r))):"none"!==n&&(i[o]="none",J.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}C.fn.extend({show:function(){return de(this,!0)},hide:function(){return de(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){le(this)?C(this).show():C(this).hide()}))}});var ge=/^(?:checkbox|radio)$/i,ve=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ye=/^$|^module$|\/(?:java|ecma)script/i,me={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function be(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&A(e,t)?C.merge([e],n):n}function we(e,t){for(var n=0,r=e.length;n<r;n++)J.set(e[n],"globalEval",!t||J.get(t[n],"globalEval"))}me.optgroup=me.option,me.tbody=me.tfoot=me.colgroup=me.caption=me.thead,me.th=me.td;var xe,Oe,Ce=/<|&#?\w+;/;function Te(e,t,n,r,i){for(var o,s,a,u,l,c,p=t.createDocumentFragment(),f=[],h=0,d=e.length;h<d;h++)if((o=e[h])||0===o)if("object"===O(o))C.merge(f,o.nodeType?[o]:o);else if(Ce.test(o)){for(s=s||p.appendChild(t.createElement("div")),a=(ve.exec(o)||["",""])[1].toLowerCase(),u=me[a]||me._default,s.innerHTML=u[1]+C.htmlPrefilter(o)+u[2],c=u[0];c--;)s=s.lastChild;C.merge(f,s.childNodes),(s=p.firstChild).textContent=""}else f.push(t.createTextNode(o));for(p.textContent="",h=0;o=f[h++];)if(r&&C.inArray(o,r)>-1)i&&i.push(o);else if(l=ae(o),s=be(p.appendChild(o),"script"),l&&we(s),n)for(c=0;o=s[c++];)ye.test(o.type||"")&&n.push(o);return p}xe=s.createDocumentFragment().appendChild(s.createElement("div")),(Oe=s.createElement("input")).setAttribute("type","radio"),Oe.setAttribute("checked","checked"),Oe.setAttribute("name","t"),xe.appendChild(Oe),y.checkClone=xe.cloneNode(!0).cloneNode(!0).lastChild.checked,xe.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!xe.cloneNode(!0).lastChild.defaultValue;var Se=/^key/,Ee=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,je=/^([^.]*)(?:\.(.+)|)/;function _e(){return!0}function Ne(){return!1}function Ae(e,t){return e===function(){try{return s.activeElement}catch(e){}}()==("focus"===t)}function ke(e,t,n,r,i,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(r=r||n,n=void 0),t)ke(e,a,n,r,t[a],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Ne;else if(!i)return e;return 1===o&&(s=i,(i=function(e){return C().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=C.guid++)),e.each((function(){C.event.add(this,t,i,r,n)}))}function Ie(e,t,n){n?(J.set(e,t,!1),C.event.add(e,t,{namespace:!1,handler:function(e){var r,i,o=J.get(this,t);if(1&e.isTrigger&&this[t]){if(o.length)(C.event.special[t]||{}).delegateType&&e.stopPropagation();else if(o=u.call(arguments),J.set(this,t,o),r=n(this,t),this[t](),o!==(i=J.get(this,t))||r?J.set(this,t,!1):i={},o!==i)return e.stopImmediatePropagation(),e.preventDefault(),i.value}else o.length&&(J.set(this,t,{value:C.event.trigger(C.extend(o[0],C.Event.prototype),o.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===J.get(e,t)&&C.event.add(e,t,_e)}C.event={global:{},add:function(e,t,n,r,i){var o,s,a,u,l,c,p,f,h,d,g,v=J.get(e);if(v)for(n.handler&&(n=(o=n).handler,i=o.selector),i&&C.find.matchesSelector(se,i),n.guid||(n.guid=C.guid++),(u=v.events)||(u=v.events={}),(s=v.handle)||(s=v.handle=function(t){return void 0!==C&&C.event.triggered!==t.type?C.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match($)||[""]).length;l--;)h=g=(a=je.exec(t[l])||[])[1],d=(a[2]||"").split(".").sort(),h&&(p=C.event.special[h]||{},h=(i?p.delegateType:p.bindType)||h,p=C.event.special[h]||{},c=C.extend({type:h,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&C.expr.match.needsContext.test(i),namespace:d.join(".")},o),(f=u[h])||((f=u[h]=[]).delegateCount=0,p.setup&&!1!==p.setup.call(e,r,d,s)||e.addEventListener&&e.addEventListener(h,s)),p.add&&(p.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,c):f.push(c),C.event.global[h]=!0)},remove:function(e,t,n,r,i){var o,s,a,u,l,c,p,f,h,d,g,v=J.hasData(e)&&J.get(e);if(v&&(u=v.events)){for(l=(t=(t||"").match($)||[""]).length;l--;)if(h=g=(a=je.exec(t[l])||[])[1],d=(a[2]||"").split(".").sort(),h){for(p=C.event.special[h]||{},f=u[h=(r?p.delegateType:p.bindType)||h]||[],a=a[2]&&new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)c=f[o],!i&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,p.remove&&p.remove.call(e,c));s&&!f.length&&(p.teardown&&!1!==p.teardown.call(e,d,v.handle)||C.removeEvent(e,h,v.handle),delete u[h])}else for(h in u)C.event.remove(e,h+t[l],n,r,!0);C.isEmptyObject(u)&&J.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,s,a=C.event.fix(e),u=new Array(arguments.length),l=(J.get(this,"events")||{})[a.type]||[],c=C.event.special[a.type]||{};for(u[0]=a,t=1;t<arguments.length;t++)u[t]=arguments[t];if(a.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,a)){for(s=C.event.handlers.call(this,a,l),t=0;(i=s[t++])&&!a.isPropagationStopped();)for(a.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==o.namespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,void 0!==(r=((C.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,u))&&!1===(a.result=r)&&(a.preventDefault(),a.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,a),a.result}},handlers:function(e,t){var n,r,i,o,s,a=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],s={},n=0;n<u;n++)void 0===s[i=(r=t[n]).selector+" "]&&(s[i]=r.needsContext?C(i,this).index(l)>-1:C.find(i,this,null,[l]).length),s[i]&&o.push(r);o.length&&a.push({elem:l,handlers:o})}return l=this,u<t.length&&a.push({elem:l,handlers:t.slice(u)}),a},addProp:function(e,t){Object.defineProperty(C.Event.prototype,e,{enumerable:!0,configurable:!0,get:m(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return ge.test(t.type)&&t.click&&A(t,"input")&&Ie(t,"click",_e),!1},trigger:function(e){var t=this||e;return ge.test(t.type)&&t.click&&A(t,"input")&&Ie(t,"click"),!0},_default:function(e){var t=e.target;return ge.test(t.type)&&t.click&&A(t,"input")&&J.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?_e:Ne,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:Ne,isPropagationStopped:Ne,isImmediatePropagationStopped:Ne,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=_e,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=_e,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=_e,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&Se.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&Ee.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},(function(e,t){C.event.special[e]={setup:function(){return Ie(this,e,Ae),!1},trigger:function(){return Ie(this,e),!0},delegateType:t}})),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){C.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||C.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),C.fn.extend({on:function(e,t,n,r){return ke(this,e,t,n,r)},one:function(e,t,n,r){return ke(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,C(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ne),this.each((function(){C.event.remove(this,e,n,t)}))}});var Pe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Me=/<script|<style|<link/i,De=/checked\s*(?:[^=]|=\s*.checked.)/i,Re=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Le(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function $e(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Fe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function qe(e,t){var n,r,i,o,s,a,u,l;if(1===t.nodeType){if(J.hasData(e)&&(o=J.access(e),s=J.set(t,o),l=o.events))for(i in delete s.handle,s.events={},l)for(n=0,r=l[i].length;n<r;n++)C.event.add(t,i,l[i][n]);Z.hasData(e)&&(a=Z.access(e),u=C.extend({},a),Z.set(t,u))}}function He(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ge.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Be(e,t,n,r){t=l.apply([],t);var i,o,s,a,u,c,p=0,f=e.length,h=f-1,d=t[0],g=m(d);if(g||f>1&&"string"==typeof d&&!y.checkClone&&De.test(d))return e.each((function(i){var o=e.eq(i);g&&(t[0]=d.call(this,i,o.html())),Be(o,t,n,r)}));if(f&&(o=(i=Te(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=C.map(be(i,"script"),$e)).length;p<f;p++)u=i,p!==h&&(u=C.clone(u,!0,!0),a&&C.merge(s,be(u,"script"))),n.call(e[p],u,p);if(a)for(c=s[s.length-1].ownerDocument,C.map(s,Fe),p=0;p<a;p++)u=s[p],ye.test(u.type||"")&&!J.access(u,"globalEval")&&C.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?C._evalUrl&&!u.noModule&&C._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")}):x(u.textContent.replace(Re,""),u,c))}return e}function ze(e,t,n){for(var r,i=t?C.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||C.cleanData(be(r)),r.parentNode&&(n&&ae(r)&&we(be(r,"script")),r.parentNode.removeChild(r));return e}C.extend({htmlPrefilter:function(e){return e.replace(Pe,"<$1></$2>")},clone:function(e,t,n){var r,i,o,s,a=e.cloneNode(!0),u=ae(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(s=be(a),r=0,i=(o=be(e)).length;r<i;r++)He(o[r],s[r]);if(t)if(n)for(o=o||be(e),s=s||be(a),r=0,i=o.length;r<i;r++)qe(o[r],s[r]);else qe(e,a);return(s=be(a,"script")).length>0&&we(s,!u&&be(e,"script")),a},cleanData:function(e){for(var t,n,r,i=C.event.special,o=0;void 0!==(n=e[o]);o++)if(Q(n)){if(t=n[J.expando]){if(t.events)for(r in t.events)i[r]?C.event.remove(n,r):C.removeEvent(n,r,t.handle);n[J.expando]=void 0}n[Z.expando]&&(n[Z.expando]=void 0)}}}),C.fn.extend({detach:function(e){return ze(this,e,!0)},remove:function(e){return ze(this,e)},text:function(e){return U(this,(function(e){return void 0===e?C.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return Be(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Le(this,e).appendChild(e)}))},prepend:function(){return Be(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Le(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Be(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Be(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(be(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return C.clone(this,e,t)}))},html:function(e){return U(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Me.test(e)&&!me[(ve.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(be(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Be(this,arguments,(function(t){var n=this.parentNode;C.inArray(this,e)<0&&(C.cleanData(be(this)),n&&n.replaceChild(t,this))}),e)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){C.fn[e]=function(e){for(var n,r=[],i=C(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),C(i[s])[t](n),c.apply(r,n.get());return this.pushStack(r)}}));var We=new RegExp("^("+re+")(?!px)[a-z%]+$","i"),Ue=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=n),t.getComputedStyle(e)},Ve=new RegExp(oe.join("|"),"i");function Ge(e,t,n){var r,i,o,s,a=e.style;return(n=n||Ue(e))&&(""!==(s=n.getPropertyValue(t)||n[t])||ae(e)||(s=C.style(e,t)),!y.pixelBoxStyles()&&We.test(s)&&Ve.test(t)&&(r=a.width,i=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=r,a.minWidth=i,a.maxWidth=o)),void 0!==s?s+"":s}function Ke(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",se.appendChild(l).appendChild(c);var e=n.getComputedStyle(c);r="1%"!==e.top,u=12===t(e.marginLeft),c.style.right="60%",a=36===t(e.right),i=36===t(e.width),c.style.position="absolute",o=12===t(c.offsetWidth/3),se.removeChild(l),c=null}}function t(e){return Math.round(parseFloat(e))}var r,i,o,a,u,l=s.createElement("div"),c=s.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===c.style.backgroundClip,C.extend(y,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),r},reliableMarginLeft:function(){return e(),u},scrollboxSize:function(){return e(),o}}))}();var Xe=["Webkit","Moz","ms"],Qe=s.createElement("div").style,Ye={};function Je(e){var t=C.cssProps[e]||Ye[e];return t||(e in Qe?e:Ye[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Xe.length;n--;)if((e=Xe[n]+t)in Qe)return e}(e)||e)}var Ze=/^(none|table(?!-c[ea]).+)/,et=/^--/,tt={position:"absolute",visibility:"hidden",display:"block"},nt={letterSpacing:"0",fontWeight:"400"};function rt(e,t,n){var r=ie.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function it(e,t,n,r,i,o){var s="width"===t?1:0,a=0,u=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(u+=C.css(e,n+oe[s],!0,i)),r?("content"===n&&(u-=C.css(e,"padding"+oe[s],!0,i)),"margin"!==n&&(u-=C.css(e,"border"+oe[s]+"Width",!0,i))):(u+=C.css(e,"padding"+oe[s],!0,i),"padding"!==n?u+=C.css(e,"border"+oe[s]+"Width",!0,i):a+=C.css(e,"border"+oe[s]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-a-.5))||0),u}function ot(e,t,n){var r=Ue(e),i=(!y.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,r),o=i,s=Ge(e,t,r),a="offset"+t[0].toUpperCase()+t.slice(1);if(We.test(s)){if(!n)return s;s="auto"}return(!y.boxSizingReliable()&&i||"auto"===s||!parseFloat(s)&&"inline"===C.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===C.css(e,"boxSizing",!1,r),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+it(e,t,n||(i?"border":"content"),o,r,s)+"px"}function st(e,t,n,r,i){return new st.prototype.init(e,t,n,r,i)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ge(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,s,a=X(t),u=et.test(t),l=e.style;if(u||(t=Je(a)),s=C.cssHooks[t]||C.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=ie.exec(n))&&i[1]&&(n=pe(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(C.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,s,a=X(t);return et.test(t)||(t=Je(a)),(s=C.cssHooks[t]||C.cssHooks[a])&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=Ge(e,t,r)),"normal"===i&&t in nt&&(i=nt[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),C.each(["height","width"],(function(e,t){C.cssHooks[t]={get:function(e,n,r){if(n)return!Ze.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ot(e,t,r):ce(e,tt,(function(){return ot(e,t,r)}))},set:function(e,n,r){var i,o=Ue(e),s=!y.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===C.css(e,"boxSizing",!1,o),u=r?it(e,t,r,a,o):0;return a&&s&&(u-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-it(e,t,"border",!1,o)-.5)),u&&(i=ie.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=C.css(e,t)),rt(0,n,u)}}})),C.cssHooks.marginLeft=Ke(y.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Ge(e,"marginLeft"))||e.getBoundingClientRect().left-ce(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),C.each({margin:"",padding:"",border:"Width"},(function(e,t){C.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+oe[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(C.cssHooks[e+t].set=rt)})),C.fn.extend({css:function(e,t){return U(this,(function(e,t,n){var r,i,o={},s=0;if(Array.isArray(t)){for(r=Ue(e),i=t.length;s<i;s++)o[t[s]]=C.css(e,t[s],!1,r);return o}return void 0!==n?C.style(e,t,n):C.css(e,t)}),e,t,arguments.length>1)}}),C.Tween=st,st.prototype={constructor:st,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var e=st.propHooks[this.prop];return e&&e.get?e.get(this):st.propHooks._default.get(this)},run:function(e){var t,n=st.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):st.propHooks._default.set(this),this}},st.prototype.init.prototype=st.prototype,st.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=C.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[Je(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}},st.propHooks.scrollTop=st.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=st.prototype.init,C.fx.step={};var at,ut,lt=/^(?:toggle|show|hide)$/,ct=/queueHooks$/;function pt(){ut&&(!1===s.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(pt):n.setTimeout(pt,C.fx.interval),C.fx.tick())}function ft(){return n.setTimeout((function(){at=void 0})),at=Date.now()}function ht(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=oe[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function dt(e,t,n){for(var r,i=(gt.tweeners[t]||[]).concat(gt.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function gt(e,t,n){var r,i,o=0,s=gt.prefilters.length,a=C.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var t=at||ft(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),o=0,s=l.tweens.length;o<s;o++)l.tweens[o].run(r);return a.notifyWith(e,[l,r,n]),r<1&&s?n:(s||a.notifyWith(e,[l,1,0]),a.resolveWith(e,[l]),!1)},l=a.promise({elem:e,props:C.extend({},t),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},n),originalProperties:t,originalOptions:n,startTime:at||ft(),duration:n.duration,tweens:[],createTween:function(t,n){var r=C.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(a.notifyWith(e,[l,1,0]),a.resolveWith(e,[l,t])):a.rejectWith(e,[l,t]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,s;for(n in e)if(i=t[r=X(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(s=C.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);o<s;o++)if(r=gt.prefilters[o].call(l,e,c,l.opts))return m(r.stop)&&(C._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return C.map(c,dt,l),m(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),C.fx.timer(C.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}C.Animation=C.extend(gt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return pe(n.elem,e,ie.exec(t),n),n}]},tweener:function(e,t){m(e)?(t=e,e=["*"]):e=e.match($);for(var n,r=0,i=e.length;r<i;r++)n=e[r],gt.tweeners[n]=gt.tweeners[n]||[],gt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,s,a,u,l,c,p="width"in t||"height"in t,f=this,h={},d=e.style,g=e.nodeType&&le(e),v=J.get(e,"fxshow");for(r in n.queue||(null==(s=C._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always((function(){f.always((function(){s.unqueued--,C.queue(e,"fx").length||s.empty.fire()}))}))),t)if(i=t[r],lt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}h[r]=v&&v[r]||C.style(e,r)}if((u=!C.isEmptyObject(t))||!C.isEmptyObject(h))for(r in p&&1===e.nodeType&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],null==(l=v&&v.display)&&(l=J.get(e,"display")),"none"===(c=C.css(e,"display"))&&(l?c=l:(de([e],!0),l=e.style.display||l,c=C.css(e,"display"),de([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===C.css(e,"float")&&(u||(f.done((function(){d.display=l})),null==l&&(c=d.display,l="none"===c?"":c)),d.display="inline-block")),n.overflow&&(d.overflow="hidden",f.always((function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]}))),u=!1,h)u||(v?"hidden"in v&&(g=v.hidden):v=J.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&de([e],!0),f.done((function(){for(r in g||de([e]),J.remove(e,"fxshow"),h)C.style(e,r,h[r])}))),u=dt(g?v[r]:0,r,f),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?gt.prefilters.unshift(e):gt.prefilters.push(e)}}),C.speed=function(e,t,n){var r=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return C.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in C.fx.speeds?r.duration=C.fx.speeds[r.duration]:r.duration=C.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&C.dequeue(this,r.queue)},r},C.fn.extend({fadeTo:function(e,t,n,r){return this.filter(le).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=C.isEmptyObject(e),o=C.speed(t,n,r),s=function(){var t=gt(this,C.extend({},e),o);(i||J.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=C.timers,s=J.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&ct.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||C.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=J.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=C.timers,s=r?r.length:0;for(n.finish=!0,C.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),C.each(["toggle","show","hide"],(function(e,t){var n=C.fn[t];C.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(ht(t,!0),e,r,i)}})),C.each({slideDown:ht("show"),slideUp:ht("hide"),slideToggle:ht("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){C.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(at=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),at=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){ut||(ut=!0,pt())},C.fx.stop=function(){ut=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(e,t){return e=C.fx&&C.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,r){var i=n.setTimeout(t,e);r.stop=function(){n.clearTimeout(i)}}))},function(){var e=s.createElement("input"),t=s.createElement("select").appendChild(s.createElement("option"));e.type="checkbox",y.checkOn=""!==e.value,y.optSelected=t.selected,(e=s.createElement("input")).value="t",e.type="radio",y.radioValue="t"===e.value}();var vt,yt=C.expr.attrHandle;C.fn.extend({attr:function(e,t){return U(this,C.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){C.removeAttr(this,e)}))}}),C.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?C.prop(e,t,n):(1===o&&C.isXMLDoc(e)||(i=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?vt:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=C.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match($);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),vt={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=yt[t]||C.find.attr;yt[t]=function(e,t,r){var i,o,s=t.toLowerCase();return r||(o=yt[s],yt[s]=i,i=null!=n(e,t,r)?s:null,yt[s]=o),i}}));var mt=/^(?:input|select|textarea|button)$/i,bt=/^(?:a|area)$/i;function wt(e){return(e.match($)||[]).join(" ")}function xt(e){return e.getAttribute&&e.getAttribute("class")||""}function Ot(e){return Array.isArray(e)?e:"string"==typeof e&&e.match($)||[]}C.fn.extend({prop:function(e,t){return U(this,C.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[C.propFix[e]||e]}))}}),C.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(e)||(t=C.propFix[t]||t,i=C.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):mt.test(e.nodeName)||bt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(C.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){C.propFix[this.toLowerCase()]=this})),C.fn.extend({addClass:function(e){var t,n,r,i,o,s,a,u=0;if(m(e))return this.each((function(t){C(this).addClass(e.call(this,t,xt(this)))}));if((t=Ot(e)).length)for(;n=this[u++];)if(i=xt(n),r=1===n.nodeType&&" "+wt(i)+" "){for(s=0;o=t[s++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");i!==(a=wt(r))&&n.setAttribute("class",a)}return this},removeClass:function(e){var t,n,r,i,o,s,a,u=0;if(m(e))return this.each((function(t){C(this).removeClass(e.call(this,t,xt(this)))}));if(!arguments.length)return this.attr("class","");if((t=Ot(e)).length)for(;n=this[u++];)if(i=xt(n),r=1===n.nodeType&&" "+wt(i)+" "){for(s=0;o=t[s++];)for(;r.indexOf(" "+o+" ")>-1;)r=r.replace(" "+o+" "," ");i!==(a=wt(r))&&n.setAttribute("class",a)}return this},toggleClass:function(e,t){var n=typeof e,r="string"===n||Array.isArray(e);return"boolean"==typeof t&&r?t?this.addClass(e):this.removeClass(e):m(e)?this.each((function(n){C(this).toggleClass(e.call(this,n,xt(this),t),t)})):this.each((function(){var t,i,o,s;if(r)for(i=0,o=C(this),s=Ot(e);t=s[i++];)o.hasClass(t)?o.removeClass(t):o.addClass(t);else void 0!==e&&"boolean"!==n||((t=xt(this))&&J.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":J.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+wt(xt(n))+" ").indexOf(t)>-1)return!0;return!1}});var Ct=/\r/g;C.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=m(e),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,C(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=C.map(i,(function(e){return null==e?"":e+""}))),(t=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=C.valHooks[i.type]||C.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(Ct,""):null==n?"":n:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:wt(C.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],u=s?o+1:i.length;for(r=o<0?u:s?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=C(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=C.makeArray(t),s=i.length;s--;)((r=i[s]).selected=C.inArray(C.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],(function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=C.inArray(C(e).val(),t)>-1}},y.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})})),y.focusin="onfocusin"in n;var Tt=/^(?:focusinfocus|focusoutblur)$/,St=function(e){e.stopPropagation()};C.extend(C.event,{trigger:function(e,t,r,i){var o,a,u,l,c,p,f,h,g=[r||s],v=d.call(e,"type")?e.type:e,y=d.call(e,"namespace")?e.namespace.split("."):[];if(a=h=u=r=r||s,3!==r.nodeType&&8!==r.nodeType&&!Tt.test(v+C.event.triggered)&&(v.indexOf(".")>-1&&(y=v.split("."),v=y.shift(),y.sort()),c=v.indexOf(":")<0&&"on"+v,(e=e[C.expando]?e:new C.Event(v,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=y.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+y.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),t=null==t?[e]:C.makeArray(t,[e]),f=C.event.special[v]||{},i||!f.trigger||!1!==f.trigger.apply(r,t))){if(!i&&!f.noBubble&&!b(r)){for(l=f.delegateType||v,Tt.test(l+v)||(a=a.parentNode);a;a=a.parentNode)g.push(a),u=a;u===(r.ownerDocument||s)&&g.push(u.defaultView||u.parentWindow||n)}for(o=0;(a=g[o++])&&!e.isPropagationStopped();)h=a,e.type=o>1?l:f.bindType||v,(p=(J.get(a,"events")||{})[e.type]&&J.get(a,"handle"))&&p.apply(a,t),(p=c&&a[c])&&p.apply&&Q(a)&&(e.result=p.apply(a,t),!1===e.result&&e.preventDefault());return e.type=v,i||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(g.pop(),t)||!Q(r)||c&&m(r[v])&&!b(r)&&((u=r[c])&&(r[c]=null),C.event.triggered=v,e.isPropagationStopped()&&h.addEventListener(v,St),r[v](),e.isPropagationStopped()&&h.removeEventListener(v,St),C.event.triggered=void 0,u&&(r[c]=u)),e.result}},simulate:function(e,t,n){var r=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(r,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each((function(){C.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}}),y.focusin||C.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){C.event.simulate(t,e.target,C.event.fix(e))};C.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=J.access(r,t);i||r.addEventListener(e,n,!0),J.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=J.access(r,t)-1;i?J.access(r,t,i):(r.removeEventListener(e,n,!0),J.remove(r,t))}}}));var Et=n.location,jt=Date.now(),_t=/\?/;C.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new n.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||C.error("Invalid XML: "+e),t};var Nt=/\[\]$/,At=/\r?\n/g,kt=/^(?:submit|button|image|reset|file)$/i,It=/^(?:input|select|textarea|keygen)/i;function Pt(e,t,n,r){var i;if(Array.isArray(t))C.each(t,(function(t,i){n||Nt.test(e)?r(e,i):Pt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==O(t))r(e,t);else for(i in t)Pt(e+"["+i+"]",t[i],n,r)}C.param=function(e,t){var n,r=[],i=function(e,t){var n=m(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,(function(){i(this.name,this.value)}));else for(n in e)Pt(n,e[n],t,i);return r.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&It.test(this.nodeName)&&!kt.test(e)&&(this.checked||!ge.test(e))})).map((function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,(function(e){return{name:t.name,value:e.replace(At,"\r\n")}})):{name:t.name,value:n.replace(At,"\r\n")}})).get()}});var Mt=/%20/g,Dt=/#.*$/,Rt=/([?&])_=[^&]*/,Lt=/^(.*?):[ \t]*([^\r\n]*)$/gm,$t=/^(?:GET|HEAD)$/,Ft=/^\/\//,qt={},Ht={},Bt="*/".concat("*"),zt=s.createElement("a");function Wt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match($)||[];if(m(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Ut(e,t,n,r){var i={},o=e===Ht;function s(a){var u;return i[a]=!0,C.each(e[a]||[],(function(e,a){var l=a(t,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),s(l),!1)})),u}return s(t.dataTypes[0])||!i["*"]&&s("*")}function Vt(e,t){var n,r,i=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&C.extend(!0,e,r),e}zt.href=Et.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Et.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Et.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Bt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Vt(Vt(e,C.ajaxSettings),t):Vt(C.ajaxSettings,e)},ajaxPrefilter:Wt(qt),ajaxTransport:Wt(Ht),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var r,i,o,a,u,l,c,p,f,h,d=C.ajaxSetup({},t),g=d.context||d,v=d.context&&(g.nodeType||g.jquery)?C(g):C.event,y=C.Deferred(),m=C.Callbacks("once memory"),b=d.statusCode||{},w={},x={},O="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(c){if(!a)for(a={};t=Lt.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(e,t){return null==c&&(e=x[e.toLowerCase()]=x[e.toLowerCase()]||e,w[e]=t),this},overrideMimeType:function(e){return null==c&&(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)T.always(e[T.status]);else for(t in e)b[t]=[b[t],e[t]];return this},abort:function(e){var t=e||O;return r&&r.abort(t),S(0,t),this}};if(y.promise(T),d.url=((e||d.url||Et.href)+"").replace(Ft,Et.protocol+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match($)||[""],null==d.crossDomain){l=s.createElement("a");try{l.href=d.url,l.href=l.href,d.crossDomain=zt.protocol+"//"+zt.host!=l.protocol+"//"+l.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=C.param(d.data,d.traditional)),Ut(qt,d,t,T),c)return T;for(f in(p=C.event&&d.global)&&0==C.active++&&C.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!$t.test(d.type),i=d.url.replace(Dt,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Mt,"+")):(h=d.url.slice(i.length),d.data&&(d.processData||"string"==typeof d.data)&&(i+=(_t.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(Rt,"$1"),h=(_t.test(i)?"&":"?")+"_="+jt+++h),d.url=i+h),d.ifModified&&(C.lastModified[i]&&T.setRequestHeader("If-Modified-Since",C.lastModified[i]),C.etag[i]&&T.setRequestHeader("If-None-Match",C.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&T.setRequestHeader("Content-Type",d.contentType),T.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Bt+"; q=0.01":""):d.accepts["*"]),d.headers)T.setRequestHeader(f,d.headers[f]);if(d.beforeSend&&(!1===d.beforeSend.call(g,T,d)||c))return T.abort();if(O="abort",m.add(d.complete),T.done(d.success),T.fail(d.error),r=Ut(Ht,d,t,T)){if(T.readyState=1,p&&v.trigger("ajaxSend",[T,d]),c)return T;d.async&&d.timeout>0&&(u=n.setTimeout((function(){T.abort("timeout")}),d.timeout));try{c=!1,r.send(w,S)}catch(e){if(c)throw e;S(-1,e)}}else S(-1,"No Transport");function S(e,t,s,a){var l,f,h,w,x,O=t;c||(c=!0,u&&n.clearTimeout(u),r=void 0,o=a||"",T.readyState=e>0?4:0,l=e>=200&&e<300||304===e,s&&(w=function(e,t,n){for(var r,i,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}(d,T,s)),w=function(e,t,n,r){var i,o,s,a,u,l={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=l[u+" "+o]||l["* "+o]))for(i in l)if((a=i.split(" "))[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(d,w,T,l),l?(d.ifModified&&((x=T.getResponseHeader("Last-Modified"))&&(C.lastModified[i]=x),(x=T.getResponseHeader("etag"))&&(C.etag[i]=x)),204===e||"HEAD"===d.type?O="nocontent":304===e?O="notmodified":(O=w.state,f=w.data,l=!(h=w.error))):(h=O,!e&&O||(O="error",e<0&&(e=0))),T.status=e,T.statusText=(t||O)+"",l?y.resolveWith(g,[f,O,T]):y.rejectWith(g,[T,O,h]),T.statusCode(b),b=void 0,p&&v.trigger(l?"ajaxSuccess":"ajaxError",[T,d,l?f:h]),m.fireWith(g,[T,O]),p&&(v.trigger("ajaxComplete",[T,d]),--C.active||C.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],(function(e,t){C[t]=function(e,n,r,i){return m(n)&&(i=i||r,r=n,n=void 0),C.ajax(C.extend({url:e,type:t,dataType:i,data:n,success:r},C.isPlainObject(e)&&e))}})),C._evalUrl=function(e,t){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t)}})},C.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return m(e)?this.each((function(t){C(this).wrapInner(e.call(this,t))})):this.each((function(){var t=C(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=m(e);return this.each((function(n){C(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){C(this).replaceWith(this.childNodes)})),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(e){}};var Gt={0:200,1223:204},Kt=C.ajaxSettings.xhr();y.cors=!!Kt&&"withCredentials"in Kt,y.ajax=Kt=!!Kt,C.ajaxTransport((function(e){var t,r;if(y.cors||Kt&&!e.crossDomain)return{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);t=function(e){return function(){t&&(t=r=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Gt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),r=a.onerror=a.ontimeout=t("error"),void 0!==a.onabort?a.onabort=r:a.onreadystatechange=function(){4===a.readyState&&n.setTimeout((function(){t&&r()}))},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}})),C.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),C.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=C("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),s.head.appendChild(t[0])},abort:function(){n&&n()}}}));var Xt,Qt=[],Yt=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Qt.pop()||C.expando+"_"+jt++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",(function(e,t,r){var i,o,s,a=!1!==e.jsonp&&(Yt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Yt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Yt,"$1"+i):!1!==e.jsonp&&(e.url+=(_t.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||C.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=n[i],n[i]=function(){s=arguments},r.always((function(){void 0===o?C(n).removeProp(i):n[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Qt.push(i)),s&&m(o)&&o(s[0]),s=o=void 0})),"script"})),y.createHTMLDocument=((Xt=s.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Xt.childNodes.length),C.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=s.implementation.createHTMLDocument("")).createElement("base")).href=s.location.href,t.head.appendChild(r)):t=s),o=!n&&[],(i=k.exec(e))?[t.createElement(i[1])]:(i=Te([e],t,o),o&&o.length&&C(o).remove(),C.merge([],i.childNodes)));var r,i,o},C.fn.load=function(e,t,n){var r,i,o,s=this,a=e.indexOf(" ");return a>-1&&(r=wt(e.slice(a)),e=e.slice(0,a)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),s.length>0&&C.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){o=arguments,s.html(r?C("<div>").append(C.parseHTML(e)).find(r):e)})).always(n&&function(e,t){s.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){C.fn[t]=function(e){return this.on(t,e)}})),C.expr.pseudos.animated=function(e){return C.grep(C.timers,(function(t){return e===t.elem})).length},C.offset={setOffset:function(e,t,n){var r,i,o,s,a,u,l=C.css(e,"position"),c=C(e),p={};"static"===l&&(e.style.position="relative"),a=c.offset(),o=C.css(e,"top"),u=C.css(e,"left"),("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1?(s=(r=c.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(u)||0),m(t)&&(t=t.call(e,n,C.extend({},a))),null!=t.top&&(p.top=t.top-a.top+s),null!=t.left&&(p.left=t.left-a.left+i),"using"in t?t.using.call(e,p):c.css(p)}},C.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){C.offset.setOffset(this,e,t)}));var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===C.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),i.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-C.css(r,"marginTop",!0),left:t.left-i.left-C.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||se}))}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;C.fn[e]=function(r){return U(this,(function(e,r,i){var o;if(b(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),C.each(["top","left"],(function(e,t){C.cssHooks[t]=Ke(y.pixelPosition,(function(e,n){if(n)return n=Ge(e,t),We.test(n)?C(e).position()[t]+"px":n}))})),C.each({Height:"height",Width:"width"},(function(e,t){C.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){C.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return U(this,(function(t,n,i){var o;return b(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?C.css(t,n,a):C.style(t,n,i,a)}),t,s?i:void 0,s)}}))})),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){C.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),C.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),C.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),m(e))return r=u.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(u.call(arguments)))}).guid=e.guid=e.guid||C.guid++,i},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=A,C.isFunction=m,C.isWindow=b,C.camelCase=X,C.type=O,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},void 0===(r=function(){return C}.apply(t,[]))||(e.exports=r);var Jt=n.jQuery,Zt=n.$;return C.noConflict=function(e){return n.$===C&&(n.$=Zt),e&&n.jQuery===C&&(n.jQuery=Jt),C},i||(n.jQuery=n.$=C),C}))},function(e,t,n){
/*!
 * clipboard.js v2.0.4
 * https://zenorocha.github.io/clipboard.js
 * 
 * Licensed MIT © Zeno Rocha
 */
var r;r=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=u(n(1)),s=u(n(3)),a=u(n(4));function u(e){return e&&e.__esModule?e:{default:e}}var l=function(e){function t(e,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.resolveOptions(n),r.listenClick(e),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),i(t,[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===r(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=(0,a.default)(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new o.default({action:this.action(t),target:this.target(t),text:this.text(t),container:this.container,trigger:t,emitter:this})}},{key:"defaultAction",value:function(e){return c("action",e)}},{key:"defaultTarget",value:function(e){var t=c("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return c("text",e)}},{key:"destroy",value:function(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)}}],[{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,n=!!document.queryCommandSupported;return t.forEach((function(e){n=n&&!!document.queryCommandSupported(e)})),n}}]),t}(s.default);function c(e,t){var n="data-clipboard-"+e;if(t.hasAttribute(n))return t.getAttribute(n)}e.exports=l},function(e,t,n){"use strict";var r,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(2),a=(r=s)&&r.__esModule?r:{default:r},u=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.resolveOptions(t),this.initSelection()}return o(e,[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action=e.action,this.container=e.container,this.emitter=e.emitter,this.target=e.target,this.text=e.text,this.trigger=e.trigger,this.selectedText=""}},{key:"initSelection",value:function(){this.text?this.selectFake():this.target&&this.selectTarget()}},{key:"selectFake",value:function(){var e=this,t="rtl"==document.documentElement.getAttribute("dir");this.removeFake(),this.fakeHandlerCallback=function(){return e.removeFake()},this.fakeHandler=this.container.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[t?"right":"left"]="-9999px";var n=window.pageYOffset||document.documentElement.scrollTop;this.fakeElem.style.top=n+"px",this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,this.container.appendChild(this.fakeElem),this.selectedText=(0,a.default)(this.fakeElem),this.copyText()}},{key:"removeFake",value:function(){this.fakeHandler&&(this.container.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(this.container.removeChild(this.fakeElem),this.fakeElem=null)}},{key:"selectTarget",value:function(){this.selectedText=(0,a.default)(this.target),this.copyText()}},{key:"copyText",value:function(){var e=void 0;try{e=document.execCommand(this.action)}catch(t){e=!1}this.handleResult(e)}},{key:"handleResult",value:function(e){this.emitter.emit(e?"success":"error",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})}},{key:"clearSelection",value:function(){this.trigger&&this.trigger.focus(),window.getSelection().removeAllRanges()}},{key:"destroy",value:function(){this.removeFake()}},{key:"action",set:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"copy";if(this._action=e,"copy"!==this._action&&"cut"!==this._action)throw new Error('Invalid "action" value, use either "copy" or "cut"')},get:function(){return this._action}},{key:"target",set:function(e){if(void 0!==e){if(!e||"object"!==(void 0===e?"undefined":i(e))||1!==e.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===this.action&&e.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===this.action&&(e.hasAttribute("readonly")||e.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes');this._target=e}},get:function(){return this._target}}]),e}();e.exports=u},function(e,t){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var n=e.hasAttribute("readonly");n||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),n||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),i=document.createRange();i.selectNodeContents(e),r.removeAllRanges(),r.addRange(i),t=r.toString()}return t}},function(e,t){function n(){}n.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function i(){r.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,i=n.length;r<i;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],i=[];if(r&&t)for(var o=0,s=r.length;o<s;o++)r[o].fn!==t&&r[o].fn._!==t&&i.push(r[o]);return i.length?n[e]=i:delete n[e],this}},e.exports=n},function(e,t,n){var r=n(5),i=n(6);e.exports=function(e,t,n){if(!e&&!t&&!n)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(e))return function(e,t,n){return e.addEventListener(t,n),{destroy:function(){e.removeEventListener(t,n)}}}(e,t,n);if(r.nodeList(e))return function(e,t,n){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,n)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,n)}))}}}(e,t,n);if(r.string(e))return function(e,t,n){return i(document.body,e,t,n)}(e,t,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var n=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},function(e,t,n){var r=n(7);function i(e,t,n,r,i){var s=o.apply(this,arguments);return e.addEventListener(n,s,i),{destroy:function(){e.removeEventListener(n,s,i)}}}function o(e,t,n,i){return function(n){n.delegateTarget=r(n.target,t),n.delegateTarget&&i.call(e,n)}}e.exports=function(e,t,n,r,o){return"function"==typeof e.addEventListener?i.apply(null,arguments):"function"==typeof n?i.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return i(e,t,n,r,o)})))}},function(e,t){var n=9;if("undefined"!=typeof Element&&!Element.prototype.matches){var r=Element.prototype;r.matches=r.matchesSelector||r.mozMatchesSelector||r.msMatchesSelector||r.oMatchesSelector||r.webkitMatchesSelector}e.exports=function(e,t){for(;e&&e.nodeType!==n;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}}])},e.exports=r()},function(e,t){e.exports=function(e){var t={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:"[A-Za-z0-9\\._:-]+",relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/},{begin:/'/,end:/'/},{begin:/[^\s"'=<>`]+/}]}]}]};return{aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,contains:[{className:"meta",begin:"<!DOCTYPE",end:">",relevance:10,contains:[{begin:"\\[",end:"\\]"}]},e.COMMENT("\x3c!--","--\x3e",{relevance:10}),{begin:"<\\!\\[CDATA\\[",end:"\\]\\]>",relevance:10},{className:"meta",begin:/<\?xml/,end:/\?>/,relevance:10},{begin:/<\?(php)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},e.inherit(e.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),e.inherit(e.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]},{className:"tag",begin:"<style(?=\\s|>)",end:">",keywords:{name:"style"},contains:[t],starts:{end:"</style>",returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:"<script(?=\\s|>)",end:">",keywords:{name:"script"},contains:[t],starts:{end:"<\/script>",returnEnd:!0,subLanguage:["actionscript","javascript","handlebars","xml"]}},{className:"tag",begin:"</?",end:"/?>",contains:[{className:"name",begin:/[^\/><\s]+/,relevance:0},t]}]}}},function(e,t){e.exports=function(e){var t={begin:"\\$+[a-zA-Z_-ÿ][a-zA-Z0-9_-ÿ]*"},n={className:"meta",begin:/<\?(php)?|\?>/},r={className:"string",contains:[e.BACKSLASH_ESCAPE,n],variants:[{begin:'b"',end:'"'},{begin:"b'",end:"'"},e.inherit(e.APOS_STRING_MODE,{illegal:null}),e.inherit(e.QUOTE_STRING_MODE,{illegal:null})]},i={variants:[e.BINARY_NUMBER_MODE,e.C_NUMBER_MODE]};return{aliases:["php","php3","php4","php5","php6","php7"],case_insensitive:!0,keywords:"and include_once list abstract global private echo interface as static endswitch array null if endwhile or const for endforeach self var while isset public protected exit foreach throw elseif include __FILE__ empty require_once do xor return parent clone use __CLASS__ __LINE__ else break print eval new catch __METHOD__ case exception default die require __FUNCTION__ enddeclare final try switch continue endfor endif declare unset true false trait goto instanceof insteadof __DIR__ __NAMESPACE__ yield finally",contains:[e.HASH_COMMENT_MODE,e.COMMENT("//","$",{contains:[n]}),e.COMMENT("/\\*","\\*/",{contains:[{className:"doctag",begin:"@[A-Za-z]+"}]}),e.COMMENT("__halt_compiler.+?;",!1,{endsWithParent:!0,keywords:"__halt_compiler",lexemes:e.UNDERSCORE_IDENT_RE}),{className:"string",begin:/<<<['"]?\w+['"]?$/,end:/^\w+;?$/,contains:[e.BACKSLASH_ESCAPE,{className:"subst",variants:[{begin:/\$\w+/},{begin:/\{\$/,end:/\}/}]}]},n,{className:"keyword",begin:/\$this\b/},t,{begin:/(::|->)+[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/},{className:"function",beginKeywords:"function",end:/[;{]/,excludeEnd:!0,illegal:"\\$|\\[|%",contains:[e.UNDERSCORE_TITLE_MODE,{className:"params",begin:"\\(",end:"\\)",contains:["self",t,e.C_BLOCK_COMMENT_MODE,r,i]}]},{className:"class",beginKeywords:"class interface",end:"{",excludeEnd:!0,illegal:/[:\(\$"]/,contains:[{beginKeywords:"extends implements"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"namespace",end:";",illegal:/[\.']/,contains:[e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"use",end:";",contains:[e.UNDERSCORE_TITLE_MODE]},{begin:"=>"},r,i]}}},function(e,t){e.exports=function(e){var t={literal:"true false null"},n=[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],r=[e.QUOTE_STRING_MODE,e.C_NUMBER_MODE],i={end:",",endsWithParent:!0,excludeEnd:!0,contains:r,keywords:t},o={begin:"{",end:"}",contains:[{className:"attr",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE],illegal:"\\n"},e.inherit(i,{begin:/:/})].concat(n),illegal:"\\S"},s={begin:"\\[",end:"\\]",contains:[e.inherit(i)],illegal:"\\S"};return r.push(o,s),n.forEach((function(e){r.push(e)})),{contains:r,keywords:t,illegal:"\\S"}}},,function(e,t,n){"use strict";n.r(t),function(e){n(11);var t=n(5),r=n.n(t),i=(n(13),n(2)),o=n.n(i),s=n(6),a=n.n(s),u=n(7),l=n.n(u),c=n(8),p=n.n(c);e(document).ready((function(){let t=e.map(e("#language-selector option"),(function(e){return e.text.toLowerCase()}));e("#language-selector").selectize({onChange:function(){-1!==e.inArray(e("#language-selector-selectized").prev().text().toLowerCase(),t)&&e("#language-form").submit()}}),e("#menuLink").click((function(t){t.preventDefault(),e("#layout").toggleClass("active"),e("#foot").toggleClass("active"),e(this).toggleClass("active")})),e(".expandable > .expander").on("click",(function(t){t.preventDefault();let n=e(t.currentTarget);n.parents(".expandable").toggleClass("expanded"),n.blur()})),o.a.registerLanguage("xml",a.a),o.a.registerLanguage("php",l.a),o.a.registerLanguage("json",p.a),e(".code-box-content.xml, .code-box-content.php, .code-box-content.json").each((function(e,t){o.a.highlightBlock(t)})),new r.a(".copy").on("success",(function(e){setTimeout((function(){e.clearSelection()}),150)}))}))}.call(this,n(0))},function(e,t,n){(function(r,i){var o,s;
/*!
 * https://github.com/paulmillr/es6-shim
 * @license es6-shim Copyright 2013-2016 by Paul Miller (http://paulmillr.com)
 *   and contributors,  MIT License
 * es6-shim: v0.35.4
 * see https://github.com/paulmillr/es6-shim/blob/0.35.3/LICENSE
 * Details and documentation:
 * https://github.com/paulmillr/es6-shim/
 */void 0===(s="function"==typeof(o=function(){"use strict";var e,t=Function.call.bind(Function.apply),n=Function.call.bind(Function.call),o=Array.isArray,s=Object.keys,a=function(e){return function(){return!t(e,this,arguments)}},u=function(e){try{return e(),!1}catch(e){return!0}},l=function(e){try{return e()}catch(e){return!1}},c=a(u),p=function(){return!u((function(){return Object.defineProperty({},"x",{get:function(){}})}))},f=!!Object.defineProperty&&p(),h="foo"===function(){}.name,d=Function.call.bind(Array.prototype.forEach),g=Function.call.bind(Array.prototype.reduce),v=Function.call.bind(Array.prototype.filter),y=Function.call.bind(Array.prototype.some),m=function(e,t,n,r){!r&&t in e||(f?Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:!0,value:n}):e[t]=n)},b=function(e,t,n){d(s(t),(function(r){var i=t[r];m(e,r,i,!!n)}))},w=Function.call.bind(Object.prototype.toString),x=function(e){return"function"==typeof e},O={getter:function(e,t,n){if(!f)throw new TypeError("getters require true ES5 support");Object.defineProperty(e,t,{configurable:!0,enumerable:!1,get:n})},proxy:function(e,t,n){if(!f)throw new TypeError("getters require true ES5 support");var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,{configurable:r.configurable,enumerable:r.enumerable,get:function(){return e[t]},set:function(n){e[t]=n}})},redefine:function(e,t,n){if(f){var r=Object.getOwnPropertyDescriptor(e,t);r.value=n,Object.defineProperty(e,t,r)}else e[t]=n},defineByDescriptor:function(e,t,n){f?Object.defineProperty(e,t,n):"value"in n&&(e[t]=n.value)},preserveToString:function(e,t){t&&x(t.toString)&&m(e,"toString",t.toString.bind(t),!0)}},C=Object.create||function(e,t){var n=function(){};n.prototype=e;var r=new n;return void 0!==t&&s(t).forEach((function(e){O.defineByDescriptor(r,e,t[e])})),r},T=function(e,t){return!!Object.setPrototypeOf&&l((function(){var n=function t(n){var r=new e(n);return Object.setPrototypeOf(r,t.prototype),r};return Object.setPrototypeOf(n,e),n.prototype=C(e.prototype,{constructor:{value:n}}),t(n)}))},S=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r)return r;throw new Error("unable to locate global object")},E=S(),j=E.isFinite,_=Function.call.bind(String.prototype.indexOf),N=Function.apply.bind(Array.prototype.indexOf),A=Function.call.bind(Array.prototype.concat),k=Function.call.bind(String.prototype.slice),I=Function.call.bind(Array.prototype.push),P=Function.apply.bind(Array.prototype.push),M=Function.call.bind(Array.prototype.shift),D=Math.max,R=Math.min,L=Math.floor,$=Math.abs,F=Math.exp,q=Math.log,H=Math.sqrt,B=Function.call.bind(Object.prototype.hasOwnProperty),z=function(){},W=E.Map,U=W&&W.prototype.delete,V=W&&W.prototype.get,G=W&&W.prototype.has,K=W&&W.prototype.set,X=E.Symbol||{},Q=X.species||"@@species",Y=Number.isNaN||function(e){return e!=e},J=Number.isFinite||function(e){return"number"==typeof e&&j(e)},Z=x(Math.sign)?Math.sign:function(e){var t=Number(e);return 0===t?t:Y(t)?t:t<0?-1:1},ee=function(e){var t=Number(e);return t<-1||Y(t)?NaN:0===t||t===1/0?t:-1===t?-1/0:1+t-1==0?t:t*(q(1+t)/(1+t-1))},te=function(e){return"[object Arguments]"===w(e)},ne=function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Array]"!==w(e)&&"[object Function]"===w(e.callee)},re=te(arguments)?te:ne,ie={primitive:function(e){return null===e||"function"!=typeof e&&"object"!=typeof e},string:function(e){return"[object String]"===w(e)},regex:function(e){return"[object RegExp]"===w(e)},symbol:function(e){return"function"==typeof E.Symbol&&"symbol"==typeof e}},oe=function(e,t,n){var r=e[t];m(e,t,n,!0),O.preserveToString(e[t],r)},se="function"==typeof X&&"function"==typeof X.for&&ie.symbol(X()),ae=ie.symbol(X.iterator)?X.iterator:"_es6-shim iterator_";E.Set&&"function"==typeof(new E.Set)["@@iterator"]&&(ae="@@iterator"),E.Reflect||m(E,"Reflect",{},!0);var ue,le=E.Reflect,ce=String,pe="undefined"!=typeof document&&document?document.all:null,fe=null==pe?function(e){return null==e}:function(e){return null==e&&e!==pe},he={Call:function(e,n){var r=arguments.length>2?arguments[2]:[];if(!he.IsCallable(e))throw new TypeError(e+" is not a function");return t(e,n,r)},RequireObjectCoercible:function(e,t){if(fe(e))throw new TypeError(t||"Cannot call method on "+e);return e},TypeIsObject:function(e){return null!=e&&!0!==e&&!1!==e&&("function"==typeof e||"object"==typeof e||e===pe)},ToObject:function(e,t){return Object(he.RequireObjectCoercible(e,t))},IsCallable:x,IsConstructor:function(e){return he.IsCallable(e)},ToInt32:function(e){return he.ToNumber(e)>>0},ToUint32:function(e){return he.ToNumber(e)>>>0},ToNumber:function(e){if("[object Symbol]"===w(e))throw new TypeError("Cannot convert a Symbol value to a number");return+e},ToInteger:function(e){var t=he.ToNumber(e);return Y(t)?0:0!==t&&J(t)?(t>0?1:-1)*L($(t)):t},ToLength:function(e){var t=he.ToInteger(e);return t<=0?0:t>Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:t},SameValue:function(e,t){return e===t?0!==e||1/e==1/t:Y(e)&&Y(t)},SameValueZero:function(e,t){return e===t||Y(e)&&Y(t)},IsIterable:function(e){return he.TypeIsObject(e)&&(void 0!==e[ae]||re(e))},GetIterator:function(t){if(re(t))return new e(t,"value");var n=he.GetMethod(t,ae);if(!he.IsCallable(n))throw new TypeError("value is not an iterable");var r=he.Call(n,t);if(!he.TypeIsObject(r))throw new TypeError("bad iterator");return r},GetMethod:function(e,t){var n=he.ToObject(e)[t];if(!fe(n)){if(!he.IsCallable(n))throw new TypeError("Method not callable: "+t);return n}},IteratorComplete:function(e){return!!e.done},IteratorClose:function(e,t){var n=he.GetMethod(e,"return");if(void 0!==n){var r,i;try{r=he.Call(n,e)}catch(e){i=e}if(!t){if(i)throw i;if(!he.TypeIsObject(r))throw new TypeError("Iterator's return method returned a non-object.")}}},IteratorNext:function(e){var t=arguments.length>1?e.next(arguments[1]):e.next();if(!he.TypeIsObject(t))throw new TypeError("bad iterator");return t},IteratorStep:function(e){var t=he.IteratorNext(e);return!he.IteratorComplete(t)&&t},Construct:function(e,t,n,r){var i=void 0===n?e:n;if(!r&&le.construct)return le.construct(e,t,i);var o=i.prototype;he.TypeIsObject(o)||(o=Object.prototype);var s=C(o),a=he.Call(e,s,t);return he.TypeIsObject(a)?a:s},SpeciesConstructor:function(e,t){var n=e.constructor;if(void 0===n)return t;if(!he.TypeIsObject(n))throw new TypeError("Bad constructor");var r=n[Q];if(fe(r))return t;if(!he.IsConstructor(r))throw new TypeError("Bad @@species");return r},CreateHTML:function(e,t,n,r){var i=he.ToString(e),o="<"+t;return""!==n&&(o+=" "+n+'="'+he.ToString(r).replace(/"/g,"&quot;")+'"'),o+">"+i+"</"+t+">"},IsRegExp:function(e){if(!he.TypeIsObject(e))return!1;var t=e[X.match];return void 0!==t?!!t:ie.regex(e)},ToString:function(e){return ce(e)}};if(f&&se){var de=function(e){if(ie.symbol(X[e]))return X[e];var t=X.for("Symbol."+e);return Object.defineProperty(X,e,{configurable:!1,enumerable:!1,writable:!1,value:t}),t};if(!ie.symbol(X.search)){var ge=de("search"),ve=String.prototype.search;m(RegExp.prototype,ge,(function(e){return he.Call(ve,e,[this])}));var ye=function(e){var t=he.RequireObjectCoercible(this);if(!fe(e)){var n=he.GetMethod(e,ge);if(void 0!==n)return he.Call(n,e,[t])}return he.Call(ve,t,[he.ToString(e)])};oe(String.prototype,"search",ye)}if(!ie.symbol(X.replace)){var me=de("replace"),be=String.prototype.replace;m(RegExp.prototype,me,(function(e,t){return he.Call(be,e,[this,t])}));var we=function(e,t){var n=he.RequireObjectCoercible(this);if(!fe(e)){var r=he.GetMethod(e,me);if(void 0!==r)return he.Call(r,e,[n,t])}return he.Call(be,n,[he.ToString(e),t])};oe(String.prototype,"replace",we)}if(!ie.symbol(X.split)){var xe=de("split"),Oe=String.prototype.split;m(RegExp.prototype,xe,(function(e,t){return he.Call(Oe,e,[this,t])}));var Ce=function(e,t){var n=he.RequireObjectCoercible(this);if(!fe(e)){var r=he.GetMethod(e,xe);if(void 0!==r)return he.Call(r,e,[n,t])}return he.Call(Oe,n,[he.ToString(e),t])};oe(String.prototype,"split",Ce)}var Te=ie.symbol(X.match),Se=Te&&((ue={})[X.match]=function(){return 42},42!=="a".match(ue));if(!Te||Se){var Ee=de("match"),je=String.prototype.match;m(RegExp.prototype,Ee,(function(e){return he.Call(je,e,[this])}));var _e=function(e){var t=he.RequireObjectCoercible(this);if(!fe(e)){var n=he.GetMethod(e,Ee);if(void 0!==n)return he.Call(n,e,[t])}return he.Call(je,t,[he.ToString(e)])};oe(String.prototype,"match",_e)}}var Ne=function(e,t,n){O.preserveToString(t,e),Object.setPrototypeOf&&Object.setPrototypeOf(e,t),f?d(Object.getOwnPropertyNames(e),(function(r){r in z||n[r]||O.proxy(e,r,t)})):d(Object.keys(e),(function(r){r in z||n[r]||(t[r]=e[r])})),t.prototype=e.prototype,O.redefine(e.prototype,"constructor",t)},Ae=function(){return this},ke=function(e){f&&!B(e,Q)&&O.getter(e,Q,Ae)},Ie=function(e,t){var n=t||function(){return this};m(e,ae,n),!e[ae]&&ie.symbol(ae)&&(e[ae]=n)},Pe=function(e,t,n){f?Object.defineProperty(e,t,{configurable:!0,enumerable:!0,writable:!0,value:n}):e[t]=n},Me=function(e,t,n){if(Pe(e,t,n),!he.SameValue(e[t],n))throw new TypeError("property is nonconfigurable")},De=function(e,t,n,r){if(!he.TypeIsObject(e))throw new TypeError("Constructor requires `new`: "+t.name);var i=t.prototype;he.TypeIsObject(i)||(i=n);var o=C(i);for(var s in r)if(B(r,s)){var a=r[s];m(o,s,a,!0)}return o};if(String.fromCodePoint&&1!==String.fromCodePoint.length){var Re=String.fromCodePoint;oe(String,"fromCodePoint",(function(e){return he.Call(Re,this,arguments)}))}var Le={fromCodePoint:function(e){for(var t,n=[],r=0,i=arguments.length;r<i;r++){if(t=Number(arguments[r]),!he.SameValue(t,he.ToInteger(t))||t<0||t>1114111)throw new RangeError("Invalid code point "+t);t<65536?I(n,String.fromCharCode(t)):(t-=65536,I(n,String.fromCharCode(55296+(t>>10))),I(n,String.fromCharCode(t%1024+56320)))}return n.join("")},raw:function(e){var t=he.ToObject(e,"bad callSite"),n=he.ToObject(t.raw,"bad raw value"),r=n.length,i=he.ToLength(r);if(i<=0)return"";for(var o,s,a,u,l=[],c=0;c<i&&(o=he.ToString(c),a=he.ToString(n[o]),I(l,a),!(c+1>=i));)s=c+1<arguments.length?arguments[c+1]:"",u=he.ToString(s),I(l,u),c+=1;return l.join("")}};String.raw&&"xy"!==String.raw({raw:{0:"x",1:"y",length:2}})&&oe(String,"raw",Le.raw),b(String,Le);var $e=function e(t,n){if(n<1)return"";if(n%2)return e(t,n-1)+t;var r=e(t,n/2);return r+r},Fe=1/0,qe={repeat:function(e){var t=he.ToString(he.RequireObjectCoercible(this)),n=he.ToInteger(e);if(n<0||n>=Fe)throw new RangeError("repeat count must be less than infinity and not overflow maximum string size");return $e(t,n)},startsWith:function(e){var t=he.ToString(he.RequireObjectCoercible(this));if(he.IsRegExp(e))throw new TypeError('Cannot call method "startsWith" with a regex');var n,r=he.ToString(e);arguments.length>1&&(n=arguments[1]);var i=D(he.ToInteger(n),0);return k(t,i,i+r.length)===r},endsWith:function(e){var t=he.ToString(he.RequireObjectCoercible(this));if(he.IsRegExp(e))throw new TypeError('Cannot call method "endsWith" with a regex');var n,r=he.ToString(e),i=t.length;arguments.length>1&&(n=arguments[1]);var o=void 0===n?i:he.ToInteger(n),s=R(D(o,0),i);return k(t,s-r.length,s)===r},includes:function(e){if(he.IsRegExp(e))throw new TypeError('"includes" does not accept a RegExp');var t,n=he.ToString(e);return arguments.length>1&&(t=arguments[1]),-1!==_(this,n,t)},codePointAt:function(e){var t=he.ToString(he.RequireObjectCoercible(this)),n=he.ToInteger(e),r=t.length;if(n>=0&&n<r){var i=t.charCodeAt(n);if(i<55296||i>56319||n+1===r)return i;var o=t.charCodeAt(n+1);return o<56320||o>57343?i:1024*(i-55296)+(o-56320)+65536}}};if(String.prototype.includes&&!1!=="a".includes("a",1/0)&&oe(String.prototype,"includes",qe.includes),String.prototype.startsWith&&String.prototype.endsWith){var He=u((function(){return"/a/".startsWith(/a/)})),Be=l((function(){return!1==="abc".startsWith("a",1/0)}));He&&Be||(oe(String.prototype,"startsWith",qe.startsWith),oe(String.prototype,"endsWith",qe.endsWith))}if(se){var ze=l((function(){var e=/a/;return e[X.match]=!1,"/a/".startsWith(e)}));ze||oe(String.prototype,"startsWith",qe.startsWith);var We=l((function(){var e=/a/;return e[X.match]=!1,"/a/".endsWith(e)}));We||oe(String.prototype,"endsWith",qe.endsWith);var Ue=l((function(){var e=/a/;return e[X.match]=!1,"/a/".includes(e)}));Ue||oe(String.prototype,"includes",qe.includes)}b(String.prototype,qe);var Ve=["\t\n\v\f\r   ᠎    ","         　\u2028","\u2029\ufeff"].join(""),Ge=new RegExp("(^["+Ve+"]+)|(["+Ve+"]+$)","g"),Ke=function(){return he.ToString(he.RequireObjectCoercible(this)).replace(Ge,"")},Xe=["","​","￾"].join(""),Qe=new RegExp("["+Xe+"]","g"),Ye=/^[-+]0x[0-9a-f]+$/i,Je=Xe.trim().length!==Xe.length;m(String.prototype,"trim",Ke,Je);var Ze=function(e){return{value:e,done:0===arguments.length}},et=function(e){he.RequireObjectCoercible(e),this._s=he.ToString(e),this._i=0};et.prototype.next=function(){var e=this._s,t=this._i;if(void 0===e||t>=e.length)return this._s=void 0,Ze();var n,r,i=e.charCodeAt(t);return r=i<55296||i>56319||t+1===e.length?1:(n=e.charCodeAt(t+1))<56320||n>57343?1:2,this._i=t+r,Ze(e.substr(t,r))},Ie(et.prototype),Ie(String.prototype,(function(){return new et(this)}));var tt={from:function(e){var t,r,i,o=this;if(arguments.length>1&&(t=arguments[1]),void 0===t)r=!1;else{if(!he.IsCallable(t))throw new TypeError("Array.from: when provided, the second argument must be a function");arguments.length>2&&(i=arguments[2]),r=!0}var s,a,u,l=void 0!==(re(e)||he.GetMethod(e,ae));if(l){a=he.IsConstructor(o)?Object(new o):[];var c,p,f=he.GetIterator(e);for(u=0;!1!==(c=he.IteratorStep(f));){p=c.value;try{r&&(p=void 0===i?t(p,u):n(t,i,p,u)),a[u]=p}catch(e){throw he.IteratorClose(f,!0),e}u+=1}s=u}else{var h,d=he.ToObject(e);for(s=he.ToLength(d.length),a=he.IsConstructor(o)?Object(new o(s)):new Array(s),u=0;u<s;++u)h=d[u],r&&(h=void 0===i?t(h,u):n(t,i,h,u)),Me(a,u,h)}return a.length=s,a},of:function(){for(var e=arguments.length,t=this,n=o(t)||!he.IsCallable(t)?new Array(e):he.Construct(t,[e]),r=0;r<e;++r)Me(n,r,arguments[r]);return n.length=e,n}};b(Array,tt),ke(Array),b((e=function(e,t){this.i=0,this.array=e,this.kind=t}).prototype,{next:function(){var t=this.i,n=this.array;if(!(this instanceof e))throw new TypeError("Not an ArrayIterator");if(void 0!==n)for(var r=he.ToLength(n.length);t<r;t++){var i,o=this.kind;return"key"===o?i=t:"value"===o?i=n[t]:"entry"===o&&(i=[t,n[t]]),this.i=t+1,Ze(i)}return this.array=void 0,Ze()}}),Ie(e.prototype);var nt=Array.of===tt.of||function(){var e=function(e){this.length=e};e.prototype=[];var t=Array.of.apply(e,[1,2]);return t instanceof e&&2===t.length}();nt||oe(Array,"of",tt.of);var rt={copyWithin:function(e,t){var n,r=he.ToObject(this),i=he.ToLength(r.length),o=he.ToInteger(e),s=he.ToInteger(t),a=o<0?D(i+o,0):R(o,i),u=s<0?D(i+s,0):R(s,i);arguments.length>2&&(n=arguments[2]);var l=void 0===n?i:he.ToInteger(n),c=l<0?D(i+l,0):R(l,i),p=R(c-u,i-a),f=1;for(u<a&&a<u+p&&(f=-1,u+=p-1,a+=p-1);p>0;)u in r?r[a]=r[u]:delete r[a],u+=f,a+=f,p-=1;return r},fill:function(e){var t,n;arguments.length>1&&(t=arguments[1]),arguments.length>2&&(n=arguments[2]);var r=he.ToObject(this),i=he.ToLength(r.length);t=he.ToInteger(void 0===t?0:t),n=he.ToInteger(void 0===n?i:n);for(var o=t<0?D(i+t,0):R(t,i),s=n<0?i+n:n,a=o;a<i&&a<s;++a)r[a]=e;return r},find:function(e){var t=he.ToObject(this),r=he.ToLength(t.length);if(!he.IsCallable(e))throw new TypeError("Array#find: predicate must be a function");for(var i,o=arguments.length>1?arguments[1]:null,s=0;s<r;s++)if(i=t[s],o){if(n(e,o,i,s,t))return i}else if(e(i,s,t))return i},findIndex:function(e){var t=he.ToObject(this),r=he.ToLength(t.length);if(!he.IsCallable(e))throw new TypeError("Array#findIndex: predicate must be a function");for(var i=arguments.length>1?arguments[1]:null,o=0;o<r;o++)if(i){if(n(e,i,t[o],o,t))return o}else if(e(t[o],o,t))return o;return-1},keys:function(){return new e(this,"key")},values:function(){return new e(this,"value")},entries:function(){return new e(this,"entry")}};if(Array.prototype.keys&&!he.IsCallable([1].keys().next)&&delete Array.prototype.keys,Array.prototype.entries&&!he.IsCallable([1].entries().next)&&delete Array.prototype.entries,Array.prototype.keys&&Array.prototype.entries&&!Array.prototype.values&&Array.prototype[ae]&&(b(Array.prototype,{values:Array.prototype[ae]}),ie.symbol(X.unscopables)&&(Array.prototype[X.unscopables].values=!0)),h&&Array.prototype.values&&"values"!==Array.prototype.values.name){var it=Array.prototype.values;oe(Array.prototype,"values",(function(){return he.Call(it,this,arguments)})),m(Array.prototype,ae,Array.prototype.values,!0)}b(Array.prototype,rt),1/[!0].indexOf(!0,-0)<0&&m(Array.prototype,"indexOf",(function(e){var t=N(this,arguments);return 0===t&&1/t<0?0:t}),!0),Ie(Array.prototype,(function(){return this.values()})),Object.getPrototypeOf&&Ie(Object.getPrototypeOf([].values()));var ot,st=l((function(){return 0===Array.from({length:-1}).length})),at=1===(ot=Array.from([0].entries())).length&&o(ot[0])&&0===ot[0][0]&&0===ot[0][1];st&&at||oe(Array,"from",tt.from);var ut=l((function(){return Array.from([0],void 0)}));if(!ut){var lt=Array.from;oe(Array,"from",(function(e){return arguments.length>1&&void 0!==arguments[1]?he.Call(lt,this,arguments):n(lt,this,e)}))}var ct=-(Math.pow(2,32)-1),pt=function(e,t){var r={length:ct};return r[t?(r.length>>>0)-1:0]=!0,l((function(){return n(e,r,(function(){throw new RangeError("should not reach here")}),[]),!0}))};if(!pt(Array.prototype.forEach)){var ft=Array.prototype.forEach;oe(Array.prototype,"forEach",(function(e){return he.Call(ft,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.map)){var ht=Array.prototype.map;oe(Array.prototype,"map",(function(e){return he.Call(ht,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.filter)){var dt=Array.prototype.filter;oe(Array.prototype,"filter",(function(e){return he.Call(dt,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.some)){var gt=Array.prototype.some;oe(Array.prototype,"some",(function(e){return he.Call(gt,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.every)){var vt=Array.prototype.every;oe(Array.prototype,"every",(function(e){return he.Call(vt,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.reduce)){var yt=Array.prototype.reduce;oe(Array.prototype,"reduce",(function(e){return he.Call(yt,this.length>=0?this:[],arguments)}),!0)}if(!pt(Array.prototype.reduceRight,!0)){var mt=Array.prototype.reduceRight;oe(Array.prototype,"reduceRight",(function(e){return he.Call(mt,this.length>=0?this:[],arguments)}),!0)}var bt=8!==Number("0o10"),wt=2!==Number("0b10"),xt=y(Xe,(function(e){return 0===Number(e+0+e)}));if(bt||wt||xt){var Ot=Number,Ct=/^0b[01]+$/i,Tt=/^0o[0-7]+$/i,St=Ct.test.bind(Ct),Et=Tt.test.bind(Tt),jt=function(e){var t;if("function"==typeof e.valueOf&&(t=e.valueOf(),ie.primitive(t)))return t;if("function"==typeof e.toString&&(t=e.toString(),ie.primitive(t)))return t;throw new TypeError("No default value")},_t=Qe.test.bind(Qe),Nt=Ye.test.bind(Ye),At=function(){var e=function(t){var n;"string"==typeof(n=arguments.length>0?ie.primitive(t)?t:jt(t,"number"):0)&&(n=he.Call(Ke,n),St(n)?n=parseInt(k(n,2),2):Et(n)?n=parseInt(k(n,2),8):(_t(n)||Nt(n))&&(n=NaN));var r=this,i=l((function(){return Ot.prototype.valueOf.call(r),!0}));return r instanceof e&&!i?new Ot(n):Ot(n)};return e}();Ne(Ot,At,{}),b(At,{NaN:Ot.NaN,MAX_VALUE:Ot.MAX_VALUE,MIN_VALUE:Ot.MIN_VALUE,NEGATIVE_INFINITY:Ot.NEGATIVE_INFINITY,POSITIVE_INFINITY:Ot.POSITIVE_INFINITY}),Number=At,O.redefine(E,"Number",At)}var kt=Math.pow(2,53)-1;b(Number,{MAX_SAFE_INTEGER:kt,MIN_SAFE_INTEGER:-kt,EPSILON:2220446049250313e-31,parseInt:E.parseInt,parseFloat:E.parseFloat,isFinite:J,isInteger:function(e){return J(e)&&he.ToInteger(e)===e},isSafeInteger:function(e){return Number.isInteger(e)&&$(e)<=Number.MAX_SAFE_INTEGER},isNaN:Y}),m(Number,"parseInt",E.parseInt,Number.parseInt!==E.parseInt),1===[,1].find((function(){return!0}))&&oe(Array.prototype,"find",rt.find),0!==[,1].findIndex((function(){return!0}))&&oe(Array.prototype,"findIndex",rt.findIndex);var It,Pt,Mt,Dt=Function.bind.call(Function.bind,Object.prototype.propertyIsEnumerable),Rt=function(e,t){f&&Dt(e,t)&&Object.defineProperty(e,t,{enumerable:!1})},Lt=function(){for(var e=Number(this),t=arguments.length,n=t-e,r=new Array(n<0?0:n),i=e;i<t;++i)r[i-e]=arguments[i];return r},$t=function(e){return function(t,n){return t[n]=e[n],t}},Ft=function(e,t){var n,r=s(Object(t));return he.IsCallable(Object.getOwnPropertySymbols)&&(n=v(Object.getOwnPropertySymbols(Object(t)),Dt(t))),g(A(r,n||[]),$t(t),e)},qt={assign:function(e,t){var n=he.ToObject(e,"Cannot convert undefined or null to object");return g(he.Call(Lt,1,arguments),Ft,n)},is:function(e,t){return he.SameValue(e,t)}},Ht=Object.assign&&Object.preventExtensions&&function(){var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}}();if(Ht&&oe(Object,"assign",qt.assign),b(Object,qt),f){var Bt={setPrototypeOf:function(e,t){var r,i=function(e,t){return function(e,t){if(!he.TypeIsObject(e))throw new TypeError("cannot set prototype on a non-object");if(null!==t&&!he.TypeIsObject(t))throw new TypeError("can only set prototype to an object or null"+t)}(e,t),n(r,e,t),e};try{r=e.getOwnPropertyDescriptor(e.prototype,"__proto__").set,n(r,{},null)}catch(t){if(e.prototype!=={}.__proto__)return;r=function(e){this.__proto__=e},i.polyfill=i(i({},null),e.prototype)instanceof e}return i}(Object)};b(Object,Bt)}Object.setPrototypeOf&&Object.getPrototypeOf&&null!==Object.getPrototypeOf(Object.setPrototypeOf({},null))&&null===Object.getPrototypeOf(Object.create(null))&&(It=Object.create(null),Pt=Object.getPrototypeOf,Mt=Object.setPrototypeOf,Object.getPrototypeOf=function(e){var t=Pt(e);return t===It?null:t},Object.setPrototypeOf=function(e,t){return Mt(e,null===t?It:t)},Object.setPrototypeOf.polyfill=!1);var zt=!u((function(){return Object.keys("foo")}));if(!zt){var Wt=Object.keys;oe(Object,"keys",(function(e){return Wt(he.ToObject(e))})),s=Object.keys}var Ut=u((function(){return Object.keys(/a/g)}));if(Ut){var Vt=Object.keys;oe(Object,"keys",(function(e){if(ie.regex(e)){var t=[];for(var n in e)B(e,n)&&I(t,n);return t}return Vt(e)})),s=Object.keys}if(Object.getOwnPropertyNames){var Gt=!u((function(){return Object.getOwnPropertyNames("foo")}));if(!Gt){var Kt="object"==typeof window?Object.getOwnPropertyNames(window):[],Xt=Object.getOwnPropertyNames;oe(Object,"getOwnPropertyNames",(function(e){var t=he.ToObject(e);if("[object Window]"===w(t))try{return Xt(t)}catch(e){return A([],Kt)}return Xt(t)}))}}if(Object.getOwnPropertyDescriptor){var Qt=!u((function(){return Object.getOwnPropertyDescriptor("foo","bar")}));if(!Qt){var Yt=Object.getOwnPropertyDescriptor;oe(Object,"getOwnPropertyDescriptor",(function(e,t){return Yt(he.ToObject(e),t)}))}}if(Object.seal){var Jt=!u((function(){return Object.seal("foo")}));if(!Jt){var Zt=Object.seal;oe(Object,"seal",(function(e){return he.TypeIsObject(e)?Zt(e):e}))}}if(Object.isSealed){var en=!u((function(){return Object.isSealed("foo")}));if(!en){var tn=Object.isSealed;oe(Object,"isSealed",(function(e){return!he.TypeIsObject(e)||tn(e)}))}}if(Object.freeze){var nn=!u((function(){return Object.freeze("foo")}));if(!nn){var rn=Object.freeze;oe(Object,"freeze",(function(e){return he.TypeIsObject(e)?rn(e):e}))}}if(Object.isFrozen){var on=!u((function(){return Object.isFrozen("foo")}));if(!on){var sn=Object.isFrozen;oe(Object,"isFrozen",(function(e){return!he.TypeIsObject(e)||sn(e)}))}}if(Object.preventExtensions){var an=!u((function(){return Object.preventExtensions("foo")}));if(!an){var un=Object.preventExtensions;oe(Object,"preventExtensions",(function(e){return he.TypeIsObject(e)?un(e):e}))}}if(Object.isExtensible){var ln=!u((function(){return Object.isExtensible("foo")}));if(!ln){var cn=Object.isExtensible;oe(Object,"isExtensible",(function(e){return!!he.TypeIsObject(e)&&cn(e)}))}}if(Object.getPrototypeOf){var pn=!u((function(){return Object.getPrototypeOf("foo")}));if(!pn){var fn=Object.getPrototypeOf;oe(Object,"getPrototypeOf",(function(e){return fn(he.ToObject(e))}))}}var hn,dn=f&&((hn=Object.getOwnPropertyDescriptor(RegExp.prototype,"flags"))&&he.IsCallable(hn.get));if(f&&!dn){var gn=function(){if(!he.TypeIsObject(this))throw new TypeError("Method called on incompatible type: must be an object.");var e="";return this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.unicode&&(e+="u"),this.sticky&&(e+="y"),e};O.getter(RegExp.prototype,"flags",gn)}var vn,yn=f&&l((function(){return"/a/i"===String(new RegExp(/a/g,"i"))})),mn=se&&f&&((vn=/./)[X.match]=!1,RegExp(vn)===vn),bn=l((function(){return"/abc/"===RegExp.prototype.toString.call({source:"abc"})})),wn=bn&&l((function(){return"/a/b"===RegExp.prototype.toString.call({source:"a",flags:"b"})}));if(!bn||!wn){var xn=RegExp.prototype.toString;m(RegExp.prototype,"toString",(function(){var e=he.RequireObjectCoercible(this);return ie.regex(e)?n(xn,e):"/"+ce(e.source)+"/"+ce(e.flags)}),!0),O.preserveToString(RegExp.prototype.toString,xn)}if(f&&(!yn||mn)){var On=Object.getOwnPropertyDescriptor(RegExp.prototype,"flags").get,Cn=Object.getOwnPropertyDescriptor(RegExp.prototype,"source")||{},Tn=function(){return this.source},Sn=he.IsCallable(Cn.get)?Cn.get:Tn,En=RegExp,jn=function e(t,n){var r=he.IsRegExp(t);return this instanceof e||!r||void 0!==n||t.constructor!==e?ie.regex(t)?new e(he.Call(Sn,t),void 0===n?he.Call(On,t):n):(r&&(t.source,void 0===n&&t.flags),new En(t,n)):t};Ne(En,jn,{$input:!0}),RegExp=jn,O.redefine(E,"RegExp",jn)}if(f){var _n={input:"$_",lastMatch:"$&",lastParen:"$+",leftContext:"$`",rightContext:"$'"};d(s(_n),(function(e){e in RegExp&&!(_n[e]in RegExp)&&O.getter(RegExp,_n[e],(function(){return RegExp[e]}))}))}ke(RegExp);var Nn=1/Number.EPSILON,An=function(e){return e+Nn-Nn},kn=Math.pow(2,-23),In=Math.pow(2,127)*(2-kn),Pn=Math.pow(2,-126),Mn=Math.E,Dn=Math.LOG2E,Rn=Math.LOG10E,Ln=Number.prototype.clz;delete Number.prototype.clz;var $n={acosh:function(e){var t=Number(e);if(Y(t)||e<1)return NaN;if(1===t)return 0;if(t===1/0)return t;var n=1/(t*t);if(t<2)return ee(t-1+H(1-n)*t);var r=t/2;return ee(r+H(1-n)*r-1)+1/Dn},asinh:function(e){var t=Number(e);if(0===t||!j(t))return t;var n=$(t),r=n*n,i=Z(t);return n<1?i*ee(n+r/(H(r+1)+1)):i*(ee(n/2+H(1+1/r)*n/2-1)+1/Dn)},atanh:function(e){var t=Number(e);if(0===t)return t;if(-1===t)return-1/0;if(1===t)return 1/0;if(Y(t)||t<-1||t>1)return NaN;var n=$(t);return Z(t)*ee(2*n/(1-n))/2},cbrt:function(e){var t=Number(e);if(0===t)return t;var n,r=t<0;return r&&(t=-t),n=t===1/0?1/0:(t/((n=F(q(t)/3))*n)+2*n)/3,r?-n:n},clz32:function(e){var t=Number(e),n=he.ToUint32(t);return 0===n?32:Ln?he.Call(Ln,n):31-L(q(n+.5)*Dn)},cosh:function(e){var t=Number(e);if(0===t)return 1;if(Y(t))return NaN;if(!j(t))return 1/0;var n=F($(t)-1);return(n+1/(n*Mn*Mn))*(Mn/2)},expm1:function(e){var t=Number(e);if(t===-1/0)return-1;if(!j(t)||0===t)return t;if($(t)>.5)return F(t)-1;for(var n=t,r=0,i=1;r+n!==r;)r+=n,n*=t/(i+=1);return r},hypot:function(e,t){for(var n=0,r=0,i=0;i<arguments.length;++i){var o=$(Number(arguments[i]));r<o?(n*=r/o*(r/o),n+=1,r=o):n+=o>0?o/r*(o/r):o}return r===1/0?1/0:r*H(n)},log2:function(e){return q(e)*Dn},log10:function(e){return q(e)*Rn},log1p:ee,sign:Z,sinh:function(e){var t=Number(e);if(!j(t)||0===t)return t;var n=$(t);if(n<1){var r=Math.expm1(n);return Z(t)*r*(1+1/(r+1))/2}var i=F(n-1);return Z(t)*(i-1/(i*Mn*Mn))*(Mn/2)},tanh:function(e){var t=Number(e);return Y(t)||0===t?t:t>=20?1:t<=-20?-1:(Math.expm1(t)-Math.expm1(-t))/(F(t)+F(-t))},trunc:function(e){var t=Number(e);return t<0?-L(-t):L(t)},imul:function(e,t){var n=he.ToUint32(e),r=he.ToUint32(t),i=65535&n,o=65535&r;return i*o+((n>>>16&65535)*o+i*(r>>>16&65535)<<16>>>0)|0},fround:function(e){var t=Number(e);if(0===t||t===1/0||t===-1/0||Y(t))return t;var n=Z(t),r=$(t);if(r<Pn)return n*An(r/Pn/kn)*Pn*kn;var i=(1+kn/Number.EPSILON)*r,o=i-(i-r);return o>In||Y(o)?n*(1/0):n*o}},Fn=function(e,t,n){return $(1-e/t)/Number.EPSILON<(n||8)};b(Math,$n),m(Math,"sinh",$n.sinh,Math.sinh(710)===1/0),m(Math,"cosh",$n.cosh,Math.cosh(710)===1/0),m(Math,"log1p",$n.log1p,-1e-17!==Math.log1p(-1e-17)),m(Math,"asinh",$n.asinh,Math.asinh(-1e7)!==-Math.asinh(1e7)),m(Math,"asinh",$n.asinh,Math.asinh(1e300)===1/0),m(Math,"atanh",$n.atanh,0===Math.atanh(1e-300)),m(Math,"tanh",$n.tanh,-2e-17!==Math.tanh(-2e-17)),m(Math,"acosh",$n.acosh,Math.acosh(Number.MAX_VALUE)===1/0),m(Math,"acosh",$n.acosh,!Fn(Math.acosh(1+Number.EPSILON),Math.sqrt(2*Number.EPSILON))),m(Math,"cbrt",$n.cbrt,!Fn(Math.cbrt(1e-300),1e-100)),m(Math,"sinh",$n.sinh,-2e-17!==Math.sinh(-2e-17));var qn=Math.expm1(10);m(Math,"expm1",$n.expm1,qn>22025.465794806718||qn<22025.465794806718);var Hn=Math.round,Bn=0===Math.round(.5-Number.EPSILON/4)&&1===Math.round(Number.EPSILON/3.99-.5),zn=Nn+1,Wn=2*Nn-1,Un=[zn,Wn].every((function(e){return Math.round(e)===e}));m(Math,"round",(function(e){var t=L(e);return e-t<.5?t:-1===t?-0:t+1}),!Bn||!Un),O.preserveToString(Math.round,Hn);var Vn=Math.imul;-5!==Math.imul(4294967295,5)&&(Math.imul=$n.imul,O.preserveToString(Math.imul,Vn)),2!==Math.imul.length&&oe(Math,"imul",(function(e,t){return he.Call(Vn,Math,arguments)}));var Gn,Kn,Xn=function(){var e=E.setTimeout;if("function"==typeof e||"object"==typeof e){he.IsPromise=function(e){return!!he.TypeIsObject(e)&&void 0!==e._promise};var t,r=function(e){if(!he.IsConstructor(e))throw new TypeError("Bad promise constructor");var t=this;if(t.resolve=void 0,t.reject=void 0,t.promise=new e((function(e,n){if(void 0!==t.resolve||void 0!==t.reject)throw new TypeError("Bad Promise implementation!");t.resolve=e,t.reject=n})),!he.IsCallable(t.resolve)||!he.IsCallable(t.reject))throw new TypeError("Bad promise constructor")};"undefined"!=typeof window&&he.IsCallable(window.postMessage)&&(t=function(){var e=[];return window.addEventListener("message",(function(t){if(t.source===window&&"zero-timeout-message"===t.data){if(t.stopPropagation(),0===e.length)return;M(e)()}}),!0),function(t){I(e,t),window.postMessage("zero-timeout-message","*")}});var o,s,a,u,l=he.IsCallable(E.setImmediate)?E.setImmediate:"object"==typeof i&&i.nextTick?i.nextTick:(o=E.Promise,(s=o&&o.resolve&&o.resolve())&&function(e){return s.then(e)}||(he.IsCallable(t)?t():function(t){e(t,0)})),c=function(e){return e},p=function(e){throw e},f={},h=function(e,t,n){l((function(){d(e,t,n)}))},d=function(e,t,n){var r,i;if(t===f)return e(n);try{r=e(n),i=t.resolve}catch(e){r=e,i=t.reject}i(r)},g=function(e,t){var n=e._promise,r=n.reactionLength;if(r>0&&(h(n.fulfillReactionHandler0,n.reactionCapability0,t),n.fulfillReactionHandler0=void 0,n.rejectReactions0=void 0,n.reactionCapability0=void 0,r>1))for(var i=1,o=0;i<r;i++,o+=3)h(n[o+0],n[o+2],t),e[o+0]=void 0,e[o+1]=void 0,e[o+2]=void 0;n.result=t,n.state=1,n.reactionLength=0},v=function(e,t){var n=e._promise,r=n.reactionLength;if(r>0&&(h(n.rejectReactionHandler0,n.reactionCapability0,t),n.fulfillReactionHandler0=void 0,n.rejectReactions0=void 0,n.reactionCapability0=void 0,r>1))for(var i=1,o=0;i<r;i++,o+=3)h(n[o+1],n[o+2],t),e[o+0]=void 0,e[o+1]=void 0,e[o+2]=void 0;n.result=t,n.state=2,n.reactionLength=0},y=function(e){var t=!1;return{resolve:function(n){var r;if(!t){if(t=!0,n===e)return v(e,new TypeError("Self resolution"));if(!he.TypeIsObject(n))return g(e,n);try{r=n.then}catch(t){return v(e,t)}if(!he.IsCallable(r))return g(e,n);l((function(){w(e,n,r)}))}},reject:function(n){if(!t)return t=!0,v(e,n)}}},m=function(e,t,r,i){e===u?n(e,t,r,i,f):n(e,t,r,i)},w=function(e,t,n){var r=y(e),i=r.resolve,o=r.reject;try{m(n,t,i,o)}catch(e){o(e)}},x=function(){var e=function(t){if(!(this instanceof e))throw new TypeError('Constructor Promise requires "new"');if(this&&this._promise)throw new TypeError("Bad construction");if(!he.IsCallable(t))throw new TypeError("not a valid resolver");var n=De(this,e,a,{_promise:{result:void 0,state:0,reactionLength:0,fulfillReactionHandler0:void 0,rejectReactionHandler0:void 0,reactionCapability0:void 0}}),r=y(n),i=r.reject;try{t(r.resolve,i)}catch(e){i(e)}return n};return e}();a=x.prototype;var O=function(e,t,n,r){var i=!1;return function(o){i||(i=!0,t[e]=o,0==--r.count&&(0,n.resolve)(t))}};return b(x,{all:function(e){var t=this;if(!he.TypeIsObject(t))throw new TypeError("Promise is not object");var n,i,o=new r(t);try{return function(e,t,n){for(var r,i,o=e.iterator,s=[],a={count:1},u=0;;){try{if(!1===(r=he.IteratorStep(o))){e.done=!0;break}i=r.value}catch(t){throw e.done=!0,t}s[u]=void 0;var l=t.resolve(i),c=O(u,s,n,a);a.count+=1,m(l.then,l,c,n.reject),u+=1}return 0==--a.count&&(0,n.resolve)(s),n.promise}(i={iterator:n=he.GetIterator(e),done:!1},t,o)}catch(e){var s=e;if(i&&!i.done)try{he.IteratorClose(n,!0)}catch(e){s=e}return(0,o.reject)(s),o.promise}},race:function(e){var t=this;if(!he.TypeIsObject(t))throw new TypeError("Promise is not object");var n,i,o=new r(t);try{return function(e,t,n){for(var r,i,o,s=e.iterator;;){try{if(!1===(r=he.IteratorStep(s))){e.done=!0;break}i=r.value}catch(t){throw e.done=!0,t}o=t.resolve(i),m(o.then,o,n.resolve,n.reject)}return n.promise}(i={iterator:n=he.GetIterator(e),done:!1},t,o)}catch(e){var s=e;if(i&&!i.done)try{he.IteratorClose(n,!0)}catch(e){s=e}return(0,o.reject)(s),o.promise}},reject:function(e){if(!he.TypeIsObject(this))throw new TypeError("Bad promise constructor");var t=new r(this);return(0,t.reject)(e),t.promise},resolve:function(e){var t=this;if(!he.TypeIsObject(t))throw new TypeError("Bad promise constructor");if(he.IsPromise(e)&&e.constructor===t)return e;var n=new r(t);return(0,n.resolve)(e),n.promise}}),b(a,{catch:function(e){return this.then(null,e)},then:function(e,t){var n=this;if(!he.IsPromise(n))throw new TypeError("not a promise");var i,o=he.SpeciesConstructor(n,x),s=arguments.length>2&&arguments[2]===f;i=s&&o===x?f:new r(o);var a,u=he.IsCallable(e)?e:c,l=he.IsCallable(t)?t:p,d=n._promise;if(0===d.state){if(0===d.reactionLength)d.fulfillReactionHandler0=u,d.rejectReactionHandler0=l,d.reactionCapability0=i;else{var g=3*(d.reactionLength-1);d[g+0]=u,d[g+1]=l,d[g+2]=i}d.reactionLength+=1}else if(1===d.state)a=d.result,h(u,i,a);else{if(2!==d.state)throw new TypeError("unexpected Promise state");a=d.result,h(l,i,a)}return i.promise}}),f=new r(x),u=a.then,x}}();if(E.Promise&&(delete E.Promise.accept,delete E.Promise.defer,delete E.Promise.prototype.chain),"function"==typeof Xn){b(E,{Promise:Xn});var Qn=T(E.Promise,(function(e){return e.resolve(42).then((function(){}))instanceof e})),Yn=!u((function(){return E.Promise.reject(42).then(null,5).then(null,z)})),Jn=u((function(){return E.Promise.call(3,z)})),Zn=function(e){var t=e.resolve(5);t.constructor={};var n=e.resolve(t);try{n.then(null,z).then(null,z)}catch(e){return!0}return t===n}(E.Promise),er=f&&(Gn=0,Kn=Object.defineProperty({},"then",{get:function(){Gn+=1}}),Promise.resolve(Kn),1===Gn),tr=function e(t){var n=new Promise(t);t(3,(function(){})),this.then=n.then,this.constructor=e};tr.prototype=Promise.prototype,tr.all=Promise.all;var nr=l((function(){return!!tr.all([1,2])}));if(Qn&&Yn&&Jn&&!Zn&&er&&!nr||(Promise=Xn,oe(E,"Promise",Xn)),1!==Promise.all.length){var rr=Promise.all;oe(Promise,"all",(function(e){return he.Call(rr,this,arguments)}))}if(1!==Promise.race.length){var ir=Promise.race;oe(Promise,"race",(function(e){return he.Call(ir,this,arguments)}))}if(1!==Promise.resolve.length){var or=Promise.resolve;oe(Promise,"resolve",(function(e){return he.Call(or,this,arguments)}))}if(1!==Promise.reject.length){var sr=Promise.reject;oe(Promise,"reject",(function(e){return he.Call(sr,this,arguments)}))}Rt(Promise,"all"),Rt(Promise,"race"),Rt(Promise,"resolve"),Rt(Promise,"reject"),ke(Promise)}var ar,ur,lr=function(e){var t=s(g(e,(function(e,t){return e[t]=!0,e}),{}));return e.join(":")===t.join(":")},cr=lr(["z","a","bb"]),pr=lr(["z",1,"a","3",2]);if(f){var fr=function(e,t){return t||cr?fe(e)?"^"+he.ToString(e):"string"==typeof e?"$"+e:"number"==typeof e?pr?e:"n"+e:"boolean"==typeof e?"b"+e:null:null},hr=function(){return Object.create?Object.create(null):{}},dr=function(e,t,r){if(o(r)||ie.string(r))d(r,(function(e){if(!he.TypeIsObject(e))throw new TypeError("Iterator value "+e+" is not an entry object");t.set(e[0],e[1])}));else if(r instanceof e)n(e.prototype.forEach,r,(function(e,n){t.set(n,e)}));else{var i,s;if(!fe(r)){if(s=t.set,!he.IsCallable(s))throw new TypeError("bad map");i=he.GetIterator(r)}if(void 0!==i)for(;;){var a=he.IteratorStep(i);if(!1===a)break;var u=a.value;try{if(!he.TypeIsObject(u))throw new TypeError("Iterator value "+u+" is not an entry object");n(s,t,u[0],u[1])}catch(e){throw he.IteratorClose(i,!0),e}}}},gr=function(e,t,r){if(o(r)||ie.string(r))d(r,(function(e){t.add(e)}));else if(r instanceof e)n(e.prototype.forEach,r,(function(e){t.add(e)}));else{var i,s;if(!fe(r)){if(s=t.add,!he.IsCallable(s))throw new TypeError("bad set");i=he.GetIterator(r)}if(void 0!==i)for(;;){var a=he.IteratorStep(i);if(!1===a)break;var u=a.value;try{n(s,t,u)}catch(e){throw he.IteratorClose(i,!0),e}}}},vr={Map:function(){var e={},t=function(e,t){this.key=e,this.value=t,this.next=null,this.prev=null};t.prototype.isRemoved=function(){return this.key===e};var r,i=function(e,t){if(!he.TypeIsObject(e)||!function(e){return!!e._es6map}(e))throw new TypeError("Method Map.prototype."+t+" called on incompatible receiver "+he.ToString(e))},o=function(e,t){i(e,"[[MapIterator]]"),this.head=e._head,this.i=this.head,this.kind=t};Ie(o.prototype={isMapIterator:!0,next:function(){if(!this.isMapIterator)throw new TypeError("Not a MapIterator");var e,t=this.i,n=this.kind,r=this.head;if(void 0===this.i)return Ze();for(;t.isRemoved()&&t!==r;)t=t.prev;for(;t.next!==r;)if(!(t=t.next).isRemoved())return e="key"===n?t.key:"value"===n?t.value:[t.key,t.value],this.i=t,Ze(e);return this.i=void 0,Ze()}});var s=function e(){if(!(this instanceof e))throw new TypeError('Constructor Map requires "new"');if(this&&this._es6map)throw new TypeError("Bad construction");var n=De(this,e,r,{_es6map:!0,_head:null,_map:W?new W:null,_size:0,_storage:hr()}),i=new t(null,null);return i.next=i.prev=i,n._head=i,arguments.length>0&&dr(e,n,arguments[0]),n};return r=s.prototype,O.getter(r,"size",(function(){if(void 0===this._size)throw new TypeError("size method called on incompatible Map");return this._size})),b(r,{get:function(e){var t;i(this,"get");var n=fr(e,!0);if(null!==n)return(t=this._storage[n])?t.value:void 0;if(this._map)return(t=V.call(this._map,e))?t.value:void 0;for(var r=this._head,o=r;(o=o.next)!==r;)if(he.SameValueZero(o.key,e))return o.value},has:function(e){i(this,"has");var t=fr(e,!0);if(null!==t)return void 0!==this._storage[t];if(this._map)return G.call(this._map,e);for(var n=this._head,r=n;(r=r.next)!==n;)if(he.SameValueZero(r.key,e))return!0;return!1},set:function(e,n){i(this,"set");var r,o=this._head,s=o,a=fr(e,!0);if(null!==a){if(void 0!==this._storage[a])return this._storage[a].value=n,this;r=this._storage[a]=new t(e,n),s=o.prev}else this._map&&(G.call(this._map,e)?V.call(this._map,e).value=n:(r=new t(e,n),K.call(this._map,e,r),s=o.prev));for(;(s=s.next)!==o;)if(he.SameValueZero(s.key,e))return s.value=n,this;return r=r||new t(e,n),he.SameValue(-0,e)&&(r.key=0),r.next=this._head,r.prev=this._head.prev,r.prev.next=r,r.next.prev=r,this._size+=1,this},delete:function(t){i(this,"delete");var n=this._head,r=n,o=fr(t,!0);if(null!==o){if(void 0===this._storage[o])return!1;r=this._storage[o].prev,delete this._storage[o]}else if(this._map){if(!G.call(this._map,t))return!1;r=V.call(this._map,t).prev,U.call(this._map,t)}for(;(r=r.next)!==n;)if(he.SameValueZero(r.key,t))return r.key=e,r.value=e,r.prev.next=r.next,r.next.prev=r.prev,this._size-=1,!0;return!1},clear:function(){i(this,"clear"),this._map=W?new W:null,this._size=0,this._storage=hr();for(var t=this._head,n=t,r=n.next;(n=r)!==t;)n.key=e,n.value=e,r=n.next,n.next=n.prev=t;t.next=t.prev=t},keys:function(){return i(this,"keys"),new o(this,"key")},values:function(){return i(this,"values"),new o(this,"value")},entries:function(){return i(this,"entries"),new o(this,"key+value")},forEach:function(e){i(this,"forEach");for(var t=arguments.length>1?arguments[1]:null,r=this.entries(),o=r.next();!o.done;o=r.next())t?n(e,t,o.value[1],o.value[0],this):e(o.value[1],o.value[0],this)}}),Ie(r,r.entries),s}(),Set:function(){var e,t=function(e,t){if(!he.TypeIsObject(e)||!function(e){return e._es6set&&void 0!==e._storage}(e))throw new TypeError("Set.prototype."+t+" called on incompatible receiver "+he.ToString(e))},r=function t(){if(!(this instanceof t))throw new TypeError('Constructor Set requires "new"');if(this&&this._es6set)throw new TypeError("Bad construction");var n=De(this,t,e,{_es6set:!0,"[[SetData]]":null,_storage:hr()});if(!n._es6set)throw new TypeError("bad set");return arguments.length>0&&gr(t,n,arguments[0]),n};e=r.prototype;var i=function(e){if(!e["[[SetData]]"]){var t=new vr.Map;e["[[SetData]]"]=t,d(s(e._storage),(function(e){var n=function(e){var t=e;if("^null"===t)return null;if("^undefined"!==t){var n=t.charAt(0);return"$"===n?k(t,1):"n"===n?+k(t,1):"b"===n?"btrue"===t:+t}}(e);t.set(n,n)})),e["[[SetData]]"]=t}e._storage=null};O.getter(r.prototype,"size",(function(){return t(this,"size"),this._storage?s(this._storage).length:(i(this),this["[[SetData]]"].size)})),b(r.prototype,{has:function(e){var n;return t(this,"has"),this._storage&&null!==(n=fr(e))?!!this._storage[n]:(i(this),this["[[SetData]]"].has(e))},add:function(e){var n;return t(this,"add"),this._storage&&null!==(n=fr(e))?(this._storage[n]=!0,this):(i(this),this["[[SetData]]"].set(e,e),this)},delete:function(e){var n;if(t(this,"delete"),this._storage&&null!==(n=fr(e))){var r=B(this._storage,n);return delete this._storage[n]&&r}return i(this),this["[[SetData]]"].delete(e)},clear:function(){t(this,"clear"),this._storage&&(this._storage=hr()),this["[[SetData]]"]&&this["[[SetData]]"].clear()},values:function(){return t(this,"values"),i(this),new o(this["[[SetData]]"].values())},entries:function(){return t(this,"entries"),i(this),new o(this["[[SetData]]"].entries())},forEach:function(e){t(this,"forEach");var r=arguments.length>1?arguments[1]:null,o=this;i(o),this["[[SetData]]"].forEach((function(t,i){r?n(e,r,i,i,o):e(i,i,o)}))}}),m(r.prototype,"keys",r.prototype.values,!0),Ie(r.prototype,r.prototype.values);var o=function(e){this.it=e};return o.prototype={isSetIterator:!0,next:function(){if(!this.isSetIterator)throw new TypeError("Not a SetIterator");return this.it.next()}},Ie(o.prototype),r}()},yr=E.Set&&!Set.prototype.delete&&Set.prototype.remove&&Set.prototype.items&&Set.prototype.map&&Array.isArray((new Set).keys);if(yr&&(E.Set=vr.Set),E.Map||E.Set){var mr=l((function(){return 2===new Map([[1,2]]).get(1)}));mr||(E.Map=function e(){if(!(this instanceof e))throw new TypeError('Constructor Map requires "new"');var t=new W;return arguments.length>0&&dr(e,t,arguments[0]),delete t.constructor,Object.setPrototypeOf(t,E.Map.prototype),t},E.Map.prototype=C(W.prototype),m(E.Map.prototype,"constructor",E.Map,!0),O.preserveToString(E.Map,W));var br=new Map,wr=((ur=new Map([[1,0],[2,0],[3,0],[4,0]])).set(-0,ur),ur.get(0)===ur&&ur.get(-0)===ur&&ur.has(0)&&ur.has(-0)),xr=br.set(1,2)===br;wr&&xr||oe(Map.prototype,"set",(function(e,t){return n(K,this,0===e?0:e,t),this})),wr||(b(Map.prototype,{get:function(e){return n(V,this,0===e?0:e)},has:function(e){return n(G,this,0===e?0:e)}},!0),O.preserveToString(Map.prototype.get,V),O.preserveToString(Map.prototype.has,G));var Or=new Set,Cr=Set.prototype.delete&&Set.prototype.add&&Set.prototype.has&&((ar=Or).delete(0),ar.add(-0),!ar.has(0)),Tr=Or.add(1)===Or;if(!Cr||!Tr){var Sr=Set.prototype.add;Set.prototype.add=function(e){return n(Sr,this,0===e?0:e),this},O.preserveToString(Set.prototype.add,Sr)}if(!Cr){var Er=Set.prototype.has;Set.prototype.has=function(e){return n(Er,this,0===e?0:e)},O.preserveToString(Set.prototype.has,Er);var jr=Set.prototype.delete;Set.prototype.delete=function(e){return n(jr,this,0===e?0:e)},O.preserveToString(Set.prototype.delete,jr)}var _r=T(E.Map,(function(e){var t=new e([]);return t.set(42,42),t instanceof e})),Nr=Object.setPrototypeOf&&!_r,Ar=function(){try{return!(E.Map()instanceof E.Map)}catch(e){return e instanceof TypeError}}();0===E.Map.length&&!Nr&&Ar||(E.Map=function e(){if(!(this instanceof e))throw new TypeError('Constructor Map requires "new"');var t=new W;return arguments.length>0&&dr(e,t,arguments[0]),delete t.constructor,Object.setPrototypeOf(t,e.prototype),t},E.Map.prototype=W.prototype,m(E.Map.prototype,"constructor",E.Map,!0),O.preserveToString(E.Map,W));var kr=T(E.Set,(function(e){var t=new e([]);return t.add(42,42),t instanceof e})),Ir=Object.setPrototypeOf&&!kr,Pr=function(){try{return!(E.Set()instanceof E.Set)}catch(e){return e instanceof TypeError}}();if(0!==E.Set.length||Ir||!Pr){var Mr=E.Set;E.Set=function e(){if(!(this instanceof e))throw new TypeError('Constructor Set requires "new"');var t=new Mr;return arguments.length>0&&gr(e,t,arguments[0]),delete t.constructor,Object.setPrototypeOf(t,e.prototype),t},E.Set.prototype=Mr.prototype,m(E.Set.prototype,"constructor",E.Set,!0),O.preserveToString(E.Set,Mr)}var Dr=new E.Map,Rr=!l((function(){return Dr.keys().next().done}));if(("function"!=typeof E.Map.prototype.clear||0!==(new E.Set).size||0!==Dr.size||"function"!=typeof E.Map.prototype.keys||"function"!=typeof E.Set.prototype.keys||"function"!=typeof E.Map.prototype.forEach||"function"!=typeof E.Set.prototype.forEach||c(E.Map)||c(E.Set)||"function"!=typeof Dr.keys().next||Rr||!_r)&&b(E,{Map:vr.Map,Set:vr.Set},!0),E.Set.prototype.keys!==E.Set.prototype.values&&m(E.Set.prototype,"keys",E.Set.prototype.values,!0),Ie(Object.getPrototypeOf((new E.Map).keys())),Ie(Object.getPrototypeOf((new E.Set).keys())),h&&"has"!==E.Set.prototype.has.name){var Lr=E.Set.prototype.has;oe(E.Set.prototype,"has",(function(e){return n(Lr,this,e)}))}}b(E,vr),ke(E.Map),ke(E.Set)}var $r=function(e){if(!he.TypeIsObject(e))throw new TypeError("target must be an object")},Fr={apply:function(){return he.Call(he.Call,null,arguments)},construct:function(e,t){if(!he.IsConstructor(e))throw new TypeError("First argument must be a constructor.");var n=arguments.length>2?arguments[2]:e;if(!he.IsConstructor(n))throw new TypeError("new.target must be a constructor.");return he.Construct(e,t,n,"internal")},deleteProperty:function(e,t){if($r(e),f){var n=Object.getOwnPropertyDescriptor(e,t);if(n&&!n.configurable)return!1}return delete e[t]},has:function(e,t){return $r(e),t in e}};Object.getOwnPropertyNames&&Object.assign(Fr,{ownKeys:function(e){$r(e);var t=Object.getOwnPropertyNames(e);return he.IsCallable(Object.getOwnPropertySymbols)&&P(t,Object.getOwnPropertySymbols(e)),t}});var qr=function(e){return!u(e)};if(Object.preventExtensions&&Object.assign(Fr,{isExtensible:function(e){return $r(e),Object.isExtensible(e)},preventExtensions:function(e){return $r(e),qr((function(){return Object.preventExtensions(e)}))}}),f){var Hr=function(e,t,n){var r=Object.getOwnPropertyDescriptor(e,t);if(!r){var i=Object.getPrototypeOf(e);if(null===i)return;return Hr(i,t,n)}return"value"in r?r.value:r.get?he.Call(r.get,n):void 0},Br=function(e,t,r,i){var o=Object.getOwnPropertyDescriptor(e,t);if(!o){var s=Object.getPrototypeOf(e);if(null!==s)return Br(s,t,r,i);o={value:void 0,writable:!0,enumerable:!0,configurable:!0}}return"value"in o?!!o.writable&&!!he.TypeIsObject(i)&&(Object.getOwnPropertyDescriptor(i,t)?le.defineProperty(i,t,{value:r}):le.defineProperty(i,t,{value:r,writable:!0,enumerable:!0,configurable:!0})):!!o.set&&(n(o.set,i,r),!0)};Object.assign(Fr,{defineProperty:function(e,t,n){return $r(e),qr((function(){return Object.defineProperty(e,t,n)}))},getOwnPropertyDescriptor:function(e,t){return $r(e),Object.getOwnPropertyDescriptor(e,t)},get:function(e,t){$r(e);var n=arguments.length>2?arguments[2]:e;return Hr(e,t,n)},set:function(e,t,n){$r(e);var r=arguments.length>3?arguments[3]:e;return Br(e,t,n,r)}})}if(Object.getPrototypeOf){var zr=Object.getPrototypeOf;Fr.getPrototypeOf=function(e){return $r(e),zr(e)}}if(Object.setPrototypeOf&&Fr.getPrototypeOf){var Wr=function(e,t){for(var n=t;n;){if(e===n)return!0;n=Fr.getPrototypeOf(n)}return!1};Object.assign(Fr,{setPrototypeOf:function(e,t){if($r(e),null!==t&&!he.TypeIsObject(t))throw new TypeError("proto must be an object or null");return t===le.getPrototypeOf(e)||!(le.isExtensible&&!le.isExtensible(e))&&!Wr(e,t)&&(Object.setPrototypeOf(e,t),!0)}})}var Ur=function(e,t){he.IsCallable(E.Reflect[e])?l((function(){return E.Reflect[e](1),E.Reflect[e](NaN),E.Reflect[e](!0),!0}))&&oe(E.Reflect,e,t):m(E.Reflect,e,t)};Object.keys(Fr).forEach((function(e){Ur(e,Fr[e])}));var Vr=E.Reflect.getPrototypeOf;if(h&&Vr&&"getPrototypeOf"!==Vr.name&&oe(E.Reflect,"getPrototypeOf",(function(e){return n(Vr,E.Reflect,e)})),E.Reflect.setPrototypeOf&&l((function(){return E.Reflect.setPrototypeOf(1,{}),!0}))&&oe(E.Reflect,"setPrototypeOf",Fr.setPrototypeOf),E.Reflect.defineProperty&&(l((function(){var e=!E.Reflect.defineProperty(1,"test",{value:1}),t="function"!=typeof Object.preventExtensions||!E.Reflect.defineProperty(Object.preventExtensions({}),"test",{});return e&&t}))||oe(E.Reflect,"defineProperty",Fr.defineProperty)),E.Reflect.construct&&(l((function(){var e=function(){};return E.Reflect.construct((function(){}),[],e)instanceof e}))||oe(E.Reflect,"construct",Fr.construct)),"Invalid Date"!==String(new Date(NaN))){var Gr=Date.prototype.toString,Kr=function(){var e=+this;return e!=e?"Invalid Date":he.Call(Gr,this)};oe(Date.prototype,"toString",Kr)}var Xr={anchor:function(e){return he.CreateHTML(this,"a","name",e)},big:function(){return he.CreateHTML(this,"big","","")},blink:function(){return he.CreateHTML(this,"blink","","")},bold:function(){return he.CreateHTML(this,"b","","")},fixed:function(){return he.CreateHTML(this,"tt","","")},fontcolor:function(e){return he.CreateHTML(this,"font","color",e)},fontsize:function(e){return he.CreateHTML(this,"font","size",e)},italics:function(){return he.CreateHTML(this,"i","","")},link:function(e){return he.CreateHTML(this,"a","href",e)},small:function(){return he.CreateHTML(this,"small","","")},strike:function(){return he.CreateHTML(this,"strike","","")},sub:function(){return he.CreateHTML(this,"sub","","")},sup:function(){return he.CreateHTML(this,"sup","","")}};d(Object.keys(Xr),(function(e){var t=String.prototype[e],r=!1;if(he.IsCallable(t)){var i=n(t,"",' " '),o=A([],i.match(/"/g)).length;r=i!==i.toLowerCase()||o>2}else r=!0;r&&oe(String.prototype,e,Xr[e])}));var Qr=function(){if(!se)return!1;var e="object"==typeof JSON&&"function"==typeof JSON.stringify?JSON.stringify:null;if(!e)return!1;if(void 0!==e(X()))return!0;if("[null]"!==e([X()]))return!0;var t={a:X()};return t[X()]=!0,"{}"!==e(t)}(),Yr=l((function(){return!se||"{}"===JSON.stringify(Object(X()))&&"[{}]"===JSON.stringify([Object(X())])}));if(Qr||!Yr){var Jr=JSON.stringify;oe(JSON,"stringify",(function(e){if("symbol"!=typeof e){var t;arguments.length>1&&(t=arguments[1]);var r=[e];if(o(t))r.push(t);else{var i=he.IsCallable(t)?t:null,s=function(e,t){var r=i?n(i,this,e,t):t;if("symbol"!=typeof r)return ie.symbol(r)?$t({})(r):r};r.push(s)}return arguments.length>2&&r.push(arguments[2]),Jr.apply(this,r)}}))}return E})?o.call(t,n,t,e):o)||(e.exports=s)}).call(this,n(1),n(12))},function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var u,l=[],c=!1,p=-1;function f(){c&&u&&(c=!1,u.length?l=u.concat(l):p=-1,l.length&&h())}function h(){if(!c){var e=a(f);c=!0;for(var t=l.length;t;){for(u=l,l=[];++p<t;)u&&u[p].run();p=-1,t=l.length}u=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new d(e,t)),1!==l.length||c||a(h)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){var r,i,o;i=[n(0),n(14),n(15)],void 0===(o="function"==typeof(r=function(e,t,n){"use strict";var r=function(e,t){if("string"!=typeof t||t.length){var n="string"==typeof t?new RegExp(t,"i"):t,r=function(e){var t=0;if(3===e.nodeType){var i=e.data.search(n);if(i>=0&&e.data.length>0){var o=e.data.match(n),s=document.createElement("span");s.className="highlight";var a=e.splitText(i),u=(a.splitText(o[0].length),a.cloneNode(!0));s.appendChild(u),a.parentNode.replaceChild(s,a),t=1}}else if(1===e.nodeType&&e.childNodes&&!/(script|style)/i.test(e.tagName)&&("highlight"!==e.className||"SPAN"!==e.tagName))for(var l=0;l<e.childNodes.length;++l)l+=r(e.childNodes[l]);return t};return e.each((function(){r(this)}))}};e.fn.removeHighlight=function(){return this.find("span.highlight").each((function(){this.parentNode.firstChild.nodeName;var e=this.parentNode;e.replaceChild(this.firstChild,this),e.normalize()})).end()};var i=function(){};i.prototype={on:function(e,t){this._events=this._events||{},this._events[e]=this._events[e]||[],this._events[e].push(t)},off:function(e,t){var n=arguments.length;return 0===n?delete this._events:1===n?delete this._events[e]:(this._events=this._events||{},void(e in this._events!=0&&this._events[e].splice(this._events[e].indexOf(t),1)))},trigger:function(e){if(this._events=this._events||{},e in this._events!=0)for(var t=0;t<this._events[e].length;t++)this._events[e][t].apply(this,Array.prototype.slice.call(arguments,1))}},i.mixin=function(e){for(var t=["on","off","trigger"],n=0;n<t.length;n++)e.prototype[t[n]]=i.prototype[t[n]]};var o,s,a=/Mac/.test(navigator.userAgent),u=a?91:17,l=a?18:17,c=!/android/i.test(window.navigator.userAgent)&&!!document.createElement("input").validity,p=function(e){return void 0!==e},f=function(e){return null==e?null:"boolean"==typeof e?e?"1":"0":e+""},h=function(e){return(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")},d={before:function(e,t,n){var r=e[t];e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)}},after:function(e,t,n){var r=e[t];e[t]=function(){var t=r.apply(e,arguments);return n.apply(e,arguments),t}}},g=function(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}},v=function(e,t,n){var r,i=e.trigger,o={};for(r in e.trigger=function(){var n=arguments[0];if(-1===t.indexOf(n))return i.apply(e,arguments);o[n]=arguments},n.apply(e,[]),e.trigger=i,o)o.hasOwnProperty(r)&&i.apply(e,o[r])},y=function(e){var t={};if("selectionStart"in e)t.start=e.selectionStart,t.length=e.selectionEnd-t.start;else if(document.selection){e.focus();var n=document.selection.createRange(),r=document.selection.createRange().text.length;n.moveStart("character",-e.value.length),t.start=n.text.length-r,t.length=r}return t},m=function(n,r){var i,o,s,a;(a=n[0]).selectize=this;var u,l,c,p=window.getComputedStyle&&window.getComputedStyle(a,null);if(s=(s=p?p.getPropertyValue("direction"):a.currentStyle&&a.currentStyle.direction)||n.parents("[dir]:first").attr("dir")||"",e.extend(this,{order:0,settings:r,$input:n,tabIndex:n.attr("tabindex")||"",tagType:"select"===a.tagName.toLowerCase()?1:2,rtl:/rtl/i.test(s),eventNS:".selectize"+ ++m.count,highlightedValue:null,isBlurring:!1,isOpen:!1,isDisabled:!1,isRequired:n.is("[required]"),isInvalid:!1,isLocked:!1,isFocused:!1,isInputHidden:!1,isSetup:!1,isShiftDown:!1,isCmdDown:!1,isCtrlDown:!1,ignoreFocus:!1,ignoreBlur:!1,ignoreHover:!1,hasOptions:!1,currentResults:null,lastValue:"",caretPos:0,loading:0,loadedSearches:{},$activeOption:null,$activeItems:[],optgroups:{},options:{},userOptions:{},items:[],renderCache:{},onSearchChange:null===r.loadThrottle?this.onSearchChange:(u=this.onSearchChange,l=r.loadThrottle,function(){var e=this,t=arguments;window.clearTimeout(c),c=window.setTimeout((function(){u.apply(e,t)}),l)})}),this.sifter=new t(this.options,{diacritics:r.diacritics}),this.settings.options){for(i=0,o=this.settings.options.length;i<o;i++)this.registerOption(this.settings.options[i]);delete this.settings.options}if(this.settings.optgroups){for(i=0,o=this.settings.optgroups.length;i<o;i++)this.registerOptionGroup(this.settings.optgroups[i]);delete this.settings.optgroups}this.settings.mode=this.settings.mode||(1===this.settings.maxItems?"single":"multi"),"boolean"!=typeof this.settings.hideSelected&&(this.settings.hideSelected="multi"===this.settings.mode),this.initializePlugins(this.settings.plugins),this.setupCallbacks(),this.setupTemplates(),this.setup()};return i.mixin(m),void 0!==n?n.mixin(m):(o="Dependency MicroPlugin is missing",(s={explanation:'Make sure you either: (1) are using the "standalone" version of Selectize, or (2) require MicroPlugin before you load Selectize.'})||(s={}),console.error("Selectize: "+o),s.explanation&&(console.group&&console.group(),console.error(s.explanation),console.group&&console.groupEnd())),e.extend(m.prototype,{setup:function(){var t,n,r,i,o,s,p,f,h,d,g,v,b,w,x=this,O=x.settings,C=x.eventNS,T=e(window),S=e(document),E=x.$input;if(p=x.settings.mode,f=E.attr("class")||"",t=e("<div>").addClass(O.wrapperClass).addClass(f).addClass(p),n=e("<div>").addClass(O.inputClass).addClass("items").appendTo(t),r=e('<input type="text" autocomplete="off" />').appendTo(n).attr("tabindex",E.is(":disabled")?"-1":x.tabIndex),s=e(O.dropdownParent||t),i=e("<div>").addClass(O.dropdownClass).addClass(p).hide().appendTo(s),o=e("<div>").addClass(O.dropdownContentClass).appendTo(i),(d=E.attr("id"))&&(r.attr("id",d+"-selectized"),e("label[for='"+d+"']").attr("for",d+"-selectized")),x.settings.copyClassesToDropdown&&i.addClass(f),t.css({width:E[0].style.width}),x.plugins.names.length&&(h="plugin-"+x.plugins.names.join(" plugin-"),t.addClass(h),i.addClass(h)),(null===O.maxItems||O.maxItems>1)&&1===x.tagType&&E.attr("multiple","multiple"),x.settings.placeholder&&r.attr("placeholder",O.placeholder),!x.settings.splitOn&&x.settings.delimiter){var j=x.settings.delimiter.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&");x.settings.splitOn=new RegExp("\\s*"+j+"+\\s*")}E.attr("autocorrect")&&r.attr("autocorrect",E.attr("autocorrect")),E.attr("autocapitalize")&&r.attr("autocapitalize",E.attr("autocapitalize")),r[0].type=E[0].type,x.$wrapper=t,x.$control=n,x.$control_input=r,x.$dropdown=i,x.$dropdown_content=o,i.on("mouseenter mousedown click","[data-disabled]>[data-selectable]",(function(e){e.stopImmediatePropagation()})),i.on("mouseenter","[data-selectable]",(function(){return x.onOptionHover.apply(x,arguments)})),i.on("mousedown click","[data-selectable]",(function(){return x.onOptionSelect.apply(x,arguments)})),v="mousedown",b="*:not(input)",w=function(){return x.onItemSelect.apply(x,arguments)},(g=n).on(v,b,(function(e){for(var t=e.target;t&&t.parentNode!==g[0];)t=t.parentNode;return e.currentTarget=t,w.apply(this,[e])})),function(t){var n=null,r=function(r,i){var o,s,a,u,l,c,p,f;i=i||{},(r=r||window.event||{}).metaKey||r.altKey||(i.force||!1!==t.data("grow"))&&(o=t.val(),r.type&&"keydown"===r.type.toLowerCase()&&(a=(s=r.keyCode)>=48&&s<=57||s>=65&&s<=90||s>=96&&s<=111||s>=186&&s<=222||32===s,46===s||8===s?(f=y(t[0])).length?o=o.substring(0,f.start)+o.substring(f.start+f.length):8===s&&f.start?o=o.substring(0,f.start-1)+o.substring(f.start+1):46===s&&void 0!==f.start&&(o=o.substring(0,f.start)+o.substring(f.start+1)):a&&(c=r.shiftKey,p=String.fromCharCode(r.keyCode),o+=p=c?p.toUpperCase():p.toLowerCase())),u=t.attr("placeholder"),!o&&u&&(o=u),(l=function(t,n){return t?(m.$testInput||(m.$testInput=e("<span />").css({position:"absolute",top:-99999,left:-99999,width:"auto",padding:0,whiteSpace:"pre"}).appendTo("body")),m.$testInput.text(t),function(e,t,n){var r,i,o={};if(n)for(r=0,i=n.length;r<i;r++)o[n[r]]=e.css(n[r]);else o=e.css();t.css(o)}(n,m.$testInput,["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"]),m.$testInput.width()):0}(o,t)+4)!==n&&(n=l,t.width(l),t.triggerHandler("resize")))};t.on("keydown keyup update blur",r),r()}(r),n.on({mousedown:function(){return x.onMouseDown.apply(x,arguments)},click:function(){return x.onClick.apply(x,arguments)}}),r.on({mousedown:function(e){e.stopPropagation()},keydown:function(){return x.onKeyDown.apply(x,arguments)},keyup:function(){return x.onKeyUp.apply(x,arguments)},keypress:function(){return x.onKeyPress.apply(x,arguments)},resize:function(){x.positionDropdown.apply(x,[])},blur:function(){return x.onBlur.apply(x,arguments)},focus:function(){return x.ignoreBlur=!1,x.onFocus.apply(x,arguments)},paste:function(){return x.onPaste.apply(x,arguments)}}),S.on("keydown"+C,(function(e){x.isCmdDown=e[a?"metaKey":"ctrlKey"],x.isCtrlDown=e[a?"altKey":"ctrlKey"],x.isShiftDown=e.shiftKey})),S.on("keyup"+C,(function(e){e.keyCode===l&&(x.isCtrlDown=!1),16===e.keyCode&&(x.isShiftDown=!1),e.keyCode===u&&(x.isCmdDown=!1)})),S.on("mousedown"+C,(function(e){if(x.isFocused){if(e.target===x.$dropdown[0]||e.target.parentNode===x.$dropdown[0])return!1;x.$control.has(e.target).length||e.target===x.$control[0]||x.blur(e.target)}})),T.on(["scroll"+C,"resize"+C].join(" "),(function(){x.isOpen&&x.positionDropdown.apply(x,arguments)})),T.on("mousemove"+C,(function(){x.ignoreHover=!1})),this.revertSettings={$children:E.children().detach(),tabindex:E.attr("tabindex")},E.attr("tabindex",-1).hide().after(x.$wrapper),e.isArray(O.items)&&(x.setValue(O.items),delete O.items),c&&E.on("invalid"+C,(function(e){e.preventDefault(),x.isInvalid=!0,x.refreshState()})),x.updateOriginalInput(),x.refreshItems(),x.refreshState(),x.updatePlaceholder(),x.isSetup=!0,E.is(":disabled")&&x.disable(),x.on("change",this.onChange),E.data("selectize",x),E.addClass("selectized"),x.trigger("initialize"),!0===O.preload&&x.onSearchChange("")},setupTemplates:function(){var t=this.settings.labelField,n=this.settings.optgroupLabelField,r={optgroup:function(e){return'<div class="optgroup">'+e.html+"</div>"},optgroup_header:function(e,t){return'<div class="optgroup-header">'+t(e[n])+"</div>"},option:function(e,n){return'<div class="option">'+n(e[t])+"</div>"},item:function(e,n){return'<div class="item">'+n(e[t])+"</div>"},option_create:function(e,t){return'<div class="create">Add <strong>'+t(e.input)+"</strong>&hellip;</div>"}};this.settings.render=e.extend({},r,this.settings.render)},setupCallbacks:function(){var e,t,n={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(e in n)n.hasOwnProperty(e)&&(t=this.settings[n[e]])&&this.on(e,t)},onClick:function(e){this.isFocused&&this.isOpen||(this.focus(),e.preventDefault())},onMouseDown:function(t){var n=this,r=t.isDefaultPrevented();if(e(t.target),n.isFocused){if(t.target!==n.$control_input[0])return"single"===n.settings.mode?n.isOpen?n.close():n.open():r||n.setActiveItem(null),!1}else r||window.setTimeout((function(){n.focus()}),0)},onChange:function(){this.$input.trigger("change")},onPaste:function(t){var n=this;n.isFull()||n.isInputHidden||n.isLocked?t.preventDefault():n.settings.splitOn&&setTimeout((function(){var t=n.$control_input.val();if(t.match(n.settings.splitOn))for(var r=e.trim(t).split(n.settings.splitOn),i=0,o=r.length;i<o;i++)n.createItem(r[i])}),0)},onKeyPress:function(e){if(this.isLocked)return e&&e.preventDefault();var t=String.fromCharCode(e.keyCode||e.which);return this.settings.create&&"multi"===this.settings.mode&&t===this.settings.delimiter?(this.createItem(),e.preventDefault(),!1):void 0},onKeyDown:function(e){if(e.target,this.$control_input[0],this.isLocked)9!==e.keyCode&&e.preventDefault();else{switch(e.keyCode){case 65:if(this.isCmdDown)return void this.selectAll();break;case 27:return void(this.isOpen&&(e.preventDefault(),e.stopPropagation(),this.close()));case 78:if(!e.ctrlKey||e.altKey)break;case 40:if(!this.isOpen&&this.hasOptions)this.open();else if(this.$activeOption){this.ignoreHover=!0;var t=this.getAdjacentOption(this.$activeOption,1);t.length&&this.setActiveOption(t,!0,!0)}return void e.preventDefault();case 80:if(!e.ctrlKey||e.altKey)break;case 38:if(this.$activeOption){this.ignoreHover=!0;var n=this.getAdjacentOption(this.$activeOption,-1);n.length&&this.setActiveOption(n,!0,!0)}return void e.preventDefault();case 13:return void(this.isOpen&&this.$activeOption&&(this.onOptionSelect({currentTarget:this.$activeOption}),e.preventDefault()));case 37:return void this.advanceSelection(-1,e);case 39:return void this.advanceSelection(1,e);case 9:return this.settings.selectOnTab&&this.isOpen&&this.$activeOption&&(this.onOptionSelect({currentTarget:this.$activeOption}),this.isFull()||e.preventDefault()),void(this.settings.create&&this.createItem()&&e.preventDefault());case 8:case 46:return void this.deleteSelection(e)}!this.isFull()&&!this.isInputHidden||(a?e.metaKey:e.ctrlKey)||e.preventDefault()}},onKeyUp:function(e){if(this.isLocked)return e&&e.preventDefault();var t=this.$control_input.val()||"";this.lastValue!==t&&(this.lastValue=t,this.onSearchChange(t),this.refreshOptions(),this.trigger("type",t))},onSearchChange:function(e){var t=this,n=t.settings.load;n&&(t.loadedSearches.hasOwnProperty(e)||(t.loadedSearches[e]=!0,t.load((function(r){n.apply(t,[e,r])}))))},onFocus:function(e){var t=this.isFocused;if(this.isDisabled)return this.blur(),e&&e.preventDefault(),!1;this.ignoreFocus||(this.isFocused=!0,"focus"===this.settings.preload&&this.onSearchChange(""),t||this.trigger("focus"),this.$activeItems.length||(this.showInput(),this.setActiveItem(null),this.refreshOptions(!!this.settings.openOnFocus)),this.refreshState())},onBlur:function(e,t){var n=this;if(n.isFocused&&(n.isFocused=!1,!n.ignoreFocus)){if(!n.ignoreBlur&&document.activeElement===n.$dropdown_content[0])return n.ignoreBlur=!0,void n.onFocus(e);var r=function(){n.close(),n.setTextboxValue(""),n.setActiveItem(null),n.setActiveOption(null),n.setCaret(n.items.length),n.refreshState(),t&&t.focus&&t.focus(),n.isBlurring=!1,n.ignoreFocus=!1,n.trigger("blur")};n.isBlurring=!0,n.ignoreFocus=!0,n.settings.create&&n.settings.createOnBlur?n.createItem(null,!1,r):r()}},onOptionHover:function(e){this.ignoreHover||this.setActiveOption(e.currentTarget,!1)},onOptionSelect:function(t){var n,r,i=this;t.preventDefault&&(t.preventDefault(),t.stopPropagation()),(r=e(t.currentTarget)).hasClass("create")?i.createItem(null,(function(){i.settings.closeAfterSelect&&i.close()})):void 0!==(n=r.attr("data-value"))&&(i.lastQuery=null,i.setTextboxValue(""),i.addItem(n),i.settings.closeAfterSelect?i.close():!i.settings.hideSelected&&t.type&&/mouse/.test(t.type)&&i.setActiveOption(i.getOption(n)))},onItemSelect:function(e){this.isLocked||"multi"===this.settings.mode&&(e.preventDefault(),this.setActiveItem(e.currentTarget,e))},load:function(e){var t=this,n=t.$wrapper.addClass(t.settings.loadingClass);t.loading++,e.apply(t,[function(e){t.loading=Math.max(t.loading-1,0),e&&e.length&&(t.addOption(e),t.refreshOptions(t.isFocused&&!t.isInputHidden)),t.loading||n.removeClass(t.settings.loadingClass),t.trigger("load",e)}])},setTextboxValue:function(e){var t=this.$control_input;t.val()!==e&&(t.val(e).triggerHandler("update"),this.lastValue=e)},getValue:function(){return 1===this.tagType&&this.$input.attr("multiple")?this.items:this.items.join(this.settings.delimiter)},setValue:function(e,t){v(this,t?[]:["change"],(function(){this.clear(t),this.addItems(e,t)}))},setActiveItem:function(t,n){var r,i,o,s,a,u,l,c;if("single"!==this.settings.mode){if(!(t=e(t)).length)return e(this.$activeItems).removeClass("active"),this.$activeItems=[],void(this.isFocused&&this.showInput());if("mousedown"===(r=n&&n.type.toLowerCase())&&this.isShiftDown&&this.$activeItems.length){for(c=this.$control.children(".active:last"),(s=Array.prototype.indexOf.apply(this.$control[0].childNodes,[c[0]]))>(a=Array.prototype.indexOf.apply(this.$control[0].childNodes,[t[0]]))&&(l=s,s=a,a=l),i=s;i<=a;i++)u=this.$control[0].childNodes[i],-1===this.$activeItems.indexOf(u)&&(e(u).addClass("active"),this.$activeItems.push(u));n.preventDefault()}else"mousedown"===r&&this.isCtrlDown||"keydown"===r&&this.isShiftDown?t.hasClass("active")?(o=this.$activeItems.indexOf(t[0]),this.$activeItems.splice(o,1),t.removeClass("active")):this.$activeItems.push(t.addClass("active")[0]):(e(this.$activeItems).removeClass("active"),this.$activeItems=[t.addClass("active")[0]]);this.hideInput(),this.isFocused||this.focus()}},setActiveOption:function(t,n,r){var i,o,s,a,u;this.$activeOption&&this.$activeOption.removeClass("active"),this.$activeOption=null,(t=e(t)).length&&(this.$activeOption=t.addClass("active"),!n&&p(n)||(i=this.$dropdown_content.height(),o=this.$activeOption.outerHeight(!0),n=this.$dropdown_content.scrollTop()||0,a=s=this.$activeOption.offset().top-this.$dropdown_content.offset().top+n,u=s-i+o,s+o>i+n?this.$dropdown_content.stop().animate({scrollTop:u},r?this.settings.scrollDuration:0):s<n&&this.$dropdown_content.stop().animate({scrollTop:a},r?this.settings.scrollDuration:0)))},selectAll:function(){"single"!==this.settings.mode&&(this.$activeItems=Array.prototype.slice.apply(this.$control.children(":not(input)").addClass("active")),this.$activeItems.length&&(this.hideInput(),this.close()),this.focus())},hideInput:function(){this.setTextboxValue(""),this.$control_input.css({opacity:0,position:"absolute",left:this.rtl?1e4:-1e4}),this.isInputHidden=!0},showInput:function(){this.$control_input.css({opacity:1,position:"relative",left:0}),this.isInputHidden=!1},focus:function(){var e=this;e.isDisabled||(e.ignoreFocus=!0,e.$control_input[0].focus(),window.setTimeout((function(){e.ignoreFocus=!1,e.onFocus()}),0))},blur:function(e){this.$control_input[0].blur(),this.onBlur(null,e)},getScoreFunction:function(e){return this.sifter.getScoreFunction(e,this.getSearchOptions())},getSearchOptions:function(){var e=this.settings,t=e.sortField;return"string"==typeof t&&(t=[{field:t}]),{fields:e.searchField,conjunction:e.searchConjunction,sort:t,nesting:e.nesting}},search:function(t){var n,r,i,o=this.settings,s=this.getSearchOptions();if(o.score&&"function"!=typeof(i=this.settings.score.apply(this,[t])))throw new Error('Selectize "score" setting must be a function that returns a function');if(t!==this.lastQuery?(this.lastQuery=t,r=this.sifter.search(t,e.extend(s,{score:i})),this.currentResults=r):r=e.extend(!0,{},this.currentResults),o.hideSelected)for(n=r.items.length-1;n>=0;n--)-1!==this.items.indexOf(f(r.items[n].id))&&r.items.splice(n,1);return r},refreshOptions:function(t){var n,i,o,s,a,u,l,c,p,h,d,g,v,y,m,b;void 0===t&&(t=!0);var w,x,O=this,C=e.trim(O.$control_input.val()),T=O.search(C),S=O.$dropdown_content,E=O.$activeOption&&f(O.$activeOption.attr("data-value"));for(s=T.items.length,"number"==typeof O.settings.maxOptions&&(s=Math.min(s,O.settings.maxOptions)),a={},u=[],n=0;n<s;n++)for(l=O.options[T.items[n].id],c=O.render("option",l),p=l[O.settings.optgroupField]||"",i=0,o=(h=e.isArray(p)?p:[p])&&h.length;i<o;i++)p=h[i],O.optgroups.hasOwnProperty(p)||(p=""),a.hasOwnProperty(p)||(a[p]=document.createDocumentFragment(),u.push(p)),a[p].appendChild(c);for(this.settings.lockOptgroupOrder&&u.sort((function(e,t){return(O.optgroups[e].$order||0)-(O.optgroups[t].$order||0)})),d=document.createDocumentFragment(),n=0,s=u.length;n<s;n++)p=u[n],O.optgroups.hasOwnProperty(p)&&a[p].childNodes.length?((g=document.createDocumentFragment()).appendChild(O.render("optgroup_header",O.optgroups[p])),g.appendChild(a[p]),d.appendChild(O.render("optgroup",e.extend({},O.optgroups[p],{html:(w=g,x=void 0,x=document.createElement("div"),x.appendChild(w.cloneNode(!0)),x.innerHTML),dom:g})))):d.appendChild(a[p]);if(S.html(d),O.settings.highlight&&(S.removeHighlight(),T.query.length&&T.tokens.length))for(n=0,s=T.tokens.length;n<s;n++)r(S,T.tokens[n].regex);if(!O.settings.hideSelected)for(n=0,s=O.items.length;n<s;n++)O.getOption(O.items[n]).addClass("selected");(v=O.canCreate(C))&&(S.prepend(O.render("option_create",{input:C})),b=e(S[0].childNodes[0])),O.hasOptions=T.items.length>0||v,O.hasOptions?(T.items.length>0?((m=E&&O.getOption(E))&&m.length?y=m:"single"===O.settings.mode&&O.items.length&&(y=O.getOption(O.items[0])),y&&y.length||(y=b&&!O.settings.addPrecedence?O.getAdjacentOption(b,1):S.find("[data-selectable]:first"))):y=b,O.setActiveOption(y),t&&!O.isOpen&&O.open()):(O.setActiveOption(null),t&&O.isOpen&&O.close())},addOption:function(t){var n,r,i;if(e.isArray(t))for(n=0,r=t.length;n<r;n++)this.addOption(t[n]);else(i=this.registerOption(t))&&(this.userOptions[i]=!0,this.lastQuery=null,this.trigger("option_add",i,t))},registerOption:function(e){var t=f(e[this.settings.valueField]);return null!=t&&!this.options.hasOwnProperty(t)&&(e.$order=e.$order||++this.order,this.options[t]=e,t)},registerOptionGroup:function(e){var t=f(e[this.settings.optgroupValueField]);return!!t&&(e.$order=e.$order||++this.order,this.optgroups[t]=e,t)},addOptionGroup:function(e,t){t[this.settings.optgroupValueField]=e,(e=this.registerOptionGroup(t))&&this.trigger("optgroup_add",e,t)},removeOptionGroup:function(e){this.optgroups.hasOwnProperty(e)&&(delete this.optgroups[e],this.renderCache={},this.trigger("optgroup_remove",e))},clearOptionGroups:function(){this.optgroups={},this.renderCache={},this.trigger("optgroup_clear")},updateOption:function(t,n){var r,i,o,s,a,u,l;if(t=f(t),o=f(n[this.settings.valueField]),null!==t&&this.options.hasOwnProperty(t)){if("string"!=typeof o)throw new Error("Value must be set in option data");l=this.options[t].$order,o!==t&&(delete this.options[t],-1!==(s=this.items.indexOf(t))&&this.items.splice(s,1,o)),n.$order=n.$order||l,this.options[o]=n,a=this.renderCache.item,u=this.renderCache.option,a&&(delete a[t],delete a[o]),u&&(delete u[t],delete u[o]),-1!==this.items.indexOf(o)&&(r=this.getItem(t),i=e(this.render("item",n)),r.hasClass("active")&&i.addClass("active"),r.replaceWith(i)),this.lastQuery=null,this.isOpen&&this.refreshOptions(!1)}},removeOption:function(e,t){e=f(e);var n=this.renderCache.item,r=this.renderCache.option;n&&delete n[e],r&&delete r[e],delete this.userOptions[e],delete this.options[e],this.lastQuery=null,this.trigger("option_remove",e),this.removeItem(e,t)},clearOptions:function(){var t=this;t.loadedSearches={},t.userOptions={},t.renderCache={};var n=t.options;e.each(t.options,(function(e,r){-1==t.items.indexOf(e)&&delete n[e]})),t.options=t.sifter.items=n,t.lastQuery=null,t.trigger("option_clear")},getOption:function(e){return this.getElementWithValue(e,this.$dropdown_content.find("[data-selectable]"))},getAdjacentOption:function(t,n){var r=this.$dropdown.find("[data-selectable]"),i=r.index(t)+n;return i>=0&&i<r.length?r.eq(i):e()},getElementWithValue:function(t,n){if(null!=(t=f(t)))for(var r=0,i=n.length;r<i;r++)if(n[r].getAttribute("data-value")===t)return e(n[r]);return e()},getItem:function(e){return this.getElementWithValue(e,this.$control.children())},addItems:function(t,n){this.buffer=document.createDocumentFragment();for(var r=this.$control[0].childNodes,i=0;i<r.length;i++)this.buffer.appendChild(r[i]);for(var o=e.isArray(t)?t:[t],s=(i=0,o.length);i<s;i++)this.isPending=i<s-1,this.addItem(o[i],n);var a=this.$control[0];a.insertBefore(this.buffer,a.firstChild),this.buffer=null},addItem:function(t,n){v(this,n?[]:["change"],(function(){var r,i,o,s,a,u=this.settings.mode;t=f(t),-1===this.items.indexOf(t)?this.options.hasOwnProperty(t)&&("single"===u&&this.clear(n),"multi"===u&&this.isFull()||(r=e(this.render("item",this.options[t])),a=this.isFull(),this.items.splice(this.caretPos,0,t),this.insertAtCaret(r),(!this.isPending||!a&&this.isFull())&&this.refreshState(),this.isSetup&&(o=this.$dropdown_content.find("[data-selectable]"),this.isPending||(i=this.getOption(t),s=this.getAdjacentOption(i,1).attr("data-value"),this.refreshOptions(this.isFocused&&"single"!==u),s&&this.setActiveOption(this.getOption(s))),!o.length||this.isFull()?this.close():this.isPending||this.positionDropdown(),this.updatePlaceholder(),this.trigger("item_add",t,r),this.isPending||this.updateOriginalInput({silent:n})))):"single"===u&&this.close()}))},removeItem:function(t,n){var r,i,o;r=t instanceof e?t:this.getItem(t),t=f(r.attr("data-value")),-1!==(i=this.items.indexOf(t))&&(r.remove(),r.hasClass("active")&&(o=this.$activeItems.indexOf(r[0]),this.$activeItems.splice(o,1)),this.items.splice(i,1),this.lastQuery=null,!this.settings.persist&&this.userOptions.hasOwnProperty(t)&&this.removeOption(t,n),i<this.caretPos&&this.setCaret(this.caretPos-1),this.refreshState(),this.updatePlaceholder(),this.updateOriginalInput({silent:n}),this.positionDropdown(),this.trigger("item_remove",t,r))},createItem:function(t,n){var r=this,i=r.caretPos;t=t||e.trim(r.$control_input.val()||"");var o=arguments[arguments.length-1];if("function"!=typeof o&&(o=function(){}),"boolean"!=typeof n&&(n=!0),!r.canCreate(t))return o(),!1;r.lock();var s="function"==typeof r.settings.create?this.settings.create:function(e){var t={};return t[r.settings.labelField]=e,t[r.settings.valueField]=e,t},a=g((function(e){if(r.unlock(),!e||"object"!=typeof e)return o();var t=f(e[r.settings.valueField]);if("string"!=typeof t)return o();r.setTextboxValue(""),r.addOption(e),r.setCaret(i),r.addItem(t),r.refreshOptions(n&&"single"!==r.settings.mode),o(e)})),u=s.apply(this,[t,a]);return void 0!==u&&a(u),!0},refreshItems:function(){this.lastQuery=null,this.isSetup&&this.addItem(this.items),this.refreshState(),this.updateOriginalInput()},refreshState:function(){this.refreshValidityState(),this.refreshClasses()},refreshValidityState:function(){if(!this.isRequired)return!1;var e=!this.items.length;this.isInvalid=e,this.$control_input.prop("required",e),this.$input.prop("required",!e)},refreshClasses:function(){var t=this.isFull(),n=this.isLocked;this.$wrapper.toggleClass("rtl",this.rtl),this.$control.toggleClass("focus",this.isFocused).toggleClass("disabled",this.isDisabled).toggleClass("required",this.isRequired).toggleClass("invalid",this.isInvalid).toggleClass("locked",n).toggleClass("full",t).toggleClass("not-full",!t).toggleClass("input-active",this.isFocused&&!this.isInputHidden).toggleClass("dropdown-active",this.isOpen).toggleClass("has-options",!e.isEmptyObject(this.options)).toggleClass("has-items",this.items.length>0),this.$control_input.data("grow",!t&&!n)},isFull:function(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems},updateOriginalInput:function(e){var t,n,r,i;if(e=e||{},1===this.tagType){for(r=[],t=0,n=this.items.length;t<n;t++)i=this.options[this.items[t]][this.settings.labelField]||"",r.push('<option value="'+h(this.items[t])+'" selected="selected">'+h(i)+"</option>");r.length||this.$input.attr("multiple")||r.push('<option value="" selected="selected"></option>'),this.$input.html(r.join(""))}else this.$input.val(this.getValue()),this.$input.attr("value",this.$input.val());this.isSetup&&(e.silent||this.trigger("change",this.$input.val()))},updatePlaceholder:function(){if(this.settings.placeholder){var e=this.$control_input;this.items.length?e.removeAttr("placeholder"):e.attr("placeholder",this.settings.placeholder),e.triggerHandler("update",{force:!0})}},open:function(){this.isLocked||this.isOpen||"multi"===this.settings.mode&&this.isFull()||(this.focus(),this.isOpen=!0,this.refreshState(),this.$dropdown.css({visibility:"hidden",display:"block"}),this.positionDropdown(),this.$dropdown.css({visibility:"visible"}),this.trigger("dropdown_open",this.$dropdown))},close:function(){var e=this.isOpen;"single"===this.settings.mode&&this.items.length&&(this.hideInput(),this.isBlurring||this.$control_input.blur()),this.isOpen=!1,this.$dropdown.hide(),this.setActiveOption(null),this.refreshState(),e&&this.trigger("dropdown_close",this.$dropdown)},positionDropdown:function(){var e=this.$control,t="body"===this.settings.dropdownParent?e.offset():e.position();t.top+=e.outerHeight(!0),this.$dropdown.css({width:e[0].getBoundingClientRect().width,top:t.top,left:t.left})},clear:function(e){this.items.length&&(this.$control.children(":not(input)").remove(),this.items=[],this.lastQuery=null,this.setCaret(0),this.setActiveItem(null),this.updatePlaceholder(),this.updateOriginalInput({silent:e}),this.refreshState(),this.showInput(),this.trigger("clear"))},insertAtCaret:function(e){var t=Math.min(this.caretPos,this.items.length),n=e[0],r=this.buffer||this.$control[0];0===t?r.insertBefore(n,r.firstChild):r.insertBefore(n,r.childNodes[t]),this.setCaret(t+1)},deleteSelection:function(t){var n,r,i,o,s,a,u,l,c;if(i=t&&8===t.keyCode?-1:1,o=y(this.$control_input[0]),this.$activeOption&&!this.settings.hideSelected&&(u=this.getAdjacentOption(this.$activeOption,-1).attr("data-value")),s=[],this.$activeItems.length){for(c=this.$control.children(".active:"+(i>0?"last":"first")),a=this.$control.children(":not(input)").index(c),i>0&&a++,n=0,r=this.$activeItems.length;n<r;n++)s.push(e(this.$activeItems[n]).attr("data-value"));t&&(t.preventDefault(),t.stopPropagation())}else(this.isFocused||"single"===this.settings.mode)&&this.items.length&&(i<0&&0===o.start&&0===o.length?s.push(this.items[this.caretPos-1]):i>0&&o.start===this.$control_input.val().length&&s.push(this.items[this.caretPos]));if(!s.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete.apply(this,[s]))return!1;for(void 0!==a&&this.setCaret(a);s.length;)this.removeItem(s.pop());return this.showInput(),this.positionDropdown(),this.refreshOptions(!0),u&&(l=this.getOption(u)).length&&this.setActiveOption(l),!0},advanceSelection:function(e,t){var n,r,i,o,s;0!==e&&(this.rtl&&(e*=-1),n=e>0?"last":"first",r=y(this.$control_input[0]),this.isFocused&&!this.isInputHidden?(o=this.$control_input.val().length,(e<0?0===r.start&&0===r.length:r.start===o)&&!o&&this.advanceCaret(e,t)):(s=this.$control.children(".active:"+n)).length&&(i=this.$control.children(":not(input)").index(s),this.setActiveItem(null),this.setCaret(e>0?i+1:i)))},advanceCaret:function(e,t){var n,r;0!==e&&(n=e>0?"next":"prev",this.isShiftDown?(r=this.$control_input[n]()).length&&(this.hideInput(),this.setActiveItem(r),t&&t.preventDefault()):this.setCaret(this.caretPos+e))},setCaret:function(t){var n,r,i,o;if(t="single"===this.settings.mode?this.items.length:Math.max(0,Math.min(this.items.length,t)),!this.isPending)for(n=0,r=(i=this.$control.children(":not(input)")).length;n<r;n++)o=e(i[n]).detach(),n<t?this.$control_input.before(o):this.$control.append(o);this.caretPos=t},lock:function(){this.close(),this.isLocked=!0,this.refreshState()},unlock:function(){this.isLocked=!1,this.refreshState()},disable:function(){this.$input.prop("disabled",!0),this.$control_input.prop("disabled",!0).prop("tabindex",-1),this.isDisabled=!0,this.lock()},enable:function(){this.$input.prop("disabled",!1),this.$control_input.prop("disabled",!1).prop("tabindex",this.tabIndex),this.isDisabled=!1,this.unlock()},destroy:function(){var t=this.eventNS,n=this.revertSettings;this.trigger("destroy"),this.off(),this.$wrapper.remove(),this.$dropdown.remove(),this.$input.html("").append(n.$children).removeAttr("tabindex").removeClass("selectized").attr({tabindex:n.tabindex}).show(),this.$control_input.removeData("grow"),this.$input.removeData("selectize"),0==--m.count&&m.$testInput&&(m.$testInput.remove(),m.$testInput=void 0),e(window).off(t),e(document).off(t),e(document.body).off(t),delete this.$input[0].selectize},render:function(t,n){var r,i,o="",s=!1;return"option"!==t&&"item"!==t||(s=!!(r=f(n[this.settings.valueField]))),s&&(p(this.renderCache[t])||(this.renderCache[t]={}),this.renderCache[t].hasOwnProperty(r))?this.renderCache[t][r]:(o=e(this.settings.render[t].apply(this,[n,h])),"option"===t||"option_create"===t?n[this.settings.disabledField]||o.attr("data-selectable",""):"optgroup"===t&&(i=n[this.settings.optgroupValueField]||"",o.attr("data-group",i),n[this.settings.disabledField]&&o.attr("data-disabled","")),"option"!==t&&"item"!==t||o.attr("data-value",r||""),s&&(this.renderCache[t][r]=o[0]),o[0])},clearCache:function(e){void 0===e?this.renderCache={}:delete this.renderCache[e]},canCreate:function(e){if(!this.settings.create)return!1;var t=this.settings.createFilter;return e.length&&("function"!=typeof t||t.apply(this,[e]))&&("string"!=typeof t||new RegExp(t).test(e))&&(!(t instanceof RegExp)||t.test(e))}}),m.count=0,m.defaults={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:!1,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,maxOptions:1e3,maxItems:null,hideSelected:null,addPrecedence:!1,selectOnTab:!1,preload:!1,allowEmptyOption:!1,closeAfterSelect:!1,scrollDuration:60,loadThrottle:300,loadingClass:"loading",dataAttr:"data-data",optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"selectize-control",inputClass:"selectize-input",dropdownClass:"selectize-dropdown",dropdownContentClass:"selectize-dropdown-content",dropdownParent:null,copyClassesToDropdown:!0,render:{}},e.fn.selectize=function(t){var n=e.fn.selectize.defaults,r=e.extend({},n,t),i=r.dataAttr,o=r.labelField,s=r.valueField,a=r.disabledField,u=r.optgroupField,l=r.optgroupLabelField,c=r.optgroupValueField;return this.each((function(){if(!this.selectize){var p=e(this),h=this.tagName.toLowerCase(),d=p.attr("placeholder")||p.attr("data-placeholder");d||r.allowEmptyOption||(d=p.children('option[value=""]').text());var g={placeholder:d,options:[],optgroups:[],items:[]};"select"===h?function(t,n){var p,h,d,g,v=n.options,y={},m=function(e){var t=i&&e.attr(i);return"string"==typeof t&&t.length?JSON.parse(t):null},b=function(t,i){t=e(t);var l=f(t.val());if(l||r.allowEmptyOption)if(y.hasOwnProperty(l)){if(i){var c=y[l][u];c?e.isArray(c)?c.push(i):y[l][u]=[c,i]:y[l][u]=i}}else{var p=m(t)||{};p[o]=p[o]||t.text(),p[s]=p[s]||l,p[a]=p[a]||t.prop("disabled"),p[u]=p[u]||i,y[l]=p,v.push(p),t.is(":selected")&&n.items.push(l)}},w=function(t){var r,i,o,s,u;for((o=(t=e(t)).attr("label"))&&((s=m(t)||{})[l]=o,s[c]=o,s[a]=t.prop("disabled"),n.optgroups.push(s)),r=0,i=(u=e("option",t)).length;r<i;r++)b(u[r],o)};for(n.maxItems=t.attr("multiple")?null:1,p=0,h=(g=t.children()).length;p<h;p++)"optgroup"===(d=g[p].tagName.toLowerCase())?w(g[p]):"option"===d&&b(g[p])}(p,g):function(t,n){var a,u,l,c,p=t.attr(i);if(p)for(n.options=JSON.parse(p),a=0,u=n.options.length;a<u;a++)n.items.push(n.options[a][s]);else{var f=e.trim(t.val()||"");if(!r.allowEmptyOption&&!f.length)return;for(a=0,u=(l=f.split(r.delimiter)).length;a<u;a++)(c={})[o]=l[a],c[s]=l[a],n.options.push(c);n.items=l}}(p,g),new m(p,e.extend(!0,{},n,g,t))}}))},e.fn.selectize.defaults=m.defaults,e.fn.selectize.support={validity:c},m.define("drag_drop",(function(t){if(!e.fn.sortable)throw new Error('The "drag_drop" plugin requires jQuery UI "sortable".');if("multi"===this.settings.mode){var n,r=this;r.lock=(n=r.lock,function(){var e=r.$control.data("sortable");return e&&e.disable(),n.apply(r,arguments)}),r.unlock=function(){var e=r.unlock;return function(){var t=r.$control.data("sortable");return t&&t.enable(),e.apply(r,arguments)}}(),r.setup=function(){var t=r.setup;return function(){t.apply(this,arguments);var n=r.$control.sortable({items:"[data-value]",forcePlaceholderSize:!0,disabled:r.isLocked,start:function(e,t){t.placeholder.css("width",t.helper.css("width")),n.css({overflow:"visible"})},stop:function(){n.css({overflow:"hidden"});var t=r.$activeItems?r.$activeItems.slice():null,i=[];n.children("[data-value]").each((function(){i.push(e(this).attr("data-value"))})),r.setValue(i),r.setActiveItem(t)}})}}()}})),m.define("dropdown_header",(function(t){var n,r=this;t=e.extend({title:"Untitled",headerClass:"selectize-dropdown-header",titleRowClass:"selectize-dropdown-header-title",labelClass:"selectize-dropdown-header-label",closeClass:"selectize-dropdown-header-close",html:function(e){return'<div class="'+e.headerClass+'"><div class="'+e.titleRowClass+'"><span class="'+e.labelClass+'">'+e.title+'</span><a href="javascript:void(0)" class="'+e.closeClass+'">&times;</a></div></div>'}},t),r.setup=(n=r.setup,function(){n.apply(r,arguments),r.$dropdown_header=e(t.html(t)),r.$dropdown.prepend(r.$dropdown_header)})})),m.define("optgroup_columns",(function(t){var n,r=this;t=e.extend({equalizeWidth:!0,equalizeHeight:!0},t),this.getAdjacentOption=function(t,n){var r=t.closest("[data-group]").find("[data-selectable]"),i=r.index(t)+n;return i>=0&&i<r.length?r.eq(i):e()},this.onKeyDown=(n=r.onKeyDown,function(e){var t,i,o,s;return!this.isOpen||37!==e.keyCode&&39!==e.keyCode?n.apply(this,arguments):(r.ignoreHover=!0,t=(s=this.$activeOption.closest("[data-group]")).find("[data-selectable]").index(this.$activeOption),void((i=(o=(s=37===e.keyCode?s.prev("[data-group]"):s.next("[data-group]")).find("[data-selectable]")).eq(Math.min(o.length-1,t))).length&&this.setActiveOption(i)))});var i=function(){var e,t=i.width,n=document;return void 0===t&&((e=n.createElement("div")).innerHTML='<div style="width:50px;height:50px;position:absolute;left:-50px;top:-50px;overflow:auto;"><div style="width:1px;height:100px;"></div></div>',e=e.firstChild,n.body.appendChild(e),t=i.width=e.offsetWidth-e.clientWidth,n.body.removeChild(e)),t},o=function(){var n,o,s,a,u,l,c;if((o=(c=e("[data-group]",r.$dropdown_content)).length)&&r.$dropdown_content.width()){if(t.equalizeHeight){for(s=0,n=0;n<o;n++)s=Math.max(s,c.eq(n).height());c.css({height:s})}t.equalizeWidth&&(l=r.$dropdown_content.innerWidth()-i(),a=Math.round(l/o),c.css({width:a}),o>1&&(u=l-a*(o-1),c.eq(o-1).css({width:u})))}};(t.equalizeHeight||t.equalizeWidth)&&(d.after(this,"positionDropdown",o),d.after(this,"refreshOptions",o))})),m.define("remove_button",(function(t){t=e.extend({label:"&times;",title:"Remove",className:"remove",append:!0},t),"single"!==this.settings.mode?function(t,n){var r,i=t,o='<a href="javascript:void(0)" class="'+n.className+'" tabindex="-1" title="'+h(n.title)+'">'+n.label+"</a>",s=function(e,t){var n=e.search(/(<\/[^>]+>\s*)$/);return e.substring(0,n)+t+e.substring(n)};t.setup=(r=i.setup,function(){if(n.append){var a=i.settings.render.item;i.settings.render.item=function(e){return s(a.apply(t,arguments),o)}}r.apply(t,arguments),t.$control.on("click","."+n.className,(function(t){if(t.preventDefault(),!i.isLocked){var n=e(t.currentTarget).parent();i.setActiveItem(n),i.deleteSelection()&&i.setCaret(i.items.length)}}))})}(this,t):function(t,n){n.className="remove-single";var r,i=t,o='<a href="javascript:void(0)" class="'+n.className+'" tabindex="-1" title="'+h(n.title)+'">'+n.label+"</a>",s=function(t,n){return e("<span>").append(t).append(n)};t.setup=(r=i.setup,function(){if(n.append){var a=e(i.$input.context).attr("id"),u=(e("#"+a),i.settings.render.item);i.settings.render.item=function(e){return s(u.apply(t,arguments),o)}}r.apply(t,arguments),t.$control.on("click","."+n.className,(function(e){e.preventDefault(),i.isLocked||i.clear()}))})}(this,t)})),m.define("restore_on_backspace",(function(e){var t,n=this;e.text=e.text||function(e){return e[this.settings.labelField]},this.onKeyDown=(t=n.onKeyDown,function(n){var r,i;return 8===n.keyCode&&""===this.$control_input.val()&&!this.$activeItems.length&&(r=this.caretPos-1)>=0&&r<this.items.length?(i=this.options[this.items[r]],this.deleteSelection(n)&&(this.setTextboxValue(e.text.apply(this,[i])),this.refreshOptions(!0)),void n.preventDefault()):t.apply(this,arguments)})})),m})?r.apply(t,i):r)||(e.exports=o)},function(e,t,n){(function(r){var i,o;void 0===(o="function"==typeof(i=function(){var e=function(e,t){this.items=e,this.settings=t||{diacritics:!0}};e.prototype.tokenize=function(e){if(!(e=o(String(e||"").toLowerCase()))||!e.length)return[];var t,n,r,i,a=[],l=e.split(/ +/);for(t=0,n=l.length;t<n;t++){if(r=s(l[t]),this.settings.diacritics)for(i in u)u.hasOwnProperty(i)&&(r=r.replace(new RegExp(i,"g"),u[i]));a.push({string:l[t],regex:new RegExp(r,"i")})}return a},e.prototype.iterator=function(e,t){(a(e)?Array.prototype.forEach||function(e){for(var t=0,n=this.length;t<n;t++)e(this[t],t,this)}:function(e){for(var t in this)this.hasOwnProperty(t)&&e(this[t],t,this)}).apply(e,[t])},e.prototype.getScoreFunction=function(e,t){var n,r,o,s;e=this.prepareSearch(e,t),r=e.tokens,n=e.options.fields,o=r.length,s=e.options.nesting;var a,u=function(e,t){var n,r;return e?-1===(r=(e=String(e||"")).search(t.regex))?0:(n=t.string.length/e.length,0===r&&(n+=.5),n):0},l=(a=n.length)?1===a?function(e,t){return u(i(t,n[0],s),e)}:function(e,t){for(var r=0,o=0;r<a;r++)o+=u(i(t,n[r],s),e);return o/a}:function(){return 0};return o?1===o?function(e){return l(r[0],e)}:"and"===e.options.conjunction?function(e){for(var t,n=0,i=0;n<o;n++){if((t=l(r[n],e))<=0)return 0;i+=t}return i/o}:function(e){for(var t=0,n=0;t<o;t++)n+=l(r[t],e);return n/o}:function(){return 0}},e.prototype.getSortFunction=function(e,n){var r,o,s,a,u,l,c,p,f,h,d;if(d=!(e=(s=this).prepareSearch(e,n)).query&&n.sort_empty||n.sort,f=function(e,t){return"$score"===e?t.score:i(s.items[t.id],e,n.nesting)},u=[],d)for(r=0,o=d.length;r<o;r++)(e.query||"$score"!==d[r].field)&&u.push(d[r]);if(e.query){for(h=!0,r=0,o=u.length;r<o;r++)if("$score"===u[r].field){h=!1;break}h&&u.unshift({field:"$score",direction:"desc"})}else for(r=0,o=u.length;r<o;r++)if("$score"===u[r].field){u.splice(r,1);break}for(p=[],r=0,o=u.length;r<o;r++)p.push("desc"===u[r].direction?-1:1);return(l=u.length)?1===l?(a=u[0].field,c=p[0],function(e,n){return c*t(f(a,e),f(a,n))}):function(e,n){var r,i,o;for(r=0;r<l;r++)if(o=u[r].field,i=p[r]*t(f(o,e),f(o,n)))return i;return 0}:null},e.prototype.prepareSearch=function(e,t){if("object"==typeof e)return e;var r=(t=n({},t)).fields,i=t.sort,o=t.sort_empty;return r&&!a(r)&&(t.fields=[r]),i&&!a(i)&&(t.sort=[i]),o&&!a(o)&&(t.sort_empty=[o]),{options:t,query:String(e||"").toLowerCase(),tokens:this.tokenize(e),total:0,items:[]}},e.prototype.search=function(e,t){var n,r,i,o;return r=this.prepareSearch(e,t),t=r.options,e=r.query,o=t.score||this.getScoreFunction(r),e.length?this.iterator(this.items,(function(e,i){n=o(e),(!1===t.filter||n>0)&&r.items.push({score:n,id:i})})):this.iterator(this.items,(function(e,t){r.items.push({score:1,id:t})})),(i=this.getSortFunction(r,t))&&r.items.sort(i),r.total=r.items.length,"number"==typeof t.limit&&(r.items=r.items.slice(0,t.limit)),r};var t=function(e,t){return"number"==typeof e&&"number"==typeof t?e>t?1:e<t?-1:0:(e=l(String(e||"")))>(t=l(String(t||"")))?1:t>e?-1:0},n=function(e,t){var n,r,i,o;for(n=1,r=arguments.length;n<r;n++)if(o=arguments[n])for(i in o)o.hasOwnProperty(i)&&(e[i]=o[i]);return e},i=function(e,t,n){if(e&&t){if(!n)return e[t];for(var r=t.split(".");r.length&&(e=e[r.shift()]););return e}},o=function(e){return(e+"").replace(/^\s+|\s+$|/g,"")},s=function(e){return(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")},a=Array.isArray||void 0!==r&&r.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},u={a:"[aḀḁĂăÂâǍǎȺⱥȦȧẠạÄäÀàÁáĀāÃãÅåąĄÃąĄ]",b:"[b␢βΒB฿𐌁ᛒ]",c:"[cĆćĈĉČčĊċC̄c̄ÇçḈḉȻȼƇƈɕᴄＣｃ]",d:"[dĎďḊḋḐḑḌḍḒḓḎḏĐđD̦d̦ƉɖƊɗƋƌᵭᶁᶑȡᴅＤｄð]",e:"[eÉéÈèÊêḘḙĚěĔĕẼẽḚḛẺẻĖėËëĒēȨȩĘęᶒɆɇȄȅẾếỀềỄễỂểḜḝḖḗḔḕȆȇẸẹỆệⱸᴇＥｅɘǝƏƐε]",f:"[fƑƒḞḟ]",g:"[gɢ₲ǤǥĜĝĞğĢģƓɠĠġ]",h:"[hĤĥĦħḨḩẖẖḤḥḢḣɦʰǶƕ]",i:"[iÍíÌìĬĭÎîǏǐÏïḮḯĨĩĮįĪīỈỉȈȉȊȋỊịḬḭƗɨɨ̆ᵻᶖİiIıɪＩｉ]",j:"[jȷĴĵɈɉʝɟʲ]",k:"[kƘƙꝀꝁḰḱǨǩḲḳḴḵκϰ₭]",l:"[lŁłĽľĻļĹĺḶḷḸḹḼḽḺḻĿŀȽƚⱠⱡⱢɫɬᶅɭȴʟＬｌ]",n:"[nŃńǸǹŇňÑñṄṅŅņṆṇṊṋṈṉN̈n̈ƝɲȠƞᵰᶇɳȵɴＮｎŊŋ]",o:"[oØøÖöÓóÒòÔôǑǒŐőŎŏȮȯỌọƟɵƠơỎỏŌōÕõǪǫȌȍՕօ]",p:"[pṔṕṖṗⱣᵽƤƥᵱ]",q:"[qꝖꝗʠɊɋꝘꝙq̃]",r:"[rŔŕɌɍŘřŖŗṘṙȐȑȒȓṚṛⱤɽ]",s:"[sŚśṠṡṢṣꞨꞩŜŝŠšŞşȘșS̈s̈]",t:"[tŤťṪṫŢţṬṭƮʈȚțṰṱṮṯƬƭ]",u:"[uŬŭɄʉỤụÜüÚúÙùÛûǓǔŰűŬŭƯưỦủŪūŨũŲųȔȕ∪]",v:"[vṼṽṾṿƲʋꝞꝟⱱʋ]",w:"[wẂẃẀẁŴŵẄẅẆẇẈẉ]",x:"[xẌẍẊẋχ]",y:"[yÝýỲỳŶŷŸÿỸỹẎẏỴỵɎɏƳƴ]",z:"[zŹźẐẑŽžŻżẒẓẔẕƵƶ]"},l=function(){var e,t,n,r,i="",o={};for(n in u)if(u.hasOwnProperty(n))for(i+=r=u[n].substring(2,u[n].length-1),e=0,t=r.length;e<t;e++)o[r.charAt(e)]=n;var s=new RegExp("["+i+"]","g");return function(e){return e.replace(s,(function(e){return o[e]})).toLowerCase()}}();return e})?i.call(t,n,t,e):i)||(e.exports=o)}).call(this,n(0))},function(e,t,n){var r,i;void 0===(i="function"==typeof(r=function(){var e={mixin:function(e){e.plugins={},e.prototype.initializePlugins=function(e){var n,r,i,o=[];if(this.plugins={names:[],settings:{},requested:{},loaded:{}},t.isArray(e))for(n=0,r=e.length;n<r;n++)"string"==typeof e[n]?o.push(e[n]):(this.plugins.settings[e[n].name]=e[n].options,o.push(e[n].name));else if(e)for(i in e)e.hasOwnProperty(i)&&(this.plugins.settings[i]=e[i],o.push(i));for(;o.length;)this.require(o.shift())},e.prototype.loadPlugin=function(t){var n=this.plugins,r=e.plugins[t];if(!e.plugins.hasOwnProperty(t))throw new Error('Unable to find "'+t+'" plugin');n.requested[t]=!0,n.loaded[t]=r.fn.apply(this,[this.plugins.settings[t]||{}]),n.names.push(t)},e.prototype.require=function(e){var t=this.plugins;if(!this.plugins.loaded.hasOwnProperty(e)){if(t.requested[e])throw new Error('Plugin has circular dependency ("'+e+'")');this.loadPlugin(e)}return t.loaded[e]},e.define=function(t,n){e.plugins[t]={name:t,fn:n}}}},t={isArray:Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}};return e})?r.call(t,n,t,e):r)||(e.exports=i)}]);
//# sourceMappingURL=bundle.js.map
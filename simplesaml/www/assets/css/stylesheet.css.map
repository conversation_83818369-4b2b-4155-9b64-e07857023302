{"version": 3, "sources": ["webpack:///pure.css", "webpack:///selectize.css", "webpack:///zenburn.css", "webpack:///./node_modules/reset-css/sass/_reset.scss", "webpack:///default.scss", "webpack:///./node_modules/font-awesome/scss/font-awesome.scss", "webpack:///./node_modules/font-awesome/scss/_path.scss", "webpack:///./node_modules/font-awesome/scss/_core.scss", "webpack:///./node_modules/font-awesome/scss/_larger.scss", "webpack:///./node_modules/font-awesome/scss/_fixed-width.scss", "webpack:///./node_modules/font-awesome/scss/_list.scss", "webpack:///./node_modules/font-awesome/scss/_variables.scss", "webpack:///./node_modules/font-awesome/scss/_bordered-pulled.scss", "webpack:///./node_modules/font-awesome/scss/_animated.scss", "webpack:///./node_modules/font-awesome/scss/_rotated-flipped.scss", "webpack:///./node_modules/font-awesome/scss/_mixins.scss", "webpack:///./node_modules/font-awesome/scss/_stacked.scss", "webpack:///./node_modules/font-awesome/scss/_icons.scss", "webpack:///./src/css/default.scss"], "names": [], "mappings": "AAAA;;;;;CAKC;AACD;;;CAGC;AACD,2EAA2E;;AAE3E;;;;EAIE;;AAEF;EACE,uBAAuB,EAAE,MAAM;EAC/B,0BAA0B,EAAE,MAAM;EAClC,8BAA8B,EAAE,MAAM;AACxC;;AAEA;;EAEE;;AAEF;EACE,SAAS;AACX;;AAEA;+EAC+E;;AAE/E;;;;;EAKE;;AAEF;;;;;;;;;;;;;EAaE,cAAc;AAChB;;AAEA;;;EAGE;;AAEF;;;;EAIE,qBAAqB,EAAE,MAAM;EAC7B,wBAAwB,EAAE,MAAM;AAClC;;AAEA;;;EAGE;;AAEF;EACE,aAAa;EACb,SAAS;AACX;;AAEA;;;EAGE;;AAEF;;EAEE,aAAa;AACf;;AAEA;+EAC+E;;AAE/E;;EAEE;;AAEF;EACE,6BAA6B;AAC/B;;AAEA;;;EAGE;;AAEF;;EAEE,UAAU;AACZ;;AAEA;+EAC+E;;AAE/E;;EAEE;;AAEF;EACE,yBAAyB;AAC3B;;AAEA;;EAEE;;AAEF;;EAEE,iBAAiB;AACnB;;AAEA;;EAEE;;AAEF;EACE,kBAAkB;AACpB;;AAEA;;;EAGE;;AAEF;EACE,cAAc;EACd,gBAAgB;AAClB;;AAEA;;EAEE;;AAEF;EACE,gBAAgB;EAChB,WAAW;AACb;;AAEA;;EAEE;;AAEF;EACE,cAAc;AAChB;;AAEA;;EAEE;;AAEF;;EAEE,cAAc;EACd,cAAc;EACd,kBAAkB;EAClB,wBAAwB;AAC1B;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,eAAe;AACjB;;AAEA;+EAC+E;;AAE/E;;EAEE;;AAEF;EACE,SAAS;AACX;;AAEA;;EAEE;;AAEF;EACE,gBAAgB;AAClB;;AAEA;+EAC+E;;AAE/E;;EAEE;;AAEF;EACE,gBAAgB;AAClB;;AAEA;;EAEE;;AAEF;EACE,+BAA+B;UACvB,uBAAuB;EAC/B,SAAS;AACX;;AAEA;;EAEE;;AAEF;EACE,cAAc;AAChB;;AAEA;;EAEE;;AAEF;;;;EAIE,iCAAiC;EACjC,cAAc;AAChB;;AAEA;+EAC+E;;AAE/E;;;EAGE;;AAEF;;;;;EAKE;;AAEF;;;;;EAKE,cAAc,EAAE,MAAM;EACtB,aAAa,EAAE,MAAM;EACrB,SAAS,EAAE,MAAM;AACnB;;AAEA;;EAEE;;AAEF;EACE,iBAAiB;AACnB;;AAEA;;;;;EAKE;;AAEF;;EAEE,oBAAoB;AACtB;;AAEA;;;;;;EAME;;AAEF;;;;EAIE,0BAA0B,EAAE,MAAM;EAClC,eAAe,EAAE,MAAM;AACzB;;AAEA;;EAEE;;AAEF;;EAEE,eAAe;AACjB;;AAEA;;EAEE;;AAEF;;EAEE,SAAS;EACT,UAAU;AACZ;;AAEA;;;EAGE;;AAEF;EACE,mBAAmB;AACrB;;AAEA;;;;;;EAME;;AAEF;;EAEE,8BAA8B;UACtB,sBAAsB,EAAE,MAAM;EACtC,UAAU,EAAE,MAAM;AACpB;;AAEA;;;;EAIE;;AAEF;;EAEE,YAAY;AACd;;AAEA;;;EAGE;;AAEF;EACE,6BAA6B,EAAE,MAAM;EACrC,+BAA+B;UACvB,uBAAuB,EAAE,MAAM;AACzC;;AAEA;;;;EAIE;;AAEF;;EAEE,wBAAwB;AAC1B;;AAEA;;EAEE;;AAEF;EACE,yBAAyB;EACzB,aAAa;EACb,8BAA8B;AAChC;;AAEA;;;EAGE;;AAEF;EACE,SAAS,EAAE,MAAM;EACjB,UAAU,EAAE,MAAM;AACpB;;AAEA;;EAEE;;AAEF;EACE,cAAc;AAChB;;AAEA;;;EAGE;;AAEF;EACE,iBAAiB;AACnB;;AAEA;+EAC+E;;AAE/E;;EAEE;;AAEF;EACE,yBAAyB;EACzB,iBAAiB;AACnB;;AAEA;;EAEE,UAAU;AACZ;;AAEA,0BAA0B;;AAE1B;;+EAE+E;;AAE/E;;EAEE;;AAEF;;EAEE;;AAEF;;IAEI,wBAAwB;AAC5B;;AAEA;;;EAGE;AACF;IACI,eAAe;IACf,YAAY;IACZ,cAAc;AAClB;;AAEA,oFAAoF;;AAEpF;IACI,uBAAuB,EAAE,+CAA+C;KACxE,sBAAuB,EAAE,iBAAiB;KAC1C,qBAAsB,EAAE,+CAA+C;IACvE,6BAA6B,EAAE,qDAAqD;;IAEpF;;;;;;;;;;;;;;;KAeC;IACD,wEAAwE;;IAExE,sEAAsE;IACtE,oBAAoB;IACpB,qBAAqB;IACrB,oBAAoB;IACpB,aAAa;IACb,8BAA8B;IAC9B,6BAA6B;IAC7B,2BAA2B;QACvB,uBAAuB;YACnB,mBAAmB;;IAE3B,6CAA6C;IAC7C,iCAAiC;QAC7B,yBAAyB;YACrB,yBAAyB;AACrC;;AAEA,kIAAkI;AAClI;CACC;EACC,cAAc;CACf;AACD;;AAEA;;;CAGC;AACD;;IAEI,qBAAqB;AACzB;;AAEA;IACI,qBAAqB;KACrB,eAAgB,EAAE,8BAA8B;IAChD,OAAO;IACP,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;AACxB;;AAEA;;;CAGC;AACD;IACI,uBAAuB;AAC3B;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8CI,qBAAqB;KACrB,eAAgB;IAChB,OAAO;IACP,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;AACxB;;AAEA;IACI,cAAc;KACd,cAAe;AACnB;;AAEA;;IAEI,cAAc;KACd,cAAe;AACnB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,UAAU;KACV,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,UAAU;KACV,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,UAAU;KACV,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,UAAU;KACV,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,UAAU;KACV,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,UAAU;KACV,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,UAAU;KACV,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;;IAEI,eAAe;KACf,eAAgB;AACpB;;AAEA;IACI,eAAe;KACf,eAAgB;AACpB;;AAEA;;;;IAII,WAAW;AACf;AACA;IACI,cAAc;IACd,qBAAqB;IACrB,OAAO;IACP,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,kBAAkB;IAClB,eAAe;IACf,uBAAuB;IACvB,yBAAyB;OACtB,sBAAsB;QACrB,qBAAqB;YACjB,iBAAiB;IACzB,8BAA8B;YACtB,sBAAsB;AAClC;;AAEA,+CAA+C;AAC/C;IACI,UAAU;IACV,SAAS;AACb;;AAEA,2BAA2B;AAC3B;IACI,uBAAuB,EAAE,+CAA+C;KACxE,sBAAuB,EAAE,iBAAiB;KAC1C,qBAAsB,EAAE,+CAA+C;IACvE,6BAA6B,EAAE,qDAAqD;AACxF;;AAEA;;IAEI,qBAAqB;AACzB;;AAEA;IACI,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;AACxB;;AAEA,6BAA6B;;AAE7B;IACI,oBAAoB;IACpB,eAAe;IACf,kBAAkB;IAClB,WAAW,EAAE,8BAA8B;IAC3C,0BAA0B,EAAE,mBAAmB;IAC/C,sBAAsB,GAAG,WAAW;IACpC,6BAA6B,GAAG,wBAAwB;IACxD,yBAAyB;IACzB,qBAAqB;IACrB,kBAAkB;AACtB;;AAEA;;;IAGI,+IAA+I;IAC/I,gGAAgG;IAChG,wFAAwF;AAC5F;AACA;IACI,UAAU;AACd;AACA;;IAEI,sFAAsF;YAC9E,8EAA8E;IACtF,kBAAkB;AACtB;;AAEA;;;;;IAKI,YAAY;IACZ,sBAAsB;IACtB,yBAAyB;IACzB,yBAAyB;IACzB,uBAAuB;IACvB,aAAa;IACb,mBAAmB;IACnB,wBAAwB;YAChB,gBAAgB;IACxB,oBAAoB;AACxB;;AAEA;IACI,aAAa;AACjB;;AAEA;;;;IAII,kCAAkC;IAClC,WAAW;AACf;;AAEA,kBAAkB;AAClB;IACI,SAAS;IACT,gBAAgB;IAChB,4BAA4B,GAAG,wCAAwC;IACvE,0CAA0C;;AAE9C;;AAEA;IACI,2BAA2B;IAC3B,8BAA8B;AAClC;AACA;IACI,4BAA4B;IAC5B,+BAA+B;IAC/B,kBAAkB;AACtB;;AAEA,0BAA0B;AAC1B;;;;;CAKC;;AAED;;;;;;;;;;;;;;;;IAgBI,oBAAoB;IACpB,qBAAqB;IACrB,sBAAsB;IACtB,wCAAwC;YAChC,gCAAgC;IACxC,kBAAkB;IAClB,sBAAsB;IACtB,8BAA8B;YACtB,sBAAsB;AAClC;;AAEA;;;CAGC;AACD;IACI,oBAAoB;IACpB,qBAAqB;IACrB,sBAAsB;IACtB,wCAAwC;YAChC,gCAAgC;IACxC,kBAAkB;IAClB,8BAA8B;YACtB,sBAAsB;AAClC;;;AAGA,+EAA+E;AAC/E,+FAA+F;AAC/F;IACI,oBAAoB;AACxB;;;AAGA;;;;;;;;;;;;;;;;IAgBI,UAAU;IACV,qBAAqB;AACzB;;AAEA;;;CAGC;AACD;IACI,UAAU;IACV,qBAAqB;AACzB;;AAEA;;;IAGI,2BAA2B;IAC3B,yBAAyB;AAC7B;AACA;;IAEI,eAAe;IACf,cAAc;AAClB;;AAEA;;;;;;;;;;;;;;;;IAgBI,mBAAmB;IACnB,yBAAyB;IACzB,cAAc;AAClB;;AAEA;;;CAGC;AACD;IACI,mBAAmB;IACnB,yBAAyB;IACzB,cAAc;AAClB;AACA;;;IAGI,sBAAsB,EAAE,wBAAwB;IAChD,WAAW,EAAE,oBAAoB;IACjC,kBAAkB;AACtB;;AAEA;;;IAGI,cAAc;IACd,qBAAqB;AACzB;AACA;;;IAGI,sBAAsB;AAC1B;AACA;IACI,sDAAsD;IACtD,cAAc;IACd,sBAAsB;IACtB,uBAAuB;AAC3B;AACA;IACI,YAAY;AAChB;AACA;IACI,qBAAqB;AACzB;AACA;IACI,SAAS;IACT,wBAAwB;IACxB,SAAS;AACb;AACA;IACI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,oBAAoB;IACpB,WAAW;IACX,gCAAgC;AACpC;;AAEA;;;;;;;;;;;;;;;;;;IAkBI,cAAc;IACd,gBAAgB;AACpB;;AAEA;;;CAGC;AACD;IACI,cAAc;IACd,gBAAgB;AACpB;AACA;;;;;;IAMI,qBAAqB;KACrB,eAAgB;KAChB,OAAQ;IACR,sBAAsB;AAC1B;AACA;IACI,mBAAmB;AACvB;;AAEA,kBAAkB;AAClB;IACI,oBAAoB;AACxB;AACA;IACI,iBAAiB;IACjB,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,iBAAiB;AACrB;AACA;IACI,sBAAsB;AAC1B;;AAEA,mBAAmB;AACnB;;IAEI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA,mBAAmB;AACnB;IACI,mBAAmB;AACvB;AACA;;IAEI,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;AACb;AACA;;IAEI,UAAU;AACd;AACA;;IAEI,QAAQ;IACR,0BAA0B;IAC1B,SAAS;AACb;AACA;;IAEI,QAAQ;IACR,kBAAkB;IAClB,SAAS;AACb;AACA;;IAEI,SAAS;IACT,0BAA0B;IAC1B,SAAS;AACb;AACA;IACI,gBAAgB;AACpB;;AAEA;IACI,WAAW;AACf;AACA;IACI,UAAU;AACd;AACA;IACI,UAAU;AACd;AACA;IACI,UAAU;AACd;AACA;IACI,UAAU;AACd;AACA;IACI,UAAU;AACd;;AAEA,0BAA0B;AAC1B,iFAAiF;AACjF;;IAEI,qBAAqB;IACrB,mBAAmB;IACnB,WAAW;IACX,sBAAsB;IACtB,kBAAkB;AACtB;;AAEA,yBAAyB;AACzB;IACI,cAAc;IACd,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI;QACI,iBAAiB;IACrB;;IAEA;;;;;;;;;;;;;;;;QAgBI,oBAAoB;QACpB,cAAc;IAClB;;IAEA;;;;;;;;;;;;;;;QAeI,gBAAgB;IACpB;;IAEA;QACI,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;QACd,WAAW;IACf;;IAEA;QACI,mBAAmB;IACvB;;IAEA,iFAAiF;IACjF;;;QAGI,cAAc;QACd,iBAAiB;QACjB,8EAA8E;QAC9E,sBAAsB;IAC1B;AACJ;;AAEA,oDAAoD;AACpD;IACI,8BAA8B;YACtB,sBAAsB;AAClC;;AAEA;IACI,eAAe;IACf,OAAO;IACP,MAAM;IACN,UAAU;AACd;;AAEA;;IAEI,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,SAAS;IACT,UAAU;AACd;;AAEA;IACI,UAAU;IACV,SAAS;IACT,YAAY;AAChB;;AAEA;;IAEI,cAAc;IACd,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,oBAAoB;AACpB;IACI,WAAW;IACX,mBAAmB;AACvB;;AAEA;IACI,qBAAqB;AACzB;;AAEA,qEAAqE;AACrE;;;IAGI,qBAAqB;KACrB,eAAgB;IAChB,OAAO;IACP,sBAAsB;AAC1B;;AAEA,6CAA6C;AAC7C;IACI,cAAc;AAClB;;AAEA;IACI,aAAa;IACb,kBAAkB;IAClB,UAAU;IACV,MAAM;IACN,SAAS;IACT,UAAU;IACV,UAAU;AACd;;AAEA;IACI,OAAO;IACP,SAAS;IACT,cAAc;AAClB;;AAEA;;IAEI,cAAc;IACd,kBAAkB;AACtB;;AAEA,6CAA6C;AAC7C;IACI,mBAAmB;IACnB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA,+CAA+C;AAC/C;IACI,gBAAgB;AACpB;;AAEA,qBAAqB;AACrB;IACI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,mBAAmB;IACnB,kBAAkB;IAClB,gBAAgB;IAChB,iCAAiC;IACjC,kEAAkE;IAClE,eAAe;AACnB;;AAEA,yBAAyB;;AAEzB;;IAEI,sBAAsB;IACtB,WAAW;IACX,cAAc;AAClB;;AAEA;IACI,UAAU;IACV,aAAa;IACb,eAAe;AACnB;;AAEA,0DAA0D;AAC1D;IACI,cAAc;IACd,WAAW;AACf;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,WAAW;AACf;;AAEA;IACI,sBAAsB;AAC1B;;AAEA;;;IAGI,iBAAiB;AACrB;;AAEA;IACI,WAAW;AACf;;AAEA;IACI,6BAA6B;AACjC;;AAEA;;;IAGI,sBAAsB;AAC1B;;AAEA;;IAEI,WAAW;AACf;;AAEA;IACI,4DAA4D;IAC5D,yBAAyB;IACzB,iBAAiB;IACjB,iBAAiB;IACjB,yBAAyB;AAC7B;;AAEA;IACI,WAAW;IACX,oCAAoC;IACpC,cAAc;IACd,kBAAkB;AACtB;;AAEA;;IAEI,8BAA8B,CAAC,yBAAyB;IACxD,uBAAuB;IACvB,kBAAkB;IAClB,SAAS;IACT,iBAAiB,EAAE,kDAAkD;IACrE,kBAAkB,EAAE,iBAAiB;AACzC;;AAEA;IACI,yBAAyB;IACzB,WAAW;IACX,gBAAgB;IAChB,sBAAsB;AAC1B;;AAEA;;;;CAIC;AACD;IACI,6BAA6B;AACjC;AACA;IACI,yBAAyB;AAC7B;;AAEA,2CAA2C;AAC3C;IACI,yBAAyB;AAC7B;;AAEA,oBAAoB;AACpB;IACI,gCAAgC;AACpC;AACA;IACI,sBAAsB;AAC1B;;;AAGA,+BAA+B;;AAE/B;;IAEI,uBAAuB;IACvB,gCAAgC;AACpC;AACA;IACI,sBAAsB;AAC1B;ACl+CA;;;;;;;;;;;;;;EAcE;;AAEF;EACE,8BAA8B;EAC9B,8BAA8B;EAC9B,0CAA0C;EAC1C,yBAAyB;EACzB,2CAA2C;EAC3C,mCAAmC;AACrC;AACA;EACE,YAAY;EACZ,kBAAkB;AACpB;AACA;EACE,gDAAgD;EAChD,wCAAwC;AAC1C;AACA;EACE,kBAAkB;EAClB,gBAAgB;EAChB,gCAAgC;EAChC,mBAAmB;EACnB,kCAAkC;EAClC,+BAA+B;EAC/B,0BAA0B;AAC5B;AACA;EACE,kBAAkB;EAClB,UAAU;EACV,QAAQ;EACR,cAAc;EACd,YAAY;EACZ,iBAAiB;EACjB,iBAAiB;EACjB,0BAA0B;AAC5B;AACA;EACE,cAAc;AAChB;AACA;EACE,+BAA+B;EAC/B,kBAAkB;EAClB,WAAW;EACX,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;AACxB;AACA;EACE,oBAAoB;AACtB;AACA;EACE,aAAa;AACf;AACA;EACE,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,8BAA8B;AAChC;AACA;EACE,UAAU;EACV,4BAA4B;EAC5B,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,SAAS;EACT,WAAW;EACX,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,cAAc;EACd,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,kBAAkB;EAClB,8BAA8B;EAC9B,kCAAkC;EAClC,+BAA+B;EAC/B,0BAA0B;EAC1B,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;AACxB;AACA;EACE,+BAA+B;AACjC;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;AACjB;AACA;EACE,kBAAkB;AACpB;AACA;;;EAGE,cAAc;EACd,oBAAoB;EACpB,eAAe;EACf,iBAAiB;EACjB,+BAA+B;AACjC;AACA;;EAEE,gBAAgB;EAChB,YAAY;EACZ,qBAAqB;AACvB;AACA;EACE,yBAAyB;EACzB,gBAAgB;EAChB,qBAAqB;EACrB,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,UAAU;EACV,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;EACtB,sDAAsD;EACtD,8CAA8C;EAC9C,0BAA0B;EAC1B,uBAAuB;EACvB,kBAAkB;AACpB;AACA;EACE,oBAAoB;AACtB;AACA;EACE,sBAAsB;AACxB;AACA;;EAEE,0BAA0B;AAC5B;AACA;EACE,uDAAuD;EACvD,+CAA+C;AACjD;AACA;EACE,kCAAkC;EAClC,+BAA+B;EAC/B,0BAA0B;AAC5B;AACA;EACE,wBAAwB;EACxB,0BAA0B;EAC1B,qBAAqB;EACrB,OAAO;GACP,eAAgB;AAClB;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,gBAAgB;EAChB,mBAAmB;EACnB,cAAc;EACd,uBAAuB;AACzB;AACA;EACE,mBAAmB;EACnB,cAAc;EACd,uBAAuB;AACzB;AACA;;EAEE,cAAc;EACd,mBAAmB;EACnB,uBAAuB;AACzB;AACA;EACE,gCAAgC;EAChC,qBAAqB;EACrB,wBAAwB;EACxB,2BAA2B;EAC3B,0BAA0B;EAC1B,4BAA4B;EAC5B,yBAAyB;EACzB,yBAAyB;EACzB,2BAA2B;EAC3B,+BAA+B;EAC/B,oCAAoC;EACpC,mCAAmC;EACnC,2BAA2B;AAC7B;AACA;EACE,aAAa;AACf;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,YAAY;EACZ,cAAc;EACd,WAAW;AACb;AACA;EACE,YAAY;EACZ,cAAc;EACd,kBAAkB;EAClB,mBAAmB;EACnB,WAAW;EACX,SAAS;EACT,OAAO;EACP,QAAQ;AACV;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,yBAAyB;EACzB,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;EAClB,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;EACtB,gDAAgD;EAChD,wCAAwC;EACxC,kCAAkC;EAClC,+BAA+B;EAC/B,0BAA0B;AAC5B;AACA;EACE,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,oCAAoC;EACpC,0BAA0B;EAC1B,uBAAuB;EACvB,kBAAkB;AACpB;AACA;;EAEE,gBAAgB;AAClB;AACA;;;EAGE,eAAe;EACf,YAAY;AACd;AACA;EACE,UAAU;AACZ;AACA;EACE,kBAAkB;AACpB;AACA;EACE,cAAc;EACd,gBAAgB;EAChB,eAAe;AACjB;AACA;EACE,yBAAyB;EACzB,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,4BAA4B;AAC9B;AACA;EACE,gBAAgB;EAChB,kBAAkB;EAClB,iBAAiB;EACjB,iCAAiC;AACnC;AACA;;EAEE,eAAe;AACjB;AACA;;EAEE,YAAY;AACd;AACA;EACE,YAAY;EACZ,cAAc;EACd,kBAAkB;EAClB,QAAQ;EACR,WAAW;EACX,gBAAgB;EAChB,QAAQ;EACR,SAAS;EACT,mBAAmB;EACnB,2BAA2B;EAC3B,yDAAyD;AAC3D;AACA;EACE,gBAAgB;EAChB,2BAA2B;EAC3B,yDAAyD;AAC3D;AACA;EACE,UAAU;EACV,WAAW;AACb;AACA;EACE,+BAA+B;AACjC;AACA;EACE,YAAY;EACZ,yBAAyB;AAC3B;AC5UA;;;;;CAKC;;AAED;EACE,cAAc;EACd,gBAAgB;EAChB,cAAc;EACd,mBAAmB;EACnB,cAAc;AAChB;;AAEA;;;EAGE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA;;;EAGE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA;;;;;;EAME,cAAc;AAChB;;AAEA;;;EAGE,cAAc;AAChB;;AAEA;;;;EAIE,cAAc;AAChB;;AAEA;;;;EAIE,cAAc;AAChB;;;AAGA;EACE,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;AACnB;AC1EA,qaAaC,QAAS,CACT,SAAU,CACV,QAAS,CACT,cAAe,CACf,YAAa,CACb,uBAAwB,CACxB,mFAIA,aAAc,CACd,UAGG,YAAa,CAChB,KAEA,aAAc,CACd,WAEA,eAAgB,CAChB,aAEA,WAAY,CACZ,oDAGA,UAAW,CACX,YAAa,CACb,MAEA,wBAAyB,CACzB,gBAAiB,CACjB;;;EChDC,CCAC,WCCD,yBAA0B,CAC1B,mDAAqE,CACrE,4WAImG,CAEnG,kBAAmB,CACnB,iBAAkB,CCVpB,IACE,oBAAqB,CACrB,4CAAoF,CACpF,iBAAkB,CAClB,mBAAoB,CACpB,kCAAmC,CACnC,iCAAkC,CAEnC,OCNC,mBAAoB,CACpB,iBAAsB,CACtB,mBAAoB,CACrB,OACuB,aAAc,CAAI,OAClB,aAAc,CAAI,OAClB,aAAc,CAAI,OAClB,aAAc,CAAI,OCTxC,eAAkB,CAClB,iBAAkB,CACnB,OCDC,cAAe,CACf,qBCM+B,CDL/B,oBAAqB,CAHvB,UAIS,iBAAkB,CAAI,OAG7B,iBAAkB,CAClB,eCA+B,CDC/B,eCD+B,CDE/B,YAAe,CACf,iBAAkB,CALpB,aAOI,eAAgC,CACjC,WEbD,wBAAyB,CACzB,wBDIwB,CCHxB,kBAAmB,CACpB,cAE8B,UAAW,CAAI,eACd,WAAY,CAAI,iBAGd,iBAAkB,CADpD,kBAEmC,gBAAiB,CAAI,YAI1C,WAAY,CAAI,WACjB,UAAW,CAAI,cAGZ,iBAAkB,CADlC,eAEiB,gBAAiB,CAAI,SCnBpC,4CAA6C,CACrC,oCAAqC,CAC9C,UAGC,8CAA+C,CACvC,sCAAuC,CAChD,2BAGC,GACE,8BAA+B,CACvB,sBAAuB,CAEjC,KACE,gCAAiC,CACzB,wBAAyB,EAIrC,mBACE,GACE,8BAA+B,CACvB,sBAAuB,CAEjC,KACE,gCAAiC,CACzB,wBAAyB,EC5BrC,cCWE,qEAAiF,CACjF,+BAAmC,CAC/B,2BAA+B,CAC3B,uBAA2B,CDdiC,eCWpE,qEAAiF,CACjF,gCAAmC,CAC/B,4BAA+B,CAC3B,wBAA2B,CDbiC,eCUpE,qEAAiF,CACjF,gCAAmC,CAC/B,4BAA+B,CAC3B,wBAA2B,CDZiC,oBCgBpE,+EAA2F,CAC3F,8BAAuC,CACnC,0BAAmC,CAC/B,sBAA+B,CDjB+B,kBCctE,+EAA2F,CAC3F,8BAAuC,CACnC,0BAAmC,CAC/B,sBAA+B,CDhB+B,gHAUtE,WAAY,CACb,UEfC,iBAAkB,CAClB,oBAAqB,CACrB,SAAU,CACV,UAAW,CACX,eAAgB,CAChB,qBAAsB,CACvB,0BAEC,iBAAkB,CAClB,MAAO,CACP,UAAW,CACX,iBAAkB,CACnB,aAC6B,mBAAoB,CAAI,aACxB,aAAc,CAAI,YACnB,ULTH,CKSyB,iBChBjB,WNwUnB,CAAO,iBMvUN,WN2dD,mBM1dL,WN0jBM,uBMzjBF,WNsOM,kBMrOX,WAAgC,CNuW1B,gBMtWP,WAAyB,CAAO,kBAC9B,WAAyB,iBAC3B,WAAyB,iBACzB,WAAyB,qBAAyB,WACrB,eAA6B,WACnC,oBAAuB,WAClB,kBAA4B,WAChD,CAAkB,oDAGlC,WAAS,CAAO,uBAA4C,WAC7C,yBAAyD,WACxD,sBAA0D,WAC7D,mBAAuD,WAC1D,gCACF,WACD,oBAAiD,WAC7C,iBN+qBM,WM/qB+C,CACxD,kBNwVM,WMxV4C,oBNwP1C,WMvP8C,iBAC1B,WAA4B,qBNohBlD,WMnhB4C,gCACQ,yCACsB,6BAC1C,WAAsC,0BNsWrE,WMrW6C,2CACgB,WAE5E,oBAAmC,WAA2B,qBAC1B,WAA4B,iBACpD,WNwYM,iBMvYV,WAAgC,CN2Y1B,sBM1YmB,WN4PZ,CM5PqC,sBACnB,WNoUnB,CAAO,uBMnUY,WNitBnB,CAAO,qBMhtBZ,WN+sBM,mBM9sBR,WAAyB,oBAC5B,WAAyB,gBAA2B,WAC5C,CAAkB,gBAA4B,WAClD,CAAkB,gBAAwB,WAChD,CAAO,oBAA2C,WACzB,kBAAyB,WAC9C,mBAAsD,WACzD,iBAAmD,WAClD,iBN2EM,WM1ER,mBN0Pa,WMzPb,wBAAkD,WAChD,CAAO,sBAA6C,WAC/C,uBAAyD,WAC1D,yBAAwD,WACxD,wBAAwD,WACtD,0BAA0D,WAC3D,iBNrCa,WMqC4C,sCAEhE,WAAyB,mBACvB,WACV,yBN2aiB,WM3a+C,wDNorB1C,WMlrBoD,mBAEjE,WACT,uBAAsC,WAA8B,mBACjC,WAA2B,iBAChD,WNwXM,4CMtXZ,WAAyB,2BAEjC,sCN+agC,WM/agD,mBACrC,WAAmC,0BACnC,WN+EZ,CM/E+C,yBACpC,WAAoB,qBACpB,WNmjBZ,CMnjB8C,2BAClC,kBAAkC,WAChE,CAAO,gBAA+C,WAC1D,oBAAkD,WACjD,CAAO,wBACR,WAAyB,yBACtB,WN+NM,kBM/N+C,WACvB,yBAAiC,WACjC,0BAChC,WAAgC,CN+I1B,uBM9IC,WAAyB,yBACxB,WAAyB,yBAC3B,WAAyB,yBAAgC,WAC/B,4BACzB,WAAyB,wBAAiC,WACjC,uBAAiC,WACvD,2BAA6D,WACjE,CAAO,0BAAkD,WACjC,gBAA+B,kCNwlBvC,WMvlB+C,wBNwDtD,WMvDsD,CACvE,oBNnCa,WMmCoC,uBNlDpC,WMmDkD,0CAE1D,WAAyB,CAAO,kBAAsB,WAC3B,qBAA+B,WACtD,iBACP,WAAgC,CN4d1B,iBM3dL,WAAyB,qBACvB,WAAyB,+BACJ,WAAyB,iBACxB,WAA0B,iBACzC,WNpDD,iBMoDgD,WAC5C,gBNuIM,WMtIpB,sBAAkD,WAClD,oDAED,WAAyB,kBAAwB,WAC3C,CAAO,oBAAgD,WACzD,CAAO,kBAClB,WAAwB,oBAAkE,WACjF,mBAAmD,WAChD,uBAAsD,WACxD,yBAAoD,WAC1B,oBAA4B,WAC7B,0BACrB,WAAyB,mBAA+B,WACtD,wBAA0D,WACxD,CAAkB,oBAA4B,WAC/C,qBAA2D,WAClE,6CAC8B,WAAgC,2BN1EtD,WM2EgD,4BACA,oCAErD,WAAyB,gBAA8B,WAClD,kCAA4D,WAC3D,qBAA6D,WAChE,wBAA0D,WAC5D,CAAkB,yBACvB,WACT,sBN6CqB,WM7CqC,oBNmDxC,WMlDgD,qBAC1B,WAAgC,4BACvB,WAA2B,uBAC/B,WAAuB,0BN+N5C,WM9NwC,oBNqc9C,WMpcgD,mBACtB,WNuRnB,CAAO,yBMtRO,WAA+B,mBACrD,WAAyB,oBAC/B,WAAyB,kBAA4B,WAC7B,qBAA2B,WAC7C,uBAA2D,WAC3D,CAAkB,wBACxB,WAAyB,oBAC3B,WAAyB,2CACgC,8BNhD9C,WMiDkD,mBAC7B,WAAiC,wBACtC,WAA4B,gCAEpD,WAAyB,kBAA6B,WAC/B,qBACzB,WAAyB,iBAA2B,WAC/C,wBAAyD,WAExE,yBN+Ya,WM9YJ,wBAAmD,WAChD,sBAAsD,WAC1D,CAAO,uBAA2C,WAC3C,8BAAyD,WAC/B,+BAC1B,WAAgC,CNkL1B,2BMjLiB,WNmLZ,CMnL0C,6BAC5B,WAAgC,kBACnD,WAAyB,mBAAsC,YAC9D,iBNjIM,WMiI0D,mBAC5D,WNjID,sBMiIuD,WAClC,uBACrC,WAAyB,mCN8lBlB,WM7lB8C,kCAEpD,WAAyB,kBAA2B,WAC1C,CAAkB,iBAA8B,WACtD,oCACE,WACP,oCACO,WACR,sBAAkD,WACjD,qCACyB,WAA0B,mBAE5D,WAAY,uDAEwB,WAA4B,oBAC1B,WN6SZ,CM7S0C,mBAEpE,WAAY,0BAAsD,WAC/B,sBAA2B,WAE9D,kBACA,WAAQ,kBAAkD,WAC/C,kBNsOM,WMtO+C,sBNqO/C,WMpO+C,6BACY,0CACR,WAClC,wBACzB,WAAgC,CNyO1B,iBMxON,WAAyB,CAAO,sBAC5B,WAAyB,CAAO,oBAAuB,WAChD,uBAA8D,mCNiI/C,WMhImD,oBACvC,WAAyB,qCACZ,WACrB,2CN7ErB,WM8EgD,wCACI,WAC9B,qBAAgC,WACpC,qBAA4B,WAEhE,wCAA0D,WAE1D,mCAAoE,WACzD,4CACuD,WACtD,sBAAsD,WACtD,uBAAsD,WACnD,kCAC2C,WACjD,oBACA,WNoFM,qBMnFF,WACb,uCAAsE,WAChC,wBAA8B,WAC7B,qBAA+B,WAEtE,2BAA0D,WAC/C,yBAAqD,WACpD,oBAAsD,WACzD,wBACW,WNrED,qBMsEJ,WAAyB,CAAO,kBACnC,WAAyB,mBAA6B,WAChD,oBAA4D,mCNlEjD,WMmE6C,CAC/D,sBAAqD,kCN+YpC,WM9Y4C,sBN0Z/C,WMxZf,mBN9JM,WM8J8C,CACpD,uBAAoD,WACnD,iBN1CM,WM0C+C,qBACjB,WAAyB,wBAC1B,WAAwB,8BACA,0CACF,WACjC,4BACpB,WAAyB,8BACP,WAAyB,uBACrB,WAA6B,wBAC1B,WAAgC,qBACnD,WAAyB,CAAO,sBAA+B,WAC9D,oBAAgE,8BACnC,WAA6B,mBAC3D,WAAyB,CAAO,0CACiB,gCNpOjD,WMqOmD,uBNpOtD,WMqOgD,wBNxO9C,WMyOkD,oBNnDrD,WMoD+C,mBN6IhD,WM5I8C,wCAE9C,WAChB,uBNuMgB,WMvM8C,qBAClB,WAAsB,0BN0Q9C,WMzQkD,oBAC9B,WAAgC,oBACpC,WAA4B,kBAC7B,WN9GZ,CM8GuC,mBAChD,WACd,CAAS,sBAAmD,8BNgDxC,WM/CkD,2BACJ,gCNqB3C,WMpBqD,iBACxC,WAA4B,gDAE9B,WNgLZ,CMhLsC,4EAEU,WACnC,2BAA2B,WACnB,iBAAmC,WAClE,sBAAsD,WACjC,2CAEjC,WAAa,qBAAuD,4BAEpE,mCACA,WAAwC,wBAAgC,WACtD,sBAA4D,WAC7C,mBAAyB,WAC7C,yBAAuD,WAEpE,uBAAyC,WNtJnB,CAAO,4BMuJe,WAAsB,mBN2FpD,WM1F4C,uBAClB,WAAgC,8BNmWnD,WMlWmD,mBAC3B,WAAuB,mBACjC,WAA2B,gCNiOjC,WMhO6C,iCACJ,YAClD,6BAA8D,WAC/C,gCACrB,WN5LM,kBM4LkD,WACjD,iBAA+D,WAC1E,mBNuPa,WMtPb,uBAAoD,gCACd,WAAwC,uBAChE,WAAyB,uBAAyC,WACrE,uBAA+D,mCNjKvD,WMkK2D,mBN+DzE,WM9D6C,CACpD,wBAAkD,WAChD,2BAAoD,WACzC,CAAkB,oBAA+B,WAC1D,CAAO,sBAA+C,WACpD,yBAAwD,WACxD,CAAO,yBAAiD,WAC/B,iCACxB,WNiMM,yBMhMc,WAA2B,oBAC9C,WAAgC,CN0I1B,sDMzIwD,WACzC,mDACiC,YACtD,wDN2KO,WM1KqD,gCNnF9C,WMoF4D,gBAC1E,WNwPM,kCAxYL,WMiJ+C,iCAEhE,WAA8B,CAAkB,4DAE3B,WN9MM,mDMgN3B,WAAiD,+BAEjD,WAAc,CAAkB,kCACA,WAAwB,iBAExD,WAAc,CAAkB,qBAAwB,WAExD,2BAAwD,WACjD,4BAEA,WACP,CAAO,2BAAiD,WAExD,6BACO,WAAgC,CNiN1B,4BM/MN,WAAyB,8BAEhC,WAAO,CAAO,qBAA0C,WACvB,wBACpB,WAAyB,2BAA8B,WACzB,oBAAmC,WAC3D,iBN0PM,WM1PuD,wBACpC,WAAoC,yBACnC,WN0PnB,CAAO,mBM1PiD,WACrC,2BAAqC,WAC7D,sBAA+D,WACvE,mBAAuD,2BAC5B,WAAgC,sBAC7B,WNyanB,CAAO,4BAFd,WMta+C,mBN4ZlD,WM3Z4C,0BN4ZrC,WM3ZmD,4BNqa3C,WMpa6C,CAC/D,yBAAqD,uCACc,WACjE,6BACH,WAAyB,kBAC5B,WAAyB,oBAAwB,WAClB,oBAA8B,YAChD,iBN/RM,WM+RwD,qBNgVlE,WM/U8C,kBACpB,WN+UZ,CM/U8C,sBACzD,WAAyB,CAAO,kBAA6B,WAC/D,CAAO,kBAAoD,4BAChC,WAAoC,uCACE,WACzE,kBAAmD,WACjD,mBNmYM,WMnY+C,oBN5W/C,WM6W+C,CACvD,eAAgC,WAAmB,eACvB,WN5KnB,CAAO,iBM6KT,WNgND,mBM/MD,WAAyB,sBAC7B,WAAgC,CN4T1B,0BM3TmB,WAA2B,iCACJ,WAChD,CAAO,+BN3DC,WM4DgD,CACzD,sDACqD,WACnD,yBAAqD,WAChC,uBAC1B,WNiWM,yBMhWsB,WAA0B,wCACE,WACxB,0BAA8B,WAC3C,CAAkB,yBAAmC,YACtD,iBN/WM,WM+W4D,4BAC1C,WAAwC,sBAExF,8BAAgD,WNzRnB,CAAO,6DM2RG,WNiWZ,CMjW2C,kDACI,WAE1E,kBAAgC,WAAwB,mBACvC,WAAyB,CAAO,kBAA2B,WAC3D,CAAO,yBACf,WAAyB,+BACf,WAAgC,CNzL1B,uBM0LZ,WNoWM,sBMnWF,WN8DD,iBM9D8C,WAE9D,0BACA,WAAuC,2BAA+B,WAEtE,mBAAkB,WAAyB,CAAO,kBACzC,WAAyB,CAAO,oBAC/B,WAAgC,CNlG1B,eMmGN,WAAyB,qBAA2B,WAC7C,kBAA2D,2BAC/C,WNyND,kBMxNb,WAAyB,iBAAgC,WAC3D,kBAAuD,WAC5D,oBAAkD,sCACkB,6BAC1B,WAA4B,yBNtNvD,WMwNb,oBNzCa,WMyCuC,sCAE9B,WNtLnB,CAAO,+BAtJF,WM6UgD,CACzD,gBN1RM,WM0R6C,CACrD,mBNsDM,WMrDJ,uBAAmD,WACpD,uBAAkD,WACxB,qBAA0B,WACxB,uBAA4B,WAC9C,wBAA4D,WAC5C,yBAA0B,WACnB,8BAC9B,WNyFM,2EMrFjB,WAAiC,iDACyB,WACtB,iDACkC,4DAEjC,WAA6B,wBAC3B,WAA+B,iBACvD,WNxLM,oBMyLL,WAAyB,qBAAiC,WACrD,yGNrMC,WMwMoD,2BAE1E,WAAyB,CAAkB,qDAE3B,WAAyB,iCAClB,WACvB,uBNpMsB,WMoMoD,gBAClC,WNjNnB,CAAO,2EMoNhB,WAAyB,0BACxB,WACb,eACA,gDAEA,WAAa,CAAO,uCACK,WN/TD,4CMkUxB,WAAgB,CAAkB,mBAC5B,WACN,wBNnQgB,WMmQ8C,mBACvB,WAA+B,sBN1KzD,WM2K2C,oBACjC,WACvB,sBACA,WAAe,6BAAyD,WACvD,CAAO,gBAAoD,WAC/D,CAAkB,6CAErB,WAAgC,CN0Q1B,eMzQR,WACR,uBAAwC,WAAgC,iBAExE,kCNXuB,WMWqD,mBNzI3D,WM0I+C,iBACxB,WNlVZ,CMkV4C,uBACrC,WAA2B,iBAC1C,WNbD,uBMciB,WN+FZ,CM/FwC,kBACnD,WNuEM,0BMtEC,WAAyB,oBACrC,WAAyB,CAAO,yBACvB,WACjB,wBN5MkB,WM6MX,oBN0Ma,WM1MoC,sBNvapC,WMyaZ,sBAAkD,kCACY,WAC5D,yBAAoD,WAC7B,kBAAyB,WAC3C,sBAAyD,WAChE,eNwPa,WMxPqC,uBNpZtC,WMqZkD,CAC5D,uBAAoD,qCACc,WACjE,uBAAqD,iCN/XzC,WMgYqD,uBNlYvD,WMmYmD,mBAC7B,WAAqB,0BNjY7C,WMkYiD,uBNjYjD,WMkYiD,sBN1bhD,WM2bkD,oBAC7B,WAAiC,gBAC1D,WN+KD,oBM9KF,WAAgC,CNnV1B,qBMoVY,WAAuB,eACxC,WAAyB,oDACiC,WACvD,qBAA2D,kCACN,sCACF,WACtD,qBAAwD,WAC5D,qBAAoD,+BNtIvC,WMuIqD,mBACrC,WAA+B,yBAChC,WAA8B,wBNvcnD,WMwc+C,CACzD,oBNjba,WMiboC,CAC7C,qBAAqD,uCACI,WACrC,oBAAuB,WAEtD,iBACA,WAAO,wBAAiD,WACrC,CAAkB,sBAA6B,WACpD,CAAO,uBAAiD,WACpD,sBAA4D,WAClE,kBN9Va,WM8VyC,iBACtB,WAAsB,oBAC9B,WAA4B,4CAEhD,WAAyB,4BAC1B,WAAgC,CNoC1B,wBMnCF,WNqCD,wBMpCoB,WN5anB,CAAO,sBM6aP,WAAyB,wBACjC,WN/VM,0BMgWgB,WAAyB,0BAClB,WAAgC,mBACjC,WN/FZ,CM+F2C,sBAChD,WN6ED,8BM5EiB,WAA8B,wBNgLrD,WM9KP,qBAAkD,WAC/C,mBNnHa,WMmHwC,sBAEhE,WAAwC,uBAAgC,WACrD,iCAA6D,WACvC,oBAAiC,WAC3D,kBAAyD,8BN0KpD,WMzKkD,mBAC9B,WAAgC,uCACI,qCACA,WAClE,qBAAoD,YAChD,wBAAwD,4EAEE,WAC5D,wDACkD,WACjD,8CAC0B,WAA+B,iDAEd,WACtC,CAAkB,8CACwB,WAClD,0BAAoD,WAC3B,qBAC7B,WACN,yBN0LsB,WM1LoD,2BNhHnD,WMiHqD,wBNpH1D,WMqHgD,0BNlW5C,WMmWoD,mBAE1E,WAAW,CAAO,0BACuB,WN/gBZ,CM+gB6C,iBAC7D,WACb,0BAAiC,WN9gBD,wBMghBhC,8DAEA,6DAEA,4DAC0C,WN3IZ,CM2I8C,qBACvC,WN3OZ,CM2OyC,8CACQ,WACxD,gDACsB,WAAgC,4BACvB,WAA2B,0BACd,oCNjetC,WMkesD,CACrE,0BAAmD,YAC3C,wBAA2D,YAC7D,qBAAyD,kCAExE,WAAmB,6BAA6D,WAC1D,CACtB,yBAAyB,CAAkB,qBAAmC,WAC/D,wBACE,WAAyB,CAAO,yBACpC,WNrQM,iCMuQnB,WAAe,uBAAyD,WACzD,wBACC,WAAyB,mBAAiC,WACvD,mBN1Ra,WM0RgD,oBACtC,WAAkC,kBAC5D,WN3RM,8BM4RG,WN/RD,qCADF,WMiSoD,mBACpC,WAA8B,kBAC7B,WN/FnB,CAAO,kBM+F2C,WACzB,4BACd,WNtUZ,CMsUmC,4BACT,WAAuB,6BNmE/C,WMlEmD,6BACI,gCAC3B,WNrKnB,CAAO,mBMsKvB,WAAgC,CN5U1B,qBM6UL,WAAyB,kBAAgC,WACrC,gBAA2B,WACpD,CAAO,sBAA6C,WAC1B,yBAC3B,WAAyB,CAAO,iBAAmB,WACvC,kBNpQM,WMqQrB,sBACQ,WAAyB,CAAO,qBACpC,WNjdM,yBMkdkB,WAA0B,iBACzB,WN/nBnB,CAAO,2BMgoBG,WN3hBD,qBM2hBuD,WACnC,iBAAqC,oCACA,2BACvD,WNjiBD,yBMkiBW,WAA6B,qBAC9B,WAA4B,mBACnD,WN5NM,yBM6Ne,WAA0B,2BACJ,mCACc,qCACI,WACjE,yBAAmD,WAC1B,4BACrB,WAAyB,CAAO,mBAChC,WAAyB,sBAA8B,WAC7C,CAAkB,uBACjC,WAAyB,oBAAyB,WACvC,mBAA6D,WACpE,uBAAsD,WAC1D,CAAO,mBAA2C,WAC1C,mBN1XM,WM2Xf,6BAAiD,WACxC,2BAA0D,WACrC,8BACF,WNlHZ,CMkHuC,iBAC9C,WAAyB,8BACvB,WAAyB,CAAO,gCACV,WAAgC,oBACvD,WAAgC,CN3C1B,uCAzDD,WMqGoD,4EAEV,WAC1B,gEAE3B,WN/LM,kBMgMP,WAAgC,CNlX1B,mBMmXF,WAAyB,6CACyB,WACtD,uBAAoD,8BN4BpC,WM3BwD,0BACvC,WAAmC,qBACzD,WAAgC,CNxK1B,0BMyKO,WAA0B,4BACd,WNtoBZ,CMsoBkD,iCACnC,wBACtC,WAAyB,kBAA4B,iCACR,WAAgD,+DAExG,WAAgE,uCAEhE,mCACQ,WNvfM,0BMwfoB,WAA0B,4BN/XpC,WMgYwC,CACrD,kBACX,WAAiB,yBAA2D,WAC9D,2BACJ,WAAyB,0CNkCZ,WMjCqD,8CAE1D,WAAyB,wBAAmC,WACpD,CAAkB,yBAAoC,WACzC,mBAA+B,WACvD,qBAAyD,WAC/D,+CNnDU,WMoDiD,mDAE5C,WNzYM,kBM0YjB,WACb,2BAA0E,gCNrXrD,WMsXmD,6EAEQ,WAC/D,CAAkB,+DACuC,WACxD,CAAO,qDAET,WNrtBM,yDACE,WMstBsD,uDAEpC,WNJnB,CAAO,kBMKb,WNHD,mDMIkD,WACxC,CAC1B,mBAAoC,WN5WnB,CAAO,2BM6WH,WACrB,4BAAoE,WAC3D,2BAAmD,WAC1C,oDACgD,mEAG9C,WAAyB,qBAAqC,WACjE,iBACjB,4BAAuD,WAA+C,iBACrF,WACjB,oBAAoB,WN3EM,oBM2EwD,WAElF,sBAAuB,WAAyB,wBAAwC,WAExF,wBAAqB,WAAyB,CAAO,sBAC3C,WN3KM,mBM4KL,WACJ,UACP,iBAAiC,UAAyB,WAC/C,UAAyB,YAA4B,sCNMhC,CMLgD,4DAE9D,eNKM,WMLsD,YAC3D,SACnB,iBAAgB,UAAyB,GAAO,QAA0B,qBACrD,WACrB,4BAAkD,CNF1B,eMEsD,gBACzC,CAAO,MAAsB,SAC1D,iBNpbM,YMqbN,kBN1hBM,CAAO,MM0hBqC,UAC3C,CAAkB,OAAO,WAAkB,SAC/C,YAAgC,CN/O1B,iBMgPN,WAAyB,kBAA4B,sBNhU7C,GMiUiD,iBACrD,gBN7KM,cM6KmD,gBAChC,INhInB,YMgImD,gBACjC,gBAA+B,cACnC,6BFnvBjC,CAAQ,GAAE,eACH,CAAG,aAEV,UACA,aACA,kBACI,CAAE,iBACN,qBAUA,qJG5BA,WACD,CAEG,wBAEF,iBACA,iBACD,cAIC,WAAW,eACH,IACR,kBACD,UAGM,gBAIL,sBAIA,gCAED,gBAGU,gBACT,UAAgB,qBAIJ,CAAM,+BAMlB,wBAIA,CAAM,qBACK,CAAK,iBAEhB,QAAO,eACP,gBAAe,iBAIf,CAAW,sCAGX,qBAIgB,CAChB,gBAAe,CAAG,UAIX,kBADR,uGAOC,iBAPD,qBAO2C,wBAExC,cACA,yBAVJ,CAAC,0BAeE,qBAIe,CACjB,8BAMQ,WACP,CAAW,eAGF,oBACT,uBAAa,CAAoB,wBAG1B,CAAE,qBAGT,qBACD,iBAKe,mBACd,oBACA,SAAQ,6BAER,iDAGA,wBAIO,CADT,YAAY,uFASV,uBADF,qBAAgB,CAGP,eAHT,8CAKa,QACV,kBAID,yBACA,CAAK,qCAEN,cAEW,0DAIO,iBAEjB,yDAGA,8BACA,4CAEA,eACD,gBAGC,mBACA,iBACA,aAAkB,gCAHpB,oCAKI,kCAED,iCAG4B,CAAY,+BACxB,CAAY,4BAC7B,sBACA,UACA,WAAe,CAAC,MACjB,kBAEiB,WAAc,eAC9B,MACA,iBACA,aAAQ,mBACT,gBAEiB,iCAEjB,sBAEiB,YAAc,4BAC9B,sCAGgB,WAAc,uBAC9B,yDAG6B,4BAE7B,oDAKA,kBAUK,CACP,WACK,CACL,4BACE,UAAoB,0BACH,yBACjB,SAAgB,oBAChB,iCACA,iCAMK,qCAGN,6BAQQ,iBACG,YAEV,iBAEA,cACA,MAAU,CApQuE,OAAO,CAqQxF,sBACA,uBAA4B,eAO5B,qBACQ,CAAI,kCAOT,cACL,2BACU,6CAOS,qBACF,iBACf,iBAAgB,cACjB,cAKK,8DAGG,4BAMgB,CAAC,oCAOpB,eACO,iBAEX,WACA,4BACD,cAEe,WACd,cAAe,iBAChB,qBAEe,yEAiBd,cACA,aAAc,CACd,WACK,CAAE,aACK,6BAEN,CAAE,eACC,YACT,8BACO,CAVT,uBAWI,CAAO,MAAE,mBACG,uBAIL,gBACX,oBACO,CAAE,eAAkB,mCAKzB,gBACA,eACD,WAEU,CAAI,QACf,OAAW,CAAI,eACf,UAAe,eACb,oBACD,cAIY,wBACX,0BACY,wBAIH,4BACG,0CAII,cAQhB,eACA,kBAAY,sEAIZ,sBAEA,0BAIA,0BACW,sBAEX,wBAAuB,CAAkB,yBAK1C,eAGC,cACA,WAAc,UACd,iBACA,sBACD,aAGC,YAAa,gCACF,iBACZ,kBAIA,qBAOC,eACD,CAEO,UACN,YACA,qBACD,sBAGC,CAAgB,6BACG,WACnB,qBACA,iBAAmB,4BACD,CAAG,wBACrB,4BAEA,iBACD,kBAEiB,eAChB,yBACA,eACD,WAEW,SACV,mFAKA,YAAkB,CAAO,gBACzB,kBACD,YAGC,6BAIO,WACP,iFAQA,sBACiB,CACjB,WAAS,eACA,oBACT,mBAEA,qBACA,aACA,iBAAa,cATf,gBAAiB,oBAYb,iBACD,gBAID,6BACA,yBACA,YAAS,eACV,WAEE,gBAEF,iBAMW,CAAE,yCAIL,cACK,CACb,aAGM,CAAE,mBACK,2EAEZ,kBACA,iBACA,CAAK,gBAEJ,iBAAG,CAAQ,MACL,CAAE,OATX,6BAQG,CAEiB,4DACd,uBACY,CACZ,oBACA,gBACA,mCACiB,iBAMrB,cAAa,WACL,uEAOH,uBACM,qBACZ,CAEW,eAEX,wBAMiB,2BAEA,CAChB,oBACD,qBAEuB,eAAO,CAAU,sEAOxC,sBAIA,sBAGU,sBACT,CAAY,+BAEb,sBAGW,kBAEF,CACR,mBAAW,qBACZ,CAGW,qBACI,CAAK,4BAEnB,8IAWD,WAID,yBACA,oDAEE,oCACgB,CACjB,sCAGY,cACZ,eAGQ,wCAIS,SACjB,wBAEyB,kBAAkB,YAAmB,UAC7D,gBAAkB,YAClB,wBACQ,qBACR,eAAY,qCAEZ,8BACS,UACA,sCAEV,kDAED,iBAA2B,UAElB,SAFT,iCAES,eAFT,+BAES,CACqB,QACjB,aACP,mBALN,gCAEE,CAAO,aAKD,EAAU,iEAWH,qBAGL,uBAET,uBAGD,oBACS,CAAE,CAAC,oCAOE,gBACL,uBAIK,sCAIG,gBAGZ,uBACa,CACjB,UAMM,WAAY,kBACjB,qBAAW,gBACE,CACZ,sBAGgB,WAAW,kBAEnB,yBAEU,UAAc,WACvB,aACD,0BAGO,eACC,2BAGR,eAGP,wBAGM,kBACN,CAAW,8BAET,qBACW,CAAE,UAKZ,oBAAY,CAAS,gBAAa,iBAAiB,iDAGvD,iCAEU,UACV,CAGH,iBAAa,sBACX,gBACE,wBAIJ,QAAO,UAAY,cAAe,cAChC,aACW,qBAQb,WACS,sBAIP,WAAQ,sBAEG,YACZ,YAEe,uBAEf,cAGC,kBAAS,cAEV,cAGC,YACD,yBAGC,0BAGW,yDAKX,qBAGD,CAEO,wCAEG,eAGV,wBAIA,+BAIA,aAEa,6BAAoB,8BAEjC,gCAME,kBACO,0BAER,6BAQI,CACJ,6BAIA,sCAIQ,eACD,CAAI,QAFb,YAIE", "file": "css/stylesheet.css", "sourcesContent": ["/*!\nPure v1.0.1\nCopyright 2013 Yahoo!\nLicensed under the BSD License.\nhttps://github.com/pure-css/pure/blob/master/LICENSE.md\n*/\n/*!\nnormalize.css v^3.0 | MIT License | git.io/normalize\nCopyright (c) <PERSON> and <PERSON>\n*/\n/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\n\n/**\n * 1. Set default font family to sans-serif.\n * 2. Prevent iOS and IE text size adjust after device orientation change,\n *    without disabling user zoom.\n */\n\nhtml {\n  font-family: sans-serif; /* 1 */\n  -ms-text-size-adjust: 100%; /* 2 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n}\n\n/**\n * Remove default margin.\n */\n\nbody {\n  margin: 0;\n}\n\n/* HTML5 display definitions\n   ========================================================================== */\n\n/**\n * Correct `block` display not defined for any HTML5 element in IE 8/9.\n * Correct `block` display not defined for `details` or `summary` in IE 10/11\n * and Firefox.\n * Correct `block` display not defined for `main` in IE 11.\n */\n\narticle,\naside,\ndetails,\nfigcaption,\nfigure,\nfooter,\nheader,\nhgroup,\nmain,\nmenu,\nnav,\nsection,\nsummary {\n  display: block;\n}\n\n/**\n * 1. Correct `inline-block` display not defined in IE 8/9.\n * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\n */\n\naudio,\ncanvas,\nprogress,\nvideo {\n  display: inline-block; /* 1 */\n  vertical-align: baseline; /* 2 */\n}\n\n/**\n * Prevent modern browsers from displaying `audio` without controls.\n * Remove excess height in iOS 5 devices.\n */\n\naudio:not([controls]) {\n  display: none;\n  height: 0;\n}\n\n/**\n * Address `[hidden]` styling not present in IE 8/9/10.\n * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.\n */\n\n[hidden],\ntemplate {\n  display: none;\n}\n\n/* Links\n   ========================================================================== */\n\n/**\n * Remove the gray background color from active links in IE 10.\n */\n\na {\n  background-color: transparent;\n}\n\n/**\n * Improve readability of focused elements when they are also in an\n * active/hover state.\n */\n\na:active,\na:hover {\n  outline: 0;\n}\n\n/* Text-level semantics\n   ========================================================================== */\n\n/**\n * Address styling not present in IE 8/9/10/11, Safari, and Chrome.\n */\n\nabbr[title] {\n  border-bottom: 1px dotted;\n}\n\n/**\n * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\n */\n\nb,\nstrong {\n  font-weight: bold;\n}\n\n/**\n * Address styling not present in Safari and Chrome.\n */\n\ndfn {\n  font-style: italic;\n}\n\n/**\n * Address variable `h1` font-size and margin within `section` and `article`\n * contexts in Firefox 4+, Safari, and Chrome.\n */\n\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n/**\n * Address styling not present in IE 8/9.\n */\n\nmark {\n  background: #ff0;\n  color: #000;\n}\n\n/**\n * Address inconsistent and variable font size in all browsers.\n */\n\nsmall {\n  font-size: 80%;\n}\n\n/**\n * Prevent `sub` and `sup` affecting `line-height` in all browsers.\n */\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsup {\n  top: -0.5em;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\n/* Embedded content\n   ========================================================================== */\n\n/**\n * Remove border when inside `a` element in IE 8/9/10.\n */\n\nimg {\n  border: 0;\n}\n\n/**\n * Correct overflow not hidden in IE 9/10/11.\n */\n\nsvg:not(:root) {\n  overflow: hidden;\n}\n\n/* Grouping content\n   ========================================================================== */\n\n/**\n * Address margin not present in IE 8/9 and Safari.\n */\n\nfigure {\n  margin: 1em 40px;\n}\n\n/**\n * Address differences between Firefox and other browsers.\n */\n\nhr {\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  height: 0;\n}\n\n/**\n * Contain overflow in all browsers.\n */\n\npre {\n  overflow: auto;\n}\n\n/**\n * Address odd `em`-unit font size rendering in all browsers.\n */\n\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n/* Forms\n   ========================================================================== */\n\n/**\n * Known limitation: by default, Chrome and Safari on OS X allow very limited\n * styling of `select`, unless a `border` property is set.\n */\n\n/**\n * 1. Correct color not being inherited.\n *    Known issue: affects color of disabled elements.\n * 2. Correct font properties not being inherited.\n * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n */\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  color: inherit; /* 1 */\n  font: inherit; /* 2 */\n  margin: 0; /* 3 */\n}\n\n/**\n * Address `overflow` set to `hidden` in IE 8/9/10/11.\n */\n\nbutton {\n  overflow: visible;\n}\n\n/**\n * Address inconsistent `text-transform` inheritance for `button` and `select`.\n * All other form control elements do not inherit `text-transform` values.\n * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.\n * Correct `select` style inheritance in Firefox.\n */\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/**\n * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n *    and `video` controls.\n * 2. Correct inability to style clickable `input` types in iOS.\n * 3. Improve usability and consistency of cursor style between image-type\n *    `input` and others.\n */\n\nbutton,\nhtml input[type=\"button\"], /* 1 */\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  -webkit-appearance: button; /* 2 */\n  cursor: pointer; /* 3 */\n}\n\n/**\n * Re-set default cursor for disabled elements.\n */\n\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n/**\n * Remove inner padding and border in Firefox 4+.\n */\n\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\n/**\n * Address Firefox 4+ setting `line-height` on `input` using `!important` in\n * the UA stylesheet.\n */\n\ninput {\n  line-height: normal;\n}\n\n/**\n * It's recommended that you don't attempt to style these elements.\n * Firefox's implementation doesn't respect box-sizing, padding, or width.\n *\n * 1. Address box sizing set to `content-box` in IE 8/9/10.\n * 2. Remove excess padding in IE 8/9/10.\n */\n\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Fix the cursor style for Chrome's increment/decrement buttons. For certain\n * `font-size` values of the `input`, it causes the cursor style of the\n * decrement button to change from `default` to `text`.\n */\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/**\n * 1. Address `appearance` set to `searchfield` in Safari and Chrome.\n * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.\n */\n\ninput[type=\"search\"] {\n  -webkit-appearance: textfield; /* 1 */\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box; /* 2 */\n}\n\n/**\n * Remove inner padding and search cancel button in Safari and Chrome on OS X.\n * Safari (but not Chrome) clips the cancel button when the search input has\n * padding (and `textfield` appearance).\n */\n\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/**\n * Define consistent border, margin, and padding.\n */\n\nfieldset {\n  border: 1px solid #c0c0c0;\n  margin: 0 2px;\n  padding: 0.35em 0.625em 0.75em;\n}\n\n/**\n * 1. Correct `color` not being inherited in IE 8/9/10/11.\n * 2. Remove padding so people aren't caught out if they zero out fieldsets.\n */\n\nlegend {\n  border: 0; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Remove default vertical scrollbar in IE 8/9/10/11.\n */\n\ntextarea {\n  overflow: auto;\n}\n\n/**\n * Don't inherit the `font-weight` (applied by a rule above).\n * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.\n */\n\noptgroup {\n  font-weight: bold;\n}\n\n/* Tables\n   ========================================================================== */\n\n/**\n * Remove most spacing between table cells.\n */\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n\n/*csslint important:false*/\n\n/* ==========================================================================\n   Pure Base Extras\n   ========================================================================== */\n\n/**\n * Extra rules that Pure adds on top of Normalize.css\n */\n\n/**\n * Always hide an element when it has the `hidden` HTML attribute.\n */\n\n.hidden,\n[hidden] {\n    display: none !important;\n}\n\n/**\n * Add this class to an image to make it fit within it's fluid parent wrapper while maintaining\n * aspect ratio.\n */\n.pure-img {\n    max-width: 100%;\n    height: auto;\n    display: block;\n}\n\n/*csslint regex-selectors:false, known-properties:false, duplicate-properties:false*/\n\n.pure-g {\n    letter-spacing: -0.31em; /* Webkit: collapse white-space between units */\n    *letter-spacing: normal; /* reset IE < 8 */\n    *word-spacing: -0.43em; /* IE < 8: collapse white-space between units */\n    text-rendering: optimizespeed; /* Webkit: fixes text-rendering: optimizeLegibility */\n\n    /*\n    Sets the font stack to fonts known to work properly with the above letter\n    and word spacings. See: https://github.com/pure-css/pure/issues/41/\n\n    The following font stack makes Pure Grids work on all known environments.\n\n    * FreeSans: Ships with many Linux distros, including Ubuntu\n\n    * Arimo: Ships with Chrome OS. Arimo has to be defined before Helvetica and\n      Arial to get picked up by the browser, even though neither is available\n      in Chrome OS.\n\n    * Droid Sans: Ships with all versions of Android.\n\n    * Helvetica, Arial, sans-serif: Common font stack on OS X and Windows.\n    */\n    font-family: FreeSans, Arimo, \"Droid Sans\", Helvetica, Arial, sans-serif;\n\n    /* Use flexbox when possible to avoid `letter-spacing` side-effects. */\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-orient: horizontal;\n    -webkit-box-direction: normal;\n    -webkit-flex-flow: row wrap;\n        -ms-flex-flow: row wrap;\n            flex-flow: row wrap;\n\n    /* Prevents distributing space between rows */\n    -webkit-align-content: flex-start;\n        -ms-flex-line-pack: start;\n            align-content: flex-start;\n}\n\n/* IE10 display: -ms-flexbox (and display: flex in IE 11) does not work inside a table; fall back to block and rely on font hack */\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\ttable .pure-g {\n\t\tdisplay: block;\n\t}\n}\n\n/* Opera as of 12 on Windows needs word-spacing.\n   The \".opera-only\" selector is used to prevent actual prefocus styling\n   and is not required in markup.\n*/\n.opera-only :-o-prefocus,\n.pure-g {\n    word-spacing: -0.43em;\n}\n\n.pure-u {\n    display: inline-block;\n    *display: inline; /* IE < 8: fake inline-block */\n    zoom: 1;\n    letter-spacing: normal;\n    word-spacing: normal;\n    vertical-align: top;\n    text-rendering: auto;\n}\n\n/*\nResets the font family back to the OS/browser's default sans-serif font,\nthis the same font stack that Normalize.css sets for the `body`.\n*/\n.pure-g [class *= \"pure-u\"] {\n    font-family: sans-serif;\n}\n\n.pure-u-1,\n.pure-u-1-1,\n.pure-u-1-2,\n.pure-u-1-3,\n.pure-u-2-3,\n.pure-u-1-4,\n.pure-u-3-4,\n.pure-u-1-5,\n.pure-u-2-5,\n.pure-u-3-5,\n.pure-u-4-5,\n.pure-u-5-5,\n.pure-u-1-6,\n.pure-u-5-6,\n.pure-u-1-8,\n.pure-u-3-8,\n.pure-u-5-8,\n.pure-u-7-8,\n.pure-u-1-12,\n.pure-u-5-12,\n.pure-u-7-12,\n.pure-u-11-12,\n.pure-u-1-24,\n.pure-u-2-24,\n.pure-u-3-24,\n.pure-u-4-24,\n.pure-u-5-24,\n.pure-u-6-24,\n.pure-u-7-24,\n.pure-u-8-24,\n.pure-u-9-24,\n.pure-u-10-24,\n.pure-u-11-24,\n.pure-u-12-24,\n.pure-u-13-24,\n.pure-u-14-24,\n.pure-u-15-24,\n.pure-u-16-24,\n.pure-u-17-24,\n.pure-u-18-24,\n.pure-u-19-24,\n.pure-u-20-24,\n.pure-u-21-24,\n.pure-u-22-24,\n.pure-u-23-24,\n.pure-u-24-24 {\n    display: inline-block;\n    *display: inline;\n    zoom: 1;\n    letter-spacing: normal;\n    word-spacing: normal;\n    vertical-align: top;\n    text-rendering: auto;\n}\n\n.pure-u-1-24 {\n    width: 4.1667%;\n    *width: 4.1357%;\n}\n\n.pure-u-1-12,\n.pure-u-2-24 {\n    width: 8.3333%;\n    *width: 8.3023%;\n}\n\n.pure-u-1-8,\n.pure-u-3-24 {\n    width: 12.5000%;\n    *width: 12.4690%;\n}\n\n.pure-u-1-6,\n.pure-u-4-24 {\n    width: 16.6667%;\n    *width: 16.6357%;\n}\n\n.pure-u-1-5 {\n    width: 20%;\n    *width: 19.9690%;\n}\n\n.pure-u-5-24 {\n    width: 20.8333%;\n    *width: 20.8023%;\n}\n\n.pure-u-1-4,\n.pure-u-6-24 {\n    width: 25%;\n    *width: 24.9690%;\n}\n\n.pure-u-7-24 {\n    width: 29.1667%;\n    *width: 29.1357%;\n}\n\n.pure-u-1-3,\n.pure-u-8-24 {\n    width: 33.3333%;\n    *width: 33.3023%;\n}\n\n.pure-u-3-8,\n.pure-u-9-24 {\n    width: 37.5000%;\n    *width: 37.4690%;\n}\n\n.pure-u-2-5 {\n    width: 40%;\n    *width: 39.9690%;\n}\n\n.pure-u-5-12,\n.pure-u-10-24 {\n    width: 41.6667%;\n    *width: 41.6357%;\n}\n\n.pure-u-11-24 {\n    width: 45.8333%;\n    *width: 45.8023%;\n}\n\n.pure-u-1-2,\n.pure-u-12-24 {\n    width: 50%;\n    *width: 49.9690%;\n}\n\n.pure-u-13-24 {\n    width: 54.1667%;\n    *width: 54.1357%;\n}\n\n.pure-u-7-12,\n.pure-u-14-24 {\n    width: 58.3333%;\n    *width: 58.3023%;\n}\n\n.pure-u-3-5 {\n    width: 60%;\n    *width: 59.9690%;\n}\n\n.pure-u-5-8,\n.pure-u-15-24 {\n    width: 62.5000%;\n    *width: 62.4690%;\n}\n\n.pure-u-2-3,\n.pure-u-16-24 {\n    width: 66.6667%;\n    *width: 66.6357%;\n}\n\n.pure-u-17-24 {\n    width: 70.8333%;\n    *width: 70.8023%;\n}\n\n.pure-u-3-4,\n.pure-u-18-24 {\n    width: 75%;\n    *width: 74.9690%;\n}\n\n.pure-u-19-24 {\n    width: 79.1667%;\n    *width: 79.1357%;\n}\n\n.pure-u-4-5 {\n    width: 80%;\n    *width: 79.9690%;\n}\n\n.pure-u-5-6,\n.pure-u-20-24 {\n    width: 83.3333%;\n    *width: 83.3023%;\n}\n\n.pure-u-7-8,\n.pure-u-21-24 {\n    width: 87.5000%;\n    *width: 87.4690%;\n}\n\n.pure-u-11-12,\n.pure-u-22-24 {\n    width: 91.6667%;\n    *width: 91.6357%;\n}\n\n.pure-u-23-24 {\n    width: 95.8333%;\n    *width: 95.8023%;\n}\n\n.pure-u-1,\n.pure-u-1-1,\n.pure-u-5-5,\n.pure-u-24-24 {\n    width: 100%;\n}\n.pure-button {\n    /* Structure */\n    display: inline-block;\n    zoom: 1;\n    line-height: normal;\n    white-space: nowrap;\n    vertical-align: middle;\n    text-align: center;\n    cursor: pointer;\n    -webkit-user-drag: none;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n        -ms-user-select: none;\n            user-select: none;\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n}\n\n/* Firefox: Get rid of the inner focus border */\n.pure-button::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\n\n/* Inherit .pure-g styles */\n.pure-button-group {\n    letter-spacing: -0.31em; /* Webkit: collapse white-space between units */\n    *letter-spacing: normal; /* reset IE < 8 */\n    *word-spacing: -0.43em; /* IE < 8: collapse white-space between units */\n    text-rendering: optimizespeed; /* Webkit: fixes text-rendering: optimizeLegibility */\n}\n\n.opera-only :-o-prefocus,\n.pure-button-group {\n    word-spacing: -0.43em;\n}\n\n.pure-button-group .pure-button {\n    letter-spacing: normal;\n    word-spacing: normal;\n    vertical-align: top;\n    text-rendering: auto;\n}\n\n/*csslint outline-none:false*/\n\n.pure-button {\n    font-family: inherit;\n    font-size: 100%;\n    padding: 0.5em 1em;\n    color: #444; /* rgba not supported (IE 8) */\n    color: rgba(0, 0, 0, 0.80); /* rgba supported */\n    border: 1px solid #999;  /*IE 6/7/8*/\n    border: none rgba(0, 0, 0, 0);  /*IE9 + everything else*/\n    background-color: #E6E6E6;\n    text-decoration: none;\n    border-radius: 2px;\n}\n\n.pure-button-hover,\n.pure-button:hover,\n.pure-button:focus {\n    background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(40%, rgba(0,0,0, 0.05)), to(rgba(0,0,0, 0.10)));\n    background-image: -webkit-linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.10));\n    background-image: linear-gradient(transparent, rgba(0,0,0, 0.05) 40%, rgba(0,0,0, 0.10));\n}\n.pure-button:focus {\n    outline: 0;\n}\n.pure-button-active,\n.pure-button:active {\n    -webkit-box-shadow: 0 0 0 1px rgba(0,0,0, 0.15) inset, 0 0 6px rgba(0,0,0, 0.20) inset;\n            box-shadow: 0 0 0 1px rgba(0,0,0, 0.15) inset, 0 0 6px rgba(0,0,0, 0.20) inset;\n    border-color: #000;\n}\n\n.pure-button[disabled],\n.pure-button-disabled,\n.pure-button-disabled:hover,\n.pure-button-disabled:focus,\n.pure-button-disabled:active {\n    border: none;\n    background-image: none;\n    /* csslint ignore:start */\n    filter: alpha(opacity=40);\n    /* csslint ignore:end */\n    opacity: 0.40;\n    cursor: not-allowed;\n    -webkit-box-shadow: none;\n            box-shadow: none;\n    pointer-events: none;\n}\n\n.pure-button-hidden {\n    display: none;\n}\n\n.pure-button-primary,\n.pure-button-selected,\na.pure-button-primary,\na.pure-button-selected {\n    background-color: rgb(0, 120, 231);\n    color: #fff;\n}\n\n/* Button Groups */\n.pure-button-group .pure-button {\n    margin: 0;\n    border-radius: 0;\n    border-right: 1px solid #111;  /* fallback color for rgba() for IE7/8 */\n    border-right: 1px solid rgba(0, 0, 0, 0.2);\n\n}\n\n.pure-button-group .pure-button:first-child {\n    border-top-left-radius: 2px;\n    border-bottom-left-radius: 2px;\n}\n.pure-button-group .pure-button:last-child {\n    border-top-right-radius: 2px;\n    border-bottom-right-radius: 2px;\n    border-right: none;\n}\n\n/*csslint box-model:false*/\n/*\nBox-model set to false because we're setting a height on select elements, which\nalso have border and padding. This is done because some browsers don't render\nthe padding. We explicitly set the box-model for select elements to border-box,\nso we can ignore the csslint warning.\n*/\n\n.pure-form input[type=\"text\"],\n.pure-form input[type=\"password\"],\n.pure-form input[type=\"email\"],\n.pure-form input[type=\"url\"],\n.pure-form input[type=\"date\"],\n.pure-form input[type=\"month\"],\n.pure-form input[type=\"time\"],\n.pure-form input[type=\"datetime\"],\n.pure-form input[type=\"datetime-local\"],\n.pure-form input[type=\"week\"],\n.pure-form input[type=\"number\"],\n.pure-form input[type=\"search\"],\n.pure-form input[type=\"tel\"],\n.pure-form input[type=\"color\"],\n.pure-form select,\n.pure-form textarea {\n    padding: 0.5em 0.6em;\n    display: inline-block;\n    border: 1px solid #ccc;\n    -webkit-box-shadow: inset 0 1px 3px #ddd;\n            box-shadow: inset 0 1px 3px #ddd;\n    border-radius: 4px;\n    vertical-align: middle;\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n}\n\n/*\nNeed to separate out the :not() selector from the rest of the CSS 2.1 selectors\nsince IE8 won't execute CSS that contains a CSS3 selector.\n*/\n.pure-form input:not([type]) {\n    padding: 0.5em 0.6em;\n    display: inline-block;\n    border: 1px solid #ccc;\n    -webkit-box-shadow: inset 0 1px 3px #ddd;\n            box-shadow: inset 0 1px 3px #ddd;\n    border-radius: 4px;\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n}\n\n\n/* Chrome (as of v.32/34 on OS X) needs additional room for color to display. */\n/* May be able to remove this tweak as color inputs become more standardized across browsers. */\n.pure-form input[type=\"color\"] {\n    padding: 0.2em 0.5em;\n}\n\n\n.pure-form input[type=\"text\"]:focus,\n.pure-form input[type=\"password\"]:focus,\n.pure-form input[type=\"email\"]:focus,\n.pure-form input[type=\"url\"]:focus,\n.pure-form input[type=\"date\"]:focus,\n.pure-form input[type=\"month\"]:focus,\n.pure-form input[type=\"time\"]:focus,\n.pure-form input[type=\"datetime\"]:focus,\n.pure-form input[type=\"datetime-local\"]:focus,\n.pure-form input[type=\"week\"]:focus,\n.pure-form input[type=\"number\"]:focus,\n.pure-form input[type=\"search\"]:focus,\n.pure-form input[type=\"tel\"]:focus,\n.pure-form input[type=\"color\"]:focus,\n.pure-form select:focus,\n.pure-form textarea:focus {\n    outline: 0;\n    border-color: #129FEA;\n}\n\n/*\nNeed to separate out the :not() selector from the rest of the CSS 2.1 selectors\nsince IE8 won't execute CSS that contains a CSS3 selector.\n*/\n.pure-form input:not([type]):focus {\n    outline: 0;\n    border-color: #129FEA;\n}\n\n.pure-form input[type=\"file\"]:focus,\n.pure-form input[type=\"radio\"]:focus,\n.pure-form input[type=\"checkbox\"]:focus {\n    outline: thin solid #129FEA;\n    outline: 1px auto #129FEA;\n}\n.pure-form .pure-checkbox,\n.pure-form .pure-radio {\n    margin: 0.5em 0;\n    display: block;\n}\n\n.pure-form input[type=\"text\"][disabled],\n.pure-form input[type=\"password\"][disabled],\n.pure-form input[type=\"email\"][disabled],\n.pure-form input[type=\"url\"][disabled],\n.pure-form input[type=\"date\"][disabled],\n.pure-form input[type=\"month\"][disabled],\n.pure-form input[type=\"time\"][disabled],\n.pure-form input[type=\"datetime\"][disabled],\n.pure-form input[type=\"datetime-local\"][disabled],\n.pure-form input[type=\"week\"][disabled],\n.pure-form input[type=\"number\"][disabled],\n.pure-form input[type=\"search\"][disabled],\n.pure-form input[type=\"tel\"][disabled],\n.pure-form input[type=\"color\"][disabled],\n.pure-form select[disabled],\n.pure-form textarea[disabled] {\n    cursor: not-allowed;\n    background-color: #eaeded;\n    color: #cad2d3;\n}\n\n/*\nNeed to separate out the :not() selector from the rest of the CSS 2.1 selectors\nsince IE8 won't execute CSS that contains a CSS3 selector.\n*/\n.pure-form input:not([type])[disabled] {\n    cursor: not-allowed;\n    background-color: #eaeded;\n    color: #cad2d3;\n}\n.pure-form input[readonly],\n.pure-form select[readonly],\n.pure-form textarea[readonly] {\n    background-color: #eee; /* menu hover bg color */\n    color: #777; /* menu text color */\n    border-color: #ccc;\n}\n\n.pure-form input:focus:invalid,\n.pure-form textarea:focus:invalid,\n.pure-form select:focus:invalid {\n    color: #b94a48;\n    border-color: #e9322d;\n}\n.pure-form input[type=\"file\"]:focus:invalid:focus,\n.pure-form input[type=\"radio\"]:focus:invalid:focus,\n.pure-form input[type=\"checkbox\"]:focus:invalid:focus {\n    outline-color: #e9322d;\n}\n.pure-form select {\n    /* Normalizes the height; padding is not sufficient. */\n    height: 2.25em;\n    border: 1px solid #ccc;\n    background-color: white;\n}\n.pure-form select[multiple] {\n    height: auto;\n}\n.pure-form label {\n    margin: 0.5em 0 0.2em;\n}\n.pure-form fieldset {\n    margin: 0;\n    padding: 0.35em 0 0.75em;\n    border: 0;\n}\n.pure-form legend {\n    display: block;\n    width: 100%;\n    padding: 0.3em 0;\n    margin-bottom: 0.3em;\n    color: #333;\n    border-bottom: 1px solid #e5e5e5;\n}\n\n.pure-form-stacked input[type=\"text\"],\n.pure-form-stacked input[type=\"password\"],\n.pure-form-stacked input[type=\"email\"],\n.pure-form-stacked input[type=\"url\"],\n.pure-form-stacked input[type=\"date\"],\n.pure-form-stacked input[type=\"month\"],\n.pure-form-stacked input[type=\"time\"],\n.pure-form-stacked input[type=\"datetime\"],\n.pure-form-stacked input[type=\"datetime-local\"],\n.pure-form-stacked input[type=\"week\"],\n.pure-form-stacked input[type=\"number\"],\n.pure-form-stacked input[type=\"search\"],\n.pure-form-stacked input[type=\"tel\"],\n.pure-form-stacked input[type=\"color\"],\n.pure-form-stacked input[type=\"file\"],\n.pure-form-stacked select,\n.pure-form-stacked label,\n.pure-form-stacked textarea {\n    display: block;\n    margin: 0.25em 0;\n}\n\n/*\nNeed to separate out the :not() selector from the rest of the CSS 2.1 selectors\nsince IE8 won't execute CSS that contains a CSS3 selector.\n*/\n.pure-form-stacked input:not([type]) {\n    display: block;\n    margin: 0.25em 0;\n}\n.pure-form-aligned input,\n.pure-form-aligned textarea,\n.pure-form-aligned select,\n/* NOTE: pure-help-inline is deprecated. Use .pure-form-message-inline instead. */\n.pure-form-aligned .pure-help-inline,\n.pure-form-message-inline {\n    display: inline-block;\n    *display: inline;\n    *zoom: 1;\n    vertical-align: middle;\n}\n.pure-form-aligned textarea {\n    vertical-align: top;\n}\n\n/* Aligned Forms */\n.pure-form-aligned .pure-control-group {\n    margin-bottom: 0.5em;\n}\n.pure-form-aligned .pure-control-group label {\n    text-align: right;\n    display: inline-block;\n    vertical-align: middle;\n    width: 10em;\n    margin: 0 1em 0 0;\n}\n.pure-form-aligned .pure-controls {\n    margin: 1.5em 0 0 11em;\n}\n\n/* Rounded Inputs */\n.pure-form input.pure-input-rounded,\n.pure-form .pure-input-rounded {\n    border-radius: 2em;\n    padding: 0.5em 1em;\n}\n\n/* Grouped Inputs */\n.pure-form .pure-group fieldset {\n    margin-bottom: 10px;\n}\n.pure-form .pure-group input,\n.pure-form .pure-group textarea {\n    display: block;\n    padding: 10px;\n    margin: 0 0 -1px;\n    border-radius: 0;\n    position: relative;\n    top: -1px;\n}\n.pure-form .pure-group input:focus,\n.pure-form .pure-group textarea:focus {\n    z-index: 3;\n}\n.pure-form .pure-group input:first-child,\n.pure-form .pure-group textarea:first-child {\n    top: 1px;\n    border-radius: 4px 4px 0 0;\n    margin: 0;\n}\n.pure-form .pure-group input:first-child:last-child,\n.pure-form .pure-group textarea:first-child:last-child {\n    top: 1px;\n    border-radius: 4px;\n    margin: 0;\n}\n.pure-form .pure-group input:last-child,\n.pure-form .pure-group textarea:last-child {\n    top: -2px;\n    border-radius: 0 0 4px 4px;\n    margin: 0;\n}\n.pure-form .pure-group button {\n    margin: 0.35em 0;\n}\n\n.pure-form .pure-input-1 {\n    width: 100%;\n}\n.pure-form .pure-input-3-4 {\n    width: 75%;\n}\n.pure-form .pure-input-2-3 {\n    width: 66%;\n}\n.pure-form .pure-input-1-2 {\n    width: 50%;\n}\n.pure-form .pure-input-1-3 {\n    width: 33%;\n}\n.pure-form .pure-input-1-4 {\n    width: 25%;\n}\n\n/* Inline help for forms */\n/* NOTE: pure-help-inline is deprecated. Use .pure-form-message-inline instead. */\n.pure-form .pure-help-inline,\n.pure-form-message-inline {\n    display: inline-block;\n    padding-left: 0.3em;\n    color: #666;\n    vertical-align: middle;\n    font-size: 0.875em;\n}\n\n/* Block help for forms */\n.pure-form-message {\n    display: block;\n    color: #666;\n    font-size: 0.875em;\n}\n\n@media only screen and (max-width : 480px) {\n    .pure-form button[type=\"submit\"] {\n        margin: 0.7em 0 0;\n    }\n\n    .pure-form input:not([type]),\n    .pure-form input[type=\"text\"],\n    .pure-form input[type=\"password\"],\n    .pure-form input[type=\"email\"],\n    .pure-form input[type=\"url\"],\n    .pure-form input[type=\"date\"],\n    .pure-form input[type=\"month\"],\n    .pure-form input[type=\"time\"],\n    .pure-form input[type=\"datetime\"],\n    .pure-form input[type=\"datetime-local\"],\n    .pure-form input[type=\"week\"],\n    .pure-form input[type=\"number\"],\n    .pure-form input[type=\"search\"],\n    .pure-form input[type=\"tel\"],\n    .pure-form input[type=\"color\"],\n    .pure-form label {\n        margin-bottom: 0.3em;\n        display: block;\n    }\n\n    .pure-group input:not([type]),\n    .pure-group input[type=\"text\"],\n    .pure-group input[type=\"password\"],\n    .pure-group input[type=\"email\"],\n    .pure-group input[type=\"url\"],\n    .pure-group input[type=\"date\"],\n    .pure-group input[type=\"month\"],\n    .pure-group input[type=\"time\"],\n    .pure-group input[type=\"datetime\"],\n    .pure-group input[type=\"datetime-local\"],\n    .pure-group input[type=\"week\"],\n    .pure-group input[type=\"number\"],\n    .pure-group input[type=\"search\"],\n    .pure-group input[type=\"tel\"],\n    .pure-group input[type=\"color\"] {\n        margin-bottom: 0;\n    }\n\n    .pure-form-aligned .pure-control-group label {\n        margin-bottom: 0.3em;\n        text-align: left;\n        display: block;\n        width: 100%;\n    }\n\n    .pure-form-aligned .pure-controls {\n        margin: 1.5em 0 0 0;\n    }\n\n    /* NOTE: pure-help-inline is deprecated. Use .pure-form-message-inline instead. */\n    .pure-form .pure-help-inline,\n    .pure-form-message-inline,\n    .pure-form-message {\n        display: block;\n        font-size: 0.75em;\n        /* Increased bottom padding to make it group with its related input element. */\n        padding: 0.2em 0 0.8em;\n    }\n}\n\n/*csslint adjoining-classes: false, box-model:false*/\n.pure-menu {\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n}\n\n.pure-menu-fixed {\n    position: fixed;\n    left: 0;\n    top: 0;\n    z-index: 3;\n}\n\n.pure-menu-list,\n.pure-menu-item {\n    position: relative;\n}\n\n.pure-menu-list {\n    list-style: none;\n    margin: 0;\n    padding: 0;\n}\n\n.pure-menu-item {\n    padding: 0;\n    margin: 0;\n    height: 100%;\n}\n\n.pure-menu-link,\n.pure-menu-heading {\n    display: block;\n    text-decoration: none;\n    white-space: nowrap;\n}\n\n/* HORIZONTAL MENU */\n.pure-menu-horizontal {\n    width: 100%;\n    white-space: nowrap;\n}\n\n.pure-menu-horizontal .pure-menu-list {\n    display: inline-block;\n}\n\n/* Initial menus should be inline-block so that they are horizontal */\n.pure-menu-horizontal .pure-menu-item,\n.pure-menu-horizontal .pure-menu-heading,\n.pure-menu-horizontal .pure-menu-separator {\n    display: inline-block;\n    *display: inline;\n    zoom: 1;\n    vertical-align: middle;\n}\n\n/* Submenus should still be display: block; */\n.pure-menu-item .pure-menu-item {\n    display: block;\n}\n\n.pure-menu-children {\n    display: none;\n    position: absolute;\n    left: 100%;\n    top: 0;\n    margin: 0;\n    padding: 0;\n    z-index: 3;\n}\n\n.pure-menu-horizontal .pure-menu-children {\n    left: 0;\n    top: auto;\n    width: inherit;\n}\n\n.pure-menu-allow-hover:hover > .pure-menu-children,\n.pure-menu-active > .pure-menu-children {\n    display: block;\n    position: absolute;\n}\n\n/* Vertical Menus - show the dropdown arrow */\n.pure-menu-has-children > .pure-menu-link:after {\n    padding-left: 0.5em;\n    content: \"\\25B8\";\n    font-size: small;\n}\n\n/* Horizontal Menus - show the dropdown arrow */\n.pure-menu-horizontal .pure-menu-has-children > .pure-menu-link:after {\n    content: \"\\25BE\";\n}\n\n/* scrollable menus */\n.pure-menu-scrollable {\n    overflow-y: scroll;\n    overflow-x: hidden;\n}\n\n.pure-menu-scrollable .pure-menu-list {\n    display: block;\n}\n\n.pure-menu-horizontal.pure-menu-scrollable .pure-menu-list {\n    display: inline-block;\n}\n\n.pure-menu-horizontal.pure-menu-scrollable {\n    white-space: nowrap;\n    overflow-y: hidden;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n    /* a little extra padding for this style to allow for scrollbars */\n    padding: .5em 0;\n}\n\n/* misc default styling */\n\n.pure-menu-separator,\n.pure-menu-horizontal .pure-menu-children .pure-menu-separator {\n    background-color: #ccc;\n    height: 1px;\n    margin: .3em 0;\n}\n\n.pure-menu-horizontal .pure-menu-separator {\n    width: 1px;\n    height: 1.3em;\n    margin: 0 .3em ;\n}\n\n/* Need to reset the separator since submenu is vertical */\n.pure-menu-horizontal .pure-menu-children .pure-menu-separator {\n    display: block;\n    width: auto;\n}\n\n.pure-menu-heading {\n    text-transform: uppercase;\n    color: #565d64;\n}\n\n.pure-menu-link {\n    color: #777;\n}\n\n.pure-menu-children {\n    background-color: #fff;\n}\n\n.pure-menu-link,\n.pure-menu-disabled,\n.pure-menu-heading {\n    padding: .5em 1em;\n}\n\n.pure-menu-disabled {\n    opacity: .5;\n}\n\n.pure-menu-disabled .pure-menu-link:hover {\n    background-color: transparent;\n}\n\n.pure-menu-active > .pure-menu-link,\n.pure-menu-link:hover,\n.pure-menu-link:focus {\n    background-color: #eee;\n}\n\n.pure-menu-selected > .pure-menu-link,\n.pure-menu-selected > .pure-menu-link:visited {\n    color: #000;\n}\n\n.pure-table {\n    /* Remove spacing between table cells (from Normalize.css) */\n    border-collapse: collapse;\n    border-spacing: 0;\n    empty-cells: show;\n    border: 1px solid #cbcbcb;\n}\n\n.pure-table caption {\n    color: #000;\n    font: italic 85%/1 arial, sans-serif;\n    padding: 1em 0;\n    text-align: center;\n}\n\n.pure-table td,\n.pure-table th {\n    border-left: 1px solid #cbcbcb;/*  inner column border */\n    border-width: 0 0 0 1px;\n    font-size: inherit;\n    margin: 0;\n    overflow: visible; /*to make ths where the title is really long work*/\n    padding: 0.5em 1em; /* cell padding */\n}\n\n.pure-table thead {\n    background-color: #e0e0e0;\n    color: #000;\n    text-align: left;\n    vertical-align: bottom;\n}\n\n/*\nstriping:\n   even - #fff (white)\n   odd  - #f2f2f2 (light gray)\n*/\n.pure-table td {\n    background-color: transparent;\n}\n.pure-table-odd td {\n    background-color: #f2f2f2;\n}\n\n/* nth-child selector for modern browsers */\n.pure-table-striped tr:nth-child(2n-1) td {\n    background-color: #f2f2f2;\n}\n\n/* BORDERED TABLES */\n.pure-table-bordered td {\n    border-bottom: 1px solid #cbcbcb;\n}\n.pure-table-bordered tbody > tr:last-child > td {\n    border-bottom-width: 0;\n}\n\n\n/* HORIZONTAL BORDERED TABLES */\n\n.pure-table-horizontal td,\n.pure-table-horizontal th {\n    border-width: 0 0 1px 0;\n    border-bottom: 1px solid #cbcbcb;\n}\n.pure-table-horizontal tbody > tr:last-child > td {\n    border-bottom-width: 0;\n}\n", "/**\n * selectize.css (v0.12.6)\n * Copyright (c) 2013–2015 <PERSON> & contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\n.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {\n  visibility: visible !important;\n  background: #f2f2f2 !important;\n  background: rgba(0, 0, 0, 0.06) !important;\n  border: 0 none !important;\n  -webkit-box-shadow: inset 0 0 12px 4px #fff;\n  box-shadow: inset 0 0 12px 4px #fff;\n}\n.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {\n  content: '!';\n  visibility: hidden;\n}\n.selectize-control.plugin-drag_drop .ui-sortable-helper {\n  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n.selectize-dropdown-header {\n  position: relative;\n  padding: 5px 8px;\n  border-bottom: 1px solid #d0d0d0;\n  background: #f8f8f8;\n  -webkit-border-radius: 3px 3px 0 0;\n  -moz-border-radius: 3px 3px 0 0;\n  border-radius: 3px 3px 0 0;\n}\n.selectize-dropdown-header-close {\n  position: absolute;\n  right: 8px;\n  top: 50%;\n  color: #303030;\n  opacity: 0.4;\n  margin-top: -12px;\n  line-height: 20px;\n  font-size: 20px !important;\n}\n.selectize-dropdown-header-close:hover {\n  color: #000000;\n}\n.selectize-dropdown.plugin-optgroup_columns .optgroup {\n  border-right: 1px solid #f2f2f2;\n  border-top: 0 none;\n  float: left;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {\n  border-right: 0 none;\n}\n.selectize-dropdown.plugin-optgroup_columns .optgroup:before {\n  display: none;\n}\n.selectize-dropdown.plugin-optgroup_columns .optgroup-header {\n  border-top: 0 none;\n}\n.selectize-control.plugin-remove_button [data-value] {\n  position: relative;\n  padding-right: 24px !important;\n}\n.selectize-control.plugin-remove_button [data-value] .remove {\n  z-index: 1;\n  /* fixes ie bug (see #392) */\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  width: 17px;\n  text-align: center;\n  font-weight: bold;\n  font-size: 12px;\n  color: inherit;\n  text-decoration: none;\n  vertical-align: middle;\n  display: inline-block;\n  padding: 2px 0 0 0;\n  border-left: 1px solid #d0d0d0;\n  -webkit-border-radius: 0 2px 2px 0;\n  -moz-border-radius: 0 2px 2px 0;\n  border-radius: 0 2px 2px 0;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.selectize-control.plugin-remove_button [data-value] .remove:hover {\n  background: rgba(0, 0, 0, 0.05);\n}\n.selectize-control.plugin-remove_button [data-value].active .remove {\n  border-left-color: #cacaca;\n}\n.selectize-control.plugin-remove_button .disabled [data-value] .remove:hover {\n  background: none;\n}\n.selectize-control.plugin-remove_button .disabled [data-value] .remove {\n  border-left-color: #ffffff;\n}\n.selectize-control.plugin-remove_button .remove-single {\n  position: absolute;\n  right: 0;\n  top: 0;\n  font-size: 23px;\n}\n.selectize-control {\n  position: relative;\n}\n.selectize-dropdown,\n.selectize-input,\n.selectize-input input {\n  color: #303030;\n  font-family: inherit;\n  font-size: 13px;\n  line-height: 18px;\n  -webkit-font-smoothing: inherit;\n}\n.selectize-input,\n.selectize-control.single .selectize-input.input-active {\n  background: #fff;\n  cursor: text;\n  display: inline-block;\n}\n.selectize-input {\n  border: 1px solid #d0d0d0;\n  padding: 8px 8px;\n  display: inline-block;\n  width: 100%;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);\n  -webkit-border-radius: 3px;\n  -moz-border-radius: 3px;\n  border-radius: 3px;\n}\n.selectize-control.multi .selectize-input.has-items {\n  padding: 6px 8px 3px;\n}\n.selectize-input.full {\n  background-color: #fff;\n}\n.selectize-input.disabled,\n.selectize-input.disabled * {\n  cursor: default !important;\n}\n.selectize-input.focus {\n  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);\n}\n.selectize-input.dropdown-active {\n  -webkit-border-radius: 3px 3px 0 0;\n  -moz-border-radius: 3px 3px 0 0;\n  border-radius: 3px 3px 0 0;\n}\n.selectize-input > * {\n  vertical-align: baseline;\n  display: -moz-inline-stack;\n  display: inline-block;\n  zoom: 1;\n  *display: inline;\n}\n.selectize-control.multi .selectize-input > div {\n  cursor: pointer;\n  margin: 0 3px 3px 0;\n  padding: 2px 6px;\n  background: #f2f2f2;\n  color: #303030;\n  border: 0 solid #d0d0d0;\n}\n.selectize-control.multi .selectize-input > div.active {\n  background: #e8e8e8;\n  color: #303030;\n  border: 0 solid #cacaca;\n}\n.selectize-control.multi .selectize-input.disabled > div,\n.selectize-control.multi .selectize-input.disabled > div.active {\n  color: #7d7d7d;\n  background: #ffffff;\n  border: 0 solid #ffffff;\n}\n.selectize-input > input {\n  display: inline-block !important;\n  padding: 0 !important;\n  min-height: 0 !important;\n  max-height: none !important;\n  max-width: 100% !important;\n  margin: 0 2px 0 0 !important;\n  text-indent: 0 !important;\n  border: 0 none !important;\n  background: none !important;\n  line-height: inherit !important;\n  -webkit-user-select: auto !important;\n  -webkit-box-shadow: none !important;\n  box-shadow: none !important;\n}\n.selectize-input > input::-ms-clear {\n  display: none;\n}\n.selectize-input > input:focus {\n  outline: none !important;\n}\n.selectize-input::after {\n  content: ' ';\n  display: block;\n  clear: left;\n}\n.selectize-input.dropdown-active::before {\n  content: ' ';\n  display: block;\n  position: absolute;\n  background: #f0f0f0;\n  height: 1px;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.selectize-dropdown {\n  position: absolute;\n  z-index: 10;\n  border: 1px solid #d0d0d0;\n  background: #fff;\n  margin: -1px 0 0 0;\n  border-top: 0 none;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  -webkit-border-radius: 0 0 3px 3px;\n  -moz-border-radius: 0 0 3px 3px;\n  border-radius: 0 0 3px 3px;\n}\n.selectize-dropdown [data-selectable] {\n  cursor: pointer;\n  overflow: hidden;\n}\n.selectize-dropdown [data-selectable] .highlight {\n  background: rgba(125, 168, 208, 0.2);\n  -webkit-border-radius: 1px;\n  -moz-border-radius: 1px;\n  border-radius: 1px;\n}\n.selectize-dropdown .option,\n.selectize-dropdown .optgroup-header {\n  padding: 5px 8px;\n}\n.selectize-dropdown .option,\n.selectize-dropdown [data-disabled],\n.selectize-dropdown [data-disabled] [data-selectable].option {\n  cursor: inherit;\n  opacity: 0.5;\n}\n.selectize-dropdown [data-selectable].option {\n  opacity: 1;\n}\n.selectize-dropdown .optgroup:first-child .optgroup-header {\n  border-top: 0 none;\n}\n.selectize-dropdown .optgroup-header {\n  color: #303030;\n  background: #fff;\n  cursor: default;\n}\n.selectize-dropdown .active {\n  background-color: #f5fafd;\n  color: #495c68;\n}\n.selectize-dropdown .active.create {\n  color: #495c68;\n}\n.selectize-dropdown .create {\n  color: rgba(48, 48, 48, 0.5);\n}\n.selectize-dropdown-content {\n  overflow-y: auto;\n  overflow-x: hidden;\n  max-height: 200px;\n  -webkit-overflow-scrolling: touch;\n}\n.selectize-control.single .selectize-input,\n.selectize-control.single .selectize-input input {\n  cursor: pointer;\n}\n.selectize-control.single .selectize-input.input-active,\n.selectize-control.single .selectize-input.input-active input {\n  cursor: text;\n}\n.selectize-control.single .selectize-input:after {\n  content: ' ';\n  display: block;\n  position: absolute;\n  top: 50%;\n  right: 15px;\n  margin-top: -3px;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 5px 5px 0 5px;\n  border-color: #808080 transparent transparent transparent;\n}\n.selectize-control.single .selectize-input.dropdown-active:after {\n  margin-top: -4px;\n  border-width: 0 5px 5px 5px;\n  border-color: transparent transparent #808080 transparent;\n}\n.selectize-control.rtl.single .selectize-input:after {\n  left: 15px;\n  right: auto;\n}\n.selectize-control.rtl .selectize-input > input {\n  margin: 0 4px 0 -2px !important;\n}\n.selectize-control .selectize-input.disabled {\n  opacity: 0.5;\n  background-color: #fafafa;\n}\n", "/*\n\nZenburn style from voldmar.ru (c) <PERSON> <<EMAIL>>\nbased on dark.css by <PERSON>\n\n*/\n\n.hljs {\n  display: block;\n  overflow-x: auto;\n  padding: 0.5em;\n  background: #3f3f3f;\n  color: #dcdcdc;\n}\n\n.hljs-keyword,\n.hljs-selector-tag,\n.hljs-tag {\n  color: #e3ceab;\n}\n\n.hljs-template-tag {\n  color: #dcdcdc;\n}\n\n.hljs-number {\n  color: #8cd0d3;\n}\n\n.hljs-variable,\n.hljs-template-variable,\n.hljs-attribute {\n  color: #efdcbc;\n}\n\n.hljs-literal {\n  color: #efefaf;\n}\n\n.hljs-subst {\n  color: #8f8f8f;\n}\n\n.hljs-title,\n.hljs-name,\n.hljs-selector-id,\n.hljs-selector-class,\n.hljs-section,\n.hljs-type {\n  color: #efef8f;\n}\n\n.hljs-symbol,\n.hljs-bullet,\n.hljs-link {\n  color: #dca3a3;\n}\n\n.hljs-deletion,\n.hljs-string,\n.hljs-built_in,\n.hljs-builtin-name {\n  color: #cc9393;\n}\n\n.hljs-addition,\n.hljs-comment,\n.hljs-quote,\n.hljs-meta {\n  color: #7f9f7f;\n}\n\n\n.hljs-emphasis {\n  font-style: italic;\n}\n\n.hljs-strong {\n  font-weight: bold;\n}\n", "/* http://meyerweb.com/eric/tools/css/reset/\n   v5.0.1 | 20191019\n   License: none (public domain)\n*/\n\nhtml, body, div, span, applet, object, iframe,\nh1, h2, h3, h4, h5, h6, p, blockquote, pre,\na, abbr, acronym, address, big, cite, code,\ndel, dfn, em, img, ins, kbd, q, s, samp,\nsmall, strike, strong, sub, sup, tt, var,\nb, u, i, center,\ndl, dt, dd, menu, ol, ul, li,\nfieldset, form, label, legend,\ntable, caption, tbody, tfoot, thead, tr, th, td,\narticle, aside, canvas, details, embed,\nfigure, figcaption, footer, header, hgroup,\nmain, menu, nav, output, ruby, section, summary,\ntime, mark, audio, video {\n\tmargin: 0;\n\tpadding: 0;\n\tborder: 0;\n\tfont-size: 100%;\n\tfont: inherit;\n\tvertical-align: baseline;\n}\n/* HTML5 display-role reset for older browsers */\narticle, aside, details, figcaption, figure,\nfooter, header, hgroup, main, menu, nav, section {\n\tdisplay: block;\n}\n/* HTML5 hidden-attribute fix for newer browsers */\n*[hidden] {\n    display: none;\n}\nbody {\n\tline-height: 1;\n}\nmenu, ol, ul {\n\tlist-style: none;\n}\nblockquote, q {\n\tquotes: none;\n}\nblockquote:before, blockquote:after,\nq:before, q:after {\n\tcontent: '';\n\tcontent: none;\n}\ntable {\n\tborder-collapse: collapse;\n\tborder-spacing: 0;\n}\n", "@import url(../../node_modules/purecss/build/pure.css);@import url(../../node_modules/selectize/dist/css/selectize.css);@import url(../../node_modules/highlight.js/styles/zenburn.css);html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,menu,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,main,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section{display:block}*[hidden]{display:none}body{line-height:1}menu,ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}table{border-collapse:collapse;border-spacing:0}/*!\n *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome\n *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)\n */@font-face{font-family:'FontAwesome';src:url(\"../fonts/fontawesome-webfont.eot?v=4.7.0\");src:url(\"../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0\") format(\"embedded-opentype\"),url(\"../fonts/fontawesome-webfont.woff2?v=4.7.0\") format(\"woff2\"),url(\"../fonts/fontawesome-webfont.woff?v=4.7.0\") format(\"woff\"),url(\"../fonts/fontawesome-webfont.ttf?v=4.7.0\") format(\"truetype\"),url(\"../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular\") format(\"svg\");font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14286em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14286em;width:2.14286em;top:.14286em;text-align:center}.fa-li.fa-lg{left:-1.85714em}.fa-border{padding:.2em .25em .15em;border:solid 0.08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{-ms-filter:\"progid:DXImageTransform.Microsoft.BasicImage(rotation=1)\";-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-ms-filter:\"progid:DXImageTransform.Microsoft.BasicImage(rotation=2)\";-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-ms-filter:\"progid:DXImageTransform.Microsoft.BasicImage(rotation=3)\";-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-ms-filter:\"progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)\";-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{-ms-filter:\"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)\";-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:\"\"}.fa-music:before{content:\"\"}.fa-search:before{content:\"\"}.fa-envelope-o:before{content:\"\"}.fa-heart:before{content:\"\"}.fa-star:before{content:\"\"}.fa-star-o:before{content:\"\"}.fa-user:before{content:\"\"}.fa-film:before{content:\"\"}.fa-th-large:before{content:\"\"}.fa-th:before{content:\"\"}.fa-th-list:before{content:\"\"}.fa-check:before{content:\"\"}.fa-remove:before,.fa-close:before,.fa-times:before{content:\"\"}.fa-search-plus:before{content:\"\"}.fa-search-minus:before{content:\"\"}.fa-power-off:before{content:\"\"}.fa-signal:before{content:\"\"}.fa-gear:before,.fa-cog:before{content:\"\"}.fa-trash-o:before{content:\"\"}.fa-home:before{content:\"\"}.fa-file-o:before{content:\"\"}.fa-clock-o:before{content:\"\"}.fa-road:before{content:\"\"}.fa-download:before{content:\"\"}.fa-arrow-circle-o-down:before{content:\"\"}.fa-arrow-circle-o-up:before{content:\"\"}.fa-inbox:before{content:\"\"}.fa-play-circle-o:before{content:\"\"}.fa-rotate-right:before,.fa-repeat:before{content:\"\"}.fa-refresh:before{content:\"\"}.fa-list-alt:before{content:\"\"}.fa-lock:before{content:\"\"}.fa-flag:before{content:\"\"}.fa-headphones:before{content:\"\"}.fa-volume-off:before{content:\"\"}.fa-volume-down:before{content:\"\"}.fa-volume-up:before{content:\"\"}.fa-qrcode:before{content:\"\"}.fa-barcode:before{content:\"\"}.fa-tag:before{content:\"\"}.fa-tags:before{content:\"\"}.fa-book:before{content:\"\"}.fa-bookmark:before{content:\"\"}.fa-print:before{content:\"\"}.fa-camera:before{content:\"\"}.fa-font:before{content:\"\"}.fa-bold:before{content:\"\"}.fa-italic:before{content:\"\"}.fa-text-height:before{content:\"\"}.fa-text-width:before{content:\"\"}.fa-align-left:before{content:\"\"}.fa-align-center:before{content:\"\"}.fa-align-right:before{content:\"\"}.fa-align-justify:before{content:\"\"}.fa-list:before{content:\"\"}.fa-dedent:before,.fa-outdent:before{content:\"\"}.fa-indent:before{content:\"\"}.fa-video-camera:before{content:\"\"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:\"\"}.fa-pencil:before{content:\"\"}.fa-map-marker:before{content:\"\"}.fa-adjust:before{content:\"\"}.fa-tint:before{content:\"\"}.fa-edit:before,.fa-pencil-square-o:before{content:\"\"}.fa-share-square-o:before{content:\"\"}.fa-check-square-o:before{content:\"\"}.fa-arrows:before{content:\"\"}.fa-step-backward:before{content:\"\"}.fa-fast-backward:before{content:\"\"}.fa-backward:before{content:\"\"}.fa-play:before{content:\"\"}.fa-pause:before{content:\"\"}.fa-stop:before{content:\"\"}.fa-forward:before{content:\"\"}.fa-fast-forward:before{content:\"\"}.fa-step-forward:before{content:\"\"}.fa-eject:before{content:\"\"}.fa-chevron-left:before{content:\"\"}.fa-chevron-right:before{content:\"\"}.fa-plus-circle:before{content:\"\"}.fa-minus-circle:before{content:\"\"}.fa-times-circle:before{content:\"\"}.fa-check-circle:before{content:\"\"}.fa-question-circle:before{content:\"\"}.fa-info-circle:before{content:\"\"}.fa-crosshairs:before{content:\"\"}.fa-times-circle-o:before{content:\"\"}.fa-check-circle-o:before{content:\"\"}.fa-ban:before{content:\"\"}.fa-arrow-left:before{content:\"\"}.fa-arrow-right:before{content:\"\"}.fa-arrow-up:before{content:\"\"}.fa-arrow-down:before{content:\"\"}.fa-mail-forward:before,.fa-share:before{content:\"\"}.fa-expand:before{content:\"\"}.fa-compress:before{content:\"\"}.fa-plus:before{content:\"\"}.fa-minus:before{content:\"\"}.fa-asterisk:before{content:\"\"}.fa-exclamation-circle:before{content:\"\"}.fa-gift:before{content:\"\"}.fa-leaf:before{content:\"\"}.fa-fire:before{content:\"\"}.fa-eye:before{content:\"\"}.fa-eye-slash:before{content:\"\"}.fa-warning:before,.fa-exclamation-triangle:before{content:\"\"}.fa-plane:before{content:\"\"}.fa-calendar:before{content:\"\"}.fa-random:before{content:\"\"}.fa-comment:before{content:\"\"}.fa-magnet:before{content:\"\"}.fa-chevron-up:before{content:\"\"}.fa-chevron-down:before{content:\"\"}.fa-retweet:before{content:\"\"}.fa-shopping-cart:before{content:\"\"}.fa-folder:before{content:\"\"}.fa-folder-open:before{content:\"\"}.fa-arrows-v:before{content:\"\"}.fa-arrows-h:before{content:\"\"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:\"\"}.fa-twitter-square:before{content:\"\"}.fa-facebook-square:before{content:\"\"}.fa-camera-retro:before{content:\"\"}.fa-key:before{content:\"\"}.fa-gears:before,.fa-cogs:before{content:\"\"}.fa-comments:before{content:\"\"}.fa-thumbs-o-up:before{content:\"\"}.fa-thumbs-o-down:before{content:\"\"}.fa-star-half:before{content:\"\"}.fa-heart-o:before{content:\"\"}.fa-sign-out:before{content:\"\"}.fa-linkedin-square:before{content:\"\"}.fa-thumb-tack:before{content:\"\"}.fa-external-link:before{content:\"\"}.fa-sign-in:before{content:\"\"}.fa-trophy:before{content:\"\"}.fa-github-square:before{content:\"\"}.fa-upload:before{content:\"\"}.fa-lemon-o:before{content:\"\"}.fa-phone:before{content:\"\"}.fa-square-o:before{content:\"\"}.fa-bookmark-o:before{content:\"\"}.fa-phone-square:before{content:\"\"}.fa-twitter:before{content:\"\"}.fa-facebook-f:before,.fa-facebook:before{content:\"\"}.fa-github:before{content:\"\"}.fa-unlock:before{content:\"\"}.fa-credit-card:before{content:\"\"}.fa-feed:before,.fa-rss:before{content:\"\"}.fa-hdd-o:before{content:\"\"}.fa-bullhorn:before{content:\"\"}.fa-bell:before{content:\"\"}.fa-certificate:before{content:\"\"}.fa-hand-o-right:before{content:\"\"}.fa-hand-o-left:before{content:\"\"}.fa-hand-o-up:before{content:\"\"}.fa-hand-o-down:before{content:\"\"}.fa-arrow-circle-left:before{content:\"\"}.fa-arrow-circle-right:before{content:\"\"}.fa-arrow-circle-up:before{content:\"\"}.fa-arrow-circle-down:before{content:\"\"}.fa-globe:before{content:\"\"}.fa-wrench:before{content:\"\"}.fa-tasks:before{content:\"\"}.fa-filter:before{content:\"\"}.fa-briefcase:before{content:\"\"}.fa-arrows-alt:before{content:\"\"}.fa-group:before,.fa-users:before{content:\"\"}.fa-chain:before,.fa-link:before{content:\"\"}.fa-cloud:before{content:\"\"}.fa-flask:before{content:\"\"}.fa-cut:before,.fa-scissors:before{content:\"\"}.fa-copy:before,.fa-files-o:before{content:\"\"}.fa-paperclip:before{content:\"\"}.fa-save:before,.fa-floppy-o:before{content:\"\"}.fa-square:before{content:\"\"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:\"\"}.fa-list-ul:before{content:\"\"}.fa-list-ol:before{content:\"\"}.fa-strikethrough:before{content:\"\"}.fa-underline:before{content:\"\"}.fa-table:before{content:\"\"}.fa-magic:before{content:\"\"}.fa-truck:before{content:\"\"}.fa-pinterest:before{content:\"\"}.fa-pinterest-square:before{content:\"\"}.fa-google-plus-square:before{content:\"\"}.fa-google-plus:before{content:\"\"}.fa-money:before{content:\"\"}.fa-caret-down:before{content:\"\"}.fa-caret-up:before{content:\"\"}.fa-caret-left:before{content:\"\"}.fa-caret-right:before{content:\"\"}.fa-columns:before{content:\"\"}.fa-unsorted:before,.fa-sort:before{content:\"\"}.fa-sort-down:before,.fa-sort-desc:before{content:\"\"}.fa-sort-up:before,.fa-sort-asc:before{content:\"\"}.fa-envelope:before{content:\"\"}.fa-linkedin:before{content:\"\"}.fa-rotate-left:before,.fa-undo:before{content:\"\"}.fa-legal:before,.fa-gavel:before{content:\"\"}.fa-dashboard:before,.fa-tachometer:before{content:\"\"}.fa-comment-o:before{content:\"\"}.fa-comments-o:before{content:\"\"}.fa-flash:before,.fa-bolt:before{content:\"\"}.fa-sitemap:before{content:\"\"}.fa-umbrella:before{content:\"\"}.fa-paste:before,.fa-clipboard:before{content:\"\"}.fa-lightbulb-o:before{content:\"\"}.fa-exchange:before{content:\"\"}.fa-cloud-download:before{content:\"\"}.fa-cloud-upload:before{content:\"\"}.fa-user-md:before{content:\"\"}.fa-stethoscope:before{content:\"\"}.fa-suitcase:before{content:\"\"}.fa-bell-o:before{content:\"\"}.fa-coffee:before{content:\"\"}.fa-cutlery:before{content:\"\"}.fa-file-text-o:before{content:\"\"}.fa-building-o:before{content:\"\"}.fa-hospital-o:before{content:\"\"}.fa-ambulance:before{content:\"\"}.fa-medkit:before{content:\"\"}.fa-fighter-jet:before{content:\"\"}.fa-beer:before{content:\"\"}.fa-h-square:before{content:\"\"}.fa-plus-square:before{content:\"\"}.fa-angle-double-left:before{content:\"\"}.fa-angle-double-right:before{content:\"\"}.fa-angle-double-up:before{content:\"\"}.fa-angle-double-down:before{content:\"\"}.fa-angle-left:before{content:\"\"}.fa-angle-right:before{content:\"\"}.fa-angle-up:before{content:\"\"}.fa-angle-down:before{content:\"\"}.fa-desktop:before{content:\"\"}.fa-laptop:before{content:\"\"}.fa-tablet:before{content:\"\"}.fa-mobile-phone:before,.fa-mobile:before{content:\"\"}.fa-circle-o:before{content:\"\"}.fa-quote-left:before{content:\"\"}.fa-quote-right:before{content:\"\"}.fa-spinner:before{content:\"\"}.fa-circle:before{content:\"\"}.fa-mail-reply:before,.fa-reply:before{content:\"\"}.fa-github-alt:before{content:\"\"}.fa-folder-o:before{content:\"\"}.fa-folder-open-o:before{content:\"\"}.fa-smile-o:before{content:\"\"}.fa-frown-o:before{content:\"\"}.fa-meh-o:before{content:\"\"}.fa-gamepad:before{content:\"\"}.fa-keyboard-o:before{content:\"\"}.fa-flag-o:before{content:\"\"}.fa-flag-checkered:before{content:\"\"}.fa-terminal:before{content:\"\"}.fa-code:before{content:\"\"}.fa-mail-reply-all:before,.fa-reply-all:before{content:\"\"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:\"\"}.fa-location-arrow:before{content:\"\"}.fa-crop:before{content:\"\"}.fa-code-fork:before{content:\"\"}.fa-unlink:before,.fa-chain-broken:before{content:\"\"}.fa-question:before{content:\"\"}.fa-info:before{content:\"\"}.fa-exclamation:before{content:\"\"}.fa-superscript:before{content:\"\"}.fa-subscript:before{content:\"\"}.fa-eraser:before{content:\"\"}.fa-puzzle-piece:before{content:\"\"}.fa-microphone:before{content:\"\"}.fa-microphone-slash:before{content:\"\"}.fa-shield:before{content:\"\"}.fa-calendar-o:before{content:\"\"}.fa-fire-extinguisher:before{content:\"\"}.fa-rocket:before{content:\"\"}.fa-maxcdn:before{content:\"\"}.fa-chevron-circle-left:before{content:\"\"}.fa-chevron-circle-right:before{content:\"\"}.fa-chevron-circle-up:before{content:\"\"}.fa-chevron-circle-down:before{content:\"\"}.fa-html5:before{content:\"\"}.fa-css3:before{content:\"\"}.fa-anchor:before{content:\"\"}.fa-unlock-alt:before{content:\"\"}.fa-bullseye:before{content:\"\"}.fa-ellipsis-h:before{content:\"\"}.fa-ellipsis-v:before{content:\"\"}.fa-rss-square:before{content:\"\"}.fa-play-circle:before{content:\"\"}.fa-ticket:before{content:\"\"}.fa-minus-square:before{content:\"\"}.fa-minus-square-o:before{content:\"\"}.fa-level-up:before{content:\"\"}.fa-level-down:before{content:\"\"}.fa-check-square:before{content:\"\"}.fa-pencil-square:before{content:\"\"}.fa-external-link-square:before{content:\"\"}.fa-share-square:before{content:\"\"}.fa-compass:before{content:\"\"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:\"\"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:\"\"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:\"\"}.fa-euro:before,.fa-eur:before{content:\"\"}.fa-gbp:before{content:\"\"}.fa-dollar:before,.fa-usd:before{content:\"\"}.fa-rupee:before,.fa-inr:before{content:\"\"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:\"\"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:\"\"}.fa-won:before,.fa-krw:before{content:\"\"}.fa-bitcoin:before,.fa-btc:before{content:\"\"}.fa-file:before{content:\"\"}.fa-file-text:before{content:\"\"}.fa-sort-alpha-asc:before{content:\"\"}.fa-sort-alpha-desc:before{content:\"\"}.fa-sort-amount-asc:before{content:\"\"}.fa-sort-amount-desc:before{content:\"\"}.fa-sort-numeric-asc:before{content:\"\"}.fa-sort-numeric-desc:before{content:\"\"}.fa-thumbs-up:before{content:\"\"}.fa-thumbs-down:before{content:\"\"}.fa-youtube-square:before{content:\"\"}.fa-youtube:before{content:\"\"}.fa-xing:before{content:\"\"}.fa-xing-square:before{content:\"\"}.fa-youtube-play:before{content:\"\"}.fa-dropbox:before{content:\"\"}.fa-stack-overflow:before{content:\"\"}.fa-instagram:before{content:\"\"}.fa-flickr:before{content:\"\"}.fa-adn:before{content:\"\"}.fa-bitbucket:before{content:\"\"}.fa-bitbucket-square:before{content:\"\"}.fa-tumblr:before{content:\"\"}.fa-tumblr-square:before{content:\"\"}.fa-long-arrow-down:before{content:\"\"}.fa-long-arrow-up:before{content:\"\"}.fa-long-arrow-left:before{content:\"\"}.fa-long-arrow-right:before{content:\"\"}.fa-apple:before{content:\"\"}.fa-windows:before{content:\"\"}.fa-android:before{content:\"\"}.fa-linux:before{content:\"\"}.fa-dribbble:before{content:\"\"}.fa-skype:before{content:\"\"}.fa-foursquare:before{content:\"\"}.fa-trello:before{content:\"\"}.fa-female:before{content:\"\"}.fa-male:before{content:\"\"}.fa-gittip:before,.fa-gratipay:before{content:\"\"}.fa-sun-o:before{content:\"\"}.fa-moon-o:before{content:\"\"}.fa-archive:before{content:\"\"}.fa-bug:before{content:\"\"}.fa-vk:before{content:\"\"}.fa-weibo:before{content:\"\"}.fa-renren:before{content:\"\"}.fa-pagelines:before{content:\"\"}.fa-stack-exchange:before{content:\"\"}.fa-arrow-circle-o-right:before{content:\"\"}.fa-arrow-circle-o-left:before{content:\"\"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:\"\"}.fa-dot-circle-o:before{content:\"\"}.fa-wheelchair:before{content:\"\"}.fa-vimeo-square:before{content:\"\"}.fa-turkish-lira:before,.fa-try:before{content:\"\"}.fa-plus-square-o:before{content:\"\"}.fa-space-shuttle:before{content:\"\"}.fa-slack:before{content:\"\"}.fa-envelope-square:before{content:\"\"}.fa-wordpress:before{content:\"\"}.fa-openid:before{content:\"\"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:\"\"}.fa-mortar-board:before,.fa-graduation-cap:before{content:\"\"}.fa-yahoo:before{content:\"\"}.fa-google:before{content:\"\"}.fa-reddit:before{content:\"\"}.fa-reddit-square:before{content:\"\"}.fa-stumbleupon-circle:before{content:\"\"}.fa-stumbleupon:before{content:\"\"}.fa-delicious:before{content:\"\"}.fa-digg:before{content:\"\"}.fa-pied-piper-pp:before{content:\"\"}.fa-pied-piper-alt:before{content:\"\"}.fa-drupal:before{content:\"\"}.fa-joomla:before{content:\"\"}.fa-language:before{content:\"\"}.fa-fax:before{content:\"\"}.fa-building:before{content:\"\"}.fa-child:before{content:\"\"}.fa-paw:before{content:\"\"}.fa-spoon:before{content:\"\"}.fa-cube:before{content:\"\"}.fa-cubes:before{content:\"\"}.fa-behance:before{content:\"\"}.fa-behance-square:before{content:\"\"}.fa-steam:before{content:\"\"}.fa-steam-square:before{content:\"\"}.fa-recycle:before{content:\"\"}.fa-automobile:before,.fa-car:before{content:\"\"}.fa-cab:before,.fa-taxi:before{content:\"\"}.fa-tree:before{content:\"\"}.fa-spotify:before{content:\"\"}.fa-deviantart:before{content:\"\"}.fa-soundcloud:before{content:\"\"}.fa-database:before{content:\"\"}.fa-file-pdf-o:before{content:\"\"}.fa-file-word-o:before{content:\"\"}.fa-file-excel-o:before{content:\"\"}.fa-file-powerpoint-o:before{content:\"\"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:\"\"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:\"\"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:\"\"}.fa-file-movie-o:before,.fa-file-video-o:before{content:\"\"}.fa-file-code-o:before{content:\"\"}.fa-vine:before{content:\"\"}.fa-codepen:before{content:\"\"}.fa-jsfiddle:before{content:\"\"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:\"\"}.fa-circle-o-notch:before{content:\"\"}.fa-ra:before,.fa-resistance:before,.fa-rebel:before{content:\"\"}.fa-ge:before,.fa-empire:before{content:\"\"}.fa-git-square:before{content:\"\"}.fa-git:before{content:\"\"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:\"\"}.fa-tencent-weibo:before{content:\"\"}.fa-qq:before{content:\"\"}.fa-wechat:before,.fa-weixin:before{content:\"\"}.fa-send:before,.fa-paper-plane:before{content:\"\"}.fa-send-o:before,.fa-paper-plane-o:before{content:\"\"}.fa-history:before{content:\"\"}.fa-circle-thin:before{content:\"\"}.fa-header:before{content:\"\"}.fa-paragraph:before{content:\"\"}.fa-sliders:before{content:\"\"}.fa-share-alt:before{content:\"\"}.fa-share-alt-square:before{content:\"\"}.fa-bomb:before{content:\"\"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:\"\"}.fa-tty:before{content:\"\"}.fa-binoculars:before{content:\"\"}.fa-plug:before{content:\"\"}.fa-slideshare:before{content:\"\"}.fa-twitch:before{content:\"\"}.fa-yelp:before{content:\"\"}.fa-newspaper-o:before{content:\"\"}.fa-wifi:before{content:\"\"}.fa-calculator:before{content:\"\"}.fa-paypal:before{content:\"\"}.fa-google-wallet:before{content:\"\"}.fa-cc-visa:before{content:\"\"}.fa-cc-mastercard:before{content:\"\"}.fa-cc-discover:before{content:\"\"}.fa-cc-amex:before{content:\"\"}.fa-cc-paypal:before{content:\"\"}.fa-cc-stripe:before{content:\"\"}.fa-bell-slash:before{content:\"\"}.fa-bell-slash-o:before{content:\"\"}.fa-trash:before{content:\"\"}.fa-copyright:before{content:\"\"}.fa-at:before{content:\"\"}.fa-eyedropper:before{content:\"\"}.fa-paint-brush:before{content:\"\"}.fa-birthday-cake:before{content:\"\"}.fa-area-chart:before{content:\"\"}.fa-pie-chart:before{content:\"\"}.fa-line-chart:before{content:\"\"}.fa-lastfm:before{content:\"\"}.fa-lastfm-square:before{content:\"\"}.fa-toggle-off:before{content:\"\"}.fa-toggle-on:before{content:\"\"}.fa-bicycle:before{content:\"\"}.fa-bus:before{content:\"\"}.fa-ioxhost:before{content:\"\"}.fa-angellist:before{content:\"\"}.fa-cc:before{content:\"\"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:\"\"}.fa-meanpath:before{content:\"\"}.fa-buysellads:before{content:\"\"}.fa-connectdevelop:before{content:\"\"}.fa-dashcube:before{content:\"\"}.fa-forumbee:before{content:\"\"}.fa-leanpub:before{content:\"\"}.fa-sellsy:before{content:\"\"}.fa-shirtsinbulk:before{content:\"\"}.fa-simplybuilt:before{content:\"\"}.fa-skyatlas:before{content:\"\"}.fa-cart-plus:before{content:\"\"}.fa-cart-arrow-down:before{content:\"\"}.fa-diamond:before{content:\"\"}.fa-ship:before{content:\"\"}.fa-user-secret:before{content:\"\"}.fa-motorcycle:before{content:\"\"}.fa-street-view:before{content:\"\"}.fa-heartbeat:before{content:\"\"}.fa-venus:before{content:\"\"}.fa-mars:before{content:\"\"}.fa-mercury:before{content:\"\"}.fa-intersex:before,.fa-transgender:before{content:\"\"}.fa-transgender-alt:before{content:\"\"}.fa-venus-double:before{content:\"\"}.fa-mars-double:before{content:\"\"}.fa-venus-mars:before{content:\"\"}.fa-mars-stroke:before{content:\"\"}.fa-mars-stroke-v:before{content:\"\"}.fa-mars-stroke-h:before{content:\"\"}.fa-neuter:before{content:\"\"}.fa-genderless:before{content:\"\"}.fa-facebook-official:before{content:\"\"}.fa-pinterest-p:before{content:\"\"}.fa-whatsapp:before{content:\"\"}.fa-server:before{content:\"\"}.fa-user-plus:before{content:\"\"}.fa-user-times:before{content:\"\"}.fa-hotel:before,.fa-bed:before{content:\"\"}.fa-viacoin:before{content:\"\"}.fa-train:before{content:\"\"}.fa-subway:before{content:\"\"}.fa-medium:before{content:\"\"}.fa-yc:before,.fa-y-combinator:before{content:\"\"}.fa-optin-monster:before{content:\"\"}.fa-opencart:before{content:\"\"}.fa-expeditedssl:before{content:\"\"}.fa-battery-4:before,.fa-battery:before,.fa-battery-full:before{content:\"\"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:\"\"}.fa-battery-2:before,.fa-battery-half:before{content:\"\"}.fa-battery-1:before,.fa-battery-quarter:before{content:\"\"}.fa-battery-0:before,.fa-battery-empty:before{content:\"\"}.fa-mouse-pointer:before{content:\"\"}.fa-i-cursor:before{content:\"\"}.fa-object-group:before{content:\"\"}.fa-object-ungroup:before{content:\"\"}.fa-sticky-note:before{content:\"\"}.fa-sticky-note-o:before{content:\"\"}.fa-cc-jcb:before{content:\"\"}.fa-cc-diners-club:before{content:\"\"}.fa-clone:before{content:\"\"}.fa-balance-scale:before{content:\"\"}.fa-hourglass-o:before{content:\"\"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:\"\"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:\"\"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:\"\"}.fa-hourglass:before{content:\"\"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:\"\"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:\"\"}.fa-hand-scissors-o:before{content:\"\"}.fa-hand-lizard-o:before{content:\"\"}.fa-hand-spock-o:before{content:\"\"}.fa-hand-pointer-o:before{content:\"\"}.fa-hand-peace-o:before{content:\"\"}.fa-trademark:before{content:\"\"}.fa-registered:before{content:\"\"}.fa-creative-commons:before{content:\"\"}.fa-gg:before{content:\"\"}.fa-gg-circle:before{content:\"\"}.fa-tripadvisor:before{content:\"\"}.fa-odnoklassniki:before{content:\"\"}.fa-odnoklassniki-square:before{content:\"\"}.fa-get-pocket:before{content:\"\"}.fa-wikipedia-w:before{content:\"\"}.fa-safari:before{content:\"\"}.fa-chrome:before{content:\"\"}.fa-firefox:before{content:\"\"}.fa-opera:before{content:\"\"}.fa-internet-explorer:before{content:\"\"}.fa-tv:before,.fa-television:before{content:\"\"}.fa-contao:before{content:\"\"}.fa-500px:before{content:\"\"}.fa-amazon:before{content:\"\"}.fa-calendar-plus-o:before{content:\"\"}.fa-calendar-minus-o:before{content:\"\"}.fa-calendar-times-o:before{content:\"\"}.fa-calendar-check-o:before{content:\"\"}.fa-industry:before{content:\"\"}.fa-map-pin:before{content:\"\"}.fa-map-signs:before{content:\"\"}.fa-map-o:before{content:\"\"}.fa-map:before{content:\"\"}.fa-commenting:before{content:\"\"}.fa-commenting-o:before{content:\"\"}.fa-houzz:before{content:\"\"}.fa-vimeo:before{content:\"\"}.fa-black-tie:before{content:\"\"}.fa-fonticons:before{content:\"\"}.fa-reddit-alien:before{content:\"\"}.fa-edge:before{content:\"\"}.fa-credit-card-alt:before{content:\"\"}.fa-codiepie:before{content:\"\"}.fa-modx:before{content:\"\"}.fa-fort-awesome:before{content:\"\"}.fa-usb:before{content:\"\"}.fa-product-hunt:before{content:\"\"}.fa-mixcloud:before{content:\"\"}.fa-scribd:before{content:\"\"}.fa-pause-circle:before{content:\"\"}.fa-pause-circle-o:before{content:\"\"}.fa-stop-circle:before{content:\"\"}.fa-stop-circle-o:before{content:\"\"}.fa-shopping-bag:before{content:\"\"}.fa-shopping-basket:before{content:\"\"}.fa-hashtag:before{content:\"\"}.fa-bluetooth:before{content:\"\"}.fa-bluetooth-b:before{content:\"\"}.fa-percent:before{content:\"\"}.fa-gitlab:before{content:\"\"}.fa-wpbeginner:before{content:\"\"}.fa-wpforms:before{content:\"\"}.fa-envira:before{content:\"\"}.fa-universal-access:before{content:\"\"}.fa-wheelchair-alt:before{content:\"\"}.fa-question-circle-o:before{content:\"\"}.fa-blind:before{content:\"\"}.fa-audio-description:before{content:\"\"}.fa-volume-control-phone:before{content:\"\"}.fa-braille:before{content:\"\"}.fa-assistive-listening-systems:before{content:\"\"}.fa-asl-interpreting:before,.fa-american-sign-language-interpreting:before{content:\"\"}.fa-deafness:before,.fa-hard-of-hearing:before,.fa-deaf:before{content:\"\"}.fa-glide:before{content:\"\"}.fa-glide-g:before{content:\"\"}.fa-signing:before,.fa-sign-language:before{content:\"\"}.fa-low-vision:before{content:\"\"}.fa-viadeo:before{content:\"\"}.fa-viadeo-square:before{content:\"\"}.fa-snapchat:before{content:\"\"}.fa-snapchat-ghost:before{content:\"\"}.fa-snapchat-square:before{content:\"\"}.fa-pied-piper:before{content:\"\"}.fa-first-order:before{content:\"\"}.fa-yoast:before{content:\"\"}.fa-themeisle:before{content:\"\"}.fa-google-plus-circle:before,.fa-google-plus-official:before{content:\"\"}.fa-fa:before,.fa-font-awesome:before{content:\"\"}.fa-handshake-o:before{content:\"\"}.fa-envelope-open:before{content:\"\"}.fa-envelope-open-o:before{content:\"\"}.fa-linode:before{content:\"\"}.fa-address-book:before{content:\"\"}.fa-address-book-o:before{content:\"\"}.fa-vcard:before,.fa-address-card:before{content:\"\"}.fa-vcard-o:before,.fa-address-card-o:before{content:\"\"}.fa-user-circle:before{content:\"\"}.fa-user-circle-o:before{content:\"\"}.fa-user-o:before{content:\"\"}.fa-id-badge:before{content:\"\"}.fa-drivers-license:before,.fa-id-card:before{content:\"\"}.fa-drivers-license-o:before,.fa-id-card-o:before{content:\"\"}.fa-quora:before{content:\"\"}.fa-free-code-camp:before{content:\"\"}.fa-telegram:before{content:\"\"}.fa-thermometer-4:before,.fa-thermometer:before,.fa-thermometer-full:before{content:\"\"}.fa-thermometer-3:before,.fa-thermometer-three-quarters:before{content:\"\"}.fa-thermometer-2:before,.fa-thermometer-half:before{content:\"\"}.fa-thermometer-1:before,.fa-thermometer-quarter:before{content:\"\"}.fa-thermometer-0:before,.fa-thermometer-empty:before{content:\"\"}.fa-shower:before{content:\"\"}.fa-bathtub:before,.fa-s15:before,.fa-bath:before{content:\"\"}.fa-podcast:before{content:\"\"}.fa-window-maximize:before{content:\"\"}.fa-window-minimize:before{content:\"\"}.fa-window-restore:before{content:\"\"}.fa-times-rectangle:before,.fa-window-close:before{content:\"\"}.fa-times-rectangle-o:before,.fa-window-close-o:before{content:\"\"}.fa-bandcamp:before{content:\"\"}.fa-grav:before{content:\"\"}.fa-etsy:before{content:\"\"}.fa-imdb:before{content:\"\"}.fa-ravelry:before{content:\"\"}.fa-eercast:before{content:\"\"}.fa-microchip:before{content:\"\"}.fa-snowflake-o:before{content:\"\"}.fa-superpowers:before{content:\"\"}.fa-wpexplorer:before{content:\"\"}.fa-meetup:before{content:\"\"}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}*{margin:0;padding:0}html,body{height:100%}body{font-family:sans-serif;line-height:1.5;min-height:100%}.wrap{width:80%;max-width:1100px;margin:auto;position:relative}.left{float:left}.right{float:right}.center{padding:auto;text-align:center}.v-center{display:table-cell;vertical-align:middle}i{font-style:italic}h1{margin:1em 0;font-size:2em;font-weight:900}h2{margin:1em 0;font-size:1.5em;font-weight:700;color:#1c1c1c;border-bottom:solid 1px #bbb}h3{font-weight:500;color:#2e3436;padding:0;margin-top:0}p{padding:0.5em 0;margin-bottom:1em}a{color:midnightblue}a:hover,a:focus,a.pure-menu-link:hover,a.pure-menu-link:focus,.pure-menu-selected a.pure-menu-link:hover,.pure-menu-selected a.pure-menu-link:focus{color:white;background-color:#444444;padding:.5em 1em}a:hover,a:focus{padding:.15rem}.overflow{overflow:hidden}ul{padding-left:1.5em}strong,b{font-weight:bold}pre,code,kbd,samp,tt{font-family:monospace, monospace;font-size:.9rem;padding:0 .2rem}.xmldata{font-family:monospace}.code-box-title .clipboard-btn{background-color:#f0f0f0;border:1px solid #ccc;position:absolute;right:0;height:inherit;margin-top:-2em;margin-right:4px}.pure-button:hover,.pure-button:focus{background-color:#555;padding:.5em 1em;color:#fff}.pure-button-red{background-color:#db0100;color:#fff}.pure-button-red:hover,.pure-button-red:focus{background-color:#555;padding:.5em 1em}.pure-button.hollow{background-color:#FFFFFF;color:#6f6f6f;border:solid 1px #E6E6E6}.pure-button.hollow:hover{background-image:none}.pure-button.hollow[disabled]{cursor:auto;opacity:initial;pointer-events:auto;-webkit-user-drag:auto;-webkit-user-select:auto;-moz-user-select:text;-ms-user-select:text;user-select:text}.pure-button.hljs{display:inline-block;border:0;background-color:transparent}.pure-button.hljs:hover,.pure-button.hljs:focus{background-color:#f0f0f0;color:black}.pure-button-group .pure-button:first-child,.pure-button-group .pure-button:last-child{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.pure-button-group.two-elements .pure-button{margin:0;line-height:unset;border:1px solid #E6E6E6}.pure-button-group.two-elements form{display:inline}.pure-button-group.two-elements .pure-button:first-child{border-right:none}.pure-button-group.two-elements .pure-button:last-child{border-right:1px solid #E6E6E6}.pure-button-group .pure-button.show-files{max-width:450px;overflow:hidden}.top-right-corner{position:absolute;right:1.75em}#layout,#menu,#foot,.menu-link{-webkit-transition:all 0.2s ease-out;-moz-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;transition:all 0.2s ease-out}#layout.active #menu{right:11em;width:11em}#menu{margin-right:-11em;width:11em;position:fixed;top:0;right:0;bottom:0;z-index:1000;background:#e8410c;overflow-y:auto;-webkit-overflow-scrolling:touch}#menu a{color:#ffffff;border:none;padding:0.6em 0 0.6em 0.6em}#menu .pure-menu,#menu .pure-menu ul{border:none;background:transparent}#menu .pure-menu ul,#menu .pure-menu .menu-item-divided{border-top:1px solid #db0100}#menu .pure-menu-selected,#menu .pure-menu-heading{background:#b8002c;color:black}#menu .pure-menu-selected a{color:#fff}#menu .pure-menu-heading{font-size:110%;color:#fff;margin:0;text-transform:none}.frontpage-menu .pure-menu-item{border-bottom:1px solid lightgrey}.frontpage-menu .pure-menu-selected{border-bottom:1px solid black;font-weight:bold}.menu-link{position:relative;display:block;top:0;right:0;background:transparent;z-index:10;height:2rem;padding:2rem 0;text-decoration:none}.menu-link:hover,.menu-link:focus{padding:2rem 0;background:none !important}.menu-link span:hover,.menu-link span:focus{color:rgba(1,1,1,0.8)}.menu-link span{position:relative;display:block;color:#FFFFFF}.menu-link span,.menu-link span:before,.menu-link span:after{background-color:transparent}@media screen and (max-width: 40em){#layout.active{position:relative;right:11em}#menuLink.menu-link.active{position:fixed;right:13em}#foot.active{margin-right:11em}}#header{height:6rem;background:linear-gradient(141deg, #b8002c 0%, #db0100 51%, #e8410c 75%)}.logo-header{min-width:8em;height:6rem;max-width:70%}#logo{font-family:sans-serif;font-size:2.5em;color:white;text-shadow:0 3px 2px #532900}.simple{font-weight:300}.saml{font-family:Verdana;letter-spacing:-0.12em;font-weight:600;margin-left:-0.05em}.language-menu{font-family:FontAwesome, sans-serif;min-width:10rem}.language-bar{height:6rem}#layout{right:0;padding-right:0}#content{padding-top:2em;padding-bottom:2rem}.message-box{background-color:#f4f4f4;border-left-color:#444444;border-left-style:solid;border-left-width:0.3125rem;box-shadow:0 5px 8px -6px rgba(0,0,0,0.2);margin:1rem 0;padding:1.3rem;position:relative}.message-box.error{background-color:#f7e4e1;border-left-color:#cc4b37}.message-box.warning{background-color:\"#fffdbf\";border-left-color:#f9f56b}.message-box.success{background-color:#daf7e6;border-left-color:#46cc48}.auth_methods{margin-top:2em;width:35%}.code-box{margin-bottom:1em;border:1px solid #ccc}.code-box a{padding:.5em}.code-box-content{font-size:1em;line-height:1.15;padding:0.5em 1em;display:inline-block;min-height:1em;width:100%;height:100%;white-space:pre-wrap;font-family:monospace}.code-box-content::selection{color:black;background:\"#fffdbf\"}.code-box-title{border-bottom:1px solid #ccc;background-color:#e0e0e0;padding:0.5em 0 0.5em 0.5em}pre#xmlmetadata{width:98%}#layout{min-height:100%;margin:0 auto -6rem auto}#bottom,#push{height:6rem}#footer{width:100%;background:linear-gradient(141deg, #b8002c 0%, #db0100 51%, #e8410c 75%);height:4rem;padding:2rem 0 0;text-align:center;color:white}#footer a,#footer a:visited{color:white}#footer a:hover,#footer a:focus,#footer a:visited:hover,#footer a:visited:focus{background-color:white;color:black;padding:.15rem;margin-left:-.15rem;margin-top:-.15rem;text-decoration:none}.copyrights{padding-top:.5rem;height:3.5rem;font-size:.8rem}.logo-footer-right{position:absolute;right:0;top:50%;transform:translate(0, -50%)}.logo-footer{height:4rem}.text-area{margin-top:.5em;width:100%;font-size:0.9em;line-height:1.15}.file-upload input[type=\"url\"][disabled]{cursor:pointer;color:inherit}input[type=\"file\"]{color:black}.form-align{position:relative}.center-form{display:inline-block;margin-right:auto;margin-left:auto}.v-center-right{position:absolute;left:0;top:50%;transform:translate(0, -50%)}.pure-button,.pure-form input.edge,.pure-form textarea.edge{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.pure-form-aligned .pure-controls{margin:0 0 0 11em}.pure-select{float:right}.selectize-input,.selectize-dropdown,.selectize-input.dropdown-active{-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}.selectize-input:after{transform:translate(8px, 0)}div .item{float:left}.selectize-dropdown{text-align:left}.selectize-control.single .selectize-input,.selectize-dropdown.single{background-color:white;background-image:none;border:1px solid #ccc;box-shadow:inset 0 1px 3px #ddd;box-sizing:border-box;font-size:inherit;padding:0.5em 0.6em;display:inline-block;vertical-align:middle}.selectize-dropdown-content{cursor:pointer}.selectize-dropdown-content .option:hover,.selectize-dropdown-content .option:focus,.selectize-dropdown-content .option.active{color:white;background-color:#444444}.selectize-dropdown-content .option span.highlight{color:black;background-color:#fffbd5}.fa{font-family:FontAwesome !important}span.fa,i.fa{padding:0 0.5em}.message-box span.fa,.message-box i.fa{padding:0}.pure-table-attributes{table-layout:fixed;width:100%}.attrname{text-align:right}.attrvalue{overflow-wrap:break-word}table.attributes ul{padding:inherit}@media screen and (max-width: 40em){.pure-form .pure-input-sm-1-1{width:100%}}@media screen and (max-width: 40em){.wrap{width:90%}.pure-form-aligned .pure-controls{margin:0.5em 0 0;float:left}#layout{padding-top:0;margin-bottom:-4rem}.auth_methods{width:60%}#logo{font-size:1.8em}#footer{height:2.5rem;padding-top:1.5rem}#footer .copyrights{height:1rem;padding-top:0}}@media screen and (max-width: 0em), screen and (min-width: 40em){.show-for-small-only{display:none !important}.input-sm-placeholder{display:inline-block}}@media screen and (min-width: 40em){.hide-for-large{display:none !important}}@media screen and (max-width: 40em){.show-for-large{display:none !important}}.float-r{float:right}.enablebox table{border:1px solid #eee;margin-left:1em}.enablebox.mini table{float:right}.enablebox tr td{padding:.5px 1em 1px .5em;margin:0}.enablebox{font-size:85%}.enablebox tr.enabled td{background:#eee}.enablebox tr.disabled td{background:#ccc}fieldset.fancyfieldset{padding-left:1.5em}fieldset.fancyfieldset legend{padding:3px 2em 3px 0;width:100%}dt{font-weight:bold}.frontpage-menu{margin-bottom:1em}.entity-name,.entity-deprecated,.entity-expired{font-weight:bold}.entity-expired{color:#500}div.preferredidp{border:1px dashed #ccc;background:#eee;padding:2px 2em 2px 2em}.clear{clear:both}.breathe-top{margin-top:1em}.expandable{border:solid 1px #bbb;width:100%}.expandable .general{padding:1em}.expandable .content{display:none;padding:1em}.expandable .expander{cursor:pointer;text-align:center;padding:.25em;display:block;color:black;background-color:#f4f4f4;border-top:solid 1px #bbb}.expandable .expander:focus,.expandable .expander:hover{background-color:#555;color:white}.expandable .expander:after{content:\"\\f078\";font-family:FontAwesome}.expandable.expanded .content{display:block;border-left:solid .25em #555;border-right:solid .25em #555}.expandable.expanded .expander{border-bottom:none;border-top:solid 1px #bbb;border-left:solid .25em #555;border-right:solid .25em #555}.expandable.expanded .expander:after{content:\"\\f077\"}.hidden{display:none}\n", "/*!\n *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome\n *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)\n */\n\n@import \"variables\";\n@import \"mixins\";\n@import \"path\";\n@import \"core\";\n@import \"larger\";\n@import \"fixed-width\";\n@import \"list\";\n@import \"bordered-pulled\";\n@import \"animated\";\n@import \"rotated-flipped\";\n@import \"stacked\";\n@import \"icons\";\n@import \"screen-reader\";\n", "/* FONT PATH\n * -------------------------- */\n\n@font-face {\n  font-family: 'FontAwesome';\n  src: url('#{$fa-font-path}/fontawesome-webfont.eot?v=#{$fa-version}');\n  src: url('#{$fa-font-path}/fontawesome-webfont.eot?#iefix&v=#{$fa-version}') format('embedded-opentype'),\n    url('#{$fa-font-path}/fontawesome-webfont.woff2?v=#{$fa-version}') format('woff2'),\n    url('#{$fa-font-path}/fontawesome-webfont.woff?v=#{$fa-version}') format('woff'),\n    url('#{$fa-font-path}/fontawesome-webfont.ttf?v=#{$fa-version}') format('truetype'),\n    url('#{$fa-font-path}/fontawesome-webfont.svg?v=#{$fa-version}#fontawesomeregular') format('svg');\n//  src: url('#{$fa-font-path}/FontAwesome.otf') format('opentype'); // used when developing fonts\n  font-weight: normal;\n  font-style: normal;\n}\n", "// Base Class Definition\n// -------------------------\n\n.#{$fa-css-prefix} {\n  display: inline-block;\n  font: normal normal normal #{$fa-font-size-base}/#{$fa-line-height-base} FontAwesome; // shortening font declaration\n  font-size: inherit; // can't have font-size inherit on line above, so need to override\n  text-rendering: auto; // optimizelegibility throws things off #1094\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n}\n", "// Icon Sizes\n// -------------------------\n\n/* makes the font 33% larger relative to the icon container */\n.#{$fa-css-prefix}-lg {\n  font-size: (4em / 3);\n  line-height: (3em / 4);\n  vertical-align: -15%;\n}\n.#{$fa-css-prefix}-2x { font-size: 2em; }\n.#{$fa-css-prefix}-3x { font-size: 3em; }\n.#{$fa-css-prefix}-4x { font-size: 4em; }\n.#{$fa-css-prefix}-5x { font-size: 5em; }\n", "// Fixed Width Icons\n// -------------------------\n.#{$fa-css-prefix}-fw {\n  width: (18em / 14);\n  text-align: center;\n}\n", "// List Icons\n// -------------------------\n\n.#{$fa-css-prefix}-ul {\n  padding-left: 0;\n  margin-left: $fa-li-width;\n  list-style-type: none;\n  > li { position: relative; }\n}\n.#{$fa-css-prefix}-li {\n  position: absolute;\n  left: -$fa-li-width;\n  width: $fa-li-width;\n  top: (2em / 14);\n  text-align: center;\n  &.#{$fa-css-prefix}-lg {\n    left: -$fa-li-width + (4em / 14);\n  }\n}\n", "// Variables\n// --------------------------\n\n$fa-font-path:        \"../fonts\" !default;\n$fa-font-size-base:   14px !default;\n$fa-line-height-base: 1 !default;\n//$fa-font-path:        \"//netdna.bootstrapcdn.com/font-awesome/4.7.0/fonts\" !default; // for referencing Bootstrap CDN font files directly\n$fa-css-prefix:       fa !default;\n$fa-version:          \"4.7.0\" !default;\n$fa-border-color:     #eee !default;\n$fa-inverse:          #fff !default;\n$fa-li-width:         (30em / 14) !default;\n\n$fa-var-500px: \"\\f26e\";\n$fa-var-address-book: \"\\f2b9\";\n$fa-var-address-book-o: \"\\f2ba\";\n$fa-var-address-card: \"\\f2bb\";\n$fa-var-address-card-o: \"\\f2bc\";\n$fa-var-adjust: \"\\f042\";\n$fa-var-adn: \"\\f170\";\n$fa-var-align-center: \"\\f037\";\n$fa-var-align-justify: \"\\f039\";\n$fa-var-align-left: \"\\f036\";\n$fa-var-align-right: \"\\f038\";\n$fa-var-amazon: \"\\f270\";\n$fa-var-ambulance: \"\\f0f9\";\n$fa-var-american-sign-language-interpreting: \"\\f2a3\";\n$fa-var-anchor: \"\\f13d\";\n$fa-var-android: \"\\f17b\";\n$fa-var-angellist: \"\\f209\";\n$fa-var-angle-double-down: \"\\f103\";\n$fa-var-angle-double-left: \"\\f100\";\n$fa-var-angle-double-right: \"\\f101\";\n$fa-var-angle-double-up: \"\\f102\";\n$fa-var-angle-down: \"\\f107\";\n$fa-var-angle-left: \"\\f104\";\n$fa-var-angle-right: \"\\f105\";\n$fa-var-angle-up: \"\\f106\";\n$fa-var-apple: \"\\f179\";\n$fa-var-archive: \"\\f187\";\n$fa-var-area-chart: \"\\f1fe\";\n$fa-var-arrow-circle-down: \"\\f0ab\";\n$fa-var-arrow-circle-left: \"\\f0a8\";\n$fa-var-arrow-circle-o-down: \"\\f01a\";\n$fa-var-arrow-circle-o-left: \"\\f190\";\n$fa-var-arrow-circle-o-right: \"\\f18e\";\n$fa-var-arrow-circle-o-up: \"\\f01b\";\n$fa-var-arrow-circle-right: \"\\f0a9\";\n$fa-var-arrow-circle-up: \"\\f0aa\";\n$fa-var-arrow-down: \"\\f063\";\n$fa-var-arrow-left: \"\\f060\";\n$fa-var-arrow-right: \"\\f061\";\n$fa-var-arrow-up: \"\\f062\";\n$fa-var-arrows: \"\\f047\";\n$fa-var-arrows-alt: \"\\f0b2\";\n$fa-var-arrows-h: \"\\f07e\";\n$fa-var-arrows-v: \"\\f07d\";\n$fa-var-asl-interpreting: \"\\f2a3\";\n$fa-var-assistive-listening-systems: \"\\f2a2\";\n$fa-var-asterisk: \"\\f069\";\n$fa-var-at: \"\\f1fa\";\n$fa-var-audio-description: \"\\f29e\";\n$fa-var-automobile: \"\\f1b9\";\n$fa-var-backward: \"\\f04a\";\n$fa-var-balance-scale: \"\\f24e\";\n$fa-var-ban: \"\\f05e\";\n$fa-var-bandcamp: \"\\f2d5\";\n$fa-var-bank: \"\\f19c\";\n$fa-var-bar-chart: \"\\f080\";\n$fa-var-bar-chart-o: \"\\f080\";\n$fa-var-barcode: \"\\f02a\";\n$fa-var-bars: \"\\f0c9\";\n$fa-var-bath: \"\\f2cd\";\n$fa-var-bathtub: \"\\f2cd\";\n$fa-var-battery: \"\\f240\";\n$fa-var-battery-0: \"\\f244\";\n$fa-var-battery-1: \"\\f243\";\n$fa-var-battery-2: \"\\f242\";\n$fa-var-battery-3: \"\\f241\";\n$fa-var-battery-4: \"\\f240\";\n$fa-var-battery-empty: \"\\f244\";\n$fa-var-battery-full: \"\\f240\";\n$fa-var-battery-half: \"\\f242\";\n$fa-var-battery-quarter: \"\\f243\";\n$fa-var-battery-three-quarters: \"\\f241\";\n$fa-var-bed: \"\\f236\";\n$fa-var-beer: \"\\f0fc\";\n$fa-var-behance: \"\\f1b4\";\n$fa-var-behance-square: \"\\f1b5\";\n$fa-var-bell: \"\\f0f3\";\n$fa-var-bell-o: \"\\f0a2\";\n$fa-var-bell-slash: \"\\f1f6\";\n$fa-var-bell-slash-o: \"\\f1f7\";\n$fa-var-bicycle: \"\\f206\";\n$fa-var-binoculars: \"\\f1e5\";\n$fa-var-birthday-cake: \"\\f1fd\";\n$fa-var-bitbucket: \"\\f171\";\n$fa-var-bitbucket-square: \"\\f172\";\n$fa-var-bitcoin: \"\\f15a\";\n$fa-var-black-tie: \"\\f27e\";\n$fa-var-blind: \"\\f29d\";\n$fa-var-bluetooth: \"\\f293\";\n$fa-var-bluetooth-b: \"\\f294\";\n$fa-var-bold: \"\\f032\";\n$fa-var-bolt: \"\\f0e7\";\n$fa-var-bomb: \"\\f1e2\";\n$fa-var-book: \"\\f02d\";\n$fa-var-bookmark: \"\\f02e\";\n$fa-var-bookmark-o: \"\\f097\";\n$fa-var-braille: \"\\f2a1\";\n$fa-var-briefcase: \"\\f0b1\";\n$fa-var-btc: \"\\f15a\";\n$fa-var-bug: \"\\f188\";\n$fa-var-building: \"\\f1ad\";\n$fa-var-building-o: \"\\f0f7\";\n$fa-var-bullhorn: \"\\f0a1\";\n$fa-var-bullseye: \"\\f140\";\n$fa-var-bus: \"\\f207\";\n$fa-var-buysellads: \"\\f20d\";\n$fa-var-cab: \"\\f1ba\";\n$fa-var-calculator: \"\\f1ec\";\n$fa-var-calendar: \"\\f073\";\n$fa-var-calendar-check-o: \"\\f274\";\n$fa-var-calendar-minus-o: \"\\f272\";\n$fa-var-calendar-o: \"\\f133\";\n$fa-var-calendar-plus-o: \"\\f271\";\n$fa-var-calendar-times-o: \"\\f273\";\n$fa-var-camera: \"\\f030\";\n$fa-var-camera-retro: \"\\f083\";\n$fa-var-car: \"\\f1b9\";\n$fa-var-caret-down: \"\\f0d7\";\n$fa-var-caret-left: \"\\f0d9\";\n$fa-var-caret-right: \"\\f0da\";\n$fa-var-caret-square-o-down: \"\\f150\";\n$fa-var-caret-square-o-left: \"\\f191\";\n$fa-var-caret-square-o-right: \"\\f152\";\n$fa-var-caret-square-o-up: \"\\f151\";\n$fa-var-caret-up: \"\\f0d8\";\n$fa-var-cart-arrow-down: \"\\f218\";\n$fa-var-cart-plus: \"\\f217\";\n$fa-var-cc: \"\\f20a\";\n$fa-var-cc-amex: \"\\f1f3\";\n$fa-var-cc-diners-club: \"\\f24c\";\n$fa-var-cc-discover: \"\\f1f2\";\n$fa-var-cc-jcb: \"\\f24b\";\n$fa-var-cc-mastercard: \"\\f1f1\";\n$fa-var-cc-paypal: \"\\f1f4\";\n$fa-var-cc-stripe: \"\\f1f5\";\n$fa-var-cc-visa: \"\\f1f0\";\n$fa-var-certificate: \"\\f0a3\";\n$fa-var-chain: \"\\f0c1\";\n$fa-var-chain-broken: \"\\f127\";\n$fa-var-check: \"\\f00c\";\n$fa-var-check-circle: \"\\f058\";\n$fa-var-check-circle-o: \"\\f05d\";\n$fa-var-check-square: \"\\f14a\";\n$fa-var-check-square-o: \"\\f046\";\n$fa-var-chevron-circle-down: \"\\f13a\";\n$fa-var-chevron-circle-left: \"\\f137\";\n$fa-var-chevron-circle-right: \"\\f138\";\n$fa-var-chevron-circle-up: \"\\f139\";\n$fa-var-chevron-down: \"\\f078\";\n$fa-var-chevron-left: \"\\f053\";\n$fa-var-chevron-right: \"\\f054\";\n$fa-var-chevron-up: \"\\f077\";\n$fa-var-child: \"\\f1ae\";\n$fa-var-chrome: \"\\f268\";\n$fa-var-circle: \"\\f111\";\n$fa-var-circle-o: \"\\f10c\";\n$fa-var-circle-o-notch: \"\\f1ce\";\n$fa-var-circle-thin: \"\\f1db\";\n$fa-var-clipboard: \"\\f0ea\";\n$fa-var-clock-o: \"\\f017\";\n$fa-var-clone: \"\\f24d\";\n$fa-var-close: \"\\f00d\";\n$fa-var-cloud: \"\\f0c2\";\n$fa-var-cloud-download: \"\\f0ed\";\n$fa-var-cloud-upload: \"\\f0ee\";\n$fa-var-cny: \"\\f157\";\n$fa-var-code: \"\\f121\";\n$fa-var-code-fork: \"\\f126\";\n$fa-var-codepen: \"\\f1cb\";\n$fa-var-codiepie: \"\\f284\";\n$fa-var-coffee: \"\\f0f4\";\n$fa-var-cog: \"\\f013\";\n$fa-var-cogs: \"\\f085\";\n$fa-var-columns: \"\\f0db\";\n$fa-var-comment: \"\\f075\";\n$fa-var-comment-o: \"\\f0e5\";\n$fa-var-commenting: \"\\f27a\";\n$fa-var-commenting-o: \"\\f27b\";\n$fa-var-comments: \"\\f086\";\n$fa-var-comments-o: \"\\f0e6\";\n$fa-var-compass: \"\\f14e\";\n$fa-var-compress: \"\\f066\";\n$fa-var-connectdevelop: \"\\f20e\";\n$fa-var-contao: \"\\f26d\";\n$fa-var-copy: \"\\f0c5\";\n$fa-var-copyright: \"\\f1f9\";\n$fa-var-creative-commons: \"\\f25e\";\n$fa-var-credit-card: \"\\f09d\";\n$fa-var-credit-card-alt: \"\\f283\";\n$fa-var-crop: \"\\f125\";\n$fa-var-crosshairs: \"\\f05b\";\n$fa-var-css3: \"\\f13c\";\n$fa-var-cube: \"\\f1b2\";\n$fa-var-cubes: \"\\f1b3\";\n$fa-var-cut: \"\\f0c4\";\n$fa-var-cutlery: \"\\f0f5\";\n$fa-var-dashboard: \"\\f0e4\";\n$fa-var-dashcube: \"\\f210\";\n$fa-var-database: \"\\f1c0\";\n$fa-var-deaf: \"\\f2a4\";\n$fa-var-deafness: \"\\f2a4\";\n$fa-var-dedent: \"\\f03b\";\n$fa-var-delicious: \"\\f1a5\";\n$fa-var-desktop: \"\\f108\";\n$fa-var-deviantart: \"\\f1bd\";\n$fa-var-diamond: \"\\f219\";\n$fa-var-digg: \"\\f1a6\";\n$fa-var-dollar: \"\\f155\";\n$fa-var-dot-circle-o: \"\\f192\";\n$fa-var-download: \"\\f019\";\n$fa-var-dribbble: \"\\f17d\";\n$fa-var-drivers-license: \"\\f2c2\";\n$fa-var-drivers-license-o: \"\\f2c3\";\n$fa-var-dropbox: \"\\f16b\";\n$fa-var-drupal: \"\\f1a9\";\n$fa-var-edge: \"\\f282\";\n$fa-var-edit: \"\\f044\";\n$fa-var-eercast: \"\\f2da\";\n$fa-var-eject: \"\\f052\";\n$fa-var-ellipsis-h: \"\\f141\";\n$fa-var-ellipsis-v: \"\\f142\";\n$fa-var-empire: \"\\f1d1\";\n$fa-var-envelope: \"\\f0e0\";\n$fa-var-envelope-o: \"\\f003\";\n$fa-var-envelope-open: \"\\f2b6\";\n$fa-var-envelope-open-o: \"\\f2b7\";\n$fa-var-envelope-square: \"\\f199\";\n$fa-var-envira: \"\\f299\";\n$fa-var-eraser: \"\\f12d\";\n$fa-var-etsy: \"\\f2d7\";\n$fa-var-eur: \"\\f153\";\n$fa-var-euro: \"\\f153\";\n$fa-var-exchange: \"\\f0ec\";\n$fa-var-exclamation: \"\\f12a\";\n$fa-var-exclamation-circle: \"\\f06a\";\n$fa-var-exclamation-triangle: \"\\f071\";\n$fa-var-expand: \"\\f065\";\n$fa-var-expeditedssl: \"\\f23e\";\n$fa-var-external-link: \"\\f08e\";\n$fa-var-external-link-square: \"\\f14c\";\n$fa-var-eye: \"\\f06e\";\n$fa-var-eye-slash: \"\\f070\";\n$fa-var-eyedropper: \"\\f1fb\";\n$fa-var-fa: \"\\f2b4\";\n$fa-var-facebook: \"\\f09a\";\n$fa-var-facebook-f: \"\\f09a\";\n$fa-var-facebook-official: \"\\f230\";\n$fa-var-facebook-square: \"\\f082\";\n$fa-var-fast-backward: \"\\f049\";\n$fa-var-fast-forward: \"\\f050\";\n$fa-var-fax: \"\\f1ac\";\n$fa-var-feed: \"\\f09e\";\n$fa-var-female: \"\\f182\";\n$fa-var-fighter-jet: \"\\f0fb\";\n$fa-var-file: \"\\f15b\";\n$fa-var-file-archive-o: \"\\f1c6\";\n$fa-var-file-audio-o: \"\\f1c7\";\n$fa-var-file-code-o: \"\\f1c9\";\n$fa-var-file-excel-o: \"\\f1c3\";\n$fa-var-file-image-o: \"\\f1c5\";\n$fa-var-file-movie-o: \"\\f1c8\";\n$fa-var-file-o: \"\\f016\";\n$fa-var-file-pdf-o: \"\\f1c1\";\n$fa-var-file-photo-o: \"\\f1c5\";\n$fa-var-file-picture-o: \"\\f1c5\";\n$fa-var-file-powerpoint-o: \"\\f1c4\";\n$fa-var-file-sound-o: \"\\f1c7\";\n$fa-var-file-text: \"\\f15c\";\n$fa-var-file-text-o: \"\\f0f6\";\n$fa-var-file-video-o: \"\\f1c8\";\n$fa-var-file-word-o: \"\\f1c2\";\n$fa-var-file-zip-o: \"\\f1c6\";\n$fa-var-files-o: \"\\f0c5\";\n$fa-var-film: \"\\f008\";\n$fa-var-filter: \"\\f0b0\";\n$fa-var-fire: \"\\f06d\";\n$fa-var-fire-extinguisher: \"\\f134\";\n$fa-var-firefox: \"\\f269\";\n$fa-var-first-order: \"\\f2b0\";\n$fa-var-flag: \"\\f024\";\n$fa-var-flag-checkered: \"\\f11e\";\n$fa-var-flag-o: \"\\f11d\";\n$fa-var-flash: \"\\f0e7\";\n$fa-var-flask: \"\\f0c3\";\n$fa-var-flickr: \"\\f16e\";\n$fa-var-floppy-o: \"\\f0c7\";\n$fa-var-folder: \"\\f07b\";\n$fa-var-folder-o: \"\\f114\";\n$fa-var-folder-open: \"\\f07c\";\n$fa-var-folder-open-o: \"\\f115\";\n$fa-var-font: \"\\f031\";\n$fa-var-font-awesome: \"\\f2b4\";\n$fa-var-fonticons: \"\\f280\";\n$fa-var-fort-awesome: \"\\f286\";\n$fa-var-forumbee: \"\\f211\";\n$fa-var-forward: \"\\f04e\";\n$fa-var-foursquare: \"\\f180\";\n$fa-var-free-code-camp: \"\\f2c5\";\n$fa-var-frown-o: \"\\f119\";\n$fa-var-futbol-o: \"\\f1e3\";\n$fa-var-gamepad: \"\\f11b\";\n$fa-var-gavel: \"\\f0e3\";\n$fa-var-gbp: \"\\f154\";\n$fa-var-ge: \"\\f1d1\";\n$fa-var-gear: \"\\f013\";\n$fa-var-gears: \"\\f085\";\n$fa-var-genderless: \"\\f22d\";\n$fa-var-get-pocket: \"\\f265\";\n$fa-var-gg: \"\\f260\";\n$fa-var-gg-circle: \"\\f261\";\n$fa-var-gift: \"\\f06b\";\n$fa-var-git: \"\\f1d3\";\n$fa-var-git-square: \"\\f1d2\";\n$fa-var-github: \"\\f09b\";\n$fa-var-github-alt: \"\\f113\";\n$fa-var-github-square: \"\\f092\";\n$fa-var-gitlab: \"\\f296\";\n$fa-var-gittip: \"\\f184\";\n$fa-var-glass: \"\\f000\";\n$fa-var-glide: \"\\f2a5\";\n$fa-var-glide-g: \"\\f2a6\";\n$fa-var-globe: \"\\f0ac\";\n$fa-var-google: \"\\f1a0\";\n$fa-var-google-plus: \"\\f0d5\";\n$fa-var-google-plus-circle: \"\\f2b3\";\n$fa-var-google-plus-official: \"\\f2b3\";\n$fa-var-google-plus-square: \"\\f0d4\";\n$fa-var-google-wallet: \"\\f1ee\";\n$fa-var-graduation-cap: \"\\f19d\";\n$fa-var-gratipay: \"\\f184\";\n$fa-var-grav: \"\\f2d6\";\n$fa-var-group: \"\\f0c0\";\n$fa-var-h-square: \"\\f0fd\";\n$fa-var-hacker-news: \"\\f1d4\";\n$fa-var-hand-grab-o: \"\\f255\";\n$fa-var-hand-lizard-o: \"\\f258\";\n$fa-var-hand-o-down: \"\\f0a7\";\n$fa-var-hand-o-left: \"\\f0a5\";\n$fa-var-hand-o-right: \"\\f0a4\";\n$fa-var-hand-o-up: \"\\f0a6\";\n$fa-var-hand-paper-o: \"\\f256\";\n$fa-var-hand-peace-o: \"\\f25b\";\n$fa-var-hand-pointer-o: \"\\f25a\";\n$fa-var-hand-rock-o: \"\\f255\";\n$fa-var-hand-scissors-o: \"\\f257\";\n$fa-var-hand-spock-o: \"\\f259\";\n$fa-var-hand-stop-o: \"\\f256\";\n$fa-var-handshake-o: \"\\f2b5\";\n$fa-var-hard-of-hearing: \"\\f2a4\";\n$fa-var-hashtag: \"\\f292\";\n$fa-var-hdd-o: \"\\f0a0\";\n$fa-var-header: \"\\f1dc\";\n$fa-var-headphones: \"\\f025\";\n$fa-var-heart: \"\\f004\";\n$fa-var-heart-o: \"\\f08a\";\n$fa-var-heartbeat: \"\\f21e\";\n$fa-var-history: \"\\f1da\";\n$fa-var-home: \"\\f015\";\n$fa-var-hospital-o: \"\\f0f8\";\n$fa-var-hotel: \"\\f236\";\n$fa-var-hourglass: \"\\f254\";\n$fa-var-hourglass-1: \"\\f251\";\n$fa-var-hourglass-2: \"\\f252\";\n$fa-var-hourglass-3: \"\\f253\";\n$fa-var-hourglass-end: \"\\f253\";\n$fa-var-hourglass-half: \"\\f252\";\n$fa-var-hourglass-o: \"\\f250\";\n$fa-var-hourglass-start: \"\\f251\";\n$fa-var-houzz: \"\\f27c\";\n$fa-var-html5: \"\\f13b\";\n$fa-var-i-cursor: \"\\f246\";\n$fa-var-id-badge: \"\\f2c1\";\n$fa-var-id-card: \"\\f2c2\";\n$fa-var-id-card-o: \"\\f2c3\";\n$fa-var-ils: \"\\f20b\";\n$fa-var-image: \"\\f03e\";\n$fa-var-imdb: \"\\f2d8\";\n$fa-var-inbox: \"\\f01c\";\n$fa-var-indent: \"\\f03c\";\n$fa-var-industry: \"\\f275\";\n$fa-var-info: \"\\f129\";\n$fa-var-info-circle: \"\\f05a\";\n$fa-var-inr: \"\\f156\";\n$fa-var-instagram: \"\\f16d\";\n$fa-var-institution: \"\\f19c\";\n$fa-var-internet-explorer: \"\\f26b\";\n$fa-var-intersex: \"\\f224\";\n$fa-var-ioxhost: \"\\f208\";\n$fa-var-italic: \"\\f033\";\n$fa-var-joomla: \"\\f1aa\";\n$fa-var-jpy: \"\\f157\";\n$fa-var-jsfiddle: \"\\f1cc\";\n$fa-var-key: \"\\f084\";\n$fa-var-keyboard-o: \"\\f11c\";\n$fa-var-krw: \"\\f159\";\n$fa-var-language: \"\\f1ab\";\n$fa-var-laptop: \"\\f109\";\n$fa-var-lastfm: \"\\f202\";\n$fa-var-lastfm-square: \"\\f203\";\n$fa-var-leaf: \"\\f06c\";\n$fa-var-leanpub: \"\\f212\";\n$fa-var-legal: \"\\f0e3\";\n$fa-var-lemon-o: \"\\f094\";\n$fa-var-level-down: \"\\f149\";\n$fa-var-level-up: \"\\f148\";\n$fa-var-life-bouy: \"\\f1cd\";\n$fa-var-life-buoy: \"\\f1cd\";\n$fa-var-life-ring: \"\\f1cd\";\n$fa-var-life-saver: \"\\f1cd\";\n$fa-var-lightbulb-o: \"\\f0eb\";\n$fa-var-line-chart: \"\\f201\";\n$fa-var-link: \"\\f0c1\";\n$fa-var-linkedin: \"\\f0e1\";\n$fa-var-linkedin-square: \"\\f08c\";\n$fa-var-linode: \"\\f2b8\";\n$fa-var-linux: \"\\f17c\";\n$fa-var-list: \"\\f03a\";\n$fa-var-list-alt: \"\\f022\";\n$fa-var-list-ol: \"\\f0cb\";\n$fa-var-list-ul: \"\\f0ca\";\n$fa-var-location-arrow: \"\\f124\";\n$fa-var-lock: \"\\f023\";\n$fa-var-long-arrow-down: \"\\f175\";\n$fa-var-long-arrow-left: \"\\f177\";\n$fa-var-long-arrow-right: \"\\f178\";\n$fa-var-long-arrow-up: \"\\f176\";\n$fa-var-low-vision: \"\\f2a8\";\n$fa-var-magic: \"\\f0d0\";\n$fa-var-magnet: \"\\f076\";\n$fa-var-mail-forward: \"\\f064\";\n$fa-var-mail-reply: \"\\f112\";\n$fa-var-mail-reply-all: \"\\f122\";\n$fa-var-male: \"\\f183\";\n$fa-var-map: \"\\f279\";\n$fa-var-map-marker: \"\\f041\";\n$fa-var-map-o: \"\\f278\";\n$fa-var-map-pin: \"\\f276\";\n$fa-var-map-signs: \"\\f277\";\n$fa-var-mars: \"\\f222\";\n$fa-var-mars-double: \"\\f227\";\n$fa-var-mars-stroke: \"\\f229\";\n$fa-var-mars-stroke-h: \"\\f22b\";\n$fa-var-mars-stroke-v: \"\\f22a\";\n$fa-var-maxcdn: \"\\f136\";\n$fa-var-meanpath: \"\\f20c\";\n$fa-var-medium: \"\\f23a\";\n$fa-var-medkit: \"\\f0fa\";\n$fa-var-meetup: \"\\f2e0\";\n$fa-var-meh-o: \"\\f11a\";\n$fa-var-mercury: \"\\f223\";\n$fa-var-microchip: \"\\f2db\";\n$fa-var-microphone: \"\\f130\";\n$fa-var-microphone-slash: \"\\f131\";\n$fa-var-minus: \"\\f068\";\n$fa-var-minus-circle: \"\\f056\";\n$fa-var-minus-square: \"\\f146\";\n$fa-var-minus-square-o: \"\\f147\";\n$fa-var-mixcloud: \"\\f289\";\n$fa-var-mobile: \"\\f10b\";\n$fa-var-mobile-phone: \"\\f10b\";\n$fa-var-modx: \"\\f285\";\n$fa-var-money: \"\\f0d6\";\n$fa-var-moon-o: \"\\f186\";\n$fa-var-mortar-board: \"\\f19d\";\n$fa-var-motorcycle: \"\\f21c\";\n$fa-var-mouse-pointer: \"\\f245\";\n$fa-var-music: \"\\f001\";\n$fa-var-navicon: \"\\f0c9\";\n$fa-var-neuter: \"\\f22c\";\n$fa-var-newspaper-o: \"\\f1ea\";\n$fa-var-object-group: \"\\f247\";\n$fa-var-object-ungroup: \"\\f248\";\n$fa-var-odnoklassniki: \"\\f263\";\n$fa-var-odnoklassniki-square: \"\\f264\";\n$fa-var-opencart: \"\\f23d\";\n$fa-var-openid: \"\\f19b\";\n$fa-var-opera: \"\\f26a\";\n$fa-var-optin-monster: \"\\f23c\";\n$fa-var-outdent: \"\\f03b\";\n$fa-var-pagelines: \"\\f18c\";\n$fa-var-paint-brush: \"\\f1fc\";\n$fa-var-paper-plane: \"\\f1d8\";\n$fa-var-paper-plane-o: \"\\f1d9\";\n$fa-var-paperclip: \"\\f0c6\";\n$fa-var-paragraph: \"\\f1dd\";\n$fa-var-paste: \"\\f0ea\";\n$fa-var-pause: \"\\f04c\";\n$fa-var-pause-circle: \"\\f28b\";\n$fa-var-pause-circle-o: \"\\f28c\";\n$fa-var-paw: \"\\f1b0\";\n$fa-var-paypal: \"\\f1ed\";\n$fa-var-pencil: \"\\f040\";\n$fa-var-pencil-square: \"\\f14b\";\n$fa-var-pencil-square-o: \"\\f044\";\n$fa-var-percent: \"\\f295\";\n$fa-var-phone: \"\\f095\";\n$fa-var-phone-square: \"\\f098\";\n$fa-var-photo: \"\\f03e\";\n$fa-var-picture-o: \"\\f03e\";\n$fa-var-pie-chart: \"\\f200\";\n$fa-var-pied-piper: \"\\f2ae\";\n$fa-var-pied-piper-alt: \"\\f1a8\";\n$fa-var-pied-piper-pp: \"\\f1a7\";\n$fa-var-pinterest: \"\\f0d2\";\n$fa-var-pinterest-p: \"\\f231\";\n$fa-var-pinterest-square: \"\\f0d3\";\n$fa-var-plane: \"\\f072\";\n$fa-var-play: \"\\f04b\";\n$fa-var-play-circle: \"\\f144\";\n$fa-var-play-circle-o: \"\\f01d\";\n$fa-var-plug: \"\\f1e6\";\n$fa-var-plus: \"\\f067\";\n$fa-var-plus-circle: \"\\f055\";\n$fa-var-plus-square: \"\\f0fe\";\n$fa-var-plus-square-o: \"\\f196\";\n$fa-var-podcast: \"\\f2ce\";\n$fa-var-power-off: \"\\f011\";\n$fa-var-print: \"\\f02f\";\n$fa-var-product-hunt: \"\\f288\";\n$fa-var-puzzle-piece: \"\\f12e\";\n$fa-var-qq: \"\\f1d6\";\n$fa-var-qrcode: \"\\f029\";\n$fa-var-question: \"\\f128\";\n$fa-var-question-circle: \"\\f059\";\n$fa-var-question-circle-o: \"\\f29c\";\n$fa-var-quora: \"\\f2c4\";\n$fa-var-quote-left: \"\\f10d\";\n$fa-var-quote-right: \"\\f10e\";\n$fa-var-ra: \"\\f1d0\";\n$fa-var-random: \"\\f074\";\n$fa-var-ravelry: \"\\f2d9\";\n$fa-var-rebel: \"\\f1d0\";\n$fa-var-recycle: \"\\f1b8\";\n$fa-var-reddit: \"\\f1a1\";\n$fa-var-reddit-alien: \"\\f281\";\n$fa-var-reddit-square: \"\\f1a2\";\n$fa-var-refresh: \"\\f021\";\n$fa-var-registered: \"\\f25d\";\n$fa-var-remove: \"\\f00d\";\n$fa-var-renren: \"\\f18b\";\n$fa-var-reorder: \"\\f0c9\";\n$fa-var-repeat: \"\\f01e\";\n$fa-var-reply: \"\\f112\";\n$fa-var-reply-all: \"\\f122\";\n$fa-var-resistance: \"\\f1d0\";\n$fa-var-retweet: \"\\f079\";\n$fa-var-rmb: \"\\f157\";\n$fa-var-road: \"\\f018\";\n$fa-var-rocket: \"\\f135\";\n$fa-var-rotate-left: \"\\f0e2\";\n$fa-var-rotate-right: \"\\f01e\";\n$fa-var-rouble: \"\\f158\";\n$fa-var-rss: \"\\f09e\";\n$fa-var-rss-square: \"\\f143\";\n$fa-var-rub: \"\\f158\";\n$fa-var-ruble: \"\\f158\";\n$fa-var-rupee: \"\\f156\";\n$fa-var-s15: \"\\f2cd\";\n$fa-var-safari: \"\\f267\";\n$fa-var-save: \"\\f0c7\";\n$fa-var-scissors: \"\\f0c4\";\n$fa-var-scribd: \"\\f28a\";\n$fa-var-search: \"\\f002\";\n$fa-var-search-minus: \"\\f010\";\n$fa-var-search-plus: \"\\f00e\";\n$fa-var-sellsy: \"\\f213\";\n$fa-var-send: \"\\f1d8\";\n$fa-var-send-o: \"\\f1d9\";\n$fa-var-server: \"\\f233\";\n$fa-var-share: \"\\f064\";\n$fa-var-share-alt: \"\\f1e0\";\n$fa-var-share-alt-square: \"\\f1e1\";\n$fa-var-share-square: \"\\f14d\";\n$fa-var-share-square-o: \"\\f045\";\n$fa-var-shekel: \"\\f20b\";\n$fa-var-sheqel: \"\\f20b\";\n$fa-var-shield: \"\\f132\";\n$fa-var-ship: \"\\f21a\";\n$fa-var-shirtsinbulk: \"\\f214\";\n$fa-var-shopping-bag: \"\\f290\";\n$fa-var-shopping-basket: \"\\f291\";\n$fa-var-shopping-cart: \"\\f07a\";\n$fa-var-shower: \"\\f2cc\";\n$fa-var-sign-in: \"\\f090\";\n$fa-var-sign-language: \"\\f2a7\";\n$fa-var-sign-out: \"\\f08b\";\n$fa-var-signal: \"\\f012\";\n$fa-var-signing: \"\\f2a7\";\n$fa-var-simplybuilt: \"\\f215\";\n$fa-var-sitemap: \"\\f0e8\";\n$fa-var-skyatlas: \"\\f216\";\n$fa-var-skype: \"\\f17e\";\n$fa-var-slack: \"\\f198\";\n$fa-var-sliders: \"\\f1de\";\n$fa-var-slideshare: \"\\f1e7\";\n$fa-var-smile-o: \"\\f118\";\n$fa-var-snapchat: \"\\f2ab\";\n$fa-var-snapchat-ghost: \"\\f2ac\";\n$fa-var-snapchat-square: \"\\f2ad\";\n$fa-var-snowflake-o: \"\\f2dc\";\n$fa-var-soccer-ball-o: \"\\f1e3\";\n$fa-var-sort: \"\\f0dc\";\n$fa-var-sort-alpha-asc: \"\\f15d\";\n$fa-var-sort-alpha-desc: \"\\f15e\";\n$fa-var-sort-amount-asc: \"\\f160\";\n$fa-var-sort-amount-desc: \"\\f161\";\n$fa-var-sort-asc: \"\\f0de\";\n$fa-var-sort-desc: \"\\f0dd\";\n$fa-var-sort-down: \"\\f0dd\";\n$fa-var-sort-numeric-asc: \"\\f162\";\n$fa-var-sort-numeric-desc: \"\\f163\";\n$fa-var-sort-up: \"\\f0de\";\n$fa-var-soundcloud: \"\\f1be\";\n$fa-var-space-shuttle: \"\\f197\";\n$fa-var-spinner: \"\\f110\";\n$fa-var-spoon: \"\\f1b1\";\n$fa-var-spotify: \"\\f1bc\";\n$fa-var-square: \"\\f0c8\";\n$fa-var-square-o: \"\\f096\";\n$fa-var-stack-exchange: \"\\f18d\";\n$fa-var-stack-overflow: \"\\f16c\";\n$fa-var-star: \"\\f005\";\n$fa-var-star-half: \"\\f089\";\n$fa-var-star-half-empty: \"\\f123\";\n$fa-var-star-half-full: \"\\f123\";\n$fa-var-star-half-o: \"\\f123\";\n$fa-var-star-o: \"\\f006\";\n$fa-var-steam: \"\\f1b6\";\n$fa-var-steam-square: \"\\f1b7\";\n$fa-var-step-backward: \"\\f048\";\n$fa-var-step-forward: \"\\f051\";\n$fa-var-stethoscope: \"\\f0f1\";\n$fa-var-sticky-note: \"\\f249\";\n$fa-var-sticky-note-o: \"\\f24a\";\n$fa-var-stop: \"\\f04d\";\n$fa-var-stop-circle: \"\\f28d\";\n$fa-var-stop-circle-o: \"\\f28e\";\n$fa-var-street-view: \"\\f21d\";\n$fa-var-strikethrough: \"\\f0cc\";\n$fa-var-stumbleupon: \"\\f1a4\";\n$fa-var-stumbleupon-circle: \"\\f1a3\";\n$fa-var-subscript: \"\\f12c\";\n$fa-var-subway: \"\\f239\";\n$fa-var-suitcase: \"\\f0f2\";\n$fa-var-sun-o: \"\\f185\";\n$fa-var-superpowers: \"\\f2dd\";\n$fa-var-superscript: \"\\f12b\";\n$fa-var-support: \"\\f1cd\";\n$fa-var-table: \"\\f0ce\";\n$fa-var-tablet: \"\\f10a\";\n$fa-var-tachometer: \"\\f0e4\";\n$fa-var-tag: \"\\f02b\";\n$fa-var-tags: \"\\f02c\";\n$fa-var-tasks: \"\\f0ae\";\n$fa-var-taxi: \"\\f1ba\";\n$fa-var-telegram: \"\\f2c6\";\n$fa-var-television: \"\\f26c\";\n$fa-var-tencent-weibo: \"\\f1d5\";\n$fa-var-terminal: \"\\f120\";\n$fa-var-text-height: \"\\f034\";\n$fa-var-text-width: \"\\f035\";\n$fa-var-th: \"\\f00a\";\n$fa-var-th-large: \"\\f009\";\n$fa-var-th-list: \"\\f00b\";\n$fa-var-themeisle: \"\\f2b2\";\n$fa-var-thermometer: \"\\f2c7\";\n$fa-var-thermometer-0: \"\\f2cb\";\n$fa-var-thermometer-1: \"\\f2ca\";\n$fa-var-thermometer-2: \"\\f2c9\";\n$fa-var-thermometer-3: \"\\f2c8\";\n$fa-var-thermometer-4: \"\\f2c7\";\n$fa-var-thermometer-empty: \"\\f2cb\";\n$fa-var-thermometer-full: \"\\f2c7\";\n$fa-var-thermometer-half: \"\\f2c9\";\n$fa-var-thermometer-quarter: \"\\f2ca\";\n$fa-var-thermometer-three-quarters: \"\\f2c8\";\n$fa-var-thumb-tack: \"\\f08d\";\n$fa-var-thumbs-down: \"\\f165\";\n$fa-var-thumbs-o-down: \"\\f088\";\n$fa-var-thumbs-o-up: \"\\f087\";\n$fa-var-thumbs-up: \"\\f164\";\n$fa-var-ticket: \"\\f145\";\n$fa-var-times: \"\\f00d\";\n$fa-var-times-circle: \"\\f057\";\n$fa-var-times-circle-o: \"\\f05c\";\n$fa-var-times-rectangle: \"\\f2d3\";\n$fa-var-times-rectangle-o: \"\\f2d4\";\n$fa-var-tint: \"\\f043\";\n$fa-var-toggle-down: \"\\f150\";\n$fa-var-toggle-left: \"\\f191\";\n$fa-var-toggle-off: \"\\f204\";\n$fa-var-toggle-on: \"\\f205\";\n$fa-var-toggle-right: \"\\f152\";\n$fa-var-toggle-up: \"\\f151\";\n$fa-var-trademark: \"\\f25c\";\n$fa-var-train: \"\\f238\";\n$fa-var-transgender: \"\\f224\";\n$fa-var-transgender-alt: \"\\f225\";\n$fa-var-trash: \"\\f1f8\";\n$fa-var-trash-o: \"\\f014\";\n$fa-var-tree: \"\\f1bb\";\n$fa-var-trello: \"\\f181\";\n$fa-var-tripadvisor: \"\\f262\";\n$fa-var-trophy: \"\\f091\";\n$fa-var-truck: \"\\f0d1\";\n$fa-var-try: \"\\f195\";\n$fa-var-tty: \"\\f1e4\";\n$fa-var-tumblr: \"\\f173\";\n$fa-var-tumblr-square: \"\\f174\";\n$fa-var-turkish-lira: \"\\f195\";\n$fa-var-tv: \"\\f26c\";\n$fa-var-twitch: \"\\f1e8\";\n$fa-var-twitter: \"\\f099\";\n$fa-var-twitter-square: \"\\f081\";\n$fa-var-umbrella: \"\\f0e9\";\n$fa-var-underline: \"\\f0cd\";\n$fa-var-undo: \"\\f0e2\";\n$fa-var-universal-access: \"\\f29a\";\n$fa-var-university: \"\\f19c\";\n$fa-var-unlink: \"\\f127\";\n$fa-var-unlock: \"\\f09c\";\n$fa-var-unlock-alt: \"\\f13e\";\n$fa-var-unsorted: \"\\f0dc\";\n$fa-var-upload: \"\\f093\";\n$fa-var-usb: \"\\f287\";\n$fa-var-usd: \"\\f155\";\n$fa-var-user: \"\\f007\";\n$fa-var-user-circle: \"\\f2bd\";\n$fa-var-user-circle-o: \"\\f2be\";\n$fa-var-user-md: \"\\f0f0\";\n$fa-var-user-o: \"\\f2c0\";\n$fa-var-user-plus: \"\\f234\";\n$fa-var-user-secret: \"\\f21b\";\n$fa-var-user-times: \"\\f235\";\n$fa-var-users: \"\\f0c0\";\n$fa-var-vcard: \"\\f2bb\";\n$fa-var-vcard-o: \"\\f2bc\";\n$fa-var-venus: \"\\f221\";\n$fa-var-venus-double: \"\\f226\";\n$fa-var-venus-mars: \"\\f228\";\n$fa-var-viacoin: \"\\f237\";\n$fa-var-viadeo: \"\\f2a9\";\n$fa-var-viadeo-square: \"\\f2aa\";\n$fa-var-video-camera: \"\\f03d\";\n$fa-var-vimeo: \"\\f27d\";\n$fa-var-vimeo-square: \"\\f194\";\n$fa-var-vine: \"\\f1ca\";\n$fa-var-vk: \"\\f189\";\n$fa-var-volume-control-phone: \"\\f2a0\";\n$fa-var-volume-down: \"\\f027\";\n$fa-var-volume-off: \"\\f026\";\n$fa-var-volume-up: \"\\f028\";\n$fa-var-warning: \"\\f071\";\n$fa-var-wechat: \"\\f1d7\";\n$fa-var-weibo: \"\\f18a\";\n$fa-var-weixin: \"\\f1d7\";\n$fa-var-whatsapp: \"\\f232\";\n$fa-var-wheelchair: \"\\f193\";\n$fa-var-wheelchair-alt: \"\\f29b\";\n$fa-var-wifi: \"\\f1eb\";\n$fa-var-wikipedia-w: \"\\f266\";\n$fa-var-window-close: \"\\f2d3\";\n$fa-var-window-close-o: \"\\f2d4\";\n$fa-var-window-maximize: \"\\f2d0\";\n$fa-var-window-minimize: \"\\f2d1\";\n$fa-var-window-restore: \"\\f2d2\";\n$fa-var-windows: \"\\f17a\";\n$fa-var-won: \"\\f159\";\n$fa-var-wordpress: \"\\f19a\";\n$fa-var-wpbeginner: \"\\f297\";\n$fa-var-wpexplorer: \"\\f2de\";\n$fa-var-wpforms: \"\\f298\";\n$fa-var-wrench: \"\\f0ad\";\n$fa-var-xing: \"\\f168\";\n$fa-var-xing-square: \"\\f169\";\n$fa-var-y-combinator: \"\\f23b\";\n$fa-var-y-combinator-square: \"\\f1d4\";\n$fa-var-yahoo: \"\\f19e\";\n$fa-var-yc: \"\\f23b\";\n$fa-var-yc-square: \"\\f1d4\";\n$fa-var-yelp: \"\\f1e9\";\n$fa-var-yen: \"\\f157\";\n$fa-var-yoast: \"\\f2b1\";\n$fa-var-youtube: \"\\f167\";\n$fa-var-youtube-play: \"\\f16a\";\n$fa-var-youtube-square: \"\\f166\";\n\n", "// Bordered & Pulled\n// -------------------------\n\n.#{$fa-css-prefix}-border {\n  padding: .2em .25em .15em;\n  border: solid .08em $fa-border-color;\n  border-radius: .1em;\n}\n\n.#{$fa-css-prefix}-pull-left { float: left; }\n.#{$fa-css-prefix}-pull-right { float: right; }\n\n.#{$fa-css-prefix} {\n  &.#{$fa-css-prefix}-pull-left { margin-right: .3em; }\n  &.#{$fa-css-prefix}-pull-right { margin-left: .3em; }\n}\n\n/* Deprecated as of 4.4.0 */\n.pull-right { float: right; }\n.pull-left { float: left; }\n\n.#{$fa-css-prefix} {\n  &.pull-left { margin-right: .3em; }\n  &.pull-right { margin-left: .3em; }\n}\n", "// Spinning Icons\n// --------------------------\n\n.#{$fa-css-prefix}-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.#{$fa-css-prefix}-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(359deg);\n            transform: rotate(359deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(359deg);\n            transform: rotate(359deg);\n  }\n}\n", "// Rotated & Flipped Icons\n// -------------------------\n\n.#{$fa-css-prefix}-rotate-90  { @include fa-icon-rotate(90deg, 1);  }\n.#{$fa-css-prefix}-rotate-180 { @include fa-icon-rotate(180deg, 2); }\n.#{$fa-css-prefix}-rotate-270 { @include fa-icon-rotate(270deg, 3); }\n\n.#{$fa-css-prefix}-flip-horizontal { @include fa-icon-flip(-1, 1, 0); }\n.#{$fa-css-prefix}-flip-vertical   { @include fa-icon-flip(1, -1, 2); }\n\n// Hook for IE8-9\n// -------------------------\n\n:root .#{$fa-css-prefix}-rotate-90,\n:root .#{$fa-css-prefix}-rotate-180,\n:root .#{$fa-css-prefix}-rotate-270,\n:root .#{$fa-css-prefix}-flip-horizontal,\n:root .#{$fa-css-prefix}-flip-vertical {\n  filter: none;\n}\n", "// Mixins\n// --------------------------\n\n@mixin fa-icon() {\n  display: inline-block;\n  font: normal normal normal #{$fa-font-size-base}/#{$fa-line-height-base} FontAwesome; // shortening font declaration\n  font-size: inherit; // can't have font-size inherit on line above, so need to override\n  text-rendering: auto; // optimizelegibility throws things off #1094\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n}\n\n@mixin fa-icon-rotate($degrees, $rotation) {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation})\";\n  -webkit-transform: rotate($degrees);\n      -ms-transform: rotate($degrees);\n          transform: rotate($degrees);\n}\n\n@mixin fa-icon-flip($horiz, $vert, $rotation) {\n  -ms-filter: \"progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$rotation}, mirror=1)\";\n  -webkit-transform: scale($horiz, $vert);\n      -ms-transform: scale($horiz, $vert);\n          transform: scale($horiz, $vert);\n}\n\n\n// Only display content to screen readers. A la Bootstrap 4.\n//\n// See: http://a11yproject.com/posts/how-to-hide-content/\n\n@mixin sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0,0,0,0);\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see http://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    margin: 0;\n    overflow: visible;\n    clip: auto;\n  }\n}\n", "// Stacked Icons\n// -------------------------\n\n.#{$fa-css-prefix}-stack {\n  position: relative;\n  display: inline-block;\n  width: 2em;\n  height: 2em;\n  line-height: 2em;\n  vertical-align: middle;\n}\n.#{$fa-css-prefix}-stack-1x, .#{$fa-css-prefix}-stack-2x {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  text-align: center;\n}\n.#{$fa-css-prefix}-stack-1x { line-height: inherit; }\n.#{$fa-css-prefix}-stack-2x { font-size: 2em; }\n.#{$fa-css-prefix}-inverse { color: $fa-inverse; }\n", "/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen\n   readers do not read off random characters that represent icons */\n\n.#{$fa-css-prefix}-glass:before { content: $fa-var-glass; }\n.#{$fa-css-prefix}-music:before { content: $fa-var-music; }\n.#{$fa-css-prefix}-search:before { content: $fa-var-search; }\n.#{$fa-css-prefix}-envelope-o:before { content: $fa-var-envelope-o; }\n.#{$fa-css-prefix}-heart:before { content: $fa-var-heart; }\n.#{$fa-css-prefix}-star:before { content: $fa-var-star; }\n.#{$fa-css-prefix}-star-o:before { content: $fa-var-star-o; }\n.#{$fa-css-prefix}-user:before { content: $fa-var-user; }\n.#{$fa-css-prefix}-film:before { content: $fa-var-film; }\n.#{$fa-css-prefix}-th-large:before { content: $fa-var-th-large; }\n.#{$fa-css-prefix}-th:before { content: $fa-var-th; }\n.#{$fa-css-prefix}-th-list:before { content: $fa-var-th-list; }\n.#{$fa-css-prefix}-check:before { content: $fa-var-check; }\n.#{$fa-css-prefix}-remove:before,\n.#{$fa-css-prefix}-close:before,\n.#{$fa-css-prefix}-times:before { content: $fa-var-times; }\n.#{$fa-css-prefix}-search-plus:before { content: $fa-var-search-plus; }\n.#{$fa-css-prefix}-search-minus:before { content: $fa-var-search-minus; }\n.#{$fa-css-prefix}-power-off:before { content: $fa-var-power-off; }\n.#{$fa-css-prefix}-signal:before { content: $fa-var-signal; }\n.#{$fa-css-prefix}-gear:before,\n.#{$fa-css-prefix}-cog:before { content: $fa-var-cog; }\n.#{$fa-css-prefix}-trash-o:before { content: $fa-var-trash-o; }\n.#{$fa-css-prefix}-home:before { content: $fa-var-home; }\n.#{$fa-css-prefix}-file-o:before { content: $fa-var-file-o; }\n.#{$fa-css-prefix}-clock-o:before { content: $fa-var-clock-o; }\n.#{$fa-css-prefix}-road:before { content: $fa-var-road; }\n.#{$fa-css-prefix}-download:before { content: $fa-var-download; }\n.#{$fa-css-prefix}-arrow-circle-o-down:before { content: $fa-var-arrow-circle-o-down; }\n.#{$fa-css-prefix}-arrow-circle-o-up:before { content: $fa-var-arrow-circle-o-up; }\n.#{$fa-css-prefix}-inbox:before { content: $fa-var-inbox; }\n.#{$fa-css-prefix}-play-circle-o:before { content: $fa-var-play-circle-o; }\n.#{$fa-css-prefix}-rotate-right:before,\n.#{$fa-css-prefix}-repeat:before { content: $fa-var-repeat; }\n.#{$fa-css-prefix}-refresh:before { content: $fa-var-refresh; }\n.#{$fa-css-prefix}-list-alt:before { content: $fa-var-list-alt; }\n.#{$fa-css-prefix}-lock:before { content: $fa-var-lock; }\n.#{$fa-css-prefix}-flag:before { content: $fa-var-flag; }\n.#{$fa-css-prefix}-headphones:before { content: $fa-var-headphones; }\n.#{$fa-css-prefix}-volume-off:before { content: $fa-var-volume-off; }\n.#{$fa-css-prefix}-volume-down:before { content: $fa-var-volume-down; }\n.#{$fa-css-prefix}-volume-up:before { content: $fa-var-volume-up; }\n.#{$fa-css-prefix}-qrcode:before { content: $fa-var-qrcode; }\n.#{$fa-css-prefix}-barcode:before { content: $fa-var-barcode; }\n.#{$fa-css-prefix}-tag:before { content: $fa-var-tag; }\n.#{$fa-css-prefix}-tags:before { content: $fa-var-tags; }\n.#{$fa-css-prefix}-book:before { content: $fa-var-book; }\n.#{$fa-css-prefix}-bookmark:before { content: $fa-var-bookmark; }\n.#{$fa-css-prefix}-print:before { content: $fa-var-print; }\n.#{$fa-css-prefix}-camera:before { content: $fa-var-camera; }\n.#{$fa-css-prefix}-font:before { content: $fa-var-font; }\n.#{$fa-css-prefix}-bold:before { content: $fa-var-bold; }\n.#{$fa-css-prefix}-italic:before { content: $fa-var-italic; }\n.#{$fa-css-prefix}-text-height:before { content: $fa-var-text-height; }\n.#{$fa-css-prefix}-text-width:before { content: $fa-var-text-width; }\n.#{$fa-css-prefix}-align-left:before { content: $fa-var-align-left; }\n.#{$fa-css-prefix}-align-center:before { content: $fa-var-align-center; }\n.#{$fa-css-prefix}-align-right:before { content: $fa-var-align-right; }\n.#{$fa-css-prefix}-align-justify:before { content: $fa-var-align-justify; }\n.#{$fa-css-prefix}-list:before { content: $fa-var-list; }\n.#{$fa-css-prefix}-dedent:before,\n.#{$fa-css-prefix}-outdent:before { content: $fa-var-outdent; }\n.#{$fa-css-prefix}-indent:before { content: $fa-var-indent; }\n.#{$fa-css-prefix}-video-camera:before { content: $fa-var-video-camera; }\n.#{$fa-css-prefix}-photo:before,\n.#{$fa-css-prefix}-image:before,\n.#{$fa-css-prefix}-picture-o:before { content: $fa-var-picture-o; }\n.#{$fa-css-prefix}-pencil:before { content: $fa-var-pencil; }\n.#{$fa-css-prefix}-map-marker:before { content: $fa-var-map-marker; }\n.#{$fa-css-prefix}-adjust:before { content: $fa-var-adjust; }\n.#{$fa-css-prefix}-tint:before { content: $fa-var-tint; }\n.#{$fa-css-prefix}-edit:before,\n.#{$fa-css-prefix}-pencil-square-o:before { content: $fa-var-pencil-square-o; }\n.#{$fa-css-prefix}-share-square-o:before { content: $fa-var-share-square-o; }\n.#{$fa-css-prefix}-check-square-o:before { content: $fa-var-check-square-o; }\n.#{$fa-css-prefix}-arrows:before { content: $fa-var-arrows; }\n.#{$fa-css-prefix}-step-backward:before { content: $fa-var-step-backward; }\n.#{$fa-css-prefix}-fast-backward:before { content: $fa-var-fast-backward; }\n.#{$fa-css-prefix}-backward:before { content: $fa-var-backward; }\n.#{$fa-css-prefix}-play:before { content: $fa-var-play; }\n.#{$fa-css-prefix}-pause:before { content: $fa-var-pause; }\n.#{$fa-css-prefix}-stop:before { content: $fa-var-stop; }\n.#{$fa-css-prefix}-forward:before { content: $fa-var-forward; }\n.#{$fa-css-prefix}-fast-forward:before { content: $fa-var-fast-forward; }\n.#{$fa-css-prefix}-step-forward:before { content: $fa-var-step-forward; }\n.#{$fa-css-prefix}-eject:before { content: $fa-var-eject; }\n.#{$fa-css-prefix}-chevron-left:before { content: $fa-var-chevron-left; }\n.#{$fa-css-prefix}-chevron-right:before { content: $fa-var-chevron-right; }\n.#{$fa-css-prefix}-plus-circle:before { content: $fa-var-plus-circle; }\n.#{$fa-css-prefix}-minus-circle:before { content: $fa-var-minus-circle; }\n.#{$fa-css-prefix}-times-circle:before { content: $fa-var-times-circle; }\n.#{$fa-css-prefix}-check-circle:before { content: $fa-var-check-circle; }\n.#{$fa-css-prefix}-question-circle:before { content: $fa-var-question-circle; }\n.#{$fa-css-prefix}-info-circle:before { content: $fa-var-info-circle; }\n.#{$fa-css-prefix}-crosshairs:before { content: $fa-var-crosshairs; }\n.#{$fa-css-prefix}-times-circle-o:before { content: $fa-var-times-circle-o; }\n.#{$fa-css-prefix}-check-circle-o:before { content: $fa-var-check-circle-o; }\n.#{$fa-css-prefix}-ban:before { content: $fa-var-ban; }\n.#{$fa-css-prefix}-arrow-left:before { content: $fa-var-arrow-left; }\n.#{$fa-css-prefix}-arrow-right:before { content: $fa-var-arrow-right; }\n.#{$fa-css-prefix}-arrow-up:before { content: $fa-var-arrow-up; }\n.#{$fa-css-prefix}-arrow-down:before { content: $fa-var-arrow-down; }\n.#{$fa-css-prefix}-mail-forward:before,\n.#{$fa-css-prefix}-share:before { content: $fa-var-share; }\n.#{$fa-css-prefix}-expand:before { content: $fa-var-expand; }\n.#{$fa-css-prefix}-compress:before { content: $fa-var-compress; }\n.#{$fa-css-prefix}-plus:before { content: $fa-var-plus; }\n.#{$fa-css-prefix}-minus:before { content: $fa-var-minus; }\n.#{$fa-css-prefix}-asterisk:before { content: $fa-var-asterisk; }\n.#{$fa-css-prefix}-exclamation-circle:before { content: $fa-var-exclamation-circle; }\n.#{$fa-css-prefix}-gift:before { content: $fa-var-gift; }\n.#{$fa-css-prefix}-leaf:before { content: $fa-var-leaf; }\n.#{$fa-css-prefix}-fire:before { content: $fa-var-fire; }\n.#{$fa-css-prefix}-eye:before { content: $fa-var-eye; }\n.#{$fa-css-prefix}-eye-slash:before { content: $fa-var-eye-slash; }\n.#{$fa-css-prefix}-warning:before,\n.#{$fa-css-prefix}-exclamation-triangle:before { content: $fa-var-exclamation-triangle; }\n.#{$fa-css-prefix}-plane:before { content: $fa-var-plane; }\n.#{$fa-css-prefix}-calendar:before { content: $fa-var-calendar; }\n.#{$fa-css-prefix}-random:before { content: $fa-var-random; }\n.#{$fa-css-prefix}-comment:before { content: $fa-var-comment; }\n.#{$fa-css-prefix}-magnet:before { content: $fa-var-magnet; }\n.#{$fa-css-prefix}-chevron-up:before { content: $fa-var-chevron-up; }\n.#{$fa-css-prefix}-chevron-down:before { content: $fa-var-chevron-down; }\n.#{$fa-css-prefix}-retweet:before { content: $fa-var-retweet; }\n.#{$fa-css-prefix}-shopping-cart:before { content: $fa-var-shopping-cart; }\n.#{$fa-css-prefix}-folder:before { content: $fa-var-folder; }\n.#{$fa-css-prefix}-folder-open:before { content: $fa-var-folder-open; }\n.#{$fa-css-prefix}-arrows-v:before { content: $fa-var-arrows-v; }\n.#{$fa-css-prefix}-arrows-h:before { content: $fa-var-arrows-h; }\n.#{$fa-css-prefix}-bar-chart-o:before,\n.#{$fa-css-prefix}-bar-chart:before { content: $fa-var-bar-chart; }\n.#{$fa-css-prefix}-twitter-square:before { content: $fa-var-twitter-square; }\n.#{$fa-css-prefix}-facebook-square:before { content: $fa-var-facebook-square; }\n.#{$fa-css-prefix}-camera-retro:before { content: $fa-var-camera-retro; }\n.#{$fa-css-prefix}-key:before { content: $fa-var-key; }\n.#{$fa-css-prefix}-gears:before,\n.#{$fa-css-prefix}-cogs:before { content: $fa-var-cogs; }\n.#{$fa-css-prefix}-comments:before { content: $fa-var-comments; }\n.#{$fa-css-prefix}-thumbs-o-up:before { content: $fa-var-thumbs-o-up; }\n.#{$fa-css-prefix}-thumbs-o-down:before { content: $fa-var-thumbs-o-down; }\n.#{$fa-css-prefix}-star-half:before { content: $fa-var-star-half; }\n.#{$fa-css-prefix}-heart-o:before { content: $fa-var-heart-o; }\n.#{$fa-css-prefix}-sign-out:before { content: $fa-var-sign-out; }\n.#{$fa-css-prefix}-linkedin-square:before { content: $fa-var-linkedin-square; }\n.#{$fa-css-prefix}-thumb-tack:before { content: $fa-var-thumb-tack; }\n.#{$fa-css-prefix}-external-link:before { content: $fa-var-external-link; }\n.#{$fa-css-prefix}-sign-in:before { content: $fa-var-sign-in; }\n.#{$fa-css-prefix}-trophy:before { content: $fa-var-trophy; }\n.#{$fa-css-prefix}-github-square:before { content: $fa-var-github-square; }\n.#{$fa-css-prefix}-upload:before { content: $fa-var-upload; }\n.#{$fa-css-prefix}-lemon-o:before { content: $fa-var-lemon-o; }\n.#{$fa-css-prefix}-phone:before { content: $fa-var-phone; }\n.#{$fa-css-prefix}-square-o:before { content: $fa-var-square-o; }\n.#{$fa-css-prefix}-bookmark-o:before { content: $fa-var-bookmark-o; }\n.#{$fa-css-prefix}-phone-square:before { content: $fa-var-phone-square; }\n.#{$fa-css-prefix}-twitter:before { content: $fa-var-twitter; }\n.#{$fa-css-prefix}-facebook-f:before,\n.#{$fa-css-prefix}-facebook:before { content: $fa-var-facebook; }\n.#{$fa-css-prefix}-github:before { content: $fa-var-github; }\n.#{$fa-css-prefix}-unlock:before { content: $fa-var-unlock; }\n.#{$fa-css-prefix}-credit-card:before { content: $fa-var-credit-card; }\n.#{$fa-css-prefix}-feed:before,\n.#{$fa-css-prefix}-rss:before { content: $fa-var-rss; }\n.#{$fa-css-prefix}-hdd-o:before { content: $fa-var-hdd-o; }\n.#{$fa-css-prefix}-bullhorn:before { content: $fa-var-bullhorn; }\n.#{$fa-css-prefix}-bell:before { content: $fa-var-bell; }\n.#{$fa-css-prefix}-certificate:before { content: $fa-var-certificate; }\n.#{$fa-css-prefix}-hand-o-right:before { content: $fa-var-hand-o-right; }\n.#{$fa-css-prefix}-hand-o-left:before { content: $fa-var-hand-o-left; }\n.#{$fa-css-prefix}-hand-o-up:before { content: $fa-var-hand-o-up; }\n.#{$fa-css-prefix}-hand-o-down:before { content: $fa-var-hand-o-down; }\n.#{$fa-css-prefix}-arrow-circle-left:before { content: $fa-var-arrow-circle-left; }\n.#{$fa-css-prefix}-arrow-circle-right:before { content: $fa-var-arrow-circle-right; }\n.#{$fa-css-prefix}-arrow-circle-up:before { content: $fa-var-arrow-circle-up; }\n.#{$fa-css-prefix}-arrow-circle-down:before { content: $fa-var-arrow-circle-down; }\n.#{$fa-css-prefix}-globe:before { content: $fa-var-globe; }\n.#{$fa-css-prefix}-wrench:before { content: $fa-var-wrench; }\n.#{$fa-css-prefix}-tasks:before { content: $fa-var-tasks; }\n.#{$fa-css-prefix}-filter:before { content: $fa-var-filter; }\n.#{$fa-css-prefix}-briefcase:before { content: $fa-var-briefcase; }\n.#{$fa-css-prefix}-arrows-alt:before { content: $fa-var-arrows-alt; }\n.#{$fa-css-prefix}-group:before,\n.#{$fa-css-prefix}-users:before { content: $fa-var-users; }\n.#{$fa-css-prefix}-chain:before,\n.#{$fa-css-prefix}-link:before { content: $fa-var-link; }\n.#{$fa-css-prefix}-cloud:before { content: $fa-var-cloud; }\n.#{$fa-css-prefix}-flask:before { content: $fa-var-flask; }\n.#{$fa-css-prefix}-cut:before,\n.#{$fa-css-prefix}-scissors:before { content: $fa-var-scissors; }\n.#{$fa-css-prefix}-copy:before,\n.#{$fa-css-prefix}-files-o:before { content: $fa-var-files-o; }\n.#{$fa-css-prefix}-paperclip:before { content: $fa-var-paperclip; }\n.#{$fa-css-prefix}-save:before,\n.#{$fa-css-prefix}-floppy-o:before { content: $fa-var-floppy-o; }\n.#{$fa-css-prefix}-square:before { content: $fa-var-square; }\n.#{$fa-css-prefix}-navicon:before,\n.#{$fa-css-prefix}-reorder:before,\n.#{$fa-css-prefix}-bars:before { content: $fa-var-bars; }\n.#{$fa-css-prefix}-list-ul:before { content: $fa-var-list-ul; }\n.#{$fa-css-prefix}-list-ol:before { content: $fa-var-list-ol; }\n.#{$fa-css-prefix}-strikethrough:before { content: $fa-var-strikethrough; }\n.#{$fa-css-prefix}-underline:before { content: $fa-var-underline; }\n.#{$fa-css-prefix}-table:before { content: $fa-var-table; }\n.#{$fa-css-prefix}-magic:before { content: $fa-var-magic; }\n.#{$fa-css-prefix}-truck:before { content: $fa-var-truck; }\n.#{$fa-css-prefix}-pinterest:before { content: $fa-var-pinterest; }\n.#{$fa-css-prefix}-pinterest-square:before { content: $fa-var-pinterest-square; }\n.#{$fa-css-prefix}-google-plus-square:before { content: $fa-var-google-plus-square; }\n.#{$fa-css-prefix}-google-plus:before { content: $fa-var-google-plus; }\n.#{$fa-css-prefix}-money:before { content: $fa-var-money; }\n.#{$fa-css-prefix}-caret-down:before { content: $fa-var-caret-down; }\n.#{$fa-css-prefix}-caret-up:before { content: $fa-var-caret-up; }\n.#{$fa-css-prefix}-caret-left:before { content: $fa-var-caret-left; }\n.#{$fa-css-prefix}-caret-right:before { content: $fa-var-caret-right; }\n.#{$fa-css-prefix}-columns:before { content: $fa-var-columns; }\n.#{$fa-css-prefix}-unsorted:before,\n.#{$fa-css-prefix}-sort:before { content: $fa-var-sort; }\n.#{$fa-css-prefix}-sort-down:before,\n.#{$fa-css-prefix}-sort-desc:before { content: $fa-var-sort-desc; }\n.#{$fa-css-prefix}-sort-up:before,\n.#{$fa-css-prefix}-sort-asc:before { content: $fa-var-sort-asc; }\n.#{$fa-css-prefix}-envelope:before { content: $fa-var-envelope; }\n.#{$fa-css-prefix}-linkedin:before { content: $fa-var-linkedin; }\n.#{$fa-css-prefix}-rotate-left:before,\n.#{$fa-css-prefix}-undo:before { content: $fa-var-undo; }\n.#{$fa-css-prefix}-legal:before,\n.#{$fa-css-prefix}-gavel:before { content: $fa-var-gavel; }\n.#{$fa-css-prefix}-dashboard:before,\n.#{$fa-css-prefix}-tachometer:before { content: $fa-var-tachometer; }\n.#{$fa-css-prefix}-comment-o:before { content: $fa-var-comment-o; }\n.#{$fa-css-prefix}-comments-o:before { content: $fa-var-comments-o; }\n.#{$fa-css-prefix}-flash:before,\n.#{$fa-css-prefix}-bolt:before { content: $fa-var-bolt; }\n.#{$fa-css-prefix}-sitemap:before { content: $fa-var-sitemap; }\n.#{$fa-css-prefix}-umbrella:before { content: $fa-var-umbrella; }\n.#{$fa-css-prefix}-paste:before,\n.#{$fa-css-prefix}-clipboard:before { content: $fa-var-clipboard; }\n.#{$fa-css-prefix}-lightbulb-o:before { content: $fa-var-lightbulb-o; }\n.#{$fa-css-prefix}-exchange:before { content: $fa-var-exchange; }\n.#{$fa-css-prefix}-cloud-download:before { content: $fa-var-cloud-download; }\n.#{$fa-css-prefix}-cloud-upload:before { content: $fa-var-cloud-upload; }\n.#{$fa-css-prefix}-user-md:before { content: $fa-var-user-md; }\n.#{$fa-css-prefix}-stethoscope:before { content: $fa-var-stethoscope; }\n.#{$fa-css-prefix}-suitcase:before { content: $fa-var-suitcase; }\n.#{$fa-css-prefix}-bell-o:before { content: $fa-var-bell-o; }\n.#{$fa-css-prefix}-coffee:before { content: $fa-var-coffee; }\n.#{$fa-css-prefix}-cutlery:before { content: $fa-var-cutlery; }\n.#{$fa-css-prefix}-file-text-o:before { content: $fa-var-file-text-o; }\n.#{$fa-css-prefix}-building-o:before { content: $fa-var-building-o; }\n.#{$fa-css-prefix}-hospital-o:before { content: $fa-var-hospital-o; }\n.#{$fa-css-prefix}-ambulance:before { content: $fa-var-ambulance; }\n.#{$fa-css-prefix}-medkit:before { content: $fa-var-medkit; }\n.#{$fa-css-prefix}-fighter-jet:before { content: $fa-var-fighter-jet; }\n.#{$fa-css-prefix}-beer:before { content: $fa-var-beer; }\n.#{$fa-css-prefix}-h-square:before { content: $fa-var-h-square; }\n.#{$fa-css-prefix}-plus-square:before { content: $fa-var-plus-square; }\n.#{$fa-css-prefix}-angle-double-left:before { content: $fa-var-angle-double-left; }\n.#{$fa-css-prefix}-angle-double-right:before { content: $fa-var-angle-double-right; }\n.#{$fa-css-prefix}-angle-double-up:before { content: $fa-var-angle-double-up; }\n.#{$fa-css-prefix}-angle-double-down:before { content: $fa-var-angle-double-down; }\n.#{$fa-css-prefix}-angle-left:before { content: $fa-var-angle-left; }\n.#{$fa-css-prefix}-angle-right:before { content: $fa-var-angle-right; }\n.#{$fa-css-prefix}-angle-up:before { content: $fa-var-angle-up; }\n.#{$fa-css-prefix}-angle-down:before { content: $fa-var-angle-down; }\n.#{$fa-css-prefix}-desktop:before { content: $fa-var-desktop; }\n.#{$fa-css-prefix}-laptop:before { content: $fa-var-laptop; }\n.#{$fa-css-prefix}-tablet:before { content: $fa-var-tablet; }\n.#{$fa-css-prefix}-mobile-phone:before,\n.#{$fa-css-prefix}-mobile:before { content: $fa-var-mobile; }\n.#{$fa-css-prefix}-circle-o:before { content: $fa-var-circle-o; }\n.#{$fa-css-prefix}-quote-left:before { content: $fa-var-quote-left; }\n.#{$fa-css-prefix}-quote-right:before { content: $fa-var-quote-right; }\n.#{$fa-css-prefix}-spinner:before { content: $fa-var-spinner; }\n.#{$fa-css-prefix}-circle:before { content: $fa-var-circle; }\n.#{$fa-css-prefix}-mail-reply:before,\n.#{$fa-css-prefix}-reply:before { content: $fa-var-reply; }\n.#{$fa-css-prefix}-github-alt:before { content: $fa-var-github-alt; }\n.#{$fa-css-prefix}-folder-o:before { content: $fa-var-folder-o; }\n.#{$fa-css-prefix}-folder-open-o:before { content: $fa-var-folder-open-o; }\n.#{$fa-css-prefix}-smile-o:before { content: $fa-var-smile-o; }\n.#{$fa-css-prefix}-frown-o:before { content: $fa-var-frown-o; }\n.#{$fa-css-prefix}-meh-o:before { content: $fa-var-meh-o; }\n.#{$fa-css-prefix}-gamepad:before { content: $fa-var-gamepad; }\n.#{$fa-css-prefix}-keyboard-o:before { content: $fa-var-keyboard-o; }\n.#{$fa-css-prefix}-flag-o:before { content: $fa-var-flag-o; }\n.#{$fa-css-prefix}-flag-checkered:before { content: $fa-var-flag-checkered; }\n.#{$fa-css-prefix}-terminal:before { content: $fa-var-terminal; }\n.#{$fa-css-prefix}-code:before { content: $fa-var-code; }\n.#{$fa-css-prefix}-mail-reply-all:before,\n.#{$fa-css-prefix}-reply-all:before { content: $fa-var-reply-all; }\n.#{$fa-css-prefix}-star-half-empty:before,\n.#{$fa-css-prefix}-star-half-full:before,\n.#{$fa-css-prefix}-star-half-o:before { content: $fa-var-star-half-o; }\n.#{$fa-css-prefix}-location-arrow:before { content: $fa-var-location-arrow; }\n.#{$fa-css-prefix}-crop:before { content: $fa-var-crop; }\n.#{$fa-css-prefix}-code-fork:before { content: $fa-var-code-fork; }\n.#{$fa-css-prefix}-unlink:before,\n.#{$fa-css-prefix}-chain-broken:before { content: $fa-var-chain-broken; }\n.#{$fa-css-prefix}-question:before { content: $fa-var-question; }\n.#{$fa-css-prefix}-info:before { content: $fa-var-info; }\n.#{$fa-css-prefix}-exclamation:before { content: $fa-var-exclamation; }\n.#{$fa-css-prefix}-superscript:before { content: $fa-var-superscript; }\n.#{$fa-css-prefix}-subscript:before { content: $fa-var-subscript; }\n.#{$fa-css-prefix}-eraser:before { content: $fa-var-eraser; }\n.#{$fa-css-prefix}-puzzle-piece:before { content: $fa-var-puzzle-piece; }\n.#{$fa-css-prefix}-microphone:before { content: $fa-var-microphone; }\n.#{$fa-css-prefix}-microphone-slash:before { content: $fa-var-microphone-slash; }\n.#{$fa-css-prefix}-shield:before { content: $fa-var-shield; }\n.#{$fa-css-prefix}-calendar-o:before { content: $fa-var-calendar-o; }\n.#{$fa-css-prefix}-fire-extinguisher:before { content: $fa-var-fire-extinguisher; }\n.#{$fa-css-prefix}-rocket:before { content: $fa-var-rocket; }\n.#{$fa-css-prefix}-maxcdn:before { content: $fa-var-maxcdn; }\n.#{$fa-css-prefix}-chevron-circle-left:before { content: $fa-var-chevron-circle-left; }\n.#{$fa-css-prefix}-chevron-circle-right:before { content: $fa-var-chevron-circle-right; }\n.#{$fa-css-prefix}-chevron-circle-up:before { content: $fa-var-chevron-circle-up; }\n.#{$fa-css-prefix}-chevron-circle-down:before { content: $fa-var-chevron-circle-down; }\n.#{$fa-css-prefix}-html5:before { content: $fa-var-html5; }\n.#{$fa-css-prefix}-css3:before { content: $fa-var-css3; }\n.#{$fa-css-prefix}-anchor:before { content: $fa-var-anchor; }\n.#{$fa-css-prefix}-unlock-alt:before { content: $fa-var-unlock-alt; }\n.#{$fa-css-prefix}-bullseye:before { content: $fa-var-bullseye; }\n.#{$fa-css-prefix}-ellipsis-h:before { content: $fa-var-ellipsis-h; }\n.#{$fa-css-prefix}-ellipsis-v:before { content: $fa-var-ellipsis-v; }\n.#{$fa-css-prefix}-rss-square:before { content: $fa-var-rss-square; }\n.#{$fa-css-prefix}-play-circle:before { content: $fa-var-play-circle; }\n.#{$fa-css-prefix}-ticket:before { content: $fa-var-ticket; }\n.#{$fa-css-prefix}-minus-square:before { content: $fa-var-minus-square; }\n.#{$fa-css-prefix}-minus-square-o:before { content: $fa-var-minus-square-o; }\n.#{$fa-css-prefix}-level-up:before { content: $fa-var-level-up; }\n.#{$fa-css-prefix}-level-down:before { content: $fa-var-level-down; }\n.#{$fa-css-prefix}-check-square:before { content: $fa-var-check-square; }\n.#{$fa-css-prefix}-pencil-square:before { content: $fa-var-pencil-square; }\n.#{$fa-css-prefix}-external-link-square:before { content: $fa-var-external-link-square; }\n.#{$fa-css-prefix}-share-square:before { content: $fa-var-share-square; }\n.#{$fa-css-prefix}-compass:before { content: $fa-var-compass; }\n.#{$fa-css-prefix}-toggle-down:before,\n.#{$fa-css-prefix}-caret-square-o-down:before { content: $fa-var-caret-square-o-down; }\n.#{$fa-css-prefix}-toggle-up:before,\n.#{$fa-css-prefix}-caret-square-o-up:before { content: $fa-var-caret-square-o-up; }\n.#{$fa-css-prefix}-toggle-right:before,\n.#{$fa-css-prefix}-caret-square-o-right:before { content: $fa-var-caret-square-o-right; }\n.#{$fa-css-prefix}-euro:before,\n.#{$fa-css-prefix}-eur:before { content: $fa-var-eur; }\n.#{$fa-css-prefix}-gbp:before { content: $fa-var-gbp; }\n.#{$fa-css-prefix}-dollar:before,\n.#{$fa-css-prefix}-usd:before { content: $fa-var-usd; }\n.#{$fa-css-prefix}-rupee:before,\n.#{$fa-css-prefix}-inr:before { content: $fa-var-inr; }\n.#{$fa-css-prefix}-cny:before,\n.#{$fa-css-prefix}-rmb:before,\n.#{$fa-css-prefix}-yen:before,\n.#{$fa-css-prefix}-jpy:before { content: $fa-var-jpy; }\n.#{$fa-css-prefix}-ruble:before,\n.#{$fa-css-prefix}-rouble:before,\n.#{$fa-css-prefix}-rub:before { content: $fa-var-rub; }\n.#{$fa-css-prefix}-won:before,\n.#{$fa-css-prefix}-krw:before { content: $fa-var-krw; }\n.#{$fa-css-prefix}-bitcoin:before,\n.#{$fa-css-prefix}-btc:before { content: $fa-var-btc; }\n.#{$fa-css-prefix}-file:before { content: $fa-var-file; }\n.#{$fa-css-prefix}-file-text:before { content: $fa-var-file-text; }\n.#{$fa-css-prefix}-sort-alpha-asc:before { content: $fa-var-sort-alpha-asc; }\n.#{$fa-css-prefix}-sort-alpha-desc:before { content: $fa-var-sort-alpha-desc; }\n.#{$fa-css-prefix}-sort-amount-asc:before { content: $fa-var-sort-amount-asc; }\n.#{$fa-css-prefix}-sort-amount-desc:before { content: $fa-var-sort-amount-desc; }\n.#{$fa-css-prefix}-sort-numeric-asc:before { content: $fa-var-sort-numeric-asc; }\n.#{$fa-css-prefix}-sort-numeric-desc:before { content: $fa-var-sort-numeric-desc; }\n.#{$fa-css-prefix}-thumbs-up:before { content: $fa-var-thumbs-up; }\n.#{$fa-css-prefix}-thumbs-down:before { content: $fa-var-thumbs-down; }\n.#{$fa-css-prefix}-youtube-square:before { content: $fa-var-youtube-square; }\n.#{$fa-css-prefix}-youtube:before { content: $fa-var-youtube; }\n.#{$fa-css-prefix}-xing:before { content: $fa-var-xing; }\n.#{$fa-css-prefix}-xing-square:before { content: $fa-var-xing-square; }\n.#{$fa-css-prefix}-youtube-play:before { content: $fa-var-youtube-play; }\n.#{$fa-css-prefix}-dropbox:before { content: $fa-var-dropbox; }\n.#{$fa-css-prefix}-stack-overflow:before { content: $fa-var-stack-overflow; }\n.#{$fa-css-prefix}-instagram:before { content: $fa-var-instagram; }\n.#{$fa-css-prefix}-flickr:before { content: $fa-var-flickr; }\n.#{$fa-css-prefix}-adn:before { content: $fa-var-adn; }\n.#{$fa-css-prefix}-bitbucket:before { content: $fa-var-bitbucket; }\n.#{$fa-css-prefix}-bitbucket-square:before { content: $fa-var-bitbucket-square; }\n.#{$fa-css-prefix}-tumblr:before { content: $fa-var-tumblr; }\n.#{$fa-css-prefix}-tumblr-square:before { content: $fa-var-tumblr-square; }\n.#{$fa-css-prefix}-long-arrow-down:before { content: $fa-var-long-arrow-down; }\n.#{$fa-css-prefix}-long-arrow-up:before { content: $fa-var-long-arrow-up; }\n.#{$fa-css-prefix}-long-arrow-left:before { content: $fa-var-long-arrow-left; }\n.#{$fa-css-prefix}-long-arrow-right:before { content: $fa-var-long-arrow-right; }\n.#{$fa-css-prefix}-apple:before { content: $fa-var-apple; }\n.#{$fa-css-prefix}-windows:before { content: $fa-var-windows; }\n.#{$fa-css-prefix}-android:before { content: $fa-var-android; }\n.#{$fa-css-prefix}-linux:before { content: $fa-var-linux; }\n.#{$fa-css-prefix}-dribbble:before { content: $fa-var-dribbble; }\n.#{$fa-css-prefix}-skype:before { content: $fa-var-skype; }\n.#{$fa-css-prefix}-foursquare:before { content: $fa-var-foursquare; }\n.#{$fa-css-prefix}-trello:before { content: $fa-var-trello; }\n.#{$fa-css-prefix}-female:before { content: $fa-var-female; }\n.#{$fa-css-prefix}-male:before { content: $fa-var-male; }\n.#{$fa-css-prefix}-gittip:before,\n.#{$fa-css-prefix}-gratipay:before { content: $fa-var-gratipay; }\n.#{$fa-css-prefix}-sun-o:before { content: $fa-var-sun-o; }\n.#{$fa-css-prefix}-moon-o:before { content: $fa-var-moon-o; }\n.#{$fa-css-prefix}-archive:before { content: $fa-var-archive; }\n.#{$fa-css-prefix}-bug:before { content: $fa-var-bug; }\n.#{$fa-css-prefix}-vk:before { content: $fa-var-vk; }\n.#{$fa-css-prefix}-weibo:before { content: $fa-var-weibo; }\n.#{$fa-css-prefix}-renren:before { content: $fa-var-renren; }\n.#{$fa-css-prefix}-pagelines:before { content: $fa-var-pagelines; }\n.#{$fa-css-prefix}-stack-exchange:before { content: $fa-var-stack-exchange; }\n.#{$fa-css-prefix}-arrow-circle-o-right:before { content: $fa-var-arrow-circle-o-right; }\n.#{$fa-css-prefix}-arrow-circle-o-left:before { content: $fa-var-arrow-circle-o-left; }\n.#{$fa-css-prefix}-toggle-left:before,\n.#{$fa-css-prefix}-caret-square-o-left:before { content: $fa-var-caret-square-o-left; }\n.#{$fa-css-prefix}-dot-circle-o:before { content: $fa-var-dot-circle-o; }\n.#{$fa-css-prefix}-wheelchair:before { content: $fa-var-wheelchair; }\n.#{$fa-css-prefix}-vimeo-square:before { content: $fa-var-vimeo-square; }\n.#{$fa-css-prefix}-turkish-lira:before,\n.#{$fa-css-prefix}-try:before { content: $fa-var-try; }\n.#{$fa-css-prefix}-plus-square-o:before { content: $fa-var-plus-square-o; }\n.#{$fa-css-prefix}-space-shuttle:before { content: $fa-var-space-shuttle; }\n.#{$fa-css-prefix}-slack:before { content: $fa-var-slack; }\n.#{$fa-css-prefix}-envelope-square:before { content: $fa-var-envelope-square; }\n.#{$fa-css-prefix}-wordpress:before { content: $fa-var-wordpress; }\n.#{$fa-css-prefix}-openid:before { content: $fa-var-openid; }\n.#{$fa-css-prefix}-institution:before,\n.#{$fa-css-prefix}-bank:before,\n.#{$fa-css-prefix}-university:before { content: $fa-var-university; }\n.#{$fa-css-prefix}-mortar-board:before,\n.#{$fa-css-prefix}-graduation-cap:before { content: $fa-var-graduation-cap; }\n.#{$fa-css-prefix}-yahoo:before { content: $fa-var-yahoo; }\n.#{$fa-css-prefix}-google:before { content: $fa-var-google; }\n.#{$fa-css-prefix}-reddit:before { content: $fa-var-reddit; }\n.#{$fa-css-prefix}-reddit-square:before { content: $fa-var-reddit-square; }\n.#{$fa-css-prefix}-stumbleupon-circle:before { content: $fa-var-stumbleupon-circle; }\n.#{$fa-css-prefix}-stumbleupon:before { content: $fa-var-stumbleupon; }\n.#{$fa-css-prefix}-delicious:before { content: $fa-var-delicious; }\n.#{$fa-css-prefix}-digg:before { content: $fa-var-digg; }\n.#{$fa-css-prefix}-pied-piper-pp:before { content: $fa-var-pied-piper-pp; }\n.#{$fa-css-prefix}-pied-piper-alt:before { content: $fa-var-pied-piper-alt; }\n.#{$fa-css-prefix}-drupal:before { content: $fa-var-drupal; }\n.#{$fa-css-prefix}-joomla:before { content: $fa-var-joomla; }\n.#{$fa-css-prefix}-language:before { content: $fa-var-language; }\n.#{$fa-css-prefix}-fax:before { content: $fa-var-fax; }\n.#{$fa-css-prefix}-building:before { content: $fa-var-building; }\n.#{$fa-css-prefix}-child:before { content: $fa-var-child; }\n.#{$fa-css-prefix}-paw:before { content: $fa-var-paw; }\n.#{$fa-css-prefix}-spoon:before { content: $fa-var-spoon; }\n.#{$fa-css-prefix}-cube:before { content: $fa-var-cube; }\n.#{$fa-css-prefix}-cubes:before { content: $fa-var-cubes; }\n.#{$fa-css-prefix}-behance:before { content: $fa-var-behance; }\n.#{$fa-css-prefix}-behance-square:before { content: $fa-var-behance-square; }\n.#{$fa-css-prefix}-steam:before { content: $fa-var-steam; }\n.#{$fa-css-prefix}-steam-square:before { content: $fa-var-steam-square; }\n.#{$fa-css-prefix}-recycle:before { content: $fa-var-recycle; }\n.#{$fa-css-prefix}-automobile:before,\n.#{$fa-css-prefix}-car:before { content: $fa-var-car; }\n.#{$fa-css-prefix}-cab:before,\n.#{$fa-css-prefix}-taxi:before { content: $fa-var-taxi; }\n.#{$fa-css-prefix}-tree:before { content: $fa-var-tree; }\n.#{$fa-css-prefix}-spotify:before { content: $fa-var-spotify; }\n.#{$fa-css-prefix}-deviantart:before { content: $fa-var-deviantart; }\n.#{$fa-css-prefix}-soundcloud:before { content: $fa-var-soundcloud; }\n.#{$fa-css-prefix}-database:before { content: $fa-var-database; }\n.#{$fa-css-prefix}-file-pdf-o:before { content: $fa-var-file-pdf-o; }\n.#{$fa-css-prefix}-file-word-o:before { content: $fa-var-file-word-o; }\n.#{$fa-css-prefix}-file-excel-o:before { content: $fa-var-file-excel-o; }\n.#{$fa-css-prefix}-file-powerpoint-o:before { content: $fa-var-file-powerpoint-o; }\n.#{$fa-css-prefix}-file-photo-o:before,\n.#{$fa-css-prefix}-file-picture-o:before,\n.#{$fa-css-prefix}-file-image-o:before { content: $fa-var-file-image-o; }\n.#{$fa-css-prefix}-file-zip-o:before,\n.#{$fa-css-prefix}-file-archive-o:before { content: $fa-var-file-archive-o; }\n.#{$fa-css-prefix}-file-sound-o:before,\n.#{$fa-css-prefix}-file-audio-o:before { content: $fa-var-file-audio-o; }\n.#{$fa-css-prefix}-file-movie-o:before,\n.#{$fa-css-prefix}-file-video-o:before { content: $fa-var-file-video-o; }\n.#{$fa-css-prefix}-file-code-o:before { content: $fa-var-file-code-o; }\n.#{$fa-css-prefix}-vine:before { content: $fa-var-vine; }\n.#{$fa-css-prefix}-codepen:before { content: $fa-var-codepen; }\n.#{$fa-css-prefix}-jsfiddle:before { content: $fa-var-jsfiddle; }\n.#{$fa-css-prefix}-life-bouy:before,\n.#{$fa-css-prefix}-life-buoy:before,\n.#{$fa-css-prefix}-life-saver:before,\n.#{$fa-css-prefix}-support:before,\n.#{$fa-css-prefix}-life-ring:before { content: $fa-var-life-ring; }\n.#{$fa-css-prefix}-circle-o-notch:before { content: $fa-var-circle-o-notch; }\n.#{$fa-css-prefix}-ra:before,\n.#{$fa-css-prefix}-resistance:before,\n.#{$fa-css-prefix}-rebel:before { content: $fa-var-rebel; }\n.#{$fa-css-prefix}-ge:before,\n.#{$fa-css-prefix}-empire:before { content: $fa-var-empire; }\n.#{$fa-css-prefix}-git-square:before { content: $fa-var-git-square; }\n.#{$fa-css-prefix}-git:before { content: $fa-var-git; }\n.#{$fa-css-prefix}-y-combinator-square:before,\n.#{$fa-css-prefix}-yc-square:before,\n.#{$fa-css-prefix}-hacker-news:before { content: $fa-var-hacker-news; }\n.#{$fa-css-prefix}-tencent-weibo:before { content: $fa-var-tencent-weibo; }\n.#{$fa-css-prefix}-qq:before { content: $fa-var-qq; }\n.#{$fa-css-prefix}-wechat:before,\n.#{$fa-css-prefix}-weixin:before { content: $fa-var-weixin; }\n.#{$fa-css-prefix}-send:before,\n.#{$fa-css-prefix}-paper-plane:before { content: $fa-var-paper-plane; }\n.#{$fa-css-prefix}-send-o:before,\n.#{$fa-css-prefix}-paper-plane-o:before { content: $fa-var-paper-plane-o; }\n.#{$fa-css-prefix}-history:before { content: $fa-var-history; }\n.#{$fa-css-prefix}-circle-thin:before { content: $fa-var-circle-thin; }\n.#{$fa-css-prefix}-header:before { content: $fa-var-header; }\n.#{$fa-css-prefix}-paragraph:before { content: $fa-var-paragraph; }\n.#{$fa-css-prefix}-sliders:before { content: $fa-var-sliders; }\n.#{$fa-css-prefix}-share-alt:before { content: $fa-var-share-alt; }\n.#{$fa-css-prefix}-share-alt-square:before { content: $fa-var-share-alt-square; }\n.#{$fa-css-prefix}-bomb:before { content: $fa-var-bomb; }\n.#{$fa-css-prefix}-soccer-ball-o:before,\n.#{$fa-css-prefix}-futbol-o:before { content: $fa-var-futbol-o; }\n.#{$fa-css-prefix}-tty:before { content: $fa-var-tty; }\n.#{$fa-css-prefix}-binoculars:before { content: $fa-var-binoculars; }\n.#{$fa-css-prefix}-plug:before { content: $fa-var-plug; }\n.#{$fa-css-prefix}-slideshare:before { content: $fa-var-slideshare; }\n.#{$fa-css-prefix}-twitch:before { content: $fa-var-twitch; }\n.#{$fa-css-prefix}-yelp:before { content: $fa-var-yelp; }\n.#{$fa-css-prefix}-newspaper-o:before { content: $fa-var-newspaper-o; }\n.#{$fa-css-prefix}-wifi:before { content: $fa-var-wifi; }\n.#{$fa-css-prefix}-calculator:before { content: $fa-var-calculator; }\n.#{$fa-css-prefix}-paypal:before { content: $fa-var-paypal; }\n.#{$fa-css-prefix}-google-wallet:before { content: $fa-var-google-wallet; }\n.#{$fa-css-prefix}-cc-visa:before { content: $fa-var-cc-visa; }\n.#{$fa-css-prefix}-cc-mastercard:before { content: $fa-var-cc-mastercard; }\n.#{$fa-css-prefix}-cc-discover:before { content: $fa-var-cc-discover; }\n.#{$fa-css-prefix}-cc-amex:before { content: $fa-var-cc-amex; }\n.#{$fa-css-prefix}-cc-paypal:before { content: $fa-var-cc-paypal; }\n.#{$fa-css-prefix}-cc-stripe:before { content: $fa-var-cc-stripe; }\n.#{$fa-css-prefix}-bell-slash:before { content: $fa-var-bell-slash; }\n.#{$fa-css-prefix}-bell-slash-o:before { content: $fa-var-bell-slash-o; }\n.#{$fa-css-prefix}-trash:before { content: $fa-var-trash; }\n.#{$fa-css-prefix}-copyright:before { content: $fa-var-copyright; }\n.#{$fa-css-prefix}-at:before { content: $fa-var-at; }\n.#{$fa-css-prefix}-eyedropper:before { content: $fa-var-eyedropper; }\n.#{$fa-css-prefix}-paint-brush:before { content: $fa-var-paint-brush; }\n.#{$fa-css-prefix}-birthday-cake:before { content: $fa-var-birthday-cake; }\n.#{$fa-css-prefix}-area-chart:before { content: $fa-var-area-chart; }\n.#{$fa-css-prefix}-pie-chart:before { content: $fa-var-pie-chart; }\n.#{$fa-css-prefix}-line-chart:before { content: $fa-var-line-chart; }\n.#{$fa-css-prefix}-lastfm:before { content: $fa-var-lastfm; }\n.#{$fa-css-prefix}-lastfm-square:before { content: $fa-var-lastfm-square; }\n.#{$fa-css-prefix}-toggle-off:before { content: $fa-var-toggle-off; }\n.#{$fa-css-prefix}-toggle-on:before { content: $fa-var-toggle-on; }\n.#{$fa-css-prefix}-bicycle:before { content: $fa-var-bicycle; }\n.#{$fa-css-prefix}-bus:before { content: $fa-var-bus; }\n.#{$fa-css-prefix}-ioxhost:before { content: $fa-var-ioxhost; }\n.#{$fa-css-prefix}-angellist:before { content: $fa-var-angellist; }\n.#{$fa-css-prefix}-cc:before { content: $fa-var-cc; }\n.#{$fa-css-prefix}-shekel:before,\n.#{$fa-css-prefix}-sheqel:before,\n.#{$fa-css-prefix}-ils:before { content: $fa-var-ils; }\n.#{$fa-css-prefix}-meanpath:before { content: $fa-var-meanpath; }\n.#{$fa-css-prefix}-buysellads:before { content: $fa-var-buysellads; }\n.#{$fa-css-prefix}-connectdevelop:before { content: $fa-var-connectdevelop; }\n.#{$fa-css-prefix}-dashcube:before { content: $fa-var-dashcube; }\n.#{$fa-css-prefix}-forumbee:before { content: $fa-var-forumbee; }\n.#{$fa-css-prefix}-leanpub:before { content: $fa-var-leanpub; }\n.#{$fa-css-prefix}-sellsy:before { content: $fa-var-sellsy; }\n.#{$fa-css-prefix}-shirtsinbulk:before { content: $fa-var-shirtsinbulk; }\n.#{$fa-css-prefix}-simplybuilt:before { content: $fa-var-simplybuilt; }\n.#{$fa-css-prefix}-skyatlas:before { content: $fa-var-skyatlas; }\n.#{$fa-css-prefix}-cart-plus:before { content: $fa-var-cart-plus; }\n.#{$fa-css-prefix}-cart-arrow-down:before { content: $fa-var-cart-arrow-down; }\n.#{$fa-css-prefix}-diamond:before { content: $fa-var-diamond; }\n.#{$fa-css-prefix}-ship:before { content: $fa-var-ship; }\n.#{$fa-css-prefix}-user-secret:before { content: $fa-var-user-secret; }\n.#{$fa-css-prefix}-motorcycle:before { content: $fa-var-motorcycle; }\n.#{$fa-css-prefix}-street-view:before { content: $fa-var-street-view; }\n.#{$fa-css-prefix}-heartbeat:before { content: $fa-var-heartbeat; }\n.#{$fa-css-prefix}-venus:before { content: $fa-var-venus; }\n.#{$fa-css-prefix}-mars:before { content: $fa-var-mars; }\n.#{$fa-css-prefix}-mercury:before { content: $fa-var-mercury; }\n.#{$fa-css-prefix}-intersex:before,\n.#{$fa-css-prefix}-transgender:before { content: $fa-var-transgender; }\n.#{$fa-css-prefix}-transgender-alt:before { content: $fa-var-transgender-alt; }\n.#{$fa-css-prefix}-venus-double:before { content: $fa-var-venus-double; }\n.#{$fa-css-prefix}-mars-double:before { content: $fa-var-mars-double; }\n.#{$fa-css-prefix}-venus-mars:before { content: $fa-var-venus-mars; }\n.#{$fa-css-prefix}-mars-stroke:before { content: $fa-var-mars-stroke; }\n.#{$fa-css-prefix}-mars-stroke-v:before { content: $fa-var-mars-stroke-v; }\n.#{$fa-css-prefix}-mars-stroke-h:before { content: $fa-var-mars-stroke-h; }\n.#{$fa-css-prefix}-neuter:before { content: $fa-var-neuter; }\n.#{$fa-css-prefix}-genderless:before { content: $fa-var-genderless; }\n.#{$fa-css-prefix}-facebook-official:before { content: $fa-var-facebook-official; }\n.#{$fa-css-prefix}-pinterest-p:before { content: $fa-var-pinterest-p; }\n.#{$fa-css-prefix}-whatsapp:before { content: $fa-var-whatsapp; }\n.#{$fa-css-prefix}-server:before { content: $fa-var-server; }\n.#{$fa-css-prefix}-user-plus:before { content: $fa-var-user-plus; }\n.#{$fa-css-prefix}-user-times:before { content: $fa-var-user-times; }\n.#{$fa-css-prefix}-hotel:before,\n.#{$fa-css-prefix}-bed:before { content: $fa-var-bed; }\n.#{$fa-css-prefix}-viacoin:before { content: $fa-var-viacoin; }\n.#{$fa-css-prefix}-train:before { content: $fa-var-train; }\n.#{$fa-css-prefix}-subway:before { content: $fa-var-subway; }\n.#{$fa-css-prefix}-medium:before { content: $fa-var-medium; }\n.#{$fa-css-prefix}-yc:before,\n.#{$fa-css-prefix}-y-combinator:before { content: $fa-var-y-combinator; }\n.#{$fa-css-prefix}-optin-monster:before { content: $fa-var-optin-monster; }\n.#{$fa-css-prefix}-opencart:before { content: $fa-var-opencart; }\n.#{$fa-css-prefix}-expeditedssl:before { content: $fa-var-expeditedssl; }\n.#{$fa-css-prefix}-battery-4:before,\n.#{$fa-css-prefix}-battery:before,\n.#{$fa-css-prefix}-battery-full:before { content: $fa-var-battery-full; }\n.#{$fa-css-prefix}-battery-3:before,\n.#{$fa-css-prefix}-battery-three-quarters:before { content: $fa-var-battery-three-quarters; }\n.#{$fa-css-prefix}-battery-2:before,\n.#{$fa-css-prefix}-battery-half:before { content: $fa-var-battery-half; }\n.#{$fa-css-prefix}-battery-1:before,\n.#{$fa-css-prefix}-battery-quarter:before { content: $fa-var-battery-quarter; }\n.#{$fa-css-prefix}-battery-0:before,\n.#{$fa-css-prefix}-battery-empty:before { content: $fa-var-battery-empty; }\n.#{$fa-css-prefix}-mouse-pointer:before { content: $fa-var-mouse-pointer; }\n.#{$fa-css-prefix}-i-cursor:before { content: $fa-var-i-cursor; }\n.#{$fa-css-prefix}-object-group:before { content: $fa-var-object-group; }\n.#{$fa-css-prefix}-object-ungroup:before { content: $fa-var-object-ungroup; }\n.#{$fa-css-prefix}-sticky-note:before { content: $fa-var-sticky-note; }\n.#{$fa-css-prefix}-sticky-note-o:before { content: $fa-var-sticky-note-o; }\n.#{$fa-css-prefix}-cc-jcb:before { content: $fa-var-cc-jcb; }\n.#{$fa-css-prefix}-cc-diners-club:before { content: $fa-var-cc-diners-club; }\n.#{$fa-css-prefix}-clone:before { content: $fa-var-clone; }\n.#{$fa-css-prefix}-balance-scale:before { content: $fa-var-balance-scale; }\n.#{$fa-css-prefix}-hourglass-o:before { content: $fa-var-hourglass-o; }\n.#{$fa-css-prefix}-hourglass-1:before,\n.#{$fa-css-prefix}-hourglass-start:before { content: $fa-var-hourglass-start; }\n.#{$fa-css-prefix}-hourglass-2:before,\n.#{$fa-css-prefix}-hourglass-half:before { content: $fa-var-hourglass-half; }\n.#{$fa-css-prefix}-hourglass-3:before,\n.#{$fa-css-prefix}-hourglass-end:before { content: $fa-var-hourglass-end; }\n.#{$fa-css-prefix}-hourglass:before { content: $fa-var-hourglass; }\n.#{$fa-css-prefix}-hand-grab-o:before,\n.#{$fa-css-prefix}-hand-rock-o:before { content: $fa-var-hand-rock-o; }\n.#{$fa-css-prefix}-hand-stop-o:before,\n.#{$fa-css-prefix}-hand-paper-o:before { content: $fa-var-hand-paper-o; }\n.#{$fa-css-prefix}-hand-scissors-o:before { content: $fa-var-hand-scissors-o; }\n.#{$fa-css-prefix}-hand-lizard-o:before { content: $fa-var-hand-lizard-o; }\n.#{$fa-css-prefix}-hand-spock-o:before { content: $fa-var-hand-spock-o; }\n.#{$fa-css-prefix}-hand-pointer-o:before { content: $fa-var-hand-pointer-o; }\n.#{$fa-css-prefix}-hand-peace-o:before { content: $fa-var-hand-peace-o; }\n.#{$fa-css-prefix}-trademark:before { content: $fa-var-trademark; }\n.#{$fa-css-prefix}-registered:before { content: $fa-var-registered; }\n.#{$fa-css-prefix}-creative-commons:before { content: $fa-var-creative-commons; }\n.#{$fa-css-prefix}-gg:before { content: $fa-var-gg; }\n.#{$fa-css-prefix}-gg-circle:before { content: $fa-var-gg-circle; }\n.#{$fa-css-prefix}-tripadvisor:before { content: $fa-var-tripadvisor; }\n.#{$fa-css-prefix}-odnoklassniki:before { content: $fa-var-odnoklassniki; }\n.#{$fa-css-prefix}-odnoklassniki-square:before { content: $fa-var-odnoklassniki-square; }\n.#{$fa-css-prefix}-get-pocket:before { content: $fa-var-get-pocket; }\n.#{$fa-css-prefix}-wikipedia-w:before { content: $fa-var-wikipedia-w; }\n.#{$fa-css-prefix}-safari:before { content: $fa-var-safari; }\n.#{$fa-css-prefix}-chrome:before { content: $fa-var-chrome; }\n.#{$fa-css-prefix}-firefox:before { content: $fa-var-firefox; }\n.#{$fa-css-prefix}-opera:before { content: $fa-var-opera; }\n.#{$fa-css-prefix}-internet-explorer:before { content: $fa-var-internet-explorer; }\n.#{$fa-css-prefix}-tv:before,\n.#{$fa-css-prefix}-television:before { content: $fa-var-television; }\n.#{$fa-css-prefix}-contao:before { content: $fa-var-contao; }\n.#{$fa-css-prefix}-500px:before { content: $fa-var-500px; }\n.#{$fa-css-prefix}-amazon:before { content: $fa-var-amazon; }\n.#{$fa-css-prefix}-calendar-plus-o:before { content: $fa-var-calendar-plus-o; }\n.#{$fa-css-prefix}-calendar-minus-o:before { content: $fa-var-calendar-minus-o; }\n.#{$fa-css-prefix}-calendar-times-o:before { content: $fa-var-calendar-times-o; }\n.#{$fa-css-prefix}-calendar-check-o:before { content: $fa-var-calendar-check-o; }\n.#{$fa-css-prefix}-industry:before { content: $fa-var-industry; }\n.#{$fa-css-prefix}-map-pin:before { content: $fa-var-map-pin; }\n.#{$fa-css-prefix}-map-signs:before { content: $fa-var-map-signs; }\n.#{$fa-css-prefix}-map-o:before { content: $fa-var-map-o; }\n.#{$fa-css-prefix}-map:before { content: $fa-var-map; }\n.#{$fa-css-prefix}-commenting:before { content: $fa-var-commenting; }\n.#{$fa-css-prefix}-commenting-o:before { content: $fa-var-commenting-o; }\n.#{$fa-css-prefix}-houzz:before { content: $fa-var-houzz; }\n.#{$fa-css-prefix}-vimeo:before { content: $fa-var-vimeo; }\n.#{$fa-css-prefix}-black-tie:before { content: $fa-var-black-tie; }\n.#{$fa-css-prefix}-fonticons:before { content: $fa-var-fonticons; }\n.#{$fa-css-prefix}-reddit-alien:before { content: $fa-var-reddit-alien; }\n.#{$fa-css-prefix}-edge:before { content: $fa-var-edge; }\n.#{$fa-css-prefix}-credit-card-alt:before { content: $fa-var-credit-card-alt; }\n.#{$fa-css-prefix}-codiepie:before { content: $fa-var-codiepie; }\n.#{$fa-css-prefix}-modx:before { content: $fa-var-modx; }\n.#{$fa-css-prefix}-fort-awesome:before { content: $fa-var-fort-awesome; }\n.#{$fa-css-prefix}-usb:before { content: $fa-var-usb; }\n.#{$fa-css-prefix}-product-hunt:before { content: $fa-var-product-hunt; }\n.#{$fa-css-prefix}-mixcloud:before { content: $fa-var-mixcloud; }\n.#{$fa-css-prefix}-scribd:before { content: $fa-var-scribd; }\n.#{$fa-css-prefix}-pause-circle:before { content: $fa-var-pause-circle; }\n.#{$fa-css-prefix}-pause-circle-o:before { content: $fa-var-pause-circle-o; }\n.#{$fa-css-prefix}-stop-circle:before { content: $fa-var-stop-circle; }\n.#{$fa-css-prefix}-stop-circle-o:before { content: $fa-var-stop-circle-o; }\n.#{$fa-css-prefix}-shopping-bag:before { content: $fa-var-shopping-bag; }\n.#{$fa-css-prefix}-shopping-basket:before { content: $fa-var-shopping-basket; }\n.#{$fa-css-prefix}-hashtag:before { content: $fa-var-hashtag; }\n.#{$fa-css-prefix}-bluetooth:before { content: $fa-var-bluetooth; }\n.#{$fa-css-prefix}-bluetooth-b:before { content: $fa-var-bluetooth-b; }\n.#{$fa-css-prefix}-percent:before { content: $fa-var-percent; }\n.#{$fa-css-prefix}-gitlab:before { content: $fa-var-gitlab; }\n.#{$fa-css-prefix}-wpbeginner:before { content: $fa-var-wpbeginner; }\n.#{$fa-css-prefix}-wpforms:before { content: $fa-var-wpforms; }\n.#{$fa-css-prefix}-envira:before { content: $fa-var-envira; }\n.#{$fa-css-prefix}-universal-access:before { content: $fa-var-universal-access; }\n.#{$fa-css-prefix}-wheelchair-alt:before { content: $fa-var-wheelchair-alt; }\n.#{$fa-css-prefix}-question-circle-o:before { content: $fa-var-question-circle-o; }\n.#{$fa-css-prefix}-blind:before { content: $fa-var-blind; }\n.#{$fa-css-prefix}-audio-description:before { content: $fa-var-audio-description; }\n.#{$fa-css-prefix}-volume-control-phone:before { content: $fa-var-volume-control-phone; }\n.#{$fa-css-prefix}-braille:before { content: $fa-var-braille; }\n.#{$fa-css-prefix}-assistive-listening-systems:before { content: $fa-var-assistive-listening-systems; }\n.#{$fa-css-prefix}-asl-interpreting:before,\n.#{$fa-css-prefix}-american-sign-language-interpreting:before { content: $fa-var-american-sign-language-interpreting; }\n.#{$fa-css-prefix}-deafness:before,\n.#{$fa-css-prefix}-hard-of-hearing:before,\n.#{$fa-css-prefix}-deaf:before { content: $fa-var-deaf; }\n.#{$fa-css-prefix}-glide:before { content: $fa-var-glide; }\n.#{$fa-css-prefix}-glide-g:before { content: $fa-var-glide-g; }\n.#{$fa-css-prefix}-signing:before,\n.#{$fa-css-prefix}-sign-language:before { content: $fa-var-sign-language; }\n.#{$fa-css-prefix}-low-vision:before { content: $fa-var-low-vision; }\n.#{$fa-css-prefix}-viadeo:before { content: $fa-var-viadeo; }\n.#{$fa-css-prefix}-viadeo-square:before { content: $fa-var-viadeo-square; }\n.#{$fa-css-prefix}-snapchat:before { content: $fa-var-snapchat; }\n.#{$fa-css-prefix}-snapchat-ghost:before { content: $fa-var-snapchat-ghost; }\n.#{$fa-css-prefix}-snapchat-square:before { content: $fa-var-snapchat-square; }\n.#{$fa-css-prefix}-pied-piper:before { content: $fa-var-pied-piper; }\n.#{$fa-css-prefix}-first-order:before { content: $fa-var-first-order; }\n.#{$fa-css-prefix}-yoast:before { content: $fa-var-yoast; }\n.#{$fa-css-prefix}-themeisle:before { content: $fa-var-themeisle; }\n.#{$fa-css-prefix}-google-plus-circle:before,\n.#{$fa-css-prefix}-google-plus-official:before { content: $fa-var-google-plus-official; }\n.#{$fa-css-prefix}-fa:before,\n.#{$fa-css-prefix}-font-awesome:before { content: $fa-var-font-awesome; }\n.#{$fa-css-prefix}-handshake-o:before { content: $fa-var-handshake-o; }\n.#{$fa-css-prefix}-envelope-open:before { content: $fa-var-envelope-open; }\n.#{$fa-css-prefix}-envelope-open-o:before { content: $fa-var-envelope-open-o; }\n.#{$fa-css-prefix}-linode:before { content: $fa-var-linode; }\n.#{$fa-css-prefix}-address-book:before { content: $fa-var-address-book; }\n.#{$fa-css-prefix}-address-book-o:before { content: $fa-var-address-book-o; }\n.#{$fa-css-prefix}-vcard:before,\n.#{$fa-css-prefix}-address-card:before { content: $fa-var-address-card; }\n.#{$fa-css-prefix}-vcard-o:before,\n.#{$fa-css-prefix}-address-card-o:before { content: $fa-var-address-card-o; }\n.#{$fa-css-prefix}-user-circle:before { content: $fa-var-user-circle; }\n.#{$fa-css-prefix}-user-circle-o:before { content: $fa-var-user-circle-o; }\n.#{$fa-css-prefix}-user-o:before { content: $fa-var-user-o; }\n.#{$fa-css-prefix}-id-badge:before { content: $fa-var-id-badge; }\n.#{$fa-css-prefix}-drivers-license:before,\n.#{$fa-css-prefix}-id-card:before { content: $fa-var-id-card; }\n.#{$fa-css-prefix}-drivers-license-o:before,\n.#{$fa-css-prefix}-id-card-o:before { content: $fa-var-id-card-o; }\n.#{$fa-css-prefix}-quora:before { content: $fa-var-quora; }\n.#{$fa-css-prefix}-free-code-camp:before { content: $fa-var-free-code-camp; }\n.#{$fa-css-prefix}-telegram:before { content: $fa-var-telegram; }\n.#{$fa-css-prefix}-thermometer-4:before,\n.#{$fa-css-prefix}-thermometer:before,\n.#{$fa-css-prefix}-thermometer-full:before { content: $fa-var-thermometer-full; }\n.#{$fa-css-prefix}-thermometer-3:before,\n.#{$fa-css-prefix}-thermometer-three-quarters:before { content: $fa-var-thermometer-three-quarters; }\n.#{$fa-css-prefix}-thermometer-2:before,\n.#{$fa-css-prefix}-thermometer-half:before { content: $fa-var-thermometer-half; }\n.#{$fa-css-prefix}-thermometer-1:before,\n.#{$fa-css-prefix}-thermometer-quarter:before { content: $fa-var-thermometer-quarter; }\n.#{$fa-css-prefix}-thermometer-0:before,\n.#{$fa-css-prefix}-thermometer-empty:before { content: $fa-var-thermometer-empty; }\n.#{$fa-css-prefix}-shower:before { content: $fa-var-shower; }\n.#{$fa-css-prefix}-bathtub:before,\n.#{$fa-css-prefix}-s15:before,\n.#{$fa-css-prefix}-bath:before { content: $fa-var-bath; }\n.#{$fa-css-prefix}-podcast:before { content: $fa-var-podcast; }\n.#{$fa-css-prefix}-window-maximize:before { content: $fa-var-window-maximize; }\n.#{$fa-css-prefix}-window-minimize:before { content: $fa-var-window-minimize; }\n.#{$fa-css-prefix}-window-restore:before { content: $fa-var-window-restore; }\n.#{$fa-css-prefix}-times-rectangle:before,\n.#{$fa-css-prefix}-window-close:before { content: $fa-var-window-close; }\n.#{$fa-css-prefix}-times-rectangle-o:before,\n.#{$fa-css-prefix}-window-close-o:before { content: $fa-var-window-close-o; }\n.#{$fa-css-prefix}-bandcamp:before { content: $fa-var-bandcamp; }\n.#{$fa-css-prefix}-grav:before { content: $fa-var-grav; }\n.#{$fa-css-prefix}-etsy:before { content: $fa-var-etsy; }\n.#{$fa-css-prefix}-imdb:before { content: $fa-var-imdb; }\n.#{$fa-css-prefix}-ravelry:before { content: $fa-var-ravelry; }\n.#{$fa-css-prefix}-eercast:before { content: $fa-var-eercast; }\n.#{$fa-css-prefix}-microchip:before { content: $fa-var-microchip; }\n.#{$fa-css-prefix}-snowflake-o:before { content: $fa-var-snowflake-o; }\n.#{$fa-css-prefix}-superpowers:before { content: $fa-var-superpowers; }\n.#{$fa-css-prefix}-wpexplorer:before { content: $fa-var-wpexplorer; }\n.#{$fa-css-prefix}-meetup:before { content: $fa-var-meetup; }\n", "$primaryBackground: #b8002c; $transitionBackground: #db0100; $secondaryBackground: #e8410c;\n@import \"../../node_modules/reset-css/sass/_reset.scss\";\n@import \"../../node_modules/purecss/build/pure.css\";\n@import \"../../node_modules/font-awesome/scss/font-awesome.scss\";\n@import \"../../node_modules/selectize/dist/css/selectize.css\";\n@import \"../../node_modules/highlight.js/styles/zenburn.css\";\n\n/*************\n * VARIABLES *\n *************/\n\n$lighYellow: \"#fffdbf\";\n\n/************************************************************\n * GENERAL\n ************************************************************/\n* {\n  margin: 0;\n  padding: 0;\n}\n\nhtml, body {\n  height: 100%;\n}\n\nbody {\n  font-family: sans-serif;\n  line-height: 1.5;\n  min-height: 100%;\n}\n\n.wrap {\n  width: 80%;\n  max-width: 1100px;\n  margin: auto;\n  position: relative;\n}\n\n.left {\n  float: left;\n}\n\n.right {\n  float: right;\n}\n\n.center {\n  padding: auto;\n  text-align: center;\n}\n\n.v-center { /*specify a height to center vertically*/\n  display: table-cell;\n  vertical-align: middle;\n}\n\ni {\n  font-style: italic;\n}\n\nh1 {\n  margin: 1em 0;\n  font-size: 2em;\n  font-weight: 900;\n}\n\nh2 {\n  margin: 1em 0;\n  font-size: 1.5em;\n  font-weight: 700;\n  color: #1c1c1c;\n  border-bottom: solid 1px #bbb;\n}\n\nh3 {\n  font-weight: 500;\n  color: #2e3436;\n  padding: 0;\n  margin-top: 0;\n}\n\np {\n  padding: 0.5em 0;\n  margin-bottom: 1em;\n}\n\na {\n  color: midnightblue;\n  &:hover,\n  &:focus,\n  &.pure-menu-link:hover,\n  &.pure-menu-link:focus,\n  .pure-menu-selected &.pure-menu-link:hover,\n  .pure-menu-selected &.pure-menu-link:focus {\n    color: white;\n    background-color: #444444;\n    padding: .5em 1em;\n  }\n  &:hover,\n  &:focus {\n    padding: .15rem;\n  }\n}\n\n.overflow {\n  overflow: hidden;\n}\n\nul {\n  padding-left: 1.5em;\n}\n\nstrong, b {\n  font-weight: bold;\n}\n\npre, code, kbd, samp, tt {\n  font-family: monospace, monospace;\n  font-size: .9rem;\n  //background-color: rgba(0, 0, 0, .1);\n  padding: 0 .2rem;\n}\n.xmldata {\n  font-family: monospace;\n}\n\n/************************************************************\n * BUTTONS\n ************************************************************/\n.code-box-title .clipboard-btn {\n  background-color: #f0f0f0;\n  border: 1px solid #ccc;\n  position: absolute;\n  right: 0;\n  height: inherit;\n  margin-top: -2em;\n  margin-right: 4px;\n}\n\n.pure-button {\n  &:hover, &:focus {\n    background-color: #555;\n    padding: .5em 1em;\n    color: #fff;\n  }\n}\n\n.pure-button-red {\n  background-color: rgb(219, 1, 0);\n  color: #fff;\n  &:hover, &:focus {\n    background-color: #555;\n    padding: .5em 1em;\n  }\n}\n\n.pure-button.hollow {\n  background-color: #FFFFFF;\n  color: #6f6f6f;\n  border: solid 1px #E6E6E6;\n}\n\n.pure-button.hollow:hover {\n  background-image: none;\n}\n\n.pure-button.hollow[disabled] {\n  cursor: auto;\n  opacity: initial;\n  pointer-events: auto;\n  -webkit-user-drag: auto;\n  -webkit-user-select: auto;\n  -moz-user-select: text;\n  -ms-user-select: text;\n  user-select: text;\n}\n\n.pure-button.hljs {\n  display: inline-block;\n  border: 0;\n  background-color: transparent;\n  &:hover, &:focus {\n    background-color: #f0f0f0;\n    color: black;\n  }\n}\n\n.pure-button-group .pure-button:first-child,\n.pure-button-group .pure-button:last-child {\n  -webkit-border-radius: 0;\n  -moz-border-radius: 0;\n  border-radius: 0;\n}\n\n.pure-button-group.two-elements .pure-button {\n  margin: 0;\n  line-height: unset;\n  border: 1px solid #E6E6E6;\n}\n\n.pure-button-group.two-elements form {\n  display: inline;\n}\n\n.pure-button-group.two-elements .pure-button:first-child {\n  border-right: none;\n}\n\n.pure-button-group.two-elements .pure-button:last-child {\n  border-right: 1px solid #E6E6E6;\n}\n\n.pure-button-group .pure-button.show-files {\n  max-width: 450px;\n  overflow: hidden;\n}\n\n.top-right-corner {\n  position: absolute;\n  right: 1.75em;\n\n}\n\n/* ***********************************************************\nSLIDING SIDE-MENU FOR SMALL SCREENS\n************************************************************ */\n/*\nAdd transition to containers so they can push in and out.\n*/\n#layout,\n#menu,\n#foot,\n.menu-link {\n  -webkit-transition: all 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out;\n  -ms-transition: all 0.2s ease-out;\n  -o-transition: all 0.2s ease-out;\n  transition: all 0.2s ease-out;\n}\n\n/*\nThis is the parent `<div>` that contains the menu and the content area.\n*/\n#layout.active #menu {\n  right: 11em;\n  width: 11em;\n}\n\n/*\nThe `#menu` `<div>` is the parent `<div>` that contains the `.pure-menu` that\nappears on the left side of the page.\n*/\n#menu {\n  margin-right: -11em; /* \"#menu\" width */\n  width: 11em;\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000; /* so the menu or its navicon stays above all content */\n  background: $secondaryBackground;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n}\n\n/*\nAll anchors inside the menu should be styled like this.\n*/\n#menu a {\n  color: #ffffff;\n  border: none;\n  padding: 0.6em 0 0.6em 0.6em;\n}\n\n/*\nRemove all background/borders, since we are applying them to #menu.\n*/\n#menu .pure-menu,\n#menu .pure-menu ul {\n  border: none;\n  background: transparent;\n}\n\n/*\nAdd that light border to separate items into groups.\n*/\n#menu .pure-menu ul,\n#menu .pure-menu .menu-item-divided {\n  border-top: 1px solid $transitionBackground;\n}\n\n/*\nThis styles the selected menu item `<li>`.\n*/\n#menu .pure-menu-selected,\n#menu .pure-menu-heading {\n  background: $primaryBackground;\n  color: black;\n}\n\n/*\nThis styles a link within a selected menu item `<li>`.\n*/\n#menu .pure-menu-selected a {\n  color: #fff;\n}\n\n/*\nThis styles the menu heading.\n*/\n#menu .pure-menu-heading {\n  font-size: 110%;\n  color: #fff;\n  margin: 0;\n  text-transform: none;\n}\n\n.frontpage-menu .pure-menu-item {\n  border-bottom: 1px solid lightgrey;\n}\n\n.frontpage-menu .pure-menu-selected {\n  border-bottom: 1px solid black;\n  font-weight: bold;\n}\n\n/* -- Dynamic Button For Responsive Menu -------------------------------------*/\n\n/*\nThe button to open/close the Menu is custom-made and not part of Pure. Here's\nhow it works:\n*/\n\n/*\n`.menu-link` represents the responsive menu toggle that shows/hides on\nsmall screens.\n*/\n.menu-link {\n  position: relative;\n  display: block; /* show this only on small screens */\n  top: 0;\n  right: 0; /* \"#menu width\" */\n  background: transparent;\n  z-index: 10;\n  height: 2rem;\n  padding: 2rem 0;\n  text-decoration: none;\n  &:hover, &:focus {\n    padding: 2rem 0;\n    background: none !important;\n  }\n}\n\n.menu-link span:hover,\n.menu-link span:focus {\n  color: rgba(1, 1, 1, 0.8);\n}\n\n.menu-link span {\n  position: relative;\n  display: block;\n  color: #FFFFFF;\n}\n\n.menu-link span,\n.menu-link span:before,\n.menu-link span:after {\n  background-color: transparent;\n}\n\n/* -- Responsive Styles (Media Queries) ------------------------------------- */\n\n@media screen and (max-width: 40em) {\n  #layout.active {\n    position: relative;\n    right: 11em;\n    /* moves to right as many ems as the one used by the menu */\n  }\n  #menuLink.menu-link.active {\n    position: fixed;\n    right: 13em;\n  }\n  #foot.active {\n    margin-right: 11em;\n  }\n}\n\n/* ***********************************************************\nHEADER\n************************************************************ */\n#header {\n  height: 6rem;\n  background: linear-gradient(141deg, $primaryBackground 0%, $transitionBackground 51%, $secondaryBackground 75%)\n}\n\n.logo-header {\n  min-width: 8em;\n  height: 6rem;\n  max-width: 70%;\n}\n\n#logo {\n  font-family: sans-serif;\n  font-size: 2.5em;\n  color: white;\n  text-shadow: 0 3px 2px rgba(83, 41, 0, 1);\n}\n\n.simple {\n  font-weight: 300;\n}\n\n.saml {\n  font-family: Verdana;\n  letter-spacing: -0.12em;\n  font-weight: 600;\n  margin-left: -0.05em;\n}\n\n.language-menu {\n  font-family: FontAwesome, sans-serif;\n  min-width: 10rem;\n}\n\n.language-bar {\n  height: 6rem;\n}\n\n/* ***********************************************************\nCONTENT\n************************************************************ */\n#layout {\n  right: 0;\n  padding-right: 0;\n}\n\n#content {\n  padding-top: 2em;\n  padding-bottom: 2rem;\n}\n\n.message-box {\n  background-color: #f4f4f4;\n  border-left-color: #444444;\n  border-left-style: solid;\n  border-left-width: 0.3125rem;\n  box-shadow: 0 5px 8px -6px rgba(0, 0, 0, 0.2);\n  margin: 1rem 0;\n  padding: 1.3rem;\n  position: relative;\n}\n\n.message-box.error {\n  background-color: #f7e4e1;\n  border-left-color: #cc4b37;\n}\n\n.message-box.warning {\n  background-color: $lighYellow;\n  border-left-color: #f9f56b;\n}\n\n.message-box.success {\n  background-color: #daf7e6;\n  border-left-color: #46cc48;\n}\n\n.auth_methods {\n  margin-top: 2em;\n  width: 35%;\n}\n\n.code-box {\n  margin-bottom: 1em;\n  border: 1px solid #ccc;\n  a {\n    padding: .5em;\n  }\n}\n\n.code-box-content {\n  font-size: 1em;\n  line-height: 1.15;\n  padding: 0.5em 1em;\n  display: inline-block;\n  min-height: 1em;\n  width: 100%;\n  height: 100%;\n  white-space: pre-wrap;\n  font-family: monospace;\n  &::selection {\n    color: black;\n    background: $lighYellow;\n  }\n}\n\n.code-box-title {\n  border-bottom: 1px solid #ccc;\n  background-color: #e0e0e0;\n  padding: 0.5em 0 0.5em 0.5em;\n}\n\npre#xmlmetadata {\n  width: 98%;\n}\n\n/* ***********************************************************\nFOOTER\n************************************************************ */\n#layout {\n  min-height: 100%;\n  margin: 0 auto -6rem auto; /*negative margin = footer height + padding-top + padding-bottom*/\n}\n\n#bottom, #push {\n  height: 6rem;\n}\n\n#footer {\n  width: 100%;\n  background: linear-gradient(141deg, $primaryBackground 0%, $transitionBackground 51%, $secondaryBackground 75%);\n  height: 4rem;\n  padding: 2rem 0 0;\n  text-align: center;\n  color: white;\n\n  a, a:visited {\n    color: white;\n    &:hover, &:focus {\n      background-color: white;\n      color: black;\n      padding: .15rem;\n      margin-left: -.15rem;\n      margin-top: -.15rem;\n      text-decoration: none;\n    }\n  }\n}\n\n.copyrights {\n  padding-top: .5rem;\n  height: 3.5rem;\n  font-size: .8rem;\n}\n\n.logo-footer-right {\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translate(0, -50%);\n}\n\n.logo-footer {\n  height: 4rem;\n}\n\n/* ***********************************************************\nFORMS\n************************************************************ */\n.text-area {\n  margin-top: .5em;\n  width: 100%;\n  font-size: 0.9em;\n  line-height: 1.15;\n}\n\n.file-upload input[type=\"url\"][disabled] {\n  cursor: pointer;\n  color: inherit;\n}\n\ninput[type=\"file\"] {\n  color: black;\n}\n\n.form-align {\n  position: relative;\n}\n\n.center-form {\n  display: inline-block;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.v-center-right {\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translate(0, -50%);\n}\n\n/* PURE */\n.pure-button,\n.pure-form input.edge,\n.pure-form textarea.edge {\n  -webkit-border-radius: 0;\n  -moz-border-radius: 0;\n  border-radius: 0;\n}\n\n.pure-form-aligned .pure-controls {\n  margin: 0 0 0 11em;\n}\n\n.pure-select {\n  float: right;\n}\n\n/* SELECTIZE */\n.selectize-input,\n.selectize-dropdown,\n.selectize-input.dropdown-active {\n  -webkit-border-radius: 0;\n  -moz-border-radius: 0;\n  border-radius: 0;\n}\n\n.selectize-input:after {\n  transform: translate(8px, 0);\n}\n\ndiv .item {\n  float: left;\n}\n\n.selectize-dropdown {\n  text-align: left;\n}\n\n.selectize-control.single .selectize-input, .selectize-dropdown.single {\n  background-color: white;\n  background-image: none;\n  border: 1px solid #ccc;\n  box-shadow: inset 0 1px 3px #ddd;\n  box-sizing: border-box;\n  font-size: inherit;\n  padding: 0.5em 0.6em;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.selectize-dropdown-content {\n  cursor: pointer;\n  .option {\n    &:hover, &:focus, &.active {\n      color: white;\n      background-color: #444444;\n    }\n    span.highlight {\n      color: black;\n      background-color: #fffbd5;\n    }\n  }\n}\n\n/* ***********************************************************\nIMAGES\n************************************************************ */\n.fa {\n  font-family: FontAwesome !important;\n}\n\nspan.fa, i.fa {\n  padding: 0 0.5em;\n}\n\n.message-box span.fa,\n.message-box i.fa {\n  padding: 0;\n}\n\n/* ***********************************************************\nTABLES\n************************************************************ */\n.pure-table-attributes {\n  table-layout: fixed;\n  width: 100%;\n}\n\n.attrname {\n  text-align: right;\n}\n\n.attrvalue {\n  overflow-wrap: break-word;\n}\n\ntable.attributes ul {\n  padding: inherit;\n}\n\n/* ***********************************************************\nMEDIA QUERIES\n************************************************************ */\n\n@media screen and (max-width: 40em) {\n  .pure-form .pure-input-sm-1-1 {\n    width: 100%;\n  }\n}\n\n@media screen and (max-width: 40em) {\n  .wrap {\n    width: 90%;\n  }\n  .pure-form-aligned .pure-controls {\n    margin: 0.5em 0 0;\n    float: left;\n  }\n  #layout {\n    padding-top: 0;\n    margin-bottom: -4rem;\n  }\n  .auth_methods {\n    width: 60%;\n  }\n  #logo {\n    font-size: 1.8em;\n  }\n  #footer {\n    height: 2.5rem;\n    padding-top: 1.5rem;\n    .copyrights {\n      height: 1rem;\n      padding-top: 0;\n    }\n  }\n}\n\n@media screen and (max-width: 0em), screen and (min-width: 40em) {\n  .show-for-small-only {\n    display: none !important;\n  }\n  .input-sm-placeholder {\n    display: inline-block;\n  }\n}\n\n@media screen and (min-width: 40em) {\n  .hide-for-large {\n    display: none !important;\n  }\n}\n\n@media screen and (max-width: 40em) {\n  .show-for-large {\n    display: none !important;\n  }\n}\n\n/*************************************************\n * Specifics for modules/core/frontpage*\n ************************************************/\n\n.float-r {\n  float: right;\n}\n\n.enablebox table {\n  border: 1px solid #eee;\n\n  margin-left: 1em;\n}\n\n.enablebox.mini table {\n  float: right;\n}\n\n.enablebox tr td {\n  padding: .5px 1em 1px .5em;\n  margin: 0;\n}\n\n.enablebox {\n  font-size: 85%;\n}\n\n.enablebox tr.enabled td {\n  background: #eee;\n}\n\n.enablebox tr.disabled td {\n  background: #ccc;\n}\n\nfieldset.fancyfieldset {\n  padding-left: 1.5em;\n  //margin: 2em 1em 1em 0;\n  //border: 1px solid #bbb;\n}\n\nfieldset.fancyfieldset legend {\n  //margin-left: 2em;\n  padding: 3px 2em 3px 0;\n  //border: 1px solid #bbb;\n  width: 100%;\n}\n\ndt {\n  font-weight: bold;\n}\n\n.frontpage-menu {\n  margin-bottom: 1em;\n}\n\n.entity-name, .entity-deprecated, .entity-expired {\n  font-weight: bold;\n}\n\n.entity-expired {\n  color: #500;\n}\n\ndiv.preferredidp {\n  border: 1px dashed #ccc;\n  background: #eee;\n  padding: 2px 2em 2px 2em;\n}\n\n\n/*********\n * Utils *\n *********/\n\n.clear {\n  clear: both;\n}\n\n.breathe-top {\n  margin-top: 1em;\n}\n\n.expandable {\n  border: solid 1px #bbb;\n  width: 100%;\n\n  .general {\n    padding: 1em;\n  }\n  .content {\n    display: none;\n    padding: 1em;\n  }\n  .expander {\n    cursor: pointer;\n    text-align: center;\n    padding: .25em;\n    display: block;\n    color: black;\n    background-color: #f4f4f4;\n    border-top: solid 1px #bbb;\n    &:focus, &:hover {\n      background-color: #555;\n      color: white;\n    }\n    &:after {\n      content: \"\\f078\";\n      font-family: FontAwesome;\n    }\n  }\n\n  &.expanded {\n    .content {\n      display: block;\n      border-left: solid .25em #555;\n      border-right: solid .25em #555;\n    }\n    .expander {\n      border-bottom: none;\n      border-top: solid 1px #bbb;\n      border-left: solid .25em #555;\n      border-right: solid .25em #555;\n      &:after {\n        content: \"\\f077\";\n      }\n    }\n  }\n}\n\n.hidden {\n  display: none;\n}\n"], "sourceRoot": ""}
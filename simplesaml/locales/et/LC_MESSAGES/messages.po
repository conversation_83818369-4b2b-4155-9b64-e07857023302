
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:31+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: et\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{admin:metadata_saml20-idp}"
msgstr "SAML 2.0 IdP metaandmed"

msgid "{errors:descr_WRONGUSERPASS}"
msgstr ""
"Kas sellise kasutajatunnusega kasutajat ei leitud või pole sinu poolt "
"sisestatud parool õige. Palun kontrolli kasutajatunnust ja parooli "
"uuesti."

msgid "{logout:failed}"
msgstr "Välja logimine ebaõnnestus"

msgid "{status:attributes_header}"
msgstr "Sinu atribuudid"

msgid "{admin:metaover_group_metadata.saml20-sp-remote}"
msgstr "SAML 2.0 teenusepakkuja (kaug)"

msgid "{errors:descr_NOCERT}"
msgstr "Autentimine ei õnnestunud: brauser ei saatnud ühtegi sertifikaati"

msgid "{errors:title_PROCESSASSERTION}"
msgstr "Tõrge identiteedipakkuja vastuse töötlemisel"

msgid "{errors:title_NOSTATE}"
msgstr "Olekuinfo kadunud"

msgid "{login:username}"
msgstr "Kasutajatunnus"

msgid "{errors:title_METADATA}"
msgstr "Metaandmete laadimise tõrge"

msgid "{admin:metaconv_title}"
msgstr "Metaandmete parsija"

msgid "{admin:cfg_check_noerrors}"
msgstr "Tõrkeid ei leitud"

msgid "{errors:descr_LOGOUTINFOLOST}"
msgstr ""
"Teave aktiivse väljalogimisoperatsiooni kohta läks kaduma. Pöördu tagasi "
"teenuse juurde, millest soovisid välja logida ja proovi uuesti. See tõrge"
" võib olla põhjustatud väljalogimisinfo aegumisest. Väljalogimisinfo "
"salvestatakse piiratud ajaks, tavaliselt mõneks tunniks. See on kauem kui"
" tavaline väljalogimine peaks aega võtma, seega võib see tõrge anda märku"
" ka mõnest teisest tõrkest seadistustes. Kui probleem ei kao, siis võta "
"ühendust oma teenusepakkujaga."

msgid "{disco:previous_auth}"
msgstr "Varem oled valinud autentida, kasutades"

msgid "{admin:cfg_check_back}"
msgstr "Mine tagasi failide nimekirja"

msgid "{errors:report_trackid}"
msgstr ""
"Kui rapoteerid sellest tõrkest, siis teata kindlasti ka jälgimisnumber, "
"mis võimaldab süsteemiadministraatoril logifailidest sinu sessiooniga "
"seotud infot leida:"

msgid "{login:change_home_org_title}"
msgstr "Muuda oma koduorganisatsiooni"

msgid "{errors:descr_METADATANOTFOUND}"
msgstr "Olemi metaandmeid ei leitud: %ENTITYID%"

msgid "{admin:metadata_metadata}"
msgstr "Metaandmed"

msgid "{errors:report_text}"
msgstr ""
"Lisaks sisesta ka oma meiliaadress, et administraatorid saaksid seosest "
"selle tõrkega vajadusel sinuga hiljem ühendust võtta:"

msgid "{errors:report_header}"
msgstr "Raporteeri tõrked"

msgid "{login:change_home_org_text}"
msgstr ""
"Sa valisid oma koduorganisatsiooniks <b>%HOMEORG%</b>. Kui see pole õige,"
" siis võid uuesti valida."

msgid "{errors:title_PROCESSAUTHNREQUEST}"
msgstr "Tõrge teenusepakkuja päringu töötlemisel"

msgid "{errors:descr_PROCESSASSERTION}"
msgstr "Identiteedipakkuja poolt saadetud vastust ei aktsepteeritud."

msgid "{errors:debuginfo_header}"
msgstr "Silumisinfo"

msgid "{admin:debug_sending_message_msg_text}"
msgstr "Kuna oled silumisrežiimis, siis on sul võimalik näha saadetava teate sisu:"

msgid "{errors:descr_RESPONSESTATUSNOSUCCESS}"
msgstr ""
"Identiteedipakkuja vastas tõrkega (SAML-vastuse olekukood polnud "
"positiivne)."

msgid "{admin:metadata_shib13-idp}"
msgstr "Shib 1.3 IdP metaandmed"

msgid "{login:help_text}"
msgstr ""
"Paha lugu! Ilma kasutajatunnust ja parooli teadmata pole võimalik seda "
"teenust kasutada. Loodetavasti saab sind keegi aidata. Võta ühendust oma "
"ülikooli kasutajatoeteenusega!"

msgid "{logout:default_link_text}"
msgstr "Mine tagasi SimpleSAMLphp paigaldusleheküljele"

msgid "{errors:error_header}"
msgstr "SimpleSAMLphp tõrge"

msgid "{login:help_header}"
msgstr "Appi! Ma ei mäleta parooli."

msgid "{errors:descr_LDAPERROR}"
msgstr ""
"LDAP on kasutajate andmebaas ja sisselogimisel püütakse  LDAP-andmebaasi "
"ühendust luua. Seekord tekkis ühenduse loomisel tõrge."

msgid "{errors:descr_METADATA}"
msgstr ""
"Midagi on su SimpleSAMLphp paigalduses valesti seadistatud. Kui sa oled "
"selle teenuse administraator, siis peaksid kontrollima, et metaandmete "
"seadistused oleks korrektselt seadistatud."

msgid "{errors:title_BADREQUEST}"
msgstr "Saabus halb päring"

msgid "{status:sessionsize}"
msgstr "Sessiooni suurus: %SIZE%"

msgid "{logout:title}"
msgstr "Logis välja"

msgid "{admin:metaconv_xmlmetadata}"
msgstr "XML-metaandmed"

msgid "{admin:metaover_unknown_found}"
msgstr "Järgmistest väljadest ei saadud aru"

msgid "{errors:title_AUTHSOURCEERROR}"
msgstr "Autentimisallika tõrge"

msgid "{login:select_home_org}"
msgstr "Vali oma koduorganisatsioon"

msgid "{logout:hold}"
msgstr "Ootel"

msgid "{admin:cfg_check_header}"
msgstr "Seadistuste kontroll"

msgid "{admin:debug_sending_message_send}"
msgstr "Saada teade"

msgid "{status:logout}"
msgstr "Logi välja"

msgid "{errors:descr_DISCOPARAMS}"
msgstr "Tuvastusteenusele saadetud parameetrid ei vastanud nõuetele."

msgid "{errors:descr_CREATEREQUEST}"
msgstr "SAML päringu loomisel ilmnes tõrge."

msgid "{admin:metaover_optional_found}"
msgstr "Lisaväljad"

msgid "{logout:return}"
msgstr "Tagasi teenuse juurde"

msgid "{admin:metadata_xmlurl}"
msgstr ""
"<a href=\"%METAURL%\">Metaandmete XML-i on võimalik saada spetsiaalselt "
"aadressilt</a>:"

msgid "{logout:logout_all}"
msgstr "Jah, kõigist teenustest"

msgid "{admin:debug_disable_debug_mode}"
msgstr ""
"Silumisrežiimi on võimalik välja lülitada SimpleSAMLphp "
"seadistustefailist <tt>config/config.php</tt>."

msgid "{disco:select}"
msgstr "Vali"

msgid "{logout:also_from}"
msgstr "Sa oled sisse logitud ja nendesse teenustesse:"

msgid "{login:login_button}"
msgstr "Logi sisse"

msgid "{logout:progress}"
msgstr "Välja logimine..."

msgid "{login:error_wrongpassword}"
msgstr "Kasutajatunnus või parool pole õige."

msgid "{admin:metaover_group_metadata.shib13-sp-remote}"
msgstr "Shib 1.3 teenusepakkuja (kaug)"

msgid "{errors:descr_PROCESSAUTHNREQUEST}"
msgstr ""
"Identiteedipakkuja sai teenusepakkujalt autentimispäringu, kui päringu "
"töötlemisel tekkis tõrge."

msgid "{logout:logout_all_question}"
msgstr "Kas sa soovid kõigist ülal loetletud teenustest välja logida?"

msgid "{errors:title_NOACCESS}"
msgstr "Ligipääs puudub"

msgid "{login:error_nopassword}"
msgstr ""
"Sa saatsid midagi sisselogimislehele, kuid miskipärast parooli ei "
"saadetud. Palun proovi uuesti."

msgid "{errors:title_NORELAYSTATE}"
msgstr "RelayState puudub"

msgid "{errors:descr_NOSTATE}"
msgstr "Olekuinfo läks kaduma ja päringut pole võimalik uuesti käivitada"

msgid "{login:password}"
msgstr "Parool"

msgid "{errors:debuginfo_text}"
msgstr ""
"Allpool olev silumisinfo võib olla administraatorile või kasutajatoele "
"väga kasulik:"

msgid "{admin:cfg_check_missing}"
msgstr "Seadistustefailist puuduvad seadistused:"

msgid "{errors:descr_UNHANDLEDEXCEPTION}"
msgstr "Ilmnes käsitlemata tõrge."

msgid "{general:yes}"
msgstr "Jah"

msgid "{errors:title_CONFIG}"
msgstr "Konfiguratsioonitõrge"

msgid "{errors:title_LOGOUTREQUEST}"
msgstr "Tõrge väljalogimispäringu töötlemisel"

msgid "{admin:metaover_errorentry}"
msgstr "Tõrge selles metaandmete kirjes"

msgid "{errors:title_METADATANOTFOUND}"
msgstr "Metaandmeid ei leitud"

msgid "{login:contact_info}"
msgstr "Kontaktinfo:"

msgid "{errors:title_UNHANDLEDEXCEPTION}"
msgstr "Käsitlemata tõrge"

msgid "{status:header_saml20_sp}"
msgstr "SAML 2.0 SP demonäide"

msgid "{login:error_header}"
msgstr "Tõrge"

msgid "{errors:title_USERABORTED}"
msgstr "Autentimine katkestatud"

msgid "{logout:incapablesps}"
msgstr ""
"Üks või mitu teenust, millesse oled sisselogitud <i>ei toeta välja "
"logimise</i>. Selleks, et olla kindel kõigi sessioonide lõpetamises "
"soovitame <i>sulgeda kõik brauseri aknad</i>."

msgid "{admin:metadata_xmlformat}"
msgstr "SAML 2.0 metaandmete XML-vormingus:"

msgid "{admin:metaover_group_metadata.saml20-idp-remote}"
msgstr "SAML 2.0 identiteedipakkuja (hostitud)"

msgid "{admin:metaover_group_metadata.saml20-idp-hosted}"
msgstr "SAML 2.0 identiteedipakkuja (hostitud)"

msgid "{admin:metaover_required_found}"
msgstr "Kohustuslikud väljad"

msgid "{admin:cfg_check_select_file}"
msgstr "Vali seadistustefail, mida kontrollida:"

msgid "{errors:descr_UNKNOWNCERT}"
msgstr "Autentimine ei õnnestunud: brauser saatis tundmatu sertifikaadi"

msgid "{logout:logging_out_from}"
msgstr "Välja logimine järgmistest teenustest:"

msgid "{logout:loggedoutfrom}"
msgstr "Sa oled nüüd edukalt välja logitud teenusest %SP%."

msgid "{errors:errorreport_text}"
msgstr "Tõrkeraport saadeti administraatoritele."

msgid "{errors:descr_LOGOUTREQUEST}"
msgstr "Väljalogimispäringu töötlemisel tekkis tõrge"

msgid "{logout:success}"
msgstr "Sa oled kõigist ülal loetletud teenustest edukalt välja logitud."

msgid "{admin:cfg_check_notices}"
msgstr "Märkused"

msgid "{errors:descr_USERABORTED}"
msgstr "Autentimine katkestati kasutaja poolt"

msgid "{errors:descr_CASERROR}"
msgstr "CAS-serveriga suhtlemisel tekkis tõrge."

msgid "{general:no}"
msgstr "Ei"

msgid "{admin:metadata_saml20-sp}"
msgstr "SAML 2.0 SP metaandmed"

msgid "{admin:metaconv_converted}"
msgstr "Teisendatud metaandmed"

msgid "{logout:completed}"
msgstr "Lõpetatud"

msgid "{errors:descr_NOTSET}"
msgstr ""
"Seadistustes on vaikimisi parool (auth.adminpassword) muutmata. Palun "
"muuda seadistustefaili."

msgid "{general:service_provider}"
msgstr "Teenusepakkuja"

msgid "{errors:descr_BADREQUEST}"
msgstr "Leheküljele esitati vigane päring. Põhjus: %REASON%"

msgid "{logout:no}"
msgstr "Ei"

msgid "{disco:icon_prefered_idp}"
msgstr "[Eelistatud valik]"

msgid "{general:no_cancel}"
msgstr "Ei, loobu"

msgid "{login:user_pass_header}"
msgstr "Sisesta oma kasutajatunnus ja parool"

msgid "{errors:report_explain}"
msgstr "Kirjelda, millega tegelesid, kui see tõrge ilmnes..."

msgid "{errors:title_ACSPARAMS}"
msgstr "SAML-vastust ei pakutud"

msgid "{errors:descr_SLOSERVICEPARAMS}"
msgstr ""
"Sa külastasid SingleLogoutService liidest, kui ei pakkunud SAML "
"LogoutRequest või LogoutResponse."

msgid "{login:organization}"
msgstr "Organisatsioon"

msgid "{errors:title_WRONGUSERPASS}"
msgstr "Kasutajatunnus või parool pole õige"

msgid "{admin:metaover_required_not_found}"
msgstr "Järgmisi kohuslikke välju ei leitud"

msgid "{errors:descr_NOACCESS}"
msgstr "See lõpp-punkt pole lubatud. Kontrolli oma simpleSAMPphp seadistust."

msgid "{errors:title_SLOSERVICEPARAMS}"
msgstr "SAML-teade puudub"

msgid "{errors:descr_ACSPARAMS}"
msgstr ""
"Sa külastasid Assertion Consumer Service liidest, kuid ei pakkunud SAML "
"autentimisvastust."

msgid "{admin:debug_sending_message_text_link}"
msgstr "Oled teadet saatmas. Jätkamiseks vajuta teateviidet."

msgid "{errors:descr_AUTHSOURCEERROR}"
msgstr "Tõrge autentimisallikas %AUTHSOURCE%. Põhjus: %REASON%"

msgid "{status:some_error_occurred}"
msgstr "Ilmnes mingi tõrge"

msgid "{login:change_home_org_button}"
msgstr "Vali koduorganisatsioon"

msgid "{admin:cfg_check_superfluous}"
msgstr "Üleliigne seadistus seadistustefailis"

msgid "{errors:report_email}"
msgstr "E-posti aadress:"

msgid "{errors:howto_header}"
msgstr "Kuidas saada abi"

msgid "{errors:title_NOTSET}"
msgstr "Parool määramata"

msgid "{errors:descr_NORELAYSTATE}"
msgstr ""
"Selle päringu algataja ei täitnud RelayState parameetrit, mis näitab, "
"kuhu edasi minna."

msgid "{status:header_diagnostics}"
msgstr "SimpleSAMLphp diagnostika"

msgid "{status:intro}"
msgstr ""
"Tere! See on SimpleSAMLphp olekuteave. Siit on võimalik näha, kas su "
"sessioon on aegunud, kui kaua see veel kestab ja kõiki teisi sessiooniga "
"seotud atribuute."

msgid "{errors:title_NOTFOUNDREASON}"
msgstr "Lehekülge ei leitud"

msgid "{admin:debug_sending_message_title}"
msgstr "Teate saatmine"

msgid "{errors:title_RESPONSESTATUSNOSUCCESS}"
msgstr "Identiteedipakkujalt saadi tõrge"

msgid "{admin:metadata_shib13-sp}"
msgstr "Shib 1.3 SP metaandmed"

msgid "{admin:metaover_intro}"
msgstr "SAML olemi detailide vaatamiseks klõpsa SAML olemi päisel."

msgid "{errors:title_NOTVALIDCERT}"
msgstr "Vigane sertifikaat"

msgid "{general:remember}"
msgstr "Jäta meelde"

msgid "{disco:selectidp}"
msgstr "Vali oma identiteedipakkuja"

msgid "{login:help_desk_email}"
msgstr "Saada kasutajatoele e-kiri."

msgid "{login:help_desk_link}"
msgstr "Kasutajatoe koduleht"

msgid "{errors:title_CASERROR}"
msgstr "CAS tõrge"

msgid "{login:user_pass_text}"
msgstr ""
"Teenus nõuab autentimist. Palun sisesta allpool olevasse vormi oma "
"kasutajatunnus ja parool."

msgid "{errors:title_DISCOPARAMS}"
msgstr "Halb tuvastusteenuse päring"

msgid "{general:yes_continue}"
msgstr "Jah, jätka"

msgid "{disco:remember}"
msgstr "Jäta valik meelde"

msgid "{admin:metaover_group_metadata.saml20-sp-hosted}"
msgstr "SAML 2.0 teenusepakkuja (hostitud)"

msgid "{admin:metadata_simplesamlformat}"
msgstr ""
"SimpleSAMLphp formaadis: kasuta seda siis, kui ka teine pool kasutab "
"SimpleSAMLphp-d:"

msgid "{disco:login_at}"
msgstr "Logi sisse"

msgid "{errors:title_GENERATEAUTHNRESPONSE}"
msgstr "Autentimisvastuse loomine ei õnnestunud"

msgid "{errors:errorreport_header}"
msgstr "Tõrkeraport saadetud"

msgid "{errors:title_CREATEREQUEST}"
msgstr "Tõrge päringu loomisel"

msgid "{admin:metaover_header}"
msgstr "Metaandmete ülevaade"

msgid "{errors:report_submit}"
msgstr "Saada tõrkeraport"

msgid "{errors:title_INVALIDCERT}"
msgstr "Vigane sertifikaat"

msgid "{errors:title_NOTFOUND}"
msgstr "Lehekülge ei leitud"

msgid "{logout:logged_out_text}"
msgstr "Sa oled välja logitud."

msgid "{admin:metaover_group_metadata.shib13-sp-hosted}"
msgstr "Shib 1.3 teenusepakkuja (hostitud)"

msgid "{admin:metadata_cert_intro}"
msgstr "Lae alla X509 sertifikaadid PEM kodeeringus failidena."

msgid "{admin:debug_sending_message_msg_title}"
msgstr "Teade"

msgid "{errors:title_UNKNOWNCERT}"
msgstr "Tundmatu sertifikaat"

msgid "{errors:title_LDAPERROR}"
msgstr "LDAP-tõrge"

msgid "{logout:failedsps}"
msgstr ""
"Ühest või mitmest teenusest välja logimine ei õnnestunud. Selleks, et "
"olla kindel kõigi sessioonide lõpetamises soovitame <i>sulgeda kõik "
"brauseri aknad</i>."

msgid "{errors:descr_NOTFOUND}"
msgstr "Seda lehekülge ei leitud. Aadress oli: %URL%"

msgid "{errors:howto_text}"
msgstr ""
"See tõrge ilmnes tõenäoliselt SimpleSAMLphp ootamatu käitumise või "
"valesti seadistamise tõttu. Võta ühendust selle sisselogimisteenuse "
"administraatoriga ja saada talle ülalolev veateade."

msgid "{admin:metaover_group_metadata.shib13-idp-hosted}"
msgstr "Shib 1.3 identiteedipakkuja (hostitud)"

msgid "{errors:descr_NOTVALIDCERT}"
msgstr "Sa ei esitanud kehtivat sertifikaati."

msgid "{admin:debug_sending_message_text_button}"
msgstr "Oled teadet saatmas. Jätkamiseks vajuta teatesaatmisnuppu."

msgid "{admin:metaover_optional_not_found}"
msgstr "Järgmisi lisavälju ei leitud"

msgid "{logout:logout_only}"
msgstr "Ei, ainult %SP%"

msgid "{login:next}"
msgstr "Edasi"

msgid "{errors:descr_GENERATEAUTHNRESPONSE}"
msgstr "Tõrge tekkis, kui see identiteedipakkuja püüdis luua autentimisvastust."

msgid "{disco:selectidp_full}"
msgstr "Palun vali identiteedipakkuja, mille juures soovid autentida:"

msgid "{errors:descr_NOTFOUNDREASON}"
msgstr "Seda lehekülge ei leitud. Põhjus oli %REASON%. Aadress oli: %URL%"

msgid "{errors:title_NOCERT}"
msgstr "Sertifikaat puudub"

msgid "{errors:title_LOGOUTINFOLOST}"
msgstr "Väljalogimisinfo läks kaotsi"

msgid "{admin:metaover_group_metadata.shib13-idp-remote}"
msgstr "Shib 1.3 identiteedipakkuja (kaug)"

msgid "{errors:descr_CONFIG}"
msgstr "Paistab, et SimpleSAMLphp on vigaselt seadistatud."

msgid "{admin:metadata_intro}"
msgstr ""
"Need on SimpleSAMLphp poolt sulle genereeritud metaandmed. Võid saata "
"need metaandmed usaldatavatele partneritele usaldatava föderatsiooni "
"loomiseks."

msgid "{admin:metadata_cert}"
msgstr "Sertifikaadid"

msgid "{errors:descr_INVALIDCERT}"
msgstr ""
"Autentimine ei õnnestunud: brauseri poolt saadetud sertifikaat on vigane "
"või pole loetav"

msgid "{status:header_shib}"
msgstr "Shibbolethi demo"

msgid "{admin:metaconv_parse}"
msgstr "Parsi"

msgid "Person's principal name at home organization"
msgstr "Isiku põhinimi koduasutuses"

msgid "Superfluous options in config file"
msgstr "Üleliigne seadistus seadistustefailis"

msgid "Mobile"
msgstr "Mobiil"

msgid "Shib 1.3 Service Provider (Hosted)"
msgstr "Shib 1.3 teenusepakkuja (hostitud)"

msgid ""
"LDAP is the user database, and when you try to login, we need to contact "
"an LDAP database. An error occurred when we tried it this time."
msgstr ""
"LDAP on kasutajate andmebaas ja sisselogimisel püütakse  LDAP-andmebaasi "
"ühendust luua. Seekord tekkis ühenduse loomisel tõrge."

msgid ""
"Optionally enter your email address, for the administrators to be able "
"contact you for further questions about your issue:"
msgstr ""
"Lisaks sisesta ka oma meiliaadress, et administraatorid saaksid seosest "
"selle tõrkega vajadusel sinuga hiljem ühendust võtta:"

msgid "Display name"
msgstr "Kuvatav nimi"

msgid "Remember my choice"
msgstr "Jäta valik meelde"

msgid "SAML 2.0 SP Metadata"
msgstr "SAML 2.0 SP metaandmed"

msgid "Notices"
msgstr "Märkused"

msgid "Home telephone"
msgstr "Kodune telefon"

msgid ""
"Hi, this is the status page of SimpleSAMLphp. Here you can see if your "
"session is timed out, how long it lasts until it times out and all the "
"attributes that are attached to your session."
msgstr ""
"Tere! See on SimpleSAMLphp olekuteave. Siit on võimalik näha, kas su "
"sessioon on aegunud, kui kaua see veel kestab ja kõiki teisi sessiooniga "
"seotud atribuute."

msgid "Explain what you did when this error occurred..."
msgstr "Kirjelda, millega tegelesid, kui see tõrge ilmnes..."

msgid "An unhandled exception was thrown."
msgstr "Ilmnes käsitlemata tõrge."

msgid "Invalid certificate"
msgstr "Vigane sertifikaat"

msgid "Service Provider"
msgstr "Teenusepakkuja"

msgid "Incorrect username or password."
msgstr "Kasutajatunnus või parool pole õige."

msgid "There is an error in the request to this page. The reason was: %REASON%"
msgstr "Leheküljele esitati vigane päring. Põhjus: %REASON%"

msgid "E-mail address:"
msgstr "E-posti aadress:"

msgid "Submit message"
msgstr "Saada teade"

msgid "No RelayState"
msgstr "RelayState puudub"

msgid "Error creating request"
msgstr "Tõrge päringu loomisel"

msgid "Locality"
msgstr "Asukoht"

msgid "Unhandled exception"
msgstr "Käsitlemata tõrge"

msgid "The following required fields was not found"
msgstr "Järgmisi kohuslikke välju ei leitud"

msgid "Download the X509 certificates as PEM-encoded files."
msgstr "Lae alla X509 sertifikaadid PEM kodeeringus failidena."

#, python-format
msgid "Unable to locate metadata for %ENTITYID%"
msgstr "Olemi metaandmeid ei leitud: %ENTITYID%"

msgid "Organizational number"
msgstr "Registrikood"

msgid "Password not set"
msgstr "Parool määramata"

msgid "SAML 2.0 IdP Metadata"
msgstr "SAML 2.0 IdP metaandmed"

msgid "Post office box"
msgstr "Postkast"

msgid ""
"A service has requested you to authenticate yourself. Please enter your "
"username and password in the form below."
msgstr ""
"Teenus nõuab autentimist. Palun sisesta allpool olevasse vormi oma "
"kasutajatunnus ja parool."

msgid "CAS Error"
msgstr "CAS tõrge"

msgid ""
"The debug information below may be of interest to the administrator / "
"help desk:"
msgstr ""
"Allpool olev silumisinfo võib olla administraatorile või kasutajatoele "
"väga kasulik:"

msgid ""
"Either no user with the given username could be found, or the password "
"you gave was wrong. Please check the username and try again."
msgstr ""
"Kas sellise kasutajatunnusega kasutajat ei leitud või pole sinu poolt "
"sisestatud parool õige. Palun kontrolli kasutajatunnust ja parooli "
"uuesti."

msgid "Error"
msgstr "Tõrge"

msgid "Next"
msgstr "Edasi"

msgid "Distinguished name (DN) of the person's home organizational unit"
msgstr "Koduorganisatsiooni allüksuse unikaalne nimi (DN)"

msgid "State information lost"
msgstr "Olekuinfo kadunud"

msgid ""
"The password in the configuration (auth.adminpassword) is not changed "
"from the default value. Please edit the configuration file."
msgstr ""
"Seadistustes on vaikimisi parool (auth.adminpassword) muutmata. Palun "
"muuda seadistustefaili."

msgid "Converted metadata"
msgstr "Teisendatud metaandmed"

msgid "Mail"
msgstr "E-post"

msgid "No, cancel"
msgstr "Ei"

msgid ""
"You have chosen <b>%HOMEORG%</b> as your home organization. If this is "
"wrong you may choose another one."
msgstr ""
"Sa valisid oma koduorganisatsiooniks <b>%HOMEORG%</b>. Kui see pole õige,"
" siis võid uuesti valida."

msgid "Error processing request from Service Provider"
msgstr "Tõrge teenusepakkuja päringu töötlemisel"

msgid "Distinguished name (DN) of person's primary Organizational Unit"
msgstr "Peamise allüksuse unikaalne nimi (DN)"

msgid ""
"To look at the details for an SAML entity, click on the SAML entity "
"header."
msgstr "SAML olemi detailide vaatamiseks klõpsa SAML olemi päisel."

msgid "Enter your username and password"
msgstr "Sisesta oma kasutajatunnus ja parool"

msgid "Login at"
msgstr "Logi sisse"

msgid "No"
msgstr "Ei"

msgid "Home postal address"
msgstr "Kodune postiaadress"

msgid "WS-Fed SP Demo Example"
msgstr "WS-Fed SP demonäide"

msgid "SAML 2.0 Identity Provider (Remote)"
msgstr "SAML 2.0 identiteedipakkuja (hostitud)"

msgid "Error processing the Logout Request"
msgstr "Tõrge väljalogimispäringu töötlemisel"

msgid "Do you want to logout from all the services above?"
msgstr "Kas sa soovid kõigist ülal loetletud teenustest välja logida?"

msgid "Select"
msgstr "Vali"

msgid "The authentication was aborted by the user"
msgstr "Autentimine katkestati kasutaja poolt"

msgid "Your attributes"
msgstr "Sinu atribuudid"

msgid "Given name"
msgstr "Eesnimi"

msgid "Identity assurance profile"
msgstr "Identiteedi tagamise profiil"

msgid "SAML 2.0 SP Demo Example"
msgstr "SAML 2.0 SP demonäide"

msgid "Logout information lost"
msgstr "Väljalogimisinfo läks kaotsi"

msgid "Organization name"
msgstr "Organisatsiooni nimi"

msgid "Authentication failed: the certificate your browser sent is unknown"
msgstr "Autentimine ei õnnestunud: brauser saatis tundmatu sertifikaadi"

msgid ""
"You are about to send a message. Hit the submit message button to "
"continue."
msgstr "Oled teadet saatmas. Jätkamiseks vajuta teatesaatmisnuppu."

msgid "Home organization domain name"
msgstr "Koduorganisatsiooni domeen"

msgid "Go back to the file list"
msgstr "Mine tagasi failide nimekirja"

msgid "Error report sent"
msgstr "Tõrkeraport saadetud"

msgid "Common name"
msgstr "Üldnimi"

msgid "Please select the identity provider where you want to authenticate:"
msgstr "Palun vali identiteedipakkuja, mille juures soovid autentida:"

msgid "Logout failed"
msgstr "Välja logimine ebaõnnestus"

msgid "Identity number assigned by public authorities"
msgstr "Isikukood"

msgid "WS-Federation Identity Provider (Remote)"
msgstr "WS-Federation identiteedipakkuja (kaug)"

msgid "Error received from Identity Provider"
msgstr "Identiteedipakkujalt saadi tõrge"

msgid "LDAP Error"
msgstr "LDAP-tõrge"

msgid ""
"The information about the current logout operation has been lost. You "
"should return to the service you were trying to log out from and try to "
"log out again. This error can be caused by the logout information "
"expiring. The logout information is stored for a limited amout of time - "
"usually a number of hours. This is longer than any normal logout "
"operation should take, so this error may indicate some other error with "
"the configuration. If the problem persists, contact your service "
"provider."
msgstr ""
"Teave aktiivse väljalogimisoperatsiooni kohta läks kaduma. Pöördu tagasi "
"teenuse juurde, millest soovisid välja logida ja proovi uuesti. See tõrge"
" võib olla põhjustatud väljalogimisinfo aegumisest. Väljalogimisinfo "
"salvestatakse piiratud ajaks, tavaliselt mõneks tunniks. See on kauem kui"
" tavaline väljalogimine peaks aega võtma, seega võib see tõrge anda märku"
" ka mõnest teisest tõrkest seadistustes. Kui probleem ei kao, siis võta "
"ühendust oma teenusepakkujaga."

msgid "Some error occurred"
msgstr "Ilmnes mingi tõrge"

msgid "Organization"
msgstr "Organisatsioon"

msgid "No certificate"
msgstr "Sertifikaat puudub"

msgid "Choose home organization"
msgstr "Vali koduorganisatsioon"

msgid "Persistent pseudonymous ID"
msgstr "Püsiv pseudonüümne ID"

msgid "No SAML response provided"
msgstr "SAML-vastust ei pakutud"

msgid "No errors found."
msgstr "Tõrkeid ei leitud"

msgid "SAML 2.0 Service Provider (Hosted)"
msgstr "SAML 2.0 teenusepakkuja (hostitud)"

msgid "The given page was not found. The URL was: %URL%"
msgstr "Seda lehekülge ei leitud. Aadress oli: %URL%"

msgid "Configuration error"
msgstr "Konfiguratsioonitõrge"

msgid "Required fields"
msgstr "Kohustuslikud väljad"

msgid "An error occurred when trying to create the SAML request."
msgstr "SAML päringu loomisel ilmnes tõrge."

msgid ""
"This error probably is due to some unexpected behaviour or to "
"misconfiguration of SimpleSAMLphp. Contact the administrator of this "
"login service, and send them the error message above."
msgstr ""
"See tõrge ilmnes tõenäoliselt SimpleSAMLphp ootamatu käitumise või "
"valesti seadistamise tõttu. Võta ühendust selle sisselogimisteenuse "
"administraatoriga ja saada talle ülalolev veateade."

#, python-format
msgid "Your session is valid for %remaining% seconds from now."
msgstr "Sinu sessioon kehtib veel %remaining% sekundit."

msgid "Domain component (DC)"
msgstr "Domeeni komponent (DC)"

msgid "Shib 1.3 Service Provider (Remote)"
msgstr "Shib 1.3 teenusepakkuja (kaug)"

msgid "Password"
msgstr "Parool"

msgid "Nickname"
msgstr "Hüüdnimi"

msgid "Send error report"
msgstr "Saada tõrkeraport"

msgid ""
"Authentication failed: the certificate your browser sent is invalid or "
"cannot be read"
msgstr ""
"Autentimine ei õnnestunud: brauseri poolt saadetud sertifikaat on vigane "
"või pole loetav"

msgid "The error report has been sent to the administrators."
msgstr "Tõrkeraport saadeti administraatoritele."

msgid "Date of birth"
msgstr "Sünniaeg"

msgid "Private information elements"
msgstr "Privaatandmete elemendid"

msgid "You are also logged in on these services:"
msgstr "Sa oled sisse logitud ja nendesse teenustesse:"

msgid "SimpleSAMLphp Diagnostics"
msgstr "SimpleSAMLphp diagnostika"

msgid "Debug information"
msgstr "Silumisinfo"

msgid "No, only %SP%"
msgstr "Ei, ainult %SP%"

msgid "Username"
msgstr "Kasutajatunnus"

msgid "Go back to SimpleSAMLphp installation page"
msgstr "Mine tagasi SimpleSAMLphp paigaldusleheküljele"

msgid "You have successfully logged out from all services listed above."
msgstr "Sa oled kõigist ülal loetletud teenustest edukalt välja logitud."

msgid "You are now successfully logged out from %SP%."
msgstr "Sa oled nüüd edukalt välja logitud teenusest %SP%."

msgid "Affiliation"
msgstr "Rollid"

msgid "You have been logged out."
msgstr "Sa oled välja logitud."

msgid "Return to service"
msgstr "Tagasi teenuse juurde"

msgid "Logout"
msgstr "Logi välja"

msgid "State information lost, and no way to restart the request"
msgstr "Olekuinfo läks kaduma ja päringut pole võimalik uuesti käivitada"

msgid "Error processing response from Identity Provider"
msgstr "Tõrge identiteedipakkuja vastuse töötlemisel"

msgid "WS-Federation Service Provider (Hosted)"
msgstr "WS-Federation teenusepakkuja (hostitud)"

msgid "Preferred language"
msgstr "Eelistatud keel"

msgid "SAML 2.0 Service Provider (Remote)"
msgstr "SAML 2.0 teenusepakkuja (kaug)"

msgid "Surname"
msgstr "Perekonnanimi"

msgid "No access"
msgstr "Ligipääs puudub"

msgid "The following fields was not recognized"
msgstr "Järgmistest väljadest ei saadud aru"

msgid "Authentication error in source %AUTHSOURCE%. The reason was: %REASON%"
msgstr "Tõrge autentimisallikas %AUTHSOURCE%. Põhjus: %REASON%"

msgid "Bad request received"
msgstr "Saabus halb päring"

msgid "User ID"
msgstr "Kasutaja ID"

msgid "JPEG Photo"
msgstr "JPEG-foto"

msgid "Postal address"
msgstr "Postiaadress"

msgid "An error occurred when trying to process the Logout Request."
msgstr "Väljalogimispäringu töötlemisel tekkis tõrge"

msgid "Sending message"
msgstr "Teate saatmine"

msgid "In SAML 2.0 Metadata XML format:"
msgstr "SAML 2.0 metaandmete XML-vormingus:"

msgid "Logging out of the following services:"
msgstr "Välja logimine järgmistest teenustest:"

msgid ""
"When this identity provider tried to create an authentication response, "
"an error occurred."
msgstr "Tõrge tekkis, kui see identiteedipakkuja püüdis luua autentimisvastust."

msgid "Could not create authentication response"
msgstr "Autentimisvastuse loomine ei õnnestunud"

msgid "Labeled URI"
msgstr "Sildistatud URI"

msgid "SimpleSAMLphp appears to be misconfigured."
msgstr "Paistab, et SimpleSAMLphp on vigaselt seadistatud."

msgid "Shib 1.3 Identity Provider (Hosted)"
msgstr "Shib 1.3 identiteedipakkuja (hostitud)"

msgid "Metadata"
msgstr "Metaandmed"

msgid "Login"
msgstr "Logi sisse"

msgid ""
"This Identity Provider received an Authentication Request from a Service "
"Provider, but an error occurred when trying to process the request."
msgstr ""
"Identiteedipakkuja sai teenusepakkujalt autentimispäringu, kui päringu "
"töötlemisel tekkis tõrge."

msgid "Yes, all services"
msgstr "Jah, kõigist teenustest"

msgid "Logged out"
msgstr "Logis välja"

msgid "Postal code"
msgstr "Postiindeks"

msgid "Logging out..."
msgstr "Välja logimine..."

msgid "Metadata not found"
msgstr "Metaandmeid ei leitud"

msgid "SAML 2.0 Identity Provider (Hosted)"
msgstr "SAML 2.0 identiteedipakkuja (hostitud)"

msgid "Primary affiliation"
msgstr "Peamine kuuluvus"

msgid ""
"If you report this error, please also report this tracking number which "
"makes it possible to locate your session in the logs available to the "
"system administrator:"
msgstr ""
"Kui rapoteerid sellest tõrkest, siis teata kindlasti ka jälgimisnumber, "
"mis võimaldab süsteemiadministraatoril logifailidest sinu sessiooniga "
"seotud infot leida:"

msgid "XML metadata"
msgstr "XML-metaandmed"

msgid ""
"The parameters sent to the discovery service were not according to "
"specifications."
msgstr "Tuvastusteenusele saadetud parameetrid ei vastanud nõuetele."

msgid "Telephone number"
msgstr "Telefoninumber"

msgid ""
"Unable to log out of one or more services. To ensure that all your "
"sessions are closed, you are encouraged to <i>close your webbrowser</i>."
msgstr ""
"Ühest või mitmest teenusest välja logimine ei õnnestunud. Selleks, et "
"olla kindel kõigi sessioonide lõpetamises soovitame <i>sulgeda kõik "
"brauseri aknad</i>."

msgid "Bad request to discovery service"
msgstr "Halb tuvastusteenuse päring"

msgid "Select your identity provider"
msgstr "Vali oma identiteedipakkuja"

msgid "Entitlement regarding the service"
msgstr "Volitused selle teenuse suhtes"

msgid "Shib 1.3 SP Metadata"
msgstr "Shib 1.3 SP metaandmed"

msgid ""
"As you are in debug mode, you get to see the content of the message you "
"are sending:"
msgstr "Kuna oled silumisrežiimis, siis on sul võimalik näha saadetava teate sisu:"

msgid "Certificates"
msgstr "Sertifikaadid"

msgid "Remember"
msgstr "Jäta meelde"

msgid "Distinguished name (DN) of person's home organization"
msgstr "Koduorganisatsiooni unikaalne nimi (DN)"

msgid "You are about to send a message. Hit the submit message link to continue."
msgstr "Oled teadet saatmas. Jätkamiseks vajuta teateviidet."

msgid "Organizational unit"
msgstr "Allüksus"

msgid "Authentication aborted"
msgstr "Autentimine katkestatud"

msgid "Local identity number"
msgstr "Kohalik isikukood"

msgid "Report errors"
msgstr "Raporteeri tõrked"

msgid "Page not found"
msgstr "Lehekülge ei leitud"

msgid "Shib 1.3 IdP Metadata"
msgstr "Shib 1.3 IdP metaandmed"

msgid "Change your home organization"
msgstr "Muuda oma koduorganisatsiooni"

msgid "User's password hash"
msgstr "Kasutaja parooliräsi"

msgid ""
"In SimpleSAMLphp flat file format - use this if you are using a "
"SimpleSAMLphp entity on the other side:"
msgstr ""
"SimpleSAMLphp formaadis: kasuta seda siis, kui ka teine pool kasutab "
"SimpleSAMLphp-d:"

msgid "Yes, continue"
msgstr "Jah, jätka"

msgid "Completed"
msgstr "Lõpetatud"

msgid ""
"The Identity Provider responded with an error. (The status code in the "
"SAML Response was not success)"
msgstr ""
"Identiteedipakkuja vastas tõrkega (SAML-vastuse olekukood polnud "
"positiivne)."

msgid "Error loading metadata"
msgstr "Metaandmete laadimise tõrge"

msgid "Select configuration file to check:"
msgstr "Vali seadistustefail, mida kontrollida:"

msgid "On hold"
msgstr "Ootel"

msgid "Error when communicating with the CAS server."
msgstr "CAS-serveriga suhtlemisel tekkis tõrge."

msgid "No SAML message provided"
msgstr "SAML-teade puudub"

msgid "Help! I don't remember my password."
msgstr "Appi! Ma ei mäleta parooli."

msgid ""
"You can turn off debug mode in the global SimpleSAMLphp configuration "
"file <tt>config/config.php</tt>."
msgstr ""
"Silumisrežiimi on võimalik välja lülitada SimpleSAMLphp "
"seadistustefailist <tt>config/config.php</tt>."

msgid "How to get help"
msgstr "Kuidas saada abi"

msgid ""
"You accessed the SingleLogoutService interface, but did not provide a "
"SAML LogoutRequest or LogoutResponse. Please note that this endpoint is "
"not intended to be accessed directly."
msgstr ""
"Sa külastasid SingleLogoutService liidest, kui ei pakkunud SAML "
"LogoutRequest või LogoutResponse."

msgid "SimpleSAMLphp error"
msgstr "SimpleSAMLphp tõrge"

msgid ""
"One or more of the services you are logged into <i>do not support "
"logout</i>. To ensure that all your sessions are closed, you are "
"encouraged to <i>close your webbrowser</i>."
msgstr ""
"Üks või mitu teenust, millesse oled sisselogitud <i>ei toeta välja "
"logimise</i>. Selleks, et olla kindel kõigi sessioonide lõpetamises "
"soovitame <i>sulgeda kõik brauseri aknad</i>."

msgid "Organization's legal name"
msgstr "Organisatsiooni ametlik nimetus"

msgid "Options missing from config file"
msgstr "Seadistustefailist puuduvad seadistused:"

msgid "The following optional fields was not found"
msgstr "Järgmisi lisavälju ei leitud"

msgid "Authentication failed: your browser did not send any certificate"
msgstr "Autentimine ei õnnestunud: brauser ei saatnud ühtegi sertifikaati"

msgid ""
"This endpoint is not enabled. Check the enable options in your "
"configuration of SimpleSAMLphp."
msgstr "See lõpp-punkt pole lubatud. Kontrolli oma simpleSAMPphp seadistust."

msgid "You can <a href=\"%METAURL%\">get the metadata xml on a dedicated URL</a>:"
msgstr ""
"<a href=\"%METAURL%\">Metaandmete XML-i on võimalik saada spetsiaalselt "
"aadressilt</a>:"

msgid "Street"
msgstr "Tänav"

msgid ""
"There is some misconfiguration of your SimpleSAMLphp installation. If you"
" are the administrator of this service, you should make sure your "
"metadata configuration is correctly setup."
msgstr ""
"Midagi on su SimpleSAMLphp paigalduses valesti seadistatud. Kui sa oled "
"selle teenuse administraator, siis peaksid kontrollima, et metaandmete "
"seadistused oleks korrektselt seadistatud."

msgid "Incorrect username or password"
msgstr "Kasutajatunnus või parool pole õige"

msgid "Message"
msgstr "Teade"

msgid "Contact information:"
msgstr "Kontaktinfo:"

msgid "Unknown certificate"
msgstr "Tundmatu sertifikaat"

msgid "Legal name"
msgstr "Ametlik nimi"

msgid "Optional fields"
msgstr "Lisaväljad"

msgid ""
"The initiator of this request did not provide a RelayState parameter "
"indicating where to go next."
msgstr ""
"Selle päringu algataja ei täitnud RelayState parameetrit, mis näitab, "
"kuhu edasi minna."

msgid "You have previously chosen to authenticate at"
msgstr "Varem oled valinud autentida, kasutades"

msgid ""
"You sent something to the login page, but for some reason the password "
"was not sent. Try again please."
msgstr ""
"Sa saatsid midagi sisselogimislehele, kuid miskipärast parooli ei "
"saadetud. Palun proovi uuesti."

msgid "Fax number"
msgstr "Faksinumber"

msgid "Shibboleth demo"
msgstr "Shibbolethi demo"

msgid "Error in this metadata entry"
msgstr "Tõrge selles metaandmete kirjes"

msgid "Session size: %SIZE%"
msgstr "Sessiooni suurus: %SIZE%"

msgid "Parse"
msgstr "Parsi"

msgid ""
"Without your username and password you cannot authenticate "
"yourself for access to the service. There may be someone that can help "
"you. Consult the help desk at your organization!"
msgstr ""
"Paha lugu! Ilma kasutajatunnust ja parooli teadmata pole võimalik seda "
"teenust kasutada. Loodetavasti saab sind keegi aidata. Võta ühendust oma "
"ülikooli kasutajatoeteenusega!"

msgid "Metadata parser"
msgstr "Metaandmete parsija"

msgid "Choose your home organization"
msgstr "Vali oma koduorganisatsioon"

msgid "Send e-mail to help desk"
msgstr "Saada kasutajatoele e-kiri."

msgid "Metadata overview"
msgstr "Metaandmete ülevaade"

msgid "Title"
msgstr "Tiitel"

msgid "Manager"
msgstr "Juhataja"

msgid "You did not present a valid certificate."
msgstr "Sa ei esitanud kehtivat sertifikaati."

msgid "Authentication source error"
msgstr "Autentimisallika tõrge"

msgid "Affiliation at home organization"
msgstr "Rollid koduorganisatsioonis"

msgid "Help desk homepage"
msgstr "Kasutajatoe koduleht"

msgid "Configuration check"
msgstr "Seadistuste kontroll"

msgid "We did not accept the response sent from the Identity Provider."
msgstr "Identiteedipakkuja poolt saadetud vastust ei aktsepteeritud."

msgid "The given page was not found. The reason was: %REASON%  The URL was: %URL%"
msgstr "Seda lehekülge ei leitud. Põhjus oli %REASON%. Aadress oli: %URL%"

msgid "Shib 1.3 Identity Provider (Remote)"
msgstr "Shib 1.3 identiteedipakkuja (kaug)"

msgid ""
"Here is the metadata that SimpleSAMLphp has generated for you. You may "
"send this metadata document to trusted partners to setup a trusted "
"federation."
msgstr ""
"Need on SimpleSAMLphp poolt sulle genereeritud metaandmed. Võid saata "
"need metaandmed usaldatavatele partneritele usaldatava föderatsiooni "
"loomiseks."

msgid "[Preferred choice]"
msgstr "[Eelistatud valik]"

msgid "Organizational homepage"
msgstr "Organisatsiooni koduleht"

msgid ""
"You accessed the Assertion Consumer Service interface, but did not "
"provide a SAML Authentication Response. Please note that this endpoint is"
" not intended to be accessed directly."
msgstr ""
"Sa külastasid Assertion Consumer Service liidest, kuid ei pakkunud SAML "
"autentimisvastust."


msgid ""
"You are now accessing a pre-production system. This authentication setup "
"is for testing and pre-production verification only. If someone sent you "
"a link that pointed you here, and you are not <i>a tester</i> you "
"probably got the wrong link, and should <b>not be here</b>."
msgstr ""
"Sa kasutad nüüd testsüsteemi. See autentimisseadistus on mõeldud "
"testimiseks ja eelkontrollimiseks. Kui keegi saatis sulle lingi, mis "
"näitas siia, ja sa ei ole <i>testija</i>, siis said tõenäoliselt vale "
"lingi ja sa ei peaks <b>siin olema</b>."


#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:31+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: hr\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{admin:metadata_saml20-idp}"
msgstr "SAML 2.0 metapodaci o autentifikacijskom servisu"

msgid "{errors:descr_WRONGUSERPASS}"
msgstr ""
"Korisnik s navedenom korisničkom oznakom ne može biti pronađen ili je "
"zaporka koju ste unijeli neispravna. Molimo provjerite korisničku oznaku "
"i pokušajte ponovo."

msgid "{logout:failed}"
msgstr "Odjava nije uspjela"

msgid "{status:attributes_header}"
msgstr "Vaši atributi"

msgid "{admin:metaover_group_metadata.saml20-sp-remote}"
msgstr "SAML 2.0 davatelj usluge (udaljeni)"

msgid "{errors:descr_NOCERT}"
msgstr ""
"Neuspješna autentifikacija: vaš web preglednik nije poslao digitalni "
"certifikat"

msgid "{errors:title_PROCESSASSERTION}"
msgstr "Greška prilikom obrade odgovora pristiglog od autentifikacijskog servisa"

msgid "{errors:title_NOSTATE}"
msgstr "Podaci o stanju su izgubljeni"

msgid "{login:username}"
msgstr "Korisnička oznaka"

msgid "{errors:title_METADATA}"
msgstr "Greška prilikom učitavanja metapodataka"

msgid "{admin:metaconv_title}"
msgstr "Analizator metapodataka"

msgid "{admin:cfg_check_noerrors}"
msgstr "Nije pronađena niti jedna greška."

msgid "{errors:descr_LOGOUTINFOLOST}"
msgstr ""
"Informacija o aktualnom zahtjevu za odjavljivanjem se izgubila. "
"Preporučamo da se vratite u aplikaciju iz koje ste se htjeli odjaviti i "
"pokušate se odjaviti ponovo. Ova greška može biti uzrokovana istekom "
"valjanosti zahtjeva za odjavom. Zahtjev se pohranjuje određeno vrijeme - "
"u pravilu nekoliko sati. Obzirom da je to dulje nego što bi bilo koja "
"operacija odjavljivanja trebala trajati, greška koja se pojavila može "
"upućivati na grešku u konfiguraciji. Ako se problem nastavi, "
"kontaktirajte administratora aplikacije. "

msgid "{disco:previous_auth}"
msgstr "Prethodno ste odabrali autentifikaciju kroz"

msgid "{admin:cfg_check_back}"
msgstr "Vrati se natrag na popis datoteka"

msgid "{errors:report_trackid}"
msgstr ""
"Molimo da prilikom prijavljivanja greške pošaljete i ovaj identifikator "
"koji će administratorima omogućiti pronalaženje dodatnih informacija u "
"dnevničkim zapisima:"

msgid "{login:change_home_org_title}"
msgstr "Promjenite odabir vaše matične ustanove"

msgid "{errors:descr_METADATANOTFOUND}"
msgstr "Metapodaci za %ENTITYID% nisu pronađeni"

msgid "{admin:metadata_metadata}"
msgstr "Metapodaci"

msgid "{errors:report_text}"
msgstr ""
"Ako želite, unesite svoju elektroničku adresu kako bi vas administratori "
"mogli kontaktirati u slučaju da su im potrebne dodatne informacije:"

msgid "{errors:report_header}"
msgstr "Prijavi grešku"

msgid "{login:change_home_org_text}"
msgstr ""
"Odabrali ste <b>%HOMEORG%</b> kao vašu matičnu ustanovu. Ako to nije "
"točno možete odabrati drugu ustanovu."

msgid "{errors:title_PROCESSAUTHNREQUEST}"
msgstr "Greška prilikom obrade autentifikacijskog zahtjeva"

msgid "{errors:descr_PROCESSASSERTION}"
msgstr "Nije prihvaćen odgovor koji je poslao autentifikacijski servis."

msgid "{errors:debuginfo_header}"
msgstr "Informacije o greški"

msgid "{admin:debug_sending_message_msg_text}"
msgstr ""
"Obzirom da ste u modu za otkrivanje grešaka, imate mogućnost vidjeti "
"sadržaj poruke koju šaljete:"

msgid "{errors:descr_RESPONSESTATUSNOSUCCESS}"
msgstr ""
"Autentifikacijski servis je poslao odgovor koji sadrži informaciju o "
"pojavi greške. (Šifra statusa dostavljena u SAML odgovoru ne odgovara "
"šifri uspješno obrađenog zahtjeva)"

msgid "{admin:metadata_shib13-idp}"
msgstr "Shib 1.3 metapodaci o autentifikacijskom servisu"

msgid "{login:help_text}"
msgstr ""
"Šteta! - Bez ispravne korisničke oznake i zaporke ne možete pristupiti "
"aplikaciji. Da biste saznali vašu zaporku kontaktirajte administratora "
"elektroničkog (LDAP) imenika vaše ustanove."

msgid "{logout:default_link_text}"
msgstr "Natrag na početnu stranicu SimpleSAMLphp instalacije"

msgid "{errors:error_header}"
msgstr "SimpleSAMLphp greška"

msgid "{login:help_header}"
msgstr "Upomoć! Zaboravio/la sam svoju zaporku."

msgid "{errors:descr_LDAPERROR}"
msgstr ""
"Došlo je do greške prilikom spajanja na LDAP poslužitelj. Vaši podaci "
"pohranjeni su u LDAP imeniku i autentifikacijski servis se mora moći "
"spojiti na LDAP poslužitelj da bi provjerio ispravnost unesene korisničke"
" oznake i zaporke."

msgid "{errors:descr_METADATA}"
msgstr ""
"Programski alat SimpleSAMLphp je pogrešno iskonfiguriran. Ako ste "
"administrator ovog servisa, provjerite jesu li svi metapodaci u "
"konfiguraciji ispravni."

msgid "{errors:title_BADREQUEST}"
msgstr "Dobiveni zahtjev nije ispravan"

msgid "{status:sessionsize}"
msgstr "Veličina sjednice: %SIZE%"

msgid "{logout:title}"
msgstr "Odjavljeni ste"

msgid "{admin:metaconv_xmlmetadata}"
msgstr "Metapodaci u XML formatu"

msgid "{admin:metaover_unknown_found}"
msgstr "Sljedeća polja nisu prepoznata"

msgid "{errors:title_AUTHSOURCEERROR}"
msgstr "Greška u autentifikacijskom modulu"

msgid "{login:select_home_org}"
msgstr "Odaberite vašu matičnu ustanovu"

msgid "{logout:hold}"
msgstr "Na čekanju"

msgid "{admin:cfg_check_header}"
msgstr "Provjera konfiguracije"

msgid "{admin:debug_sending_message_send}"
msgstr "Pošalji poruku"

msgid "{status:logout}"
msgstr "Odjava"

msgid "{errors:descr_DISCOPARAMS}"
msgstr "Parametri poslani lokacijskom servisu nisu u ispravnom formatu."

msgid "{errors:descr_CREATEREQUEST}"
msgstr "Pojavila se greška prilikom kreiranja SAML zahtjeva."

msgid "{admin:metaover_optional_found}"
msgstr "Opcionalna polja"

msgid "{logout:return}"
msgstr "Povratak u aplikaciju"

msgid "{admin:metadata_xmlurl}"
msgstr "Metapodaci su dostupni na <a href=\"%METAURL%\">ovoj adresi</a>:"

msgid "{logout:logout_all}"
msgstr "Da, iz svih servisa"

msgid "{admin:debug_disable_debug_mode}"
msgstr ""
"Mod za otkrivanje grešaka možete isključiti u glavnoj SimpleSAMLphp "
"konfiguracijskoj datoteci <tt>config/config.php</tt>. "

msgid "{disco:select}"
msgstr "Odaberi"

msgid "{logout:also_from}"
msgstr "Također ste prijavljeni u sljedećim servisima:"

msgid "{login:login_button}"
msgstr "Prijavi se"

msgid "{logout:progress}"
msgstr "Odjava u tijeku..."

msgid "{login:error_wrongpassword}"
msgstr "Neispravna korisnička oznaka ili zaporka."

msgid "{admin:metaover_group_metadata.shib13-sp-remote}"
msgstr "Shib 1.3 davatelj usluge (udaljeni)"

msgid "{errors:descr_PROCESSAUTHNREQUEST}"
msgstr ""
"Autentifikacijski servis je dobio zahtjev od davatelja usluge, ali se "
"pojavila greška prilikom obrade zahtjeva."

msgid "{logout:logout_all_question}"
msgstr "Želite li se odjaviti iz svih gore navedenih servisa?"

msgid "{errors:title_NOACCESS}"
msgstr "Pristup nije dozvoljen"

msgid "{login:error_nopassword}"
msgstr ""
"Iz nekog razloga autentifikacijskom servisu nije proslijeđena vaša "
"zaporka. Molimo pokušajte ponovo."

msgid "{errors:title_NORELAYSTATE}"
msgstr "Parametar RelayState nije zadan"

msgid "{errors:descr_NOSTATE}"
msgstr "Podaci o stanju su izgubljeni i zahtjev se ne može reproducirati"

msgid "{login:password}"
msgstr "Zaporka"

msgid "{errors:debuginfo_text}"
msgstr ""
"Sljedeće informacije mogu biti zanimljive administratorima ili službi za "
"podršku korisnicima:"

msgid "{admin:cfg_check_missing}"
msgstr "Parametri koji nedostaju u konfiguracijskoj datoteci"

msgid "{errors:descr_UNHANDLEDEXCEPTION}"
msgstr "Pojavila se iznimka koja ne može do kraja biti obrađena."

msgid "{general:yes}"
msgstr "Da"

msgid "{errors:title_CONFIG}"
msgstr "Greška u konfiguraciji"

msgid "{errors:title_LOGOUTREQUEST}"
msgstr "Greška prilikom odjavljivanja"

msgid "{admin:metaover_errorentry}"
msgstr "Ovaj zapis metapodataka sadrži grešku"

msgid "{errors:title_METADATANOTFOUND}"
msgstr "Metapodaci nisu pronađeni"

msgid "{login:contact_info}"
msgstr "Kontakt podaci:"

msgid "{errors:title_UNHANDLEDEXCEPTION}"
msgstr "Neobrađena iznimka"

msgid "{status:header_saml20_sp}"
msgstr "Primjer SAML 2.0 davatelja usluge"

msgid "{login:error_header}"
msgstr "Greška"

msgid "{errors:title_USERABORTED}"
msgstr "Proces autentifikacije je prekinut"

msgid "{logout:incapablesps}"
msgstr ""
"Jedan ili više servisa na koje ste prijavljeni <i>ne podržava "
"odjavljivanje</i>. Da biste bili sigurni da su sve vaše sjednice "
"završene, preporučamo da <i>zatvorite web preglednik</i>."

msgid "{admin:metadata_xmlformat}"
msgstr "Metapodaci u SAML 2.0 XML formatu:"

msgid "{admin:metaover_group_metadata.saml20-idp-remote}"
msgstr "SAML 2.0 autentifikacijski servis (udaljeni)"

msgid "{admin:metaover_group_metadata.saml20-idp-hosted}"
msgstr "SAML 2.0 autentifikacijski servis (lokalni)"

msgid "{admin:metaover_required_found}"
msgstr "Obavezna polja"

msgid "{admin:cfg_check_select_file}"
msgstr "Odaberite konfiguracijsku datoteku koju želite provjeriti:"

msgid "{errors:descr_UNKNOWNCERT}"
msgstr ""
"Neuspješna autentifikacija: digitalni certifikat kojeg je poslao vaš web "
"preglednik je nepoznat"

msgid "{logout:logging_out_from}"
msgstr "Odjavljujete se iz sljedećih servisa:"

msgid "{logout:loggedoutfrom}"
msgstr "Uspješno ste odjavljeni iz %SP%."

msgid "{errors:errorreport_text}"
msgstr "Prijava greške poslana je administratorima."

msgid "{errors:descr_LOGOUTREQUEST}"
msgstr "Došlo je do greške prilikom obrade zahtjeva za odjavljivanjem."

msgid "{logout:success}"
msgstr "Uspješno ste se odjavili iz svih gore navedenih servisa."

msgid "{admin:cfg_check_notices}"
msgstr "Napomene"

msgid "{errors:descr_USERABORTED}"
msgstr "Korisnik je prekinuo proces autentifikacie"

msgid "{errors:descr_CASERROR}"
msgstr "Greška u komunikaciji sa CAS poslužiteljem."

msgid "{general:no}"
msgstr "Ne"

msgid "{admin:metadata_saml20-sp}"
msgstr "SAML 2.0 metapodaci o davatelju usluge"

msgid "{admin:metaconv_converted}"
msgstr "Pretvoreni metapodaci"

msgid "{logout:completed}"
msgstr "Završeno"

msgid "{errors:descr_NOTSET}"
msgstr ""
"Izvorna vrijednost administratorske zaporke (parametar "
"auth.adminpassword) u konfiguraciji nije promjenjena. Molimo promjenite "
"administratorsku zaporku u konfiguracijskoj datoteci."

msgid "{general:service_provider}"
msgstr "Davatelj usluge"

msgid "{errors:descr_BADREQUEST}"
msgstr "Dogodila se greška prilikom dohvaćanja ove stranice. Razlog: %REASON%"

msgid "{logout:no}"
msgstr "Ne"

msgid "{disco:icon_prefered_idp}"
msgstr "[Primarni odabir]"

msgid "{general:no_cancel}"
msgstr "Ne, odustani"

msgid "{login:user_pass_header}"
msgstr "Unesite korisničku oznaku i zaporku"

msgid "{errors:report_explain}"
msgstr "Opišite što ste radili kad se pojavila greška..."

msgid "{errors:title_ACSPARAMS}"
msgstr "Nije dostavljen nikakav SAML odgovor"

msgid "{errors:descr_SLOSERVICEPARAMS}"
msgstr ""
"Pristupili ste sučelju za odjavljivanje iz sustava jedinstvene "
"autentifikacije, ali niste dostavili SAML LogoutRequest ili "
"LogoutResponse poruku."

msgid "{login:organization}"
msgstr "Ustanova"

msgid "{errors:title_WRONGUSERPASS}"
msgstr "Neispravna korisnička oznaka ili zaporka"

msgid "{admin:metaover_required_not_found}"
msgstr "Nisu pronađena sljedeća obavezna polja"

msgid "{errors:descr_NOACCESS}"
msgstr ""
"Ova odredišna adresa nije omogućena. Provjerite dozvole u konfiguraciji "
"vaše instalacije programskog alata SimpleSAMLphp."

msgid "{errors:title_SLOSERVICEPARAMS}"
msgstr "Nije dostavljena nikakva SAML poruka"

msgid "{errors:descr_ACSPARAMS}"
msgstr ""
"Pristupili ste sučelju za obradu SAML potvrda, ali niste dostavili SAML "
"autentifikacijski odgovor."

msgid "{admin:debug_sending_message_text_link}"
msgstr "Kliknite na poveznicu \"Pošalji poruku\" da biste poslali poruku."

msgid "{errors:descr_AUTHSOURCEERROR}"
msgstr ""
"Došlo je do greške u autentifikacijskom modulu %AUTHSOURCE%. Razlog: "
"%REASON%"

msgid "{status:some_error_occurred}"
msgstr "Pojavila se greška"

msgid "{login:change_home_org_button}"
msgstr "Odaberite matičnu ustanovu"

msgid "{admin:cfg_check_superfluous}"
msgstr "Suvišni parametri u konfiguracijskoj datoteci"

msgid "{errors:report_email}"
msgstr "E-mail adresa:"

msgid "{errors:howto_header}"
msgstr "Kome se obratiti za pomoć"

msgid "{errors:title_NOTSET}"
msgstr "Zaporka nije postavljena"

msgid "{errors:descr_NORELAYSTATE}"
msgstr ""
"Aplikacija koja je inicirala ovaj zahtjev nije poslala RelayState "
"parametar koji sadrži adresu na koju treba preusmjeriti korisnikov web "
"preglednik nakon uspješne autentifikacije."

msgid "{status:header_diagnostics}"
msgstr "SimpleSAMLphp dijagnostika"

msgid "{status:intro}"
msgstr ""
"Ovo je stranica s prikazom aktualnog stanja Single Sign-On sjednice. Na "
"ovoj stranici možete vidjeti je li vam istekla sjednica, koliko će još "
"dugo vaša sjednica trajati te sve atribute koji su vezani uz vašu "
"sjednicu."

msgid "{errors:title_NOTFOUNDREASON}"
msgstr "Stranica nije pronađena"

msgid "{admin:debug_sending_message_title}"
msgstr "Šaljem poruku"

msgid "{errors:title_RESPONSESTATUSNOSUCCESS}"
msgstr "Autentifikacijski servis je prijavio grešku"

msgid "{admin:metadata_shib13-sp}"
msgstr "Shib 1.3 metapodaci o davatelju usluge"

msgid "{admin:metaover_intro}"
msgstr "Da biste vidjeli detalje o SAML entitetu, kliknite na njegovo zaglavlje."

msgid "{errors:title_NOTVALIDCERT}"
msgstr "Certifikat nije valjan"

msgid "{general:remember}"
msgstr "Zapamti moj odabir"

msgid "{disco:selectidp}"
msgstr "Odaberite autentifikacijski servis"

msgid "{login:help_desk_email}"
msgstr "Pošaljite e-mail službi za podršku korisnicima"

msgid "{login:help_desk_link}"
msgstr "Stranice službe za podršku korisnicima"

msgid "{errors:title_CASERROR}"
msgstr "CAS greška"

msgid "{login:user_pass_text}"
msgstr ""
"Aplikacija zahtjeva od vas da se autentificirate. Unesite vašu korisničku"
" oznaku i zaporku u dolje navedena polja."

msgid "{errors:title_DISCOPARAMS}"
msgstr "Lokacijskom servisu poslan je neispravan upit"

msgid "{general:yes_continue}"
msgstr "Da, nastavi"

msgid "{disco:remember}"
msgstr "Zapamti moj odabir"

msgid "{admin:metaover_group_metadata.saml20-sp-hosted}"
msgstr "SAML 2.0 davatelj usluge (lokalni)"

msgid "{admin:metadata_simplesamlformat}"
msgstr ""
"U SimpleSAMLphp formatu - koristite ovu opciju ako se na drugoj strani "
"također nalazi SimpleSAMLphp entitet:"

msgid "{disco:login_at}"
msgstr "Prijavi se kroz"

msgid "{errors:title_GENERATEAUTHNRESPONSE}"
msgstr "Ne mogu kreirati autentifikacijski odgovor"

msgid "{errors:errorreport_header}"
msgstr "Prijava greške poslana"

msgid "{errors:title_CREATEREQUEST}"
msgstr "Greška prilikom kreiranja zahtjeva"

msgid "{admin:metaover_header}"
msgstr "Pregled metapodataka"

msgid "{errors:report_submit}"
msgstr "Pošalji prijavu greške"

msgid "{errors:title_INVALIDCERT}"
msgstr "Neispravan digitalni certifikat"

msgid "{errors:title_NOTFOUND}"
msgstr "Stranica nije pronađena"

msgid "{logout:logged_out_text}"
msgstr "Uspješno ste se odjavili."

msgid "{admin:metaover_group_metadata.shib13-sp-hosted}"
msgstr "Shib 1.3 davatelj usluge (lokalni)"

msgid "{admin:metadata_cert_intro}"
msgstr "Preuzmite X509 certifikate u PEM formatu."

msgid "{admin:debug_sending_message_msg_title}"
msgstr "Poruka"

msgid "{errors:title_UNKNOWNCERT}"
msgstr "Nepoznat digitalni certifikat"

msgid "{errors:title_LDAPERROR}"
msgstr "LDAP greška"

msgid "{logout:failedsps}"
msgstr ""
"Odjavljivanje iz jednog ili više servisa nije uspjelo. Da biste bili "
"sigurni da su sve vaše sjednice završene, preporučamo da <i>zatvorite web"
" preglednik</i>."

msgid "{errors:descr_NOTFOUND}"
msgstr "Tražena stranica nije pronađena. Adresa stranice je: %URL%"

msgid "{errors:howto_text}"
msgstr ""
"Ova greška se vjerojatno javila zbog neočekivanog ponašanja ili "
"neispravne konfiguracije programskog alata SimpleSAMLphp. Kontaktirajte "
"administratore ovog servisa i pošaljite im gore navedenu poruku o greški."

msgid "{admin:metaover_group_metadata.shib13-idp-hosted}"
msgstr "Shib 1.3 autentifikacijski servis (lokalni)"

msgid "{errors:descr_NOTVALIDCERT}"
msgstr "Niste predočili valjani certifikat."

msgid "{admin:debug_sending_message_text_button}"
msgstr "Kliknite na gumb \"Pošalji poruku\" da biste poslali poruku."

msgid "{admin:metaover_optional_not_found}"
msgstr "Nisu pronađena sljedeća opcionalna polja"

msgid "{logout:logout_only}"
msgstr "Ne, samo iz %SP%"

msgid "{login:next}"
msgstr "Dalje"

msgid "{errors:descr_GENERATEAUTHNRESPONSE}"
msgstr ""
"Došlo je do greške prilikom kreiranja odgovora na autentifikacijski "
"zahtjev."

msgid "{disco:selectidp_full}"
msgstr "Molimo odaberite servis preko kojeg se želite autentificirati:"

msgid "{errors:descr_NOTFOUNDREASON}"
msgstr ""
"Tražena stranica nije pronađena. Razlog: %REASON% Adresa stranice je: "
"%URL%"

msgid "{errors:title_NOCERT}"
msgstr "Nema digitalnog certifikata"

msgid "{errors:title_LOGOUTINFOLOST}"
msgstr "Informacija o odjavljivanju je izgubljena"

msgid "{admin:metaover_group_metadata.shib13-idp-remote}"
msgstr "Shib 1.3 autentifikacijski servis (udaljeni)"

msgid "{errors:descr_CONFIG}"
msgstr "Čini se da je SimpleSAMLphp pogrešno iskonfiguriran."

msgid "{admin:metadata_intro}"
msgstr ""
"Ovo su metapodaci koje je SimpleSAMLphp izgenerirao za vas. Te "
"metapodatke možete poslati davateljima usluga ili elektroničkih "
"identiteta u koje imate povjerenja i s kojima želite uspostaviti "
"federaciju."

msgid "{admin:metadata_cert}"
msgstr "Certifikati"

msgid "{errors:descr_INVALIDCERT}"
msgstr ""
"Neuspješna autentifikacija: digitalni certifikat koji je poslao vaš web "
"preglednik nije ispravan ili se ne može pročitati"

msgid "{status:header_shib}"
msgstr "Shibboleth primjer"

msgid "{admin:metaconv_parse}"
msgstr "Analiziraj"

msgid "Person's principal name at home organization"
msgstr "Korisnička oznaka"

msgid "Superfluous options in config file"
msgstr "Suvišni parametri u konfiguracijskoj datoteci"

msgid "Mobile"
msgstr "Broj mobilnog telefona"

msgid "Shib 1.3 Service Provider (Hosted)"
msgstr "Shib 1.3 davatelj usluge (lokalni)"

msgid ""
"LDAP is the user database, and when you try to login, we need to contact "
"an LDAP database. An error occurred when we tried it this time."
msgstr ""
"Došlo je do greške prilikom spajanja na LDAP poslužitelj. Vaši podaci "
"pohranjeni su u LDAP imeniku i autentifikacijski servis se mora moći "
"spojiti na LDAP poslužitelj da bi provjerio ispravnost unesene korisničke"
" oznake i zaporke."

msgid ""
"Optionally enter your email address, for the administrators to be able "
"contact you for further questions about your issue:"
msgstr ""
"Ako želite, unesite svoju elektroničku adresu kako bi vas administratori "
"mogli kontaktirati u slučaju da su im potrebne dodatne informacije:"

msgid "Display name"
msgstr "Mrežno ime"

msgid "Remember my choice"
msgstr "Zapamti moj odabir"

msgid "SAML 2.0 SP Metadata"
msgstr "SAML 2.0 metapodaci o davatelju usluge"

msgid "Notices"
msgstr "Napomene"

msgid "Home telephone"
msgstr "Kućni telefonski broj"

msgid ""
"Hi, this is the status page of SimpleSAMLphp. Here you can see if your "
"session is timed out, how long it lasts until it times out and all the "
"attributes that are attached to your session."
msgstr ""
"Ovo je stranica s prikazom aktualnog stanja Single Sign-On sjednice. Na "
"ovoj stranici možete vidjeti je li vam istekla sjednica, koliko će još "
"dugo vaša sjednica trajati te sve atribute koji su vezani uz vašu "
"sjednicu."

msgid "Explain what you did when this error occurred..."
msgstr "Opišite što ste radili kad se pojavila greška..."

msgid "An unhandled exception was thrown."
msgstr "Pojavila se iznimka koja ne može do kraja biti obrađena."

msgid "Invalid certificate"
msgstr "Certifikat nije valjan"

msgid "Service Provider"
msgstr "Davatelj usluge"

msgid "Incorrect username or password."
msgstr "Neispravna korisnička oznaka ili zaporka."

msgid "There is an error in the request to this page. The reason was: %REASON%"
msgstr "Dogodila se greška prilikom dohvaćanja ove stranice. Razlog: %REASON%"

msgid "E-mail address:"
msgstr "E-mail adresa:"

msgid "Submit message"
msgstr "Pošalji poruku"

msgid "No RelayState"
msgstr "Parametar RelayState nije zadan"

msgid "Error creating request"
msgstr "Greška prilikom kreiranja zahtjeva"

msgid "Locality"
msgstr "Mjesto (lokalitet)"

msgid "Unhandled exception"
msgstr "Neobrađena iznimka"

msgid "The following required fields was not found"
msgstr "Nisu pronađena sljedeća obavezna polja"

msgid "Download the X509 certificates as PEM-encoded files."
msgstr "Preuzmite X509 certifikate u PEM formatu."

#, python-format
msgid "Unable to locate metadata for %ENTITYID%"
msgstr "Metapodaci za %ENTITYID% nisu pronađeni"

msgid "Organizational number"
msgstr "Brojčani identifikator ustanove"

msgid "Password not set"
msgstr "Zaporka nije postavljena"

msgid "SAML 2.0 IdP Metadata"
msgstr "SAML 2.0 metapodaci o autentifikacijskom servisu"

msgid "Post office box"
msgstr "Broj poštanskog pretinca"

msgid ""
"A service has requested you to authenticate yourself. Please enter your "
"username and password in the form below."
msgstr ""
"Aplikacija zahtjeva od vas da se autentificirate. Unesite vašu korisničku"
" oznaku i zaporku u dolje navedena polja."

msgid "CAS Error"
msgstr "CAS greška"

msgid ""
"The debug information below may be of interest to the administrator / "
"help desk:"
msgstr ""
"Sljedeće informacije mogu biti zanimljive administratorima ili službi za "
"podršku korisnicima:"

msgid ""
"Either no user with the given username could be found, or the password "
"you gave was wrong. Please check the username and try again."
msgstr ""
"Korisnik s navedenom korisničkom oznakom ne može biti pronađen ili je "
"zaporka koju ste unijeli neispravna. Molimo provjerite korisničku oznaku "
"i pokušajte ponovo."

msgid "Error"
msgstr "Greška"

msgid "Next"
msgstr "Dalje"

msgid "Distinguished name (DN) of the person's home organizational unit"
msgstr "Jedinstveni naziv (DN) korisnikove organizacijske jedinice"

msgid "State information lost"
msgstr "Podaci o stanju su izgubljeni"

msgid ""
"The password in the configuration (auth.adminpassword) is not changed "
"from the default value. Please edit the configuration file."
msgstr ""
"Izvorna vrijednost administratorske zaporke (parametar "
"auth.adminpassword) u konfiguraciji nije promjenjena. Molimo promjenite "
"administratorsku zaporku u konfiguracijskoj datoteci."

msgid "Converted metadata"
msgstr "Pretvoreni metapodaci"

msgid "Mail"
msgstr "Elektronička adresa"

msgid "No, cancel"
msgstr "Ne"

msgid ""
"You have chosen <b>%HOMEORG%</b> as your home organization. If this is "
"wrong you may choose another one."
msgstr ""
"Odabrali ste <b>%HOMEORG%</b> kao vašu matičnu ustanovu. Ako to nije "
"točno možete odabrati drugu ustanovu."

msgid "Error processing request from Service Provider"
msgstr "Greška prilikom obrade autentifikacijskog zahtjeva"

msgid "Distinguished name (DN) of person's primary Organizational Unit"
msgstr "Jedinstveni naziv (DN) korisnikove primarne organizacijske jedinice"

msgid ""
"To look at the details for an SAML entity, click on the SAML entity "
"header."
msgstr "Da biste vidjeli detalje o SAML entitetu, kliknite na njegovo zaglavlje."

msgid "Enter your username and password"
msgstr "Unesite korisničku oznaku i zaporku"

msgid "Login at"
msgstr "Prijavi se kroz"

msgid "No"
msgstr "Ne"

msgid "Home postal address"
msgstr "Kućna poštanska adresa"

msgid "WS-Fed SP Demo Example"
msgstr "Primjer WS-Fed davatelja usluge"

msgid "SAML 2.0 Identity Provider (Remote)"
msgstr "SAML 2.0 autentifikacijski servis (udaljeni)"

msgid "Error processing the Logout Request"
msgstr "Greška prilikom odjavljivanja"

msgid "Do you want to logout from all the services above?"
msgstr "Želite li se odjaviti iz svih gore navedenih servisa?"

msgid "Select"
msgstr "Odaberi"

msgid "The authentication was aborted by the user"
msgstr "Korisnik je prekinuo proces autentifikacie"

msgid "Your attributes"
msgstr "Vaši atributi"

msgid "Given name"
msgstr "Ime"

msgid "Identity assurance profile"
msgstr "Usklađenost sa standardima zaštite korisničkih podataka"

msgid "SAML 2.0 SP Demo Example"
msgstr "Primjer SAML 2.0 davatelja usluge"

msgid "Logout information lost"
msgstr "Informacija o odjavljivanju je izgubljena"

msgid "Organization name"
msgstr "Naziv matične ustanove"

msgid "Authentication failed: the certificate your browser sent is unknown"
msgstr ""
"Neuspješna autentifikacija: digitalni certifikat kojeg je poslao vaš web "
"preglednik je nepoznat"

msgid ""
"You are about to send a message. Hit the submit message button to "
"continue."
msgstr "Kliknite na gumb \"Pošalji poruku\" da biste poslali poruku."

msgid "Home organization domain name"
msgstr "Oznaka matične ustanove"

msgid "Go back to the file list"
msgstr "Vrati se natrag na popis datoteka"

msgid "Error report sent"
msgstr "Prijava greške poslana"

msgid "Common name"
msgstr "Ime i prezime"

msgid "Please select the identity provider where you want to authenticate:"
msgstr "Molimo odaberite servis preko kojeg se želite autentificirati:"

msgid "Logout failed"
msgstr "Odjava nije uspjela"

msgid "Identity number assigned by public authorities"
msgstr "Brojčani identifikator osobe"

msgid "WS-Federation Identity Provider (Remote)"
msgstr "WS-Federation autentifikacijski servis (udaljeni)"

msgid "Error received from Identity Provider"
msgstr "Autentifikacijski servis je prijavio grešku"

msgid "LDAP Error"
msgstr "LDAP greška"

msgid ""
"The information about the current logout operation has been lost. You "
"should return to the service you were trying to log out from and try to "
"log out again. This error can be caused by the logout information "
"expiring. The logout information is stored for a limited amout of time - "
"usually a number of hours. This is longer than any normal logout "
"operation should take, so this error may indicate some other error with "
"the configuration. If the problem persists, contact your service "
"provider."
msgstr ""
"Informacija o aktualnom zahtjevu za odjavljivanjem se izgubila. "
"Preporučamo da se vratite u aplikaciju iz koje ste se htjeli odjaviti i "
"pokušate se odjaviti ponovo. Ova greška može biti uzrokovana istekom "
"valjanosti zahtjeva za odjavom. Zahtjev se pohranjuje određeno vrijeme - "
"u pravilu nekoliko sati. Obzirom da je to dulje nego što bi bilo koja "
"operacija odjavljivanja trebala trajati, greška koja se pojavila može "
"upućivati na grešku u konfiguraciji. Ako se problem nastavi, "
"kontaktirajte administratora aplikacije. "

msgid "Some error occurred"
msgstr "Pojavila se greška"

msgid "Organization"
msgstr "Ustanova"

msgid "No certificate"
msgstr "Nema digitalnog certifikata"

msgid "Choose home organization"
msgstr "Odaberite matičnu ustanovu"

msgid "Persistent pseudonymous ID"
msgstr "Trajni anonimni identifikator"

msgid "No SAML response provided"
msgstr "Nije dostavljen nikakav SAML odgovor"

msgid "No errors found."
msgstr "Nije pronađena niti jedna greška."

msgid "SAML 2.0 Service Provider (Hosted)"
msgstr "SAML 2.0 davatelj usluge (lokalni)"

msgid "The given page was not found. The URL was: %URL%"
msgstr "Tražena stranica nije pronađena. Adresa stranice je: %URL%"

msgid "Configuration error"
msgstr "Greška u konfiguraciji"

msgid "Required fields"
msgstr "Obavezna polja"

msgid "An error occurred when trying to create the SAML request."
msgstr "Pojavila se greška prilikom kreiranja SAML zahtjeva."

msgid ""
"This error probably is due to some unexpected behaviour or to "
"misconfiguration of SimpleSAMLphp. Contact the administrator of this "
"login service, and send them the error message above."
msgstr ""
"Ova greška se vjerojatno javila zbog neočekivanog ponašanja ili "
"neispravne konfiguracije programskog alata SimpleSAMLphp. Kontaktirajte "
"administratore ovog servisa i pošaljite im gore navedenu poruku o greški."

#, python-format
msgid "Your session is valid for %remaining% seconds from now."
msgstr "Vaša sjednica bit će valjana još %remaining% sekundi."

msgid "Domain component (DC)"
msgstr "Domenska komponenta (DC)"

msgid "Shib 1.3 Service Provider (Remote)"
msgstr "Shib 1.3 davatelj usluge (udaljeni)"

msgid "Password"
msgstr "Zaporka"

msgid "Nickname"
msgstr "Nadimak"

msgid "Send error report"
msgstr "Pošalji prijavu greške"

msgid ""
"Authentication failed: the certificate your browser sent is invalid or "
"cannot be read"
msgstr ""
"Neuspješna autentifikacija: digitalni certifikat koji je poslao vaš web "
"preglednik nije ispravan ili se ne može pročitati"

msgid "The error report has been sent to the administrators."
msgstr "Prijava greške poslana je administratorima."

msgid "Date of birth"
msgstr "Datum rođenja"

msgid "Private information elements"
msgstr "Postavke privatnosti"

msgid "You are also logged in on these services:"
msgstr "Također ste prijavljeni u sljedećim servisima:"

msgid "SimpleSAMLphp Diagnostics"
msgstr "SimpleSAMLphp dijagnostika"

msgid "Debug information"
msgstr "Informacije o greški"

msgid "No, only %SP%"
msgstr "Ne, samo iz %SP%"

msgid "Username"
msgstr "Korisnička oznaka"

msgid "Go back to SimpleSAMLphp installation page"
msgstr "Natrag na početnu stranicu SimpleSAMLphp instalacije"

msgid "You have successfully logged out from all services listed above."
msgstr "Uspješno ste se odjavili iz svih gore navedenih servisa."

msgid "You are now successfully logged out from %SP%."
msgstr "Uspješno ste odjavljeni iz %SP%."

msgid "Affiliation"
msgstr "Povezanost s ustanovom"

msgid "You have been logged out."
msgstr "Uspješno ste se odjavili."

msgid "Return to service"
msgstr "Povratak u aplikaciju"

msgid "Logout"
msgstr "Odjava"

msgid "State information lost, and no way to restart the request"
msgstr "Podaci o stanju su izgubljeni i zahtjev se ne može reproducirati"

msgid "Error processing response from Identity Provider"
msgstr "Greška prilikom obrade odgovora pristiglog od autentifikacijskog servisa"

msgid "WS-Federation Service Provider (Hosted)"
msgstr "WS-Federation davatelj usluge (lokalni)"

msgid "Preferred language"
msgstr "Primarni jezik"

msgid "SAML 2.0 Service Provider (Remote)"
msgstr "SAML 2.0 davatelj usluge (udaljeni)"

msgid "Surname"
msgstr "Prezime"

msgid "No access"
msgstr "Pristup nije dozvoljen"

msgid "The following fields was not recognized"
msgstr "Sljedeća polja nisu prepoznata"

msgid "Authentication error in source %AUTHSOURCE%. The reason was: %REASON%"
msgstr ""
"Došlo je do greške u autentifikacijskom modulu %AUTHSOURCE%. Razlog: "
"%REASON%"

msgid "Bad request received"
msgstr "Dobiveni zahtjev nije ispravan"

msgid "User ID"
msgstr "Identifikator korisnika u ustanovi"

msgid "JPEG Photo"
msgstr "Slika u JPEG formatu"

msgid "Postal address"
msgstr "Poštanska adresa"

msgid "An error occurred when trying to process the Logout Request."
msgstr "Došlo je do greške prilikom obrade zahtjeva za odjavljivanjem."

msgid "Sending message"
msgstr "Šaljem poruku"

msgid "In SAML 2.0 Metadata XML format:"
msgstr "Metapodaci u SAML 2.0 XML formatu:"

msgid "Logging out of the following services:"
msgstr "Odjavljujete se iz sljedećih servisa:"

msgid ""
"When this identity provider tried to create an authentication response, "
"an error occurred."
msgstr ""
"Došlo je do greške prilikom kreiranja odgovora na autentifikacijski "
"zahtjev."

msgid "Could not create authentication response"
msgstr "Ne mogu kreirati autentifikacijski odgovor"

msgid "Labeled URI"
msgstr "URI adresa"

msgid "SimpleSAMLphp appears to be misconfigured."
msgstr "Čini se da je SimpleSAMLphp pogrešno iskonfiguriran."

msgid "Shib 1.3 Identity Provider (Hosted)"
msgstr "Shib 1.3 autentifikacijski servis (lokalni)"

msgid "Metadata"
msgstr "Metapodaci"

msgid "Login"
msgstr "Prijavi se"

msgid ""
"This Identity Provider received an Authentication Request from a Service "
"Provider, but an error occurred when trying to process the request."
msgstr ""
"Autentifikacijski servis je dobio zahtjev od davatelja usluge, ali se "
"pojavila greška prilikom obrade zahtjeva."

msgid "Yes, all services"
msgstr "Da, iz svih servisa"

msgid "Logged out"
msgstr "Odjavljeni ste"

msgid "Postal code"
msgstr "Broj pošte"

msgid "Logging out..."
msgstr "Odjava u tijeku..."

msgid "Metadata not found"
msgstr "Metapodaci nisu pronađeni"

msgid "SAML 2.0 Identity Provider (Hosted)"
msgstr "SAML 2.0 autentifikacijski servis (lokalni)"

msgid "Primary affiliation"
msgstr "Temeljna povezanost s ustanovom"

msgid ""
"If you report this error, please also report this tracking number which "
"makes it possible to locate your session in the logs available to the "
"system administrator:"
msgstr ""
"Molimo da prilikom prijavljivanja greške pošaljete i ovaj identifikator "
"koji će administratorima omogućiti pronalaženje dodatnih informacija u "
"dnevničkim zapisima:"

msgid "XML metadata"
msgstr "Metapodaci u XML formatu"

msgid ""
"The parameters sent to the discovery service were not according to "
"specifications."
msgstr "Parametri poslani lokacijskom servisu nisu u ispravnom formatu."

msgid "Telephone number"
msgstr "Broj telefona"

msgid ""
"Unable to log out of one or more services. To ensure that all your "
"sessions are closed, you are encouraged to <i>close your webbrowser</i>."
msgstr ""
"Odjavljivanje iz jednog ili više servisa nije uspjelo. Da biste bili "
"sigurni da su sve vaše sjednice završene, preporučamo da <i>zatvorite web"
" preglednik</i>."

msgid "Bad request to discovery service"
msgstr "Lokacijskom servisu poslan je neispravan upit"

msgid "Select your identity provider"
msgstr "Odaberite autentifikacijski servis"

msgid "Entitlement regarding the service"
msgstr "Pripadnost grupi"

msgid "Shib 1.3 SP Metadata"
msgstr "Shib 1.3 metapodaci o davatelju usluge"

msgid ""
"As you are in debug mode, you get to see the content of the message you "
"are sending:"
msgstr ""
"Obzirom da ste u modu za otkrivanje grešaka, imate mogućnost vidjeti "
"sadržaj poruke koju šaljete:"

msgid "Certificates"
msgstr "Certifikati"

msgid "Remember"
msgstr "Zapamti moj odabir"

msgid "Distinguished name (DN) of person's home organization"
msgstr "Jedinstveni naziv (DN) korisnikove matične ustanove"

msgid "You are about to send a message. Hit the submit message link to continue."
msgstr "Kliknite na poveznicu \"Pošalji poruku\" da biste poslali poruku."

msgid "Organizational unit"
msgstr "Organizacijska jedinica"

msgid "Authentication aborted"
msgstr "Proces autentifikacije je prekinut"

msgid "Local identity number"
msgstr "Lokalni brojčani identifikator osobe u ustanovi (LOCAL_NO)"

msgid "Report errors"
msgstr "Prijavi grešku"

msgid "Page not found"
msgstr "Stranica nije pronađena"

msgid "Shib 1.3 IdP Metadata"
msgstr "Shib 1.3 metapodaci o autentifikacijskom servisu"

msgid "Change your home organization"
msgstr "Promjenite odabir vaše matične ustanove"

msgid "User's password hash"
msgstr "Kriptirana zaporka"

msgid ""
"In SimpleSAMLphp flat file format - use this if you are using a "
"SimpleSAMLphp entity on the other side:"
msgstr ""
"U SimpleSAMLphp formatu - koristite ovu opciju ako se na drugoj strani "
"također nalazi SimpleSAMLphp entitet:"

msgid "Yes, continue"
msgstr "Da, nastavi"

msgid "Completed"
msgstr "Završeno"

msgid ""
"The Identity Provider responded with an error. (The status code in the "
"SAML Response was not success)"
msgstr ""
"Autentifikacijski servis je poslao odgovor koji sadrži informaciju o "
"pojavi greške. (Šifra statusa dostavljena u SAML odgovoru ne odgovara "
"šifri uspješno obrađenog zahtjeva)"

msgid "Error loading metadata"
msgstr "Greška prilikom učitavanja metapodataka"

msgid "Select configuration file to check:"
msgstr "Odaberite konfiguracijsku datoteku koju želite provjeriti:"

msgid "On hold"
msgstr "Na čekanju"

msgid "Error when communicating with the CAS server."
msgstr "Greška u komunikaciji sa CAS poslužiteljem."

msgid "No SAML message provided"
msgstr "Nije dostavljena nikakva SAML poruka"

msgid "Help! I don't remember my password."
msgstr "Upomoć! Zaboravio/la sam svoju zaporku."

msgid ""
"You can turn off debug mode in the global SimpleSAMLphp configuration "
"file <tt>config/config.php</tt>."
msgstr ""
"Mod za otkrivanje grešaka možete isključiti u glavnoj SimpleSAMLphp "
"konfiguracijskoj datoteci <tt>config/config.php</tt>. "

msgid "How to get help"
msgstr "Kome se obratiti za pomoć"

msgid ""
"You accessed the SingleLogoutService interface, but did not provide a "
"SAML LogoutRequest or LogoutResponse. Please note that this endpoint is "
"not intended to be accessed directly."
msgstr ""
"Pristupili ste sučelju za odjavljivanje iz sustava jedinstvene "
"autentifikacije, ali niste dostavili SAML LogoutRequest ili "
"LogoutResponse poruku."

msgid "SimpleSAMLphp error"
msgstr "SimpleSAMLphp greška"

msgid ""
"One or more of the services you are logged into <i>do not support "
"logout</i>. To ensure that all your sessions are closed, you are "
"encouraged to <i>close your webbrowser</i>."
msgstr ""
"Jedan ili više servisa na koje ste prijavljeni <i>ne podržava "
"odjavljivanje</i>. Da biste bili sigurni da su sve vaše sjednice "
"završene, preporučamo da <i>zatvorite web preglednik</i>."

msgid "Organization's legal name"
msgstr "Službeni naziv ustanove"

msgid "Options missing from config file"
msgstr "Parametri koji nedostaju u konfiguracijskoj datoteci"

msgid "The following optional fields was not found"
msgstr "Nisu pronađena sljedeća opcionalna polja"

msgid "Authentication failed: your browser did not send any certificate"
msgstr ""
"Neuspješna autentifikacija: vaš web preglednik nije poslao digitalni "
"certifikat"

msgid ""
"This endpoint is not enabled. Check the enable options in your "
"configuration of SimpleSAMLphp."
msgstr ""
"Ova odredišna adresa nije omogućena. Provjerite dozvole u konfiguraciji "
"vaše instalacije programskog alata SimpleSAMLphp."

msgid "You can <a href=\"%METAURL%\">get the metadata xml on a dedicated URL</a>:"
msgstr "Metapodaci su dostupni na <a href=\"%METAURL%\">ovoj adresi</a>:"

msgid "Street"
msgstr "Ulica"

msgid ""
"There is some misconfiguration of your SimpleSAMLphp installation. If you"
" are the administrator of this service, you should make sure your "
"metadata configuration is correctly setup."
msgstr ""
"Programski alat SimpleSAMLphp je pogrešno iskonfiguriran. Ako ste "
"administrator ovog servisa, provjerite jesu li svi metapodaci u "
"konfiguraciji ispravni."

msgid "Incorrect username or password"
msgstr "Neispravna korisnička oznaka ili zaporka"

msgid "Message"
msgstr "Poruka"

msgid "Contact information:"
msgstr "Kontakt podaci:"

msgid "Unknown certificate"
msgstr "Nepoznat digitalni certifikat"

msgid "Legal name"
msgstr "Službeni naziv"

msgid "Optional fields"
msgstr "Opcionalna polja"

msgid ""
"The initiator of this request did not provide a RelayState parameter "
"indicating where to go next."
msgstr ""
"Aplikacija koja je inicirala ovaj zahtjev nije poslala RelayState "
"parametar koji sadrži adresu na koju treba preusmjeriti korisnikov web "
"preglednik nakon uspješne autentifikacije."

msgid "You have previously chosen to authenticate at"
msgstr "Prethodno ste odabrali autentifikaciju kroz"

msgid ""
"You sent something to the login page, but for some reason the password "
"was not sent. Try again please."
msgstr ""
"Iz nekog razloga autentifikacijskom servisu nije proslijeđena vaša "
"zaporka. Molimo pokušajte ponovo."

msgid "Fax number"
msgstr "Broj telefaksa"

msgid "Shibboleth demo"
msgstr "Shibboleth primjer"

msgid "Error in this metadata entry"
msgstr "Ovaj zapis metapodataka sadrži grešku"

msgid "Session size: %SIZE%"
msgstr "Veličina sjednice: %SIZE%"

msgid "Parse"
msgstr "Analiziraj"

msgid ""
"Without your username and password you cannot authenticate "
"yourself for access to the service. There may be someone that can help "
"you. Consult the help desk at your organization!"
msgstr ""
"Šteta! - Bez ispravne korisničke oznake i zaporke ne možete pristupiti "
"aplikaciji. Da biste saznali vašu zaporku kontaktirajte administratora "
"elektroničkog (LDAP) imenika vaše ustanove."

msgid "Metadata parser"
msgstr "Analizator metapodataka"

msgid "Choose your home organization"
msgstr "Odaberite vašu matičnu ustanovu"

msgid "Send e-mail to help desk"
msgstr "Pošaljite e-mail službi za podršku korisnicima"

msgid "Metadata overview"
msgstr "Pregled metapodataka"

msgid "Title"
msgstr "Naziv"

msgid "Manager"
msgstr "Voditelj"

msgid "You did not present a valid certificate."
msgstr "Niste predočili valjani certifikat."

msgid "Authentication source error"
msgstr "Greška u autentifikacijskom modulu"

msgid "Affiliation at home organization"
msgstr "Povezanost s matičnom ustanovom"

msgid "Help desk homepage"
msgstr "Stranice službe za podršku korisnicima"

msgid "Configuration check"
msgstr "Provjera konfiguracije"

msgid "We did not accept the response sent from the Identity Provider."
msgstr "Nije prihvaćen odgovor koji je poslao autentifikacijski servis."

msgid "The given page was not found. The reason was: %REASON%  The URL was: %URL%"
msgstr ""
"Tražena stranica nije pronađena. Razlog: %REASON% Adresa stranice je: "
"%URL%"

msgid "Shib 1.3 Identity Provider (Remote)"
msgstr "Shib 1.3 autentifikacijski servis (udaljeni)"

msgid ""
"Here is the metadata that SimpleSAMLphp has generated for you. You may "
"send this metadata document to trusted partners to setup a trusted "
"federation."
msgstr ""
"Ovo su metapodaci koje je SimpleSAMLphp izgenerirao za vas. Te "
"metapodatke možete poslati davateljima usluga ili elektroničkih "
"identiteta u koje imate povjerenja i s kojima želite uspostaviti "
"federaciju."

msgid "[Preferred choice]"
msgstr "[Primarni odabir]"

msgid "Organizational homepage"
msgstr "Web stranice ustanove"

msgid ""
"You accessed the Assertion Consumer Service interface, but did not "
"provide a SAML Authentication Response. Please note that this endpoint is"
" not intended to be accessed directly."
msgstr ""
"Pristupili ste sučelju za obradu SAML potvrda, ali niste dostavili SAML "
"autentifikacijski odgovor."


msgid ""
"You are now accessing a pre-production system. This authentication setup "
"is for testing and pre-production verification only. If someone sent you "
"a link that pointed you here, and you are not <i>a tester</i> you "
"probably got the wrong link, and should <b>not be here</b>."
msgstr ""
"Pristupate sustavu koji se nalazi u pretprodukcijskoj fazi. Ove "
"autentifikacijske postavke služe za testiranje i provjeru ispravnosti "
"rada pretprodukcijskog sustava. Ako vam je netko poslao adresu koja "
"pokazuje na ovu stranicu, a vi niste <i>osoba zadužena za testiranje</i>,"
" vjerojatno ste <b>na ovu stranicu došli greškom</b>."

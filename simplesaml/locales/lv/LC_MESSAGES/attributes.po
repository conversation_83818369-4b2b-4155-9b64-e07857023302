msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 0 || n % 100 >= 11 && n % 100 <= 19) ? 0 : ((n % 10 == 1 && n % 100 != 11) ? 1 : 2);\n"

msgid "aRecord"
msgstr "aRecord"

msgid "urn:mace:dir:attribute-def:aRecord"
msgstr "urn:mace:dir:attribute-def:aRecord"

msgid "urn:oid:0.9.2342.********.100.1.26"
msgstr "urn:oid:0.9.2342.********.100.1.26"

msgid "aliasedEntryName"
msgstr "aliasedEntryName"

msgid "urn:mace:dir:attribute-def:aliasedEntryName"
msgstr "urn:mace:dir:attribute-def:aliasedEntryName"

msgid "urn:oid:2.5.4.1"
msgstr "urn:oid:2.5.4.1"

msgid "aliasedObjectName"
msgstr "aliasedObjectName"

msgid "urn:mace:dir:attribute-def:aliasedObjectName"
msgstr "urn:mace:dir:attribute-def:aliasedObjectName"

msgid "associatedDomain"
msgstr "associatedDomain"

msgid "urn:mace:dir:attribute-def:associatedDomain"
msgstr "urn:mace:dir:attribute-def:associatedDomain"

msgid "urn:oid:0.9.2342.********.100.1.37"
msgstr "urn:oid:0.9.2342.********.100.1.37"

msgid "associatedName"
msgstr "associatedName"

msgid "urn:mace:dir:attribute-def:associatedName"
msgstr "urn:mace:dir:attribute-def:associatedName"

msgid "urn:oid:0.9.2342.********.100.1.38"
msgstr "urn:oid:0.9.2342.********.100.1.38"

msgid "audio"
msgstr "audio"

msgid "urn:mace:dir:attribute-def:audio"
msgstr "urn:mace:dir:attribute-def:audio"

msgid "urn:oid:0.9.2342.********.100.1.55"
msgstr "urn:oid:0.9.2342.********.100.1.55"

msgid "authorityRevocationList"
msgstr "authorityRevocationList"

msgid "urn:mace:dir:attribute-def:authorityRevocationList"
msgstr "urn:mace:dir:attribute-def:authorityRevocationList"

msgid "urn:oid:2.5.4.38"
msgstr "urn:oid:2.5.4.38"

msgid "buildingName"
msgstr "buildingName"

msgid "urn:mace:dir:attribute-def:buildingName"
msgstr "urn:mace:dir:attribute-def:buildingName"

msgid "urn:oid:0.9.2342.********.100.1.48"
msgstr "urn:oid:0.9.2342.********.100.1.48"

msgid "businessCategory"
msgstr "businessCategory"

msgid "urn:mace:dir:attribute-def:businessCategory"
msgstr "urn:mace:dir:attribute-def:businessCategory"

msgid "urn:oid:2.5.4.15"
msgstr "urn:oid:2.5.4.15"

msgid "c"
msgstr "c"

msgid "urn:mace:dir:attribute-def:c"
msgstr "urn:mace:dir:attribute-def:c"

msgid "urn:oid:2.5.4.6"
msgstr "urn:oid:2.5.4.6"

msgid "cACertificate"
msgstr "cACertificate"

msgid "urn:mace:dir:attribute-def:cACertificate"
msgstr "urn:mace:dir:attribute-def:cACertificate"

msgid "urn:oid:2.5.4.37"
msgstr "urn:oid:2.5.4.37"

msgid "cNAMERecord"
msgstr "cNAMERecord"

msgid "urn:mace:dir:attribute-def:cNAMERecord"
msgstr "urn:mace:dir:attribute-def:cNAMERecord"

msgid "urn:oid:0.9.2342.********.100.1.31"
msgstr "urn:oid:0.9.2342.********.100.1.31"

msgid "carLicense"
msgstr "carLicense"

msgid "urn:mace:dir:attribute-def:carLicense"
msgstr "urn:mace:dir:attribute-def:carLicense"

msgid "urn:oid:2.16.840.1.113730.3.1.1"
msgstr "urn:oid:2.16.840.1.113730.3.1.1"

msgid "certificateRevocationList"
msgstr "certificateRevocationList"

msgid "urn:mace:dir:attribute-def:certificateRevocationList"
msgstr "urn:mace:dir:attribute-def:certificateRevocationList"

msgid "urn:oid:2.5.4.39"
msgstr "urn:oid:2.5.4.39"

# English string: Common name
msgid "cn"
msgstr "Vārds"

# English string: Common name
msgid "urn:mace:dir:attribute-def:cn"
msgstr "Vārds"

# English string: Common name
msgid "urn:oid:2.5.4.3"
msgstr "Vārds"

msgid "co"
msgstr "co"

msgid "urn:mace:dir:attribute-def:co"
msgstr "urn:mace:dir:attribute-def:co"

msgid "urn:oid:0.9.2342.********.100.1.43"
msgstr "urn:oid:0.9.2342.********.100.1.43"

msgid "commonName"
msgstr "commonName"

msgid "urn:mace:dir:attribute-def:commonName"
msgstr "urn:mace:dir:attribute-def:commonName"

msgid "countryName"
msgstr "countryName"

msgid "urn:mace:dir:attribute-def:countryName"
msgstr "urn:mace:dir:attribute-def:countryName"

msgid "crossCertificatePair"
msgstr "crossCertificatePair"

msgid "urn:mace:dir:attribute-def:crossCertificatePair"
msgstr "urn:mace:dir:attribute-def:crossCertificatePair"

msgid "urn:oid:2.5.4.40"
msgstr "urn:oid:2.5.4.40"

msgid "dITRedirect"
msgstr "dITRedirect"

msgid "urn:mace:dir:attribute-def:dITRedirect"
msgstr "urn:mace:dir:attribute-def:dITRedirect"

msgid "urn:oid:0.9.2342.********.100.1.54"
msgstr "urn:oid:0.9.2342.********.100.1.54"

msgid "dSAQuality"
msgstr "dSAQuality"

msgid "urn:mace:dir:attribute-def:dSAQuality"
msgstr "urn:mace:dir:attribute-def:dSAQuality"

msgid "urn:oid:0.9.2342.********.100.1.49"
msgstr "urn:oid:0.9.2342.********.100.1.49"

# English string: Domain component (DC)
msgid "dc"
msgstr "Domēns (DC)"

# English string: Domain component (DC)
msgid "urn:mace:dir:attribute-def:dc"
msgstr "Domēns (DC)"

# English string: Domain component (DC)
msgid "urn:oid:0.9.2342.********.100.1.25"
msgstr "Domēns (DC)"

msgid "deltaRevocationList"
msgstr "deltaRevocationList"

msgid "urn:mace:dir:attribute-def:deltaRevocationList"
msgstr "urn:mace:dir:attribute-def:deltaRevocationList"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "departmentNumber"
msgstr "departmentNumber"

msgid "urn:mace:dir:attribute-def:departmentNumber"
msgstr "urn:mace:dir:attribute-def:departmentNumber"

msgid "urn:oid:2.16.840.1.113730.3.1.2"
msgstr "urn:oid:2.16.840.1.113730.3.1.2"

msgid "description"
msgstr "description"

msgid "urn:mace:dir:attribute-def:description"
msgstr "urn:mace:dir:attribute-def:description"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "destinationIndicator"
msgstr "destinationIndicator"

msgid "urn:mace:dir:attribute-def:destinationIndicator"
msgstr "urn:mace:dir:attribute-def:destinationIndicator"

msgid "urn:oid:********"
msgstr "urn:oid:********"

# English string: Display name
msgid "displayName"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "urn:mace:dir:attribute-def:displayName"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "urn:oid:2.16.840.1.113730.3.1.241"
msgstr "Parādāmais vārds"

msgid "distinguishedName"
msgstr "distinguishedName"

msgid "urn:mace:dir:attribute-def:distinguishedName"
msgstr "urn:mace:dir:attribute-def:distinguishedName"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "dmdName"
msgstr "dmdName"

msgid "urn:mace:dir:attribute-def:dmdName"
msgstr "urn:mace:dir:attribute-def:dmdName"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "dnQualifier"
msgstr "dnQualifier"

msgid "urn:mace:dir:attribute-def:dnQualifier"
msgstr "urn:mace:dir:attribute-def:dnQualifier"

msgid "urn:oid:2.5.4.46"
msgstr "urn:oid:2.5.4.46"

msgid "documentAuthor"
msgstr "documentAuthor"

msgid "urn:mace:dir:attribute-def:documentAuthor"
msgstr "urn:mace:dir:attribute-def:documentAuthor"

msgid "urn:oid:0.9.2342.********.100.1.14"
msgstr "urn:oid:0.9.2342.********.100.1.14"

msgid "documentIdentifier"
msgstr "documentIdentifier"

msgid "urn:mace:dir:attribute-def:documentIdentifier"
msgstr "urn:mace:dir:attribute-def:documentIdentifier"

msgid "urn:oid:0.9.2342.********.100.1.11"
msgstr "urn:oid:0.9.2342.********.100.1.11"

msgid "documentLocation"
msgstr "documentLocation"

msgid "urn:mace:dir:attribute-def:documentLocation"
msgstr "urn:mace:dir:attribute-def:documentLocation"

msgid "urn:oid:0.9.2342.********.100.1.15"
msgstr "urn:oid:0.9.2342.********.100.1.15"

msgid "documentPublisher"
msgstr "documentPublisher"

msgid "urn:mace:dir:attribute-def:documentPublisher"
msgstr "urn:mace:dir:attribute-def:documentPublisher"

msgid "urn:oid:0.9.2342.********.100.1.56"
msgstr "urn:oid:0.9.2342.********.100.1.56"

msgid "documentTitle"
msgstr "documentTitle"

msgid "urn:mace:dir:attribute-def:documentTitle"
msgstr "urn:mace:dir:attribute-def:documentTitle"

msgid "urn:oid:0.9.2342.********.100.1.12"
msgstr "urn:oid:0.9.2342.********.100.1.12"

msgid "documentVersion"
msgstr "documentVersion"

msgid "urn:mace:dir:attribute-def:documentVersion"
msgstr "urn:mace:dir:attribute-def:documentVersion"

msgid "urn:oid:0.9.2342.********.100.1.13"
msgstr "urn:oid:0.9.2342.********.100.1.13"

msgid "domainComponent"
msgstr "domainComponent"

msgid "urn:mace:dir:attribute-def:domainComponent"
msgstr "urn:mace:dir:attribute-def:domainComponent"

msgid "drink"
msgstr "drink"

msgid "urn:mace:dir:attribute-def:drink"
msgstr "urn:mace:dir:attribute-def:drink"

msgid "urn:oid:0.9.2342.********.100.1.5"
msgstr "urn:oid:0.9.2342.********.100.1.5"

# English string: Organizational homepage
msgid "eduOrgHomePageURI"
msgstr "Organizācijas mājas lapa"

# English string: Organizational homepage
msgid "urn:mace:dir:attribute-def:eduOrgHomePageURI"
msgstr "Organizācijas mājas lapa"

# English string: Organizational homepage
msgid "urn:oid:*******.4.1.5923.1.2.1.2"
msgstr "Organizācijas mājas lapa"

msgid "eduOrgIdentityAuthNPolicyURI"
msgstr "eduOrgIdentityAuthNPolicyURI"

msgid "urn:mace:dir:attribute-def:eduOrgIdentityAuthNPolicyURI"
msgstr "urn:mace:dir:attribute-def:eduOrgIdentityAuthNPolicyURI"

msgid "urn:oid:*******.4.1.5923.1.2.1.3"
msgstr "urn:oid:*******.4.1.5923.1.2.1.3"

# English string: Organization's legal name
msgid "eduOrgLegalName"
msgstr "Organizācijas juridiskais nosaukums"

# English string: Organization's legal name
msgid "urn:mace:dir:attribute-def:eduOrgLegalName"
msgstr "Organizācijas juridiskais nosaukums"

# English string: Organization's legal name
msgid "urn:oid:*******.4.1.5923.1.2.1.4"
msgstr "Organizācijas juridiskais nosaukums"

msgid "eduOrgSuperiorURI"
msgstr "eduOrgSuperiorURI"

msgid "urn:mace:dir:attribute-def:eduOrgSuperiorURI"
msgstr "urn:mace:dir:attribute-def:eduOrgSuperiorURI"

msgid "urn:oid:*******.4.1.5923.1.2.1.5"
msgstr "urn:oid:*******.4.1.5923.1.2.1.5"

msgid "eduOrgWhitePagesURI"
msgstr "eduOrgWhitePagesURI"

msgid "urn:mace:dir:attribute-def:eduOrgWhitePagesURI"
msgstr "urn:mace:dir:attribute-def:eduOrgWhitePagesURI"

msgid "urn:oid:*******.4.1.5923.1.2.1.6"
msgstr "urn:oid:*******.4.1.5923.1.2.1.6"

# English string: Affiliation
msgid "eduPersonAffiliation"
msgstr "Piederība"

# English string: Affiliation
msgid "urn:mace:dir:attribute-def:eduPersonAffiliation"
msgstr "Piederība"

# English string: Affiliation
msgid "urn:oid:*******.4.1.5923.1.1.1.1"
msgstr "Piederība"

# English string: Identity assurance profile
msgid "eduPersonAssurance"
msgstr "Apraksts, kā atšķirt cilvēku no robota"

# English string: Identity assurance profile
msgid "urn:mace:dir:attribute-def:eduPersonAssurance"
msgstr "Apraksts, kā atšķirt cilvēku no robota"

# English string: Identity assurance profile
msgid "urn:oid:*******.4.1.5923.1.1.1.11"
msgstr "Apraksts, kā atšķirt cilvēku no robota"

# English string: Entitlement regarding the service
msgid "eduPersonEntitlement"
msgstr "Pilnvaras attiecībā uz servisu"

# English string: Entitlement regarding the service
msgid "urn:mace:dir:attribute-def:eduPersonEntitlement"
msgstr "Pilnvaras attiecībā uz servisu"

# English string: Entitlement regarding the service
msgid "urn:oid:*******.4.1.5923.1.1.1.7"
msgstr "Pilnvaras attiecībā uz servisu"

# English string: Nickname
msgid "eduPersonNickname"
msgstr "Niks"

# English string: Nickname
msgid "urn:mace:dir:attribute-def:eduPersonNickname"
msgstr "Niks"

# English string: Nickname
msgid "urn:oid:*******.4.1.5923.1.1.1.2"
msgstr "Niks"

# English string: Distinguished name (DN) of person's home organization
msgid "eduPersonOrgDN"
msgstr "Organizācijas vārds (DN)"

# English string: Distinguished name (DN) of person's home organization
msgid "urn:mace:dir:attribute-def:eduPersonOrgDN"
msgstr "Organizācijas vārds (DN)"

# English string: Distinguished name (DN) of person's home organization
msgid "urn:oid:*******.4.1.5923.1.1.1.3"
msgstr "Organizācijas vārds (DN)"

# English string: Distinguished name (DN) of the person's home organizational unit
msgid "eduPersonOrgUnitDN"
msgstr "Organizācijas vienības vārds (DN)"

# English string: Distinguished name (DN) of the person's home organizational unit
msgid "urn:mace:dir:attribute-def:eduPersonOrgUnitDN"
msgstr "Organizācijas vienības vārds (DN)"

# English string: Distinguished name (DN) of the person's home organizational unit
msgid "urn:oid:*******.4.1.5923.1.1.1.4"
msgstr "Organizācijas vienības vārds (DN)"

# English string: Primary affiliation
msgid "eduPersonPrimaryAffiliation"
msgstr "Pamatdarba amats"

# English string: Primary affiliation
msgid "urn:mace:dir:attribute-def:eduPersonPrimaryAffiliation"
msgstr "Pamatdarba amats"

# English string: Primary affiliation
msgid "urn:oid:*******.4.1.5923.1.1.1.5"
msgstr "Pamatdarba amats"

# English string: Distinguished name (DN) of person's primary Organizational Unit
msgid "eduPersonPrimaryOrgUnitDN"
msgstr "Personas pamata organizācijas vienības vārds (DN)"

# English string: Distinguished name (DN) of person's primary Organizational Unit
msgid "urn:mace:dir:attribute-def:eduPersonPrimaryOrgUnitDN"
msgstr "Personas pamata organizācijas vienības vārds (DN)"

# English string: Distinguished name (DN) of person's primary Organizational Unit
msgid "urn:oid:*******.4.1.5923.*******"
msgstr "Personas pamata organizācijas vienības vārds (DN)"

# English string: Person's principal name at home organization
msgid "eduPersonPrincipalName"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "urn:mace:dir:attribute-def:eduPersonPrincipalName"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "urn:oid:*******.4.1.5923.*******"
msgstr "Priekšnieka vārds"

# English string: Affiliation at home organization
msgid "eduPersonScopedAffiliation"
msgstr "Amats organizācijā"

# English string: Affiliation at home organization
msgid "urn:mace:dir:attribute-def:eduPersonScopedAffiliation"
msgstr "Amats organizācijā"

# English string: Affiliation at home organization
msgid "urn:oid:*******.4.1.5923.*******"
msgstr "Amats organizācijā"

# English string: Persistent pseudonymous ID
msgid "eduPersonTargetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Persistent pseudonymous ID
msgid "urn:mace:dir:attribute-def:eduPersonTargetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Persistent pseudonymous ID
msgid "urn:oid:*******.4.1.5923.********"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Person's non-reassignable, persistent pseudonymous ID at home organization
msgid "eduPersonUniqueId"
msgstr "eduPersonUniqueId"

# English string: Person's non-reassignable, persistent pseudonymous ID at home organization
msgid "urn:mace:dir:attribute-def:eduPersonUniqueId"
msgstr "urn:mace:dir:attribute-def:eduPersonUniqueId"

# English string: Person's non-reassignable, persistent pseudonymous ID at home organization
msgid "urn:oid:*******.4.1.5923.********"
msgstr "urn:oid:*******.4.1.5923.********"

msgid "email"
msgstr "email"

msgid "urn:mace:dir:attribute-def:email"
msgstr "urn:mace:dir:attribute-def:email"

msgid "urn:oid:1.2.840.113549.1.9.1"
msgstr "urn:oid:1.2.840.113549.1.9.1"

msgid "emailAddress"
msgstr "emailAddress"

msgid "urn:mace:dir:attribute-def:emailAddress"
msgstr "urn:mace:dir:attribute-def:emailAddress"

msgid "employeeNumber"
msgstr "employeeNumber"

msgid "urn:mace:dir:attribute-def:employeeNumber"
msgstr "urn:mace:dir:attribute-def:employeeNumber"

msgid "urn:oid:2.16.840.1.113730.3.1.3"
msgstr "urn:oid:2.16.840.1.113730.3.1.3"

msgid "employeeType"
msgstr "employeeType"

msgid "urn:mace:dir:attribute-def:employeeType"
msgstr "urn:mace:dir:attribute-def:employeeType"

msgid "urn:oid:2.16.840.1.113730.3.1.4"
msgstr "urn:oid:2.16.840.1.113730.3.1.4"

msgid "enhancedSearchGuide"
msgstr "enhancedSearchGuide"

msgid "urn:mace:dir:attribute-def:enhancedSearchGuide"
msgstr "urn:mace:dir:attribute-def:enhancedSearchGuide"

msgid "urn:oid:********"
msgstr "urn:oid:********"

# English string: Fax number
msgid "facsimileTelephoneNumber"
msgstr "Fakss"

# English string: Fax number
msgid "urn:mace:dir:attribute-def:facsimileTelephoneNumber"
msgstr "Fakss"

# English string: Fax number
msgid "urn:oid:********"
msgstr "Fakss"

msgid "favouriteDrink"
msgstr "favouriteDrink"

msgid "urn:mace:dir:attribute-def:favouriteDrink"
msgstr "urn:mace:dir:attribute-def:favouriteDrink"

msgid "fax"
msgstr "fax"

msgid "urn:mace:dir:attribute-def:fax"
msgstr "urn:mace:dir:attribute-def:fax"

msgid "federationFeideSchemaVersion"
msgstr "federationFeideSchemaVersion"

msgid "urn:mace:dir:attribute-def:federationFeideSchemaVersion"
msgstr "urn:mace:dir:attribute-def:federationFeideSchemaVersion"

msgid "urn:oid:*******.4.1.2428.90.1.9"
msgstr "urn:oid:*******.4.1.2428.90.1.9"

msgid "friendlyCountryName"
msgstr "friendlyCountryName"

msgid "urn:mace:dir:attribute-def:friendlyCountryName"
msgstr "urn:mace:dir:attribute-def:friendlyCountryName"

msgid "generationQualifier"
msgstr "generationQualifier"

msgid "urn:mace:dir:attribute-def:generationQualifier"
msgstr "urn:mace:dir:attribute-def:generationQualifier"

msgid "urn:oid:2.5.4.44"
msgstr "urn:oid:2.5.4.44"

# English string: Given name
msgid "givenName"
msgstr "Vārds"

# English string: Given name
msgid "urn:mace:dir:attribute-def:givenName"
msgstr "Vārds"

# English string: Given name
msgid "urn:oid:********"
msgstr "Vārds"

msgid "gn"
msgstr "gn"

msgid "urn:mace:dir:attribute-def:gn"
msgstr "urn:mace:dir:attribute-def:gn"

# English string: Home telephone
msgid "homePhone"
msgstr "Telefons"

# English string: Home telephone
msgid "urn:mace:dir:attribute-def:homePhone"
msgstr "Telefons"

# English string: Home telephone
msgid "urn:oid:0.9.2342.********.100.1.20"
msgstr "Telefons"

# English string: Home postal address
msgid "homePostalAddress"
msgstr "Pasta adrese"

# English string: Home postal address
msgid "urn:mace:dir:attribute-def:homePostalAddress"
msgstr "Pasta adrese"

# English string: Home postal address
msgid "urn:oid:0.9.2342.********.100.1.39"
msgstr "Pasta adrese"

msgid "homeTelephoneNumber"
msgstr "homeTelephoneNumber"

msgid "urn:mace:dir:attribute-def:homeTelephoneNumber"
msgstr "urn:mace:dir:attribute-def:homeTelephoneNumber"

msgid "host"
msgstr "host"

msgid "urn:mace:dir:attribute-def:host"
msgstr "urn:mace:dir:attribute-def:host"

msgid "urn:oid:0.9.2342.********.100.1.9"
msgstr "urn:oid:0.9.2342.********.100.1.9"

msgid "houseIdentifier"
msgstr "houseIdentifier"

msgid "urn:mace:dir:attribute-def:houseIdentifier"
msgstr "urn:mace:dir:attribute-def:houseIdentifier"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "info"
msgstr "info"

msgid "urn:mace:dir:attribute-def:info"
msgstr "urn:mace:dir:attribute-def:info"

msgid "urn:oid:0.9.2342.********.100.1.4"
msgstr "urn:oid:0.9.2342.********.100.1.4"

msgid "initials"
msgstr "initials"

msgid "urn:mace:dir:attribute-def:initials"
msgstr "urn:mace:dir:attribute-def:initials"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "internationaliSDNNumber"
msgstr "internationaliSDNNumber"

msgid "urn:mace:dir:attribute-def:internationaliSDNNumber"
msgstr "urn:mace:dir:attribute-def:internationaliSDNNumber"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "janetMailbox"
msgstr "janetMailbox"

msgid "urn:mace:dir:attribute-def:janetMailbox"
msgstr "urn:mace:dir:attribute-def:janetMailbox"

msgid "urn:oid:0.9.2342.********.100.1.46"
msgstr "urn:oid:0.9.2342.********.100.1.46"

# English string: JPEG Photo
msgid "jpegPhoto"
msgstr "JPEG fotogrāfija"

# English string: JPEG Photo
msgid "urn:mace:dir:attribute-def:jpegPhoto"
msgstr "JPEG fotogrāfija"

# English string: JPEG Photo
msgid "urn:oid:0.9.2342.********.100.1.60"
msgstr "JPEG fotogrāfija"

msgid "knowledgeInformation"
msgstr "knowledgeInformation"

msgid "urn:mace:dir:attribute-def:knowledgeInformation"
msgstr "urn:mace:dir:attribute-def:knowledgeInformation"

msgid "urn:oid:2.5.4.2"
msgstr "urn:oid:2.5.4.2"

# English string: Locality
msgid "l"
msgstr "Atrašanās vieta"

# English string: Locality
msgid "urn:mace:dir:attribute-def:l"
msgstr "Atrašanās vieta"

# English string: Locality
msgid "urn:oid:2.5.4.7"
msgstr "Atrašanās vieta"

# English string: Labeled URI
msgid "labeledURI"
msgstr "URI nosaukums"

# English string: Labeled URI
msgid "urn:mace:dir:attribute-def:labeledURI"
msgstr "URI nosaukums"

# English string: Labeled URI
msgid "urn:oid:*******.4.1.250.1.57"
msgstr "URI nosaukums"

msgid "localityName"
msgstr "localityName"

msgid "urn:mace:dir:attribute-def:localityName"
msgstr "urn:mace:dir:attribute-def:localityName"

msgid "mDRecord"
msgstr "mDRecord"

msgid "urn:mace:dir:attribute-def:mDRecord"
msgstr "urn:mace:dir:attribute-def:mDRecord"

msgid "urn:oid:0.9.2342.********.100.1.27"
msgstr "urn:oid:0.9.2342.********.100.1.27"

msgid "mXRecord"
msgstr "mXRecord"

msgid "urn:mace:dir:attribute-def:mXRecord"
msgstr "urn:mace:dir:attribute-def:mXRecord"

msgid "urn:oid:0.9.2342.********.100.1.28"
msgstr "urn:oid:0.9.2342.********.100.1.28"

# English string: Mail
msgid "mail"
msgstr "Pasts"

# English string: Mail
msgid "urn:mace:dir:attribute-def:mail"
msgstr "Pasts"

# English string: Mail
msgid "urn:oid:0.9.2342.********.100.1.3"
msgstr "Pasts"

msgid "mailPreferenceOption"
msgstr "mailPreferenceOption"

msgid "urn:mace:dir:attribute-def:mailPreferenceOption"
msgstr "urn:mace:dir:attribute-def:mailPreferenceOption"

msgid "urn:oid:0.9.2342.********.100.1.47"
msgstr "urn:oid:0.9.2342.********.100.1.47"

# English string: Manager
msgid "manager"
msgstr "Priekšnieks"

# English string: Manager
msgid "urn:mace:dir:attribute-def:manager"
msgstr "Priekšnieks"

# English string: Manager
msgid "urn:oid:0.9.2342.********.100.1.10"
msgstr "Priekšnieks"

msgid "member"
msgstr "member"

msgid "urn:mace:dir:attribute-def:member"
msgstr "urn:mace:dir:attribute-def:member"

msgid "urn:oid:2.5.4.31"
msgstr "urn:oid:2.5.4.31"

# English string: Mobile
msgid "mobile"
msgstr "Mobilais telefons"

# English string: Mobile
msgid "urn:mace:dir:attribute-def:mobile"
msgstr "Mobilais telefons"

# English string: Mobile
msgid "urn:oid:0.9.2342.********.100.1.41"
msgstr "Mobilais telefons"

msgid "mobileTelephoneNumber"
msgstr "mobileTelephoneNumber"

msgid "urn:mace:dir:attribute-def:mobileTelephoneNumber"
msgstr "urn:mace:dir:attribute-def:mobileTelephoneNumber"

msgid "nSRecord"
msgstr "nSRecord"

msgid "urn:mace:dir:attribute-def:nSRecord"
msgstr "urn:mace:dir:attribute-def:nSRecord"

msgid "urn:oid:0.9.2342.********.100.1.29"
msgstr "urn:oid:0.9.2342.********.100.1.29"

msgid "name"
msgstr "name"

msgid "urn:mace:dir:attribute-def:name"
msgstr "urn:mace:dir:attribute-def:name"

msgid "urn:oid:2.5.4.41"
msgstr "urn:oid:2.5.4.41"

msgid "norEduOrgAcronym"
msgstr "norEduOrgAcronym"

msgid "urn:mace:dir:attribute-def:norEduOrgAcronym"
msgstr "urn:mace:dir:attribute-def:norEduOrgAcronym"

msgid "urn:oid:*******.4.1.2428.90.1.6"
msgstr "urn:oid:*******.4.1.2428.90.1.6"

# English string: Organizational number
msgid "norEduOrgNIN"
msgstr "Organizācijas reģistrācijas numurs"

# English string: Organizational number
msgid "urn:mace:dir:attribute-def:norEduOrgNIN"
msgstr "Organizācijas reģistrācijas numurs"

# English string: Organizational number
msgid "urn:oid:*******.4.1.2428.90.1.12"
msgstr "Organizācijas reģistrācijas numurs"

msgid "norEduOrgSchemaVersion"
msgstr "norEduOrgSchemaVersion"

msgid "urn:mace:dir:attribute-def:norEduOrgSchemaVersion"
msgstr "urn:mace:dir:attribute-def:norEduOrgSchemaVersion"

msgid "urn:oid:*******.4.1.2428.90.1.11"
msgstr "urn:oid:*******.4.1.2428.90.1.11"

msgid "norEduOrgUniqueIdentifier"
msgstr "norEduOrgUniqueIdentifier"

msgid "urn:mace:dir:attribute-def:norEduOrgUniqueIdentifier"
msgstr "urn:mace:dir:attribute-def:norEduOrgUniqueIdentifier"

msgid "urn:oid:*******.4.1.2428.90.1.7"
msgstr "urn:oid:*******.4.1.2428.90.1.7"

msgid "norEduOrgUniqueNumber"
msgstr "norEduOrgUniqueNumber"

msgid "urn:mace:dir:attribute-def:norEduOrgUniqueNumber"
msgstr "urn:mace:dir:attribute-def:norEduOrgUniqueNumber"

msgid "urn:oid:*******.4.1.2428.90.1.1"
msgstr "urn:oid:*******.4.1.2428.90.1.1"

msgid "norEduOrgUnitUniqueIdentifier"
msgstr "norEduOrgUnitUniqueIdentifier"

msgid "urn:mace:dir:attribute-def:norEduOrgUnitUniqueIdentifier"
msgstr "urn:mace:dir:attribute-def:norEduOrgUnitUniqueIdentifier"

msgid "urn:oid:*******.4.1.2428.90.1.8"
msgstr "urn:oid:*******.4.1.2428.90.1.8"

msgid "norEduOrgUnitUniqueNumber"
msgstr "norEduOrgUnitUniqueNumber"

msgid "urn:mace:dir:attribute-def:norEduOrgUnitUniqueNumber"
msgstr "urn:mace:dir:attribute-def:norEduOrgUnitUniqueNumber"

msgid "urn:oid:*******.4.1.2428.90.1.2"
msgstr "urn:oid:*******.4.1.2428.90.1.2"

# English string: Date of birth
msgid "norEduPersonBirthDate"
msgstr "Dzimšanas datums"

# English string: Date of birth
msgid "urn:mace:dir:attribute-def:norEduPersonBirthDate"
msgstr "Dzimšanas datums"

# English string: Date of birth
msgid "urn:oid:*******.4.1.2428.90.1.3"
msgstr "Dzimšanas datums"

# English string: Local identity number
msgid "norEduPersonLIN"
msgstr "Personas kods"

# English string: Local identity number
msgid "urn:mace:dir:attribute-def:norEduPersonLIN"
msgstr "Personas kods"

# English string: Local identity number
msgid "urn:oid:*******.4.1.2428.90.1.4"
msgstr "Personas kods"

# English string: Identity number assigned by public authorities
msgid "norEduPersonNIN"
msgstr "Publisko autoritāšu piešķirtais identitātes numurs"

# English string: Identity number assigned by public authorities
msgid "urn:mace:dir:attribute-def:norEduPersonNIN"
msgstr "Publisko autoritāšu piešķirtais identitātes numurs"

# English string: Identity number assigned by public authorities
msgid "urn:oid:*******.4.1.2428.90.1.5"
msgstr "Publisko autoritāšu piešķirtais identitātes numurs"

# English string: Organization name
msgid "o"
msgstr "Organizācijas nosaukums"

# English string: Organization name
msgid "urn:mace:dir:attribute-def:o"
msgstr "Organizācijas nosaukums"

# English string: Organization name
msgid "urn:oid:2.5.4.10"
msgstr "Organizācijas nosaukums"

msgid "objectClass"
msgstr "objectClass"

msgid "urn:mace:dir:attribute-def:objectClass"
msgstr "urn:mace:dir:attribute-def:objectClass"

msgid "urn:oid:2.5.4.0"
msgstr "urn:oid:2.5.4.0"

msgid "organizationName"
msgstr "organizationName"

msgid "urn:mace:dir:attribute-def:organizationName"
msgstr "urn:mace:dir:attribute-def:organizationName"

msgid "organizationalStatus"
msgstr "organizationalStatus"

msgid "urn:mace:dir:attribute-def:organizationalStatus"
msgstr "urn:mace:dir:attribute-def:organizationalStatus"

msgid "urn:oid:0.9.2342.********.100.1.45"
msgstr "urn:oid:0.9.2342.********.100.1.45"

msgid "organizationalUnitName"
msgstr "organizationalUnitName"

msgid "urn:mace:dir:attribute-def:organizationalUnitName"
msgstr "urn:mace:dir:attribute-def:organizationalUnitName"

# English string: Organizational unit
msgid "urn:oid:2.5.4.11"
msgstr "Organizācijas vienība"

msgid "otherMailbox"
msgstr "otherMailbox"

msgid "urn:mace:dir:attribute-def:otherMailbox"
msgstr "urn:mace:dir:attribute-def:otherMailbox"

msgid "urn:oid:0.9.2342.********.100.1.22"
msgstr "urn:oid:0.9.2342.********.100.1.22"

# English string: Organizational unit
msgid "ou"
msgstr "Organizācijas vienība"

# English string: Organizational unit
msgid "urn:mace:dir:attribute-def:ou"
msgstr "Organizācijas vienība"

msgid "owner"
msgstr "owner"

msgid "urn:mace:dir:attribute-def:owner"
msgstr "urn:mace:dir:attribute-def:owner"

msgid "urn:oid:2.5.4.32"
msgstr "urn:oid:2.5.4.32"

msgid "pager"
msgstr "pager"

msgid "urn:mace:dir:attribute-def:pager"
msgstr "urn:mace:dir:attribute-def:pager"

msgid "urn:oid:0.9.2342.********.100.1.42"
msgstr "urn:oid:0.9.2342.********.100.1.42"

msgid "pagerTelephoneNumber"
msgstr "pagerTelephoneNumber"

msgid "urn:mace:dir:attribute-def:pagerTelephoneNumber"
msgstr "urn:mace:dir:attribute-def:pagerTelephoneNumber"

# English string: Service-specific pseudonymous ID at home organization
msgid "pairwise-id"
msgstr "pairwise-id"

# English string: Service-specific pseudonymous ID at home organization
msgid "urn:oasis:names:tc:SAML:attribute:pairwise-id"
msgstr "urn:oasis:names:tc:SAML:attribute:pairwise-id"

msgid "personalSignature"
msgstr "personalSignature"

msgid "urn:mace:dir:attribute-def:personalSignature"
msgstr "urn:mace:dir:attribute-def:personalSignature"

msgid "urn:oid:0.9.2342.********.100.1.53"
msgstr "urn:oid:0.9.2342.********.100.1.53"

msgid "personalTitle"
msgstr "personalTitle"

msgid "urn:mace:dir:attribute-def:personalTitle"
msgstr "urn:mace:dir:attribute-def:personalTitle"

msgid "urn:oid:0.9.2342.********.100.1.40"
msgstr "urn:oid:0.9.2342.********.100.1.40"

msgid "photo"
msgstr "photo"

msgid "urn:mace:dir:attribute-def:photo"
msgstr "urn:mace:dir:attribute-def:photo"

msgid "urn:oid:0.9.2342.********.100.1.7"
msgstr "urn:oid:0.9.2342.********.100.1.7"

msgid "physicalDeliveryOfficeName"
msgstr "physicalDeliveryOfficeName"

msgid "urn:mace:dir:attribute-def:physicalDeliveryOfficeName"
msgstr "urn:mace:dir:attribute-def:physicalDeliveryOfficeName"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "pkcs9email"
msgstr "pkcs9email"

msgid "urn:mace:dir:attribute-def:pkcs9email"
msgstr "urn:mace:dir:attribute-def:pkcs9email"

# English string: Post office box
msgid "postOfficeBox"
msgstr "Pasta kaste"

# English string: Post office box
msgid "urn:mace:dir:attribute-def:postOfficeBox"
msgstr "Pasta kaste"

# English string: Post office box
msgid "urn:oid:********"
msgstr "Pasta kaste"

# English string: Postal address
msgid "postalAddress"
msgstr "Pasta adrese"

# English string: Postal address
msgid "urn:mace:dir:attribute-def:postalAddress"
msgstr "Pasta adrese"

# English string: Postal address
msgid "urn:oid:********"
msgstr "Pasta adrese"

# English string: Postal code
msgid "postalCode"
msgstr "Pasta kods"

# English string: Postal code
msgid "urn:mace:dir:attribute-def:postalCode"
msgstr "Pasta kods"

# English string: Postal code
msgid "urn:oid:********"
msgstr "Pasta kods"

msgid "preferredDeliveryMethod"
msgstr "preferredDeliveryMethod"

msgid "urn:mace:dir:attribute-def:preferredDeliveryMethod"
msgstr "urn:mace:dir:attribute-def:preferredDeliveryMethod"

msgid "urn:oid:********"
msgstr "urn:oid:********"

# English string: Preferred language
msgid "preferredLanguage"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "urn:mace:dir:attribute-def:preferredLanguage"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "urn:oid:2.16.840.1.113730.3.1.39"
msgstr "Vēlamā valoda"

msgid "presentationAddress"
msgstr "presentationAddress"

msgid "urn:mace:dir:attribute-def:presentationAddress"
msgstr "urn:mace:dir:attribute-def:presentationAddress"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "protocolInformation"
msgstr "protocolInformation"

msgid "urn:mace:dir:attribute-def:protocolInformation"
msgstr "urn:mace:dir:attribute-def:protocolInformation"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "pseudonym"
msgstr "pseudonym"

msgid "urn:mace:dir:attribute-def:pseudonym"
msgstr "urn:mace:dir:attribute-def:pseudonym"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "registeredAddress"
msgstr "registeredAddress"

msgid "urn:mace:dir:attribute-def:registeredAddress"
msgstr "urn:mace:dir:attribute-def:registeredAddress"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "rfc822Mailbox"
msgstr "rfc822Mailbox"

msgid "urn:mace:dir:attribute-def:rfc822Mailbox"
msgstr "urn:mace:dir:attribute-def:rfc822Mailbox"

msgid "roleOccupant"
msgstr "roleOccupant"

msgid "urn:mace:dir:attribute-def:roleOccupant"
msgstr "urn:mace:dir:attribute-def:roleOccupant"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "roomNumber"
msgstr "roomNumber"

msgid "urn:mace:dir:attribute-def:roomNumber"
msgstr "urn:mace:dir:attribute-def:roomNumber"

msgid "urn:oid:0.9.2342.********.100.1.6"
msgstr "urn:oid:0.9.2342.********.100.1.6"

msgid "sOARecord"
msgstr "sOARecord"

msgid "urn:mace:dir:attribute-def:sOARecord"
msgstr "urn:mace:dir:attribute-def:sOARecord"

msgid "urn:oid:0.9.2342.********.100.1.30"
msgstr "urn:oid:0.9.2342.********.100.1.30"

msgid "schacCountryOfCitizenship"
msgstr "schacCountryOfCitizenship"

msgid "urn:mace:terena.org:attribute-def:schacCountryOfCitizenship"
msgstr "urn:mace:terena.org:attribute-def:schacCountryOfCitizenship"

msgid "urn:oid:*******.4.1.25178.1.2.5"
msgstr "urn:oid:*******.4.1.25178.1.2.5"

msgid "urn:schac:attribute-def:schacCountryOfCitizenship"
msgstr "urn:schac:attribute-def:schacCountryOfCitizenship"

msgid "schacCountryOfResidence"
msgstr "schacCountryOfResidence"

msgid "urn:mace:terena.org:attribute-def:schacCountryOfResidence"
msgstr "urn:mace:terena.org:attribute-def:schacCountryOfResidence"

msgid "urn:oid:*******.4.1.25178.1.2.11"
msgstr "urn:oid:*******.4.1.25178.1.2.11"

msgid "urn:schac:attribute-def:schacCountryOfResidence"
msgstr "urn:schac:attribute-def:schacCountryOfResidence"

msgid "schacDateOfBirth"
msgstr "schacDateOfBirth"

msgid "urn:mace:terena.org:attribute-def:schacDateOfBirth"
msgstr "urn:mace:terena.org:attribute-def:schacDateOfBirth"

msgid "urn:oid:*******.4.1.25178.1.2.3"
msgstr "urn:oid:*******.4.1.25178.1.2.3"

msgid "urn:schac:attribute-def:schacDateOfBirth"
msgstr "urn:schac:attribute-def:schacDateOfBirth"

msgid "schacExpiryDate"
msgstr "schacExpiryDate"

msgid "urn:mace:terena.org:attribute-def:schacExpiryDate"
msgstr "urn:mace:terena.org:attribute-def:schacExpiryDate"

msgid "urn:oid:*******.4.1.25178.1.2.17"
msgstr "urn:oid:*******.4.1.25178.1.2.17"

msgid "urn:schac:attribute-def:schacExpiryDate"
msgstr "urn:schac:attribute-def:schacExpiryDate"

msgid "schacGender"
msgstr "schacGender"

msgid "urn:mace:terena.org:attribute-def:schacGender"
msgstr "urn:mace:terena.org:attribute-def:schacGender"

msgid "urn:oid:*******.4.1.25178.1.2.2"
msgstr "urn:oid:*******.4.1.25178.1.2.2"

msgid "urn:schac:attribute-def:schacGender"
msgstr "urn:schac:attribute-def:schacGender"

# English string: Home organization domain name
msgid "schacHomeOrganization"
msgstr "Organizācijas domeins"

# English string: Home organization domain name
msgid "urn:mace:terena.org:attribute-def:schacHomeOrganization"
msgstr "Organizācijas domeins"

# English string: Home organization domain name
msgid "urn:oid:*******.4.1.25178.1.2.9"
msgstr "Organizācijas domeins"

# English string: Home organization domain name
msgid "urn:schac:attribute-def:schacHomeOrganization"
msgstr "Organizācijas domeins"

msgid "schacHomeOrganizationType"
msgstr "schacHomeOrganizationType"

msgid "urn:mace:terena.org:attribute-def:schacHomeOrganizationType"
msgstr "urn:mace:terena.org:attribute-def:schacHomeOrganizationType"

msgid "urn:oid:*******.4.1.25178.1.2.10"
msgstr "urn:oid:*******.4.1.25178.1.2.10"

msgid "urn:schac:attribute-def:schacHomeOrganizationType"
msgstr "urn:schac:attribute-def:schacHomeOrganizationType"

msgid "schacMotherTongue"
msgstr "schacMotherTongue"

msgid "urn:mace:terena.org:attribute-def:schacMotherTongue"
msgstr "urn:mace:terena.org:attribute-def:schacMotherTongue"

msgid "urn:oid:*******.4.1.25178.1.2.1"
msgstr "urn:oid:*******.4.1.25178.1.2.1"

msgid "urn:schac:attribute-def:schacMotherTongue"
msgstr "urn:schac:attribute-def:schacMotherTongue"

msgid "schacPersonalPosition"
msgstr "schacPersonalPosition"

msgid "urn:mace:terena.org:attribute-def:schacPersonalPosition"
msgstr "urn:mace:terena.org:attribute-def:schacPersonalPosition"

msgid "urn:oid:*******.4.1.25178.1.2.13"
msgstr "urn:oid:*******.4.1.25178.1.2.13"

msgid "urn:schac:attribute-def:schacPersonalPosition"
msgstr "urn:schac:attribute-def:schacPersonalPosition"

msgid "schacPersonalTitle"
msgstr "schacPersonalTitle"

msgid "urn:mace:terena.org:attribute-def:schacPersonalTitle"
msgstr "urn:mace:terena.org:attribute-def:schacPersonalTitle"

msgid "urn:oid:*******.4.1.25178.1.2.8"
msgstr "urn:oid:*******.4.1.25178.1.2.8"

msgid "urn:schac:attribute-def:schacPersonalTitle"
msgstr "urn:schac:attribute-def:schacPersonalTitle"

msgid "schacPersonalUniqueCode"
msgstr "schacPersonalUniqueCode"

msgid "urn:mace:terena.org:attribute-def:schacPersonalUniqueCode"
msgstr "urn:mace:terena.org:attribute-def:schacPersonalUniqueCode"

msgid "urn:oid:*******.4.1.25178.1.2.14"
msgstr "urn:oid:*******.4.1.25178.1.2.14"

msgid "urn:schac:attribute-def:schacPersonalUniqueCode"
msgstr "urn:schac:attribute-def:schacPersonalUniqueCode"

msgid "schacPersonalUniqueID"
msgstr "schacPersonalUniqueID"

msgid "urn:mace:terena.org:attribute-def:schacPersonalUniqueID"
msgstr "urn:mace:terena.org:attribute-def:schacPersonalUniqueID"

msgid "urn:oid:*******.4.1.25178.1.2.15"
msgstr "urn:oid:*******.4.1.25178.1.2.15"

msgid "urn:schac:attribute-def:schacPersonalUniqueID"
msgstr "urn:schac:attribute-def:schacPersonalUniqueID"

msgid "schacPlaceOfBirth"
msgstr "schacPlaceOfBirth"

msgid "urn:mace:terena.org:attribute-def:schacPlaceOfBirth"
msgstr "urn:mace:terena.org:attribute-def:schacPlaceOfBirth"

msgid "urn:oid:*******.4.1.25178.1.2.4"
msgstr "urn:oid:*******.4.1.25178.1.2.4"

msgid "urn:schac:attribute-def:schacPlaceOfBirth"
msgstr "urn:schac:attribute-def:schacPlaceOfBirth"

msgid "schacProjectMembership"
msgstr "schacProjectMembership"

msgid "urn:mace:terena.org:attribute-def:schacProjectMembership"
msgstr "urn:mace:terena.org:attribute-def:schacProjectMembership"

msgid "urn:oid:*******.4.1.25178.1.2.20"
msgstr "urn:oid:*******.4.1.25178.1.2.20"

msgid "urn:schac:attribute-def:schacProjectMembership"
msgstr "urn:schac:attribute-def:schacProjectMembership"

msgid "schacProjectSpecificRole"
msgstr "schacProjectSpecificRole"

msgid "urn:mace:terena.org:attribute-def:schacProjectSpecificRole"
msgstr "urn:mace:terena.org:attribute-def:schacProjectSpecificRole"

msgid "urn:oid:*******.4.1.25178.1.2.21"
msgstr "urn:oid:*******.4.1.25178.1.2.21"

msgid "urn:schac:attribute-def:schacProjectSpecificRole"
msgstr "urn:schac:attribute-def:schacProjectSpecificRole"

msgid "schacSn1"
msgstr "schacSn1"

msgid "urn:mace:terena.org:attribute-def:schacSn1"
msgstr "urn:mace:terena.org:attribute-def:schacSn1"

msgid "urn:oid:*******.4.1.25178.1.2.6"
msgstr "urn:oid:*******.4.1.25178.1.2.6"

msgid "urn:schac:attribute-def:schacSn1"
msgstr "urn:schac:attribute-def:schacSn1"

msgid "schacSn2"
msgstr "schacSn2"

msgid "urn:mace:terena.org:attribute-def:schacSn2"
msgstr "urn:mace:terena.org:attribute-def:schacSn2"

msgid "urn:oid:*******.4.1.25178.1.2.7"
msgstr "urn:oid:*******.4.1.25178.1.2.7"

msgid "urn:schac:attribute-def:schacSn2"
msgstr "urn:schac:attribute-def:schacSn2"

msgid "schacUserPresenceID"
msgstr "schacUserPresenceID"

msgid "urn:mace:terena.org:attribute-def:schacUserPresenceID"
msgstr "urn:mace:terena.org:attribute-def:schacUserPresenceID"

msgid "urn:oid:*******.4.1.25178.1.2.12"
msgstr "urn:oid:*******.4.1.25178.1.2.12"

msgid "urn:schac:attribute-def:schacUserPresenceID"
msgstr "urn:schac:attribute-def:schacUserPresenceID"

# English string: Private information elements
msgid "schacUserPrivateAttribute"
msgstr "Privātās informācijas elementi"

# English string: Private information elements
msgid "urn:mace:terena.org:attribute-def:schacUserPrivateAttribute"
msgstr "Privātās informācijas elementi"

# English string: Private information elements
msgid "urn:oid:*******.4.1.25178.1.2.18"
msgstr "Privātās informācijas elementi"

# English string: Private information elements
msgid "urn:schac:attribute-def:schacUserPrivateAttribute"
msgstr "Privātās informācijas elementi"

msgid "schacUserStatus"
msgstr "schacUserStatus"

msgid "urn:mace:terena.org:attribute-def:schacUserStatus"
msgstr "urn:mace:terena.org:attribute-def:schacUserStatus"

msgid "urn:oid:*******.4.1.25178.1.2.19"
msgstr "urn:oid:*******.4.1.25178.1.2.19"

msgid "urn:schac:attribute-def:schacUserStatus"
msgstr "urn:schac:attribute-def:schacUserStatus"

msgid "schacYearOfBirth"
msgstr "schacYearOfBirth"

msgid "urn:mace:terena.org:attribute-def:schacYearOfBirth"
msgstr "urn:mace:terena.org:attribute-def:schacYearOfBirth"

msgid "urn:oid:*******.4.1.25178.1.0.2.3"
msgstr "urn:oid:*******.4.1.25178.1.0.2.3"

msgid "urn:schac:attribute-def:schacYearOfBirth"
msgstr "urn:schac:attribute-def:schacYearOfBirth"

msgid "searchGuide"
msgstr "searchGuide"

msgid "urn:mace:dir:attribute-def:searchGuide"
msgstr "urn:mace:dir:attribute-def:searchGuide"

msgid "urn:oid:2.5.4.14"
msgstr "urn:oid:2.5.4.14"

msgid "secretary"
msgstr "secretary"

msgid "urn:mace:dir:attribute-def:secretary"
msgstr "urn:mace:dir:attribute-def:secretary"

msgid "urn:oid:0.9.2342.********.100.1.21"
msgstr "urn:oid:0.9.2342.********.100.1.21"

msgid "seeAlso"
msgstr "seeAlso"

msgid "urn:mace:dir:attribute-def:seeAlso"
msgstr "urn:mace:dir:attribute-def:seeAlso"

msgid "urn:oid:2.5.4.34"
msgstr "urn:oid:2.5.4.34"

msgid "serialNumber"
msgstr "serialNumber"

msgid "urn:mace:dir:attribute-def:serialNumber"
msgstr "urn:mace:dir:attribute-def:serialNumber"

msgid "urn:oid:2.5.4.5"
msgstr "urn:oid:2.5.4.5"

msgid "singleLevelQuality"
msgstr "singleLevelQuality"

msgid "urn:mace:dir:attribute-def:singleLevelQuality"
msgstr "urn:mace:dir:attribute-def:singleLevelQuality"

msgid "urn:oid:0.9.2342.********.100.1.50"
msgstr "urn:oid:0.9.2342.********.100.1.50"

msgid "sisSchoolGrade"
msgstr "sisSchoolGrade"

msgid "urn:mace:dir:attribute-def:sisSchoolGrade"
msgstr "urn:mace:dir:attribute-def:sisSchoolGrade"

msgid "urn:oid:1.2.752.194.10.2.2"
msgstr "urn:oid:1.2.752.194.10.2.2"

msgid "sisLegalGuardianFor"
msgstr "sisLegalGuardianFor"

msgid "urn:mace:dir:attribute-def:sisLegalGuardianFor"
msgstr "urn:mace:dir:attribute-def:sisLegalGuardianFor"

msgid "urn:oid:1.2.752.194.10.2.1"
msgstr "urn:oid:1.2.752.194.10.2.1"

# English string: Surname
msgid "sn"
msgstr "Uzvārds"

# English string: Surname
msgid "urn:mace:dir:attribute-def:sn"
msgstr "Uzvārds"

# English string: Surname
msgid "urn:oid:2.5.4.4"
msgstr "Uzvārds"

msgid "st"
msgstr "st"

msgid "urn:mace:dir:attribute-def:st"
msgstr "urn:mace:dir:attribute-def:st"

msgid "urn:oid:*******"
msgstr "urn:oid:*******"

msgid "stateOrProvinceName"
msgstr "stateOrProvinceName"

msgid "urn:mace:dir:attribute-def:stateOrProvinceName"
msgstr "urn:mace:dir:attribute-def:stateOrProvinceName"

# English string: Street
msgid "street"
msgstr "Iela"

# English string: Street
msgid "urn:mace:dir:attribute-def:street"
msgstr "Iela"

# English string: Street
msgid "urn:oid:*******"
msgstr "Iela"

msgid "streetAddress"
msgstr "streetAddress"

msgid "urn:mace:dir:attribute-def:streetAddress"
msgstr "urn:mace:dir:attribute-def:streetAddress"

# English string: Pseudonymous ID at home organization
msgid "subject-id"
msgstr "subject-id"

# English string: Pseudonymous ID at home organization
msgid "urn:oasis:names:tc:SAML:attribute:subject-id"
msgstr "urn:oasis:names:tc:SAML:attribute:subject-id"

msgid "subtreeMaximumQuality"
msgstr "subtreeMaximumQuality"

msgid "urn:mace:dir:attribute-def:subtreeMaximumQuality"
msgstr "urn:mace:dir:attribute-def:subtreeMaximumQuality"

msgid "urn:oid:0.9.2342.********.100.1.52"
msgstr "urn:oid:0.9.2342.********.100.1.52"

msgid "subtreeMinimumQuality"
msgstr "subtreeMinimumQuality"

msgid "urn:mace:dir:attribute-def:subtreeMinimumQuality"
msgstr "urn:mace:dir:attribute-def:subtreeMinimumQuality"

msgid "urn:oid:0.9.2342.********.100.1.51"
msgstr "urn:oid:0.9.2342.********.100.1.51"

msgid "supportedAlgorithms"
msgstr "supportedAlgorithms"

msgid "urn:mace:dir:attribute-def:supportedAlgorithms"
msgstr "urn:mace:dir:attribute-def:supportedAlgorithms"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "supportedApplicationContext"
msgstr "supportedApplicationContext"

msgid "urn:mace:dir:attribute-def:supportedApplicationContext"
msgstr "urn:mace:dir:attribute-def:supportedApplicationContext"

msgid "urn:oid:2.5.4.30"
msgstr "urn:oid:2.5.4.30"

msgid "surname"
msgstr "surname"

msgid "urn:mace:dir:attribute-def:surname"
msgstr "urn:mace:dir:attribute-def:surname"

# English string: Telephone number
msgid "telephoneNumber"
msgstr "Telefons"

# English string: Telephone number
msgid "urn:mace:dir:attribute-def:telephoneNumber"
msgstr "Telefons"

# English string: Telephone number
msgid "urn:oid:********"
msgstr "Telefons"

msgid "teletexTerminalIdentifier"
msgstr "teletexTerminalIdentifier"

msgid "urn:mace:dir:attribute-def:teletexTerminalIdentifier"
msgstr "urn:mace:dir:attribute-def:teletexTerminalIdentifier"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "telexNumber"
msgstr "telexNumber"

msgid "urn:mace:dir:attribute-def:telexNumber"
msgstr "urn:mace:dir:attribute-def:telexNumber"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "textEncodedORAddress"
msgstr "textEncodedORAddress"

msgid "urn:mace:dir:attribute-def:textEncodedORAddress"
msgstr "urn:mace:dir:attribute-def:textEncodedORAddress"

msgid "urn:oid:0.9.2342.********.100.1.2"
msgstr "urn:oid:0.9.2342.********.100.1.2"

# English string: Title
msgid "title"
msgstr "Amats"

# English string: Title
msgid "urn:mace:dir:attribute-def:title"
msgstr "Amats"

# English string: Title
msgid "urn:oid:********"
msgstr "Amats"

# English string: User ID
msgid "uid"
msgstr "Lietotāja ID"

# English string: User ID
msgid "urn:mace:dir:attribute-def:uid"
msgstr "Lietotāja ID"

# English string: User ID
msgid "urn:oid:0.9.2342.********.100.1.1"
msgstr "Lietotāja ID"

msgid "uniqueIdentifier"
msgstr "uniqueIdentifier"

msgid "urn:mace:dir:attribute-def:uniqueIdentifier"
msgstr "urn:mace:dir:attribute-def:uniqueIdentifier"

msgid "urn:oid:0.9.2342.********.100.1.44"
msgstr "urn:oid:0.9.2342.********.100.1.44"

msgid "uniqueMember"
msgstr "uniqueMember"

msgid "urn:mace:dir:attribute-def:uniqueMember"
msgstr "urn:mace:dir:attribute-def:uniqueMember"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "userCertificate"
msgstr "userCertificate"

msgid "urn:mace:dir:attribute-def:userCertificate"
msgstr "urn:mace:dir:attribute-def:userCertificate"

msgid "urn:oid:2.5.4.36"
msgstr "urn:oid:2.5.4.36"

msgid "userClass"
msgstr "userClass"

msgid "urn:mace:dir:attribute-def:userClass"
msgstr "urn:mace:dir:attribute-def:userClass"

msgid "urn:oid:0.9.2342.********.100.1.8"
msgstr "urn:oid:0.9.2342.********.100.1.8"

msgid "userPKCS12"
msgstr "userPKCS12"

msgid "urn:mace:dir:attribute-def:userPKCS12"
msgstr "urn:mace:dir:attribute-def:userPKCS12"

msgid "urn:oid:2.16.840.1.113730.3.1.216"
msgstr "urn:oid:2.16.840.1.113730.3.1.216"

# English string: User's password hash
msgid "userPassword"
msgstr "Paroles jaucējsumma (hash)"

# English string: User's password hash
msgid "urn:mace:dir:attribute-def:userPassword"
msgstr "Paroles jaucējsumma (hash)"

# English string: User's password hash
msgid "urn:oid:********"
msgstr "Paroles jaucējsumma (hash)"

msgid "userSMIMECertificate"
msgstr "userSMIMECertificate"

msgid "urn:mace:dir:attribute-def:userSMIMECertificate"
msgstr "urn:mace:dir:attribute-def:userSMIMECertificate"

msgid "urn:oid:2.16.840.1.113730.3.1.40"
msgstr "urn:oid:2.16.840.1.113730.3.1.40"

msgid "userid"
msgstr "userid"

msgid "urn:mace:dir:attribute-def:userid"
msgstr "urn:mace:dir:attribute-def:userid"

msgid "x121Address"
msgstr "x121Address"

msgid "urn:mace:dir:attribute-def:x121Address"
msgstr "urn:mace:dir:attribute-def:x121Address"

msgid "urn:oid:********"
msgstr "urn:oid:********"

msgid "x500UniqueIdentifier"
msgstr "x500UniqueIdentifier"

msgid "urn:mace:dir:attribute-def:x500UniqueIdentifier"
msgstr "urn:mace:dir:attribute-def:x500UniqueIdentifier"

msgid "urn:oid:********"
msgstr "urn:oid:********"

# English string: Common name
msgid "facebook_cn"
msgstr "Vārds"

msgid "http://axschema.org/contact/country/home"
msgstr "http://axschema.org/contact/country/home"

msgid "openid.sreg.country"
msgstr "openid.sreg.country"

msgid "facebook.about_me"
msgstr "facebook.about_me"

msgid "linkedin.summary"
msgstr "linkedin.summary"

msgid "twitter.description"
msgstr "twitter.description"

# English string: Display name
msgid "facebook.name"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "http://axschema.org/namePerson/friendly"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "openid.sreg.nickname"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "http://axschema.org/namePerson"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "openid.sreg.fullname"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "twitter.name"
msgstr "Parādāmais vārds"

# English string: Display name
msgid "windowslive.displayName"
msgstr "Parādāmais vārds"

# English string: Person's principal name at home organization
msgid "facebook_user"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "linkedin_user"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "twitter_screen_n_realm"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "windowslive_user"
msgstr "Priekšnieka vārds"

# English string: Person's principal name at home organization
msgid "windowslive.userPrincipalName"
msgstr "Priekšnieka vārds"

# English string: Persistent pseudonymous ID
msgid "facebook_targetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Persistent pseudonymous ID
msgid "linkedin_targetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Persistent pseudonymous ID
msgid "twitter_targetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Persistent pseudonymous ID
msgid "windowslive_targetedID"
msgstr "Pastāvīgs pseidonīma ID"

# English string: Fax number
msgid "http://axschema.org/contact/phone/fax"
msgstr "Fakss"

# English string: Given name
msgid "facebook.first_name"
msgstr "Vārds"

# English string: Given name
msgid "linkedin.firstName"
msgstr "Vārds"

# English string: Given name
msgid "http://axschema.org/namePerson/first"
msgstr "Vārds"

# English string: Given name
msgid "windowslive.FirstName"
msgstr "Vārds"

# English string: Given name
msgid "windowslive.givenName"
msgstr "Vārds"

# English string: Home telephone
msgid "http://axschema.org/contact/phone/home"
msgstr "Telefons"

# English string: Locality
msgid "windowslive.Location"
msgstr "Atrašanās vieta"

# English string: Labeled URI
msgid "facebook.profile_url"
msgstr "URI nosaukums"

# English string: Labeled URI
msgid "twitter.url"
msgstr "URI nosaukums"

# English string: Mail
msgid "facebook.email"
msgstr "Pasts"

# English string: Mail
msgid "http://axschema.org/contact/email"
msgstr "Pasts"

# English string: Mail
msgid "openid.sreg.email"
msgstr "Pasts"

# English string: Mail
msgid "windowslive_mail"
msgstr "Pasts"

# English string: Mail
msgid "windowslive.mail"
msgstr "Pasts"

# English string: Mobile
msgid "http://axschema.org/contact/phone/cell"
msgstr "Mobilais telefons"

# English string: Organization name
msgid "http://axschema.org/company/name"
msgstr "Organizācijas nosaukums"

msgid "http://axschema.org/namePerson/prefix"
msgstr "http://axschema.org/namePerson/prefix"

# English string: Postal code
msgid "http://axschema.org/contact/postalCode/home"
msgstr "Pasta kods"

# English string: Postal code
msgid "openid.sreg.postcode"
msgstr "Pasta kods"

# English string: Preferred language
msgid "facebook.locale"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "http://axschema.org/pref/language"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "openid.sreg.language"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "twitter.lang"
msgstr "Vēlamā valoda"

# English string: Preferred language
msgid "windowslive.preferredLanguage"
msgstr "Vēlamā valoda"

# English string: Surname
msgid "facebook.last_name"
msgstr "Uzvārds"

# English string: Surname
msgid "linkedin.lastName"
msgstr "Uzvārds"

# English string: Surname
msgid "http://axschema.org/namePerson/last"
msgstr "Uzvārds"

# English string: Surname
msgid "windowslive.LastName"
msgstr "Uzvārds"

# English string: Surname
msgid "windowslive.surname"
msgstr "Uzvārds"

# English string: Telephone number
msgid "http://axschema.org/contact/phone/default"
msgstr "Telefons"

# English string: Telephone number
msgid "http://axschema.org/contact/phone/business"
msgstr "Telefons"

# English string: Title
msgid "linkedin.headline"
msgstr "Amats"

# English string: Title
msgid "http://axschema.org/company/title"
msgstr "Amats"

# English string: User ID
msgid "facebook.username"
msgstr "Lietotāja ID"

# English string: User ID
msgid "linkedin.id"
msgstr "Lietotāja ID"

# English string: User ID
msgid "twitter.screen_name"
msgstr "Lietotāja ID"

# English string: User ID
msgid "windowslive_uid"
msgstr "Lietotāja ID"

# English string: User ID
msgid "windowslive.id"
msgstr "Lietotāja ID"


#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:31+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: nl\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{admin:metadata_saml20-idp}"
msgstr "SAML 2.0 IdP Metadata"

msgid "{errors:descr_WRONGUSERPASS}"
msgstr ""
"De opgegeven gebruikersnaam bestaat niet, of het wachtwoord is ongeldig. "
"Verifieer de gebruikersnaam en probeer het nogmaals."

msgid "{logout:failed}"
msgstr "Uitloggen mislukt"

msgid "{status:attributes_header}"
msgstr "Uw attributen"

msgid "{admin:metaover_group_metadata.saml20-sp-remote}"
msgstr "SAML 2.0 Service Provider (Remote)"

msgid "{errors:descr_NOCERT}"
msgstr "Authenticatie niet gelukt: uw browser stuurde geen certificaat"

msgid "{errors:title_PROCESSASSERTION}"
msgstr "Fout in IdP response"

msgid "{errors:title_NOSTATE}"
msgstr "Toestandsinformatie verloren"

msgid "{login:username}"
msgstr "Gebruikersnaam"

msgid "Back"
msgstr "Terug"

msgid "{errors:title_METADATA}"
msgstr "Fout bij het laden van metadata"

msgid "{admin:metaconv_title}"
msgstr "Metadata parser"

msgid "{admin:cfg_check_noerrors}"
msgstr "Geen fouten gevonden."

msgid "{errors:descr_LOGOUTINFOLOST}"
msgstr ""
"De informatie over de huidige logout operatie is verloren gegaan. Je zou "
"nu moeten terugkeren naar de dienst waar je probeerde uit te loggen, om "
"het nogmaals te proberen. Deze fout kan optreden wanneer de logout "
"informatie is verlopen. De logout informatie wordt gedurende een beperkte"
" tijdsduur bewaard, normaal gesproken een aantal uren. Dit is langer dan "
"een normale logout operatie zou moeten duren, dus deze fout kan er op "
"wijzen dat er een configuratie probleem is. Als het probleem zich blijft "
"voordoen kun u contact opnemen met de Service Provider."

msgid "{disco:previous_auth}"
msgstr "Je hebt eerder gekozen voor authenticatie bij"

msgid "{admin:cfg_check_back}"
msgstr "Ga terug naar de lijst van files."

msgid "{errors:report_trackid}"
msgstr ""
"Wanneer je deze fout rapporteert, geef dan AUB ook de volgende tracking "
"ID door, waarmee het mogelijk is om jouw sessie in de logs terug te "
"vinden:"

msgid "{login:change_home_org_title}"
msgstr "Verander je organisatie"

msgid "{errors:descr_METADATANOTFOUND}"
msgstr "Kan geen metadata vinden voor %ENTITYID%"

msgid "{admin:metadata_metadata}"
msgstr "Metadata"

msgid "{errors:report_text}"
msgstr ""
"Voeg desgewenst je e-mailadres toe, zodat de beheerders contact kunnen "
"zoeken voor verder informatie over dit probleem:"

msgid "{errors:report_header}"
msgstr "Rapporteer fouten"

msgid "{login:change_home_org_text}"
msgstr ""
"Je hebt <b>%HOMEORG%</b> gekozen als je organisatie. Als dit niet correct"
" is kun je een andere keuze maken."

msgid "{errors:title_PROCESSAUTHNREQUEST}"
msgstr "Fout in Service Provider request"

msgid "{errors:descr_PROCESSASSERTION}"
msgstr "Het antwoord van de Identity Provider is niet geaccepteerd."

msgid "{errors:debuginfo_header}"
msgstr "Debuginformatie"

msgid "{admin:debug_sending_message_msg_text}"
msgstr ""
"Omdat u in debug mode bent, kunt u de inhoud van het bericht dat u "
"verstuurt inzien"

msgid "{errors:descr_RESPONSESTATUSNOSUCCESS}"
msgstr ""
"De Identity Provider antwoordde met een fout. (De statuscode in de SAML "
"Response was niet success)"

msgid "{admin:metadata_shib13-idp}"
msgstr "Shib 1.3 IdP Metadata"

msgid "{login:help_text}"
msgstr ""
"Zonder je gebruikersnaam en wachtwoord kun je je niet authenticeren en "
"dus niet gebruikmaken van deze dienst."

msgid "{logout:default_link_text}"
msgstr "Ga terug naar de SimpleSAMLphp installatiepagina"

msgid "{errors:error_header}"
msgstr "SimpleSAMLphp-fout"

msgid "{login:help_header}"
msgstr "Help! Ik weet mijn wachtwoord niet meer."

msgid "{errors:descr_LDAPERROR}"
msgstr ""
"De account database is in LDAP opgeslagen en bij het inloggen moet er "
"worden gecommuniceerd met een LDAP backend. Daarbij is een fout "
"opgetreden."

msgid "{errors:descr_METADATA}"
msgstr ""
"SimpleSAMLphp is niet goed geconfigureerd. De beheerder van deze dienst "
"dient de metadata configuratie te controleren."

msgid "{errors:title_BADREQUEST}"
msgstr "Incorrecte request ontvangen"

msgid "{status:sessionsize}"
msgstr "Sessiegrootte: %SIZE%"

msgid "{logout:title}"
msgstr "Uitgelogd"

msgid "{admin:metaover_group_metadata.adfs-sp-remote}"
msgstr "ADFS Service Provider (Remote)"

msgid "{admin:metaconv_xmlmetadata}"
msgstr "XML metadata"

msgid "{status:subject_format}"
msgstr "Formaat"

msgid "{admin:metaover_unknown_found}"
msgstr "De volgende velden zijn niet bekend"

msgid "{errors:title_AUTHSOURCEERROR}"
msgstr "Fout in authenticatiebron"

msgid "{login:select_home_org}"
msgstr "Kies je organisatie"

msgid "{logout:hold}"
msgstr "Vastgehouden"

msgid "{admin:cfg_check_header}"
msgstr "Configuratie-validatie"

msgid "{admin:debug_sending_message_send}"
msgstr "Verstuur bericht"

msgid "{status:logout}"
msgstr "Logout"

msgid "{errors:descr_DISCOPARAMS}"
msgstr ""
"De parameters die naar de discovery service zijn gestuurd, zijn niet "
"correct volgens de specificatie."

msgid "{errors:descr_CREATEREQUEST}"
msgstr "Er is een fout opgetreden bij het aanmaken van een SAML request."

msgid "{admin:metaover_optional_found}"
msgstr "Optionele velden"

msgid "{logout:return}"
msgstr "Terug naar service"

msgid "{admin:metadata_xmlurl}"
msgstr ""
"U kunt <a href=\"%METAURL%\">deze directe URL gebruiken</a> om de "
"metadata XML op te vragen:"

msgid "{logout:logout_all}"
msgstr "Ja, alle diensten"

msgid "{admin:debug_disable_debug_mode}"
msgstr ""
"U kunt debug mode uitschakelen in de globale SimpleSAMLphp configuratie "
"file <tt>config/config.php</tt>."

msgid "{disco:select}"
msgstr "Kies"

msgid "{logout:also_from}"
msgstr "Je bent ook ingelogd bij deze diensten:"

msgid "{login:login_button}"
msgstr "Inloggen"

msgid "{logout:progress}"
msgstr "Uitloggen..."

msgid "{login:error_wrongpassword}"
msgstr "Gebruikersnaam of wachtwoord niet bekend."

msgid "{admin:metaover_group_metadata.shib13-sp-remote}"
msgstr "Shib 1.3 Service Provider (Remote)"

msgid "{login:remember_username}"
msgstr "Onthoud gebruikersnaam"

msgid "{errors:descr_PROCESSAUTHNREQUEST}"
msgstr ""
"Deze IdP heeft een authenticatie verzoek ontvangen van een Service "
"Provider, maar er is een fout opgetreden bij het verwerken ervan."

msgid "{logout:logout_all_question}"
msgstr "Wil je uitloggen van alle bovenvermelde diensten?"

msgid "{errors:title_NOACCESS}"
msgstr "Geen toegang"

msgid "{login:error_nopassword}"
msgstr ""
"Je hebt wel iets ingetikt, maar blijkbaar is je wachtwoord niet "
"verstuurd. Probeer het opnieuw AUB."

msgid "{errors:title_NORELAYSTATE}"
msgstr "Geen RelayState"

msgid "{errors:descr_NOSTATE}"
msgstr ""
"Informatie over de toestand is verloren, en het verzoek kan niet herstart"
" worden"

msgid "{login:password}"
msgstr "Wachtwoord"

msgid "{errors:debuginfo_text}"
msgstr ""
"Onderstaande debuginformatie kan van belang zijn voor de beheerder / "
"helpdesk:"

msgid "{admin:cfg_check_missing}"
msgstr "Opties missen in de config file"

msgid "{errors:descr_UNHANDLEDEXCEPTION}"
msgstr "Een onverwachte foutmelding is opgetreden"

msgid "{general:yes}"
msgstr "Ja"

msgid "{errors:title_CONFIG}"
msgstr "Configuratie fout"

msgid "{errors:title_LOGOUTREQUEST}"
msgstr "Fout bij het verwerken van een Logout Request"

msgid "{admin:metaover_errorentry}"
msgstr "Fout in metadata"

msgid "{errors:title_METADATANOTFOUND}"
msgstr "Metadata niet gevonden"

msgid "{login:contact_info}"
msgstr "Contactinformatie"

msgid "{errors:title_UNHANDLEDEXCEPTION}"
msgstr "Onverwachte foutmelding"

msgid "{status:header_saml20_sp}"
msgstr "SAML 2.0 SP Demo"

msgid "{login:error_header}"
msgstr "Fout"

msgid "{errors:title_USERABORTED}"
msgstr "Authenticatie afgebroken"

msgid "{logout:incapablesps}"
msgstr ""
"Een of meer diensten waarop je bent inlogd hebben <i>geen ondersteuning "
"voor uitloggen</i>. Om er zeker van te zijn dat al je sessies zijn "
"beëindigd, kun je het beste <i>je webbrowser afsluiten</i>."

msgid "{admin:metadata_xmlformat}"
msgstr "In SAML 2.0 Metadata XML formaat:"

msgid "{admin:metaover_group_metadata.saml20-idp-remote}"
msgstr "SAML 2.0 Identity Provider (Remote)"

msgid "{admin:metaover_group_metadata.saml20-idp-hosted}"
msgstr "SAML 2.0 Identity Provider (Hosted)"

msgid "{status:subject_notset}"
msgstr "niet aanwezig"

msgid "{admin:metaover_required_found}"
msgstr "Verplichte velden"

msgid "{admin:cfg_check_select_file}"
msgstr "Selecteer een configuratiefile voor validatie:"

msgid "{errors:descr_UNKNOWNCERT}"
msgstr ""
"Authenticatie niet gelukt: het certificaat dat uw browser stuurde is "
"onbekend"

msgid "{logout:logging_out_from}"
msgstr "Uitloggen van de volgende diensten:"

msgid "{logout:loggedoutfrom}"
msgstr "Je bent nu succesvol uitgelogd van %SP%."

msgid "{errors:errorreport_text}"
msgstr "Het foutmeldingsrapport is verstuurd naar de beheerders"

msgid "{status:subject_header}"
msgstr "SAML Subject"

msgid "{errors:descr_LOGOUTREQUEST}"
msgstr "Er is een fout opgetreden tijdens het verwerken van een Logout Request."

msgid "{logout:success}"
msgstr "Je bent succesvol uitgelogd van de bovenvermelde services."

msgid "{admin:cfg_check_notices}"
msgstr "Opmerkingen"

msgid "{errors:descr_USERABORTED}"
msgstr "De authenticatie is afgebroken door de gebruiker"

msgid "{errors:descr_CASERROR}"
msgstr "Fout tijdens communicatie met de CAS server."

msgid "{general:no}"
msgstr "Nee"

msgid "{admin:metadata_saml20-sp}"
msgstr "SAML 2.0 SP Metadata"

msgid "{admin:metaconv_converted}"
msgstr "Geconverteerde metadata"

msgid "{logout:completed}"
msgstr "Voltooid"

msgid "{errors:descr_NOTSET}"
msgstr ""
"Het default wachtwoord in de configuratie (auth.adminpassword) is niet "
"aangepast; pas de configuratie aan aub."

msgid "{general:service_provider}"
msgstr "Service Provider"

msgid "{errors:descr_BADREQUEST}"
msgstr ""
"Er is een fout opgetreden in het verzoek voor deze pagina. De oorzaak is:"
" %REASON%"

msgid "{logout:no}"
msgstr "Nee"

msgid "{disco:icon_prefered_idp}"
msgstr "[Voorkeurskeuze]"

msgid "{general:no_cancel}"
msgstr "Nee, ik weiger"

msgid "{login:user_pass_header}"
msgstr "Geef je gebruikersnaam en wachtwoord"

msgid "{errors:report_explain}"
msgstr "Leg uit wat je deed toen deze foutmelding optrad..."

msgid "{errors:title_ACSPARAMS}"
msgstr "Geen SAML response gevonden"

msgid "{errors:descr_SLOSERVICEPARAMS}"
msgstr ""
"Je hebt de SingleLogoutService interface aangeroepen, maar hebt geen SAML"
" LogoutRequest of LogoutResponse meegestuurd."

msgid "{login:organization}"
msgstr "Organisatie"

msgid "{errors:title_WRONGUSERPASS}"
msgstr "Incorrecte gebruikersnaam of wachtwoord"

msgid "{admin:metaover_required_not_found}"
msgstr "De volgende verplichte velden konden niet worden gevonden"

msgid "{errors:descr_NOACCESS}"
msgstr ""
"Deze toegangsmogelijkheid is niet beschikbaar. Controleer de opties in de"
" configuratie van SimpleSAMLphp."

msgid "{errors:title_SLOSERVICEPARAMS}"
msgstr "Geen SAML bericht gevonden"

msgid "{errors:descr_ACSPARAMS}"
msgstr ""
"Je hebt de Assertion Consumer Service interface aangeroepen, maar hebt "
"geen SAML Authentication Response meegestuurd."

msgid "{admin:debug_sending_message_text_link}"
msgstr ""
"U gaat een bericht versturen. Klik op de Verstuur bericht link om door te"
" gaan."

msgid "{errors:descr_AUTHSOURCEERROR}"
msgstr "Fout in authenticatiebron %AUTHSOURCE%. Als reden werd gegeven: %REASON%."

msgid "{status:some_error_occurred}"
msgstr "Er is een fout opgetreden"

msgid "{login:change_home_org_button}"
msgstr "Kies je organisatie"

msgid "{admin:cfg_check_superfluous}"
msgstr "Teveel opties in de config file"

msgid "{errors:report_email}"
msgstr "E-mailadres:"

msgid "{errors:howto_header}"
msgstr "Hoe kan ik hulp vragen"

msgid "{errors:title_NOTSET}"
msgstr "Wachtwoord niet ingevuld"

msgid "{errors:descr_NORELAYSTATE}"
msgstr ""
"De afzender van deze request heeft geen RelayState parameter meegestuurd "
"om aan te geven wat de volgende bestemming is."

msgid "{status:header_diagnostics}"
msgstr "SimpleSAMLphp controle"

msgid "{status:intro}"
msgstr ""
"Dit is de overzichtspagina van SimpleSAMLphp. Hier kunt u zien of uw "
"sessie nog geldig is, hoe lang het nog duurt voordat deze verloopt, en u "
"kunt alle attributen bekijken die aanwezig zijn in deze sessie."

msgid "{errors:title_NOTFOUNDREASON}"
msgstr "Pagina niet gevonden"

msgid "{admin:debug_sending_message_title}"
msgstr "Bericht"

msgid "{errors:title_RESPONSESTATUSNOSUCCESS}"
msgstr "Foutmelding ontvangen van Identity Provider"

msgid "{admin:metadata_shib13-sp}"
msgstr "Shib 1.3 SP Metadata"

msgid "{admin:metaover_intro}"
msgstr "Klik op een SAML-entiteit om de details voor die entiteit te bekijken."

msgid "{errors:title_NOTVALIDCERT}"
msgstr "Ongeldig certificaat"

msgid "{general:remember}"
msgstr "Bewaar toestemming"

msgid "{disco:selectidp}"
msgstr "Kies je Identity Provider"

msgid "{login:help_desk_email}"
msgstr "Stuur een e-mail naar de helpdesk"

msgid "{login:help_desk_link}"
msgstr "Helpdesk homepage"

msgid "{login:remember_me}"
msgstr "Onthoud mij"

msgid "{errors:title_CASERROR}"
msgstr "CAS Fout"

msgid "{login:user_pass_text}"
msgstr ""
"Voor deze dienst is authenticatie vereist. Geef je gebruikersnaam en "
"wachtwoord in onderstaand formulier."

msgid "{errors:title_DISCOPARAMS}"
msgstr "Toegangsfout bij discovery service"

msgid "{general:yes_continue}"
msgstr "Ja, ik ga akkoord"

msgid "{disco:remember}"
msgstr "Onthoud mijn keuze"

msgid "{admin:metaover_group_metadata.saml20-sp-hosted}"
msgstr "SAML 2.0 Service Provider (Hosted)"

msgid "{admin:metadata_simplesamlformat}"
msgstr ""
"In SimpleSAMLphp flat file formaat - gebruik dit wanneer uw "
"federatiepartner ook SimpleSAMLphp gebruikt"

msgid "{admin:metadata_adfs-sp}"
msgstr "ADFS SP Metadata"

msgid "{disco:login_at}"
msgstr "Inloggen bij"

msgid "{errors:title_GENERATEAUTHNRESPONSE}"
msgstr "Authenticatie response kon niet worden aangemaakt"

msgid "{errors:errorreport_header}"
msgstr "Foutmeldingsrapport verstuurd"

msgid "{errors:title_CREATEREQUEST}"
msgstr "Fout bij nieuw request"

msgid "{admin:metaover_header}"
msgstr "Metadata-overzicht"

msgid "{errors:report_submit}"
msgstr "Verstuur het foutmeldingsrapport"

msgid "{errors:title_INVALIDCERT}"
msgstr "Ongeldig certificaat"

msgid "{errors:title_NOTFOUND}"
msgstr "Pagina niet gevonden"

msgid "{logout:logged_out_text}"
msgstr "U bent uitgelogd. Dank u voor het gebruiken van deze dienst."

msgid "{admin:metaover_group_metadata.shib13-sp-hosted}"
msgstr "Shib 1.3 Service Provider (Hosted)"

msgid "{admin:metadata_cert_intro}"
msgstr "Download de X509-certificaten in PEM-formaat."

msgid "{admin:debug_sending_message_msg_title}"
msgstr "Bericht"

msgid "{admin:metaover_group_metadata.adfs-idp-hosted}"
msgstr "ADFS Identity Provider (Hosted)"

msgid "{errors:title_UNKNOWNCERT}"
msgstr "Onbekend certificaat"

msgid "{errors:title_LDAPERROR}"
msgstr "LDAP Fout"

msgid "{logout:failedsps}"
msgstr ""
"Het was niet mogelijk bij een of meerdere diensten uit te loggen. Om alle"
" sessies te sluiten, raden wij u aan uw <i>webbrowser te af te "
"sluiten</i>."

msgid "{errors:descr_NOTFOUND}"
msgstr "Deze pagina bestaat niet. De URL was: %URL%"

msgid "{errors:howto_text}"
msgstr ""
"Deze foutmelding is waarschijnlijk ontstaan door onverwacht gedrag of "
"door verkeerde configuratie van SimpleSAMLphp. Meld dit bij de beheerder "
"van deze authenticatiedienst, en geef bovenstaande melding door."

msgid "{admin:metadata_adfs-idp}"
msgstr "ADFS IdP Metadata"

msgid "{admin:metaover_group_metadata.shib13-idp-hosted}"
msgstr "Shib 1.3 Identity Provider (Hosted)"

msgid "{errors:descr_NOTVALIDCERT}"
msgstr "Je hebt geen geldig certificaat meegegeven"

msgid "{admin:debug_sending_message_text_button}"
msgstr ""
"U gaat een bericht versturen. Klik op de Verstuur bericht knop om door te"
" gaan."

msgid "{admin:metaover_optional_not_found}"
msgstr "De volgende optionele velden konden niet worden gevonden"

msgid "{logout:logout_only}"
msgstr "Nee, alleen %SP%"

msgid "{login:next}"
msgstr "Volgende"

msgid "{errors:descr_GENERATEAUTHNRESPONSE}"
msgstr ""
"Tijdens het aanmaken van een authenticatie response door deze Identity "
"Provider is er een fout opgetreden."

msgid "{disco:selectidp_full}"
msgstr "Selecteer de Identity Provider waar je wil authenticeren:"

msgid "{errors:descr_NOTFOUNDREASON}"
msgstr ""
"De opgegeven pagina kon niet worden gevonden. De oorzaak is: %REASON%. De"
" URL is: %URL%"

msgid "{errors:title_NOCERT}"
msgstr "Geen certificaat"

msgid "{errors:title_LOGOUTINFOLOST}"
msgstr "Logout informatie is verloren gegaan"

msgid "{admin:metaover_group_metadata.shib13-idp-remote}"
msgstr "Shib 1.3 Identity Provider (Remote)"

msgid "{errors:descr_CONFIG}"
msgstr "SimpleSAMLphp is niet goed geconfigureerd."

msgid "{admin:metadata_intro}"
msgstr ""
"Dit is de metadata die automatisch is gegenereerd door SimpleSAMLphp. U "
"kunt deze metadata uitwisselen met uw federatiepartners."

msgid "{admin:metadata_cert}"
msgstr "Certificaten"

msgid "{errors:descr_INVALIDCERT}"
msgstr ""
"Authenticatie niet gelukt: uw browser stuurde een certificaat dat "
"ongeldig is of niet gelezen kon worden"

msgid "{status:header_shib}"
msgstr "Shibboleth demo"

msgid "{admin:metaconv_parse}"
msgstr "Parse"

msgid "Person's principal name at home organization"
msgstr "Persoons ID bij organisatie"

msgid "Superfluous options in config file"
msgstr "Teveel opties in de config file"

msgid "Mobile"
msgstr "Mobiel"

msgid "Shib 1.3 Service Provider (Hosted)"
msgstr "Shib 1.3 Service Provider (Hosted)"

msgid ""
"LDAP is the user database, and when you try to login, we need to contact "
"an LDAP database. An error occurred when we tried it this time."
msgstr ""
"De account database is in LDAP opgeslagen en bij het inloggen moet er "
"worden gecommuniceerd met een LDAP backend. Daarbij is een fout "
"opgetreden."

msgid ""
"Optionally enter your email address, for the administrators to be able "
"contact you for further questions about your issue:"
msgstr ""
"Voeg desgewenst je e-mailadres toe, zodat de beheerders contact kunnen "
"zoeken voor verder informatie over dit probleem:"

msgid "Display name"
msgstr "Weergavenaam"

msgid "Remember my choice"
msgstr "Onthoud mijn keuze"

msgid "Format"
msgstr "Formaat"

msgid "SAML 2.0 SP Metadata"
msgstr "SAML 2.0 SP Metadata"

msgid "ADFS IdP Metadata"
msgstr "ADFS IdP Metadata"

msgid "Notices"
msgstr "Opmerkingen"

msgid "Home telephone"
msgstr "Thuistelefoon"

msgid ""
"Hi, this is the status page of SimpleSAMLphp. Here you can see if your "
"session is timed out, how long it lasts until it times out and all the "
"attributes that are attached to your session."
msgstr ""
"Dit is de overzichtspagina van SimpleSAMLphp. Hier kunt u zien of uw "
"sessie nog geldig is, hoe lang het nog duurt voordat deze verloopt, en u "
"kunt alle attributen bekijken die aanwezig zijn in deze sessie."

msgid "Explain what you did when this error occurred..."
msgstr "Leg uit wat je deed toen deze foutmelding optrad..."

msgid "An unhandled exception was thrown."
msgstr "Een onverwachte foutmelding is opgetreden"

msgid "Invalid certificate"
msgstr "Ongeldig certificaat"

msgid "Service Provider"
msgstr "Service Provider"

msgid "Incorrect username or password."
msgstr "Gebruikersnaam of wachtwoord niet bekend."

msgid "There is an error in the request to this page. The reason was: %REASON%"
msgstr ""
"Er is een fout opgetreden in het verzoek voor deze pagina. De oorzaak is:"
" %REASON%"

msgid "E-mail address:"
msgstr "E-mailadres:"

msgid "Submit message"
msgstr "Verstuur bericht"

msgid "No RelayState"
msgstr "Geen RelayState"

msgid "Error creating request"
msgstr "Fout bij nieuw request"

msgid "Locality"
msgstr "Plaats"

msgid "Unhandled exception"
msgstr "Onverwachte foutmelding"

msgid "The following required fields was not found"
msgstr "De volgende verplichte velden konden niet worden gevonden"

msgid "Download the X509 certificates as PEM-encoded files."
msgstr "Download de X509-certificaten in PEM-formaat."

#, python-format
msgid "Unable to locate metadata for %ENTITYID%"
msgstr "Kan geen metadata vinden voor %ENTITYID%"

msgid "Organizational number"
msgstr "Organisatie nummer"

msgid "Password not set"
msgstr "Wachtwoord niet ingevuld"

msgid "SAML 2.0 IdP Metadata"
msgstr "SAML 2.0 IdP Metadata"

msgid "Post office box"
msgstr "Postbus"

msgid ""
"A service has requested you to authenticate yourself. Please enter your "
"username and password in the form below."
msgstr ""
"Voor deze dienst is authenticatie vereist. Geef je gebruikersnaam en "
"wachtwoord in onderstaand formulier."

msgid "CAS Error"
msgstr "CAS Fout"

msgid ""
"The debug information below may be of interest to the administrator / "
"help desk:"
msgstr ""
"Onderstaande debuginformatie kan van belang zijn voor de beheerder / "
"helpdesk:"

msgid ""
"Either no user with the given username could be found, or the password "
"you gave was wrong. Please check the username and try again."
msgstr ""
"De opgegeven gebruikersnaam bestaat niet, of het wachtwoord is ongeldig. "
"Verifieer de gebruikersnaam en probeer het nogmaals."

msgid "Error"
msgstr "Fout"

msgid "Next"
msgstr "Volgende"

msgid "Distinguished name (DN) of the person's home organizational unit"
msgstr "Distinguished name (DN) van de afdeling van de persoon"

msgid "State information lost"
msgstr "Toestandsinformatie verloren"

msgid ""
"The password in the configuration (auth.adminpassword) is not changed "
"from the default value. Please edit the configuration file."
msgstr ""
"Het default wachtwoord in de configuratie (auth.adminpassword) is niet "
"aangepast; pas de configuratie aan aub."

msgid "Converted metadata"
msgstr "Geconverteerde metadata"

msgid "Mail"
msgstr "E-mail"

msgid "No, cancel"
msgstr "Nee"

msgid ""
"You have chosen <b>%HOMEORG%</b> as your home organization. If this is "
"wrong you may choose another one."
msgstr ""
"Je hebt <b>%HOMEORG%</b> gekozen als je organisatie. Als dit niet correct"
" is kun je een andere keuze maken."

msgid "Error processing request from Service Provider"
msgstr "Fout in Service Provider request"

msgid "Distinguished name (DN) of person's primary Organizational Unit"
msgstr ""
"Distinguished name (DN) van de organisatie hoofdafdeling waartoe de "
"persoon behoort"

msgid ""
"To look at the details for an SAML entity, click on the SAML entity "
"header."
msgstr "Klik op een SAML-entiteit om de details voor die entiteit te bekijken."

msgid "Enter your username and password"
msgstr "Geef je gebruikersnaam en wachtwoord"

msgid "Login at"
msgstr "Inloggen bij"

msgid "No"
msgstr "Nee"

msgid "Home postal address"
msgstr "Werkadres"

msgid "WS-Fed SP Demo Example"
msgstr "WS-Fed SP Demo"

msgid "SAML 2.0 Identity Provider (Remote)"
msgstr "SAML 2.0 Identity Provider (Remote)"

msgid "Error processing the Logout Request"
msgstr "Fout bij het verwerken van een Logout Request"

msgid "Do you want to logout from all the services above?"
msgstr "Wil je uitloggen van alle bovenvermelde diensten?"

msgid "Select"
msgstr "Kies"

msgid "The authentication was aborted by the user"
msgstr "De authenticatie is afgebroken door de gebruiker"

msgid "Your attributes"
msgstr "Uw attributen"

msgid "Given name"
msgstr "Voornaam"

msgid "Identity assurance profile"
msgstr "Identiteitsverzekeringsprofiel"

msgid "SAML 2.0 SP Demo Example"
msgstr "SAML 2.0 SP Demo"

msgid "Logout information lost"
msgstr "Logout informatie is verloren gegaan"

msgid "Organization name"
msgstr "Organisatie naam"

msgid "Authentication failed: the certificate your browser sent is unknown"
msgstr ""
"Authenticatie niet gelukt: het certificaat dat uw browser stuurde is "
"onbekend"

msgid ""
"You are about to send a message. Hit the submit message button to "
"continue."
msgstr ""
"U gaat een bericht versturen. Klik op de Verstuur bericht knop om door te"
" gaan."

msgid "Home organization domain name"
msgstr "Unieke Organisatie ID"

msgid "Go back to the file list"
msgstr "Ga terug naar de lijst van files."

msgid "SAML Subject"
msgstr "SAML Subject"

msgid "Error report sent"
msgstr "Foutmeldingsrapport verstuurd"

msgid "Common name"
msgstr "Algemene naam"

msgid "Please select the identity provider where you want to authenticate:"
msgstr "Selecteer de Identity Provider waar je wil authenticeren:"

msgid "Logout failed"
msgstr "Uitloggen mislukt"

msgid "Identity number assigned by public authorities"
msgstr "Burgerservicenummer"

msgid "WS-Federation Identity Provider (Remote)"
msgstr "WS-Federation Identity Provider (Remote)"

msgid "Error received from Identity Provider"
msgstr "Foutmelding ontvangen van Identity Provider"

msgid "LDAP Error"
msgstr "LDAP Fout"

msgid ""
"The information about the current logout operation has been lost. You "
"should return to the service you were trying to log out from and try to "
"log out again. This error can be caused by the logout information "
"expiring. The logout information is stored for a limited amout of time - "
"usually a number of hours. This is longer than any normal logout "
"operation should take, so this error may indicate some other error with "
"the configuration. If the problem persists, contact your service "
"provider."
msgstr ""
"De informatie over de huidige logout operatie is verloren gegaan. Je zou "
"nu moeten terugkeren naar de dienst waar je probeerde uit te loggen, om "
"het nogmaals te proberen. Deze fout kan optreden wanneer de logout "
"informatie is verlopen. De logout informatie wordt gedurende een beperkte"
" tijdsduur bewaard, normaal gesproken een aantal uren. Dit is langer dan "
"een normale logout operatie zou moeten duren, dus deze fout kan er op "
"wijzen dat er een configuratie probleem is. Als het probleem zich blijft "
"voordoen kun u contact opnemen met de Service Provider."

msgid "Some error occurred"
msgstr "Er is een fout opgetreden"

msgid "Organization"
msgstr "Organisatie"

msgid "No certificate"
msgstr "Geen certificaat"

msgid "Choose home organization"
msgstr "Kies je organisatie"

msgid "Persistent pseudonymous ID"
msgstr "Persistente anonieme ID"

msgid "No SAML response provided"
msgstr "Geen SAML response gevonden"

msgid "No errors found."
msgstr "Geen fouten gevonden."

msgid "SAML 2.0 Service Provider (Hosted)"
msgstr "SAML 2.0 Service Provider (Hosted)"

msgid "The given page was not found. The URL was: %URL%"
msgstr "Deze pagina bestaat niet. De URL was: %URL%"

msgid "Configuration error"
msgstr "Configuratie fout"

msgid "Required fields"
msgstr "Verplichte velden"

msgid "An error occurred when trying to create the SAML request."
msgstr "Er is een fout opgetreden bij het aanmaken van een SAML request."

msgid ""
"This error probably is due to some unexpected behaviour or to "
"misconfiguration of SimpleSAMLphp. Contact the administrator of this "
"login service, and send them the error message above."
msgstr ""
"Deze foutmelding is waarschijnlijk ontstaan door onverwacht gedrag of "
"door verkeerde configuratie van SimpleSAMLphp. Meld dit bij de beheerder "
"van deze authenticatiedienst, en geef bovenstaande melding door."

#, python-format
msgid "Your session is valid for %remaining% seconds from now."
msgstr "Uw sessie is nog %remaining% seconden geldig vanaf dit moment."

msgid "Domain component (DC)"
msgstr "Domeincomponent"

msgid "Shib 1.3 Service Provider (Remote)"
msgstr "Shib 1.3 Service Provider (Remote)"

msgid "Password"
msgstr "Wachtwoord"

msgid "Nickname"
msgstr "Bijnaam"

msgid "Send error report"
msgstr "Verstuur het foutmeldingsrapport"

msgid ""
"Authentication failed: the certificate your browser sent is invalid or "
"cannot be read"
msgstr ""
"Authenticatie niet gelukt: uw browser stuurde een certificaat dat "
"ongeldig is of niet gelezen kon worden"

msgid "The error report has been sent to the administrators."
msgstr "Het foutmeldingsrapport is verstuurd naar de beheerders"

msgid "Date of birth"
msgstr "Geboortedatum"

msgid "Private information elements"
msgstr "Privé informatie-elementen"

msgid "You are also logged in on these services:"
msgstr "Je bent ook ingelogd bij deze diensten:"

msgid "SimpleSAMLphp Diagnostics"
msgstr "SimpleSAMLphp controle"

msgid "Debug information"
msgstr "Debuginformatie"

msgid "No, only %SP%"
msgstr "Nee, alleen %SP%"

msgid "Username"
msgstr "Gebruikersnaam"

msgid "Go back to SimpleSAMLphp installation page"
msgstr "Ga terug naar de SimpleSAMLphp installatiepagina"

msgid "You have successfully logged out from all services listed above."
msgstr "Je bent succesvol uitgelogd van de bovenvermelde services."

msgid "You are now successfully logged out from %SP%."
msgstr "Je bent nu succesvol uitgelogd van %SP%."

msgid "Affiliation"
msgstr "Affiliatie"

msgid "You have been logged out."
msgstr "U bent uitgelogd. Dank u voor het gebruiken van deze dienst."

msgid "Return to service"
msgstr "Terug naar service"

msgid "Logout"
msgstr "Logout"

msgid "State information lost, and no way to restart the request"
msgstr ""
"Informatie over de toestand is verloren, en het verzoek kan niet herstart"
" worden"

msgid "Error processing response from Identity Provider"
msgstr "Fout in IdP response"

msgid "WS-Federation Service Provider (Hosted)"
msgstr "WS-Federation Service Provider (Hosted)"

msgid "Remember my username"
msgstr "Onthoud gebruikersnaam"

msgid "Preferred language"
msgstr "Voorkeurstaal"

msgid "SAML 2.0 Service Provider (Remote)"
msgstr "SAML 2.0 Service Provider (Remote)"

msgid "Surname"
msgstr "Achternaam"

msgid "No access"
msgstr "Geen toegang"

msgid "The following fields was not recognized"
msgstr "De volgende velden zijn niet bekend"

msgid "Authentication error in source %AUTHSOURCE%. The reason was: %REASON%"
msgstr "Fout in authenticatiebron %AUTHSOURCE%. Als reden werd gegeven: %REASON%."

msgid "Bad request received"
msgstr "Incorrect request ontvangen"

msgid "User ID"
msgstr "Gebruikers ID"

msgid "JPEG Photo"
msgstr "JPEG-foto"

msgid "Postal address"
msgstr "Adres"

msgid "An error occurred when trying to process the Logout Request."
msgstr "Er is een fout opgetreden tijdens het verwerken van een Logout Request."

msgid "ADFS SP Metadata"
msgstr "ADFS SP Metadata"

msgid "Sending message"
msgstr "Bericht"

msgid "In SAML 2.0 Metadata XML format:"
msgstr "In SAML 2.0 Metadata XML formaat:"

msgid "Logging out of the following services:"
msgstr "Uitloggen van de volgende diensten:"

msgid ""
"When this identity provider tried to create an authentication response, "
"an error occurred."
msgstr ""
"Tijdens het aanmaken van een authenticatie response door deze Identity "
"Provider is er een fout opgetreden."

msgid "Could not create authentication response"
msgstr "Authenticatie response kon niet worden aangemaakt"

msgid "Labeled URI"
msgstr "URI"

msgid "SimpleSAMLphp appears to be misconfigured."
msgstr "SimpleSAMLphp is niet goed geconfigureerd."

msgid "Shib 1.3 Identity Provider (Hosted)"
msgstr "Shib 1.3 Identity Provider (Hosted)"

msgid "Metadata"
msgstr "Metadata"

msgid "Login"
msgstr "Inloggen"

msgid ""
"This Identity Provider received an Authentication Request from a Service "
"Provider, but an error occurred when trying to process the request."
msgstr ""
"Deze IdP heeft een authenticatie verzoek ontvangen van een Service "
"Provider, maar er is een fout opgetreden bij het verwerken ervan."

msgid "Yes, all services"
msgstr "Ja, alle diensten"

msgid "Logged out"
msgstr "Uitgelogd"

msgid "Postal code"
msgstr "Postcode"

msgid "Logging out..."
msgstr "Uitloggen..."

msgid "not set"
msgstr "niet aanwezig"

msgid "Metadata not found"
msgstr "Metadata niet gevonden"

msgid "SAML 2.0 Identity Provider (Hosted)"
msgstr "SAML 2.0 Identity Provider (Hosted)"

msgid "Primary affiliation"
msgstr "Primaire relatie"

msgid ""
"If you report this error, please also report this tracking number which "
"makes it possible to locate your session in the logs available to the "
"system administrator:"
msgstr ""
"Wanneer je deze fout rapporteert, geef dan AUB ook de volgende tracking "
"ID door, waarmee het mogelijk is om jouw sessie in de logs terug te "
"vinden:"

msgid "XML metadata"
msgstr "XML metadata"

msgid ""
"The parameters sent to the discovery service were not according to "
"specifications."
msgstr ""
"De parameters die naar de discovery service zijn gestuurd, zijn niet "
"correct volgens de specificatie."

msgid "Telephone number"
msgstr "Telefoonnummer"

msgid ""
"Unable to log out of one or more services. To ensure that all your "
"sessions are closed, you are encouraged to <i>close your webbrowser</i>."
msgstr ""
"Het was niet mogelijk bij een of meerdere diensten uit te loggen. Om alle"
" sessies te sluiten, raden wij u aan uw <i>webbrowser te af te "
"sluiten</i>."

msgid "Bad request to discovery service"
msgstr "Toegangsfout bij discovery service"

msgid "Select your identity provider"
msgstr "Kies je Identity Provider"

msgid "Group membership"
msgstr "Groepslidmaatschap"

msgid "Entitlement regarding the service"
msgstr "Recht"

msgid "Shib 1.3 SP Metadata"
msgstr "Shib 1.3 SP Metadata"

msgid ""
"As you are in debug mode, you get to see the content of the message you "
"are sending:"
msgstr ""
"Omdat u in debug mode bent, kunt u de inhoud van het bericht dat u "
"verstuurt inzien"

msgid "Certificates"
msgstr "Certificaten"

msgid "Remember"
msgstr "Bewaar toestemming"

msgid "Distinguished name (DN) of person's home organization"
msgstr "Distinguished name (DN) van de organisatie van de persoon"

msgid "You are about to send a message. Hit the submit message link to continue."
msgstr ""
"U gaat een bericht versturen. Klik op de Verstuur bericht link om door te"
" gaan."

msgid "Organizational unit"
msgstr "Afdeling"

msgid "Authentication aborted"
msgstr "Authenticatie afgebroken"

msgid "Local identity number"
msgstr "Identiteitsnummer"

msgid "Report errors"
msgstr "Rapporteer fouten"

msgid "Page not found"
msgstr "Pagina niet gevonden"

msgid "Shib 1.3 IdP Metadata"
msgstr "Shib 1.3 IdP Metadata"

msgid "Change your home organization"
msgstr "Verander je organisatie"

msgid "User's password hash"
msgstr "Password hash"

msgid ""
"In SimpleSAMLphp flat file format - use this if you are using a "
"SimpleSAMLphp entity on the other side:"
msgstr ""
"In SimpleSAMLphp flat file formaat - gebruik dit wanneer uw "
"federatiepartner ook SimpleSAMLphp gebruikt"

msgid "Yes, continue"
msgstr "Ja, ik ga akkoord"

msgid "Completed"
msgstr "Voltooid"

msgid ""
"The Identity Provider responded with an error. (The status code in the "
"SAML Response was not success)"
msgstr ""
"De Identity Provider antwoordde met een fout. (De statuscode in de SAML "
"Response was niet success)"

msgid "Error loading metadata"
msgstr "Fout bij het laden van metadata"

msgid "Select configuration file to check:"
msgstr "Selecteer een configuratiefile voor validatie:"

msgid "On hold"
msgstr "Vastgehouden"

msgid "ADFS Identity Provider (Hosted)"
msgstr "ADFS Identity Provider (Hosted)"

msgid "Error when communicating with the CAS server."
msgstr "Fout tijdens communicatie met de CAS server."

msgid "No SAML message provided"
msgstr "Geen SAML bericht gevonden"

msgid "Help! I don't remember my password."
msgstr "Help! Ik weet mijn wachtwoord niet meer."

msgid ""
"You can turn off debug mode in the global SimpleSAMLphp configuration "
"file <tt>config/config.php</tt>."
msgstr ""
"U kunt debug mode uitschakelen in de globale SimpleSAMLphp configuratie "
"file <tt>config/config.php</tt>."

msgid "How to get help"
msgstr "Hoe kan ik hulp vragen"

msgid ""
"You accessed the SingleLogoutService interface, but did not provide a "
"SAML LogoutRequest or LogoutResponse. Please note that this endpoint is "
"not intended to be accessed directly."
msgstr ""
"Je hebt de SingleLogoutService interface aangeroepen, maar hebt geen SAML"
" LogoutRequest of LogoutResponse meegestuurd."

msgid "SimpleSAMLphp error"
msgstr "SimpleSAMLphp-fout"

msgid ""
"One or more of the services you are logged into <i>do not support "
"logout</i>. To ensure that all your sessions are closed, you are "
"encouraged to <i>close your webbrowser</i>."
msgstr ""
"Een of meer diensten waarop je bent inlogd hebben <i>geen ondersteuning "
"voor uitloggen</i>. Om er zeker van te zijn dat al je sessies zijn "
"beëindigd, kun je het beste <i>je webbrowser afsluiten</i>."

msgid "Remember me"
msgstr "Onthoud mij"

msgid "Organization's legal name"
msgstr "Organisatie naam"

msgid "Options missing from config file"
msgstr "Opties missen in de config file"

msgid "The following optional fields was not found"
msgstr "De volgende optionele velden konden niet worden gevonden"

msgid "Authentication failed: your browser did not send any certificate"
msgstr "Authenticatie niet gelukt: uw browser stuurde geen certificaat"

msgid ""
"This endpoint is not enabled. Check the enable options in your "
"configuration of SimpleSAMLphp."
msgstr ""
"Deze toegangsmogelijkheid is niet beschikbaar. Controleer de opties in de"
" configuratie van SimpleSAMLphp."

msgid "You can <a href=\"%METAURL%\">get the metadata xml on a dedicated URL</a>:"
msgstr ""
"U kunt <a href=\"%METAURL%\">deze directe URL gebruiken</a> om de "
"metadata XML op te vragen:"

msgid "Street"
msgstr "Straat"

msgid ""
"There is some misconfiguration of your SimpleSAMLphp installation. If you"
" are the administrator of this service, you should make sure your "
"metadata configuration is correctly setup."
msgstr ""
"SimpleSAMLphp is niet goed geconfigureerd. De beheerder van deze dienst "
"dient de metadata configuratie te controleren."

msgid "Incorrect username or password"
msgstr "Incorrecte gebruikersnaam of wachtwoord"

msgid "Message"
msgstr "Bericht"

msgid "Contact information:"
msgstr "Contactinformatie"

msgid "Unknown certificate"
msgstr "Onbekend certificaat"

msgid "Legal name"
msgstr "Officiële naam"

msgid "Optional fields"
msgstr "Optionele velden"

msgid ""
"The initiator of this request did not provide a RelayState parameter "
"indicating where to go next."
msgstr ""
"De afzender van deze request heeft geen RelayState parameter meegestuurd "
"om aan te geven wat de volgende bestemming is."

msgid "You have previously chosen to authenticate at"
msgstr "Je hebt eerder gekozen voor authenticatie bij"

msgid ""
"You sent something to the login page, but for some reason the password "
"was not sent. Try again please."
msgstr ""
"Je hebt wel iets ingetikt, maar blijkbaar is je wachtwoord niet "
"verstuurd. Probeer het opnieuw AUB."

msgid "Fax number"
msgstr "Faxnummer"

msgid "Shibboleth demo"
msgstr "Shibboleth demo"

msgid "Error in this metadata entry"
msgstr "Fout in metadata"

msgid "Session size: %SIZE%"
msgstr "Sessiegrootte: %SIZE%"

msgid "Parse"
msgstr "Parse"

msgid ""
"Without your username and password you cannot authenticate "
"yourself for access to the service. There may be someone that can help "
"you. Consult the help desk at your organization!"
msgstr ""
"Zonder je gebruikersnaam en wachtwoord kun je je niet authenticeren en "
"dus niet gebruikmaken van deze dienst."

msgid "Metadata parser"
msgstr "Metadata parser"

msgid "ADFS Service Provider (Remote)"
msgstr "ADFS Service Provider (Remote)"

msgid "Choose your home organization"
msgstr "Kies je organisatie"

msgid "Send e-mail to help desk"
msgstr "Stuur een e-mail naar de helpdesk"

msgid "Metadata overview"
msgstr "Metadata-overzicht"

msgid "Title"
msgstr "Titel"

msgid "Manager"
msgstr "Manager"

msgid "You did not present a valid certificate."
msgstr "Je hebt geen geldig certificaat meegegeven"

msgid "Authentication source error"
msgstr "Fout in authenticatiebron"

msgid "Affiliation at home organization"
msgstr "Groep"

msgid "Help desk homepage"
msgstr "Helpdesk homepage"

msgid "Configuration check"
msgstr "Configuratie-validatie"

msgid "We did not accept the response sent from the Identity Provider."
msgstr "Het antwoord van de Identity Provider is niet geaccepteerd."

msgid "The given page was not found. The reason was: %REASON%  The URL was: %URL%"
msgstr ""
"De opgegeven pagina kon niet worden gevonden. De oorzaak is: %REASON%. De"
" URL is: %URL%"

msgid "Shib 1.3 Identity Provider (Remote)"
msgstr "Shib 1.3 Identity Provider (Remote)"

msgid ""
"Here is the metadata that SimpleSAMLphp has generated for you. You may "
"send this metadata document to trusted partners to setup a trusted "
"federation."
msgstr ""
"Dit is de metadata die automatisch is gegenereerd door SimpleSAMLphp. U "
"kunt deze metadata uitwisselen met uw federatiepartners."

msgid "[Preferred choice]"
msgstr "[Voorkeurskeuze]"

msgid "Organizational homepage"
msgstr "Organisatie homepage"

msgid ""
"You accessed the Assertion Consumer Service interface, but did not "
"provide a SAML Authentication Response. Please note that this endpoint is"
" not intended to be accessed directly."
msgstr ""
"Je hebt de Assertion Consumer Service interface aangeroepen, maar hebt "
"geen SAML Authentication Response meegestuurd."


msgid ""
"You are now accessing a pre-production system. This authentication setup "
"is for testing and pre-production verification only. If someone sent you "
"a link that pointed you here, and you are not <i>a tester</i> you "
"probably got the wrong link, and should <b>not be here</b>."
msgstr ""
"Je gaat nu een pre-productiesysteem gebruiken. Deze authenticatie is "
"uitsluitend opgezet voor testen en pre-productie-verfificatie. Als iemand"
" je een link hierheen stuurde, en je bent geen <i>tester</i>, dan is dit "
"waarschijnlijk een vergissing en zou je <b>niet hier moeten zijn</b>."

{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9a3fbedbe45ff74e1445dab8b7cb1cbc", "packages": [{"name": "gettext/gettext", "version": "v4.8.2", "source": {"type": "git", "url": "https://github.com/php-gettext/Gettext.git", "reference": "e474f872f2c8636cf53fd283ec4ce1218f3d236a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Gettext/zipball/e474f872f2c8636cf53fd283ec4ce1218f3d236a", "reference": "e474f872f2c8636cf53fd283ec4ce1218f3d236a", "shasum": ""}, "require": {"gettext/languages": "^2.3", "php": ">=5.4.0"}, "require-dev": {"illuminate/view": "*", "phpunit/phpunit": "^4.8|^5.7|^6.5", "squizlabs/php_codesniffer": "^3.0", "symfony/yaml": "~2", "twig/extensions": "*", "twig/twig": "^1.31|^2.0"}, "suggest": {"illuminate/view": "Is necessary if you want to use the Blade extractor", "symfony/yaml": "Is necessary if you want to use the Yaml extractor/generator", "twig/extensions": "Is necessary if you want to use the Twig extractor", "twig/twig": "Is necessary if you want to use the Twig extractor"}, "type": "library", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP gettext manager", "homepage": "https://github.com/oscarotero/Gettext", "keywords": ["JS", "gettext", "i18n", "mo", "po", "translation"], "time": "2019-12-02T10:21:14+00:00"}, {"name": "gettext/languages", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/php-gettext/Languages.git", "reference": "38ea0482f649e0802e475f0ed19fa993bcb7a618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Languages/zipball/38ea0482f649e0802e475f0ed19fa993bcb7a618", "reference": "38ea0482f649e0802e475f0ed19fa993bcb7a618", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16.0", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "bin": ["bin/export-plural-rules"], "type": "library", "autoload": {"psr-4": {"Gettext\\Languages\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "gettext languages with plural rules", "homepage": "https://github.com/php-gettext/Languages", "keywords": ["cldr", "i18n", "internationalization", "l10n", "language", "languages", "localization", "php", "plural", "plural rules", "plurals", "translate", "translations", "unicode"], "time": "2019-11-13T10:30:21+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.18", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "reference": "0a58ef6e3146256cc3dc7cc393927bcc7d1b72db", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2019-01-03T20:59:08+00:00"}, {"name": "phpfastcache/riak-client", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/PHPSocialNetwork/riak-php-client.git", "reference": "d771f75d16196006604a30bb15adc1c6a9b0fcc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPSocialNetwork/riak-php-client/zipball/d771f75d16196006604a30bb15adc1c6a9b0fcc9", "reference": "d771f75d16196006604a30bb15adc1c6a9b0fcc9", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.4"}, "conflict": {"basho/riak": "*"}, "require-dev": {"apigen/apigen": "4.1.*", "phpunit/phpunit": "4.8.*"}, "type": "library", "autoload": {"psr-4": {"Basho\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Georges.L", "email": "<EMAIL>", "homepage": "https://github.com/Geolim4", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/christophermancini", "role": "Former Lead <PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/alexmoore", "role": "Former <PERSON><PERSON><PERSON>"}], "description": "Riak client for PHP (Fork of the official basho/riak due to maintainer significant inactivity)", "homepage": "https://github.com/PHPSocialNetwork/riak-php-client", "keywords": ["basho", "client", "crdt", "data", "database", "datatype", "driver", "kv", "nosql", "riak"], "time": "2017-11-23T21:33:15+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.1.3", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "a25ae38e03de4ee4031725498a600012364787c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/a25ae38e03de4ee4031725498a600012364787c7", "reference": "a25ae38e03de4ee4031725498a600012364787c7", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "php": ">=5.5.0"}, "require-dev": {"doctrine/annotations": "^1.2", "friendsofphp/php-cs-fixer": "^2.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2019-11-21T09:37:46+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/log", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/446d54b4cb6bf489fc9d75f55843658e6f25d801", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2019-11-01T11:05:21+00:00"}, {"name": "robrichards/xmlseclibs", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/robrichards/xmlseclibs.git", "reference": "0a53d3c3aa87564910cae4ed01416441d3ae0db5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/robrichards/xmlseclibs/zipball/0a53d3c3aa87564910cae4ed01416441d3ae0db5", "reference": "0a53d3c3aa87564910cae4ed01416441d3ae0db5", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">= 5.4"}, "type": "library", "autoload": {"psr-4": {"RobRichards\\XMLSecLibs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A PHP library for XML Security", "homepage": "https://github.com/robrichards/xmlseclibs", "keywords": ["security", "signature", "xml", "xmldsig"], "time": "2019-11-05T11:44:22+00:00"}, {"name": "simplesamlphp/composer-module-installer", "version": "v1.1.6", "source": {"type": "git", "url": "https://github.com/simplesamlphp/composer-module-installer.git", "reference": "b70414a2112fe49e97a7eddd747657bd8bc38ef0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/composer-module-installer/zipball/b70414a2112fe49e97a7eddd747657bd8bc38ef0", "reference": "b70414a2112fe49e97a7eddd747657bd8bc38ef0", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "simplesamlphp/simplesamlphp": "*"}, "type": "composer-plugin", "extra": {"class": "SimpleSamlPhp\\Composer\\ModuleInstallerPlugin"}, "autoload": {"psr-0": {"SimpleSamlPhp\\Composer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "description": "A Composer plugin that allows installing SimpleSAMLphp modules through Composer.", "time": "2017-04-24T07:12:50+00:00"}, {"name": "simplesamlphp/saml2", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/simplesamlphp/saml2.git", "reference": "3806d276edb066c60aa3d748ffd0681d92ffbda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/saml2/zipball/3806d276edb066c60aa3d748ffd0681d92ffbda7", "reference": "3806d276edb066c60aa3d748ffd0681d92ffbda7", "shasum": ""}, "require": {"ext-dom": "*", "ext-openssl": "*", "ext-zlib": "*", "php": ">=5.4", "psr/log": "~1.0", "robrichards/xmlseclibs": "^3.0.4", "webmozart/assert": "^1.4"}, "require-dev": {"mockery/mockery": "~0.9", "phpmd/phpmd": "~2.6", "phpunit/phpunit": "~5.7", "sebastian/phpcpd": "~2.0", "sensiolabs/security-checker": "~4.1", "simplesamlphp/simplesamlphp-test-framework": "0.0.11", "squizlabs/php_codesniffer": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "v3.1.x-dev"}}, "autoload": {"psr-0": {"SAML2\\": "src/"}, "files": ["src/_autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "SAML2 PHP library from SimpleSAMLphp", "time": "2019-11-06T10:00:32+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-adfs", "version": "v0.9.5", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-adfs.git", "reference": "3ac7d15825e609152ca04faceea80ee0db3afcb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-adfs/zipball/3ac7d15825e609152ca04faceea80ee0db3afcb1", "reference": "3ac7d15825e609152ca04faceea80ee0db3afcb1", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\adfs\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that implements the WS-federation IDP", "keywords": ["adfs", "simplesamlphp"], "time": "2019-12-03T08:45:21+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authcrypt", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authcrypt.git", "reference": "cc2950cf710933063192e883ba2804321b8af6db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authcrypt/zipball/cc2950cf710933063192e883ba2804321b8af6db", "reference": "cc2950cf710933063192e883ba2804321b8af6db", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\authcrypt\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This module provides authentication against password hashes or .htpasswd files", "keywords": ["authcrypt", "simplesamlphp"], "time": "2019-12-03T08:56:36+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authfacebook", "version": "v0.9.2", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authfacebook.git", "reference": "661cc25ac21ea422552a2394ea35ce9e8873ce39"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authfacebook/zipball/661cc25ac21ea422552a2394ea35ce9e8873ce39", "reference": "661cc25ac21ea422552a2394ea35ce9e8873ce39", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.10"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\authfacebook\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to authenticate against Facebook", "keywords": ["facebook", "simplesamlphp"], "time": "2019-12-03T08:58:26+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authorize", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authorize.git", "reference": "470470373f352d0682b7e57ff41626fd0453c7c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authorize/zipball/470470373f352d0682b7e57ff41626fd0453c7c3", "reference": "470470373f352d0682b7e57ff41626fd0453c7c3", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\authorize\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "This module provides a user authorization filter based on attribute matching", "keywords": ["authorize", "simplesamlphp"], "time": "2019-05-29T14:32:20+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authtwitter", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authtwitter.git", "reference": "29a15e58061222632fea9eb2c807aef5e2c0d54a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authtwitter/zipball/29a15e58061222632fea9eb2c807aef5e2c0d54a", "reference": "29a15e58061222632fea9eb2c807aef5e2c0d54a", "shasum": ""}, "require": {"php": ">=5.5", "simplesamlphp/composer-module-installer": "~1.0", "simplesamlphp/simplesamlphp-module-oauth": "^0.9"}, "require-dev": {"phpunit/phpunit": "~4.8.35", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\authtwitter\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to perform authentication against Twitter", "keywords": ["simplesamlphp", "twitter"], "time": "2019-12-03T09:00:09+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authwindowslive", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authwindowslive.git", "reference": "f40aecec6c0adaedb6693309840c98cec783876e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authwindowslive/zipball/f40aecec6c0adaedb6693309840c98cec783876e", "reference": "f40aecec6c0adaedb6693309840c98cec783876e", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\authwindowslive\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to perform authentication against Windows Live", "keywords": ["live", "simplesamlphp", "windows", "windowslive"], "time": "2019-12-03T09:01:13+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authx509", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authX509.git", "reference": "32f4fb3822b4325fdccbff824996e82fa1042e0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authX509/zipball/32f4fb3822b4325fdccbff824996e82fa1042e0d", "reference": "32f4fb3822b4325fdccbff824996e82fa1042e0d", "shasum": ""}, "require": {"php": ">=5.5", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9"}, "require-dev": {"phpunit/phpunit": "~4.8.36", "simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.7"}, "type": "simplesamlphp-module", "extra": {"ssp-mixedcase-module-name": "authX509"}, "autoload": {"psr-4": {"SimpleSAML\\Module\\authX509\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "Jo<PERSON><PERSON>@surfnet.nl"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to authenticate users based on X509 client certificates", "keywords": ["simplesamlphp", "x509"], "time": "2019-12-03T08:48:01+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-authyubikey", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-authyubikey.git", "reference": "8c27bfeb4981d2e6fa40a831e945f40c5a4ad3d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-authyubikey/zipball/8c27bfeb4981d2e6fa40a831e945f40c5a4ad3d2", "reference": "8c27bfeb4981d2e6fa40a831e945f40c5a4ad3d2", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "extra": {"ssp-mixedcase-module-name": "auth<PERSON><PERSON><PERSON>"}, "autoload": {"psr-4": {"SimpleSAML\\modules\\yubikey\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to authenticate against YubiKey", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "simplesamlphp"], "time": "2019-12-03T08:52:49+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-cas", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-cas.git", "reference": "63b72e4600550c507cdfc32fdd208ad59a64321e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-cas/zipball/63b72e4600550c507cdfc32fdd208ad59a64321e", "reference": "63b72e4600550c507cdfc32fdd208ad59a64321e", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\cas\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A module that provides CAS authentication", "keywords": ["cas", "simplesamlphp"], "time": "2019-12-03T09:03:06+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-cdc", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-cdc.git", "reference": "16a5bfac7299e04e5feb472af328e07598708166"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-cdc/zipball/16a5bfac7299e04e5feb472af328e07598708166", "reference": "16a5bfac7299e04e5feb472af328e07598708166", "shasum": ""}, "require": {"simplesamlphp/composer-module-installer": ">=1.1.6"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\cdc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A SimpleSAMLphp module that allows integration with CDC", "homepage": "https://simplesamlphp.org/", "keywords": ["cdc", "simplesamlphp"], "time": "2019-12-03T09:04:11+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-consent", "version": "v0.9.4", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-consent.git", "reference": "bb9e9af9ae5ffaf7bf5047793be9401f873cb91d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-consent/zipball/bb9e9af9ae5ffaf7bf5047793be9401f873cb91d", "reference": "bb9e9af9ae5ffaf7bf5047793be9401f873cb91d", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\consent\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A module that will ask for user consent before releasing attributes", "keywords": ["consent", "simplesamlphp"], "time": "2019-12-03T09:05:12+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-consentadmin", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-consentadmin.git", "reference": "466e8d0d751f0080162d78e63ab2e125b24d17a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-consentadmin/zipball/466e8d0d751f0080162d78e63ab2e125b24d17a1", "reference": "466e8d0d751f0080162d78e63ab2e125b24d17a1", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-consent": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "extra": {"ssp-mixedcase-module-name": "consent<PERSON>dmin"}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A module that allows users to manage their consent", "keywords": ["<PERSON><PERSON><PERSON>", "simplesamlphp"], "time": "2019-12-03T09:06:40+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-discopower", "version": "v0.9.2", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-discopower.git", "reference": "016dc96a3f83414f49cbfa40079c0b137fa656b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-discopower/zipball/016dc96a3f83414f49cbfa40079c0b137fa656b2", "reference": "016dc96a3f83414f49cbfa40079c0b137fa656b2", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4 <1.6"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\modules\\discopower\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fancy tabbed discovery service with filtering capabilities where SPs can have different sets of metadata listed", "keywords": ["discopower", "discovery", "simplesamlphp"], "time": "2019-12-03T09:07:50+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-exampleattributeserver", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-exampleattributeserver.git", "reference": "63e0323e81c32bc3c9eaa01ea45194bb10153708"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-exampleattributeserver/zipball/63e0323e81c32bc3c9eaa01ea45194bb10153708", "reference": "63e0323e81c32bc3c9eaa01ea45194bb10153708", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\exampleattributeserver\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An example for SAML attributes queries", "keywords": ["exampleattributeserver", "simplesamlphp"], "time": "2019-05-28T12:37:15+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-expirycheck", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-expirycheck.git", "reference": "ee92306763729b05256ced90acb0f7eca20b7e3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-expirycheck/zipball/ee92306763729b05256ced90acb0f7eca20b7e3f", "reference": "ee92306763729b05256ced90acb0f7eca20b7e3f", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\expirycheck\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The expirycheck module validates user's expiry date", "keywords": ["expirycheck", "simplesamlphp"], "time": "2019-12-03T09:17:44+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-ldap", "version": "v0.9.4", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-ldap.git", "reference": "21301b3fcd7bc6147acdc673ada9e17e5282e908"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-ldap/zipball/21301b3fcd7bc6147acdc673ada9e17e5282e908", "reference": "21301b3fcd7bc6147acdc673ada9e17e5282e908", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "suggest": {"ext-ldap": "Needed when using LDAP authentication in SimpleSAMLphp"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\ldap\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that provides authentication against LDAP stores", "keywords": ["ldap", "simplesamlphp"], "time": "2019-12-03T12:01:56+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-memcachemonitor", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-memcachemonitor.git", "reference": "0e08e87707cd7b1fb91bbcf65cc454d8849571b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-memcachemonitor/zipball/0e08e87707cd7b1fb91bbcf65cc454d8849571b0", "reference": "0e08e87707cd7b1fb91bbcf65cc454d8849571b0", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "~0.0.6"}, "type": "simplesamlphp-module", "extra": {"ssp-mixedcase-module-name": "memcacheMonitor"}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able display usage statistics of a memcache(d) store", "keywords": ["memcachemonitor", "simplesamlphp"], "time": "2019-12-03T09:19:35+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-memcookie", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-memcookie.git", "reference": "fb17b5e5ff507fac166df3fe6d79b5c27feec3c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-memcookie/zipball/fb17b5e5ff507fac166df3fe6d79b5c27feec3c1", "reference": "fb17b5e5ff507fac166df3fe6d79b5c27feec3c1", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": ">=1.1.6"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.6"}, "type": "simplesamlphp-module", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A SimpleSAMLphp module that allows integration with Auth MemCookie, allowing web applications written in other languages than PHP to integrate with SimpleSAMLphp.", "homepage": "https://simplesamlphp.org/", "keywords": ["<PERSON>th MemCookie", "apache", "cookies", "simplesamlphp"], "time": "2019-12-14T19:47:30+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-metarefresh", "version": "v0.9.3", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-metarefresh.git", "reference": "3451ed118b7ebf7ba2657cff267c82c684b19ebe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-metarefresh/zipball/3451ed118b7ebf7ba2657cff267c82c684b19ebe", "reference": "3451ed118b7ebf7ba2657cff267c82c684b19ebe", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\metarefresh\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The metarefresh module will download and parse metadata documents and store them locally", "keywords": ["metarefresh", "simplesamlphp"], "time": "2019-12-03T09:20:40+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-negotiate", "version": "v0.9.4", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-negotiate.git", "reference": "08998d51b38592c5e90bfdcb61c91a8255b35f5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-negotiate/zipball/08998d51b38592c5e90bfdcb61c91a8255b35f5f", "reference": "08998d51b38592c5e90bfdcb61c91a8255b35f5f", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "suggest": {"ext-krb5": "Needed in case the SimpleSAMLphp negotiate module is used"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\negotiate\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Negotiate module implements Microsofts Kerberos SPNEGO mechanism", "keywords": ["negotiate", "simplesamlphp"], "time": "2019-11-20T08:50:01+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-oauth", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-oauth.git", "reference": "17450420b5d4c1810055b8ab655cc4d045a0c477"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-oauth/zipball/17450420b5d4c1810055b8ab655cc4d045a0c477", "reference": "17450420b5d4c1810055b8ab655cc4d045a0c477", "shasum": ""}, "require": {"simplesamlphp/composer-module-installer": ">=1.1.6"}, "require-dev": {"phpunit/phpunit": "~4.8.36", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.1"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A SimpleSAMLphp module that allows integration with OAuth1,", "homepage": "https://simplesamlphp.org/", "keywords": ["oauth1", "simplesamlphp"], "time": "2019-12-03T09:22:08+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-preprodwarning", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-preprodwarning.git", "reference": "925ef60b51a7230286b390c0abc0e815d8b9768e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-preprodwarning/zipball/925ef60b51a7230286b390c0abc0e815d8b9768e", "reference": "925ef60b51a7230286b390c0abc0e815d8b9768e", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17", "webmozart/assert": "^1.4"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\preprodwarning\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Display a warning when using a pre-production environment", "keywords": ["preprodwarning", "simplesamlphp"], "time": "2019-12-03T09:17:47+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-radius", "version": "v0.9.3", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-radius.git", "reference": "36bd0f39f9a13f7eb96ead97c97c3634aa1c3f2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-radius/zipball/36bd0f39f9a13f7eb96ead97c97c3634aa1c3f2d", "reference": "36bd0f39f9a13f7eb96ead97c97c3634aa1c3f2d", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.7"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\radius\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A module that is able perform authentication against a RADIUS server", "keywords": ["radius", "simplesamlphp"], "time": "2019-10-03T18:13:07+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-riak", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-riak.git", "reference": "c1a9d9545cb4e05b9205b34624850bb777aca991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-riak/zipball/c1a9d9545cb4e05b9205b34624850bb777aca991", "reference": "c1a9d9545cb4e05b9205b34624850bb777aca991", "shasum": ""}, "require": {"php": ">=5.6", "phpfastcache/riak-client": "^3.4", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\riak\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A module that is able to store key/value pairs in a Riak store", "keywords": ["riak", "simplesamlphp"], "time": "2019-12-03T08:28:45+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-sanitycheck", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-sanitycheck.git", "reference": "1efbeab5df8e616522690bcc6e49a99436a748b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-sanitycheck/zipball/1efbeab5df8e616522690bcc6e49a99436a748b9", "reference": "1efbeab5df8e616522690bcc6e49a99436a748b9", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\sanitycheck\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Perform sanity checks on configuration", "keywords": ["sanitycheck", "simplesamlphp"], "time": "2019-05-28T12:19:05+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-smartattributes", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-smartattributes.git", "reference": "b45d3ecd916e359a9cae05f9ae9df09b5c42f4e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-smartattributes/zipball/b45d3ecd916e359a9cae05f9ae9df09b5c42f4e6", "reference": "b45d3ecd916e359a9cae05f9ae9df09b5c42f4e6", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\smartattributes\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SmartAttributes module provides additional authentication processing filters to manipulate attributes.", "keywords": ["simplesamlphp", "smartattributes"], "time": "2019-12-03T09:24:09+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-sqlauth", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-sqlauth.git", "reference": "31bce8763ad97f4b4473e4ad4a5a96ddc136ef6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-sqlauth/zipball/31bce8763ad97f4b4473e4ad4a5a96ddc136ef6b", "reference": "31bce8763ad97f4b4473e4ad4a5a96ddc136ef6b", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17", "webmozart/assert": "^1.4"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\sqlauth\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This is a authentication module for authenticating a user against a SQL database", "keywords": ["simplesamlphp", "sqlauth"], "time": "2019-12-03T09:07:09+00:00"}, {"name": "simplesamlphp/simplesamlphp-module-statistics", "version": "v0.9.4", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-module-statistics.git", "reference": "1bb1e46921d8dc84707bc9cd3c307c8abd723ac7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-module-statistics/zipball/1bb1e46921d8dc84707bc9cd3c307c8abd723ac7", "reference": "1bb1e46921d8dc84707bc9cd3c307c8abd723ac7", "shasum": ""}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "^1.4"}, "require-dev": {"phpunit/phpunit": "~5.7", "simplesamlphp/simplesamlphp": "^1.17", "simplesamlphp/simplesamlphp-test-framework": "^0.0.12"}, "type": "simplesamlphp-module", "autoload": {"psr-4": {"SimpleSAML\\Module\\statistics\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SimpleSAMLphp statistics module", "keywords": ["simplesamlphp", "statistics"], "time": "2019-12-03T08:42:27+00:00"}, {"name": "simplesamlphp/twig-configurable-i18n", "version": "v2.2", "source": {"type": "git", "url": "https://github.com/simplesamlphp/twig-configurable-i18n.git", "reference": "b036c134157ce40ed66da2fc9d01f63e3b1d3abd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/twig-configurable-i18n/zipball/b036c134157ce40ed66da2fc9d01f63e3b1d3abd", "reference": "b036c134157ce40ed66da2fc9d01f63e3b1d3abd", "shasum": ""}, "require": {"twig/extensions": "^1.5"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ~7.5", "twig/twig": "^1.37 || ^2.7"}, "type": "project", "autoload": {"psr-4": {"SimpleSAML\\TwigConfigurableI18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "This is an extension on top of <PERSON><PERSON>'s i18n extension, allowing you to customize which functions to use for translations.", "keywords": ["extension", "gettext", "i18n", "internationalization", "translation", "twig"], "time": "2019-07-09T08:35:44+00:00"}, {"name": "symfony/config", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "a599a867d0e4a07c342b5f1e656b3915a540ddbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/a599a867d0e4a07c342b5f1e656b3915a540ddbe", "reference": "a599a867d0e4a07c342b5f1e656b3915a540ddbe", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2019-12-01T10:45:41+00:00"}, {"name": "symfony/debug", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "f72e33fdb1170b326e72c3157f0cd456351dd086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/f72e33fdb1170b326e72c3157f0cd456351dd086", "reference": "f72e33fdb1170b326e72c3157f0cd456351dd086", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2019-10-24T15:33:53+00:00"}, {"name": "symfony/dependency-injection", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "0d201916bfb3af939fec3c0c8815ea16c60ac1a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/0d201916bfb3af939fec3c0c8815ea16c60ac1a2", "reference": "0d201916bfb3af939fec3c0c8815ea16c60ac1a2", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/container": "^1.0"}, "conflict": {"symfony/config": "<3.3.7", "symfony/finder": "<3.3", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2019-12-01T08:33:36+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "f9031c22ec127d4a2450760f81a8677fe8a10177"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/f9031c22ec127d4a2450760f81a8677fe8a10177", "reference": "f9031c22ec127d4a2450760f81a8677fe8a10177", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2019-10-24T15:33:53+00:00"}, {"name": "symfony/filesystem", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "00cdad0936d06fab136944bc2342b762b1c3a4a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/00cdad0936d06fab136944bc2342b762b1c3a4a2", "reference": "00cdad0936d06fab136944bc2342b762b1c3a4a2", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2019-11-25T16:36:22+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "d2d0cfe8e319d9df44c4cca570710fcf221d4593"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/d2d0cfe8e319d9df44c4cca570710fcf221d4593", "reference": "d2d0cfe8e319d9df44c4cca570710fcf221d4593", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2019-11-28T12:52:59+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "c42c8339acb28cfff0fb1786948db4d23d609ff7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/c42c8339acb28cfff0fb1786948db4d23d609ff7", "reference": "c42c8339acb28cfff0fb1786948db4d23d609ff7", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2019-12-01T13:50:37+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.13.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "f8f0b461be3385e56d6de3dbb5a0df24c0c275e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/f8f0b461be3385e56d6de3dbb5a0df24c0c275e3", "reference": "f8f0b461be3385e56d6de3dbb5a0df24c0c275e3", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2019-11-27T13:56:44+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.13.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "7b4aab9743c30be783b73de055d24a39cf4b954f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/7b4aab9743c30be783b73de055d24a39cf4b954f", "reference": "7b4aab9743c30be783b73de055d24a39cf4b954f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-11-27T14:18:11+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.13.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "53dd1cdf3cb986893ccf2b96665b25b3abb384f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/53dd1cdf3cb986893ccf2b96665b25b3abb384f4", "reference": "53dd1cdf3cb986893ccf2b96665b25b3abb384f4", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-11-27T13:56:44+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.13.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "af23c7bb26a73b850840823662dda371484926c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/af23c7bb26a73b850840823662dda371484926c4", "reference": "af23c7bb26a73b850840823662dda371484926c4", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-11-27T13:56:44+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.13.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "964a67f293b66b95883a5ed918a65354fcd2258f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/964a67f293b66b95883a5ed918a65354fcd2258f", "reference": "964a67f293b66b95883a5ed918a65354fcd2258f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2019-11-27T13:56:44+00:00"}, {"name": "symfony/routing", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "b689ccd48e234ea404806d94b07eeb45f9f6f06a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/b689ccd48e234ea404806d94b07eeb45f9f6f06a", "reference": "b689ccd48e234ea404806d94b07eeb45f9f6f06a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2019-12-01T08:33:36+00:00"}, {"name": "symfony/yaml", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "dab657db15207879217fc81df4f875947bf68804"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/dab657db15207879217fc81df4f875947bf68804", "reference": "dab657db15207879217fc81df4f875947bf68804", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2019-10-24T15:33:53+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v1.42.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "e587180584c3d2d6cb864a0454e777bb6dcb6152"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/e587180584c3d2d6cb864a0454e777bb6dcb6152", "reference": "e587180584c3d2d6cb864a0454e777bb6dcb6152", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/debug": "^3.4|^4.2", "symfony/phpunit-bridge": "^4.4@dev|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.42-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "homepage": "https://twig.symfony.com/contributors", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2019-11-11T16:49:32+00:00"}, {"name": "webmozart/assert", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "88e6d84706d09a236046d686bbea96f07b3a34f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/88e6d84706d09a236046d686bbea96f07b3a34f4", "reference": "88e6d84706d09a236046d686bbea96f07b3a34f4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2019-08-24T08:43:50+00:00"}, {"name": "whitehat101/apr1-md5", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/whitehat101/apr1-md5.git", "reference": "8b261c9fc0481b4e9fa9d01c6ca70867b5d5e819"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/whitehat101/apr1-md5/zipball/8b261c9fc0481b4e9fa9d01c6ca70867b5d5e819", "reference": "8b261c9fc0481b4e9fa9d01c6ca70867b5d5e819", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.0.*"}, "type": "library", "autoload": {"psr-4": {"WhiteHat101\\Crypt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Apache's APR1-MD5 algorithm in pure PHP", "homepage": "https://github.com/whitehat101/apr1-md5", "keywords": ["MD5", "apr1"], "time": "2015-02-11T11:06:42+00:00"}], "packages-dev": [{"name": "composer/ca-bundle", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "10bb96592168a0f8e8f6dcde3532d9fa50b0b527"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/10bb96592168a0f8e8f6dcde3532d9fa50b0b527", "reference": "10bb96592168a0f8e8f6dcde3532d9fa50b0b527", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2019-08-30T08:44:50+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "cbe23383749496fe0f373345208b79568e4bc248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/cbe23383749496fe0f373345208b79568e4bc248", "reference": "cbe23383749496fe0f373345208b79568e4bc248", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2019-11-06T16:40:04+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14T21:17:01+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5", "reference": "dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2019-12-07T18:20:45+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "mikey179/vfsstream", "version": "v1.6.8", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "231c73783ebb7dd9ec77916c10037eff5a2b6efe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/231c73783ebb7dd9ec77916c10037eff5a2b6efe", "reference": "231c73783ebb7dd9ec77916c10037eff5a2b6efe", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.5|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "time": "2019-10-30T15:31:00+00:00"}, {"name": "muglug/package-versions-56", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/muglug/PackageVersions.git", "reference": "a67bed26deaaf9269a348e53063bc8d4dcc60ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/muglug/PackageVersions/zipball/a67bed26deaaf9269a348e53063bc8d4dcc60ffd", "reference": "a67bed26deaaf9269a348e53063bc8d4dcc60ffd", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "php": "^5.6 || ^7.0"}, "require-dev": {"composer/composer": "^1.3", "ext-zip": "*", "phpunit/phpunit": "^5.7.5"}, "type": "composer-plugin", "extra": {"class": "Muglug\\PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Muglug\\PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A backport of ocramius/package-versions that supports php ^5.6. Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "time": "2018-03-26T03:22:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "reference": "3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^4.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2017-10-19T19:58:43+00:00"}, {"name": "nikic/php-parser", "version": "v3.1.5", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "bb87e28e7d7b8d9a7fda231d37457c9210faf6ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/bb87e28e7d7b8d9a7fda231d37457c9210faf6ce", "reference": "bb87e28e7d7b8d9a7fda231d37457c9210faf6ce", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2018-02-28T20:30:58+00:00"}, {"name": "openlss/lib-array2xml", "version": "0.5.1", "source": {"type": "git", "url": "https://github.com/nullivex/lib-array2xml.git", "reference": "c8b5998a342d7861f2e921403f44e0a2f3ef2be0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nullivex/lib-array2xml/zipball/c8b5998a342d7861f2e921403f44e0a2f3ef2be0", "reference": "c8b5998a342d7861f2e921403f44e0a2f3ef2be0", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"LSS": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bryantong.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://openlss.org"}], "description": "Array2XML conversion library credit to lalit.org", "homepage": "http://openlss.org", "keywords": ["array", "array conversion", "xml", "xml conversion"], "time": "2016-11-10T19:10:18+00:00"}, {"name": "php-coveralls/php-coveralls", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/php-coveralls/php-coveralls.git", "reference": "3e6420fa666ef7bae5e750ddeac903153e193bae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-coveralls/php-coveralls/zipball/3e6420fa666ef7bae5e750ddeac903153e193bae", "reference": "3e6420fa666ef7bae5e750ddeac903153e193bae", "shasum": ""}, "require": {"ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.0", "php": "^5.5 || ^7.0", "psr/log": "^1.0", "symfony/config": "^2.1 || ^3.0 || ^4.0 || ^5.0", "symfony/console": "^2.1 || ^3.0 || ^4.0 || ^5.0", "symfony/stopwatch": "^2.0 || ^3.0 || ^4.0 || ^5.0", "symfony/yaml": "^2.0.5 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.0"}, "suggest": {"symfony/http-kernel": "Allows Symfony integration"}, "bin": ["bin/php-coveralls"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"PhpCoveralls\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.facebook.com/satooshi.jp", "role": "Original creator"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Google Inc"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/keradus"}, {"name": "Contributors", "homepage": "https://github.com/php-coveralls/php-coveralls/graphs/contributors"}], "description": "PHP client library for Coveralls API", "homepage": "https://github.com/php-coveralls/php-coveralls", "keywords": ["ci", "coverage", "github", "test"], "time": "2019-11-20T16:29:20+00:00"}, {"name": "php-cs-fixer/diff", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "78bb099e9c16361126c86ce82ec4405ebab8e756"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/78bb099e9c16361126c86ce82ec4405ebab8e756", "reference": "78bb099e9c16361126c86ce82ec4405ebab8e756", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3", "symfony/process": "^3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "SpacePossum"}], "description": "sebastian/diff v2 backport support for PHP5.6", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "time": "2018-02-15T16:58:55+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2017-09-11T18:02:19+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/bf329f6c1aadea3299f08ee804682b7c45b326a2", "reference": "bf329f6c1aadea3299f08ee804682b7c45b326a2", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2017-11-10T14:09:06+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/9c977708995954784726e25d0cd1dddf4e65b0f7", "reference": "9c977708995954784726e25d0cd1dddf4e65b0f7", "shasum": ""}, "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2017-07-14T14:27:02+00:00"}, {"name": "phpspec/prophecy", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "f6811d96d97bdf400077a0cc100ae56aa32b9203"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/f6811d96d97bdf400077a0cc100ae56aa32b9203", "reference": "f6811d96d97bdf400077a0cc100ae56aa32b9203", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2019-10-03T11:07:50+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/1ce90ba27c42e4e44e6d8458241466380b51fa16", "reference": "1ce90ba27c42e4e44e6d8458241466380b51fa16", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2017-12-04T08:55:13+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2018-02-01T05:50:59+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2017-06-30T09:13:00+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "sensiolabs/security-checker", "version": "v5.0.3", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "46be3f58adac13084497961e10eed9a7fb4d44d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/46be3f58adac13084497961e10eed9a7fb4d44d1", "reference": "46be3f58adac13084497961e10eed9a7fb4d44d1", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "php": ">=5.5.9", "symfony/console": "~2.7|~3.0|~4.0"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"SensioLabs\\Security\\": "SensioLabs/Security"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "time": "2018-12-19T17:14:59+00:00"}, {"name": "simplesamlphp/simplesamlphp-test-framework", "version": "v0.0.14", "source": {"type": "git", "url": "https://github.com/simplesamlphp/simplesamlphp-test-framework.git", "reference": "d9b90d829ffd1597f0119570b4b1f5b7c91a56e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplesamlphp/simplesamlphp-test-framework/zipball/d9b90d829ffd1597f0119570b4b1f5b7c91a56e1", "reference": "d9b90d829ffd1597f0119570b4b1f5b7c91a56e1", "shasum": ""}, "require": {"php": ">=5.6", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "~5.7|^6.0|^7.0|^8.0", "vimeo/psalm": "1.1.9|^2.0|^3.0|^4.0"}, "require-dev": {"ext-curl": "*", "ext-krb5": "*", "simplesamlphp/simplesamlphp": "dev-master"}, "bin": ["bin/check-syntax-json.sh", "bin/check-syntax-php.sh", "bin/check-syntax-xml.sh", "bin/check-syntax-yaml.sh"], "type": "project", "autoload": {"psr-4": {"SimpleSAML\\TestUtils\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Test framework for SimpleSAMLphp and related repositories ", "keywords": ["test-framework"], "time": "2019-10-22T20:45:13+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.5.3", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "557a1fc7ac702c66b0bbfe16ab3d55839ef724cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/557a1fc7ac702c66b0bbfe16ab3d55839ef724cb", "reference": "557a1fc7ac702c66b0bbfe16ab3d55839ef724cb", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "time": "2019-12-04T04:46:47+00:00"}, {"name": "symfony/console", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "1ee23b3b659b06c622f2bd2492a229e416eb4586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/1ee23b3b659b06c622f2bd2492a229e416eb4586", "reference": "1ee23b3b659b06c622f2bd2492a229e416eb4586", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2019-12-01T10:04:45+00:00"}, {"name": "symfony/stopwatch", "version": "v3.4.36", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "efe0af281ad336bc3b10375c88b117499f1d8494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/efe0af281ad336bc3b10375c88b117499f1d8494", "reference": "efe0af281ad336bc3b10375c88b117499f1d8494", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2019-11-03T17:17:59+00:00"}, {"name": "vimeo/psalm", "version": "1.1.9", "source": {"type": "git", "url": "https://github.com/vimeo/psalm.git", "reference": "d15cf3b7f50249caf933144c8926c8e69aff3d34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/psalm/zipball/d15cf3b7f50249caf933144c8926c8e69aff3d34", "reference": "d15cf3b7f50249caf933144c8926c8e69aff3d34", "shasum": ""}, "require": {"composer/xdebug-handler": "^1.1", "muglug/package-versions-56": "1.2.4", "nikic/php-parser": "^3.1", "openlss/lib-array2xml": "^0.0.10||^0.5.1", "php": "^5.6||^7.0", "php-cs-fixer/diff": "^1.2"}, "provide": {"psalm/psalm": "self.version"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^5.7.4", "squizlabs/php_codesniffer": "^3.0"}, "suggest": {"ext-igbinary": "^2.0.5"}, "bin": ["psalm", "psalter"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"Psalm\\": "src/Psalm"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["code", "inspection", "php"], "time": "2018-08-14T16:06:16+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"simplesamlphp/simplesamlphp-module-memcookie": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.6", "ext-spl": "*", "ext-zlib": "*", "ext-pcre": "*", "ext-openssl": "*", "ext-dom": "*", "ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-mbstring": "*"}, "platform-dev": {"ext-curl": "*"}}
<?xml version="1.0"?>
<ruleset name="SimpleSAMLphp ruleset">
    <config name="ignore_warnings_on_exit" value="1"/>

    <description>
        By default it is less stringent about long lines than other coding standards
    </description>

    <file>bin</file>
    <file>config-templates</file>
    <file>lib</file>
    <file>metadata-templates</file>
    <file>modules/admin</file>
    <file>modules/core</file>
    <file>modules/cron</file>
    <file>modules/multiauth</file>
    <file>modules/portal</file>
    <file>modules/saml</file>
    <file>src</file>
    <file>tests</file>
    <file>www</file>

    <!-- Exclude deprecated classes; they are removed in SSP 2.0 -->
    <exclude-pattern>lib/SimpleSAML/Auth/LDAP.php</exclude-pattern>
    <exclude-pattern>lib/SimpleSAML/Auth/TimeLimitedToken.php</exclude-pattern>
    <exclude-pattern>lib/SimpleSAML/Bindings/Shib13/*</exclude-pattern>
    <exclude-pattern>lib/SimpleSAML/Utilities.php</exclude-pattern>
    <exclude-pattern>lib/SimpleSAML/XML/Shib13/*</exclude-pattern>
    <exclude-pattern>modules/saml/lib/IdP/SAML1.php</exclude-pattern>
    <exclude-pattern>modules/saml/www/sp/saml1-acs.php</exclude-pattern>
    <exclude-pattern>tests/lib/SimpleSAML/Auth/TimeLimitedTokenTest.php</exclude-pattern>
    <exclude-pattern>www/shib13</exclude-pattern>

    <!-- Exclude old PHP templates (and related); they are removed in SSP 2.0 -->
    <exclude-pattern>modules/*/templates/*.tpl.php</exclude-pattern>
    <exclude-pattern>templates/*.tpl.php</exclude-pattern>
    <exclude-pattern>www/resources/jquery-ui-1.8.js</exclude-pattern>
    <exclude-pattern>www/resources/clipboard.min.js</exclude-pattern>
    <exclude-pattern>www/resources/jquery-1.8.js</exclude-pattern>

    <!-- This is the rule we inherit from. If you want to exlude some specific rules, see the docs on how to do that -->
    <rule ref="PSR12"/>

    <!-- Lines can be a little bit longer before they break the build -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="120"/>
            <property name="absoluteLineLimit" value="130"/>
        </properties>

        <!-- Exclude files with long lines that we cannot immediately fix -->
        <exclude-pattern>tests/lib/SimpleSAML/Metadata/MetaDataStorageSourceTest.php</exclude-pattern>
        <exclude-pattern>tests/lib/SimpleSAML/Metadata/SAMLParserTest.php</exclude-pattern>
    </rule>

    <!-- Ignore these warnings until PHP 7.1 becomes the minimum required version -->
    <rule ref="PSR12.Properties.ConstantVisibility">
        <exclude-pattern>lib/*</exclude-pattern>
        <exclude-pattern>modules/*</exclude-pattern>
        <exclude-pattern>tests/*</exclude-pattern>
    </rule>

    <!-- Ignore wrong casing until we can rename then in SSP 2.0 -->
    <rule ref="PSR1.Methods.CamelCapsMethodName">
        <exclude-pattern>lib/SimpleSAML/XHTML/Template.php</exclude-pattern>
    </rule>

    <!-- Ignore files with side effects that we cannot fix -->
    <rule ref="PSR1.Files.SideEffects">
        <exclude-pattern>bin/*</exclude-pattern>
        <exclude-pattern>lib/_autoload_modules.php</exclude-pattern>
        <exclude-pattern>tests/_autoload_modules.php</exclude-pattern>
        <exclude-pattern>www/_include.php</exclude-pattern>
    </rule>
</ruleset>


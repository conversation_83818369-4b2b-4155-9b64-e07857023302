# Security Policy

SimpleSAMLphp is a software product critical for the security of applications and the privacy of users. As such, we take every security issue very seriously, and try to solve them as fast as possible, avoiding any potential damage to both users of this software and end users.

This page contains information about security vulnerabilities, incidents or issues related to SimpleSAMLphp. Read below to learn how to properly report vulnerabilities, as well as to find information about issues already reported and fixed.

## Supported Versions

Only the current minor version (X.Y) receives bug and security fixes as a general rule. Release candidates are not considered stable, meaning the previous minor version will still receive fixes until the release candidate promotes to stable.

## Reporting a Vulnerability

In case you find a vulnerability in SimpleSAMLphp, or you want to confirm a possible security issue in the software, please get in touch with us through [UNINETT's CERT team](https://www.uninett.no/cert). Please use our PGP public key to encrypt any possible sensitive data that you may need to submit. We will get back to you as soon as possible according to our working hours in Central European Time.

When reporting a security issue, please add as much information as possible to help us identify, confirm, replicate and fix the problem. In particular, remember to include the following information in your report:

* The version or versions of SimpleSAMLphp affected.
* An exact version that can be used to replicate the issue.
* Any module or modules involved in the issue.
* Any particular configuration details relevant to the setup affected.
* A detailed description and a clear and concise, step-by-step guide to allow us reproduce the issue.
* Screenshots, videos, or any other media that would help identify the issue.
* Pointers to the exact line or lines in the code where the vulnerability is supposed to be.
* Context on how you discovered the issue.
* Your own name and whether you want to be credited for the discovery or not.

Please **DO NOT** report security incidents related to systems that use SimpleSAMLphp, where this software is not the cause of the incident. Issues related to the use (or misuse) of infrastructure, misconfiguration of the software, malfunction of a particular system or user-related errors should not be reported either. If you are using SimpleSAMLphp to authenticate or login to services, but you don't know what SimpleSAMLphp is or you are not sure about the nature of the issue, please contact the organization running the service for you.

Finally, be reasonable. We'll do our best to resolve the issue according to our principles of security and transparency. Every confirmed vulnerability will be published and resolved in a timely manner. All we ask in return is that you contact us privately first in order to avoid any potential damage to those using the software.

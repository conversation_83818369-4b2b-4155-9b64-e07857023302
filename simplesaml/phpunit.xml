<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         bootstrap="./tests/bootstrap.php">
    <testsuites>
        <testsuite name="Unit tests">
            <directory>./tests/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./lib/</directory>
            <directory suffix=".php">./modules/core/lib/</directory>
            <directory suffix=".php">./modules/saml/lib/</directory>
            <exclude>
                <directory>./vendor/</directory>
                <directory>./tests/</directory>
                <file>./lib/SimpleSAML/Utils/HttpAdapter.php</file>
                <file>./lib/SimpleSAML/Auth/LDAP.php</file>
            </exclude>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="true"/>
        <log type="coverage-html" target="build/coverage" title="PHP Coveralls" charset="UTF-8" yui="true" highlight="true" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
    </logging>
</phpunit>

<?php

$ord_id = false;
if (isset($_GET['k'])) {
    $r_ord = ord_orders_get_with_adresses(0, 0, 0, '', false, false, false, false, null, array(_FLD_ORD_NOTIFY_KEY => $_GET['k']));
    if ($r_ord && ria_mysql_num_rows($r_ord)) {
        $ord_id = ria_mysql_result($r_ord, 0, 'id');
    }
}

if (!$ord_id) {
    @include('skin/header.inc.php');

    print <<<TPL
    Cher client,<br />
    Le devis que vous souhaitez consulter n'est pas encore disponible.<br />
    Il est possible que :<br />
    <br />
    La tablette de votre commercial n'ait pas encore accès à une connexion Internet.<br />
    Le devis ait été modifié(e) après l'envoi de l'e-mail.<br />
    Le devis ait été validé(e) ou supprimé(e).<br />
    Nous vous conseillons de réessayer dans quelques instants ou de vous rapprocher de votre commercial si le problème persiste.<br />
    <br />
    L'équipe ${config['site_name']}<br />
TPL
    ;

    @include('skin/footer.inc.php');
    exit;
}

require_once('Pdf/pdf.inc.php');
try {
    generate_devis($ord_id);
} catch(Exception $e){
    @include('skin/header.inc.php');
    if ($e->getCode() >= 1000) {
        print '<div class="error">' . htmlspecialchars(class_exists('i18n', true) ? i18n::get($e->getMessage()) : $e->getMessage()) . '</div>';
    } else {
        $mess = 'Erreur lors de la génération du devis';
        print '<div class="error">' . htmlspecialchars(class_exists('i18n', true) ? i18n::get($mess) : $mess) . '</div>';
    }
    @include('skin/footer.inc.php');
    exit;
}
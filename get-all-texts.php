<?php
	/** \file get-all-texts.php
	 * 	Ce script est chargé de charger l'ensemble des textes de l'administration présentes dans la base de données.
     *  Cette liste est faites pour être traduite via la méthode xgettext.
	 */
    set_include_path(dirname(__FILE__) . '/./include/');
    
    // Si l'on est sur la GCloud, on force un logtoken permettant de ce connecter à une base et récupère les textes à traduire
    require_once('RegisterGCP.inc.php');
    if( RegisterGCP::onGcloud() ){
        $_GET['logtoken'] = '0d52ba6e14a81c897a3cab9b252adbbf';
    }
    
    require_once('db.inc.php');
    require_once('ria.mysql.inc.php');

    $ar_tables = array(
        'adn_menu' => array(
            'global' => true,
            'cols' => array('amn_name', 'amn_full_name', 'amn_tag_title', 'amn_desc')
        ),
        'ctr_conditions' => array(
            'global' => true,
            'cols' => array('ccd_name')
        ),
        'dlv_service_types' => array(
            'global' => true,
            'cols' => array('srt_name', 'srt_name_pl')
        ),
         'fld_types' => array(
            'global' => true,
            'cols' => array('type_name')
        ),
        'fld_classes' => array(
            'global' => 'cls_tnt_id',
            'cols' => array('cls_name')
        ),
        'gu_adr_types' => array(
            'global' => true,
            'cols' => array('type_name')
        ),
        'gu_categories_rights' => array(
            'global' => true,
            'cols' => array('cat_name', 'cat_desc')
        ),
        'gu_messages_state' => array(
            'global' => true,
            'cols' => array('cnt_text')
        ),
        'gu_messages_type' => array(
            'global' => true,
            'cols' => array('cnt_text', 'cnt_text_pl', 'cnt_desc')
        ),
        'gu_profiles' => array(
            'global' => 'prf_tnt_id',
            'cols' => array('prf_name', 'prf_pl_name', 'prf_desc')
        ),
        'gu_rights' => array(
            'global' => 'rgh_tnt_id',
            'cols' => array('rgh_name', 'rgh_desc')
        ),
        'gu_titles' => array(
            'global' => true,
            'cols' => array('title_name', 'title_abr')
        ),
        'ipt_schema_cat' => array(
            'global' => true,
            'cols' => array('cat_name')
        ),
        'ipt_schemas' => array(
            'global' => true,
            'cols' => array('sch_name', 'sch_desc')
        ),
        'mkt_symbols' => array(
            'global' => true,
            'cols' => array('sym_title', 'sym_desc')
        ),
        'ord_payment_types' => array(
            'global' => 'pay_tnt_id',
            'cols' => array('pay_name')
        ),
       'ord_returns_mode' => array(
            'global' => true,
            'cols' => array('rm_name')
        ),
       'ord_returns_products_states' => array(
            'global' => true,
            'cols' => array('prd_state_name', 'prd_state_desc')
        ),
       'ord_returns_states' => array(
            'global' => true,
            'cols' => array('state_name', 'state_desc')
        ),
       'ord_states' => array(
            'global' => true,
            'cols' => array('state_name', 'state_name_plural')
        ),
       'pmt_conditions' => array(
            'global' => true,
            'cols' => array('cdt_name', 'cdt_desc')
        ),
       'pmt_types' => array(
            'global' => true,
            'cols' => array('type_name', 'type_desc')
        ),
       'prc_symbols' => array(
            'global' => true,
            'cols' => array('psy_desc')
        ),
       'prc_types' => array(
            'global' => true,
            'cols' => array('type_name')
        ),
       'prd_nomenclatures_types' => array(
            'global' => true,
            'cols' => array('type_name')
        ),
       'rel_relations_types' => array(
            'global' => 'rrt_tnt_id',
            'cols' => array('rrt_name', 'rrt_name_plural')
        ),
       'rp_report_type_groups' => array(
            'global' => true,
            'cols' => array('rtg_name')
        ),
       'rp_report_type_notes' => array(
            'global' => 'rptn_tnt_id',
            'cols' => array('rptn_name')
        ),
       'rp_report_types' => array(
            'global' => 'rpt_tnt_id',
            'cols' => array('rpt_name')
        ),
       'rwd_action_conditions' => array(
            'global' => true,
            'cols' => array('rwc_name', 'rwc_desc')
        ),
       'rwd_actions' => array(
            'global' => true,
            'cols' => array('rwa_name', 'rwa_name_contrary', 'rwa_desc')
        ),
       'seg_criterion_groups' => array(
            'global' => true,
            'cols' => array('scg_name')
        ),
       'seg_criterions' => array(
            'global' => true,
            'cols' => array('sec_name')
        ),
       'seg_ord_source_groups' => array(
            'global' => true,
            'cols' => array('sog_name')
        ),
       'seg_ord_sources' => array(
            'global' => true,
            'cols' => array('sos_name')
        ),
       'sys_sort_types' => array(
            'global' => true,
            'cols' => array('sort_name', 'sort_desc')
        ),
       'sys_zone_types' => array(
            'global' => true,
            'cols' => array('znt_name')
        ),
       'tnt_website_types' => array(
            'global' => true,
            'cols' => array('wty_name')
        ),
    );

    print '<?php'.PHP_EOL;

    
    $ar_unique = array();
    foreach ($ar_tables as $table=>$data) {
        foreach ($data['cols'] as $col) {
            $sql = '
                select distinct '.$col.' as col
                from '.$table.'
                where 1
            ';

            if ($data['global'] !== true) {
                $sql .= ' and '.$data['global'].' = 0';
            }

            $res = ria_mysql_query($sql);

            if (!$res) {
                print ria_mysql_error().' => '.$sql;
                exit;
            } else {
                while ($r = ria_mysql_fetch_assoc($res)) {
                    if (trim($r['col']) == '') {
                        continue;
                    }

                    $unique = md5($r['col']);
                    if (array_key_exists($unique, $ar_unique)) {
                        continue;
                    }

                    $r['col'] = str_replace("'", "\'", $r['col']);
                    $r['col'] = str_replace(array("\r", "\n", "\n\r"), " ", $r['col']);

                    print '_(\''.$r['col'].'\');'.PHP_EOL;
                    $ar_unique[ $unique ] = $unique;
                }
            }
        }
    }
<?php
	/*
	 * 	Ce script permet l'actualisation des coordonnées pour les resellers
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	$rrsl = rsl_resellers_get();

	if( !$rrsl || !ria_mysql_num_rows($rrsl) ){
		return false;
	}
	
	while( $rsl = ria_mysql_fetch_array($rrsl) ){
		//$rsl['country'] = 'FRANCE';
		if( (!is_numeric($rsl['latitude']) || !is_numeric($rsl['longitude'])) ){
			
			// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
			$gps = sys_google_maps_search( $rsl['address1'].' '.$rsl['zipcode'].' '.$rsl['city'].', '.$rsl['country'] );
			if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
				$gps = sys_google_maps_search( $rsl['zipcode'].' '.$rsl['city'].', '.$rsl['country'] );
			}
			
			// mise à jour des coodonées GPS trouvées
			if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
				print 'update '.$rsl['name'].' - '.$gps['lat']."\n";
				ria_mysql_query('update rsl_resellers set rsl_latitude = '.$gps['lat'].', rsl_longitude = '.$gps['lng'].' where rsl_id = '.$rsl['id']); 
			}
			sleep(1);
		}
	}
<?php
	/** \file verify_rewards_orders.php
	 *
	 * 	Ce script est destiné à vérifier si une ou plusieurs commandes ont bien eu leurs points de fidélités d'attribués
	 */

    set_include_path(dirname(__FILE__) . '/../include/');

	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('orders.inc.php');
	require_once('rewards.inc.php');

    $ord_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] ? $argv[1] : 0;
    if (!$ord_id) {
        exit;
    }

    $r_order = ord_orders_get_simple(array('id'=>$ord_id));
    if (!$r_order || !ria_mysql_num_rows($r_order)) {
        exit;
    }

    $order = ria_mysql_fetch_assoc($r_order);

    if (in_array($order['state_id'], ord_states_get_ord_valid(false, true)) && $order['state_id'] != _STATE_ARCHIVE) {
        $prf = gu_users_get_prf($order['usr_id']);
        if ($prf) {
            $typcalc = rwd_rewards_get_type_calc($prf);
            if ($typcalc == 'order') {
                rwd_actions_apply('RWA_ORDER', CLS_ORDER, $ord_id, array('date_created'=>$order['date']));
            }

            $rwd_sp = rwd_rewards_get_sponsor(0, $config['wst_id'], $prf);
            if (is_array($rwd_sp) && count($rwd_sp)) {
                if (is_numeric($rwd_sp['points']) && $rwd_sp['points']) {
                    rwd_actions_apply('RWA_SPONSOR', CLS_ORDER, $ord_id, array('prf' => $prf, 'godson' => $order['usr_id']));
                } else {
                    $order_promotions = ord_orders_promotions_get($order['id']);
                    if (!empty($order_promotions)) {
                        foreach ($order_promotions as $order_promotion) {
                            rwd_actions_apply('RWA_SPONSOR', CLS_ORDER, $ord_id, array('prf' => $prf, 'cod' => $order_promotion['pmt_id'], 'godson' => $order['usr_id']));
                        }

                    }
                }
            }

        }
    } else {
                // annulation de commande
        if (in_array($order['state_id'], ord_states_get_canceled(true))) {
            rwd_actions_apply('RWC_CANCEL_ORDER', CLS_ORDER, $ord_id);
        }
    }
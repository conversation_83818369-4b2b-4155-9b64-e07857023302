<?php
	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	$totalUrls = 0;
	$totalImgs = 0;
	
	// todo utiliser la fonction rew_rewritemap_get() avec un nouveau paramètre ($wst = identifiant du site web)
	$res = ria_mysql_query('select url_extern from rew_rewritemap where url_tnt_id = ' . $config['tnt_id'] . ' and url_wst_id = ' . $config['wst_id'] . ' and url_code = 200 and url_public = 1;');
	
	$toutesLesUrls = array();
	while ($dat = ria_mysql_fetch_assoc($res)) $toutesLesUrls[$config['site_url'] . $dat['url_extern']] = true;
	
	$urlsIndexees = array();
	$urlFailures = array();
	
	$rep = $config['site_dir']. '/pages/sitemaps/';
	$dir = opendir($rep);
	while( $fileName = readdir($dir) ){
		if ($fileName === '.' || $fileName === '..') continue;
		
		$len = strlen($fileName);
		if (substr($fileName, $len-4, 4) !== '.xml') continue;
		
		$f = $rep  . $fileName;
		$content = file_get_contents($f);
		
		if (strpos($content, '<sitemap>') !== false) continue;
		
		$occ = substr_count($content, '<loc>');
		$images = substr_count($content, '<image:image>');
		
		preg_match_all('/\<loc\>(.*)\<\/loc\>+/', $content, $match);
		$urls = $match[1];
		foreach( $urls as $url ){
			unset($toutesLesUrls[$url]);
			$urlsIndexees[] = $url;
		}
		
		$totalUrls += $occ;
		$totalImgs += $images;
	}
	
	foreach( $toutesLesUrls as $url => $v ){
		if( !($c = curl_init()) ) die('Erreur curl_init !');
		curl_setopt($c, CURLOPT_URL, $url);
		curl_setopt($c, CURLOPT_FOLLOWLOCATION, 0);
		curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($c, CURLOPT_CONNECTTIMEOUT, 5);
		$r = curl_exec($c);
		$infos = curl_getinfo($c);
		curl_close($c);
		if( $infos['http_code'] != '200' ){
			$urlFailures[] = $url;
			unset($toutesLesUrls[$url]);
		}
	}
	
	print "\n".$totalImgs.' images'."\n";
	print 'URL public ne renvoyant pas le code 200 : '.sizeof( $urlFailures ).'' . "\n";
	print 'URL public non présentes dans le sitemap ('.sizeof( $toutesLesUrls ).')' . "\n";
	foreach ($toutesLesUrls as $url => $v) 
		print $url. "\n";
	


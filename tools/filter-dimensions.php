<?php
	/*
		Ce script enregistre les dimensions disponibles pour chaque produit ayant des prototypes.
		Les produits peuvent alors apparaître dans des recherche avec un critère sur la dimension.
	*/
	
	set_include_path('/var/www/somelys.maquettes.riastudio.fr/htdocs/include' . PATH_SEPARATOR . '/var/www/riashop.maquettes.riastudio.fr/include');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	
	require_once('models/product.php');
	
	// récupère la liste des dimensions
	$dims = array();
	$values = fld_restricted_values_get(0, FIELD_DIM_ID);
	while ($dat = ria_mysql_fetch_assoc($values)) $dims[$dat['name']] = $dat['id'];
	
	// chaque produit
	$products = Product::getProducts(array('published' => true));
	foreach ($products as $productID => $product) {
		// chaque produit enfant
		$prototypes = $product->getPrototypes();
		
		if (count($prototypes) > 0) {
			// ajoute le modèle de saisie Produit avec dimension si pas encore appliqué
			fld_object_models_add($productID, MODEL_PRD_HAS_DIMS_ID);
			
			$liste = array();
			foreach ($prototypes as $prototype) {
				// récupère les dimension lllxLLL
				$dim = $prototype->getDim();
				$dim = str_pad($dim[0], 3, '0', STR_PAD_LEFT).'x'.str_pad($dim[1], 3, '0', STR_PAD_LEFT);
				
				// ajoute la dimension si elle n'existe pas dans la liste
				if (! array_key_exists($dim, $dims)) {
					$dims[$dim] = fld_restricted_values_add(FIELD_DIM_ID, $dim);
				}
				
				$liste[] = $dims[$dim];
			}
			
			// enregistrement
			fld_object_values_set($productID, FIELD_DIM_ID, $liste, 'fr');
		}
	}


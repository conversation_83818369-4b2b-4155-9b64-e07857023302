<?php
	/** \file clean-errors-404.php
	 * 	Ce script est destiné à faire le ménage dans les erreurs 404 en supprimant celle qui ne le sont plus.
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'db.inc.php' );
	require_once('errors.inc.php');

	$r_error = err_errors_get( $config['wst_id'], '', false, '', 'pages', false );
	while( $error = ria_mysql_fetch_assoc($r_error) ){
		$site_url 	= wst_websites_languages_get_url( $config['wst_id'], $error['lng'] );
		$check_url 	= Sitemap::checkUrl( $site_url.$error['url'], false, $site_url );

		if( $check_url ){
			err_errors_del( $config['wst_id'], $error['lng'], $error['url'] );
		}
		// print ( $check_url ? '200/200' : '404/301' ).' => '.$config['site_url'].$error['url'].PHP_EOL;
	}	

<?php

if(!isset($argv[1], $argv[2])){
	echo 'Veuillez saisir la class puis le nom du listener';
	exit;
}

$class = $argv[1];
$listener = $argv[2];
$event = isset($argv[3]) ? $argv[3] : '';

$filepath = dirname(__FILE__).'/../../include/EventService/';

$filepath .= $class . '/';
if( !is_dir($filepath)){
	mkdir($filepath);
}
$filepath .= 'Listeners/';
if( !is_dir($filepath)){
	mkdir($filepath);
}

$filepath .= $listener.'.inc.php';
$handle = fopen($filepath, 'w');

if( $event ){

}

$namespace = 'EventService\\'.$class;
$event_name = $event ? 'use '.$namespace.'\\'.$event.';' : '';

$content = "<?php
namespace $namespace\\Listeners;
{$event_name}

class {$listener}
{
	public function handle({$event} \$event)
	{
		//your code
	}
}";

fwrite($handle, $content);
fclose($handle);
exit;
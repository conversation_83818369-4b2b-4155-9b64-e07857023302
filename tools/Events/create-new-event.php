<?php
if(!isset($argv[1], $argv[2])){
	echo 'Veuillez saisir la class puis le nom de l\'événement';
	exit;
}

$class = $argv[1];
$event = $argv[2];

$filepath = dirname(__FILE__).'/../../include/EventService/';

$filepath .= $class . '/';
if( !is_dir($filepath)){
	mkdir($filepath);
}
$filepath .= 'Events/';
if( !is_dir($filepath)){
	mkdir($filepath);
}

$filepath .= $event.'.inc.php';
$handle = fopen($filepath, 'w');

$content = "<?php
namespace EventService\\$class\\Events;


class {$event}
{
	/**
	 * __construct
	 *
	 * @return void
	 */
	public function __construct()
	{
		// your code
	}
}";

fwrite($handle, $content);
fclose($handle);
exit;
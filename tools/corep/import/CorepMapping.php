<?php

set_include_path(dirname(__FILE__) . '/../../../include/');

/**
 * Cette classe contient une liste de l'ensemble de champs à traiter (indexés sur le nom de leurs propriétés dans le fichier JSON)
 * et des methodes de traitement à appliquer
 */
class CorepMapping
{
    public $fields;

    public $id_tenant = 395;
    public $id_fields_model = 1878; // to do : idem l'id du model "Corep import test" sur mon dev
    public $id_cat_root = 238487; // to do : idem id de la categorie racine "Yuto"

    public function __construct()
    {
        $this->fields = $this->getFields();
    }

    /**
     * Recuperation de la liste des champs et de leurs definitions
     * Chaque champ a :
     * 1- Un type (standard si valeur texte basique)
     * 2- L'id du champ (ou le nom si la propriete est native au CMS, c-a-d ne correpond pas à un champ personnalisé)
     * 3- une fonction a executé en callback et les paramètres (optionnels) à fournir à cette fonction
     */
    private function getFields()
    {
        return [
            'id_code_erp' => [
                'type' => 'text',
                'id' => 'prd_ref', // reference
                'callback' => null
            ],
            'com_couleur' => [
                'type' => 'text',
                'id' => 101749,
                'callback' => null
            ],
            'com_couleur_commercial' => [
                'type' => 'text',
                'id' => 101750,
                'callback' => null
            ],
            'com_diametre' => [
                'type' => 'float',
                'id' => 101751,
                'callback' => 'formatValue'
            ],
            'com_hauteur' => [ // hauteur
                'type' => 'float',
                'id' => 'prd_height',
                'callback' => 'formatValue'
            ],
            'com_hauteur_totale' => [
                'type' => 'float',
                'id' => 101753,
                'callback' => 'formatValue'
            ],
            'com_libelle' => [ // description courte
                'type' => 'text',
                'id' => 'prd_desc', 
                'callback' => null
            ],
            'com_largeur' => [ // largeur
                'type' => 'text',
                'id' => 'prd_width', 
                'callback' => 'formatValue'
            ],
            'com_longueur' => [ // longueur
                'type' => 'text',
                'id' => 'prd_length', 
                'callback' => 'formatValue'
            ],
            'com_nom_produit' => [ // nom
                'type' => 'text',
                'id' => 'prd_name', 
                'callback' => null
            ],
            'id_gencod_produit' => [ // EAN13
                'type' => 'text',
                'id' => 'prd_barcode', 
                'callback' => null
            ],
            'com_poids_brut' => [ // poids brut
                'type' => 'float',
                'id' => 'prd_weight', 
                'callback' => 'formatValue'
            ],
            'com_poids_net' => [ // poids net
                'type' => 'float',
                'id' => 'prd_weight_net', 
                'callback' => 'formatValue'
            ],
            'com_profondeur' => [ 
                'type' => 'float',
                'id' => 101752, 
                'callback' => null
            ],
            'com_matiere' => [
                'type' => 'text',
                'id' => 101754, 
                'callback' => ['getValueFromOptionsList', ['index' => 'com_matiere', 'multiple' => false]]
            ],
            'com_matiere2' => [
                'type' => 'text',
                'id' => 101755, 
                'callback' => ['getValueFromOptionsList', ['index' => 'com_matiere', 'multiple' => false]]
            ],
            'com_plus_produit' => [
                'type' => 'text',
                'id' => 101759, 
                'callback' => ['getValueFromOptionsList', ['index' => 'com_plus_produit', 'multiple' => true]]
            ],
            'com_libelle_serie' => [
                'type' => 'text',
                'id' => 101756,
                'callback' => null
            ],
            'com_serie' => [
                'type' => 'text',
                'id' => 101757,
                'callback' => null
            ],
            'com_taille_mark' => [
                'type' => 'text',
                'id' => 101758,
                'callback' => null
            ],
            'sku' => [
                'type' => 'text',
                'id' => 101760,
                'callback' => null
            ],
            'id_marque' => [ // marque
                'type' => 'text',
                'id' => 'prd_brd_id',
                'callback' => 'getBrandId'
            ],
            'id_nature_produit' => [
                'type' => 'text',
                'id' => 101761, 
                'callback' => ['getValueFromOptionsList', ['index' => 'id_nature_produit', 'multiple' => false]]
            ],
            'id_famille_produit' => [
                'type' => 'text',
                'id' => 101762, 
                'callback' => null
            ],
            'id_vie_produit' => [
                'type' => 'text',
                'id' => 101763, 
                'callback' => null
            ],
            'id_designer' => [
                'type' => 'text',
                'id' => 101764, 
                'callback' => null
            ],
            'channel' => [
                'type' => 'text',
                'id' => 101765, 
                'callback' => null
            ],
            'family' => [
                'type' => 'text',
                'id' => 101766, 
                'callback' => null
            ],
            'aj_dbb' => [
                'type' => 'bool',
                'id' => 101713,
                'callback' => null
            ],
            'aj_dbh' => [
                'type' => 'bool',
                'id' => 101714,
                'callback' => null
            ],
            'aj_dia_bas' => [
                'type' => 'float',
                'id' => 101715,
                'callback' => 'formatValue'
            ],
            'aj_dia_haut' => [
                'type' => 'float',
                'id' => 101716,
                'callback' => 'formatValue'
            ],
            'aj_forme' => [
                'type' => 'text',
                'id' => 101717,
                'callback' => null
            ],
            'aj_hauteur' => [
                'type' => 'float',
                'id' => 101718,
                'callback' => 'formatValue'
            ],
            'aj_pente' => [
                'type' => 'float',
                'id' => 101719,
                'callback' => 'formatValue'
            ],
            'aj_prof_bas' => [
                'type' => 'float',
                'id' => 101720,
                'callback' => 'formatValue'
            ],
            'aj_prof_haut' => [
                'type' => 'float',
                'id' => 101721,
                'callback' => 'formatValue'
            ],
            'aj_v' => [
                'type' => 'bool',
                'id' => 101722,
                'callback' => null
            ],
            'elec_ampoule_incluse' => [
                'type' => 'bool',
                'id' => 101728,
                'callback' => null
            ],
            'elec_certificat' => [
                'type' => 'text',
                'id' => 101729,
                'callback' => null
            ],
            'elec_classe' => [
                'type' => 'text',
                'id' => 101730,
                'callback' => null
            ],
            'elec_couleur_cable' => [
                'type' => 'text',
                'id' => 101731,
                'callback' => null
            ],
            'elec_couleur_douille' => [
                'type' => 'text',
                'id' => 101732,
                'callback' => null
            ],
            'elec_d3e_qte' => [
                'type' => 'text',
                'id' => 101733,
                'callback' => null
            ],
            'elec_d3e_taxe' => [
                'type' => 'bool',
                'id' => 101734,
                'callback' => null
            ],
            'elec_d3e_taxe' => [
                'type' => 'bool',
                'id' => 101767,
                'callback' => null
            ],
            'elec_ip' => [
                'type' => 'text',
                'id' => 101735,
                'callback' => null
            ],
            'elec_kelvin' => [
                'type' => 'text',
                'id' => 101736,
                'callback' => null
            ],
            'elec_led_integree' => [
                'type' => 'bool',
                'id' => 101737,
                'callback' => null
            ],
            'elec_lumen' => [
                'type' => 'int',
                'id' => 101738,
                'callback' => null
            ],
            'elec_nbre_pile' => [
                'type' => 'int',
                'id' => 101739,
                'callback' => null
            ],
            'elec_pav_diametre' => [
                'type' => 'float',
                'id' => 101740,
                'callback' => 'formatValue'
            ],
            'elec_pav_h' => [
                'type' => 'float',
                'id' => 101748,
                'callback' => 'formatValue'
            ],
            'elec_piles_fourni' => [
                'type' => 'bool',
                'id' => 101741,
                'callback' => null
            ],
            'elec_piles' => [
                'type' => 'text',
                'id' => 101742,
                'callback' => null
            ],
            'elec_transformateur' => [
                'type' => 'bool',
                'id' => 101743,
                'callback' => null
            ],
            'elec_type_douille' => [
                'type' => 'text',
                'id' => 101744,
                'callback' => null
            ],
            'elec_type_interrupteur' => [
                'type' => 'text',
                'id' => 101745,
                'callback' => null
            ],
            'elec_type_lumiere' => [
                'type' => 'text',
                'id' => 101746,
                'callback' => null
            ],
            'elec_watt_max' => [
                'type' => 'float',
                'id' => 101747,
                'callback' => 'formatValue'
            ],
            'fsc_essence' => [
                'type' => 'text',
                'id' => 101723,
                'callback' => null
            ],
            'fsc_fsc' => [
                'type' => 'bool',
                'id' => 101724,
                'callback' => null
            ],
            'fsc_label' => [
                'type' => 'text',
                'id' => 101725,
                'callback' => null
            ],
            'fsc_percent' => [
                'type' => 'float',
                'id' => 101726,
                'callback' => 'formatValue'
            ],
            'fsc_type' => [
                'type' => 'text',
                'id' => 101727,
                'callback' => null
            ],
        ];
    }

    /**
     * Formatage d'une valeur de dimension, de poids ou de puissance.
     * @param string $value la valeur à formater (ex : "33.000000000000CENTIMETER", "340.000000000000GRAM", etc)
     * @param int optionnel $decimals le nombre de décimales à conserver (2 par défaut)
     * @return float la valeur débarassée de l'unité et ramenée au no
     */
    public function formatValue($value, $decimals = 2)
    {
        $units_array = [
            'GRAM', 'CENTIMETER', 'WATT', '%'
        ];
        return number_format(floatval(str_replace($units_array, '', $value)), $decimals);
    }

    /**
     * Recuperation d'une valeur a partir d'une propriete du fichier JSON
     * @param array $data : les donnees du fichier JSON sous forme de tableau
     * @param string $value : la valeur recherchee
     * @param array $params : les options de recherche ("index" contient la propriete sur laquelle on va faire la recherche, "multiple" si on doit rechercher une valuer unique ou plusieurs)
     * @return la valeur sous forme de chaine de caractère
     */
    public function getValueFromOptionsList($data, $value, $params)
    {
        if (!$params['multiple']) {
            return $data['i18n']['options'][$params['index']][$value[0]];
        } else {
            $values_array = [];
            foreach ($value as $v) {
                $values_array[] = $data['i18n']['options'][$params['index']][$v];
            }
            return implode(", ", $values_array);
        }
    }

    /**
     * Recuperation de l'id de la marque à partir de son nom. Si elle n'existe pas on la cree
     * @param string $brand_name le nom de la marque
     * @return id de la marque
     */
    public function getBrandId($brand_name)
    {
        $r_brd = prd_brands_get(0, false, $brand_name);
        $brand = ria_mysql_fetch_assoc($r_brd);
        if ($brand !== false) {
            return $brand['id'];
        } else {
            $brand_add = prd_brands_add($brand_name);
            return $brand_add != 0 ? $brand : null;
        }
    }

}
<?php
// Etape 2 - Script d'integration produits à partir des fichiers zip téléchargés depuis le FTP Corep
error_reporting(E_ALL ^ E_WARNING ^ E_NOTICE); 

require_once('CorepImport.php');

$CorepImport = new CorepImport('integrate');

$msg = 'Récupération de la liste des fichiers pour l\'intégration';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);

// Recuperation de la liste des fichiers a traiter
if (!$CorepImport->getFilesToProcess()) {
    $error = 'Le répertoire temporaire n\'existe pas ou ne contient aucun fichier à traiter';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    exit;
}

// Traitement de la liste des fichiers valides
$msg = 'Lancement de l\'intégration des produits';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);
$CorepImport->processFiles();

// Bilan de l'intégration
$msg = 'Fin de l\'intégration des produits';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg. PHP_EOL);
// Nombre de produits intégrés
$msg = sprintf('%d fichiers intégrés sur un total de %d fichiers', $CorepImport->counters['processed_files'], (($CorepImport::_COREP_IMPORT_TEST_MODE_ === false) ? count($CorepImport->files) : 10));
echo($msg. PHP_EOL);
// Nombre d'erreurs
if (!empty($CorepImport->errors)) {
    $error = sprintf('%d erreurs survenues lors du processus.', count($CorepImport->errors));
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $error .= 'Veuillez consulter le fichier integrate.log pour plus de détails';
    }
    echo($error. PHP_EOL);
}
echo('Fin'.PHP_EOL);

// Suite - étape 3 : fichier associate.php
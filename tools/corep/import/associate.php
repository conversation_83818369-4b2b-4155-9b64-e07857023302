<?php
// Etape 3 - Script de creation des categories et d'association produits/categories
error_reporting(E_ALL ^ E_WARNING ^ E_NOTICE); 

require_once('CorepImport.php');

$CorepImport = new CorepImport('associate');

$msg = 'Récupération de la liste des fichiers pour le traitement des catégories';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);

// Recuperation de la liste des fichiers a traiter
if (!$CorepImport->getFilesToProcess()) {
    $error = 'Le répertoire temporaire n\'existe pas ou ne contient aucun fichier à traiter';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    exit;
}

// Traitement de la liste des fichiers valides
$msg = 'Lancement du traitement des catégories';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);
$CorepImport->processCategories();

// Bilan de l'intégration
$msg = 'Fin du traitement des catégories';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg. PHP_EOL);
// Nombre d'erreurs
if (!empty($CorepImport->errors)) {
    $error = sprintf('%d erreurs survenues lors du processus.', count($CorepImport->errors));
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $error .= 'Veuillez consulter le fichier associate.log pour plus de détails';
    }
    echo($error. PHP_EOL);
}
echo('Fin'.PHP_EOL);
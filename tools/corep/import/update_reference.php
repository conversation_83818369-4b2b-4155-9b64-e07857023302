<?php
// Etape 3 - Script de creation des categories et d'association produits/categories
error_reporting(E_ALL ^ E_WARNING ^ E_NOTICE); 

require_once('CorepImport.php');

$CorepImport = new CorepImport('update_reference');

$msg = 'Récupération de la liste des fichiers pour la mise-à-jour des références';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);

// Recuperation de la liste des fichiers a traiter
if (!$CorepImport->getFilesToProcess()) {
    $error = 'Le répertoire temporaire n\'existe pas ou ne contient aucun fichier à traiter';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    exit;
}

// Traitement de la liste des fichiers valides
$msg = 'Lancement de la mise-à-jour des références';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);
// Propriété utilisee precedemment comme reference du produit et propriété qui la remplace
$before = 'sku';
$after = 'id_code_erp';
$CorepImport->updateReference($before, $after);

// Bilan de l'intégration
$msg = 'Mise-à-jour des références terminée';
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg. PHP_EOL);
// Nombre d'erreurs
if (!empty($CorepImport->errors)) {
    $error = sprintf('%d erreurs survenues lors du processus.', count($CorepImport->errors));
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $error .= 'Veuillez consulter le fichier update_reference.log pour plus de détails';
    }
    echo($error. PHP_EOL);
}
echo('Fin'.PHP_EOL);
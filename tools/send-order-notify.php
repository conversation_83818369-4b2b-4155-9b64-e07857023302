<?php

	/** \file send-order-notify.php
	 *
	 * 	Ce script est destiné à envoyer la notification de commande.
	 *
	 */


	 set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'orders.inc.php' );
	
	$order  = isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;
	$state3 = isset($argv[2]) && $argv[2]=='false' ? false : true;
	
	if( !ord_orders_exists($order) ){
		print 'La commande donnée en paramètre n\'existe pas.'."\n";
		exit;
	}
	
	if( $state3 ){
		ord_orders_state_update( $order, 3 );
	}
	
	ord_orders_state_update( $order, 4 );


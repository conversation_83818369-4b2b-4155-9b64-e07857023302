<?php
	// ce script permet l'importation des données de référencement auto et personnalisé
	// attention le fichier importer dois respecter le format de celui exporté par le fichier export-referencing.php
	
	set_include_path(dirname(__FILE__) . '/../include/');
    require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	
	$fichier = isset($argv[1]) && $argv[1] ? $argv[1] : false;
	if( !is_file($fichier) ){
		die('Le fichier passé en paramètre est introuvable.');
	}
	$file_content = file_get_contents( $fichier ); 
	
	$cpt = 0;
	foreach( explode("\r\n",$file_content) as $line ){
		if($cpt++ == 0 ) continue;	// on ignore la première ligne qui sont les entetes
		if( trim($line) == '' ) continue; 
		
		$cols = explode(';', $line);
		// permet de retire les " " qui entoure les cellules
		foreach( $cols as $k => $col ){
			$cols[$k] = preg_replace('/^"(.*)"$/','$1', $col);
		}
		
		$type = $cols[0];
		$id_obj_1 = (int)$cols[2];
		$id_obj_2 = (int)$cols[4];
		$id_obj_3 = (int)$cols[6];
		$lng = $cols[7];
		$meta_title_perso = trim($cols[8]);
		$meta_desc_perso = trim($cols[10]);
		$meta_key_perso = trim($cols[12]);

		if( empty($meta_title_perso) && empty($meta_desc_perso) && empty($meta_key_perso) ){
			continue;
		}
		
		$metas = array(
			_FLD_CMS_TAG_TITLE => $meta_title_perso,
			_FLD_CMS_TAG_DESC => $meta_desc_perso,
			_FLD_CMS_TAG_KEYWORDS => $meta_key_perso
		);
		
		// switch sur le type 
		switch( $type ){
			case 'ACTUALITE' : {
				if( $lng == $config['i18n_lng'] ){
					if( !news_update_referencing( $id_obj_1, $meta_title_perso, $meta_desc_perso, $meta_key_perso ) ){
						print 'Erreur d\'insertion des métas pour l\'actualité : '.$id_obj_1."\n";
						continue;
					}
				}elseif( in_array($lng, $config['i18n_lng_used']) ){
					if( !fld_translates_add($id_obj_1, $lng, $metas) ){
						print 'Erreur d\'insertion des métas traduite pour l\'actualité : '.$id_obj_1."\n";
						continue;
					}
				}				
				break;
			}
			case 'CMS' : {
				if( $lng == $config['i18n_lng'] ){
					if( !cms_categories_update_referencing( $id_obj_1, $meta_title_perso, $meta_desc_perso, $meta_key_perso ) ){
						print 'Erreur d\'insertion des métas pour le cms : '.$id_obj_1."\n";
						continue;
					}
				}elseif( in_array($lng, $config['i18n_lng_used']) ){
					if( !fld_translates_add($id_obj_1, $lng, $metas) ){
						print 'Erreur d\'insertion des métas traduite pour le cms : '.$id_obj_1."\n";
						continue;
					}
				}			
				break;
			}
			case 'MAGASINS' : {
				if( $lng == $config['i18n_lng'] ){
					if( !dlv_stores_update_referencing( $id_obj_1, $meta_title_perso, $meta_desc_perso, $meta_key_perso ) ){
						print 'Erreur d\'insertion des métas pour le cms : '.$id_obj_1."\n";
						continue;
					}
				}elseif( in_array($lng, $config['i18n_lng_used']) ){
					if( !fld_translates_add($id_obj_1, $lng, $metas) ){
						print 'Erreur d\'insertion des métas traduite pour le cms : '.$id_obj_1."\n";
						continue;
					}
				}			
				break;
			}
			case 'CATEGORIE' : {
				if( $lng == $config['i18n_lng'] ){
					if( !prd_categories_update_referencing( $id_obj_1, $meta_title_perso, $meta_desc_perso ) ){
						print 'Erreur d\'insertion des métas title/desc pour la catégorie : '.$id_obj_1."\n";
						continue;
					}
					if( !prd_categories_update_keywords( $id_obj_1, $meta_key_perso ) ){
						print 'Erreur d\'insertion des métas key pour la catégorie : '.$id_obj_1."\n";
						continue;
					}
				}elseif( in_array($lng, $config['i18n_lng_used']) ){
					if( !fld_translates_add($id_obj_1, $lng, $metas) ){
						print 'Erreur d\'insertion des métas traduite pour la catégories : '.$id_obj_1."\n";
						continue;
					}
				}							
				break;
			}
			case 'PRODUIT' : {
				if( !prd_products_exists($id_obj_1 ) ) continue;
				
				if( $lng == $config['i18n_lng'] ){
					if( !prd_classify_update_referencing( $id_obj_2, $id_obj_1, $meta_title_perso, $meta_desc_perso, $meta_key_perso ) ){
						print 'Erreur d\'insertion des métas pour le produit : '.$id_obj_1."\n";
						continue;
					}
				}elseif( in_array($lng, $config['i18n_lng_used']) ){
					if( !fld_translates_add(array($id_obj_1, $id_obj_2), $lng, $metas) ){
						print 'Erreur d\'insertion des métas traduite pour le produit : '.$id_obj_1."\n";
						continue;
					}
				}
				break;
			}
			default: {
				print 'Le type de la ligne est inconnue : '.$type."\n";
				continue;
			}				
		}
		
		print $cpt."\n";
		
	}
	die('Fin de l\'importation'."\n");
	
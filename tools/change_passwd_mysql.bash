#!/bin/bash

OLDMYSQLPASS='3CSJ1jY9'
NEWMYSQLPASS='hNl50sRy'
ROOTMYSQLPASS='622bGVXC'

echo ">>Changement du password en db Mysql"
echo "Update mysql.user set Password=PASSWORD('${NEWMYSQLPASS}') where User='riashop' and Host='localhost'" | mysql -u root -p$ROOTMYSQLPASS mysql


echo ">>Changement du password dans le fichier de conf"
sed -i "s/${OLDMYSQLPASS}/${NEWMYSQLPASS}/g" include/db.inc.php 


echo ">>Flushing des privileges mysql"
echo "Flush privileges;" | mysql -u root -p$ROOTMYSQLPASS mysql

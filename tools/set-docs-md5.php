<?php 
    /** 
     * Ce script permet de mettre à jour les champs doc_md5_content de tout les documents de la base qui ne sont pas "supprimés" et pour lesquels le champ comprenant
     * la clé md5 n'est pas renseignée
     */

    $tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] ? $argv[1] : 0;

    set_include_path(dirname(__FILE__) . '/../include/');

    require_once( 'documents.inc.php' );
    require_once( 'cfg.variables.inc.php' );
    
    //Enlever la valeur numérique pour traiter sur tout les tenants
    $configs = cfg_variables_get_all_tenants($tnt_id);
    
    $count_success = 0;
    $count_error = 0;

    foreach ($configs as $tnt_id => $config) {
        $r_websites = wst_websites_get(0,false,$tnt_id);
        while ($website = ria_mysql_fetch_assoc($r_websites)){
            $lngs = wst_websites_languages_get_array($website['id']);
            $r_docs = doc_documents_get(0,0,$website['id']);
            while ($document = ria_mysql_fetch_assoc($r_docs)) {
                if ($document['md5_content'] === null){
                    $doc_url = $config['doc_dir'].'/'.$document['id'];
                    if (file_exists($doc_url)){
                        $content = file_get_contents($doc_url);
                        $md5_code = md5($content);
                                           
                        if(doc_documents_set_md5($document['id'], $md5_code)){
                            $count_success++;
                        } else {
                            $count_error++;
                        }
                    } else {
                        $count_error++;
                    }
                }
                if(is_array($lngs) && sizeof($lngs)){
                    foreach ($lngs as $lng_code => $lng_url) {
                        if (doc_websites_get_md5($website['id'], $document['id'], $lng_code) === null){
                            if ($lng_code == $config['i18n_lng']){
                                $doc_url = $config['doc_dir'].'/'.$document['id'];
                            } else {
                                $doc_url = $config['doc_dir'].'/'.$lng_code.'-'.$document['id'];
                                if (!file_exists($doc_url)){
                                    $doc_url = $config['doc_dir'].'/'.$document['id'];
                                }
                            }
                            if (file_exists($doc_url)){
                                $content = file_get_contents($doc_url);
                                $md5_code = md5($content);
                                                
                                if(doc_websites_set_md5( $website['id'], $document['id'], $lng_code, $md5_code)){
                                    $count_success++;
                                } else {
                                    $count_error++;
                                }
                            } else {
                                $count_error++;
                            }
                        }
                    }
                }
            }
        }
    }
    print 'succès : '.$count_success.' | erreurs : '.$count_error;



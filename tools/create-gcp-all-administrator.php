<?php
		/** \file create-gcp-all-administrator.php
		 * 	Ce fichier permet de créer les comptes administrateurs présents en base dans le registre.
		 */

		set_include_path(dirname(__FILE__).'/../include/');
		require_once('db.inc.php');
		require_once('tenants.inc.php');
		require_once('RegisterGCP.inc.php');
		require_once('users.inc.php');

		$ar_tenant_ids = RegisterGCP::create()->getTenantIDs();
		if (!is_array($ar_tenant_ids) || !count($ar_tenant_ids)) {
				exit;
		}

		foreach ($ar_tenant_ids as $tnt_id) {
			if (is_numeric($tnt_id) && $tnt_id > 0) {
				RegisterGCPConnection::init( $tnt_id, true, true );
				print 'Tenant => '.$tnt_id.PHP_EOL;

				$r_user = gu_users_get( 0, '', '', PRF_ADMIN );
				if( $r_user ){
					while( $user = ria_mysql_fetch_assoc($r_user) ){
						if( $user['tenant'] <= 0 ){
							continue;
						}

						$administrator = new Administrator();
						$administrator->setCivility( $user['title_id'] )
									->setFirstname( $user['adr_firstname'] )
									->setLastname( $user['adr_lastname'] )
									->setPhone( $user['phone'] )
									->setMobile( $user['mobile'] )
									->setWork( $user['work'] )
									->setFax( $user['fax'] )
									->setEmail( $user['email'] )
									->setLang( 'fr_FR' )
									->setPassword( $user['password'], true )
									->addTenantAccess( $tnt_id );

						$res = $administrator->save( false );
						if( $res === -1 ){
							print '	Compte existant déjà : '.$user['email'].PHP_EOL;
						}elseif( !$res ){
							print '	Erreur lors de la création du compte : '.$user['email'].PHP_EOL;
						}
					}
				}
			}
		}

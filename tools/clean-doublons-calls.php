<?php
/**
 * Ce fichier permet de faire le néttoyage dans les appels téléphonique en double
 * attention cela ne va pas les supprimers physique mais que virtuelement pour que Yuto s'adapte
 */

set_include_path(dirname(__FILE__).'/../include/');

print "L'utilisation de ce fichier n'est pas recommandé. Si vraiment nécessaire, décommentez le code ci-dessous.\n";

require_once('db.inc.php');
require_once('tenants.inc.php');
require_once('RegisterGCP.inc.php');
require_once('users.inc.php');
require_once('calls.inc.php');

$ar_tenant_ids = RegisterGCP::create()->getTenantIDs();
if (!is_array($ar_tenant_ids) || !count($ar_tenant_ids)) {
		exit;
}
foreach ($ar_tenant_ids as $tnt_id) {
	if (is_numeric($tnt_id) && $tnt_id > 0) {
		RegisterGCPConnection::init( $tnt_id, true, true );
		print 'Tenant => '.$tnt_id.PHP_EOL;


		$fetchs = array(); // tableau pour sotcker les datas deja passé key = clé unique, value = id de l'appel

		$rcalls = gcl_calls_get_by_view( "", 0, 0);
	    foreach ($rcalls as $key => $call) {
	        if (is_array($call)) {

	        	$key = ""
		        	.( isset($call['gcl_author_ref']) ? $call['gcl_author_ref'] : "")
		        	.( isset($call['gcl_usr_ref']) ? $call['gcl_usr_ref'] : "")
		        	.( isset($call['gcl_usr_email']) ? $call['gcl_usr_email'] : "")
		        	.( isset($call['gcl_date_created']) ? $call['gcl_date_created'] : "")
		        	.( isset($call['gcl_phone']) ? $call['gcl_phone'] : "")
		        	.( isset($call['gcl_author_email']) ? $call['gcl_author_email'] : "")
		        		;

	        	if( isset($fetchs[ $key ] ) ){
	        		print "delete call id : ".$call['_id']."\n";
	        		gcl_calls_del($call['_id']);
	        	}else{
		        	$fetchs[ $key ] = $call['_id'];
		        }

			}
		}
	}
}
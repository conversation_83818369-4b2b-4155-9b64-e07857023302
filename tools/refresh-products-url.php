<?php
	if( !isset($argv[1]) || !is_numeric($argv[1]) ){
		$help = PHP_EOL;
		$help .= 'Utilisation du rafraichissement des miniatures d\'image: refresh-products-url.php identifiant_tenant'.PHP_EOL;
		$help .= '	- premier paramètre obligatoire, correspond à l\'identifiant de locataire.'.PHP_EOL;
		$help .= '	- deuxième paramètre obligatoire, correspond à la confirmation d\'exécution du script'.PHP_EOL;
		$help .= PHP_EOL;
		echo $help;
		exit;
	}

	set_include_path(dirname(__FILE__).'/../include');
	require_once('tenants.inc.php');
	require_once('RegisterGCP.inc.php');
	RegisterGCPConnection::init((int) $argv[1], true);

	require_once('rewrite.inc.php');
	require_once('products.inc.php');
	if( !isset($argv[2]) || $argv[2] != 'true' ){
		$warning = PHP_EOL;
		$warning .= 'ATTENTION à ce script, veuiller confirmer son exécution'.PHP_EOL;
		$warning .= PHP_EOL;
		echo $warning;
		exit;
	}

	// Suppression de toutes les urls liées au catégories
	$count = rew_rewritemap_del_by_objects( CLS_PRODUCT );
	print 'Nombre d\'urls supprimées : '.$count."\n\n";

	// Recréation de toutes les urls catégories
	$r_cly = prd_classify_get();
	if( $r_cly ){
		while( $cly = ria_mysql_fetch_array( $r_cly ) ){
			// recréer l'url
			$new_url = prd_products_url_alias_add( $cly['prd'], $cly['cat'] );

			if( isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']) > 1 ){
				foreach( $config['i18n_lng_used'] as $one_lng ){
					if( $one_lng == $config['i18n_lng'] ){
						continue;
					}

					rew_rewritemap_add_multilingue( array($cly['cat'], $cly['prd']), CLS_PRODUCT, $one_lng, $new_url, _FLD_PRD_URL );
				}
			}

			print $new_url."\n";
		}
	}

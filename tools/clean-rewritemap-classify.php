<?php
	
	/** \file clean-rewritemap-classify.php
	 * 	Ce script est destiné à nettoyer la table "rew_rewritemap" des URLs pointant vers des classements ("prd_classify") qui n'existent pas.
	 *	Il est à noter que le script ne nettoye pas (pour l'instant) les redirections 301.
	 *	Pour chaque ligne supprimée, une ligne de type "REPLACE INTO" est crée dans le fichier de sauvegarde.
	 *	La sauvegarde se trouve à l'adresse /var/www/riashop.riastudio.fr/sql/backup-rew_rewritemap[tnt-X]-yyyymmddhhmmss.sql (où X est l'identifiant du locataire).
	 *	Le script peut être interrompu et repris ultérieurement.
	 *	En spécifiant "-t" (-test) en argument, aucune URL ne sera réellement supprimée.
	 *	En spécifiant "-q" (-quiet) en argument, seules les erreurs sont affichées.
	 */
	
	$dry_run = $quiet = false;
	for( $i = 1; $i < sizeof($argv); $i++ ){
		$arg =  strtolower(trim($argv[ $i ]));
		if( $arg == '-t' || $arg == '-test' ){
			$dry_run = true;
		}elseif( $arg == '-q' || $arg == '-quiet' ){
			$quiet = true;
		}
	}
	
	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once('define.inc.php');
	require_once('rewrite.inc.php');
	require_once('cfg.variables.inc.php');
	require_once('products.inc.php');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	
	// charge la / les configuration(s) d'URLs
	$rcfg_url = cfg_urls_get( 0, CLS_PRODUCT );
	if( !$rcfg_url || !ria_mysql_num_rows($rcfg_url) ){
		print "Impossible de charger cfg_urls_get()\n";
		exit;
	}
	
	// récupère tous les classements
	$rcly = prd_classify_get();
	if( !$rcly ){
		print "Impossible de charger prd_classify_get().\n";
		exit;
	}
	
	// tableau de toutes les URLs internes valides + tableau de tous les patterns possibles
	$cly_ar = $patterns = array();
	while( $cfg_url = ria_mysql_fetch_array($rcfg_url) ){
		while( $cly = ria_mysql_fetch_array($rcly) ){
			$cly_ar[] = strtolower($cfg_url['url'].'?cat='.$cly['cat'].'&prd='.$cly['prd']);
		}
		ria_mysql_data_seek($rcly, 0); // important dans une double boucle !
		$patterns[] = strtolower($cfg_url['url']);
	}
	$patterns = array_unique($patterns);
	
	if( !sizeof($cly_ar) ){
		print "Aucune URL interne n'a pu être déterminée à partir des classements de produit. Cette situation est anormale, le script va être stoppé.\n";
		exit;
	}
	
	$i = $err = 0;
	$first_row = true;
	$filename = '/var/www/riashop.riastudio.fr/sql/backup-rew_rewritemap[tnt-'.$config['tnt_id'].']-'.date('YmdHis').'.sql';
	
	$log_file = fopen($filename, 'w+');
	
	if( $log_file === false ){
		print "Impossible de créer le fichier de sauvegarde SQL. Le script va être stoppé.\n";
		exit;
	}
	
	foreach( $patterns as $pattern ){
		
		if( !$quiet ){
			print "Chargement des URLs pour le pattern ".$pattern." (peut prendre plusieurs secondes)...\n";
		}
		
		// chargement de toutes les URLs pour ce pattern
		// pattern contient typiquement "/catalog/product.php"
		$rrew = rew_rewritemap_get( '', '', 200, null, false, $pattern.'?cat=%&prd=', 'intern' );
		if( !$rrew ){
			print "Erreur au chargement de rew_rewritemap_get( pattern = ".$pattern." ). Le script va être stoppé.\n";
			break;
		}
		
		while( $rew = ria_mysql_fetch_array($rrew) ){
			
			// on vérifie si l'URL interne courante est présente dans le tableau de celles construites à partir de prd_classify
			if( !in_array(strtolower($rew['intern']), $cly_ar) ){
				
				// suppression de l'URL
				$del_ok = true;
				if( !$dry_run ){
					$del_ok = rew_rewritemap_del( $rew['extern'], '', $rew['lng_code'], $rew['wst_id'] );
				}
				
				if( !$del_ok ){
					print "Erreur inconnue lors de rew_rewritemap_del().\n";
					$err++;
				}else{
					$i++;
					if( !$quiet ){
						print "Suppression OK de l'URL ".$rew['extern']." (site ".$rew['wst_id'].", langue ".$rew['lng_code'].").\n";
					}
					
					$fields = array('url_tnt_id', 'url_lng_code', 'url_wst_id', 'url_extern', 'url_intern', 'url_code', 'url_public');
					$values = array($config['tnt_id'], '"'.$rew['lng_code'].'"', $rew['wst_id'], '"'.addslashes($rew['extern']).'"', '"'.addslashes($rew['intern']).'"', $rew['code'], $rew['public']);
					
					if( $first_row ){
						fwrite($log_file, 'use riashop;'."\n");
						$first_row = false;
					}
					
					// écrit la ligne supprimée dans le log
					if( $log_file !== false ){
						@fwrite($log_file, 'REPLACE INTO rew_rewritemap ('.implode(', ', $fields).') values ('.implode(', ', $values).');'."\n");
					}
				}
				
			}else{
				if( !$quiet ){
					print "URL valide : ".$rew['extern']."\n";
				}
			}
			
		}
	}
	
	if( $log_file !== false ){
		@fclose($log_file);
	}
	
	// rien n'a été écrit, on peut supprimer le fichier .sql
	if( $first_row ){
		@unlink($filename);
	}
	
	if( !$quiet ){
		print "Fin de procédure (".$i." lignes supprimées, ".$err." erreurs)\n";
	}


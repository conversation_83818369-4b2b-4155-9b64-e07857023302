<?php
	/**	Classe les produits dans cat_export_amazon
	 */
	
	$dev = strpos( getenv('PWD'), 'maquettes' )!==false;
	
	set_include_path( '/var/www/riashop'.(  $dev? '.maquettes' : '' ).'.riastudio.fr/include/' );
	require_once( 'products.inc.php' );
	require_once( 'promotions.inc.php' );
	require_once('products.inc.php');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	if( !isset($config['cat_export_amazon']) ){
		print 'Catégorie Amazon non définie.'."\n";
		return;
	}
	
	$products = array(
		'22101004',
		'22314001',
		'22314002',
		'22314003',
		'22401001',
		'22401002',
		'22401006',
		'22401008',
		'22401014',
		'22401061',
		'22401062',
		'22401067',
		'22401069',
		'22401070',
		'22401071',
		'22401105',
		'22401106',
		'22401107',
		'23332303',
		'23333020',
		'23333025',
		'23333028',
		'23333031',
		'23333034',
		'23333038',
		'23333044',
		'23333047',
		'23333055',
		'23333073',
		'23333502',
		'23333503',
		'23333504',
		'23333551',
		'23333560',
		'23333561',
		'23333562',
		'23333563',
		'23500522',
		'27335948',
		'31410516',
		'31411098',
		'31412001',
		'31412002',
		'31412003',
		'31412112',
		'31412113',
		'31412123',
		'31412124',
		'31412126',
		'31412127',
		'31412128',
		'31418101',
		'31418104',
		'31418107',
		'31418109',
		'31418110',
		'31418111',
		'31418235',
		'31418236',
		'31418237',
		'31418238',
		'31418239',
		'31418240',
		'32421167',
		'32421180',
		'32421181',
		'32421182',
		'32422021',
		'32422025',
		'32422026',
		'32422027',
		'32423006',
		'32423032',
		'32423040',
		'32425060',
		'32425061',
		'32425062',
		'32426036',
		'32426038',
		'32426041',
		'32426046',
		'32426047',
		'32427101',
		'32427220',
		'32427226',
		'32427236',
		'32427641',
		'32427654',
		'32427670',
		'32428001',
		'32428002',
		'32428003',
		'32428004',
		'32428005',
		'32429001',
		'33100005',
		'33100006',
		'33100007',
		'33100008',
		'33100009',
		'33100010',
		'33100011',
		'33100012',
		'33200003',
		'33200004',
		'42500599',
		'42500600',
		'42500601',
		'42500602',
		'42500610',
		'42501313',
		'42501316',
		'42501319',
		'42501322',
		'42501324',
		'42501380',
		'42501381',
		'42501392',
		'42501403',
		'42501411',
		'42501431',
		'42501519',
		'42501526',
		'42501528',
		'42501591',
		'42501593',
		'43490500',
		'43490502',
		'43490505',
		'43490507',
		'43490512',
		'43490513',
		'43490514',
		'43490515',
		'43490518',
		'43537015',
		'43537016',
		'44517330',
		'44517334',
		'44517335',
		'44517339',
		'44517340',
		'44517343',
		'44517350',
		'44517351',
		'44517353',
		'44517354',
		'44517375',
		'44517377',
		'44517378',
		'44517384',
		'44517386',
		'44517387',
		'44517392',
		'44517394',
		'44517395',
		'44527471',
		'44528346',
		'44528347',
		'44528348',
		'45520800',
		'45520804',
		'45520806',
		'45520808',
		'45520809',
		'45520810',
		'45520813',
		'45520815',
		'45520817',
		'45520819',
		'45520820',
		'45520829',
		'45520830',
		'45520831',
		'45520832',
		'45520842',
		'45520880',
		'45520881',
		'45520882',
		'45520883',
		'45520884',
		'45520886',
		'45527653',
		'45527655',
		'45527656',
		'45527657',
		'45527658',
		'45527659',
		'45527660',
		'45527661',
		'45527662',
		'45527663',
		'45527664',
		'45527665',
		'45527666',
		'45527667',
		'45527668',
		'45527669',
		'45527670',
		'45529026',
		'45529027',
		'45529028',
		'45529048',
		'45529049',
		'45529050',
		'45529052',
		'45529055',
		'45532000',
		'45532001',
		'46200001',
		'46200002',
		'46200003',
		'46200004',
		'46200005',
		'46531134',
		'46531136',
		'46531141',
		'46531142',
		'46531175',
		'46531182',
		'46531183',
		'46531186',
		'46531188',
		'46531194',
		'46531200',
		'46531208',
		'46531215',
		'46531220',
		'46531240',
		'46531241',
		'46531242',
		'46531243',
		'46531244',
		'46531245',
		'46531246',
		'46531247',
		'46531248',
		'46531249',
		'46531250',
		'46531612',
		'46531613',
		'46531614',
		'46531657',
		'46532401',
		'46532402',
		'46532403',
		'46532404',
		'46532405',
		'46532406',
		'46532407',
		'46532408',
		'46532409',
		'46532410',
		'46532411',
		'46533001',
		'46534430',
		'46534431',
		'46534432',
		'46534434',
		'46534435',
		'46534438',
		'46534440',
		'46534441',
		'46534442',
		'46534443',
		'46534460',
		'46534470',
		'46534473',
		'46534480',
		'46534481',
		'46534482',
		'47535645',
		'47535646',
		'47535648',
		'47535649',
		'47535650',
		'47535651',
		'47535653',
		'475356543',
		'47535656',
		'47535658',
		'47535659',
		'47535660',
		'47535661',
		'47535663',
		'47535667',
		'47535669',
		'47535670',
		'47535671',
		'47535673',
		'47535676',
		'48400020',
		'48400021',
		'48400022',
		'48400023',
		'48400024',
		'48400030',
		'48401001',
		'48401002',
		'48510000',
		'48510013',
		'48510014',
		'48510015',
		'48510020',
		'48510025',
		'48540014',
		'48540016',
		'48540036',
		'48540038',
		'48540044',
		'48540046',
		'48540047',
		'48540048',
		'48540051',
		'48540052',
		'48540058',
		'48540059',
		'48540060',
		'48540068',
		'48540075',
		'48540079',
		'48540081',
		'48540718',
		'48540723',
		'48540724',
		'48540725',
		'48540726',
		'48540727',
		'48540728',
		'48540729',
		'48540731',
		'48540733',
		'48540738',
		'48540739',
		'48540741',
		'48540742',
		'48540745',
		'48540746',
		'48540904',
		'48540905',
		'48540906',
		'48540907',
		'48540910',
		'48540911',
		'48540912',
		'48543336',
		'52502100',
		'52502144',
		'52502164',
		'52502502',
		'52502506',
		'52502566',
		'52504301',
		'52504306',
		'52504334',
		'52504339',
		'52504346',
		'52504351',
		'52504390',
		'52504392',
		'52504394',
		'52504395',
		'55528526',
		'55528527',
		'55528528',
		'56200001',
		'56200002',
		'56531155',
		'56531156',
		'56532412',
		'56534229',
		'56534242',
		'56534243',
		'56534244',
		'56534247',
		'56534250',
		'56534262',
		'56534263',
		'56534264',
		'56534265',
		'56534266',
		'56534267',
		'56800001',
		'56800002',
		'56800003',
		'56800005',
		'57535339',
		'57535343',
		'57535344',
		'57535345',
		'57535346',
		'57535347',
		'57535450',
		'57535451',
		'57535452',
		'57535453',
		'57539002',
		'57539010',
		'57539011',
		'57539014',
		'57539015',
		'57539017',
		'57539018',
		'57539019',
		'57539021',
		'57539022',
		'57539023',
		'57539024',
		'57539025',
		'57539026',
		'58540500',
		'58540501',
		'58540550',
		'58540552',
		'58540554',
		'58540558',
		'58540560',
		'58540561',
		'58542442',
		'58542604',
		'58542605',
		'58542751',
		'58542752',
		'58542753',
		'58542754',
		'58542755',
		'58542756',
		'58543263',
		'58543264',
		'58543265',
		'58543266',
		'64565001',
		'64565003',
		'64565007',
		'64565033',
		'64565045',
		'64565046',
		'64565047',
		'64565048',
		'64565075',
		'64565077',
		'64565078',
		'64565079',
		'64565081',
		'64565103',
		'64565107',
		'64565111',
		'64565120',
		'64565121.',
		'64565122',
		'64565130',
		'64565134',
		'64565140',
		'64565145',
		'64565202',
		'64565208',
		'64565222',
		'64565243',
		'64565244',
		'64565260',
		'64565263',
		'64565264',
		'64565266',
		'64565269',
		'64565271',
		'64565274',
		'64565277',
		'64565298',
		'64565304',
		'64565307',
		'64565355',
		'64565356',
		'64565357',
		'64565358',
		'64565371',
		'65610260',
		'65610262',
		'65610263',
		'65610264',
		'65610265',
		'65610269',
		'65610274',
		'65610275',
		'65610276',
		'65610277',
		'65610278',
		'65610280',
		'66620330',
		'66620331',
		'66620332',
		'66620596',
		'66620597',
		'66620598',
		'67632300',
		'67632301',
		'67632302',
		'67632303',
		'67632304',
		'67632310',
		'67632311',
		'67632312',
		'69651730',
		'69651731',
		'69651732',
		'69652122',
		'69653508',
		'69653509',
		'69653510',
		'69653511',
		'69653516',
		'69653517',
		'69653518',
		'69653519',
		'69653521',
		'69653522',
		'69653523',
		'69653527',
		'69654030',
		'69654031',
		'69660002',
		'77800400',
		'77800401',
		'77800402',
		'77800456',
		'77800457',
		'77800649',
		'77800650',
		'77800651',
		'77800653',
		'77800654',
		'77800655',
		'77800656',
		'77800657',
		'77800660',
		'77800661',
		'77800662',
		'77800663',
		'77800670',
		'77800671',
		'77800672',
		'78800027',
		'78800030',
		'78800033',
		'78800053',
		'78800055',
		'78800056',
		'78800139',
		'78800140',
		'78800141',
		'78800142',
		'78800150',
		'78800151',
		'78800152',
		'78800153',
		'78800173',
		'78800174',
		'78800175',
		'78800176',
		'78800181',
		'78800182',
		'78800184',
		'78800185',
		'78800186',
		'78800187',
		'78800188',
		'78800189',
		'78800190',
		'78800191',
		'78800196',
		'78800197',
		'78800198',
		'78800199',
		'78800210',
		'78800211',
		'78800212',
		'78800213',
		'78800214',
		'78800215',
		'78800375',
		'78800501',
		'82540066',
		'82540068',
		'45527653',
		'45527655',
		'45527656',
		'45529026',
		'45529027',
		'45529028'
	);
	
	$rprd = prd_products_get_simple( 0, $products, true, 0, false, false, true );
	
	if( !$rprd ){
		print 'Erreur lors du chargement des produits.'."\n";
		return;
	}
	
	while( $prd = ria_mysql_fetch_array($rprd) ){
		if( prd_products_add_to_cat( $prd['id'], $config['cat_export_amazon'] ) ){
			print 'Produit '.$prd['ref'].' classé.'."\n";
		}else{
			print 'ERROR - Produit '.$prd['ref'].' non classé.'."\n";
		}
	}
	
	print 'Fin de procédure.'."\n";
	


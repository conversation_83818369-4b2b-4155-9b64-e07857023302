<?php
	/** \file refresh-images-cache.php
	 *	\ingroup crontabs ctr_comparators
	 * 	Ce script est destiné à mettre à jour le cache sur les images.
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once('images.inc.php');

	if (!array_key_exists('img_sizes', $config)) {
		error_log(__FILE__.':'.__LINE__.' COnfigurations images non disponibles');
		exit;
	}

	$url_site = isset($argv[1]) ? $argv[1] : '';
	if (trim($url_site) == '') {
		print 'Premier paramètre obligatoire : URL du site (ex. http://www.chazelles.com).'.PHP_EOL;
		exit;
	}

	$img_id 	= isset($argv[2]) && is_numeric($argv[2]) && $argv[2] > 0 ? $argv[2] : 0;
	$code_img	= isset($argv[3]) && trim($argv[3]) != '' ? $argv[3] : 0;

	$r_image = img_images_get( $img_id );
	if ($r_image) {
		while ($image = ria_mysql_fetch_assoc($r_image)) {
			foreach ($config['img_sizes'] as $code => $cfg_img) {
				if (trim($code_img) != '' && $code != $code_img) {
					continue;
				}

				$url_image = $url_site.'/images/products/'.$cfg_img['width'].'x'.$cfg_img['height'].'/'.$image['id'].'.'.$cfg_img['format'];
				print 'curl -X PURGE '.$url_image.PHP_EOL;
			}
		}
	}

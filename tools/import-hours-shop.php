<?php

/* argv[1] correspond au tenant
 * argv[2] correspond au fichier .csv à importer
 */

 set_include_path(dirname(__FILE__) . '/../include/');
require_once('cfg.variables.inc.php');
require_once('periods.inc.php');
require_once('strings.inc.php');
require_once('delivery.inc.php');



if (($handle = fopen($argv[2], "r")) !== FALSE) {

    unset($config);
    $configs = cfg_variables_get_all_tenants($argv[1]);
    $header_row = true;
    $days =  array( 1 => 'monday', 2 =>'thuesday', 3 =>'wednesday', 4 =>'thursday', 5 =>'friday', 6 =>'saturday', 7 =>'sunday' );

    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
        if($header_row){//Ignore la première ligne du .csv
            $header_row = false;
        }else{
            $champs = explode(';', $data[0]);
            if(dlv_stores_exists($champs[0])){
                $id_magasin = $champs[0];
            }else{
                error_log("L'identifiant ". $champs[0]." ne correspond à aucun magasin.");
                continue;
            }

            $j=2;//Variable parcourant les collones du tableau
            foreach($days as $day_id=>$day){
                $start_morning = false;
                $continuous_hour = false;
                for($i=1 ; $i<=2; $i++){ //1=matin 2=après-midi
                    $start = $j;
                    $end = $j+1;
                    $j = $j+2;
                    if(ishour($champs[$start]) && ishour($champs[$end])){
                        $r_magasin = per_objects_get( CLS_STORE, $id_magasin, 0, 0, 0, array($i));
                        if($r_magasin && ria_mysql_num_rows($r_magasin)){
                           $magasin = ria_mysql_fetch_assoc($r_magasin);
                        }else{
                            $magasin['id'] = per_objects_add(CLS_STORE, $i ,$id_magasin);
                        }
                        $r_period = per_periods_get($magasin['id'],$day_id);
                        if($r_period && ria_mysql_num_rows($r_period)){//met a jour la période si elle existe déja
                            $period = ria_mysql_num_rows($r_period);
                            per_periods_update($magasin['id'], $day_id, $champs[$start], $champs[$end], true);
                        }else{//ajoute la période si elle n'existe pas
                            per_periods_add($magasin['id'], $day, $champs[$start], $champs[$end], true);
                        }
                    }else{
                        //Gere le cas des magasins avec une journée continue
                        if($i==1 && ishour($champs[$start])){
                            $start_morning = $champs[$start];
                        }
                        if($i==2 && ishour($champs[$end]) && $start_morning){
                            if(strtotime($start_morning)<=strtotime('12:00') && strtotime($champs[$end]) >= strtotime('12:00')){
                                $middle_hour = '12:00';
                            }else{
                                $middle_hour = $start_morning;
                            }

                            $r_magasin = per_objects_get( CLS_STORE, $id_magasin, 0, 0, 0, array(1));
                            if($r_magasin && ria_mysql_num_rows($r_magasin)){
                            $magasin = ria_mysql_fetch_assoc($r_magasin);
                            }else{
                                $magasin['id'] = per_objects_add(CLS_STORE, 1 ,$id_magasin);
                            }
                            $r_period = per_periods_get($magasin['id'],$day_id);
                            if($r_period && ria_mysql_num_rows($r_period)){//met a jour la période si elle existe déja
                                $period = ria_mysql_num_rows($r_period);
                                per_periods_update($magasin['id'], $day_id, $start_morning, $middle_hour, true);
                            }else{//ajoute la période si elle n'existe pas
                                per_periods_add($magasin['id'], $day, $start_morning, $middle_hour, true);
                            }

                            $r_magasin = per_objects_get( CLS_STORE, $id_magasin, 0, 0, 0, array(2));
                            if($r_magasin && ria_mysql_num_rows($r_magasin)){
                            $magasin = ria_mysql_fetch_assoc($r_magasin);
                            }else{
                                $magasin['id'] = per_objects_add(CLS_STORE, 2 ,$id_magasin);
                            }
                            $r_period = per_periods_get($magasin['id'],$day_id);
                            if($r_period && ria_mysql_num_rows($r_period)){//met a jour la période si elle existe déja
                                $period = ria_mysql_num_rows($r_period);
                                per_periods_update($magasin['id'], $day_id, $middle_hour, $champs[$end], true);
                            }else{//ajoute la période si elle n'existe pas
                                per_periods_add($magasin['id'], $day, $middle_hour, $champs[$end], true);
                            }
                            $continuous_hour = true;
                        }


                        if(!$continuous_hour){
                            //Supprime la période si elle existe deja mais qu'aucune heure n'est renseigné dans le .csv
                            $r_magasin = per_objects_get( CLS_STORE, $id_magasin, 0, 0, 0, array($i));
                            if($r_magasin && ria_mysql_num_rows($r_magasin)){
                                $magasin = ria_mysql_fetch_assoc($r_magasin);
                            }else{
                                continue;
                            }
                            $r_period = per_periods_get($magasin['id'],$day_id);
                            if($r_period && ria_mysql_num_rows($r_period)){
                                $period = ria_mysql_num_rows($r_period);
                                per_periods_del($magasin['id'], $day_id);
                            }
                        }
                    }
                }
            }
        }
    }
    fclose($handle);

}


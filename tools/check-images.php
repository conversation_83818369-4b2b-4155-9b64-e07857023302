<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once('strings.inc.php');
	
	$count = $missing = $resolved = 0;
	$forced = isset($argv[1]) && $argv[1]=='true' ? true : false;
	$forced_image = isset($argv[2]) && is_numeric($argv[2]) && $argv[2]>0 ? $argv[2] : 0;

	$ar_configs = array();
	if( sizeof($argv) >= 3 ){
		for( $i=3 ; $i <= sizeof($argv) ; $i++ ){
			if( !isset($argv[$i]) || trim($argv[$i]) == '' ){
				break;
			}

			$ar_configs[] = trim( $argv[$i] );
		}
	}
	
	$count = $missing = $resolved = 0;
	$images = img_images_get( $forced_image, '', '', '', '', null, false, false, false, null, ['id' => 'desc'] );
	while( $image = ria_mysql_fetch_array($images) ){
		try
		{
			$img_id = $image['id'];
			if( img_images_exists( $img_id ) ){ 
				if( $forced_image> 0 && $img_id!=$forced_image ) continue;
				
				if( !($result = img_images_thumbnails_refresh( $img_id, $forced, false, $ar_configs )) ){
					throw new Exception('echec img_images_thumbnails_refresh');
				}

				$count += $result['count'];
				$missing += $result['missing'];
				$resolved += $result['resolved'];
					
				print 'Image : '.$img_id." | total : ".$result['count']." | manquante : ".$result['missing']." | creer : ".$result['resolved']."\n";
					
			}
		}
		catch (Exception $ex)
		{
			error_log(__FILE__.':'.__LINE__.' Image '.$image['id'].' : '.$ex->getMessage());
			print 'Image '.$image['id'].' : '.$ex->getMessage()."\n";
		}
	}

	echo number_format($missing,0,',',' ').' fichiers manquants sur un total de '.number_format($count,0,',',' ').' images'."\n";
	echo number_format($resolved,0,',',' ').' fichiers regeneres sur un total de '.number_format($missing,0,',',' ').' images manquantes'."\n";

<?php
	
	/**
	 *	Ce fichier permet de générer le flux RSS d'un site dans toutes les langues. Le fichier rss des langues étrangère 
	 *	se nomme "actualites-[lng].rss" celui de la langue par défaut actualites.rss
	 *
	 *	Pré requis : le dossier du site doit contenir un dossier /htdocs/pages/rss
	 */
	
	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once('news.inc.php');
	require_once('i18n.inc.php');

	i18n::enterContext('NEWS');
	
	// chaque langue à son propre fichier RSS d'actualité
	foreach( $config['i18n_lng_used'] as $lng ){
		i18n::setLang( $lng );
		
		$file = $config['site_dir'].'/pages/rss/actualites'.( $lng!=$config['i18n_lng'] ? '-'.$lng : '' ).'.rss';
		
		$fp = fopen( $file, 'w' ) or die('Impossible d\'ouvrir le fichier des actualités'."\n");

		fwrite( $fp, '<?xml version="1.0" encoding="utf-8"?>'."\n" );
		fwrite( $fp, '<rss xmlns:content="http://purl.org/rss/1.0/modules/content/" version="2.0">'."\n" );

		// Description du fil RSS
		fwrite( $fp, '	<channel>'."\n" );
		fwrite( $fp, '		<title>'.i18n::get('Actualités').' - '.htmlspecialchars($config['site_name']).'</title>'."\n" );
		fwrite( $fp, '		<link>'.$config['site_url'].'/actualites/</link>'."\n" );
		fwrite( $fp, '		<description>'.htmlspecialchars($config['meta_description']).'</description>'."\n" );
		fwrite( $fp, '		<language>fr-FR</language>'."\n" );
		fwrite( $fp, '		<pubDate>'.date( 'Y-m-d H:i:s' ).'</pubDate>'."\n" );
		
		// récupère les actualités
		$ractu = news_get( 0, true, null, 0, $config['wst_id'], $lng );
		if( $ractu ){
			while( $actu = ria_mysql_fetch_array($ractu) ){
				$sizeImg = $config['img_sizes']['medium'];
				
				$idImg = $actu['img_id'];
				if( !is_numeric($idImg) || $idImg<=0 ){
					$rimg = news_images_get( $actu['id'] );
					if( $rimg && ria_mysql_num_rows($rimg) )
						$idImg = ria_mysql_result( $rimg, 0, 'id' );
				}
				
				$image = '';
				if( $idImg ){
					$image = '<img style="float:left !important; margin-right:10px; !important" width="'.$sizeImg['width'].'" height="'.$sizeImg['height'].'" alt="'.htmlspecialchars($actu['name']).'" src="'.$config['img_url'].'/'.$sizeImg['width'].'x'.$sizeImg['height'].'/'.$idImg.'.'.$sizeImg['format'].'" />';
				}
				
				fwrite( $fp, '		<item>'."\n" );
				fwrite( $fp, '			<title><![CDATA['.htmlspecialchars($actu['name']).']]></title>'."\n" );
				fwrite( $fp, '			<description><![CDATA['.$image.'<br />'.htmlspecialchars( str_replace('&nbsp;', ' ', html_strip_tags($actu['desc'])) ).']]></description>'."\n" );
				fwrite( $fp, '			<link>'.$config['site_url'].$actu['url_alias'].'</link>'."\n" );
				fwrite( $fp, '			<guid isPermaLink="true">'.$config['site_url'].$actu['url_alias'].'</guid>'."\n" );
				fwrite( $fp, '			<pubDate><![CDATA['.$actu['publish_date'].']]></pubDate>'."\n" );
				fwrite( $fp, '			<source url="'.$config['site_url'].'/rss/nouveaute.rss"><![CDATA['.htmlspecialchars($config['site_name']).']]></source>'."\n" );
				/* if( $idImg ){
					fwrite( $fp, '			<enclosure url="'.$config['img_url'].'/'.$sizeImg['width'].'x'.$sizeImg['height'].'/'.$idImg.'.'.$sizeImg['format'].'" type="image/jpeg" ></enclosure>'."\n" );
				} */
				fwrite( $fp, '		</item>'."\n");
			}
		}
	
	}
	
	i18n::leaveContext();

<?php
	/** \file refresh_prd_parents_publish.php
	 * 	Ce script est destiné à mettre à jour l'information de publication des articles parents en fonction de leurs enfants
	 *	La variable de configuration catalog_parent_publish doit être définie à "auto"
	 *	Le premier paramètre passé à true permet d'exécuter le script sans apporté de changement (pré-visualisation des informations de publication)
	 */


	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'products.inc.php' );

	$no_action = isset($argv[1]) && $argv[1] == 'true';
	var_dump($no_action);

	// Récupère les identifiants de tous les articles parents
	$ar_parents = array();

	$r_rel = prd_hierarchy_get();
	if ($r_rel && ria_mysql_num_rows($r_rel)) {
		while ($rel = ria_mysql_fetch_assoc($r_rel)) {
			$ar_parents[] = $rel['parent'];
		}

		$ar_parents = array_unique( $ar_parents );
	}

	print 'count : '.count($ar_parents).PHP_EOL;

	if (count($ar_parents)) {
		prd_products_set_parent_publish( 0, $ar_parents, $no_action );
	}


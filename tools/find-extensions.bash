#!/bin/bash

if [[ "$1" == "" ]]
	then 
		echo " 
Script donnant l'extension de tous les fichiers d'un repertoire.
Usage : $0 <DIRECTORY>
"
	exit 1
fi
find $1 -type f -exec file {} \; | \
sed -e "s/$1\///g" |\
sed -e 's/: /|/g' |\
sed -e 's/|JPEG.*$/;jpg/g' | \
sed -e 's/|TIF.*$/;tif/g' | \
sed -e 's/|Adobe.*$/;psd/g' | \
sed -e 's/|PNG.*$/;png/g' | \
sed -e 's/|PDF.*$/;pdf/g' | \
sed -e 's/|GIF.*$/;gif/g' | \
sed -s 's/|ASCII.$/;txt/g' 

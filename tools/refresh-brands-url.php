<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('rewrite.inc.php');
	require_once('products.inc.php');
	
	die('ATTENTION à ce script');

	// Suppression de toutes les urls liées aux marques
	$count = rew_rewritemap_del_by_objects( CLS_BRAND );
	print 'Nombre d\'urls supprimées : '.$count."\n\n";

	// Recréation de toutes les urls marques
	$r_brand = prd_brands_get();
	if( $r_brand ){
		while( $brand = ria_mysql_fetch_array( $r_brand ) ){
			// supprime l'url alias s'il existe
			if( $brand['url_alias'] ){
				rew_rewritemap_del( $brand['url_alias'] );
				rew_rewritemap_del( '', $brand['url_alias'] );
			}
			
			// recréer l'url
			$new_url = prd_brands_url_alias_add( $brand['id'] );

			if( isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']) > 1 ){
				foreach( $config['i18n_lng_used'] as $one_lng ){
					if( $one_lng == $config['i18n_lng'] ){
						continue;
					}

					rew_rewritemap_add_multilingue( array($brand['id']), CLS_BRAND, $one_lng, $new_url, _FLD_BRD_URL );
				}
			}

			print $new_url."\n";
		}
	}


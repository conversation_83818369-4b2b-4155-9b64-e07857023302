<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('categories.inc.php');
	
	$simulate = isset($argv[1]) && $argv[1] == 'true' ? true : false;
	
	$categories = prd_categories_get_all();
	while( $r = ria_mysql_fetch_assoc($categories) ){
		if( !$simulate ){
			print $r['name'].' : ok'."\n";
			prd_categories_refresh_products_published( $r['id'] );
		}else{
			$res = prd_categories_need_refresh_products_published( $r['id'] );
			print '[ '.$r['id'].' ][ '.$r['name'].' ][ '.( $res ? 'OUI' : 'NON' ).' ]'."\n";
		}
	}



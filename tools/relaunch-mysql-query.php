<?php
    set_include_path(dirname(__FILE__).'/../include');
    require_once('define.inc.php');
    require_once('db.inc.php');
    require_once('ria.mysql.inc.php');

    $file_log       = isset($argv[1]) ? $argv[1] : '';
    $file_report    = '/var/log/php/report-relaunch-mysql-query.csv';

    if (trim($file_log) == '' || !file_exists($file_log)) {
        die('Fichier de log introuvable');
    }

    $res = array();

    if (($handle = fopen($file_log, 'r')) !== false) {
        for ($i=0; $i < 2; $i++) { 
            $l = 0;
            while (($line = fgets($handle)) !== false) {
                $data = explode(';', $line);

                if (!is_array($data) || count($data) != 3) {
                    continue;
                }
                
                $data[1] = number_format($data[1], 6);
                $data[2] = trim($data[2]);

                $start = microtime(true);
                ria_mysql_query($data[2]);
                $end = microtime(true);

                if ($i == 0) {
                    $res[$l] = $data;
                }

                $res[$l][] = number_format(round(($end - $start), 6), 6);
                
                $l++;
            }
            
            fseek($handle, 0);
        }
        
        fclose($handle);

        if (count($res)) {
            $handle_relaunch = fopen($file_report, 'w');
            foreach ($res as $r) {
                fputcsv($handle_relaunch, $r, ';', '"');
            }
            
            fclose($handle_relaunch);
        }
    }
    
    print 'Le rapport se trouve ici : '.$file_report.PHP_EOL;
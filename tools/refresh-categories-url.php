<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('categories.inc.php');
	
	die('ATTENTION à ce script');

	// Suppression de toutes les urls liées au catégories
	$count = rew_rewritemap_del_by_objects( CLS_CATEGORY );
	print 'Nombre d\'urls supprimées : '.$count."\n\n";

	// Recréation de toutes les urls catégories
	$cats = prd_categories_get(0,false,-1);
	if( $cats ){
		while( $c = ria_mysql_fetch_array( $cats ) ){
		
			// supprime l'url alias s'il existe
			if( $c['url_alias'] ){
				
				rew_rewritemap_del( $c['url_alias'] );
				rew_rewritemap_del( '', $c['url_alias'] );
								
			}
			
			// recréer l'url
			$new_url = prd_categories_url_alias_add( $c['id'] );

			if( isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']) > 1 ){
				foreach( $config['i18n_lng_used'] as $one_lng ){
					if( $one_lng == $config['i18n_lng'] ){
						continue;
					}

					rew_rewritemap_add_multilingue( array($c['id']), CLS_CATEGORY, $one_lng, $new_url, _FLD_CAT_URL );
				}
			}
			
			print $new_url."\n";
		}
	}


<?php

set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'products.inc.php' );

	/*
		Référence vendeur	EAN (Facultatif pour Mode et Maison)	Marque	Nature du produit	Code catégorie	Libellé court panier	Libellé long produit	Description produit	Image 1 (jpeg)	
		
		Sku famille	Taille (borné)	Couleur marketing	

		Description marketing	Image 2 (jpeg)	Image 3 (jpeg)	Image 4 (jpeg)	Navigation / classification / rayon	ISBN	MFPN	Longueur (cm)	Largeur (cm)	Hauteur (cm)	Poids (kg)	

		Avertissement(s)	Commentaire	Couleur(s)	Couleur principale	Genre	Licence	Sports	Type de public
	*/

	$ar_ref = array(
		'1010PSE',	'3030TRE',	'6040TRE',	'3060PSE',	'4040PSE',	'6060TRE',
	);
	$r_catalog = ctr_catalogs_get( CTR_CDISCOUNT, 0, 0, true );

	$data = array();
	while( $catalog = mysql_fetch_assoc($r_catalog) ){
		$r_product = prd_products_get_simple( $catalog['prd_id'] );
		if( !$r_product || !mysql_num_rows($r_product) ){
			continue;
		}

		$product = mysql_fetch_assoc( $r_product );

		if( sizeof($ar_ref) && !in_array($product['ref'], $ar_ref) ){ continue; }
		if( trim($product['barcode']) == '' ){ continue; }
		if( !is_numeric($product['img_id']) || $product['img_id'] <= 0 ){ continue; }

		$ctr_cat = ctr_catalogs_get_categorie( CTR_CDISCOUNT, $product['id'], false );
		if( !is_numeric($ctr_cat) || $ctr_cat <= 0 ){ continue; }

		$ref_ctr_cat = ctr_categories_get_ref( CTR_CDISCOUNT, $ctr_cat );
		if( trim($ref_ctr_cat) == '' ){ continue; }

		$product['title'] = ctr_catalogs_get_prd_title( CTR_CDISCOUNT, $product['id'], false );
		$product['desc']  = str_replace(array('"', "\n", "\r\n", "\r"), array('', ' ', ' ', ' '), ctr_catalogs_get_prd_desc(  CTR_CDISCOUNT, $product['id'], false ) );
		$product['weight_net'] = !is_numeric($product['weight_net']) || $product['weight_net'] <= 0 ? $product['weight'] : $product['weight_net'];

		if( trim($product['desc']) == '' ){ $product['desc'] = $product['title']; }

		$size = $config['img_sizes']['shopzilla'];
		$url_img = $config['img_url'].'/'.$size['width'].'x'.$size['height'];

		$ar_imgs = array();

		$r_img = prd_images_get( $product['id'] );
		if( $r_img && mysql_num_rows($r_img) ){
			while( $img = mysql_fetch_assoc($r_img) ){
				$ar_imgs[] = $img['id'];
			}
		}

		$data[ $product['id'] ] = array(
			$product['ref'], $product['barcode'], $product['brd_title'], 'Standard', $ref_ctr_cat, $product['title'], $product['title'], $product['desc'], $url_img.'/'.$product['img_id'].'.'.$size['format'],

			'', '', '', 

			$product['desc']
		);

		for( $i=0 ; $i<3 ; $i++ ){
			$t_img = isset($ar_imgs[$i]) ? $url_img.'/'.$ar_imgs[$i].'.'.$size['format'] : '';
			$data[ $product['id'] ][] = $t_img;
		}

		$data[ $product['id'] ] = array_merge( $data[ $product['id'] ], array(
			'', '', '', $product['length'], $product['width'], $product['height'], round( ($product['weight_net'] / 1000), 2)
		));

		print utf8_decode( '"'.implode( '";"', $data[ $product['id'] ] ).'";"END"' )."\n";
	}

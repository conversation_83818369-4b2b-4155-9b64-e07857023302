<?php

set_include_path(dirname(__FILE__) . '/../include/');
require_once(str_replace('/tools', '', getenv('PWD')) . '/htdocs/config.inc.php');
require_once('orders.inc.php');

$keys = array();
$options = array();

if (isset($argv[1]) && is_numeric($argv[1]) && $argv[1]) {
    $keys['id'] = intval($argv[1]);
}
if (isset($argv[2]) && isdate($argv[2])) {
    $options['start'] = $argv[2];
}
if (isset($argv[3]) && isdate($argv[3])) {
    $options['end'] = $argv[3];
}

$r_order = ord_orders_get_simple( $keys, $options, array(), array() );
if (!$r_order || !ria_mysql_num_rows($r_order)) {
    exit;
}

$i = 1;
$nb = ria_mysql_num_rows($r_order);

while ($order = ria_mysql_fetch_assoc($r_order)) {
    print ($i++).' / '.$nb."\n";
    ord_orders_update_totals($order['id'], false, false, true);
}
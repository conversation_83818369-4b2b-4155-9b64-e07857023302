<?php
	/**
	*	Ce script permet de mettre � jour toutes les positions des images Cms en fonction de leur img_id.
	*/
	
	set_include_path(dirname(__FILE__) . '/../include/');

	require_once( 'db.inc.php' );
	
	$rline = ria_mysql_query('
		select ci_tnt_id, ci_cat_id
		from cms_img
		group by ci_tnt_id, ci_cat_id
	');
	if( $rline && ria_mysql_num_rows($rline) ){
		while( $line = ria_mysql_fetch_array( $rline ) ){
			$rimg = ria_mysql_query('select ci_tnt_id, ci_img_id, ci_cat_id from cms_img where ci_tnt_id = '.$line['ci_tnt_id'].' and ci_cat_id='.$line['ci_cat_id'].' order by ci_img_id asc');
			
			print 'Image '.$line['ci_tnt_id'].' : '.$line['ci_cat_id']."\n";
			if( $rimg ){
				$pos = 1;
				while( $img = ria_mysql_fetch_array( $rimg ) ){
					ria_mysql_query('update cms_img set ci_pos = '.$pos.' where ci_tnt_id = '.$img['ci_tnt_id'].' and ci_img_id = '.$img['ci_img_id'].' and ci_cat_id = '.$img['ci_cat_id'].'');
					$pos++;
				}
			}
		}
	}
	

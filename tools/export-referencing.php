<?php
	// ce script permet l'exportation des données de référencement auto et personnalisé
	
	set_include_path(dirname(__FILE__) . '/../include/');

    require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	
	// paramètre d'export 
	$publish = true;
	$include_childs = false;
	
	$fp = fopen('export-referencing-'.$config['tnt_id'].'.csv', 'w');
	
	function push_to_csv($line){
		global $fp;
		
		$tmp = implode('";"',$line); 
		$tmp = str_replace("\n","",$tmp);
		$tmp = str_replace("\r","",$tmp);
		fwrite($fp, '"'.$tmp.'";'."\n");
		
	}
	
	push_to_csv( array('Type', 'Titre obj 1', 'Identifiant obj 1', 'Titre obj 2', 'Identifiant obj 2', 'Titre obj 3', 'Identifiant obj 3','Langue','Meta titre perso', 'Meta titre auto','Meta desc perso','Meta desc auto', 'Meta key perso','Meta key auto') );
	
	
	// pour chaque langue
	$cpt=0;
	$rlng = wst_websites_languages_get($config['wst_id']);
	while( $lng = ria_mysql_fetch_array($rlng) ){
		i18n::setLang( $lng['lng_code'] );
		
		print i18n::getLang()."\n";
						
		// traite les actualités
		$rnews = news_get(0,$publish);
		if( $rnews ){
			while( $news = ria_mysql_fetch_array( $rnews ) ){

				$title_perso = page_obj_title( CLS_NEWS, array($news['id']), true ); 
				$title_auto = page_obj_title( CLS_NEWS, array($news['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_NEWS, array($news['id']), true ); 
				$desc_auto = page_obj_desc( CLS_NEWS, array($news['id']), false ); 
				
				$key_perso = page_obj_key( CLS_NEWS, array($news['id']), true ); 
				$key_auto = page_obj_key( CLS_NEWS, array($news['id']), false ); 
				
				push_to_csv( array('ACTUALITE', $news['name'], $news['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
		// traite les cms
		$rcms = cms_categories_get(0,false,$publish);
		if( $rcms ){
			while( $cms = ria_mysql_fetch_array( $rcms ) ){

				$title_perso = page_obj_title( CLS_CMS, array($cms['id']), true ); 
				$title_auto = page_obj_title( CLS_CMS, array($cms['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_CMS, array($cms['id']), true ); 
				$desc_auto = page_obj_desc( CLS_CMS, array($cms['id']), false ); 
				
				$key_perso = page_obj_key( CLS_CMS, array($cms['id']), true ); 
				$key_auto = page_obj_key( CLS_CMS, array($cms['id']), false ); 
				
				push_to_csv( array('CMS', $cms['name'], $cms['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
		// traite les marques
		$rbrd = prd_brands_get(0,$publish);
		if( $rbrd ){
			while( $brd = ria_mysql_fetch_array( $rbrd ) ){

				$title_perso = page_obj_title( CLS_BRAND, array($brd['id']), true ); 
				$title_auto = page_obj_title( CLS_BRAND, array($brd['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_BRAND, array($brd['id']), true ); 
				$desc_auto = page_obj_desc( CLS_BRAND, array($brd['id']), false ); 
				
				$key_perso = page_obj_key( CLS_BRAND, array($brd['id']), true ); 
				$key_auto = page_obj_key( CLS_BRAND, array($brd['id']), false ); 
				
				push_to_csv( array('MARQUE', $brd['name'], $brd['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
		// traite les question
		$rqst = faq_questions_get(0,0,$publish);
		if( $rqst ){
			while( $qst = ria_mysql_fetch_array( $rqst ) ){

				$title_perso = page_obj_title( CLS_FAQ_QST, array($qst['id']), true ); 
				$title_auto = page_obj_title( CLS_FAQ_QST, array($qst['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_FAQ_QST, array($qst['id']), true ); 
				$desc_auto = page_obj_desc( CLS_FAQ_QST, array($qst['id']), false ); 
				
				$key_perso = page_obj_key( CLS_FAQ_QST, array($qst['id']), true ); 
				$key_auto = page_obj_key( CLS_FAQ_QST, array($qst['id']), false ); 
				
				push_to_csv( array('QUESTION', $qst['name'], $qst['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
		// traite les stores
		$publish_str = $publish ? true : null;
		$rstr = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, $publish_str );
		if( $rstr ){
			while( $str = ria_mysql_fetch_array( $rstr ) ){

				$title_perso = page_obj_title( CLS_STORE, array($str['id']), true ); 
				$title_auto = page_obj_title( CLS_STORE, array($str['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_STORE, array($str['id']), true ); 
				$desc_auto = page_obj_desc( CLS_STORE, array($str['id']), false ); 
				
				$key_perso = page_obj_key( CLS_STORE, array($str['id']), true ); 
				$key_auto = page_obj_key( CLS_STORE, array($str['id']), false ); 
				
				push_to_csv( array('MAGASINS', $str['name'], $str['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
					
		// traite les categorie
		$rcat = prd_categories_get_all( $publish );
		if( $rcat ){
			while( $cat = ria_mysql_fetch_array( $rcat ) ){

				$title_perso = page_obj_title( CLS_CATEGORY, array($cat['id']), true ); 
				$title_auto = page_obj_title( CLS_CATEGORY, array($cat['id']), false ); 
				
				$desc_perso = page_obj_desc( CLS_CATEGORY, array($cat['id']), true ); 
				$desc_auto = page_obj_desc( CLS_CATEGORY, array($cat['id']), false ); 
				
				$key_perso = page_obj_key( CLS_CATEGORY, array($cat['id']), true ); 
				$key_auto = page_obj_key( CLS_CATEGORY, array($cat['id']), false ); 
				
				push_to_csv( array('CATEGORIE', $cat['title'], $cat['id'], '', '', '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
		// récupère chaque classification
		$rcly = prd_classify_get();
		if( $rcly ){
			while( $cly = ria_mysql_fetch_array( $rcly ) ){
				$prd = prd_products_get_simple( $cly['prd'], '', $publish );
				if( !$prd || !ria_mysql_num_rows($prd) ) continue;
				$prd = ria_mysql_fetch_array( $prd );
				
				if( !$include_childs ){
					$parents = prd_parents_get( $cly['prd'], false, $publish);
					if( $parents && ria_mysql_num_rows($parents) ) continue; 
				}
				
				$cat = prd_categories_get( $cly['cat'],$publish );
				if( !$cat || !ria_mysql_num_rows($cat) ) continue;
				$cat = ria_mysql_fetch_array( $cat );
				
				$title_perso = page_obj_title( CLS_PRODUCT, array($cly['prd'], $cly['cat']), true ); 
				$title_auto = page_obj_title( CLS_PRODUCT, array($cly['prd'], $cly['cat']), false ); 
				
				$desc_perso = page_obj_desc( CLS_PRODUCT, array($cly['prd'], $cly['cat']), true ); 
				$desc_auto = page_obj_desc( CLS_PRODUCT, array($cly['prd'], $cly['cat']), false ); 
				
				$key_perso = page_obj_key( CLS_PRODUCT, array($cly['prd'], $cly['cat']), true ); 
				$key_auto = page_obj_key( CLS_PRODUCT, array($cly['prd'], $cly['cat']), false ); 
				
				push_to_csv( array('PRODUIT', $prd['title'], $cly['prd'], $cat['title'], $cly['cat'], '', '', i18n::getLang(), $title_perso, $title_auto, $desc_perso, $desc_auto, $key_perso, $key_auto) );
			}
		}
		
	}
	
fclose($fp);
<?php

	/** \file rebuild-ctr-cat-hierarchy.php
	 *	\ingroup crontabs ctr_comparators
	 * 	Ce script est destiné à recontruire la hiérarchie des catégories pour un comparateur dont l'identifiant est passé en paramêtre
	 *
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');
	require_once('comparators.inc.php');

	if( !isset($argv[1]) || !ctr_comparators_exists($argv[1]) ){
		print 'Veuillez préciser l\'identifiant du comparateur pour lequel vous souhaitez reconstruire la hiérarchie des catégories.'."\n";
		exit;
	}
	
	// Reconstruction de la hiérarchie des catégories
	ctr_categories_hierarchy_rebuild( $argv[1] );


<?php

set_include_path( '/var/www/riashop.riastudio.fr/include/' );
require_once('comparators.inc.php');

$handle = fopen('/var/www/riashop.riastudio.fr/tools/temp.csv', 'r');


// initialisation du tableau de catégorie rencontré pour ne pas avoir a les rechercher constament
$ctr_cats = array();
$ctr_cats_fields = array();
$ctr_cats_fields_used = array();

$ctr_cats_names = array();

// numéro de la ligne sur l'excel ou csv
$line_count = 0;

while($line = fgetcsv($handle, 0, ';') ){
	$line_count++;
	if( $line_count < 2 ){
		continue;
	}

	echo $line_count.PHP_EOL;
	// maping des colonnes au valeur
	$categories = array(
		trim($line[0]),
		trim($line[1]),
		trim($line[2]),
		trim($line[3])
	);
	$code = $line[4];
	$label = $line[5];
	$unite = $line[6];
	$mandatory = $line[7];
	$type = strtolower($line[9]);
	$value = $line[10];
	$modify = $line[11];
	$cat_ref = $line[12];

	if(trim($cat_ref) == ''){
		$tmp_ref = $cat_ref.$line_count;
	}else{
		$tmp_ref = $cat_ref;
	}
	// on check si on a déja rencontrer la catégorie
	if( !isset($ctr_cats[$tmp_ref]) ){
		// si on ne la pas rencontrer on cherche son identifiant
		$cat_id = ctr_categories_get_id_byref( CTR_RUEDUCOMMERCE_MIRAKL, $cat_ref );
		if( !$cat_id ){

			$cat_id = 0;
			for($i=0; $i< sizeof($categories); $i++) {

				if( trim($categories[$i]) == ''){
					break;
				}

				$is_last = (!isset($categories[$i+1]) || trim($categories[$i+1]) == '');
				
				if( isset($ctr_cats_names[$categories[$i]]) ){
					$cat_id = $ctr_cats_names[$categories[$i]];
					if( $is_last ){
						if(trim($cat_ref) != ''){
							ctr_categories_update_ref( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id, $cat_ref );
						}
						break;
					}
				}else{
					$rCat = ctr_categories_get( CTR_RUEDUCOMMERCE_MIRAKL, 0, '', 0, false, trim($categories[$i]) );
					if( !$rCat || !ria_mysql_num_rows($rCat) ){		
						$ref = '';
						if( $is_last ){
							$ref = $cat_ref;
						}
						$cat_id = ctr_categories_add( CTR_RUEDUCOMMERCE_MIRAKL, trim($categories[$i]), $ref, $cat_id, 0 );
						if( $cat_id == false ){
							echo 'Erreur a l\'ajout d\'une catégorie'.PHP_EOL;
							break;
						}

						if( $is_last ){
							if(trim($cat_ref) == ''){
								$ctr_cats[$cat_ref.$line_count] = $cat_id;
							}else{
								$ctr_cats[$cat_ref] = $cat_id;
							}
							$ctr_cats_names[$categories[$i]] = $cat_id;
							break;
						}else{
							$ctr_cats_names[$categories[$i]] = $cat_id;
							continue;
						}
					}else{
						$cate = ria_mysql_fetch_assoc($rCat);
						$cat_id = $cate['id'];
						if( $is_last ){
							ctr_categories_update_ref( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id, $cat_ref );
							if(trim($cat_ref) == ''){
								$ctr_cats[$cat_ref.$line_count] = $cat_id;
							}else{
								$ctr_cats[$cat_ref] = $cat_id;
							}
							$ctr_cats_names[$categories[$i]] = $cat_id;
							break;
						}else{
							$ctr_cats_names[$categories[$i]] = $cat_id;
							continue;
						}
					}
				}
			}
		}
		if( !$cat_id ){
			echo 'une erreur est survenu lors de l\'ajout d\'une catégorie'.PHP_EOL;
			continue;
		}
		if(trim($cat_ref) == ''){
			$ctr_cats[$cat_ref.$line_count] = $cat_id;
		}else{
			$ctr_cats[$cat_ref] = $cat_id;
		}
		$rFields = ctr_cat_fields_get( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id );
		if( $rFields ){
			while( $field = ria_mysql_fetch_assoc($rFields) ){
				$ctr_cats_fields[$cat_id][$field['code']] = $field['id'];
			}
		}
		
	}else{
		// Si on l'a déja rencontrer on prend son identifiant dans le tableau
		if(trim($cat_ref) == ''){
			$cat_id = $ctr_cats[$cat_ref.$line_count];
		}else{
			$cat_id = $ctr_cats[$cat_ref];
		}
	}
	
	// on convertie la valeur du mandatory en booléan
	switch( $mandatory ){
		case 'O' :
			$mandatory = true;
			break;
		case 'N' :
			$mandatory = false;
			break;
		default :
			$mandatory = false;
			break;
	}

	// On convertie la valeur du type en fld_type
	switch( $type ){
		case 'liste de valeurs':
			$fld_type = 5;
			break;
		case 'décimal':
			$fld_type = 4;
			break;
		case 'entier':
			$fld_type = 3;
			break;
		case 'text':
		default :
			$fld_type = 1;
			break;
	}

	if( isset($ctr_cats_fields[$cat_id][$code]) ){
		// ajout de l'attribut dans la liste des attribut utiliser par la catégorie
		$ccf_id = $ctr_cats_fields_used[$cat_id][$code] = $ctr_cats_fields[$cat_id][$code];

		if( !ctr_cat_fields_update( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id, $code, $label, $desc='', $mandatory, $fld_type, $unite ) ){
			echo 'Erreur lors de la mise à jours de l\'attribut ligne '.$line_count.PHP_EOL;
			error_log('Erreur lors de la mise à jours de l\'attribut ligne '.$line_count);
			continue;
		}


		$old_values = ctr_cat_field_values_get_array( $ccf_id );

		$values = array(trim($value));

		if( $fld_type === 5 ){
			$values = array_map('trim',explode(';',$value));
		}

		if( !empty($old_values) ){
			$unset = array();
			foreach( $old_values as $id => $old ){
				if( !in_array($old ,$values) ){
					if( !ctr_cat_field_value_del( $id ) ){
						echo 'Erreur lors de la suppression d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count.PHP_EOL;
						error_log('Erreur lors de la suppression d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count);
					}
				}else{
					$unset[] = $old;
				}
			}

			$values = array_diff($values, $unset);
		}
		
		foreach( $values as $value ){

			if( trim($value) == '' ){
				continue;
			}
			if( !ctr_cat_field_values_add( $ccf_id, $value ) ){
				echo 'error upd'.PHP_EOL;
				echo 'Erreur lors de l\'ajout d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count.PHP_EOL;
				error_log('Erreur lors de l\'ajout d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count);
			}
		}				
	
	}else{
		// on ajoute l'attribut
		$ccf_id = ctr_cat_fields_add( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id, $code, $label, $desc='', $mandatory, $fld_type, $unite );

		if( !$ccf_id ){
			echo 'Erreur lors de l\'ajout de l\'attribut ligne '.$line_count.PHP_EOL;
			error_log('Erreur lors de l\'ajout de l\'attribut ligne '.$line_count);
			continue;
		}

		// si le type est liste à sélection unique
		// on explode la valeur pour avoir untableau puis on le parcour pour ajouter chaque valeur à l'attribut
		$values = array(trim($value));

		if( $fld_type === 5 ){
			$values = array_map('trim',explode(';',$value));
		}

		foreach( $values as $val ){
			if( trim($val) == '' ){
				continue;
			}
			if( !ctr_cat_field_values_add( $ccf_id, $val ) ){
				echo 'error add'.PHP_EOL;
				echo 'Erreur lors de l\'ajout d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count.PHP_EOL;
				error_log('Erreur lors de l\'ajout d\'une valeur pour l\'attribut '.$code.' ligne '.$line_count);
			}
		}
	}
}
// Suppression des attributs  qui ne sont plus utilisé pour chaque catégorie
foreach( $ctr_cats as $ref => $cat_id ){

	if( !isset($ctr_cats_fields[$cat_id]) ){
		continue;
	}

	if( !isset($ctr_cats_fields_used[$cat_id]) ){
		continue;
	}

	$fld = $ctr_cats_fields[$cat_id];
	$used = $ctr_cats_fields_used[$cat_id];

	$del = array();

	foreach( $fld as $code => $id ){
	    if( !in_array($id,$used) ){
	        if( !ctr_cat_fields_delete( CTR_RUEDUCOMMERCE_MIRAKL, $id ) ){
	        	echo 'Une erreur est survenue lors de la suppression des attribut non utiliser de la.PHP_EOL catégorie '.$ref.PHP_EOL;
	        	error_log('Une erreur est survenue lors de la suppression des attribut non utiliser de la catégorie '.$ref);
	        }
	    }
	}
}
if( !ctr_categories_hierarchy_rebuild(CTR_RUEDUCOMMERCE_MIRAKL) ){
	error_log('Une erreur est survenue lors du rebuild.');
}

fclose($handle);

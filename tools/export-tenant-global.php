<?php

	/** \file export-tenant-global.php
	 * 	Ce script exporte l'ensemble des données global ou de tenant 0, au format SQL. Il s'utilise comme suit :
	 *	\code{.sh}
	 *	php export-tenant-global.php [base de donnée] > export.sql
	 *	\endcode
	 *	où :
	 *	- base de données est le nom de la base de données source (par défaut : riashop)
	 */

	// Nom de la base de données d'où les données doivent être exportée
	$database = isset($argv[1]) ? $argv[1] : 'riashop';
	
	// Dossier d'include
	set_include_path(dirname(__FILE__) . '/../include/');

	// Connexion à la base de données
	require_once( 'db.inc.php' );


	// récupère l'ensemble des tables 
	$tables = ria_mysql_query('
		select table_name as name
		from information_schema.tables
		where table_schema = \''.addslashes($database).'\'
	');

	print "SET FOREIGN_KEY_CHECKS = 0;\n";

	$excluded_tables = array('search_keywords', 'search_terms', 'test_charles');

	// pour chaque tables
	while( $table = ria_mysql_fetch_assoc($tables) ) {
		if( in_array($table['name'], $excluded_tables) ) continue;

		// Récupère les colonnes de ces tables
		$rcol = mysql_query('desc '.addslashes($database).'.'.addslashes($table['name']));
		if( !$rcol ){
			print mysql_error();
			exit;
		}
		
		$tnt_field = false;
		$first = true; $cols = ''; $tcols = array();
		while( $col = ria_mysql_fetch_assoc($rcol) ){

			// Liste des colonnes
			$cols .= $first ? $col['Field'] : ', '.$col['Field'];
			$tcols[ $col['Field'] ] = array( 'typ'=>$col['Type'], 'cannull'=>$col['Null'], 'default'=>$col['Default'] );
			
			// Colonne fesant le lien avec le tenant
			if( strpos($col['Field'], 'tnt_id')!==false ){
				$tnt_field = $col['Field'];
			}
			
			$first = false;
		}

		// Récupère les donnéés si table avec tnt .. ou non 
		$result =  ria_mysql_query( 'select '.$cols.' from '.addslashes($database).'.'.$table['name'].' where '.( $tnt_field ? $tnt_field.'=0' :'1' ));
		
		$last = false; $count = 1;
		if( $result && ria_mysql_num_rows($result) ){
			// Affiche la requê SQL d'insertion
			
			print 'insert into '.$table['name'].'( '.$cols.' ) values'."\n";
			while( $res = ria_mysql_fetch_assoc($result) ){
				print '( ';
				$r = 1;
				foreach( $tcols as $col=>$info ){ 
					$last = $r==sizeof($tcols) ? true : false;
					if( trim($res[ $col ])!='' )
						$val = $res[ $col ];
					elseif( $info['cannull']=='YES' )
						$val = 'null';
					else
						$val = $info['default'];
					
					if( $val=='null' )
						print $val;
					else
						print '\''.addslashes(stripslashes($val)).'\'';
					
					if( !$last )
						print ', ';
					
					$r++;
				}
				
				if( $count<ria_mysql_num_rows($result) && $count%1000==0 ){
					print ' );'."\n";
					print 'insert into '.$table['name'].'( '.$cols.' ) values'."\n";
				} elseif( $count!=ria_mysql_num_rows($result) )
					print ' ),'."\n";
				else
					print ' );'."\n";
				$count++;
			}
		}
	}

	print "SET FOREIGN_KEY_CHECKS = 1;\n";

<?php

set_include_path(dirname(__FILE__) . '/../include/');
require_once(str_replace('/tools', '', getenv('PWD')) . '/htdocs/config.inc.php');
require_once('ord.invoices.inc.php');

$inv_id = 0;
$date_start = $date_end = false;

if (isset($argv[1]) && is_numeric($argv[1]) && $argv[1]) {
    $inv_id = intval($argv[1]);
}
if (isset($argv[2]) && isdate($argv[2])) {
    $date_start = $argv[2];
}
if (isset($argv[3]) && isdate($argv[3])) {
    $date_end = $argv[3];
}

$r_invoice = ord_invoices_get( $inv_id, 0, 0, false, false, false, false, 0, $date_start, $date_end );
if (!$r_invoice || !ria_mysql_num_rows($r_invoice)) {
    exit;
}

$i = 1;
$nb = ria_mysql_num_rows($r_invoice);

while ($invoice = ria_mysql_fetch_assoc($r_invoice)) {
    print ($i++).' / '.$nb."\n";
    ord_invoices_update_totals($invoice['id']);
}
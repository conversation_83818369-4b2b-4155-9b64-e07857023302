<?php
/// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');

use Google\Cloud\PubSub\PubSubClient;


/** \defgroup model_pubsub PubSub
 *	\ingroup yuto system
 *	Ce module comprend les fonctions nécessaires à la gestion de PubSub
 *	@{
 */

class PubSub {
	private $pubSubClient = null;

	private $project_id = '';

	private $topicClient = null;

	public static function create($topic_id){
		return new PubSub($topic_id);
	}

	/** Constructeur de la classe
	 *  @param array $topic_id : Constante du topic a utiliser pour l'instance
	 */
	public function __construct($topic_id){
		global $config;

		if (!isset($config['env_sandbox']) || $config['env_sandbox']) {
			$this->project_id = "infra-dev-riashop";
		}else{
			$this->project_id = "riashop-186610";
		}


		$this->pubSubClient = new PubSubClient([
		    'projectId' => $this->project_id,
		]);

		$this->topicClient = $this->pubSubClient->topic($topic_id);
		if( !$this->topicClient->exists() ){
			$this->pubSubClient->createTopic($topic_id);
		}
	}

	/** Publication d'un message
	 *  @param array $type : type permet de filter l'action a faire de l'autre côté
	 *  @param array $data : text du message, peut être du json ou un tableau
	 *  @param array $attributes : tableau des données a push
	 */
	public function publish($type, $data="", $attributes=array()){
		if( !$type ){
			throw new Exception("Le type est obligatoire.");
		}
		if( !is_array($attributes) ){
			throw new Exception("Les données attributes doivent être un tableau");
		}

		$this->topicClient->publish([
		    'data' => is_array($data) ? json_encode($data) : $data,
		    'attributes' => array_merge(array('type' => $type ), $attributes)
		]);
	}

}

/// @}

/// \endcond

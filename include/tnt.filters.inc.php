<?php
// \cond onlyria
/**	\defgroup tnt_filters Filtres de trafic
 *	\ingroup model_tenants
 *	Ce module comprend les fonctions nécessaires à la gestion de filtre sur le trafic des sites clients.
 *	Ces fonctions sont utiles pour exclure le trafic en provenance du siège ou du domicile des collaborateurs, afin de ne pas fausser les statistiques.
 *	@{
 */

/** Cette fonction charge un tableau contenant les codes des symbols accessibles pour les filtres de statistique.
 *	@return array Un tableau contenant tous les codes de symboles
 */
function tnt_filters_get_symbols(){
	return array( '=', '%LIKE', 'LIKE', 'LIKE%' );
}

/** Cette fonction permet d'ajouter un filtre.
 *	@param string $name Obligatoire, nouveau nom du filtre
 *	@param string $value Obligatoire, nouvelle valeur
 *	@param string $symbol Optionnel, nouveau symbôle par défaut à "=" (égal)
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function tnt_filters_add( $name, $value, $symbol='=' ){
	if( trim($name)=='' || trim($value)=='' ){
		return false;
	}

	if( !in_array($symbol, tnt_filters_get_symbols()) ){
		return false;
	}

	global $config, $memcached;

	$sql = '
		insert into tnt_filters
			( flt_tnt_id, flt_name, flt_psy_symbol, flt_value )
		values
			( '.$config['tnt_id'].', \''.addslashes( $name ).'\', \''.addslashes( $symbol ).'\', \''.addslashes( $value ).'\' )
	';

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	// Supprime le cache sur l'IP
	$memcached->delete( 'tnt_filters_is_detected:'.$config['tnt_id'].':'.$value );

	return ria_mysql_insert_id();
}

/** Cette fonction permet de vérifier qu'un filtre existe.
 *	@param int $id Obligatoire, identifiant du filtre
 *	@return bool True s'il existe, False dans le cas contraire
 */
function tnt_filters_exists( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from tnt_filters
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_id='.$id.'
			and flt_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		if( ria_mysql_errno() ){
			error_log( $sql.' - '.mysql_error() );
			return false;
		}

		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour un filtre.
 *	@param int $id Obligatoire, identifiant du filtre
 *	@param string $name Obligatoire, nouveau nom du filtre
 *	@param $value Obligatoire, nouveau valeur
 *	@param $symbol Optionnel, nouveau symbol par défaut à "=" (égal)
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function tnt_filters_update( $id, $name, $value, $symbol='=' ){
	if( !tnt_filters_exists($id) ){
		return false;
	}

	if( trim($name)=='' || trim($value)=='' ){
		return false;
	}

	if( !in_array($symbol, tnt_filters_get_symbols()) ){
		return false;
	}

	global $config, $memcached;

	$sql = '
		update tnt_filters
		set flt_name=\''.addslashes( $name ).'\',
			flt_value=\''.addslashes( $value ).'\',
			flt_psy_symbol=\''.addslashes( $symbol ).'\'
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_id='.$id.'
	';

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	// Supprime le cache sur l'IP
	$memcached->delete( 'tnt_filters_is_detected:'.$config['tnt_id'].':'.$value );

	return $r;
}

/** Cette fonction permet de récupérer un ou plusieurs filtres.
 *	@param int $id Optionnel, identifiant d'un filtre (si spécifié tous les autres paramètres sont ignorés)
 *	@return resource Un résultat MySQL contenant :
 *				- id 	 : identifiant du filtre
 *				- name 	 : nom du filtre
 *				- symbol : symbole appliqué au filtre
 *				- value  : valeur du filtre
 *	@return bool False si l'un des paramètres fournis est faux
 */
function tnt_filters_get( $id=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select flt_id as id, flt_name as name, flt_psy_symbol as symbol, flt_value as value
		from tnt_filters
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and flt_id='.$id;
	}

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	return $r;
}

/** Cette fonction permet de récupérer la désignation d'un filtre.
 *	@param int $id Obligatoire, identifiant d'un filtre
 *	@return string Le nom du filtre, False si le filtre n'existe pas
 */
function tnt_filters_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select flt_name as name
		from tnt_filters
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_id='.$id.'
			and flt_date_deleted is null
	';

	$res = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction permet de supprimer un ou plusieurs filtres.
 *	@param int $id Obligatoire, identifiant ou tableau d'identifiants de filtres
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function tnt_filters_del( $id ){
	if( !is_array($id) ){
		if( !is_numeric($id) || $id<=0 ){
			return false;
		}

		$id = array( $id );
	} else {
		if( !sizeof($id) ){
			return false;
		}

		foreach( $id as $i ){
			if( !is_numeric($i) || $i<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		update tnt_filters
		set flt_date_deleted=now()
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_id in ('.implode( ', ', $id ).')
	';

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	return $r;
}

/** Cette fonction permet de contrôler qu'aucun filtre n'est actionné.
 *	@return bool True si un filtre est actionné, False dans le cas contraire
 */
function tnt_filters_is_detected(){
	$ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
	if( trim($ip)=='' ){
		return false;
	}

	global $config, $memcached;

	if( $kid = $memcached->get( 'tnt_filters_is_detected:'.$config['tnt_id'].':'.$ip ) ){
		return $kid=='no-detect' ? false : true;
	}

	$sql = '
		select 1
		from tnt_filters
		where (flt_tnt_id=0 or flt_tnt_id='.$config['tnt_id'].')
			and flt_value=\''.addslashes($ip).'\'
			and flt_date_deleted is null
	';

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		$r = false;
	}

	if( !$r || !ria_mysql_num_rows($r) ){
		$r = false;
	}

	$memcached->set( 'tnt_filters_is_detected:'.$config['tnt_id'].':'.$ip, ( $r && ria_mysql_num_rows($r) ? 'detect' : 'no-detect' ), 86400 );

	return ( $r && ria_mysql_num_rows($r) ? true : false );
}

/// @}
// \endcond

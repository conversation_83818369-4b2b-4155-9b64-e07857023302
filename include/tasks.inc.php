<?php
// \cond onlyria

require_once('db.inc.php');
require_once('tenants.inc.php');
require_once('strings.inc.php');

/**	\defgroup synchro Synchronisation
 *	Ce module contient les fonctions nécessaires à la gestion des tâches de synchronisation
 *	@{
 */

/** Cette fonction ajoute une tâche de synchronisation
 *	@param int $id Obligatoire, Identifiant de la tâche. Si une tâche existe déjà avec cet identifiant, la fonction n'effectuera aucune action et retournera True. S'il est négatif, elle retournera False.
 *	@param $parent Obligatoire, Identifiant du groupe (tâche) parent. La hiérarchie n'a que 4 niveaux (root, types, sous-types, tâches). Le niveau spécifié ici ne peut être que "sous-type"
 *	@param string $name Obligatoire, Nom de la tâche. Retournera False en cas de chaîne vide. Le même nom pour plusieurs tâches est toléré.
 *	@param string $desc Facultatif, Description détaillée de la tâche
 *	@return bool Booléen indiquant le succès ou l'échec de l'opération
 */
function tsk_tasks_add( $id, $parent, $name, $desc=null ){
	if( !is_numeric($id) || $id<=0 || trim($name)=='' ) return false;
	if( !tsk_tasks_exists($parent) || $parent>=0 ) return false;

	if( tsk_tasks_exists($id) ){
		$res = ria_mysql_query( 'update tsk_tasks set tsk_parent_id='.$parent.', tsk_name="'.addslashes(trim($name)).'" where tsk_id='.$id );
	}else{
		$res = ria_mysql_query( 'insert into tsk_tasks (tsk_id, tsk_parent_id, tsk_name) values ('.$id.','.$parent.',"'.addslashes(trim($name)).'")' );
	}

	if( $res )
		$res = tsk_tasks_set_desc( $id, $desc );

	return $res;
}

/** Cette fonction détermine l'existence d'une tâche de synchronisation (tout niveau)
 *	@param int $id Identifiant de la tâche. S'il n'est pas numérique, la fonction retournera False.
 *	@return bool True si la tâche existe, False sinon.
 */
function tsk_tasks_exists( $id ){
	if( !is_numeric($id) )
		return false;

	$res = ria_mysql_query( 'select tsk_id from tsk_tasks where tsk_id='.$id );
	if( !$res )
		return false;

	return ria_mysql_num_rows( $res );
}

/**	Cette fonction met à jour la description détaillé d'une tâche
 *	@param int $id Identifiant de la tâche
 *	@param string $desc Description de la tâche. Mettre NULL pour retirer la valeur actuelle
 *	@return bool true en cas de succès, False en cas d'échec
 */
function tsk_tasks_set_desc( $id, $desc ){
	if( !tsk_tasks_exists( $id ) )
		return false;

	return ria_mysql_query( 'update tsk_tasks set tsk_desc='.( $desc===null ? 'NULL' : '\''.addslashes(trim($desc)).'\'' ).' where tsk_id='.$id );
}

/** Cette fonction retourne des informations sur ou ou plusieurs tâches en fonction de paramètres optionnels
 *	@param int $id Facultatif, Identifiant d'une tâche sur lequel filtrer le résultat
 *	@param string $name Facultatif, Nom ou partie du nom sur lequel filtrer le résultat
 *	@param int $parent Facultatif, Identifiant d'une tâche parent sur lequel filtrer le résultat
 *	@param bool $name_is_part Facultatif, Détermine si $name est tout ou partie du nom d'une tâche. Est ignoré si $name est une chaîne vide.
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant de la tâche
 *		- name : nom de la tâche
 *		- parent : identifiant du parent de la tâche
 *		- desc : descrption détaillé de la tâche
 */
function tsk_tasks_get( $id=null, $name='', $parent=null, $name_is_part=false ){
	if( $id!==null && !tsk_tasks_exists( $id ) ) return false;
	if( $parent!==null && !tsk_tasks_exists( $parent ) ) return false;

	$name = trim($name);

	$sql = '
		select
			tsk_id as id, tsk_name as name, tsk_parent_id as parent, tsk_desc as "desc"
		from
			tsk_tasks
		where 1
	';

	if( $id!==null )
		$sql .= ' and tsk_id='.$id;
	if( $name!='' ){
		if( $name_is_part )
			$sql .= ' and tsk_name like \'%'.addslashes( $name ).'%\'';
		else
			$sql .= ' and tsk_name=\''.addslashes( $name ).'\'';
	}
	if( $parent!==null )
		$sql .= ' and tsk_parent_id='.$parent;

	$sql .= ' order by abs(tsk_id) asc';

	return ria_mysql_query( $sql );
}

/** Cette fonction liste les tâches enfants (premier niveau) d'une tâche spécifiée
 *	@param int $tsk Identifiant de la tâche parent
 *	@return bool False en cas d'échec
 *	@return array Un tableau simple contenant les identifiants des tâches enfants
 */
function tsk_tasks_get_childs( $tsk ){
	if( !tsk_tasks_exists( $tsk ) ) return false;

	$tasks = ria_mysql_query( 'select tsk_id as id from tsk_tasks where tsk_parent_id='.$tsk );

	if( !$tasks ) return false;

	$results = array();

	while( $task = ria_mysql_fetch_array($tasks) )
		$results[] = $task['id'];

	return $results;
}

/** Cette fonction détermine, pour le locataire courant, l'existence de données synchronisées pour une tâche donnée à un instant donné
 *	@param int $tsk Obligatoire, identifiant de la tâche
 *	@param string $date Facultatif, date (et heure éventuellement) de la synchronisation. NULL permet d'accéder au timestamp courant
 *	@return bool True si des données existent, False sinon
 */
function tsk_tasks_exists_exact( $tsk, $date=null ){

	if( !tsk_tasks_exists($tsk) ) return false;
	if( $date!==null && ( !isdate($date) || !isdateheure($date) ) ) return false;

	$date = $date!==null ? ( isdateheure( $date ) ? dateheureparse( $date ) : dateparse( $date ) ) : null;

	global $config;

	$sql = '
		select 1
		from tsk_tasks_tenants
		where tkt_tnt_id='.$config['tnt_id'].' and tkt_tsk_id='.$tsk.'
		and tkt_date=STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date===null ? 'TIME(NOW())' : 'TIME(\''.$date.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )
	';

	$result = ria_mysql_query( $sql );
	if( !$result ) return false;

	return ria_mysql_num_rows( $result );
}

/** Cette fonction crée un log de synchronisation entre une tâche et une boutique
 *	La création de log n'est possible uniquement que sur les tâches de dernier niveau
 *	Si un log existe déjà pour l'heure actuelle, les valeurs count et time seront cumulées
 *	@param int $tsk Obligatoire, identifiant de la tâche
 *	@param string $date Facultatif, date du log. Si null, prend le timestamp actuel
 *	@param int $count Facultatif, nombre de synchronisations réalisées
 *	@param int $time Facultatif, nombre de secondes de synchronisation
 *
 *	@return bool True en cas de succès, False sinon
 */
function tsk_tasks_tenants_add( $tsk, $date=null, $count=0, $time=0 ){
	global $config;

	if( !tsk_tasks_exists( $tsk ) ) return false;
	if( $date!==null && ( !isdate($date) || !isdateheure($date) ) ) return false;
	if( !is_numeric( $count ) || $count<0 ) return false;
	if( !is_numeric( $time ) || $time<0 ) return false;

	if( $date!==null ){
		if( isdateheure($date) )
			$date = dateheureparse($date);
		else
			$date = dateparse($date);
	}

	$sql = '';

	if( tsk_tasks_exists_exact( $tsk, $date ) ){
		$sql = '
			update tsk_tasks_tenants
			set
				tkt_sync_count=tkt_sync_count+'.$count.',
				tkt_sync_time=tkt_sync_time+'.$time.'
			where
				tkt_date=STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date===null ? 'TIME(NOW())' : 'TIME(\''.$date.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )
				and tkt_tsk_id='.$tsk.'
				and tkt_tnt_id='.$config['tnt_id'].'
		';
	}else{
		$sql = '
			insert into tsk_tasks_tenants (
				tkt_tsk_id,
				tkt_tnt_id,
				tkt_date,
				tkt_sync_count,
				tkt_sync_time
			) values (
				'.$tsk.',
				'.$config['tnt_id'].',
				STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date===null ? 'TIME(NOW())' : 'TIME(\''.$date.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' ),
				'.$count.',
				'.$time.'
			)
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction teste l'existence d'un ou plusieurs logs
 *	@param int $tsk Obligaroire, identifiant de la tâche
 *	@param string $date_start Facultatif, date de début de la période à tester. Mettre False pour ne pas tenir compte, NULL pour le timestamp courant
 *	@param string $date_end Facultatif, date de fin de la période à tester. Mettre False pour ne pas tenir compte, NULL pour le timestamp courant
 *
 *	@return bool True si un ou plusieurs log(s) existe(nt), False sinon
 */
function tsk_tasks_tenants_exists( $tsk, $date_start=false, $date_end=false ){
	global $config;

	if( !tsk_tasks_exists( $tsk ) ) return false;
	if( $date_start!==false && $date_start!==null && ( !isdate($date_start) || !isdateheure($date_start) ) ) return false;
	if( $date_end!==false && $date_end!==null && ( !isdate($date_end) || !isdateheure($date_end) ) ) return false;

	if( $date_start!==null && $date_start!==false ){
		if( isdateheure($date_start) )
			$date_start = dateheureparse($date_start);
		else
			$date_start = dateparse($date_start);
	}

	if( $date_end!==null && $date_end!==false ){
		if( isdateheure($date_end) )
			$date_end = dateheureparse($date_end);
		else
			$date_end = dateparse($date_end);
	}

	$sql = 'select tkt_tsk_id, tkt_tnt_id from tsk_tasks_tenants where tkt_tsk_id='.$tsk.' and tkt_tnt_id='.$config['tnt_id'];
	if( $date_start!==false )
		$sql .= ' and tkt_date>=STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date_start===null ? 'TIME(NOW())' : 'TIME(\''.$date_start.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )';
	if( $date_end!==false )
		$sql .= ' and tkt_date<=\STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date_end===null ? 'TIME(NOW())' : 'TIME(\''.$date_end.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )';

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows( $res );
}

/** Cette fonction recherche un ou plusieurs logs selon des paramètres optionnels
 *	@param int $tsk Facultatif, identifiant de la tâche
 *	@param int $tnt Facultatif, identifiant de la boutique
 *	@param string $date_start Facultatif, date de début de la période, mettre false pour ne pas tenir compte, null pour la date actuelle
 *	@param string $date_end Facultatif, date de fin de la période, mettre false pour ne pas tenir compte, null pour la date actuelle
 *	@param int $count_minima Facultatif, nombre minimal de synchronisations du log
 *	@param int $time_minima Facultatif, temps minimal de synchronisations du log
 *	@param array $sort Facultatif, tableau associatif de type "nom du champ" => "ordre de tri" sur lequel trier le résultat. Les noms des champs sont les même que ceux du résultat.
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- tsk : identifiant de la tâche
 *		- tnt : identifiant de la boutique
 *		- date : date du log
 *		- sync-count : nombre de synchronisation pour ce log
 *		- sync-time : temps de synchronisation en secondes pour ce log
 *		- tnt-tempo : temporisation SQL du client
 *		- date-created : date de création du log
 *		- date-modified : date de dernière modification du log
 */
function tsk_tasks_tenants_get( $tsk=null, $tnt=0, $date_start=false, $date_end=false, $count_minima=0, $time_minima=0, $sort=null ){
	if( $tsk!=null && !tsk_tasks_exists( $tsk ) ) return false;
	if( $tnt!=0 && !tsk_tenants_exists( $tnt ) ) return false;
	if( $date_start!=false && $date_start!=null && !isdate($date_start) ) return false;
	if( $date_end!=false && $date_end!=null && !isdate($date_end) ) return false;
	if( !is_numeric($count_minima) || $count_minima<0 ) return false;
	if( !is_numeric($time_minima) || $time_minima<0 ) return false;

	$sql = 'select
				tkt_tsk_id as tsk,
				tkt_tnt_id as tnt,
				tkt_date as date,
				tkt_sync_count as "sync-count",
				tkt_sync_time as "sync-time",
			from
				tsk_tasks_tenants
			where
				tkt_sync_count>='.$count_minima.'
				and tkt_sync_time>='.$time_minima.'
		';

	if( $tsk!=null ){

		$array_tsks = array( $tsk );
		// parcours récursif des enfants
		/*if( !tsk_tasks_get_last_childs( $array_tsks ) )
			return false;*/
		$sql .= ' and tkt_tsk_id in ('.implode( ',',$array_tsks ).')';

	}
	if( $tnt!=0 )
		$sql .= ' and tkt_tnt_id='.$tnt;
	if( $date_start!=false )
		$sql .= ' and tkt_date>=STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date_start==null ? 'TIME(NOW())' : 'TIME(\''.$date_start.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )';
	if( $date_end!=false )
		$sql .= ' and tkt_date<=\STR_TO_DATE( CONCAT( DATE(NOW()), \' \', HOUR('.( $date_end==null ? 'TIME(NOW())' : 'TIME(\''.$date_end.'\')' ).'), \':00:00\' ), \'%Y-%m-%d %H:%i:%s\' )';

	$fields_allow = array( 'tsk','tnt','date','sync-count','sync-time' );

	$first_param_sort = true;
	if( $sort!=null && sizeof( $sort ) ){
		foreach( $sort as $key=>$value ){
			if( trim($value)=='asc' || trim($value)=='desc' ){
				if( in_array( trim($key),$fields_allow ) ){
					if( $first_param_sort==true ){
						$first_param_sort = false;
						$sql .= ' order by '.trim($key).' '.trim($value);
					}else{
						$sql .= ', '.trim($key).' '.trim($value);
					}
				}
			}
		}
	}

	return ria_mysql_query( $sql );
}

/** Récupère des statistiques simples sur l'activité de synchronisation
 *	Les résultats sont triés par nombre de locataires décroissant et par total général décroissant
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- tsk_id : Identifiant de la tâche
 *		- tsk_name : Nom de la tâche
 *		- count-tenant : Nombre de locataires ayant cette tâche active
 *		- total-ID : ID est l'identifiant d'un locataire. Temps de synchronisation moyen pour la tâche pour ce locataire
 *		- total-general : Temps de synchronisation moyen pour la tâche pour tous les locataires
 */
function tsk_tasks_get_stats_standard(){
	global $config;

	$rtenants = tnt_tenants_get();
	if( !$rtenants ) return false;

	$tnt_sql = array();
	while( $tnt = ria_mysql_fetch_array($rtenants) ){
		if( $tnt['id']!=7 && $tnt['id']!=9 )
			$tnt_sql[] = '(( select sum(tkt_sync_time)/sum(tkt_sync_count) from tsk_tasks_tenants where tkt_tsk_id=tsk_id and tkt_tnt_id='.$tnt['id'].' )/1000) as "total-'.$tnt['id'].'"';
	}

	if( sizeof($tnt_sql)==0 ) return false;

	$sql = '
		select tsk_id, tsk_name, (
			select count( distinct tkt_tnt_id ) from tsk_tasks_tenants where tkt_tsk_id=tsk_id
		) as "count-tenant", '.implode( ',',$tnt_sql ).', ((
			select sum(tkt_sync_time)/sum(tkt_sync_count) from tsk_tasks_tenants where tkt_tsk_id=tsk_id
		)/1000) as "total-general"
		from tsk_tasks
		where tsk_id>0
		order by (
			select count( distinct tkt_tnt_id ) from tsk_tasks_tenants where tkt_tsk_id=tsk_id
		) desc, (
			select sum(tkt_sync_time)/sum(tkt_sync_count) from tsk_tasks_tenants where tkt_tsk_id=tsk_id
		) desc
	';

	return ria_mysql_query( $sql );
}

/**	Cette fonction crée ou met à jour une ligne déterminant l'activation d'une tâche de synchronisation pour un locataire
 *	@param int $tsk Identifiant de la tâche. Les groupes et sous-groupes seront refusés ($tsk <= 0)
 *	@param int $tnt Identifiant du locataire
 *	@param bool $active Tâche activée oui / non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tsk_tasks_activation_set( $tsk, $tnt, $active ){

	if( !tsk_tasks_exists($tsk) || $tsk<=0 ) return false;
	if( !tnt_tenants_exists($tnt) ) return false;

	$active = $active ? '1' : '0';

	tsk_tasks_activation_del( $tsk, $tnt );

	return ria_mysql_query('insert into tsk_tasks_activation (tta_tsk_id, tta_tnt_id, tta_is_active) values ('.$tsk.', '.$tnt.', '.$active.')');
}

/**	Cette fonction retourne les relations entre tâches et locataires, selon des paramètres optionnels
 *	@param int $tsk Optionnel, Identifiant de tâche ou tableau d'identifiants de tâches
 *	@param int $tnt Optionnel, Identifiant de locataire ou tableau d'identifiants de locataires
 *	@param bool $active Optionnel, tâches actives seulement oui / non (NULL pour ne pas filter)
 *	@param bool $tsk_group Optionnel, permet de récupérer toutes les tâches d'un sous-groupe
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tsk_id : identifiant de la tâche
 *		- tnt_id : identifiant du locataire
 *		- tsk_name : nom de la tâche
 *		- is_active : activation oui / non
 */
function tsk_tasks_activation_get( $tsk=0, $tnt=0, $active=null, $tsk_group=null ){

	if( is_array($tsk) ){
		foreach( $tsk as $one_tsk ){
			if( !is_numeric($one_tsk) || $one_tsk<=0 ) return false;
		}
	}else{
		if( !is_numeric($tsk) || $tsk<0 ) return false;
		if( $tsk>0 )
			$tsk = array($tsk);
		else
			$tsk = array();
	}

	if( is_array($tnt) ){
		foreach( $tnt as $one_tnt ){
			if( !is_numeric($one_tnt) || $one_tnt<=0 ) return false;
		}
	}else{
		if( !is_numeric($tnt) || $tnt<0 ) return false;
		if( $tnt>0 )
			$tnt = array($tnt);
		else
			$tnt = array();
	}

	$sql = '
		select
			tsk_id, tta_tnt_id as tnt_id, tsk_name, tta_is_active as is_active
		from
			tsk_tasks_activation
			join tsk_tasks on tta_tsk_id=tsk_id
		where 1
	';
	if( sizeof($tsk) ){
		$sql .= ' and tsk_id in ('.implode(', ', $tsk).')';
	}
	if( sizeof($tnt) ){
		$sql .= ' and tta_tnt_id in ('.implode(', ', $tnt).')';
	}
	if( is_numeric($tsk_group) && $tsk_group<0 ){
		$sql .= ' and tsk_parent_id='.$tsk_group;
	}
	if( $active!==null ){
		$sql .= ' and tta_is_active='.( $active ? '1' : '0' );
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet de supprimer la relation entre une (ou des) tâche(s) et un (ou des) locataire(s)
 *	@param int $tsk Identifiant de tâche ou tableau d'identifiants de tâches
 *	@param int $tnt Identifiant de locataire ou tableau d'identifiants de locataires
 *	@return bool True en cas de succès, False à la moindre échec
 */
function tsk_tasks_activation_del( $tsk, $tnt ){

	if( !is_array($tsk) ) $tsk = array($tsk);
	if( !is_array($tnt) ) $tnt = array($tnt);
	if( !sizeof($tnt) || !sizeof($tsk) ) return false;

	foreach( $tsk as $one_tsk ){
		if( !is_numeric($one_tsk) || $one_tsk<=0 ) return false;
	}
	foreach( $tnt as $one_tnt ){
		if( !is_numeric($one_tnt) || $one_tnt<=0 ) return false;
	}

	foreach( $tsk as $one_tsk ){
		foreach( $tnt as $one_tnt ){
			if( !ria_mysql_query('delete from tsk_tasks_activation where tta_tsk_id='.$one_tsk.' and tta_tnt_id='.$one_tnt) )
				return false;
		}
	}

	return true;
}

/**	Cette fonction vide toutes les informations d'activation de tâches pour un locataire spécifié
 *	@param int $tnt_id Identifiant du locataire
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tsk_tasks_activation_reset( $tnt_id ){
	if( !tnt_tenants_exists($tnt_id) ) return false;

	return ria_mysql_query('delete from tsk_tasks_activation where tta_tnt_id='.$tnt_id);
}

/**	Cette fonction permet de mettre à jour les statistiques d'une tâche
 *	@param int $tsk Identifiant de tâche ou tableau d'identifiants de tâches
 *	@param int $count_total Nombre total d'élément pris en compte par la tâche
 *	@param int $count_remaining Nombre total d'élément restant à synchroniser
 *	@param int $count_fail Nombre total d'élément où une erreur est survenue
 *	@param string $date_start Date du début de l'éxécution de la tâche
 *	@param string $date_end Date du fin de l'éxécution de la tâche
 *	@param string $last_date_obj Date de modification du dernier object synchronisé
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tsk_activities_set( $tsk, $count_total, $count_remaining, $count_fail, $date_start, $date_end, $last_date_obj=null ){
	global $config;

	if(tsk_activities_exists($tsk)){
		return tsk_activities_update( $tsk, $count_total, $count_remaining, $count_fail, $date_start, $date_end, $last_date_obj );
	}
	return tsk_activities_add( $tsk, $count_total, $count_remaining, $count_fail, $date_start, $date_end, $last_date_obj );
}

/** Ajoute les statistiques de progression d'une tâche. De préférence, il faut utiliser tsk_activities_set.
 *	@param int $tsk Identifiant de tâche ou tableau d'identifiants de tâches
 *	@param int $count_total Nombre total d'élément pris en compte par la tâche
 *	@param int $count_remaining Nombre total d'élément restant à synchroniser
 *	@param int $count_fail Nombre total d'élément où une erreur est survenue
 *	@param string $date_start Date du début de l'éxécution de la tâche
 *	@param string $date_end Date du fin de l'éxécution de la tâche
 *	@param string $last_date_obj Date de modification du dernier object synchronisé
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tsk_activities_add( $tsk, $count_total, $count_remaining, $count_fail, $date_start, $date_end, $last_date_obj=null ){
	global $config;
	if( !is_numeric($tsk) ) return false;
	if( !is_numeric($count_total) ) return false;
	if( !is_numeric($count_remaining) ) return false;
	if( !is_numeric($count_fail) ) return false;
	if( !isdateheure($date_start) ) return false;
	if( !isdateheure($date_end) ) return false;

	$last_date_obj = $last_date_obj!==null && isdateheure($last_date_obj) ? '\''.dateheureparse($last_date_obj).'\'' : 'null';

	return ria_mysql_query( 'insert into tsk_tasks_activities'.
							'(tta_tnt_id, tta_tsk_id, tta_count_total, tta_count_remaining, tta_count_fail, tta_date_start, tta_date_end, tta_last_date_obj) values '.
							'('.$config['tnt_id'].','.$tsk.','.$count_total.','.$count_remaining.','.$count_fail.',\''.dateheureparse($date_start).'\',\''.dateheureparse($date_end).'\','.$last_date_obj.')' );
}

/** Met à jour les statistique de progression d'une tâche. De préférence, il faut utiliser tsk_activities_set.
 *	@param int $tsk Identifiant de tâche ou tableau d'identifiants de tâches
 *	@param int $count_total Nombre total d'élément pris en compte par la tâche
 *	@param int $count_remaining Nombre total d'élément restant à synchroniser
 *	@param int $count_fail Nombre total d'élément où une erreur est survenue
 *	@param string $date_start Date du début de l'éxécution de la tâche
 *	@param string $date_end Date du fin de l'éxécution de la tâche
 *	@param string $last_date_obj Date de modification du dernier object synchronisé
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tsk_activities_update( $tsk, $count_total, $count_remaining, $count_fail, $date_start, $date_end, $last_date_obj=null ){
	global $config;
	if( !is_numeric($tsk) ) return false;
	if( !is_numeric($count_total) ) return false;
	if( !is_numeric($count_remaining) ) return false;
	if( !is_numeric($count_fail) ) return false;
	if( !isdateheure($date_start) ) return false;
	if( !isdateheure($date_end) ) return false;

	$sql =  'update tsk_tasks_activities set '.
							'tta_count_total='.$count_total.','.
							'tta_count_remaining='.$count_remaining.','.
							'tta_count_fail='.$count_fail.','.
							'tta_date_start=\''.dateheureparse($date_start).'\','.
							'tta_date_end=\''.dateheureparse($date_end).'\','.
							'tta_last_date_obj='.( $last_date_obj!==null && isdateheure($last_date_obj) ? '\''.dateheureparse($last_date_obj).'\'' : 'null').
							' where tta_tnt_id='.$config['tnt_id'].' and tta_tsk_id='.$tsk ;

	return ria_mysql_query($sql);
}

/** Cette fonction détermine l'existence des statistiques de synchronisation
 *	@param int $tsk Identifiant de la tâche. S'il n'est pas numérique, la fonction retournera False.
 *	@return bool True si la tâche existe, False sinon.
 */
function tsk_activities_exists( $tsk ){
	global $config;

	if( !is_numeric($tsk) ){
		return false;
	}

	$res = ria_mysql_query( 'select tta_tsk_id from tsk_tasks_activities where tta_tnt_id='.$config['tnt_id'].' and tta_tsk_id='.$tsk );
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows( $res );
}

/** Cette fonction permet de recupérer les statistiques de synchronisation
 *	@param int $tsk Identifiant de la tâche. S'il n'est pas numérique, la fonction retournera False.
 *	@param bool $is_active Permet de ne retourner que les totaux sur les taches actives
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tsk_id : identifiant de la tâche
 *		- count_total : nombre total d'élement
 *		- count_remaining : nombre total d'élément restant à synchroniser
 *		- count_fail : nombre total d'élément en erreur
 *		- date_start : date de début de la tache
 *		- date_end : date de fin de la tache
 *		- last_date_obj : date de dernière modification du dernier object
 */
function tsk_activities_get( $tsk=0, $is_active=null ){
	global $config;
	if( $tsk!=0 && !is_numeric($tsk) ){
		return false;
	}

	$sql = '
		select t1.tta_tsk_id as tsk_id, t1.tta_count_total as count_total, t1.tta_count_remaining as count_remaining,
			t1.tta_count_fail as count_fail, t1.tta_date_start as date_start, t1.tta_date_end as date_end,
			t1.tta_last_date_obj as last_date_obj
		from tsk_tasks_activities t1
		left join tsk_tasks_activation t2 on t1.tta_tsk_id=t2.tta_tsk_id and t2.tta_tnt_id='.$config['tnt_id'].'
		where t1.tta_tnt_id='.$config['tnt_id'].'
		';

	if( is_numeric($tsk) && $tsk != 0){
		$sql .= ' and t1.tta_tsk_id = '.$tsk;
	}

	if( $is_active!==null){
		$sql .= ' and t2.tta_is_active = '.($is_active ? '1' : '0');
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer le md5 d'un objet (lié à l'API Swagger pour Alliance-Elevage)
 *  @param int|array $obj Obligatoire, identifiant ou tableau d'identifiants d'objet
 *  @param int $cls Obligatoire, identifiant de la classe d'objet
 * 	@param int $ipt_id Optionnel, identifiant d'un import
 * 	@return bool false en cas d'échec, une chaine vide si le md5 n'est pas présent, sinon le md5
 */
function tsk_md5_get( $cls, $obj, $ipt_id=0 ){
	$obj = control_array_integer($obj, true, true, true);
	if ($obj === false) {
		return false;
	}

	if (!is_numeric($cls) || $cls <= 0) {
		return false;
	}

	if (!is_numeric($ipt_id) || $ipt_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select tkm_md5 as md5
		from tsk_md5
		where tkm_tnt_id = '.$config['tnt_id'].'
		and tkm_cls_id = '.$cls.'
	';

	$i = 0;
	while( $i<sizeof($obj) && $i < 3){
		$sql .= ' and tkm_obj_id_'.$i.' = '.$obj[$i];
		$i++;
	}

	if ($ipt_id >= 0) {
		$sql .= ' and tkm_ipt_id = '.$ipt_id;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer la somme md5 d'un objet de la base de données
 * 	@param int $cls Obligatoire, identifiant de la classe d'objet
 * 	@param int|array $obj Obligatoire, identifiant ou tableau d'identifiants d'objet
 * 	@param int $ipt_id Optionnel, identifiant d'un import
 * 	@return bool false en cas d'échec, une tableau vide si il n'y a pas de md5 détaillé, sinon un tableau associatif contenant les différent md5
 */
function tsk_md5_details_get( $cls, $obj, $ipt_id=0 ){
	if( !is_numeric($cls) || $cls<=0 ){
		return false;
	}

	$obj = control_array_integer($obj, true, true, true);
	if ($obj === false) {
		return false;
	}

	if (!is_numeric($ipt_id) || $ipt_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select tkm_details_md5 as details_md5
		from tsk_md5
		where tkm_tnt_id = '.$config['tnt_id'].'
		and tkm_cls_id = '.$cls.'
	';

	$i = 0;
	while( $i<sizeof($obj) && $i < 3){
		$sql .= ' and tkm_obj_id_'.$i.' = '.$obj[$i];
		$i++;
	}

	if ($ipt_id >= 0) {
		$sql .= ' and tkm_ipt_id = '.$ipt_id;
	}

	$res = ria_mysql_query($sql);
	if( $res === false ){
		return false;
	}

	$details_md5 = array();
	if( ria_mysql_num_rows($res) ){
		$r = ria_mysql_fetch_assoc($res);
		$details_md5 = json_decode($r['details_md5']);
	}

	return (array) $details_md5;
}

/** Cette fonction permet d'ajouter un md5 à un objet
 *  @param int $cls Obligatoire, identifiant de la classe d'objet
 *  @param int|array $obj Obligatoire, identifiant ou tableau d'identifiants d'objet
 * 	@param string $md5 Obligatoire, identifiant md5 associé
 * 	@param int $ipt_id Optionnel, identifiant d'un import
 * 	@param array $details Optionnel, tableau associatif contenant des md5 de certain champs de l'objet (ex: array( 'picture' => 'md5Picture', 'nameEnglish' => 'md5NameEnglish'))
 * 	@return bool false en cas d'échec, true en cas de succès
 */
function tsk_md5_add( $cls, $obj, $md5, $ipt_id=0, $details='' ){
	$obj = control_array_integer($obj, true, true, true);
	if ($obj === false) {
		return false;
	}

	if ( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if (trim($md5) == ''){
		return false;
	}

	if (!is_numeric($ipt_id) || $ipt_id < 0) {
		return false;
	}

	if( $details != '' && !is_array($details) ){
		return false;
	}

	global $config;

	$sql = '
		replace into tsk_md5
			(tkm_tnt_id, tkm_cls_id, tkm_md5, tkm_ipt_id, tkm_details_md5
	';

	$i = 0;
	while( $i<sizeof($obj) && $i < 3){
		$sql .= ', tkm_obj_id_'.$i;
		$i++;
	}

	$sql .= ' )
		values
			('.$config['tnt_id'].', '.$cls.', "'.$md5.'", '.($ipt_id ? $ipt_id : '0').',\''.json_encode($details).'\'
	';

	$i = 0;
	while( $i<sizeof($obj) && $i < 3){
		$sql .= ', '.$obj[$i];
		$i++;
	}

	$sql .= ')';

	return ria_mysql_query($sql);
}

/** Cette fonction permet d'ajouter un md5 à un objet
 *  @param int|array $obj Obligatoire, identifiant ou tableau d'identifiants d'objet
 *  @param int $cls Obligatoire, identifiant de la classe d'objet
 * 	@param string $md5 Obligatoire, identifiant md5 associé
 * 	@param int $ipt_id Optionnel, identifiant d'un import
 * 	@return bool false en cas d'échec, true en cas de succès
 */
function tsk_md5_upd( $cls, $obj, $md5, $ipt_id=0 ){
	return tsk_md5_add($cls, $obj, $md5, $ipt_id);
}


/// @}
// \endcond
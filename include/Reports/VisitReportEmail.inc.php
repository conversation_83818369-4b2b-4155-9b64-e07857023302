<?php
namespace Reports;
/** \defgroup excel_report_email Rapports de visite excel via email
 *	\ingroup excel_report
 *	Ce module permet la génération d'un email
 */
/**
 * \class VisitReportEmail
 * \brief Cette class permet l'envoi de l'email avec le raport excel de visite
 */
class VisitReportEmail
{
	/**
	 * Objet Email
	 *
	 * @var Email $email
	 */
	protected $email;

	/**
	 * Chemin complet du rapport
	 *
	 * @var string $report
	 */
	protected $report;

	/**
	 * Constructeur de la classe
	 *
	 * @param \Email $email
	 * @param mixed $report=null
	 * @return void
	 */
	public function __construct(\Email $email, $report=null)
	{
		$this->email = $email;
		if (!is_null($report) && file_exists($report)) {
			$this->report = $report;
		}
	}

	/**
	 * Cette fonction permet de notifier le rapport
	 *
	 * @return bool Retourne true si le rapport est bien envoyé sinon false
	 */
	public function notify()
	{
		$Date = new \DateTime('today');
		$this->email->addBcc('<EMAIL>');
		$this->email->setSubject(_('Rapports de visite du ') . $Date->format('d/m/Y'));
		$this->email->setHtmlMessage('<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">');
		$this->email->addParagraph(_('Cher client, chère cliente,'));
		if (!is_null($this->report)) {
			$this->email->addParagraph(_('Ci-joint les rapports de visites saisies pour aujourd\'hui.'));
			$this->email->addAttachment($this->report, _('Rapport_de_visite_') . $Date->format('Y_m_d') . '.xls');
		} else {
			$this->email->addParagraph(_('Vous n\'avez pas saisie de rapport de visite pour aujourd\'hui.'));
		}
		$this->email->addHtml('</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:contact@fr">contact@fr</a></div></div>');

		return $this->email->send();
	}
}
<?php
namespace Reports;
require_once('debug.inc.php');
require_once('excel/PHPExcel.php');
/** \defgroup excel_report Rapports de visite excel
 *	\ingroup yuto crm
 *	Ce module permet la génération excel d'un rapport de visite
 */
/**
 * \class VisitReport
 * \brief Cette classe permet la génération d'un export au format Microsoft Excel des rapports de visite en fonction des rapport en base
 */
class VisitReport
{
    /**
     * @var string $report_name Titre du rapport
     */
    protected $report_name;

    /**
     * @var integer $type_id Identifiant du type de rapport
     */
    protected $type_id;

    /**
     * @var integer $author Auteur du rapport
     */
    protected $author;

    /**
     * @var string $date_start Date de début de la période
     */
    protected $date_start;

    /**
     * @var string $date_end Date de fin de la période
     */
    protected $date_end;

    /**
     * @var \PHPExcel $objPHPExcel Référence à PHPExcel
     */
    protected $objPHPExcel;

    /**
     * @var \PHPExcel_IOFactory $objWriter
     */
    protected $objWriter;

    /**
     * @var ressource $r_reports Résultat de requête MySQL comprenant la liste des rapports
     */
    protected $r_reports;

    /**
     * @var array $report_fields Champs avancés des rapports de viste indexés par identifiant
     */
    protected $report_fields;

    /**
     * @var array $reports_fld_values Valeurs des champs avancés des rapports de visite indexés par identifiant de rapport et identifiant de champs avancé
     */
    protected $reports_fld_values;

    /**
     * __construct
     *
     * @param integer $type_id Type de rapport
     * @param integer $author Auteur du rapport
     * @param null|string $date_start Date de début de la période
     * @param null|string $date_end Date de fin de la période
     *
     * @return void
     */
    public function __construct($type_id = 0, $author = 0, $date_start = null, $date_end = null)
    {
        $this->type_id = $type_id;
        $this->author = $author;
        $this->date_start = $date_start;
        $this->date_end = $date_end;
    }

    /**
     * Permet de générer un rapport avec un résultat mysql de rp_reports_get
     *
     * @param resource $r_reports Résultat de rp_reports_get
     * @param integer $type_id Facultatif, type de rapport
     * @return VisitReport
     */
    public static function createFromResource($r_reports, $type_id = 0)
    {
        $instance = new VisitReport($type_id, 0, null,null);

        $instance->withReports($r_reports);

        return $instance;
    }

    /**
     * Retourne si il oui ou non il y a des rapports
     *
     * @return boolean
     */
    public function areThereReports()
    {
        return $this->r_reports && ria_mysql_num_rows($this->r_reports);
    }

    /**
     * Set le nom du rapport
     *
     * @param mixed $name Nom du rapport
     * @return Reports::VisitReport
     */
    public function withReportName($name)
    {
        $this->report_name = $name;

        return $this;
    }

    /**
     * Définie les rapports
     *
     * @param resource $r_reports Résultat de rp_reports_get
     * @return Reports::VisitReport
     */
    public function withReports($r_reports)
    {
        if (!ria_mysql_control_ressource($r_reports)) {
            throw new \Exception("Error Processing Request", 1);
        }
        $this->r_reports = $r_reports;

        return $this;
    }

    /**
     * Charge les rapports en fonction des paramètres fourni dans le constructeur
     *
     * @return $this
     */
    public function loadReport()
    {
        $this->r_reports = rp_reports_get(0, $this->type_id, 0, $this->author, $this->date_start, $this->date_end);

        return $this;
    }

    /**
     * Charge en mémoire les champs avancés associés aux rapports de visite
     *
     * @return VisitReport l'instance
     */
    public function loadReportFields()
    {
        $this->report_fields = array();
        $this->reports_fld_values = array();
        $r_mdl_id = rp_types_models_get( $this->type);
        $model = 0;
        if( $r_mdl_id ){
            while( $mdl = ria_mysql_fetch_assoc($r_mdl_id) ){
                $model = $mdl['mdl_id'];
            }
        }

        $fld_ids = array();

        $r_fld_fields_test = fld_models_fields_get($model);
        if ($r_fld_fields_test && ria_mysql_num_rows($r_fld_fields_test)) {
            while ($fld_field_test = ria_mysql_fetch_assoc($r_fld_fields_test)) {
                if (!in_array($fld_field_test['fld_id'], $fld_ids)) {
                    $fld_ids[] = $fld_field_test['fld_id'];
                }
            }
        }
        
        foreach ($fld_ids as $fld_id) {
            $r_fld_fields = fld_fields_get($fld_id);
            if ($r_fld_fields && ria_mysql_num_rows($r_fld_fields)) {
                while ($fld_field = ria_mysql_fetch_assoc($r_fld_fields)) {

                    if( !isset($this->report_fields[ $fld_field['id'] ]) ){
                        $this->report_fields[ $fld_field['id'] ] = [
                            'id' => $fld_field['id'],
                            'name' => $fld_field['name'],
                            'c_used' => 0,
                            'pos'=>0
                        ];
                    }
                    if($model){
                        $r_fld_model = fld_models_fields_get_pos($fld_field['id']);
                        if($r_fld_model && ria_mysql_num_rows($r_fld_model)){
                            while( $fld_model = ria_mysql_fetch_assoc($r_fld_model) ){
                                if(isset( $this->report_fields[ $fld_model['mf_fld_id'] ])){
                                    $this->report_fields[ $fld_field['id'] ]['pos'] =  $fld_model['mf_fld_pos'];
                                }

                            }
                        }
                    }


                    if( trim($fld_field['obj_value']) != '' ){
                        $this->report_fields[ $fld_field['id'] ]['c_used']++;
                    }
                }
            }

        }

        if( $this->r_reports && ria_mysql_num_rows($this->r_reports) ){


            while( $report = ria_mysql_fetch_assoc($this->r_reports) ){



                $r_fld_values = fld_object_values_get_all(CLS_REPORT, $report['id']);
                if( $r_fld_values && ria_mysql_num_rows($r_fld_values) ){
                    while( $fld_value = ria_mysql_fetch_assoc($r_fld_values) ){
                        if( !isset($this->reports_fld_values[$report['id']]) ){
                            $this->reports_fld_values[$report['id']] = array();
                        }

                        $this->reports_fld_values[$report['id']][$fld_value['id']] = $fld_value['obj_value'];
                    }
                }
            }

            // Les champs ayant une valeur en premier les autres après
            $this->report_fields = array_msort( $this->report_fields, ['pos' => SORT_ASC] );

            // Le tableau des champs avancés ne peut pas excéder 235 champs sinon l'export échoué car il y a trop de colonne pour la librairies utilisée
            // pour la génération du fichier Excel
            if( count($this->report_fields) > 235 ){
                $this->report_fields = array_slice( $this->report_fields, 0, 235 );
            }

            ria_mysql_data_seek($this->r_reports, 0);
        }

        return $this;
    }

    /**
     * Génére le rapport excel
     *
     * @param mixed $file Facultatif un fichier de sortie si non fournie le fichier sera envoie au navigateur pour téléchargement
     * @return void
     */
    public function generate($file = null)
    {
        if (is_null($this->r_reports)) {
            $this->loadReport();
        }

        if (!is_array($this->report_fields) || !is_array($this->reports_fld_values)) {
            $this->loadReportFields();
        }

        $this->genDefaultReportName();

        if ($this->shouldCreateExcel()) {
            $this->objPHPExcel = $this->createExcel();
            $this->objWriter = $this->createWriter();
        }

        if (is_null($file)) {
            $this->outputDownload();
        } else {
            $this->writeToFile($file);
        }
    }

    /**
     * écrit sur un fichier  l'excel
     *
     * @param mixed $file Fichier
     * @return void
     */
    protected function writeToFile($file)
    {
        $this->objWriter->save($file);
    }

    /**
     * Créer  l'objet writer de l'excel
     *
     * @return Excel5
     */
    protected function createWriter()
    {
        return \PHPExcel_IOFactory::createWriter($this->objPHPExcel, 'Excel5');
    }

    /**
     * Permet d'envoyer le fichier en téléchargement sur le navigateur
     *
     * @return void
     */
    protected function outputDownload()
    {
        // Redirect output to a client web browser (Excel5)
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="rapports-' . $this->report_name . date('dmY') . '.xls"');
        header('Cache-Control: max-age=0');

        // Ecrit le fichier et le sauvegarde
        $this->objWriter->save('php://output');
    }

    /**
     * Génère le nom du rapport en fonction du nom en base
     *
     * @return Reports::VisitReport
     */
    protected function genDefaultReportName()
    {
        if (is_null($this->report_name)) {
            $name = rp_types_get_name($this->type_id);
            $this->withReportName($name != false ? strtolower(str_replace(" ", "_", str_remove_accents($name))) . '-' : ""); // Suppression des accents dans le nom du fichier
        }

        return $this;
    }

    /**
     * Détermine si l'on doit générer l'objet PHPExcel
     *
     * @return boolean
     */
    protected function shouldCreateExcel()
    {
        return is_null($this->objPHPExcel);
    }

    /**
     * Génére l'objet PHPExcel avec l'excel près a être mis sous fichier
     *
     * @return PHPExcel
     */
    protected function createExcel()
    {
        global $config;

        // Création de l'objet PHPExcel
        $objPHPExcel = new \PHPExcel();

        // Déterminé les propriétés de la feuille Excel
        $objPHPExcel->getProperties()->setCreator("riaStudio")
            ->setLastModifiedBy("riaStudio")
            ->setTitle( _("Rapports de visite") )
            ->setSubject( _("Rapports de visite commerciale") )
            ->setDescription( _("Export des rapports de visite réalisées par les commerciaux") )
            ->setKeywords("export rapports")
            ->setCategory("");

        // Création du fichier
        $objWorksheet = $objPHPExcel->getActiveSheet();

        // 1° feuille : Contient l'entête des rapport
        $objWorksheet->setTitle( _('Rapports') );

        // 2° feuille : Contient les Notes de rapports
        $objWorksheet2 = $objPHPExcel->createSheet();
        $objWorksheet2->setTitle( _('Notes de rapports') );

        // 3° feuille : Contient les Objets liés
        $objWorksheet3 = $objPHPExcel->createSheet();
        $objWorksheet3->setTitle( _('Objets liés') );

        // Format numérique sur les colonnes contenant des chiffres
        $objWorksheet->getStyle('H')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00);
        $objWorksheet->getStyle('I')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00);
        $objWorksheet->getStyle('L')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00);
        $objWorksheet->getStyle('M')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_NUMBER_00);

        $objWorksheet2->getStyle('A1:D1')->getFont()->setBold(true);
        $objWorksheet2->getStyle('A1:D1')->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
        $objWorksheet2->getStyle('A1:D1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('00009999');
        $objWorksheet2->getStyle('A1:D1')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objWorksheet2->getStyle('A1:D1')->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        $objWorksheet3->getStyle('A1:C1')->getFont()->setBold(true);
        $objWorksheet3->getStyle('A1:C1')->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
        $objWorksheet3->getStyle('A1:C1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('00009999');
        $objWorksheet3->getStyle('A1:C1')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objWorksheet3->getStyle('A1:C1')->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        // Enregistre le nom des colonnes Rapport - Feuille N°1
        $col = 0;
        $cols_report = array(
            array('col' => $col++, 'col_name' => _('Id'), 'col_width' => 13),
            array('col' => $col++, 'col_name' => _('Type de rapport'), 'col_width' => 28),
            array('col' => $col++, 'col_name' => _('Statut du compte'), 'col_width' => 22),
            array('col' => $col++, 'col_name' => _('Référence client'), 'col_width' => 15),
            array('col' => $col++, 'col_name' => _('Nom client'), 'col_width' => 25)
        );

        if( isset($config['report_export_user_fields']) && is_array($config['report_export_user_fields']) && count($config['report_export_user_fields']) ){
            // Parcours de tous les champs avancés pour récupérer le nom du champ et l'ajouter à l'export
            foreach( $config['report_export_user_fields'] as $fld_id ){
                $fld_name = fld_fields_get_name($fld_id);
                if( trim($fld_name) ){
                    $cols_report[] = array('col' => $col++, 'col_name' => $fld_name, 'col_width' => 25);
                }
            }
        }

        $cols_report[] = array('col' => $col++, 'col_name' => _('Ville'), 'col_width' => 23);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Commentaire'), 'col_width' => 50);
        $cols_report[] = array('col' => $col++, 'col_name' => _('CA N-1'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('CA N'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Commandes N-1'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Commandes N'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Commande lors du RDV'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Devis lors du RDV'), 'col_width' => 20);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Référence de l\'auteur'), 'col_width' => 23);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Nom de l\'auteur'), 'col_width' => 25);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Date de création'), 'col_width' => 25);

        // Ajout des colonnes durée
        $cols_report[] = array('col' => $col++, 'col_name' => _('Durée'), 'col_width' => 25);
        $cols_report[] = array('col' => $col++, 'col_name' => _('Durée (en h)'), 'col_width' => 25);

        // Ajout des champs avancés en fin de fichier
        if (is_array($this->report_fields)) {
            foreach( $this->report_fields as $fld) {
                $cols_report[] = array('col' => $col++, 'col_name' => $fld['name'], 'col_width' => 25);
            }
        }

        foreach ($cols_report as $col_report) {

            // Enregistre le nom de la colonne comme titre de la colonne
            $objWorksheet->setCellValueByColumnAndRow($col_report['col'], 1, $col_report['col_name']);
            $objWorksheet->getColumnDimension($col_report['col'])->setWidth($col_report['col_width']);

        }

        // Police sur l'entête des colonnes

        $range = \PHPExcel_Cell::stringFromColumnIndex(0).'1:'.\PHPExcel_Cell::stringFromColumnIndex($col-1).'1';

        $objWorksheet->getStyle($range)->getFont()->setBold(true);
        $objWorksheet->getStyle($range)->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
        $objWorksheet->getStyle($range)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('00009999');
        $objWorksheet->getStyle($range)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objWorksheet->getStyle($range)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        // END colonnes Rapport - Feuille N°1

        // Enregistre le nom des colonnes notes - Feuille N°2
        $cols_note = array(
            array('col' => 'A', 'col_name' => _('Id'), 'col_width' => 15),
            array('col' => 'B', 'col_name' => _('Nom de la note'), 'col_width' => 25),
            array('col' => 'C', 'col_name' => _('Commentaire'), 'col_width' => 25),
            array('col' => 'D', 'col_name' => _('Valeur'), 'col_width' => 25)
        );

        foreach ($cols_note as $col_note) {
            $objWorksheet2->setCellValue($col_note['col'] . '1', $col_note['col_name']);
            $objWorksheet2->getColumnDimension($col_note['col'])->setWidth($col_note['col_width']);
        }
        // END colonnes notes - Feuille N°2

        // Enregistre le nom des colonnes objets - Feuille N°3
        $cols_object = array(
            array('col' => 'A', 'col_name' => _('Id'), 'col_width' => 15),
            array('col' => 'B', 'col_name' => _('Type'), 'col_width' => 25),
            array('col' => 'C', 'col_name' => _('Nom de l\'objet'), 'col_width' => 25)
        );

        foreach ($cols_object as $col_object) {
            $objWorksheet3->setCellValue($col_object['col'] . '1', $col_object['col_name']);
            $objWorksheet3->getColumnDimension($col_object['col'])->setWidth($col_object['col_width']);
        }
        // END colonnes objets - Feuille N°3


        // Récupération des rapports de visite

        $line_rpr = 2;
        $line_note = 2;
        $line_object = 2;

        // Enregistre la valeurs des cellules Rapport - Feuille N°1
        if ($this->areThereReports()) {
            while ($r = ria_mysql_fetch_assoc($this->r_reports)) {
                $col = 0;
                // Récupération des rapport ayant un durée (classe : CLS_CHECKIN)
                $object = rp_report_objects_get($r['id'],CLS_CHECKIN);

                $duree = _('Aucune durée');
                $duree_h = '';
                if( ria_mysql_num_rows($object) > 0 ){
                    $obj = ria_mysql_fetch_assoc($object);
                    // récupération de la durée du rapport
                    $rrck = rp_checkin_get( $obj['obj_id_0'] );
                    if( $rrck && ria_mysql_num_rows($rrck) > 0 ){
                        $rck = ria_mysql_fetch_assoc($rrck);
                        // Ajout de deux colonnes durées, la première affiche le temps en lecture (ex 1 heure 30 minute) et le secon en minute pour le calcul (ex 90)
                        $duree = convert_second_to_readable_delay(strtotime($rck["date_end_en"])-strtotime($rck["date_start_en"]));
                        $duree_h = (strtotime($rck["date_end_en"])-strtotime($rck["date_start_en"]))/3600;
                    }
                }
                $r['statut'] = _(gu_users_get_is_customer($r['usr_id'], $r['date_created']) ? 'Client' : 'Prospect');

                $objWorksheet->setCellValueByColumnAndRow($col, $line_rpr, $r['id']);
                $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['type_name']);
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['statut']); // cf #25304

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['usr_ref']);
                $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['usr_name']);

                if( isset($config['report_export_user_fields']) && is_array($config['report_export_user_fields']) && count($config['report_export_user_fields']) ){
                    // Parcours de tout les champs avancés pour récupérer la valeur du champ et l'ajouter à l'export
                    foreach( $config['report_export_user_fields'] as $fld_id ){
                        $fld_content = fld_object_values_get($r['usr_id'], $fld_id, '', false, true);
                        $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $fld_content === false ? '' : $fld_content);
                        $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                    }
                }

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['usr_city']);
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['comments']);

                // CA N-1, Nombre de commandes N-1, CA N, Nbr de commandes N (cf #25304)

                // Chiffres année N-1
                $totals_n1 = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('01/01/Y 00:00:00', strtotime($r['date_created_en'] . ' -1 year')), date('31/12/Y 23:59:59', strtotime($r['date_created_en'] . ' -1 year')));
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $totals_n1['total_ht']);

                $objWorksheet->getStyle(\PHPExcel_Cell::stringFromColumnIndex(0).$line_rpr.':'.\PHPExcel_Cell::stringFromColumnIndex($col).$line_rpr)
                    ->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_TOP);


                // Chiffres année N
                $totals_n = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('01/01/Y 00:00:00', strtotime($r['date_created_en'])), date('31/12/Y 23:59:59', strtotime($r['date_created_en'])));
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $totals_n['total_ht']);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $totals_n1['count']);
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $totals_n['count']);

                // Commande lors du rendez-vous (on prend la journée en référence)
                $visit_order = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('d/m/Y 00:00:00', strtotime($r['date_created_en'])), date('d/m/Y 23:59:59', strtotime($r['date_created_en'])));
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $visit_order['total_ht']);

                // Devis réalisé lors du rendez-vous (on prend la journée en référence)
                $visit_devis = ord_orders_range_get_totals(array(_STATE_BASKET, _STATE_DEVIS), $r['usr_id'], date('d/m/Y 00:00:00', strtotime($r['date_created_en'])), date('d/m/Y 23:59:59', strtotime($r['date_created_en'])));
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $visit_devis['total_ht']);

                // Auteur du rapport
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['author_ref']);
                $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['author_name']);
                $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $r['date_created']);
                $objWorksheet->getStyleByColumnAndRow($col, $line_rpr)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $duree);
                $objWorksheet->setCellValueByColumnAndRow(++$col, $line_rpr, $duree_h);

                // Sauvegarde les valeurs contenues dans les champs avancés (1er onglet)
                if( is_array($this->report_fields) ){
                    foreach( $this->report_fields as $fld ){
                        $col++;
                        if( is_array($this->reports_fld_values) && isset($this->reports_fld_values[$r['id']], $this->reports_fld_values[$r['id']][$fld['id']]) ){

                            // Récupère la cellule
                            $cell = $objWorksheet->getCellByColumnAndRow( $col, $line_rpr );

                            // Défini la valeur explicitement comme de type Chaîne pour éviter les problèmes d'interprétation par Excel
                            // des numéros de téléphones et autres valeurs pouvant être interprétées par Excel
                            $cell->setValueExplicit(
                                $this->reports_fld_values[ $r['id'] ][ $fld['id'] ],
                                \PHPExcel_Cell_DataType::TYPE_STRING
                            );

                        }
                    }
                }

                // Sauvegarde les commentaires (2ème onglet)
                $note = rp_report_notes_get($r['id']);
                if (!!$note) {
                    while ($n = ria_mysql_fetch_assoc($note)) {
                        $objWorksheet2->setCellValue('A' . $line_note, $n['rpr_id']);
                        $objWorksheet2->setCellValue('B' . $line_note, $n['note_name']);
                        $objWorksheet2->setCellValue('C' . $line_note, $n['comments']);
                        $objWorksheet2->setCellValue('D' . $line_note, $n['value']);

                        $objWorksheet2->getStyle('A' . $line_note)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                        $objWorksheet2->getStyle('D' . $line_note)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                        $objWorksheet2->getStyle('A' . $line_note . ':H' . $line_note)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_TOP);
                        $line_note++;
                    }
                }

                // Sauvegarde les objets associés (3ème onglet)
                $object = rp_report_objects_get($r['id']);
                if (!!$object) {
                    while ($o = ria_mysql_fetch_assoc($object)) {
                        $objWorksheet3->setCellValue('A' . $line_object, $o['rpr_id']);
                        $objWorksheet3->setCellValue('B' . $line_object, fld_classes_get_name($o["cls_id"]));

                        switch ($o["cls_id"]) {
                            case 1:
                                $objWorksheet3->setCellValue('C' . $line_object, prd_products_get_name($o["obj_id_0"]));
                                break;
                            case 3:
                                $objWorksheet3->setCellValue('C' . $line_object, prd_categories_get_name($o["obj_id_0"]));
                                break;
                            default:
                                break;
                        }

                        $objWorksheet3->getStyle('A' . $line_object)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                        $objWorksheet3->getStyle('D' . $line_object)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                        $objWorksheet3->getStyle('A' . $line_object . ':H' . $line_object)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_TOP);
                        $line_object++;
                    }
                }

                $line_rpr++;
            }
        }

        $objPHPExcel->setActiveSheetIndex(0);

        return $objPHPExcel;
    }
}
<?php
	require_once('db.inc.php');


/** \defgroup sys_naf_codes Nomenclature d'activités française
 *	\ingroup system
 *	Ce module comprend les fonctions nécessaires à la consultation des codes NAF.
 *	@{
 */

/** Cette fonction permet de récupérer un ou plusieurs codes naf.
 *	@param string $naf Optionnel, code NAF exact
 *	@param string $like Optionnel, début d'un code NAF
 *	@return resource Un résultat MySQL contenant :
 *				- code : code naf
 *				- name : intitulé du code naf
 */
function sys_naf_codes_get( $naf='', $like='' ){
	$sql = '
		select naf_code as code, naf_name as name
		from sys_naf_codes
		where 1
	';

	if( trim($naf)!='' ){
		$sql .= ' and naf_code=\''.addslashes( $naf ).'\'';
	}

	if( trim($like)!='' ){
		$sql .= ' and naf_code like \''.addslashes( $like ).'%\'';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de savoir si un code naf existe.
 *	@param string $naf Obligatoire, code naf à tester
 *	@return bool True si le code naf existe, False dans le cas contraire
 */
function sys_naf_codes_exists( $naf ){
	if( trim($naf)=='' ){
		return false;
	}

	$naf = trim( $naf );

	$sql = '
		select 1
		from sys_naf_codes
		where naf_code=\''.addslashes( $naf ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/// @}


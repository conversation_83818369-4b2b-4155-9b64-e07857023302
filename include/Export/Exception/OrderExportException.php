<?php

namespace Export\Exception;

require_once('Export/Exception/ExportException.php');

class OrderExportException extends ExportException
{
    /**
     * Identifiant de commande
     * 
     * @var int $order_id
     */
    protected $order_id;

    /**
     * Récupération de l'identifiant de la commande exportée
     *
     * @return int|null Identifiant de la commande
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * Modification de l'identifiant de la commande exportée
     *
     * @param int $order_id Identifiant de la commande
     * 
     * @return $this
     */
    public function setOrderId($order_id)
    {
        $this->order_id = $order_id;

        return $this;
    }

    /**
     * Constructeur de classe
     *
     * @param int $ord_id       Identifiant de la commande
     * @param string $message   Message d'erreur
     * @param int $code         Code d'erreur
     * 
     * @return void
     */
    public function __construct($ord_id = null, $message = '', $code = 0)
    {
        parent::__construct($message, $code);
        
        $this->order_id = $ord_id;
    }
}
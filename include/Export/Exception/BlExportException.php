<?php

namespace Export\Exception;

require_once('Export/Exception/ExportException.php');

class BlExportException extends ExportException
{
    /**
     * Identifiant de commande
     *
     * @var int $bl_id
     */
    protected $bl_id;

    /**
     * Récupération de l'identifiant du bon de commande exportée
     *
     * @return int|null Identifiant du bon de commande
     */
    public function getBlId()
    {
        return $this->bl_id;
    }

    /**
     * Modification de l'identifiant du bon de commande exportée
     *
     * @param int $bl_id Identifiant du bon de commande
     *
     * @return $this
     */
    public function setBlId($bl_id)
    {
        $this->bl_id = $bl_id;

        return $this;
    }

    /**
     * Constructeur de classe
     *
     * @param int $bl_id       Identifiant du bon de commande
     * @param string $message   Message d'erreur
     * @param int $code         Code d'erreur
     *
     * @return void
     */
    public function __construct($bl_id = null, $message = '', $code = 0)
    {
        parent::__construct($message, $code);

        $this->bl_id = $bl_id;
    }
}
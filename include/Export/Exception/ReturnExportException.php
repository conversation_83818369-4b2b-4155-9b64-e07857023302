<?php
namespace Export\Exception;

require_once( 'Export/Exception/ExportException.php' );

class ReturnExportException extends ExportException{
	protected $return_id; ///< Identifiant du bon de retour

	/** Récupération de l'identifiant du bon de retour exporté
	 * 	@return int|null Identifiant du bon de retour
	 */
	public function getReturnId(){
		return $this->return_id;
	}

	/** Modification de l'identifiant du bon de retour exporté
	 * 	@param int $return_id Identifiant du bon de retour
	 *	@return ReturnExportException L'objet courant
	 */
	public function setReturnId( $return_id ){
		$this->return_id = $return_id;
		return $this;
	}

	/** Constructeur de classe
	 * 	@param int $return_id Optionnel, identifiant du bon de retour
	 * 	@param string $message Optionnel, mMessage d'erreur
	 * 	@param int $code Optionnel, code d'erreur
	 * 	@return void
	 */
	public function __construct( $return_id=null, $message='', $code=0 ){
		parent::__construct( $message, $code );
		$this->return_id = $return_id;
	}
}
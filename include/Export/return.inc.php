<?php

require_once('Export/Exception/ReturnExportException.php');

use \Export\Exception\ReturnExportException;

/** Export d'un bon de retour
 *  @param int Identifiant du bon de retour
 *  @param string Format d'export. Par défaut: pdf
 *  @param array|null $options Options d'export
 *  @param array|null $bl Données du bon de retour (au format ord_bl_get) pour optimisation
 *  @return mixed Commande exportée dans le format demandé
 */
function export_return( $return_id, $output_format='pdf', array &$options=null, array &$bl=null ){
	global $config;

	$data['return'] = $bl;
	// Charge le bon de retour s'il n'a pas déjà été fourni en paramètre
	if( is_null($data['return']) ){
		$r_return = ord_returns_get( $return_id );
		if( !$r_return || !ria_mysql_num_rows($r_return) ){
			throw new ReturnExportException( $return_id, 'Bon de retour non trouvé' );
		}

		$data['return'] = ria_mysql_fetch_assoc( $r_return );
	}

	$r_contact = ria_mysql_query('
		select adr_firstname, adr_lastname
		from gu_adresses
			join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and adr_id = usr_adr_invoices)
			join ord_returns on (return_tnt_id = '.$config['tnt_id'].' and return_contact_id = usr_id)
		where adr_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id.'
	');

	if( $r_contact && ria_mysql_num_rows($r_contact) ){
		$contact = ria_mysql_fetch_assoc( $r_contact );
		$data['return']['contact'] = [
			'firstname' => $contact['adr_firstname'],
			'lastname' => $contact['adr_lastname'],
		];
	}

	if (is_null($options)) {
		$options = array();
	}

	$options = array_merge(
		array(
			'name' => $config['pdf_generation_return_name'],
			'logo' => $config['pdf_generation_return_logo'],
			'logo_disposition' => $config['pdf_generation_return_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_return_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_return_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_return_display_dlv_address'],
			'prd_reduce' => false,
			'display_payment' => isset($config['pdf_generation_return_display_payment']) ? $config['pdf_generation_return_display_payment'] : null,
			'header' => $config['pdf_generation_return_header'],
			'header_content' => $config['pdf_generation_return_header_content'],
			'footer' => $config['pdf_generation_return_footer'],
			'footer_content' => $config['pdf_generation_return_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_return_prd_reftruncated'],
			'prd_ecotaxe' => $config['pdf_generation_return_prd_ecotaxe'],
			'prd_barcode' => $config['pdf_generation_return_prd_barcode'],
			'prd_img' => $config['pdf_generation_return_prd_img'],
			'font_size' => $config['pdf_generation_return_font_size'],
			'ref' => ''
		),
		$options
	);

	// extrait les produits du bon de retour et leurs taxes
	$r_return_prd = ord_returns_products_get( 0, $data['return']['id'] );

	$data['return_products'] = array();
	$data['ecotaxe'] = array('base' => 0, 'amount' => 0);
	$data['tva'] = array();

	if( $r_return_prd ){
		while( $return_prd = ria_mysql_fetch_assoc($r_return_prd) ){
			$return_prd['img_id'] = prd_images_main_get( $return_prd['id'] );
			$return_prd['total_ht'] = $return_prd['price_ht'] * $return_prd['qte'];
			$return_prd['total_ttc'] = $return_prd['price_ttc'] * $return_prd['qte'];

			$data['return_products'][] = $return_prd;

			$eco_total_ht = $eco_total_ttc = 0;

			if ($return_prd['ecotaxe'] > 0) {
				$eco_total_ht = $return_prd['ecotaxe'] * $return_prd['qte'];
				$eco_total_ttc = $eco_total_ht * _TVA_RATE_DEFAULT;

				$data['ecotaxe']['base'] += $eco_total_ht;
				$data['ecotaxe']['amount'] += $eco_total_ttc - $eco_total_ht;
			}

			$total_ht = $return_prd['total_ht'];
			$total_ttc = $return_prd['total_ttc'];
			if (isset($return_prd['discount_type']) && isset($return_prd['discount'])){
				if ($return_prd['discount_type'] === "0"){ // Euros
					$total_ht = $return_prd['total_ht'] - $return_prd['discount'];
					$total_ttc = $return_prd['total_ttc'] - ($return_prd['discount'] * $return_prd['tva_rate']);
				} else { // %
					$total_ht = $return_prd['total_ht'] * (1-($return_prd['discount']/100));
					$total_ttc = $return_prd['total_ttc'] * (1-($return_prd['discount']/100));
				}
			}

			if (!isset($data['tva'][$return_prd['tva_rate']])) {
				$data['tva'][$return_prd['tva_rate']] = array('base' => 0, 'amount' => 0);
			}

			$total_ht = $total_ht - $eco_total_ht;
			$total_ttc = $total_ttc - $eco_total_ttc;

			$data['tva'][$return_prd['tva_rate']]['base'] += $total_ht;
			$data['tva'][$return_prd['tva_rate']]['amount'] += $total_ttc - $total_ht;
		}
	}

	if (!isset($data['return']['user_id'])) {
		throw new ReturnExportException($data['return']['id'], 'Aucun utilisateur lié au bon de retour');
	}

	$r_user = gu_users_get( $data['return']['user_id'] );
	$data['user'] = null;
	if( $r_user===false || ria_mysql_num_rows($r_user) <= 0) {
		//throw new ReturnExportException($data['return']['id'], 'Utilisateur non trouvé');
	}else{
		$data['user'] = ria_mysql_fetch_assoc($r_user);
	}

	$data['addresses'] = ord_orders_address_load([
		'inv_id' => $data['return']['adr_invoices'],
		'dlv_id' => $data['return']['adr_delivery'],
		'usr_id' => $data['return']['user_id'],
		'str_id' => $data['return']['str_id'],
		'rly_id' => 0,
	]);

	$data['addresses']['invoice']['type'] = 'facturation';
	$data['addresses']['delivery']['type'] = 'livraison';

	// extrait les infos du propriétaire du site
	$data['owner'] = site_owner_get();

	return export_return_pdf($data, $options);
}

/** Export PDF du bon de retour
 *	@see export_order
 *
 * 	@param array $data Données du bon de retour
 * 	@param array|null &$options Options
 *
 * 	@return mixed Export PDF (dépend de l'option ouput)
 */
function export_return_pdf( array &$data, array &$options = null ){
	global $config;

	switch($config['tnt_id']){
		default:
			// hack causé par les héritages
			$data['ord'] = $data['return'];
			unset( $data['return'] );

			require_once('Pdf/ReturnPdf.php');
			$pdf = new \Pdf\ReturnPdf( $data['ord'] );

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			$pdf->setData($data);
			$pdf->table()->withBody($data['return_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'].'.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->showTaxcode(false)
				->addTotalPage()
				->generate($filename, $output);
	}
}
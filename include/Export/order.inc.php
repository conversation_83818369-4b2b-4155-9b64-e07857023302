<?php

require_once('Export/Exception/OrderExportException.php');

use \Export\Exception\OrderExportException;

/**
 * Export d'une commande
 *
 * @param int Identifiant de la commande
 * @param string Format d'export. Par défaut: pdf
 * @param array|null & $options Options d'export
 * @param array|null & $ord Données de la commande (au format ord_orders_get_simple) pour optimisation
 *
 * @return mixed Commande exportée dans le format demandé
 */
function export_order( $ord_id, $output_format = 'pdf', array & $options = null, array & $ord = null ){
	global $config;

	$data['ord'] = $ord;
	// Charge la commande si elle n'a pas déjà été fournie en paramètre
	if( is_null($data['ord'])) {
		$r_ord = ord_orders_get_simple(array('id' => $ord_id));
		if( !$r_ord || !ria_mysql_num_rows($r_ord)){
			throw new OrderExportException($ord_id, 'Commande non trouvée');
		}
		$data['ord'] = ria_mysql_fetch_assoc($r_ord);
	}

	$r_contact = ria_mysql_query('
		select adr_firstname, adr_lastname
		from gu_adresses
			join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and adr_id = usr_adr_invoices)
				join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_contact_id = usr_id)
		where adr_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( $r_contact && ria_mysql_num_rows($r_contact) ){
		$contact = ria_mysql_fetch_assoc( $r_contact );
		$data['ord']['contact'] = [
			'firstname' => $contact['adr_firstname'],
			'lastname' => $contact['adr_lastname'],
		];
	}

	if (is_null($options)) {
		$options = array();
	}

	$options = array_merge(
		array(
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'prd_with_discount' => $config['pdf_generation_devis_with_discount'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_ecotaxe' => $config['pdf_generation_devis_prd_ecotaxe'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_tva' => $config['pdf_generation_devis_prd_tva'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => ''
		),
		$options
	);

	// Si demandé, vérifie que le statut de la commande fait partie des status autorisés
	if( isset($options['check_status']) && $options['check_status']
		&& isset($options['required_statuses']) && is_array($options['required_statuses'])
		&& !in_array(
			intval($data['ord']['state_id']),
			$options['required_statuses']
		)
	){
		throw new OrderExportException($data['ord']['id'], 'Le statut de la commande ne convient pas');
	}

	// extrait les produits de la commande et leurs taxes
	$r_ord_prd = ord_products_get($data['ord']['id'], array('group' => 'asc', 'group_parent' => 'asc', 'line_pos'=>'asc'));
	$data['ord_products'] = array();
	$data['ecotaxe'] = array('base' => 0, 'amount' => 0);
	$data['tva'] = array();
	if (false !== $r_ord_prd && ria_mysql_num_rows($r_ord_prd) > 0) {
		while ($ord_prd = ria_mysql_fetch_assoc($r_ord_prd)) {

			$ord_prd['fields'] = export_order_product_fields($ord_prd);
			$data['ord_products'][] = $ord_prd;

			$eco_total_ht = $eco_total_ttc = 0;

			if ($ord_prd['ecotaxe'] > 0) {
				$eco_total_ht = $ord_prd['ecotaxe'] * $ord_prd['qte'];
				$eco_total_ttc = $eco_total_ht * _TVA_RATE_DEFAULT;

				if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
					$eco_total_ht = round( $eco_total_ht );
					$eco_total_ttc = round( $eco_total_ttc );
				}

				$data['ecotaxe']['base'] += $eco_total_ht;
				$data['ecotaxe']['amount'] += $eco_total_ttc - $eco_total_ht;
			}

			$total_ht = $ord_prd['total_ht'];
			$total_ttc = $total_ht *  $ord_prd['tva_rate'];

			if (isset($ord_prd['discount_type']) && isset($ord_prd['discount'])){
				if ($ord_prd['discount_type'] === "0"){ // Euros
					$total_ht = $ord_prd['total_ht'] - $ord_prd['discount'];
					$total_ttc = ($ord_prd['total_ht'] * $ord_prd['tva_rate']) - ($ord_prd['discount'] * $ord_prd['tva_rate']);
				} else { // %
					$total_ht = $ord_prd['total_ht'] * (1-($ord_prd['discount']/100));
					$total_ttc = ($ord_prd['total_ht'] * $ord_prd['tva_rate']) * (1-($ord_prd['discount']/100));
				}
			}

			if (!isset($data['tva'][$ord_prd['tva_rate']])) {
				$data['tva'][$ord_prd['tva_rate']] = array('base' => 0, 'amount' => 0);
			}

			if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
				$total_ht = round( $total_ht );
				$total_ttc = round( $total_ttc );
			}

			$total_ht = $total_ht - $eco_total_ht;
			$total_ttc = $total_ttc - $eco_total_ttc;

			$data['tva'][$ord_prd['tva_rate']]['base'] += $total_ht;
			$data['tva'][$ord_prd['tva_rate']]['amount'] += $total_ttc - $total_ht;
		}
	}

	if (!isset($data['ord']['usr_id'])) {
		throw new OrderExportException($data['ord']['id'], 'Aucun utilisateur lié à la commande');
	}

	$r_user = gu_users_get( $data['ord']['usr_id'] );
	$data['user'] = null;
	if( $r_user===false || ria_mysql_num_rows($r_user) <= 0) {
		//throw new OrderExportException($data['ord']['id'], 'Utilisateur non trouvé');
	}else{
		$data['user'] = ria_mysql_fetch_assoc($r_user);
	}

	$data['addresses'] = ord_orders_address_load($data['ord']);
	$data['addresses']['invoice']['type'] = 'facturation';
	$data['addresses']['delivery']['type'] = 'livraison';

	$data['pay_name'] = '';
	$data['installments'] = array();

	// extrait l'échéancier
	$r_installments = ord_installments_get(0, 1, $data['ord']['id']);
	if ($r_installments && ria_mysql_num_rows($r_installments)) {
		while ($installement = ria_mysql_fetch_assoc($r_installments)) {
			$r_pay = gu_users_payment_types_get(0, $installement['pay']);
			$installement['pay_name'] = '';
			if ($r_pay && ria_mysql_num_rows($r_pay)) {
				$pay = ria_mysql_fetch_assoc($r_pay);
				$installement['pay_name'] = $pay['name'];
				$data['pay_name'] = gu_users_payment_types_view($pay);
			}
			$data['installments'][] = $installement;
		}
	}

	// extrait le moyen de paiement
	if (trim($data['pay_name']) == '') {
		$r_pay = false;
		if ($data['ord']['pay_id'] <= 0) {
			$r_pay = gu_users_payment_types_get($data['user']['id']);
		} else {
			$r_pay = gu_users_payment_types_get(0, $data['ord']['pay_id']);
		}

		if ($r_pay && ria_mysql_num_rows($r_pay)) {
			$pay = ria_mysql_fetch_assoc($r_pay);
			$data['pay_name'] = gu_users_payment_types_view($pay);
		}
	}

	// extrait les infos du propriétaire du site
	$data['owner'] =site_owner_get();

	// extrait les infos bancaire du site
	$r_bankinfo = site_bank_details_get(0);
	$data['bank_details'] = null;
	if ($r_bankinfo && ria_mysql_num_rows($r_bankinfo)) {
		$data['bank_details'] = ria_mysql_fetch_assoc($r_bankinfo);
	}


	// On regarde s'il existe une promotion pour la commande
	// On part du pédicat qu'il n'y en a pas et on cherche à en trouver une
	if( $options['prd_reduce'] && $config["pdf_generation_devis_prd_reduce_hide_if_empty"] ) {
		$discountExists = false;
		$i = 0;
		while (!$discountExists && $i < sizeof($data["ord_products"])) {
			$product = $data["ord_products"][$i];

			$fld_discount = fld_object_values_get(array($product['ord_id'], $product['id'], $product['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
			if (is_numeric($fld_discount) && $fld_discount > 0) {
				$discountExists = true;
			}

			$i += 1;
		}

		$options['prd_reduce'] = $discountExists;
	}

	switch ($output_format) {
		case 'excel':
			return export_order_excel($data, $options);
		case 'pdf':
			return export_order_pdf($data, $options);
	}

	return null;
}

/** Cette fonction permet de récupérer l'emplacement de l'image signature.
 * 	@param int $ord_id Obligatoire, identifiant d'une commande
 * 	@return string L'emplacement réseau de la signature
 */
function export_order_create_sign( $ord_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	$r_sign_row = ord_orders_signature_get( $ord_id );
	if( !$r_sign_row || !ria_mysql_num_rows($r_sign_row) ){
		return false;
	}

	$sign_row = ria_mysql_fetch_assoc( $r_sign_row );
	$sign = $sign_row['signature'];
	if( trim($sign) == '' ){
		return false;
	}

	$filename = $config['doc_dir'].'/img-sign-'.$ord_id.'.jpg';
	if( file_exists($filename) ){
		unlink( $filename );
	}

	$ar_sign_pt = explode(',', $sign);

	$min_x = $min_y = null;
	$max_x = $max_y = 0;
	foreach( $ar_sign_pt as $val ){
		$val = str_replace(['moveTo:', 'lineTo:'], '', $val);
		$v = explode(':', $val);

		// Min
		if( $v[0] < $min_x || $min_x === null ){
			$min_x = $v[0];
		}
		if( $v[1] < $min_y || $min_y === null ){
			$min_y = $v[1];
		}

		// Max
		if( $v[0] > $max_x ){
			$max_x = $v[0];
		}
		if( $v[1] > $max_y ){
			$max_y = $v[1];
		}
	}

	$height = $max_y > $max_x ? 500 : 250;
	$height = $height - ( $min_y * ($height / $max_y));

	$width = $max_x > $max_y ? 500 : 250;
	$width = $width - ( $min_x * ($width / $max_x));

	$ratioX = round( ($width / $max_x), 6 );
	$ratioY = round( ($height / $max_y), 6 );


	$draw = new ImagickDraw();

	$x = $y = 0;
	foreach( $ar_sign_pt as $val ){
		$action = strstr( $val, 'moveTo:') ? 'moveTo' : 'lineTo';
		$val = str_replace(['moveTo:', 'lineTo:'], '', $val);
		$v = explode(':', $val);

		if( $action == 'lineTo' ){
			$draw->setStrokeColor('black');
			$draw->setStrokeWidth(2);
			$draw->setFillColor('black');
			$draw->line(( ($x - $min_x) * $ratioX), (($y - $min_y) * $ratioY), (($v[0] - $min_x) * $ratioX), (($v[1] - $min_y) * $ratioY));
		}

		$x = $v[0];
		$y = $v[1];
	}

	$image = new Imagick();
	$image->newImage( $width - ($min_x * $ratioX)+2, $height - ($min_y * $ratioY), 'white' );
	$image->setImageFormat('jpeg');
	$image->drawImage($draw);

	$image->writeImage( $filename );
	return $filename;
}

/**	Récupère la liste des champs souhaités et leurs valeurs
 * @param	array	$ord_prd	Ligne de commande
 * @return	array|false			Tableau contenant les champs et valeurs d'un produit, false sinon
 */
function export_order_product_fields(array $ord_prd){

	global $config;

	if( !isset($config['export_order_prd_fields']) || !is_string($config['export_order_prd_fields']) ){
		return false;
	}
	$ex = explode(',', $config['export_order_prd_fields']);

	if( !is_array($ex) || !count($ex) ){
		return false;
	}
	$fields = [];

	foreach($ex as $fld_id){

		if( !is_numeric($fld_id) || !$fld_id || !fld_fields_exists($fld_id) ){
			continue;
		}
		$fld_id = (int)$fld_id;
		$val = fld_object_values_get($ord_prd['id'], $fld_id);

		if( !is_string($val) && !is_numeric($val) ){
			continue;
		}

		if( is_string($val) && trim($val) == ''){
			continue;
		}
		$fld_name = fld_fields_get_name($fld_id);
		$fields[$fld_name] = strip_tags($val);

	}

	return count($fields) ? $fields : false;

}

/**
 * Formattage du nom d'une ligne de commande
 *
 * @param array $ord_prd Ligne de commande
 *
 * @return string Nom de la ligne de commande formatté
 */
function export_order_product_name( array & $ord_prd ){
	$is_colisage = prd_colisage_classify_exists($ord_prd['id']);
	$colisage_id = 0;
	if ($is_colisage && $colisage_id = fld_object_values_get(array($ord_prd['ord_id'], $ord_prd['id'], $ord_prd['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
		$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
		$colisage = ria_mysql_fetch_assoc($r_colisage);
	} else {
		$is_colisage = false;
	}

	$comment = '';
	if( $ord_prd['id']!=0 && $ord_prd['notes'] ){
		$comment = "\n";
	}
	if ($ord_prd['notes']) {
		$comment .= $ord_prd['notes'];
	}

	// Remplace le retour chariot par un saut ligne suivi d'un retour chariot.
	$comment = str_replace("\r", "\n\r", $comment);
	$product_name = ($ord_prd['id'] != 0 ? $ord_prd['name']. ($is_colisage ? ' - '.$colisage['name'].' ('.parseInt($colisage['qte']).')' : '') : '') . $comment;

	if( is_numeric($ord_prd['group_parent_id']) && $ord_prd['group_parent_id'] >= 0 ){
		$product_name = '    '.$product_name;
	}

	if(prd_nomenclatures_options_exists($ord_prd['id'])){
		$r_nomenclature_products = ord_products_get( $ord_prd['ord_id'], array('name' => 'asc'), 0, '', null, false, -1, $ord_prd['id'], $ord_prd['child-line'] );
		if ($r_nomenclature_products && ria_mysql_num_rows($r_nomenclature_products)){
			$last_name = '';
			$last_comment = '';
			$qte = 0;
			$count = 0;
			while ($product = ria_mysql_fetch_assoc($r_nomenclature_products)){
				if (trim($last_name) != '' && $last_name != $product['name']) {
					$product_name .= "\n- ".$last_name .' x'.$qte;
					if ($product['notes']) {
						$product_name .= "\n   ".$last_comment;
					}
					$count = 0;
					$qte = 0;
				}

				if(!$count){
					$count++;
				}
				$last_name = $product['title'];
				$last_comment = $product['notes'];
				$qte += $product['qte'];

			}
			$product_name .= "\n- ".$last_name.' x'.$qte;
			if ($product['notes']) {
				$product_name .= "\n   ".$last_comment;
			}
		}
	}

	return $product_name;
}

/**
 * Export PDF de la commande
 * @see export_order
 *
 * @param array $data Données de la commande
 * @param array|null & $options Options
 *
 * @return mixed Export PDF (dépend de l'option ouput)
 */
function export_order_pdf( array & $data, array & $options = null ){
	global $config;

	switch($config['tnt_id']){
		case 268: // Zolux
		case 1317: // Zolux Es
		case 1118: // Francodex
		case 1053: //Saint Bernard
			if ($config['tnt_id'] == 268) {
				require_once('Pdf/custom/ZoluxOrderPdf.inc.php');
				$pdf = new Pdf\ZoluxOrderPdf($data['ord']);
			}
			if ($config['tnt_id'] == 1317) {
				require_once('Pdf/custom/ZoluxEsOrderPdf.inc.php');
				$pdf = new Pdf\ZoluxEsOrderPdf($data['ord']);
			}
			else if ($config['tnt_id'] == 1118) {
				require_once('Pdf/custom/FrancodexOrderPdf.inc.php');
				$pdf = new Pdf\FrancodexOrderPdf($data['ord']);
			}
			else if ($config['tnt_id'] == 1053) {
				require_once('Pdf/custom/SaintBernardOrderPdf.inc.php');
				$pdf = new Pdf\SaintBernardOrderPdf($data['ord']);				
			}

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			// Transforme la date de commande au format FR
			$date = new DateTime( $data['ord']['date'] );
			$data['ord']['date'] = $date->format('d/m/Y');

			// Transforme la date de livraison au format FR
			if( trim($data['ord']['date_livr']) != '' ){
				$date = new DateTime( $data['ord']['date_livr'] );
				$data['ord']['date_livr'] = $date->format('d/m/Y');
			}

			// Charge le compte du représentant
			$data['seller'] = false;
			if( is_numeric($data['ord']['seller_id']) && $data['ord']['seller_id'] > 0 ){
				$r_seller = gu_users_get( $data['ord']['seller_id'] );
				if( $r_seller && ria_mysql_num_rows($r_seller) ){
					$data['seller'] = ria_mysql_fetch_assoc( $r_seller );
				}
			}

			// Charge les informations de BL
			$r_bl = ord_bl_get( 0, 0, false, false, false, [], false, [$data['ord']['id']] );
			$data['bl'] = ['id' => 0, 'piece' => '', 'ref' => ''];
			if( $r_bl && ria_mysql_num_rows($r_bl) ){
				$data['bl'] = ria_mysql_fetch_assoc( $r_bl );
			}

			// Charge les informations de facturation
			$r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $data['ord']['id'] );
			$data['inv'] = ['id' => 0, 'piece' => '', 'ref' => '' ];
			if( $r_inv && ria_mysql_num_rows($r_inv) ){
				$data['inv'] = ria_mysql_fetch_assoc( $r_inv );
			}

			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->withUserInfoOnAllPages(false)
				->withAdressBlocBorder(false)
				->generate($filename, $output);

			break;
		case 1024: // Hermitage
			// $config['site_url'] = 'http://hermitage.nicolas_clisson.dev.fr';
			// $config['site_dir'] = '/var/www/nicolas_clisson/hermitage/htdocs';
			require_once $config['site_dir'] . '/include/view.site.inc.php';
			require_once 'Services/Template.class.php';
			require_once 'Services/Catalog/Product.class.php';
			require_once 'Services/Customer/Customer.class.php';
			require_once 'Services/Customer/Order.class.php';

			$config['fld_order_title']					= 105900;
			$config['fld_order_state']					= 114140;
			$config['fld_order_line_license']			= 105901;
			$config['fld_order_user_origin']			= 114172;
			$config['fld_order_line_license']			= 105901;
			$config['fld_order_user_origin']			= 114172;

			$Order = new OrderService([
				'ord'		=> $data['ord']['id'],
				'user'		=> $data['ord']['usr_id'],
				'status'	=> [_STATE_DEVIS]
			]);

			if (!$Order->exists()) {
				throw new Exception(i18n::get('Le devis n\'existe pas ou plus.', 'ERROR'), 1);
			}
			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			/** @phpstan-ignore-next-line */
			hermitage_generate_devis($Order, $output);
			break;
		case 395: //Corep
			require_once('Pdf/custom/CorepOrderPdf.inc.php');
			$pdf = new Pdf\CorepOrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			$pdf->setData($data);

			if($data['ord']['pay_id'] == _PAY_VIREMENT){
				$pdf->setDisplayBankInfo();
			}

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->showTaxcode(false)
				->addTotalPage()
				->generate($filename, $output);

			break;
		case 352: //boplan
			require_once('Pdf/custom/BoplanOrderPdf.php');

			$pdf = new Pdf\BoplanOrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->withUserInfoOnAllPages(false)
				->withAdressBlocBorder(false)
				->generate($filename, $output);

			break;
		case 268: // Zolux
			require_once('Pdf/custom/ZoluxOrderPdf.inc.php');
			$pdf = new Pdf\ZoluxOrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			// Transforme la date de commande au format FR
			$date = new DateTime( $data['ord']['date'] );
			$data['ord']['date'] = $date->format('d/m/Y');

			// Transforme la date de livraison au format FR
			if( trim($data['ord']['date_livr']) != '' ){
				$date = new DateTime( $data['ord']['date_livr'] );
				$data['ord']['date_livr'] = $date->format('d/m/Y');
			}

			// Charge le compte du représentant
			$data['seller'] = false;
			if( is_numeric($data['ord']['seller_id']) && $data['ord']['seller_id'] > 0 ){
				$r_seller = gu_users_get( $data['ord']['seller_id'] );
				if( $r_seller && ria_mysql_num_rows($r_seller) ){
					$data['seller'] = ria_mysql_fetch_assoc( $r_seller );
				}
			}

			// Charge les informations de BL
			$r_bl = ord_bl_get( 0, 0, false, false, false, [], false, [$data['ord']['id']] );
			$data['bl'] = ['id' => 0, 'piece' => '', 'ref' => ''];
			if( $r_bl && ria_mysql_num_rows($r_bl) ){
				$data['bl'] = ria_mysql_fetch_assoc( $r_bl );
			}

			// Charge les informations de facturation
			$r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $data['ord']['id'] );
			$data['inv'] = ['id' => 0, 'piece' => '', 'ref' => '' ];
			if( $r_inv && ria_mysql_num_rows($r_inv) ){
				$data['inv'] = ria_mysql_fetch_assoc( $r_inv );
			}

			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->withUserInfoOnAllPages(false)
				->withAdressBlocBorder(false)
				->generate($filename, $output);

			break;
		case 447: // Meliconi
		// case 31: // Pour test : NE PAS dé-commenter EN PROD
			require_once('Pdf/custom/MeliconiOrderPdf.php');
			$pdf = new Pdf\MeliconiOrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			// Ajout des champs avancé pour meliconi dans les data
			$data['ord']['total_D3E'] = $data['ord']['total_MOB'] = $data['ord']['total_TNT'] = 0;

			// Pour chaque produit, on récupère les taxes D3E, TNT et MOB
			foreach($data['ord_products'] as $id => $ord_line){

				$data['ord_products'][$id]['D3E'] = $data['ord_products'][$id]['TNT'] = $data['ord_products'][$id]['MOB'] = false;
				$json = fld_object_values_get(array($ord_line['ord_id'],$ord_line['id'], $ord_line['line']), 5074);
				$ar_fld = json_decode($json, true);

				foreach($ar_fld['taxes']['detail'] as $taxes){
					if ($taxes['type'] == '3'){

						// $ar_D3E = ['D3E', 'D3E05', 'D3E08', 'D3E15', 'D3E16', 'D3E30', 'D3E83', 'D3EB', 'D4E', 'D4EC', 'D4ECB'];
						// $ar_TNT = ['TNT', 'TNT1', 'TNT2', 'TNT3', 'TNT4'];
						if( strpos($taxes['name'], 'D3E') !== false || strpos($taxes['name'], 'D4E') !== false) {
							$data['ord_products'][$id]['D3E'] = $taxes['price'];
						} else if(strpos($taxes['name'], 'TNT') !== false) {
							$data['ord_products'][$id]['TNT'] = $taxes['price'];
						} else if (strpos($taxes['name'], "MOB") !== false){
							$data['ord_products'][$id]['MOB'] = $taxes['price'];
						}
					}
				}
			}

			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->showTaxcode(false)
				->addTotalPage()
				->generate($filename, $output);

			break;
		case 524: // 524 Isoweck, 56 RiaShopSync sur dev
			$r_model = fld_object_models_get( $data['ord']['id'] );
			// Charge le compte du représentant
			$data['seller'] = false;
			if( is_numeric($data['ord']['seller_id']) && $data['ord']['seller_id'] > 0 ){
				$r_seller = gu_users_get( 0, '', '', array(PRF_ADMIN, PRF_SELLER), '', 0, '', false, false, $data['ord']['seller_id'] );
				if( $r_seller && ria_mysql_num_rows($r_seller) ){
					$data['seller'] = ria_mysql_fetch_assoc( $r_seller );
				}
			}
			// Pour la récupération des moyens de paiement
			$data['user']['user_payment_name'] = '';
			$data['user']['user_payment_days'] = '';
			// On regarde s'il existe un type de paiement corrélé entre le pay_id et l'utilisateur
			if ($data['ord']['pay_id'] > 0 && $data['user']['id'] > 0) {
				$r_pay = gu_users_payment_types_get($data['user']['id'], $data['ord']['pay_id'], true);

				if ($r_pay && ria_mysql_num_rows($r_pay)) {
					$pay = ria_mysql_fetch_assoc($r_pay);
					$data['user']['user_payment_name'] = gu_users_payment_types_view($pay);
					$data['user']['user_payment_days'] = $pay['days'];
				}
			}

			// Si on n'a rien trouvé, on recherche un autre paiement à partir de l'id de paiement de la commande
			// Cette partie m'a l'air "aléatoire" mais c'est celle qui est utilisée dans le standard
			if (trim($data['user']['user_payment_name']) == '' && $data['ord']['pay_id'] > 0) {
				$r_pay = false;
				$r_pay = gu_users_payment_types_get(0, $data['ord']['pay_id']);

				if ($r_pay && ria_mysql_num_rows($r_pay)) {
					$pay = ria_mysql_fetch_assoc($r_pay);
					$data['user']['user_payment_name'] = gu_users_payment_types_view($pay);
					$data['user']['user_payment_days'] = $pay['days'];
				}
			}

			// Si la commande ne possède pas d'ID, on prend le moyen de règlement du client
			if(trim($data['user']['user_payment_name']) == '' && isset($data['user']['opm_id']) && is_numeric($data['user']['opm_id']) && $data['user']['opm_id'] > 0 ){
				if( $rdetails = ord_payment_model_details_get( $data['user']['opm_id'] ) ){
					while( $detail = ria_mysql_fetch_assoc($rdetails) ){

						$data['user']['user_payment_name'] = $detail['pay_name'];
						$data['user']['user_payment_days'] = $detail['days'];
						$detailedPaymentName = gu_users_payment_types_view($detail);
						if ($detailedPaymentName)
						{
							$data['user']['user_payment_name'] = $detailedPaymentName;
						}
					}
				}
			}
			if( $r_model && ria_mysql_num_rows($r_model) ){
                $model = ria_mysql_fetch_assoc( $r_model );

                switch( $model['mdl_id'] ){
                    case 2070: // Gré à Gré Particulier - Voir ticket ISO-72
						$data['ord']['AdresseChantier'] = fld_object_values_get($data['ord']['id'], 102975 );
						$data['ord']['ComplementAdresseChantier'] = fld_object_values_get($data['ord']['id'], 105892 );
						$data['ord']['CodePostalChantier'] = fld_object_values_get($data['ord']['id'], 102980 );
						$data['ord']['VilleChantier'] = fld_object_values_get($data['ord']['id'], 105891 );
						$data['ord']['NumeroLotChantier'] = fld_object_values_get($data['ord']['id'], 103052 );
						$data['ord']['ZoneTravailChantier'] = fld_object_values_get($data['ord']['id'], 103087 );
						$data['ord']['NombreSpotsChantier'] = fld_object_values_get($data['ord']['id'], 102981 );
						$data['ord']['TypeAccesComblesChantier'] = fld_object_values_get($data['ord']['id'], 103091 );
						require_once('Pdf/custom/Isoweck/IsoweckQuoteGreAGreParticulierPdf.inc.php');
						$pdf = new Pdf\IsoweckQuoteGreAGreParticulierPdf($data['ord']);
						break;
					case 1927: // Gré à Gré Plaquiste - Voir ticket ISO-72
						$data['ord']['AdresseChantier'] = fld_object_values_get($data['ord']['id'], 102975 );
						$data['ord']['ComplementAdresseChantier'] = fld_object_values_get($data['ord']['id'], 105892 );
						$data['ord']['CodePostalChantier'] = fld_object_values_get($data['ord']['id'], 102980 );
						$data['ord']['VilleChantier'] = fld_object_values_get($data['ord']['id'], 105891 );
						$data['ord']['NumeroLotChantier'] = fld_object_values_get($data['ord']['id'], 103052 );
						$data['ord']['TypeAccesComblesChantier'] = fld_object_values_get($data['ord']['id'], 103091 );
						require_once('Pdf/custom/Isoweck/IsoweckQuoteGreAGrePlaquistePdf.inc.php');
						$pdf = new Pdf\IsoweckQuoteGreAGrePlaquistePdf($data['ord']);
						break;
					case 2044: // Gros chantiers Plaquiste/Entreprise général - Voir ticket ISO-72
						$data['ord']['AdresseChantier'] = fld_object_values_get($data['ord']['id'], 102975 );
						$data['ord']['ComplementAdresseChantier'] = fld_object_values_get($data['ord']['id'], 105892 );
						$data['ord']['CodePostalChantier'] = fld_object_values_get($data['ord']['id'], 102980 );
						$data['ord']['VilleChantier'] = fld_object_values_get($data['ord']['id'], 105891 );
						$data['ord']['NumeroLotChantier'] = fld_object_values_get($data['ord']['id'], 103052 );
						$data['ord']['ZoneTravailChantier'] = fld_object_values_get($data['ord']['id'], 103087 );
						$data['ord']['TypeAccesComblesChantier'] = fld_object_values_get($data['ord']['id'], 103091 );
						require_once('Pdf/custom/Isoweck/IsoweckQuoteGrosChantierPlaquisteEntreprisePdf.inc.php');
						$pdf = new Pdf\IsoweckQuoteGrosChantierPlaquisteEntreprisePdf($data['ord']);
						break;
					case 2073: // Tarifs constructeurs - Voir ticket ISO-59
						$data['ord']['AnneeTarif'] = fld_object_values_get($data['ord']['id'], 103101 );
						$data['ord']['PrestationType'] = fld_object_values_get($data['ord']['id'], 105908 );
						$data['ord']['ProduitType'] = fld_object_values_get($data['ord']['id'], 105909 );
						$data['ord']['DateValidite'] = fld_object_values_get($data['ord']['id'], 103102 );
						$data['ord']['ForfaitIntervention85'] = fld_object_values_get($data['ord']['id'], 103103 );
						$data['ord']['ForfaitIntervention9'] = fld_object_values_get($data['ord']['id'], 105910 );
						$data['ord']['DateApplication'] = fld_object_values_get($data['ord']['id'], 103104 );
						require_once('Pdf/custom/Isoweck/IsoweckQuoteConstructorTarifPdf.inc.php');
						$pdf = new Pdf\IsoweckQuoteConstructorTarifPdf($data['ord']);
						break;
					default:
						require_once('Pdf/OrderPdf.php');
						$pdf = new Pdf\OrderPdf($data['ord']);

						if(!is_null($options)){
							$pdf->setOptions($options);
						}
						break;
				}
			}
			else
			{
				require_once('Pdf/OrderPdf.php');
				$pdf = new Pdf\OrderPdf($data['ord']);

				if(!is_null($options)){
					$pdf->setOptions($options);
				}
			}
			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->withUserInfoOnAllPages(false)
				->withAdressBlocBorder(false)
				->generate($filename, $output);
			break;

		case 1118: // Francodex
			require_once('Pdf/custom/FrancodexOrderPdf.inc.php');
			$pdf = new Pdf\FrancodexOrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			// Transforme la date de commande au format FR
			$date = new DateTime( $data['ord']['date'] );
			$data['ord']['date'] = $date->format('d/m/Y');

			// Transforme la date de livraison au format FR
			if( trim($data['ord']['date_livr']) != '' ){
				$date = new DateTime( $data['ord']['date_livr'] );
				$data['ord']['date_livr'] = $date->format('d/m/Y');
			}

			// Charge le compte du représentant
			$data['seller'] = false;
			if( is_numeric($data['ord']['seller_id']) && $data['ord']['seller_id'] > 0 ){
				$r_seller = gu_users_get( $data['ord']['seller_id'] );
				if( $r_seller && ria_mysql_num_rows($r_seller) ){
					$data['seller'] = ria_mysql_fetch_assoc( $r_seller );
				}
			}

			// Charge les informations de BL
			$r_bl = ord_bl_get( 0, 0, false, false, false, [], false, [$data['ord']['id']] );
			$data['bl'] = ['id' => 0, 'piece' => '', 'ref' => ''];
			if( $r_bl && ria_mysql_num_rows($r_bl) ){
				$data['bl'] = ria_mysql_fetch_assoc( $r_bl );
			}

			// Charge les informations de facturation
			$r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $data['ord']['id'] );
			$data['inv'] = ['id' => 0, 'piece' => '', 'ref' => '' ];
			if( $r_inv && ria_mysql_num_rows($r_inv) ){
				$data['inv'] = ria_mysql_fetch_assoc( $r_inv );
			}

			$pdf->setData($data);

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->withUserInfoOnAllPages(false)
				->withAdressBlocBorder(false)
				->generate($filename, $output);

			break;
		default:
			// APOL - On conserve le standard PDF pour éviter le code dupliqué on met le cas ici
			if ($config['tnt_id'] == 1080 && fld_object_values_get($data['ord']['id'], 114427 ) == 'INCERMED') {
				$data['owner']['name'] = 'INCERMED DISTRIBUTION';
				$data['owner']['address1'] = '599 rue Réné Cassin';
				$data['owner']['inscription'] = '**************';
				$data['owner']['capital'] = '10000';
				$data['owner']['naf'] = '3250A';
				$data['owner']['taxcode'] = 'FR50535234793';
				$options['logo'] = 797745;
				$options['logo_size_x'] = 425;
				$options['logo_size_y'] = 119;
			}
			require_once('Pdf/OrderPdf.php');
			$pdf = new Pdf\OrderPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			$pdf->setData($data);

			if($data['ord']['pay_id'] == _PAY_VIREMENT){
				$pdf->setDisplayBankInfo();
			}

			$pdf->table()->withBody($data['ord_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->showTaxcode(false)
				->addTotalPage()
				->generate($filename, $output);
	}
}

/** Cette fonction permet d'initialiser la devises.
 * 	@param array $data Obligatoire, information sur la commande
 * 	@return empty
 */
function export_order_excel_set_currency( &$data ){
	$data['currency'] = '€';

	if( is_array( $data['ord'] ) ){
		if( isset( $data['ord']['id'] ) && isset( $data['ord']['currency'] ) ){
			$currency = $data['ord']['currency'];
		}
	}

	if( !isset( $currency ) && is_array( $usr = $data['user'] ) && ria_mysql_num_rows( $r_prc = prd_prices_categories_get( isset( $usr['prc_id'] ) ? $usr['prc_id'] : 0 ) ) ){
		$prc = ria_mysql_fetch_assoc( $r_prc );

		if( is_array( $prc ) && isset( $prc['money_code'] ) ){
			$currency = $prc['money_code'];
		}
	}

	switch( strtoupper( isset( $currency ) ? $currency : 'EUR' ) ){
		case 'GBP':
			$data['currency'] = '£';
			break;
		case 'CHF':
			$data['currency'] = 'CHF';
			break;
		case 'XPF':
			$data['currency'] = 'XPF';
			break;
		case 'FCFP':
			$data['currency'] = 'FCFP';
			break;
		case 'USD':
			$data['currency'] = '$';
			break;
		default:
			$data['currency'] = '€';
			break;
	}
}

/**
 * Export Excel de la commande
 * @see export_order
 *
 * @param array $data Données de la commande
 * @param array|null $options Options
 *
 * @return mixed Export Excel (dépend de l'option ouput)
 */
function export_order_excel( array & $data, array & $options = null ){
	global $config;

	require_once('excel/PHPExcel.php');
	require_once('excel/PHPExcel/IOFactory.php');

	// Export spécifique aux client
	switch( $config['tnt_id'] ){
		case 395: //Corep
			include include('Pdf/custom/CorepOrderExcel.inc.php');
			break;
		case 1053: //Saint Bernard
			include include('Pdf/custom/SaintBernardOrderExcel.inc.php');
			break;
		case 268: // Zolux
			include('Pdf/custom/ZoluxOrderExcel.inc.php');
			break;
		case 1317 : // Zolux Es 
			include('Pdf/custom/ZoluxEsOrderExcel.inc.php');
			break;
		case 1118: // Francodex
			include('Pdf/custom/FrancodexOrderExcel.inc.php');
			break;
		case 447:
			include('Pdf/custom/MeliconiOrderExcel.inc.php');
			break;
		default :
			ob_start();

			// Gestion de l'arrondi des tarifs à zéro chiffre après la virgule
			if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
				$data['ord']['total_ht'] = round( $data['ord']['total_ht'] );
				$data['ord']['total_ttc'] = round( $data['ord']['total_ttc'] );

				if( isset($data['ord_products']) && is_array($data['ord_products']) ){
					foreach( $data['ord_products'] as &$d ){
						$d['price_ht'] = round( $d['price_ht'] );
						$d['price_ttc'] = round( $d['price_ttc'] );
						$d['total_ht'] = round( $d['total_ht'] );
						$d['total_ttc'] = round( $d['total_ttc'] );
						$d['ecotaxe'] = round( $d['ecotaxe'] );
					}
				}
			}

			// Initialisation de la devise
			export_order_excel_set_currency( $data );

			$data['file_sign'] = export_order_create_sign( $data['ord']['id'] );

			$doc = new PHPExcel();

			$first_line = 5;
			$sheet = $doc->getActiveSheet();
			$sheet->getDefaultStyle()->applyFromArray(export_order_excel_default_style());
			$sheet->getDefaultColumnDimension()->setWidth(18);

			list($last_logo_col, $last_logo_row) = export_order_excel_logo($sheet, $data, $options, 5, 1);
			list($last_owner_col, $last_owner_row) = export_order_excel_owner($sheet, $data, $options, 0, 1);
			list($last_adr_col, $last_adr_row) = export_order_excel_addresses($sheet, $data, $options, 5, 0, $last_owner_row + 2);
			list($last_header_col, $last_header_row) = export_order_excel_header($sheet, $data, $options, 0, $last_adr_row);

			list($last_user_info_col, $last_user_info_row) = export_order_excel_user_info($sheet, $data, $options, 0, $last_header_row + 2);
			list($last_products_col, $last_products_row) = export_order_excel_product_table($sheet, $data, $options, 0, $last_user_info_row + 2);
			list($last_taxes_col, $last_taxes_row) = export_order_excel_taxes($sheet, $data, $options, 0, $last_products_row + 2);
			$totals_first_col = max($last_taxes_col + 1, 5);
			list($last_totals_col, $last_totals_row) = export_order_excel_totals($sheet, $data, $options, $totals_first_col, $last_products_row + 2);
			list($last_payments_col, $last_payments_row) = export_order_excel_payments($sheet, $data, $options, $totals_first_col, $last_totals_row + 1);

			list($last_footer_col, $last_footer_row) = export_order_excel_footer($sheet, $data, $options, 0, $last_payments_row+ 2);

			$last_bank_details_col = 0;
			$last_bank_details_row = 1;
			if( $data['ord']['pay_id'] == _PAY_VIREMENT ){
				list($last_bank_details_col, $last_bank_details_row) = export_order_excel_bank_details($sheet, $data, $options, 0, $last_footer_row + 2);
			}

			$max_col = max(
				$last_logo_col,
				$last_owner_col,
				$last_adr_col,
				$last_header_col,
				$last_user_info_col,
				$last_products_col,
				$last_taxes_col,
				$last_totals_col,
				$last_payments_col,
				$last_footer_col,
				$last_bank_details_col
			) + 1;

			$max_row = max(
				$last_logo_row,
				$last_owner_row,
				$last_adr_row,
				$last_header_row,
				$last_user_info_row,
				$last_products_row,
				$last_taxes_row,
				$last_totals_row,
				$last_payments_row,
				$last_footer_row,
				$last_bank_details_row
			) + 1;

			$sheet->getPageSetup()->setPrintAreaByColumnAndRow(0, 1, $max_col, $max_row);
			$sheet->getPageMargins()
				->setLeft(0.5)
				->setRight(0.5);

			$sheet->setShowGridlines(false);

			$output_writer = isset($options['output_writer']) ? $options['output_writer'] : 'Excel5';

			$writer = PHPExcel_IOFactory::createWriter($doc, $output_writer);

			switch ($output_writer) {
				case 'CSV':
					$writerExtension = '.csv';
					break;
				case 'Excel5':
					$writerExtension = '.xls';
					break;
				case 'PDF':
					$writerExtension = '.pdf';
					break;
				case 'HTML':
					$writerExtension = '.html';
					break;
				case 'Serialized':
					$writerExtension = '.txt';
					break;
				case 'Excel2007':
				default:
					$writerExtension = '.xlsx';
					break;
			}

			$output = isset($options['output']) && trim($options['output']) != '' ? $options['output'] : 'D';
			$filename = isset($options['filename']) && trim($options['filename']) != '' ? $options['filename'] : 'order' . $writerExtension;

			if ($output == 'D' || $output == 'I') {
				try {
					// Charge le contenu du fichier Excel dans une variable temporaire
					// afin de supprimer le fichier image de la signature
					ob_start();
					$writer->save('php://output');
					$content = ob_get_clean();

					// La signature est tout de suite supprimée après utilisation
					if( file_exists($data['file_sign']) ){
						unlink($data['file_sign']);
					}

					// Charge le contenu du fichier Excel
					header('Content-Type: application/vnd.ms-excel');
					header('Content-Disposition: attachment;filename="'.$filename.'"');
					print $content;
					exit;
				}catch( \Exception $e ){
					throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
				}
			}else{
				//Save to local file
				if (!isset($options['filename'])) {
					throw new OrderExportException($data['ord']['id'], 'Impossible d\'écrire le fichier.');
				}

				try {
					$writer->save($options['filename']);
					// La signature est tout de suite supprimée après utilisation
					if( file_exists($data['file_sign']) ){
						unlink($data['file_sign']);
					}
				}catch( \Exception $e ){
					throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
				}
			}
			break;
	}
}

/**
 * Export Excel de la commande : logo
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_logo( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	if (!isset($options['logo']) || intval($options['logo']) <= 0) {
		return array($col, $row);
	}

	global $config;
	$r_img = img_images_get($options['logo']);
	if( $r_img && ria_mysql_num_rows($r_img) ){
		$img = ria_mysql_fetch_assoc($r_img);
		if( @file_exists($config['img_dir'].'/source/'.$img['id'].'-'.$img['src_name'].'.'.$img['type']) ){
			$data['logo'] = $config['img_dir'].'/source/'.$img['id'].'-'.$img['src_name'].'.'.$img['type'];
			$objDrawing = new PHPExcel_Worksheet_Drawing();

			$objDrawing->setPath($data['logo']);
			$objDrawing->setHeight(80);
			$objDrawing->setWorksheet($sheet);
			$objDrawing->setCoordinates( $sheet->getCellByColumnAndRow($col, $row)->getCoordinate());
		}
	}

	return array($col, $row);
}

/**
 * Export Excel de la commande : style par défaut des cellules
 *
 * @return array Style par défaut
 */
function export_order_excel_default_style( ){
	return array(
		'font'  => array(
			'bold'  => false,
			'size'  => 9,
			'name'  => 'Arial'
		)
	);
}

/**
 * Export Excel de la commande : bordures
 *
 * @return array Style des bordures fines
 */
function export_order_excel_thin_borders( ){
	return array(
		'borders' => array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
}

/**
 * Export Excel de la commande : propriétaire du site
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_owner( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		$data['owner']['name'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray(array(
		'font'  => array(
			'bold'  => true,
			'size'  => 12,
			'name'  => 'Arial'
		)
	));

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		$data['owner']['address1'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		$data['owner']['zipcode'] . ' ' . $data['owner']['city'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'N° Siret : ',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		$data['owner']['inscription'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	if ($data['owner']['capital'] != ""){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			$data['owner']['inscription'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 1,
			$row,
			number_format($data['owner']['capital'], 0, ',', ' ').' '.$data['currency'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	if ($data['owner']['naf'] != ""){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			'N.A.F. : ',
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 1,
			$row,
			$data['owner']['naf'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'N° intracommunautaire : ',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col+1,
		$row,
		$data['owner']['taxcode'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'Téléphone : ',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		$data['owner']['phone'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	if( $data['owner']['fax'] != '' ) {
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			'Télécopie : ',
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col+1,
			$row,
			$data['owner']['fax'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'Email : ',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		$data['owner']['email'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$var_code = 'pdf_generation_devis_url';
	$display_site_section = true;

	// Si la variable de config existe est n'est pas vide alors nous l'affichons en tant que lien du site.
	if( isset($config[$var_code]) && trim($var_code) ){
		$site_url = trim($config[$var_code]);
	} else {
		// Nous nous assurons que le "site_url" ne contienne pas les mots "maquettes" ou "preprod"
		// Ceci est une sécurité supplémentaire pour être sur de ne pas afficher un site de développement au client.
		if( strpos($config['site_url'], 'maquettes') === false && strpos($config['site_url'], 'preprod') === false ){
			$site_url = $config['site_url'];
		} else {
			// Si aucun lien n'est valide, alors il ne faut pas montrer la section concernant le site.
			$display_site_section = false;
		}
	}

	if( $display_site_section ){
		$sheet->setCellValueExplicitByColumnAndRow(
				$col,
				++$row,
				utf8_decode('Site : '),
				PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 1,
			$row,
			utf8_decode($site_url),
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	return array($col + 1, $row);
}

/**
 * Export Excel de la commande : adresses de livraison et de facturation
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_addresses( & $sheet, array & $data, array & $options = null, $col_inv = 10, $col_dlv = 0, $row = 0 ){
	$display_dlv_address = false;
	if ($options['display_dlv_address']
		&& isset($data['addresses'])
		&& isset($data['addresses']['delivery'])
		&& !is_null($data['addresses']['delivery'])
	){
		$display_dlv_address = true;
	}

	$max_col_inv = $col_inv;
	$max_row_inv = $row;
	$max_col_dlv = $col_dlv;
	$max_row_dlv = $row;
	if (isset($data['addresses'])
		&& isset($data['addresses']['invoice'])
		&& !is_null($data['addresses']['invoice'])
	) {
		list($max_col_inv,$max_row_inv) = export_order_excel_address($sheet, 'facturation', $data['addresses']['invoice'], $options, $col_inv, $row);
	}

	if( $display_dlv_address ){
		list($max_col_dlv,$max_row_dlv) = export_order_excel_address($sheet, 'livraison', $data['addresses']['delivery'], $options, $col_dlv, $row);
	}

	return array(max($max_col_inv, $max_col_dlv), max($max_row_inv, $max_row_dlv));
}

/**
 * Export Excel de la commande : addresse
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_address( & $sheet, $type, array & $address, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'Adresse de ' . $type,
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$style = export_order_excel_default_style();
	$style['font']['bold'] = true;
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	if (trim($address['society'])) {
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			$address['society'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		$address['firstname'] . ' ' . $address['lastname'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		($row += 2),
		$address['address1'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	if( trim($address['address2']) ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			$address['address2'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		$address['postal_code'] . ' ' . $address['city'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	return array($col+1, $row);
}

/**
 * Export Excel de la commande : header
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_header( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	if( trim($options['header']) != '' ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			$options['header_content'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	$style = export_order_excel_default_style();
	$style['font']['size'] = 20;
	$style['font']['bold'] = true;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'Devis',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	return array($col + 3, $row);
}

/**
 * Export Excel de la commande : footer
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_footer( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	if (trim($options['footer']) != '') {
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			++$row,
			$options['footer_content'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'Pour le client,'."\n".'Signature précédée de la mention'."\n".'"Lu et Approuvé, Bon pour Accord"',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	if( file_exists($data['file_sign']) ){
		$objDrawing = new PHPExcel_Worksheet_Drawing();
		$objDrawing->setPath( $data['file_sign'] );
		$objDrawing->setWidth(200);
		$objDrawing->setHeight(200);
		$objDrawing->setWorksheet($sheet);
		$objDrawing->setCoordinates($sheet->getCellByColumnAndRow($col, ++$row)->getCoordinate());
		$sheet->getRowDimension($row)->setRowHeight(200);
	}

	return array($col + 3, $row);
}

/**
 * Export Excel de la commande : références de la commande
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_user_info( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	$style = export_order_excel_default_style();
	$style['font']['bold'] = true;
	$borders = export_order_excel_thin_borders();

	$style = array_merge($style, $borders);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'DEVIS N°',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		'DATE',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		'REFERENCE DEVIS',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		'PAGE N°',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style);

	if( trim($data['user']['ref']) != '' ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 4,
			$row,
			'CLIENT',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col + 4, $row)->applyFromArray($style);
	}

	$row++;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		$data['ord']['piece'] != "" ? $data['ord']['piece'] : $data['ord']['id'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($borders);

	$date = new \DateTime($data['ord']['date']);
	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		$date->format('d/m/y H:i'),
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($borders);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		$data['ord']['ref'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($borders);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		1,
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($borders);

	if( trim($data['user']['ref']) != '' ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 4,
			$row,
			$data['user']['ref'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col + 4, $row)->applyFromArray($borders);
	}

	return array($col + 3, $row);
}

/**
 * Export Excel de la commande : liste des produits
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_product_table( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	$current_col = $col;
	$show_prd_img = isset($options['prd_img']) && !is_null($options['prd_img']) && isset($config['img_sizes']['small']);

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);

	$style_body = $style;
	if (isset($options['font_size']) && $options['font_size']) {
		$style_body = array_merge(
			$style_body,
			array(
				'font'=>array('size' => $options['font_size']),
				'alignment' => array('vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER)
			)
		);
	}

	$style['font']['size'] = 7;
	$style['font']['bold'] = true;

	if( $show_prd_img ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			'Image',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		$current_col++;
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$current_col,
		$row,
		'Référence',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Désignation',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style);
	$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Qté',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'P.U. HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Eco-part.',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	if (isset($options['prd_reduce']) && $options['prd_reduce']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Remise',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Montant HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Code EAN',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$row++;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	foreach ($data['ord_products'] as & $p) {
		$current_col = $col;
		$fields = '';

		if( isset($p['fields']) && is_array($p['fields']) && count($p['fields']) ){

			foreach($p['fields'] as $name => $val){
				$fields .= "\r".$name.' : '.$val;
			}

		}

		if( $show_prd_img){
			if ($p['img_id']
				&& file_exists($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format'])
			) {
				$objDrawing = new PHPExcel_Worksheet_Drawing();
				$objDrawing->setPath($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format']);
				$objDrawing->setWidth($config['img_sizes']['small']['width']);
				$objDrawing->setHeight($config['img_sizes']['small']['height']);
				$objDrawing->setWorksheet($sheet);
				$objDrawing->setCoordinates($sheet->getCellByColumnAndRow($current_col, $row)->getCoordinate());
			}

			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
			$sheet->getRowDimension($row)->setRowHeight($config['img_sizes']['small']['height']);
			$current_col++;
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			$p['id'] == 0 ? '' : $p['ref'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['name'].$fields,
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style_body);
		$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : floatval($p['qte']),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$price_ht = $p['price_ht'];
		if (isset($options['prd_reduce']) && $options['prd_reduce']) {
			if (isset($p['price_brut_ht'])) {
				$price_ht = $p['price_brut_ht'];
			}else{
				if ($p['discount'] > 0){
					if ($p['discount_type'] === "0"){ // Euros
						$price_ht = $p['price_ht'] + $p['discount'];
					} else { // %
						$price_ht = ($p['price_ht'] * 100 ) / (100 - $p['discount']);
					}
				}
			}

			// Charge la remise depuis le champ avancé Yuto prévu à cet effet
			$fld_discount = fld_object_values_get([$p['ord_id'], $p['id'], $p['line']], _FLD_ORD_LINE_DISCOUNT, '', false, true );
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$price_ht = ($p['price_ht'] * 100 ) / (100 - $fld_discount);
			}
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] ==0 ? null : round($price_ht, $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$p['id'] == 0 ? null : round($p['ecotaxe'], $decimals),
				PHPExcel_Cell_DataType::TYPE_NUMERIC
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		if (isset($options['prd_reduce']) && $options['prd_reduce']) {
			$remise = 0;
			if (isset($p['price_brut_ht'])) {
				$remise = round(100 - ($p['price_ht'] * 100 / $p['price_brut_ht']), 0);
			}

			if ($p['discount'] > 0){
				if ($p['discount_type'] === "0"){ // Euros
					$remise = floatval(round($p['discount'], $decimals)).' '.$data['currency'];
				} else { // %
					$remise = floatval(round($p['discount'], 0)).' %';
				}
			}

			// Charge la remise depuis le champ avancé Yuto prévu à cet effet
			$fld_discount = fld_object_values_get([$p['ord_id'], $p['id'], $p['line']], _FLD_ORD_LINE_DISCOUNT, '', false, true );
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$remise = floatval( str_replace('.00', '', round($fld_discount, 2))).' %';
			}

			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$remise == 0 ? null : str_replace('.', ',', $remise),
				PHPExcel_Cell_DataType::TYPE_STRING
			);

			$style = [ 'font' => [ 'size' => 8 ], 'borders' => [ 'allborders' => [ 'style' => 'thin' ] ],'alignment' => [ 'horizontal' => 'right', 'vertical' => 'center' ] ];
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : round($p['discount_type'] === "0" ? $p['total_ht'] - $p['discount'] : $p['total_ht'] * (1-($p['discount']/100)), $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				prd_products_get_barcode($p['id']),
				PHPExcel_Cell_DataType::TYPE_STRING
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		$row++;
	}

	return array($current_col, $row);
}

/**
 * Export Excel de la commande : liste des taxes
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_taxes( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0){
  global $config;

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);
	$style_body = $style;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	$style['font']['bold'] = true;
	$style['font']['size'] = 8;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'CODE',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		'BASE',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		'TAUX',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		'MONTANT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style);

	$row++;
	$total = 0;
	foreach ($data['tva'] as $rate => $tva) {
		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			$row,
			'Tva',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 1,
			$row,
			number_format($tva['base'], $decimals, ',', ' ').' '.$data['currency'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 2,
			$row,
			round(($rate - 1) * 100, $decimals) . ' %',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 3,
			$row,
			number_format($tva['amount'], $decimals, ',', ' ').' '.$data['currency'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style_body);

		$total += $tva['amount'];
		$row++;
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'Eco',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		number_format($data['ecotaxe']['base'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);

	$rate = 0;
	if ($data['ecotaxe']['base'] > 0) {
		$rate = round( $data['ecotaxe']['amount'] * 100 / $data['ecotaxe']['base'], 2);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		$rate. ' %' ,
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		number_format($data['ecotaxe']['amount'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style_body);

	$total += $data['ecotaxe']['amount'];

	$row++;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'TOTAL :',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		'Tva+Eco-participation',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);

	$sheet->mergeCellsByColumnAndRow($col + 1, $row, $col + 2, $row);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		number_format($total, $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style_body);

	return array($col + 3, $row);
}

/**
 * Export Excel de la commande : totaux
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_totals( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
  global $config;

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);
	$style_body = $style;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	$style['font']['bold'] = true;
	$style['font']['size'] = 8;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'TOTAL H.T.',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		'TOTAL T.T.C.',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		'NET A PAYER',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		number_format($data['ord']['total_ht'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		number_format($data['ord']['total_ttc'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		number_format($data['ord']['total_ttc'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);

	return array($col + 2, $row);
}

/**
 * Export Excel de la commande : échéancier + conditions de paiement
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_payments( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	if (!isset($options['display_payment']) || !$options['display_payment'] || trim($data['pay_name']) == '') {
		return array($col, $row);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'Conditions de règlement :',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		$data['pay_name'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);


	$length = isset($data['installments']) && is_array($data['installments']) ? count($data['installments']) : 0;
	if( $length <= 0 ){
		return array($col, $row);
	}

	foreach ($data['installments'] as $i => $installement) {
		$row++;
		$date = new \DateTime($installement['expire']);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col,
			$row,
			'le ' . $date->format('d/m/Y'),
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 1,
			$row,
			$installement['pay_name'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->setCellValueExplicitByColumnAndRow(
			$col + 2,
			$row,
			ria_number_french($installement['rest']),
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}

	return array($col + 2, $row);
}

/**
 * Export Excel de la commande : informations bancaires
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function export_order_excel_bank_details( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	if( !isset($data['bank_details']) || is_null($data['bank_details']) ){
		return array($col, $row);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'IBAN: ' . $data['bank_details']['iban'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		'BIC: ' . $data['bank_details']['bic'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		$data['bank_details']['name']
			. ' - ' . $data['bank_details']['agency_name']
			. ' - ' . $data['bank_details']['address1']
			. ' ' . $data['bank_details']['address2']
			. ' - ' . $data['bank_details']['zipcode']
			. ' ' . $data['bank_details']['city'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	return array($col, $row);
}

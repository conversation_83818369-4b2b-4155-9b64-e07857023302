<?php
// \cond onlyria

/**	\defgroup codial Codial
 *	\ingroup synchro
 *	Ce module comprend les fonctions nécessaires à la communication avec la gestion commerciale Codial, éditée par Saytec.
 *	@{
 */

// boissinot token : 41659da28d91fd450715ee81b90d8
require_once ('strings.inc.php');
require_once('imports.inc.php');

define('CODIAL_FTP_DIR', $config['site_dir'].'/../ftp-codial/');
define('CODIAL_IMP_DIR', $config['site_dir'].'/../sync-codial/');
define('CODIAL_IMP_PRD_ID', $config['codial_imp_prd']);
define('CODIAL_IMP_STK_ID', $config['codial_imp_stock']);
define('CODIAL_IMP_CAT_ID', $config['codial_imp_cat']);
define('CODIAL_IMP_PRICE_ID', $config['codial_imp_price']);
define('CODIAL_IMP_FACTURE', 1);
define('CODIAL_IMP_ORD_STATE', 2);

/**	Cette fonction permet la vérification du droit d'accès d'un token d'auhentification aux fonctions Codial
 */
function codial_identify(){
	if( !isset($_GET['token']) || !trim($_GET['token']) ){
		header("HTTP/1.1 401 Unauthorized");
		exit;
	}
	$authorized = tnt_tenants_is_authorized($_GET['token']);
	if ($authorized['error']) {
		header("HTTP/1.1 401 Unauthorized");
		exit;
	}
}

/** Cette fonction permet de générer les fichier CSV d'export de commande
 */
function codial_generate_orders_export(){
	codial_identify();
	global $config;

	$rOrders = ord_orders_get_new_to_import(false, ord_states_get_ord_valid( false, true ));

	$first = true;

	if( !$rOrders || !ria_mysql_num_rows($rOrders) ){
		echo 'OrderID'.PHP_EOL;
		echo 'Aucune commande à exporter'.PHP_EOL;
		return false;
	}

	$filename = 'export_cmd_codial_'. date('Ymd_Hi'). '.csv';

	$handle = fopen(CODIAL_FTP_DIR.$filename, 'w');

	while( $ord = ria_mysql_fetch_assoc($rOrders) ){

		$rOrder = ord_orders_get_with_adresses( 0, $ord['id']);

		if( !$rOrder || !ria_mysql_num_rows($rOrder) ){
			continue;
		}

		$order = ria_mysql_fetch_assoc($rOrder);

		if( $first ){
			$datefile = new DateTime($order['date_en']);
			$first = false;
		}

		$shippingport = ord_orders_port_get( $order['id'], true );

		$dlv_type = dlv_services_get_name($order['srv_id']);

		if( !$dlv_type ){
			$dlv_type = dlv_stores_get_name( $order['str_id']);
		}

		$user_id = $order['user'];
		$user_email = gu_users_get_email($user_id);
		if (isset($config['codial_orders_user_id']) && trim($config['codial_orders_user_id']) != "") {
			$user_id = $config['codial_orders_user_id'];
		}

		$coupon_code = '';
		$coupon_discount = '';
		if( $order['pmt_id'] ){
			$r_code = pmt_codes_get($order['pmt_id'], null, false, _PMT_TYPE_CODE);
			if( ria_mysql_num_rows($r_code) ){
				$code = ria_mysql_fetch_assoc($r_code);
				$coupon_code = $code['code'];

				$r_total = ord_orders_calculate_totals($order['id']);
				$total_ttc = ord_orders_get_total( $order['id'], true);
				$order_total = ria_mysql_fetch_assoc($r_total);
				$coupon_discount = $order_total['total_ttc'] - $total_ttc;
			}
		}

		$pay_type = ord_payment_types_get_name( $order['pay_id'] );
		$inv_code = sys_countries_get_code($order['inv_country']);
		$dlv_code = sys_countries_get_code($order['dlv_country']);
		$order_line = array(
			'['.$config['codial_cmd_constant'].']',
			'[ORDERS]',
			$order['id'],
			$user_id,
			'',
			$order['inv_society'],
			$order['inv_lastname'],
			$order['inv_firstname'],
			$order['inv_address1'],
			$order['inv_city'],
			$order['inv_postal_code'],
			$inv_code == 'FR' ? 'FRA' : $inv_code,
			$user_email,
			$order['inv_phone'],
			$order['dlv_society'],
			$order['dlv_lastname'],
			$order['dlv_firstname'],
			$order['dlv_address1'],
			$order['dlv_postal_code'],
			$order['dlv_city'],
			$dlv_code == 'FR' ? 'FRA' : $dlv_code,
			$order['total_ttc'],
			$pay_type,
			$order['comments'],
			$dlv_type,
			$order['date_livr_en'],
			$shippingport,
			$config['codial_cmd_constant'],
			$order['inv_address2'],
			$order['dlv_address2'],
			'',
			'',
			$coupon_code,
			$coupon_discount,
			'',
			(trim($order['dlv_mobile']) == '' ? $order['dlv_phone'] : $order['dlv_mobile'])
		);
		fputs($handle, implode(';', $order_line).PHP_EOL);

		$rPrds = ord_products_get( $order['id']);

		if( !$rPrds || !ria_mysql_num_rows($rPrds) ){
			continue;
		}
		$prds = array();

		while( $prd =ria_mysql_fetch_assoc($rPrds) ){
			if (prd_products_is_port_id($prd['id'])) {
				continue;
			}
			$prd_line = array(
				'['.$config['codial_cmd_constant'].']',
				'[OITEMS]',
				$prd['line'],
				$prd['ref'],
				$prd['id'],
				$prd['name'],
				$prd['real_qte'],
				$prd['price_ttc']
			);
			fputs($handle, implode(';', $prd_line).PHP_EOL);
			$prds[] = $prd;
		}

		foreach($prds as $prd){
			if (prd_products_is_port_id($prd['id'])) {
				continue;
			}
			$stock = prd_dps_stocks_get_sum( $prd['id'] );
			$rCat = prd_products_categories_get( $prd['id'], true, true);
			$cat1 = '';
			$cat2 = '';
			if( $rCat && ria_mysql_num_rows($rCat) ){
				while( $cat = ria_mysql_fetch_assoc($rCat) ){
					$cat2 = $cat['name'];
					$catParent = prd_categories_get_parent_id( $cat['cat'] );
					if( $catParent ){
						$cat1 = prd_categories_get_name( $catParent );
					}
				}
			}
			$prd_line = array(
				'['.$config['codial_cmd_constant'].']',
				'[PRODUCTS]',
				$prd['id'],
				$prd['ref'],
				$prd['name'],
				$cat1,
				$cat2,
				$prd['price_ttc'],
				$stock['qte'],
				$prd['brd_name'],
				$prd['weight_net_total']
			);
			fputs($handle, implode(';', $prd_line).PHP_EOL);
		}
		ord_orders_piece_set( $ord['id'], 'W'.$ord['id'], false, false, false, false );
	}
	fclose($handle);
	echo 'OrderID'.PHP_EOL;
	if( isset($datefile) ){
		copy(CODIAL_FTP_DIR.$filename, CODIAL_IMP_DIR.'archives/EXPORT_CMD_'.$datefile->format('Ymd_His').'.csv');
	}else{
		unlink(CODIAL_FTP_DIR.$filename);
		echo 'Aucune commande à exporter'.PHP_EOL;
	}
	exit;
}

/** Cette fonction permet de récupérer et d'ajouter à la workqueue le fichier passé en paramètre
*/
function codial_import_files(){
	// codial_identify();

	global $config;

	$file = '';

	if( isset($_GET['filename']) ){
		$file = $_GET['filename'];
	}elseif( isset($_GET['xml']) ){
		$file = $_GET['xml'];
	}

	if( !file_exists(CODIAL_FTP_DIR.$file) ){
		return false;
	}

	$import = false;

	if( preg_match("/EXPORT_ART/", $file, $matches) ){
		$import = CODIAL_IMP_PRD_ID;
	}elseif( preg_match("/EXPORT_CAT/", $file, $matches) ){
		$import = CODIAL_IMP_CAT_ID;
	}elseif( preg_match("/EXPORT_STOCK/", $file, $matches) ){
		$import = CODIAL_IMP_STK_ID;
	}elseif( preg_match("/EXPORT_PRIX/", $file, $matches) ){
		$import = CODIAL_IMP_PRICE_ID;
	}elseif( preg_match("/EXPORT_FAC/", $file, $matches) ){
		$import = CODIAL_IMP_FACTURE;
	}elseif( preg_match("/CMD_ETAT/", $file, $matches)) {
		$import = CODIAL_IMP_ORD_STATE;
	}

	$extention = pathinfo($file, PATHINFO_EXTENSION);


	switch( $import ){
		case CODIAL_IMP_PRD_ID:{
			$xml = simplexml_load_file(CODIAL_FTP_DIR.$file);

			$filename = pathinfo($file, PATHINFO_FILENAME);
			$filecsv = $filename.'_'.date('YmdHis').'.csv';

			$pending_file = CODIAL_IMP_DIR.'pending/'.$filecsv;

			$handle = fopen($pending_file, 'w');


			$articles_headers = array(
				'clanguage',
				'ccode',
				'cname',
				'productvat',
				'marque',
				'design_courte',
				'prixAchat',
				'weight',
				'SousCat',
				'productmatch',
				'cprice',
				'cstock',
				'retailprice',
				'hide'
			);
			fputcsv($handle, $articles_headers, ';');
			foreach ($xml->children() as $prd)	{
				$csv_line = array();
				$current_price = 0;
				foreach ($articles_headers as $i => $head) {
					$val = '';
					foreach( $prd->children() as $key => $value) {
						if ($head == $key) {
							if ($value instanceof SimpleXMLElement) {
								$value = $value->__toString();
							}

							$val = $value;
							if ($head == 'cprice'){
								$current_price = $val;
							}

							if ($head == 'retailprice' && $val != 0 && $val > $current_price) {
								$csv_line[10] = $val;
								$val = $current_price;
							}
						}
					}
					$csv_line[$i] = $val;
				}
				fputcsv($handle, $csv_line, ';');
			}

			// ria_convert_xml_to_csv( $xml, $handle, $headers, $cols, false, array() );

			fclose($handle);
			// $pending_file = codial_fix_csv($articles_headers, $filecsv);

			break;
		}
		case CODIAL_IMP_CAT_ID:{
			$xml = simplexml_load_file(CODIAL_FTP_DIR.$file);

			$filename = pathinfo($file, PATHINFO_FILENAME);
			$filecsv = $filename.'_'.date('YmdHis').'.csv';

			$pending_file = CODIAL_IMP_DIR.'pending/tmp-'.$filecsv;

			$handle = fopen($pending_file, 'w' );

			$headers = array();
			$cols= array();

			ria_convert_xml_to_csv( $xml, $handle, $headers, $cols, false, array() );

			fclose($handle);
			$cat_headers = array(
				'categoryid',
				'Categorie',
				'highercategoryid',
				'hassubcategory',
				'Image',
				'Sel',
				'Cache',
				'Site',
				'Sous-famille'
				);
			$pending_file = codial_fix_csv($cat_headers, $filecsv);
			break;
		}
		case CODIAL_IMP_STK_ID:{
			$filename = pathinfo($file, PATHINFO_FILENAME);
			$filecsv = $filename.'_'.date('YmdHis').'.csv';
			$filecsv2 = $filename.'_purchase_price_'.date('YmdHis').'.csv';

			$pending_file = CODIAL_IMP_DIR.'pending/tmp-'.$filecsv;
			$pending_file2 = CODIAL_IMP_DIR.'pending/tmp-'.$filecsv2;

			$handle = fopen(CODIAL_FTP_DIR.$file, "r");
			$handle_write = fopen($pending_file, "w");
			$handle_write2 = fopen($pending_file2, "w");

			$headers = fgetcsv($handle, null, ';');

			array_unshift($headers, "depots");

			fputcsv($handle_write, $headers,';');
			fputcsv($handle_write2, $headers,';');

			while( $line = fgetcsv($handle, null, ';') ){
				array_unshift($line, $config['codial_main_dps']);
				fputcsv($handle_write, $line, ';');
				fputcsv($handle_write2, $line, ';');
			}
			fclose($handle);
			fclose($handle_write);
			fclose($handle_write2);

			$stk_headers = array(
				'depots',
				'ccode',
				'cstock'
			);
			$pending_file = codial_fix_csv($stk_headers, $filecsv);

			$stk_headers2 = array(
				'ccode',
				'prix_achat'
			);
			$pending_file2 = codial_fix_csv($stk_headers2, $filecsv2);
			tsk_imports_add( $config['codial_imp_purchase_price'], $pending_file2, false, true  );
			break;
		}
		case CODIAL_IMP_PRICE_ID: {
			$filename = pathinfo($file, PATHINFO_FILENAME);
			$handle = fopen(CODIAL_FTP_DIR.$file, 'r');
			$filecsv = $filename.'_'.date('YmdHis').'.csv';
			$pending_file = CODIAL_IMP_DIR.'pending/'.$filecsv;
			$h = fopen($pending_file, 'w');

			$first = true;
			while($line = fgetcsv($handle, 0, ';')) {
				if ($first) {
					fputcsv($h, $line, ';');
					$first = false;
					continue;
				}
				// le prix promo est toujours envoyé
				// le produit est en promotion si le cprice (line 1) est inférieur au prix promo (line 2)
				// les prix son donc inversé.
				// il faut donc passer la valeur de cprice dans en line 2
				$cprice = $line[1];
				$promo = $line[2];

				if ($cprice < $promo) { // cprice est le prix promo
					$line[1] = $promo;
					$line[2] = $cprice;
				}elseif ($promo == 0 || $cprice > $promo) {
					// on supprime un éventuel prix promo
					// et on met le prix promo dans le fichier à 0 pour qu'il ne soit pas importé
					$line[2] = 0;
					$prd_id = prd_products_get_id($line[0]);
					prc_prices_del( 0, 0, $prd_id, 0, true );
				}

				fputcsv($h, $line, ';');
			}
			fclose($handle);
			fclose($h);
			break;
		}
		case CODIAL_IMP_ORD_STATE: {
			$filename = pathinfo($file, PATHINFO_FILENAME);
			$handle = fopen(CODIAL_FTP_DIR.$file, 'r');
			$first = true;
			while ($state = fgetcsv($handle, 0, ';')) {
				if ($first) {
					$first = false;
					continue;
				}

				$ord_id = $state[0]; // id commande riashop
				$shipped = $state[1]; // si la commande est envoé ou non (création de bl)
				$ria_state = $state[2]; // identifiant du statut riashop actuelement 4 et 11
				$colis = $state[3]; // numéro du colis
				$piece = $state[4]; // pièce codial

				$r_order = ord_orders_get_simple(array('id' => $ord_id));
				if (!$r_order || !ria_mysql_num_rows($r_order)) {
					continue;
				}

				$order = ria_mysql_fetch_assoc($r_order);
				ord_orders_update_status($order['id'], trim($ria_state));
				ord_orders_piece_set( $order['id'], $piece, false, false, false, false);
				$order['piece'] = $piece;
				if ($shipped == '1') {
					// création du bl si colis expédié
					$bl = ord_bl_add_sage($order['usr_id'], $order['piece'], $order['ref'], date('Y-m-d'), _STATE_BL_EXP, 0, $order['id']);
					if ($bl) {
						$r_p = ord_products_get($order['id']);
						if ($r_p && ria_mysql_num_rows($r_p)) {
							while($prd = ria_mysql_fetch_assoc($r_p)) {
								ord_bl_products_add_sage($bl, $prd['id'], $prd['line'], $prd['ref'], $prd['name'], $prd['qte'], $prd['price_ht'], $prd['tva_rate'], $order['id'], $colis);
							}
						}
						ord_bl_notify( $bl, true);
					}
				}

			}
			fclose($handle);
			$info = pathinfo($file);
			copy(CODIAL_FTP_DIR.$file, CODIAL_IMP_DIR.'archives/'.$info['filename'].'_'.date('YmdHis').'.'.$info['extension']);
			return true;
			break;
		}
		case CODIAL_IMP_FACTURE:
			$xml = simplexml_load_file(CODIAL_FTP_DIR.$file);
			foreach( $xml->children() as $child ){
				if( !isset($child->NomFacture) ){
					continue;
				}
				$pdfFile = $child->NomFacture->__toString();

				if( !file_exists(CODIAL_FTP_DIR.$pdfFile) ){
					continue;
				}

				$ord_id = $child->NumComSite->__toString();
				ord_orders_state_update($ord_id, _STATE_INVOICE);
				$filename = pathinfo($pdfFile, PATHINFO_FILENAME);
				$pdf = $filename.'_'.$ord_id.'.pdf';
				if( isset($config["fld_cmd_link"]) ){
					fld_object_values_set( $ord_id, $config["fld_cmd_link"], $pdf);
				}
				copy(CODIAL_FTP_DIR.$pdfFile, $config['site_dir'].'/documents/invoices/'.$pdf);
				copy(CODIAL_FTP_DIR.$pdfFile, CODIAL_IMP_DIR.'archives/'.$pdf);
				unlink(CODIAL_FTP_DIR.$pdfFile);
			}
			return true;
	}
	$info = pathinfo($file);
	copy(CODIAL_FTP_DIR.$file, CODIAL_IMP_DIR.'archives/'.$info['filename'].'_'.date('YmdHis').'.'.$info['extension']);
	tsk_imports_add( $import, $pending_file, false, true  );
}

/** Cette fonction permet de corriger l'ordre des colonnes dans le fichier CSV
 *	@param array $headers les headers dans l'ordre souhaité
 *	@param string $filecsv le nom du fichier CSV
 *
 * 	@return string le chemin vers le fichier final
 */
function codial_fix_csv( $headers, $filecsv ){
	if( !is_array($headers) || !count($headers) ){
		return false;
	}
	global $config;
	$tmp_file = CODIAL_IMP_DIR.'pending/tmp-'.$filecsv;

	$handle_tmp = fopen($tmp_file, 'r' );
	$handle_final = fopen(CODIAL_IMP_DIR.'pending/'.$filecsv, 'w' );

	$tmp_headers = fgetcsv($handle_tmp, null, ';');
	fputcsv($handle_final, $headers, ';');

	$indexes = array();

	foreach( $headers as $index => $header ){
		$key = array_search($header, $tmp_headers);
		$indexes[] = $key;
	}

	while($line = fgetcsv($handle_tmp, null, ';') ){
		$new_line = array();
		foreach( $indexes as $key => $index ) {
			if( $index === false || !array_key_exists($index, $line) ){
				$new_line[] = '';
			}else{
				if( $headers[$key] == 'highercategoryid' && $line[$index] == 0 && isset($config['cat_root']) ){
					$new_line[] = $config['cat_root'];
				}else{
					$new_line[] = $line[$index];
				}
			}
		}

		fputcsv($handle_final, $new_line, ';');
	}
	fclose($handle_tmp);
	fclose($handle_final);
	unlink($tmp_file);
	return CODIAL_IMP_DIR.'pending/'.$filecsv;
}

/// @}

// \endcond
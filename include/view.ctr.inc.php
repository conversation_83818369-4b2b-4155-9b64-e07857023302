<?php
// \cond onlyria

require_once('comparators.inc.php');
require_once('comparators/ctr.amazon.inc.php');
require_once('comparators/ctr.priceminister.inc.php');
require_once('comparators/ctr.cdiscount.inc.php');
require_once('comparators/ctr.ebay.inc.php');
require_once('products.inc.php');

/** \defgroup view_ctr Visualisation des comparateurs de prix
 *	\ingroup view_admin
 *	Ce module comprend des fonctions utiles à l'affichage des comparateurs de prix dans l'interface d'administration
 *	@{
 */

/** Cette fonction gère l'affichage des conditions de recherche en fonction de leur type (le type provenant de fld_types) :
 *		- Booléen (8) : Affiche des boutons radios (Oui, Non, Indifférent)
 *		- Date (10) : Affiche un sélecteur de date
 *		- Nombre entier (3) : Affiche un champ de type input text
 *		- Nombre à virgule flottante (4) : Affiche un champ de type input text
 *	@param $count Compteur de position (1er, 2ème, 3ème champ, etc...). Sous forme numérique.
 *	@param $code Code identifiant la condition (CTR_SEARCH_*)
 *	@param $val Optionnel, Valeur à pré-renseigner
 *	@param $sbl Optionnel, Symbôle à pré-renseigner (lorsque le champ est associé à plusieurs symbôles possibles)
 *	@param $all Optionnel
 *	@todo Documenter le paramètre $all dont l'utilité n'est pas claire
 *	@return string Le code HTML utilisé pour afficher les champs de recherche correspondants
 */
function view_ctr_conditions( $count, $code, $val=false, $sbl=false, $all=true ){

	$rcdt = ctr_conditions_get( 0, $code );
	if( !$rcdt || !ria_mysql_num_rows($rcdt) )
		return false;

	$cdt = ria_mysql_fetch_array( $rcdt );

	$html = '';
	$atrID = $code.'-'.$count;

	if( $all ){
		$class =  'class="name-cdt '.( $cdt['type']==8 ? 'name-cdt-radio options-radio' : '' ).'"';
		$html .= '	<div class="elem">';
		$html .= '		<label '.$class.' for="'.$atrID.'-y" title="'.$cdt['desc'].'">'.$cdt['name'].' :</label>';
	}

	$html .= '		<div class="options">';
	if( $cdt['code'] == 'CTR_SEARCH_CATEGORIE' ){
		if( $val ){
			$rcat = prd_categories_get( $val );
			$cat_name = '';
			if( $rcat && ria_mysql_num_rows( $rcat ) ){
				$cat = ria_mysql_fetch_array( $rcat );
				$cat_name = $cat['title'];
			}
		}
		$html .= '	<input type="hidden" name="cdts['.$code.'-'.$count.']" id="cdts-'.$atrID.'" value="'.( $val!==false ? $val : '' ).'" />';
		$html .= '	<input class="text search-cats" type="text" name="cdts-label['.$code.'-'.$count.']" id="cdts-label-'.$atrID.'" value="'.( $val!==false ? $cat_name : '' ).'" />';
	}elseif( $cdt['code'] == 'CTR_SEARCH_PRD_CONTENTS' ){
		$html .= '	<input class="text search-txt" type="text" name="cdts['.$code.'-'.$count.']" id="cdts-'.$atrID.'" value="'.( $val!==false ? $val : '' ).'" />';
	}else{
		switch( $cdt['type'] ){
			case 8 :
				$html .= '	<input class="radio" type="radio" name="cdts['.$code.']" id="'.$atrID.'-y" value="1" '.( $val!==false && $val=='1' ? 'checked="checked"' : '' ).' />';
				$html .= '	<label for="'.$atrID.'-y">'._('Oui').'</label>';
				$html .= '	<input class="radio" type="radio" name="cdts['.$code.']" id="'.$atrID.'-n" value="0" '.( $val!==false && $val=='0' ? 'checked="checked"' : '' ).' />';
				$html .= '	<label for="'.$atrID.'-n">'._('Non').'</label>';
				$html .= '	<input class="radio" type="radio" name="cdts['.$code.']" id="'.$atrID.'-i" value="-1" '.( $val!==false && $val=='-1' ? 'checked="checked"' : '' ).' />';
				$html .= '	<label for="'.$atrID.'-i">'._('Indifférent').'</label>';
				break;
			case 10 :
				$html .= '	<input class="text date datepicker" type="text" name="cdts['.$code.']" id="'.$atrID.'" value="" />';
				break;
			case 2 :
				if( $code == 'CTR_SEARCH_REFS' && $val ){
					$sh_refs = array();
					foreach( explode("\r\n",$val) as $line ){
						$lref = preg_split("/[\s\t]+/", trim($line));
						if( isset($lref[0]) ){
							$sh_refs[] = $lref[0];
						}
					}
					$dispo = prd_products_get_simple( 0, $sh_refs );
					$sh_refs = array();
					if( $dispo ){
						while( $d = ria_mysql_fetch_array($dispo) ){
							$sh_refs[]=$d['ref'];
						}
					}
					if( !sizeof($sh_refs) ){
						$val = false;
					}else{
						$val = implode("\n",$sh_refs);
					}
					$html .= '	<a id="code_'.$code.'-'.$count.'" class="show_popup" name="show_textarea">'.sizeof($sh_refs).' produit'.(sizeof($sh_refs)>1 ? 's':'').'</a>';
					$html .= '	<div id="textarea_'.$code.'-'.$count.'" class="textarea_popup_content">
									<div class="batch-content">
										<textarea class="text batch" name="cdts['.$code.'-'.$count.']" id="'.$atrID.'">'.( $val!==false ? htmlspecialchars($val) : '' ).'</textarea>
										<div class="actions">
											<input class="btn-action" type="button" name="update_textarea" value="Modifier"/>
										</div>
									</div>
								</div>';
				}
				else{
					$html .= '	<textarea class="text" name="cdts['.$code.'-'.$count.']" id="'.$atrID.'">'.( $val!==false ? htmlspecialchars($val) : '' ).'</textarea>';
				}
				break;
			default :
				$rsymbols = ctr_conditions_get_symbols( $cdt['id'] );
				if( $rsymbols && ria_mysql_num_rows($rsymbols) ){
					$html .= '	<select name="sbl['.$code.'-'.$count.']" id="sbl-'.$atrID.'">';
					while( $symbol = ria_mysql_fetch_array($rsymbols) ){
						if( $symbol['symbol']=='><' ){
							continue;
						}
						$html .= '	<option value="'.$symbol['symbol'].'" '.( $sbl && $sbl==$symbol['symbol'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $symbol['desc'] ).'</option>';
					}
					$html .= '	</select>';
					$html .= '	<input class="text" type="text" name="cdts['.$code.'-'.$count.']" id="cdts-'.$atrID.'" value="'.( $val!==false ? $val : '' ).'" />';
				}
				break;
		}
	}

	if( $cdt['type']==8 ){
		$html .= '		&nbsp;<a onclick="return delConditions( $(this) );" href="#" class="del"></a>';
	}
	$html .= '		</div>';

	if( $all ){
		$class =  'class="del name-cdt '.( $cdt['type']==8 ? 'name-cdt-radio' : '' ).'"';
		if( $cdt['type']!=8 ){
			$html .= '		&nbsp;<a '.$class.' onclick="return delConditions( $(this) );" href="#" class="del"></a>';
		}
		$html .= '		<div class="clear"></div>';
		$html .= '	</div>';
	}

	return $html;
}

/** Cette fonction permet d'afficher la liste des catégories d'un comparateur.
 *	@param $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $id Obligatoire, identifiant d'une catégorie
 *	@param $type Optionnel, affiche ou non une checkbox devant chaque nom de catégorie
 *	@param $total Optionnel, affiche ou non le nom de catégories produit rattachées à la catégorie du comparateur
 *	@return Retourne du code HTML sous forme de listes imbriquées de toutes les catégories (récursivement)
 */
function list_categories( $ctr, $id, $type="", $total=false ){
	$c = ria_mysql_fetch_array( ctr_categories_get($ctr, $id) );

	$r_child = ctr_categories_child_get( $id, $ctr );
	$childs = ctr_categories_childs_get_array( $id );
	$count_publish = ctr_categories_count($ctr, true);
	$published = ctr_categories_is_published( $id ) ? 1 : 0;

	$txt = '';
	$txt .= '<li id="li-cat-'.$c['id'].'">';

	if( $type == "checkbox" )
		$txt .= '<input type="checkbox" name="cat[]" id="cat-'.$c['id'].'" value="'.$c['id'].'" '.($childs ? 'onclick="childsCat('.$ctr.',['.implode(',',$childs).'],'.$c['id'].','.$c['parent'].',true);"' : '').' '.( !$published && $count_publish>0  ? 'checked="checked"' : '' ).' />';

	if( $total ){
		$count = ctr_prd_categories_count($c['ctr_id'], $c['id']);
		$txt .= '&nbsp; <label>
			'.xmlentities($c['name']).'<br />
			<span class="card-title-ctr">(dont '.($count>1 ? $count.' catégories rattachées' : $count.' catégorie rattachée')
			.')</span>
		</label>';
	} else {
			$txt .= '&nbsp; <label>'.xmlentities($c['name']).'</label>';
	}

	if( !ria_mysql_num_rows($r_child) ){
		$txt .= '</li>';
	} else {
		$txt .= '<ul id="subcat-'.$c['id'].'">';

		while( $child = ria_mysql_fetch_array($r_child) ){
			$txt .= list_categories($ctr, $child['id'], $type, $total);
		}

		$txt .= '</ul></li>';
	}

	return $txt;
}

/** Affiche une liste de produits exportés dans une catégorie d'un comparateur de prix
 *	@param $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $id Obligatoire, identifiant d'une catégorie
 *	@param $type Optionnel, par défaut ne fait rien, mettre 'checkbox' pour en affiche une demande chaque catégorie, mettre 'radio' pour avoir un bouton radio
 *	@return Retourne du code HTML sous la forme d'une liste à puce contenant la liste des produits
 */
function list_categories_prd( $ctr, $id, $type="" ){
	$c = ria_mysql_fetch_array( prd_categories_get($id,true,0,'',false) );
	$childs = prd_categories_childs_get_array($id, true);
	$r_child = prd_categories_get(0, true, $id,'',false);
	$cat_ctr = ria_mysql_fetch_array( ctr_prd_categories_get( $ctr, $c['id'] ) );

	if( $c['products']>0 ){

		$txt = '<li>';

		if( $type == "checkbox" )
			$txt .= '<input type="checkbox" name="cat[]" id="cat-'.$c['id'].'" value="'.$c['id'].'" '.($publish ? 'checked="checked"' : '').' />';
		else if ( $type == "radio" )
			$txt .= '<input type="radio" name="cat" id="cat-'.$c['id'].'" value="'.$c['id'].'" />';

		// Rattachement
		$link = '';
		if( ctr_prd_categories_is_publish($ctr,$c['id']) ){
			if( $cat_ctr=="" )
				$link = '<span class="card-title-ctr">(Exportée sans rattachement)</span>';
			else
				$link = '<span class="card-title-ctr">(rattachée à '.$cat_ctr['name'].')</span>';
		}

		$txt .= '&nbsp; <label for="cat-'.$c['id'].'" onclick="show_card('.$c['id'].','.$ctr.');" id="spanprd-'.$c['id'].'">'.( htmlspecialchars($c['name']) ).' '.$link.'</label>';

		if( sizeof($childs)>0 ){
			$txt .= '<ul id="subcatprd-'.$c['id'].'">';

			while( $child = ria_mysql_fetch_array($r_child) ){
				$txt .= list_categories_prd($ctr, $child['id'], $type);
			}

			$txt .= '</ul></li>';
		} else {
			$txt .= '</li>';
		}

	}

	return $txt;
}

/** Cette fonction affiche l'image dans l'entête général d'un comparateur de prix
 *	@param string $ctr_name Obligatoire, nom du comparateur de prix
 *	@return string Le code HTML de l'image
 */
function view_ctr_header_img( $ctr_name ){
	switch( $ctr_name ){
		case 'Amazon.fr':
			return '<img class="img-comparator" width="196" height="64" name="amazon" id="amazon" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/amazon.png" />';
		case 'Amazon.co.uk':
			return '<img class="img-comparator" width="196" height="64" name="amazon" id="amazon" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/amazon.co.uk.png" />';
		case 'Amazon.de':
			return '<img class="img-comparator" width="196" height="64" name="amazon" id="amazon" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/amazon.de.png" />';
		case 'Amazon.es':
			return '<img class="img-comparator" width="196" height="64" name="amazon" id="amazon" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/amazon.es.png" />';
		case 'Amazon.it':
			return '<img class="img-comparator" width="196" height="64" name="amazon" id="amazon" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/amazon.it.png" />';
		case 'Le Guide':
			return '<img class="img-comparator" width="196" height="64" name="leguide" id="leguide" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/leguide.png" />';
		case 'Cdiscount':
			return '<img class="img-comparator" width="196" height="64" name="cdiscount" id="cdiscount" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/cdiscount.png" />';
		case 'Kelkoo':
			return '<img class="img-comparator" width="196" height="64" name="kelkoo" id="kelkoo" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/kelkoo.png" />';
		case 'Google Shopping':
			return '<img class="img-comparator" width="196" height="64" name="google" id="google" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/google.png" />';
		case 'PriceMinister':
			return '<img class="img-comparator" width="196" height="64" name="priceminister" id="priceminister" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/priceminister.gif" />';
		case 'Shopping.com':
			return '<img class="img-comparator" width="196" height="64" name="shopping" id="shopping" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/shopping.png" />';
		case 'Shopzilla':
			return '<img class="img-comparator" width="196" height="64" name="shopzilla" id="shopzilla" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/shopzilla.png" />';
		case 'Twenga':
			return '<img class="img-comparator" width="196" height="64" name="twenga" id="twenga" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/twenga.png" />';
		case 'Rue du Commerce':
			return '<img class="img-comparator" width="196" height="64" name="rueducomemrce" id="rueducommerce" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/rue-du-commerce.gif" />';
		case 'Rue du Commerce - Mirakl':
			return '<img class="img-comparator" width="196" height="110" name="rueducomemrce" id="rueducommerce" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/mirakl.png" />';
		case 'Cherchons':
			return '<img class="img-comparator" width="196" height="64" name="cherchons" id="cherchons" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/cherchons.png" />';
		case 'Nextag':
			return '<img class="img-comparator" width="196" height="64" name="nextag" id="nextag" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/nextag.png" />';
		case 'eBay':
			return '<img class="img-comparator" width="196" height="64" name="ebay" id="ebay" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/ebay.png" />';
		case 'Mon Corner Jardin':
			return '<img class="img-comparator" width="182" height="71" name="corner-jardin" id="corner-jardin" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/corner-jardin.jpg" />';
		case 'Pourdebon':
			return '<img class="img-comparator" width="200" height="90" name="corner-jardin" id="corner-jardin" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/pourdebon.png" />';
		case 'Prixan':
			return '<img class="img-comparator" width="196" height="64" name="prixan" id="prixan" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/prixan.png" />';
		case 'BeezUp':
			return '<img class="img-comparator" width="206" height="79" name="prixan" id="prixan" title="'.htmlspecialchars($ctr_name).'" alt="'.htmlspecialchars($ctr_name).'" src="/admin/images/comparators/beezup.jpg" />';
		default :
			return $ctr_name;
	}
}

/** Cette fonction permet d'affiche l'entête général d'un comparateur de prix
 *	@param $ctr Optionnel, par défaut l'entête de tous les comparateurs de prix est affiche l'un en dessous de l'autre, mettre l'identifiant du comparateur
 *	@param $marketplace Optionnel, filtre sur place de marché (true), comparateur (false) ou indifférent (null)
 *	@return string Le code HTML de chaque entête général si aucun comparateur n'est donné en argument sinon seulement l'entête de celui-ci
 */
function view_ctr_header( $ctr=0, $marketplace=null ){
	global $config;

	$rctr = ctr_comparators_get( $ctr, true, false, ($marketplace === null) ? ($ctr != 0 ? null : false) : $marketplace );

	$use_ctr_amazon = ctr_amazon_get_marketplace();
	$all_ctr_amazon = ctr_amazon_get_marketplace( true );

	$html = '';

	if( $rctr!=false ){

		// Statistiques
		$cat_count = 0; $tmp_ids = array();
		$count = 0;
		$rcat = prd_categories_get( 0, true );
		if( $rcat && ria_mysql_num_rows($rcat) ){
			while( $cat = ria_mysql_fetch_array($rcat) ){

				$tmp_ids[] = $cat['id'];
				$childs = prd_categories_childs_get_array( $cat['id'], true, true );
				$cat_count = $cat_count + sizeof( $childs ) + 1;

				$tmp_ids = array_merge( $childs, $tmp_ids );
			}
		}

		$ctr_cat_publish = ctr_categories_count(is_array($ctr) ? $ctr['id'] : $ctr, true);
		$prd_count = ria_mysql_num_rows(prd_products_get_simple(0, '', true, 0, true, false, false, false, array('have_stock' => true, 'childs' => true)));

		// Affiche le ou les comparateurs
		while( $ctr = ria_mysql_fetch_array($rctr) ){
			// Nom de familles publiées
			$ctr_cat_publish = 0;
			if( ctr_has_categories($ctr['id']) ){
				$ctr_cat_publish = ctr_categories_count( $ctr['id'], true );
			}

			// Savoir si le comparateur est activé
			$active = ctr_comparators_actived( $ctr['id'] );

			// Place de marché ?
			$isMarketplace = $ctr['marketplace'];

			// Lien vers le fichier
			$link = ctr_comparators_file_get($ctr['id']);
			$link_bak = $link;
			if( strlen($link)>43 ){
				$link = substr($link, 0, 20).'...'.substr($link, -20, 20);
			}

			$html .= '
				<div class="comparator" id="comparator-'.$ctr['id'].'">
					<div class="ctr-info">
						<div class="img">
							'.view_ctr_header_img( $ctr['name'] ).'
						</div>
						<div class="info-comparator">
							<a href="'.$ctr['site'].'" target="_blank" title="Visite le site de '.$ctr['name'].'">Visiter le site</a>
			';

			if( trim($ctr['register'])!='' ){
				$html .= '
							<a href="'.$ctr['register'].'" target="_blank" title="Créer votre compte marchand pour '.$ctr['name'].'">S\'enregistrer</a>
				';
			}

			if( trim($ctr['seller'])!='' ){
				$html .= '
							<a href="'.$ctr['seller'].'" target="_blank" title="Aller sur votre espace marchand de '.$ctr['name'].'">Espace marchand</a>
				';
			}

			$html .= '
						</div>
			';

			$mkt_childs = false;
			if( !(!in_array($ctr['id'], $all_ctr_amazon) || in_array($ctr['id'], $use_ctr_amazon) ) ){
				$mkt_childs = true;
			}elseif( in_array($ctr['id'], ctr_cdiscount_partners_get_ria_id()) ){
				$mkt_childs = ctr_cdiscount_partners_use_catalog( $ctr['id'] );
			}

			if( !$mkt_childs ){
				$html .= '
						<div class="stat">
							<span class="stat-title">Statistiques : </span>
							<ul>
								<li>Catégories Exportées : '.ria_number_format(ctr_prd_categories_count($ctr['id'])).' sur '.ria_number_format($cat_count).'</li>
								<li>Produits Exportés : '.ria_number_format(ctr_catalogs_count($ctr['id'])).' sur '.ria_number_format($prd_count).'</li>
					';

				if( ctr_has_categories($ctr['id']) ){
					$html .= '
								<li>Familles Publiées : '.($ctr_cat_publish > 0 ? ria_number_format($ctr_cat_publish).' sur '.ria_number_format(ctr_categories_count($ctr['id'])) : 'Toutes, vous pouvez les filtrer ici : <a href="categories.php?ctr='.$ctr['id'].'">Filtrer</a>').'</li>
					';
				}

				if( $ctr['id'] == CTR_BEEZUP ){
					if( trim($link_bak)!='' ){
						$html .= '
								<li>Fichier : <a target="blank" href="'.$link_bak.'">'.$link.'</a></li>
						';
					}
				}

				if( $active && !$ctr['marketplace'] ){
					if( trim($link_bak)!='' ){
						$html .= '
								<li>Fichier : <a target="blank" href="'.$link_bak.'">'.$link.'</a></li>
						';

						$date = ctr_comparator_logs_get_last_access( $ctr['id'] );
						$html .= '
								<li title="Date du dernier accès par le comparateur de prix lui-même.">Dernier accès : '.( trim($date)!='' ? '<a class="log-access" href="#ctr-id='.$ctr['id'].'">Le '.ria_date_format($date).'</a>' : 'Aucun accès pour le moment' ).'</li>
						';
					}else{
						$html .= '
								<li>Fichier : En attente de génération...</li>
						';
					}
				}

				$html .= '
							</ul>
						</div>
				';
			}else{
				$mkt_parent = $config['ctr_amazon_sync'];
				if( in_array($ctr['id'], ctr_cdiscount_partners_get_ria_id()) ){
					$html .= '
							<div class="stat">Le catalogue présent sur cette place de marché est identique à celui envoyé sur CDiscount. Vous pouvez le personnalisé en activant l\'option dans la partie <a href="/admin/comparators/params.php?ctr='.$ctr['id'].'">Configuration</a>.</div>
					';
				}else{
					$html .= '
							<div class="stat">Le catalogue présent sur cette place de marché est directement synchronisé par Amazon depuis le catalogue envoyé sur '.ctr_comparators_get_name( $mkt_parent ).'.</div>
					';
				}
			}

			$html .= '
						<div class="clear"></div>
					</div>
					<div class="action">
						<form method="post" action="/admin/comparators/comparators.php?'.($isMarketplace ? 'marketplace=1&' : '').'ctr='.$ctr['id'].'">
			';

			if( !$mkt_childs ){
				$html .= '
							<div style="float:left;margin-right:3px">
								<input class="btn-action" type="submit" name="stats-ctr" id="stats-ctr-'.$ctr['id'].'" title="Statistiques' . ($isMarketplace ? ' de la place de marché' : ' du comparateur') . ' ' . $ctr['name'].'" value="Statistiques" />
							</div>
							<div style="float:left;">
								<input class="btn-action" type="submit" name="search-ctr" id="search-ctr-'.$ctr['id'].'" title="Rechercher un ou plusieurs produits" value="Rechercher" />
							</div>
							<div style="float:left;text-align:center;width:65%;">
								<input class="btn-action" type="submit" name="links-cat" id="links-cat-'.$ctr['id'].'" title="Exporter vos catégories produits vers le catalogue de '.$ctr['name'].'" value="Exporter Catégories" />
				';

				if (ctr_has_categories($ctr['id'])) {
					$html .= '
								<input class="btn-action" type="submit" name="filters-cat" id="filters-cat-'.$ctr['id'].'" title="Filtrer les familles du comparateur '.$ctr['name'].'" value="Filtrer Familles" />
					';
				}

				$rattr = ctr_cat_fields_get( $ctr['id'] );
				if( $rattr && ria_mysql_num_rows($rattr) ){
					$html .= '
								<input class="btn-action" type="submit" name="mapping-attr" id="mapping-attr-'.$ctr['id'].'" title="Rattacher les attributs '.$ctr['name'].' à vos informations produits" value="Mapping attributs" />
					';
				}

				$html .= '
							</div>
				';
			}

			$html .= '
							<div class="param-activ">
			';

			if( $active ){
				$html .= '
								<input onclick="return ctrSwitchActivation('.$ctr['id'].', false)" class="btn-action actif" type="submit" name="active" id="active-'.$ctr['id'].'" value="Actif" title="Cliquez pour désactiver ' . ($ctr['marketplace'] ? 'la place de marché' : 'le comparateur de prix') . ' ' . $ctr['name'].'" />
				';
			}else{
				$html .= '
								<input onclick="return ctrSwitchActivation('.$ctr['id'].', true)" class="btn-action inactif" type="submit" name="active" id="inactive-'.$ctr['id'].'" value="Inactif" title="Cliquez pour activer ' . ($ctr['marketplace'] ? 'la place de marché' : 'le comparateur de prix') . ' ' . $ctr['name'].'" />
				';
			}

			$html .= '
								<input class="btn-action" type="submit" name="params" id="params-'.$ctr['id'].'" title="Configurer ' . ($ctr['marketplace'] ? 'la place de marché' : 'le comparateur de prix') . ' '.$ctr['name'].'" value="Configuration" />
							</div>
							<div style="float:left;"></div>
						</form>
					</div>
					<div class="clear"></div>
				</div>
			';
		}
	}

	return $html;
}

/** Affiche l'information de stock lors du choix de la catégorie d'export
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param $infos Obligatoire, mettre true pour afficher un texte ou false pour n'affiche qu'une icone
 *	@param $real Optionnel, par défaut le stock commandé n'est pas pris en compte dans l'informations de stock, mettre true pour le prendre en compte
 *	@param $label_only Optionnel, par défaut le retour est sous forme de texte, metre à true pour retourné le texte sous forme html
 *	@param $return Optionnel, par défaut la fonction fini par afficher le résultat (html/text) mettre à true pour stocker le résultat dans une variable
 *	@param $size Optionnel, taille de l'image en stock, par défaut 26x27 array(width, height)
 *	@return Sous du code html sous un libellé si le param $return est à true sinon rien
 */
function view_prd_stocks( $prd, $infos, $real=false, $label_only = false, $return = false, $size=array() ){
	if(!is_array($size) || empty($size) || count($size)<2 ){
		$size = array(26,27);
	}
	$class = '';
	$html = '';
	if( $prd['orderable'] ){
		if( $prd['stock'] ){
			if( !$label_only ){
				$html .= '<div class="en-stock stock-indic"><img src="/images/images/template/picto-stock.png" width="'.$size[0].'" height="'.$size[1].'" alt="'._('En stock').'" title="'._('En stock').'" class="valign-middle" /> ';
			}
			if( $infos ){
				$html .= _('En stock');
			}
			if( !$label_only ){
				$html .= '</div>';
			}
			$class = 'en-stock';
		}else{
			if( $prd['stock_com'] ){
				if( $prd['stock_livr'] ){
					if(!$label_only)
						$html .= '<div class="disponibilite stock-indic"><img src="/images/images/template/picto-disponibilite.png" width="'.$size[0].'" height="'.$size[1].'" alt="'.str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le #param[date]#')).'" title="'.str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le #param[date]#')).'" class="valign-middle" /> ';
					if ($infos)
						$html .= str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le : #param[date]#'));
					if(!$label_only)
						$html .= '</div>';
					$class = 'disponibilite';
				}else{
					if(!$label_only)
						$html .= '<div class="pre-commande stock-indic"><img src="/images/images/template/picto-precommande.png" width="'.$size[0].'" height="'.$size[1].'" alt="'._('Pré commande').'" title="'._('Pré commande').'" class="valign-middle" /> ';
					if ($infos)
						$html .= _('En pré-commande');
					if(!$label_only)
						$html .= '</div>';
					$class = 'pre-commande';
				}
			}else{
				if( $prd['sleep'] ){
					if(!$label_only)
						$html .= '<div class="disponibilite stock-indic"><img src="/images/images/template/picto-disponibilite.png" width="'.$size[0].'" height="'.$size[1].'" alt="'.str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le #param[date]#')).'" title="'.str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le #param[date]#')).'" class="valign-middle" /> ';
					if ($infos)
						if($prd['stock_livr']){
							$html .= str_replace('#param[date]#', $prd['stock_livr'], _('Disponible le : #param[date]#'));
						}
						else{
							$html .= _('Délai non précisé');
						}
					if(!$label_only)
						$html .= '</div>';
					$class = 'disponibilite';
				}else{
					if( isset($prd['supplier_delay']) && $prd['supplier_delay']!=0 ){
						if(!$label_only)
							$html .= '<div class="delai-en-jours stock-indic"><img src="/images/images/template/picto-delai.png" width="'.$size[0].'" height="'.$size[1].'" alt="'.str_replace('#param[nombre de jours]#', $prd['supplier_delay'], _('Délai de #param[nombre de jours] jours')).'" title="'.str_replace('#param[nombre de jours]#', $prd['supplier_delay'], _('Délai de #param[nombre de jours] jours')).'" class="valign-middle" /> ';
						if ($infos)
							$html .= str_replace('#param[nombre de jours]#', $prd['supplier_delay'], _('Délai : #param[nombre de jours]# jours'));
						if(!$label_only)
							$html .= '</div>';
						$class = 'delai-en-jours';
					}else{
						if(!$label_only)
							$html .= '<div class="pre-commande stock-indic"><img src="/images/images/template/picto-precommande.png" width="'.$size[0].'" height="'.$size[1].'" alt="'._('Pré commande').'" title="'._('Pré commande').'" class="valign-middle" /> ';
						if ($infos)
							$html .= _('En pré-commande');
						if(!$label_only)
							$html .= '</div>';
						$class = 'pre-commande';
					}
				}
			}
		}
		if( $real ){
			$html .= '<div class="'.$class.' stock-real">';
				$html .= _('Stock réel : ').$prd['stock'].' ';
				$html .= _('Commande : ').$prd['stock_com'].' ';
				$html .= _('Réservé :  ').$prd['stock_res'];
			$html .= '</div>';
		}
		if( $return ){
			return $html;
		}else{
			print $html;
		}
	}

}

/**	Génère le code html qui va afficher l'état d'exportation d'un produit vers un comparateur de prix, et permettre d'activer/désactiver l'exportation
 *	@param int $ctr_id Obligatoire, identifiant du comparateur de prix ou de la place de marché
 *	@param $ctr_name Obligatoire, désignation du comparateur ou de la place de marché
 *	@param $prd_id Obligatoire, identifiant du produit à exporter ou ne pas exporter
 *	@param $cat_id Optionnel, identifiant d'une catégorie produit exporté et où se trouve le produit
 *	@param $tab Optionnel, détermine si l'appel est fait depuis l'onglet Comparateurs / Places de marché d'une fiche produit (par défaut à False)
 *	@param $mdl Optionnel, identifiant d'un modèle à transmettre à la méthode view_prd_export_data().
 *
 *	@return string Le code HTML à afficher pour activer/désactiver l'exportation du produit vers le comparateur de prix
 */
function view_prd_export( $ctr_id, $ctr_name, $prd_id, $cat_id=false, $tab=false, $mdl=false ){
	global $config;

	if( !ctr_comparators_exists($ctr_id) ){
		return false;
	}

	if( trim($ctr_name)=='' ){
		return false;
	}

	$rproduct = prd_products_get_simple( $prd_id );
	if( !$rproduct || !ria_mysql_num_rows($rproduct) ){
		return false;
	}

	$product = ria_mysql_fetch_array( $rproduct );

	// Détermine si le produit est publié sur le comparateur de prix
	$is_publish = ctr_catalogs_is_publish( $ctr_id, $prd_id );

	// Récupère la surcharge directe sur le titre
	$prd_title = ctr_catalogs_get_prd_title( $ctr_id, $prd_id );

	// Récupère la surcharge directe sur la description
	$prd_desc = ctr_catalogs_get_prd_desc( $ctr_id, $prd_id );

	$onclick = $tab ? 'onclick="return showChooseCategory('.$ctr_id.', '.$prd_id.', '.( $cat_id>0 ? $cat_id : '0' ).', '.( $tab ? 'true' : 'false' ).');"' : '';

	if( !$tab ){
		print '
			<input type="hidden" name="prd-title" id="prd-title-'.$ctr_id.'" value="'.$prd_title.'" />
			<input type="hidden" name="prd-desc" id="prd-desc-'.$ctr_id.'" value="'.$prd_desc.'" />

			<table id="tbl-export-'.$ctr_id.'" title="'._('Informations sur le produit').'">
				<caption>'.str_replace(
					'#param[nom du comparateur]#',
					htmlspecialchars($ctr_name),
					_('Exportation du produit vers #param[nom du comparateur]#')
				).'</caption>
			<tbody>
		';
	} else {
		print '
			<tr>
				<th colspan="2">'.htmlspecialchars($ctr_name).'</th>
			</tr>
		';
	}

	print '
		<tr>
			<td class="message-return" colspan="2" id="msg-'.$ctr_id.'"></td>
		</tr>
		<tr>
			'.( !$tab ? '<th>'._('Export activé :').'</th>' : '<td>'._('Export activé :').'</td>' ).'
			<td class="middle">
	';

	if( $is_publish ){
		print '
				'._('Oui').' - <a onclick="return unactivatedProduct( $(this), '.$ctr_id.' );" name="prd-'.$prd_id.'" href="#" class="cancel edit">'._('Désactiver son export').'</a>
		';
	} else {
		print '
				'._('Non').' - <a onclick="return activatedProduct( $(this), '.$ctr_id.' );" name="prd-'.$prd_id.'" href="#" class="active edit">'._('Activer son export').'</a>
		';
	}

	print '
			</td>
		</tr>
	';

	// lien avec une famille de comparateur
	if( ctr_comparators_have_categories($ctr_id) ){
		print '
			<tr>
				'.( $tab ? '<td class="col170px">' : '<th>' ).'
					'._('Actuellement lié à :').'
				'.( $tab ? '</td>' : '</th>' ).'
				<td class="middle" id="cat-ctr-'.$ctr_id.'">
					<span class="ctr_cat">
		';

		if( $cat_id>0 ){
			print ctr_categories_export( $ctr_id, $cat_id, 2, '>' );

			if( ctr_categories_get_disabled($ctr_id, $cat_id) ){
				print '
						<div class="error ctr-no-cat">La catégorie actuellement utilisée a été supprimée de '.htmlspecialchars($ctr_name).'. Ceci peut empêcher le produit d’apparaître dans '.htmlspecialchars($ctr_name).'. Nous vous conseillons de modifier cette catégorie et de la remplacer par une autre.</div>
				';
			}
			$button_comparator = 'Modifier';
		}else{
			print _('Actuellement lié à aucune famille du comparateur');
			$button_comparator = 'Ajouter';
		}

		print '
					</span>
		';

		if( $cat_id > 0 && $tab ){
			print '
					<input type="image" formaction="/admin/catalog/product.php?cat=0&amp;prd='.$prd_id.'&amp;ctr='.$ctr_id.'" name="del-link-family" src="/admin/images/del-cat.svg" title="'._('Supprimer le lien').'" class="icon-del-cat" alt="'._('Supprimer').'" />
			';
		}

		print '
					<br/><a class="edit" '.$onclick.' href="?ctr='.$ctr_id.'&amp;prd='.$prd_id.'&amp;cat='.$cat_id.'">'.$button_comparator.'</a>
				</td>
			</tr>
		';
	}

	$params = ctr_catalogs_get_params( $ctr_id, $prd_id );
	switch( $ctr_id ){
		case CTR_PRICEMINISTER :
		{
			$type = isset($_POST['typeproduct']) ? $_POST['typeproduct'] : (isset($params['typeproduct']) ? $params['typeproduct'] : '-1');

			$cats = ctr_priceminister_product_types_get();
			if( is_array($cats) && sizeof($cats) ){
				print '
					<tr>
						'.( $tab ? '<td>' : '<th>' ).( $is_publish || $type!=-1 ? '<span class="mandatory">*</span>' : '' ).'
							Type de produit :
						'.( $tab ? '</td>' : '</th>' ).'
						<td>
							<select name="typeproduct" id="typeproduct">
								<option value="-1">&nbsp;</option>
				';

				foreach( $cats as $k=>$name ){
					print '
								<option '.( $k==$type ? 'selected="selected"' : '' ).' value="'.addslashes( htmlspecialchars($k) ).'">'.htmlspecialchars( $name ).'</option>
					';
				}

				print '
							</select>
							<div class="marketplace-mdl" id="templatepriceminister">
				';

				if( isset($_POST['attr']) && is_array($_POST['attr']) ){
					foreach( $_POST['attr'] as $key=>$val ){
						print '
								<input type="hidden" name="hiddenattr-'.$key.'" value="'.$val.'" />
						';
					}
				} elseif( isset($params['template']) && is_array($params['template']) && sizeof($params['template']) ){
					foreach( $params['template'] as $key=>$val ){
						print '
								<input type="hidden" name="hiddenattr-'.$key.'" value="'.$val.'" />
						';
					}
				}

				print '
							</div>
						</td>
					</tr>
				';
			}
			break;
		}
		case CTR_EBAY :
		{
			require_once('comparators/ctr.ebay.inc.php');

			$ebay = new EBay();

			$multiSKU = null; $ref = ''; $fields = false;
			if( $cat_id>0 ){
				$ref = ctr_categories_get_ref( CTR_EBAY, $cat_id );
				if( trim($ref)!='' ){
					$fields = $ebay->getCategorySpecifics( $ref );
					$multiSKU = $ebay->getAcceptedMultiSKU( $ref );
				}
			}

			print '
				<tr style="display: table-row">
					'.( $tab ? '<td>' : '<th>' ).'
						'._('Informations complémentaires :').'
					'.( $tab ? '</td>' : '</th>' ).'
					<td>
						<div id="templatectr-'.$ctr_id.'" class="marketplace-mdl">
							<input type="hidden" name="ebay-cat" value="'.$ref.'" />
							<input type="hidden" name="ebay-multisku" value="'.$multiSKU.'" />
			';

			if( is_array($fields) && sizeof($fields) ){
				print '<p>Afin que nous puissions exporter votre produit vers eBay, vous devez renseigner toutes les informations ci-dessous avec une <span class="mandatory">*</span>.</p>
						<hr />';

				$i = 0;
				foreach( $fields as $f ){
					$vals = isset($params[$f['name']]) ? explode( ';', $params[$f['name']] ) :  array();

					$show_other = false;
					if( isset($_POST['param-ebay'][$i]) && is_array($_POST['param-ebay'][$i]) ){
						$show_other = in_array('other', $_POST['param-ebay'][$i]) ? true : false;
					}elseif( isset($params[$f['name']]) ){
						$show_other = trim($params[$f['name']])!='' && !in_array($params[$f['name']], $f['values']) ? true  : false;
					}

					$msg = array();
					if( is_array($f['rules']['relations']) && sizeof($f['rules']['relations']) ){
						$tmp = '<span class="bold">';

						if( sizeof($f['rules']['relations'])>1 ){
							$tmp .= 'Les champs "'.implode('", "', $f['rules']['relations']).'" doivent être renseignés.';
						}else{
							$tmp .= 'Le champ "'.implode('", "', $f['rules']['relations']).'" doit être renseigné.';
						}

						$tmp .= '</span>';

						$msg[] = $tmp;
					}

					if( $f['mode']=='FreeText' && is_array($f['values']) && sizeof($f['values']) ){
						if( $f['rules']['maxlength']>0 ){
							if( $f['rules']['maxlength']>1 ){
								$msg[] = '<span class="opt-other" style="display: '.( $show_other ? 'block' : 'none' ).';">En choisissant l\'option "Autre", vous avez la possibilité de renseigner jusqu\'à '.$f['rules']['maxlength'].' valeurs différentes séparées par un point-virgule, en tenant compte des valeurs déjà sélectionnées.</span>';
							}else{
								$msg[] = '<span class="opt-other" style="display: '.( $show_other ? 'block' : 'none' ).';">En choisissant "Autre", la valeur saisie ne doit contenir aucun point-virgule.</span>';
							}
						}
					}

					print '
								<div class="elem">
									<input type="hidden" name="param-ebay-type['.$i.']" value="'.htmlspecialchars($f['name']).'" />
									<div class="label">
										<label for="param-ebay-'.$i.'">'.( ria_array_get($f['rules'], 'minlength', 0)>0 ? '<span class="mandatory">*</span> ' : '' ).htmlspecialchars( $f['name'] ).'</label>
					';

					if( sizeof($msg) ){
						print '
										<sub>'.implode('<br />', $msg).'</sub>
						';
					}

					print '</div>';

					switch( $f['mode'] ){
						case 'FreeText':
							if( !is_array($f['values']) || !sizeof($f['values']) ){
								print '
									<input class="text" type="text" name="param-ebay['.$i.']" id="param-ebay-'.$i.'" value="" />
								';

								break;
							}
						case 'SelectionOnly' :
							$multiple = $f['rules']['maxlength']>1 ? 'multiple="multiple"' : '';
							$size = trim($multiple)!='' ? 'size="'.(ceil(sizeof($f['values'])/2)+2).'"' : '';

							print '
								<select name="param-ebay['.$i.'][]" id="param-ebay-'.$i.'" '.$multiple.' '.$size.'>
									<option value="">&nbsp;</option>
							';

							foreach( $f['values'] as $v ){
								$selected = '';
								if( isset($_POST['param-ebay'][$i]) ){
									$selected = in_array($v, $_POST['param-ebay'][$i]) ? 'selected="selected"' : '';
								}elseif( isset($params[$f['name']]) ){
									$selected = in_array($v, $vals) ? 'selected="selected"' : '';
								}

								print '
									<option '.$selected.' value="'.htmlspecialchars( $v ).'">'.htmlspecialchars( $v ).'</option>
								';
							}

							if( $f['mode']=='FreeText' ){
								$selected = $show_other ? 'selected="selected"' : '';

								print '
									<option '.$selected.' value="other">'._('Autre').'</option>
								';
							}

							print '
								</select>
							';

							if( $f['mode']=='FreeText' ){
								$other = '';
								if( isset($_POST['param-other-ebay'][$i]) ){
									$other = $_POST['param-other-ebay'][$i];
								}elseif( isset($params[$f['name']]) ){
									foreach( $vals as $v ){
										if( !in_array($v, $f['values']) ){
											$other .= (trim($other)!='' ? ';' : '' ).$v;
										}
									}
								}

								$class = trim($other)!='' ? 'show' : '';

								print '
									<div class="other '.$class.'">
										<input class="text" type="text" name="param-other-ebay['.$i.']" value="'.$other.'" />
									</div>
								';
							}
					}

					print '
									<div class="clear"></div>
								</div>
					';

					$i++;
				}
			}elseif( !$cat_id ){
				print '<p>'._('Vous devez choisir une catégorie afin de personnaliser l\'export du produit.').'</p>';
			}

			print '</div>';

			$rparent = prd_parents_get( $prd_id );
			if( $rparent && ria_mysql_num_rows($rparent) ){
				$parent = ria_mysql_fetch_array( $rparent );

				print '
						<div class="marketplace-mdl">
							<p>Ce produit est identifié comme étant une déclinaison de "<a href="/admin/catalog/product.php?cat=0&amp;prd='.$parent['id'].'&amp;tab=marketplace">'.view_prd_is_sync($parent).' '.$parent['ref'].' - '.htmlspecialchars( $parent['name'] ).'</a>". Vous pouvez proposer toutes les déclinaisons sur la même page eBay, pour cela vous devez configurer les options de regroupement sur le produit parent : <a href="/admin/catalog/product.php?cat=0&amp;prd='.$parent['id'].'&amp;tab=marketplace">Configurer les options</a>.</p>
						</div>
				';
			}elseif( prd_products_is_parent($prd_id) ){
				print '
						<div class="marketplace-mdl">
							<p>Ce produit possède plusieurs déclinaisons, afin de toutes les proposer sur la même page eBay vous devez configurer les options de regroupement. Exemple pour un T-Shirt vendu en plusieurs tailles et couleurs, vous devez sélectionner les informations de tailles et de couleurs.</p>
				';

				if( $multiSKU=='false' ){
					print '
						<div class="notice" style="margin-top: 5px;">Attention ! La catégorie choisie n\'accepte pas les déclinaisons. Chaque déclinaison sera exportée séparément sur eBay, nous vous conseillons de choisir une catégorie acceptant les déclinaisons ou bien de renseigner les informations complémentaires sur chaque déclinaison.</div>
					';
				}

				print '
							<hr />
				';

				$option = array(
					'weight' => _('Poids brut'),
					'weight_net' => _('Poids net')
				);

				print '
							<div class="group-option">
				';

				$rchild = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs'=>true, 'parent'=>$_GET['prd']) );
				if( $rchild && ria_mysql_num_rows($rchild) ){
					print '
								<div class="bold">'._('Déclinaisons').'</div>
								<div class="elem">
									<ul>
					';

					while( $child = ria_mysql_fetch_array($rchild) ){
						$is_actived = ctr_catalogs_is_publish( CTR_EBAY, $child['id'] );
						print '
							<li>
								<a href="/admin/catalog/product.php?cat=0&amp;prd='.$child['id'].'&amp;tab=marketplace">'.view_prd_is_sync($child).' '.htmlspecialchars( $child['ref'].' - '.$child['name'] ).'</a>
						';

						if( $is_actived ){
							print '
								<a class="cancel" name="prd-'.$child['id'].'" href="#" onclick="return unactivatedProductChild( $(this), '.CTR_EBAY.' );">'._('Désactiver son export').'</a>
							';
						}else{
							print '
								<a class="active" href="#" name="prd-'.$child['id'].'" onclick="return activatedProductChild( $(this), '.CTR_EBAY.' );">'._('Activer son export').'</a>
							';
						}

						print '</li>';
					}

					print '
									</ul>
								</div>
					';
				}

				print '
							</div>
							<div class="group-option">
								<div class="bold">'._('Options de regroupement').'</div>
								<div class="ul-opt elem">
				';

				$opt_actived = array();
				if( isset($params['opt-grp']) && is_array($params['opt-grp']) && sizeof($params['opt-grp']) ){
					$opt_actived = array_keys( $params['opt-grp'] );

					print '<ul>';

					foreach( $params['opt-grp'] as $key=>$name ){
						print '
							<li>
								<input type="image" src="/admin/images/del-cat.png" class="del-option" title="'._('Retirer cette option').'" name="del-'.$key.'" /> '.htmlspecialchars( $name ).'
							</li>
						';
					}

					print '</ul>';
				}else{
					print _('Aucune option de regroupement n\'a encore été sélectionnée.');
				}

				print '
								</div>
							</div>
							<div class="add-option">
								<input type="hidden" name="add-opt-ctr" value="'.CTR_EBAY.'" />
								<label class="bold" for="add-option">'._('Ajouter :').'</label>
								<select name="add-option" id="add-option">
									<option value=""></option>
				';

				$tmp = array();
				foreach( $option as $code=>$name ){
					if( in_array($code, $opt_actived) ){
						continue;
					}

					$tmp[] = '<option value="'.$code.'">'.htmlspecialchars( $name ).'</option>';
				}

				if( sizeof($tmp) ){
					print '
								<optgroup label="'._('Informations sur le produit').'">
									'.implode( '', $tmp ).'
								</optgroup>
					';
				}

				$rmdl = fld_models_get( 0, 0, CLS_PRODUCT );
				if( $rmdl && ria_mysql_num_rows($rmdl) ){
					while( $mdl_data = ria_mysql_fetch_array($rmdl) ){
						$rfld = fld_fields_get( 0, 0, $mdl_data['id'] );

						if( !$rfld || !ria_mysql_num_rows($rfld) ){
							continue;
						}

						$tmp = array();
						while( $fld = ria_mysql_fetch_array($rfld) ){
							if( in_array($fld['id'], $opt_actived) ){
								continue;
							}

							$tmp[] = '<option value="'.$fld['id'].'">'.htmlspecialchars( $fld['name'] ).'</option>';
						}

						if( sizeof($tmp) ){
							print '
								<optgroup label="'.htmlspecialchars( $mdl_data['name'] ).'">
									'.implode( '', $tmp ).'
								</optgroup>
							';
						}
					}
				}

				print '
								</select>
							</div>
						</div>
				';
			}

			print '
					</td>
				</tr>
			';

			break;
		}
		default :
		{
			$cats_id = array($cat_id);
			if (ctr_comparators_get_fields_use_hierarchy($ctr_id)) {
				$r_cats_id = ctr_categories_parents_get($cat_id);
				if ($r_cats_id && ria_mysql_num_rows($r_cats_id)) {
					while($p_id = ria_mysql_fetch_assoc($r_cats_id)) {
						$cats_id[] = $p_id['id'];
					}
				}
			}
			$have_fields = ctr_comparators_cat_fields_exists($ctr_id, $cats_id);
			print '
				<tr style="display: '.( $cat_id && $have_fields ? 'table-row' : 'none' ).'">
					'.( $tab ? '<td>' : '<th>' ).( $is_publish ? '<span class="mandatory">*</span> ' : '' )._('Informations complémentaires :').'
					'.( $tab ? '</td>' : '</th>' ).'
					<td>
						<div id="templatectr-'.$ctr_id.'" class="marketplace-mdl">
			';

			if( $have_fields ){
				print '
						<p>Afin que nous puissions exporter votre produit vers '.htmlspecialchars($ctr_name).', vous devez renseigner toutes les informations ci-dessous avec une <span class="mandatory">*</span>.</p>
						<hr />
				';

				if( $cat_id ){
					$fields = ctr_cat_fields_get( $ctr_id, $cats_id );
					if( $fields && ria_mysql_num_rows($fields) ){
						$count = 0;
						while( $field = ria_mysql_fetch_assoc($fields) ){
							print '
								<div class="elem">

									<label for="attr-'.$ctr_id.'-'.$count.'">'.( $field['mandatory'] ? '<span class="mandatory">*</span> ' : '' ).$field['name'].' :
										'.( trim($field['desc']) != '' ? '<br /><span class="desc">'.htmlspecialchars( $field['desc'] ).'</span>' : '' ).'
										'.( trim($field['type_name']) != '' ? '<sub>'.htmlspecialchars( $field['type_name'] ).'</sub>' : '' ).'
										'.( trim($field['unite']) != '' ? '<sub>Unité : '.htmlspecialchars( $field['unite'] ).'</sub>' : '' ).'
									</label>
							';

							$values = ctr_cat_field_values_get( $field['id'] );
							if( $values && ria_mysql_num_rows($values) ){
								print '
									<select name="vals['.$ctr_id.']['.$field['id'].']" id="attr-'.$ctr_id.'-'.$count.'">
										<option value=""></option>
								';

								while( $val = ria_mysql_fetch_assoc($values) ){
									$selected = isset($params[$field['id']]) && $params[$field['id']]==$val['val'] ? 'selected="selected"' : '';

									print '
										<option value="'.$val['id'].'" '.$selected.'>'.htmlspecialchars( $val['val'] ).'</option>
									';
								}

								print '
									</select>
								';
							} else {
								$val = '';
								if( isset($params[ $field['id'] ]) && trim($params[ $field['id'] ]) != '' ){
									$val = $params[ $field['id'] ];
								}else{
									$val = ctr_cat_field_values_get_default( $ctr_id, $field['id'], $field['code'], $product );
								}
								print '
									<input class="text" type="text" name="vals['.$ctr_id.']['.$field['id'].']" id="attr-'.$ctr_id.'-'.$count.'" value="'.htmlspecialchars( $val ).'" />
								';
							}

							print '
									<div class="clear"></div>
								</div>
							';

							$count++;
						}
					}

					if( isset($_POST['attr']) && is_array($_POST['attr']) ){
						foreach( $_POST['attr'] as $key=>$val ){
							print '
								<input type="hidden" name="hiddenattr-'.$key.'" value="'.$val.'" />
							';
						}
					} elseif( isset($params['template']) && is_array($params['template']) && sizeof($params['template']) ){
						foreach( $params['template'] as $key=>$val ){
							print '
								<input type="hidden" name="hiddenattr-'.$key.'" value="'.$val.'" />
							';
						}
					}
				} else {
					print _('Vous devez choisir une catégorie afin de personnaliser l\'export du produit.');
				}
			}

			print '
						</div>
					</td>
				</tr>
			';

			break;
		}
	}

	// affiche la gestion des enchères si le comparateur le permet
	if( ctr_comparators_auctions_used($ctr_id) ){
		$direct = ctr_catalogs_get_auctions( $ctr_id, $prd_id );

		$auctions = $direct;
		if( !$direct )
			$auctions = ctr_catalogs_get_auctions( $ctr_id, $prd_id, false );

		$direct = number_format( $direct, 2, ',', ' ' );
		$auctions = number_format( $auctions, 2, ',', ' ' );

		print '
			<tr>
				'.( !$tab ? '<th>Enchère :</th>' : '<td>Enchère :</td>' ).'
				<td class="middle" id="td-auctions-'.$ctr_id.'">
					<input class="qte" type="text" value="'.$auctions.'" id="auctions-6" name="auctions['.$ctr_id.']" /> €
		';

		$cat = ctr_catalogs_get_categorie( $ctr_id, $prd_id, false );
		if( $cat ){
			$min = ctr_categories_get_min_auctions( $ctr_id, $cat );

			if( $min>0 ){
				print '
					<span style="font-size: 11px">(Minimum = '.number_format( $min, 2, ',', ' ' ).' €)</span>
				';
			}
		}

		print '
				</td>
			</tr>
		';
	}

	if( !$tab ){
		print '
			<tr>
				<th>'._('Titre utilisé lors de l\'export :').'</th>
				<td class="info middle">
					<input type="text" value="'.htmlspecialchars( $prd_title ).'" id="title-'.$ctr_id.'" name="title['.$ctr_id.']" class="text" />
				</td>
			</tr>
			<tr>
				<th>'._('Description utilisée lors de l\'export :').'</th>
				<td class="info middle">
					<textarea rows="10" cols="50" id="desc-'.$ctr_id.'" name="desc['.$ctr_id.']">'.htmlspecialchars( $prd_desc ).'</textarea>
				</td>
			</tr>
		';
	}else{
		$is_market = ctr_comparators_is_marketplace($ctr_id);

		$date_start = isset($_SESSION['datepicker_date1']) && isdate($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : false;
		$date_end = isset($_SESSION['datepicker_date2']) && isdate($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : false;
		$wst_id = isset($_SESSION['websitepicker']) && wst_websites_exists( $_SESSION['websitepicker'] ) ? $_SESSION['websitepicker'] : $config['wst_id'];

		// Performence commerciale du produit
		$stats = stats_comparators_get($ctr_id, 0, $prd_id, dateparse($date_start), dateparse($date_end), true, $wst_id);

		print '
			<tr>
				<td>'._('Statistiques :').'</td>
				<td>
					<ul>
						'.(!$is_market ? '<li>'._('Clics').' : '.ria_number_format($stats['clicks'], NumberFormatter::DECIMAL).'</li>' : '').'
						'.(!$is_market ? '<li>'._('Coût').' : '.ria_number_format($stats['cost'], NumberFormatter::CURRENCY, 2).'</li>' : '').'
						<li>'._('Vente(s)').' : '.ria_number_format($stats['sales'], NumberFormatter::DECIMAL).'</li>
						'.(!$is_market ? '<li>'._('Coût par vente').' : '.ria_number_format($stats['cost-sales'], NumberFormatter::CURRENCY, 2).'</li>' : '').'
						<li>'._('Chiffre d\'affaires généré').' : '.ria_number_format($stats['ca'], NumberFormatter::CURRENCY, 2).' HT / '.ria_number_format($stats['ca-ttc'], NumberFormatter::CURRENCY, 2).' TTC</li>
						<li>'._('Taux de transformation').' : '.ria_number_format($stats['transfo'], NumberFormatter::PERCENT, 2).'</li>
						<li>'._('Marge').' : '.ria_number_format($stats['transfo'], NumberFormatter::CURRENCY, 2).'</li>
						<li>ROI : <span class="'.($stats['roi'] ? 'val-positif':'val-negatif').'">'.ria_number_format($stats['roi'], NumberFormatter::PERCENT, 2).'</span></li>
					</ul>
				</td>
			</tr>
			<tr>
				<td>'._('Informations exportées : ').'</td>
				<td class="ctr-export-info-'.$ctr_id.'-'.$prd_id.'">
					'.view_prd_export_data($ctr_id, $product['id'], (trim($prd_title) ?: ''), (trim($prd_desc) ?: ''), $mdl).'
				</td>
			</tr>
		';

		// Configuration supplémentaire pour BeezUp
		if( $ctr_id == CTR_BEEZUP ){
			print '<tr>'
				.'<td>'._('Configuration : ').'</td>'
				.'<td>#config_beezup#</td>'
			.'</tr>';
		}
	}

	if( !$tab ){
		print '
				</tbody>
				<tfoot>
					<tr>
						<th colspan="2">
							<input type="submit" onclick="return saveInfosPerso('.$ctr_id.');" value="Enregistrer" id="save-info-'.$ctr_id.'" name="save-info" class="save" />
						</th>
					</tr>
				</tfoot>
			</table>
		';
	}
}

/** Cette fonction charge les informations sur les données exportés vers les comparateurs de prix / places de marché.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param $prd_id Obligatoire, identifiant d'un produit
 *	@param $prd_title Optionnel, titre surchargé
 *	@param $prd_desc Optionnel, description surchargée
 *	@param $mdl_id Optionnel, identifiant d'un modèle de donnée par défaut
 *	@return string Le code HTML pour la zone "Informations"
 */
function view_prd_export_data( $ctr_id, $prd_id, $prd_title=false, $prd_desc=false, $mdl_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$html = '';
	$use_mdl = $mdl_id && ctr_models_comparators_exists( $ctr_id, $mdl_id );
	$ctr_name = ctr_comparators_get_name( $ctr_id );
	$is_market = ctr_comparators_is_marketplace( $ctr_id );

	$prd_title = ctr_catalogs_get_prd_title( $ctr_id, $prd_id );
	$prd_desc = ctr_catalogs_get_prd_desc( $ctr_id, $prd_id );

	$ctr_mdl = false;
	$rmdl = ctr_models_get( $mdl_id, $prd_id, $is_market );
	if( $rmdl && ria_mysql_num_rows($rmdl) ){
		$ctr_mdl = ria_mysql_fetch_array( $rmdl );

		$mdl_id = $ctr_mdl['mdl_id'];
	}

	$has_mdl_title = isset($ctr_mdl['prd_title']) && trim($ctr_mdl['prd_title'])!='' ? true : false;
	$has_mdl_desc  = isset($ctr_mdl['prd_desc'])  && trim($ctr_mdl['prd_desc'])!=''  ? true : false;

	$surcharge = true;
	$txt_market = $is_market ? 'cette place de marché' : 'ce comparateur de prix';

	if( trim($prd_title)!='' && trim($prd_desc)!='' ){
		$html .= '
			Le titre et la description sont directement surchargés pour '.$txt_market.'.
		';
	}elseif( trim($prd_title)!='' ){
		$html .= '
			Le titre est directement surchargé et
		';

		if( $mdl_id && $has_mdl_desc ){
			$html .= '
				la description utilise votre modèle personnalisé
			';
		}else{
			$html .= '
				les informations du produit sont utilisées pour la description
			';
		}

		$html .= '
			lors de l\'export vers  '.htmlspecialchars($ctr_name).'.
		';
	}elseif( trim($prd_desc)!='' ){
		$html .= '
			La description est directement surchargée et
		';

		if( $mdl_id && $has_mdl_title ){
			$html .= '
				le titre utilise votre modèle personnalisé
			';
		}else{
			$html .= '
				les informations du produit sont utilisées pour le titre
			';
		}

		$html .= '
			lors de l\'export vers '.htmlspecialchars($ctr_name).'.
		';
	}else{
		$surcharge = false;

		if( !$mdl_id || (!$has_mdl_title && !$has_mdl_desc) ){
			$html .= '
				Le titre et la description utilisent les informations du produit lors de son export vers '.htmlspecialchars($ctr_name).'.
			';
		}else{
			if( $has_mdl_title && $has_mdl_desc ){
				$html .= '
					Le titre et la description utilisent votre modèle personnalisé lors de l\'export du produit vers '.htmlspecialchars($ctr_name).'.
				';
			}elseif( $has_mdl_title ){
				$html .= '
					Le titre utilise votre modèle personnalisé et les informations du produit sont utilisées pour la description lors de l\'export du produit vers '.htmlspecialchars($ctr_name).'.
				';
			}elseif( $has_mdl_desc ){
				$html .= '
					Le description utilise votre modèle personnalisé et les informations du produit sont utilisées pour le titre lors de l\'export du produit vers '.htmlspecialchars($ctr_name).'.
				';
			}
		}
		if( $mdl_id && $has_mdl_title && $has_mdl_desc ){

		}
	}

	$img_mdl = $img_ctr = false;

	$rimg = ctr_images_get( 0, $ctr_id );
	if( $rimg && ria_mysql_num_rows($rimg) ){
		$img_ctr = true;
	}else{
		if( $mdl_id ){
			$rimg = ctr_images_get( $mdl_id );
			if( $rimg && ria_mysql_num_rows($rimg) ){
				$img_mdl = true;
			}
		}
	}


	$html .= '
				'.' Les images seront quant à elles récupérées depuis'.'
	';

	if( $img_ctr ){
		$html.= ' la surcharge faite sur '.$txt_market.'.';
	}elseif( $img_mdl ){
		$html .= ' celles présentes dans votre modèle personnalisé.';
	}else{
		$html .= ' celles de la fiche produit.';
	}

	$html .= '
			<ul class="mdl-actions">
	';

	if( $surcharge ){
		if( $mdl_id ){
			$html .= '
				<li>
					<a class="edit" title="Cette action supprime la surcharge directe pouvant être en place sur le titre, la description ou les images, le modèle par défaut sera alors utilisé." href="#" onclick="return useOnlyMdlInCtr('.$ctr_id.', '.$prd_id.', '.$mdl_id.');">N\'utilise que le modèle par défaut</a>
				</li>
			';
		}/*else{
			$html .= '
				<li><a class="edit" title="Cette action permet de renseigner le modèle par défaut utilisé lors de l\'export du produit sur '.( $is_market ? 'toutes les places de marché' : 'tous les comparateurs de prix' ).'." href="#tbl-ctr-mdl">Éditer le modèle par défaut</a></li>
			';
		}*/

		$html .= '
				<li><a class="edit" title="Cette action permet de renseigner une surcharge pour le titre et/ou la description directement pour '.$txt_market.'." onclick="return editInfosCtrMarket('.$ctr_id.', '.$prd_id.');" href="#">'._('Modifier la surcharge').'</a></li>
		';
	}else{
		/*if( !$mdl_id ){
			$html .= '
				<li><a class="edit" title="Cette action permet de renseigner le modèle par défaut utilisé lors de l\'export du produit sur '.( $is_market ? 'toutes les places de marché' : 'tous les comparateurs de prix' ).'." href="#tbl-ctr-mdl">Éditer le modèle par défaut</a></li>
			';
		}*/

		$html .= '
				<li><a class="edit" title="Cette action permet de renseigner une surcharge pour le titre et/ou la description directement pour '.$txt_market.'." onclick="return editInfosCtrMarket('.$ctr_id.', '.$prd_id.');" href="#">Surcharger les informations pour '.$txt_market.'</a></li>
		';
	}

	$html .= '
			</ul>
	';

	return $html;
}

/// @}
// \endcond
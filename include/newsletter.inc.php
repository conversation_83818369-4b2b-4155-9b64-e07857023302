<?php

require_once('db.inc.php');
require_once('users.inc.php');
require_once('strings.inc.php');
require_once('email.inc.php');

/** \defgroup field_constants_nlr_type Newsletter
 *	\ingroup model_constants
 *	@{
 */
/// Toutes adresses
define( 'NEWSLETTER_TYPE_ALL', 0 );
/// Adresses dont l'inscription n'est pas terminée
define( 'NEWSLETTER_TYPE_PRE_INSCRIPT', 1 );
/// Adresses dont l'inscription est terminée et qui ne sont pas désinscrits
define( 'NEWSLETTER_TYPE_INSCRIPT', 2 );
/// Adresses dont la désinscription a été demandée mais non terminée (donc toujours inscrites)
define( 'NEWSLETTER_TYPE_PRE_UNINSCRIPT', 3 );
/// Adresses désinscrites
define( 'NEWSLETTER_TYPE_UNINSCRIPT', 4 );

/// @}

/** \defgroup tools_newsletter Newsletter
 *	\ingroup tools
 *
 *	Ce module contient les fonctions de gestion de la newsletter. Ceci regroupe aussi bien les fonctions
 *	d'inscription/désinscription que les fonctions d'envoi.
 *
 *	Les fonctions d'inscription et de désinscription utilisent des emails de confirmation afin de garantir
 *	que le propriétaire de l'adresse email est bien l'auteur de la demande. Ces emails permettent également
 *	de vérifier la validité de l'adresse email utilisée.
 *
 *	Pour garantir la sécurité du système, les codes de confirmation doivent être suffisamment inviolables et
 *	totalement uniques.
 *	Pour cela, ils sont composés :
 *		- de l'identifiant attribué à la ligne d'inscription
 *		- d'un nombre aléatoire, associé à l'enregistrement.
 *
 *	@todo Dans l'interface d'administration, ajouter une fonction d'importation
 *
 * @{
 */

/// Tableau des types et libellés de types d'adresses
$NEWSLETTER_TYPES = array(
	NEWSLETTER_TYPE_ALL 			=>	'Toutes les adresses',
	NEWSLETTER_TYPE_PRE_INSCRIPT 	=>	'Inscriptions non terminées',
	NEWSLETTER_TYPE_INSCRIPT 		=>	'Inscrits',
	NEWSLETTER_TYPE_PRE_UNINSCRIPT 	=>	'Désinscriptions non terminées',
	NEWSLETTER_TYPE_UNINSCRIPT 		=>	'Désinscriptions terminées'
);

/**	Permet le chargement d'une ou plusieurs adresses emails connues du système de newsletter
 *
 *	@param $type Facultatif, Type d'adresses à charger, à choisir parmi les constantes NEWSLETTER_TYPE_ALL, NEWSLETTER_TYPE_PRE_INSCRIPT, NEWSLETTER_TYPE_INSCRIPT, NEWSLETTER_TYPE_PRE_UNINSCRIPT, NEWSLETTER_TYPE_UNINSCRIPT
 *	@param int $id Facultatif, identifiant d'une inscription sur laquelle filtrer le résultat
 *	@param string $email Facultatif, adresse email sur laquelle filtrer le résultat
 *	@param int $nlr_cat_id Facultatif, categorie associé à aux adresses
 *	@param $deleted Falcutatif, 0 pour les emails non supprimés, 1 pour les supprimés, une autre valeur pour tous les mails
 *	@param string $date_confirmed_start Facultatif, date de confirmation d'inscription postérieures ou égale à cette date
 *	@param string $date_confirmed_end Facultatif, date de confirmation d'inscription antérieure ou égale à cette date
 *	@param int $seg_id Optionnel, identifiant de segment sur lequel filtrer le résultat
 *	@param int $prd_cat_id Optionnel, identifiant d'une catégorie de produits (annule et remplace $nlr_cat_id si spécifié).
 *	@param $sort Optionnel, personnalisation du tri, par défaut le tri est fait sur l'adresse mail (ordre alphabétique), les valeurs acceptées sont : email, inscript-requested
 *	@param string $phone Optionnel, numéro de téléphone sur laquelle filtrer le résultat
 *	@param $sub_usr_id Optionnel, identifiant du compte rattaché à l'inscription sur lequel filtrer le résultat
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'enregistrement
 *			- email : adresse email
 *			- phone : numéro de téléphone
 *			- email-verified : booléen indiquant si l'adresse a été vérifiée par envoi d'un email
 *			- cat_id : identifiant de la catégorie de newsletter
 *			- cat : nom de la catégorie de newsletter
 *			- inscript-requested : date/heure de demande d'inscription
 *			- inscript-confirmed : date/heure de confirmation d'inscription
 *			- inscript-confirmed-en : date/heure de confirmation d'inscription (format EN)
 *			- uninscript-requested : date/heure de demande de désinscription
 *			- uninscript-confirmed : date/heure de confirmation de désinscription
 *			- uninscript-confirmed-en : date/heure de confirmation de désinscription (format EN)
 *			- usr_id : identifiant du compte client lié à l'adresse mail inscrite
 *			- sub_usr_id : nouveau, identifiant du compte client inscrit
 *			- prf_name : désignation du profil du compte client
 *			- prc_name : désignation de la catégorie tarifaire
 *			- usr_orders : nombre de commandes passées avec ce compte client
 *			- prd_cat_id : identifiant de la catégorie de produit
 *			- mailjet_id : identifiant du contact sur MailJet
 *			- type_cat : type de newsletter
 */
function nlr_subscribers_get( $type=NEWSLETTER_TYPE_ALL, $id=0, $email='', $nlr_cat_id=1, $deleted=0, $date_confirmed_start=null, $date_confirmed_end=null, $seg_id=0, $prd_cat_id=0, $sort=false, $phone=null, $sub_usr_id=0 ){

	if( isdate($date_confirmed_start) && !isdateheure($date_confirmed_start) ){
		$date_confirmed_start = $date_confirmed_start.' 00:00:00';
	}

	if( isdate($date_confirmed_end) && !isdateheure($date_confirmed_end) ){
		$date_confirmed_end = $date_confirmed_end.' 23:59:59';
	}

	global $config;

	$sql = '
		select	sub_id as id,
				sub_usr_id,
				sub_email as email,
				sub_telephone as phone,
				sub_email_verified as "email-verified",
				sub_cat_id as cat_id,
				cnt_cat as cat,
				date_format(sub_inscript_requested,"%d/%m/%Y à %H:%i") as "inscript-requested",
				date_format(sub_inscript_confirmed,"%d/%m/%Y à %H:%i") as "inscript-confirmed",
				sub_inscript_confirmed as "inscript-confirmed-en",
				date_format(sub_uninscript_requested,"%d/%m/%Y à %H:%i") as "uninscript-requested",
				date_format(sub_uninscript_confirmed,"%d/%m/%Y à %H:%i") as "uninscript-confirmed",
				sub_uninscript_confirmed as "uninscript-confirmed-en",
				usr_id, usr_orders, prf_name, prc_name, cnt_type as type_cat,
				title_name, adr_firstname as firstname, adr_lastname as lastname, adr_society as society,
				md5(concat(sub_id,sub_email)) as code,
				sub_prd_cat_id as prd_cat_id, sub_mailjet_id as mailjet_id
		from nlr_subscribers
			left join gu_users on ( (0=usr_tnt_id or sub_tnt_id=usr_tnt_id) and sub_email=usr_email and usr_date_deleted is null )
			left join gu_profiles on ((0=prf_tnt_id or usr_tnt_id=prf_tnt_id) and usr_prf_id=prf_id)
			left join prd_prices_categories on (usr_tnt_id=prc_tnt_id and usr_prc_id=prc_id)
			left join gu_adresses on (usr_tnt_id=adr_tnt_id and usr_adr_invoices=adr_id and usr_id=adr_usr_id)
			left join gu_titles on (adr_title_id=title_id)
			left join nlr_cat on (sub_cat_id=cnt_id and sub_tnt_id=cnt_tnt_id)
		where sub_tnt_id='.$config['tnt_id'].'
			and cnt_date_deleted is null
	';

	switch( $type ){
		case NEWSLETTER_TYPE_PRE_INSCRIPT:
			$sql .= ' and sub_inscript_confirmed is null';
			break;
		case NEWSLETTER_TYPE_INSCRIPT:
			$sql .= ' and sub_inscript_confirmed is not null and sub_uninscript_confirmed is null';
			break;
		case NEWSLETTER_TYPE_PRE_UNINSCRIPT:
			$sql .= ' and sub_uninscript_requested is not null and sub_uninscript_confirmed is null';
			break;
		case NEWSLETTER_TYPE_UNINSCRIPT:
			$sql .= ' and sub_uninscript_confirmed is not null';
			break;
		default: // NEWSLETTER_TYPE_ALL
			break;
	}

	if( is_numeric($id) && $id > 0 ){
		$sql .= ' and sub_id='.$id;
	}

	if( is_numeric($prd_cat_id) && $prd_cat_id > 0 ){
		$sql .= ' and sub_prd_cat_id='.$prd_cat_id;
		$nlr_cat_id = 0;
	}

	if( is_numeric($nlr_cat_id) && $nlr_cat_id > 0 ){
		$sql .= ' and sub_cat_id='.$nlr_cat_id;
	}

	// Si l'email est renseigné, alors on filtre sur celui ci
	if( trim($email) ){
		$sql .= ' and sub_email like "%'.addslashes(strtolower(trim($email))).'%"';
	} elseif( trim($phone) ){ // Sinon si le téléphone est renseigné, on filtre dessus
		$sql .= ' and sub_telephone like "%'.$phone.'%"';
	}

	if( $deleted == 1 ){
		$sql .= ' and sub_date_deleted is not null';
	}elseif( $deleted == 0 ){
		$sql .= ' and sub_date_deleted is null';
	}

	if( isdateheure($date_confirmed_start) ){
		$sql .= ' and sub_inscript_confirmed >= "'.dateheureparse($date_confirmed_start).'"';
	}

	if( isdateheure($date_confirmed_end) ){
		$sql .= ' and sub_inscript_confirmed <= "'.dateheureparse($date_confirmed_end).'"';
	}

	if( is_numeric($seg_id) && $seg_id > 0 ){
		$users = gu_users_get_by_segment( $seg_id, null, true, true );
		if( !is_array($users) || !sizeof($users) ){
			$users = array(-1); // aucun résultat
		}
		$sql .= ' and usr_id in ('.implode(', ', $users).')';
	}

	if ( is_numeric($sub_usr_id) && $sub_usr_id > 0 ){
		$sql .= ' and (sub_usr_id='.$sub_usr_id.' or usr_id='.$sub_usr_id.')';
	}

	$sql .= '
		group by sub_id
	';

	$sort_final = array();

	if( $sort !== false && is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col=>$dir ){
			switch( $col ){
				case 'email': {
					$sort_final[] = 'sub_email '.$dir;
					break;
				}
				case 'inscript-requested': {
					$sort_final[] = 'sub_inscript_requested '.$dir;
					break;
				}
			}
		}
	}

	if( !sizeof($sort_final) ){
		$sort_final = array('sub_email, sub_telephone asc');
	}

	$sql .= ' order by '.implode( ', ', $sort_final );

	return ria_mysql_query( $sql );

}

/**	Permet la vérification d'un identifiant d'inscription. Cette fonction vérifie aussi bien la forme (type, intervalle), que l'existance dans la base de données d'une inscription possédant cet identifiant.
 *	@param int $id Identifiant à vérifier.
 *	@param int $nlr_cat_id Facultatif, identifiant de categorie de newsletters
 *	@param int $prd_cat_id Facultatif, identifiant de catégorie de produit (annule et remplace $nlr_cat_id si spécifié)
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide
 */
function nlr_subscribers_exists( $id , $nlr_cat_id=1, $prd_cat_id=0 ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select sub_id from nlr_subscribers
		where sub_tnt_id='.$config['tnt_id'].' and sub_id='.$id.'
	';

	if( is_numeric($prd_cat_id) && $prd_cat_id > 0 ){
		$sql .= ' and sub_prd_cat_id='.$prd_cat_id;
		$nlr_cat_id = 0;
	}

	if( is_numeric($nlr_cat_id) && $nlr_cat_id > 0 ){
		$sql .= ' and sub_cat_id='.$nlr_cat_id;
	}

	$res = ria_mysql_query($sql);

	return $res ? ria_mysql_num_rows($res) : false;
}

/**	Permet l'inscription d'une adresse email ou un numéro de téléphone sans passer par le système de vérification de l'adresse.
 *	Cette fonction est destinée à l'administration de la liste.
 * 	Il faut obligatoirement que le mail ou le numéro de téléphone soit renseigné
 *	@param string $email Facultatif, Adresse email à inscrire
 *	@param int $nlr_cat_id Facultatif, identifiant de catégorie de newsletter
 *	@param int $prd_cat_id Facultatif, identifiant de catégorie de produit (annule et remplace $nlr_cat_id si spécifié)
 *	@param string $phone Facultatif, numero de téléphone à inscrire
 *	@param $sub_usr_id Facultatif, identifiant du compte client à rattacher à l'inscription
 *	@param string $firstname Facultatif, prénom de l'inscrit
 *	@param string $lastname Facultatif, nom de l'inscrit
 *	@return bool true en cas de succès, false en cas d'échec
 */
function nlr_subscribers_add( $email=null, $nlr_cat_id=1, $prd_cat_id=0, $phone=null, $sub_usr_id=0, $firstname='', $lastname='' ){
	if($email !== null){
		$email = str_replace(' ', '', strtolower(strtoupper2($email)));
	}
	if ($phone !== null) {
		$phone = str_replace(' ', '', $phone);
	}

	if($email == null && $phone == null){
		return false;
	}

	if( $email != null && (!trim($email) || !isemail($email)) ){
		return false;
	}

	if( $phone != null && (!trim($phone) || !isphone($phone)) ){
		return false;
	}

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( !$nlr_cat_id && !$prd_cat_id ){
		return false;
	}

	if( $email !== null && newsletter_is_inscripted( $email, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'mail' ) ){
		return true;
	} elseif ($phone !== null && newsletter_is_inscripted( $phone, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'phone' )){
		return true;
	}

	if( $prd_cat_id > 0 ){
		$nlr_cat_id = 'NULL';
	}else{
		$prd_cat_id = 'NULL';
	}

	global $config;

	$fields = [];
	$values = [];

	$fields[] = 'sub_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'sub_inscript_requested';
	$values[] = 'now()';

	$fields[] = 'sub_inscript_confirmed';
	$values[] = 'now()';

	$fields[] = 'sub_cat_id';
	$values[] = $nlr_cat_id;

	$fields[] = 'sub_prd_cat_id';
	$values[] = $prd_cat_id;

	if( trim($email) != '' ){
		$fields[] = 'sub_email';
		$values[] = '"'.addslashes( $email ).'"';
	}

	if( trim($phone) != '' ){
		$fields[] = 'sub_telephone';
		$values[] = '"'.addslashes( $phone ).'"';
	}

	if( is_numeric($sub_usr_id) && $sub_usr_id > 0 ){
		$fields[] = 'sub_usr_id';
		$values[] = $sub_usr_id;
	}

	if( trim($firstname) != '' ){
		$fields[] = 'sub_firstname';
		$values[] = '"'.addslashes( $firstname ).'"';
	}

	if( trim($lastname) != '' ){
		$fields[] = 'sub_lastname';
		$values[] = '"'.addslashes( $lastname ).'"';
	}

	return ria_mysql_query('
		insert into nlr_subscribers
			('.implode( ', ', $fields ).')
		values
			('.implode( ', ', $values ).')
	');
}

/**	Permet la désinscription d'une adresse email sans passer par le système de vérification d'adresse.
 *	Cette fonction est destinée à l'administration de la liste.
 * 	Il faut obligatoirement que le mail ou le numéro de téléphone soit renseigné
 *	@param string $email Facultatif, Adresse email à désinscrire
 *	@param int $nlr_cat_id Facultatif, categorie de newsletter
 *	@param int $prd_cat_id Facultatif, categorie de produits (annule et remplace $nlr_cat_id si spécifié)
 *	@param $mailjet Optionnel, si oui ou non la désinscription provient de MailJet
 *	@param string $phone Facultatif, Adresse email à désinscrire
 *	@param $sub_usr_id Facultatif, identifiant du compte client associé à l'inscription
 *	@return bool true en cas de succès, false en cas d'échec
 */
function nlr_subscribers_del( $email=null, $nlr_cat_id=1, $prd_cat_id=0, $mailjet=false, $phone=null, $sub_usr_id=0 ){
	if($email !== null){
		$email = str_replace(' ', '', strtolower(strtoupper2($email)));
	}
	if ($phone !== null) {
		$phone = str_replace(' ', '', $phone);
	}

	if($email == null && $phone == null){
		return false;
	}

	if( $email != null && (!trim($email) || !isemail($email)) ){
		return false;
	}

	if( $phone != null && (!trim($phone) || !isphone($phone)) ){
		return false;
	}

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( $email !== null && !newsletter_is_inscripted( $email, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'mail' ) ){
		return true;
	} elseif ($phone !== null && !newsletter_is_inscripted( $phone, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'phone' )){
		return true;
	}

	if( $prd_cat_id ){
		$nlr_cat_id = 0;
	}

	global $config;

	$sql = '
		update nlr_subscribers
		set sub_uninscript_requested = now(), sub_uninscript_confirmed = now(), sub_unsubscribe_mailjet = '.( $mailjet ? '1' : '0' ).'
		where sub_tnt_id = '.$config['tnt_id'].'
		and sub_inscript_confirmed is not null
		and sub_uninscript_confirmed is null
	';

	if ($phone !== null){
		$sql .= ' and sub_telephone = "'.$phone.'"';
	} else {
		$sql .= ' and sub_email = "'.addslashes($email).'"';
	}

	if( is_numeric($nlr_cat_id) && $nlr_cat_id > 0 ){
		$sql .= ' and sub_cat_id = '.$nlr_cat_id;
	}

	if( is_numeric($prd_cat_id) && $prd_cat_id > 0 ){
		$sql .= ' and sub_prd_cat_id = '.$prd_cat_id;
	}

	if( is_numeric($sub_usr_id) && $sub_usr_id > 0 ){
		$sql .= ' and sub_usr_id = '.$sub_usr_id;
	}

	return ria_mysql_query($sql);

}

/**	Permet la désinscription via l'id du suscriber
 *	Cette fonction est destinée à l'administration de la liste.
 *	@param int $id id du suscriber
 *	@param $mailjet Optionnel, si oui ou non la désinscription provient de MailJet
 *	@return bool true en cas de succès, false en cas d'échec
 */
function nlr_subscribers_del_by_id( $id, $mailjet=false ){
	global $config;

	$sql = '
		update nlr_subscribers
		set sub_uninscript_requested = now(), sub_uninscript_confirmed = now(), sub_unsubscribe_mailjet = '.( $mailjet ? '1' : '0' ).'
		where sub_id = '.$id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour l'email d'une inscrition à la newsletter.
 *	@param int $id Obligatoire, identifiant de l'inscrit.
 *	@param string $email Obligatoire, nouvelle adresse email de l'inscrit.
 *	@param int $nlr_cat_id Optionnel, identifiant de la catégorie de newsletter.
 *	@param int $prd_cat_id Optionnel, identifiant de la catégorie de produit.
 *	@param $sub_usr_id Optionnel, identifiant du compte client qui s'inscrit
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function nlr_subscribers_email_set( $id, $email, $nlr_cat_id=1, $prd_cat_id=0, $sub_usr_id=0 ){

	$email = str_replace(' ', '', strtolower(strtoupper2($email)));

	if( !isemail($email) ){
		return false;
	}

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( !nlr_subscribers_exists( $id, $nlr_cat_id, $prd_cat_id ) ){
		return false;
	}

	if( newsletter_is_inscripted( $email, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'mail' ) ){
		return false;
	}

	global $config;

	$sql = '
		update nlr_subscribers
		set sub_email = "'.addslashes($email).'"
		where sub_tnt_id = '.$config['tnt_id'].'
		and sub_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

/** Met à jour le téléphone d'un inscrit.
 *	@param int $id Obligatoire, identifiant de l'inscrit.
 *	@param string $phone Obligatoire, nouveau téléphone de l'inscrit.
 *	@param int $nlr_cat_id Optionnel, identifiant de la catégorie de newsletter.
 *	@param int $prd_cat_id Optionnel, identifiant de la catégorie de produit.
 *	@param $sub_usr_id Optionnel, identifiant du compte client concerné
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function nlr_subscribers_phone_set( $id, $phone, $nlr_cat_id=1, $prd_cat_id=0, $sub_usr_id=0 ){
	$phone = str_replace(' ', '', strtolower(strtoupper2($phone)));

	if( !isphone($phone) ){
		return false;
	}

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( !nlr_subscribers_exists( $id, $nlr_cat_id, $prd_cat_id ) ){
		return false;
	}

	if( newsletter_is_inscripted( $phone, $nlr_cat_id, $prd_cat_id, $sub_usr_id, 'phone' ) ){
		return false;
	}

	global $config;

	$sql = '
		update nlr_subscribers
		set sub_telephone = "'.addslashes($phone).'"
		where sub_tnt_id = '.$config['tnt_id'].'
		and sub_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

// \cond onlyria
/** Cette fonction met à jour l'identifiant de l'adresse mail sur MailJet.
 *	@param string $email Obligatoire, email de l'inscrit
 *	@param $mailjet_id Obligatoire, identifiant du contact sur MailJet
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function nlr_subscribers_update_mailjet_id( $email, $mailjet_id ){
	if( trim($email) == '' ){
		return false;
	}

	if( !is_numeric($mailjet_id) || $mailjet_id<=0 ){
	    return false;
	}

	global $config;

	return ria_mysql_query('
		update nlr_subscribers
		set sub_mailjet_id = '.$mailjet_id.'
		where sub_tnt_id = '.$config['tnt_id'].'
			and sub_email = "'.addslashes( $email ).'"
	');
}
// \endcond

/**	Cette fonction vérifie si une adresse email est inscrite à la newsletter ou non.
 *	@param string $email Obligatoire, adresse email à vérifier
 *	@param int $nlr_cat_id Facultatif, catégorie de newsletter
 *	@param int $prd_cat_id Facultatif, catégorie de produit (annule et remplace $nlr_cat_id si spécifié)
 *	@param int $sub_usr_id Optionnel, identifiant du compte client qui serait inscrit
 *	@param int $type Facultatif, type de newsletter
 *	@return bool true si l'adresse est inscrite
 *	@return bool false si l'adresse n'est pas ou plus inscrite
 */
function newsletter_is_inscripted( $email, $nlr_cat_id=1, $prd_cat_id=0, $sub_usr_id=0, $type='mail' ){

	$email = str_replace(array('votreemailici', ' '), array('', ''), strtolower(strtoupper2($email)));

	if( !trim($email) ){
		return false;
	}

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( $prd_cat_id ){
		$nlr_cat_id = 0;
	}

	global $config;

	// Les inscrits sont ceux dont l'inscription a été confirmée et dont la désinscription ne l'a pas été
	$sql = '
		select sub_id from nlr_subscribers
		where sub_tnt_id = '.$config['tnt_id'].'
		and sub_inscript_confirmed is not null
		and sub_uninscript_confirmed is null
	';

	switch($type){
		case 'phone' : {
			$sql .= ' and sub_telephone = "'.addslashes($email).'"';
			break;
		}
		case 'mail' : {
			$sql .= ' and sub_email = "'.addslashes($email).'"';
			break;
		}
	}

	if( is_numeric($nlr_cat_id) && $nlr_cat_id > 0 ){
		$sql .= ' and sub_cat_id = '.$nlr_cat_id;
	}

	if( is_numeric($prd_cat_id) && $prd_cat_id > 0 ){
		$sql .= ' and sub_prd_cat_id = '.$prd_cat_id;
	}

	if( is_numeric($sub_usr_id) && $sub_usr_id > 0 ){
		$sql .= ' and sub_usr_id = '.$sub_usr_id;
	}

	$rins = ria_mysql_query($sql);

	return $rins ? ria_mysql_num_rows($rins) : false;

}

/**	Cette fonction permet de faire entrer l'adresse email passée en paramètre dans le processus d'inscription.
 *	Elle va envoyer un email de confirmation à l'adresse email demandée, pour confirmer sa validité.
 *
 *	Ceci présente également l'avantage de vérifier que l'adresse email est valide. Le propriétaire
 *	de l'adresse ne commencera à recevoir la newsletter qu'à partir du moment ou son adresse aura
 *	été validée.
 *
 *	Si l'adresse email est déjà inscrite, cette fonction s'interrompt et retourne false.
 *
 *	@param string $email Obligatoire, Adresse email à inscrire.
 *	@param int $nlr_cat_id Facultatif, categorie ou tableau de catégories de newsletters pour l'inscription
 *	@param bool $sendmail Facultatif, par défaut la fonction envoie une confirmation d'inscription, mettre False pour ne pas l'envoyer
 *	@param int $title Facultatif, identifiant RiaShop de civilité
 *	@param string $firstname Facultatif, prénom de l'inscrit
 *	@param string $lastname Facultatif, nom de l'inscrit
 *	@param int $prd_cat_id Facultatif, catégorie de produit.
 *
 *	@return bool False en cas d'échec
 *	@return bool True en cas de succès et $sendmail
 *	@return int L'identifiant de l'inscription si succès et $sendmail = False
 */
function newsletter_request_inscript( $email, $nlr_cat_id=1, $sendmail=true, $title=false, $firstname=false, $lastname=false, $prd_cat_id=0 ){

	// Tente de s'assurer que l'email sera valide (par suppression des espaces et des accents)
	$email = str_replace(' ', '', strtolower(strtoupper2($email)));
	// Supprime également le traditionnel "Votre email ici"
	$email = str_replace('votreemailici', '', $email);

	if( !trim($email) ){
		return false;
	}

	// Vérifie le format de l'adresse email
	if( !isemail($email) ){
		return false;
	}

	$nlr_cat_ids = control_array_integer($nlr_cat_id);

	if( !$nlr_cat_ids ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( !$nlr_cat_ids && !$prd_cat_id ){
		return false;
	}
	$nlr_cat_id_is_zero = false;
	if( $prd_cat_id ){
		$nlr_cat_id_is_zero = true;
		$nlr_cat_ids = array(0);
	}

	foreach($nlr_cat_ids as $index => $nlr_cat_id) {
		// Vérifie que l'adresse n'est pas déjà enregistrée
		if( newsletter_is_inscripted( $email, $nlr_cat_id, $prd_cat_id, 0, 'mail' ) ){
			unset($nlr_cat_ids[$index]);
		}
	}

	if (empty($nlr_cat_ids)) {
		return false;
	}

	global $config;

	$fields = array(
		'sub_tnt_id' => $config['tnt_id'],
		'sub_email' => '"'.addslashes($email).'"',
		'sub_inscript_requested' => 'now()'
	);


	if( $prd_cat_id ){
		$fields['sub_prd_cat_id'] = $prd_cat_id;
	}

	if( $title !== false && gu_titles_exists( $title ) ){
		$fields['sub_title'] = $title;
	}

	if( $firstname !== false && trim($firstname) != '' ){
		$fields['sub_firstname'] = '"'.addslashes(trim($firstname)).'"';
	}

	if( $lastname !== false && trim($lastname) != '' ){
		$fields['sub_lastname'] = '"'.addslashes(trim($lastname)).'"';
	}

	if( !$nlr_cat_id_is_zero ){
		$fields['sub_cat_id'] = 0;
	}

	$sql = '
		insert into nlr_subscribers
			('.implode(', ', array_keys($fields)).')
		values
	';
	$end_index = count($nlr_cat_ids)-1;
	foreach ($nlr_cat_ids as $index => $nlr_cat_id) {
		$values = $fields;
		if( !$nlr_cat_id_is_zero ){
			$values['sub_cat_id'] = $nlr_cat_id;
		}

		$sql .= '('.implode(', ', array_values($values)).')';

		if ($end_index != $index) {
			$sql .= ','.PHP_EOL;
		}else{
			$sql .= ';';
		}
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// enregistre l'origine de l'inscription
	stats_origins_add( $id, CLS_NLR_SUBSCRIBERS );

	$token = nlr_sub_tokens_request_token($email, $nlr_cat_ids);
	nlr_sub_tokens_add($email, $nlr_cat_ids, $token);

	if( !$sendmail ){
		return $token;
	}

	// Charge la configuration de l'alerte mail.
	$rcfg = cfg_emails_get('newsletter');

	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc($rcfg);

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );
	if( $cfg['bcc'] ){
		$mail->addBcc( $cfg['bcc'] );
	}
	if( $cfg['reply-to'] ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	$url = $config['site_url'].'/newsletter/confirmation?code='.$token;

	if( $prd_cat_id ){
		$url .= '&amp;prd_cat='.$prd_cat_id;
	}else{
		if (count($nlr_cat_ids) == 1) {
			$url .= '&amp;nlr='.$nlr_cat_ids[0];
		}
	}

	switch( $config['tnt_id'] ){
		case 29: {
			$name = '';
			if( $prd_cat_id ){
				$name = prd_categories_get_name( $prd_cat_id, true );
			}else{
				$name = nlr_categorie_get_name( $nlr_cat_id );
			}
			if( trim($name)=='' ){
				return false;
			}

			$mail->setSubject( 'Confirmation d\'inscription à la newsletter "'.htmlspecialchars( $name ).'"' );
			$mail->addHtml( $config['email_html_header'] );
			$mail->addParagraph( 'Cher client, Chère cliente,' );
			$mail->addParagraph( 'Une demande d\'inscription à notre lettre d\'information "'.htmlspecialchars( $name ).'" vous concernant a été effectuée sur notre site à '.date('H:i').' aujourd\'hui.' );
			$mail->addParagraph( 'Nous vous envoyons cet email pour confirmer que vous êtes bien l\'auteur de cette demande, et que vous souhaitez effectivement vous inscrire à notre lettre d\'information.' );

			$mail->addParagraph( 'Pour confirmer votre inscription, veuillez vous rendre à l\'adresse ci-dessous : ' );
			$mail->addParagraph( '<a href="'.$url.'">'.$url.'</a>' );

			$mail->addParagraph( 'Si la demande d\'inscription a été effectuée par une autre personne que vous même, veuillez simplement ignorer ce message, et cette demande d\'inscription sera automatiquement annulée.' );

			$mail->addParagraph( 'Si vous avez des questions concernant la procédure d\'inscription à la newsletter, n\'hésitez pas à nous contacter en répondant simplement à cet email.' );

			$mail->addParagraph( "Votre service client,\n".$config['site_name']."\n".'<a href="'.$config['site_url'].'">'.$config['site_url'].'</a>' );
			$mail->addHtml( $config['email_html_footer'] );
			break;
		}
		default : {
			$mail->setSubject( 'Confirmation d\'inscription à la newsletter '.$config['site_name'] );
			$mail->addHtml( $config['email_html_header'] );
			$mail->addParagraph( 'Cher client, Chère cliente,' );
			$mail->addParagraph( 'Une demande d\'inscription à notre lettre d\'information vous concernant a été effectuée sur notre site à '.date('H:i').' aujourd\'hui.' );
			$mail->addParagraph( 'Nous vous envoyons cet email pour confirmer que vous êtes bien l\'auteur de cette demande, et que vous souhaitez effectivement vous inscrire à notre lettre d\'information.' );

			$mail->addParagraph( 'Pour confirmer votre inscription, veuillez vous rendre à l\'adresse ci-dessous : ' );
			$mail->addParagraph( '<a href="'.$url.'">'.$url.'</a>' );

			$mail->addParagraph( 'Si la demande d\'inscription a été effectuée par une autre personne que vous même, veuillez simplement ignorer ce message, et cette demande d\'inscription sera automatiquement annulée.' );

			$mail->addParagraph( 'Si vous avez des questions concernant la procédure d\'inscription à la newsletter, n\'hésitez pas à nous contacter en répondant simplement à cet email.' );

			$mail->addParagraph( "Votre service client,\n".$config['site_name']."\n".'<a href="'.$config['site_url'].'">'.$config['site_url'].'</a>' );
			$mail->addHtml( $config['email_html_footer'] );
			break;
		}
	}

	return $mail->send();
}

/**	Cette fonction va confirmer une inscription à la newsletter. Pour ce faire, elle prend en argument
 *	un code calculé à partir de l'adresse email, de l'heure de demande d'inscription et de l'identifiant
 *	attribué à l'abonné. Cette sécurité est destinée à garantir qu'aucune inscription contre le gré du
 *	propriétaire de l'adresse email n'est lieu.
 *
 *	@param string $code Code de confirmation d'inscription
 *	@param int $nlr_cat_id DEPRECATED, ce paramètre n'est plus pris en compte car le $code est lié aux catégories.
 *	@param int $prd_cat_id Facultatif, catégorie de produit (annule et remplace $nlr_cat_id si spécifié)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function newsletter_confirm_inscript( $code, $nlr_cat_id=1, $prd_cat_id=0 ){

	if( strlen($code) != 32 ){
		return false;
	}

	$nlr_cat_ids = null;

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( $prd_cat_id ){
		$nlr_cat_ids = array(0);
	}

	global $config;


	$r = nlr_sub_tokens_get($code, '', $nlr_cat_ids);
	if (!$r || !ria_mysql_num_rows($r)) {
		return false;
	}

	while ($token = ria_mysql_fetch_assoc($r)) {

		$sql = '
			update nlr_subscribers
			set sub_inscript_confirmed = now(), sub_email_verified = 1
			where sub_tnt_id = '.$config['tnt_id'].'
				and sub_email = "'.$token['email'].'"
				and sub_cat_id = "'.$token['cat_id'].'"
		';
		if( $prd_cat_id ){
			$sql .= ' and sub_prd_cat_id='.$prd_cat_id;
		}

		$res = ria_mysql_query($sql);

		if( $res ){

			$sql = '
				delete from nlr_subscribers
				where sub_tnt_id = '.$config['tnt_id'].'
				and sub_email = "'.addslashes($token['email']).'"
				and ( sub_inscript_confirmed is null or sub_uninscript_confirmed is not null )
				and sub_cat_id = '.$token['cat_id'].'
			';

			if( $prd_cat_id ){
				$sql .= ' and sub_prd_cat_id = '.$prd_cat_id;
			}

			ria_mysql_query($sql);
		}
	}

	return nlr_sub_tokens_validate($code);
}

/**	Cette fonction permet de faire entrer l'adresse email passée en paramètre dans le processus de désinscription.
 *	Elle va envoyer un email de confirmation à l'adresse email demandée, pour confirmer sa validité.
 *	Ceci présente également l'avantage de vérifier que l'adresse email est valide. Le propriétaire
 *	de l'adresse ne sera réellement désinscrit de la newsletter qu'une fois la désinscription confirmée.
 *	@param string $email Adresse email à désinscrire.
 *	@param int $nlr_cat_id Facultatif, categorie ou tableau de catégories de newsletters pour la désinscription
 *	@param int $prd_cat_id Facultatif, catégorie de produit.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function newsletter_request_uninscript( $email, $nlr_cat_id=1, $prd_cat_id=0 ){

	$nlr_cat_ids = control_array_integer($nlr_cat_id);

	if( !$nlr_cat_ids ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( !$nlr_cat_ids && !$prd_cat_id ){
		return false;
	}
	$nlr_cat_id_is_zero = false;
	if( $prd_cat_id ){
		$nlr_cat_id_is_zero = true;
		$nlr_cat_ids = array(0);
	}

	if (empty($nlr_cat_ids)) {
		return false;
	}

	// Tente de s'assurer que l'email sera valide (par suppression des espaces et des accents)
	$email = str_replace(' ', '', strtolower(strtoupper2($email)));

	global $config;

	foreach ($nlr_cat_ids as $key => $nlr_cat_id) {
		$sql = '
			select sub_id from nlr_subscribers
			where sub_tnt_id='.$config['tnt_id'].'
			and sub_email = "'.addslashes($email).'"
			and sub_inscript_confirmed is not null
			and sub_uninscript_confirmed is null
		';

		if( $prd_cat_id ){
			$sql .= ' and sub_prd_cat_id = '.$prd_cat_id;
		}else{
			$sql .= ' and sub_cat_id = '.$nlr_cat_id;
		}

		// Extrait l'enregistrement d'inscription correspondant à l'email
		$rid = ria_mysql_query($sql);
		if( !$rid || !ria_mysql_num_rows($rid) ){
			unset($nlr_cat_ids[$key]);
			continue;
		}

		$id = ria_mysql_result($rid, 0, 0);

		$sql = '
			update nlr_subscribers
			set sub_uninscript_requested = now()
			where sub_tnt_id = '.$config['tnt_id'].'
			and sub_id = '.$id.'
		';

		if( $prd_cat_id ){
			$sql .= ' and sub_prd_cat_id = '.$prd_cat_id;
		}else{
			$sql .= ' and sub_cat_id = '.$nlr_cat_id;
		}

		// Enregistre la date et l'heure de demande de désincription
		ria_mysql_query($sql);
	}

	if (empty($nlr_cat_ids)) {
		return false;
	}

	// Charge la configuration de l'alerte mail.
	$rcfg = cfg_emails_get('newsletter');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc($rcfg);

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );
	if( $cfg['bcc'] ){
		$mail->addBcc( $cfg['bcc'] );
	}
	if( $cfg['reply-to'] ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	$token = nlr_sub_tokens_request_token($email, $nlr_cat_ids, true);
	nlr_sub_tokens_add($email, $nlr_cat_ids, $token, true);

	$url = $config['site_url'].'/newsletter/confirmation-desinscription?code='.$token;

	if( $prd_cat_id ){
		$url .= '&amp;prd_cat='.$prd_cat_id;
	}else{
		if (count($nlr_cat_ids) == 1) {
			$url .= '&amp;nlr='.$nlr_cat_ids[0];
		}
	}

	switch( $config['tnt_id'] ){
		case 29 :{
			$name = '';
			if( $prd_cat_id ){
				$name = prd_categories_get_name( $prd_cat_id, true );
			}else{
				$name = nlr_categorie_get_name( $nlr_cat_id );
			}

			if( trim($name)=='' ){
				return false;
			}

			$mail->setSubject( 'Confirmation de désinscription à la newsletter "'.htmlspecialchars( $name ).'"' );
			$mail->addHtml( $config['email_html_header'] );
			$mail->addParagraph( 'Cher client, Chère cliente,' );
			$mail->addParagraph( 'Une demande de désinscription à notre lettre d\'information "'.htmlspecialchars( $name ).'" vous concernant a été effectuée sur notre site à '.date('H:i').' aujourd\'hui.' );
			$mail->addParagraph( 'Nous vous envoyons cet email pour confirmer que vous êtes bien l\'auteur de cette demande, et que vous souhaitez effectivement vous désinscrire de notre lettre d\'information.' );

			$mail->addParagraph( 'Pour confirmer votre désinscription, veuillez vous rendre à l\'adresse ci-dessous : ' );
			$mail->addParagraph( '<a href="'.$url.'">'.$url.'</a>' );

			$mail->addParagraph( 'Si la demande de désinscription a été effectuée par une autre personne que vous même, veuillez simplement ignorer ce message, et cette demande de désinscription sera automatiquement annulée.' );

			$mail->addParagraph( 'Si vous avez des questions concernant la procédure de désinscription à la newsletter, n\'hésitez pas à nous contacter en répondant simplement à cet email.' );

			$mail->addParagraph( "Votre service client,\n".$config['site_name']."\n".$config['site_url'] );
			break;
		}
		default : {
			$mail->setSubject( 'Confirmation de désinscription à la newsletter '.$config['site_name'] );
			$mail->addHtml( $config['email_html_header'] );
			$mail->addParagraph( 'Cher client, Chère cliente,' );
			$mail->addParagraph( 'Une demande de désinscription à notre '.( $config['tnt_id']!=5 ? 'lettre d\'information' : 'newsletter' ).' vous concernant a été effectuée sur notre site à '.date('H:i').' aujourd\'hui.' );
			$mail->addParagraph( 'Nous vous envoyons cet email pour confirmer que vous êtes bien l\'auteur de cette demande, et que vous souhaitez effectivement vous désinscrire de notre lettre d\'information.' );

			$mail->addParagraph( 'Pour confirmer votre désinscription, veuillez vous rendre à l\'adresse ci-dessous : ' );
			$mail->addParagraph( '<a href="'.$url.'">'.$url.'</a>' );

			$mail->addParagraph( 'Si la demande de désinscription a été effectuée par une autre personne que vous même, veuillez simplement ignorer ce message, et cette demande de désinscription sera automatiquement annulée.' );

			$mail->addParagraph( 'Si vous avez des questions concernant la procédure de désinscription à la newsletter, n\'hésitez pas à nous contacter en répondant simplement à cet email.' );

			$mail->addParagraph( "Votre service client,\n".$config['site_name']."\n".$config['site_url'] );
			break;
		}
	}

	return $mail->send();
}

/**	Cette fonction est chargée de valider une désinscription. La désinscription effective de l'adresse email n'a
 *	réellement lieu qu'une fois l'appel à cette fonction effectué.
 *	@param string $code Code de confirmation de désinscription, fourni dans l'email envoyé par la fonction newsletter_request_uninscript
 *	@param int $nlr_cat_id DEPRECATED, ce paramètre n'est plus pris en compte car le $code est lié aux catégories.
 *	@param int $prd_cat_id Facultatif, catégorie de produit (annule et remplace $nlr_cat_id si spécifié)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function newsletter_confirm_uninscript( $code, $nlr_cat_id=1, $prd_cat_id=0 ){

	if( strlen($code) != 32 ){
		return false;
	}

	$nlr_cat_ids = null;

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	if( $prd_cat_id ){
		$nlr_cat_ids = array(0);
	}

	global $config;

	$r = nlr_sub_tokens_get($code, '', $nlr_cat_ids, false, true);
	if (!$r || !ria_mysql_num_rows($r)) {
		return false;
	}

	$return = true;
	while ($token = ria_mysql_fetch_assoc($r)) {
		$sql = '
			update nlr_subscribers
			set sub_uninscript_confirmed = now()
			where sub_tnt_id = '.$config['tnt_id'].'
			and sub_cat_id = '.$token['cat_id'].'
			and sub_email = "'.$token['email'].'"
		';

		if( $prd_cat_id ){
			$sql .= ' and sub_prd_cat_id='.$prd_cat_id.' ';
		}

		if (!ria_mysql_query($sql)) {
			$return = false;
			error_log('Erreur désinscription : '.$token['email'].' '.$token['token']);
		}
	}

	return nlr_sub_tokens_validate($code, '', null, true);
}

/**	Retourne la liste des catégories où est inscrit un utilisateur.
 *	@param string $email Obligatoire, email de l'utilisateur
 *	@param bool $get_prd_cat Optionnel, indique si on s'intéresse aux catégories de newsletter (False) ou de produits (True).
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant de l'abonné
 *		- cat : Identifiant de catégorie de newsletter (ou de produit)
 *	@return bool False en cas d'échec
 */
function newsletter_users_categorie_get( $email, $get_prd_cat=false ){

	$email = addslashes(strtolower(trim($email)));

	global $config;

	$sql = '
		 select sub_cat_id as id, '.( $get_prd_cat ? 'sub_prd_cat_id' : 'cnt_cat' ).' as cat
		 from nlr_subscribers
	';
	if( !$get_prd_cat ){
		$sql .= ' left join nlr_cat on (sub_cat_id=cnt_id and sub_tnt_id=cnt_tnt_id)';
	}
	$sql .= '
		 where sub_tnt_id='. $config[ 'tnt_id' ] .'
		 and sub_email="'.addslashes($email).'"
		 and sub_uninscript_confirmed is null
		 and sub_inscript_confirmed is not null
		 and sub_date_deleted is null
	';
	if( !$get_prd_cat ){
		$sql .= ' and cnt_date_deleted is null';
	}
	$sql .= '
		 group by cat
	';

	return ria_mysql_query($sql);

}

/** Cette fonction permet de vérifier qu'une adresse mail est bien inscrit à une newsletter en particulier
 *	@param string $email Obligatoire, adresse email
 *	@param int $nlr_id Obligatoire, identifiant de la newsletter
 *	@return bool True si l'adresse mail est bien inscrite, False dans le cas contraire
 */
function newsletter_users_categories_exists( $email, $nlr_id ){
	if( trim($email) == '' ){
		return false;
	}

	if( !is_numeric($nlr_id) || $nlr_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from nlr_subscribers
		where sub_tnt_id = '.$config['tnt_id'].'
			and sub_email = "'.addslashes( $email ).'"
			and sub_uninscript_confirmed is null
			and sub_inscript_confirmed is not null
			and sub_date_deleted is null
			and sub_cat_id = '.$nlr_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/// @}

/** \defgroup model_nlr_categories Catégories de newsletters
 *	\ingroup tools_newsletter
 *	Ce module permet de gérer les différentes catégories de newsletters. Il doit toujours exister une catégorie par défaut pour un locataire.
 *	@{
 */

/**	Cette fonction retourne le ou les catégories disponible(s) selon si une catégorie est indiquée.
 *	@param int|string $categorie Optionnel, l'identifiant de la catégorie recherchée ou son nom
 *	@param int $wst_id Optionnel, identifiant d'un site internet
 *	@return resource la liste des catégories
 */
function nlr_categorie_get( $categorie='', $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	$sql = '
		 select cnt_id as id, cnt_cat as cat, cnt_desc as "desc", cnt_wst_id as wst_id, cnt_mailjet_id as mailjet_id, cnt_type as type
		 from nlr_cat
		 where cnt_tnt_id='. $config[ 'tnt_id' ] .'
	';

	if( $categorie != "" ){
		if( is_numeric($categorie) ){
			$sql .= ' and cnt_id=\''. $categorie . '\'';
		}else{
			$sql .= ' and cnt_cat=\''. addslashes($categorie) . '\'';
		}
	}

	if( $wst_id>0 ){
		$sql .= ' and (cnt_wst_id is null or cnt_wst_id='.$wst_id.')';
	}

	$sql .= ' and cnt_date_deleted is null';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de retourner le nom d'une newsletter.
 *	@param int $nlr_id Obligatoire, identifiant d'une newsletter
 *	@return string Le nom de la newsletter
 */
function nlr_categorie_get_name( $nlr_id ){
	if( !is_numeric($nlr_id) || $nlr_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select cnt_cat as name
		from nlr_cat
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$nlr_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['name'];
}

// \cond onlyria
/**	Ajoute une catégorie de newsletters.
 *	@param $value Valeur de la catégorie à ajouter.
 *	@param int $wst_id Optionnel, identifiant d'un site internet
 *	@param $type Optionnel, type de liste. Les valeurs acceptées sont email et phone.
 *	@return int l'identifiant attributé à la catégorie en cas de succès, false en cas d'échec
 */
function nlr_categorie_add( $value, $wst_id=null, $type='' ){
	global $config;

	if( !trim($value) ){
		return false;
	}

	if( $type=='' ){
		$type = 'email';
	}
	if( $type!='email' && $type!='phone' ){
		return false; // Type non supporté
	}

	$sql = '
		 select cnt_id
		 from nlr_cat
		 where cnt_tnt_id='. $config[ 'tnt_id' ] .'
			and cnt_cat=\''. addslashes($value) .'\'
			and cnt_date_deleted is null
	';

	$cat = ria_mysql_query( $sql );
	if( ria_mysql_num_rows( $cat ) ){
		return false;
	}

	$sql = '
		insert into
			nlr_cat( cnt_tnt_id, cnt_cat, cnt_wst_id, cnt_type )
		values
			( '. $config[ 'tnt_id' ] .',\''. addslashes($value) .'\', '.( is_numeric($wst_id) && $wst_id>0 ? $wst_id : 'null' ).',\'' . $type . '\' );
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

/** Cette fonction permet de vérifier qu'une catégorie existe bien.
 *	@param int $id Obligatoire, identifiant de la catégorie
 *	@param int $wst_id Optionnel, identifiant d'un site internet
 *	@param string $type Optionnel, type de newsletter (valeur acceptée : 'email' ou 'phone')
 *	@return bool True si la catégorie existe bien, False dans le cas contraire
 */
function nlr_categories_exists( $id, $wst_id=0, $type='' ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	if( trim($type) != '' && !in_array($type, ['email', 'phone']) ){
		return false;
	}

	$sql = '
		select 1
		from nlr_cat
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
	';

	if( trim($type) != '' ){
		$sql .= ' and cnt_type = "'.addslashes( $type ).'"';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \cond onlyria
/** Cette fonction permet de mettre à jour les informations sur une newsletter
 *	@param int $nlr_id Obligatoire, identifiant d'une newsletter
 *	@param string $name Obligatoire, nom de la newsletter
 *	@param string $desc Optionnel, description de la newsletter
 *	@param int $wst_id Optionnel, identifiant du site sur lequel l'insciption est possible
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function nlr_categorie_update( $nlr_id, $name, $desc='', $wst_id=0 ){
	if( !nlr_categories_exists($nlr_id) ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		update nlr_cat
		set cnt_cat="'.addslashes( $name ).'",
			cnt_desc='.( trim($desc)!='' ? '"'.addslashes( $desc ).'"' : 'null' ).',
			cnt_wst_id='.( $wst_id>0 ? $wst_id : 'null' ).'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$nlr_id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une newsletter (en utilisant son id).
 *	@param int $nlr_id Obligatoire, identifiant d'une newsletter
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function nlr_categorie_del( $nlr_id ){
	if( !is_numeric($nlr_id) || $nlr_id<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update nlr_cat
		set cnt_date_deleted=now()
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$nlr_id.'
	');
}
// \endcond

// \cond onlyria
/** Suprrime une categorie.
 *	@param $value Valeur de la catégorie à supprimer.
 */
function nlr_categorie_delete( $value ){
	global $config;

	//Recherche de l'identifiant de la catégorie
	$sql = '
		 select cnt_id as id
		 from nlr_cat
		 where cnt_tnt_id='. $config[ 'tnt_id' ] .'
		 and cnt_cat=\''. addslashes($value) .'\'
	';

	$cat = ria_mysql_fetch_array( ria_mysql_query( $sql ) );

	//Supression des emails associer à la catégorie
	$sql = '
		 update nlr_subscribers
		 set sub_date_deleted=now()
		 where sub_tnt_id='. $config[ 'tnt_id' ] .'
		 and sub_cat_id='. $cat[ 'id' ] .'
	';

	ria_mysql_query( $sql );

	//Supression de la catégorie
	$sql = '
		 update nlr_cat
		 set cnt_date_deleted=now()
		 where cnt_tnt_id='. $config[ 'tnt_id' ] .'
		 and cnt_cat=\''. addslashes($value) .'\'
	';

	ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine le nombre d'inscrits confirmés à une catégorie de newsletter (ou de produits) donnée
 *	@param int $nlr_cat_id Optionnel, identifiant de la catégorie. Par défaut, le décompte est global sur toutes les catégories
 *	@param int $prd_cat_id Optionnel, identifiant d'une catégorie de produit.
 *	@return int Le nombre d'inscrits confirmés à la catégorie de newsletter / de produits donnée, False en cas d'échec
 */
function nlr_categorie_mail_count( $nlr_cat_id=0, $prd_cat_id=0 ){

	if( !is_numeric($nlr_cat_id) || $nlr_cat_id < 0 ){
		return false;
	}

	if( !is_numeric($prd_cat_id) || $prd_cat_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		 select
			count(*) as nb_mail
		 from
			nlr_subscribers
		 	left join nlr_cat on sub_cat_id = cnt_id and sub_tnt_id = cnt_tnt_id
		 where
			sub_tnt_id = '. $config['tnt_id'] .'
			and sub_inscript_confirmed is not null
			and sub_uninscript_confirmed is null
			and sub_date_deleted is null
			and cnt_date_deleted is null
	';

	if( $nlr_cat_id > 0 ){
		$sql .= ' and sub_cat_id = '. $nlr_cat_id;
	}

	if( $prd_cat_id > 0 ){
		$sql .= ' and sub_prd_cat_id = '. $prd_cat_id;
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 'nb_mail');

}
// \endcond

/// @}

// \cond onlyria
/** \defgroup model_subscribers_products Historique des notifications aux abonnements produits
 *	\ingroup tools_newsletter
 *	Ce module permet de gérer l'hsitorique des notifications reçues par des adresses email, lorsque des produits sont publiés dans des catégories auxquels l'adresse est abonnée.
 *	@{
 */

/**	Cette fonction crée une ligne d'historique de notification pour une adresse email et un produit donné.
 *
 *	@param string $email Obligatoire, adresse email de l'abonné.
 *	@param int $prd_id Obligatoire, identifiant du produit. Le produit doit exister (attention aux restrictions / mercuriales).
 *	@param $date Optionnel, date spécifique de la notification (si non spécifié, la date courante est utilisée).
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function nlr_subscribers_products_add( $email, $prd_id, $date=false ){

	if( !trim($email) ){
		return false;
	}

	if( !prd_products_exists( $prd_id ) ){
		return false;
	}

	$date = isdateheure($date) ? '"'.dateheureparse($date).'"' : 'now()';

	$email = '"'.addslashes(strtolower(trim($email))).'"';

	global $config;

	$sql = '
		replace into nlr_subscribers_products
			(nsp_tnt_id, nsp_email, nsp_prd_id, nsp_date)
		values
			('.$config['tnt_id'].', '.$email.', '.$prd_id.', '.$date.')
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction supprime une ou des lignes d'historique de notifications d'abonnement à des produits.
 *	Il faut obligatoirement spécifier soit l'email, soit le produit (ou les deux).
 *
 *	@param string $email Optionnel, adresse email dont on souhaite supprimer l'historique.
 *	@param int $prd_id Optionnel, identifiant d'un produit dont on souhaite supprimer l'historique.
 *	@param string $date_start Optionnel, date de début de suppression de l'historique.
 *	@param string $date_end Optionnel, date de fin de suppression de l'historique.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function nlr_subscribers_products_del( $email='', $prd_id=0, $date_start=false, $date_end=false ){

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	if( !trim($email) && !$prd_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from nlr_subscribers_products
		where nsp_tnt_id = '.$config['tnt_id'].'
	';

	if( trim($email) ){
		$sql .= ' and nsp_email = "'.addslashes(strtolower(trim($email))).'"';
	}

	if( $prd_id ){
		$sql .= ' and nsp_prd_id = '.$prd_id;
	}

	if( isdateheure($date_start) ){
		$sql .= ' and nsp_date >= "'.dateheureparse($date_start).'"';
	}

	if( isdateheure($date_end) ){
		$sql .= ' and nsp_date <= "'.dateheureparse($date_end).'"';
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction teste l'existence d'une notification d'abonnement à un produit pour une adresse email donnée.
 *
 *	@param string $email Obligatoire, l'adresse email à tester.
 *	@param int $prd_id Obligatoire, le produit à tester.
 *	@param $multiple_date Optionnel : si activé, un tableau des dates est retourné.
 *
 *	@return bool False en cas d'échec ou si aucune ligne existe.
 *	@return bool True si une ou des lignes existent et $multiple_date non activé.
 *	@return array Un tableau de date si des lignes existent et que $multiple_date est activé.
 */
function nlr_subscribers_products_exists( $email, $prd_id, $multiple_date=false ){

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	if( !trim($email) ){
		return false;
	}

	global $config;

	$sql = '
		select
			nsp_date as "date"
		from
			nlr_subscribers_products
		where
			nsp_tnt_id = '.$config['tnt_id'].'
			and nsp_email = "'.addslashes(strtolower(trim($email))).'"
			and nsp_prd_id = '.$prd_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	if( !$multiple_date ){
		return true;
	}

	$ar_dates = array();

	while( $row = ria_mysql_fetch_assoc($res) ){
		$ar_dates[] = $row['date'];
	}

	return $ar_dates;

}

/**	Cette fonction retourne une ou des lignes de notifications d'abonnement à des produits, suivant des critères optionnels.
 *
 *	@param string $email Optionnel, adresse email ou tableau d'adresses emails.
 *	@param int $prd_id Optionnel, identifiant d'un produit ou tableau d'identifiants de produits.
 *	@param string $date_start Optionnel, date minimale de prise en compte des notifications.
 *	@param string $date_end Optionnel, date maximale de prise en compte des notifications.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- email : adresse email de l'abonné.
 *		- prd_id : identifiant du produit.
 *		- date : date de la notification.
 *		- usr_id : identifiant d'un éventuel compte utilisateur lié par l'adresse email (Null si aucun).
 */
function nlr_subscribers_products_get( $email='', $prd_id=0, $date_start=false, $date_end=false ){

	{ // validation des paramètres

		$prd_id = control_array_integer($prd_id, false);
		if( $prd_id === false ){
			return false;
		}

		$email_tmp = array();
		if( is_array($email) ){
			foreach( $email as $one_email ){
				if( trim($one_email) ){
					$email_tmp[] = addslashes(strtolower(trim($one_email)));
				}
			}
		}else{
			if( trim($email) ){
				$email_tmp[] = addslashes(strtolower(trim($email)));
			}
		}
		$email = $email_tmp;

		$date_start = isdateheure($date_start) ? dateheureparse($date_start) : false;

		$date_end = isdateheure($date_end) ? dateheureparse($date_end) : false;

	}

	global $config;

	$sql = '
		select
			nsp_email as "email", nsp_prd_id as "prd_id", nsp_date as "date", (
				select usr_id from gu_users
				where if(usr_tnt_id = 0, nsp_tnt_id, usr_tnt_id) = nsp_tnt_id and usr_email = nsp_email
				order by usr_can_login desc
				limit 0, 1
			) as "usr_id"
		from
			nlr_subscribers_products
		where
			nsp_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($email) ){
		$sql .= ' and nsp_email in ("'.implode('", "', $email).'")';
	}

	if( sizeof($prd_id) ){
		$sql .= ' and nsp_prd_id in ('.implode(', ', $prd_id).')';
	}

	if( $date_start ){
		$sql .= ' and nsp_date >= "'.$date_start.'"';
	}

	if( $date_end ){
		$sql .= ' and nsp_date <= "'.$date_end.'"';
	}

	return ria_mysql_query($sql);

}

/// @}
// \endcond

// \cond onlyria
/** \defgroup model_subscribers_mailjet Gestion des actions pour MailJet
 *	\ingroup mailjet tools_newsletter
 *	Ce module permet de gérer le lien entre MailJet et les inscriptions RiaShop
 *	@{
 */

/** Cette fonction permet de récupérer les informations liés à MailJet.
 *	@return resource Un résultat MySql contenant :
 *				- api_key : clé privé pour se connecter à l'API MailJet (Login)
 *				- secret_key : clé secrète pour se connecter à l'API MailJet (Password)
 *				- last_update : dernière mise à jour des contacts
 *				- force_update : si oui ou non une demande de mise à jour a été émise depuis l'espace d'administration
 *				- cat_id : l'identifiant de la liste de contact RiaShop sur MailJet
 */
function nlr_mailjet_get(){
	global $config;

	$sql = '
		select nlm_api_key as api_key, nlm_secret_key as secret_key, nlm_last_update as last_update, nlm_force_update as force_update, nlm_cat_id as cat_id
		from nlr_mailjet
		where nlm_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de forcer une mise à jour des contacts
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
 */
function nlr_mailjet_force_update(){
	return nlr_mailjet_set_force_update( true );
}

/** Cette fonction permet de mettre à jour l'information "Forcer une mise à jour"
 *	@param bool $force Optionnel, par défaut réinitialise la valeur (ne pas forcer), mettre True pour forcer une mise
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
 */
function nlr_mailjet_set_force_update( $force=false ){
	global $config;

	$res = ria_mysql_query('
		update nlr_mailjet
		set nlm_force_update = '.( $force ? '1' : '0' ).'
		where nlm_tnt_id = '.$config['tnt_id'].'
	');

	return $res;
}

/** Cette fonction permet de mettre à jour l'identifiant de la liste de contact sur MailJet pour une catégorie de newsletter
 *	@param int $cat_id Obligatoire, identifiant de la liste
 *	@param int $mailjet_id Obligatoire, identifiant de la liste de contacts Mailjet
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
 */
function nlr_mailjet_set_cat_id( $cat_id, $mailjet_id ){
	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}

	if( !is_numeric($mailjet_id) || $mailjet_id<=0 ){
	    return false;
	}

	global $config;

	$res = ria_mysql_query('
		update nlr_cat
		set cnt_mailjet_id = '.$mailjet_id.'
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_id = '.$cat_id.'
	');

	return $res;
}

/** Cette fonction permet de mettre à jour la date de dernier traitement MailJet
 *	@param $date Obligatoire, date de dernier traitement
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function nlr_mailjet_set_last_update( $date ){
	if( !isdateheure($date) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update nlr_mailjet
		set nlm_last_update = "'.$date.'"
		where nlm_tnt_id = "'.$config['tnt_id'].'"
	');
}

/// @}
// \endcond

// \cond onlyria
/** \defgroup nlr_sub_tokens Gestion des token d'inscription au newsletter
 *	\ingroup tools_newsletter
 *	Ce module permet de gérer les inscriptions aux newsletter avec des tokens
 * 	Un token peux permettre la validation de plusieurs newsletter à la fois.
 * 	Cela permet d'envoyer un mail pour s'inscrire ou ce désinscrire de plusieurs newsletter en même temps
 *	@{
 */
/**
 * Cette fonction permet de valider un token d'inscription ou de désinscription
 *
 * @param string $token Facultatif, le token a validé
 * @param string $email Facultatif, l'email à validé
 * @param array|integer $newsletters_categories_ids Facultatif, identifiant des catégories de newsletter à valider.
 * @param boolean $is_desincription Facultatif, Si c'est une désinscription ou une inscription
 * @return boolean Retourne true si success, false si erreur
 */
function nlr_sub_tokens_validate($token='', $email='', $newsletters_categories_ids=null, $is_desincription=false){
	if (trim($email) && !isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	if (!is_string($token)) {
		throw new Exception("token doit être une chaine de caractère valide");
	}

	if (!is_null($newsletters_categories_ids)){
		$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

		if (!$newsletters_categories_ids) {
			throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
		}
	}

	if (!trim($token) && !trim($email)) {
		throw new Exception("Il faut au moins un token ou un email pour valider des lignes de token");
	}

	global $config;

	$sql = '
		update nlr_sub_tokens
			set sbt_is_validated = 1,
			sbt_date_validated = now()
		where sbt_tnt_id = '.$config['tnt_id'].'
			and sbt_is_validated = 0
	';

	if (trim($token)) {
		$sql .= ' and sbt_token="'.$token.'" ';
	}

	if (trim($email)) {
		$sql .= ' and sbt_email="'.$email.'"';
	}

	//TODO date d'expiration des tokens en fonction de la date de creation

	if (!is_null($is_desincription)) {
		$sql .= ' and sbt_is_uninscription=' . ($is_desincription ? '1' : '0'). ' ';
	}

	if (!is_null($newsletters_categories_ids)) {
		$sql .= ' and sbt_cat_id in (' . implode(', ', $newsletters_categories_ids). ') ';
	}

	return ria_mysql_query($sql);
}
/**
 * Cette fonction permet de récupérer le token en le récupérent si il existe ou en créant un token.
 *
 * @param string $email Email de l'utilisateur demandeur de token
 * @param array|integer $newsletters_categories_ids Catégorie de newsletter
 * @param boolean $is_desincription Facultatif, si c'est un token de désinscription ou non
 * @return string|boolean Retourne le token, false si erreur
 */
function nlr_sub_tokens_request_token($email, $newsletters_categories_ids, $is_desincription=false){
	global $config;
	try{
		$token = nlr_sub_tokens_get_token($email, $newsletters_categories_ids, $is_desincription);

		if (!$token) {
			$token = nlr_sub_tokens_generate_token($email, $newsletters_categories_ids);
		}

		return $token;

	}catch(Exception $e) {
		error_log('Error : module newsletter ['.$config['tnt_id'].']'.$e->getMessage());
		return false;
	}
}
/**
 * Cette fonction permet de récupérer le token pour un email et une catégorie de newsletter
 *
 * @param string $email Email du demandeur de token
 * @param integer|array $newsletters_categories_ids Identifiant ou tableau d'identifiants de catégorie de newsletter
 * @param boolean $is_desincription Facultatif, récupère si c'est un token d'inscription ou de désinscription
 * @return string|boolean Retourne le token si il existe sinon false
 */
function nlr_sub_tokens_get_token($email, $newsletters_categories_ids, $is_desincription=false){
	if (!isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

	if (!$newsletters_categories_ids) {
		throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
	}

	global $config;

	$sql = '
		select sbt_token as token
		from nlr_sub_tokens
		where sbt_tnt_id='.$config['tnt_id'].'
			and sbt_email="'.$email.'"
			and sbt_cat_id in (' . implode(', ', $newsletters_categories_ids). ')
	';

	if (!is_null($is_desincription)) {
		$sql .= ' and sbt_is_uninscription=' . ($is_desincription ? '1' : '0'). ' ';
	}

	$sql .= '
		group by sbt_email, sbt_token
	';

	$r_token = ria_mysql_query($sql);

	if (!$r_token || !ria_mysql_num_rows($r_token)) {
		return false;
	}

	$token = ria_mysql_fetch_assoc($r_token);

	return $token['token'];
}

 /**
  * Cette fonction permet la génération d'un token pour un email et une ou plusieurs newsletter
  *
  * @param string $email Email qui veux s'inscrire à la newsletter
  * @param integer|array $newsletters_categories_ids Identifiant ou tableau d'identifiants de newsletter
  * @return string Retourne le token
  */
function nlr_sub_tokens_generate_token($email, $newsletters_categories_ids){
	if (!isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

	if (!$newsletters_categories_ids) {
		throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
	}
	global $config;

	return md5($email.implode(',', $newsletters_categories_ids).uniqid());
}
/**
 * Cette fonction permet de récupérer les token de demande d'inscription
 *
 * @param string $token Facultatif, le token envoyé par email
 * @param string $email Facultatif, l'email lié au token
 * @param integer|array $newsletters_categories_ids Facultatif, identifiant ou tableau des catégories de newsletter
 * @param boolean $validated Facultatif, si c'est un token validé ou non
 * @param boolean $is_desincription Facultatif, si c'est un token de désinscription ou non
 * @return resource Resource mysql avec les clé suivante :
 * 						- email, email de l'utilisateur
 * 						- cat_id, identifiant de la catégorie de newsletter
 * 						- token, token de demande
 * 						- date_created, date de la création du token
 * 						- date_validated, date de validation
 * 						- is_validated, si c'est validé
 * 						- is_uninscription, si c'est un token de désinscription
 */
function nlr_sub_tokens_get($token='', $email='', $newsletters_categories_ids=null, $validated=false, $is_desincription=false){
	if (trim($email) && !isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	if (!is_string($token)) {
		throw new Exception("token doit être une chaine de caractère valide");
	}

	if (!is_null($newsletters_categories_ids)){
		$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

		if (!$newsletters_categories_ids) {
			throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
		}
	}

	global $config;

	$sql = '
		select
			sbt_id as id,
			sbt_email as email,
			sbt_cat_id as cat_id,
			sbt_token as token,
			sbt_date_created as date_created,
			sbt_date_validated as date_validated,
			sbt_is_validated as is_validated,
			sbt_is_uninscription as is_uninscription
		from nlr_sub_tokens
		where sbt_tnt_id='.$config['tnt_id'].'
	';

	if (trim($token)) {
		$sql .= ' and sbt_token="'.$token.'" ';
	}

	if (trim($email)) {
		$sql .= ' and sbt_email="'.$email.'"';
	}

	//TODO date d'expiration des tokens en fonction de la date de creation

	if (!is_null($validated)) {
		$sql .= ' and sbt_is_validated=' . ($validated ? '1' : '0'). ' ';
	}

	if (!is_null($is_desincription)) {
		$sql .= ' and sbt_is_uninscription=' . ($is_desincription ? '1' : '0'). ' ';
	}

	if (!is_null($newsletters_categories_ids)) {
		$sql .= ' and sbt_cat_id in (' . implode(', ', $newsletters_categories_ids). ') ';
	}

	return ria_mysql_query($sql);
}

/**
 * Cette fonction permet de vérifier l'existance d'un token pour une catégorie et un email
 *	TODO date d'expiration des tokens en fonction de la date de creation
 * @param string $email Email de l'utilisateur pour qui il faut vérifier l'existance d'une demande dinscription
 * @param array|integer $newsletters_categories_ids Identifiant ou tableau d'identifiants de newsletter
 * @param boolean $validated Facultatif, permet de vérifier l'existance d'un token pour une inscription non validé
 * @param boolean $is_desincription Facultatif, permet de vérifier l'existance d'un token de désinscription
 * @return boolean Retourne True si il en existe déjà un, sinon false
 */
function nlr_sub_tokens_exists($email, $newsletters_categories_ids, $validated=false, $is_desincription=false){
	if (!isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

	if (!$newsletters_categories_ids) {
		throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
	}

	global $config;

	$sql = '
		select 1 from nlr_sub_tokens
		where sbt_tnt_id='.$config['tnt_id'].'
			and sbt_email="'.$email.'"
			and sbt_cat_id in ('.implode(',', $newsletters_categories_ids).')
	';

	//TODO date d'expiration des tokens en fonction de la date de creation

	if (!is_null($validated)) {
		$sql .= ' and sbt_is_validated=' . ($validated ? '1' : '0'). ' ';
	}

	if (!is_null($is_desincription)) {
		$sql .= ' and sbt_is_uninscription=' . ($is_desincription ? '1' : '0'). ' ';
	}

	$r = ria_mysql_query($sql);

	return $r && ria_mysql_num_rows($r);
}

/**
 * Cette fonction permet d'insérer un token par email pour plusieurs catégorie de newsletter
 *
 * @param string $email Email de l'utilisateur qui veux s'inscrire
 * @param integer|array $newsletters_categories_ids Identifiant ou tableau d'identifiants de newsletter
 * @param string $token Token de la newsletter
 * @param boolean $is_desincription Facultatif, détermine si c'est un token d'inscription ou de désinscription
 * @return boolean Retourne true si l'insertion est réusite false au contraire
 */
function nlr_sub_tokens_add($email, $newsletters_categories_ids, $token, $is_desincription=false){

	if (!isemail($email)) {
		throw new Exception("email doit être une chaine de caractère valide");
	}

	if (!is_string($token) || !trim($token)) {
		throw new Exception("token doit être une chaine de caractère valide");
	}

	$newsletters_categories_ids = control_array_integer($newsletters_categories_ids, true, true);

	if (!$newsletters_categories_ids) {
		throw new Exception("newsletters_categories_ids doit être un identifiant ou un tableau d'identifiant");
	}

	if (nlr_sub_tokens_exists($email, $newsletters_categories_ids, false, $is_desincription)) {
		return true;
	}

	global $config;

	$fields = array(
		'sbt_tnt_id',
		'sbt_cat_id',
		'sbt_email',
		'sbt_token',
		'sbt_date_created'
	);

	if ($is_desincription) {
		$fields[] = 'sbt_is_uninscription';
	}

	$sql = '
		insert into nlr_sub_tokens
			('.implode(',', $fields).')
		values
	';

	//ajout d'une ligne par catégorie de newsletter
	$ending_index = count($newsletters_categories_ids)-1;
	foreach ($newsletters_categories_ids as $index => $newsletter_id) {
		$values = array();
		$values[] = $config['tnt_id'];
		$values[] = $newsletter_id;
		$values[] = '"'.$email.'"';
		$values[] = '"'.$token.'"';
		$values[] = 'now()';

		if ($is_desincription) {
			$values[] = '1';
		}

		$sql .= '('.implode(', ', $values).')';

		if ($index == $ending_index) {
			$sql .= ';';
		}else{
			$sql .= ','.PHP_EOL;
		}
	}

	return ria_mysql_query($sql);
}
/// @}
// \endcond


<?php

require_once('users.inc.php');

/** \defgroup model_relation Relations entre les objets
 *	\ingroup system
 *	Ce module comprend les fonctions nécessaires à la gestion des relations entre les objets
 */

/// Le produit de destination est déjà dans l'arborescence de la source
define('ERR_REL_IN_PARENTS', -1);
/// Le produit source est déjà dans l'arborescence de la destination
define('ERR_REL_IN_CHILDS', -2);
/// La relation existe déjà
define('ERR_REL_EXISTS', -3);

/** Affiche à l'utilisateur un message lui décrivant la raison de l'échec de l'ajout d'une relation
 *	@param int $error_id Obligatoire, Identifiant de l'erreur, tel que défini dans la liste de constantes
 *	@return string Message d'erreur explicite
 */
function rel_err_describe( $error_id ){

	switch($error_id){
		case ERR_REL_IN_PARENTS:
			$message = _("L'objet que vous tentez d'ajouter comme enfant est déjà présent dans l'arborescence de l'objet source.");
			break;
		case ERR_REL_IN_CHILDS:
			$message = _("L'objet que vous tentez d'ajouter comme parent est déjà présent dans l'arborescence de l'objet source.");
			break;
		case ERR_REL_EXISTS:
			$message = _("La relation existe déjà.");
			break;
	}

	return $message;
}

/**	Cette fonction permet l'ajout d'un type de relation entre deux objets
 *	@param string $name Obligatoire, désignation du type de relation, au singulier
 *	@param $plural Obligatoire, désignation du type de relation, au pluriel
 *	@param int $src_cls_id Obligatoire, Identifiant de la classe source
 *	@param int $dst_cls_id Obligatoire, Identifiant de la classe de destination
 *	@return int l'identifiant attribué au type de relation en cas de succès, false en cas d'échec
 */
function rel_relations_types_add( $name, $plural, $src_cls_id, $dst_cls_id ){
	if( !is_numeric($src_cls_id) ){
		return false;
	}
	if( !is_numeric($dst_cls_id) ){
		return false;
	}
	$name = ucfirst(trim($name));
	if( !$name ){
		return false;
	}

	$plural = ucfirst(trim($plural));
	if( !$plural ){
		return false;
	}

	global $config;

	$sql = '
		insert into rel_relations_types
			(rrt_tnt_id, rrt_src_cls, rrt_dst_cls, rrt_name, rrt_name_plural, rrt_date_created)
		values
			('.$config['tnt_id'].', '.$src_cls_id.', '.$dst_cls_id.', "'.addslashes($name).'", "'.addslashes($plural).'", now)
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction permet la mise à jour d'un type de relation entre les objets
 * 	@todo Le changement des classes n'est pas permis, idéalement il faudrait le faire mais aussi controler les données dans la table rel_relations_objects
 *	@param int $id Obligatoire, identifiant du type de relation
 *	@param string $name Obligatoire, désignation du type de relation, au singulier
 *	@param $plural Obligatoire, désignation du type de relation, au pluriel
 *	@return bool true en cas de succès, false en cas d'échec
 * 	@todo Le changement des classes n'est pas permis, idéalement il faudrait le faire mais aussi controler les données dans la table rel_relations_objects
 */
function rel_relations_types_update( $id, $name, $plural ){

	if( !rel_relations_types_exists( $id ) ){
		return false;
	}

	$name = ucfirst(trim($name));
	if( !$name ){
		return false;
	}

	$plural = ucfirst(trim($plural));
	if( !$plural ){
		return false;
	}

	global $config;

	$sql = '
		update rel_relations_types
		set rrt_name = "'.addslashes($name).'", rrt_name_plural = "'.addslashes($plural).'"
		where rrt_tnt_id = '.$config['tnt_id'].' and rrt_id = '.$id.' and rrt_date_deleted is null
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction permet la vérification d'un identifiant de type de relation
 *	@param int $id Obligatoire, identifiant du type de relation
 *	@return bool true si l'identifiant est valide, false dans le cas contraire
 */
function rel_relations_types_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select rrt_id from rel_relations_types
		where (rrt_tnt_id='.$config['tnt_id'].' or rrt_tnt_id=0) and rrt_id = '.$id.'
		and rrt_date_deleted is null
	';

	$r_exists = ria_mysql_query($sql);
	if( !$r_exists ){
		return false;
	}

	return ria_mysql_num_rows($r_exists);

}

/**	Cette fonction supprime un type de relation.
 *	@param int $id Obligatoire, identifiant du type à supprimer.
 *	@param $del_rel Optionnel, détermine si les relations existantes sont supprimées (sinon, la suppression est refusée si des relations existent).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function rel_relations_types_del( $id, $del_rel=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !rel_relations_types_exists( $id ) ){
		return true;
	}

	// charge les relations existantes
	$rrel = rel_relations_get( 0, null, null, $id );
	if( !$rrel ){
		return false;
	}

	if( ria_mysql_num_rows($rrel) ){
		if( $del_rel ){
			// supprime chaque relation
			while( $rel = ria_mysql_fetch_assoc($rrel) ){
				rel_relations_del( $rel['id'] );
			}
		}else{
			return false;
		}
	}

	global $config;

	$sql = '
		update rel_relations_types
		set rrt_date_deleted = now()
		where rrt_tnt_id = '.$config['tnt_id'].' and rrt_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction permet le chargement d'un ou plusieurs types de relations.
 *	Les type de relations sont triées par leur nom.
 *	@param int $id Facultatif, identifiant d'un type de relation sur lequel filtrer le résultat (ou tableau)
 *	@param int $src_cls Facultatif, identifiant d'une classe de relation en source
 *	@param int $dst_cls Facultatif, identifiant d'une classe de relation en destinations
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de relation
 *			- name : désignation du type de relation
 *			- name_plural : désignation du type de relation, au pluriel
 *			- date_modified : date de dernière modification
 *			- src_cls : identifiant de la classe source
 *			- dst_cls : identifiant de la classe de destination
 */
function rel_relations_types_get( $id=0, $src_cls=0, $dst_cls=0 ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}
	$src_cls = control_array_integer( $src_cls, false );
	if( $src_cls === false ){
		return false;
	}
	$dst_cls = control_array_integer( $dst_cls, false );
	if( $dst_cls === false ){
		return false;
	}

	global $config;

	$sql = '
		select rrt_id as id, rrt_name as name, rrt_name_plural as name_plural, rrt_date_modified as date_modified,
			rrt_src_cls as src_cls, rrt_dst_cls as dst_cls
		from rel_relations_types
		where (rrt_tnt_id = '.$config['tnt_id'].' or rrt_tnt_id = 0)
			and rrt_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and rrt_id in ('.implode(', ', $id).')';
	}
	if( sizeof($src_cls) ){
		$sql .= ' and rrt_src_cls in ('.implode(', ', $src_cls).')';
	}
	if( sizeof($dst_cls) ){
		$sql .= ' and rrt_dst_cls in ('.implode(', ', $dst_cls).')';
	}

	$sql .= '
		order by rrt_name
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction permet l'ajout d'une relation entre deux objets
 *	@param int|array $src Obligatoire, identifiant de l'objet source ou tableau d'identifiants dans le cas de clé composé
 *	@param int|array $dst Optionnel, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@param int $type Obligatoire, identifiant du type de relation
 *	@param bool $rebuild Facultatif, permet de rebuilder la hiérachie ou non, a utiliser avec précaution
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rel_relations_add( $src, $dst, $type, $ref=null, $rebuild=true ){
	global $config;

	$src = control_array_integer( $src, true, true, true );
	if( $src===false || !$src[0] ){
		return false;
	}
	$dst = control_array_integer( $dst, true, true, true );
	if( $dst===false || !$dst[0] ){
		return false;
	}

	while( sizeof($src) < COUNT_OBJ_ID ){
		$src[] = 0;
	}
	while( sizeof($dst) < COUNT_OBJ_ID ){
		$dst[] = 0;
	}

	if( !is_numeric($type) || $type<=0 ){
		return false;
	}

	// si la relation existe
	if( rel_relations_exists($src, $dst, $type) ){
		return ERR_REL_EXISTS;
	}

	// récupère le type de classe
	$rtype = rel_relations_types_get($type);
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		return false;
	}
	$type = ria_mysql_fetch_assoc($rtype);

	// si la classe source et destination est la même on controle que l'objet ne ce référence pas lui même
	if( $type['src_cls'] == $type['dst_cls'] ){
		if( $src[0] == $dst[0] && $src[1] == $dst[1] && $src[2] == $dst[2] ){
			return ERR_REL_IN_PARENTS;
		}
	}

	// récupèration des objets parents de l'objet à mettre en parent
	$parents = rel_relations_get_parents($type['id'], $src);
	if( $parents && sizeof($parents) ){
		foreach( $parents as $parent ){
			// Il n'est pas possible de mettre l'objet source en parent, d'un parent déja présent dans sa propre arborescence ( boucle infinie )
			if( $parent['obj'][0] == $dst[0] && $parent['obj'][1] == $dst[1] && $parent['obj'][2] == $dst[2]){
				return ERR_REL_IN_PARENTS;
			}
		}
	}
	// récupèration des objets enfants de l'objet à mettre en enfant
	$childs = rel_relations_get_childs($type['id'], $dst);
	if( $childs && sizeof($childs) ){
		foreach( $childs as $child ){
			// Il n'est pas possible de mettre l'objet destination en enfant, d'un enfant déja présent dans sa propre arborescence ( boucle infinie )
			if( $child['obj'][0] == $dst[0] && $child['obj'][1] == $dst[1] && $child['obj'][2] == $dst[2]){
				return ERR_REL_IN_CHILDS;
			}
		}
	}

	$pos = 0;

	$sql = '
		select ifnull(max(rro_pos) + 1, 0) as max_pos
		from rel_relations_objects
		where rro_tnt_id='.$config['tnt_id'].' and rro_rrt_id='.$type['id'].' and rro_date_deleted is null
		and rro_src_0='.$src[0].' and rro_src_1='.$src[1].' and rro_src_2='.$src[2].'
	';
	$rpos = ria_mysql_query($sql);

	if( $rpos && ria_mysql_num_rows($rpos) ){
		$pos = ria_mysql_result($rpos, 0, 'max_pos');
	}

	$sqlref = 'null';
	if( $ref !== null ){
		$sqlref = '\''.addslashes($ref).'\'';
	}

	$res = ria_mysql_query('
		insert into rel_relations_objects
			(rro_tnt_id,rro_src_0,rro_src_1,rro_src_2,rro_dst_0,rro_dst_1,rro_dst_2,rro_rrt_id,rro_pos,rro_ref,rro_date_created)
		values
			('.$config['tnt_id'].','.$src[0].','.$src[1].','.$src[2].','.$dst[0].','.$dst[1].','.$dst[2].','.$type['id'].','.$pos.','.$sqlref.',now())
	');

	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();
	if( !$id ){
		return false;
	}

	// régénération de la hiérachies
	if( $rebuild ){
		rel_relations_hierarchy_rebuild( $type['id'], $dst);
	}

	return $id;
}

/**	Cette fonction permet la suppression des relations pour un objet
 *	@param int $cls_id Obligatoire, identifiant de la classe de l'objet
 *	@param int|array $obj Obligatoire, identifiant de l'objet  ou tableau d'identifiants dans le cas de clé composé
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rel_relations_del_by_class( $cls_id, $obj ){
	if( !$cls_id || !is_numeric($cls_id) ){
		return false;
	}
	$obj = control_array_integer( $obj, true, true, true );
	if( $obj==false || !$obj[0] ){
		return false;
	}

	while( sizeof($obj) < COUNT_OBJ_ID ){
		$obj[] = 0;
	}

	// récupère tous les types de relations pour la classe donnée en tant que source
	$rtype = rel_relations_types_get( 0, $cls_id, 0 );
	if( $rtype ){
		while( $type = ria_mysql_fetch_assoc($rtype) ){
			if( !rel_relations_del(0, $obj, 0, $type['id']) ){
				return false;
			}
		}
	}
	// récupère tous les types de relations pour la classe donnée en tant que destination
	$rtype = rel_relations_types_get( 0, 0, $cls_id );
	if( $rtype ){
		while( $type = ria_mysql_fetch_assoc($rtype) ){
			if( !rel_relations_del(0, 0, $obj, $type['id']) ){
				return false;
			}
		}
	}

	return true;
}

/**	Cette fonction permet la suppression d'une relation entre deux objets
 *	@param int $id Obligatoire, identifiant de la relation
 *	@param int|array $src Obligatoire, identifiant de l'objet source ou tableau d'identifiants dans le cas de clé composé
 *	@param int|array $dst Optionnel, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@param int $type Optionnel, identifiant du type de relation
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rel_relations_del( $id=0, $src=0, $dst=0, $type=0 ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}
	if( !is_numeric($type) ){
		return false;
	}

	$src = control_array_integer( $src, false, true, true );
	if( $src==false || !$src[0] ){
		$src = array(0);
	}
	$dst = control_array_integer( $dst, false, true, true );
	if( $dst==false || !$dst[0] ){
		$dst = array(0);
	}

	while( sizeof($src) < COUNT_OBJ_ID ){
		$src[] = 0;
	}
	while( sizeof($dst) < COUNT_OBJ_ID ){
		$dst[] = 0;
	}

	if( $id == 0 && $src[0] == 0 && $dst[0] == 0 && $type == 0 ){
		return false;
	}

	// le type de la relations est obligatoire dans le cas ou nous passons par la source ou la destination
	if( ($src[0] != 0 || $dst[0] != 0) && $type == 0 && $id==0 ){
		return false;
	}

	// dans le cas d'une suppression via l'id, celui-ci prend le dessus
	if( $id ){
		$src = array(0, 0, 0);
		$dst = array(0, 0, 0);
	}

	$date_deleted = date('Y-m-d H:i:s');

	$sql = '
		update rel_relations_objects set rro_date_deleted=\''.$date_deleted.'\'
		where rro_tnt_id='.$config['tnt_id'].' and rro_date_deleted is null';

	if( $id > 0 ){
		$sql .= ' and rro_id='.$id;
	}else{
		if( $type > 0 ){
			$sql .= ' and rro_rrt_id='.$type;
		}
		if( $dst[0] != 0 ){
			$sql .= ' and rro_dst_0='.$dst[0];
			$sql .= ' and rro_dst_1='.$dst[1];
			$sql .= ' and rro_dst_2='.$dst[2];
		}
		if( $src[0] != 0 ){
			$sql .= ' and rro_src_0='.$src[0];
			$sql .= ' and rro_src_1='.$src[1];
			$sql .= ' and rro_src_2='.$src[2];
		}
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// si nous avons supprimé avec l'id on récupère les données source et destination de la relation pour recalculer la position et hiérarchi plus tard
	if( $id ){
		$rdata = ria_mysql_query('select rro_rrt_id, rro_src_0, rro_src_1, rro_src_2, rro_dst_0, rro_dst_1, rro_dst_2 from rel_relations_objects where rro_tnt_id='.$config['tnt_id'].' and rro_id='.$id);
		if( $rdata && ria_mysql_num_rows($rdata) ){
			$data = ria_mysql_fetch_assoc($rdata);
			$src = array( $data['rro_src_0'], $data['rro_src_1'], $data['rro_src_2'] );
			$dst = array( $data['rro_dst_0'], $data['rro_dst_1'], $data['rro_dst_2'] );
			$type = $data['rro_rrt_id'];
		}
	}

	// on repositionne les relation entre elles (pour éviter les trous)
	if( $dst[0]!=0 ){

		$sql = '
			select rro_rrt_id, rro_src_0, rro_src_1, rro_src_2, rro_id
			from rel_relations_objects r1
			where rro_tnt_id='.$config['tnt_id'].' and rro_date_deleted is null
		';

		if( $type > 0 ){
			$sql .= ' and rro_rrt_id='.$type;
		}

		if( $src[0] != 0 ){
			$sql .= ' and rro_src_0='.$src[0];
			$sql .= ' and rro_src_1='.$src[1];
			$sql .= ' and rro_src_2='.$src[2];
		}elseif( $dst[0] != 0 ){
			$sql .= ' and exists (';
			$sql .= ' select 1 ' ;
			$sql .= ' from rel_relations_objects r2 ' ;
			$sql .= ' where r2.rro_tnt_id='.$config['tnt_id'] ;
			$sql .= ' 	and r2.rro_dst_0='.$dst[0] ;
			$sql .= ' 	and r2.rro_dst_1='.$dst[1];
			$sql .= ' 	and r2.rro_dst_2='.$dst[2];
			$sql .= ' 	and r2.rro_date_deleted is null';
			$sql .= ' 	and r2.rro_src_0=r1.rro_src_0 ';
			$sql .= ' 	and r2.rro_src_1=r1.rro_src_1 ';
			$sql .= ' 	and r2.rro_src_2=r1.rro_src_2 ';
			$sql .= ' ) ';
		}

		$sql .= ' order by rro_rrt_id asc, rro_src_0 asc, rro_src_1 asc, rro_src_2 asc, rro_pos asc';

		$rrel = ria_mysql_query($sql);

		if( $rrel && ria_mysql_num_rows($rrel) ){
			$pos = 0;
			$last_key = false;
			while( $t = ria_mysql_fetch_assoc($rrel) ){
				if( $last_key != $t['rro_rrt_id'] ){
					$pos = 0;
				}
				$last_key = $t['rro_rrt_id'];

				$sql = '
					update rel_relations_objects
					set rro_pos='.$pos.'
					where rro_tnt_id='.$config['tnt_id'].'
						and rro_id='.$t['rro_id'].'
				';

				ria_mysql_query($sql);
				$pos++;
			}
		}
	}

	// régénération de la hiérachies pour toutes les destinations supprimé
	if( $dst[0]!=0){
		rel_relations_hierarchy_rebuild( $type, $dst);
	}
	else if( $src[0]!=0 ){

		$rdata = ria_mysql_query('select rro_rrt_id, rro_dst_0, rro_dst_1, rro_dst_2 from rel_relations_objects where rro_tnt_id='.$config['tnt_id'].' and rro_rrt_id='.$type.' and rro_src_0='.$src[0].' and rro_src_1='.$src[1].' and rro_src_2='.$src[2].' and rro_date_deleted =\''.$date_deleted.'\' ');
		if( $rdata && ria_mysql_num_rows($rdata) ){
			while( $data = ria_mysql_fetch_assoc($rdata) ){
				rel_relations_hierarchy_rebuild( $data['rro_rrt_id'], array($data['rro_dst_0'], $data['rro_dst_1'], $data['rro_dst_2']));
			}
		}
	}

	return true;
}

/**	Cette fonction permet le chargement d'une ou plusieurs relations entre objets
 *	@param int $id Facultatif, identifiant d'une relation
 *	@param int|array $src Obligatoire, identifiant de l'objet source ou tableau d'identifiants dans le cas de clé composée
 *	@param int|array $dst Optionnel, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composée
 *	@param int $type Facultatif, identifiant ou tableau d'identifiants de types de relation sur lequel filtrer le résultat (ou tableau d'identifiants)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la relation
 *			- type_id : identifiant du type de la relation
 *			- type_name : nom du type de la relation
 *			- type_name_plural : nom au pluriel du type de la relation
 *			- src_cls : identifiant de la classe source
 *			- src_0 : identifiant 0 de l'objet source
 *			- src_1 : identifiant 1 de l'objet source
 *			- src_2 : identifiant 2 de l'objet source
 *			- dst_cls : identifiant de la classe de destination
 *			- dst_0 : identifiant 0 de l'objet de destination
 *			- dst_1 : identifiant 1 de l'objet de destination
 *			- dst_2 : identifiant 2 de l'objet de destination
 *			- ref : référence de la relation
 *			- pos : position de la destination dans la liste des objets destinations
 *			- date_created : date de création de la relation
 *			- date_modified : date de modification de la relation
 *	@return bool False en cas d'échec.
 */
function rel_relations_get( $id=0, $src=null, $dst=null, $type=null, $ref=null ){
	if( !is_numeric($id) ){
		return false;
	}

	$src = $src === null ? 0 : $src;
	$src = control_array_integer( $src, false, true, true );
	if( $src==false || !$src[0] ){
		$src = array(0);
	}

	$dst = $dst === null ? 0 : $dst;
	$dst = control_array_integer( $dst, false, true, true );
	if( $dst==false || !$dst[0] ){
		$dst = array(0);
	}

	while( sizeof($src) < COUNT_OBJ_ID ){
		$src[] = 0;
	}
	while( sizeof($dst) < COUNT_OBJ_ID ){
		$dst[] = 0;
	}

	$type = $type === null ? 0 : $type;
	$type = control_array_integer( $type, false );
	if( $type === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			rro_id as id, rrt_id as type_id, rrt_name as type_name, rrt_name_plural as type_name_plural,
			rrt_src_cls as src_cls, rro_src_0 as src_0, rro_src_1 as src_1, rro_src_2 as src_2,
			rrt_dst_cls as dst_cls, rro_dst_0 as dst_0, rro_dst_1 as dst_1, rro_dst_2 as dst_2,
			rro_pos as pos,
			rro_date_created as date_created, rro_date_modified as date_modified
		from
			rel_relations_objects
			join rel_relations_types on ( rro_tnt_id = rrt_tnt_id or 0 = rrt_tnt_id ) and rro_rrt_id = rrt_id
		where
			rro_tnt_id = '.$config['tnt_id'].'
			and rro_date_deleted is null
			and rrt_date_deleted is null
	';

	if( $id > 0 ){
		$sql .= ' and rro_id='.$id;
	}
	if( $type > 0 ){
		$sql .= ' and rro_rrt_id in ('.implode(',', $type).')';
	}
	if( $dst[0] != 0 ){
		$sql .= ' and rro_dst_0='.$dst[0];
		$sql .= ' and rro_dst_1='.$dst[1];
		$sql .= ' and rro_dst_2='.$dst[2];
	}
	if( $src[0] != 0 ){
		$sql .= ' and rro_src_0='.$src[0];
		$sql .= ' and rro_src_1='.$src[1];
		$sql .= ' and rro_src_2='.$src[2];
	}
	if( $ref!=null ){
		$sql .= ' and rro_ref=\''.addslashes($ref).'\'';
	}

	$sql .= '
		order by rro_pos asc
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

/**	Cette fonction permet de tester l'existance d'une relation
 *	@param int|array $src Obligatoire, identifiant de l'objet source ou tableau d'identifiants dans le cas de clé composé
 *	@param int|array $dst Obligatoire, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@param int $type Obligatoire, identifiant du type de la relation
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rel_relations_exists( $src, $dst, $type ){
	$src = control_array_integer( $src, true, true, true );
	if( $src===false || !$src[0] ){
		return false;
	}
	$dst = control_array_integer( $dst, true, true, true );
	if( $dst===false || !$dst[0] ){
		return false;
	}

	while( sizeof($src) < COUNT_OBJ_ID ){
		$src[] = 0;
	}
	while( sizeof($dst) < COUNT_OBJ_ID ){
		$dst[] = 0;
	}

	if( !is_numeric($type) || $type<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from
			rel_relations_objects
			join rel_relations_types on ( rro_tnt_id = rrt_tnt_id or 0 = rrt_tnt_id ) and rro_rrt_id = rrt_id
		where
			rro_tnt_id = '.$config['tnt_id'].'
			and rro_date_deleted is null
			and rrt_date_deleted is null
			and rro_rrt_id = '.$type.'
			and rro_dst_0='.$dst[0].'
			and rro_dst_1='.$dst[1].'
			and rro_dst_2='.$dst[2].'
			and rro_src_0='.$src[0].'
			and rro_src_1='.$src[1].'
			and rro_src_2='.$src[2].'
	';

	$r_exists = ria_mysql_query($sql);
	if( !$r_exists ){
		return false;
	}

	return ria_mysql_num_rows($r_exists);
}

/**	Cette fonction permet de récupérer récursivement la liste des objets parents
 *	@param int $type Obligatoire, identifiant du type de la relation.
 *		Les valeurs acceptées sont REL_USR_HIERARCHY, REL_SELLER_HIERARCHY et REL_RESELLER_STORE_HIERARCHY.
 *	@param int|array $dst Obligatoire, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@param int $lvl Facultatif, niveau de récursivité. Le niveau maximum est de 100.
 *	@param int $parents Facultatif, Liste des objets parents, pour le passage en recursif
 *	@return la liste des objets parent, attention il y a les 3 ids en cas de succès, false en cas d'échec
 *	@warning A utiliser le moins souvent possible et préfére les appels à la table de hiérarchie, Attention ne pas changer l'ordre de tri
 *		car cette fonction est utilisée pour la synchronisation
 */
function rel_relations_get_parents( $type, $dst, $lvl=0, $parents=array() ){

	if($lvl > 100 ){
		error_log("[rel_relations_get_parents] Arret après 100 récursivité : type = ".$type." dst = ".print_r($dst,true));
		return null;
	}

	$dst = control_array_integer( $dst, true, true, true );
	if( $dst===false || !$dst[0] ){
		return false;
	}

	while( sizeof($dst) < COUNT_OBJ_ID ){
		$dst[] = 0;
	}

	if( !is_numeric($type) || $type<=0 ){
		return false;
	}

	$rrel = rel_relations_get( 0, null, $dst, $type );
	if( $rrel && ria_mysql_num_rows($rrel) ){
		while( $rel = ria_mysql_fetch_assoc($rrel) ){
			$parents[] = array('lvl' => $lvl, 'obj' => array($rel['src_0'],$rel['src_1'],$rel['src_2']));

			$new_parents = rel_relations_get_parents($type, array($rel['src_0'],$rel['src_1'],$rel['src_2']), $lvl+1, $parents);
			if( $new_parents && sizeof($new_parents) ){
				$parents = $new_parents;
			}
		}
	}

	return $parents;
}

/**	Cette fonction permet de récupérer récursivement la liste des objets enfants
 *	@param int $type Obligatoire, identifiant du type de la relation
 *		Les valeurs acceptées sont REL_USR_HIERARCHY, REL_SELLER_HIERARCHY et REL_RESELLER_STORE_HIERARCHY.
 *	@param int|array $src Obligatoire, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@param int $lvl Facultatif, niveau de récursivité
 *	@param array $childs Facultatif, Liste des objets enfants, pour le passage en recursif
 *	\warning A utiliser le moins souvent possible et préfére les appels à la table de hiérchie
 *	@return array la liste des objets parent, attention il y a les 3 ids en cas de succès
 *	@return bool false en cas d'échec
 */
function rel_relations_get_childs( $type, $src, $lvl=0, $childs=array() ){

	if($lvl > 100 ){
		error_log("[rel_relations_get_childs] Arret après 100 récursivité : type = ".$type." src = ".print_r($src,true));
		return null;
	}

	$src = control_array_integer( $src, true, true, true );
	if( $src===false || !$src[0] ){
		return false;
	}

	while( sizeof($src) < COUNT_OBJ_ID ){
		$src[] = 0;
	}

	if( !is_numeric($type) || $type<=0 ){
		return false;
	}

	$rrel = rel_relations_get( 0, $src, null, $type );
	if( $rrel && ria_mysql_num_rows($rrel) ){
		while( $rel = ria_mysql_fetch_assoc($rrel) ){
			$childs[] = array('lvl' => $lvl, 'obj' => array($rel['dst_0'],$rel['dst_1'],$rel['dst_2']));

			$new_childs = rel_relations_get_childs($type, array($rel['dst_0'],$rel['dst_1'],$rel['dst_2']), $lvl+1, $childs);
			if( $new_childs && sizeof($new_childs) ){
				$childs = $new_childs;
			}
		}
	}

	return $childs;
}

/**	Reconstruit la hiérarchie complète des relations. La reconstruction se fait de manière hiérarchique, en partant
 *	des relations de premier niveau pour descendre.
 *
 *	@param int $type_id Facultatif, identifiant du type de la relation.
 *	@param $obj Facultatif, point de départ de la reconstruction. Pour une reconstruction complète, laisser vide.
 *	@param $done_ids Facultatif, liste des éléments déjà traité.
 *	@param $lvl Facultatif, niveau de profondeur
 *
 */
function rel_relations_hierarchy_rebuild( $type_id, $obj=0, $done_ids=array(), $level=0 ){
	$obj = control_array_integer( $obj, false, true, true );
	if( $obj==false || !$obj[0] ){
		$obj = array(0);
	}

	while( sizeof($obj) < COUNT_OBJ_ID ){
		$obj[] = 0;
	}

	if( !is_numeric($type_id) || $type_id<=0 ){
		// dans le cas ou le type n'est pas donnée la hiérarchie est recalculé pour tous les types
		$rtype = rel_relations_types_get();
		if( $rtype && ria_mysql_num_rows($rtype) ){
			while( $type = ria_mysql_fetch_assoc($rtype) ){
				rel_relations_hierarchy_rebuild($type['id'], $obj);
			}
		}
		return;
	}
	global $config;

	// requete permettant l'acutalisation de la date de modification pour les utilisateurs + de leurs enfants ( contacts )
	// attention la mise à jour de cette date est utile pour les tablettes yuto, elle est faite avant de vider les données et après.
	// tous les utilisateurs de la hiérachie modifié doivent être réactualisé.
	// fait en 2 temps pour un gains de perfs
	$sql_usr_modified = '
				select '.$obj[0].' as usr_id

				union

				select usr_id
				from gu_users
				where usr_tnt_id='.$config['tnt_id'].' and usr_parent_id='.$obj[0].' and usr_date_deleted is null

				union

				select rrh_dst_0 as usr_id
				from rel_relations_hierarchy
				where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id = '.REL_SELLER_HIERARCHY.'
					and rrh_src_0='.$obj[0].' and rrh_src_1=0 and rrh_src_2=0

				union

				select u2.usr_id as usr_id
				from rel_relations_hierarchy
				join gu_users as u on rrh_tnt_id=u.usr_tnt_id and rrh_dst_0=u.usr_id
				join gu_users as u2 on u.usr_tnt_id=u2.usr_tnt_id and u.usr_seller_id=u2.usr_seller_id
				where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id = '.REL_SELLER_HIERARCHY.'
				  and ( (rrh_src_0='.$obj[0].' and rrh_src_1=0 and rrh_src_2=0) or (rrh_dst_0='.$obj[0].' and rrh_dst_1=0 and rrh_dst_2=0) )
				  and u.usr_prf_id='.PRF_SELLER.'
				  and u.usr_date_deleted is null
				  and u2.usr_date_deleted is null
	';

	// mise à jour de la date sur les utilisateurs uniquement pour faire fonctionner la synchronisation des tablettes
	if( $obj[0]!= 0 && $level==0 && $type_id == REL_SELLER_HIERARCHY ){

		$usr_ids=array();
		$rusr = ria_mysql_query($sql_usr_modified);
		while( $u = ria_mysql_fetch_assoc($rusr) ){
			$usr_ids[] = $u['usr_id'];
		}

		$refresh_usr_date_modified = '
				update gu_users
				set usr_date_modified = now()
				where usr_tnt_id = '.$config['tnt_id'].' and usr_id in ('.implode(",", $usr_ids).')
			';

		ria_mysql_query($refresh_usr_date_modified);
	}

	if( $obj[0]== 0 ){
		// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
		ria_mysql_query('delete from rel_relations_hierarchy where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id='.$type_id);

		// récupère toutes les lignes présente dans la table relation qui sont uniquement dans les src, ce qui nous donnes les lignes de premier niveau
		$sql  = 'select rro_src_0, rro_src_1, rro_src_2 ';
		$sql .= 'from rel_relations_objects as r1 ';
		$sql .= 'where r1.rro_tnt_id='.$config['tnt_id'];
		$sql .= ' 	and r1.rro_rrt_id = '.$type_id;
		$sql .= '	and r1.rro_date_deleted is null';
		$sql .= ' 	and not exists ( ';
		$sql .= '		select 1 ';
		$sql .= ' 		from rel_relations_objects as r2 ';
		$sql .= '		where r2.rro_dst_0=r1.rro_src_0 ';
		$sql .= '			and r2.rro_dst_1=r1.rro_src_1 ';
		$sql .= '			and r2.rro_dst_2=r1.rro_src_2 ';
		$sql .= '			and r2.rro_rrt_id=r1.rro_rrt_id ';
		$sql .= '			and r2.rro_tnt_id='.$config['tnt_id'];
		$sql .= '			and r2.rro_date_deleted is null';
		$sql .= ' 	) ';

		$parents = ria_mysql_query($sql);
		if( $parents ){
			while( $p = ria_mysql_fetch_assoc($parents) ){
				rel_relations_hierarchy_rebuild( $type_id, array($p['rro_src_0'],$p['rro_src_1'],$p['rro_src_2']), $done_ids, $level+1 );
			}
		}
	}else{

		// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
		if( !in_array($obj, $done_ids) ) {
			ria_mysql_query('delete from rel_relations_hierarchy where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id='.$type_id.' and rrh_src_0='.$obj[0].' and rrh_src_1='.$obj[1].' and rrh_src_2='.$obj[2]);
		}

		$done_ids[] = $obj;

		$tmp_header = 'replace into rel_relations_hierarchy (rrh_tnt_id,rrh_rrt_id, rrh_src_0, rrh_src_1, rrh_src_2, rrh_dst_0, rrh_dst_1, rrh_dst_2, rrh_level) ';

		// récupère tous les enfants
		$rchilds = rel_relations_get_childs($type_id, $obj);
		if( $rchilds ){
			$tmp_values = array();

			foreach( $rchilds as $child ){

				rel_relations_hierarchy_rebuild( $type_id, array($child['obj'][0],$child['obj'][1],$child['obj'][2]), $done_ids, $level+1 );

				$tmp_values[] = '('.$config['tnt_id'].','.$type_id.','.$obj[0].','.$obj[1].','.$obj[2].','.$child['obj'][0].','.$child['obj'][1].','.$child['obj'][2].','.$child['lvl'].')';
			}
			if( sizeof( $tmp_values ) ){
				ria_mysql_query( $tmp_header.' values '.implode(",", $tmp_values));
			}
		}

		// récupère tous les parents présent dans la hierarchi pour faire le diff plus tard
		$oldparentstmp = array();
		$oldparentsfordelete = array();
		$roldparents = ria_mysql_query('
				select rrh_src_0, rrh_src_1, rrh_src_2, rrh_dst_0, rrh_dst_1, rrh_dst_2
				from rel_relations_hierarchy
				where rrh_tnt_id = '.$config['tnt_id'].'
					and rrh_rrt_id='.$type_id.'
					and rrh_dst_0 = '.$obj[0].'
					and rrh_dst_1 = '.$obj[1].'
					and rrh_dst_2 = '.$obj[2].'
			');
		if( $roldparents && ria_mysql_num_rows($roldparents)){
			while( $oldparent = ria_mysql_fetch_assoc($roldparents) ){
				$oldparentstmp[$oldparent['rrh_src_0'].'-'.$oldparent['rrh_src_1'].'-'.$oldparent['rrh_src_2']] = array(
					'src_0' => $oldparent['rrh_src_0'],
					'src_1' => $oldparent['rrh_src_1'],
					'src_2' => $oldparent['rrh_src_2']
				);
			}
			$oldparentsfordelete = $oldparentstmp;
		}

		// récupère tous les parents
		$rparents = rel_relations_get_parents($type_id, $obj);
		if( $rparents ){
			$tmp_values = array();

			foreach( $rparents as $parent ){

				$key = $parent['obj'][0].'-'.$parent['obj'][1].'-'.$parent['obj'][2];
				if( isset($oldparentstmp[$key]) ){ // double tableau car le parent peut être retourner plusieurs fois dans le get_parents...
					if( isset($oldparentsfordelete[$key]) ){
						unset($oldparentsfordelete[$key]);
					}
				}else{
					$tmp_values[] = '('.$config['tnt_id'].','.$type_id.','.$parent['obj'][0].','.$parent['obj'][1].','.$parent['obj'][2].','.$obj[0].','.$obj[1].','.$obj[2].',0)';
				}
			}
			if( sizeof( $tmp_values ) ){
				ria_mysql_query( $tmp_header.' values '.implode(",", $tmp_values));
			}
		}

		// suppression de lien parent invalide
		foreach( $oldparentsfordelete as $p ){
			ria_mysql_query('
				delete from rel_relations_hierarchy
				where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_src_0 = '.$p['src_0'].'
					and rrh_src_1 = '.$p['src_1'].'
					and rrh_src_2 = '.$p['src_2'].'
					and rrh_rrt_id='.$type_id.'
					and rrh_dst_0 = '.$obj[0].'
					and rrh_dst_1 = '.$obj[1].'
					and rrh_dst_2 = '.$obj[2]
			);
		}
	}


	// mise à jour de la date sur les utilisateurs uniquement pour faire fonctionner la synchronisation des tablettes
	if( $obj[0]!= 0 && $level==0 && $type_id == REL_SELLER_HIERARCHY ){

		$usr_ids=array();
		$rusr = ria_mysql_query($sql_usr_modified);
		while( $u = ria_mysql_fetch_assoc($rusr) ){
			$usr_ids[] = $u['usr_id'];
		}

		$refresh_usr_date_modified = '
				update gu_users
				set usr_date_modified = now()
				where usr_tnt_id = '.$config['tnt_id'].' and usr_id in ('.implode(",", $usr_ids).')
			';

		ria_mysql_query($refresh_usr_date_modified);
	}

}

/** Cette fonction permet la récupération des identifiants enfant pour un type et d'une objet
 *	@param int $type_id Obligatoire, identifiant du type de la relation
 *	@param $obj Obligatoire, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composé
 *	@return bool false dans le cas d'un echec, ou un tableau contenant les ids de l'objet, attention pour certaine classe le tableau n'a qu'une dimension pour simplifier le code
 */
function rel_relations_hierarchy_childs_get_ids( $type_id, $obj ){

	$obj = control_array_integer( $obj, true, true, true );
	if( $obj==false || !$obj[0] ){
		return false;
	}

	while( sizeof($obj) < COUNT_OBJ_ID ){
		$obj[] = 0;
	}

	$type_id = control_array_integer( $type_id, true );
	if( $type_id === false ){
		return false;
	}

	global $config;

	$data = array();

	$sql  = 'select rrt_dst_cls, rrh_dst_0, rrh_dst_1, rrh_dst_2 ';
	$sql .= 'from rel_relations_hierarchy ';
	$sql .= 'join rel_relations_types on (rrh_tnt_id = rrt_tnt_id or rrt_tnt_id = 0) and rrh_rrt_id = rrt_id ';
	$sql .= 'where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id in ('.implode(',',$type_id).') and rrh_src_0='.$obj[0].' and rrh_src_1='.$obj[1].' and rrh_src_2='.$obj[2];

	$req = ria_mysql_query($sql);
	if($req){
		while( $r = ria_mysql_fetch_assoc($req) ){
			switch($r['rrt_dst_cls']){
				case CLS_USER :
				case CLS_PRODUCT :
					$data[] = $r['rrh_dst_0'];
					break;
				default:
					$data[] = array($r['rrh_dst_0'], $r['rrh_dst_1'], $r['rrh_dst_2']);
					break;
			}
		}
	}

	return $data;
}

/** Cette fonction permet la récupération des identifiants parent pour un type et d'une objet
 *	@param int $type_id Obligatoire, identifiant du type de la relation
 *	@param int|array $obj Obligatoire, identifiant de l'objet de destination ou tableau d'identifiants dans le cas de clé composée
 *	@return bool false dans le cas d'un echec, ou un tableau contenant les ids de l'objet, attention pour certaine classe le tableau n'a qu'une dimension pour simplifier le code
 */
function rel_relations_hierarchy_parents_get_ids( $type_id, $obj ){

	$obj = control_array_integer( $obj, true, true, true );
	if( $obj==false || !$obj[0] ){
		return false;
	}

	while( sizeof($obj) < COUNT_OBJ_ID ){
		$obj[] = 0;
	}

	$type_id = control_array_integer( $type_id, true );
	if( $type_id === false ){
		return false;
	}

	global $config;

	$data = array();

	$sql  = 'select rrt_src_cls, rrh_src_0, rrh_src_1, rrh_src_2 ';
	$sql .= 'from rel_relations_hierarchy ';
	$sql .= 'join rel_relations_types on (rrh_tnt_id = rrt_tnt_id or rrt_tnt_id = 0) and rrh_rrt_id = rrt_id ';
	$sql .= 'where rrh_tnt_id='.$config['tnt_id'].' and rrh_rrt_id in ('.implode(',',$type_id).') and rrh_dst_0='.$obj[0].' and rrh_dst_1='.$obj[1].' and rrh_dst_2='.$obj[2];

	$req = ria_mysql_query($sql);
	if($req){
		while( $r = ria_mysql_fetch_assoc($req) ){
			switch($r['rrt_src_cls']){
				case CLS_USER :
				case CLS_PRODUCT :
					$data[] = $r['rrh_src_0'];
					break;
				default:
					$data[] = array($r['rrh_src_0'], $r['rrh_src_1'], $r['rrh_src_2']);
					break;
			}
		}
	}

	return $data;
}

/** Cette fonction permet de récupérer la liste des relations pour un ou plusieurs objets source
 *	@param $cls Obligatoire, identifiant de la classe conserné
 *	@param $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *		- doc_id : identifiant du document
 *		- cls_id : identifiant de la classe de l'objet
 *		- obj_id_0 : identifiant de l'objet
 *		- obj_id_1 : identifiant de l'objet (clé composée - 0 sinon)
 *		- obj_id_2 : identifiant de l'objet (clé composée - 0 sinon)
 *		- level : Niveau de profondeur de la relation
 */
function rel_relation_hierarchy_get_all($cls, $obj, $multi_key=false){
	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			rrh_rrt_id, rrt_src_cls, rrt_dst_cls,
			rrh_src_0, rrh_src_1, rrh_src_2,
			rrh_dst_0, rrh_dst_1, rrh_dst_2,
			rrh_level
		from
			rel_relations_hierarchy
		join rel_relations_types on (rrh_tnt_id = rrt_tnt_id or rrt_tnt_id = 0) and rrh_rrt_id = rrt_id

		where rrh_tnt_id = '.$config['tnt_id'].' and rrt_src_cls = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' rrh_src_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and rrh_src_1 = '.$o[1];
				}
				if( isset($o[2]) ){
					$where .= ' and rrh_src_2 = '.$o[2];
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and rrh_src_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and rrh_src_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and rrh_src_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and rrh_src_2 = '.$obj[2];
		}
	}

	return ria_mysql_query($sql);
}


/// @}

/// @}
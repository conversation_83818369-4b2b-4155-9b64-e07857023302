<?php
require_once('users.inc.php');
require_once('news.inc.php');
require_once('antispam.inc.php');

/** \defgroup reviews Avis consommateurs
 *
 *	Ce module permet la gestion des avis consommateurs sur le site et sur les produits.
 */

/** \defgroup reviews_site Sur le site
 * 	\ingroup reviews
 *
 * 	Ce module permet la gestion des avis consommateurs attachés à un site donné (à diférencier des avis consommateurs sur les produits)
 *
 *	@{
 */

// \cond onlyria
/** Cette fonction permet la création d'un nouvel avis consommateur sur le site (à différencier des avis consommateur sur les produits).
 * 	@param int $usr_id Obligatoire, identifiant de l'utilisateur déposant l'avis (pour modération et crm) si c'est un utilisateur enregistré, donner la valeur null pour un utilisateur non enregistré.
 *	@param string $name Obligatoire, titre de l'avis.
 *	@param string $desc Obligatoire, description / contenu de l'avis.
 *	@param $type Obligatoire, type de l'avis, 5 pour avis produit , 6 pour avis site, 7 commentaires sur actualité
 *	@param $note Facultatif, note sur 10 attribuée au site par le consommateur.
 *	@param $prd Facultatif, identifiant du produit pour lequel ajouter un avis consommateur.
 *	@param $ans_desc Facultatif, réponse au commentaire.
 *	@param $published Facultatif, détermine s'il faut publier le commentaire sans passer par la modération.
 *	@param string $usr_firstname Facultatif, prénom de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_lastname Facultatif, nom de famille de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_email Facultatif, email de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_society Facultatif, nom de la société de l'utilisateur (pour un utilisateur non enregistré : facultatif).
 *	@param $parent Facultatif, identifiant d'un avis
 *	@param int $wst_id Facultatif, permet de spécifier un autre site que celui de la configuration
 *	@param $note_dlv Facultatif, notation sur la livraison du produit (permet de séparer la qualité du produit de la qualité du transporteur)
 *	@param $note_pkg Facultatif, notation sur l'emballage du produit (permet de séparer la qualité de préparation des colis de la qualité du transporteur ou du produit)
 *
 *	@return int l'identifiant attribué à l'avis en cas de succès, false en cas d'échec.
 */
function rvw_reviews_add( $usr_id, $name, $desc, $type, $note=null, $prd=null, $ans_desc=null, $published=null, $usr_firstname=null, $usr_lastname=null, $usr_email=null, $usr_society=null, $parent=false, $wst_id=false, $note_dlv = null, $note_pkg = null ){
	global $config;

	$spam_id = verify_message( $desc );

	$usr_registred = isset( $usr_id ); // permet de savoir si l'utilisateur est enregistré ou non

	// Contrôles
	if( !trim( $name ) ) return false;
	if( !trim( $desc ) ) return false;
	if( !is_numeric( $type ) ) return false;
	if( !is_numeric( $note ) || $note<0 || $note>10 ) $note = 'null';
	if( !isset( $published ) ) $published = false;

	// Contrôles sur le produit
	if( isset( $prd ) ){
		if( !is_numeric( $prd ) || $prd<=0 ) return false;
		if( !$config['prd_reviews'] || !prd_products_exists( $prd ) ) return false;
		$type = 5; // force le type d'avis : avis produit

	} elseif( $type===0 ){
		return false; // un avis sur produit DOIT avoir un produit associé

	} else {
		// Il n'y a pas de produit associé, donc c'est un avis sur le site
		$type = 6;
	}
	if($type == 1)
		$type = 6;
	elseif($type == 0)
		$type = 5;

	// Formatage
	$name = addslashes( ucfirst( trim( $name ) ) );
	$desc = addslashes( ucfirst( trim( $desc ) ) );
	$ans_desc = empty( $ans_desc ) ? '' : addslashes( ucfirst( trim( $ans_desc ) ) );

	// SQL : into
	$into = 'cnt_tnt_id, cnt_subject, cnt_body, cnt_type, cnt_note, cnt_ans_body, cnt_date_created, cnt_wst_id';
	// SQL : values
	$values = $config['tnt_id'].',"'.$name.'", "'.$desc.'", "'.$type.'", '.$note.', "'.$ans_desc.'", now(), '.( is_numeric($wst_id) && $wst_id > 0 ? $wst_id : $config['wst_id'] ).'';

	if( isset( $prd ) ){
		$into .= ', cnt_prd_id';
		$values .= ', '.$prd;
	}

	if (isset($_SERVER['REMOTE_ADDR'])) {
		$into .= ', cnt_ip';
		$values .= ', "'.addslashes($_SERVER['REMOTE_ADDR']).'"';
	}

	if( $spam_id!=0 )
	{
		$into .= ', cnt_spam_id, cnt_state, cnt_send';
		$values .= ', '.$spam_id.', "-2", 0';
	}
	else if( isset( $published ) && $published ){
		$into .= ', cnt_state, cnt_date_publish';
		$values .= ', "1", now()';
		if($note !== 'null' && $type==0)
			rvw_reviews_avg_update($prd, $note);
	}
	else if($config['prd_reviews_moderation']=='before' || $config['prd_reviews_moderation']=='after')
	{
		if($config['prd_reviews_moderation']=='before')
		{
			$into .= ', cnt_state, cnt_date_publish';
			$values .= ', "-1", now()';
		}
		else if($config['prd_reviews_moderation']=='after')
		{
			$into .= ', cnt_state, cnt_date_publish';
			$values .= ', "1", now()';
		}
	}

	if( $note_dlv!=null ) {
		$into .= ' ,cnt_note_delivery';
		$values .= ','.$note_dlv;
	}
	if( $note_pkg!=null ) {
		$into .= ' ,cnt_note_package';
		$values .= ','.$note_pkg;
	}

	// Vérifie, pour un utilisateur potentiellement non enregistre ($usr_id = null),
	// si son adresse email est connue parmi les utilisateurs enregistrés
	if( !$usr_registred && gu_users_exists( 0, 0, $usr_email ) ){
		$usr_id = ria_mysql_result( gu_users_get( 0, $usr_email ), 0, 'id' );
		$usr_registred = true;
	}

	// Pour un utilisateur enregistré...
	if ( $usr_registred && $usr_id>0 ){

		// Contrôles
		if( !is_numeric( $usr_id ) || $usr_id<0 ) return false;
		if( !gu_users_exists( $usr_id ) ) return false;

		// SQL : into
		$into .= ', cnt_usr_id';
		// SQL : values
		$values .= ', '.$usr_id;

	// Pour un utilisateur non enregistré...
	} else {

		// Contrôles et formatage
		if( !trim( $usr_firstname ) ) return false;
		$usr_firstname = addslashes( ucfirst( trim( $usr_firstname ) ) );
		if( !trim( $usr_lastname ) ) return false;
		$usr_lastname = addslashes( ucfirst( trim( $usr_lastname ) ) );
		if( !trim( $usr_email ) ) return false;
		$usr_email = addslashes( trim( $usr_email ) );
		if( isset( $usr_society ) )
			$usr_society = addslashes( trim( $usr_society ) );

		// SQL : into
		$into .= ', cnt_firstname, cnt_lastname, cnt_email, cnt_society';
		// SQL : values
		$values .= ', "'.$usr_firstname.'", "'.$usr_lastname.'", "'.$usr_email.'", "'.$usr_society.'"';
	}

	if(is_numeric($parent) && rvw_reviews_exists($parent) ){
		// SQL : into
		$into .= ', cnt_parent';
		// SQL : values
		$values .= ', '.$parent;
	}
	// Requête SQL
	$sql = '
		insert into gu_messages('.$into.')
		values('.$values.')
	';
	// Procède à l'insertion
	$res = ria_mysql_query( $sql );

	if( $res )
		return ria_mysql_insert_id();


	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'un avis consommateur sur le site (à différencier des avis consommateur sur les produits).
 *
 *	@param int $id Obligatoire, identifiant de l'avis à mettre à jour.
 *	@param string $name Obligatoire, titre de l'avis.
 *	@param string $desc Obligatoire, description / contenu de l'avis.
 *	@param $note Facultatif, note sur 10 attribuée au site par le consommateur.
 *	@param $ans_desc Facultatif, réponse au commentaire.
 *	@param $published Facultatif, détermine s'il faut publier le commentaire sans passer par la modération.
 *	@param string $usr_firstname Facultatif, prénom de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_lastname Facultatif, nom de famille de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_email Facultatif, email de l'utilisateur (pour un utilisateur non enregistré : obligatoire).
 *	@param string $usr_society Facultatif, nom de la société de l'utilisateur (pour un utilisateur non enregistré : facultatif).
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rvw_reviews_update( $id, $name=null, $desc=null, $note=null, $ans_desc=null, $published=null, $usr_firstname=null, $usr_lastname=null, $usr_email=null, $usr_society=null ){

	global $config;

	// Contrôles
	if( !is_numeric( $id ) || $id<0 ) return false;
	if( !rvw_reviews_exists( $id ) ) return false;
	if( $name && !trim( $name ) ) return false;
	if( $desc && !trim( $desc ) ) return false;
	if( !is_numeric( $note ) || $note<0 || $note>10 ) $note = 'null';
	if( !isset( $published ) ) $published = false;
	if( !$config['prd_reviews_note'] ) $note = 'null';
	$publish = $published ? 1 : 0;

	$cols = 'cnt_note='.$note;

	if($name){
		$name = addslashes( ucfirst( trim( $name ) ) );
		$cols .= ' ,cnt_subject=\''.$name.'\'';
	}
	if($desc){
		$desc = addslashes( ucfirst( trim( $desc ) ) );
		$cols .= ' ,cnt_body=\''.$desc.'\'';
	}

	if($ans_desc){
		$ans_desc = empty( $ans_desc ) ? '' : addslashes( ucfirst( trim( $ans_desc ) ) );
		$cols .= ' ,cnt_ans_body=\''.$ans_desc.'\' ';
	}

	if($published)
		$cols .= ' ,cnt_state=\''.$publish.'\' ';

	// Vérifie si l'avis est déjà publié
	// car il ne faut mettre à jour la date de publication que s'il y a changement de l'état de publication
	if( $published ){
		$q = ria_mysql_query( 'select cnt_state="1" from gu_messages where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id );
		$r = ria_mysql_fetch_row( $q );
		$already_published = $r[0];

		if( !$already_published )
			$cols .= ', cnt_date_publish=now()';

	}

	// Recherche si l'utilisateur est un utilisateur enregistré ou non
	// Cela permet de savoir si les paramètres $usr_fistname, $usr_lastname et $usr_email sont obligatoires ou non
	$q = ria_mysql_query( 'select not isnull(cnt_usr_id) from gu_messages where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id );
	$r = ria_mysql_fetch_row( $q );
	$usr_registred = $r[0];

	// Vérifie, pour un utilisateur potentiellement non enregistre ($usr_id = null),
	// si son adresse email est connue parmi les utilisateurs enregistrés
	if( !$usr_registred && gu_users_exists( 0, 0, $usr_email ) )
		$usr_registred = true;

	// Pour un utilisateur non enregistré...
	if( !$usr_registred ){
		// Contrôles et formatage
		if( !trim( $usr_firstname ) ) return false;
		$usr_firstname = addslashes( ucfirst( trim( $usr_firstname ) ) );
		if( !trim( $usr_lastname ) ) return false;
		$usr_lastname = addslashes( ucfirst( trim( $usr_lastname ) ) );
		if( !trim( $usr_email ) ) return false;
		$usr_email = addslashes( trim( $usr_email ) );
		if( isset( $usr_society ) )
			$usr_society = addslashes( trim( $usr_society ) );

		// SQL
		$cols .= '
			, cnt_firstname=\''.$usr_firstname.'\'
			, cnt_lastname=\''.$usr_lastname.'\'
			, cnt_email=\''.$usr_email.'\'
			, cnt_society=\''.$usr_society.'\'
		';
	}

	// Requête SQL
	$sql = '
		update gu_messages
		set '.$cols.'
		where cnt_tnt_id='.$config['tnt_id'].'
		and cnt_id='.$id.'
	';

	// Procède à la mise à jour
	if( !ria_mysql_query( $sql ) ) return false;

	//recupere l'identifiant du produit et met a jour la note moyenne du produit
	$prd = ria_mysql_result(ria_mysql_query('select cnt_prd_id from gu_messages where cnt_tnt_id='.$config['tnt_id'].' and cnt_id = '.$id),0,0);
	if($note !== 'null' )
		rvw_reviews_avg_update($prd, $note);
	return true;
}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs avis consommateur portés sur le site, éventuellement filtrés en fonction
 *	des paramètres optionnels fournis.
 *
 *	@param int $id Facultatif, identifiant d'un avis sur lequel filtrer le résultat.
 *	@param $prd Facultatif, identifiant d'un produit sur lequel filtrer le résultat.
 *	@param int $usr_id Facultatif, identifiant d'un utilisateur sur lequel filtrer le résultat.
 *	@param $published Facultatif, état de publication de l'avis sur lequel filtrer le résultat.
 *	@param $type Facultatif, type d'avis sur lequel filtrer le résultat (produit ou site),  0 pour avis produit , 1 pour avis site.
 *	@param string $usr_email Facultatif, adresse email d'un utilisateur non enregistré sur lequel filtrer le résultat.
 *	@param $parent Facultatif, identifiant d'un avis parent
 *	@param $news Facultatif, identifiant d'une actualité pour laquelle on désire les commentaires
 *	@param $spam_id Facultatif, permet de filtrer les messages sur un identifiant de spam, par défaut seul les messages qui ne sont pas des spams sont retourné
 *	@param $sort Facultatif, permet de trier les résultats, fournir un tableau sous cette forme : array('colonne(date_created|note)'=>'direction(asc|desc)')
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer le résultat. Par défaut, les résultats ne sont pas filtrés (la variable $config['wst_id'] n'est pas utilisée)
 *	@param bool|null $with_avis_verifier Facultatif, détermine si on récupère tout les avis (null) ou que ceux du site (false) ou que ceux d'avis vérifier (true)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'avis
 *			- usr_id : identifiant de l'utilisateur ayant créé l'avis
 *			- name : titre de l'avis
 *			- desc : description/contenu de l'avis
 *			- note : note attribuée par l'avis
 *			- note_pkg : note attribuée par l'avis sur le colis
 *			- note_dlv : note attribuée par l'avis sur la livraison
 *			- ans_desc : description/contenu de la réponse à l'avis
 *			- type : type de l'avis (produit ou site)
 *			- date-en : date en version En
 *			- date : date/heure de création de l'avis (doublon de date_created à conserver pour compatibilité)
 *			- date_created : date/heure de création de l'avis
 *			- date_modified : date/heure de dernière modification de l'avis
 *			- date_published : date/heure de publication de l'avis
 *			- publish : état de publication de l'avis (-1 : attente de modération, 0 : refusé, 1 : accepté)
 *			- prd_id : identifiant du produit concerné par l'avis
 *			- prd_name : nom du produit concerné par l'avis
 *			- prd_ref : référence du produit concerné par l'avis
 *			- prd_deleted : détermine si le produit a été supprimé
 *			- usr_firstname : prénom de l'utilisateur ayant créé l'avis
 *			- usr_lastname : nom de famille de l'utilisateur ayant créé l'avis
 *			- usr_email : adresse email de l'utilisateur ayant créé l'avis
 *			- usr_society : nom de société de l'utilisateur ayant créé l'avis
 *			- buyed : détermine si l'utilisateur a acheté le produit en question
 *			- id_verifie : clé avis vérifié s'il s'agit d'un avis vérifié
 */
function rvw_reviews_get( $id=0, $prd=null, $usr_id=null, $published=null, $type=null, $usr_email=null, $parent = false, $news = false, $spam_id = 0, $sort = array(), $wst_id=false, $with_avis_verifier=null ){

	global $config;

	// Contrôles
	if( !is_numeric( $id ) || $id<0 ) return false;
	if( $news && !news_exists($news) ) return false;

	if($type == 1)
		$type = 6;
	else if($type == 2)
		$type = 7;
	// Requête SQL
	$sql = '
		select
			cnt_id as id,
			cnt_usr_id as usr_id,
			cnt_subject as name,
			cnt_body as "desc",
			cnt_note as note,
			cnt_note_package as note_pkg,
			cnt_note_delivery as note_dlv,
			cnt_ans_body as ans_desc,
			cnt_type as type,
			cnt_date_created as "date-en",
			date_format( cnt_date_created, "%d/%m/%Y à %H:%i" ) as date,
			date_format( cnt_date_created, "%d/%m/%Y à %H:%i" ) as date_created,
			date_format( cnt_date_modified, "%d/%m/%Y à %H:%i" ) as date_modified,
			if( cnt_date_publish is null, "", date_format( cnt_date_publish, "%d/%m/%Y à %H:%i" ) ) as date_published,
			cnt_state as publish,
			cnt_prd_id as prd_id,
			prd_products.prd_name as prd_name,
			prd_products.prd_ref as prd_ref,
			prd_date_deleted is not null as prd_deleted,
			ifnull( adr_firstname, cnt_firstname ) as usr_firstname,
			ifnull( adr_lastname, cnt_lastname ) as usr_lastname,
			ifnull( usr_email, cnt_email ) as usr_email,
			ifnull( adr_society, cnt_society ) as usr_society,
			not isnull( prd_ord_id ) as buyed,
			cnt_parent as parent,
			cnt_news_id as news,
			cnt_id_verifie as id_verifie
		from
			gu_messages
			left join prd_products on (prd_products.prd_tnt_id='.$config['tnt_id'].' and cnt_prd_id=prd_products.prd_id)
			left join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and cnt_usr_id=usr_id)
			left join gu_adresses on (adr_tnt_id='.$config['tnt_id'].' and usr_adr_invoices=adr_id)
			left join ord_orders on (ord_tnt_id='.$config['tnt_id'].' and usr_id=ord_usr_id and ord_state_id>3)
			left join ord_products on (ord_products.prd_tnt_id='.$config['tnt_id'].' and prd_ord_id=ord_id and ord_products.prd_id=cnt_prd_id)
		where
			cnt_tnt_id='.$config['tnt_id'].'
		 	and cnt_date_delete is null
	';

	// Filtres
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}
	if( $id>0 )
		$sql .= ' and cnt_id='.$id;
	if( isset( $type ) && is_numeric( $type ) )
		$sql .= ' and cnt_type=\''.$type.'\'';
	else
		$sql .= ' and (cnt_type=5 or cnt_type=6)';
	if( isset( $prd ) && is_numeric( $prd ) && $prd>0 )
		$sql .= ' and cnt_prd_id='.$prd;
	if( isset( $usr_id ) && is_numeric( $usr_id ) && $usr_id>0 )
		$sql .= ' and cnt_usr_id='.$usr_id;
	if( isset( $usr_email ) ){
		$usr_email = addslashes( trim( $usr_email ) );
		$sql .= 'and cnt_email=\''.$usr_email.'\'';
	}
	if( $spam_id == -1 )
		$sql .= ' and cnt_spam_id IS NOT NULL';
	else if( $spam_id > 0 )
		$sql .= ' and cnt_spam_id='.$spam_id;
	else
		$sql .= ' and cnt_state != -2';
	if( isset( $published ) ){
		if( $published === -1)
			$sql .= ' and cnt_state = \'-1\'';
		elseif( $published === 0)
			$sql .= ' and cnt_state = \'0\'';
		elseif( $published === 1)
			$sql .= ' and cnt_state = \'1\'';
		else
		{
			if( $config['prd_reviews_moderation']=='before' ){
				$sql .= ' and cnt_state'.( $published ? '=\'1\'' : '!=\'1\'' );
			}
			elseif( $config['prd_reviews_moderation']=='after' ){
				$sql .= ' and cnt_state'.( $published ? '!=\'0\'' : '=\'0\'' );
			}
		}
	}

	// avis vérifié
	if( !is_null($with_avis_verifier) ){
		if( $with_avis_verifier ){
			$sql .= ' and cnt_id_verifie is not null';
		}else{
			$sql .= ' and cnt_id_verifie is null';
		}
	}

	if( is_numeric( $parent ) && rvw_reviews_exists($parent) ){
		$sql .= ' and cnt_parent ='.$parent;
	}
	elseif( !$parent ){
		$sql .= ' and cnt_parent is null';
	}

	if($news)
		$sql .= ' and cnt_news_id = '.$news;

	// SQL
	$sql .= ' group by cnt_id';

	if( !is_array($sort) || !sizeof($sort) ){
		if( $published )
			$sort = array( 'note'=>'desc', 'date_created'=>'desc' );
		else
			$sort = array( 'date_created'=>'desc' );
	}

	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$dir = $dir=='asc' ? 'asc' : 'desc';
		switch( $col ){
			case 'date_created' :
				array_push( $sort_final, 'cnt_date_created '.$dir );
				break;
			case 'note' :
				array_push( $sort_final, 'cnt_note '.$dir );
				break;
			case 'date_publish' :
				array_push( $sort_final, 'cnt_date_publish '.$dir );
				break;
		}
	}

	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	if( is_numeric( $id ) && $id>0 )
		$sql .= ' limit 0,1';

	// Procède au chargement
	$res = ria_mysql_query( $sql );
	return $res;
}

// \cond onlyria
/** Cette fonction renvoie la moyenne des notes attribuées à un produit.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer le décompte des notes
 *	@return bool False en cas d'échec
 *	@return float La note moyenne du produit
 */
function rvw_reviews_avg( $prd, $wst_id=false ){
	global $config;

	if( $prd !== null && (!is_numeric($prd) || $prd < 0) ){
		return false;
	}

	$sql = '
		select  avg(cnt_note) as average
		from gu_messages
		where
			cnt_tnt_id='.$config['tnt_id'].'
			and cnt_note is not null
			and cnt_date_delete is null
			and cnt_state = "1"
			and (cnt_type=5 or cnt_type=6)
	';

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}

	if( $prd === null ){
		$sql .= ' and cnt_prd_id is null';
	}elseif( is_numeric($prd) && $prd >= 0 ){
		$sql .= ' and cnt_prd_id = '.$prd;
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	$fetch = ria_mysql_fetch_assoc($r);

	return $fetch['average'];
}
// \endcond

// \cond onlyria
/** Cette fontion permet de mettre à jour la moyenne des notes d'un produit.
 *	@param int $prd Facultatif, produit à mettre à jour
 *	@param int $note Facultatif, note à ajouter à la moyenne
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rvw_reviews_avg_update( $prd=null, $note=0 ){
	global $config;

	if(  !isset( $prd ) || !is_numeric( $prd ) || $prd<0 )	return false;
	$avg = rvw_reviews_avg($prd);
	return ria_mysql_query('update prd_products set prd_rvw_avg = '.$avg.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.' and (cnt_type=5 or cnt_type=6)');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet le contrôle d'un identifiant d'avis consommateur sur le site.
 *
 *	@param int $id Obligatoire, identifiant de l'avis à contrôler.
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer le contrôle
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rvw_reviews_exists( $id, $wst_id=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select cnt_id from gu_messages
		where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id.' and cnt_date_delete is null
	';

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().''.$sql );
	}

	return ria_mysql_num_rows($r)>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression virtuelle d'un avis consommateur porté sur le site.
 *
 *	@param int $id Obligatoire, identifiant de l'avis consommateur à supprimer.
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rvw_reviews_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_query('update gu_messages set cnt_date_delete=now() where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la publication d'un avis consommateur porté sur le site.
 *
 *	@param int $id Obligatoire, identifiant de l'avis à publier.
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rvw_reviews_publish( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !ria_mysql_query('update gu_messages set cnt_date_publish=now(),cnt_state=\'1\' where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id.' and cnt_date_delete is null') )
		return false;

	$prd = ria_mysql_fetch_array(rvw_reviews_get($id));
	return rvw_reviews_avg_update($prd['prd_id'],$prd['note']);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la dé-publication d'un avis consommateur porté sur le site.
 *
 *	@param int $id Obligatoire, identifiant de l'avis à dépublier.
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rvw_reviews_unpublish( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !ria_mysql_query('update gu_messages set cnt_date_publish=null,cnt_state=\'0\' where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id.' and cnt_date_delete is null') )
		return false;

	$prd = ria_mysql_fetch_array(rvw_reviews_get($id));
	return rvw_reviews_avg_update($prd['prd_id'],$prd['note']);
}
// \endcond

// \cond onlyria
/** Cette fonction compte le nombre d'avis pour chaque état (publié, non publié, en attente).
 *	@param $type Facultatif, identifiant du type de message, par défaut NULL pour les avis produits et site
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer les résultats
 *	@param $group_wst Facultatif, permet d'ajouter le site dans la clause SQL "Group By" (note : n'a pas de sens si $wst_id est spécifié)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- etat : identifiant du gu_messages_type
 *		- number : nombre de messages (spams exclus)
 *		- wst_id : identifiant du site, si $group_wst est activé
 */
function rvw_reviews_count( $type=null, $wst_id=false, $group_wst=false ){
	global $config;

	$sql = '
		select cnt_state as etat'.( $group_wst ? ', cnt_wst_id as wst_id' : '' ).', count(*) as number
		from gu_messages
		where cnt_tnt_id='.$config['tnt_id'].'
		and cnt_date_delete is null
		and cnt_spam_id is null
	';

	if( is_numeric($type) && $type > 0 ){
		$sql .= ' and cnt_type = '.$type;
	}else{
		$sql .= ' and (cnt_type=5 or cnt_type=6)';
	}

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}

	$sql .= '
		group by cnt_state'.( $group_wst ? ', cnt_wst_id' : '' ).'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester si un avis à une réponse
 *	@param int $id Obligatoire, identifiant de l'avis à tester
 *	@param bool $publish Facultatif. Si true, alors l'avis consommateur devra être publiée. Si false, la publication de l'avis n'intervient pas.
 *	@return le nombre de réponses formulées à l'avis, ou 0 si aucun.
 */
function rvw_reviews_childs_exists( $id, $publish=false ){
	global $config;

	if( !is_numeric($id) || !rvw_reviews_exists($id) ) return false;

	$sql = ' select cnt_id from gu_messages where cnt_tnt_id='.$config['tnt_id'].' and cnt_parent = '.$id;

	if( $publish ){
		$sql .= ' and cnt_date_publish is not null ';
	}

	$result = ria_mysql_query( $sql );

	return ria_mysql_num_rows($result) > 0;
}
// \endcond

/**	Permet l'ajout d'un avis utilisateur  sur un site.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur émettant l'avis
 *	@param string $firstname Obligatoire, Prénom de l'émetteur
 *	@param string $lastname Obligatoire, Nom de l'émetteur
 *	@param string $email Obligatoire, Adresse email de l'émetteur
 *	@param string $subject Obligatoire, Titre de l'avis consommateur
 *	@param string $desc Obligatoire, Description/Contenu de l'avis
 *	@param $note Facultatif, Note attribué par le consommateur
 *	@param $parent Facultatif, identifiant parent de l'avis
 *	@param int $wst_id Facultatif, identifiant d'un site, autre que celui de la configuration
 *
 *	@return int L'identifiant de l'avis généré, ou False en cas d'échec
 */
function site_rvw_reviews_add( $usr, $lastname, $firstname, $email, $subject, $desc, $note=0, $parent=false, $wst_id=false ){
	global $config;


	$spam_id = verify_message( $desc );
	$id = rvw_reviews_add( $usr, $subject, $desc, 6, $note, null, null, null, $firstname, $lastname, $email, null, $parent, $wst_id );

	if( $spam_id == 0 && ($config['prd_reviews_moderation']=='before' || $config['prd_reviews_moderation']=='after') ){
		site_rvw_reviews_add_mail( $id );
	}

	return $id;
}

// \cond onlyria
/**	Envoie la notification d'un nouveau avis consommateur pour un site
 *	@param int $id Obligatoire, identifiant du message près à l'envoi
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function site_rvw_reviews_add_mail( $id ){
	$rmsg = messages_get( 0, '', 0, $id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) )
		return false;

	$msg = ria_mysql_fetch_array( $rmsg );
	global $config;
	$http_host_ria = $config['backoffice_url'];

	$rcfg = cfg_emails_get('prd-review');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$destinataire = ria_mysql_fetch_array($rcfg);

	$lastname = $msg['lastname']; $firstname = $msg['firstname']; $mail = $msg['email'];
	if( $msg['usr_id'] > 0 )
	{
		$user = gu_adresses_get($msg['usr_id']);
		$u = ria_mysql_fetch_array($user);
		$lastname = $u['lastname'];
		$firstname = $u['firstname'];
		$user = gu_users_get($msg['usr_id']);
		$u = ria_mysql_fetch_array($user);
		$mail = $u['email'];
	}

	// Crée le message
	$email = new Email();
	$email->setFrom( $mail );

	$email->addTo( $destinataire['to'] );
	$email->addCc( $destinataire['cc'] );
	$email->addBcc( $destinataire['bcc'] );
	$email->setReplyTo( $destinataire['reply-to'] );

	$email->setSubject( 'Nouvel avis site' );

	$email->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$email->addHtml( $config['email_html_header'] );
	$email->addHtml('<div style="font-size:0.8em">');

	$email->addParagraph( 'Bonjour,' );
	$email->addParagraph('Un nouvel avis consommateur vient d\'être enregistré sur le site '.$config['site_url'].' . Cet avis a été déposé par '.$lastname.' '.$firstname.' à '.date('H:i').' aujourd\'hui. Vous trouverez son commentaire ci-dessous :');

	$email->openTable('auto', 1, 'font-size:0.9em');
		$email->openTableRow();
			$email->addCell('Nom : ');
			$email->addCell($lastname);
		$email->closeTableRow();

		$email->openTableRow();
			$email->addCell( _('Prénom :') );
			$email->addCell($firstname);
		$email->closeTableRow();
		$email->openTableRow();
			$email->addCell('Email : ');
			$email->addCell($mail);
		$email->closeTableRow();
		$email->openTableRow();
			$email->addCell( 'Titre de l\'avis :' );
			$email->addCell( $msg['subject'] );
		$email->closeTableRow();
		$email->openTableRow();
			$email->addCell( 'Contenu de l\'avis :' );
			$email->addCell( nl2br($msg['body']) );
		$email->closeTableRow();
	$email->closeTable();


	// Indique si l'avis est publié ou non.
	if( $config['prd_reviews_moderation']=='before' ){
		$email->addParagraph('Conformément au mode de modération choisi, cet avis attend votre approbation pour être affiché dans la boutique. Pour le publier, veuillez vous rendre dans <a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type=RVW_SITE&amp;rvw='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=reviews&amp;utm_content=review-edit">votre interface d\'administration</a>.');
		$email->addParagraph('Vous pouvez aussi modérer l\'ensemble des avis produits en attente sur cette <a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type=RVW_SITE&amp;have-rep=0">page</a>.');
	}else{
		$email->addParagraph('Conformément au mode de modération choisi, cet avis est déjà publié dans la boutique. Si vous souhaitez le retirer, vous pouvez le faire en vous rendant <a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type=RVW_SITE&amp;rvw='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=reviews&amp;utm_content=review-edit">dans votre interface d\'administration</a>.');
	}


	// Indique comment contacter l'auteur de l'avis consommateur.
	$email->addParagraph( "Pour contacter l'auteur de cet avis consommateur, répondez simplement à cet email." );


	$email->addHtml('</div>');
	$email->addHtml( $config['email_html_footer'] );

	$email->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$res = $email->send();

	if( $res )
		message_set_send( $id );

	return $res;
}
// \endcond

/// @}


<?php

require_once('db.inc.php');
require_once('define.inc.php');
require_once('strings.inc.php');

// \cond onlyria
/** \defgroup model_profiles Profils utilisateurs
 *	\ingroup model_users
 *	Ce module comprend les fonctions nécessaires à la gestion des profils de compte utilisateurs.
 *
 *	@{
 */
// \endcond

// \cond onlydev
/** \defgroup model_profiles Profils utilisateurs
 *	\ingroup model_rights
 *	Ce module comprend les fonctions nécessaires à la gestion des profils de compte utilisateurs.
 *
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction détermine l'existence d'un profil.
 *
 *	@param int $id Obligatoire, Identifiant du profil à tester
 *	@param bool $tnt_link Facultatif, détermine la relation du profil testé avec le locataire courant. True pour un profil crée par le locataire, False pour un profil global, toute autre valeur pour les deux cas
 *
 *	@return bool True si le profil existe, False sinon
 *
 */
function gu_profiles_exists( $id, $tnt_link=null ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$sql = 'select 1 from gu_profiles where prf_is_deleted = 0 and prf_id='.$id;
	if( $tnt_link===true ){
		$sql .= ' and prf_tnt_id='.$config['tnt_id'];
	}elseif( $tnt_link===false ){
		$sql .= ' and prf_tnt_id=0';
	}else{
		$sql .= ' and ( prf_tnt_id=0 or prf_tnt_id='.$config['tnt_id'].' )';
	}

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}
// \endcond

/**	Cette fonction charge les profils utilisateurs stockés dans la base de données. Par défaut, les résultats sont triés par identifiant croissant.
 *
 *	@param int $id Facultatif, identifiant d'un profil sur lequel filtrer le résultat (ou tableau)
 *	@param string $name Facultatif, nom au singulier du profil. La comparaison est insensible à la casse
 *	@param string $name_pl Facultatif, nom au pluriel du profil. La comparaison est insensible à la casse
 *	@param $sort Facultatif, tableau associatif de tri (utiliser le nom des colonnes du résultat, sauf "tenant" qui ne sera pas pris en compte)
 *	@param $tnt_link Facultatif, détermine la relation des profils retournés avec le locataire courant. True filtre uniquement les profils crées par le locataire, False uniquement les profils globaux, toute autre valeur retourne les deux cas
 *	@param $tnt Facultatif, par défaut on tient compte des super-admin pour compter le nombre d'utilisateur, mettre true pour ne pas les inclure
 *	@param $get_count Optionnel, par défaut on retourne le nombre de client rattaché au profil, mettre False pour que ce ne soit pas le cas
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant du profil
 *		- name : nom du profil
 *		- pl_name : nom au pluriel du profil
 *		- desc : description du profil
 *		- display_prices : Type d'affichage des prix par défaut (ttc ou ht)
 *		- users : nombre de comptes utiliseurs rattachés au profil
 *		- tenant : identifiant du locataire
 *
 */
function gu_profiles_get( $id=false, $name=false, $name_pl=false, $sort=false, $tnt_link=null, $tnt=false, $get_count=true ){

	// Contrôles de cohérence sur le paramètre $id
	if( $id === false ){
		$id = 0;
	}
	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	// Si un utilisateur de type représentant est connecté, on applique automatiquement le filtre sur le seller_id pour que le nombre corresponde
	// au nombre d'utilisateurs qu'il a le droit de voir. Dans le cas contraire, il s'agit sûrement d'un compte administrateur et aucun filtre
	// ne s'applique.
	$seller_filter = false;
	if(
		isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id'])
		&& is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0
	){
		$seller_filter = $_SESSION['usr_seller_id'];
	}

	$sql = '
		select prf_id as id, prf_name as name, if(prf_pl_name="",prf_name,prf_pl_name) as pl_name, prf_desc as "desc",
		prf_display_prices as display_prices, prf_tnt_id as tenant
	';

	// Les calculs de nombre d'utilisateurs ne sont lancés que sur demande (coûteux)
	if( $get_count ){

		$sql .= '
			, (
				select count(*)
				from gu_users
				where usr_date_deleted is null
					and usr_tnt_id in ('.$config['tnt_id'].')
					and usr_prf_id=prf_id
		';

		if( $seller_filter ){
			$sql .= 'and (
				usr_seller_id = '.$_SESSION['usr_seller_id'].'
			';

			// Dans le contexte d'administration, on recherche aussi dans les relations clients
			if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
				$sql .= '
					or exists (
						select 1
						from rel_relations_hierarchy
						where rrh_tnt_id = '.$config['tnt_id'].'
							and rrh_rrt_id = 2
							and rrh_src_0 = '.$_SESSION['usr_seller_id'].'
							and rrh_src_1 = 0
							and rrh_src_2 = 0
							and rrh_dst_0 = usr_id
							and rrh_dst_1 = 0
							and rrh_dst_2 = 0
					)
				';
			}

			$sql .= '
				)
			';
		}

		$sql .= '
			) as users
		';

	}

	$sql .= '
		from gu_profiles
		where prf_is_deleted=0
	';

	// Filtre sur le tenant
	if( $tnt_link === true ){
		$sql .= ' and prf_tnt_id='.$config['tnt_id'].'';
	}elseif( $tnt_link === false ){
		$sql .= ' and prf_tnt_id=0';
	}else{
		$sql .= ' and prf_tnt_id in (0, '.$config['tnt_id'].')';
	}

	// Filtre sur l'identifiant d'un profil
	if( sizeof($id) ){
		$sql .= ' and prf_id in ('.implode(', ', $id).')';
	}

	// Filtre sur le nom du profil
	if( trim($name) ){
		$sql .= ' and LOWER(prf_name) = LOWER("'.addslashes(trim($name)).'")';
	}

	// Filtre sur le nom du profil au pluriel
	if( trim($name_pl) ){
		$sql .= ' and LOWER(prf_pl_name) = LOWER("'.addslashes(trim($name_pl)).'")';
	}

	// Applique un filtre automatique sur les profils si c'est un commercial qui est connecté
	if( $seller_filter ){
		$sql .= ' and prf_id not in ('. PRF_ADMIN .')';
	}

	// Validation du tri
	$real_sort = array();
	$allowed_columns = array('id', 'name', 'pl_name', 'users');
	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col => $dir ){
			$col = strtolower(trim($col));
			if( in_array($col, $allowed_columns) ){
				$real_sort[] = $col.' '.( strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc' );
			}
		}
	}
	if( !sizeof($real_sort) ){
		$real_sort[] = 'prf_id asc';
	}

	// application du tri
	$sql .= ' order by '.implode(', ', $real_sort);

	return ria_mysql_query($sql);

}

// \cond onlyria
/**	Cette fonction détermine si le profil passé en paramètre est spécifique à un locataire ou s'il est générique
 *
 *	@param int $id Identifiant du profil
 *
 *	@return bool False si le profil est global, True s'il est spécifique
 *
 */
function gu_profiles_is_tenant_linked( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$profiles = ria_mysql_query('
		select prf_tnt_id as tenant
		from gu_profiles
		where (prf_tnt_id=0 or prf_tnt_id='.$config['tnt_id'].')
		and prf_id='.$id.'
		and prf_is_deleted = 0
	');

	if( !$profiles || !ria_mysql_num_rows($profiles) ){
		return false;
	}

	$prf = ria_mysql_fetch_array($profiles);

	return $prf['tenant'];
}
// \endcond

// \cond onlyria
/**	Crée un nouveau profil utilisateur
 *
 *	@param string $name Nom singulier du profil, doit être unique (dans un contexte insensible à la casse)
 *	@param string $name_pl Optionnel, nom pluriel du profil, doit être unique (dans un contexte insensible à la casse). Si false, ajoute uniquement un "s" au nom singulier
 *	@param string $desc Optionnel, description du profil
 *  @param string $display_prices Optionnel, type d'affichage par défaut pour les prix (ttc ou ht)
 *
 *	@return int L'identifiant du profil en cas de succès, False en cas d'échec
 *
 */
function gu_profiles_add( $name, $name_pl=false, $desc='', $display_prices='ttc' ){

	// formatages
	$name = trim($name);
	if( $name_pl!==false ){
		if( $name_pl=='' && substr($name,-1,1)!='s' ){
			$name_pl = $name.'s';
		}elseif( $name_pl=='' ){
			$name_pl = $name;
		}
	}

	// contrôles
	if( $name_pl=='' || $name=='' ){
		return false;
	}
	$r = gu_profiles_get( false, $name );
	if( $r && ria_mysql_num_rows( $r ) ){
		return false;
	}
	$r = gu_profiles_get( false, false, $name_pl );
	if( $r && ria_mysql_num_rows( $r ) ){
		return false;
	}
	if($display_prices!='ttc' && $display_prices!='ht'){
		return false;
	}

	global $config;

	$r = ria_mysql_query('
		insert into gu_profiles
			( prf_tnt_id, prf_name, prf_pl_name, prf_desc, prf_display_prices )
		values
			( '.$config['tnt_id'].', \''.addslashes($name).'\', \''.addslashes($name_pl).'\', '.( trim($desc) ? '\''.addslashes($desc).'\'' : 'null' ).',\''.$display_prices.'\' )
	');

	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour les libellés d'un profil
 *	Seuls les profils crées par le locataire sont modifiables
 *
 *	@param int $id Obligatoire, identifiant du profil à modifier
 *	@param string $name Obligatoire, nom singulier du profil
 *	@param string $name_pl Optionnel, nom pluriel du profil. Si False, seul un "s" est ajouté au nom singulier
 *	@param string $desc Optionnel, description du profil
 *  @param string $display_prices Optionnel, type d'affichage par défaut pour les prix (ttc ou ht)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 *
 */
function gu_profiles_update( $id, $name, $name_pl=false, $desc='', $display_prices='ttc'){
	if( !gu_profiles_is_tenant_linked( $id ) ){
		return false;
	}

	if($display_prices!='ttc' && $display_prices!='ht'){
		return false;
	}

	$name = trim($name);
	$name_pl = $name_pl===false ? $name.'s' : trim($name_pl);

	$res = ria_mysql_query('
		select 1
		from gu_profiles
		where prf_id!='.$id.'
			and (
				lower(prf_name)=\''.addslashes( strtolower2($name) ).'\'
				or lower(prf_pl_name)=\''.addslashes( strtolower2($name_pl) ).'\'
			)
	');

	if( $res && ria_mysql_num_rows($res) )
		return false;

	global $config;

	return ria_mysql_query('
		update gu_profiles
		set prf_name=\''.addslashes($name).'\', prf_pl_name=\''.addslashes($name_pl).'\', prf_desc='.( trim($desc) ? '\''.addslashes($desc).'\'' : 'null' ).', prf_display_prices=\''.$display_prices.'\'
		where prf_tnt_id='.$config['tnt_id'].'
			and prf_id='.$id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de supprimer un profil
 *	Seul les profils crées par le locataire peuvent être supprimés
 *
 *	@param int $id Obligatoire, identifiant du profil à supprimer
 *	@param $substitute Facultatif, détermine une valeur de substitution pour les comptes utilisateurs étant rattaché au profil à supprimer. False, par défaut, empêche la suppression
 *
 *	@return bool true en cas de succès, False en cas d'échec
 *
 */
function gu_profiles_del( $id, $substitute=false ){

	if( !gu_profiles_is_tenant_linked( $id ) ){
		return false;
	}

	if( $substitute !== false ){
		if( !gu_profiles_exists( $substitute ) || $substitute == $id ){
			return false;
		}
	}

	global $config;

	$rprofile = gu_profiles_get( $id );
	if( !$rprofile ){
		return false;
	}elseif( !ria_mysql_num_rows( $rprofile ) ){
		return true;
	}else{
		$profile = ria_mysql_fetch_assoc( $rprofile );
		if( $profile['users'] ){
			if( $substitute ){
				$sql = '
					update gu_users
					set usr_prf_id = '.$substitute.'
					where usr_prf_id = '.$id.' and usr_tnt_id = '.$config['tnt_id'].'
				';
				if( !ria_mysql_query($sql) ){
					return false;
				}
			}else{
				return false;
			}
		}
	}

	$sql = '
		update gu_profiles
		set prf_is_deleted = 1
		where prf_id = '.$id.' and prf_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction retourne le type d'affichage par défaut pour les tarifs
 * @param int $id Identifiant du profil
 *
 * @return le type d'affichage (ttc ou ht), false en cas d'erreur
 */
function gu_profiles_get_display_prices( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0){
		return false;
	}

	// Spécifique client
	switch( $config['tnt_id'] ){
		case 171: // Chadog
			// Les comptes professionnel sur Chadog seront en HT
			if( $id == PRF_CUST_PRO ){
				return 'ht';
			}
		break;
	}

	$res = ria_mysql_query('
		select prf_display_prices from gu_profiles
		where prf_tnt_id in(0, '.$config['tnt_id'].')
			and prf_id='.$id.'
			and prf_is_deleted = 0
	');

	if( !$res || !ria_mysql_num_rows($res)){
		return false;
	}

	return ria_mysql_result($res, 0, 0);
}

// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer le nom d'un profil utilisateur à partir de son identifiant.
 *	La fonction fonctionne de la même façon pour un profil spécifique au client et pour un profil global
 *	@param int $id Obligatoire, Identifiant du profil dont on souhaite récupérer le nom
 *	@return string Le nom du profil
 *	@return bool False en cas d'échec
 */
function gu_profiles_get_name( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prf_name from gu_profiles
		where prf_tnt_id in (0, '.$config['tnt_id'].') and prf_id='.$id.' and prf_is_deleted = 0
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 0);

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un profil.
 *	@param int $id Identifiant du profil.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_profiles_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update gu_profiles
		set prf_date_modified = now()
		where prf_tnt_id in (0, '.$config['tnt_id'].')
		and prf_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

/// @}


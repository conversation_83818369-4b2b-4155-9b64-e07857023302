<?php

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('rewrite.inc.php');
require_once('faq.images.inc.php');
require_once('views.inc.php');


/** \defgroup tools_faq Foire Aux Questions
 *	\ingroup tools
 *
 *	@todo Indexer le contenu de la faq par le moteur de recherche
 *
 *	@todo Tri. Le tri des questions et des catégories est actuellement alphabétique. Le système de tri en place
 *	dans ezPublish pourrait être une excellente inspiration. L'utilisateur pourrait ainsi choisir entre tri alphabétique (asc ou desc) et tri arbitraire.
 *
 *	@{
 */

 // \cond onlyria
/**	Permet l'ajout d'une catégorie de questions.
 *
 *	@param string $name Intitulé de la catégorie, obligatoire.
 *	@param string $desc Description du contenu de la catégorie, facultatif.
 *
 *	@return int L'identifiant de la catégorie en cas de succès, false en cas d'échec.
 *
 */
function faq_categories_add( $name, $desc='' ){
	global $config;
	if( !($name = trim($name)) ) return false;

	$name = ucfirst($name);
	$desc = ucfirst(trim($desc));

	if( !ria_mysql_query("insert into faq_categories (cat_tnt_id,cat_name,cat_desc) values (".$config['tnt_id'].",'".addslashes($name)."','".addslashes($desc)."')") )
		return false;

	$id = ria_mysql_insert_id();

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($id), CLS_FAQ_CAT );
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_FAQ_CAT);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) )
				rew_rewritemap_add_specify_class( CLS_FAQ_CAT, $alias.$page['key'], $page['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
		}
	}

	try{
		// Index la catégorie dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_FAQ_CAT,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	$r = ria_mysql_query('update faq_categories set cat_url_alias="'.$alias.'" where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);

	return $id;
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour d'une catégorie de questions.
 *	Cette fonction permet la modification du nom et de la description d'une catégorie de questions.
 *
 *	@param int $id Identifiant interne de la catégorie
 *	@param string $name Nom/Titre de la catégorie
 *	@param string $desc Description du contenu de la catégorie
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function faq_categories_update( $id, $name, $desc='' ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !($name = trim($name)) ) return false;

	$lastname = faq_categories_get_name($id);
	$name = ucfirst($name);
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query("update faq_categories set cat_name='".addslashes($name)."', cat_desc='".addslashes($desc)."' where cat_tnt_id=".$config['tnt_id']." and cat_id=".$id);

	if( $res ){
		// Met à jour l'url simplifiée de la catégorie
		rew_rewritemap_del_multilingue( _FLD_FAQ_CAT_URL, array($id) );
		rew_rewritemap_del( '/faq/'.urlalias($lastname) );

		// Récupère les sites
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) )
			return false;

		// Crée les alias
		$alias = rew_rewritemap_generated( array($id), CLS_FAQ_CAT );
		while( $wst = ria_mysql_fetch_array($rwst) ){
			$prd_pages = cfg_urls_get( $wst['id'], CLS_FAQ_CAT);
			if( $prd_pages ){
				while( $page = ria_mysql_fetch_array($prd_pages) )
					rew_rewritemap_add_specify_class( CLS_FAQ_CAT, $alias.$page['key'], $page['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
			}
		}

		ria_mysql_query('update faq_categories set cat_url_alias="'.$alias.'" where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);

		try{
			// Réindex la catégorie dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_FAQ_CAT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Permet la modification d'un categorie FAQ
 *	@param int $cat Obligatoire, Identifiant de la catégorie FAQ
 *	@param string $tag_title Facultatif, titre de la catégorie FAQ
 *	@param string $tag_desc Facultatif, meta-description de la catégorie FAQ
 *	@param string $keywords Facultatif, mots clés de la catégorie FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_categories_update_referencing( $cat, $tag_title='', $tag_desc='', $keywords='' ){
	global $config;
	if(!is_numeric($cat) || !faq_categories_exists($cat)) return false;

	//on met a jour
	return 	ria_mysql_query('update faq_categories set cat_keywords="'.addslashes($keywords).'",cat_tag_title="'.addslashes($tag_title).'", cat_tag_desc="'.addslashes($tag_desc).'" where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE categorie FAQ
 *	@param int $cat Obligatoire, Identifiant de la catégorie FAQ
 *	@param string $tag_title Facultatif, titre de la catégorie FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_categories_update_referencing_tag_title( $cat, $tag_title='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update faq_categories
		set cat_tag_title='.( trim($tag_title)!='' ? '\''.addslashes( $tag_title ).'\'' : 'null' ).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE categorie FAQ
 *	@param int $cat Obligatoire, Identifiant de la catégorie FAQ
 *	@param string $tag_desc Facultatif, meta-description de la catégorie FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_categories_update_referencing_tag_desc( $cat, $tag_desc='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update faq_categories
		set cat_tag_desc='.( trim($tag_desc)!='' ? '\''.addslashes( $tag_desc ).'\'' : 'null' ).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Permet la suppression d'une catégorie de questions.
 *	Pour éviter des erreurs de manipulation, la suppression échoue si la catégorie contient une ou plusieurs questions.
 *
 *	@param int $id Identifiant interne de la catégorie
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function faq_categories_del( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( faq_questions_get_count($id) ) return false;

	// Suppression des urls traduite
	rew_rewritemap_del_multilingue( _FLD_FAQ_CAT_URL, array($id) );
	rew_rewritemap_del( '', '/faq/category.php?cat='.$id );

	// Supprime la catégorie de l'index du moteur de recherche
	if( $res = ria_mysql_fetch_array(ria_mysql_query('select cnt_id from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_alt_url="/admin/tools/faq/category.php?cat='.$id.'"')) )
		search_index_clean( 'faq-cat', $res['cnt_id'] );

	return ria_mysql_query('delete from faq_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
}
// \endcond

/** Cette fonction permet le chargement d'une ou plusieurs catégories de questions.
 *	Cette fonction renvoie un résultat de requête MySQL, comprenant les colonnes suivantes :
 *		- id : identifiant de la catégorie
 *		- name : nom de la catégorie
 *		- url_alias : url simplifiée d'accès à la catégorie
 *		- desc : description de la catégorie
 *		- questions : nombre de questions contenues dans la catégorie
 *		- questions_published : nombre de questions publiées
 *	Les catégories sont retournées triées par nom (ordre croissant).
 *
 *	@param int $id Facultatif, identifiant d'une catégorie sur laquelle filtrer le résultat (ou tableau)
 *	@param $publish	Facultatif. Si true, ne retourne que les catégories publiées
 *	@param $urlalias Facultatif. Filtrer le résultat sur la catégorie d'alias $urlalias
 *	@param $sort Facultatif, tri à appliquer au résultat retourné
 *
 */
function faq_categories_get( $id=0, $publish=false, $urlalias='', $sort=false ){
	global $config;

	$sql = '
		select cat_id as id, cat_name as name, cat_url_alias as url_alias, cat_desc as "desc", count(qall.qst_id) as questions, count(qpub.qst_id) as questions_published, cat_keywords as keywords, cat_tag_title as tag_title, cat_tag_desc as tag_desc, cat_pos as pos
		from faq_categories
			left join faq_questions as qall on (qall.qst_tnt_id='.$config['tnt_id'].' and cat_id=qall.qst_cat_id)
			left join faq_questions as qpub on (qpub.qst_tnt_id='.$config['tnt_id'].' and cat_id=qpub.qst_cat_id and qpub.qst_publish=1 and qpub.qst_id=qall.qst_id)
		where cat_tnt_id='.$config['tnt_id'].'
	';

	if( is_numeric($id) && $id>0 )
		$sql .= ' and cat_id='.$id.' group by cat_id, cat_name, cat_desc, cat_pos';
	elseif( is_array($id) && sizeof($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ) return false;
		}
		$sql .= ' and cat_id in ('.implode(', ', $id).') group by cat_id, cat_name, cat_pos';
	}else{
		$sql .= 'group by cat_id, cat_name, cat_url_alias, cat_desc, cat_pos';
	}

	if( trim($urlalias) ){
		$sql .= " having questions_published>0 and url_alias='".$urlalias."'";
	}elseif( $publish ){
		$sql .= ' having questions_published>0';
	}

	$sort_final = array();
	if (is_array($sort)) {
		foreach ($sort as $col => $dir) {
			if (!in_array($dir, array('asc', 'desc'))) {
				$dir = 'asc';
			}

			switch ($col) {
				case 'id':
					array_push($sort_final, 'cat_id '.$dir);
					break;
				case 'name':
					array_push($sort_final, 'cat_name '.$dir);
					break;
			}
		}
	}

	if( sizeof($sort_final)==0 ){
		$perso_sort = faq_categories_get_order();

		$sort_final = $perso_sort ? array('cat_pos asc') : array( 'cat_name asc' );
	}

	if ( !is_numeric($id) || $id<=0 ) {
		$sql .= ' order by '.implode(', ', $sort_final);
	} else {
		$sql .= ' limit 0,1';
	}

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction est un raccourci utilisé pour les urls simplifiées.
 *	Elle a pour objectif de fournir une alternative plus rapide que la
 *	fonction faq_categories_get.
 *	@param $urlalias Alias de la catégorie
 *	@return int L'identifiant de la catégorie, ou FALSE en cas d'erreur
 */
function faq_categories_get_id_by_alias( $urlalias ){
	global $config;
	$r = ria_mysql_query("select cat_id from faq_categories where cat_tnt_id=".$config['tnt_id']." and cat_url_alias='".$urlalias."'");
	if( ria_mysql_num_rows($r)>0 )
		return ria_mysql_result($r,0,0);
	else
		return false;
}
// \endcond

/** Cette fonction permet de récupérer un nom d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une categorie
 *	@return Retourne le nom d'une catégorie
 *	@return bool Retourne false si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function faq_categories_get_name( $cat ){
	if( !faq_categories_exists($cat) ) return false;
	global $config;

	$res = ria_mysql_query( 'select cat_name as name from faq_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

// \cond onlyria
/** Cette fonction permet de récupérer l'url d'un catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 * 	@return Retourne l'url d'une catégorie
 *	@return bool Retourne false si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function faq_categories_get_url( $cat ){
	if( !faq_categories_exists($cat) ) return false;
	global $config;

	$res = ria_mysql_query( 'select cat_url_alias as url from faq_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'url' );
}
// \endcond

/**	Teste la validité d'un identifiant de catégorie.
 *	@param int $id Identifiant de catégorie à tester
 *	@return bool true si la catégorie existe
 *	@return bool false si la catégorie n'existe pas
 */
function faq_categories_exists( $id ){
	global $config;
	$sql = 'select 1 from faq_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id = '.$id;
	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}

// \cond onlyria
/** Cette fonction permet de déterminer si le menu faq doit être activé ou non dans la partie publique du site.
 *	@return bool true si la faq contient au moins une question publiée
 *	@return bool false si la faq est vide.
 */
function faq_published(){
	global $config;
	return ria_mysql_num_rows(ria_mysql_query('select qst_id from faq_questions where qst_tnt_id='.$config['tnt_id'].' and qst_publish!=0 limit 0,1'));
}
// \endcond

/** Cette fonction permet le chargement de questions.
 *	Le résultat fournit par cette requête dépend des paramètres optionnels.
 *
 *	@param int $id Identifiant de question sur lequel filtrer le résultat (ou tableau)
 *	@param int $cat Identifiant de catégorie de question sur lequel filtrer le résultat
 *	@param bool $publish Booléen indiquant si l'on filtre le résultat sur les seules questions publiées ou non.
 *	@param $sort Optionnel, tri à appliquer au résultat retourné
 *	@param $is_frequent Optionnel, indique si l'on filtre les résultat sur les questions les plus fréquentes ou non
 *
 *	@return resource Les réponses sont retournées sous la forme d'un résultat de requête MySQL comprenant les
 *	colonnes suivantes :
 *		- id : identifiant de la question
 *		- cat_id : identifiant de la catégorie dans laquelle la question est classée
 *		- cat_name : nom de la catégorie dans laquelle la question est classée
 *		- name : Intitulé de la question
 *		- desc : réponse
 *		- publish : indique si la question est publiée sur le site ou non.
 *		- alias : alias de la question
 *		- cat_url : url de la catégorie de FAQ associé
 *		- keywords : mots clés liés à la question
 *		- tag_title : pour le référencement, meta title de la page
 *		- tag_desc : pour le référencement, meta description de la page
 *		- pos : position de la question dans sa catégorie
 *		- frequent : booléen permettant de savoir si la question fait parti des questions les plus fréquemment posées
 *		- prev_id : si $id et $cat renseignés et numériques, ???
 *		- next_id : si $id et $cat renseignés et numériques, ???
 *
 */
function faq_questions_get( $id=0, $cat=0, $publish=false, $sort=false, $is_frequent=false ){
	global $config;

	// Ecriture de la requête
	$sql = '
		select qst_id as id, qst_name as name, qst_desc as "desc", cat_id, cat_name, qst_publish as publish, qst_alias as alias,
			cat_url_alias as cat_url, qst_keywords as keywords, qst_tag_title as tag_title, qst_tag_desc as tag_desc, qst_pos as pos,
			qst_is_frequent as frequent
	';

	if( is_numeric($id) && $id>0 && is_numeric($cat) && $cat>0 ){
		$sql .= ' ,(select max(qst_id) from faq_questions, faq_categories where qst_tnt_id='.$config['tnt_id'].' and cat_tnt_id=qst_tnt_id and qst_cat_id=cat_id and qst_publish=1 and qst_id < '.$id.' and qst_cat_id='.$cat.' limit 0,1) as prev_id';
	}
	if( is_numeric($id) && $id>0 && is_numeric($cat) && $cat>0 ){
		$sql .= ' ,(select min(qst_id) from faq_questions, faq_categories where qst_tnt_id='.$config['tnt_id'].' and cat_tnt_id=qst_tnt_id and qst_cat_id=cat_id and qst_publish=1 and qst_id > '.$id.' and qst_cat_id='.$cat.' limit 0,1) as next_id';
	}

	$sql .=' from faq_questions, faq_categories
		where qst_tnt_id='.$config['tnt_id'].' and cat_tnt_id='.$config['tnt_id'].' and qst_cat_id=cat_id
	';

	// Si l'on veut récupérer uniquement les questions publiées
	if( $publish ){
		$sql .= ' and qst_publish=1';
	}

	// Si l'on veut récupérer uniquement les questions fréquentes
	if( $is_frequent ){
		$sql .= ' and qst_is_frequent=1';
	}

	if( is_numeric($cat) && $cat>0 ){
		$sql .= ' and qst_cat_id='.$cat;
	}

	if( is_numeric($id) && $id>0 ){
		$sql .= ' and qst_id='.$id.' limit 0,1';
	}else{
		if( is_array($id) && sizeof($id) ){
			foreach( $id as $one_id ){
				if( !is_numeric($one_id) || $one_id<=0 ){
					return false;
				}
			}
			$sql .= ' and qst_id in ('.implode(', ', $id).')';
		}

		// Gestion du tri des questions
		$sort_final = array();
		if( is_array($sort) ){
			foreach( $sort as $col => $dir ){
				if( !in_array($dir, array('asc', 'desc')) ){
					$dir = 'asc';
				}

				switch( $col ){
					case 'id':
						array_push($sort_final, 'qst_id ' . $dir);
						break;
					case 'name':
						array_push($sort_final, 'qst_name ' . $dir);
						break;
				}
			}
		}

		if( sizeof($sort_final)==0 ){
			$perso_sort = faq_questions_get_order($cat);

			$sort_final = $perso_sort ? array('qst_pos asc') : array( 'qst_name asc' );
		}

		if( !is_numeric($id) || $id<=0 ){
			$sql .= ' order by '.implode(', ', $sort_final);
		}
	}

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction retourne l'url d'une réponse
 *	@param $qst Obligatoire, identifiant de la réponse
 *	@return Retourne l'url de la question
 *	@return bool Retourne false si le paramètre est omis ou bien si la réponse n'existe pas
 */
function faq_questions_get_url( $qst ){
	if( !faq_questions_exists($qst) ) return false;
	global $config;

	$res = ria_mysql_query( 'select qst_alias as alias from faq_questions where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$qst );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'alias' );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne le nombre de questions total ou contenues dans une catégorie donnée.
 *
 *	@param int $cat Identifiant de la catégorie pour laquelle le décompte doit être effectué. Optionnel.
 *	@return Le nombre de questions total, ou le nombre de questions contenues dans une catégorie.
 *
 */
function faq_questions_get_count( $cat=0 ){
	global $config;
	$sql = 'select count(*) from faq_questions
		where qst_tnt_id='.$config['tnt_id'].'
	';
	if( is_numeric($cat) && $cat>0 )
		$sql .= ' and qst_cat_id='.$cat;
	return ria_mysql_result(ria_mysql_query($sql),0,0);
}
// \endcond

// \cond onlyria
/** Permet la création d'une question, dans une catégorie donnée.
 *
 *	@param int $cat Identifiant de la catégorie dans laquelle créer la question. Obligatoire.
 *	@param string $name Intitulé de la question. Obligatoire.
 *	@param string $desc Réponse. Obligatoire.
 *	@param bool $publish Indique si le couple question/réponse est publié sur le site ou non. Obligatoire.
 *	@param $is_frequent Optionnel, indique si la question est considérée comme fréquente.
 *
 *	@return int l'identifiant de la question en cas de succès, false en cas d'échec
 *
 */
function faq_questions_add( $cat, $name, $desc, $publish, $is_frequent=false ){
	// Vérifications sur les paramètres
	{
		if( !($name = trim($name)) ){
			return false;
		}
		if( !($desc = trim($desc)) ){
			return false;
		}
		if( !faq_categories_exists($cat) ){
			return false;
		}
	}

	global $config;

	// Uniformisation du nom et de la description. Le premier caractère doit être une majuscule
	$name = ucfirst($name);
	$desc = ucfirst($desc);

	// Crée la requête
	$fields = array( 'qst_tnt_id', 'qst_cat_id', 'qst_name', 'qst_desc', 'qst_publish', 'qst_is_frequent' );
	$values = array( $config['tnt_id'], $cat, '"'.addslashes($name).'"', '"'.addslashes($desc).'"', $publish ? 1 : 0, $is_frequent ? 1 : 0 );

	$sql = '
		insert into faq_questions ( '.implode( ', ', $fields ).' ) values ( '.implode( ', ', $values ).' )
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	// Crée les alias
	$alias = rew_rewritemap_generated( array($id), CLS_FAQ_QST );
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_FAQ_QST);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				rew_rewritemap_add_specify_class( CLS_FAQ_QST, $alias.$page['key'], $page['url'].'?cat='.$cat.'&qst='.$id, 200, $wst['id'], false, null, array($cat, $id) );
			}
		}
	}

	if( $alias ){
		faq_questions_update_alias($id,$alias);
	}

	try{
		// Index la question dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_FAQ_QST,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return $id;
}
// \endcond

/** Teste la validité d'un identifiant de question.
 *
 *	@param int $id Identifiant de question dont on souhaite tester la validité
 *	@return bool true si la catégorie existe, false dans le cas contraire.
 *
 */
function faq_questions_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select qst_id from faq_questions where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$id))==1;
}

// \cond onlyria
/** Permet la mise à jour d'une question, dans une catégorie donnée.
 *
 *	@param int $id Identifiant de la question. Obligatoire.
 *	@param int $cat Identifiant de la catégorie dans laquelle classer la question. Obligatoire.
 *	@param string $name Intitulé de la question. Obligatoire.
 *	@param string $desc Réponse. Obligatoire.
 *	@param bool $publish Indique si le couple question/réponse est publié sur le site ou non. Obligatoire.
 *	@param $is_frequent Optionnel, indique si la question est considérée comme fréquente.
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function faq_questions_update( $id, $cat, $name, $desc, $publish, $is_frequent=false ){
	// Vérifications sur les paramètres
	{
		if( !($name = trim($name)) ){
			return false;
		}
		if( !($desc = trim($desc)) ){
			return false;
		}
		if( !faq_questions_exists($id) ){
			return false;
		}
		if( !faq_categories_exists($cat) ){
			return false;
		}
	}

	global $config;

	// Uniformisation du nom et de la description. Le premier caractère doit être une majuscule
	$name = ucfirst($name);
	$desc = ucfirst($desc);

	// Récupération en base de la question de FAQ
	$faq = ria_mysql_fetch_array(faq_questions_get($id));

	// Ecriture de la requête de mise à jour
	$sql = '
		update faq_questions set
			qst_name="'.addslashes($name).'",
			qst_desc="'.addslashes($desc).'",
			qst_cat_id='.$cat.',
			qst_publish='.($publish ? 1 : 0).',
			qst_is_frequent='.($is_frequent ? 1 : 0).'
		where qst_tnt_id='.$config['tnt_id'].'
		and qst_id='.$id.'
	';

	// Exécution de la requête
	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	$alias = false;
	// Si la catégorie de faq, ou que le nom est différent d'à l'origine
	if( $faq['cat_id'] != $cat || $faq['name'] != $name ){
		// Suppression des anciens alias
		rew_rewritemap_del_multilingue( _FLD_FAQ_QST_URL, array($id) );
		rew_rewritemap_del($faq['alias']);

		// Récupère les sites
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) ){
			return false;
		}

		// Crée les alias
		$alias = rew_rewritemap_generated( array($id), CLS_FAQ_QST );
		while( $wst = ria_mysql_fetch_array($rwst) ){
			$prd_pages = cfg_urls_get( $wst['id'], CLS_FAQ_QST);
			if( $prd_pages ){
				while( $page = ria_mysql_fetch_array($prd_pages) ){
					rew_rewritemap_add_specify_class( CLS_FAQ_QST, $alias.$page['key'], $page['url'].'?cat='.$cat.'&qst='.$id, 200, $wst['id'], false, null, array($cat, $id) );
				}
			}
		}
	}

	try{
		// Réindex la question dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_FAQ_QST,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	if( $alias ){
		return faq_questions_update_alias($id, $alias);
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet la modification d'une question FAQ
 *	@param int $id Obligatoire, Identifiant de la question FAQ
 *	@param string $tag_title Facultatif, titre de la question FAQ
 *	@param string $tag_desc Facultatif, meta-description de la question FAQ
 *	@param string $keywords Facultatif, mots clés de la question FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_questions_update_referencing( $id, $tag_title='', $tag_desc='', $keywords='' ){
	global $config;
	if(!is_numeric($id) || !faq_questions_exists($id)) return false;

	// on ajoute l'url dans la table
	return 	ria_mysql_query('update faq_questions set qst_keywords="'.addslashes($keywords).'",qst_tag_title="'.addslashes($tag_title).'", qst_tag_desc="'.addslashes($tag_desc).'" where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$id);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'une question FAQ
 *	@param int $id Obligatoire, Identifiant de la question FAQ
 *	@param string $tag_title Facultatif, titre de la question FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_questions_update_referencing_tag_title( $id, $tag_title='' ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update faq_questions
		set qst_tag_title='.( trim($tag_title)!='' ? '\''.addslashes( $tag_title ).'\'' : 'null' ).'
		where qst_tnt_id='.$config['tnt_id'].'
			and qst_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'une question FAQ
 *	@param int $id Obligatoire, Identifiant de la question FAQ
 *	@param string $tag_desc Facultatif, meta-description de la question FAQ
 *	@return bool True en cas de succès, false en cas d'échec
 */
function faq_questions_update_referencing_tag_desc( $id, $tag_desc='' ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update faq_questions
		set qst_tag_desc='.( trim($tag_desc)!='' ? '\''.addslashes( $tag_desc ).'\'' : 'null' ).'
		where qst_tnt_id='.$config['tnt_id'].'
			and qst_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Permet la mise à jour de l'alias d'une question
 *
 *	@param int $id Identifiant de la question. Obligatoire.
 *	@param $alias Url externe de la question
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function faq_questions_update_alias( $id, $alias ){
	global $config;
	if( !faq_questions_exists($id) ) return false;
	if( !($alias = trim($alias)) ) return false;

	return ria_mysql_query("update faq_questions set qst_alias='".addslashes($alias)."' where qst_tnt_id=".$config['tnt_id']." and qst_id=".$id);
}
// \endcond

// \cond onlyria
/** Permet de dire si une question est considérée parmi les plus fréquentes ou non
 *
 *	@param int $id Obligatoire, identifiant de la question.
 *	@param $is_frequent Obligatoire, booléen indiquant si la question est fréquente ou non
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
 function faq_questions_set_is_frequent( $id, $is_frequent ){
	if( !faq_questions_exists($id) ){
		return false;
	}

	global $config;

	return ria_mysql_query('update faq_questions set qst_is_frequent='.($is_frequent ? 1 : 0).' where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$id);
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une question.
 *
 *	@param int $id Identifiant de la question à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function faq_questions_del( $id ){
	global $config;
	if( !faq_questions_exists($id) ) return false;

	// Suppression des urls traduite
	rew_rewritemap_del_multilingue( _FLD_FAQ_QST_URL, array($id) );

	if( $res = ria_mysql_fetch_array(faq_questions_get($id)) ){

		if( $cid = ria_mysql_fetch_array(ria_mysql_query('select cnt_id from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_alt_url="/admin/tools/faq/question.php?qst='.$id.'&cat='.$res['cat_id'].'"')) )
			search_index_clean( 'faq-qst', $cid['cnt_id'] );

	}

	$url = faq_questions_get_url( $id );
	if( trim($url)!='' )
		rew_rewritemap_del( $url );

	return ria_mysql_query('delete from faq_questions where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$id);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'indexer une catégorie de FAQ
 *	@param int $cat Obligatoire, identifiant d'une categorie
 *	@param $lng Optionnel, code ISO 639-1 de la langue, par défaut la FAQ est indexé par la langue du site
 *	@return bool True si tout s'est correctement déroulé
 *	@return bool False dans le cas contraire
 */
function faq_categories_add_index( $cat, $lng=false ){
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$rcat = faq_categories_get( $cat );

	if( !$rcat || !ria_mysql_num_rows($rcat) )
		return false;

	$cat = ria_mysql_fetch_array( $rcat );

	if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
		$tsk_cat = fld_translates_get( CLS_FAQ_CAT, $cat['id'], $lng, $cat, array(_FLD_FAQ_CAT_NAME=>'name', _FLD_FAQ_CAT_DESC=>'desc') );
		$cat['name'] = $tsk_cat['name']; $cat['desc'] = $tsk_cat['desc'];
	}

	// Indexation de la catégorie
	return search_index_content( '/faq/'.urlalias($cat['name']), 'faq-cat', $cat['name'], $cat['desc'], $cat['name'].' '.$cat['desc'], '/admin/tools/faq/category.php?cat='.$cat['id'], $cat['questions_published']>0 ? 1 : 0, $cat['id'], false, '', '', $lng );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'indexer une question de FAQ
 *	@param $qst Optionnel, identifiant d'une question
 *	@param int $cat Optionnel, identifiant d'une catégorie de questions
 *	@param $lng Optionnel, code ISO 639-1 de la langue, par défaut la FAQ est indexé par la langue du site
 *	@return bool Retourne true si tout s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function faq_questions_add_index( $qst=0, $cat=0, $lng=false ){
	if( $qst>0 && !faq_questions_exists($qst) ) return false;
	if( $cat>0 && !faq_categories_exists($cat) ) return false;
	global $config;

	$rqst = faq_questions_get( $qst, $cat );

	if( !$rqst )
		return false;

	while( $qst = ria_mysql_fetch_array($rqst) ){
		if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
			$tsk_qst = fld_translates_get( CLS_FAQ_QST, $qst['id'], $lng, $qst, array(_FLD_FAQ_QST_NAME=>'name', _FLD_FAQ_QST_DESC=>'desc') );
			$qst['name'] = $tsk_qst['name']; $qst['desc'] = $tsk_qst['desc'];
		}

		if( !search_index_content( '/faq/'.urlalias($qst['name']), 'faq-qst', $qst['name'], $qst['desc'], $qst['name'].' '.$qst['desc'], '/admin/tools/faq/question.php?qst='.$qst['id'].'&cat='.$qst['cat_id'], $qst['publish'], $qst['id'], false, '', '', $lng ) )
			return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de ré-indexer tout la FAQ.
 *	@param $lng Optionnel code ISO 639-1 de la langue, par défaut la FAQ est indexé dans toutes les langues
 */
function faq_index_rebuild( $lng=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	$r_cat = faq_categories_get();
	while( $cat = ria_mysql_fetch_array($r_cat) ){

		foreach( $lng as $l ){
			faq_categories_add_index( $cat['id'], $l );

			// Indexation de ses questions
			faq_questions_add_index( 0, $cat['id'], $l );
		}

	}
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les catégories de faq à un niveau donné.
 *
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function faq_categories_get_order(){
	global $config;

	return ria_mysql_num_rows(ria_mysql_query('
		select cat_id from faq_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_pos is not null
	'))>0;
}
// \endcond

// \cond onlyria
/**	Déplace la catégorie avant ou après une autre catégorie
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *
 *	@param int $source Identifiant de la catégorie source
 *	@param int $target Identifiant de la catégorie cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function faq_categories_position_update( $source, $target, $where ) {
	$res = obj_position_update( DD_FAQ_CATEGORY, $source, $target, $where );

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de catégories de la faq.
 *
 *	@param $order Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function faq_categories_order_update( $order ){

	if( !( $types = faq_categories_get() ) ){
		return false;
	}

	global $config;

	$pos = 0;
	while( $t = ria_mysql_fetch_array($types) ){
		ria_mysql_query('update faq_categories set cat_pos='.( $order ? $pos : 'NULL' ).' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$t['id']);
		$pos++;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les question d'une catégorie à un niveau donné.
 *
 * 	@param int $cat Obligatoire, identifiant de la catégorie de questions dont on souhaite connaître le mode de tri
 *
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function faq_questions_get_order($cat){
	global $config;

	return ria_mysql_num_rows(ria_mysql_query('
		select qst_id from faq_questions
		where qst_tnt_id='.$config['tnt_id'].' and qst_pos is not null and qst_cat_id = '.$cat.'
	'))>0;
}
// \endcond

// \cond onlyria
/**	Déplace la question avant ou après une autre question
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *
 *	@param int $source Identifiant de la question source
 *	@param int $target Identifiant de la question cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function faq_questions_position_update( $source, $target, $where ) {
	$res = obj_position_update( DD_FAQ_QUESTIONS, $source, $target, $where );

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de questions d'une catégorie.
 *
 *	@param $order Obligatoire, mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *	@param int $cat Obligatoire, identifiant de la catégorie de questions
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function faq_questions_order_update( $order, $cat ){

	if( !( $types = faq_questions_get(0, $cat) ) ){
		return false;
	}

	global $config;

	$pos = 0;
	while( $t = ria_mysql_fetch_array($types) ){
		ria_mysql_query('update faq_questions set qst_pos='.( $order ? $pos : 'NULL' ).' where qst_tnt_id='.$config['tnt_id'].' and qst_id='.$t['id']);
		$pos++;
	}

	return true;
}
// \endcond

/// @}

<?php
/*	\defgroup fiscalYear Exercice comptable
 *
 *	Ce module est destiné à la gestion des exercies comptables.
 *
 *	@{
 */

/** Cette fonction permet de récupérer les exercices comptables d'un tenant
 * @param int $id Facultatif, identifiant de l'exercice comptable
 * @param bool|string $start Facultatif, date de début de l'exercice comptable. Valeur par défaut : false
 * @param bool|string $end Facultatif, date de fin de l'exercice comptable. Valeur par défaut : false
 * @param bool $is_closed Facultatif, par default a false permet de récupérer les exercices comptables qui ne sont pas cloturé
 *
 * @return resource Un résultat de requête mysql avec les colonnes suivantes :
 *		- tnt_id : Identifiant du tenant
 *		- id : Identifiant de l'exercice comptable
 *		- start : date de début de l'exercice comptable
 *		- end : date de fin de l'exercice comptable
 *		- closed : si l'exercice comptable est clôturé ou pas
 * False si erreur
 */
function fly_fiscal_year_get( $id=0, $start=false, $end=false, $is_closed=false ){
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}

	if( $start!==false && !isdateheure($start) ){
		return false;
	}

	if( $end!==false && !isdateheure($end) ){
		return false;
	}
	if( $start !== false && $end !== false ){
		if( strtotime($start) > strtotime($end) ){
			return false;
		}
	}

	global $config;

	$sql = '
		select fly_tnt_id as tnt_id,
			fly_id as id,
			fly_start as start,
			fly_end as end,
			fly_closed as closed
		from fly_fiscal_year
		where fly_tnt_id = '.$config['tnt_id'].'
			and fly_date_deleted is null
	';

	if( $id ){
		$sql .= ' and fly_id='.$id;
	}
	if( $start !== false ){
		$sql .= 'and fly_start >= "'.dateheureparse($start).'"';
	}
	if( $end !== false ){
		$sql .= 'and fly_end <= "'.dateheureparse($end).'"';
	}
	if( $is_closed !== null ){
		if( !$is_closed ){
			$sql .= 'and fly_closed = 0';
		}else{
			$sql .= 'and fly_closed = 1';
		}
	}

	$r = ria_mysql_query($sql);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return $r;
}

/** Cette fonction permet d'ajouter un exercice comptable
 *	@param string $start Obligatoire, date de début de l'exercice comptable
 *	@param string $end Obligatoire, date de fin de l'exercice comptable
 *	@param bool $closed Facultatif, détermine si l'exercice comptable est clôturé ou non. Valeur par défaut : false
 *
 *	@return int l'identifiant de la ligne ajouter, false si erreur
 */
function fly_fiscal_year_add( $start, $end, $closed=false ){
	if( !isdateheure($start) ){
		return false;
	}

	if( !isdateheure($end) ){
		return false;
	}

	if( strtotime(dateheureparse($start)) > strtotime(dateheureparse($end)) ){
		return false;
	}

	global $config;

	$data = array(
		'fly_tnt_id' => $config['tnt_id'],
		'fly_start' => '"'.dateheureparse($start).'"',
		'fly_end' => '"'.dateheureparse($end).'"',
		'fly_closed' => !$closed ? 0 : 1,
		'fly_date_created' => 'now()'
	);

	$sql = '
		insert into fly_fiscal_year
			('.implode(', ', array_keys($data)).')
		values
			('.implode(', ', array_values($data)).')
	';

	$r = ria_mysql_query($sql);
	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de modifier un exercice comptable
 *	@param int $id Obligatoire, identifiant de l'exercice comptable
 *	@param string $start Obligatoire, date de début de l'exercice comptable
 *	@param string $end Obligatoire, date de fin de l'exercice comptable
 *	@param bool $closed Facultatif, détermine si l'exercice comptable est clôturé ou non. Valeur par défaut : false.
 *
 *	@return bool true, si echec false
 */
function fly_fiscal_year_upd( $id, $start, $end, $is_closed=false ){
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}

	if( !isdateheure($start) ){
		return false;
	}

	if( !isdateheure($end) ){
		return false;
	}

	if( strtotime(dateheureparse($start)) > strtotime(dateheureparse($end)) ){
		return false;
	}

	global $config;

	$data = array(
		'fly_start = "'.dateheureparse($start).'"',
		'fly_end = "'.dateheureparse($end).'"',
		'fly_closed = '.(!$is_closed ? 0 : 1)
	);

	$sql = '
		update fly_fiscal_year
		set '.implode(',', $data).'
		where fly_tnt_id = '.$config['tnt_id'].'
		and fly_id ='.$id.'
	';

	$r = ria_mysql_query($sql);
	if( !$r ){
		return false;
	}

	return true;
}

/** Cette fonction permer de supprimer virtuelment un exercice comptable
 * @param int $id Obligatoire, Identifiant de l'exercice comptable
 *
 * @return bool true ou false suivant le succès ou l'échec de l'opération
 */
function fly_fiscal_year_del( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update fly_fiscal_year
		set fly_date_deleted = now()
		where fly_tnt_id = '.$config['tnt_id'].'
			and fly_id = '.$id.'
	';

	$r = ria_mysql_query($sql);
	if( !$r ){
		return false;
	}

	return true;
}

// @}
<?php

require_once('db.inc.php');
require_once('fields.inc.php');
require_once('cms.inc.php');
require_once('documents.inc.php');
require_once('tools.faq.inc.php');
require_once('define.inc.php');
require_once('systems.inc.php');
require_once('errors.inc.php');
require_once('sitemaps.inc.php');
require_once('products.inc.php');
require_once('websites.inc.php');

/**	\defgroup rewriting URL Rewriting
 *	Ce module comprend les fonctions nécessaires à la gestion de l'url rewriting
 *	@{
 */

// \cond onlyria
/** Cette fonction permet de réécrire les urls pour un contenu
 *	@param array $obj Obligatoire, tableau contenant au minimum un objet
 *	@param int $class Obligatoire, classe de l'objet
 *	@param int $wst Facultatif, identifiant d'un site web
 *	@param bool|string $lng Facultatif, code langue (ISO 3166)
 *	@return string Une chaine de caractère contenant l'url réécrite pour le contenu passé en paramètre
 */
function rew_rewritemap_generated( $obj, $class, $wst=0, $lng=false ){
	if( !is_array($obj) || sizeof($obj)<=0 ) return false;
	global $config;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];
	$lng = strtolower($lng);

	// Url externe du produit
	$url = false;

	// Pattern de la classe
	$pattern = fld_classes_get_pattern( $class, $lng );
	$split = explode('/', $pattern);

	if( $class==CLS_STORE ){
		$store = ria_mysql_fetch_array( dlv_stores_get( $obj[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ) );

		if( $lng!=$config['i18n_lng'] ){
			$val = fld_object_values_get( $store['id'], _FLD_STR_NAME, $lng);
			$store['name'] = trim($val)!='' ? $val : $store['name'];
		}

		$sector = is_numeric($store['sct_id']) && dlv_sectors_exists($store['sct_id']) ? $store['sct_id'] : 0;
	}

	foreach( $split as $s ){
		if( trim($s)=='' ){
			continue;
		}

		// Composant statique d'une url
		if( trim($s)!='' && strpos($s, '%')===false ){
			$url .= '/'.urlalias( $s, $lng );
		} else {
			// Champs avancés dans une url
			$s = str_replace('%', '', $s);

			// Information champ avancé
			switch( $s ){
				case _FLD_CAT_NAME : {

					// Récupère les catégories publiées d'un produit
					$rcat = prd_categories_get( isset($obj[1]) && $obj[1]>0 ? $obj[1] : $obj[0] );
					if( !$rcat || !ria_mysql_num_rows($rcat) ){
						break;
					}

					while( $cat = ria_mysql_fetch_array($rcat) ){
						if( $lng!=$config['i18n_lng'] ){
							$val = fld_translates_get( $class, $cat['id'], $lng, $cat, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title'), true );
							$cat['name'] = trim($val['title'])!='' ? $val['title'] : $val['name'];
						}

						$parents = prd_categories_parents_get($cat['id']);
						if( !$parents ){
							break;
						}
						if( ria_mysql_num_rows($parents)>0 ){
							while( $parent = ria_mysql_fetch_array($parents) ){
								if( $lng!=$config['i18n_lng'] ){
									$val = fld_translates_get( $class, $parent['id'], $lng, $parent, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title'), true );
									$parent['name'] = trim($val['title'])!='' ? $val['title'] : $val['name'];
								}

								$url.= '/'.urlalias( $parent['name'], $lng );
							}
						}
						$url.= '/'.urlalias( $cat['name'], $lng );
					}
					break;
				}
				case _FLD_PRD_NAME : {
					$name = prd_products_get_name($obj[0]);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $obj[0], _FLD_PRD_NAME, $lng, false, true );
						$name = trim($val)!='' ? $val : $name;
					}

					$url .= '/'.urlalias( $name, $lng );
					break;
				}
				case _FLD_PRD_TITLE : {
					$title = prd_products_get_name( $obj[0], true );

					if( $lng != $config['i18n_lng'] ){
						$val = fld_object_values_get( $obj[0], _FLD_PRD_TITLE, $lng, false, true );

						if( trim($val) == '' ){
							$val = fld_object_values_get( $obj[0], _FLD_PRD_NAME, $lng, false, true );
						}

						$title = trim($val)!='' ? $val : $title;
					}

					$url .= '/'.urlalias( $title, $lng );
					break;
				}
				case _FLD_SCT_NAME : {

					// Récupère le nom d'un secteur
					if( $sector || (isset($obj[1]) && $obj[1]>0 && dlv_sectores_exists($obj[1])) ){
						$r_sct = dlv_sectors_get( $sector ? $sector : $obj[1] );
						if( !$r_sct || !ria_mysql_num_rows($r_sct) ){
							break;
						}

						$name = ria_mysql_result($r_sct, 0, 'name');

						if( $lng!=$config['i18n_lng'] ){
							$val = fld_object_values_get( ria_mysql_fetch_array($r_sct), _FLD_SCT_NAME, $lng, false, true );
							$name = trim($val)!='' ? $val : $name;
						}

						$url .= '/'.urlalias( $name, $lng );
					}

					break;
				}
				case _FLD_STR_ZIPCODE : {
					$dept = substr( $store['zipcode'], 0, 2 );
					$rzone = sys_zones_get( 0, $dept, '', false, 0, '', 2 );
					if( $rzone && ria_mysql_num_rows($rzone) ){
						$url .= '/'.urlalias( ria_mysql_result($rzone, 0, 'name'), $lng );
					}
					break;
				}
				case _FLD_STR_COUNTRY : {
					$url .= '/'.urlalias( $store['country'], $lng );
					break;
				}
				case _FLD_STR_CITY : {

					if( $store['city']!='' ){
						$url .= '/'.urlalias( $store['city'], $lng );
						$city = true;
					}

					break;
				}
				case _FLD_STR_NAME : {

					$url .= '/'.urlalias( $store['name'] );
					break;
				}
				case _FLD_BRD_NAME : {

					$brands = prd_brands_get( $obj[0] );
					if( !$brands || !ria_mysql_num_rows($brands) ){
						break;
					}
					$brand = ria_mysql_fetch_array($brands);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $brand['id'], _FLD_BRD_NAME, $lng, false, true );
						$brand['name'] = trim($val)!='' ? $val : $brand['name'];
					}

					$url .= '/'.urlalias( $brand['name'], $lng );
					break;
				}
				case _FLD_CMS_PARENT_ID : {

					if( isset($obj[1]) && $obj[1]>0 && $class==CLS_CMS ){
						$parents = cms_hierarchy_get($obj[1]);
						if(count($parents)) {
							foreach($parents as $key => $parent) {
								if( $parent['url_masked'] ) continue;

								if( $lng!=$config['i18n_lng'] ){
									$val = fld_object_values_get( $parent['id'], _FLD_CMS_NAME, $lng, false, true );
									$parent['name'] = trim($val)!='' ? $val : $parent['name'];
								}
								$url .= '/'.urlalias( $parent['name'], $lng );
							}
						}
					}
					break;
				}
				case _FLD_CMS_NAME : {

					$rcms = cms_categories_get($obj[0], $wst>0 ? $wst : false, false, -1, false, false, true, null, false, null, false);
					if( !$rcms || !ria_mysql_num_rows($rcms) ){
						break;
					}
					$cms = ria_mysql_fetch_array($rcms);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $cms['id'], _FLD_CMS_NAME, $lng, false, true );
						$cms['name'] = trim($val)!='' ? $val : $cms['name'];
					}

					$url .= '/'.urlalias( $cms['name'], $lng );
					break;
				}
				case _FLD_DOC_TYPE_NAME : {

					$type_name = doc_types_get_name( $obj[0] );
					if( $type_name === false || trim($type_name) == '' ){
						break;
					}

					if( $lng != $config['i18n_lng'] ){
						$val = fld_object_values_get( $obj[0], _FLD_DOC_TYPE_NAME, $lng, false, true );
						$type_name = trim($val) != '' ? $val : $type_name;
					}

					// concaténation des parents (attention à l'inversion de l'ordre)
					$types_parent = doc_types_get_parents_array( $obj[0] );
					$types_parent = array_reverse($types_parent);
					foreach( $types_parent as $type_parent_id ){
						$type_parent_name = doc_types_get_name( $type_parent_id );
						if( $type_parent_name !== false && trim($type_parent_name) != '' ){
							if( $lng != $config['i18n_lng'] ){
								$val = fld_object_values_get( $type_parent_id, _FLD_DOC_TYPE_NAME, $lng, false, true );
								$type_parent_name = trim($val) != '' ? $val : $type_parent_name;
							}
							$url .= '/'.urlalias($type_parent_name, $lng);
						}
					}

					$url .= '/'.urlalias($type_name, $lng);

					break;
				}
				case _FLD_FAQ_CAT_NAME : {

					$rcat = faq_categories_get($obj[0]);
					if( !$rcat || !ria_mysql_num_rows($rcat) ){
						break;
					}
					$cat = ria_mysql_fetch_array($rcat);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $cat['id'], _FLD_FAQ_CAT_NAME, $lng, false, true );
						$cat['name'] = trim($val)!='' ? $val : $cat['name'];
					}

					$url .= '/'.urlalias( $cat['name'], $lng );
					break;
				}
				case _FLD_FAQ_QST_NAME : {

					$rqst = faq_questions_get($obj[0]);
					if( !$rqst || !ria_mysql_num_rows($rqst) ){
						break;
					}
					$qst = ria_mysql_fetch_array($rqst);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $qst['id'], _FLD_FAQ_QST_NAME, $lng, false, true );
						$qst['name'] = trim($val)!='' ? $val : $qst['name'];
					}

					$url .= '/'.urlalias( $qst['name'], $lng );
					break;
				}
				case _FLD_NEWS_NAME : {

					$rnews = news_get( $obj[0], false, null, 0, 0, false, false );
					if( !$rnews || !ria_mysql_num_rows($rnews) ){
						break;
					}
					$news = ria_mysql_fetch_array($rnews);

					if( $lng!=$config['i18n_lng'] ){
						$val = fld_object_values_get( $news['id'], _FLD_NEWS_NAME, $lng, false, true );
						$news['name'] = trim($val)!='' ? $val : $news['name'];
					}

					$url .= '/'.urlalias( $news['name'], $lng );
					break;
				}
				case _FLD_NEWS_CAT_NAME : {

					$rcat = news_categories_get($obj[0]);
					if( !$rcat || !ria_mysql_num_rows($rcat) ){
						break;
					}
					$cat = ria_mysql_fetch_array($rcat);

					// concatène chaque catégorie d'actu parent
					if( $ar_cat_parent = news_categories_get_parents( $cat['id'] ) ){
						foreach( $ar_cat_parent as $cat_parent_id => $cat_depth ){
							$catp = ria_mysql_fetch_assoc(news_categories_get( $cat_parent_id ));

							if( $lng != $config['i18n_lng'] ){
								$val = fld_object_values_get( $catp['id'], _FLD_NEWS_CAT_NAME, $lng, false, true );
								$catp['name'] = trim($val) != '' ? $val : $catp['name'];
							}

							$url .= '/'.urlalias( $catp['name'], $lng );
						}
					}

					if( $lng != $config['i18n_lng'] ){
						$val = fld_object_values_get( $cat['id'], _FLD_NEWS_CAT_NAME, $lng);
						$cat['name'] = trim($val) != '' ? $val : $cat['name'];
					}

					$url .= '/'.urlalias( $cat['name'], $lng );
					break;
				}
				case 'NAMEUSER' : {
					$user_id = 0;
					if( $class==CLS_WISHLISTS ){
						$user_id = gu_wishlists_get_user_id( $obj[0] );
					}

					if( $user_id>0 ){
						$ruser = gu_users_get( $user_id );
						if( $ruser && ria_mysql_num_rows($ruser) ){
							$user = ria_mysql_fetch_array( $ruser );

							$tmp = trim( $user['adr_firstname'].' '.$user['society'] );
							if( trim($tmp)!='' ){
								$url .='/'.urlalias( $tmp );
							}
						}
					}
					break;
				}
				case _FLD_WISHLIST_NAME : {
					$rwishlist = gu_wishlists_get( $obj[0] );
					if( $rwishlist && ria_mysql_num_rows($rwishlist) ){
						$wishlist = ria_mysql_fetch_array( $rwishlist );
						$url .= '/'.urlalias( $wishlist['name'] );
					}
					break;
				}
				case _FLD_PLS_NAME : {
					$rpls = doc_playlists_get( 0, 0, $obj[0] );
					if( $rpls && ria_mysql_num_rows($rpls) ){
						$pls = ria_mysql_fetch_assoc( $rpls );
						$url .= '/'.urlalias( $pls['name'] );
					}
					break;
				}
				case _FLD_MEDIA_NAME : {
					$rmedia = doc_medias_get( 0, 0, 0, $obj[0] );
					if( $rmedia && ria_mysql_num_rows($rmedia) ){
						$media = ria_mysql_fetch_assoc( $rmedia );
						$url .= '/'.urlalias( $media['name'] );
					}
					break;
				}
			}
		}
	}

	return $url;
}
// \endcond

/**	Réécrit une url simplifiée pour trouver sa correspondance avec l'url interne à l'application.
 *	Pour travailler, cette fonction utilise un index interne, contenu dans la table rew_rewritemap.
 *	Pour que la résolution puisse se faire, l'url doit au préalable avoir été déclarée à l'aide de la fonction rew_rewritemap_add.
 *
 *	@param string $url Obligatoire, url externe à résoudre
 *	@param bool|string $lng Facultatif, code langue, la langue par défaut du site est utilisé
 *	@param bool $use_memcached Facultatif, par défaut memcached est utilisé, mettre False pour ne pas l'utilisé
 *
 *	@return string L'URL interne correspond à l'url externe passée en paramètre. Si l'url externe n'a pas pu être résolue, elle est retournée.
 */
function rew_rewrite( $url, $lng=false, $use_memcached=true ){
	global $config;
	global $memcached;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];
	$lng = strtolower( $lng );

	$old_url = $url;

	// activation du cache seulement pour les urls d'une longue maximale de 150 caractères
	$time_memcached  = 21600; // secondes (6h)
	$active_memached = $use_memcached && strlen($url) <= 150;

	// utilisation d'un cache s'il existe
	if( $active_memached ){
		$key_memcached = 'rew_rewrite:'.$config['tnt_id'].':'.$config['wst_id'].':'.$lng.':'.$url;
		$res = $memcached->get($key_memcached);
		if( $res && !$config['env_sandbox'] ){
			if( is_array($res) && sizeof($res) && isset($res['code'], $res['url']) ){

				if( $res['code']==301 ){
					if( $config['tnt_id'] == 4 && $config['wst_id'] == 30 ){
						if( defined('APP_MODE') && APP_MODE == 'BORNES' ){
							$res['url'] = str_replace( $config['site_url'], 'https://bornes2021.proloisirs.fr', $res['url'] );
						}else{
							$res['url'] = str_replace( 'https://bornes2021.proloisirs.fr', $config['site_url'], $res['url'] );
						}
					}

					header('Location: '.$res['url']); // Adresse de la nouvelle page
					header('HTTP/1.1 301 Moved Permanently'); // Code HTTP de redirection permanente
					header('Status: 301 Moved Permanently'); // Doublon utile à certaines versions de PHP et serveurs
					exit;
				}

				rew_rewrite_set_content_type_if_is_xml($res['url']);

				return $res['url'];
			}
		}
	}

	// Prend en charge la pagination
	if( preg_match( '/\/-page([0-9]+)/', $url, $matches ) ){
		$page = $matches[1];
		$url = preg_replace( '/\/-page[0-9]+/', '', $url );
	}
	// Prend en charge l'éventuelle querystring
	if( preg_match( '/\?(.+)/', $url, $matches ) ){
		$params = $matches[1];
		$url = preg_replace( '/\?(.+)/', '', $url );
	}
	// Résout l'url
	$r = ria_mysql_query('
		select url_intern, url_code as code, url_cls_id as cls_id, url_obj_id_1 as obj_id_1
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and ( url_wst_id=0 or url_wst_id='.$config['wst_id'].' )
			and url_lng_code=\''.$lng.'\'
			and url_extern=\''.addslashes($url).'\'
		order by url_wst_id desc, url_id desc
	');

	if( !ria_mysql_num_rows($r) ){
		$r = ria_mysql_query('
			select url_intern, url_code as code, url_cls_id as cls_id, url_obj_id_1 as obj_id_1
			from rew_rewritemap
			where url_tnt_id='.$config['tnt_id'].'
				and ( url_wst_id=0 or url_wst_id='.$config['wst_id'].' )
				and url_lng_code=\''.$lng.'\'
				and url_extern=\''.addslashes($old_url).'\'
			order by url_wst_id desc, url_id desc
		');
		$is_old = true;
	}

	if( ria_mysql_num_rows($r) ){
		$rew = ria_mysql_fetch_assoc($r);
		$url = $rew['url_intern'];
		$code = $rew['code'];

		if( isset($params, $is_old) && $is_old ){
			unset($params);
		}
	}

	// Prend en charge la pagination (suite)
	$p_url = '';
	if( isset($page) ){
		if( strstr($url,'?') ){
			$p_url .= '&page='.$page;
		}else{
			$p_url .= '?page='.$page;
		}
	}
	// Prend en charge l'éventuelle suite
	$pr_url = '';
	if( isset($params) ){
		if( strstr($url,'?') ){
			$pr_url .= '&'.$params;
		}else{
			$pr_url .= '?'.$params;
		}
	}

	$url_rewrite = $url.$p_url.$pr_url;
	if( ria_mysql_num_rows($r) && isset($rew) ){

		if( $code==301 ){
			if( $lng != $config['i18n_lng'] ){
				$temp = wst_websites_languages_get_url( $config['wst_id'], $lng );
				if( trim($temp) != '' ){
					$site_url = $temp.rew_strip($url);
				}else{
					$site_url = 'http://'.$_SERVER['HTTP_HOST'].rew_strip($url);
				}
			}else{
				$site_url = $config['site_url'].rew_strip($url);
			}

			if( isset($config['site_url_override'] ) && trim($config['site_url_override']) ){
				$site_url = $config['site_url_override'].rew_strip($url);
			}

			if( $rew['cls_id'] == CLS_PRODUCT && is_numeric($rew['obj_id_1']) && $rew['obj_id_1'] > 0 ){
				$prd_id = $rew['obj_id_1'];

				if( ($url_unreachable = prd_products_get_url_unreachable($prd_id)) !== $old_url ){
					if (trim($url_unreachable) != '') {
						$site_url = $url_unreachable;
					}
				}
			}

			if( $active_memached ){
				$memcached->set( $key_memcached, array('code'=>'301', 'url'=>$site_url.$p_url.$pr_url), $time_memcached );
			}

			if( $config['tnt_id'] == 4 && $config['wst_id'] == 30 ){
				if( defined('APP_MODE') && APP_MODE == 'BORNES' ){
					$site_url = str_replace( $config['site_url'], 'https://bornes2021.proloisirs.fr', $site_url );
				}
			}

			header('Location: '.$site_url.$p_url.$pr_url); // Adresse de la nouvelle page
			header('HTTP/1.1 301 Moved Permanently'); // Code HTTP de redirection permanente
			header('Status: 301 Moved Permanently'); // Doublon utile à certaines versions de PHP et serveurs
			exit;
		}

		if( $active_memached ){
			$memcached->set( $key_memcached, array('code'=>'200', 'url'=>$url_rewrite), $time_memcached );
		}

	} elseif( $lng!=$config['i18n_lng'] ) {
		// Si l'url pour le site étranger n'existe pas, on essaye de charger l'url avec la langue par défaut
		$url_rewrite = rew_rewrite($url, $config['i18n_lng'], $use_memcached);
	}

	rew_rewrite_set_content_type_if_is_xml($url_rewrite);

	return $url_rewrite;
}

/**	Cette fonction est chargée de retirer d'une url rewritée une section complète. Elle permet notamment de gérer plusieurs sites publics sous des sous-domaines différents.
 *	@param string $url Url à réécrire
 *	@return string L'URL réécrite, débarrassée de la portion définie dans $config['rew_strip']
 */
function rew_strip( $url ){
	global $config;

	if( isset($config['rew_strip']) && $config['rew_strip'] ){
		$url = preg_replace( '/'.str_replace('/','\/',$config['rew_strip']).'/', '', $url );
	}
	return $url;
}

// \cond onlyria
/**
 * Permet donner le type de contenu application/xml si c'est un sitemap
 *
 * @param string $file Chemin vers le sitemap
 * @return void
 */
function rew_rewrite_set_content_type_if_is_xml($file)
{
	$matches = array();
	preg_match("/^\/sitemaps\/.*\.xml$/", $file, $matches);
	if (!empty($matches)){
		header('Content-Type: application/xml');
	}
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un alias vers une url interne
 *	@param string $extern Url externe utilisée par les internautes
 *	@param string $intern Url interne à l'application correspond à l'url externe
 *	@param int $code Facultatif, code de redirection
 *	@param int $wst Facultatif, identifiant d'un site web
 *	@param bool|string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site
 *	@param int $cls_id Facultatif, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *	@return string L'URL virtuelle finalement attribuée à l'objet
 *
 *	@deprecated C'est fonction est destiné à disparaître, utilisez plutôt rew_rewritemap_add_specify_class(). Ne pas supprimer tout de suite,
 *		il existe encore 5 appels à cette fonction.
 *
 */
function rew_rewritemap_add( $extern, $intern, $code=200, $wst=0, $lng=false, $cls_id=0, $obj=0 ){
	global $config;

	$wst = $wst>0 && !wst_websites_exists($wst) ? $config['wst_id'] : $wst ;
	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];
	$lng = strtolower( $lng );

	if( strlen($extern)>255 ){
		return false;
	}

	if( !is_numeric($code) ){
		return false;
	}

	if( $extern==$intern ){
		return false;
	}

	if( $code==301 && $extern=='/' ){
		return false;
	}

	if( !is_numeric($cls_id) || $cls_id<0 ){
		return false;
	}

	if( is_array($obj) ){
		if( sizeof($obj)<0 || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}

		$obj = $obj>0 ? array( $obj ) : array();
	}

	$obj_cols = array();
	$obj_vals = array();
	if( $cls_id>0 ){
		$obj_cols[] = 'url_cls_id';
		$obj_vals[] = $cls_id;
	}

	if( sizeof($obj) ){
		$i=0;
		while( $i<sizeof($obj) ){
			$obj_cols[] = 'url_obj_id_'.$i;
			$obj_vals[] = $obj[$i];
			$i++;
		}
	}

	// Vérifie que cette url n'est pas déjà utilisée
	$sql = '
		select url_intern
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and (
				url_extern = "'.addslashes( $extern ).'"
				or url_extern = "'.addslashes( rew_strip($extern) ).'"
			)
			and url_lng_code=\''.$lng.'\'
	';

	if( isset($config['url_no_category']) && $config['url_no_category'] ){
		if( $cls_id == CLS_PRODUCT ){
			$sql .= ' and url_obj_id_1 != '.$obj_vals[2];
		}
	}

	$r = ria_mysql_query( $sql );

	if( ria_mysql_num_rows($r)==0 ){
		// Enregistre la nouvelle url virtuelle
		ria_mysql_query('
			insert into rew_rewritemap
				(url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).')
			values
				('.$config['tnt_id'].', '.$wst.', \''.$lng.'\', \''.addslashes( $extern ).'\', \''.addslashes( $intern ).'\', '.$code.', 1'.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).')
		');
	}else{
		if( ria_mysql_result($r,0,0)==$intern )
			return $extern; // L'url était déjà enregistrée.
		else{
			$c = 1;
			while( true ){
				$sql = '
					select url_intern
					from rew_rewritemap
					where url_tnt_id='.$config['tnt_id'].'
						and url_wst_id='.$wst.'
						and (
							url_extern = "'.addslashes( "$extern-$c" ).'"
							or url_extern = "'.addslashes( rew_strip("$extern-$c") ).'"
						)
						and url_lng_code=\''.$lng.'\'
				';

				if( isset($config['url_no_category']) && $config['url_no_category'] ){
					if( $cls_id == CLS_PRODUCT ){
						$sql .= ' and url_obj_id_1 != '.$obj_vals[2];
					}
				}

				$r = ria_mysql_query( $sql );

				if( ria_mysql_num_rows($r)==0 ){
					// Enregistre la nouvelle url virtuelle
					$extern = "$extern-$c";
					ria_mysql_query('
						insert into rew_rewritemap
							(url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).')
						values
							('.$config['tnt_id'].', '.$wst.', \''.$lng.'\', \''.addslashes($extern).'\', \''.addslashes($intern).'\', '.$code.', 1'.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).')
					');
					break;
				}elseif( ria_mysql_result($r,0,0)==$intern ){
					// L'url était déjà enregistrée
					$extern = "$extern-$c";
					break;
				}else{
					$c++; // Incrémente le compteur
				}
			}
		}
	}

	return $extern; // Retourne l'url virtuelle finalement attribuée à l'objet
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour une url
 * 	@param int $id Obligatoire, identifiant d'une URL
 * 	@param int $code Optionnel, code de requête (soit 200, soit 301)
 * 	@param string $intern Optionnel, Url intern
 * 	@return bool true en cas de succès, false dans le cas contraire
 */
function rew_rewritemap_update( $id, $code=null, $intern=null ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( $code !== null ){
		if( !in_array($code, [200, 301]) ){
			return false;
		}
	}

	if( $intern !== null ){
		if( trim($intern) == '' ){
			return false;
		}
	}

	// Aucune action si $code et $intern ne sont pas fournis
	if( $code === null && $intern === null ){
		return true;
	}
	// On récupère les informations de l'url actuelle pour les stocker/sélectionner la potentielle url en doublon (la méthode du JOIN est trop lente)
	$sqlSelectURLToUpdate = '
				SELECT url_tnt_id as tnt_id,
					url_wst_id as wst_id,
					url_lng_code as lng_code,
					url_extern as extern,';
					if ($intern !== null) {
						$sqlSelectURLToUpdate .= '
									"'.addslashes( $intern ).'" as intern,';
					}
					else {
						$sqlSelectURLToUpdate .= '
									url_intern as intern,';
					}
					if ($code !== null) {
						$sqlSelectURLToUpdate .= '
									\''.$code.'\' as code,';
					}
					else {
						$sqlSelectURLToUpdate .= '
									url_code as code,';
					}
	$sqlSelectURLToUpdate .= '
					url_public as "public",
					url_cls_id as cls_id,
					url_obj_id_0 as obj_id_0,
					url_obj_id_1 as obj_id_1,
					url_obj_id_2 as obj_id_2
				FROM rew_rewritemap
				WHERE url_id = '.$id.'
				AND url_tnt_id = '.$config['tnt_id'].'
				';

	$rurlToUpdate = ria_mysql_query($sqlSelectURLToUpdate);
	$urlToUpdateAsArray = ria_mysql_fetch_array($rurlToUpdate);
	// On supprime les potentiels doublons qui existeraient après le update (à priori vu la clé de la table il ne devrait y en avoir que 1 max à chaque fois)
	if (isset($urlToUpdateAsArray) && $urlToUpdateAsArray)
	{
		$sqlDeleteDuplicate = '
		DELETE FROM rew_rewritemap
		WHERE url_tnt_id = '.$config['tnt_id'].'
		AND url_wst_id = '.$urlToUpdateAsArray['wst_id'].'
		AND url_lng_code = "'.addslashes($urlToUpdateAsArray['lng_code']).'"
		AND url_extern = "'.addslashes($urlToUpdateAsArray['extern']).'"
		AND url_intern = "'.addslashes($urlToUpdateAsArray['intern']).'"
		AND url_code = '.$urlToUpdateAsArray['code'].'
		AND url_public = '.$urlToUpdateAsArray['public'].'
		AND url_cls_id = '.$urlToUpdateAsArray['cls_id'].'
		AND url_obj_id_0 = "'.addslashes($urlToUpdateAsArray['obj_id_0']).'"
		AND url_obj_id_1 = "'.addslashes($urlToUpdateAsArray['obj_id_1']).'"
		AND url_obj_id_2 = "'.addslashes($urlToUpdateAsArray['obj_id_2']).'"
		AND url_id != '.$id.'
		';
		ria_mysql_query($sqlDeleteDuplicate);
	}

	$sql = '
		update rew_rewritemap
		set url_extern = url_extern
	';

	if( $code !== null ){
		$sql .= ', url_code = '.$code;
	}

	if( $intern !== null ){
		$sql .= ', url_intern = "'.addslashes( $intern ).'"';
	}

	$sql .= '
		where url_tnt_id = '.$config['tnt_id'].'
			and url_id = '.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un alias vers une url interne
 *	@param int $cls Classe de l'url. L'url ne sera pas ajoutée si la classe n'utilise pas les urls.
 *	@param string $extern Url externe utilisée par les internautes
 *	@param string $intern Url interne à l'application correspond à l'url externe
 *	@param int $code Facultatif, code de redirection
 *	@param int $wst Facultatif, identifiant d'un site web
 *	@param bool|string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site
 *	@param null|bool $public Facultatif, Indique si l'url doit petre publique ou non. null utilisera la configuration du site.
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *	@return string L'URL virtuelle finalement attribuée à l'objet
 */
function rew_rewritemap_add_specify_class( $cls, $extern, $intern, $code=200, $wst=0, $lng=false, $public=null, $obj=0 ){
	global $config;

	if( $extern==$intern ){
		return false;
	}

	$rurl = cfg_urls_is_used( $wst, $cls );
	if( !$rurl ){
		return true;
	}

	if( $public===null ){
		$public = 1;
		if( $wst>0 ){
			$public = wst_websites_url_is_published( $wst );
		}
	}else{
		$public = $public ? 1 : 0;
	}

	$wst = $wst>0 && !wst_websites_exists($wst) ? $config['wst_id'] : $wst ;
	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];
	$lng = strtolower( $lng );

	if( strlen($extern)>255 ){
		$extern = substr($extern, 0, 253);
	}

	if( !is_numeric($code) ){
		return false;
	}

	if( $code==301 && $extern=='/' ){
		return false;
	}

	if( is_array($obj) ){
		if( sizeof($obj)<0 || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}

		$obj = $obj>0 ? array( $obj ) : array();
	}

	$obj_cols = array();
	$obj_vals = array();
	if( $cls>0 ){
		$obj_cols[] = 'url_cls_id';
		$obj_vals[] = $cls;
	}

	if( sizeof($obj) ){
		$i=0;
		while( $i<sizeof($obj) ){
			$obj_cols[] = 'url_obj_id_'.$i;
			$obj_vals[] = $obj[$i];
			$i++;
		}
	}

	// Vérifie que cette url n'est pas déjà utilisée
	$sql = '
		select url_intern
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and (
				url_extern = "'.addslashes( $extern ).'"
				or url_extern = "'.addslashes( rew_strip($extern) ).'"
			)
			and url_lng_code=\''.$lng.'\'
	';

	if( isset($config['url_no_category']) && $config['url_no_category'] ){
		if( $cls == CLS_PRODUCT ){
			$sql .= ' and url_obj_id_1 != '.$obj_vals[2];
		}
	}

	$r = ria_mysql_query( $sql );

	if( ria_mysql_num_rows($r)==0 ){
		$r_exists = rew_rewritemap_exists( $extern, $intern, $lng, $wst, $code, 1, ($cls > 0 ? $cls : null), (count($obj) ? $obj : null) );
		if( !$r_exists ){
			// Enregistre la nouvelle url virtuelle
			ria_mysql_query('
				insert into rew_rewritemap
					(url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).')
				values
					('.$config['tnt_id'].', '.$wst.', \''.$lng.'\', \''.addslashes( $extern ).'\', \''.addslashes( $intern ).'\', '.$code.', '.$public.''.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).')
			');
		}
	}else{
		if( ria_mysql_result($r,0,0)==$intern ){
			return $extern; // L'url était déjà enregistrée.
		}else{
			$c = 1;
			while( true ){
				$sql = '
					select url_intern
					from rew_rewritemap
					where url_tnt_id='.$config['tnt_id'].'
						and url_wst_id='.$wst.'
						and (
							url_extern = "'.addslashes( "$extern-$c" ).'"
							or url_extern = "'.addslashes( rew_strip("$extern-$c") ).'"
						)
						and url_lng_code=\''.$lng.'\'
				';

				if( isset($config['url_no_category']) && $config['url_no_category'] ){
					if( $cls == CLS_PRODUCT ){
						$sql .= ' and url_obj_id_1 != '.$obj_vals[2];
					}
				}

				$r = ria_mysql_query( $sql );

				if( ria_mysql_num_rows($r)==0 ){
					// Enregistre la nouvelle url virtuelle
					$extern = "$extern-$c";

					$r_exists = rew_rewritemap_exists( $extern, $intern, $lng, $wst, $code, 1, ($cls > 0 ? $cls : null), (count($obj) ? $obj : null) );
					if( !$r_exists ){
						ria_mysql_query('
							insert into rew_rewritemap
								(url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).')
							values
								('.$config['tnt_id'].', '.$wst.', \''.$lng.'\', \''.addslashes($extern).'\', \''.addslashes($intern).'\', '.$code.', '.$public.''.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).')
						');
					}
					break;
				}elseif( ria_mysql_result($r,0,0)==$intern ){
					// L'url était déjà enregistrée
					$extern = "$extern-$c";
					break;
				}else{
					$c++; // Incrémente le compteur
				}
			}
		}
	}

	return $extern; // Retourne l'url virtuelle finalement attribuée à l'objet
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une url multilingue.
 *	@param array $ids Obligatoire, tableau d'identifiants des objets dans l'ordre de fld_object_values
 *	@param int $class Obligatoire, identifiant de la classe de l'objet
 *	@param string $lng Obligatoire, code ISO 639-1 de la langue
 *	@param string $url_df Obligatoire, url utilisée pour la langue par défaut
 *	@param int $fld Obligatoire, identifiant du champs avancé qui correspond à l'url du contenu
 *	@return bool Retourne true si l'ajout de l'url s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function rew_rewritemap_add_multilingue( $ids, $class, $lng, $url_df, $fld ){
	if( !is_array($ids) || sizeof($ids)<=0 ) return false;
	if( !i18n_languages_exists($lng) ) return false;
	global $config;

	if( ($class==CLS_CMS || $class==CLS_CATEGORY) && substr($url_df, -1)!='/' ){
		$url_df .= '/';
	}

	if( $class!=CLS_CMS || ($class==CLS_CMS && cms_categories_get_parent_id($ids[0])>0) ){
		$rintern = rew_rewritemap_get( '', $url_df, 200, $config['i18n_lng'] );
		if( !$rintern || !ria_mysql_num_rows($rintern ) ){
			return false;
		}
		$intern = ria_mysql_result( $rintern, 0, 'intern' );
	} else {
		return true;
	}

	// Ajoute l'url traduite sur le contenu
	$end = $class==CLS_CATEGORY || $class==CLS_CMS ? '/' : '';

	// Génére l'url du contenu traduit dans une langue
	if( $class==CLS_PRODUCT ){
		$alias = rew_rewritemap_generated( array($ids[1], $ids[0]), $class, $config['wst_id'], $lng );
	}else{
		$alias = rew_rewritemap_generated( $ids, $class, $config['wst_id'], $lng );
	}

	$old_url = fld_object_values_get( $ids, $fld, $lng );

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	// Crée les alias
	while( $wst = ria_mysql_fetch_array($rwst) ){
		if( trim($old_url)!='' ){ // Met à jour l'url traduite
			foreach (array('301', '200') as $code) {
				$where_exists = '
						url_tnt_id='.$config['tnt_id'].'
					and url_wst_id='.$wst['id'].'
					and '.( $code == '301' ? 'url_intern' : 'url_extern' ).'="'.addslashes($old_url).'"
					and url_code='.$code.'
					and url_lng_code="'.strtolower($lng).'"
				';

				for ($i=0; $i < count($ids); $i++) {
					$where_exists .= '
						and url_obj_id_'.$i.' = '.$ids[$i].'
					';
				}

				$r_exists = ria_mysql_query('select 1 from rew_rewritemap where '.$where_exists);
				if ($r_exists && ria_mysql_num_rows($r_exists)) {
					ria_mysql_query('delete from rew_rewritemap where '.$where_exists);
				}
			}

			fld_object_values_set( $ids, $fld, '', $lng );
		}

		if( $class!=CLS_CMS ){
			$prd_pages = cfg_urls_get( $wst['id'], $class);
			if( $prd_pages ){
				while( $page = ria_mysql_fetch_array($prd_pages) ){
					// Lien entre url traduite et url interne (redirect° 200)
					$url = rew_rewritemap_add_specify_class( $class, $alias.$end, $intern, 200, $wst['id'], $lng, null, $ids );

					// Lien entre url par défaut et url traduite (redirect° 301)
					rew_rewritemap_add_specify_class( $class, $url_df, $url, 301, $wst['id'], $lng, null, $ids );

					// Lien entre url traduite et url traduite.'/' (redirect° 301)
					if( trim($end)!='' ){
						rew_rewritemap_add_specify_class( $class, $alias, $url, 301, $wst['id'], $lng, null, $ids );
					}
				}
			}
		} else {
			// Lien entre url traduite et url interne (redirect° 200)
			$url = rew_rewritemap_add_specify_class( $class, $alias.$end, $intern, 200, $config['wst_id'], $lng, null, $ids );

			// Lien entre url par défaut et url traduite (redirect° 301)
			rew_rewritemap_add_specify_class( $class, $url_df, $url, 301, $config['wst_id'], $lng, null, $ids );
			// Lien entre url traduite et url traduite.'/' (redirect° 301)
			if( trim($end)!='' ){
				rew_rewritemap_add_specify_class( $class, $url, $url, 301, $config['wst_id'], $lng, null, $ids );
			}
		}
	}

	return fld_object_values_set( $ids, $fld, $url, $lng );

}
// \endcond

// \cond onlyria
/** Permet de récupérer les informations de rewritemap selon les paramètres
 *	@param string $intern Facultatif, url interne
 *	@param string $extern Facultatif, url externe
 *	@param int $code Facultatif, code de redirection (200 ou 301)
 *	@param string|null $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site, mettre null pour récupérer toutes les langues
 *	@param int $wst Facultatif, identifiant d'un site web, par défaut aucun filtre sur le site est en place
 *	@param string $like Facultatif, permet de filtrer les urls retournées sur un mot clé. La recherche est très simple, de type like '%motcle%'
 *	@param string $like_type Facultatif, détermine si le paramètre $like s'applique à l'url interne "intern", externe "extern" ou les deux "both" (par défaut ce dernier)
 *	@param int $cls_id Facultatif, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *	@param int $url_id Optionnel, identifiant d'une URL
 *
 *	@return bool false si la requête de sélection échoue
 *	@return resource Un résultat de requête MySQL contenant les colonnes suivantes :
 *			- intern : url interne
 *			- extern : url externe
 *			- code : code de redirction employé
 *			- wst_id : identifiant du site
 *			- lng_code : code ISO de la langue
 *			- public : URL publique ou non (?)
 *			- cls_id : classe de l'objet
 *			- obj_id_0 : identifiant de l'objet
 *			- obj_id_1 : identifiant de l'objet
 *			- obj_id_2 : identifiant de l'objet
 *			- url_id : identifiant attribué à l'url
 */
function rew_rewritemap_get( $intern='', $extern='', $code=0, $lng=false, $wst=false, $like='', $like_type='both', $cls_id=0, $obj=0, $url_id=0 ){
	global $config;

	if( trim($intern)!='' && !rew_rewritemap_exists('', $intern, $lng) ){
		return false;
	}

	if( trim($extern)!='' && !rew_rewritemap_exists($extern, '', $lng) ){
		return false;
	}

	if( $code>0 && $code!=200 && $code!=301 ){
		return false;
	}

	if( $wst!=false && !wst_websites_exists($wst) ){
		return false;
	}

	if( !is_numeric($cls_id) || $cls_id<0 ){
		return false;
	}

	if( is_array($obj) ){
		if( sizeof($obj)<0 || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}

		$obj = $obj>0 ? array( $obj ) : array();
	}

	if( !is_numeric($url_id) || $url_id < 0 ){
		return false;
	}

	if( $lng!==null ){
		$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtoupper($lng) : $config['i18n_lng'];
	}

	$sql = '
		select url_intern as intern, url_extern as extern, url_code as code, url_wst_id as wst_id, url_lng_code as lng_code, url_public as "public",
			url_cls_id as cls_id, url_obj_id_0 as obj_id_0, url_obj_id_1 as obj_id_1, url_obj_id_2 as obj_id_2, url_id
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
	';

	if( $lng!==null ){
		$sql .= ' and url_lng_code=\''.strtolower($lng).'\'';
	}

	if( trim($intern)!='' ){
		$sql .= ' and url_intern = \''.addslashes($intern).'\'';
	}

	if( trim($extern)!='' ){
		$sql .= ' and url_extern = \''.addslashes($extern).'\'';
	}

	if( $code>0 ){
		$sql .= ' and url_code = '.$code;
	}

	if( $wst>0 ){
		$sql .= ' and url_wst_id='.$wst;
	}

	if( trim($like) ){
		$like = strtolower(addslashes($like));
		$like_type = strtolower(trim($like_type));

		if( $like_type == 'extern' ){
			$sql .= ' and (lower(url_extern) like "%'.$like.'%")';
		}elseif( $like_type == 'intern' ){
			$sql .= ' and (lower(url_intern) like "%'.$like.'%")';
		}else{
			$sql .= ' and (lower(url_extern) like "%'.$like.'%" or lower(url_intern) like "%'.$like.'%")';
		}
	}

	if( $cls_id>0 ){
		$sql .= ' and url_cls_id='.$cls_id;
	}

	if( sizeof($obj) ){
		$i = 0;
		while( $i<sizeof($obj) ){
			$sql .= ' and url_obj_id_'.$i.'='.$obj[$i];
			$i++;
		}
	}

	if( $url_id > 0 ){
		$sql .= ' and url_id = '.$url_id;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les urls rattachées à un contenu spécifique
 *	@param int $cls_id Facultatif, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *	@return resource Un résultat de requête MySQL contenant :
 *			- intern : url interne
 *			- extern : url externe
 *			- code : code de redirction employé
 *			- wst_id : identifiant du site
 *			- lng_code : code ISO de la langue
 *			- public : URL publique ou non (?)
 */
function rew_rewritemap_get_byclass( $cls_id=0, $obj=0 ){
	return rew_rewritemap_get( '', '', 0, false, false, '', 'both', $cls_id, $obj );
}
// \endcond

// \cond onlyria
/** Permet de tester l'existence d'une url interne ou externe
 *	Au moins l'un des paramètres est obligatoire.
 *
 *	@param string $url_extern Facultatif, url externe
 *	@param string $url_intern Facultatif, url interne
 *	@param string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site (mettre null pour ne pas tenir compte de la langue)
 *	@param int $wst Facultatif, identifiant du site web
 *	@param int $code Optionnel, code d'url 200 ou 301
 *	@param bool $public Optionnel, si oui ou non l'url est public
 *	@param int $cls_id Optionnel, identifiant d'une classe pour laquelle l'url est en place
 *	@param null|array $obj_ids Optionnel, clé de l'objet pour lequel l'url est en place
 *
 *	@return bool Retourne true si les url sont trouvées
 *	@return bool Retourne false dans le cas contraire
 */
function rew_rewritemap_exists( $url_extern='' , $url_intern='', $lng=false, $wst=false, $code=null, $public=null, $cls_id=null, $obj_ids=null ){
	if( trim($url_extern)=='' && trim($url_intern)=='' ){
		return false;
	}

	if( $code !== null ){
		if( !is_numeric($code) || $code <= 0 ){
			return false;
		}
	}

	if( $cls_id !== null ){
		if( !is_numeric($cls_id) || $cls_id <= 0 ){
			return false;
		}

		if( $obj_ids !== null ){
			$obj_ids = control_array_integer( $obj_ids, false, false, true );
			if( $obj_ids === false ){
				return false;
			}
		}
	}

	global $config;

	if ($lng !== null) {
		$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtoupper($lng) : $config['i18n_lng'];
	}

	$sql = '
		select 1
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
	';

	if ($lng !== null) {
		$sql .= ' and url_lng_code=\''.strtolower($lng).'\'';
	}

	if( trim($url_extern!='') ){
		$sql .= ' and url_extern = \''.addslashes($url_extern).'\'';
	}

	if( trim($url_intern!='') ){
		$sql .= ' and url_intern = \''.addslashes($url_intern).'\'';
	}

	if( $wst!=false ){
		$sql .= ' and url_wst_id='.$wst;
	}

	if( $code !== null ){
		$sql .= ' and url_code = '.$code;
	}

	if( $public !== null ){
		$sql .= ' and url_public = '.( $public ? '1' : '0' );
	}

	if( $cls_id !== null ){
		$sql .= ' and url_cls_id = '.$cls_id;

		if( $obj_ids !== null && count($obj_ids) ){
			$i = 0;
			while( $i < count($obj_ids) ){
				$sql .= ' and url_obj_id_'.$i.' = '.$obj_ids[$i];
				$i++;
			}
		}
	}

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}
// \endcond

// \cond onlyria
/** Permet de mettre à jour un alias vers une url interne.
 *	@param string $old Obligatoire, ancienne url
 *	@param string $new Obligatoire, nouvelle url
 *	@param string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site
 *	@return bool Retourne true si la mise à jours s'est correctement passée
 *	@return bool Retourne false dans le cas contraire
 */
function rew_rewritemap_extern_update( $old, $new, $lng=false ){
	if( trim($old)=='' || trim($new)=='' ){
		return false;
	}

	if( substr($new, 0, 1)!='/' ){
		$new = '/'.$new;
	}

	global $config;

	$lng = $lng!=false &&  in_array(strtolower($lng), $config['i18n_lng_used']) ? strtoupper($lng) : $config['i18n_lng'];

	return ria_mysql_query('
		update rew_rewritemap
		set url_extern=\''.addslashes($new).'\'
		where url_tnt_id='.$config['tnt_id'].'
			and url_extern=\''.addslashes($old).'\'
			and url_lng_code=\''.strtolower($lng).'\'
	');
}
// \endcond

// \cond onlyria
/** Permet de mettre à jour l'url intern d'une ou plusieurs redirection.
 *	@param string $old Obligatoire, url intern actuellement utilisée
 *	@param string $new Obligatoire, nouvelle url intern
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rew_rewritemap_set_redirection( $old, $new ){
	if( trim($old)=='' || trim($new)=='' ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update rew_rewritemap
		set url_intern = "'.addslashes( $new ).'"
		where url_tnt_id='.$config['tnt_id'].'
			and url_intern="'.addslashes( $old ).'"
			and url_code="301"
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un alias vers une url interne.
 * 	Un des trois paramètres suivants doit obligatoirement être passés : $extern, $intern ou $url_id
 *
 *	@param string $extern Facultatif, url externe à supprimer
 *	@param string $intern Facultatif, url interne à supprimer (obligatoire si $extern non spécifié)
 *	@param bool|string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site
 *	@param int $wst_id Facultatif, identifiant d'un site (par défaut tous les sites)
 *	@param int $url_id Optionnel, identifiant d'une url
 *	@param int $code Optionnel, code d'url (200 ou 301)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function rew_rewritemap_del( $extern='', $intern='', $lng=false, $wst_id=null, $url_id=0, $code=0 ){
	global $config;

	if( !is_numeric($url_id) || $url_id < 0 ){
		return false;
	}

	$lng = strtolower( $lng );
	if( !in_array($lng, $config['i18n_lng_used']) ){
		$lng = strtolower( $config['i18n_lng'] );
	}

	if( $wst_id !== null && ( !is_numeric($wst_id) || $wst_id <= 0 ) ){
		return false;
	}

	if( !is_numeric($code) || $code < 0 ){
		return false;
	}

	if( $code > 0 ){
		if( $code != 200 && $code != 301 ){
			return false;
		}
	}

	$sql = '
		delete from rew_rewritemap
		where url_tnt_id = '.$config['tnt_id'].'
			and url_lng_code = "'.addslashes( $lng ).'"
	';

	if( $wst_id !== null ){
		$sql .= ' and url_wst_id = '.$wst_id;
	}

	if( trim($extern) && $extern != '/' ){
		$sql .= ' and url_extern = "'.addslashes( $extern ).'"';
	}elseif( trim($intern) ){
		$sql .= ' and url_intern = "'.addslashes( $intern ).'"';
	}elseif( $url_id > 0 ){
		$sql .= ' and url_id = '.$url_id;
	}else{
		// ni interne, ni externe, ni id
		return false;
	}

	if( $code > 0 ){
		$sql .= ' and url_code = '.$code;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer les urls par un identifiant d'objet
 *	@param int $class Obligatoire, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant de l'objet (ou tableau d'identifiants pour les clés composées)
 *	@param int $wst_id Facultatif, identifiant du site web
 *	@return int Le nombre de lignes supprimées
 *	@return bool False dans le cas où la suppression n'a pas fonctionnée
 */
function rew_rewritemap_del_by_objects( $class, $obj=0, $wst_id=0 ){
	if( !is_numeric($class) || $class<=0 ){
	    return false;
	}

	if( $obj !== 0 ){
		$obj = control_array_integer( $obj, true, false, true );
		if( $obj === false ){
			return false;
		}
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from rew_rewritemap
		where url_tnt_id = '.$config['tnt_id'].'
			and url_cls_id = '.$class.'
	';

	if( is_array($obj) && sizeof($obj) > 0 ){
		$i = 0;
		while( $i<sizeof($obj) ){
			$sql .= ' and url_obj_id_'.$i.'='.$obj[$i];
			$i++;
		}
	}

	if( $wst_id > 0 ){
		$sql .= ' and url_wst_id = '.$wst_id;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_affected_rows();
}
// \endcond

// \cond onlyria
/** Permet la suppression des urls multilingue
 *	@param int $fld_url Obligatoire, identifiant du champ avancé contenant les urls multilingue
 *	@param array $objs Obligatoire, tableau contenant les identifiants de l'objet
 *	@return bool Retourne true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function rew_rewritemap_del_multilingue( $fld_url, $objs ){
	global $config;

	if(
		!fld_fields_exists($fld_url)
		|| !is_array($objs) || !sizeof($objs)
	){
		return false;
	}

	foreach( $config['i18n_lng_used'] as $lng ){
		$old_url = fld_object_values_get( $objs, $fld_url, $lng );
		if( trim($old_url)!='' ){
			$sql = '
				delete from rew_rewritemap
				where url_tnt_id='.$config['tnt_id'].'
					and (
						(url_extern = \''.addslashes($old_url).'\' and url_code = 200)
						or
						(url_intern = \''.addslashes($old_url).'\' and url_code = 301)
					)
					and url_lng_code = \''.strtolower($lng).'\'
			';

			if( isset($config['url_no_category']) && $config['url_no_category'] ){
				for ($i=0; $i < count($objs); $i++) {
					$sql .= '
						and (url_obj_id_'.$i.' is null or url_obj_id_'.$i.' = '.$objs[$i].')
					';
				}
			}

			if( !ria_mysql_query($sql) ){
				return false;
			}else{
				error_log('[rew_rewritemap_del_multilingue][SQL]'.$sql."\n", 3, '/var/log/php/debug.log');
			}

			fld_object_values_set( $objs, $fld_url, '', $lng );
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet de personnaliser une url simplifiée
 *	@param string $old Obligatoire, url externe actuellement utilisé
 *	@param string $new Obligatoire, nouvelle url externe
 *	@param string $intern Obligatoire, url interne vers quoi la nouvelle url doit pointer
 *	@param bool|string $lng Facultatif, code ISO d'une langue, par défaut on prend la langue par défaut du site
 *	@return bool Retourne false si l'url actuellement utilisé n'existe pas, si la nouvelle url est déjà utitlisée ou si la personnalisation a échouée
 *	@return bool Retourne true si la personnalisation a correctement fonctionnée
 *	@param int $cls_id Facultatif, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *
 *	@return bool|int True si l'enregistrement s'est correctement déroulée, False dans le cas contraire et -1 si l'url personnalisée est déjà utilisée
 */
function rew_rewritemap_customized( $old, $new, $intern, $lng=false, $cls_id=0, $obj=0 ){
	if( !rew_rewritemap_exists($old, '', $lng) ) return false;
	if( rew_rewritemap_exists($new, '', $lng) )return -1;
	global $config;

	if( $new==$intern ){
		return false;
	}

	if( substr($new, 0, 1)!='/' ){
		$new = '/'.$new;
	}

	if( !is_numeric($cls_id) || $cls_id<0 ){
		return false;
	}

	if( is_array($obj) ){
		if( sizeof($obj)<0 || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}

		$obj = $obj>0 ? array( $obj ) : array();
	}

	$obj_cols = array();
	$obj_vals = array();
	if( $cls_id>0 ){
		$obj_cols[] = 'url_cls_id';
		$obj_vals[] = $cls_id;
	}

	if( sizeof($obj) ){
		$i=0;
		while( $i<sizeof($obj) ){
			$obj_cols[] = 'url_obj_id_'.$i;
			$obj_vals[] = $obj[$i];
			$i++;
		}
	}

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtoupper($lng) : $config['i18n_lng'];
	$lng = strtolower( $lng );

	$sql = '
		insert into rew_rewritemap
			( url_tnt_id, url_lng_code, url_extern, url_intern, url_code'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).' )
		values
			( \''.$config['tnt_id'].'\', \''.$lng.'\', \''.$new.'\', \''.$intern.'\', 200'.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_query('
		update rew_rewritemap
		set url_intern=\''.addslashes($new).'\', url_code=301
		where url_tnt_id='.$config['tnt_id'].'
			and url_lng_code=\''.$lng.'\'
			and (url_extern=\''.addslashes($old).'\' or url_intern=\''.addslashes($old).'\')
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une redirection
 *	@param string $url Obligatoire, url à l'origine d'une redirection 301
 *	@param string $dest Obligatoire, url de destination d'une redirection 301, celle-ci doit déjà existée
 *	@param int $wst Facultatif, permet de spécifier sur quel site la redirection doit être ajoutée
 *	@param bool|string $lng Facultatif, permet de spécifier sur quelle version du site la redirection doit être ajoutée
 *	@param int $cls_id Facultatif, identifiant d'une classe
 *	@param int|array $obj Facultatif, identifiant d'un objet ou tableau d'identifiants (pour les clés composées)
 *
 *	@return bool|int true en cas de succès, -1 si l'url de redirection existe déjà ou false en cas d'erreur
 */
function rew_rewritemap_redirection_add( $url, $dest, $wst=0, $lng=false, $cls_id=0, $obj=0 ){
	if( $wst>0 && !wst_websites_exists($wst) ){
		return false;
	}

	// vérifier que l'url de destination est une url qui existe
	if( !rew_rewritemap_exists($dest, '', $lng) ){
		return false;
	}

	// vérifier que l'url source existe déjà
	if( rew_rewritemap_exists($url, '', $lng, $wst>0 ? $wst : false) ){
		return -1;
	}

	if( $url==$dest ){
		return false;
	}

	// créer un tableau pour tous les sites
	$websites = array();
	if( $wst>0 ){
		$websites[] = $wst;
	} else {
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) ){
			return false;
		}

		while( $wst = ria_mysql_fetch_array($rwst) ){
			$websites[] = $wst['id'];
		}
	}

	// tableau contenant les langues pour les sites
	$languages = array();
	foreach( $websites as $wst ){
		$languages[$wst] = array();

		$rl = wst_websites_languages_get( $wst );
		if( !$rl || !ria_mysql_num_rows($rl) ){
			return false;
		}

		while( $l = ria_mysql_fetch_array($rl) ){
			if( ($lng && $l['lng_code']==$lng) || !$lng ){
				$languages[ $wst ][] = $l['lng_code'];
			}
		}
	}

	if( !sizeof($languages) ){
		return false;
	}

	if( !is_numeric($cls_id) || $cls_id<0 ){
		return false;
	}

	if( is_array($obj) ){
		if( sizeof($obj)<0 || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}

		$obj = $obj>0 ? array( $obj ) : array();
	}

	$obj_cols = array();
	$obj_vals = array();
	if( $cls_id>0 ){
		$obj_cols[] = 'url_cls_id';
		$obj_vals[] = $cls_id;
	}

	if( sizeof($obj) ){
		$i=0;
		while( $i<sizeof($obj) ){
			$obj_cols[] = 'url_obj_id_'.$i;
			$obj_vals[] = $obj[$i];
			$i++;
		}
	}

	global $config;

	if( $url=='/' ){
		return false;
	}

	$error = false;
	foreach( $websites as $wst ){
		if( !isset($languages[$wst]) ){
			$error = true;
		}else{
			$lng = $languages[ $wst ];
			foreach( $lng as $l ){
				$sql ='
					insert into rew_rewritemap
						( url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public'.( sizeof($obj_cols) ? ', '.implode(', ', $obj_cols) : '' ).' )
					values
						( '.$config['tnt_id'].', '.$wst.', \''.$l.'\', \''.$url.'\', \''.$dest.'\', 301, 1'.( sizeof($obj_vals) ? ', '.implode(', ', $obj_vals) : '' ).' )
				';

				if( !ria_mysql_query($sql) ){
					$error = true;
				}
			}
		}
	}

	if( $error ){
		rew_rewritemap_redirection_del( $url );
	}

	return ( !$error ? true : false );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une redirection
 *	@param string $url Obligatoire, url de redirection
 *	@param bool|string $lng Facultatif, code ISO de la langue utilisé pour la redirection
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function rew_rewritemap_redirection_del( $url, $lng=false ){
	if( !trim($url) || $url=='/' ) return false;
	if( $lng!==false && !in_array($lng, $config['i18n_lng_used']) ) return false;
	global $config;

	$sql = '
		delete from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_extern=\''.addslashes($url).'\'
			and url_code=301
	';

	if( $lng ){
		$sql .= ' and url_lng_code\''.$lng.'\'';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une redirection et de recréer les liens par rapport à une l'url de base
 *	@param string $url Obligatoire, url de redirection
 *	@param string $base_url Obligatoire, url de base qui était présente avant la redirection
 *	@param bool|string $lng Facultatif, code ISO de la langue utilisé pour la redirection
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function rew_rewritemap_clean_redirection_del_then_update_with_base_url( $old_url, $base_url, $lng=false){
	global $config;
	if( !trim($old_url) || $old_url=='/' ) {
		return false;
	}

	if( !trim($base_url) || $base_url=='/' ) {
		return false;
	}
	if( $lng!=false && !in_array($lng, $config['i18n_lng_used']) ){
		return false;
	}
	// Fonctionnement de l'algorithme :
	// Dans le cas d'une URL qui a été personnalisée, l'URL de base redirige normalement vers l'URL personnalisée, puis y a une 200 qui est crée depuis l'URL personnalisée
	// Il y a aussi pontentiellement des redictions d'autres articles vers l'URL personnalisée
	// Pour tout conserver, l'algorithme est le suivant :
	// 1 - Suppression des redirections entre l'URL de base et l'URL qui a été mise en personnalisée (permet de ne plus utiliser l'URL personnalisée par rapport à l'URL de base)
	// 2 - Modification des URLs qui partent de l'URL personnalisée pour mettre l'URL de base (permet de conserver les liens qui avaient été créés à partir de l'URL personnalisée comme les redirections/la 200)
	// 3 - Modification des URLs qui vont vers l'URL personnalisée pour mettre l'URL de base (permet de conserver les produits qui pointaient vers le produit actuel)
	// 4 - Modification des prd_url_unreachable des produits qui avaient l'URL personnalisée pour mettre l'URL de base (idem que précédemment)


	// 1- Suppression des redirections entre l'URL de base et l'URL qui a été mise en personnalisée
	$sql = '
		delete from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
		and url_extern=\''.addslashes($base_url).'\'
		and url_intern=\''.addslashes($old_url).'\'
			and url_code=301
	';
	if( $lng ){
		$sql .= ' and url_lng_code=\''.$lng.'\'';
	}
	if (!ria_mysql_query( $sql )) {
		return false;
	}

	// 2 - Modification des URLs qui partent de l'URL personnalisée pour mettre l'URL de base
	$sql = '
	UPDATE rew_rewritemap
	SET url_extern = \''.addslashes($base_url).'\'
	where url_tnt_id='.$config['tnt_id'].'
	and url_extern=\''.addslashes($old_url).'\'
	';
	if( $lng ){
		$sql .= ' and url_lng_code=\''.$lng.'\'';
	}

	if (!ria_mysql_query( $sql )) {
		return false;
	}
	// Techniquement il devrait aussi forcément y avoir au moins une 301 ou une 200 mais en fait pas forcément parce qu'il est juste possible qu'une url soit pas disponible donc pas de vérif ici// 2 - Modification des URLs qui partent de l'URL personnalisée pour mettre l'URL de base

	// 3 - Modification des URLs qui vont vers l'URL personnalisée pour mettre l'URL de base
	$sql = '
	UPDATE rew_rewritemap
	SET url_intern = \''.addslashes($base_url).'\'
	where url_tnt_id='.$config['tnt_id'].'
	and url_intern=\''.addslashes($old_url).'\'
	';
	if( $lng ){
		$sql .= ' and url_lng_code=\''.$lng.'\'';
	}

	if (!ria_mysql_query( $sql )) {
		return false;
	}

	// 4 - Modification des prd_url_unreachable des produits qui avaient l'URL personnalisée pour mettre l'URL de base pour les produits seulement
	// Fonctionne quelle que soit la classe de l'URL personnalisée supprimée, étant donné qu'on peut mettre n'importe quelle URL personnalisée
	return prd_products_change_url_unreachable( $old_url, $base_url );
}
// \endcond


/** Cette fonction permet de récupérer une url traduite d'une page fixe.
 *	@param string $extern Obligatoire, url externe de la page
 *	@param bool|string $lng Obligatoire, code ISO d'une langue différente de celle par défaut
 *	@return string L'URL de la page traduite si elle existe
 */
function rew_rewritemap_translate_get( $extern, $lng ){
	if( !trim($extern) ) return $extern;
	global $config;

	if( !in_array($lng, $config['i18n_lng_used']) ) return $extern;

	$res = ria_mysql_query('
		select url_intern as intern
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and (url_wst_id=0 or url_wst_id='.$config['wst_id'].')
			and url_extern=\''.addslashes( $extern ).'\'
			and url_lng_code=\''.addslashes( $lng ).'\'
			and url_code=301
		order by url_wst_id desc
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return $extern;
	}

	return ria_mysql_result( $res, 0, 'intern' );
}

// \cond onlyria
/** Cette fonction permet de supprimer les caches en places pour une ou plusieurs urls
 *	@param string|array $urls Obligatoire, url ou tableau d'url
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function rew_rewritemap_clean_cache( $urls ){
	if( !is_array($urls) ){
		if( !is_string($urls) || trim($urls) == '' ){
			return false;
		}

		$urls = array( $urls );
	}else{
		foreach( $urls as $one_url ){
			if( !is_string($one_url) || trim($one_url) == '' ){
				return false;
			}
		}
	}

	global $config, $memcached;

	$rwebsite = wst_websites_languages_get();
	if( !$rwebsite || !ria_mysql_num_rows($rwebsite) ){
		return false;
	}

	foreach( $urls as $one_url ){
		ria_mysql_data_seek( $rwebsite, 0 );

		while( $website = ria_mysql_fetch_assoc($rwebsite) ){
			$res = $memcached->delete( 'rew_rewrite-'.$config['tnt_id'].'-'.$website['wst'].'-'.$website['lng_code'].'-'.$one_url );
			if( $res !== true && ria_is_memcached_result_ok($memcached) ){
				return false;
			}
		}
	}

	return true;
}
// \endcond

/// @}

// \cond onlyria

/**	\defgroup rewriting_seo Référencement des pages statiques
 *	\ingroup rewriting
 *	Ce module comprend les fonctions nécessaires à l'ajout d'informations de référencement naturel aux pages statiques qui ne sont pas gérées par la gestion de contenu.
 *	Les pages statiques ont vocation à disparaître et à être remplacées par des pages dynamiques.
 *	@{
 */

/** Cette fonction permet d'ajout une personnalisation de référencement pour une page statique.
 *	@param string $url Obligatoire, url de la page en question
 *	@param string $name Obligatoire, nom de la page
 *	@param string $tag_title Facultatif, titre personnalisée
 *	@param string $tag_desc Facultatif, description personnalisée
 *	@param int $wst Facultatif, identifiant d'un site (par défaut celui en cours)
 *	@param string $lng Optionnel code de la langue (si vide, on prend la langue par défaut)
 *	@return int L'identifiant si l'ajout s'est correctement déroulé, False dans le cas contraire
 *	@return int -1 si l'url n'existe pas
 *	@return int -2 si l'url fait référence à un contenu personnalisable
 */
function rew_tags_add( $url, $name, $tag_title='', $tag_desc='', $wst=false, $lng='' ){
	if( trim($url)=='' || (trim($tag_title)=='' && trim($tag_desc)=='') ){
		return false;
	}

	global $config;

	$wst = !$wst || !wst_websites_exists($wst) ? $config['wst_id'] : $wst;
	$lng = trim($lng)!='' && in_array( strtolower($lng), $config['i18n_lng_used'] ) ? $lng : $config['i18n_lng'];

	$tag_id = rew_tags_exists_byurl( $url, $wst, $lng );
	if( $tag_id ){
		return rew_tags_update( $id, $name, $tag_title, $tag_desc );
	}

	$rsite_url = wst_websites_languages_get( $wst, $lng );
	if( !$rsite_url || !ria_mysql_num_rows($rsite_url) ){
		return false;
	}

	// Si l'url donnée est une url absolue, alors on a la complète avec l'url du site
	$site_url = ria_mysql_result( $rsite_url, 0, 'url' );
	if( !strstr($url, $site_url) ){
		$url = $site_url.$url;
	}

	if( !Sitemap::checkUrl($url, true, $site_url) ){
		return -1;
	}

	$url = str_replace( $site_url, '', $url );
	if( !rew_tags_is_static($url, $wst, $lng) ){
		return -2;
	}

	global $config;

	$res = ria_mysql_query('
		insert into rew_tags
			(tag_tnt_id, tag_wst_id, tag_lng_code, tag_name, tag_url, tag_title, tag_desc )
		values
			( '.$config['tnt_id'].', '.$wst.', "'.addslashes( $lng ).'", "'.addslashes( $name ).'", "'.addslashes( $url ).'", "'.addslashes( $tag_title ).'", "'.addslashes( $tag_desc ).'" )
	');

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de savoir si une personnalisation existe bien.
 *	@param int $id Obligatoire, identifiant d'une personnalisation
 *	@return bool True si elle existe, False dans le cas contraire
 */
function rew_tags_exists( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from rew_tags
		where tag_tnt_id='.$config['tnt_id'].'
			and tag_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de vérifier qu'une url est bien une page statique, donc aucune contenu personnalisable (produit, catégories, cms, actualité...).
 *	@param string $url Obligatoire, url de la page
 *	@param int $wst Facultatif, identifiant d'un site
 *	@param string $lng Facultatif, identifiant d'une langue
 *	@return bool True s'il s'agit d'une page statique, False dans le cas contraire
 */
function rew_tags_is_static( $url, $wst=0, $lng='' ){
	if( trim($url)=='' || trim($url)=='/' ){
		return false;
	}

	global $config;

	$wst = !$wst || !wst_websites_exists($wst) ? $config['wst_id'] : $wst;
	$lng = trim($lng)!='' && in_array( strtolower($lng), $config['i18n_lng_used'] ) ? $lng : $config['i18n_lng'];

	$rsite_url = wst_websites_languages_get( $wst, $lng );
	if( !$rsite_url || !ria_mysql_num_rows($rsite_url) ){
		return false;
	}

	// Si l'url donnée est une url absolue, alors on a la complète avec l'url du site
	$site_url = ria_mysql_result( $rsite_url, 0, 'url' );

	$have_param = false;
	if( preg_match( '/\/-page([0-9]+)/', $url, $matches ) ){
		$have_param = true;
		$url = preg_replace( '/\/-page[0-9]+/', '', $url );
	}

	if( preg_match( '/\?(.+)/', $url, $matches ) ){
		$have_param = true;
		$url = preg_replace( '/\?(.+)/', '', $url );
	}

	// Récupère l'url intern reliée à cette url
	$intern = ''; $i = 0;
	while( true ){
		if( $i>=10 ){
			return false;
		}

		$no_strip = $url = str_replace( $site_url, '', $url );

		if( isset($config['rew_strip']) && trim($config['rew_strip'])!='' ){
			if( !preg_match('/'.str_replace('/', '\/', $config['rew_strip']).'/', $url) ){
				$url = str_replace(
					array('/catalogue/', '/catalog/', '/catalogo/'),
					array('/catalogue'.$config['rew_strip'].'/', '/catalog'.$config['rew_strip'].'/', '/catalogo'.$config['rew_strip'].'/'),
					$url
				);
			}
		}

		$res = ria_mysql_query('
			select url_intern, url_code
			from rew_rewritemap
			where url_tnt_id='.$config['tnt_id'].'
				and (url_extern="'.addslashes( $url ).'" or url_extern="'.addslashes( $no_strip ).'")
				and (url_wst_id=0 or url_wst_id='.$wst.')
				and url_lng_code="'.addslashes( $lng ).'"
			order by url_wst_id desc
		');

		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		$r = ria_mysql_fetch_array( $res );
		$url = $intern = $r['url_intern'];

		if( $r['url_code']==200 ){
			break;
		}

		$i++;
	}

	$ar_intern_no_static = array();

	// Récupère les constitutions des urls internes des CMS
	if( !$have_param ){
		$rcms = ria_mysql_query('
			select cat_url
			from cms_categories
			where cat_tnt_id='.$config['tnt_id'].'
				and cat_parent_id=0
				and ifnull(cat_url, "")!=""
		');

		if( $rcms && ria_mysql_num_rows($rcms) ){
			while( $cms = ria_mysql_fetch_array($rcms) ){
				if( trim($cms['cat_url'])!='' && trim($cms['cat_url'])!='/' ){
					$ar_intern_no_static[] = '/'.str_replace(array('/', '?'), array('\/', '\?'), $cms['cat_url']).'/i';
				}
			}
		}
	}

	$rcfg = cfg_urls_get( $wst );
	if( $rcfg && ria_mysql_num_rows($rcfg) ){
		while( $cfg = ria_mysql_fetch_array($rcfg) ){
			switch( $cfg['cls_id'] ){
				case CLS_BRAND :
					$ar_intern_no_static[] = '/'.str_replace(array('/', '?'), array('\/', '\?'), $cfg['url']).''.( strstr($cfg['url'], '?') ? '&' : '\?' ).'brd=[0-9]+$/i';
					break;
				case CLS_PRODUCT :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+\&prd=[0-9]+$/i';
					break;
				case CLS_CATEGORY :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+$/i';
					break;
				case CLS_STORE :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?str=[0-9]+$/i';
					break;
				case CLS_TYPE_DOCUMENT :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?t=[0-9]+$/i';
					break;
				case CLS_FAQ_CAT :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+$/i';
					break;
				case CLS_FAQ_QST :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+&qst=[0-9]+$/i';
					break;
				case CLS_NEWS_CAT :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+$/i';
					break;
				case CLS_NEWS :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?n=[0-9]+$/i';
					break;
				case CLS_WISHLISTS :
					$ar_intern_no_static[] = '/'.str_replace('/', '\/', $cfg['url']).'\?cat=[0-9]+$/i';
					break;
				default :
					$ar_intern_no_static[] = '/'.str_replace(array('/', '?'), array('\/', '\?'), $cfg['url']).''.( strstr($cfg['url'], '?') ? '&' : '\?' ).'/i';
					break;
			}

		}
	}

	foreach( $ar_intern_no_static as $pattern ){
		if( preg_match( $pattern, $intern) ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de savoir si une personnalisation d'une page statique existe déjà d'après son url
 *	@param string $url Obligatoire, url de la page en question
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $lng Obligatoire code de la langue
 *	@return int L'identifiant de la personnalisation si elle existe, False dans le cas contraire
 */
function rew_tags_exists_byurl( $url, $wst, $lng ){
	if( trim($url)=='' ){
		return false;
	}

	if( !is_numeric($wst) || $wst<=0 ){
		return false;
	}

	global $config;

	if( trim($lng)=='' || !in_array($lng, $config['i18n_lng_used']) ){
		return false;
	}

	$res = ria_mysql_query('
		select tag_id
		from rew_tags
		where tag_tnt_id='.$config['tnt_id'].'
			and tag_wst_id='.$wst.'
			and tag_lng_code="'.addslashes( $lng ).'"
			and tag_url="'.addslashes( $url ).'"
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['tag_id'];
}

/** Cette fonction permet de récupérer les informations de références pour une ou plusieurs pages statiques.
 *	@param int $id Facultatif, identifiant de la page statique
 *	@param string $url Facultatif, url d'une page précise
 *	@param int $wst Facultatif, identifiant d'un site web
 *	@param string $lng Facultatif, identifiant d'une langue
 *	@return resource Un résultat de requête MySQL contenant :
 *				- id : identifiant de l'enregistrement
 *				- name : nom donnée à la personnalisation
 *				- url : url de la page
 *				- wst_id : identifiant du site de personnalisation
 *				- lng_code : code de la langue de personnalisation
 *				- tag_title : titre personnalisé
 *				- tag_desc : description personnalisée
 */
function rew_tags_get( $id=0, $url='', $wst=0, $lng='' ){
	if( !is_numeric($id) || $id<0 ){
	    return false;
	}

	if(!is_numeric($wst) || $wst<0 ){
		return false;
	}

	global $config;

	$sql = '
		select tag_id as id, tag_name as name, tag_url as url, tag_title, tag_desc, tag_wst_id as wst_id, tag_lng_code as lng_code
		from rew_tags
		where tag_tnt_id='.$config['tnt_id'].'
	';

	if( $id>0 ){
		$sql .= ' and tag_id='.$id;
	}

	if( trim($url)!='' ){
		$sql .= ' and tag_url="'.addslashes( $url ).'"';
	}

	if( $wst>0 ){
		$sql .= ' and tag_wst_id='.$wst;
	}

	if( trim($lng)!='' ){
		$sql .= ' and tag_lng_code="'.$lng.'"';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer le titre personnalisé d'une page statique.
 *	@param string $url Obligatoire, url de la page
 *	@param int $wst Facultatif, identifiant d'un site internet, par défaut on prendre celui de la configuration
 *	@param string $lng Facultatif, code de la langue, par défaut on prendre celle en cours d'utilisation
 *	@return string Le titre personnalisé de la page
 */
function rew_tags_get_title( $url, $wst=0, $lng='' ){
	if( trim($url)=='' ){
		return false;
	}

	global $config, $memcached;

	$wst = is_numeric($wst) && $wst>0  ? $wst : $config['wst_id'];
	$lng = trim($lng)!='' && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : i18n::getLang();

	$key_memcached = 'rewtagsgettitle:'.$config['tnt_id'].':'.md5($url).':'.$wst.':'.$lng;

	$tag_title = $memcached->get( $key_memcached );
	if( trim($tag_title)!='' ){
		return $tag_title;
	}

	$rtag = rew_tags_get( 0, $url, $wst, $lng );
	if( $rtag && ria_mysql_num_rows($rtag) ){
		$tag = ria_mysql_fetch_array( $rtag );
		$tag_title = $tag['tag_title'];
	}

	$memcached->set( $key_memcached, $tag_title, 604800 );
	return $tag_title;
}

/** Cette fonction permet de récupérer la description personnalisée d'une page statique.
 *	@param string $url Obligatoire, url de la page
 *	@param int $wst Facultatif, identifiant d'un site internet, par défaut on prendre celui de la configuration
 *	@param string $lng Facultatif, code de la langue, par défaut on prendre celle en cours d'utilisation
 *	@return string La description personnalisée de la page
 */
function rew_tags_get_desc( $url, $wst=0, $lng='' ){
	if( trim($url)=='' ){
		return false;
	}

	global $config, $memcached;

	$wst = is_numeric($wst) && $wst>0  ? $wst : $config['wst_id'];
	$lng = trim($lng)!='' && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : i18n::getLang();

	$key_memcached = 'rewtagsgetdesc:'.$config['tnt_id'].':'.md5($url).':'.$wst.':'.$lng;

	$tag_desc = $memcached->get( $key_memcached );
	if( trim($tag_desc)!='' ){
		return $tag_desc;
	}

	$rtag = rew_tags_get( 0, $url, $wst, $lng );
	if( $rtag && ria_mysql_num_rows($rtag) ){
		$tag = ria_mysql_fetch_array( $rtag );
		$tag_desc = $tag['tag_desc'];
	}

	$memcached->set( $key_memcached, $tag_desc, 604800 );
	return $tag_desc;
}

/** Cette fonction permet de mettre à jour la personnsalisation du référencement pour une page statique.
 *	@param int $id Obligatoire, identifiant du tag à mettre à jour
 *	@param string $name Facultatif, nom de la page
 *	@param string $tag_title Facultatif, titre personnalisée
 *	@param string $tag_desc Facultatif, description personnalisée
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rew_tags_update( $id, $name='', $tag_title='', $tag_desc='' ){
	if( !rew_tags_exists($id) ){
		return false;
	}

	if( trim($tag_title)=='' && trim($tag_desc)=='' ){
		return rew_tags_del( $url, $wst, $lng );
	}

	global $config, $memcached;

	$name = trim($name)!=''  ? '"'.addslashes( $name ).'"' : 'tag_name';

	$res = ria_mysql_query('
		update rew_tags
		set tag_name='.$name.',
			tag_title="'.addslashes( $tag_title ).'",
			tag_desc="'.addslashes( $tag_desc ).'"
		where tag_tnt_id='.$config['tnt_id'].'
			and tag_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// Supprimer les clés de memcached correspondant à cette url
	$rtag = rew_tags_get( $id );
	if( $rtag && ria_mysql_num_rows($rtag) ){
		$tag = ria_mysql_fetch_array( $rtag );

		$keys = array(
			'rewtagsgettitle:'.$config['tnt_id'].':'.md5($tag['url']).':'.$tag['wst_id'].':'.$tag['lng_code'],
			 'rewtagsgetdesc:'.$config['tnt_id'].':'.md5($tag['url']).':'.$tag['wst_id'].':'.$tag['lng_code']
		);

		foreach( $keys as $k ){
			$memcached->get( $k );
			if( ria_is_memcached_result_ok($memcached) && !$memcached->delete( $k ) ){
				return false;
			}
		}
	}

	return true;
}

/** Cette fonction permet de supprimer une personnsalisation du référencement pour une page statique.
 *
 *	@param int $id Obligatoire, identifiant du tag à supprimer
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function rew_tags_del( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rew_tags
		where tag_tnt_id='.$config['tnt_id'].'
			and tag_id='.$id.'
	');
}

/// @}
// \endcond
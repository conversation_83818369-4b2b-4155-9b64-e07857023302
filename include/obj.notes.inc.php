<?php
// \cond onlyria

require_once('fields.inc.php');
require_once('users.inc.php');

/** \defgroup model_object_notes Notes / commentaires sur les objets par les utilisateurs
 *	Ce module comprend les fonctions nécessaires à la gestion des notes et commentaires concernant tous les types d'objets.
 *	Une note est caractérisée par :
 *		- Son auteur (usr_id)
 *		- Sa date de création, dernière modification et éventuellement suppression
 *		- Son intitulé et son contenu (l'intitulé seul est suffisant, le contenu est facultatif)
 *		- L'objet auquel il fait référence (compte client, commande, produit...)
 *
 *	@{
 */

/**	Cette fonction crée une note sur un objet.
 *	@param int $cls_id Obligatoire, identifiant de la classe de l'objet
 *	@param int $obj_id Obligatoire, identifiant de l'objet (ou tableau, pour les clés multiples)
 *	@param string $name Obligatoire, intitulé de la note
 *	@param $content Optionnel, détail de la note
 *	@param int $usr_id Optionnel, identifiant de l'auteur de la note (par défaut, l'utilisateur en session). Spécifier NULL quand le créateur est inconnu
 *	@param bool $is_sync Optionnel, détermine si la note est synchronisée
 *
 *	@return int L'identifiant généré pour la note en cas de succès
 *	@return bool false en cas d'échec
 */
function fld_object_notes_add( $cls_id, $obj_id, $name, $content='', $usr_id=0, $is_sync=false ){
	if( !fld_classes_exists( $cls_id ) ){
		return false;
	}
	if(!fld_check_object_id($obj_id))
		return false;

	if( !is_array($obj_id) ){
		$obj_id = array( $obj_id );
	}

	if( trim($name) == '' ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		if( $usr_id !== null ){
			// tentative de récupération de la session
			if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || $_SESSION['usr_id'] <= 0 ){
				return false;
			}
			$usr_id = $_SESSION['usr_id'];
		}else{
			$usr_id = 'NULL';
		}
	}elseif( !gu_users_exists( $usr_id ) ){
		// $usr_id est spécifié mais incorrect
		return false;
	}

	$is_sync = $is_sync ? '1' : '0';

	global $config;

	$fields = array('ono_tnt_id', 'ono_cls_id', 'ono_name', 'ono_content', 'ono_date_created', 'ono_usr_id', 'ono_is_sync');
	$values = array($config['tnt_id'], $cls_id, '"'.addslashes(trim($name)).'"', '"'.addslashes(trim($content)).'"', 'now()', $usr_id, $is_sync);

	for( $i = 0; $i < sizeof($obj_id); $i++ ){
		$fields[] = 'ono_obj_id_'.$i;
		$values[] = $obj_id[$i];
	}

	$sql = 'insert into fld_object_notes ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	$r = ria_mysql_insert_id();

	fld_objects_set_date_modified( $cls_id, $obj_id );

	return $r;
}

/**	Cette fonction permet de mettre à jour le libellé et le contenu d'une note sur un objet.
 *	Elle ne permet pas de modifier l'objet auquel elle est rattachée.
 *	@param int $id Identifiant de la note
 *	@param string $name Intitulé de la note
 *	@param $content Contenu de la note (peut être vide)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_object_notes_upd( $id, $name, $content ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( trim($name) == '' ){
		return false;
	}

	$rnote = fld_object_notes_get( $id );
	if( !$rnote || !ria_mysql_num_rows($rnote) ){
		return false;
	}
	$note = ria_mysql_fetch_assoc($rnote);

	global $config;

	$sql = '
		update fld_object_notes
		set ono_name = "'.addslashes(trim($name)).'", ono_content = "'.addslashes(trim($content)).'"
		where ono_tnt_id = '.$config['tnt_id'].' and ono_id = '.$id.'
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	fld_objects_set_date_modified( $note['cls_id'], array($note['obj_id_0'],$note['obj_id_1'],$note['obj_id_2']) );

	return true;
}

/**	Cette fonction supprime une note sur un objet.
 *	La note n'est que virtuellement supprimée via une date de suppression.
 *	@param int $id Identifiant de la note à supprimer
 *
 *	@return bool True en cas de succès, ou si la note avait déjà été supprimée
 *	@return bool False en cas d'échec
 */
function fld_object_notes_del( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// charge la note pour récupérer un éventuel usr_id (et permet de vérifier son existence)
	$rnote = fld_object_notes_get( $id );
	if( !$rnote ){
		return false;
	}elseif( !ria_mysql_num_rows($rnote) ){
		return true;
	}
	$note = ria_mysql_fetch_assoc($rnote);

	global $config;

	$sql = 'update fld_object_notes set ono_date_deleted = now() where ono_tnt_id = '.$config['tnt_id'].' and ono_id = '.$id;

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	fld_objects_set_date_modified( $note['cls_id'], array($note['obj_id_0'],$note['obj_id_1'],$note['obj_id_2']) );

	return true;
}

/**	Cette fonction teste l'existence d'une note sur un objet.
 *	@param int $id Identifiant de la note à tester
 *
 *	@return bool True si la note existe, False sinon
 */
function fld_object_notes_exists( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = 'select 1 from fld_object_notes where ono_tnt_id = '.$config['tnt_id'].' and ono_id = '.$id.' and ono_date_deleted is null';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return true;
}

/**	Cette fonction procède à une recherche sur les notes concernant des objets, selon des paramètres optionnels.
 *	@param int $id Optionnel, identifiant de la note (un tableau d'identifiants est également accepté, mais il ne doit pas être vide)
 *	@param int $cls_id Optionnel, identifiant de la classe de l'objet à laquelle la note fait référence
 *	@param int $obj_id Optionnel, identifiant de l'objet auquel la note fait référence(ou tableau pour les objets à clé composée). Ce paramètre n'est pas pris en compte si $cls_id n'est pas spécifié
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur ayant crée la note (null permet de filtrer les notes sans créateur)
 *	@param string $date_creation Optionnel, recherche sur la date de création, en spécifiant soit une date, soit un tableau de deux dates (début, fin). Dans le cas d'un tableau, la comparaison sera "date >= [0] && date < [1]". La partie horaire de la date n'est jamais prise en compte. Il est possible de spécifier null pour un des deux éléments du tableau.
 *	@param string $name Optionnel, recherche sur l'intitulé de la note. La recherche est de type "%%"
 *	@param $content Optionnel, recherche sur le contenu de la note. La recherche est de type "%%"
 *	@param $case_sensitive Optionnel, détermine si les paramètres $name et $content sont sensibles à la casse. Par défaut c'est le cas
 *	@param bool $is_sync Optionnel, permet de filtrer les notes synchronisées, non synchronisées ou les deux (par défaut)
 *	@param $sort Optionnel, paramètre de tri
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la note
 *		- cls_id : identifiant de la classe de l'objet auquel la note fait référence
 *		- obj_id_0 : identifiant de l'objet auquel la note fait référence
 *		- obj_id_1 : identifiant de l'objet auquel la note fait référence (clé composée - 0 sinon)
 *		- obj_id_2 : identifiant de l'objet auquel la note fait référence (clé composée - 0 sinon)
 *		- obj_id : alias de obj_id_0
 *		- usr_id : identifiant du compte ayant crée la note
 *		- name : intitulé de la note
 *		- content : contenu de la note
 *		- is_sync : détermine si la note est synchronisée
 *		- date_created : date de création au format jj/MM/aaaa hh:mm
 *		- date_created_en : date de création au format yyyy-MM-dd hh:mm:ss
 *		- date_modified : date de dernière modification au format jj/MM/aaaa hh:mm
 *		- date_modified_en : date de dernière modification au format yyyy-MM-dd hh:mm:ss
 *		- usr_exist : détermine si l'utilisateur qui a crée la note existe dans la base de donnée
 */
function fld_object_notes_get( $id=0, $cls_id=0, $obj_id=0, $usr_id=0, $date_creation=null, $name='', $content='', $case_sensitive=true, $is_sync=null, $sort=false ){
	{ // contrôles
		if( is_array($id) ){
			if( !sizeof($id) ){
				return false;
			}
			foreach( $id as $one_id ){
				if( !is_numeric($one_id) || $one_id <= 0 ){
					return false;
				}
			}
		}else{
			if( !is_numeric($id) || $id < 0 ){
				return false;
			}
			if( $id ){
				$id = array($id);
			}else{
				$id = array();
			}
		}

		if( $cls_id !== 0 ){
			if( !fld_classes_exists( $cls_id ) ){
				return false;
			}
			if( is_array($obj_id) ){
				if( !sizeof($obj_id) || sizeof($obj_id) > COUNT_OBJ_ID ){
					return false;
				}
				foreach( $obj_id as $one_id ){
					if( !is_numeric($one_id) || $one_id <= 0 ){
						return false;
					}
				}
			}else{
				if( !is_numeric($obj_id) || $obj_id < 0 ){
					return false;
				}
				// conversion systématique de $obj_id en tableau
				if( $obj_id ){
					$obj_id = array($obj_id);
				}else{
					$obj_id = array();
				}
			}
		}

		if( ( !is_numeric($usr_id) || $usr_id < 0 ) && $usr_id !== null ){
			return false;
		}

		if( is_array($date_creation) ){
			if( sizeof($date_creation) != 2 ){
				return false;
			}
			if( $date_creation[0] !== null ){
				if( !isdateheure($date_creation[0]) ){
					return false;
				}
				$date_creation[0] = substr(dateheureparse($date_creation[0]), 0, 10);
			}
			if( $date_creation[1] !== null ){
				if( !isdateheure($date_creation[1]) ){
					return false;
				}
				$date_creation[1] = substr(dateheureparse($date_creation[1]), 0, 10);
			}
		}elseif( $date_creation !== null ){
			if( !isdateheure($date_creation) ){
				return false;
			}
			$date_creation = substr(dateheureparse($date_creation), 0, 10);
		}

		$name = trim($name);
		$content = trim($content);
	}

	global $config;

	$sql = '
		select
			ono_id as "id", ono_cls_id as cls_id, ono_obj_id_0 as obj_id,
			ono_obj_id_0 as obj_id_0, ono_obj_id_1 as obj_id_1, ono_obj_id_2 as obj_id_2,
			ono_name as "name", ono_content as "content", ono_is_sync as is_sync,
			ono_usr_id as usr_id, if(ifnull(author.usr_id, 0)=0, 0, 1) as usr_exist,
			date_format(ono_date_created,"%d/%m/%Y à %H:%i") as date_created, ono_date_created as date_created_en,
			date_format(ono_date_modified,"%d/%m/%Y à %H:%i") as date_modified, ono_date_modified as date_modified_en
		from
			fld_object_notes
			left join gu_users as author on ono_tnt_id = author.usr_tnt_id and ono_usr_id = author.usr_id and author.usr_date_deleted is null
		where
			ono_tnt_id = '.$config['tnt_id'].' and ono_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and ono_id in ('.implode(', ', $id).')';
	}
	if( $cls_id ){
		$sql .= ' and ono_cls_id = '.$cls_id;
		if( sizeof($obj_id) ){
			for( $i = 0; $i < sizeof($obj_id); $i++ ){
				$sql .= ' and ono_obj_id_'.$i.' = '.$obj_id[$i];
			}
			// plus performant en ajoutant toutes les colonnes de l'index
			for( $i = sizeof($obj_id); $i < COUNT_OBJ_ID; $i++ ){
				$sql .= ' and ono_obj_id_'.$i.' = 0';
			}
		}
	}
	if( $usr_id ){
		$sql .= ' and ono_usr_id = '.$usr_id;
	}elseif( $usr_id === null ){
		$sql .= ' and ono_usr_id is null';
	}
	if( $name ){
		if( $case_sensitive ){
			$sql .= ' and ono_name like "%'.addslashes($name).'%"';
		}else{
			$sql .= ' and lower(ono_name) like "%'.addslashes(strtolower($name)).'%"';
		}
	}
	if( $content ){
		if( $case_sensitive ){
			$sql .= ' and ono_content like "%'.addslashes($content).'%"';
		}else{
			$sql .= ' and lower(ono_content) like "%'.addslashes(strtolower($content)).'%"';
		}
	}
	if( is_array($date_creation) ){
		if( $date_creation[0] !== null ){
			$sql .= ' and date(ono_date_created) >= "'.$date_creation[0].'"';
		}
		if( $date_creation[1] !== null ){
			$sql .= ' and date(ono_date_created) < "'.$date_creation[1].'"';
		}
	}elseif( $date_creation !== null ){
		$sql .= ' and date(ono_date_created) = "'.$date_creation.'"';
	}
	if( $is_sync !== null ){
		$sql .= ' and ono_is_sync = '.( $is_sync ? '1' : '0' );
	}

	// gestion du tri
	$sort_final = array();
	if( is_array($sort) ){
		foreach( $sort as $col=>$dir ){
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'date_created' :
					array_push( $sort_final, 'date_created '.$dir );
					break;
				case 'date_modified' :
					array_push( $sort_final, 'date_modified '.$dir );
					break;
			}
		}
	}
	if( sizeof($sort_final)==0 ) $sort_final = array( 'date_created asc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return $r;
}

/** Cette fonction permet de récupérer la liste des notes pour un ou plusieurs objets
 *	@param $cls Obligatoire, identifiant de la classe conserné
 *	@param $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *		- id : identifiant de la note
 *		- cls_id : identifiant de la classe de l'objet auquel la note fait référence
 *		- obj_id_0 : identifiant de l'objet auquel la note fait référence
 *		- obj_id_1 : identifiant de l'objet auquel la note fait référence (clé composée - 0 sinon)
 *		- obj_id_2 : identifiant de l'objet auquel la note fait référence (clé composée - 0 sinon)
 *		- usr_id : identifiant du compte ayant crée la note
 *		- name : intitulé de la note
 *		- content : contenu de la note
 *		- date_created_en : date de création au format yyyy-MM-dd hh:mm:ss
 *		- date_modified_en : date de dernière modification au format yyyy-MM-dd hh:mm:ss
 */
function fld_object_notes_get_all($cls, $obj, $multi_key=false){
	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			ono_id as "id", ono_cls_id as cls_id, ono_obj_id_0 as obj_id,
			ono_obj_id_0 as obj_id_0, ono_obj_id_1 as obj_id_1, ono_obj_id_2 as obj_id_2,
			ono_name as "name", ono_content as "content", ono_usr_id as usr_id,
			ono_date_created as date_created_en, ono_date_modified as date_modified_en
		from
			fld_object_notes
		where ono_tnt_id = '.$config['tnt_id'].' and ono_cls_id = '.$cls.' and ono_date_deleted is null
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' ono_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and ono_obj_id_1 = '.$o[1];
				}
				if( isset($o[2]) ){
					$where .= ' and ono_obj_id_2 = '.$o[2];
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and ono_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and ono_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and ono_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and ono_obj_id_2 = '.$obj[2];
		}
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour le marqueur de synchronisation d'une note sur un objet
 *	@param int $id Identifiant de la note
 *	@param bool $is_sync Marqueur de synchronisation True / False
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_object_notes_set_is_sync( $id, $is_sync ){
	if( !fld_object_notes_exists( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		update fld_object_notes
		set ono_is_sync = '.( $is_sync ? '1' : '0' ).'
		where ono_id = '.$id.' and ono_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);

	if( !$r && ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction récupère le marqueur de synchronisation d'une note sur un objet
 *	@param int $id Identifiant de la note
 *	@return bool Un booléen indiquant si la note est synchronisée ou non
 */
function fld_object_notes_get_is_sync( $id ){
	if( !fld_object_notes_exists( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		select ono_is_sync as is_sync
		from fld_object_notes
		where ono_id = '.$id.' and ono_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return ria_mysql_result($r, 0, 'is_sync');
}

/// @}
// \endcond

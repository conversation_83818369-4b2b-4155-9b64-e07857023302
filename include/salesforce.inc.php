<?php
use Riashop\Salesforce\TaskFactory;
use Riashop\Salesforce\Exceptions\RetryTaskException;
use <PERSON>iashop\Salesforce\Exceptions\RetryOverTaskException;
use Riashop\PriceWatching\models\LinearRaised\prw_offers;
use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;
use Riashop\PriceWatching\models\LinearRaised\LinearRaisedGetter;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;

require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');
require_once( 'tasks.inc.php' );
require_once ('salesforce/autoload.php');

define('SF_FIELDS_VERSION', 5);
define('RETRY_SAVE_ROW_AMOUNT', 4);
define('LEGRAND_FLD_USR_CONTEXT', 101610);
define('LEGRAND_FLD_PRD_LINE_CLI', 101611);
define('LEGRAND_FLD_PRD_PERIMETRE', 101612);
define('LEGRAND_FLD_CNT_QUALIFICATION', 101613);
define('REL_SECTOR_HIERARCHY', 13);

// \cond onlyria

/**	\defgroup salesforce Salesforce
 *	\ingroup synchro
 *	Ce module comprend les fonctions nécessaires à la communication avec la gestion commerciale Salesforce
 *	Attention beaucoup de donnée sont pour le moment mise en dure et correspondent uniquement au fonctionnement de LeGrand, il est possible que des séparations doivent être faite si d'autre client arrive avec SalesForces
 *	@{
 */

$sf_errors = array();

/** Cette fonction permet l'enregistrement de log dans une variable tampons
 *	@param $string Obligatoire, chaîne de caractère contenant le message à stocker comme log
 */
function sf_log( $string ){
	global $config, $sf_errors;

	if( isset($config['sf_connexion']) && $config['sf_connexion'] != null ){
		// $string = ($sf_connexion->getLastRequest()).$string;
	}

	if( isset($config['sf_debug']) && $config['sf_debug'] ){
		print $string."\r\n";
	}
	$sf_errors[] = $string;
}

/** Cette fonction pert la connexion à Salesforce
 *	@param $action Par défaut en mode écriture, mettre read pour passer en mode lecture
 *	@return object un objet de connexion de type Soap si c'est bon, sinon retourne une exception
 */
function sf_login($action='write'){
	global $config;

	try{

		$salesforce_login	 = $config['salesforce_login'];
		$salesforce_password = $config['salesforce_password'];

		if( !$config['env_sandbox'] ){

			if ($action == 'read') {
				if (
					isset($config['salesforce_login_read'], $config['salesforce_password_read'])
					&& trim($config['salesforce_login_read']) != '' && trim($config['salesforce_password_read']) != ''
				) {
					$salesforce_login = $config['salesforce_login_read'];
					$salesforce_password = $config['salesforce_password_read'];
				}
			}

			// partie de code spé à legrand pour le moment, a déplacer au besoin
			// décryptage du password via google kms pour ne pas l'avoir en clair
			$projectId = "riashop-186610";
			$locationId = "europe-west1";
			$keyRingId = "Legrand-Salesforce";
			$cryptoKeyId = "Salesforce_api_password";

			$bearer=exec("/usr/bin/gcloud auth print-access-token");

			$header = array(
				'Authorization:Bearer '.$bearer,
				'Content-Type:application/json',
			);
			$data = array('ciphertext' => $salesforce_password);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, "https://cloudkms.googleapis.com/v1/projects/".$projectId."/locations/".$locationId."/keyRings/".$keyRingId."/cryptoKeys/".$cryptoKeyId.":decrypt");
			curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
			curl_setopt($ch, CURLOPT_TIMEOUT, 60);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
			curl_setopt($ch, CURLOPT_POST, 1);
			$result = curl_exec($ch);

			if( !$result ){
				throw new Exception("Erreur de décryptage de la clé");
			}

			$result = json_decode($result, true);
			if( !isset($result['plaintext']) ){
				throw new Exception("Erreur de décryptage de la clé");
			}

			$password = base64_decode($result['plaintext']);
		}else{
			$password = $salesforce_password;
		}

		$sf_connexion = new SforceEnterpriseClient();
		$mySoapClient = $sf_connexion->createConnection($config['site_dir'].'/../salesforce/'.$config['salesforce_wsdl'], null, array('cache_wsdl' => WSDL_CACHE_NONE));
		$login = $sf_connexion->login($salesforce_login, $password);

		// lance la récupération des variables de config défini dans Saleforces
		$query_select = array(
				'Id_Prix_commando__c',
				'Id_Prix_commando_distributeur__c',
				'Profile_representant__c',
				'Id_Ciblage__c',
				'Id_Type_Comptes__c',
				'Contact_Status__c',
				'Compte_Status__c',
				'Profile_revendeurs__c',
				'Id_demande_type_moyen_pub__c',
				'Id_marque_legrand__c',
				'Id_ciblage_distributeur__c',
				'Status_commande__c',
				'Date_livraison_max__c'
			);

		$response = $sf_connexion->queryAll("select Id, ".implode(',',$query_select)." from Yuto__c where IsDeleted=false");
		if( !$response ){
			throw new Exception("Erreur de récupération des configurations");
		}
		$response = sf_soql_response_parse($response, $query_select);
		if( sizeof($response['records']) != 1 ){
			throw new Exception("Erreur de récupération des configurations, aucune ou plusieurs configuration défini");
		}
		foreach ($response['records'] as $record) {
			$salesforce_config =  (array) $record;
		}

		// récupère la configuration actuelle de riashop, si elle est différente il va falloir intervenir
		if( $config['salesforce_config'] ){
			$config['salesforce_config'] = (array) json_decode($config['salesforce_config']);

			foreach( $salesforce_config as $key => $val){
				if( !isset($config['salesforce_config'][$key]) || $config['salesforce_config'][$key] != $val ){

					$updated = false;

					if( $key == 'Contact_Status__c' ){ // remise à zéro de la synchro des contacts, le filtre à changé
						tsk_activities_set( TSK_CONTACT_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						$updated = true;
					}

					if( in_array($key, array('Id_Ciblage__c', 'Id_ciblage_distributeur__c', 'Id_Type_Comptes__c', 'Profile_representant__c', 'Compte_Status__c')) ){ // remise à zéro de la synchro des contacts, le filtre à changé
						tsk_activities_set( TSK_USR_SELLER_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						tsk_activities_set( TSK_USER_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						tsk_activities_set( TSK_CONTACT_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						$updated = true;
					}

					if( in_array($key, array('Profile_revendeurs__c', 'Compte_Status__c')) ){
						tsk_activities_set( TSK_RESELLER_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						$updated = true;
					}

					if( in_array($key, array('Id_Prix_commando__c', 'Id_Prix_commando_distributeur__c')) ){
						tsk_activities_set( TSK_PRD_PRC_ADD, 0, 0, 0, date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), null );
						$updated = true;
					}

					if( in_array($key, array('Status_commande__c', 'Id_demande_type_moyen_pub__c', 'Id_marque_legrand__c','Date_livraison_max__c')) ){
						$updated = true;
					}

					// need action
					if(!$updated){
						throw new Exception("La configuration SF à été changé SYNCHRO BLOQUE : ". $key."=".$val);
					}
				}
			}
		}

		$config['sf_connexion'] = $sf_connexion;

		sf_fields_models_update();

		// la config ria prend celle de SF.
		$config['salesforce_config'] = array_merge(
			$salesforce_config,
			array(
				'sf_fields_version' => SF_FIELDS_VERSION
			)
		);

		// enregistre cette config dans la base
		if( !cfg_overrides_set_value( 'salesforce_config', json_encode($config['salesforce_config']) ) ){
			throw new Exception("Erreur d'enregistrement des configurations dans la base ");
		}

		return $sf_connexion;

	} catch (Exception $e) {
		sf_log($e->getMessage());
	}

	return false;
}

/** Cette fonction pert la déconnexion à Salesforce
 */
function sf_logout(){
	global $config;

	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ) return;
	try{
		$config['sf_connexion']->logout();
		$config['sf_connexion'] = false;
	} catch (Exception $e) {
		return;
	}
}

/** Cette fonction retourne la liste des champs avancé sur les comptes clients
 * Modèles de saisies Concurrences
 */
function sf_fields_get(){
	return array(
		449 => array( //'Concurrence LG - Appareillage',
				101614 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_StandardEquipment__c'),
				101615 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_App_Etanche__c'),
				101616 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Goulotte__c'),
				101617 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Domotic_System__c'),
				101618 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_2_Brand_SandardEquipment__c'),
				101619 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_2_Brand_App_Etanche__c'),
				101620 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_Priv_Brand_Goulotte__c'),
				101657 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_App_Saillie__c'),
				101658 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_2_Brand_App_Saillie__c'),
		),
		450 => array( //'Concurrence LG - Protection',
				101621 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_ModularProtection_2__c'),
				101622 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_PowerEquipment__c'),
				101623 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Mesure__c'),
				101624 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Indust_Box_Closet__c'),
				101625 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_Priv_Brand_ModularProtection_2__c'),
				101626 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_2_Brand_PowerEquipment__c'),
		),
		451 => array( //'Concurrence LG - Systèmes',
				101627 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_EmergencyLighting__c'),
				101628 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_VDI_BoxCloset__c'),
				101629 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Com_Casket__c'),
				101630 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_SL_Eclairage_marque_1__c'),
				101631 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Lighting_Tertiary__c'),
				101632 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_1_Brand_Portier__c'),
				101633 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_LG_Brand1_Conduits__c'),
				101634 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_2_Brand_EmergencyLighting__c'),
				101635 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_Priv_Brand_VDI_BoxCloset__c'),
		),
		452 => array( //'Concurrence LG - Intégration',
				101636 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_X1ReMarqueCVC__c'),
				101637 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_IPTV__c'),
				101638 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_X1ReMarqueSonorisation__c'),
				101639 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_X1ReMarqueVideosurveillance__c'),
				101640 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_X1ReMarqueControleDacces__c'),
				101641 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_GtbGtcBms__c'),
				101642 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_X1ReMarqueLogicielDeReservation__c'),
		),
		453 => array( //'Equipement LG',
				101643 => array( 'fld_type' => FLD_TYPE_SELECT_MULTIPLE, 'sf_name' => 'FR_Presentation_Means__c'),
		),
		455 => array( //QUalification
				101644 => array( 'fld_type' => FLD_TYPE_FLOAT, 'sf_name' => 'AnnualRevenue'),
				101645 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_AR_Year_Total__c'),
				101646 => array( 'fld_type' => FLD_TYPE_FLOAT, 'sf_name' => 'FR_AR_LeGrand__c'),
				101647 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_AR_Year_Legrand__c'),
				101648 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_Sales_Pt_Config__c'),
				101649 => array( 'fld_type' => FLD_TYPE_INT, 'sf_name' => 'NumberOfEmployees'),
				101650 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_Point_Of_Sales_Domain__c'),
				101651 => array( 'fld_type' => FLD_TYPE_INT, 'sf_name' => 'FR_Nb_Of_Electricity_Branch_Employees__c'),
				101652 => array( 'fld_type' => FLD_TYPE_SELECT, 'sf_name' => 'FR_DO_Schneider__c'),
				101653 => array( 'fld_type' => FLD_TYPE_BOOLEAN_YES_NO, 'sf_name' => 'FR_TG_Premium__c'),
				101654 => array( 'fld_type' => FLD_TYPE_BOOLEAN_YES_NO, 'sf_name' => 'FR_Envoi_Catalogue__c'),
		)
	);
}

/**
 *	Cette fonction permet de gérer les différentes valeurs de champs possible pour les compte
 *  En fonction de la config sf_fields_version elle peut aussi mettre à jour les valeurs de restrictions en prenant la version de SF.
 */
function sf_fields_models_update(){
	global $config;

	if( $config['tnt_id'] != 59 ) return;

	// on va parser ces information que lors ce que la version de la config a été monté, pour éviter du traitement inutile tous les appels
	if( !isset($config['salesforce_config']['sf_fields_version'] ) || $config['salesforce_config']['sf_fields_version'] < SF_FIELDS_VERSION ){

		foreach( sf_fields_get() as $mdl_id => $fields ){

			// récupère la liste des champs associé à ce model sur riashop
			$ria_fields = array();
			$rflds = fld_models_fields_get($mdl_id);
			if( $rflds ){
				while( $fld = ria_mysql_fetch_assoc($rflds) ){
					$ria_fields[ $fld['id'] ] = $fld['id'];
				}
			}


			foreach( $fields as $fld_id => $fld ){

				// check si le fld fait partie du model
				if( isset($ria_fields[ $fld_id ]) ){
					unset($ria_fields[ $fld_id ]);
				}else{
					fld_models_fields_add($mdl_id, $fld_id);
				}

				// récupère la liste des valeurs possible pour ce champs dans la bdd local
				$ria_values = array();
				$rvalues = fld_restricted_values_get(0, $fld_id);
				if( $rvalues ){
					while( $v = ria_mysql_fetch_assoc($rvalues) ){
						$ria_values[$v['name']] = $v['id'];
					}
				}


				// lance la récupération des variables de config défini dans Saleforces
				$query_select = array(
						'id',
						'DurableId',
						'DataType',
						'QualifiedApiName',
						'Label'
					);

				$response = $config['sf_connexion']->queryAll("
					select ".implode(',',$query_select)."
					from FieldDefinition
					where EntityDefinition.QualifiedApiName = 'Account' and QualifiedApiName='".$fld['sf_name']."'");
				if( !$response ){
					throw new Exception("Erreur de récupération des champs : ".$fld['sf_name']);
				}
				$response = sf_soql_response_parse($response, $query_select);
				if( sizeof($response['records']) != 1 ){
					throw new Exception("Erreur de récupération des champs, aucune ou plusieurs champs défini");
				}
				foreach ($response['records'] as $record) {
					$one_field =  (array) $record;

					// mise à jour du nom de champ local
					fld_fields_set_name($fld_id, $one_field['Label']);


					if( $fld['fld_type'] == FLD_TYPE_SELECT  || $fld['fld_type'] == FLD_TYPE_SELECT_MULTIPLE ){
						// récupération des valeurs possible sur SF
						$query_select = array(
							'DurableId',
							'EntityParticleId',
							'IsActive',
							'IsDefaultValue',
							'Label',
							'ValidFor',
							'Value'
						);

						$response_values = $config['sf_connexion']->queryAll("
							select ".implode(',',$query_select)."
							from PicklistValueInfo
							where EntityParticleId='".$one_field['DurableId']."' and IsActive = true");
						if( !$response_values ){
							throw new Exception("Erreur de récupération des valeurs du champs : ".$fld['sf_name']);
						}
						$response_values = sf_soql_response_parse($response_values, $query_select);
						foreach ($response_values['records'] as $record_value) {
							$one_value =  (array) $record_value;

							//
							if( isset($ria_values[$one_value['Value']]) ){
								unset($ria_values[$one_value['Value']]);
							}else{
								fld_restricted_values_add($fld_id, $one_value['Value']);
							}
						}
					}

				}

				// suppression des valeurs anciennes
				foreach( $ria_values as $name => $id ){
					fld_restricted_values_del($id);
				}

			}

			//suppression des champs du model
			foreach( $ria_fields as $fld_id => $id ){
				fld_models_fields_del($mdl_id, $fld_id);
			}

		}
	}
}

function sf_task_execute($tsk_id, $retry=0){
	global $config;
	if( !$config['sf_connexion'] ) return;

	date_default_timezone_set('UTC');

	$date_start = date('Y-m-d H:i:s');

	// récupère la date de la dernière synchronisation
	$previous_last_date_obj = null;
	$last_date_obj = null;
	$ract = tsk_activities_get($tsk_id);
	if( $ract && ria_mysql_num_rows($ract) ){
		$act = ria_mysql_fetch_assoc($ract);
		$previous_last_date_obj = $last_date_obj = date('Y-m-d H:i:s', strtotime($act['last_date_obj']));
	}

	// cas particulier avec les catégories on repasse à chaque fois dessus pour checker les dates de publications
	// pas fait avec les notres pour éviter de grosse modifications côté yuto.
	if( $tsk_id == TSK_CATEGORIE_ADD ){
		$last_date_obj = null;
	}

	$query_table = sf_task_query_from($tsk_id);
	$query_select = sf_task_query_select($tsk_id);
	$query_where = sf_task_query_where($tsk_id);

	if( $last_date_obj != null ){
		$query_where['LastModifiedDate'] = 'LastModifiedDate >'.gmdate('c', strtotime($last_date_obj));
	}

	try{

		$response = $config['sf_connexion']->queryAll(sf_soql_format($query_table, $query_select, $query_where, array()));

		 // retire la config temporaire car le nombre de commerciaux peux changer d'une tache a l'autre
		if( isset($config['salesforce_targeting_account']) ) {
			unset($config['salesforce_targeting_account']);
		}
		if( isset($config['salesforce_getrow_cache']) ) {
			unset($config['salesforce_getrow_cache']);
		}

		$cpt = 0;
		$nb_obj_done=0;
		while($cpt++ < 2000){
			$response = sf_soql_response_parse($response, $query_select);

			if( isset($config['sf_debug']) && $config['sf_debug'] ){
				print "Nombre d'élément à sync : ".$response['size']." : traité= ".$nb_obj_done."\n";
			}

			if( sizeof( $response['records'] ) ){

				try{
					foreach ($response['records'] as $record) {

							$newrecord = sf_task_execute_before_queue($tsk_id, $record);
							if($newrecord != null ){
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_SAVE_ROW, array('tsk_id'=>$tsk_id, 'record'=>$newrecord));
							}

							$query_where['LastModifiedDate'] = 'LastModifiedDate >'.$record['LastModifiedDate'];

							// mise à jour de la date de modification à chaque élément fini pour lequel la date précédante est différente
							$current_last_date_obj = date('Y-m-d H:i:s', strtotime($record['LastModifiedDate']));

							// permet de ne mettre à jour la date localement que lorsque nous avons fini tous les éléments qui sont modifié sur la même heure. En cas d'erreur sur un élément on reprend à 0.
							if( $previous_last_date_obj!=$current_last_date_obj ){
								$previous_last_date_obj = $current_last_date_obj;
								if( $nb_obj_done ){ // le premier élément ne peut faire changer la date car on ne sais pas son précédent.
									$last_date_obj = $previous_last_date_obj;
								}
							}
							$nb_obj_done++;
							if( $response['done'] == true && $nb_obj_done == $response['size']){ // si la requete marque donne c'est qu'il n'y a plus d'élément à la suite et on peux considéré que la date est correcte.
								$last_date_obj = $current_last_date_obj;
							}
					}

				}catch(Exception $e){
					if( $last_date_obj != null ){
						tsk_activities_set( $tsk_id, $response['size'], $response['size']-$nb_obj_done, 0, $date_start, date('Y-m-d H:i:s'), $last_date_obj );
					}
					throw $e;
				}
			}

			if( $last_date_obj != null ){
				tsk_activities_set( $tsk_id, $response['size'], $response['size']-$nb_obj_done, 0, $date_start, date('Y-m-d H:i:s'), $last_date_obj );
			}

			// passage à la requete suivante
			if( $response['done'] != true ){
				$response = $config['sf_connexion']->queryMore($response['queryLocator']);
			}else{
				break; // fin du lot
			}
		}

	}catch(Exception $e){


		if( isset($config['sf_debug']) && $config['sf_debug'] ){
			print $e->getMessage();
		}

		if( $retry > 1 ){
			throw $e;
		}

		// parfois le temps de load chez SF est trop long donc on retry après une pause de 5s, on tente le retry qu'une fois
		sleep(5);
		$retry++;
		sf_task_execute($tsk_id, $retry);
	}

	// à la fin de toutes les synchro, on est obligé de recontroller les liaisons Bundle / produits car la mise à jour ou la suppression via SF marche pas bien et ne nous donnes pas l'information
	if( $tsk_id == TSK_PRD_LINKED_ADD ){
		$tmp_parent_id = array();
		$tmp_linked_key = array();

		// construction d'un tableau avec en clé src-dst en provnance de SF
		$query_table = sf_task_query_from(TSK_PRD_LINKED_ADD);
		$query_select = sf_task_query_select(TSK_PRD_LINKED_ADD);
		$query_where = sf_task_query_where(TSK_PRD_LINKED_ADD);
		$response = $config['sf_connexion']->queryAll(sf_soql_format($query_table, $query_select, $query_where, array()));
		$response = sf_soql_response_parse($response, $query_select);
		if( sizeof( $response['records'] ) ){
			foreach ($response['records'] as $record) {

				// récupère le produit source
				$src_id = prd_products_get_by_ref_gescom($record['Bundle__c']);
				if( !$src_id ){
					continue;
				}

				// récupère le produit destination
				$dst_id = prd_products_get_by_ref_gescom($record['Product__c']);
				if( !$dst_id ){
					continue;
				}

				$tmp_linked_key[$src_id."-".$dst_id] = 1;
				$tmp_parent_id[] = $src_id;
			}

			// récupération de tous les liens présent dans riashop sur les différents parents
			foreach( $tmp_parent_id as $parent_id ){
				$rchilds = prd_nomenclatures_products_get($parent_id);
				while( $child = ria_mysql_fetch_assoc($rchilds) ){
					if( !isset($tmp_linked_key[$parent_id."-".$child['id']]) ){ // si la laison est pas dans SalesForce on la bute
						prd_nomenclatures_products_del($parent_id, $child['id'], true);
					}
				}
			}
		}
	}

	// on rebuild les règles de restrictions avec toutes les possibilités
	if( $tsk_id == TSK_USER_ADD ){
		if( $config['tnt_id'] == 59 ){  // uniquement pour l'instance de DR Pour le moemnt

			// pour chaque valeur possible dans le champs LEGRAND_FLD_USR_CONTEXT
			$all_values = fld_fields_get_values( LEGRAND_FLD_USR_CONTEXT );
			if( $all_values ){
				while( $val = ria_mysql_fetch_assoc($all_values) ){

					// on vire les config actuelle
					$current_restrictions = prd_restrictions_get( false, false, null, false, false, false, array(
							'fld' => LEGRAND_FLD_USR_CONTEXT,
							'value' => $val['value']
						));
					if( $current_restrictions ){
						while( $r = ria_mysql_fetch_assoc($current_restrictions) ){
							prd_restrictions_del($r['id']);
						}
					}

					$split = explode('||', $val['value']);
					if( sizeof($split) == 2 ){
						prd_restrictions_add( LEGRAND_FLD_USR_CONTEXT, $val['value'], LEGRAND_FLD_PRD_PERIMETRE, $split[0], 'LIKE', 1, true );
						prd_restrictions_add( LEGRAND_FLD_USR_CONTEXT, $val['value'], LEGRAND_FLD_PRD_LINE_CLI, $split[1], 'LIKE', 1, true );
					}

				}
			}
		}

	}


}
/**
 * Cette fonction permet de faire des requetes complémentaires sur SF pour compléter les données avant de l'envoyer dans les workers.
 * Les workers ne peuvent pas ce connecter à SF, et doivent donc avoir toutes les datas dans leur tableaux avant.
 * @param $tsk_id Obligatoire, identifiant de la tâche
 * @param $record Obligatoire, enregistrement
 */
function sf_task_execute_before_queue($tsk_id, $record){
	global $config;

	// ajout dans la queue le traitement
	if( $tsk_id == TSK_PRODUCT_ADD ){
		// récupération des tarifs de ce produits
		$query_from = sf_task_query_from(TSK_PRD_PRC_ADD);
		$query_select = sf_task_query_select(TSK_PRD_PRC_ADD);
		$query_where = sf_task_query_where(TSK_PRD_PRC_ADD);
		$query_where[] = 'Product2Id=\''.$record['Id'].'\'';
		$response_price = $config['sf_connexion']->queryAll(sf_soql_format($query_from, $query_select, $query_where, array()));

		$record[TSK_PRD_PRC_ADD] = array();
		if( $response_price ){
			$response_price = sf_soql_response_parse($response_price, $query_select);

			if( isset($config['sf_debug']) && $config['sf_debug'] ){
				print "Ajout des tarifs sur le produits: ".$response_price['size']."\n";
			}

			$record[TSK_PRD_PRC_ADD] = $response_price['records'];
		}
	}
	else if( $tsk_id == TSK_PRD_PRC_ADD ){

		// on tests ici si le tarifs concerne une categ tarifaire diff de celle remonté.
		// si c'est le cas c'est que le tarifs doit être mise à jour au niveau de la liaison bundle > produit
		if( !in_array($record['Pricebook2Id'], array($config['salesforce_config']['Id_Prix_commando__c'], $config['salesforce_config']['Id_Prix_commando_distributeur__c'])) ){

			if( isset($config['sf_debug']) && $config['sf_debug'] ){
				print "Ajout des tarifs sur categ tarifaire non valide ( prix sur les linked ) prd:".$record['Product2Id']." prix: ".$record['Pricebook2Id']."\n";
			}

			// on récupère donc tous les link avec cette categ tarifaire
			$query_from = sf_task_query_from(TSK_PRD_LINKED_ADD);
			$query_select = sf_task_query_select(TSK_PRD_LINKED_ADD);
			$query_where = array();
			$query_where[] = 'Catalogue_Installateur__c=\''.$record['Pricebook2Id'].'\'';
			$query_where[] = 'Product__c=\''.$record['Product2Id'].'\'';
			$response_link = $config['sf_connexion']->queryAll(sf_soql_format($query_from, $query_select, $query_where, array()));

			if( $response_link ){
				$response_link = sf_soql_response_parse($response_link, $query_select);

				if( sizeof($response_link['records']) > 0 ){
					foreach ($response_link['records'] as $record) {
						$newrecord = sf_task_execute_before_queue(TSK_PRD_LINKED_ADD, $record);
						RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_SAVE_ROW, array('tsk_id'=>TSK_PRD_LINKED_ADD, 'record'=>$newrecord));
					}
				}
			}

			return null;
		}

	}
	else if( $tsk_id == TSK_PRD_LINKED_ADD ){

		// récupère le produit source
		$src_id = prd_products_get_by_ref_gescom($record['Bundle__c']);
		if( !$src_id ){

			// lancement de la tache de récupération du bundle
			$r = sf_get_row(TSK_PRD_PARENT_ADD, $record['Bundle__c']);
			if( $r ){
				$src_id = sf_save_row(TSK_PRD_PARENT_ADD, $r);
			}

			if( !$src_id && !$record['IsDeleted'] ){
				throw new Exception("Le produit source n'a pas été trouvé : ".print_r($record,true));
			}
		}

		// récupère le produit destination
		$dst_id = prd_products_get_by_ref_gescom($record['Product__c']);
		if( !$dst_id ){

			// lancement de la tache de récupération du bundle
			$r = sf_get_row(TSK_PRODUCT_ADD, $record['Product__c']);
			if( $r ){
				$dst_id = sf_save_row(TSK_PRODUCT_ADD, $r);
			}

			if( !$dst_id && !$record['IsDeleted'] ){
				throw new Exception("Le produit destination n'a pas été trouvé : ".print_r($record,true));
			}
		}

		// si tarifs sur la liaison on recupère le contenu du prix
		if( trim($record['Catalogue_Installateur__c']) != '' ){

			// récupération des tarifs de ce produits
			$query_from = sf_task_query_from(TSK_PRD_PRC_ADD);
			$query_select = sf_task_query_select(TSK_PRD_PRC_ADD);
			$query_where = array();
			$query_where[] = 'Pricebook2Id=\''.$record['Catalogue_Installateur__c'].'\'';
			$query_where[] = 'Product2Id=\''.$record['Product__c'].'\'';
			$response_price = $config['sf_connexion']->queryAll(sf_soql_format($query_from, $query_select, $query_where, array()));

			$record[TSK_PRD_PRC_ADD] = array();
			if( $response_price ){
				$response_price = sf_soql_response_parse($response_price, $query_select);

				if( isset($config['sf_debug']) && $config['sf_debug'] ){
					print "Ajout des tarifs sur le produits: ".$response_price['size']."\n";
				}

				$record[TSK_PRD_PRC_ADD] = $response_price['records'];
			}

		}
	}

	return $record;
}
/**
 * Cette fonction sert a merger les ciblages salesforce en une chaine de caractère pour l'utilisation dans une requête soql
 *
 * @return string
 */
function sf_merge_targets_config_for_query(){
	global $config;
	return implode(', ', array_reduce(array(
		$config['salesforce_config']['Id_Ciblage__c'],
		$config['salesforce_config']['Id_ciblage_distributeur__c'],
	), function($initial, $target){
		$sane = str_replace(array("'", ' '), '', $target);

		if( strstr($sane, ',') ){
			$values = explode(',', $sane);
		}else{
			$values = array($sane);
		}

		foreach ($values as $value) {
			$initial[] = "'". $value. "'";
		}

		return $initial;
	}, array()));
}

/** Cette fonctin permet la régénération de la hiérarchie des commercials / clients
 * plus utilié ??
 *
 */
function sf_tsk_relation_rebuild(){
	global $config;
	if( !isset($config['sf_ciblage_updated']) || sizeof($config['sf_ciblage_updated'])<=0 ){
		return;
	}

	if( isset($config['sf_debug']) && $config['sf_debug'] ){
		print 'sf_tsk_relation_rebuild: '.print_r($config['sf_ciblage_updated'],true)."\n";
	}

	$rseller = gu_users_get($config['sf_ciblage_updated'], '', '', PRF_SELLER);
	while( $s = ria_mysql_fetch_assoc($rseller) ){
		rel_relations_hierarchy_rebuild(REL_SELLER_HIERARCHY, array($s['id']));
	}

	// force la mise à jour des comptes liées
	gu_users_set_date_modified( $config['sf_ciblage_updated'], true );

	unset($config['sf_ciblage_updated']);
}

/** Cette fonction permet de retourner la liste des champs à récupérer pour une type de donnée
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@return array Un tableau des colonnes d'un select correspondant à la table sur saleforce
 */
function sf_task_query_select($tsk_id){
	global $config;

	$query_select = array();

	$query_select[] = 'Id'; // id salesforce
	$query_select[] = 'LastModifiedDate'; // date de modification

	if( !in_array($tsk_id, array(TSK_SELLER_ADD,TSK_SECTOR_ADD,TSK_USR_RELATIONS_DEL)) ){ // cas spé les user n'ont pas de date de suppression
		$query_select[] = 'IsDeleted'; // Si true le compte est supprimé
	}

	// préparation des données à récupérer
	switch ($tsk_id) {
		case TSK_CATEGORIE_ADD:
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Inactif__c'; // nom
			$query_select[] = 'BeginningDate__c'; // date de début
			$query_select[] = 'EndingDate__c'; // date de fin
			$query_select[] = 'Instance_Yuto__c'; // instance pour la publications
			$query_select[] = 'FR_line_of_customer__c';
			$query_select[] = 'Perimetre_Yuto__c';
			$query_select[] = 'FR_Disponibilite__c';
			break;
		case TSK_PRODUCT_INTERVENTION:
			$query_select[] = 'ProductCode'; // référence produit
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Description'; // description
			$query_select[] = 'Family'; // famille de produit ( liste de sélection côté salesforce )
			$query_select[] = 'IsActive'; // mise en sommeil?
			$query_select['url_img'] = 'FR_Url_Photo_Produit_Yuto__c'; // url de l'image
			$query_select['url_img_2'] = 'FR_Vignette_Yuto__c'; // url de l'image pour les interventions
			$query_select['col_qte'] = 'FR_Packaging__c'; // conditionnement
			$query_select['publish'] = 'FR_Moyen_Yuto__c'; // information de publication
			$query_select[] = 'FR_Means_Type__c'; // type de moyen pub
			break;
		case TSK_PRODUCT_ADD:
			$query_select[] = 'ProductCode'; // référence produit
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Description'; // description
			$query_select[] = 'Family'; // famille de produit ( liste de sélection côté salesforce )
			$query_select[] = 'IsActive'; // mise en sommeil?
			$query_select['url_img'] = 'FR_Url_Photo_Produit_Yuto__c'; // url de l'image
			$query_select['col_qte'] = 'FR_Packaging__c'; // conditionnement
			break;
		case TSK_PRD_PARENT_ADD:  // bundles
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Domaine__c'; // Domaine
			$query_select[] = 'Inactif__c'; // Actf ou non
			$query_select[] = 'Step__c'; // step
			$query_select['desc'] = 'FR_Description__c'; // description du bundle
			$query_select['url_img'] = 'FR_Url_Photo_Yuto__c'; // url de l'image
			$query_select['brd_name'] = 'FR_Distribution_Selective__c'; // Champs marque, spé en fonction du client je pense
			$query_select[] = '(select Product__r.ProductCode from Associations_bundle_produit__r where isDeleted=false)'; // ref sur produit enfant
			break;
		case TSK_PRD_LINKED_ADD: // liaison entre bundle et produit
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Bundle__c'; // Bundle
			$query_select[] = 'Product__c'; // Produit
			$query_select[] = 'Quantite__c'; // qte
			$query_select[] = 'Catalogue_Installateur__c'; // catalogue de prix à utiliser
			$query_select[] = 'Catalogue_de_Prix_Distributeur__c'; // catalogue de prix a utiliser pour les distribs
			break;
		case TSK_PRC_CAT_ADD:
			$query_select[] = 'Name'; // nom
			$query_select[] = 'FR_BeginsDate__c'; // date début
			$query_select[] = 'FR_EndDate__c'; // date fin
			break;
		case TSK_PRD_PRC_ADD: // prix par catégorie tarifaires
			$query_select[] = 'Name'; // nom
			$query_select[] = 'Pricebook2Id'; // id de la catégorie tarifaire
			$query_select[] = 'Product2Id'; // id du produit
			$query_select[] = 'UnitPrice'; // prix
			$query_select['date_start'] = 'Pricebook2.FR_BeginsDate__c'; // date début et de fin sur la catégorie tarifaire
			$query_select['date_end'] = 'Pricebook2.FR_EndDate__c'; // date début et de fin sur la catégorie tarifaire
			break;
		case TSK_RESELLER_ADD:
		case TSK_USER_ADD:
			$query_select[] = 'Lastname'; // prénom
			$query_select[] = 'Firstname'; // nom
			$query_select[] = 'Name'; // nom de la société
			$query_select[] = 'Phone'; // numéro de téléphone
			$query_select[] = 'Fax'; // numéro de fax
			$query_select[] = 'IsPersonAccount'; // détermine si c'est une personne ou non
			$query_select[] = 'Type'; // type du compte prospect, client, ..
			$query_select[] = 'Website'; // site internet du compte
			$query_select[] = 'FR_Account_Email__c'; // champs email pas la version standard Sf
			$query_select[] = 'BillingAddress'; //facturation
			$query_select[] = 'ShippingAddress'; //livraison
			$query_select[] = 'RecordTypeId'; //type d'enregistrement = profile
			$query_select[] = 'FR_RecordType_Name__c'; //nom du type d'enregistrement pour champs avancé ?
			$query_select[] = 'FR_Siret__c'; //numéro de siret
			$query_select[] = 'FR_State__c'; //status
			$query_select[] = 'FR_Leadformance__c'; //status EC
			$query_select[] = 'FR_Validity_Concurrence_Flag__c'; //Validité concurance legrand
			$query_select['brd_name'] = 'FR_Distribution_Selective__c'; // Champs marque, spé en fonction du client je pense

			// on ajoute tous les champs modifiables
			foreach( sf_fields_get() as $mdl_id => $flds ){
				foreach( $flds as $fld_id => $fld){
					$query_select[] = $fld['sf_name'];
				}
			}
			break;
		case TSK_USR_SELLER_ADD:
			$query_select[] = 'Owner.Id'; // id du représentant
			$query_select[] = 'FR_Acount__c'; // id du compte
			break;
		case TSK_USR_RELATIONS_ADD:
			$query_select[] = 'ObjectId'; // id du compte
			$query_select[] = 'Territory2Id'; // id du représentant
			break;
		case TSK_USR_RELATIONS_DEL:
			$query_select[] = 'UserId'; // id du compte
			$query_select[] = 'Territory2Id'; // id du représentant
			$query_select[] = 'IsActive';
			break;
		case TSK_SECTOR_ADD:
			$query_select[] = 'Name';
			$query_select[] = 'ParentTerritory2Id';
			break;
		case TSK_SELLER_ADD:
			$query_select[] = 'Lastname';
			$query_select[] = 'Firstname';
			$query_select[] = 'ProfileId';
			$query_select[] = 'Username';
			$query_select[] = 'Email';
			$query_select[] = 'IsActive';
			$query_select[] = 'FederationIdentifier';
			$query_select[] = 'FR_line_of_customer__c';
			$query_select[] = 'CompanyName';
			$query_select[] = 'Address'; //facturation
			$query_select[] = 'FR_Utilisateur_de_Yuto__c'; //permet de savoir si c'est un utilisateur yuto ou non
			$query_select[] = 'FR_Entity__c'; // périmètre ou ligne de clientèle
			break;
		case TSK_CONTACT_ADD:
			$query_table = 'Contact';
			$query_select[] = 'AccountId'; // référence du compte client
			$query_select[] = 'Account.Name'; // nom de société
			$query_select[] = 'Email'; // email
			$query_select[] = 'LastName'; // prénom
			$query_select[] = 'FirstName'; // nom
			$query_select[] = 'Title'; // fonction
			$query_select[] = 'MailingAddress'; // adresse
			$query_select[] = 'Phone'; //tel fixe
			$query_select[] = 'MobilePhone'; //tel mobil
			$query_select[] = 'Salutation'; // title du contact
			$query_select[] = 'FR_ID_User__r.Id'; // id du commando
			$query_select[] = 'FR_State__c'; // Statut
			$query_select[] = 'IsEmailBounced'; // IsEmailBounced bounced d'email
			break;
		case TSK_ORD_STATE:
			$query_select[] = 'Status';
			$query_select[] = 'OrderNumber';
			break;
		case TSK_CASE_STATE:
			$query_select[] = 'CaseNumber'; // piece de la demande
			$query_select[] = 'FR_Order_Status__c'; // status de la demande
			break;
		case TSK_CASE:
			$query_select[] = 'CaseNumber'; // piece de la demande
			$query_select[] = 'AccountId'; // référence du compte
			$query_select[] = 'ContactId'; // référence du contact
			$query_select[] = 'Description'; // description de la demande
			$query_select[] = 'FR_Delivery_Phone__c'; // numéro de téléphone
			$query_select[] = 'FR_Delivery_Account_Name__c'; //
			$query_select[] = 'FR_Delivery_Address_1__c'; // adresse 1 de livraison
			$query_select[] = 'FR_Delivery_Address_2__c'; // adresse 2 de livraison
			$query_select[] = 'FR_Delivery_Address_3__c'; // adresse 3 de livraison
			$query_select[] = 'FR_Delivery_ZipCode__c'; // code postal
			$query_select[] = 'FR_Delivery_City__c'; // ville
			$query_select[] = 'FR_Delivery_Country__c'; // pays
			$query_select[] = 'FR_Delivery_Date__c'; // date de livraison
			$query_select[] = 'FR_Case_Type_View__c'; // type de demande
			$query_select[] = 'FR_Order_Status__c'; // status de la demande
			$query_select[] = 'CreatedDate'; // date de création de la demande
			break;
		case TSK_CASE_PRODUCT:
			$query_select[] = 'FR_Case__c'; // l'identifiant gescom de la commande
			$query_select[] = 'FR_Case__r.CaseNumber'; // la piece de la commande
			$query_select[] = 'Name'; // l'identifiant de la ligne
			$query_select[] = 'FR_Product_Code__c'; // la référence du produit
			$query_select[] = 'FR_Product_Name__r.name'; // le nom du produit
			$query_select[] = 'FR_Quantity__c'; // quantité
			$query_select[] = 'FR_Net_Price__c'; // prix
			break;
		case TSK_LINEAR_RAISED:
			$query_select[] = 'Compte_Distributeur__c'; // l'identifiant gescom de la commande
			$query_select[] = 'Affiliation_un_Groupe__c'; // la piece de la commande
			$query_select[] = 'Commentaire__c '; // prix
			$query_select[] = 'Attachments '; // prix
			$query_select[] = 'CreatedById '; // prix
			break;
		case TSK_LINEAR_OFFERS:
			$query_select[] = 'Name';
			$query_select[] = 'FR_Releve_lineaire__c';
			$query_select[] = 'Hauteur__c';
			$query_select[] = 'Largeur__c';
			$query_select[] = 'Lineaire__c';
			$query_select[] = 'Metre_lineaire__c';
			$query_select[] = 'Nbre_d_elements__c';
			$query_select[] = 'Nbre_de_ref_presente__c';
			$query_select[] = 'Nbre_etagere__c';
			$query_select[] = 'Notes';
			break;
		case TSK_COLLECTION:
			$query_select[] = 'Name'; // Nom de la collection
			$query_select[] = 'Collection_Active__c'; // Si la collection est active ou non
			break;
		case TSK_COLLECTION_PRODUCTS:
			$query_select[] = 'Name'; // Nom du produit
			$query_select[] = 'FR_Collection__c'; // id de lq collection
			$query_select[] = 'FR_Collection__r.Name'; // no, de lq collection
			$query_select[] = 'Ref_Emblematique__c'; // ref emblématique
			$query_select[] = 'Marque__c'; // ref emblématique
			break;
	}

	// ajoute à la liste les champs avancé
	$advanced_fields = sf_task_query_select_advanced($tsk_id);
	foreach( $advanced_fields as $fld ){
		$query_select[] = (isset($fld['table']) ? $fld['table'].'.' : ''). $fld['name'];
	}

	return $query_select;
}

/** Cette fonction permet de retourner la liste des champs avancé à récupérer pour une type de donnée
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@return array Un tableau contenant les champs avancé dans saleforce lié à cette tâche
 */
function sf_task_query_select_advanced($tsk_id){

	$advanced_fields = array();

	// préparation des données à récupérer
	switch ($tsk_id) {
		case TSK_USER_ADD:
			$advanced_fields[] = array( 'name' => 'FR_Statut_EC_fr__c', 'fld' => 10082);  //status electricien
			$advanced_fields[] = array( 'name' => 'FR_Commentaires_Electriciencertifi__c', 'fld' => 10081);  //commentaire electricien
			$advanced_fields[] = array( 'table'=>'FR_Contact_Electriciencertifie_fr__r', 'name' => 'Name', 'fld' => 10076,); // nom du contact certifié
			break;
		case TSK_CONTACT_ADD:
			$advanced_fields[] = array( 'name' => 'FR_Contact_Yuto__c', 'fld' => 10140);
			$advanced_fields[] = array( 'name' => 'FR_Compte_cible__c', 'fld' => 10077);
			$advanced_fields[] = array( 'name' => 'FR_Compte_EC_fr__c', 'fld' => 10078);
			$advanced_fields[] = array( 'name' => 'FR_Partenaire_Particulier__c', 'fld' => 10079);
			break;
		case TSK_ORD_STATE:
			$advanced_fields[] = array( 'name' => 'FR_Type_de_Commande__c', 'fld' => 10139);
			break;
		case TSK_CASE:
		case TSK_CASE_ADD:
			$advanced_fields[] = array( 'name' => 'Priority', 'fld' => 101328);
			break;
	}

	return $advanced_fields;
}

/** Cette fonction permet de retourner la liste des champs à récupérer pour une type de donnée
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@return string Le nom de la table saleforce correspondant à la tâche
 */
function sf_task_query_from($tsk_id){

	$query_table = "";

	// préparation des données à récupérer
	switch ($tsk_id) {
		case TSK_CATEGORIE_ADD:
			$query_table = 'Domaine__c'; // semble être spé Legrand
			break;
		case TSK_PRODUCT_ADD:
		case TSK_PRODUCT_INTERVENTION:
			$query_table = 'Product2';
			break;
		case TSK_PRD_PARENT_ADD:  // bundles
			$query_table = 'Bundle__c';
			break;
		case TSK_PRD_LINKED_ADD: // liaison entre bundle et produit
			$query_table = 'Association_bundle_product__c';
			break;
		case TSK_PRC_CAT_ADD:
			$query_table = 'Pricebook2';
			break;
		case TSK_PRD_PRC_ADD: // prix par catégorie tarifaires
			$query_table = 'PricebookEntry';
			break;
		case TSK_SELLER_ADD:
			$query_table = 'User';
			break;
		case TSK_RESELLER_ADD:
		case TSK_USER_ADD:
			$query_table = 'Account';
			break;
		case TSK_SECTOR_ADD:
			$query_table = 'Territory2';
			break;
		case TSK_USR_RELATIONS_ADD:
			$query_table = 'ObjectTerritory2Association';
			break;
		case TSK_USR_RELATIONS_DEL:
			$query_table = 'UserTerritory2Association';
			break;
		case TSK_USR_SELLER_ADD:
			$query_table = 'FR_Targetting__c';
			break;
		case TSK_CONTACT_ADD:
			$query_table = 'Contact';
			break;
		case TSK_ORD_STATE:
			$query_table = 'Order';
			break;
		case TSK_CASE_STATE:
		case TSK_CASE:
			$query_table = 'Case';
			break;
		case TSK_CASE_PRODUCT:
			$query_table = 'FR_Suggested_Product_Solution__c';
			break;
		case TSK_LINEAR_RAISED:
			$query_table = 'Releves_Lineaires__c';
			break;
		case TSK_LINEAR_OFFERS:
			$query_table = 'Lineaire_releve__c';
			break;
		case TSK_COLLECTION:
			$query_table = 'Collection__c';
			break;
		case TSK_COLLECTION_PRODUCTS:
			$query_table = 'Lineaire__c';
			break;
	}

	return $query_table;
}

/** Cette fonction permet de retourner la liste des filtres pour un type de donnée
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@return Les conditions minimals d'un where liés à cette tâche
 */
function sf_task_query_where($tsk_id){
	global $config;

	$query_where = array();

	$ciblage_where = 'RecordTypeId in ('.sf_merge_targets_config_for_query().') and FR_Acount__r.RecordTypeId in ('.$config['salesforce_config']['Id_Type_Comptes__c'].') and Owner.IsActive=true and Owner.ProfileId in ('.$config['salesforce_config']['Profile_representant__c'].') and FR_Acount__r.FR_State__c in ('.$config['salesforce_config']['Compte_Status__c'].')';


	$date_archived = new DateTime();
	$date_archived->modify('-3 years');

	switch ($tsk_id) {
		case TSK_PRODUCT_INTERVENTION:
			$query_where[] = "Family='Moyens PUB'";
			$query_where[] = "FR_Advertising_Means_Sales__c=true";
			$query_where[] = "FR_Brand_Code__c='".$config['salesforce_config']['Id_marque_legrand__c']."'";
			break;
		case TSK_PRODUCT_ADD:
			$query_where[] = 'Id  in (select Product__c  from Association_bundle_product__c)'; // pour filter uniquement les produits dans les bundles
			break;
		case TSK_PRC_CAT_ADD:
			$query_where['IsActive'] = 'IsActive=true'; // pour filter uniquement les enregistrements âctif
			break;
		case TSK_USR_SELLER_ADD:
			//	$query_where[] = $ciblage_where;
			break;
		case TSK_SELLER_ADD:
		//	$query_where[] = 'ProfileId in ('.$config['salesforce_config']['Profile_representant__c'].') ';
			break;
		case TSK_RESELLER_ADD:
			$query_where[] = 'RecordTypeId in ('.$config['salesforce_config']['Profile_revendeurs__c'].')';
			break;
		case TSK_USER_ADD:
			$query_where[] = 'RecordTypeId not in ('.$config['salesforce_config']['Profile_revendeurs__c'].')';
			break;
		case TSK_CONTACT_ADD:
			break;
		case TSK_CASE_PRODUCT:
			$query_where[] = 'FR_Case__c in (select Id from Case where FR_Case_Type_View__c=\'Moyen publicitaire\' and CreatedDate > '.$date_archived->format('c').')';
			break;
		case TSK_CASE_STATE:
		case TSK_CASE:
			$query_where[] = "FR_Case_Type_View__c='Moyen publicitaire'";
			$query_where[] = "AccountId != ''";
			$query_where[] = "CreatedDate > ".$date_archived->format('c');

			$case_products = 'Id in (select FR_Case__c from FR_Suggested_Product_Solution__c where FR_Product_Name__r.Family=\'Moyens PUB\'and FR_Product_Name__r.FR_Advertising_Means_Sales__c=true and FR_Product_Name__r.FR_Brand_Code__c=\''.$config['salesforce_config']['Id_marque_legrand__c'].'\' and FR_Product_Name__r.IsDeleted=false)';
			$query_where[] = $case_products;

			break;
		case TSK_PRD_PRC_ADD: // prix par catégorie tarifaires

			$price_ids = array();
			$price_ids[] = $config['salesforce_config']['Id_Prix_commando__c'];
			$price_ids[] = $config['salesforce_config']['Id_Prix_commando_distributeur__c'];

			// attention il est possible ici de ne pas trouver de tarifs car le tarifs peut être mis sur la liaison parent / enfant.
			// et donc il n'a pas de prc_prices. Dans ce cas on fait une requete dans SalesForce pour trouver le bon catalogue de prix
			$res = $config['sf_connexion']->query('select Catalogue_Installateur__c from Association_bundle_product__c where Catalogue_Installateur__c !=\'\'');
			if( !sizeof($res->records) ){
				throw new Exception("Pas de tarifs trouvé pour inscrire la commande ou plusieurs liaisons : ".print_r($child, true));
			}

			foreach ($res->records as $record) {
				if( trim($record->Catalogue_Installateur__c)!='' ){
					$price_ids[] = $record->Catalogue_Installateur__c;
				}
			}
			$price_ids = array_unique($price_ids);

			$query_where[] = 'Pricebook2Id in (\''.implode('\',\'', $price_ids).'\')'; // catégorie tarifaire commando uniquement
			$query_where[] = 'Product2Id in (select Product__c from Association_bundle_product__c)'; // pour filter uniquement les produits dans les bundles
			break;
	}

	return $query_where;
}

/** Cette fonction permet de retourner pour un element si celui-ci est valide ou non
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@param $record Obligatoire, élément à supprimer
 * 	@return bool True ou False en fonction de si l'élément est éligible à une suppression
 */
function sf_task_record_deleted($tsk_id, $record){

	global $config;
	// if( !$config['sf_connexion'] ) return;

	if( isset($record['IsDeleted']) && $record['IsDeleted'] ){
		return true;
	}

	switch ($tsk_id) {
		case TSK_PRODUCT_ADD:
			break;
		case TSK_PRC_CAT_ADD:
			break;
		case TSK_USR_RELATIONS_DEL:
			return !$record['IsActive'];
			break;
		case TSK_SELLER_ADD:

			// si dispose d'un "ex" alors si va disparaitre un jour donc suppression
			if( strpos($record['LastName'], "EX-")!==false ){
				return true;
			}

			break;
		case TSK_RESELLER_ADD:

			// si le compte n'est pas dans un état
			$cnf = sf_config_parse($config['salesforce_config']['Compte_Status__c']);
			if( !in_array($record['FR_State__c'], $cnf) ){
				return true;
			}

			break;
		case TSK_USER_ADD:
			break;
		case TSK_CONTACT_ADD:
			// si le compte n'est pas dans les états données
			$cnf = sf_config_parse($config['salesforce_config']['Contact_Status__c']);
			if( !in_array($record['FR_State__c'], $cnf) ){
				return true;
			}
			break;
		case TSK_PRD_PRC_ADD: // prix par catégorie tarifaires
			break;
	}

	return false;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 client.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@param int $prf Optionnel, identifiant du profil du compte client (par défaut : client professionnel)
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id du compte traité si tous est bon.
 */
function sf_task_users($record, $prf=PRF_CUST_PRO){
	if( $record == null ) throw new Exception("sf_task_users record null");
	global $config;

	$usr_id = 0;
	$type_adr = 2;
	$can_login = false; // pour LG les client ne peuvent ce connecter donc otus les clients sont passé la dessus pour éviter au maximum des erreurs de mails

	// tente la récupération du client avec l'id, prend dans le tableau de donnée pour gagner en traitement si celui-ci est défini
	$rusr = gu_users_by_ref($record['Id']);

	// vérification de la suppression du compte
	$record['IsDeleted'] = sf_task_record_deleted($prf==PRF_RESELLER ? TSK_RESELLER_ADD:TSK_USER_ADD, $record);

	if( $rusr ){
		$usr_id = $rusr;

		// l'utilisateur existe, s'il est supprimé sur salesforce on lance la suppression localement
		if( $record['IsDeleted'] ){

			// supression du magasin liée
			if( $prf == PRF_RESELLER ){
				// tente de récupérer un magasin associé à son compte
				$str_ids = rel_relations_hierarchy_childs_get_ids(REL_RESELLER_STORE_HIERARCHY, $usr_id);

				if( $str_ids && sizeof($str_ids) ){

					// dans le cas de legrand on prend le premier magasin uniquement
					$rstr = dlv_stores_get($str_ids[0]);
					if( $rstr && ria_mysql_num_rows($rstr) ){
						$str = ria_mysql_fetch_assoc($rstr);

						// mise à jour du magasin
						if( !dlv_stores_del( $str['id'] ) ){
							throw new Exception("Erreur lors de la suppression du magasin associé.");
						}
					}
				}
			}

			//supression du compte
			if( !gu_users_del($usr_id, true) ){
				throw new Exception("Erreur lors de la suppression du compte client :".$usr_id);
			}

			return 0;
		}else{
			$rusr = gu_users_get($usr_id);
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_assoc($rusr);

				// sinon mise à jour des données
				if( $record['FR_Account_Email__c']!="" && $usr['email']!=$record['FR_Account_Email__c'] && gu_valid_email($record['FR_Account_Email__c'])){
					if( gu_users_get_doublon_email($usr_id, $record['FR_Account_Email__c'])==0 ){
						if( !gu_users_update_email($usr_id, $record['FR_Account_Email__c']) ){
							throw new Exception("Erreur lors de la mise à jour de l'email.".$usr_id." ".$record['FR_Account_Email__c']);
						}
					}
				}

				if( $usr['prf_id']!=$prf &&  !gu_users_set_profile( $usr_id, $prf, true ) ){
				 	throw new Exception("Erreur lors de la mise à jour du profile du compte client.".$usr_id);
				}

				if( $usr['ref']!=$record['Id'] && !gu_users_update_ref( $usr_id, $record['Id'] ) ){
					throw new Exception("Erreur lors de la mise à jour de la ref du compte client.".$usr_id);
				}
				if( $usr['can_login']!=$can_login && !gu_users_set_can_login( $usr_id, $can_login ) ){
					throw new Exception("Erreur lors de la mise à jour du can login sur compte client.".$usr_id);
				}

				$diff_adr_in_gescom = $record['Firstname'] != $usr['adr_firstname']
					|| $record['Lastname'] != $usr['adr_lastname']
					|| $record['Name'] != $usr['society']
					|| $record['FR_Siret__c'] != $usr['siret']
					|| $record['BillingAddress']['street'] != $usr['address1']
					|| $record['BillingAddress']['postalCode'] != $usr['zipcode']
					|| $record['BillingAddress']['city'] != $usr['city']
					|| $record['BillingAddress']['country'] != $usr['country']
					|| $record['Phone'] != $usr['phone']
					|| $record['Fax'] != $usr['fax'];

				if($diff_adr_in_gescom && !gu_adresses_update( $usr_id, $usr['adr_invoices'], $type_adr, null, $record['Firstname'], $record['Lastname'], $record['Name'], $record['FR_Siret__c'],
					$record['BillingAddress']['street'], '', $record['BillingAddress']['postalCode'], $record['BillingAddress']['city'], $record['BillingAddress']['country'], $record['Phone'], $record['Fax'], null, null, true ) ){
					throw new Exception("La mise à jour de l'adresse de facturation l'utilisateur a échoué : ".print_r($record, true));
				}
			}
		}

	}else{
		if( $record['IsDeleted'] ){
			return 0;
		}

		// création de l'utilisateur
		if( !trim($record['FR_Account_Email__c']) ){
			$usr_id = gu_users_add_without_email( gu_password_create(), $prf, $record['Id'], true, 0, 0, null, false, $can_login, $record['Id'], false );
		}else{
			$usr_id = gu_users_add( $record['FR_Account_Email__c'], gu_password_create(), $prf, $record['Id'], true, 0, 0, null, false, $can_login, 32, false, $record['Id'] );
		}

		if( $usr_id ){

			// ajoute le moyen de paiement par défaut à tous les comptes "en compte"
			gu_users_payment_types_add( $usr_id, _PAY_COMPTE, 0, 2, 0 );

			// ajout de l'adresse de facturation
			$adr = gu_adresses_add( $usr_id, $type_adr, null, $record['Firstname'], $record['Lastname'], $record['Name'], $record['FR_Siret__c'], $record['BillingAddress']['street'], '', $record['BillingAddress']['postalCode'], $record['BillingAddress']['city'], $record['BillingAddress']['country'], $record['Phone'], $record['Fax'], '', '' );
			if( !$adr ){
				gu_users_del($usr_id, true);
				throw new Exception("La création de l'adresse de facturation l'utilisateur a échoué : ".print_r($record, true));
			}else{
				gu_users_address_set($usr_id, $adr);
				$usr['id'] = $usr_id;
				$usr['adr_invoices'] = $adr;
				$usr['adr_delivery'] = $adr;
			}

		}else{
			throw new Exception("La création de l'utilisateur a échoué: ".print_r($record, true));
		}
	}

	if( $usr > 0 ){
		/* soucis sur scertain compte .. ?
		if(  !gu_users_set_website( $usr['id'], trim($record['Website']) && strlen($record['Website'])<69 ? $record['Website'] : '' ) ){
			throw new Exception("Erreur lors de la mise à jour du site web du compte client :".$usr['id'].' web='.$record['Website']);
		}
		*/
		$default_adress = array(
			'street' => "",
			'postalCode' => "",
			'city' => "",
			'country' => "",
		);

		fld_object_values_set($usr['id'], _FLD_USR_SLEEP, in_array($record['FR_State__c'], array('Client', 'Prospect', 'A Modifier')) ? 'Non' : 'Oui');

		$ShippingAddress = isset($record['ShippingAddress']) && is_array($record['ShippingAddress']) && !empty($record['ShippingAddress']) ? $record['ShippingAddress'] : $default_adress;
		$BillingAddress = isset($record['BillingAddress']) ? $record['BillingAddress'] : $default_adress;

		// mise à jour de l'adrese de livraison
		$diff_adr_in_gescom = $BillingAddress['street'] != $ShippingAddress['street']
			|| $BillingAddress['postalCode'] != $ShippingAddress['postalCode']
			|| $BillingAddress['city'] != $ShippingAddress['city']
			|| $BillingAddress['country'] != $ShippingAddress['country'];
		$diff_adr_in_ria = $usr['adr_invoices']!=$usr['adr_delivery'];

		if( $diff_adr_in_ria && !$diff_adr_in_gescom ){
			// suppression de l'adresse de livraison par défaut
			gu_users_address_delivery_set($usr['id'], null);
		}else if( !$diff_adr_in_ria && $diff_adr_in_gescom ){
			// ajout de l'adresse de livraison
			$adr_delivery = gu_adresses_add( $usr['id'], $type_adr, null, $record['Firstname'], $record['Lastname'], $record['Name'], $record['FR_Siret__c'], $ShippingAddress['street'], '', $ShippingAddress['postalCode'], $ShippingAddress['city'], $ShippingAddress['country'], $record['Phone'], $record['Fax'], '', '' );
			if( $adr_delivery ){
				gu_users_address_delivery_set($usr['id'], $adr_delivery);
			}
		}else if( !$diff_adr_in_ria && !$diff_adr_in_gescom ){

			$radr = gu_adresses_get( $usr['id'], $usr['adr_delivery'] );
			if( !$radr || !ria_mysql_num_rows($radr) ){
				throw new Exception("Erreur adresse introuvable :".$usr['adr_delivery']);
			}
			$adr = ria_mysql_fetch_assoc($radr);

			$need_update = $record['Firstname'] != $adr['firstname']
				|| $record['Lastname'] != $adr['lastname']
				|| $record['Name'] != $adr['society']
				|| $record['FR_Siret__c'] != $adr['siret']
				|| $ShippingAddress['street'] != $adr['address1']
				|| $ShippingAddress['postalCode'] != $adr['zipcode']
				|| $ShippingAddress['city'] != $adr['city']
				|| $ShippingAddress['country'] != $adr['country']
				|| $record['Phone'] != $adr['phone']
				|| $record['Fax'] != $adr['fax'];

			// ajout de l'adresse de livraison
			if( $need_update ){
				// ajout de l'adresse de livraison
				gu_adresses_update( $usr['id'], $usr['adr_delivery'], $type_adr, null, $record['Firstname'], $record['Lastname'], $record['Name'], $record['FR_Siret__c'], $ShippingAddress['street'], '', $ShippingAddress['postalCode'], $ShippingAddress['city'], $ShippingAddress['country'], $record['Phone'], $record['Fax'], '', '' );
			}
		}

		// récupère la catégorie comptable en fonction du nom
		$sf_cac_name = $record['FR_Leadformance__c'] ? 'EC' : 'Non EC';
		$cac_id = -1;
		$rcac = gu_accounting_categories_get( false, $sf_cac_name );
		if( $rcac && ria_mysql_num_rows($rcac) ){
			$cac = ria_mysql_fetch_assoc($rcac);
			$cac_id = $cac['id'];
		}

		if( $cac_id == -1 ){
			throw new Exception("Erreur cac comptable  introuvable :".$sf_cac_name);
		}

		// gestion de la catégorie comptable pour mettre des restrictions en place
		if( gu_users_get_accouting_category($usr['id']) != $cac_id ){
			gu_users_set_accouting_category($usr['id'], $cac_id);
		}

		// spé pour Legrand,
		// Les revendeurs doivent généré un magasin pour la gestion sur Yuto avec la notion de magasin
		if( $prf == PRF_RESELLER ){
			// tente de récupérer un magasin associé à son compte
			$str_ids = rel_relations_hierarchy_childs_get_ids(REL_RESELLER_STORE_HIERARCHY, $usr_id);

			if( $str_ids && sizeof($str_ids) ){

				// dans le cas de legrand on prend le premier magasin uniquement
				$rstr = dlv_stores_get($str_ids[0]);
				if( $rstr && ria_mysql_num_rows($rstr) ){
					$str = ria_mysql_fetch_assoc($rstr);

					$str_id = $str['id'];

					// mise à jour du magasin
					if( !dlv_stores_update( $str['id'], $record['Name'], $str['url'], $str['desc'], $ShippingAddress['street'], '', $ShippingAddress['postalCode'], $ShippingAddress['city'], $ShippingAddress['country'], $str['manager'], $record['Phone'], $record['Fax'], $record['FR_Account_Email__c'] ? $record['FR_Account_Email__c'] : $str['email'], $record['Website'] ? $record['Website'] : '', $str['allow_delivery'], $str['sct_id'], $str['publish'] ) ){

						throw new Exception("Erreur lors de la mise à jour du magasin associé.");
					}
				}

			}else{

				// création du magasin
				$str_id = dlv_stores_add($record['Name'], '', '', $ShippingAddress['street'], '', $ShippingAddress['postalCode'], $ShippingAddress['city'], $ShippingAddress['country'], '', $record['Phone'], $record['Fax'], $record['FR_Account_Email__c'], $record['Website'] ? $record['Website'] : '', true, 0, 0, true, true );
				if( !$str_id ){
					throw new Exception("Erreur lors de la création du magasin associé.");
				}

				// ajoute le lien entre le client et le magasin
				if( !rel_relations_add( $usr_id, $str_id, REL_RESELLER_STORE_HIERARCHY ) ){
					throw new Exception("Erreur lors de la relation entre le revendeur et le magasin  : ".$usr_id." : ".$str_id);
				}

			}

			// gestion de la marque
			$sf_brands = explode(';', $record['brd_name']);
			if( sizeof( $sf_brands ) == 0 ){
				dlv_stores_brands_del($str_id);
			}else{
				if( sizeof( $sf_brands )>1 ){ // si plus de 1 alors on prend toutes les marques possible en plus des autres
					$sf_brands[] = $record['brd_name'];
				}

				$str_ria_brd = array();
				$allbrands = dlv_stores_brands_get( $str_id );
				if( $allbrands && ria_mysql_num_rows($allbrands) ){
					while( $b = ria_mysql_fetch_assoc($allbrands) ){
						$str_ria_brd[$b['brd_id']] = $b['brd_id'];
					}
				}

				$tmp_val = array();

				foreach( $sf_brands as $b ){
					$brd_id = sf_brand_add($b);
					if( $brd_id == 0 ){
						continue;
					}

					$tmp_val[] = $brd_id;

					if( isset($str_ria_brd[$brd_id]) ){
						unset($str_ria_brd[$brd_id]);
					}

					if( !dlv_stores_brands_exists($str_id, $brd_id) && !dlv_stores_brands_add( $str_id, $brd_id ) ){
						throw new Exception("Erreur lors de l'affectation de la marque au magasin.");
					}
				}

				if( sizeof($str_ria_brd) ){
					foreach($str_ria_brd as $id){
						dlv_stores_brands_del($str_id, $id);
					}
				}

				// mise à jour de la marque dans le champs prévu
				fld_object_values_set($usr['id'], _FLD_USR_BRAND, implode(",", $tmp_val));
			}

		}

		// mise à jour du niveau de qualification pour le filtre
		$qualification = '';
		if( !$record['FR_Validity_Concurrence_Flag__c'] ){
			$qualification = 'Revoir validité Concurrence LG';
		}

		fld_object_values_set($usr['id'], LEGRAND_FLD_CNT_QUALIFICATION, $qualification);

		// mise à jour des champs pour l'éditions
		foreach( sf_fields_get() as $mdl_id => $flds ){
			foreach( $flds as $fld_id => $fld){

				if( $fld['fld_type'] == FLD_TYPE_SELECT ){

					$value = null;
					$rvalue = fld_restricted_values_get( 0, $fld_id, '', 0, $record[$fld['sf_name']]);
					if( $rvalue && ria_mysql_num_rows($rvalue) > 0 ){
						$value = ria_mysql_result($rvalue, 0, 'id');
					}
					fld_object_values_set($usr['id'], $fld_id, $value);

				}else if( $fld['fld_type'] == FLD_TYPE_SELECT_MULTIPLE ){

					$val_ids = array();
					$val_names = explode(',', $record[$fld['sf_name']]);
					foreach( $val_names as $val_name ){
						$rvalue = fld_restricted_values_get( 0, $fld_id, '', 0, trim($val_name));
						if( $rvalue && ria_mysql_num_rows($rvalue) > 0 ){
							$val_ids[] = ria_mysql_result($rvalue, 0, 'id');
						}
					}

					fld_object_values_set($usr['id'], $fld_id, implode(',',$val_ids));

				}else{

					fld_object_values_set($usr['id'], $fld_id,  $record[$fld['sf_name']]);

				}

			}
		}
	}

	return $usr_id;
}

/** Cette fonction permet la gestion du ciblage d'un compte à un commercial
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne true si tous est bon.
 */
function sf_tsk_update_sellers($record){
	if( $record == null ) throw new Exception("sf_tsk_update_sellers record null");
	if( trim($record['FR_Acount__c']) == '' ) return true; // possible que j'ai un ciblage sans compte
	if( trim($record['Owner']['Id']) == '' ) return true; // possible que j'ai un ciblage sans proprio
	global $config;

	// récupère le réprésentant
	$rseller = gu_users_by_ref($record['Owner']['Id']);
	if( !$rseller ){
		if( !$record['IsDeleted'] ){
			throw new RetryTaskException("Owner id non trouvé : ".$record['Owner']['Id']);
		}
	}

	// récupère le compte
	$rusr = gu_users_by_ref($record['FR_Acount__c']);
	if( !$rusr ){
		if( !$record['IsDeleted'] ){
			throw new RetryTaskException("FR_Acount__c non trouvé : ".$record['FR_Acount__c']);
		}
	}

	if( $record['IsDeleted'] ){
		// suppression de la relation
		$rrel = rel_relations_get( 0, null, null, REL_SELLER_HIERARCHY, $record['Id'] );
		if( $rrel && ria_mysql_num_rows($rrel) ){
			while( $r = ria_mysql_fetch_assoc($rrel) ){
				rel_relations_del($r['id']);
			}
		}
	}

	if( $record['IsDeleted'] ){
		return true;
	}

	if( !$rseller || $rseller === true ){
		return true;
	}

	if( !$rusr || $rusr === true ){
		return true;
	}

	// affectation du compte au commercial
	if( !rel_relations_add( $rseller, $rusr, REL_SELLER_HIERARCHY, $record['Id'], true ) ){
		throw new Exception("Erreur lors de l'affectation du comemrcial : usr=".$record['FR_Acount__c']."(".$rusr.") : commercial=".$record['Owner']->Id."(".$rseller.")");
	}

	return true;
}

/** Cette fonction permet la gestion des affectations d'un territoire
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne true si tous est bon.
 */
function sf_tsk_relations($record){
	if( $record == null ) throw new Exception("sf_tsk_relations record null");

	global $config;

	if( isset($record['ObjectId']) ){
		$parent_ref = $record['Territory2Id'];
		$child_ref = $record['ObjectId'];
	}else{
		$parent_ref = $record['UserId'];
		$child_ref = $record['Territory2Id'];
	}

	// récupère le réprésentant
	$rseller = gu_users_by_ref($parent_ref);
	if( !$rseller ){
		if( !$record['IsDeleted'] ){
			throw new RetryTaskException("parent_ref id non trouvé : ".$parent_ref);
		}
	}

	// récupère le compte
	$rusr = gu_users_by_ref($child_ref);
	if( !$rusr ){
		if( !$record['IsDeleted'] ){
			throw new RetryTaskException("child_ref non trouvé : ".$child_ref);
		}
	}

	if( $record['IsDeleted'] ){
		// suppression de la relation
		$rrel = rel_relations_get( 0, null, null, REL_SECTOR_HIERARCHY, $record['Id'] );
		if( $rrel && ria_mysql_num_rows($rrel) ){
			while( $r = ria_mysql_fetch_assoc($rrel) ){
				rel_relations_del($r['id']);
			}
		}
	}

	if( $record['IsDeleted'] ){
		return true;
	}

	if( !$rseller || $rseller === true ){
		return true;
	}

	if( !$rusr || $rusr === true ){
		return true;
	}

	// affectation du compte au commercial
	if( !rel_relations_add( $rseller, $rusr, REL_SECTOR_HIERARCHY, $record['Id'], true ) ){
		throw new Exception("Erreur lors de l'affectation du territoire : ".print_r($record, true));
	}


	return true;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour la création des territoires
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne true si tous est bon.
 */
function sf_tsk_sector($record){
	if( $record == null ) throw new Exception("sf_tsk_sector record null");
	global $config;

	$email = str_replace('@','-', $record['Id']).'@com'; // génération d'une mail "bidon" pour éviter des doublons

	// test si le compte du commercial existe
	$rseller = gu_users_by_ref($record['Id']);

	$is_deleted = sf_task_record_deleted(TSK_SECTOR_ADD, $record);

	if( $is_deleted  ){
		if( $rseller ){
			if(!gu_users_del($rseller)){
				throw new Exception("Erreur lors de la suppression du territoire : ".$email);
			}
		}
		return true;
	}

	$city = $country = $street = $postalCode = "";

	if( !$rseller ){

		// création du compte
		$seller_id = gu_users_add( $email, md5($record['Id']), PRF_SELLER, $record['Id'], true, 0, 0, null, false, true, 16, false, $record['Id'] );
		if( !$seller_id ){
			throw new Exception("Erreur lors de la création du territoire : ".print_r($record,true));
		}

		$adr_id = gu_adresses_add( $seller_id, 2, 1, '', '',  $record['Name'], '', $street, '', $postalCode, $city, $country, '', '', '', '' );
		if( !$adr_id ){
			throw new Exception("Erreur lors de la création de l'adresse du territoire : ".$email);
		}

		gu_users_address_set($seller_id, $adr_id);
		gu_users_set_seller_id($seller_id, $seller_id); // le seller_id est son id de compte
	}else{
		$seller_id = $rseller;

		$rusr = gu_users_get($seller_id);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);

			if( !gu_adresses_update( $seller_id, $usr['adr_invoices'], 2, 4, '', '', $record['Name'], '',
				$street, '', $postalCode, $city, $country, '', '', '', null, true) ){
				throw new Exception("La mise à jour de l'adresse de facturation du territoire a échoué : ".print_r($record, true));
			}

			// Mise à jour de l'ID de représentant si celui-ci est null
			$old_seller_id = gu_users_get_seller_id( $seller_id, true );
			if( !is_numeric($old_seller_id) || $old_seller_id <= 0 ){
				gu_users_set_seller_id($seller_id, $seller_id);
			}
		}else{
			throw new Exception("La récupération du territoire a échoué: ".print_r($record, true));
		}

	}

	// permet d'activer ou non des users.
	gu_users_set_can_login($seller_id, false);

	// gestion de la hierachie des territoires
	if( $record['ParentTerritory2Id'] != '' ){
		$parent_id = gu_users_by_ref($record['ParentTerritory2Id']);
		if( !$parent_id  ){
			throw new RetryTaskException("Le parent n'existe pas encore : ".print_r($record, true));
		}

		$rel_parent_ref = 'sector_parent_'.$record['Id'];
		$rrel = rel_relations_get( 0, null, null, REL_SECTOR_HIERARCHY, $rel_parent_ref);
		if( $rrel && ria_mysql_num_rows($rrel) ){
			while( $r = ria_mysql_fetch_assoc($rrel) ){
				rel_relations_del($r['id']);
			}
		}
		if( !rel_relations_add( $parent_id, $seller_id, REL_SECTOR_HIERARCHY, $rel_parent_ref, true ) ){
			throw new Exception("Erreur lors de l'affectation du territoire parent : ".print_r($record, true));
		}
	}

	return true;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour la liaison client / commercial.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne true si tous est bon.
 */
function sf_tsk_usr_sellers_add($record){
	if( $record == null ) throw new Exception("sf_tsk_usr_sellers_add record null");
	global $config;

	$email = str_replace('@','-', $record['Username']).'@exemple.com'; // génération d'une mail "bidon" pour éviter des doublons

	// test si le compte du commercial existe
	$rseller = gu_users_by_ref($record['Id']);

	$is_deleted = sf_task_record_deleted(TSK_SELLER_ADD, $record);

	if( $is_deleted  ){
		if( $rseller ){
			// TODO pour faire mieux ici il faudrait virer les comptes clients plus présent dans un autre ciblage
			if(!gu_users_del($rseller)){
				throw new Exception("Erreur lors de la suppression du commercial : ".$email);
			}
		}
		return true;
	}

	$city = isset($record['Address']) && isset($record['Address']['city']) ? $record['Address']['city'] : "";
	$country = isset($record['Address']) && isset($record['Address']['country']) ? $record['Address']['country'] : "";
	$street = isset($record['Address']) && isset($record['Address']['street']) ? $record['Address']['street'] : "";
	$postalCode = isset($record['Address']) && isset($record['Address']['postalCode']) ? $record['Address']['postalCode'] : "";

	if( !$rseller ){

		// création du compte
		$seller_id = gu_users_add( $email, md5($record['Id']), PRF_SELLER, $record['Id'], true, 0, 0, null, false, true, 16, false, $record['Id'] );
		if( !$seller_id ){
			throw new Exception("Erreur lors de la création du commercial : ".print_r($record,true));
		}

		$adr_id = gu_adresses_add( $seller_id, 1, 1, $record['FirstName'], $record['LastName'],  $record['CompanyName'], '', $street, '', $postalCode, $city, $country, '', '', '', '' );
		if( !$adr_id ){
			throw new Exception("Erreur lors de la création de l'adresse du commercial : ".$email);
		}

		gu_users_address_set($seller_id, $adr_id);
		gu_users_set_seller_id($seller_id, $seller_id); // le seller_id est son id de compte
	}else{
		$seller_id = $rseller;

		if( !gu_users_update( $seller_id, $email, md5($record['Id']), false ) ){
			throw new Exception("Erreur lors de la mise à jour du commercial : ".$email);
		}

		$rusr = gu_users_get($seller_id);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);

			if( !gu_adresses_update( $seller_id, $usr['adr_invoices'], 3, 4, $record['FirstName'], $record['LastName'], $record['CompanyName'], '',
				$street, '', $postalCode, $city, $country, '', '', '', null, true) ){
				throw new Exception("La mise à jour de l'adresse de facturation du commercial a échoué : ".print_r($record, true));
			}

			// Mise à jour de l'ID de représentant si celui-ci est null
			$old_seller_id = gu_users_get_seller_id( $seller_id, true );
			if( !is_numeric($old_seller_id) || $old_seller_id <= 0 ){
				gu_users_set_seller_id($seller_id, $seller_id);
			}
		}else{
			throw new Exception("La récupération du commercial a échoué: ".print_r($record, true));
		}

	}

	// prend en compte le champs permettant d'activer le compte pour yuto ou non
	$record['IsActive'] = $record['FR_Utilisateur_de_Yuto__c'];

	// permet d'activer ou non des users.
	gu_users_set_can_login($seller_id, $record['IsActive']);

	if( $record['FR_line_of_customer__c'] != '' ){
		$r_pfl = prw_followed_lists::get();
		if( ria_mysql_num_rows($r_pfl) ){
			while( $pfl = ria_mysql_fetch_assoc($r_pfl) ) {
				if( $record['IsActive'] ){
					prw_followed_users::add($pfl['id'], PRF_SELLER, $seller_id);
				}else{
					prw_followed_users::delete($pfl['id'], $seller_id);
				}
			}
		}
	}

	// mise à jour de la ligne de clientèle sur le compte
	fld_object_values_set($seller_id, LEGRAND_FLD_USR_CONTEXT, $record['FR_Entity__c'] . ' || '.$record['FR_line_of_customer__c']);

	return true;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 contact.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id du compte traité si tous est bon.
 */
function sf_task_contact($record){
	if( $record == null ) throw new Exception("sf_task_contact record null");

	$usr_id = 0;

	$type_adr = 3;
	$prf = PRF_CONTACT;
	$title_id = 4;
	switch($record['Salutation']){
		case 'Madame' : $title = 2; break;
		case 'Monsieur' : $title = 1; break;
	}

	// tente la récupération du compte principale
	$rparent = gu_users_by_ref( $record['AccountId'] );

	if( !$rparent ){

		// récupère le compte
		if( !$record['IsDeleted'] ){
			throw new RetryTaskException("AccountId non trouvé : ".$record['AccountId']);
		}

		if( !$rparent ){
			return $usr_id; // dans le cas ou on ne trouve pas le compte principal on reste tolérent et on considére que le contact est bien ajouté pour ne pas bloqué la suite.
		}
	}

	// tente la récupération du client avec l'id
	$usr_id = gu_users_by_ref($record['Id']);
	if( $usr_id ){

		// l'utilisateur existe, s'il est supprimé sur salesforce on lance la suppression localement
		if( $record['IsDeleted'] ){
			if( !gu_users_del($usr_id, true) ){
				throw new Exception("Erreur lors de la suppression du contact :".$usr_id);
			}
			return 0;
		}

	}else{
		if( $record['IsDeleted'] ){
			return 0;
		}

		// création du contact
		if( trim($record['Email'])=="" ){
			$usr_id = gu_users_add_without_email( gu_password_create(), $prf, $record['Id'], true, 0, 0, $rparent, false, false, 16, false);
		}else{
			$usr_id = gu_users_add( $record['Email'], gu_password_create(), $prf, $record['Id'], true, 0, 0, $rparent, false, false, 16, false);
		}

	}

	if( $usr_id ){
		$rusr = gu_users_get($usr_id);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);
		}else{
			throw new Exception("La récupération du contact a échoué: ".print_r($record, true));
		}
	}else{
		throw new Exception("La création du contact a échoué: ".print_r($record, true));
	}

	fld_object_values_set($usr['id'], _FLD_USR_SLEEP, $record['FR_State__c'] == 'Actif' ? 'Non' : 'Oui');
	//mise Ã our du parent
	if( $usr['parent_id'] != $rparent ){
		gu_users_set_parent_id( $usr['id'], $rparent );
	}

	$city = isset($record['MailingAddress']) && isset($record['MailingAddress']['city']) ? $record['MailingAddress']['city'] : "";
	$country = isset($record['MailingAddress']) && isset($record['MailingAddress']['country']) ? $record['MailingAddress']['country'] : "";
	$street = isset($record['MailingAddress']) && isset($record['MailingAddress']['street']) ? $record['MailingAddress']['street'] : "";
	$postalCode = isset($record['MailingAddress']) && isset($record['MailingAddress']['postalCode']) ? $record['MailingAddress']['postalCode'] : "";
	$account_name = isset($record['Account']) && isset($record['Account']['Name']) ? $record['Account']['Name'] : "";

	// mise à jour de l'adresse facturation
	if( !$usr['adr_invoices'] ){
		// ajout de l'adresse de facturation
		$adr = gu_adresses_add( $usr['id'], $type_adr, $title_id, $record['FirstName'], $record['LastName'], $account_name, '', $street, '', $postalCode, $city, $country, $record['Phone'], '', $record['MobilePhone'], '', $record['Title'] );

		if( !$adr ){
			gu_users_del($usr['id'], true);
			throw new Exception("La création de l'adresse de facturation du contact a échoué : ".print_r($record, true));
		}else{
			gu_users_address_set($usr['id'], $adr);
		}
	}else{

		if( $usr['type_id'] != $type_adr
		|| $usr['title_id'] != $title_id
		|| $usr['adr_firstname'] != $record['FirstName']
		|| $usr['adr_lastname'] != $record['LastName']
		|| $usr['society'] != $account_name
		|| $usr['address1'] != $street
		|| $usr['zipcode'] != $postalCode
		|| $usr['city'] != $city
		|| $usr['country'] != $country
		|| $usr['phone'] != $record['Phone']
		|| $usr['mobile'] != $record['MobilePhone']
		|| $usr['adr_desc'] != $record['Title']
		 ){

			if( !gu_adresses_update( $usr['id'], $usr['adr_invoices'], $type_adr, $title_id, $record['FirstName'], $record['LastName'], $account_name, '',
				$street, '', $postalCode, $city, $country, $record['Phone'], '', $record['MobilePhone'], null, true, $record['Title']) ){
				throw new Exception("La mise à jour de l'adresse de facturation du contact a échoué : ".print_r($record, true));
			}
		}

	}

	// mise à jour de l'emial
	if( $usr['email'] != $record['Email'] && gu_valid_email($record['Email']) ){
		if( !gu_users_update_email($usr_id, $record['Email']) ){
			throw new Exception("Erreur lors de la mise à jour de l'email sur un contact: ".print_r($record, true));
		}
	}

	// mise à jour du représentant
	$seller = isset($record['FR_ID_User__r']) ? trim($record['FR_ID_User__r']['Id']) : false;
	if( !$seller  ){
		if( $usr['seller_id'] ){
			gu_users_set_seller_id($usr['id'], false);
		}
	}else{
		// tente la récupérération du compte localement
		$seller_usr_id = gu_users_by_ref($seller);
		if( $seller_usr_id && $usr['seller_id']!=$seller_usr_id){
			gu_users_set_seller_id($usr['id'], $seller_usr_id);
		}else if($usr['seller_id'] && !$seller_usr_id){
			gu_users_set_seller_id($usr['id'], false);
		}
	}


	// mise à jour de la qualification du contact :
	$qualification = '';

	if( $record['IsEmailBounced'] ){
		$qualification = 'Email invalide.';
	}else if( !trim($record['Email']) ){
		$qualification = 'Email vide.';
	}else if( !trim($record['Phone']) && !trim($record['MobilePhone']) ){
		$qualification = 'Téléphone vide.';
	}

	fld_object_values_set($usr['id'], LEGRAND_FLD_CNT_QUALIFICATION, $qualification);

	return $usr_id;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 catégorie.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id de la catégorie traité si tous est bon.
 */
function sf_task_categories($record){
	if( $record == null ) throw new Exception("sf_task_categories record null");
	global $config;

	$cat_id = 0;

	$publish = !$record['Inactif__c'] && $record['FR_Disponibilite__c'] == 'Yuto' ;

	// test en fonction de l'instance pour savoir si la categorie est publié ou non
	if( $publish ){
		switch (trim($record['Instance_Yuto__c'])) {
			case 'Commando':
				if( $config['tnt_id'] != 105 ){
					$publish = false;
				}
				break;
			case 'Multi-marques':
				if( $config['tnt_id'] != 104 ){
					$publish = false;
				}
				break;
			case 'DR':
				if( $config['tnt_id'] != 59 ){
					$publish = false;
				}
				break;
			default: // par défaut une valeur dans le champs instance_yuto est nécessaire
				$publish = false;
				break;
		}
	}

	// tente la récupération de la catégorie par le nom
	$rcat = prd_categories_get( 0, false, $config['sync_default_category'] ? $config['sync_default_category']: 0, '', false, true, null, false, array(), false, false, false, false, false, false, false, false, false, $record['Id']);
	if( $rcat && ria_mysql_num_rows($rcat) ){
		$cat = ria_mysql_fetch_assoc($rcat);
		$cat_id = $cat['id'];

		if( $record['IsDeleted'] ){
			if( !prd_categories_del( $cat_id, false, true ) ){
				throw new Exception("Erreur lors de la suppression de la catégorie");
			}
			return 0;
		}else{

			if( $record['Name'] != $cat['name'] ){
				// mise à jour de la catégorie
				if( !prd_categories_update( $cat['id'], $record['Name'], $cat['title'], $cat['desc'], $config['sync_default_category'] ? $config['sync_default_category'] : 0, $cat['publish']) ){
					throw new Exception("Erreur lors de la mise à jour de la catégorie");
				}
			}

			if( !$publish && $cat['publish'] ){
				if( !prd_categories_unpublish($cat_id) ){
					throw new Exception("Erreur lors de la dépublication de la catégorie");
				}
			}

		}

	}elseif(!$record['IsDeleted']){
		// ajout de la catégorie
		if( !($cat_id = prd_categories_add( $record['Name'], '', '', $config['sync_default_category'] ? $config['sync_default_category'] : 0, $publish, true, $record['Id'])) ){
			throw new Exception("Erreur lors de l'ajout de la catégorie");
		}
	}

	if( $cat_id > 0 && $publish ){

		$rcat = prd_categories_get( $cat_id );
		if( $rcat && ria_mysql_num_rows($rcat) ){
			$cat = ria_mysql_fetch_assoc($rcat);

			$date_from = isdateheure($record['BeginningDate__c']) ?  strtotime(dateheureparse($record['BeginningDate__c'])) : false;
			$date_to = isdateheure($record['EndingDate__c']) ?  strtotime(dateheureparse($record['EndingDate__c'])) : false;

			$need_publish = false;

			// check la date de début et de fin pour la notion de publication
			if( $date_from && $date_to && $date_from < time() && $date_to > time() ){
				$need_publish = true;
			}
			if( $date_from && !$date_to && $date_from < time() ){
				$need_publish = true;
			}
			if( !$date_from && $date_to && $date_to > time() ){
				$need_publish = true;
			}

			// mise à jour de la catégorie
			if( $need_publish ){
				if( !prd_categories_publish($cat_id) ){
					throw new Exception("Erreur lors de la publication de la catégorie");
				}
			}else{
				if( !prd_categories_unpublish($cat_id) ){
					throw new Exception("Erreur lors de la dépublication de la catégorie");
				}
			}
		}
	}

	// mise à jour de la ligne de clientèle pour les catégories
	fld_object_values_set($cat_id, LEGRAND_FLD_PRD_LINE_CLI, $record['FR_line_of_customer__c']);

	// mise à jour du périmètre ( entité )
	fld_object_values_set($cat_id, LEGRAND_FLD_PRD_PERIMETRE, $record['Perimetre_Yuto__c']);

	return $cat_id;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 produit.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param array $record Obligatoire object de type stdClass en provenance de l'api Soap
 *	@param boolean $intervention Facultatif true si c'est une intervention, stockage dans les catégories correspondante, false pour le traitement normal.
 *	@return bool Retourne l'id du produit traité si tous est bon.
 *  @throws Exception Lance une exception dans le cas d'une erreur.
 */
function sf_task_products($record, $intervention=false){
	if( $record == null ) throw new Exception("sf_task_products record null");
	global $config;

	$prd_id = 0;
	$brd_id = 0;

	$publish = $record['IsActive']; // les produits sont toujours publié $record['IsActive'];
	$tva_rate = _TVA_RATE_DEFAULT; // voir à l'avenir comment le récupérer

	if( $publish && $intervention ){ // dans le cas des produits intervnetions la publication est géré par une case à coché dédié
		$publish = $record['publish'];
	}

	// tente la récupération du produit
	$prd_id = prd_products_get_by_ref_gescom($record['Id']);
	if( $prd_id ){

		if( $record['IsDeleted'] ){
			if( !prd_products_del( $prd_id, true ) ){
				throw new Exception("Erreur lors de la suppression du produit".print_r($record,true));
			}
			return 0;
		}else{
			// mise à jour
			if( !prd_products_update( $prd_id, $record['ProductCode'], $record['Name'], $record['Description'], $brd_id, $publish, 0, 0, 0, 0, '' ) ){
				throw new Exception("Erreur lors de la mise à jour du produit".print_r($record,true));
			}
		}

	}elseif(!$record['IsDeleted']){
		// ajout de la catégorie
		if( !($prd_id = prd_products_add( $record['ProductCode'], $record['Name'], $record['Description'], $brd_id, $publish, 0, 0, 0, 0, '', true)) ){
			throw new Exception("Erreur lors de l'ajout du produit".print_r($record,true));
		}
		// ajout de la réf gescom
		if( !prd_products_set_ref_gescom($prd_id, $record['Id'], true) ){
			throw new Exception("Erreur lors de l'ajout de la ref gescom du produit".print_r($record,true));
		}
	}

	// action générique après upd ou add
	if( !$record['IsDeleted']){
		if( $intervention ){
			if( !isset($config['cat_root_intervention']) || !is_numeric($config['cat_root_intervention']) ){
				throw new Exception("config [cat_root_intervention] must be set");
			}
			if( !prd_categories_exists($config['cat_root_intervention']) ){
				throw new Exception("cat_root_intervention doesn't exists, it must be created first");
			}
			if( in_array($record['FR_Means_Type__c'], array('Autres PLV', 'Documentation')) ){
				if( $record['FR_Means_Type__c'] == 'Autres PLV' ){
					$cat_name = 'Autre PLV';
					$r_cat_plv = prd_categories_get(0, false, $config['cat_root_intervention'], $cat_name);
					if( !ria_mysql_num_rows($r_cat_plv) ){
						$cat_plv_id = prd_categories_add($cat_name, '', '', $config['cat_root_intervention']);
					}else{
						$cat_plv = ria_mysql_fetch_assoc($r_cat_plv);
						$cat_plv_id = $cat_plv['id'];
					}
					if( !prd_products_add_to_cat($prd_id, $cat_plv_id, true) ){
						throw new Exception("Erreur lors de l'ajout du produit dans la catégorie Autres PLV : ".print_r($record,true));
					}
				}else{ // documentation
					$cat_name = 'Documentation';
					$r_cat_cod = prd_categories_get(0, false, $config['cat_root_intervention'], $cat_name);
					if( !ria_mysql_num_rows($r_cat_cod) ){
						$cat_cod_id = prd_categories_add($cat_name, '', '', $config['cat_root_intervention']);
					}else{
						$cat_cod = ria_mysql_fetch_assoc($r_cat_cod);
						$cat_cod_id = $cat_cod['id'];
					}
					if( !prd_products_add_to_cat($prd_id, $cat_cod_id, true) ){
						throw new Exception("Erreur lors de l'ajout du produit dans la catégorie documentation : ".print_r($record,true));
					}
				}
			}
		}

		// récupèréation de l'image du produits et chargement local
		$has_img = false;
		$img_key = array('url_img','url_img_2');
		foreach($img_key as $key ){
			if( isset($record[$key]) && $record[$key] != '' ){
				$has_img = true;

				$img_content = file_get_contents($record[$key]);
				if( $img_content ){
					$tmpfname = tempnam(sys_get_temp_dir(), 'SFIMG');
					file_put_contents($tmpfname, $img_content);
					prd_images_main_del($prd_id);
					prd_images_del($prd_id);
					if( !prd_images_main_add( $prd_id, $tmpfname ) ){
						throw new Exception("Erreur lors de l'ajout de l'image : ".print_r($record,true));
					}
				}
			}
		}
		if( !$has_img ){
			prd_images_main_del($prd_id);
			prd_images_del($prd_id);
		}

		// ajout de la tva sur le produit
		if( !prc_tvas_add( $tva_rate, $prd_id, 0, true ) ){
			throw new Exception("Erreur de mise à jour de la tva ".print_r($record,true));
		}

		{
			// passe le produit en non suivi en stock
			prd_products_set_follow_stock( $prd_id, false );

			// passe le produit en commandable que comme article liée
			prd_products_set_childonly( $prd_id, !$intervention );

			// tarifs
			if( isset($record[TSK_PRD_PRC_ADD]) && !empty($record[TSK_PRD_PRC_ADD]) ){
				foreach ($record[TSK_PRD_PRC_ADD] as $price) {
					sf_save_row(TSK_PRD_PRC_ADD, $price);
				}
			}
		}
	}

	return $prd_id;
}

/**
 * Cette fonction permet d'ajouter un produit de type intervention
 *
 * @param array $record Obligatoire object de type stdClass en provenance de l'api Soap
 * @return bool Retourne l'id du produit traité si tous est bon.
 * @throws Exception Lance une exception dans le cas d'une erreur.
 */
function sf_task_products_intervention($record){
	return sf_task_products($record, true);
}
/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 produit de type article liée.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id du produit ajouté si tous est bon.
 */
function sf_task_prd_parent($record){
	if( $record == null ) throw new Exception("sf_task_prd_parent record null");
	global $config;

	$prd_id = 0;

	// gestion de la marque
	$brd_id = sf_brand_add($record['brd_name']);

	$publish = !$record['Inactif__c'];
	$tva_rate = _TVA_RATE_DEFAULT; // voir à l'avenir comment le récupérer

	$prd_ref = $record['Id']; // spé legrand: concataine les refs des produits enfants
	if( isset($record['Associations_bundle_produit__r']['records']) && sizeof($record['Associations_bundle_produit__r']['records']) ){
		$prd_ref = "";
		foreach( $record['Associations_bundle_produit__r']['records'] as $p ){
			$prd_ref .= $p['Product__r']['ProductCode'];
		}
	}

	// tente la récupération du produit
	$prd_id = prd_products_get_by_ref_gescom($record['Id']);
	if( $prd_id ){

		if( $record['IsDeleted'] ){
			if( !prd_products_del( $prd_id, true ) ){
				throw new Exception("Erreur lors de la suppression du produit parent".print_r($record,true));
			}
			return 0;
		}else{
			// mise à jour
			if( !prd_products_update( $prd_id, $prd_ref, $record['Name'], $record['desc'], 0, $publish, 0, 0, 0, 0, '' ) ){
				throw new Exception("Erreur lors de la mise à jour du produit parent".print_r($record,true));
			}
		}

	}elseif(!$record['IsDeleted']){
		// ajout de la catégorie
		if( !($prd_id = prd_products_add( $prd_ref, $record['Name'], $record['desc'], 0, $publish, 0, 0, 0, 0, '', true)) ){
			throw new Exception("Erreur lors de l'ajout du produit parent".print_r($record,true));
		}
		// ajout de la réf gescom
		if( !prd_products_set_ref_gescom($prd_id, $record['Id'], true) ){
			throw new Exception("Erreur lors de l'ajout de la ref gescom du produit parent".print_r($record,true));
		}
	}

	if(!$record['IsDeleted']){
		// ajout du type de nomenclature
		if( prd_products_get_nomenclature_type($prd_id) != NM_TYP_LINKED && !prd_products_set_nomenclature_type($prd_id, NM_TYP_LINKED) ){
			throw new Exception("Erreur lors de la mise à jour du type de nomenclature ".print_r($record,true));
		}

		// Spé  Legrand
		{
			$cat_child_id = 0;

			// récupréation des totes les sous catégories pour supprimer tous les classements synchronisé
			$catchilds = prd_categories_childs_get_array($config['sync_default_category'] ? $config['sync_default_category']: 0);

			// retire le classement du produit
			prd_products_del_from_cat( $prd_id, $catchilds, true);

			// récupère le catégorie du domaine
			if( trim($record['Domaine__c']) ){
				$rcat = prd_categories_get( 0, false, $config['sync_default_category'] ? $config['sync_default_category']: 0, '', false, true, null, false, array(), false, false, false, false, false, false, false, false, false, $record['Domaine__c']);
				if( !$rcat || !ria_mysql_num_rows($rcat) ){
					throw new Exception("La catégorie domaine n'est pas encore créé : ".print_r($record,true));
				}
				$cat = ria_mysql_fetch_assoc($rcat);

				// tente la récupration de la catégorie de second niveau
				if( trim($record['Step__c']) == '' ){
					$cat_child_id = $cat['id'];
				}else{
					$rcatchild = prd_categories_get( 0, false, $cat['id'], $record['Step__c'], false, true, null, false, array(), false, false, false, false, false, false, false, false, false);
					if( !$rcatchild || !ria_mysql_num_rows($rcatchild) ){
						$cat_child_id = prd_categories_add( $record['Step__c'], '', '', $cat['id'], true, true );
						if( !$cat_child_id ){
							throw new Exception("Erreur lors de la création de la catégorie sous domaine (step) : ".print_r($record,true));
						}
					}else{
						$catchild = ria_mysql_fetch_assoc($rcatchild);
						$cat_child_id = $catchild['id'];
					}
				}

				// classement du produit en fonction du domaine
				if( $cat_child_id ){
					$rcls = prd_classify_get( true, $prd_id, $cat_child_id );
					if( !$rcls || !ria_mysql_num_rows($rcls) ){
						// ajout du classement
						if( !prd_products_add_to_cat( $prd_id, $cat_child_id, true ) ){
							throw new Exception("Le classement du produit n'est pas réussi : ".print_r($record,true));
						}
					}
				}
			}

			// passe le produit en non suivi en stock
			prd_products_set_follow_stock( $prd_id, false );

			// gestion de l'url de l'image
			if( $record['url_img'] != '' ){
				$img_content = file_get_contents($record['url_img']);
				if( $img_content ){
					$tmpfname = tempnam(sys_get_temp_dir(), 'SFIMG');
					file_put_contents($tmpfname, $img_content);
					prd_images_main_del($prd_id);
					prd_images_del($prd_id);
					if( !prd_images_main_add( $prd_id, $tmpfname ) ){
						throw new Exception("Erreur lors de l'ajout de l'image : ".print_r($record,true));
					}
				}
			}else{
				prd_images_main_del($prd_id);
				prd_images_del($prd_id);
			}
		}
	}

	return $prd_id;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour la liaison entre les produits de type articles liées
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou true si tous est bon.
 */
function sf_task_linked($record){
	if( $record == null ) throw new Exception("sf_task_linked record null");
	global $config;

	$src_id = $dst_id = 0;
	$price_ht = null;

	if(!$record['Quantite__c']) $record['Quantite__c'] = 0;
	if(!$record['Product__c']) return 0;

	// récupère le produit source
	$src_id = prd_products_get_by_ref_gescom($record['Bundle__c']);
	if( !$src_id ){
		if( !$src_id && !$record['IsDeleted'] ){
			throw new Exception("Le produit source n'a pas été trouvé : ".print_r($record,true));
		}
	}
	// récupère le produit destination
	$dst_id = prd_products_get_by_ref_gescom($record['Product__c']);
	if( !$dst_id ){
		if( !$dst_id && !$record['IsDeleted'] ){
			throw new Exception("Le produit destination n'a pas été trouvé : ".print_r($record,true));
		}
	}

	if( !$src_id || !$dst_id ) return 0;
	if( $record['IsDeleted'] || $record['Quantite__c'] <= 0 ){

		if( !prd_nomenclatures_products_del( $src_id, $dst_id, true ) ){
			throw new Exception("Erreur lors de la suppression de la relation article liée : ".print_r($record, true));
		}
		return 0;

	}else{

		// tarifs
		if( isset($record[TSK_PRD_PRC_ADD]) && !empty($record[TSK_PRD_PRC_ADD]) ){
			foreach ($record[TSK_PRD_PRC_ADD] as $price) {
				if( !$price['IsDeleted'] && ($price['date_start']== '' || strtotime($price['date_start']) > time()) && ($price['date_end']=='' || strtotime($price['date_end']) < time()) ){ // test si le tarifs est actif
					$price_ht = $price['UnitPrice'];
					break;
				}
			}
		}

		// mise à jour de la catégorie
		if( !prd_nomenclatures_products_add( $src_id, array(array('prd'=>$dst_id, 'qte'=>$record['Quantite__c'], 'price_ht'=>$price_ht)), true) ){
			throw new Exception("Erreur lors de l'ajout de la relation article liée : ".print_r($record, true).' prix='.$price_ht.' src_id='.$src_id.' dst_id='.$dst_id);
		}

		// passe le produit dans la catégorie Salesforces linked
		prd_products_add_to_cat($dst_id, $config['sync_default_catalog'], true);
	}

	return true;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 catégorie tarifaires.
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id de la catégorie ajouté si tous est bon.
 */
function sf_task_prices_categorie($record){
	if( $record == null ) throw new Exception("sf_task_prices_categorie record null");
	global $config;

	$prc_cat_id = 0;

	$name = $record['Name'].' ('.$record['Id'].')'; // todo idéalement créer une colonne pour déplacer la clé gescom dedant

	// tente la récupération de la catégorie par le nom
	$rcat = prd_prices_categories_get( 0, true, $name, true );
	if( $rcat && ria_mysql_num_rows($rcat) ){
		$cat = ria_mysql_fetch_assoc($rcat);
		$prc_cat_id = $cat['id'];

		if( $record['IsDeleted'] ){
			if( !prd_prices_categories_del( $prc_cat_id ) ){
				throw new Exception("Erreur lors de la suppression de la catégorie tarifaire");
			}
			return 0;
		}else{
			// mise à jour de la catégorie
			if( !prd_prices_categories_update( $prc_cat_id, $name, true) ){
				throw new Exception("Erreur lors de la mise à jour de la catégorie tarifaire");
			}
		}

	}elseif(!$record['IsDeleted']){
		// ajout de la catégorie
		if( !($prc_cat_id = prd_prices_categories_add( $name, true, true)) ){
			throw new Exception("Erreur lors de l'ajout de la catégorie tarifaire");
		}
	}

	return $prc_cat_id;
}

/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 commande
 *	Elle va gérer la modification la mise à jour du status sur les commandes
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne l'id de la commande si tous est bon.
 */
function sf_task_order_states($record){
	if( $record == null ) throw new Exception("sf_task_order_states record null");
	global $config;

	$ord_id = 0;

	// tente la récupération de la commande par le numéro de pièce
	if( ord_orders_exists_piece($record['OrderNumber']) ){
		$ord_id = ord_orders_get_id($record['OrderNumber']);
		if( $ord_id > 0 ){
			$state_id = 0;

			if( $record['IsDeleted'] ){
				if( !ord_orders_state_update($ord_id, _STATE_CANCEL_MERCHAND) ){
					throw new Exception("Erreur lors de la mise à jour du status de lacommande ".print_r($record, true));
				}
			}else{

				switch ($record['Status']) {
					case 'Version préliminaire':
						$state_id = _STATE_WAIT_PAY;
						break;
					case 'Activée':
						$state_id = _STATE_PAY_CONFIRM;
						break;
					case 'Traitée':
						$state_id = _STATE_IN_PROCESS;
						break;
					case 'Confirmée':
						$state_id = _STATE_BL_EXP;
						break;
					default:
						//throw new Exception("Status de commande inconnue ".print_r($record, true));
						break;
				}
				if( $state_id > 0 && !ord_orders_state_update($ord_id, $state_id) ){
					throw new Exception("Erreur lors de la mise à jour du status de lacommande ".print_r($record, true));
				}
			}
		}
	}

	return $ord_id;
}

function sf_task_case_states($record){
	if( $record == null ) throw new Exception("sf_task_case_states record null");
	global $config;

	$ord_id = 0;

	// tente la récupération de la commande par le numéro de pièce
	if( ord_orders_exists_piece($record['CaseNumber']) ){
		$ord_id = ord_orders_get_id($record['CaseNumber']);
		if( $ord_id > 0 ){
			$state_id = 0;

			if( $record['IsDeleted'] ){
				if( !ord_orders_state_update($ord_id, _STATE_INTERVENTION_CANCEL) ){
					throw new Exception("Erreur lors de la mise à jour du status de la demande ".print_r($record, true));
				}
			}else{
				switch($record['FR_Order_Status__c']){
					case 'à préparer':
						$state_id = _STATE_INTERVENTION_PREP;
						break;
					case 'expédié':
						$state_id = _STATE_INTERVENTION_EXP;
						break;
					case 'Annulé':
						$state_id = _STATE_INTERVENTION_CANCEL;
						break;
					case 'A prendre en compte':
					default:
						$state_id = _STATE_INTERVENTION_DEVIS;
						break;
				}
				if( $state_id > 0 && !ord_orders_state_update($ord_id, $state_id) ){
					throw new Exception("Erreur lors de la mise à jour du status de la demande ".print_r($record, true));
				}
			}
		}
	}

	return $ord_id;
}
/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 demande, les demandes sont géré comme des commandes
 *
 * @param array $record Obligatoire, object de type array en provenance de l'api Soap
 * @return Exception Lance une exception dans le cas d'une erreur ou retourne l'id de la demande si tous est bon.
 */
function sf_task_case($record){
	if( $record == null ) throw new Exception("sf_task_case record null");

	if( $record['AccountId'] == null ){
		throw new Exception("sf_task_case pas de compte client rattaché");
	}

	global $config;
	// tente la récupération la commande l'id, prend dans le tableau de donnée pour gagner en traitement si celui-ci est défini
	$r_order = ord_orders_get_simple(array(
		'piece' => $record['CaseNumber']
	));

	// tente la récupération du client avec l'id, prend dans le tableau de donnée pour gagner en traitement si celui-ci est défini
	$r_usr = gu_users_get(0, '', '', 0, '', 0, $record['AccountId']);

	if( !$r_usr || !ria_mysql_num_rows($r_usr) ){
		throw new RetryTaskException($record['AccountId']." user doesn't exists");
	}
	$user = ria_mysql_fetch_assoc($r_usr);

	$date = new \DateTime($record['CreatedDate']);
	// choix du statut
	switch($record['FR_Order_Status__c']){
		case 'à préparer':
			$state = _STATE_INTERVENTION_PREP;
			break;
		case 'expédié':
			$state = _STATE_INTERVENTION_EXP;
			break;
		case 'Annulé':
			$state = _STATE_INTERVENTION_CANCEL;
			break;
		case 'A prendre en compte':
		default:
			$state = _STATE_INTERVENTION_DEVIS;
			break;
	}

	$error = $order_id = false;

	// recupération ou génération de la commande
	if( $r_order && ria_mysql_num_rows($r_order) ){
		$order = ria_mysql_fetch_assoc($r_order);
		$order_id = $order['id'];
		ord_orders_state_update($order_id, $state);
		if( $record['IsDeleted'] ){
			ord_orders_unmask( $order_id, true);
		}
	}elseif( !$record['IsDeleted'] ){

		$order_id = ord_orders_add_sage($user['id'], $date->format('Y-m-d H:i:s'), $state, $record['FR_Order_Status__c'], $record['CaseNumber'], '' , false, false, null, $record['Id']);

		if( !is_numeric($order_id) ){
			throw new Exception("sf_task_case Une erreur s'est produite lors de la création de la commande");
		}

		if( !is_numeric($order_id) ){
			throw new Exception("sf_task_case aucune commande");
		}

		ord_orders_comments_update($order_id, $record['Description']);

		$r_adr = gu_adresses_get( $user['id'], $user['adr_delivery'] );
		if( !$r_adr || !ria_mysql_num_rows($r_adr) ){
			throw new Exception("Erreur adresse introuvable :".$user['adr_delivery']);
		}

		$adr = ria_mysql_fetch_assoc($r_adr);

		$type_adr = 2;

		$need_update = $record['FR_Delivery_Account_Name__c'] != $adr['society']
			|| $record['FR_Delivery_Address_1__c'] != $adr['address1']
			|| $record['FR_Delivery_Address_2__c'] != $adr['address2']
			|| $record['FR_Delivery_Address_3__c'] != $adr['address3']
			|| $record['FR_Delivery_ZipCode__c'] != $adr['zipcode']
			|| $record['FR_Delivery_City__c'] != $adr['city']
			|| $record['FR_Delivery_Country__c'] != $adr['country']
			|| $record['FR_Delivery_Phone__c'] != $adr['phone'];

		// ajout de l'adresse de livraison
		if( $need_update ){
			// ajout de l'adresse de livraison
			$adr_id = gu_adresses_add($user['id'], $type_adr, '', '', '', $record['FR_Delivery_Account_Name__c'], '', $record['FR_Delivery_Address_1__c'], $record['FR_Delivery_Address_2__c'], $record['FR_Delivery_ZipCode__c'], $record['FR_Delivery_City__c'], $record['FR_Delivery_Country__c'], $record['FR_Delivery_Phone__c'], '', '', '', '', '', null, $record['FR_Delivery_Address_3__c']);

			if( !$adr_id ){
				throw new Exception("Erreur lors de la création de l'adresse de livraison".print_r($record, true));
			}

			if( !ord_orders_adr_delivery_set($order_id, $adr_id) ){
				throw new Exception("Erreur lors de la liaison de l'adresse à la demande".print_r($record, true));
			}
		}


		if( $record['ContactId'] !== null ){
			$contact_id = gu_users_by_ref($record['ContactId']);
			if( $contact_id ){
				ord_orders_set_contact_id($order_id, $contact_id);
			}
		}
	}

	return true;
}
/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 demande, les demandes sont géré comme des commandes
 *
 * @param array $record Obligatoire, object de type array en provenance de l'api Soap
 * @return Exception Lance une exception dans le cas d'une erreur ou retourne l'id de la demande si tous est bon.
 */
function sf_task_case_product($record){
	if( $record == null ) throw new Exception("sf_task_case_product record null");

	// récupération de la commande soit via le ord_id ajouté ou via FR_Case__r.CaseNumber
	if( isset($record['ria_ord_id']) ){
		$order_id = $record['ria_ord_id'];
	}else{
		$r_order = ord_orders_get_simple(array(
			'piece' => $record['FR_Case__r.CaseNumber']
		));

		if( !$r_order || !ria_mysql_num_rows($r_order) ){
			throw new RetryTaskException("sf_task_case_product aucune commande correspondante");
		}

		$order = ria_mysql_fetch_assoc($r_order);
		$order_id = $order['id'];
	}

	$prd_id = prd_products_get_id($record['FR_Product_Code__c']);

	if( !$prd_id ){
		sf_log("sf_task_case_product ce produit n'existe pas : '".$record['FR_Product_Code__c']."'");
		return;
	}

	$r_tvas = prc_tvas_get(0, false, $prd_id);
	$rate = _TVA_RATE_DEFAULT;
	if( $r_tvas && ria_mysql_num_rows($r_tvas) ){
		$tva = ria_mysql_fetch_assoc($r_tvas);
		$rate = $tva['rate'];
	}

	if( ord_products_exists( $order_id, $prd_id ) ){
		$result = ord_products_update_sage($order_id, $prd_id, $record['Name'], $record['FR_Product_Code__c'],$record['FR_Product_Name__r.name'], $record['FR_Quantity__c'], $record['FR_Net_Price__c'], $rate, '');
	}else{
		$result = ord_products_add_sage($order_id, $prd_id, $record['Name'], $record['FR_Product_Code__c'],$record['FR_Product_Name__r.name'], $record['FR_Quantity__c'], $record['FR_Net_Price__c'], $rate, '');
	}

	return $result;
}

/** Cette fonction permet d'envoyer a salesforce un relevé linéaire
 *
 * @param int $id Identifiant du relevé linéaire
 * @return void
 */
function sf_task_linear_raised_add($id){
	if( !is_numeric($id) || $id <= 0 ) {
		throw new Exception("sf_task_linear_raised id doit être un entier positif");
	}

	global $config;

	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ){
		return;
	}

	$r_linear_raised = prw_linear_raised::get($id);
	if( !$r_linear_raised || !ria_mysql_num_rows($r_linear_raised) ){
		throw new Exception("sf_task_linear_raised aucun relevé");
	}

	$linear_raised = ria_mysql_fetch_assoc($r_linear_raised);

	$r_user = gu_users_get($linear_raised['usr_id']);

	if( !$r_user || !ria_mysql_num_rows($r_user) ){
		throw new Exception("sf_task_linear_raised pas de compte associer");
	}

	$user = ria_mysql_fetch_assoc($r_user);

	$r_offers = prw_offers::get($id);

	if( !$r_offers || !ria_mysql_num_rows($r_offers) ){
		throw new Exception("sf_task_linear_raised pas d'offre de relevé");
	}

	$r_prw_followed_lists = prw_followed_lists::get($linear_raised['pfl_id']);
	$famille = false;
	if( $r_prw_followed_lists && ria_mysql_num_rows($r_prw_followed_lists) ){
		$prw_followed_lists = ria_mysql_fetch_assoc($r_prw_followed_lists);
		$famille = $prw_followed_lists['name'];
	}

	$seller_ref = false;
	if( is_numeric($linear_raised['author_id']) ){
		$seller_ref = gu_users_get_ref($linear_raised['author_id']);
	}

	$advanced_fields = array(
		101374 => 'Largeur__c',
		101375 => 'Hauteur__c',
		101376 => 'Commentaire__c',
	);

	$sf_linear_ids = array();
	$Releves_Lineaires_collection = array();

	// pour legrand une offre correspondra a un relevé dans saleforce
	// le produit correspond à la collection
	// les assortiments correspondent aux familles de produit
	$offers_synced = array();
	while( $offer = ria_mysql_fetch_assoc($r_offers) ){

		$sf_linear_id = 0;
		$offers_synced[] = $offer['id'];
		$Releves_Lineaires__c = new stdClass;
		$Releves_Lineaires__c->Compte_Distributeur__c = $user['ref_gescom'];
		$Releves_Lineaires__c->Affiliation_un_Groupe__c = $user['society'];
		$name = prd_products_get_name($offer['prd_id']);
		if( $name ){
			$Releves_Lineaires__c->Collection_exposee__c = $name;
		}
		if( $famille ){
			$Releves_Lineaires__c->Familles_produits__c = $famille;
		}

		$Releves_Lineaires__c->Nbre_delements__c = $offer['count'];
		$Releves_Lineaires__c->Nbre_de_ref_presente__c = $offer['facings'];
		$Releves_Lineaires__c->Nbre_etagere__c = $offer['level'];
		$Releves_Lineaires__c->FR_Id_Releve_yuto__c = $id;

		// ajout du représentant
		if( $seller_ref ){
			$Releves_Lineaires__c->FR_Utilisateur__c = $seller_ref;
		}

		foreach ($advanced_fields as $fld_id => $atribute) {
			$attr_value = fld_object_values_get($offer['id'], $fld_id);
			if( $attr_value ){
				$Releves_Lineaires__c->$atribute = $attr_value;
			}
		}

		$Releves_Lineaires_collection[] = $Releves_Lineaires__c;
	}

	$linearResponses = $config['sf_connexion']->create($Releves_Lineaires_collection, 'Releves_Lineaires__c');
	foreach ($linearResponses as $i => $linearResponse) {
		if( isset($linearResponse->errors) && sizeof($linearResponse->errors) ){
			throw new Exception("Erreur de création du relevé '".$id."' dans SalesForce ".print_r($linearResponses, true));
		}

		$sf_linear_ids[] = $linearResponse->id;
		break;
	}


	// ajout pièce jointe sur chaque relevé
	$attachements = array();
	$r_img_related = img_images_objects_get(0, $id);
	if( $r_img_related && ria_mysql_num_rows($r_img_related) ){
		while( $img_related = ria_mysql_fetch_assoc($r_img_related) ){
			$filesource_name = img_images_get_filesource($img_related['img_id']);
			if( $filesource_name ){
				$filesource = $config['img_dir'].'/source/'. $filesource_name;
				if( file_exists($filesource) ){
					$content = file_get_contents($filesource);
					$r_names = img_image_names_get($img_related['img_id']);
					$name = $filesource_name. '.jpg';
					if( $r_names && ria_mysql_num_rows($r_names) ){
						$info = ria_mysql_fetch_assoc($r_names);
						$name = $info['name'];
					}
					$base64 = base64_encode($content);

					foreach ($sf_linear_ids as $sf_linear_id) {
						$Attachment = new stdClass;
						$Attachment->Body = $base64;
						$Attachment->Name = $name;
						$Attachment->ParentId = $sf_linear_id;
						$attachements[] = $Attachment;
					}
				}
			}
		}

		if( !empty($attachements) ){
			$imgResponses = $config['sf_connexion']->create($attachements, 'Attachment');
			foreach ($imgResponses as $imgResponse) {
				if( isset($imgResponse->errors) && sizeof($imgResponse->errors) ){
					throw new Exception("Erreur de création de l'image dans SalesForce ".print_r($imgResponses, true));
				}
			}
		}
	}

	return true;
}
/** Cette fonction permet le traitement des lignes retourné par l'api Soap Salesforces pour 1 tarif produit/catalogue
 *	Elle va gérer la modification, l'ajout, ou la suppresion de l'élément.
 *	@param $record : Obligatoire object de type stdClass en provenance de l'api Soap
 *	@return Lance une exception dans le cas d'une erreur ou retourne true si tous est bon.
 */
function sf_task_prd_prc($record){
	if( $record == null ) throw new Exception("sf_task_prd_prc_add record null");
	global $config;

	$prc_id = 0;

	if( !$record['IsDeleted'] ){
		// récupère le produit du tarifs
		$prd = prd_products_get_by_ref_gescom($record['Product2Id']);
		if( !$prd ){
			throw new Exception("Le produit n'a pas été trouvé : ".print_r($record,true));
		}

		// récupère la catégorie tarifaire
		$rcat = prd_prices_categories_get( 0, true, $record['Pricebook2Id'], true );
		if( !$rcat || !ria_mysql_num_rows($rcat) ){
			throw new Exception("La catégorie tarifaire n'a pas été trouvé : ".print_r($record,true));
		}
		$cat = ria_mysql_fetch_assoc($rcat);
	}

	// préparation des conditions
	$conditions = array(_FLD_USR_PRC => array('symbol' => '=', 'value'=> $cat['id']));

	// tente la récupération du tarifs
	$rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, false, false, false, false, null, true, 1, 0, $record['Id'] );
	if( $rprice && ria_mysql_num_rows($rprice) ){
		$price = ria_mysql_fetch_assoc($rprice);
		$prc_id = $price['id'];

		if( $record['IsDeleted'] ){
			if( !prc_prices_del( $prc_id ) ){
				throw new Exception("Erreur lors de la suppression du tarifs: ".print_r($record,true));
			}
			return 0;
		}else{
			// mise à jour du tarifs
			if( !prc_prices_update( $prc_id, NEW_PRICE, $record['UnitPrice'], $record['date_start'], $record['date_end'], 1, $prd, 0, true, $record['Id'], false ) ){
				throw new Exception("Erreur lors de la mise à jour du tarifs: ".print_r($record,true));
			}
			if( !prc_price_conditions_upd( $prc_id, _FLD_USR_PRC, $cat['id'], '=' ) ){
				throw new Exception("Erreur lors de la mise à jour des conditions tarifaire: ".print_r($record,true));
			}
		}

	}elseif(!$record['IsDeleted']){
		// ajout du tarifs
		if( !($prc_id = prc_prices_add( NEW_PRICE, $record['UnitPrice'], $record['date_start'], $record['date_end'], 1, $prd, 0, true, true, $record['Id'], $conditions, false, false, 0 )) ){
			throw new Exception("Erreur lors de l'ajout du tarif: ".print_r($record,true));
		}
	}

	return $prc_id;
}

/** Permet d'ajouter les colonnes nullable dans le retour pour eviter des tests de présence
 * L'api Soap de salesforce ne retourne pas les valeurs si elle sont null. permet aussi de mettre les alias en place ( clé du tableau select )
 *	@param $response : objet de réponse contenant toutes les lignes de retour
 *	@param $select : tableau contenant toutes les colonnes que nous souhaitions à l'appel
 *	@return object un objet de réponse avec les colonnes manquantes en plus
 */
function sf_soql_response_parse($response, $select){
	if( !is_array($response) ){
		$response = json_decode(json_encode($response), true);
	}
	foreach( $response['records'] as $i => $record ){
		foreach( $select as $k => $s ){
			if( !isset($record[$s]) ){
				$record[$s] = "";
			}

			if( !is_numeric($k) ){
				$record[$k] = $record[$s];
				unset($record[$s]);
			}
		}
		$response['records'][$i] = $record;
	}
	return $response;
}

/** Permet de parser une config de sf sous la forme : 'val1','val2','val3'
 *	@param $config : la config
 *	@return array un tableau
 */
function sf_config_parse($config){

	$ret = array();
	$tmp = explode(',', $config);
	foreach( $tmp as $t ){
		$t = str_replace('\'', '', $t);
		$ret[] = $t;
	}
	return $ret;
}

/** Cette fonction permet le formatage d'une requete soql pour l'envoyer à l'api
 * @param string $table Obligatoire, nom de la table pour la requete
 * @param array $select Obligatoire, tableau des colonnes que l'on souhaite récupérer
 * @param array $where Obligatoire, tableau des conditions where souhaité
 * @param $join : tableau contenant les jointures à faire ( a tester )
 * @param $limit : nombe d'élément que l'ont souhaite avoir en retour
 * @return string une chaine de caractère de type soql
 */
function sf_soql_format($table, $select, $where, $join=array(), $limit=false){
	global $config;

	$sql = '
		select '.implode(',',$select).'
		from '.$table.' ';

	if( is_array($join) && sizeof($join) ){
		foreach( $join as $k => $v ){
			$sql .= 'join '.$k. ' on '.$v;
		}
	}
	if( is_array($where) && sizeof($where) ){
		$sql .= ' where '.implode(' and ', $where);
	}

	$sql .= ' order by LastModifiedDate asc ';

	if( $limit!== false ){
		$sql .= ' limit '.$limit;
	}

	if( isset($config['sf_debug']) && $config['sf_debug'] ){
		print $sql."\r\n";
	}
	return $sql;
}

/** Cette fonction permet de rettourner le nom de l'instance pour SF
 */
function sf_get_instance_name(){
	global $config;

	// tableau
	$instances = array(
		59 => 'DR',
		104 => 'Multi-marques',
		105 => 'Commando'
	);

	return isset($instances[$config['tnt_id']]) ? $instances[$config['tnt_id']] : '';
}

/** Cette fonction permet d'envoyer les rapports de visites dans SF
 *	@param $rpr_id Obligatoire, identifiant du rapport à intégrer
 */
function sf_reports_add( $rpr_id ){
	global $config;
	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ) return;
	if( !is_numeric($rpr_id) ){
		return false;
	}

	$rrpr = rp_reports_get($rpr_id);
	if( !$rrpr || !ria_mysql_num_rows($rrpr) ){
		return false;
	}
	$rpr = ria_mysql_fetch_assoc($rrpr);


	$prc_ref_gescom= 'n/a';

	switch( $rpr['type_id'] ){
		case 4: // questionnaires plexo

			$val =

			$sfReport = new stdclass();
			$sfReport->FR_Compte_Enquete__c = $rpr['usr_ref'];
			$sfReport->Name = $rpr['type_name'];
			$sfReport->Source__c = "Yuto";
			$sfReport->FR_Commentaire__c = $rpr['comments'];

			// gestion du contact associé au rapport de visite
			$cnt_id = fld_object_values_get($rpr_id, 5079, "", false, true, false, false);  // champ pour le contact
			if( !$cnt_id ){
				// récupération d'un contact par defaut
				$cnts = gu_users_get(0,'','',PRF_CONTACT,'',0,'',false, false, false, false, '', false, 0, '', $rpr['usr_id']);
				if( $cnts && ria_mysql_num_rows($cnts) ){
					$cnt = ria_mysql_fetch_assoc($cnts);
					$cnt_id = $cnt['id'];
				}
			}
			$rcontact = gu_users_get($cnt_id );
			if( !$rcontact || !ria_mysql_num_rows($rcontact) ){
				throw new Exception("Contact introuvable pour le rapport : ".$rpr['id']);
			}
			$contact = ria_mysql_fetch_assoc($rcontact);
			$sfReport->FR_Contact__c = $contact['ref'];


			// gestion de l'auteur du rapport
			$rauthor = gu_users_get($rpr['author_id'] );
			if( !$rauthor || !ria_mysql_num_rows($rauthor) ){
				throw new Exception("Auteur introuvable pour le rapport : ".$rpr['id']);
			}
			$author = ria_mysql_fetch_assoc($rauthor);
			$sfReport->FR_Effectuee_par__c = $author['ref'];


			$FR_Critere_design__c = fld_object_values_get($rpr_id, 101655, "", false, true, false, false);
			if( trim($FR_Critere_design__c) ){
				$sfReport->FR_Critere_design__c = $FR_Critere_design__c;
			}

			$FR_Demarche_prealable__c = fld_object_values_get($rpr_id, 101656, "", false, true, false, false);
			if( trim($FR_Demarche_prealable__c) ){
				$sfReport->FR_Demarche_prealable__c = $FR_Demarche_prealable__c;
			}

			$marque = fld_object_values_get($rpr_id, 101599, "", false, true, false, false);
			if( trim($marque) ){
				$sfReport->Marque__c = $marque;
			}
			$val = fld_object_values_get($rpr_id, 101596, "", false, true, false, false);
			if( trim($val) ){
				$sfReport->Position_1__c = $val;
			}
			$val = fld_object_values_get($rpr_id, 101598, "", false, true, false, false);
			if( trim($val) ){
				$sfReport->Position_2__c = $val;
			}
			$val = fld_object_values_get($rpr_id, 101597, "", false, true, false, false);
			if( trim($val) ){
				$sfReport->Position_3__c = $val;
			}
			$val = fld_object_values_get($rpr_id, 101595, "", false, true, false, false);
			if( trim($val) ){
				$sfReport->Position_4__c = $val;
			}
			$sfReport->Instance_Yuto__c = sf_get_instance_name();

			$rprReponses = $config['sf_connexion']->create(array($sfReport), 'FR_Enquetes__c');
			foreach ($rprReponses as $rprReponse) {
				if( isset($rprReponse->errors) && sizeof($rprReponse->errors) ){
					throw new Exception("Erreur de création de l'enquete".print_r($rprReponses, true).print_r($sfReport, true));
				}
				$ref_gescom = $rprReponse->id;
				break;
			}

			break;
		default:
			return false;
	}


	rp_reports_set_ref( $rpr_id, $ref_gescom );
	rp_reports_set_need_sync( $rpr_id, false );

	return true;
}

/** Cette fonction permet la mise à jour d'un compte ou contact dans SF
 *	@param $usr_id Obligatoire, identifiant de l'utilisateur à modifier
 */
function sf_users_add( $usr_id ){
	global $config;
	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ) return;

	if( $config['tnt_id'] != 59 ){
		throw new Exception("Modification de compte non permise sur les autres instance que DR pour le moment.");
	}
	if( !is_numeric($usr_id) ){
		throw new Exception("Compte non donnée");
	}

	$rusr = gu_users_get($usr_id);
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		throw new Exception("Compte non trouvé dans la bdd");
	}
	$usr = ria_mysql_fetch_assoc($rusr);

	if( !$usr['ref'] ){
		throw new Exception("Compte sans ID SF");
	}

	$sfUser = new stdclass();
    $sfUser->Id = $usr['ref'];

    if( !preg_match('/.*(riashop|riastudio|exemple).*/', $usr['email']) ){
		if( $usr['prf_id'] == PRF_CONTACT ){
            $sfUser->Email = $usr['email'];
        }else{
            $sfUser->FR_Account_Email__c = $usr['email'];
        }
    }


	if( $usr['prf_id'] == PRF_CONTACT ){

		// dégeux .. mais SF veut pas les +33
		$usr['phone'] = str_replace('+33', '0', $usr['phone']);
		$usr['mobile'] = str_replace('+33', '0', $usr['mobile']);

        $sfUser->Phone = $usr['phone'];
        $sfUser->MobilePhone = $usr['mobile'];

        $usrResponses = $config['sf_connexion']->update(array($sfUser), 'Contact');

	}else{


        foreach( sf_fields_get() as $mdl_id => $flds ){

        	// pour limite l'ecriture qu'a 1 seul models ur les profile
			if( $usr['prf_id'] == PRF_RESELLER ){
				if( $mdl_id != 455 ){
					continue;
				}
			}else{
				if( $mdl_id == 455 ){
					continue;
				}
			}

			foreach( $flds as $fld_id => $fld){

				$val = fld_object_values_get($usr['id'], $fld_id, '', false, true);

				if( $fld['fld_type'] == FLD_TYPE_BOOLEAN_YES_NO ){
					if( $val == 'Oui' || $val == 'oui' || $val == '1') {
						$val = 'true';
					}else{
						$val = 'false';
					}
				}else if( $fld['fld_type'] == FLD_TYPE_FLOAT || $fld['fld_type'] == FLD_TYPE_INT ){
					if( !is_numeric($val) ){
						continue;
					}
				}

				$sfUser->{$fld['sf_name']} = $val;
			}
		}

        $usrResponses = $config['sf_connexion']->update(array($sfUser), 'Account');
		//throw new Exception("Mise à jour des clients non permise pour le moment.. ".print_r($sfUser, true));
	}


	foreach ($usrResponses as $usrResponse) {
		if( isset($usrResponse->errors) && sizeof($usrResponse->errors) ){
			throw new Exception("Erreur de la mise à jour sur SF du client ".print_r($usrResponses, true).print_r($sfUser, true));
		}
	}

	return true;
}

/** Cette fonction permet l'intégration d'une commande dans le système salesforce
 *	@param $ord_id Obligatoire, identifiant de la commande à intégrer
 */
function sf_order_add( $ord_id ){
	global $config;
	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ) return;

	if( !is_numeric($ord_id) ){
		return false;
	}

	// récupère l'entete de la commande
	$rord = ord_orders_get_with_adresses( 0, $ord_id );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		throw new Exception("Commande introuvable pour la commande : ".$ord_id);
	}

	$ord = ria_mysql_fetch_assoc($rord);

	// si c'est un statut intervention on fait le process pour les demandes moyen publicaitaire
	if( in_array($ord['state_id'], array(_STATE_INTERVENTION_DEVIS, _STATE_INTERVENTIONS_CONFIRM, _STATE_INTERVENTION_PREP,_STATE_INTERVENTION_EXP,_STATE_INTERVENTION_CANCEL,_STATE_INTERVENTION_DEVIS)) ){
		return sf_task_case_add($ord);
	}

	// commande prise par
	$author = false;
	$rstate = ord_orders_states_get( $ord['id'], _STATE_WAIT_PAY);
	if( $rstate && ria_mysql_num_rows($rstate) ){
		$state = ria_mysql_fetch_assoc($rstate);

		if( $state['usr_id'] != $ord['user'] ){
			// récuère l'id salesforce
			$rauthor = gu_users_get($state['usr_id'] );
			if( !$rauthor || !ria_mysql_num_rows($rauthor) ){
				throw new Exception("Auteur introuvable pour la commande : ".$ord['id']);
			}
			$author = ria_mysql_fetch_assoc($rauthor);
		}
	}

	//on va parcourir tous les produits pour voir si un split est nécessaire si c'ets le cas on stop l'export de la commande temporairement
	$split_array = array();
	$roprd = ord_products_get($ord['id']);
	while( $oprd = ria_mysql_fetch_assoc($roprd) ){

		$added = false;

		// récupère le domaine si le champ split est à 1
		$res = $config['sf_connexion']->query('select Domaine__r.Name from Bundle__c where Id=\''.$oprd['ref_gescom'].'\' and Domaine__r.Split__c=\'Oui\' ');
		if( sizeof($res->records) ){
			$row = json_decode(json_encode($res->records[0]), true);
			if( isset($row['Domaine__r']['Name']) && $row['Domaine__r']['Name']){
				if( !isset($split_array[$row['Domaine__r']['Name']] ) ) $split_array[$row['Domaine__r']['Name']] = array();
				$split_array[$row['Domaine__r']['Name']][] = $oprd['id'];
				$added = true;
			}
		}

		if( !$added) {
			if( !isset($split_array['none'] ) ) $split_array['none'] = array();
			$split_array['none'][] = $oprd['id'];
		}

	}

	// lance le split en fonction du domaine des porduits
	if( sizeof( $split_array) > 1 ){
		// le cas ou tous les produits doivent être splitté en plusieurs commande et que la commande d'orgine n'aurais plus de produit
		// on va conservé tous les produits d'un bundle
		if( !isset($split_array['none']) ){
			$tmp = array_pop($split_array);
			$split_array['none'] = $tmp;
		}
		foreach( $split_array as $k => $prds ){
			if( $k == 'none' || $k == '' ) continue;

			// création de la commande
			$new_ord_id = ord_orders_add_sage( $ord['user'], $ord['date_en'], _STATE_BASKET, '', $ord['piece'], $k, false, $ord['wst_id'] );

			if( !$new_ord_id ){
				return false;
			}

			// magasin de livraison
			if( $ord['str_id'] ){
				ord_orders_set_dlv_store( $new_ord_id, $ord['str_id'] );
			}
			// adresse de livraison
			if( $ord['adr_delivery'] ){
				ord_orders_adr_delivery_set( $new_ord_id, $ord['adr_delivery'] );
			}
			// adresse de livraison
			if( $ord['adr_invoices'] ){
				ord_orders_adr_invoices_set( $new_ord_id, $ord['adr_invoices'] );
			}
			// consignes
			if( $ord['dlv-notes'] ){
				ord_orders_dlv_notes_set( $new_ord_id, $ord['dlv-notes'] );
			}
			// représentant
			if( $ord['seller_id'] ){
				ord_orders_set_seller_id( $new_ord_id, $ord['seller_id'] );
			}
			// date de livraison
			if( $ord['date_livr_en'] ){
				ord_orders_set_date_livr( $new_ord_id, $ord['date_livr_en'] );
			}
			// dépot de la commande
			if( $ord['dps_id'] ){
				ord_orders_set_deposit( $new_ord_id, $ord['dps_id'] );
			}
			// payement
			if( $ord['pay_id'] ){
				ord_orders_pay_type_set( $new_ord_id, $ord['pay_id'] );
			}

			// mise à jours du contact
			if( isset($ord['contact_id']) ){
				if( !ord_orders_set_contact_id( $new_ord_id, $ord['contact_id']) ){
					throw new Exception("Erreur dans la mise à jour du contact");
				}
			}

			// mise à jours du contact
			if( isset($ord['reseller_id']) ){
				if( !ord_orders_set_reseller_id( $new_ord_id, $ord['reseller_id']) ){
					throw new Exception("Erreur dans la mise à jour du revendeur : ".$ord['reseller_id']);
				}
			}

			// mise à jours du contact du revendeur
			if( isset($ord['reseller_contact_id']) ){
				if( !ord_orders_set_reseller_contact_id( $new_ord_id, $ord['reseller_contact_id']) ){
					throw new Exception("Erreur dans la mise à jour du contact du revendeur");
				}
			}

			// traitement pour la ligne
			foreach( $prds as $id ){

				ria_mysql_query('
					update ord_products
					set prd_ord_id = '.$new_ord_id.'
					where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
					and prd_id = '.$id.'
				');

				// pour les enfants
				ria_mysql_query('
					update ord_products
					set prd_ord_id = '.$new_ord_id.'
					where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$ord_id.'
					and prd_parent_id = '.$id.'
				');
			}

			// données complémentaires de la ligne
			{
				ria_mysql_query('
					update fld_object_values
					set pv_obj_id_0 = '.$new_ord_id.'
					where pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = '.$ord_id.'
					and pv_obj_id_1 in (
						select prd_id from ord_products
						where prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = '.$new_ord_id.'
					)
				');
			}


			// recalcul des totaux sur les deux commandes
			ord_orders_update_totals( $new_ord_id );
			ord_orders_update_totals( $ord_id );

			// valide la commande
			ord_orders_state_update( $new_ord_id, _STATE_WAIT_PAY, '', false, $author ? $author['id'] : false );

			// send la commande à SF
			sf_order_add($new_ord_id);
		}
	}


	// récupère l''utilisateur de la commande
	$rusr = gu_users_get($ord['user']);
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		throw new Exception("Client introuvable pour la commande : ".$ord_id);
	}
	$usr = ria_mysql_fetch_assoc($rusr);

	// récupère la catégorie tarifaire commando distrib
	$prc_commando_distrib = 0;
	$rprc = prd_prices_categories_get( 0, true, $config['salesforce_config']['Id_Prix_commando_distributeur__c'], true );
	if( $rprc && ria_mysql_num_rows($rprc) ){
		$prc = ria_mysql_fetch_assoc($rprc);
		$prc_commando_distrib = $prc['id'];
	}

	$prc_commando = 0;
	$rprc = prd_prices_categories_get( 0, true, $config['salesforce_config']['Id_Prix_commando__c'], true );
	if( $rprc && ria_mysql_num_rows($rprc) ){
		$prc = ria_mysql_fetch_assoc($rprc);
		$prc_commando = $prc['id'];
	}

	// prépration de l'objet pour l'envoi à SF
	$sfOrd = new stdclass();
	$sfOrd->EffectiveDate = date('c', strtotime($ord['date_en']));
	$sfOrd->AccountId = $usr['ref'];
	$sfOrd->BillingCity = $ord['inv_city'];
	$sfOrd->BillingCountry = $ord['inv_country'];
	$sfOrd->BillingPostalCode = $ord['inv_postal_code'];
	$sfOrd->BillingStreet = $ord['inv_address1'];
	$sfOrd->Status = 'Version préliminaire';
	$sfOrd->Description = 'En cours d\'écriture par le Webservice';
	$sfOrd->Pricebook2Id = $config['salesforce_config']['Id_Prix_commando__c'];
	$sfOrd->Instance_Yuto__c = sf_get_instance_name();

	// ajout de la date de livraison
	if( $ord['date_livr_en'] ){
		if( isset($config['salesforce_config']['Date_livraison_max__c']) && $config['salesforce_config']['Date_livraison_max__c'] && strtotime($ord['date_livr_en']) > strtotime($config['salesforce_config']['Date_livraison_max__c'])){
			$ord['date_livr_en'] = $config['salesforce_config']['Date_livraison_max__c'];
		}

		$sfOrd->Date_de_livraison_souhait_e__c = date('Y-m-d', strtotime($ord['date_livr_en']));
	}

	$sfPoints = 0;

	$roprd = ord_products_get($ord['id']);
	while( $oprd = ria_mysql_fetch_assoc($roprd) ){
		// récupère le nb de pts de fid
		$res = $config['sf_connexion']->query('select Points__c from Bundle__c where Id=\''.$oprd['ref_gescom'].'\'');
		if( !sizeof($res->records) ){
			error_log(__FILE__."Bundle non trouvé pour écrire la commande  : ".$sf_ord_id);
			ord_products_del($oprd['ord_id'], $oprd['id'], $oprd['line']);
			continue;
		}
		foreach ($res->records as $record) {
			if( isset($record->Points__c) && $record->Points__c > 0 ){
				$sfPoints += $record->Points__c * $oprd['qte'];
			}
		}
	}

	$roprd = ord_products_get($ord['id']);
	if( !ria_mysql_num_rows($roprd) ){
		ord_orders_state_update($ord['id'], _STATE_CANCEL_MERCHAND);
		return 0;
	}


	$sfOrd->TotalPoints__c = $sfPoints;

	// récupère le contact de la commande
	if( $ord['contact_id'] > 0 ){
		$rcnt = gu_users_get($ord['contact_id']);
		if( !$rcnt || !ria_mysql_num_rows($rcnt) ){
			throw new Exception("Contact introuvable pour la commande : ".$ord['id']);
		}
		$cnt = ria_mysql_fetch_assoc($rcnt);
		$sfOrd->CustomerAuthorizedById = $cnt['ref'];
	}

	// $sfOrd->TotalAmount = $ord['total_ttc']; // pas le droit ?

	// récupère le revendeur de la commande
	if( $ord['reseller_id'] ) {

		$rreseller = gu_users_get($ord['reseller_id']);
		if( !$rreseller || !ria_mysql_num_rows($rreseller) ){
			throw new Exception("Revendeur introuvable pour la commande : ".$ord['id']);
		}
		$reseller = ria_mysql_fetch_assoc($rreseller);
		$sfOrd->Distributeur__c = $reseller['ref'];

		// contact revendeur de la commande
		if( $ord['reseller_contact_id'] ) {

			$rresellercnt = gu_users_get($ord['reseller_contact_id']);
			if( !$rresellercnt || !ria_mysql_num_rows($rresellercnt) ){
				throw new Exception("Contact du revendeur introuvable pour la commande : ".$ord['id']);
			}
			$resellercnt = ria_mysql_fetch_assoc($rresellercnt);
			$sfOrd->Contact_du_Distributeur__c = $resellercnt['ref'];
		}
	}
	else if( $ord['str_id'] ) {
		$resellers = rel_relations_hierarchy_parents_get_ids(REL_RESELLER_STORE_HIERARCHY, $ord['str_id']);
		if( !sizeof($resellers ) ){
			throw new Exception("Revendeur introuvable pour la commande : ".$ord['id']);
		}

		$reseller = $resellers[0];

		// récuère l'id salesforce
		$rresel = gu_users_get($reseller);
		if( !$rresel || !ria_mysql_num_rows($rresel) ){
			throw new Exception("Revendeur introuvable pour la commande : ".$ord['id']);
		}
		$resel = ria_mysql_fetch_assoc($rresel);

		$sfOrd->Distributeur__c = $resel['ref'];
	}

	// prise en compte du lieu de livraison
	$sfOrd->FR_Lieu_Livraison__c = fld_object_values_get($ord['id'], _FLD_ORD_DLV_HOME, '', false, true) == 'Oui' ? 'Livraison à votre adresse' : 'Mise à dispo chez distributeur';

	// commande prise par
	if( $author ){
		$sfOrd->FR_Utilisateur__c = $author['ref'];

		// récupère le devices associé au compte
		$rdev = dev_devices_get(0, $author['id'], '', -1, '=', false, false, true);
		if( $rdev && ria_mysql_num_rows($rdev) ){
			$dev = ria_mysql_fetch_assoc($rdev);
			$sfOrd->FR_Device__c = $dev['brand'].' ('.$dev['model'].') ';
		}
	}

	// création de l'entete de la commande dans SalesForce
	$sf_ord_id = 0;
	$ordResponses = $config['sf_connexion']->create(array($sfOrd), 'Order');
	foreach ($ordResponses as $ordResponse) {
		if( isset($ordResponse->errors) && sizeof($ordResponse->errors) ){
			throw new Exception("Erreur de création de l'entete de la commande dans SalesForce ".print_r($ordResponses, true).print_r($sfOrd, true));
		}

		$sf_ord_id = $ordResponse->id;
		break; // que 1 commande à la fois
	}

	// ajout des produits de la commande
	$sfBundles = array();
	$sfPrds = array();
	$sfPrds_Distrib = array();
	$roprd = ord_products_get($ord['id']);
	while( $oprd = ria_mysql_fetch_assoc($roprd) ){

		// préparation de l'ajout pour l'association bundle / commande
		$sfBundle = new stdclass();
		$sfBundle->Bundle__c = $oprd['ref_gescom'];
		$sfBundle->Quantity__c = $oprd['qte'];
		$sfBundle->Order__c = $sf_ord_id;

		$sfBundles[] = $sfBundle;

		// prépration de l'ajout pour l'association produit / commande
		$rchilds = ord_products_get( $ord['id'], false, 0, '', null, false, -1, $oprd['id'], $oprd['child-line'] );
		if( $rchilds && ria_mysql_num_rows($rchilds) ){
			while( $child = ria_mysql_fetch_assoc($rchilds) ){

				$price_id_gescom = "";

				$prc_ref_gescom = "";
				$prc_ref_gescom_distrib = "";


				// attention il est possible ici de ne pas trouver de tarifs car le tarifs peut être mis sur la liaison parent / enfant.
				// et donc il n'a pas de prc_prices. Dans ce cas on fait une requete dans SalesForce pour trouver le bon catalogue de prix
				$res = $config['sf_connexion']->query('select Catalogue_Installateur__c, Catalogue_de_Prix_Distributeur__c  from Association_bundle_product__c where Bundle__c=\''.$oprd['ref_gescom'].'\' and Product__c=\''.$child['ref_gescom'].'\'');
				if( sizeof($res->records)==1 ){

					foreach ($res->records as $record) {
						if( isset($record->Catalogue_Installateur__c) && trim($record->Catalogue_Installateur__c)!='' ){
							$prc_ref_gescom = $record->Catalogue_Installateur__c;
							$prc_ref_gescom_distrib = $record->Catalogue_de_Prix_Distributeur__c;

							// récupère l'id du tarifs
							$res = $config['sf_connexion']->query('select Id from PricebookEntry where IsDeleted=false
									and (Pricebook2.FR_BeginsDate__c > '.date('Y-m-d', time()).' or Pricebook2.FR_BeginsDate__c=null)
									and (Pricebook2.FR_EndDate__c < '.date('Y-m-d', time()).' or Pricebook2.FR_EndDate__c = null )
									and Pricebook2Id=\''.$config['salesforce_config']['Id_Prix_commando__c'].'\' and Product2Id=\''.$child['ref_gescom'].'\'');
							if( sizeof($res->records) ){
								foreach ($res->records as $record) {
									$price_id_gescom = $record->Id;
									break;
								}
								break;
							}

						}
					}
				}

				if( !$price_id_gescom ){
					// tente la récupération de l'id du tarifs sur le catalogue
					$rprc = prc_prices_get( 0, NEW_PRICE, $child['price_ht'],false,false,$child['id'],false,false,false,null,true,1,0,false,false,array(array( 'fld'=>_FLD_USR_PRC, 'symbol'=>'=', 'value'=>$prc_commando)));
					if( $rprc && ria_mysql_num_rows($rprc) ){
						$prc = ria_mysql_fetch_assoc($rprc);
						$price_id_gescom = $prc['name'];
					}
				}
				if( !$price_id_gescom ){
					// tente la récupération de l'id du tarifs sur le catalogue sans passé le tarifs cas des chiffre après virugle
					$rprc = prc_prices_get( 0, NEW_PRICE, false,false,false,$child['id'],false,false,false,null,true,1,0,false,false,array(array( 'fld'=>_FLD_USR_PRC, 'symbol'=>'=', 'value'=>$prc_commando)));
					if( $rprc && ria_mysql_num_rows($rprc) ){
						$prc = ria_mysql_fetch_assoc($rprc);
						$price_id_gescom = $prc['name'];
					}
				}

				$sfPrd = new stdclass();
				$sfPrd->Bundle__c = $oprd['ref_gescom'];
				$sfPrd->Product2Id = $child['ref_gescom'];
				$sfPrd->OrderId = $sf_ord_id;
				$sfPrd->Quantity = $child['qte'];
				$sfPrd->UnitPrice = $child['price_ht'];
				$sfPrd->PriceBookEntryId = $price_id_gescom;

				$sfPrds[] = $sfPrd;

				// pour le distributeur
				$sfPrd_Distrib = new stdclass();
				$sfPrd_Distrib->Bundle__c = $oprd['ref_gescom'];
				$sfPrd_Distrib->Product__c = $child['ref_gescom'];
				$sfPrd_Distrib->Order__c = $sf_ord_id;
				$sfPrd_Distrib->Quantity__c = $child['qte'];

				// a ce niveau il faut qu'on récupère maintenant le prix distributeur si on dispose d'un catalogue de prix distrib
				if( $prc_ref_gescom_distrib!=''){
					$res = $config['sf_connexion']->query('select UnitPrice from PricebookEntry where IsDeleted=false
							and (Pricebook2.FR_BeginsDate__c > '.date('Y-m-d', time()).' or Pricebook2.FR_BeginsDate__c=null)
							and (Pricebook2.FR_EndDate__c < '.date('Y-m-d', time()).' or Pricebook2.FR_EndDate__c = null )
							and Pricebook2Id=\''.$prc_ref_gescom_distrib.'\' and Product2Id=\''.$child['ref_gescom'].'\'');
					if( !sizeof($res->records) ){
						throw new Exception("Pas de tarifs distributeur trouvé pour inscrire la commande ou plusieurs liaisons : ".print_r($child, true).$prc_ref_gescom_distrib);
					}

					foreach ($res->records as $record) {
						$sfPrd_Distrib->UnitPrice__c = $record->UnitPrice;
					}
				}else{
					$rprc = prd_products_get_price( $child['id'], 0, $prc_commando_distrib );
					if( $rprc && ria_mysql_num_rows($rprc) ){
						$prc = ria_mysql_fetch_assoc($rprc);
						if( $prc['price_ht'] ){
							$sfPrd_Distrib->UnitPrice__c = $prc['price_ht'];
						}
					}else{
						throw new Exception("Pas de tarifs distributeur trouvé pour inscrire la commande  : ".print_r($child, true));
					}
				}

				$sfPrds_Distrib[] = $sfPrd_Distrib;
			}
		}else{
			// produit bundle  ajouté sans composition ??
		}
	}

	if( sizeof($sfBundles) ){
		$prdsResponses = $config['sf_connexion']->create($sfBundles, 'OrderBundle__c');
		foreach ($prdsResponses as $prdResponse) {
			if( isset($prdResponse->errors) && sizeof($prdResponse->errors) ){
				throw new Exception("Erreur lors de la création de la ligne bundle : ".print_r($prdResponse, true)." : ".print_r($sfBundles, true));
			}
		}
	}

	if( sizeof($sfPrds) ){
		$prdsResponses = $config['sf_connexion']->create($sfPrds, 'OrderItem');
		foreach ($prdsResponses as $prdResponse) {
			if( isset($prdResponse->errors) && sizeof($prdResponse->errors) ){
				throw new Exception("Erreur lors de la création de la ligne client : ".print_r($prdResponse, true)." : ".print_r($sfPrds, true));
			}
		}
	}

	if( sizeof($sfPrds_Distrib) ){
		$prdsResponses = $config['sf_connexion']->create($sfPrds_Distrib, 'OrderItemDistributor__c');
		foreach ($prdsResponses as $prdResponse) {
			if( isset($prdResponse->errors) && sizeof($prdResponse->errors) ){
				throw new Exception("Erreur lors de la création de la ligne distrib : ".print_r($prdResponse, true)." : ".print_r($sfPrds_Distrib, true));
			}
		}
	}
	// requete pour récupérer le numéro de la commande
	$res = $config['sf_connexion']->query('select OrderNumber from Order where Id=\''.$sf_ord_id.'\'');
	if( !sizeof($res->records) ){
		throw new Exception("Commande non trouvé dans salesforce?  : ".$sf_ord_id);
	}
	foreach ($res->records as $record) {
		if( !ord_orders_piece_set( $ord['id'], $record->OrderNumber) ){
			throw new Exception("Erreur lors de la mise à jour du numéro de pièce : ".$ord['id']);
		}
	}

	// mise à jour du commentaire pour dire que la commande est totalement écrite
	$sfOrd = new stdclass();
	$sfOrd->Id = $sf_ord_id;
	$sfOrd->ActivatedDate = date('c');
	$sfOrd->ActivatedById = $config['salesforce_yuto_usr'];
	$sfOrd->Status = isset($config['salesforce_config']['Status_commande__c']) ? $config['salesforce_config']['Status_commande__c'] : 'Traitée';
	$sfOrd->Description = trim($ord['dlv-notes']) ? $ord['dlv-notes'] : '.';
	$ordResponses = $config['sf_connexion']->update(array($sfOrd), 'Order');

	foreach ($ordResponses as $ordResponse) {
		if( isset($ordResponse->errors) && sizeof($ordResponse->errors) ){
			throw new Exception("Erreur lors de la mise à jour du status de la commande : ".print_r($ordResponse, true)." : ".print_r($sfOrd, true));
		}
	}

	// mise à jour du status en local
	ord_orders_state_update($ord['id'], _STATE_PAY_CONFIRM, '', false );

	return $ord_id;
}
/**
 * Cette fonction permet d'envoyé une demande d'intervention publicitaire
 *
 * @param array $ord Tableau représentant la commande
 * @return void
 */
function sf_task_case_add( $ord ){
	global $config;

	if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ){
		return;
	}

	$ord_id = $ord['id'];

	// récupère l''utilisateur de la commande
	$r_usr = gu_users_get($ord['user']);
	if( !$r_usr || !ria_mysql_num_rows($r_usr) ){
		throw new Exception("Client introuvable pour la commande : ".$ord_id);
	}

	$user = ria_mysql_fetch_assoc($r_usr);

	$Case = new stdClass;
	$Case->AccountId = $user['ref'];
	$Case->FR_CustomerComment_255__c = $ord['comments'];
	$Case->Instance_Yuto__c = sf_get_instance_name();
	$Case->Description = trim($ord['dlv-notes']) ? $ord['dlv-notes'] : '.';

	// prise en compte du lieu de livraison
	$is_ldd = fld_object_values_get($ord['id'], _FLD_ORD_DLV_HOME, '', false, true) == 'Oui' ;
	$Case->FR_Recipient_Type__c = $is_ldd ? 'Client' : 'A compléter';

	if( $is_ldd ){
		$Case->FR_Delivery_Account_Name__c = $ord['dlv_society'];
		$Case->FR_Delivery_Address_1__c = substr($ord['dlv_address1'], 0, 34);
		$Case->FR_Delivery_Address_2__c = substr($ord['dlv_address2'], 0, 34);
		$Case->FR_Delivery_Address_3__c = substr($ord['dlv_address3'], 0, 34);
		$Case->FR_Delivery_ZipCode__c = $ord['dlv_postal_code'];
		$Case->FR_Delivery_City__c = $ord['dlv_city'];
		$Case->FR_Delivery_Country__c = $ord['dlv_country'];
		$Case->FR_Delivery_Phone__c = $ord['dlv_phone'];
	}else{

		// commande prise par
		$rstate = ord_orders_states_get( $ord['id'], _STATE_INTERVENTIONS_CONFIRM);
		if( $rstate && ria_mysql_num_rows($rstate) ){
			$state = ria_mysql_fetch_assoc($rstate);

			// récuère l'id salesforce
			$rauthor = gu_users_get($state['usr_id'] );
			if( !$rauthor || !ria_mysql_num_rows($rauthor) ){
				throw new Exception("Auteur introuvable pour la commande : ".$ord['id']);
			}
			$author = ria_mysql_fetch_assoc($rauthor);

			$res = $config['sf_connexion']->query('select Email from User where Id=\''.$author['ref'].'\'');
			if( !sizeof($res->records) ){
				throw new Exception("Auteur non trouvé dans salesforce?  : ".$sf_case_id);
			}

			foreach ($res->records as $record) {
				$Case->FR_Delivery_Email__c  = $record->Email;
			}

			// récupèration du compte commercial car c'est l'adresse de celui ci qui doit être affiché
			$Case->FR_Delivery_Account_Name__c = $author['adr_firstname'].' '.$author['adr_lastname'];
			$Case->FR_Delivery_Address_1__c = substr($author['address1'], 0, 34);
			$Case->FR_Delivery_Address_2__c = substr($author['address2'], 0, 34);
			$Case->FR_Delivery_Address_3__c = substr($author['address3'], 0, 34);
			$Case->FR_Delivery_ZipCode__c = $author['zipcode'];
			$Case->FR_Delivery_City__c = $author['city'];
			$Case->FR_Delivery_Country__c = $author['country'];
			$Case->FR_Delivery_Phone__c = trim($author['phone']) ? $author['phone'] : $ord['dlv_phone'];
		}
	}

	// ajout du représentant
	if( is_numeric($ord['seller_id']) ){
		$seller_ref = gu_users_get_ref($ord['seller_id']);
		if( $seller_ref ){
			$Case->FR_User__c = $seller_ref;
		}
	}


	// $Case->Type = 'Moyens PUB';
	$Case->RecordTypeId = $config['salesforce_config']['Id_demande_type_moyen_pub__c'];

	switch($ord['state_id']){
		case _STATE_INTERVENTION_PREP:
			$state = 'à préparer';
			break;
		case _STATE_INTERVENTION_EXP:
			$state = 'expédié';
			break;
		case _STATE_INTERVENTION_CANCEL:
			$state = 'Annulé';
			break;
		case _STATE_INTERVENTION_DEVIS:
		default:
			$state = 'A prendre en compte';
			break;
	}
	$Case->FR_Order_Status__c = $state;
	$Case->Priority = ($prio = fld_object_values_get($ord_id, 101328)) ? $prio : 'Normal';

	// récupère le contact de la commande
	if( $ord['contact_id'] > 0 ){
		$r_contact = gu_users_get($ord['contact_id']);
		if( !$r_contact || !ria_mysql_num_rows($r_contact) ){
			throw new Exception("Contact introuvable pour la demande : ".$ord['id']);
		}
		$contact = ria_mysql_fetch_assoc($r_contact);
		$Case->ContactId = $contact['ref'];
	}

	// commande prise par
	$rstate = ord_orders_states_get( $ord['id'], _STATE_INTERVENTIONS_CONFIRM);
	if( $rstate && ria_mysql_num_rows($rstate) ){
		$state = ria_mysql_fetch_assoc($rstate);

		if( $state['usr_id'] != $ord['user'] ){
			// récuère l'id salesforce
			$rauthor = gu_users_get($state['usr_id'] );
			if( !$rauthor || !ria_mysql_num_rows($rauthor) ){
				throw new Exception("Auteur introuvable pour la demande : ".$ord['id']);
			}
			$author = ria_mysql_fetch_assoc($rauthor);

			$Case->OwnerId = $author['ref'];
		}
	}

	// création de l'entete de la commande dans SalesForce
	$sf_case_id = 0;
	$caseResponses = $config['sf_connexion']->create(array($Case), 'Case');
	foreach ($caseResponses as $caseResponse) {
		if( isset($caseResponse->errors) && sizeof($caseResponse->errors) ){
			throw new Exception("Erreur de création de la demande dans SalesForce ".print_r($caseResponses, true)." ".print_r($Case, true));
		}

		$sf_case_id = $caseResponse->id;
		break; // que 1 commande à la fois
	}

	ord_orders_ref_gescom_set($ord_id, $sf_case_id);

	$Suggested_Product_Solution = array();
	$roprd = ord_products_get($ord_id);
	while( $oprd = ria_mysql_fetch_assoc($roprd) ){

		// préparation de l'ajout des produits
		$FR_Suggested_Product_Solution__c = new stdclass();
		$FR_Suggested_Product_Solution__c->FR_Product_Name__c = $oprd['ref_gescom'];
		$FR_Suggested_Product_Solution__c->FR_Quantity__c = $oprd['qte'];
		$FR_Suggested_Product_Solution__c->FR_Case__c = $sf_case_id;
		$FR_Suggested_Product_Solution__c->FR_Over_Billing__c = true;

		$Suggested_Product_Solution[] = $FR_Suggested_Product_Solution__c;
	}

	if( count($Suggested_Product_Solution) ){
		$prdsResponses = $config['sf_connexion']->create($Suggested_Product_Solution, 'FR_Suggested_Product_Solution__c');
		foreach ($prdsResponses as $prdResponse) {
			if( isset($prdResponse->errors) && sizeof($prdResponse->errors) ){
				throw new Exception("Erreur lors de la création de la ligne solution proposé : ".print_r($prdResponse, true));
			}
		}
	}

	// requete pour récupérer le numéro de la commande
	$res = $config['sf_connexion']->query('select CaseNumber from Case where Id=\''.$sf_case_id.'\'');
	if( !sizeof($res->records) ){
		throw new Exception("Commande non trouvé dans salesforce?  : ".$sf_case_id);
	}

	foreach ($res->records as $record) {
		if( !ord_orders_piece_set( $ord['id'], $record->CaseNumber) ){
			throw new Exception("Erreur lors de la mise à jour du numéro de pièce : ".$ord['id']);
		}
	}

	return $ord_id;
}
/**
 * Cette fonction permet d'ajouter un relevé linéaire dans riashop
 * Il y a une particularité pour legrand car un relevé de riashop correspond a plusieurs de Salesforce
 * Les relevé de legrand serons des offres dans riashop d'un relevé.
 * Donc pour chaque relevé on fait un match dans la journé en fonction du commercial, du client et l'assortiment
 * si on en a déjà un on ajoute une offres au relevé.
 *
 * @param array $record Donnée récupéré de Salesforce
 * @return integer Retourne l'identifiant du relevé de
 */
function sf_task_linear_raised($record){
	if( $record == null ){
		throw new Exception("sf_task_linear_raised record null");
	}

	if( $record['Compte_Distributeur__c'] == null ){
		throw new Exception("sf_task_linear_raised pas de compte client rattaché");
	}

	$usr_id = gu_users_by_ref($record['Compte_Distributeur__c']);
	if( !$usr_id ){
		throw new Exception("sf_task_linear_raised l'utilisateur n'existe pas : ".$record['Compte_Distributeur__c']);
	}

	if( $record['FR_Utilisateur__c '] ){
		$seller_id = gu_users_by_ref($record['FR_Utilisateur__c']);
	}

	if( !$seller_id ){
		$seller_id = gu_users_by_ref($record['CreatedById']);
	}

	$LinearRaisedGetter = new LinearRaisedGetter;

	$LinearRaisedGetter->withUserId($usr_id);

	if( !$seller_id ){
		throw new Exception("sf_task_linear_raised le commercial n'existe pas");
	}

	$LinearRaisedGetter->withAuthorId($seller_id);

	$pfl = prw_followed_lists::getByName($record['Familles_produits__c']);
	$pfl_id = null;
	if( $pfl !== null ){
		$LinearRaisedGetter->withPflId($pfl['id']);
		$pfl_id = $pfl['id'];
	}

	$today = new DateTime('today');
	$LinearRaisedGetter->fromTo($today, $today);


	$r_linear_raised = $LinearRaisedGetter->query();
	if( !$r_linear_raised || !ria_mysql_num_rows($r_linear_raised) ){
		$plr_id = prw_linear_raised::add($seller_id, $usr_id, $pfl_id);
		if( !$plr_id ){
			throw new Exception("sf_task_linear_raised erreur lors de la création de l'en tête du relevé");
		}
	}else{
		$linear_raised = ria_mysql_fetch_assoc($r_linear_raised);
		$plr_id = $linear_raised['id'];
	}

	if( $record['Collection_exposee__c'] != null ){
		$prd_id = prd_products_get_id($record['Collection_exposee__c']);
		if( !$prd_id ){
			throw new Exception("sf_task_linear_raised la collection n'existe pas");
		}

		$ofr_id = prw_offers::add($plr_id, $prd_id, $usr_id);
		if( !$ofr_id ){
			throw new Exception("sf_task_linear_raised erreur lors de l'ajout de l'offre");
		}

		$advanced_fields = array(
			101373 => 'Nbre_etagere__c',
			101374 => 'Largeur__c',
			101375 => 'Hauteur__c',
			101376 => 'Commentaire__c',
			101377 => 'Nbre_delements__c',
		);

		foreach ($advanced_fields as $fld_id => $attribut_name) {
			if( isset($record[$attribut_name]) ){
				fld_object_values_set($ofr_id, $fld_id, $record[$attribut_name]);
			}
		}
	}

	return $plr_id;
}
/**	Cette fonction permet gérer la création d'une marque
 * 	@param string $name nom de la marque
 *	@return int l'identifiant de la marque dans la base de donnée riashop
 */
function sf_brand_add( $name ){
	if( trim($name) == ''){
		return 0;
	}

	$brd_id = 0;

	$rbrd  = prd_brands_get(0, false, $name);
	if( !$rbrd || ! ria_mysql_num_rows($rbrd) ){
		if( !($brd_id = prd_brands_add($name, '', '', true, '', true)) ){
			throw new Exception('Erreur lors de la création de la marque produit'.$name);
		}
	}else{
		$brd = ria_mysql_fetch_assoc($rbrd);
		$brd_id = $brd['id'];
	}

	return $brd_id;
}

/** Permet de faire une requete pour récupérer uniquement un record
 *	Attention cette fonction utilise un cache temporaire qui est vidé à chaque changemetn de taches
 *	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 *	@param $record_id Obligatoire, idnetifiant du record
 *	@return Le record en question, false en cas d'erreur
 */
function sf_get_row($tsk_id, $record_id){
	global $config;
	if( !$config['sf_connexion'] ) return;

	if( isset($config['salesforce_getrow_cache']) && isset($config['salesforce_getrow_cache'][$record_id]) ){
		return $config['salesforce_getrow_cache'][$record_id];
	}

	$query_select = sf_task_query_select($tsk_id);
	$response = $config['sf_connexion']->queryAll(sf_soql_format(sf_task_query_from($tsk_id), $query_select, array('Id=\''.$record_id.'\''), array()));
	$response = sf_soql_response_parse($response, $query_select);

	if( sizeof($response['records']) != 1 ){
		return false;
	}

	foreach ($response['records'] as $record) {
		$config['salesforce_getrow_cache'][$record_id] = $record; // ajoute le resultat au cache temporaire pour éviter de pourrir de requete SF.
		return $record;
	}

	return false;
}

/** Cette fonction permet de lancer un enregistrement d'un element
 * 	@param int $tsk_id Obligatoire, identifiant de la tâche de synchronisation
 * 	@param $record Obligatoire, élément à enregistrer
 */
function sf_save_row($tsk_id, $record){
	global $config;

	if( !$record ){
		return false;
	}

	$tried = ria_array_get($record, 'recursive', 0);

	if( $tried > RETRY_SAVE_ROW_AMOUNT ){
		$message = ria_array_get($record, 'exception', '');
		throw new RetryOverTaskException("Tache $tsk_id retry over limit, original error : ".$message);
	}

	// dans le cas du passage via api on a pas l'amorcage de SF_login
	if( !is_array($config['salesforce_config']) ){
		$config['salesforce_config'] = (array) json_decode($config['salesforce_config']);
	}

	try{
		try{
			$TaskFactory = new TaskFactory(ria_array_get($config, 'sf_connexion', null));
			$Task = $TaskFactory->make($tsk_id);
			$id = $Task->saveRow($record);
		}catch(RunTimeException $e){
			unset($id);
		}

		if( !isset($id) ){
			// en fonction du type de la classe on lance un parse différent
			switch ($tsk_id) {
				case TSK_CATEGORIE_ADD:
					$id = sf_task_categories($record);
					break;
				case TSK_PRODUCT_ADD:
					$id = sf_task_products($record);
					break;
				case TSK_SELLER_ADD:
					$id = sf_tsk_usr_sellers_add($record);
					break;
				case TSK_USER_ADD:
					$id = sf_task_users($record, PRF_CUST_PRO);
					break;
				case TSK_RESELLER_ADD:
					$id = sf_task_users($record, PRF_RESELLER);
					break;
				case TSK_USR_SELLER_ADD:
					$id = sf_tsk_update_sellers($record);
					break;
				case TSK_SECTOR_ADD:
					$id = sf_tsk_sector($record);
					break;
				case TSK_USR_RELATIONS_ADD:
				case TSK_USR_RELATIONS_DEL:
					$id = sf_tsk_relations($record);
					break;
				case TSK_CONTACT_ADD:
					$id = sf_task_contact($record);
					break;
				case TSK_PRD_LINKED_ADD:
					$id = sf_task_linked($record);
					break;
				case TSK_PRD_PARENT_ADD:
					$id = sf_task_prd_parent($record);
					break;
				case TSK_PRC_CAT_ADD:
					$id = sf_task_prices_categorie($record);
					break;
				case TSK_PRD_PRC_ADD:
					$id = sf_task_prd_prc($record);
					break;
				case TSK_ORD_STATE:
					$id = sf_task_order_states($record);
					break;
				case TSK_CASE_STATE:
					$id = sf_task_case_states($record);
					break;
				case TSK_CASE:
					$id = sf_task_case($record);
					break;
				case TSK_CASE_PRODUCT:
					$id = sf_task_case_product($record);
					break;
				case TSK_PRODUCT_INTERVENTION:
					$id = sf_task_products_intervention($record);
					break;
				default:
					throw new Exception("fonction d'enregistrement non implémenté");
			}
		}
	}catch(RetryTaskException $e){
		$record['recursive'] = $tried + 1;
		$record['exception'] = $e->getMessage();
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_SAVE_ROW, array('tsk_id'=>$tsk_id, 'record'=>$record));
		return false;
	}

	// parfois la fonction de save va retourner true mais n'enregistrera rien
	if( $id === true ){
		return true;
	}

	$advanced_fields = sf_task_query_select_advanced($tsk_id);
	if( $id > 0 && sizeof($advanced_fields) > 0 ){

		if( !isset($config['fld_types']) ){
			$config['fld_types'] = array();
		}

		$datas = array();

		// gestion des champs avancé attention les types de champs doivent être identique sur SF et Ria
		foreach($advanced_fields as $fld){

			// récupère le type du champs
			if( !isset($config['fld_types'][$fld['fld']]) ){
				$config['fld_types'][$fld['fld']] = fld_fields_get_type($fld['fld']);
			}

			// récupère la valeur donnée à SF
			$val = '';
			if( isset($fld['table']) ){
				$val = isset($record[$fld['table']], $record[$fld['table']][$fld['name']]) ? $record[$fld['table']][$fld['name']] : '';
			}else{
				$val = isset($record[$fld['name']]) ? $record[$fld['name']] : '';
			}

			switch( $config['fld_types'][$fld['fld']] ){
				case FLD_TYPE_TEXTAREA:
				case FLD_TYPE_TEXT:
					// ras
					break;
				case FLD_TYPE_BOOLEAN_YES_NO:
					$val = $val=='Oui' ? 'Oui' : 'Non';
					break;
				default :
					throw new Exception("Type de champs avancé non implémenté : type=".$config['fld_types'][$fld['fld']].' : fld='.print_r($fld, true).' : val='.$val);
			}

			// check la valeur précédente
			if( $val != fld_object_values_get( $id, $fld['fld'], $config['i18n_lng'], false, true) ){
				$datas[] = array(
						'cls_id' => CLS_USER,
						'obj_0' => $id,
						'fld_id' => $fld['fld'],
						'lng' => $config['i18n_lng'],
						'value' => $val
					);
			}
		}
		if( sizeof($datas) && !fld_object_values_set_multiple($datas) ){
			throw new Exception("Erreur d'enregistrement des champs avancé : data=".print_r($datas, true));
		}
	}

	return $id;
}

/**
 * Cette class permet la gestion rapide des liste de correspondance
 */
class dependentPicklist{

	private $object; ///> la description de l'objet salesforce
	private $samples = array(); ///> Las liste de correspondance pour chaque liste déroulante pick list
	/**
	 * Instancie la gestion des correspondance de liste
	 *
	 * @param Object $object
	 */
	public function __construct($object)
	{
		$this->object = $object;
	}

	/**
	 * Cette fonction permet pour une picklist de récupérer la liste des correspondance
	 *
	 * @param string $picklist
	 * @return array
	 */
	public function sample($picklist){
		if( array_key_exists($picklist, $this->samples) ){
			return $this->samples[$picklist];
		}

		$finalApplicableOptions = array();
		foreach( $this->object->fields as $field ){
			if($field->name == $picklist){
				foreach($field->picklistValues as $index => $item ){
					$finalApplicableOptions[$index]['label'] = $item->label;
				}
			}
		}

		foreach( $this->object->fields as $field ){
			if(isset($field->controllerName) && $field->controllerName == $picklist){
				foreach($field->picklistValues as $index => $item ){
					$byteArr = $item->validFor;
					$maparray = array();
					$map = "";
					foreach(str_split($byteArr) as $c){
						$maparray[] = sprintf("%08b", ord($c));
					}
					$map = implode("", $maparray);
					for ($k = 0; $k < strlen($map); $k++){
						if($map{$k} == "1"){
							$finalApplicableOptions[$k]['dependencies'][$field->name][] = $item->label;
						}
					}
				}
			}
		}

		$this->samples[$picklist] = $finalApplicableOptions;

		return $this->samples[$picklist];
	}
}
/// @}
// \endcond

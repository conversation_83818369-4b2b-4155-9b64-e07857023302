<?php
// \cond onlyria

require_once('db.inc.php');

/** \defgroup model_contacts_types Types de contacts
 *	\ingroup model_contacts crm
 *	Ce module comprend les fonctions nécessaires à la gestion des types de contacts.
 *	Les types aident au classement des contacts société.
 * @{
 */

/**	Permet l'ajout d'un type de contact.
 *	@param string $name Désignation du type de contact (ne peut être vide)
 *	@param string $desc Description du type de contact.
 *	@return int|bool l'identifiant attribué au type en cas de succès, false en cas d'échec
 */
function cnt_types_add( $name, $desc ){
	global $config;

	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('insert into cnt_types (type_tnt_id, type_name,type_desc,type_date_created) values ('.$config['tnt_id'].',\''.addslashes($name).'\',\''.addslashes($desc).'\',now());');
	if( $res )
		return ria_mysql_insert_id();
	return false;
}

/**	Cette fonction permet la mise à jour d'un type de contact.
 *	@param int $id Identifiant du type de contact
 *	@param string $name Désignation du type de contact (ne peut être vide)
 *	@param string $desc Description du type de contact
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cnt_types_update( $id, $name, $desc ){
	global $config;
	if( !cnt_types_exists($id) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	return ria_mysql_query('update cnt_types set type_name=\''.addslashes($name).'\', type_desc=\''.addslashes($desc).'\' where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
}

/**	Cette fonction permet la suppression "logique" d'un type de contact.
 *	@param int $id Identifiant du type de contact à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cnt_types_del( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_query('update cnt_types set type_date_deleted=now() where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
}

/**	Cette fonction permet la validation d'un identifiant de type de contact.
 *	@param int $id Identifiant du type de contact à vérifier.
 *	@return bool true si l'identifiant est valide et correspond à un type de contact enregistré dans la base de données.
 *	@return bool false dans le cas contraire.
 */
function cnt_types_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select type_id from cnt_types where type_tnt_id='.$config['tnt_id'].' and type_id='.$id));
}

/**	Cette fonction permet le chargement d'un ou plusieurs types de contact
 *	@param int $id Facultatif, identifiant d'un type de contact sur lequel filtrer le résultat.
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de contact
 *			- name : désignation du type de contact
 *			- desc : description du type de contact
 */
function cnt_types_get( $id=0 ){
	global $config;

	$sql = '
		select type_id as id, type_name as name, type_desc as "desc", count(ctt_cnt_id) as contacts
		from cnt_types
			left join cnt_contacts_types on (type_tnt_id=ctt_tnt_id and type_id=ctt_type_id)
		where type_tnt_id='.$config['tnt_id'].' and type_date_deleted is null
	';

	if( is_numeric($id) && $id>0 ) $sql .= ' and type_id='.$id;
	$sql .= '
		group by type_id, type_name, type_desc
		order by type_name
	';

	return ria_mysql_query($sql);
}

/// @}

// \endcond

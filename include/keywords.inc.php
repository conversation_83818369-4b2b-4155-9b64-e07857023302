<?php


/** \defgroup model_keywords Mots clés
 *  \ingroup seo
 *	Ce module comprend les fonctions nécessaires à la génération des mots clés utilisés dans les pages du site.
 *	@{
 */

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('categories.inc.php');
require_once('products.inc.php');

// \cond onlyria

/// \private
/// STR_STOPWORDS est défini dans le fichier strings.inc.php
define( 'META_STOPWORDS', STR_STOPWORDS );

/// \private
/// Nombre de caractères maximum autorisés dans la balise meta Description
define( 'META_DESCRIPTION_LIMIT', 156 );

/// \private
/// Nombre de caractères maximum autorisés dans la balise meta Keywords
define( 'META_KEYWORDS_LIMIT', 1000 );

/** Cette fonction permet la récupération du tableau des contantes pour les phrases de référencement personnalisé
 * @return array le tableau des constantes avec leurs informations
 */
function seo_templates_constants_get(){
	return array(
		'SEO_PRD_TITLE' => array(
			'name' => 'Titre du produit',
			'fld_id' => _FLD_PRD_TITLE,
			'cls_id' => array(CLS_PRODUCT),
		),
		'SEO_CAT_TITLE' => array(
			'name' => 'Titre de la catégorie',
			'fld_id' => _FLD_CAT_TITLE,
			'cls_id' => array(CLS_CATEGORY, CLS_PRODUCT),
		),
		'SEO_BRD_TITLE' => array(
			'name' => 'Titre de la marque',
			'fld_id' => _FLD_BRD_TITLE,
			'cls_id' => array(CLS_BRAND, CLS_PRODUCT),
		),
		'SEO_PRD_COUNT' => array(
			'name' => 'Nombre de produits',
			'fld_id' => null,
			'cls_id' => array(CLS_CATEGORY, CLS_BRAND),
		),
	);
}

/** Cette fonction permet de récupérer les informations liées à une constante (phrases de référencement personnalisé)
 * @param string $code Obligatoire, code de la constante
 * @return array un tableau contenant les informations de la constante
 */
function seo_templates_constants_get_by_code($code) {
	if (trim($code) == '') {
		return false;
	}

	$ar_constants = seo_templates_constants_get();
	if (!array_key_exists($code, $ar_constants)) {
		return false;
	}

	return $ar_constants[$code];
}

/** Cette fonction permet de récupérer les informations des constantes en filtrant par classes
 * @param int $cls_id Obligatoire, identifiant de la classe d'objet
 * @return array un tableau comprenant les informations des constantes récupérées
 */
function seo_templates_constants_get_by_class($cls_id) {
	if (!is_numeric($cls_id) || !$cls_id) {
		return false;
	}

	$ar_constants = seo_templates_constants_get();

	$ar_temp = array();
	foreach ($ar_constants as $key => $value) {
		if (!in_array($cls_id, $value['cls_id'])) {
			continue;
		}

		$ar_temp[$key] = $value;
	}

	return $ar_temp;
}

/** Cette fonction permet de récupérer les classes pour lequel le référencement personnalisé est supporté
 * @return array le tableau des classes supportées
 */
function seo_templates_constants_get_classes() {
	$ar_constants = seo_templates_constants_get();

	$classes = array();

	foreach ($ar_constants as $key => $value) {
		$classes = array_merge($value['cls_id'], $classes);
	}

	return array_unique($classes);
}

// \endcond

/**	Retourne une description de la page adaptée au contenu en cours de visualisation.
 *	@todo Cette fonction est très liée à la boutique bigship.
 */
function page_description(){
	global $config;

	$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], i18n::getLang(), $website, array(_FLD_WST_TAG_DESC=>'meta_desc') );
		$website['meta_desc'] = $tsk_website['meta_desc'];
	}

	if( defined('PAGE_DESC') ){
		if( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI'])!='' && trim($_SERVER['REQUEST_URI'])!='/' ){
			$tag_desc = rew_tags_get_desc( $_SERVER['REQUEST_URI'] );
			if( trim($tag_desc)!='' ){
				return htmlspecialchars( $tag_desc );
			}
		}
	}

	if( defined('PAGE_DESC') ){
		return (i18n::getLang()!=$config['i18n_lng'] ? i18n::get(PAGE_DESC) : PAGE_DESC );
	}elseif(defined("MODULE_CMS")) {
		$_GET['cat'] = isset($_GET['cat']) ? $_GET['cat'] : (MODULE_CMS>1 ? MODULE_CMS : 0);
		return htmlspecialchars(page_obj_desc( CLS_CMS, $_GET['cat']));
	} elseif(defined('MODULE_FAQ') && isset($_GET['cat'])) {
		return htmlspecialchars(page_obj_desc( CLS_FAQ_CAT, $_GET['cat'] ));
	} elseif(defined("MODULE_CAT_NEWS")) {
		$_GET['cat'] = isset($_GET['cat']) ? $_GET['cat'] : (MODULE_CAT_NEWS>1 ? MODULE_CAT_NEWS : 0);

		if( $_GET['cat']>0 && $news_cat = news_categories_get($_GET['cat']) ) {
			$news_cat = ria_mysql_fetch_array($news_cat);
			if($news_cat['desc']){
				return htmlspecialchars($news_cat['desc']);
			}else{
				return $news_cat['name'].' sur le site '.$config['site_name'];
			}
		} else {
			return htmlspecialchars($config['meta_description']);
		}
	} elseif( isset($_GET['n']) ) {
		return	htmlspecialchars(page_obj_desc( CLS_NEWS, $_GET['n']));
	} elseif( isset($_GET['prd'], $_GET['cat']) ){
		return	htmlspecialchars(page_obj_desc( CLS_PRODUCT, array($_GET['prd'], $_GET['cat']) ));
	}elseif( isset($_GET['new']) ){
		return htmlspecialchars('Nouveaux articles du catalogue '.$config['site_name'].'.');
	}elseif( isset($_GET['promotions']) ){
		return htmlspecialchars('Promotions en cours sur le catalogue '.$config['site_name'].'.');
	}elseif( isset($_GET['destockage']) ){
		return htmlspecialchars('Déstockage sur le catalogue '.$config['site_name'].'.');
	}elseif( isset($_GET['meilleures-ventes']) ){
		return htmlspecialchars('Déstockage sur le catalogue '.$config['site_name'].'.');
	}elseif( isset($_GET['qst']) ){
		return htmlspecialchars(page_obj_desc( CLS_FAQ_QST, $_GET['qst']));
	}elseif( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ){
		return htmlspecialchars(page_obj_desc( CLS_CATEGORY, $_GET['cat']));
	}elseif( isset($_GET['brd']) && is_numeric($_GET['brd']) ){
		return htmlspecialchars(page_obj_desc( CLS_BRAND, $_GET['brd']));
	}elseif( isset($_GET['str']) ){
		return htmlspecialchars(page_obj_desc( CLS_STORE, $_GET['str']));
	}elseif( isset($_GET['q']) && trim($_GET['q']) != '' ) {
		return htmlspecialchars( i18n::get('Résultats de recherche pour')." '".$_GET['q']."'");
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/cart.php' ){
		return htmlspecialchars('Visualiser le contenu de votre panier et passez commande sur le site '.$config['site_name'].'.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/sitemap.php' ){
		return htmlspecialchars('Plan du site '.$config['site_name'].'.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/users/register.php' ){
		return htmlspecialchars('Créer un compte client sur le site '.$config['site_name'].'. La création d\'un compte est ouverte aux particuliers ainsi qu\'aux professionnels.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/users/particulier.php' ){
		return htmlspecialchars('Création d\'un compte client pour les particuliers.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/users/professionnel.php' ){
		return htmlspecialchars('Création d\'un compte client pour les professionnels.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/contact.php' ){
		return htmlspecialchars('Toutes les informations pour nous contacter.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/contact-result.php' ){
		return htmlspecialchars('Merci de nous avoir contacté');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/society.php' ){
		return htmlspecialchars('Présentation de la société '.$config['site_name'].' et de son réseau de distribution.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/warranty.php' ){
		return htmlspecialchars('Nos conditions de garantie');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/delivery.php' ){
		return htmlspecialchars('Présentation de nos différents modes de livraison');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/cgv.php' ){
		return htmlspecialchars('Conditions Générales de Vente du site '.$config['site_name'].'.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/newsletter/index.php' ){
		return htmlspecialchars('Gérez votre inscription à la newsletter '.$config['site_name'].'.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/index.php' ){
		return htmlspecialchars('Carte de france des magasins '.$config['site_name'].', un réseau de plus de 42 magasins dans toute la france.');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/nord.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' du nord de la France');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/bretagne.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' de bretagne');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/atlantique.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' sur la côte Atlantique');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/guadeloupe.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' de Guadeloupe');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/pyrenees.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' du sud-ouest de la France');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/mediterranee-1.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' sur la côte méditerranéenne');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/mediterranee-2.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' sur la côte méditerranéenne');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/corse.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' en Corse');
	}elseif( $_SERVER['SCRIPT_FILENAME']==$config['site_dir'].'/reseau/est.php' ){
		return htmlspecialchars('Magasins '.$config['site_name'].' de l\'est de la France');
	}

	// Récupère si possible le référencement personnalisée de la page
	if( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI'])!='' && trim($_SERVER['REQUEST_URI'])!='/' ){
		$tag_desc = rew_tags_get_desc( $_SERVER['REQUEST_URI'] );
		if( trim($tag_desc)!='' ){
			return htmlspecialchars( $tag_desc );
		}
	}

	// Par défaut, la description du site est retournée
	return htmlspecialchars($website['meta_desc']);
}

/**	Retourne un titre adapté à la page en cours de consultation
 * 	@param bool Si un titre automatique est chargé selon l'url où l'on se trouve (par défaut à false)
 */
function page_title( $autoload=false ){
	global $config;

	// Récupère si possible le référencement personnalisée de la page
	if( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI'])!='' && trim($_SERVER['REQUEST_URI'])!='/' ){
		$tag_title = rew_tags_get_title( $_SERVER['REQUEST_URI'] );
		if( trim($tag_title)!='' ){
			return htmlspecialchars( $tag_title );
		}
	}

	$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], i18n::getLang(), $website, array(_FLD_WST_TAG_TITLE=>'site_title') );
		$website['site_title'] = $tsk_website['site_title'];
	}

	if( defined('PAGE_TITLE_SOFT') || defined('PAGE_TITLE') ){
		if( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI'])!='' && trim($_SERVER['REQUEST_URI'])!='/' ){
			$tag_title = rew_tags_get_title( $_SERVER['REQUEST_URI'] );
			if( trim($tag_title)!='' ){
				return htmlspecialchars( $tag_title );
			}
		}
	}

	if(defined('PAGE_TITLE_SOFT')) {
		return (i18n::getLang()!=$config['i18n_lng'] ? i18n::get(PAGE_TITLE_SOFT) : PAGE_TITLE_SOFT );
	}elseif(defined('PAGE_TITLE')) {
		return (i18n::getLang()!=$config['i18n_lng'] ? i18n::get(PAGE_TITLE) : PAGE_TITLE ).' - '.htmlspecialchars($website['site_title']);
	}elseif(defined('PAGE_TITLE_NO_TRANSLATE')) {
		if( isset($_GET['prd'], $_GET['cat']) ){
			$surcharge = page_obj_title( CLS_PRODUCT, array($_GET['prd'], $_GET['cat']), true );
			if( trim($surcharge) ){
				return htmlspecialchars( $surcharge );
			}
		}
		return PAGE_TITLE_NO_TRANSLATE.' - '.htmlspecialchars($website['site_title']);
	} elseif(defined('MODULE_CMS')) {
		return htmlspecialchars(page_obj_title( CLS_CMS, isset($_GET['cat']) ? $_GET['cat'] : (MODULE_CMS>1 ? MODULE_CMS : 0) ));
	} elseif(defined('MODULE_FAQ') && isset($_GET['cat'])) {
		return htmlspecialchars(page_obj_title( CLS_FAQ_CAT, $_GET['cat'] ));
	} elseif(defined("MODULE_CAT_NEWS")) {
		$_GET['cat'] = isset($_GET['cat']) ? $_GET['cat'] : (MODULE_CAT_NEWS>1 ? MODULE_CAT_NEWS : 0);

		if( $_GET['cat']>0 && $news_cat = news_categories_get($_GET['cat']) ) {
			$news_cat = ria_mysql_fetch_array($news_cat);
			if($news_cat['name'])
				return htmlspecialchars($news_cat['name']).' - '.htmlspecialchars($website['site_title']);
		} else {
			return i18n::get('Actualités', 'METADATA').' - '.htmlspecialchars($website['site_title']);
		}
	} elseif( isset($_GET['brd']) && is_numeric($_GET['brd']) ){
		return htmlspecialchars(page_obj_title( CLS_BRAND, $_GET['brd'] ));
	} elseif( isset($_GET['n']) ) {
		return htmlspecialchars(page_obj_title( CLS_NEWS, $_GET['n'] ));
	} elseif( isset($_GET['q']) && trim($_GET['q']) != '' ) {
		return htmlspecialchars( i18n::get('Résultats de recherche pour')." '".$_GET['q']."'".' - '.$website['site_title'] );
	} elseif( isset($_GET['prd'], $_GET['cat']) ){
		return htmlspecialchars(page_obj_title( CLS_PRODUCT, array($_GET['prd'], $_GET['cat']) ));
	} elseif(isset($_GET['promotions'])) {
		if( isset($_GET['cat']) && $_GET['cat']>0 ){
			$r_cat = prd_categories_get($_GET['cat']);
			if( $r_cat!==false && ria_mysql_num_rows($r_cat)>0 ){
				$cat = ria_mysql_fetch_array( $r_cat );
				if( $cat['tag_title'] )
					return i18n::get('Promotions').' - '.$cat['tag_title'];
				else
					return i18n::get('Promotions').' - '.$cat['title'].' - '.htmlspecialchars($website['site_title']);
			} else
				return i18n::get('Promotions').' - '.htmlspecialchars($website['site_title']);
		}
		return i18n::get('Promotions').' - '.htmlspecialchars($website['site_title']);
	} elseif(isset($_GET['new']) && trim($_GET['new']) != '') {
		if( isset($_GET['cat']) && $_GET['cat']>0 ){
			$r_cat = prd_categories_get($_GET['cat']);
			if( $r_cat!==false && ria_mysql_num_rows($r_cat)>0 ){
				$cat = ria_mysql_fetch_array( $r_cat );
				if( $cat['tag_title'] )
					return i18n::get('Nouveautés').' - '.$cat['tag_title'];
				else
					return i18n::get('Nouveautés').' - '.$cat['title'].' - '.htmlspecialchars($website['site_title']);
			} else
				return i18n::get('Nouveautés').' -'.htmlspecialchars($website['site_title']);
		}
		return i18n::get('Nouveautés').' - '.htmlspecialchars($website['site_title']);
	} elseif(isset($_GET['meilleures-ventes'])) {
		if( isset($_GET['cat']) && $_GET['cat']>0 ){
			$r_cat = prd_categories_get($_GET['cat']);
			if( $r_cat!==false && ria_mysql_num_rows($r_cat)>0 ){
				$cat = ria_mysql_fetch_array( $r_cat );
				if( $cat['tag_title'] )
					return i18n::get('Meilleures ventes').' - '.$cat['tag_title'];
				else
					return i18n::get('Meilleures ventes').' - '.$cat['title'].' - '.htmlspecialchars($website['site_title']);
			} else
				return i18n::get('Meilleures ventes').' - '.htmlspecialchars($website['site_title']);
		}
		return i18n::get('Meilleures ventes').' - '.htmlspecialchars($website['site_title']);
	} elseif( isset($_GET['qst']) ){
		return htmlspecialchars(page_obj_title( CLS_FAQ_QST, $_GET['qst'] ));
	} elseif( isset($_GET['cat']) && $_GET['cat'] > 0){
		return htmlspecialchars(page_obj_title( CLS_CATEGORY, $_GET['cat'] ));
	} elseif( isset($_GET['str']) ) {
		return htmlspecialchars(page_obj_title( CLS_STORE, $_GET['str'] ));
	} elseif( isset($_GET['t']) ){
		return htmlspecialchars(page_obj_title( CLS_TYPE_DOCUMENT, $_GET['t'] ));
	} elseif( $autoload && isset($_SERVER['PATH_INFO']) && trim($_SERVER['PATH_INFO'])!='' && trim($_SERVER['PATH_INFO'])!='/' ){
		// Mise en place d'un titre par défaut en fonction de l'URL où l'on est

		if( http_response_code() == 404 ){
			return i18n::get('Page introuvable', 'METADATA').' - '.$website['site_title'];
		}

		$ar_title_autoload = [
			'/connexion/'								=> i18n::get('Connexion', 'METADATA'),
			'/mot-de-passe-perdu/'						=> i18n::get('Mot de passe oublié ?', 'METADATA'),
			'/demande-ouverture-compte/'				=> i18n::get('Demande d\'ouverture de compte', 'METADATA'),
			'/premiere-connexion/'						=> i18n::get('Première connexion', 'METADATA'),
			'/telechargements/'							=> i18n::get('Téléchargements', 'METADATA'),
			'/sav/'										=> i18n::get('Faire une demande de retour SAV', 'METADATA'),
			'/foire-aux-questions/'						=> i18n::get('Foire aux questions', 'METADATA'),
			'/mon-panier/'								=> i18n::get('Mon panier', 'METADATA'),
			'/mon-compte/'								=> i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/commande-rapide/'				=> i18n::get('Commande rapide', 'METADATA'),
			'/mon-compte/mes-commandes/'				=> i18n::get('Historique de mes commandes', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/mes-devis/'					=> i18n::get('Mes devis', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/mes-paniers/'					=> i18n::get('Mes paniers sauvegardés', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/informations-personnelles/'	=> i18n::get('Informations personnelles', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/carnet-adresses/'				=> i18n::get('Mes adresses de livraison', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/mot-de-passe/'					=> i18n::get('Mot de passe', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/options/'					=> i18n::get('Mes options', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/mon-compte/alert-emails/'					=> i18n::get('Alertes emails', 'METADATA').' - '.i18n::get('Mon compte', 'METADATA'),
			'/commander/livraison/'						=> i18n::get('Livraison', 'METADATA'),
			'/commander/paiement/'						=> i18n::get('Paiement', 'METADATA'),
			'/commander/recapitulatif/'					=> i18n::get('Récapitulatif', 'METADATA'),
			'/commander/confirmation/'					=> i18n::get('Confirmation', 'METADATA'),
			'/nous-contacter/'							=> i18n::get('Nous contacter', 'METADATA'),
			'/actualites/'								=> i18n::get('Actualités', 'METADATA'),
			'/nos-marques/'								=> i18n::get('Nos marques', 'METADATA'),
			'/reinitialisation-mot-de-passe/'			=> i18n::get('Nouveau mot de passe', 'METADATA'),
			'/nouveautes/'								=> i18n::get('Nouveautés', 'METADATA'),
			'/promotions/'								=> i18n::get('Promotions', 'METADATA'),
			'/conditions-generales-de-vente/'			=> i18n::get('Conditions générales de ventes', 'METADATA'),
			'/conditions-generales-dutilisation/'		=> i18n::get('Conditions générales d\'utilisation', 'METADATA'),
			'/commande-rapide/'							=> i18n::get('Commande rapide', 'METADATA'),
		];

		$path = preg_replace('/^\/('.i18n::getLang().')\//', '/', $_SERVER['PATH_INFO']);

		if( array_key_exists($path, $ar_title_autoload) ){
			return $ar_title_autoload[ $path ].' - '.$website['site_title'];
		}else{
			$title_autoload = '';

			if( isset($_GET['ord']) && is_numeric($_GET['ord']) && $_GET['ord'] > 0 ){
				$title_autoload  = ( isset($_GET['devis']) && $_GET['devis'] == 1 ? i18n::get('Devis n°') : i18n::get('Commande n°') ).$_GET['ord'].' - ';
				$title_autoload .= ( isset($_GET['devis']) && $_GET['devis'] == 1 ? i18n::get('Historique de mes devis', 'METADATA') : i18n::get('Historique de mes commandes', 'METADATA') ).' - '.i18n::get('Mon compte', 'METADATA');
			}

			return $title_autoload.' - '.$website['site_title'];
		}
	}

	// Par défaut, la description du site est retournée
	return htmlspecialchars($website['site_title']);
}

/** Retourne les mots à utiliser dans une page donnée. Les mots clés sont constitués en prenant dans l'ordre :
 *	- le produit en cours de consultation
 *	- la catégorie en cours de consultation
 *	- toutes les catégories parentes de la catégorie en cours
 *	- les mots clés du site
 *	@deprecated Cette fonction ne doit plus être utilisée
 */
function page_keywords(){
	return '';
}

/** Cette fonction permet la génération des balise link rel="alternate" en fonction des diférente langue pour un site
 * 	@return string L'html des balise link
 */
function page_alternate_hreflang(){
	global $config;

	$r_wst_languages = wst_websites_languages_get($config['wst_id']);
	if (!$r_wst_languages || !ria_mysql_num_rows($r_wst_languages)) {
		return '';
	}

	ob_start();
	while ($wst = ria_mysql_fetch_assoc($r_wst_languages)) {
		if (trim($wst['url']) == '') {
			continue;
		}
		?>
			<link rel="alternate" href="<?php echo $wst['url']?>" hreflang="<?php echo $wst['lng_code']?>"/>
		<?php
	}
	return ob_get_clean();
}

// \cond onlyria
/** Retourne le titre pour un objet donné
 *	@param int $classe Obligatoire, identifiant de classe concerné
 *	@param int|array $obj Facultatif, identifiant d'un objet de la classe correspondante au modèle sur lequel filtrer le résultat (ou tableau d'identifiants pour les clés composées)
 *	@param bool $return_override Facultatif, si true ne retournera que la surcharge, si false que l'automatique, si null retour la surcharge en priorité sinon l'auto
 *	@param bool $admin Facultatif, si true permet de ne pas utiliser le cache pour la récupération des champs avancés
 *
 *	@return string une chaine de caractère
 */
function page_obj_title( $classe, $obj, $return_override=null, $admin=false ){
	global $config;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj) > COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ){
			return false;
		}
		if( $obj == 0 ){
			$obj = array();
		}else{
			$obj = array( $obj );
		}
	}

	if( !isset($obj[0]) ){
		return false;
	}

	$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], i18n::getLang(), $website, [_FLD_WST_TAG_TITLE=>'site_title'] );
		$website['site_title'] = $tsk_website['site_title'];
	}

	$seo_objects = array();

	$title_auto = $website['site_title'];
	$title_override = '';

	switch($classe){
		case CLS_PRODUCT:{
			$rp = prd_products_get_simple( $obj[0] );
			if( $rp && $obj[1] && ria_mysql_num_rows($rp) ){
				$p = ria_mysql_fetch_array( $rp );

				if( i18n::getLang()==$config['i18n_lng'] ){
					$title = prd_classify_get_tagtitle( $obj[0], $obj[1] );
					if( trim($title)!='' ){
						$title_override = $title;
					}
				} else {
					$title = fld_object_values_get( array($obj[1], $obj[0]), _FLD_CLY_TAG_TITLE, i18n::getLang(), false, $admin );
					if( trim($title)!='' ){
						$title_override = $title;
					}
				}

				$title = $p['title'];

				// traduction du titre
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk = fld_object_values_get( $obj[0], _FLD_PRD_TITLE, i18n::getLang(), false, $admin );
					$title = trim($tsk) ? $tsk : $title;
				}

				$rcat = prd_categories_get( $obj[1] );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					$cat = ria_mysql_fetch_array( $rcat );
					if( i18n::getLang()!=$config['i18n_lng'] ){
						$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], i18n::getLang(), $cat, array(_FLD_CAT_TITLE=>'title',_FLD_CAT_NAME=>'name', _FLD_CAT_TAG_TITLE=>'tag_title') );
						$cat['title'] = $tsk_cat['title'];
						$cat['name'] = $tsk_cat['name'];
						$cat['tag_title'] = $tsk_cat['tag_title'];

						$tsk = fld_object_values_get( $cat['id'], _FLD_CAT_TAG_TITLE, i18n::getLang(), false, $admin );
						$cat['tag_title'] = $tsk;
					}

					$title .= $cat['tag_title'] ? ' - '.$cat['tag_title'] : ' - '.$cat['name'];
				}

				$title_auto =   $title.' - '.$website['site_title'];

				$date_created = dateheureparse( str_replace(' à ', ' ', $p['date_created']).':00' );
				switch ($config['tnt_id']) {
					case 16:
						if (isdateheure($date_created) && strtotime($date_created) >= strtotime('2017-02-07 11:00:00')) {
							$title = str_replace( ' - Animal & Co', '', $title );
							$title_auto =   $title.' - Animal & Co';
						}
						break;
					case 43:
						if (isdateheure($date_created) && strtotime($date_created) >= strtotime('2017-02-17 11:00:00')) {
							$title = $p['title'];

							// traduction du titre
							if( i18n::getLang()!=$config['i18n_lng'] ){
								$tsk = fld_object_values_get( $obj[0], _FLD_PRD_TITLE, i18n::getLang(), false, $admin );
								$title = isset($tsk) && trim($tsk) ? $tsk : $title;
							}

							if(isset($p["brd_title"]) && $p["brd_title"] != ""){
								$title = $p["brd_title"] . " " . $title;
							}

							$title_auto =   $title.' - Purebike';
						}
						break;
					default:
						break;
				}

				$variables = array(
					$p['id'],
					$obj[1],
					$p['brd_id'] !== null ? $p['brd_id'] : false
				);
			}
			$seo_objects = array($obj[1], $obj[0]);

			break;
		}
		case CLS_CMS:{
			if(cms_categories_exists($obj[0])) {
				$cms_page_load = ria_mysql_fetch_array(cms_categories_get($obj[0]));
				if( trim($cms_page_load['tag_title']) )
					$title_override = $cms_page_load['tag_title'];
				elseif($cms_page_load['name'])
					$title_auto = $cms_page_load['name'].' - '.$website['site_title'];
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_BRAND: {
			$rbrd = prd_brands_get( $obj[0], true );
			if( $rbrd ){
				$brd = ria_mysql_fetch_array($rbrd);
				if( trim($brd['tag_title']) ) {
					$title_override =  $brd['tag_title'];
				} else {
					$title_auto = $brd['title'].' - '.$config['site_name'];
				}

				$variables = array(
					$brd['id'],
					$brd['products']
				);

			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_NEWS:  {
			// Actualités
			$rnews = news_get($obj[0]);
			if( $rnews && ria_mysql_num_rows($rnews) ){
				$news = ria_mysql_fetch_array($rnews);
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_cat = fld_translates_get( CLS_NEWS, $news['id'], i18n::getLang(), $news, array(_FLD_NEWS_NAME=>'name') );
					$news['name'] = $tsk_cat['name'];
					// $cat['tag_title'] = fld_object_values_get( $cat['id'], _FLD_CAT_TAG_TITLE, i18n::getLang() );
				}

				if( trim($news['tag_title']) )
					$title_override = $news['tag_title'];
				else
					$title_auto = $news['name'].' - '.$config['site_name'].' - '.$config['site_desc'];
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_FAQ_CAT:  {
			$rcfaq = faq_categories_get( $obj[0] );
			if( $rcfaq && ria_mysql_num_rows($rcfaq) ){
				$cfaq = ria_mysql_fetch_array( $rcfaq );

				if( $cfaq['tag_title']!='' ){
					$title_override = $cfaq['tag_title'];
				}elseif( trim($cfaq['name']) ){
					$title_auto =  html_strip_tags($cfaq['name']).' - '.$website['site_title'];
				}
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_FAQ_QST:  {
			$rfaq = faq_questions_get( $obj[0] );
			if( $rfaq && ria_mysql_num_rows($rfaq) ){
				$faq = ria_mysql_fetch_array( $rfaq );

				if( $faq['tag_title']!='' )
					$title_override = $faq['tag_title'];
				elseif( trim($faq['name']) )
					$title_auto =  html_strip_tags($faq['name']);
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_CATEGORY:  {
			$rcat = prd_categories_get( $obj[0] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array( $rcat );
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], i18n::getLang(), $cat, array(_FLD_CAT_TITLE=>'title',_FLD_CAT_NAME=>'name',_FLD_CAT_DESC=>'desc') );
					$cat['title'] = $tsk_cat['title']; $cat['name'] = $tsk_cat['name'];  $cat['desc'] = $tsk_cat['desc'];
					$cat['tag_title'] = fld_object_values_get( $cat['id'], _FLD_CAT_TAG_TITLE, i18n::getLang(), false, $admin );
				}

				if( $cat['tag_title']!='' ) {
					$title_override = $cat['tag_title'];
				} else {
					$title_auto = $cat['title'].' - '. $website['site_title'];
				}

				$variables = array(
					$cat['id'],
					$cat['products']
				);

			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_STORE:  {
			$depts = array();$depts["01"] = "Ain";$depts["02"] = "Aisne";$depts["03"] = "Allier";$depts["04"] = "Alpes de Haute Provence";$depts["05"] = "Hautes Alpes";
			$depts["06"] = "Alpes Maritimes";$depts["07"] = "Ardèche";$depts["08"] = "Ardennes";$depts["09"] = "Ariège";$depts["10"] = "Aube";$depts["11"] = "Aude";
			$depts["12"] = "Aveyron";$depts["13"] = "Bouches du Rhône";$depts["14"] = "Calvados";$depts["15"] = "Cantal";$depts["16"] = "Charente";$depts["17"] = "Charente Maritime";
			$depts["18"] = "Cher";$depts["19"] = "Corrèze";$depts["20"] = "Corse";$depts["21"] = "Côte d'Or";$depts["22"] = "Côtes d'Armor";$depts["23"] = "Creuse";
			$depts["24"] = "Dordogne";$depts["25"] = "Doubs";$depts["26"] = "Drôme";$depts["27"] = "Eure";$depts["28"] = "Eure et Loir";$depts["29"] = "Finistère";
			$depts["30"] = "Gard";$depts["31"] = "Haute Garonne";$depts["32"] = "Gers";$depts["33"] = "Gironde";$depts["34"] = "Hérault";$depts["35"] = "Ille et Vilaine";
			$depts["36"] = "Indre";$depts["37"] = "Indre et Loire";$depts["38"] = "Isère";$depts["39"] = "Jura";$depts["40"] = "Landes";$depts["41"] = "Loir et Cher";$depts["42"] = "Loire";
			$depts["43"] = "Haute Loire";$depts["44"] = "Loire Atlantique";$depts["45"] = "Loiret";$depts["46"] = "Lot";$depts["47"] = "Lot et Garonne";$depts["48"] = "Lozère";
			$depts["49"] = "Maine et Loire";$depts["50"] = "Manche";$depts["51"] = "Marne";$depts["52"] = "Haute Marne";$depts["53"] = "Mayenne";$depts["54"] = "Meurthe et Moselle";
			$depts["55"] = "Meuse";$depts["56"] = "Morbihan";$depts["57"] = "Moselle";$depts["58"] = "Nièvre";$depts["59"] = "Nord";$depts["60"] = "Oise";$depts["61"] = "Orne";
			$depts["62"] = "Pas de Calais";$depts["63"] = "Puy de Dôme";$depts["64"] = "Pyrénées Atlantiques";$depts["65"] = "Hautes Pyrénées";$depts["66"] = "Pyrénées Orientales";
			$depts["67"] = "Bas Rhin";$depts["68"] = "Haut Rhin";$depts["69"] = "Rhône";$depts["70"] = "Haute Saône";$depts["71"] = "Saône et Loire";$depts["72"] = "Sarthe";
			$depts["73"] = "Savoie";$depts["74"] = "Haute Savoie";$depts["75"] = "Paris";$depts["76"] = "Seine Maritime";$depts["77"] = "Seine et Marne";$depts["78"] = "Yvelines";
			$depts["79"] = "Deux Sèvres";$depts["80"] = "Somme";$depts["81"] = "Tarn";$depts["82"] = "Tarn et Garonne";$depts["83"] = "Var";$depts["84"] = "Vaucluse";
			$depts["85"] = "Vendée";$depts["86"] = "Vienne";$depts["87"] = "Haute Vienne";$depts["88"] = "Vosges";$depts["89"] = "Yonne";$depts["90"] = "Territoire de Belfort";
			$depts["91"] = "Essonne";$depts["92"] = "Hauts de Seine";$depts["93"] = "Seine St Denis";$depts["94"] = "Val de Marne";$depts["95"] = "Val d'Oise";$depts["97"] = "DOM";

			$rstr = dlv_stores_get( $obj[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( $rstr && ria_mysql_num_rows($rstr) ){
				$str = ria_mysql_fetch_array( $rstr );

				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_str = fld_translates_get( CLS_STORE, $str['id'], i18n::getLang(), $str, array(_FLD_STR_NAME=>'name') );
					$str['name'] = $tsk_str['name'];

					$str['tag_title'] = fld_object_values_get($obj[0], _FLD_STR_TAG_TITLE, i18n::getLang(), false, $admin);
				}

				$department = '';
				if( in_array($str['country'], array('France', 'FRANCE', 'FR', 'fr', 'Fr')) && $str['zipcode']!='' ){
					$str['zipcode'] = strlen($str['zipcode'])<=4 ? '0'.$str['zipcode'] : $str['zipcode'];
					$department = isset($depts[ substr($str['zipcode'], 0, 2) ]) ? $depts[ substr($str['zipcode'], 0, 2) ] : '';
				}

				if( $str['tag_title']!='' )
					$title_override =  $str['tag_title'];
				elseif( trim($str['name'])!='' && $str['name']!=$str['city'] )
					$title_auto =  $str['name'].' - '.$str['city'].( $department!='' ? ' - '.$department : '' ) ;
				elseif( trim($str['name'])!='' )
					$title_auto =  $str['name'].( $department!='' ? ' - '.$department : '' ) ;
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_TYPE_DOCUMENT:
			$ar_title = [];

			$r_parent = ria_mysql_query('
				select type_parent_id
				from doc_types
				where type_tnt_id = '.$config['tnt_id'].'
					and type_id = '.$obj[0].'
			');

			$parent = 0;
			if( $r_parent && ria_mysql_num_rows($r_parent) ){
				$parent = ria_mysql_fetch_assoc( $r_parent );
				$parent = $parent['type_parent_id'];
			}

			$i = 0;
			while( $parent ){
				if( ($i++)>20 ){
					// Sécurité
					break;
				}

				// Info sur le parent
				$r_type_parent = ria_mysql_query('
					select type_name, type_url_alias, type_parent_id
					from doc_types
					where type_tnt_id = '.$config['tnt_id'].'
						and type_id = '.$parent.'
				');

				$parent = 0;
				if( $r_type_parent && ria_mysql_num_rows($r_type_parent) ){
					$type_parent = i18n::getTranslation( CLS_TYPE_DOCUMENT, ria_mysql_fetch_assoc($r_type_parent) );
					$ar_title[] = $type_parent['type_name'];

					if( is_numeric($type_parent['type_parent_id']) && $type_parent['type_parent_id'] > 0 ){
						$parent = $type_parent['type_parent_id'];
					}
				}
			}

			$title_auto = doc_types_get_name( $obj[0] );
			if( count($ar_title) ){
				$title_auto .= ' - '.implode(' - ', $ar_title);
			}
			$title_auto .= ' - '.i18n::get('Téléchargements', 'METADATA').' - '.$website['site_title'];
		break;
	}

	$r_objects = seo_objects_get($seo_objects, 0, 0, $classe);
	if( $r_objects && ria_mysql_num_rows($r_objects) ){
		while( $seo_object = ria_mysql_fetch_assoc($r_objects) ){
			$r_template = seo_templates_get($seo_object['seo_id'], 'title', 0, i18n::getLang());
			if( $r_template && ria_mysql_num_rows($r_template) ){
				$template = ria_mysql_fetch_assoc($r_template);
				$title_override = seo_templates_get_translation($template['id'], $variables, i18n::getLang());
			}
		}
	}

	$title_auto = html_entity_decode(str_replace(array("\n","\r"),array(" "," "),$title_auto),ENT_QUOTES,"UTF-8");
	$title_override = html_entity_decode(str_replace(array("\n","\r"),array(" "," "),$title_override),ENT_QUOTES,"UTF-8");

	if( $return_override === false || ( $return_override !== true && trim( $title_override ) == '') ){
		return $title_auto;
	}

	return $title_override;
}
// \endcond

// \cond onlyria
/** Retourne la desc pour un objet donné
 *	@param int $classe Obligatoire, identifiant de classe concerné
 *	@param int|array $obj Facultatif, identifiant d'un objet de la classe correspondante au modèle sur lequel filtrer le résultat (ou tableau d'identifiants pour les clés composées)
 *	@param bool $return_override Facultatif, si true ne retournera que la surcharge, si false que l'automatique, si null retour la surcharge en priorité sinon l'auto
 *	@param bool $admin Facultatif, si true permet de ne pas utiliser le cache pour la récupération des champs avancés
 *
 *	@return string une chaine de caractère
 */
function page_obj_desc( $classe, $obj, $return_override=null, $admin=false ){
	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ) return false;
		if( $obj == 0 )
			$obj = array();
		else
			$obj = array( $obj );
	}
	if( !isset($obj[0]) ) return false;

	global $config;

	$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], i18n::getLang(), $website, array(_FLD_WST_TAG_DESC=>'meta_desc') );
		$website['meta_desc'] = $tsk_website['meta_desc'];
	}

	$seo_objects = array();

	$desc_auto = $website['meta_desc'];
	$desc_override = '';

	switch($classe){
		case CLS_PRODUCT:{
			$rp = prd_products_get_simple( $obj[0] );
			if( $obj[1] && $rp && ria_mysql_num_rows($rp) ){

				$p = ria_mysql_fetch_array( $rp );

				if( i18n::getLang()==$config['i18n_lng'] ){
					$desc = prd_classify_get_tagdesc( $obj[0], $obj[1] );
					if( trim($desc)!='' ){
						$desc_override = $desc;
					}
				} else {
					$desc = fld_object_values_get( array($obj[1], $obj[0]), _FLD_CLY_TAG_DESC, i18n::getLang(), false, $admin );
					if( trim($desc)!='' ){
						$desc_override = $desc;
					}
				}

				// traduction de la desc
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk = fld_translates_get( CLS_PRODUCT, $p['id'], i18n::getLang(), $p, array(_FLD_PRD_DESC=>'desc', _FLD_PRD_DESC_LG=>'desc-long') );
					$p['desc'] = $tsk['desc'];
					$p['desc-long'] = $tsk['desc-long'];
				}

				$desc_auto = html_strip_tags( $p['desc'].' '.$p['desc-long'] ).( $p['brd_title'] ? ' '.$p['brd_title'] : '' );

				$date_created = dateheureparse( str_replace(' à ', ' ', $p['date_created']).':00' );
				switch ($config['tnt_id']) {
					case 16:
						if (isdateheure($date_created) && strtotime($date_created) >= strtotime('2017-02-07 11:00:00')) {
							$desc_auto = strcut( $desc_auto, 156, '' );
						}
						break;
					case 43:
						// Pour Purebike
						// Si nouveau produit, prend uniquement la description courte
						if (isdateheure($date_created) && strtotime($date_created) >= strtotime('2017-08-01 00:00:00')) {
							$desc_auto = html_strip_tags( $p['desc'] );
						}
						break;
					default:
						break;
				}

				$variables = array(
					$p['id'],
					$obj[1],
					$p['brd_id'] !== null ? $p['brd_id'] : false
				);
			}
			$seo_objects = array($obj[1], $obj[0]);

			break;
		}
		case CLS_CMS:{
			if( cms_categories_exists($obj[0]) ) {
				$cms_page_load = ria_mysql_fetch_array(cms_categories_get($obj[0]));
				if( $cms_page_load['tag_desc'] )
					$desc_override = $cms_page_load['tag_desc'];
				elseif($cms_page_load['short_desc'])
					$desc_auto = $cms_page_load['short_desc'];
				else
					$desc_auto = $cms_page_load['name'].' sur le site '.$config['site_name'];
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_BRAND: {
			$rbrd = prd_brands_get( $obj[0], true );
			if( $rbrd ){
				$brd = ria_mysql_fetch_array($rbrd);
				if( trim($brd['tag_desc']) ){
					$desc_override =  $brd['tag_desc'];
				} else {
					$desc_auto = 'Produits '.$brd['title'].' sur le site '.$config['site_name'].', '.$config['site_desc'].'.';
				}

				$variables = array(
					$brd['id'],
					$brd['products']
				);
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_NEWS:  {
			// Actualités
			$rnews = news_get($obj[0]);
			if( $rnews && ria_mysql_num_rows($rnews) ){
				$news = ria_mysql_fetch_array($rnews);

				if( trim( $news['tag_desc'] ) != '' ){
					$desc_override = $news['tag_desc'];
				}elseif( trim($news['intro'])!='' ){
					$desc_auto = $news['intro'];
				}elseif( trim($news['name'])!='' ){
					$desc_auto = $news['name'];
				}
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_FAQ_CAT:  {
			$rcfaq = faq_categories_get( $obj[0] );
			if( $rcfaq && ria_mysql_num_rows($rcfaq) ){
				$cfaq = ria_mysql_fetch_array( $rcfaq );

				$cfaq['desc'] = str_replace( "[\s].", ' ', html_strip_tags( $cfaq['desc'] ) );
				if( $cfaq['tag_desc']!='' ){
					$desc_override = $cfaq['tag_desc'];
				}elseif( trim($cfaq['name']) ){
					$desc_auto = 'Retrouvez ici toutes les réponses aux questions que vous pouvez vous poser sur '.$cfaq['name'];
				}
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_FAQ_QST:  {
			$rfaq = faq_questions_get( $obj[0] );
			if( $rfaq && ria_mysql_num_rows($rfaq) ){
				$faq = ria_mysql_fetch_array( $rfaq );

				$faq['desc'] = str_replace( "[\s].", ' ', html_strip_tags( $faq['desc'] ) );
				if( $faq['tag_desc']!='' )
					$desc_override = $faq['tag_desc'];
				elseif( trim($faq['desc']) )
					$desc_auto = $faq['desc'];
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_CATEGORY:  {
			$rcat = prd_categories_get( $obj[0] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array( $rcat );
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], i18n::getLang(), $cat, array(_FLD_CAT_TITLE=>'title',_FLD_CAT_NAME=>'name') );
					$cat['title'] = $tsk_cat['title']; $cat['name'] = $tsk_cat['name'];
					$cat['desc'] = fld_object_values_get( $cat['id'], _FLD_CAT_DESC, i18n::getLang(), false, $admin );
					$cat['tag_desc'] = fld_object_values_get( $cat['id'], _FLD_CAT_TAG_DESC, i18n::getLang(), false, $admin );
				}

				if( $cat['tag_desc']!='' ){
					$desc_override = $cat['tag_desc'];
				}elseif( $cat['desc']!='' ){
					$desc_auto = html_strip_tags($cat['desc']);
				}else{
					$desc_auto = $website['meta_desc'];
				}

				$variables = array(
					$cat['id'],
					$cat['products']
				);
			}

			$seo_objects = array($obj[0]);

			break;
		}
		case CLS_STORE:  {
			$rstr = dlv_stores_get( $obj[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( $rstr && ria_mysql_num_rows($rstr) ){
				$str = ria_mysql_fetch_array( $rstr );

				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_str = fld_translates_get( CLS_STORE, $str['id'], i18n::getLang(), $str, array(_FLD_STR_DESC=>'desc') );
					$str['desc'] = $tsk_str['desc'];

					$str['tag_desc'] = fld_object_values_get($obj[0], _FLD_STR_TAG_DESC, i18n::getLang(), false, $admin);
				}

				$str['desc'] = str_replace( "[\s].", ' ', html_strip_tags( $str['desc'] ) );
				if( $str['tag_desc']!='' ){
					$desc_override =  $str['tag_desc'] ;
				}elseif( trim($str['desc']) ){
					$desc_auto = html_strip_tags($str['desc']);
				}
			}

			$seo_objects = array($obj[0]);

			break;
		}
	}

	$r_objects = seo_objects_get($seo_objects, 0, 0);
	if (ria_mysql_num_rows($r_objects)){
		while ($seo_object = ria_mysql_fetch_assoc($r_objects)){
			$r_template = seo_templates_get($seo_object['seo_id'], 'desc', 0, i18n::getLang());
			if ($r_template && $template = ria_mysql_fetch_assoc($r_template)){
				$desc_override = seo_templates_get_translation($template['id'], $variables, i18n::getLang());
			}
		}
	}

	$desc_auto = mb_strlen($desc_auto, 'UTF-8') > META_DESCRIPTION_LIMIT ? mb_substr( $desc_auto, 0, META_DESCRIPTION_LIMIT, 'UTF-8' ).'...' : $desc_auto;
	$desc_override = mb_strlen($desc_override, 'UTF-8') > META_DESCRIPTION_LIMIT ? mb_substr( $desc_override, 0, META_DESCRIPTION_LIMIT, 'UTF-8' ).'...' : $desc_override;

	$desc_auto = html_entity_decode(str_replace(array("\n","\r"),array(" "," "),$desc_auto),ENT_QUOTES,"UTF-8");
	$desc_override = html_entity_decode(str_replace(array("\n","\r"),array(" "," "),$desc_override),ENT_QUOTES,"UTF-8");

	if( $return_override === false || ( $return_override !== true && trim( $desc_override ) == '') ) return $desc_auto;
	return $desc_override;
}
// \endcond

// \cond onlyria
/** Retourne les keywords pour un objet donné
 *	@param int $classe Obligatoire, identifiant de classe concerné
 *	@param int|array $obj Facultatif, identifiant d'un objet de la classe correspondante au modèle sur lequel filtrer le résultat (ou tableau d'identifiants pour les clés composées)
 *	@param bool $return_override Facultatif, si true ne retournera que la surcharge, si false que l'automatique, si null retour la surcharge en priorité sinon l'auto
 *	@param bool $admin Facultatif, si true permet de ne pas utiliser le cache pour la récupération des champs avancés
 *
 *	@return string une chaine de caractère
 */
function page_obj_key( $classe, $obj, $return_override=null, $admin=false ){
	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ) return false;
		if( $obj == 0 ){
			$obj = array();
		}else{
			$obj = array( $obj );
		}
	}
	if( !isset($obj[0]) ) return false;
	global $config;

	$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], i18n::getLang(), $website, array(_FLD_WST_TAG_KEYWORDS=>'meta_kwd') );
		$website['meta_kwd'] = $tsk_website['meta_kwd'];
	}

	$key_auto = '';
	$key_override = '';
	$add_key_site = true;  // par défaut on rajoute les métas keywords du site

	switch($classe){
		case CLS_PRODUCT:{ // produits
			$rp = prd_products_get_simple( $obj[0] );
			if( $rp && ria_mysql_num_rows($rp) ){
				$add_key_site = false;
				$p = ria_mysql_fetch_array( $rp );

				//$p_kwd = prd_classify_get_keywords( $obj[0], $obj[1] );

				if( i18n::getLang()==$config['i18n_lng'] ){
					$p_kwd = prd_classify_get_keywords( $obj[0], $obj[1] );
					if( trim($p_kwd)!='' ){
						$key_override = ','.$p_kwd;
					}
				} else {
					// traduit les titres / desc du produit
					$tsk = fld_translates_get( CLS_PRODUCT, $obj[0], i18n::getLang(), $p, array(_FLD_PRD_TITLE => 'title', _FLD_PRD_DESC=>'desc') );
					$p['title'] = $tsk['title'];
					$p['desc'] = $tsk['desc'];

					$p_kwd = fld_object_values_get( array($obj[1], $obj[0]), _FLD_CLY_KEYWORDS, i18n::getLang(), false, $admin );
					if( trim($p_kwd)!='' ){
						$key_override = ','.$p_kwd;
					}
				}

				$title = html_revert_wysiwyg( page_obj_title(CLS_PRODUCT, array($obj[0], $obj[1])) );
				$desc  = html_revert_wysiwyg( page_obj_desc(CLS_PRODUCT, array($obj[0], $obj[1])) );
				$kwds  = html_revert_wysiwyg( page_obj_key(CLS_CATEGORY, $obj[1]) );

				$tmp_keywords_auto = array_merge(
					explode(' ',$title),
					explode(' ',$desc),
					explode(',',$kwds)
				);

				$key_auto = implode(',', $tmp_keywords_auto);
			}
			break;
		}
		case CLS_CMS:{ // gestion de contenu
			if( cms_categories_exists($obj[0]) ) {
				$cms_page_load = ria_mysql_fetch_array(cms_categories_get($obj[0]));
				if( $cms_page_load['keywords'] ){
					$key_override = $cms_page_load['keywords'];
				}
			}
			break;
		}
		case CLS_BRAND: { // marque
			$rbrd = prd_brands_get( $obj[0] );
			if( $rbrd && ria_mysql_num_rows($rbrd) ){
				$brd = ria_mysql_fetch_array( $rbrd );
				if( trim($brd['keywords']) ){
					$key_override = $brd['keywords'];
				}else{
					$key_auto .= ','.$brd['name'].','.$brd['title'].','.str_replace( ' ', ',', $brd['desc'] );
				}
			}
			break;
		}
		case CLS_NEWS:  { // actualités
			$rnews = news_get($obj[0]);

			if( $rnews && ria_mysql_num_rows($rnews) ){
				$news = ria_mysql_fetch_array($rnews);
				if( trim( $news['keywords'] ) != '' ){
					$key_override = $news['keywords'];
				}else{
					$key_auto = $news['name'].', '.$news['intro'];
				}
			}
			break;
		}
		case CLS_FAQ_CAT:  {
			$rcfaq = faq_categories_get( $obj[0] );
			if( $rcfaq && ria_mysql_num_rows($rcfaq) ){
				$cfaq = ria_mysql_fetch_array( $rcfaq );

				if( $cfaq['keywords']!='' ){
					$key_override = $cfaq['keywords'];
				}
			}
			break;
		}
		case CLS_FAQ_QST:  { // faq ( question )
			$rfaq = faq_questions_get( $obj[0] );
			if( $rfaq && ria_mysql_num_rows($rfaq) ){
				$faq = ria_mysql_fetch_array( $rfaq );

				if( $faq['keywords']!='' ){
					$key_override = $faq['keywords'];
				}
			}
			break;
		}
		case CLS_CATEGORY:  { // catégories
			$tmp_keywords_auto = array();

			$rcat = prd_categories_get( $obj[0] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array($rcat);
				if( i18n::getLang()!=$config['i18n_lng'] ){
					$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], i18n::getLang(), $cat, array(_FLD_CAT_TITLE=>'title',_FLD_CAT_DESC=>'desc',_FLD_CAT_NAME=>'name') );
					$cat['title'] = $tsk_cat['title'];
					$cat['name'] = $tsk_cat['name'];
					$cat['desc'] = $tsk_cat['desc'];
					$cat['keywords'] = fld_object_values_get( $cat['id'], _FLD_CAT_TAG_KEYWORD, i18n::getLang(), false, $admin );
				}

				if( $cat['keywords']!='' ){
					$key_override = ','.$cat['keywords'];
				}

				$tmp_keywords_auto[] = $cat['title'];
				if( $cat['desc'] ) {
					$tmp_keywords_auto = array_merge( $tmp_keywords_auto, explode(' ',$cat['desc']) );
				}

				// pour la premiere catégorie parente
				$rpcat = prd_categories_parents_get_array($cat['id'] );
				if( $rpcat && sizeof( $rpcat ) ){
					$rpcat = array_reverse($rpcat);
					$add_key_site = false;
					foreach( $rpcat as $pcat ){
						$tmp_keywords_auto = array_merge( $tmp_keywords_auto, explode(',',page_obj_key( CLS_CATEGORY, $pcat )) );
						break;
					}
				}

				// on ajoute la meta title
				$tmp_keywords_auto = array_merge( $tmp_keywords_auto, explode(' ',page_obj_title(CLS_CATEGORY, $cat['id'] )) );

				// on ajoute la meta desc
				$tmp_keywords_auto = array_merge( $tmp_keywords_auto, explode(' ',page_obj_desc(CLS_CATEGORY, $cat['id'] )) );

				// on ajoute les marques des produits présents dans la catégories
				/*$rbrd = prd_categories_brands_get($cat['id'],true);
				if( $rbrd && ria_mysql_num_rows($rbrd) ){
					while( $brd = ria_mysql_fetch_array( $rbrd ) ){
						$tmp_keywords_auto[] = $brd['title'];
					}
				}*/

				$key_auto = implode(',',$tmp_keywords_auto);

			}
			break;
		}
		case CLS_STORE: { // magasins
			$rstr = dlv_stores_get( $obj[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( $rstr && ria_mysql_num_rows($rstr) ){
				$str = ria_mysql_fetch_array( $rstr );

				if( i18n::getLang()!=$config['i18n_lng'] ){
					$str['keywords'] = fld_object_values_get($obj[0], _FLD_STR_KEYWORDS, i18n::getLang(), false, $admin);
				}

				if( $str['keywords']!='' ){
					$key_override = $str['keywords'];
				}
			}

			break;
		}

	}

	if( $return_override === false || ( $return_override !== true && trim( $key_override ) == '' ) ){
		$keywords = $key_auto;
		if( $add_key_site ) $keywords .= ','.$website['meta_kwd'];
	}else{
		$keywords = $key_override;
	}

	// Simplifie la liste de mots clés
	if( i18n::getLang()!='ru' ){
		$keywords = strtolower2($keywords);
	} else {
		$keywords = mb_strtolower($keywords , 'UTF-8');
	}

	if (i18n::getLang() == $config['i18n_lng']) {
		$keywords = preg_replace('/[^a-z0-9_ ]/', ',', $keywords);
		$keywords = preg_replace('/,[a-z0-9_ ]{1,2},/', ',', $keywords);
	}

	// Supprime les séparateurs inutiles
	$keywords = str_replace( "\r", '', $keywords );
	$keywords = str_replace( "\n", '', $keywords );
	$keywords = str_replace( " ", ',', $keywords );
	$keywords = preg_replace( '/^,/', '', $keywords );
	$keywords = preg_replace( '/,^/', '', $keywords );
	$keywords = preg_replace( '/,[,]+/', ',', $keywords );


	// Dédoublonne les mots clés
	$r_keywords = explode( ',', $keywords );
	$r_keywords = array_map('trim', $r_keywords );
	$r_keywords = array_unique( $r_keywords );

	// Supprime les stopwords qui pourraient se trouver dans les mots clés (notre, votre, leur, de, pour, etc...)
	foreach( $r_keywords as $k=>$kwd ){
		$kwd = strtoupper($kwd);
		if( strpos(META_STOPWORDS, ",$kwd,")!==false ){
			unset( $r_keywords[$k] );
		}
	}

	$keywords = implode( ',', $r_keywords );

	// Vérifie que la taille du champ meta-keywords respecte les conditions de limites définies
	// et applique cette limite si elle n'est pas respectée
	if( strlen($keywords)>META_KEYWORDS_LIMIT ){
		// Tronque la chaîne de mots clés pour la faire entrer dans les limites maximum définies
		$keywords = substr( $keywords, 0, META_KEYWORDS_LIMIT );
		// Supprime le dernier mot clé au cas où celui-ci aurait été tronqué par l'opération précédente
		$last_comma = strrpos( $keywords, ',' );
		$keywords = substr( $keywords, 0, $last_comma );
	}

	return $keywords;
}
// \endcond

/** Cette fonction permet de récupérer l'url canonique d'un produit.
 *	Attention : la constrante _FILE_ doit être définie
 *
 *	@param string $filename Obligatoire, fichier php appelé permettant de vérifier qu'il s'agit de la page produit
 *	@param bool $https Optionnel, permet de savoir si l'on est sur une installation ssl ou non
 *	@param bool $only_url Optionnel, par défaut la fonction retourne la balise, mettre true pour retourner seulement l'url canonique
 *
 *	@return string L'url canonique ou la balise "canonical"
 */
function page_canonical_get( $filename='', $https=false, $only_url=false ){
	if( !$filename && !defined('_FILE_') ){
		return false;
	}

	global $config;
	if( !$filename ){
		$filename = str_replace($config['site_dir'].'/pages', '', _FILE_ );
	}
	$https = is_bool($https) ? $https : false;
	$url = '';

	switch ($filename) {
		case '/catalog/product.php':{
			if( isset($_GET['prd']) ){
				$unset = $_GET;

				unset($unset['prd']);
				if( isset($unset['cat']) ){
					unset($unset['cat']);
				}

				if( isset($product['url_alias']) && trim($product['url_alias']) ){
					$url = $product['url_alias'];
				}

				if( trim($url) == '' ){
					$url = prd_products_get_url( $_GET['prd'], true, 0, true );
				}

				if (trim($url) != '') {
					if (!isset($config['site_ssl_url']) || trim($config['site_ssl_url']) == '') {
						$config['site_ssl_url'] = $config['site_url'];
					}

					$url = rew_strip( $url );
				}

				if (isset($config['canonical_use_parent']) && $config['canonical_use_parent']) {
					$parentIds = prd_hierarchy_get(0, $_GET['prd']);
					if ($parentIds && ria_mysql_num_rows($parentIds)) {
						while (empty($url) && $parentIds && $parentId = ria_mysql_fetch_assoc($parentIds)) {
							$url = trim(prd_products_get_url($parentId['parent'], true, 0, true));

							if (!empty($url)) {
								if (!isset($config['site_ssl_url']) || trim($config['site_ssl_url']) == '') {
									$config['site_ssl_url'] = $config['site_url'];
								}

								$url = rew_strip($url);
							}
						}
					}
				}
			}

			if (trim($url) == '' && $https) {
				$url = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : '';
			}
			break;
		}
		default: {
			// if ($https) {
				// $url = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : '';
			// }

			if( isset($_SERVER['SCRIPT_URL']) ){
				$url = $_SERVER['SCRIPT_URL'];

			}elseif( isset($_SERVER['REDIRECT_URL']) ){
				$url = $_SERVER['REDIRECT_URL'];

			}elseif( isset($_SERVER['REQUEST_URI']) ){
				$p = parse_url($_SERVER['REQUEST_URI']);
				$url = $p['path'];

			}
			break;
		}
	}

	$canonical = '';
	if (trim($url) != '') {
		if (!isset($config['site_ssl_url']) || trim($config['site_ssl_url']) == '') {
			$config['site_ssl_url'] = $config['site_url'];
		}

		$url = ( $https ? $config['site_ssl_url'] : $config['site_url'] ).$url;
		$url = str_replace('//', '/', $url);
		$url = str_replace(array('http:/', 'https:/'), array('http://', 'https://'), $url);

		$canonical = '<link rel="canonical" href="'.rew_strip( $url ).'"/>';

	}

	return ($only_url ? $url : $canonical);
}

// \cond onlyria
/** Cette fonction permet de récupérer les objects liés à une meta-description
 *	@param string $desc Obligatoire, meta-description recherchée
 *	@param bool $publish Optionnel, par défaut tous les contenus sont retournés, mettre True pour n'avoir que ce publié
 *	@return resource Un résultat MySQL contenant :
 *				- type : type de contenu (cly, cat, brd, str, cms, news, faq-cat ou faq-qst)
 *				- obj_id_0 : identifiant de l'objet
 *				- obj_id_1 : complément de l'identifiant de l'objet (cly : 0=cat_id et 1=prd_id, faq-qst : 0=cat_id et 1 = qst_id)
 */
function key_meta_description_get_objects( $desc, $publish=false ){
	if( trim($desc)=='' ){
		return false;
	}

	global $config;

	$sql = '
		select "cly" as type, cly_cat_id as obj_id_0, cly_prd_id as obj_id_1
		from prd_classify
			'.( $publish ? 'join prd_products on (cly_tnt_id=prd_tnt_id and cly_prd_id=prd_id)' : '' ).'
		where cly_tnt_id='.$config['tnt_id'].'
			and ifnull(cly_tag_desc, "")!=""
			and cly_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and prd_publish and prd_publish_cat' : '' ).'

		union all

		select "cat" as type, cat_id as obj_id_0, 0 as obj_id_1
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_date_deleted is null
			and ifnull(cat_tag_desc, "")!=""
			and cat_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and cat_publish and cat_products' : '' ).'

		union all

		select "brd" as type, brd_id as obj_id_0, 0 as obj_id_1
		from prd_brands
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_date_deleted is null
			and ifnull(brd_tag_desc, "")!=""
			and brd_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and brd_products>0' : '' ).'

		union all

		select "str" as type, str_id as obj_id_0, 0 as obj_id_1
		from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_date_deleted is null
			and ifnull(str_tag_desc, "")!=""
			and str_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and str_publish' : '' ).'

		union all

		select "cms" as type, cat_id as obj_id_0, 0 as obj_id_1
		from cms_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and date_deleted is null and cat_current=1
			and ifnull(cat_tag_desc, "")!=""
			and cat_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and cat_publish_date is not null and cat_publish_date < now()' : '' ).'

		union all

		select "news" as type, news_id as obj_id_0, 0 as obj_id_1
		from news
		where news_tnt_id='.$config['tnt_id'].'
			and ifnull(news_tag_desc, "")!=""
			and news_tag_desc="'.addslashes( $desc ).'"
			'.( $publish ? ' and news_publish_date<=now()' : '' ).'

		union all

		select "faq-cat" as type, cat_id as obj_id_0, 0 as obj_id_1
		from faq_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and ifnull(cat_tag_desc, "")!=""
			and cat_tag_desc="'.addslashes( $desc ).'"

		union all

		select "faq-qst" as type, qst_cat_id as obj_id_0, qst_id as obj_id_1
		from faq_questions
		where qst_tnt_id='.$config['tnt_id'].'
			and ifnull(qst_tag_desc, "")!=""
			and qst_tag_desc="'.addslashes( $desc ).'"
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les meta-descriptions personnalisées en double (réutilisées sur n'importe quelle autre page du site).
 *	@param bool $publish Optionnel, par défaut tous les contenus sont retournés, mettre True pour n'avoir que ce publié
 *	@return resource Retourne un résultat MySQL contenant :
 *				- tag_desc : meta-description personnalisée
 *				- used : nombre d'utilisation total
 */
function key_meta_description_get_multiple( $publish=false ){
	global $config;

	$sql = '
		select tag_desc, count(*) as used
		from (
			select cly_tag_desc as tag_desc
			from prd_classify
				'.( $publish ? 'join prd_products on (cly_tnt_id=prd_tnt_id and cly_prd_id=prd_id)' : '' ).'
			where cly_tnt_id='.$config['tnt_id'].'
				and ifnull(cly_tag_desc, "")!=""
				'.( $publish ? ' and prd_publish and prd_publish_cat' : '' ).'

			union all

			select cat_tag_desc as tag_desc
			from prd_categories
			where cat_tnt_id='.$config['tnt_id'].'
				and cat_date_deleted is null
				and ifnull(cat_tag_desc, "")!=""
				'.( $publish ? ' and cat_publish and cat_products' : '' ).'

			union all

			select brd_tag_desc as tag_desc
			from prd_brands
			where brd_tnt_id='.$config['tnt_id'].'
				and brd_date_deleted is null
				and ifnull(brd_tag_desc, "")!=""
				'.( $publish ? ' and brd_products>0' : '' ).'

			union all

			select str_tag_desc as tag_desc
			from dlv_stores
			where str_tnt_id='.$config['tnt_id'].'
				and str_date_deleted is null
				and ifnull(str_tag_desc, "")!=""
				'.( $publish ? ' and str_publish' : '' ).'

			union all

			select cat_tag_desc as tag_desc
			from cms_categories
			where cat_tnt_id='.$config['tnt_id'].'
				and date_deleted is null and cat_current=1
				and ifnull(cat_tag_desc, "")!=""
				'.( $publish ? ' and cat_publish_date is not null and cat_publish_date < now()' : '' ).'

			union all

			select  news_tag_desc as tag_desc
			from news
			where news_tnt_id='.$config['tnt_id'].'
				and ifnull(news_tag_desc, "")!=""
				'.( $publish ? ' and news_publish_date<=now()' : '' ).'

			union all

			select cat_tag_desc as tag_desc
			from faq_categories
			where cat_tnt_id='.$config['tnt_id'].'
				and ifnull(cat_tag_desc, "")!=""

			union all

			select qst_tag_desc as tag_desc
			from faq_questions
			where qst_tnt_id='.$config['tnt_id'].'
				and ifnull(qst_tag_desc, "")!=""
		) all_tag
		group by tag_desc
		having count(*)>1
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de charger les paramètres pour les campagnes de remarketing Google.
 *	@param array $object Optionnel, permet de passer les informations sur un objet concerné par les paramètres (exemple : une catégorie, un produit) tel que retourné par un ria_mysql_fetch_assoc( ..._get() )
 *	@param string $page Optionnel, page sur laquelle sont chargés les paramètres (par défaut on essaye d'utiliser _FILE_ s'il est défini)
 *	@param bool $new_tag Optionnel, par défaut à False, mettre True pour utiliser les dimensions personnalisées via "ga"
 *	@param bool $in_script Optionnel, par défaut à False, mettre True pour signaler que la fonction est appelée dans une balise "<script>"
 *
 *	@return string le code Html des paramètres
 */
function page_params_remarketing( $object=false, $page='', $new_tag=false, $in_script=false ){
	if( trim($page) == '' ){
		$page = defined( '_FILE_' ) ? _FILE_ : '';
	}

	if( trim($page) != '' ){
		$page = str_replace( '/pages', '', $page );
	}

	if( trim($page) == '' ){
		$page = 'none';
	}

	$ar_params = array( 'ecomm_pagetype' => 'other' );
	switch( $page ){
		case '/index.php': {
			$ar_params['ecomm_pagetype']		= 'home';
			break;
		}
		case '/catalog/index.php':
		case '/category.php': {
			if( ria_array_key_exists(array('title'), $object) && trim($object['title']) != '' ){
				$ar_params['ecomm_pagetype'] 	= 'category';
				$ar_params['ecomm_pcat'] 		= addslashes( $object['title'] );
			}
			break;
		}
		case '/catalog/product.php': {
			if( ria_array_key_exists(array('id', 'title'), $object) && is_numeric($object['id']) && $object['id'] && trim($object['title']) != '' ){
				$ar_params['ecomm_prodid']		= $object['id'];
				$ar_params['ecomm_pagetype']	= 'product';
				$ar_params['ecomm_pname']		= addslashes( $object['title'] );

				if( isset($object['price_ttc']) ){
					$ar_params['ecomm_totalvalue'] = number_format( $object['price_ttc'], 2 , '.', '' );
				}
			}
			break;
		}
		case '/cart/index.php':
		case '/cart/cart.php':
		case '/cart/review.php': {
			if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
				$rp = ord_products_get( $_SESSION['ord_id'] );
				if( $rp && ria_mysql_num_rows($rp) ){
					$ar_prod = array(); $total_ttc = 0;
					while( $p = ria_mysql_fetch_assoc($rp) ){
						if( prd_products_is_port($p['ref']) ){
							continue;
						}

						$ar_prod[]  = $p['id'];
						$total_ttc += $p['total_ttc'];
					}

					if( sizeof($ar_prod) ){
						$ar_params['ecomm_pagetype'] 	= 'cart';

						if( sizeof($ar_prod) == 1 ){
							$ar_params['ecomm_prodid']		= $ar_prod[0];
						}else{
							$ar_params['ecomm_prodid']		= $ar_prod;
						}

						$ar_params['ecomm_totalvalue'] 	= number_format( $total_ttc, 2, '.', '' );
					}
				}
			}
			break;
		}
		case '/cart/complete.php': {
			if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
				$rp = ord_products_get( $_SESSION['ord_id'] );
				if( $rp && ria_mysql_num_rows($rp) ){
					$ar_prod = array(); $total_ttc = 0;
					while( $p = ria_mysql_fetch_assoc($rp) ){
						if( prd_products_is_port($p['ref']) ){
							continue;
						}

						$ar_prod[]  = $p['id'];
						$total_ttc += $p['total_ttc'];
					}

					if( sizeof($ar_prod) ){
						$ar_params['ecomm_pagetype'] 	= 'purchase';

						if( sizeof($ar_prod) == 1 ){
							$ar_params['ecomm_prodid']		= $ar_prod[0];
						}else{
							$ar_params['ecomm_prodid']		= $ar_prod;
						}

						$ar_params['ecomm_totalvalue'] 	= number_format( $total_ttc, 2, '.', '' );
					}
				}
			}
			break;
		}
	}

	$html = '';
	if( sizeof($ar_params) ){
		if (!$in_script) {
			$html .= '
				<!-- Code Google de la balise de remarketing -->
				<script>
			';
		}

		if ($new_tag) {
			if (array_key_exists('ecomm_prodid', $ar_params)) {
				$html .= "ga('set','dimension1', '".(is_array($ar_params['ecomm_prodid']) ? implode(", ", $ar_params['ecomm_prodid']) : $ar_params['ecomm_prodid'])."');";
			}
			if (array_key_exists('ecomm_pagetype', $ar_params)) {
				$html .= "ga('set','dimension2', '".$ar_params['ecomm_pagetype']."');";
			}
			if (array_key_exists('ecomm_totalvalue', $ar_params)) {
				$html .= "ga('set','dimension3', '".$ar_params['ecomm_totalvalue']."');";
			}
			if (array_key_exists('ecomm_pcat', $ar_params)) {
				$html .= "ga('set','dimension4', '".$ar_params['ecomm_pcat']."');";
			}
			if (array_key_exists('ecomm_pname', $ar_params)) {
				$html .= "ga('set','dimension5', '".$ar_params['ecomm_pname']."');";
			}
		}

		$html .= '
				var google_tag_params = {';

		$i = 1;
		foreach( $ar_params as $code=>$value ){
			if( is_array($value) ){
				$value = '["'.implode( '", "', $value ).'"]';
			}else{
				$value = '\''.$value.'\'';
			}
			$html .= '
					'.$code.': '.$value.( $i == sizeof($ar_params) ? '' : ',' );

			$i++;
		}

		$html .= '
				};
		';

		if (!$in_script) {
			$html .= '
				</script>
			';
		}
	}

	return $html;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une ou plusieurs phrases de référencement personnalisés
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @param int $type Optionnel, identifiant du type de phrase (s'il s'agit d'un titre ou d'une description)
 *  @param int $cls_id Optionnel, identifiant de la classe d'objet pour lequel la phrase est utilisée
 *  @param string $lng Optionnel, code de la langue
 *  @return resource Un résultat MySQL comprenant les colonnes suivantes :
 * 		- id : Identifiant de la phrase
 * 		- type : Type de phrase (title ou desc)
 * 		- cls_id : Identifiant de la classe d'objet
 * 		- content : Contenu textuel de la phrase
 * 		- lng : Code de la langue de la phrase
 * 		- date_created : Date de création de la phrase
 * 		- date_deleted : Date de suppression de la phrase (vaut NULL si la phrase n'a pas été supprimée, sinon la date de suppression)
 */
function seo_templates_get($seo_id=0, $type='', $cls_id=0, $lng=''){
	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}

	$type = trim($type);
	if ($type != '') {
		if (!in_array($type, array('title', 'desc'))) {
			return false;
		}
	}

	if (!is_numeric($cls_id) || $cls_id < 0){
		return false;
	}

	global $config;

	$sql = '
		select
			seo_id as id, seo_type as type, seo_cls_id as cls_id,
			seo_content as content, seo_lng_code as lng,
			seo_date_created as date_created, seo_date_deleted as date_deleted
		from seo_templates
		where seo_tnt_id = '.$config['tnt_id'].'
			and seo_date_deleted is null
	';

	if ($seo_id) {
		$sql .= ' and seo_id = '.$seo_id;
	}

	if ($type != '') {
		$sql .= ' and seo_type = "'.addslashes($type).'"';
	}

	if ($cls_id) {
		$sql .= ' and seo_cls_id = '.$cls_id;
	}

	$lng = trim($lng);
	if ($lng != '' && in_array($lng, $config['i18n_lng_used'])) {
		$sql .= ' and seo_lng_code = "'.addslashes($lng).'"';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le tag d'une phrase de référencement personnalisé
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @return string le tag de la phrase de référencement personnalisé
 */
function seo_templates_get_tag($seo_id){
	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}
	if (!seo_templates_exists($seo_id)){
		return false;
	}

	global $config;

	$sql = '
		select
			seo_type as type
		from seo_templates
		where seo_tnt_id = '.$config['tnt_id'].'
			and seo_date_deleted is null
	';

	if ($seo_id) {
		$sql .= ' and seo_id = '.$seo_id;
	}

	return ria_mysql_result(ria_mysql_query($sql),0,0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le tag d'une phrase de référencement personnalisé
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @return string le tag de la phrase de référencement personnalisé
 */
function seo_templates_get_content($seo_id){
	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}
	if (!seo_templates_exists($seo_id)){
		return false;
	}

	global $config;

	$sql = '
		select
			seo_content as content
		from seo_templates
		where seo_tnt_id = '.$config['tnt_id'].'
			and seo_date_deleted is null
	';

	if ($seo_id) {
		$sql .= ' and seo_id = '.$seo_id;
	}

	return ria_mysql_result(ria_mysql_query($sql),0,0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet l'ajout de nouvelles phrases de référencement personnalisés.
 *  @param int $cls_id Obligatoire, identifiant de la classe d'objet
 *  @param int $type Obligatoire, identifiant du type de phrase (s'il s'agit d'un titre ou d'une description)
 *  @param string $content Obligatoire, contenu textuel de la phrase
 * 	@param string $lng Obligatoire, code de la langue de la phrase
 *  @return bool true en cas de succès, false en cas d'échec, -1 si la phrase existait déjà
 */
function seo_templates_add($cls_id, $type, $content, $lng){
	if (!is_numeric($cls_id) || $cls_id <= 0){
		return false;
	}
	$type = trim($type);
	if (!in_array($type, array('title', 'desc'))){
		return false;
	}
	if (trim($content) == ''){
		return false;
	}

	global $config;

	$lng = trim($lng);
	if ($lng == '' || !in_array($lng, $config['i18n_lng_used'])){
		return false;
	}

	if(seo_templates_exists($cls_id, $type, $content, $lng)){
		return -1;
	}

	$sql = '
		insert into seo_templates
			(`seo_tnt_id`, `seo_cls_id`, `seo_type`, `seo_content`, `seo_lng_code`, `seo_date_created`)
		values
			('.$config['tnt_id'].', '.$cls_id.', "'.addslashes($type).'", "'.addslashes($content).'", "'.$lng.'", now())
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une phrase de référencement personnalisé. Afin de garder un historique sur ces phrases,
 * celles-ci ne sont supprimées que virtuellement.
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @return bool true en cas de succès, false en cas d'échec
 */
function seo_templates_del($seo_id){
	if (!seo_templates_exists($seo_id)){
		return false;
	}

	global $config;

	//On supprime d'abord les enregistrements liés
	if (seo_objects_exists(array(), 0, $seo_id)){
		if (!seo_objects_del(0, $seo_id)){
			return false;
		}
	}

	$sql = '
		update seo_templates
		set seo_date_deleted=now()
		where seo_id = '.$seo_id.'
		and seo_tnt_id = '.$config['tnt_id'].'
	';

	//Et on supprime la phrase
	if (!ria_mysql_query($sql)){
		return false;
	}


	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour une phrase de référencement personnalisé. En réalité, cette fonction va supprimer
 *  la phrase actuelle et en créer une nouvelle, afin de garder un historique.
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @param string $new_content Obligatoire, nouveau contenu textuel de la phrase
 *  @return bool true en cas de succès, false en cas d'échec
 */
function seo_templates_update($seo_id, $new_content){
	$r_old = seo_templates_get($seo_id);
	if( !$r_old || !ria_mysql_num_rows($r_old) ){
		return false;
	}

	$old = ria_mysql_fetch_assoc($r_old);

	$have_objects = false;

	// Récupération des associations liées à la phrase dynamique
	$r_old_objects = seo_objects_get(0, 0, $seo_id);
	if( $r_old_objects && ria_mysql_num_rows($r_old_objects) ){
		$have_objects = true;
	}

	// On supprime la ligne et ses enregistrements associés
	if( !seo_templates_del($seo_id) ){
		return false;
	}

	// On ajoute la nouvelle ligne
	if( !seo_templates_add($old['cls_id'], $old['type'], $new_content, $old['lng']) ){
		return false;
	}

	// On fait la réinsertion des anciens champs avec le nouvel identifiant seo_id
	if( $have_objects ){
		$new_seo_id = mysql_insert_id();
		$error_occured = false;
		while( $old_object = ria_mysql_fetch_assoc($r_old_objects) ){
			if( !seo_objects_add(array($old_object['obj_id_0'], $old_object['obj_id_1'], $old_object['obj_id_2']), $old_object['cls_id'], $new_seo_id) ){
				$error_occured = true;
			}
		}

		if( $error_occured ){
			return false;
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de connaitre l'existence d'une phrase de référencement personnalisé dans la table seo_templates
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @param int $cls_id Optionnel, identifiant de la classe d'objet pour lequel la phrase est utilisée
 *  @param int $type Optionnel, identifiant du type de phrase (s'il s'agit d'un titre ou d'une description)
 *  @param string $content Optionnel, contenu textuel de la phrase
 *  @param string $lng Optionnel, code de la langue
 *  @return bool true si la phrase existe déjà, false sinon
 */
function seo_templates_exists($seo_id=0, $cls_id=0, $type='', $content='', $lng=''){
	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}
	if (!is_numeric($cls_id) || $cls_id < 0){
		return false;
	}
	$type = trim($type);
	if ($type != '' && !in_array($type, array('title', 'desc'))){
		return false;
	}

	global $config;

	$lng = trim($lng);
	if ($lng != '' && !in_array($lng, $config['i18n_lng_used'])){
		return false;
	}

	$sql = '
		select 1
		from seo_templates
		where seo_tnt_id = '.$config['tnt_id'].'
			and seo_date_deleted is null
	';

	if ($seo_id) {
		$sql .= ' and seo_id = '.$seo_id;
	}

	if ($type != '') {
		$sql .= ' and seo_type = "'.addslashes($type).'"';
	}

	$content = trim($content);
	if ($content != '') {
		$sql .= ' and seo_content = "'.addslashes($content).'"';
	}

	if ($cls_id) {
		$sql .= ' and seo_cls_id = '.$cls_id;
	}

	$lng = trim($lng);
	if ($lng != '') {
		$sql .= ' and seo_lng_code = "'.addslashes($lng).'"';
	}

	return ria_mysql_num_rows(ria_mysql_query($sql)) ? true : false;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la phrase type utilisée en fonction des identifiants passés en paramètre
 *  @param int|array $obj Obligatoire, identifiant ou tableaux d'identifiants d'objets
 *  @param int $sob_id Optionnel, identifiant de la ligne
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @param int $cls_id Optionnel, identifiant de la classe
 *  @return resource un résultat MySQL comprenant les colonnes suivantes :
 * 		- id : identifiant de la ligne
 * 		- cls_id : identifiant de la classe de l'objet
 * 		- seo_id : identifiant de la phrase liée
 * 		- obj_id_0 : identifiant 0 de l'objet
 * 		- obj_id_1 : identifiant 1 de l'objet (par défaut 0)
 * 		- obj_id_2 : identifiant 2 de l'objet (par défaut 0)
 * 		- date_created : date de création de l'enregistrement
 * 		- date deleted : date de suppression de l'enregistrement (par défaut NULL)
 */
function seo_objects_get($obj=0, $sob_id=0, $seo_id=0, $cls_id=0){
	if (is_array($obj)){
		if (!sizeof($obj) || sizeof($obj) > 3){
			return false;
		}
		foreach ($obj as $key => $value) {
			if (!is_numeric($value) || $value <= 0){
				return false;
			}
		}
	} else {
		if (!is_numeric($obj) || $obj < 0){
			return false;
		}
		if ($obj != 0){
			$obj = array($obj);
		}
	}

	if (!is_numeric($sob_id) || $sob_id < 0){
		return false;
	}

	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}

	global $config;

	$sql = '
		select
			sob_id as id, sob_cls_id as cls_id, sob_seo_id as seo_id,
			sob_obj_id_0 as obj_id_0, sob_obj_id_1 as obj_id_1, sob_obj_id_2 as obj_id_2,
			sob_date_created as date_created, sob_date_deleted as date_deleted
		from seo_objects
		where sob_tnt_id = '.$config['tnt_id'].'
		and sob_wst_id = '.$config['wst_id'].'
		and sob_date_deleted is null
		'.($seo_id ? 'and sob_seo_id = '.$seo_id : '').'
		'.($sob_id ? 'and sob_id = '.$sob_id : '').'
	';

	if( is_numeric($cls_id) && $cls_id > 0 ){
		$sql .= ' and sob_cls_id='. $cls_id;
	}

	if ($obj != 0){
		$i = 0;
		while( $i<sizeof($obj) ){
			$sql .= ' and sob_obj_id_'.$i.' = '.$obj[$i];
			$i++;
		}
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'assigner à un objet une phrase de référencement personnalisé
 *  @param int|array $obj Obligatoire, identifiant ou tableaux d'identifiants d'objets
 *  @param int $cls_id Obligatoire, identifiant de la classe d'objet
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @return bool true en cas de succès, false en cas d'échec
 */
function seo_objects_add($obj, $cls_id, $seo_id){
	if( is_array($obj) ){
		if( !sizeof($obj) || sizeof($obj) > 3 ){
			return false;
		}
		foreach( $obj as $key => $value ){
			if( !is_numeric($value) || $value < 0 ){
				return false;
			}
		}
	} else {
		if( !is_numeric($obj) || $obj <= 0 ){
			return false;
		}
		$obj = array($obj);
	}

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	if( !is_numeric($seo_id) || $seo_id <= 0 ){
		return false;
	}

	global $config;

	// Ecriture de la requête
	$sql = '
		insert into seo_objects
			(`sob_tnt_id`, `sob_wst_id`, `sob_cls_id`';

	$i = 0;
	while( $i < sizeof($obj) ){
		$sql .= ', `sob_obj_id_'.$i.'`';
		$i++;
	}
	$sql .= ', `sob_seo_id`, `sob_date_created`)
		values
			('.$config['tnt_id'].', '.$config['wst_id'].', '.$cls_id;

	$i = 0;
	while( $i < sizeof($obj) ){
		$sql .= ', '.$obj[$i];
		$i++;
	}

	$sql .= ', '.$seo_id.', now())';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppresion d'une liaison entre une phrase et un objet
 *  @param int $sob_id Optionnel, identifiant de l'enregistrement
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @return bool false en cas d'échec, true en cas de succès
 */
function seo_objects_del($sob_id=0, $seo_id=0){
	if (!is_numeric($sob_id) || $sob_id < 0){
		return false;
	}
	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}

	global $config;

	$sql = '
		update seo_objects
		set sob_date_deleted = now()
		where sob_tnt_id = '.$config['tnt_id'].'
		and sob_wst_id = '.$config['wst_id'].'
		and sob_date_deleted is null
		'.($seo_id ? 'and sob_seo_id = '.$seo_id : '').'
		'.($sob_id ? 'and sob_id = '.$sob_id : '').'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour d'une phrase liée à un objet
 *  @param int $sob_id Optionnel, identifiant de l'enregistrement
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @return bool false en cas d'échec, true en cas de succès
 */
function seo_objects_update($sob_id=0, $seo_id=0){
	$r_olds = seo_objects_get(0, $sob_id, $seo_id);
	if (!$r_old || !ria_mysql_num_rows($r_old)) {
		return false;
	}

	if (!seo_objects_del($sob_id, $seo_id)){
		return false;
	}

	$res = true;
	while ($old = ria_mysql_fetch_assoc($r_olds) && $res){
		$res = seo_objects_add(array($old['obj_id_0'], $old['obj_id_1'], $old['obj_id_2']), $obj['cls_id'], $seo_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier l'existence d'une liaison entre un objet et une phrase
 *  @param array $obj Optionnel, tableau d'identifiants de l'objet
 *  @param int $sob_id Optionnel, identifiant de l'enregistrement
 *  @param int $seo_id Optionnel, identifiant de la phrase
 *  @return bool false en cas d'échec, true en cas de succès
 */
function seo_objects_exists($obj=array(), $sob_id=0, $seo_id=0){
	if (is_array($obj)){
		if (sizeof($obj)){
			foreach ($obj as $key => $value) {
				if (!is_numeric($value) || $value <= 0){
					return false;
				}
			}
		}
	} else {
		if (!is_numeric($obj) || $obj <= 0){
			return false;
		}
		$obj = array($obj);
	}

	if (!is_numeric($sob_id) || $sob_id < 0){
		return false;
	}

	if (!is_numeric($seo_id) || $seo_id < 0){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from seo_objects
		where sob_tnt_id = '.$config['tnt_id'].'
		and sob_wst_id = '.$config['wst_id'].'
		and sob_date_deleted is null
		'.($seo_id ? 'and sob_seo_id = '.$seo_id : '').'
		'.($sob_id ? 'and sob_id = '.$sob_id : '').'
	';

	$i = 0;
	while( $i<sizeof($obj) ){
		$sql .= ' and sob_obj_id_'.$i.' = '.$obj[$i];
		$i++;
	}

	return ria_mysql_num_rows(ria_mysql_query($sql)) ? true : false;
}
// \endcond

/** Cette fonction permet de convertir la phrase de l'état brut à l'état final, en fonction des champs présents et de la langue
 *  @param int $seo_id Obligatoire, identifiant de la phrase
 *  @param array $objects Obligatoire, tableau d'identifiants de l'objet
 *  @param string $lng Obligatoire, langue dans laquelle doivent être traduits les éléments
 *  @return string une chaine de caractères contenant la phrase convertie.
 */
function seo_templates_get_translation($seo_id, $objects, $lng){

	if (!is_numeric($seo_id) || $seo_id <= 0){
		return false;
	}
	if (!is_array($objects) || !sizeof($objects)){
		return false;
	}

	$r_template = seo_templates_get($seo_id);

	if (!$r_template || !ria_mysql_num_rows($r_template)){
		return false;
	}

	$template = ria_mysql_fetch_assoc($r_template);


	$translation = $template['content'];
	switch($template['cls_id']){
		case CLS_PRODUCT: {
			// prd
			$r_prd = prd_products_get_simple($objects[0]);
			$objects[0] = $r_prd ? i18n::getTranslation(CLS_PRODUCT, ria_mysql_fetch_assoc($r_prd), false, $lng) : array('title' => '');
			$translation = str_replace('fld-'._FLD_PRD_TITLE, $objects[0]['title'], $translation);

			// cat
			$r_cat = prd_categories_get($objects[1]);
			$objects[1] = $r_cat ? i18n::getTranslation(CLS_CATEGORY, ria_mysql_fetch_assoc($r_cat), false, $lng) : array('title' => '');
			$translation = str_replace('fld-'._FLD_CAT_TITLE, $objects[1]['title'], $translation);

			// brd
			if( isset($objects[2]) ){
				$r_brd = prd_brands_get($objects[2]);
				$objects[2] = $r_brd ? i18n::getTranslation(CLS_BRAND, ria_mysql_fetch_assoc($r_brd), false, $lng) : array('title' => '');
				$translation = str_replace('fld-'._FLD_BRD_TITLE, $objects[2]['title'], $translation);
			}
			break;
		}
		case CLS_CMS: {

			break;
		}
		case CLS_BRAND: {
			$objects[0] = i18n::getTranslation(CLS_BRAND, ria_mysql_fetch_assoc(prd_brands_get($objects[0])), false, $lng);
			$translation = str_replace('fld-'._FLD_BRD_TITLE, $objects[0]['title'], $translation);
			$translation = str_replace('[Nombre de produits]', $objects[1], $translation);
			break;
		}
		case CLS_NEWS: {

			break;
		}
		case CLS_FAQ_CAT: {

			break;
		}
		case CLS_FAQ_QST: {

			break;
		}
		case CLS_CATEGORY: {
			$objects[0] = i18n::getTranslation(CLS_CATEGORY, ria_mysql_fetch_assoc(prd_categories_get($objects[0])), false, $lng);
			$translation = str_replace('fld-'._FLD_CAT_TITLE, $objects[0]['title'], $translation);
			$translation = str_replace('[Nombre de produits]', $objects[1], $translation);
			break;
		}
		case CLS_STORE: {

			break;
		}
	}
	return $translation;
}

/// @}

?>

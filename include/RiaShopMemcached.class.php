<?php

require_once('i18n.inc.php');

class RiaShopMemcached extends Memcached {
  private $key = '';
  private $expiration = 0;
  private $group = '';

  /** Cette fonction permet d'initialiser un object permettant d'utiliser le cache Memcached.
   *  @param string $persitant_id Par défaut, les instances Memcached sont détruites à la fin de la requête. Pour créer un objet qui persiste entre les requêtes, utilisez le paramètre persistent_id pour spécifier un identifiant unique pour l'instance. Tous les objets créés avec le même identifiant persistent_id partageront la même connexion.
   */
  public function __construct( $persitant_id='' ){
    parent::__construct( $persitant_id );
  }

  /** Cette fonction permet de définir dans le cache une valeur pour une clé.
   *  @param string $key Obligatoire, la clé avec laquelle stocker la valeur
   *  @param mixed $value Obligatoire, la valeur à stocker
   *  @param $expiration Optionnel, le délai d'expiration (en seconde), par défaut à zéro.
   *  @return bool true en cas de succès ou false si une erreur survient. Utilisez Memcached::getResultCode() si nécessaire.
   */
  public function set( $key, $value, $expiration=0, $group=[] ){
    $this->key = self::transformKey( $key );
    $this->group = $group;
    $this->expiration = $expiration;

    // Sauvegarde en base la déclaration du cache
    // Cela permettra de mettre en place une interface pour vider le cache
    $this->addInBdd();

    return parent::set( $this->key, $value, $expiration );
  }

  /** Cette fonction permet de récupérer la valeur d'une clé dans le cache.
   *  @param string $key Obligatoire, la clé de l'élément à lire
   *  @param callable $cache_cb Optionnel, une fonction de rappel en cas d'absence ou null
   *  @param int $flag Optionnel, drapeaux pour contrôler le résultat retourné. Quand Memcached::GET_EXTENDED est fournit, la fonction retournera aussi le jeton CAS.
   *  @return mixed Retourne la valeur stockée dans le cache, ou bien false sinon. Si flags est définit à Memcached::GET_EXTENDED, un tableau contenant la valeur et le jeton CAS est retourné au lieu d'uniquement la valeur. La méthode Memcached::getResultCode() retourne Memcached::RES_NOTFOUND si la clé n'existe pas.
   */
  public function get( $key, $cache_cb=null, $flag=null ){
    return parent::get( self::transformKey($key), $cache_cb, $flag );
  }

  /** Cette fonction permet de supprimer les entrées du cache à partir d'un code de regroupement.
   *  @param string $code Obligatoire, code de regroupement
   *  @param int $wst_id Optionnel, identifiant d'un site de restriction, par défaut 0 donc sur tous les sites
   *  @return bool true si tout s'est correctement passé, false dans le cas contraire
   */
  public static function clearByCode( $code, $wst_id=0 ){
    global $config, $memcached;

    if( trim($code) == '' ){
      return false;
    }

    if( !is_numeric($wst_id) || $wst_id < 0 ){
      return false;
    }

    $r_entry = ria_mysql_query('
      select cch_tnt_id, cch_wst_id, cch_code, cch_keys
      from cch_caches
      where cch_tnt_id = '.$config['tnt_id'].'
        '.( $wst_id > 0 ? ' and cch_wst_id = '.$wst_id : '' ).'
        and cch_code = "'.addslashes( $code ).'"
    ');

    if( $r_entry ){
      while( $entry = ria_mysql_fetch_assoc($r_entry) ){
        $json = json_decode( $entry['cch_keys'], true );

        foreach( $json as $key => $val ){
          @$memcached->delete( $key );
        }

        ria_mysql_query('
          delete from cch_caches
          where cch_tnt_id = '.$entry['cch_tnt_id'].'
            and cch_wst_id = '.$entry['cch_wst_id'].'
            and cch_code = "'.addslashes( $entry['cch_code'] ).'"
        ');
      }
    }

    return true;
  }

  /** Cette fonction permet de gérer l'ajout du cache en base de données.
   *  @return bool true en cas de succès, false en cas d'erreur
   */
  private function addInBdd(){
    global $config;

    if( !ria_array_key_exists(['code', 'name'], $this->group) ){
      return false;
    }

    if( trim($this->key) == '' ){
      return false;
    }

    if( !is_numeric($this->expiration) || $this->expiration < 0 ){
      return false;
    }

    if( !isset($this->group['desc']) ){
      $this->group['desc'] = '';
    }

    $date_expiration = new DateTime();

    if( $this->expiration > 0 ){
      $date_expiration->modify( '+'.$this->expiration.' seconds' );
    }else{
      $date_expiration->modify( '+100 years' );
    }

    // Recherche le code en base, on en profite pour récupérer le JSON des clés
    $res = ria_mysql_query('
      select cch_keys, cch_date_created
      from cch_caches
      where cch_tnt_id = '.$config['tnt_id'].'
        and cch_code = "'.addslashes( $this->group['code'] ).'"
    ');

    $date_created = 'now()';

    $keys = [];
    if( $res && ria_mysql_num_rows($res) > 0 ){
      $r = ria_mysql_fetch_assoc( $res );

      $keys = json_decode( $r['cch_keys'], true );
      $date_created = '"'.$r['cch_date_created'].'"';
    }

    $keys[ $this->key ] = $date_expiration->format('Y-m-d H:i:s');
    $json_keys = json_encode( $keys, true );

    $cols = $values = [];

    $cols[] = 'cch_tnt_id';
    $values[] = $config['tnt_id'];

    $cols[] = 'cch_wst_id';
    $values[] = $config['wst_id'];

    $cols[] = 'cch_code';
    $values[] = '"'.addslashes( $this->group['code'] ).'"';

    $cols[] = 'cch_name';
    $values[] = '"'.addslashes( $this->group['name'] ).'"';

    $cols[] = 'cch_desc';
    $values[] = '"'.addslashes( $this->group['desc'] ).'"';

    $cols[] = 'cch_keys';
    $values[] = '"'.addslashes( $json_keys ).'"';

    $cols[] = 'cch_date_end';
    $values[] = '"'.$date_expiration->format('Y-m-d H:i:s').'"';

    $cols[] = 'cch_date_created';
    $values[] = $date_created;

    return ria_mysql_query('
      replace into cch_caches
        ( '.implode( ', ', $cols ).' )
      values
        ( '.implode( ', ', $values ).' )
    ');
  }

  /** Cette fonction permet de transformer la clé donnée pour le cache en une clé toujours valide et unique.
   *  La fonction s'assure notamment que les paramètres de contextes soient fournies (tnt_id, wst_id, lang)
   *  @return string La clé mise en forme
   */
  private static function transformKey( $key ){
    global $config;
    return $config['tnt_id'].':'.(isset($config['wst_id']) ? $config['wst_id'] : 0).':'.i18n::getLang().':'.md5( $key );
  }
}

<?php
// \cond onlyria

/**`\defgroup mailjet MailJet
 *	\ingroup emailing
 *	Ce module contient les fonctions et classes nécessaires à l'interfaçage avec la plateforme d'emailing MailJet.
 *	@{
 */
	require_once('newsletter.inc.php');

	class MailJet {
		private $apiUrl 	= 'https://api.mailjet.com/v3/REST/';
		private $action		= '';
		private $method		= 'GET';
		private $params		= array();


		private $_apiKey 	= '';
		private $_secretKey = '';

		/**	Constructeur
		 *	@param $api_key Obligatoire, clé d'identification
		 *	@param $secret_key Obligatoire, clé secrètre d'authentification
		 */
		public function __construct( $api_key, $secret_key ){
			$this->_apiKey 		= $api_key;
			$this->_secretKey 	= $secret_key;
		}

		/** Cette fonction permet d'ajouter une liste de contact
		 *	@param string $name Obligatoire, nom de la liste
		 *	@return int L'identifiant de la liste créée en cas de succès, False dans le cas contraire
		 */
		public function AddList( $name ){
			if( trim($name) == '' ){
				return false;
			}

			$this->action = 'contactslist';
			$this->method = 'POST';
			$this->params = array( 'Name' => $name );

			$response = $this->Call();
			if( $response === false ){
				return false;
			}

			return $response['Data'][0]['ID'];
		}

		/** Cette fonction permet de récupérer les listes de contacts
		 *	@param $idList Optionnel, identifiant d'une liste de contact
		 *	@return array Un tableau contenant les informations tel que retourné par MailJet
		 */
		public function getContactLists( $idList=0 ){
			$this->action = 'contactslist';
			$this->method = 'GET';

			if( is_numeric($idList) || $idList > 0 ){
				$params = array(
					'ID' => $idList
				);
			}

			return self::call();
		}

		/** Cette fonction permet de classer un contact dans une liste
		 *	@param $idCnt Obligatoire, identifiant d'un contact
		 *	@param $idList Obligatoire, identifiant d'une liste
		 *	@return bool True en cas de succès, False dans le cas contraire
		 */
		public function addContactInList( $email, $idList ){
			if( trim($email) == '' ){
				return false;
			}

			if( !is_numeric($idList) || $idList<=0 ){
				return false;
			}

			$this->action = 'contactslist/'.$idList.'/managecontact';
			$this->method = 'POST';
			$this->params = array( 'Email' => $email, 'Action' => 'addforce' );

			return $this->call();
		}

		/** Cette fonction permet de désinscrire un contact dans une liste
		 *	@param $idCnt Obligatoire, identifiant d'un contact
		 *	@param $idList Obligatoire, identifiant d'une liste
		 *	@return bool True en cas de succès, False dans le cas contraire
		 */
		public function unsubscribedContactInList( $email, $idList ){
			if( trim($email) == '' ){
				return false;
			}

			if( !is_numeric($idList) || $idList<=0 ){
				return false;
			}

			$this->action = 'contactslist/'.$idList.'/managecontact';
			$this->method = 'POST';
			$this->params = array( 'Email' => $email, 'Action' => 'unsub' );

			return $this->call();
		}

		/** Cette fonction permet de supprimer un contact d'une liste
		 *	@param $idCnt Obligatoire, identifiant d'un contact
		 *	@param $idList Obligatoire, identifiant d'une liste
		 *	@return bool True en cas de succès, False dans le cas contraire
		 */
		public function delContactInList( $email, $idList ){
			if( trim($email) == '' ){
				return false;
			}

			if( !is_numeric($idList) || $idList<=0 ){
				return false;
			}

			$this->action = 'contactslist/'.$idList.'/managecontact';
			$this->method = 'POST';
			$this->params = array( 'Email' => $email, 'Action' => 'remove' );

			return $this->call();
		}

		/** Cette fonction permet de récupérer un ou plusieurs contacts selon différents paramètres
		 *	@param $idList Optionnel, identifiant d'une liste de contact
		 *	@param $isUnsubscibed Optionnel, par défaut tous les contacts sont retournés, mettre True pour n'avoir que ce désabonnée, False pour tous les autres (donc abonnés)
		 *	@return array Un tableau contenant les informations tel que retourné par MailJet
		 */
		public function getContacts( $idList=0, $isUnsubscibed=null, $limit=10, $offset=0 ){
			$this->params = array( 'limit' => $limit, 'offset' => $offset );
			$this->action = 'contact';
			$this->method = 'GET';

			if( is_numeric($idList) && $idList > 0 ){
				$this->params = array_merge( $this->params, array(
					'ContactsList' => $idList
				));
			}

			if( $isUnsubscibed !== null ){
				$this->params = array_merge( $this->params, array(
					'IsUnsubscribed' => ( $isUnsubscibed ? 1 : 0 )
				));
			}

			return self::call();
		}

		/** Cette fonction permet de récupérer tous les contacts d'une liste
		 *	@param $idList Obligatoire, identifiant d'une liste de contact
		 *	@return array Un tableau contenant :
		 *					-
		 */
		public function getAllContactByList( $idList ){
			if( !is_numeric($idList) || $idList<=0 ){
			    return false;
			}

			$ar_contact = array();

			$i = 0;
			while( true ){
				if( $i > 1000 ){
					break;
				}

				$response = self::getContacts( $idList, null, (($i+1)*1000), ($i*1000) );
				if( $response === false ){
					break;
				}

				foreach( $response['Data'] as $one_contact ){
					if( $one_contact['Email'] ){
						exit;
					}

					$ar_contact[] = $one_contact['Email'];
				}


				if( $response['Count'] < 1000 ){
					break;
				}

				$i++;
			}

			return $ar_contact;
		}

		/** Cette fonction permet de vérifier qu'une adresse mail existe sur MailJet
		 *	@param string $email Obligatoire, adresse mail
		 *	@return int L'identifiant si l'adresse mail existe, False dans le cas contraire
		 */
		public function existsContact( $email ){
			if( trim($email) == '' ){
				return false;
			}

			global $config, $memcached;

			$key_memcached = 'existsContact2:'.$config['tnt_id'].':'.$email;
			if( $get = $memcached->get($key_memcached) ){
				return ( $get == 'ko' ? false : $get );
			}

			$this->params = array();
			$this->action = 'contact/'.$email;
			$this->method = 'GET';

			$response = self::call( false );

			$exists = false;
			if( $response !== false ){
				$exists = $response['Data'][0]['ID'];
			}

			$memcached->set($key_memcached, ($exists ? $exists : 'ko'), 300 );
			return $exists;
		}

		/** Cette fonction permet de récupérer un tableau de toutes les listes où se trouve un contact sur MailJet.
		 *	@param $idCnt Obligatoire, identifiant du contact sur MailJet
		 *	@return array Un tableau contenant les identifiants de listes
		 */
		public function getContactAllLists( $idCnt ){
			if( !is_numeric($idCnt) || $idCnt<=0 ){
			    return false;
			}

			global $config, $memcached;

			$key_memcached = 'getContactAllLists:v2:'.$config['tnt_id'].':'.$idCnt;
			if( $get = $memcached->get($key_memcached) ){
				switch ($get) {
					case 'none'  : $res = false; break;
					case 'empty' : $res = array(); break;
					default      : $res = $get; break;
				}

				return $res;
			}

			$this->params = array();
			$this->action = 'contact/'.$idCnt.'/getcontactslists';
			$this->method = 'GET';

			$response = self::call();

			$ar_list = false;

			if( is_array($response) && array_key_exists('Data', $response) ){
				$ar_list = array();

				foreach( $response['Data'] as $one_list ){
					if( $one_list['IsUnsub'] != '1' ){
						$ar_list[] = $one_list['ListID'];
					}
				}
			}

			$val_cache = 'none';
			if (is_array($ar_list)) {
				$val_cache = 'empty';

				if (count($ar_list)) {
					$val_cache = $ar_list;
				}
			}

			$memcached->set( $key_memcached, $ar_list, 300 );

			return $ar_list;
		}

		/** Cette fonction permet de créer un contact
		 *	@param string $email Obligatoire, adresse e-mail du contact
		 *	@param string $name Optionnel, nom du contact
		 *	@return bool False en cas d'échec sinon l'identifiant du contact sur MailJet
		 */
		public function addContact( $email, $name='' ){
			if( !isemail($email) ){
				return false;
			}

			$this->action = 'contact';
			$this->method = 'POST';

			$this->params = array(
				'Email' => $email
			);

			if( trim($name) != '' ){
				$this->params = array_merge( $this->params, array(
					'Name' => $name
				));
			}

			$response = self::call();

			if( $response === false ){
				return false;
			}

			return $response['Data'][0]['ID'];
		}

		/** Cette fonction permet de classer un contact dans une liste
		 *	@param $idUser Obligatoire, identifiant d'un contact
		 *	@param $idList Obligatoire, identifiant d'une liste
		 *	@return bool True en cas de succès, False dans le cas contraire
		 */
		// public function

		/** Cette fonction permet d'envoyer une requête à MailJet
		*	@return Le résultat MailJet sous format d'un tableau
		*/
		private function call( $show_error=false ){
			$ch = curl_init();

			$url_api_call = $this->apiUrl.$this->action;

			switch( $this->method ){
				case 'POST': {
					curl_setopt($ch, CURLOPT_POST, true);
					curl_setopt($ch, CURLOPT_POSTFIELDS, $this->params);
					break;
				}
				case 'GET': {
					$url_api_call .= '?'.http_build_query($this->params);
					break;
				}
				case 'PUT': {
					curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
					curl_setopt($ch, CURLOPT_POSTFIELDS, $this->params);
					break;
				}
				case 'DELETE': {
					curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
					curl_setopt($ch, CURLOPT_POSTFIELDS, $this->params);
					break;
				}
			}
			curl_setopt($ch, CURLOPT_URL, $url_api_call);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
			curl_setopt($ch, CURLOPT_TIMEOUT, 5);
			curl_setopt($ch, CURLOPT_USERPWD, $this->_apiKey.':'.$this->_secretKey);

			$response = curl_exec($ch);
			curl_close($ch);

			$json = json_decode( $response, true );

			if( !is_array($json) || array_key_exists('ErrorInfo', $json) ){
				if( $show_error ){
					error_log('Erreur call() MailJet ('.$url_api_call.') : '.print_r($json, true));
				}

				return false;
			}

			return $json;
		}
	}

/// @}

// \endcond

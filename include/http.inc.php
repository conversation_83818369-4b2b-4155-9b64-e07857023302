<?php

/**	\ingroup model_http
 *	@{
 */

/**	Redirige un client vers une autre url. La redirection est indiquée comme permanente,
 *	invalidant l'url d'appel. Après l'envoi de la redirection, cette fonction arrête
 *	l'exécution du script.
 *	@param $destination Url de destination de la redirection
 *	@return void
 */
function http_redirect_permanent( $destination ){
	header('Moved Permanently',true,301);
	header('Location: '.$destination);
	exit;	
}

/**	Envoi une erreur 403 au navigateur client. Cette fonction interrompt l'exécution du
 * 	script.
 */
function http_403(){
	global $config, $admin_account;

	header('HTTP/1.1 403 Forbidden',true,403);
	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ) {
		require_once( dirname(__FILE__).'/../htdocs/admin/errors/403.php');
	}
	exit;
}

/**	Contrôle le champ HTTP_REFERER et déclenche un erreur 403 en cas de risque de requête CSRF
 *	@see https://www.cert.ssi.gouv.fr/information/CERTA-2008-INF-003/
 */
function http_check_referrer(){

	// Check referer si POST
	if( isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'POST' ){
		// Vérifie que toutes les informations nécessaires au contrôle sont bien présentes
		if( !isset($_SERVER['HTTP_REFERER'], $_SERVER['SERVER_NAME']) || trim($_SERVER['HTTP_REFERER']) == '' || trim($_SERVER['SERVER_NAME']) == '' ){
			http_403();			
			exit;
		}
		
		// Contrôle que l'on retrouve bien le site dans le referer
		if( preg_match('/^(http|https):\/\/'.$_SERVER['SERVER_NAME'].'/', $_SERVER['HTTP_REFERER']) !== 1 ){
			http_403();
			exit;
		}
	}
	
}

/**	Envoie une erreur 400 au navigateur client. Cette fonction ne stoppe pas l'exécution
 *	du script. Il est donc possible d'envoyer ensuite une page html expliquant l'erreur
 *	et ses causes possibles.
 *	@return void
 */
function http_400(){
	header('HTTP/1.0 400 Bad Request');
	exit;
}

/**	Envoie une erreur 404 au navigateur client. Cette fonction ne stoppe pas l'exécution
 *	du script. Il est donc possible d'envoyer ensuite une page html expliquant l'erreur
 *	et ses causes possibles.
 *	@return void
 */
function http_404(){
	header('HTTP/1.0 404 Not Found');
	exit;
}

/**	Cette fonction redirige un client vers une autre url. La redirection est indiquée comme
 *	temporaire. Après l'envoi de la redirection, cette fonction interrompt l'exécution du
 *	script.
 *	@param $destination Url de destination de la redirection
 */
function http_redirect_temporarily( $destination ){
	header('Moved Temporarily',true,303);
	header('Location: '.$destination);
	exit;
}

/**	Cette fonction va envoyer les entêtes HTTP permettant une mise en cache du fichier,
 * 	pour la durée passée en argument
 * 	@param $duration Facultatif, nombre de secondes pendant lesquelles ce fichier peut être mis en cache. La valeur par défaut est de 86400 (1 journée)
 */
function http_cache_control( $duration=86400 ){
    $expires = gmdate("D, d M Y H:i:s", time() + $duration) . " GMT";

    header( 'Cache-Control: max-age='.$duration.', public, no-check', true );
    header( 'Expires: '.$expires );
	header( 'Pragma: cache', true );

}

/// @}
 


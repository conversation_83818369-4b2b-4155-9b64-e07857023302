<?php

/**
 * Class Container
 */
class Container implements ArrayAccess {
	/**
	 * Tableau contenant les valeurs du conteneur
	 *
	 * @var array $container
	 */
	protected $container = array();
	/**
	 * Tableau de cache
	 *
	 * @var array $cache
	 */
	protected $cache = array();

	/**
	 * Container constructor.
	 *
	 * @param array|null $data
	 */
	public function __construct( array $data = null) {
		if (!is_null($data)) {
			$this->container = $data;
		}
	}

	/**
	 * Cette fonction permet de récupérer une valeur du conteneur au première appel la valeur sera mise en cache
	 * Si c'est une closure elle sera exécuté et la valeur sera mise en cache
	 *
	 * @param string $key
	 * @return mixed
	 */
	public function get($key)
	{
		$value = null;
		if (!array_key_exists($key, $this->cache)) {
			$value = $this->offsetGet($key);
			if ($value instanceof Closure) {
				$value = $value($this);
			}
			if (!is_null($value)) {
				$this->cache[$key] = $value;
			}
		}else{
			$value = $this->cache[$key];
		}

		return $value;
	}

	/**
	 * @param mixed $offset
	 * @param mixed $value
	 */
	public function offsetSet( $offset, $value) {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

	/**
	 * @param mixed $offset
	 *
	 * @return bool
	 */
	public function offsetExists($offset) {
        return isset($this->container[$offset]);
    }

	/**
	 * @param mixed $offset
	 */
	public function offsetUnset( $offset) {
        unset($this->container[$offset]);
    }

	/**
	 * @param mixed $offset
	 *
	 * @return mixed|null
	 */
	public function offsetGet($offset) {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }
}
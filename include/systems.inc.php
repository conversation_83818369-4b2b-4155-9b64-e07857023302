<?php
	require_once( 'db.inc.php' );

/**	\defgroup i18n_languages Langues
 *	\ingroup i18n
 *	Ce module comprend les fonctions nécessaires à la gestion des langues. Nous utilisons dans ce module la norme
 *	ISO 639-1 comme base de données de référence.
 *	@see https://fr.wikipedia.org/wiki/Liste_des_codes_ISO_639-1
 *	@{
 */

	/** Cette fonction permet de récupérer le nom d'une langue
	 *	@param string $lng Obligatoire, code de la langue au format ISO 639-1
	 *	@return string Retourne le nom de la langue si elle existe
	 *	@return string Retourne une chaine vide dans le cas contraire
	 */
	function i18n_languages_get_name( $lng ){
		if (trim($lng) == '') {
			return false;
		}

		$get = ria_mysql_query( '
			select lng_name from sys_languages
			where if(lng_cnt_code="", lng_code, concat(lng_code, "-", lng_cnt_code)) = \''.addslashes( strtolower($lng) ).'\'
		');

		if( !$get || !ria_mysql_num_rows($get) ){
			return false;
		}

		return ria_mysql_result( $get, 0, 'lng_name' );
	}

	/** Cette fonction permet de récupérer le code d'une langue
	 *	@param string $name Obligatoire, nom de la langue au format ISO 639-1
	 *	@return string Retourne le code de la langue si elle existe
	 *	@return string Retourne une chaine vide dans le cas contraire
	 */
	function i18n_languages_get_code( $name ){
		$get = ria_mysql_query( '
			select lng_code from sys_languages where lng_name = \''.addslashes( $name ).'\'
		' );

		if( !$get || !ria_mysql_num_rows($get) ){
			return false;
		}

		return ria_mysql_result( $get, 0, 'lng_code' );
	}

	/** Cette fonction permet de tester si une langue existe dans la base de données
	 *	@param string $lng Obligatoire, code de la langue au format ISO 639-1
	 *	@return bool Retourne true si la langue est reconnue
	 *	@return bool Retourne false dans le cas contraire
	 */
	function i18n_languages_exists( $lng ){
		if( !trim($lng) ) return false;

		return ria_mysql_num_rows( ria_mysql_query('
			select 1 from sys_languages
			where if(lng_cnt_code="", lng_code, concat(lng_code, "-", lng_cnt_code))=\''.addslashes(strtolower($lng)).'\'
		') )>0;
	}

	/** Cette fonction récupère toutes les langues ainsi que leur code
	 *	@return bool False en cas d'échec
	 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
	 *		- code : Code ISO de la langue
	 *		- name : Nom de la langue
	 */
	function i18n_languages_get(){

		return ria_mysql_query('
			select lng_code as code, lng_name as name
			from sys_languages
		');
	}

	/**	Cette fonction crée et retourne un tableau de toutes les langues.
	 *	Elle utilise memcached dans la mesure du possible (24 heures)
	 *	@param bool $keys Optionnel, si activé, ne retourne que les clés (les codes ISO)
	 *	@return array Un tableau associatif "code" (code ISO de la langue) => "name" (nom)
	 */
	function i18n_languages_get_array( $keys = false ){
		global $memcached;

		$memkey = 'i18n:languagesget';

		if( $kid = $memcached->get($memkey) ){
			return $keys && is_array($kid) ? array_keys($kid) : $kid;
		}

		$lngs = array();

		if( $rlng = i18n_languages_get() ){
			while( $lng = ria_mysql_fetch_array($rlng) ){
				$lngs[ $lng['code'] ] = $lng['name'];
			}
		}

		// cache de 24 heures
		$memcached->set($memkey, $lngs, 3600 * 24);

		return $keys ? array_keys($lngs) : $lngs;
	}

/// @}

/**	\defgroup i18n_contexts Contextes de traduction
 *	\ingroup i18n
 *	Ce module comprend les fonctions nécessaires à la gestion des contextes de traduction
 *	@deprecated Les fonctions ci-dessous ne doivent pas être appelées directement, l'utilisation de la classe i18n est recommandée.
 *	@{
 */
	/** Cette fonction permet de tester si un contexte de traduction existe dans la base de données.
	 * 	S'il existe, la réponse est mise en cache pour 24h.
	 *	@param string $code Obligatoire, code d'un contexte
	 *	@return bool Retourne true s'il existe
	 *	@return bool Retourne false dans le cas contraire
	 */
	function i18n_contexts_exists( $code ){
		global $config, $memcached;

		if( !trim($code) ){
			return false;
		}

		$key_memcached = 'tnt:'.$config['tnt_id'].':i18n_contexts_exists:'.$code;
		$exists = $memcached->get( $key_memcached );
		if( $exists ){
			return true;
		}

		$exists = ria_mysql_num_rows( ria_mysql_query('select 1 from sys_translate_contexts where stc_code=\''.addslashes($code).'\'') )>0;
		if( $exists ){
			$memcached->set( $key_memcached, $exists, 60*60*24 );
		}
		return $exists;
	}

	/** Cette fonction permet d'ajouter un context de traduction
	 *	@param string $context Obligatoire code qui sera utilisé pour définir ce contexte
	 *	@param string $name Optionnel, nom donnée à ce contexte
	 *	@return bool Retourne true si l'ajout s'est correctement déroulée
	 *	@return bool Retourne false dans le cas contraire
	 */
	function i18n_contexts_add( $context, $name='' ){
		if( !trim($context) ){
			return false;
		}

		return ria_mysql_query('
			insert into sys_translate_contexts
				( stc_code, stc_name )
			values
				( \''.addslashes( $context ).'\', '.( trim($name)!='' ? '\''.addslashes( strtoupper($name) ).'\'' : 'null' ).' )
		');

	}

	/** Cette fonction permet de récupérer les contextes de traduction
	 *	@param string $code Optionnel, code utilisé par le contexte
	 *	@return bool Retourne false si le paramètre est faux
	 *	@return resource Retourne un résultat MySQL contenant :
	 *			- code : code du contexte
	 *			- name : nom donné au contexte
	 */
	function i18n_contexts_get( $code='' ){
		global $config;

		if( trim($code)!='' && !i18n_contexts_exists($code) ){
			return false;
		}

		$get = '
			select stc_code as code, if(stc_name is not null, stc_name, stc_code) as name
			from sys_translate_contexts
			join tnt_translates on (ttl_tnt_id='.$config['tnt_id'].' and ttl_stc_code=stc_code)
		';

		if( trim($code)!='' ){
			$get .= ' where stc_code = \''.addslashes( $code ).'\'';
		}

		$get .= '
			group by stc_code
			order by name
		';

		return ria_mysql_query( $get );
	}

	/** Cette fonction permet de récupérer le nom d'un contexte de traduction
	 *	@param string $code Obligatoire, code du contexte
	 *	@return bool Retourne false si le paramètre est faux ou si le context n'existe pas
	 *	@return string Retourne une chaine de caractère qui correspond au nom du contexte de traduction
	 */
	function i18n_contexts_get_name( $code ){
		if( !i18n_contexts_exists($code) ){
			return false;
		}

		$res = ria_mysql_query( 'select stc_name from sys_translate_contexts where stc_code = \''.addslashes( $code ).'\'');
		if( !$res ){
			return false;
		}

		return ria_mysql_result( $res, 0, 0 );
	}

/// @}

/**	\defgroup i18n_translations Traductions
 *	\ingroup i18n
 *	Ce module comprend les fonctions nécessaires à la gestion des traductions de site client
 *	@deprecated Les fonctions ci-dessous ne doivent pas être appelées directement, l'utilisation de la classe i18n est recommandée.
 *	@{
 */

	/** Cette fonction permet d'ajouter une traduction
	 *	@param string $lng Obligatoire, code ISO 639-1 de la langue de traduction
	 *	@param string $context Obligatoire, code du contexte utilisé pour la traduction
	 *	@param string $original Obligatoire, texte original
	 *	@param string $translation Optionnel, texte après traduction, par défaut aucun ne sera sauvegardé
	 *	@return bool Retourne true si l'ajout s'est correctement déroulé, false dans le cas contraire
	 */
	function i18n_translates_add( $lng, $context, $original, $translation=''){
		global $config;

		if( trim($translation)!='' && i18n_translates_exists($original, $lng, $context) ){
			i18n_translates_update( $lng, md5($original), $translation, $context );
		}
		if( !i18n_languages_exists($lng) ){
			return false;
		}
		if( !i18n_contexts_exists($context) ){
			return false;
		}
		if( !trim($original) ){
			return false;
		}

		return ria_mysql_query('
			insert into tnt_translates
				( ttl_tnt_id, ttl_lng_code, ttl_stc_code, ttl_md5, ttl_original, ttl_translate )
			values
				( '.$config['tnt_id'].', \''.strtolower($lng).'\', \''.$context.'\', md5(\''.addslashes( $original ).'\'), \''.addslashes( $original ).'\', '.( trim($translation)!='' ? '\''.addslashes( $translation ).'\'' : 'null' ).' )
		');

	}

	/** Cette fonction permet de mettre à jour une traduction
	 *	@param string $lng Obligatoire, code ISO 639-1 de la langue de traduction
	 *	@param string $md5 Obligatoire, md5 identifiant la traduction
	 *	@param string $translation Obligatoire, texte après traduction
	 *	@param string $context Obligatoire, contexte de traduction
	 */
	function i18n_translates_update( $lng, $md5, $translation, $context='' ){
		global $config;

		if( !i18n_languages_exists($lng) ){
			return false;
		}
		if( !trim($md5) ){
			return false;
		}
		if( trim($context)!='' && !i18n_contexts_exists($context) ){
			return false;
		}

		$update = '
			update tnt_translates
			set ttl_translate='.( trim($translation)!='' ? '\''.addslashes( $translation ).'\'' : 'null' ).'
			where ttl_tnt_id = '.$config['tnt_id'].'
				and ttl_lng_code = \''.strtolower( $lng ).'\'
				and ttl_md5 = \''.$md5.'\'
		';

		if( trim($context)!='' ){
			$update .= ' and ttl_stc_code = \''.strtoupper( $context ).'\'';
		}
		return ria_mysql_query( $update );
	}

	/** Cette fonction permet de récupérer une traduction
	 *	@param string $lng Optionnel, code ISO 639-1 de la langue de traduction
	 *	@param string $context Optionnel, code du contexte de traduction
	 *	@param string $md5 Optionnel, md5 du texte d'origine sur lequel filtrer le résultat (fonctionnement à vérifier car un md5() est appliqué dessus par la fonction)
	 * 	@param bool $is_translate Optionnel, par défaut retourne tous les textes, mettre à True pour ne retourne que les textes qui sont traduits, mettre False pour les textes non traduits
	 *	@param string $filter Optionnel, retourne les textes commençant par le filtre
	 *	@param int $wst_id Optionnel, id du website rechercher
	 *	@return resource Retourne un résultat MySQL contenant :
	 *			- md5 : md5() du texte d'origine
	 *			- original : texte d'origine
	 *			- translation : traduction du texte
	 *			- code : code du contexte utilisé pour la traduction
	 *			- context : contexte utilisé pour la traduction
	 *			- lng_code : code de la langue
	 *	@return bool Retourne false si l'un des paramètres est omis ou faux
	 */
	function i18n_translates_get( $lng='', $context='', $md5='', $is_translate=null, $filter='', $wst_id=0 ){
		global $config;

		$get = '
			select
				ttl_md5 as "md5", ttl_original as original, ttl_translate as translation, stc_code as code, if(stc_name is not null, stc_name, stc_code) as context,
				ttl_lng_code as lng_code
			from tnt_translates, sys_translate_contexts, tnt_websites_languages
			where ttl_tnt_id = '.$config['tnt_id'].'
				and stc_code=ttl_stc_code
		';

		if( trim($lng)!='' ){
			$get .= ' and ttl_lng_code = \''.addslashes( strtolower($lng) ).'\'';
		}
		if( trim($md5)!='' ){
			$get .= ' and ttl_md5 = md5( \''.addslashes( $md5 ).'\' )';
		}
		if( trim($context)!='' ){
			$get .= ' and ttl_stc_code = \''.addslashes( strtoupper( $context ) ).'\'';
		}

		if( $is_translate!==null ){
			if( $is_translate ){
				$get .= ' and ttl_translate is not null';
			}else{
				$get .= ' and ttl_translate is null';
			}
		}
		if( trim($filter)!='' && trim($filter)!='undefined' ){
			$get .= ' and LOWER(ttl_original) like lower(\''.addslashes( ($filter) ).'%\')';
		}

		if( $wst_id!=0 ){
			$get .= ' and twl_wst_id = '.$wst_id;
			$get .= ' and ttl_lng_code = twl_lng_code';
		}else{
			$get .= ' and twl_wst_id = '.$config['wst_id'];
			$get .= ' and ttl_lng_code = twl_lng_code';
		}

		$get .= '
			order by ttl_original asc, ttl_translate asc
		';

		return ria_mysql_query( $get );
	}

	/** Cette fonction permet de tester l'existance d'une traduction
	 *	@param string $text Obligatoire, il s'agit du texte à traduire, le texte dois être en minuscule
	 *	@param string $lng Obligatoire, code ISO 639-1 de la langue de traduction
	 *	@param string $context Obligatoire, contexte de traduction
	 *	@return bool Retourne true s'il existe
	 *	@return bool Retourne false dans le cas contraire
	 */
	function i18n_translates_exists( $text, $lng, $context ){
		global $config;

		if( !trim($text) ){
			return false;
		}

		// $text = strtolower( $text );
		return ria_mysql_num_rows( ria_mysql_query('
			select 1 from tnt_translates
			where ttl_tnt_id='.$config['tnt_id'].'
				and ttl_md5=md5(\''.addslashes( $text ).'\')
				and ttl_lng_code=\''.addslashes( strtolower($lng) ).'\'
				and ttl_stc_code=\''.addslashes( strtoupper($context)).'\'
		') )>0;
	}

	/** Cette fonction permet de supprimer un texte statique à traduire.
	 *	@param string $lng Obligatoire, code ISO 639-1 de la langue de traduction
	 *	@param string $context Obligatoire, contexte de traduction
	 *	@param string $md5 Obligatoire, md5 de la chaine à traduire
	 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
	 */
	function i18n_translates_del( $lng, $context, $md5 ){
		global $config;

		// Contrôle les paramètres d'entrée
		if(
			trim($lng)==''
			|| trim($context)==''
			|| trim($md5)==''
		){
			return false;
		}

		// Supprime le texte à traduire
		return ria_mysql_query('
			delete from tnt_translates
			where ttl_tnt_id='.$config['tnt_id'].'
				and ttl_lng_code="'.addslashes( $lng ).'"
				and ttl_stc_code="'.addslashes( $context ).'"
				and ttl_md5="'.addslashes( $md5 ).'"
		');
	}

/// @}

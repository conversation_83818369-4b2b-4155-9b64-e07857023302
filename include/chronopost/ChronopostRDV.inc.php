<?php
require_once('chronopost/models/CreneauWS.php');
require_once('chronopost/models/wsResponse.php');
require_once('chronopost/models/searchDeliverySlot.php');
require_once('chronopost/models/searchDeliverySlotResponse.php');
require_once('chronopost/models/deliverySlotResponse.php');
require_once('chronopost/models/confirmDeliverySlot.php');
require_once('chronopost/models/confirmDeliverySlotResponse.php');
require_once('chronopost/models/serviceResponse.php');
require_once('chronopost/models/productService.php');
require_once('chronopost/models/slot.php');

/** \defgroup chronopostRDV ChronopostRDV
 *	\ingroup scm
 *	Cette permet de créer les objects pour les requêtes SOAP avec des paramètres par default,
 *	 modifiable à travers les setters de l'objet retourné.
 *	 Elle permet aussi de formater les créneaux dans un tableau associatif a 2 niveaux avec le premier niveaux tous les créneaux
 *	 et en deuxième niveaux les object slot qui contienent l'informations des créneaux
 *
 * 	Variable obligatoire :
 * 	- $config['chronordv_shipper_zipcode'] variable avec le zipcode par default
 *	Exemple : Récupération des créneaux
 *	\code{.php}
 *		try{
 *			$ChronopostRDV = new ChronopostRDV();
 *			$service = new CreneauWS();
 *			$searchDeliverySlot = $ChronopostRDV->searchDeliverySlot(true);
 *			$dateBegin = new DateTime($date);
 *			$dateEnd = new DateTime($date);
 *			$dateEnd->modify('+22 hours');
 *			$searchDeliverySlot->setProductType('FRESH')
 *					->setShipperZipCode($config['chronordv_shipper_zipcode'])
 *					->setRecipientZipCode($zipcode)
 *					->setDateBegin($dateBegin)
 *					->setDateEnd($dateEnd);
 *			$deliverySlotResponse =  $service->searchDeliverySlot($searchDeliverySlot)->getReturn();
 *		}catch(Exception $e){
 *			error_log($e->getMessage());
 *		}
 *	\endcode
 *	@{
 */
/**	\brief Cette classe est permet de manipuler les réponses du soap et de crééer certaines requête.
 *
 */
class ChronopostRDV {

	///	@var object $deliverySlotResponse Instance de la classe deliverySlotResponse
	protected $deliverySlotResponse = null;

	///	@var int $order_id Identifiant de la commande actuelle
	protected $order_id = null;

	///	@var string $CodeSlot Le code du créneau retourné choisi
	protected $CodeSlot = null;

	///	@var string Le rang du créneau choisi
	protected $rank = null;

	///	@var array Liste des créneaux (instance de slot)
	protected $slots = array();

	///	@var array Liste des code postaux couvert par le service chronoRDV
	public static $COVERED_ZIPCODES = array(
		'75001',
		'75002',
		'75003',
		'75004',
		'75005',
		'75006',
		'75007',
		'75008',
		'75009',
		'75010',
		'75011',
		'75012',
		'75013',
		'75014',
		'75015',
		'75016',
		'75017',
		'75018',
		'75019',
		'75020',
		'75116',
		'78000',
		'78110',
		'78140',
		'78150',
		'78160',
		'78170',
		'78220',
		'78230',
		'78290',
		'78350',
		'78360',
		'78380',
		'78400',
		'78420',
		'78430',
		'78560',
		'92000',
		'92100',
		'92110',
		'92120',
		'92130',
		'92140',
		'92150',
		'92160',
		'92170',
		'92190',
		'92200',
		'92210',
		'92220',
		'92230',
		'92240',
		'92250',
		'92260',
		'92270',
		'92290',
		'92300',
		'92310',
		'92320',
		'92330',
		'92340',
		'92350',
		'92360',
		'92370',
		'92380',
		'92390',
		'92400',
		'92410',
		'92420',
		'92430',
		'92500',
		'92600',
		'92700',
		'92800',
		'93000',
		'93100',
		'93110',
		'93120',
		'93130',
		'93140',
		'93150',
		'93160',
		'93170',
		'93190',
		'93200',
		'93210',
		'93220',
		'93230',
		'93240',
		'93250',
		'93260',
		'93300',
		'93310',
		'93320',
		'93330',
		'93340',
		'93350',
		'93360',
		'93370',
		'93380',
		'93390',
		'93400',
		'93410',
		'93430',
		'93440',
		'93450',
		'93460',
		'93470',
		'93500',
		'93700',
		'93800',
		'94000',
		'94100',
		'94110',
		'94120',
		'94130',
		'94140',
		'94150',
		'94160',
		'94170',
		'94190',
		'94200',
		'94210',
		'94220',
		'94230',
		'94240',
		'94250',
		'94260',
		'94270',
		'94290',
		'94300',
		'94310',
		'94320',
		'94340',
		'94350',
		'94360',
		'94370',
		'94380',
		'94390',
		'94400',
		'94410',
		'94420',
		'94430',
		'94450',
		'94460',
		'94470',
		'94480',
		'94490',
		'94500',
		'94550',
		'94600',
		'94700',
		'94800',
		'94880',
		'51100',
		'59000',
		'59777',
		'59800',
		'67000',
		'67100',
		'67200',
		'35000',
		'35200',
		'35700',
		'44000',
		'44100',
		'44200',
		'44300',
		'6000',
		'6100',
		'6200',
		'6300',
		'13001',
		'13002',
		'13003',
		'13004',
		'13005',
		'13006',
		'13007',
		'13008',
		'13009',
		'13010',
		'13011',
		'13012',
		'13013',
		'13014',
		'13015',
		'13016',
		'38000',
		'38100',
		'69001',
		'69002',
		'69003',
		'69004',
		'69005',
		'69006',
		'69007',
		'69008',
		'69009',
		'83000',
		'83100',
		'83200',
		'31000',
		'31100',
		'31200',
		'31300',
		'31400',
		'31500',
		'33000',
		'33100',
		'33200',
		'33300',
		'33800',
		'34000',
		'34070',
		'34080',
		'34090',
		'94440',
		'94510',
		'94520'
	);

	/**
	 * Semainier constructor.
	 */
	public function __construct(){
		if( isset($_SESSION['ord_id']) ){
			$this->order_id = $_SESSION['ord_id'];
		}
	}

	/**	Initialise l'identifiant de la commande actuelle
	 *  @param int $order_id Obligatoire, identifiant de la commande actuelle
	 *	@return object $this
	 */
	public function setOrderId($order_id){
		$this->order_id = $order_id;
		return $this;
	}

	/** Retourne le MeshCode
	 *	@return string le MeshCode
	 */
	public function getMeshCode(){
		if( !isset($this->deliverySlotResponse) ){
			throw new Exception('Erreur deliverySlotResponse n\'est pas défini');
		}
		return $this->deliverySlotResponse->getMeshCode();
	}

	/**	Retourne le transactionID
	 *	@return string l'identifiant de la transaction
	 */
	public function getTransactionID(){
		if( !isset($this->deliverySlotResponse) ){
			throw new Exception('Erreur deliverySlotResponse n\'est pas défini');
		}
		return $this->deliverySlotResponse->getTransactionID();
	}

	/** Retourne la liste des créneaux
	 *	@return array liste des créneaux
	 */
	public function getSlotList(){
		return $this->slots;
	}

	/**	Retourne le code du slot
	 *	@return string code du slot
	 */
	public function getCodeSlot(){
		return $this->CodeSlot;
	}

	/**	Retourne le rang du créneau
	 *	@return string le rang du créneau
	 */
	public function getRank(){
		return $this->rank;
	}

	/**	Permet l'injection de la classe deliverySlotResponse si nécessaire
	 *	@param deliverySlotResponse $deliverySlotResponse l'instance à injecter
	 */
	public function setDeliverySlotResponse( deliverySlotResponse $deliverySlotResponse ){
		$this->deliverySlotResponse = $deliverySlotResponse;
		$this->slots = $this->deliverySlotResponse->getSlotList();
	}
	/**	Permet de formater la liste des créneaux
	 *	@return object l'instance
	 */
	public function formatSlotList(){
		if( !isset($this->deliverySlotResponse) ){
			throw new Exception('Erreur deliverySlotResponse n\'est pas défini');
		}
		$dayOfWeek = 0;
		$this->slots = array();
		$slots = $this->deliverySlotResponse->getSlotList();

		if( is_null($slots) || empty($slots) ){
			throw new Exception("Il n'y a aucun slot disponible.");
		}
		foreach( $slots as $slot ){
			if( $dayOfWeek != $slot->getDayOfWeek() ){
				$dayOfWeek = $slot->getDayOfWeek();
			}
			$key = $slot->getStartHour().'.'.$slot->getEndHour();
			$this->slots[$key][$dayOfWeek] = $slot;
		}

		ksort($this->slots, SORT_NUMERIC);

		return $this;
	}

	/**	Réinitialise les informations de chronoRDV pour la commande actuel
	 *	@return object l'instance
	 */
	public function resetSlotInSession(){
		fld_object_values_set($this->order_id, _FLD_DLV_CHRONORDV, '');
		return $this;
	}

	/**	Initialise les informations de chronoRDV pour la commande actuel
	 *	@param string $slot Le créneau choisi format start.end ex : 8.10
	 *	@param string $delivery_date La date de livraison format en
	 *	@param $productType facultatif, type de produit (FRESH,FREEZE)
	 *	@param $productCode facultatif, résultat de la confirmation de chronopost du même non
	 *	@param $serviceCode facultatif, résultat de la confirmation de chronopost du même non
	 *	@param int $slotCode facultatif, Identifiant ChronoRDV du créneau
	 *	@param int $rank facultatif, Identifiant ChronoRDV du rang du créneau
	 *
	 *	@return object l'instance
	 */
	public function saveSlotInSession( $slot, $delivery_date, $productType='FRESH', $productCode='', $serviceCode='', $slotCode='', $rank=''){
		$mesh = $transaction = '';

		if( isset($_SESSION['chronoRDV']) && is_array($_SESSION['chronoRDV']) && ria_array_key_exists(['meshCode', 'transactionId'], $_SESSION['chronoRDV'])){
			$mesh = $_SESSION['chronoRDV']['meshCode'];
			$transaction = $_SESSION['chronoRDV']['transactionId'];
		}

		$value = [
			'slot'			=> $slot,
			'dlv_date'		=> $delivery_date,
			'productType'	=> $productType,
			'productCode'	=> $productCode,
			'serviceCode'	=> $serviceCode,
			'slotCode'		=> $slotCode,
			'rank'			=> $rank,
			'meshCode'		=> $mesh,
			'transactionId'	=> $transaction,
		];

		fld_object_values_set($this->order_id, _FLD_DLV_CHRONORDV, json_encode($value));
		return $this;
	}

	/**	Retourne les informations de chronopostRDV pour la commande actuelle
	 *	@return bool false si erreur, un tableau avec :
	 * 					- slot : créneau choisi
	 * 					- dlv_date :  la date d'envoi
	 * 					- productType : le type de produit choisi (FRESH, FREEZE)
	 */
	public function getSlotInSession(){
		$rdv_data = fld_object_values_get($this->order_id, _FLD_DLV_CHRONORDV, '', false, true);
		$return = false;
		if( $rdv_data ){
			$return = json_decode($rdv_data, true);
		}
		return $return;
	}

	/**	Permet de vérifier si les informations de chronoRDV pour la commande actuel sont toujours valables
	 *	@return bool True si la session et valide, false dans le cas contraire
	 *	@throws Exception
	 */
	public function checkIfSlotInSessionIsAvailable(){
		$rdv_data = fld_object_values_get($this->order_id, _FLD_DLV_CHRONORDV);
		if( !$rdv_data ){
			throw new Exception('Erreur le créneaux ChronoRDV à expiré.');
		}
		if( !isset($this->deliverySlotResponse) ){
			throw new Exception('Erreur deliverySlotResponse n\'est pas défini');
		}
		$rdv = json_decode($rdv_data, true);
		$Session_available = false;
		$slots = $this->deliverySlotResponse->getSlotList();
		$dateBegin = new DateTime($rdv['dlv_date']);
		if( !is_array($slots) ){
			return $Session_available;
		}
		foreach( $slots as $slot ){

			$key = $slot->getStartHour().'.'.$slot->getEndHour();
			if( $rdv['slot'] != $key ){
				continue;
			}
			$slotdate = new DateTime( $slot->getDeliveryDate() );
			if( $slot->getStatus() != 'F' &&  !in_array($slot->getCodeStatus(), array(1,2,3,4,5,6)) && $slotdate == $dateBegin ){

				$_SESSION['chronoRDV']['meshCode'] = $this->getMeshCode();
				$_SESSION['chronoRDV']['transactionId'] = $this->getTransactionID();
				$_SESSION['chronoRDV']['deliverySlotCode'] = $this->CodeSlot = $slot->getDeliverySlotCode();
				$this->rank = $slot->getRank();
				$Session_available = true;
			}
		}
		return $Session_available;
	}

	/**	Permet de tester l'éligibilité d'un code postal a chronoRDV
	 *	@param string $zipcode Code postal
	 *	@param int $delivery_type Facultatif, Type de service de livraison
	 *
	 *	@return bool true si éligible, false si non
	 */
	public function isZipcodeEligable( $zipcode, $delivery_type = 1){
		$is_RDV_eligable = false;
		if( isset($zipcode) && in_array($zipcode, self::$COVERED_ZIPCODES) && $delivery_type == 1){
			$is_RDV_eligable = true;
		}

		return $is_RDV_eligable;
	}

	/**	Cette fonction permet de créer l'objet qui permet de confirmer un créneau d'envoie
	 *	@param DateTime $dateBegin Objet datetime avec la date du jour de livraison
	 *	@param string $productType Type de produit de chronopost utilisé
	 *	@param string $callerTool Facultatif, identifiant d'ou provient le requète par default du webservice
	 *
	 *	@return confirmDeliverySlot
	 */
	public function confirmDeliverySlot( DateTime $dateBegin, $productType, $callerTool='RDVWS'){
		$confirmDeliverySlot = new confirmDeliverySlot();
		$confirmDeliverySlot->setTransactionID($this->getTransactionID())
			->setMeshCode($this->getMeshCode())
			->setCallerTool($callerTool)
			->setProductType($productType)
			->setCodeSlot($this->getCodeSlot())
			->setRank($this->getRank())
			->setDateSelected($dateBegin);
		return $confirmDeliverySlot;
	}

	/**	Cette fonction permet de retourner l'objet searchDeliverySlot pour réaliser des requêtes
	 *	@param bool $isDeliveryDate si la date de début correspond a la date d'envoi
	 *
	 *	@return searchDeliverySlot
	 */
	public function searchDeliverySlot( $isDeliveryDate){
		$searchDeliverySlot = new searchDeliverySlot($isDeliveryDate);
		$searchDeliverySlot->setCallerTool('RDVWS')
				->setShipperCountry('FR')
				->setRecipientCountry('FR')
				->setWeight(ord_orders_weight_get( $this->order_id, 'kg'));
		return $searchDeliverySlot;
	}
}
/// @}

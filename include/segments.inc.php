<?php
// \cond onlyria

require_once('fields.inc.php');

/** \defgroup segments Segments
 * 	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des segments. Les segments sont des catégories dynamiques de comptes clients.
 *	@{
 */

/// Le symbole n'est pas autorisé pour le type de donnée spécifié
define( '_SEG_ERROR_SYMBOL', -1 );
/// La valeur saisie n'est pas un entier valide
define( '_SEG_ERROR_INT', -2 );
/// La valeur saisie n'est pas un décimal valide
define( '_SEG_ERROR_FLOAT', -3 );
/// La valeur saisie n'est pas un booléen valide
define( '_SEG_ERROR_BOOL', -4 );
/// La valeur saisie n'est pas une date valide
define( '_SEG_ERROR_DATE', -5 );
/// La valeur saisie ne fait pas référence à un produit
define( '_SEG_ERROR_NO_PRD', -6 );
/// La valeur saisie ne fait pas référence à une catégorie de produit
define( '_SEG_ERROR_NO_CAT', -7 );
/// La valeur saisie ne fait pas référence à une catégorie tarifaire
define( '_SEG_ERROR_NO_CAT_PRC', -8 );
/// La valeur saisie ne fait pas référence à un profil / droit d'accès
define( '_SEG_ERROR_NO_PRF', -9 );
/// La valeur saisie ne fait pas référence à un magasin
define( '_SEG_ERROR_NO_STR', -10 );
/// La valeur saisie ne fait pas référence à un compte utilisateur
define( '_SEG_ERROR_NO_USR', -11 );
/// La valeur saisie ne fait pas référence à une newsletter
define( '_SEG_ERROR_NO_NLR', -12 );
/// La valeur saisie ne fait pas référence à un code promotion
define( '_SEG_ERROR_NO_PMT', -13 );
/// La valeur saisie ne fait pas référence à une catégorie comptable
define( '_SEG_ERROR_NO_AC_CAT', -14 );
/// La valeur saisie ne fait pas référence à un type de vente en magasin
define( '_SEG_ERROR_NO_STR_SLTY', -15 );
/// La valeur saisie ne fait pas référence à une marque
define( '_SEG_ERROR_NO_BRD', -16 );
/// La valeur saisie ne fait pas référence à un site web
define( '_SEG_ERROR_NO_WST', -17 );

/// date à partir de laquelle les consultations sont stockées par date. Avant cette date, nous disposons juste d'une information "Nombre de consultations"
define( 'DATE_CONVERSION_HITS', '2013-03-26 16:21:00' );
/// Nombre de jours avant qu'un panier soit considéré comme abandonné
define( 'DAYS_CART_GIVEUP', 31 );


/**	Liste des identifiants de critères du groupe 4 ("Commandes") qui ne gère pas de clause web / non web
 *	@return Liste d'identifiants de critères
 */
function seg_criterions_noweb(){
	return array(59);
}

/**	Cette fonction permet de générer un message d'erreur pour le backoffice en fonction d'un code d'erreur
 *	@param $err_code Numéro du code erreur (voir liste des define en début de fichier)
 *	@param $sec Identifiant du critère
 *	@param $grp Numéro du groupe de conditions
 *
 *	@return Un message d'erreur, ou chaine vide
 */
function seg_err_describe( $err_code, $sec, $grp ){
	$rsecname = seg_criterions_get( $sec );
	$secname = ria_mysql_result( $rsecname, 0, 'name' );

	$message = '';
	switch( $err_code ){
		case _SEG_ERROR_SYMBOL :
			$message = sprintf( _('Le symbole utilisé pour le critère "%s" du groupe n°%d n\'est pas autorisé.'), $secname, $grp );
			break;
		case _SEG_ERROR_INT :
			$message = sprintf( _('La valeur saisie pour le critère "%s" du groupe n°%d n\'est pas un entier.'), $secname, $grp );
			break;
		case _SEG_ERROR_FLOAT :
			$message = sprintf( _('La valeur saisie pour le critère "%s" du groupe n°%d n\'est pas un décimal.'), $secname, $grp );
			break;
		case _SEG_ERROR_BOOL :
			$message = sprintf( _('La valeur saisie pour le critère "%s" du groupe n°%d n\'est reconnue. Veuillez saisir "Oui" ou "Non".'), $secname, $grp );
			break;
		case _SEG_ERROR_DATE :
			$message = sprintf( _('La valeur saisie pour le critère "%s" du groupe n°%d n\'est pas une date au format JJ/MM/AAAA (exemple : 31/12/1972).'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_PRD :
			$message = sprintf( _('Le produit renseigné pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_CAT :
			$message = sprintf( _('La catégorie renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_BRD :
			$message = sprintf( _('La marque renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_CAT_PRC :
			$message = sprintf( _('La catégorie renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_PRF :
			$message = sprintf( _('Le profil client renseigné pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_STR :
			$message = sprintf( _('Le magasin renseigné pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_USR :
			$message = sprintf( _('Le compte renseigné pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_NLR :
			$message = sprintf( _('La newsletter renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_PMT :
			$message = sprintf( _('La promotion renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		case _SEG_ERROR_NO_AC_CAT :
			$message = sprintf( _('La catégorie renseignée pour le critère "%s" du groupe n°%d n\'existe pas.'), $secname, $grp );
			break;
		default :
			$message = _("Une erreur inattendue s'est produite lors de l'enregistrement des critères.<br />Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			break;
	}

	return $message;
}

/**	Cette fonction charge les sources de commande disponibles pour les critères de segment, filtrées selon des paramètres optionnels.
 *	les résultats sont retournés triés par leur position de tri personnalisée croissante
 *	@param int $id Optionnel, identifiant d'une source spécifique (ou tableau)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la source
 *		- code : code de la source
 *		- name : nom de la source
 *		- pos : position de tri de la source
 */
function seg_ord_sources_get( $id=0 ){
	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ) return false;
		}
	}else{
		if( !is_numeric($id) || $id<0 ) return false;
		if( $id ) $id = array($id);
		else $id = array();
	}

	global $config;

	$exclude = '';
	for( $i = 1 ; $i <= 30 ; $i++ ){
		if( !isset($config['ctr_tenant_actived']) || !in_array($i, $config['ctr_tenant_actived']) ){
			$exclude .= ( trim($exclude) != '' ? ', ' : '' ).'"CTR_'.$i.'", "MKT_'.$i.'"';
		}
	}

	$sql = '
		select sos_id as "id", sos_code as "code", sos_name as "name", sos_pos as "pos", ifnull(sos_sog_id, 0) as group_id, ifnull(sog_name, "") as group_name
		from seg_ord_sources
			left join seg_ord_source_groups on ( sos_sog_id = sog_id )
		where 1
	';

	if( sizeof($id) ){
		$sql .= ' and sos_id in ('.implode(', ', $id).')';
	}

	if( trim($exclude) != '' ){
		$sql .= ' and sos_code not in ('.$exclude.')';
	}

	$sql .= ' order by sos_sog_id asc, sos_pos asc';

	return ria_mysql_query($sql);

}

/**	Cette fonction est un alias de seg_ord_sources_get() mais elle retourné un tableau de lignes plutôt que le résultat MySQL.
 * 	Le résultat de cette fonction est mis en cache pour 30 minutes.
 *
 *	@see Voir seg_ord_sources_get()
 */
function seg_ord_sources_get_array( $id=0 ){
	global $config, $memcached;

	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':seg_ord_sources_get_array:'.(is_array($id) ? implode('-', $id) : $id);
	if ($get = $memcached->get($key_memcached)) {
		return ($get === 'false' ? false : ($get == 'none' ? array() : $get));
	}

	$r = seg_ord_sources_get( $id );
	if( !$r ) {
		$memcached->set($key_memcached, 'false', 60 * 30); // Mise en cache pour 30 minutes
		return false;
	}

	$vals = array();

	while( $v = ria_mysql_fetch_array($r) ){
		$vals[] = $v;
	}

	$memcached->set($key_memcached, count($vals) ? $vals : 'none', 60 * 30); // Mise en cache pour 30 minutes
	return $vals;
}

/** Cette fonction retourne un tableau associatif avec les codes des dates dynamiques, elle utile pour généré un selecteur de date dynamique sur la page segment.php
 */
function seg_dyn_date_get(){
	return array(
		'perso' => _('Personnalisée'),
		'today' => _('Aujourd\'hui'),
		'yesterday' => _('Hier'),
		'sweek' => _('Cette semaine (depuis dimanche)'),
		'mweek' => _('Cette semaine (depuis lundi)'),
		'last7' => _('Les 7 derniers jours'),
		'lweekss' => _('Semaine dernière (Dim -Sam)'),
		'lweekms' => _('Semaine dernière (Lun - Dim)'),
		'lweekmf' => _('Semaine dernière (Lun -Ven)'),
		'last14' => _('14 derniers jours'),
		'thismonth' => _('Ce mois-ci'),
		'last30' => _('30 derniers jours'),
		'lmonth' => _('Le mois dernier'),
		'all' => _('Toute la période'),
	);
}
/**	Cette fonction charge les groupes de critères selon des paramètres optionnels.
 *	Les résultats sont triés par position personnalisée croissante
 *	@param int $id Optionnel, identifiant d'un groupe de condition
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du groupe
 *		- name : nom du groupe
 *		- cls_id : identifiant de classe du groupe
 *		- pos : position de tri du groupe
 */
function seg_criterion_groups_get( $id=0, $cls_id=0 ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;

	$sql = '
		select scg_id as "id", scg_name as "name", sgc_cls_id as cls_id, scg_pos as "pos"
		from seg_criterion_groups
		where 1
	';
	if( $id>0 )
		$sql .= ' and scg_id='.$id;
	if( $cls_id>0 )
		$sql .= ' and scg_cls_id='.$cls_id;

	$sql .= ' order by scg_pos asc';

	return ria_mysql_query($sql);

}

/**	Cette fonction est un alias de seg_criterion_groups_get à la différence qu'elle retourne un tableau plutôt qu'un résultat de requête MySQL
 *	@param int $id Voir seg_criterion_groups_get()
 *	@param int $cls_id Voir seg_criterion_groups_get()
 *	@return Voir seg_criterion_groups_get()
 */
function seg_criterion_groups_get_array( $id=0, $cls_id=0 ){
	$r = seg_criterion_groups_get( $id, $cls_id );
	if( !$r ) return false;

	$vals = array();

	while( $v = ria_mysql_fetch_array($r) ){
		$vals[] = $v;
	}

	return $vals;
}

/**	Cette fonction récupère les informations de base d'un critère
 *	@param int $id Optionnel, identifiant du critère
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *	@param $type_id Optionnel, identifiant d'un type de donnée (texte, entier, décimal...). La valeur Null permet de filtrer les critères sur champ avancé
 *	@param $code Optionnel, filtrage par un code de critère (insensible à la casse)
 *	@param $is_yuto Optionnel, mettre à true pour retourne les critère de segmentation spécial Yuto
 *				(ce paramètre est ignorer si $id est renseigné)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du critère
 *		- code : code du critère
 *		- name : nom du critère
 *		- cls_id : classe du critère (fld_classes)
 *		- cls_name : nom de la classe
 *		- type_id : type du critère (fld_type). NULL pour le critère "Champ avancé"
 *		- related_cls : classe du pointeur (si type_id == 11)
 *		- pos : Position de tri du critère
 *		- scg_pos : Position du groupe de critère
 *		- scg_name : Nom du groupe de critère
 *		- use_wst : Détermine si le critère varie selon le site
 */
function seg_criterions_get( $id=0, $cls_id=0, $type_id=0, $code='', $is_yuto=false ){

	if( !is_numeric($id) || $id < 0 ){
		return false;
	}
	if( !is_numeric($cls_id) || $cls_id < 0 ){
		return false;
	}
	if( $type_id !== null && ( !is_numeric($type_id) || $type_id < 0 ) ){
		return false;
	}

	$sql = '
		select
			sec_id as "id", sec_code as "code", sec_name as "name", sec_cls_id as cls_id, cls_name, sec_use_wst as use_wst,
			sec_type_id as type_id, sec_related_cls as related_cls, sec_pos as "pos", scg_pos, scg_name
		from seg_criterions
			join fld_classes on sec_cls_id = cls_id
			left join seg_criterion_groups on sec_cls_id = scg_cls_id and sec_scg_id = scg_id
		where 1
	';

	if( $id ){
		$sql .= ' and sec_id = '.$id;
	}elseif( !$is_yuto ){
		$sql .= ' and sec_scg_id != 11';
	}

	if( $cls_id ){
		$sql .= ' and sec_cls_id = '.$cls_id;
	}

	if( $type_id === null ){
		$sql .= ' and sec_type_id is null';
	}elseif( $type_id ){
		$sql .= ' and sec_type_id = '.$type_id;
	}

	if( trim($code) != '' ){
		$sql .= ' and UPPER(sec_code) = "'.addslashes(strtoupper(trim($code))).'"';
	}

	$sql .= ' order by ifnull(scg_pos, -1) asc, sec_pos asc';

	return ria_mysql_query($sql);

}

/** Cette fonction permet de récupérer tous les critères dans un tableau.
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *	@return bool False en cas d'échec
 *	@return array Un tableau, dont chaque élément est une ligne du résultat de requête obtenue par la fonction seg_criterions_get()
 */
function seg_criterions_get_array( $cls_id=0 ){
	$criterions = array();

	$rcrit = seg_criterions_get( 0, $cls_id, 0, '' );
	if( $rcrit && ria_mysql_num_rows($rcrit) ){
		while( $crit = ria_mysql_fetch_array($rcrit) ){
			if( $cls_id == CLS_USER ){
				// Certains critères sont masqués dans des cas particuliers
				if( !gu_users_admin_rights_used('_MDL_ORDERS') && in_array($crit['scg_name'], array('Commandes', 'Adresse de facturation', 'Adresses de livraison', 'Intentions d\'achats')) ){
					continue;
				}
				if( !gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER') && $crit['scg_name'] == 'Newsletters' ){
					continue;
				}
			}

			$criterions[] = $crit;
		}
	}

	return $criterions;
}

/**	Cette fonction récupère l'information "use_wst" d'un critère donné
 *	@param int $id Identifiant du critère
 *
 *	@return Le booléen "use_wst" du critère, ou False en cas d'échec
 */
function seg_criterions_get_use_wst( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select sec_use_wst from seg_criterions
		where sec_id='.$id.'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}

/**	Cette fonction permet de savoir si un critère peut être déterminé par une période de date
 *	@param int $id Identifiant du critère
 *
 *	@return Le booléen "sec_date_to_date" du critère, ou False en cas d'échec
 */
function seg_criterions_get_use_period( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select sec_date_to_date from seg_criterions
		where sec_id='.$id.'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}

/** Cette fonction permet de savoir si un critère fait référence à des champs avancés.
 *	@param $code Obligatoire, code d'un critère
 *	@return bool True si le critère fait référence à des champs avancés, False dans le cas contraire
 */
function seg_criterions_get_use_fields( $code ){
	if( trim($code) == '' ){
		return false;
	}

	$rcrit = seg_criterions_get( 0, 0, null, $code );

	return $rcrit && ria_mysql_num_rows($rcrit);
}

/** Cette fonction permet de retourner un tableau contenant les symboles acceptés par une condition.
 *
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@param $type_date Facultatif, pour un type de champ libre date, identifiant du type de comparaison de date (voir les constantes dans prices.inc.php)
 *	@param $fld_id Facultatif, identifiant d'un champ libre utilisé comme critère
 *
 *	@return array Un tableau contenant les symboles avec les informations suivantes :
 *					- symbol : symbole
 *					- desc : label du symbole
 */
function seg_criterions_get_symbols( $id, $type_date=CUSTOM_DATE, $fld_id=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	// Récupère le code du critère
	$code = seg_criterions_get_code( $id );
	if( trim($code) == '' ){
		return false;
	}

	if( $fld_id && seg_criterions_get_use_fields($code) ){
		$type = fld_fields_get_type( $fld_id );
		$rsym = prc_symbols_get( $type );

		$ar_symbols = array();
		if( $rsym && ria_mysql_num_rows($rsym) ){
			while( $sym = ria_mysql_fetch_array($rsym) ){
				$ar_symbols[] = array ( 'symbol'=>$sym['symbol'], 'desc'=> _( $sym['desc'] ) );
			}
		}
	}else{
		// récupère le type d'information
		$type = seg_criterions_get_type( $id );
		if( !is_numeric($type) || $type<=0 ){
			return array();
		}

		$ar_symbols = array();

		$rpsy = prc_symbols_get( $type, $type_date, array('><', '%LIKE', 'LIKE%') );
		if( $rpsy && ria_mysql_num_rows($rpsy) ){
			while( $psy = ria_mysql_fetch_array($rpsy) ){
				$ar_symbols[] = $psy;
			}
		}
	}

	return $ar_symbols;
}

/**	Cette fonction récupère l'identifiant d'un critère à partir de son code
 *	@param $code Obligatoire, code du critère
 *
 *	@return int L'identifiant du critère, ou False en cas d'échec
 */
function seg_criterions_get_id( $code ){
	if( !trim($code) ) return false;

	$r = ria_mysql_query('
		select sec_id from seg_criterions
		where sec_code="'.addslashes(strtoupper(trim($code))).'"
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}

/**	Cette fonction récupère le code d'un critère à partir de son identifiant
 *	@param int $id Obligatoire, identifiant du critère
 *
 *	@return Le code du critère, ou False en cas d'échec
 */
function seg_criterions_get_code( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select sec_code from seg_criterions
		where sec_id='.$id.'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction récupère le groupe d'appartenance d'un critère à partir de son identifiant.
 *	@param int $id Obligatoire, identifiant d'un critère
 *
 *	@return Le groupe du critère, ou False en cas d'échec
 */
function seg_criterions_get_group( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select sec_scg_id from seg_criterions
		where sec_id='.$id.'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction permet de récupérer le type de donnée attendu pour un critère de segmentation.
 *	@param int $id Obligatoire, identifiant d'un critère
 *	@return int Le type de donnée attendu.
 *	@return bool False en cas d'échec, ou si le critère est "Valeur d'une champ avancé", dans quel cas le type est celui du champ avancé.
 */
function seg_criterions_get_type( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select sec_type_id as type
		from seg_criterions
		where sec_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type' );
}

/**	Cette fonction crée un nouveau segment
 *	@param string $name Obligatoire, nom du segment
 *	@param int $cls_id Obligatoire, identifiant de la classe du segment
 *	@param string $desc Optionnel, description du segment
 *
 *	@return bool False en cas d'échec
 *	@return int L'identifiant du segment généré en cas de succès
 */
function seg_segments_add( $name, $cls_id, $desc=null ){
	if( !fld_classes_exists( $cls_id ) )return false;
	if( !trim($name) ) return false;

	$desc = !$desc ? 'NULL' : '"'.addslashes($desc).'"';
	$name = addslashes(ucfirst(trim($name)));

	global $config;

	$pos = 1;
	{ // calcul de la position
		$r = ria_mysql_query('
			select max(ifnull(seg_pos, 0)) from seg_segments where seg_tnt_id='.$config['tnt_id'].' and seg_cls_id='.$cls_id
		);

		if( !$r ){
			return false;
		}

		if( ria_mysql_num_rows($r) ){
			$pos = ria_mysql_result($r, 0, 0) + 1;
		}
	}

	$r = ria_mysql_query('
		insert into seg_segments
			(seg_tnt_id, seg_cls_id, seg_name, seg_desc, seg_date_created, seg_pos)
		values
			('.$config['tnt_id'].', '.$cls_id.', "'.$name.'", '.$desc.', now(), '.$pos.')
	');

	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction met à jour les propriétés d'un segment
 *	$name et $desc ne peuvent pas être False en même temps
 *	@param int $id Obligatoire, identifiant du segment
 *	@param string $name Optionnel, nom du segment (False ne change pas la valeur actuelle)
 *	@param string $desc Optionnel, description du sgement (False ne change pas la valeur actuelle, NULL équivaut à un Null SQL)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_segments_upd( $id, $name=false, $desc=false ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( $name!==false && !trim($name) ) return false;
	if( $name===false && $desc===false ) return false;

	global $config;

	$sql = 'update seg_segments set ';

	if( $name!==false && $desc!==false ){
		$sql .= ' seg_name="'.addslashes(ucfirst(trim($name))).'", seg_desc='.( !$desc ? 'NULL' : '"'.addslashes($desc).'"' );
	}elseif( $name!==false ){
		$sql .= ' seg_name="'.addslashes(ucfirst(trim($name))).'"';
	}else{
		$sql .= ' seg_desc='.( !$desc ? 'NULL' : '"'.addslashes($desc).'"' );
	}

	$sql .= ' where seg_id='.$id.' and seg_tnt_id='.$config['tnt_id'];

	return ria_mysql_query($sql);

}

/**	Cette fonction supprime un segment (virtuellement)
 *	@param int $id Identifiant du segment
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_segments_del( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		update seg_segments set seg_date_deleted=now()
		where seg_id='.$id.' and seg_tnt_id='.$config['tnt_id']
	);

	// suppression en cascade
	if( $r ){
		$rsc = seg_segment_criterions_get( 0, $id);
		while( $sc = ria_mysql_fetch_assoc($rsc) ){
			seg_criterions_sources_del( $sc['id'] ); // les origines de commande lié au critères du segment
		}
		seg_segment_criterions_del( 0, $id ); // critères du segment
		seg_objects_del( 0, 0, $id ); // objets restreints par le segment
	}

	return $r;
}

/**	Cette fonction récupère les informations concernant un segment
 *	@param int $id Optionnel, identifiant du segment
 *	@param int $cls_id Optionnel, identifiant de la classe du segment
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du segment
 *		- name : nom du segment
 *		- cls_id : identifiant de la classe du segment
 *		- date_created : date de création du segment
 *		- desc : description du segment
 *		- pos : ordre de priorité du segment
 *		- objects : nombre d'éléments comptabilisés pour le segment
 *		- last_check : date de dernière mise à jour du nombre d'éléments
 */
function seg_segments_get( $id=0, $cls_id=0 ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;

	global $config;

	$sql = '
		select seg_id as "id", seg_name as "name", seg_cls_id as cls_id, seg_date_created as date_created, seg_desc as "desc",
		seg_pos as "pos", seg_objects as "objects", seg_last_check as "last_check"
		from seg_segments
		where seg_tnt_id='.$config['tnt_id'].' and seg_date_deleted is null
	';
	if( $id )
		$sql .= ' and seg_id='.$id;
	if( $cls_id )
		$sql .= ' and seg_cls_id='.$cls_id;

	$sql .= ' order by seg_pos';

	return ria_mysql_query($sql);

}

/**	Ctete fonction permet de précalculer le nombre d'éléments filtrés par le segment. Le résultat de cette fonction
 *  est mis en cache pour 7 heures.
 *
 *	@param int $id identifiant du segment
 *	@param bool $force_expiry force l'expiration du cache, valeur par défaut à false
 *
 *	@return int Le nombre d'éléments respectant les conditions du segment
 *	@return bool False en cas d'échec
 */
function seg_segments_count_objects( $id, $force_expiry = false ){
	global $memcached;
	global $config;

	$memkey = 'segments:countobjects_v2:'.$id;

	if(!$force_expiry && $kid = $memcached->get($memkey)) {
		return $kid;
	} else {
		$rseg = seg_segments_get( $id );
		if( !$rseg || !ria_mysql_num_rows($rseg) ) return false;
		$seg = ria_mysql_fetch_array($rseg);

		$count = false;

		// pour chaque classe segmentable, une fonction spécifique détermine et enregistre le nombre d'éléments
		// ce nombre est ainsi mis en cache, le temps de calcul pouvant être important
		switch( $seg['cls_id'] ){
			case CLS_USER:
				if( $rusers = gu_users_get_by_segment( $id ) ){
					$count = sizeof($rusers);
				}
				break;
			case CLS_STORE:
				if( $rstores = dlv_stores_get_by_segment( $id ) ){
					$count = sizeof($rstores);
				}
				break;
			default:
				error_log( 'Classe non implémentée ('.$seg['cls_id'].') pour le segment '.$id.'.' );
				break;
		}

		// met à jour le nombre d'élements et la date de MAJ dans la base de données
		ria_mysql_query('
			update seg_segments
			set seg_objects = '.( is_numeric($count) ? $count : 0 ).',
				seg_last_check = now()
			where seg_tnt_id = '.$config['tnt_id'].' and seg_id = '.$id.'
		');

		$memcached->set( $memkey,  $count, 7 * 60 * 60 ); // Mise en cache pour 7 heures
		return $count;
	}
}

/**	Cette fonction détermine l'existence d'un segment
 *	@param int $id Obligatoire, identifiant du segment
 *	@param int $cls_id Optionnel, identifiant de classe du segment
 *
 *	@return bool True si le segment existe, False sinon
 */
function seg_segments_exists( $id, $cls_id=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;

	global $config;

	$sql = '
		select 1
		from seg_segments
		where seg_tnt_id='.$config['tnt_id'].' and seg_date_deleted is null and seg_id='.$id.'
	';
	if( $cls_id ){
		$sql .= ' and seg_cls_id='.$cls_id;
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	return ria_mysql_num_rows($r);
}

/** Cette fonction permet de dupliquer un segment.
 *	@param int $seg_id Obligatoire, identifiant de segment
 *	@return int L'identifiant du nouveau segment, False en cas d'erreur
 */
function seg_segments_duplicate( $seg_id ){
	if( !is_numeric($seg_id) || $seg_id <= 0 ){
		return false;
	}

	$rseg = seg_segments_get( $seg_id );
	if( !$rseg || !ria_mysql_num_rows($rseg) ){
		return false;
	}

	$seg_source = ria_mysql_fetch_assoc( $rseg );

	// Récupère les critères du segment source
	$rcriterion = seg_segment_criterions_get( 0, $seg_id );
	if( !$rcriterion ){
		return false;
	}


	global $config;

	// Copie du nom
	$new_name = 'Copie de '.$seg_source['name'];

	$i = 1;
	while( true ){
		$exists = ria_mysql_query('
			select 1
			from seg_segments
			where seg_tnt_id = '.$config['tnt_id'].'
				and seg_name = "'.addslashes( $new_name ).'"
				and seg_date_deleted is null
		');

		if( $exists && ria_mysql_num_rows($exists) ){
			$new_name = 'Copie ('.$i.') de '.$seg_source['name'];
		}else{
			break;
		}

		$i++;
	}

	$new_seg = seg_segments_add( $new_name, $seg_source['cls_id'], $seg_source['desc'] );
	if( !$new_seg ){
		return false;
	}

	while( $c = ria_mysql_fetch_assoc($rcriterion) ){
		if( !is_numeric($c['grp_id']) ){ $c['grp_id'] = 0; }
		if( !is_numeric($c['sos_id']) ){ $c['sos_id'] = 0; }
		if( !is_numeric($c['wst_id']) ){ $c['wst_id'] = 0; }
		if( !is_numeric($c['fld_id']) ){ $c['fld_id'] = 0; }


		$add = seg_segment_criterions_add( $new_seg, $c['sec_id'], $c['grp_id'], $c['symbol'], $c['value'], $c['sos_id'], $c['wst_id'], $c['fld_id'], $c['date_start'], $c['date_end'], $c['date_days'] );
		if( !$add ){
			ria_mysql_query( 'delete from seg_segment_criterions where sgc_tnt_id = '.$config['tnt_id'].' and sgc_seg_id = '.$new_seg );
			ria_mysql_query( 'delete from seg_segments where seg_tnt_id = '.$config['tnt_id'].' and seg_id = '.$new_seg );
			return false;
		}
		$rsos_id = seg_criterions_sources_get($c['sgc_id']);
		if($rsos_id){
			while($row = ria_mysql_fetch_assoc($rsos_id)){
				if(!seg_criterions_sources_add( $add, $row['sos_id'] )){
					ria_mysql_query( 'delete from seg_segment_criterions where sgc_tnt_id = '.$config['tnt_id'].' and sgc_seg_id = '.$add );
					ria_mysql_query( 'delete from seg_segments where seg_tnt_id = '.$config['tnt_id'].' and seg_id = '.$add );
					return false;
				}
			}
		}
	}

	return $new_seg;
}

/**	Cette fonction vérifie que, pour un critère de segment donné, les arguments sont cohérents par rapport à la classe et au type du critère
 *	La cohérence se fait sur les points suivantes :
 *		- Le segment et le critère doivent être de la même classe
 *		- Le symbole doit être cohérent par rapport au type de donnée du critère
 *		- La valeur doit être cohérente par rapport au type de donnée du critère
 *		- Si le type de donnée du critère est 11 (pointeur vers une autre classe), la valeur est contrôlée par rapport aux élément de cette classe (il y un switch empirique)
 *		- Dans le cas où $sec_id = USR_FLD_VALUE, la cohérence est basée sur la valeur de $fld_id
 *
 *	@param int $seg_id Identifiant du segment
 *	@param $sec_id Identifiant du critère
 *	@param $symbol Symbole de comparaison
 *	@param $value Valeur de comparaison. Ce paramètre peut être modifié par la fonction (par exemple, transformation d'une date FR en EN)
 *	@param $fld_id Identifiant du champ avancé si $sec_id = USR_FLD_VALUE, sinon 0
 *
 *	@return bool True si les arguments sont cohérents
 *	@return Un code d'erreur si le triplet symbole / valeur / critère est incohérent (voir liste des define en haut du fichier)
 *	@return bool False pour toute autre erreur
 */
function seg_segment_criterions_check( $seg_id, $sec_id, $symbol, &$value, $fld_id ){
	$rseg = seg_segments_get( $seg_id );
	$rcri = seg_criterions_get( $sec_id );

	if( !$rseg || !ria_mysql_num_rows($rseg) ) return false;
	if( !$rcri || !ria_mysql_num_rows($rcri) ) return false;

	$seg = ria_mysql_fetch_array($rseg);
	$cri = ria_mysql_fetch_array($rcri);

	// incohérence de classe
	if( $seg['cls_id']!=$cri['cls_id'] ) return false;

	// incohérence spécifique champ avancé
	if( !is_numeric($fld_id) || $fld_id < 0 ){
		$fld_id = false;
	}
	if( !seg_criterions_get_use_fields($cri['code']) && $fld_id ){
		return false;
	}elseif( seg_criterions_get_use_fields($cri['code']) && !$fld_id ){
		return false;
	}

	// type_id et related_cls sont surchargés par les propriétés de fld_id
	if( seg_criterions_get_use_fields($cri['code']) ){
		$rfld = fld_fields_get( $fld_id );
		if( !$rfld || !ria_mysql_num_rows($rfld) ){
			return false;
		}
		$cri['type_id'] = ria_mysql_result($rfld, 0, 'type_id');
		$cri['related_cls'] = ria_mysql_result($rfld, 0, 'related_class');
	}

	// incohérence du symbole
	if( trim($symbol)=='><' ) return false; // le symbole "est compris entre" n'est pas autorisé
	if( !prc_symbols_exists( $symbol, $cri['type_id'] ) ){
		return _SEG_ERROR_SYMBOL;
	}

	// incohérence de valeur
	switch( $cri['type_id'] ){
		case FLD_TYPE_TEXT:
		case FLD_TYPE_TEXTAREA:
		case FLD_TYPE_SELECT:
		case FLD_TYPE_SELECT_MULTIPLE:
			// pas de contrôles
			break;
		case FLD_TYPE_INT:
		case FLD_TYPE_SELECT_HIERARCHY:
			// nombre entier
			$value = str_replace( array(',', ' '), array('.', ''), $value );
			if( !is_numeric($value) || floor($value)!=$value ) return _SEG_ERROR_INT;
			break;
		case FLD_TYPE_FLOAT:
			// nombre
			$value = str_replace( array(',', ' '), array('.', ''), $value );
			if( !is_numeric($value) ) return _SEG_ERROR_FLOAT;
			break;
		case FLD_TYPE_BOOLEAN_YES_NO:
			// booléen
			$value = strtolower(trim($value));
			if( $value!='0' && $value!='1' && $value!='oui' && $value!='non' ) return _SEG_ERROR_BOOL;
			$value = $value=='oui' || $value=='1' ? '1' : '0';
			break;
		case FLD_TYPE_DATE:
			// date ou date + heure ou constante (DAY_AND_MONTH_IS_NOW, WEEK_IS_NOW, MONTH_IS_NOW)
			if( !isdate($value) && !isdateheure($value) ){
				if( !in_array($value, array(DAY_AND_MONTH_IS_NOW, WEEK_IS_NOW, MONTH_IS_NOW)) )
					return _SEG_ERROR_DATE;
			}
			if( isdateheure($value) )
				$value = dateheureparse($value);
			elseif( isdate($value) )
				$value = dateparse($value);
			break;
		case FLD_TYPE_REFERENCES_ID:
			// pointeur vers un autre élément RiaShop (switch incomplet)
			switch( $cri['related_cls'] ){
				case CLS_PRODUCT:
					if( !prd_products_exists( $value ) ) return _SEG_ERROR_NO_PRD;
					break;
				case CLS_CATEGORY:
					if( !prd_categories_exists( $value ) ) return _SEG_ERROR_NO_CAT;
					break;
				case CLS_BRAND:
					if( !prd_brands_exists( $value ) ) return _SEG_ERROR_NO_BRD;
					break;
				case CLS_PRICE_CATEGORY:
					if( !prd_prices_categories_exists( $value ) ) return _SEG_ERROR_NO_CAT_PRC;
					break;
				case CLS_PROFIL:
					if( !gu_profiles_exists( $value ) ) return _SEG_ERROR_NO_PRF;
					break;
				case CLS_STORE:
					if( !dlv_stores_exists( $value ) ) return _SEG_ERROR_NO_STR;
					break;
				case CLS_USER:
					if( !gu_users_exists( $value ) ) return _SEG_ERROR_NO_USR;
					break;
				case CLS_NLR_CATEGORY:
					if( !nlr_categories_exists( $value ) ) return _SEG_ERROR_NO_NLR;
					break;
				case CLS_PMT_CODE:
					if( !pmt_codes_exists( $value ) ) return _SEG_ERROR_NO_PMT;
					break;
				case CLS_ACCOUNTING_CATEGORY:
					if( !gu_accouting_categories_exists( $value ) ) return _SEG_ERROR_NO_AC_CAT;
					break;
				case CLS_STR_SALE_TYPE:
					if( !dlv_sales_types_exists( $value ) ){
						return _SEG_ERROR_NO_STR_SLTY;
					}
					break;
				case CLS_WEBSITE:
					if( !wst_websites_exists( $value ) ){
						return _SEG_ERROR_NO_WST;
					}
					break;
				default:
					error_log( __FILE__.' : '.__LINE__.' => Le contrôle sur la classe '.$cri['related_cls'].' n\'est pas implémenté.' );
					return false;
			}
			break;
		default: // types non gérés
			error_log( 'Le type '.$cri['type_id'].' n\'est pas implémenté.' );
			return false;
	}

	return true;
}

/**	Cette fonction ajoute un critère à un segment
 *	Voir le commentaire de la fonction seg_segment_criterions_check() pour la cohérence des arguments
 *	@param int $seg_id Identifiant du segment
 *	@param $sec_id Identifiant du critère
 *	@param $grp_id Numéro de groupe (permet de former les blocs de conditions OU)
 *	@param $symbol Symbole de comparaison (note : le symbole "est compris entre" n'est pas autorisé)
 *	@param $value Valeur de comparaison
 *	@param $sos_id Optionnel, identifiant d'une source pour le canal de commande (si le critère est de type commande. Dans les autres cas il estignoré)
 *	@param int $wst_id Optionnel, identifiant d'un site en particulier pour lequel le critère s'applique (dépend de sec_use_wst)
 *	@param $fld_id Optionnel, identifiant d'un champ avancé, si $sec_id = USR_FLD_VALUE
 *	@param string $date_start Optionnel, date de début de prise en compte du critère (si le critère est bornable)
 *	@param string $date_end Optionnel, date de fin de prise en compte du critère (si le critère est bornable)
 *	@param string $date_days Optionnel, période dynamique en nombre de jours
 *
 *	@return Identifiant généré en cas de succès, False (ou code d'erreur) en cas d'échec
 */
function seg_segment_criterions_add( $seg_id, $sec_id, $grp_id, $symbol, $value, $sos_id=0, $wst_id=0, $fld_id=0, $date_start=false, $date_end=false, $date_days=false ){

	if( !is_numeric($grp_id) || $grp_id<0 ) return false;
	if( !is_numeric($sos_id) || $sos_id<0 ) return false;
	if( !is_numeric($wst_id) || $wst_id<0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	// contrôle de la cohérence
	$check_r = seg_segment_criterions_check( $seg_id, $sec_id, $symbol, $value, $fld_id );
	if( !$check_r || ( is_numeric($check_r) && $check_r<0 ) ) return $check_r;

	if( isdateheure($date_start) ){
		$date_start = '"'.dateheureparse($date_start).'"';
	}else{
		$date_start = 'NULL';
	}

	if( isdateheure($date_end) ){
		$date_end = '"'.dateheureparse($date_end).'"';
	}else{
		$date_end = 'NULL';
	}

	global $config;

	$r = ria_mysql_query('
		insert into seg_segment_criterions
			(sgc_tnt_id, sgc_seg_id, sgc_sec_id, sgc_grp_id, sgc_symbol, sgc_value, sgc_sos_id, sgc_wst_id, sgc_fld_id, sgc_date_start, sgc_date_end, sgc_days)
		values
			('.$config['tnt_id'].', '.$seg_id.', '.$sec_id.', '.$grp_id.', "'.addslashes(trim($symbol)).'", "'.addslashes($value).'", '.( !$sos_id ? 'NULL' : $sos_id ).', '.( !$wst_id ? 'NULL' : $wst_id ).', '.( !$fld_id ? 'NULL' : $fld_id ).', '.$date_start.', '.$date_end.', '.( !$date_days ? 'NULL' : '"'.trim($date_days).'"' ).')
	');

	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction supprime un (ou des) critère(s) de segment
 *	$sgc_id et $seg_id sont mutuellement obligatoires
 *	@param $sgc_id Optionnel, identifiant du couple critère/segment
 *	@param int $seg_id Optionnel, identifiant du segment
 *	@param $sec_id Optionnel, identifiant du critère
 *	@param $grp_id Optionnel, numéro de groupe (attention, NULL diffère de 0)
 *	@param $sos_id Optionnel, identifiant de source de commande
 *	@param int $wst_id Optionnel, identifiant d'un site
 *	@param $fld_id Optionnel, identifiant d'un champ avancé
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_segment_criterions_del( $sgc_id=0, $seg_id=0, $sec_id=0, $grp_id=null, $sos_id=0, $wst_id=0, $fld_id=0 ){
	if( !is_numeric($sgc_id) || $sgc_id<0 ) return false;
	if( !is_numeric($seg_id) || $seg_id<0 ) return false;
	if( !is_numeric($sec_id) || $sec_id<0 ) return false;
	if( !is_numeric($sos_id) || $sos_id<0 ) return false;
	if( !is_numeric($wst_id) || $wst_id<0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;
	if( $grp_id!==null && ( !is_numeric($grp_id) || $grp_id<0 ) ) return false;

	if( !$sgc_id && !$seg_id ) return false;

	global $config;

	$sql = '
		delete from seg_segment_criterions
		where
			sgc_tnt_id='.$config['tnt_id'].'
	';
	if( $sgc_id ){
		$sql .= ' and sgc_id='.$sgc_id;
	}
	if( $seg_id ){
		$sql .= ' and sgc_seg_id='.$seg_id;
	}
	if( $sec_id ){
		$sql .= ' and sgc_sec_id='.$sec_id;
	}
	if( $grp_id!==null ){
		$sql .= ' and sgc_grp_id='.$grp_id;
	}
	if( $sos_id ){
		$sql .= ' and sgc_sos_id='.$sos_id;
	}
	if( $wst_id ){
		$sql .= ' and sgc_wst_id='.$wst_id;
	}
	if( $fld_id ){
		$sql .= ' and sgc_fld_id='.$fld_id;
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère les critères d'un segment donné
 *	$sgc_id et $seg_id sont mutuellement obligatoires
 *	@param $sgc_id Optionnel, identifiant du couple critère / segment
 *	@param int $seg_id Optionnel, identifiant du segment
 *	@param $grp_id Optionnel, identifiant d'un groupe de critères (condition OU) (attention, il y a une différence entre NULL et 0)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MYSQL comprenant les colonnes suivantes :
 *		- sgc_id : numéro de couple critère / segment
 *		- sec_id : identifiant du critère
 *		- sec_code : code du critère
 *		- sec_name : nom du critère
 *		- seg_id : identifiant du segment
 *		- grp_id : numéro de groupe
 *		- symbol : symbole de comparaison
 *		- value : valeur de comparaison
 *		- cls_id : identifiant de la classe du segment et du critère
 *		- type_id : identifiant du type de donnée du critère (NULL si critère "Valeur d'un champ avancé")
 *		- related_cls : classe sur lequel pointe le critère quand il est de type 11 (pointeur)
 *		- sos_id : identifiant du canal de commande
 *		- sos_code : code du canal de commande
 *		- sos_name : nom du canal de commande
 *		- wst_id : site sur lequel le critère s'applique
 *		- fld_id : identifiant du champ avancé servant de critère (nullable)
 *		- fld_type_id : type de fld_id (nullable)
 *		- fld_name : nom du champ avancé référencé par fld_id (nullable)
 *		- date_start : date de début de prise en compte du critère
 *		- date_end : date de fin de prise en compte du critère
 *		- date_days : période dynamique en nombre de jours
 */
function seg_segment_criterions_get( $sgc_id=0, $seg_id=0, $grp_id=null ){
	if( !is_numeric($sgc_id) || $sgc_id<0 ) return false;
	if( !is_numeric($seg_id) || $seg_id<0 ) return false;
	if( $grp_id!==null && ( !is_numeric($grp_id) || $grp_id<0 ) ) return false;

	if( !$sgc_id && !$seg_id ) return false;

	global $config;

	$sql = '
		select
			sec_id, seg_id, sgc_grp_id as grp_id, sgc_symbol as "symbol", sgc_value as "value", sgc_id, sos_id, sos_code,
			sos_name, sec_cls_id as cls_id, sec_related_cls as related_cls, sec_type_id as type_id, sec_code, sec_name,
			sgc_wst_id as wst_id, fld_id, fld_type_id, fld_name, sgc_date_start as date_start, sgc_date_end as date_end, sgc_days as date_days
		from
			seg_segment_criterions
			join seg_segments on sgc_tnt_id=seg_tnt_id and sgc_seg_id=seg_id
			join seg_criterions on sgc_sec_id=sec_id
			left join seg_ord_sources on sgc_sos_id=sos_id
			left join fld_fields on sgc_fld_id = fld_id and sgc_tnt_id = if(fld_tnt_id=0, sgc_tnt_id, fld_tnt_id)
		where
			sgc_tnt_id='.$config['tnt_id'].' and
			seg_date_deleted is null and
			fld_date_deleted is null
	';
	if( $sgc_id )
		$sql .= ' and sgc_id='.$sgc_id;
	if( $seg_id )
		$sql .= ' and sgc_seg_id='.$seg_id;
	if( $grp_id!==null )
		$sql .= ' and sgc_grp_id='.$grp_id;

	$sql .= '
		order by sgc_grp_id, sgc_seg_id, sgc_id
	';

	return ria_mysql_query($sql);

}

/* Cette fonction permet de copier un groupe de critère pour un segment.
 *	@param int $seg_id Obligatoire, identifiant d'un segment
 *	@param $grp_id Obligatoire, identiifnat d'un groupe
 *	@return bool True si la copie s'est correctement déroulée, False dans le cas contraire.
 */
function seg_segment_criterions_copy_group( $seg_id, $grp_id ){
	if( !is_numeric($seg_id) || $seg_id<=0 ) return false;
	if( !is_numeric($grp_id) || $grp_id<0 ) return false;
	global $config;

	// déplace tous les groupes vers le bas
	$sql = '
		update seg_segment_criterions
		set sgc_grp_id = sgc_grp_id + 1
		where sgc_tnt_id='.$config['tnt_id'].'
			and sgc_seg_id='.$seg_id.'
			and sgc_grp_id>'.$grp_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !ria_mysql_query($sql) ){
		return false;
	}

	// récupère les critères en place dans le groupe
	$rsc = seg_segment_criterions_get( 0, $seg_id, $grp_id );
	if( !$rsc || !ria_mysql_num_rows($rsc) ){
		return false;
	}

	$next_grp = $grp_id + 1;
	while( $sc = ria_mysql_fetch_array($rsc) ){

		$res = seg_segment_criterions_add(
			$seg_id, $sc['sec_id'], $next_grp, $sc['symbol'], $sc['value'],
			is_numeric($sc['sos_id']) ? $sc['sos_id'] : 0,
			is_numeric($sc['wst_id']) ? $sc['wst_id'] : 0,
			is_numeric($sc['fld_id']) ? $sc['fld_id'] : 0,
			isdateheure($sc['date_start']) ? $sc['date_start'] : false,
			isdateheure($sc['date_end']) ? $sc['date_end'] : false,
			$sc['date_days'] !== '' ? $sc['date_days'] : false
		);
		if( $res===false ){
			return false;
		}
		$rsos_id = seg_criterions_sources_get($sc['sgc_id']);
		if($rsos_id){
			while($row = ria_mysql_fetch_assoc($rsos_id)){
				if(!seg_criterions_sources_add( $res, $row['sos_id'] )){
					return false;
				}
			}
		}

	}

	return true;
}

/**	Cette fonction récupère les groupes de conditions OU pour un segment donné
 *	@param int $seg_id Identifiant du segment
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau des identifiants de groupes
 */
function seg_segment_criterions_get_groups( $seg_id ){
	if( !is_numeric($seg_id) || $seg_id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select distinct sgc_grp_id as id
		from seg_segment_criterions
		where sgc_seg_id='.$seg_id.' and sgc_tnt_id='.$config['tnt_id'].'
	');

	if( !$r ){
		return false;
	}

	$groups = array();
	while( $g = ria_mysql_fetch_array($r) ){
		$groups[] = $g['id'];
	}

	return $groups;
}

/**	Cette fonction crée une segmentation sur un objet donné (bannière, actualité, gestion de contenu, etc...)
 *	Si la segmentation existe déjà, la fonction retournera True sans lever d'erreurs
 *	@param int $cls_id Obligatoire, identifiant de la classe d'objet
 *	@param int $obj_id Obligatoire, identifiant de l'objet (tableau pour les clés multiples)
 *	@param int $seg_id Obligatoire, identifiant du segment
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_objects_add( $cls_id, $obj_id, $seg_id ){
	if( !fld_classes_exists( $cls_id ) ) return false;
	if( !seg_segments_exists( $seg_id ) ) return false;
	if( is_array($obj_id) ){
		if( !sizeof($obj_id) || sizeof($obj_id)>3 ) return false;
		foreach( $obj_id as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
		for( $i=sizeof($obj_id); $i<3; $i++ ){
			$obj_id[ $i ] = 0;
		}
	}else{
		if( !is_numeric($obj_id) || $obj_id<=0 ) return false;
		$obj_id = array($obj_id, 0, 0);
	}
	if( seg_objects_exists( $cls_id, $obj_id, $seg_id ) ) return true;

	global $config;

	$sql = '
		insert into seg_objects
			(sgo_tnt_id, sgo_seg_id, sgo_cls_id, sgo_obj_id_0, sgo_obj_id_1, sgo_obj_id_2)
		values
			('.$config['tnt_id'].', '.$seg_id.', '.$cls_id.', '.$obj_id[0].', '.$obj_id[1].', '.$obj_id[2].')
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction supprime une segmentation sur un objet donné
 *	Il est possible de supprimer toutes les références à un segment ou toutes les segmentations d'un objet
 *	@param int $cls_id Optionnel, identifiant de la classe d'objet
 *	@param int $obj_id Optionnel, identifiant de l'objet (tableau pour les clés multiples)
 *	@param int $seg_id Optionnel, identifiant du segment
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_objects_del( $cls_id=0, $obj_id=0, $seg_id=0 ){
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;
	if( !is_numeric($seg_id) || $seg_id<0 ) return false;

	if( is_array($obj_id) ){
		if( !sizeof($obj_id) || sizeof($obj_id)>3 ) return false;
		foreach( $obj_id as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
		for( $i=sizeof($obj_id); $i<3; $i++ ){
			$obj_id[ $i ] = 0;
		}
	}elseif( is_numeric($obj_id) ){
		if( $obj_id<0 ) return false;
		elseif( $obj_id>0 ) $obj_id = array($obj_id, 0, 0);
	}else{
		return false;
	}

	if( is_array($obj_id) && !$cls_id ) return false;
	if( !is_array($obj_id) && !$seg_id ) return false;

	global $config;

	$sql = 'delete from seg_objects where sgo_tnt_id='.$config['tnt_id'];
	if( $seg_id )
		$sql .= ' and sgo_seg_id='.$seg_id;
	if( is_array($obj_id) ){
		$sql .= ' and sgo_cls_id='.$cls_id;
		$sql .= ' and sgo_obj_id_0='.$obj_id[0];
		$sql .= ' and sgo_obj_id_1='.$obj_id[1];
		$sql .= ' and sgo_obj_id_2='.$obj_id[2];
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction vérifie l'existence d'un segment particulier sur un objet donné
 *	@param int $cls_id Identifiant de la classe d'objet
 *	@param int $obj_id Identifiant de l'objet (tableau pour les clés multiples)
 *	@param int $seg_id Identifiant du segment
 *
 *	@return bool True si l'objet est segmenté, False sinon
 */
function seg_objects_exists( $cls_id, $obj_id, $seg_id ){
	if( !fld_classes_exists( $cls_id ) ) return false;
	if( !seg_segments_exists( $seg_id ) ) return false;
	if( is_array($obj_id) ){
		if( !sizeof($obj_id) || sizeof($obj_id)>3 ) return false;
		foreach( $obj_id as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
		for( $i=sizeof($obj_id); $i<3; $i++ ){
			$obj_id[ $i ] = 0;
		}
	}else{
		if( !is_numeric($obj_id) || $obj_id<=0 ) return false;
		$obj_id = array($obj_id, 0, 0);
	}

	global $config;

	$sql = '
		select 1 from seg_objects
		where sgo_tnt_id='.$config['tnt_id'].' and sgo_seg_id='.$seg_id.' and sgo_cls_id='.$cls_id.'
		and sgo_obj_id_0='.$obj_id[0].' and sgo_obj_id_1='.$obj_id[1].' and sgo_obj_id_2='.$obj_id[2].'
	';

	$r = ria_mysql_query($sql);
	if( !$r ) return false;

	return ria_mysql_num_rows($r);
}

/**	Cette fonction récupère la segmentation d'un objet donné
 *	@param int $cls_id Identifiant de la classe d'objet
 *	@param int $obj_id Identifiant de l'objet (tableau pour les clés multiples)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant du segment
 *		- name : Nom du segment
 */
function seg_objects_get_segments( $cls_id, $obj_id ){
	if( !fld_classes_exists( $cls_id ) ) return false;
	if( is_array($obj_id) ){
		if( !sizeof($obj_id) || sizeof($obj_id)>3 ) return false;
		foreach( $obj_id as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
		for( $i=sizeof($obj_id); $i<3; $i++ ){
			$obj_id[ $i ] = 0;
		}
	}else{
		if( !is_numeric($obj_id) || $obj_id<=0 ) return false;
		$obj_id = array($obj_id, 0, 0);
	}

	global $config;

	$sql = '
		select sgo_seg_id as id , seg_name as name
		from seg_objects
			join seg_segments on (sgo_tnt_id=seg_tnt_id and sgo_seg_id=seg_id)
		where sgo_tnt_id='.$config['tnt_id'].'
			and sgo_cls_id='.$cls_id.'
			and sgo_obj_id_0='.$obj_id[0].'
			and sgo_obj_id_1='.$obj_id[1].'
			and sgo_obj_id_2='.$obj_id[2].'
			and seg_date_deleted is null
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction est chargée de vérifier si la segmentation d'un objet cible s'applique dans un contexte source donné.
 *	Le résultat de cette fonction est mis en cache pendant 30 minutes.
 *
 *	@param int $cls_dst_id Obligatoire, identifiant de la classe de l'objet cible
 *	@param int $obj_dst_id Obligatoire, identifiant de l'objet cible (ou tableau d'identifiants s'il s'agit d'une clé composée)
 *	@param int $cls_src_id Obligatoire, identifiant de la classe de l'objet source (seules les classes CLS_USER et CLS_STORE sont actuellement autorisées)
 *	@param int $obj_src_id Obligatoire, identifiant de l'objet source (les tableaux ne sont pas encore autorisés)
 *	@param bool $control_connect Optionnel, permet de forcer le contrôle de la connexion
 *
 *	@return bool True si la segmentation de la cible inclue la source, ou si la cible n'est pas segmentée
 *	@return bool False en cas d'erreur, ou si la segmentation de la cible n'inclue pas la source
 */
function seg_objects_check_segment( $cls_dst_id, $obj_dst_id, $cls_src_id, $obj_src_id, $control_connect=false ){ // , $false_if_no_seg=false

	global $memcached;
	global $config;

	// construction du paramètre memcached (tableau)
	$ar_key_val = $obj_dst_id;
	if( is_array($obj_dst_id) ){
		$ar_key_val = '';
		for( $i = 0; $i < sizeof($obj_dst_id); $i++ ){
			$ar_key_val .= $obj_dst_id[ $i ].'|';
		}
	}

	$key_memcached = 'seg_objects_check_segment:tenant:'.$config['tnt_id'].':wst_id:'.$config['wst_id'].':cls_dst_id:'.$cls_dst_id.':obj_dst_id:'.$ar_key_val.':cls_src_id:'.$cls_src_id.':obj_src_id:'.$obj_src_id.':control_connect:'.($control_connect ? '1' : '0');
	$time_mem = 30 * 60; // 30 minutes

	$res = $memcached->get($key_memcached);

	if( ria_is_memcached_result_ok($memcached) && $res !== false ){
		return $res;
	}

	if( !fld_classes_exists( $cls_dst_id ) ){
		$memcached->set($key_memcached, false, $time_mem);
		return false;
	}

	if( is_array($obj_dst_id) ){

		$count_odst = sizeof($obj_dst_id);

		if( !$count_odst || $count_odst > COUNT_OBJ_ID ){
			$memcached->set($key_memcached, false, $time_mem);
			return false;
		}

		foreach( $obj_dst_id as $o ){
			if( !is_numeric($o) || $o < 0 ){
				$memcached->set($key_memcached, false, $time_mem);
				return false;
			}
		}

		for( $i = $count_odst; $i < COUNT_OBJ_ID; $i++ ){
			$obj_dst_id[ $i ] = 0;
		}

	}else{

		if( !is_numeric($obj_dst_id) || $obj_dst_id <= 0 ){
			$memcached->set($key_memcached, false, $time_mem);
			return false;
		}

		$tmp = array($obj_dst_id);
		for( $i = 1; $i < COUNT_OBJ_ID; $i++ ){
			$tmp[] = 0;
		}
		$obj_dst_id = $tmp;

	}

	if( !fld_classes_exists( $cls_src_id ) ){
		$memcached->set($key_memcached, false, $time_mem);
		return false;
	}

	if( !is_numeric($obj_src_id) ){
		$memcached->set($key_memcached, false, $time_mem);
		return false;
	}

	$ar_cls = fld_classes_get_segmentable_array();
	if( !is_array($ar_cls) || !sizeof($ar_cls) || !in_array($cls_src_id, $ar_cls) ){
		error_log('seg_objects_check_segment() - Classe source '.$cls_src_id.' non segmentable.');
		$memcached->set($key_memcached, false, $time_mem);
		return false;
	}

	$rsegs = seg_objects_get_segments( $cls_dst_id, $obj_dst_id );
	if( !$rsegs ){
		$memcached->set($key_memcached, false, $time_mem);
		return false;
	}

	if( !ria_mysql_num_rows($rsegs) ){
		// l'objet cible n'est pas segmenté
		$memcached->set($key_memcached, true, $time_mem);
		return true;
	}

	if ($cls_src_id == CLS_USER) {
		if( isset($config['seg_usr_parent_holder']) && $config['seg_usr_parent_holder'] ){
			// controle si le compte courant à un parent, dans ce cas la on récupére le parent
			$parent_id = gu_users_get_parent_id($obj_src_id);
			$obj_src_id = $parent_id > 0 ? $parent_id : $obj_src_id;
		}
	}

	while( $s = ria_mysql_fetch_assoc($rsegs) ){
		switch( $cls_src_id ){
			case CLS_USER:
				if (!$control_connect || $obj_src_id) {
					if( gu_users_get_by_segment( $s['id'], $obj_src_id ) ){
						$memcached->set($key_memcached, true, $time_mem);
						return true;
					}
				}
				break;
			case CLS_STORE:
				if( dlv_stores_get_by_segment( $s['id'], $obj_src_id ) ){
					$memcached->set($key_memcached, true, $time_mem);
					return true;
				}
				break;
			default:
				error_log('seg_objects_check_segment() - classe '.$cls_src_id.' non implémentée.');
				break;
		}
	}

	$memcached->set($key_memcached, false, $time_mem);

	return false;

}

/**	Cette fonction est un alias de seg_segments_get_sql spécifique aux segments sur les comptes utilisateurs
 *	@param int $seg_id Identifiant de segment
 *	@return array Voir seg_segments_get_sql
 */
function seg_segments_get_sql_user( $seg_id ){
	return seg_segments_get_sql( $seg_id, CLS_USER );
}

/**	Cette fonction est un alias de seg_segments_get_sql spécifique aux segments sur les magasins
 *	@param int $seg_id Identifiant de segment
 *	@return array Voir seg_segments_get_sql
 */
function seg_segments_get_sql_store( $seg_id ){
	return seg_segments_get_sql( $seg_id, CLS_STORE );
}

/**	Cette fonction génère les conditions SQL qui permettent de filtrer les éléments en fonction d'un segment. Cette fonction est destinée à un usage interne uniquement.
 *	A l'heure actuelle, seules les classes CLS_USER et CLS_STORE sont gérées.
 *	@param int $seg_id Identifiant du segment
 *	@param int $cls Classe attendue du segment
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau dont chaque élément est un groupe OU de conditions SQL
 *		L'exploitation des résultats doit être faite dans une requête incluant les tables appropriées (gu_users et gu_adresses pour CLS_USER, dlv_stores pour CLS_STORE), de la manière suivante :
 *		$sql .= ' and ( ('.implode(') and (', $resultat_de_la_fonction).') )';
 */
function seg_segments_get_sql( $seg_id, $cls ){
	$ar_cls = fld_classes_get_segmentable_array();
	if( !is_array($ar_cls) || !sizeof($ar_cls) ){
		return false;
	}

	if( !is_numeric($cls) || !in_array($cls, $ar_cls) ){
		return false;
	}
	if( !seg_segments_exists( $seg_id, $cls ) ){
		return false;
	}

	$passed_st = ord_states_get_ord_valid();
	$cancel_st = ord_states_get_canceled( true );
	$giveup_st = array_diff(ord_states_get_canceled(), $cancel_st);
	$basket_st = ord_states_get_uncompleted();
	$valid_st = ord_states_get_ord_valid( true );

	$groups = seg_segment_criterions_get_groups( $seg_id );
	if( !is_array($groups) ){
		return false;
	}

	global $config;

	$conditions = array();
	foreach( $groups as $grp ){
		$rcrit = seg_segment_criterions_get( 0, $seg_id, $grp );
		if( !$rcrit ){
			return false;
		}
		$group_conditions = array();
		while( $crit = ria_mysql_fetch_array($rcrit) ){

			$is_like = in_array(strtoupper($crit['symbol']), array('LIKE', 'NOT LIKE'));
			$is_empty = $crit['symbol']=="=''" || $crit['symbol']=="!=''";
			$not = trim($crit['symbol']) == '=' ? '' : 'not';
			$and_or = trim($crit['symbol']) == '=' ? 'or' : 'and';
			$yesv = strtolower(trim($crit['value']))=='oui' || $crit['value']==1;

			$rsos_id = seg_criterions_sources_get( $crit['sgc_id'] );
			$sos_id = array();
			while( $row = ria_mysql_fetch_assoc($rsos_id) ){
				$sos_id[] = $row['sos_id'];
			}

			$origin = array();

			if( is_array($sos_id) && count($sos_id) ){
				$origin = seg_ord_sources_get_array( $sos_id );
			}

			$grp_cnd = '';

			switch( $crit['sec_code'] ){
				// caractéristiques (clients)
				case 'USR_ID': {
					$grp_cnd = 'usr_id '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_IS_SYNC': {
					$grp_cnd = 'usr_is_sync = '.( $yesv ? '1' : '0' );
					break;
				}
				case 'USR_GENDER': {
					$grp_cnd = 'case when adr_title_id=1 then "homme" when adr_title_id=2 then "femme" when adr_title_id=3 then "femme" else "" end '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' lower("%'.addslashes(trim($crit['value'])).'%")';
					else
						$grp_cnd .= ' lower("'.addslashes(trim($crit['value'])).'")';
					break;
				}
				case 'USR_PROFIL': {
					$grp_cnd = 'usr_prf_id '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_CREATED_FROM': {
					$grp_cnd = '(
						ifnull(usr_wst_id, '.$config['wst_id'].') '.$crit['symbol'].' '.$crit['value'].' and
						usr_email NOT LIKE "%@riastudio.fr" and
						usr_email NOT LIKE "%@kontinuum.fr" and
						usr_email NOT LIKE "%@yuto.com"
					)';
					break;
				}
				case 'USR_PRICE_CATEGORY': {
					$grp_cnd = 'usr_prc_id '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_ACCOUNTING_CATEGORY': {
					$grp_cnd = 'ifnull(usr_cac_id, '.$config['default_cac_id'].') '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_CAN_LOGIN': {
					$grp_cnd = 'ifnull(usr_can_login, 0) = '.( $yesv ? '1' : '0' );
					break;
				}
				case 'USR_SELLER': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from gu_users as u2
						where u2.usr_tnt_id='.$config['tnt_id'].' and u2.usr_prf_id='.PRF_SELLER.' and u2.usr_date_deleted is null
						and u2.usr_id='.$crit['value'].'
						and u2.usr_seller_id = u.usr_seller_id
					)';
					break;
				}
				case 'USR_DATE_CREATION': {
					if( isdate($crit['value']) ){
						$grp_cnd = 'usr_date_created '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month(usr_date_created) '.$crit['symbol'].' month(now()) and
									dayofmonth(usr_date_created) '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week(usr_date_created) '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month(usr_date_created) '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				case 'USR_DATE_LOGIN': {
					if( isdate($crit['value']) ){
						$grp_cnd = 'ifnull(usr_last_login, "1969-12-31 23:59:59") '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month(ifnull(usr_last_login, "1969-12-31 23:59:59")) '.$crit['symbol'].' month(now()) and
									dayofmonth(ifnull(usr_last_login, "1969-12-31 23:59:59")) '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week(ifnull(usr_last_login, "1969-12-31 23:59:59")) '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month(ifnull(usr_last_login, "1969-12-31 23:59:59")) '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				case 'USR_DATE_OF_BIRTH': {
					if( isdate($crit['value']) ){
						$grp_cnd = 'ifnull(usr_dob, "1900-01-01 00:00:00") '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month(ifnull(usr_dob, "1900-01-01 00:00:00")) '.$crit['symbol'].' month(now()) and
									dayofmonth(ifnull(usr_dob, "1900-01-01 00:00:00")) '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week(ifnull(usr_dob, "1900-01-01 00:00:00")) '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month(ifnull(usr_dob, "1900-01-01 00:00:00")) '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				case 'USR_REWARDS': {
					// Calcul la sommes totale des points de fidélités pour les clients
					$grp_cnd = '(
						select sum(stats_points) from stats_rewards
						where stats_tnt_id=usr_tnt_id and stats_usr_id=usr_id and stats_date_deleted is null
					';

					if( $crit['wst_id'] )
						$grp_cnd .= ' and stats_wst_id='.$crit['wst_id'];
					$grp_cnd .= ') '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_SUBSCRIT_REWARDS': {
					$grp_cnd = ' '.( $yesv ? '' : 'not' ).' exists (
						select 1
						from rwd_users
						where rwu_tnt_id = '.$config['tnt_id'].'
							and rwu_usr_id = usr_id
							and rwu_date_inscript is not null
							and rwu_date_inscript <= now()
							and rwu_date_uninscript is null)';
					break;
				}
				case 'USR_SUBSCRIT_PARTNERS': {
					$grp_cnd = 'usr_accept_partners = '.( $yesv ? '1' : '0' );
					break;
				}
				case 'USR_EMAIL': {
					$grp_cnd = 'upper(usr_email) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				// adresse de facturation (clients)
				case 'USR_INV_CITY': {
					$grp_cnd = 'upper(adr_city) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'USR_INV_ZIPCODE': {
					$grp_cnd = 'upper(adr_postal_code) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'USR_INV_DEPARTMENT': {
					$grp_cnd = 'substring(lpad(adr_postal_code, 5, "0"), 1, 2) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' "%'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'%"';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' "'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'"';
					break;
				}
				case 'USR_INV_COUNTRY': { // Pays de facturation
					$grp_cnd = 'upper(adr_country) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'USR_INV_SOCIETY': { // Société de facturation
					$grp_cnd = 'upper(adr_society) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'USR_INV_SIRET': { // Siret de facturation
					$grp_cnd = 'upper(adr_siret) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'USR_INV_STREET': { // No et rue de facturation
					$grp_cnd = 'upper(adr_address1) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				// adresses de livraison (clients)
				case 'USR_DLV_CITY': { // Ville de livraison
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and upper(a2.adr_city) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_DLV_STREET': { // No et rue de livraison
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and upper(a2.adr_address1) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_DLV_ZIPCODE': { // Code postal de livraison
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and upper(a2.adr_postal_code) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_DLV_DEPARTMENT': { // Département de livraison
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and substring(lpad(a2.adr_postal_code, 5, "0"), 1, 2) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' "%'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'%"';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' "'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'"';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_DLV_COUNTRY': { // Pays de livraison
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and upper(a2.adr_country) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_DLV_COMPANY': { // Société
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						join ord_orders on a2.adr_tnt_id=ord_tnt_id and a2.adr_usr_id=ord_usr_id and a2.adr_id=ord_adr_delivery
						where a2.adr_tnt_id='.$config['tnt_id'].'
						and ord_state_id in ('.implode(', ', $valid_st).')
					';
					$grp_cnd .= 'and upper(a2.adr_society) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;

				}
				case 'USR_DLV_STORE': { // Livraison dans un magasin spécifique
					$grp_cnd = ' '.$not.' exists (
						select 1 from ord_orders
						where ord_tnt_id='.$config['tnt_id'].' and ord_state_id in ('.implode(', ', $valid_st).')
						and ord_str_id='.$crit['value'].'
						and ord_usr_id = usr_id
					)';
					break;
				}
				// Adresse de compte Yuto
				case 'USR_ADR_COUNTRY': {
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						where a2.adr_tnt_id='.$config['tnt_id'].'
					';
					$grp_cnd .= 'and upper(a2.adr_country) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_ADR_CITY': {
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						where a2.adr_tnt_id='.$config['tnt_id'].'
					';
					$grp_cnd .= 'and upper(a2.adr_city) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_ADR_DEPARTMENT': {
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						where a2.adr_tnt_id='.$config['tnt_id'].'
					';
					$grp_cnd .= 'and substring(lpad(a2.adr_postal_code, 5, "0"), 1, 2) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' "%'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'%"';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' "'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'"';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				case 'USR_ADR_ZIPCODE': {
					$grp_cnd = ' exists (
						select 1 from gu_adresses as a2
						where a2.adr_tnt_id='.$config['tnt_id'].'
					';
					$grp_cnd .= 'and upper(a2.adr_postal_code) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					$grp_cnd .= '
						and a2.adr_usr_id = usr_id
					)';
					break;
				}
				// commandes (clients)
				case 'USR_ORDERS_COUNT': {
					$grp_cnd = '(
						select count(*) from ord_orders
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id
						and ord_parent_id is null
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date( $crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_ORDERS_CANCEL_COUNT': {
					$grp_cnd = '(
						select count(*) from ord_orders
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id
						and ord_parent_id is null
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $cancel_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_ORDERS_AMOUNT': {
					$grp_cnd = '(
						select sum(ord_total_ttc) from ord_orders
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_usr_id=usr_id and ord_tnt_id=usr_tnt_id
						and ord_parent_id is null
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $valid_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_ORDERS_AMOUNT_HT': {
					$grp_cnd = '(
						select sum(ord_total_ht) from ord_orders
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_usr_id=usr_id and ord_tnt_id=usr_tnt_id
						and ord_parent_id is null
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins($origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $valid_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_ORDERS_WEB_RATIO': {
					$grp_cnd = '(
						(
						select sum(ord_total_ht) from ord_orders
						where ord_usr_id = usr_id and ord_tnt_id = usr_tnt_id
						and ord_parent_id is null
						and ord_state_id in ('.implode(', ', $valid_st).')
						and ord_pay_id is not null
					';
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and ord_wst_id = '.$crit['wst_id'];
					}
					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						) / (
							select sum(ord_total_ht) from ord_orders
							where ord_usr_id = usr_id and ord_tnt_id = usr_tnt_id
							and ord_parent_id is null
							and ord_state_id in ('.implode(', ', $valid_st).')
					';
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and ord_wst_id = '.$crit['wst_id'];
					}

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						) * 100
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'USR_DATE_ORDER': {
					$datestring = 'ifnull((
						select max(ord_date)
						from ord_orders
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id=usr_tnt_id
							and ord_usr_id=usr_id
					';

					$datestring .= seg_criterions_get_sql_where_origins( $origin );

					$date_null = '1900-01-01 00:00:00';
					if (in_array($crit['symbol'], array('<', '<='))) {
						$date_null = '9999-01-01 00:00:00';
					}

					if( $crit['wst_id'] )
						$datestring .= ' and ord_wst_id='.$crit['wst_id'];
					$datestring .= ' and ord_state_id in ('.implode(', ', $passed_st).')), "'.$date_null.'")';

					if( isdate($crit['value']) ){
						$grp_cnd = $datestring.' '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month('.$datestring.') '.$crit['symbol'].' month(now()) and
									dayofmonth('.$datestring.') '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week('.$datestring.') '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month('.$datestring.') '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				case 'USR_PRD_ORDERED': {
					$grp_cnd .= ' '.$not.' exists (
						select 1 from ord_orders
						join ord_products as my_op on ord_id=prd_ord_id and ord_tnt_id=my_op.prd_tnt_id
						left join prd_hierarchy as h on my_op.prd_tnt_id=h.prd_tnt_id and prd_id=prd_child_id
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id='.$config['tnt_id'].'
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
						and ( prd_id='.$crit['value'].'  or h.prd_parent_id='.$crit['value'].' )
						and ord_usr_id = usr_id
					)';
					break;
				}
				case 'USR_CAT_ORDERED': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from ord_orders
						join ord_products on ord_id=prd_ord_id and ord_tnt_id=prd_tnt_id
						join prd_classify on prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id
						'.seg_criterions_get_sql_join_origins( $origin ).'
						left join prd_cat_hierarchy on cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id
						where ord_tnt_id='.$config['tnt_id'].'
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
						and ( cly_cat_id='.$crit['value'].' or cat_parent_id='.$crit['value'].' )
						and ord_usr_id = usr_id
					)';
					break;
				}
				case 'USR_BRD_ORDERED': {
					$grp_cnd .= ' '.$not.' exists (
						select 1 from ord_orders
						join ord_products as my_op on ord_id=prd_ord_id and ord_tnt_id=my_op.prd_tnt_id
						join prd_products as rp on my_op.prd_tnt_id=rp.prd_tnt_id and my_op.prd_id=rp.prd_id
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id='.$config['tnt_id'].'
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
						and rp.prd_brd_id='.$crit['value'].'
						and ord_usr_id = usr_id
					)';
					break;
				}
				case 'USR_USE_CODE_PROMOTION': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from ord_orders
						join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)
						'.seg_criterions_get_sql_join_origins( $origin ).'
						where ord_tnt_id='.$config['tnt_id'].'
					';

					$grp_cnd .= seg_criterions_get_sql_where_origins( $origin );

					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];
					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
						and (ord_pmt_id='.$crit['value'].' or oop_pmt_id='.$crit['value'].')
						and ord_usr_id = usr_id
					)';
					break;
				}
				// newsletter (clients)
				case 'USR_SUBSCRIT_NEWSLETTER': {
					$grp_cnd = ' '.( $yesv ? '' : 'not' ).' exists (
						select 1 from nlr_subscribers
						where sub_tnt_id='.$config['tnt_id'].'
						and sub_date_deleted is null and sub_inscript_confirmed is not null
						and sub_uninscript_confirmed is null
						and sub_email = usr_email
					)';
					break;
				}
				case 'USR_SUBSCRIT_NEWSLETTER_SPEC': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from nlr_subscribers
						where sub_tnt_id='.$config['tnt_id'].'
						and sub_date_deleted is null and sub_inscript_confirmed is not null
						and sub_uninscript_confirmed is null
						and sub_cat_id = '.$crit['value'].'
						and sub_email = usr_email
					)';
					break;
				}
				case 'USR_SUBSCRIT_NEWSLETTER_DATE': {
					$news_cat = isset($config['newsletter_cat']) && is_numeric($config['newsletter_cat']) ? $config['newsletter_cat'] : 1;
					$grp_cnd = ' (
							select sub_inscript_confirmed from nlr_subscribers
							where sub_tnt_id='.$config['tnt_id'].' and sub_date_deleted is null
							and sub_uninscript_confirmed is null and sub_email = usr_email
							and sub_inscript_confirmed is not null and sub_cat_id='.$news_cat.'
							limit 0, 1
						) '.$crit['symbol'].' "'.$crit['value'].'"
					';
					break;
				}
				// consultations (clients)
				case 'USR_PRD_CONSULTED': {
					$grp_cnd = '(
						'.$not.' exists (
							select 1 from stats_prd_products_users
							left join prd_hierarchy on stats_tnt_id=prd_tnt_id and stats_prd_id=prd_child_id
							where ( stats_prd_id='.$crit['value'].' or prd_parent_id='.$crit['value'].' )
							and stats_tnt_id='.$config['tnt_id'].'
							'.( $crit['wst_id'] ? 'stats_wst_id='.$crit['wst_id'] : '' ).'
							';

							$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "stats_date");

						$grp_cnd.='
							and stats_usr_id = usr_id
						) '.$and_or.' '.$not.' exists (
							select 1 from stats_prd_products_users_histo
							left join prd_hierarchy on stats_tnt_id=prd_tnt_id and stats_prd_id=prd_child_id
							where ( stats_prd_id='.$crit['value'].' or prd_parent_id='.$crit['value'].' )
							and stats_tnt_id='.$config['tnt_id'].'
							'.( $crit['wst_id'] ? 'stats_wst_id='.$crit['wst_id'] : '' ).'
							';

							$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, DATE_CONVERSION_HITS);

						$grp_cnd.='
							and stats_usr_id = usr_id
						)
					)';
					break;
				}
				case 'USR_CAT_CONSULTED': {
					$grp_cnd = '(
						'.$not.' exists (
							select 1 from stats_prd_categories_users
							left join prd_cat_hierarchy on stats_tnt_id=cat_tnt_id and stats_cat_id=cat_child_id
							where stats_tnt_id='.$config['tnt_id'].'
							'.( $crit['wst_id'] ? 'stats_wst_id='.$crit['wst_id'] : '' ).'
							';

							$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "stats_date");

						$grp_cnd.='
							and ( stats_cat_id='.$crit['value'].' or cat_parent_id='.$crit['value'].' )
							and stats_usr_id = usr_id
						) '.$and_or.' '.$not.' exists (
							select 1 from stats_prd_categories_users_histo
							left join prd_cat_hierarchy on stats_tnt_id=cat_tnt_id and stats_cat_id=cat_child_id
							where stats_tnt_id='.$config['tnt_id'].'
							'.( $crit['wst_id'] ? 'stats_wst_id='.$crit['wst_id'] : '' ).'
							and ( stats_cat_id='.$crit['value'].' or cat_parent_id='.$crit['value'].' )
							';

							$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, DATE_CONVERSION_HITS);

						$grp_cnd.='
							and stats_usr_id = usr_id
						)
					)';
					break;
				}
				case 'USR_PRD_CANCELLED': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from ord_orders
						join ord_products_intentions as my_op on ord_id=prd_ord_id and ord_tnt_id=my_op.prd_tnt_id
						left join prd_hierarchy as h on my_op.prd_tnt_id=h.prd_tnt_id and prd_id=prd_child_id
						where ord_tnt_id='.$config['tnt_id'].' and ( prd_id='.$crit['value'].' or h.prd_parent_id='.$crit['value'].' )
					';
					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "prd_date_created");

					$grp_cnd .= '
						and (
							ord_state_id in ('.implode(', ', $giveup_st).') or (
								ord_state_id in ('.implode(', ', $basket_st).')
								and datediff( now(), prd_date_created )>'.DAYS_CART_GIVEUP.'
							)
						)
						and ord_usr_id = usr_id
					)';
					break;
				}
				case 'USR_CAT_CANCELLED': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from ord_orders
						join ord_products_intentions on ord_id=prd_ord_id and ord_tnt_id=prd_tnt_id
						join prd_classify on prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id
						left join prd_cat_hierarchy on cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id
						where ord_tnt_id='.$config['tnt_id'].' and ( cly_cat_id='.$crit['value'].' or cat_parent_id='.$crit['value'].' )
					';
					if( $crit['wst_id'] )
						$grp_cnd .= ' and ord_wst_id='.$crit['wst_id'];

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "prd_date_created");

					$grp_cnd .= '
						and (
							ord_state_id in ('.implode(', ', $giveup_st).') or (
								ord_state_id in ('.implode(', ', $basket_st).')
								and datediff(now(), prd_date_created)>'.DAYS_CART_GIVEUP.'
							)
						)
						and ord_usr_id = usr_id
					)';
					break;
				}
				// caractéristiques (magasins)
				case 'STR_ID': {
					$grp_cnd = 'str_id '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'STR_IS_SYNC': {
					$grp_cnd = 'str_is_sync = '.( $yesv ? '1' : '0' );
					break;
				}
				case 'STR_NAME': {
					$grp_cnd = 'upper(str_name) '.$crit['symbol'];
					if( $is_like ){
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					}elseif( $is_empty ){
						;
					}else{
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					}
					break;
				}
				case 'STR_ALLOW_DLV': {
					$grp_cnd = 'str_allow_delivery = '.( $yesv ? '1' : '0' );
					break;
				}
				case 'STR_DATE_CREATION': {
					error_log(__FILE__.':'.__LINE__.' - le critère "STR_DATE_CREATION" n\'est pas implémenté.');
					break;
				}
				case 'STR_SALE_TYPE': {
					$grp_cnd = ' '.$not.' exists (
						select 1 from dlv_store_sales_types
						where slc_tnt_id = '.$config['tnt_id'].' and slc_type_id = '.$crit['value'].'
						and slc_str_id = str_id
					)';
					break;
				}
				// localisation (magasins)
				case 'STR_CITY': {
					$grp_cnd = 'upper(str_city) '.$crit['symbol'];
					if( $is_like ){
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					}elseif( $is_empty ){
						;
					}else{
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					}
					break;
				}
				case 'STR_ZIPCODE': {
					$grp_cnd = 'upper(str_zipcode) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				case 'STR_DEPARTMENT': {
					$grp_cnd = 'substring(lpad(str_zipcode, 5, "0"), 1, 2) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' "%'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'%"';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' "'.addslashes(str_pad(trim($crit['value']), 2, '0', STR_PAD_LEFT)).'"';
					break;
				}
				case 'STR_COUNTRY': {
					$grp_cnd = 'upper(str_country) '.$crit['symbol'];
					if( $is_like )
						$grp_cnd .= ' upper("%'.addslashes($crit['value']).'%")';
					elseif( $is_empty )
						;
					else
						$grp_cnd .= ' upper("'.addslashes($crit['value']).'")';
					break;
				}
				// commandes (magasins)
				case 'STR_ORDERS_COUNT': {
					$grp_cnd = '(
						select count(*) from ord_orders
						where ord_tnt_id = str_tnt_id and ord_str_id = str_id
					';
					if( $crit['sos_code'] == 'WEB' ){
						$grp_cnd .= ' and ord_pay_id is not null';
					}elseif( $crit['sos_code'] == 'NO_WEB' ){
						$grp_cnd .= ' and ord_pay_id is null';
					}
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and ord_wst_id = '.$crit['wst_id'];
					}

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $passed_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'STR_ORDERS_AMOUNT_HT': {
					$grp_cnd = '(
						select sum(ord_total_ht) from ord_orders
						where ord_str_id = str_id and ord_tnt_id = str_tnt_id
					';
					if( $crit['sos_code'] == 'WEB' ){
						$grp_cnd .= ' and ord_pay_id is not null ';
					}elseif( $crit['sos_code'] == 'NO_WEB' ){
						$grp_cnd .= ' and ord_pay_id is null ';
					}
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and ord_wst_id = '.$crit['wst_id'];
					}

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $valid_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'STR_ORDERS_AMOUNT_TTC': {
					$grp_cnd = '(
						select sum(ord_total_ttc) from ord_orders
						where ord_str_id = str_id and ord_tnt_id = str_tnt_id
					';
					if( $crit['sos_code'] == 'WEB' ){
						$grp_cnd .= ' and ord_pay_id is not null ';
					}elseif( $crit['sos_code'] == 'NO_WEB' ){
						$grp_cnd .= ' and ord_pay_id is null ';
					}
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and ord_wst_id = '.$crit['wst_id'];
					}

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "ord_date");

					$grp_cnd .= '
						and ord_state_id in ('.implode(', ', $valid_st).')
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'STR_DATE_ORDER': {
					$datestring = 'ifnull((select max(ord_date) from ord_orders where ord_tnt_id = str_tnt_id and ord_str_id = str_id';
					foreach( $origin as $value ){
						if( $value['code']=='WEB' ){
							$datestring .= ' and ord_pay_id is not null ';
							break;
						}elseif( $value['code']=='NO_WEB' ){
							$datestring .= ' and ord_pay_id is null ';
							break;
						}
					}
					if( $crit['wst_id'] ){
						$datestring .= ' and ord_wst_id = '.$crit['wst_id'];
					}
					$datestring .= ' and ord_state_id in ('.implode(', ', $passed_st).')), "1900-01-01 00:00:00")';

					if( isdate($crit['value']) ){
						$grp_cnd = $datestring.' '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month('.$datestring.') '.$crit['symbol'].' month(now()) and
									dayofmonth('.$datestring.') '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week('.$datestring.') '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month('.$datestring.') '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				// contacts (magasins)
				case 'STR_CONTACTS_COUNT': {
					$grp_cnd = '(
						select count(*) from gu_messages
						where cnt_tnt_id = str_tnt_id and cnt_str_id = str_id
					';
					if( $crit['wst_id'] ){
						$grp_cnd .= ' and cnt_wst_id = '.$crit['wst_id'];
					}

					$grp_cnd .= seg_criterions_get_grp_cnd_date($crit, "cnt_date_created");

					$grp_cnd .= '
						and cnt_date_delete is null and cnt_state != -2
					) '.$crit['symbol'].' '.$crit['value'];
					break;
				}
				case 'STR_DATE_CONTACT': {
					$datestring = 'ifnull((select max(cnt_date_created) from gu_messages where cnt_tnt_id = str_tnt_id and cnt_str_id = str_id';
					if( $crit['wst_id'] ){
						$datestring .= ' and cnt_wst_id = '.$crit['wst_id'];
					}
					$datestring .= ' and cnt_date_delete is null and cnt_state != -2), "1900-01-01 00:00:00")';

					if( isdate($crit['value']) ){
						$grp_cnd = $datestring.' '.$crit['symbol'].' "'.$crit['value'].'"';
					}else{
						switch( $crit['value'] ){
							case DAY_AND_MONTH_IS_NOW:
								$grp_cnd = '(
									month('.$datestring.') '.$crit['symbol'].' month(now()) and
									dayofmonth('.$datestring.') '.$crit['symbol'].' dayofmonth(now())
								)';
								break;
							case WEEK_IS_NOW:
								$grp_cnd = 'week('.$datestring.') '.$crit['symbol'].' week(now())';
								break;
							case MONTH_IS_NOW:
								$grp_cnd = 'month('.$datestring.') '.$crit['symbol'].' month(now())';
								break;
						}
					}
					break;
				}
				// valeur d'un champ avancé (fonctionnement commun entre les classes)
				case 'STR_FLD_VALUE':
				case 'USR_FLD_VALUE': {

					// le nom de la colonne de jointure est fonction du critère
					$col_id = 'usr_id';
					if( $crit['sec_code'] == 'STR_FLD_VALUE' ){
						$col_id = 'str_id';
					}

					// sous-requête sur fld_object_values
					$base_sql_fld = '(
						select pv_value from fld_object_values
						where pv_tnt_id = '.$config['tnt_id'].' and pv_fld_id = '.$crit['fld_id'].'
						and pv_obj_id_0 = '.$col_id.' and pv_lng_code = "'.addslashes($config['i18n_lng']).'"
					)';

					switch( $crit['fld_type_id'] ){
						case FLD_TYPE_TEXT:
						case FLD_TYPE_TEXTAREA:
						case FLD_TYPE_SELECT:
						case FLD_TYPE_SELECT_MULTIPLE:
							// insensible à la casse
							if( $is_like ){
								$grp_cnd = 'upper(ifnull('.$base_sql_fld.', "")) '.$crit['symbol'].' upper("%'.addslashes($crit['value']).'%")';
							}elseif( $is_empty ){
								$grp_cnd = 'upper(ifnull('.$base_sql_fld.', "")) '.$crit['symbol'];
							}else{
								$grp_cnd = 'upper(ifnull('.$base_sql_fld.', "")) '.$crit['symbol'].' upper("'.addslashes($crit['value']).'")';
							}
							break;
						case FLD_TYPE_INT:
							// signé
							$grp_cnd = 'cast(ifnull('.$base_sql_fld.', "0") as signed) '.$crit['symbol'].' '.$crit['value'];
							break;
						case FLD_TYPE_REFERENCES_ID:
						case FLD_TYPE_SELECT_HIERARCHY:
							// non signé
							$grp_cnd = 'cast(ifnull('.$base_sql_fld.', "0") as unsigned) '.$crit['symbol'].' '.$crit['value'];
							break;
						case FLD_TYPE_FLOAT:
							// même précision après la virgule
							$grp_cnd = 'cast(ifnull('.$base_sql_fld.', "0") as decimal(12, 6)) '.$crit['symbol'].' cast('.$crit['value'].' as decimal(12, 6))';
							break;
						case FLD_TYPE_BOOLEAN_YES_NO:
							// conversion des oui / non en 1 / 0
							$grp_cnd = 'if(ifnull('.$base_sql_fld.', "Non") in ("Non", "0"), "0", "1") = '.( $yesv ? '1' : '0' );
							break;
						case FLD_TYPE_DATE:
							$castdt = 'cast(ifnull('.$base_sql_fld.', "1000-01-01 00:00:00") as datetime)';
							if( isdateheure($crit['value']) ){
								// comparaison sans la partie horaire
								$grp_cnd = 'date('.$castdt.') '.$crit['symbol'].' date("'.dateheureparse($crit['value']).'")';
							}else{
								switch( $crit['value'] ){
									case DAY_AND_MONTH_IS_NOW:
										$grp_cnd = 'month('.$castdt.') '.$crit['symbol'].' month(now()) and dayofmonth('.$castdt.') '.$crit['symbol'].' dayofmonth(now())';
										break;
									case WEEK_IS_NOW:
										$grp_cnd = 'week('.$castdt.') '.$crit['symbol'].' week(now())';
										break;
									case MONTH_IS_NOW:
										$grp_cnd = 'month('.$castdt.') '.$crit['symbol'].' month(now())';
										break;
								}
							}
							break;
						default:
							error_log( __FILE__.':'.__LINE__.' le type de champ '.$crit['fld_type_id'].' n\'est pas implémenté pour le segment '.$seg_id.', critère : '.$crit['sgc_id'].'.' );
							return false;
					}
					break;
				}
			}
			if( trim($grp_cnd) ){
				$group_conditions[] = $grp_cnd;
			}
		}
		if( sizeof($group_conditions) ){
			$conditions[] = '('.implode(') or (', $group_conditions).')';
		}
	}

	return $conditions;
}

/**	Cette fonction récupère la liste des couples ID / Name des objets relatifs à un critère donné (on suppose que le critère est de type 11 "pointeur").
 *
 *	@param $code Obligatoire, code du critère
 *	@param $fld_id Facultatif, identifiant d'un champ libre utilisé comme critère
 *
 *	@return array Un tableau de tableau des objets relatifs à la classe du critère donné. Chaque élément du tableau est composé de la manière suivante :
 *		- id : Identifiant de l'objet (parfois alphanumérique, pour le sexe par exemple)
 *		- name : Nom de l'objet
 */
function seg_criterions_get_objects_array( $code, $fld_id=0 ){
	$ar_data = array();

	$code = strtoupper(trim($code));

	if( seg_criterions_get_use_fields($code) && (!is_numeric($fld_id) || $fld_id<=0) ){
		return false;
	}
	switch( $code ){
		case 'USR_DLV_STORE' :
		case 'STR_ID': {
			require_once('delivery.inc.php');
			if( $rstr = dlv_stores_get( 0, null, array('name'=>'asc'), 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ) ){
				while( $str = ria_mysql_fetch_array($rstr) ){
					$ar_data[] = array( 'id' => $str['id'], 'name' => $str['name'].' ('.$str['zipcode'].')' );
				}
			}
			break;
		}
		case 'STR_SALE_TYPE': {
			require_once('delivery.inc.php');
			if( $rtype = dlv_sales_types_get( 0, 0, null ) ){
				while( $type = ria_mysql_fetch_array($rtype) ){
					$ar_data[] = array( 'id' => $type['id'], 'name' => $type['name'] );
				}
			}
			break;
		}
		case 'USR_GENDER' : {
			$ar_data[] = array( 'id' => 'homme', 'name' => 'Homme' );
			$ar_data[] = array( 'id' => 'femme', 'name' => 'Femme' );
			break;
		}
		case 'USR_INV_COUNTRY' :
		case 'USR_DLV_COUNTRY' :
		case 'STR_COUNTRY' : {
			require_once('sys.countries.inc.php');
			if( $rcountry = sys_countries_get() ){
				while( $country = ria_mysql_fetch_array($rcountry) ){
					$ar_data[] = array( 'id' => $country['name'], 'name' => $country['name'] );
				}
			}
			break;
		}
		case 'USR_PRICE_CATEGORY' : {
			require_once('gu.categories.inc.php');
			if( $rcat = prd_prices_categories_get() ){
				while( $cat = ria_mysql_fetch_array($rcat) ){
					$ar_data[] = array( 'id' => $cat['id'], 'name' => $cat['name'] );
				}
			}
			break;
		}
		case 'USR_PROFIL' : {
			require_once('profiles.inc.php');
			if( $rprf = gu_profiles_get() ){
				while( $prf = ria_mysql_fetch_array($rprf) ){
					$ar_data[] = array( 'id' => $prf['id'], 'name' => $prf['name'] );
				}
			}
			break;
		}
		case 'USR_SUBSCRIT_NEWSLETTER_SPEC' : {
			require_once('newsletter.inc.php');
			if( $rnlr = nlr_categorie_get() ){
				while( $nlr = ria_mysql_fetch_array($rnlr) ){
					$ar_data[] = array( 'id' => $nlr['id'], 'name' => $nlr['cat'] );
				}
			}
			break;
		}
		case 'USR_ACCOUNTING_CATEGORY' : {
			require_once('gu.categories.inc.php');
			if( $rprc = gu_accounting_categories_get() ){
				while( $prc = ria_mysql_fetch_array($rprc) ){
					$ar_data[] = array( 'id' => $prc['id'], 'name' => $prc['name'] );
				}
			}
			break;
		}
		case 'USR_INV_DEPARTMENT' :
		case 'USR_DLV_DEPARTMENT' :
		case 'STR_DEPARTMENT' : {
			require_once('sys.zones.inc.php');
			if( $rdpt = sys_zones_get( 0, '', '', false, 0, '', _ZONE_DPT_FRANCE, array('code'=>'asc'), -1, -1, true, true ) ){
				while( $dpt = ria_mysql_fetch_array($rdpt) ){
					$ar_data[] = array( 'id' => $dpt['code'], 'name' => $dpt['code'].' - '.$dpt['name'] );
				}
			}
			break;
		}
		case 'USR_FLD_VALUE' :
		case 'STR_FLD_VALUE' : {
			$type = fld_fields_get_type( $fld_id );
			if( $type==FLD_TYPE_SELECT_HIERARCHY ){
				$rval = fld_restricted_values_get( 0, $fld_id );
				while( $r = ria_mysql_fetch_array($rval) ){
					$name_val = $r['name'];
					$id = $r['id'];
					while( $vals2 = fld_restricted_values_get( $r['parent'] ) ){
						if( $r = ria_mysql_fetch_array($vals2) ){
							$name_val = $r['name'].' >> '.$name_val;
						}
					}

					$ar_data[] = array( 'id' => $id, 'name' => $name_val );
					$tmp = array_msort( $ar_data, array( 'name'=>SORT_ASC ) );

					$ar_data = array();
					foreach( $tmp as $t ){
						$ar_data[] = $t;
					}
				}

			}else{
				$rval = fld_restricted_values_get( 0, $fld_id );
				if( $rval && ria_mysql_num_rows($rval) ){
					while( $val = ria_mysql_fetch_array($rval) ){
						$ar_data[] = array( 'id'=>$val['name'], 'name'=>$val['name'] );
					}
				}
			}
			break;
		}
		case 'USR_BRD_ORDERED': {
			require_once('brands.inc.php');
			if( $rbrd = prd_brands_get() ){
				while( $brd = ria_mysql_fetch_assoc($rbrd) ){
					$ar_data[] = array( 'id' => $brd['id'], 'name' => $brd['title'] );
				}
			}
			break;
		}
		case 'USR_CREATED_FROM': {
			require_once('websites.inc.php');
			if( $rwst = wst_websites_get() ){
				while( $wst = ria_mysql_fetch_assoc($rwst) ){
					$ar_data[] = array( 'id' => $wst['id'], 'name' => $wst['name'] );
				}
			}
			break;
		}
	}

	return $ar_data;
}
/** Cette fonction interne va retourner le code SQL permettant de filtrer une requête principale sur une période
 *	@param $crit Résultat de la fonction seg_criterions_get()
 *	@param $column Nom de la colonne sur laquel effectuer la condition
 *	@return une portion de code SQL à insérer dans la clause WHERE
 */
function seg_criterions_get_grp_cnd_date( $crit, $column ){
	$group_condition = '';
	if( !is_array($crit) || !sizeof($crit) ){
		return $group_condition;
	}
	if( !is_string(trim($column)) || trim($column) === '' ){
		return $group_condition;
	}
	global $config;

	$tenant = ria_mysql_fetch_assoc(tnt_tenants_get($config['tnt_id']));

	$date_start = $date_end = false;

	$today = date('Y-m-d');
	$yesterday = date('Y-m-d', strtotime('-1 day'));
	$last7 = date('Y-m-d', strtotime('-7 day'));
	$last_monday = date('Y-m-d', strtotime('last Monday'));
	$last_sunday = date('Y-m-d', strtotime('last Sunday'));
	$last14 = date( 'Y-m-d', strtotime('-14 day') );
	$last30 = date( 'Y-m-d', strtotime('-30 day') );
	$start_month = date( 'Y-m-d', strtotime('first day of this month') );
	$end_month = date( 'Y-m-d', strtotime('last day of this month') );
	$start_last_month = date( 'Y-m-d', strtotime('first day of previous month') );
	$end_last_month = date( 'Y-m-d', strtotime('last day of previous month') );
	$tenant_creation = date( 'Y-m-d', strtotime($tenant['date-created']) );
	$saturday_lweek = date('Y-m-d', strtotime('last Saturday'));
	$monday_lweek = date('Y-m-d', strtotime('-2 Monday'));
	$friday_lweek = date('Y-m-d', strtotime('-2 Friday'));

	switch ($crit['date_days']) {
		case 'perso':
			if( isdateheure($crit['date_start']) ) $date_start = dateheureparse($crit['date_start']);
			if( isdateheure($crit['date_end']) ) $date_end = dateheureparse($crit['date_end']);
			break;
		case 'today':
			$date_start = $today;
			break;
		case 'yesterday':
			$date_start = $yesterday;
			$date_end = $today;
			break;
		case 'last7':
			$date_start = $last7;
			$date_end = $today;
			break;
		case 'mweek':
			$date_start = $last_monday;
			break;
		case 'sweek':
			$date_start = $last_sunday;
			break;
		case 'last14':
			$date_start = $last14;
			$date_end = $today;
			break;
		case 'last30':
			$date_start = $last30;
			$date_end = $today;
			break;
		case 'thismonth':
			$date_start = $start_month;
			$date_end = $end_month;
			break;
		case 'lmonth':
			$date_start = $start_last_month;
			$date_end = $end_last_month;
			break;
		case 'all':
			$date_start = $tenant_creation;
			break;
		case 'lweekss':
			$date_start = $saturday_lweek;
			$date_end = $last_sunday;
			break;
		case 'lweekms':
			$date_start = $monday_lweek;
			$date_end = $last_sunday;
			break;
		case 'lweekmf':
			$date_start = $monday_lweek;
			$date_end = $friday_lweek;
			break;
	}
	if( $date_start ){
		$group_condition .= ' and '.(isdateheure($column) ? '"'.$column.'"' : $column).' >= "'.$date_start.'"';
	}
	if( $date_end ){
		$group_condition .= ' and '.(isdateheure($column) ? '"'.$column.'"' : $column).' < "'.$date_end.'"';
	}

	return $group_condition;
}

/** Cette fonction interne va retourner le code SQL permettant de filtrer une requête principale sur des origines de commande
 *	@param array $origin tableau contenant les origines correspondant à une commande
 *	@return string une portion de code SQL à insérer dans la clause FROM
 */
function seg_criterions_get_sql_join_origins( $origin=array() ){
	if( !is_array($origin) ){
		return '';
	}

	foreach( $origin as $value ){
		if(!in_array( $value['code'], array('ALL', 'WEB', 'NO_WEB')) ){
			return ' left join stats_origins on ( stats_tnt_id = ord_tnt_id and stats_obj_id_0 = ord_id )';
		}
	}

	return '';
}

/** Cette fonction interne va retourner le code SQL permettant de filtrer une requête principale sur plusieurs origines de commande
 *	@param array $origin Obligatoire, tableau contenant les origines correspondant à une commande
 *	@return string une portion de code SQL à insérer dans la clause WHERE
 */
function seg_criterions_get_sql_where_origins( $origin=array() ){
	if( !is_array($origin) ){
		return '';
	}

	$ar_origins = array();

	$sql = '';
	foreach( $origin as $value ){
		if( $value['code']=='ALL'){
			return $sql;
		}
		switch( $value['code'] ){
			case 'WEB':
				$ar_origins[] = 'ord_pay_id is not null';
				break;
			case 'NO_WEB':
				$ar_origins[] = 'ord_pay_id is null';
				break;
			case 'DIRECT':
				$ar_origins[] = 'stats_source="(direct)"';
				break;
			case 'REFERAL':
				$ar_origins[] = '( stats_name = "(referral)" and stats_medium = "referral" )';
				break;
			case 'NATURAL':
				$ar_origins[] = 'stats_medium = "organic"';
				break;
			case 'ADWORDS':
				$ar_origins[] = '( stats_source = "google" and stats_medium = "cpc" )';
				break;
			case 'NEWSLETTER':
				$ar_alerts = array( 'alert', 'alerte', 'alerts', 'alertes', 'newsletter', 'newsletters', 'mailjet' );
				$ar_origins[] = '( lower(stats_source) in ("'.implode( '", "', $ar_alerts ).'"))';
				break;
			case 'RECOMMENDATION':
				$ar_origins[] = 'stats_medium = "site"';
				break;
			default: {
				$source = false;

				if( strstr( $value['code'], 'CTR_') ){
					$ctr = str_replace( 'CTR_', '',  $value['code'] );

					if( is_numeric($ctr) && $ctr > 0 ){
						$source = ctr_comparators_get_source( $ctr );
					}
				}elseif( strstr( $value['code'], 'MKT_') ){
					$ctr = str_replace( 'MKT_', '',  $value['code'] );

					if( is_numeric($ctr) && $ctr > 0 ){
						$source = ctr_comparators_get_source( $ctr );
					}
				}

				if( $source !== false && trim($source) != '' ){
					$ar_origins[] = 'stats_source="'.addslashes($source).'"';
				}

				break;
			}
		}
	}

	if( sizeof($ar_origins) ){
		$sql = 'and( '.implode( ' or ', $ar_origins ).' )';
	}

	return $sql;
}

/**	Cette fonction permet de vérifier l'existance d'une condition de segment
 *	@param $sgc_id Identifiant d'une condition de segment
 *	@return bool True si existant, false en cas d'échec
 */
function seg_segment_criterions_exists( $sgc_id ){
	global $config;

	if( $sgc_id <= 0 || !is_numeric($sgc_id) ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from seg_segment_criterions
		where sgc_tnt_id='.$config['tnt_id'].'
			and sgc_id ='.$sgc_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
/**	Cette fonction permet de vérifier l'existance d'une origine de commande
 *	@param $sos_id Obligatoire, identifiant d'une origine de commande
 *	@return bool True si existant, false en cas d'échec
 */
function seg_ord_sources_exists( $sos_id ){
	global $config;

	if( $sos_id <= 0 || !is_numeric($sos_id) ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from seg_ord_sources
		where sos_id ='.$sos_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
/** Cette fonction permet d'ajouter des sources pour un dondition de segment
 * @param $sgc_id Identifiant d'une condition de segment pour lequel on veut ajouter une source
 * @param $sos_id Identifiant d'une origine de commande
 * @return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function seg_criterions_sources_add( $sgc_id, $sos_id ){
	global $config;

	if( !seg_segment_criterions_exists($sgc_id) ){
		return false;
	}
	if( !seg_ord_sources_exists($sos_id) ){
		return false;
	}

	return ria_mysql_query('
		insert into seg_criterions_sources
			(scs_tnt_id, scs_sgc_id, scs_sos_id)
		values
			('.$config['tnt_id'].', '.$sgc_id.', '.$sos_id.')
	');

}
/** Cette fonction permet de vérifier si pour un identifiant d'une condition d'un segment donnée il existe bien des sources
 * @param $sgc_id Identifiant d'une condition de segment
 * @param $sos_id Facultatif, identifiant d'une origine de commande
 * @return bool True si existant, false dans le cas contraire ou si error
 */
function seg_criterions_sources_exists( $sgc_id, $sos_id=0 ){
	global $config;

	if( $sgc_id <= 0 || !is_numeric($sgc_id) ){
		return false;
	}
	if( $sos_id < 0 || !is_numeric($sos_id) ){
		return false;
	}

	$sql = '
		select top 1 scs_sgc_id
		from seg_criterions_sources
		where scs_tnt_id='.$config['tnt_id'].'
			and scs_sgc_id ='.$sgc_id.'
	';

	if($sos_id){
		$sql .= ' and scs_sos_id='.$sos_id;
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les identifiants de source pour un segment
 * @param $sgc_id Facultaif, identifiant d'une condition de segment
 * @param $sos_id Facultatif, identifiant d'une origine de commande
 * @return bool false si erreur, sinon le résultat de la requete sous forme de resource sql
 */
function seg_criterions_sources_get( $sgc_id=0, $sos_id=0 ){
	global $config;

	if( $sgc_id < 0 || !is_numeric($sgc_id) ){
		return false;
	}
	if( $sos_id < 0 || !is_numeric($sos_id) ){
		return false;
	}

	$sql = '
		select scs_sgc_id as sgc_id, scs_sos_id as sos_id
		from seg_criterions_sources
		where scs_tnt_id='.$config['tnt_id'].'
	';

	if( $sgc_id ){
		$sql.= ' and scs_sgc_id='.$sgc_id;
	}
	if( $sos_id ){
		$sql.= ' and scs_sos_id='.$sos_id;
	}

	return ria_mysql_query($sql);

}

/** Cette fonction permet de supprimer des identifiants source pour un segment
 * @param $sgc_id Identifiant d'une condition de segment
 * @param $sos_id Facultatif, identifiantd'une origine de commande
 *	@return bool True en cas de succès, False en cas d'échec
 */
function seg_criterions_sources_del( $sgc_id,  $sos_id=0){
	global $config;

	if( $sgc_id <= 0 || !is_numeric($sgc_id) ){
		return false;
	}
	if( $sos_id < 0 || !is_numeric($sos_id) ){
		return false;
	}

	$sql = '
		delete from seg_criterions_sources
		where scs_tnt_id='.$config['tnt_id'].'
			and scs_sgc_id='.$sgc_id.'
	';

	if( $sos_id ){
		$sql .= ' and scs_sos_id='.$sos_id;
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction permet de modifier les identifiants source pour un segment
 *	@param int $sgc_id Identifiant d'une condition de segment
 *	@param int $sos_id Facultatif, nouvelle identifiant d'rigine de commande
 *	@return bool True en cas de succès, false en cas d'échec
 */
function seg_criterions_sources_update( $sgc_id,  $sos_id=0 ){
	if( !seg_segment_criterions_exists($sgc_id) ){
		return false;
	}
	if( $sos_id < 0 || !is_numeric($sos_id) ){
		return false;
	}

	global $config;

	if(!seg_criterions_sources_del($sgc_id)){
		return false;
	}
	if(!seg_criterions_sources_add($sgc_id, $sos_id)){
		return false;
	}else{
		return true;
	}
}
/** @} */

// \endcond

<?php
namespace Helpers\Https;

use Exception;
/**
 * \ingroup https_transition
 */
/**	\brief HttpObject permet d'avoir une base commune pourla génération des requêtes de récupération et de modification
 *	@{
 */
class HttpObject
{
	/**
	 * Liste d'identifiant d'objet
	 *
	 * @var array $obj_col_ids
	 */
	protected $obj_col_ids = array();
	/**
	 * La table correspondant à l'objet
	 *
	 * @var string $table
	 */
	protected $table = '';
	/**
	 * Le nom de la colonne qui correspond au tnt_id
	 *
	 * @var string $tnt_col
	 */
	protected $tnt_col = '';
	/**
	 * Nom des colonnes qui doivent être vérifié pour des lien htttp
	 *
	 * @var array $http_cols
	 */
	protected $http_cols = array();
	/**
	 * __construct
	 *
	 * @param array|string $obj_cols
	 * @param string $table
	 * @param string $tnt_col
	 * @param array|string $http_cols
	 * @return void
	 */
	public function __construct($obj_col_ids, $table, $tnt_col, $http_cols)
	{
		if (!is_array($obj_col_ids) && !is_string($obj_col_ids) ) {
			throw new Exception("obj_col_ids doit être un tableau ou un string");
		}
		if (!is_array($http_cols) && !is_string($http_cols) ) {
			throw new Exception("http_cols doit être un tableau ou un string");
		}
		if (!is_string($table) ) {
			throw new Exception("table doit être un string");
		}
		if (!is_string($tnt_col) ) {
			throw new Exception("tnt_col doit être un string");
		}

		$this->obj_col_ids = is_array($obj_col_ids) ? $obj_col_ids : array($obj_col_ids);
		$this->table = $table;
		$this->tnt_col = $tnt_col;
		$this->http_cols = is_array($http_cols) ? $http_cols : array($http_cols);
	}
	/**
	 * objColIds
	 *
	 * @return array
	 */
	public function objColIds()
	{
		return $this->obj_col_ids;
	}
	/**
	 * table
	 *
	 * @return string
	 */
	public function table()
	{
		return $this->table;
	}
	/**
	 * tntCol
	 *
	 * @return string
	 */
	public function tntCol()
	{
		return $this->tnt_col;
	}
	/**
	 * httpCols
	 *
	 * @return array
	 */
	public function httpCols()
	{
		return $this->http_cols;
	}
}
//@}
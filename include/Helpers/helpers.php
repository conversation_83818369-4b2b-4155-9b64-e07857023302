<?php
/**
 * @file Ce fichier regroupe des fonctions simple et rapide qui aide au quotidient du dev
 */
if( !function_exists('env') ){
	/**
	 * Cette fonction permet de retourner une variable d'environnement si elle est définie et d'avoir une valeur par défaut
	 *
	 * @param string $varname Nom de la variable d'environement
	 * @param mixed $default Facultatif, une valeur quelconque
	 *
	 * @return mixed Retourne la valeur de la variable d'environnement ou la valeur par defaut, null si rien
	 */
	function env($varname, $default=null){
		return ria_array_get($_ENV, $varname, $default);
	}
}

if( !function_exists('url_translate') ){
	/**
	 * Cette fonction permet de récupérer la traduction d'une url en fonction du contexte en court
	 *
	 * @param string $url Url à traduire
	 * @return string Retourne l'url traduite
	 */
	function url_translate($url){
		return rew_rewritemap_translate_get($url, i18n::getLang());
	}
}
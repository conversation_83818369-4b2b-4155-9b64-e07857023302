FPDF est constitu� des �l�ments suivants :

- le fichier principal fpdf.php contenant la classe
- les fichiers de m�trique des polices (contenus dans le r�pertoire font de cette archive)

Les fichiers de m�trique sont indispensables d�s que l'on veut �crire du texte dans un document.
Ils peuvent se trouver en trois endroits diff�rents :

- dans le r�pertoire d�fini par la constante FPDF_FONTPATH (si cette constante est d�finie)
- dans le r�pertoire font s'il se trouve au m�me niveau que fpdf.php (comme c'est le cas dans cette archive)
- dans un des r�pertoires accessibles par include()

Voici un exemple de d�finition de FPDF_FONTPATH (notez le slash final obligatoire) :

define('FPDF_FONTPATH','/home/<USER>/font/');
require('fpdf.php');

Si ces fichiers ne sont pas accessibles, la m�thode SetFont() produira l'erreur suivante :

FPDF error: Could not include font metric file


Remarques :

- Seuls les fichiers correspondant aux polices effectivement utilis�es sont n�cessaires
- Les tutoriels fournis dans l'archive sont pr�ts � �tre ex�cut�s

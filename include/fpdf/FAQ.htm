<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>FAQ</title>
<link type="text/css" rel="stylesheet" href="fpdf.css">
<style type="text/css">
ul {list-style-type:none; margin:0; padding:0}
ul#answers li {margin-top:2em}
</style>
</head>
<body>
<h2>FAQ</h2>
<ul>
<li><b>1.</b> <a href='#q1'>Quelle est exactement la licence de FPDF ? Y a-t-il des restrictions d'utilisation ?</a>
<li><b>2.</b> <a href='#q2'>Lorsque j'essaie de créer un PDF, plein de caractères bizarres s'affichent à l'écran.</a>
<li><b>3.</b> <a href='#q3'>J'essaie de générer un PDF et IE m'affiche une page blanche. Que se passe-t-il ?</a>
<li><b>4.</b> <a href='#q4'>J'envoie des paramètres en utilisant la méthode POST et les valeurs n'apparaissent pas dans le PDF.</a>
<li><b>5.</b> <a href='#q5'>Lorsque j'utilise une session PHP, IE n'affiche plus mon PDF mais propose de le télécharger.</a>
<li><b>6.</b> <a href='#q6'>Quand je suis en SSL, IE n'arrive pas à ouvrir le PDF.</a>
<li><b>7.</b> <a href='#q7'>Les caractères accentués sont remplacés par des caractères bizarres, par exemple â©.</a>
<li><b>8.</b> <a href='#q8'>Comment mettre un fond à mon PDF ?</a>
<li><b>9.</b> <a href='#q9'>J'ai l'erreur suivante quand j'essaie de générer un PDF : Warning: Cannot add header information - headers already sent by (output started at script.php:X)</a>
<li><b>10.</b> <a href='#q10'>J'essaie d'afficher une variable dans la méthode Header mais rien ne s'imprime.</a>
<li><b>11.</b> <a href='#q11'>J'ai défini les méthodes Header et Footer dans ma classe PDF mais rien ne s'affiche.</a>
<li><b>12.</b> <a href='#q12'>Je n'arrive pas à faire de retour à la ligne. J'ai bien mis \n dans la chaîne imprimée par MultiCell mais ça ne marche pas.</a>
<li><b>13.</b> <a href='#q13'>J'essaie de mettre le caractère euro mais je n'y arrive pas.</a>
<li><b>14.</b> <a href='#q14'>Je dessine un cadre avec des dimensions très précises, mais à l'impression je constate des écarts.</a>
<li><b>15.</b> <a href='#q15'>Je voudrais utiliser toute la surface de la page mais à l'impression j'ai toujours des marges. Comment les enlever ?</a>
<li><b>16.</b> <a href='#q16'>Quelle est la taille limite des fichiers que je peux générer avec FPDF ?</a>
<li><b>17.</b> <a href='#q17'>Est-ce que je peux modifier un PDF avec FPDF ?</a>
<li><b>18.</b> <a href='#q18'>Je voudrais faire un moteur de recherche en PHP et indexer des PDF. Est-ce que je peux le faire avec FPDF ?</a>
<li><b>19.</b> <a href='#q19'>Est-ce que je peux transformer une page HTML en PDF avec FPDF ?</a>
<li><b>20.</b> <a href='#q20'>Est-ce que je peux concaténer des PDF avec FPDF ?</a>
</ul>
<ul id='answers'>

<li id='q1'>
<p><b>1.</b> <span class='st'>Quelle est exactement la licence de FPDF ? Y a-t-il des restrictions d'utilisation ?</span></p>
FPDF est Freeware (c'est indiqué au début du fichier source). Il n'y a pas de restriction
d'usage. Vous pouvez l'incorporer librement dans votre application (commerciale ou non), avec
ou sans modification. Vous pouvez également le redistribuer.
</li>

<li id='q2'>
<p><b>2.</b> <span class='st'>Lorsque j'essaie de créer un PDF, plein de caractères bizarres s'affichent à l'écran.</span></p>
Ces caractères "bizarres" sont en fait le contenu réel du PDF. Ce comportement est un bug d'IE.
Lorsqu'il reçoit d'abord une page HTML, puis un PDF à partir de la même URL, il l'affiche
directement sans lancer le plug-in Acrobat. Cela arrive fréquemment en cours de développement :
à la moindre erreur de script, une page HTML est envoyée, et après correction, le PDF arrive.
<br>
Pour résoudre le problème, il suffit de fermer IE et de le relancer. On peut aussi aller sur
une autre URL et revenir.
<br>
Pour éviter ce genre de désagrément durant le développement, on peut générer le PDF directement
dans un fichier et l'ouvrir via l'explorateur.
</li>

<li id='q3'>
<p><b>3.</b> <span class='st'>J'essaie de générer un PDF et IE m'affiche une page blanche. Que se passe-t-il ?</span></p>
Tout d'abord, vérifiez que vous n'envoyez rien au navigateur après le PDF (même pas un espace
ou un retour-chariot). Vous pouvez mettre un exit juste après l'appel à la méthode Output()
pour en être sûr.
<br>
Si ce n'est pas ça, c'est que vous êtes victime du syndrome de la "page blanche". IE utilisé
en conjonction avec le plug-in Acrobat souffre de très nombreux bugs, quelles que soient les
versions. Essayez de tester votre application avec le plus de versions d'IE possible (en tout cas
si elle est sur Internet). Le problème survient surtout lorsqu'on utilise la méthode POST, c'est
pourquoi il est fortement déconseillé de l'utiliser (d'autant qu'elle pose d'autres problèmes,
voir la question suivante). Le GET marche mieux mais peut échouer lorsque l'URL devient trop
longue : il ne faut pas dépasser 45 caractères pour la query string. Il existe cependant une
astuce pour dépasser cette limite : terminer l'URL par .pdf, ce qui trompe IE. Si vous utilisez
un formulaire, il suffit de rajouter un champ caché en dernière position :
<div class="doc-source">
<pre><code>&lt;INPUT TYPE=&quot;HIDDEN&quot; NAME=&quot;ext&quot; VALUE=&quot;.pdf&quot;&gt;</code></pre>
</div>
L'utilisation de session PHP cause également souvent des dysfonctionnements (il faut éviter
d'envoyer des en-têtes HTTP empêchant la mise en cache). Voir la question 5 pour un moyen
de résoudre le problème.
<br>
<br>
Pour éviter tous ces problèmes de manière fiable, il existe deux principales techniques :
<br>
<br>
- Désactiver le plug-in et utiliser Acrobat comme application externe. Pour cela, lancez
Acrobat ; dans le menu Fichier, Préférences, Générales, désactivez l'option "Intégrer au
navigateur Web" (pour Acrobat 5 : Edition, Préférences, Options, "Afficher dans le navigateur").
Puis, lorsque vous récupérez un PDF dans IE, ce dernier affiche la boîte "Ouvrir ce fichier" ou
"Enregistrer ce fichier". Décochez la case "Toujours demander avant d'ouvrir ce type de fichier"
et choisissez Ouvrir. Dorénavant les PDF s'ouvriront automatiquement dans une fenêtre Acrobat
indépendante.
<br>
L'inconvénient de la méthode est qu'il faut toucher à la configuration du poste client, ce
qu'on peut faire en intranet mais pas pour Internet.
<br>
<br>
- Utiliser une technique de redirection. Le principe consiste à générer le PDF dans un fichier
temporaire sur le serveur et à rediriger le client dessus (en utilisant du JavaScript, pas
l'en-tête HTTP Location qui pose aussi des problèmes). Par exemple, à la fin du script, on
peut mettre :
<div class="doc-source">
<pre><code>//Détermination d'un nom de fichier temporaire dans le répertoire courant
$file=basename(tempnam(getcwd(),'tmp'));
//Sauvegarde du PDF dans le fichier
$pdf-&gt;Output($file);
//Redirection JavaScript
echo &quot;&lt;HTML&gt;&lt;SCRIPT&gt;document.location='getpdf.php?f=$file';&lt;/SCRIPT&gt;&lt;/HTML&gt;&quot;;</code></pre>
</div>
Puis ceci dans getpdf.php :
<div class="doc-source">
<pre><code>&lt;?php
$f=$HTTP_GET_VARS['f'];
//Contrôle du fichier (à ne pas oublier !)
if(substr($f,0,3)!='tmp' or strpos($f,'/') or strpos($f,'\\'))
    die(&quot;Nom de fichier incorrect&quot;);
if(!file_exists($f))
    die(&quot;Le fichier n'existe pas&quot;);
//Traitement de la requête spéciale IE au cas où
if($HTTP_SERVER_VARS['HTTP_USER_AGENT']=='contype')
{
    Header('Content-Type: application/pdf');
    exit;
}
//Envoi du PDF
Header('Content-Type: application/pdf');
Header('Content-Length: '.filesize($f));
readfile($f);
//Suppression du fichier
unlink($f);
exit;
?&gt;</code></pre>
</div>
Cette méthode fonctionne dans la plupart des cas, mais pose encore des problèmes avec IE6. La
méthode "ultime" consiste à rediriger directement sur le fichier temporaire. Ce dernier doit
donc avoir l'extension .pdf :
<div class="doc-source">
<pre><code>//Détermination d'un nom de fichier temporaire dans le répertoire courant
$file=basename(tempnam(getcwd(),'tmp'));
rename($file,$file.'.pdf');
$file.='.pdf';
//Sauvegarde du PDF dans le fichier
$pdf-&gt;Output($file);
//Redirection JavaScript
echo &quot;&lt;HTML&gt;&lt;SCRIPT&gt;document.location='$file';&lt;/SCRIPT&gt;&lt;/HTML&gt;&quot;;</code></pre>
</div>
Cette méthode transforme un PDF dynamique en PDF statique et évite ainsi tous les ennuis.
Par contre, il faut prévoir une procédure de nettoyage pour effacer les fichiers temporaires.
Par exemple :
<div class="doc-source">
<pre><code>function CleanFiles($dir)
{
    //Efface les fichiers temporaires
    $t=time();
    $h=opendir($dir);
    while($file=readdir($h))
    {
        if(substr($file,0,3)=='tmp' and substr($file,-4)=='.pdf')
        {
            $path=$dir.'/'.$file;
            if($t-filemtime($path)&gt;3600)
                @unlink($path);
        }
    }
    closedir($h);
}</code></pre>
</div>
Cette fonction efface tous les fichiers de la forme tmp*.pdf dans le répertoire spécifié qui
datent de plus d'une heure. Vous pouvez l'appeler où vous voulez, par exemple dans le script
qui génère le PDF.
<br>
<br>
Remarque : il est nécessaire d'ouvrir une nouvelle fenêtre pour le PDF, car on ne peut plus
revenir en arrière à cause de la redirection.
</li>

<li id='q4'>
<p><b>4.</b> <span class='st'>J'envoie des paramètres en utilisant la méthode POST et les valeurs n'apparaissent pas dans le PDF.</span></p>
C'est un problème qui affecte certaines versions d'IE (en particulier la première 5.5). Voir la
question précédente pour les moyens de le contourner.
</li>

<li id='q5'>
<p><b>5.</b> <span class='st'>Lorsque j'utilise une session PHP, IE n'affiche plus mon PDF mais propose de le télécharger.</span></p>
C'est un problème qui affecte certaines versions d'IE. Pour le contourner, ajoutez la ligne
suivante avant session_start() :
<div class="doc-source">
<pre><code>session_cache_limiter('private');</code></pre>
</div>
ou bien faites une redirection comme expliqué à la question 3.
</li>

<li id='q6'>
<p><b>6.</b> <span class='st'>Quand je suis en SSL, IE n'arrive pas à ouvrir le PDF.</span></p>
Le problème peut être résolu en ajoutant cette ligne :
<div class="doc-source">
<pre><code>Header('Pragma: public');</code></pre>
</div>
</li>

<li id='q7'>
<p><b>7.</b> <span class='st'>Les caractères accentués sont remplacés par des caractères bizarres, par exemple â©.</span></p>
Il ne faut pas utiliser l'encodage UTF-8. Les polices standard de FPDF utilisent l'ISO-8859-1 ou Windows-1252.
On peut effectuer une conversion en ISO-8859-1 grâce à utf8_decode() :
<div class="doc-source">
<pre><code>$str = utf8_decode($str);</code></pre>
</div>
Mais certains caractères comme l'euro ne seront pas correctement traduits. Si vous disposez de l'extension
iconv, la bonne manière de faire est la suivante :
<div class="doc-source">
<pre><code>$str = iconv('UTF-8', 'windows-1252', $str);</code></pre>
</div>
</li>

<li id='q8'>
<p><b>8.</b> <span class='st'>Comment mettre un fond à mon PDF ?</span></p>
Pour une image, appelez Image() dans la méthode Header(), avant toute autre écriture. Pour mettre simplement
une couleur, utilisez Rect().
</li>

<li id='q9'>
<p><b>9.</b> <span class='st'>J'ai l'erreur suivante quand j'essaie de générer un PDF : Warning: Cannot add header information - headers already sent by (output started at script.php:X)</span></p>
Il ne faut rien envoyer d'autre au navigateur que le PDF lui-même : pas d'HTML, pas d'espace, pas
de retour-chariot, ni avant ni après. Le script envoie quelque chose à la ligne X.
</li>

<li id='q10'>
<p><b>10.</b> <span class='st'>J'essaie d'afficher une variable dans la méthode Header mais rien ne s'imprime.</span></p>
Il faut utiliser le mot-clé <tt>global</tt>, par exemple :
<div class="doc-source">
<pre><code>function Header()
{
    global $titre;

    $this-&gt;SetFont('Arial','B',15);
    $this-&gt;Cell(0,10,$titre,1,1,'C');
}</code></pre>
</div>
</li>

<li id='q11'>
<p><b>11.</b> <span class='st'>J'ai défini les méthodes Header et Footer dans ma classe PDF mais rien ne s'affiche.</span></p>
Il faut créer un objet de la classe PDF et non pas FPDF :
<div class="doc-source">
<pre><code>$pdf=new PDF();</code></pre>
</div>
</li>

<li id='q12'>
<p><b>12.</b> <span class='st'>Je n'arrive pas à faire de retour à la ligne. J'ai bien mis \n dans la chaîne imprimée par MultiCell mais ça ne marche pas.</span></p>
Il faut mettre la chaîne entre guillemets et non pas entre apostrophes.
</li>

<li id='q13'>
<p><b>13.</b> <span class='st'>J'essaie de mettre le caractère euro mais je n'y arrive pas.</span></p>
Pour les polices standard, le caractère euro a pour code 128. Vous pouvez par commodité définir
une constante comme suit :
<div class="doc-source">
<pre><code>define('EURO',chr(128));</code></pre>
</div>
</li>

<li id='q14'>
<p><b>14.</b> <span class='st'>Je dessine un cadre avec des dimensions très précises, mais à l'impression je constate des écarts.</span></p>
Pour respecter les dimensions, il faut décocher la case "Ajuster" dans la boîte de dialogue
d'impression.
</li>

<li id='q15'>
<p><b>15.</b> <span class='st'>Je voudrais utiliser toute la surface de la page mais à l'impression j'ai toujours des marges. Comment les enlever ?</span></p>
Les imprimantes ont toutes des marges physiques (variables en fonction du modèle), il est donc
impossible de les supprimer et d'imprimer sur la totalité de la page.
</li>

<li id='q16'>
<p><b>16.</b> <span class='st'>Quelle est la taille limite des fichiers que je peux générer avec FPDF ?</span></p>
Il n'y a pas de limite particulière. Il existe cependant certaines contraintes :
<br>
<br>
- La taille mémoire allouée par défaut aux scripts PHP est de 8 Mo. Pour de très gros
documents, en particulier avec des images, cette limite peut être atteinte (le fichier étant
construit en mémoire). Elle est paramétrée dans php.ini.
<br>
<br>
- Le temps d'exécution alloué par défaut est de 30 secondes. Cette limite peut bien entendu
être facilement dépassée. Elle est paramétrée dans php.ini et peut être éventuellement modifiée
à l'exécution par set_time_limit().
<br>
<br>
- Les navigateurs ont généralement un time-out de 5 minutes. Si vous envoyez le PDF directement
au navigateur et que vous dépassez cette limite, il sera perdu. Il est donc conseillé pour les
très gros documents de les générer dans un fichier, et d'envoyer des données de temps en temps
au navigateur (par exemple page 1, page 2... en utilisant flush() pour forcer l'envoi).
Lorsque le fichier est terminé, vous pouvez effectuer une redirection dessus avec JavaScript
ou bien créer un lien.
<br>
Remarque : même lorsque le navigateur part en time-out, il est possible que le script continue
à s'exécuter sur le serveur.
</li>

<li id='q17'>
<p><b>17.</b> <span class='st'>Est-ce que je peux modifier un PDF avec FPDF ?</span></p>
Il est possible d'importer un PDF existant grâce à l'extension FPDI :<br>
<br>
<a href="http://www.setasign.de/products/pdf-php-solutions/fpdi/" target="_blank">http://www.setasign.de/products/pdf-php-solutions/fpdi/</a>
</li>

<li id='q18'>
<p><b>18.</b> <span class='st'>Je voudrais faire un moteur de recherche en PHP et indexer des PDF. Est-ce que je peux le faire avec FPDF ?</span></p>
Non. Par contre il existe un utilitaire GPL en C, pdftotext, capable d'extraire le contenu
textuel d'un PDF. Il est fourni avec l'archive de Xpdf :<br>
<br>
<a href="http://www.foolabs.com/xpdf/" target="_blank">http://www.foolabs.com/xpdf/</a>
</li>

<li id='q19'>
<p><b>19.</b> <span class='st'>Est-ce que je peux transformer une page HTML en PDF avec FPDF ?</span></p>
Non, on ne peut convertir que de l'HTML très simple, pas des pages réelles. Par contre il
existe un utilitaire GPL en C, htmldoc, qui permet de le faire et donne de bons résultats :<br>
<br>
<a href="http://www.htmldoc.org" target="_blank">http://www.htmldoc.org</a>
</li>

<li id='q20'>
<p><b>20.</b> <span class='st'>Est-ce que je peux concaténer des PDF avec FPDF ?</span></p>
Non. Par contre il existe des utilitaires gratuits pour le faire :<br>
<br>
<a href="http://thierry.schmit.free.fr/spip/spip.php?rubrique7" target="_blank">http://thierry.schmit.free.fr/spip/spip.php?rubrique7</a><br>
<a href="http://www.accesspdf.com/pdftk/" target="_blank">http://www.accesspdf.com/pdftk/</a>
</li>
</ul>
</body>
</html>

<?php

/** \defgroup ExportModule Module d'export des données de riashop
 *	Ce module comprend les fonctions nécessaires à la gestion des exports
 * @{
 */


/** Cette fonction permet d'ajouter un export
 *  @param int $cls_id Obligatoire, Identifiant de la classe de l'export
 *  @param string $file_path Obligatoire, Chemin du fichier csv sur le disque
 *  @param string $name Obligatoire, Nom du fichier de l'export
 *  @param int $line_count Optionnel, Nombre de lignes de l'export
 *  @param int $line_processed Optionnel, Nombre de ligne traitées
 *  @param string $state Optionnel, Etat de l'export ('pending', 'processing', 'finished', 'error')
 *  @param string $error_msg Optionnel, Message d'erreur de l'export
 *
 *  @return int|bool L'id de l'export en cas de succès, false dans le cas contraire
 */
function exp_exports_add( $cls_id, $file_path, $name='', $line_count=0, $line_processed=0, $state='pending', $error_msg='' ){


    if( !is_numeric( $cls_id ) || $cls_id <= 0 ){
		return false;
    }

    if( !is_string( $file_path ) || trim( $file_path ) == '' ){
		return false;
    }

    if( !is_string($name) || trim($name) == '' ){
        return false;
    }

    if( !is_numeric($line_count) || $line_count < 0 ){
        return false;
    }

    if( !is_numeric($line_processed) ){
        return false;
    }

    if( !in_array($state, array( 'pending', 'processing', 'finished', 'error' )) ){
        return false;
    }


    global $config;

    $fields = array();
    $values = array();

    $fields[] = 'exp_tnt_id';
    $values[] = $config['tnt_id'];

    $fields[] = 'exp_wst_id';
    $values[] = $config['wst_id'];

    $fields[] = 'exp_date_created';
    $values[] = 'now()';

    $fields[] = 'exp_cls_id';
    $values[] = $cls_id;

    $fields[] = 'exp_file_path';
    $values[] = '"'.addslashes( $file_path ).'"';

    $fields[] = 'exp_name';
    $values[] = '"'.addslashes( $name ).'"';

    if( $line_count > 0 ){
        $fields[] = 'exp_line_count';
        $values[] = $line_count;
    }

    $fields[] = 'exp_line_processed';
    $values[] = $line_processed;

    $fields[] = 'exp_state';
    $values[] = '"'.addslashes( $state ).'"';

    if( trim($error_msg) != '' ){
        $fields[] = 'exp_error_msg';
        $values[] = '"'.addslashes( $error_msg ).'"';
    }

    $sql = '
		insert into exp_exports
			('.implode( ', ', $fields ).')
		values
			('.implode( ', ', $values ).')
    ';

	$r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}


/** Cette fonction permet de récupérer des exports
 *  @param int $id Optionnel, Identifiant de l'export
 *  @param array $state Optionnel, Tableau d'état de l'export ('pending', 'processing', 'finished', 'error')
 *  @param int $cls_id Optionnel, Classe de l'export
 *  @param int $wst_id Optionnel, Identifiant d'un site (prend par défaut $config['wst_id'])
 *
 *  @return resource Un résultat de requête mysql avec les colonnes suivantes :
 *          - id : identifiant de l'export
 *          - name : Nom du fichier de l'export
 *          - line_count : Nombre de lignes de l'export
 *          - line_processed : Nombre de lignes traitées de l'export
 *          - cls_id : Classe de l'export (type de contenu)
 *          - state : Etat de l'export
 *          - error_msg : Message d'erreur éventuel
 *          - file_path : Chemin du fichier d'export sur le disque
 *          - date_created : Date de création de l'export
 */
function exp_exports_get( $id=0, $state=array(), $cls_id=0, $wst_id=false ){

    if( !is_numeric($id) || $id < 0 ){
        return false;
    }

    if( count($state) ){
        foreach( $state as $s ){
            if( !in_array( $s, array( 'pending', 'processing', 'finished', 'error' )) ){
                return false;
            }
        }
    }

    if( !is_numeric($cls_id) || $cls_id < 0 ){
        return false;
    }

    global $config;

    if( !$wst_id ){
        $wst_id = $config['wst_id'];
    }

    $sql = '
        select
            exp_id as id,
            exp_name as name,
            exp_line_count as line_count,
            exp_line_processed as line_processed,
            exp_cls_id as cls_id,
            exp_state as state,
            exp_error_msg as error_msg,
            exp_file_path as file_path,
            exp_date_created as date_created
        from exp_exports
        where exp_tnt_id = '.$config['tnt_id'].'
            and exp_wst_id = '.$wst_id.'
            and exp_date_deleted is null
    ';

    if( count($state) ){
        $sql .= ' and exp_state in ("'.implode('","', $state).'")';
    }

    if( $cls_id > 0 ){
        $sql .= ' and exp_cls_id = '.$cls_id;
    }

    if( $id > 0 ){
        $sql .= ' and exp_id = '.$id;
    }

    $sql .= ' order by exp_date_created desc';
    $r = ria_mysql_query( $sql );

	if( !$r || !ria_mysql_num_rows( $r ) ){
		return false;
	}

	return $r;
}

/** Cette fonction permet de mettre à jour un export
 *  @param int $exp_id Obligatoire, identifiant de l'export
 *  @param string $state Optionnel, Etat de l'export ('pending', 'processing', 'finished', 'error')
 *  @param int $line_count Optionnel, nombre de lignes de l'export
 *  @param int $line_processed Optionnel, nombre de ligne traitée
 *
 *  @return bool True en cas de succès, false dans le cas contraire
 */
function exp_exports_upd( $exp_id, $state='', $line_count=0, $line_processed=0 ){

    if( !is_numeric($exp_id) || $exp_id <= 0 ){
        return false;
    }

    if( trim($state) != '' && !in_array($state, array( 'pending', 'processing', 'error', 'finished' )) ){
        return false;
    }

    if( !is_numeric($line_count) || $line_count < 0 ){
        return false;
    }

    if( !is_numeric($line_processed) || $line_processed < 0 ){
        return false;
    }

    global $config;

    $fields = array();
	$values = array();

    if( trim($state) != '' ){
        $fields[] = 'exp_state';
        $values[] = '"'.addslashes( $state ).'"';
    }

    if( $line_count > 0 ){
        $fields[] = 'exp_line_count';
        $values[] = $line_count;
    }

    if( $line_processed > 0 ){
        $fields[] = 'exp_line_processed';
        $values[] = $line_processed;
    }

    $data = array();
	for( $i = 0; $i < count( $fields ); $i++ ){
		$data[] = $fields[$i].'='.$values[$i];
	}

    $sql = '
        update exp_exports
        set '.implode( ', ', $data ).'
        where exp_tnt_id = '.$config['tnt_id'].'
        and exp_id = '.$exp_id.'
    ';

    $r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
    }

    return true;
}

/** Cette fonction permet d'archiver un export
 *  @param int $exp_id Obligatoire, identifiant de l'export
 *
 *  @return int L'identifiant de la ligne supprimé si succès, false dans le cas contraire
 */
function exp_exports_del( $exp_id ){

    if( !is_numeric($exp_id) || $exp_id <= 0 ){
        return false;
    }

    global $config;

    $sql = '
        update exp_exports
        set exp_date_deleted = now()
        where exp_tnt_id = '.$config['tnt_id'].'
            and exp_id = '.$exp_id.'
    ';

    $r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
	}

	return $exp_id;
}

/** Cette fonction permet de mettre l'export dans l'état 'finished', elle met également à jour le nombre de ligne traité
 *  @param int $exp_id Obligatoire, identifiant de l'export
 *
 *  @return bool True si succès, false dans le cas contraire
 */
function exp_exports_set_finished( $exp_id ){

    if( !is_numeric($exp_id) || $exp_id <= 0 ){
        return false;
    }

    global $config;

    $sql = '
        update exp_exports
        set exp_state = "finished",
            exp_line_processed = exp_line_count
        where exp_tnt_id = '.$config['tnt_id'].'
            and exp_id = '.$exp_id.'
    ';

    $r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
    }

    return true;
}

/** Cette fonction permet de renseigner un message d'erreur pour l'export
 *  @param int $exp_id Obligatoire, identifiant de l'export
 *  @param string $msg Obligatoire, message d'erreur
 *
 *  @return bool True en cas de succès, false dans le cas contraire
 */
function exp_exports_set_error( $exp_id, $msg ){

    if( !is_numeric($exp_id) || $exp_id <= 0 ){
        return false;
    }

    if( !is_string($msg) || trim($msg) == '' ){
        return false;
    }

    global $config;

    $sql = '
        update exp_exports
        set exp_state = "error",
            exp_error_msg = "'.addslashes( $msg ).'"
        where exp_tnt_id = '.$config['tnt_id'].'
            and exp_id = '.$exp_id.'
    ';

    $r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
    }

    return true;
}


/** Permet de récupérer la classe css du statut pour la couleur
 * @param string $state Obligatoire, le statut de l'export
 * @return string Retourne la classe css
 */
function exp_exports_state_css_class( $state ){

	switch($state){
		case 'error' :
			return 'exp-err';
		case 'finished' :
			return 'exp-success';
		case 'processing' :
            return 'exp-processing';
        case 'pending' :
            return 'exp-pending';
		default :
			return '';
	}
}

/** Cette fonction permet d'afficher les statuts d'exportation
 *	@param string $state Obligatoire, le statut de l'export
 *	@return string|bool Le statut, false si échec
 */
function exp_state_display( $state ){

	switch( $state ){
		case 'error' :
			return _('Erreur');
			break;
		case 'pending' :
			return _('En attente');
			break;
		case 'processing' :
			return _('En cours de traitement');
			break;
		case 'finished' :
			return _('Finalisé');
			break;
        default :
            return false;
            break;
	}
}



/// @}




<?php

require_once('orders.inc.php');


/** \defgroup model_ord_invoices Gestion des factures
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des factures.
 *
 *	@{
 */

 // \cond onlyria
/**	Cette fonction permet l'enregistrement d'une nouvelle facture depuis la gestion commerciale SAGE.
 *	Par défaut, les factures créées par cette fonction sont masquées et n'apparaissent donc pas dans
 *	la boutique.
 *	@param int $usr Identifiant interne de l'utilisateur facturé
 *	@param $piece Numéro de pièce sage
 *	@param string $ref Référence de la facture SAGE
 *	@param $date Date de création de la facture
 *	@param $discount Facultatif, taux d'escompte
 *	@param string $date_deadline Facultatif, date d'échéance de la facture
 *	@return int l'identifiant de la nouvelle facture en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_invoices_add_sage( $usr, $piece, $ref, $date, $discount=0, $date_deadline=false ){
	global $config;
	if( !gu_users_exists($usr) ) return false;
	if( !trim($piece) ) return false;

	$discount = str_replace( array(',', ' '), array('.', ''), $discount );
	if( !is_numeric( $discount ) ) return false;

	$date = preg_replace('/([0-9]{2})\/([0-9]{2})\/([0-9]{4})/','\3-\2-\1',$date);
	$date_deadline = $date_deadline !== false ? $date_deadline : 'null';

	// si une facture non masqué existe avec ce numéro de pièce on la supprime
	$rexists = ria_mysql_query('select inv_id from ord_invoices where inv_tnt_id='.$config['tnt_id'].' and inv_piece="'.addslashes($piece).'" and inv_masked=0');
	if( $rexists && ria_mysql_num_rows($rexists) ){
		$exists = ria_mysql_fetch_assoc($rexists);
		if( !ord_invoices_del($exists['inv_id']) ){
			return false;
		}
	}

	$res = ria_mysql_query('insert into ord_invoices (inv_tnt_id,inv_usr_id,inv_piece,inv_ref,inv_date,inv_masked,inv_discount,inv_date_deadline) values ('.$config['tnt_id'].','.$usr.',\''.addslashes($piece).'\',\''.addslashes($ref).'\',"'.$date.'",1,'.$discount.', '.$date_deadline.');');
	if( $res ){
		$inv_id = ria_mysql_insert_id();
		try{
			// Index la facture dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_INVOICE,
				'obj_id_0' => $inv_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
		return $inv_id;
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet le remplacement de la pièce d'une facture
 *
 * @param integer $inv_id Identifiant de la facture
 * @param string $piece Piece de la facture
 * @return boolean Retourne true si succès sinon false
 */
function ord_invoices_set_piece($inv_id, $piece)
{
	if (!is_numeric($inv_id) || $inv_id<=0) {
		throw new Exception("inv_id soit être un entier supperieur à 0");
	}

	if (!is_string($piece) || !trim($piece)) {
		throw new Exception("piece doit être une chaine de caractère");
	}

	global $config;

	$update = '
		update ord_invoices
			set inv_piece="'.addslashes($piece).'"
		where inv_tnt_id='.$config['tnt_id'].'
			and inv_id='.$inv_id.'
	';

	if( ria_mysql_query($update) ){

		try{
			// Index la facture dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_INVOICE,
				'obj_id_0' => $inv_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return true;
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet l'enregistrement d'une nouvelle facture
 * 	@param int $usr Identifiant de l'utilisateur facturé
 *  @param $date Date de création de la facture
 *  @param $discount Facultatif, taux d'escompte
 *  @return int L'identifiant de la nouvelle facture en cas de succès, false en cas d'échec
 */
function ord_invoices_add( $usr, $date, $discount=0){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	$discount = str_replace( array(',', ' '), array('.', ''), $discount );
	if( !is_numeric( $discount ) ) return false;

	$date = preg_replace('/([0-9]{2})\/([0-9]{2})\/([0-9]{4})/','\3-\2-\1',$date);

	$res = ria_mysql_query('insert into ord_invoices (inv_tnt_id,inv_usr_id,inv_date,inv_masked,inv_discount) values ('.$config['tnt_id'].','.$usr.',"'.$date.'",0,'.$discount.');');
	if( $res )
		return ria_mysql_insert_id();
	else
		return false;
}

// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existance d'une facture.
 *	@param int $id Identifiant de la facture à vérifier
 *	@param int $usr Optionnel, Identifiant de l'utilisateur auquel la commande doit appartenir
 *	@param $masked Optionnel, permet de rechercher dans les factures masquées.
 *	@return bool true si la facture existe
 *	@return bool false si la facture n'existe pas ou en cas d'erreur
 */
function ord_invoices_exists( $id, $usr=0, $masked=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select inv_id from ord_invoices
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$id.'
	';
	if( !$masked ){
		$sql .= ' and inv_masked = 0';
	}

	if( is_numeric($usr) && $usr > 0 ){
		$sql .= ' and inv_usr_id = '.$usr;
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existence d'une facture par son numéro de pièce.
 *	@param $piece Obligatoire, numéro de pièce de la facture à vérifier
 *	@return bool true si la facture existe
 *	@return bool false si la facture n'existe pas ou en cas d'erreur
 */
function ord_invoices_exists_piece( $piece ){

	$piece = trim($piece);
	if( !$piece ){
		return false;
	}

	$rinv = ord_invoices_get( 0, 0, 0, false, false, false, $piece, 0, false, false );

	return $rinv && ria_mysql_num_rows($rinv);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une facture.
 *	@param int $id Identifiant de la facture à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_invoices_del( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from ord_inv_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_inv_id = '.$id.'
	';

	ria_mysql_query($sql);

	$sql = '
		update ord_invoices
		set inv_masked = 1
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

/**	Cette fonction retourne les factures disponibles dans la base de données, éventuellement
 *	filtrées en fonction des paramètres optionnels fournis.
 *
 *	@param int|array $inv Optionnel, identifiant de la facture sur laquelle filtrer le résultat (ou tableau)
 *	@param int $usr Optionnel, identifiant ou tableau d'identifiants des utilisateurs sur lequel filtrer le résultat
 *	@param int $year Optionnel, année (à 4 chiffres) sur laquelle filtrer le résultat
 *	@param array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par date. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : piece, ref, date, total. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param bool $is_sync Optionnel, si true, ne retourne que les factures synchronisées (celles dont le numéro de pièce est présente)
 *	@param bool $usr_exist Optionnel, si true, ne retourne que les factures des clients non supprimés (excepté dans le cas où $usr est précisé)
 *	@param string $piece Optionnel, identifiant de la piece de la facture
 *	@param int $ord Optionnel, si précisé, retourne uniquement la ou les factures issues de cette commande
 *	@param string $date_start Facultatif, date de début d'une période sur laquelle filtrer le résultat
 *	@param string $date_end Facultatif, date de fin d'une période sur laquelle filtrer le résultat
 *	@param bool $include_mask Facultatif, afficher les factures masquées oui / non
 *	@param string $piece_like_type Facultatif, spécifie si le numéro de pièce (s'il est donné) est utilisé dans une clause LIKE. Plusieurs choix possibles :
 *		- both : LIKE '%[$piece]%'
 *		- right : LIKE '%[$piece]'
 *		- left : LIKE '[$piece]%'
 *		- Autres valeurs : pas de clause LIKE
 *	@param int $pay_id Facultatif, identifiant du moyen de paiement sur lequel filtrer le résultat
 *	@param int $seller_id Facultatif, identifiant du commercial attaché à cette facture sur lequel filtrer le résultat
 *	@param int $dps_id Facultatif, identifiant du dépôt de stockage attaché à cette facture sur lequel filtrer le résultat
 *
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la facture
 *			- usr_id : identifiant du compte utilisateur
 *			- year : Année de la facture
 *			- total_ht : montant total hors taxe de la facture
 *			- total_ttc : montant total ttc de la facture
 *			- piece : code de la pièce sage correspondante
 *			- ref : référence externe de la facture
 *			- date : date de la facture au format jj/mm/aaaa à hh:mm
 *			- datenotime : date de la facture au format jj/mm/aaaa
 *			- date_en : date de la facture au format anglais
 *			- age : nombre de jours depuis la création de la facture
 *			- discount : pourcentage d'escompte sur la facture (ce pourcentage est inclut dans les montants HT et TTC)
 *			- date_modified : date de dernière modification
 *			- date_modified_en : date de dernière modification au format EN
 *			- masked : détermine si la facture est masquée
 *			- date_deadline : date d'échéance de la facture
 *			- seller_id : identifiant du représentant rattaché à la facture
 *			- adr_invoices : identifiant de l'adresse de facturation
 *			- adr_delivery : identifiant de l'adresse de livraison
 *			- pay_id : identifiant du moyen de paiement
 */
function ord_invoices_get( $inv=0, $usr=0, $year=0, $sort=false, $is_sync=false, $usr_exist=false, $piece=false, $ord=0, $date_start=false, $date_end=false, $include_mask=false, $piece_like_type='', $pay_id=0, $seller_id=0, $dps_id=0 ){

	$inv = control_array_integer( $inv, false );
	if( $inv === false ){
		return false;
	}

	$usr = control_array_integer( $usr, false );
	if( $usr === false ){
		return false;
	}

	if( !is_numeric($year) || $year < 0 ){
		return false;
	}

	if( !is_numeric($ord) || $ord < 0 ){
		return false;
	}

	if( !is_numeric($pay_id) || $pay_id < 0 ){
		return false;
	}

	if( !is_numeric($seller_id) || $seller_id < 0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id < 0 ){
		return false;
	}

	if( $date_start && !isdate($date_start) ){
		return false;
	}

	if( $date_end && !isdate($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select
			inv_id as id, inv_usr_id as usr_id, year(inv_date) as "year",
			inv_total_ht as total_ht, inv_total_ttc as total_ttc,
			inv_piece as piece, inv_ref as ref, date_format(inv_date,"%d/%m/%Y à %H:%i") as date,
			date_format(inv_date,"%d/%m/%Y") as datenotime, inv_masked as "masked",
			to_days(now()) - to_days(inv_date) as age, inv_discount as discount, inv_date as date_en,
			date_format(inv_date_modified, "%d/%m/%Y à %H:%i") as date_modified, inv_date_modified as date_modified_en,
			date_format(inv_date_deadline, "%d/%m/%Y") as date_deadline, inv_date_deadline as date_deadline_en,
			inv_seller_id as seller_id,
			inv_adr_invoices as adr_invoices, inv_adr_delivery as adr_delivery, inv_pay_id as pay_id
		from
			ord_invoices
		where
			inv_tnt_id = '.$config['tnt_id'].'
	';

	if( !$include_mask && sizeof($inv)!=1 ){
		$sql .= ' and inv_masked = 0';
	}

	if( sizeof($inv) ){
		$sql .= ' and inv_id in ('.implode(', ', $inv).')';
	}

	if( sizeof($usr) ){
		$sql .= ' and inv_usr_id in ('.implode(', ', $usr).')';
	}

	if( !$date_start && !$date_end && $year > 0 ){
		$sql .= ' and year(inv_date) = '.$year;
	}

	if( $date_start ){
		$sql .= ' and date(inv_date) >= "'.dateparse($date_start).'"';
	}

	if( $date_end ){
		$sql .= ' and date(inv_date) <= "'.dateparse($date_end).'"';
	}

	if( $is_sync ){
		$sql .= ' and inv_piece != ""';
	}

	if( $usr_exist && !sizeof($usr) ){
		$sql .= '
			and inv_usr_id in (
				select usr_id from gu_users
				where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_date_deleted is null
			)
		';
	}

	if( $piece !== false && trim($piece) != '' ){
		$piece = addslashes($piece);
		switch( strtolower(trim($piece_like_type)) ){
			case 'both':
				$piece = '%'.$piece.'%';
				break;
			case 'left':
				$piece = $piece.'%';
				break;
			case 'right':
				$piece = '%'.$piece;
				break;
		}
		$sql .= ' and inv_piece like "'.$piece.'"';
	}

	if( $ord > 0 ){
		$sql .= '
			and exists (
				select 1 from ord_inv_products
				where prd_tnt_id = '.$config['tnt_id'].' and prd_inv_id = inv_id and prd_ord_id = '.$ord.'
			)
		';
	}

	if( $pay_id>0 ){
		$sql .= ' and inv_pay_id='.$pay_id;
	}

	if( $seller_id>0 ){
		$sql .= ' and inv_seller_id='.$seller_id;
	}

	if( $dps_id>0 ){
		$sql .= ' and inv_dps_id='.$dps_id;
	}

	$sort_final = array();

	// Converti le paramètre de tri en SQL
	if( is_array($sort) ){
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'piece':
					$sort_final[] = 'inv_piece '.$dir;
					break;
				case 'ref':
					$sort_final[] = 'inv_ref '.$dir;
					break;
				case 'date':
					$sort_final[] = 'inv_date '.$dir;
					break;
				case 'total':
					$sort_final[] = 'inv_total_ht '.$dir;
					break;
			}
		}
	}

	if( !sizeof($sort_final) ){
		$sort_final[] = 'inv_date desc';
	}

	$sql .= ' order by '.implode(', ', $sort_final);

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

/**	Cette fonction retourne les années pour lesquelles des factures sont disponibles dans l'historique
 *	d'un utilisateur donné.
 *	@param int $usr Identifiant de l'utilisateur dont on souhaite interroger l'historique
 *	@return bool False en cas d'échec
 *	@return array Un tableau simple contenant les années pour lesquels le client a eu au moins une facture. Si aucun résultat n'est trouvé, le tableau contient uniquement l'année en cours
 */
function ord_invoices_years_get( $usr ){

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}

	$years = array();

	global $config;

	$sql = '
		select year(inv_date) as y
		from ord_invoices
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_usr_id = '.$usr.'
			and inv_masked = 0
		group by year(inv_date)
		order by year(inv_date) desc
	';

	if( $ryears = ria_mysql_query($sql) ){
		while( $r = ria_mysql_fetch_assoc($ryears) ){
			$years[] = $r['y'];
		}
	}

	// ajoute l'année courante en début de tableau si elle n'est pas présente
	if( !sizeof($years) || $years[0] < date('Y') ){
		array_unshift( $years, date('Y') );
	}

	return $years;

}

/**	Cette fonction retourne les commandes rattachées à une facture donnée.
 *	@param int $inv Obligatoire, Identifiant de la facture
 *	@param $smart_sort Facultatif, si True, les commandes sont triées par le nombre de lignes les concernant contenues dans la facture
 *
 * 	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 * 			- id : identifiant de la commande
 * 			- ref : référencement de la commande
 * 			- piece : numéro de pièce de la commande dans la gestion commerciale
 * 			- date : date de commande (formaté au format jj/mm/yyyy à hh:mm)
 *			- pmt_id : Identifiant d'un éventuel code promotion
 *			- total_ht : total HT de la commande
 *			- total_ttc : Total TTC de la commande
 *			- pay_id : Identifiant du moyen de paiement de la commande
 *			- srv_id : Identifiant du service de livraison de la commande
 *			- wst_id : Identifiant du site de la commande
 *			- seller_id : Identifiant du représentant
 *	@return bool false en cas d'erreur
 */
function ord_invoices_orders_get( $inv, $smart_sort=false ){
	if( !ord_invoices_exists($inv) ) return false;
	global $config;

	$sql = '
		select
			ord_id as id, ord_ref as ref, ord_piece as piece,
			date_format(ord_date,"%d/%m/%Y à %H:%i") as date,
			ord_pmt_id as pmt_id, ord_total_ht as total_ht, ord_total_ttc as total_ttc, ord_pay_id as pay_id, ord_srv_id as srv_id, ord_wst_id as wst_id, ord_adr_invoices as inv_id, ord_adr_delivery as dlv_id, ord_usr_id as usr_id, ord_str_id as str_id, ord_rly_id as rly_id, ord_seller_id as seller_id
		from ord_orders
			join ord_inv_products on (ord_tnt_id=prd_tnt_id and ord_id=prd_ord_id)
		where ord_tnt_id='.$config['tnt_id'].' and prd_inv_id='.$inv.'
		group by ord_id, ord_ref, ord_piece, date_format(ord_date,"%d/%m/%Y à %H:%i"), ord_pmt_id, ord_total_ht, ord_total_ttc, ord_pay_id, ord_srv_id
		';

	if( $smart_sort==true ){
		$sql .= '
			order by count(*) desc
		';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/**	Alias de ord_invoices_orders_get()
 *	@param int $inv Voir ord_invoices_orders_get()
 *	@param $smart_sort Optionnel, voir ord_invoices_orders_get()
 *	@return Voir ord_invoices_orders_get()
 */
function ord_inv_orders_get( $inv, $smart_sort=false ){
	return ord_invoices_orders_get( $inv, $smart_sort );
}
// \endcond

// \cond onlyria
/**	Affiche une facture masquée dans la boutique
 * 	ATTENTION : Cette fonction est appelé à plusieurs moment lors de la synchronisation d'une commande et notamment lors de la mise à jour de celle-ci, cela veut dire que cette fonction peut-être appelé sur une commande qui a plusieurs mois.
 *	@param int $id Identifiant interne de la facture
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_invoices_unmask( $id ){

	if( !ord_invoices_exists( $id, 0, true ) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_masked = 0
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// Enregistrement des points de fidélité si ces derniers sont calculés sur leur facturation
	if( $config['rwd_reward_actived'] ){
		$user = ord_invoices_get_user( $id );
		$prf = gu_users_get_prf( $user );
		if( $prf ){
			$typcalc = rwd_rewards_get_type_calc( $prf );
			if( $typcalc == 'invoice' ){
				rwd_actions_apply( 'RWA_INVOICE', CLS_INVOICE, $id );
			}
		}
	}

	// Génération des cartes cadeaux à partir des factures (soumis à l'activation d'une variable de config : send_invoice_gifts)
	if( isset($config['send_invoice_gifts']) && $config['send_invoice_gifts'] ){
		if( !pmt_promotions_get_gift_card(CLS_INVOICE, $id, 0, 0) ){
			error('['.$config['tnt_id'].'] Impossible de générer les cartes cadeaux à partir de la facture n°'.$id);
			return false;
		}
	}

	try{
		// Index la facture dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_INVOICE,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du compte de rattachement d'une facture.
 *	Elle vient en correction d'un problème rencontré sur la base de données Boero.
 *	@param int $inv Identifiant interne de la facture
 *	@param int $usr Identifiant du compte client auquel la facture s'applique
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_invoices_update_user( $inv, $usr ){

	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( !gu_users_exists( $usr ) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_usr_id = '.$usr.'
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$inv.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la référence d'une facture
 *	@param int $inv Identifiant de la facture
 *	@param string $ref Nouvelle référence
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_invoices_update_ref( $inv, $ref ){

	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_ref = "'.addslashes($ref).'"
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$inv.'
	';

	if( ria_mysql_query($sql) ){

		try{
			// Index la facture dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_INVOICE,
				'obj_id_0' => $inv,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return true;
	}else{
		return false;
	}

}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de recalculer les totaux pour une facture.
 *	@param int $inv Identifiant de la facture à actualiser
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_invoices_update_totals( $inv ){
	global $config;
	if( !is_numeric($inv) || $inv<=0 ) return false;

	$sql = '
		update
			ord_invoices
		set
			inv_total_ht=(
				select sum( prd_price_ht*prd_qte ) from ord_inv_products where prd_tnt_id='.$config['tnt_id'].' and prd_inv_id=inv_id
			) * ( (100 - inv_discount) / 100 ), inv_total_ttc=(
				select sum( ifnull(prd_price_ttc,prd_price_ht*prd_tva_rate)*prd_qte ) from ord_inv_products where prd_tnt_id='.$config['tnt_id'].' and prd_inv_id=inv_id
			) * ( (100 - inv_discount) / 100 )
		where
			inv_tnt_id='.$config['tnt_id'].' and
			inv_id='.$inv
	;

	if( ria_mysql_query($sql) ){
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_INVOICE_STATS_PUT, array('tnt_id'=>$config['tnt_id'], 'id'=>$inv));
		return true;
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la date d'une facture
 *	@param int $inv Identifiant interne de la facture
 *	@param $date Date de la facture
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_invoices_update_date( $inv, $date ){

	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( !isdateheure($date) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_date = "'.$date.'"
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$inv.'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la date d'échéance d'une facture
 *	@param int $inv Identifiant interne de la facture
 *	@param string $date_deadline Date d'échéance de la facture
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_invoices_update_date_deadline( $inv, $date_deadline ){
	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( !isdateheure($date_deadline) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_date_deadline = "'.($date_deadline).'"
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id = '.$inv.'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le taux d'escompte (remise suivant la date de paiement) d'une facture
 *	@param int $inv Identifiant de la facture
 *	@param $discount Pourcentage de l'escompte
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_invoices_set_discount( $inv, $discount ){

	// Contrôles
	if( !is_numeric($inv) || $inv<=0 ) return false;
	$discount = str_replace( array(',', ' '), array('.', ''), $discount );
	if( !is_numeric( $discount ) ) return false;

	global $config;

	// Procède à la mise à jour et rafrachît les totaux
	$res = ria_mysql_query( 'update ord_invoices set inv_discount='.$discount.' where inv_id='.$inv.' and inv_tnt_id='.$config['tnt_id'] );
	if( $res )
		$res = ord_invoices_update_totals( $inv );

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer le ou les bons de livraison rattachés à une facture.
 *	La relation triplet (prd_ord_id, prd_id, prd_line_id) est utilisé ainsi que le lien directe avec les lignes de facture (ord_inv_products.prd_bl)
 *	@param integer $inv Identifiant de la facture
 *	@return resource résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du bl
 *			- piece : numéro de pièce sage
 *			- ref : référence sage
 *			- date : date/heure de création du bl dans sage
 *			- date_en : date/heure de création du bl dans sage au format d'origine (EN)
 *			- srv_id : identifiant du service de livraison
 *			- adr_dlv_id : identifiant de l'adresse de livraison du bl
 */
function ord_invoices_bl_get( $inv ){
	if( !is_numeric($inv) || $inv<=0 ){
		return false;
	}
	global $config;

	$ids = array();

	// récupération des identifiants des bl_id sur les ligne de bl lié a une facture par la commande
	$select_bl_from_orders = '
		select bp.prd_bl_id as id
		from ord_bl_products as bp
		join ord_inv_products as ip on ( bp.prd_tnt_id=ip.prd_tnt_id and bp.prd_id=ip.prd_id and bp.prd_line_id=ip.prd_line_id and bp.prd_ord_id=ip.prd_ord_id )
		where bp.prd_tnt_id='.$config['tnt_id'].' and ip.prd_inv_id='.$inv.'
		group by bp.prd_bl_id;
	';

	$r = ria_mysql_query($select_bl_from_orders);
	if ($r && ria_mysql_num_rows($r)) {
		while($id = ria_mysql_fetch_assoc($r)) {
			$ids[] = $id['id'];
		}
	}
	// récupération des identifiants des bl_id sur les ligne de facture directement lié au bl
	$select_bl_from_inv = '
		select ip.prd_bl as id
		from ord_inv_products as ip
		where ip.prd_tnt_id='.$config['tnt_id'].' and ip.prd_inv_id='.$inv.' and ip.prd_bl is not null
		group by ip.prd_bl;
	';

	$r = ria_mysql_query($select_bl_from_inv);
	if ($r && ria_mysql_num_rows($r)) {
		while($id = ria_mysql_fetch_assoc($r)) {
			$ids[] = $id['id'];
		}
	}

	//récupération des bls par les identifiants.
	$select_bl = '
		select bl_id as id,
			bl_piece as piece,
			bl_ref as ref,
			date_format(bl_date,"%d/%m/%Y à %H:%i") as date,
			bl_date as date_en,
			bl_srv_id as srv_id,
			bl_adr_dlv_id as adr_dlv_id
		from ord_bl
		where bl_tnt_id='.$config['tnt_id'].' and bl_masked = 0
		';
	if (!empty($ids)) {
		$select_bl .= ' and bl_id in ('.implode(', ', $ids).')';
	}else{
		// Pour question de rétro compatibilité la fonction retourne toujours un résultat mysql
		// or si $ids ne contient aucun identifiant la requête ne peux pas fonctioner
		// comme bl_id fait parti de la clé primaire de ord_bl il ne peux pas être null donc on s'assure qu'avec la condition
		// "and bl_id is null"
		// la requête ne retournera aucun résultat mais la fonction retournera toujours un résultat mysql, mais vide.
		$select_bl .= ' and bl_id is null';
	}


	return ria_mysql_query($select_bl);
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de renseigner un identifiant de bl a une ligne de facture
 *
 * @param integer $inv_id Identifiant de la facture
 * @param integer $prd_id Identifiant du produit de la facture
 * @param integer $line_id Identifiant de la ligne de facture
 * @param integer $bl_id Identifiant du bon de livraison
 * @return boolean Retourn true si success, sinon false
 *
 * @throws InvalidArgumentException Si un paramètre est fautif
 * @throws Exception Si il y a une erreur sql
 */
function ord_inv_products_set_bl_id($inv_id, $prd_id, $line_id, $bl_id=null) {
	if (!is_numeric($inv_id) || $inv_id <= 0) {
		throw new InvalidArgumentException("inv_id doit être un entier");
	}

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		throw new InvalidArgumentException("prd_id doit être un entier");
	}

	if (!is_numeric($line_id) || $line_id < 0) {
		throw new InvalidArgumentException("line_id doit être un entier");
	}

	if (!is_null($bl_id) && (!is_numeric($bl_id) || $bl_id <= 0)) {
		throw new InvalidArgumentException("bl_id doit être un entier ou null");
	}

	global $config;

	$update = '
		update ord_inv_products
			set prd_bl='.(is_null($bl_id) ? 'null' : $bl_id).'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_inv_id='.$inv_id.'
			and prd_id='.$prd_id.'
			and prd_line_id='.$line_id.'
	';


	$r = ria_mysql_query($update);

	if (!$r) {
		throw new Exception(ria_mysql_error());
	}

	ord_invoices_set_date_modified($inv_id);

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction notifie la création d'une nouvelle facture
 *	Il n'y a pas de notification standard, chaque loctaire dispose de son propre pattern
 *	Les factures supérieures à 5 jours ne sont pas notifiées
 *	Cette notification ne gère pas les factures englobant plusieurs commandes (seule la première commande est prise en compte)
 *
 *	@param int $inv Identifiant de la facture à notifier
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_invoices_notify( $inv ){
	global $config;

	if( !$config['email_alerts_enabled'] ){
		return true;
	}

	// désactivation temporaire de l'envoi des factures pour Chazelles
	if( $config['tnt_id']==8 ) return true;

	// Récupère la facture
	$rinv = ord_invoices_get($inv);
	if( !$rinv || !ria_mysql_num_rows( $rinv ) ) return false;
	$invoice = ria_mysql_fetch_array( $rinv );

	// Pas d'evnoi pour les factures supérieures à 5 jours
	if( $invoice['age']>5 ){
		return false;
	}



	// Récupère le compte client
	$rusr = gu_users_get( $invoice['usr_id'] );
	if( !$rusr || !ria_mysql_num_rows( $rusr ) ){
		return false;
	}
	$user = ria_mysql_fetch_array( $rusr );

	// Bigship ne gère pas le désabonnement à cette notification pour les clients particuliers (+ pierre oteiza temporairement)
	if( $config['tnt_id']!=1 && $config['tnt_id']!=13 ){
		if( !gu_ord_alerts_exists( $invoice['usr_id'], 8 ) ){
			return true;
		}
	}

	$wst_id = $config['wst_id'];
	$order = array( 'piece'=>'', 'date'=>'', 'ref'=>'', 'id'=>0, 'pmt_id'=>0, 'total_ht'=>0, 'total_ttc'=>0 );
	// Récupère la première commande de la facture ( pas de gestion multi-commandes, sauf pour Chazelles )
	$rorders = ord_inv_orders_get( $invoice['id'], true );
	if( $rorders && ria_mysql_num_rows( $rorders ) ){
		if( $config['tnt_id']!=8 ){
			$order = ria_mysql_fetch_array( $rorders );
			$wst_id = $order['wst_id'];
			ria_mysql_data_seek( $rorders, 0 );
		}else{
			$wst_id = ria_mysql_result( $rorders, 0, 'wst_id' );
			ria_mysql_data_seek( $rorders, 0 );
		}

		$have_pay_id = false;
		while( $o = ria_mysql_fetch_array($rorders) ){
			if( is_numeric($o['pay_id']) && $o['pay_id']>0 ){
				$have_pay_id = true;
				break;
			}
		}
		ria_mysql_data_seek( $rorders, 0 );

		// facture issue de commandes non web
		if( $config['ord_notify_only_web'] && !$have_pay_id ){
			return true;
		}
	}elseif (!isset($config['ord_inv_notify_without_order']) || !$config['ord_inv_notify_without_order']){
		return false;
	}

	$config_copy = $config;

	// Charge l'adresse du compte
	$raddress = gu_adresses_get( $invoice['usr_id'] );
	if( !$raddress || !ria_mysql_num_rows( $raddress ) ){
		return false;
	}
	$address = ria_mysql_fetch_array( $raddress );

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('ord-invoice', $wst_id);

	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		$cfg_email_name = 'ord-alert';
		if( $config['tnt_id']==8 && substr(gu_users_get_parent_ref($user['id']),0,2)=='US' ){
			$cfg_email_name = 'ord-alert-store';
		}
		$rcfg = cfg_emails_get($cfg_email_name, $wst_id);
	}

	if( !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array($rcfg);

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	switch( $config['tnt_id'] ){
		case 1: { /* Bigship */
			// notification spé bigship site public
			if( $config['tnt_id'] == 1 && $wst_id == 1 && (isset($config['bigship_prod']) && $config['bigship_prod'] || isset($config['bigship_emails_test']) && in_array($user['email'], $config['bigship_emails_test'])) ){
				require_once( $config['site_dir'].'/include/view.emails.inc.php' );
				$send = bigship_notify_invoice( $email, $cfg, $invoice, $user, $order, $address);
				$config = $config_copy;
				return $send;
			}

			if( $user['prf_id'] != PRF_CUSTOMER ) return false;

			$ems = ria_mysql_fetch_array(cfg_emails_get('ord-alert', $wst_id));
			$email->addBcc( $ems['bcc'] );
			$email->setReplyTo( $ems['reply-to'] );
			$email->addTo( $user['email'] );
			$email->setSubject('Facture bigship');
			$email->addHtml( $config['email_new_html_header'] );
			$email->addHtml( '<div style="text-align:center"><img src="'.$config['site_url'].'/images/email/header_facture.gif" /></div>' );
			$content  = "Votre Numéro de compte client : <strong>".$user['ref']."</strong><br/><br/><br/>";
			if( $address['title_name'] == null && $user['adr_lastname'] == null ) $clt = $user['society'];
			else $clt = $address['title_name']." ".$user['adr_lastname'];
			$content .= "Cher ".$clt.",<br/><br/>

						Nous vous remercions pour votre commande <strong>No ".$order['piece']."</strong> du <strong>".$order['date']."</strong>. <br/><br/>
									Vous trouverez ci-joint la facture <strong>N°".$invoice['piece']."</strong> correspondante.<br/><br/>

						Vous pouvez également la consulter via le lien suivant :<br/>
						<a href=\"".$config['site_url']."/mon-compte/commandes\" style=\"color:black\">".$config['site_url']."/mon-compte/commandes/</a> <br/><br/>

						Merci de votre confiance.<br/><br/>


						Cordialement,<br/>
						L'équipe Big Ship Accastillage.<br/><br/>

						<div style=\"text-align: center;font-size:0.8em;\">
							Votre satisfaction est notre priorité.<br/>
							Merci de nous faire part de vos commentaires et obsservations.<br/>
							<a href=\"".$config['site_url']."/nous-contacter\"><img src=\"".$config['site_url']."/images/email/avis.gif\"  style=\"border:none;\"/></a>
						</div>";

			$style_gauche = 'vertical-align: top;';
			$style_droite = 'vertical-align: top;width:172px;';
			$email->addHtml( '<table style="margin-top:20px;margin-bottom:20px; font-size: 1em;"><tr><td style="'.$style_gauche.'">'.$content.'</td></tr></table>' );
			//footer
			$footer = "
			<div style=\"font-size:0.7em;color:#8F8F8F;text-align: left;margin-bottom:10px;\">
				Cet email vous a été envoyé pour vous tenir informé(e) de l'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de
				ne plus le recevoir en utilisant la fonction 'Mes options' de votre espace client : <a style=\"color:#8F8F8F;\" href=\"".$config['site_url']."/mon-compte/options\">
				".$config['site_url']."/mon-compte/options</a>. <br/>
				Nous sommes en permanence à l'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet,
				n'hésitez pas à nous contacter : <a style=\"color:#8F8F8F;\" href=\"".$config['site_url']."/nous-contacter\">".$config['site_url']."/nous-contacter</a>
			</div>
			<img src=\"".$config[ 'site_url']."/images/email/footer_public.gif\"/>";

			$email->addHtml( $footer );
			//preparation de la facture en pièces jointes
			$dir = $config['doc_dir'].'/facture/'.$inv.'.pdf';
			require_once( '/var/www/www.bigship.com/htdocs/include/invoice-pdf.inc.php' );
			create_pdf($inv, $user['prf_id'], $dir);
			$email->addAttachment($dir);
				$email->addHtml( $config[ 'email_new_html_footer' ] );
			$res = $email->send();
			if( $res ){
				@unlink($dir);
			}
			return $res;
		}
		case 4: { /* Proloisirs */
			if ($config['wst_id'] == 30) { // Notification spécifique site public
				$file_notify_exists = false;
				$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
				if (file_exists($file_emails_perso)) {
					$file_notify_exists = true;
				}

				if ($file_notify_exists) {
					require_once($file_emails_perso);

					if (function_exists('riashop_invoice_notify')) {
						$notify_invoice = riashop_invoice_notify($email, $cfg, $invoice, $user, $order, $address);
						$config = $config_copy;
						return $notify_invoice;
					}
				}
			}elseif( $config['wst_id']==8 ){ // Notification spécifique Extranet
				$email->addTo( $user['email'] );
				if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
				if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
				if( $user['alert_cc'] ) $email->addCc( $user['alert_cc'] );
				$email->setSubject('Facturation de votre commande '.$order['ref']);
				$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
				$email->addParagraph('Cher client, chère cliente,');
				$email->addBlankTextLine();
				$inv = $invoice;
				$email->addParagraph('La facture correspondante à votre commande '.$order['ref'].' est disponible dans <a href="'.$config['site_url'].'/mon-compte/factures">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>.');
				$email->addBlankTextLine();

				$email->addParagraph(($user['ref'] ? 'Votre compte client porte le numéro '.$user['ref'].'.'."\n" : '' ).' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');
				$email->addBlankTextLine();

				$email->addParagraph(
					"Cordialement,\n".
					"L'équipe ".$config['site_name'].'.'
				);

				// Rappel configuration possible
				$email->addHorizontalRule();
				$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/options/alertes">'.$config['site_url'].'/mon-compte/options/alertes</a>.');
				$email->addBlankTextLine();

				$email->addHtml( $config['email_html_footer'] );
				return $email->send();
			}else{
				$email->addTo( $user['email'] );
				if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
				if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
				if( $user['alert_cc'] ) $email->addCc( $user['alert_cc'] );
				$email->setSubject('Votre commande '.$config['site_name'].' '.$order['ref']);
				$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
				$email->addParagraph('Cher client, chère cliente,');
				$email->addBlankTextLine();
				$inv = $invoice;
				$email->addParagraph('La facture correspondante à votre commande est disponible dans <a href="'.$config['site_url'].'/mon-compte/factures">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>.');
				$email->addBlankTextLine();

				$email->addParagraph('Votre commande porte le numéro '.$order['id'].' dans notre boutique en ligne.'."\n".( $order['piece'] ? 'Cette alerte email porte le numéro de pièce '.$order['piece'].' dans notre gestion commerciale.'."\n" : '' ).( $user['ref'] ? 'Votre compte client porte le numéro '.$user['ref'].'.'."\n" : '' ).' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');
				// Rappel suivi possible
				$email->addBlankTextLine();
				$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
				$email->addBlankTextLine();
				$email->addParagraph(
					"Cordialement,\n".
					"L'équipe ".$config['site_name'].'.'
				);

				// Rappel configuration possible
				$email->addHorizontalRule();
				$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/options/alertes">'.$config['site_url'].'/mon-compte/options/alertes</a>.');
				$email->addBlankTextLine();

				$email->addHtml( $config['email_html_footer'] );
				return $email->send();
			}
			break;
		}
		case 5: { /* LPO */
			$email->addTo( $user['email'] );
			if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
			if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
			if( $user['alert_cc'] ) $email->addCc( $user['alert_cc'] );
			$email->setSubject('Votre commande '.$config['site_name'].' '.$order['ref']);
			$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
			$email->addParagraph('Cher client, chère cliente,');
			$email->addBlankTextLine();
			$email->addParagraph('Le statut de votre commande No '.( $order['ref'] ? $order['ref'] : $order['id'] ).', enregistrée le '.$order['date'].', a été traitée par notre service client et va être confiée à notre transporteur très prochainement.');
			$email->addBlankTextLine();
			$inv = $invoice;
			$email->addParagraph('La facture correspondante est disponible dans <a href="'.$config['site_url'].'/mon-compte/factures/">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/factures/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>. Merci de bien vouloir noter qu\'aucune facture ne vous sera envoyée par courrier, dans un souci de développement durable. En cas de problème pour télécharger votre facture, n\'hésitez-pas à contacter notre <a href="'.$config['site_url'].$config['contact_page_url'].'">service client</a>.');
			$email->addBlankTextLine();
			$email->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');

			// Tableau récapitulatif
			$email->openTable();
			$email->openTableRow();
			$email->addCell( 'Ref' );
			$email->addCell( 'Désignation' );
			if( $user['prf_id']==PRF_CUSTOMER ){
				$email->addCell( 'Prix TTC' );
				$email->addCell( 'Qté' );
				$email->addCell( 'Total TTC' );
			}else{
				$email->addCell( 'Prix HT' );
				$email->addCell( 'Qté' );
				$email->addCell( 'Total HT' );
			}
			$email->closeTableRow();

			$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
			$unit = 'Gr';
			if( $ratio==1000 )
				$unit = 'Kg';
			elseif( $ratio==100000 )
				$unit = 'Qt';
			elseif( $ratio==1000000 )
				$unit = 'Tn';
			$port = 0;
			$products = ord_products_get($order['id']);
			while( $p = ria_mysql_fetch_array($products) ){
				if( prd_products_is_port($p['ref']) ){
					if( $user['prf_id']==PRF_CUSTOMER )
						$port += $p['price_ttc'];
					else
						$port += $p['price_ht'];
				}else{
					$email->openTableRow();
					$email->addCell( $p['ref'], 'right' );
					$email->addCell( $p['name'] );
							$qte_poids = null;
					if( $p['sell_weight'] && $p['weight_net_total'] && $config['show_price_in_weight_unit'] )
						$qte_poids = $p['weight_net_total'] / $ratio;
								$suffix = $user['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

					if( $qte_poids!==null )
						$email->addCell( number_format($p['price_'.$suffix] / $qte_poids,2,',',' '), 'right' );
					else
						$email->addCell( number_format($p['price_'.$suffix],2,',',' '), 'right' );
							if( $qte_poids!==null )
						$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
					elseif( $p['sell_weight'] && $p['weight_net_total'] )
						$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net_total'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
					else
						$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
							$email->addCell( number_format($p['total_'.$suffix],2,',',' '), 'right' );
							$email->closeTableRow();
							if( (trim($p['notes']) && $p['notes']!=$order['ref']) || trim($p['date-livr']) ){
						$prd_more = '';
						if( trim($p['notes']) && $p['notes']!=$order['ref'] )
							$prd_more = 'Notes : '.htmlspecialchars($p['notes']);
						if( trim($p['date-livr']) ){
							if( $prd_more ) $prd_more .= ', ';
							$prd_more .= 'Livraison différée le : '.$p['date-livr'];
						}
						$email->openTableRow();
						$email->addCell( $prd_more, 'left', 5 );
						$email->closeTableRow();
					}
						}
			}

			if($config[ 'show_port_in_order_confirmation' ]){
				// Exception Frais de port offerts
				$free_shipping = false;
				if( $order['pmt_id'] ){
					$pmt = ria_mysql_fetch_array(pmt_codes_get($order['pmt_id']));
					if( $pmt['free_shipping'] ){
						$services = pmt_codes_get_services($order['pmt_id']);
						if( !$order['srv_id'] && !$order['str_id'] )
							$free_shipping = true;
						elseif( in_array($order['srv_id'], $services) )
							$free_shipping = true;
						elseif( $order['str_id'] && in_array(-1, $services) )
							$free_shipping = true;
					}

					// Affichage code promotion
					$email->openTableRow();
					$email->addCell( 'Code promotion :', 'right', 4 );
					$email->addCell( $pmt['code'], 'right', 1, true );
					$email->closeTableRow();
				}

				// Frais de port
				$email->openTableRow();
				$email->addCell( 'Frais de port :', 'right', 4 );
				if( $free_shipping )
					$email->addCell( 'Offerts', 'right', 1, true );
				else
					$email->addCell( number_format($port,2,',',' '), 'right', 1, true );
				$email->closeTableRow();
			}
			if( $user['prf_id']!=PRF_CUSTOMER ){
				// Total HT
				$email->openTableRow();
				$email->addCell( 'Total HT :', 'right', 4 );
				$email->addCell( number_format($order['total_ht'],2,',',' '), 'right', 1, true );
				$email->closeTableRow();

				// Tva
				$email->openTableRow();
				$email->addCell( 'TVA :', 'right', 4 );
				$email->addCell( number_format($order['total_ttc']-$order['total_ht'],2,',',' '), 'right', 1, true );
				$email->closeTableRow();
			}

			// Total TTC
			$email->openTableRow();
			$email->addCell( 'Total TTC :', 'right', 4 );
			$email->addCell( number_format($order['total_ttc'],2,',',' '), 'right', 1, true );
			$email->closeTableRow();

			$email->closeTable();
			$email->addBlankTextLine();

			// Numéro de pièce interne
			$email->addBlankTextLine();

			$email->addParagraph('Votre commande porte le numéro '.$order['id'].' dans notre boutique en ligne.'."\n".( $order['piece'] ? 'Cette alerte email porte le numéro de pièce '.$order['piece'].' dans notre gestion commerciale.'."\n" : '' ).( $user['ref'] ? 'Votre compte client porte le numéro '.$user['ref'].'.'."\n" : '' ).' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');
			// Rappel suivi possible
			$email->addBlankTextLine();
			$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
			$email->addBlankTextLine();
			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);

			// Rappel configuration possible
			$email->addHorizontalRule();
			$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/mes-options">'.$config['site_url'].'/mon-compte/mes-options</a>.');
			$email->addBlankTextLine();

			// Rappel contact possible
			$email->addHorizontalRule();
			$email->addBlankTextLine();
			$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$config['site_url'].$config['contact_page_url'].'">'.$config['site_url'].$config['contact_page_url'].'</a>');

			$email->addHtml( $config['email_html_footer'] );

			return $email->send();
		}
		case 8: { /* Chazelles */
			$site_url = ria_mysql_fetch_array( cfg_overrides_get( 22, array(), 'site_url' ) );
			$config['site_url'] = $site_url['value'];
			$site_dir = ria_mysql_fetch_array( cfg_overrides_get( 22, array(), 'site_dir' ) );
			$config['site_dir'] = $site_dir['value'];
			$config['fld_order_inv_line'] = 737;
			$email->addTo( $user['email'] );
			if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
			if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
			if( $user['alert_cc'] ) $email->addCc( $user['alert_cc'] );
			$email->setSubject('Votre facture '.$config['site_name'].' '.$invoice['piece']);
			$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
			$order_text = '';
			if( ria_mysql_num_rows($rorders)==1 ){
				$order = ria_mysql_fetch_array($rorders);
				$order_text = "votre commande <strong>".( trim($order['ref'])!='' ? $order['ref'] : $order['piece'] )."</strong> du ".$order['date'];
			}else{
				$order_text = "vos commandes :\n";
				$first = true;
				while( $order = ria_mysql_fetch_array($rorders) ){
					if( $first )
						$first = false;
					else
						$order_text .= "\n";
					$order_text .= ( trim($order['ref'])!='' ? $order['ref'] : $order['piece'] )." du ".str_replace( ' à 00:00', '', $order['date'] );
				}
			}
			$email->addParagraph("
				Cher client, chère cliente,\n
				Nous vous remercions pour ".$order_text.".\n
				Vous trouverez ci-joint la facture <strong>N°".$invoice['piece']."</strong> correspondante.\n
				Vous pouvez également la consulter via le lien suivant :\n
				<a href=\"".$config['site_url']."/mes-factures/facture/?inv=".$invoice['id']."\">".$config['site_url']."/mes-factures/facture/?inv=".$invoice['id']."</a>\n
				Merci de votre confiance.
			");
			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe Chazelles."
			);
			// Rappel configuration possible
			$email->addHorizontalRule();
			$email->addBlankTextLine();
			$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Alertes emails\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/options/">'.$config['site_url'].'/mon-compte/options/</a>.');
			// Rappel contact possible
			$email->addHorizontalRule();
			$email->addBlankTextLine();
			$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$config['site_url'].$config['contact_page_url'].'">'.$config['site_url'].$config['contact_page_url'].'</a>');
			$email->addHtml( $config['email_html_footer'] );
			require_once( $config['site_dir'].'/include/view.invoice.inc.php' );
			$tmpdir = pdf_invoices( $invoice, ord_inv_products_get( $invoice['id'] ), $user );
			$email->addAttachment($tmpdir, 'Facture-'.$invoice['piece'].'.pdf');
			$res = $email->send();
			@unlink($tmpdir);
			return $res;
		}
		case 13: { /* Pierre Oteiza */

			$inv = $invoice;

			// pour test
			$email->addTo( '<EMAIL>' );

			/*$email->addTo( $user['email'] );
			if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
			if( $user['alert_cc'] ) $email->addCc( $user['alert_cc'] );*/

			$email->setSubject( 'Avis d\'expédition de votre commande '.$config['site_name'].' '.$inv['ref'] );
			$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
			$email->addParagraph('Cher client, chère cliente,');
			$email->addBlankTextLine();

			if( ria_mysql_num_rows($rorders) == 1 ){
				$ord = ria_mysql_fetch_array($rorders);
				$email->addParagraph('Le statut de votre commande No '.( $ord['ref'] ? $ord['ref'] : $ord['id'] ).', enregistrée le '.$ord['date'].', a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
			}elseif( ria_mysql_num_rows($rorders)>1 ){
				$ar_orders = array();
				while( $ord = ria_mysql_fetch_array($rorders) ){
					$ar_orders[] = ( trim($ord['ref']) != '' ? $ord['ref'] : $ord['id'] );
				}
				$email->addParagraph('Le statut de vos commandes '.implode(', ',$ar_orders).' a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
			}

			// service de livraison dans un champ libre de la facture (pas de BL)
			$srv_id = fld_object_values_get( $inv['id'], $config['fld_srv_inv'], '', false, true );
			// numéros des colis dans un champ libre de la facture (pas de BL)
			$colis = fld_object_values_get( $inv['id'], $config['fld_colis_inv'], '', false, true );

			$no_srv = false;
			if( is_numeric($srv_id) && $srv_id > 0 ){
				$rsrv = dlv_services_get( $srv_id );
				if( $rsrv && ria_mysql_num_rows($rsrv) ){
					$srv = ria_mysql_fetch_array($rsrv);
					if( $srv['url-site'] ){
						$srv_name = '<a href="'.$srv['url-site'].'">'.htmlspecialchars($srv['name']).'</a>';
					}else{
						$srv_name = htmlspecialchars($srv['name']);
					}
					$email->addParagraph( str_replace( '%nom%', $srv_name, $srv['alert-msg'] ) );
				}else{
					$no_srv = true;
				}
			}else{
				$no_srv = true;
			}

			if( $no_srv ){
				$email->addParagraph('Votre commande vient d\'être expédiée.'."\n".'Elle a été prise en charge par notre transporteur.');
			}elseif( $srv['url-colis'] ){
				// Numéros de colis
				$colis = explode(',', $colis);
				for( $i = 0; $i < sizeof($colis); $i++ ){
					$colis[ $i ] = '<a href="'.$srv['url-colis'].trim($colis[$i]).'">'.trim($colis[$i]).'</a>';
				}
				switch( sizeof($colis) ){
					case 0:
						break;
					case 1:
						$email->addParagraph('Votre commande porte le numéro de colis suivant chez notre transporteur : '.$colis[0]);
						break;
					default:
						$email->addParagraph('Votre commande à été expédiée en plusieurs colis.'."\n".'Voici leur références chez notre transporteur : '.implode(', ',$colis));
						break;
				}
			}

			// Rappel du contenu de la préparation de livraison
			$email->addBlankTextLine();
			$email->addParagraph('Veuillez trouver ci-dessous le détail des produits expédiés :');

			$suffix = $user['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

			// Tableau récapitulatif
			$email->openTable();
			$email->openTableRow();
			$email->addCell( 'Ref' );
			$email->addCell( 'Désignation' );
			$email->addCell( 'Prix '.strtoupper($suffix) );
			$email->addCell( 'Qté' );
			$email->addCell( 'Total '.strtoupper($suffix) );
			$email->closeTableRow();

			$products = ord_inv_products_get( $inv['id'] );

			while( $p = ria_mysql_fetch_array($products) ){
				$email->openTableRow();
				$email->addCell( $p['ref'], 'right' );
				$email->addCell( $p['name'] );

				if( $p['sell_weight'] ){
					$email->addCell( number_format($p['price_'.$suffix] * 1000,2,',',' '), 'right' );
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'] / 1000,3,',',' ') ).'&nbsp;Kg', 'right' );
				}else{
					$email->addCell( number_format($p['price_'.$suffix],2,',',' '), 'right' );
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
				}

				$email->addCell( number_format($p['total_'.$suffix],2,',',' '), 'right' );

				$email->closeTableRow();
			}

			if( $user['prf_id']!=PRF_CUSTOMER ){
				// Total HT
				$email->openTableRow();
				$email->addCell( 'Total HT :', 'right', 4 );
				$email->addCell( number_format($inv['total_ht'],2,',',' '), 'right', 1, true );
				$email->closeTableRow();

				// Tva
				$email->openTableRow();
				$email->addCell( 'TVA :', 'right', 4 );
				$email->addCell( number_format($inv['total_ttc']-$inv['total_ht'],2,',',' '), 'right', 1, true );
				$email->closeTableRow();
			}

			// Total TTC
			$email->openTableRow();
			$email->addCell( 'Total TTC :', 'right', 4 );
			$email->addCell( number_format($inv['total_ttc'],2,',',' '), 'right', 1, true );
			$email->closeTableRow();

			$email->closeTable();
			$email->addBlankTextLine();

			// Rappel suivi possible
			$email->addBlankTextLine();
			$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes/">'.$config['site_url'].'/mon-compte/commandes/</a>.');

			// Signature
			$email->addBlankTextLine();
			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);

			// Rappel configuration possible
			$email->addHorizontalRule();
			$email->addParagraph( 'Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/options/">'.$config['site_url'].'/mon-compte/options/</a>.' );
			$email->addBlankTextLine();

			$email->addHtml( $config['email_html_footer'] );

			return $email->send();
		}
		case 39: { /* Freevox */
			$is_extranet = $user['prf_id'] != PRF_CUSTOMER;

			$ems = ria_mysql_fetch_array(cfg_emails_get('ord-alert', $wst_id));

			$email->addTo( $user['email'] );
			if( trim($ems['cc']) != '' ){
				$email->addCc( $ems['cc'] );
			}
			if( trim($ems['bcc']) != '' ){
				$email->addBcc( $ems['bcc'] );
			}
			if( trim($ems['reply-to']) != '' ){
				$email->setReplyTo( $ems['reply-to'] );
			}

			if( $address['title_name'] == null && $user['adr_lastname'] == null ) $clt = $user['society'];
			else $clt = $address['title_name']." ".$user['adr_lastname'];

			$email->setSubject('Extranet Freevox : Votre facture n°'.$invoice['piece']);
			$email->addHtml( $config['email_html_header'] );

			$email->addParagraph("Cher ".$clt.",");

			if( $is_extranet ){
				$domain = 'http://extranet.freevox.fr';
			}else{
				$domain = 'http://www.freevox.fr';
			}

			$email->addBlankTextLine();
			$email->addParagraph('La facture correspondante à votre commande <strong>'.$order['piece'].'</strong> du '.$order['date'].' est disponible dans <a href="'.$domain.'/mon-compte/factures/">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$domain.'/mon-compte/factures/facture?inv='.$invoice['id'].'">'.$invoice['piece'].'</a>.');


				$email->addParagraph(
					"Cordialement,\n".
					"L'équipe ".$config['site_name'].'.'
				);

				$email->addHorizontalRule();
				$email->addHtml("<p>Cet email vous a été envoyé pour vous tenir informé(e) de l'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction 'Mes options' de votre espace client : <a style=\"color:#8F8F8F;\" href=\"".$domain."/mon-compte/options/\">\"Mes options\"</a>.</p>");
				$email->addBlankTextLine();

				$email->addHorizontalRule();
				$email->addHtml("<p>Nous sommes en permanence à l'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n'hésitez pas à nous contacter : <a style=\"color:#8F8F8F;\" href=\"".$domain."/nous-contacter/\">".$domain."/nous-contacter/</a></p>");
				$email->addBlankTextLine();

				$email->addHtml( $config[ 'email_html_footer' ] );
				$res = $email->send();

			return $res;
		}
		case 43: { /* Purebike */
			$ems = ria_mysql_fetch_array(cfg_emails_get('ord-alert', $wst_id));

			$email->addTo( $user['email'] );
			if( trim($ems['cc']) != '' ){
				$email->addCc( $ems['cc'] );
			}
			if( trim($ems['bcc']) != '' ){
				$email->addBcc( $ems['bcc'] );
			}

			if( $address['title_name'] == null && $user['adr_lastname'] == null ){
				$clt = $user['society'];
			}else{
				$clt = '';
				if( $address['title_id'] == 1 ){
					$clt = 'Cher ';
				}elseif( $address['title_id'] == 2 || $address['title_id'] == 3 ){
					$clt = 'Chère ';
				}
				$clt .= $address['title_name']." ".$user['adr_lastname'];
			}

			$email->setSubject('Purebike : Votre facture n°'.$invoice['piece']);
			$email->addHtml( $config['email_html_header'] );

			$email->addParagraph($clt.",");

			$email->addBlankTextLine();
			$email->addParagraph('La facture correspondante à votre commande <strong>'.$order['piece'].'</strong> du '.$order['date'].' est disponible dans <a href="http://www.purebike.fr/mon-compte/factures/">votre espace client</a>. Elle porte le numéro de pièce <a href="http://www.purebike.fr/mon-compte/facture/?inv='.$invoice['id'].'">'.$invoice['piece'].'</a>.');


			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);

			$email->addHorizontalRule();
			$email->addHtml("<p>Cet email vous a été envoyé pour vous tenir informé(e) de l'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction 'Mes options' de votre espace client : <a style=\"color:#8F8F8F;\" href=\"http://www.purebike.fr/mon-compte/options/\">\"Mes options\"</a>.</p>");
			$email->addBlankTextLine();

			$email->addHorizontalRule();
			$email->addHtml("<p>Nous sommes en permanence à l'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n'hésitez pas à nous contacter : <a style=\"color:#8F8F8F;\" href=\"http://www.purebike.fr/nous-contacter/?subject=contact\">http://www.purebike.fr/nous-contacter/</a></p>");
			$email->addBlankTextLine();

			$email->addHtml( $config[ 'email_html_footer' ] );
			$res = $email->send();

			return $res;
		}
		case 47: { /* Groupe-Leblanc */
			$ems = ria_mysql_fetch_array(cfg_emails_get('ord-alert', $wst_id));

			$email->addTo( $user['email'] );
			if( trim($ems['cc']) != '' ){
				$email->addCc( $ems['cc'] );
			}
			if( trim($ems['bcc']) != '' ){
				$email->addBcc( $ems['bcc'] );
			}
			if( trim($ems['reply-to']) != '' ){
				$email->setReplyTo( $ems['reply-to'] );
			}

			if( $address['title_name'] == null && $user['adr_lastname'] == null ) $clt = $user['society'];
			else $clt = $address['title_name']." ".$user['adr_lastname'];

			$email->setSubject('Extranet Groupe Leblanc : Votre facture n°'.$invoice['id']);
			$email->addHtml( $config['email_html_header'] );

			$email->addParagraph("Cher ".$clt.",");

			$email->addBlankTextLine();
			$email->addParagraph('La facture correspondante à votre commande <strong>'.$order['piece'].'</strong> du '.$order['date'].' est disponible dans <a href="'.rew_rewritemap_translate_get('http://extranet.groupe-leblanc.maquettes.riastudio.fr/mon-compte/factures/',i18n::getLang()).'">votre espace client</a>. Elle porte la référence '.$invoice['id'].((isset($invoice['ref'])&&trim($invoice['ref']))?" - ".$invoice['ref']:"").'');


			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);

			$email->addHorizontalRule();
			$email->addHtml("<p>Cet email vous a été envoyé pour vous tenir informé(e) de l'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction 'Mes options' de votre espace client : <a style=\"color:#8F8F8F;\" href=\"http://extranet.groupe-leblanc.maquettes.riastudio.fr/mon-compte/options/\">\"Mes options\"</a>.</p>");
			$email->addBlankTextLine();

			$email->addHorizontalRule();
			$email->addHtml("<p>Nous sommes en permanence à l'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n'hésitez pas à nous contacter : <a style=\"color:#8F8F8F;\" href=\"http://extranet.groupe-leblanc.maquettes.riastudio.fr/contact/\">Nous contacter</a></p>");
			$email->addBlankTextLine();

			$email->addHtml( $config[ 'email_html_footer' ] );
			$res = $email->send();

			return $res;
		}
		case 36: { /* Sardeco */
			$ems = ria_mysql_fetch_array(cfg_emails_get('ord-alert', $wst_id));

			$email->addTo( $user['email'] );
			if( trim($ems['cc']) != '' ){
				$email->addCc( $ems['cc'] );
			}
			if( trim($ems['bcc']) != '' ){
				$email->addBcc( $ems['bcc'] );
			}
			if( trim($ems['reply-to']) != '' ){
				$email->setReplyTo( $ems['reply-to'] );
			}

			if( $address['title_name'] == null && $user['adr_lastname'] == null ) $clt = $user['society'];
			else $clt = $address['title_name']." ".$user['adr_lastname'];

			$email->setSubject('Extranet Sardéco : Votre facture n°'.ord_invoices_name( $invoice['ref'], $invoice['piece'], $invoice['id'] ));
			$email->addHtml( $config['email_html_header'] );

			$email->addParagraph("Cher ".$clt.",");

			$email->addBlankTextLine();
			$email->addParagraph('La facture correspondante à votre commande <strong>'.$order['piece'].'</strong> du '.$order['date'].' est disponible dans <a href="'.rew_rewritemap_translate_get($config['site_url'].'/mon-compte/factures/',i18n::getLang()).'">votre espace client</a>. Elle porte la référence '.ord_invoices_name( $invoice['ref'], $invoice['piece'], $invoice['id'] )).'.';


			$email->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);

			$email->addHorizontalRule();
			$email->addHtml("<p>Cet email vous a été envoyé pour vous tenir informé(e) de l'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction 'Mes options' de votre espace client : <a style=\"color:#8F8F8F;\" href=\"".$config['site_url']."/mon-compte/options/\">\"Mes options\"</a>.</p>");
			$email->addBlankTextLine();

			$email->addHorizontalRule();
			$email->addHtml("<p>Nous sommes en permanence à l'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n'hésitez pas à nous contacter : <a style=\"color:#8F8F8F;\" href=\"".$config['site_url']."/contact/\">Nous contacter</a></p>");
			$email->addBlankTextLine();

			$email->addHtml( $config[ 'email_html_footer' ] );
			$res = $email->send();

			return $res;
		}
		case 51: { /* IDMAT OnEnFaitDesTonnes */
			if ( file_exists($config['site_dir'].'/include/view.emails.inc.php') ) {
				require_once($config['site_dir'].'/include/view.emails.inc.php');
				if ( function_exists('idmat_custom_invoice_notify') ) {
					$custom_order_notify = idmat_custom_invoice_notify($email, $cfg, $invoice, $user, $order, $address);
					$config = $config_copy;
					return $custom_order_notify;
				}
			}
			break;
		}
		default: {
			if (trim($cfg['cc']) != '') {
				$email->addCC($cfg['cc']);
			}

			if (trim($cfg['bcc']) != '') {
				$email->addBcc($cfg['bcc']);
			}

			if (trim($cfg['reply-to']) != '') {
				$email->setReplyTo($cfg['reply-to']);
			}

			// Gestion des notifications personnalisées
			if (isset($config['active_email_perso']) && $config['active_email_perso']) {
				$file_notify_exists = false;
				$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
				if (file_exists($file_emails_perso)) {
					$file_notify_exists = true;
				} else {
					$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';
					echo $file_emails_perso;

					if (file_exists($file_emails_perso)) {
						$file_notify_exists = true;
					}
				}
				if ($file_notify_exists) {
					require_once($file_emails_perso);

					if (function_exists('riashop_invoice_notify')) {
						$notify_invoice = riashop_invoice_notify($email, $cfg, $invoice, $user, $order, $address);
						$config = $config_copy;
						return $notify_invoice;
					}
				}
			}
			break;
		}
	}

	return false;
}
// \endcond

/**	Retourne la dénomination de la facture, à partir des éléments disponibles (référence, numéro de pièce sage, identifiant internet).
 *	@param string $ref Référence client de la facture
 *	@param $piece Numéro de pièce sage
 *	@param int $id Identifiant internet
 */
function ord_invoices_name( $ref, $piece, $id ){
	$inv_name = '';
	if( trim($ref) ){
		$inv_name = ucfirst($ref);
		if( trim($piece) )
			$inv_name .= ' ('.$piece.')';
	}elseif( trim($piece) ){
		$inv_name = $piece;
	}else{
		$inv_name = str_pad($id,8,'0',STR_PAD_LEFT);
	}
	return $inv_name;
}

/**	Cette fonction permet de retourner la liste des colis et numéro de BL associé à la facture
 *	@param int $inv Identifiant de facture à une référence recherche
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- bl_piece : Numéro de pièce du BL
 *		- colis : Numéro de colis
 *		- ord_piece : Numéro de pièce de la commande d'origine
 *		- date : Date de la commande (ou date du BL le cas échéant) au format jj/mm/aaaa
 *		- ord_id : Identifiant de la commande d'origine
 */
function ord_invoices_get_colis( $inv ){
	if( !ord_invoices_exists( $inv ) ) return false;
	global $config;

	$sql = '
		select distinct bl_piece , prd_colis as colis, ifnull(ord.ord_piece,\'\') as ord_piece,  ifnull(date_format(ord.ord_date,"%d/%m/%Y"),date_format(bl_date,"%d/%m/%Y")) as date, ord_id
		from ord_bl as bl
			join ord_bl_products as bl_prd on (bl_prd.prd_tnt_id='.$config['tnt_id'].' and bl_prd.prd_bl_id=bl.bl_id)
			join ord_inv_products as inv on ( inv.prd_tnt_id='.$config['tnt_id'].' and bl_prd.prd_id=inv.prd_id and bl_prd.prd_line_id=inv.prd_line_id and bl_prd.prd_ord_id=inv.prd_ord_id)
			left join ord_orders as ord on (ord.ord_tnt_id='.$config['tnt_id'].' and bl_prd.prd_ord_id = ord.ord_id)
		where bl.bl_tnt_id='.$config['tnt_id'].' and inv.prd_inv_id='.$inv.' and bl_masked = 0
	';

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de retourner l'identifiant du compte client concernés par une facture.
 *	@param int $inv Obligatoire, identifiant d'une facture
 *	@return bool False si la facture n'existe pas, sinon l'identifiant du compte client
 */
function ord_invoices_get_user( $inv ){
	if( !is_numeric($inv) || $inv<=0 ) return false;
	global $config;

	$sql = '
		select inv_usr_id as user
		from ord_invoices
		where inv_tnt_id='.$config['tnt_id'].'
			and inv_id='.$inv.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'user' );
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'une facture.
 *	@param int $id Identifiant ou tableau d'identifiants de facture.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_invoices_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update ord_invoices
		set inv_date_modified = now()
		where inv_tnt_id = '.$config['tnt_id'].'
			and inv_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'une ligne de facture
 *	@param int $inv Obligatoire, Identifiant interne de la facture
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Numéro de ligne du produit (permet d'avoir plusieurs fois le même produit sur plusieurs lignes différentes)
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $price Obligatoire, Tarif HT du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param $tva Obligatoire, Taux de tva, sous la forme 1.x (ex: 1.196)
 *	@param int $ord Obligatoire, Identifiant de la commande à laquelle s'applique la ligne de facture
 *	@param float $price_ttc Optionnel, Tarif TTC du produit
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *	@param $purchase_avg Optionnel, Prix d'achat moyen de du produit
 *	@param $update_totals Optionnel, Permet de ne pas recalculé les totaux
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_inv_products_add_sage( $inv, $prd, $line, $ref, $name, $price, $qte, $tva, $ord, $price_ttc=null, $ecotaxe=0, $purchase_avg=null, $update_totals=true ){

	if( !ord_invoices_exists( $inv, 0, true ) ){
		return false;
	}
	if( !prd_products_exists( $prd ) ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}
	if( !trim($ref) ){
		return false;
	}

	if( !trim($name) ){
		$name = $ref;
	}

	$price = str_replace(array(' ', ','), array('', '.'), $price);
	$tva = str_replace(array(' ', ','), array('', '.'), $tva);

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	$ecotaxe = str_replace(array(' ', ','), array('', '.'), $ecotaxe);

	if( !is_numeric($price) ){
		return false;
	}
	if( !is_numeric($tva) ){
		return false;
	}
	if( !is_numeric($qte) ){
		return false;
	}
	if( !is_numeric($ecotaxe) ){
		return false;
	}

	if( !is_numeric($ord) || !$ord ){
		$ord = 'null';
	}elseif( !ord_orders_exists( $ord ) ){
		return false;
	}

	$fields = array('prd_tnt_id', 'prd_inv_id', 'prd_id', 'prd_line_id', 'prd_ref', 'prd_name', 'prd_price_ht', 'prd_qte', 'prd_tva_rate', 'prd_ord_id', 'prd_ecotaxe');
	$values = array($config['tnt_id'], $inv, $prd, $line, '"'.addslashes($ref).'"', '"'.addslashes($name).'"', $price, $qte, $tva, $ord, $ecotaxe);

	if( $price_ttc !== null ){
		$price_ttc = str_replace(array(',', ' '), array('.', ''), $price_ttc);
		if( is_numeric($price_ttc) ){
			$fields[] = 'prd_price_ttc';
			$values[] = $price_ttc;
		}
	}

	if( $purchase_avg!==null ){
		$purchase_avg = str_replace( array(',',' '), array('.',''), $purchase_avg );
		if( is_numeric($purchase_avg) ){
			$fields[] = 'prd_purchase_avg';
			$values[] = $purchase_avg;
		}
	}

	$sql = '
		insert into ord_inv_products
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';

	$res = ria_mysql_query($sql);

	// supprime une ligne précédente puis retente
	if( !$res && ord_inv_products_exists( $inv, $prd, $line ) ){
		ord_inv_products_del( $inv, $prd, $line );
		$res = ria_mysql_query($sql);
	}

	// Met à jour les totaux pour la commande
	if( $res ){
		if( $update_totals ){
			ord_invoices_update_totals( $inv );
		}
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'une ligne de facture
 *	@param int $inv Obligatoire, Identifiant interne de la facture
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Numéro de ligne du produit (permet d'avoir plusieurs fois le même produit sur plusieurs lignes différentes)
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $price Obligatoire, Tarif HT du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param $tva Obligatoire, Taux de tva, sous la forme 1.x (ex: 1.196)
 *	@param int $ord Obligatoire, Identifiant de la commande à laquelle s'applique la ligne de facture
 *	@param float $price_ttc Optionnel, Tarif TTC du produit
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *	@param $purchase_avg Optionnel, Prix d'achat moyen de du produit
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_inv_products_add( $inv, $prd, $line, $ref, $name, $price, $qte, $tva, $ord, $price_ttc=null, $ecotaxe=0, $purchase_avg=null ){

	if( !ord_invoices_exists( $inv, 0, true ) ){
		return false;
	}
	if( !prd_products_exists( $prd ) ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}
	if( !trim($ref) ){
		return false;
	}

	if( !trim($name) ){
		$name = $ref;
	}

	$price = str_replace(array(' ', ','), array('', '.'), $price);
	$tva = str_replace(array(' ', ','), array('', '.'), $tva);

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	$ecotaxe = str_replace(array(' ', ','), array('', '.'), $ecotaxe);

	if( !is_numeric($price) ){
		return false;
	}
	if( !is_numeric($tva) ){
		return false;
	}
	if( !is_numeric($qte) ){
		return false;
	}
	if( !is_numeric($ecotaxe) ){
		return false;
	}

	if( !is_numeric($ord) || !$ord ){
		$ord = 'null';
	}elseif( !ord_orders_exists( $ord ) ){
		return false;
	}

	$fields = array('prd_tnt_id', 'prd_inv_id', 'prd_id', 'prd_line_id', 'prd_ref', 'prd_name', 'prd_price_ht', 'prd_qte', 'prd_tva_rate', 'prd_ord_id', 'prd_ecotaxe');
	$values = array($config['tnt_id'], $inv, $prd, $line, '"'.addslashes($ref).'"', '"'.addslashes($name).'"', $price, $qte, $tva, $ord, $ecotaxe);

	if( $price_ttc !== null ){
		$price_ttc = str_replace(array(',', ' '), array('.', ''), $price_ttc);
		if( is_numeric($price_ttc) ){
			$fields[] = 'prd_price_ttc';
			$values[] = $price_ttc;
		}
	}

	if( $purchase_avg!==null ){
		$purchase_avg = str_replace( array(',',' '), array('.',''), $purchase_avg );
		if( is_numeric($purchase_avg) ){
			$fields[] = 'prd_purchase_avg';
			$values[] = $purchase_avg;
		}
	}

	$sql = '
		insert into ord_inv_products
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';

	$res = ria_mysql_query($sql);

	// supprime une ligne précédente puis retente
	if( !$res && ord_inv_products_exists( $inv, $prd, $line ) ){
		ord_inv_products_del( $inv, $prd, $line );
		$res = ria_mysql_query($sql);
	}

	// Met à jour les totaux pour la commande
	if( $res ){
		ord_invoices_update_totals( $inv );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une ligne de facture
 *	@param int $inv Identifiant de la facture à modifier
 *	@param int $prd Facultatif, Identifiant du produit
 *	@param $line Facultatif, Identifiant de la ligne
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_inv_products_del( $inv, $prd=false, $line=false ){

	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( $prd !== false && (!is_numeric($prd) || $prd <= 0) ){
		return false;
	}
	if( $line !== false && (!is_numeric($line) || $line < 0) ){
		return false;
	}

	global $config;

	$sql = '
		delete from ord_inv_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_inv_id = '.$inv;

	if($prd !== false){
		$sql .= ' and prd_id='.$prd;
	}
	if($line !== false){
		$sql .= ' and prd_line_id='.$line;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		ord_invoices_update_totals( $inv );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la mise d'une ligne de facture importée depuis la gestion commerciale SAGE.
 *
 *	@param int $inv Obligatoire, Identifiant interne de la facture
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Numéro de ligne (permet d'avoir plusieurs fois le même produit sur des lignes différentes).
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix unitaire HT du produit (remises comprises)
 *	@param $tva Obligatoire, Taux de tva à appliquer à la ligne
 *	@param int $ord Obligatoire, Identifiant de la commande d'origine
 *	@param float $price_ttc Optionnel, Prix unitaire TTC du produit (remises comprises)
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *	@param $purchase_avg Optionnel, Prix d'achat moyen de la ligne
 *	@param $update_totals Optionnel, permet de ne pas recalculer les totaux
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_inv_products_update_sage( $inv, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $price_ttc=null, $ecotaxe=0, $purchase_avg=null, $update_totals=true){

	if( !ord_invoices_exists( $inv, 0, true ) ){
		return false;
	}
	if( !prd_products_exists( $prd ) ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	$price_ht = str_replace(array(',', ' '), array('.', ''), $price_ht);
	$tva = str_replace(array(',', ' '), array('.', ''), $tva);
	$ecotaxe = str_replace(array(',', ' '), array('.', ''), $ecotaxe);

	if( !is_numeric($qte) ){
		return false;
	}
	if( !is_numeric($price_ht) ){
		return false;
	}
	if( !is_numeric($tva) ){
		return false;
	}
	if( !is_numeric($ecotaxe) ){
		return false;
	}

	if( !is_numeric($ord) || !$ord ){
		$ord = 'null';
	}elseif( !ord_orders_exists( $ord ) ){
		return false;
	}

	// Ajout optionnel du montant TTC
	$sql_price_ttc = '';
	if( $price_ttc !== null ){
		$price_ttc = str_replace(array(',', ' '), array('.', ''), $price_ttc);
		if( is_numeric($price_ttc) ){
			$sql_price_ttc = ', prd_price_ttc = '.$price_ttc;
		}
	}

	if( $purchase_avg!==null ){
		$purchase_avg = str_replace( array(',',' '), array('.',''), $purchase_avg );
	}

	$sql = '
		update
			ord_inv_products
		set
			prd_ref = "'.addslashes($ref).'",
			prd_name = "'.addslashes($name).'",
			prd_qte = '.$qte.',
			prd_price_ht = '.$price_ht.',
			prd_tva_rate = '.$tva.',
			prd_ord_id = '.$ord.',
			prd_ecotaxe = '.$ecotaxe.',
			prd_purchase_avg = '.(is_numeric($purchase_avg) ? $purchase_avg : 'null').'
			'.$sql_price_ttc.'
		where
			prd_tnt_id = '.$config['tnt_id'].'
			and prd_inv_id = '.$inv.'
			and prd_id = '.$prd.'
			and prd_line_id = '.$line.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		if( $update_totals ){
			ord_invoices_update_totals( $inv );
		}
	}

	return $res;

}
// \endcond

/** Retourne tous les produits contenus dans une facture.
 *
 *	@param int|array $inv Obligatoire, Identifiant de la facture (ou tableau d'identifiants)
 *	@param $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par date de création de la ligne de commande. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : ref, ps-ref, name, date, price, qte, total. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param $supplier Optionnel, identifiant d'un fournisseur sur lequel le tri par référence fournisseur sera appliqué
 *	@param $colis Optionnel, identifiant de colis sur lequel filtrer le resultat
 *	@param $bl_piece Optionnel, identifiant de de bon de livraison sur lequel filtrer le resultat
 *	@param $order Optionnel, identifiant de commande sur lequel filtrer le resultat
 * 	@param $ord_null Optionnel, True pour renvoyer les lignes sans commande, false pour renvoyer les lignes avec commande, "bl" pour renvoyer les lignes sans commande OU sans bl (facture Bigship)
 *	@param int $prd Optionnel, identifiant de produit ou tableau d'identifiants
 *	@param $line Optionnel, numéro de ligne ou tableau de numéros de lignes
 *	@param bool $correled Optionnel, si activé et $inv, $prd et $line des tableaux de même taille, teste un à un les triplets "prd_inv_id / prd_id / prd_line_id"
 *	@param int $usr_id Optionnel, identifiant de compte client
 *	@param $fld Optionnel, filtrage habituel par champ avancé
 *	@param $or_between_val Optionnel, opérateur logique entre les valeurs d'un champ
 *	@param $or_between_fld Optionnel, opérateur logique entre les champs
 *	@param string $lng Optionnel, langue des champs
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- id : Identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- qte : quantité commandée
 *			- price_ht : prix ht du produit à l'unité
 *			- price_ttc : prix ttc du produit à l'unité
 *			- total_ht : sous total pour le produit, hors taxes (price_ht*qte)
 *			- total_ttc : sous total pour le produit, ttc (price_ttc*qte)
 *			- ecotaxe : Eco-participation unitaire
 *			- sell_weight : Détermine si la quantité est un poids ou un volume (si Oui, dans la plus petite unité possible)
 *			- weight_net : Poids total de la ligne
 *			- line : Numéro de ligne
 * 			- group_id : L'identifiant du groupe.
 * 			- group_parent_id : L'identifiant du parent du groupe.
 *			- orderable : Détermine si le produit est commandable
 *			- tva_rate : taux de TVA de la ligne
 *			- ord_id : Identifiant de la commande d'origine
 *			- img_id : Identifiant de l'image principale du produit
 *			- inv_id : Numéro de facture
 *			- purchase_avg : Prix d'achat moyen du produit
 *			- seller_id : identifiant du commercial
 *			- pos : position de la ligne
 *			- price_brut_ht : prix brut de la ligne
 */
function ord_inv_products_get( $inv, $sort=false, $supplier=false, $colis=null, $bl_piece=null, $order=null, $ord_null=null, $prd=0, $line=null, $correled=false, $usr_id=0, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false ){

	// $inv est supposé être obligatoire mais 0 ou array() est autorisé
	$inv = control_array_integer( $inv, false );
	if( $inv === false ){
		return false;
	}

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( $line === null ){
		$line = array();
	}
	$line = control_array_integer( $line, false, true );
	if( $line === false ){
		return false;
	}

	if( $correled && ( sizeof($inv) != sizeof($prd) || sizeof($inv) != sizeof($line) ) ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( !is_array($sort) ){
		$sort = array('ref' => 'asc');
	}

	global $config;

	$sql = '
		select
			i.prd_id as id, i.prd_ref as ref, i.prd_line_id as line, i.prd_group_id as group_id, i.prd_group_parent_id as group_parent_id, i.prd_name as name, '.($config['use_decimal_qte'] ? 'i.prd_qte' : 'cast(i.prd_qte as signed)' ).' as qte, prd_ord_id as ord_id,
			i.prd_price_ht as price_ht, ifnull(i.prd_price_ttc, i.prd_price_ht * i.prd_tva_rate) as price_ttc, i.prd_tva_rate as tva_rate,
			i.prd_price_ht * i.prd_qte as total_ht, ifnull(i.prd_price_ttc, i.prd_price_ht * i.prd_tva_rate) * i.prd_qte as total_ttc,
			p.prd_img_id as img_id, i.prd_ecotaxe as ecotaxe, ifnull(p.prd_sell_weight, 0) as sell_weight, ifnull(i.prd_weight, "0") as weight_net, ifnull(prd_orderable, 0) as orderable, i.prd_inv_id as inv_id,
			if(ifnull(prd_title, "")="", p.prd_name, prd_title) as title, if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr, i.prd_purchase_avg as purchase_avg, i.prd_seller_id as seller_id, i.prd_pos as "pos", prd_price_brut_ht as price_brut_ht
		from
			ord_inv_products as i
			left join prd_products as p on
				i.prd_tnt_id = p.prd_tnt_id and i.prd_id = p.prd_id

	';
	if( isset($sort['ps-ref']) ){
		$sql .= '
			left join prd_suppliers on
				i.prd_tnt_id = ps_tnt_id and i.prd_id = ps_prd_id and '.( $supplier === false ? 'ps_main != 0' : 'ps_usr_id = '.$supplier ).'
		';
	}

	$sql .= '
		where i.prd_tnt_id='.$config['tnt_id'].'
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($inv); $i++ ){
			$cnds[] = 'i.prd_inv_id = '.$inv[ $i ].' and i.prd_id = '.$prd[ $i ].' and i.prd_line_id = '.$line[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($inv) ){
			$sql .= ' and i.prd_inv_id in ('.implode(', ', $inv).')';
		}
		if( sizeof($prd) ){
			$sql .= ' and i.prd_id in ('.implode(', ', $prd).')';
		}
		if( sizeof($line) ){
			$sql .= ' and i.prd_line_id in ('.implode(', ', $line).')';
		}
	}

	if( $colis !== null ){
		$sql .= '
			and exists (
				select 1 from ord_bl_products as bl
				where bl.prd_tnt_id = '.$config['tnt_id'].' and bl.prd_id = i.prd_id
				and bl.prd_ord_id = i.prd_ord_id and bl.prd_line_id = i.prd_line_id
				and prd_colis="'.addslashes($colis).'"
			)
		';
	}

	if( $bl_piece !== null ){
		$sql .= '
			and exists (
				select 1 from ord_bl_products as bl
				join ord_bl on bl.prd_tnt_id = bl_tnt_id and prd_bl_id = bl_id
				where bl.prd_tnt_id = '.$config['tnt_id'].'
					and bl.prd_id = i.prd_id
					and bl.prd_ord_id = i.prd_ord_id
					and bl.prd_line_id = i.prd_line_id
					and bl_masked = 0
					and bl_piece = "'.addslashes($bl_piece).'"
			)
		';
	}

	if( is_numeric($order) && $order >= 0 ){
		$sql .= ' and ifnull(i.prd_ord_id, 0) = '.$order;
	}

	if( $ord_null === true ){
		$sql .= ' and i.prd_ord_id is null';
	}elseif( $ord_null === false ){
		$sql .= ' and i.prd_ord_id is not null';
	}elseif( $ord_null == 'bl' ){
		$sql .= '
			and (
				i.prd_ord_id is null or not exists (
					select 1 from ord_bl_products as bl
					where bl.prd_tnt_id = '.$config['tnt_id'].' and bl.prd_id = i.prd_id
					and bl.prd_ord_id = i.prd_ord_id and bl.prd_line_id = i.prd_line_id
				)
			)
		';
	}

	if( $usr_id > 0 ){
		$sql .= ' and exists (
			select 1 from ord_invoices
			where inv_tnt_id = '.$config['tnt_id'].' and inv_id = i.prd_inv_id
			and inv_usr_id = '.$usr_id.' and inv_masked = 0
		)';
	}

	$sql .= fld_classes_sql_get( CLS_INV_PRODUCT, $fld, $or_between_val, $or_between_fld, $lng, 'i' );

	$sort_final = array();
	foreach( $sort as $col => $dir ){
		$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
		switch( strtolower(trim($col)) ){
			case 'ref':
				$sort_final[] = 'i.prd_ref '.$dir;
				break;
			case 'ps-ref':
				$sort_final[] = 'i.ps_ref '.$dir;
				break;
			case 'name':
				$sort_final[] = 'i.prd_name '.$dir;
				break;
			case 'qte':
				$sort_final[] = 'i.prd_qte '.$dir;
				break;
			case 'price':
				$sort_final[] = 'i.prd_price_ht '.$dir;
				break;
			case 'total':
				$sort_final[] = 'i.prd_price_ht*prd_qte '.$dir;
				break;
			case 'pos':
				$sort_final[] = 'i.prd_pos '.$dir;
				break;
		}
	}

	if( !sizeof($sort_final) ){
		$sort_final[] = 'i.prd_ref asc';
	}

	$sql .= ' order by '.implode(', ', $sort_final);

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

// \cond onlyria
/**
 * Cette fonction permet de récupérer le dernière prix facturé pour une utilisateur
 * @param int $usr_id Obligatoire, identifiant de l'utilisateur
 * @param int $prd_id Obligatoire, identifiant d'un produit
 * @return array un résultat sous forme de FdvContentvalues
 */
function ord_inv_products_get_last_user_sold_price($usr_id, $prd_id) {

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$sql = "
		select sub_ip.prd_price_ht as price_ht,
			sub_ip.prd_tva_rate as tva_rate,
			sub_ip.prd_price_ttc as price_ttc,
			sub_ip.prd_ecotaxe as ecotaxe,
			sub_ip.prd_price_brut_ht as price_brut_ht,
			sub_i.inv_id as inv_id
		from ord_inv_products sub_ip
		join ord_invoices sub_i on (sub_ip.prd_tnt_id = sub_i.inv_tnt_id and sub_ip.prd_inv_id=sub_i.inv_id)
		where sub_ip.prd_tnt_id = ".$config['tnt_id']." and sub_i.inv_usr_id=" . $usr_id . " and sub_ip.prd_id=" . $prd_id . "
		order by sub_i.inv_date desc
		limit 0,1
	";

	$r = ria_mysql_query($sql);

	if (!$r || !ria_mysql_num_rows($r)) {
		return false;
	}

	return ria_mysql_fetch_assoc($r);
}


/**	Vérifie l'existance d'un produit dans une facture, par son identifiant
 *	@param int $inv Identifiant de la facture à tester
 *	@param int $prd Identifiant du produit à tester
 *	@param $line Numéro de la ligne à tester. Si false (par défaut), ce paramètre n'est pas pris en compte
 *	@return bool true si le produit est présent dans la facture
 *	@return bool false si le produit n'existe pas dans la facture, ou si une erreur s'est produite
 */
function ord_inv_products_exists( $inv, $prd, $line=false ){

	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( !prd_products_exists( $prd ) ){
		return false;
	}
	if( $line !== false && ( !is_numeric($line) || $line < 0 ) ){
		return false;
	}

	global $config;

	$sql = '
		select prd_id from ord_inv_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_inv_id = '.$inv.'
			and prd_id = '.$prd.'
	';

	if( $line !== false ){
		$sql .= ' and prd_line_id = '.$line;
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un couple produit / quantité existe pour une facture donnée.
 *	@param int $inv Identifiant de la facture
 *	@param int $prd Identifiant du produit
 *	@param $qte Quantité
 *
 *	@return bool True si une ou des lignes existent, False sinon
 */
function ord_inv_products_exists_qte( $inv, $prd, $qte ){
	if( !is_numeric($inv) || $inv <= 0 ){
		return false;
	}
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) || !$qte ){
		return false;
	}

	$sql = '
		select 1
		from ord_inv_products
		where
			prd_tnt_id = '.$config['tnt_id'].' and prd_inv_id = '.$inv.'
			and prd_id = '.$prd.' and prd_qte = '.$qte.'
	';

	if( !( $r = ria_mysql_query($sql) ) ){
		return false;
	}

	return ria_mysql_num_rows($r);

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le commercial sur une ligne de facture
 *	Attention pas de controle sur le numéro du commercial donnée (fait par la synchro)
 *	@param int $inv_id Identifiant de la facture
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant de ligne
 *	@param int $seller_id Identifiant du commercial
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_inv_products_set_seller( $inv_id, $prd_id, $line_id, $seller_id ){
	if( !is_numeric($inv_id) || $inv_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($line_id) || $line_id<0 ){
		return false;
	}
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	if( $seller_id == 0 ){
		$seller_id = 'null';
	}

	global $config;

	$res = ria_mysql_query( 'update ord_inv_products set prd_seller_id='.$seller_id.' where prd_inv_id='.$inv_id.' and prd_id='.$prd_id.' and prd_line_id='.$line_id.' and prd_tnt_id='.$config['tnt_id'] );

	if( $res ){
		// mise à jour de la date de modification de la commande pour forcer une mise à jour des tablettes
		ord_invoices_set_date_modified($inv_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le prix brut sur les lignes de facture
 *	@param int $inv_id Identifiant de la facture
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant de ligne
 *	@param $price_brut_ht prix brut de la ligne
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_inv_products_set_price_brut( $inv_id, $prd_id, $line_id, $price_brut_ht ){
	if( !is_numeric($inv_id) || $inv_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($line_id) || $line_id<0 ){
		return false;
	}
	if( !is_numeric($price_brut_ht) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query( 'update ord_inv_products set prd_price_brut_ht='.$price_brut_ht.' where prd_inv_id='.$inv_id.' and prd_id='.$prd_id.' and prd_line_id='.$line_id.' and prd_tnt_id='.$config['tnt_id'] );

	if( $res ){
		// mise à jour de la date de modification de la facture
		ord_invoices_set_date_modified($inv_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le poids net sur les lignes de facture
 *	@param int $inv_id Identifiant de la facture
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant de ligne
 *	@param $weight poids net de la ligne en gramme
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_inv_products_set_weight( $inv_id, $prd_id, $line_id, $weight ){
	if( !is_numeric($inv_id) || $inv_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($line_id) || $line_id<0 ){
		return false;
	}
	if( !is_numeric($weight) || $weight<0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query( 'update ord_inv_products set prd_weight='.$weight.' where prd_inv_id='.$inv_id.' and prd_id='.$prd_id.' and prd_line_id='.$line_id.' and prd_tnt_id='.$config['tnt_id'] );

	if( $res ){
		// mise à jour de la date de modification de la facture
		ord_invoices_set_date_modified($inv_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le trie sur les lignes de facture
 *	@param int $inv_id Identifiant de la facture
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant de ligne
 *	@param int $pos position de la ligne
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_inv_products_set_pos( $inv_id, $prd_id, $line_id, $pos ){
	if( !is_numeric($inv_id) || $inv_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($line_id) || $line_id<0 ){
		return false;
	}
	if( !is_numeric($pos) || $pos<0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query( 'update ord_inv_products set prd_pos='.$pos.' where prd_inv_id='.$inv_id.' and prd_id='.$prd_id.' and prd_line_id='.$line_id.' and prd_tnt_id='.$config['tnt_id'] );

	if( $res ){
		// mise à jour de la date de modification de la facture
		ord_invoices_set_date_modified($inv_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le prix d'achat sur les lignes de facture
 *	@param int $inv_id Identifiant de la facture
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant de ligne
 *	@param $purchase_avg prix d'achat moyen de la ligne
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_inv_products_set_purchase_avg( $inv_id, $prd_id, $line_id, $purchase_avg ){
	if( !is_numeric($inv_id) || $inv_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($line_id) || $line_id<0 ){
		return false;
	}

	$purchase_avg = str_replace(array(',', ' '), array('.', ''), $purchase_avg);
	if( !is_numeric($purchase_avg) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query( 'update ord_inv_products set prd_purchase_avg='.$purchase_avg.' where prd_inv_id='.$inv_id.' and prd_id='.$prd_id.' and prd_line_id='.$line_id.' and prd_tnt_id='.$config['tnt_id'] );

	if( $res ){
		// mise à jour de la date de modification de la facture
		ord_invoices_set_date_modified($inv_id);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Détermine le montant total, la marge, le montant moyen des commandes facturées
 *	@param string $date_start Optionnel, date de début de la période
 *	@param string $date_end Optionnel, date de fin de la période
 *	@param int $inv_id Optionnel, identifiant d'une facture sur laquelle filtrer le résultat
 *	@param $users Facultatif, permet de filtrer les factures sur un ou plusieurs utilisateurs, accepte un tableau d'identifiants, attention dans le cas d'utilisateur parent il n'ira pas chercher les utilisateurs enfants.
 *	@param $min Facultatif, minimum de facture sous cette forme array('amount'=>minimum, 'is_ttc'=>true/false)
 *	@param int $seller_id Facultatif, identifiant du représentant de la ou des facture(s)
 *	@param bool $by_user Optionnel, permet de regrouper les statistiques par client
 *	@param $use_relation_sellers Optionnel, par défaut à false donc seule les pièces liées directement au représentant sont comptabilitées, mettre true pour utiliser aussi les relations (uniquement si $seller_id est renseigné)
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau comprenant les colonnes suivantes :
 *				- volume : nombre de commande (Web)
 *				- total_ht : total ht des commandes
 *				- total ttc : total ttc des commandes
 *				- average_ht : panier moyen en ht
 *				- average_ttc : panier moyen en ttc
 *				- marge : marge brute
 *				- usr_id : identifiant du compte client (uniquement si $by_user est activé)
 */
function ord_invoices_get_average_totals( $date_start=false, $date_end=false,  $inv_id=0, $users=false, $min=0, $seller_id=0, $by_user=false, $use_relation_sellers=false ){

	{ // contrôles

		if( $date_start !== false && !isdateheure($date_start) ){
			return false;
		}

		if( $date_end !== false && !isdateheure($date_end) ){
			return false;
		}

		if( is_array($inv_id) ){
			foreach( $inv_id as $one_id ){
				if( !is_numeric($one_id) || $one_id <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($inv_id) && $inv_id > 0 ){
			$inv_id = array($inv_id);
		}else{
			$inv_id = array();
		}


		if( is_array($users) ){
			foreach( $users as $one_usr ){
				if( !is_numeric($one_usr) || $one_usr <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($users) && $users > 0 ){
			$users = array($users);
		}else{
			$users = array();
		}


		if( is_array($seller_id) ){
			foreach( $seller_id as $one_sll ){
				if( !is_numeric($one_sll) || $one_sll <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($seller_id) && $seller_id > 0 ){
			$seller_id = array($seller_id);
		}else{
			$seller_id = array();
		}

	}

	global $config;

	$sql = '
		select
			count(*) as volume,
			sum(inv_total_ht) as total_ht,
			sum(inv_total_ttc) as total_ttc,
			sum(inv_total_ht) / count(*) as average_ht,
			sum(inv_total_ttc) / count(*) as average_ttc,
			sum(marge) as marge, inv_usr_id as usr_id
		from (
			select t1.inv_id, t1.inv_total_ht, t1.inv_total_ttc, sum(marge) as marge, inv_usr_id
			from (
				select
					inv_id, inv_total_ht, inv_total_ttc, inv_usr_id,
					if(ifnull(oip.prd_purchase_avg, ifnull(p.prd_purchase_avg, 0)) = 0, 0,(oip.prd_price_ht - ifnull(oip.prd_purchase_avg, ifnull(p.prd_purchase_avg, 0))) * oip.prd_qte) as marge
				from
					ord_inv_products as oip
					join prd_products as p on oip.prd_id = p.prd_id and oip.prd_tnt_id = p.prd_tnt_id
					join ord_invoices on oip.prd_tnt_id = inv_tnt_id and oip.prd_inv_id = inv_id
					join gu_users on inv_usr_id = usr_id and inv_tnt_id = usr_tnt_id

	';


	$sql .= '
				where
					inv_tnt_id = '.$config['tnt_id'].'
					and inv_masked = 0
					and usr_date_deleted is null
					and prd_date_deleted is null
	';

	if( sizeof($seller_id) ){
		$sql .= '
			and (
				usr_seller_id in ('.implode(', ', $seller_id).')
		';

		if( $use_relation_sellers ){
			// On regarde aussi les relations du client de la facture avec ses représentant
			$sql .= '
				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 in ('.implode(', ', $seller_id).')
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = inv_usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)
			';
		}

		$sql .= '
			)
		';
	}

	if( sizeof($users) ){
		$sql .= ' and inv_usr_id in ( '.implode(', ',$users).' )';
	}

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(inv_date) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and inv_date >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(inv_date) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and inv_date <= "'.dateheureparse($date_end).'"';
		}
	}

	if( sizeof($inv_id) ){
		$sql .= ' and inv_id in ('.implode(', ', $inv_id).')';
	}

	$sql .= '
			) t1
	';

	$sql .= ' where 1 ';

	if( is_array($min) && isset($min['amount'], $min['is_ttc']) ){
		$amount_min = str_replace(array(',', ' '), array('.', ''), $min['amount']);
		if( is_numeric($amount_min) ){
			$sql .= ' and t1.inv_total_'.( $min['is_ttc'] ? 'ttc' : 'ht' ).' >= '.$amount_min;
		}
	}

	$sql .= '
			group by '.( $by_user ? 't1.inv_usr_id' : 't1.inv_id' ).'
		) t2
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return ria_mysql_fetch_assoc($res);

}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour toute les factures.
 *
 */
function ord_invoices_index_rebuild(){
	global $config;

	$r_invoices = ria_mysql_query('
		select inv_id
		from ord_invoices
		where inv_tnt_id='.$config['tnt_id'].'
	');

	while( $invoice = ria_mysql_fetch_array($r_invoices) ) {
		ord_invoices_index($invoice['inv_id']);
	}
}
// \endcond

// \cond onlyria
/** Indexe une facure pour qu'elle puisse apparaître dans le moteur de recherche de l'interface d'administration
 *  Si la facture était déjà indexé, cette fonction rafraîchit simplement son entrée.
 *
 * 	@param int $id Obligatoire, Identifiant de la facture
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_invoices_index( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$r_invoices = ord_invoices_get( $id );

	// Si aucune facture récupérer, la fonction d'indexation retournera faux
	if( !$r_invoices || !ria_mysql_num_rows($r_invoices) ){
		return false;
	}

	$invoice = ria_mysql_fetch_assoc( $r_invoices );

	$url = '/admin/orders/invoice.php?inv='.$invoice['id'];

	// Le titre du résultat sera Pièce n°XXX du dd/mm/yy à 00:00
	$name = sprintf( _('Pièce n°%d du %s'), str_pad( $invoice['id'], 8, '0', STR_PAD_LEFT ), ria_date_format($invoice['date']));

	// La description sera Numéro de pièce : XXXXX
	$desc = _('Numéro de pièce : ').$invoice['piece'];

	// Le contenu indéxé
	$contents = $invoice['piece'].' '.$invoice['ref'];

	$cid = search_index_content( $url, 'inv', $name, $desc, $contents, $url, 0, $invoice['id'] );


	return ria_mysql_query('
		update ord_invoices
		set inv_cnt_id='.$cid.'
		where inv_tnt_id='.$config['tnt_id'].'
			and inv_id='.$invoice['id']
	);
}

// \endcond


// \endcond

// \cond onlyria
/** Modifie le group_id et le group_parent_id pour la ligne de la facture donnée.
 *
 * @param int $inv L'identifiant de la facture
 * @param int $prd Identifiant du produit
 * @param int $line L'identifiant de la ligne
 * @param int $group_id L'identifiant du groupe. Si NULL la valeur sera supprimée
 * @param int $group_parent_id  Optionnel. L'identifiant du parent du groupe. Si NULL la valeur sera supprimée
 * @return bool true si succès, false autrement
 */
function ord_inv_products_set_group_id( $inv, $prd, $line, $group_id=false, $group_parent_id=false ){

	if(
		(!is_numeric($inv) || $inv <= 0) ||
		(!is_numeric($prd) || $prd < 0) ||
		(!is_numeric($line) || $line < 0) ||
		($group_id===false || ($group_id!==null && !is_numeric($group_id))) ||
		($group_parent_id!==false && $group_parent_id!==null && !is_numeric($group_parent_id))
	){
		return false;
	}

	global $config;

	$sql = '
		update ord_inv_products
		set	prd_group_id='. ( $group_id!==null ? $group_id : 'null' ).'
		'.( $group_parent_id!==false ? ',prd_group_parent_id='. ( $group_parent_id!==null ? $group_parent_id : 'null' ) : '' ).'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_inv_id = '.$inv.'
			and prd_id = '.$prd.'
			and prd_line_id = '.$line.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return true;
}
// \endcond

/// @}


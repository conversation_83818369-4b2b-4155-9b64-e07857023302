<?php
/** \defgroup PriceWatchingModel Modèle
 * 	@deprecated Ce module ne sera pas porté vers la nouvelle interface d'administration et ne dois pas
 * 	être mis à disposition dans la nouvelle API. Seules les fonctions de relevé de linéaires pourront l'être.
 *	\ingroup PriceWatching
 *	\warning Ce module possède un schéma en commun avec le relevé de linéaire mais prend en compte l'identifiant de concurrent (cpt_id)
 * @{
 */

/** \class prw_offers
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_offers
 *
 * Cette classe permet de récupérer les lignes d'offres liés à la veille tarifaire donc que des lignes avec un identifiant de concurrent (cpt_id).
 * Si l'identifiant de concurrent (cpt_id) est égal à 0 ce n'est pas une ligne liées à la veille tarifaire mais au relevé linéaire
 *
 * @see Riashop::PriceWatching::models::LinearRaised::prw_offers prw_offers.php
 */
class prw_offers
{
	/**
	 * @var prw_offers Instance de prw_offers pour la tâche cron
	 */
	private static $_instance = null;

	/**
	 * Pattern de singleton pour optimiser la mémoire lors de l'exécution de la tâche cron
	 * @todo Cette classe n'a aucun attribut hormis $_instance, le pattern n'est pas utile. A refactorer.
	 * @return null|prw_offers
	 */
	public static function getInstance()
	{
		if( is_null( self::$_instance ) ){
			self::$_instance = new prw_offers();
		}

		return self::$_instance;
	}

	/**	Cette fonction permet d'ajouter une ligne dans la table prw_offers
	 *
	 *	@param $landedprice   Prix TTC du produit
	 *	@param $shippingprice Prix de livraison du produit
	 *	@param $url           URL d'un site concurrent
	 *	@param $cpt_id        Identifiant d'un concurrent
	 *	@param $prd_id        Identifiant d'un produit
	 *	@param $promoprice    Facultatif, prix ttc en promotion du produit
	 *
	 *	@return bool|int    retourne false si erreur, sinon l'identifant de l'offre créée
	 */
	public function prw_offers_add( $landedprice, $shippingprice, $url, $cpt_id, $prd_id, $promoprice=null )
	{
		global $config;

		if( !is_numeric( $landedprice ) || $landedprice < 0 ){
			return false;
		}
		if( !is_numeric( $shippingprice ) || $shippingprice < 0) {
			return false;
		}
		if( !is_numeric( $cpt_id ) || $cpt_id < 0) {
			return false;
		}
		if( !is_numeric( $prd_id ) || $prd_id < 0){
			return false;
		}
		if( !is_string( $url ) ){
			return false;
		}
		if( !is_null($promoprice) && (!is_numeric( $promoprice ) || $promoprice < 0) ){
			return false;
		}

		$url = addslashes( ucfirst( trim( $url ) ) );

		$fields = array();
		$values = array();

		$fields[] = 'ofr_cpt_id';
		$values[] = $cpt_id;

		$fields[] = 'ofr_prd_id';
		$values[] = $prd_id;

		$fields[] = 'ofr_tnt_id';
		$values[] = $config['tnt_id'];

		$fields[] = 'ofr_landedprice';
		$values[] = $landedprice;

		$fields[] = 'ofr_shippingprice';
		$values[] = $shippingprice;

		if( !is_null($promoprice) ){
			$fields[] = 'ofr_promo_price';
			$values[] = $promoprice;
		}

		$fields[] = 'ofr_url';
		$values[] = '"'.$url.'"';

		$fields[] = 'ofr_date_created';
		$values[] = 'now()';

		$sql = '
			insert into prw_offers
        		('.implode(',', $fields).')
        	  values
        	  	('.implode(',', $values).')
		';

		$r = ria_mysql_query( $sql );

		if( !$r ){
			return false;
		}

		return  ria_mysql_insert_id();
	}

	/**	Cette fonction permet de récupérer une offre grâce à son identifiant
	 *
	 *	@param $ofrs_id    Tableau d'identifiant d'offres
	 *
	 *	@return array|bool    retourne false si erreur, sinon un tableau contenant chaque offres avec les clé suivante :
	 * 							- tnt_id : Identifiant du tenant
	 * 							- cpt_id : Identifiant du concurrent
	 * 							- prd_id : identifiant du produit
	 * 							- url : url du produit sur la plateforme du concurrent
	 * 							- ListingPrice : Prix ttc de l'offre
	 * 							- shippingprice : frais de port ttc de l'offre
	 * 							- promo_price : prix en promotion ttc de l'offre
	 * 							- created : date d'ajout de l'offre
	 */
	public function prw_offers_get_by_ids( array $ofrs_id )
	{
		if( !is_array( $ofrs_id ) || count( $ofrs_id ) < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select
			ofr_tnt_id as tnt_id,
			ofr_cpt_id as cpt_id,
			ofr_prd_id as prd_id,
			ofr_url as url,
			ofr_landedprice as ListingPrice,
			ofr_shippingprice as shippingprice,
			ofr_promo_price as promo_price,
			ofr_date_created as created
        from prw_offers
		where ofr_tnt_id='.$config['tnt_id'].'
			ofr_id in (' . implode( ',', $ofrs_id ) . ')';

		$r = ria_mysql_query( $sql );

		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		$rst = array();
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}

	/** Cette fonction permet à travers un tableau d'identifiants d'offres récupérer toutes les offres associé.
	 *
	 * @param $prd_id    Identifiant d'un produit
	 * @param $cpt_id    Identifiant d'un concurrent
	 *
	 * @return array|bool retourne false si erreur, sinon tableau associatif contenant les jeux de valeur suivante :
	 *                    - tnt_id :       Identifiant du tenant
	 *                    - cpt_id  :          Identifiant du concurrent
	 *                    - prd_id  :          Identifiant du produit
	 *                    - url     :           Url de la page produit chez le concurrent
	 *                    - ListingPrice  :      Tarif TTC chez le client
	 *                    - shippingprice  :  Tarif de livraison pour le concurrent
	 *                    - created : date d'ajout de l'offre
	 */
	public function prw_offers_get( $prd_id, $cpt_id )
	{
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select ofr_tnt_id as tnt_id,
				ofr_cpt_id as cpt_id,
				ofr_prd_id as prd_id,
				ofr_url as url,
				ofr_landedprice as ListingPrice,
				ofr_promo_price as promo_price,
				ofr_shippingprice as shippingprice,
			 	ofr_date_created as created
        from prw_offers
		where
			ofr_tnt_id='.$config['tnt_id'].'
			ofr_prd_id=' . $prd_id . '
			and ofr_cpt_id =' . $cpt_id;

		$r = ria_mysql_query( $sql );

		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		$rst = array();
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}

	/**	Cette fonction permet de récupérer la dernière offre créé
	 *
	 *	@param int $prd_id    Identifiant d'un produit
	 *	@param int $cpt_id    Identifiant d'un concurrent
	 *
	 *	@return array|bool    retourne false si erreur, sinon tableau associatif contenant les jeux de valeur suivante :
	 *                    - tnt_id        Identifiant du tenant
	 *                    - cpt_id            Identifiant du concurrent
	 *                    - prd_id            Identifiant du produit
	 *                    - url                Url de la page produit chez le concurrent
	 *                    - ListingPrice        Tarif TTC chez le client
	 *                    - shippingprice    Tarif de livraison pour le concurrent
	 *                    - promo_price    Tarif en promotion du client
	 *                    - created   Date de création de l'offre
	 */
	public function get_offer_getLast( $prd_id=0, $cpt_id=0 )
	{
		if( !is_numeric( $cpt_id ) || $cpt_id < 0){
			return false;
		}

		$prd_id = control_array_integer($prd_id, false);
		if ($prd_id === false) {
			return false;
		}

		global $config;

		$sql = '
			select prw_offers.ofr_tnt_id as tnt_id,
					prw_offers.ofr_cpt_id as cpt_id,
					prw_offers.ofr_prd_id as prd_id,
					prw_offers.ofr_url as url,
					prw_offers.ofr_landedprice as ListingPrice,
					prw_offers.ofr_shippingprice as shippingprice,
					prw_offers.ofr_promo_price as promo_price,
					prw_offers.ofr_date_created as created
			FROM
			(SELECT
				ofr_tnt_id as tnt_id, MAX(ofr_id) as id
			FROM
				prw_offers
				where ofr_tnt_id = '.$config['tnt_id'].'

		';

		if( count($prd_id) ){
			$sql .= '  and ofr_prd_id in ('.implode(', ', $prd_id).') ';
		}

		if( $cpt_id ){
			$sql .= ' and ofr_cpt_id = '.$cpt_id.' ';
		}else{
			$sql .= ' and ofr_cpt_id != 0 ';
		}

		$sql .= '
			GROUP BY ofr_prd_id) AS latest_offer
			INNER JOIN
			prw_offers
			ON
			prw_offers.ofr_id = latest_offer.id and prw_offers.ofr_tnt_id = latest_offer.tnt_id
		';

		$r = ria_mysql_query( $sql );

		if( !$r || !ria_mysql_num_rows( $r ) ){
			return false;
		}
		if( ria_mysql_num_rows($r) > 1 ){
			$rst = array();

			while( $l = ria_mysql_fetch_assoc( $r ) ){
				$rst[$l['prd_id']] = $l;
			}
		}else{
			$rst = ria_mysql_fetch_assoc($r);
		}

		return $rst;
	}

	/**	Cette fonction permet de récupérer toutes les offres de la base de données, ou toute les offres pour un concurrent en pasant en paramètre l'identifiant d'un concurrent
	 *
	 *	@param integer $cpt_id Facultatif, identifiant du concurrent
	 *
	 *	@return array Retourne un tableau avec les lignes d'offres avec les clés suivantes :
	 * 						- tnt_id : identifiant du tenant
	 * 						- cpt_id : identifiant du concurrent
	 * 						- prd_id : identifiant du produit
	 * 						- url : url du produit chez la plateforme du concurrent
	 * 						- landedprice : Prix ttc du produit sur la plateforme du concurrent
	 * 						- shippingprice : faris de port ttc du produit sur la plateforme du concurrent
	 */
	public function prw_offers_getAll( $cpt_id = null )
	{

		if( !is_null( $cpt_id ) && (!is_numeric($cpt_id) || $cpt_id <= 0)){
			return false;
		}

		global $config;

		$sql = '
			select
				ofr_tnt_id as tnt_id,
				ofr_cpt_id as cpt_id,
				ofr_prd_id as prd_id,
				ofr_cpt_url as url,
				ofr_landedprice as landedprice,
				ofr_shippingprice as shippingprice
        	from prw_offers
			where ofr_tnt_id=' . $config['tnt_id'].'
		';

		if( !is_null( $cpt_id ) ){
			$sql .= ' and ofr_cpt_id=' . $cpt_id;
		}else{
			$sql .= ' and ofr_cpt_id != 0';
		}

		$sql .= ' and ofr_date_deleled is null';

		$r = ria_mysql_query( $sql );
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}

	/**	Cette fonction permet de modifié une offre existante
	 *
	 *	@param int $id               Identifiant de l'offre à modifier
	 *	@param float $landedprice      Prix TTC
	 *	@param float $shippingprice    Prix de livraison
	 *	@param string $url              Nouvelle url
	 *	@param int $cpt_id           Identifiant concurrent
	 *	@param int $prd_id           Identifiant produit
	 *
	 *	@return bool    retourne false si erreur, sinon true.
	 */
	public function prw_offers_update( $id, $landedprice, $shippingprice, $url, $cpt_id, $prd_id )
	{
		if( !is_numeric( $id ) || $id < 0 || !is_numeric( $landedprice ) || $landedprice < 0 || !is_numeric( $shippingprice ) || $shippingprice < 0 || !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 || !is_string( $url ) ){
			return false;
		}

		global $config;

		$url = addslashes( ucfirst( trim( $url ) ) );

		$sql = '
			update prw_offers
				set ofr_cpt_id=' . $cpt_id . ',
				ofr_prd_id=' . $prd_id . ',
				ofr_landedprice=' . $landedprice . ',
				ofr_shippingprice=' . $shippingprice . ',
				ofr_url="' . $url . '"
			where ofr_tnt_id='.$config['tnt_id'].'
				and ofr_id=' . $id .'
			';

		return ria_mysql_query( $sql );
	}

	/**	Cette fonction permet de supprimer une offre en passant en paramètre son identifiant.
	 *
	 *	@param int $id    Identifiant d'une offre
	 *
	 *	@return bool    retourne false si erreur, sinon true
	 */
	public function prw_offers_delete( $id )
	{
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from prw_offers
			where ofr_tnt_id='.$config['tnt_id'].'
			ofr_id=' . $id .'
		';

		return ria_mysql_query( $sql );
	}
}

/// @}
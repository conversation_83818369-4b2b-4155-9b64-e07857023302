<?php
/**
 * \ingroup PriceWatchingModel
 */
/** \class prw_competitors
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_competitors
 */
class prw_competitors
{
	/**	Cette fonction permet d'ajouter un concurrent dans la table
	 *
	 *	@param string $name        Obligatoire, nom du concurrent
	 *	@param string $lng_code    Obligatoire, lng code du concurrent
	 *	@param $url         Obligatoire, url du concurrent
	 *
	 *	@return bool|int    false si erreur, sinon identifiant du concurrent entré
	 */
	public function prw_competitors_add( $id, $name, $lng_code, $url )
	{
		global $config;
		if( !is_numeric( $id ) || $id < 0 || !is_string( $name ) || !is_string( $lng_code ) || !is_string( $url ) ){
			return false;
		}

		$name = str_replace( '"', '\'\'', $name );
		$name = addslashes( ucfirst( trim( $name ) ) );
		$lng_code = addslashes( ucfirst( trim( $lng_code ) ) );
		$url = addslashes( ucfirst( trim( $url ) ) );

		$values = $id . ', "' . $name . '", "' . $lng_code . '", "' . $url . '"';
		$sql = 'insert
        into prw_competitors ( cpt_id, cpt_name, cpt_lng_code, cpt_url ) 
        VALUES (' . $values . ')';

		ria_mysql_query( $sql );
		$id = ria_mysql_insert_id();

		return $id;
	}

	/** Cette fonction permet de récupérer un concurrent associé a un identifiant
	 *
	 * @param int $id    Obligatoire, identifiant concurrent
	 *
	 * @return array|bool    retourne false si erreur, sinon un tableau associatif avec les jeux de valeur suivant :
	 *                    -id            identifiant concurrent
	 *                    -name            nom du concurrent
	 *                    -lng_code        code lng
	 *                    -url            url du site du concurrent
	 */
	public function prw_competitors_get( $id )
	{
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		$sql = 'select cpt_id as id, cpt_name as name, cpt_lng_code as lng_code, cpt_url as url
        from prw_competitors 
        where cpt_id=' . $id;

		$r = ria_mysql_query( $sql );
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}

	/** Cette fonction récupère tous les concurrents de la table
	 * @return array    retourne un tableau avec tous les concurrents avec les jeux de valeur suivant :
	 *               -id            identifiant concurrent
	 *               -name            nom du concurrent
	 *               -lng_code        code lng
	 *               -url            url du site du concurrent
	 */
	public function prw_competitors_getAll()
	{
		global $config;
		$sql = 'select cpt_id as id,  cpt_name as name, cpt_lng_code as lng_code, cpt_url as url
        from prw_competitors 
        where cpt_tnt_id=' . $config['tnt_id'];

		$r = ria_mysql_query( $sql );
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}

	/**	Cette fonction récupère tous les concurrents activés pour un client
	 *	@return array|bool    retourne un tableau avec tous les concurrents avec les jeux de valeur suivant :
	 *               -id            identifiant concurrent
	 *               -name            nom du concurrent
	 *               -lng_code        code lng
	 *               -url            url du site du concurrent
	 */
	public function prw_competitors_getActive( $cli = true )
	{
		global $config;
		$sql = 'select cpt_id as id,  cpt_name as name, cpt_lng_code as lng_code, cpt_url as url
        from prw_competitors
        join prw_competitors_tenants
        on prw_competitors_tenants.ctn_cpt_id=prw_competitors.cpt_id
        where prw_competitors_tenants.ctn_is_active=1
        and prw_competitors_tenants.ctn_tnt_id=' . $config['tnt_id'];

		if( !$cli ){
			$sql .= ' and prw_competitors.cpt_id!=' . PRW_CLIENT . '';
		}

		$r = ria_mysql_query( $sql );
		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			$rst[] = $row;
		}

		return $rst;
	}


	/**	Cette fonction permet de mettre à jour un concurrent
	 *
	 *	@param int $id          Obligatoire, identifiant du concurrent à modifier
	 *	@param string $name        Obligatoire,    nom du concurrent, nouveau ou actuel
	 *	@param string $lng_code    Obligatoire,    code lng, nouveau ou actuel
	 *	@param $url         Obligatoire,    url du site concurrent, nouveau ou actuel
	 *
	 *	@return bool    retourne false si erreur
	 */
	public function prw_competitors_update( $id, $name, $lng_code, $url )
	{
		global $config;
		if( !is_numeric( $id ) || $id <= 0 || !is_string( $name ) || !is_string( $lng_code ) || !is_string( $url ) ){
			return false;
		}

		$name = str_replace( '"', '\'\'', $name );
		$name = addslashes( ucfirst( trim( $name ) ) );
		$lng_code = addslashes( ucfirst( trim( $lng_code ) ) );
		$url = addslashes( ucfirst( trim( $url ) ) );

		$sql = 'UPDATE prw_competitors 
        SET 
        cpt_name = ' . $name . ',
        cpt_lng_code = ' . $lng_code . ',
        cpt_url = ' . $url . ' 
        WHERE cpt_id=' . $id;

		ria_mysql_query( $sql );
		$id = $config['tnt_id'];

		return $id;
	}

	/** Cette fonction permet de supprimer un concurrent
	 *
	 * @param int $id    olbigatoir, identifiant concurrent
	 *
	 * @return bool    retourne false si erreur, sinon true.
	 */
	public function prw_competitors_delete( $id )
	{
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		$sql = 'delete 
        from prw_competitors
         WHERE cpt_id=' . $id;
		ria_mysql_query( $sql );

		return true;
	}
}
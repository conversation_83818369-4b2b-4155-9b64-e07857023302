<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaised
 */

/** \class prw_followed_lists
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_followed_lists
 */
class prw_followed_list_sections {
	/**
	 * permet de validé un string
	 *
	 * @param string $string
	 * @return boolean true si valide false si non
	 */
	private static function validString($string) {
		return is_string($string) && trim($string);
	}

	/**
	 * permet de validé un entier
	 *
	 * @param integer $int
	 * @return boolean true si valide false si non
	 */
	private static function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}

	/**
	 * Cette fonction permet la création d'une section pour des assortiments
	 *
	 * @param string $title Nom de la section
	 * @return integer|boolean Retourne l'indentifiant de la section créé, sinon false
	 */
	public static function add($title) {
		if (!self::validString($title)) {
			throw new \InvalidArgumentException("title doit être un string");
		}

		global $config;

		$fields = array(
			'fls_tnt_id',
			'fls_title',
			'fls_date_created',
		);

		$values = array(
			$config['tnt_id'],
			'"'.$title.'"',
			'now()',
		);

		$insert = '
			insert into prw_followed_list_sections
				('.implode(', ', $fields).')
			values
				('.implode(', ', $values).');
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}
	/**
	 * Cette fonction permet de récupérer une section
	 *
	 * @param integer $id Identifiant de la section
	 * @return void
	 */
	public static function get($id=null) {
		if (!is_null($id)) {
			$ids = control_array_integer($id);
			if (!$ids) {
				throw new \InvalidArgumentException("id doit être un entier ou un tableau d'entier");
			}
		}

		global $config;
		$select = '
			select
				fls_id as id,
				fls_title as title,
				fls_date_created as date_created
			from prw_followed_list_sections
			where fls_tnt_id='.$config['tnt_id'].'
				and fls_date_deleted is null
		';

		if (!is_null($id)) {
			$select .= ' and fls_id in ('.implode(', ', $ids).')';
		}

		$r = ria_mysql_query($select);

		if (!$r) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet la suppression d'une section
	 *
	 * @param integer $id Identifiant d'une section
	 * @param string $title Nom de la section
	 * @return boolean Retourne true si succès sinon false
	 */
	public static function update($id, $title) {
		if (!self::validInteger($id)) {
			throw new \InvalidArgumentException("id doit être un entier");
		}

		if (!self::validString($title)) {
			throw new \InvalidArgumentException("title doit être un string");
		}

		global $config;

		$update = '
			update prw_followed_list_sections
				set fls_title = "'.$title.'"
			where fls_tnt_id='.$config['tnt_id'].'
				and fls_id='.$id.'
				and fls_date_deleted is null
		';

		return ria_mysql_query($update);
	}
	/**
	 * Cette fonction permet de récupérer le nombre d'assortiment lié a un secteur
	 *
	 * @param integer $id Identifiant d'un assortiment
	 * @return integer Le nombre d'assortiment liés
	 *
	 * @throws InvalidArgumentException si une de paramètre
	 * @throws Exception si une erreur SQL
	 */
	public static function countLinkedFollowedLists($id)
	{
		if (!self::validInteger($id)) {
			throw new \InvalidArgumentException("title doit être un entier");
		}

		global $config;

		$select = '
			select count(*) as "count"
			from prw_followed_lists
			where pfl_tnt_id = '.$config['tnt_id'].'
			and pfl_date_deleted is null
			and pfl_fls_id='.$id.'
		';

		$r = ria_mysql_query($select);

		if (!$r) {
			throw new \Exception(ria_mysql_error());
		}

		$one = ria_mysql_fetch_assoc($r);

		return $one['count'];
	}
	/**
	 * Cette fonction permet la suppression d'une section
	 *
	 * @param integer $id Identifiant d'une section
	 * @return boolean Retourne true si succès sinon false
	 */
	public static function delete($id) {
		if (!self::validInteger($id)) {
			throw new \InvalidArgumentException("title doit être un entier");
		}

		global $config;
		$select = '
			update prw_followed_list_sections
				set fls_date_deleted = now()
			where fls_tnt_id='.$config['tnt_id'].'
				and fls_id='.$id.'
				and fls_date_deleted is null
		';

		return ria_mysql_query($select);
	}
}
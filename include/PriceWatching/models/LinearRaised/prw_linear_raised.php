<?php
namespace Riashop\PriceWatching\models\LinearRaised;

use DateTime;

/**
 * \ingroup LinearRaisedModel
 */

/** \class prw_linear_raised
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_linear_raised
 */
class prw_linear_raised
{
	private static function validInteger($int)
	{
		return is_numeric($int) && $int >= 0;
	}
	/**
	 * Cette fonction permet d'ajouter un relevé linéaire
	 *
	 * @param integer $author_id Identifiant de l'auteur du rapport
	 * @param integer $usr_id Identifiant du compte client
	 * @param integer $pfl_id Identifiant de l'assortiment associé
	 * @param integer $group_id Facultatif, groupe de relevé
	 * @param integer $total_dn Facultatif, total de produit saisie dans le relevé
	 * @param integer $total_dn_section Facultatif, total de produit dans le rayon
	 * @param DateTime $date_created Facultatif, date de création
	 * @param mixed $ref_gescom Facultatif, Référence gescom du relevé
	 * @return integer|boolean Retourne l'identifiant insérer sinon false.
	 *
	 * @throws InvalidArgumentException
	 */
	public static function add($author_id, $usr_id, $pfl_id, $group_id = null, $total_dn = null, $total_dn_section = null, \DateTime $date_created = null, $ref_gescom=null)
	{
		if (!self::validInteger($author_id)) {
			throw new \InvalidArgumentException("author_id doit être un entier");
		}

		if (!self::validInteger($usr_id)) {
			throw new \InvalidArgumentException("usr_id doit être un entier");
		}

		if (!self::validInteger($pfl_id)) {
			throw new \InvalidArgumentException("pfl_id doit être un entier");
		}

		if (!is_null($group_id) && !self::validInteger($group_id)) {
			throw new \InvalidArgumentException("group_id doit être un entier");
		}

		if (!is_null($total_dn) && !self::validInteger($total_dn)) {
			throw new \InvalidArgumentException("total_dn doit être un entier");
		}

		if (!is_null($total_dn_section) && !self::validInteger($total_dn_section)) {
			throw new \InvalidArgumentException("total_dn_section doit être un entier");
		}

		global $config;

		$date = is_null($date_created) ? 'now()' : '"' . $date_created->format("Y-m-d H:i:s") . '"';

		$fields = array(
			'plr_tnt_id',
			'plr_author_id',
			'plr_usr_id',
			'plr_pfl_id',
			'plr_date_created',
		);
		$values = array(
			$config['tnt_id'],
			$author_id,
			$usr_id,
			$pfl_id,
			$date
		);

		if (!is_null($group_id)) {
			$fields[] = 'plr_group_id';
			$values[] = $group_id;
		}

		if (!is_null($total_dn)) {
			$fields[] = 'plr_total_dn';
			$values[] = $total_dn;
		}

		if (!is_null($total_dn_section)) {
			$fields[] = 'plr_total_dn_section';
			$values[] = $total_dn_section;
		}

		if( $ref_gescom !== null ){
			$fields[] = 'plr_ref_gescom';
			$fields[] = '"'.addslashes($ref_gescom).'"';
		}

		$insert = '
			insert into prw_linear_raised
				(' . implode(', ', $fields) . ')
			values
				(' . implode(', ', $values) . ')
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}
	/**
	 * Cette fonction permet de récupérer les relevés linéaire
	 *
	 * @param integer|array $plr_id Identifiant ou tableau du relevé
	 * @param integer $group_id Facultatif, Identifiant du groupe de relevé
	 * @return resource|boolean Retourne une ressource mysql avec les champs suivant :
	 * 					- id : Identifiant du relevé
	 *					- author_id : Identifiant de l'auteur du rapport
	 *					- usr_id : Identifiant du client
	 *					- pfl_id : identifiant de l'assortiment
	 *					- group_id : identifiant du groupe
	 *					- date_created : date de creation
	 *					- date_modified : date de modification
	 *					- total_dn : total de produit sur le relevé
	 *					- total_dn_section : total de produit dans le rayon
	 *					- ref_gescom : référence du linéaire en gescom
	 *
	 * @throws InvalidArgumentException
	 */
	public static function get($plr_id, $group_id = null)
	{
		$ids = control_array_integer($plr_id);

		if (!$ids) {
			throw new \InvalidArgumentException("plr_id doit être un entier ou un tableau d'entier");
		}

		$LinearRaisedGetter = new LinearRaisedGetter;
		$LinearRaisedGetter->withId($ids);
		if (!is_null($group_id) && self::validInteger($group_id)) {
			$LinearRaisedGetter->withGroupId($group_id);
		}
		return $LinearRaisedGetter->query();
	}

	/**
	 * Cette fonction permet de récupérer la ref gescom pour un identifiant
	 *
	 * @param integer $plr_id Identifiant du relevé linéaire
	 * @return string|boolean Reoturne la ref gescom ou false si rien
	 *
	 * @throws InvalidArgumentException Envoie une exception si mauvais paramètre
	 */
	public function getRefGescom($plr_id)
	{
		if (!self::validInteger($plr_id)) {
			throw new \InvalidArgumentException("plr_id doit être un entier");
		}

		$LinearRaisedGetter = new LinearRaisedGetter;
		$LinearRaisedGetter->withId($plr_id)->onlyRefGescom();

		$r_plr = $LinearRaisedGetter->query();

		if( !ria_mysql_num_rows($r_plr) ){
			return false;
		}

		$plr = ria_mysql_fetch_assoc($r_plr);

		return $plr['ref_gescom'];
	}

	/**
	 * Cette fonction retourne l'objet responsable de générer les requêtes de récupération des relevés
	 *
	 * @return LinearRaisedGetter Instance de LinearRaisedGetter
	 */
	public static function getGetter(){
		return new LinearRaisedGetter;
	}

	/**
	 * Cette fonction permet de récupérer les relevé linéaire à synchroniser
	 *
	 * @param DateTime $date_modified Facultatif, Date de dernière modification
	 * @return resource|boolean Retourne une ressource mysql avec les champs suivant :
	 * 					- id : Identifiant du relevé
	 *					- author_id : Identifiant de l'auteur du rapport
	 *					- usr_id : Identifiant du client
	 *					- pfl_id : identifiant de l'assortiment
	 *					- group_id : identifiant du groupe
	 *					- date_created : date de creation
	 *					- date_modified : date de modification
	 *					- total_dn : total de produit sur le relevé
	 *					- total_dn_section : total de produit dans le rayon
	 *					- ref_gescom : référence du linéaire en gescom
	 */
	public static function getLastModified(DateTime $date_modified=null)
	{
		global $config;

		$select = '
			select plr_id as id,
				plr_author_id as author_id,
				plr_usr_id as usr_id,
				plr_pfl_id as pfl_id,
				plr_group_id as group_id,
				plr_total_dn as total_dn,
				plr_total_dn_section as total_dn_section,
				plr_date_created as date_created,
				plr_date_modified as date_modified,
				plr_ref_gescom as ref_gescom
			from prw_linear_raised
			where plr_tnt_id=' . $config['tnt_id'] . '
				and plr_date_deleted is null
				adn plr_ref_gescom is null
		';

		if( $date_modified !== null ){
			$select .= '
				and plr_date_modified > "'.$date_modified->format('Y-m-d H:i:s').'"
			';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}

	/**
	 * Cette fonction permet de mettre a jour un relevé
	 *
	 * @param integer $plr_id Identifiant du relevé linéair
	 * @param integer $author_id Facultatif, identifiant de l'auteur
	 * @param integer $usr_id Facultatif, identifiant de l'utilisateur lié au relevé
	 * @param integer $pfl_id Facultatif, identifiant de l'assortiment
	 * @param integer $group_id Facultatif, identifiant du groupe
	 * @param integer $total_dn Facultatif, total de produit dans le relevé
	 * @param integer $total_dn_section Facultatif, total de produit dans le rayon
	 * @param integer $ref_gescom Facultatif, référence gescom
	 * @return boolean Retourne true si succès, false si échec
	 *
	 * @throws InvalidArgumentException
	 */
	public static function update($plr_id, $author_id=null, $usr_id=null, $pfl_id=null, $group_id=null, $total_dn=null, $total_dn_section=null, $ref_gescom=null )
	{
		if (!self::validInteger($plr_id)) {
			throw new \InvalidArgumentException("plr_id doit être un entier");
		}

		$values = array();

		if (!is_null($author_id)){
			if (!self::validInteger($author_id)) {
				throw new \InvalidArgumentException("author_id doit être un entier");
			}else{
				$values[] = ' plr_author_id ='.$author_id;
			}
		}

		if (!is_null($usr_id)){
			if (!self::validInteger($usr_id)) {
				throw new \InvalidArgumentException("usr_id doit être un entier");
			}else{
				$values[] = ' plr_usr_id ='.$usr_id;
			}
		}

		if (!is_null($pfl_id)){
			if (!self::validInteger($pfl_id)) {
				throw new \InvalidArgumentException("pfl_id doit être un entier");
			}else{
				$values[] = ' plr_pfl_id ='.$pfl_id;
			}
		}

		if (!is_null($group_id)){
			if (!self::validInteger($group_id)) {
				throw new \InvalidArgumentException("group_id doit être un entier");
			}else{
				$values[] = ' plr_group_id ='.$group_id;
			}
		}

		if (!is_null($total_dn)){
			if (!self::validInteger($total_dn)) {
				throw new \InvalidArgumentException("total_dn doit être un entier");
			}else{
				$values[] = ' plr_total_dn ='.$total_dn;
			}
		}

		if (!is_null($total_dn_section)){
			if (!self::validInteger($total_dn_section)) {
				throw new \InvalidArgumentException("total_dn_section doit être un entier");
			}else{
				$values[] = ' plr_total_dn_section ='.$total_dn_section;
			}
		}

		if( $ref_gescom !== null ){
			$values[] = ' plr_ref_gescom = "'.addslashes($ref_gescom).'"';
		}


		if ( empty($values)) {
			throw new \Exception("Aucun paramètre pour la mise a jours");
		}

		global $config;

		$update = '
			update prw_linear_raised
				set '.implode(',', $values).'
			where plr_tnt_id=' . $config['tnt_id'] . '
				and plr_id='.$plr_id.'
				and plr_date_deleted is null
		';

		return ria_mysql_query($update);
	}

	/** Cette fonction permet de mettre à jour la référence gescom
	 *
	 * @param int $plr_id Identfiant du relevé linéaire
	 * @param mixed $ref_gescom Reférence Gescom du relevé linéaire
	 * @return boolean retourne true si succès sinon false
	 *
	 * @throws InvalidArgumentException Lance une exception si erreur de paramètre
	 */
	public static function setRefGescom($plr_id, $ref_gescom){
		if (!self::validInteger($plr_id)) {
			throw new \InvalidArgumentException("plr_id doit être un entier");
		}

		global $config;

		$update = '
			update prw_linear_raised
				set plr_ref_gescom="'.addslashes($ref_gescom).'"
			where plr_tnt_id=' . $config['tnt_id'] . '
				and plr_id='.$plr_id.'
				and plr_date_deleted is null
		';

		return ria_mysql_query($update);
	}
	/**
	 * Cette fonction permet de supprimé un ou plusieurrelevé linéaire
	 *
	 * @param integer|array $plr_id Identifiant ou tableau d'identifiant de relevé
	 * @return boolean Retourne true si succès, false si erreur.
	 *
	 * @throws InvalidArgumentException
	 */
	public static function delete($plr_id)
	{
		$ids = control_array_integer($plr_id);

		if (!$ids) {
			throw new \InvalidArgumentException("plr_id doit être un entier ou un tableau d'entier");
		}

		global $config;

		$select = '
			update prw_linear_raised
				set plr_date_deleted = now()
			where plr_tnt_id=' . $config['tnt_id'] . '
				and plr_id in (' . implode(', ', $ids) . ')
				and plr_date_deleted is null
		';

		return ria_mysql_query($select);
	}
}
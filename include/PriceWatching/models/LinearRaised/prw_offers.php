<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaisedModel
 */

/** \class prw_offers
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_offers
 *
 * Cette class ce base sur les lignes d'offre avec l'identifiant de concurrent (cpt_id) à 0. Si il y a un identifiant de concurrent (1, 2, ...) c'est lié au module de veille tarifaire.
 * @see prw_offers prw_offers.inc.php
 */
class prw_offers {
	/**
	 * Propriété sur les offres
	 *
	 * @var array
	 */
	private static $allowed_props = array(
		'count' => 'int',
		'usr_id' => 'int',
		'level' => 'int',
		'facings' => 'int',
		'positions' => 'int',
		'pwf_rank' => 'int',
		'pwf_pmc' => 'float',
		'landedprice' => 'float',
		'is_cpt' => 'bool',
	);
	/**
	 * permet de validé un entier
	 *
	 * @param integer $int
	 * @return boolean true si valide false si non
	 */
	private static function validInteger($int) {
		return is_numeric($int) && $int >= 0;
	}
	/**
	 * permet de validé un décimal
	 *
	 * @param float|integer $float
	 * @return boolean true si valide false si non
	 */
	private static function validFloat($float) {
		return (is_float($float) || is_numeric($float)) && $float >= 0;
	}
	/**
	 * permet de validé un string
	 *
	 * @param string $string
	 * @return boolean true si valide false si non
	 */
	private static function validString($string) {
		return is_string($string) && trim($string);
	}
	/**
	 * Permet de validé la valeur d'une propriété en fonction de si elle est prise en compte et si ça valeur est correcte
	 *
	 * @param string $prop Propriété
	 * @param mixed $value Valeur de la propriété
	 * @return boolean True si la propriété est bonne, false si non
	 */
	private static function isValidProp($prop, $value) {
		if (!array_key_exists($prop, self::$allowed_props)) {
			return false;
		}

		$type = self::$allowed_props[$prop];

		switch ($type) {
			case 'int':
				return self::validInteger($value);
			case 'float':
				return self::validFloat($value);
			case 'string':
				return self::validString($value);
			case 'bool':
				return is_bool($value);
		}

		return false;
	}
	/**
	 * Permet de formaté la propriété pour être ajouté dans le sql
	 *
	 * @param string $prop Nom de la propriété
	 * @param mixed $value Valeur de la propriété
	 * @return mixed La valeur formaté
	 */
	private static function sanitizeProp($prop, $value) {
		if (!array_key_exists($prop, self::$allowed_props)) {
			return false;
		}

		$type = self::$allowed_props[$prop];

		switch ($type) {
			case 'int':
				return $value;
			case 'float':
				return $value;
			case 'string':
				return '"'.$value.'"';
			case 'bool':
				return $value ? '1' : '0';
		}
	}
	/**
	 * Cette fonction permet d'ajouter une offre
	 *
	 * @param integer $plr_id Identifiant du relevé
	 * @param integer $prd_id identifiant du produit
	 * @param integer $usr_id identifiant de l'utilisateur
	 * @param array $props Facultatif, Propriété supplémentaire du produit
	 * @return integer|boolean Retourne l'identifiant du l'offre, ou false si erreur.
	 *
	 * @throws InvalidArgumentException si argument invalid
	 */
	public static function add($plr_id, $prd_id, $usr_id,array $props=array()) {
		if (!self::validInteger($plr_id)){
			throw new \InvalidArgumentException("plr_id doit être un entier");
		}
		if (!self::validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}
		if (!self::validInteger($usr_id)){
			throw new \InvalidArgumentException("usr_id doit être un entier");
		}

		$valid_props = array();
		foreach ($props as $prop => $value) {
			if (!self::isValidProp($prop, $value)) {
				throw new \InvalidArgumentException("$prop invalid type");
			}

			$valid_props['ofr_'.$prop] = self::sanitizeProp($prop, $value);
		}

		global $config;

		$fields = array(
			'ofr_tnt_id',
			'ofr_plr_id',
			'ofr_prd_id',
			'ofr_usr_id',
			'ofr_date_created',
		);

		$values = array(
			$config['tnt_id'],
			$plr_id,
			$prd_id,
			$usr_id,
			'now()',
		);

		if (!empty($valid_props)) {
			$fields = array_merge($fields, array_keys($valid_props));
			$values = array_merge($values, array_values($valid_props));
		}

		$insert = '
			insert into prw_offers
				('.implode(', ', $fields).')
			values
				('.implode(', ', $values).')
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}
	/**
	 * Cette fonction permet de mettre à jour une offre
	 *
	 * @param integer $ofr_id Identifiant de l'offre
	 * @param integer $plr_id Facultatif, identifiant du listing
	 * @param integer $prd_id Facultatif, identifiant du produit lié
	 * @param array $updated_props Facultatif, tableau des propriétés supplémentaires
	 * @return boolean true si succès false dans le cas contraire.
	 *
	 * @throws InvalidArgumentException si argument invalid
	 */
	public static function update($ofr_id, $plr_id=null, $prd_id=null, array $updated_props=array()) {
		if (!self::validInteger($ofr_id)){
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}
		if (!is_null($plr_id) && !self::validInteger($plr_id)){
			throw new \InvalidArgumentException("plr_id doit être un entier");
		}
		if (!is_null($prd_id) && !self::validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		$valid_props = array();
		foreach ($updated_props as $prop => $value) {
			if (!self::isValidProp($prop, $value)) {
				throw new \InvalidArgumentException("$prop invalid type");
			}
			$valid_props[] = 'ofr_'.$prop.'='.self::sanitizeProp($prop, $value);
		}

		if (empty($valid_props)) {
			throw new \Exception("Update impossible auccunne information valid a modifier");
		}

		global $config;

		$update = '
			update prw_offers
				set '.implode(', ', $valid_props).'
			where ofr_tnt_id='.$config['tnt_id'].'
				and ofr_id ='.$ofr_id.'
				and ofr_date_deleted is null
		';
		if (!is_null($plr_id)) {
			$update .= ' and ofr_plr_id='.$plr_id.' ';
		}

		if (!is_null($prd_id)) {
			$update .= ' and ofr_prd_id='.$prd_id.' ';
		}

		return ria_mysql_query($update);
	}

	/** Cette fonction permet de mettre à jour la référence gescom
	 *
	 * @param int $ofr_id Identfiant de l'offre
	 * @param mixed $ref_gescom Reférence Gescom du relevé linéaire de salesforce
	 * @return boolean retourne true si succès sinon false
	 *
	 * @throws InvalidArgumentException Lance une exception si erreur de paramètre
	 */
	public static function setRefGescom($ofr_id, $ref_gescom){
		if (!self::validInteger($ofr_id)) {
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		global $config;

		$update = '
			update prw_offers
				set ofr_ref_gescom="'.addslashes($ref_gescom).'"
			where ofr_tnt_id=' . $config['tnt_id'] . '
				and ofr_id='.$ofr_id.'
				and ofr_date_deleted is null
		';

		return ria_mysql_query($update);
	}

	/**
	 * Cette fonction permet de récupérer les offres d'un relevé
	 *
	 * @param integer|array $plr_id Identifiant ou tableau d'identifiant de relevé
	 * @param integer $ofr_id Facultatif, identifiant de l'offre
	 * @param integer $prd_id Facultatif, identifiant du produit
	 * @return array|boolean Retourn un tableau avec :
	 * 						- id : identifiant de l'offre
	 * 						- cpt_id : identifiant du concurrent
	 * 						- landedprice : prix relevé
	 * 						- shippingprice : frais de port relevé
	 * 						- promo_price : prix promo relevé
	 * 						- url : url du produit sur la plateforme du concurrent
	 * 						- plr_id : identifiant du relevé
	 * 						- prd_id : identifaint du produit
	 * 						- is_cpt : si c'est une offre concurrente ou non
	 * 						- usr_id : identifiant de l'utilidateur
	 * 						- count : colonne
	 * 						- level : niveaux
	 * 						- facings : facings
	 * 						- positions : positions
	 * 						- pwf_rank : rang dans l'assortiment
	 * 						- pwf_pmc : pmc dans l'assortiment
	 * 						- ost_id : identifiant du statu
	 * 						- date_created : date de création
	 * 						- date_modified : date de modification
	 * @throws InvalidArgumentException si argument invalid
	 */
	public static function get($plr_id, $ofr_id=null, $prd_id=null) {
		$ids = control_array_integer($plr_id);

		if (!$ids) {
			throw new \InvalidArgumentException("ids doit être un entier ou un tableau d'entier");
		}

		if (!is_null($ofr_id) && !self::validInteger($ofr_id)){
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		if (!is_null($prd_id) && !self::validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		global $config;

		$select = '
			select
				ofr_id as id,
				ofr_cpt_id as cpt_id,
				ofr_landedprice as landedprice,
				ofr_shippingprice as shippingprice,
				ofr_promo_price as promo_price,
				ofr_url as url,
				ofr_plr_id as plr_id,
				ofr_prd_id as prd_id,
				ofr_is_cpt as is_cpt,
				ofr_usr_id as usr_id,
				ofr_count as count,
				ofr_level as level,
				ofr_facings as facings,
				ofr_positions as positions,
				ofr_pwf_rank as pwf_rank,
				ofr_pwf_pmc as pwf_pmc,
				ofr_date_created as date_created,
				ofr_date_modified as date_modified
			from prw_offers
			where ofr_tnt_id='.$config['tnt_id'].'
				and ofr_plr_id in ('.implode(', ', $ids).')
				and ofr_date_deleted is null
		';

		if (!is_null($ofr_id)) {
			$select .= ' and ofr_id='.$ofr_id.' ';
		}

		if (!is_null($prd_id)) {
			$select .= ' and ofr_prd_id='.$prd_id.' ';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet de supprimer les offres d'un relevé
	 *
	 * @param integer|array $plr_id Identifiant ou tableau d'identifiant de relevé
	 * @param integer $ofr_id Facultatif, Identifiant de l'offre
	 * @param integer $prd_id Facultatif, Identifiant du produit
	 * @return boolean Retourne true si succès, false dans le cas contraire.
	 *
	 * @throws InvalidArgumentException si argument invalid
	 */
	public static function delete($plr_id, $ofr_id=null, $prd_id=null) {
		global $config;

		$ids = control_array_integer($plr_id);

		if (!$ids) {
			throw new \InvalidArgumentException("ids dois être un entier ou un tableau d'entier");
		}

		if (!is_null($ofr_id) && !self::validInteger($ofr_id)){
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		if (!is_null($prd_id) && !self::validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		$delete = '
			update prw_offers
				set ofr_date_deleted = now()
			where ofr_tnt_id='.$config['tnt_id'].'
				and ofr_plr_id in ('.implode(', ', $ids).')
				and ofr_date_deleted is null
		';

		if (!is_null($ofr_id)) {
			$delete .= ' and ofr_id='.$ofr_id.' ';
		}

		if (!is_null($prd_id)) {
			$delete .= ' and ofr_prd_id='.$prd_id.' ';
		}

		return ria_mysql_query($delete);
	}
}
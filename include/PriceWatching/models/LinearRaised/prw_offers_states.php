<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaisedModel
 */

/** \class prw_offers_states
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_offers_states
 */
class prw_offers_states {
	private static function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}
	/**
	 * Cette fonction permet d'ajouter un statut à une offre
	 *
	 * @param integer $ofr_id Identifiant de l'offre
	 * @param integer $ps_id Identifiant du statut
	 * @return boolean Retourne true si succès, false dans le cas contraire
	 */
	public static function add($ofr_id, $ps_id) {
		if (!self::validInteger($ofr_id)) {
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		if (!self::validInteger($ps_id)) {
			throw new \InvalidArgumentException("ps_id doit être un entier");
		}

		global $config;

		$fields = array(
			'pst_tnt_id',
			'pst_ps_id',
			'pst_ofr_id',
		);

		$values = array(
			$config['tnt_id'],
			$ps_id,
			$ofr_id,
		);

		$insert = '
			insert into prw_offers_states
				('.implode(', ', $fields).')
			values
				('.implode(', ', $values).')
		';

		return ria_mysql_query($insert);
	}
	/**
	 * Cette fonction permet de récupérer les statut pour une offre
	 *
	 * @param integer $ofr_id Identifiant de l'offre
	 * @param integer $ps_id Facultatif, identifiant du statut
	 * @return resource Retourne un résultat mysql avec :
	 * 						- ofr_id : Identifiant de l'offre
	 * 						- ps_id : Identifiant du statut
	 * 						- date_created : date d'ajout à l'offre
	 */
	public static function get($ofr_id, $ps_id=null) {
		if (!self::validInteger($ofr_id)) {
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		if (!is_null($ps_id) && !self::validInteger($ps_id)) {
			throw new \InvalidArgumentException("ps_id doit être un entier");
		}

		global $config;

		$select = '
			select
				pst_ofr_id as ofr_id,
				pst_ps_id as ps_id,
				pst_date_created as date_created
			from prw_offers_states
			where pst_tnt_id='.$config['tnt_id'].'
				and pst_ofr_id='.$ofr_id.'
		';

		if (!is_null($ps_id)) {
			$select .= ' and pst_ps_id='.$ps_id.' ';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}

	/**
	 * Cette fonction permet de supprimé une relation offre statut
	 * @param integer $ofr_id Identifiant de l'offre
	 * @param integer $ps_id Facultatif, identifiant du statut
	 * @return boolean Retourne true si succès de la suppression sinon false
	 */
	public static function delete($ofr_id, $ps_id=null) {
		if (!self::validInteger($ofr_id)) {
			throw new \InvalidArgumentException("ofr_id doit être un entier");
		}

		if (!is_null($ps_id) && !self::validInteger($ps_id)) {
			throw new \InvalidArgumentException("ps_id doit être un entier");
		}

		global $config;

		$delete = '
			delete from prw_offers_states
			where pst_tnt_id='.$config['tnt_id'].'
				and pst_ofr_id='.$ofr_id.'
		';

		if (!is_null($ps_id)) {
			$delete .= ' and pst_ps_id='.$ps_id.' ';
		}

		return ria_mysql_query($delete);
	}

}
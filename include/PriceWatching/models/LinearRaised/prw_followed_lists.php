<?php
namespace Riashop\PriceWatching\models\LinearRaised;

require_once 'strings.inc.php';
/**
 * \defgroup LinearRaisedModel Modéle
 * \ingroup LinearRaised
 */

/** \class prw_followed_lists
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_followed_lists
 */
class prw_followed_lists
{
	/**
	 * Type d'assortiment web
	 */
	const TYPE_WEB = 0;
	/**
	 * Type d'assortiment yuto
	 */
	const TYPE_YUTO = 1;
	/**
	 * Type d'assortiment possible, si type yuto l'assortiment sera publié sur Yuto
	 *
	 * @var array
	 */
	public static $types = array(
		'yuto' => self::TYPE_YUTO,
		'web' => self::TYPE_WEB,
	);

	/**
	 * Permet l'ajout d'un assortiment
	 *
	 * @param string $name Le nom de l'assortiment
	 * @param string $type Le type de l'assortiment yuto ou web
	 * @param boolean $is_published Facultatifs si l'assortiment est actif ou non
	 * @param integer $fls_id Facultatifs identifiant d'une section
	 * @param string $ref_gescom Facultatifs identifiant de la gescom
	 * @return boolean|integer Retourne l'identifiant insérer, false si erreur
	 *
	 * @throws InvalidArgumentException
	 */
	public static function add($name, $type, $is_published = false, $fls_id = null, $ref_gescom=null)
	{
		if (!is_string($name) || !trim($name)) {
			throw new \InvalidArgumentException("name doit être un string");
		}

		if (!is_numeric($type) || !in_array($type, self::$types)) {
			throw new \InvalidArgumentException("type doit être un entier et appartenir à prw_followed_lists::type");
		}

		if (!is_bool($is_published)) {
			throw new \InvalidArgumentException("is_published doit être un boolean");
		}

		if (!is_null($fls_id) && (!is_numeric($fls_id) || $fls_id < 0)) {
			throw new \InvalidArgumentException("fls_id doit être un entier supérieur à 0");
		}

		if (!is_null($ref_gescom) && (!is_string($ref_gescom) || trim($ref_gescom) == '')) {
			throw new \InvalidArgumentException("ref_gescom doit être une chaine de caractère");
		}

		global $config;

		$fields = array(
			'pfl_tnt_id',
			'pfl_name',
			'pfl_type',
			'pfl_is_published',
			'pfl_date_created',
		);

		$values = array(
			$config['tnt_id'],
			'"' . addslashes($name) . '"',
			$type,
			$is_published ? '1' : '0',
			'now()',
		);

		if (!is_null($fls_id)) {
			$fields[] = 'pfl_fls_id';
			$values[] = $fls_id;
		}

		if (!is_null($ref_gescom)) {
			$fields[] = 'pfl_ref_gescom';
			$values[] = '"' . addslashes($ref_gescom) . '"';
		}

		$insert = '
			insert into prw_followed_lists
				(' . implode(', ', $fields) . ')
			values
				(' . implode(', ', $values) . ');
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}

	/**
	 * Cette fonction permet de récupérer un ou plusieurs assortiment
	 *
	 * @param integer|array $id Identifiant ou tableau d'identifiant d'assortiment
	 * @param boolean $is_published Facultatif, Si l'assortiment est publié ou non
	 * @param integer $type Facultatif, identifiant du type 0 => WEB, 1 => YUTO
	 * @param integer $fls_id Facultatif, Identifiant d'une section
	 * @param string $name Facultatif, nom de l'assortiment
	 * @return boolean|resource Retourne un tableau avec les lignes :
	 * 								- id : Identifiant de l'assortiment
	 * 								- name : Nom de l'assortiment
	 * 								- type : Type de l'assortiment
	 * 								- fls_id : Identifiant d'une section
	 * 								- ref_gescom : Identifiant de la gescom
	 * 								- is_published : Si l'assortiment est publié
	 * 								- date_created : Date de creation de l'assortiment
	 * 								- date_modified : Date de dernière modification de l'assortiment
	 *
	 * @throws InvalidArgumentException
	 */
	public static function get($id = null, $is_published = null, $type = null, $fls_id = null, $name = null, $ref_gescom=null)
	{
		if (!is_null($id)) {
			$ids = control_array_integer($id);
			if (!$ids) {
				throw new \InvalidArgumentException("id doit être un entier ou un tableau d'entier");
			}
		}

		if (!is_null($is_published) && !is_bool($is_published)) {
			throw new \InvalidArgumentException("is_published doit être un booléan");
		}

		if (!is_null($type) && (!is_numeric($type) || !in_array($type, self::$types))) {
			throw new \InvalidArgumentException("type doit être un entier et appartenir à prw_followed_lists::type");
		}

		if (!is_null($fls_id) && (!is_numeric($fls_id) || $fls_id < 0)) {
			throw new \InvalidArgumentException("fls_id doit être un entier supérieur à 0");
		}

		if (!is_null($name) && (!is_string($name) || trim($name) == '')) {
			throw new \InvalidArgumentException("name doit être une chaine de caractère");
		}

		if (!is_null($ref_gescom) && (!is_string($ref_gescom) || trim($ref_gescom) == '')) {
			throw new \InvalidArgumentException("ref_gescom doit être une chaine de caractère");
		}

		global $config;

		$select = '
			select pfl_id as id,
				pfl_name as name,
				pfl_type as type,
				pfl_fls_id as fls_id,
				pfl_ref_gescom as ref_gescom,
				pfl_is_published as is_published,
				pfl_date_created as date_created,
				pfl_date_modified as date_modified
			from prw_followed_lists
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_date_deleted is null
		';

		if (!is_null($id)) {
			$select .= ' and pfl_id in (' . implode(', ', $ids) . ') ';
		}

		if (!is_null($type)) {
			$select .= ' and pfl_type=' . $type . ' ';
		}

		if (!is_null($fls_id)) {
			$select .= ' and pfl_fls_id=' . $fls_id . ' ';
		}

		if (!is_null($name)) {
			$select .= ' and pfl_name = "' . addslashes($name) . '" ';
		}

		if (!is_null($ref_gescom)) {
			$select .= ' and pfl_ref_gescom = "' . addslashes($ref_gescom) . '" ';
		}

		if (!is_null($is_published)) {
			$select .= ' and pfl_is_published=' . ($is_published ? 1 : 0) . ' ';
		}

		$select .= '
			order by pfl_name asc
		';

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Alias de get mais que pour récupérer en filtrant par le nom de l'assortiment
	 *
	 * @param string $name Nom de l'assortiment
	 * @return array|null Retourne un tableau avec les lignes suivante sinon null :
	 * 								- id : Identifiant de l'assortiment
	 * 								- name : Nom de l'assortiment
	 * 								- type : Type de l'assortiment
	 * 								- is_published : Si l'assortiment est publié
	 * 								- date_created : Date de creation de l'assortiment
	 * 								- date_modified : Date de dernière modification de l'assortiment
	 *
	 * @throws InvalidArgumentException
	 */
	public static function getByName($name)
	{
		$r_pfl = self::get(null, null, null, null, $name);
		if( $r_pfl && ria_mysql_num_rows($r_pfl) ){
			return ria_mysql_fetch_assoc($r_pfl);
		}

		return null;
	}
	/**
	 * Alias de get mais que pour récupérer en filtrant par la ref gescom de l'assortiment
	 *
	 * @param string $ref_gescom Nom de l'assortiment
	 * @return resource Retourne une resource mysql avec les colonnes suivante :
	 * 								- id : Identifiant de l'assortiment
	 * 								- name : Nom de l'assortiment
	 * 								- type : Type de l'assortiment
	 * 								- is_published : Si l'assortiment est publié
	 * 								- date_created : Date de creation de l'assortiment
	 * 								- date_modified : Date de dernière modification de l'assortiment
	 *
	 * @throws InvalidArgumentException
	 */
	public static function getByRefGescom($ref_gescom)
	{
		return self::get(null, null, null, null, null, $ref_gescom);
	}
	/**
	 * Cette fonction permet de supprimer un ou plusieurs assortiments produits
	 *
	 * @param integer|array $id Identifiant ou tableau d'identifiant d'assortiment
	 * @return boolean Retourne True si la suppression c'est bien passé, false dans le cas contraire.
	 *
	 * @throws InvalidArgumentException
	 */
	public static function delete($id)
	{
		$ids = control_array_integer($id);
		if (!$ids) {
			throw new \InvalidArgumentException("ids doit être un entier ou tableau d'entier");
		}

		global $config;

		$select = '
			update prw_followed_lists
				set pfl_date_deleted = now()
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_id in (' . implode(', ', $ids) . ')
				and pfl_date_deleted is null;
		';

		return ria_mysql_query($select);
	}

	/**
	 * Cette fonction permet de mettre à jours un assortiment
	 *
	 * @param integer $id Identifiant d'un assortiment
	 * @param string $name Facultatif nouveau nom de l'assortiment
	 * @param string $type Faucltatif nouveau type de l'assortiment
	 * @return boolean Retourne true si la mise à jours est un succès, false si $name et $type sont null en même temps ou si il y aeu une erreur d'identifiant.
	 *
	 * @throws InvalidArgumentException
	 */
	public static function update($id, $name = null, $type = null, $fls_id = null)
	{
		if (!is_numeric($id) || $id <= 0) {
			throw new \InvalidArgumentException("id doit être un entier");
		}

		if (is_null($name) && is_null($type) && is_null($fls_id)) {
			throw new \InvalidArgumentException("Aucune information pour la mise à jour");
		}

		$update = array();

		if (!is_null($name) && is_string($name) && trim($name)) {
			$update[] = 'pfl_name = "' . addslashes($name) . '"';
		}

		if (!is_null($type) && is_numeric($type) && in_array($type, self::$types)) {
			$update[] = 'pfl_type = ' . $type;
		}

		if (!is_null($fls_id) && is_numeric($fls_id) && $fls_id >= 0) {
			$update[] = 'pfl_fls_id = ' . $fls_id;
		}

		if (empty($update)) {
			return false;
		}

		global $config;

		$sql = '
			update prw_followed_lists
				set ' . implode(', ', $update) . '
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_id = ' . $id . '
				and pfl_date_deleted is null;
		';

		return ria_mysql_query($sql);
	}

	/**
	 * Cette fonction permet de modifier la date de dernière modification d'un assortiment
	 *
	 * @param integer $id Identifiant d'un assortiment
	 * @return boolean Retourne true si modification avec succès, false dans le cas contraire
	 *
	 * @throws InvalidArgumentException
	 */
	public static function setDateModified($id)
	{
		if (!is_numeric($id) || $id <= 0) {
			throw new \InvalidArgumentException("id doit être un entier");
		}

		global $config;

		$sql = '
			update prw_followed_lists
				set pfl_date_modified = now()
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_id = ' . $id . '
				and pfl_date_deleted is null;
		';

		return ria_mysql_query($sql);
	}
	/**
	 * Cette fonction permet de publié ou dépublié un assortiment
	 *
	 * @param integer $id Identifiant de l'assortiment
	 * @param boolean $publish Si on le publie ou non
	 * @return boolean Retourne true si la publication/depublication est un succès, false dans le cas contraire
	 *
	 * @throws InvalidArgumentException
	 */
	public static function publish($id, $publish = true)
	{
		if (!is_numeric($id) || $id <= 0) {
			throw new \InvalidArgumentException("id doit être un entier");
		}

		if (!is_bool($publish)) {
			throw new \InvalidArgumentException("publish doit être un boolean");
		}

		global $config;

		$sql = '
			update prw_followed_lists
				set pfl_is_published = ' . ($publish ? '1' : '0') . '
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_id = ' . $id . '
				and pfl_date_deleted is null;
		';

		return ria_mysql_query($sql);
	}

	/**
	 * Cette fonction permet de renseigner la ref gescom d'une liste d'assortiment
	 *
	 * @param integer $id Identifiant de la liste
	 * @param string $ref_gescom Référence gescom de la liste
	 * @return boolean Retourne true si succès false dans le cas contraire
	 */
	public static function setRefGescom($id, $ref_gescom)
	{
		if (!is_numeric($id) || $id <= 0) {
			throw new \InvalidArgumentException("id must be a numeric over 0");
		}

		if (!is_string($ref_gescom) || trim($ref_gescom) == '') {
			throw new \InvalidArgumentException("ref_gescom must be a string");
		}

		global $config;

		$sql = '
			update prw_followed_lists
				set pfl_ref_gescom = "' . addslashes($ref_gescom) . '"
			where pfl_tnt_id=' . $config['tnt_id'] . '
				and pfl_id = ' . $id . '
				and pfl_date_deleted is null;
		';

		return ria_mysql_query($sql);
	}
}
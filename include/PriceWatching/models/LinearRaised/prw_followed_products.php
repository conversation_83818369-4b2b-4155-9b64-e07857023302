<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaisedModel
 */

 /** \class prw_followed_products
  * \brief Cette class permet la gestion des opérations CRUD de la table prw_followed_products
  */
class prw_followed_products {
	private static function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}
	/**
	 * Cette fonction permet d'ajouter un produit à l'assortiment
	 *
	 * @param integer $prd_id Identifiant du produit à ajouter
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @param integer $rank Rang du produit dans l'assortiement
	 * @param integer|float $pmc Pmc du produit dans l'assortiment
	 * @param boolean $is_from_competition Facultatif, si c'est un produit de la concurrence ou non
	 * @return array|boolean Retourne un tableau avec :
	 *
	 */
	public static function add($prd_id, $pfl_id, $rank, $pmc=null, $is_from_competition=false) {
		if (!self::validInteger($prd_id)) {
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		if (!self::validInteger($pfl_id)) {
			throw new \InvalidArgumentException("pfl_id doit être un entier");
		}

		if (!self::validInteger($rank)) {
			throw new \InvalidArgumentException("rank doit être un entier");
		}

		if (!is_null($pmc) && !(is_float($pmc) || is_numeric($pmc))) {
			throw new \InvalidArgumentException("pmc doit être un décimal ou un entier");
		}

		if (!is_bool($is_from_competition)) {
			throw new \InvalidArgumentException("is_from_competition doit être un boolean");
		}

		global $config;

		$fields = array(
			'pwf_tnt_id',
			'pwf_prd_id',
			'pwf_pfl_id',
			'pwf_rank',
			'pwf_is_cpt',
			'pwf_date_created',
		);

		$values = array(
			$config['tnt_id'],
			$prd_id,
			$pfl_id,
			$rank,
			$is_from_competition ? '1' : '0',
			'now()',
		);

		if (!is_null($pmc)) {
			$fields[] = 'pwf_pmc';
			$values[] = $pmc;
		}

		$insert = '
			insert into prw_followed_products
				('.implode(', ', $fields).')
			values
				('.implode(', ', $values).')
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}
	/**
	 * Cette fonction permet de récupérer la liste de produit
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @param boolean $is_from_competition Si on récupére tous les produits ou que ce de la compétition ou que ceux du client
	 * @return void
	 */
	public static function get($pfl_id, $is_from_competition=null) {
		if (!self::validInteger($pfl_id)) {
			return false;
		}

		global $config;

		$select = '
			select pwf_id as id,
				pwf_cpt_id as cpt_id,
				pwf_cpt_ref as cpt_ref,
				pwf_disable as disable,
				pwf_prd_id as prd_id,
				pwf_pfl_id as pfl_id,
				pwf_rank as rank,
				pwf_pmc as pmc,
				pwf_is_cpt as is_cpt,
				pwf_date_created as date_created,
				pwf_date_modified as date_modified,
				pwf_date_lastcheck as date_lastcheck
			from prw_followed_products
			where pwf_tnt_id='.$config['tnt_id'].'
				and pwf_pfl_id='.$pfl_id.'
				and pwf_date_deleted is null
		';

		if (!is_null($is_from_competition)) {
			$select .= ' and pwf_is_cpt = '.($is_from_competition?'1':'0');
		}

		$select .= '
			order by pwf_rank asc
		';

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet la suppression des ligne produit lié a un assortiment
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @param integer $prd_id Facultatif, Identifiant du produit
	 * @return boolean Retourne true si suppression avec succès, false dans le cas contraire
	 */
	public static function delete($pfl_id, $prd_id=null) {
		if (!self::validInteger($pfl_id)) {
			return false;
		}

		if( !is_null($prd_id) && !self::validInteger($pfl_id) ){
			return false;
		}

		global $config;

		$delete = '
			update prw_followed_products
				set pwf_date_deleted = now()
			where pwf_tnt_id='.$config['tnt_id'].'
				and pwf_pfl_id='.$pfl_id.'
				and pwf_date_deleted is null
		';

		if( $prd_id !== null ){
			$delete .= '
				and pwf_prd_id='.$prd_id.'
			';
		}

		return ria_mysql_query($delete);
	}


	public function getCountOfUsersLinked($prd_id, $prf_id=null)
	{
		$ar_prd_id = control_array_integer($prd_id);

		if (!$ar_prd_id) {
			throw new \InvalidArgumentException("prd_id doit être un entier ou un tableau d'entier");
		}
		$ar_prf_id = null;
		if (!is_null($prf_id)) {
			$ar_prf_id = control_array_integer($prf_id);
			if (!$ar_prd_id) {
				throw new \InvalidArgumentException("prd_id doit être un entier ou un tableau d'entier");
			}
		}

		global $config;

		$select = '
			select pwf_prd_id as prd_id, count(distinct pfu_usr_id) as "users"
			from prw_followed_products
				join prw_followed_users on pwf_tnt_id=pfu_tnt_id and pwf_pfl_id=pfu_pfl_id
			where pwf_tnt_id='.$config['tnt_id'].'
				and pfu_date_deleted is null
				and pwf_date_deleted is null
				and pwf_prd_id in ('.implode(', ', $ar_prd_id).')
		';
		if (!is_null($ar_prf_id)) {
			$select .= '
				and pfu_prf_id in ('.implode(', ', $ar_prf_id).')
			';
		}
		$select .= '
			group by pwf_prd_id
		';

		return ria_mysql_query($select);
	}
}
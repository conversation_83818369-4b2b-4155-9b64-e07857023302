<?php
namespace Riashop\PriceWatching\models\LinearRaised;

use DateTime;
use InvalidArgumentException;
/**
 * \ingroup LinearRaisedModel
 */

/** \class LinearRaisedGetter
 * \brief Cette class fournie une api simple pour la récupération des relevés linéaires
 */
class LinearRaisedGetter
{
	const DATE_FORMAT = 'Y-m-d'; ///< Format de la date pour le filtrage
	private $id_list = array(); ///< Liste d'identifiant de relevé pour filter
	private $usr_id_list = array(); ///< Liste d'identifiant utilisateur
	private $author_id_list = array(); ///< Liste d'identifiant d'auteur, représentant
	private $pfl_id_list = array(); ///< liste d'identifiant d'assortiement
	private $group_id_list = array(); ///< Liste d'identifiant de groupe de relevé linéaire
	private $ref_gescom_list = array(); ///< Liste des références client
	private $date_from = null; ///< Date de début du filtre
	private $date_to = null; ///< Date de fin du filtre
	private $distinct_users = false; ///< Avoir que les utilisateurs unique
	private $central_id = null; ///< Avoir que les utilisateurs unique
	private $force_centrals = false; ///< Forcer la centrale d'achat sur les comptes client
	private $only_ref_gescom = false; ///< Avoir que la ref gescom
	private $usr_fld = []; ///< Filtre les comptes clients sur des champs avancés

	public function __construct()
	{
		global $config;
		$this->force_centrals = ria_array_get($config, 'linear_raised_stats_force_centrals', false);
	}
	/**
	 * Cette fonction permet de récupérer que la ref gescom
	 *
	 * @param boolean $only Récupérer que la ref gescom
	 * @return LinearRaisedGetter Retourne l'instance en court
	 */
	public function onlyRefGescom($only=true)
	{
		$this->only_ref_gescom = (bool) $only;

		return $this;
	}
	/**
	 * Définie la récupération que des compte unique
	 *
	 * @param boolean $with
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withDistinctUsers($with)
	{
		$this->distinct_users = $with;
		return $this;
	}

	/**
	 * Cette fonction permet de filtrer par une liste d'identifiant de relevé
	 *
	 * @param array|integer $id Identifiant ou tableau d'identifiant de relevé
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withId($id){
		$controlled_id = $this->control_array_integer($id);
		$this->id_list = array_merge($this->id_list, $controlled_id);
		return $this;
	}
	/**
	 * Cette fonction permet de filtrer par une liste d'identifiant d'utilisateur
	 *
	 * @param array|integer $id Identifiant ou tableau d'identifiant d'utilisateur
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withUserId($id){
		$controlled_id = $this->control_array_integer($id);
		$this->usr_id_list = array_merge($this->usr_id_list, $controlled_id);
		return $this;
	}

	/**
	 * Cette fonction permet de filtrer par une liste d'identifiant d'auteur, représentant/commercial
	 *
	 * @param array|integer $id Identifiant ou tableau d'identifiant d'auteur, représentant/commercial
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withAuthorId($id){
		$controlled_id = $this->control_array_integer($id);
		$this->author_id_list = array_merge($this->author_id_list, $controlled_id);
		return $this;
	}

	/**
	 * Cette fonction permet de filtrer par une liste d'identifiant d'assortiment
	 *
	 * @param array|integer $id Identifiant ou tableau d'identifiant d'assortiment
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withPflId($id){
		$controlled_id = $this->control_array_integer($id);
		$this->pfl_id_list = array_merge($this->pfl_id_list, $controlled_id);
		return $this;
	}

	/**
	 * Cette fonction permet de filtrer par une liste d'identifiants de groupe
	 *
	 * @param array|integer $id Identifiant ou tableau d'identifiant de groupe
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withGroupId($id){
		$controlled_id = $this->control_array_integer($id);
		$this->group_id_list = array_merge($this->group_id_list, $controlled_id);
		return $this;
	}

	/**
	 * Cette fonction permet de filtrer par une liste de référence gescom
	 *
	 * @param array|integer $ref Identifiant ou tableau de référence gescom
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function withRefGescom($ref){
		$controlled_ref = $this->control_array_string($ref);
		$this->ref_gescom_list = array_merge($this->ref_gescom_list, $controlled_ref);
		return $this;
	}
	/**
	 * Cette fonction permet de filtrer par une date de début de période
	 *
	 * @param DateTime $from Date de début de période de filtrage
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function from(DateTime $from){
		$this->date_from = $from;
		return $this;
	}
	/**
	 * Cette fonction permet de filtrer par une date de fin de période
	 *
	 * @param DateTime $to Date de fin de période de filtrage
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function to(DateTime $to){
		$this->date_to = $to;
		return $this;
	}
	/**
	 * Cette fonction permet de filtrer par une date de début et de fin de période
	 *
	 * @param DateTime $from Date de début de période de filtrage
	 * @param DateTime $to Date de fin de période de filtrage
	 * @return LinearRaisedGetter Retourne l'instance
	 */
	public function fromTo(DateTime $from, DateTime $to){
		return $this->from($from)->to($to);
	}

	/** Cette fonction Permet de filtrer le résultat sur la central
	 *
	 * @param string $central Identifiant de la central
	 * @return LinearRaisedGetter
	 */
	public function withCentral($central)
	{
		if (!is_string($central) || !trim($central)) {
			throw new InvalidArgumentException("central doit être une chaine de caractère non vide");
		}

		$this->central_id = $central;

		return $this;
	}
	/**
	 * Cette fonction génère le sql et envoie la requête
	 *
	 * @return resource|boolean Retourne une ressource MySql avec les champs suivant :
	 * 					- id : Identifiant du relevé
	 *					- author_id : Identifiant de l'auteur du rapport
	 *					- usr_id : Identifiant du client
	 *					- pfl_id : identifiant de l'assortiment
	 *					- group_id : identifiant du groupe
	 *					- date_created : date de creation
	 *					- date_modified : date de modification
	 *					- total_dn : total de produit sur le relevé
	 *					- total_dn_section : total de produit dans le rayon
	 *					- ref_gescom : référence du linéaire en gescom
	 */
	public function query(){
		global $config;


		if( $this->distinct_users ){
			$select = '
				select count(distinct plr_usr_id) as "count"
			';
		}else{
			if( $this->only_ref_gescom ){
				$select = '
					select plr_ref_gescom as ref_gescom
				';
			}else{
				$select = '
					select plr_id as id,
						plr_author_id as author_id,
						plr_usr_id as usr_id,
						plr_pfl_id as pfl_id,
						plr_group_id as group_id,
						plr_total_dn as total_dn,
						plr_total_dn_section as total_dn_section,
						plr_ref_gescom as ref_gescom,
						plr_date_created as date_created,
						plr_date_modified as date_modified
				';
			}
		}

		$select .= '
			from prw_linear_raised
		';

		if( $this->force_centrals ||  !is_null($this->central_id) ){
			$select .= '
				join fld_object_values on (
					pv_tnt_id = '.$config['tnt_id'].'
					and pv_fld_id = '._FLD_USR_CENTRAL.'
					and pv_obj_id_0 = plr_usr_id
					'.(!is_null($this->central_id) ? ' and pv_value = "'.addslashes($this->central_id).'"' : ' and pv_value != ""').'
				)
			';
		}

		$select .= '
			where plr_tnt_id=' . $config['tnt_id'] . '
				and plr_date_deleted is null
		';

		if( is_array($this->usr_fld) ){
			foreach( $this->usr_fld as $fld_id=>$fld_val ){
				if( trim($fld_val) == '' ){
					continue;
				}

				$fld_val = urldecode( $fld_val );

				$select .= '
					and exists (
						select 1
						from fld_object_values
						where pv_tnt_id = '.$config['tnt_id'].'
							and pv_fld_id = '.$fld_id.'
							and pv_obj_id_0 = plr_usr_id
							and pv_value = "'.addslashes( $fld_val ).'"
					)
				';
			}
		}

		if( !empty($this->id_list) ){
			$select .= 'and plr_id in (' . implode(', ', $this->id_list) . ')';
		}

		if( !empty($this->usr_id_list) ){
			$select .= ' and plr_usr_id in (' . implode(', ', $this->usr_id_list) . ')';
		}

		if( !empty($this->author_id_list) ){
			$select .= ' and plr_author_id in (' . implode(', ', $this->author_id_list) . ')';
		}

		if( !empty($this->pfl_id_list) ){
			$select .= ' and plr_pfl_id in (' . implode(', ', $this->pfl_id_list) . ')';
		}

		if( !empty($this->group_id_list) ){
			$select .= ' and plr_group_id in (' . implode(', ', $this->group_id_list) . ')';
		}

		if( $this->date_from !== null ){
			$select .= ' and date(plr_date_created) >= "'.$this->date_from->format(self::DATE_FORMAT).'"';
		}

		if( $this->date_to !== null ){
			$select .= ' and date(plr_date_created) <= "'.$this->date_to->format(self::DATE_FORMAT).'"';
		}

		$group = array();

		if( !empty($this->ref_gescom_list) ){
			$select .= '
				and plr_ref_gescom in ("' . implode('", "', $this->ref_gescom_list) . '")
			';
		}

		if( !empty($group) ){
			$select .= '
				group by '.implode(', ', $group).'
			';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet de vérifier si c'est un entier ou tableau d'entier et retourne forcément un tableau
	 * si erreur il y aura une exception
	 *
	 * @param array|integer $integer Entier ou tableau d'entier
	 * @return array Retourne un tableau d'entier
	 *
	 * @throws InvalidArgumentException Lance une exception si paramètre non valide
	 */
	private function control_array_integer($integer){
		$controlled_integer = control_array_integer($integer);
		if( !$controlled_integer ){
			throw new InvalidArgumentException("le paramêtre doit être un entier ou un tableau d'entier");
		}

		return $controlled_integer;
	}
	/**
	 * Cette fonction permet de vérifier si c'est une chaine de caractère ou tableau de chaine de caractère et retourne forcément un tableau
	 * si erreur il y aura une exception
	 *
	 * @param array|string $integer Entier ou tableau d'entier
	 * @return array Retourne un tableau d'entier
	 *
	 * @throws InvalidArgumentException Lance une exception si paramètre non valide
	 */
	private function control_array_string($string){
		if( is_string($string) ){
			return array(addslashes($string));
		}elseif( is_array($string) ){
			return array_map(function($str){
				return addslashes($str);
			}, $string);
		}else{
			throw new InvalidArgumentException("le paramêtre doit être une chaine de caractère ou un tableau de chaine de caractère");
		}
	}

	/** Cette fonction permet de filtrer le résultat sur des comptes clients correspondants à des valeurs avancés
	 * 	@param array $fields Tableau clé/valeur sur des champs avancés (clé = fld_id et valeur = valeur de restriction)
	 * 	@return LinearRaisedGetter
	 */
	public function withUserFields( $fields ){
		if( !is_array($fields) ){
			throw new InvalidArgumentException('usr_fld doit être un tableau');
		}

		$this->usr_fld = $fields;
		return $this;
	}
}
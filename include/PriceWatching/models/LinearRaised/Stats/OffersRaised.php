<?php
namespace Riashop\PriceWatching\models\LinearRaised\Stats;
use DateTime;
use Exception;
use InvalidArgumentException;
/**
 * \ingroup LinearRaised
 */
/**
 * \class OffersRaised
 * \brief OffersRaised Cette class permet réalisé une requête pour les statistiques des relevés linéaires
 */
class OffersRaised
{
	const MODE_STATS = 'STATS';

	const MODE_DASHBOARD = 'DASHBOARD';

	private $sellers = array(); ///< Tableau d'identifiant des représentants auteur d'un relevé pour filtrer le résultat

	private $customers = array(); ///< Tableau d'identifiant des comptes utilisateurs lié a un relevé pour filtrer le résultat

	private $central_id = null; ///< Identifiant de la central

	private $periodStart = null; ///< Date de début de la période de filtrage du résultat

	private $periodEnd = null; ///< Date de fin de la période de filtrage du résultat

	private $page = 1; ///< Numéro de la page du résultat

	private $perPage = 50; ///< Nombre de résultat par page défaut 50

	private $sorting = array(); ///< Tableau contenant le tri a réalisé pour la requête

	private $product_ref = null; ///< Référence ou début de référence pour avoir le réusltat sur un ou des produits spécifiques

	private $allowed_sorting = array( ///< Clés valident pour le tri
		'ref',
		'dn',
		'name',
		'count',
		'store',
		'total_stores',
		'facings',
		'avg_price',
		'out_of_stock'
	);

	private $allowed_sorting_dashboard = array(///< Clés valident pour le tri
		'stores',
		'central',
		'total_stores',
		'cover',
	);

	private $force_centrals = false; ///< Détermine si on force les stats pour les clients lié une une centrale

	private $mode = null; ///< Détermine le mode de requête pour l'interface de dashboard ou de stats influence le tri

	private $with_seller_parents = false; ///< Détermine si on regarde si l'on récupère les relevés linéaires faient par les représentants parents

	private $usr_fld = []; ///< Filtre les comptes clients sur des champs avancés

	/** Cette fonction permet d'initialisé les filtres principaux
	 *
	 * @param mixed $sellers Facultatif, Identifiant ou tableau d'identifiant de représentant
	 * @param mixed $customers Facultatif, Identifiant ou tableau d'identifiant de compte utilisateur gu_users
	 * @param mixed $periodStart Facultatif, Date de début de la période
	 * @param mixed $periodEnd Facultatif, Date de fin de la période
	 * @return void
	 */
	public function __construct($sellers=null, $customers=null, $periodStart=null, $periodEnd=null)
	{
		global $config;

		$this->with_seller_parents = isset($config['linear_raised_fld_can_get_parents']) && is_numeric($config['linear_raised_fld_can_get_parents']) && $config['linear_raised_fld_can_get_parents'] > 0;

		if (!is_null($sellers)) {
			$this->withSellers($sellers);
		}

		if (!is_null($customers)) {
			$this->withCustomers($customers);
		}

		if (is_null($periodStart)) {
			$periodStart = new DateTime('1 month ago');
		}

		if (is_null($periodEnd)) {
			$periodEnd = new DateTime('now');
		}

		$this->mode = self::MODE_STATS;

		$this->force_centrals = ria_array_get($config, 'linear_raised_stats_force_centrals', false);

		$this->forPeriodStart($periodStart);
		$this->forPeriodEnd($periodEnd);
	}

	public function setModeDashboard()
	{
		$this->mode = self::MODE_DASHBOARD;

		return $this;
	}

	public function setModeStats()
	{
		$this->mode = self::MODE_STATS;

		return $this;
	}
	/** Cette fonction Permet de filtrer le résultat sur la central
	 *
	 * @param string $central Identifiant de la central
	 * @return OffersRaised
	 */
	public function withCentral($central)
	{
		if (!is_string($central) || !trim($central)) {
			throw new InvalidArgumentException("central doit être une chaine de caractère non vide");
		}

		$this->central_id = $central;

		return $this;
	}

	/** Cette fonction permet de filtrer le résultat sur des comptes clients correspondants à des valeurs avancés
	 * 	@param array $fields Tableau clé/valeur sur des champs avancés (clé = fld_id et valeur = valeur de restriction)
	 * 	@return OffersRaised
	 */
	public function withUserFields( $fields ){
		if( !is_array($fields) ){
			throw new InvalidArgumentException('usr_fld doit être un tableau');
		}

		$this->usr_fld = $fields;
		return $this;
	}

	/** Cette fonction permet d'initialisé le filtre sur les représentants / auteur du relévé
	 *
	 * @param integer|array $seller_id Identifiant ou tableau d'identifiant de représentant
	 * @return OffersRaised Retourne L'instance
	 */
	public function withSellers($seller_id)
	{
		$ids = control_array_integer($seller_id);

		if (!$ids) {
			throw new InvalidArgumentException("seller_id doit être un entier ou un tableau d'entier");
		}

		global $config;

		$tmp_sellers = $ids;
		// On regarde si l'on récupère les relevés linéaires faient par les représentants parents
		if( $this->with_seller_parents ){
			$tmp_sellers = array();
			foreach( $ids as $one_seller ){
				// Si la valeur déterminant pour quel représentant on récupère les parent est à Oui
				if( in_array(fld_object_values_get($one_seller, $config['linear_raised_fld_can_get_parents'], '', false, true), $config['fld_vals_yes']) ){
					// Récupération des représentant parents
					$rel = rel_relations_get_parents(REL_SELLER_HIERARCHY, $one_seller);

					if( is_array($rel) && count($rel) ){
						foreach( $rel as $r ){
							$tmp_sellers[] = $r['obj'][0];
						}
					}
				}
			}
		}

		$this->sellers = empty($tmp_sellers) ? $ids : $tmp_sellers;

		return $this;
	}

	/** Cette fonction permet d'initialisé le filtre sur les utilisateurs lié au relevé
	 *
	 * @param integer|array $customer_id Identifiant ou tableau d'identifiant de compte utilisateur gu_users
	 * @return OffersRaised
	 */
	public function withCustomers($customer_id)
	{
		$ids = control_array_integer($customer_id);
		if (!$ids) {
			throw new InvalidArgumentException("customer_id doit être un entier ou un tableau d'entier");
		}

		$this->customers = $ids;

		return $this;
	}

	/** Cette fonction ermet d'initialisé la date de début de la période pour le filtre
	 *
	 * @param DateTime $start Date de début de la période
	 * @return OffersRaised
	 */
	public function forPeriodStart(DateTime $start)
	{
		$this->periodStart = $start;

		return $this;
	}

	/** Cette fonction permet d'initialisé la date de fin de la période pour le filtre
	 *
	 * @param DateTime $end Date de fin de la période
	 * @return OffersRaised
	 */
	public function forPeriodEnd(DateTime $end)
	{
		$this->periodEnd = $end;

		return $this;
	}

	/** Cette fonction permet d'initialisé le filtre sur la référence du produit
	 *
	 * @param string $product_ref Chaine de caractère représentant la référence à filtrer
	 * @return OffersRaised
	 */
	public function productRefLike($product_ref)
	 {
		 if (!is_string($product_ref) || !trim($product_ref)) {
			 throw new InvalidArgumentException("product_ref doit être un string");
		 }

		 $this->product_ref = $product_ref;

		 return $this;
	 }
	/** Cette fonction permet d'initialisé le tri du résultat
	 *
	 * @param array $sort Tableau contenant les colonnes pour le tri du résultat
	 * @return OffersRaised
	 */
	public function sortBy(array $sort)
	{
		$allowed_sorting = $this->allowed_sorting;
		if( $this->mode == self::MODE_DASHBOARD ){
			$allowed_sorting = $this->allowed_sorting_dashboard;
		}

		foreach ($sort as $key => $value) {
			$continue = true;

			if (in_array($key, $allowed_sorting)) {
				$continue = false;
			}

			if (!in_array(strtolower($value), array('asc', 'desc'))) {
				$continue = true;
			}

			if ($continue) {
				continue;
			}


			$this->sorting[$key] = strtolower($value);
		}

		return $this;
	}

	/**
	 * Setter de la pagination
	 *
	 * @param integer $page Facultatif, page du résultat
	 * @param integer $per_pages Facultatif, nombre de résultat par page
	 * @return OffersRaised
	 */
	public function paginate($page=1, $per_pages=50)
	{
		if (!is_numeric($page) || $page <= 0) {
			throw new InvalidArgumentException("page doit être un entier");
		}

		if (!is_numeric($per_pages) || $per_pages <= 0) {
			throw new invalidArgumentException("per_pages doit être un entier");
		}

		$this->page = $page;
		$this->perPage = $per_pages;

		return $this;
	}

	/**
	 * Nombre total de produits relevés en fonction des conditions
	 *
	 * @return integer Retourne le nombre total de produits relevés
	 */
	public function count()
	{
		global $config;

		$select = '
			select count(distinct ofr.ofr_prd_id) as "count"
			from prw_offers as ofr
				join prw_linear_raised as linear_raised on (plr_tnt_id=ofr.ofr_tnt_id and plr_id=ofr.ofr_plr_id)
				'.(is_null($this->product_ref) ? '' : 'join prd_products on prd_tnt_id=ofr.ofr_tnt_id and prd_id=ofr.ofr_prd_id').'
			where ofr.ofr_tnt_id='.$config['tnt_id'] . '
				'.$this->getWhereSql('ofr', !is_null($this->product_ref) ) . '
		';

		$r = ria_mysql_query($select);

		if (!$r) {
			throw new Exception(ria_mysql_error());
		}

		$one = ria_mysql_fetch_assoc($r);

		return $one['count'];
	}

	/**
	 * Permet de générer et exécuté le sql de la récupération du stats de relevé linéaire
	 *
	 * @return resource Retourne un résultat mysql avec les colonne suivante :
	 * 				- prd_id : Identifiant du produit
	 * 				- is_sync : Si le produit est synchronisé ou non
	 * 				- ref : Référence du produit
	 * 				- name : Nom du produit
	 * 				- store : nombre présence magasin
	 * 				- store_total : Nombre de magasin unique relevé
	 * 				- dn : présence magasin sur le nombre de magasin total
	 * 				- facings : Nombre total de facing sur l'ensemble des relevés
	 * 				- level_n : Nombre de produit au total au niveau 1
	 * 				- avg_price : Le prix moyen relevé
	 * 				- out_of_stock : le taux d'appararition sur ce niveaux en fonction du total d'apparition
	 */
	public function query()
	{
		global $config;

		$select = '
			select
				prd_id,
				prd_is_sync as is_sync,
				prd_ref as ref,
				ifnull(prd_name, prd_title) as name,
				SUM(ofr.ofr_count) as "count",
				count(distinct (if(ofr.ofr_facings > 0,ofr_usr_id, null))) as store,
				count(distinct ofr_usr_id) as store_total,
				(count(distinct (if(ofr.ofr_facings > 0,ofr_usr_id, null))) / count(distinct ofr_usr_id)) as dn,
				sum(ofr.ofr_facings) as facings,
				(
					select max(pwf_rank)
					from prw_followed_products
						join prw_followed_lists on pwf_tnt_id=pfl_tnt_id and pwf_pfl_id=pfl_id
					where pwf_tnt_id=ofr.ofr_tnt_id
						and pfl_is_published=1
						and pwf_date_deleted is null
						and pwf_prd_id=prd_id
						and (pwf_cpt_id=0 or pwf_cpt_id is null)
						and pwf_pfl_id is not null
				) as rank,
				sum(ofr.ofr_landedprice) / count(ofr.ofr_id) as avg_price,
				sum(if(COALESCE(pst_ps_id, 0) = 2, 1, 0)) / count(ofr.ofr_id) as out_of_stock
			from prw_offers as ofr
				join (
          select plr_id, plr_date_deleted, plr_author_id
          from prw_linear_raised
          where plr_tnt_id = '.$config['tnt_id'].'
            and plr_date_deleted is null
          group by plr_author_id, plr_usr_id, plr_pfl_id, date(plr_date_created)
          order by max(plr_date_created)
				) as linear_raised on (linear_raised.plr_id = ofr.ofr_plr_id)
				left join prw_offers_states on pst_tnt_id=ofr.ofr_tnt_id and pst_ofr_id=ofr.ofr_id
				left join prd_products on prd_tnt_id=ofr.ofr_tnt_id and prd_id=ofr.ofr_prd_id
			where ofr.ofr_tnt_id='.$config['tnt_id'].'
				'.$this->getWhereSql().'
			';



			$select .= '
			group by ofr.ofr_prd_id
			'.$this->orderBy().'
			limit '.($this->page - 1) * $this->perPage.', '.$this->perPage.'
		';

		$r = ria_mysql_query($select);

		if (!$r) {
			throw new Exception(ria_mysql_error());
		}

		return $r;
	}
	/**
	 * Cette fonction permet de réalisé une requête pour récupérer le nombre de magasin ayant été relevé
	 * !pour utiliser le order by mettre la classe en mode dashboard setModeDashboard
	 * @return resource Retourne une resource SQL
	 */
	public function queryDashboard()
	{
		global $config;

		$sql = '
			select p.central as central, sum(raised) as stores, count(distinct store) as total_stores, sum(raised)/count(distinct store) as cover
			from (select pv_value as central,
				(
				select count(distinct plr_usr_id) as stores
				from prw_linear_raised
				where plr_tnt_id=pv_tnt_id
					and plr_usr_id=pv_obj_id_0
					and plr_date_deleted is null
			';

			if (!empty($this->sellers)) {
				$sql .= '
					and plr_author_id in (' . implode(', ', $this->sellers) . ')
				';
			}

			$sql .= '
					and date(plr_date_created) >= "' . $this->periodStart->format('Y-m-d') . '"
					and date(plr_date_created) <= "' . $this->periodEnd->format('Y-m-d') . '"
				) as raised,
				pv_obj_id_0 as store
			from fld_object_values
			where pv_tnt_id=' . $config['tnt_id'] . '
				and pv_fld_id='._FLD_USR_CENTRAL.'
				and pv_value!=""
			';

			if (!empty($this->customers)) {
				$sql .= '
					and pv_obj_id_0 in (' .implode(', ', $this->customers) . ')
				';
			}
		$sql .= '
			) as p
			group by p.central
			' . $this->orderBy() . '
		';

		return ria_mysql_query($sql);
	}
	/**
	 * Cette fonction permet de récupérer le nombre de compte client lié a des centrales et via un filtre secondaire sur certain id
	 *
	 * @return resource Retourne un résultat mysql avec en clé count pour le nombre de compte client
	 */
	public function queryTotalUsers()
	{
		global $config;

		$select = '
			select count(distinct pv_obj_id_0) as "count"
			from
				fld_object_values
			where
				pv_tnt_id = ' . $config['tnt_id'] . '
				and pv_fld_id = '._FLD_USR_CENTRAL.'
				'.(!is_null($this->central_id) ?
					' and pv_value = "'.addslashes($this->central_id).'"' : ' and pv_value != ""').'
		';

		if (!empty($this->customers)) {
			$select .= '
				and pv_obj_id_0 in (' .implode(', ', $this->customers) . ')
			';
		}


		$r = ria_mysql_query($select);

		if (!$r) {
			throw new Exception(ria_mysql_error());
		}

		return $r;
	}

	/**
	 * Permet de générer le trie de la requête
	 *
	 * @return string retourne le ssql du tri (order by)
	 */
	private function orderBy()
	{
		$order_by = '';
		$sort = array();
		$allowed_sorting = $this->allowed_sorting;
		if( $this->mode == self::MODE_DASHBOARD ){
			$allowed_sorting = $this->allowed_sorting_dashboard;
		}
		foreach ($this->sorting as $key => $value) {

			$continue = true;

			if (in_array($key, $allowed_sorting)) {
				$continue = false;
			}

			if (!$continue) {
				$sort[] = $key . ' ' . $value;
			}
		}
		if (!empty($sort)) {
			$order_by = 'order by ' . implode(', ', $sort);
		}

		return $order_by;
	}

	/**
	 * Cette fonction permet de générer les filtre du where en fonction de la configuration de la requête
	 *
	 * @return string les conditions where de la requête
	 */
	private function getWhereSql($prefix='ofr', $prd_product_join=true)
	{
		global $config;

		$sql = '
			and ('.$prefix.'.ofr_cpt_id is null or '.$prefix.'.ofr_cpt_id = 0)
			and '.$prefix.'.ofr_date_deleted is null
			and date('.$prefix.'.ofr_date_created) >= "' . $this->periodStart->format('Y-m-d') . '"
			and date('.$prefix.'.ofr_date_created) <= "' . $this->periodEnd->format('Y-m-d') . '"
			and linear_raised.plr_date_deleted is null
		';

		if (!empty($this->sellers)) {
			$sql .= '
				and plr_author_id in (' . implode(', ', $this->sellers) . ')
			';
		}

		if (!empty($this->customers)) {
			$sql .= '
				and '.$prefix.'.ofr_usr_id in (' .implode(', ', $this->customers) . ')
			';
		}

		if ($prd_product_join && !is_null($this->product_ref)) {
			$sql .= '
				and lower(prd_ref) like "%'.addslashes($this->product_ref).'%"
			';
		}

		if( $this->force_centrals || !is_null($this->central_id) ){
			$sql .= '
				and exists (
					select 1
					from fld_object_values
					where pv_tnt_id = '.$config['tnt_id'].'
						and pv_fld_id = '._FLD_USR_CENTRAL.'
						and pv_obj_id_0 = '.$prefix.'.ofr_usr_id
						'.( !is_null($this->central_id) ? ' and pv_value = "'.addslashes( $this->central_id ).'"' : '' ).'
				)
			';
		}

		if( is_array($this->usr_fld) ){
			foreach( $this->usr_fld as $fld_id=>$fld_val ){
				if( trim($fld_val) == '' ){
					continue;
				}

				$fld_val = urldecode( $fld_val );

				$sql .= '
					and exists (
						select 1
						from fld_object_values
						where pv_tnt_id = '.$config['tnt_id'].'
							and pv_fld_id = '.$fld_id.'
							and pv_obj_id_0 = '.$prefix.'.ofr_usr_id
							and pv_value = "'.addslashes( $fld_val ).'"
					)
				';
			}
		}


		return $sql;
	}
}

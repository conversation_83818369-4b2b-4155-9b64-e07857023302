<?php
/**
 * \ingroup PriceWatchingModel
 */
/** \class prw_followed_products
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_followed_products
 */
class prw_followed_products {
	/**	Cette fonction permet d'ajouter un produit à surveiller dans la table
	 *
	 *	@param int $prd_id  Obligatoire, identifiant produit
	 *	@param int $cpt_id  Obligatoire, identifiant concurrent
	 *	@param string $ref     Obligatoire, référence produit chez le concurrent
	 *	@param $disable Obligatoire, si le produit est activé pour la veille tarifaire "0" ou
	 * désactivé "1"
	 *
	 *	@return bool    false si erreur, sinon true
	 */
	public function prw_followed_products_add( $prd_id, $cpt_id, $ref, $disable ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 || is_null( $ref ) || !is_numeric( $disable ) ){
			return false;
		}

		$values = $config['tnt_id'].', '.$cpt_id.', '.$prd_id.', "'.$ref.'", '.$disable.', now()';
		$sql = 'insert into prw_followed_products (pwf_tnt_id, pwf_cpt_id, pwf_prd_id, pwf_cpt_ref, pwf_disable, pwf_date_created)
        values ('.$values.')';

		ria_mysql_query( $sql );

		return true;
	}


	/**	Cette fonction permet de récupérer les informations pour un produit
	 *
	 *	@param int $prd_id    Obligatoire, identifiant produit
	 *	@param int $cpt_id    Obligatoire, identifiant concurrent
	 *	@param null $disable   Optionnel, paramètre valide "1","0"
	 *	@param bool $last      Optionnel, true ou false, récupère le dernier produit suivi créé
	 *
	 *	@return array|bool    retourne false si erreur, sinon un tableau avec les produits suivis
	 *                       chaque produit possède le jeux de valeur suivant :
	 *                       -tnt_id        Identifiant tenants
	 *                       -cpt_id        Identifiant concurrent
	 *                       -prd_id        Identifiant produit
	 *                       -ref            Référence du produit chez le concurrent
	 *                       -disable        Identifiant d'activation
	 *                       -last_check    Date de dernière mise à jour
	 *                       -barcode        Code barre du produit
	 */
	public function prw_followed_products_get( $prd_id, $cpt_id, $disable = null, $last = false ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		$sql = 'select pwf_tnt_id as tnt_id, pwf_cpt_id as cpt_id, pwf_prd_id as prd_id, pwf_cpt_ref as ref, pwf_disable as disable, pwf_date_lastcheck as last_check, prd_products.prd_barcode as barcode
        from prw_followed_products
        left join prd_products on prd_products.prd_id = prw_followed_products.pwf_prd_id
        where pwf_prd_id='.$prd_id.'
        and pwf_tnt_id='.$config['tnt_id'].'
        and  pwf_cpt_id ='.$cpt_id;

		if( !is_null( $disable ) ){
			$sql .= ' and pwf_disable='.$disable;
		}
		if( $last ){
			$sql .= ' order by pwf_date_lastcheck ASC';
		}

		$r = ria_mysql_query( $sql );
		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ){
			$rst[] = $row;
		}

		return $rst;
	}

	/** Cette fonction permet de récupérer le champs "disable"  d'un produit
	 *
	 * @param $prd_id    Obligatoire, identifiant produit
	 * @param $cpt_id    Obligatoire, identifiant concurrent
	 *
	 * @return bool|null    retourne false si erreur, retourne le champ disable "1" ou  "0", si
	 * inexistant retourne null.
	 */
	public function prw_followed_products_get_disable( $prd_id, $cpt_id ){

		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}
		global $config;
		$sql = '
          select pwf_disable as disable
          from prw_followed_products
          where pwf_prd_id = '.$prd_id.'
          and pwf_cpt_id = '.$cpt_id.'
            and pwf_tnt_id = '.$config['tnt_id'].'
        ';


		$r = ria_mysql_query( $sql );

		if( $row = ria_mysql_fetch_row( $r ) ){
			return $row[0];
		}else{
			return null;
		}
	}
	/** Cette fonction permet d'ajouter ou de supprimer un produit à une sélection personaliser
	 *  @param $prd_id Identifiant du produit
	 *  @param $is_selection facultatif, defaut a false ne l'ajoute pas a la selection
	 *
	 *	@return bool True si le produit et ajouter a la sélection false
	 */
	public function prw_followed_products_set_is_selection( $prd_id, $is_selection=false){
		if( !is_numeric( $prd_id ) || $prd_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			update prw_followed_products
			set pwf_is_selection='.($is_selection ? 1 : 0).'
			where pwf_prd_id = '.$prd_id.'
				and pwf_cpt_id = '.PRW_CLIENT.'
				and pwf_tnt_id = '.$config['tnt_id'].'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de vérifier si un produit et dans une sélection
	 *	@param $prd_id Identifiant du produit
	 *
	 *	@return bool true si dans la sélection, false si le contraire
	 */
	public function prw_followed_products_is_selection( $prd_id ){

		if( !is_numeric($prd_id) || $prd_id<= 0 ){
			return false;
		}

		global $config;

		$sql = '
			select pwf_is_selection
			from prw_followed_products
			where pwf_tnt_id='.$config['tnt_id'].'
			  and pwf_cpt_id='.PRW_CLIENT.'
			  and pwf_prd_id='.$prd_id.'
			  and pwf_is_selection = 1
		';

		$r = ria_mysql_query($sql);

		if( !$r || !ria_mysql_num_rows($r) ){
			return false;
		}

		return ria_mysql_result($r, 0, 0);
	}


	/** Cette fonction permet de récupérer les produits suivis pour le prix dans une catégorie.
	 *	@param $cat_id Obligatoire, identifiant d'une catégories
	 *	@param $is_selection Optionnel, par défaut ignoré, mettre True pour récupérer les produits faisant parti de la sélection personnalisé
	 *	@return resource Un résultat MySQL contenant :
	 *				- pwf_is_selection : si oui ou non fait parti de la sélection
	 */
	public function prw_followed_products_get_from_cat( $cat_id, $is_selection=false ){
		if( !is_numeric($cat_id) || $cat_id<= 0 ){
			return false;
		}

		global $config;

		$sql = '
			select pwf_is_selection
		';
		$categories = prd_categories_childs_get_list($cat_id);
		if( is_array($cat_id) ){
			$categories = $categories ? implode(',',$cat_id).','.$categories : implode(',',$cat_id);
		}else{
			$categories = $categories ? $cat_id.','.$categories : $cat_id;
		}
		$sql .= '
			from prw_followed_products as prd
			inner join prd_classify on (prd.pwf_prd_id=cly_prd_id and cly_tnt_id='.$config['tnt_id'].' and cly_cat_id in ('.$categories.')  )
		';

		$sql .= '
			join prd_products on ( cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id )
			where pwf_tnt_id='.$config['tnt_id'].'
			  and pwf_cpt_id='.PRW_CLIENT.'
			  and pwf_is_selection = 1
			  and prd_date_deleted is null
		';
		if( $is_selection ){
			$sql .= '
				and pwf_is_selection = 1
			';
		}

		$sql .= ' group by pwf_prd_id ';
		$r = ria_mysql_query($sql);

		if( !$r || !ria_mysql_num_rows($r) ){
			return false;
		}

		return $r;
	}

	/** Cette fonction permet de récupérer l'identifiant d'un produit chez le concurrent
	 *
	 * @param $prd_id    Obligatoire, identifiant produit
	 * @param $cpt_id    Obligatoire, identifiant concurrent
	 *
	 * @return bool|null    retourne false si erreur, retourne le champ disable "1" ou  "0", si
	 * inexistant retourne null.
	 */
	public function prw_followed_products_get_cpt_ref( $prd_id, $cpt_id ){
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select pwf_cpt_ref
			from prw_followed_products
			where pwf_prd_id = '.$prd_id.'
			and pwf_cpt_id = '.$cpt_id.'
			and pwf_tnt_id = '.$config['tnt_id'].'
		';

		$res = ria_mysql_query( $sql );
		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		$r = ria_mysql_fetch_assoc( $res );
		return $r['pwf_cpt_ref'];
	}

	/**	Cette fonction permet de mettre à jour pour un produit le champ "disable"
	 *
	 *	@param int $prd_id     Obligatoire, identifiant produit
	 *	@param int $cpt_id     Obligatoire, identifiant concurrent
	 *	@param $disable    Obligatoire, "0" ou "1".
	 *
	 *	@return bool|int    retourne false si erreur, sinon true.
	 */
	public function prw_followed_products_update_disable( $prd_id, $cpt_id, $disable ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 || !is_numeric( $disable ) || $disable < 0 ){
			return false;
		}
		$sql = 'update prw_followed_products
         set
         pwf_disable='.$disable.'
         where
         pwf_cpt_id='.$cpt_id.'
          and pwf_prd_id='.$prd_id.'
           and pwf_tnt_id='.$config['tnt_id'];

		ria_mysql_query( $sql );

		return true;
	}

	/**
	 *  @param bool $is_myselection Facultatif, Détermine s'il s'agit d'un produit faisant partie de "ma sélection"
	 *	@param int $cat_id Facultatif, identifiant de la catégorie
	 *	@return array
	 */
	public function prw_followed_products_getAll_enable_group_by_id($is_myselection=false, $cat_id=0){
		if( !is_numeric($cat_id) || $cat_id<0 ){
			return false;
		}
		global $config;

		$sql = '
			select result.tnt_id, cpt_id, result.prd_id, p.prd_ref as ref, p.prd_name as name, p.prd_is_sync as is_sync, disable, last_check
			from (
				select pwf_tnt_id as tnt_id,
					pwf_cpt_id as cpt_id,
					pwf_prd_id as prd_id,
					pwf_cpt_ref as ref,
					pwf_disable as disable,
					pwf_date_lastcheck as last_check
				from prw_followed_products
				left join ord_products on (prw_followed_products.pwf_prd_id=ord_products.prd_id and prw_followed_products.pwf_tnt_id=ord_products.prd_tnt_id)
				left join ord_orders on (ord_products.prd_ord_id=ord_orders.ord_id and ord_products.prd_tnt_id=ord_orders.ord_tnt_id)
				where pwf_disable = 0
					and pwf_tnt_id='.$config['tnt_id'].'
			  ';
		if( $is_myselection ){
			$sql .= ' and pwf_is_selection=1';
		}
		$sql .='			and (ord_state_id is null or ord_state_id in('.implode( ',', ord_states_get_ord_valid() ).'))
				group by pwf_tnt_id, pwf_cpt_id, pwf_prd_id, pwf_cpt_ref, pwf_disable, pwf_date_lastcheck
			union all
				select pwf_tnt_id as tnt_id,
					pwf_cpt_id as cpt_id,
					pwf_prd_id as prd_id,
					pwf_cpt_ref as ref,
					pwf_disable as disable,
					pwf_date_lastcheck as last_check
				from prw_followed_products
				where pwf_disable = 0
					and pwf_tnt_id='.$config['tnt_id'].'
			  ';
		if( $is_myselection ){
			$sql .= ' and pwf_is_selection=1';
		}
		$sql .= '
				group by pwf_tnt_id, pwf_cpt_id, pwf_prd_id, pwf_cpt_ref, pwf_disable, pwf_date_lastcheck
			) as result
			join prd_products as p on (p.prd_tnt_id=result.tnt_id and p.prd_id=result.prd_id)
			';
		if( $cat_id ){
			$categories = prd_categories_childs_get_list($cat_id);
			if( is_array($cat_id) ){
				$categories = $categories ? implode(',',$cat_id).','.$categories : implode(',',$cat_id);
			}else{
				$categories = $categories ? $cat_id.','.$categories : $cat_id;
			}
			$sql .= '
				inner join prd_classify on (p.prd_id=cly_prd_id and cly_tnt_id='.$config['tnt_id'].' and cly_cat_id in ('.$categories.')  )
			';
		}
		$sql .= '
			group by tnt_id, result.prd_id
			order by prd_id desc
			';

		$r = ria_mysql_query( $sql );
		if( !$r || !ria_mysql_num_rows( $r ) ){
			return false;
		}
		$rst = array();
		while( $row = ria_mysql_fetch_assoc( $r ) ){
			$rst[] = $row;
		}

		return $rst;
	}

	/**
	 * @param $prd_id
	 *
	 * @return bool
	 */
	public function prw_followed_products_get_cat( $prd_id ){
		$sql = 'SELECT classify.cly_cat_id AS cat_id
        FROM prd_classify AS classify
        JOIN prw_followed_products AS followed ON classify.cly_prd_id = followed.pwf_prd_id
        WHERE classify.cly_prd_id ='.$prd_id;
		$r = ria_mysql_query( $sql );
		if( !$r ){
			return false;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ){
			return $row['cat_id'];
		}

	}

	/**
	 * @param array|null $prd_id
	 * @param null       $cpt_id
	 * @param null       $disable
	 *
	 * @return array|bool
	 */
	public function prw_followed_products_getAll(
		array $prd_id = null,
		$cpt_id = null,
		$disable = null,
		$last = false
	){
		global $config;
		$sql = 'select pwf_tnt_id as tnt_id, pwf_cpt_id as cpt_id, pwf_prd_id as prd_id, pwf_cpt_ref as ref, pwf_disable AS disable, pwf_date_lastcheck as last_check, prd_products.prd_barcode as barcode
        from prw_followed_products
        left join prd_products on prd_products.prd_id = prw_followed_products.pwf_prd_id
        where pwf_tnt_id='.$config['tnt_id'];

		if( !is_null( $disable ) && is_numeric( $disable ) ){

			$sql .= ' and pwf_disable='.$disable;
		}
		if( !is_null( $prd_id ) && is_array( $prd_id ) && !empty( $prd_id ) ){
			$ids = join( ',', $prd_id );
			$sql .= ' and pwf_prd_id in('.$ids.')';
		}
		if( !is_null( $cpt_id ) && is_numeric( $cpt_id ) ){

			$sql .= ' and pwf_cpt_id='.$cpt_id;
		}else{
			$sql .= ' and pwf_cpt_id!='.PRW_CLIENT.'
            group by 3';
		}
		if( $last ){
			$sql .= ' order by pwf_date_lastcheck ASC';
		}

		$r = ria_mysql_query( $sql );
		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ){
			$rst[] = $row;
		}

		return $rst;
	}

	/**
	 * @param $cpt_id
	 * @param $prd_id
	 * @param $ref
	 *
	 * @return bool|int
	 */
	public function prw_followed_products_update_ref( $cpt_id, $prd_id, $ref ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 || !is_string( $ref ) ){
			return false;
		}

		$sql = 'update prw_followed_products
         set
         pwf_cpt_ref="'.$ref.'"
         where
         pwf_cpt_id='.$cpt_id.'
          and pwf_prd_id='.$prd_id.'
           and pwf_tnt_id='.$config['tnt_id'];

		$r = ria_mysql_query( $sql );
		$id = ria_mysql_insert_id();

		return $id;
	}

	/**
	 * @param $cpt_id
	 * @param $prd_id
	 *
	 * @return bool
	 */
	public function prw_followed_products_delete( $cpt_id, $prd_id ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		$sql = 'delete from prw_followed_products
        where pwf_cpt_id='.$cpt_id.'
          and pwf_prd_id='.$prd_id.'
           and pwf_tnt_id='.$config['tnt_id'];

		$r = ria_mysql_query( $sql );
	}

	/**
	 * @param $cpt_id
	 * @param $prd_id
	 *
	 * @return array|bool
	 */
	public function prw_followed_products_get_lastcheck( $cpt_id, $prd_id ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		$sql = 'select pwf_prd_id as prd_id, pwf_cpt_id as cpt_id, pwf_disable as disable, pwf_cpt_ref as cpt_ref, pwf_date_lastcheck as last_check
        from prw_followed_products
        where pwf_prd_id='.$prd_id.'
        and pwf_cpt_id='.$cpt_id.'
        and pwf_tnt_id='.$config['tnt_id'];

		$r = ria_mysql_query( $sql );
		if( !ria_mysql_num_rows( $r ) ){
			return false;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ){
			$rst = $row;
		}

		return $rst;
	}

	/**
	 * @param $cpt_id
	 * @param $prd_id
	 *
	 * @return bool|int
	 *
	 * @param $prd_id
	 * @param $cpt_id
	 *
	 * @return bool|int
	 */
	public function prw_followed_products_update_lastcheck( $prd_id, $cpt_id ){
		global $config;
		if( !is_numeric( $cpt_id ) || $cpt_id < 0 || !is_numeric( $prd_id ) || $prd_id < 0 ){
			return false;
		}

		$sql = 'update prw_followed_products
         set
         pwf_date_lastcheck = now()
         where
         pwf_cpt_id='.$cpt_id.'
          and pwf_prd_id='.$prd_id.'
           and pwf_tnt_id='.$config['tnt_id'];

		$r = ria_mysql_query( $sql );
		$id = ria_mysql_insert_id();

		return $id;
	}


}

<?php
/// \cond onlyria
/**
 * \defgroup PriceWatching Veille tarifaire
 * 	\ingroup cpq
 * 	@deprecated Ce module ne sera pas porté vers la nouvelle interface d'administration et ne dois pas
 * 	être mis à disposition dans la nouvelle API.
 */

/**
 * \defgroup PriceWatchingCron Watch module
 * \brief Classe qui permette la gestion du cron de veille tarifaire
 * \ingroup PriceWatching
 */

/* \class PriceWatchingFactory
 * \brief PriceWatchingFactory est une class qui permet d'instancié la class du concurrent et exécuter la veille tarifaire.
 *
 * Cela permet la facilité d'utilisation du module et l'abstraction des concurrents dans le crontabs
 */
class PriceWatchingFactory
{
	/**
	 * Cette fonction permet d'inclure et d'invoquer automatiquement la classe à utiliser et la méthode watch
	 * @param $class        Obligatoire, nom du concurrent exemple amazon, client, cdiscount
	 * @param int $cat          Obligatoire, identifiant de la catégorie
	 * @param $prd          Obligatoire,    identifiant d'un produit
	 * @param $recursive    Obligatoire, indique la récursivité 1 ou 0
	 *	@return null dans tous les cas
	 */
	public static function build( $class, $cat, $prd, $recursive )
	{
//        import de la class de competitor
		require_once( 'PriceWatching' . ucfirst( $class ) . '.inc.php' );
		$class = ucfirst( $class ) . 'Watch';
//        utilisation de la méthode
		$class::watch( $cat, $prd, $recursive );
	}
}

// \endcond
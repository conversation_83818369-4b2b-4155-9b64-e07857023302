<?php
/**
 * \ingroup PriceWatchingCron
 */
/** \interface iWatching
 * \brief Interface de base pour un script de veille tarifaire d'un concurrent
 *
 * Cette interface doit être implémenté pour le fonctionnement avec le builder du crontabs price-watching
 * @see PriceWatchingFactory
 */
interface iWatching
{
	/**	Cette méthode permet de réalisé la surveillance des prix
	 *
	 *	@param int $cat          Obligatoire, identifiant d'une catégorie
	 *	@param int $prd          Obligatoire, identifiant d'un produit
	 *	@param bool $recursive    Obligatoire, indique si pour la catégorie la récursivité est activée ou non
	 *
	 *	@return mixed 		Dépend de l'implémentation
	 */
	public static function watch( $cat, $prd, $recursive );
}
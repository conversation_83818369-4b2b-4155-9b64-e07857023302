<?php

require_once('db.inc.php');
require_once('categories.inc.php');
require_once('comparators.inc.php');
require_once('comparators/MarketplaceWebServiceProducts/Client.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/GetCompetitivePricingForASINRequest.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/GetLowestPricedOffersForASINRequest.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/GetMatchingProductRequest.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/GetMatchingProductForIdRequest.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/ASINListType.php');
require_once('comparators/MarketplaceWebServiceProducts/Model/IdListType.php');
require_once('PriceWatching/prw.iCompetitors.inc.php');

/**
 * \ingroup PriceWatching
 */

/** \class Amazon
 * \brief Class qui gère la connexion vers Amazon et qui récupère les données du WebService
 */
class Amazon implements iCompetitors
{
	/**
	 * Identifiants AWS
	 */
	private $keys;

	/**
	 * Configuration du service Amazon
	 */
	private $config = array(
		'ServiceURL' => 'https://mws-eu.amazonservices.com/Products/2011-10-01',
	);

	/**
	 * Service de base pour Amazon
	 */
	private $service;

	/**
	 * Type de codes barre recherché sur Amazon
	 */
	private $ar_barcode_types = array('EAN', 'UPC');

	/** Constructeur
	 * Initialise la classe avec les valeurs par défaut.
	 */
	public function __construct()
	{
		//  Récupération des identifiants AWS
		$this->setKeys();

		//  Initialisation du service Amazon
		$this->service = new MarketplaceWebServiceProducts_Client(
			$this->keys['AWS_ACCESS_KEY_ID'],
			$this->keys['AWS_SECRET_ACCESS_KEY'],
			'veille tarifaire',
			'1.0',
			$this->config
		);
	}

	/** Récupère les identifiants AWS.
	 *
	 * \return array Les identifiants AWS stockés sur l'objet.
	 */
	private function getKeys()
	{
		return $this->keys;
	}

	/**
	 * Récupère et stocke les identifiants AWS depuis la base de données.
	 */
	private function setKeys()
	{
		$keys = ctr_params_get_array(CTR_AMAZON, array(
			'AWS_ACCESS_KEY_ID',
			'AWS_SECRET_ACCESS_KEY',
			'AWS_MERCHANT_ID',
			'AWS_MARKETPLACE_ID',
		));

		$this->keys = $keys;
	}

	/**	Cette fonction permet de créer les différente requête XML à travers la librairie Amazon.
	 *
	 *	@param MarketplaceWebServiceProducts_Interface $service
	 *	@param $request
	 *	@param $get
	 *
	 *	@return SimpleXMLElement retourne la réponse XML, si erreur lors de la requête retourne un message d'erreur avec
	 * 		les données suivantes :
	 *		 	- Caught Exception:
	 * 			- Response Status Code
	 * 			- Error Code
	 * 			- Error Type
	 * 			- Request ID
	 * 			- XML
	 * 			- ResponseHeaderMetadata
	 */
	private function invokeResponse(MarketplaceWebServiceProducts_Interface $service, $request, $get)
	{
		try {
			$response = $service->$get($request);

			$dom = new DOMDocument;
			$dom->loadXML($response->toXML());
			$dom->preserveWhiteSpace = false;
			$dom->formatOutput = true;

			return simplexml_load_string($dom->saveXML());
		} catch( MarketplaceWebServiceProducts_Exception $e ) {
			// Si quota atteint, retourne message d'erreur
			$error = 'Caught Exception: ' . $e->getMessage() . PHP_EOL;
			$error.= 'Response Status Code: ' . $e->getStatusCode() . PHP_EOL;
			$error.= 'Error Code: ' . $e->getErrorCode() . PHP_EOL;
			$error.= 'Error Type: ' . $e->getErrorType() . PHP_EOL;
			$error.= 'Request ID: ' . $e->getRequestId() . PHP_EOL;
			$error.= 'XML: ' . $e->getXML() . PHP_EOL;
			$error.= 'ResponseHeaderMetadata: ' . $e->getResponseHeaderMetadata() . PHP_EOL;

			return $error;
		}
	}

	/** La fonction génère la requête pour rechercher l'identifiant d'un produit chez Amazon (ASIN).
	 *
	 *	@param $code Obligatoire, code EAN du produit dans RiaShop
	 *	@param $type Optionnel, par défaut la recherche est faite sur l'EAN et l'UPC chez Amazon, préciser le code pour ne recherche que sur un type
	 *	@return L'ASIN si le produit existe chez Amazon, false dans le cas contriare
	 */
	public function getCptRef($code, $type=false)
	{
		if ($type) {
			if (!is_string($type) || !in_array($type, $this->ar_barcode_types)) {
				return false;
			}
		}

		$asin = false;

		foreach( $this->ar_barcode_types as $data ){
			if( $type && $data != $type ){
				continue;
			}

			$ctrl_code = trim($code);

			switch( $data ){
				case 'UPC':
					$ctrl_code = str_pad($ctrl_code, 12, '0', STR_PAD_LEFT);
					break;
				case 'EAN':
					$ctrl_code = str_pad($ctrl_code, 13, '0', STR_PAD_LEFT);
					break;
			}

			$request = (new MarketplaceWebServiceProducts_Model_GetMatchingProductForIdRequest)
				->setSellerId($this->keys['AWS_MERCHANT_ID'])
				->setMarketplaceId($this->keys['AWS_MARKETPLACE_ID'])
				->setIdType($data)
				->setIdList(new MarketplaceWebServiceProducts_Model_IdListType(array('Id' => $ctrl_code)));

			$XML = $this->invokeResponse($this->service, $request, 'GetMatchingProductForId');

			if( isset($XML->GetMatchingProductForIdResult->Products, $XML->GetMatchingProductForIdResult->Products->Product, $XML->GetMatchingProductForIdResult->Products->Product->Identifiers) ){
				$obj = get_object_vars(
					$XML->GetMatchingProductForIdResult->Products->Product->Identifiers->MarketplaceASIN
				);

				$asin = $obj['ASIN'];
				break;
			}
		}

		return $asin;
	}

	/**
	 * @param $asin
	 * @return array|bool|string retourne false si erreur, un message si pas de résultat ou un tableau associatif avec
	 * les jeux de valeur suivante :
	 *    -ListingPrice    tarif TTC du produit chez le concurrent
	 *    -ShippingPrice    tarif de livraison chez le concurrent
	 */
	public function getOffer( $asin )
	{
		if( !is_string( $asin ) ){
			return false;
		}
		$keys = $this->getKeys();
		$request = new MarketplaceWebServiceProducts_Model_GetCompetitivePricingForASINRequest();
		$request->setSellerId( $keys['AWS_MERCHANT_ID'] )->setMarketplaceId( $keys['AWS_MARKETPLACE_ID'] )->setASINList( new MarketplaceWebServiceProducts_Model_ASINListType( array('ASIN' => array( $asin ) ) ) );
		$XML = $this->invokeResponse( $this->service, $request, 'GetCompetitivePricingForASIN' );
		if( !is_object($XML) ){
			return 'Ce produit ne possède aucune offre actuellement';
		}
		$lists = $XML->GetCompetitivePricingForASINResult;
		//   si l'objet XML possède un objet error on retourne un message d'erreur
		if( isset( $lists->Error ) ){
			return 'Ce produit ne possède aucune offre actuellement';
		}
		//   si pas d'erreur on recherche si l'objet CompetitivePrices
		$price = $lists->Product->CompetitivePricing->CompetitivePrices;
		//      On test si l'objet n'est pas vide sinon on retourne un message d'erreur
		if( ( count( get_object_vars( $price ) ) ) ){
			$landedprice = get_object_vars( $price->CompetitivePrice->Price->ListingPrice );
			$shippingprice = get_object_vars( $price->CompetitivePrice->Price->Shipping );

			$shipPrice = 0;
			if( isset($shippingprice['Amount']) ){
				$shipPrice = $shippingprice['Amount'];
			}
			return array(
				'ListingPrice'  => $landedprice['Amount'],
				'ShippingPrice' => $shipPrice
			);
		} else{
			return 'Ce produit ne possède aucune offre actuellement';
		}
	}

	/**
	 * cette méthode réalise une requête qui possède un cuota de 200 requêtes par heure,
	 * si ce cuota est atteind la requete retourne un message d'erreur sous forme de string.
	 *
	 * @param $asin
	 *
	 * @return array|bool|SimpleXMLElement|string retourne false si erreur, un message si pas de résultat ou l'objet
	 *                                            erreur ou un tableau associatif avec les jeux de valeur suivante :
	 *    -ListingPrice    tarif TTC du produit chez le concurrent
	 *    -ShippingPrice    tarif de livraison chez le concurrent
	 */
	public function getOffers( $asin )
	{
		if( !is_string( $asin ) ){
			return false;
		}
		$keys = $this->getKeys();
		$request = new MarketplaceWebServiceProducts_Model_GetLowestPricedOffersForASINRequest();
		$request->setSellerId( $keys['AWS_MERCHANT_ID'] )->setMarketplaceId( $keys['AWS_MARKETPLACE_ID'] )->setASIN( $asin )->setItemCondition( 'New' );
		$XML = $this->invokeResponse( $this->service, $request, 'GetLowestPricedOffersForASIN' );
		//  Si le quota est atteind le xml retourne une erreur sous forme de string
		if( is_string( $XML ) ){
			return $XML;
		}
		if( !is_object($XML) ){
			return 'Ce produit ne possède aucune offre actuellement';
		}
		// on test si il y a des offres disponibles
		// on prend la première
		$list = $XML->GetLowestPricedOffersForASINResult->Summary;
		$obj = get_object_vars( $list );
		if( $obj['TotalOfferCount'] !== "0" ){
			$ListingPrice = get_object_vars( $list->LowestPrices->LowestPrice->ListingPrice );
			$ShippingPrice = get_object_vars( $list->LowestPrices->LowestPrice->Shipping );

			return array(
				'ListingPrice'  => $ListingPrice['Amount'],
				'ShippingPrice' => $ShippingPrice['Amount']
			);
		} else{
			return _('Ce produit ne possède aucune offre actuellement');
		}
	}

}

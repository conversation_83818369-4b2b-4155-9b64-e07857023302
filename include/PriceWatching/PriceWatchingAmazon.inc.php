<?php

require_once( 'PriceWatching/models/prw_followed_products.inc.php' );
require_once( 'PriceWatching/models/prw_offers.inc.php' );
require_once( 'PriceWatching/prw.amazon.inc.php' );
require_once( 'PriceWatching/prw.iWatching.inc.php' );
require_once( 'PriceWatching/PriceWatchingEmail.inc.php' );
/**
 * \ingroup PriceWatchingCron
 */
/** \class AmazonWatch
 * \brief Class qui gère la tâche de la veille tarifaire sur amazon
 */
class AmazonWatch implements iWatching {
	/**
	 * @var array $errors
	 */
	private static $errors = array();
	/**
	 * @var array $changes
	 */
	private static $changes = array();
	/**
	 * @var int $count
	 */
	private static $count = 1;
	/**
	 * @var array $pending
	 */
	private static $pending = array();

	/**
	 * Cette fonction a pour but de récupérer l'identifiant ASIN pour chaque produit si ils n'en
	 * possèdent pas et de récupérer les offres depuis Amazon pour en suite créer une ligne dans la
	 * table prw_offers à la fin de son exécution la fonction envoi un mail au client et des mails
	 * a l'admin si il y a eu des erreurs ou des produits non pas pu être vérifié.
	 *
	 * \param int $cat Optionnel, identifiant d'une catégorie
	 * \param int $prd Optionnel, identifiant d'un produit
	 * \param int $recursive Optionnel, si un identifiant de catégorie est saisi, ce paramètre
	 * indique l'option de récursivité. pour la catégorie
	 *
	 * \return false si erreur
	 */
	public static function watch( $cat = 0, $prd = 0, $recursive = 0 ){
		global $config;
		if( !is_numeric( $cat ) || !is_numeric( $prd ) || !is_numeric( $recursive ) ){
			return false;
		}

		$oFollowed = new prw_followed_products();
		$Amazon = new Amazon();

		if( $cat && !$prd ){
			$product_category = prd_products_get_simple( 0, '', false, $cat, $recursive );
			$prd_ids = array();
			while( $row = ria_mysql_fetch_assoc( $product_category ) ){
				$prd_ids[] = $row['id'];
			}
			$followedProducts = $oFollowed->prw_followed_products_getAll( $prd_ids, PRW_AMAZON, 0, true );
		}elseif( $prd ){
			$followedProducts = $oFollowed->prw_followed_products_get( $prd, PRW_AMAZON, 0, true );
		}else{
			$followedProducts = $oFollowed->prw_followed_products_getAll( null, PRW_AMAZON, 0, true );
		}

		if( !$followedProducts ){
			return true;
		}

		foreach( $followedProducts as $followedProduct ){
			if( empty( $followedProduct['ref'] ) ){
				$followedProduct['ref'] = $Amazon->getCptRef( $followedProduct['barcode'] );
				if( $followedProduct['ref'] ){
					$oFollowed->prw_followed_products_update_ref( PRW_AMAZON, $followedProduct['prd_id'], $followedProduct['ref'] );
				}else{
					continue;
				}
			}
			$offer = $Amazon->getOffer( $followedProduct['ref'] );
			if( is_string( $offer ) && self::$count !== 200 ){
				$offers = $Amazon->getOffers( $followedProduct['ref'] );
				$offer = $offers;
				self::$count++;
			}
			if( is_string( $offer ) ){
				if( self::$count !== 200 ){
					self::$errors[$followedProduct['prd_id']] = $offer;
					$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['prd_id'], PRW_AMAZON );
				}else{
					self::$pending[] = $followedProduct['prd_id'];
					continue;
				}
			}else{
				// instanciation unique de prw offers pour optimisation de la mémoire
				$oOffers = prw_offers::getInstance();
				$last_offer = $oOffers->get_offer_getLast( $followedProduct['prd_id'], PRW_AMAZON );

				if( !$last_offer ){
					$ListingPrice = $offer['ListingPrice'];
					$ShippingPrice = $offer['ShippingPrice'];
					$url = 'http://asin.info/a/'.$followedProduct['ref'];
					$prd_id = $followedProduct['prd_id'];
					$id = $oOffers->prw_offers_add( $ListingPrice, $ShippingPrice, $url, PRW_AMAZON, $prd_id );
					if( is_numeric( $id ) ){
						$rprd = prd_products_get_simple( $prd_id, '', false, 0, false, false, true );
						if( $rprd || ria_mysql_num_rows( $rprd ) ){
							$prd = ria_mysql_fetch_array( $rprd );
							$tr = array(
								'ref'     => '<img class="sync" src="'.$config['site_url'].'/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg" title="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" alt="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" /> <a href="'.$config['site_url'].'/admin/catalog/product.php?cat=0&prd='.$prd_id.'&lng=fr&tab=pricewatching">'.$prd['ref'].'</a>',
								'name'    => '<a href="'.$config['site_url'].'/admin/catalog/product.php?cat=0&prd='.$prd_id.'&lng=fr&tab=pricewatching">'.htmlspecialchars( $prd['name'] ).'</a>',
								'current' => round( $prd['price_ttc'], 2 ),
								'last'    => 'non renseigné',
								'new'     => $ListingPrice
							);
						}
						self::$changes[] = $tr;
					}
					$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['prd_id'], PRW_AMAZON );
				}else{
					if( $last_offer['ListingPrice'] == $offer['ListingPrice'] && $last_offer['shippingprice'] == $offer['ShippingPrice'] ){
						$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['prd_id'], PRW_AMAZON );
					}else{
						$ListingPrice = $offer['ListingPrice'];
						$ShippingPrice = $offer['ShippingPrice'];
						$url = 'http://asin.info/a/'.$followedProduct['ref'];
						$prd_id = $followedProduct['prd_id'];
						$id = $oOffers->prw_offers_add( $ListingPrice, $ShippingPrice, $url, PRW_AMAZON, $prd_id );
						if( is_numeric( $id ) ){
							$rprd = prd_products_get_simple( $prd_id, '', false, 0, false, false, true );
							if( $rprd || ria_mysql_num_rows( $rprd ) ){
								$prd = ria_mysql_fetch_array( $rprd );
								$tr = array(
									'ref'     => '<img class="sync" src="'.$config['site_url'].'/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg" title="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" alt="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" /> <a href="'.$config['site_url'].'/admin/catalog/product.php?cat=0&prd='.$prd_id.'&lng=fr&tab=pricewatching">'.$prd['ref'].'</a>',
									'name'    => '<a href="'.$config['site_url'].'/admin/catalog/product.php?cat=0&prd='.$prd_id.'&lng=fr&tab=pricewatching">'.htmlspecialchars( $prd['name'] ).'</a>',
									'current' => number_format( $prd['price_ttc'], 2, ',', ' ' ).' €',
									'last'    => ( is_numeric( floatval( $last_offer['ListingPrice'] ) ) ? number_format( floatval( $last_offer['ListingPrice'] ), 2, ',', ' ' ).' €' : floatval( $last_offer['ListingPrice'] ) ),
									'new'     => number_format( $ListingPrice, 2, ',', ' ' ).' €',
									'diff'    => number_format( ( $prd['price_ttc'] - $ListingPrice ), 2, ',', ' ' ).' €'
								);
							}
							self::$changes[] = $tr;
						}
						$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['prd_id'], PRW_AMAZON );
					}
				}
			}

		}
		/*$email = new PriceWatchingEmail( 'Amazon' );
		try{
			if( !empty( self::$changes ) ){
				$email->emailChanges( self::$changes );
			}
			if( !empty( self::$errors ) ){
				$email->emailErrors( self::$errors );
			}
			if( !empty( self::$pending ) ){
				$email->emailPending( self::$pending );
			}
		}catch(Exception $e){
			error_log($e->getMessage());
		}*/
	}
}


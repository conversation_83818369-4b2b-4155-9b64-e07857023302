<?php
require_once( 'PriceWatching/ReportEmailing.inc.php' );
require_once( 'email.inc.php' );
/**
 * \ingroup PriceWatchingCron
 */

/**
 * \class PriceWatchingEmail
 * \brief Cette classe permet la geston des emails lors de la veille tarifaire pour n'importe quel concurrent
 */
class PriceWatchingEmail implements ReportEmailing {

	/**
	 *	@var string $concurenceName
	 *	Nom du concurrent
	 */
	private $concurenceName;

	/**
	 *	@var array $names
	 *	Nom de concurrent valide
	 */
	private $names = array(
		'amazon',
		'cdiscount'
	);

	/**
	 * PriceWatchingEmail constructor permet d'initialiser la gestion des emails
	 *
	 * @param $name Obligatoire, Nom du concurrent
	 */
	public function __construct( $name ){
		$this->setConcurenceName( $name );
	}

	/**
	 * Permet d'initialiser le nom du concurrent
	 *
	 * @param $name Nom du concurrent
	 *
	 * @throws Exception si le nom n'est pas valable
	 */
	public function setConcurenceName( $name ){
		if( !in_array( strtolower( $name ), $this->names ) ){
			throw new Exception( 'You need to enter a valid concurence name' );
		}
		$this->concurenceName = ucfirst( $name );
	}

	/**	Permet d'envoyer un email au client avec les information sur les nouveaux tarifs
	 *
	 *	@param array $changes Tableau contenant les changemants et les clés suivantes :
	 *                       ref : la référence du produit ou html avec la référence du porduit
	 *                       name : nom du produit ou html avec le nom du produit
	 *                       current : le prix actuel du produit pour le client
	 *                       last : le prix du produit lors du dernier relevé
	 *                       new : nouveau porduit si renseigné
	 *                       diff : différence entre le current et le new
	 *
	 *	@return bool true si l'email c'est bien envoyé, false si non
	 */
	public function emailChanges( array $changes ){
		if( count($changes) == 0 ){
			throw new Exception('Changes is empty');
		}
		global $config;
		$r = cfg_emails_get( 'price-watching', $config['wst_id'] );
		if( !$r || !ria_mysql_num_rows($r) ){
			throw new Exception('Pas de variable email price-watching pour le tenant '.$config['tnt_id']);
		}
		$cfg = ria_mysql_fetch_assoc( $r );

		$email = $this->createEmail();
		$email->addTo( $cfg['to'] );
		$email->addCC( $cfg['cc'] );
		$email->addBcc( $cfg['bcc'] );
		$email->setReplyTo( $cfg['reply-to'] );
		$email->setSubject( 'Rapport de veille tarifaire' );
		$email->addHtml( '<table width="auto" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
		$email->addParagraph( 'Cher client, chère cliente,' );
		$email->addParagraph( 'Nombre de changement trouvé : '.count( $changes ) );
		$email->openTable();
		$email->openTableRow();
		$email->addCell( '<strong>Référence</strong>', 'center', 1, 0, 'width:140px;' );
		$email->addCell( '<strong>Désignation</strong>', 'center', 1, 0, 'width:200px;' );
		$email->addCell( '<strong>Votre Prix</strong>', 'center', 1, 0, 'width:140px;' );
		$email->addCell( '<strong>Relever Précédent</strong>', 'center', 1, 0, 'width:140px;' );
		$email->addCell( '<strong>Dernier relever</strong>', 'center', 1, 0, 'width:140px;' );
		$email->addCell( '<strong>Écart de prix</strong>', 'center', 1, 0, 'width:140px;' );
		$email->closeTableRow();
		foreach( $changes as $change ){
			if( !array_key_exists('diff', $change) ){
				$change['diff'] = 0;
			}

			$email->openTableRow();
			$email->addCell( $change['ref'] );
			$email->addCell( $change['name'] );
			$email->addCell( $change['current'], 'right' );
			$email->addCell( $change['last'], 'right' );
			$email->addCell( $change['new'], 'right' );
			$email->addCell( ( $change['diff'] <= 0 ? $change['diff'] : '+'.$change['diff'] ), 'right', 1, 0, ( $change['diff'] <= 0 ? 'color:green;' : 'color:red;' ) );
			$email->closeTableRow();
		}
		$email->closeTable();

		$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb( 255, 255, 255 );" href="mailto:contact@fr">contact@fr</a></div></div>' );

		return $email->send();
	}

	/** Cette fonction permet d'envoyer un rapport avec les erreur survenue lors de la veille tarifaire
	 * @param array $errors Tableau avec les erreurs survenus lors de la veille tarifaire :
	 *                      - identifiant produit => message d'erreur
	 *
	 * @return true si l'email c'est bien envoyé, false si non
	 */
	public function emailErrors( array $errors ){
		global $config;
		$email = $this->createEmail( true );
		$email->setSubject( 'Rapport d\'erreurs veille tarifaire '.tnt_tenants_get_name( $config['tnt_id'] ) );
		$email->addParagraph( 'Nombre d\'erreurs trouvé : '.count( $errors ) );
		foreach( $errors as $key => $error ){
			$email->addHorizontalRule();
			$email->addParagraph( $this->getProductLink( $key ) );
			$email->addParagraph( $error );
		}

		return $email->send();
	}

	/** Cette fonction permet d'envoyer un rapport au développeur des produit en attente de veille tarifaire
	 * @param array $pending Tableau avec les identifiants produits en attentent de veille tarifaire.
	 *
	 * @return true si le message c'est bien envoyé, false si non
	 */
	public function emailPending( array $pending ){
		global $config;
		$email = $this->createEmail( true );
		$email->setSubject( 'Rapport des produits en attente d\'actualisation '.tnt_tenants_get_name( $config['tnt_id'] ) );
		$email->addParagraph( 'Nombre de produits en attentent : '.count( $pending ) );
		foreach( $pending as $key => $pend ){
			$email->addHorizontalRule();
			$email->addParagraph( $this->getProductLink( $pend ) );
		}

		return $email->send();
	}

	/** Permet de créer l'objet email avec configuration commune
	 * @param bool $forDev true pour les message dédier au développeur
	 *
	 * @return l'objet email
	 */
	private function createEmail( $forDev = false ){
		global $config;
		$email = new Email();
		$email->setFrom( 'RiaShop Veille tarifaire <contact@fr>' );
		date_default_timezone_set( 'Europe/Paris' );
		setlocale( LC_TIME, 'fr_FR.utf8', 'fra' );
		$email->addParagraph( 'Voici le rapport des changements de prix pour '.$this->concurenceName.' d\'aujourd\'hui : '.strftime( "%A %d %B %Y à %H:%M:%S" ) );
		if( $forDev ){
			$email->addTo( '<EMAIL>' );
			$email->addCC( '<EMAIL>' );
			$email->addParagraph( 'Pour le tenant : '.$config['tnt_id'].' '.tnt_tenants_get_name( $config['tnt_id'] ) );
		}

		return $email;
	}

	/** Cette fonciton permet de retourner le lien avec vers le produit avec l'information de syncronisation, son nom et sa référence
	 * @param $id
	 *
	 * @return string
	 * @throws Exception
	 */
	private function getProductLink( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			throw new Exception( 'You need to pass an valid numeric value' );
		}
		global $config;

		return 'Identifiant produit <a href="'.$config['site_url'].'/admin/catalog/product.php?cat=0&prd='.$id.'">'.prd_products_get_ref( $id ).' '.prd_products_get_name( $id ).'</a>';
	}
}
<?php
// \cond onlyria

require_once('stats.inc.php');


/** \defgroup view_stats Affichage des statistiques
 *	\ingroup view_admin
 *	Ce module comprend les fonctions nécessaires à l'affichage des statistiques dans l'interface d'administration
 * @{
 */

/**	Retourne les années comportant des statistiques, sous la forme d'un tableau.
 *	@return array Un tableau des années comportant des statistiques.
 */
function view_stats_get_years(){

	$periods = array();

	global $config;

	$sql = '
		select MIN(YEAR(stat_datetime)) as "min",
			MAX(YEAR(stat_datetime)) as "max"
		from stats_hourly
		where stat_tnt_id = '.$config['tnt_id'].'
			and stat_wst_id = '.$config['wst_id'].'
	';

	if( $res = ria_mysql_query($sql) ){
		if( $limits = ria_mysql_fetch_assoc($res) ){
			for( $i = $limits['min']; $i <= $limits['max']; $i++ ){
				$periods[ $i ] = $i;
			}
		}
	}

	return $periods;

}

/**	Retourne les mois comportant des statistiques, sous la forme d'un tableau.
 *	@return array Un tableau des mois comportant des statistiques.
 */
function view_stats_get_months(){

	$periods = array();

	global $config;

	$sql = '
		select MIN(YEAR(stat_datetime)) as "min",
			MAX(YEAR(stat_datetime)) as "max"
		from stats_hourly
		where stat_tnt_id = '.$config['wst_id'].'
			and stat_wst_id = '.$config['wst_id'].'
	';

	if( $res = ria_mysql_query($sql) ){
		if( $limits = ria_mysql_fetch_assoc($res) ){
			for( $i = $limits['min']; $i <= $limits['max']; $i++ ){
				$periods[ $i ] = $i;
			}
		}
	}

	return $periods;

}

/// @}

// \endcond
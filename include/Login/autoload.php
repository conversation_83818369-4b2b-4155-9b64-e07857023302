<?php

/**
 * Autoloader spécifique au module Login
 * @param $className Nom de la classe
 * @return bool true si success, false si erreur (class/fichier n'existe pas ou class qui n'est pas du module)
 */
function login_service_autoload($className){
	$filename = str_replace("\\", '/', $className) . '.php';
    if (strstr($filename, 'Login') && file_exists(__DIR__.'/../'.$filename)) {
    	require_once($filename);
        if (class_exists($className)) {
            return true;
        }
    }
    return false;
}
spl_autoload_register('login_service_autoload');
<?php
namespace Login;

use DateTime;

class LoginAttempt
{
	/**
	 * Date de base a utilisé pour vérifier les connexions
	 */
	const BASE_DATE = '2018-01-01';
	/**
	 * La date actuel d'essaie de login
	 *
	 * @var DateTime|null $attempt_date
	 */
	protected $attempt_date = null;
	/**
	 * L'utilisateur qui essai de ce connecté
	 *
	 * @var array|null $user
	 */
	protected $user;
	/**
	 * Le nombre d'essai avant le blocage du compte
	 *
	 * @var integer $max_attempts
	 */
	protected $max_attempts = 0;
	/**
	 * L'interval de temps en minutes pour vérifier le nombre d'essai
	 *
	 * @var integer $checking_interval
	 */
	protected $checking_interval = 0;
	/**
	 * Si on a bloquer la connexion ou non
	 *
	 * @var boolean $bloc_login
	 */
	protected $bloc_login = false;
	/**
	 * Constructeur prend en compte quel variable de config prendre
	 *
	 * @param boolean $is_admin_login si on est sur une connexion sur l'admin ou non
	 */
	public function __construct($is_admin_login)
	{
		global $config;
		if ($is_admin_login) {
			$this->max_attempts = $config['admin_login_max_attempts'];
			$this->checking_interval = $config['admin_login_attempts_interval'];
		}else{
			// TODO pour les sites client
		}
	}
	/**
	 * Enregistre un essai de connexion et détermine si il faut bloquer
	 *
	 * @param string $email L'email de l'utilisateur
	 * @return void
	 * @throws LoginAttemptException si le compte est bloquer
	 */
	public function attempt($email)
	{
		$r_user = gu_users_get(0, $email);
		if ($r_user && ria_mysql_num_rows($r_user)) {
			$this->user = ria_mysql_fetch_assoc($r_user);
			$date = gu_users_logins_add_attempt($this->user['id']);
			if ($date instanceof DateTime) {
				$this->attempt_date = $date;

				if ($this->shouldBloc()) {
					gu_users_set_can_login( $this->user['id'], false );
					$this->bloc_login = true;
					throw new LoginAttemptException("Trop de tentatives de connexion, ce compte est maintenant bloqué. Veuillez réinitialiser votre mot passe ou prendre contact avec nous.");
				}
			}
		}
	}
	/**
	 * Permet de définir l'essaie de connexion comme succès si le compte n'est pas bloqué
	 *
	 * @return void
	 */
	public function success()
	{
		if (!is_null($this->attempt_date) && !is_null($this->user) && !$this->bloc_login) {
			gu_users_logins_set_attempt_successfull($this->attempt_date, $this->user['id']);
		}
	}
	/**
	 * Détermine s'il faut bloquer ou non le compte
	 *
	 * @return boolean true si le compte doit être bloquer, false dans le cass contraire
	 */
	private function shouldBloc()
	{
		if ($this->max_attempts <= 0) {
			return false;
		}

		if ($this->checking_interval > 0) {
			$date = clone $this->attempt_date;
			$date->modify('-'.$this->checking_interval.' minutes');
		}elseif (!is_null($this->user['last_login_en'])){
			$date = new DateTime($this->user['last_login_en']);
		}else{
			$date = new DateTime(self::BASE_DATE);
		}

		$r_attempts = gu_users_logins_get_attempts_from($date, $this->user['id']);

		if ($r_attempts && ria_mysql_num_rows($r_attempts) >= $this->max_attempts) {
			return true;
		}

		return false;
	}
}
<?php

	require_once('db.inc.php');

	// *********************************** //
	// ATTENTION : LES CONSTANTES COMMENCANT PAR "_DB" SONT INTERDITES (réservées pour les connexions aux bases de données)
	// Une plage d'identifiant a été réservée pour la création des champs de type global (tnt = 0) : de 5000 à 10000 (inclus).
	// *********************************** //

	/** \defgroup model_constants _Contantes
	 * 	\ingroup system
	 */

	// Variable globale
	$ria_debug_timer = time();
	$ria_db_connect = $ria_db_selected = false;

	/**	\ingroup module_db_connect
	 *	@{
	 */
	/// Identifiant de connexion à RiaShop
	define('_DB_RIASHOP', 1);
	/// Connexion par défaut
	define('_DB_DEFAULT', _DB_RIASHOP);
	/// @}

	/// Durée d'une journée, exprimée en secondes
	define( '_TIME_ONE_DAY', 86400 );

	// tva rate par défaut
	/**	\ingroup model_prd_tva
	 *	@{
	 */
	/// Taux de TVA par défaut, exprimé sous la forme 1,x pour simplifier les calculs
	define( '_TVA_RATE_DEFAULT', 1.200 );
	/// @}

	// types de site
	/**	\ingroup tnt_sites
	 *	@{
	 */
	/// Type de site : Boutique en ligne
	define( '_WST_TYPE_SHOP', 1 );
	/// Type de site : Extranet Clients / Revendeurs
	define( '_WST_TYPE_EXTRANET', 2 );
	/// Type de site : Extranet Fournisseurs
	define( '_WST_TYPE_SUPPLIER', 3 );
	/// Type de site : Extranet Forces de Vente
	define( '_WST_TYPE_EXT_FDV', 4 );
	/// Type de site : Site public (sans vente en ligne)
	define( '_WST_TYPE_PUBLIC', 5 );
	/// Type de site : Installation Tablettes Forces de vente
	define( '_WST_TYPE_FDV', 6 );
	/// @}

	// types de service de livraison
	/**	\ingroup dlv_service_types
	 *	@{
	 */
	/// Type de site : Bureau de poste
	define( '_SRV_TYPE_BDP', 1 );
	/// Type de site : Point relais
	define( '_SRV_TYPE_CMT', 2 );
	/// Type de site : À domicile
	define( '_SRV_TYPE_DOM', 3 );
	/// @}

	// modules bancaire
	/**	\ingroup module_bancaire
	 *	@{
	 */
	/// Module de paiement : PayPlug
	define( '_PAY_MODULE_PAYPLUG', 'payplug' );
	/// Module de paiement : Payzen
	define( '_PAY_MODULE_PAYZEN', 'payzen' );
	/// Module de paiement : SystemPay
	define( '_PAY_MODULE_SYSTEMPAY', 'systempay' );
	/// Module de paiement : ETransaction
	define( '_PAY_MODULE_ETRANSACTION', 'etransaction' );
	/// Module de paiement : Paylib
	define( '_PAY_MODULE_PAYLIB', 'paylib' );
	/// Module de paiement : Payline
	define( '_PAY_MODULE_PAYLINE', 'payline' );
	/// Module de paiement : Paybox
	define( '_PAY_MODULE_PAYBOX', 'paybox' );
	/// Module de paiement : Sogecommerce
	define( '_PAY_MODULE_SOGECOMMERCE', 'sogecommerce' );
	/// Module de paiement : PayPal
	define( '_PAY_MODULE_PAYPAL', 'paypal' );
	/// Module de paiement : Lyra
	define( '_PAY_MODULE_LYRA', 'lyra' );
	/// @}

	// Code d'identifiant de module dans le back-office RiaShop
	/**	\ingroup view_admin
	 *	@{
	 */
	/// Section du back-office : Catalogue des produits
	define( '_MDL_CATALOG', 1 );
	/// Section du back-office : Clients
	define( '_MDL_CUSTOMER', 2 );
	/// Section du back-office : Commandes
	define( '_MDL_ORDER', 3 );
	/// Section du back-office : Promotions
	define( '_MDL_PROMOTION', 4 );
	/// Section du back-office : Médiathèque
	define( '_MDL_MEDIA', 5 );
	/// Section du back-office : Outils
	define( '_MDL_TOOLS', 6 );
	/// Section du back-office : Configuration
	define( '_MDL_CONFIG', 7 );
	/// Section du back-office : Modération
	define( '_MDL_MODERATION', 8 );
	/// Section du back-office : Statistiques
	define( '_MDL_STATS', 9 );
	/// Section du back-office : Comparateurs de prix
	define( '_MDL_COMPARATOR', 10 );
	/// Section du back-office : Mes Options
	define( '_MDL_OPTION', 11 );
	/// Section du back-office : Places de marché
	define( '_MDL_MARKETPLACE', 12 );
	/// Section du back-office : Tablettes Forces de Vente
	define( '_MDL_FDV', 13 );
	/// Section du back-office : Synchronisation
	define( '_MDL_SYNC', 14 );
	/// @}

	// Nombre de champs, dans des tables tels que fld_object_values et fld_object_models, déterminant la clé de l'objet, en partant toujours d'un indice _0
	define( 'COUNT_OBJ_ID', 3 );

	/**	\ingroup scm_zones
	 *	@{
	 */
	// Types de zones géographiques
	/// Région de France - métropole
	define( '_ZONE_RGN_FRANCE', 1 );
	/// Département de France - métropole
	define( '_ZONE_DPT_FRANCE', 2 );
	/// Code postal
	define( '_ZONE_ZIPCODES', 5 );
	/// Code insee
	define( '_ZONE_INSEE', 6 );
	/// Province d'un pays
	define( '_ZONE_PROVINCE', 10 );
	/// Etat d'un pays
	define( '_ZONE_STATE', 11 );
	/// Préfecture d'un pays
	define( '_ZONE_PREFECTURE', 12 );
	/// Pays
	define( '_ZONE_PAYS', 13 );

	/// Région de l'Allemagne
	define( '_ZONE_ZONE_ALLEMAGNE', 4 );
	/// region de l'allemagne
	define( '_ZONE_REGION_ALLEMAGNE', 14 );
	/// ville/commune d'Allemagne
	define( '_ZONE_VILLE_ALLEMAGNE', 15 );
	define( '_ZONE_ZIPCODE_ALLEMAGNE',43);

		/// Région de l'Italie
	define( '_ZONE_REGION_ITALIE', 16 );
	/// region de l'Italie
	define( '_ZONE_PROV_ITALIE', 17 );
	/// ville/commune d'Italie
	define( '_ZONE_VILLE_ITALIE', 18 );
	define( '_ZONE_ZIPCODE_ITALIE',44);

			/// Région de l'Espagne
	define( '_ZONE_COMMUNAUTE_ESPAGNE', 19 );
	/// region de l'Espagne
	define( '_ZONE_PROV_ESPAGNE', 20 );
	/// ville/commune d'Espagne
	define( '_ZONE_VILLE_ESPAGNE', 21 );
	define( '_ZONE_ZIPCODE_ESPAGNE',45);

	/// Région du lechtenstein
	define( '_ZONE_REGION_LIECHTENSTEIN', 22 );
	/// ville/commune d'lechtenstein
	define( '_ZONE_VILLE_LIECHTENSTEIN', 23 );
	define( '_ZONE_ZIPCODE_LIECHTENSTEIN',46);

	/// Région du tahiti
	define( '_ZONE_ARCHIPEL_TAHITI', 24 );
	/// region de l'tahiti
	define( '_ZONE_ILE_TAHITI', 25 );
	/// ville/commune de tahiti
	define( '_ZONE_VILLE_TAHITI', 26 );
	define( '_ZONE_ZIPCODE_TAHITI',47)	;

	/// Région du portugal
	define( '_ZONE_REGION_PORTUGAL', 27 );
	/// region de du portugal
	define( '_ZONE_DEPARTEMENT_PORTUGAL', 28 );
	/// ville/commune du portugal
	define( '_ZONE_VILLE_PORTUGAL', 29 );
	define( '_ZONE_ZIPCODE_PROTUGAL',48);

	/// Région de Suisse
	define( '_ZONE_REGION_SUISSE', 30 );
	/// canton de suisse
	define( '_ZONE_CANTON_SUISSE', 31 );
	/// ville/commune de Suisse
	define( '_ZONE_VILLE_SUISSE', 32 );
	define( '_ZONE_ZIPCODE_SUISSE', 49)	;

	/// Région d'Autriche
	define( '_ZONE_REGION_AUTRICHE', 33 );
	/// district d'autriche
	define( '_ZONE_DISTRICT_AUTRICHE', 34 );
	/// ville/commune d'autriche
	define( '_ZONE_VILLE_AUTRICHE', 35 );
	define( '_ZONE_ZIPCODE_AUTRICHE',50);

	/// Région du Pays bas
	define( '_ZONE_REGION_PAYSBAS', 36 );
	/// Province du Pays bas
	define( '_ZONE_PROV_PAYSBAS', 37 );
	/// ville/commune du Pays bas
	define( '_ZONE_VILLE_PAYSBAS', 38 );
	define( '_ZONE_ZIPCODE_PAYSBAS', 51);

	/// ville/commune d'irlande'
	define( '_ZONE_VILLE_IRLANDE', 39 );
	define( '_ZONE_ZIPCODE_IRLANDE',52);

	/// Région du Pays bas
	define( '_ZONE_REGION_BELGIQUE', 40 );
	/// Province du Pays bas
	define( '_ZONE_PROV_BELGIQUE', 41 );
	/// ville/commune du Pays bas
	define( '_ZONE_VILLE_BELGIQUE', 42 );
	define( '_ZONE_ZIPCODE_BELGIQUE', 53);

	/// Région du royaume uni
	define( '_ZONE_ETAT_ROYAUME_UNI', 54 );
	/// Province du royaume uni
	define( '_ZONE_COMTE_ROYAUME_UNI', 55 );
	/// ville/commune du royaume uni
	define( '_ZONE_DISTRICT_ROYAUME_UNI', 56 );
	define( '_ZONE_VILLE_ROYAUME_UNI', 57 );
	define( '_ZONE_ZIPCODE_ROYAUME_UNI', 58);

	// Continents du monde
	/// Continent : Afrique
	define( '_CONTINENT_AFRICA', 'AF' );
	/// Continent :  Antartique
	define( '_CONTINENT_ANTARCTICA', 'AN' );
	/// Continent :  Asie
	define( '_CONTINENT_ASIA', 'AS' );
	/// Continent :  Europe
	define( '_CONTINENT_EUROPE', 'EU' );
	/// Continent :  Océanie
	define( '_CONTINENT_OCEANIA', 'OC' );
	/// Continent :  Amérique du nord
	define( '_CONTINENT_NORTH_AMERICA', 'NA' );
	/// Continent :  Amérique du sud
	define( '_CONTINENT_SOUTH_AMERICA', 'SA' );
	/// @}

	// Identifiants des comparateurs de prix et places de marché
	/**	\ingroup marketplace
	 *	@{
	 */
	/// Comparateur de prix : Le Guide
	define( 'CTR_LEGUIDE', 1 );
	/// Comparateur de prix :  AchetezFacile (fermé)
	//define( 'CTR_ACHETEZFACILE', 2 );
	/// Comparateur de prix :  Kelkoo
	define( 'CTR_KELKOO', 3 );
	/// Comparateur de prix :  Google Shopping
	define( 'CTR_GOOGLE', 4 );
	/// Comparateur de prix :  Shopping.com
	define( 'CTR_SHOPPING', 5 );
	/// Comparateur de prix :  Shopzilla
	define( 'CTR_SHOPZILLA', 6 );
	/// Places de marché : Amazon
	define( 'CTR_AMAZON', 7 );
	/// Places de marché :  Amazon UK
	//define( 'CTR_AMAZON_UK', 18 );
	/// Places de marché :  Amazon DE
	//define( 'CTR_AMAZON_DE', 19 );
	/// Places de marché :  Amazon ES
	define( 'CTR_AMAZON_ES', 20 );
	/// Places de marché :  Amazon IT
	define( 'CTR_AMAZON_IT', 21 );
	/// Places de marché :  PriceMinster
	define( 'CTR_PRICEMINISTER', 8 );
	/// Places de marché :  Cdiscount
	define( 'CTR_CDISCOUNT', 9 );
	/// Comparateur de prix :  CDiscount pro
	define( 'CTR_CDISCOUNT_PRO', 33 );
	/// Places de marché :  Mon Corner Baby
	//define( 'CTR_CORNER_BABY', 22 );
	/// Places de marché :  Mon Corner Brico
	//define( 'CTR_CORNER_BRICO', 23 );
	/// Places de marché :  Mon Corner Déco
	//define( 'CTR_CORNER_DECO', 24 );
	/// Places de marché :  Mon Corner Homme
	//define( 'CTR_CORNER_HOMME', 25 );
	/// Places de marché :  Mon Corner Kids
	//define( 'CTR_CORNER_KIDS', 26 );
	/// Places de marché :  Mon Corner Jardin
	define( 'CTR_CORNER_JARDIN', 27 );
	/// Places de marché :  Mon Corner Parfum
	//define( 'CTR_CORNER_PARFUM', 28 );
	/// Comparateur de prix :  Twenga
	define( 'CTR_TWENGA', 10 );
	/// Places de marché :  Rue du Commerce
	define( 'CTR_RUEDUCOMMERCE', 11 );
	/// Places de marché :  Rue du Commerce - Mirakl
	define( 'CTR_RUEDUCOMMERCE_MIRAKL', 30 );
	/// Places de marché :  TRUFFAUT - Mirakl
	//define( 'CTR_TRUFFAUT_MIRAKL', 31 );
	/// Places de marché :  POURDEBON - Mirakl
	define( 'CTR_POURDEBON_MIRAKL', 32 );
	/// Comparateur de prix :  Cherchons
	define( 'CTR_CHERCHONS', 12 );
	/// Comparateur de prix :  Nextag
	define( 'CTR_NEXTAG', 13 );
	/// Places de marché :  FNAC
	define( 'CTR_FNAC', 14 );
	/// Places de marché :  Ebay
	define( 'CTR_EBAY', 15 );
	/// Comparateur de prix :  Touslesprix
	define( 'CTR_TOUSLESPRIX', 16 );
	/// Comparateur de prix :  Hellopro
	//define( 'CTR_HELLOPRO', 17 );
	/// Comparateur de prix :  Prixan
	define( 'CTR_PRIXAN', 34 );
	/// Pateforme de flux BeezUp
	define( 'CTR_BEEZUP', 35 );

	/// @}

	/// \cond onlyria
	/** \defgroup pricewatching Veille tarifaire
	 * 	\ingroup cpq
	 * 	@deprecated Ce module ne sera pas porté vers la nouvelle interface d'administration et ne dois pas
	 * 	être mis à disposition dans la nouvelle API.
	 * \ingroup model_constants
	 *	@{
	 */
	/// Concurrent : Client
	define('PRW_CLIENT', 1);
	/// Concurrent : Amazon
	define('PRW_AMAZON', 2);
	/// @}
	/// \endcond

	/** \defgroup const_payments Moyens de paiements
	 * \ingroup model_constants cpq
	 *	@{
	 */
	/// Moyen de paiement : Carte bancaire
	define( '_PAY_CB', 1 );
	/// Moyen de paiement : Chèque
	define( '_PAY_CHEQUE', 2 );
	/// Moyen de paiement : Chèque - 3 fois
	define( '_PAY_CHEQUE_X', 60 );
	/// Moyen de paiement : Compte / Encours
	define( '_PAY_COMPTE', 3 );
	/// Moyen de paiement : Espèces
	define( '_PAY_ESPECE', 4 );
	/// Moyen de paiement : Paypal
	define( '_PAY_PAYPAL', 5 );
	/// Moyen de paiement : Kwixo
	define( '_PAY_KWIXO', 6 ); // Deprecated
	/// Moyen de paiement : Virement
	define( '_PAY_VIREMENT', 11 );
	/// Moyen de paiement : Sofinco
	define( '_PAY_SOFINCO', 13 );
	/// Moyen de paiement : YesByCash
	define( '_PAY_YESBYCASH', 15 );
	/// Moyen de paiement : PAYLIB
	define( '_PAY_PAYLIB', 69 );
	/// Moyen de paiement : Choozen (paiement en plusieurs fois de Natifix)
	define( '_PAY_CHOOZEN', 101 );
	/// Moyen de paiement : Vide pour les commandes venant de Yuto sans moyen de paiement
	define( '_PAY_EMPTY', 229 );
	/// Moyen de paiement : Cofidis
	define ('_PAY_COFIDIS', 232);
	/// Moyen de paiement : Alma
	define ('_PAY_ALMA', 108);
	/// @}

	// Types de tri
	/**	\ingroup sort_type
	 *	@{
	 */
	/// Type de tri : tri personnalisé
	define( 'SORT_PERSO', 1 );
	/// Type de tri : tri alphabétique
	define( 'SORT_ALPHA', 2 );
	/// Type de tri : tri par prix
	define( 'SORT_PRICE', 3 );
	/// Type de tri : tri par référence
	define( 'SORT_REF', 4 );
	/// Type de tri : tri par marque
	define( 'SORT_BRAND', 5 );
	/// Type de tri : tri par taux d'information renseigné
	define( 'SORT_COMPLETION', 6 );
	/// Type de tri : tri par date de création
	define( 'SORT_DATE_CREATED', 7 );
	/// Type de tri : tri par date de publication
	define( 'SORT_DATE_PUBLISH', 8 );
	/// Type de tri : tri par date de promotion
	define( 'SORT_DATE_PROMO', 9 );
	/// Type de tri : tri par nombre de ventes réalisées
	define( 'SORT_SELLED', 10 );
	/// Type de tri : tri des lignes au hasard
	define( 'SORT_RANDOM', 11 );
	/// Type de tri : tri suivant le stock
	define( 'SORT_STOCK', 12 );
	/// @}

	// Profils globaux
	/** \defgroup field_constants_profiles Profils globaux
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Profil de compte : Administrateur
	define( 'PRF_ADMIN', 1 );
	/// Profil de compte : Client particulier
	define( 'PRF_CUSTOMER', 2 );
	/// Profil de compte : Client professionnel
	define( 'PRF_CUST_PRO', 3 );
	/// Profil de compte : Revendeur
	define( 'PRF_RESELLER', 4 );
	/// Profil de compte : Représentant / Vendeur
	define( 'PRF_SELLER', 5 );
	/// Profil de compte : Fournisseur
	define( 'PRF_SUPPLIER', 6 );
	/// Profil de compte : Utilisateur (profil des comptes créer par d'autre utilisateur - pour la gestion des droits)
	define( 'PRF_USER', 33 );
	/// Profil de compte : Contacts, ils ne peuvent pas ce connecter sur les sites
	define( 'PRF_CONTACT', 65 );
	/// @}

	// Types de nomenclature
	/** \defgroup field_constants_typ_nom Types de nomenclature
	 *	\ingroup model_constants
	 *	@{
	 */

	/// Type de nomenclature : Aucune
	define( 'NM_TYP_NONE', 0 );
	/// Type de nomenclature : Fabrication
	define( 'NM_TYP_BUILT', 1 );
	/// Type de nomenclature : Commerciale / composé
	define( 'NM_TYP_COMPOSED', 2 );
	/// Type de nomenclature : Commerciale / composant
	define( 'NM_TYP_COMPONENT', 3 );
	/// Type de nomenclature : Article lié
	define( 'NM_TYP_LINKED', 4 );
	/// Type de nomenclature : Variable
	define( 'NM_TYP_VARIABLE', 5 );
	/// Type de nomenclature : Commerciale / composé, composants optionnels
	define( 'NM_TYP_COMPOSED_OPT', 6 );
	/// @}

	// Type de contenu utilisant le Drag & Drop
	/**	\ingroup positions
	 *	@{
	 */
	/// Produits
	define( 'DD_PRODUCT',1 );
	/// Catégories de produits
	define( 'DD_CATEGORY',2 );
	/// CMS
	define( 'DD_CMS', 3 );
	/// Type de document
	define( 'DD_TYPE_DOCUMENT', 4 );
	/// Article de CGV
	define( 'DD_CGV_ARTICLE', 5 );
	/// Motif de retour
	define( 'DD_ORD_RETURN_REASON', 6 );
	/// Valeur de restriction d'un champ avancé
	define( 'DD_FLD_RESTRICTED_VALUE', 7 );
	/// Modèle de saisie
	define( 'DD_FLD_MODEL', 8 );
	/// Catégorie de champs
	define( 'DD_FLD_CATEGORY', 9 );
	/// Champ avancé
	define( 'DD_FLD', 10 );
	/// Segment avancé
	define( 'DD_SEGMENT', 11 );
	/// Image produit
	define( 'DD_PRD_IMAGE', 12 );
	/// Actualités
	define( 'DD_NEWS', 13 );
	/// Image d'actualité
	define( 'DD_NWS_IMAGE', 14 );
	/// Image de magasin
	define( 'DD_STR_IMAGE', 15 );
	/// Image de CMS
	define( 'DD_CMS_IMAGE', 16 );
	/// Service de livraison
	define( 'DD_DLV_SERVICES', 17 );
	/// Image de catégorie
	define( 'DD_CAT_IMAGE', 18 );
	/// Document
	define( 'DD_DOCUMENT', 19 );
	/// Modèle de comparateurs de prix
	define( 'DD_CTR_MODELS', 20 );
	/// Place de marché
	define( 'DD_CTR_MKT', 21 );
	/// Paniers Modèle
	define( 'DD_MODEL', 22 );
	/// Lignes du panier modèle
	define( 'DD_MODEL_PRODUCTS', 23 );
	/// Articles liés
	define( 'DD_PRODUCT_RELATED', 24 );
	/// Marques
	define( 'DD_BRAND', 25 );
	/// Position de champs personnalisé d'une classe
	define( 'DD_FLD_POS', 26 );
	/// Position d'une image d'un objet
	define( 'DD_IMAGES_OBJECT', 27 );
	/// Catégories de Faq
	define( 'DD_FAQ_CATEGORY', 28 );
	/// Questions de Faq
	define( 'DD_FAQ_QUESTIONS', 29 );
	/// Produits par marques
	define( 'DD_BRD_PRODUCTS', 30 );
	/// Image de catégorie dans FAQ
	define( 'DD_FAQ_CAT_IMAGE', 31 );
	/// Image de question dans FAQ
	define( 'DD_FAQ_QST_IMAGE', 32 );
	/// Produits dans la commande
	define( 'DD_ORD_PRODUCTS', 33 );
	// Store
	define('DD_STORE', 34);
	// Types de document
	define('DD_DOC_TYPE_IMAGE', 35);
	// Documents
	define('DD_DOC_IMAGE', 36);
	/// @}

	// hosts pour les médias
	/**	\ingroup model_medias
	 *	@{
	 */
	define( 'HST_YOUTUBE', 1 );
	define( 'HST_DAILYMOTION', 2 );
	define( 'HST_WATTV', 3 );
	define( 'HST_VIMEO', 4 );
	/// @}

	// Type de message global
	/**	\ingroup messages
	 *	@{
	 */
	/// Type de message : Avis consommateur sur les produits
	define( 'MSG_TYPE_RVW_PRODUCT', 5 );
	/// Type de message : SAV
	define( 'MSG_TYPE_SAV', 28 );
	/// @}

	// Type de periods
	/**	@{
	 */
	/// Type de période : Périodes d'activité ( Horaires de travail ) d'un utilisateur
	define( 'PER_USER_SHIFT', 1 );
	/// @}

	// classes systèmes
	/** \defgroup field_constants_classes Classes d'objets
	 *	\ingroup model_constants
	 *	@{
	 */

	/// Classe système : DEVIS
	define( 'CLS_DEVIS', -1 );
	/// Classe système : Produits
	define( 'CLS_PRODUCT', 1 );
	/// Classe système : Comptes utilisateurs
	define( 'CLS_USER', 2 );
	/// Classe système : Catégories
	define( 'CLS_CATEGORY', 3 );
	/// Classe système : Bons de commande
	define( 'CLS_ORDER', 4 );
	/// Classe système : Marques
	define( 'CLS_BRAND', 5 );
	/// Classe système : Magasins
	define( 'CLS_STORE', 6 );
	/// Classe système : Messages de contacts
	define( 'CLS_MESSAGE', 7 );
	/// Classe système : Lignes de bons de commande
	define( 'CLS_ORD_PRODUCT', 8 );
	/// Classe système : Conditionnements de produits
	define( 'CLS_PRD_COLISAGE', 9 );
	/// Classe système : Secteurs de livraison (voir les modifications dans fields.inc.php)
	define( 'CLS_SECTOR', 10 );
	/// Classe système : Articles de gestion de contenu
	define( 'CLS_CMS', 11 );
	/// Classe système : Types de documents
	define( 'CLS_TYPE_DOCUMENT', 12 );
	/// Classe système : Catégories de FAQ
	define( 'CLS_FAQ_CAT', 13 );
	/// Classe système : Actualités
	define( 'CLS_NEWS', 14 );
	/// Classe système : Catégories d'actualités
	define( 'CLS_NEWS_CAT', 15 );
	/// Classe système : Questions de FAQ
	define( 'CLS_FAQ_QST', 16 );
	/// Classe système : Documents
	define( 'CLS_DOCUMENT', 17 );
	/// Classe système : Bannières
	define( 'CLS_BANNER', 19 );
	/// Classe système : Articles de conditions générales de vente
	define( 'CLS_CGV_ARTICLE', 20 );
	/// Classe système : Relations entre les produits
	define( 'CLS_PRD_RELATIONS', 21 );
	/// Classe système : Erratums sur le catalogue
	define( 'CLS_ERRATUM', 22 );
	/// Classe système : Sites des locataires
	define( 'CLS_WEBSITE', 23 );
	/// Classe système : Lignes de factures
	define( 'CLS_INV_PRODUCT', 24 );
	/// Classe système : Lignes de bons de livraison
	define( 'CLS_BL_PRODUCT', 25 );
	/// Classe système : Lignes de préparations de livraison
	define( 'CLS_PL_PRODUCT', 26 );
	/// Classe système : Factures
	define( 'CLS_INVOICE', 27 );
	/// Classe système : Bons de livraison
	define( 'CLS_BL', 29 );
	/// Classe système : Classements des produits dans les familles
	define( 'CLS_CLASSIFY', 30 );
	/// Classe système : Catégories comptables
	define( 'CLS_ACCOUNTING_CATEGORY', 32 );
	/// Classe système : Catégories tarifaires
	define( 'CLS_PRICE_CATEGORY', 33 );
	/// Classe système : Profils / droits d'accès
	define( 'CLS_PROFIL', 34 );
	/// Classe système : Inscrits à la newsletter
	define( 'CLS_NLR_SUBSCRIBERS', 35 );
	/// Classe système : Codes promotions
	define( 'CLS_PMT_CODE', 36 );
	/// Classe système : Catégories de newsletters
	define( 'CLS_NLR_CATEGORY', 37 );
	/// Classe système : Services de livraison
	define( 'CLS_DLV_SERVICE', 40 );
	/// Classe système : Moyens de paiements
	define( 'CLS_ORD_PAYMENT', 41 );
	/// Classe système : Images
	define( 'CLS_IMAGE', 42 );
	/// Classe système : Tarifs
	define( 'CLS_PRICE', 45 );
	/// Classe système : Notes / commentaires
	define( 'CLS_NOTE', 47 );
	/// Classe système : Catalogue sur les comparateurs de prix / places de marché (produits)
	define( 'CLS_CTR_CATALOGS', 48 );
	/// Classe système : Liste de produits personnalisée
	define( 'CLS_WISHLISTS', 49 );
	/// Classe système : Retours sur commande
	define( 'CLS_RETURN', 52 );
	/// Classe système : Adresses de comptes
	define( 'CLS_ADDRESS', 55 );
	/// Classe système : Types de vente des magasins
	define( 'CLS_STR_SALE_TYPE', 56 );
	/// Classe système : Hiérarchie entre les produits
	define( 'CLS_PRD_HIERARCHY', 57 );
	/// Classe système : Images secondaires de produits
	define( 'CLS_PRD_IMAGE', 58 );
	/// Classe système : Moyens de paiement des clients
	define( 'CLS_USR_PAYMENT', 59 );
	/// Classe système : Bons de préparation (PL)
	define( 'CLS_PL', 60 );
	/// Classe système : Taux de TVA sur les produits
	define( 'CLS_TVA', 61 );
	/// Classe système : Version de conditions générales de vente
	define( 'CLS_CGV_VERSION', 62 );
	/// Classe système : Exemptions de TVA
	define( 'CLS_EXEMPT_TVA', 63 );
	/// Classe système : Conditionnements
	define( 'CLS_COLISAGE', 64 );
	/// Classe système : Conditions des tarifs
	define( 'CLS_PRICE_CONDITION', 65 );
	/// Classe système : Conditions des exonérations
	define( 'CLS_EXEMPT_TVA_CONDITION', 66 );
	/// Classe système : Restrictions sur les produits
	define( 'CLS_RESTRICT_PRODUCT', 67 );
	/// Classe système : Restrictions sur les catégories
	define( 'CLS_RESTRICT_CATEGORY', 68 );
	/// Classe système : Restrictions sur les marques
	define( 'CLS_RESTRICT_BRAND', 69 );
	/// Classe système : Dépôts de stockage
	define( 'CLS_DEPOSIT', 70 );
	/// Classe système : Stocks multi-dépôts
	define( 'CLS_STOCK', 71 );
	/// Classe système : Signature sur commande
	define( 'CLS_ORDER_SIGNATURE', 72 );
	/// Classe système : Types de relation (entre produits)
	define( 'CLS_RELATION_TYPE', 73 );
	/// Classe système : Champs disponibles pour la tarification
	define( 'CLS_FIELD_PRICE', 74 );
	/// Classe système : Modèle pour les CTR / MKT
	define( 'CLS_CTR_MODELS', 75 );
	/// Classe système : Comparateurs de prix / Places de marché
	define( 'CLS_CTR_MKT', 76 );
	/// Classe système : Playlist
	define( 'CLS_PLAYLIST', 77 );
	/// Classe système : Vidéo
	define( 'CLS_VIDEO', 78 );
	/// Classe système : Chaine (sur Youtube, Dailymotion...)
	define( 'CLS_CHANNEL', 79 );
	/// Classe système : Champs disponibles pour l'exonération
	define( 'CLS_FIELD_TVA', 80 );
	/// Classe système : Unités de vente
	define( 'CLS_SELL_UNIT', 81 );
	/// Classe système : Statuts personnalisés de commande
	define( 'CLS_ORD_STATE_NAME', 82 );
	/// Classe système : Options (emplacements) de nomenclature
	define( 'CLS_PRD_NOM_OPTION', 83 );
	/// Classe système : Modèles de réglements
	define( 'CLS_ORD_PAYMENT_MODEL', 84 );
	/// Classe système : Informations bancaires
	define( 'CLS_BANK_DETAIL', 85 );
	/// Classe système : Type de rapport de visite
	define( 'CLS_REPORT_TYPE', 86 );
	/// Classe système : Rapport de visite
	define( 'CLS_REPORT', 87 );
	/// Classe système : Code risque
	define( 'CLS_RISK_CODES', 88 );
	/// Classe système : Informations des revendeurs sur les produits
	define( 'CLS_PRD_RESELLER', 89 );
	/// Classe système : Relations entre les classes
	define( 'CLS_RELATIONS', 90 );
	/// Classe système : Règles de négociations
	define( 'CLS_TRADING_RULES', 91 );
	/// Classe système : Demandes de modérations
	define( 'CLS_UPDATE_REQUESTS', 103 );
	/// Classe système : Check-in (lien avec les rapports)
	define( 'CLS_CHECKIN', 112 );
	/// Classe système : Exercice comptable
	define( 'CLS_FISCAL_YEAR', 116 );
	/// Classe système : Périodes (Représente un type de période de temps pour un object ex: Horaires d'activité )
	define( 'CLS_PERIODS_TYPES', 117 );
	/// Classe système : Périodes (Représente une période de temps pour un object ex: 9:00-12:00 )
	define( 'CLS_PERIODS', 118 );
	/// Classe système : Objet d'image (Represente un object d'image)
	define( 'CLS_IMAGES_OBJECT', 122);
	/// Classe système : Type d'objet d'image (Represente un type d'object)
	define( 'CLS_IMAGES_OBJECT_TYPES', 123);
	/// Classe système : Appels téléphoniques
	define( 'CLS_CALLS', 127);
	/// Classe système : Packaging de livraison
	define( 'CLS_DLV_PACKAGES', 130);
	/// Classe système : Modèle de saisie
	define( 'CLS_FLD_MODELS', 200 );
	/// Classe système : Echéancier sur les commandes
	define( 'CLS_ORD_INSTALLMENTS', 201 );
	/// Classe système : Notifications
	define( 'CLS_NOTIFICATIONS', 202 );
	/// Classe système : Historique des téléchargements de documents
	define( 'CLS_DOCUMENTS_HISTO', 203 );
	/// Classe système : Historique des accès de paiements
	define( 'CLS_PAYMENT_ACCESS_HISTO', 204 );
	/// Classe système : Historique des retours de paiements
	define( 'CLS_PAYMENT_RETURN_HISTO', 205 );
	/// Classe système : Liste de produits suivi
	define( 'CLS_FOLLOWED_LIST', 206 );
	/// Classe système : Liste des utilisateurs concerné par le suivi
	define( 'CLS_FOLLOWED_LIST_USR', 207 );
	/// Classe système : Liste des produits concerné par le suivi
	define( 'CLS_FOLLOWED_LIST_PRD', 208 );
	/// Classe système : Liste des relevés de prix
	define( 'CLS_LINEAR_RAISED', 209 );
	/// Classe système : Liste des offres sur les relevés de prix
	define( 'CLS_LINEAR_OFFERS', 210 );
	/// Classe système : Liste des status pour sur les relevés de prix
	define( 'CLS_LINEAR_STATES', 211 );
	/// Classe système : Liste rayons possible pour les listes de produits
	define( 'CLS_FOLLOWED_LIST_SECTIONS', 212 );
	/// Classe personnalisé
	define( 'CLS_CLASSES', 10000 );
	// Classe système : statistiques
	define( 'CLS_STATS', 207 );
	// Classe système : statistiques des objectifs
	define( 'CLS_STATS_GOALS', 250 );
	// Classe système : Types d'objectifs
	define( 'CLS_OBJECTIF_TYPES', 251 );
	// Classe système : Objectifs
	define( 'CLS_OBJECTIFS', 252 );
	// Classe système : Chat conversations
	define( 'CLS_CHAT_CONVERSATIONS', 253 );
	// Classe système : Chat messages
	define( 'CLS_CHAT_MESSAGES', 254 );
	// Classe système : Zones
	define( 'CLS_ZONES', 255 );
	// Classe système : Licences Yuto
	define( 'CLS_DEV_SUB', 256);
	/// Classe système : Lignes des bons de retours
	define( 'CLS_RETURN_PRODUCTS', 257 );
	/// Classe système : object signature
	define( 'CLS_SIGNATURES', 258 );
	/// Classe système : actions effectuées
	define( 'CLS_ACTIONS_HISTORY', 259 );
	/// Classe système : Récompenses des produits
	define( 'CLS_PRD_REWARDS', 260 );
	/// Classe système : Statistiques des récompenses
	define( 'CLS_STATS_REWARDS', 261 );
	/// Classe système : Serials des objets
	define( 'CLS_SERIALS', 262 );
	/// Classe système : Champs avancés
	define( 'CLS_FIELDS', 263 );
	/// Classe système : Valeur des champs avancés
	define( 'CLS_FLD_VALUES', 264 );
	/// @}

	// Relations entre classe
	/// Hiérarchie entre les comptes clients
	define( 'REL_USR_HIERARCHY', 1 );
	/// Hiérarchie entre les comptes clients pour les commerciaux
	define( 'REL_SELLER_HIERARCHY', 2 );
	/// Hiérarchie entre les comptes clients et les magasins
	define( 'REL_RESELLER_STORE_HIERARCHY', 12 );

	// Typage des champs avancés
	/** \defgroup field_constants_types Types de champs avancé
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Type de champ avancé : texte court
	define( 'FLD_TYPE_TEXT', 1 );
	/// Type de champ avancé : texte formaté TinyMCE (si "fld_old_txt_type" = 0, texte long non formaté)
	define( 'FLD_TYPE_TEXTAREA', 2 );
	/// Type de champ avancé : nombre entier
	define( 'FLD_TYPE_INT', 3 );
	/// Type de champ avancé : nombre à virgule flottante
	define( 'FLD_TYPE_FLOAT', 4 );
	/// Type de champ avancé : liste de choix à sélection unique (stockage val_name)
	define( 'FLD_TYPE_SELECT', 5 );
	/// Type de champ avancé : liste de choix à sélection multiple (stockage val_name, séparateur ", ")
	define( 'FLD_TYPE_SELECT_MULTIPLE', 6 );
	/// Type de champ avancé : image
	define( 'FLD_TYPE_IMAGE', 7 );
	/// Type de champ avancé : booléen (stockage "Oui" / "Non" ou "1" / "0")
	define( 'FLD_TYPE_BOOLEAN_YES_NO', 8 );
	/// Type de champ avancé : Obsolète, type images
	define( 'FLD_TYPE_IMAGES', 9 );
	/// Type de champ avancé : date ("fld_old_txt_type" = 0 / 1 pour le stockage de l'heure oui / non)
	define( 'FLD_TYPE_DATE',10 );
	/// Type de champ avancé : pointeur (stockage de l'ID pointé, la classe est stockée dans fld_related_class)
	define( 'FLD_TYPE_REFERENCES_ID',11 );
	/// Type de champ avancé : liste de choix hiérarchisée à sélection multiple (stockage val_id, séparateur ", ")
	define( 'FLD_TYPE_SELECT_HIERARCHY',12 );
	/// Type de champ avancé : type d'upload de fichier
	define( 'FLD_TYPE_FILE_UPLOAD',13 );
	/// @}

	// Gestion des retours
	/**	\ingroup model_ord_returns
	 *	@{
	 */
	/// Délai légal de retour après livraison
	define('ORD_RETURNS_LEGAL_DELAY', 14);

	/// Etat en cours de création
	define('ORD_RETURNS_STATE_CREATE', 1);
	/// Demande effectuée
	define('ORD_RETURNS_STATE_QUERY', 2);
	/// En attente de produit
	define('ORD_RETURNS_STATE_WAIT', 3);
	/// Produit retourné en attente d'examen
	define('ORD_RETURNS_STATE_RETURNED', 4);
	/// Retour partiellement traité
	define('ORD_RETURNS_STATE_PARTIAL', 5);
	/// Retour traité
	define('ORD_RETURNS_STATE_COMPLETE', 6);
	/// Retour annulé
	define('ORD_RETURNS_STATE_CANCEL', 7);

	/// Etat de produit en cours de création
	define('ORD_RETURNS_PRD_STATE_CREATE', 1);
	/// Demande effectuée
	define('ORD_RETURNS_PRD_STATE_QUERY', 2);
	/// En attente de réception
	define('ORD_RETURNS_PRD_STATE_WAIT', 3);
	/// Produit réceptionné en attente d'examen
	define('ORD_RETURNS_PRD_STATE_RETURNED', 4);
	/// Retour refusé
	define('ORD_RETURNS_PRD_STATE_FAILURE', 5);
	/// Retour accepté
	define('ORD_RETURNS_PRD_STATE_SUCCESS', 6);

	// mode avoir
	/// Gestion des retours : mode Avoir
	define('ORD_RETURNS_MODE_AVOIR', 2);
	/// @}

	// Produit
	/** \defgroup field_constants_types_price Prix de base / de vente
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Prix de base du produit
	define( 'PRD_PRICE_PUBLIC_HT', 'price_public_ht' );
	/// prix de vente du produit
	define( 'PRD_PRICE_USR_HT', 'price_usr_ht' );

	/// Prix sur les produits
	define( 'PRC_KIND_DEFAULT', 1 );
	/// prix sur les taxes
	define( 'PRC_KIND_TAXES', 2 );
	/// @}

	// code erreur sur le contrôle de l'application des points de fidélité
	/**	\defgroup model_constants_rewards Programme de fidélité
	 * 	\ingroup model_constants
	 *	@{
	 */
	/// Tous les paramètres obligatoires ne sont pas fournis
	define( 'ERROR_RWC_PARAMS', -1 );
	/// Envoyer à un ami - limite journalière de point est atteinte
	define( 'ERROR_RWC_FRIEND_MAX', -2 );
	/// Envoyer à un ami - le destinataire a déjà fait l'objet d'une recommandation de produit
	define( 'ERROR_RWC_FRIEND_MAIL', -3 );
	/// Anniversaire - Aucune commande a été passée
	define( 'ERROR_RWC_ORD_BIRTHDAY', -4 );
	/// Passage d'une commande - Aucune commande existe
	define( 'ERROR_RWC_NO_ORDER', -5 );
	/// Passage d'une commande - Le montant de la commande n'est pas assez élevé pour recevoir des points
	define( 'ERROR_RWC_MIN_ORDER', -6 );
	/// Passage de la Nième commande - Le palier n'existe pas
	define( 'ERROR_RWC_NO_NTH', -7 );
	/// Passage de la 1er commande - il ne s'agit pas de la première commande
	define( 'ERROR_RWC_NO_FIRST_ORDER', -8 );
	/// Anniversaire - points déjà attribué cette année
	define( 'ERROR_RWC_BIRTHDAY', -9 );
	/// @}

	/** \defgroup field_constants_ord_products Champs avancés - Ligne de commande
	 *	\ingroup model_constants
	 *	@{
	 */

	/// Identifiant du conditionnement
	define( '_FLD_PRD_COL_ORD_PRODUCT', 490);
	/// Produit exclus des statistiques
	define( '_FLD_PRD_EXCLUDE_FROM_STATS', 3784 );
	/// Prix négociable : Oui / Non
	define( '_FLD_PRD_ORD_FREE', 491 );
	/// Identifiant de l'option dans une nomenclature variable
	define( '_FLD_PRD_ORD_OPT_ID', 1138 );
	/// Identifiant de la ligne de commande des places de marché
	define( '_FLD_PRD_ORD_MKT_ID', 1265 );
	/// Prix brut unitaire HT de la ligne
	define( '_FLD_PRD_ORD_PRICE_BRUT', 1589 );
	/// Information pour savoir si l'expédition a bien été notifié aux comparateurs / places de marché
	define( '_FLD_PRD_ORD_CTR_SHIPPED', 1829 );
	/// Carte cadeau envoyée
	define( '_FLD_PRD_ORD_GIFT_SEND', 2363 );
	/// Identifiant du service de livraison utilisé pour cette ligne de commande
	define( '_FLD_PRD_ORD_SRV_ID', 2965 );
	/// Quantité unitaire pour les articles vendus au poids
	define( '_FLD_PRD_ORD_QTE_UNIT', 3026 );
	/// Destinataire d'une carte cadeau
	define( '_FLD_PRD_ORD_GIFT_DEST', 3252 );
	/// Permet d'identifier une ligne de commande ajouté par le système de promotion spéciale "Remise" des articles hors stock
	define( '_FLD_PRD_ORD_REMISE_OUTSTOCK', 3436 );
	/// Permet d'identifier une ligne de commande en reliquat (oui = toujours en reliquat, non = jamais en reliquat, vide = fonctionnement normale)
	define( '_FLD_PRD_DELAYED', 3851 );
	/// Prix de vente client final
	define( '_FLD_PRD_ORD_PVC', 3571 );
	/// Numéro(s) de carte(s) cadeau(x) lié(s) à une ligne de commande
	define( '_FLD_PRD_ORD_CODE_GIFT', 5015 );
	/// Référence du produit composer
	define( '_FLD_PRD_ORD_COMPOSER', 5018 );
	/// Commande relancé par email
	define( '_FLD_ORD_CART_NOTIFY', 2816 );
	/// Code promotions applicables
	define('_FLD_PRD_APPLICABLE_DISCOUNTS', 5029);
	/// Prix HT avant remise
	define('_FLD_PRD_PRICE_BEFORE_DISCOUNTS', 5030);
	/// Liste de marque autorisé
	define('_FLD_USR_BRAND', 5057);
	/// Information sur le DEEE
	define( '_FLD_PRD_ORD_DEEE', 5074 );

	/// @}

	/** \defgroup field_constants_website Champs avancés - Site internet
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Description du site
	define( '_FLD_WST_DESC', 760 );
	/// Titre utilisé dans la baslise meta-title (site)
	define( '_FLD_WST_TAG_TITLE', 655 );
	/// Description utilisée dans la baslise meta-description (site)
	define( '_FLD_WST_TAG_DESC', 656 );
	/// Mots clés utilisés dans la baslise meta-keywords (site)
	define( '_FLD_WST_TAG_KEYWORDS', 661 );

	/// @}

	/** \defgroup field_constants_user Champs avancés - Compte client
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Catégorie tarifaire du compte (gu_users->usr_prc_id)
	define( '_FLD_USR_PRC', 456 );
	/// Référence du compte (gu_users->usr_ref)
	define( '_FLD_USR_REF', 455 );
	/// Date inscription (gu_users->usr_date_created)
	define( '_FLD_USR_DATE_SUBSCRIPT', 497 );
	/// Nombre de commande validée gu_users->(usr_orders - usr_orders_cancelled)
	define( '_FLD_USR_COUNT_CMD', 501 );
	/// Identifiant compte (gu_users->usr_id)
	define( '_FLD_USR_ID', 1210 );
	/// Catégorie comptable du compte (gu_users->usr_cac_id)
	define( '_FLD_USR_CAC', 513 );
	/// Profil / droit d'accès du compte (gu_users->usr_prf_id)
	define( '_FLD_USR_PRF', 1212 );
	/// Verrouillage de l'encours
	define( '_FLD_USR_ENCOURS_LOCK', 1230 );
	/// Le compte est en sommeil oui / non
	define( '_FLD_USR_SLEEP', 3402 );
	/// Référence de la central d'achat
	define( '_FLD_USR_CENTRAL', 3634 );
	/// Dernière relance client
	define( '_FLD_USR_BOOST_SEND', 3647 );
	/// Surnom utilisé sur le compte client
	define( '_FLD_PSEUDO', 685 );
	/// Identifiant Facebook
	define( '_FLD_USR_FACEBOOK', 3261 );
	/// Identifiant Twitter
	define( '_FLD_USR_TWITTER', 3263 );
	/// Identifiant Google
	define( '_FLD_USR_GOOGLE', 4031 );
	/// Identifiant Paypal
	define( '_FLD_USR_PAYPAL', 4032 );
	/// Identifiant provider indique si un compte est ratacher a un provider
	define( '_FLD_USR_PROVIDER', 4061 );
	/// Numéro de carte de fidélité
	define( '_FLD_USR_CARD_RWD', 5005 );
	/// Date de connexion du dernier compte enfant
	define( '_FLD_USR_LAST_LOGIN_CHILD', 5014);

	/// @}

	/** \defgroup field_constants_pvd Provider
	 *	\ingroup model_constants
	 *	@{
	 */
	/// identifiant twitter
	define( '_PVD_TWITTER', 1 );
	/// identifiant facebook
	define( '_PVD_FACEBOOK', 2 );
	/// identifiant Google
	define( '_PVD_GOOGLE', 3 );
	/// identifiant paypal
	define( '_PVD_PAYPAL', 4 );
	/// @}

	/** \defgroup field_constants_adr Champs avancés - Adresse de compte client
	 *	\ingroup model_constants
	 *	@{
	 */

	/// Code pays d'une adresse postal d'un compte client
	define( '_FLD_ADR_CNT_CODE', 3844 );

	/// @}

	/** \defgroup field_constants_yuto Champs avancés - Yuto
         *      \ingroup model_constants
         *      @{
         */

        /// Paiement de l'abonnement Yuto après la période d'essai
	define( '_FLD_YUTO_PAY_ABO_TEST', 5039);
	/// Paiement de l'abonnement Yuto
	define( '_FLD_YUTO_PAY_ABO', 5040);

        /// @}

	/** \defgroup field_constants_order Champs avancés - Commande
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Détermine l'identifiant de CGV applicable lors du passage d'une commande
	define( '_FLD_ORD_CGV', 666 );
	/// Détermine la personne qui a refusée ou validée un panier
	define( '_FLD_ORD_WHO', 808 );
	/// Contient le message expliquant pourquoi la commande a été refusée
	define( '_FLD_ORD_WHY', 809 );
	/// Nombre de points de fidélité utilisés sur une commande
	define( '_FLD_ORD_PTS', 1129 );
	/// Montant de la remise accordée par l'option 4 du programme de fidélité (Remise automatique)
	define( '_FLD_ORD_RWD_OPT4_HT', 5007 );
	/// Date de mise à jour du status de commande
	define( '_FLD_ORD_UPD_STATUT', 1137 );
	/// Identifiant du statut de commande
	define( '_FLD_ORD_STATE_ID', 3922 );
	/// Information pour savoir si l'expédition a bien été notifié aux comparateurs / places de marché
	define( '_FLD_ORD_CTR_SHIPPED', 1063 );
	/// Référence de la commande chez la marketplace
	define( '_FLD_ORD_CTR_REF', 1753);
	/// ord_orders->ord_comments		// Commentaires sur la commande (traduction des paniers modèles)
	define( '_FLD_ORD_COMMENTS', 2191 );
	/// Identifiant d'une commande dans un systeme de paiement (pour le moment sur YesByCash)
	define( '_FLD_ORD_BARCODE', 3063 );
	/// Url permettant la validation d'une commande payée via YesByCash
	define( '_FLD_ORD_URl_YESBYCASH', 3070 );
	/// Token permettant l'authentification de la commande envoyer par email via FDV
	define( '_FLD_ORD_NOTIFY_KEY', 3264);
	/// Commande en HT : Oui / Non
	define( '_FLD_ORD_IN_HT', 3808 );
	/// (deprecated) - Délai de signature d'une commande
	define( '_FLD_ORD_SIGN_DELAY', 4161 );
	/// Date de fin de signature d'une commande
	define( '_FLD_ORD_PIPE_SIGN_DATE', 4136);
	/// Date de validation d'une commande
	define( '_FLD_ORD_PIPE_SIGN_WIN_DATE', 5016);
	/// Taux estimé de signature d'une commande
	define( '_FLD_ORD_SIGN_RATE', 4162 );
	/// Gain parrainage envoyé
	define( '_FLD_ORD_SPONSOR', 5001);
	/// Identifiant du nouveau panier après copie
	define( '_FLD_ORD_COPY_CART_NEW', 5003);
	/// Identifiant de l'ancien panier utilisé pour la copie
	define( '_FLD_ORD_COPY_CART_OLD', 5004);
	/// Identifiant de la commande sur la place de marché
	define( '_FLD_ORD_MKT_ID', 5006 );
	/// Identifiant de champs pour la gestion de la notion de LDD
	define( '_FLD_ORD_DLV_HOME', 496 );
	/// Champ qui stock les information de la chronordv pour une commande
	define( '_FLD_DLV_CHRONORDV', 4287);
	/// Champ avis vérifie
	define( '_FLD_ORD_AVIS_VERIFIE', 5031);
	/// Champ "Frais de port 1"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_1', 5032);
	///Champ "Frais de port 2"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_2', 5033);
	///Champ "Frais de port 3"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_3', 5094);
	///Champ "Frais de port 4"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_4', 5095);
	///Champ "Frais de port 5"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_5', 5096);
	///Champ "Frais de port 6"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_6', 5097);
	///Champ "Frais de port 7"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_7', 5098);
	///Champ "Frais de port 8"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_8', 5099);
	///Champ "Frais de port 9"
	define('_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_9', 5100);
	/// Numéro de transaction PayPlug
	define( '_FLD_ORD_PAYPLUG_PAY_ID', 5063 );
	/// Identifiant de commande Glagla Relais
	define( '_FLD_ORD_TENTATIVE_ID', 5081 );
	/// Identifiant de la commande sur la place de marché
	define( '_FLD_ORD_MKT_ACCOUNT_ID', 5083 );
	/// Identifiant de la commande sur la place de marché
	define( '_FLD_ORD_MKT_CODE', 5084 );
	/// @}

	/** \defgroup field_constants_colisage Champs avancés - Conditionnement
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Identifiant du conditionnement rattaché au produit (prd_colisage_classify->cly_col_id)
	define( '_FLD_PRD_CLY_COLISAGE', 488 );

	/// @}

	/** \defgroup field_constants_brand Champs avancés - Marque
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la marque (prd_brands->brd_name)
	define( '_FLD_BRD_NAME', 522 );
	/// Titre de la marque (prd_brands->brd_title)
	define( '_FLD_BRD_TITLE', 591);
	/// Description de la marque (prd_brands->brd_desc)
	define( '_FLD_BRD_DESC', 590 );
	/// Url de la marque (prd_brands->brd_url_alias)
	define( '_FLD_BRD_URL', 620 );
	/// Identifiant de la marque (prd_brands->brd_id)
	define( '_FLD_BRD_ID', 697 );
	/// Url du site de la marque (prd_brands->brd_url)
	define( '_FLD_BRD_WEB', 3665 );
	/// Titre utilisé pour le référencement (prd_brands->brd_tag_title)
	define( '_FLD_BRD_TAG_TITLE', 4190 );
	/// Description utilisée pour le référencement (prd_brands->brd_tag_desc)
	define( '_FLD_BRD_TAG_DESC', 4191 );
	/// Mots clés utilisés pour le référencement (prd_brands->brd_tag_keywords)
	define( '_FLD_BRD_KEYWORDS', 4192 );

	/// @}

	/** \defgroup field_constants_product Champs avancés - Produit
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Référence du produit
	define( '_FLD_PRD_REF', 583 );
	/// Nom du produit
	define( '_FLD_PRD_NAME', 518 );
	/// Titre du produit
	define( '_FLD_PRD_TITLE', 532 );
	/// Description du produit
	define( '_FLD_PRD_DESC', 533 );
	/// Description longue du produit
	define( '_FLD_PRD_DESC_LG', 534 );
	/// Meta-title du produit
	define( '_FLD_PRD_TAG_TITLE', 587 );
	/// Meta-description du produit
	define( '_FLD_PRD_TAG_DESC', 588 );
	/// Meta-keywords du produit
	define( '_FLD_PRD_TAG_KEYWORD', 589 );
	/// Produit centralisé OUI / NON
	define( '_FLD_PRD_CENTRALIZED', 507 );
	/// Frais de port OUI / NON
	define( '_FLD_IS_PORT', 511 );
	/// Identifiant de la marque rattachée au produit
	define( '_FLD_PRD_BRD_ID', 505 );
	/// Publication du produit OUI / NON
	define( '_FLD_PRD_PUBLISH', 609 );
	/// Détermine si le produit est commandable
	define( '_FLD_PRD_ORDERABLE', 674 );
	/// Identifiant du produit
	define( '_FLD_PRD_ID', 695 );
	/// Prix d'achat du produit
	define( '_FLD_PRD_PURCHASE', 3375 );
	/// Identifiant du produit sur eBay
	define( '_FLD_PRD_EBAY_ID', 2246 );
	/// Date de désactation du produit sur eBay
	define( '_FLD_PRD_EBAY_DISABLED', 2248 );
	/// Date de fin de vente du produit
	define( '_FLD_PRD_EBAY_END', 2284 );
	/// Coûts de mise en vente du produit
	define( '_FLD_PRD_EBAY_FEES', 2285 );
	/// Produit non remisable suivant la catégorie tarifaire
	define( '_FLD_PRD_NO_DISCOUNT', 3027 );
	/// Identifiant du produit chez Amazon (code ASIN)
	define( '_FLD_PRD_ASIN_AMAZON', 3108 );
	/// Unité de vente du produit (ex. par 2)
	define( '_FLD_PRD_SALES_UNIT', 3567 );

	/// @}

	/** \defgroup field_constants_fields Champs avancés - Champs avancés
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Désignation des champs avancés
	define( '_FLD_FLD_NAME', 5092 );
	/// @}

	/** \defgroup field_constants_category Champs avancés - Catégorie de produits
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la catégorie
	define( '_FLD_CAT_NAME', 512 );
	/// titre de la catégorie
	define( '_FLD_CAT_TITLE', 531 );
	/// description de la catégorie
	define( '_FLD_CAT_DESC', 530 );
	/// meta-title de la catégorie
	define( '_FLD_CAT_TAG_TITLE', 584 );
	/// meta-description de la catégorie
	define( '_FLD_CAT_TAG_DESC', 585 );
	/// meta-keyword de la catégorie
	define( '_FLD_CAT_TAG_KEYWORD', 586);
	/// url de la catégorie
	define( '_FLD_CAT_URL', 612 );
	/// url personnalisée de la catégorie
	define( '_FLD_CAT_URL_PERSO', 2780 );
	/// identifiant de la catégorie
	define( '_FLD_CAT_ID', 696 );
	/// Filtre par marque
	define( '_FLD_CAT_FILTER_BRD', 1243 );
	/// Filtre par disponibilité
	define( '_FLD_CAT_FILTER_STOCK', 5073 );
	/// Filtre par famillle
	define( '_FLD_CAT_FILTER_CATCHILDREN', 5091 );

	/// @}

	/** \defgroup field_constants_classify Champs avancés - Classification
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Titre utilisé pour le référencement
	define( '_FLD_CLY_TAG_TITLE', 934 );
	/// Description utilisée pour le référencement
	define( '_FLD_CLY_TAG_DESC', 935 );
	/// Mots clés utilisés pour le référencement
	define( '_FLD_CLY_KEYWORDS', 936 );
	/// Url personnalisée du produit pour une catégorie
	define( '_FLD_CLY_URL_PERSO', 2779 );
	/// Url du produit pour une catégorie
	define( '_FLD_PRD_URL', 613 );
	/// Titre du produit utilisé pour le référencement
	define( '_FLD_CLY_PRD_TITLE', 5009 );
	/// Description courte du produit utilisée pour le référencement
	define( '_FLD_CLY_PRD_DESC', 5010 );
	/// Description longue du produit utilisée pour le référencement
	define( '_FLD_CLY_PRD_DESC_LONG', 5011 );

	/// @}

	/** \defgroup field_constants_store Champs avancés - Magasin
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du magasin
	define( '_FLD_STR_NAME', 520 );
	/// Description du magasin
	define( '_FLD_STR_DESC', 535 );
	/// Ville du magasin
	define( '_FLD_STR_CITY', 521 );
	/// Url du magasin
	define( '_FLD_STR_URL', 621 );
	/// Contenu de la balise title du magasin
	define( '_FLD_STR_TAG_TITLE', 761 );
	/// Contenu de la balise meta-description du magasin
	define( '_FLD_STR_TAG_DESC', 762 );
	/// Contenu de la balise meta-keywords du magasin
	define( '_FLD_STR_KEYWORDS', 763 );
	/// Pays du magasin
	define( '_FLD_STR_COUNTRY', 1731 );
	/// Code postal du magasin
	define( '_FLD_STR_ZIPCODE', 1732 );
	/// Identifiant du compte client rattaché au magasin
	define( '_FLD_STR_USR_ID', 4294 );

	/// @}

	/** \defgroup field_constants_faq Champs avancés - Foire aux questions
	 *	\ingroup model_constants
	 *	@{
	 */
	/// nom de la catégorie
	define( '_FLD_FAQ_CAT_NAME', 526 );
	/// description de la catégorie
	define( '_FLD_FAQ_CAT_DESC', 538 );
	/// url de la catégorie
	define( '_FLD_FAQ_CAT_URL', 614 );
	/// nom de la question
	define( '_FLD_FAQ_QST_NAME', 529 );
	/// description de la question (réponse)
	define( '_FLD_FAQ_QST_DESC', 539 );
	/// url de la question (réponse)
	define( '_FLD_FAQ_QST_URL', 615 );

	/// @}

	/** \defgroup field_constants_cms Champs avancés - Page de contenu
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Identifiant du cms parent
	define( '_FLD_CMS_PARENT_ID', 523 );
	/// Nom du cms
	define( '_FLD_CMS_NAME', 524 );
	/// Description courte du cms (introduction)
	define( '_FLD_CMS_SHORT_DESC', 540 );
	/// Description du cms (contenu de la page)
	define( '_FLD_CMS_DESC', 541 );
	/// Url du cms (contenu de la page)
	define( '_FLD_CMS_URL', 616 );
	/// Url personnalisée du cms
	define( '_FLD_CMS_URL_PERSO', 2782 );
	/// Balise title du cms
	define( '_FLD_CMS_TAG_TITLE', 757 );
	/// Balise meta-description du cms
	define( '_FLD_CMS_TAG_DESC', 758 );
	/// Balise meta-keywords du cms
	define( '_FLD_CMS_TAG_KEYWORDS', 759 );

	/// @}

	/** \defgroup field_constants_doc_type Champs avancés - Type de documents
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du type
	define( '_FLD_DOC_TYPE_NAME', 525 );
	/// Description du type
	define( '_FLD_DOC_TYPE_DESC', 542 );
	/// Url du type
	define( '_FLD_DOC_TYPE_URL', 617 );

	/// @}

	/** \defgroup field_constants_doc Champs avancés - Document
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du document
	define( '_FLD_DOC_NAME', 543 );
	/// Description du document
	define( '_FLD_DOC_DESC', 544 );
	/// Poids total du document
	define( '_FLD_DOC_SIZE', 1748 );
	/// Nom d'origine du document
	define( '_FLD_DOC_FILENAME', 1749 );

	/// @}

	/** \defgroup field_constants_cat_news Champs avancés - Catégorie d'actualités
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la catégorie
	define( '_FLD_NEWS_CAT_NAME', 528 );
	/// Description de la catégorie
	define( '_FLD_NEWS_CAT_DESC', 599 );
	/// Url de la catégorie
	define( '_FLD_NEWS_CAT_URL', 618 );

	/// @}

	/** \defgroup field_constants_news Champs avancés - Actualité
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de l'actualité
	define( '_FLD_NEWS_NAME', 527 );
	/// Introduction de l'actualité
	define( '_FLD_NEWS_INTRO', 536 );
	/// Description de l'actualité
	define( '_FLD_NEWS_DESC', 537 );
	/// Url de l'actualité
	define( '_FLD_NEWS_URL', 619 );

	/// @}

	/** \defgroup field_constants_gsr Champs avancés - Glossaire
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du glossaire
	define( '_FLD_GSR_NAME', 545 );
	/// Nom pluriel du glossaire
	define( '_FLD_GSR_NAME_PL', 546 );
	/// Description du glossaire (définition)
	define( '_FLD_GSR_DESC', 547 );

	/// @}

	/** \defgroup field_constants_bnr Champs avancés - Bannière
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la bannière
	define( '_FLD_BNR_NAME', 548 );
	/// Nom alternative de la bannière
	define( '_FLD_BNR_NAME_ALT', 549 );
	/// Url de destination de la bannière
	define( '_FLD_BNR_URL', 673 );
	/// Identifiant d'un produit en destination de la bannière
	define( '_FLD_BNR_PRD', 1688 );

	/// @}

	/** \defgroup field_constants_dlv_sct Champs avancés - Secteur géographique
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du secteur
	define( '_FLD_SCT_NAME', 519 );

	/// @}

	/** \defgroup field_constants_cgv Champs avancés - Conditions de ventes
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de l'article
	define( '_FLD_CGV_ART_NAME', 550 );
	/// Description de l'article
	define( '_FLD_CGV_ART_DESC', 551 );

	/// @}

	/** \defgroup field_constants_erratum Champs avancés - Erratum
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Description d'une erreur catalogue ou sur le site (cat_erratums->err_desc)
	define( '_FLD_ERR_DESC', 600 );

	/// @}

	/** \defgroup field_constants_line_bl_pl_inv Champs avancés - Ligne de BL, PL et de facture
	 *	\ingroup model_constants
	 *	@{
	 */
	/// DEPRECATED : Poids net total en grammes de la ligne de facture remplacé par une colonne dans la table prd_weight
	define( '_FLD_INV_LINE_WEIGHT', 662 );
	/// Poids net total en grammes de la ligne de BL
	define( '_FLD_BL_LINE_WEIGHT', 663 );
	/// Poids net total en grammes de la ligne de PL
	define( '_FLD_PL_LINE_WEIGHT', 664 );
	/// Poids net total en grammes de la ligne de commande
	define( '_FLD_ORD_LINE_WEIGHT', 665 );
	/// remise en % appliqué sur la ligne de la commande ( négo )
	define( '_FLD_ORD_LINE_DISCOUNT', 3257 );
	/// remise en % appliqué sur la ligne d'un bon de livraison ( négo )
	define( '_FLD_BL_LINE_DISCOUNT', 5042 );
	/// DEPRECATED : Numéro de ligne (pour le tri) remplacé par une colonne dans la table prd_pos
	define( '_FLD_INV_ORDER_NO', 737 );
	/// prix brut unitaire HT de la ligne (PL)
	define( '_FLD_PRD_PL_PRICE_BRUT', 1590 );
	/// prix brut unitaire HT de la ligne (BL)
	define( '_FLD_PRD_BL_PRICE_BRUT', 1591 );
	/// DEPRECATED : prix brut unitaire HT de la ligne (facture) remplacé par une colonne dans la table prd_price_brut_ht
	define( '_FLD_PRD_INV_PRICE_BRUT', 1592 );
	/// identifiant du conditionnement (PL)
	define( '_FLD_PRD_COL_PL_PRODUCT', 3016);
	/// identifiant du conditionnement (BL)
	define( '_FLD_PRD_COL_BL_PRODUCT', 3017);
	/// identifiant du conditionnement (FA)
	define( '_FLD_PRD_COL_INV_PRODUCT', 3018);
	/// Carte cadeau envoyée
	define( '_FLD_PRD_INV_GIFT_SEND', 5023 );
	/// Destinataire d'une carte cadeau
	define( '_FLD_PRD_INV_GIFT_DEST', 5024 );
	/// Numéro(s) de carte(s) cadeau(x) lié(s) à une ligne de facture
	define( '_FLD_PRD_INV_CODE_GIFT', 5025 );

	/// @}
	///
	///

	/** \defgroup field_constants_bl Champs avancés - Bon de livraison
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Code promotion appliqué sur le BL
	define( 'FLD_PMT_BL', 793 );

	/// @}


	/** \defgroup field_constants_fdp Champs avancés - Frais de port produit
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Frais de port n°1
	define( 'FLD_PRODUCT_FDP_1', 5032 );
	/// Frais de port n°2
	define( 'FLD_PRODUCT_FDP_2', 5033 );
	/// Frais de port n°3
	define( 'FLD_PRODUCT_FDP_3', 5094 );
	/// Frais de port n°4
	define( 'FLD_PRODUCT_FDP_4', 5095 );
	/// Frais de port n°5
	define( 'FLD_PRODUCT_FDP_5', 5096 );
	/// Frais de port n°6
	define( 'FLD_PRODUCT_FDP_6', 5097 );
	/// Frais de port n°7
	define( 'FLD_PRODUCT_FDP_7', 5098 );
	/// Frais de port n°8
	define( 'FLD_PRODUCT_FDP_8', 5099 );
	/// Frais de port n°9
	define( 'FLD_PRODUCT_FDP_9', 5100 );

	/// @}


	/** \defgroup field_constants_srv Champs avancés - Service de livraison
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du service de livraison
	define( '_FLD_SRV_NAME', 2185 );
	/// Description du service
	define( '_FLD_SRV_DESC', 3652 );
	/// URL du service
	define( '_FLD_SRV_URL_SITE', 3653 );
	/// URL de suivi du service
	define( '_FLD_SRV_URL_COLIS', 3654 );
	/// Activation du service
	define( '_FLD_SRV_ACTIVE', 3655 );
	/// Message du service dans la notification
	define( '_FLD_SRV_ALERT_MSG', 3656 );
	/// Prix HT du service
	define( '_FLD_SRV_PRC_HT', 3657 );
	/// Prix HT revendeur du service
	define( '_FLD_SRV_DEALER_PRC_HT', 3658 );
	/// Franco de port du service
	define( '_FLD_SRV_FRANCO', 3659 );
	/// Poids minimal du service
	define( '_FLD_SRV_WEIGHT_MIN', 3660 );
	/// Poids maximal du service
	define( '_FLD_SRV_WEIGHT_MAX', 3661 );
	/// Commande minimal du service
	define( '_FLD_SRV_ORD_MIN', 3662 );
	/// Commande maximal du service
	define( '_FLD_SRV_ORD_MAX', 3663 );
	/// Consigne de livraison du service
	define( '_FLD_SRV_CONSIGN', 3664 );

	/// @}

	/** \defgroup field_constants_pay_type Champs avancés - Moyen de paiement
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du moyen de paiement
	define( '_FLD_PAY_TYPE_NAME', 2186 );
	/// Identifiant du moyen de paiement d'un client
	define( '_FLD_USR_PAY_ID', 5000 );


	/// @}

	/** \defgroup field_constants_profile Champs avancés - Profil / Droits d'accès
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom du droit d'accès (gu_profiles->prf_name)
	define( '_FLD_PRF_NAME', 2188 );
	/// Nom du droit d'accès au pluriel (gu_profiles->prf_pl_name)
	define( '_FLD_PRF_NAME_PL', 2189 );
	/// Description du profil (gu_profiles->prf_desc)
	define( '_FLD_PRF_DESC', 2190 );

	/// @}

	/** \defgroup field_constants_bl_line Champs avancés - Ligne de bon de livraison
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Numéro de colis envoyé aux places de marché
	define( '_FLD_BL_PRD_MKT', 2309 );

	/// @}

	/** \defgroup field_constants_promo Champs avancés - Code promotion
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Identifiant de la commande rattaché à un code promotion
	define( '_FLD_PMT_ORD', 2437 );
	/// Date de fin d'un code promotion
	define( '_FLD_OFF_DATE_STOP', 3920);
	/// Identifiant du code promotion ratacher a la promotion
	define( '_FLD_OFF_COD_ID', 4079);
	/// Date de dernière envoie au flux google shopping promotion
	define( '_FLD_PMT_LAST_GOOGLE_SHOPPING_EXPORT', 5012);

	/// @}

	/** \defgroup field_constants_wishlist Champs avancés - Wishlist
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la wishlist
	define( '_FLD_WISHLIST_NAME', 2604 );

	/// @}

	/** \defgroup field_constants_invoice Champs avancés - Facture
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Facture comptabilisée oui / non
	define( '_FLD_INV_ACCOUNTED', 2827 );

	/// @}

	/** \defgroup field_constants_msg Champs avancés - Message
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Identifiant de commande rattachée à un message
	define( '_FLD_MSG_ORDER', 2838 );
	/// Identifiant de ligne de commande rattaché à un message
	define( '_FLD_MSG_ORD_PRODUCTS', 3007 );

	/// @}

	/** \defgroup field_constants_media Champs avancés - Média
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom de la playlist
	define( '_FLD_PLS_NAME', 3044 );
	/// Nom du média
	define( '_FLD_MEDIA_NAME', 3045 );

	/// @}

	/** \defgroup field_constants_image_objects Champs avancés - Objects liés aux images
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Texte alternatif
	define( '_FLD_IMG_OBJ_ALT', 5020 );

	/// @}

	/** \defgroup field_constants_price Champs avancés - Tarif
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Tarifs ayant fait l'object d'une notification de baisse de prix
	define( '_FLD_PRC_DROP_NTF', 3640 );

	/// @}

	/** \defgroup field_constants_nomv Champs avancés - Nomenclature
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Nom d'une option de nomenclature (prd_options->opt_name)
	define( '_FLD_NMT_OPT_NAME', 3729 );

	/// @}

	/** \defgroup field_constants_ctr Champs avancés - Comparateur de prix / Place de marché
	 *	\ingroup model_constants
	 *	@{
	 */
	/// Identifiant de l'import réalisé
	define( '_FLD_CTR_IMP_ID', 4296);
	/// Identifiant du champ qui détermine si un produit existe sur la place de marché ou pas
	define( '_FLD_CTR_PRODUCT_EXIST', 5013);
	/// @}

	// Nombre de lignes maximum d'un fichier pour être importé immédiatement
	define( 'IMPORT_MAX_LINES', 1000 );
	// L'exécution a-t-elle lieu dans le cadre d'une requête Ajax ?
	define( 'IS_AJAX', isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] == "XMLHttpRequest" );
	define( 'IMPORT_ROOT_DIR', __DIR__ . '/../../sites/riashop/htdocs/tools/imports/files/' );

	// Statuts de commande
	// Lors de l'ajout d'un statut, mettre à jour les fonctions d'énumération du fichiers ord.states.inc.php
	/**	\defgroup model_constants_ord_states Status du commande
	 * 	\ingroup model_constants oms
	 *	@{
	 */

	/// Panier (éditable)
	define( '_STATE_BASKET', 1 );
	/// Panier abandonné (non éditable). Statut obsolète
	define( '_STATE_NO_FINISH', 2 );
	/// Commande en attente de paiement
	define( '_STATE_WAIT_PAY', 3 );
	/// Commande à traiter (paiement confirmé)
	define( '_STATE_PAY_CONFIRM', 4 );
	/// Commande en cours de traitement
	define( '_STATE_IN_PROCESS', 5 );
	/// Commande prête pour l'expédition (associée généralement à un bon de livraison)
	define( '_STATE_BL_READY', 6 );
	/// Commande expédiée (associée généralement à un bon de livraison)
	define( '_STATE_BL_EXP', 7 );
	/// Commande facturée (associée généralement à une facture)
	define( '_STATE_INVOICE', 8 );
	/// Commande annulée par le client
	define( '_STATE_CANCEL_USER', 9 );
	/// Commande annulée par le marchand (sert aussi de suppression virtuelle associé à ord_masked)
	define( '_STATE_CANCEL_MERCHAND', 10 );
	/// Commande en cours de préparation (associée généralement à un bon de préparation)
	define( '_STATE_PREPARATION', 11 );
	/// Archive. Statut obsolète depuis l'utilisation de "ord_date_archived"
	define( '_STATE_ARCHIVE', 12 );
	/// Panier supprimé / abandonné (non éditable). Statut obsolète
	define( '_STATE_BASKET_CANCEL', 13 );
	/// Panier sauvegardé (éditable)
	define( '_STATE_BASKET_SAVE', 14 );
	/// Commande fournisseur à l'étude
	define( '_STATE_SUPP_STUDY', 15 );
	/// Commande fournisseur en attente de livraison
	define( '_STATE_SUPP_WAIT_LIVR', 16 );
	/// Commande fournisseur livrée
	define( '_STATE_SUPP_LIVR', 17 );
	/// Commande fournisseur partiellement livrée
	define( '_STATE_SUPP_PARTIEL_LIVR', 18 );
	/// Commande fournisseur partiellement facturée
	define( '_STATE_SUPP_PARTIEL_INV', 19 );
	/// Commande fournisseur en attente de confirmation
	define( '_STATE_SUPP_WAIT_CONFIRM', 20 );
	// Commande fournisseur annulée
	define( '_STATE_SUPP_CANCEL', 43 );
	/// Panier en attente de validation par un administrateur (éditable)
	define( '_STATE_WAIT_VALIDATION', 21 );
	/// Panier réfusé par l'administrateur (n'est plus éditable)
	define( '_STATE_REFUSED', 22 );
	/// Commande "négative" associée à un retour
	define( '_STATE_RETURN', 23 );
	/// Commande expédiée en magasin
	define( '_STATE_BL_STORE', 24 );
	/// Commande dont le paiement a été reçu (généralement un chèque), et est en cours de validation
	define( '_STATE_PAY_WAIT_CONFIRM', 25 );
	/// Modèle de panier
	define( '_STATE_MODEL', 26 );
	/// Commande réceptionnée au magasin
	define( '_STATE_INV_STORE', 27 );
	/// Devis (panier non éditable depuis un site, mais éditable depuis les tablettes FDV ou le backoffice). Peut être synchronisé avec Sage.
	define( '_STATE_DEVIS', 28 );
	/// Projet en attente de validation
	define( '_STATE_PRJ_WAIT_VALIDATION', 29 );
	/// Projet refusé
	define( '_STATE_PRJ_REFUSED', 30 );
	/// Click'n'collect
	define( '_STATE_CLICK_N_COLLECT', 31);
	/// Commande partiellement expédié
	define( '_STATE_BL_PARTIEL_EXP', 32);
	/// Panier en cours de règlement CB
	define( '_STATE_BASKET_PAY_CB', 33 );
	/// Intervention en cours
	define( '_STATE_INTERVENTION_DEVIS', 35 );
	// Intervention confirmé
	define( '_STATE_INTERVENTIONS_CONFIRM', 36 );
	/// Commande validée
	define( '_STATE_VALIDATE', 37 );
	/// Intervention en préparée
	define( '_STATE_INTERVENTION_PREP', 38 );
	// Intervention expédiée
	define( '_STATE_INTERVENTION_EXP', 39 );
	// Intervention annulée
	define( '_STATE_INTERVENTION_CANCEL', 40 );
	// BL en cours
	define( '_STATE_BL_PROGRESS', 41 );
	// Modification en attente de validation du client
	define( '_STATE_ORD_WAIT_CONFIRM', 42 );

	/// @}

	// Partenaire SMS
	/// Partenaire sms ovh
	define('_SMS_PARTNER_OVH', 1);

	// Type d'emplacement de bannière
	/// Bannière
	define('_ADV_PLC_BANNERS', 1 );
	/// Zone d'action (click, hover...)
	define('_ADV_PLC_ACTION_ZONE', 2 );
	/// Visionneuse
	define('_ADV_PLC_VISIO', 3);

	// Type de promotion
	/**	\defgroup model_constants_promo Type de promotion
	 * 	\ingroup model_constants
	 *	@{
	 */
	/// Codes promotions
	define( '_PMT_TYPE_CODE', 1 );
	/// Bons d'achats
	define( '_PMT_TYPE_BA', 2 );
	/// Produits offerts
	define( '_PMT_TYPE_PRD', 3 );
	/// Réductions dégressives
	define( '_PMT_TYPE_REDUC', 4 );
	/// X acheté(s) égal(s) Y offert(s)
	define( '_PMT_TYPE_BUY_X_FREE_Y', 5 );
	/// Code promotion utilisé pour le parrainage
	define( '_PMT_TYPE_REWARD', 6 );
	/// Chéquiers cadeaux
	define( '_PMT_TYPE_CHEEKBOOK', 7 );
	/// Cartes cadeaux
	define( '_PMT_TYPE_GIFTS', 8 );
	/// Remises automatiques
	define( '_PMT_TYPE_REMISE', 9 );
	/// Soldes
	define( '_PMT_TYPE_SOLDES', 10 );
	/// AVOIR
	define( '_PMT_TYPE_CREDIT', 11 );
	// Condition de promotion
	/// Montant HT de la commande
	define( '_PMT_CDT_ORD_HT', 1 );
	/// Montant TTC de la commande
	define( '_PMT_CDT_ORD_TTC', 2 );
	/// Nombre de ligne de commande
	define( '_PMT_CDT_ORD_NB_LINE', 3 );
	/// Nombre total de produit dans la commande
	define( '_PMT_CDT_ORD_NB_PRD', 4 );
	/// Poids total de la commande
	define( '_PMT_CDT_ORD_WEIGHT', 5 );
	/// Service de livraison utilisé
	define( '_PMT_CDT_ORD_SRV', 6 );
	/// Moyen de paiement utilisé
	define( '_PMT_CDT_ORD_PAY', 7 );
	/// Quantité commandé de la ligne
	define( '_PMT_CDT_PRD_QTE', 9 );
	/// Montant total en HT de la ligne
	define( '_PMT_CDT_PRD_HT', 10 );
	/// Montant total en TTC de la ligne
	define( '_PMT_CDT_PRD_TTC', 11 );
	/// Prix de vente HT du produit
	define( '_PMT_CDT_PRD_PRICE_HT', 12 );
	/// Prix de vente TTC du produit
	define( '_PMT_CDT_PRD_PRICE_TTC', 13 );
	/// Montant HT de la commande (Public, hors remise client)
	define( '_PMT_CDT_ORD_HT_PUBLIC', 14 );

	// Conditions spécials des promotion
	/// Condition pointant vers les services de livraison
	define( '_PMT_CDT_SRV', 6 );
	/// Condition pointant vers les moyens de paiement
	define( '_PMT_CDT_PAY_TYPE', 7 );

	/// Condition pointant vers les conditionnements de produit
	define( '_PMT_CDT_PRD_COL_TYPE', 15 );
	/// @}

	// Système de conversion des points de fidélité
	/**	\defgroup model_constants_rewards Programme de fidélité
	 * 	\ingroup model_constants
	 *	@{
	 */
	/// Système de conversion par Points/Euro
	define( '_RWD_SYSTEM_POINTS', 1 );
	/// Système de conversion par Code promotion
	define( '_RWD_SYSTEM_CODE', 2 );
	/// Système Produit Offert
	define( '_RWD_SYSTEM_PRODUCTS', 3 );
	/// Système par remise automatique (dès qu'un certains nombre de points sont atteints)
	define( '_RWD_SYSTEM_REMISE', 4 );

	/// Système de parrainage par point
	define( '_RWD_SP_SYS_POINTS', 1 );
	/// Système de parrainage par code promo
	define( '_RWD_SP_SYS_POURCENT', 2 );
	/// @}

	// Liste des gestions commerciales
	/**	\ingroup model_gescom
	 *	@{
	 */
	/// SAGE
	define('GESCOM_TYPE_SAGE', 0);
	/// ISIALIS
	define('GESCOM_TYPE_ISIALIS', 1);
	/// LIMS
	define('GESCOM_TYPE_LIMS', 2);
	/// HARMONYS
	define('GESCOM_TYPE_HARMONYS', 3);
	/// PLATON
	define('GESCOM_TYPE_PLATON', 4);
	/// APINEGOCE
	define('GESCOM_TYPE_APINEGOCE', 5);
	/// G5
	define('GESCOM_TYPE_G5', 6);
	/// DIVALTO
	define('GESCOM_TYPE_DIVALTO', 7);
	/// CLISSON
	define('GESCOM_TYPE_CLISSON', 8);
	/// EEE
	define('GESCOM_TYPE_EEE', 9);
	/// DYNAMICS
	define('GESCOM_TYPE_DYNAMICS', 10);
	/// SOFI
	define('GESCOM_TYPE_SOFI', 11);
	/// WAVESOFT
	define('GESCOM_TYPE_WAVESOFT', 12);
	/// CEGID
	define('GESCOM_TYPE_CEGID', 13);
	/// SAGE_X3
	define('GESCOM_TYPE_SAGE_X3', 14);
	/// DYNAMICS_NAVISION
	define('GESCOM_TYPE_DYNAMICS_NAVISION', 15);
	/// SINERES
	define('GESCOM_TYPE_SINERES', 16);
	/// DIVALTO_SQL
	define('GESCOM_TYPE_DIVALTO_SQL', 17);
	/// BATIGEST
	define('GESCOM_TYPE_BATIGEST', 18);
	/// Star6000 (Staris)
	define('GESCOM_TYPE_STAR6000', 19);
	/// EBP
	define('GESCOM_TYPE_EBP', 20);
	/// Sage 50c
	define('GESCOM_TYPE_SAGE_50C', 21);
	/// Quadratus
	define('GESCOM_TYPE_QUADRATUS', 22);

	// Liste des taches de synchronisation disponible
	/// Ajout des catégories
	define('TSK_CATEGORIE_ADD', 1);
	/// Ajout des clients
	define('TSK_USER_ADD', 46);
	/// Mise à jour des adresses postales
	define('TSK_ADR_UPD', 52);
	/// Ajout des revendeurs
	define('TSK_RESELLER_ADD', 405);
	/// Ajout des représentants
	define('TSK_SELLER_ADD', 67);
	/// Ajout des secteurs
	define('TSK_SECTOR_ADD', 500);
	/// Affectaction des commerciaux au client
	define('TSK_USR_SELLER_ADD', 70);
	/// Ajout des relations client
	define('TSK_USR_RELATIONS_ADD', 116);
	/// Mise a jour des relations client
	define('TSK_USR_RELATIONS_DEL', 122);
	/// Ajout des produits
	define('TSK_PRODUCT_ADD', 6);
	/// Ajout des contacts
	define('TSK_CONTACT_ADD', 187);
	/// Ajout des produits liées
	define('TSK_PRD_LINKED_ADD', 308);
	/// Ajout des produits "parent"
	define('TSK_PRD_PARENT_ADD', 128);
	/// Ajout des catégories tarifaires
	define('TSK_PRC_CAT_ADD', 43);
	/// Ajout des tarifs sur catégories tarifaires
	define('TSK_PRD_PRC_ADD', 59);
	/// Ajout des marques
	define('TSK_BRD', 12);
	/// Changement de status des commandes
	define('TSK_ORD_STATE', 217);
	/// Changement de status des demandes
	define('TSK_CASE_STATE', 629);
	/// Ajout des demandes
	define('TSK_CASE', 623 );
	/// Ajout des produits liés aux demandes
	define('TSK_CASE_PRODUCT', 624 );
	/// Envoie des demandes
	define('TSK_CASE_ADD', 625 );
	/// Envoie des produits liés aux demandes
	define('TSK_CASE_PRODUCT_ADD', 626 );
	/// Ajout produit intervention
	define('TSK_PRODUCT_INTERVENTION', 627 );
	/// Envoie des relevés linéaires
	define('TSK_LINEAR_RAISED_ADD', 628 );
	/// Ajout des relevés linéaires
	define('TSK_LINEAR_RAISED', 630 );
	/// Ajout des Collections
	define('TSK_COLLECTION', 631);
	/// Ajout des produits d'une collection
	define('TSK_COLLECTION_PRODUCTS', 632);
	/// Ajout des offres de relevé linéaire
	define('TSK_LINEAR_OFFERS', 633);
	/// Envoi des Collections
	define('TSK_COLLECTION_ADD', 634);
	/// Envoi des produits d'une collection
	define('TSK_COLLECTION_PRODUCTS_ADD', 635);
	/// Envoi des offres de relevé linéaire
	define('TSK_LINEAR_OFFERS_ADD', 636);
	/// Envoi des images à salesforce
	define('TSK_IMAGES_OBJECTS_ADD', 637);
	// Mis à jours des information sur le compte client
	define('TSK_USER_UPD', 638);
	// Envoie des information complémentaire dans saleforce
	define('TSK_OBJ_VAL_ADD', 639);
	/// @}


	// Liste des kpi
	/// Nombre de commande
	define('_STATS_NB_ORDER', 1);
	/// Chiffre d'affaire commandé
	define('_STATS_CA_ORDER', 2);
	/// Chiffre d'affaire facturé
	define('_STATS_CA_INVOICE', 3);
	/// Marge commandée actuelle
	define('_STATS_MARGE_ORDER', 4);
	/// Marge facturée actuelle
	define('_STATS_MARGE_INVOICE', 19);
	/// Nombre de devis éffectué
	define('_STATS_NB_DEVIS', 5);
	/// Nombre d'appels réussie
	define('_STATS_NB_CALLS', 6);
	/// Durée des appels
	define('_STATS_TIME_CALLS', 7);
	/// Nombre de rendez-vous
	define('_STATS_NB_REPORT', 8);
	/// Temps total des rendez-vous
	define('_STATS_TIME_REPORT', 9);
	/// Nombre de produits par commande
	define('_STATS_NB_PRD_ORDER', 10);
	/// Nombre de vente avec une valeur à plus de 3 chiffres
	define('_STATS_BIG_SALE', 11);
	/// Panier moyen
	define('_STATS_AVG_BASKET', 12);
	/// Nouveaux client
	define('_STATS_NB_CUSTOMERS', 13);
	/// Mise à jour de fiche client
	define('_STATS_UPDATED_CUSTOMERS', 14);
	/// Durée entre création de la fiche et premier appel
	define('_STATS_FIRST_CALL', 15);
	/// Création de contact
	define('_STATS_NB_CONTACT', 16);
	/// Mise à jour de contact
	define('_STATS_UPDATED_CONTACT', 17);
	/// Création de prospect
	define('_STATS_NB_PROSPECT', 18);

	// Code de réponses pour l'utilisation d'Amazon MWS
	define('MWS_ASIN_FOUND', 1);
	define('MWS_ASIN_NOT_FOUND', 2);
	define('MWS_PRODUCT_IS_PUBLISHED', 3);
	define('MWS_PRODUCT_IS_NOT_PUBLISHED', 4);
	define('MWS_PRODUCT_ATTRIBUTES_SUCCESS', 5);
	define('MWS_PRODUCT_ATTRIBUTES_FAIL', 6);
	/// Nombre de relevé de linéaire
	define('_STATS_NB_LINEAR_RAISED', 20);
	/// Nombre de points de fidelité
	define('_STATS_NB_REWARDS', 21);

	// Nom des base de donnée CouchDb
	define('_COUCHDB_DB_NAME', 'riashop');
	define('_COUCHDB_CHAT_DB_NAME', 'riashop_chat');
	define('_COUCHDB_DOWNLOAD_DB_NAME', 'riashop_downloads');
	define('_COUCHDB_HIST_PAY_DB_NAME', 'riashop_payments');
	define('_COUCHDB_GOALS_DB_NAME', 'riashop_goals');

	// Etat de la synchronisation
	define('_RIASHOPSYNC_STATE_RUNNING', 'running');
	define('_RIASHOPSYNC_STATE_STOPING', 'stoping');
	define('_RIASHOPSYNC_STATE_STARTING', 'starting');
	define('_RIASHOPSYNC_STATE_STOP', 'stop');
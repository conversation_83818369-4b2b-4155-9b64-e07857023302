<?php
/**
 * Class Instagram
 */
class Instagram {

	private $client_id = '423840135542755'; ///< identifiant correspondant au ClientID pour l'API
	private $client_secret = '300ee1729f26805e0623832877537d82'; ///< identifiant correspondant au ClientSecret pour l'API
	private $redirect_uri; ///< URI de redirection pour l'API

	/**	Constructeur d'une instance d'Instagram
	 * Permet d'initialiser le profil utilisateur ainsi que l'URI de redirection
	 */
	public function __construct(){
		global $config;
		$http_host_ria = $config['backoffice_url'];
		if( isset($config['env_sandbox']) && $config['env_sandbox'] ){
			$this->redirect_uri = 'https://app.olivier-girard.dev.fr/config/instagram/index.php';
		}else{
			$this->redirect_uri = 'https://'.$http_host_ria.'/config/instagram/index.php';
		}
	}

	/** Cette fonction permet de récupérer le ClientID
	 * 	@deprecated Cette fonction n'est plus à utiliser, elle sera supprimée dans une prochaine mise à jour du moteur RiaShop
	 *  @return le ClientID
	 */
	public function getClientID(){
		mail( '<EMAIL>', 'Instagram - backtrace getClientID()', print_r(debug_backtrace(), true) );
		return $this->client_id;
	}

	/** Cette fonction permet de récupérer le ClientSecret
	 * 	@deprecated Cette fonction n'est plus à utiliser, elle sera supprimée dans une prochaine mise à jour du moteur RiaShop
	 *  @return le ClientSecret
	 */
	public function getClientSecret(){
		mail( '<EMAIL>', 'Instagram - backtrace getClientSecret()', print_r(debug_backtrace(), true) );
		return $this->client_secret;
	}

	/** Cette fonction permet de récupérer l'URI de redirection
	 * 	@return l'URI de redirection
	 */
	public function getRedirectUri(){
		return $this->redirect_uri;
	}

	/** Cette fonction permet de configurer le ClientID en fonction du paramètre renseigné
	 * 	@deprecated Cette fonction n'est plus à utiliser, elle sera supprimée dans une prochaine mise à jour du moteur RiaShop
	 * 	@param $clientid - ClientID à configurer
	 */
	public function setClientID($clientid){
		mail( '<EMAIL>', 'Instagram - backtrace setClientID()', print_r(debug_backtrace(), true) );
		$this->client_id = $clientid;
	}

	/** Cette fonction permet de configurer le ClientID en fonction du paramètre renseigné
	 * 	@deprecated Cette fonction n'est plus à utiliser, elle sera supprimée dans une prochaine mise à jour du moteur RiaShop
	 * 	@param $clientsecret - ClientSecret à configurer
	 */
	public function setClientSecret($clientsecret){
		mail( '<EMAIL>', 'Instagram - backtrace setClientSecret()', print_r(debug_backtrace(), true) );
		$this->client_secret = $clientsecret;
	}

	/** Cette fonction permet de récupérer les données Instagram du propriétaire du token
	 * @param int $limit - le nombre d'éléments à récupérer - valeur par défaut : 6
	 * @param bool $cache - true si on se sert du cache pour récupérer les données, false sinon - valeur par défaut : true
	 * @return bool|array
	 * 			- false si la valeur $limit n'est pas numérique ou négative
	 * 			- un tableau vide si le cache a été chargé
	 *			- le tableau des données en cache sinon - si le cache est chargé, on récupère du cache, sinon on récupère via l'API
	 */
	function getUserDataFromToken($limit=6, $cache=true){
		global $config, $memcached;

		//Si la valeur $limit n'est pas numérique ou négative, on renvoie false
		if( !is_numeric($limit) || $limit <= 0 ){
			return false;
		}

		//Si la donnée recherchée est en cache, on la renvoie
		$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':getUserDataFromToken:limit'.$limit;
		if ($cache && ($get = $memcached->get($key_memcached))) {
			return $get == 'none' ? [] : $get;
		}

		$data = $this->getPostInstagram();
		$ar_posts = [];

		$c = 0;
		if( is_array($data) ){
			foreach( $data as $one_post ){
				if( $c >= $limit ){
					break;
				}

				$ar_posts[] = [
					'images' => [$one_post['media_url']],
					'link' => $one_post['permalink'],
					'likes' => 0,
					'comments' => 0
				];

				$c++;
			}
		}else{
			//Si erreur dans la récupération des posts, un log d'erreur est enregistré et transmis
			// Todo : peut-être prévoir un envoi de mail pour prévenir du problème
		}

		//Mise en cache des données
		$memcached->set($key_memcached, (count($ar_posts) ? $ar_posts : 'none'), 60 * 60);
		return $ar_posts;
	}

	/** Cette fonction permet de connaitre la validité du token de l'instance
	 * @return bool true si valide, false sinon
	 */
	public function isTokenValid(){
		$data = $this->getPostInstagram();
		return is_array( $data );
	}

	/** Cette fonction permet de créer l'URL pour générer le code d'authentification pour permettre la récupération du token.
	 * 	@return string L'URL sur laquel redirigé l'utilisateur pour authoriser la connexion à son compte Instagram
	 */
	public function getUrlForCode(){
		$url = 'https://api.instagram.com/oauth/authorize?client_id={app-id}&redirect_uri={redirect-uri}&scope=user_profile,user_media&response_type=code&state=1';

		$url = str_replace( '{app-id}', $this->client_id, $url );
		$url = str_replace( '{redirect-uri}', $this->redirect_uri, $url );

		return $url;
	}

	/** Cette fonction permet de récupérer le token en fonction d'un code spécifique
	 * 	@param string $code Obligatoire, code spécifique, généré par l'API avant l'appel de cette fonction
	 * 	@return string Le token de liasion au compte Instagram
	 */
	public function getAccessTokenFromCode( $code ){
		{ // Excécution d'une requête curl permettant de récuperer un ensemble données contenant :
			// 	- si succès, l'access_token ainsi que des données sur l'utilisateur
			// 	- si échec, un contenu d'erreur
			// Ce token est dit de courte durée (1h)
			$ar_p = [
				'client_id' => $this->client_id,
				'client_secret' => $this->client_secret,
				'code' => $_GET['code'],
				'grant_type' => 'authorization_code',
				'redirect_uri' => $this->redirect_uri
			];

			$params = '';
			foreach( $ar_p as $k=>$p ){
				if( trim($params) != '' ){
					$params .= '&';
				}

				$params .= $k.'='.$p;
			}


			// create curl resource
			$ch = curl_init();

			// set url
			curl_setopt($ch, CURLOPT_URL, 'https://api.instagram.com/oauth/access_token');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

			//return the transfer as a string
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

			// $output contains the output string
			$output = curl_exec($ch);

			$result = json_decode( $output );
			if( !isset($result->access_token) || trim($result->access_token) == '' ){
				throw new Exception( _("Impossible de générer le token Instagram."), 1 );
			}
		}

		{ // Récupère le token de longue durée (60 jours)
			$ar_p = [
				'client_secret' => $this->client_secret,
				'grant_type' => 'ig_exchange_token',
				'access_token' => $result->access_token
			];

			$params = '';
			foreach( $ar_p as $k=>$p ){
				if( trim($params) != '' ){
					$params .= '&';
				}

				$params .= $k.'='.$p;
			}

			// create curl resource
			$ch = curl_init();

			// set url
			curl_setopt($ch, CURLOPT_URL, 'https://graph.instagram.com/access_token?'.$params);

			//return the transfer as a string
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

			// $output contains the output string
			$output = curl_exec($ch);

			$result = json_decode( $output );
			if( !isset($result->access_token) || trim($result->access_token) == '' ){
				throw new Exception( _("Impossible de générer le token Instagram."), 1 );
			}
		}

		// Enregistre le token en configuration
		$r_website = wst_websites_get();
		if( $r_website ){
			while( $website = ria_mysql_fetch_assoc($r_website) ){
				if( !cfg_overrides_set_value('instagram_token', $result->access_token, $website['id']) ){
					throw new Exception( _("Impossible de générer le token Instagram."), 1 );
				}
			}
		}

		return $result->access_token;
	}

	/** Cette fonction permet de récupérer un code unique sur le site, codifié en md5
	 * 	@deprecated Cette fonction n'est plus à utiliser, elle sera supprimée dans une prochaine mise à jour du moteur RiaShop
	 * 	@return le code "instagram[tnt_id][wst_id]", tnt_id et wst_id étant unique au site. Ce code est codifié en md5
	 */
	public function getCodeCallback(){
		global $config;
		mail( '<EMAIL>', 'Instagram - backtrace getCodeCallback()', print_r(debug_backtrace(), true) );
		return md5('instagram'.$config['tnt_id'].$config['wst_id']);
	}

	/** Cette fonction permet la récupération des posts en fonction du token. Ce token est récupéré en base.
	 * 	Le résultat est mis en cache pendant 1 heure dans le cas où la récupération des posts est un succès.
	 * 	@return bool false si la récupération des posts a échouée
	 * 	@return array Un tableau contenant les 25 derniers posts et pour chacun les information suivantes :
	 * 		- id : identifiant unique du post sur Instagram
	 * 		- caption : titre du post
	 * 		- media_url : URL du média partagé
	 * 		- permalink : lien permanent vers le post sur Instagram
	 * 		- date : date de pubication du post au format YYYY-MM-DD HH:MM:SS
	 */
	public function getPostInstagram(){
		global $config, $memcached;

		$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':Instagram->getPostInstagram';

		if( $get = $memcached->get($key_memcached) ){
			return ($get == 'none' ? [] : $get);
		}

		//On teste si en base le token n'est pas renseigné, ou null, on retourne false
		if (!isset($config['instagram_token']) || trim($config['instagram_token']) == '') {
			return false;
		}

		//Requete curl permettant la récupération des données avec l'API Instagram
		{
			$endpoint  = 'https://graph.instagram.com/me/media';
			$endpoint .= '?fields=id,caption,media_url,permalink,timestamp&access_token='.$config['instagram_token'];

			$curl = curl_init($endpoint);

			curl_setopt($curl,CURLOPT_CONNECTTIMEOUT,3);
			curl_setopt($curl,CURLOPT_RETURNTRANSFER,true);
			curl_setopt($curl,CURLOPT_SSL_VERIFYPEER,true);

			$json = curl_exec($curl);
			curl_close($curl);
		}

		$result = json_decode($json);
		if( !isset($result->data) || !count($result->data) ){
			return false;
		}

		$ar_post = [];

		foreach( $result->data as $one_post ){
			$publish = new DateTime( $one_post->timestamp );

			$ar_post[] = [
				'id' => $one_post->id,
				'caption' => $one_post->caption,
				'media_url' => $one_post->media_url,
				'permalink' => $one_post->permalink,
				'date' => $publish->format('Y-m-d H:i:s')
			];
		}

		// Mise en cache du résultat pendant 1 heure
		$memcached->set( $key_memcached, (count($ar_post) ? $ar_post : 'none'), 60 * 60 );
		return $ar_post;
	}
}

<?php
// \cond onlyria

/**	\defgroup model_experts Assistants experts
 *	\ingroup tools
 *	Les assistants experts vont guider l'internaute vers les produits correspondant à son besoin. Ils sont composés de questions qui permettent d'affiner
 *	une sélection de produits.
 *	@{
 */

/**	Cette fonction permet le chargement d'un ou plusieurs noeuds
 *	@param int $id Optionnel, identifiant du noeud sur lequel filtrer le résultat
 *	@param int $parent Optionnel, identifiant d'un noeud parent sur lequel filtrer le résultat (seul les noeuds enfants seront retournés)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du noeud
 *			- name : désignation du noeud
 *			- desc : description du noeud
 */
function exp_nodes_get( $id=0, $parent=0 ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($parent) || $parent<0 ) return false;
	$sql = '
		select nod_id as id, nod_name as name, nod_desc as "desc"
	';
	if( $parent>0 ){
		$sql .= ' from exp_nodes, exp_nodes_hierarchy where nod_tnt_id='.$config['tnt_id'].' and hrc_tnt_id=nod_tnt_id and nod_id=hrc_child_id and hrc_parent_id='.$parent;
	}else{
		$sql .= ' from exp_nodes where nod_tnt_id='.$config['tnt_id'];
	}
	if( $id>0 ){
		$sql .= ' and nod_id='.$id;
	}
	$sql .= ' order by nod_id';

	return ria_mysql_query($sql);
}

/**	Cette fonction permet le chargement d'un ou plusieurs niveaux hiérarchiques
 *	@param int $parent Optionnel, identifiant du noeud parent sur lequel filtrer le résultat
 *  @param int $child Optionnel, identifiant du noeud enfant sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- parent : identifiant du noeud parent
 *			- child : identifiant du noeud enfant
 */
function exp_nodes_hierarchy_get( $parent=0, $child=0 ){
	global $config;

	if( !is_numeric($parent) || $parent<0 ) return false;
	if( !is_numeric($child) || $child<0 ) return false;

	$sql = '
		select hrc_parent_id as parent, hrc_child_id as child
		from exp_nodes_hierarchy
		where hrc_tnt_id='.$config['tnt_id'];
	if( $parent>0 ){
		$sql .= ' and hrc_parent_id='.$parent;
	}
	if( $child>0 ){
		$sql .= ' and hrc_child_id='.$child;
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet le chargement d'une ou plusieurs valeurs
 *	@param int $nod Optionnel, identifiant du noeud sur lequel filtrer le résultat
 *  @param int $var Optionnel, identifiant de la variable sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- var : identifiant de la variable
 *			- nod : identifiant du noeud
 *			- value : valeur
 */
function exp_node_variables_get( $nod=0, $var=0 ){
	global $config;

	if( !is_numeric($nod) || $nod<0 ) return false;
	if( !is_numeric($var) || $var<0 ) return false;
	$sql = '
		select nod_var_id as var, nod_nod_id as nod, nod_var_value as value
		from exp_node_variables where nod_tnt_id='.$config['tnt_id'];
	if( $var>0 ){
		$sql .= ' and nod_var_id='.$var;
	}
	if( $nod>0 ){
		$sql .= ' and nod_nod_id='.$nod;
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet d'ajouter un devis
 *	@param int $ses_ccl_id Obligatoire, identifiant du cycle
 *	@param int $ses_usr_id Obligatoire, identifiant de l'utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function exp_sessions_add( $ses_ccl_id, $ses_usr_id ){
	global $config;

	if( !is_numeric($ses_ccl_id) || $ses_ccl_id<=0 ) return false;
	if( !is_numeric($ses_usr_id) || $ses_usr_id<=0 ) return false;

	return ria_mysql_query('insert into exp_sessions (ses_tnt_id, ses_usr_id, ses_ccl_id, ses_date_created) values ('.$config['tnt_id'].', '.$ses_usr_id.','.$ses_ccl_id.',NOW())');
}

/**	Cette fonction permet de savoir si un devis est présent dans la base de données
 *	@param int $ses_id Obligatoire, identifiant du cycle
 *	@return bool true en cas de succès, false en cas d'échec
 */
function exp_sessions_exist( $ses_id ){
	global $config;

	$sql = 'select * from exp_sessions where ses_tnt_id='.$config['tnt_id'].' and ses_id = '.$ses_id;
	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}

/**	Cette fonction permet de supprimer un devis
 *	@param int $ses_id Obligatoire, identifiant du devis à supprimer
 *  @param int $usr_id Obligatoire, identifiant de l'utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function exp_sessions_del( $ses_id, $usr_id ){
	global $config;

	if( !is_numeric($ses_id) || $ses_id<=0 ) return false;
	if( !is_numeric($usr_id) || $usr_id<=0 ) return false;

	return ria_mysql_query('delete from exp_sessions where ses_tnt_id='.$config['tnt_id'].' and ses_id='.$ses_id.' and ses_usr_id='.$usr_id);
}

/**	Cette fonction permet de recuperer les données d'un cycle
 *	@param $ses_ccl_id Obligatoire, identifiant du cycle
 *	@param $ses_usr_id Obligatoire, identifiant de l'utilisateur
 *	@param $svr_var_id Facultatif, identifiant d'une variable sur laquelle filtrer le résultat
 *	@param $ses_id Facultatif, identifiant du cycle
 *	@return une tableau avec tout les données nécessaires
 */
function exp_session_variables_get( $ses_ccl_id, $ses_usr_id, $svr_var_id=0, $ses_id=0 ){
	global $config;
	if( !is_numeric($ses_ccl_id) || $ses_ccl_id<=0 ) return false;
	if( !is_numeric($ses_usr_id) || $ses_usr_id<=0 ) return false;

	$sql ='select svr_var_id as var, svr_value as value
						from exp_sessions ses, exp_session_variables var
						where ses.ses_tnt_id='.$config['tnt_id'].'
					 	  and var.svr_tnt_id=ses.ses_tnt_id
						  and ses.ses_id = var.svr_ses_id
						  and ses.ses_ccl_id='.$ses_ccl_id.'
						  and ses.ses_usr_id='.$ses_usr_id;
	if( $svr_var_id!=0 ){
		$sql .= ' and var.svr_var_id = '.$svr_var_id;
	}
	if( $ses_id > 0 ){
		$sql .= ' and ses.ses_id = '.$ses_id;
	}
	return ria_mysql_query($sql);
}

/**	Cette fonction permet de récuperer la liste des devis d'un utilisateur
 *	@param $ses_usr_id Obligatoire, identifiant de l'utilisateur
 *	@param $ses_id Facultatif, identifiant de session utilisateur
 *	@return une tableau avec tout les données nécessaires
 */
function exp_sessions_get( $ses_usr_id, $ses_id=0 ){
	global $config;

	$sql ='select *
			from exp_sessions ses, exp_session_variables var
			where ses.ses_tnt_id='.$config['tnt_id'].'
				and var.svr_tnt_id=ses.ses_tnt_id
				and ses.ses_id = var.svr_ses_id
				and var.svr_var_id=13
				and ses.ses_usr_id='.$ses_usr_id;
	if( $ses_id!=0 ){
		$sql .= ' and ses.ses_id='.$ses_id;
	}
	$sql .= ' ORDER BY ses.ses_date_created DESC';

	return ria_mysql_query($sql);//on recup seulement le nom du bateau
}

/**	Cette fonction permet la vérification d'une variable
 *	@param $ses_ccl_id Obligatoire, identifiant du cycle
 * 	@param $ses_usr_id Obligatoire, identifiant de l'utilisateur
 * 	@param $svr_var_id Obligatoire, identifiant de la variable
 * 	@param $svr_value Obligatoire, valeur de la variable
 *	@return bool true si la variable est existante, false dans le cas contraire
 */
function exp_ses_variables_exists( $ses_ccl_id, $ses_usr_id, $svr_var_id, $svr_value=0 ){
	global $config;

	if( !is_numeric($ses_ccl_id) || $ses_ccl_id<=0 ) return false;
	if( !is_numeric($ses_usr_id) || $ses_usr_id<=0 ) return false;
	if( !is_numeric($svr_var_id) || $svr_var_id<=0 ) return false;

	$select = 'select *
		from exp_sessions ses, exp_session_variables var
		where ses.ses_tnt_id='.$config['tnt_id'].'
			and var.svr_tnt_id=ses.ses_tnt_id
			and ses.ses_ccl_id = '.$ses_ccl_id.'
			and ses.ses_usr_id = '.$ses_usr_id.'
			and var.svr_var_id = '.$svr_var_id;
	if($svr_value != 0){
		$select .= ' and var.svr_value = '.$svr_value;
	}
	$select .= ' and var.svr_ses_id = ses.ses_id';

	return ria_mysql_num_rows(ria_mysql_query($select))>0;
}

/**	Cette fonction permet d'ajouter les variables pour un devis enregistré
 *	@param $svr_var_id Obligatoire, identifiant de la variable
 *	@param $svr_ses_id Obligatoire, identifiant de la session
 *	@param $svr_value Obligatoire, valeur de la variable
 *	@return bool true en cas de succès, false en cas d'échec
 */
function exp_ses_variables_add( $svr_ses_id, $svr_var_id, $svr_value ){
	global $config;

	if( !is_numeric($svr_var_id) || $svr_var_id<=0 ) return false;
	if( !is_numeric($svr_ses_id) || $svr_ses_id<=0 ) return false;

	return ria_mysql_query('insert into exp_session_variables (svr_tnt_id,svr_ses_id,svr_var_id, svr_value) values ('.$config['tnt_id'].','.$svr_ses_id.','.$svr_var_id.',\''.$svr_value.'\')');
}

/**	Cette fonction permet d'ajouter des noeuds dans exp_session_path
 *	@param $esp_ses_id Obligatoire, identifiant de la session
 *	@param $esp_pos Obligatoire, position du noeud
 *	@param $esp_parent_node Obligatoire, identifiant du noeud pere
 *	@param $esp_child_node Obligatoire, identifiant du noeud fils
 *	@return bool true en cas de succès, false en cas d'échec
 */
function exp_session_path_add( $esp_ses_id, $esp_pos, $esp_parent_node, $esp_child_node ){
	global $config;

	if( !is_numeric($esp_ses_id) || $esp_ses_id<=0 ) return false;
	if( !is_numeric($esp_pos) || $esp_pos<=0 ) return false;
	if( !is_numeric($esp_parent_node) || $esp_parent_node<=0 ) return false;
	if( !is_numeric($esp_child_node) || $esp_child_node<=0 ) return false;

	return ria_mysql_query('insert into exp_session_path (esp_tnt_id,esp_ses_id, esp_pos,esp_parent_node, esp_child_node) values ('.$config['tnt_id'].','.$esp_ses_id.','.$esp_pos.','.$esp_parent_node.','.$esp_child_node.')');
}

/**	Cette fonction permet de récupérer le parcours
 *	@param $esp_ses_id Obligatoire, identifiant de la session
 *	@return resource un résultat de requête MySQL décrivant le parcours de l'utilisateur durant sa session
 */
function exp_session_path_get( $esp_ses_id ){
	global $config;

	if( !is_numeric($esp_ses_id) || $esp_ses_id<=0 ) return false;
	if( !exp_session_path_exists($esp_ses_id)) return false;

	return ria_mysql_query('select *
						from exp_session_path
						where esp_tnt_id='.$config['tnt_id'].' and esp_ses_id = '.$esp_ses_id.'
						order by esp_pos asc');
}

/** Cette fonction test l'exsitance d'une session
*	@param $esp_ses_id Obligatoire, identifiant de la session
*	@return bool true en cas de succès, false en cas d'échec
*/
function exp_session_path_exists( $esp_ses_id ){
	global $config;
	if( !is_numeric($esp_ses_id) || $esp_ses_id<=0 ) return false;

	$temp = ria_mysql_query('select * from exp_session_path where esp_tnt_id='.$config['tnt_id'].' and esp_ses_id = '.$esp_ses_id);

	return ria_mysql_num_rows($temp)>0;
}

/// @}

// \endcond
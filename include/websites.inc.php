<?php
/// \cond onlyria

require_once('i18n.inc.php');

/** \defgroup tnt_sites Sites Internet
 *	\ingroup model_tenants
 *	Ce module comprend les fonctions nécessaires à la gestion d'un ou plusieurs sites Internet par un locataire
 *	@{
 */

/** Cette fonction permet d'ajouter un site internet à un locataire
 *	@param int $tnt Obligatoire, identifiant du locataire
 *	@param int $type Obligatoire, identifiant du type de site
 *	@param string $name Obligatoire, nom du site
 *	@param string $desc Optionnel, description du site
 *	@param string $ga_key Optionnel, clé google analytics
 *	@param string $gm_key Optionnel, clé google maps du site
 *	@param string $msg_wait Déprécié
 *	@param string $msg_maint Déprécié
 *	@param string $msg_not_found Déprécié
 *	@param string $title Optionnel, contenu de la balise title par défaut
 *	@param string $meta_desc Optionnel, contenu de la balise meta-description par défaut
 *	@param string $meta_kwd Optionnel, contenu de la balise meta-keywords par défaut
 *	@return int|bool l'identifiant du nouveau site en cas de succès sinon false en cas d'échec
 */
function wst_websites_add( $tnt, $type, $name, $desc='', $ga_key='', $gm_key='', $msg_wait='', $msg_maint='', $msg_not_found='', $title='', $meta_desc='', $meta_kwd='' ){
	if( !tnt_tenants_exists($tnt) ) return false;
	if( !wst_website_types_exists($type) ) return false;
	if( !trim($name) ) return false;
	global $config;

	$sql = '
		insert into tnt_websites
			( wst_tnt_id, wst_wty_id, wst_name, wst_desc, wst_ga_key, wst_gm_key, wst_site_title, wst_meta_desc, wst_meta_keywords, wst_date_created )
		values
			( '.$tnt.', '.$type.', \''.addslashes($name).'\', \''.addslashes($desc).'\', \''.addslashes($ga_key).'\', \''.addslashes($gm_key).'\', \''.addslashes($title).'\', \''.addslashes($meta_desc).'\', \''.addslashes($meta_kwd).'\', now() )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet le chargement d'un ou plusieurs site(s).
 *	@param int $id Facultatif, identifiant de site
 *	@param array $sort Facultatif, ordre de tri à appliquer au résultat. Par défaut, les locataires sont triés par nom (ordre alphabétique).
		Il est possible d'indiquer ici la valeur tenant pour obtenir un tri par identifiant de locataire.
 *	@param int $tnt_id Facultatif, identifiant de locataire (null indique le locataire courant, par défaut).
 *	@param bool $active_excluded Optionnel, par défaut à False, mettre true pour exclure les sites contenu dans selector_websites_excluded
 *	@param int $wty_id Facultatif, identifiant ou tableau d'identifiant d'un type de site sur lequel filtrer le résultat
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL, trié par identifiant de site, comprenant les champs suivants :
 *		- tnt_id : Identifiant du locataire
 *		- id : Identifiant du site
 *		- name : Nom du site
 *		- desc : Description du site
 *		- ga_key : Code de tracking Google Analytics
 * 		- gm_key : Clé Google Maps.
 * 		- date-created : Date de création.
 *		- site_title : contenu de la balise title
 *		- meta_desc : contenu de la balise meta description
 *		- meta_kwd : contenu de la balise meta keywords
 *		- type_id : Identifiant du type de site
 *		- type_name : Nom du type de site
 *		- ord-detach : Détermine si le panier doit être détaché
 *		- is_default : Détermine s'il s'agit du site principal du locataire (celui permettant généralement l'accès à l'interface d'administration, et surtout celui chargé par le fichier de synchronisation)
 */
function wst_websites_get( $id=0, $sort=false, $tnt_id=null, $active_excluded=false, $wty_id=false ){
	global $config;

	if( $tnt_id !== null ){
		$tnt_id = control_array_integer( $tnt_id );
		if( $tnt_id === false ){
			return false;
		}
	}

	$wty_id = control_array_integer( $wty_id, false );

	$sql = '
		select wst_tnt_id as tnt_id, wst_id as id, wst_name as name, wst_desc as "desc",
			wst_ga_key as ga_key, wst_gm_key as gm_key,	wst_date_created as "date-created",
			wst_site_title as site_title, wst_meta_desc as meta_desc, wst_meta_keywords as meta_kwd,
			wty_id as type_id, wty_name as type_name, wty_ord_detach as "ord-detach", wst_default as is_default
		from tnt_websites
			join tnt_website_types on wst_wty_id=wty_id
		where wst_date_deleted is null
	';

	if ($tnt_id === null) {
		if( isset($config['tnt_id']) && is_numeric($config['tnt_id']) && $config['tnt_id'] ){
			$sql .=  ' and wst_tnt_id = '.$config['tnt_id'];
		}
	}else{
		$sql .=  ' and wst_tnt_id in ('.implode( ', ', $tnt_id ).')';
	}

	if( is_numeric($id) && $id>0 ){
		$sql .= ' and wst_id='.$id;
	}

	if( is_array($wty_id) && count($wty_id) > 0 ){
		$sql .= ' and wty_id in ('.implode( ', ', $wty_id ).')';
	}

	if( $active_excluded ){
		if( isset($config['selector_websites_excluded']) && is_array($config['selector_websites_excluded']) && sizeof($config['selector_websites_excluded']) ){
			$sql .= ' and wst_id not in ('.implode( ', ', $config['selector_websites_excluded'] ).')';
		}
	}

	if( is_numeric($id) && $id>0 )
		$sql .= ' limit 0,1';
	else{
		// Converti le paramètre de tri en SQL
		$sort_final = array();

		// Récupère un éventuel tri par identifiant de locataire
		if( is_array($sort) && sizeof($sort) ){
			foreach( $sort as $col=>$dir ){
				switch( $col ){
					case 'tenant' :
						array_push ($sort_final, 'wst_tnt_id '.$dir );
						break;
					case 'id' :
						array_push ($sort_final, 'wst_id '.$dir );
						break;
				}
			}
		}

		// Ajoute la clause de tri
		if( sizeof($sort_final)==0 ) $sort_final = array( 'wst_name asc' );

		$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet de récupérer un tableau de tous les identifiants des sites du locataire courant
 *	@param string $type_result Optionnel, type de résultat par défaut un tableau d'identifiants : 'name' retourne un tableau array('id'=>'name')
 *	@return array Un tableau d'identifiants. Un tableau vide en cas d'échec
 */
function wst_websites_get_array( $type_result='id' ){
	global $config, $memcached;

	// On regarde si le résultat de la fonction (en tenant compte du paramètre) existe en cache
	$key_memcached = $config['tnt_id'].':wst_websites_get_array:'.$type_result;
	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'none' ? array() : $get);
	}

	$ar_wst = array();

	// Création d'un tableau d'identifiants ou de noms (selon paramètre) des différents sites d'un tenant
	if( $rwst = wst_websites_get() ){
		while( $w = ria_mysql_fetch_array($rwst) ){
			switch ($type_result) {
				case 'name':
					$ar_wst[ $w['id'] ] = $w['name'];
					break;
				default:
					$ar_wst[] = $w['id'];
					break;
			}
		}
	}

	// Le résultat de la fonction est mise en cache pendant 24h
	$memcached->set($key_memcached, (count($ar_wst) ? $ar_wst : 'none'), 60 * 60 *24, [
		'code' => 'RIASHOP_WEBSITE_GET_INFOS',
		'name' => 'Chargement des informations générales du site'
	]);

	return $ar_wst;
}

/** Cette fonction permet de récupérer le nom d'un site
 *	@param int $wst Obligatoire l'identifiant d'un site
 *	@return bool false en cas d'erreur sinon le nom du site si celui-ci est trouvé
 */
function wst_websites_get_name( $wst ){
	if( !is_numeric($wst) || $wst<=0 ) return false;
	global $config;

	$sql = '
		select wst_name as name
		from tnt_websites
		where wst_tnt_id='.$config['tnt_id'].'
			and wst_id='.$wst.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction permet de récupérer les scripts qui seraient dans l'entête et/ou dans le footer d'un site.
 * 	@param int $wst_id Obligatoire, identifiant d'un site
 * 	@return bool false si le paramètre obligatoire est omis ou faux
 * 	@return resource Un résultat MySql contenant :
 * 		- head : scripts qui seront implémenté juste avant la balise </head>
 * 		- footer : scripts qui seront implémenté juste avant la balise </body>
 */
function wst_websites_get_script( $wst_id ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		select wst_script_head as head, wst_script_footer as footer
		from tnt_websites
		where wst_tnt_id = '.$config['tnt_id'].'
			and wst_id = '.$wst_id.'
	');
}

/** Cette fonction permet de supprimer un site internet (la suppression n'est que virtuel).
 * 	@param int $wst_id Obligatoire, identifiant d'un site internet
 * 	@return bool True si la suppression s'est correctement déroulée, False dans le cas contaire
 */
function wst_websites_del( $wst_id ){
	if (!is_numeric($wst_id) || $wst_id <= 0) {
		return false;
	}

	return ria_mysql_query('
		update tnt_websites
		set wst_date_deleted = now()
		where wst_id = '.$wst_id.'
	');
}

/** Cette fonction permet de récupérer les types de sites
 *	@param int $id Optionnel, identifiant d'un type donnée
 *	@return bool false en cas d'erreur
 *	@return resource Un tableau MySQL contenant :
 *				- id : identifiant du type
 *				- name : nom du type
 */
function wst_website_types_get( $id=0 ){
	if( $id>0 && !wst_website_types_exists($id) ) return false;
	global $config;

	$sql = '
		select wty_id as id, wty_name as name
		from tnt_website_types
	';

	if( $id>0 )
		$sql .= ' and wty_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier l'existance d'un type de site
 *	@param int $id Obligatoire, identifiant d'un type
 *	@return bool true si le type existe false dans le cas contraire
 */
function wst_website_types_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query( 'select 1 from tnt_website_types where wty_id='.$id );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/**	Cette fonction renvoie si les urls du site sont publiques.
 *	@param int $id Identifiant du site à tester.
 *	@return bool true si les urls sont publiques, false sinon. Renvoie null si une erreur se produit.
 */
function wst_websites_url_is_published( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$sql = '
		select wty_public as public
		from tnt_website_types
			join tnt_websites on wty_id = wst_wty_id
		where wst_id='.$id.'
	';

	if( isset($config[ 'tnt_id' ]) )
		$sql .=  ' and wst_tnt_id='.$config[ 'tnt_id' ];
	$sql .=  ' limit 0,1';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return null;


	return ria_mysql_result( $res, 0, 'public' );
}

/**	Cette fonction permet de déterminer si un site existe dans la base de données.
 *	@param int $id Identifiant du site à tester.
 *	@param int $tnt_id Optionnel, identifiant d'un tenant (par défaut : on utilise le $config['tnt_id'] si défini)
 *	@return bool true si le site existe, false s'il n'existe pas.
 */
function wst_websites_exists( $id, $tnt_id=0 ){

	static $prev_id = 0;
	static $prev_tnt_id = 0;
	static $prev_exists = false;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	global $config;

	if( !$tnt_id ){
		if( isset($config['tnt_id']) && $config['tnt_id'] > 0 ){
			$tnt_id = $config['tnt_id'];
		}
	}

	if( $id==$prev_id && $tnt_id==$prev_tnt_id ){
		return $prev_exists;
	}

	$sql = '
		select wst_id from tnt_websites
		where wst_id = '.$id.'
	';

	if ($tnt_id) {
		$sql .= ' and wst_tnt_id = '.$tnt_id;
	}

	$sql .=  ' limit 0,1';

	$res = ria_mysql_query($sql);

	$prev_id = $id;
	$prev_tnt_id = $tnt_id;
	$prev_exists = $res ? ria_mysql_num_rows($res) : false;

	return $prev_exists;

}

/** Cette fonction permet de mettre à jour un site
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param int $type Obligatoire, identifiant du type de site
 *	@param string $name Obligatoire, nom du site
 *	@param string $desc Optionnel, description du site
 *	@param string $ga_key Optionnel, clé de google analytics
 *	@param string $gm_key Optionnel, clé de google maps
 *	@param string $msg_wait Déprécié
 *	@param string $msg_maint Déprécié
 *	@param string $msg_not_found Déprécié
 *	@param string $title Optionnel, contenu de la balise title par défaut
 *	@param string $meta_desc Optionnel, contenu de la balise meta-description par défaut
 *	@param string $meta_kwd Optionnel, contenu de la balise meta-keywords par défaut
 *
 *	@return bool true en cas de succès ou false en cas d'échec
 */
function wst_websites_update( $wst, $type, $name, $desc='', $ga_key='', $gm_key='', $msg_wait='', $msg_maint='', $msg_not_found='', $title='', $meta_desc='', $meta_kwd='' ){
	if( !wst_websites_exists($wst) ) return false;
	if( !wst_website_types_exists($type) ) return false;
	if( !trim($name) ) return false;
	global $config;

	return ria_mysql_query('
		update tnt_websites set
			wst_wty_id='.$type.',
			wst_name=\''.addslashes($name).'\',
			wst_desc=\''.addslashes($desc).'\',
			wst_ga_key=\''.addslashes($ga_key).'\',
			wst_gm_key=\''.addslashes($gm_key).'\',
			wst_site_title=\''.addslashes($title).'\',
			wst_meta_desc=\''.addslashes($meta_desc).'\',
			wst_meta_keywords=\''.addslashes($meta_kwd).'\',
			wst_date_modified=now()
		where wst_id='.$wst.'
	');

}

/** Cette fonction permet de mettre à jour la description d'un site
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $desc Obligatoire, description de ce site
 *	@return bool Retourne true si la mise à jour s'est correctement passée
 *	@return bool Retourne false dans le cas contraire
 */
function wst_websites_update_desc( $wst, $desc ){
	if( !wst_websites_exists($wst) ) return false;
	global $config;

	return ria_mysql_query( 'update tnt_websites set wst_desc=\''.addslashes( $desc ).'\' where wst_tnt_id='.$config['tnt_id'].' and wst_id='.$wst );
}

/** Cette fonction permet de mettre à jour les scripts qui seront ajoutés au site.
 * 	@param int $wst_id Obligatoire, identifiant d'un site
 * 	@param string $head Optionnel, scripts qui seront ajoutés juste avant le </head> (null par défaut donc reste inchangé)
 * 	@param string $footer Optionnel scripts qui seront ajoutés juste avant le </body> (null par défaut donc reste inchangé)
 * 	@return bool true si la mise à jour s'est correctement déroulé, false dans le cas contraire
 */
function wst_websites_update_script( $wst_id, $head=null, $footer=null ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	if( $head === null && $footer === null ){
		return true;
	}

	$sql = '
		update tnt_websites
		set
	';

	if( $head !== null ){
		if( trim($head) == '' ){
			$sql .= ' wst_script_head = null';
		}else{
			$sql .= ' wst_script_head = "'.addslashes( $head ).'"';
		}

		if( $footer !== null ){
			$sql .= ', ';
		}
	}

	if( $footer !== null ){
		if( trim($footer) == '' ){
			$sql .= ' wst_script_footer = null';
		}else{
			$sql .= ' wst_script_footer = "'.addslashes( $footer ).'"';
		}
	}

	$sql .= '
		where wst_tnt_id = '.$config['tnt_id'].'
			and wst_id = '.$wst_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour les balises title, meta-description et meta-keywords d'un site web
 *
 *	@param int $wst Obligatoire, identifiant d'un site web
 *	@param string $title Obligatoire, nouveau titre du site web
 *	@param string $desc Optionnel, nouvelle description du site web
 *	@param string $kwd Optionnel, nouvelle liste de mots clés
 *
 *	@return bool Retourne true si la mise à jour à bien fonctionnée
 *	@return bool Retourne false dans le cas contraire
 *
 */
function wst_websites_meta_update( $wst, $title, $desc='', $kwd='' ){
	if( !wst_websites_exists($wst) ) return false;
	if( !trim($title) ) return false;
	global $config;

	return ria_mysql_query('update tnt_websites set
			wst_site_title = "'.addslashes($title).'",
			wst_meta_desc = "'.addslashes($desc).'",
			wst_meta_keywords = "'.addslashes($kwd).'"
		where wst_tnt_id='.$config['tnt_id'].'
			and wst_id='.$wst);
}

/**	Cette fonction détermine si un locataire possède un site de type "extranet fournisseur", elle peut également tester directement un site.
 *	le résultat de cette fonction est mis en cache pendant 24 heures.
 *	@param int $tnt_id Obligatoire, identifiant du locataire.
 *	@param int $wst_id Optionnel, identifiant du site.
 *	@return bool True si le locataire possède un extranet fournisseur ou si le site donné est un extranet fournisseur, False sinon.
 */
function wst_websites_is_supplier( $tnt_id, $wst_id=0 ){

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}
	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	$mem_key = 'wst-websites-is-supplier:tnt:'.$tnt_id.':wst:'.$wst_id;

	global $memcached;

	if( $cache = $memcached->get( $mem_key ) ){
		return $cache;
	}

	$sql = 'select 1 from tnt_websites where wst_tnt_id = '.$tnt_id.' and wst_wty_id = '._WST_TYPE_SUPPLIER;
	if( $wst_id ){
		$sql .= ' and wst_id = '.$wst_id;
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		error_log('erreur wst_websites_is_supplier : '.mysql_error()."\n".$sql);
		return false;
	}

	$res = ria_mysql_num_rows($res) > 0;

	$memcached->set( $mem_key, $res, 60*60*24, [
		'code' => 'RIASHOP_WEBSITE_GET_IS_SUPPLIER',
		'name' => 'Site "extranet fournisseur" existant',
		'desc' => 'Détermine si un site de type "extranet fournisseur" existe.'
	] );

	return $res;

}

/**	Cette fonction détermine si un locataire possède une application FDV, elle peut également tester directement un site.
 *	le résultat de cette fonction est mis en cache pendant 24 heures.
 *	@param int $tnt_id Obligatoire, identifiant du locataire.
 *	@param int $wst_id Optionnel, identifiant du site.
 *	@return bool True si le locataire possède une application FDV, ou si le site donné est une application FDV, False sinon.
 */
function wst_websites_is_fdv_app( $tnt_id, $wst_id=0 ){

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}
	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	$mem_key = 'wst-websites-is-fdv-app:tnt:'.$tnt_id.':wst:'.$wst_id;

	global $memcached;

	if( $cache = $memcached->get( $mem_key ) ){
		return $cache;
	}

	$sql = '
		select 1
		from tnt_websites
		where wst_tnt_id = '.$tnt_id.'
			and wst_wty_id = '._WST_TYPE_FDV.'
			and wst_date_deleted is null
	';
	if( $wst_id ){
		$sql .= ' and wst_id = '.$wst_id;
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		error_log('erreur wst_websites_is_fdv_app : '.mysql_error()."\n".$sql);
		return false;
	}

	$res = ria_mysql_num_rows($res) > 0;

	$memcached->set( $mem_key, $res, 60*60*24, [
		'code' => 'RIASHOP_WEBSITE_IS_YUTO',
		'name' => 'Application Yuto existante',
		'desc' => 'Détermine si l\'application Yuto est activée.'
	] );

	return $res;

}

/** Cette fonction permet de charger les informations multilingues d'un site
 *	@param int $wst Optionnel, identifiant d'un site
 *	@param string $lng Optionnel, code ISO d'une langue
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *				- wst : identifiant du site internet
 *				- lng_code : code de la langue
 *				- name : désignation de la langue
 *				- is_main :  si la langue est celle par défaut ou non
 *				- url : url du site pour une langue
 *	@return bool false en cas d'erreur
 */
function wst_websites_languages_get( $wst=0, $lng='' ){
	global $config;

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	$sql = '
		select twl_wst_id as wst, twl_lng_code as lng_code, lng_name as name, twl_is_main as is_main, twl_url as url
		from tnt_websites_languages
			join sys_languages on if(lng_cnt_code="", lng_code, concat(lng_code, "-", lng_cnt_code))=twl_lng_code
		where twl_tnt_id='.$config['tnt_id'].'
	';

	if( $wst>0 ){
		$sql .= ' and twl_wst_id='.$wst;
	}

	if( trim($lng)!='' ){
		$sql .= ' and twl_lng_code=\''.strtolower($lng).'\'';
	}

	$sql .= ' order by twl_is_main desc, ifnull(twl_pos, 100) asc';
	return ria_mysql_query( $sql );
}

/**	Cette fonction va compter le nombre de langues correspondantes aux filtres fournis en argument et retourner le résultat
 *	@param int $wst Optionnel, identifiant d'un site
 *	@param string $lng Optionnel, code ISO d'une langue
 *	@return int le nombre de langues correspondant aux fitlres passés en argument
 */
function wst_websites_languages_get_count( $wst=0, $lng='' ){
	global $config;

	// Contrôle les paramètres d'entrée
	if(
		!is_numeric($wst) || $wst<0
	){
		return false;
	}

	$rlang = ria_mysql_query('
		select distinct twl_lng_code as lng_code
		from tnt_websites_languages
		where twl_tnt_id='.$config['tnt_id'].'
			'.( $wst>0 ? ' and twl_wst_id='.$wst : '' ).'
			'.( trim($lng) ? ' and twl_lng_code=\''.addslashes(trim($lng)).'\'' : '' ).'
	');

	if( $rlang===false ){
		return false;
	}

	return ria_mysql_num_rows( $rlang );

}

/** Cette fonction permet de charger l'url du site pour chaque langue sous forme d'un tableau ('lng' => 'url').
 *	Un cache de 24 heures est appliqué.
 *	@param int $wst Optionnel, identifiant d'un site
 *	@return resource Un résultat de requête MySQL contenant les colonnes suivantes :
 *				- lng_code : code de la langue
 *				- url : url du site pour une langue
 *	@return bool false en cas d'erreur
 */
function wst_websites_languages_get_array( $wst ){
	global $config, $memcached;

	if( !is_numeric($wst) || $wst<=0 ){
		return false;
	}

	$key_memcached = $config['tnt_id'].':'.$wst.':wst_websites_languages_get_array';
	$ar_wst_urls = array();

	if ($get = $memcached->get($key_memcached)) {
		return ($get == 'none' ? false : $get);
	}

	$res = ria_mysql_query('
		select twl_lng_code as lng_code, twl_url as url
		from tnt_websites_languages
		where twl_tnt_id='.$config['tnt_id'].'
			and twl_wst_id='.$wst.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		$memcached->set( $key_memcached, 'none', 60 * 60 * 24, [
			'code' => 'RIASHOP_WEBSITE_GET_LANG',
			'name' => 'Chargement des langues',
			'desc' => 'Les différentes langues seront chargées avec les informations liées comme le code de langue ou bien l\'url du site.'
		] );
		return false;
	}

	while ($r = ria_mysql_fetch_assoc($res)) {
		$ar_wst_urls[ $r['lng_code'] ] = $r['url'];
	}

	$memcached->set( $key_memcached, (count($ar_wst_urls) ? $ar_wst_urls : 'none'), 60 * 60 * 24, [
		'code' => 'RIASHOP_WEBSITE_GET_LANG',
		'name' => 'Chargement des langues',
		'desc' => 'Les différentes langues seront chargées avec les informations liées comme le code de langue ou bien l\'url du site.'
	] );
	return $ar_wst_urls;
}

/** Cette fonction permet de retourner une url pour un site dans une langue
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $lng Obligatoire, code ISO d'une langue utilisé par le locataire
 *	@return bool false si l'un des paramètres est omis ou bien faux
 *	@return string L'url du site dans une langue
 */
function wst_websites_languages_get_url( $wst, $lng ){
	if( !is_numeric($wst) || $wst<=0 ) return false;
	if( !i18n_languages_exists($lng) ) return false;
	global $config;

	$sql = '
		select twl_url as url_alias
		from tnt_websites_languages
		where twl_tnt_id='.$config['tnt_id'].'
			and twl_wst_id='.$wst.'
			and twl_lng_code=\''.strtolower($lng).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'url_alias' );
}

/** Cette fonction permet de vérifier si une langue est utilisé sur un site
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $lng Obligatoire, code ISO d'une langue
 *	@return bool true si la langue est utilisée sur le site, false dans le cas contraire
 */
function wst_websites_languages_exists( $wst, $lng ){
	if( !is_numeric($wst) || $wst<=0 ) return false;
	if( !trim($lng) ) return false;
	global $config;

	$sql = '
		select 1
		from tnt_websites_languages
		where twl_tnt_id='.$config['tnt_id'].'
			and twl_wst_id='.$wst.'
			and twl_lng_code=\''.strtolower($lng).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de mettre à jour les balises title, meta-description et meta-keywords d'un site web
 *
 *	@param int $wst Obligatoire, identifiant d'un site web
 *	@param string $lang Obligatoire, code de la langue
 *	@param string $url url du website
 *
 *	@return bool Retourne true si la mise à jour à bien fonctionnée
 *	@return bool Retourne false dans le cas contraire
 *
 */
function wst_websites_languages_url_set( $wst, $lang, $url ){
	if( !wst_websites_exists($wst) ) return false;
	if( !trim($url) || !preg_match('/http[s]?:\/\//', $url) ) return false;
	global $config;

	return ria_mysql_query(
		'update tnt_websites_languages set
			twl_url = "'.addslashes($url).'"
		where twl_tnt_id='.$config['tnt_id'].'
			and twl_lng_code= "'.addslashes($lang).'"
			and twl_wst_id='.$wst
	);
}

/**	Cette fonction récupère le code langue par défaut au niveau locataire (ce qui est différent de la variable de configuration "i18n_lng").
 *	Le résultat de cette fonction est mis en cache pendant une journée.
 *	@param int $tnt_id Optionnel, identifiant du locataire à récupérer. Si non spécifié, celui de la configuration sera utilisé.
 *	@return string Le code langue associé au locataire. Une valeur "fr" est toujours retournée par défaut.
 */
function wst_websites_languages_default( $tnt_id=0 ){

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return 'fr';
	}

	global $memcached;

	$key_memcached = 'wst_websites_languages_default:tnt:'.$tnt_id;

	if( $kid = $memcached->get($key_memcached) ){
		return $kid;
	}

	$sql = '
		select twl_lng_code as "lng"
		from tnt_websites_languages
		where twl_is_main = 1
	';
	if( $tnt_id ){
		$sql .= ' and twl_tnt_id = '.$tnt_id;
	}else{
		global $config;
		$sql .= ' and twl_tnt_id = '.$config['tnt_id'];
	}
	$sql .= '
		order by twl_wst_id
		limit 0, 1
	';

	$res = ria_mysql_query($sql);

	$lng_code = 'fr';
	if( $res && ria_mysql_num_rows($res) ){
		$lng_data = ria_mysql_fetch_assoc($res);
		$lng_code = $lng_data['lng'];
	}

	$memcached->set($key_memcached, $lng_code, 24*60*60, [
		'code' => 'RIASHOP_WEBSITE_GET_LANG_DEFAULT',
		'name' => 'Chargement la langue par défaut',
		'desc' => 'Récupère le code de langue par défaut appliqué sur un site.'
	]);

	return $lng_code;

}

/** Cette fonction permet de récupérer le code du module bancaire.
 * 	@param int $wst_id Obligatoire, identifiant du site web
 * 	@return string Code du module bancaire, vide si aucun module de défini
 */
function wst_websites_get_cb_module( $wst_id ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	$r_module = ria_mysql_query('
		select ifnull(wst_module_cb , "") as module_cb
		from tnt_websites
		where wst_tnt_id = '.$config['tnt_id'].'
			and wst_id = '.$wst_id.'
			and wst_date_deleted is null
	');

	if( !$r_module && !ria_mysql_num_rows($r_module) ){
		return '';
	}

	$module = ria_mysql_fetch_assoc( $r_module );
	return $module['module_cb'];
}

/// @}

/// \endcond

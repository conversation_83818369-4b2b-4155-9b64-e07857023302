<?php
/// \cond onlyria

require_once('db.inc.php');

require_once('users.inc.php');
require_once('ord.installments.inc.php');
require_once('Administrator.inc.php');

/** \defgroup model_tenants Gestion des locataires
 *	Ce module comprend les fonctions nécessaires à la gestion des locataires.
 *	Un locataire est une société utilisatrice de la plateforme, qui s'acquitte d'un abonnement pour y avoir accès.
 *	Toutes les fonctions standards sont présentes (add, get, upd, del, exists).
 *
 *	@{
 */

/** Cette fonction met à jour la date de dernière synchronisation d'un locataire
 *	Pour fonctionner, cette fonction a besoin que la variable $config['tnt_id'] soit définie.
 *
 *	@return bool True si la mise à jour a réussi
 *	@return bool False si la mise à jour a échoué ou si une erreur est survenue
 */
function tnt_tenants_set_last_sync(){
	global $config;

	if( !isset($config['tnt_id']) || !is_numeric($config['tnt_id']) ){
		return false;
	}

	return ria_mysql_query( 'update tnt_tenants set tnt_last_sync=now() where tnt_id='.$config['tnt_id'] );
}

/** Cette fonction détermine si un locataire existe
 *
 *	@param int $tnt Identifiant du locataire
 *
 *	@return bool True si le locataire existe
 *	@return bool False s'il n'existe pas ou en cas d'erreur
 */
function tnt_tenants_exists( $tnt ){
	if( !is_numeric( $tnt ) || $tnt<=0 ) return false;

	$sql = 'select tnt_id from tnt_tenants where tnt_id='.$tnt.' and tnt_date_deleted is null';

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet d'ajouter un locataire
 *	@param string $name Obligatoire, nom du locataire. 45 caractères maximum.
 *	@param string $address1 Obligatoire, numéro et rue du locataire. 45 caractères maximum.
 *	@param string $address2 Optionnel, complément d'adresse. 45 caractères maximum.
 *	@param string $zipcode Obligatoire, code postal du locataire. 6 caractères maximum.
 *	@param string $city Obligatoire, ville du locataire. 45 caractères maximum.
 *	@param string $email Optionnel, adresse mail de contact du locataire. 75 caractères maximum.
 *	@param string $phone Optionnel, numéro de téléphone du locataire. 20 caractères maximum.
 *	@param string $fax Optionnel, numéro de fax du locataire. 20 caractères maximum.
 *	@param bool $use_sync Optionnel, détermine si le locataire est synchronisé avec sa gestion commerciale (active les mises à jour automatique de RiaShopSync)
 *	@param int $tnt_id Facultatif, identifiant de locataire à attribuer à ce compte. Laisser vide pour attribution automatique.
 *	@return int|bool l'identifiant du nouveau locataire en cas de succès ou false en cas d'échec
 */
function tnt_tenants_add( $name, $address1, $address2='', $zipcode, $city, $email='', $phone='', $fax='', $use_sync=true, $tnt_id=0 ){
	if( !trim($name) ) return false;
	if( !trim($address1) ) return false;
	if( !trim($zipcode) ) return false;
	if( !trim($city) ) return false;

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	// tnt_id tnt_name tnt_address1 tnt_address2 tnt_zipcode tnt_city tnt_phone tnt_fax tnt_email tnt_date_created tnt_date_modified tnt_date_deleted tnt_last_sync
	$sql = '
		insert into tnt_tenants
			( '.($tnt_id > 0 ? 'tnt_id, ' : '').'tnt_name, tnt_address1, tnt_address2, tnt_zipcode, tnt_city, tnt_phone, tnt_fax, tnt_email, tnt_date_created, tnt_sync_update )
		values
			( '.($tnt_id > 0 ? $tnt_id.', ' : '').'\''.addslashes($name).'\', \''.addslashes($address1).'\', \''.addslashes($address2).'\', \''.addslashes($zipcode).'\', \''.addslashes($city).'\', \''.addslashes($phone).'\', \''.addslashes($fax).'\', \''.addslashes($email).'\', now(), '.( $use_sync ? '1' : '0' ).' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction récupère les informations disponibles concernant un locataire
 *
 *	@param int $tnt Facultatif, identifiant ou tableau d'identifiant du locataire
 *	@param bool $sync_only Facultatif, ne récupère que les locataires qui ont une synchronisation active
 *	@param bool|array $sort Facultatif, ordre de tri à appliquer, par défaut le résultat est trié par identifiant ascendant. Valeurs acceptées : 'id', 'name' (option de tri acceptée : 'asc', 'desc')
 *	@param null|bool $is_leased Facultatif , si oui ou non le tenant est locataire (par défaut à null)
 *
 *	@return bool False en cas d'erreur ou si le locataire n'existe pas
 *	@return resource un résultat de requête MySQL comprenant les champs suivants :
 *		- id, identifiant du locataire
 *		- name, nom du locataire
 *		- address1, adresse du locataire
 *		- address2, complément d'adresse du locataire
 *		- zipcode, code postal du locataire
 *		- city, ville du locataire
 *		- phone, téléphone du locataire
 *		- fax, fax du locataire
 *		- email, email du locataire
 *		- date-created, date de création du locataire
 *		- date-modified, date de dernière modification du locataire
 *		- last-sync, date de dernière synchronisation du locataire
 *		- token, Clé d'utilisation de RiaShopSync
 *		- wst_default, identifiant du site principal
 *		- sync_update, détermine si le locataire bénéficie des MAJ de RiaShopSync
 *		- sync_version, numéro de version de RiaShopSync actuellement installée
 *		- sync_reboot, détermine si un redémarrage du logiciel de synchronisation a été demandé
 *		- sync_info, message à caractère informatif sur le logiciel de sync
 *		- sync_running, permet de savoir si la synchro est up ou down
 */
function tnt_tenants_get( $tnt=0, $sync_only=false, $sort=false, $is_leased=null ){
	$tnt = control_array_integer( $tnt, false );
	if( $tnt === false ){
		return false;
	}

	$sql = '
		select
			tnt_id as id,
			tnt_name as name,
			tnt_address1 as address1,
			tnt_address2 as address2,
			tnt_zipcode as zipcode,
			tnt_city as city,
			tnt_phone as phone,
			tnt_fax as fax,
			tnt_email as email,
			tnt_date_created as "date-created",
			tnt_date_modified as "date-modified",
			tnt_last_sync as "last-sync",
			md5(concat(tnt_id, tnt_date_created)) as token,
			ifnull((select wst_id from tnt_websites where wst_default=1 and wst_tnt_id=tnt_id), 0) as wst_default,
			tnt_sync_update as sync_update,
			tnt_sync_version as sync_version,
			tnt_sync_reboot as sync_reboot,
			tnt_sync_info as sync_info,
			tnt_sync_running as sync_running
		from tnt_tenants
		where tnt_date_deleted is null
	';

	if( count($tnt) > 0 ){
		$sql .= ' and tnt_id in ('.implode( ', ', $tnt ).')';
	}

	if( $sync_only ){
		$sql .= ' and tnt_sync_update';
	}

	if ($is_leased !== null) {
		if ($is_leased) {
			$sql .= ' and tnt_is_leased = 1';
		}else{
			$sql .= ' and tnt_is_leased = 0';
		}
	}

	$sort_final = array();

	if (is_array($sort)) {
		foreach ($sort as $col => $dir) {
			if (!in_array($dir, array('asc', 'desc'))) {
				$dir = 'asc';
			}

			switch ($col) {
				case 'id':
					array_push($sort_final, 'tnt_id '.$dir);
					break;
				case 'name':
					array_push($sort_final, 'tnt_name '.$dir);
					break;
			}
		}
	}

	if (!count($sort_final)) {
		array_push($sort_final, 'tnt_id asc');
	}

	$sql .= ' order by '.implode(', ', $sort_final);

	return ria_mysql_query( $sql );
}

/**	Cette fonction récupère le nom d'un locataire donné. Ce nom est mis en cache pour une durée de 24h
 *	@param int $id Obligatoire, Identifiant du locataire
 *	@return string Le nom du locataire, chaîne vide s'il n'a pas pu être déterminé
 */
function tnt_tenants_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return '';

	global $memcached;
	if( $cache = $memcached->get( 'tenants-getname:id:'.$id ) ){
		return $cache;
	}

	$r = ria_mysql_query('select tnt_name from tnt_tenants where tnt_id = '.$id.' and tnt_date_deleted is null');

	if( !$r || !ria_mysql_num_rows($r) ){
		$name = '';
	}else{
		$name = ria_mysql_result( $r, 0, 0 );
	}

	$memcached->set( 'tenants-getname:id:'.$id, $name, 86400 );

	return $name;
}

/** Cette fonction permet de récupérer l'identifiant du site par défaut d'un tenant.
 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
 * 	@return int L'identifiant du site par défaut si trouvé, False dans le cas contraire
 */
function tnt_tenants_get_website_default( $tnt_id ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	$sql = '
		select wst_id
		from tnt_websites
		where wst_default=1
			and wst_tnt_id='.$tnt_id.'
	';

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['wst_id'];
}

/** Cette fonction permet la suppression virtuelle d'un locataire
 *
 *	@param $tnt Obligatoire, identifiant du locataire
 *
 *	@return bool False si l'identifiant fourni n'est pas numérique, inférieur à 1 ou si la suppression virtuelle a échoué
 *	@return bool True si la suppression virtuelle a réussi ou si le locataire était déjà supprimé
 */
function tnt_tenant_del( $tnt ){
	if( !is_numeric( $tnt ) || $tnt<=0 ){
		return false;
	}
	if( !tnt_tenants_exists( $tnt ) ){
		return true;
	}

	return ria_mysql_query('
		update tnt_tenants set tnt_date_deleted=now() where tnt_id='.$tnt
	);

}

/** Cette fonction met à jour un locataire, à l'exception de la date de dernière synchronisation
 *
 *	@param int $tnt Oblgatoire, identifiant du locataire
 *	@param string $name Facultatif, nom du locataire (45 caractères maximum). Laisser NULL pour ne pas le modifier.
 *	@param string $address1 Facultatif, adresse du locataire (45 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $address2 Facultatif, complément d'adresse du locataire (45 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $zipcode Facultatif, code postal du locataire (6 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $city Facultatif, ville du locataire (45 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $phone Facultatif, téléphone du locataire (20 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $fax Facultatif, fax du locataire (20 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param string $email Facultatif, email du locataire (75 caractères maximum). Laisser NULL pour ne pas le modifier
 *	@param bool $use_sync Facultatif, détermine si le locataire peut bénéficier des MAJ RiaShopSync
 *
 *	@return bool False en cas d'erreur ou si le locataire n'existe pas
 *	@return bool True en cas de succès ou si tout les paramètres sont NULL
 */
function tnt_tenants_update( $tnt, $name=null, $address1=null, $address2=null, $zipcode=null, $city=null, $phone=null, $fax=null, $email=null, $use_sync=null ){
	if( !tnt_tenants_exists( $tnt ) ) return false;
	if( $name==null && $address1==null && $address2==null && $zipcode==null && $city==null && $phone==null && $fax==null && $email==null && $use_sync==null ) return true;

	$fields = array();

	if( $name!=null ){
		$fields[] = 'tnt_name=\''.addslashes( $name ).'\'';
	}

	if( $address1!=null ){
		$fields[] = 'tnt_address1=\''.addslashes( $address1 ).'\'';
	}

	if( $address2!=null ){
		$fields[] = 'tnt_address2=\''.addslashes( $address2 ).'\'';
	}

	if( $zipcode!=null ){
		$fields[] = 'tnt_zipcode=\''.addslashes( $zipcode ).'\'';
	}

	if( $city!=null ){
		$fields[] = 'tnt_city=\''.addslashes( $city ).'\'';
	}

	if( $phone!=null ){
		$fields[] = 'tnt_phone=\''.addslashes( $phone ).'\'';
	}

	if( $fax!=null ){
		$fields[] = 'tnt_fax=\''.addslashes( $fax ).'\'';
	}

	// ne détecte pas la validité de l'email
	if( $email!=null ){
		$fields[] = 'tnt_email=\''.addslashes( $email ).'\'';
	}

	if( $use_sync!==null ){
		$fields[] = 'tnt_sync_update='.( $use_sync ? '1' : '0' );
	}

	$sql = 'update tnt_tenants set '.implode( ',', $fields ).' where tnt_id='.$tnt;

	return ria_mysql_query( $sql );
}

/** Cette fonction permet définir pour un tenant s'il est sous engagement
 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
 * 	@param $is_leased Optionnel, si oui ou non le tenant sous engagement (par défaut à false)
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function tnt_tenants_set_is_leased( $tnt_id, $is_leased=false ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	return ria_mysql_query('
		update tnt_tenants
		set tnt_is_leased = '.( $is_leased ? '1' : '0').'
		where tnt_id = '.$tnt_id.'
	');
}

/** Cette fonction permet définir pour un tenant si la synchro est allumé ou éteinte
 * 	@param $running Optionnel, si oui ou non la synchro est demarré
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function tnt_tenants_set_sync_running( $state=_RIASHOPSYNC_STATE_RUNNING ){
	if( !in_array($state, array( _RIASHOPSYNC_STATE_RUNNING, _RIASHOPSYNC_STATE_STOPING, _RIASHOPSYNC_STATE_STARTING, _RIASHOPSYNC_STATE_STOP) ) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update tnt_tenants
		set tnt_sync_running = \''.addslashes($state).'\'
		where tnt_id = '.$config['tnt_id'].'
	');
}

/** Cette fonction permet de savoir si la synchro est allumé ou étainte
 * 	@return bool True si la synchronisation tourne, et false dans le cas contraire
 */
function tnt_tenants_get_sync_running(){
	global $config;

	$res = ria_mysql_query('select ifnull(tnt_sync_running,\'\') from tnt_tenants where tnt_id='.$config['tnt_id']);

	if( !$res ){
		return false;
	}

	return ria_mysql_result($res, 0, 0);
}

/**	Cette fonction détermine si une clé d'utilisation est valide, ce qui permet la synchronisation avec la gestion commerciale ou avec les tablettes tactiles.
 *	En l'absence d'un système d'expiration de clé, une clé est valide s'il existe un locataire correspondant à celle-ci.
 *	Le résultat de l'authentification est mis en cache 24 heures.
 *
 *	@param string $logtoken Obligatoire, la clé d'identification de locataire à valider.
 *	@param int $wst_id Facultatif, permet de charger un autre site que celui par défaut.
 *	@param $fdv Facultatif, indique une synchronisation avec les tablettes de l'application Yuto.
 *
 *	@return bool False si la clé est invalide
 *	@return Si la clé est valide et $get_infos à True, un tableau associatif comprenant les clés suivantes :
 *		- tenant : identifiant du locataire
 *		- website : identifiant du site principal (ou du site dédié FDV, ou $wst_id si spécifié)
 *		- sync_update : détermine si les mises à jour sont autorisées pour ce locataire
 *		- error : détermine si une erreur de chargement est survenue ou si le jeton n'est pas valide
 */
function tnt_tenants_is_authorized( $logtoken, $wst_id=0, $fdv=false ){
	global $memcached;

	$wst_id = is_numeric($wst_id) && $wst_id > 0 ? $wst_id : 0;

	$key_memcached = 'tenants-authorized:logtoken:'.$logtoken.':wst:'.$wst_id.( $fdv ? ':fdv:1' : '' );

	if( $in_cache = $memcached->get($key_memcached) ){
		return $in_cache;
	}

	$tab_result = array( 'tenant'=>0, 'website'=>0, 'error'=>true, 'sync_update'=>0 );

	$sql = '
		select
			tnt_id as tenant, (
				select wst_id from tnt_websites
				where wst_tnt_id = tnt_id
	';
	if( $wst_id ){
		$sql .= ' and wst_id = '.$wst_id;
	}elseif( $fdv ){
		$sql .= ' and wst_wty_id = '._WST_TYPE_FDV;
	}else{
		$sql .= ' and wst_default = 1';
	}
	$sql .= '
			) as website, tnt_sync_update as sync_update
		from
			tnt_tenants
		where
			tnt_date_deleted is null
			and (
				exists (
					select 1
					from tnt_tenant_tokens
					where ttt_tnt_id = tnt_id
						and ttt_date_deleted is null
						and ttt_actived = 1
						and ttt_token = "'.addslashes( $logtoken ).'"
				)

				or
	';

	// la clé FDV est limitée à 6 caractères
	if( $fdv ){
		$sql .= ' substring(md5(concat(tnt_id, tnt_date_created)), 1, 6) = "'.addslashes($logtoken).'"';
	}else{
		$sql .= ' md5(concat(tnt_id, tnt_date_created)) = "'.addslashes($logtoken).'"';
	}

	$sql .= '
			)
	';

	$r = ria_mysql_query($sql);

	if( $r && ria_mysql_num_rows($r) ){
		$infos = ria_mysql_fetch_assoc($r);
		if( $infos['website'] ){
			$tab_result['tenant'] = $infos['tenant'];
			$tab_result['website'] = $infos['website'];
			$tab_result['sync_update'] = $infos['sync_update'];
			$tab_result['error'] = false;
		}
	}

	// 24h de mise en cache si succès de l'authentification
	if( !$tab_result['error'] ){
		// 24h en secondes : 60 * 60 * 24
		$memcached->set($key_memcached, $tab_result, 86400);
	}

	return $tab_result;
}

/** Cette fonction permet de supprimer le cache permettant de savoir si la clé API est valide
 *	@param int $tnt_id Obligatoire, Identifiant d'un locataire
 *	@param string $logtoken Obligatoire, clé API dont le cache doit être supprimé
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function tnt_tenants_authorized_delete_cache( $tnt_id, $logtoken ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (trim($logtoken) == '') {
		return false;
	}

	global $memcached;

	$ar_wst_ids = array( 0 );

	// Récupère les websites rattachés à cette clé
	$sql_website = '
		select wst_id
		from tnt_websites
		where wst_tnt_id = '.$tnt_id.'
	';

	$r_website = ria_mysql_query( $sql_website );
	if ($r_website) {
		while ($website = mysql_fetch_assoc($r_website)) {
			$ar_wst_ids[] = $website['wst_id'];
		}
	}

	// Suppression de tous les caches possible en fonction du (des) website(s) et de l'information fdv à 0 ou 1
	foreach ($ar_wst_ids as $wst_id) {
		for ($i=0; $i < 2; $i++) {
			$key_memcached = 'tenants-authorized:logtoken:'.$logtoken.':wst:'.$wst_id.( $i>0 ? ':fdv:1' : '' );

			$get = $memcached->get( $key_memcached );
			if( ria_is_memcached_result_ok($memcached) && !$memcached->delete( $key_memcached ) ){
				return false;
			}
		}
	}

	return true;
}

/** Cette fonction récupère la clé d'utilisation actuelle d'un locataire donné
 *	La prochaine étape consistera à la vérification de la date d'expiration de cette clé ( retournera False si expiré )
 *	@param int $tnt Obligatoire, identifiant du locataire
 *	@return string|bool Clé d'utilisation du locataire, False en cas d'erreur
 */
function tnt_tenants_get_logtoken( $tnt ){
	if( !is_numeric( $tnt ) ) return false;

	$sql = '
		select md5( concat( tnt_id,tnt_date_created ) ) as token
		from tnt_tenants
		where tnt_id='.$tnt
	;

	$token = false;
	if( $rt = ria_mysql_query($sql) ){
		if( $t = ria_mysql_fetch_array( $rt ) ){
			$token = $t['token'];
		}
	}

	return $token;
}

/** Cette fonction permet de récupérer la date de création d'un locataire donné.
 *	@param $tnt Obligatoire, identifiant du locataire
 *	@return La date de création, False dans le cas contraire
 */
function tnt_tenants_get_date_created( $tnt ){
	if( !is_numeric($tnt) || $tnt<=0 ) return false;

	$sql = '
		select tnt_date_created as date_created
		from tnt_tenants
		where tnt_id='.$tnt.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'date_created' );
}

/**	Cette fonction récupère des statistiques diverses sur l'activité d'un locataire (ou un rapport général sur tous les locataires si $id n'est pas précisé)
 *	@param int $id Optionnel, Identifiant de locataire
 *	@param string $date_start Optionnel, date de début de la période sur laquelle on souhaite obtenir des statistiques
 *	@param string $date_end Optionnel, date de fin de la période sur laquelle on souhaite obtenir des statistiques
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- ca-total-ht : chiffre d'affaires HT du locataire sur la période choisie
 *		- ca-total-ttc : chiffre d'affaires TTC du locataire sur la période choisie
 *		- ord-count : nombre de commandes enregistrées sur la période choisie
 *		- ord-count-daily : nombre de commandes enregistrées par jour en moyenne sur la période choisie
 */
function tnt_tenants_get_stats( $id=0, $date_start=false, $date_end=false ){
	if( $id!=0 && !tnt_tenants_exists( $id ) ) return false;
	if( $date_start!=false && !isdate($date_start) ) return false;
	if( $date_end!=false && !isdate($date_end) ) return false;

	$no_date_start_value = false;
	$rtnt = tnt_tenants_get( $id );
	if( $rtnt!=false && ria_mysql_num_rows($rtnt) ){
		if( $tnt = ria_mysql_fetch_array($rtnt) ){
			$no_date_start_value = $tnt['date-created'];
		}
	}
	if( $no_date_start_value==false )
		$no_date_start_value = '2005-01-01 00:00:00';

	$conditions_string = '
		from ord_orders
		where ord_state_id in (3,4,5,6,7,8,11,'._STATE_ARCHIVE.','._STATE_BL_STORE.','._STATE_PAY_WAIT_CONFIRM.','._STATE_INV_STORE.') and
		ord_masked!=1 and
		ord_parent_id is null and
		ord_usr_id in (
			select usr_id
			from gu_users
			where usr_tnt_id=ord_tnt_id
			and usr_date_deleted is null
			and	usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.','.PRF_RESELLER.','.PRF_SELLER.')
		)
	';
	if( $id!=0 )
		$conditions_string .= ' and ord_tnt_id='.$id;
	if( $date_start!=false )
		$conditions_string .= ' and ord_date>=\''.$date_start.'\'';
	if( $date_end!=false )
		$conditions_string .= ' and ord_date<=\''.$date_end.'\'';

	$ord_by_date_string = 'count(*)/abs(datediff(';
	$ord_by_date_string .= ($date_start!=false ? '\''.$date_start.'\'' : '\''.$no_date_start_value.'\'');
	$ord_by_date_string .= ', ';
	$ord_by_date_string .= ($date_end!=false ? '\''.$date_end.'\'' : 'now()');
	$ord_by_date_string .= '))';

	$sql = '
		select
			( select sum(ord_total_ht) '.$conditions_string.' ) as "ca-total-ht",
			( select sum(ord_total_ttc) '.$conditions_string.' ) as "ca-total-ttc",
			count(*) as "ord-count",
			sum(ord_total_ht) as "ca-web-ht",
			sum(ord_total_ttc) as "ca-web-ttc",
			'.$ord_by_date_string.' as "ord-count-daily"
	';
	$sql .= $conditions_string.' and ord_pay_id is not null';

	return ria_mysql_query( $sql );
}

/**	Cette fonction met à jour le numéro de la version de RiaShopSync actuellement installée chez un locataire.
 *	Elle utilise la variable de configuration globale $config['tnt_id'] pour son fonctionnement.
 *	@param $version Obligatoire, Numéro de version (au format 0.0.0.0, ou NULL)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function tnt_tenants_set_sync_version( $version ){
	global $config;

	if( !isset($config['tnt_id']) || !is_numeric($config['tnt_id']) ){
		return false;
	}

	if( $version !== null ){
		$version = trim($version);
		$v_parts = explode( '.', $version );
		if( sizeof($v_parts)!=4 ) return false;
		foreach( $v_parts as $p ){
			if( !is_numeric($p) || $p<0 ) return false;
		}
	}
	if( $version=='0.0.0.0' ) $version = null;

	$sql = 'update tnt_tenants set tnt_sync_version='.( $version===null ? 'NULL' : '"'.addslashes($version).'"' ).' where tnt_id='.$config['tnt_id'];

	return ria_mysql_query( $sql );
}

/**	Cette fonction détermine si un locataire doit redémarrer sa synchronisation. Les valeurs possibles sont les suivantes :
 *		- 0 : aucune action d'arrêt ou de redémarrage en cours
 *		- Un nombre inférieur à 0 : en cours d'arrêt
 *		- Un nombre supérieur à 0 : en cours de redémarrage
 *	@param int $tnt Obligatoire, Identifiant du locataire.
 *	@return int|bool False en cas d'échec ou si aucune action ne doit être effectuée, -1 si un arrêt est requis, 1 ou plus si un redémarrage est requis.
 */
function tnt_tenants_get_sync_reboot( $tnt ){

	if( !is_numeric($tnt) || $tnt<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select tnt_sync_reboot as "reboot"
		from tnt_tenants
		where tnt_id = '.$tnt.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	$data = ria_mysql_fetch_assoc($res);

	return $data['reboot'];

}

/**	Cette fonction met à jour l'information de redémarrage de synchronisation d'un locataire.
 *	@param int $tnt Obligatoire, Identifiant du locataire.
 *	@param int $reboot Un nombre entier indiquant l'action à réaliser :
 *		- 0 pour annuler une action précédente d'arrêt ou de redémarrage.
 *		- Un nombre inférieur à 0 pour arrêter (converti en -1).
 *		- Un nombre supérieur à 0 pour redémarrer. Dans ce cas de figure, le nombre peut indiquer le numéro d'ordre de la configuration à charger dans RiaSHopSync au redémarrage.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function tnt_tenants_set_sync_reboot( $tnt, $reboot ){

	if( !is_numeric($tnt) || $tnt<=0 ){
		return false;
	}
	if( !is_numeric($reboot) ){
		return false;
	}

	if( $reboot < 0 ){
		$reboot = -1;
	}

	return ria_mysql_query('
		update tnt_tenants
		set tnt_sync_reboot = '.$reboot.'
		where tnt_id = '.$tnt.'
	');

}

/**	Cette fonction détermine si le locataire utilise RiaShopSync actuellement
 *	@param $tnt Obligatoire, Identifiant du locataire
 *	@return bool True si le locataire utilise RiaShopSync actuellement, False sinon
 */
function tnt_tenants_get_sync_update( $tnt ){
	if( !is_numeric($tnt) || $tnt<=0 ) return false;

	$r = ria_mysql_query('select tnt_sync_update from tnt_tenants where tnt_id='.$tnt);
	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction permet d'ajouter un token d'accès complémentaire à l'API à un locataire.
 *	@param int $tnt_id Obligatoire, Identifiant du locataire
 *	@param string $token Obligatoire, clé d'accès complémentaire à l'API à créer
 *	@param string $who Obligatoire, Personne / prestataire a qui est attribué ce token. 150 caractères maximum.
 *	@param string $date_start Optionnel, date de début d'accès à l'API
 *	@param string $date_end Optionnel, date de fin d'accès à l'API
 *	@param $sleep Optionnel, temporisation dans l'API
 *	@return int L'identifiant du token, False dans le cas contraire
 */
function tnt_tenant_tokens_add( $tnt_id, $token, $who, $date_start=false, $date_end=false, $sleep=0 ){
	if (!tnt_tenants_exists($tnt_id)) {
		return false;
	}

	if (trim($who) == '') {
		return false;
	}

	if ($date_start !== false) {
		$date_start = dateheureparse( $date_start );

		if (!isdateheure($date_start)) {
			return false;
		}
	}

	if ($date_end !== false) {
		$date_end = dateheureparse( $date_end );

		if (!isdateheure($date_end)) {
			return false;
		}
	}

	if (!is_numeric($sleep) || $sleep < 0) {
		return false;
	}

	$sql = '
		insert into tnt_tenant_tokens
			(ttt_tnt_id, ttt_token, ttt_who, ttt_date_start, ttt_date_end, ttt_sleep)
		values
			('.$tnt_id.', "'.addslashes( $token ).'", "'.addslashes( $who ).'", "'.addslashes( $date_start ).'", "'.addslashes( $date_end ).'", '.( $sleep ? $sleep : '"null"' ).')
	';

	$res = ria_mysql_query( $sql );
	if (!$res) {
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de récupérer toutes les clés complémentaires d'accès à l'API existants, éventuellement filtrés sur l'identifiant d'un locataire ou sur un token particulier.
 *	@param int $tnt_id Facultatif, identifiant d'un locataire
 *	@param int $id Facultatif, identifiant d'un token en particulier
 *	@param $active Facultatif, Permet de retourner uniquement les tokens actifs
 *	@param $riashopsync Facultatif, Permet de retourner uniquement les tokens pour le logiciel de synchronisation
 *	@return resource Un résultat MySQL contenant :
 *				- tnt_id : identifiant du locataire
 *				- $who : Personne / prestataire a qui est attribué ce token
 *				- token : clé API
 *				- date_start : date de début d'accès à l'API
 *				- date_start : date de fin d'accès à l'API
 *				- sleep : temporisation dans l'API
 *				- actived : si oui ou non la clé est activé
 */
function tnt_tenant_tokens_get( $tnt_id=0, $id=0, $active=null, $riashopsync=null ){
	if (!is_numeric($tnt_id) || $tnt_id < 0) {
		return false;
	}

	if (!is_numeric($id) || $id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select
			ttt_tnt_id as tnt_id, ttt_id as id, ttt_who as who, ttt_token as token, ttt_date_start as date_start, ttt_date_end as date_end, ttt_sleep as sleep,
			ttt_actived as actived
		from tnt_tenant_tokens
		where ttt_date_deleted is null
	';

	if ($tnt_id) {
		$sql .= ' and ttt_tnt_id = '.$tnt_id;
	}

	if ($id) {
		$sql .= ' and ttt_id = '.$id;
	}

	if($active!==null){
		$sql .= ' and ttt_actived='.($active ? 1 : 0);
	}

	if($riashopsync!==null){
		$sql .= ' and ttt_who '.($riashopsync ? '=' : '!=').' \'RiashopSync\'';
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer la clé API à partir de son identifiant.
 *	@param int $tnt_id Facultatif, Identifiant du locataire
 *	@param int $id Facultatif, Identifiant d'un token en particulier
 *	@param bool $delete Facultatif, par défaut les clés supprimées ne seront prisent en compte, mettre true pour que ce soit le cas
 *	@return La clé API correspondant à l'identifiant, False si le paramètre est omis ou faux
 */
function tnt_tenant_tokens_get_byid( $tnt_id=0, $id=0, $delete=false ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select ttt_token as token
		from tnt_tenant_tokens
		where ttt_tnt_id = '.$tnt_id.'
			and ttt_id = '.$id.'
	';

	if (!$delete) {
		$sql .= ' and ttt_date_deleted is null';
	}

	$res = ria_mysql_query( $sql );
	if (!$res || !mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['token'];
}

/** Cette fonction permet de supprimer une clé complémentaire à l'API pour un locataire.
 *	@param int $tnt_id Obligatoire, Identifiant du locataire
 *	@param int $id Obligatoire, Identifiant du token d'API à supprimer
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function tnt_tenant_tokens_delete( $tnt_id, $id ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	$res =  ria_mysql_query('
		update tnt_tenant_tokens
		set ttt_date_deleted = now()
		where ttt_tnt_id = '.$tnt_id.' and ttt_id = '.$id.'
	');

	if (!$res) {
		die('error');
		return false;
	}

	$token = tnt_tenant_tokens_get_byid($tnt_id, $id, true);
	if (trim($token) == '') {
		return false;
	}

	return tnt_tenants_authorized_delete_cache( $tnt_id, $token );
}

/** Cette fonction permet de désactiver une clé complémentaire à l'API pour un locataire.
 *	@param int $tnt_id Obligatoire, Identifiant du locataire
 *	@param int $id Obligatoire, Identifiant de la clé à désactiver
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function tnt_tenant_tokens_disabled( $tnt_id, $id ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		update tnt_tenant_tokens
		set ttt_actived = 0
		where ttt_tnt_id = '.$tnt_id.' and ttt_id = '.$id.'
	');

	if (!$res) {
		return false;
	}

	$token = tnt_tenant_tokens_get_byid($tnt_id, $id);
	if (trim($token) == '') {
		return false;
	}

	return tnt_tenants_authorized_delete_cache( $tnt_id, $token );
}

/** Cette fonction permet de réactiver une clé complémentaire à l'API pour un locataire.
 *	@param int $tnt_id Obligatoire, Identifiant du locataire
 *	@param int $id Obligatoire, Identifiant de la clé à ré-activer
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function tnt_tenant_tokens_enabled( $tnt_id, $id ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		update tnt_tenant_tokens
		set ttt_actived = 1
		where ttt_tnt_id = '.$tnt_id.' and ttt_id = '.$id.'
	');

	if (!$res) {
		return false;
	}

	$token = tnt_tenant_tokens_get_byid($tnt_id, $id);
	if (trim($token) == '') {
		return false;
	}

	return tnt_tenants_authorized_delete_cache( $tnt_id, $token );
}

/** Cette fonction permet de savoir si un locataire dispose d'un autre site que Yuto.
 * 	@param int $tnt_id Optionnel, identifiant d'un locataire (si celui-ci n'est pas défini, on récupère celui dans $config['tnt_id']);
 * 	@return bool True si le locataire dipose d'un site web hors Yuto,
 * 	@return bool False si le locataire ne dispose d'un autre site que Yuto
 */
function tnt_tenants_have_websites( $tnt_id=0 ){
	global $config, $memcached;

	if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
		$tnt_id = isset($config['tnt_id']) ? $config['tnt_id'] : 0;
	}

	if( $tnt_id <= 0 ){
		return false;
	}

	$key_memcached = 'tnt_tenants_have_websites:'.$tnt_id;

	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'ok' ? true : false);
	}

	$r_website = ria_mysql_query('
		select 1
		from tnt_websites
		where wst_tnt_id = '.$tnt_id.'
			and wst_wty_id != 6
	');

	$res = false;
	if( $r_website && ria_mysql_num_rows($r_website) ){
		$res = true;
	}

	$memcached->set($key_memcached, ($res ? 'ok' : 'ko'), 24 * 60 * 60);
	return $res;
}

/** Cette fonction permet de savoir si un locataire dispose ou non de Yuto.
 * 	@param int $tnt_id Optionnel, identifiant d'un locataire (si celui-ci n'est pas défini, on récupère celui dans $config['tnt_id']);
 * 	@return bool True si le locataire dipose de Yuto,
 * 	@return bool False si le locataire ne dispose pas de Yuto
 */
function tnt_tenants_have_yuto( $tnt_id=0 ){
	global $config, $memcached;

	if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
		$tnt_id = isset($config['tnt_id']) ? $config['tnt_id'] : 0;
	}

	if( $tnt_id <= 0 ){
		return false;
	}

	$key_memcached = 'tnt_tenants_have_yuto:'.$tnt_id;

	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'ok' ? true : false);
	}

	$r_website = ria_mysql_query('
		select 1
		from tnt_websites
		where wst_tnt_id = '.$tnt_id.'
			and wst_wty_id = 6
			and wst_date_deleted is null
	');

	$res = false;
	if( $r_website && ria_mysql_num_rows($r_website) ){
		$res = true;
	}

	$memcached->set($key_memcached, ($res ? 'ok' : 'ko'), 24 * 60 * 60);
	return $res;
}

/** Cette fonction permet de savoir si le Riashop du locataire est du type Yuto Essentiel
 * 	@deprecated La formule essentiel n'existe plus
 * 	@return bool True si le Riashop est de type Yuto Essentiel, false dans le cas contraire
 */
function tnt_tenants_is_yuto_essentiel(){
	if( !RegisterGCP::onGcloud() ){
		return false;
	}

	global $config;

	$package = RegisterGCP::getPackage($config['tnt_id']);
	return $package == 'essentiel';
}

/** Cette fonction permet de créer un nouveau tenant et de le configurer.
 * 	@param array $data Obligatoire, tableau contenant toutes les informations sur le tenant (voir ci-dessus)
 * 	@param bool $app_riashop Optionnel, s'il s'agit ou non d'un tenant n'ayant que Yuto (par défaut à False)
 * 	@param string $package Optionnel, permet de savoir quelle formule de Yuto a été choisi (business ou legacy, par défaut "business")
 * 	@param bool $sub Optionnel, permet de savoir si la gestion de l'abonnement est activée (true) ou non (false)
 * 	@return bool True si la création s'est correctement déroulé, False dans le cas contraire
 */
function tnt_tenants_create_poc($data, $app_riashop=false, $package='business', $sub=false){
	global $config;

	$tnt_id = $wst_id = 0;
	$error = array();

	if( !$app_riashop ){
		$data['dirname'] = $data['dirname'].'.maquettes.riastudio.fr';
	}

	{ // Contrôle des paramètres
		// Contrôle du paramètre dirname (obligatoire) seulement si l'on est pas sur l'admin unifié (vrai pour un Yuto pur)
		if( !$app_riashop ){
			if (!isset($data['dirname']) || trim($data['dirname']) == '') {
				$error[] = "Veuillez renseigner le nom du dossier.";
			}

			if (file_exists('/var/www/'.$data['dirname'].'.maquettes.riastudio.fr')) {
				$error[] = 'Le dossier "'.$data['dirname'].'.maquettes.riastudio.fr" existe deja sur le serveur.';
			}
		}

		if (!isset($data['name']) || trim($data['name']) == '') {
			$error[] = "Veuillez renseigner le nom de la société.";
		}

		if (!isset($data['address1']) || trim($data['address1']) == '') {
			$error[] = "Veuillez renseigner le numéro et nom de rue.";
		}

		if (!isset($data['zipcode']) || trim($data['zipcode']) == '') {
			$error[] = "Veuillez renseigner le code postal.";
		}

		if (!isset($data['city']) || trim($data['city']) == '') {
			$error[] = "Veuillez renseigner la ville.";
		}

		if (!isset($data['admin_civility']) || trim($data['admin_civility']) == '') {
			$error[] = "Veuillez renseigner la civilité de l'administrateur par défaut.";
		}

		if (!isset($data['admin_firstname']) || trim($data['admin_firstname']) == '') {
			$error[] = "Veuillez renseigner le prénom de l'administrateur par défaut.";
		}

		if (!isset($data['admin_lastname']) || trim($data['admin_lastname']) == '') {
			$error[] = "Veuillez renseigner le nom de l'administrateur par défaut.";
		}

		if (!isset($data['admin_email']) || trim($data['admin_email']) == '') {
			$error[] = "Veuillez renseigner l'adresse mail de l'administrateur par défaut.";
		}

		if (!isset($data['pay'])) { $data['pay'] = array(); }
		if (!isset($data['payname'])) { $data['payname'] = array(); }

		if (!(is_array($data['pay']) && count($data['pay'])) && !(is_array($data['payname']) && count($data['payname']))) {
			$error[] = "Veuillez renseigner au moins un moyen de paiement par défaut.";
		} else{
			$pay_err = true;

			foreach ($data['pay'] as $pay) {
				if (trim($pay) != '') {
					$pay_err = false;
					break;
				}
			}

			if ($pay_err) {
				foreach ($data['payname'] as $pay) {
					if (trim($pay) != '') {
						$pay_err = false;
						break;
					}
				}
			}

			if ($pay_err) {
				$error[] = "Veuillez renseigner au moins un moyen de paiement par défaut.";
			}
		}
	}

	// Création du tenant
	if (!count($error)) {
		$tnt_id = tnt_tenants_add($data['name'], $data['address1'], $data['address2'], $data['zipcode'], $data['city'], '', '', '', false, $data['next']);
		if (!$tnt_id) {
			$error[] = "Une erreur inattendue s'est produite lors de la création du locataire.";
		}
	}

	// Création du website
	if (!count($error)) {
		$wst_id = wst_websites_add($tnt_id, _WST_TYPE_FDV, 'Yuto');
		if (!$wst_id) {
			$error[] = "Une erreur inattendue s'est produite lors de la création du site Yuto.";
		}

		// Définie ce site comme site par défaut
		ria_mysql_query( '
			update tnt_websites
			set wst_default = 1
			where wst_tnt_id = '.$tnt_id.'
				and wst_id = '.$wst_id.'
		');
	}

	// Création de la catégorie tarifaire par défaut
	if (!count($error)) {
		$config['tnt_id'] = $tnt_id;
		$config['wst_id'] = $wst_id;

		$prc_default = prd_prices_categories_add('Par défaut');
		if (!$prc_default) {
			$error[] = "Une erreur inattendue s'est produite lors de la création de la catégorie tarifaire.";
		} elseif (!cfg_overrides_set_value('default_prc_id', $prc_default, $wst_id, 0, $tnt_id)) {
			$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie tarifaire par défaut.";
		}
	}

	// Création des moyens de paiements
	if (!count($error)) {
		$pay_ids = $data['pay'];

		if (count($data['payname'])) {
			// Création du moyen des nouveaux moyens de paiement
			foreach ($data['payname'] as $pay) {
				if (trim($pay) == '') {
					continue;
				}

				$pay_id = ord_payment_types_add($pay);
				if (!$pay_id) {
					$error[] = "Une erreur inattendue s'est produite lors de la création du moyen de paiement \"".htmlspecialchars($pay)."\".";
				} else {
					$pay_ids[] = $pay_id;
				}
			}
		}

		if (!count($error)) {
			foreach ($pay_ids as $pay_id) {
				$pay_name = ord_payment_types_get_name($pay_id);
				if (trim($pay_name) == '') {
					$error[] = "Une erreur inattendue s'est produite lors de la récupération du moyen de paiement.";
				} else{
					$model_id = ord_payment_models_add($pay_name);
					if (!$model_id) {
						$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement du moyen de paiement.";
					} elseif (!ord_payment_model_details_add($model_id, $pay_id, 0, 0, 0, 1)) {
						$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement du moyen de paiement.";
					}
				}

				if (count($error)) {
					break;
				}
			}

			if (!count($error)) {
				if (!cfg_overrides_set_value('default_usr_payments', implode(', ', $pay_ids))) {
					$error[] = "Une erreur inattendue s'est produite lors de la mise à jour de la variable de configuration des moyens de paiements par défaut.";
				}
			}
		}
	}

	// Création de l'administrateur
	if (!count($error)) {
		$config['password_regex'] = '^[a-zA-Z0-9\-\_\=\.$#§\/\\\[\]\{\}\<\>\&\(\)\+\=\*\%\!\;\,\?\:]{6,}$';
		$config['password_regex_match_valid'] = true;
		$config['password_error_message'] = '';
		$config['notify_new_user'] = false;
		$config['rwd_reward_actived'] = false;
		$config['i18n_lng'] = 'fr';
		$config['i18n_lng_used'] = array('fr');

		$adn_id = gu_users_add_with_adresse($data['admin_email'], 3, $data['admin_civility'], $data['admin_firstname'], $data['admin_lastname'], null, PRF_ADMIN, '', false, 0, 0, null, false, $data['admin_society'], $data['admin_siret'], $data['address1'], $data['address2'], $data['zipcode'], $data['city'], 'FRANCE', $data['admin_phone']);
		if (!$adn_id) {
			$error[] = "Une erreur inattendue s'est produite lors de la création du compte administrateur.";
		}

		// Si un mot de passe est renseigné, celui-ci est sauvegardé (md5 + mdp salé)
		if( isset($data['password_md5']) && trim($data['password_md5']) != '' ){
			$res = gu_users_update_password($adn_id, $data['password_md5'], 16, true);
			if( !$res ){
				$error[] = "Une erreur est survenue lors de la mise à jour du mot de passe de l'administrateur dans la base de données.";
			}
		}

		// Création de l'administrateur dans le registre
		$reg_admin = new Administrator();

		$reg_admin->setEmail($data['admin_email'])
			->setCivility($data['admin_civility'])
			->setFirstname($data['admin_firstname'])
			->setLastname($data['admin_lastname'])
			->setLang('fr_FR')
			->setWelcome('true');

		if( isset($data['password']) && trim($data['password']) != '' ){
			$reg_admin->setPassword($data['password'], true);
		}

		$res = $reg_admin->save(false);

		if( !$res ){
			$error[] = "Une erreur est survenue lors de l'ajout du compte administrateur dans le registre";
		}
	}

	// Création du dépôt par défaut
	if (!count($error)) {
		$dps_id = prd_deposits_add('Par défaut', true, '', '', '', '', 'FRANCE', '', '', '', false, 0);
		if (!$dps_id) {
			$error[] = "Une erreur inattendue s'est produite lors de la création du dépot par défaut.";
		} elseif (!cfg_overrides_set_value('default_dps_id', $dps_id)) {
			$error[] = "Une erreur inattendue s'est produite lors de la mise à jour de la variable de configuration du dépôt par défaut.";
		}
	}

	// Configuration minimale du site
	if (!count($error)) {
		$res_query = ria_mysql_query("
			replace into cfg_images
				( img_tnt_id, img_code, img_width, img_height, img_background, img_clip, img_transparent, img_format, img_old_system )
			values
				( ".$tnt_id.", 'high', 260, 260, '#FFFFFF', 0, 0, 'jpg', 1 ),
				( ".$tnt_id.", 'medium', 150, 150, '#FFFFFF', 0, 0, 'jpg', 1 ),
				( ".$tnt_id.", 'small', 80, 80, '#FFFFFF', 0, 0, 'jpg', 1 ),
				( ".$tnt_id.", 'yuto', 1000, 0, '#FFFFFF', 0, 0, 'jpg', 0 );
		");

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into cfg_images_filters
					( cif_tnt_id, cif_img_code, cif_filter_code, cif_width, cif_height, cif_value, cif_pos )
				values
					( ".$tnt_id.", 'high', 'fill', 260, 260, '', 0 ),
					( ".$tnt_id.", 'medium', 'fill', 150, 150, '', 0 ),
					( ".$tnt_id.", 'small', 'fill', 80, 80, '', 0 ),
					( ".$tnt_id.", 'yuto', 'fill', 1000, 0, '', 0 );
			");
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into cfg_emails
					( email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos )
				values
					( ".$tnt_id.", ".$wst_id.", 'ord-alert', 'Suivi des commandes', 'Un email de suivi de commande est envoyé à chaque étape du traitement de la commande client (enregistrement, préparation de livraison, livraison, etc...).', 1, 0, 0, 1, '<EMAIL>', '".$data['admin_email']."', '', '', 0),
					( ".$tnt_id.", ".$wst_id.", 'ord-owner', 'Notifications de commande', 'Cet email vous est envoyé pour vous avertir d''une nouvelle commande dans la boutique.nIl contient des informations uniquement destinée à l''usage interne (ex: informations bancaires).', 1, 1, 1, 1, '<EMAIL>', '".$data['admin_email']."', '', '', 0),
					( ".$tnt_id.", ".$wst_id.", 'sync_error_odbc', 'Destinataire erreurs ODBC', 'Cet email vous est envoyé pour vous avertir des erreurs de synchronisation.', 1, 1, 1, 1, '<EMAIL>', '".$data['admin_email']."', '', '', 0),
					( ".$tnt_id.", ".$wst_id.", 'device-new', 'Nouvel appareil', 'Envoi une notification dès qu''un nouvel appareil associe l''application Yuto à votre installation.', 1, 1, 1, 1, '<EMAIL>', '".$data['admin_email']."', '', '', 0),
					( ".$tnt_id.", ".$wst_id.", 'notify-usr-del', 'Suppression contact', 'Envoi une notification dès qu\'un contact doit être supprimé.', 1, 1, 1, 1, '<EMAIL>', '".$data['admin_email']."', '', '', 0);
			");
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into site_owner
					( owner_tnt_id, owner_type, owner_name, owner_firstname, owner_lastname, owner_address1, owner_address2, owner_zipcode, owner_city,
					owner_phone, owner_fax, owner_email, owner_naf, owner_taxcode, owner_inscription, owner_capital, owner_publication, owner_redaction )
				values
					( ".$tnt_id.", 0, '', '', '', '', '', '', '', '', '', '', '', '', '', NULL , '', '' );
			");
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into prc_price_fields
					( ppf_tnt_id, ppf_fld_id, ppf_priority )
				values
					( ".$tnt_id.", 455, 1 ),
					( ".$tnt_id.", 3634, 2 ),
					( ".$tnt_id.", 456, 3 );
			");
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into cfg_overrides
					( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
				values
				( ".$tnt_id.", ".$wst_id.", 0, 'email_alerts_enabled', '0' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'faq_enabled', '0' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'newsletter_enabled', '0' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'site_desc', '' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'site_name', 'Yuto' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'fdv_can_only_edit_user', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'fdv_ord_edit_payment', '0' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_sage_price_merge_discount_enable', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_sage_price_or_discount_enable', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_use_new_api', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_sage_contact_empty_email_enable', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_sage_price_merge_discount_enable', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_sage_order_import_note_as_line_enable', '1' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'sync_global_empty_email_parttern', '*@yuto.com' ),
				( ".$tnt_id.", ".$wst_id.", 0, 'fdv_ord_sign_required', '0' );
			");
		}

		if( $res_query ){
			if( $app_riashop ){
				$md5_dirname_media = md5($tnt_id.tnt_tenants_get_date_created($tnt_id).'media');

				$res_query = ria_mysql_query( "
					replace into cfg_overrides
						( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
					values
					( ".$tnt_id.", ".$wst_id.", 0, 'banners_url', 'https://media.fr/".$md5_dirname_media."/banners' ),
					( ".$tnt_id.", ".$wst_id.", 0, 'banners_dir', '/var/www/media.fr/".$md5_dirname_media."/banners' ),
					( ".$tnt_id.", ".$wst_id.", 0, 'doc_dir', '/var/www/media.fr/".$md5_dirname_media."/documents' ),
					( ".$tnt_id.", ".$wst_id.", 0, 'img_dir', '/var/www/media.fr/".$md5_dirname_media."/images' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'img_url', 'https://media.fr/".$md5_dirname_media."/images' );
				");

				// Création des dossiers minimal
				mkdir('/var/www/media.fr/'.$md5_dirname_media);
				mkdir('/var/www/media.fr/'.$md5_dirname_media.'/banners');
				mkdir('/var/www/media.fr/'.$md5_dirname_media.'/documents');
				mkdir('/var/www/media.fr/'.$md5_dirname_media.'/documents/pieces_jointes');
				mkdir('/var/www/media.fr/'.$md5_dirname_media.'/images');
				mkdir('/var/www/media.fr/'.$md5_dirname_media.'/images/source');

				// Attribution des droits
				chown('/var/www/media.fr/'.$md5_dirname_media, 'www-data');
				chown('/var/www/media.fr/'.$md5_dirname_media.'/banners', 'www-data');
				chown('/var/www/media.fr/'.$md5_dirname_media.'/documents', 'www-data');
				chown('/var/www/media.fr/'.$md5_dirname_media.'/documents/pieces_jointes', 'www-data');
				chown('/var/www/media.fr/'.$md5_dirname_media.'/images', 'www-data');
				chown('/var/www/media.fr/'.$md5_dirname_media.'/images/source', 'www-data');
				chgrp('/var/www/media.fr/'.$md5_dirname_media, 'www-data');
				chgrp('/var/www/media.fr/'.$md5_dirname_media.'/banners', 'www-data');
				chgrp('/var/www/media.fr/'.$md5_dirname_media.'/documents', 'www-data');
				chgrp('/var/www/media.fr/'.$md5_dirname_media.'/images', 'www-data');
				chgrp('/var/www/media.fr/'.$md5_dirname_media.'/images/source', 'www-data');
			}else{
				$res_query = ria_mysql_query( "
					replace into cfg_overrides
						( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
					values
						( ".$tnt_id.", ".$wst_id.", 0, 'banners_url', '/images/banners' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'banners_dir', '/var/www/".$data['dirname'].".maquettes.riastudio.fr/htdocs/images/banners' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'doc_dir', '/var/www/".$data['dirname'].".maquettes.riastudio.fr/htdocs/documents' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'img_dir', '/var/www/".$data['dirname'].".maquettes.riastudio.fr/htdocs/images/products' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'img_url', 'http://".$data['dirname'].".maquettes.riastudio.fr/images/products' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'site_dir', '/var/www/".$data['dirname'].".maquettes.riastudio.fr/htdocs' ),
						( ".$tnt_id.", ".$wst_id.", 0, 'site_url', 'http://".$data['dirname'].".maquettes.riastudio.fr' );
				");
			}
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into cfg_urls
					( url_tnt_id, url_wst_id, url_cls_id, url_key, url_name, url_used )
				values
					( ".$tnt_id.", ".$wst_id.", 1, '', '/catalog/product.php', 1),
					( ".$tnt_id.", ".$wst_id.", 3, '', '/catalog/index.php', 1),
					( ".$tnt_id.", ".$wst_id.", 6, '', '/stores/index.php', 1),
					( ".$tnt_id.", ".$wst_id.", 11, '', '/cms/view.php', 1),
					( ".$tnt_id.", ".$wst_id.", 13, '', '/faq/category.php', 1),
					( ".$tnt_id.", ".$wst_id.", 14, '', '/news/news.php', 1),
					( ".$tnt_id.", ".$wst_id.", 15, '', '/news/index.php', 1),
					( ".$tnt_id.", ".$wst_id.", 16, '', '/faq/category.php', 1);
			");
		}

		if ($res_query) {
			$res_query = ria_mysql_query("
				replace into prd_relations_types
					( type_tnt_id, type_name, type_name_plural )
				values
					( ".$tnt_id.", 'Produit associé', 'Produits associés');
			");
		}

		if (!$res_query) {
			$error[] = _("Une erreur inattendue s'est produite lors de l'enregistrement de la configuration minimale.");
		}
	}

	// Configuration minimal des pocs
	if (!count($error)) {
		if (
			!cfg_overrides_set_value('fdv_can_edit_user', 'Oui')
			|| !cfg_overrides_set_value('fdv_ord_sign_required', 'Non')
			|| !cfg_overrides_set_value('fdv_usr_locked_can_valid_order', ' Oui')
			|| !cfg_overrides_set_value('fdv_ord_edit_payment', 'Non')
			|| !cfg_overrides_set_value('fdv_ord_duplicate_comment', 'Oui')
			|| !cfg_overrides_set_value('device_subscription_active', 'Oui')
			|| !cfg_overrides_set_value('admin_visible_order_statuses', '28,3,25,5,7,24,8,9,10,12')
			|| !cfg_overrides_set_value('orders_update_state_included', '3,4,5,6,7,11')
			|| !cfg_overrides_set_value('allow_orders_update_state', 'Oui')
		) {
			$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement de la configuration Yuto par défaut.";
		}
	}

	// Gestion des droits par défaut
	if (!count($error)) {
		// Ci-dessous la liste des droits pour un tenant standard
		$rgh_default = array(
			1254, 1255, 2000, 2001, 2002, 2003, 2005, 3000, 3001, 3004, 3005, 3008, 3050, 3100, 3102, 5000, 5001, 5008, 6000, 6050, 6250, 6400, 7050, 7051, 7052,
			7053, 7058, 7105, 7109, 7130, 7250, 9560, 11100, 11150, 11152, 11153, 11154, 11160, 11200, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007,
			13008, 13009, 13010, 13011, 13012, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028,
			13029, 13030, 13031, 13032, 13033, 13034, 13035, 13036, 13037, 13038, 13039, 13040, 13041, 13042, 13047, 13049,
			13055, 13056, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105,
			1150, 1151, 1152, 1153, 1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 2004, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105, 2150,
			2151, 2152, 2153, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5002, 5003, 5004, 5005, 5006, 5009, 5010, 5011, 5012, 5013, 5050, 5051, 5052,
			5053, 5054, 5055, 5056, 5057, 5100, 6100, 6101, 6102, 6103, 6104, 6401, 6402, 6403, 6404, 6405, 6600, 6601, 6602, 6603, 6604, 6605, 6606, 6607,
			6610, 7000, 7100, 7101, 7102, 7103, 7104, 7106, 7107, 7108, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7131, 7150,
			7200, 7201, 7202, 7203, 7204, 7205, 7206, 7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223,
			7224, 7225, 7251, 7252, 7253, 7254, 7300, 7301, 7302, 7303, 7304, 7305, 7306, 7307, 7350, 7400, 7450, 7600, 7650, 7700, 7701, 7702, 7800, 7801,
			7802, 7803, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 11000, 11001, 11002, 11003, 11050, 11101, 11102, 11151, 11250, 12050, 12100,
			6609, 6611, 6612, 6613, 6614, 6650
		);

		// Ci-dessous la liste des droits pour un tenant avec une gestion d'abonnement pour un Yuto Business
		if( $package == 'business' ){
			$rgh_default = array(
				1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105, 1150, 1151, 1152, 1153,
				1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 1254, 1255, 2000, 2001, 2002, 2003, 2004, 2005, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105,
				2150, 2151, 2152, 2153, 3000, 3001, 3004, 3005, 3008, 3050, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5000, 5001, 5002, 5003, 5004, 5005, 5008, 5009, 5010,
				5011, 5012, 5013, 5050, 5051, 5052, 5053, 5054, 5055, 5056, 5057, 6600, 6601, 6603, 6604, 6605, 6606, 6607, 6610, 7000, 7050, 7051, 7052, 7053, 7058, 7100,
				7101, 7102, 7103, 7104, 7105, 7106, 7107, 7108, 7109, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7130, 7131, 7200, 7205, 7206,
				7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223, 7224, 7225, 7250, 7251, 7252, 7253, 7254, 7300, 7301,
				7302, 7303, 7304, 7305, 7306, 7307, 7350, 7400, 7450, 7600, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 9560, 11000, 11001, 11002, 11003, 11050,
				11100, 11101, 11102, 11150, 11151, 11152, 11153, 11154, 11160, 11200, 12050, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007, 13008, 13009, 13010,
				13011, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028, 13029, 13030, 13031, 13032, 13033, 13034,
				13035, 13037, 13038, 13039, 13040, 13041, 13042, 13043, 13044, 13045, 13046, 13047, 13048, 13049, 13055, 13056, 13058, 13059, 13060, 13061, 13062, 13012,
				6609, 6611, 6612, 6613, 6614, 6650, 1000092, 1000093, 1000094, 1000095, 1000096, 1000097, 1000098, 1000099, 1000100, 1000101, 1000102, 1000103, 1000104, 1000105,
				11250,
				1000075, 1000076, 1000077, 1000078, 1000079
			);
		}

		// Si la gestion de l'abonnement est activé, alors les droits liés à ces interfaces sont ajoutés
		if( $sub ){
			$rgh_default = array_merge($rgh_default, array(1000029));

			// Défini la clé API géocod
			cfg_overrides_set_value( 'gmaps_geocode_key', 'AIzaSyAZPEjesQ0yeZ9ZdUvOKYPcqrb3-4qLzJE', $wst_id, 0, $tnt_id );
		}


		if (!wst_website_rights_add($tnt_id, $wst_id, $rgh_default)) {
			$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement des droits par défaut.";
		}
	}


	// Configuration spécifique à la formule "Business" de Yuto
	if( in_array($package, array('business')) ){
		cfg_overrides_set_value('dev_need_activation', 0, $wst_id);
		cfg_overrides_set_value('admin-list-prds-cols', 'ref,name,price_ht,price_ttc,publish-site,fields-missing', $wst_id);
	}else{
		if (!count($error)) {
			// Création de la catégorie cat_root Yuto
			$cat_id = prd_categories_add('Yuto');
			if (!$cat_id) {
				$error[] = "Une erreur inattendue s'est produite lors de la création de la catégorie principale \"Yuto\".";
			} elseif (!cfg_overrides_set_value('cat_root', $cat_id) || !cfg_overrides_set_value('sync_default_category', $cat_id)) {
				$error[] = "Une erreur inattendue s'est produite lors de la configuration de la catégorie principale.";
			}

			// Création de la catégorie cat_root pour les produits venant de la gescom
			$cat_id = prd_categories_add('Gescom');
			if (!$cat_id) {
				$error[] = "Une erreur inattendue s'est produite lors de la création de la catégorie principale \"Gescom\".";
			} elseif (!cfg_overrides_set_value('sync_prd_new_cat_root', $cat_id)) {
				$error[] = "Une erreur inattendue s'est produite lors de la configuration de la catégorie pour les produits par défaut.";
			}

			// Création de la catégorie cat_catalog pour les produits venant de la gescom
			$cat_id = prd_categories_add('Catalogue');
			if (!$cat_id) {
				$error[] = "Une erreur inattendue s'est produite lors de la création de la catégorie principale \"Catalogue\".";
			} elseif (!cfg_overrides_set_value('sync_default_catalog', $cat_id)) {
				$error[] = "Une erreur inattendue s'est produite lors de la configuration de la catégorie pour le catalogue sage.";
			}
		}
	}

	// Configuration générale
	if (!count($error)) {
		if (isset($data['cfg-values']) && is_array($data['cfg-values']) && count($data['cfg-values'])) {
			foreach ($data['cfg-values'] as $key => $value) {
				if (!cfg_overrides_set_value($key, $value)) {
					$error[] = "Une erreur inattendue s'est produite lors de l'enregistrement de la configuration générale (\"".htmlspecialchars($key)."\").";
					break;
				}
			}
		}
	}

	// Création d'un type d'album pour les comptes clients
	img_images_types_add(CLS_USER, 'Images');

	// Configure le nombre de licences Yuto acceptées
	if( !count($error) ){
		$date = new DateTime();

		// Détermine la date de début de l'abonnement à aujourd'hui
		$date_start = $date->format('Y-m-d');

		// Détermine la date de fin de l'abonnement
		if( isset($data['date_end']) && isdate($data['date_end']) ){
			$date_end = $data['date_end'];
		}else{
			$date->modify('+1 '.$data['typ_subscription']);
			$date_end = $date->format('Y-m-d');
		}

		// Détermine la période d'essai (en nombre de jours)
		$testing = 0;
		if( is_numeric($data['testing']) && $data['testing'] > 0 ){
			$testing = $data['testing'];
		}

		// Détermine si une commande Yuto VEL est à l'origine de l'abonnement
		$ord_id = 0;
		if( isset($data['ord_id']) && is_numeric($data['ord_id']) && $data['ord_id'] > 0 ){
			$ord_id = $data['ord_id'];
		}

		// Détermine le compte Yuto VEL à l'origine de l'abonnement
		$usr_id = 0;
		if( isset($data['usr_id']) && is_numeric($data['usr_id']) && $data['usr_id'] > 0 ){
			$usr_id = $data['usr_id'];
		}

		// Détermine le montant de l'abonnement
		$cost = $cost_ht = 0;
		if( isset($data['cost']) && is_numeric($data['cost']) && $data['cost'] > 0 ){
			$cost = $data['cost'];
		}
		if( isset($data['cost_ht']) && is_numeric($data['cost_ht']) && $data['cost_ht'] > 0 ){
                        $cost_ht = $data['cost_ht'];
                }

		$res = dev_subscribtions_add($data['nb_devices'], $date_start, $date_end, $testing, $ord_id, $cost, $usr_id, $cost_ht);
		if( !$res ){
			$error[] = 'Une erreur inattendue s\'est produite lors de l\'enregistrement de la période d\'essai';
		}
	}

	if (count($error)) {
		return array(
			'result' => false,
			'content' => array(
				'errors' => $error,
				'tnt_id' => $tnt_id,
				'wst_id' => $wst_id,
			)
		);
	}

	return array(
		'result' => true,
		'content' => array(
			'tnt_id' => $tnt_id,
			'wst_id' => $wst_id,
		)
	);
}


/** Cette fonction permet de le website pour yuto, si celui ci n'existe pas deja
 * 	@return bool True si la création s'est correctement déroulé, False dans le cas contraire
 */
function tnt_tenants_add_yuto(){
	global $config;

	// récupère les website du tenant pour tester si un si web yuto existe deja.
	$rwst = wst_websites_get(0,false, null, false, _WST_TYPE_FDV);
	if( $rwst && ria_mysql_num_rows($rwst) > 0 ){
		return true;
	}

	// Création du website
	$wst_id = wst_websites_add($config['tnt_id'], _WST_TYPE_FDV, 'Yuto');
	if (!$wst_id) {
		error_log(__FILE__.':'.__LINE__.' Erreur dans la création du website yuto');
		return false;
	}

	// Configuration copié à partir du site par défaut
	$res_query = ria_mysql_query("
		replace into cfg_emails
			( email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos )
		values
			( ".$config['tnt_id'].", ".$wst_id.", 'ord-alert', 'Suivi des commandes', 'Un email de suivi de commande est envoyé à chaque étape du traitement de la commande client (enregistrement, préparation de livraison, livraison, etc...).', 1, 0, 0, 1, '<EMAIL>', '', '', '<EMAIL>', 0),
			( ".$config['tnt_id'].", ".$wst_id.", 'ord-owner', 'Notifications de commande', 'Cet email vous est envoyé pour vous avertir d''une nouvelle commande dans la boutique.nIl contient des informations uniquement destinée à l''usage interne (ex: informations bancaires).', 1, 1, 1, 1, '<EMAIL>', '<EMAIL>', '', '', 0),
			( ".$config['tnt_id'].", ".$wst_id.", 'device-new', 'Nouvel appareil', 'Envoi une notification dès qu''un nouvel appareil associe l''application Yuto à votre installation.', 1, 1, 1, 1, '<EMAIL>', '<EMAIL>', '', '', 0),
			( ".$config['tnt_id'].", ".$wst_id.", 'notify-usr-del', 'Suppression contact', 'Envoi une notification dès qu\'un contact doit être supprimé.', 1, 1, 1, 1, '<EMAIL>', '<EMAIL>', '', '', 0);
	");

	if ($res_query) {
		$res_query = ria_mysql_query("
			replace into cfg_overrides
				( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )

			select ovr_tnt_id, ".$wst_id.", ovr_usr_id, ovr_var_code, ovr_value
				from cfg_overrides
				join tnt_websites on wst_tnt_id = ovr_tnt_id and wst_default=1
				where ovr_tnt_id=".$config['tnt_id']."
		");
	}

	if ($res_query) {
		$res_query = ria_mysql_query("
			replace into wst_rights
				( wrg_tnt_id, wrg_wst_id, wrg_rgh_id, wrg_rgh_allowed )

			select wrg_tnt_id, ".$wst_id.", wrg_rgh_id, wrg_rgh_allowed
				from wst_rights
				join tnt_websites on wst_tnt_id = wrg_tnt_id and wst_default=1
				where wrg_tnt_id=".$config['tnt_id']."
		");
	}

	if (!$res_query) {
		error_log(__FILE__.':'.__LINE__.' Erreur dans la duplication des configs pour le website yuto');
		return false;
	}

	return true;
}
/// @}

/// \endcond


/**	Retourne la désignation de l'ERP du client actuellement connecté à l'instance. Pour fonctionner, cette fonction se base sur les variables
 * 	de configuration suivantes :
 * 		- sync_global_gescom_type : type de gestion commerciale
 * 		- sync_sage_version : version de sage
 * 		- sync_sage_om_enable : booléen indiquant si les objets métiers sont activés ou bien si nous utilisons le driver ODBC
 *	@return string le nom de l'erp actif pour le client
 */
function tnt_sync_erp_name(){
	global $config;

	$erp_name = 'Inconnu';
	switch( $config['sync_global_gescom_type'] ){
		case GESCOM_TYPE_SAGE:
			$erp_name = 'Sage '.$config['sync_sage_version'].' '.( $config['sync_sage_om_enable'] ? 'OM': 'ODBC');
			break;
		case GESCOM_TYPE_ISIALIS:
			$erp_name = 'Isialis';
			break;
		case GESCOM_TYPE_LIMS:
			$erp_name = 'Lims';
			break;
		case GESCOM_TYPE_HARMONYS:
			$erp_name = 'Harmonys';
			break;
		case GESCOM_TYPE_PLATON:
			$erp_name = 'Platon';
			break;
		case GESCOM_TYPE_APINEGOCE:
			$erp_name = 'APINEGOCE';
			break;
		case GESCOM_TYPE_G5:
			$erp_name = 'G5';
			break;
		case GESCOM_TYPE_DIVALTO:
			$erp_name = 'Divalto';
			break;
		case GESCOM_TYPE_CLISSON:
			$erp_name = 'Clisson';
			break;
		case GESCOM_TYPE_EEE:
			$erp_name = 'EEE';
			break;
		case GESCOM_TYPE_DYNAMICS:
			$erp_name = 'Dynamics AX';
			break;
		case GESCOM_TYPE_SOFI:
			$erp_name = 'Sofi';
			break;
		case GESCOM_TYPE_WAVESOFT:
			$erp_name = 'Wavesoft';
			break;
		case GESCOM_TYPE_CEGID:
			$erp_name = 'Cegid';
			break;
		case GESCOM_TYPE_SAGE_X3:
			$erp_name = 'Sage X3';
			break;
		case GESCOM_TYPE_DYNAMICS_NAVISION:
			$erp_name = 'Navision';
			break;
		case GESCOM_TYPE_SINERES:
			$erp_name = 'Sineres';
			break;
		case GESCOM_TYPE_DIVALTO_SQL:
			$erp_name =  'Divalto SQL';
			break;
		case GESCOM_TYPE_BATIGEST:
			$erp_name = 'BATIGEST';
			break;
		case GESCOM_TYPE_STAR6000:
			$erp_name = 'Star6000';
			break;
		case GESCOM_TYPE_EBP:
			$erp_name = 'EBP';
			break;
		case GESCOM_TYPE_SAGE_50C:
			$erp_name = 'Sage 50c';
			break;
		case GESCOM_TYPE_QUADRATUS:
			$erp_name = 'Quadratus';
			break;
	}

	return $erp_name;

}
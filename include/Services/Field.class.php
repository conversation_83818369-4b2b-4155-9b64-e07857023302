<?php

class FieldService
{

	/**	Identifiant du champ avancé
	 * @var	int
	 */
	protected	$id = 0;

	/**	Nom du champ avancé
	 * @var	string
	 */
	protected	$name = '';

	/**	Description du champ avancé
	 * @var	string
	 */
	protected	$desc = '';

	/**	Valeurs restreintes du champ avancé
	 * @var	array
	 */
	protected	$values = [];

	/**	Si le champ avancé est obligatoire ou non
	 * @var	bool
	 */
	protected	$required = false;

	/**	Type du champ avancé
	 * @var	null|int
	 */
	protected	$type = null;

	/**	Classe du champ avancé
	 * @var	null|int
	 */
	protected	$cls = null;


	/**	Contructeur de la classe
	 * @param	int		$id		Obligatoire, Identifiant d'un champ avancé
	 * @return	void
	 */
	public function __construct($id)
	{
		$this->id = is_numeric($id) && $id > 0 ? $id : 0;
	}

	/**	Permet de définir une information sur le champ avancé
	 * @param	string		$prop	Obligatoire, Information à ajouter
	 * @param	mixed		$value	Obligatoire, Valeur à définir
	 * @return	FieldService	L'object en cours
	 */
	public function setProperty($prop, $value)
	{
		if (!is_string($prop) || !trim($prop)) {
			return $this;
		}

		switch (strtolower(trim($prop))) {
			case 'name':
				$this->name = is_string($value) ? $value : '';
				break;
			case 'desc':
				$this->desc = is_string($value) ? $value : '';
				break;
			case 'values':
				$this->values = is_array($value) ? $value : [];
				break;
			case 'required':
				$this->required = is_bool($value) ? $value : false;
				break;
			case 'type':
				$this->type = is_numeric($value) && $value >= FLD_TYPE_TEXT && $value <= FLD_TYPE_FILE_UPLOAD ? (int)$value : null;
				break;
			case 'cls':
				$this->cls = is_numeric($value) && $value >= CLS_DEVIS && $value <= CLS_FLD_VALUES ? (int)$value : null;
		}

		return $this;
	}

	/**	Permet de retourner des informations du champ avancé
	 * @param	bool	$load	Optionnel, True pour forcer le chargement des informations du champ avancé
	 * @return	array	Tableau de toutes les informations du champ avancé
	 */
	public function getData($load = false)
	{
		if (is_bool($load) && $load) {
			$this->__load();
		}
		$array = get_object_vars($this);

		return $array;
	}

	/**	Permet le chargement des informations du champ avancé (va surcharger les informations déjà définies)
	 * @return	FieldService	L'objet en cours
	 */
	private function __load()
	{
		if (!is_numeric($this->id) || $this->id <= 0) {
			return $this;
		}

		$rfld = fld_fields_get($this->id);

		if (!ria_mysql_num_rows($rfld)) {
			return $this;
		}
		$fld = ria_mysql_fetch_assoc($rfld);
		$values = [];

		if (in_array($fld['type_id'], [FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY, FLD_TYPE_SELECT])) {
			$rres = fld_restricted_values_get(0, $this->id);

			if( ria_mysql_num_rows($rres)){

				while($res = ria_mysql_fetch_assoc($rres)){
					$values[] = $res;
				}
			}
		}

		$this->setProperty('name', $fld['name'])
			->setProperty('desc', $fld['desc'])
			->setProperty('type', $fld['type_id'])
			->setProperty('cls', $fld['cls_id'])
			->setProperty('required', $fld['is_mandatory'] == 1)
			->setProperty('values', $values);

		return $this;
	}
}

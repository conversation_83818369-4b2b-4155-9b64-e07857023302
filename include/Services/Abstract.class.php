<?php

require_once 'Services/Interface.class.php';
require_once 'Services/Hook.class.php';

abstract class AbstractService implements InterfaceService
{
	/**	Tableau contenant les attributs de requête
	 * @var	array
	 */
	protected $attributes;

	/**	Permet de définir une valeur à un attribut
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	object	L'objet en cours
	 */
	public function setAttribute($key, $value)
	{
		$this->__sanitizeAttribute($key, $value);
		return $this;
	}

	/**	La valeur d'un attribut
	 * @param	string		$key	Obligatoire, Nom de l'attribut
	 * @return	mixed|null	Valeur de l'attribut, null en cas d'erreur
	 */
	public function getAttribute($key)
	{
		if (!is_string($key)) {
			return null;
		}
		$key = trim(strtolower($key));

		if (!$key) {
			return null;
		}
		return array_key_exists($key, $this->attributes) ? $this->attributes[$key] : null;
	}

	/**	Retourne tous les attributs
	 * @return	array	Tableau attribut => valeur
	 */
	public function getAttributes()
	{
		return $this->attributes;
	}

	/**	Permet de controler la valeur d'un attribut et de le mettre à jour
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	bool	True en cas de succes, false sinon
	 */
	protected function __sanitizeAttribute($key, $value)
	{
		return $value;
	}

	/**	Retourne les données de l'objet en cours sous format d'un tableau associatif
	 * @param	bool	$recursive	Optionnel, Si les données doivent être transformées récursivement
	 * @return	array	Un tableau contenant les informations liées à l'objet courant
	 */
	public function getData($recusive = true)
	{
		return $this->__transformValue($this, $recusive);
	}

	/**	Analyse et transforme récursivement un objet en tableau
	 * @param	mixed	$value		Obligatoire, valeur a analyser et transformer en tableau si besoin
	 * @param	bool	$recursive	Optionnel, Si les données doivent être transformées récursivement
	 * @return	mixed	La valeur
	 */
	protected function __transformValue($value, $recusive = true)
	{
		$array = is_object($value) ? get_object_vars($value) : $value;

		if (!is_array($array)) {
			return $value;
		}
		$final = [];

		foreach ($array as $prop => $value) {
			$final[$prop] = $this->__transformValue($value, $recusive);
		}

		return $final;
	}
}

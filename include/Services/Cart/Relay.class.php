<?php

require_once('orders.inc.php');
require_once('Services/Service.class.php');

/** \brief Cette classe permet de charger les points relais.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 97 : Le service de point relai configuré n'existe pas
 * 			- 98 : Aucun service de point relais n'est configuré
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class RelayService extends Service{
	private $service = false; ///< Prestataire de point relai
	private $srvid = 0; ///< Identifiant du service de livraison

	// Une fois la recherche effectuée, les points relais seront stocks dans cette liste
	private $list = []; ///< Tableau des points relais

	/** Cette fonction permet d'initialiser une recherche de point relais.
	 *  L'initialisation passe obligatoire par la passage du service de point relai.
	 *  @param $service Obligatoire, identifiant du prestataire de points relais
	 * 	@param $srv_id Optionnel, identifiant du service de livraison
	 *  @return RelayService L'objet courant
	 */
	public function __construct( $service=0, $srv_id=0 ){
		if( !dlv_relay_types_exists($service) ){
			throw new Exception('Le service de point relais configuré n\'existe pas.', 97);
		}

		$this->service = $service;
		$this->srvid = $srv_id;

		return $this;
	}

	/** Cette fonction permet de retourner un tableau des points relais.
	 *  Il faut pour cela donner un code postal et une ville.
	 *  @param string $zipcode Obligatoire, code postal
	 *  @param string $city Obligatoire, ville
	 * 	@param string $country Optionnel, code ISO du pays dans lequel recherche les points relais (par défaut "FR")
	 *  @return RelayService L'objet courant
	 */
	public function search( $zipcode, $city, $country='FR' ){
		if( trim($zipcode) == '' ){
			throw new Exception(i18n::get('Le code postal n\'est pas renseigné.', 'ERROR'), 1);
		}

		if( trim($city) == '' ){
			throw new Exception(i18n::get('La ville n\'est pas renseignée.', 'ERROR'), 2);
		}

		if( !is_numeric($this->service) || $this->service <= 0 ){
			throw new Exception(i18n::get('Aucun service de point relais n\'est configuré.', 'ERROR'), 98);
		}

		// Recherche de point relai selon le service
		$xml_relay = '';

		$ar_country = [ $country ];

		if( $this->srvid > 0 ){
			$ar_country = [];

			$r_zone = dlv_zones_get( 0, true, $this->srvid );
			if( $r_zone ){
				while( $zone = ria_mysql_fetch_assoc($r_zone) ){
				$r_pack = dlv_package_price_zones_get( false, false, $zone['id'] );
					while( $pack = ria_mysql_fetch_assoc($r_pack) ){
						if( $pack['dzn_type_id'] == _ZONE_PAYS && trim($pack['dzn_code']) != '' ){
						$ar_country[ $pack['dzn_code'] ] = $pack['dzn_code'];
						}

						if( in_array($pack['dzn_type_id'], [_ZONE_RGN_FRANCE, _ZONE_DPT_FRANCE, _ZONE_ZIPCODES, _ZONE_INSEE]) ){
							$ar_country[ 'FR' ] = 'FR';
						}
					}
				}
			}
		}

		foreach( $ar_country as $one_coutry ){
			switch( $this->service ){
				case CHRONOPOST_RELAY_TYPE:
					$xml_relay = dlv_relays_get_chronopost_list( $zipcode, $city, $one_coutry );
					break;
				case SO_COLISSIMO_TYPE:
					$date_livr = new DateTime();
					$date_livr->modify('+4 days');

					$xml_relay = dlv_relays_get_colissimo_list( $zipcode, $city, $date_livr->format('d/m/Y') );
					break;
				case MONDIAL_RELAY_TYPE:
					$xml_relay = dlv_relays_get_mondialrelay_list( $zipcode, $city, 0, -1, true, $one_coutry );
					break;
				case GLS_RELAY_TYPE:
					$xml_relay = dlv_relays_get_gls_list( $zipcode, $one_coutry, 20, true );
					break;
			}

			if( trim($xml_relay) != '' ){
				break;
			}
		}

		if( trim($xml_relay) == '' ){
			throw new Exception(i18n::get('Aucun point relais n\'a été trouvé.', 'ERROR'), 3);
		}

		$xml = simplexml_load_string( $xml_relay );
		if( !isset($xml->listePointRetraitAcheminement) || !count($xml->listePointRetraitAcheminement) ){
			throw new Exception(i18n::get('Aucun point relais n\'a été trouvé.', 'ERROR'), 3);
		}

		// Ajout des points relais au moteur RiaShop
		dlv_relays_add_by_xml( $this->service, $xml_relay );

		foreach( $xml as $one_relay  ){
			$this->list[] = [
				'code' => (string) $one_relay->identifiant,
				'name' => (string) $one_relay->nom,
				'address1' => (string) $one_relay->adresse1,
				'address2' => (string) $one_relay->adresse2,
				'address3' => (string) $one_relay->adresse3,
				'zipcode' => (string) $one_relay->codePostal,
				'city' => (string) $one_relay->localite,
				'latitude' => (string) $one_relay->coordGeolocalisationLatitude,
				'longitude' => (string) $one_relay->coordGeolocalisationLongitude,
				'distance' => (int) $one_relay->distanceEnMetre,
				'opening-time' => [
					'sunday' => $this->openingTime( (string) $one_relay->horairesOuvertureDimanche ),
					'monday' => $this->openingTime( (string) $one_relay->horairesOuvertureLundi ),
					'tuesday' => $this->openingTime( (string) $one_relay->horairesOuvertureMardi ),
					'wednesday' => $this->openingTime( (string) $one_relay->horairesOuvertureMercredi ),
					'thursday' => $this->openingTime( (string) $one_relay->horairesOuvertureJeudi ),
					'friday' => $this->openingTime( (string) $one_relay->horairesOuvertureVendredi ),
					'saturday' => $this->openingTime( (string) $one_relay->horairesOuvertureSamedi ),
				]
			];
		}

		return $this;
	}

	/** Cette fonction retourne la liste des points relais trouvés lors d'une recherche.
	 *  @return array La liste des points relais
	 */
	public function getRelayList(){
		return $this->list;
	}

	/** Cette fonction permet de découper les horaires d'ouvertures d'une journée.
	 * 	La valeur d'entrée qui est attendu doit être sous ce format : 00:00-00:00 00:00-00:00.
	 * 	@return array Un tableau contenant [ 'am' => ['ouverture', 'fermeture'], 'pm' => ['ouverture', 'fermeture'] ]
	 */
	private function openingTime( $time ){
		$am_pm = explode( ' ', $time );

		$am = $pm = '';
		if( is_array($am_pm) && count($am_pm) == 2 ){
			$am = explode( '-', $am_pm[0] );
			$pm = explode( '-', $am_pm[1] );
		}

		return [ 'am' => $am, 'pm' => $pm ];
	}
}
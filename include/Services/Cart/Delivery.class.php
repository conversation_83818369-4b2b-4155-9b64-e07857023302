<?php
require_once 'Services/Service.class.php';
require_once 'Services/Cart/Cart.class.php';

require_once 'delivery.inc.php';

/**	\brief Cette classe permet de gérer le système de livraison.
 */
class DeliveryService extends Service {
	private static $instance = null;

	protected $services = null; ///< Liste des services éligibles à la livraison
	protected $service = null; ///< Tableau contenant les données du service de livraison
	protected $zone = null; ///< Tableau contenant les données de la zone de livraison

	private $prds = []; ///< Filtre les services de livraison en fonction de produits
	protected $cart = null; ///< Filtre les services selon un panier en cours (rattaché l'objet CartService)
	private $cache = [];

	private $options = []; ///< Tableau des options supplémentaire ()

	// Liste des options possible :
	// - force_ht : force le montant ht de la commande
	// - force_ttc : force le montant ttc de la commande

	// Tableau de la localisation par défaut
	private $defaultLocation = [
		'zipcode'	=> '75001',
		'city'		=> 'PARIS',
		'country'	=> 'FRANCE',
		'cntcode'	=> 'FR'
	];

	/** Cette fonction permet d'initialiser un objet livraison.
	 * 	@param array $options Optionnel permet de passer des options supplémentaire
	 * 	@return object L'instance nouvellement créée
	 */
	public static function getInstance( $options=[] ){
		if( is_null(self::$instance) ){
			self::$instance = new DeliveryService( $options );
		}
		return self::$instance;
	}

	/** Cette fonction retour le tableau des services.
	 * 	@return array Un tableau contenant les services
	 */
	public function getData(){
		return $this->transformObjectToArray( $this->services );
	}

	/** Cette fonction permet de récupérer le détail sur les frais de ports.
	 * 	Elle s'appuit sur le panier en cours
	 * @param	array	$cat		Optionnel, Tableau d'identifiants de catégories pour lesquelles limiter le résultat
	 * @param	array	$prd		Optionnel, Tableau d'identifiants de produits pour lesquels limiter le résultat
	 * @param	bool	$simulate	Optionnel, Permet d'activer la simulation sur une localisation par défaut @deprecated Ne plus utiliser se paramètre
	 * @return	object	L'objet en cours
	 */
	public function getPort( $cat=[], $prd=[], $simulate=false, $noforce=false ){
		global $config, $memcached;
		$time_start = microtime(true);

		// Identifiant de la commande en cours
		$Cart = CartService::getInstance();
		$Cart->fields();

		$ar_srv_perso = new Collection();
		{ // Chargement des frais de port personnalisé pour la commande
			$r_port_perso = ria_mysql_query('
				select
					srv_id, srv_name, srv_desc, srv_type_id, srv_img_id, srv_prd_ref, srv_fld_id, srv_weight_min,
					srv_weight_max, pv_value
				from fld_object_values
					join dlv_services on (srv_tnt_id = '.$config['tnt_id'].' and srv_fld_id = pv_fld_id)
				where pv_tnt_id = '.$config['tnt_id'].'
					and pv_obj_id_0 = '.$Cart->getID().'
					and pv_fld_id in (5032, 5033, 5094, 5095, 5096, 5097, 5098, 5099, 5100)
					and srv_date_deleted is null
			');

			if( $r_port_perso ){
				while( $port_perso = ria_mysql_fetch_assoc($r_port_perso) ){
					$ar_srv_perso->addItem( [
						'id' => $port_perso['srv_id'],
						'name' => $port_perso['srv_name'],
						'desc' => $port_perso['srv_desc'],
						'type' => $port_perso['srv_type_id'],
						'image' => $port_perso['srv_img_id'],
						'prd' => $port_perso['srv_prd_ref'],
						'weight_min' => $port_perso['srv_weight_min'],
						'weight_max' => $port_perso['srv_weight_max'],
						'fld_id' => $port_perso['srv_fld_id'],
						'port' => $port_perso['pv_value'],
						'zone' => 0,
					], 'srv'.$port_perso['srv_id'] );
				}
			}
		}

		// Si des frais de port sont personnalisés sur la commande alors on ne chargera pas les grilles
		// tarifaire comme en temps normal
		if( $ar_srv_perso !== null && $ar_srv_perso->length() > 0 ){
			$this->services = $ar_srv_perso;
		}else{

			$adr_delivery = $this->defaultLocation;

			if( is_a( $this->cart, 'CartService') ){
				$this->cart->address();
				$temp = $this->cart->getData()['adrdelivery'];
				if( ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $temp) ){
					$adr_delivery = $temp;
				}

				if( trim($adr_delivery['country']) == '' ){
					$temp = $this->cart->getData()['adrinvoice'];
					if( ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $temp) ){
						$adr_delivery = $temp;
					}
				}
			}

			if( $adr_delivery['country'] == 'FR' ){
				$adr_delivery['country'] = 'FRANCE';
				$adr_delivery['cntcode'] = 'FR';
			}
			if( $adr_delivery['country'] == 'BE' ){
				$adr_delivery['country'] = 'BELGIQUE';
				$adr_delivery['cntcode'] = 'BE';
			}
			if( $adr_delivery['country'] == 'LU' ){
				$adr_delivery['country'] = 'LUXEMBOURG';
				$adr_delivery['cntcode'] = 'LU';
			}

			if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
				if( trim($adr_delivery['cntcode']) == '' ){
					$adr_delivery['cntcode'] = 'NULL';
				}
			}

			// Création d'un cache interne à la classe
			$key = Template::cache( 'key:getport:'.http_build_query([$cat, $prd]).$adr_delivery['country'].':noforce-'.($noforce ? '0' : '1').':asset-1' );

			$order_id = $Cart->getID();
			$order_weight = $Cart->getWeight();

			// Charge un tableau contenant les services de livraison offerts pour les promotions appliquées sur la commande
			$ar_srv_free = [];

			// Récupère les promotions appliquées sur la commande
			$pmts = ord_orders_promotions_get( $order_id );
			if (is_array($pmts) && !empty($pmts)) {
				foreach($pmts as $pmt){
					$code_services = pmt_codes_get_services( $pmt['pmt_id'] );

					if( is_array($code_services) && count($code_services) ){
						$ar_srv_free = array_merge( $ar_srv_free, $code_services );
					}
				}
			}

			// Récupère les promotions appliquées sur les lignes de commandes
			$r_prd_cod = ord_products_promotions_get( $order_id );
			if( ria_mysql_num_rows($r_prd_cod) ){
				while( $prd_cod = ria_mysql_fetch_assoc($r_prd_cod) ){
					$code_services = pmt_codes_get_services( $prd_cod['cod_id'] );

					if( is_array($code_services) && count($code_services) ){
						$ar_srv_free = array_merge( $ar_srv_free, $code_services );
					}
				}
			}

			// Récupère les services de livraison pour lesquels les FDP sont offerts
			$all_discount = pmt_codes_get( null, null, true, [_PMT_TYPE_REMISE]);
			while($pmt = ria_mysql_fetch_assoc($all_discount) ){

				if( !isset($pmt['free_shipping']) || !$pmt['free_shipping'] ){
					continue;
				}
				$pmt_services = pmt_codes_get_services($pmt['id']);

				if( !is_array($pmt_services) || !count($pmt_services) ){
					continue;
				}
				$apply = pmt_codes_is_applicable(0, $order_id, $pmt['id'], false, false, $pmt);

				if( !is_bool($apply) || !$apply ){
					continue;
				}

				if( !pmt_code_conditions_apply($pmt['id'], $order_id, false, false, $pmt) ){
					continue;
				}
				$ar_srv_free = array_merge( $ar_srv_free, $pmt_services );
			}

			if( !array_key_exists($key, $this->cache) ){
				$ar_services = new Collection();

				$ar_cache_services_get = [];
				$zipcode = '';

				if( iszipcode($adr_delivery['zipcode']) ){
					$zipcode = substr($adr_delivery['zipcode'], 0, 2);

					if( in_array($zipcode, [97, 98]) ){
						$zipcode = substr($adr_delivery['zipcode'], 0, 3);
					}
				}

				if( in_array(strtoupper2($adr_delivery['country']), ['FR_FRANCE', 'FR-FRANCE', 'FR - FRANCE']) ){
					$adr_delivery['country'] = 'FRANCE';
					$adr_delivery['cntcode'] = 'FR';
				}

				$data_delivery = [
					_ZONE_DPT_FRANCE	=> [ 'code' => $zipcode, 'name' => '' ],
					_ZONE_ZIPCODES		=> [ 'code' => '', 'name' => $adr_delivery['zipcode'] ],
					_ZONE_INSEE			=> [ 'code' => '', 'name' => $adr_delivery['city'] ],
					_ZONE_RGN_FRANCE	=> [ 'code'	=> '', 'name' => isset($adr_delivery['region']) ? $adr_delivery['region'] : ''],
					_ZONE_PAYS			=> [ 'code' => $adr_delivery['cntcode'], 'name' => $adr_delivery['country'] ],
				];

				// Chargement seulement si des services de livraison sont définis
				if( $this->services !== null && $this->services->length() > 0 ){
					// Charge les grilles de frais de port
					$r_pack = dlv_package_prices_get( null, null, null, null, null, null, null, array('value_min'=>'desc'));

					// Charge dans un tableau les grilles de frais de port configurer pour chaque zone géographique
					$ar_package_price = [];
					$r_temp = dlv_package_price_zones_get();
					if( $r_temp ){
						while( $temp = ria_mysql_fetch_assoc($r_temp) ){
							if( !isset($ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ]) ){
								$ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ] = [];
							}

							$ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ][] = $temp;
						}
					}

					// Charge un tableau du tarif à zéro pour chaque zone
					$ar_calc = [];

					$r_calc = dlv_zones_get( 0, true, 0, $config['wst_id'] );
					if( $r_calc ){
						while( $calc = ria_mysql_fetch_assoc($r_calc) ){

							$ar_calc[ $calc['id'] ] = $calc;

							// Calcul la valeur minimal pour un tarif à zéro (franco)
							$res = ria_mysql_query('
								select dpp_value_min
								from dlv_package_price_zones
									join dlv_package_prices on (dpp_tnt_id = '.$config['tnt_id'].' and dpp_id = ppz_dpp_id)
								where ppz_tnt_id = '.$config['tnt_id'].'
									and ppz_zone_id = '.$calc['id'].'
									and dpp_price_ht = 0
							');

							$ar_calc[ $calc['id'] ]['franco_value_min'] = null;
							if( $res && ria_mysql_num_rows($res) ){
								$r = ria_mysql_fetch_assoc( $res );
								$ar_calc[ $calc['id'] ]['franco_value_min'] = $r['dpp_value_min'];
							}
						}
					}
				}

				if( $this->services !== null ){
					// Recherche des zones correspondantes aux lieux de livraison
					// Le traitement est différents selon si le pays est France ou non
					// Hors France, les zones ne peuvent porter que sur le pays
					if( strtolower($data_delivery[_ZONE_PAYS]['name']) == 'france' ){
						foreach( $data_delivery as $type=>$search ){
							if( trim($search['code']) == '' && trim($search['name']) == '' ){
								continue;
							}

							$r_zone = sys_zones_get( 0, $search['code'], $search['name'], false, 0, '', $type, [], -1, -1, false );
							if( $r_zone ){
								while( $zone = ria_mysql_fetch_assoc($r_zone) ){
									$ar_zones[] = $zone['id'];
								}
							}
						}
					}elseif( trim($data_delivery[_ZONE_PAYS]['name']) != '' ){
						// Recherche de l'identifiant de la zone Pays
						// $r_zone = sys_zones_get( 0, '', $data_delivery[_ZONE_PAYS]['name'], false, 0, '', _ZONE_PAYS, [], -1, -1, false );
						$r_zone = sys_zones_get( 0, $data_delivery[_ZONE_PAYS]['code'], '', false, 0, '', _ZONE_PAYS, [], -1, -1, false );
						if( $r_zone ){
							while( $zone = ria_mysql_fetch_assoc($r_zone) ){
								$ar_zones[] = $zone['id'];
							}
						}
					}

					if( isset($ar_zones) && is_array($ar_zones) ){
						// On conserve un tableau d'identifiant unique
						$ar_zones = array_unique($ar_zones);
						$ar_zones_p = [];

						foreach( $ar_zones as $zone_id ){
							$r_zone = sys_zones_parents_get($zone_id);
							$ar_tmp_zone = [];

							if ( ria_mysql_num_rows($r_zone)) {
								while($zo = ria_mysql_fetch_assoc($r_zone)){
									$ar_tmp_zone[] = $zo['id'];
								}
							}
							$ar_tmp_zone[] = $zone_id;
							// Récupère toutes les zones parent de la zone de livraison sélectionnée, de la plus précise à la moins précise
							$ar_tmp_zone = array_reverse($ar_tmp_zone);
							$ar_zones_p[$zone_id] = $ar_tmp_zone;
						}
					}

					foreach( $this->services->getAll() as $service ){
						if( $service['weight_min'] > 0 && $order_weight < $service['weight_min'] ){
							continue;
						}

						if( $service['weight_max'] > 0 && $order_weight > $service['weight_max'] ){
							continue;
						}

						$port_val = false;
						$ar_zone_port_vals = [];
						$round = !isset($config['round_port_ref_value']) || $config['round_port_ref_value'];

						if( !ria_mysql_num_rows($r_pack) || !isset($ar_zones_p) || count($ar_zones_p) <= 0 ){
							continue;
						}

						// Initialise un cache pour accélérer le chargement des données
						$ar_cache_package = [];

						foreach( $ar_zones_p as $zone_id => $zone){
							foreach( $zone as $v ){
								while( $pack = ria_mysql_fetch_assoc($r_pack) ){
									//pour chaque tarification répondant au critère
									// Utilisation ou création de l'entrée du cache sur les tarifs d'une zone
									if( !array_key_exists($pack['id'].'-'.$v, $ar_cache_package) ){
										$ar_cache_package[ $pack['id'].'-'.$v ] = [];


										if( !array_key_exists($pack['id'].$v, $ar_package_price) ){
											continue;
										}

										foreach( $ar_package_price[$pack['id'].$v] as $z_data ){
											$ar_cache_package[ $pack['id'].'-'.$v ][] = $z_data;
										}
									}

									if( !count($ar_cache_package[ $pack['id'].'-'.$v ]) ){
										continue;
									}

									foreach( $ar_cache_package[ $pack['id'].'-'.$v ] as $z_data ){
										if( !array_key_exists($z_data['zone_id'], $ar_calc) ){
											continue;
										}

										$zo = $ar_calc[ $z_data['zone_id'] ];

										if( $service['id'] ){
											if( !array_key_exists($service['id'].'..'.$zo['id'], $ar_cache_services_get) ){
												$ar_cache_services_get[ $service['id'].'..'.$zo['id'] ] = ria_mysql_num_rows(
													dlv_services_get( $service['id'], false, $zo['id'] )
												);
											}

											// vérifie si le service de la commande existe pour la zone
											if( $ar_cache_services_get[ $service['id'].'..'.$zo['id'] ] < 1 ){
												continue;
											}
										}

										if( $this->cart === null || !$this->cart->getID() ){
											$value = 1;
										}else{
											switch($zo['type']){
												case 'qte':
													$value = $this->cart->getAllQuantity();
													break;
												case 'weight':
													$value = ord_orders_weight_get( $this->cart->getID() , 'kg', false, $prd, $zo['id'], $round, $cat);
													break;
												case 'weight_net':
													$value = ord_orders_weight_get( $this->cart->getID() ,'kg', true, $prd, $zo['id'], $round, $cat);
													break;
												case 'HT':
													if( !$noforce && isset($this->options['force_ht']) && is_numeric($this->options['force_ht']) ){
														$value = $this->options['force_ht'];
													}else{
														$value = $this->cart->getWithoutPort();
													}
													break;
												case 'TTC':
													if( !$noforce && isset($this->options['force_ttc']) && is_numeric($this->options['force_ttc']) ){
														$value = $this->options['force_ttc'];
													}else{
														$value = $this->cart->getWithoutPort( true );
													}
													break;
												default:
													$value = -1;
													break;
											}
										}

										if($pack['value_min'] > $value || $value < 0){
											continue;
										}

										$port_val = $pack['price_ht'];

										if( $pack['is_prorata'] && $pack['slice'] && $pack['sl_price']){ //calcul de répartition par tranches
											if(($value - $pack['slice']) > 0 ){
												if(!$pack['is_cumuled']){
													$port_val = 0;
												}else{
													$value -= $pack['value_min'];
												}
												while( ($value - $pack['slice']) > 0 ){
													$value -= $pack['slice'];
													$port_val += $pack['sl_price'];
												}
											}
										}

										if($pack['is_prorata'] && !$pack['slice'] ){ //calcul au prorata proportionnel
											$port_val = ( $pack['price_ht'] * $value ) / $pack['value_min'];
										}

										if (!array_key_exists($zo['id'], $ar_zone_port_vals)){
											$ar_zone_port_vals[$zo['id']] = array(
												'port_val' => parseFloat($port_val),
												'qte' => $value
											);
										}
									}
								}

								ria_mysql_data_seek($r_pack, 0);
							}
						}

						if( !count($ar_zone_port_vals) ){
							continue;
						}

						$result = [
							'port' => null,
							'zone' => 0,
						];

						$rule_multi = 'max';
						if( isset($config['multiple_dlv_rule']) && in_array($config['multiple_dlv_rule'], array('min', 'max', 'sum')) ){
							$rule_multi =  $config['multiple_dlv_rule'];
						}

						switch($rule_multi){
							case 'min' :
								foreach($ar_zone_port_vals as $zone_id => $array_info){
									if( $result['port'] === null || $result['port'] >= $array_info['port_val'] ){
										$result = [
											'port' => $array_info['port_val'],
											'zone' => $ar_calc[ $zone_id ],
										];
									}
								}
								break;
							case 'max' :
								foreach($ar_zone_port_vals as $zone_id => $array_info){
									if( $result['port'] === null || $result['port'] <= $array_info['port_val'] ){
										$result = [
											'port' => $array_info['port_val'],
											'zone' => $ar_calc[ $zone_id ],
										];
									}
								}
								break;
							case 'sum' :
								$result['port'] = 0;
								foreach($ar_zone_port_vals as $zone_id => $array_info){
									$result['port'] += $array_info['port_val'];
									$result['zone'] = $ar_calc[ $zone_id ];
								}
								break;
						}

						if( isset($result) && is_array($result) ){
							$ar_services->addItem( array_merge( $service, $result ), 'srv'.$service['id'] );
						}
					}
				}

				$this->services = $ar_services;
				$this->cache[ $key ] = $ar_services;
			}else{
				$this->services = $this->cache[ $key ];
			}
		}

		// identifiants des services viables
		$ar_srv_available = [ 0 ];

		$temp = new Collection();
		foreach( $this->services->getAll() as $service ){
			if( isset($ar_srv_free) && in_array($service['id'], $ar_srv_free) ){
				$service['port'] = 0;
				$service['zone']['franco_value_min'] = 0;
			}

			if( $service['fld_id'] > 0 && is_numeric($Cart->getFieldValue($service['fld_id'])) ){
				$service['port'] = $Cart->getFieldValue($service['fld_id']);

				$service['zone'] = [
					'dlv_min' => 10,
					'dlv_max' => 10,
					'dlv_step' => 10,
					'dlv_days' => '1, 2, 3, 4, 5',
					'franco_value_min' => 999999999
				];
			}else{
				// Si la zone n'est pas viable par rapport au contenu du panier et ses règgles d'inclusions / exlusions, elle ne sera pas proposée
				if( !self::productIsIncluded($service['zone']) ){
					continue;
				}
			}

			$ar_srv_available[] = $service['id'];
			$temp->addItem( $service, 'srv'.$service['id']);
		}

		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 && $config['tnt_id'] !== 13 ){
			// Réinitialise le service de livraison sur la commande si celui-ci n'est plus valide
			ria_mysql_query('
				update ord_orders
				set ord_srv_id = null
				where ord_tnt_id = '.$config['tnt_id'].'
				and ord_id = '.$_SESSION['ord_id'].'
				and ord_srv_id not in ('.implode( ', ', $ar_srv_available ).')
			');
		}

		$this->services = $temp;
		return $this;
	}

	/** Cette fonction permet de récupérer le détail sur les frais de ports.
	 * 	Elle s'appuit sur le panier en cours
	 * @param	array		$cat		Optionnel, Tableau d'identifiants de catégories pour lesquelles limiter le résultat
	 * @param	array		$prd		Optionnel, Tableau d'identifiants de produits pour lesquels limiter le résultat
	 * @param	int|bool	$ord_id		Optionnel, Surcharge la commande en cours
	 * @param	bool		$calc_vat	Optionnel, Calcul la tva du produit FDP
	 * @return	object	L'objet en cours
	 */
	public function getPort2( $cat=[], $prd=[], $ord_id=false, $calc_vat=false ){

		if(!($this->services instanceof Collection) || $this->services->length() <= 0 ){
			return $this;
		}
		global $config;
	
		$francoAllowed = true;
		if($config['tnt_id'] == 588){
			$francoAllowed = $this->BertonGetAllowedFranco($this->cart->getData());
		}
		// Identifiant de la commande en cours
		if(is_numeric($ord_id) && $ord_id > 0){
			$Cart = new OrderService([
				'ord'		=> $ord_id,
				'status'	=> [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]
			]);
			$Cart->products();

		}else{
			$Cart = CartService::getInstance();
		}

		if( !$Cart->exists() ){
			return $this;
		}
		if($Cart instanceof CartService){
			$Cart->reload();
		}
		$this->cart = $Cart;
		$Cart->fields();

		$adr_delivery = $this->defaultLocation;

		if($this->cart instanceof CartService || $this->cart instanceof OrderService){
			$this->cart->address();
			$cartdata = $this->cart->getData();
			$temp = $cartdata['adrdelivery'];

			if( ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $temp) ){
				$adr_delivery = $temp;
			}

			if( trim($adr_delivery['country']) == '' ){
				$temp = $cartdata['adrinvoice'];

				if( ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $temp) ){
					$adr_delivery = $temp;
				}
			}
		}

		$order_id = $Cart->getID();
		$order_weight = $Cart->getWeight();

		// Charge un tableau contenant les services de livraison offerts pour les promotions appliquées sur la commande
		$ar_srv_free = [];

		// Récupère les promotions appliquées sur la commande
		$pmts = ord_orders_promotions_get( $order_id );
		if (is_array($pmts) && !empty($pmts)) {
			foreach($pmts as $pmt){
				$code_services = pmt_codes_get_services( $pmt['pmt_id'] );

				if( is_array($code_services) && count($code_services) ){
					$ar_srv_free = array_merge( $ar_srv_free, $code_services );
				}
			}
		}

		// Récupère les promotions appliquées sur les lignes de commandes
		$r_prd_cod = ord_products_promotions_get( $order_id );
		if( ria_mysql_num_rows($r_prd_cod) ){
			while( $prd_cod = ria_mysql_fetch_assoc($r_prd_cod) ){
				
				if($config['tnt_id'] == 588 && $prd_cod['cod_id']==12705 && !$francoAllowed){
					continue;
				}
				$code_services = pmt_codes_get_services( $prd_cod['cod_id'] );

				if( is_array($code_services) && count($code_services) ){
					$ar_srv_free = array_merge( $ar_srv_free, $code_services );
				}
			}
		}

		// Récupère les services de livraison pour lesquels les FDP sont offerts
		$all_discount = pmt_codes_get( null, null, true, [_PMT_TYPE_REMISE]);
		while($pmt = ria_mysql_fetch_assoc($all_discount) ){

			if($config['tnt_id'] == 588 && $pmt['id']==12705 && !$francoAllowed){
				continue;
			}
			if( !isset($pmt['free_shipping']) || !$pmt['free_shipping'] ){
				continue;
			}
			$pmt_services = pmt_codes_get_services($pmt['id']);

			if( !is_array($pmt_services) || !count($pmt_services) ){
				continue;
			}
			$apply = pmt_codes_is_applicable(0, $order_id, $pmt['id'], false, false, $pmt);

			if( !is_bool($apply) || !$apply ){
				continue;
			}

			if( !pmt_code_conditions_apply($pmt['id'], $order_id, false, false, $pmt) ){
				continue;
			}
			$ar_srv_free = array_merge( $ar_srv_free, $pmt_services );
		}
		$ar_services = new Collection();

		$ar_cache_services_get = [];
		$zipcode = '';

		if( iszipcode($adr_delivery['zipcode']) ){
			$zipcode = substr($adr_delivery['zipcode'], 0, 2);

			if( in_array($zipcode, [97, 98]) ){
				$zipcode = substr($adr_delivery['zipcode'], 0, 3);
			}
		}

		if( in_array(strtoupper2($adr_delivery['country']), ['FR_FRANCE', 'FR-FRANCE', 'FR - FRANCE']) ){
			$adr_delivery['country'] = 'FRANCE';
			$adr_delivery['cntcode'] = 'FR';
		}

		$data_delivery = [
			_ZONE_DPT_FRANCE	=> [ 'code' => $zipcode, 'name' => '' ],
			_ZONE_ZIPCODES		=> [ 'code' => '', 'name' => $adr_delivery['zipcode'] ],
			_ZONE_INSEE			=> [ 'code' => '', 'name' => $adr_delivery['city'] ],
			_ZONE_RGN_FRANCE	=> [ 'code'	=> '', 'name' => isset($adr_delivery['region']) ? $adr_delivery['region'] : ''],
			_ZONE_PAYS			=> [ 'code' => $adr_delivery['cntcode'], 'name' => $adr_delivery['country'] ],
		];

		// Charge les grilles de frais de port
		$r_pack = dlv_package_prices_get( null, null, null, null, null, null, null, array('value_min'=>'desc'));
		$ar_pack = [];

		if( ria_mysql_num_rows($r_pack) ){
			while( $pack = ria_mysql_fetch_assoc($r_pack) ){
				$ar_pack[] = $pack;
			}
		}

		// Charge dans un tableau les grilles de frais de port configurer pour chaque zone géographique
		$r_temp = dlv_package_price_zones_get();
		$ar_package_price = [];

		if( ria_mysql_num_rows($r_temp) ){
			while( $temp = ria_mysql_fetch_assoc($r_temp) ){
				if( !isset($ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ]) ){
					$ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ] = [];
				}

				$ar_package_price[ $temp['dpp_id'].$temp['dzn_id'] ][] = $temp;
			}
		}


		// Charge un tableau du tarif à zéro pour chaque zone
		$r_calc = dlv_zones_get( 0, true, 0, $config['wst_id'] );
		$ar_calc = [];
		$ar_calc_ids = [];

		if( ria_mysql_num_rows($r_calc) ){
			while( $calc = ria_mysql_fetch_assoc($r_calc) ){
				$ar_calc_ids[] = $calc['id'];
				$ar_calc[ $calc['id'] ] = $calc;
			}
		}

		if(count($ar_calc_ids) > 0 ){
			// Calcul la valeur minimal pour un tarif à zéro (franco)
			$res = ria_mysql_query('
				select
					ppz_zone_id as zone_id,
					dpp_value_min as value_min
				from dlv_package_price_zones
				join dlv_package_prices
				on
					dpp_tnt_id = '.$config['tnt_id'].'
				and dpp_id = ppz_dpp_id
				where
					ppz_tnt_id = '.$config['tnt_id'].'
				and ppz_zone_id in ('.implode(',', $ar_calc_ids).')
				and dpp_price_ht = 0
			');

			if(ria_mysql_num_rows($res)){
				while($r = ria_mysql_fetch_assoc($res)){
					$ar_calc[$r['zone_id']]['franco_value_min'] = is_numeric($r['value_min']) ? $r['value_min'] : null;
				}
			}
		}


		// Recherche des zones correspondantes aux lieux de livraison
		// Le traitement est différents selon si le pays est France ou non
		// Hors France, les zones ne peuvent porter que sur le pays
		if( strtolower($data_delivery[_ZONE_PAYS]['name']) == 'france' ){
			foreach( $data_delivery as $type=>$search ){
				if( trim($search['code']) == '' && trim($search['name']) == '' ){
					continue;
				}

				$r_zone = sys_zones_get( 0, $search['code'], $search['name'], false, 0, '', $type, [], -1, -1, false );
				if( $r_zone ){
					while( $zone = ria_mysql_fetch_assoc($r_zone) ){
						$ar_zones[] = $zone['id'];
					}
				}
			}
		}elseif( trim($data_delivery[_ZONE_PAYS]['name']) != '' ){
			// Recherche de l'identifiant de la zone Pays
			// $r_zone = sys_zones_get( 0, '', $data_delivery[_ZONE_PAYS]['name'], false, 0, '', _ZONE_PAYS, [], -1, -1, false );
			$r_zone = sys_zones_get( 0, $data_delivery[_ZONE_PAYS]['code'], '', false, 0, '', _ZONE_PAYS, [], -1, -1, false );
			if( $r_zone ){
				while( $zone = ria_mysql_fetch_assoc($r_zone) ){
					$ar_zones[] = $zone['id'];
				}
			}
		}
		$has_packages = is_array($ar_pack) && count($ar_pack) > 0 && is_array($ar_package_price) && count($ar_package_price) > 0;

		if( isset($ar_zones) && is_array($ar_zones) && $has_packages ){
			// On conserve un tableau d'identifiant unique
			$ar_zones = array_unique($ar_zones);
			$ar_zones_p = [];

			foreach( $ar_zones as $zone_id ){
				$r_zone = sys_zones_parents_get($zone_id);
				$ar_tmp_zone = [];

				if ( ria_mysql_num_rows($r_zone)) {
					while($zo = ria_mysql_fetch_assoc($r_zone)){
						$ar_tmp_zone[] = $zo['id'];
					}
				}
				$ar_tmp_zone[] = $zone_id;
				// Récupère toutes les zones parent de la zone de livraison sélectionnée, de la plus précise à la moins précise
				$ar_tmp_zone = array_reverse($ar_tmp_zone);
				$ar_tmp_zone_2 = [];

				foreach($ar_tmp_zone as $v){

					if( !$has_packages ){
						$ar_tmp_zone_2[$v] = false;
						continue;
					}
					foreach($ar_pack as $pack){
						if( !array_key_exists($pack['id'].$v, $ar_package_price) ){
							continue;
						}

						foreach( $ar_package_price[$pack['id'].$v] as $z_data ){
							$ar_cache_package[ $pack['id'].'-'.$v ][] = $z_data;
						}

					}
				}
				$ar_zones_p[$zone_id] = $ar_tmp_zone;
			}


		}

		foreach( $this->services->getAll() as $service ){
			if( $service['weight_min'] > 0 && $order_weight < $service['weight_min'] ){
				continue;
			}

			if( $service['weight_max'] > 0 && $order_weight > $service['weight_max'] ){
				continue;
			}

			$port_val = false;
			$ar_zone_port_vals = [];
			$round = !isset($config['round_port_ref_value']) || $config['round_port_ref_value'];

			if( !ria_mysql_num_rows($r_pack) || !isset($ar_zones_p) || count($ar_zones_p) <= 0 ){
				continue;
			}
			// Initialise un cache pour accélérer le chargement des données
			$ar_cache_package = [];

			foreach( $ar_zones_p as $zone_id => $zone){
				foreach( $zone as $v ){
					while( $pack = ria_mysql_fetch_assoc($r_pack) ){
						//pour chaque tarification répondant au critère
						// Utilisation ou création de l'entrée du cache sur les tarifs d'une zone
						if( !array_key_exists($pack['id'].'-'.$v, $ar_cache_package) ){
							$ar_cache_package[ $pack['id'].'-'.$v ] = [];


							if( !array_key_exists($pack['id'].$v, $ar_package_price) ){
								continue;
							}

							foreach( $ar_package_price[$pack['id'].$v] as $z_data ){
								$ar_cache_package[ $pack['id'].'-'.$v ][] = $z_data;
							}
						}

						if( !count($ar_cache_package[ $pack['id'].'-'.$v ]) ){
							continue;
						}

						foreach( $ar_cache_package[ $pack['id'].'-'.$v ] as $z_data ){
							if( !array_key_exists($z_data['zone_id'], $ar_calc) ){
								continue;
							}

							$zo = $ar_calc[ $z_data['zone_id'] ];

							if( $service['id'] ){
								if( !array_key_exists($service['id'].'..'.$zo['id'], $ar_cache_services_get) ){
									$ar_cache_services_get[ $service['id'].'..'.$zo['id'] ] = ria_mysql_num_rows(
										dlv_services_get( $service['id'], false, $zo['id'] )
									);
								}

								// vérifie si le service de la commande existe pour la zone
								if( $ar_cache_services_get[ $service['id'].'..'.$zo['id'] ] < 1 ){
									continue;
								}
							}

							if( $this->cart === null || !$this->cart->getID() ){
								$value = 1;
							}else{
								switch($zo['type']){
									case 'qte':
										$value = $this->cart->getAllQuantity();
										break;
									case 'weight':
										$weight = $this->cart->getWeight();
										$value = $weight;
										// $value = $round ? round($weight/1000) : $weight/1000;
										// $value = ord_orders_weight_get( $this->cart->getID() , 'kg', false, $prd, $zo['id'], $round, $cat);
										break;
									case 'weight_net':
										$weight = $this->cart->getWeight(false, true);
										$value = $weight;
										// $value = $round ? round($weight/1000) : $weight/1000;
										// $value = ord_orders_weight_get( $this->cart->getID() ,'kg', true, $prd, $zo['id'], $round, $cat);
										break;
									case 'HT':
										$value = $this->cart->getWithoutPort();
										break;
									case 'TTC':
										$value = $this->cart->getWithoutPort( true );
										break;
									default:
										$value = -1;
										break;
								}
							}

							if($pack['value_min'] > $value || $value < 0){
								continue;
							}

							$port_val = $pack['price_ht'];

							if( $pack['is_prorata'] && $pack['slice'] && $pack['sl_price']){ //calcul de répartition par tranches
								if(($value - $pack['slice']) > 0 ){
									if(!$pack['is_cumuled']){
										$port_val = 0;
									}else{
										$value -= $pack['value_min'];
									}
									while( ($value - $pack['slice']) > 0 ){
										$value -= $pack['slice'];
										$port_val += $pack['sl_price'];
									}
								}
							}

							if($pack['is_prorata'] && !$pack['slice'] ){ //calcul au prorata proportionnel
								$port_val = ( $pack['price_ht'] * $value ) / $pack['value_min'];
							}

							if (!array_key_exists($zo['id'], $ar_zone_port_vals)){
								$ar_zone_port_vals[$zo['id']] = array(
									'port_val' => parseFloat($port_val),
									'qte' => $value
								);
							}
						}
					}

					ria_mysql_data_seek($r_pack, 0);
				}
			}

			if( !count($ar_zone_port_vals) ){
				continue;
			}

			$result = [
				'port'		=> null,
				'portttc'	=> null,
				'tax_rate'	=> _TVA_RATE_DEFAULT,
				'zone'		=> 0,
			];

			$rule_multi = 'max';
			if( isset($config['multiple_dlv_rule']) && in_array($config['multiple_dlv_rule'], array('min', 'max', 'sum')) ){
				$rule_multi =  $config['multiple_dlv_rule'];
			}

			switch($rule_multi){
				case 'min' :
					foreach($ar_zone_port_vals as $zone_id => $array_info){
						if( $result['port'] === null || $result['port'] >= $array_info['port_val'] ){
							$result['port'] = $array_info['port_val'];
							$result['zone'] = $ar_calc[ $zone_id ];
						}
					}
					break;
				case 'max' :
					foreach($ar_zone_port_vals as $zone_id => $array_info){
						if( $result['port'] === null || $result['port'] <= $array_info['port_val'] ){
							$result['port'] = $array_info['port_val'];
							$result['zone'] = $ar_calc[$zone_id];
						}
					}
					break;
				case 'sum' :
					$result['port'] = 0;
					foreach($ar_zone_port_vals as $zone_id => $array_info){
						$result['port'] += $array_info['port_val'];
						$result['zone'] = $ar_calc[ $zone_id ];
					}
					break;
			}

			if(is_numeric($result['port']) && $result['port'] > 0){
				$result['portttc'] = $result['port'] * _TVA_RATE_DEFAULT;
			}

			$ar_services->addItem( array_merge( $service, $result ), 'srv'.$service['id'] );
		}

		$this->services = $ar_services;

		// identifiants des services viables
		$ar_srv_available = [ 0 ];

		$temp = new Collection();
		$calc_vat = is_bool($calc_vat) && $calc_vat;
		$ar_vat = [];
		$cnt_code = null;

		if($calc_vat){
			$cnt_code = gu_users_get_cnt_code($Cart->getUserID(), false, $Cart->getID());
			$cnt_code = str_replace( '"', '', $cnt_code );
		}

		foreach( $this->services->getAll() as $service ){
			if( in_array($service['id'], $ar_srv_free) ){
				$service['port'] = 0;
				$service['zone']['franco_value_min'] = 0;
			}

			if( $service['fld_id'] > 0 && is_numeric($Cart->getFieldValue($service['fld_id'])) ){
				$service['port'] = $Cart->getFieldValue($service['fld_id']);
			}else{
				// Si la zone n'est pas viable par rapport au contenu du panier et ses règles d'inclusions / exlusions, elle ne sera pas proposée
				if( !self::productIsIncluded($service['zone']) ){
					continue;
				}
			}

			if($calc_vat){
				$has_vat = array_key_exists($service['prd'], $ar_vat);
				$vat = $has_vat ? $ar_vat[$service['prd']] : _TVA_RATE_DEFAULT;

				if(!$has_vat && !is_null($cnt_code)){
					$rptva = ria_mysql_query('
						select
							prd.prd_id as id,
							ptv.ptv_tva_rate as tva_rate
						from
							prd_products prd
						inner join
							prc_tvas ptv
						on
								ptv.ptv_date_deleted is null
							and ptv.ptv_tnt_id='.$config['tnt_id'].'
							and ptv.ptv_prd_id=prd.prd_id
							and ptv.ptv_cnt_code="'.htmlspecialchars($cnt_code).'"
						where
								prd.prd_tnt_id='.$config['tnt_id'].'
							and prd.prd_ref="'.$service['prd'].'"
						limit 1
					');

					if( ria_mysql_num_rows($rptva) ){
						$ptva = ria_mysql_fetch_assoc($rptva);
						$vat = $ptva['tva_rate'];
						$ar_vat[$service['prd']] = $vat;
					}
				}
				$service['tax_rate'] = $vat;
				$service['portttc'] = is_numeric($service['port']) && $service['port'] > 0 ? $service['port'] * $vat : 0;
			}

			$ar_srv_available[] = $service['id'];
			$temp->addItem( $service, 'srv'.$service['id']);
		}

		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
			// Réinitialise le service de livraison sur la commande si celui-ci n'est plus valide
			ria_mysql_query('
				update ord_orders
				set ord_srv_id = null
				where ord_tnt_id = '.$config['tnt_id'].'
				and ord_id = '.$_SESSION['ord_id'].'
				and ord_srv_id not in ('.implode( ', ', $ar_srv_available ).')
			');
		}

		$this->services = $temp;

		return $this;
	}

	public function BertonGetAllowedFranco($cart){
		if(is_array($cart) && isset($cart['prds']) && is_array($cart['prds']) && count($cart['prds'])){
			foreach($cart['prds'] as $prd){
				$res = pmt_products_get(12705,$prd['prd']['id']);
				while($result = ria_mysql_fetch_assoc($res)){
					if($result['include'] == 0){
						return false;
					}
				}
			}
		}
	
		return true;
	}

	/** Cette fonction permet de calculer le franco de port applicable.
	 * 	@param ProductService $product Optionnel, permet de passer un objet ProductService
	 * 	@return array Un tableau vide si aucun franco de port n'est applicable
	 * 	@return array Un tableau contenant les informations de franco :
	 * 			- type : sur quoi est calculer le franco (HT, TTC, qte, weight, weight_net)
	 * 			- amount : montant du franco (dépendant du type donc cela peut-être un poids, une quantité ou un montant HT/TTC)
	 * 			- actual : montant actuel (dépendant du type donc cela peut-être un poids, une quantité ou un montant HT/TTC)
	 */
	public function getFranco( $product=null ){
		global $config;

		$this->getPort();

		$srv_in_cart = null;
		if( $this->cart !== null ){
			$srv_in_cart = ord_orders_get_dlv_service( $this->cart->getID() );
			if( $srv_in_cart === false ){
				$srv_in_cart = null;
			}
		}

		$orderby_type = [
			'HT' 			=> 0,
			'TTC' 			=> 1,
			'weight_net' 	=> 2,
			'weight' 		=> 3,
			'qte' 			=> 4,
		];

		$ar_franco = [];

		// On parcours tous les services de livraison elligible
		foreach( $this->services->getAll() as &$one_service ){
			// Si aucune zone de livraison, on ne vas pas plus loin
			if( !isset($one_service['zone']['id']) ){
				continue;
			}

			// On ne vas pas plus loin si le service ne correspond pas à celui présent sur le panier en cours
			// Seulement si un service est déjà choisi
			if( $srv_in_cart !== null && $one_service['id'] != $srv_in_cart ){
				continue;
			}

			// Un ordre est appliqué en fonction du type de calcul
			$pos = null;
			if( isset($one_service['zone']['type']) && array_key_exists( $one_service['zone']['type'], $orderby_type) ){
				$pos = $orderby_type[ $one_service['zone']['type'] ];
			}

			if( $pos === null ){
				continue;
			}

			if( !isset($one_service['zone']['franco_value_min']) || !is_numeric($one_service['zone']['franco_value_min']) || $one_service['zone']['franco_value_min'] < 0 ){
				continue;
			}

			$ar_franco[] = [
				'type' => $one_service['zone']['type'],
				'pos' => $pos,
				'amount' => parseFloat( $one_service['zone']['franco_value_min'] ),
			];
		}

		// On ne vas pas plus loin si aucun franco
		if( !count($ar_franco) ){
			return [];
		}


		// Tri les franco possible par position puis pas montant
		$ar_franco = array_msort( $ar_franco, ['pos' => SORT_ASC, 'amount' => SORT_ASC] );

		// On retourne en résultat la première ligne du tableau des francos possible
		// L'information "pos" est supprimée
		$ar_franco = array_shift( $ar_franco );
		unset($ar_franco['pos']);

		// Récupère l'information actuelle du panier
		$value = null;
		if( $this->cart !== null && $this->cart->getID() ){
			$round = !isset($config['round_port_ref_value']) || $config['round_port_ref_value'];

			switch( $ar_franco['type'] ){
				case 'qte':
					$value = $this->cart->getAllQuantity();

					if( is_a($product, 'ProductService') ){
						$value += $product->getSellUnit();
					}
					break;
				case 'weight':
					$value = ord_orders_weight_get( $this->cart->getID() , 'kg', false, [], '', $round );

					if( is_a($product, 'ProductService') ){
						$value += $product->getSellUnit() * $product->getWeight();
					}
					break;
				case 'weight_net':
					$value = ord_orders_weight_get( $this->cart->getID() ,'kg', true, [], '', $round );

					if( is_a($product, 'ProductService') ){
						$value += $product->getSellUnit() * $product->getWeight();
					}
					break;
				case 'HT':
					$value = $this->cart->getWithoutPort();

					if( is_a($product, 'ProductService') ){
						$value += $product->getSellUnit() * $product->getPriceHT();
					}
					break;
				case 'TTC':
					$value = $this->cart->getWithoutPort( true );

					if( is_a($product, 'ProductService') ){
						$value += $product->getSellUnit() * $product->getPriceHT();
					}
					break;
			}
		}

		$ar_franco['actual'] = parseFloat( $value );
		return $ar_franco;
	}

	/**	Cette méthode permet de calculer les dates de livraison
	 * @param	bool	$shipping	Optionnel, True pour charger les frais de port
	 * @param	array	$data		Optionnel, Tableau de données libres
	 * @return	DeliveryService	L'objet en cours
	 */
	public function getDates($shipping=true, $data=[]){

		global $hook;
		global $config;

		// Les service ne sont chargés qu'une seule fois
		if( $this->services === null ){
			$this->services();
		}

		// Si aucun service (tableau vide), alors c'est qu'aucun service n'est élligible
		// Notamment lorsque la fonction est appelée pour une commande ou un produit et qu'aucun service n'est élligible
		if( $this->services === null || !$this->services->length() ){
			return $this;
		}

		// Charge les informations sur les livraisons possibles
		if( is_bool($shipping) && $shipping ){
			$this->getPort();
		}
		// Liste des jours fériés
		$all_holidays = ['1_1', '1_5', '8_5', '14_7', '15_8', '1_11', '11_11', '25_12'];
		$data = is_array($data) ? $data : [];

		// Année en cours
		$current_year = date('Y');

		$rhld = dlv_holidays_get($current_year, true);
		$dispatch = [];
		$holidays = [];

		if (ria_mysql_num_rows($rhld)) {
			while ($hld = ria_mysql_fetch_assoc($rhld)) {
				$Date = new DateTime($hld['date']);
				$dispatch[] = $Date->format('j_n');
			}
		}

		// Pâques
		$easter = new Datetime(date('Y-m-d', easter_date($current_year)));
		$all_holidays[] = $easter->format('j_n');

		// Lundi de Pâques
		$temp = clone $easter;
		$all_holidays[] = $temp->modify('+1 day')->format('j_n');

		// Ascension
		$temp = clone $easter;
		$all_holidays[] = $temp->modify('+39 days')->format('j_n');

		// Pentecote
		$temp = clone $easter;
		$all_holidays[] = $temp->modify('+50 days')->format('j_n');

		foreach ($all_holidays as $holiday) {
			if (in_array($holiday, $dispatch)) {
				continue;
			}
			$holidays[] = $holiday;
		}

		// Fermetures exceptionnelles site
		$rclosing = dlv_events_get(0, 2);

		if (ria_mysql_num_rows($rclosing)) {

			while ($closing = ria_mysql_fetch_assoc($rclosing)) {
				$Start = new DateTime($closing['start_en']);
				$End = new DateTime($closing['end_en']);

				if ($Start > $End) {
					continue;
				}

				while ($Start < $End) {
					$holidays[] = $Start->format('j_n');

					$Start->modify('+1 day');
				}
			}
		}

		// Fermetures exceptionnelles magasin sélectionné si existant
		if($this->cart->exists()){
			$cart = $this->cart->getData();

			if(is_numeric($cart['strid']) && $cart['strid'] > 0){
				$data['store'] = $cart['strid'];
				$rcstr = dlv_events_get(0, 2, $cart['strid']);

				if (ria_mysql_num_rows($rcstr)) {

					while ($cstr = ria_mysql_fetch_assoc($rcstr)) {
						$Start = new DateTime($cstr['start_en']);
						$End = new DateTime($cstr['end_en']);

						if ($Start > $End) {
							continue;
						}

						while ($Start < $End) {
							$holidays[] = $Start->format('j_n');

							$Start->modify('+1 day');
						}
					}
				}
			}
		}

		$holidays = $hook->apply_filter('DeliveryService_overridesDispatchClosingDates', $holidays, $data);
		usort($holidays, function($a, $b){
			$ar_a = explode('_', $a);
			$ar_b = explode('_', $b);

			if($ar_a[1] == $ar_b[1]){
				return ($ar_a[0] < $ar_b[0]) ? -1 : 1;
			}
			return $ar_a[1] < $ar_b[1] ? -1 : 1;
		});
		$data['holidays'] = $holidays;

		$ar_services = new Collection();

		foreach( $this->services->getAll() as $delivery ){
			if( !array_key_exists('zone', $delivery) ){
				continue;
			}

			if( $delivery['zone']['dlv_max'] === null ){
				$delivery['zone']['dlv_max'] = 1;
			}

			$date_min = new DateTime();
			$date_max = new DateTime();

			$date_min = $hook->apply_filter('DeliveryService_overridesBaseDateMin', $date_min, $data);
			$date_max = $hook->apply_filter('DeliveryService_overridesBaseDateMax', $date_max, $data);

			$day_livr_min = $delivery['zone']['dlv_min'];
			$day_livr_max = $delivery['zone']['dlv_max'];

			// Si l'heure limite de commande est dépassée, on rajoute une journée
			if( is_numeric($delivery['zone']['dlv_step']) && $date_min->format('H') >= $delivery['zone']['dlv_step'] ){
				$day_livr_min++;
				$day_livr_max++;
			}

			$exp_days = explode( ', ', $delivery['zone']['dlv_days'] );

			// On rajoute autant de jour qu'il faut tant qu'on a pas trouvé une journée d'expédition
			// ex. Si aujourd'hui, il n'y a pas d'expédition, on regarde demain
			$t = 0;
			while(($t++) < 7){

				if( !in_array($date_min->format('N'), $exp_days) ){
					$date_min->modify('+1 day');
				}

				if( !in_array($date_max->format('N'), $exp_days) ){
					$date_max->modify('+1 day');
				}
			}

			// À partir de là on connait la date d'expédition et le délai minimum de livraison
			// On applique ce délai sur des jours ouvrables et non fériés pour calculer la date minimale de livraison
			while( $day_livr_min > 0 ){
				// Spé Berton : prise en compte du samedi et jours féries en jours non ouvrés pour la livraison
				if(($config['tnt_id'] == 588 || $config['tnt_id'] == 1279) && ($date_min->format('w') == 6 || in_array($date_min->format('j_n'), $holidays))) {
					$date_min->modify('+1 day');
					continue;
				}
				if( $date_min->format('w') == 0) {
				$date_min->modify('+1 day');
					continue;
				}
				$date_min->modify('+1 day');
				$day_livr_min--;
			}

			// On applique ce délai sur des jours ouvrables et non fériés pour calculer la date maximal de livraison
			while( $day_livr_max > 0 ){
				// Spé Berton : prise en compte du samedi et jours féries en jours non ouvrés pour la livraison
				if ($config['tnt_id'] == 588 || $config['tnt_id'] == 1279) {
					if ($date_max->format('w') == 0 || $date_max->format('w') == 6 || in_array($date_max->format('j_n'), $holidays)) {
					$date_max->modify('+1 day');
					continue;
				}
				} else {
					if ($date_max->format('w') == 0) {
				$date_max->modify('+1 day');
						continue;
					}
				}
				$date_max->modify('+1 day');
				$day_livr_max--;
			}

			// On vérifie les jours fériés + vacances
			if(is_array($holidays) && count($holidays) > 0){
				foreach($holidays as $h){
					if($date_min->format('j_n') == $h){
						$date_min->modify('+1 day');
					}

					if($date_max->format('j_n') == $h){
						$date_max->modify('+1 day');
					}
				}
			}

			$delivery['dates'] = [ 'min' => $date_min->format('Y-m-d'), 'max' => $date_max->format('Y-m-d') ];
			// Spé Berton : prise en compte de l'heure pour la disponibilité C&C
			if($config['tnt_id'] == 588 || $config['tnt_id'] == 1279){

				// Vérification des jours fériés, vacances et weekends pour berton avant de renvoyer la date
				if (is_array($holidays) && count($holidays) > 0) {
					
					$isInvalidDate = function($date) use ($holidays) {
						return in_array($date->format('j_n'), $holidays) || $date->format('N') >= 6;
					};

					while ($isInvalidDate($date_min) || $isInvalidDate($date_max)) {
						if ($isInvalidDate($date_min)) {
							$date_min->modify('+1 day');
						}
						if ($isInvalidDate($date_max)) {
							$date_max->modify('+1 day');
						}
					}
				}
				$delivery['dates'] = [ 'min' => $date_min->format('Y-m-d H:m:s'), 'max' => $date_max->format('Y-m-d') ];
			}

			$ar_services->addItem( $delivery, 'srv'.$delivery['id'] );
		}

		$this->services = $ar_services;
		return $this;
	}

	/** Cette méthode permet de calculer les dates min/ max de livraison rapport à la zone de livraison en cours
	 * 	@return array|bool	Tableau des dates min/ max, false sinon
	 */
	public function getDate(){

		if( !is_array($this->zone) ){
			return false;
		}

		if( $this->zone['dlv_max'] === null ){
			$this->zone['dlv_max'] = 1;
		}

		$date_min = new DateTime();
		$date_max = new DateTime();

		// Liste des jours fériés
		$holidays = array('1_1', '1_5', '8_5', '14_7', '15_8', '1_11', '11_11', '25_12');

		// Pâques
		$easter = new Datetime( date('Y-m-d', easter_date($date_min->format('Y'))) );
		$holidays[] = $easter->format('j_n');

		// Lundi de Pâques
		$temp = $easter;
		$holidays[] = $temp->modify('+1 day')->format('j_n');

		// Ascension
		$temp = $easter;
		$holidays[] = $temp->modify('+39 days')->format('j_n');

		// Pentecote
		$temp = $easter;
		$holidays[] = $temp->modify('+50 days')->format('j_n');

		$day_livr_min = $this->zone['dlv_min'];
		$day_livr_max = $this->zone['dlv_max'];

		// Si l'heure limite de commande est dépassée, on rajoute une journée
		if( is_numeric($this->zone['dlv_step']) && $date_min->format('H') >= $this->zone['dlv_step'] ){
			$day_livr_min++;
			$day_livr_max++;
		}

		$exp_days = explode( ', ', $this->zone['dlv_days'] );

		// On rajoute autant de jour qu'il faut tant qu'on a pas trouvé une journée d'expédition
		// ex. Si aujourd'hui, il n'y a pas d'expédition, on regarde demain
		$t = 0;
		while( true ){
			if( ($t++) >=7 ){
				break;
			}

			if( !in_array($date_min->format('w'), $exp_days) ){
				$date_min->modify('+1 day');
			}

			if( !in_array($date_max->format('w'), $exp_days) ){
				$date_max->modify('+1 day');
			}
		}

		// À partir de là on connait la date d'expédition et le délai minimum de livraison
		// On applique se délai sur des jours ouvrables et non fériés pour calculer la date minimale de livraison
		$i = 0;
		while( $day_livr_min > 0 ){
			if( ($i++) >= 30 ){
				break;
			}

			$date_min->modify('+1 day');

			// Si la livraison est faite sur un jour férié, on passe une journée
			if( in_array($date_min->format('j_n'), $holidays) ){
				continue;
			}

			// Si la livraison est faite un dimanche, on passe une jourée
			if( $date_min->format('w') == 0 ){
				continue;
			}

			$day_livr_min--;
		}

		// On applique se délai sur des jours ouvrables et non fériés pour calculer la date maximal de livraison
		$i = 0;
		while( $day_livr_max > 0 ){
			if( ($i++) >= 30 ){
				break;
			}

			$date_max->modify('+1 day');

			// Si la livraison est faite sur un jour férié, on passe une journée
			if( in_array($date_max->format('j_n'), $holidays) ){
				continue;
			}

			// Si la livraison est faite un dimanche, on passe une jourée
			if( $date_max->format('w') == 0 ){
				continue;
			}

			$day_livr_max--;
		}

		$this->zone['dates'] = [ 'min' => $date_min->format('Y-m-d'), 'max' => $date_max->format('Y-m-d') ];

		return $this->zone['dates'];

	}

	/** Cette fonction permet de limitée les services de livraison à un ou plusieurs produits.
	 * 	@param int $prd_id Obligatoire, identifiant de produit
	 * 	@return object L'instance courante
	 */
	public function setProduct( $prd_id ){
		// On contrôle que le produit n'a pas déjà été contrôlé
		if( array_key_exists($prd_id, $this->prds) ){
			return $this;
		}

		// Contrôle que chaque service est éligible pour le produit donné
		if( $this->services !== null ){
			foreach( $this->services->getAll() as $key=>$data ){
				if( !dlv_products_is_available($prd_id, $data['id']) ){
					$this->services->deleteItem( $key );
				}
			}
		}

		$this->prds[ $prd_id ] = $prd_id;

		return $this;
	}

	/** Cette fonction permet de contrôler que les services sont éligible à la commande
	 * 	@return object L'object courant
	 */
	public function controlCart(){
		global $config;

		if( $this->cart == null || !$this->cart->getID() ){
			return $this;
		}
		$cart_svr = $this->cart->getServiceID();

		if( $this->services === null || $this->services->length() == 0 ){
			ord_orders_set_dlv_service( $this->cart->getID(), false );

			// Supprime toutes les références de frais de port du panier
			foreach( $config['dlv_prd_references'] as $prd_port_ref ){
				ord_products_del_ref( $this->cart->getID(), $prd_port_ref );
			}

			return $this;

		}

		foreach( $this->services->getAll() as $key=>$data ){
			if( !ord_prd_services_is_available($this->cart->getID(), $data['id']) ){
				$this->services->deleteItem( $key );

				// Si le service était appliqué sur la commande, il est retiré
				if( $cart_svr == $data['id'] ){
					ord_orders_set_dlv_service( $this->cart->getID(), false );
				}
			}
		}

		return $this;
	}

	/**	Cette méthode permet de vérifier les produits de la commande en fonction du service de livraison et de la zone de livraison
	 * @todo	/!\ La méthode `controlServiceCompatibility` doit être appelée avant celle-ci /!\
	 * @param	bool	$must_delete	Optionnel, True si les produits non compatibles doivent être supprimés
	 * @return	DeliveryService		L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function controlProductsCompatibility($must_delete=true){
		if( $this->cart == null || !$this->cart->getID() ){
			throw new Exception(i18n::get('Aucun panier en cours.', 'DELIVERY'), 93);
		}

		$must_delete = is_bool($must_delete) ? $must_delete : true;
		$cart_id = $this->cart->getID();
		$ar_prds = $this->cart->getProducts();

		if( !is_array($ar_prds) || !count($ar_prds) ){
			throw new Exception(i18n::get('Le panier en cours ne contient pas de produit.', 'DELIVERY'), 94);
		}

		// Si pas de service ou zone
		if( $this->service === null || $this->zone === null ){
			return $this;
		}
		$zone_prds = dlv_zones_products_get($this->zone['id']);

		if( !ria_mysql_num_rows($zone_prds) ){
			return $this;
		}
		$excluded = [];

		while($prd = ria_mysql_fetch_assoc($zone_prds) ){
			// Si le produit est inclus, on passe
			if( isset($prd['include']) && $prd['include'] ){
				continue;
			}
			$excluded[] = $prd['id'];
		}

		if( !count($excluded) ){
			return $this;
		}
		$error = 0;
		$deleted = 0;

		foreach($ar_prds as $data){

			if( !in_array($data['prd']['id'], $excluded) ){
				continue;
			}

			if( $must_delete ){
				if( !ord_products_del($cart_id, $data['prd']['id']) ){
					$error++;
					continue;
				}
			}
			$deleted++;
		}

		if( $error > 1 ){
			throw new Exception( i18n::get('Plusieurs produits ne preuvent être livrés à cette adresse de livraison et n\'ont pu être correctement supprimés.', 'DELIVERY'), 95);

		}elseif( $error === 1 ){
			throw new Exception( i18n::get('Un produit ne peut être livré à cette adresse de livraison et n\'a pu être correctement supprimé.', 'DELIVERY'), 95);

		}

		if( $deleted > 1 ){
			if( $must_delete ){
				throw new Exception( i18n::get('Plusieurs produits ne preuvent être livrés à cette adresse de livraison et ont été supprimés de votre panier.', 'DELIVERY'), 96);
			}else{
				throw new Exception( i18n::get('Plusieurs produits ne preuvent être livrés à cette adresse de livraison.', 'DELIVERY'), 96);
			}
		}elseif( $deleted === 1 ){
			if($must_delete){
				throw new Exception( i18n::get('Un produit ne peut être livré à cette adresse de livraison et a été supprimé de votre panier.', 'DELIVERY'), 96);
			}else{
				throw new Exception( i18n::get('Un produit ne peut être livré à cette adresse de livraison.', 'DELIVERY'), 96);
			}
		}

		return $this;

	}

	/**	Cette méthode permet de vérifier le service de livraison, les zones de livraison si nécessaire
	 * @param	bool	$check_zone	Optionnel, S'il y a nécessité de vérifier la zone de livraison = true, false si non
	 * @param	int		$type		Optionnel, Si $check_zone = true, alors il faut spécifier le type de recherche de zone effectuer
	 * @return	DeliveryService		L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function controlServiceCompatibility($check_zone=false, $type=_ZONE_PAYS){
		global $config;

		if( $this->cart == null || !$this->cart->getID() ){
			throw new Exception(i18n::get('Aucun panier en cours.', 'DELIVERY'), 97);
		}

		$cart_id = $this->cart->getID();
		$cart_svr = $this->cart->getServiceID();

		// Si la commande ne contient qu'une ou plusieurs cartes cadeaux, aucun contrôle de service de livraison n'est effectué
		// car la commande ne sera pas expédiée physiquement, mais seules la ou les cartes cadeaux seront envoyées par mail
		if( ord_orders_not_only_gifts($cart_id) === false ){
			return $this;
		}

		if( !$cart_svr ){
			throw new Exception(i18n::get('Veuillez choisir un service de livraison.', 'DELIVERY'), 98);
		}

		if( !ord_prd_services_is_available($cart_id, $cart_svr) ){
			throw new Exception(i18n::get('Ce service de livraison n\'est pas disponible.', 'DELIVERY'), 99);
		}

		if( !$this->services->length() ){
			throw new Exception(i18n::get('Aucun service disponible pour votre commande.', 'DELIVERY'), 100);
		}
		$services = $this->services->getAll();

		foreach( $services as $data ){

			if( $data['id'] != $cart_svr ){
				continue;
			}
			$service = $data;
			break;
		}

		if( !isset($service) ){
			throw new Exception(i18n::get('Le service sélectionné n\'est pas disponible pour votre commande.', 'DELIVERY'), 101);
		}
		$this->service = $service;
		$check_zone = is_bool($check_zone) ? $check_zone : false;

		if( !$check_zone ){
			return $this;
		}
		$this->cart->address();

		$adr_delivery = $this->cart->getData()['adrdelivery'];

		if( in_array(strtoupper2($adr_delivery['country']), ['FR_FRANCE', 'FR-FRANCE', 'FR - FRANCE']) ){
			$adr_delivery['country'] = 'FRANCE';
			$adr_delivery['cntcode'] = 'FR';
		}

		if( $adr_delivery === null ){
			throw new Exception(i18n::get('Veuillez renseigner l\'adresse de livraison de votre commande.', 'DELIVERY'), 102);
		}
		$zipcode = substr($adr_delivery['zipcode'], 0, 2);

		if( in_array($zipcode, [97, 98]) ){
			$zipcode = substr($adr_delivery['zipcode'], 0, 3);
		}
		$zones = dlv_zones_get(0, true, $cart_svr, $config['wst_id']);


		if( !ria_mysql_num_rows($zones) ){
			throw new Exception(i18n::get('Le service sélectionné n\'est pas disponible pour cette adresse de livraison.', 'DELIVERY'), 103);
		}
		$type = in_array($type, [_ZONE_DPT_FRANCE, _ZONE_RGN_FRANCE, _ZONE_ZIPCODES, _ZONE_INSEE ]) ? $type : _ZONE_PAYS;
		$r_geo = false;
		$ar_compatible = [];

		switch( $type ){
			case _ZONE_DPT_FRANCE:
				$r_geo = sys_zones_get(0, $zipcode, '', false, 0, '', $type, [], -1, -1, false);
				break;
			case _ZONE_RGN_FRANCE:
				$r_children = sys_zones_get(0, $zipcode, '', false, 0, '', _ZONE_DPT_FRANCE, [], -1, -1, false);
				$parent_ids = [];

				if( !ria_mysql_num_rows($r_children) ){
					break;
				}
				while($child = ria_mysql_fetch_assoc($r_children) ){
					$parent_ids[] = $child['parent_id'];
					$ar_compatible[ $child['id'] ] = $child;
				}
				$r_geo = sys_zones_get($parent_ids);
				break;
			case _ZONE_PAYS:
				$r_geo = sys_zones_get(0, $adr_delivery['cntcode'], '', false, 0, '', $type, [], -1, -1, false);
				break;
		}

		if( !ria_mysql_num_rows($r_geo) ){
			throw new Exception(i18n::get('Le service sélectionné n\'est pas disponible pour cette adresse de livraison.', 'DELIVERY'), 103);
		}

		while( $geo = ria_mysql_fetch_assoc($r_geo) ){
			$ar_compatible[$geo['id']] = $geo;
		}

		while( $zone = ria_mysql_fetch_assoc($zones) ){
			$ar = dlv_package_price_zones_get(false, false, $zone['id']);

			if( !ria_mysql_num_rows($ar) ){
				continue;
			}

			while($a = ria_mysql_fetch_assoc($ar) ){
				if( array_key_exists($a['dzn_id'], $ar_compatible) ){
					$this->zone = $zone;
					return $this;
				}
			}
		}
		throw new Exception(i18n::get('Le service sélectionné n\'est pas disponible pour cette adresse de livraison.', 'DELIVERY'), 103);
	}

	/** Cette fonction permet de mettre à jour les informations de livraison.
	 * 	@return object L'objet courant
	 */
	public function reload(){

		// Mise à jour de l'objet panier
		self::$instance = new DeliveryService();
		return self::$instance;
	}

	/** Permet le chargement des champs avancés liés
	 * @param	int|array	$fld		Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($fld=0){

		if ($this->services === null || !$this->services->length()) {
			return $this;
		}

		foreach( $this->services->getAll() as $k => $srv ){
			$r_fields = fld_fields_get( $fld, 0, -2, 0, 0, $srv['id'], null, [], false, [], null, CLS_DLV_SERVICE );

			if( !ria_mysql_num_rows($r_fields) ){
				continue;
			}
			$service = $this->services->getItem($k);
			$service['fields'] = [];

			while( $field = ria_mysql_fetch_assoc($r_fields) ){
				$service['fields'][ 'field'.$field['id'] ] = [
					'id'		=> $field['id'],
					'name'		=> $field['name'],
					'value'		=> $field['obj_value'],
				];
			}
			$this->services->updateItem($service, $k);

		}

		return $this;

	}

	/** Cette fonction permet d'instancier la classe.
	 * 	@param array $options Optionnel permet de passer des options supplémentaire
	 * 	@return object L'object courant
	 */
	protected function __construct( $options=[] ){
		global $config;

		$this->cart = CartService::getInstance();

		$this->setDefaultLocation()
			->services();

		$this->options = $options;

		if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
			require_once('view.cart.inc.php');
			$res = chadog_gold_user_have_franco();
			if( $res ){
				$this->options['force_ht'] = 9999999.99;
				$this->options['force_ttc'] = 9999999.99;
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer les services de livraison éligible.
	 * 	@return array Un tableau contenant les services de livraison
	 */
	private function services(){
		global $config;

		$prds = 0;
		// Si un panier est en cours
		if( $this->cart !== null ){
			// Les services proposés ne seront que ceux étant disponible pour les produits du panier
			$prds = $this->cart->getProductIDs();
			if( !is_array($prds) || !count($prds) ){
				$prds = 0;
			}
		}

		$r_srv = dlv_services_get( 0, true, 0, false, $prds, false );

		if( !ria_mysql_num_rows($r_srv) ){
			$this->controlCart();
			return $this;
		}
		$this->services = new Collection();

		while( $srv = ria_mysql_fetch_assoc($r_srv) ){
			$r_available = ria_mysql_query('
				select 1
				from dlv_service_available
				where avl_tnt_id = '.$config['tnt_id'].'
				and avl_srv_id = '.$srv['id'].'
			');

			if( !ria_mysql_num_rows($r_available) ){
				continue;
			}
			$this->services->addItem([
				'id'			=> $srv['id'],
				'name'			=> $srv['name'],
				'desc'			=> $srv['desc'],
				'type'			=> $srv['type_id'],
				'image'			=> $srv['img_id'],
				'prd'			=> $srv['prd_ref'],
				'weight_min'	=> $srv['weight_min'],
				'weight_max'	=> $srv['weight_max'],
				'fld_id'		=> $srv['fld_id'],
				'rly_type_id'	=> $srv['presta_id']
			], 'srv'.$srv['id']);
		}

		$this->controlCart();

		return $this;
	}

	/**	Cette méthode permet de mettre localisation par défaut pour récupérer les services suivant les zones
	 * 	@param array $default Optionnel, permet de surcharger la configuration par défaut, si le tableau est vide alors $config['default_location'] est utilisé
	 *
	 * 	La variable de config 'default_location' doit être un tableau contenant:
	 * 					- zipcode	=> xxxxx,
	 * 					- city		=> XXXXX,
	 * 					- country	=> XXXXX,
	 * 					- cntcode	=> XX (Code pays)
	 * @return	object	L'objet en cours
	 */
	public function setDefaultLocation( $default=[] ){
		global $config;

		if( is_array($default) && count($default) ){
			if( !ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $default) ){
				return $this;
			}

			$this->defaultLocation = $default;
			return $this;
		}

		if( !isset($config['default_location']) || !is_array($config['default_location']) ){
			return $this;
		}

		if( !ria_array_key_exists(['zipcode', 'city', 'country', 'cntcode'], $config['default_location']) ){
			return $this;
		}
		$this->defaultLocation = $config['default_location'];

		return $this;
	}

	/**	Cette méthode permet de récupérer les services de livraison disponible suivant l'adresse par défaut
	 * @param	bool	$check_zone	Optionnel, S'il y a nécessité de vérifier la zone de livraison = true, false si non
	 * @param	int		$type		Optionnel, Si $check_zone = true, alors il faut spécifier le type de recherche de zone effectuer
	 * @return	array				Tableau contenant les services de livraison disponible, false si pas de services de livraison
	 */
	public function getDefaultServices($check_zone=false, $type=_ZONE_PAYS){

		$prds = 0;
		// Si un panier est en cours
		if( $this->cart !== null ){
			// Les services proposés ne seront que ceux étant disponible pour les produits du panier
			$prds = $this->cart->getProductIDs();
			if( !is_array($prds) || !count($prds) ){
				$prds = 0;
			}
		}

		$services = dlv_services_get( 0, true, 0, false, $prds, false );

		if( !ria_mysql_num_rows($services) ){
			return false;
		}
		$ar_services = [];
		$check_zone = is_bool($check_zone) ? $check_zone : false;

		while( $service = ria_mysql_fetch_assoc($services) ){

			if( !$check_zone ){
				if( !$this->isServiceAvailable($service['id'], $check_zone, $type) ){
					continue;
				}
				$ar_services[] = $service;
				continue;
			}
			$zone = $this->getServiceZone($service['id'], $type);

			if( !is_array($zone) ){
				continue;
			}
			$service['zone'] = $zone;
			$ar_services[] = $service;
		}
		return count($ar_services) ? $ar_services : false;

	}

	/**	Cette méthode permet de vérifier un service de livraison par rapport à l'adresse pas défaut
	 * @param	int		$svr_id		Obligatoire, Identifiant d'un service de livraison
	 * @param	bool	$check_zone	Optionnel, S'il y a nécessité de vérifier la zone de livraison = true, false si non
	 * @param	int		$type		Optionnel, Si $check_zone = true, alors il faut spécifier le type de recherche de zone effectuer
	 * @return	bool				True si le service est disponible, false sinon
	 */
	private function isServiceAvailable($svr_id, $check_zone=false, $type=_ZONE_PAYS){

		if( $this->cart == null || !$this->cart->getID() || !is_numeric($svr_id) || !$svr_id ){
			return false;
		}
		$cart_id = $this->cart->getID();

		if( !ord_prd_services_is_available($cart_id, $svr_id) ){
			return false;
		}

		if( is_bool($check_zone) && !$check_zone ){
			return true;
		}

		return is_array($this->getServiceZone($svr_id, $type));

	}

	/**	Récupère la zone de livraison associée à un service de livraison
	 * @param	int		$svr_id		Obligatoire, Identifiant d'un service de livraison
	 * @param	int		$type		Optionnel, Spécifie le type de recherche de zone
	 * @return	array|bool			Tableau de la zone en cas de succès, false sinon
	 */
	private function getServiceZone($svr_id, $type=_ZONE_PAYS){
		global $config;

		if( $this->cart == null || !$this->cart->getID() || !is_numeric($svr_id) || !$svr_id ){
			return false;
		}

		$cart_id = $this->cart->getID();

		if( !ord_prd_services_is_available($cart_id, $svr_id) ){
			return false;
		}
		$adr_delivery = $this->defaultLocation;
		$zipcode = substr($adr_delivery['zipcode'], 0, 2);

		if( in_array($zipcode, [97, 98]) ){
			$zipcode = substr($adr_delivery['zipcode'], 0, 3);
		}
		$zones = dlv_zones_get(0, true, $svr_id, $config['wst_id']);

		if( !ria_mysql_num_rows($zones) ){
			return false;
		}
		$type = in_array($type, [_ZONE_DPT_FRANCE, _ZONE_RGN_FRANCE, _ZONE_ZIPCODES, _ZONE_INSEE ]) ? $type : _ZONE_PAYS;
		$r_geo = false;

		switch( $type ){
			case _ZONE_DPT_FRANCE:
				$r_geo = sys_zones_get(0, $zipcode, '', false, 0, '', $type, [], -1, -1, false);
				break;
			case _ZONE_RGN_FRANCE:
				if( !isset($adr_delivery['region']) || !is_string($adr_delivery['region']) || trim($adr_delivery['region']) == '' ){
					break;
				}
				$r_geo = sys_zones_get(0, '', $adr_delivery['region'], false, 0, '', $type, [], -1, -1, false);
				break;
			case _ZONE_PAYS:
				$r_geo = sys_zones_get(0, $adr_delivery['cntcode'], '', false, 0, '', $type, [], -1, -1, false);
				break;
		}

		if( !ria_mysql_num_rows($r_geo) ){
			return false;
		}
		$ar_compatible = [];

		while( $geo = ria_mysql_fetch_assoc($r_geo) ){
			$ar_compatible[$geo['id']] = $geo;
		}

		while( $zone = ria_mysql_fetch_assoc($zones) ){
			$ar = dlv_package_price_zones_get(false, false, $zone['id']);

			if( !ria_mysql_num_rows($ar) ){
				continue;
			}

			while($a = ria_mysql_fetch_assoc($ar) ){
				if( !array_key_exists($a['dzn_id'], $ar_compatible) ){
					continue;
				}
				$prices = dlv_package_price_zones_get_all($zone['id']);

				if( is_array($prices) ){
					return $prices;
				}

			}
		}
		return false;

	}

	/** Cette fonction permet de vérifier qu'une zone est viable en fonction du contenu du panier et de ses règles d'inclusions / exclusion.
	 * 	@param array $zone Obligatoire, il s'agit des données sur la zone (à minima : 'id', 'all_catalog')
	 * 	@return bool True si la zone est viable, False dans le cas contraire
	 */
	private static function productIsIncluded( $zone ){
		global $config;

		// Exclusion de certains tenants du fait qu'avant les règles d'inclusions / exclusion n'étaient pas prise en compte et que certains
		// clients en ont créé des partielles.
		// https://kontinuum.atlassian.net/browse/PIERREOTMT-86
		if( in_array($config['tnt_id'], [13]) ){ // Pierre Oteiza, ...
			return true;
		}

		if( !ria_array_key_exists(['id', 'all_catalog'], $zone) ){
			return false;
		}

		static $last_product_cart_load = null;

		if( $last_product_cart_load === null ){
			$Cart = CartService::getInstance();
			$product = $Cart->getProducts();

			$last_product_cart_load = $product;
		}else{
			$product = $last_product_cart_load;
		}

		// Si aucun produit est dans le panier alors aucun restriction d'appliquée
		if( !is_array($product) || !count($product) ){
			return true;
		}

		// Charge pour chaque produit l'identifiant de la marque auquel il est lié ainsi que les identifiants des catégories où il est classé
		foreach( $product as $one_prd ){
			$brand_id = 0;
			if( isset($one_prd['prd']['brand']['id']) ){
				$brand_id = $one_prd['prd']['brand']['id'];
			}

			$r_cly = prd_classify_get( false, $one_prd['prd']['id'] );
			$ar_cats = [];
			while($cly = ria_mysql_fetch_assoc($r_cly)){
				$ar_cats[] = $cly['cat'];
			}

			$ar_products[ $one_prd['prd']['id'] ] = array(
				'include' => $zone['all_catalog'],
				'brd' => $brand_id,
				'cat' => $ar_cats,
			);
		}

		// Charge les inclusions / exclusions de catégorie et de leur héirarchie
		$r_dlv_categories = dlv_zones_categories_get( $zone['id'] );
		$ar_dlv_categories = [];
		while ($dlv_category = ria_mysql_fetch_assoc($r_dlv_categories)){
			$ar_dlv_categories[$dlv_category['id']] = $dlv_category['include'];
			$childs = prd_categories_childs_get_array($dlv_category['id']);
			foreach($childs as $key => $id){
				$ar_dlv_categories[$id] = $dlv_category['include'] === '1';
			}
		}

		// Charge les inclusions / exclusions de marques
		$r_dlv_brands = dlv_zones_brands_get( $zone['id'] );
		$ar_dlv_brands = [];
		while ($dlv_brands = ria_mysql_fetch_assoc($r_dlv_brands)){
			$ar_dlv_brands[$dlv_brands['id']] = $dlv_brands['include'] === '1';
		}

		// Charge les inclusions / exclusions de produits
		$r_dlv_products = dlv_zones_products_get( $zone['id'] );
		$ar_dlv_products = [];
		while ($dlv_products = ria_mysql_fetch_assoc($r_dlv_products)){
			$ar_dlv_products[$dlv_products['id']] = $dlv_products['include'] === '1';
		}

		$excluded_products = false;
		if( $ar_dlv_categories != [] || $ar_dlv_brands != [] || $ar_dlv_products != [] ){
			foreach( $ar_products as $prd_id => $data ){
				$have_rule = false; // Détermine si une règle s'applique pour ce produit, ou sa marque ou une des ses catégories

				foreach( $data['cat'] as $key => $cat_id ){
					if( array_key_exists($cat_id, $ar_dlv_categories) ){
						$ar_products[$prd_id]['include'] = $ar_dlv_categories[$cat_id];
						$have_rule = true;
					}
				}

				if( array_key_exists($data['brd'], $ar_dlv_brands) ){
					$ar_products[$prd_id]['include'] = $ar_dlv_brands[$data['brd']];
					$have_rule = true;
				}

				if( array_key_exists($prd_id, $ar_dlv_products) ){
					$ar_products[$prd_id]['include'] = $ar_dlv_products[$prd_id];
					$have_rule = true;
				}

				if( $have_rule ){
					// Si des règles d'inclusion / exclusion sont appliqué sur le produit ou sa marque ou une de ces catégories alors son inclusion en dépendra
					$excluded_products = $ar_products[ $prd_id ]['include'] === false;
				}else{
					// Si aucune règle d'inclusion / exclusion ne concerne le produit ou sa marque ou une de ces catégories alors le produit sera inclut si par défaut tout le catalogue est inclus
					$excluded_products = $zone['all_catalog'] == 1 ? false : true;

				}

				if( $excluded_products ){
					break;
				}
			}
		}

		return $excluded_products === false;
	}
}
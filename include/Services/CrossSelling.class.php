<?php
require_once('Services/Service.class.php');

/**	\brief Cette classe permet de charger les informations sur un produit en fonction de son contexte.
 *
 */
class CrossSellingService extends Service {
	private $cross = null;
	private $only_ids = false;

	/** Cette fonction permet d'iniatliser une liste de cross-selling présent sur une page.
	 * 	@param array $data Optionnel, permet de passer des données à la classe
	 * 			- only_ids : true = retourne uniquement les identifiants des produits | false = retourne les produits sous forme de cards (par défaut : false)
	 * 	@return empty
	 */
	public function __construct( $data=[] ){
		// Initialise la collection de cross-selling si celle-ci n'existe pas encore
		$this->cross = new Collection();

		if( isset($data['only_ids']) && is_bool($data['only_ids']) ){
			$this->only_ids = $data['only_ids'];
		}
	}

	/** Cette fonction permet de créer un tableau contenant les différents cross-selling précédement activés.
	 * 	@return array Un tableau contenant les cross-selling activé
	 */
	public function getData(){
		return $this->transformObjectToArray( $this->cross );
	}

	/** Cette fonction charge le cross-selling contenant les nouveaux articles.
	 * 	Le cross-selling contiendra :
	 * 			- titre : le titre du cross-selling
	 * 			- products : une liste de produits (cf. ProductService pour avoir le détail des données, info via card())
	 * 			- lastpublished : true = produits dernièrement publiés | false = produits marqués comme "Nouveautés"
	 * 	@param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Nouveautés")
	 */
	public function getNewProducts( $title='' ){
		global $config;

		$new = new Collection();
		$lastpublished = false;

		// Chargement des articles en Nouveautés
		$r_product = prd_products_get_simple(
				0, '', true, $config['cat_root'], true, false, false, false,
				['maxrows' => Template::get('cross-selling-new-limit'), 'new' => true]
		);

		// Si aucun article nouveau est trouvé, on récupère les derniers publiés
		if( Template::get('cross-selling-new-lastpublished') ){
			if( !ria_mysql_num_rows($r_product) ){
				$r_product = prd_products_get_simple(
						0, '', true, $config['cat_root'], true, false, false,
						['date-published' => SORT_DESC],
						['maxrows' => Template::get('cross-selling-new-limit')]
				);

				$lastpublished = true;
				$title = i18n::get('Derniers articles publiés', 'CATALOG');
			}
		}

		if( $r_product ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$obj_prd = new ProductService( ['prd' => $product['id']] );
				$obj_prd->fields();
				$new->addItem( $obj_prd->card() );
			}
		}

		// Par défaut, le titre de l'encart est "Nouveautés"
		if( trim($title) == '' ){
			$title = i18n::get('Nouveautés', 'CROSS-SELLING');
		}

		if( $new->length() ){
			$this->cross->addItem([
				'title' => $title,
				'products' => $new,
				'lastpublished' => $lastpublished
			], 'new');
		}
	}

	/** Cette fonction charge le cross-selling contenant les promotions actuels.
	 * 	@param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Promotions")
	 */
	public function getPromotions( $title='' ){
		global $config;

		$promo = new Collection();

		// Chargement des articles en promotions
		$r_product = prd_products_get_simple(0, '', true, $config['cat_root'], true, false, false, false, array('maxrows' => Template::get('cross-selling-promo-limit'), 'promotion' => true));
		if( $this->only_ids && ria_mysql_num_rows($r_product) ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$promo->addItem( $product['id'] );
			}
		} else if( ria_mysql_num_rows($r_product) ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$obj_prd = new ProductService( ['prd' => $product['id']] );
				$obj_prd->fields();
				$promo->addItem( $obj_prd->card() );
			}
		}

		// Si la limite n'est pas atteinte, on charge les produits présents dans les promotions spéciales active
		if( $promo->length() < Template::get('cross-selling-promo-limit') ){
			$ar_prd_special_promo = pmt_specials_get_products( $config['wst_id'], 0, false, true );
			if( is_array($ar_prd_special_promo) && count($ar_prd_special_promo) ){
				$r_product = prd_products_get_simple( $ar_prd_special_promo, '', true, $config['cat_root'], true, false, false, false, ['maxrows' => Template::get('cross-selling-promo-limit') - $promo->length()]);

				if( $this->only_ids && ria_mysql_num_rows($r_product) ){
					while( $product = ria_mysql_fetch_assoc($r_product) ){
						$promo->addItem( $product['id'] );
					}
				}else if( ria_mysql_num_rows($r_product) ){
					while( $product = ria_mysql_fetch_assoc($r_product) ){
						$obj_prd = new ProductService( ['prd' => $product['id']] );
						$obj_prd->fields();
						$promo->addItem( $obj_prd->card() );
					}
				}
			}
		}

		if( trim($title) == '' ){
			$title = i18n::get('Promotions', 'CROSS-SELLING');
		}

		if( $promo->length() ){
			$this->cross->addItem(['title' => $title, 'products' => $promo], 'promo');
		}
	}

	/** Cette fonction charge le cross-selling contenant les bestsellers.
	 * 	@param string $title Optionnel, permet de personnalisé le titre de l'encart
	 */
	public function getBestseller( $title='' ){
		global $config;

		$bestseller = new Collection();

		// Calcul de la date de début
		$start = new DateTime();
		$start->modify('- '.Template::get('cross-selling-bestseller-days').' days');

		// Charge les bestsellers
		$r_best = stats_bestsellers_get($start->format('Y-m-d'), false, Template::get('cross-selling-bestseller-limit'), false, null, $config['wst_id'], true, 0, $config['cat_root'], true);

		if( $this->only_ids && ria_mysql_num_rows($r_best) ){
			while( $best = ria_mysql_fetch_assoc($r_best) ){
				$bestseller->addItem( $best['id'] );
			}
		}else if( ria_mysql_num_rows($r_best) ){
			while( $best = ria_mysql_fetch_assoc($r_best) ){
				$product = new ProductService(['prd' => $best['id']]);
				$product->fields();
				$bestseller->addItem( $product->card() );
			}
		}

		if( trim($title) == '' ){
			$title = i18n::get('Meilleurs ventes', 'CROSS-SELLING');
		}

		if( $bestseller->length() ){
			$this->cross->addItem(['title' => $title, 'products' => $bestseller], 'bestseller');
		}
	}

	/** Cette fonction permet de charger le cross-selling "Vous aimerez également"
	 * 	@param $prd_id Obligatoire, identifiant d'un produit
	 * 	@param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Derniers articles consultés")
	 * 	@return empty
	 */
	public function getYouWillAlsoLike( $prd_id, $title='' ){
		global $config, $memcached;

		$prd_id = control_array_integer( $prd_id, true );
		if( $prd_id === false ){
			return false;
		}
		if( ($get = $memcached->get(Template::cache('CrossSelling:getYouWillAlsoLike:'.implode('-', $prd_id).':'.$title))) ){
			$this->cross = $get;
		}else{
			$ar_like = new Collection();

			// Récupère les catégories où le produit est classé
			$ar_cats = [];

			$r_cat = prd_products_categories_get( $prd_id, true, true, null, $config['cat_root'], null );
			if( $r_cat ){
				while( $cat = ria_mysql_fetch_assoc($r_cat) ){
					$ar_cats[] = $cat['cat'];
				}
			}

			if( count($ar_cats) ){
				// Récupère les articles en stock présents dans ces catégories
				$r_product = prd_products_get_simple( 0, '', true, $ar_cats, true, false, false, false, [
					'exclude' => $prd_id,
					'have_stock' => true,
					'limit' => Template::get('cross-selling-also-like-limit')
				]);

				if( $this->only_ids && ria_mysql_num_rows($r_product) ){
					while( $product = ria_mysql_fetch_assoc($r_product) ){
						$ar_like->addItem( $product['id'] );
					}
				}else if( ria_mysql_num_rows($r_product) ){
					while( $product = ria_mysql_fetch_assoc($r_product) ){
						$obj_prd = new ProductService( ['prd' => $product['id']] );
						$obj_prd->fields();
						$ar_like->addItem( $obj_prd->card() );
					}
				}

				if( trim($title) == '' ){
					$title = i18n::get('Vous aimerez également', 'CROSS-SELLING');
				}

				if( $ar_like->length() ){
					$this->cross->addItem( ['title' => $title, 'products' => $ar_like], 'alsolike' );
				}
			}

			$memcached->set( Template::cache('CrossSelling:getYouWillAlsoLike:'.implode('-', $prd_id).':'.$title), $this->cross, 60 * 60 * 1 );
		}
	}

	/** Cette fonction permet de charger le cross-selling des derniers articles consultés
	 * 	@param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Derniers articles consultés")
	 * 	@return empty
	 */
	public function getLastViewed( $title='' ){

		$last_viewed = new Collection();

		// Récupère les derniers articles consultés
		$ar_last = stats_user_products_get_last_consulted( Template::get('cross-selling-last-viewed-limit'), true, true );
		if( $this->only_ids && is_array($ar_last) ){
			foreach( $ar_last as $one_last ){
				$last_viewed->addItem( $one_last['id'] );
			}
		}else if( is_array($ar_last) ){
			foreach( $ar_last as $one_last ){
				$obj_prd = new ProductService( ['prd' => $one_last['id']] );
				$obj_prd->fields();
				$last_viewed->addItem( $obj_prd->card() );
			}
		}

		if( trim($title) == '' ){
			$title = i18n::get('Derniers produits consultés', 'CROSS-SELLING');
		}

		if( $last_viewed->length() ){
			$this->cross->addItem( ['title' => $title, 'products' => $last_viewed], 'lastviewed' );
		}
	}

	/** Cette fonction permet de charger le cross-selling des articles associés au produit donné
	 * 	@param int $prd_id Identifiant d'un produit
	 * 	@param int $relation Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Les clients ayant acheté cet article ont également acheté")
	 * 	@param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Articles associés")
	 * 	@return empty
	 */
	public function getAssociatedProducts( $prd_id, $relation, $title='' ){

		if( !( $prd_id > 0 && is_numeric($prd_id) && prd_products_exists($prd_id) ) ){
			return false;
		}

		if( !($relation > 0 && is_numeric($relation)) ){
			return false;
		}

		$prd_related = new Collection();

		$r_ar_related = prd_relations_get( $prd_id, null, $relation, true, Template::get('cross-selling-associated-products') );

		if( $this->only_ids && ria_mysql_num_rows($r_ar_related) ){
			while( $ar_related = ria_mysql_fetch_assoc($r_ar_related) ){
				$prd_related->addItem( $ar_related['dst_id'] );
			}
		}else if( ria_mysql_num_rows($r_ar_related) ){
			while( $ar_related = ria_mysql_fetch_assoc($r_ar_related) ){
				$obj_prd = new ProductService( ['prd' => $ar_related['dst_id']] );
				$obj_prd->fields();
				$prd_related->addItem( $obj_prd->card() );
			}
		}


		if( trim($title) == '' ){
			$title = i18n::get('Produits associés à cet article', 'CROSS-SELLING');
		}

		if( $prd_related->length() ){
			$this->cross->addItem( ['title' => $title, 'products' => $prd_related], 'associatedproducts' );
		}
	}

	/** Cette fonction permet de charger le cross-selling des articles souvent acheter avec le produit donné
	 * 	@param int $prd_id Identifiant d'un produit
	 *  @param string $title Optionnel, permet de personnaliser le titre de l'encart (par défaut : "Les clients ayant acheté cet article ont également acheté")
	 * 	@return empty
	 */
	public function getUsersAlsoBuy( $prd_id, $title='' ){
		global $config;


		if( !( $prd_id > 0 && is_numeric($prd_id) && prd_products_exists($prd_id) ) ){
			return false;
		}

		// $categories = prd_categories_childs_get_list($config['cat_root'], true);
		// $list_cat_ids = trim($categories) != '' ?  $config['cat_root'] . ',' . $categories : $config['cat_root'];
		//Bout de requete supprimer suite a optimisation
		//JOIN prd_classify on (p.prd_tnt_id=cly_tnt_id and p.prd_id=cly_prd_id and cly_cat_id in ('. $list_cat_ids .'))

		$sql = '
			SELECT op.prd_id AS id
			FROM ord_products AS op
			JOIN ord_orders ON op.prd_tnt_id = ord_tnt_id AND op.prd_ord_id = ord_id
			JOIN prd_products AS p ON op.prd_tnt_id = p.prd_tnt_id AND op.prd_id = p.prd_id
			WHERE op.prd_tnt_id = '. $config['tnt_id'] .'
				AND op.prd_ord_id IN (
					SELECT DISTINCT prd_ord_id
					FROM ord_products
					WHERE prd_tnt_id = '. $config['tnt_id'] .'
						AND prd_id = '. $prd_id .'
				)
				AND op.prd_id <> '. $prd_id .'
				AND p.prd_publish = 1
				AND p.prd_publish_cat = 1
				AND p.prd_date_deleted IS NULL
			GROUP BY op.prd_id
			ORDER BY op.prd_qte DESC
			LIMIT '. Template::get('cross-selling-users-also-buy-limit') .';
		';

		$r_also = ria_mysql_query( $sql );

		$ar_also = [];
		if( ria_mysql_num_rows($r_also) ){
			while( $also = ria_mysql_fetch_assoc($r_also) ){
				$ar_also[] = $also;
			}
		}

		$users_also_buy = new Collection();

		// Récupère les articles souvent acheter avec le produit donné
		if( is_array($ar_also) && !empty($ar_also) ){
			foreach( $ar_also as $one_also ){
				$users_also_buy->addItem( $one_also['id'] );
			}
		}else if( is_array($ar_also) && !empty($ar_also) ){
			foreach( $ar_also as $one_also ){
				$obj_prd = new ProductService( ['prd' => $one_also['id']] );
				$obj_prd->fields();
				$users_also_buy->addItem( $obj_prd->card() );
			}
		}

		if( trim($title) == '' ){
			$title = i18n::get('Les clients ayant acheté cet article ont également acheté', 'CROSS-SELLING');
		}

		if( $users_also_buy->length() ){
			$this->cross->addItem( ['title' => $title, 'products' => $users_also_buy], 'usersalsobuy' );
		}
	}
}
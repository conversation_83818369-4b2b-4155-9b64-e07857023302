<?php

require_once('documents.inc.php');

require_once('Services/Service.class.php');
require_once('Services/Catalog/Category.class.php');
require_once('Services/Catalog/Product.class.php');

/**	\brief Cette classe permet de charger les informations sur une marque.
 * 	Elle hérite de CategoryService permettant de charger un listing produit de la même façon.
 * 	De cette façon le tri et les filtres sont mutualisés.
 */
class BrandService extends CategoryService {
	protected $name = ''; ///< Nom de la marque (n'existe que pour la v1 de la classe)
	protected $mainimage = ''; ///< Identifiant de l'image principale
	protected $is_brand = true;
	protected $brd_id = 0; ///< Identifiant de la marque
	private $published = true;

	/** Cette fonction créé un objet permettant de charger les informations sur la marque.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- brd : identifiant de la marque
	 */
	public function __construct( $data=[] ){
		global $config;

		$this->id = ria_array_get( $data, 'brd', 0 );
		$this->brd_id = ria_array_get( $data, 'brd', 0 );
		$this->desc = ria_array_get( $data, 'desc', 0 );
		$this->desc_strip_tags = html_revert_wysiwyg( ria_array_get($data, 'desc', 0) );
		$this->mainimage = ria_array_get( $data, 'mainimage', 0 );
		$this->published = ria_array_get( $data, 'published', true);

		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('La marque n\'est pas identifiée.');
		}

		$r_brand = prd_brands_get( $this->id, true, '', '', false, $this->published, false, false, false, false, false );
		if( !$r_brand || !ria_mysql_num_rows($r_brand) ){
			throw new Exception('La marque n\'existe pas ou plus.');
		}

		$r_brd_prd = ria_mysql_query('
				select prd_id
				from prd_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_date_deleted is null
					and prd_publish = 1 and prd_publish_cat = 1
					and ifnull( prd_brd_id,0 ) = '.$this->id.'
		');

		$ar_prd_ids = [];
		if( $r_brd_prd ){
			while( $brd_prd = ria_mysql_fetch_assoc($r_brd_prd) ){
				$ar_prd_ids[ $brd_prd['prd_id'] ] = $brd_prd['prd_id'];
			}
		}

		// Charge la catégorie principale en appliquant un filtre sur les identifiants produits pouvant être chargé
		parent::__construct( [
			'prd_ids' => $ar_prd_ids,
		], [
			'cat' => $config['cat_root'],
		]);
	}

	/** Cette fonction permet de charger les informations générales de la marque.
	 * 	@return Brand L'objet courant
	 */
	public function general(){
		if( !is_numeric($this->brd_id) || $this->brd_id <= 0 ){
			throw new Exception('La marque n\'est pas identifiée.');
		}

		if( trim($this->title) == '' ){
			$r_brand = prd_brands_get( $this->brd_id, true );
			if( !$r_brand || !ria_mysql_num_rows($r_brand) ){
				throw new Exception('La marque n\'existe pas ou plus.');
			}

			$brand = i18n::getTranslation( CLS_BRAND, ria_mysql_fetch_assoc($r_brand) );

			$this->title = $brand['title'];
			$this->name = $brand['title'];
			$this->desc = view_site_format_description( $brand['desc'] );
			$this->url = $brand['url_alias'];

			$this->mainimage = $brand['img_id'];
		}

		return $this;
	}

	/** Cette fonction vérifie que les informations de la marque ont biens été chargées
	 * 	@return true si tout est bon, false dans le cas contraire
	 */
	public function exists(){
		return trim($this->title) != '';
	}

	/** Cette fonction permet de charger le fil d'Arinae pour la catégorie.
	 * 	@param string $title Optionnel, titre du premier niveau par défaut "Découvrez nos marques"
	 * 	@return Category L'objet courant
	 */
	public function breadcrumbs( $title='Découvrez nos marques'){
		global $config;

		if( is_numeric($this->brd_id) && $this->brd_id > 0 && $this->breadcrumbs === null ){
			$this->breadcrumbs = new Collection();

			$this->breadcrumbs->addItem([
				'url' => Template::getURL('allbrands'),
				'text' => $title
			]);
		}
	}

	/** Cette fonction permet de récupérer toutes les marques.
	 * 	@param int $limit Optionnel, limite le nombre de marque retournée (par défaut: 0, elles sont toutes retournées)
	 * 	@return array Un tableau contenant toutes les marques
	 */
	public static function all( $limit=0 ){
		global $config;

		$ar_brands = new Collection();

		$r_brand = prd_brands_get( 0, true, '', '', false, true, false, false, false, false, false, $config['cat_root'], true, true, 0, $limit );

		if( $r_brand ){
			while( $brand = ria_mysql_fetch_assoc($r_brand) ){
				$obj_brand = new BrandService( [
					'brd'			=> $brand['id'],
					'title'			=> $brand['title'],
					'name'			=> $brand['title'],
					'desc'			=> $brand['desc'],
					'url'			=> $brand['url_alias'],
					'mainimage'		=> $brand['img_id'],
				]);

				$ar_brands->addItem( $obj_brand->general() );
			}
		}

		return self::transformObjectToArray( $ar_brands );
	}
}
<?php

require_once('Services/Customer/Customer.class.php');
require_once('promotions.inc.php');

/**	\brief Cette classe permet de charger les informations sur les promotions spéciales en cours
 */
class PromotionsService extends Service {
	private $pmts = []; ///< Tableau contenant toutes les informations sur les promotions
	private static $instance = null;

	/** Cette fonction permet d'initialiser la classe.
	 * 	@return PromotionsService L'objet courant
	 */
	public static function getInstance(){
		if( is_null(self::$instance) ){
			self::$instance = new PromotionsService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de charger un tableau contenant les différents labels de promotions */
	public function labelsProduct( $prd_id ){
		if( !is_numeric($prd_id) || $prd_id <= 0 ){
			return [];
		}

		$prd_labels = [];

		foreach( self::$instance->pmts as $one_pmt ){
			// Contrôle que le produit est inclut dans la promotions
			if( $one_pmt['products'] != 'all' && !array_key_exists($prd_id, $one_pmt['products']) ){
				continue;
			}
			$ar_label = [];

			if( count($one_pmt['label']) ){
				$ar_label = $one_pmt['label'];
			}elseif( trim($one_pmt['cod']['desc']) != '' ){
				$ar_label[] = $one_pmt['cod']['desc'];
			}else{
				// Charge les offres sur la promotions
				$r_offers = pmt_offers_get( $one_pmt['cod']['id'] );

				// Gestion du texte promotion en fonction du type de promotion
				switch( $one_pmt['cod']['type'] ){
					case _PMT_TYPE_PRD : // Produit(s) offert(s)
						$r_prd = pmt_offer_products_get( $one_pmt['cod']['id'] );
						if( $r_prd ){
							if( ria_mysql_num_rows($r_prd) == 1 ){
								$offer = ria_mysql_fetch_assoc( $r_offers );
								$prd = ria_mysql_fetch_assoc( $r_prd );

								if( $prd['qty'] > 1 ){
									$text_promo = i18n::get( '#param[qte]# #param[produit]# offerts', 'PROMO' );
								}else{
									$text_promo = i18n::get( '#param[qte]# #param[produit]# offert', 'PROMO' );
								}

								$ar_label[] = str_replace(
									[ '#param[qte]#', '#param[produit]#' ],
									[ $prd['qty'], $prd['prd_title'] ],
									$text_promo
								);
							}
						}
					break;
					case _PMT_TYPE_REDUC : // Réductions dégresives
						if( $r_offers ){
							while( $offer = ria_mysql_fetch_assoc($r_offers) ){
								$ar_label[] = str_replace(
									[ '#param[reduc]#', '#param[pos]#' ],
									[
										str_replace(',00', '', number_format($offer['discount'], 2, ',', ' ')),
										$offer['prd_pos']
									],
									i18n::get( '-#param[reduc]#% sur le #param[pos]#ème', 'PROMO' )
								);
							}
						}
					break;
					case _PMT_TYPE_BUY_X_FREE_Y : // X acheté(s) = Y offert(s)
						if( $r_offers ){
							while( $offer = ria_mysql_fetch_assoc($r_offers) ){
								$text_promo = '#param[x]# acheté = #param[y]# offert';
								if( $offer['buy_x'] > 1 && $offer['free_y'] > 1){
									$text_promo = '#param[x]# achetés = #param[y]# offerts';
								}elseif( $offer['buy_x'] > 1 ){
									$text_promo = '#param[x]# achetés = #param[y]# offert';
								}elseif( $offer['free_y'] > 0 ){
									$text_promo = '#param[x]# acheté = #param[y]# offerts';
								}

								$ar_label[] = str_replace(
									[ '#param[x]#', '#param[y]#' ],
									[ $offer['buy_x'], $offer['free_y'] ],
									$text_promo
								);
							}
						}
					break;
				}

				self::$instance->pmts[ $one_pmt['cod']['id'] ]['label'] = $ar_label;
			}

			$prd_labels = array_merge( $prd_labels, $ar_label );
		}

		return array_unique( $prd_labels );
	}

	/** Cette fonction permet de charger un tableau des identifiants de produits présents dans les promotions.
	 * 	@return array Un tableau contenant les identifiants de produit
	 */
	public function getProducts(){
		$ar_prd_ids = [];

		foreach( self::$instance->pmts as $one_pmt ){
			if( is_array($one_pmt['products']) && count($one_pmt['products']) ){
				$ar_prd_ids = $ar_prd_ids + $one_pmt['products'];
			}
		}

		return $ar_prd_ids;
	}

	/** Cette fonction permet de charger les champs avancés de chaque promotions.
	 * 	@return PromotionsService L'objet courant
	 */
	public function fields(){
		foreach( self::$instance->pmts as &$one_pmt ){
			$one_pmt['fields'] = [];

			$r_fields = fld_fields_get( 0, 0, -2, 0, 0, $one_pmt['cod']['id'], null, [], false, [], null, CLS_PMT_CODE );

			if( $r_fields ){
				while( $field = ria_mysql_fetch_assoc($r_fields) ){
					$one_pmt['fields'][] = [
						'id' => $field['id'],
						'name' => $field['name'],
						'value' => $field['obj_value'],
						'fldpos' => $field['fld_pos'],
						'unit' => $field['unit_symbol']
					];
				}
			}
		}
	}

	/** Cette fonction permet de mettre à jour les promotions.
	 * 	@return PromotionsService L'objet courant
	 */
	public function reload(){
		self::$instance = new PromotionsService();
		return self::$instance;
	}

	/** Cette fonction permet de retourner tous le détails sur les promotions.
	 * 	@return Un tableau contenant les promotions
	 */
	public function get( $prd_id=0 ){
		$promotions = $this->pmts;

		if( is_numeric($prd_id) && $prd_id > 0 ){
			foreach( $promotions as $key_pmt=>$pmt ){
				if( $pmt['products'] != 'all' && !in_array($prd_id, $pmt['products']) ){
					unset( $promotions[$key_pmt] );
				}
			}
		}

		return $promotions;
	}

	/** Cette fonction permet de charger les promotions.
	 * 	@return empty
	 */
	private function __construct(){
		global $config;

		// Récupère les informations sur le client connecté
		$user = CustomerService::getInstance();

		$in_cart = false;
		// Détermine si le chargement est fait dans le processus de commande ou en dehors
		if( isset($_SERVER['PATH_INFO']) && trim($_SERVER['PATH_INFO']) != '' ){
			if( in_array($_SERVER['PATH_INFO'], Template::getURLCart()) ){
				$in_cart = true;
			}
		}

		// Récupère le panier en cours (sans les promotions)
		$order = false;

		if( $in_cart ){
			if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
				$cart = new OrderService( ['ord' => $_SESSION['ord_id'], 'status' => [_STATE_BASKET, _STATE_BASKET_SAVE]] );

				if( $cart !== false && $user->isConnected() ){
					$temp_ord = $cart->address()->getData();

					$order = [
						'user' => $user->getID(),
						'id' => $cart->getID(),
						'inv_firstname' => $temp_ord['adrinvoice']['firstname'],
						'inv_lastname' => $temp_ord['adrinvoice']['lastname'],
						'inv_society' => $temp_ord['adrinvoice']['society'],
						'inv_address1' => $temp_ord['adrinvoice']['address1'],
						'inv_address2' => $temp_ord['adrinvoice']['address2'],
						'inv_postal_code' => $temp_ord['adrinvoice']['zipcode'],
						'inv_city' => $temp_ord['adrinvoice']['city'],
						'inv_country' => $temp_ord['adrinvoice']['country'],
						'dlv_firstname' => $temp_ord['adrdelivery']['firstname'],
						'dlv_lastname' => $temp_ord['adrdelivery']['lastname'],
						'dlv_society' => $temp_ord['adrdelivery']['society'],
						'dlv_address1' => $temp_ord['adrdelivery']['address1'],
						'dlv_address2' => $temp_ord['adrdelivery']['address2'],
						'dlv_postal_code' => $temp_ord['adrdelivery']['zipcode'],
						'dlv_city' => $temp_ord['adrdelivery']['city'],
						'dlv_country' => $temp_ord['adrdelivery']['country'],
					];
				}
			}
		}

		{ // Récupère les promotions spéciales automatiques de type :
			// 	- Produits offerts
			// 	- Réduction dégressive
			// 	- X acheté(s) = Y offert(s)

			$field = false;

			// Hack Chadog, seules les promotions avec "Hors seuil max" à Oui sont récupérées
			// if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
			// 	$field = [ $config['fld_cod_without_max']=>'Oui' ];
			// }

			$r_cod = pmt_codes_get( null, null, true, [_PMT_TYPE_PRD, _PMT_TYPE_REDUC, _PMT_TYPE_BUY_X_FREE_Y, _PMT_TYPE_REMISE], true, null, null, null, null, $field );
		}

		if( $r_cod ){
			while( $cod = ria_mysql_fetch_assoc($r_cod) ){
				// Contrôle que le client connecté a bien accès à cette promotion
				if( $user->isConnected() ){
					if( !pmt_users_is_included($cod, $user->getID()) ){
						continue;
					}
				}else{
					// Si aucun client n'est connecté alors il faut que la promotion soit accessible par défaut à tous
					if( !$cod['all-customers'] ){
						continue;
					}
				}

				// Contrôle des conditions
				if( pmt_codes_is_applicable($cod['id'], (isset($order['id']) ? $order['id'] : 0), 0, false, false, $cod, $order) !== true ){
					continue;
				}

				// Charge les informations générales sur la promotions
				$this->pmts[ $cod['id'] ] = [
					'cod' => [
						'id' 			=> $cod['id'],
						'name' 			=> $cod['name'],
						'type' 			=> $cod['type'],
						'desc' 			=> $cod['desc'],
						'date_start' 	=> $cod['date_start_en'],
						'date_stop' 	=> $cod['date_stop_en'],
						'discount' 		=> $cod['discount']
					],

					// Le label sera chargé au fur et à mesure des besoins
					// Il contient la description visible par les internautes
					'label' => []
				];

				if( $cod['type'] == _PMT_TYPE_REMISE ){
					if( $cod['discount_type'] == 0 ){
						$this->pmts[ $cod['id'] ]['label'][] = '- '.str_replace( '.00', '', round( $cod['discount'], 2 ) ).' €';
					}elseif( $cod['discount_type'] == 1){
						$this->pmts[ $cod['id'] ]['label'][] = '- '.str_replace( '.00', '', round( $cod['discount'], 2 ) ).' %';
					}
				}

				// Charge les articles incluts dans la promotion
				if( $cod['all-catalog'] ){
					$this->pmts[ $cod['id'] ]['products'] = 'all';
				}else{
					$this->pmts[ $cod['id'] ]['products'] = [];

					$r_prd = pmt_codes_products_get( $cod['id'], false, 0, $cod, true, false );
					if( !is_array($r_prd) || !count($r_prd) ){
						unset($this->pmts[ $cod['id'] ]);
					}else{
						foreach( $r_prd as $prd ){
							$this->pmts[ $cod['id'] ]['products'][ $prd['prd_id'] ] = $prd['prd_id'];
						}
					}
				}
			}
		}

		return $this;
	}
}
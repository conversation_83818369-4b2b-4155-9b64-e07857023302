<?php

require_once('Services/Service.class.php');
require_once('Services/Catalog/Category.class.php');

/**	\brief Cette classe permet de charger la catégorie virtuelle "Promotions"
 *
 */
class PromotionCategoryService extends CategoryService{

	public function __construct(){
		global $config;

		$ar_prd_ids = [];
		$temp = PromotionsService::getInstance()->getProducts();
		if( is_array($temp) && count($temp) ){
			$ar_prd_ids = $ar_prd_ids + $temp;
		}

		// Récupère les produits pour lesquels un tarif promotionnel est en place
		$r_price = prc_prices_get_promotions( false, 1, 1 );
		if( $r_price ){
			while( $price = ria_mysql_fetch_assoc($r_price) ){
				if( is_numeric($price['prd']) && $price['prd'] > 0 ){
					$parent = prd_products_get_parent( $price['prd'] );
					if( is_array($parent) && count($parent) ){
						foreach( $parent as $one_parent ){
							$ar_prd_ids[ $one_parent ] = $one_parent;
						}
					}

					$ar_prd_ids[ $price['prd'] ] = $price['prd'];
				}
			}
		}

		if( !count($ar_prd_ids) ){
			$ar_prd_ids = [0];
		}

    // Charge la catégorie principale
		parent::__construct( [
			'prd_ids' => $ar_prd_ids,
		], [
			'cat' => $config['cat_root']
	]);

}

	public function general(){
		parent::general();

		// Force les informations comme le titre, l'URl et la description
		$this->title = i18n::get('Promotions', 'CATALOG');
		$this->url = Template::getURL('catalogpromo');
		$this->desc = '';

		return $this;
	}
}
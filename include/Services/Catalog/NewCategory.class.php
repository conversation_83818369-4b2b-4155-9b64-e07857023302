<?php

require_once('Services/Service.class.php');
require_once('Services/Catalog/Category.class.php');

/**	\brief Cette classe permet de charger la catégorie virtuelle "Nouveautés"
 *
 */
class NewCategoryService extends CategoryService{

	public function __construct(){
		global $config;

		// Charge la catégorie principale
		parent::__construct( [
			'new' => true
		], [
			'cat' => $config['cat_root']
	]);

}

	public function general(){
		parent::general();

		// Force les informations comme le titre, l'URl et la description
		$this->title = i18n::get('Nouveautés', 'CATALOG');
		$this->url = Template::getURL('catalognew');
		$this->desc = '';

		return $this;
	}
}
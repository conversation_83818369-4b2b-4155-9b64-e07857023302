<?php

require_once('Services/Service.class.php');
require_once('Services/Catalog/Product.class.php');
require_once('categories.inc.php');
require_once('prd/category-filters.inc.php');

/**	\brief Cette classe permet de charger les informations liées à une famille de produits.
 *
 */
class CategoryService extends Service{
	protected $title = ''; ///< Désignation de la catégorie
	protected $url = ''; ///< URL public de la catégorie
	protected $desc = ''; ///< Description de la catégorie
	protected $desc_strip_tags = ''; ///< Description de la catégorie sans balise HTML
	protected $type = '';
	protected $children = null; ///< Liste des catégories enfants
	protected $fields = null; ///< Champs avancés liés à la catégorie
	protected $parent = 0; ///< Identifiant de la catégorie parent

	protected $filters = null; ///< Filtres pouvant être utilisés sur les articles de la catégorie
	protected $pricerange = []; ///< Filtres sur les tarifs
	protected $ismenuactive = false; ///< Si la catégorie doit être marqué comme active dans le menu

	protected $count_prds = 0; ///< Permet de connaitre le nombre de produit total
	protected $prds = null; ///< Liste des produits présents dans la catégorie
	protected $isfilter = false; ///< Si oui ou non la liste des produits est soumis à l'application de filtre

	protected $isdiscount = false; ///< Si oui ou non la catégorie est une catégorie soldes/ promotions

	protected $main_image = [];///< Image princiapale
	protected $images = null; ///< Tableau des images liés à cette catégorie
	protected $breadcrumbs = null; ///< Fil d'Ariane

	protected $have_new_prds = null; // Détermine si la catégorie dispose de nouveautés
	protected $id; ///< Identifiant de la catégorie
	private $applyfilters = []; ///< Il s'agit des filtres activés
	private $prd_ids = []; ///< Permet de limiter la liste des produits à une certaine liste de produits
	private $exclude_prd_ids = []; ///< Permet d'exclure certains articles du résultats
	private $new = false; ///< Limite la liste des articles aux nouveautés

	/** Cette fonction permet de créer un objet chargeant les informations liés à une famille de produits.
	 *  @param $cfg Optionnel, permet de surcharger les variables du contrôleur
	 *  @param $data Optionnel, permet de transmettre les informations suivantes :
	 *                - cat : identifiant de la famille
	 *                - withprice : charge le tarif du produit
	 */
	public function __construct( $cfg=[], $data=[] ){
		$this->new = ria_array_get($cfg, 'new', 0);
		$this->prd_ids = ria_array_get($cfg, 'prd_ids', 0);
		$this->exclude_prd_ids = ria_array_get($cfg, 'exclude', []);

		$this->id = ria_array_get($data, 'cat', 0);
		$this->title = ria_array_get($data, 'title', '');
		$this->url = ria_array_get($data, 'url', '');
		$this->desc = view_site_format_riawysiwyg( ria_array_get($data, 'desc', '') );
		$this->desc_strip_tags = strip_tags( ria_array_get($data, 'desc', '') );
		$this->type = ria_array_get($data, 'type', '');
		$this->children = ria_array_get($data, 'children', null);
		$this->parent = ria_array_get($data, 'parent', null);
		$this->isdiscount = ria_array_get($data, 'isdiscount', false);
		$this->count_prds = ria_array_get($data, 'count_prds', 0);

		$this->prds = new Collection();
		$this->filters = new Collection();
	}

	/** Cette fonction charge les informations générales de la famille.
	 *  @return object L'objet courant, False si les informations n'ont pas pu être chargé
	 */
	public function general(){
		global $config;

		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('La catégorie n\'est pas identifiée.');
		}

		// Seule une catégorie publié est retournée
		$cat_root = is_numeric($this->parent) && $this->parent > 0 ? $this->parent : $config['cat_root'];
		$cat_root = $this->id == $cat_root ? 0 : $cat_root;

		if( trim($this->title) == '' ){
			$r_cat = prd_categories_get($this->id, true, $cat_root, '', false, false, null, false, array(), true);
			if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
				throw new Exception('La catégorie n\'existe pas ou plus.'.$this->id);
			}

			$cat = i18n::getTranslation( CLS_CATEGORY, ria_mysql_fetch_assoc($r_cat) );

			// Vérifie si la catégorie possède une date de fin de parution, si oui vérifie qu'elle est dépassé ou non pour savoir s'il affichage la catégorie
			if( isset($cat['date_to_en'])){
				$date_now = time();
				$cat_date_over = strtotime($cat['date_to_en']);
				if( $cat_date_over < $date_now){
					return $this;
				}
			}

			$this->title = $cat['title'];
			$this->url = rew_strip( $cat['url_alias'] );
			$this->desc = view_site_format_riawysiwyg( $cat['desc'] );
			$this->desc_strip_tags = strip_tags( $cat['desc'] );
			$this->parent = $cat['parent_id'];
			$this->isdiscount = isset($cat['is_soldes']) && $cat['is_soldes'];
		}

		$this->url = rew_strip( $this->url );
		$this->images = new Collection();

		$r_img = prd_cat_images_get( $this->id );
		if( $r_img ){
			while( $img = ria_mysql_fetch_assoc($r_img) ){
				$this->images->addItem( $img['id'] );
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les champs avancés liés
	 * @param	int|array	$id	Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($id=0){
		global $config;

		if( !$this->id || $this->fields !== null ){
			return $this;
		}

		$id = control_array_integer( $id, false );
		if( $id === false ){
			return $this;
		}

		$this->fields = [];

		$r_fields = ria_mysql_query('
			select fld_id as id, pv_value as obj_value, fld_type_id as type_id
			from fld_object_values
				join fld_fields on (
					fld_tnt_id = '.$config['tnt_id'].'
					and fld_id = pv_fld_id and fld_cls_id = '.CLS_CATEGORY.'
				)
			where pv_tnt_id = '.$config['tnt_id'].'
				and pv_obj_id_0 = '.$this->id.' and pv_obj_id_1 = 0 and pv_obj_id_2 = 0
				and fld_date_deleted is null
				'.( count($id) > 0 ? ' and pv_fld_id in ( '.implode( ', ', $id ).' )' : '' ).'
		');

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			// Transforme la valeur en tableau pour certains types de champs avancés
			// Liste de choix unique, Liste de choix multiple ou Liste de choix multiple hiérarchique
			if( in_array( $field['type_id'], [FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY]) ){
				$field['obj_value'] = explode( ', ', $field['obj_value'] );
			}

			$this->fields[ 'field'.$field['id'] ] = [
				'id' => $field['id'],
				'value' => $field['obj_value'],
			];
		}

		return $this;
	}

	/** Cette fonction permet de charger les catégories enfants.
	 * @param		int		$depth	Optionnel, permet de préciser un niveau de profondeur de récupération
	 * @param		int		$max	Optionnel, permet de limiter le résultat
	 * @return		object 	L'objet courant
	 */
	public function childs( $depth=0, $max=0 ){
		global $config, $hook;

		if( $this->id <= 0 ){
			throw new Exception('L\'identifiant de la catégorie parente est manquant, impossible de charger les catégories enfants.');
		}

		// Récupère les catégories enfants
		$sql = '
			select
				cat_id as child, cat_products as products, if(cat_title="", cat_name, cat_title) as title, cat_desc as "desc",
				cat_parent_id as parent_id, cat_is_soldes as is_soldes,
				if(ifnull(cat_url_perso, "")="", cat_url_alias, cat_url_perso) as url_alias
			from prd_categories
			where cat_tnt_id = '.$config['tnt_id'].'
				and cat_parent_id = '.$this->id.'
				and cat_publish = 1
				and cat_products > 0
				and cat_date_deleted is null
				and ( cat_date_from is null or cat_date_from <= now() ) and ( cat_date_to is null or cat_date_to > now() )
		';

		if( $config['use_catalog_restrictions'] ){
			$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cat_id' ).')';
		}

		$sql .= '
			order by cat_pos asc, if( cat_title!="", cat_title, cat_name )
		';

		if( is_numeric($max) && $max > 0 ){
			$sql .= ' limit '.$max;
		}

		$childs = new Collection();

		$r_child = ria_mysql_query( $sql );
		if( $r_child ){
			$depth--;

			while( $child = ria_mysql_fetch_assoc($r_child) ){
				try{
					$temp = new CategoryService( [], [
						'cat' => $child['child'],
						'title' => $child['title'],
						'desc' => $child['desc'],
						'parent' => $child['parent_id'],
						'isdiscount' => $child['is_soldes'],
						'url' => $child['url_alias'],
						'count_prds' => $child['products']
					]);

					$temp->general()->fields();

					if( $depth > 1 ){
						$temp->childs( $depth );
					}

					$hook->do_action('CategoryService_onLoadingChild', ['Category' => $temp]);

					$childs->addItem( $temp );
				}catch( Exception $e ){
					// Pas de blocage on continue de charger les autres catégories enfants
				}
			}

		}

		$this->children = $childs;

		return $this;
	}

	/** Cette fonction permet de charger les articles présent dans une catégorie.
	 * 	@param int $start Optionnel, permet de préciser à partir de quel curseur on récupère les produits
	 * 	@param bool|int $limit Optionnel, permet de préciser combien de produit sont récupérés (par défaut $config['prd_list_length'])
	 * 	@param bool|string|null $sort Optionnel, tri à appliquer (valeur acceptée : 'sort|asc' => prix croissant, 'sort|desc' => prix décroissant, 'new' => Nouveautés en premier), null pour appliquer le tri de la catégorie
	 * 	@param bool $just_ids Optionnel, permet de retourner un tableau des identifiants produits (par défaut désactivé)
	 * 	@param array $others Optionnel, tout autre paramètre optionnel
	 * 		- index : true|false permet de charger les mêmes informations que s'il s'agit de la fiche produit
	 * 		- reviews : true|false permet de charger les avis sur les produits
	 * 		- fields : true|false permet de charger les champs avancés sur les produits
	 * 		- apply_limit : true|false permet d'appliquer d'une limite grâce aux paramètres $start et $limit
	 * 	@return object L'object courant
	 */
	public function products( $start=0, $limit=false, $sort=false, $just_ids=false, $others=[] ){
		global $config, $hook;

		if( !is_numeric($start) || $start <= 0 ){
			$start = 0;
		}

		if( !is_numeric($limit) || $limit <= 0 ){
			$limit = $config['prd_list_length'];
		}

		{ // Préparer les filtres / tris sur la récupération des articles
			$params = [];
			if( isset($others['params']) && is_array($others['params']) ){
				$params = $others['params'];
			}

			$params['only_prd_ids'] = true;

			$with_price = $fld = false;

			// Applique le filtre par marque
			if( array_key_exists('brand', $this->applyfilters) ){
				$params['brand'] = $this->applyfilters['brand'];
				$this->isfilter = true;
			}

			// Applique le filtre par sous-famille
			$cat_filter = $this->id;
			if( array_key_exists('catchilds', $this->applyfilters) ){
				$cat_filter = $this->applyfilters['catchilds'];
				$this->isfilter = true;
			}

			// Applique le filtre par disponibilité
			if( array_key_exists('availability', $this->applyfilters) ){
				if( in_array( 1, $this->applyfilters['availability'] ) ){ // En stock
					$params['have_stock'] = true;
					$this->isfilter = true;
				}
				if( in_array( 2, $this->applyfilters['availability'] ) ){ // Prochainement disponible
					$params['ordered'] = true;
					$params['have_stock'] = false;
					$this->isfilter = true;
				}
				if( in_array(1, $this->applyfilters['availability']) && in_array(2, $this->applyfilters['availability']) ){
					$params['ordered'] = true;
					$params['have_stock'] = true;
					$params['have_stock_or_ordered'] = true;
					$this->isfilter = true;
				}
			}

			// Applique le filtre par disponibilité
			if( array_key_exists('have_stock', $this->applyfilters) ){
				$params['have_stock'] = $this->applyfilters['have_stock'];
				$this->isfilter = true;
			}

			// Applique le filtre sur les champs avancés
			if( array_key_exists('fld', $this->applyfilters) ){
				$fld = $this->applyfilters['fld'];
				$params['or_between_val'] = true;
				$params['fld_in_child'] = Template::get('catalog-filter-children');
				$this->isfilter = true;
			}

			// Filtre sur les prix
			if( array_key_exists('prcmin', $this->applyfilters) && is_numeric($this->applyfilters['prcmin']) && $this->applyfilters['prcmin'] > 0 ){
				$with_price = true;
				$params['only_prd_ids'] = false;
				$params['use_child_price'] = true;
				$params['price-min'] = $this->applyfilters['prcmin'];
				$params['price_borne_ttc'] = Template::get('catalog-slide-price-type') == 'ttc';
				$this->isfilter = true;
			}
			if( array_key_exists('prcmax', $this->applyfilters) && is_numeric($this->applyfilters['prcmax']) && $this->applyfilters['prcmax'] > $this->applyfilters['prcmin'] ){
				$with_price = true;
				$params['only_prd_ids'] = false;
				$params['use_child_price'] = true;
				$params['price-max'] = $this->applyfilters['prcmax'];
				$params['price_borne_ttc'] = Template::get('catalog-slide-price-type') == 'ttc';
				$this->isfilter = true;
			}

			// Application des tri
			$ar_sort = [];
			$init_limit = $init_start= false;

			if( $sort !== false ){
				$params['only_prd_ids'] = false;

				if( $sort === null ){
					$sort = false;
				}

			}

			if( $sort !== false ){

				if( !is_array($sort) ){
					$sort = [$sort];
				}

				foreach( $sort as $key => $one_sort ){
					switch( $one_sort ){
						case 'set':
							// Accessible uniquement si une liste d'identifiant de produit est donné
							if( count($this->prd_ids) ){
								$ar_sort['set'] = 'asc';
							}
						break;
						case 'prc|asc':
						case 'prc|desc':
							$init_start = $start;
							$init_limit = $limit;

							$start = 0;
							$limit = 9999;

							$with_price = true;
							$params['use_child_price'] = true;
							$params['only_prd_ids'] = false;
						break;
						case 'new':
							$ar_sort['new'] = 'desc';
						break;
						case 'bestseller':
							$ar_sort['selled'] = 'dir';
						break;
						case 'random':
							$ar_sort['random'] = 'asc';
							break;
						case 'ref|asc':
							$ar_sort['ref'] = 'asc';
							break;
						case 'ref|desc':
							$ar_sort['ref'] = 'desc';
							break;
						case 'name|asc':
							$ar_sort['name'] = 'asc';
							break;
						case 'name|desc':
							$ar_sort['name'] = 'desc';
							break;
						case 'prcprices|asc':
							$ar_sort['prcprices'] = 'asc';
							break;
						case 'prcprices|desc':
							$ar_sort['prcprices'] = 'desc';
							break;
						case 'asc':
						case 'desc':
							$ar_sort[ $key ] = $one_sort;
							break;
					}
				}
			}

			if( $this->new ){
				$params['new'] = true;
			}

			$params['exclude'] = false;
			if( count($this->exclude_prd_ids) ){
				$params['exclude'] = $this->exclude_prd_ids;
			}

			if( $just_ids){
				$params['only_prd_ids'] = true;
			}

			if( isset($others['force_limit']) && is_numeric($others['force_limit']) && $others['force_limit'] > 0 ){
				$params['limit'] = $others['force_limit'];
			}
		}

		// Hook : remplace la fonction prd_products_get_simple
		if( !$hook->do_action('CategoryService_loadingProducts', ['Category' => $this, 'id' => (count($this->prd_ids) ? $this->prd_ids : 0), 'cat' => $cat_filter, 'fld' => $fld, 'sort' => $ar_sort, 'others_param' => $params]) ){
			// Récupère la liste des articles présent dans la catégorie
			$r_product = prd_products_get_simple( (count($this->prd_ids) ? $this->prd_ids : 0), '', true, $cat_filter, true, $fld, $with_price, $ar_sort, $params );


			if( $just_ids ){
				$ar_prd_ids = [];

				if( $r_product ){
					while( $product = ria_mysql_fetch_assoc($r_product) ){
						$ar_prd_ids[] = $product['id'];
					}
				}

				return $ar_prd_ids;
			}
			$__count_prds = ria_mysql_num_rows( $r_product );

			if( !$__count_prds ){
				return $this;
			}
			$this->count_prds = $__count_prds;

			if( $start >= $this->count_prds ){
				return $this;
			}
			ria_mysql_data_seek( $r_product, $start );

			// Création de la liste des produits dans la limite demandé
			$i = 1;
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				if( ($i++) > $limit ){
					break;
				}

				$product = i18n::getTranslation( CLS_PRODUCT, $product );
				$data = $product;
				$data['prd'] = $product['id'];
				$data['withprice'] = true;

				if( isset($product['follow_stock']) ){
					$data['followstock'] = $product['follow_stock'];
				}
				$obj_prd = new ProductService( $data );

				if( in_array('index', $others) ){
					$obj_prd->index( true );
				}

				if( in_array('reviews', $others) ){
					$obj_prd->reviews();
				}

				if( in_array('fields', $others) ){
					$obj_prd->fields();
				}

				$this->prds->addItem( $obj_prd->card() );
			}
		}

		$temp = $this->transformObjectToArray( $this->prds);

		if( $just_ids ){
			$ar_prd_ids = [];

			if( count($temp) ){
				foreach( $temp as $product ){
					$ar_prd_ids[] = $product['id'];
				}
			}

			return $ar_prd_ids;
		}

		// Applique le tri croissant / décroissant sur les articles
		if( is_array($sort) ){
			if( in_array('prc|asc', $sort) ){
				$temp = array_msort( $temp, ['priceht' => SORT_ASC] );
			}elseif( in_array('prc|desc', $sort) ){
				$temp = array_msort( $temp, ['priceht' => SORT_DESC] );
			}
		}

		if( isset($others['apply_limit']) && is_bool($others['apply_limit']) && $others['apply_limit'] ){
			$init_start = $start;
			$init_limit = $limit;
		}

		// Réapplique une limite
		if( $init_start !== false && $init_limit !== false ){
			$temp = array_slice( $temp, $init_start, $init_limit );
		}

		// Cette ligne est mis en commentaire car par défaut cette fonction travail que sur la page demandé et donc si l'on demande un page sur le site, il n'y a pas tous les produits donc on ne charge pas de produit
		// Problème survenue sur Naturanimo au chargement d'une page autre que la première, par contre cela risque de poser problème sur Berton
		// $temp = array_slice( $temp, $start, $limit );

		$this->prds = new Collection();
		foreach( $temp as $one_prd ){
			$this->prds->addItem( $one_prd );
		}

		return $this;
	}

	/** Cette fonction permet de compléter l'url des produits pour le moteur de recherche.
	 * 	@param array $search Obligatoire, tableau contenant pour chaque ID son complément
	 * 	@return object L'object courant
	 */
	public function complSearchUrl( $search ){
		if( get_class($this->prds) == 'Collection' ){
			$temp = new Collection();

			foreach( $this->prds->getAll() as $one_prd ){
				if( array_key_exists($one_prd['id'], $search) ){
					$one_prd['url'] .= $search[ $one_prd['id'] ];
					$temp->addItem( $one_prd );
				}
			}

			$this->prds = $temp;
		}

		return $this;
	}

	/**	Permet d'ajouter un produit à la liste
	 * @param	array	$prd	Obligatoire, Tableau des données du produit
	 * @param	bool	$reset	Optionnel, True pour réinitialiser la liste avant d'ajouter le produit
	 * @return	bool	False en cas d'erreur, true sinon
	 */
	public function addProduct($prd, $reset = false){

		if( !is_array($prd) ){
			return false;
		}
		$reset = is_bool($reset) ? $reset : false;

		if( !($this->prds instanceof Collection) || $reset ){
			$this->prds = new Collection();
			$this->count_prds = 0;
		}
		$this->prds->addItem($prd);
		$this->count_prds++;
		return true;
	}

	/**	Permet de vider la liste des produits
	 * @return	object	L'objet en cours
	 */
	public function resetProducts(){
		$this->prds = new Collection();
		$this->count_prds = 0;

		return $this;

	}

	/** Cette fonction permet de récupérer la liste des catégories dans lesquelles les produits sont classés.
	 * 	@return array Un tableau des données sur les catégories (CategoryService)
	 */
	public function getListCategories(){
		global $config;

		$ar_cats = new Collection();

		$ar_prd_ids = $this->products( 0, false, false, true );

		if( is_array($ar_prd_ids) && count($ar_prd_ids) ){
			$sql = '
				select distinct cly_cat_id as cat
				from prd_classify
					join prd_cat_hierarchy on (
						cat_tnt_id = '.$config['tnt_id'].'
						and cat_parent_id = '.$this->id.'
						and cat_child_id = cly_cat_id
					)
				where cly_tnt_id = '.$config['tnt_id'].'
					and cly_cat_publish = 1
					and cly_prd_id in ('.implode( ', ', $ar_prd_ids ).')
			';

			$r_cat = ria_mysql_query( $sql );
			if( $r_cat ){
				while( $cat = ria_mysql_fetch_assoc($r_cat) ){
					$obj_cat = new CategoryService( [], $cat );
					$obj_cat->general();

					$ar_cats->addItem( $obj_cat );
				}
			}
		}

		$ar_cats = self::transformObjectToArray( $ar_cats->getAll() );
		$ar_cats = array_msort( $ar_cats, ['title' => SORT_ASC] );

		return $ar_cats;
	}

	/** Cette fonction permet de récupérer les filters qui peuvent sur la catégorie.
	 * 	@return object Une collection de filtre
	 */
	public function filters(){
		global $config, $hook;

		// On récupère le prix maximal (prix public) si une option slide de prix est activé
		if( Template::get('catalog-slide-price') ){
			$this->pricerange['min'] = 0;
			$this->pricerange['max'] = 1000;

			if(property_exists($this, 'brd_id') && is_numeric($this->brd_id) && $this->brd_id > 0){
				$sql = '
					select
						max(prc.prc_value) as max_price_ht
					from
						prc_prices prc
					join
						prd_products prd
					on
							prd.prd_tnt_id = ' . $config['tnt_id'] . '
						and prd.prd_id = prc.prc_prd_id
						and prd.prd_brd_id = ' . $this->brd_id . '
					where
							prc.prc_tnt_id = '.$config['tnt_id'].'
						and prc.prc_is_deleted = 0
						and prc.prc_date_start <= now()
						and prc.prc_date_end >= now()
						and prc.prc_type_id = 1
				';

			}else{
				// On récupère les identifiants de catégories enfants
				$ar_cats = [$this->id];

				if( Template::get('catalog-slide-price-with-children') ){
					$ar_cats = array_merge( $ar_cats, prd_categories_childs_get_array( $this->id, true, true ) );
				}
				$sql = '
					select
						max(prc_value) as max_price_ht
					from
						prc_prices
					join
						prd_classify
					on
							cly_tnt_id = '.$config['tnt_id'].'
						and cly_prd_id = prc_prd_id
					where
							prc_tnt_id = '.$config['tnt_id'].'
						and prc_is_deleted = 0
						and prc_date_start <= now() and prc_date_end >= now()
						and prc_type_id = 1
						and cly_cat_id in ('.implode( ', ', $ar_cats ).')
				';
			}

			// On récupère le tarif, le plus élevés, en vigureur pour les articles classés dans la catégorie
			$r_price = ria_mysql_query($sql);

			if( $r_price && ria_mysql_num_rows($r_price) ){
				$price = ria_mysql_fetch_assoc( $r_price );
				$vat = Template::get('catalog-slide-price-type') == 'ttc';
				$this->pricerange['max'] = ceil($vat ? ($price['max_price_ht'] * _TVA_RATE_DEFAULT) : $price['max_price_ht']);
			}
		}

		// On récupère les filtres seulement si l'option est activé
		if( Template::get('catalog-filter-active') ){
			// récupère les filtres applicables sur une catégorie
			$cat_filters = prd_category_filters_get_bycat(
				$this->id, true, true, (count($this->prd_ids) ? $this->prd_ids : false), true, Template::get('catalog-filter-brand'),
				Template::get('catalog-filter-parent'), [], true, true, Template::get('catalog-filter-stock'),
				Template::get('catalog-filter-catchilds')
			);

			if( is_array($cat_filters) ){
				foreach ($cat_filters as $code => $data) {
					$this->filters->addItem([
						'code' => $code,
						'name' => $data['name'],
						'values' => $data['values'],
					]);
				}
			}
		}

		// Hook permettant de surcharger le filtre de disponibilité
		$hook->do_action( 'CategoryService_replaceStocksFilter', ['cat' => $this] );

		return $this;
	}

	/** Cette fonction permet de récupérer les filtres désactivés.
	 * 	@return array Un tableau contenant les valeurs/filtres désactivé
	 */
	public function getDisabledFilters(){
		$ar_disabled = [];
		if( $this->filters->length() ){
			foreach( $this->filters->getAll() as $one_filter ){
				foreach( $one_filter['values'] as $one_value ){
					if( $one_value['disabled'] ){
						if( !isset($ar_disabled[ $one_filter['code'] ]) ){
							$ar_disabled[ $one_filter['code'] ] = [];
						}

						$ar_disabled[ $one_filter['code'] ][] = $one_value['id'];
					}
				}
			}
		}

		return $ar_disabled;
	}

	/** Cette fonction permet de charger le fil d'Arinae pour la catégorie.
	 * 	@return Category L'objet courant
	 */
	public function breadcrumbs(){
		global $config;

		if( is_numeric($this->id) && $this->id > 0 && $this->breadcrumbs === null ){
			// Charge les catégories parents
			$r_parent = ria_mysql_query('
				select cat_id as id, if(ifnull(cat_title, "") != "", cat_title, cat_name) as title, cat_url_alias as url
				from prd_categories as c
					join prd_cat_hierarchy as h on (h.cat_tnt_id = '.$config['tnt_id'].' and h.cat_parent_id = c.cat_id)
				where c.cat_tnt_id = '.$config['tnt_id'].'
					and cat_date_deleted is null
						and h.cat_child_id = '.$this->id.'
				order by cat_parent_depth asc
			');

			if( $r_parent ){
				$this->breadcrumbs = new Collection();

				while( $parent = ria_mysql_fetch_assoc($r_parent) ){
					if( $parent['id'] == $config['cat_root'] ){
						continue;
					}

					// Ne pas afficher la catégorie "Offres spéciales" dans le fil d'Ariane
					if( $parent['id'] == Template::get('catalog-category-special') ){
						continue;
					}

					$this->breadcrumbs->addItem([
						'id' => $parent['id'],
						'url' => rew_strip( $parent['url'] ),
						'text' => $parent['title']
					]);
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet d'activer des filtres lors de la récupération de produits
	 * 	Elle s'appuit automatiquement sur les paramètres envoyés en méthode GET.
	 * 	Il est possible de forcer un filtre en le donnant en paramètre. Voici la liste des filtres autorisés :
	 * 			- brand : identifiant ou tableau d'identifiant de marque
	 * 			- fld : tableau (fld_id => [value])
	 * 			- prcmin : prix minimal
	 * 			- prcmax : prix maximal
	 * 			- availability :
	 * 				- 1 (have_stock) : détermine si le produit est disponible ou non
	 * 				- 2 (ordered) : détermine si le produit est prochainement disponible ou non
	 *
	 * 	@param string $code Optionnel, code d'un filtre
	 * 	@param mixed $value Optionnel, valeur du filtre
	 *
	 * 	@return object L'object courant
	 */
	public function setFilters( $code='', $value=null ){
		if( trim($code) != '' ){
			if( $value === null && array_key_exists($code, $this->applyfilters) ){
				unset( $this->applyfilters[$code] );
			}else{
				if( array_key_exists($code, $this->applyfilters) && is_array($this->applyfilters[$code]) && is_array($value) ){
					$this->applyfilters[ $code ] = $this->applyfilters[$code] + $value;
				}else{
					$this->applyfilters[$code] = $value;
				}
			}
		}else{
			// Filtre sur la marque
			if( isset($_GET['brand']) ){
				$temp = control_array_integer( $_GET['brand'], true );
				if( $temp !== false ){
					$this->applyfilters['brand'] = $temp;
				}
			}

			// Filtre sur les sous-familles
			if( isset($_GET['catchilds']) ){
				$temp = control_array_integer( $_GET['catchilds'], true );
				if( $temp !== false ){
					$this->applyfilters['catchilds'] = $temp;
				}
			}

			// Filtre sur la disponibilité
			if( isset($_GET['availability']) ){
				if( in_array( 1, $_GET['availability'] ) ){
					$this->applyfilters['have_stock'] = true;
					$this->applyfilters['availability'][] = 1;
				}
				if( in_array( 2, $_GET['availability'] ) ){
					$this->applyfilters['ordered'] = true;
					$this->applyfilters['have_stock'] = false;
					$this->applyfilters['availability'][] = 2;
				}

				if( in_array(1, $_GET['availability']) && in_array(2, $_GET['availability']) ){
					$this->applyfilters['have_stock'] = true;
					$this->applyfilters['ordered'] = true;
					$this->applyfilters['have_stock_or_ordered'] = true;
				}
			}

			// Filtre sur la disponibilité
			if( isset($_GET['have_stock']) ){
				$temp = control_array_integer( $_GET['have_stock'], true );
				if( $temp !== false ){
					$this->applyfilters['have_stock'] = $temp;
				}
			}

			// Filtre sur les champs avancés
			if( isset($_GET['fld']) && is_array($_GET['fld']) ){
				foreach( $_GET['fld'] as $fld_id => $one_fld ){
					if( (is_array($one_fld) && count($one_fld)) || trim($one_fld) != '' ){
						$this->applyfilters['fld'][ $fld_id ] = $one_fld;
					}
				}
			}

			// Filtre sur les tarifs
			if( isset($_GET['value-range']) ){
				if( !isset($this->pricerange['min']) ){
					$this->pricerange['min'] = 0;
				}
				if( !isset($this->pricerange['max']) ){
					$this->pricerange['max'] = 1000;
				}

				$prc_range = explode( '/', $_GET['value-range'] );
				if( is_array($prc_range) && count($prc_range) ){
					// N'applique pas le filtre sur les prix pour un peu optimiser les performances quand on ne filtre pas par prix
					if ($prc_range[0] > $this->pricerange['min'] || $prc_range[1] < $this->pricerange['max']) {
						$this->applyfilters['prcmin'] = $prc_range[0];
						$this->applyfilters['prcmax'] = $prc_range[1];

						$this->pricerange['acmin'] = $prc_range[0];
						$this->pricerange['acmax'] = $prc_range[1];
					}
				}
			}

			if( isset($_GET['fltstock']) && is_array($_GET['fltstock']) && count($_GET['fltstock']) > 0  ){
				$tmp_fltstock = $_GET['fltstock'];
				$this->applyfilters['fltstock'] = array_shift( $tmp_fltstock );
				unset($tmp_fltstock);
			}
		}

		// Récupère la liste des produits filtrés
		// Seulement si des filtres sont activé
		if( $this->filters->length() ){
			$ar_prd_ids = [];
			if( count($this->applyfilters) ){
				$ar_prd_ids = $this->products( 0, false, false, true );
			}

			// Met à jour la liste des filtres applications sur la catégorie
			// On parcours toutes les valeurs pour renseigner si oui ou non elle est appliquée à la liste des produits
			$ar_filters = new Collection();
			foreach( $this->filters->getAll() as $one_filter ){
				foreach( $one_filter['values'] as $key=>$val ){
					$selected = $disabled = $used = false;

					if( preg_match('/^fld\[([0-9]+)\]$/', $one_filter['code'], $matches) ){
						// Il s'agit d'un filtre sur des champs avancés
						if( is_array($matches) && count($matches) == 2 ){
							// $matches contient en deuxième ligne l'identifiant du champ avancé
							if( isset($this->applyfilters['fld'][$matches[1]]) ){
								// On marque le filtre comme étant appliqué
								$used = true;

								if( is_array($this->applyfilters['fld'][$matches[1]]) ){
									$selected = in_array( $val['id'], $this->applyfilters['fld'][$matches[1]] );
								}else{
									$selected = $this->applyfilters['fld'][$matches[1]] == $val['id'];
								}
							}
						}
					}else{
						// On regarde si la valeur du filtre est utilisé
						if( array_key_exists($one_filter['code'], $this->applyfilters) ){
							// On marque le filtre comme étant appliqué
							$used = true;

							if( is_array($this->applyfilters[$one_filter['code']]) ){
								$selected = in_array( $val['id'], $this->applyfilters[$one_filter['code']] );
							}else{
								$selected = $this->applyfilters[$one_filter['code']] == $val['id'];
							}
						}
					}

					// La valeur est désactivé si le filtre n'est pas utilisé et qu'aucun produit utilisé cette valeur
					if( count($this->applyfilters) && isset($val['ids']) ){
						$ids_can_used = explode( ',', $val['ids'] );
						$can_used = array_intersect( $ids_can_used, $ar_prd_ids );

						if( !$used || !Template::get('catalog-filter-used-active') ){
							if( !is_array($can_used) || !count($can_used) ){
								$disabled = true;
							}

							// Mise à jour du nombre d'article par valeur
							$one_filter['values'][$key]['count'] = count( $can_used );
						}

					}
					$one_filter['values'][$key]['selected'] = (  $selected ? 1 : 0 );
					$one_filter['values'][$key]['disabled'] = (  $disabled ? 1 : 0 );
				}

				$ar_filters->addItem( $one_filter );
			}

			$this->filters = $ar_filters;
		}

		return $this;
	}

	/** Cette fonction permet d'appliquer une restriction sur une liste d'identifiatn de produtis.
	 * 	@param $prd_ids Obligatoire, tableau d'identifiant de produits (peut-être vide)
	 * 	@return CategoryService L'objet courant
	 */
	public function setPrdListID( $prd_ids ){
		$this->prd_ids = $prd_ids;
		return $this;
	}

	/** Cette fonction permet de mettre à jour l'information comme quoi la catégorie doit-être marquée comme active dans le menu.
	 * 	@param bool $is_active Obligatoire si oui ou non la catégorie est active
	 * 	@return CategoryService L'objet courant
	 */
	public function setIsMenuActived( $is_active ){
		$this->ismenuactive = $is_active;
		return $this;
	}

	/** Cette fonction permet de mettre à jour le titre de la catégorie.
	 * 	@param string $title Obligatoire, nouveau titre
	 * 	@return CategoryService L'objet courant
	 */
	public function setTitle( $title ){
		$this->title = $title;
		return $this;
	}

	/** Cette fonction permet de mettre à jour l'information déterminant si la catégorie dispose de nouveautés.
	 * 	@param string $have_new Obligatoire, détermine si la catégorie dispose de nouveauté (valeur acceptée : 'yes' ou 'no')
	 * 	@return CategoryService L'objet courant
	 */
	public function setHaveNewPrds( $have_new ){
		if( !in_array($have_new, ['yes', 'no']) ){
			$have_new = 'no';
		}

		$this->have_new_prds = $have_new;
		return $this;
	}

	/** Cette fonction permet d'exclure une partie des produits par champs avancés
	 * 	@param array $fld Obligatoire, tableau des champs avancés pour chaque entrée la clé est l'identifiant de champs
	 * 	@return CategoryService L'objet courant
	 */
	public function excludeFields( $fld ){
		global $config;

		if( is_array($fld) && count($fld) ){
			// Recherche des articles répondant au critère d'exclusion
			$sql = '
				select prd_id
				from prd_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_date_deleted is null
			';

			$sql .= fld_classes_sql_get( CLS_PRODUCT, $fld, false, true );
			$r_exclude = ria_mysql_query( $sql );
			if( $r_exclude ){
				while( $exclude = ria_mysql_fetch_assoc($r_exclude) ){
					$this->exclude_prd_ids[] = $exclude['prd_id'];
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de retourner l'identifiant d'une catégorie.
	 * 	@return int L'identifiant de la catégorie
	 */
	public function getID(){
		return $this->id;
	}

	/**	Vérifie si la catégorie existe
	 * @return	bool	True si la catégorie existe, false sinon
	 */
	public function exists(){
		return is_numeric($this->id) && $this->id > 0;
	}

	/** Cette fonction permet de retourner le nombre de produits dans la catégories
	 * 	@return int L'attribut 'count_prds' contenant le nombre de produits
	 */
	public function getCountProducts(){
		return $this->count_prds;
	}

	/** Cette fonction permet de retourner l'attribut 'prds' contenant la collection de produit
	 * 	@return Collection L'attribut 'getPrds' contenant la collection de produit
	 */
	public function getPrds(){
		return $this->prds;
	}

	/** Cette fonction permet de retourner la propriété contenant les catégories enfants.
	 *	@param bool $get_attr Optionnel, par défaut un tableau est retourné, mettre true pour retourner la collection
	 * 	@return array Le tableau des catégories enfants
	 */
	public function getChildren( $get_attr=false ){
		if( !is_a($this->children, 'Collection') ){
			return [];
		}
		return $this->children->getAll();
	}

	/** Cette fonction permet de récupérer la propriété 'breadcrumbs' contenant le fil d'Ariane.
	 * 	@return array La propriété 'breadcrumbs'
	 */
	public function getBreadcrumbs(){
		if( !is_a($this->breadcrumbs, 'Collection') ){
			return [];
		}

		return $this->breadcrumbs->getAll();
	}

	/** Cette fonction permet de récupérer la propriété 'url' de la catégorie.
	 * 	@return array La propriété 'url'
	 */
	public function getUrl(){
		return $this->url;
	}

	/** Cette fonction permet de récupérer la propriété 'title' de la catégorie.
	 * 	@return array La propriété 'title'
	 */
	public function getTitle(){
		return $this->title;
	}

	/** Cette fonction permet de récupérer la propriété 'filters' de la catégorie.
	 * 	@return Collection Un objet de type collection contenant les filtres
	 */
	public function getCollectionFilters(){
		return $this->filters;
	}

	/** Cette fonction permet de mettre à jour la propriété 'filters' de la catégorie.
	 * 	@param Collection $filters Obligatoire, nouvelle collection de filtres
	 * 	@return Collection Un objet de type collection contenant les filtres
	 */
	public function setCollectionFilters(Collection $filters){
		return $this->filters = $filters;
	}

	/** Cette fonction permet de charger les articles marqués comme nouveau.
	 * 	@param $limit Optionnel, permet de spécifier une limite au nombre de produits retournés
	 * 	@param $with_price Optionnel, permet de charger les tarifs en même temps
	 * 	@return array Un tableau contenant toutes les informations nécessaire à un affichage
	 */
	public function getNewCategory( $limit=0, $with_price=false ){
		global $config;

		$ar_new = new Collection();

		$r_product = prd_products_get_simple(0, '', true, $config['cat_root'], true, false, $with_price, false, array('maxrows' => $limit));
		if( $r_product ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$product = i18n::getTranslation(CLS_PRODUCT, $product, false, i18n::getLang(), true);

				$obj_prd = new ProductService([
					'prd' => $product['id'],
					'withprice' => true,
					'ref' => $product['ref'],
					'title' => $product['title'],
					'desc' => $product['desc'],
					'longdesc' => $product['desc-long'],
					'new' => $product['new'],
				]);

				$ar_new->addItem( $obj_prd->card() );
			}
		}

		return $this->transformObjectToArray($ar_new);
	}

	/** Charge les images
	 * @param	string	$cfg Optionnel, Code images
	 * @param	bool	$main Optionnel, True pour charger uniquement les images principales
	 * @return	object	L'objet en cours
	 */
	public function loadImages($cfg='', $main=true){
		global $config;

		// Détermine la limite
		$main = is_bool($main) ? $main : true;
		$limit = $main ? 1 : 0;
		// Récupération des identifiants des images
		$imgs = prd_cat_images_get($this->id, 0, $limit);
		$thumb = $config['img_sizes']['high'];

		if( trim($cfg) != '' && array_key_exists($cfg, $config['img_sizes']) ){
			$thumb = $config['img_sizes'][$cfg];
		}

		if( !ria_mysql_num_rows($imgs) ){
			return $this;
		}
		$this->images = new Collection();

		while( $img = ria_mysql_fetch_assoc($imgs) ){
			$this->images->addItem([
				'id'		=> $img['id'],
				'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$img['id'].'.'.$thumb['format'],
				'alt'		=> $this->title,
				'width'		=> $thumb['width'],
				'height'	=> $thumb['height'],
			]);

		}

		// Renseigne l'image principale du produit
		if( $this->images->length() ){
			$this->main_image = $this->images->getFirstItem();
		}
		return $this;

	}
	/** Charge le tri apliqué sur les produits
	 * @param int $cat Obligatoire, identifiant de la catégorie
	 * @return array Le tri appliqué sur les produits dans la catégorie | false si erreur ou catégorie non valide
	 */
	public function getProductSort($cat){

		$params_sort = false;

		if(isset($cat) && is_numeric($cat) && $cat > 0){
			$params_sort = prd_categories_sort_get($cat);
		}

		return $params_sort;

	}
}
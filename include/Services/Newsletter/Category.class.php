<?php

require_once('Services/Service.class.php');
require_once('Services/News/News.class.php');
require_once('news.categories.inc.php');

/**	\brief Cette classe permet de charger les informations sur une catégorie de newsletter.
 *
 */
class NewsletterCategory extends Service {
	protected $id; ///< Identifiant de la catégorie de newsletter
	protected $name; ///< Nom de la catégorie de newsletter
	protected $desc; ///< Description de la catégorie de newsletter

	/** Cette fonction créé un objet permettant de charger les informations sur une catégorie.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- cat : identifiant de la catégorie de newsletter
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'cat', 0 );
	}

	/** Cette fonction permet de charger les informations générales de la catégorie de newsletter.
	 * 	@return NewsletterCategory L'objet courant
	 */
	public function general(){
    global $config;

		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('La catégorie de newsletter n\'est pas identifiée.');
		}

		$r_cat = nlr_categorie_get( $this->id, $config['wst_id'] );
		if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
			throw new Exception('La catégorie de newsletter n\'existe pas ou plus.');
		}

		$cat = i18n::getTranslation( CLS_NLR_CATEGORY, ria_mysql_fetch_assoc($r_cat) );

		$this->name = $cat['cat'];
		$this->desc = $cat['desc'];

    return $this;
  }

	/** Cette fonction permet de récupérer la liste des catégories d'actualités.
	 * 	@return array Un tableau contenant les inforamtions sur les catégories d'actualités
	 */
	public static function all(){
    global $config;

		$ar_cat = new Collection();

		// Récupère toutes les catégories
		$r_cat = nlr_categorie_get( 0, $config['wst_id'] );

		if( $r_cat ){
			while( $cat = ria_mysql_fetch_assoc($r_cat) ){
				$obj_cat = new NewsletterCategory( ['cat' => $cat['id']] );
				$ar_cat->addItem( $obj_cat->general() );
			}
		}

		return self::transformObjectToArray( $ar_cat );
	}
}
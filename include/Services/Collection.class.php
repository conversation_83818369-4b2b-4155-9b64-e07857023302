<?php

/**	\brief Cette classe permet des listes d'objets
 *
 */
class Collection {
	private $items = []; ///< Liste d'objet

	/** Cette fonction permet de vérifier qu'une clé n'est pas déjà présente.
	 * 	@param $key Obligatoire, clé à tester
	 * 	@return true si elle existe déjà, false dans le cas contraire
	 */
	public function exitsKey( $key ){
		return isset( $this->items[$key] );
	}

	/**	Permet d'ajouter un item à la collection
	 * @param	mixed	$obj Obligatoire, objet à ajouter
	 * @param	string	$key Optionnel, clé attribué à l'objet
	 */
	public function addItem( $obj, $key = null ){
		if( $key == null ){
			$this->items[] = $obj;
			return;
		}

		if( $this->exitsKey($key) ){
			// Une execption est levée si la clé est déjà utilisé
			throw new Exception('Key '.$key.' already in use.');
		}
		$this->items[$key] = $obj;

	}

	/**	Permet de mettre à jour un item de la collection
	 * @param	mixed		$data	Obligatoire, Valeur associée à la clé
	 * @param	string|int	$key	Obligatoire, Clé de l'item à mettre à jour
	 */
	public function updateItem($data, $key){

		if( !$this->exitsKey($key) ){
			throw new Exception('Undefined key');
		}
		$this->items[$key] = $data;

	}

	/** Cette fonction permet d'ajouter en début de la liste d'objet.
	 * 	\param $obj Obligatoire, objet à ajouter
	 */
	public function addFirstItem( $obj ){
		array_unshift($this->items, $obj);
	}

	/** Cette fonction permet de supprimer un élément de la liste d'objet.
	 * 	\param $key Obligatoire, clé de l'objet à supprimer
	 */
	public function deleteItem( $key ){
		if( isset($this->items[$key]) ){
			unset($this->items[$key]);
		}else{
			// Une exception est levée si la clé n'existe pas
			throw new Exception('Invalid key '.$key.'.');
		}
	}

	/** Cette fonction permet de récupérer un objet de la liste.
	 * 	\param $key Obligatoire, clé de l'objet à retrouner
	 * 	\return L'objet correspondant à la clé
	 */
	public function getItem( $key ){
		if( isset($this->items[$key]) ){
			return $this->items[$key];
		}else{
			// Une exeption est levée si la clé n'existe pas
			throw new Exception("Invalid key $key.");
		}
	}

	/** Cette fonction permet de récupérer le premier éléments de la liste d'objet.
	 * 	@return mixed Le premier objet dans la liste
	 */
	public function getFirstItem(){
		if( $this->length() ){
			$temp = $this->items;
			return array_shift( $temp );
		}else{
			throw new Exception("List is empty.");
		}
	}

	/** Cette fonction permet de récupérer toute la collection.
	 * 	@return Collection Toute la collection
	 */
	public function getAll(){
		return $this->items;
	}

	/** Cette fonction permet de compter le nombre d'objet dans une liste.
	 * 	@return int Le nombre d'objet
	 */
	public function length(){
		return count($this->items);
	}

	/** Cette fonction permet de renverser l'ordre de la collection
	 * 	@return empty
	 */
	public function reverse(){
		$this->items = array_reverse( $this->items );
	}
}
<?php

require_once 'Services/Service.class.php';

abstract class AbstractStores extends Service
{

	/**
	 * Tableau contenant les paramétres de dlv_get_store()
	 */
	private $ar_params = [];

	/**	Permet d'ajouter des paramètres lors de la récupération des données du/ des magasin.s
	 * @see		paramètres acceptés par dlv_stores_get
	 * @param	string	$var Obligatoire, nom de la variable
	 * @param	mixed	$value Obligatoire, valeur de la variable
	 * @return	object	L'object en cours, sinon false
	 */
	public function setParameter($var, $value)
	{

		if (!is_string($var)) {
			throw new Exception('$var doit être un nom de variable valide');
		}
		$var = strtolower(trim($var));

		if (!array_key_exists($var, $this->ar_params)) {
			throw new Exception('$var doit être un nom de variable valide');
		}
		// $typeof = gettype($this->ar_params[$var]);

		// if( $typeof != gettype($value) ){
		// 	throw new Exception('$value doit être une valeur de type '.$typeof.' pour la variable '.$var);
		// }
		$this->ar_params[$var] = $value;

		return $this;
	}

	/**	Récupére la valeur d'un paramètre
	 * @param	string	$param	Obligatoire, nom du paramètre
	 * @return	mixed	Valeur du paramètre, null si non défini
	 */
	protected function getParameter($param)
	{
		return !is_string($param) || !array_key_exists($param, $this->ar_params) ? null : $this->ar_params[$param];
	}

	/**
	 * Permet de mettre les paramétres par défaut
	 * @return object	L'object en cours
	 */
	public function resetParameters()
	{

		$this->ar_params = [
			'allow_delivery'		=> null,
			'sort'					=> [],
			'sector'				=> 0,
			'dept'					=> 0,
			'coordinates'			=> [],
			'zipcode'				=> 0,
			'country'				=> '',
			'start_row'				=> 0,
			'limit'					=> 0,
			'fld'					=> false,
			'or_between_val'		=> false,
			'or_between_fld'		=> false,
			'sales_types'			=> [],
			'name'					=> false,
			'is_like'				=> false,
			'publish'				=> true,
			'email'					=> '',
			'seg_id'				=> 0,
			'continent'				=> '',
			'brands'				=> [],
			'zones'					=> false,
			'website'				=> null,
			'city'					=> '',
			'sales_types_or'		=> true,
			'is_clickandcollect'	=> null,
			'like_type_fld'			=> false,
			'lng'					=> false,
			'alias_tbl'				=> '',
			'check_on_childs'		=> '',
			'distances'				=> [],
			'images'				=> false, // True pour récupérer les images
			'only_main_img'			=> false, // True s'il faut récupérer uniquement l'image principale
			'cfg_img'				=> 'high', // Config taille des images
			'timetable'				=> false, // @TODO True pour récupérer la grille des horaires d'ouverture
			'time_slot'				=> '', // @TODO Pattern qui crée le créneau horaire
			'ezoom'					=> 7, // Zoom pour la carte embed Google
		];

		return $this;
	}

	/**
	 * Retourne le tableau des paramètres
	 * @return	array	Le tableau des paramètres
	 */
	protected function getParameters()
	{
		return $this->ar_params;
	}
}

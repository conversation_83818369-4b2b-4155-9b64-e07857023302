<?php

require_once 'Services/Stores/Store.class.php';
require_once 'Services/Stores/AbstractStores.class.php';

class StoresService extends AbstractStores {

	/**
	 * Nombre de magasins trouvés
	 */
	protected $length = 0;

	/**
	 * Liste des magasins
	 */
	protected $stores = null;

	/**
	 * Tableau des lignes constituantes l'adresse
	 */
	private $adr_lines = [];

	/**
	 * Séparateur des lignes des adresses
	 */
	private $adr_separator = '-';

	/**
	 * Initialise StoresService
	 */
	public function __construct(){

		// Obligatoire
		$this->resetParameters();
		$this->stores = new Collection();

	}

	/**
	 * Récupére les données des magasins
	 */
	public function loadStores(){
		// Construit les variables à passer en paramètre de dlv_stores_get()
		extract(parent::getParameters());

		$r_stores = dlv_stores_get(0, $allow_delivery, $sort, $sector, $dept, $coordinates, $zipcode, $country, $start_row, $limit, $fld, $or_between_val, $or_between_fld, $sales_types, $name, $is_like, $publish, $email, $seg_id, $continent, $brands, $zones, $website, $city, $sales_types_or, $is_clickandcollect, $like_type_fld, $lng, $alias_tbl, $check_on_childs);
		$this->length = ria_mysql_num_rows($r_stores);
		$this->length = !is_numeric($this->length) ? 0 : (int)$this->length;

		if( !$this->length ){
			return $this;
		}
		$i = 0;
		$with_all_images = isset($images) && is_bool($images) && $images;
		$with_main_img_only = isset($only_main_img) && is_bool($only_main_img) && $only_main_img;
		$with_images = $with_all_images || $with_main_img_only;

		$with_timetable = isset($timetable) && is_bool($timetable) && $timetable;

		while( $store = ria_mysql_fetch_assoc($r_stores) ){
			$store = i18n::getTranslation(CLS_STORE, $store);
			$store['ezoom'] = $ezoom;
			$Store = new StoreService($store);

			// Crée l'addresse complète du magasin
			$Store->buildAddress($this->adr_lines, $this->adr_separator);

			// Charge les images
			if( $with_images ){
				$Store->getImages($with_main_img_only);
			}

			// Charge les horaires
			if( $with_timetable ){
				$Store->loadTimePeriod();

			}
			$this->stores->addItem($Store);

		}
		return $this;

	}

	/**
	 * Détermine les lines constituantes les adresses des magasins
	 * @param	array	$lines		Optionnel, lignes constituantes l'adresse, l'ordre est important (exemple: ['address1', 'zipcode city', 'country'])
	 * @param	string	$separator	Optionnel, séparateur des différentes lignes
	 * @param	object	L'object en cours
	 */
	public function defineAddressLines($lines=[], $separator=' - '){
		$this->adr_lines = is_array($lines) ? $lines : [];
		$this->adr_separator = is_string($separator) ? $separator : '-';

		return $this;

	}

}
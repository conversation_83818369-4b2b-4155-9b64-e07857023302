<?php
require_once 'Services/Collection.class.php';
require_once 'Services/Catalog/Product.class.php';

/**	\brief Cette classe permet d'analyser et retourner un tableau de produits trouvés lors d'une saisie dans "commande rapide".
 */
class BatchService {

	/** Tableau de types de fichier (MIMETYPE)
	 * @var	array|bool
	 */
	private $file_types = false;

	/** Collection des produits analysés
	 * @var	object
	 */
	private $found = null;

	/**	Collection des produits trouvés
	 * @var	object
	 */
	private $products = null;

	/**	Tableau des références produits supprimées
	 * @var	array
	 */
	protected $prds_deleted = [];

	/**	Tableau des références produits dont la quantité a été ajustée en fonction du stock disponible
	 * @var	array
	 */
	protected $prds_stock = [];

	/** Initialise la classe
	 * @param	string	$area		Optionnel, références/ codes EAN des produits espacés des quantités (une ligne = un produit + quantité). Exemple:
	 * 									REFERENCE_ONE 12
	 * 									REFERENCE_TWO 8
	 * 									REFERENCE_THREE 92
	 *
	 * @param	array	$file		Optionnel, Tableau contenant les informations du fichier ($_FILES)
	 * @param	array	$file_types	Obligatoire si l'analyse par fichier est autorisée, défaut: false
	 */
	public function __construct($area='', $file=[], $file_types=false){

		$this->products = new Collection();
		$this->found = new Collection();

		if( is_array($file_types) && count($file_types) ){
			$this->file_types = new Collection();

			$this->file_types->addItem('application/octet-stream');

			foreach($file_types as $type){

				if( !is_string($type) ){
					continue;
				}

				switch( trim(strtolower($type)) ){
					case 'text/csv':
					case 'csv':
						$type = 'text/csv';
						break;
					case 'text/plain':
					case 'text':
					case 'texte':
					case 'txt':
						$type = 'text/plain';
						break;
					default:
						$type = false;

				}

				if( !$type ){
					continue;

				}
				$this->file_types->addItem($type);
			}
			$this->file_types = $this->file_types->length() ? $this->file_types->getAll() : false;

		}
		$this->parseString($area); // Analyse la zone de saisie
		$this->parseFile($file); // Analyse le fichier

	}

	/** Analyse une chaine de caractères
	 * @param	string	$str	Obligatoire, chaine de caractères
	 * @param	string	$type	Optionnel, origine de la chaine de caractère (file|area) défaut = area
	 * @return	object	L'objet en cours, Une execption sera levée en cas d'erreur
	 */
	private function parseString($str, $type='area'){
		$type = is_string($type) ? trim( strtolower($type) ) : 'area';
		$isFile = $type === 'file';

		if( !is_string($str) ){
			return $this;
		}

		$str = trim( strip_tags($str) );

		if( strlen($str) < 1 ){
			return $this;
		}

		$rows = explode( "\n", $str );

		if( !count($rows) ){
			throw new Exception( i18n::get('La saisie n\'est pas correctement structurée. Veuillez réessayer.'), 1);
		}

		foreach( $rows as $row ){
			if( !is_string($row) && !is_numeric($row) ){
				continue;
			}

			foreach( [' ', ';', ','] as $delimiter ){
				$prd = explode( $delimiter, str_replace( ' ', ' ', trim( $row ) ) );

				if( count($prd) !== 2 || (int)$prd[1] < 1 ){
					continue;
				}else{
					break;
				}
			}

			$data = [
				'prd'	=> preg_replace('/\"\'/', '', $prd[0]),
				'qte'	=> (int)$prd[1]
			];

			$this->found->addItem($data);
			$this->addProduct($data);
		}

		return $this;
	}

	/** Analyse le contenu d'un fichier
	 * @param	array	$file	Optionnel, Tableau contenant les informations du fichier ($_FILES['example'])
	 */
	private function parseFile($file=[]){
		if( !is_array($file) || !count($file) ){
			return $this;
		}

		if( !$this->file_types ){
			throw new Exception( i18n::get('Aucun type de fichier à analyser.', 'BATCH'), 2);
		}

		$ar_used = ['name', 'type', 'size', 'tmp_name'];

		foreach($ar_used as $key){
			if( !array_key_exists($key, $file) || (!is_string($file[$key]) && !is_numeric($file[$key])) ){
				throw new Exception( i18n::get('Le fichier à analyser n\'est pas valide. Veuillez réessayer.', 'BATCH'), 3);
			}
		}

		if( $file['size'] == 0 ){
			return $this;
		}

		if( !in_array($file['type'], $this->file_types) ){
			throw new Exception( sprintf( i18n::get('Le fichier doit être au format %s.', 'BATCH'), join(', ', $this->file_types) ), 4);

		}

		$content = file_get_contents($file['tmp_name']);
		$this->parseString($content, 'file');

		return $this;
	}

	/** Recherche et ajoute un produit à la collection si les conditions sont réunies
	 * @param	array	$one	Obligatoire, Tableau contenant:
	 * 								- prd => Référence/ code EAN d'un produit
	 * 								- qte => Quantité souhaitée
	 * @return void
	 */
	private function addProduct($one){
		global $config, $hook;

		if( !is_array($one) || !isset($one['prd'], $one['qte']) || !is_numeric($one['qte']) || $one['qte'] < 1 ){
			return;
		}

		$one['prd'] = is_string($one['prd']) ? trim($one['prd']) : $one['prd'];
		$r = prd_products_get_byref( $one['prd'], false, false );

		if( !ria_mysql_num_rows($r) ){
			// Dans le cas de Chadog, les références peuvent être préfixé par un 1- dont le client ignore l'existance
			switch( $config['tnt_id'] ){
				case 171: {
					$one['prd'] = '1-'.$one['prd'];
					$r = prd_products_get_byref($one['prd'], false, false);

					if( !ria_mysql_num_rows($r) ){
						if( !preg_match('/^([0-9]{8}|[0-9]{13}|[0-9]{128})$/i', $one['prd']) ){
						}
					}
					break;
				}
				default:{
					if( !preg_match('/^([0-9]{8}|[0-9]{13}|[0-9]{128})$/i', $one['prd']) ){
					}

					$r = prd_products_get_by_barcode($one['prd']);
					if( !ria_mysql_num_rows($r) ){
						$this->prds_deleted[] = $one['prd'];
					}
					break;
				}
			}

		}

		while($prd = ria_mysql_fetch_assoc($r) ){
			if( !prd_products_exists($prd['id'], true) || prd_products_is_port($prd['ref']) ){
				$this->prds_deleted[] = $prd['ref'];
				continue;
			}

			$Product = $hook->apply_filter('BatchService_filterLoadProduct', false, ['Batch' => $this, 'prd' => $prd]);

			// Si le hook n'existe pas, n'a pas fonctionné ou n'a pas retourné de données
			if( !$Product ){
				$Product = new ProductService([
					'prd'		=> $prd['id'],
					'withprice'	=> true
				]);

				$Product->card();
			}
			$stock = $Product->getStock();

			if( $stock < 1 ){
				$this->prds_deleted[] = $prd['ref'];
				continue;
			}
			$qty = $one['qte'];

			if( $qty > $stock ){
				$this->prds_stock[] = $prd['ref'];
				$qty = $stock;
			}
			$product = $Product->getData();
			$product['quantity'] = $qty;
			$this->products->addItem( $product );
		}
	}

	/** Tableau des produits trouvés
	 * @return	array	Tableau des produits trouvés
	 */
	public function products(){
		return $this->products->getAll();
	}

	/** Nombre de produits retournés
	 * @return	int	Nombre de produits retournés
	 */
	public function length(){
		return $this->products->length();
	}

	/** Nombre initial de produits
	 * @return	int	Nombre initial de produits
	 */
	public function sourceLength(){
		return $this->found->length();
	}

	/**	Retourne les références produits qui ont été supprimées
	 * @return	array|bool	Tableau des références produits qui ont été supprimées, false sinon
	 */
	public function getDeletedProducts(){
		return is_array($this->prds_deleted) && count($this->prds_deleted) > 0 ? $this->prds_deleted : false;
	}

	/**	Retourne les références produits dont la quantité a été ajustée en fonction du stock disponible
	 * @return	array|bool	Tableau des références produits dont la quantité a été ajustée en fonction du stock disponible, false sinon
	 */
	public function getAdjustedQtyProducts(){
		return is_array($this->prds_stock) && count($this->prds_stock) > 0 ? $this->prds_stock : false;
	}

}
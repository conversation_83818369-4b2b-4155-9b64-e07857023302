<?php
require_once 'Services/Collection.class.php';
require_once 'Services/News/Category.class.php';

class NewsCategories extends Service
{
	/**	L'instance de la classe
	 * @var	null|NewsCategories
	 */
	private static $instance = null;

	/**	Objet contenant les catégories d'actualité
	 * @var	null|Collection
	 */
	protected	$categories = null;

	/**	Permet de récupérer l'instance de la classe en cours
	 * @return	NewsCategories	L'instance en cours
	 */
	public static function getInstance()
	{
		if (is_null(self::$instance)) {
			self::$instance = new NewsCategories();
		}
		return self::$instance;
	}

	/**	Constructeur de la classe, permet le chargement de toutes les catégories d'actualité publiées
	 * @return	void
	 */
	private function __construct()
	{
		$rcategories = news_categories_get(0, true, false);

		if (!ria_mysql_num_rows($rcategories)) {
			return $this;
		}

		$this->categories = new Collection();

		while ($category = ria_mysql_fetch_assoc($rcategories)) {
			$data = [
				'cat'		=> $category['id'],
				'name'		=> $category['name'],
				'desc'		=> $category['desc'],
				'news'		=> $category['news'],
				'url_alias'	=> $category['url_alias']
			];

			$Category = new NewsCategory($data);

			$this->categories->addItem($Category);
		}
	}
}

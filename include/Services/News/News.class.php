<?php

require_once('Services/Service.class.php');
require_once('news.inc.php');

/**	\brief Cette classe permet de charger les informations sur une actualité
 *
 */
class News extends Service {
	protected $id; ///< Identifiant de la actualité
	protected $name; ///< Nom de la actualité
	protected $intro; ///< Introduction de l'actualité
	protected $desc; ///< Contenu de l'actualité
	protected $date; ///< Date de publication de l'actualité
	protected $end; ///< Date de fin de publication
	protected $url; ///< URL donnant accès à cette actualité
	protected $images; ///< Identifiant de l'image principale
	protected $catid; ///< Identifiant de la catégorie d'actualité où celle-ci est classée
	protected $catname; ///< Nom de la catégorie d'actualité où celle-ci est classée
	protected $fields = []; ///< Champs avancés liés à l'atualité

	/** Cette fonction créé un objet permettant de charger les informations sur une catégorie.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- cat : identifiant de la actualité
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'news', 0 );
		$this->name = ria_array_get( $data, 'name', '' );
		$this->intro = ria_array_get( $data, 'intro', '' );
		$this->desc = ria_array_get( $data, 'desc', '' );
		$this->date = ria_array_get( $data, 'date', '' );
		$this->end = ria_array_get( $data, 'end', '' );
		$this->url = ria_array_get( $data, 'url', '' );
		$this->catid = ria_array_get( $data, 'catid', 0 );
	}

	/** Cette fonction permet de charger les informations générales de la actualité.
	 * 	@return News L'objet courant
	 */
	public function general(){
		global $config;

		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('L\'actualité n\'est pas identifiée.', 1);
		}

		if( trim($this->name) == '' ){
			$r_news = news_get( $this->id, true, null, 0, $config['wst_id'] );
			if( !$r_news || !ria_mysql_num_rows($r_news) ){
				throw new Exception('L\'actualité n\'existe pas ou plus. => '.$this->id, 2);
			}

			$news = i18n::getTranslation( CLS_NEWS, ria_mysql_fetch_assoc($r_news) );

			$this->name = $news['name'];
			$this->intro = $news['intro'];
			$this->desc = $news['desc'];
			$this->date = $news['publish_date_en'];
			$this->end = $news['publish_date_end_en'];
			$this->url = $news['url_alias'];
			$this->catid = $news['cat_id'];
		}

		// Charge le nom de la catégorie dans laquelle l'actualité est classée
		if( is_numeric($this->catid) && $this->catid > 0 ){
			$r_cat = news_categories_get( $this->catid, true );
			if( $r_cat && ria_mysql_num_rows($r_cat) ){
				$cat = i18n::getTranslation( CLS_NEWS_CAT, ria_mysql_fetch_assoc($r_cat) );
				$this->catname = $cat['name'];
			}
		}

		$this->images = new Collection();

		// Charge les images de l'actualité
		$r_img = news_images_get( $this->id );
		if( $r_img ){
			while( $img = ria_mysql_fetch_assoc($r_img) ){
				$this->images->addItem( $img['id'] );
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les champs avancés liés
	 * @param	int|array	$id	Optionnel, Identifiant ou tableau d'identifiants de champs avancés sur lequel/ lesquel filtrer le résultat
	 * @return object L'instance en cours
	 */
	public function fields( $id=0 ){

		if( !$this->id ){
			return $this;
		}
		$r_fields = fld_fields_get( $id, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_NEWS );

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			// Transforme la valeur en tableau pour certains types de champs avancés
			// Liste de choix unique, Liste de choix multiple ou Liste de choix multiple hiérarchique
			if( in_array( $field['type_id'], [FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY]) ){
				$field['obj_value'] = explode( ', ', $field['obj_value'] );
			}

			$this->fields[ 'field'.$field['id'] ] = [
				'id' => $field['id'],
				'value' => $field['obj_value'],
			];
		}

		return $this;
	}

	/** Cette fonction permet de récupérer les dernières actualités publiées.
	 * 	@param $fields Optionnel, par défaut les champs avancés liés aux actualités ne sont pas chargé, mettre true pour le faire (le paramètre accepte aussi un identifiant ou tableau d'identifiant de champs avancés)
	 * 	@return array Un tableau contenant les dernières actualités publiées
	 */
	public static function all( $fields=false ){
		global $config;

		$ar_news = new Collection();

		// Récupère toutes les actualiés publiées triées par position puis par date de publication (tri par défaut)
		$r_news = news_get( 0, true, null, 0, $config['wst_id'], i18n::getLang() );

		if( $r_news ){
			while( $news = ria_mysql_fetch_assoc($r_news) ){
				// Traduction des actualités
				$news = i18n::getTranslation( CLS_NEWS, $news );

				$obj_news = new News( [
					'news' 	=> $news['id'],
					'name' 	=> $news['name'],
					'intro' => $news['intro'],
					'desc' 	=> $news['desc'],
					'date' 	=> $news['publish_date_en'],
					'end' 	=> $news['publish_date_end_en'],
					'url' 	=> $news['url_alias'],
					'catid' 	=> $news['cat_id'],
				]);

				$obj_news->general();

				if( $fields ){
					$obj_news->fields( $fields === true ? 0 : $fields );
				}

				$ar_news->addItem( $obj_news );
			}
		}

		return self::transformObjectToArray( $ar_news );
	}

	/** Cette fonction permet de récupérer les dernières actualités publiées.
	 * 	@return array Un tableau contenant les dernières actualités publiées
	 */
	public static function last(){
		global $config;

		$r_news = ria_mysql_query('
			select news_id
			from news
				join news_websites on (nw_tnt_id = '.$config['tnt_id'].' and nw_news_id = news_id)
			where news_tnt_id = '.$config['tnt_id']. '
				and (news_publish_date is not null and news_publish_date <= now() )
				and (news_publish_date_end is null or news_publish_date_end > now() )
				and nw_wst_id = '.$config['wst_id'].'
				and nw_lng_code = "'.i18n::getLang().'"
			order by news_publish_date desc
			limit 0, '.Template::get('news-slide-limit'). '
		');

		$ar_news = new Collection();

		if( $r_news ){
			while( $news = ria_mysql_fetch_assoc($r_news) ){
				try{
					$obj_news = new News( ['news' => $news['news_id']] );
					$obj_news->general();
					$ar_news->addItem( $obj_news );
				}catch(Exception $e){
					// On ne loggue que l'erreur du manque d'identifiant d'actualité
					if( $e->getCode() !== 2 ){
						error_log($e->getMessage());
					}
				}
			}
		}

		return self::transformObjectToArray( $ar_news );
	}

	/** Cette fonction compte le nombre d'actualités publiées
	 * 	@return int Le nombre d'actualité publié
	 */
	public static function count(){
		global $config;

		$r_news = news_get( 0, true, null, 0, $config['wst_id'], i18n::getLang() );
		return $r_news ? ria_mysql_num_rows( $r_news ) : 0;
	}

	/**	Récupère l'actualité précédente
	 * @return	bool|array	L'actualité précédente. False sinon
	 */
	public function getPrevious(){
		return $this->__get_adjacent();
	}

	/**	Récupère l'actualité suivante
	 * @return	bool|array	L'actualité suivante. False sinon
	 */
	public function getNext(){
		return $this->__get_adjacent(false);
	}

	/**	Récupère l'actualité adjacente
	 * @param	bool		$previous	Optionnel, True pour récupérer l'actualité précédente, false pour la suivante
	 * @return	bool|array	Peut être l'actualité suivante ou précédente. False sinon
	 */
	private function __get_adjacent($previous=true){

		if(!is_numeric($this->id) || $this->id <= 0){
			return false;
		}
		global $config;

		$op = !is_bool($previous) || !$previous ? '>' : '<';

		$sql = '
			select
				n.news_id as id,
				n.news_name as name,
				n.news_intro as intro,
				n.news_url_alias as url
			from
				news n
			inner join
				news_websites ns
			on
					ns.nw_tnt_id = '.$config['tnt_id'].'
				and ns.nw_news_id = n.news_id
				and ns.nw_wst_id = '.$config['wst_id'].'
				and ns.nw_lng_code = "'.i18n::getLang().'"
			where
					n.news_tnt_id = '.$config['tnt_id']. '
				and n.news_publish_date is not null
				and n.news_publish_date <= now()
				and n.news_publish_date '.$op.' "'.$this->date.'"
				and (n.news_publish_date_end is null or n.news_publish_date_end > now() )
			limit 0, 1
		';

		$r_adj = ria_mysql_query($sql);

		if(!ria_mysql_num_rows($r_adj)){
			return false;
		}
		return i18n::getTranslation( CLS_NEWS, ria_mysql_fetch_assoc($r_adj) );
	}

}
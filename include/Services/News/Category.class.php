<?php

require_once('Services/Service.class.php');
require_once('Services/News/News.class.php');
require_once('news.categories.inc.php');

/**	\brief Cette classe permet de charger les informations sur une catégorie d'actualités
 *
 */
class NewsCategory extends Service {
	protected $id; ///< Identifiant de la catégorie d'actualités
	protected $name; ///< Nom de la catégorie d'actualités
	protected $desc; ///< Description de la catégorie d'actualités
	protected $count; ///< Nombre d'actualités publiées dans cette catégorie
	protected $url; ///< URL donnant accès à cette catégorie d'actualités
	protected $news; ///< Liste des actualités (chargée à la demande)

	/** Cette fonction créé un objet permettant de charger les informations sur une catégorie.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- cat : identifiant de la catégorie d'actualités
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'cat', 0 );
		$this->name = ria_array_get($data, 'name', '');
		$this->desc = ria_array_get($data, 'desc', '');
		$this->count = ria_array_get($data, 'news', '');
		$this->url = ria_array_get($data, 'url_alias', '');
		$this->news = new Collection();
	}

	/** Cette fonction permet de charger les informations générales de la catégorie d'actualités.
	 * 	@return NewsCategory L'objet courant
	 */
	public function general(){
		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('La catégorie d\'actualité n\'est pas identifiée.');
		}

		$r_cat = news_categories_get( $this->id, true );
		if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
			throw new Exception('La catégorie d\'actualité n\'existe pas ou plus.');
		}

		$cat = i18n::getTranslation( CLS_NEWS_CAT, ria_mysql_fetch_assoc($r_cat) );

		$this->name = $cat['name'];
		$this->desc = $cat['desc'];
		$this->count = $cat['news'];
		$this->url = $cat['url_alias'];

		return $this;
	}

	/** Cette fonction permet de charger les actualités présentes dans une catégories.
	 * 	Les actualités seront toujours triées pour afficher les dernières publications en premiers.
	 *
	 * 	@param int $start Optionnel, permet de préciser à partir de quel curseur on récupère les actualités
	 * 	@param bool|int $limit Optionnel, permet de préciser combien d'actualités sont récupérées (par défaut 20)
	 *
	 * 	@return NewsCategory L'object courant
	 */
	public function news(){
		global $config;

		// Récupère les actualités publiées présentes dans cette catégorie
		$r_news = news_get( 0, true, null, $this->id, $config['wst_id'], i18n::getLang() );
		if( $r_news ){
			while( $news = ria_mysql_fetch_assoc($r_news) ){
				$obj_news = new News( [
					'news' 	=> $news['id'],
					'name' 	=> $news['name'],
					'intro' => $news['intro'],
					'desc' 	=> $news['desc'],
					'date' 	=> $news['publish_date_en'],
					'end' 	=> $news['publish_date_end_en'],
					'url' 	=> $news['url_alias'],
					'catid' 	=> $news['cat_id'],
				]);
				$this->news->addItem( $obj_news->general() );
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer la liste des actualités.
	 * 	@return array Un tableau des actualités
	 */
	public function getNews(){
		return $this->transformObjectToArray( $this->news );
	}

	/** Cette fonction permet de récupérer la liste des catégories d'actualités.
	 * 	@return array Un tableau contenant les inforamtions sur les catégories d'actualités
	 */
	public static function all(){
		$ar_cat = new Collection();

		// Récupère toutes les catégories publiées
		$r_cat = news_categories_get( 0, true );

		if( $r_cat ){
			while( $cat = ria_mysql_fetch_assoc($r_cat) ){
				$obj_cat = new NewsCategory( ['cat' => $cat['id']] );
				$ar_cat->addItem( $obj_cat->general() );
			}
		}

		return self::transformObjectToArray( $ar_cat );
	}
}
<?php

require_once('Services/Service.class.php');
require_once('documents.inc.php');

/**	\brief Cette classe permet de charger les informations sur un document
 *
 */
class Document extends Service {
	protected $id; ///< Identifiant du document
	protected $name; ///< Nom du document
	protected $desc; ///< Description du document
	protected $size; ///< Taille du document (en octet)
	protected $filename; ///< Nom du fichier source
	protected $extension; ///< Extension du fichier source
	protected $typeid; ///< Identifiant du type lié au fichier
	protected $typename; ///< Nom du type lié au fichier
	protected $url; ///< URL permettant de télécharger le fichier
	protected $images; ///< Identifiant d'images liés à ce document

	/** Cette fonction créé un objet permettant de charger les informations sur un document.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- doc : identifiant du document
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'doc', 0 );
	}

	/** Cette fonction permet de charger les informations générales du document.
	 * 	@return Document L'objet courant
	 */
	public function general(){
		global $config;

		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('Le document n\'est pas identifié.');
		}

		$r_doc = doc_documents_get( $this->id, 0, $config['wst_id'], '', true, i18n::getLang() );
		if( !$r_doc || !ria_mysql_num_rows($r_doc) ){
			throw new Exception('Le document n\'existe pas ou plus.');
		}

		$doc = i18n::getTranslation( CLS_DOCUMENT, ria_mysql_fetch_assoc($r_doc) );

		$this->id = $doc['id'];
		$this->name = $doc['name'];
		$this->desc = view_site_format_description( $doc['desc'] );
		$this->size = $doc['size'];
		$this->filename = $doc['filename'];
		$this->extension = $doc['doc_ext'];
		$this->url = Template::getUrl('download').'?doc='.$this->id;

		$this->typeid = $doc['type_id'];
		$this->typename = $doc['type_name'];

		$this->images = new Collection();

		$r_img = doc_images_get( $this->id );
		if( $r_img ){
			while( $img = ria_mysql_fetch_assoc($r_img) ){
				$this->images->addItem( $img['id'] );
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger tous les documents
	 * 	@param int $type Optionnel, filtre sur le type de document
	 * 	@param int $limit Optionnel, permet de limiter le nombre de document retourné (par défaut aucune limite appliquée)
	 * 	@param array $obj Optionnel, limité le résultat à un objet précis dans ce cas fournir un tableau avec : 'cls_id' et 'obj_ids' en clé
	 * 	@return array Un tableau contenant les documents
	 */
	public static function all( $type=0, $limit=0, $obj=false ){
		global $config;

		$type = control_array_integer( $type, false );
		if( $type === false ){
			return [];
		}

		$docs = new Collection();

		// Limite le résultat sur les documents liés à un objet
		$doc_ids = 0;
		if( ria_array_key_exists(['cls_id', 'obj_ids'], $obj) ){
			$r_obj_docs = doc_objects_get( 0, $obj['cls_id'], $obj['obj_ids'] );
			if( $r_obj_docs && ria_mysql_num_rows($r_obj_docs) ){
				$doc_ids = [];

				while( $obj_docs = ria_mysql_fetch_assoc($r_obj_docs) ){
					$doc_ids[] = $obj_docs['doc_id'];
				}
			}
		}

		if( $obj === false || (is_array($doc_ids) && count($doc_ids) > 0) ){
			$r_doc = doc_documents_get( $doc_ids, $type, $config['wst_id'], '', true, i18n::getLang(), true, null, ($limit > 0 ? $limit : null) );
			if( $r_doc ){
				while( $doc = ria_mysql_fetch_assoc($r_doc) ){
					$obj_doc = new Document( ['doc' => $doc['id']] );
					$docs->addItem( $obj_doc->general() );
				}
			}
		}

		return self::transformObjectToArray( $docs );
	}
}
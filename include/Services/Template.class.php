<?php

require_once('Services/Hook.class.php');
require_once('Services/Content/Page.class.php');
require_once('site.inc.php');
require_once('keywords.inc.php');

/**	\brief Cette classe permet de charger la configuration d'un projet RiaShop / Twig.
 *
 */
class Template {
	private static $config = [];
	private static $instance = null;
	private static $data = [
		'owner' => [
			'name' 			=> '',
			'address1' 	=> '',
			'address2' 	=> '',
			'zipcode' 	=> '',
			'city' 			=> '',
			'capital' 	=> '',
			'phone' 		=> '',
			'fax' 			=> '',
			'email' 		=> '',
			'naf' 			=> '',
			'taxcode' 	=> '',
		], // Information sur le propriétaire
		'page' 	=> [], // Pages de contenu globales
		'currentlang' => 'fr',
		'languages' 	=> [], // Langues activées
		'url' 	=> [
			'social' => [
				'facebook' 	=> '',
				'twitter' 	=> '',
				'instagram' => '',
				'pinterest' => '',
				'linkedin' => '',
				'flickr' => '',
				'youtube' => '',
				'viadeo' => '',
			],
		], // URL générique
	];

	/** Cette fonction se charge de créer une clé de cache.
	 * 	Elle prend en paramètre permettant d'unique chaque clé
	 * 	@param string $key Obligatoire, chaine de caractère rendant unique chaque clé
	 */
	public static function cache( $key ){
		global $config;

		// Complète la clé avec l'identifiant du locataire et du site web
		$key .= $config['tnt_id'].':'.$config['wst_id'].':'.i18n::getLang();

		// Ajout de la notion de client
		$user = CustomerService::getInstance();
		if( $user->isConnected() ){
			$key .= ':user-'.$user->getID();
		}

		return md5( $key );
	}

	/** Cette fonction permet d'initialiser la configuration une seule fois.
	 *
	 */
	public static function load(){
		if( is_null(self::$instance) ){
			self::$instance = new Template();

			// Charge les méta-données
			self::$instance->meta();

			// Charge les informations sur le propriétaire du site
			self::$instance->owner();

			// Charge les pages de contenu global
			self::$instance->pages();

			// Charge les URLs globales (celles qui ne sont liées à aucun objet)
			self::$instance->urls();

			// Thème couleur + Logo
			self::$data['themecolor'] 		= self::$config['themecolor'];
			self::$data['showstocknumber'] 	= self::$config['showstocknumber'];
			self::$data['logourl'] 			= self::$config['logourl'];
			self::$data['favicon'] 			= self::$config['favicon'];
			self::$data['onlycolisage'] 	= self::$config['catalog-only-price-colisage'];

			// Module bancaire pour le paiement CB
			self::$data['paycb_module'] = self::$config['module-cb-payement'];

			// Langues
			self::$instance->languages();

			// Scripts additionnels
			self::$instance->scripts();
		}

		return self::$instance;
	}

	/** Cette fonction permet de retourner un tableau des données global.
	 * 	@return array Un tableau contenant les données globales
	 */
	public static function getData(){
		return self::$data;
	}

	/** Cette fonction permet de récupérer la valeur d'une configuration
	 *  @param $code Obligatoire, configuration dont on souhaite récupérer la valeur
	 *  @return array|null La valeur de la configuration donnée en paramètre (null si la variable n'existe pas)
	 */
	public static function get($code){
		if( trim($code) == '' ){
			return null;
		}

		return isset(self::$config[ $code ]) ? self::$config[ $code ] : null;
	}

	/** Cette fonction permet de récupérer une URL via son code.
	 * 	@param string $code Obligatoire, code de l'URL recherchée
	 * 	@return string L'URL demandée (vide si non trouvée)
	 */
	public static function getURL( $code ){
		$url = '';

		if( trim($code) != '' && array_key_exists($code, self::$data['url']) ){
			$url = self::$data['url'][ $code ];
		}

		return $url;
	}

	/** Cette fonction permet de charger la liste des pays
	 * 	@param bool $delivery Optionnel, mettre True pour ne charger que les pays éligible à la livraison
	 * 	@return array Un tableau contenant les pays
	 */
	public static function countries($delivery=false){
		global $config, $memcached;

		$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':TemplateService:countr():'.($delivery ? 'delivery' : 'all');
		if( ($get = $memcached->get($key_memcached)) ){
			return $get;
		}
		$cnt_code = self::get('cnt-codes');
		$ar_countries = [];

		$r_country = sys_countries_get( '', $cnt_code );
		if( $r_country ){
			while( $country = ria_mysql_fetch_assoc($r_country) ){
				$ar_countries[] = [
					'code' => $country['code'],
					'name' => $country['name'],
				];
			}
		}

		$memcached->set( $key_memcached, $ar_countries, 60 * 60 );
		return $ar_countries;
	}

	/**	Cette méthode permet de récupérer la liste des territoires français
	 * @param	bool	$domtom	Optionnel, True pour récupérer les domtom (Monaco inclus)
	 * @return	array	Tableau des territoires contenant:
	 * 							- code
	 * 							- name
	 */
	public static function frenchLands($domtom=true){
		$codes = ['"FR"'];

		if( is_bool($domtom) && $domtom ){
			$codes = array_merge($codes, ['"GP"', '"GF"', '"MQ"', '"YT"', '"MC"', '"NC"', '"PF"', '"RE"', '"BL"', '"MF"', '"PM"', '"TF"', '"WF"']);

		}
		$sql = '
			select cnt_name as name, cnt_code as code
			from sys_countries where cnt_code in ('.implode(', ', $codes).')
		';
		$countries = ria_mysql_query( $sql );
		$ar_countries = [];

		while( $country = ria_mysql_fetch_assoc( $countries ) ){
			$ar_countries[] = [
				'code' => $country['code'],
				'name' => $country['name'],
			];
		}
		return $ar_countries;
	}

	/** Cette fonction retourne un tableau de toutes les URLs accessible hors connexion.
	 * 	@return array Un tableau de toutes les urls accessible hors connexion
	 */
	public static function getURLWithoutLogin(){
		return [
			Template::getURL('login'),
			Template::getURL('firstlogin'),
			Template::getURL('disconnect'),
			Template::getURL('signup'),
			Template::getURL('forgottenpassword'),
			Template::getURL('reinitpassword'),
		];
	}

	/** Cette fonction retourne un tableau des urls liés au processus de commande.
	 * 	@return array Un tableau contenant toutes les urls
	 */
	public static function getURLCart(){
		return [
			Template::getURL('mycart'),
			Template::getURL('orddelivery'),
			Template::getURL('ordreview'),
			Template::getURL('ordpayment'),
			Template::getURL('ordconfirm'),
		];
	}

	/** Cette fonction permet de forcer la mise à jour des métadatas
	 * 	@return empty
	 */
	public static function reloadMetaData(){
		self::$instance->meta();
	}

	/** Cette fonction charge les données sur le propriétaire du site.
	 * 	@return empty
	 */
	private function owner(){
		global $config;

		$owner = site_owner_get( $config['wst_id'] );
		if( is_array($owner) && count($owner) ){
			self::$data['owner'] = array_replace( self::$data['owner'], $owner );
		}
	}

	/** Cette fonction charge les pages de contenu global (ex visible dans le footer).
	 * 	@return empty
	 */
	private function pages(){
		// TODO : le mieux serait de donner la main depuis l'interface pour gérer les pages globals (selecteur de zone d'affichage - Header,Footer,etc...)
		if( is_array(self::get('footer-cms-id')) && count(self::get('footer-cms-id')) ){
			$page_global = new PageService();

			foreach( self::get('footer-cms-id') as $cms_id ){
				self::$data['page']['cms'.$cms_id] = $page_global->zone( $cms_id );
			}
		}
	}

	/** Cette fonction permet de charger les URLs qui sont liés à aucun contenu dans RiaShop.
	 * 	Elles corresponds à des URLs génériques (accueil, page devis, page contact, process de commande, etc...)
	 */
	private function urls(){
		global $hook;
		// URL des réseaux sociaux
		self::$data['url']['social']['facebook'] = self::$config['facebook'];
		self::$data['url']['social']['twitter'] = self::$config['twitter'];
		self::$data['url']['social']['instagram'] = self::$config['instagram'];
		self::$data['url']['social']['pinterest'] = self::$config['pinterest'];
		self::$data['url']['social']['linkedin'] = self::$config['linkedin'];
		self::$data['url']['social']['flickr'] = self::$config['flickr'];
		self::$data['url']['social']['youtube'] = self::$config['youtube'];
		self::$data['url']['social']['viadeo'] = self::$config['viadeo'];

		// Page d'accueil
		self::$data['url']['home'] 							= '/';

		// Catalogue
		self::$data['url']['bestsellers'] 					= rew_rewritemap_translate_get( '/meilleurs-ventes/', i18n::getLang() );

		// Connexion / Demande d'ouverture de compte / Réinialisation de mot de passe
		self::$data['url']['login'] 						= rew_rewritemap_translate_get( '/connexion/', i18n::getLang() );
		self::$data['url']['firstlogin'] 					= rew_rewritemap_translate_get( '/premiere-connexion/', i18n::getLang() );
		self::$data['url']['disconnect'] 					= rew_rewritemap_translate_get( '/deconnexion/', i18n::getLang() );
		self::$data['url']['signup'] 						= rew_rewritemap_translate_get( '/demande-ouverture-compte/', i18n::getLang() );
		self::$data['url']['forgottenpassword'] 			= rew_rewritemap_translate_get( '/mot-de-passe-perdu/', i18n::getLang() );
		self::$data['url']['reinitpassword'] 				= rew_rewritemap_translate_get( '/reinitialisation-mot-de-passe/', i18n::getLang() );

		// Espace "Mon compte"
		self::$data['url']['myaccount'] 		= rew_rewritemap_translate_get( '/mon-compte/', i18n::getLang() );
		self::$data['url']['inscription'] 		= rew_rewritemap_translate_get( '/creer-compte/', i18n::getLang() );
		self::$data['url']['personalinfo'] 		= rew_rewritemap_translate_get( '/mon-compte/informations-personnelles/', i18n::getLang() );
		self::$data['url']['myorders'] 			= rew_rewritemap_translate_get( '/mon-compte/mes-commandes/', i18n::getLang() );
		self::$data['url']['myorder'] 			= rew_rewritemap_translate_get( '/mon-compte/mes-commandes/commande/', i18n::getLang() );
		self::$data['url']['mydevislist'] 		= rew_rewritemap_translate_get( '/mon-compte/mes-devis/', i18n::getLang() );
		self::$data['url']['mydevis'] 			= rew_rewritemap_translate_get( '/mon-compte/mes-devis/devis/', i18n::getLang() );
		self::$data['url']['mycarts'] 			= rew_rewritemap_translate_get( '/mon-compte/mes-paniers/', i18n::getLang() );
		self::$data['url']['mycartsave'] 		= rew_rewritemap_translate_get( '/mon-compte/mes-paniers/panier/', i18n::getLang() );
		self::$data['url']['myinvoices'] 		= rew_rewritemap_translate_get( '/mon-compte/mes-factures/', i18n::getLang() );
		self::$data['url']['myinvoice'] 		= rew_rewritemap_translate_get( '/mon-compte/mes-factures/facture/', i18n::getLang() );
		self::$data['url']['addressbook'] 		= rew_rewritemap_translate_get( '/mon-compte/carnet-adresses/', i18n::getLang() );
		self::$data['url']['mypassword'] 		= rew_rewritemap_translate_get( '/mon-compte/mot-de-passe/', i18n::getLang() );
		self::$data['url']['batch'] 			= rew_rewritemap_translate_get( '/mon-compte/commande-rapide/', i18n::getLang() );
		self::$data['url']['myoptions'] 		= rew_rewritemap_translate_get( '/mon-compte/options/', i18n::getLang() );
		self::$data['url']['myalerts'] 			= rew_rewritemap_translate_get( '/mon-compte/alert-emails/', i18n::getLang() );
		self::$data['url']['bookmarks'] 		= rew_rewritemap_translate_get( '/mon-compte/mes-favoris/', i18n::getLang() );
		self::$data['url']['rightsindex'] 		= rew_rewritemap_translate_get( '/mon-compte/gestion-des-droits/', i18n::getLang() );
		self::$data['url']['rightsadduser'] 	= rew_rewritemap_translate_get( '/mon-compte/gestion-des-droits/nouveau-compte/', i18n::getLang() );
		self::$data['url']['rightssetuser'] 	= rew_rewritemap_translate_get( '/mon-compte/gestion-des-droits/editer/', i18n::getLang() );
		self::$data['url']['myreturns'] 		= rew_rewritemap_translate_get( '/mon-compte/retours/', i18n::getLang() );
		self::$data['url']['myreturn'] 			= rew_rewritemap_translate_get( '/mon-compte/retours/retour/', i18n::getLang() );

		// Processus de commande
		self::$data['url']['mycart'] 						= rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() );
		self::$data['url']['orddelivery'] 					= rew_rewritemap_translate_get( '/commander/livraison/', i18n::getLang() );
		self::$data['url']['ordreview'] 					= rew_rewritemap_translate_get( '/commander/recapitulatif/', i18n::getLang() );
		self::$data['url']['ordpayment'] 					= rew_rewritemap_translate_get( '/commander/paiement/', i18n::getLang() );
		self::$data['url']['ordconfirm'] 					= rew_rewritemap_translate_get( '/commander/confirmation/', i18n::getLang() );

		// Pages fixes
		self::$data['url']['cgv'] 							= rew_rewritemap_translate_get( '/conditions-generales-de-vente/', i18n::getLang() );

		// Divers formulaire
		self::$data['url']['contact'] 						= rew_rewritemap_translate_get( '/nous-contacter/', i18n::getLang() );
		self::$data['url']['sav'] 							= rew_rewritemap_translate_get( '/sav/', i18n::getLang() );
		self::$data['url']['askquote'] 						= rew_rewritemap_translate_get( '/demander-un-devis/', i18n::getLang() );

		// Documents / Téléchargement
		self::$data['url']['docslibrary'] 					= rew_rewritemap_translate_get( '/telechargements/', i18n::getLang() );
		self::$data['url']['download'] 						= rew_rewritemap_translate_get( '/documents/telecharger/', i18n::getLang() );

		// Ajax
		self::$data['url']['ajaxcart'] 						= rew_rewritemap_translate_get( '/ajax-cart/', i18n::getLang() );
		self::$data['url']['ajaxcatalog'] 					= rew_rewritemap_translate_get( '/ajax-catalog/', i18n::getLang() );
		self::$data['url']['ajaxpay'] 						= rew_rewritemap_translate_get( '/ajax-paiement/', i18n::getLang() );

		// Magasins
		self::$data['url']['stores'] 						= rew_rewritemap_translate_get( '/magasins/', i18n::getLang() );

		// Recherche
		self::$data['url']['search'] 						= rew_rewritemap_translate_get( '/recherche/', i18n::getLang() );

		// Foire aux questions
		self::$data['url']['faq'] 							= rew_rewritemap_translate_get( '/foire-aux-questions/', i18n::getLang() );

		// Actualités
		self::$data['url']['news'] 							= rew_rewritemap_translate_get( '/actualites/', i18n::getLang() );

		// Catalogue
		self::$data['url']['allbrands'] 					= rew_rewritemap_translate_get( '/nos-marques/', i18n::getLang() );
		self::$data['url']['catalognew'] 					= rew_rewritemap_translate_get( '/nouveautes/', i18n::getLang() );
		self::$data['url']['catalogpromo'] 					= rew_rewritemap_translate_get( '/promotions/', i18n::getLang() );

		self::$data['url'] = $hook->apply_filter('Template_onLoadUrls', self::$data['url'], self::$data['url']);
	}

	/** Cette fonction permet de charger les méta-données.
	 *
	 */
	private function meta(){
		self::$data['meta']['title'] = page_title( true );
		self::$data['meta']['desc']  = page_description();
	}

	/** Cette fonction permet de charger les langues.
	 * 	@return empty
	 */
	private function languages(){
		global $config;

		self::$data['currentlang'] = i18n::getLang();

		foreach( $config['i18n_lng_used'] as $one_lang ){
			self::$data['languages'][ $one_lang ] = [
				'code' => $one_lang,
				'name' => i18n_languages_get_name( $one_lang ),
				'url' => wst_websites_languages_get_url( $config['wst_id'], $one_lang )
			];
		}
	}

	/** Cette fonction permet de charger les scripts additionnels.
	 * 	@return empty
	 */
	private function scripts(){
		global $config;

		$r_script = wst_websites_get_script( $config['wst_id'] );
		if( $r_script && ria_mysql_num_rows($r_script) ){
			$script = ria_mysql_fetch_assoc( $r_script );

			self::$data['scripts'] = [
				'head' => trim( $script['head'] ),
				'footer' => trim( $script['footer'] ),
			];
		}
	}


	/** Cette fonction permet de charger la configuration d'un projet.
	 *  \param $code Optionnel, permet de charger la configuration concernant qu'une partie spécifique
	 *  \return Le tableau de la configuration
	 */
	private function __construct(){
		global $config, $hook;

		$variable = [];

		if ($config['env_sandbox']) {
			$config['site_dir'] = isset($_SERVER['site_dir']) ? $_SERVER['site_dir'] : $_SERVER['DOCUMENT_ROOT'];
		}

		// Chargement de la configuration depuis le config.json
		if( file_exists($config['site_dir'].'/../config.json') ){
			$variable = json_decode( file_get_contents($config['site_dir'].'/../config.json'), true );
		}

		if( isset($config['extranet_template_configs']) ){
			$var_template = json_decode( $config['extranet_template_configs'], true );

			if( is_array($var_template) && count($var_template) ){
				if( isset($var_template['theme-color']) ){
					$var_template['themecolor'] = $var_template['theme-color'];
					unset($var_template['theme-color']);
				}

				if( isset($var_template['show-stock-number']) ){
					$var_template['showstocknumber'] = $var_template['show-stock-number'] == 'Oui';
					unset($var_template['show-stock-number']);
				}

				if( isset($var_template['theme-favicon']) ){
					$var_template['favicon'] = $var_template['theme-favicon'];
					unset($var_template['theme-favicon']);
				}

				if( isset($var_template['theme-logo-img'], $config['img_sizes']['logo']) ){
					$size = $config['img_sizes']['logo'];
					$var_template['logourl'] = $config['img_url'].'/'.$size['dir'].'/'.$var_template['theme-logo-img'].'.'.$size['format'];
					unset($var_template['theme-logo-img']);
				}

				$variable = array_merge( $variable, $var_template );
			}
		}

		// Spé Benjamin, temporaire : il faut l'interface de personnalisation des configurations
		if( $config['tnt_id'] == 135 ){
			$variable['catalog-only-price-colisage'] = true;
		}

		$__configs = array_merge( self::$default_config, $variable );

		self::$config = $hook->apply_filter('Template_onLoadSiteConfigs', $__configs, $__configs);
	}

	private static $default_config = [
		// Options générales
		'themecolor' => 'canard', // Thème couleur choisi (par défaut "canard")
		'favicon' => '',
		'logourl' => '',
		'module-cb-payement' => _PAY_MODULE_PAYPLUG, // Module bancaire pour le paiement CB (par défaut PayPlug)
		'rights-actived' => false, // Détermine si la gestion des droits est activée
		'rights-excluded' => [], // Code des droits est exclut du projet
		'cnt-codes' => false, // Détermine quels pays charger (code ISO, par défaut tous les pays)
		'showstocknumber' => true, // affiche par défaut la valeur en stock

		// URL vers les pages des réseaux sociaux
		'facebook' => '',
		'twitter' => '',
		'instagram' => '',
		'pinterest' => '',
		'linkedin' => '',
		'flickr' => '',
		'youtube' => '',
		'viadeo' => '',

		// Options de commande
		'ord-product-nostock'=> false, // Si oui ou non la commande d'article en rupture est autorisé
		'ord-multi-currency' => false, // Détermine si la commande accepte plusieurs devises
		'ord-default-currency' => '€', // Devise par défaut (va de paire avec "ord-multi-currency")
		'use-sales-unit'=> true, // Si oui ou non certain article sont vendus avec une unité de vente (ex. par 2 - donc impossible de commande 1 ou 3, mais possible 2 ou 4)

		// catégorie de CMS à charger dans le footer
		'footer-cms-id' => 0, // (CMS) Page permettant la personnalisation du texte de référencement visible dans le footer

		'index-product-relation' => true,
		'index-product-price-in-relation' => true,
		'index-product-relation-limit' => 8,
		'index-product-image-noempty' => false,

		'menu-depth' => 3, // Nombre de niveau de catégories dans le menu principal
		'menu-inc-root'	=> false,
		'menu-link-special-postion' => 'last',
		'menu-link-promo' => true,
		'menu-link-new' => true,
		'menu-link-destock' => true,

		// Options sur la page de téléchargements
		'downloads-depth' => 3, // Nombre de niveau dans le menu des types de documents
		'downloads-cms-id' => 0, // (CMS) Page permettant la personnalisation pour la page d'accueil de l'espace de téléchargement

		// Options sur l'encard des produits
		'productcard-show-price' => false, // Activer la présence des tarifs dans l'encard des produits
		'productcard-with-reviews' => false, // Activer la présence des avis dans l'encard des produits
		'productcard-cfg-image' => '', // Configuration d'image pour l'encard des produits

		// Options sur la fiche d'un produit
		'product-relations-active' => false, // Activer les relations de produit personnalisé
		'product-documents-download-active' => false, // Active le téléchargement des documents
		'product-published-brand' => true, // True pour récupérer la marque publiée, false non publiée, null pas d'importance

		// Cross-selling
		'cross-selling-new-limit' => 8, // Nombre de produit présentant dans le cross-selling des nouveautés
		'cross-selling-new-lastpublished' => false, // Si oui ou non les derniers articles publiés sont récupérés dans le cas où aucun article est nouveau
		'cross-selling-promo-limit' => 8, // Nombre de produit présentant dans le cross-selling des promotions
		'cross-selling-bestseller-limit' => 8, // Nombre de produit présentant dans le cross-selling des meilleurs ventes
		'cross-selling-bestseller-days' => 60, // Délai de prise en compte des commandes pour le cross-selling des meilleurs ventes
		'cross-selling-last-viewed-limit' => 8, // Nombre de produit présentant dans le cross-selling des derniers articles consultés
		'cross-selling-also-like-limit' => 8, // Nombre de produit présentant dans le cross-selling "Vous aimerez également"
		'cross-selling-users-also-buy-limit' => 8, // Nombre de produit présentant dans le cross-selling "Les utilisateurs ayant acheté ce produit ont également acheté"
		'cross-selling-associated-products' => 8, // Nombre de produit présentant dans le cross-selling "Produits associés"

		// Options sur la page d'accueil
		'home-cross-selling-new' => false, // Active le cross-selling des nouveautés
		'home-cross-selling-promo' => false, // Active le cross-selling des promotions
		'home-cross-selling-bestseller' => false, // Active le cross-selling des meilleurs ventes
		'home-cross-selling-new-title' => '', // Personnalisation du titre du cross-selling des nouveautés sur la page d'accueil
		'home-cross-selling-promo-title' => '', // Personnalisation du titre du cross-selling des promotions sur la page d'accueil
		'home-cross-selling-bestseller-title' => '', // Personnalisation du titre du cross-selling des meilleurs ventes sur la page d'accueil

		// Options sur le listing des produits (page d'une catégorie)
		"catalog-show-price-connected" => false, // Affiche les prix des produits quand l'utilisateur est connecté
		'catalog-slide-price' => false, // Activer le slider prix min / prix max
		'catalog-slide-price-with-children' => true, // Activer/ Désactiver le slider prix min/ max incluant les produits des catégories enfants
		'catalog-slide-price-type' => 'ht', // Si le slider prix min / prix max est sur le HT (par défaut) ou sur le TTC
		'catalog-filter-active' => false, // Activer la gestion de filtre
		'catalog-filter-parent' => true, // Activer la récupération des filtres sur les catégories parent
		'catalog-filter-brand' => false, // Activer l'affichage du filtre "Marque" sur tous les listings
		'catalog-filter-stock' => false, // Activer l'affichage du filtre "Disponibilité" sur tous les listings
		'catalog-filter-catchilds' => false, // Activer l'affichage du filtre "Familles" qui contient les sous-familles
		'catalog-filter-children' => true, // Détermine si les filtres (lié à des champs avancés) tiendront compte des articles enfant
		'catalog-filter-used-active' => true, // Détermine si toutes les valeurs, d'un filtre utilisé, sont activées par défaut
		'catalog-cross-selling-last-viewed' => false, // Activer le cross-selling des derniers articles consulté
		'catalog-cross-selling-last-viewed-title' => false, // Personnalisation du titre du cross-selling des derniers articles consulté
		'catalog-best-sellers-cms-id' => 0, // (CMS) Personnalisation des textes pour l'affichage des best-sellers
		'catalog-category-special' => 0, // Identifiant de la catégorie contenant les offres spéciales
		'catalog-only-price-colisage' => false, // Active le prix unitaire qu'en fonction de conditionnement
		'catalog-only-price-child-published' => false, // Active le chargement du prix d'un article parent à partir des enfants publiés uniquement
		'catalog-nostock-filter' => false, // Active le choix "En rupture" dans le filtre "Disponibilité"

		// Options sur la page Mon panier
		'mycart-cross-selling-last-viewed' => false, // Activer le cross-selling des dernier articles consulté
		'mycart-cross-selling-last-viewed-title' => false, // Personnalisation du titre du cross-selling des derniers articles consulté

		// Option sur la connexion
		'myaccount-login-cms-id' => 0, // (CMS) Personnalisation des textes sur la page de connexion
		'myaccount-forgotten-password-cms-id' => 0, // (CMS) Personnalisation des textes sur la page "Mot de passe oublié ?"
		'myaccount-firstlogin-cms-id' => 0,  // (CMS) Personnalisation des textes sur la page "Première connexion"
		'myaccount-signup-cms-id' => 0,  // (CMS) Personnalisation des textes sur la page de demande d'ouverture de compte
		'myaccount-login-profiles-access' => [PRF_ADMIN, PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER, PRF_SELLER, PRF_SUPPLIER], // Profils ayant par défaut accès
		'myaccount-login-restore-card-days' => 60,

		// Options sur l'historique des commandes
		'myaccount-orders-state-list' => [
			_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_EXP, _STATE_INVOICE,
			_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_ARCHIVE, _STATE_BASKET_SAVE
		], // Liste des status visible par défaut dans l'historique des commandes

		// Options sur la médiathèque des documents
		'media-doc-exclude' => false, // Exclusion de certains types de documents (premier niveau)

		// Options sur les actualités
		'news-cms-id' => 0, // (CMS) Personnalisation des textes sur la page d'accueil des actualités
		'news-slide-limit' => 8, // Nombre d'actualité visible dans un slide d'actualité

		// Options sur la page 404
		'404-cms-id' => 0, // (CMS) Personnalisation des textes sur la page 404

		// Options sur le moteur de recherche
		'search-cms-id' => 0, // (CMS) Personnalisation des textes sur la page de recherche

		// Options sur le formulaire SAV
		'sav-cms-id' => 0, // (CMS) Personnalisation des textes sur le formulaire de SAV

		// Options sur la page de contact
		'contact-cms-id' => 0, // (CMS) Personnalisation des textes sur le formulaire de contact

		// Options sur la page de foire aux questions
		'faq-cms-id' => 0, // (CMS) Personnalisation des textes sur la foire aux questions

		// Options sur la page des conditions générales de ventes
		'cgv-cms-id' => 0, // (CMS) Personnalisation des textes sur les conditions générales de ventes

		// Active l'estimation des frais de ports sur la commande
		'order-specific-shipping-charges' => false,

	]; ///< Tableau contenant la configuration par défaut
}
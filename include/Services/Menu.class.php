<?php

require_once('Services/Service.class.php');
require_once('Services/Catalog/Category.class.php');
require_once('categories.inc.php');

/**	\brief Cette classe permet de charger les informations sur le menu principal.
 *  Il s'agit d'un singleton, il ne peut y avoir qu'un seul objet menu à la fois.
 */
class MenuService extends Service
{
	private static $instance = null;
	protected $arbo = null; ///< Arborescence du menu
	protected $root = null; ///< Information de la catégorie root

	private $cats_new = []; ///< Tableau charger en interne contenant les identifiants de catégories disposant d'un ou plusieurs produits nouveau

	/** Cette fonction permet de charger une seule instance de l'objet menu.
	 *  @return	object	L'objet menu chargé
	 */
	public static function getInstance(){
		if (is_null(self::$instance)) {
			self::$instance = new MenuService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de récupérer une entrée de menu à laquelle appartient une catégorie
	 * 	@param int $cat_id Obligatoire, identifiant d'une catégorie
	 * 	@param bool|array $submenu Optionnel, permet de recherche la catégorie dans une arborescence précise (par défaut on part du premier niveau du menu)
	 * 	@param bool|array $ar_cats_arbo Optionnel, permet de passer en paramètre les identifiants de catégorie "active" dans le menu
	 * 	@param bool $only_active Optionnel, par défaut désactivé, mettre true pour se limite à la catégorie dans laquelle on se trouve
	 * 	@return array Un tableau contenant l'arborescence de premier niveau où se trouve la catégorie
	 */
	public function getEntry( $cat_id, $submenu=false, $ar_cats_arbo=false, $only_active=false ){
		if( !is_numeric($cat_id) || $cat_id <= 0 ){
			throw new Exception("L'identifiant de la catégorie est manquant, impossible de charger son arborescence.");
		}

		$return_menu = false;
		$first = $submenu === false;

		// Charge l'arborescence des catégories produits
		if ($submenu === false) {
			$submenu = $this->arbo->getAll();
		}

		// Charge les catégories "active"
		if( $ar_cats_arbo === false ){
			$ar_cats_arbo = [ $cat_id ];

			// Charge les catégories parentes
			$ar_parents = prd_categories_parents_get_array( $cat_id );
			if( is_array($ar_parents) && count($ar_parents) ){
				$ar_cats_arbo = array_merge( $ar_cats_arbo, $ar_parents );
			}
		}

		foreach( $submenu as $first_level ){
			// Contrôle si la catégorie fait partie de celle devant être marqué comme activée dans le menu
			if( in_array( $first_level->getID(), $ar_cats_arbo) ){
				$first_level->setIsMenuActived( true );
			}elseif( $first_level ){
				if( $only_active ){
					continue;
				}
			}

			if( $first_level->getID() == $cat_id ){
				$return_menu = $first_level->getChildren();
				break;
			}

			$children = $first_level->getChildren();
			if( $children !== false ){
				if( $this->getEntry($cat_id, $first_level->getChildren(), $ar_cats_arbo) !== false ){
					$return_menu = $first_level->getChildren();
					break;
				}
			}
		}

		return $this->transformObjectToArray($return_menu);
	}

	/** Cette fonction permet de récupérer l'arborescence du menu.
	 * 	@return array Un tableau contenant l'arborescence
	 */
	public function getArbo(){
		return $this->transformObjectToArray($this->arbo);
	}

	/** Cette fonction permet de récupérer les identifiants des catégories du menu active.
	 *	@return array Un tableau des identifiants
	 */
	public function getActivated(){
		$ar_active_cats = [];
		if( isset($_GET['cat']) ){
			$ar_active_cats[] = $_GET['cat'];

			$ar_parents = prd_categories_parents_get_array( $_GET['cat'] );
			if( is_array($ar_parents) && count($ar_parents) ){
				$ar_active_cats = array_merge( $ar_active_cats, $ar_parents );
			}
		}

		return $ar_active_cats;
	}

	/** Cette fonction permet de charger le premier niveau d'une catégorie.
	 * 	@param int $cat_id Obligatoire, identifiant d'une catégorie
	 * 	@return array|null Un tableau des informations de la catégorie de premier niveau, null si le premier niveau n'est pas trouvé
	 */
	public function getFirstLevel( $cat_id ){
		if( !is_numeric($cat_id) || $cat_id <= 0 ){
			throw new Exception("L'identifiant de la catégorie est manquant, impossible de charger son premier niveau.");
		}

		$ar_parents = prd_categories_parents_get_array( $cat_id );
		if( is_array($ar_parents) && count($ar_parents) ){
			foreach( $this->arbo->getAll() as $first_level ){
				if( in_array($first_level->getID(), $ar_parents) || $first_level->getID() == $cat_id ){
					return $first_level->getData();
				}
			}
		}

		return null;
	}

	/** Charge les images
	 * @param	string	$cfg Optionnel, Code images
	 * @param	bool	$main Optionnel, True pour charger uniquement les images principales
	 * @return	object	L'objet en cours
	 */
	public function loadImages($cfg='', $depth=0, $main=true){
		$this->__loadImages($this->arbo->getAll(), $cfg, $depth, $main);
		return $this;
	}

	/** Cette fonction permet de savoir si l'accès marque peut-être activé ou non.
	 * 	@return bool true si au moins une marque est active, false dans le cas contraire
	 */
	public static function accessBrand(){
		$r_brand = prd_brands_get( 0, true, '', '', false, true );
		return $r_brand && ria_mysql_num_rows( $r_brand );
	}

	/** Cette fonction permet de charger les champs avancés liés
	 * @param	int|array	$id	Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($id=0){
		$this->__fields($this->arbo->getAll(), $id);
		return $this;
	}

	/** Cette fonction permet de créer un objet chargeant le menu des familles de produits
	 */
	private function __construct(){
		global $config, $memcached, $hook;

		$key_memcached = Template::cache('MenuService:__construct:asset-4');

		if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached)) ){
			// Utilisation du contenu du cache
			$this->arbo = $get;
		}else{
			$this->arbo = new Collection();

			$res = prd_categories_get_for_new( $config['cat_root'], false );
			if( $res ){
				while( $r = ria_mysql_fetch_assoc($res) ){
					$this->cats_new[ $r['id'] ] = $r['id'];
				}
			}

			// Charge les familles de premier niveau
			$r_cat = prd_categories_get(
				0, true, $config['cat_root'], '', false, false, null, false, [], false, false, false, false, false, false, false,
				false, false, null, false, true
			);

			if ($r_cat) {
				while ($cat = ria_mysql_fetch_assoc($r_cat)) {
					// Traduction de la catégorie
					$cat = i18n::getTranslation(CLS_CATEGORY, $cat);

					try{
						$temp = new CategoryService([], [
							'cat' => $cat['id'],
							'title' => $cat['title'],
							'url' => rew_strip( $cat['url_alias'] ),
							'desc' => $cat['desc'],
							'parent' => $cat['parent_id'],
							'isdiscount' => isset($cat['is_soldes']) && $cat['is_soldes'],
							'count_prds' => $cat['products'],
						]);

						$temp->loadImages();
						$temp->setHaveNewPrds( array_key_exists($cat['id'], $this->cats_new) ? 'yes' : 'no' );

						// Charge le sous-menu
						$temp->childs(Template::get('menu-depth'));

						$hook->do_action('MenuService_onloadCategory', ['Category' => $temp]);

						$this->arbo->addItem($temp);
					}catch( Exception $e ){
						// Pas de blocage si la catégorie n'est pas chargé dans le menu
					}
				}
			}

			$memcached->set( $key_memcached, $this->arbo, (60 * 60 * 1), [
				'code' => 'MENUSERVICE_CONTRUCT_FUNC',
				'name' => 'Menu principal du site'
			] );
		}

		// Charge les accès spéciaux
		$this->loadSpecialMenu();

		$this->loadRootCategory();
	}

	/** Cette fonction permet de charger les champs avancés liés
	 * @param	int|array	$id	Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	private function __fields($objects, $id=0){
		if( !is_array($objects) || !count($objects) ){
			return $this;
		}

		foreach($objects as $obj){
			if( !is_object($obj) || !method_exists($obj, 'fields') ){
				continue;
			}
			$obj->fields($id);
			$children = $obj->getChildren();

			$this->__fields($children, $id);
		}
		return $this;
	}

	/** Charge les images récursivement
	 * @param	string	$cfg Optionnel, Code images
	 * @param	bool	$main Optionnel, True pour charger uniquement les images principales
	 * @return	object	L'objet en cours
	 */
	private function __loadImages($objects, $cfg='', $depth, $main=true){
		if( !is_array($objects) || !count($objects) ){
			return $this;
		}

		foreach($objects as $obj){
			if( !is_object($obj) || !method_exists($obj, 'loadImages') ){
				continue;
			}

			$obj->loadImages($cfg, $main);

			if( $depth <= 0 ){
				continue;
			}

			$children = $obj->getChildren();
			$this->__loadImages($children, $cfg, $depth--, $main);
		}

		return $this;
	}

	/** Cette fonction permet de charger les familles virtuelle : Promotons, Nouveautés, Destockage dans le menu principale.
	 */
	private function loadSpecialMenu(){
		global $config, $memcached;

		if( !$config['env_sandbox'] && ($get = $memcached->get(Template::cache('MenuService:loadSpecialMenu'))) ){
			// Utilisation du contenu en cache
			$this->arbo = $get;
		}else{
			// Accès au menu "Promotion"
			if (Template::get('menu-link-promo')) {
				$temp = new CategoryService([], [
					'title' => i18n::get('Promotions', 'MENU'),
					'url' => '/promotions/',
					'type' => 'promo',
					'children' => []
				]);

				if (Template::get('menu-link-special-postion') == 'last') {
					$this->arbo->addItem($temp);
				} else {
					$this->arbo->addFirstItem($temp);
				}
			}

			// Accès au menu "Nouveautés"
			if (Template::get('menu-link-new')) {
				$sub[] = new CategoryService([], [
					'title' => i18n::get('Nouveautés', 'MENU'),
					'url' => '/promotions/',
					'type' => 'new',
					'children' => []
				]);
			}

			// Accès au menu Destockage
			if (Template::get('menu-link-destock')) {
				$sub[] = new CategoryService([], [
					'title' => i18n::get('Destockage', 'MENU'),
					'url' => '/destockage/',
					'type' => 'destock',
					'children' => []
				]);
			}

			$memcached->set( Template::cache('MenuService:loadSpecialMenu'), $this->arbo, 60 * 60 * 1 );
		}
	}

	/**
	 *
	 */
	private function loadRootCategory(){

		if( !Template::get('menu-inc-root') ){
			return $this;
		}
		global $config;

		try{

			$Cat = new CategoryService([], [
				'cat' => $config['cat_root'],
				'children' => $this->getArbo()
			]);

			$Cat->general();

			$this->arbo = new Collection();
			$this->arbo->addItem($Cat);

		}catch( Exception $e ){

		}

	}
}

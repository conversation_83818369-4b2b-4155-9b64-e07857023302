<?php
require_once 'Services/Service.class.php';
require_once 'Services/Customer/Address.class.php';
require_once 'Services/Stores/Store.class.php';
require_once 'Services/Customer/Invoice.class.php';

/**	\brief Cette classe permet de charger les informations sur le client qui est connecté.
 *	Une seule instance de cette classe peut existe à la fois
 */
class CustomerService extends Service {
	private static $instance = null;

	protected $id				= 0; ///< Identifiant du client
	protected $ref				= ''; ///< Code client
	protected $profile			= 0; ///< Profil du client (2 = particulier, 3 = professionel)
	protected $genderid			= 0; ///< Identifiant de type de civilité (1 = Monsieur, 2 = Madame)
	protected $gender			= ''; ///< Intitulé de sa civilité
	protected $firstname		= ''; ///< Son prénom
	protected $lastname			= ''; ///< Son nom
	protected $society			= ''; ///< Le nom de sa société
	protected $siret			= ''; ///< Numéro de SIRET
	protected $email			= ''; ///< Son adresse mail
	protected $phone			= ''; ///< Numéro de téléphone
	protected $adrinvoice		= null; ///< Information sur son adresse de facturation
	protected $adrdelivery		= null; ///< Information sur son adresse de livraison par défaut
	protected $adrbook 			= null; ///< Carnet d'adresse du client
	protected $payments 		= null; ///< Moyens de paiement utilisable par le client
	protected $orders 			= null; ///< Liste des commandes associées à l'utilisateur en cours (le filtre sur le statut peut y être appliqué)
	protected $invoices			= null; ///< Liste des factures associées à l'utilisateur en cours
	protected $nbOrders			= 0;
	protected $nbInvoices		= 0;
	protected $isconnect 		= false; // Si oui ou non un client est connecté
	protected $storeid			= null; // Identifiant du magasin rattaché au compte client
	protected $wishlists		= null; // Listes des favoris
	protected $bankDetails		= null; ///< Informations bancaire
	protected $byconsultas		= false; ///< Si l'accès au compte est fait via "Consulter en tant que"
	protected $canConsultas		= false; ///< Si l'utilisateur en cours ($_SESSION['usr_id']) peut utiliser le "Consulter en tant que"
	protected $ordalerts 		= []; ///< Options d'alertes mails
	protected $alertcc 			= ''; ///< Email en copie des alertes mails
	protected $bookmarks 		= null; ///< Tableau contenant les identifiants de produits favoris
	protected $parents			= null; ///< Tableau contenant les comptes parents suivant le type de hierarchie
	protected $childs 			= null; ///< Tableau contenant les comptes "enfant" (lié à la gestion des droits)
	protected $seller			= null; ///< Informations sur le représentant
	protected $stores 			= null; /// Tableau contenant les magasins liés au compte
	protected $bestsellers		= null; // Tableau contenant les produits bestsellers pour ce compte
	protected $fields			= []; ///< Tableau des champs avancés
	protected $cart				= null; /// < Tableau du panier en cours
	protected $carts21 			= null; ///< Tableau des paniers en attente de validation
	protected $carts22 			= null; ///< Tableau des paniers refusé
	protected $displayprices	= 'ttc'; ///< Mode d'affichage des tarifs (valeur possible 'ht' ou 'ttc')
	protected $issync			= false; ///< Si le compte client est synchronisé (true) ou non (false) avec la gescom
	protected $website 			= ''; ///< Site web du client

	private $prcid 			= 0; ///< Identifiant de sa catégorie tarifaire
	private $sellerid		= 0; ///< Identifiant de représentant
	private $rightsenabled	= null; ///< Tableau associative des droits activés [ 'id' => 'code' ]
	private $hy_type		= REL_USR_HIERARCHY; ///< Type de relation

	/** Cette fonction permet d'initialiser la classe.
	 * 	@return object L'instance nouvellement créé
	 */
	public static function getInstance(){
		if( is_null(self::$instance) ){
			self::$instance = new CustomerService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de récupérer la catégorie tarifaire du client.
	 * 	@return int La catégorie tarifaire
	 */
	public function getPriceCategory(){
		return $this->prcid;
	}

	/** Cette fonction permet de récupérer l'identifiant du client.
	 * 	@return int L'identifiant du client
	 */
	public function getID(){
		return $this->id;
	}

	/** Cette fonction permet de récupérer le code client.
	 * 	@return string Le code client
	 */
	public function getCode(){
		return $this->ref;
	}

	/** Cette fonction permet de récupérer l'adresse e-mail du client.
	 * 	@return string l'adresse e-mail du client
	 */
	public function getEmail(){
		return $this->email;
	}

	/** Cette fonction permet de récupérer le prénom.
	 * 	@return string le prénom
	 */
	public function getFirstname(){
		return $this->firstname;
	}

	/** Cette fonction permet de récupérer le nom.
	 * 	@return string le nom
	 */
	public function getLastname(){
		return $this->lastname;
	}

	/** Cette fonction permet de récupérer la société.
	 * 	@return string la société
	 */
	public function getSociety(){
		return $this->society;
	}

	/** Cette fonction permet de récupérer la société.
	 * 	@return string la société
	 */
	public function getPhone(){
		return $this->phone;
	}

	/** Cette fonction permet de récupérer le tableau contenant l'historique des commandes du client.
	 * @param	bool|array	$ar_state	Optionnel, permet de limiter la liste à un ou plusieurs statut de commande
	 * @param int $offset Optionnel, début de la pagination
	 * @param int $limit Optionnel, fin de la pagination (par défaut à 0 donc aucune pagination)
	 * @return	array		Le tableau contenant l'historique
	 */
	public function getOrders( $ar_state=false, $offset=0, $limit=0, $filters=[] ){
		// Charge l'historique du client
		$this->orders( $ar_state, $offset, $limit, $filters );

		return $this->transformObjectToArray( $this->orders );
	}

	/**	Permet de connaitre le nombre total de commandes de l'utilisateur en cours. Le résultat peut être filtré par statut.s de commande
	 * @param	bool|array		$ar_state	Optionnel, Tableau de statut.s de commande
	 * @return	int				Le nombre total de commandes
	 */
	public function getCountOrders( $ar_state=false ){
		// Charge l'historique du client
		$this->orders($ar_state);

		return $this->nbOrders;

	}

	/** Un tableau contenant la liste des factures du client
	 * @param		int			$offset		Optionnel, début de la pagination
	 * @param		int			$limit		Optionnel, fin de la pagination (par défaut à 0 donc aucune pagination)
	 * @return		bool|array	Le tableau contenant la liste des factures, false si aucune facture
	 */
	public function getInvoices($offset=0, $limit=0){
		$this->invoices($offset, $limit);

		return $this->transformObjectToArray($this->invoices);

	}

	/**	Permet de connaitre le nombre total de factures de l'utilisateur en cours
	 * @return	int				Le nombre total de factures
	 */
	public function getCountInvoices(){
		// Charge l'historique du client
		$this->invoices();

		return $this->nbInvoices;

	}

	/** Cette fonction permet de récupérer l'identifiant de l'adresse de facturation.
	 * 	@return int L'identifiant de l'adresse de facturation
	 */
	public function getInvoiceID(){
		return $this->adrinvoice->getID();
	}

	/** Cette fonction permet de vérifier qu'un client est bien connecté.
	 * 	@return bool True ou False selon si le client est identifié
	 */
	public function isConnected(){
		return $this->isconnect === true;
	}

	/** Cette methode permet de retourner le magasin rattaché au client
	 * @return	int	Identifiant du magasin
	 */
	public function getStoreID(){
		return !is_numeric($this->storeid) ? 0 : $this->storeid;
	}

	/** Cette fonction permet de savoir si le compte est identifié via "Consulter en tant que".
	 * 	@return bool true si c'est bien le cas, false dans le cas contraire
	 */
	public function isConsultedAs(){
		return $this->byconsultas;
	}

	/**	Permet de savoir si le compte en cours ($_SESSION['usr_id']) peut utiliser le consulter en tant que
	 * @return	bool	True s'il peut, false sinon
	 */
	public function canConsultas(){
		return $this->canConsultas;
	}

	/** Cette fonction permet de récupérer le carnet d'adresse du client
	 * 	@param bool $inc_invoice Optionnel, par défaut l'adresse de facturation ne sera pas présente dans le carnet
	 * 	@return object L'objet courant
	 */
	public function addressBook( $inc_invoice=false ){
		global $hook;

		if( !$this->isconnect ){
			return $this;
		}
		// Récupération de toutes les adresses du client
		$r_adr = gu_adresses_get( $this->id, 0, '', [], false, '', '', '', '', '', '', '', false, false, false, false );
		$inc_invoice = is_bool($inc_invoice) ? $inc_invoice : false;

		if( !ria_mysql_num_rows($r_adr) ){
			return $this;
		}
		$this->adrbook = new Collection();

		while( $adr = ria_mysql_fetch_assoc($r_adr) ){
			// Exclut par défaut de l'adresse de facturation
			// Celle-ci peut être inclue en fonction du paramètre de la fonction
			if( !$inc_invoice && $adr['id'] == $this->adrinvoice->getID() ){
				continue;
			}

			$Address = new AddressService([
				'adr'		=> $adr['id'],
				'type'		=> $adr['type_id'],
				'label'		=> $adr['description'],
				'firstname'	=> $adr['firstname'],
				'lastname'	=> $adr['lastname'],
				'society'	=> $adr['society'],
				'address1'	=> $adr['address1'],
				'address2'	=> $adr['address2'],
				'address3'	=> $adr['address3'],
				'zipcode'	=> $adr['postal_code'],
				'city'		=> $adr['city'],
				'country'	=> $adr['country'],
				'cntcode'	=> $adr['country_code'],
				'phone'		=> $adr['phone'],
				'mobile'	=> $adr['mobile'],
				'email'		=> $adr['email'],
			]);

			$hook->do_action('CustomerService_onLoadingAddressBook', ['Address' => $Address]);

			$this->adrbook->addItem($Address, 'adr'.$adr['id']);
		}

		return $this;
	}

	/** Cette méthode permet de retourner les listes des favoris
	 * @param	bool	$force	Optionnel, permet de recharger les listes des favoris
	 * @return	array	Tableau des listes des favoris de l'utilisateur
	 */
	public function getWishlists($force=false){
		$force = is_bool($force) ? $force : false;

		$this->wishlists($force);

		return $this->transformObjectToArray($this->wishlists);

	}

	/** Cette méthode permet de récupérer une liste de favoris
	 * @param	int			$id	Obligatoire, identifiant d'une liste de favoris
	 * @return	bool|array	Le tableau contenant les informations de la liste, false en cas d'erreur
	 */
	public function getWishlist($id){
		global $config;

		if( !$this->id || $this->isconnect ){
			return false;
		}

		if( !is_numeric($id) || $id < 1 || !gu_wishlists_exists($id, $this->id) ){
			return false;
		}

		if( $this->wishlists !== null ){
			$error = false;

			try{
				$w = $this->wishlists->getItem($id);

			}catch( Exception $e ){
				$error = true;
			}

			if( !$error && isset($w) ){
				return $w;
			}
		}
		$r = gu_wishlists_get($id, $this->id, null, $config['wst_id'], false);

		if( !ria_mysql_num_rows($r) ){
			return false;
		}
		$w = ria_mysql_fetch_assoc($r);
		$this->wishlists = new Collection();
		$this->wishlists->addItem($w, $w['id']);

		return $w;

	}

	/** Cette fonction permet de récupérer les moyens de paiement utilisable par le client.
	 * 	@return object L'instance en cours
	 */
	public function payments(){
		// Seulement si le client est identifié
		if( $this->id <= 0 ){
			return $this;
		}

		// Récupère les moyens de paiement qui lui sont attribués
		$r_payment = gu_users_payment_types_get( $this->id );

		if( !ria_mysql_num_rows($r_payment) ){
			return $this;
		}
		$this->payments = new Collection();

		while( $payment = ria_mysql_fetch_assoc($r_payment) ){
			$desc = '';

			if( is_numeric($payment['days']) && $payment['days'] > 0 ){
				$desc = gu_users_payment_types_get_desc( $payment );
			}

			$this->payments->addItem([
				'id' => $payment['id'],
				'name' => $payment['name'],
				'desc' => $desc,
			], 'pay'.$payment['id']);
		}
	}

	/**	Permet le chargement si le compte en cours ($_SESSION['usr_id']) peut utiliser le consulter en tant que
	 * @return	object	L'objet en cours
	 */
	private function __canConsultas(){

		if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || $_SESSION['usr_id'] <= 0 ){
			$this->canConsultas = false;
			return $this;
		}
		global $config;

		$r_user = ria_mysql_query('
			select 1
			from gu_users
			where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
				and usr_id = '.$_SESSION['usr_id'].'
				and (usr_prf_id = ' . PRF_ADMIN . ' or usr_prf_id = ' . PRF_SELLER . ')
				and usr_date_deleted is null
			limit 1
		');

		$this->canConsultas = ria_mysql_num_rows($r_user) > 0;
		return $this;

	}

	/** Cette fonction permet de charger la fonctionnalité "Consulter en tant que".
	 * 	@param string $search Optionnel, chaine à rechercher
	 * 	@param int|array $prf_id Optionnel, filtre sur le profil (identifiant)
	 *	@return array Un tableau contenant les comptes accessibles via "Consulter en tant que"
	 */
	public function consultas( $search='', $prf_id=0 ){
		global $config, $memcached;

		if( !$this->isconnect ){
			return [];
		}

		$prf_id = control_array_integer( $prf_id, false );
		if( $prf_id === false ){
			return [];
		}

		$admin_data = [
			'usr_id' => $this->id,
			'prf_id' => $this->profile,
			'seller_id' => $this->sellerid
		];

		// Si le compte est identifié via "Consulter en tant que"
		// Alors on récupère les informations du compte réellement connecté via la variable $_SESSION['usr_id']
		if( $this->byconsultas ){
			$r_user = ria_mysql_query('
				select usr_id, usr_prf_id as prf_id, usr_seller_id as seller_id
				from gu_users
				where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
					and usr_id = '.$_SESSION['usr_id'].'
					and usr_date_deleted is null
			');

			if( $r_user && ria_mysql_num_rows($r_user) ){
				$admin_data = ria_mysql_fetch_assoc( $r_user );
			}
		}

		// On vérifie si le compte client est parent d'un autre compte
		$is_parent = gu_users_is_parent( $_SESSION['usr_id'] );

		// Seuls les administrateurs ou les représentants on accès au "Consulter en tant que"
		// Dans le cas contraire, on regarde s'il est parent d'autre compte
		if( !in_array($admin_data['prf_id'], [PRF_ADMIN, PRF_SELLER]) ){
			if( !$is_parent ){
				return [];
			}
		}

		$ar_account = [];


		$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':'.$admin_data['usr_id'].':Customer:consultas:isparent:'.$is_parent;
		if( ($get = $memcached->get($key_memcached))!==false ){
			$ar_account = $get === 'none' ? [] : $get;
		}else{
			if( in_array($admin_data['prf_id'], [PRF_ADMIN, PRF_SELLER]) ){
				$ar_ids = 0;

				$ar_prf = [PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER, PRF_USER];
				if( count($prf_id) ){
					$ar_prf = $prf_id;
				}

				switch((int)$admin_data['prf_id']){
					case PRF_SELLER :
						if( !is_numeric($admin_data['seller_id']) || $admin_data['seller_id'] <= 0 ){
							$er = true;
							break;
						}
						$ar_ids = gu_users_seller_customers_get($admin_data['seller_id']);

						if( !is_array($ar_ids) || !count($ar_ids) ){
							$er = true;
						}
						break;
					case PRF_ADMIN :
						$ar_prf[] = PRF_SELLER;
						break;

				}

				if( !isset($er) ){
					$r_account = gu_users_get( $ar_ids, '', '', $ar_prf, '', 0, '', 'ref', 'asc');
				}
			}elseif( $is_parent ){
				// Chargement des comptes "enfant"
				$r_account = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', $_SESSION['usr_id'] );
			}

			if( $r_account ){
				while( $account = ria_mysql_fetch_assoc($r_account) ){
					$label = trim( $account['adr_firstname'].' '.$account['adr_lastname'].' '.$account['society'] );
					if( $label == '' ){
						$label = trim( $account['society'] );
					}

					$label .= ' '.$account['email'];

					$ar_account[] = [
						'id' => $account['id'],
						'code' => $account['ref'],
						'label' => $label
					];
				}
			}

			// Mise en cache du résultat pour une durée de 1 heure
			$memcached->set( $key_memcached, count($ar_account) ? $ar_account : 'none', 1 * 60 * 60 );
		}

		if( trim($search) != '' ){
			// Insensible à la casse, tout est passé en minuscule
			$search = strtolower2( $search );

			foreach( $ar_account as $key=>$data ){
				// Si le code et le label ne match pas avec la recherche, le compte est retiré
				if( !strstr(strtolower2($data['code']), $search) && !strstr(strtolower2($data['label']), $search) ){
					unset($ar_account[$key]);
				}
			}
		}

		return $ar_account;
	}

	/** Cette méthode permet de récupérer les informations bancaire
	 * @param	int		$id	Optionnel, Identifiant des informations bancaire (ou tableau d'identifiants)
	 * @param	bool	$user Optionnel, True pour retourner uniquement les informations relative à l'utilisateur en cours
	 * @return	array	Tableau des informations bancaire
	 */
	public function getBankDetails($id=0, $user=false){
		$this->bankDetails($id, $user);

		return $this->transformObjectToArray($this->bankDetails);
	}

	/** Cette fonction permet de charger les alertes e-mails activé pour le client.
	 * 	@return CustomerService L'objet courant
	 */
	public function alerts(){
		if( !$this->id ){
			return $this;
		}
		$r_alert = gu_ord_alerts_get( $this->id );

		if( !ria_mysql_num_rows($r_alert) ){
			return $this;
		}

		while( $alert = ria_mysql_fetch_assoc($r_alert) ){
			$this->ordalerts[] = $alert['state_id'];
		}

		return $this;
	}

	/** Cette fonction permet de retourner les articles favoris.
	 * 	@return array Un tableau contenant les articles favoris
	 */
	public function bookmarks(){
		if( $this->bookmarks === null ){
			$this->bookmarks = [];

			if( $this->id > 0 ){
				$r_bookmark = gu_bookmarks_get( $this->id );

				if( $r_bookmark ){
					while( $bookmark = ria_mysql_fetch_assoc($r_bookmark) ){
						$this->bookmarks[] = $bookmark['prd_id'];
					}
				}
			}
		}
		return $this->bookmarks;
	}

	/** Cette fonction permet de charger la liste des magasins du compte.
	 * 	@return CustomerService L'objet courant
	 */
	public function stores(){
		if( $this->stores === null ){
			$this->stores = new Collection();

			$usr_store = $this->id;

			$parent_id = gu_users_get_parent_id( $this->id );
			if( is_numeric($parent_id) && $parent_id > 0 ){
				$usr_store = $parent_id;
			}

			$r_store = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, [], false, false, true, '', 0, '', [], false, null,
			'', true, null, false, false, '', '', $usr_store );
			if( $r_store ){
				while( $store = ria_mysql_fetch_assoc($r_store) ){
					$obj_store = new StoreService( $store );
					$this->stores->addItem( $obj_store );
				}
			}
		}

		return $this;
	}

	/** Cette méthode permet de charger et retourner les informations du représentant rattaché au compte en cours
	 * @return	bool|array	Tableau des informations du représentant, false sinon
	 */
	public function getSeller(){
		$this->__loadSeller();

		return is_array($this->seller) ? $this->seller : false;

	}

	/**	Permet de charger et retourner un tableau des comptes parents
	 * @param	int			$hy_type	Obligatoire, type de hierarchie
	 * @return	bool|array	Tableau des informations des comptes parents, false sinon
	 */
	public function getParents($hy_type){
		$this->__loadParents($hy_type);

		return $this->parents !== null ? $this->parents : false;

	}

	/**	Permet le chargement des informations du représentant rattaché au compte en cours
	 * @return	object	L'objet en cours
	 */
	private function __loadSeller(){

		if( is_numeric($this->sellerid) && $this->sellerid > 0 && is_array($this->seller) ){
			return $this;
		}

		if( !is_numeric($this->sellerid) || $this->sellerid <= 0 ){
			$this->sellerid = null;
			$this->seller = null;

			$sellerid = gu_users_get_seller_id($this->id);

			if( !is_numeric($sellerid) || $sellerid <= 0 ){
				return $this;
			}
			$this->sellerid = $sellerid;
		}
		$rseller = gu_users_get($this->sellerid);

		if( !ria_mysql_num_rows($rseller) ){
			$this->sellerid = null;
			$this->seller = null;
			return $this;
		}
		$this->seller = ria_mysql_fetch_assoc($rseller);

		return $this;

	}

	/**	Permet le chargement des comptes parents
	 * @param	int		$hy_type	Obligatoire, type de hierarchie
	 * @return	object	L'objet en cours
	 */
	private function __loadParents($hy_type){

		if( $this->id <= 0 || !rel_relations_types_exists($hy_type) ){
			return $this;
		}

		if( $this->parents !== null && $hy_type == $this->hy_type ){
			return $this;
		}
		$this->hy_type = $hy_type;

		$rrel = rel_relations_get(0, null, $this->id, $hy_type);

		if( !ria_mysql_num_rows($rrel) ){
			return $this;
		}
		$this->parents = [];

		while($rel = ria_mysql_fetch_assoc($rrel) ){
			$r_user = gu_users_get( $rel['src_0'] );

			if( !ria_mysql_num_rows($r_user) ){
				continue;
			}
			$user = ria_mysql_fetch_assoc($r_user);
			$this->parents[] = $user;
		}

		if( !count($this->parents) ){
			$this->parents = null;
		}
		return $this;

	}

	/** Cette fonction permet de charger les comptes "enfants" (lié à la gestion des droits).
	 * 	@return CurstomerService L'objet courant
	 */
	public function childs(){
		if( $this->childs === null ){
			$this->childs = new Collection();

			if( $this->id > 0 ){
				$parent_id = gu_users_get_parent_id( $this->id );
				$child_ids = gu_users_get_family( $parent_id ? $parent_id : $this->id );

				if( is_array($child_ids) ){
					foreach( $child_ids as $one_child_id ){
						$obj_child = new CustomerService( ['user' => $one_child_id] );
						$this->childs->addItem( $obj_child );
					}
				}
			}
		}
		return $this;
	}

	/**	Cette méthode charge le panier en cours (version lite) s'il existe.
	 * Le tableau sera composé de :
	 * 		- id		Identifiant du panier
	 * 		- prds		Tableau des identifiants des produits
	 * 		- nb_line	Total des lignes produits
	 * 		- nb_prds	Total de toutes les quantités
	 * Cette méthode est utilisée pour charger les informations essentielles,
	 * CartService viendra completer à la demande (au clique sur le mini panier)
	 * @param	bool	$round_price	Optionnel, True pour activer l'arrondi, false par défaut (pas d'arrondi)
	 * @return	CustomerService		L'objet en cours
	 */
	public function cart($round_price=false){
		global $config, $hook;

		if( !isset($_SESSION['ord_id']) || !is_numeric($_SESSION['ord_id']) || $_SESSION['ord_id'] <= 0 ){
			return $this;
		}
		$round_price = is_bool($round_price) && $round_price;
		$total_ht = $total_ttc = 0;

		$sql = '
			select
				p.prd_id as id,
				p.prd_ref as ref,
				p.prd_qte as qty,
				p.prd_price_ht as price_ht,
				p.prd_tva_rate as vat,
				p.prd_price_ttc as price_ttc
			from
				ord_products p
			join
				ord_orders o
			on
				o.ord_id=p.prd_ord_id
			and o.ord_tnt_id=p.prd_tnt_id
			and o.ord_wst_id='.$config['wst_id'].'
			and o.ord_state_id='._STATE_BASKET.'
			where
				p.prd_ord_id='.$_SESSION['ord_id'].'
			and p.prd_tnt_id='.$config['tnt_id']
		;
		$rproducts = ria_mysql_query($sql);

		if( !ria_mysql_num_rows($rproducts) ){
			return $this;
		}
		$ar_products = $to_delete = [];

		while($prd = ria_mysql_fetch_assoc($rproducts)){

			if( prd_products_is_port($prd['ref']) ){
				continue;
			}

			$psql = '
				select
					p.prd_id as id
				from
					prd_products p

				where
					p.prd_id='.$prd['id'].'
				and p.prd_tnt_id='.$config['tnt_id'].'
				and p.prd_publish
				and p.prd_publish_cat
				and p.prd_orderable
				limit 1
			';

			$rproduct = ria_mysql_query($psql);

			if( !ria_mysql_num_rows($rproduct) ){
				$to_delete[] = $prd['id'];
				continue;
			}
			$prd['vat'] = is_numeric($prd['vat']) && $prd['vat'] > 0 ? $prd['vat'] : _TVA_RATE_DEFAULT;
			$prd['price_ttc'] = !is_numeric($prd['price_ttc']) || $prd['price_ttc'] <= 0  ? ($prd['price_ht'] * $prd['vat']) : $prd['price_ttc'];
			$prd['total_ht'] = $prd['price_ht'] * $prd['qty'];

			if($round_price){
				$prd['price_ttc'] = round($prd['price_ttc'], $config['round_digits_count']);
			}
			$prd['total_ttc'] = $prd['price_ttc'] * $prd['qty'];

			$total_ht += $prd['total_ht'];
			$total_ttc += $prd['total_ttc'];

			$ar_products[] = $prd;

		}

		$to_delete = $hook->apply_filter('CustomerService_filterCartControlProducts', $to_delete, ['products' => $ar_products]);

		if( is_array($to_delete) && count($to_delete) ){
			ord_products_del($_SESSION['ord_id'], $to_delete);

			// Recharge les produits de la commande
			$rproducts = ria_mysql_query($sql);

			if( !ria_mysql_num_rows($rproducts) ){
				return $this;
			}
			$ar_products = [];

			while($prd = ria_mysql_fetch_assoc($rproducts)){
				$ar_products[] = $prd;
			}

		}
		$nb_prds = 0;

		foreach($ar_products as $prd){
			$nb_prds += $prd['qty'];
		}

		$this->cart = [
			'id'		=> $_SESSION['ord_id'],
			'prds'		=> $ar_products,
			'nb_line'	=> count($ar_products),
			'nb_prds'	=> $nb_prds,
			'total_ht'	=> $total_ht,
			'total_ttc'	=> $total_ttc,
		];
		return $this;

	}

	/** Cette fonction permet de charger les paniers en attente de validation.
	 * 	Le compte doit avoir accès au droit de validation des commandes
	 * 	@return CustomerService L'objet courant
	 */
	public function cartsWaitValidation(){
		if( $this->carts21 === null ){
			$this->carts21 = new Collection();

			// Seulement si le compte à le droit de valider des commandes
			if( gu_users_rights_used('_RGH_ORD_VLD', $this->id, 0, false) ){
				// Récupère tous les comptes enfants
				$child_ids = gu_users_get_family( $this->id );

				if( is_array($child_ids) && count($child_ids) ){
					// Charge les paniers en attente de validation
					$r_cart = ord_orders_get_simple( [], [], ['state_id' => _STATE_WAIT_VALIDATION, 'usr_id' => $child_ids] );
					if( $r_cart ){
						while( $cart = ria_mysql_fetch_assoc($r_cart) ){
							$data_cart = new OrderService( ['ord' => $cart['id'], 'user' => $cart['usr_id']] );
							$data_cart->products();
							$data_cart->address();
							$this->carts21->addItem( $data_cart );
						}
					}
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les paniers refusés.
	 * 	Le compte doit avoir accès au droit de validation des commandes
	 * 	@return CustomerService L'objet courant
	 */
	public function cartsRefused(){
		if( $this->carts22 === null ){
			$this->carts22 = new Collection();

			// Seulement si le compte à le droit de valider des commandes
			if( gu_users_rights_used('_RGH_ORD_VLD', $this->id, 0, false) ){
				// Récupère tous les comptes enfants
				$child_ids = gu_users_get_family( $this->id );

				if( is_array($child_ids) && count($child_ids) ){
					// Charge les paniers refusés
					$r_cart = ord_orders_get_simple( [], [], ['state_id' => _STATE_REFUSED, 'usr_id' => $child_ids] );
					if( $r_cart ){
						while( $cart = ria_mysql_fetch_assoc($r_cart) ){
							$data_cart = new OrderService( ['ord' => $cart['id'], 'user' => $cart['usr_id']] );
							$data_cart->products();
							$data_cart->address();
							$this->carts22->addItem( $data_cart );
						}
					}
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer l'attribut 'rightsenabled'.
	 * 	@return array L'attribut 'rightsenabled'
	 */
	public function getRights(){
		return $this->rightsenabled;
	}

	/** Cette fonction permet de récupérer l'attribut 'childs'.
	 * 	@return array L'attribut 'childs'
	 */
	public function getChilds(){
		return $this->childs->getAll();
	}

	/** Cette fonction permet de récupérer les bestsellers d'un client.
	 * 	@param int $limit Optionnel, nombre de produit (par défaut : 10 produits) retourner
	 * 	@param int $days Optionnel, nombre de jours (par défaut : 30 jours) d'historique
	 * 	@param bool $state_on_line Optionnel, par défaut à false, mettre true pour utiliser le statut sur la ligne de commande plutôt que sur la commande
	 * 	@return CustomerService L'objet courant
	 */
	public function bestsellers( $limit=10, $days=30, $state_on_line=false ){
		global $config;

		if( $this->bestsellers === null ){
			$this->bestsellers = new Collection();

			// Calcul de la date de début
			$start = new DateTime();
			$start->modify('- '.$days.' days');

			// Charge les bestsellers
			$r_best = stats_bestsellers_get(
				$start->format('Y-m-d'), false, $limit, false, null, $config['wst_id'], true, 0, $config['cat_root']
				, true, 0, 0, 0, [$this->id], false, $state_on_line
			);

			if( $r_best && ria_mysql_num_rows($r_best) ){
				while( $best = ria_mysql_fetch_assoc($r_best) ){
					$product = new ProductService(['prd' => $best['id']]);
					$product->images()->fields();
					$product->qte = $best['qte'];
					$this->bestsellers->addItem( $product->card() );
				}
			}
		}

		return $this;
	}

	/**	Permet de savoir si l'utilisateur dispose d'un droit en particulier
	 * @param	string		$code	Obligatoire, Code du droit
	 * @return	bool		True si l'utilisateur dispose du droit, false sinon
	 */
	public function hasRight($code){

		$this->rights();

		if( !is_string($code) ){
			return false;
		}

		if( !is_array($this->rightsenabled) ){
			return true;
		}
		return array_key_exists($code, $this->rightsenabled);

	}

	/** Cette fonction permet de charger les droits accessibles au compte.
	 * 	@return CustomerService L'objet courant
	 */
	private function rights(){

		if( $this->rightsenabled !== null || !Template::get('rights-actived') ){
			return $this;
		}

		if( $this->id <= 0 ){
			return $this;
		}
		$this->rightsenabled = [];

		$r_right = gu_users_rights_get( $this->id );

		if( !ria_mysql_num_rows($r_right) ){
			return $this;
		}

		while( $right = ria_mysql_fetch_assoc($r_right) ){
			$this->rightsenabled[ $right['code'] ] = $right['id'];
		}

		return $this;
	}

	/** Cette méthode permet de récupérer les listes des favoris
	 * @param	bool	$force	Optionnel, permet de recharger les listes
	 */
	private function wishlists($force=false){
		global $config;

		// Seulement si le client est identifié
		if( !$this->id || !$this->isconnect || ($this->wishlists !== null && !$force) ){
			return;
		}
		$r = gu_wishlists_get(0, $this->id, null, $config['wst_id'], false);

		if( !ria_mysql_num_rows($r) ){
			return;
		}
		$this->wishlists = new Collection();

		while( $w = ria_mysql_fetch_assoc($r) ){
			$this->wishlists->addItem($w, $w['id']);
		}

	}

	/** Cette méthode permet de récupérer les informations bancaire
	 * @param	int		$id	Optionnel, Identifiant des informations bancaire (ou tableau d'identifiants)
	 * @param	bool	$user Optionnel, True pour retourner uniquement les informations relative à l'utilisateur en cours
	 * @return	object	L'objet en cours
	 */
	private function bankDetails($id=0, $user=false){


		if( !$this->id || !$this->isconnect || !is_numeric($id) ){
			return $this;
		}
		$this->bankDetails = new Collection();
		$user_id = is_bool($user) && $user ? $this->id : ($user == -1 ? -1 : 0);

		// Récupère les informations bancaire
		$r = site_bank_details_get( $id, $this->profile, $user_id);

		if( !ria_mysql_num_rows($r) ){
			return $this;
		}
		$bnk = ria_mysql_fetch_assoc($r);

		$this->bankDetails->addItem($bnk['cbank'].' '.$bnk['counter'].' '.$bnk['account'].' '.$bnk['key'], 'rib');
		$this->bankDetails->addItem(chunk_split( $bnk['iban'], 4, ' ' ), 'iban');
		$this->bankDetails->addItem($bnk['bic'], 'bic');

		return $this;
	}

	/** Cette fonction permet de récupérer l'historique des commandes du client
	 * @param	bool|array	$ar_state	Optionnel, permet de limiter la liste à un ou plusieurs statut de commande
	 * @param int $offset Optionnel, début de la pagination
	 * @param int $limit Optionnel, fin de la pagination (par défaut à 0 donc aucune pagination
	 * @return	object		L'objet CustomerService courant
	 */
	private function orders( $ar_state=false, $offset=0, $limit=0, $filters=[] ){
		global $hook;

		if( $this->id <= 0 || $this->orders !== null ){
			return $this;
		}

		// Défini le statut des commandes qui sera récupérées
		$ar_state = control_array_integer( $ar_state, false );
		if( $ar_state === false || !count($ar_state) ){
			// Par défaut, seuls les commandes des status visibles dans l'historique sont récupérées
			$ar_state = Template::get('myaccount-orders-state-list');
		}

		$all = false;
		$other = $hook->apply_filter('CustomerService_setOrderParameter_Other', []);

		if( is_numeric($limit) && $limit > 0 ){
			$other['limit'] = $limit;

			if( is_numeric($offset) && $offset >= 0 ){
				$other['offset'] = $offset;
			}
		}else{
			$limit = -1;
			$offset = 1;

			if( !is_array($other) ){
				$other = [];
			}

			if( isset($other['limit'], $other['offset'], $other['all']) && is_bool($other['all']) && $other['all'] ){
				$all = true;
				$limit = is_numeric($limit) ? (int)$other['limit'] : -1;
				$offset = is_numeric($offset) && $offset > 0 ? (int)$other['offset'] : 0;
				unset($other['limit'], $other['offset']);
			}
		}
		$user_ord = $this->id;

		// Si la liste ne porte que sur des paniers enregistrés, seuls ceux rattaché au compte seront retournés
		// contrairement à une recherche d'historique de commandes
		if( count($ar_state) == 1 && $ar_state[0] == _STATE_BASKET_SAVE ){
		}else{
			if( gu_users_rights_used('_RGH_HIST_ORD_VIEW_ALL', $this->id) ){
				$user_ord = gu_users_get_family( $this->id, true );
			}
		}

		$keys = [];
		$period = [];
		$ar_filters = array_merge(['state_id' => $ar_state, 'usr_id' => $user_ord], $filters);
		$filters = $hook->apply_filter('CustomerService_setOrderParameter_Filter', $ar_filters);

		if( is_array($filters) && count($filters) > 0 ){

			if( isset($filters['ids']) ){
				$keys['id'] = $filters['ids'];
				unset( $filters['ids'] );
			}
			if( isset($filters['start']) ){
				$period['start'] = $filters['start'];
				unset( $filters['start'] );
			}
			if( isset($filters['end']) ){
				$period['end'] = $filters['end'];
				unset( $filters['end'] );
			}
		}

		// Récupère les commandes du client
		$r_order = ord_orders_get_simple(
			$keys, // Clé
			$period, // Période
			$filters, // Filtre
			$other, // Autre paramètre
			['type' => 'replace', 'columns' => ['ord_id' => 'id']] // Valeurs retournées
		);

		$this->nbOrders = ria_mysql_num_rows($r_order);

		if( !$this->nbOrders ){
			return $this;
		}
		$this->orders = new Collection();
		$count = 0;

		while( $order = ria_mysql_fetch_assoc($r_order) ){
			$count++;

			if( $all ){

				if( $limit > 0 && ($this->orders->length() + 1) > $limit ){
					break;
				}

				if( $count <= $offset ){
					continue;
				}

			}
			$Order = new OrderService(['ord' => $order['id'], 'status' => $ar_state]);

			$hook->do_action( 'CustomerService_onLoadCustomerOrder', ['Order' => $Order] );
			$this->orders->addItem( $Order, 'ord'.$order['id'] );
		}

		return $this;
	}

	private function invoices($offset=0, $limit=0){
		global $config, $hook;

		if( $this->id <= 0 || $this->orders !== null ){
			return $this;
		}

		$sql = '
			select
				inv.inv_id as id
			from
				ord_invoices inv
			where
					inv.inv_tnt_id = ' . $config['tnt_id'] . '
				and inv.inv_masked = 0
				and inv.inv_usr_id = ' . $this->id . '
			group by inv.inv_id
			order by inv.inv_date desc
		';

		$sql = $hook->apply_filter('CustomerService_overrideRequestUserInvoices', $sql, ['User' => $this]);

		$r_invoices = ria_mysql_query($sql);

		$this->nbInvoices = ria_mysql_num_rows($r_invoices);

		if( !$this->nbInvoices ){
			return $this;
		}
		$this->invoices = new Collection();

		$offset = is_numeric($offset) && $offset > 0 ? $offset : 0;
		$limit = is_numeric($limit) && $limit > 0 ? $limit : 0;
		$count = 0;

		while( $invoice = ria_mysql_fetch_assoc($r_invoices) ){
			$count++;

			if( $limit > 0 && ($this->invoices->length() + 1) > $limit ){
				break;
			}

			if( $count <= $offset ){
				continue;
			}
			$Invoice = new InvoiceService(['inv' => $invoice['id']]);
			$this->invoices->addItem( $Invoice, 'inv'.$invoice['id'] );
		}

		return $this;

	}

	/** Cette méthode permet de récupérer l'identifiant magasin rattaché au compte client
	 * @return	object	L'objet en cours
	 */
	private function storeID(){
		global $config;

		$sql = '
			select usr_store_choosed as store_id
			from gu_users
			where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$this->id.';
		';
		$res = ria_mysql_query($sql);
		$id = null;

		if( ria_mysql_num_rows($res) ){
			$store = ria_mysql_fetch_assoc($res);
			$id = is_numeric($store['store_id']) && $store['store_id'] && dlv_stores_exists($store['store_id']) ? (int)$store['store_id'] : null;

		}
		$this->storeid = $id;

		return $this;

	}

	/** Cette fonction permet de charger les informations principales du client connecté */
	public function __construct( $data=[] ){
		global $config;

		$user_id = ria_array_get( $data, 'user', 0 );

		// Charge la catégorie tarifaire par défaut
		$this->prcid = isset($config['default_prc_id']) && is_numeric($config['default_prc_id']) ? $config['default_prc_id'] : 0;

		// Détermine l'identifiant du compte connexion sauf si l'identifiant est donné en paramètre
		if( $user_id <= 0 ){
			// On se base soir sur "admin_view_user" variable de session initialisée lors de l'utilisation de "Consulter en tant que"
			// Si celle-ci n'est pas définie, on s'appuit sur la variable de session "usr_id" initialisée lors de la connexion
			if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
				$user_id = $_SESSION['admin_view_user'];
				$this->byconsultas = true;
			}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
				$user_id = $_SESSION['usr_id'];
			}
		}

		if( $user_id > 0 ){
			// Recherche le compte correspondant à cet identifiant
			$r_user = gu_users_get( $user_id );

			if( $r_user && ria_mysql_num_rows($r_user) ){
				// Information comme quoi un client est connecté
				$this->isconnect = true;

				$user = ria_mysql_fetch_assoc( $r_user );

				// Information générale sur le client
				$this->id 				= $user['id'];
				$this->ref 				= $user['ref'];
				$this->profile			= $user['prf_id'];
				$this->genderid			= $user['title_id'];
				$this->gender			= $user['title_name'];
				$this->firstname		= $user['adr_firstname'];
				$this->lastname			= $user['adr_lastname'];
				$this->society			= $user['society'];
				$this->siret			= $user['siret'];
				$this->email			= $user['email'];
				$this->phone			= $user['phone'];
				$this->prcid			= $user['prc_id'];
				$this->sellerid			= $user['seller_id'];
				$this->alertcc			= $user['alert_cc'];
				$this->displayprices	= $user['display_prices'];
				$this->issync			= $user['is_sync'] == 1;
				$this->website			= $user['website'];

				// Chargement de son adresse de facturation
				$this->adrinvoice = new AddressService( ['adr' => $user['adr_invoices'], 'usr' => $user_id] );

				// Chargement de son adresse de livraison par défaut
				if( is_numeric($user['adr_delivery']) && $user['adr_delivery'] > 0 ){
					$this->adrdelivery = new AddressService( ['adr' => $user['adr_delivery'], 'usr' => $user_id] );
				}
				// Chargement du magasin rattaché au compte client en cours
				$this->storeID();
			}else{
				// Dans le cas où le compte client n'est pas trouvé
				// On contrôle que la fonctionnalité "Consulter en tant que" n'est pas activée
				// Si c'est le cas, on l'a réinitilialise
				if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
					unset($_SESSION['admin_view_user']);
				}
			}

			// Chargement des droits
			$this->rights();
		}

		$this->__canConsultas();

		return $this;
	}

	/**	Cette méthode recharge les informations sur l'utilisateur en cours
	 * @return object L'instance courant
	 */
	public function reload(){
		// Mise à jour de l'objet
		self::$instance = new CustomerService();
		return self::$instance;
	}

	/** Permet le chargement des champs avancés liés
	 * @param	int|array	$fld		Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields( $fld=0 ){
		if( !$this->id ){
			return $this;
		}

		$r_fields = fld_fields_get( $fld, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_USER );

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){

			$val_id = false;

			if($field['type_id'] == FLD_TYPE_SELECT){
				$val_id = fld_restricted_values_get_id($field['id'], $field['obj_value']);
			}
			$this->fields[ 'field'.$field['id'] ] = [
				'id'		=> $field['id'],
				'name'		=> $field['name'],
				'value'		=> $field['obj_value'],
				'value_id'	=> $val_id
			];
		}

		return $this;

	}

	/**	Cette méthode permet de récupérer la valeur d'un champ avancé
	 * @param	int		$fld	Obligatoire, Identifiant du champ avancé
	 * @param	bool	$val_id	Optionnel, True pour retourner l'identifiant de la valeur (dans le cas de valeur restreinte), false par défaut
	 * @return	mixed	Valeur du champ avancé
	 */
	public function getFieldValue($fld, $val_id=false){

		if( !is_numeric($fld) || !$fld ){
			return false;
		}
		$val_id = is_bool($val_id) ? $val_id : false;

		if( is_array($this->fields) && isset($this->fields[ 'field'.$fld ]) ){
			return $val_id ? $this->fields[ 'field'.$fld ]['value_id'] : $this->fields[ 'field'.$fld ]['value'];
		}
		$this->fields($fld);

		if( is_array($this->fields) && isset($this->fields[ 'field'.$fld ]) ){
			return $val_id ? $this->fields[ 'field'.$fld ]['value_id'] : $this->fields[ 'field'.$fld ]['value'];
		}
		return false;

	}

	/** Cette fonction permet de retourner le mode d'affichage des tarifs.
	 * 	@return string Le mode d'affichage des tarifs ('ht' ou 'ttc')
	 */
	public function getDisplayPrices(){
		return $this->displayprices;
	}
}

<?php

require_once 'ord.invoices.inc.php';
require_once 'Services/Service.class.php';

/** \brief Cette classe permet de charger les informations sur une facture d'un client
 */
class InvoiceService extends Service
{
	protected $id			= 0; ///< Identifiant de la facture
	protected $ref			= ''; ///< Référence de la facture
	protected $piece		= ''; ///< Numéro de piece de la facture (il s'agit de l'identifiant de celle-ci dans la gestion commerciale)

	protected $date			= ''; ///< Date de la facture
	protected $datemodified = ''; ///< Date de modification de la facture
	protected $nbprd		= 0; ///< Nombre de produit distinct dans la facture
	protected $qtyprd		= 0; ///< Quantité total de produits
	protected $prds			= null; ///< Liste des produits dans la facture

	protected $user			= 0; ///< Identifiant du client
	protected $fields		= []; ///< Champs avancés liés à la facture

	protected $portht		= 0; ///< Frais de port HT appliqué sur la facture
	protected $portttc		= 0; ///< Frais de port TTC appliqué sur la facture
	protected $totalht		= 0; ///< Total HT de la facture
	protected $totalttc		= 0; ///< Total TTC de la facture
	protected $totaleco		= 0; ///< Montant total d'écotaxe
	protected $discount		= 0; ///< Montant de la réduction

	protected $adrinvoice	= null; ///< Adresse de facturation
	protected $adrdelivery	= null; ///< Adresse de livraison

	protected $paytypeid	= null; ///< Identifiant du type de moyen de paiement

	private $adrinvid		= 0; ///< Identifiant de l'adresse de facturation

	/**	Récupération et initialisation des informations de base d'une facture
	 * @param	array	$data	Obligatoire, Tableau contenant les informations sur lesquelles filtrer le résultat
	 * @return	void
	 */
	public function __construct($data = [])
	{
		if (!array_key_exists('inv', $data) || !is_numeric($data['inv']) || $data['inv'] <= 0) {
			return;
		}

		if (array_key_exists('user', $data) && is_numeric($data['user']) && $data['user'] > 0) {
			$usr_id = $data['user'];
		} else {
			$Customer = CustomerService::getInstance();
			$usr_id = $Customer->getID();
		}

		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return;
		}
		global $config;

		$cols = [
			'inv.inv_id'			=> 'id',
			'inv.inv_usr_id'		=> 'usr_id',
			'year(inv.inv_date)'	=> '"year"',
			'inv.inv_total_ht'		=> 'total_ht',
			'inv.inv_total_ttc'		=> 'total_ttc',
			'inv.inv_piece'			=> 'piece',
			'inv.inv_ref'			=> 'ref',
			'inv.inv_date'			=> 'date',
			'inv.inv_date_modified'	=> 'date_modified',
			'inv.inv_discount'		=> 'discount',
			'inv.inv_seller_id'		=> 'seller_id',
			'inv.inv_adr_invoices'	=> 'adr_invoices',
			'inv.inv_pay_id'		=> 'pay_id'
		];

		$ar_cols = [];
		foreach ($cols as $col => $as) {
			$ar_cols[] = $col . ' as ' . $as;
		}

		$sql = '
			select ' . implode(', ', $ar_cols) . '
			from
				ord_invoices inv
			where
					inv.inv_id=' . $data['inv'] . '
				and inv.inv_usr_id=' . $usr_id . '
				and inv.inv_masked=0
				and inv.inv_tnt_id=' . $config['tnt_id'] . '
		';

		if (array_key_exists('is_sync', $data) && is_bool($data['is_sync']) && $data['is_sync']) {
			$sql .= ' and inv_piece!=""';
		}

		if (array_key_exists('seller_id', $data) && is_numeric($data['seller_id']) && $data['seller_id'] > 0) {
			$sql .= ' and inv_seller_id=' . $data['seller_id'];
		}

		$r_invoice = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($r_invoice)) {
			return;
		}
		$invoice = ria_mysql_fetch_assoc($r_invoice);

		$this->id			= $invoice['id'];
		$this->ref			= $invoice['ref'];
		$this->piece		= $invoice['piece'];
		$this->date			= $invoice['date'];
		$this->datemodified = $invoice['date_modified'];

		$this->paytypeid	= $invoice['pay_id'];
		$this->user			= is_numeric($invoice['usr_id']) && $invoice['usr_id'] > 0 ? (int)$invoice['usr_id'] : 0;

		// Montants
		$this->totalht		= $invoice['total_ht'];
		$this->totalttc		= $invoice['total_ttc'];
		$this->discount		= $invoice['discount'];

		// Identifiant de livraison
		$this->adrinvid		= $invoice['adr_invoices'];

		return $this;
	}

	/** Permet de vérifier que la facture existe
	 * @return	bool	True si la facture existe, False dans le cas contraire
	 */
	public function exists()
	{
		return is_numeric($this->id) && $this->id;
	}

	/** Cette fonction permet de charger les produits dans la commande.
	 * 	@param bool $nopromo Optionnel, ne pas inclure les promotions
	 *  @return OrderService L'objet OrderService courant
	 */
	public function products($nopromo = false)
	{
		global $config, $hook;

		if ($this->id <= 0 || $this->prds instanceof Collection) {
			return $this;
		}

		// Charge le minimum des informations sur les produits
		$r_products = ord_inv_products_get($this->id);

		if (!ria_mysql_num_rows($r_products)) {
			return $this;
		}
		$this->prds = new Collection();

		while ($product = ria_mysql_fetch_assoc($r_products)) {
			if (!is_numeric($product['id']) || $product['id'] <= 0) {
				continue;
			}

			if (prd_products_is_port($product['ref'])) {
				// Calcul le montant total des frais de port
				$this->portht += $product['total_ht'];
				$this->portttc += $product['total_ttc'];

				// On exclut les produits frais de port du nombre de produit sur la facture
				continue;
			}

			try {
				$obj_prd = new ProductService([
					'prd'		=> $product['id'],
					'withprice'	=> true,
					'nopromo'	=> $nopromo
				]);
				$obj_prd->general()->images()->fields();

				$this->nbprd  = $this->nbprd + 1;
				$this->qtyprd = $this->qtyprd + $product['qte'];

				if (is_numeric($product['ecotaxe']) && $product['ecotaxe'] > 0) {
					$this->totaleco += $product['ecotaxe'] * $product['qte'];
				}

				$this->prds->addItem([
					'prd'		=> $obj_prd,
					'line'		=> [
						'id'					=> $product['line'],
						'name'					=> $product['name'],
						'qty'					=> $product['qte'],
						'priceht'				=> $product['price_ht'],
						'pricettc'				=> $product['price_ttc'],
						'tvarate'				=> $product['tva_rate'],
						'totalht'				=> $product['total_ht'],
						'totalttc'				=> $product['total_ttc'],
						'ecotaxe'				=> $product['ecotaxe']
					]
				]);
			}catch(Exception $e){
				// Ne rien faire
			}
		}

		return $this;
	}

	/**	Permet le chargement de l'adresse de facturation
	 * @return	object	L'instance en cours
	 */
	public function address()
	{
		if (!$this->id) {
			return $this;
		}

		if (!is_numeric($this->adrinvid) || $this->adrinvid <= 0) {
			$Customer = CustomerService::getInstance();
			$this->adrinvid = $Customer->getInvoiceID();
		}

		// Récupère l'adresse de factuation
		$this->adrinvoice = new AddressService(['adr' => $this->adrinvid, 'usr' => $this->user]);

		return $this;
	}

	/**	Retourne l'identifiant de la facture
	 * @return	int	L'identifiant de la facture
	 */
	public function getID()
	{
		return $this->id;
	}

	/**	Retourne le numéro pièce de la facture
	 * @return	string	Le numéro pièce
	 */
	public function getPiece()
	{
		return $this->piece;
	}

	/**	Retourne le total HT hors frais de port
	 * @return	float	Total HT hors frais de port
	 */
	public function getTotalHTWithoutPort()
	{
		return $this->totalht - $this->portht;
	}

	/**	Retourne le total TTC hors frais de port
	 * @return	float	Total TTC hors frais de port
	 */
	public function getTotalTTCWithoutPort()
	{
		return $this->totalttc - $this->portttc;
	}

	/**	Retourne la quantité total de produits dans la facture
	 * @return	int	La quantité total de produits
	 */
	public function getAllQuantity()
	{
		return $this->qtyprd;
	}

	/**	Retourne un tableau des identifiants produits contenus dans la facture
	 * @return	bool|array	Un tableau des identifiants produits, false si aucun produit
	 */
	public function getProductIDs()
	{
		if (is_null($this->prds)) {
			return false;
		}
		$ar_ids = [];

		foreach ($this->prds->getAll() as $prd) {
			$ar_ids[] = $prd['prd']->getID();
		}

		return $ar_ids;
	}

	/**	Cette méthode permet de récupérer un tableau des produits de la commande
	 * @return	bool|array	Tableau contenant les produits, false si pas de produit
	 */
	public function getProducts()
	{
		if (is_null($this->prds)) {
			return false;
		}
		$prds = $this->prds->getAll();
		$ar_prds = [];

		foreach ($prds as $prd) {
			$prd['prd'] = $prd['prd']->getData();
			$ar_prds[] = $prd;
		}

		return $ar_prds;
	}

	/** Permet le chargement des champs avancés liés
	 * @param	int|array	$fld		Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($fld = 0)
	{

		if (!$this->id) {
			return $this;
		}

		$r_fields = fld_fields_get($fld, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_INVOICE);

		if (!ria_mysql_num_rows($r_fields)) {
			return $this;
		}

		while ($field = ria_mysql_fetch_assoc($r_fields)) {
			$this->fields['field' . $field['id']] = [
				'id' => $field['id'],
				'name' => $field['name'],
				'value' => $field['obj_value'],
			];
		}

		return $this;
	}

	/**	Cette méthode permet de récupérer la valeur d'un champ avancé sur la facture
	 * @param	int		$fld	Obligatoire, Identifiant du champ avancé
	 * @return	mixed	Valeur du champ avancé
	 */
	public function getFieldValue($fld)
	{

		if (!is_numeric($fld) || !$fld) {
			return false;
		}

		if (is_array($this->fields) && isset($this->fields['field' . $fld])) {
			return $this->fields['field' . $fld]['value'];
		}

		return fld_object_values_get($this->id, $fld, i18n::getLang());
	}

	/**	Retourne la date de la facture
	 * @return	string	Date de la facture
	 */
	public function getDate()
	{
		return $this->date;
	}
}

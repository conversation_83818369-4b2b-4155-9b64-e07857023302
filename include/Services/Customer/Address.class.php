<?php

/**	\brief Cette classe permet de charger les informations sur le client qui est connecté.
 *	Une seule instance de cette classe peut existe à la fois
 */
class AddressService extends Service {
	protected $id 				= 0; ///< Identifiant de l'adresse
	protected $adrtype			= 1; ///< Identifiant du type d'adresse
	protected $label 			= ''; ///< Nom donné à l'adresse
	protected $genderid 	= ''; ///< Identifiant civilité
	protected $gender 		= ''; ///< Civilité
	protected $firstname 	= ''; ///< Prénom
	protected $lastname 	= ''; ///< Nom
	protected $society 		= ''; ///< Société
	protected $address1 	= ''; ///< Adresse
	protected $address2 	= ''; ///< Complément d'adresse
	protected $address3 	= ''; ///< Complément d'adresse
	protected $zipcode 		= ''; ///< Code postal
	protected $city 		= ''; ///< Ville
	protected $country 		= ''; ///< Pays
	protected $cntcode 		= ''; ///< Code ISO du pays
	protected $phone 		= ''; ///< Téléphone
	protected $mobile 		= ''; ///< Mobile
	protected $email		= ''; ///< Adresse email
	protected $fields		= []; ///< Champs avancés liés à l'adresse

	private $userid = 0; ///< Identifiant du client lié à cette adresse

	/** Cette fonction permet de créer un objet contenant les informations sur une adresse postal.
	 *  @param $data Optionnel, permet de précharger les informations sur l'adresse
	 *  @return object L'instance nouvelle créée
	 */
	public function __construct( $data=[] ){
		$this->id 			= ria_array_get($data, 'adr', 0);
		$this->adrtype		= ria_array_get($data, 'type', 1);
		$this->userid 		= ria_array_get($data, 'usr', 0);
		$this->label 		= ria_array_get($data, 'label', '');
		$this->genderid 	= ria_array_get($data, 'genderid', '');
		$this->gender 		= ria_array_get($data, 'gender', '');
		$this->firstname 	= ria_array_get($data, 'firstname', '');
		$this->lastname 	= ria_array_get($data, 'lastname', '');
		$this->society 		= ria_array_get($data, 'society', '');
		$this->address1 	= ria_array_get($data, 'address1', '');
		$this->address2 	= ria_array_get($data, 'address2', '');
		$this->address3 	= ria_array_get($data, 'address3', '');
		$this->zipcode 		= ria_array_get($data, 'zipcode', '');
		$this->city 		= ria_array_get($data, 'city', '');
		$this->country 		= ria_array_get($data, 'country', '');
		$this->cntcode 		= ria_array_get($data, 'cntcode', '');
		$this->phone 		= ria_array_get($data, 'phone', '');
		$this->mobile 		= ria_array_get($data, 'mobile', '');
		$this->email 		= ria_array_get($data, 'email', '');

		// Si l'identifiant du compte n'est pas donné en paramètre
		// L'identifiant du compte sera récupéré à partir du compte déjà connecté
		// Une exception est levée dans le cas où aucun compte ne serait connecté
		if( !$this->userid ){
			$user = CustomerService::getInstance();
			if( !$user->isConnected() ){
				throw new Exception( i18n::get('Identifiant client absent', 'ERROR'), 1);
			}

			$this->userid = $user->getID();
		}

		// Chargement de l'adresse depuis la base de donné si les informations ne sont pas fourni lors de l'appel du constructeur
		// Pour cela on regarde si l'attribut "address1" est fourni
		if( !array_key_exists('address1', $data) && is_numeric($this->id) && $this->id > 0 ){
			// Récupère les informations sur l'adresse
			$r_adr = gu_adresses_get( $this->userid, $this->id, '', array(), false, '', '', '', '', '', '', '', false, false, false, null );

			if( $r_adr && ria_mysql_num_rows($r_adr) ){
				$adr = ria_mysql_fetch_assoc( $r_adr );

				$this->label 		= $adr['description'];
				$this->adrtype		= $adr['type_id'];
				$this->genderid 	= $adr['title_id'];
				$this->gender 		= $adr['title_name'];
				$this->firstname 	= $adr['firstname'];
				$this->lastname 	= $adr['lastname'];
				$this->society 		= $adr['society'];
				$this->address1 	= $adr['address1'];
				$this->address2 	= $adr['address2'];
				$this->address3 	= $adr['address3'];
				$this->zipcode 		= $adr['postal_code'];
				$this->city 		= $adr['city'];
				$this->country 		= $adr['country'];
				$this->cntcode 		= $adr['country_code'];
				$this->phone 		= $adr['phone'];
				$this->mobile 		= $adr['mobile'];
				$this->email 		= $adr['email'];
			}
		}
	}

	/** Cette fonction retourne l'identifiant de l'adresse.
	 *  @return int L'identifiant de l'adresse
	 */
	public function getID(){
		return $this->id;
	}

	/** Cette fonction permet de vérifier qu'une adresse existe.
	 * 	Ce contrôle vérifie que l'adresse existe bien pour le client connecté
	 * 	@param int $adr_id Obligatoire, identifiant d'une adresse
	 * 	@return bool True si l'adresse existe, False dans le cas contraire
	 */
	public static function exists( $adr_id ){
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			return false;
		}

		return gu_adresses_exists( $adr_id, $user->getID() );
	}

	/** Permet le chargement des champs avancés liés
	 * @param	int|array	$fld		Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($fld=0){

		if( !$this->id ){
			return $this;
		}

		$r_fields = fld_fields_get( $fld, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_ADDRESS );

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			$this->fields[ 'field'.$field['id'] ] = [
				'id' => $field['id'],
				'name' => $field['name'],
				'value' => $field['obj_value'],
			];
		}

		return $this;

	}
}
<?php

require_once('orders.inc.php');
require_once('Services/Service.class.php');

/** \brief Cette classe permet de charger les informations sur une commande d'un client
 */
class OrderService extends Service{
	protected $id			= 0; ///< Identifiant de la commande
	protected $name			= ''; ///< Nom de la commande
	protected $date			= ''; ///< Date de la commande
	protected $datelivr		= ''; ///< Date de livraison souhaité
	protected $datemodified = ''; ///< Date de modification de la commande
	protected $nbprd		= 0; ///< Nombre de produit distinct dans le panier
	protected $qtyprd		= 0; ///< Quantité total d'articles
	protected $status		= []; ///< Statut de la commande
	protected $prds			= null; ///< Liste des produts dans la commande
	protected $service		= []; ///< Service de livraison appliqué sur le panier ('id' => 0, 'name' => '')
	protected $ref			= ''; ///< Référence sur la commande
	protected $piece			= ''; ///< Numéro de piece de la commande (il s'agit de l'identifiant de celle-ci dans la gestion commerciale)
	protected $pmtcod		= null; ///< Code promotion appliqué sur le panier
	protected $comments		= ''; ///< Commentaire sur la commande
	protected $notes		= ''; ///< Consignes de livraison
	protected $payment		= null; ///< Moyen de règlement choisi
	protected $user			= 0; ///< Identifiant du client
	protected $fields		= []; ///< Champs avancés liés à la commande
	protected $seller		= 0; ///< Identifiant du représentant commercial

	protected $portht 		= 0; ///< Frais de port HT appliqué sur le panier
	protected $portttc 		= 0; ///< Frais de port TTC appliqué sur le panier
	protected $withoutht 	= 0; ///< Total HT du panier hors promotion
	protected $withoutttc = 0; ///< Total TTC du panier hors promotion
	protected $totaleco   = 0; ///< Montant total d'écotaxe
	protected $totalht    = 0; ///< Total HT de la commande
	protected $totalttc   = 0; ///< Total TTC de la commande

	protected $weight 		= null; ///< Poids brut de la commande (en grammes)
	protected $netweight	= null; ///< Poids net de la commande (en grammes)

	protected $dlvtype 		= ''; ///< Type de livraison (dom = domicile, str = en magasin, rly = en point relais)
	protected $adrinvoice = null; ///< Adresse de facturation
	protected $adrdelivery = null; ///< Adresse de livraison
	protected $adrthird = null; ///< Troisième adresse
	protected $delivery 	= null; ///< Informations sur les dates de livraison estimées
	protected $paytypeid = null; /// Type de paiement de la commande
	protected $cancreatereturn = null; /// Détermine si la création d'un retour est possible (yes si c'est le cas)

	protected $strid       		= 0; ///< Identifiant du magasin auquel le panier sera livré
	private $adrinvid   		= 0; ///< Identifiant de l'adresse de facturation
	private $adrdlvid   		= 0; ///< Identifiant de l'adresse de livraison
	private $adrthirdid 		= 0; ///< Identifiant de la troisième adresse
	private $rlyid       		= 0; ///< Identifiant du point relai auquel le panier sera livré

	private static $ar_status = []; ///< Tableau contenant les status de commande possible

	/** Cette fonction permet d'initaliser un objet contenant les informations sur une commande */
	public function __construct( $data=[] ){
		self::listStatus();

		if( !array_key_exists('status', $data) || !$data['status'] ){
			$data['status'] = Template::get('myaccount-orders-state-list');
		}

		if( !array_key_exists('ord', $data) || !is_numeric($data['ord']) || $data['ord'] <= 0 ){
			return $this;
		}

		$usr_id = CustomerService::getInstance()->getID();
		if( gu_users_rights_used('_RGH_HIST_ORD_VIEW_ALL', $usr_id) ){
			$usr_id = gu_users_get_family( $usr_id, true );
		}

		if( array_key_exists('user', $data) && is_numeric($data['user']) && $data['user'] > 0 ){
			$usr_id = $data['user'];
		}

		// Récupère les informations sur la commande
		$order = ord_orders_get_simple(
			[ 'id' => $data['ord'] ],
			[],
			[
				'state_id' => $data['status'] == 'all' ? 0 : $data['status'],
				'usr_id' => $usr_id
			],
			[],
			[
				'type' => 'complete',
				'columns' => ['ord_pmt_id' => 'cod_id', 'ord_comments' => 'comments', 'ord_dlv_notes' => 'notes', 'ord_date_livr' => 'datelivr', 'ord_date_modified' => 'date_modified']
			]
		);

		if( !ria_mysql_num_rows($order) ){
			return $this;
		}
		$order = ria_mysql_fetch_assoc( $order );

		if( is_numeric($order['srv_id']) && $order['srv_id'] > 0 ){
			$r_srv = dlv_services_get( $order['srv_id'] );

			if( $r_srv && ria_mysql_num_rows($r_srv) ){
				$srv = ria_mysql_fetch_assoc( $r_srv );

				$this->service['id'] = $order['srv_id'];
				$this->service['name'] = $srv['name'];
				$this->service['image'] = $srv['img_id'];
				$this->service['type'] = $srv['type_id'];
				$this->service['rly_type_id'] = $srv['presta_id'];
			}
		}

		$this->id			= $order['id'];
		$this->name			= ord_orders_name( $order['ref'], $order['piece'], $order['id'] );
		$this->date			= $order['date'];
		$this->datelivr		= $order['datelivr'];
		$this->datemodified = $order['date_modified'];

		if( Template::get('ord-multi-currency') ){
			$this->totalht = [];
			$this->totalttc = [];
		}else{
			$this->totalht		= $order['total_ht'];
			$this->totalttc		= $order['total_ttc'];
		}

		$this->ref			= $order['ref'];
		$this->piece		= $order['piece'];
		$this->comments		= $order['comments'];
		$this->notes		= $order['notes'];
		$this->paytypeid	= $order['pay_id'];
		$this->cancreatereturn = ord_returns_can_create_for_order( $order['id'] ) ? 'yes' : 'no';
		$this->user			= is_numeric($order['usr_id']) && $order['usr_id'] > 0 ? (int)$order['usr_id'] : 0;

		// Identifiant de livraison
		$this->adrinvid		= $order['inv_id'];
		$this->adrdlvid		= $order['dlv_id'];
		$this->adrthirdid	= $order['third_id'];
		$this->strid 		= $order['str_id'];
		$this->rlyid 		= $order['rly_id'];

		// Récupère le seller_id si il a été défini
		$this->seller		= is_numeric($order['seller_id']) && $order['seller_id'] > 0 ? (int)$order['seller_id'] : 0;

		// Détermine le type de livraison
		if( $this->rlyid ){
			$this->dlvtype = 'rly';
		}elseif( $this->strid ){
			$this->dlvtype = 'str';
		}else{
			$this->dlvtype = 'dom';
		}

		$this->pmtcod = new Collection();

		// Charge les informations sur le ou les codes promotions appliqués
		$ar_list_cod = ord_orders_promotions_get( $this->id, [_PMT_TYPE_CODE] );
		if( count($ar_list_cod) ){
			foreach( $ar_list_cod as $one_cod ){
				$r_cod = pmt_codes_get( $one_cod['pmt_id'] );

				if( $r_cod && ria_mysql_num_rows($r_cod) ){
					$cod = ria_mysql_fetch_assoc( $r_cod );

					$discount_ttc = $cod['discount'];
					if( $cod['discount_type'] == 0 ){
						$discount_ttc = $cod['discount'] * $cod['tva_rate'];
						$cod['discount'] = round( $cod['discount'], 2 );
						$discount_ttc = round( $discount_ttc, 2 );
					}

					$this->pmtcod->addItem([
						'id' => $cod['id'],
						'cod' => $cod['code'],
						'name' => $cod['name'],
						'discount' => $cod['discount'],
						'discount_type' => $cod['discount_type'],
						'label' => '- '.number_format($cod['discount'], 2, ',', ' ').' '.( $cod['discount_type'] == 0 ? '€' : '%' ),
						'label_ttc' => '- '.number_format($discount_ttc, 2, ',', ' ').' '.( $cod['discount_type'] == 0 ? '€' : '%' )
					]);
				}
			}
		}

		$this->status = [
			'id' => $order['state_id'],
			'name' => self::$ar_status[ $order['state_id'] ]['name'],
		];

		return $this;
	}

	/** Cette fonction permet de vérifier que la commande existe bien.
	 *  @return bool True si la commande existe bien, False dans le cas contraire
	 */
	public function exists(){
		return is_numeric($this->id) && $this->id;
	}

    /** Cette fonction permet de dupliquer une commande existante.
     * @param bool $mask_old Optionnel, masquer la commande d'origine
     * @param bool $copy_piece_to_ref Optionnel, copie la pièce de la commande d'origine dans la référence de la nouvelle commande
     *  @return int True si la commande existe bien, False si la commande n'existe pas ou si la duplication ne peut être effectuée.
     */
    public function duplicate($mask_old=false, $copy_piece_to_ref=false) {
        if (!$this->exists()) {
            return false;
        }
        $new_id = ord_orders_copy($this->id);
        if (empty($new_id)) {
            return false;
        }
        if ($mask_old) {
            ord_orders_set_masked($this->id);
        }
        if ($copy_piece_to_ref) {
            ord_orders_ref_update($new_id, $this->piece);
        }
        return $new_id;
    }

	/** Cette fonction permet de charger les produits dans la commande.
	 * 	@param bool $nopromo Optionnel, ne pas inclure les promotions
	 * 	@param bool $reload Optionnel, permet de recharger les articles de la commande, même si cela sont déjà charger dans l'objet
	 *  @return OrderService L'objet OrderService courant
	 */
	public function products( $nopromo=false, $reload=false ){
		global $config, $hook;

		if( $this->id <= 0 || ($reload === false && $this->prds !== null) ){
			return $this;
		}

		// Charge le minimum des informations sur les produits
		$r_product = ord_products_get( $this->id );

		$prf_id = isset($_SESSION['usr_prf_id']) ? $_SESSION['usr_prf_id'] : 2;

		if( !ria_mysql_num_rows($r_product) ){
			return $this;
		}

		$this->prds = new Collection();
		$ar_prd_pmt = [];

		/**
		 * Récupére les produits offerts dans des promotions
		 * pour empêcher la suppression de ceux-ci
		 */
		$rpmt = pmt_codes_get(null, null, true, _PMT_TYPE_PRD);

		if( ria_mysql_num_rows($rpmt) ){
			while( $pmt = ria_mysql_fetch_assoc($rpmt) ){

				$apply = pmt_codes_is_applicable(0, $this->id, $pmt['id'], false, false, $pmt);

				if( !is_bool($apply) || !$apply ){
					continue;
				}

				if( !pmt_code_conditions_apply($pmt['id'], $this->id, false, false, $pmt) ){
					continue;
				}
				$roprd = pmt_offer_products_get($pmt['id']);

				if( !ria_mysql_num_rows($roprd) ){
					continue;
				}

				while( $oprd = ria_mysql_fetch_assoc($roprd) ){
					$ar_prd_pmt[] = $oprd['prd_id'];
				}
			}
		}
		
		while( $product = ria_mysql_fetch_assoc($r_product) ){
			if( !is_numeric($product['id']) || $product['id'] <= 0 ){
				continue;
			}

			if( prd_products_is_port($product['ref']) ){
				// Calcul le montant total des frais de port
				$this->portht += $product['total_ht'];
				$this->portttc += $product['total_ttc'];
				$this->service['prd'] = $product['ref'];

				// On exclut les produits frais de port du nombre de produit sur la commande
				continue;
			}
			try{
				// Chargement du produit avec les informations de base, son tarif et ses images
				if( isset($this->status['id']) && !in_array($this->status['id'], [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
					$old_use_catalog_restrictions = $config['use_catalog_restrictions'];
					$config['use_catalog_restrictions'] = false;
				}

				$obj_prd = $hook->apply_filter('OrderService_onLoadingProduct', false, ['product' => $product, 'nopromo' => $nopromo]);

				if( !$obj_prd ){
					$obj_prd = new ProductService([
						'prd' => $product['id'],
						'withprice' => true,
						'nopromo' => $nopromo
					]);
					$obj_prd->general();
				}

				$obj_prd->images()->fields();

				if( isset($this->status['id']) && !in_array($this->status['id'], [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
					$config['use_catalog_restrictions'] = $old_use_catalog_restrictions;
				}

				$this->nbprd  = $this->nbprd + 1;
				$this->qtyprd = $this->qtyprd + $product['qte'];

				// Les produits offerts ne sont pas comptabilisé dans les totaux
				if( !$product['cod'] || $product['price_ht'] > 0 ){
					if( is_numeric($product['ecotaxe']) && $product['ecotaxe'] > 0 ){
						$this->totaleco += $product['ecotaxe'] * $product['qte'];
					}


					$price_ht = $obj_prd->getPriceHT();
					if( is_array($price_ht) ){
						$price_ht = $price_ht['value'];
					}

					$this->withoutht  += ( $obj_prd->getOriginalPriceHT() ? $obj_prd->getOriginalPriceHT() : $price_ht ) * $product['qte'];
					$this->withoutttc += ( $obj_prd->getOriginalPriceTTC() ? $obj_prd->getOriginalPriceTTC() : $obj_prd->getPriceTTC() ) * $product['qte'];
				}

				// Récupère la composition d'une nomenclature
				$r_option = ord_products_get( $this->id, false, 0, '', null, false, -1, $product['id'], $product['child-line'] );

				$options = new Collection();
				if( $r_option ){
					while( $option = ria_mysql_fetch_assoc($r_option) ){
						$options->addItem([
							'ref'	=> $option['ref'],
							'title'	=> $option['title'],
							'qty'	=> $option['qte']
						]);
					}
				}

				$fields = [];
				$fld_ids = $hook->apply_filter('OrderService_loadLineFields', $fields, $product);

				if( is_array($fld_ids) && count($fld_ids) ){
					$r_fields = fld_fields_get( $fld_ids, 0, -2, 0, 0, [$this->id, $product['id'], $product['line']], null, [], false, [], null, CLS_ORD_PRODUCT );

					if( ria_mysql_num_rows($r_fields) ){
						while( $field = ria_mysql_fetch_assoc($r_fields) ){
							$fields[ 'field'.$field['id'] ] = [
								'id'	=> $field['id'],
								'name'	=> $field['name'],
								'value'	=> $field['obj_value'],
							];
						}
					}
				}

				$product['orig_price_ht'] = $product['orig_price_ttc'] = $product['discount_label'] = null;

				if( isset($product['discount'], $product['discount_type']) && is_numeric($product['discount']) && $product['discount'] > 0 ){
					$product['orig_price_ht'] = $product['price_ht'];
					$product['orig_price_ttc'] = $product['price_ttc'];

					switch((int)$product['discount_type']){
						case 0:
							if( $product['total_ht'] < $product['discount'] ){
								break;
							}

							$product['price_ht'] = $product['price_ht'] - $product['discount'];
							$product['price_ht'] = $product['price_ht'] * $product['tva_rate'];

							$product['total_ht'] = $product['total_ht'] - $product['discount'];
							$product['total_ttc'] = $product['total_ht'] * $product['tva_rate'];

							$product['discount_label'] = '- '.str_replace( ['.00', ',00'], '', $product['discount'] ).' '.( trim($product['currency']) != '' ? $product['currency'] : '€' );
							break;

						case 1:
							if( $product['discount'] <= 0 || $product['discount'] > 100 ){
								break;
							}

							if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
								$product['price_ht'] = round( ($product['price_ht'] - $product['price_ht'] * ($product['discount'] / 100)), 2 );
								$product['price_ttc'] = $product['price_ht'] * $product['tva_rate'];

								$product['total_ht'] = $product['price_ht'] * $product['qte'];
								$product['total_ttc'] = $product['total_ht'] * $product['tva_rate'];
							}else{
								$product['price_ht'] = $product['price_ht'] - ( $product['price_ht'] * ($product['discount'] / 100) );
								$product['price_ttc'] = $product['price_ht'] * $product['tva_rate'];

								$product['total_ht'] = $product['total_ht'] - ($product['total_ht'] * ($product['discount']/100));
								$product['total_ttc'] = $product['total_ht'] * $product['tva_rate'];
							}

							$product['discount_label'] = '- '.str_replace( ['.00', ',00'], '', $product['discount'] ).' %';
							break;
					}
				}

				$this->prds->addItem([
					'prd'		=> $obj_prd,
					'line'		=> [
						'id' 							=> $product['line'],
						'colid' 					=> $product['col_id'],
						'colname'					=> $product['col_name'],
						'colqte' 					=> $product['col_qte'],
						'title' 					=> $product['title'],
						'qty' 						=> $product['qte'],
						'priceht' 				=> $product['price_ht'],
						'pricettc' 				=> $product['price_ttc'],
						'tvarate' 				=> $product['tva_rate'],
						'totalht' 				=> $product['total_ht'],
						'totalttc' 				=> $product['total_ttc'],
						'dpsid' 					=> $product['line_dps_id'],
						'currency' 				=> $product['currency'],
						'state' 					=> $product['state_line'],
						'notes' 					=> $product['notes'],
						'datelivr' 				=> $product['date_livr_en'],
						'codid' 					=> $product['cod'],
						'origpriceht' 	=> $product['orig_price_ht'],
						'origpricettc' 	=> $product['orig_price_ttc'],
						'discount' 				=> $product['discount'],
						'discounttype' 		=> $product['discount_type'],
						'discountlabel' 		=> $product['discount_label'],
						'fields'					=> $fields
					],
					'options'	=> $options
				]);

				if( Template::get('ord-multi-currency') && trim($product['currency']) != '' ){
					$fmt = new NumberFormatter( 'en-US@currency='.$product['currency'], NumberFormatter::CURRENCY );
					$symbol = $fmt->getSymbol(NumberFormatter::CURRENCY_SYMBOL);

					if( !isset($this->totalht[ $product['currency'] ]) ){
						$this->totalht[ $product['currency'] ] = [
							'currency' => $symbol,
							'total' => 0
						];
					}

					if( !isset($this->totalttc[ $product['currency'] ]) ){
						$this->totalttc[ $product['currency'] ] = [
							'currency' => $symbol,
							'total' => 0
						];
					}

					$this->totalht[ $product['currency'] ]['total'] += $product['total_ht'];
					$this->totalttc[ $product['currency'] ]['total'] += $product['total_ht'];
				}

				$weight = is_numeric( $product['weight'] ) && $product['weight'] > 0 ? $product['weight'] : 0;
				$net_weight = is_numeric( $product['weight_net'] ) && $product['weight_net'] > 0 ? $product['weight_net'] : 0;

				$this->weight += $weight * $product['qte'];
				$this->netweight += $net_weight * $product['qte'];

			}catch( Exception $e ){

				$reward = rwd_catalogs_get( $prf_id, $product['id'] );
				/** L'article sera supprimé dans le cas où :
				 * 		- Il s'agit d'un panier
				 * 		- L'article n'est pas un article acheté avec des points de fidélité
				 * 		- L'article n'est pas un produit offert
				 */
				if( !ria_mysql_num_rows($reward) && !in_array($product['id'], $ar_prd_pmt) ){
					if( isset($this->status['id']) && in_array($this->status['id'], [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
						// Les articles de types "DON" ne sont pas gérer de la même façon qu'un produit normal
						// Ils ne sont pas forcément publiés, classés ni même commandable
						// Leur suppression d'un panier est régie pas un fonctionnement bien distinct
						if( !isset($config['prd_don_refs']) || !in_array($product['ref'], $config['prd_don_refs']) ){
							ord_products_del( $this->id, $product['id'], $product['line']);
						}
					}
				}
			}
		}


		if( Template::get('ord-multi-currency') ){
			ksort( $this->totalht );
			ksort( $this->totalttc );
		}

		// Hook : remplace la fonction products
		$hook->do_action( 'OrderService_completeProductsLoad', ['Order' => $this] );

		return $this;
	}

	/**	Cette méthode permet de retourner les informations sur le statut de la commande en cours
	 * @return	bool|array	Tableau contenant l'identifiant (id) et le nom (name) du statut, false en cas d'erreur
	 */
	public function getStatus(){
		return is_array($this->status) && array_key_exists('id', $this->status) ? $this->status : false;
	}

	/**	Cette méthode permet de retourner l'historique des statut de la commande en cours
	 * @return	bool|array	Tableau contenant les différents statuts par lesquels la commande est passée, false en cas d'erreur
	 */
	public function getStatusHistory(){

		$history = false;
		$r_history = ord_orders_states_get( $this->id );

		if ( $r_history && ria_mysql_num_rows($r_history) ) {
			$history = ria_mysql_fetch_assoc_all($r_history);
		}

		return $history;
	}

	/** Cette fonction charge et met en cache un tableau de statut de commande.
	 *  @param array $filter Optionnel, tableau d'identiant que l'on souhaite récupérer
	 *  @return array Le tableau des status
	 */
	public static function listStatus( $filter=[] ){
		global $config, $memcached;

		$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':loadStatus';
		if( ($get = $memcached->get($key_memcached)) ){
			// On récupère les statuts depuis le cache
			self::$ar_status = $get;
		}else{
			// On récupère les statuts depuis la base de données et on les mets en cache pour 24h
			$temp = ord_states_get_array();
			foreach( $temp as $id=>$data ){
				self::$ar_status[ $id ] = [
					'id' => $id,
					'name' => $data['name'],
					'nameplural' => $data['name_pl'],
				];
			}

			$memcached->set( $key_memcached, self::$ar_status, 24 * 60 * 60 );
		}

		$result = self::$ar_status;

		// On filtre le résultat en fonction des status passés en paramètre
		$filter = control_array_integer( $filter, false );
		if( $filter !== false && count($filter) ){
			foreach( $result as $key=>$val ){
				if( !in_array($key, $filter) ){
					unset( $result[ $key ]);
				}
			}
		}

		return $result;
	}

	/** Cette fonction permet de charger l'adresse de facturation et de livraison sur le panier en cours.
	 * 	@return object L'instance en cours
	 */
	public function address(){
		if( !$this->id ){
			return $this;
		}

		$user_id = $this->user;

		if( !is_numeric($user_id) || $user_id <= 0 ){
			$user = CustomerService::getInstance();
			if( !$user->isConnected() ){
				return $this;
			}

			$user_id = $user->getID();
		}

		// Récupère l'adresse de factuation
		$this->adrinvoice = new AddressService( ['adr' => $this->adrinvid, 'usr' => $user_id] );

		// Récupère la troisième adresse
		$this->adrthird = new AddressService( ['adr' => $this->adrthirdid, 'usr' => $user_id] );

		// Récupère l'adresse de livraison
		switch( $this->dlvtype ){
			case 'dom' : // Livraison à domicile
				$this->adrdelivery = new AddressService( ['adr' => $this->adrdlvid, 'usr' => $user_id] );
				break;
			case 'str' : // Livraison en magasin
				$r_store = dlv_stores_get( $this->strid );
				if( $r_store && ria_mysql_num_rows($r_store) ){
					$store = ria_mysql_fetch_assoc( $r_store );

					$this->adrdelivery = new AddressService([
						'id' 					=> $store['id'],
						'society' 		=> $store['name'],
						'address1' 		=> $store['address1'],
						'address2' 		=> $store['address2'],
						'zipcode' 		=> $store['zipcode'],
						'city' 				=> $store['city'],
						'country' 		=> $store['country'],
						'cntcode' 		=> sys_countries_get_code( $store['country'] ),
						'phone' 			=> $store['phone'],
						'usr' 				=> $user_id
					]);
				}
				break;
			case 'rly' : // Livraison en point relais
				$r_rly = dlv_relays_get( $this->rlyid );
				if( $r_rly && ria_mysql_num_rows($r_rly) ){
					$rly = ria_mysql_fetch_assoc( $r_rly );

					$this->adrdelivery = new AddressService([
						'id' 					=> $rly['id'],
						'society' 		=> $rly['name'],
						'address1' 		=> $rly['address1'],
						'address2' 		=> $rly['address2'],
						'zipcode' 		=> $rly['zipcode'],
						'city' 				=> $rly['city'],
						'country' 		=> $rly['country'],
						'cntcode' 		=> $rly['cnt_code'],
						'phone' 			=> $rly['phone'],
					]);
				}
				break;
		}

		return $this;
	}

	/** Cette fonction permet de charger les informations de règlement.
	 * 	L'attribut 'payment' sera chargé de cette façon :
	 * 			- id : identifiant du moyen de règlement
	 * 			- name : nom du moyen de règlement
	 * 			- pay : true ou false en fonction de si la commande a été payée (passage par le statut "En attente de règlement")
	 * 			- date : date de paiement
	 * 	@return OrderService L'objet courant
	 */
	public function payment(){
		global $config;

		if( $this->id && $this->payment === null ){
			$pay_id = ord_orders_get_pay_id( $this->id );

			$this->payment = [
				'id' => $pay_id,
				'name' => ord_payment_types_get_name( $pay_id )
			];

			$this->payment['pay'] = false;

			// Récupère la date de règlement
			$r_date = ria_mysql_query('
				select oos_datetime as date
				from ord_orders_states
				where oos_tnt_id = '.$config['tnt_id'].'
					and oos_ord_id = '.$this->id.'
					and oos_state_id = '._STATE_PAY_CONFIRM.'
			');

			if( $r_date && ria_mysql_num_rows($r_date) ){
				$res = ria_mysql_fetch_assoc( $r_date );
				$this->payment['date'] = $res['date'];
				$this->payment['pay']  = true;
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer l'attribut contenant le service de livraison appliqué sur le panier en cours
	 * 	@return int L'identifiant du service de livraison lié au panier
	 */
	public function getServiceID(){
		return isset($this->service['id']) ? $this->service['id'] : null;
	}

	/** Cette fonction permet de récupérer l'attribut contenant le type de service de livraison appliqué sur le panier en cours
	 * 	@return int L'identifiant du type de service de livraison lié au panier
	 */
	public function getServiceType(){
		return isset($this->service['type']) ? $this->service['type'] : null;
	}

	/**	Retourne les informations du service de livraison appliqué sur le panier en cours
	 * @return	array|bool	Tableau des informations sur le service de livraison, false sinon
	 */
	public function getService(){
		return is_array($this->service) && isset($this->service['id']) ? $this->service : false;
	}

	/** Cette fonction permet de récupérer l'identifiant du compte client
	 * 	@return int L'identifiant du compte client
	 */
	public function getUserID(){
		return $this->user;

	}

	/** Cette fonction permet de récupérer l'attribut contenant l'identifiant du panier en cours
	 * 	@return int L'identifiant du panier en cours
	 */
	public function getID(){
		return $this->id;
	}

	/**	Cette méthode permet de retourner le numéro pièce de la commande
	 * @return	string	Le numéro pièce
	 */
	public function getPiece(){
		return $this->piece;
	}

	/** Cette fonction permet de récupérer le total HT hors frais de port.
	 * 	@return int L'identifiant du panier en cours
	 */
	public function getTotalHTWithoutPort(){
		return $this->totalht - $this->portht;
	}

	/** Cette fonction permet de récupérer l'identifiant de l'adresse de facturation du panier en cours.
	 * 	@return int L'identifiant de l'adresse de facturation
	 */
	public function getInvoiceID(){
		return $this->adrinvid;
	}

	/** Cette fonction permet de récupérer l'attribut contenant la quantité total de produits dans le panier.
	 * 	@return int La quantité total de produits
	 */
	public function getAllQuantity(){
		return $this->qtyprd;
	}

	/** Cette fonction permet de récupérer le montant du panier hors frais de port.
	 * 	@param bool $ttc Optionnel, par défaut le montant HT est retourné, mettre true pour retourner le TTC
	 * 	@return float Le montant HT ou TTC en fonction du paramètre donné
	 */
	public function getWithoutPort( $ttc=false ){
		$total = $this->totalht - $this->portht;

		if( $ttc ){
			$total = $this->totalttc - $this->portttc;
		}

		return $total;
	}

	/** cette fonction permet de récupérer un tableau des identifiants produits contenus dans la commande.
	 * 	@return array Un tableau des identifiants produits
	 */
	public function getProductIDs(){
		$ar_ids = [];

		if( $this->prds !== null ){
			foreach( $this->prds->getAll() as $prd ){
				$ar_ids[] = $prd['prd']->getID();
			}
		}

		return $ar_ids;
	}

	/**	Cette méthode permet de récupérer un tableau des produits de la commande
	 * @return	bool|array	Tableau contenant les produits, false si pas de produit
	 */
	public function getProducts(){
		if( $this->prds === null ){
			return false;
		}

		$prds = $this->prds->getAll();
		$ar_prds = [];

		foreach($prds as $prd){
			try
			{
			$prd['prd'] = $prd['prd']->brand()->getData();
			}
			catch ( \Exception $e )
			{
				/*
				* Cette méthode permet de récupérer des données sur un produit ne comportant pas de marque
				* Exemple : Coupe Aluminium 2m
				*/
				if($e->getMessage() === "La marque n'existe pas ou plus.")
				{
					$prd['prd'] = $prd['prd']->getData();
				}
			}
			$ar_prds[] = $prd;
		}

		return $ar_prds;
	}

	/** Cette fonction permet de récupérer un tableau des lignes de commande suite à l'ajout au panier.
	 * 	@param array $added Obligatoire, résultat tel que retourné par CartActions::addProducts()
	 * 	@return array Un tableau contenant les lignes de commande ajoutées
	 */
	public function getProductLinesAdded( $result ){
		if( $this->prds === null ){
			return false;
		}

		if( !is_array($result) ){
			return false;
		}

		// Transforme le tableau de résultat en un tableau simple
		$ar_prds_add = [];
		foreach( $result as $r ){
			// Supprime la colonne quantité
			$qty = $r[3];
			unset($r[3]);

			$ar_prds_add[ implode( '-', $r ) ] = $qty;
		}

		$prds = $this->prds->getAll();
		$ar_prds = [];

		foreach($prds as $prd){
			$prd['prd'] = $prd['prd']->getData();

			$needle = $this->id.'-'.$prd['prd']['id'].'-'.$prd['line']['id'];
			if( array_key_exists($needle, $ar_prds_add) ){
				$prd['line']['qty'] = $ar_prds_add[ $needle ];
				$ar_prds[] = $prd;
			}
		}

		return $ar_prds;
	}

	/** Permet le chargement des champs avancés liés
	 * @param	int|array	$fld		Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($fld=0){

		if( !$this->id ){
			return $this;
		}

		$r_fields = fld_fields_get( $fld, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_ORDER );

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			$this->fields[ 'field'.$field['id'] ] = [
				'id' => $field['id'],
				'name' => $field['name'],
				'value' => $field['obj_value'],
			];
		}

		return $this;

	}

	/**	Cette méthode permet de récupérer la valeur d'un champ avancé sur la commande
	 * @param	int		$fld	Obligatoire, Identifiant du champ avancé
	 * @return	mixed	Valeur du champ avancé
	 */
	public function getFieldValue($fld){

		if( !is_numeric($fld) || !$fld ){
			return false;
		}

		if( is_array($this->fields) && isset($this->fields[ 'field'.$fld ]) ){
			return $this->fields[ 'field'.$fld ]['value'];
		}

		return fld_object_values_get($this->id, $fld, i18n::getLang());
	}

	/** Cette fonction permet de récupérer l'attribut 'comments'.
	 * 	@return L'attribut 'comments'
	 */
	public function getComments(){
		return $this->comments;
	}

	/** Cette fonction permet de récupérer l'attribut 'date'.
	 * 	@return L'attribut 'date'
	 */
	public function getDate(){
		return $this->date;
	}

	/** Cette fonction permet de récupérer l'attribut 'strid'.
	 * 	@return L'attribut 'strid' correspondant à l'identifiant du magasin lié à la commande
	 */
	public function getStoreID(){
		return $this->strid;
	}

	/**	Retourner le poids de la commande en grammes
	 * @param	bool	$recalculate	Optionnel, True pour recalculer le poids de la commande
	 * @param	bool	$net			Optionnel, True pour récupérer le poids net de la commande
	 * @return	int|float	Le poids de la commande en grammes
	 */
	public function getWeight($recalculate=false, $net=false){
		$net = is_bool($net) ? $net : false;

		if( $this->weight === null || (is_bool($recalculate) && $recalculate) ){
			$this->weight = ord_orders_weight_get( $this->id, 'g', $net);
		}

		return $net ? $this->netweight : $this->weight;
	}


	/**	Récupére le montant total du panier
	 * @param	bool	$ttc	Optionnel, par défaut le montant HT est retourné, mettre true pour retourner le TTC
	 * @return	float	Le montant HT ou TTC en fonction du paramètre donné
	 */
	public function getTotal( $ttc=false ){
		return is_bool($ttc) && $ttc ? $this->totalttc : $this->totalht;
	}
}
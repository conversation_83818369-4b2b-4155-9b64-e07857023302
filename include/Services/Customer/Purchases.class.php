<?php

require_once 'orders.inc.php';
require_once 'Services/Customer/Customer.class.php';
require_once 'Services/Catalog/Product.class.php';

class PurchasesService
{

	/**	Instance de l'utilisateur en cours
	 * @var	CustomerService	$User
	 */
	private $User = null;

	/**	Tableau des statuts
	 * @var	array	$status
	 */
	private $status = [];

	/**	Valeur de tri ou false pour n'appliquer aucun tri
	 * @var	string|bool	$sort
	 */
	private $sort = false;

	/**	Tableau des achats
	 * @var	array|null	$purchases
	 */
	private $purchases = null;

	/**	Valeur de départ
	 * @var	int	$offset
	 */
	private $offset = 0;

	/**	Limite d'achats à retourner
	 * @var	int	$limit
	 */
	private $limit = 20;

	/**	Nombre total d'achats
	 * @var	int	$count
	 */
	private $count = 0;

	/**	Constructeur de la classe
	 * @param	array	$data	Optionnel, Tableau des données de surchage
	 * @return	object	L'objet en cours
	 */
	public function __construct($data = [])
	{
		global $config;

		$User = CustomerService::getInstance();

		if ($User->getID()) {
			$this->User = $User;
		}
		$data = is_array($data) ? $data : [];
		$this->status = [_STATE_IN_PROCESS, _STATE_INVOICE, _STATE_BL_EXP, _STATE_BL_READY, _STATE_PREPARATION, _STATE_ARCHIVE];

		if (array_key_exists('status', $data) && is_array($data['status']) && count($data['status'])) {
			$this->status = $data['status'];
		}

		if (array_key_exists('sort', $data)) {
			$this->__sanitizeSort($data['sort']);
		}

		if (array_key_exists('offset', $data) && is_numeric($data['offset']) && $data['offset'] > 0) {
			$this->offset = $data['offset'];
		}
		$this->limit = is_numeric($config['prd_list_length']) && $config['prd_list_length'] > 0 ? $config['prd_list_length'] : 20;

		if (array_key_exists('limit', $data) && is_numeric($data['limit']) && $data['limit'] > 0) {
			$this->limit = $data['limit'];
		}

		$this->__loadPurchases();
	}

	/**	Retourne la liste des achats effectués
	 * @param	bool		$force	Optionnel, permet de forcer (true) ou non (false) le (re)chargement des achats
	 * @return	array|bool	Tableau des achats, false en cas d'erreur ou pas d'achats
	 */
	public function getPurchases($force = false)
	{
		$this->__loadPurchases($force);

		return is_array($this->purchases) ? $this->purchases : false;
	}

	/**	Retourne le nombre total des achats
	 * @return	int		Le nombre total des achats
	 */
	public function getCount()
	{
		$this->__loadPurchases();

		return $this->count;
	}

	/**	Permet de normaliser le tri qui sera appliqué à la requête SQL
	 * @param	string	$sort	Obligatoire, valeur de tri
	 * @return	object	L'objet en cours
	 */
	private function __sanitizeSort($sort)
	{

		if (!is_string($sort)) {
			$this->sort = false;
			return $this;
		}

		switch ($sort) {
			case 'ref|asc':
				$this->sort['upper(ref)'] = 'asc';
				break;
			case 'ref|desc':
				$this->sort['upper(ref)'] = 'desc';
				break;
			case 'name|asc':
				$this->sort['upper(p_title)'] = 'asc';
				break;
			case 'name|desc':
				$this->sort['upper(p_title)'] = 'desc';
				break;
			case 'date|asc':
				$this->sort['orddate'] = 'asc';
				break;
			case 'date|desc':
				$this->sort['orddate'] = 'desc';
				break;
			case 'prcprices|asc':
				$this->sort['prc_priceht'] = 'asc';
				break;
			case 'prcprices|desc':
				$this->sort['prc_priceht'] = 'desc';
				break;
		}
		return $this;
	}

	/**	Charge les achats effectués
	 * @param	bool	$force	Optionnel, permet de forcer (true) ou non (false) le (re)chargement des achats
	 * @return	object	L'objet en cours
	 */
	private function __loadPurchases($force = false)
	{

		if (!$this->__isUser()) {
			return $this;
		}
		$force = is_bool($force) ? $force : false;

		if (is_array($this->purchases) && !$force) {
			return $this;
		}

		global $config;

		$ar_select = [
			'oprd.prd_ord_id'		=> 'ord',
			'oprd.prd_id'			=> 'id',
			'oprd.prd_line_id'		=> 'line',
			'oprd.prd_qte'			=> 'qty',
			'oprd.prd_ref'			=> 'ref',
			'oprd.prd_name'			=> 'name',
			'oprd.prd_price_ht'		=> 'priceht',
			'oprd.prd_tva_rate'		=> 'tvarate',
			'oprd.prd_price_ttc'	=> 'pricettc',
			'oprd.prd_ecotaxe'		=> 'ecotaxe',
			'ord.ord_date'			=> 'orddate',
			'oprd.prd_date_created'	=> 'prddate',

			// Colonnes produit source
			'_prd.prd_name'											=> 'p_name',
			'if(_prd.prd_title="",_prd.prd_name,_prd.prd_title)'	=> 'p_title',

			// Colonnes prix de base
			'_prd.prc_id'				=> 'prc_id',
			'_prd.prc_value'			=> 'prc_priceht',
			'_prd.prc_cat_id'			=> 'prc_cat',
			'_prd.prc_grp_id'			=> 'prc_grp'

			// Colonnes produit source
			// 'prd.prd_name'											=> 'p_name',
			// 'if(prd.prd_title="",prd.prd_name,prd.prd_title)'	=> 'p_title',

			// // Colonnes prix de base
			// 'prc.prc_id'				=> 'prc_id',
			// 'prc.prc_value'			=> 'prc_priceht',
			// 'prc.prc_cat_id'			=> 'prc_cat',
			// 'prc.prc_grp_id'			=> 'prc_grp'
		];

		$str_select = '';

		foreach ($ar_select as $select => $as) {
			$str_select .= $select . ' as ' . $as . ', ';
		}
		$str_select = rtrim($str_select, ', ');

		$state = '';
		foreach ($this->status as $st) {
			$state .= 'ord.ord_state_id = ' . $st . ' or ';
		}
		$state = rtrim($state, ' or ');

		//!\\ NE PAS SUPPRIMER //!\\
		// $sql = '
		// 	select ' . $str_select . '
		// 	from ord_products oprd
		// 	inner join ord_orders ord on
		// 			ord.ord_tnt_id = ' . $config['tnt_id'] . '
		// 		and ord.ord_id = oprd.prd_ord_id
		// 		and ord.ord_usr_id = ' . $this->User->getID() . '
		// 		and ord.ord_wst_id = ' . $config['wst_id'] . '
		// 		and (' . $state . ')
		// 	inner join prd_products prd on
		// 			prd.prd_tnt_id = ' . $config['tnt_id'] . '
		// 		and prd.prd_id = oprd.prd_id
		// 		and prd.prd_date_deleted is null
		// 	inner join prc_prices prc on
		// 			prc.prc_is_deleted=0
		// 		and prc.prc_tnt_id= ' . $config['tnt_id'] . '
		// 		and ( prc.prc_date_start<=now() ) and ( prc.prc_date_end>now() )
		// 		and prc.prc_prd_id= oprd.prd_id
		// 		and exists (
		// 			select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id = ' . $config['tnt_id'] . '
		// 		)
		// 		and prc.prc_qte_min=1
		// 		and prc.prc_is_promotion=0
		// 	where
		// 		oprd.prd_tnt_id = ' . $config['tnt_id'] . '
		// 		and not exists (
		// 			select 1
		// 			from fld_object_values fld
		// 			where
		// 					fld.pv_tnt_id = ' . $config['tnt_id'] . '
		// 				and	fld.pv_obj_id_0 = oprd.prd_id
		// 				and	fld.pv_fld_id = ' . _FLD_IS_PORT . '
		// 			limit 1
		// 		)
		// ';

		$sql = '
			select ' . $str_select . '
			from ord_products oprd
			inner join ord_orders ord on
					ord.ord_tnt_id = ' . $config['tnt_id'] . '
				and ord.ord_id = oprd.prd_ord_id
				and ord.ord_usr_id = ' . $this->User->getID() . '
				and ord.ord_wst_id = ' . $config['wst_id'] . '
				and (' . $state . ')
			inner join (
				select
					prd.prd_id as prd_id,
					prd.prd_name as prd_name,
					prd.prd_title as prd_title,
					prc.prc_id as prc_id,
					prc.prc_value as prc_value,
					prc.prc_cat_id as prc_cat_id,
					prc.prc_grp_id as prc_grp_id
				from prd_products prd
				inner join prc_prices prc on
						prc.prc_is_deleted=0
					and prc.prc_tnt_id= ' . $config['tnt_id'] . '
					and ( prc.prc_date_start<=now() ) and ( prc.prc_date_end>now() )
					and prc.prc_prd_id= prd.prd_id
					and exists (
						select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id = ' . $config['tnt_id'] . '
					)
					and prc.prc_qte_min=1
					and prc.prc_is_promotion=0
				where
					prd.prd_tnt_id = ' . $config['tnt_id'] . '
				and prd.prd_date_deleted is null
				group by prd.prd_id
			) _prd on
				_prd.prd_id = oprd.prd_id
		where
			oprd.prd_tnt_id = ' . $config['tnt_id'] . '
			and not exists (
				select 1
				from fld_object_values fld
				where
						fld.pv_tnt_id = ' . $config['tnt_id'] . '
					and	fld.pv_obj_id_0 = oprd.prd_id
					and	fld.pv_fld_id = ' . _FLD_IS_PORT . '
				limit 1
			)
		';

		if (is_array($this->sort)) {
			$sql .= 'order by ';

			foreach ($this->sort as $k => $dir) {
				$sql .= $k . ' ' . $dir;
				break;
			}
		}
		$r_purchases = ria_mysql_query($sql);

		if (!$r_purchases) {
			return $this;
		}
		$this->count = ria_mysql_num_rows($r_purchases);

		if (!$this->count || $this->offset >= $this->count) {
			return $this;
		}
		ria_mysql_data_seek($r_purchases, $this->offset);

		// Création de la liste des produits dans la limite demandé
		$i = 0;
		$ar_purchases = [];

		while ($purchase = ria_mysql_fetch_assoc($r_purchases)) {

			if ($i >= $this->limit) {
				break;
			}

			// $obj_prd = new ProductService([
			// 	'prd'		=> $purchase['id'],
			// 	'withprice'	=> true,
			// 	'nopromo'	=> false
			// ]);

			// $obj_prd->general();

			$ar_purchases[] = [
				// 'prd'	=> $obj_prd->getData(),
				'prd'	=> [
					'id'		=> $purchase['id'],
					'ref'		=> $purchase['ref'],
					'name'		=> $purchase['p_name'],
					'title'		=> $purchase['p_title'],
					'priceht'	=> $purchase['prc_priceht'],
					'stock'		=> 999 // @todo
				],
				'ord'	=> [
					'id'	=> $purchase['ord'],
					'date'	=> $purchase['orddate']
				],
				'line'	=> [
					'id'		=> $purchase['line'],
					'name'		=> $purchase['name'],
					'qty'		=> (int)$purchase['qty'],
					'ref'		=> $purchase['ref'],
					'priceht'	=> $purchase['priceht'],
					'tvarate'	=> $purchase['tvarate'],
					'pricettc'	=> $purchase['pricettc'],
					'ecotaxe'	=> $purchase['ecotaxe'],
					'date'		=> $purchase['prddate'],
					'ecotaxe'	=> $purchase['ecotaxe'],
				]
			];
			$i++;
		}

		$this->purchases = count($ar_purchases) ? $ar_purchases : null;

		return $this;
	}

	/**	Permet de vérifier s'il y a bien un utilisateur en cours
	 * @return	bool	True s'il y a bien un utilisateur en cours, false sinon
	 */
	private function __isUser()
	{
		return $this->User instanceof CustomerService;
	}
}

<?php

require_once('Services/Service.class.php');
require_once('news.inc.php');

/**	\brief Cette classe permet de charger les informations sur un droit
 *
 */
class RightsService extends Service {

  public static function all(){
    $ar_rights = [];

    $r_right = gu_rights_get();
    if( $r_right ){
      while( $right = ria_mysql_fetch_assoc($r_right) ){
        // Tiens compte des exclusions de droits non prient en compte par le projet
        if( is_array(Template::get('rights-excluded')) && in_array($right['code'], Template::get('rights-excluded')) ){
          continue;
        }

        if( !isset($ar_rights[ $right['cat_id'] ]) ){
          $ar_rights[ $right['cat_id'] ] = [
            'id' => $right['cat_id'],
            'name' => $right['cat_name'],
            'rights' => []
          ];
        }

        $ar_rights[ $right['cat_id'] ]['rights'][ $right['id'] ] = [
          'id' => $right['id'],
          'code' => $right['code'],
          'name' => $right['name'],
          'desc' => $right['desc'],
        ];
      }

      ksort( $ar_rights );
    }

    return $ar_rights;
  }
}
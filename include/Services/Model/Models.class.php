<?php
require_once 'Services/Abstract.class.php';
require_once 'Services/Model/Model.class.php';

/**	Cette classe permet le chargement des paniers modèle.
 */
class CartsModelService extends AbstractService
{
	/**	Tableau des paniers modèle
	 * @var	null|array
	 */
	protected $models = null;

	/**	Nombre de paniers modèle
	 * @var	int
	 */
	protected $count = 0;

	/**	Tableau des champs avancés
	 * @var	null|array
	 */
	protected $fields = null;

	/**	Tableau des attributs de requête
	 * @var	array
	 */
	protected $attributes = [
		'mods'				=> 0, //<<<< Identifiant d'un produit ou tableau d'identifiants de paniers modèle
		'fld'				=> false, //<<<< Identifiant d'un champ avancé ou tableau d'identifiants de champs avancés ou tableau fld_id => valeur
		'or_between_fld'	=> false, //<<<< Indique si, pour le tableau $fld, les différents champs sont séparés par des "ET" (vrai par défaut) ou des "OU"
		'limit'				=> -1,

		// 'with_img'			=> false, //<<<< Si vrai, récupére les images des produits
		// 'cfg_img'			=> 'high', //<<<< Nom de la config image à utiliser
		// 'published'			=> null, //<<<< Si vrai seul les produits publiés sont retournés. Si faux, seul les produits non publiés sont retournés. Valeur par défaut : null (tous les produits)
		// 'orderable'			=> null, //<<<< Si vrai seul les produits commandables sont retournés. Si faux, seul les produits non commandables sont retournés. Valeur par défaut : null (tous les produits)
		// 'only_prd_ids'		=> false, //<<<< Retourne uniquement les identifiants des produits si à true
		// 'with_price'		=> false,

	];

	/**	Constructeur de la classe
	 * @return	void
	 */
	public function __construct()
	{
	}

	/**	Retourne un tableau contenant les paniers modèle
	 * @return	array|bool	Tableau des paniers modèle, false sinon
	 */
	public function getModels()
	{
		return is_array($this->models) && count($this->models) > 0 ? $this->models : false;
	}

	/**	Retourne le nombre de paniers modèle
	 * @return	int	Nombre de paniers modèle
	 */
	public function getCount()
	{
		return $this->count;
	}

	/**	Charge les paniers modèle
	 * @todo	Autorisations
	 * @return	CartsModelService	L'instance en cours
	 */
	public function models()
	{
		global $config, $hook;

		$mods = $this->getAttribute('mods');
		$fld = $this->getAttribute('fld');
		$or_between_fld = $this->getAttribute('or_between_fld');
		$limit = $this->getAttribute('limit');
		$limit = $limit > 0 ? $limit : '18446744073709551615';

		$join = '';

		if (is_array($fld) && count($fld)) {
			$i = 1;
			foreach ($fld as $fld_id => $fld_val) {
				$pf = 'fld' . $i;
				$join .= '
					inner join
						fld_object_values ' . $pf . '
					on
						' . $pf . '.pv_tnt_id=' . $config['tnt_id'] . '
					and ' . $pf . '.pv_obj_id_0=m.ord_id
					and ' . $pf . '.pv_fld_id=' . $fld_id . '
				';
				if (is_array($fld_val) && count($fld_val)) {
					$operator = $or_between_fld ? 'or' : 'and';
					// $join .= 'and (fld.pv_value REGEXP "'.implode('" '.$operator.' fld.pv_value REGEXP "', $fld_val).'")';
					$join .= 'and (' . $pf . '.pv_value LIKE "%' . implode('%" ' . $operator . ' ' . $pf . '.pv_value LIKE "%', $fld_val) . '%")';
				} elseif ($or_between_fld) {
					$join .= 'and ' . $pf . '.pv_value in(' . $fld_val . ')';
				} else {
					$join .= 'and ' . $pf . '.pv_value="' . $fld_val . '"';
				}
				$i++;
			}
		}

		$where = '
				m.ord_tnt_id = ' . $config['tnt_id'] . '
			and m.ord_state_id = ' . _STATE_MODEL . '
			and m.ord_wst_id = ' . $config['wst_id'] . '
			and m.ord_masked = 0
			and m.ord_date_archived is null
		';

		if (is_array($mods) && count($mods)) {
			$where .= ' and m.ord_id in(' . implode(',', $mods) . ')';
		}

		$sql = '
			select
				m.ord_id as id
			from
				ord_orders m
			' . $join . '
			where
			' . $where . '
			group by m.ord_id
			limit 0, ' . $limit . '
		';

		$rmod = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rmod)) {
			return $this;
		}

		while ($mod = ria_mysql_fetch_assoc($rmod)) {
			$Model = new CartModelService($mod['id']);
			$Model->setAttribute('only_prd_ids', true);
			$Model->products();

			$hook->do_action('CartsModelService_onLoadingSingleModel', ['Model' => $Model]);

			$this->models[] = $Model;
			$this->count++;
		}

		return $this;
	}

	/**	Permet de controler la valeur d'un attribut et de le mettre à jour
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	bool	True en cas de succes, false sinon
	 */
	protected function __sanitizeAttribute($key, $value)
	{
		if (!is_string($key)) {
			return false;
		}

		$key = trim(strtolower($key));

		switch ($key) {

			case 'mods':
				$this->attributes[$key] = control_array_integer($value, false);
				break;

			case 'fld':
				$this->attributes[$key] = is_array($value) && count($value) ? $value : false;
				break;

			case 'or_between_fld':
				$this->attributes[$key] = is_bool($value) ? $value : false;
				break;

			case 'limit':
				$this->attributes[$key] = is_numeric($value) && $value > 0 ? $value : -1;
				break;

			default:
				return false;
		}
		return true;
	}
}

<?php

require_once('cgv.inc.php');
require_once('Services/Service.class.php');

/**	\brief Cette classe permet de charger les informations sur les conditions générales de ventes.
 * 	Elle récupère automatiquement la dernière version publiée.
 */
class CgvService extends Service {
	private static $instance = null;
	private $version = null; ///< Identifiant de la dernière version publiée

	/** Cette fonction permet d'initialiser un objet panier.
	 * 	@return object L'instance nouvellement créée
	 */
	public static function getInstance(){
		if( is_null(self::$instance) ){
			self::$instance = new CgvService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de charger les informations de CGV.
	 * 	Ces dernières sont les articles regroupés par catégorie
	 * 	@return array Un tableau contenant les CGV
	 */
	public function getData(){
		global $config;

		$ar_art = [];

		if( is_numeric($this->version) && $this->version > 0 ){
			// Récupération des articles de la dernier version publiée
			$r_art = cgv_articles_get( null, null, $this->version, $config['wst_id'] );

			if( $r_art ){
				while( $art = ria_mysql_fetch_assoc($r_art) ){
					// Traduction des articales
					$art = i18n::getTranslation( CLS_CGV_ARTICLE, $art, false, i18n::getLang() );

					$ar_art[] = [
						'id' => $art['id'],
						'title' => $art['name'],
						'content' => view_site_format_riawysiwyg( $art['desc'] ),
					];
				}
			}
		}

		return $ar_art;
	}

	/** Cette fonction permet d'initialiser la récupération des conditions générales de ventes.
	 * 	@return empty
	 */
	private function __construct(){
		$this->version = cgv_versions_get_current();
	}
}
<?php

require_once('tools.faq.inc.php');
require_once('Services/Service.class.php');

/**	\brief Cette classe permet de charger les informations sur la foire aux questions.
 * 	Elle récupère automatiquement la dernière version publiée.
 */
class FaqService extends Service {
	protected $categories = null; ///< Collection des catégories de questions
	private static $instance = null;

	/** Cette fonction permet d'initialiser un objet panier.
	 * 	@return object L'instance nouvellement créée
	 */
	public static function getInstance(){
		if( is_null(self::$instance) ){
			self::$instance = new FaqService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de charger les informations de CGV.
	 * 	Ces dernières sont les articles regroupés par catégorie
	 * 	@return array Un tableau contenant les CGV
	 */
	public function getData(){
		global $config;

		$ar_faq = new Collection();

		foreach( $this->categories->getAll() as &$category ){
			$r_question = faq_questions_get( 0, $category['id'], true );

			if( !ria_mysql_num_rows($r_question) ){
				continue;
			}
			while( $question = ria_mysql_fetch_assoc($r_question) ){
				$category['questions']->addItem([
					'id'		=> $question['id'],
					'title'		=> $question['name'],
					'content'	=> view_site_format_riawysiwyg($question['desc']),
				]);
			}

			$ar_faq->addItem( $category );
		}

		return $this->transformObjectToArray( $this );
	}

	/** Cette fonction permet d'initialiser la récupération des conditions générales de ventes.
	 * 	@return empty
	 */
	private function __construct(){
		$this->categories = new Collection();

		$r_cat = faq_categories_get( 0, true, '', false );
		if( $r_cat ){
			while( $cat = ria_mysql_fetch_assoc($r_cat) ){
				$this->categories->addItem([
					'id' 				=> $cat['id'],
					'title' 			=> $cat['name'],
					'url' 				=> $cat['url_alias'],
					'content'			=> $cat['desc'],
					'contentformated'	=> view_site_format_description($cat['desc']),
					'questions' 		=> new Collection()
				]);
			}
		}
	}
}
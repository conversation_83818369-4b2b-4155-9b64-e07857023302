<?php
	// Variable global liés aux hook (contient un tableau des actions/filtres exécutable)
	global $hook;

	/**	\brief Cette classe permet d'implémenter le système de Hook dans les services RiaShop.
	 *	Classe originale : https://github.com/edouardl/php_hooks (Auteur : <PERSON><PERSON>)
	 *
	 * 	Exemple d'utilisation :
	 * 		- Dans une fonction moteur
	 *		\code{.php}
	 *			$hook->do_action( 'NOM_DU_HOOK', ['var1' => $var1, 'var2' => $var2] );
	 *		\endcode
	 *
	 * 		-
	 * 		\code{.php}
	 * 			$hook->add_action( 'NOM_DU_HOOK', 'NOM_FONCTION_APPELEE', 1 );
	 * 			function NOM_FONCTION_APPELEE( $params ){
	 * 				return $params['var1'].' '.$params['var2'];
	 * 			}
	 * 		\endcode
	 *
	 * 	Quelques règles sur le nom des fonctions :
	 * 		- Dans les services RiaShop : NOM_DE_LA_CLASSE_DU_SERVICE_SAISIE_LIBRE, exemple ProductService_replaceLoadPrice pour
	 * 		remplacer loadPrice dans la fonction chargeant le tarif d'un produit
	 */
	class Hook {

		public $hooks = array();
		private $_default_priority = 10;

		/** Cette fonction permet d'initier le système de hook
		 * 	@return empty Rien
		 */
		function __construct() {

		}

		/** Cette fonction permet d'ajout une action (fonction) qui sera exécuté par un hook spécifique.
		 * 	@param string $hook_name Optionnel, nom du hook qui déclenchera l'action
		 * 	@param string $function_name Optionnel, nom de la fonction qui sera exécuté
		 * 	@param int $priority Optionnel, priorité d'exécution (plus petit pour la première exécution - 10 par défaut)
		 * 	@return bool true si l'ajout s'est correctement déroulé, false dans le cas contraire
		 */
		function add_action( $hook_name = '', $function_name = '', $priority = 0 ) {
			if( !isset( $function_name ) ){
				return false;
			}

			// Priorité par défaut appliqué si non renseignée en paramètre
			if( !isset($priority) || !(intval( $priority ) > 0) ){
				$priority = $this->_default_priority;
			}

			// Ajout du nom de la fonction au tableau des Hook
			$this->hooks[ $hook_name ][] = array(
				'name' => $function_name,
				'priority' => intval( $priority )
			);
			return true;
		}

		/** Cette fonction permet de récupérer toutes les fonctions pour un hook spécifique ou pour tous.
		 * 	@param string $hook_name Optionnel, nom du hook
		 * 	@return array La liste des actions lié au hook en paramètre ou bien tous les hooks avec une liste des actions
		 */
		function get_hook( $hook_name = '' ) {
			if( !isset($hook_name) || empty($hook_name) || !isset($this->hooks[ $hook_name ]) ){
				return $this->hooks;
			}

			return $this->hooks[ $hook_name ];
		}

		/** Cette fonction permet d'exécuter une action pour un hook.
		 * 	@param string $hook_name Obligatoire, nom du hook
		 * 	@param array $akv_params Optionnel, tableau des paramètres qui seront données lors de l'exécution
		 * 	@param bool $try_function Optionnel, si l'exécution est réalisée dans un try...catch (false par défaut)
		 * 	@param bool false en cas d'erreur, true en cas de succès
		 */
		function do_action( $hook_name = '', $akv_params = array(), $try_function = false ) {
			if( empty($hook_name) || !isset($this->hooks[ $hook_name ]) ){
				return false;
			}

			if( !is_array($akv_params) ){
				$akv_params = array();
			}

			// Tri des action selon leur priority
			ksort( $this->hooks[ $hook_name ] );

			// Parcours chaque action définie pour ce hook
			foreach( $this->hooks[ $hook_name ] as $function ) {
				// Utilisation d'un try...catch selon le paramètre
				if( $try_function != true ) {
					if( is_array( $function['name'] ) ) { // S'il s'agit d'un appel d'une classe
						$function['name'][0]->$function['name'][1]( $akv_params );
					} else {
						$function['name']( $akv_params );
					}
				} else {
					try {
						if( is_array( $function['name'] ) ) { // S'il s'agit d'un appel d'une classe
							$function['name'][0]->$function['name'][1]( $akv_params );
						} else {
							$function['name']( $akv_params );
						}
					} catch ( Exception $exc ) {
						error_log( $exc->getTraceAsString() );
					}
				}
			}

			return true;
		}

		/** Cette fonction retourne la valeur après avoir appliqué tous les filtres définis pour un hook
			* @param string $hook_name Obligatoire, nom du hook
			* @param mixed $value_to_filter Optionnel, valeur à éditer ou remplacer lors de l'utilisation des filtres du hook
			* @param array $akv_params Optionnel, liste des paramètres
			* @param bool $try_function Optionnel, si l'exécution est réalisée dans un try...catch (false par défaut)
			* @return mixed chaine en cas de succès, false en cas d'erreur
			*/
		function apply_filter( $hook_name = '', $value_to_filter = false, $akv_params = array(), $try_function = false ) {
			// Check if valid hook to execute actions
			if( empty( $hook_name ) || !isset( $this->hooks[ $hook_name ] ) ) {
				// Return default value, unfiltred
				return $value_to_filter;
			}
			// Sanitize params if it's not an array
			if( !is_array( $akv_params ) ) {
				$akv_params = array();
			}
			// Sort functions by priority (low priority for first execution)
			ksort( $this->hooks[ $hook_name ] );

			// Loop on functions saved for this hook to execute them
			foreach( $this->hooks[ $hook_name ] as $function ) {
				// Use a try..catch or not
				if( $try_function != true ) {
					// check if function from a class
					if( is_array( $function['name'] ) ) {
						// Execute
						$value_to_filter = $function['name'][0]->$function['name'][1]( $value_to_filter, $akv_params );
					} else {
						// Execute
						$value_to_filter = $function['name']( $value_to_filter, $akv_params );
					}
				} else {
					try {
						if( is_array( $function['name'] ) ) {
							// Execute
							$value_to_filter = $function['name'][0]->$function['name'][1]( $value_to_filter, $akv_params );
						} else {
							// Execute
							$value_to_filter = $function['name']( $value_to_filter, $akv_params );
						}
					} catch ( Exception $exc ) {
						echo $exc->getTraceAsString();
					}
				}
			}
			// Return filtred value
			return $value_to_filter;
		}
	}

	/*
	 * Liste des fonctions liées aux hook
	 */
	if( !function_exists( 'do_action' ) ) {
		/** Alias pour un appel rapide à Hook->do_action */
		function do_action( $hook_name = '', $akv_params = array(), $try_function = false ) {
			global $hook;
			return $hook->do_action( $hook_name, $akv_params, $try_function );
		}
	} else {
		error_log( 'Function name : "do_action", is reserved for the Hook Class System.' );
		mail('<EMAIL>', 'Hook duplicate function', 'Function name : "do_action", is reserved for the Hook Class System.');
		exit;
	}

	if( !function_exists( 'apply_filter' ) ) {
		/** Alias pour un appel rapide à Hook->apply_filter */
		function apply_filter( $hook_name = '', $value_to_filter = false, $akv_params = array(), $try_function = false ) {
			global $hook;
			return $hook->apply_filter( $hook_name, $value_to_filter, $akv_params, $try_function );
		}
	} else {
		error_log( 'Function name : "apply_filter", is reserved for the Hook Class System.' );
		mail('<EMAIL>', 'Hook duplicate function', 'Function name : "apply_filter", is reserved for the Hook Class System.');
		exit;
	}

	if( !function_exists( 'add_action' ) ) {
		/** Alias pour un appel rapide à Hook->add_action */
		function add_action( $hook_name, $function_name, $priority = 0 ) {
			global $hook;
			return $hook->add_action( $hook_name, $function_name, $priority );
		}
	} else {
		error_log( 'Function name : "add_action", is reserved for the Hook Class System.' );
		mail('<EMAIL>', 'Hook duplicate function', 'Function name : "add_action", is reserved for the Hook Class System.');
		exit;
	}

	if( !function_exists( 'add_filter' ) ) {
		/** Alias à Hook->add_action pour ajouter un filtre */
		function add_filter( $hook_name = '', $function_name = '', $priority = 0 ) {
			return add_action( $hook_name, $function_name, $priority );
		}
	} else {
		error_log( 'Function name : "add_filter", is reserved for the Hook Class System.' );
		mail('<EMAIL>', 'Hook duplicate function', 'Function name : "add_filter", is reserved for the Hook Class System.');
		exit;
	}

	// Auto initialisation de la classe Hook, initié dans la variable global $hook
	$hook = new Hook();
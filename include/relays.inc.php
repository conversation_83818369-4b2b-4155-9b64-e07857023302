<?php

require_once( 'db.inc.php' );
require_once( 'sys.countries.inc.php' );
require_once( 'strings.inc.php' );
require_once( 'DPD/DPDFrance.class.php' );

/** \defgroup model_relays Points-relais
 *	\ingroup scm
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des points-relais.
 *	Les points-relais et leurs fonctionnalités ne sont rattachés à aucun locataire.
 *	Un point-relais est caractérisé par :
 *		- Un type (généralement une société, tel que Exapaq par exemple).
 *		- Des horaires d'ouvertures sur une grille hebdomadaire.
 *		- Des fermetures exceptionelles.
 *		- Une adresse complète, de préférence géolocalisable (coordonnées GPS).
 *		- Des contraintes métiers pour les colis qui y transitent (poids, dimensions, ...)
 *		- Une durée maximale de conservation du colis à la livraison au point-relais.
 *	Les types de point-relais sont ensuite associés à des locataires ou des sites (table "dlv_relay_types_used").
 *
 *	Il était prévu initialement que certains locataires puissent bannir certains points relais (table "dlv_relays_ban"),
 *	cependant ce système n'est pas en exploitation actuellement et n'a apparemment jamais été testé.
 *
 *	Chaque type de point-relais à son propre retour de service web (généralement du XML dont la structure varie). Des fonctions spécifiques "get_list" existent pour chaque type, mais le résultat XML retourné est harmonisé sur la base du XML du type SO COLISSIMO.
 *
 *	@{
 */

/// Identifiant du type So Colissimo
define( 'SO_COLISSIMO_TYPE', 1 );
/// Identifiant du type Mondial Relay
define( 'MONDIAL_RELAY_TYPE', 2 );
/// Identifiant du type TNT Relay
define( 'TNT_RELAY_TYPE', 3 );
/// Identifiant du type Chronopost
define( 'CHRONOPOST_RELAY_TYPE', 4 );
/// Identifiant du type Lettre de suivies
define( 'LETTRES_SUIVIES_TYPE', 5 );
/// Identifiant du type DPD
define( 'DPD_RELAY_TYPE', 6 );
/// Identifiant du type Glagla Relais
define( 'GLAGLARELAIS_RELAY_TYPE', 7 );
/// Identifiant du type GLS
define( 'GLS_RELAY_TYPE', 8 );

/// Nombre de jours par défaut à partir duquel un point-relais non mis à jour est considéré comme obsolète
define( 'DAYS_UPD_MAX', 7 );

/** Cette fonction crée un nouveau type de point-relais
 *	@param string $name Obligatoire, nom du type (Exemples : So Colissimo, Exapaq...). Ne peut pas être une chaîne vide
 *	@param string $desc Facultatif, description
 *	@param string $url_site Facultatif, URL du site web
 *	@param string $url_service Facultatif, URL du service web (permettant une récupération dynamique des points-relais qui y sont rattachés)
 *
 *	@return int L'identifiant du type crée, ou false en cas d'écehc
 */
function dlv_relay_types_add( $name, $desc=false, $url_site=false, $url_service=false ){
	$name = trim( $name );
	if( !$name ) return false;

	$keys = array( 'rlt_name' );
	$values = array( $name );

	if( $desc ){
		$keys[] = 'rlt_desc';
		$values[] = '\''.addslashes( $desc ).'\'';
	}
	if( $url_site ){
		$keys[] = 'rlt_url_site';
		$values[] = '\''.addslashes( $url_site ).'\'';
	}
	if( $url_service ){
		$keys[] = 'rlt_url_service';
		$values[] = '\''.addslashes( $url_service ).'\'';
	}

	if( ria_mysql_query( 'insert into dlv_relay_types ('.implode( ', ', $keys ).') values ('.implode( ', ', $values ).')' ) ){
		return ria_mysql_insert_id();
	}

	return false;
}

/** Cette fonction permet de mettre à jour les informations concernant un type de point-relais spécifié
 *	@param int $id Obligatoire, identifiant du type de point-relais
 *	@param string $name Facultatif, nouveau nom du type. False ne change pas la valeur en place. Dans le cas contraire, une chaîne non-vide est obligatoire
 *	@param string $desc Facultatif, nouvelle description. False ne change pas la valeur en place, Null assigne la valeur NULL.
 *	@param string $url_site Facultatif, nouvelle URL de site. False ne change pas la valeur en place, Null assigne la valeur NULL.
 *	@param string $url_service Facultatif, nouvelle URL du service. False ne change pas la valeur en place, Null assigne la valeur NULL.
 *
 *	@return bool True en cas de succès (ou si rien n'a été modifié), False en cas d'échec
 */
function dlv_relay_types_upd( $id, $name=false, $desc=false, $url_site=false, $url_service=false ){
	if( !dlv_relay_types_exists( $id ) ) return false;
	if( $name===false && $desc===false && $url_site===false && $url_service===false ) return true;
	if( $name!==false ){
		$name = trim( $name );
		if( !$name ) return false;
	}

	$updated = array();
	if( $name!==false ){
		$updated[] = 'rlt_name=\''.addslashes( $name ).'\'';
	}
	if( $desc!==false ){
		$updated[] = 'rlt_desc='.( $desc===null ? 'NULL' : '\''.addslashes( $desc ).'\'' );
	}
	if( $url_site!==false ){
		$updated[] = 'rlt_url_site='.( $url_site===null ? 'NULL' : '\''.addslashes( $url_site ).'\'' );
	}
	if( $url_service!==false ){
		$updated[] = 'rlt_url_service='.( $url_service===null ? 'NULL' : '\''.addslashes( $url_service ).'\'' );
	}

	$sql = 'update dlv_relay_types set '.implode( ', ', $updated ).' where rlt_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction détermine l'existence d'un type de point-relais
 *	@param int $id Identifiant du type à tester
 *
 *	@return bool true si le type existe, False sinon
 */
function dlv_relay_types_exists( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;

	$sql = 'select 1 from dlv_relay_types where rlt_id='.$id;

	if( $res = ria_mysql_query( $sql ) )
		return ria_mysql_num_rows( $res );

	return false;
}

/** Cette fonction crée un nouveau point-relais
 * 	Si le point existe déjà par sa paire référence / type, il sera mis à jour. L'échec de la création des périodes d'ouveture / fermeture n'impacte pas le résultat final retourné
 *	@param string $ref Obligatoire, référence interne au type de point-relais. Ne peut pas être une chaîne vide
 *	@param int $type_id Obligatoire, identifiant du type de point-relais
 *	@param string $name Obligatoire, nom du point-relais. Ne peut pas être une chaîne vide
 *	@param string $address1 Obligatoire, adresse principale du point-relais. Ne peut pas être une chaîne vide
 *	@param string $address2 Obligatoire, complément pour l'adresse du point-relais
 *	@param string $zipcode Obligatoire, code postal du point-relais. Ne peut pas être une chaîne vide
 *	@param string $city Obligatoire, ville du point-relais. Ne peut pas être une chaîne vide
 *	@param $cnt_code Obligatoire Code ISO du pays du point-relais. Une valeur vide sera remplacée par "FR"
 *	@param string $phone Obligatoire, numéro de téléphone du point-relais
 *	@param $latitude Facultatif, latitude pour les coordonnées GPS du point-relais
 *	@param $longitude Facultatif, longitude pour les coordonnées GPS du point-relais
 *	@param $delay_max Facultatif, nombre de jours maximals pendant lesquels un colis est stocké dans le point-relais
 *	@param $weight_min Facultatif, poids minimal du colis
 *	@param $weight_max Facultatif, poids maximal du colis. Si précisé et $weight_min précisé, $weight_max doit être supérieur à $weight_min
 *	@param $width_max Facultatif, largeur maximale du colis
 *	@param $height_max Facultatif, hauteur maximale du colis
 *	@param $length_max Facultatif, longueur maximale du colis
 *	@param $access_disabled Facultatif, détermine si le point-relais est accessible aux personnes à mobilité réduite (False par défaut)
 *	@param $open_periods Facultatif, détermine les périodes d'ouverture habituelles du point-relais. Il s'git d'un tableau de tableaux, les arguments de chaque sous-tableau sont : jour, ouverture, fermeture. Se réferer à la fonction dlv_relay_periods_add
 *	@param $vacation_periods Facultatif, détermine les périodes de fermeture annuelle du point-relais. Il s'git d'un tableau de tableaux, les arguments de chaque sous-tableau sont : date de début, date de fin. Se référer à la fonction dlv_relay_vacations_add
 *	@param $from_service Facultatif, si False, la date de dernière MAJ restera à NULL (autrement dit, la création ne provient pas des données récentes du service)
 *	@param $type Facultatif, type de point relais (par exemple pour So Colissimo : BPR pour Bureau de postal ou A2P pour domicile)
 *
 *	@return int L'identifiant du point relais crée (ou modifié) en cas de succès, False en cas d'échec
 */
function dlv_relays_add( $ref, $type_id, $name, $address1, $address2, $zipcode, $city, $cnt_code, $phone, $latitude=null, $longitude=null, $delay_max=null, $weight_min=null, $weight_max=null, $width_max=null, $height_max=null, $length_max=null, $access_disabled=false, $open_periods=null, $vacation_periods=null, $from_service=true, $type=null ){

	// trim et cast
	$ref = trim( $ref );
	$name = trim( $name );
	$address1 = trim( $address1 );
	$address2 = trim( $address2 );
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	$cnt_code = strtoupper( trim( $cnt_code ) );
	$phone = trim( $phone );
	$cnt_code = $cnt_code=='' ? 'FR' : $cnt_code;

	// contrôles
	if( !dlv_relay_types_exists( $type_id ) ) return false;
	$name_test = sys_countries_get_name( $cnt_code );
	if( !$name_test ) return false;
	if( $ref=='' || $name=='' || $address1=='' || $zipcode=='' || $city=='' ) return false;

	// existence du point-relais (update)
	$exist_id = dlv_relays_exists_ref( $ref, $type_id );
	if( $exist_id ){
		$result = dlv_relays_upd( $exist_id, $name, $address1, $address2, $zipcode, $city, $cnt_code, $phone, $latitude, $longitude, $delay_max, $weight_min, $weight_max, $width_max, $height_max, $length_max, $access_disabled, $open_periods, $vacation_periods, true, $type );
		return $result ? $exist_id : false;
	}

	// création des paires clé/valeur
	$keys = array( 'rly_ref', 'rly_rlt_id', 'rly_name', 'rly_address1', 'rly_address2', 'rly_zipcode', 'rly_city', 'rly_cnt_code', 'rly_phone' );
	$values = array( '\''.addslashes( $ref ).'\'', $type_id, '\''.addslashes( $name ).'\'', '\''.addslashes( $address1 ).'\'', '\''.addslashes( $address2 ).'\'', '\''.addslashes( $zipcode ).'\'', '\''.addslashes( $city ).'\'', '\''.addslashes( $cnt_code ).'\'', '\''.addslashes( $phone ).'\'' );

	if( is_numeric( $latitude ) ){
		$keys[] = 'rly_latitude';
		$values[] = $latitude;
	}
	if( is_numeric( $longitude ) ){
		$keys[] = 'rly_longitude';
		$values[] = $longitude;
	}
	if( is_numeric( $delay_max ) && $delay_max>=0 ){
		$keys[] = 'rly_delay_max';
		$values[] = $delay_max;
	}
	if( is_numeric( $weight_min ) && $weight_min>=0 ){
		$keys[] = 'rly_weight_min';
		$values[] = $weight_min;
	}
	if( is_numeric( $weight_max ) && $weight_max>0 && ( !is_numeric( $weight_min ) || $weight_max>$weight_min ) ){
		$keys[] = 'rly_weight_max';
		$values[] = $weight_max;
	}
	if( is_numeric( $width_max ) && $width_max>=0 ){
		$keys[] = 'rly_width_max';
		$values[] = $width_max;
	}
	if( is_numeric( $height_max ) && $height_max>=0 ){
		$keys[] = 'rly_height_max';
		$values[] = $height_max;
	}
	if( is_numeric( $length_max ) && $length_max>=0 ){
		$keys[] = 'rly_length_max';
		$values[] = $length_max;
	}
	if( $access_disabled ){
		$keys[] = 'rly_access_disabled';
		$values[] = '1';
	}
	if( $from_service ){
		$keys[] = 'rly_last_access';
		$values[] = 'now()';
	}
	if( $type !== null && trim($type) != '' ){
		$keys[] = 'rly_type';
		$values[] = '"'.addslashes( $type ).'"';
	}

	// insertion
	if( ria_mysql_query( 'insert into dlv_relays ('.implode( ', ', $keys ).') values ('.implode( ', ', $values ).')' ) ){
		$rly_id = ria_mysql_insert_id();

		// ajout des horaires d'ouverture
		if( is_array( $open_periods ) && sizeof( $open_periods ) ){
			foreach( $open_periods as $op ){
				if( is_array( $op ) && sizeof( $op )==3 )
					dlv_relay_periods_add( $rly_id, $op[0], $op[1], $op[2] );
			}
		}

		// ajout des périodes de fermeture
		if( is_array( $vacation_periods ) && sizeof( $vacation_periods ) ){
			foreach( $vacation_periods as $vp ){
				if( is_array( $vp ) && sizeof( $vp )==2 )
					dlv_relay_vacations_add( $rly_id, $op[0], $op[1] );
			}
		}

		return $rly_id;
	}

	return false;
}

/** Cette fonction met à jour un point-relais existant
 *	L'échec de la création des périodes d'ouveture / fermeture n'impacte pas le résultat final retourné
 * 	@param int $id obligatoire, identifiant du point-relais à mettre à jour
 *	@param string $name Obligatoire, nom du point-relais. Ne peut pas être une chaîne vide
 *	@param string $address1 Obligatoire, adresse principale du point-relais. Ne peut pas être une chaîne vide
 *	@param string $address2 Obligatoire, complément pour l'adresse du point-relais
 *	@param string $zipcode Obligatoire, code postal du point-relais. Ne peut pas être une chaîne vide
 *	@param string $city Obligatoire, ville du point-relais. Ne peut pas être une chaîne vide
 *	@param $cnt_code Obligatoire Code ISO du pays du point-relais. Une valeur vide sera remplacée par "FR"
 *	@param string $phone Obligatoire, numéro de téléphone du point-relais
 *	@param $latitude Facultatif, latitude pour les coordonnées GPS du point-relais
 *	@param $longitude Facultatif, longitude pour les coordonnées GPS du point-relais
 *	@param $delay_max Facultatif, nombre de jours maximals pendant lesquels un colis est stocké dans le point-relais
 *	@param $weight_min Facultatif, poids minimal du colis
 *	@param $weight_max Facultatif, poids maximal du colis. Si précisé et $weight_min précisé, $weight_max doit être supérieur à $weight_min
 *	@param $width_max Facultatif, largeur maximale du colis
 *	@param $height_max Facultatif, hauteur maximale du colis
 *	@param $length_max Facultatif, longueur maximale du colis
 *	@param $access_disabled Facultatif, détermine si le point-relais est accessible aux personnes à mobilité réduite
 *	@param $open_periods Facultatif, détermine les périodes d'ouverture habituelles du point-relais. Il s'git d'un tableau de tableaux, les arguments de chaque sous-tableau sont : jour, ouverture, fermeture. Se réferer à la fonction dlv_relay_periods_add
 *	@param $vacation_periods Facultatif, détermine les périodes de fermeture annuelle du point-relais. Il s'git d'un tableau de tableaux, les arguments de chaque sous-tableau sont : date de début, date de fin. Se référer à la fonction dlv_relay_vacations_add
 *	@param $from_service Facultatif, si False, la date de dernière MAJ ne sera pas modifiée (autrement dit, la mise à jour ne provient pas des données récentes du service)
 *	@param $type Facultatif, type de point relais (par exemple pour So Colissimo : BPR pour Bureau de postal ou A2P pour domicile)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_relays_upd( $id, $name, $address1, $address2, $zipcode, $city, $cnt_code, $phone, $latitude=null, $longitude=null, $delay_max=null, $weight_min=null, $weight_max=null, $width_max=null, $height_max=null, $length_max=null, $access_disabled=null, $open_periods=null, $vacation_periods=null, $from_service=true, $type=null ){

	// trim et cast
	$name = trim( $name );
	$address1 = trim( $address1 );
	$address2 = trim( $address2 );
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	$cnt_code = strtoupper( trim( $cnt_code ) );
	$phone = trim( $phone );
	$cnt_code = $cnt_code=='' ? 'FR' : $cnt_code;

	// contrôles
	if( !dlv_relays_exists( $id ) ) return false;
	$name_test = sys_countries_get_name( $cnt_code );
	if( !$name_test ) return false;
	if( $name=='' || $address1=='' || $zipcode=='' || $city=='' ) return false;

	$sql = '
		update dlv_relays
		set
			rly_name=\''.addslashes( $name ).'\',
			rly_address1=\''.addslashes( $address1 ).'\',
			rly_address2=\''.addslashes( $address2 ).'\',
			rly_zipcode=\''.addslashes( $zipcode ).'\',
			rly_city=\''.addslashes( $city ).'\',
			rly_cnt_code=\''.addslashes( $cnt_code ).'\',
			rly_phone=\''.addslashes( $phone ).'\',
			rly_latitude='.( is_numeric( $latitude ) ? $latitude : 'NULL' ).',
			rly_longitude='.( is_numeric( $longitude ) ? $longitude : 'NULL' ).',
			rly_delay_max='.( is_numeric( $delay_max ) && $delay_max>=0 ? $delay_max : 'NULL').',
			rly_weight_min='.( is_numeric( $weight_min ) && $weight_min>=0 ? $weight_min : 'NULL' ).',
			rly_weight_max='.( is_numeric( $weight_max ) && $weight_max>0 && ( !is_numeric( $weight_min ) || $weight_max>$weight_min ) ? $weight_max : 'NULL' ).',
			rly_width_max='.( is_numeric( $width_max ) && $width_max>=0 ? $width_max : 'NULL' ).',
			rly_height_max='.( is_numeric( $height_max ) && $height_max>=0 ? $height_max : 'NULL' ).',
			rly_length_max='.( is_numeric( $length_max ) && $length_max>=0 ? $length_max : 'NULL' ).',
			rly_access_disabled='.( $access_disabled ? '1' : '0' ).',
			rly_last_access='.( $from_service ? 'now()' : 'rly_last_access' ).',
			rly_type='.( $type !== null && trim($type) != '' ? '"'.addslashes( $type ).'"' : 'NULL' ).'
		where
			rly_id='.$id.'
			and rly_date_deleted is null
	';

	if( ria_mysql_query( $sql ) ){

		// ajout des horaires d'ouverture
		dlv_relay_periods_del( $id );
		if( is_array( $open_periods ) && sizeof( $open_periods ) ){
			foreach( $open_periods as $op ){
				if( is_array( $op ) && sizeof( $op )==3 )
					dlv_relay_periods_add( $id, $op[0], $op[1], $op[2] );
			}
		}

		// ajout des périodes de fermeture
		dlv_relay_vacations_del( $id );
		if( is_array( $vacation_periods ) && sizeof( $vacation_periods ) ){
			foreach( $vacation_periods as $vp ){
				if( is_array( $vp ) && sizeof( $vp )==2 )
					dlv_relay_vacations_add( $id, $op[0], $op[1] );
			}
		}

		return true;
	}

	return false;
}

/** Cette fonction supprime virtuellement un point-relais
 *	@param int $id Identifiant du point-relais
 *
 *	@return bool True en cas de succès (ou si le point relais n'existait pas), False sinon
 */
function dlv_relays_del( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;
	if( !dlv_relays_exists( $id ) ) return true;

	$sql = 'update dlv_relays set rly_date_deleted=now() where rly_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction rétablit un point-relais virtuellement supprimé
 *	@param int $id Identifiant du point-relais
 *
 *	@return bool True en cas de succès (ou si le point-relais existait déjà), False sinon
 */
function dlv_relays_retablish( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;
	if( dlv_relays_exists( $id ) ) return true;

	$sql = 'update dlv_relays set rly_date_deleted=NULL where rly_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction détermine l'existence d'un point-relais
 *	@param int $id Identifiant du point-relais
 *
 *	@return bool true s'il existe, False sinon
 */
function dlv_relays_exists( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;

	if( $res = ria_mysql_query( 'select 1 from dlv_relays where rly_date_deleted is null and rly_id='.$id ) ){
		return ria_mysql_num_rows( $res );
	}

	return false;
}

/** Cette fonction détermine l'existence d'un point-relais via sa clé référence interne / type
 *	@param string $ref Référence interne du point-relais
 *	@param int $type_id Identifiant du type de point-relais
 *
 *	@return int l'identifiant si le point relais existe, False sinon
 */
function dlv_relays_exists_ref( $ref, $type_id ){
	if( !dlv_relay_types_exists( $type_id ) ) return false;

	if( $res = ria_mysql_query( 'select rly_id as id from dlv_relays where rly_date_deleted is null and rly_rlt_id='.$type_id.' and rly_ref=\''.addslashes( $ref ).'\'' ) ){
		if( $relay = ria_mysql_fetch_array( $res ) )
			return $relay['id'];
	}

	return false;
}

/**	Cette fonction récupère un point-relais unique par son identifiant, sans controler sa date de dernière MAJ par le service web (back-office et fonctions internes)
 *	@param int $id Identifiant du point-relais
 *	@return bool|resource Voir le retour de la fonction dlv_relays_get()
 */
function dlv_relays_get_simple( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	return dlv_relays_get( $id, '', 0, '', array(), array(), array(), false, array(), array(), false, null, false );
}

/** Cette fonction récupère des points-relais selon des paramètres optionnels fournis. Elle doit être appelée notamment si l'appel au service web a échoué
 *	Par défaut, les points-relais sont triés par type et par référence (sauf si $gps est précisé, dans ce cas le tri est par rayon croissant)
 *	@param int $id Facultatif, identifiant d'un point-relais (ou tableau d'identifiants)
 *	@param string $ref Facultatif, référence interne d'un point-relais
 *	@param int $type Facultatif, type de point-relais
 *	@param string $name_like Facultatif, nom ou partie du nom du point-relais
 *	@param array $address Facultatif, tableau associatif de coordonnées géographiques ('address1', 'address2', 'zipcode', 'city', 'cnt_code')
 *	@param array $gps Facultatif, tableau associatif de coordonnées GPS à une certaine proximité (en mêtres) du ou des point(s)-relais ('latitude', 'longitude', 'rayon')
 *	@param array $opened_at Facultatif, tableau de dates où le point-relais doit être ouvert
 *	@param bool|float $weight Facultatif, poids du colis, qui doit entrer dans les critères du point-relais
 *	@param array $dims Facultatif, dimensions du colis sous la forme d'un tableau associatif ('height', 'length', 'width')
 *	@param array $sort Facultatif, paramètre de tri sous la forme d'un tableau associatif (la clé est un des alias des champs retournés, la valeur est 'asc' ou 'desc')
 *	@param bool $include_ban Facultatif, ce paramètre n'est plus utilisé
 *	@param bool $access_disabled Facultatif, détermine si le point-relais doit être accessible aux personnes à mobilité réduite. Par défaut, cet argument est ignoré
 *	@param int $days_upd_max Facultatif, détermine le nombre de jours maximal depuis lequel le point-relais n'a pas été mis à jour par le service. La valeur apr défaut est la constante DAYS_UPD_MAX. Une valeur non numérique ou inférieure à 0 retirera le filtre
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du point-relais
 *		- ref : Référence du point-relais
 *		- type : Type de point-relais
 *		- name : Nom du point-relais
 *		- address1 : Adresse principale du point-relais
 *		- address2 : Complément d'adresse du point-relais
 *		- zipcode : Code postal du point-relais
 *		- city : Ville du point-relais
 *		- cnt_code : Code ISO du pays du point-relais
 *		- country : Pays en toutes lettres
 *		- phone : N° de téléphone du point relais
 *		- latitude : Coordonnée du point-relais
 *		- longitude : Coordonnée du point-relais
 *		- weight_min : Poids minimal du colis pour ce point-relais
 *		- weight_max : Poids maximal du colis pour ce point-relais
 *		- delay_max : Délai de garde maximal, en jours, pour ce point-relais
 *		- height_max : Hauteur maximale du colis
 *		- width_max : Largeur maximale du colis
 *		- length_max : Longueur maximale du colis
 *		- access_disabled : Détermine si le point-relais est accessible aux personnes à mobilité réduite
 *		- rayon (si $gps est précise uniquement) : distance du point-relais par rapport au point donné, en mêtres
 *		- last_access : dernière fois que le point-relais a été mis à jour par rapport au service
 */
function dlv_relays_get( $id=0, $ref='', $type=0, $name_like='', $address=array(), $gps=array(), $opened_at=array(), $weight=false, $dims=array(), $sort=array(), $include_ban=false, $access_disabled=null, $days_upd_max=DAYS_UPD_MAX ){
	global $config;

	// Contrôle des arguments
	if( is_array( $id ) ){
		foreach( $id as $one_id ){
			if( !is_numeric( $one_id ) || $one_id<=0 ) return false;
		}
	}else{
		if( !is_numeric( $id ) || $id<0 ) return false;
		if( $id )
			$id = array( $id );
		else
			$id = array();
	}
	if( $type!=0 && !dlv_relay_types_exists( $type ) ) return false;

	// Parse des arguments
	$ref = trim( $ref );
	$name_like = trim( $name_like );
	if( !is_array( $address ) ) $address = array();
	if( !is_array( $gps ) ) $gps = array();
	elseif( !isset( $gps['latitude'] ) || !is_numeric( $gps['latitude'] ) )  $gps = array();
	elseif( !isset( $gps['longitude'] ) || !is_numeric( $gps['longitude'] ) )  $gps = array();
	elseif( !isset( $gps['rayon'] ) || !is_numeric( $gps['rayon'] ) )  $gps = array();
	if( !is_array( $opened_at ) ) $opened_at = array();
	else{
		$tmp = array();
		foreach( $opened_at as $opa ){
			if( isdate( $opa ) ){
				if( isdateheure( $opa ) ) $tmp[] = dateheureparse( $opa );
				else $tmp[] = dateparse( $opa );
			}
		}
		$opened_at = $tmp;
	}
	if( $weight!==false && !is_numeric( $weight ) || $weight<=0 ) $weight = false;

	// select
	$sql = '
		select
			rly_id as id, rly_ref as "ref", rly_rlt_id as "type", rlt_name as type_name, rly_name as "name", rly_address1 as address1, rly_address2 as address2, rly_zipcode as zipcode, rly_city as city,
			rly_cnt_code as "cnt_code", cnt_name as "country", rly_phone as phone, rly_latitude as latitude, rly_longitude as longitude, rly_weight_min as weight_min, rly_weight_max as weight_max,
			rly_delay_max as delay_max, rly_height_max as height_max, rly_width_max as width_max, rly_length_max as length_max, rly_access_disabled as access_disabled,
			rly_last_access as last_access
	';
	if( sizeof( $gps ) ){
		$sql .= ', ABS(6378137 * 2 * ATAN2(SQRT(SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) * SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) + COS(RADIANS('.$gps['latitude'].')) * COS(RADIANS(rly_latitude)) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2)), SQRT(1 - (SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) * SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) + COS(RADIANS('.$gps['latitude'].')) * COS(RADIANS(rly_latitude)) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2))))) as "rayon" ';
	}

	// from
	$sql .= '
		from dlv_relays
			join dlv_relay_types on rly_rlt_id=rlt_id
			join dlv_relay_types_used on rly_rlt_id=rlw_rlt_id and rlw_tnt_id='.$config['tnt_id'].'
	';
	if( sizeof( $opened_at ) ){
		$sql .= '
			join dlv_relay_periods on rly_id=rlp_rly_id
			left join dlv_relay_vacations on rly_id=rlv_rly_id
		';
	}
	$sql .= 'left join sys_countries on rly_cnt_code=cnt_code';

	// where
	$sql .= ' where rly_date_deleted is null';
	if( sizeof( $id ) )
		$sql .= ' and rly_id in ('.implode( ', ', $id ).')';
	if( $ref )
		$sql .= ' and rly_ref=\''.addslashes( $ref ).'\'';
	if( $type )
		$sql .= ' and rly_rlt_id='.$type;
	if( $name_like )
		$sql .= ' and rly_name like \'%'.addslashes( $name_like ).'%\'';
	if( sizeof( $address ) ){
		$allowed = array( 'address1', 'address2', 'zipcode', 'city', 'cnt_code' );
		foreach( $address as $k=>$v ){
			if( in_array( $k, $allowed ) && trim( $v )!='' ){
				$v = mb_strtolower( str_replace( array('\'', ',', '-', 'sainte ', 'saint '), array(' ', ' ', ' ', 'ste ', 'st '), $v ) );
				$k = "lower(replace(replace(replace(replace(replace( rly_".$k.", '\'', ' ' ), ',', ' ' ), '-', ' ' ), 'sainte ', 'ste ' ), 'saint ', 'st ' ))";
				$sql .= ' and '.$k.'=\''.addslashes( trim( $v ) ).'\'';
			}
		}
	}
	if( sizeof( $gps ) ){
		$sql = '
			and ABS(6378137 * 2 * ATAN2(SQRT(SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) * SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) + COS(RADIANS('.$gps['latitude'].')) * COS(RADIANS(rly_latitude)) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2)), SQRT(1 - (SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) * SIN((RADIANS(rly_latitude) - RADIANS('.$gps['latitude'].')) / 2) + COS(RADIANS('.$gps['latitude'].')) * COS(RADIANS(rly_latitude)) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2) * SIN((RADIANS(rly_longitude) - RADIANS('.$gps['longitude'].')) / 2)))))<=ABS('.$gps['rayon'].')
		';
	}
	if( sizeof( $opened_at ) ){
		foreach( $opened_at as $opa ){
			$sql .= ' and ( rlv_rly_id is null or rlv_date_start>\''.$opa.'\' or rlv_date_stop<\''.$opa.'\' ) and if(rlp_day=7,1,rlp_day-1)=DAYOFWEEK(\''.$opa.'\')';
			if( isdateheure( $opa ) ){
				$time_array = explode( ':', str_replace( substr( $opa, 0, 11 ), '', $opa ) );
				$titme_total = ( $time_array[0] * 60 ) + $time_array[1];
				$sql .= ' and rlp_hour_stop>='.$titme_total.' and rlp_hour_stop<='.$titme_total;
			}
		}
	}
	if( $weight )
		$sql .= ' and rly_weight_min<='.$weight.' and rly_weight_max>='.$weight;
	if( is_array( $dims ) && sizeof( $dims ) ){
		$allowed = array( 'length', 'height', 'width' );
		foreach( $dims as $k=>$v ){
			if( in_array( $k, $allowed ) && is_numeric( $v ) && $v>0 )
				$sql .= ' and rly_'.$k.'_min='.$v;
		}
	}
	if( $access_disabled!==null ){
		if( $access_disabled )
			$sql .= ' and rly_access_disabled=1';
		else
			$sql .= ' and rly_access_disabled=0';
	}
	if( is_numeric($days_upd_max) && $days_upd_max>=0 ){
		$sql .= ' and DATEDIFF(now(), rly_last_access)<='.$days_upd_max;
	}

	// group by
	$sql .= '
		group by rly_id
	';

	// order by
	$sql .= '
		order by
	';
	$sorted = false;
	if( sizeof( $sort ) ){
		$allowed = array( 'id', 'ref', 'type', 'name', 'address1', 'address2', 'zipcode', 'city', 'cnt_code', 'phone', 'latitude', 'longitude', 'weight_min', 'weight_max', 'delay_max', 'height_max', 'width_max', 'length_max' );
		if( sizeof( $gps ) ) $allowed[] = 'rayon';
		foreach( $sort as $k=>$v ){
			if( in_array( $k, $allowed ) ){
				if( !$sorted )
					$sorted = true;
				else
					$sql .= ', ';
				$sql .= ' "'.$k.'" '.( $v=='desc' ? 'desc' : 'asc' );
			}
		}
	}
	if( !$sorted ){
		if( sizeof( $gps ) )
			$sql .= ' "rayon" asc';
		else
			$sql .= ' "type" asc, "ref" asc';
	}

	return ria_mysql_query( $sql );
}

/** Récupère le code type d'un point-relais en fonction de son identifiant
 *	@param int $id Identifiant du point-relais
 *
 *	@return La référence du point-relais ou False en cas d'échec
 */
function dlv_relays_get_type( $id ){
	if( !is_numeric( $id ) || $id<=0 ){
		return false;
	}

	$sql = '
		select ifnull(rly_type, "") as type
		from dlv_relays
		where rly_date_deleted is null
			and rly_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows( $res ) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['type'];
}

/** Récupère la référence interne d'un point-relais en fonction de son identifiant
 *	@param int $id Identifiant du point-relais
 *
 *	@return La référence du point-relais ou False en cas d'échec
 */
function dlv_relays_get_ref( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;

	$sql = 'select rly_ref from dlv_relays where rly_date_deleted is null and rly_id='.$id;

	$r = ria_mysql_query( $sql );
	if( !$r || !ria_mysql_num_rows( $r ) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction crée une période d'ouverture habituelle pour un point-relais donné
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param $day Obligatoire, jour de la semaine (de 1 pour lundi à 7 pour dimanche)
 *	@param $start Obligatoire, minute de début, sur une journée de 0 (minuit) à 1439 (23:59) minutes
 *	@param $stop Obligatoire, minute de fin, sur une journée de 0 (minuit) à 1439 (23:59) minutes
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_relay_periods_add( $id, $day, $start, $stop ){

	if( !dlv_relays_exists( $id ) ) return false;
	if( !is_numeric( $day ) || $day<1 || $day>7 ) return false;
	if( !is_numeric( $start ) || $start<0 ) return false;
	if( !is_numeric( $stop ) || $stop<0 ) return false;

	while( $start>=1440 ) $start -= 1440;
	while( $stop>=1440 ) $stop -= 1440;

	$sql = '
		insert into dlv_relay_periods (
			rlp_rly_id, rlp_day, rlp_hour_start, rlp_hour_stop
		) values (
			'.$id.', '.$day.', '.$start.', '.$stop.'
		)
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction supprime une ou plusieurs période(s) d'ouverture(s) habituelle(s) d'un point-relais
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param $day Facultatif, jour (entre 1 pour lundi et 7 pour dimanche), ou tableau de jours, à supprimer
 *	@param $start Facultatif, horaire, en minutes à compter de minuit, à supprimer
 *
 *	@return bool True en cas de succès, False sinon
 */
function dlv_relay_periods_del( $id, $day=0, $start=null ){

	if( !dlv_relays_exists( $id ) ) return false;

	if( is_array( $day ) ){
		foreach( $day as $d ){
			if( !is_numeric( $d ) || $d<1 || $d>7 ) return false;
		}
	}else{
		if( !is_numeric( $day ) || $day<0 || $day>7 ) return false;
		if( $day>0 )
			$day = array( $day );
		else
			$day = array();
	}

	if( $start!==null ){
		if( !is_numeric($start) || $start<0 ) return false;
	}

	$sql = '
		delete from dlv_relay_periods
		where
			rlp_rly_id='.$id.'
	';
	if( sizeof( $day ) )
		$sql .= ' and rlp_day in ('.implode( ', ', $day ).')';
	if( $start!==null )
		$sql .= ' and rlp_hour_start='.$start;

	return ria_mysql_query( $sql );
}

/** Cette fonction récupère les périodes d'ouverture habituelles d'un point-relais
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param $day Facultatif, jour d'ouverture (entre 1 pour lundi et 7 pour dimanche), ou tableau de jours
 *	@param $time_in Facultatif, détermine une minute (sur une journée de 1440 minutes, base 0) où le point-relais doit être ouvert
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifaint du point-relais
 *		- day : Jour d'ouverture
 *		- day_name : Nom du jour d'ouverture
 *		- start : Minute d'ouverture (0 étant le début d'une journée / minuit)
 *		- stop : Minute de fermeture
 */
function dlv_relay_periods_get( $id, $day=0, $time_in=null ){

	if( !dlv_relays_exists( $id ) ) return false;

	if( is_array( $day ) ){
		foreach( $day as $d ){
			if( !is_numeric( $d ) || $d<1 || $d>7 ) return false;
		}
	}else{
		if( !is_numeric( $day ) || $day<0 || $day>7 ) return false;
		if( $day>0 )
			$day = array( $day );
		else
			$day = array();
	}

	if( $time_in!==null ){
		if( !is_numeric( $time_in ) || $time_in<0 ) return false;
		while( $time_in>=1440 )
			$time_in -= 1440;
	}

	$sql = '
		select
			rlp_rly_id as id,
			rlp_day as "day",
			if(rlp_day=1,"Lundi",if(rlp_day=2,"Mardi",if(rlp_day=3,"Mercredi",if(rlp_day=4,"Jeudi",if(rlp_day=5,"Vendredi",if(rlp_day=6,"Samedi","Dimanche")))))) as day_name,
			rlp_hour_start as "start",
			rlp_hour_stop as "stop"
		from
			dlv_relay_periods
		where
			rlp_rly_id='.$id.'
	';
	if( sizeof( $day ) )
		$sql .= ' and rlp_day in ('.implode( ', ', $day ).')';
	if( $time_in!==null )
		$sql .= ' and rlp_hour_start<='.$time_in.' and rlp_hour_stop>='.$time_in;

	return ria_mysql_query( $sql );
}

/** Cette fonction crée une période de fermeture pour un point-relais
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param $start Obligatoire, date de début de la période
 *	@param $stop Obligatoire, date de fin de la période
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_relay_vacations_add( $id, $start, $stop ){

	if( !dlv_relays_exists( $id ) ) return false;
	if( !isdate( $start ) ) return false;
	if( !isdate( $stop ) ) return false;

	$sql = '
		insert into dlv_relay_vacations (
			rlv_rly_id, rlv_date_start, rlv_date_stop
		) values (
			'.$id.', \''.dateparse( $start ).'\', \''.dateparse( $stop ).'\'
		)
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction supprime une ou plusieures période(s) de fermeture d'un point-relais
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param $start Facultatif, date de début de la période
 *	@param $stop Facultatif, date de fin de la période
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_relay_vacations_del( $id, $start=null, $stop=null ){

	if( !dlv_relays_exists( $id ) ) return false;
	if( $start!==null && !isdate( $start ) ) return false;
	if( $stop!==null && !isdate( $stop ) ) return false;

	$sql = '
		delete from
			dlv_relay_vacations
		where
			rlv_rly_id='.$id.'
	';

	if( $start!==null )
		$sql .= ' and rlv_date_start=\''.dateparse( $start ).'\'';

	if( $stop!==null )
		$sql .= ' and rlv_date_stop=\''.dateparse( $stop ).'\'';

	return ria_mysql_query( $sql );
}

/** Cette fonction récupére une ou des période(s) de fermeture d'un point-relais
 *	@param int $id Obligatoire, identifiant du point-relais
 *	@param string $date_in Facultatif, une date comprise dans la période de fermeture
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du point-relais
 *		- start : Date de début de la période
 *		- stop : Date de fin de la période
 *		- nb_days : Nombre de jours compris dans la période
 */
function dlv_relay_vacations_get( $id, $date_in=null ){

	if( !dlv_relays_exists( $id ) ) return false;
	if( $date_in!==null && !isdate( $date_in ) ) return false;

	if( $date_in!==null ) $date_in = dateparse( $date_in );

	$sql = '
		select
			rlv_rly_id as id,
			rlv_date_start as "start",
			rlv_date_stop as "stop",
			DATEDIFF(rlv_date_stop, rlv_date_start) as nb_days
		from
			dlv_relay_vacations
		where
			rlv_rly_id='.$id.'
	';

	if( $date_in!==null ){
		$sql .= '
			and rlv_date_start<=\''.dateparse( $date_in ).'\'
			and rlv_date_stop>=\''.dateparse( $date_in ).'\'
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les différents prestataire externes de points relais prient en charge.
 * 	@return Un résultat MySQL content :
 * 				- id : identifiant du prestataire externe
 * 				- name : nom du prestataire externe
 */
function dlv_relay_types_get(){
	return ria_mysql_query('
		select rlt_id as id, rlt_name as name
		from dlv_relay_types
	');
}

/** Cette fonction crée une association entre le locataire courant et un type de point-relais. Cette association peut ne concerner qu'un site du locataire
 *	@param int $rlt_id Obligatoire, identifiant du type de point-relais
 *	@param null|string $login Optionnel, login permettant d'utiliser l'API (null => ignoré, indissociable de $password)
 *	@param null|string $password Optionnel, mot de passe permettant d'utiliser l'API (null => ignoré, indissociable de $login)
 *	@return bool true en cas de succès (ou si l'association existait déjà), False sinon
 */
function dlv_relay_types_used_add( $rlt_id, $login=null, $password=null ){
	if( !dlv_relay_types_exists( $rlt_id ) ) return false;

	global $config;

	$sql_exists = 'select 1 from dlv_relay_types_used where rlw_rlt_id='.$rlt_id.' and rlw_tnt_id='.$config['tnt_id'];

	if( $rexists = ria_mysql_query( $sql_exists ) ){
		if( ria_mysql_num_rows( $rexists ) ){
			// Mise à jour du login et password si fourni
			if( $login !== null && $password !== null ){
				return ria_mysql_query('
					update dlv_relay_types_used
					set rlw_tnt_login = "'.addslashes($login). '",
							rlw_tnt_passwd = "'.addslashes($password).'"
					where rlw_tnt_id = '.$config['tnt_id'].'
						and rlw_rlt_id = '.$rlt_id.'
				');
			}

			return true;
		}
	}

	$sql = '
		insert into dlv_relay_types_used
			( rlw_rlt_id, rlw_tnt_id, rlw_tnt_login, rlw_tnt_passwd )
		values
			( '.$rlt_id.', '.$config['tnt_id'].', "'.( $login !== null ? addslashes($login) : '' ).'", "'.( $password !== null ? addslashes($password) : '' ).'" )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction supprime une ou des association(s) entre le locataire en cours et un type de point-relais (le nombre de sites ceoncernés peut varier)
 *	@param int $rlt_id Obligatoire, identifiant du type de point-relais
 *
 *	@return bool True en cas de succès, False sinon
 */
function dlv_relay_types_used_del( $rlt_id ){
	if( !is_numeric( $rlt_id ) || $rlt_id<=0 ) return false;

	global $config;

	return ria_mysql_query( 'delete from dlv_relay_types_used where rlw_rlt_id='.$rlt_id.' and rlw_tnt_id='.$config['tnt_id'] );
}

/**	Cette fonction récupère les informations spécifiques au type de point-relais pour le locataire courant
 *
 *	@param int $rlt_id Identifiant du type de point-relais
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- rlt_id : Identifiant du type de point-relais
 *		- rlt_name : Nom du type de point-relais
 *		- rlt_url_site : URL du site du type de point-relais
 *		- rlt_url_service : URL du webservice
 *		- tnt_id : Identifiant du locataire
 *		- login : Nom du compte pour l'accès au service
 *		- password : Mot de passe pour l'accès au service
 */
function dlv_relay_types_used_get( $rlt_id ){
	if( !is_numeric( $rlt_id ) || $rlt_id<=0 ) return false;

	global $config;

	$sql = '
		select
			rlw_rlt_id as rlt_id, rlw_tnt_id as tnt_id, rlt_name, rlt_url_site, rlt_url_service, rlw_tnt_login as "login", rlw_tnt_passwd as "password"
		from
			dlv_relay_types_used
			join dlv_relay_types on rlw_rlt_id=rlt_id
		where
			rlw_rlt_id='.$rlt_id.' and rlw_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}

/**	Accesseur sur le poids maximal autorisé par un point+relais
 *	@param int $id identifiant du point-relais
 *
 *	@return bool False en cas d'échec
 *	@return poids maximal autorisé
 */
function dlv_relays_get_weight_max( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;

	$r = ria_mysql_query( 'select rly_weight_max from dlv_relays where rly_date_deleted is null and rly_id='.$id );
	if( !$r || !ria_mysql_num_rows( $r ) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

/**	Cette fonction permet d'obtenir la liste des points relais So Collissimo, éventuellement filtrée en fonction des critères fournis en argument.
 *	@param string $zipcode Obligatoire, code postal de la commune
 *	@param string $city Obligatoire, nom de la commune
 *	@param string $country Facultatif, code pays
 *	@param $date Facultatif, date de livraison souhaitée
 *	@param int $limit Facultatif, nombre maximum de points relais à retourner. La valeur par défaut, -1, permet de retourner la liste complète.
 *	@return retourne la liste sous forme d'un document XML
 */
function dlv_relays_get_chronopost_list( $zipcode, $city, $country='FR', $date='', $limit=-1 ){
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	$country = trim( $country );

	if( $zipcode == '' || $city == '' || $country == '' ){
		return false;
	}

	if( trim($date) != '' ){
		if( !isdate($date) ){
			return false;
		}
	}else{
		$date = date('d/m/Y');
	}

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( CHRONOPOST_RELAY_TYPE );

	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ){
		return false;
	}
	$infos = ria_mysql_fetch_assoc( $rinfos );

	try{
		$client = new SoapClient( $infos['rlt_url_service'], array('trace' => 0, 'connection_timeout' => 10));

		$params = array(
	        'accountNumber' => $infos['login'],
	        'password' => $infos['password'],
	        'address' => '',
	        'zipCode' => $zipcode,
	        'city' => $city,
	        'countryCode' => $country,
	        'type' => 'P',
	        'productCode' => '1',
	        'service' => 'T',
	        'weight' => 2000,
	        'shippingDate' => date('d/m/Y'),
	        'maxPointChronopost' => (is_numeric($limit) && $limit > 0 ? $limit : 5),
	        'maxDistanceSearch' => 50,
	        'holidayTolerant' => 1
	    );

	    $results = $client->recherchePointChronopost($params);
	    $chronopost = $results->return;

		$dom_content = '';

		$count = 1;
		if( $chronopost->errorCode == '0' ){
			foreach( $chronopost->listePointRelais as $relay ){
				$name_country = sys_countries_get_name( $relay->codePays );

				$periods = array();
				foreach( $relay->listeHoraireOuverture as $one_day ){
					$periods[ ($one_day->jour - 1) ] = $one_day->horairesAsString;
				}

				if( is_numeric($limit) && $limit > 0 ){
					if( $count > $limit ){
						break;
					}
				}

				$dom_content .= '
					<listePointRetraitAcheminement>
						<identifiant>'.xmlentities( trim($relay->identifiant) ).'</identifiant>
						<nom>'.xmlentities( trim($relay->nom) ).'</nom>
						<adresse1>'.xmlentities( trim($relay->adresse1) ).'</adresse1>
						<adresse2>'.xmlentities( trim($relay->adresse2) ).'</adresse2>
						<adresse3 />
						<codePostal>'.xmlentities( $relay->codePostal ).'</codePostal>
						<localite>'.xmlentities( trim($relay->localite) ).'</localite>
						<pays>'.xmlentities( $name_country ).'</pays>
						<distanceEnMetre>'.xmlentities( $relay->distanceEnMetre ).'</distanceEnMetre>
						<poidsMaxi />
						<accesPersonneMobiliteReduite />
						<horairesOuvertureLundi>'.xmlentities( isset($periods[0]) ? $periods[0] : '' ).'</horairesOuvertureLundi>
						<horairesOuvertureMardi>'.xmlentities( isset($periods[1]) ? $periods[1] : '' ).'</horairesOuvertureMardi>
						<horairesOuvertureMercredi>'.xmlentities( isset($periods[2]) ? $periods[2] : '' ).'</horairesOuvertureMercredi>
						<horairesOuvertureJeudi>'.xmlentities( isset($periods[3]) ? $periods[3] : '' ).'</horairesOuvertureJeudi>
						<horairesOuvertureVendredi>'.xmlentities( isset($periods[4]) ? $periods[4] : '' ).'</horairesOuvertureVendredi>
						<horairesOuvertureSamedi>'.xmlentities( isset($periods[5]) ? $periods[5] : '' ).'</horairesOuvertureSamedi>
						<horairesOuvertureDimanche>'.xmlentities( isset($periods[6]) ? $periods[6] : '' ).'</horairesOuvertureDimanche>
					</listePointRetraitAcheminement>
				';

				$count++;
			}
		}

		$finalDom = new DomDocument();
		$finalDom->loadXML( '<list>'.$dom_content.'</list>' );
		return $finalDom->saveXML();
	}catch( Exception $exception ){
		error_log('[Exception catchée Chronopost] - '.$exception->getMessage());
	}

	// Chargement statique activé
	if( $load_static ){
		if( $country == 'FR' || trim($country) == '' ){
			return dlv_relays_build_xml_list( CHRONOPOST_RELAY_TYPE, $zipcode, $city, null, $weight_colis, $limit );
		}else{
			error_log('[Chronopost] Le chargement déconnecté de points-relais en dehors de la France ('.$country.') n\'est pas activé.');
		}
	}

	return false;
}

/**	Cette fonction, spécifique au type SO COLISSIMO, récupère, (insère) et renvoie un point-relais dont la référence interne est connue
 *
 *	@param string $rly_ref Référence du point-relais dans le système SO COLISSIMO
 *	@param string $date_livr Date de livraison estimée, au format dd/mm/yyyy. Ce paramètre est obligatoire et dépend des règles métiers du locataire courant
 *	@param $return_xml Facultatif, si True, le noeud XML contenant l'ensemble des informations sur le point-relais est retourné, et dans ce cas le point-relais n'est pas inséré (False par défaut)
 *
 *	@return int L'identifiant RiaShop du point-relais trouvé et inséré localement
 *	@return Le noeud XML contenant les informations du point-relais, si le paramètre $return_xml est activé
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_colissimo_point( $rly_ref, $date_livr, $return_xml = false ){

	// contrôles
	$rly_ref = trim( $rly_ref );
	if( $rly_ref=='' ) return false;
	if( !isdate($date_livr) ) return false;

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( SO_COLISSIMO_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );

	$rly_id = false;

	// construction de l'URL
	$base_uri = $infos['rlt_url_service'].'/findPointRetraitAcheminementByID?accountNumber='.urlencode($infos['login']).'&password='.urlencode($infos['password']).'&id='.urlencode($rly_ref).'&filterRelay=1&date='.urlencode($date_livr);

	// récupération du contenu
	$xml_content = '';
	if( $uri_reader = fopen($base_uri, 'rb') ){
		while( !feof($uri_reader) )
			$xml_content .= fread( $uri_reader, 8192 );
		fclose($uri_reader);
	}

	// lecture du xml
	if( $xml_content ){
		$dom = new DomDocument();
		$dom->loadXML( $xml_content );
		$node_list = $dom->getElementsByTagName( 'pointRetraitAcheminement' );
		if( $node_list->length > 0 ){
			$main_node = $node_list->item(0);
			if( $main_node->hasChildNodes() ){

				// retour XML
				if( $return_xml )
					return $main_node;

				$ref = $name = $address1 = $address2 = $zipcode = $city = '';
				$latitude = $longitude = $delay_max = $weight_max = null;
				$access_disabled = false;
				$open_periods = $vacation_periods = array();

				foreach( $main_node->childNodes as $child ){
					if( $child->hasChildNodes() ){
						switch( $child->localName ){
							case 'accesPersonneMobiliteReduite':
								$access_disabled = strtolower($child->firstChild->nodeValue)=='true';
								break;
							case 'adresse1':
								$address1 = $child->firstChild->nodeValue;
								break;
							case 'adresse2':
								if( $address2=='' ){
									$address2 = $child->firstChild->nodeValue;
								}else{
									$address2 = $child->firstChild->nodeValue.' '.$address2;
								}
								break;
							case 'adresse3':
								if( $address2=='' ){
									$address2 = $child->firstChild->nodeValue;
								}else{
									$address2 .= ' '.$child->firstChild->nodeValue;
								}
								break;
							case 'codePostal':
								$zipcode = $child->firstChild->nodeValue;
								break;
							case 'coordGeolocalisationLatitude':
								$val = $child->firstChild->nodeValue;
								$val = str_replace( '.', ',', $val );
								if( is_numeric( $val ) ){
									$latitude = $val;
								}
								break;
							case 'coordGeolocalisationLongitude':
								$val = $child->firstChild->nodeValue;
								$val = str_replace( '.', ',', $val );
								if( is_numeric( $val ) ){
									$longitude = $val;
								}
								break;
							case 'horairesOuvertureDimanche':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 7, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureJeudi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 4, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureLundi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 1, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureMardi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 2, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureMercredi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 3, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureSamedi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 6, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'horairesOuvertureVendredi':
								$v = trim( str_replace( '00:00-00:00', '', $child->firstChild->nodeValue ) );
								if( $v ){
									$vp = explode( ' ', $v );
									foreach( $vp as $p ){
										if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$p) ){
											$open_periods[] = array( 5, substr($p, 0, 2)*60 + substr($p, 3, 2),  substr($p, 6, 2)*60 + substr($p, 9, 2));
										}
									}
								}
								break;
							case 'identifiant':
								$ref = $child->firstChild->nodeValue;
								break;
							case 'listeConges':
								$d_start = array();
								$d_end = array();
								foreach( $child->childNodes as $ss_child ){
									if( $ss_child->hasChildNodes() ){

										if( $ss_child->localName=='calendarDeDebut' )
											$d_start[] = substr( $ss_child->firstChild->nodeValue, 0, 10 );
										elseif( $ss_child->localName=='calendarDeFin' )
											$d_end[] = substr( $ss_child->firstChild->nodeValue, 0, 10 );
									}
								}
								if( sizeof($d_start) && sizeof($d_end) ){
									$min = sizeof($d_start)<sizeof($d_end) ? sizeof($d_start) : sizeof($d_end);
									for( $i=0; $i<$min; $i++ )
										$vacation_periods[] = array( $d_start[$i], $d_end[$i] );
								}
								break;
							case 'localite':
								$city = $child->firstChild->nodeValue;
								break;
							case 'nom':
								$name = $child->firstChild->nodeValue;
								break;
							case 'poidsMaxi':
								$val = $child->firstChild->nodeValue;
								$val = str_replace( '.', ',', $val );
								if( is_numeric( $val ) && $val > 0 )
									$weight_max = $val;
								break;
						}
					}
				}

				$rly_id = dlv_relays_add( $ref, SO_COLISSIMO_TYPE, $name, $address1, $address2, $zipcode, $city, 'FR', '', $latitude, $longitude, $delay_max, null, $weight_max, null, null, null, $access_disabled, $open_periods, $vacation_periods );

			}
		}
	}

	return $rly_id;
}

/** Cette fonction, spécifique au type SO COLISSIMO, récupère une liste de de points-relais à proximité d'une adresse donnée
 *
 *	@param string $zipcode Obligatoire, code postal de recherche
 *	@param string $city Obligatoire, ville de recherche
 *	@param string $date_livr Obligatoire,  date de livraison estimée, au format dd/mm/yyyy. Ce paramètre est obligatoire et dépend des règles métiers du locataire courant
 *	@param $weight_colis Facultatif, poids total du colis en grammes
 *	@param int $limit Facultatif, nombre maximal de points-relais retournés
 *	@param $load_static Facultatif, détermine s'il y a chargement depuis la base en cas de non réponse du service (True par défaut)
 *	@param $type Facultatif, type de point relai, par défaut ils sont tous retournés, mettre 'BPR' pour les bureaux de poste ou mettre 'A2P' pour les points relais
 *	@param string $country Facultatif, code du pays
 *
 *	@return Un fichier XML contenant des noeuds "listePointRetraitAcheminement" pour chaque point.
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_colissimo_list( $zipcode, $city, $date_livr, $weight_colis=0, $limit=-1, $load_static=true, $type='all', $country='FR' ){

	// contrôles
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	if( $zipcode=='' && $city=='' ) return false;
	$weight_colis = str_replace( ',', '.', $weight_colis );
	if( !is_numeric($weight_colis) || $weight_colis<0 ) return false;
	if( !isdate($date_livr) ) return false;
	if( !is_numeric($limit) ) return false;

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( SO_COLISSIMO_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );

	// construction de l'URL
	$base_uri = $infos['rlt_url_service'].'2.0/findRDVPointRetraitAcheminement?accountNumber='.urlencode($infos['login']).'&password='.urlencode($infos['password']).'&shippingDate='.urlencode($date_livr);
	if( $zipcode )
		$base_uri .= '&zipCode='.urlencode( $zipcode );
	if( $city )
		$base_uri .= '&city='.urlencode( $city );
	if( $weight_colis )
		$base_uri .= '&weight='.$weight_colis;

	$base_uri .= '&countryCode='.$country;

	if( $country != 'FR' ){
		$base_uri .= '&optionInter=1';
	}

	// récupération du contenu
	$xml_content = '';
	if( $uri_reader = fopen($base_uri, 'rb') ){
		while( !feof($uri_reader) )
			$xml_content .= fread( $uri_reader, 8192 );
		fclose($uri_reader);
	}

	if( $xml_content ){

		// document XML final
		$finalDom = new DomDocument();
		$finalDom->loadXML( '<?xml version="1.0" encoding="utf-8"?><list />' );

		$dom = new DomDocument();
		$dom->loadXML( $xml_content );
		$node_list = $dom->getElementsByTagName('listePointRetraitAcheminement');
		$count = 0;
		foreach( $node_list as $node ){
			// limite atteinte
			if( $limit>=0 && $count>=$limit )
				break;
			// recherche de la référence
			$ref = '';
			foreach( $node->childNodes as $n ){

				if( $n->localName == 'typeDePoint' && $type != 'all' ){
					if( $type != $n->nodeValue ){
						continue(2);
					}
				}

				if( $n->localName=='identifiant' && $n->hasChildNodes() ){
					$ref = $n->firstChild->nodeValue;
				}
			}


			if( !$ref )
				continue;

			// pas de ban
			$new_n = $finalDom->importNode( $node, true );
			$finalDom->firstChild->appendChild( $new_n );
			$count++;
		}

		if( $count>0 )
			return $finalDom->saveXml();
	}

	// récupération déconnectée
	if( $load_static )
		return dlv_relays_build_xml_list( SO_COLISSIMO_TYPE, $zipcode, $city, $date_livr, $weight_colis, $limit, $country );

	return false;
}

/** Cette fonction, spécifique au type GLS, récupère une liste des points-relais à proximité d'une adresse donnée
 *
 *	@param string $zipcode Obligatoire, code postal de recherche
 *	@param string $country Obligatoire, code du pays, sur deux lettres, de recherche (par défaut : 'FR')
 *	@param int $limit Facultatif, nombre maximal de points-relais retournés (par défaut : 20)
 *	@param bool $load_static Facultatif, détermine s'il y a chargement depuis la base en cas de non réponse du service (True par défaut)
//  *	@param string $date_livr Obligatoire,  date de livraison estimée, au format dd/mm/yyyy. Ce paramètre est obligatoire et dépend des règles métiers du locataire courant
//  *	@param int $weight_colis Facultatif, poids total du colis en grammes
//  *	@param string $type Facultatif, type de point relais, par défaut ils sont tous retournés, mettre 'BPR' pour les bureaux de poste ou mettre 'A2P' pour les points relais
//  *	@param string $country Facultatif, code du pays
 *
 *	@return Un fichier XML contenant des noeuds "listePointRetraitAcheminement" pour chaque point.
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_gls_list( $zipcode, $country='FR', $limit=20, $load_static=true ){

	// infos Mondial / locataire
	$rinfos = dlv_relay_types_used_get( GLS_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ){
		return false;
	}

	$infos = ria_mysql_fetch_assoc( $rinfos );

	// Recherche les coordonnées géographique
	$geoloc = sys_google_maps_search( $zipcode.', '.$country );

	$url = 'https://api.gls-group.net/parcel-shop-management/v2/available-public-parcel-shops'
		.'?latitude='.$geoloc['lat']
		.'&longitude='.$geoloc['lng']
		.'&limit=20'
		.'&distance=50'
		.'&type[]=SHOPINSHOP&type[]=SHOP'
		.'&resultCountryCodes='.$country;

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER, array(
		'ApiKey: '.$infos['login'],
		'Accept: application/json',
	));
	$result = curl_exec($ch);
	curl_close($ch);

	$ar_relay = json_decode( $result, true );

	$dom_content = '';

	if( isset($ar_relay['data']) && is_array($ar_relay['data']) ){
		$count = 1;

		foreach( $ar_relay['data'] as $relay ){
			if( is_numeric($limit) && $limit > 0 ){
				if( $count > $limit ){
					break;
				}
			}

			if( is_numeric($limit) && $limit > 0 ){
				if( $count > $limit ){
					break;
				}
			}

			$open_days = [];
			if( isset($relay['openingDays']) && is_array($relay['openingDays']) ){
				foreach( $relay['openingDays'] as $one_openday ){
					$open_days[ $one_openday['weekday'] ] = [];

					foreach( $one_openday['hours'] as $one_hour ){
						$open_days[ $one_openday['weekday'] ][] = $one_hour['openingTime'].'-'.$one_hour['closingTime'];
					}
				}

			}

			$dom_content .= '
				<listePointRetraitAcheminement>
					<identifiant>'.xmlentities( trim($relay['parcelShopId']) ).'</identifiant>
					<nom>'.xmlentities( trim($relay['name']) ).'</nom>
					<adresse1>'.xmlentities( trim($relay['address']['street']) ).'</adresse1>
					<adresse2></adresse2>
					<adresse3 />
					<codePostal>'.xmlentities( $relay['address']['zipCode'] ).'</codePostal>
					<localite>'.xmlentities( trim($relay['address']['city']) ).'</localite>
					<pays>'.xmlentities( sys_countries_get_name( $relay['address']['countryCode'] ) ).'</pays>
					<distanceEnMetre>'.xmlentities( $relay['distance'] * 1000 ).'</distanceEnMetre>
					<poidsMaxi />
					<accesPersonneMobiliteReduite />
					<horairesOuvertureLundi>'.xmlentities( 		isset($open_days['MON']) ? implode(' | ', $open_days['MON']) : '' ).'</horairesOuvertureLundi>
					<horairesOuvertureMardi>'.xmlentities( 		isset($open_days['TUE']) ? implode(' | ', $open_days['TUE']) : '' ).'</horairesOuvertureMardi>
					<horairesOuvertureMercredi>'.xmlentities( isset($open_days['WED']) ? implode(' | ', $open_days['WED']) : '' ).'</horairesOuvertureMercredi>
					<horairesOuvertureJeudi>'.xmlentities( 		isset($open_days['THU']) ? implode(' | ', $open_days['THU']) : '' ).'</horairesOuvertureJeudi>
					<horairesOuvertureVendredi>'.xmlentities( isset($open_days['FRI']) ? implode(' | ', $open_days['FRI']) : '' ).'</horairesOuvertureVendredi>
					<horairesOuvertureSamedi>'.xmlentities( 	isset($open_days['SAT']) ? implode(' | ', $open_days['SAT']) : '' ).'</horairesOuvertureSamedi>
					<horairesOuvertureDimanche>'.xmlentities( isset($open_days['SUN']) ? implode(' | ', $open_days['SUN']) : '' ).'</horairesOuvertureDimanche>
				</listePointRetraitAcheminement>
			';

			$count++;
		}

		$finalDom = new DomDocument();
		$finalDom->loadXML( '<list>'.$dom_content.'</list>' );
		return $finalDom->saveXML();
	}

	// récupération déconnectée
	if( $load_static ){
		return dlv_relays_build_xml_list( GLS_RELAY_TYPE, $zipcode, '', null, 0, -1, $country );
	}

	return false;
}

/** Cette fonction, spécifique au type DPD, récupère une liste de de points-relais à proximité d'une adresse donnée
 *
 *	@param string $zipcode Obligatoire, code postal de recherche
 *	@param string $city Obligatoire, ville de recherche
 *	@param string $date_livr Obligatoire,  date de livraison estimée, au format dd/mm/yyyy. Ce paramètre est obligatoire et dépend des règles métiers du locataire courant
 *	@param float $weight_colis Facultatif, poids total du colis en grammes
 *	@param int $limit Facultatif, nombre maximal de points-relais retournés
 *	@param bool $load_static Facultatif, détermine s'il y a chargement depuis la base en cas de non réponse du service (True par défaut)
 *	@param string $country Facultatif, code du pays
 *	@param string $address Facultatif, adresse
 *	@param string $request_id Facultatif, identifiant permettant de suivre les appels au webservice (identifiant de commande par exemple)
 *  @param float $max_distance Facultatif, distance maximale du point relais
 *  @param string $category Facultatif, catégorie
 *  @param bool $holiday_tolerant Facultatif, filtrer les relais en congés partiels
 *	@param string $default_country Optionnel, pays des points relais (par défaut "FRANCE")
 *	@param int $timeout Optionnel, --- paramètre non prit en charge ---
 *	@return string Un fichier XML contenant des noeuds "listePointRetraitAcheminement" pour chaque point.
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_dpd_list(
	$zipcode,
	$city,
	$date_livr,
	$weight_colis=0,
	$limit=-1,
	$load_static=true,
	$country='FR',
	$address = false,
	$request_id = false,
	$max_distance = false,
	$category = false,
	$holiday_tolerant = null,
	$default_country = 'FRANCE',
	$timeout = 120
){
	// contrôles
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	if( $zipcode=='' && $city=='' ) return false;
	$weight_colis = str_replace( ',', '.', $weight_colis );
	if( !is_numeric($weight_colis) || $weight_colis<0 ) $weight_colis = null;
	if( !isdate($date_livr) ) return false;
	if( !is_numeric($limit) ) return false;
	if( !is_numeric($max_distance) ) $max_distance = false;
	if( !is_null($holiday_tolerant) ) $holiday_tolerant = $holiday_tolerant ? 1 : 0;

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( DPD_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );

	// Paramètre la requête à DPD
	DPDFrance::$date = $date_livr;
	if( $weight_colis ){
		DPDFrance::$weight = $weight_colis;
	}
	if ($country) {
		DPDFrance::$country = $country;
	}
	if( $request_id ){
		DPDFrance::$request_id = $request_id;
	}
	if( $limit && $limit>0 ){
		DPDFrance::$limit = $limit;
	}
	if ($max_distance && $max_distance > 0) {
		DPDFrance::$max_distance = $max_distance;
	}
	if ($category) {
		DPDFrance::$category = $category;
	}
	if (!is_null($holiday_tolerant)) {
		DPDFrance::$holiday_tolerant = $holiday_tolerant;
	}

	// Récupère la liste des points relais correspondants aux critères de recherche
	$relays_list = DPDFrance::getPickupPoints( $address, $zipcode, $city );

	// récupération du contenu
	if ( $relays_list===false || sizeof($relays_list)==0 ){
		if( $load_static )
			return dlv_relays_build_xml_list( DPD_RELAY_TYPE, $zipcode, $city, $date_livr, $weight_colis, $limit, $country );
	}

	$mapping = array(
		'ID' => 'identifiant',
		'NAME' => 'nom',
		'ADDRESS1' => 'adresse1',
		'ADDRESS2' => 'adresse2',
		'ADDRESS3' => 'adresse3',
		'ZIPCODE' => 'codePostal',
		'CITY' => 'localite',
		'DISTANCE' => 'distanceEnMetre',
		'LATITUDE' => 'latitude',
		'LONGITUDE' => 'longitude',
	);

	$days_mapping = array(
		'monday' => 'Lundi',
		'tuesday' => 'Mardi',
		'wednesday' => 'Mercredi',
		'thursday' => 'Jeudi',
		'friday' => 'Vendredi',
		'saturday' => 'Samedi',
		'sunday' => 'Dimanche'
	);

	if ( sizeof($relays_list) ){
		// document XML final
		$finalDom = new DomDocument();
		$finalDom->loadXML( '<?xml version="1.0" encoding="utf-8"?><list />' );

		$count = 0;

		foreach( $relays_list as $node ) {
			// recherche de la référence
			$new_n = $finalDom->createElement('listePointRetraitAcheminement');

			// Identifiant
			$ref = $node['relay_id'];

			if( !$ref ){
				continue;
			}

			// Mappe les différents champs vers le fichier XML de sortie
			foreach( $mapping as $src=>$dst ){

				switch( $src ){
					case 'ID':
						$value = $node['relay_id'];
					break;
					case 'NAME':
						$value = $node['shop_name'];
					break;
					case 'ADDRESS1':
						$value = $node['address1'];
					break;
					case 'ADDRESS2':
						$value = isset($node['address2']) ? $node['address2'] : '';
					break;
					case 'ADDRESS3':
						$value = isset($node['address3']) ? $node['address3'] : '';
					break;
					case 'ZIPCODE':
						$value = isset($node['zipcode']) ? $node['zipcode'] : '';
					break;
					case 'CITY':
						$value = isset($node['city']) ? $node['city'] : '';
					break;
					case 'DISTANCE':
						$value = isset($node['distance']) ? $node['distance'] : '';
					break;
					case 'LATITUDE':
						$value = isset($node['coord_lat']) ? $node['coord_lat'] : '';
					break;
					case 'LONGITUDE':
						$value = isset($node['coord_lng']) ? $node['coord_lng'] : '';
					break;
				}

				$field_node = $finalDom->createElement( $dst );
				$value_node = $finalDom->createTextNode( $value );

				$field_node->appendChild($value_node);
				$new_n->appendChild($field_node);

			}

			// Horaires d'ouverture
			foreach( $node['opening_hours'] as $day_id => $opening ){
				$str_opening = str_replace( '&', ' et ', $opening );
				$field_node = $finalDom->createElement('horairesOuverture' . $days_mapping[$day_id]);
				$value_node = $finalDom->createTextNode( $str_opening );
				$field_node->appendChild($value_node);
				$new_n->appendChild($field_node);
			}

			// Pays
			$field_node = $finalDom->createElement('pays');
			$value_node = $finalDom->createTextNode($default_country);

			$field_node->appendChild($value_node);
			$new_n->appendChild($field_node);

			// Poids maxi
			$field_node = $finalDom->createElement('poidsMaxi');
			$new_n->appendChild($field_node);

			// Accessibilité
			$field_node = $finalDom->createElement('accesPersonneMobiliteReduite');
			$new_n->appendChild($field_node);

			$finalDom->firstChild->appendChild( $new_n );
			$count++;
		}

		if( $count>0 )
			return $finalDom->saveXml();
	}

	// récupération déconnectée
	if ($load_static) {
		return dlv_relays_build_xml_list(DPD_RELAY_TYPE, $zipcode, $city, $date_livr, $weight_colis, $limit, $country);
	}

	return false;
}

/** Cette fonction, spécifique au points-relais de type Mondial Relay, récupère une liste de points-relais suivant des paramètres optionnels spécifiés
 *	@param string $zipcode Code postal de recherche
 *	@param string $city Ville de recherche
 *	@param float $weight_colis Optionnel, poids total du colis en grammes
 *	@param int $limit Optionnel, limite le nombre de points-relais retournés
 *	@param bool $load_static Optionnel, détermine si un chargement depuis notre base est effectué si le chargement à partir du service a échoué
 *	@param string $country Optionnel, par défaut la recherche de point relays est faite en france, mettre le code pays pour une recherche en dehors de la France
 *	@param array $more_params Optionnel, liste des paramètres supplémentaire pour l'appel SOAP
 *
 *	@return string Un document XML listant les points-relais trouvés
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_mondialrelay_list( $zipcode, $city, $weight_colis=0, $limit=-1, $load_static=true, $country='FR', array $more_params=array() ){

	// contrôles
	$zipcode = trim( $zipcode );
	$city = trim( $city );

	if( $zipcode=='' && $city=='' ){
		return false;
	}

	$weight_colis = str_replace( ',', '.', $weight_colis );
	if( !is_numeric($weight_colis) || $weight_colis<0 ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	$country = strtoupper2( $country );

	// infos Mondial / locataire
	$rinfos = dlv_relay_types_used_get( MONDIAL_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ){
		return false;
	}

	$infos = ria_mysql_fetch_array( $rinfos );

	// Préparation des arguments pour l'appel SOAP
	$params = array(
		'Enseigne' => $infos['login'],
		'Pays' => $country,
		'Ville' => $city,
		'CP' => $zipcode,
		'Taille' => '',
		'Poids' => $weight_colis==0 ? '' : $weight_colis,
		'Action' => '',
	);

	$params = array_merge($params, $more_params);

	$code = implode("", $params);
	$code .= $infos['password'];
	$params["Security"] = strtoupper(md5($code));
	// appel SOAP
	try{
		$soap_options = array(); // à configurer
		$soap_client = new SoapClient( $infos['rlt_url_service'], $soap_options );
		$soap_result = $soap_client->WSI4_PointRelais_Recherche( $params );

		if( !is_soap_fault( $soap_result ) ){
			$soap_result = $soap_result->WSI4_PointRelais_RechercheResult;
			if( (int)$soap_result->STAT == 0 ){

				$dom_content = '<?xml version="1.0" encoding="utf-8"?><list>';
				$count = 0;
				if( isset($soap_result->PointsRelais->PointRelais_Details) ){
					foreach( $soap_result->PointsRelais->PointRelais_Details as $obj_relay ){
						// limite atteinte
						if( $limit>=0 && $count>=$limit )
							break;

						if( !is_object($obj_relay) ){
							throw new Exception("La propriété PointRelais_Details n'est pas un énumérateur valide de points-relais.");
						}

						$periods = array();
						for( $i=0; $i<7; $i++ ){
							$a1 = $a2 = $b1 = $b2 = '0000';
							switch( $i ){
								case 0:
									$a1 = $obj_relay->Horaires_Lundi->string[0];
									$a2 = $obj_relay->Horaires_Lundi->string[1];
									$b1 = $obj_relay->Horaires_Lundi->string[2];
									$b2 = $obj_relay->Horaires_Lundi->string[3];
									break;
								case 1:
									$a1 = $obj_relay->Horaires_Mardi->string[0];
									$a2 = $obj_relay->Horaires_Mardi->string[1];
									$b1 = $obj_relay->Horaires_Mardi->string[2];
									$b2 = $obj_relay->Horaires_Mardi->string[3];
									break;
								case 2:
									$a1 = $obj_relay->Horaires_Mercredi->string[0];
									$a2 = $obj_relay->Horaires_Mercredi->string[1];
									$b1 = $obj_relay->Horaires_Mercredi->string[2];
									$b2 = $obj_relay->Horaires_Mercredi->string[3];
									break;
								case 3:
									$a1 = $obj_relay->Horaires_Jeudi->string[0];
									$a2 = $obj_relay->Horaires_Jeudi->string[1];
									$b1 = $obj_relay->Horaires_Jeudi->string[2];
									$b2 = $obj_relay->Horaires_Jeudi->string[3];
									break;
								case 4:
									$a1 = $obj_relay->Horaires_Vendredi->string[0];
									$a2 = $obj_relay->Horaires_Vendredi->string[1];
									$b1 = $obj_relay->Horaires_Vendredi->string[2];
									$b2 = $obj_relay->Horaires_Vendredi->string[3];
									break;
								case 5:
									$a1 = $obj_relay->Horaires_Samedi->string[0];
									$a2 = $obj_relay->Horaires_Samedi->string[1];
									$b1 = $obj_relay->Horaires_Samedi->string[2];
									$b2 = $obj_relay->Horaires_Samedi->string[3];
									break;
								case 6:
									$a1 = $obj_relay->Horaires_Dimanche->string[0];
									$a2 = $obj_relay->Horaires_Dimanche->string[1];
									$b1 = $obj_relay->Horaires_Dimanche->string[2];
									$b2 = $obj_relay->Horaires_Dimanche->string[3];
									break;
							}
							if( $a1!='0000' || $a2!='0000' ){
								$periods[] = substr($a1, 0, 2).':'.substr($a1, 2, 2).'-'.substr($a2, 0, 2).':'.substr($a2, 2, 2);
							}
							if( $b1!='0000' || $b2!='0000' ){
								if( !isset( $periods[$i] ) )
									$periods[] = substr($b1, 0, 2).':'.substr($b1, 2, 2).'-'.substr($b2, 0, 2).':'.substr($b2, 2, 2);
								else
									$periods[$i] .= ' '.substr($b1, 0, 2).':'.substr($b1, 2, 2).'-'.substr($b2, 0, 2).':'.substr($b2, 2, 2);
							}
						}

						$name_country = sys_countries_get_name( $obj_relay->Pays );
						$adr_relay = array();

						if (isset($obj_relay->LgAdr2)) {
							$adr_relay[] = $obj_relay->LgAdr2;
						}
						if( isset($obj_relay->LgAdr3)) {
							$adr_relay[] = $obj_relay->LgAdr3;
						}
						if( isset($obj_relay->LgAdr4)) {
							$adr_relay[] = $obj_relay->LgAdr4;
						}

						$adr_xml = '';
						foreach ($adr_relay as $key => $value) {
							$key = $key+1;
							$adr_xml .= '<adresse'.$key.'>'.xmlentities( trim($value) ).'</adresse'.$key.'>';
						}
						$adr_relay = array();
						if (isset($obj_relay->LgAdr2) && trim($obj_relay->LgAdr2) != '') {
							$adr_relay[] = $obj_relay->LgAdr2;
						}
						if( isset($obj_relay->LgAdr3) && trim($obj_relay->LgAdr3) != '') {
							$adr_relay[] = $obj_relay->LgAdr3;
						}
						if( isset($obj_relay->LgAdr4) && trim($obj_relay->LgAdr4) != '') {
							$adr_relay[] = $obj_relay->LgAdr4;
						}

						if( count($adr_relay) == 1 ){
							$adr_relay[] = '';
						}

						$adr_xml = '';
						foreach ($adr_relay as $key => $value) {
							$key = $key+1;
							$adr_xml .= '<adresse'.$key.'>'.xmlentities( trim($value) ).'</adresse'.$key.'>';
						}
						// Construction du noeud
						$dom_content .= '
							<listePointRetraitAcheminement>
								<identifiant>'.xmlentities( trim($obj_relay->Num) ).'</identifiant>
								<nom>'.xmlentities( trim($obj_relay->LgAdr1) ).'</nom>
								'.$adr_xml.'
								<codePostal>'.xmlentities( $obj_relay->CP ).'</codePostal>
								<localite>'.xmlentities( trim($obj_relay->Ville) ).'</localite>
								<pays>'.xmlentities( $name_country ).'</pays>
								<latitude>'.xmlentities( trim(str_replace(',', '.', $obj_relay->Latitude)) ).'</latitude>
								<longitude>'.xmlentities( trim(str_replace(',', '.', $obj_relay->Longitude)) ).'</longitude>
								<distanceEnMetre>'.xmlentities( trim($obj_relay->Distance) ).'</distanceEnMetre>
								<poidsMaxi />
								<accesPersonneMobiliteReduite />
								<horairesOuvertureLundi>'.xmlentities( isset($periods[0]) ? $periods[0] : '' ).'</horairesOuvertureLundi>
								<horairesOuvertureMardi>'.xmlentities( isset($periods[1]) ? $periods[1] : '' ).'</horairesOuvertureMardi>
								<horairesOuvertureMercredi>'.xmlentities( isset($periods[2]) ? $periods[2] : '' ).'</horairesOuvertureMercredi>
								<horairesOuvertureJeudi>'.xmlentities( isset($periods[3]) ? $periods[3] : '' ).'</horairesOuvertureJeudi>
								<horairesOuvertureVendredi>'.xmlentities( isset($periods[4]) ? $periods[4] : '' ).'</horairesOuvertureVendredi>
								<horairesOuvertureSamedi>'.xmlentities( isset($periods[5]) ? $periods[5] : '' ).'</horairesOuvertureSamedi>
								<horairesOuvertureDimanche>'.xmlentities( isset($periods[6]) ? $periods[6] : '' ).'</horairesOuvertureDimanche>
							</listePointRetraitAcheminement>
						';

						$count++;
					}
				}else{
					error_log( __FILE__.':'.__LINE__.' Aucun point-relais n\'a pu être chargé : la propriété SOAP "PointsRelais->PointRelais_Details" n\'est pas définie pour la méthode dlv_relays_get_mondialrelay_list() avec les arguments suivants : '.$zipcode.', '.$city.', '.$weight_colis.', '.$limit.', '.$load_static.', '.$country.'.' );
				}

				$dom_content .= '</list>';

				$finalDom = new DomDocument();
				$finalDom->loadXML( $dom_content );
				return $finalDom->saveXML();
			}
		}
	}catch( Exception $exception ){
		error_log('[Exception catchée MondialRelay] - '.$exception->getMessage());
	}

	// Chargement statique activé
	if( $load_static ){
		if( $country == 'FR' || trim($country) == '' ){
			return dlv_relays_build_xml_list( MONDIAL_RELAY_TYPE, $zipcode, $city, null, $weight_colis, $limit, $country );
		}
	}

	return false;
}

/**	Cette fonction, spécifique au type Mondial Relay, récupère, insère et renvoie un point-relais dont la référence interne est connue
 *	@param string $rly_ref Référence du point-relais dans le système Mondial Relay
 *	@param string $country Optionnel, par défaut la recherche de point relays est faite en france, mettre le code pays pour une recherche en dehors de la France
 *	@param bool $load_static Optionnel, détermine si une recherche déconnectée est tenté en cas d'échec avec le service SOAP
 *
 *	@return int L'identifiant RiaShop du point-relais trouvé et inséré localement
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_mondialrelay_point( $rly_ref, $country='FR', $load_static=true ){

	$rly_ref = trim( $rly_ref );
	if( $rly_ref=='' ) return false;

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( MONDIAL_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );

	$rly_id = false;

	// Préparation des arguments pour l'appel SOAP
	$params = array(
		'Enseigne' => $infos['login'],
		'Num' => $rly_ref,
		'Pays' => $country,
		'Security' => strtoupper( md5( $infos['login'].$rly_ref.$country.$infos['password'] ) )
	);

	$id = false;

	try{

	// appel SOAP
	$soap_options = array(); // à configurer
	$soap_client = new SoapClient( $infos['rlt_url_service'], $soap_options );
	$soap_result = $soap_client->WSI2_DetailPointRelais( $params );

	if( is_soap_fault( $soap_result ) )
		return $rly_id;

	$soap_result = $soap_result->WSI2_DetailPointRelaisResult;
	if( (int)$soap_result->STAT != 0 )
		return $rly_id;

	$open_periods = array();
	for( $i=1; $i<=7; $i++ ){
		$a1 = $a2 = $b1 = $b2 = '0000';
		switch( $i ){
			case 0:
				$a1 = $soap_result->Horaires_Lundi->string[0];
				$a2 = $soap_result->Horaires_Lundi->string[1];
				$b1 = $soap_result->Horaires_Lundi->string[2];
				$b2 = $soap_result->Horaires_Lundi->string[3];
				break;
			case 1:
				$a1 = $soap_result->Horaires_Mardi->string[0];
				$a2 = $soap_result->Horaires_Mardi->string[1];
				$b1 = $soap_result->Horaires_Mardi->string[2];
				$b2 = $soap_result->Horaires_Mardi->string[3];
				break;
			case 2:
				$a1 = $soap_result->Horaires_Mercredi->string[0];
				$a2 = $soap_result->Horaires_Mercredi->string[1];
				$b1 = $soap_result->Horaires_Mercredi->string[2];
				$b2 = $soap_result->Horaires_Mercredi->string[3];
				break;
			case 3:
				$a1 = $soap_result->Horaires_Jeudi->string[0];
				$a2 = $soap_result->Horaires_Jeudi->string[1];
				$b1 = $soap_result->Horaires_Jeudi->string[2];
				$b2 = $soap_result->Horaires_Jeudi->string[3];
				break;
			case 4:
				$a1 = $soap_result->Horaires_Vendredi->string[0];
				$a2 = $soap_result->Horaires_Vendredi->string[1];
				$b1 = $soap_result->Horaires_Vendredi->string[2];
				$b2 = $soap_result->Horaires_Vendredi->string[3];
				break;
			case 5:
				$a1 = $soap_result->Horaires_Samedi->string[0];
				$a2 = $soap_result->Horaires_Samedi->string[1];
				$b1 = $soap_result->Horaires_Samedi->string[2];
				$b2 = $soap_result->Horaires_Samedi->string[3];
				break;
			case 6:
				$a1 = $soap_result->Horaires_Dimanche->string[0];
				$a2 = $soap_result->Horaires_Dimanche->string[1];
				$b1 = $soap_result->Horaires_Dimanche->string[2];
				$b2 = $soap_result->Horaires_Dimanche->string[3];
				break;
		}
		if( $a1!='0000' || $a2!='0000' ){
			$open_periods[] = array( $i, (substr( $a1, 0, 2 ) * 60) + substr( $a1, 2, 2 ), (substr( $a2, 0, 2 ) * 60) + substr( $a2, 2, 2 ) );
		}
		if( $b1!='0000' || $b2!='0000' ){
			$open_periods[] = array( $i, (substr( $b1, 0, 2 ) * 60) + substr( $b1, 2, 2 ), (substr( $b2, 0, 2 ) * 60) + substr( $b2, 2, 2 ) );
		}
	}

	$id = dlv_relays_add( $soap_result->Num, MONDIAL_RELAY_TYPE, $soap_result->LgAdr1, $soap_result->LgAdr3, $soap_result->LgAdr4, $soap_result->CP, $soap_result->Ville, $soap_result->Pays, '', null, null, null, null, null, null, null, null, false, $open_periods );

	}catch( Exception $exception ){
		error_log('[Exception dlv_relays_get_mondialrelay_point] - '.$exception->getMessage());
	}

	if( !$id && $load_static ){
		if( $rrelay = dlv_relays_get( 0, $rly_ref, MONDIAL_RELAY_TYPE, '', array('cnt_code' => $country) ) ){
			if( ria_mysql_num_rows($rrelay) == 1 ){
				$id = ria_mysql_result($rrelay, 0, 'id');
			}elseif( ria_mysql_num_rows($rrelay) > 1 ){
				error_log('[dlv_relays_get_mondialrelay_point] Plusieurs points-relais déconnectés trouvés ('.$rly_ref.')');
			}
		}
	}

	return $id;
}

/** Cette fonction, spécifique au points-relais de type Tnt, récupère une liste de points-relais suivant des paramètres optionnels spécifiés
 *
 *	@param string $zipcode Code postal de recherche
 *	@param string $city Ville de recherche
 *	@param int $limit Optionnel, limite le nombre de points-relais retournés
 *	@param $load_static Optionnel, détermine si un chargement depuis notre base est effectué si le chargement à partir du service a échoué
 *
 *	@return Un document XML listant les points-relais trouvés
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_tntrelay_list( $zipcode, $city, $limit=-1, $load_static=true ){

	// contrôles
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	if( $zipcode=='' && $city=='' ) return false;
	if( !is_numeric($limit) ) return false;


	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( TNT_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );


	$xml = @simplexml_load_file($infos['rlt_url_service'].'?cp='.$zipcode.'&commune='.$city);
	if (!isset($xml->POINT_RELAIS)) {
		$xml = @simplexml_load_file('http://www.tnt.fr/public/b2c/relaisColis/recherche.do?code='.$zipcode);
	}

	if( $xml ){
		$dom_content = '<?xml version="1.0" encoding="utf-8"?><list>';

		foreach( $xml->POINT_RELAIS as $pts ){

			$lundi = $mardi = $mercredi = $jeudi = $vendredi = $samedi = $dimanche = array();
			if( trim($pts->LUNDI_AM) ) $lundi[] = $pts->LUNDI_AM;
			if( trim($pts->LUNDI_PM) ) $lundi[] = $pts->LUNDI_PM;
			if( trim($pts->MARDI_AM) ) $mardi[] = $pts->MARDI_AM;
			if( trim($pts->MARDI_PM) ) $mardi[] = $pts->MARDI_PM;
			if( trim($pts->MERCREDI_AM) ) $mercredi[] = $pts->MERCREDI_AM;
			if( trim($pts->MERCREDI_PM) ) $mercredi[] = $pts->MERCREDI_PM;
			if( trim($pts->JEUDI_AM) ) $jeudi[] = $pts->JEUDI_AM;
			if( trim($pts->JEUDI_PM) ) $jeudi[] = $pts->JEUDI_PM;
			if( trim($pts->VENDREDI_AM) ) $vendredi[] = $pts->VENDREDI_AM;
			if( trim($pts->VENDREDI_PM) ) $vendredi[] = $pts->VENDREDI_PM;
			if( trim($pts->SAMEDI_AM) ) $samedi[] = $pts->SAMEDI_AM;
			if( trim($pts->SAMEDI_PM) ) $samedi[] = $pts->SAMEDI_PM;
			if( trim($pts->DIMANCHE_AM) ) $dimanche[] = $pts->DIMANCHE_AM;
			if( trim($pts->DIMANCHE_PM) ) $dimanche[] = $pts->DIMANCHE_PM;

			// Construction du noed
			$dom_content .= '
				<listePointRetraitAcheminement>
					<identifiant>'.xmlentities( trim($pts->CODE_RELAIS) ).'</identifiant>
					<nom>'.xmlentities( trim($pts->NOM_RELAIS) ).'</nom>
					<adresse1>'.xmlentities( trim($pts->ADRESSE) ).'</adresse1>
					<adresse2 />
					<adresse3 />
					<codePostal>'.xmlentities( $pts->CODE_POSTAL ).'</codePostal>
					<localite>'.xmlentities( trim($pts->VILLE) ).'</localite>
					<pays>FRANCE</pays>
					<distanceEnMetre />
					<poidsMaxi />
					<accesPersonneMobiliteReduite />
					<horairesOuvertureLundi>'.xmlentities( implode(' | ',$lundi) ).'</horairesOuvertureLundi>
					<horairesOuvertureMardi>'.xmlentities( implode(' | ',$mardi) ).'</horairesOuvertureMardi>
					<horairesOuvertureMercredi>'.xmlentities( implode(' | ',$mercredi) ).'</horairesOuvertureMercredi>
					<horairesOuvertureJeudi>'.xmlentities( implode(' | ',$jeudi) ).'</horairesOuvertureJeudi>
					<horairesOuvertureVendredi>'.xmlentities( implode(' | ',$vendredi) ).'</horairesOuvertureVendredi>
					<horairesOuvertureSamedi>'.xmlentities( implode(' | ',$samedi) ).'</horairesOuvertureSamedi>
					<horairesOuvertureDimanche>'.xmlentities( implode(' | ',$dimanche) ).'</horairesOuvertureDimanche>
				</listePointRetraitAcheminement>
			';
		}

		$dom_content .= '</list>';

		return $dom_content;
	}

	// Chargement statique activé
	if( $load_static ){
		return dlv_relays_build_xml_list( TNT_RELAY_TYPE, $zipcode, $city, null, 0, $limit );
	}

	return false;
}

/**	Cette fonction, spécifique au type Tnt Relay, récupère, insère et renvoie un point-relais dont la référence interne est connue
 *	@param $rly_ref Référence du point-relais dans le système TNT
 *	@param $rly_zipcode Code postal du point relais
 *	@param $rly_commune Commune du point relais
 *	@param $load_static Optionnel, détermine si une recherche déconnectée est tenté en cas d'échec avec le service SOAP
 *
 *	@return int L'identifiant RiaShop du point-relais trouvé et inséré localement
 *	@return bool False en cas d'échec
 */
function dlv_relays_get_tntrelay_point( $rly_ref, $rly_zipcode, $rly_commune, $load_static=true ){

	$rly_ref = trim( $rly_ref );
	if( $rly_ref=='' ) return false;

	// infos so colissimo / locataire
	$rinfos = dlv_relay_types_used_get( TNT_RELAY_TYPE );
	if( !$rinfos || !ria_mysql_num_rows( $rinfos ) ) return false;
	$infos = ria_mysql_fetch_array( $rinfos );

	$rly_id = false;
	$id = false;

	$xml = @simplexml_load_file($infos['rlt_url_service'].'?cp='.$rly_zipcode.'&commune='.$rly_commune);

	if( $xml ){

		foreach( $xml->POINT_RELAIS as $pts ){
			if( $pts->CODE_RELAIS != $rly_ref ) continue;


			$array_jour = array(
					0 => array('LUNDI_AM','LUNDI_PM'),
					1 => array('MARDI_AM','MARDI_PM'),
					2 => array('MERCREDI_AM','MERCREDI_PM'),
					3 => array('JEUDI_AM','JEUDI_PM'),
					4 => array('VENDREDI_AM','VENDREDI_PM'),
					5 => array('SAMEDI_AM','SAMEDI_PM'),
					6 => array('DIMANCHE_AM','DIMANCHE_PM')
				);
			$open_periods = array();
			foreach( $array_jour as $k => $cols ){
				foreach( $cols as $col ){
					if( trim($pts->{$col}) && $pts->{$col} != 'Fermé' ){
						$open_periods[] = array( $k, substr($pts->{$col}, 0, 2)*60 + substr($pts->{$col}, 3, 2),  substr($pts->{$col}, 6, 2)*60 + substr($pts->{$col}, 9, 2));
					}
				}
			}

			$id = dlv_relays_add( $pts->CODE_RELAIS, TNT_RELAY_TYPE, $pts->NOM_RELAIS, $pts->ADRESSE, '', $pts->CODE_POSTAL, $pts->VILLE, 'FR', '', null, $pts->LATITUDE, $pts->LONGITUDE, null, null, null, null, null, false, $open_periods );
		}

	}

	if( !$id && $load_static ){
		if( $rrelay = dlv_relays_get( 0, $rly_ref, TNT_RELAY_TYPE, '' ) ){
			if( ria_mysql_num_rows($rrelay) == 1 ){
				$id = ria_mysql_result($rrelay, 0, 'id');
			}elseif( ria_mysql_num_rows($rrelay) > 1 ){
				error_log('[dlv_relays_get_tntrelay_point] Plusieurs points-relais déconnectés trouvés ('.$rly_ref.')');
			}
		}
	}

	return $id;
}

/**	Cette fonction récupère des points-relais dans notre base et les retourne sous la forme d'un document XML. Elle est notamment utile quand l'appel à un service Web échoue
 *	@param int $type Obligatoire, identifaint du type de point-relais
 *	@param string $zipcode Obligatoire, code postal du point-relais
 *	@param string $city Obligatoire, ville du point-relais (ce paramètre et $zipcode ne peuvent pas être vides tous les deux)
 *	@param string $date_livr Facultatif, date de livraison estimée du colis
 *	@param float $weight_colis Facultatif, poids du colis en grammes
 *	@param int $limit Facultatif, nombre maximal de résultats retournés
 *	@param string $country Facultarif, code ISO du pays
 *
 *	@return bool False en cas d'échec
 *	@return string Un document XML contenant une liste de points-relais. Attention, ceux-ci ne sont pas forcément à jour
 */
function dlv_relays_build_xml_list( $type, $zipcode, $city, $date_livr=null, $weight_colis=0, $limit=-1, $country='FR' ){

	// contrôles
	$zipcode = trim( $zipcode );
	$city = trim( $city );
	if( $zipcode=='' && $city=='' ) return false;
	$weight_colis = str_replace( ',', '.', $weight_colis );
	if( !is_numeric($weight_colis) || $weight_colis<0 ) return false;
	if( !is_numeric($limit) ) return false;

	$node_list = array();
	$counter = 0;
	$dom_content = '<?xml version="1.0" encoding="utf-8"?><list>';

	for( $cpt=0; $cpt<=2; $cpt++ ){
		if( $counter==$limit )
			break;

		// gestion de la recherche par priorité (code postal + ville / ville / code postal)
		$array_adr = array();
		if( $cpt==2 ){
			$array_adr['zipcode'] = $zipcode;
		}elseif( $cpt==1 ){
			$array_adr['city'] = $city;
		}else{
			$array_adr['zipcode'] = $zipcode;
			$array_adr['city'] = $city;
		}

		$array_adr['cnt_code'] = $country;

		if( $rrly = dlv_relays_get( 0, '', $type, '', $array_adr, array(), array(), $weight_colis ) ){
			while( $rly = ria_mysql_fetch_array( $rrly ) ){
				// limite atteinte
				if( $counter==$limit )
					break;

				// fermé à la livraison estimée
				if( isdate($date_livr) ){
					if( $rclose = dlv_relay_vacations_get( $rly['id'], $date_livr ) ){
						if( ria_mysql_num_rows( $rclose ) )
							continue;
					}
				}

				$node_list[] = $rly;

				$counter++;
			}
		}
	}

	// construction du noeud
	foreach( $node_list as $rly ){
		$distance = ria_array_get($rly, 'rayon', '');
		$node = '
			<listePointRetraitAcheminement>
				<identifiant>'.xmlentities( $rly['ref'] ).'</identifiant>
				<nom>'.xmlentities( $rly['name'] ).'</nom>
				<adresse1>'.xmlentities( $rly['address1'] ).'</adresse1>
				<adresse2>'.xmlentities( $rly['address2'] ).'</adresse2>
				<adresse3></adresse3>
				<codePostal>'.xmlentities( $rly['zipcode'] ).'</codePostal>
				<localite>'.xmlentities( $rly['city'] ).'</localite>
				<pays>'.xmlentities( $rly['country'] ).'</pays>
				<distanceEnMetre>'.str_replace(',', '.', $distance).'</distanceEnMetre>
				<longitude>'.str_replace( ',', '.', $rly['longitude'] ).'</longitude>
				<latitude>'.str_replace( ',', '.', $rly['latitude'] ).'</latitude>
				<poidsMaxi>'.xmlentities( $rly['weight_max'] ).'</poidsMaxi>
				<accesPersonneMobiliteReduite>'.( $rly['access_disabled'] ? 'true' : 'false' ).'</accesPersonneMobiliteReduite>
		';

		$array_periods = array( 'Lundi'=>'00:00-00:00', 'Mardi'=>'00:00-00:00', 'Mercredi'=>'00:00-00:00', 'Jeudi'=>'00:00-00:00', 'Vendredi'=>'00:00-00:00', 'Samedi'=>'00:00-00:00', 'Dimanche'=>'00:00-00:00' );
		if( $rperiods = dlv_relay_periods_get( $rly['id'] ) ){
			while( $period = ria_mysql_fetch_array($rperiods) ){
				if( $array_periods[$period['day_name']] == '00:00-00:00' )
					$array_periods[$period['day_name']] = dlv_relay_periods_convert( $period['start'], $period['stop'] );
				else
					$array_periods[$period['day_name']] .= ' '.dlv_relay_periods_convert( $period['start'], $period['stop'] );
			}
		}
		foreach( $array_periods as $d=>$ap )
			$node .= '<horairesOuverture'.$d.'>'.xmlentities( $ap ).'</horairesOuverture'.$d.'>';

		$node .= '</listePointRetraitAcheminement>';

		$dom_content .= $node;
	}

	$dom_content .= '</list>';

	$finalDom = new DomDocument();
	$finalDom->loadXML( $dom_content );
	return $finalDom->saveXml();
}

/** Cette fonction permet de rajouter au moteur RiaShop des points relais à partir d'un code XML.
 *	@param int $type Obligatoire, identifiant du type de point relais
 *	@param string $contentXML Obligatoire contenant XML
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function dlv_relays_add_by_xml( $type, $contentXML ){
	if( !dlv_relay_types_exists($type) ){
		return false;
	}

	if( trim($contentXML) == '' ){
		return false;
	}

	$xml = simplexml_load_string( $contentXML );

	if( isset($xml->listePointRetraitAcheminement) && sizeof($xml->listePointRetraitAcheminement) ){
		$ar_periods = array(
			'horairesOuvertureLundi', 'horairesOuvertureMardi', 'horairesOuvertureMercredi', 'horairesOuvertureJeudi', 'horairesOuvertureVendredi', 'horairesOuvertureSamedi', 'horairesOuvertureDimanche'
		);

		foreach( $xml->listePointRetraitAcheminement as $pts ){
			$open_periods = array();

			foreach( $ar_periods as $day=>$period ){
				$period = $pts->$period;

				foreach( explode(' ', $period) as $one_period ){
					if( preg_match("/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}-[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/",$one_period) ){
						$open_periods[] = array( ($day+1), substr($one_period, 0, 2)*60 + substr($one_period, 3, 2),  substr($one_period, 6, 2)*60 + substr($one_period, 9, 2));
					}
				}
			}

			$weight_max = is_numeric( $pts->poidsMaxi ) ? $pts->poidsMaxi : null;
			$latitude 	= is_numeric( $pts->coordGeolocalisationLatitude ) ? $pts->coordGeolocalisationLatitude : null;
			$longitude 	= is_numeric( $pts->coordGeolocalisationLongitude ) ? $pts->coordGeolocalisationLongitude : null;

			if( $latitude == null && is_numeric($pts->latitude)){
				$latitude = $pts->latitude;
			}
			if( $longitude == null && is_numeric($pts->longitude)){
				$latitude = $pts->longitude;
			}

			$country = isset($pts->codePays) ? $pts->codePays : sys_countries_get_code((string) $pts->pays, true);

			$rly_id = dlv_relays_add(
				$pts->identifiant, $type, $pts->nom, $pts->adresse1, $pts->adresse2, $pts->codePostal, $pts->localite, $country, '', $latitude, $longitude, null, null, $weight_max, null, null, null, false, $open_periods,
				null, true, $pts->typeDePoint
			);

			if( !$rly_id ){
				return false;
			}

		}
	}else{
		return false;
	}

	return true;
}

/** Cette fonction convertit une période donnée (minute de début, minute de fin) en une chaîne de type 00:00-00:00 (autocomplétion à gauche par 0). Elle n'a d'intérêt qu'au sein de la fonction dlv_relays_build_xml_list()
 *	@param int $start Minute de début (0 -> 1439)
 *	@param int $stop Minute de fin (0 -> 1439)
 *	@return string Une chaîne vide en cas d'échec, la chaîne formatée sinon
 */
function dlv_relay_periods_convert( $start, $stop ){
	if( !is_numeric($start) || $start<0 || $start>=1440 ) return '';
	if( !is_numeric($stop) || $stop<0 || $stop>=1440 ) return '';
	if( $stop<$start ) return '';

	$hour_deb = (int)( $start/60 );
	$minu_deb = $start % 60;
	$hour_fin = (int)( $stop/60 );
	$minu_fin = $stop % 60;

	return ( $hour_deb<10 ? '0' : '' ).$hour_deb.':'.( $minu_deb<10 ? '0' : '' ).$minu_deb.'-'.( $hour_fin<10 ? '0' : '' ).$hour_fin.':'.( $minu_fin<10 ? '0' : '' ).$minu_fin;
}

/// @}
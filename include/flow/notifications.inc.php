<?php

require_once('notifications.inc.php');
require_once('devices.inc.php');

// \cond onlyria
/**	\defgroup notif Notifications
 * 	\ingroup flow
 *	Gestion des notifications qui seront envoyées lors d'une nouvelle entrée dans le flux.
 *	@{
 */
	/** Cette fonction permet de récupérer la liste des notifications.
	 * 	@return array Tableau contenant les notifications
	 *
	 */
	function flow_notifications_get_codes(){
		$ar_notif = [
			'add-cart' 	=> [ 'group' => _('Commandes'), 'value' => 'add-cart', 	'label' => _('Création de panier') ],
			'add-devis' => [ 'group' => _('Commandes'), 'value' => 'add-devis', 	'label' => _('Opportunité (Nouveau devis)') ],
			'add-order' => [ 'group' => _('Commandes'), 'value' => 'add-order', 	'label' => _('Nouvelle commande') ],
			'add-usr' 	=> [ 'group' => _('Comptes'), 'value' => 'add-usr', 		'label' => _('Ajout d\'un compte') ],
			'upd-usr' 	=> [ 'group' => _('Comptes'), 'value' => 'upd-usr', 		'label' => _('Mise à jour d\'un compte') ],
			'del-usr' 	=> [ 'group' => _('Comptes'), 'value' => 'del-usr', 		'label' => _('Suppression d\'un compte') ],
		];

		$r_type = rp_types_get();
		if( $r_type ){
			while( $type = ria_mysql_fetch_assoc($r_type) ){
				$ar_notif[ 'rp-'.$type['id'] ] = [
					'group' => _('Rapport de visite'),
					'value' => 'rp-'.$type['id'],
					'label' => ucfirst( str_replace(_('Rapport de visite '), '', $type['name']) )
				];
			}
		}

		return $ar_notif;
	}

	/** Cette fonction permet de vérifier qu'un code de notification existe.
	 * 	@param string Obligatoire, code à vérifier
	 * 	@return bool true si le code existe, false dans le cas contraire
	 */
	function flow_notifications_code_exists( $code ){
		$ar_notif = flow_notifications_get_codes();
		return is_array($ar_notif) && array_key_exists( $code, $ar_notif) ? true : false;
	}

	/** Cette fonction permet d'envoyer une notification pour tout nouvelle entée dans le flux.
	 * 	@param int $cls_id Obligatoire, identifiant de la classe d'objet à laquelle est liée la notification
	 * 	@param array $data Obligatoire, informations liées à la notification
	 * 	@return bool true si l'envoi s'est correctement déroulé, false dans le cas contraire
	 */
	function flow_notifications_send( $cls_id, $data ){
		global $config;
		$http_host_ria = $config['backoffice_url'];
		if( !empty($config['bo_url']) ){
			$http_host_ria = $config['bo_url'];
		}
		$link_admin = 'https://'.$http_host_ria;
		if( $config['env_sandbox'] ){
			$link_admin = 'http://riashop-transition.olivier_girard.dev.fr';
		}

		$title = null;
		$subtitle = null;
		$description = null;
		$link = null;

		$author = $username = '';

		if( isset($data['linked']['author'][0]) && ria_array_key_exists(['adr_firstname', 'adr_lastname'], $data['linked']['author'][0]) ){
			$r_author = $data['linked']['author'][0];
			$author = trim( $r_author['adr_firstname'].' '.$r_author['adr_lastname'] );
		}

		if( isset($data['linked']['user'][0]) && ria_array_key_exists(['adr_firstname', 'adr_lastname', 'society', 'prf_id'], $data['linked']['user'][0]) ){
			$r_user = $data['linked']['user'][0];
			$username = trim( $r_user['adr_firstname'].' '.$r_user['adr_lastname'].' '.$r_user['society'] );
		}

		$code = '';

		switch( $cls_id ){
			case 4 : // Panier / Devis / Commande
				if( ria_array_key_exists(['ref', 'piece', 'id', 'state_id', 'total_ht', 'currency'], $data) ){
					// Le sous-titre est toujours le même quelque soit le statut
					$subtitle = 'N°'.ord_orders_name( $data['ref'], $data['piece'], $data['id'] );

					switch( $data['state_id'] ){
						case _STATE_BASKET : // Panier
							$code = 'add-cart';
							$title = _('Panier');

							$description = str_replace(
								'%amount%',
								'<strong>'.$data['total_ht'].' '.$data['currency'].'</strong>',
								_('Un panier d\'un montant de %amount% a été démarré.')
							);

							if( !is_numeric($data['total_ht']) || $data['total_ht'] <= 0 ){
								$description = _('Un panier a été démarré.');
							}
						break;
						case _STATE_DEVIS : // Oportunité / Devis
							$code = 'add-devis';
							$title = _('Opportunité');

							// Détermnine la description en fonction des informations sur le représentant et le client qui seront ou non manquantes.
							$t_desc = _('%seller% a enregistré un nouveau devis de %amount% pour %username%.');
							if( trim($author) == '' ){
								$t_desc = _('Nouveau devis de %amount% pour %username%.');

								if( trim($username) == '' ){
									$t_desc = _('Nouveau devis de %amount%.');
								}
							}elseif( trim($username) == '' ){
								$t_desc = _('%seller% a enregistré un nouveau devis de %amount%.');
							}

							$description = str_replace(
								[ '%seller%', '%amount%', '%username%' ],
								[ $author, '<strong>'.$data['total_ht'].' '.$data['currency'].'</strong>', $username ],
								$t_desc
							);
						break;
						case _STATE_WAIT_PAY :
						case _STATE_PAY_CONFIRM : // Commande
							$code = 'add-order';
							$title = _('Commande');

							// Détermine la description en fonction des informations sur le représentant qui seraient ou non manquantes
							$t_desc = _('Une commande d\'un montant de %amount% a été validée pour %username%.');
							if( trim($username) == '' ){
								$t_desc = _('Une commande d\'un montant de %amount% a été validée.');
							}

							$description = str_replace(
								[ '%amount%', '%username%' ],
								[ '<strong>'.$data['total_ht'].' '.$data['currency'].'</strong>', $username ],
								$t_desc
							);

						break;
					}

					$link = '/admin/orders/order.php?ord='.$data['id'];
				}
			break;
			case 127 : // Appel entrant / Appel sortant
				// à venir
			break;
			case 202 : // Notifications
				if( ria_array_key_exists(['nt_type_id', 'nt_title'], $data) ){
					$subtitle = $author;

					switch( $data['nt_type_id'] ){
						case NT_TYPE_USER_DELETE :
							$code = 'del-usr';
							$title = _('Suppression d\'un compte');
							$description = $data['nt_title'];
						break;
						case NT_TYPE_USER_ADD :
							$code = 'add-usr';
							$title = _('Ajout d\'un compte');
							$description = $data['nt_title'];
						break;
						case NT_TYPE_USER_UPD :
							$code = 'upd-usr';
							$title = _('Mise à jour d\'un compte');
							$description = $data['nt_title'];
						break;
					}

					if( in_array($data['nt_type_id'], [NT_TYPE_USER_DELETE, NT_TYPE_USER_ADD, NT_TYPE_USER_UPD]) ){
						if( isset($data['objects'][0]) && ria_array_key_exists(['cls_id', 'obj_id_0'], $data['objects'][0]) ){
							$link = '/admin/customers/edit.php?usr='.$data['objects'][0]['obj_id_0'];
						}
					}
				}
			break;
			case 87 : // Rapport de visite
				if( ria_array_key_exists(['type_id', 'id'], $data) ){
					$code = 'rp-'.$data['type_id'];
					$subtitle = 'N°'.$data['id'];

					$t_desc = _('%author% a saisi un rapport de visite pour %username%.');
					if( trim($author) == '' ){
						$t_desc = _('Nouveau rapport de visite pour %username%.');
						if( trim($username) == '' ){
							$t_desc = _('Nouveau rapport de visite.');
						}
					}elseif( trim($username) == '' ){
						$t_desc = _('%author% a saisi un rapport de visite.');
					}

					$description = str_replace(
						['%author%', '%username%'],
						[$author, $username],
						$t_desc
					);

					$title = rp_types_get_name( $data['type_id'] );
					if( trim($title) == '' ){
						$title = _('Rapport de visite');
					}

					$link = '/admin/fdv/reports/view.php?type='.$data['type_id'].'&rp='.$data['id'];
				}
			break;
		}

		if( trim($code) == '' ){
			return true;
		}

		$ar_users = flow_notifications_get_administrator( $code );
		if( !count($ar_users) ){
			return true;
		}

		// Si le contenu du mail est vide, on ira pas plus loin
		if( $title === null || $description === null ){
			return true;
		}

		// var_dump($title);
		// var_dump($subtitle);
		//var_dump($description);
		//var_dump($link);
		// print_r( $ar_users );
		// return true;

		$html = '<table width="480" border="0">'
			.'<tbody>'
				.'<tr>'
					.'<td align="center">'
						.'<img height="110" width="650" alt="RiaShop" src="https://start.yuto.fr/dist/images/RS_header.svg" />'
					.'</td>'
				.'</tr>'
				.'<tr>'
					.'<td style="font-size: 0.9em;">'
						.'<br><font face="Verdana,Arial,Helvetica,sans-serif">'
							.'<p>Cher client, chère cliente,</p>'
							.'<p>'
								.str_replace( '..', '.', $description.'.' )
								.' <a href="'.$link_admin.$link.'">Plus d\'infos</a>'
							.'</p>'
						.'</font>'
					.'</td>'
				.'</tr>'
			.'</tbody>'
		.'</table>'
		.'<div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(35,46,99)">'
			.'<div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;'
				.'<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a>'
			.'</div>'
		.'</div>';

		// print $html;return true;

		$email = new Email();
		$email->setFrom( 'RiaShop <<EMAIL>>' );
		$email->setSubject( '[Notification] '.$title );
		$email->addTo( '<EMAIL>' );
		foreach( $ar_users as $one_user ){
			$email->addBcc( $one_user );
		}
		$email->addHtml( $html );

		return $email->send();
	}

	/** Cette fonction permet de récupérer les clients étant inscript à une notification.
	 * 	@param string $code Obligatoire, code de la notification
	 * 	@return array Un tableau contenant l'adresse mail des administration souhaitant reçevoir cette notification
	 */
	function flow_notifications_get_administrator( $code ){
		global $config;

		if( trim($code) == '' ){
			return [];
		}

		$res = ria_mysql_query('
			select usr_email
			from cfg_overrides
				join gu_users on ( (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].') and usr_id = ovr_usr_id )
			where ovr_tnt_id = '.$config['tnt_id'].'
				and ovr_value like \'%"'.htmlspecialchars( $code ).'"%\'
				and ovr_var_code = \'admin_flow_notifications_send\'
				and usr_prf_id = '.PRF_ADMIN.'
				and usr_date_deleted is null
		');

		$ar_users = [];
		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				$ar_users[] = $r['usr_email'];
			}
		}

		return $ar_users;
	}

/// @}
// \endcond
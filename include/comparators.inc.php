<?php
/// \cond onlyria

require_once('categories.inc.php');
require_once('products.inc.php');
require_once('fields.inc.php');

require_once('comparators/ctr.amazon.inc.php');
require_once('comparators/ctr.cdiscount.inc.php');
require_once('comparators/ctr.ebay.inc.php');
require_once('comparators/ctr.priceminister.inc.php');
require_once('comparators/ctr.rueducommerce.inc.php');

/** \defgroup ctr_comparators Comparateurs de prix
 *	\ingroup marketplace
 *	Ce module comprend les fonctions nécessaires à la gestion des comparateurs de prix
 *	@deprecated Il n'est pas prévu de porter ce module vers la nouvelle interface d'administration.
 *	@{
 */

/** Cette fonction permet de retourner les comparateurs de prix contenu dans la base de données.
 *	@param int $id Optionnel, Identifiant du comparateur de prix
 *	@param bool $publish Optionnel, permet de retourner que les comparateurs pouvant être utilisé, par défaut à true, mettre à false pour tous les retourner
 *	@param bool $actived Optionnel, permet de récupérer que les comparateurs qui sont activés
 *	@param bool $marketplace Optionnel, filtre sur place de marché (true), comparateur (false) ou indifférent (null)
 *	@return bool false en cas d'échec
 *
 *	Le résultat est fourni sous la forme d'un résultat de requête mysql,
 *	comprenant les colonnes :
 *		- id : identifiant du comparateur de prix
 *		- name : nom du comparateur
 *		- site : url du site du comparateur de prix
 *		- register : url pour créer un espace marchand
 *		- seller : url pour se connecter à son espace marchand
 *		- marketplace : si c'est une place de marché
 *		- source : nom de la source
 *		- max_title : nombre de caractère maximal pour le titre
 *		- max_desc : nombre de caractère maximal pour la description
 *		- fields_use_hierarchy : détermine si l'on utilise les attributs de la hierarchie ou que de la catégorie où le produits est classé
 */
function ctr_comparators_get( $id=0, $publish=true, $actived=false, $marketplace=false ){
	if( $id>0 && !ctr_comparators_exists($id) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || $marketplace || ($id>0 && ctr_comparators_is_marketplace($id)) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select
			ctr.ctr_id as id, ctr_name as name, ctr_url as site, ctr_url_seller as seller, ctr_url_register as register, ctr_marketplace as marketplace, ctr_source as source,
			ctr_max_title as max_title, ctr_max_desc as max_desc, ctr_fields_use_hierarchy as fields_use_hierarchy
		from ctr_comparators as ctr
			left join ctr_comparator_tenants as cct on ( '.$config['tnt_id'].'=cct.ctr_tnt_id and ctr.ctr_id=cct.ctr_id and '.$wst_id.'=ctr_wst_id )
		where 1 '.( isset($config['ctr_tenant_actived']) && is_array($config['ctr_tenant_actived']) && sizeof($config['ctr_tenant_actived']) ? ' and ctr.ctr_id in ('.implode(', ', $config['ctr_tenant_actived']).')' : '' ).'
	';

	if( $actived ){
		$sql .= ' and ifnull(cct.ctr_tnt_id, 0)!=0';
	}

	if( $id>0 ){
		$sql .= ' and ctr.ctr_id='.$id;
	}

	if( $publish ){
		$sql .= ' and ctr_publish';
	}

	if( $marketplace !== null ){
		$sql .= ' and ctr_marketplace = ' . ($marketplace ? 1 : 0);
	}

	$sql .= ' order by if( ifnull(cct.ctr_actived, 0)=0, 0, 1 ) desc, ctr_marketplace desc, ctr_pos, ctr_name';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		error_log(  __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
	}

	return $res;
}

/**	Retourne les identifiants utilisateurs des places de marché (comptes clients)
 * 	@return array une liste d'identifiants sous forme de tableau
 */
function ctr_comparators_get_user_ids(){

	// Identifiants de comptes utilisateurs dont les commandes ne doivent pas être prises en compte (places de marché)
	$usr_exclusions = array();

	// Charge l'identifiant de compte Amazon
	if( $amazon = ctr_amazon_get_user() ){
		$usr_exclusions[] = $amazon;
	}
	// Charge l'identifiant de compte CDiscount
	if( $cdiscount = ctr_cdiscount_get_user() ){
		$usr_exclusions[] = $cdiscount;
	}
	// Charge l'identifiant de compte Ebay
	if( $ebay = ctr_ebay_get_user() ){
		$usr_exclusions[] = $ebay;
	}
	// Charge l'identifiant de compte Price Minister
	if( $pm = ctr_priceminister_get_user() ){
		$usr_exclusions[] = $pm;
	}
	// Charge l'identifiant de compte Rue du commerce
	if( $rdc = ctr_rueducommerce_get_user() ){
		$usr_exclusions[] = $rdc;
	}

	return $usr_exclusions;
}

/** Cette fonction permet de savoir si oui ou non un comparateur est une place de marché.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@return bool True s'il s'agit d'une place de marché, False dans le cas contraire
 */
function ctr_comparators_is_marketplace( $ctr ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;

	$sql = '
		select ctr_marketplace as marketplace
		from ctr_comparators
		where ctr_id='.$ctr.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'marketplace' );
}

/** Cette fonction permet de récupérer le terme utilisé pour identifiant un comparateur dans Google Analytics.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@return bool false en cas de problème, le terme utilisé dans le cas contraire
 */
function ctr_comparators_get_source( $ctr ){
	if( !ctr_comparators_exists($ctr) ) return false;

	$sql = '
		select ctr_source as source
		from ctr_comparators
		where ctr_id='.$ctr.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'source' );
}

/** Cette fonction permet de récupérer l'identifiant d'un comparateur par rapport à son nom de source utilisé dans l'export du catalogue
 *	@param string $source Obligatoire, source pour laquelle on souhaite récupérer l'identifiant
 *	@return bool false si une erreur s'est produite ou si aucun comparateur de corresponds à cette source, dans le cas contraire un résultat MySQL contenant :
 *					- id : identifiant du comparateur
 *					- name : nom du comparateur
 */
function ctr_comparators_get_bysource( $source ){
	if( !trim($source) ) return false;

	$sql = '
		select ctr_id as id, ctr_name as name
		from ctr_comparators
		where ctr_source=\''.addslashes($source).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return $res;
}

/** Cette fonction permet de récupérer le nom d'un comparateur de prix, à partir de son identifiant
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@return bool false en cas d'échec, sinon le nom du comparateur si celui-ci existe
 */
function ctr_comparators_get_name( $ctr ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;

	$sql = '
		select ctr_name as name
		from ctr_comparators
		where ctr_id='.$ctr.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}
/**
 * Cette fonction permet de récupérer l'information qui détermine si le comparateur utilise des attributs supplémentaire sur la hiérarchie ou que sur la catégorie où le produit est classé.
 *
 * @param int $ctr_id Identifiant du comparateur/place de marché
 * @return boolean Retourne true si les attributs ce trouve sur la hiérarchie, false si non
 */
function ctr_comparators_get_fields_use_hierarchy( $ctr_id )
{
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	$sql = '
		select ctr_fields_use_hierarchy as use_hierarchy
		from ctr_comparators
		where ctr_id='.$ctr_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);

	return $r['use_hierarchy']=='1' ? true : false;
}

/** Cette fonction permet de récupérer le nombre de caractère maximal pour le titre.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / d'une place de marché
 *	@return int Le nombre de caractères maximal pour le titre, 0 si aucune limite
 */
function ctr_comparators_get_max_title( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ctr_max_title
		from ctr_comparators
		where ctr_id='.$ctr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 'ctr_max_title' );
}

/** Cette fonction permet de récupérer le nombre de caractère maximal pour la description.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / d'une place de marché
 *	@return int Le nombre de caractères maximal pour la description, 0 si aucune limite
 */
function ctr_comparators_get_max_desc( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id ){
		return false;
	}

	$res = ria_mysql_query('
		select ctr_max_desc
		from ctr_comparators
		where ctr_id='.$ctr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 'ctr_max_desc' );
}

/** Cette fonction permet de récupérer les produits ajoutés automatiquement à une commande provenant des places de marché
 *	@param int $ctr_id Optionnel, identifiant d'une place de marché
 *	@param int $wst_id Optionnel, identifiant d'un site internet (la configuration de ce paramètre en dépend)
 *	@return array Un tableau contenant les références des produits
 */
function ctr_comparators_get_ord_import_add( $ctr_id=0, $wst_id=0 ){
	$ar_ref = array();

	if( !is_numeric($ctr_id) || $ctr_id < 0 ){
		return $ar_ref;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return $ar_ref;
	}

	global $config;

	$sql = '
		select cpf_fld_id as cpf_value
		from ctr_param_fields
		where cpf_tnt_id = '.$config['tnt_id'].'
			'.( $wst_id > 0 ? ' and cpf_wst_id = '.$wst_id : '' ).'
			'.( $ctr_id > 0 ? ' and cpf_ctr_id = '.$ctr_id : '' ).'
			and cpf_cpr_code = "ord_import_add_ref"
	';

	$res = ria_mysql_query( $sql );
	if( $res ){
		$r = ria_mysql_fetch_assoc( $res );
		$ar_ref = explode(',', str_replace(' ', '', $r['cpf_value']) );
	}

	return array_unique( $ar_ref );
}

/** Cette fonction permet de savoir si un comparateur est activé ou non.
 *	@param int $id Obligatoire, il s'agit de l'identifiant d'un comparateur de prix
 *	@return bool True si il est activé, False dans le cas contraire
 */
function ctr_comparators_actived( $id ){
	if( !ctr_comparators_exists($id) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($id) */ ){
		$wst_id = $config['wst_id'];
	}

	$res = ria_mysql_query( '
		select 1
		from ctr_comparator_tenants
		where ctr_tnt_id='.$config['tnt_id'].'
			'.( isset($config['ctr_tenant_actived']) && is_array($config['ctr_tenant_actived']) && sizeof($config['ctr_tenant_actived']) ? ' and ctr_id in ('.implode(', ', $config['ctr_tenant_actived']).')' : '' ).'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$id.'
			and ctr_actived=1
	' );

	if( $res && ria_mysql_num_rows($res) ){
		return true;
	}

	if( !$res ){
		error_log(  __FILE__.':'.__LINE__.' '.mysql_error() );
	}

	return false;
}

/** Cette fonction permet de savoir si oui ou non un comparateur dispose d'une ou plusieurs familles de produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@return bool True si il dispose de famille, False dans le cas contraire
 */
function ctr_comparators_have_categories( $ctr ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;

	$sql = '
		select 1
		from ctr_categories
		where cat_ctr_id='.$ctr.'
		limit 1
	';

	$res = ria_mysql_query( $sql );
	if( $res && ria_mysql_num_rows($res) ){
		return true;
	}

	if( !$res ){
		error_log(  __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
	}

	return false;
}

/** Cette fonction permet d'activer ou de désactiver un comparateur de prix.
 *	@param int $id Obligatoire, il s'agit de l'identifiant d'un comparateur de prix.
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ctr_comparators_activation( $id ){
	if( !ctr_comparators_exists($id) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($id) */ ){
		$wst_id = $config['wst_id'];
	}

	// S'il s'agit d'un partenaire CDiscount, on contrôle qu'il est activé sur CDiscount
	$partner_CDiscount = ctr_cdiscount_get_partners( $id );
	$is_partnerCDiscount = is_array($partner_CDiscount) && sizeof($partner_CDiscount);

	if( $is_partnerCDiscount ){
		$used = ctr_cdiscount_get_partners_used();

		if( !is_array($used) || !sizeof($used) || !in_array($partner_CDiscount['id'], array_keys( $used )) ){
			return "-1";
		}
	}

	$ctr = ria_mysql_fetch_array( ctr_comparators_get($id, true, false, false) );

	$actived = false;

	// On recherche une ligne dans ctr_comparator_tenants
	$ractive = ria_mysql_query('
		select ctr_actived
		from ctr_comparator_tenants
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$id.'
	');

	if( $ractive && ria_mysql_num_rows($ractive) ){
		$active = ria_mysql_result( $ractive, 0, 'ctr_actived' );

		$res = ria_mysql_query('
			update ctr_comparator_tenants
			set ctr_actived = '.( $active ? '0' : '1' ).'
			where ctr_tnt_id = '.$config['tnt_id'].'
				and ctr_wst_id='.$wst_id.'
				and ctr_id = '.$id.'
		');

		$actived = $res && !$active;
	}else{
		$actived = true;

		$res = ria_mysql_query('
			insert into ctr_comparator_tenants
				( ctr_tnt_id, ctr_wst_id, ctr_id, ctr_actived )
			values
				( '.$config['tnt_id'].', '.$wst_id.', '.$id.', 1 )
		');
	}

	if( !$res ){
		return false;
	}

	// S'il s'agit de l'activation d'un partenaire CDiscount utilisant le même catalogue, on réexporte tous les produits
	if( $actived && $is_partnerCDiscount && ctr_cdiscount_partners_use_catalog($id) ){
		return ctr_cdiscount_import_partner_catalog( $id );
	}

	return true;
}

/** Cette fonction permet de récupérer le lien vers le fichier d'export
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@return string Retourne le lien vers le fichier
 *	@return bool false en cas d'échec
 */
function ctr_comparators_file_get( $ctr ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$res = ria_mysql_query('
		select ctr_link_file
		from ctr_comparator_tenants
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$ctr.'
	');

	if( ria_mysql_num_rows($res) )
		return ria_mysql_result($res,0,0);
	else
		return false;
}

/** Cette fonction permet de mettre à jour le lien vers le fichier d'export
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@param string $file Obligatoire, chemin du fichier d'export
 *	@return bool true en cas de succès
 *	@return bool false dans le cas contraire
 */
function ctr_comparators_file_update( $ctr, $file ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !trim($file) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_comparators_actived($ctr) ){
		return false;
	}

	return ria_mysql_query('
		update ctr_comparator_tenants
		set ctr_link_file="'.$file.'"
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$ctr.'
	');
}

/** Cette fonction permet de contrôler qu'un comparateur de prix a déjà été activé une fois.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function ctr_comparator_tenants_exists( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select 1
		from ctr_comparator_tenants
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$ctr_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de vérifier qu'un locataire utilise un comparateur de prix ou une place de marché
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@return bool True si le comparateur de prix ou la place de marché est activé, False dans le cas contraire
 */
function ctr_comparator_tenants_used( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ctr_comparator_tenants
		where ctr_tnt_id = '.$config['tnt_id'].'
			and ctr_id = '.$ctr_id.'
			and ctr_actived = 1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour le budget mensuel maximum pour un comparateur de prix.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param int $budget Obligatoire, montant du budget, mettre 0 pour ne pas mettre de limite
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_comparator_tenants_update_budget( $ctr_id, $budget ){
	if( !ctr_comparators_exists($ctr_id) ){
		return false;
	}

	if( !is_numeric($budget) || $budget<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_comparator_tenants_exists($ctr_id) ){
		$res = ria_mysql_query('
			insert into ctr_comparator_tenants
				( ctr_tnt_id, ctr_wst_id, ctr_id, ctr_actived )
			values
				( '.$config['tnt_id'].', '.$wst_id.', '.$ctr_id.', 0 )
		');

		if( !$res ){
			return false;
		}
	}

	return ria_mysql_query('
		update ctr_comparator_tenants
		set ctr_budget='.( $budget>0 ? $budget : 'null' ).'
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$ctr_id.'
	');
}

/** Cette fonction permet de récupérer le budget mensuel maximum défini pour un comparateur de prix.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@return int Le budget mensuel maximum pour le comparateur
 */
function ctr_comparator_tenants_get_budget( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select ctr_budget as budget
		from ctr_comparator_tenants
		where ctr_tnt_id='.$config['tnt_id'].'
			and ctr_wst_id='.$wst_id.'
			and ctr_id='.$ctr_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['budget'];
}

/** Cette fonction permet de savoir si le budget définit pour un comparateur de prix est atteint.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@return bool True si le budget a été atteint, False dans le cas contraire
 */
function ctr_comparator_tenants_budget_is_reach( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$budget = ctr_comparator_tenants_get_budget( $ctr_id );
	if( !is_numeric($budget) || $budget<=0 ){
		return false;
	}

	// Récupère le coût total des clics pour le comparateur en question
	$date_start = date( 'Y-m-01' );
	$date_end = date('Y-m-').date('t');
	$stats = stats_comparators_get( $ctr_id, 0, 0, $date_start, $date_end );
	if( !isset($stats['cost']) || !is_numeric($stats['cost']) ){
		return false;
	}

	$reach = $stats['cost']>=$budget;
	if( $reach ){
		mail('<EMAIL>', 'Budget atteint pour : '.ctr_comparators_get_name($ctr_id), utf8_decode("Le budget de ".$budget." euros a été atteint, aucun produit n'a donc été export dans le flux récupérer par ce comparateur.\n\nLes produits présents sur ce comparateur devraient être retirés lors de la prochaine récupération du flux."));
	}

	return $reach;
}

/** Cette fonction permet d'enregistrer un log d'accès à un flux d'un comparateur de prix.
 *	@return bool True si l'enregistrement s'est correctement déroulée, False dans le cas contraire
 */
function ctr_comparator_logs_add(){
	$url = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
	if( trim($url)=='' ){
		return false;
	}

	// Si l'accès est fait depuis l'espace d'administration, on retourne True et on enregistre pas l'accès
	if( isset($_SERVER['HTTP_REFERER']) && strstr($_SERVER['HTTP_REFERER'], '/admin/') ){
		return true;
	}

	global $config;

	$url = str_replace( '/shopbots/', '', $url );
	$url = substr( $url, strpos($url, '/')+1 );

	// Lien entre la fichier et le comparateur
	$ar_source = array(
		1  => 'leguide.xml',
		3  => 'kelkoo.xml',
		4  => 'google_shopping.xml',
		5  => 'shopping.csv',
		6  => 'shopzilla.txt',
		10 => 'twenga.xml',
		12 => 'cherchons.xml',
		13 => 'nextag.xml'
	);

	$res = true;
	$ctr_id = array_search( $url, $ar_source );
	if( is_numeric($ctr_id) && $ctr_id>0 ){
		// Récupère les informations complémentaire : user_agent, IP, HOST
		$agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
		$ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
		$host = @gethostbyaddr( $ip );

		$host = trim($host)=='' ? $host = 'null' : '\''.addslashes( $host ).'\'';
		if( trim($agent)=='' || trim($ip)=='' ){
			return false;
		}

		$res = ria_mysql_query('
			insert into ctr_comparator_logs
				( ccl_tnt_id, ccl_wst_id, ccl_ctr_id, ccl_datetime, ccl_user_agent, ccl_ip, ccl_host )
			values
				( '.$config['tnt_id'].', '.$config['wst_id'].', '.$ctr_id.', now(), \''.$agent.'\', \''.$ip.'\', '.$host.')
		');
	}

	return $res;
}

/** Cette fonction permet de récupérer les logs d'accès aux flux des comparateurs de prix
 *
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur de prix
 *	@param string $date_start Optionnel, date de début
 *	@param string $date_end Optionnel, date de fin
 *	@param int $limit Optionnel, permet de limiter le nombre d'accès retourné
 *
 *	@return resource Retourne un résultat MySQL contenant :
 *				- ctr_id : identifiant du comparateur de prix
 *				- name : nom du comparateur de prix
 *				- date : date d'accès au fichier
 *				- date_en : date d'accès au fichier au format EN
 *				- user_agent : user agent accédant au fichier
 *				- ip : ip utilisée lors de l'accès au fichier
 *				- hors : host par lequel l'accès au fichier est passé
 */
function ctr_comparator_logs_get( $ctr_id=0, $date_start=false, $date_end=false, $limit=0 ){
	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $date_start!==false ){
		if( !isdate($date_start) ){
			return false;
		}
	}

	if( $date_end!==false ){
		if( !isdate($date_end) ){
			return false;
		}
	}

	if( !is_numeric($limit) || $limit<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ($ctr_id>0 && ctr_comparators_is_marketplace($ctr_id)) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select
			ccl_ctr_id as ctr_id, ctr_name as name, ccl_datetime as date_en, ccl_user_agent as user_agent, ccl_ip as ip, ccl_host as host,
			date_format(ccl_datetime,"%d/%m/%Y à %H:%i") as date
		from ctr_comparator_logs
			join ctr_comparators on (ccl_ctr_id=ctr_id)
		where ccl_tnt_id='.$config['tnt_id'].'
			and ccl_wst_id='.$wst_id.'
	';

	if( $ctr_id>0 ){
		$sql .= ' and ccl_ctr_id='.$ctr_id;
	}

	if( isdate($date_start) ){
		$sql .= ' and date(ccl_datetime)>=\''.$date_start.'\'';
	}

	if( isdate($date_end) ){
		$sql .= ' and date(ccl_datetime)<=\''.$date_end.'\'';
	}

	$sql .= '
		order by ccl_datetime desc
	';

	if( $limit>0 ){
		$sql .= ' limit 0'. $limit;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer la date du dernier accès au flux d'un comparateur de prix au format JJ/MM/AAAA à 00:00.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@return string La date du dernier accès, False si le paramètre est omis ou faux
 */
function ctr_comparator_logs_get_last_access( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select date_format( max(ccl_datetime),"%d/%m/%Y à %H:%i" ) as date
		from ctr_comparator_logs
		where ccl_tnt_id='.$config['tnt_id'].'
			and ccl_wst_id='.$wst_id.'
			and ccl_ctr_id='.$ctr_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'date' );
}

/** Cette fonction permet de tester l'existance d'un comparateur de prix dans la base de données,
 *	@param int $id Obligatoire, Identifiant à tester.
 *	@param bool $publish Optionnel, par défaut le comparateur / la place de marché doit être publié(e), mettre false pour ne pas en tenir compte
 *	@return bool true si il existe bien dans la base de données
 *	@return bool false dans le cas contraire.
 */
function ctr_comparators_exists( $id, $publish=true ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select ctr_id
		from ctr_comparators
		where ctr_id='.$id.'
			'.( $publish ? 'and ctr_publish!=0' : '' ).'
		limit 0,1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de savoir si un comparateur utilise un système d'enchère.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param bool $min Optionnel, par défaut on ne tient pas compte du système avec enchère minimal
 *	@return bool true si le comparateur utilise un système d'enchère, false dans le cas contraire
 */
function ctr_comparators_auctions_used( $ctr, $min=null ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;

	$sql = '
		select 1
		from ctr_comparators
		where ctr_id='.$ctr.'
			and ctr_auctions=1
	';

	if( $min!==null )
		$sql .= ' and ctr_min_auctions='.( $min ? '1' : '0' );

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/// @}

/** \defgroup ctr_catalogs Catalogue des produits et des rattachements des catégories de produits pour un comparateur
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des catalogues de produits à
 *	publier sur les comparateurs de prix.
 *	@{
 */

/** Cette fonction permet de vérifier si une catégorie produit est bien présente dans un export
 *	@param int $ctr Obligatoire, identifiant d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie produit
 *	@return bool false si la catégorie n'est pas présente, true dans le cas contraire
 */
function ctr_prd_categories_exists( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select 1
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/**	Renvoie si le comparateurs a des catégories
 *	@param int $ctr_id Identifiant du comparateur
 *	@return	bool True si le comparateur a des catégories, false dans le cas contraire
 */
function ctr_has_categories( $ctr_id ){
	if( !is_numeric($ctr_id) )
		return false;
	$rctr = ria_mysql_query('select 1 from ctr_categories where cat_ctr_id = ' . $ctr_id . ' limit 1');
	if( !$rctr )
		return false;
	return ria_mysql_num_rows($rctr);
}

/** Cette fonction permet de rattacher une catégorie produits à une famille d'un ctr/mkp
 *	Si la catégorie possède des enfants, ils seront automatiquement rattachés à la même catégorie.
 * 	S'ils étaient déjà rattachés, le lien est conservé.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat_prd Obligatoire, identifiant d'une catégorie produits
 *	@param int $cat_ctr Facultatif, identifiant d'une famille d'un comparateur de prix
 *	@param int $amount Optionnel, permet de personnalisée l'enchère sur l'export de la catégorie
 *	@param bool $not_actived_prds Optionnel, par défaut l'export des produits contenus dans cette catégorie seront activé, mettre true pour que ce ne soit pas le cas
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ctr_prd_categories_add( $ctr, $cat_prd, $cat_ctr=0, $amount=0, $not_actived_prds=false ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !prd_categories_exists($cat_prd) ){
		return false;
	}

	if( $cat_ctr>0 && !ctr_categories_exists($cat_ctr) ){
		return false;
	}

	if( !is_numeric($amount) || $amount<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		replace into ctr_prd_categories
			( cpc_tnt_id, cpc_wst_id, cpc_ctr_id, cpc_prd_cat, cpc_cat_id, cpc_amount )
		values
			( '.$config['tnt_id'].', '.$wst_id.', '.$ctr.', '.$cat_prd.', '.$cat_ctr.', '.( $amount ? $amount : 'null' ).' )
	';

	if( !ria_mysql_query($sql) )
		return false;


	if( $cat_ctr > 0 ){

		// Récupère les catégories enfant
		$childs = prd_categories_childs_get_array( $cat_prd, true );

		if( sizeof($childs)>0 ){ // Rattache la même catégorie produit aux catégories enfant
			foreach( $childs as $child ){
				if( !ctr_prd_categories_is_publish($ctr, $child, false) ){
					$cat = ria_mysql_fetch_array(prd_categories_get($child));
					if(	$cat['products']>0 ){
						ctr_prd_categories_del( $ctr, $child, false, $not_actived_prds );
						$res = ria_mysql_query('
							replace into ctr_prd_categories
								( cpc_tnt_id, cpc_wst_id, cpc_ctr_id, cpc_cat_id, cpc_prd_cat )
							values
								( '.$config['tnt_id'].', '.$wst_id.', '.$ctr.', '.$cat_ctr.', '.$child.' )
						');

						if( !$res ){
							return false;
						}
					}
				}
			}
		}

	}

	if( !$not_actived_prds ){
		$r_prd = prd_products_get_simple(0, '', true, $cat_prd, true, false, false, false, array( 'childs' => true ));
		while( $prd = ria_mysql_fetch_array($r_prd) ){
			if( !ctr_catalogs_exists($ctr, $prd['id']) ){
				if( !ria_mysql_query('insert into ctr_catalogs( ctl_tnt_id, ctl_wst_id, ctl_ctr_id, ctl_prd_id, ctl_active ) values ( '.$config['tnt_id'].','.$wst_id.','.$ctr.','.$prd['id'].', 1 )') )
					return false;

			}elseif( !ctr_catalogs_is_publish($ctr, $prd['id']) ){
				if( !ria_mysql_query('update ctr_catalogs set ctl_active=1 where ctl_tnt_id='.$config['tnt_id'].' and ctl_wst_id='.$wst_id.' and ctl_ctr_id='.$ctr.' and ctl_prd_id='.$prd['id']) )
					return false;
			}
		}
	}

	return true;
}

/** Cette fonction permet de mettre à jour l'export d'une catégorie dans un comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat_prd Obligatoire, identifiant d'une catégorie produit
 *	@param int $cat_ctr Optionnel, identifiant d'une famille du comparateur
 *	@param int $amount Optionnel, permet de personnalisée l'enchère sur l'export de la catégorie
 */
function ctr_prd_categories_update( $ctr, $cat_prd, $cat_ctr=0, $amount=0 ){
	return ctr_prd_categories_add( $ctr, $cat_prd, $cat_ctr, $amount, true );
}

/** Cette fonction permet de supprimer un rattachement catégorie produits à une famille de comparateur de prix.
 *	@param int $ctr Identifiant du comparateur de prix
 *	@param int $cat_prd Identifiant de la catégorie produits
 *	@param array $childs Facultatif, tableau d'identifiants enfants
 *	@param bool $not_actived_prds Optionnel, par défaut l'export des produits contenus dans cette catégorie seront activé, mettre true pour que ce ne soit pas le cas
 *	@return bool true en cas de succès
 *	@return bool false dans le cas contraire
 */
function ctr_prd_categories_del( $ctr, $cat_prd, $childs=false, $not_actived_prds=false ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_numeric($cat_prd) || $cat_prd <= 0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$childs = $childs ? prd_categories_childs_get_array( $cat_prd, true ) : false;

	// supprime les produits du catalogue du comparateur
	if( !$not_actived_prds ){
		$ar_prd = array();

		$rp = prd_products_get_simple(0, '', true, $cat_prd, is_array($childs) && sizeof($childs) ? true : false, false, false, false, array( 'childs' => true ));
		if( !$rp ){
			return false;
		}

		while( $p = ria_mysql_fetch_array($rp) ){

			// On vérifie que le produit n'est pas dans une autre catégorie exporté vers le comparateur de prix
			$sql = ria_mysql_query('
				select cly_cat_id
				from prd_classify
					join ctr_prd_categories on cly_cat_id = cpc_prd_cat and cly_tnt_id = cpc_tnt_id
				where cly_tnt_id = '.$config['tnt_id'].'
					and cpc_wst_id='.$wst_id.'
					and	cpc_ctr_id = '.$ctr.'
					and cly_prd_id = '.$p['id'] .'
					and cpc_prd_cat  in (
						select hry.cat_parent_id
						from prd_cat_hierarchy as hry, prd_categories
						where hry.cat_tnt_id='.$config['tnt_id'].'
							and hry.cat_tnt_id=prd_categories.cat_tnt_id
							and hry.cat_parent_id=cat_id
							and cat_child_id='.$cat_prd.'
					)
			');

			if( !$sql || !ria_mysql_num_rows($sql) || ($sql && ria_mysql_num_rows($sql) <= 1) ){
				$ar_prd[] = $p['id'];
			}
		}
	}

	// supprime le rattachement des catégories
	$res = ria_mysql_query('
		delete from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat in ('.$cat_prd.( is_array($childs) && sizeof($childs) ? ', '.implode(',', $childs) : '' ).')
	');

	if( !$res ){
		return false;
	}

	if( !$not_actived_prds ){
		if( !sizeof($ar_prd) ){
			return true;
		}

		$res = ria_mysql_query('
			update ctr_catalogs
			set ctl_active=0
			where ctl_tnt_id='.$config['tnt_id'].'
				and ctl_wst_id='.$wst_id.'
				and ctl_ctr_id='.$ctr.'
				and ctl_prd_id in ('.implode(', ', $ar_prd).')
		');
	}

	return $res;
}

/** Cette fonction permet de récupérer toutes les catégories ayant été activées pour un comparateur.
 * 	@param int $ctr_id Obligatoire, identifinat d'un comparateur
 * 	@param int $wst_id Obligatoire, identifiant d'un site
 * 	@return array Un tableau contenant les identifiants de catégories produits, vide si aucune catégorie n'est activée pour ce comparateur
 */
function ctr_prd_categories_get_array( $ctr_id, $wst_id ){
	global $config;

	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return [];
	}

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return [];
	}

	$res = ria_mysql_query('
		select distinct cpc_prd_cat as cat
		from ctr_prd_categories
		where cpc_tnt_id = '.$config['tnt_id'].'
			and cpc_wst_id = '.$wst_id.'
			and cpc_ctr_id = '.$ctr_id.'
	');

	$ar_ids = [];
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_ids[] = $r['cat'];
		}
	}

	return $ar_ids;
}

/** Cette fonction permet de connaître la catégories comparateur de prix dans laquelle la catégories produit
 *	est exportée.
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@param int $cat Optionnel, identifiant de la catégorie produits
 *	@return array Retourne un tableau MySql contenant :
 *			- id : identifiant de la catégorie comparateur de prix
 *			- name : nom de la catégorie
 *			- title : titre de la catégorie
 *			- cat : identifiant de la catégorie produit
 *			- is_disabled : permet de savoir si une famille d'un comparateur est désactivée ou non
 *			- amount : montant personnalisée d'une enchère
 *	@return bool false en cas d'échec
 */
function ctr_prd_categories_get( $ctr, $cat=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select cat_id as id, cat_name as name, ifnull(cat_title, cat_name) as title, cpc_prd_cat as cat, if(cat_date_disabled is null, 0, 1) as is_disabled,
		cpc_amount as amount
		from ctr_categories, ctr_prd_categories
		where cat_id = cpc_cat_id
			and cat_ctr_id=cpc_ctr_id
			and cpc_tnt_id = '.$config['tnt_id'].'
			and cpc_wst_id = '.$wst_id.'
			and cpc_ctr_id = '.$ctr.'
	';

	if( $cat>0 )
		$sql .= ' and cpc_prd_cat = '.$cat.' limit 0, 1';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer un montant d'enchère pour une catégorie produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une catégorie produit
 *	@param bool $direct Optionnel, si oui ou non on retourne le montant directement sur la catégorie produit
 *	@return bool false si l'information n'existe pas, sinon le montant de l'enchère
 */
function ctr_prd_categories_get_auctions( $ctr, $cat, $direct=true ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select cpc_amount as amount
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat='.$cat.'
			and cpc_amount is not null
	';

	$res = ria_mysql_query( $sql );
	$amount = false;

	if( (!$res || !ria_mysql_num_rows($res)) && !$direct ) {

		$family = ctr_prd_categories_get_family( $ctr, $cat );
		if( $family ){
			$amount = ctr_categories_get_min_auctions( $ctr, $family );
		}

	} elseif( $res && ria_mysql_num_rows($res) ){
		$amount = ria_mysql_result( $res, 0, 'amount' );
	}

	return $amount;
}

/** Cette fonction permet de récupérer l'identifiant de la famille vers laquelle est exportée une catégorie produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une catégorie produit
 *	@return bool false si l'un des paramètres est faux ou omis, l'identifiant de la famille dans l'autre cas
 */
function ctr_prd_categories_get_family( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select cpc_cat_id as cat
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'cat' );
}

/** Cette fonction permet de récupérer la configuration d'une catégorie produit.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une catégorie produit
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *	@return bool false si l'un des paramètres est faux ou omis, l'identifiant de la famille dans l'autre cas
 */
function ctr_prd_categories_get_params( $ctr_id, $cat_id, $wst_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id <= 0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	global $config;
	$sql = '
		select cpc_params as params
		from ctr_prd_categories
		where cpc_tnt_id = '.$config['tnt_id'].'
			and cpc_ctr_id = '.$ctr_id.'
			and cpc_prd_cat = '.$cat_id.'
	';

	if( $wst_id > 0 ){
		$sql .=  'and cpc_wst_id = '.$wst_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['params'];
}

/** Cette fonction permet de savoir si une catégorie produits est exportée
 *	pour un comparateur de prix donné.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant de la catégorie produits
 *	@param bool $have_cat Optionnel, permet de savoir si une catégorie est exportée en étant rattachée, par défault on ne cherche pas à savoir
 *	@return bool True si elle est exportée, False dans le cas contraire
 */
function ctr_prd_categories_is_publish( $ctr, $cat, $have_cat=false ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !prd_categories_exists($cat) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select cpc_cat_id
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat='.$cat.'
			and cpc_cat_id is not null
	';

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}

/** Cette fonction permet de connaître le nombre de catégories de produits exportées
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $ctr_cat Optionnel, permet de filtrer selon une catégorie du comparateur
 *	@return int Retourne le nombre de catégories de produits exportées vers le comparateur
 *	@return bool false en cas d'échec
 */
function ctr_prd_categories_count( $ctr, $ctr_cat=0 ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( $ctr_cat>0 && !ctr_categories_exists($ctr_cat) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select cpc_cat_id
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
	';

	if( $ctr_cat )
		$sql .= ' and cpc_cat_id='.$ctr_cat;

	return ria_mysql_num_rows( ria_mysql_query($sql) );
}

/** Cette fonction permet de ratacher un ou plusieurs produits à une famille d'un comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant (ou tableau d'identifiant) d'un produits
 *	@param int $cat Optionnel, identifiant d'une famille de comparateur de prix
 *	@param string $title Optionnel, titre ou tableau de titre de surcharge
 *	@param string $desc Optionnel, description ou tableau de description de surcharge
 *	@param bool $active Optionnel, active ou non l'export
 *	@param int $amount Optionnel, montant de l'enchère
 *
 *	@return bool true si le ou les produits ont bien été insérés
 *	@return bool false dans le cas contraire.
 */
function ctr_catalogs_add( $ctr, $prd, $cat=0, $title=false, $desc=false, $active=true, $amount=0 ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_numeric($prd) && !is_array($prd) ){
		return false;
	}

	if( $cat>0 && !ctr_categories_exists($cat, $ctr) ){
		return false;
	}

	global $config;

	$cat = $cat>0 ? $cat : 'null';
	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( is_numeric($prd) )
		$prd = array( $prd );

	if( $title!=false && !is_array($title) )
		$title = array( $title );

	if( $desc!=false && !is_array($desc) )
		$desc = array( $desc );

	$count = 0;
	foreach($prd as $p){

		if( !is_numeric($p) || !prd_products_exists($p) )
			return false;

		$prd_title = isset($title[$count]) ? $title[$count] : '';
		$prd_desc = isset($desc[$count]) ? $desc[$count] : '';

		// conserve la surcharge sur le titre et la desription
		$res = ria_mysql_query('
			select ifnull(ctl_prd_title, \'\') as title, ifnull(ctl_prd_desc, \'\') as "desc" , ctl_active as active
			from ctr_catalogs
			where ctl_tnt_id='.$config['tnt_id'].'
				and ctl_wst_id='.$wst_id.'
				and ctl_ctr_id='.$ctr.'
				and ctl_prd_id='.$p.'
			limit 0,1
		');

		$prd_active = false;
		if( $res && ria_mysql_num_rows($res) ){
			if( !ctr_catalogs_del($ctr, $p) )
				return false;

			$r = ria_mysql_fetch_array( $res);
			$prd_title = $r['title'];
			$prd_desc = $r['desc'];
			$prd_active = $r['active'];
		}

		if( $active===null ){
			$active = $prd_active;
		}

		$res = ria_mysql_query(
			'insert into ctr_catalogs
				( ctl_tnt_id, ctl_wst_id, ctl_ctr_id, ctl_prd_id, ctl_cat_id, ctl_prd_title, ctl_prd_desc, ctl_active, ctl_amount )
			values
				( '.$config['tnt_id'].', '.$wst_id.', '.$ctr.', '.$p.', '.$cat.', '.( trim($prd_title) ? '\''.addslashes($prd_title).'\'' : 'null' ).', '.( trim($prd_desc) ? '\''.addslashes($prd_desc).'\'' : 'null' ).', '.( $active ? 1 : 0 ).', '.( $amount ? $amount : 'null' ).' )'
		);

		if( !$res )
			return false;

		$count++;
	}

	return true;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images d'un produit sur un comparateur / places de marché.
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param string $fieldname Obligatoire, nom du champ de type "file" qui contient l'image à télécharger
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur / d'une place de marché
 *	@return int L'identifiant RiaShop attribué à l'image.
 */
function ctr_catalogs_images_upload( $fieldname, $prd_id, $ctr_id ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	return ctr_catalogs_images_add( $_FILES[$fieldname]['tmp_name'], $prd_id, $ctr_id, $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à modèle ou un comparateur / une place de marché. Cette fonction est similaire à ctr_images_upload,
 *	a l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile lors d'importations.
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *
 *	@param string $filename Obligatoire, nom du fichier image.
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur / d'une place de marché
 *	@param string $srcname Facultatif, nom de l'image source
 *	@return int L'identifiant attribué à l'image.
 */
function ctr_catalogs_images_add( $filename, $prd_id, $ctr_id, $srcname='' ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	if( $id = img_images_add( $filename, $srcname ) ){
		ctr_catalogs_images_add_existing( $id, $prd_id, $ctr_id, null );
		img_images_count_update($id);
	}
	return $id;
}

/** Cette fonction permet d'ajouter une image spécifique aux comparateurs / places de marché pour un produit
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param int $img_id Obligatoire, identifiant d'une image
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur / d'une place de marché
 *	@param int $pos Facultatif, position de l'image. Lorsque plusieurs images sont envoyées, permet d'imposer leur ordre d'apparition.
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function ctr_catalogs_images_add_existing( $img_id, $prd_id, $ctr_id, $pos=null ){
	if( !is_numeric($img_id) && $img_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	global $config;

	if( !is_numeric($pos) || $pos<0 ){
		$pos = ria_mysql_result(
			ria_mysql_query('
				select count(*)
				from ctr_catalogs_images
				where img_tnt_id='.$config['tnt_id'].'
					and img_prd_id='.$prd_id.'
					and img_ctr_id='.$ctr_id
			), 0, 0
		);
	}

	$res = ria_mysql_query('
		replace into ctr_catalogs_images
			( img_tnt_id, img_id, img_prd_id, img_ctr_id, img_pos )
		values
			( '.$config['tnt_id'].', '.$img_id.', '.$prd_id.', '.$ctr_id.', '.$pos.' )
	');

	if( !$res ){
		return false;
	}

	return img_images_count_update( $img_id );
}

/** Cette fonction récupère les images associées à un comparateur / une place de marché pour un produit.
 *	@param int $prd_id Optionnel, identifiant d'un produit
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@param int $img_id Facultatif, identifiant d'une image
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de l'image
 *				- prd_id : identifiant du produit
 *				- ctr_id : identifiant du comparateur / de la place de marché
 *				- pos : position de l'image
*/
function ctr_catalogs_images_get( $prd_id=0, $ctr_id=0, $img_id=0 ){
	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( !is_numeric($img_id) || $img_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id, img_pos as pos, img_prd_id as prd_id, img_ctr_id as ctr_id
		from ctr_catalogs_images
		where img_tnt_id='.$config['tnt_id'].'
	';

	if( $prd_id ){
		$sql .= ' and img_prd_id='.$prd_id;
	}

	if( $ctr_id ){
		$sql .= ' and img_ctr_id='.$ctr_id;
	}

	if( $img_id ){
		$sql .= ' and img_id='.$img_id;
	}

	$sql .= '
		order by img_pos asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un lien entre une image et un produit pour un comparateur / une place de marché
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@param int $img_id Optionnel, identifiant d'une image
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_catalogs_images_del( $prd_id, $ctr_id=0, $img_id=0 ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) || $img_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	global $config;

	$imgs = ctr_catalogs_images_get( $prd_id, $ctr_id, $img_id );
	while( $r = ria_mysql_fetch_array($imgs) ){

		$r_pos = ria_mysql_query('select img_pos from ctr_catalogs_images where img_tnt_id='.$config['tnt_id'].' and img_prd_id='.$r['prd_id'].' and img_ctr_id='.$r['ctr_id'].' and img_id='.$r['id']);
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos,0,0);
			ria_mysql_query('update ctr_catalogs_images set img_pos=img_pos-1 where img_tnt_id='.$config['tnt_id'].' and img_prd_id='.$r['prd_id'].' and img_ctr_id='.$r['ctr_id'].' and img_pos>'.$pos);
			ria_mysql_query('delete from ctr_catalogs_images where img_tnt_id='.$config['tnt_id'].' and img_prd_id='.$r['prd_id'].' and img_ctr_id='.$r['ctr_id'].' and img_id='.$r['id']);
		}
		img_images_count_update($r['id']);
	}

	return true;
}

/** Cette fonction permet de connaitre la catégorie d'un produit. Il existe trois niveaux :
 *			- 1° : on prend la famille du comparateur à laquelle le produit est rattaché
 *			- 2° : le produit lui-même n'est pas rattaché à une famille, on prend la famille du comparateur de sa catégorie
 *			- 3° : la catégorie produit et le produit ne sont pas rattaché à une famille, on prend la catégorie produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *
 *	@return array Retourne un tableau contenant
 *			- statut, ctr = famille de comparateur, prd = lié directement à la famille, cat = catégories produit
 *			- id, identifiant de la catégorie
 *			- name, nom de la catégories
 *			- title : titre de la catégorie
 *	@return bool false si il y a un conflic sur le rattachement d'une famille de comparateur de prix
 */
function ctr_cat_product_get( $ctr, $prd ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !prd_products_exists($prd) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$result = array();

	// Récupère les informations sur le produit nécessaire
	$p = ria_mysql_fetch_array(prd_products_get($prd));
	$r_cat = prd_products_categories_get($prd, true);

	$f_prd = ria_mysql_fetch_array(ctr_catalogs_get($ctr, $prd));
	if( $f_prd['cat_id'] ){ // Première solution

		$cat = ria_mysql_fetch_array(ctr_categories_get($ctr, $f_prd['cat_id'], '', 0, false, '', true));

		$result['statut'] = 'prd';
		$result['id'] = $cat['id'];
		$result['name'] = $cat['name'];
		$result['title'] = $cat['title'];

		return $result;

	} elseif( ria_mysql_num_rows($r_cat)>1 ){ // Deuxième solution

		$count = 0;
		$publish = 0;
		// Compte le nombre de catégories rattaché à une famille de comparateur
		while( $c = ria_mysql_fetch_array($r_cat) ){
			if( ctr_prd_categories_is_publish($ctr,$c['cat'],true) ){
				$publish = $c['cat'];
				$count++;
			}
		}

		if( $count==1 ){ // Deuxième solution

			$cat = ctr_prd_categories_get($ctr,$publish);
			$result['statut'] = 'ctr';
			$result['id'] = $cat['id'];
			$result['name'] = $cat['name'];
			$result['title'] = $cat['title'];

		} else if( $count==0 ){ //  Troisième solution
			$categories = ria_mysql_fetch_array(prd_products_categories_get($prd, true));
			$cat = ria_mysql_fetch_array(prd_categories_get($categories['cat']));
			$result['statut'] = 'cat';
			$result['id'] = $cat['id'];
			$result['name'] = $cat['name'];
			$result['title'] = $cat['title'];

		} else { // Plusieurs catégories produits sont rattachées, conflit
			return false;
		}

		return $result;
	}

	$categories = ria_mysql_fetch_array($r_cat);
	if( ctr_prd_categories_is_publish($ctr,$categories['cat']) ){ // Deuxième solution

		$cat = ctr_prd_categories_get($ctr,$categories['cat']);
		$result['statut'] = 'ctr';
		$result['id'] = $cat['id'];
		$result['name'] = $cat['name'];

	} else { // Troisième solution

		$cat = ria_mysql_fetch_array(prd_categories_get($categories['cat']));
		$result['statut'] = 'cat';
		$result['id'] = $cat['id'];
		$result['name'] = $cat['name'];
		$result['title'] = $cat['title'];

	}

	return $result;
}

/** Cette fonction permet de vérifier qu'une ligne existe pour l'export d'un produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool false si la ligne n'existe pas, true dans le cas contraire
 */
function ctr_catalogs_exists( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select 1
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de retourner un seul ou tous les produits contenu dans un catalogue.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix sur lequel filtrer le résultat
 *	@param int $prd Optionnel, identifiant d'un produit sur lequel filtrer le résultat
 *	@param int $cat Optionnel, identifiant d'une catégorie de produit, si ce paramètre est donné, le paramètre $prd est ignoré
 *	@param bool $active Optionnel, si true, seul les comparateurs actifs pour ce locataire seront retournés. Si false, tous les comparateurs seront retournés.
 *
 *	@return array Retourne un tableau MySQL contenant les produits à exporter :
 *			- prd_id : identifiant produit
 *			- cat_id : identifiant de la famille de rattachement
 *			- prd_title : titre de surcharge pour le produit
 *			- prd_desc : description de surcharge pour le produit
 *			- amount : montant de l'enchère personnalisé du produit
 *			- price_ht : prix ht utilisé lors de l'export du produit
 *			- qte : quantité du produit lors de son exportation
 *			- tva_rate : taux de tva du produits lors de son exportation
 *			- params : paramètre optionnel pour l'export du produit
 *			- is_active : si oui ou non le produit est activé
 *	@return bool false si aucun produits n'est spécifié.
 */
function ctr_catalogs_get( $ctr, $prd=0, $cat=0, $active=false ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( $cat<=0 ){
		$sql ='
			select ctl_prd_id as prd_id, ctl_cat_id as cat_id, ifnull(ctl_prd_title, \'\') as prd_title, ifnull(ctl_prd_desc, \'\') as prd_desc, ctl_amount as amount,
			ctl_price_ht as price_ht, ctl_qte as qte, ctl_tva_rate as tva_rate, ctl_params as params, ctl_active as is_active
			from ctr_catalogs
			where ctl_tnt_id='.$config['tnt_id'].'
				and ctl_wst_id='.$wst_id.'
				and ctl_ctr_id='.$ctr.'
		';

		if( $active )
			$sql .= ' and ctl_active';

		if( $prd>0 )
			$sql .= ' and ctl_prd_id='.$prd.' limit 0, 1';
	} else {
		$sql = '
			select ctl_prd_id as prd_id, ctl_cat_id as cat_id, ifnull(ctl_prd_title, \'\') as prd_title, ifnull(ctl_prd_desc, \'\') as prd_desc
			from (
				select ctl_prd_id, ctl_cat_id, ctl_prd_title, ctl_prd_desc
				from ctr_catalogs
					join prd_classify on ( cly_tnt_id=ctl_tnt_id and cly_prd_id=ctl_prd_id )
					join prd_cat_hierarchy on ( cat_tnt_id=cly_tnt_id and cat_child_id=cly_cat_id )
				where ctl_tnt_id='.$config['tnt_id'].'
					and ctl_wst_id='.$wst_id.'
					and ctl_ctr_id='.$ctr.' '.( $active ? ' and ctl_active' : '' ).'
					and cat_parent_id='.$cat.'

				union all

				select ctl_prd_id, ctl_cat_id, ctl_prd_title, ctl_prd_desc
				from ctr_catalogs
					join prd_classify on ( cly_tnt_id=ctl_tnt_id and cly_prd_id=ctl_prd_id )
				where ctl_tnt_id='.$config['tnt_id'].'
					and ctl_wst_id='.$wst_id.'
					and ctl_ctr_id='.$ctr.' '.( $active ? ' and ctl_active' : '' ).'
					and cly_cat_id='.$cat.'
			) as result
			group by ctl_prd_id
		';
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer le tarif HT utilisé pour le précédent export du produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool False si l'un des paramètres est faux, sinon le prix du produit lors du précédent export
 */
function ctr_catalogs_get_price( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select ifnull(ctl_price_ht, 0) as price_ht
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'price_ht' );
}

/** Cette fonction permet de récupérer la quantité enregistrée du produit au moment de son export.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un comparateur de prix
 *	@return bool False si l'un des paramètres est faux, sinon la quantité du produit au moment de son export
 */
function ctr_catalogs_get_quantity( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select ifnull(ctl_qte, 0) as qte
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'qte' );
}

/** Cette fonction permet de récupérer les paramètres d'export du produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool False si l'un des paramètres est faux, sinon un tableau contenant les paramètres
 */
function ctr_catalogs_get_params( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select ifnull(ctl_params, \'\') as params
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	$params = ria_mysql_result( $res, 0, 'params' );
	return json_decode( $params, true );
}

/** Cette fonction permet de récupérer le montant personnalisé d'un produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param bool $direct Optionnel, par défaut on retourne que le montant directement sauvegardé sur le produit, mettre true pour remonté plus haut (catégorie puis famille)
 *	@param int $ctr_cat Optionnel, identifiant d'une famille d'export
 *	@return int Le montant de l'enchère du produit
 */
function ctr_catalogs_get_auctions( $ctr, $prd, $direct=true, $ctr_cat=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	// récupère l'enchère directement sur le produit
	$res = ria_mysql_query('
		select ctl_amount as amount
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
			and ctl_amount is not null
	');

	$auctions = false;

	if( (!$res || !ria_mysql_num_rows($res)) && !$direct ){
		// récupère l'enchère sur l'une de ses catégories
		$rcly = prd_products_categories_get( $prd, true );
		if( $rcly && ria_mysql_num_rows($rcly) ){
			while( $cly = ria_mysql_fetch_array($rcly) ){
				$rf = ctr_prd_categories_get_auctions( $ctr, $cly['cat'] );
				if( is_numeric($rf) && $rf>0 ){
					$auctions = $rf;
					break;
				}
			}
		}

		// récupère l'enchère sur la famille où il est exporté
		if( !$auctions ){
			$family = ctr_catalogs_get_categorie( $ctr, $prd, false );
			if( $family ){
				$ramount = ctr_cat_auctions_get( $ctr, $family );
				if( $ramount && ria_mysql_num_rows($ramount) )
					$auction = ria_mysql_result( $ramount, 0, 'amount' );
			}
		}

		// récupère l'enchère minimale sur la famille où il est exporté
		if( !$auctions && isset($family) && $family ){
			$auctions = ctr_categories_get_min_auctions( $ctr, $family );
		}
	} elseif( $res && ria_mysql_num_rows($res) ) {
		$auctions = ria_mysql_result( $res, 0, 'amount' );
	}

	if( !$auctions ){
		if( $ctr_cat>0 ){
			$tmp = ctr_categories_get_min_auctions( $ctr, $ctr_cat );
		}

		if( !isset($tmp) ){
			$parents = prd_parents_get( $prd );
			if( $parents ){
				while( $parent = ria_mysql_fetch_array($parents) ){
					$tmp = ctr_catalogs_get_auctions( $ctr, $parent['id'], false );
					if( $tmp ){
						break;
					}
				}
			}
		}

		if( isset($tmp) && $tmp ){
			$auctions = $tmp;
		}
	}

	return $auctions;
}

/** Cette fonction permet de récupérer la catégorie d'un comparateur à laquelle le produit est directement rattaché.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param bool $direct Optionnel, par défaut on retourne la famille directement lié au produit, mettre false pour essayer de retourner celle de sa catégorie
 *	@param bool $used_parent Optionnel, si la famille est désactivée alors on récupère une famille parent (si elle n'est pas aussi désactivée)
 *
 *	@return bool false si les des paramètres est omis ou bien faux
 *	@return int l'identifiant de la catégorie du comparateur ou 0 si aucun lien direct existe
 */
function ctr_catalogs_get_categorie( $ctr, $prd, $direct=true, $used_parent=false ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}
	$sql = '
		select ctl_cat_id as cat_id
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
			and ctl_cat_id is not null
	';

	$res = ria_mysql_query($sql);

	$family = false;

	if( (!$res || !ria_mysql_num_rows($res)) && !$direct ){
		// récupère les catégories publiés du produit
		$rcly = prd_products_categories_get( $prd, true );

		if( $rcly && ria_mysql_num_rows($rcly) ){

			while( $cly = ria_mysql_fetch_array($rcly) ){
				// vérifie si la catégorie est liée à une famille
				$tmp = ctr_prd_categories_get( $ctr, $cly['cat'] );
				if( $tmp && ria_mysql_num_rows($tmp) ){
					$family = ria_mysql_result( $tmp, 0, 'id' );
					break;
				}

			}
		}
	} elseif( $res && ria_mysql_num_rows($res) ){
		$family = ria_mysql_result( $res, 0, 'cat_id' );
	}

	if( $family && $used_parent ){
		// Si désactivé, on remonte dans la hiérarchie
		if( ctr_categories_get_disabled($ctr, $family) ){
			$rparent = ctr_categories_parents_get( $family );
			if( $rparent && ria_mysql_num_rows($rparent) ){
				while( $parent = ria_mysql_fetch_array($rparent) ){
					if( !ctr_categories_get_disabled($ctr, $parent['id']) ){
						$family = $parent['id'];
						break;
					}
				}
			}
		}
	}

	return $family;
}

/** Cette fonction permet de récupérer le titre de surcharge d'un produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param bool $direct Optionnel, par défaut la surcharge directe du titre est retourné, mettre false pour retourner le titre qui sera utilisé dans l'export
 *	@param bool $length_cut Optionnel, si le titre doit être coupé au nombre maximum accepté par le comparateur / la place de marché
 *	@return string La surchage sur le titre d'un produit
 */
function ctr_catalogs_get_prd_title( $ctr_id, $prd_id, $direct=true, $length_cut=false ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	$is_market = ctr_comparators_is_marketplace( $ctr_id );

	if( !$wst_id /* || $is_market */ ){
		$wst_id = $config['wst_id'];
	}

	$title = '';

	// Récupère le titre directement surchargé
	$res = ria_mysql_query('
		select ifnull(ctl_prd_title, \'\') as title
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr_id.'
			and ctl_prd_id='.$prd_id.'
	');

	if( $res && ria_mysql_num_rows($res) ){
		$title = ria_mysql_result( $res, 0, 'title' );
	}

	if( $direct ){
		return $title;
	}

	// Récupère le titre du modèle de donnée (si ce dernier existe et que le titre surchargé n'est pas défini)
	if( trim($title)=='' ){
		$rmdl = ctr_models_get( 0, $prd_id, $is_market );
		if( $rmdl && ria_mysql_num_rows($rmdl) ){
			$mdl = ria_mysql_fetch_array( $rmdl );

			if( trim($mdl['prd_title'])!='' ){
				$title = $mdl['prd_title'];
			}
		}
	}

	// Récupère le titre directement selon les informations du produit en dernier recours
	if( trim($title)=='' ){
		$title = prd_products_get_name( $prd_id, true );
	}

	if( $length_cut ){
		$max_len = ctr_comparators_get_max_title( $ctr_id );
		if( is_numeric($max_len) && $max_len>0 ){
			$title = strcut( $title, $max_len );
		}
	}

	return $title;
}

/** Cette fonction permet de récupérer la description de surcharge d'un produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param bool $direct Optionnel, par défaut la surcharge de la description est retournée, mettre false pour récupérer la description qui sera exportée
 *	@param bool $length_cut Optionnel, si la description doit être coupée au nombre maximum accepté par le comparateur / la place de marché
 *	@param bool $never_empty Optionnel, applicable seulement si $direct est à False, par défaut la description n'est jamais vide (récupère le titre en dernier recours), mettre false pour ne récupérer que la description
 *	@return string La surcharge de la description d'un produit
 */
function ctr_catalogs_get_prd_desc( $ctr_id, $prd_id, $direct=true, $length_cut=false, $never_empty=true ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	$is_market = ctr_comparators_is_marketplace( $ctr_id );

	if( !$wst_id /* || $is_market */ ){
		$wst_id = $config['wst_id'];
	}

	$desc = '';

	$res = ria_mysql_query('
		select ifnull(ctl_prd_desc, \'\') as "desc"
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr_id.'
			and ctl_prd_id='.$prd_id.'
	');

	if( $res && ria_mysql_num_rows($res) ){
		$desc = ria_mysql_result( $res, 0, 'desc' );
	}

	if( $direct ){
		return $desc;
	}

	// Récupère la description du modèle de donnée (si ce dernier existe et que le titre surchargé n'est pas défini)
	if( trim($desc)=='' ){
		$rmdl = ctr_models_get( 0, $prd_id, $is_market );
		if( $rmdl && ria_mysql_num_rows($rmdl) ){
			$mdl = ria_mysql_fetch_array( $rmdl );

			if( trim($mdl['prd_desc'])!='' ){
				$desc = $mdl['prd_desc'];
			}
		}
	}

	// Récupère la description directement selon les informations du produit en dernier recours
	if( trim($desc)=='' ){
		$desc = prd_products_get_desc( $prd_id ).' '.prd_products_get_desc_long( $prd_id );
		$desc = html_revert_wysiwyg( $desc );
	}

	// Si la description est vide, on récupère le titre du produit exporté
	if( $never_empty && trim($desc)=='' ){
		$desc = ctr_catalogs_get_prd_title( $ctr_id, $prd_id, false );
	}

	if( $length_cut ){
		$max_len = is_numeric($length_cut) ? $length_cut : ctr_comparators_get_max_desc( $ctr_id );

		if( is_numeric($max_len) && $max_len>0 ){
			$desc = strcut( $desc, $max_len );
		}
	}

	return $desc;
}

/** Cette fonction permet de récupérer un tableau des identifiants des images à envoyer sur le comparateur / la place de marché.
 *	L'ordre de priorité est la suivante : Surcharge > Modèle > Fiche produit
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / d'une place de marché
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $limit Optionnel, permet de limiter le nombre d'image (si 1 alors le premier identifiant est retourné hors tableau)
 *	@param string $position Optionnel, par défaut 'all' tous les images sont dans le tableau, mettre 'main' pour avoir l'id de l'image principale, 'second' pour n'avoir que les images secondaires
 *	@return array Un tableau contenant tous les identifiants d'images (le premier identifiant est l'image principale)
 */
function ctr_catalogs_get_prd_images( $ctr_id, $prd_id, $limit=0, $position='all' ){
	$ar_imgs = array();
	$limit = is_numeric($limit) && $limit>0 ? $limit : 0;

	if( $position=='main' ){
		$limit = 1;
	}

	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return ( $limit > 1 ? $ar_imgs : 0 );
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return ( $limit > 1 ? $ar_imgs : 0 );
	}

	global $config;

	$is_market = ctr_comparators_is_marketplace( $ctr_id );

	$rimg = ctr_catalogs_images_get( $prd_id, $ctr_id );
	if( !$rimg || !ria_mysql_num_rows($rimg) ){
		$model = false;

		// Récupère le modèle en place
		$rmdl = ctr_models_get( 0, $prd_id, $is_market );
		if( $rmdl && ria_mysql_num_rows($rmdl) ){
			$model = ria_mysql_fetch_assoc( $rmdl );
		}

		if( $model!==false ){
			$rimg = ctr_images_get( $model['mdl_id'] );
		}
	}

	if( $rimg && ria_mysql_num_rows($rimg) ){
		while( $img = ria_mysql_fetch_assoc($rimg) ){
			$ar_imgs[] = $img['id'];
		}
	}else{
		$img_main = prd_images_main_get( $prd_id );
		if( is_numeric($img_main) && $img_main ){
			$ar_imgs[] = $img_main;
		}

		$secondary = prd_images_get( $prd_id );
		if( $secondary ){
			while( $s = ria_mysql_fetch_assoc($secondary) ){
				$ar_imgs[] = $s['id'];
			}
		}
	}

	$return = false;
	if( $limit==1 ){
		$return = ( sizeof($ar_imgs) ? $ar_imgs[0] : 0 );
	}else{
		$offset = $position=='all' ? 0 : 1;
		$length = $limit>0 ? $limit : null;
		$return = array_slice( $ar_imgs, $offset, $length );
	}

	return $return;
}
/** Cette fonction permet de récupérer les produits enfants d'un produit qui sont exportés sur un comparateur ou une place de marché
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix ou de la place de marché
 *	@param int $parent Obligatoire, identifiant du produit parent
 *	@param bool $actived Optionnel, par défaut tous les produits enfants exportés sont retournés, mettre True pour n'avoir que ceux qui sont activés, False pour les autres
 *	@return array Un tableau contenant les identifiants des produits enfants
 */
function ctr_catalogs_get_childs( $ctr, $parent, $actived=null ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($parent) || $parent<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select ctl_prd_id as prd
		from ctr_catalogs
			join prd_hierarchy on (ctl_tnt_id=prd_tnt_id and ctl_prd_id=prd_child_id)
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and prd_parent_id='.$parent.'
			and ctl_ctr_id='.$ctr.'
	';

	if( $actived!==null ){
		if( $actived ){
			$sql .= ' and ctl_active=1';
		}else{
			$sql .= ' and ctl_active=0';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$ar_childs = array();
	while( $r = ria_mysql_fetch_array($res) ){
		$ar_childs[] = $r['prd'];
	}

	return $ar_childs;
}

/** Cette fonction permet d'activer un produit dans l'export du catalogue pour un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int|array $prd Obligatoire, identifiant ou tableau d'identifiants d'un produit
 *	@return bool false si une erreur s'est produite, true dans le cas contraire
 */
function ctr_catalogs_activated( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_array($prd) ){
		if( !is_numeric($prd) || $prd<=0 ){
			return false;
		}

		if( !ctr_catalogs_exists($ctr, $prd) ){
			if( !ctr_catalogs_add($ctr, $prd) ){
				return false;
			}else{
				return true;
			}
		}

		$prd = array( $prd );
	} else {
		if( !sizeof($prd) ){
			return false;
		}

		$temp = $prd; $prd = array();

		foreach( $temp as $p ){
			if( !is_numeric($p) || $p<=0 ){
				return false;
			}

			if( !ctr_catalogs_exists($ctr, $p) ){
				if( !ctr_catalogs_add($ctr, $p) ){
					return false;
				}
			}else{
				$prd[] = $p;
			}
		}

		if( !sizeof($prd) ){
			return true;
		}
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	return ria_mysql_query('
		update ctr_catalogs
		set ctl_active=1
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id in ('.implode( ', ', $prd ).')
	');
}

/** Cette fonction permet de désactiver un produit dans l'export du catalogue pour un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int|array $prd Obligatoire, identifiant ou tableau d'identifiants d'un produit
 *	@return bool false si une erreur s'est produite, true dans le cas contraire
 */
function ctr_catalogs_unactivated( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_array($prd) ){
		if( !is_numeric($prd) || $prd<=0 ){
			return false;
		}

		$prd = array( $prd );
	} else {
		if( !sizeof($prd) ){
			return false;
		}

		$temp = $prd; $prd = array();

		foreach( $temp as $p ){
			if( !is_numeric($p) || $p<=0 ){
				return false;
			}

			if( ctr_catalogs_exists($ctr, $p) ){
				$prd[] = $p;
			}
		}

		if( !sizeof($prd) ){
			return true;
		}
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	return ria_mysql_query('
		update ctr_catalogs
		set ctl_active=0
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id in ('.implode( ', ', $prd ).')
	');
}

/** Cette fonction permet de mettre à jour le titre de surcharge d'un produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $title Optionnel, nouveau titre de surcharge (laissé vide pour l'effacé)
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 */
function ctr_catalogs_set_prd_title( $ctr, $prd, $title='' ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_catalogs_exists($ctr, $prd) )
		return ctr_catalogs_add( $ctr, $prd, 0, $title, false, null );

	// Contrôle que le titre est bien une surcharge
	$rp = ria_mysql_query('
		select if(ifnull(prd_title, \'\')!=\'\', prd_title, prd_name) as title
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd.'
	');

	if( !$rp || !ria_mysql_num_rows($rp) ){
		return false;
	}

	$p = ria_mysql_fetch_array( $rp );
	if( trim($title)!='' ){
		if( $title==$p['title'] ){
			return true;
		}
	}

	$res = ria_mysql_query('
		update ctr_catalogs
		set ctl_prd_title='.( trim($title) ? '\''.addslashes($title).'\'' : 'null' ).'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	');
	return $res;
}

/** Cette fonction permet de mettre à jour la description de surcharge d'un produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $desc Optionnel, nouvelle description de surcharge (laissé vide pour l'effacé)
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 */
function ctr_catalogs_set_prd_desc( $ctr, $prd, $desc='' ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_catalogs_exists($ctr, $prd) )
		return ctr_catalogs_add( $ctr, $prd, 0, false, $desc, null );

	return ria_mysql_query('
		update ctr_catalogs
		set ctl_prd_desc='.( trim($desc) ? '\''.addslashes($desc).'\'' : 'null' ).'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	');
}

/** Cette fonction permet de savoir si un produit est déjà exporté dans le catalogue.
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@param int $prd Obligatoire, identifiant du produit
 *	@return bool true si il est dajà exporté
 *	@return bool false dans le cas contraire
 */
function ctr_catalogs_is_publish( $ctr, $prd ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select 1
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
			and ctl_active=1
		limit 0,1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;

}

/** Cette fonction permet de mettre à jour la famille d'export d'un produit
 *	@param int $ctr Identifiant d'un comparateur
 *	@param int $prd Identifiant d'un produit
 *	@param int $cat Optionnel, identifiant d'une famille du comparateur / de la place de marché
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function ctr_catalogs_update_categorie( $ctr, $prd, $cat=0 ){
	if( !is_numeric($ctr) || $ctr <= 0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_catalogs_exists($ctr, $prd) )
		return ctr_catalogs_add( $ctr, $prd, $cat, false, false, null );

	$res = ria_mysql_query('
		update ctr_catalogs
		set ctl_cat_id = '.( $cat > 0 ? $cat : 'null' ).'
		where ctl_tnt_id = '.$config['tnt_id'].'
			and ctl_wst_id = '.$wst_id.'
			and ctl_ctr_id = '.$ctr.'
			and ctl_prd_id = '.$prd.'
	');

	return $res;
}

/** Cette fonction permet de mettre à jour les paramètres pour un export de produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $params Optionnel, paramètres de l'export, mettre false pour supprimer tous les paramètres
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contaire
 */
function ctr_catalogs_update_params( $ctr, $prd, $params=false ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( $params!==false && trim($params)=='' ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( !ctr_catalogs_exists($ctr, $prd) ){
		if( !ctr_catalogs_add($ctr, $prd, 0, false, false, null) ){
			return false;
		}
	}

	$res = ria_mysql_query('
		update ctr_catalogs
		set ctl_params='.( $params ? '\''.addslashes( $params ).'\'' : 'null' ).'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	');

	return $res;
}

/** Cette fonction permet de mettre à jour le tarif utilisé lors de l'export d'un produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param float $price Optionnel, prix du produit utilisé lors de son export, False pour réinitialisé la valeur (entrainera une nouvelle maj sur le comparateur / place de marché)
 *	@param float $tva_rate Optionnel, taux de TVA utilisé lors de son export, False pour réinitialisé la valeur (entrainera une nouvelle maj sur le comparateur / place de marché)
 *	@return bool False si la mise à jour a échouée, True dans le cas contraire
 */
function ctr_catalogs_update_price( $ctr, $prd=0, $price=false, $tva_rate=false ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	if( $price!==false && (!is_numeric($price) || $price<=0) ){
		return false;
	}

	if( $tva_rate!==false && (!is_numeric($tva_rate) || $tva_rate<0) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	// Si un identifiant de produit est donné en paramètre, on contrôle l'existance de ce dernier dans le catalogue
	if( $prd > 0 ){
		if( !ctr_catalogs_exists($ctr, $prd) ){
			if( !ctr_catalogs_add($ctr, $prd, 0, false, false, null) ){
				return false;
			}
		}
	}

	$sql = '
		update ctr_catalogs
		set ctl_price_ht='.( $price ? $price : 'null' ).',
			ctl_tva_rate='.( $tva_rate ? $tva_rate : 'null' ).'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
	';

	if( $prd > 0 ){
		$sql .= ' and ctl_prd_id='.$prd;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour la quantité du produit au moment de son export.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param int $qte Optionnel, quantité du produit au moment de son export, False pour réinitialisé la valeur (entrainera une nouvelle maj sur le comparateur / place de marché)
 *	@return bool False si la mise à jour a échouée, True dans le cas contraire
 */
function ctr_catalogs_update_quantity( $ctr, $prd=0, $qte=false ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	if( $qte!==false && !is_numeric($qte) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	// Si un identifiant de produit est donné en paramètre, on contrôle l'existance de ce dernier dans le catalogue
	if( $prd > 0 ){
		if( !ctr_catalogs_exists($ctr, $prd) ){
			if( !ctr_catalogs_add($ctr, $prd, 0, false, false, null) ){
				return false;
			}
		}
	}

	$sql = '
		update ctr_catalogs
		set ctl_qte='.(is_numeric($qte) ? $qte : 'null').'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
	';

	if( $prd > 0 ){
		$sql .= ' and ctl_prd_id='.$prd;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de supprimer un produit d'un catalogue de comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@param int $prd Optionnel, identifiant (ou tableau d'identifiant) d'un produits à retirer
 *	@return bool true si le ou les produits ont bien été retirés
 *	@return bool false dans le cas contraire
 */
function ctr_catalogs_del( $ctr, $prd=0 ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_numeric($prd) && !is_array($prd) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	if( is_array($prd) ){
		foreach( $prd as $p ){
			if( !is_numeric($p) || $p<=0 ) return false;
		}
	}

	$sql = '
		update ctr_catalogs
		set ctl_active=0
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
	';

	if( is_array($prd) && sizeof($prd) ){
		$sql .= ' and ctl_prd_id in ('.implode(',', $prd).')';
	}elseif( is_numeric($prd) && $prd>0 ){
		$sql .= ' and ctl_prd_id='.$prd;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de connaître le nombre de produits exportée dans le catalogue d'un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $ctr_cat Optionnel, permet de filtrer selon une catégorie du comparateur
 *	@return int Retourne le nombre de produits exportés vers le comparateur
 */
function ctr_catalogs_count( $ctr, $ctr_cat=0 ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_numeric($ctr_cat) || $ctr_cat<0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}
	$sql = '
		select ctl_prd_id
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_active=1
	';

	if( $ctr_cat )
		$sql .= ' ctl_cat_id='.$ctr_cat;

	if( !ria_mysql_query($sql) )
		return 0;
	else
		return ria_mysql_num_rows( ria_mysql_query($sql) );
}

/** Cette fonction permet de comptabiliser les clics provenants d'un comparateur de prix
 *	@param string $source Obligatoire, il s'agit du terme source utilisé
 *	@param int $cat Obligatoire, identifiant d'une famille de comparateur de prix
 *	@param int $prd Obligatoire, identifiant du produit consulté
 *	@return bool false en cas d'échec lors de la sauvegarde du clic, true dans le cas contraire
 */
function ctr_clicks_add( $source, $cat, $prd ){
	if( !prd_products_exists($prd) ) return false;

	$prd = urldecode( $prd );

	// récupère le comparateur par rapport à la source
	$rctr = ctr_comparators_get_bysource( $source );
	if( !$rctr || !ria_mysql_num_rows($rctr) ) return false;
	$ctr = ria_mysql_fetch_array($rctr);

	// vérifier que la catégorie appartient bien au comparateur de prix
	if( !ctr_categories_exists($cat, $ctr['id']) ) return false;
	global $config;

	if( (isset($_SESSION['usr_id']) && $_SESSION['usr_prf_id']!=PRF_ADMIN || !isset($_SESSION['usr_id'])) && !stats_is_bot() ){

		$cols = array( 'cct_tnt_id' ,'cct_wst_id', 'cct_ctr_id', 'cct_prd_id', 'cct_cat_id', 'cct_datetime' );
		$vals = array( $config['tnt_id'], $config['wst_id'], $ctr['id'], $prd, $cat, 'now()' );

		if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ){
			$cols[] = 'cct_usr_id';
			$vals[] = $_SESSION['usr_id'];
		}

		$sess = session_id();
		if( !trim($sess) ) session_start();
		$cols[] = 'cct_session';
		$vals[] = '\''.addslashes( session_id() ).'\'';

		if( isset($_SERVER['HTTP_REFERER']) && trim($_SERVER['HTTP_REFERER']) ){
			$cols[] = 'cct_http_referer';
			$vals[] = '\''.addslashes($_SERVER['HTTP_REFERER']).'\'';
		}

		if( isset($_SERVER['REMOTE_ADDR']) && trim($_SERVER['REMOTE_ADDR']) ){
			$cols[] = 'cct_ip';
			$vals[] = '\''.addslashes($_SERVER['REMOTE_ADDR']).'\'';
		}

		return ria_mysql_query('
			insert into ctr_clicks
				('.implode( ',', $cols ).')
			values
				('.implode( ',', $vals ).')
		');
	}

	return true;
}

 /// @}

/** \defgroup ctr_categories Categories des comparateurs - produits
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories des comparateurs de prix
 *	@{
 */

/** Cette fonction permet l'affichage de la catégorie dans l'export
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $display Optionnel, façon d'afficher la catégorie dans l'export, par défaut à 1 :
 *				1 : référence comparateur
 *				2 : hierarchique (Vêtements > T-Shirt > T-Shirt à manches courtes)
 *				3 : nom (T-Shirt à manches courtes)
 *	@param string $separate Optionnel, séparateur utilisé dans le mode 2 par défaut '>'
 *	@return bool false si l'un des paramètres est faux
 *	@return string l'affichage de la catégorie
 */
function ctr_categories_export( $ctr, $cat, $display=1, $separate='>' ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !in_array($display, array(1, 2, 3)) ){
		return false;
	}

	if( $display==2 && !trim($separate) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$rcat = ctr_categories_get( $ctr, $cat, '', 0, false, '', false );

	if( !$rcat || !ria_mysql_num_rows($rcat) )
		return false;

	$category = ria_mysql_fetch_array( $rcat );

	if( $display==1 ){
		return trim($category['ref']) ? $category['ref'] : $category['id'];
	} elseif( $display==3 ){
		return $category['name'];
	} else {

		$string = '';
		$separate = trim( $separate );

		$rh = ctr_categories_parents_get( $cat, 0, 0, $ctr );
		if( $rh && ria_mysql_num_rows($rh) ){
			while( $h = ria_mysql_fetch_array($rh) ){
				$string .= ($string!='' ? ' '.$separate.' ' : '').$h['name'];
			}
		}

		$string .= ($string!='' ? ' '.$separate.' ' : '').$category['name'];
		return $string;
	}
}

/** Cette fonction permet d'ajout une nouvelle famille.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $name Obligatoire, nom de la nouvelle famille
 *	@param string $ref Optionnel, référence de la nouvelle famille
 *	@param int $parent Optionnel, identifiant d'une catégorie parent
 *	@param float $cpc Optionnel, cout par clic de la nouvelle famille
 *	@return int|bool l'identifiant de la catégorie, false dans le cas contraire
 */
function ctr_categories_add( $ctr, $name, $ref='', $parent=0, $cpc=0 ){
	if( !ctr_comparators_exists($ctr) ) return false;
	if( !trim($name) ) return false;
	if( !is_numeric($parent) || $parent<0 ) return false;
	if( !is_numeric($cpc) || $cpc<0 ) return false;

	$ref = trim($ref) ? '\''.addslashes($ref).'\'' : 'null';
	$cpc = $cpc>0 ? $cpc : 'null';

	$sql = '
		insert into ctr_categories
			(cat_ref, cat_name, cat_parent_id, cat_ctr_id, cat_cpc)
		values
			('.$ref.', \''.addslashes($name).'\', '.$parent.', '.$ctr.', '.$cpc.')
	';

	$res = ria_mysql_query( $sql );
	if( !$res )
		return false;

	return ria_mysql_insert_id();
}

/**
 *	Retourne l'ensemble des familles de produits pour un comparateur de prix donné.
 *	@param int $ctr Optionnel, il s'agit de l'identifiant d'un comparateur de prix
 *	@param int $id Optionnel, il s'agit de l'identifiant d'une famille.
 *	@param int $parent Optionnel, identifiant d'une famille parent
 *	@param bool $publish Optionnel, détermine si la famille doit être publiée
 *	@param $sort Optionnel, permet de trier les famille
 *	@param $like Optionnel, permet de fitrer le résultat sur une partie d'une chaine de caractères
 *	@param $disabled Optionnel, par défaut seul les familles activées sont retournées, mettre True pour toutes les récupérer
 *	@param string $ref Optionnel, référence de la catégorie du comparateur de prix
 *	@param $used Optionnel, par défaut toutes les catégories sont récupérées, mettre true pour récupérer que les catégories utilisées
 *
 *	@return resource Le résultat est fourni sous la forme d'un résultat de requête mysql,
 *	comprenant les colonnes :
 *		- id : identifiant de la famille
 *		- name : nom de la famille
 *		- title : titre de la famille
 *		- parent : identifiant de la famille parent
 *		- ref : référence du comparateur.
 *		- ctr_id : identifiant du comparateur de prix
 *		- disabled : date de désactivation
 *		- is_disabled : si la famille est désactivée ou non
 *		- cpc : cout par clic de la famille
 *		- publish : si oui ou non la catégorie est publiée
 */
function ctr_categories_get( $ctr=0, $id=0, $parent='', $publish=0, $sort=false, $like='', $disabled=false, $ref=false, $used=false ){
	if( $ctr>0 && !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( $id>0 && !ctr_categories_exists($id) ){
		return false;
	}

	if( $parent>0 && !is_numeric($parent) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ($ctr>0 && ctr_comparators_is_marketplace($ctr)) */ ){
		$wst_id = $config['wst_id'];
	}

	// Contrôle s'il existe au moins une catéogrie non public
	$rno_filter = ria_mysql_query('select 1 from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].' and cat_ctr_id='.$ctr);
	$no_filter = !$rno_filter  || !ria_mysql_num_rows($rno_filter) ? true : false;

	$sql = '
		select
			c1.cat_ctr_id as ctr_id, c1.cat_id as id, cat_name as name, cat_parent_id as parent, cat_ref as ref, cat_date_disabled as disabled, cat_cpc as cpc,
	';

	if( isset($config['tnt_id']) ){
		$sql .= '
			if('.($no_filter ? 1 : 0).'=1, 1, if(c2.cat_id is null, 0, 1)) as publish,
		';
	}

	$sql .= '
			ifnull(cat_title, cat_name) as title,
			if(cat_date_disabled is null, 0, 1) as is_disabled
		from ctr_categories as c1
	';

	if( isset($config['tnt_id']) ){
		$sql .= '
			left join ctr_cat_publish as c2 on ( c1.cat_ctr_id=c2.cat_ctr_id and c1.cat_id=c2.cat_id and '.$config['tnt_id'].'=cat_tnt_id )
		';
	}

	$sql .= '
		where 1
	';

	if( $ctr>0 )
		$sql .= ' and c1.cat_ctr_id='.$ctr;
	if( $id>0 )
		$sql .= ' and c1.cat_id='.$id;
	if( $parent!=='' )
		$sql .= ' and cat_parent_id='.$parent;
	if( isset($config['tnt_id']) && !$no_filter && $publish )
		$sql .= ' and if(c2.cat_id is null, 0, 1)=1';
	if( $like!='' )
		$sql .= ' and lower(cat_name) like "%'.addslashes( mb_strtolower($like, 'UTF-8') ).'%"';
	if( $ref != '' )
		$sql .= ' and cat_ref = \''.addslashes(trim($ref)).'\'';

	if( !$disabled ){
		$sql .= ' and cat_date_disabled is null';
	}elseif( $disabled ){
		$sql .= ' and cat_date_disabled is not null';
	}

	if( $used ){
		$sql .= '
			and (
				exists (
					select 1
					from ctr_prd_categories
					where cpc_tnt_id='.$config['tnt_id'].'
						and cpc_wst_id='.$wst_id.'
						and cpc_cat_id=c1.cat_id
						and cpc_ctr_id=c1.cat_ctr_id
				)
				or exists (
					select 1
					from ctr_catalogs
					where ctl_tnt_id='.$config['tnt_id'].'
						and ctl_wst_id='.$wst_id.'
						and ctl_cat_id=c1.cat_id
						and ctl_ctr_id=c1.cat_ctr_id
				)
			)
		';
	}

	if( is_numeric($ctr) && $ctr==CTR_AMAZON ){
		if( isset($config['amazon_cat_used']) && is_array($config['amazon_cat_used']) && sizeof($config['amazon_cat_used']) ){
			$sql .= '
				and ( exists (
					select 1
					from ctr_cat_hierarchy as c3
					where c3.cat_ctr_id='.$ctr.' and c3.cat_child_id=c1.cat_id and c3.cat_parent_id in ('.implode( ', ', $config['amazon_cat_used'] ).')
				) or c1.cat_id in ('.implode( ', ', $config['amazon_cat_used'] ).') )
			';
		}
	}

	$sort_final = array();
	if( !is_array($sort) || !sizeof($sort) )
		$sort_final = array('cat_name asc');

	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col=>$dir ){
			$dir = $dir=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'cat_id':
					array_push( $sort_final, 'cat_id '.$dir );
					break;
			}
		}
	}

	$sql .= '
		order by '.implode(', ', $sort_final).'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
	}

	return $res;
}

/** Cette fonction permet de récupérer les objects (Catégories ou produits) directement rattachés à une famille.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $cat_id Obligatoire, identifiant d'une famille
 *	@param $class Optionnel, identifiant d'une classe à filtrer
 *	@return array Un tableau contenant, pour chaque classe, les objects rattachés à la famille (le niveau classe n'est pas disponible si le second paramètre est fourni)
 */
function ctr_categories_get_objects( $ctr_id, $cat_id, $class=0 ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
	    return false;
	}

	if( !is_numeric($cat_id) || $cat_id<=0 ){
	    return false;
	}

	if( !is_numeric($class) || $class<0 ){
	    return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	$ar_objects = array(
		CLS_CATEGORY	=> array(),
		CLS_PRODUCT		=> array()
	);

	if( !$class || $class == CLS_CATEGORY ){
		$sql = '
			select cat_id as id, cat_name as name
			from ctr_prd_categories
				join prd_categories on ( cat_tnt_id = cpc_tnt_id and cat_id = cpc_prd_cat )
			where cpc_tnt_id = '.$config['tnt_id'].'
				and cpc_ctr_id = '.$ctr_id.'
				and cpc_cat_id = '.$cat_id.'
				'.( $wst_id ? ' and cpc_wst_id = '.$wst_id : '' ).'
			group by cat_id
		';

		$res = ria_mysql_query( $sql );
		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				$ar_objects[ CLS_CATEGORY ][] = $r;
			}
		}
	}

	if( !$class || $class == CLS_PRODUCT ){
		$sql = '
			select prd_id as id, prd_name as name
			from ctr_catalogs
				join prd_products on ( ctl_tnt_id = prd_tnt_id and ctl_prd_id = prd_id )
			where ctl_tnt_id = '.$config['tnt_id'].'
				and ctl_ctr_id = '.$ctr_id.'
				and ctl_cat_id = '.$cat_id.'
				'.( $wst_id ? ' and ctl_wst_id = '.$wst_id : '' ).'
			group by prd_id
		';

		$res = ria_mysql_query( $sql );
		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				$ar_objects[ CLS_PRODUCT ][] = $r;
			}
		}
	}

	if( $class > 0 && !isset($ar_objects[ $class ]) ){
		return false;
	}

	return ( $class > 0 ? $ar_objects[ $class ] : $ar_objects );
}

/** Cette fonction permet de récupérer le nombre d'utilisation d'une famille.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return array Un tableau contenant :
 *				- categories : nombre de catégories utilisant la famille
 *				- products : nombre de produits utilisant directement la famille (surcharge du classe de ses catégories)
 */
function ctr_categories_get_count_used( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$count = array( 'categories' => 0, 'products' => 0 );

	$rcats = ria_mysql_query('
		select 1
		from ctr_prd_categories
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_cat_id='.$cat.'
	');

	if( $rcats && ria_mysql_num_rows($rcats) ){
		$count['categories'] = ria_mysql_num_rows( $rcats );
	}

	$rprd = ria_mysql_query('
		select 1
		from ctr_catalogs
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_cat_id='.$cat.'
	');

	if( $rprd && ria_mysql_num_rows($rprd) ){
		$count['products'] = ria_mysql_num_rows( $rprd );
	}

	return $count;
}

/** Cette fonction permet de récupérer le minimum d'une enchère sur une famille de comparateur.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return bool false si aucune information n'est trouvée, sinon le montant minimum de l'enchère
 */
function ctr_categories_get_min_auctions( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$sql = '
		select cat_min_amount as min_amount
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
			and cat_min_amount is not null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'min_amount' );
}

/** Cette fonction permet de savoir si une famille est désactivée ou non.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return bool true si la catégorie est désactivée, false dans le cas contraire
 */
function ctr_categories_get_disabled( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$sql = '
		select if(cat_date_disabled is null, 0, 1) as is_disabled
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'is_disabled' );
}

/** Cette fonction permet de récupérer le nom d'une famille de comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return bool False si l'un des paramètres est omis ou faux, sinon le nom de la famille
 */
function ctr_categories_get_name( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;

	$sql = '
		select cat_name as name
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction permet de récupérer la référence d'une famille d'un comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param $direct Optionnel, par défaut si la référence n'est pas fournie alors on retourne l'identifiant, mettre true pour retourner que la référence
 *	@return bool false si la famille n'existe pas, sinon la référence (si celle n'est pas renseignée, on retourne l'identifiant de la famille)
 */
function ctr_categories_get_ref( $ctr, $cat, $direct=false ){
	if( !is_numeric($ctr) || $ctr<0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;

	$sql = '
		select '.( $direct ? 'cat_ref' : 'ifnull(cat_ref, cat_id)' ).' as ref
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'ref' );
}

/** Cette fonction permet de récupérer l'identifiant d'une famille d'un comparateur de prix selon sa référence.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $ref Obligatoire, référence d'une famille de produit
 *	@return int L'identifiant de la première famille trouvée avec la référence donnée
 */
function ctr_categories_get_id_byref( $ctr, $ref ){
	if( !is_numeric($ctr) || $ctr<0 ) return false;
	if( trim($ref)=='' ) return false;

	$sql = '
		select cat_id as id
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_ref=\''.$ref.'\'
			and cat_date_disabled is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'id' );
}

/** Cette fonction permet de récupérer l'identifiant de la famille parent d'une autre famille.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $cat Obligatoire, iedntifiant d'une famille
 *	@return bool false en cas d'échèc sinon l'identifiant de la famille parent (0 s'il s'agit d'une famille de première niveau)
 */
function ctr_categories_get_parent( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('
		select cat_parent_id as parent
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'parent' );
}

/** Cette fonction permet de récupérer la famille d'un comparateur par rapport à une référence.
 *	@param string $ref Obligatoire, référence de la famille recherchée (il peut s'agir de l'identifiant de la famille si celle-ci n'a pas de référence)
 *	@return bool false en cas de problème ou de non existance de la référence, sinon un résultat MySQL contenant :
 *				- id : identifiant de la famille
 *				- name : nom de la famille
 *				- title : titre de la famille
 */
function ctr_categories_get_byref( $ref ){
	if( !trim($ref) ) return false;

	$sql = '
		select cat_id as id, cat_name as name, ifnull(cat_title, cat_name) as title
		from ctr_categories
		where ifnull(cat_ref, cat_id)=\''.addslashes($ref).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return $res;
}

/** Cette fonction permet de mettre à jour la date de désactivation d'une famille de comparteur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param bool $disabled Optionnel, par défaut la famille est désactivée, mettre false pour la réactivée
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 */
function ctr_categories_update_disabled( $ctr, $cat, $disabled=true ){
	if( !ctr_categories_exists($cat, $ctr) ) return false;

	$disabled = $disabled ? 'now()' : 'null';

	return ria_mysql_query('
		update ctr_categories
		set cat_date_disabled='.$disabled.'
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	');
}

/** Cette fonction permet de mettre à jour l'identifiant de la famille parent.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param int $parent Optionnel, nouvel identifiant de la famille parent, mettre 0 pour retirer celui qui est déjà affecté
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 */
function ctr_categories_update_parent( $ctr, $cat, $parent=0 ){
	if( !ctr_categories_exists($cat, $ctr) ) return false;
	if( !is_numeric($parent) || $parent<0 ) return false;

	return ria_mysql_query('
		update ctr_categories
		set cat_parent_id='.$parent.'
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	');
}

/** Cette fonction permet de mettre à jour le nom de la catégorie.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param string $name Facultatif, nouveau nom pour cette catégorie
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 *	\warning Il est étrange que l'argument $name soit facultatif alors que l'objet de la fonction est de fournir un nouveau nom à une catégorie. Il serait plus normal que ce paramètre soit obligatoire.
 */
function ctr_categories_update_name( $ctr, $cat, $name=0 ){
	if( !ctr_categories_exists($cat, $ctr) ) return false;
	if( trim($name)=='' ) return false;

	return ria_mysql_query('
		update ctr_categories
		set cat_name=\''.$name.'\'
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	');
}

/** Cette fonction permet de mettre à jour le code de la catégroie.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param $code Optionnel, nouvel identifiant de la famille parent, mettre 0 pour retirer celui qui est déjà affecté
 *	@return bool true en cas de succès de la mise à jour, false dans le cas contraire
 */
function ctr_categories_update_ref( $ctr, $cat, $code='' ){
	if( !ctr_categories_exists($cat, $ctr) ){
		return false;
	}

	return ria_mysql_query('
		update ctr_categories
		set cat_ref = '.( trim($code) != '' ? '"'.addslashes( $code ).'"' : 'null' ).'
		where cat_ctr_id='.$ctr.'
			and cat_id='.$cat.'
	');
}

/** Cette fonction permet de tester l'existance d'une famille d'un comparateur de prix.
 *	@param int $id Obligatoire, Identifiant d'une catégorie
 *	@param int $ctr Optionnel, permet de vérifier si une catégorie appartient bien à un comparateur donnée
 *	@return bool true si elle existe bien dans la base de données
 *	@return bool false dans le cas contraire
 */
function ctr_categories_exists( $id, $ctr=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($ctr) || $ctr<0 ) return false;

	$sql = '
		select 1
		from ctr_categories
		where cat_id='.$id.'
	';

	if( $ctr>0 )
		$sql .= ' and cat_ctr_id='.$ctr;

	$sql .= ' limit 0,1';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de tester l'existance d'une famille de comparateur de prix selon son nom.
 *	Important : à n'utiliser que par le système de création d'un comparateur
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $name Obligatoire, nom de la famille
 *	@return int L'identifiant de la catégorie si elle existe, False dans le cas contraire
 */
function ctr_categories_exists_name( $ctr, $name ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( trim($name)=='' ) return false;

	$name = mb_strtolower( $name, 'UTF-8' );

	$sql = '
		select cat_id as cat
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and lower(cat_name)=\''.addslashes( $name ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'cat' );
}

/** Cette fonction permet de tester l'existance d'une famille de comparateur de prix selon sa reference.
 *  @param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *  @param string $ref Obligatoire, reference de la famille
 *  @return int L'identifiant de la catégorie si elle existe, false dans le cas contraire
 */
function ctr_categories_exists_ref( $ctr, $ref ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( trim($ref)=='' ) return false;

	$sql ='
		select cat_id as cat
		from ctr_categories
		where cat_ctr_id='.$ctr.'
			and cat_ref = "'.$ref.'"
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'cat' );
}

/** Cette fonction permet de connaître le nombre de catégories d'un comparateur de prix.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param bool $publish Optionnel, savoir le nombre de catégories publiées
 *	@return int Retourne le nombre de catégories
 *	@return bool false ne cas d'échec
 */
function ctr_categories_count( $ctr, $publish=0 ){
	if( !ctr_comparators_exists($ctr) )	return false;
	global $config;

	if( $publish )
		$sql = 'select cat_id from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].' and cat_ctr_id='.$ctr;
	else
		$sql = 'select cat_id from ctr_categories where cat_ctr_id='.$ctr;

	$res = ria_mysql_query($sql);
	if( $res )
		return ria_mysql_num_rows($res);
	else
		return false;
}

/** Cette fonction permet de supprimer une ou plusieurs catégories de comparateur de prix ou de place de marché
 *	@param int $ctr_id Identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int|array $cat_id Facultatif, identifiant ou tableau d'identifiants de catégories
 *	@return bool True si la ou les catégories ont bien été supprimées, False dans le cas contraire
 */
function ctr_categories_delete( $ctr_id, $cat_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	$cat_id = control_array_integer( $cat_id, false );
	if( $cat_id === false ){
		return false;
	}

	$sql = '
		delete from ctr_categories
		where cat_ctr_id = '.$ctr_id.'
	';

	if( count($cat_id) ){
		$sql .= ' and cat_id in ('.implode( ', ', $cat_id ).')';
	}

	return ria_mysql_query( $sql );
}

/**	Cette fonction enregistre une relation parent/enfant dans la table de hiérarchie
 *	des catégories d'un comparateur de prix
 *
 *	@param int $ctr Identifiant de la catégorie
 *	@param int $parent Identifiant de la catégorie parent
 *	@param int $child Identifiant de la catégorie enfant
 *
 *	@return bool true en cas de succès, False en cas d'échec
 */
function ctr_categories_hierarchy_add( $ctr, $parent, $child ){
	if( !ctr_comparators_exists($ctr) ) return false;
	if( !is_numeric($parent) || $parent<=0 ) return false;
	if( !is_numeric($child) || $child<=0 ) return false;

	// Ajoute la catégorie en tant qu'enfant direct de sa catégorie parente
	$depth = ria_mysql_num_rows( ctr_categories_parents_get($parent, 0, 0, $ctr) );
	ria_mysql_query('insert into ctr_cat_hierarchy (cat_ctr_id, cat_parent_id,cat_child_id,cat_parent_depth) values ('.$ctr.','.$parent.','.$child.','.$depth.');');

	// Maintient à jour la hiérarchie indirecte
	$gparents = ctr_categories_parents_get($parent, 0, 0, $ctr);
	while( $r = ria_mysql_fetch_array($gparents) )
		ria_mysql_query('insert into ctr_cat_hierarchy (cat_ctr_id, cat_parent_id,cat_child_id,cat_parent_depth) values ('.$ctr.','.$r['id'].','.$child.','.$r['depth'].');');

	return true;
}

/**	Retourne l'ensemble des catégories parent de la catégorie passée en argument, triées par niveau décroissant.
 *
 *	@param int $id Identifiant de la catégorie dont on souhaite trouver les parents.
 *	@param int $limit Facultatif, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Facultatif, offset à partir duquel démarrer le résultat
 *	@param int $ctr Optionnel, identifiant d'un comparateur de prix
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant de la catégorie
 *			- name : la désignation de la catégorie
 *			- title : titre de la catégorie
 *			- depth : la profondeur de la catégorie dans l'arborescence
 *			- parent_id : l'identifiant de la catégorie parente
 *
 */
function ctr_categories_parents_get( $id, $limit=0, $offset=0, $ctr=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($limit) || $limit<=0 ) $limit = '18446744073709551615';
	if( !is_numeric($offset) || $offset<=0 ) $offset = 0;
	if( !is_numeric($ctr) || $ctr<0 ) return false;

	return ria_mysql_query('
		select cat_id as id, cat_name as name, ifnull(cat_title, cat_name) as title, cat_parent_depth as depth, ctr_categories.cat_parent_id as parent_id
		from ctr_cat_hierarchy, ctr_categories
		where ctr_cat_hierarchy.cat_parent_id=cat_id
			and cat_child_id='.$id.'
			'.( $ctr>0 ? ' and ctr_cat_hierarchy.cat_ctr_id='.$ctr.' and ctr_cat_hierarchy.cat_ctr_id=ctr_categories.cat_ctr_id' : '' ).'
		order by cat_parent_depth
		limit '.$offset.','.$limit.'
	');

}

/**	Retourne l'ensemble des catégories enfant de la catégorie passée en argument.
 *
 *	@param int $id Identifiant de la catégorie dont on souhaite trouver les parents.
 *	@param int $ctr Optionnel, identifiant d'un comparateur de prix
 *	@param int $limit Facultatif, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Facultatif, offset à partir duquel démarrer le résultat
 *	@param bool $publish Facultatif, catégories publiées uniquement
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant de la catégorie
 *			- ref : référence de la catégorie
 *			- name : la désignation de la catégorie
 *			- title : titre de la catégorie
 *			- parent_id : l'identifiant de la catégorie parente
 *
 */
function ctr_categories_child_get( $id, $ctr=0, $limit=0, $offset=0, $publish=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($limit) || $limit<=0 ) $limit = '18446744073709551615';
	if( !is_numeric($offset) || $offset<=0 ) $offset = 0;
	global $config;

	$sql = '
		select cat_id as id, cat_name as name, cat_ref as ref, ifnull(cat_title, cat_name) as title, ctr_categories.cat_parent_id as parent_id
		from ctr_categories
		where cat_parent_id='.$id.' '.( $ctr>0 ? ' and cat_ctr_id='.$ctr : '' ).'
			and cat_date_disabled is null
	';

	if( $publish ){
		if( ctr_categories_count($ctr, true)>0 )
			$sql .= ' and cat_id in (select cat_id from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].')';
	}

	$sql .= ' limit '.$offset.', '.$limit;

	return ria_mysql_query($sql);

}

/**	Retourne les catégories enfants et petits enfants d'une catégorie donnée.
 *	@param int $id Identifiant de la catégorie mère ou un tableau d'identifiant
 *	@param bool $publish Facultatif, catégories publiées uniquement
 *	@return array Un tableau des identifiants des catégories enfants
 *	@return bool False en cas d'échec
 */
function ctr_categories_childs_get( $id, $publish=false ){
	if( !is_array($id) && !ctr_categories_exists($id) )	return false;
	global $config;

	$sql = ' select cat_child_id as id
		from ctr_cat_hierarchy as hry
	';

	if( is_array($id) )
		$sql .= ' where cat_parent_id in ('.implode(',',$id).')';
	else
		$sql .= ' where cat_parent_id='.$id;

	if( $publish )
		$sql .= ' and cat_child_id in (select cat_id from ctr_cat_publish as cat where cat_tnt_id='.$config['tnt_id'].' and hry.cat_ctr_id=cat.cat_ctr_id)';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de savoir si une famille d'un comparateur à des sous-familles
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return bool true si la catégorie possède des enfants, false dans le cas contraire
 */
function ctr_categories_have_childs( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('
		select 1
		from ctr_cat_hierarchy
		where cat_ctr_id='.$ctr.'
			and cat_parent_id='.$cat.'
		limit 1
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de supprimer la hierarchie des catégories d'un comparateur de prix ou d'une place de marché
 *	@param int $ctr_id Identifiant d'un comparateur de prix ou d'une place de marché
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_categories_hierarchy_delete( $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		delete from ctr_cat_hierarchy
		where cat_ctr_id='.$ctr_id.'
	');
}

/**	Reconstruit la hierarchie complète des catégories d'un comparateur de prix donné. La reconstruction se fait de manière hiérarchique, en partant
 *	des catégories de premier niveau pour descendre vers les catégories les plus profondes.
 *	@param int $ctr Obligatoire Identifiant du comparateur de prix.
 *	@param int $parent Point de départ de la reconstruction. Pour une reconstruction complète, laisser vide.
 */
function ctr_categories_hierarchy_rebuild( $ctr, $parent=0 ){
	if( !ctr_comparators_exists($ctr) )	return false;

	// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
	if( $parent==0 ){
		ctr_categories_hierarchy_delete( $ctr );
	}

	// Reconstruit la hiérarchie pour toutes les catégories enfants d'un comparateur de prix donné
	$categories = ctr_categories_get( $ctr, 0, $parent, 0, false, '', false );

	while( $c = ria_mysql_fetch_array($categories) ){
		ctr_categories_hierarchy_add( $ctr, $c['parent'], $c['id'] );
		ctr_categories_hierarchy_rebuild( $ctr, $c['id'] );
	}

}

/** Cette fonction permet de dépublier une ou plusieurs catégories
 *	@param int $ctr Obligatoire, identifiant du comparateur
 *	@param array $ids Facultatif, identifiant des catégories à publier
 *	@param int $cat_visit Facultatif, à documenter
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ctr_categories_unpublish( $ctr, $ids=0, $cat_visit=0 ){
	if( !ctr_comparators_exists($ctr) )	return false;
	if( $ids && !is_array($ids) ) return false;
	global $config;

	if( $ids == 0 ){

		if( !ria_mysql_query('delete from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].' and cat_ctr_id='.$ctr) ){
			return false;
		}

	} else if( ctr_categories_count($ctr, true)==0 ){ // Lorsqu'il n'existe aucun filtre.
		if( !ria_mysql_query('
				insert into ctr_cat_publish( cat_tnt_id, cat_ctr_id, cat_id )
				select '.$config['tnt_id'].','.$ctr.',cat_id
				from ctr_categories
				where cat_ctr_id='.$ctr
			) ){
			return false;
		}
		ctr_categories_unpublish( $ctr, $ids, $cat_visit );

	} else {
		$sql = '
			delete from ctr_cat_publish
			where cat_tnt_id='.$config['tnt_id'].'
				and cat_ctr_id='.$ctr.'
		';

		if( !ria_mysql_query($sql) ){
			return false;
		}

		$sql = '
			insert into ctr_cat_publish
				( cat_tnt_id, cat_ctr_id, cat_id )
			select '.$config['tnt_id'].', '.$ctr.', cat_id
			from ctr_categories
			where cat_ctr_id='.$ctr.'
				and cat_id not in ('.implode(', ', $ids).')
				and cat_id not in (
					select cat_child_id
					from ctr_cat_hierarchy
					where cat_ctr_id='.$ctr.'
						and cat_parent_id in ('.implode( ',', $ids ).')
				)
		';

		if( !ria_mysql_query($sql) ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de publier une ou plusieurs famille de comparateur / place de marché
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur ou d'une place de marché
 *	@param int|array $cat_id Obligatoire, identifiant ou tableau d'identifiants de famille à publier
 */
function ctr_categories_publish( $ctr_id, $cat_id ){
	if( !ctr_comparators_exists($ctr_id) ){
		return false;
	}

	if( !is_array($cat_id) ){
		if( !is_numeric($cat_id) || $cat_id<=0 ){
			return false;
		}

		$cat_id = array( $cat_id );
	}else{
		if( !sizeof($cat_id) ){
			return false;
		}

		foreach( $cat_id as $cat ){
			if( !is_numeric($cat) || $cat<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		replace into ctr_cat_publish
			( cat_tnt_id, cat_ctr_id, cat_id )
		select '.$config['tnt_id'].', '.$ctr_id.', cat_id
		from ctr_categories
		where cat_ctr_id='.$ctr_id.'
			and (
				cat_id in ('.implode(', ', $cat_id).')

			)
	';

	return ria_mysql_query( $sql );
}

/**	Retourne les catégories enfants et petits enfants d'une catégorie donnée.
 *	Le résultat est retourné dans un tableau.
 *	@param int $id Identifiant de la catégorie mère ou un tableau d'identifiant
 *	@return array Un tableau des identifiants des catégories enfants
 *
 */
function ctr_categories_childs_get_array( $id ){
	$r_childs = ctr_categories_childs_get($id);
	$childs = array();

	while( $r = ria_mysql_fetch_array($r_childs) ){
		$childs[] = $r['id'];
	}

	return $childs;
}

/** Cette fonction permet de savoir si une categories est publiée.
 *	@param int $id Obligatoire, identifiant de la catégorie
 *	@return bool true si elle est publiée
 *	@return bool false dans le cas contraire
 */
function ctr_categories_is_published( $id ){
	if( !ctr_categories_exists($id) ) return false;
	global $config;
	return ria_mysql_num_rows(ria_mysql_query('select cat_id from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id.' limit 0,1'))>0;
}

/** Cette fonction permet d'ajouter un ancien coût par clic à une catégorie
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param string $date_start Obligatoire, date de début du coût par clic
 *	@param string $date_end Obligatoire, date de fin du coût par clic
 *	@param $cpc Obligatoire, montant du coût par clic
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire
 */
function ctr_categories_prices_add( $ctr_id, $cat_id, $date_start, $date_end, $cpc ){
	if( !ctr_comparators_exists($ctr_id) ){
		return false;
	}

	if( !ctr_categories_exists($cat_id) ){
		return false;
	}

	if( !isdateheure($date_start) || !isdateheure($date_end) ){
		return false;
	}

	if( !is_numeric($cpc) || $cpc<0 ){
		return false;
	}

	$date_start = dateheureparse( $date_start );
	$date_end   = dateheureparse( $date_end );

	$sql = '
		insert into ctr_categories_prices
			( prc_ctr_id, prc_cat_id, prc_cpc, prc_date_start, prc_date_end )
		values
			( '.$ctr_id.', '.$cat_id.', '.$cpc.', \''.$date_start.'\', \''.$date_end.'\' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les anciens coûts par clic d'une ou plusieurs catégories.
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur de prix
 *	@param int $cat_id Optionnel, identifiant d'une catégorie
 *	@return resource Un résultat MySQL contenant :
 *				- ctr_id : identifiant du comparateur de prix
 *				- cat_id : identifiant de la catégorie
 *				- date_start_en : date de début du CPC au format EN
 *				- date_end_en : date de début du CPC au format EN
 *				- date_start : date de début du CPC au format FR
 *				- date_end : date de début du CPC au format FR
 */
function ctr_categories_prices_get( $ctr_id=0, $cat_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<0 ){
		return false;
	}

	$sql = '
		select
			prc_ctr_id as ctr_id, prc_cat_id as cat_id, prc_cpc as cpc, prc_date_start as date_start_en, prc_date_end as date_end_en,
			date_format(prc_date_start,"%d/%m/%Y à %H:%i") as date_start, date_format(prc_date_end,"%d/%m/%Y à %H:%i") as date_end
		from ctr_categories_prices
		where 1
	';

	if( $ctr_id>0 ){
		$sql .= ' and prc_ctr_id='.$ctr_id;
	}

	if( $cat_id>0 ){
		$sql .= ' and prc_cat_id='.$cat_id;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour l'identifiant de famille d'export des catégories et des produits directement rattachées à la famille désactivée en paramètre.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur ou d'une place de marché
 *	@param int $cat Obligatoire, identifiant d'une famille désactivée
 *	@param $redirection Obligatoire, identifiant d'une famille servant de redirection
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_cat_redirections_update( $ctr, $cat, $redirection ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($redirection) || $redirection<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update ctr_prd_categories
		set cpc_cat_id='.$redirection.'
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_ctr_id='.$ctr.'
			and cpc_cat_id='.$cat.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		update ctr_catalogs
		set ctl_cat_id='.$redirection.'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_ctr_id='.$ctr.'
			and ctl_cat_id='.$cat.'
	';

	return ria_mysql_query( $sql );
}
 /// @}

/** \defgroup ctr_config Configuration d'un comparateur
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la configuration d'un comparateur de prix
 *	@{
 */

/** Cette fonction permet de récupérer les paramètre d'un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param $code Optionnel, code d'un paramètre ou tableau de code
 *	@return bool Retourne false si l'un des paramètres est faux
 *	@return array Retourne un tableau MySQL contenant :
 *				- code : code du paramètre
 *				- name : nom du paramètre
 *				- desc : description du paramètre
 *				- is_obligatory : si oui ou non le paramètre est obligatoire
 *				- fld : value du champs
 *				- type : type de données (lien vers fld_types)
 *				- params : Paramètres
 */
function ctr_params_get( $ctr, $code='' ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !is_array($code) && $code!='' && !ctr_params_exists($ctr, $code) ){
		return false;
	}

	if( is_array($code) && sizeof($code)<=0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}


	$sql = '
		select cpr_code as code, cpr_name as name, cpr_desc as "desc", cpr_is_obligatory as is_obligatory, cpf_fld_id as fld, cpr_type_id as type, cpr_params as params
		from ctr_params
			left join ctr_param_fields on (cpf_tnt_id='.$config['tnt_id'].' and cpf_ctr_id=cpr_ctr_id and cpf_cpr_code=cpr_code and '.$wst_id.'=cpf_wst_id)
		where cpr_ctr_id='.$ctr.'
	';

	if( is_array($code) )
		$sql .= ' and cpr_code in (\''.implode('\', \'', $code).'\')';
	elseif( trim($code)!='' )
		$sql .= ' and cpr_code=\''.$code.'\'';

	$sql .= ' order by cpr_pos';

	return ria_mysql_query( $sql );
}

/**	Cette fonction renvoie un tableau des paramètres nommés ($code => $valeur)
 *	@param int $ctr	Comparateur
 *	@param string $code Optionnel, code d'un paramètre ou tableau de code
 *	@return	array Tableau clé/valeur des paramètres, false si échec
 */
function ctr_params_get_array( $ctr, $code='' ){
	$rparams = ctr_params_get( $ctr, $code );
	if( !$rparams ){
		return false;
	}

	$params = array();
	while( $param = ria_mysql_fetch_assoc($rparams) ){
		$params[$param['code']] = $param['fld'];
	}

	return $params;
}

/** Cette fonction permet de récupérer la valeur saisie pour un paramètre.
 *	@param int $ctr Obligatoire, identifiant du comparateur de prix
 *	@param string $code Obligatoire, code du paramètre
 *	@param int $prd Optionnel, identifiant du produit sur lequel on souhaite récupérer la valeur
 *	@return string La valeur saisie pour un paramètre
 */
function ctr_params_get_value( $ctr, $code, $prd=0 ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !trim($code) ){
		return false;
	}

	if( !is_numeric($prd) || $prd < 0 ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}


	$sql = '
		select cpf_fld_id as val, cpr_type_id as type
		from ctr_param_fields
			join ctr_params on (cpr_ctr_id=cpf_ctr_id and cpr_code=cpf_cpr_code)
		where cpf_tnt_id='.$config['tnt_id'].'
			and cpf_wst_id='.$wst_id.'
			and cpf_ctr_id='.$ctr.'
			and cpf_cpr_code=\''.addslashes($code).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$value = ria_mysql_result( $res, 0, 'val' );
	$type = ria_mysql_result( $res, 0, 'type' );

	if( $type==1 && $prd > 0 ){
		$value = fld_object_values_get( $prd, $value );
	}

	return $value;
}

/** Cette fonction permet de tester l'existance d'un paramètre pour un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $code Obligatoire, code d'un paramètre
 *	@return bool Retourne true si le code paramètre exists
 *	@return bool Retourne false dans le cas contraire
 */
function ctr_params_exists( $ctr, $code ){
	if( !ctr_comparators_exists($ctr) ) return false;
	if (trim($code)=='' ) return false;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from ctr_params where cpr_ctr_id='.$ctr.' and cpr_code=\''.$code.'\'') )>0;
}

/** Cette fonction permet d'ajouter un lien entre un paramètre et une valeur (qui peut être un identifiant de champ avancé)
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $code Obligatoire, code d'un paramètre
 *	@param string $val Obligatoire, valeur de l'information
 *	@return bool Retourne false si l'un des paramètres est omis ou faux
 *	@return bool Retourne true si l'ajout a correctement fonctionné
 */
function ctr_param_fields_add( $ctr, $code, $val ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !ctr_params_exists($ctr, $code) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}


	if( ctr_param_fields_exists($ctr, $code) ){
		return ctr_param_fields_update( $ctr, $code, $val );
	}

	$add = '
		insert into ctr_param_fields
			( cpf_tnt_id, cpf_wst_id, cpf_ctr_id, cpf_cpr_code, cpf_fld_id )
		values
			( '.$config['tnt_id'].', '.$wst_id.', '.$ctr.', \''.$code.'\', \''.addslashes($val).'\' )
	';

	$res = ria_mysql_query( $add );
	if( !$res ){
		return false;
	}

	if( $code == 'CDISCOUNT_CATALOG' ){
		$actived = ctr_comparators_actived( $ctr );

		// S'il s'agit d'un partenaire CDiscount, on contrôle qu'il est activé sur CDiscount
		$partner_CDiscount = ctr_cdiscount_get_partners( $ctr );
		$is_partnerCDiscount = is_array($partner_CDiscount) && sizeof($partner_CDiscount);

		if( $is_partnerCDiscount ){
			$used = ctr_cdiscount_get_partners_used();

			if( !in_array($partner_CDiscount['id'], array_keys( $used )) ){
				$actived = false;
			}
		}

		// S'il s'agit de l'activation d'un partenaire CDiscount utilisant le même catalogue, on réexporte tous les produits
		if( $actived && $is_partnerCDiscount ){
			return ctr_cdiscount_import_partner_catalog( $ctr );
		}
	}

	return true;
}

/** Cette fonction permet de mettre à jour la valeur rattachée à un paramètre d'un comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $code Obligatoire, code de paramètre
 *	@param string $val Obligatoire, valeur de l'information
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_param_fields_update( $ctr, $code, $val ){
	if( !ctr_param_fields_exists($ctr, $code) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}


	$update = '
		update ctr_param_fields
		set cpf_fld_id=\''.addslashes($val).'\'
		where cpf_tnt_id='.$config['tnt_id'].'
			and cpf_ctr_id='.$ctr.'
			and cpf_cpr_code=\''.$code.'\'
			and cpf_wst_id='.$wst_id.'
	';

	$res = ria_mysql_query( $update );
	if( !$res ){
		return false;
	}

	if( $code == 'CDISCOUNT_CATALOG' ){
		$actived = ctr_comparators_actived( $ctr );

		// S'il s'agit d'un partenaire CDiscount, on contrôle qu'il est activé sur CDiscount
		$partner_CDiscount = ctr_cdiscount_get_partners( $ctr );
		$is_partnerCDiscount = is_array($partner_CDiscount) && sizeof($partner_CDiscount);

		if( $is_partnerCDiscount ){
			$used = ctr_cdiscount_get_partners_used();

			if( !in_array($partner_CDiscount['id'], array_keys( $used )) ){
				$actived = false;
			}
		}

		// S'il s'agit de l'activation d'un partenaire CDiscount utilisant le même catalogue, on réexporte tous les produits
		if( $actived && $is_partnerCDiscount ){
			return ctr_cdiscount_import_partner_catalog( $ctr );
		}
	}

	return true;
}

/** Cette fonction permet de tester si un paramètre est déjà rattaché à une valeur (qui peut être un identifiant de champ avancé)
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $code Obligatoire, code d'un paramètre
 *	@return bool Retourne true si le code est déjà rattaché à une valeur (qui peut être un identifiant de champ avancé)
 *	@return bool Retourne false si l'un des paramètres est omis ou faux ou bien si le code n'est pas rattaché à une valeur
 */
function ctr_param_fields_exists( $ctr, $code ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !ctr_params_exists($ctr, $code) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	return ria_mysql_num_rows( ria_mysql_query('
		select 1
		from ctr_param_fields
		where cpf_tnt_id='.$config['tnt_id'].'
			and cpf_wst_id='.$wst_id.'
			and cpf_ctr_id='.$ctr.'
			and cpf_cpr_code=\''.$code.'\'
	') )>0;
}

/** Cette fonction permet de supprimer un lien entre un paramètre de comparateur de prix et une valeur (qui peut être un identifiant de champ avancé)
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param string $code Obligatoire, code d'un paramètre
 *	@return bool Retourne false si la suppression a échouée
 *	@return bool Retourne true si le lien n'existe pas ou si la suppression s'est correctement déroulée
 */
function ctr_param_fields_delete( $ctr, $code ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !ctr_params_exists($ctr, $code) ){
		return false;
	}

	if( !ctr_param_fields_exists($ctr, $code) ){
		return true;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$delete = '
		delete from ctr_param_fields
		where cpf_tnt_id='.$config['tnt_id'].'
			and cpf_wst_id='.$wst_id.'
			and cpf_ctr_id='.$ctr.'
			and cpf_cpr_code=\''.$code.'\'
	';

	return ria_mysql_query( $delete );
}

/** Cette fonction permet de récupérer les paramètres utilisés par un comparateur de prix avec la valeur affecté à un produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool Retourne false si l'un des paramètres est omis ou faux
 *	@return array Retourne un tableau MySQL contenant :
 *				- code : code du paramètre
 *				- valeur : valeur pour le produt passé en paramètre
 */
function ctr_param_fields_get_values( $ctr, $prd ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	if( !prd_products_exists($prd) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$fields = '
		select cpr_code as code, pv_value as value
		from ctr_param_fields
			join ctr_params on (cpf_cpr_code=cpr_code and cpf_ctr_id=cpr_ctr_id)
			join fld_fields on (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].')
			join fld_object_values on (pv_tnt_id='.$config['tnt_id'].')
		where cpf_tnt_id='.$config['tnt_id'].'
			and cpf_wst_id='.$wst_id.'
			and fld_id=cpf_fld_id
			and pv_fld_id=cpf_fld_id
			and cpf_ctr_id='.$ctr.'
			and pv_obj_id_0='.$prd.'
			and fld_cls_id=1
	';

	return ria_mysql_query( $fields );
}


/** Cette fonction permet de vérifier l'existance des catégories importées et de supprimer celles qui n'existent plus dans l'arborescence des produits.
 *	@param int $ctr Obligatoire, Identifiant du comparateur de prix
 *	@param bool $return_tbl Facultatif, si true retourne un tableau des identifiants de catégories supprimés. Si false, retourne true dans tous les cas.
 *	@return bool true dans le cas où toutes les catégories exportées existent
 *	@return array un tableau d'identifiants comprenant l'ensemble des catégories importées qui ont été supprimées
 *	@return bool false dans le cas contraire
 */
function ctr_catalogs_verify( $ctr, $return_tbl=false ){
	if( !ctr_comparators_exists($ctr) ){
		return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	// Récupère les catégories déjà exportées et qui n'existent plus dans les catégories de produits
	$cpc_cats = ria_mysql_query('
		select cpc_prd_cat
		from ctr_prd_categories
			join prd_categories on ( cpc_tnt_id=cat_tnt_id and cpc_prd_cat=cat_id )
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cat_date_deleted is not null
	');

	if( !$cpc_cats )
		return false;

	// On supprime toutes les catégories qui n'existent plus dans le comparateur ainsi que les produits sous ces catégories
	$cat_del = array();
	while( $cpc_cat = ria_mysql_fetch_array($cpc_cats)){
		if( ctr_prd_categories_del($ctr , $cpc_cat['cpc_prd_cat']) ){
			$cat_del[] = $cpc_cat['cpc_prd_cat'];
		}
	}

	return $return_tbl ? $cat_del : true;
}

 /// @}

/** \defgroup ctr_auctions Gestion des enchères sur les familles de comparateur de prix
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des enchère
 *	@{
 */

/** Cette fonction permet de récupérer l'enchère personnalisée sur une ou plusieurs familles.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Optionnel, identifiant ou tableau d'identifiants d'un comparateur de prix
 *	@return bool false en cas de problème, sinon un résultat MySQL contenant :
 *				- cat : identifiant d'une catégorie
 *				- amount : montant de l'enchère personnalisé
 */
function ctr_cat_auctions_get( $ctr, $cat=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( $cat!=0 ){
		if( is_array($cat) && !sizeof($cat) ) return false;
		if( is_numeric($cat) && $cat<0 ) return false;

		if( is_numeric($cat) )
			$cat = array( $cat );

		if( is_array($cat) ){
			foreach( $cat as $c ){
				if( !is_numeric($c) || $c<=0 )
					return false;
			}
		}
	}
	global $config;

	$sql = '
		select cta_cat_id as cat, cta_amount as amount
		from ctr_cat_auctions
		where cta_tnt_id='.$config['tnt_id'].'
			and cta_ctr_id='.$ctr.'
	';

	if( is_array($cat) && sizeof($cat) )
		$sql .= ' and cta_cat_id in ('.implode( ',', $cat ).')';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier si une enchère personnalisée est déjà en place sur une famille de comparateur.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@return bool true si l'enchère personnalisée est en place, false dans le cas contraire
 */
function ctr_cat_auctions_exists( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$sql = '
		select 1
		from ctr_cat_auctions
		where cta_tnt_id='.$config['tnt_id'].'
			and cta_ctr_id='.$ctr.'
			and cta_cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet d'ajout une enchère personnalisée sur une famille de comparateur
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param int $amount Obligatoire, montant de l'enchère
 *	@return bool retourne false en cas d'échec d'ajout, true dans le cas contraire
 */
function ctr_cat_auctions_add( $ctr, $cat, $amount ){
	if( ctr_cat_auctions_exists($ctr, $cat) )
		return ctr_cat_auctions_update( $ctr, $cat, $amount );
	if( !ctr_comparators_exists($ctr) ) return false;
	if( !ctr_categories_exists($cat) ) return false;
	if( !is_numeric($amount) || $amount<=0 ) return false;
	global $config;

	$sql = '
		insert into ctr_cat_auctions
			( cta_tnt_id, cta_ctr_id, cta_cat_id, cta_amount )
		values
			( '.$config['tnt_id'].', '.$ctr.', '.$cat.', '.$amount.' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour l'enchère personnalisée sur une famille de comparateur de prix
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param int $amount Obligatoire, montant de l'enchère, si le montant est à 0 la ligne est supprimée
 *	@return bool false en cas d'échec de la mise à jour, true dans le cas contraire
 */
function ctr_cat_auctions_update( $ctr, $cat, $amount ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	if( !is_numeric($amount) || $amount<0 ) return false;

	if( $amount==0 ){
		return ctr_cat_auctions_del( $ctr, $cat );
	}

	global $config;

	$sql = '
		update ctr_cat_auctions
		set cta_amount='.$amount.'
		where cta_tnt_id='.$config['tnt_id'].'
			and cta_ctr_id='.$ctr.'
			and cta_cat_id='.$cat.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une enchère sur une famille.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une de ses familles
 *	@return bool false en cas d'échec de suppression, true dans le cas contraire
 */
function ctr_cat_auctions_del( $ctr, $cat ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$sql = '
		delete from ctr_cat_auctions
		where cta_tnt_id='.$config['tnt_id'].'
			and cta_ctr_id='.$ctr.'
			and cta_cat_id='.$ctr.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour une enchère personnalisée directement sur l'export d'une catégorie produit
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Obligatoire, identifiant d'une catéorie produit
 *	@param int $amount Optionnel, montant de l'enchère, laissé vide pour supprimer la personnalisation
 *	@return bool false en cas d'échec de la mise à jour, true dans le cas contraire
 */
function ctr_prd_categories_set_auctions( $ctr, $cat, $amount=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !ctr_prd_categories_exists($ctr, $cat) ){
		return ctr_prd_categories_add( $ctr, $cat, 0, $amount );
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$amount = !is_numeric($amount) || $amount<0 ? 'null' : $amount;

	$sql = '
		update ctr_prd_categories
		set cpc_amount = '.$amount.'
		where cpc_tnt_id='.$config['tnt_id'].'
			and cpc_wst_id='.$wst_id.'
			and cpc_ctr_id='.$ctr.'
			and cpc_prd_cat='.$cat.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour la famille d'export d'une catégorie de produits.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / place de marché
 *	@param int $fml_id Obligatoire, identifiant d'une famille
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie de produit
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_prd_categories_set_family( $ctr_id, $fml_id, $cat_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
	    return false;
	}

	if( !ctr_categories_exists($fml_id, $ctr_id) ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<=0 ){
	    return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		update ctr_prd_categories
		set cpc_cat_id = '.$fml_id.'
		where cpc_tnt_id = '.$config['tnt_id'].'
			and cpc_wst_id = '.$wst_id.'
			and cpc_ctr_id = '.$ctr_id.'
			and cpc_prd_cat = '.$cat_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour les paramètres de configuration propre à une catégories.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / place de marché
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie de produit
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *	@param string $params Optionnel, configuration à sauvegarder (au format JSON)
 *	@return bool true si la mise à jour s'est correctement déroulée, false dans le cas contraire
 */
function ctr_prd_categories_set_params( $ctr_id, $cat_id, $params='', $wst_id=0 ){
	global $config;

	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id <= 0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	// Si la catégorie n'est pas présentes dans celle inclut, on la rajoute
	if( !ctr_prd_categories_exists($ctr_id, $cat_id) ){
		ctr_prd_categories_add( $ctr_id, $cat_id );
	}

	$sql = '
		update ctr_prd_categories
		set cpc_params = '.( trim($params) != '' ? '"'.addslashes( $params ).'"' : 'null' ).'
		where cpc_tnt_id = '.$config['tnt_id'].'
			and cpc_ctr_id = '.$ctr_id.'
			and cpc_prd_cat = '.$cat_id.'
	';

	if( $wst_id > 0 ){
		$sql .= ' and cpc_wst_id = '.$wst_id;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour une enchère personnalisée directement sur le produit.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $amount Obligatoire, montant de l'enchère, laissé vide pour supprimer la personnalisation
 *	@return bool false en cas d'échéc de la mise à jour, true dans le cas contraire
 */
function ctr_catalogs_set_auctions( $ctr, $prd, $amount ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !ctr_catalogs_exists($ctr, $prd) ){
		return ctr_catalogs_add( $ctr, $prd, 0, false, false, null, $amount );
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr) */ ){
		$wst_id = $config['wst_id'];
	}

	$amount = !is_numeric($amount) || $amount<0 ? 'null' : $amount;

	$sql = '
		update ctr_catalogs
		set ctl_amount = '.$amount.'
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_wst_id='.$wst_id.'
			and ctl_ctr_id='.$ctr.'
			and ctl_prd_id='.$prd.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour la famille d'export d'un produit.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix / place de marché
 *	@param int $fml_id Obligatoire, identifiant d'une famille
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_catalogs_set_family( $ctr_id, $fml_id, $prd_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
	    return false;
	}

	if( !ctr_categories_exists($fml_id, $ctr_id) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
	    return false;
	}

	global $config;

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || ctr_comparators_is_marketplace($ctr_id) */ ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		update ctr_catalogs
		set ctl_cat_id = '.$fml_id.'
		where ctl_tnt_id = '.$config['tnt_id'].'
			and ctl_wst_id = '.$wst_id.'
			and ctl_ctr_id = '.$ctr_id.'
			and ctl_prd_id = '.$prd_id.'
	';

	return ria_mysql_query( $sql );
}

 /// @}

/** \defgroup ctr_conditions Gestion des conditions de recherches de produits non exportés
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des filtres de recherches spécifiques aux comparateurs de prix
 *	@{
 */

/** Cette fonction permet de récupérer une ou plusieurs conditions de recherche.
 *	@param int $ccd Optionnel, identifiant d'une condition
 *	@param string $code Optionnel, code d'une condition
 *	@param bool|array $exclude Optionnel, permet d'exclure certaines conditions grâce à leur code
 *	@return bool false si le paramètre est faux, sinon un résultat MySQL contenant :
 *				- id : identifiant de la condition
 *				- code : code unique de la condition
 *				- name : nom de la condition
 *				- desc : description de la condition
 *				- type : type de données attendues par la condition (booléen, entier, chiffres à virgules
 */
function ctr_conditions_get( $ccd=0, $code='', $exclude=false ){
	if( !is_numeric($ccd) || $ccd<0 ) return false;

	$sql = '
		select ccd_id as id, ccd_code as code, ccd_name as name, ccd_desc as "desc", ccd_type_id as type
		from ctr_conditions
		where 1
	';

	if( $ccd>0 ) $sql .= ' and ccd_id='.$ccd;
	if( trim($code) ) $sql .= ' and ccd_code=\''.addslashes( $code ).'\'';
	if( is_array($exclude) && sizeof($exclude) ) $sql .= ' and ccd_code not in (\''.implode('\', \'', $exclude).'\')';

	$sql .= ' order by ccd_name';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier si une condition existe.
 *	@param int $ccd Optionnel, identifiant d'une condition
 *	@param string $code Optionnel, code d'une condition
 *	@return bool false si elle n'existe pas ou si aucun paramètre n'est fourni, true dans le cas contraire
 */
function ctr_conditions_exists( $ccd=0, $code='' ){
	if( !is_numeric($ccd) || $ccd<0 ) return false;
	if( $ccd<=0 && !trim($code) ) return false;

	$sql = '
		select 1
		from ctr_conditions
		where 1
	';

	if( $ccd>0 )
		$sql .= ' and ccd_id='.$ccd;
	if( trim($code) )
		$sql .= ' and ccd_code=\''.addslashes( $code ).'\'';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}
/** Cette fonction permet de récupérer les symboles pouvant être utilisées pour une condition.
 *	@param int $ccd Obligatoire, identifiant d'une condition
 *	@return bool false si le paramètre est omis ou faux, sinon un résultat MySQL contenant :
 *				- symbol : code du symbole
 *				- desc : symbole en toute lettre
 */
function ctr_conditions_get_symbols( $ccd ){
	if( !is_numeric($ccd) || $ccd<=0 ) return false;
	global $config;

	$sql = '
		select psy_symbol as symbol, psy_desc as "desc"
		from prc_symbols
			join prc_symbol_types on  psy_symbol=pst_symbol
			join ctr_conditions on pst_type_id=ccd_type_id
		where ccd_id='.$ccd.'
		order by psy_pos
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer le type de données attendues pour une condition.
 *	@param string $code Obligatoire, code d'une condition
 *	@return bool false si le code ne corresponds à aucune condition, sinon le type de données attendues
 */
function ctr_conditions_get_type_bycode( $code ){
	if( !trim($code) ) return false;

	$sql = '
		select ccd_type_id as type
		from ctr_conditions
		where ccd_code=\''.addslashes( $code ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'type' );
}

/** Cette fonction retour l'identifiant d'une condition selon le code passé en paramètre.
 *	@param string $code Obligatoire, code pour lequel on recherche l'identifiant de condition
 *	@return bool false si la condition n'existe pas, sinon l'identifiant de la condition
 */
function ctr_conditions_get_id_bycode( $code ){
	if( !trim($code) ) return false;

	$sql = '
		select ccd_id as id
		from ctr_conditions
		where ccd_code=\''.addslashes( $code ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'id' );
}

/** Cette fonction retourne un tableau des symboles valides pour une condition
 *	@param int $ccd Obligatoire, identifiant d'une condition
 *	@return bool false si le paramètre est omis ou faux, sinon un tableau contenant les symboles valides
 */
function ctr_conditions_get_array_symbols( $ccd ){
	if( !is_numeric($ccd) || $ccd<=0 )return false;

	$rsbl = ctr_conditions_get_symbols( $ccd );
	if( !$rsbl )
		return array();

	$ar_symbols = array();
	while( $sbl = ria_mysql_fetch_array($rsbl) )
		$ar_symbols[] = $sbl['symbol'];

	return $ar_symbols;
}

/** Cette fonction permet de sauvegarder un filtre de recherche.
 *	@param string $name Obligatoire, nom du filtre
 *	@param string $desc Optionnel, description du filtre
 *	@param array $conditions Optionnel, conditions requises
 *	@param bool $marketplace Optionnel, indique si place de marché
 *	@return bool false en cas d'échec d'ajout sinon l'identifiant du nouveau filtre
 */
function ctr_filters_add( $name, $desc='', $conditions=false, $marketplace=false ){
	if( !trim($name) ) return false;
	global $config;

	$desc = trim($desc) ? '\''.addslashes( $desc ).'\'' : 'null';
	$usr = isset($_SESSION['usr_id']) && $_SESSION['usr_id'] ? $_SESSION['usr_id'] : 'null';

	$sql = '
		insert into ctr_filters
			( flt_tnt_id, flt_name, flt_comment, flt_usr_id, flt_marketplace )
		values
			( '.$config['tnt_id'].', \''.addslashes($name).'\', '.$desc.', '.$usr.', '.($marketplace ? 1 : 0).' )
	';

	if( !ria_mysql_query($sql) )
		return false;

	$id = ria_mysql_insert_id();

	if( is_array($conditions) && sizeof($conditions) ){
		ctr_filters_conditions_add( $id, $conditions );
	}

	return $id;
}

/** Cette fonction permet de récupérer tous les filtres de recherche sauvegardés.
 *	@param int $flt Optionnel, identifiant d'un filtre
 *	@param bool $marketplace Optionnel, filtre sur place de marché (true = place de marché, false = pas place de marché, null = indifférent)
 *	@return bool false en cas d'échec de récupération, sinon un résultat MySQL contenant :
 *				- id : Identiifnat du filtre
 *				- name : Nom du filtre
 *				- comment : commentaire fait sur le filtre
 *				- usr : identifiant utilisateur auquel le filtre appartient
 */
function ctr_filters_get( $flt=0, $marketplace=null ){
	if( !is_numeric($flt) || $flt<0 ) return false;
	global $config;

	$sql = '
		select flt_id as id, flt_name as name, flt_comment as comment, flt_usr_id as usr
		from ctr_filters
		where flt_tnt_id='.$config['tnt_id'].'
	';

	if ($marketplace !== null) $sql .= ' and flt_marketplace = ' . ($marketplace ? 1 : 0);

	if( $flt>0 )
		$sql .= ' and flt_id='.$flt;

	if( isset($config['ctr_filter_perso'], $_SESSION['usr_id']) && $config['ctr_filter_perso'] && $_SESSION['usr_id'] )
		$sql .= ' and (flt_usr_id is null or flt_usr_id='.$_SESSION['usr_id'].')';


	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier si un filtre existe bien
 *	@param int $flt Obligatoire, identifiant d'un filtre
 *	@param int $usr Optionnel, identifiant d'un utilisateur : si il est fourni, on vérifie en plus qu'il appartient à l'utilisateur en question
 *	@return bool false si le filtre n'existe pas, true dans le cas contraire
 */
function ctr_filters_exists( $flt, $usr=0 ){
	if( !is_numeric($flt) || $flt<=0 ) return false;
	if( !is_numeric($usr) || $usr<0 ) return false;
	global $config;

	$sql = '
		select 1
		from ctr_filters
		where flt_tnt_id='.$config['tnt_id'].'
			and flt_id='.$flt.'
	';

	if( $usr>0 ) $sql .= ' and flt_usr_id='.$usr;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de supprimer un filtre de recherche. Il est impossible de supprimer un filtre appartenant à un autre administrateur.
 *	@param int $flt Obligatoire, identifiant d'un filtre
 *	@return bool false en cas d'échec de suppression, true dans le cas contraire
 */
function ctr_filters_del( $flt ){
	if( !is_numeric($flt) || $flt<=0 ) return false;
	global $config;

	$usr = isset($_SESSION['usr_id']) && $_SESSION['usr_id'] ? $_SESSION['usr_id'] : 0;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !ctr_filters_exists($flt, $usr) ) return false;

	$sql = '
		delete from ctr_filters_conditions
		where cfc_tnt_id='.$config['tnt_id'].' and cfc_flt_id='.$flt.'
	';

	if( !ria_mysql_query($sql) )
		return false;

	$sql = '
		delete from ctr_filters
		where flt_tnt_id='.$config['tnt_id'].' and flt_id='.$flt.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'ajouter les conditions d'un filtre de recherche.
 *	@param int $flt Obligatoire, identifiant d'un filtre
 *	@param array $conditions Obligatoire, tableau contenant les conditions sous cette forme (pour chaque condition) array( 'id'=>ccd_id, 'symbol'=>psy_symbol, 'value'=>'%VALEUR%')
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ctr_filters_conditions_add( $flt, $conditions ){
	if( !ctr_filters_exists($flt) ) return false;
	if( !is_array($conditions) || !sizeof($conditions) ) return false;
	global $config;

	// vérification des données présentes dans le tableau des conditions
	foreach( $conditions as $cdt ){
		if( !is_array($cdt) || !sizeof($cdt) ) return false;
		if( !isset($cdt['id']) || !ctr_conditions_exists($cdt['id']) ) return false;
		if( !isset($cdt['symbol']) || !prc_symbols_exists($cdt['symbol']) ) return false;
		if( !isset($cdt['value']) || trim($cdt['value'])=='' ) return false;
	}

	$sql = '
		insert into ctr_filters_conditions
			( cfc_tnt_id, cfc_flt_id, cfc_ccd_id, cfc_psy_symbol, cfc_value )
		value
	';

	$count = 1;
	$last = sizeof( $conditions );
	foreach( $conditions as $cdt ){
		$line = '
			( '.$config['tnt_id'].', '.$flt.', '.$cdt['id'].', \''.addslashes( $cdt['symbol'] ).'\', \''.addslashes( $cdt['value'] ).'\' )
		';

		$sql .= $line.( $count!=$last ? ',' : '' );
		$count++;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les conditions applicables à un filtre de recherche.
 *	@param int $flt Obligatoire, identifiant d'un filtre
 *	@return bool false si le paramètre est omis ou incorecte sinon un résultat MySQL contenant :
 *				- id : identifiant de la condition
 *				- code : code de la condition
 *				- symbol : symbole utilisé
 *				- value : valeur de restriction
 */
function ctr_filters_conditions_get( $flt ){
	if( !is_numeric($flt) || $flt<=0 ) return false;
	global $config;

	$sql = '
		select cfc_ccd_id as id, cfc_psy_symbol as symbol, cfc_value as value, ccd_code as code
		from ctr_filters_conditions
			join ctr_conditions on cfc_ccd_id=ccd_id
		where cfc_tnt_id='.$config['tnt_id'].'
			and cfc_flt_id='.$flt.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de comparer deux valeurs selon un symbol
 *	@param string $val Obligatoire, valeur à comparer
 *	@param string $symbol Obligatoire, symbole utilisé pour la comparaison
 *	@param string $val_test Obligatoire, valeur de comparaison
 */
function ctr_symbols_exec( $val, $symbol, $val_test ){
	if( !in_array($symbol, array('<', '<=', '=', '>=', '>', '!=')) ) return false;
	if( trim($val)=='' || trim($val_test)=='' ) return false;
	if( in_array($symbol, array('<', '<=', '=', '>=', '>')) ){
		$val = str_replace( ',', '.', $val );
		$val_test = str_replace( ',', '.', $val_test);

		if( !is_numeric($val) || !is_numeric($val_test) ){
			return false;
		}
	}

	$res = false;
	switch( $symbol ){
		case '<' :
			$res = $val<$val_test ? true : false;
			break;
		case '<=' :
			$res = $val<=$val_test ? true : false;
			break;
		case '=' :
			$res = $val==$val_test ? true : false;
			break;
		case '>=' :
			$res = $val>=$val_test ? true : false;
			break;
		case '>' :
			$res = $val>$val_test ? true : false;
			break;
		case '!=' :
			$res = $val!=$val_test ? true : false;
			break;
	}

	return $res;

}
/** Cette fonction permet de lancer une recherche de produit par rapport au filtre.
 *
 *	@param int $ctr Optionnel, identifiant d'un comparateur de prix
 *	@param int $cat Optionnel, identifiant d'une catégorie de produits
 *	@param string $date_start Optionnel, date de début pour la prise en compte des statistiques
 *	@param string $date_end Optionnel, date de fin pour la prise en compte des statistiques
 *	@param array $conditions Optionnel, tableau contenant les conditions sous cette forme (pour chaque condition) array( 'code'=>ccd_code, 'symbol'=>psy_symbol, 'value'=>'%VALEUR%')
 *	@param bool $marketplace Optionnel, si true seules les places de marché sont recherchées, si false seuls les comparateurs de prix sont recherchés
 *
 *	@return bool|resource false si le premier paramètre est omis ou faux, sinon le résultats MySQL contenant :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- title : titre du produit
 *			- name : nom du produit
 *			- desc : description longue du produit
 *			- click : nombre de clics générés par le produit
 *			- cost : coût total des clics générés par le produit
 *			- sales : nombre de ventes générées par le produit
 *			- ca : chiffre d'affaires généré par le produit
 *			- ca_ttc : chiffre d'affaires TTC généré par le produit
 */
function ctr_search_products( $ctr=0, $cat=0, $date_start=false, $date_end=false, $conditions=false, $marketplace=false ){
	if( !is_numeric($ctr) || $ctr<0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;
	if( $date_start && !isdate($date_start) ) return false;
	if( $date_end && !isdate($date_end) ) return false;
	global $config;

	// vérifier le paramètre de l'identifiant du comparateur en récupérer le terme utilisé comme source
	$source = '';

	if( $ctr>0 ){
		$source = ctr_comparators_get_source( $ctr );
		if( !trim($source) ) return false;
		$source = array( $source );
	} else {
		$source = array();
		$rc = ctr_comparators_get( 0, true, true, $marketplace );
		if( !$rc || !ria_mysql_num_rows($rc) ){
			return false;
		}

		while( $c = ria_mysql_fetch_array($rc) ){
			if( trim($c['source']) ){
				$source[] = $c['source'];
			}
		}
	}

	if( !is_array($source) || !sizeof($source) ){
		return false;
	}

	$wst_id = false;
	if( isset($_SESSION['websitepicker']) ){
		if( is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ){
			$wst_id = $_SESSION['websitepicker'];
		}
	}

	if( !$wst_id /* || $marketplace || ($ctr>0 && ctr_comparators_is_marketplace($ctr)) */ ){
		$wst_id = $config['wst_id'];
	}

	$marketplace = $marketplace ? $marketplace : ctr_comparators_is_marketplace( $ctr );

	$rctr = ctr_comparators_get( $ctr, true, false, $marketplace );
	$count_ctr = $rctr ? ria_mysql_num_rows($rctr) : 0;

	// vérification des données présentes dans le tableau des conditions
	$sh_txt = '';
	$others_params = array( 'orderable'=>true, 'childs'=>true );
	$sh_sync = $sh_childsonly = null;
	$sh_refs = array();
	$sh_publish = $sh_stock = $sh_qte_stock = $sh_sales = $sh_click = $sh_cost = $sh_costsales = $sh_margin = $sh_section = $sh_price = false;
	$sh_export = -1;
	$ar_qte_stock = $ar_click = $ar_cost = $ar_sales = $ar_costsales = $ar_margin = $ar_price_ht = $ar_price_ttc = false;
	foreach( $conditions as $key=>$cdt ){
		if( !is_array($cdt) || !sizeof($cdt) ) return false;
		if( !isset($cdt['code']) || !ctr_conditions_exists(0, $cdt['code']) ) return false;
		if( !isset($cdt['symbol']) || !prc_symbols_exists($cdt['symbol']) ) return false;
		if( !isset($cdt['value']) || trim($cdt['value'])=='' ){
			unset($conditions[$key]);
		}

		switch( $cdt['code'] ){
			case 'CTR_SEARCH_PRD_CHILDS' :
				if( $cdt['value']!=-1 ){
					$sh_childsonly = $cdt['value'] ? true : false;
				}
				break;
			case 'CTR_SEARCH_IS_SYNC' :
				if( $cdt['value']!=-1 ){
					$sh_sync = $cdt['value'] ? true : false;
					$others_params['is_sync'] = $sh_sync;
				}
				break;
			case 'CTR_SEARCH_CATEGORIE' :
				if( is_numeric($cdt['value']) && prd_categories_exists( $cdt['value'] ) ){
					$sh_section = $cdt['value'];
				}
				break;
			case 'CTR_SEARCH_PRD_CONTENTS' :
				if( strlen(trim($cdt['value'])) > 2 ){
					$sh_txt .= trim($cdt['value']).' ';
				}
				break;
			case 'CTR_SEARCH_PUBLISH' :
				if( $cdt['value']!=-1 ){
					$sh_publish = $cdt['value'] ? true : false;
				}
				break;
			case 'CTR_SEARCH_STOCK' :
				if( $cdt['value']!=-1 ){
					$others_params['have_stock'] = $cdt['value'] ? true : false;
				}
				$sh_stock = true; break;
			case 'CTR_SEARCH_QTE_STOCK' :
				$ar_qte_stock = $cdt;
				$sh_qte_stock = true; break;
			case 'CTR_SEARCH_SALES' :
				$ar_sales = $cdt;
				$sh_sales = true; break;
			case 'CTR_SEARCH_CLICKS' :
				$ar_click = $cdt;
				$sh_click = true; break;
			case 'CTR_SEARCH_COST' :
				$ar_cost = $cdt;
				$sh_cost = true; break;
			case 'CTR_SEARCH_COSTSALES' :
				$ar_costsales = $cdt;
				$sh_costsales = true; break;
			case 'CTR_SEARCH_PRD_PRICEHT' :
				$ar_price_ht = $cdt;
				$sh_price = true;
				break;
			case 'CTR_SEARCH_PRD_PRICETTC' :
				$ar_price_ttc = $cdt;
				$sh_price = true;
				break;
			case 'CTR_SEARCH_MARGIN' :
				$ar_margin = $cdt;
				$sh_margin = true; break;
			case 'CTR_SEARCH_EXPORT' :
				$sh_export = $cdt['value'];
				break;
			case 'CTR_SEARCH_REFS' :
				foreach( explode("\r\n",$cdt['value']) as $line ){
					$lref = preg_split("/[\s\t]+/", trim($line));
					if( isset($lref[0]) ){
						$sh_refs[] = $lref[0];
					}
				}
				$dispo = prd_products_get_simple( 0, $sh_refs );
				$sh_refs = array();
				if( $dispo ){
					while( $d = ria_mysql_fetch_array($dispo) ){
						$sh_refs[]=$d['ref'];
					}
				}
				if( !sizeof($sh_refs) ) return false;
				break;
		}
	}

	// effectue la recherche avec le moteur de recherche interne.
	$prd_ids = array();
	if( trim($sh_txt) != '' ){
		$rsearch = search3(1, $sh_txt, 1, 0, $sh_publish, $sh_section, 6, array(2), true );
		if( !$rsearch ) return false;
		while( $sh = ria_mysql_fetch_array( $rsearch ) ){
			$prd_ids[] = $sh['tag'];
		}

		if( !sizeof($prd_ids) ){
			return array();
		}
	}

	$ar_export = array();
	// if( $sh_export!=-1 ){
		$resexport = ria_mysql_query('
			select ctl_prd_id as prd
			from ctr_catalogs
				join ctr_comparators on ( ctl_ctr_id=ctr_id )
			where ctl_tnt_id='.$config['tnt_id'].' and ctl_wst_id='.$wst_id.'
				and '.( $ctr>0 ? ' ctl_ctr_id='.$ctr : ' ctr_marketplace='.($marketplace ? 1 : 0) ).'
				and ctl_active=1
				'.( sizeof( $prd_ids ) > 0 ? 'and ctl_prd_id in( '.implode( ',', $prd_ids ).' )':'').'
		');

		if( $resexport ){
			while( $r = ria_mysql_fetch_array($resexport) ){
				$ar_export[] = $r['prd'];
			}
		}
	// }

	if( $sh_section ){
		$rprd = prd_products_get_simple( $prd_ids, (sizeof($sh_refs) ? $sh_refs : ''), $sh_publish, $sh_section, true, false, ($sh_price ? true : false ), false, $others_params );
	}else{
		$rprd = prd_products_get_simple( $prd_ids, (sizeof($sh_refs) ? $sh_refs : ''), $sh_publish, 0, false, false, ($sh_price ? true : false ), false, $others_params );
	}

	if( !$rprd || !ria_mysql_num_rows($rprd) )
		return false;


	$ar_prds = array();
	while( $prd = ria_mysql_fetch_array($rprd) ){
		if( is_array($ar_qte_stock) && sizeof($ar_qte_stock) ){
			if( !ctr_symbols_exec($prd['stock'], $ar_qte_stock['symbol'], $ar_qte_stock['value']) )
				continue;
		}

		if( $sh_sync !== null && ( ($sh_sync && !$prd['is_sync']) || (!$sh_sync && $prd['is_sync']) ) ) continue;
		if( $sh_childsonly !== null && ( ($sh_childsonly && !$prd['childonly']) || (!$sh_childsonly && $prd['childonly']) ) ) continue;

		if( $sh_export!=-1 ){
			if( ($sh_export && !in_array($prd['id'], $ar_export)) || (!$sh_export && in_array($prd['id'], $ar_export)) ){
				continue;
			}
		}

		$count = 0;
		if( in_array($prd['id'], $ar_export) ){
			$rcount = ria_mysql_query('
				select 1
				from ctr_catalogs
					join ctr_comparators on (ctl_ctr_id=ctr_id)
				where ctl_tnt_id='.$config['tnt_id'].'
					and ctl_wst_id='.$wst_id.'
					and ctl_prd_id='.$prd['id'].'
					and ctl_active=1
					and ctr_marketplace='.( $marketplace ? '1' : '0' ).'
					and ctr_publish=1
			');

			$count = $rcount ? ria_mysql_num_rows($rcount) : 0;
		}

		$ar_prds[ $prd['id'] ] = array(
			'id' => $prd['id'],
			'ref' => $prd['ref'],
			'name' => $prd['name'],
			'title' => $prd['title'],
			'desc' => $prd['desc-long'],
			'img_id' => $prd['img_id'],
			'is_sync' => $prd['is_sync'],
			'price_ht' => $sh_price ? $prd['price_ht'] : '',
			'price_ttc' => $sh_price ? $prd['price_ttc'] : '',
			'click' => 0,
			'cost' => 0,
			'costtotal' => 0,
			'sales' => 0,
			'ca' => 0,
			'ca_ttc' => 0,
			'margin' => 0,
			'qte' => 0,
			'roi' => 0,
			'transfo' => 0,
			'purchase_avg' => $prd['purchase_avg'],
			'export' => in_array($prd['id'], $ar_export) ? ($count>=$count_ctr ? true : 'mix') : false
		);
	}

	$sql = '
		select
			cct_prd_id as prd_id, count(*) as click,
			sum(ifnull((
				select prc_cpc
				from ctr_categories_prices
				where prc_ctr_id=cct_ctr_id
					and prc_cat_id=cct_cat_id
					and cct_datetime>=prc_date_start
					and cct_datetime<=prc_date_end
			), cat_cpc)) as cost
		from ctr_clicks
			join ctr_categories on (cct_cat_id=cat_id)
			join ctr_comparators on (cct_ctr_id=ctr_id)
		where cct_tnt_id='.$config['tnt_id'].'
			and cct_wst_id='.$wst_id.'
			and cct_prd_id in ('.implode( ',', array_keys($ar_prds) ).')
			and '.( $ctr>0 ? ' cct_ctr_id='.$ctr : ' ctr_marketplace='.($marketplace ? 1 : 0) ).'
	';

	if( $date_start!=false ){
		$sql .= ' and date(cct_datetime)>=\''.$date_start.'\'';
	}

	if( $date_end!=false ){
		$sql .= ' and date(cct_datetime)<=\''.$date_end.'\'';
	}

	$sql .= '
		group by cct_prd_id
	';

	$resclick = ria_mysql_query( $sql );
	if( $resclick ){
		while( $r = ria_mysql_fetch_array($resclick) ){
			$ar_prds[ $r['prd_id'] ]['click'] = $r['click'];
			$ar_prds[ $r['prd_id'] ]['cost'] = $r['cost'];
		}
	}

	if( $marketplace ){
		$sql = '
			select prd_id, count(ord_id) as sales, sum(ord_total_ht) as ca, sum(ord_total_ttc) as cattc, sum(purchase) as sum_purchase
			from (
				select prd_id, ord_id, ord_total_ht, ord_total_ttc,
				(
					select sum(if( ifnull( ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg), 0 )=0, 0, ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg)*prd_qte ))
					from prd_products as p2
						join ord_products as op2 on (op2.prd_tnt_id=p2.prd_tnt_id and op2.prd_id=p2.prd_id)
					where p2.prd_date_deleted is null
						and p2.prd_tnt_id=p.prd_tnt_id
						and op2.prd_ord_id=ord_id
				) as purchase
		';
	} else {
		$sql = '
			select prd_id,
				count(*) as sales,
				sum(ord_total_ht) as ca,
				sum(ord_total_ttc) as cattc,
				(
					select sum(if( ifnull( ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg), 0 )=0, 0, ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg)*prd_qte ))
					from prd_products as p2
					join ord_products as op on p2.prd_tnt_id=op.prd_tnt_id and p2.prd_id=op.prd_id
					where p2.prd_date_deleted is null
						and op.prd_ord_id=ord_id
						and p2.prd_tnt_id=ord_tnt_id
				) as sum_purchase
		';
	}

	$sql .= '
		from ord_orders
			join stats_origins on (stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id)
	';

	if( $marketplace ){
		$sql .= ' join ord_products as p on (prd_tnt_id=stats_tnt_id and prd_ord_id=ord_id)';
	}else{
		$sql .= ' join prd_products as p on (prd_tnt_id=stats_tnt_id and prd_ref=stats_term)';
	}

	$sql .= '
		where ord_tnt_id='.$config['tnt_id'].'
			and (ord_wst_id is null or ord_wst_id='.$wst_id.')
			and stats_source in (\''.implode('\', \'', $source).'\')
			and stats_cls_id='.CLS_ORDER.'
			and ord_state_id in ( '.implode( ', ', ord_states_get_ord_valid() ).')
			and prd_id in ('.implode( ',', array_keys($ar_prds) ).')
			and ord_parent_id is null
	';

	if( !$marketplace )
		$sql .= ' and prd_date_deleted is null';

	if( $date_start )
		$sql .= ' and date(ord_date)>=\''.$date_start.'\'';
	if( $date_end )
		$sql .= ' and date(ord_date)<=\''.$date_end.'\'';

	if( $cat>0 ) {
		$sql .= '
			and (exists(
				select 1
				from prd_classify
				where cly_prd_id=prd_id
					and cly_tnt_id = '.$config['tnt_id'].'
					and cly_cat_id='.$cat.'
			))
		';
	}

	if( $prd>0 )
		$sql .= ' and prd_id='.$prd;

	if( $marketplace ){
		$sql .= '
			) as results
		';
	}

	$sql .= '
		group by prd_id
	';

	$ressales = ria_mysql_query( $sql );

	$t = array();
	if( $ressales ){
		while( $r = ria_mysql_fetch_array($ressales) ){
			$ar_prds[ $r['prd_id'] ]['sales'] = $r['sales'];
			$ar_prds[ $r['prd_id'] ]['ca'] = $r['ca'];
			$ar_prds[ $r['prd_id'] ]['ca_ttc'] = $r['cattc'];

			$cost = $r['sum_purchase'] + $ar_prds[ $r['prd_id'] ]['cost'];
			$margin = $r['ca'] - $cost;

			$ar_prds[ $r['prd_id'] ]['margin'] = $margin;
			$ar_prds[ $r['prd_id'] ]['costtotal'] = $cost;

			$cost = $cost>0 ? $cost : 1;
			$ar_prds[ $r['prd_id'] ]['roi'] = $ar_prds[ $r['prd_id'] ]['margin'] / $cost * 100;

			$cliks = $ar_prds[ $r['prd_id'] ]['click']>0 ? $ar_prds[ $r['prd_id'] ]['click'] : 1;
			$ar_prds[ $r['prd_id'] ]['transfo'] = ($ar_prds[ $r['prd_id'] ]['sales'] / $cliks) * 100;

		}

	}

	foreach( $ar_prds as $prd=>$info ){
			$cost = $info['costtotal'];
			$margin = $info['margin'];

			$r = true;
			if( is_array($ar_click) && sizeof($ar_click) ){
				if( !ctr_symbols_exec($info['click'], $ar_click['symbol'], $ar_click['value']) ){
					$r = false;
				}
			}

			if( is_array($ar_cost) && sizeof($ar_cost) ){
				if( !ctr_symbols_exec($info['cost'], $ar_cost['symbol'], $ar_cost['value']) ){
					$r = false;
				}
			}

			if( is_array($ar_sales) && sizeof($ar_sales) ){
				if( !ctr_symbols_exec($info['sales'], $ar_sales['symbol'], $ar_sales['value']) ){
					$r = false;
				}
			}

			if( $sh_price && is_array($ar_price_ttc) && sizeof($ar_price_ttc) ){
				if( !ctr_symbols_exec($info['price_ttc'], $ar_price_ttc['symbol'], $ar_price_ttc['value']) ){
					$r = false;
				}
			}

			if( $sh_price && is_array($ar_price_ht) && sizeof($ar_price_ht) ){
				if( !ctr_symbols_exec($info['price_ht'], $ar_price_ht['symbol'], $ar_price_ht['value']) ){
					$r = false;
				}
			}

			if( is_array($ar_costsales) && sizeof($ar_costsales) ){
				$costsales = $info['sales'] ? $cost / $info['sales'] : 0;
				if( !ctr_symbols_exec($costsales, $ar_costsales['symbol'], $ar_costsales['value']) ){
					$r = false;
				}
			}

			if( is_array($ar_margin) && sizeof($ar_margin) ){
				if( !ctr_symbols_exec($margin, $ar_margin['symbol'], $ar_margin['value']) ){
					$r = false;
				}
			}

			if( $r ){
				$t[$prd] = $prd;
			}
	}

	if( is_array($t) ){
		foreach( $ar_prds as $prd=>$info ){
			if( !in_array($prd, $t) ){
				unset( $ar_prds[$prd] );
			}
		}
		$t = array();
	}

	return array_values( $ar_prds );
}

/**	Charge les produits à exporter d'un comparateur.
 *	@param int $ctr Obligatoire, identifiant du comparateur.
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur (permet de récupérer les tarifs appropriés).
 *	@return bool False en cas d'échec.
 *	@return array Un tableau associatif dont la clé est l'identifiant du produit, la valeur le résultat "prd_products_get()" du produit. Les prix, y compris promotionnels, sont inclus.
 */
function ctr_products_export_get( $ctr, $usr_id=0 ){

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	$rctr = ctr_catalogs_get( $ctr, 0, 0, true );
	if( !$rctr ){
		return false;
	}

	$ids = array();
	while( $ctr = ria_mysql_fetch_assoc($rctr) ){
		$ids[] = $ctr['prd_id'];
	}

	// change le user_id en session pour récupérer les prix dans prd_products_get_simple()
	$old_session_id = null;
	if( isset($_SESSION['usr_id']) ){
		$old_session_id = $_SESSION['usr_id'];
	}
	if( $usr_id ){
		$_SESSION['usr_id'] = $usr_id;
	}

	// avec produits "enfant seulement"
	// avec tarifs
	$rprd = prd_products_get_simple( $ids, '', false, 0, false, false, true, false, array('childs' => true) );

	$products = array();

	$prd_unique = array();
	while( $prd = ria_mysql_fetch_assoc($rprd) ){

		if( !prd_products_is_parent( $prd['id'] ) ){

			if( in_array($prd['id'], $prd_unique) ){
				continue;
			}
			$prd_unique[] = $prd['id'];

			// promotions
			$promo = prc_promotions_get( $prd['id'], $usr_id, 0, 1, 0, array('price_ht' => $prd['price_ht'], 'tva_rate' => $prd['tva_rate']) );
			if( is_array($promo) && sizeof($promo) && $promo['price_ttc'] > 0 && $promo['price_ttc'] < $prd['price_ttc'] ){
				$prd['price_ttc'] = $promo['price_ttc'];
			}

			$products[ $prd['id'] ] = $prd;

		}else{

			// chargement des enfants
			$rchilds = prd_products_get_simple( 0, '', false, 0, false, false, true, false, array('parent' => $prd['id'], 'childs' => true) );
			if( !$rchilds ){
				continue;
			}

			while( $child = ria_mysql_fetch_assoc($rchilds) ){
				if( in_array($child['id'], $prd_unique) ){
					continue;
				}
				$prd_unique[] = $child['id'];

				// promotions
				$promo = prc_promotions_get( $child['id'], $usr_id, 0, 1, 0, array('price_ht' => $child['price_ht'], 'tva_rate' => $child['tva_rate']) );
				if( is_array($promo) && sizeof($promo) && $promo['price_ttc'] > 0 && $promo['price_ttc'] < $child['price_ttc'] ){
					$child['price_ttc'] = $promo['price_ttc'];
				}

				$products[ $child['id'] ] = $child;
			}

		}

	}

	// rétablit la session
	if( $old_session_id !== null ){
		$_SESSION['usr_id'] = $old_session_id;
	}

	return $products;

}
 /// @}

/** \defgroup ctr_cat_fields Attributs des familles de produits
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des attributs et de leurs valeurs sur une famille de comparateurs
 *	@{
 */

/** Cette fonction permet d'ajouter un attribut sur une famille de comparateur.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $cat Obligatoire, identifiant d'une famille
 *	@param string $code Obligatoire, code de l'attribut
 *	@param string $name Obligatoire, nom de l'attribut
 *	@param string $desc Optionnel, description de l'attribut
 *	@param bool $mandatory Optionnel, par défaut l'attribut n'est pas obligatoire mettre true pour le rendre
 *	@param $type Optionnel, type de champ (1 par défaut)
 *	@param $unite Optionnel, unité de mesure (cm, kg, etc...)
 *	@return bool False si la création a échouée, sinon l'identifiant du nouvel attribut
 */
function ctr_cat_fields_add( $ctr, $cat, $code, $name, $desc='', $mandatory=false, $type=1, $unite='' ){
	if( !ctr_categories_exists($cat, $ctr) ){
		return false;
	}
	if( trim($code)=='' ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	if( !fld_types_exists($type) ){
		return false;
	}

	$unite = trim($unite) != '' ? '"'.addslashes( $unite ).'"' : 'null';
	$sql = '
		insert into ctr_cat_fields
			( ccf_ctr_id, ccf_cat_id, ccf_code, ccf_name, ccf_desc, ccf_mandatory, ccf_type_id, ccf_unite )
		values
			( '.$ctr.', '.$cat.', \''.addslashes($code).'\', \''.addslashes($name).'\', \''.addslashes($desc).'\', '.( $mandatory ? 1 : 0 ).', '.$type.', '.$unite.' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}
/** Cette fonction permet la mise à jour d'un attribut sur une famille de comparateur.
 *	@param int $ctr  identifiant d'un comparateur
 *	@param int $cat  identifiant d'une famille
 *	@param $code  code de l'attribut
 *	@param string $name Facultatif, nom de l'attribut
 *	@param string $desc Optionnel, description de l'attribut
 *	@param bool $mandatory Optionnel, par défaut l'attribut n'est pas obligatoire mettre true pour le rendre
 *	@param $type Optionnel, type de champ (1 par défaut)
 *	@param $unite Optionnel, unité de mesure (cm, kg, etc...)
 *	@return bool False si la mise à jours a échouée, sinon true
 */
function ctr_cat_fields_update( $ctr, $cat, $code, $name='', $desc='', $mandatory=false, $type=1, $unite='' ){
	if( !ctr_categories_exists($cat, $ctr) ){
		return false;
	}

	if( trim($code)=='' ){
		return false;
	}

	if( !fld_types_exists($type) ){
		return false;
	}

	$unite = trim($unite) != '' ? '"'.addslashes( $unite ).'"' : 'null';
	$sql = '
		update ctr_cat_fields
		set ccf_name=\''.addslashes($name).'\',
			ccf_desc=\''.addslashes($desc).'\',
			ccf_mandatory='.( $mandatory ? 1 : 0 ).',
			ccf_type_id='.$type.',
			ccf_unite='.$unite.'
		where ccf_ctr_id='.$ctr.'
			and ccf_cat_id='.$cat.'
			and ccf_code=\''.addslashes($code).'\'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de vérifier l'existance de champ à renseigner pour l'export d'un produit dans une famille précise.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $cat Optionnel, identifiant d'une famille de comparateur
 *	@return bool true s'il existe des champs à renseigner, false dans le cas contraire
 */
function ctr_comparators_cat_fields_exists( $ctr, $cat=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	$cat_ids = control_array_integer($cat, true, true);
	if (!$cat_ids) {
		return false;
	}

	$sql = '
		select 1
		from ctr_cat_fields
		where ccf_ctr_id='.$ctr.'
	';

	if( $cat_ids ){
		$sql .= ' and (ccf_cat_id=0 or ccf_cat_id in ('.implode(', ', $cat_ids).'))';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les attributs d'une famille de comparateur.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $cat Optionnel, identifiant d'une famille
 *	@param $code Optionnel, code d'un attribut
 *	@param $used Optionnel, indique s'il s'agit de produits d'occasion (true) ou bien de produits neufs (false, valeur par défaut)
 *	@param bool $publish Optionnel, par défaut seul les attributs public sont retournés mettre false pour tous les retourner
 *	@param bool $mandatory Optionnel, par défaut tous les attributs sont retournés, mettre True pour n'avoir que ceux obligatoires, False pour tous les autres
 *	@param $exclude_masked Optionnel, par défaut à False mettre True pour exclure les attributs des catégories masquées
 *	@param $ccf_id Optionnel, identifiant d'un attribut
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de l'attribut
 *				- code : code de l'attribut
 *				- name : nom de l'attribut
 *				- desc : description de l'attribut
 *				- mandatory : si oui ou non l'attribu est obligatoire
 *				- cat : identifiant de la famille
 *				- type_id : type de données attendue
 *				- unite : unité de la valeur
 */
function ctr_cat_fields_get( $ctr, $cat=0, $code='', $publish=true, $used=false, $mandatory=null, $exclude_masked=false, $ccf_id=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;

	if( !is_numeric($ccf_id) || $ccf_id<0 ) return false;

	global $config;

	$sql = '
		select
			ccf_id as id, ccf_code as code, ccf_name as name, ccf_desc as "desc", ccf_mandatory as mandatory, ccf_cat_id as cat, ccf_type_id as type_id, type_name, ccf_unite as unite
		from ctr_cat_fields
			left join fld_types on (ccf_type_id = type_id)
		where ccf_ctr_id='.$ctr.'
	';

	$cats = control_array_integer($cat);
	if( $cats ){
		$sql .= ' and (ccf_cat_id=0 or ccf_cat_id in ('.implode(', ', $cats).'))';
	}
	if( $ccf_id > 0 ){
		$sql .= ' and ccf_id = '.$ccf_id;
	}
	if( trim($code)!='' ){
		$sql .= ' and ccf_code=\''.addslashes( $code ).'\'';
	}
	if( $publish ){
		$sql .= ' and ccf_publish=1';
	}

	if( $used ){
		$no_cat_link = in_array( $ctr, array(CTR_CDISCOUNT) );

		$sql .= '
			and (
				exists(
					select 1
					from ctr_catalogs
					where ctl_tnt_id = '.$config['tnt_id'].'
						'.( $no_cat_link ? '' : 'and ctl_cat_id = ccf_cat_id' ).'
						and ctl_ctr_id = ccf_ctr_id
				)

				or exists (
					select 1
					from ctr_prd_categories
					where cpc_tnt_id = '.$config['tnt_id'].'
						'.( $no_cat_link ? '' : 'and cpc_cat_id = ccf_cat_id' ).'
						and cpc_ctr_id = ccf_ctr_id
				)
			)
		';
	}

	if( $mandatory !== null ){
		if( $mandatory ){
			$sql .= ' and ccf_mandatory = 1';
		}else{
			$sql .= ' and ccf_mandatory = 0';
		}
	}

	if( $exclude_masked ){
		// Contrôle s'il existe au moins une catéogrie non public
		$rno_filter = ria_mysql_query('select 1 from ctr_cat_publish where cat_tnt_id='.$config['tnt_id'].' and cat_ctr_id='.$ctr);
		$no_filter = !$rno_filter  || !ria_mysql_num_rows($rno_filter) ? true : false;

		if( !$no_filter ){
			$sql .= '
				and exists(
					select 1
					from ctr_cat_publish
					where cat_tnt_id = '.$config['tnt_id'].'
						and cat_ctr_id = ccf_ctr_id
						and cat_id = ccf_cat_id
				)
			';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer le code d'un attribut donné.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur
 *	@param int $id Obligatoire, identifiant d'un attribut
 *	@return bool false si l'attribut n'existe pas, sinon le code de l'attribut
 */
function ctr_cat_fields_get_code( $ctr, $id ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select ccf_code as code
		from ctr_cat_fields
		where ccf_ctr_id='.$ctr.'
			and ccf_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'code' );
}

/** Cette fonction permet de vérifier qu'un attribut existe.
 *	@param int $ccf Obligatoire, identifiant de l'attribut
 *	@return bool True si l'attribut existe, False dans le cas contraire
 */
function ctr_cat_fields_exists( $ccf ){
	if( !is_numeric($ccf) || $ccf<=0 ) return false;

	$sql = '
		select 1
		from ctr_cat_fields
		where ccf_id='.$ccf.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer qu'un ou plusieurs attributs.
 *	@param int $ctr_id Obligatoire, Identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $ccf_id Facultatif, identifiant de l'attribut
 *	@return bool True si l'attribut existe, False dans le cas contraire
 */
function ctr_cat_fields_delete( $ctr_id, $ccf_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id <= 0 ){
		return false;
	}

	if( !is_numeric($ccf_id) || $ccf_id < 0 ){
		return false;
	}

	$sql = '
		delete from ctr_cat_fields
		where ccf_ctr_id = '.$ctr_id.'
	';

	if( $ccf_id > 0 ){
		$sql .= ' and ccf_id='.$ccf_id;
	}

	if( !ctr_cat_field_values_del($ccf_id) ){
		return false;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de suprimé une valeur pour un attribut
 *	@param int $cfv_id Identifiant de la valeur
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_cat_field_value_del( $cfv_id ){

	if( !is_numeric($cfv_id) || $cfv_id <= 0 ){
		return false;
	}

	$sql = '
		delete from ctr_cat_field_values
		where cfv_id = '.$cfv_id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return true;
}
/** Supprimer les valeur pour un attribut
 *	@param int $ccf_id Facultatif, identifiant d'un attribut
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_cat_field_values_del( $ccf_id=0 ){
	if( !is_numeric($ccf_id) || $ccf_id < 0 ){
		return false;
	}

	$ar_ccf_id = array();
	if( $ccf_id > 0 ){
		$ar_ccf_id = array( $ccf_id );
	}else{
		$res = ria_mysql_query('
			select cfv_ccf_id
			from ctr_cat_field_values
			where not exists (
				select 1
				from ctr_cat_fields
				where ccf_id = cfv_ccf_id
			)
		');

		if( !$res ){
			return false;
		}

		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_ccf_id[] = $r['cfv_ccf_id'];
		}
	}

	if( !count($ar_ccf_id) ){
		return true;
	}

	return ria_mysql_query('
		delete from ctr_cat_field_values
		where cfv_ccf_id = '.$ccf_id.'
	');
}

/** Cette fonction permet de créer d'ajouter une valeur à un attribut.
 *	@param int $ccf Obligatoire, identifiant d'un attribut
 *	@param string $value Obligatoire, valeur de l'attribut
 *	@return bool False si l'ajout a échouée, sinon l'identifiant de la valeur
 */
function ctr_cat_field_values_add( $ccf, $value ){
	if( !ctr_cat_fields_exists($ccf) ) return false;
	if( trim($value)=='' ) return false;

	$sql = '
		insert into ctr_cat_field_values
			( cfv_ccf_id, cfv_value )
		values
			( '.$ccf.', \''.addslashes($value).'\' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de récupérer les valeurs de restriction d'un attribut
 *	@param int $ccf Obligatoire, identifiant d'un champ
 *	@param int $id Optionnel, identifiant d'une valeur
 *	@return bool False si le champ n'existe pas ou qu'aucun valeur n'existe
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de la valeur
 *				- val : valeur de l'attribut
 */
function ctr_cat_field_values_get( $ccf, $id=0 ){
	if( !is_numeric($ccf) || $ccf<=0 ) return false;
	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select cfv_id as id, cfv_value as val
		from ctr_cat_field_values
		where cfv_ccf_id='.$ccf.'
	';

	if( $id>0 ){
		$sql .= ' and cfv_id='.$id;
	}

	$sql .= ' order by cfv_value';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les valeurs de restriction d'un attribut dans un tableau.
 *	@param int $ccf Obligatoire, identifiant d'un champ
 *	@return bool False si le champ n'existe pas ou qu'aucun valeur n'existe
 *	@return array Un tableau - array(id=>val) - contenant :
 *				- id : identifiant de la valeur
 *				- val : valeur de l'attribut
 */
function ctr_cat_field_values_get_array( $ccf ){
	if( !is_numeric($ccf) || $ccf<=0 ) return false;

	$rval = ctr_cat_field_values_get( $ccf );
	if( !$rval ||!ria_mysql_num_rows($rval) ){
		return false;
	}

	$vals = array();
	while( $val = ria_mysql_fetch_array($rval) ){
		$vals[ $val['id'] ] = $val['val'];
	}

	return $vals;
}

/** Cette fonction récupère la valeur par défaut d'un attribut selon les informations du produit
 *	@param int $ctr_id Obligatoire, identifiant du comparateur de prix ou de la place de marché
 *	@param int $ccf_id Obligatoire, identifiant de l'attribut
 *	@param string $code Obligatoire, code de l'attribut (ou l'identifiant s'il n'existe pas de code)
 *	@param array $product Obligatoire, tableau contenant les informations sur le produit (libre sur la source), colonne obligatoire : id, height, length, width, weight
 *	@return mixed La valeur par défaut
 */
function ctr_cat_field_values_get_default( $ctr_id, $ccf_id, $code, $product ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	if( !is_numeric($ccf_id) || $ccf_id<=0 ){
		return false;
	}
	if( trim($code) == '' ){
		return false;
	}
	if( !ria_array_key_exists(array('id', 'height', 'length', 'width', 'weight'), $product) ){
		return false;
	}

	$val = '';

	switch( $ctr_id ){
		case CTR_RUEDUCOMMERCE_MIRAKL: {
			switch( $code ){
				case 'dimensions_height_cm':
				case 'dimensions_depth_cm':
					$val = $product['height']; break;
				case 'dimensions_height_m' :
					$val = $product['height'] / 100; break;
				case 'dimensions_height_mm' :
					$val = $product['height'] * 10; break;
				case 'dimensions_length_cm':
					$val = $product['length']; break;
				case 'dimensions_length_m':
					$val = $product['length'] / 100; break;
				case 'dimensions_length_mm':
					$val = $product['length'] * 10; break;
				case 'dimensions_width_cm':
					$val = $product['width']; break;
				case 'dimensions_width_m':
					$val = $product['width'] / 100; break;
				case 'dimensions_width_mm':
					$val = $product['width'] * 10; break;
				case 'weight_g':
					$val = $product['weight']; break;
				case 'weight_kg':
					$val = $product['weight'] / 1000; break;
				default :
					$val = ctr_cat_field_get_value_by_mapping( $ctr_id, $ccf_id, $product['id'] );
					break;
			}

			break;
		}
	}
	return $val;
}

/** Cette fonction permet de récupérer la valeur correspondant à un identifiant
 *	@param int $ccf_id Obligatoire, identifiant d'un attribut
 *	@param int $cfv_id Obligatoire, identifiant d'une valeur
 *	@return mixed La valeur correspondante, False si l'un des paramètres obligatoires est omis ou faux
 */
function ctr_cat_fields_get_value( $ccf_id, $cfv_id ){
	if( !is_numeric($ccf_id) || $ccf_id<=0 ){
		return false;
	}

	if( !is_numeric($cfv_id) || $cfv_id<=0 ){
		return false;
	}

	$sql = '
		select cfv_value
		from ctr_cat_field_values
		where cfv_ccf_id='.$ccf_id.'
			and cfv_id='.$cfv_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['cfv_value'];
}

 /// @}

/** \defgroup ctr_carriers Transporteurs des places de marché
 *	\ingroup ctr_comparators model_dlv
 *	Ce module comprend les fonctions nécessaires à la gestion transporteurs des places de marché
 *	@{
 */

/** Cette fonction permet d'ajouter un transporteur.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param string $code Obligatoire, code du transporteur dans la place de marché
 *	@param string $name Obligatoire, nom du transporteur
 *	@return bool False si l'ajout a échouée sinon l'identifiant du nouveau transporteur
 */
function ctr_carriers_add( $ctr, $code, $name ){
	if( !ctr_comparators_exists($ctr) ) return false;
	if( trim($code)=='' ) return false;
	if( trim($name)=='' ) return false;

	$ccr = ctr_carriers_get( $ctr, 0, $code );
	if( $ccr && ria_mysql_num_rows($ccr) ){
		return ria_mysql_result( $ccr, 0, 'id' );
	}

	$sql = '
		insert into ctr_carriers
			( ccr_ctr_id, ccr_code, ccr_name )
		values
			( '.$ctr.', \''.addslashes($code).'\', \''.addslashes($name).'\' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de récupérer la liste des transporteurs d'une place de marché
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $id Facultatif, identifiant du transporteur sur lequel filtrer le résultat
 *	@param string $code Facultatif, code du transporteur sur lequel filtrer le résultat
 *	@return bool False si le paramètre obligatoire est omis ou faux, sinon un résultat MySQL contenant :
 *				- id : identifiant du transporteur
 *				- code : code du transporteur chez la place de marché
 *				- name : nom du transporteur
 */
function ctr_carriers_get( $ctr, $id=0, $code='' ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select ccr_id as id, ccr_code as code, ccr_name as name
		from ctr_carriers
		where ccr_ctr_id='.$ctr.'
	';

	if( $id>0 ){
		$sql .= ' and ccr_id='.$id;
	}

	if( trim($code)!='' ){
		$sql .= ' and ccr_code=\''.addslashes( trim($code) ).'\'';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'un transporteur existe bien pour une place de marché.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $ccr Obligatoire, identifiant d'un transporteur
 *	@return bool True si le transporteur existe, False dans le cas contraire
 */
function ctr_carriers_exists( $ctr, $ccr ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($ccr) || $ccr<=0 ) return false;

	$sql = '
		select 1
		from ctr_carriers
		where ccr_ctr_id='.$ctr.'
			and ccr_id='.$ccr.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer un transporteur pour une place de marché.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $id Facultatif, identifiant d'un transporteur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ctr_carriers_del( $ctr, $id=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ){
		return false;
	}

	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !ctr_carriers_services_del($ctr, 0, $id) ){
		return false;
	}

	$sql = '
		delete from ctr_carriers
		where ccr_ctr_id='.$ctr.'
	';

	if( $id>0 ){
		$sql .= '
			and ccr_id='.$id.'
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction d'ajouter un lien entre un transporteur et un service de livraison.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $srv Obligatoire, identifiant d'un service de livraison
 *	@param int $ccr Obligatoire, identifiant d'un transporteur
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire
 */
function ctr_carriers_services_add( $ctr, $srv, $ccr ){
	if( !ctr_comparators_exists($ctr) ) return false;
	if( !dlv_services_exists($srv) ) return false;
	if( !ctr_carriers_exists($ctr, $ccr) ) return false;
	global $config;

	// supprime le lien du service dans le cas où il existe
	if( !ctr_carriers_services_del($ctr, $srv) ){
		return false;
	}

	$sql = '
		insert into ctr_carriers_services
			( ccs_tnt_id, ccs_ctr_id, ccs_srv_id, ccs_ccr_id )
		values
			( '.$config['tnt_id'].', '.$ctr.', '.$srv.', '.$ccr.' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un ou plusieurs lien entre les transporteurs et les services de livraison.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $srv Optionnel, identifiant d'un service de livraison
 *	@param int $ccr Optionnel, identifiant d'un transporteur
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_carriers_services_del( $ctr, $srv=0, $ccr=0 ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($srv) || $srv<0 ) return false;
	if( !is_numeric($ccr) || $ccr<0 ) return false;
	global $config;

	$sql = '
		delete from ctr_carriers_services
		where ccs_tnt_id='.$config['tnt_id'].'
	';

	if( $ctr>0 ){
		$sql .= ' and ccs_ctr_id='.$ctr;
	}

	if( $srv>0 ){
		$sql .= ' and ccs_srv_id='.$srv;
	}

	if( $ccr>0 ){
		$sql .= ' and ccs_ccr_id='.$ccr;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne l'identifiant du transporteur rattaché à un service.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $srv Obligatoire, identifiant d'un service de livraison
 *	@return bool False si l'un des paramètres est faux ou omis, sinon l'identifiant du transporteur
 */
function ctr_carriers_services_get_carrier( $ctr, $srv ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($srv) || $srv<=0 ) return false;
	global $config;

	$sql = '
		select ccs_ccr_id as carrier
		from ctr_carriers_services
		where ccs_tnt_id='.$config['tnt_id'].'
			and ccs_ctr_id='.$ctr.'
			and ccs_srv_id='.$srv.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'carrier' );
}

/** Cette fonction permet de récupérer le code d'un transporteur rattaché à un service donnée.
 *	@param int $ctr Obligatoire, identifiant d'une place de marché
 *	@param int $srv Obligatoire, idetifiant d'un service de livraison
 *	@return bool False si l'un des paramètres est omis ou faux ou bien si aucun transporteur n'est rattaché au service
 *	@return string Le code du transporteur rattaché au service de livraison
 */
function ctr_carriers_services_get_code( $ctr, $srv ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($srv) || $srv<=0 ) return false;
	global $config;

	$sql = '
		select ccr_code as code
		from ctr_carriers
			join ctr_carriers_services on ( ccr_id=ccs_ccr_id )
		where ccr_ctr_id='.$ctr.'
			and ccs_tnt_id='.$config['tnt_id'].'
			and ccs_srv_id='.$srv.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'code' );
}

/// @}

/** \defgroup ctr_models Modèle de données pour les comparateurs / places de marché
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion des modèles de données exporter sur les comparateurs / places de marché
 *	@{
 */

/** Cette fonction permet d'ajouter un modèle
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param string $name Optionnel, nom donné à ce modèle
 *	@param string $desc Optionnel, description donnée à ce modèle
 *	@param bool $is_market Optionnel, par défaut il s'agit d'un modèle pour les comparateurs de prix, mettre true pour que ce soit pour les places de marché
 *	@param string $prd_title Optionnel, titre utilisé dans ce modèle
 *	@param string $prd_desc Optionnel, description utilisée dans ce modèle
 *	@return int L'identifiant du modèle si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function ctr_models_add( $prd_id, $name='', $desc='', $is_market=false, $prd_title, $prd_desc='' ){
	if( !prd_products_exists($prd_id) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		insert into ctr_models
			( mdl_tnt_id, mdl_name, mdl_desc, mdl_is_marketplace, mdl_prd_title, mdl_prd_desc, mdl_prd_id )
		values
			( '.$config['tnt_id'].', "'.addslashes( $name ).'", "'.addslashes( $desc ).'", '.( $is_market ? '1' : '0' ).', "'.addslashes( $prd_title ).'", "'.addslashes( $prd_desc ).'", '.$prd_id.' )
	');

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de contrôler l'existance d'un modèle
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $prd_id Optionnel, idetifiant d'un produit
 *	@return bool True si le modèle existe, False si les deux paramètres sont faux ou non renseignée ou si aucun modèle n'existe
 */
function ctr_models_exists( $mdl_id=0, $prd_id=0 ){
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $prd_id==0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ctr_models
		where mdl_tnt_id='.$config['tnt_id'].'
	';

	if( $mdl_id>0 ){
		$sql .= '
			and mdl_id='.$mdl_id.'
		';
	}

	if( $prd_id>0 ){
		$sql .= '
			and mdl_prd_id='.$prd_id.'
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour un modèle
 *	@param int $mdl_id Obligatoire, identifiant du modèle à mettre à jour
 *	@param string $name Optionnel, nom donné à ce modèle
 *	@param string $desc Optionnel, description donnée à ce modèle
 *	@param string $prd_title Optionnel, titre utilisé dans ce modèle
 *	@param string $prd_desc Optionnel, description utilisée dans ce modèle
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function ctr_models_update( $mdl_id, $name='', $desc='', $prd_title, $prd_desc='' ){
	if( !ctr_models_exists($mdl_id) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update ctr_models
		set mdl_name = '.( trim($name)!='' ? '"'.addslashes( $name ).'"' : 'null' ).',
			mdl_desc = '.( trim($desc)!='' ? '"'.addslashes( $desc ).'"' : 'null' ).',
			mdl_prd_title = '.( trim($prd_title)!='' ? '"'.addslashes( $prd_title ).'"' : 'null' ).',
			mdl_prd_desc = '.( trim($prd_desc)!='' ? '"'.addslashes( $prd_desc ).'"' : 'null' ).'
		where mdl_tnt_id='.$config['tnt_id'].'
			and mdl_id='.$mdl_id.'
	');
}

/** Cette fonction permet de supprime un ou plusieurs modèle lié à un produit.
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $prd_id Optionnel, idetifiant d'un produit
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_models_del( $mdl_id, $prd_id ){
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $prd_id==0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query( 'delete from ctr_models_comparators where mct_tnt_id='.$config['tnt_id'].' and mct_mdl_id='.$mdl_id );
	if( !$res ){
		return false;
	}

	if( !ctr_images_del(0, $mdl_id) ){
		return false;
	}

	$sql = '
		delete from ctr_models
		where mdl_tnt_id='.$config['tnt_id'].'
	';

	if( $mdl_id>0 ){
		$sql .= '
			and mdl_id='.$mdl_id.'
		';
	}

	if( $prd_id>0 ){
		$sql .= '
			and mdl_prd_id='.$prd_id.'
		';
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$rmax = ria_mysql_query( 'select max(mdl_id) from ctr_models' );
	if( $rmax && ria_mysql_num_rows($rmax) ){
		$max = ria_mysql_result( $rmax, 0, 0 );
		$max = is_numeric($max) && $max>0 ? $max : 1;

		$res = ria_mysql_query( 'alter table ctr_models auto_increment='.$max );
		if( !$res ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de savoir si un modèle est créé pour les comparateurs ou les places de marché
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@return bool True s'il s'agit d'un modèle pour les places de marché, False dans le cas contraire
 */
function ctr_models_is_marketplace( $mdl_id ){
	if( !ctr_models_exists($mdl_id) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from ctr_models
		where mdl_tnt_id='.$config['tnt_id'].'
			and mdl_id='.$mdl_id.'
			and mdl_is_marketplace=1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de savoir si un modèle dispose d'image directement rattaché à lui.
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@return bool True s'il a bien des images, False dans le cas contraire
 */
function ctr_models_has_images( $mdl_id ){
	if( !is_numeric($mdl_id) || $mdl_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ctr_images
		where img_tnt_id='.$config['tnt_id'].'
			and img_mdl_id='.$mdl_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les informations sur un ou plusieurs modèles.
 *	@param int $mdl_id Optionnel, identifiant d'un modèle sur lequel filtrer le résultat
 *	@param int $prd_id Optionnel, identifiant d'un produit sur lequel filtrer le résultat
 *	@param bool $is_market Optionnel, par défaut ce paramètre n'est pas pris en compte, mettre True pour ne récupérer que les modèles comparateurs et False pour les modèles des places de marché
 *	@return resource Un résultat MySQL contenant :
 *				- mdl_id : identifiant du modèle
 *				- name : nom du modèle
 *				- desc : description du modèle
 *				- prd_id : identifiant du produit sur lequel le modèle est rattaché
 *				- prd_title : surcharge du titre dans ce modèle
 *				- prd_desc : surcharge de la description dans ce modèle
 */
function ctr_models_get( $mdl_id=0, $prd_id=0, $is_market=null ){
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select mdl_id, mdl_name as name, mdl_desc as "desc", mdl_prd_id as prd_id, mdl_prd_title as prd_title, mdl_prd_desc as prd_desc
		from ctr_models
		where mdl_tnt_id='.$config['tnt_id'].'
	';

	if( $mdl_id>0 ){
		$sql .= '
			and mdl_id='.$mdl_id.'
		';
	}

	if( $prd_id>0 ){
		$sql .= '
			and mdl_prd_id='.$prd_id.'
		';
	}

	if( $is_market!==null ){
		$sql .= '
			and mdl_is_marketplace='.( $is_market ? '1' : '0' ).'
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de savoir sur quel produit s'applique un modèle
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@return int|bool Retourne l'identifiant du produit, False si le paramètre obligatoire est omis ou faux
 */
function ctr_models_get_prd( $mdl_id ){
	if( !ctr_models_exists($mdl_id) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select mdl_prd_id as prd_id
		from ctr_models
		where mdl_tnt_id='.$config['tnt_id'].'
			and mdl_id='.$mdl_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['prd_id'];
}

/** Cette fonction permet d'appliquer un modèle de donnée à un export produit sur un comparateur / une place de marché.
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle de donnée
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur / d'une place de marché
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
 */
function ctr_models_comparators_apply( $mdl_id, $ctr_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	if( !is_numeric($mdl_id) || $mdl_id<=0 ){
		return false;
	}

	global $config;

	$prd_id = ctr_models_get_prd( $mdl_id );
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !ctr_models_comparators_add($mdl_id, $ctr_id) ){
		return false;
	}

	$res = ria_mysql_query('
		update ctr_catalogs
		set ctl_prd_title=null,
			ctl_prd_desc=null
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_prd_id='.$prd_id.'
			and ctl_ctr_id='.$ctr_id.'
	');

	return ctr_images_del( 0, 0, $ctr_id );
}

/** Cette fonction permet de rattacher un modèle à un ou plusieurs comparateurs / places de marché, selon si le modèle est créé pour les comparateurs ou les places de marché.
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@param int|array $ar_ctr Obligatoire, identifiant ou tableau d'identifiants de comparateurs / places de marché
 *	@return bool True si le rattachement s'est correctement déroulé, False dans le cas contraire
 */
function ctr_models_comparators_add( $mdl_id, $ar_ctr ){
	if( !ctr_models_exists($mdl_id) ){
		return false;
	}

	if( !is_array($ar_ctr) ){
		if( !is_numeric($ar_ctr) || $ar_ctr<=0 ){
			return false;
		}

		$ar_ctr = array( $ar_ctr );
	}else{
		foreach( $ar_ctr as $c ){
			if( !is_numeric($c) || $c<=0 ){
			    return false;
			}
		}
	}

	$for_market = ctr_models_is_marketplace( $mdl_id );
	foreach( $ar_ctr as $c ){
		if( $for_market ){
			if( !ctr_comparators_is_marketplace($c) ){
				return false;
			}
		}else{
			if( !ctr_comparators_exists($c) || ctr_comparators_is_marketplace($c) ){
				return false;
			}
		}
	}

	// Supprime les anciennes relations
	if( !ctr_models_comparators_del($mdl_id) ){
		return false;
	}

	global $config;

	$sql = '
		insert into ctr_models_comparators
			( mct_tnt_id, mct_mdl_id, mct_ctr_id )
		values
	';

	$count = 1;
	foreach( $ar_ctr as $c ){
		$sql .= '
			( '.$config['tnt_id'].', '.$mdl_id.', '.$c.' )'.( $count<sizeof($ar_ctr) ? ', ' : '' ).'
		';

		$count++;
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	/*$prd_id = ctr_models_get_prd( $mdl_id );
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	return ria_mysql_query('
		update ctr_catalogs
		set ctl_prd_title=null,
			ctl_prd_desc=null
		where ctl_tnt_id='.$config['tnt_id'].'
			and ctl_prd_id='.$prd_id.'
			and ctl_ctr_id in ('.implode( ', ', $ar_ctr ).')
	');*/

	return true;
}

/** Cette fonction permet de savoir si un modèle (donné l'id pour spécifier ce mmodèle) est appliqué sur un comparateur / une place de marché
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur / d'une place de marché
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@return bool True si le modèle est bien appliqué, False dans le cas contraire
 */
function ctr_models_comparators_exists( $ctr_id, $mdl_id=0 ){
	if( !ctr_comparators_exists($ctr_id) ){
		return false;
	}

	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}elseif( $mdl_id>0 && !ctr_models_exists($mdl_id) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ctr_models_comparators
		where mct_tnt_id='.$config['tnt_id'].'
			and mct_ctr_id='.$ctr_id.'
	';

	if( $mdl_id>0 ){
		$sql .= '
			and mct_mdl_id='.$mdl_id.'
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer un ou plusieurs rattachement
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@param int|array $ar_ctr Optionnel, identifiant ou tableau d'identifiants de comparateurs / places de marché
 *	@return bool true si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_models_comparators_del( $mdl_id, $ar_ctr=0 ){
	if( !ctr_models_exists($mdl_id) ){
		return false;
	}

	if( !is_array($ar_ctr) ){
		if( !is_numeric($ar_ctr) || $ar_ctr<0 ){
			return false;
		}

		$ar_ctr = $ar_ctr>0 ? array( $ar_ctr ) : array();
	}else{
		foreach( $ar_ctr as $c ){
			if( !is_numeric($c) || $c<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		delete from ctr_models_comparators
		where mct_tnt_id='.$config['tnt_id'].'
			and mct_mdl_id='.$mdl_id.'
	';

	if( sizeof($ar_ctr) ){
		$sql .= '
			and mct_ctr_id in ('.implode( ', ', $ar_ctr ).')
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupér un tableau de tous les comparateurs / places de marché rattachés à un modèle
 *	@param int $mdl_id Obligatoire, identifiant d'un modèle
 *	@return array Un tableau contenant les identifiant des comparateurs / places de marché
 */
function ctr_models_comparators_get_ids( $mdl_id ){
	if( !ctr_models_exists($mdl_id) ){
		return array();
	}

	global $config;

	$ar_ctr = array();

	$res = ria_mysql_query('
		select mct_ctr_id as ctr_id
		from ctr_models_comparators
		where mct_tnt_id='.$config['tnt_id'].'
			and mct_mdl_id='.$mdl_id.'
	');

	if( $res && ria_mysql_num_rows($res) ){
		while( $r = ria_mysql_fetch_array($res) ){
			$ar_ctr[] = $r['ctr_id'];
		}
	}

	return $ar_ctr;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images du modèle ou du comparateur / place de marché
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param string $fieldname Obligatoire, nom du champ de type "file" contenant l'image à télécharger
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@return int L'identifiant attribué à l'image.
 */
function ctr_images_upload( $fieldname, $mdl_id=0, $ctr_id=0 ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ){
		return false;
	}

	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $ctr_id==0 ){
		return false;
	}

	return ctr_images_add( $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'], $mdl_id, $ctr_id );
}

/** Permet l'ajout d'un fichier image à modèle ou un comparateur / une place de marché. Cette fonction est similaire à ctr_images_upload,
 *	a l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile lors d'importations.
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@return int L'identifiant attribué à l'image.
 */
function ctr_images_add( $filename, $srcname='', $mdl_id=0, $ctr_id=0 ){
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $ctr_id==0 ){
		return false;
	}

	global $config;

	if( $id = img_images_add( $filename, $srcname ) ){
		ctr_images_add_existing( $id, $mdl_id, $ctr_id, null );
		img_images_count_update($id);
	}
	return $id;
}

/** Cette fonction permet d'ajouter une image spécifique aux comparateurs / places de marché
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param int $img_id Obligatoire, identifiant d'une image
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@param int $pos Optionnel, position arbitraire permettant de positionner l'image lorsque plusieurs sont présentes
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function ctr_images_add_existing( $img_id, $mdl_id=0, $ctr_id=0, $pos=null ){
	if( !is_numeric($img_id) && $img_id<=0 ){
		return false;
	}

	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $ctr_id==0 ){
		return false;
	}

	if( $mdl_id>0 ){
		$ctr_id = 0;
	}

	global $config;

	if( !is_numeric($pos) || $pos<0 ){
		$pos = ria_mysql_result(
			ria_mysql_query('
				select count(*)
				from ctr_images
				where img_tnt_id='.$config['tnt_id'].'
					and img_mdl_id='.$mdl_id.'
					and img_ctr_id='.$ctr_id
			), 0, 0
		);
	}

	$res = ria_mysql_query('
		replace into ctr_images
			( img_tnt_id, img_id, img_mdl_id, img_ctr_id, img_pos )
		values
			( '.$config['tnt_id'].', '.$img_id.', '.$mdl_id.', '.$ctr_id.', '.$pos.' )
	');

	if( !$res ){
		return false;
	}

	return img_images_count_update( $img_id );
}

/** Cette fonction récupère les images associées à un comparateur / une place de marché ou un modèle.
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@param int $img_id Facultatif, identifiant d'une image
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de l'image
 *				- pos : position de l'image
*/
function ctr_images_get( $mdl_id=0, $ctr_id=0, $img_id=0 ){
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $ctr_id==0 ){
		return false;
	}

	if( !is_numeric($img_id) || $img_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id, img_pos as pos, img_mdl_id as mdl_id, img_ctr_id as ctr_id
		from ctr_images
		where img_tnt_id='.$config['tnt_id'].'
	';

	if( $mdl_id ){
		$sql .= ' and img_mdl_id='.$mdl_id;
	}

	if( $ctr_id ){
		$sql .= ' and img_ctr_id='.$ctr_id;
	}

	if( $img_id ){
		$sql .= ' and img_id='.$img_id;
	}

	$sql .= '
		order by img_pos asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un lien entre une image et un modèle ou un comparateur / une place de marché
 *	Un identifiant de modèle ou de comparateur / place de marché est obligatoire
 *	@param int $img_id Optionnel, identifiant d'une image
 *	@param int $mdl_id Optionnel, identifiant d'un modèle
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur / d'une place de marché
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_images_del( $img_id=0, $mdl_id=0, $ctr_id=0 ){
	if( !is_numeric($img_id) || $img_id<0 ){
		return false;
	}
	if( !is_numeric($mdl_id) || $mdl_id<0 ){
		return false;
	}

	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}

	if( $mdl_id==0 && $ctr_id==0 ){
		return false;
	}

	global $config;

	$imgs = ctr_images_get( $mdl_id, $ctr_id, $img_id );
	while( $r = ria_mysql_fetch_array($imgs) ){

		$r_pos = ria_mysql_query('select img_pos from ctr_images where img_tnt_id='.$config['tnt_id'].' and img_mdl_id='.$r['mdl_id'].' and img_ctr_id='.$r['ctr_id'].' and img_id='.$r['id']);
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos,0,0);
			ria_mysql_query('update ctr_images set img_pos=img_pos-1 where img_tnt_id='.$config['tnt_id'].' and img_mdl_id='.$r['mdl_id'].' and img_ctr_id='.$r['ctr_id'].' and img_pos>'.$pos);
			ria_mysql_query('delete from ctr_images where img_tnt_id='.$config['tnt_id'].' and img_mdl_id='.$r['mdl_id'].' and img_ctr_id='.$r['ctr_id'].' and img_id='.$r['id']);
		}
		img_images_count_update($r['id']);
	}

	return true;
}

/** Cette fonction permet de récupérer un tableau contenant les identifiants de compte client utilisé pour les places de marchés.
 *	@param int $mkt_id Optionnel, identifiant d'une place de marché
 *	@return array Un tableau contenant les identifiants de comptes client
 */
function ctr_marketplaces_get_users( $mkt_id=0 ){
	if( !is_numeric($mkt_id) || $mkt_id < 0 ){
		return false;
	}

	global $config;

	$ruser = ria_mysql_query('
		select distinct cpf_fld_id as usr_id
        from ctr_param_fields
        where cpf_tnt_id = '.$config['tnt_id'].'
        	and cpf_cpr_code = "USR_ID"
	');

	$ar_usr_ids = array();
	if( $ruser ){
		while( $user = ria_mysql_fetch_assoc($ruser) ){
			$ar_usr_ids[] = $user['usr_id'];
		}
	}

	return $ar_usr_ids;
}

/** Cette fonction permet d'ajouter un lien entre un attribut et un champ avancé
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $ccf_id Obligatoire, identifiant d'un attribut
 *	@param int $fld_id Obligatoire, identifiant d'un champ avancé
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function ctr_cat_field_mapping_add( $ctr_id, $ccf_id, $fld_id ){
	if( !ctr_comparators_exists($ctr_id, false) ){
		return false;
	}
	if( !ctr_cat_fields_exists($ccf_id) ){
		return false;
	}
	if( !fld_fields_exists($fld_id) ){
		return false;
	}

	global $config;

	$sql = '
		insert into ctr_cat_field_mapping
			( cfm_tnt_id, cfm_ctr_id, cfm_ccf_id, cfm_fld_id )
		values
			( '.$config['tnt_id'].', '.$ctr_id.', '.$ccf_id.', '.$fld_id.' )
	';
	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer les mappgins entre attributs et champs avancés existant
 *	@param int $ctr_id Optionnel, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $ccf_id Optionnel, identifiant d'un attribut
 *	@param int $fld_id Optionnel, identifiant d'un champ avancé
 *	@param bool $mandatory Optionnel, par défaut tout est retourné (mettre True pour n'avoir que les champs obligatoires, False pour tous les autres)
 *	@return resource Un résultat MySQL contenant :
 *					- ccf_id : identifiant du champ
 *					- name : nom du champ
 *					- desc : description du champ
 *					- fld_id : identifiant du champ avancé rattachée à cet attribut
 */
function ctr_cat_field_mapping_get( $ctr_id=0, $ccf_id=0, $fld_id=0, $mandatory=null ){
	if( !is_numeric($ctr_id) || $ctr_id<0 ){
		return false;
	}
	if( !is_numeric($ccf_id) || $ccf_id<0 ){
		return false;
	}
	if( !is_numeric($fld_id) || $fld_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select cfm_ccf_id as "ccf_id", ccf_code, ccf_name as name, ccf_desc as "desc", cfm_fld_id as "fld_id"
		from ctr_cat_field_mapping
			left join ctr_cat_fields on (cfm_ctr_id = ccf_ctr_id and cfm_ccf_id = ccf_id)
		where cfm_tnt_id = '.$config['tnt_id'].'
	';

	if( $ctr_id > 0 ){
		$sql .= ' and cfm_ctr_id = '.$ctr_id;
	}
	if( $ccf_id > 0 ){
		$sql .= ' and cfm_ccf_id = '.$ccf_id;
	}
	if( $fld_id > 0 ){
		$sql .= ' and cfm_fld_id = '.$fld_id;
	}

	if( $mandatory !== null ){
		if( $mandatory ){
			$sql .= ' and ccf_mandatory = 1';
		}else{
			$sql .= ' and ccf_mandatory = 0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un lien entre un attribut et un champ (physique ou avancé)
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $ccf_id Optionnel, identifiant d'un attribut
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_cat_field_mapping_del( $ctr_id, $ccf_id=0 ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	if( !is_numeric($ccf_id) || $ccf_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from ctr_cat_field_mapping
		where cfm_tnt_id = '.$config['tnt_id'].'
			and cfm_ctr_id = '.$ctr_id.'
	';

	if( $ccf_id > 0 ){
		$sql .= ' and cfm_ccf_id = '.$ccf_id;
	}

	return ria_mysql_query( $sql );
}

/** Retourne un tableau des codes d'attribut exclure du mapping car faisant référence à des champs physiques. */
function ctr_cat_fields_mapping_exclude_codes(){
	return array(
		'dimensions_height_cm', 'dimensions_depth_cm', 'dimensions_height_m', 'dimensions_height_mm', 'dimensions_length_cm', 'dimensions_length_m', 'dimensions_length_mm', 'dimensions_width_cm', 'dimensions_width_m',
		'dimensions_width_mm', 'weight_g', 'weight_kg'
	);
}

/** Cette fonction permet de supprimer le mapping fait sur les champs avec un certain code
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param string $code Obligatoire, code de l'attribut
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ctr_cat_field_mapping_del_bycode( $ctr_id, $code ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	if( trim($code) == '' ){
		return false;
	}

	global $config;

	$ar_ccf = array();

	$res = ria_mysql_query('select ccf_id from ctr_cat_fields where ccf_ctr_id = '.$ctr_id.' and ccf_code = "'.addslashes( $code ).'"');
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_ccf[] = $r['ccf_id'];
		}
	}

	if( !$ar_ccf ){
		return true;
	}

	return ria_mysql_query('
		delete from ctr_cat_field_mapping
		where cfm_tnt_id = '.$config['tnt_id'].'
			and cfm_ccf_id in ( '.implode(', ', $ar_ccf).' )
			and cfm_ctr_id = '.$ctr_id.'
	');
}

/** Cette fonction permet de lié un champ avancé à plusieurs attribut ayant le même code
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param string $code Obligatoire, code de l'attribut
 *	@param int $fld_id Obligatoire, identifiant du champ avancé
 *	@return bool True si l'ajout  s'est correctement déroulé, False dans le cas contraire
 */
function ctr_cat_field_mapping_add_bycode( $ctr_id, $code, $fld_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	if( trim($code) == '' ){
		return false;
	}

	global $config;

	$ar_ccf = array();

	$res = ria_mysql_query('select ccf_id from ctr_cat_fields where ccf_ctr_id = '.$ctr_id.' and ccf_code = "'.addslashes( $code ).'"');
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_ccf[] = $r['ccf_id'];
		}
	}

	if( !$ar_ccf ){
		return true;
	}

	return ria_mysql_query('
		insert into ctr_cat_field_mapping
			( cfm_tnt_id, cfm_ctr_id, cfm_ccf_id, cfm_fld_id )
		select '.$config['tnt_id'].', '.$ctr_id.', ccf_id, '.$fld_id.'
		from ctr_cat_fields
		where ccf_ctr_id = '.$ctr_id.'
			and ccf_code = "'.addslashes( $code ).'"
	');
}

/** Cette fonction permet de récupérer la valeur pour un attribut selon le mapping réalisé.
 *	@param int $ctr_id Obligatoire, identifiant d'un comparateur de prix ou d'une place de marché
 *	@param int $ccf_id Obligatoire, identifiant d'un attribut
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@return string La valeur selon le mapping
 */
function ctr_cat_field_get_value_by_mapping( $ctr_id, $ccf_id, $prd_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}
	if( !is_numeric($ccf_id) || $ccf_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select cfm_fld_id as fld_id
		from ctr_cat_field_mapping
		where cfm_tnt_id = '.$config['tnt_id'].'
			and cfm_ccf_id = '.$ccf_id.'
			and cfm_ctr_id = '.$ctr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return '';
	}

	$r = ria_mysql_fetch_assoc( $res );
	return fld_object_values_get( $prd_id, $r['fld_id'] );
}

/** Cette fonction permet de récupérer la liste des articles rattachés à un attribut d'une place de marché selon son classement.
 *	@param int $ctr_id Obligatoire, identifiant d'une place de marché
 *	@param int $ccf_id Obligatoire, identifiant de l'attribut
 *	@return array Un tableau contenant tous les identifiants de produits rattachés à l'attribut en paramètre
 */
function ctr_cat_fields_get_products( $ctr_id, $ccf_id ){
	if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		return false;
	}

	if( !is_numeric($ccf_id) || $ccf_id<=0 ){
		return false;
	}

	global $config, $memcached;

	$ar_prds = array();

	// Récupère les catégories liées à cet attribut ainsi que les ids
	$r_cat = ria_mysql_query('select distinct ccf_id, ccf_cat_id from ctr_cat_fields where ccf_ctr_id = '.$ctr_id.' and ccf_id = "'.$ccf_id.'"');
	if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
		return $ar_prds;
	}

	$ar_ids  = array();
	$ar_cats = array();
	while( $cat = ria_mysql_fetch_assoc($r_cat) ){
		$ar_ids[]  = $cat['ccf_id'];
		$ar_cats[] = $cat['ccf_cat_id'];
	}

	$r_catalog = ctr_catalogs_get( $ctr_id, 0, 0, true );
	if( !$r_catalog || !ria_mysql_num_rows($r_catalog) ){
		return $ar_prds;
	}

	$ar_catalogs = array();

	$key_memcached = 'ctr_cat_fields_get_products:ctrid-'.$ctr_id;
	if( $get = $memcached->get($key_memcached) ){
		$ar_catalogs = $get;
	}else{
		while( $catalog = ria_mysql_fetch_assoc($r_catalog) ){
			$ar_catalogs[] = array(
				'prd_id' => $catalog['prd_id'],
				'fml_id' => ctr_catalogs_get_categorie( $ctr_id, $catalog['prd_id'], false, true ),
				'params' => $catalog['params']
			);
		}

		$memcached->set( $key_memcached, $ar_catalogs, 30 );
	}

	$no_cat_link = in_array( $ctr_id, array(CTR_CDISCOUNT) );

	foreach( $ar_catalogs as $one_prd ){
		$params = array();
		if( trim($one_prd['params']) != '' ){
			$params = json_decode( $one_prd['params'], true );
		}

		if( is_array($params) ){
			foreach( $params as $key => $val ){
				if( in_array($key, $ar_ids) && trim($val) != '' ){
					continue(2);
				}
			}
		}

		if( $no_cat_link || in_array($one_prd['fml_id'], $ar_cats) ){
			$ar_prds[] = $one_prd['prd_id'];
		}
	}

	return $ar_prds;
}

/// @}

/// \endcond
<?php

// Dépendances
require_once('db.inc.php');
require_once('define.inc.php');
require_once('obj_position.inc.php');
require_once('products.inc.php');
require_once('prd/relations.inc.php');
require_once('users.inc.php');
require_once('i18n.inc.php');
require_once('views.inc.php');
require_once('prd.stocks.inc.php');
require_once('ria.queue.inc.php');

// Sous-modules
require_once('fld/update-requests.inc.php');
require_once('fld/translates.inc.php');
require_once('fld/classes.inc.php');
require_once('fld/classes-fields.inc.php');
require_once('fld/units.inc.php');
require_once('fld/types.inc.php');
require_once('fld/categories.inc.php');
require_once('fld/restricted-values.inc.php');
require_once('fld/restricted-values-constraints.inc.php');
require_once('fld/models.inc.php');
require_once('fld/objects.inc.php');
require_once('fld/object-models.inc.php');
require_once('fld/object-values.inc.php');

/** \defgroup model_fields Champs avancés
 *
 *	Ce module comprend les fonctions nécessaires à la gestion de champs libres permettant de constituer une fiche avancée.
 *
 *	@{
 */

// \cond onlyria

/// Condition d'erreur de type "Le nom existe déjà"
define( 'ERR_NAME_EXISTS', -2 );

/// Condition d'erreur de type "Longueur minimale non atteinte"
define( 'ERR_FLD_MIN', -2 );

/// Condition d'erreur de type "Longueur maximale atteinte"
define( 'ERR_FLD_MAX', -3 );

/// Champs système
define( 'FLD_PRD_NAME', 225 );			// Identifiant riaShopSync 1
define( 'FLD_PRD_DESC', 226 );			// Identifiant riaShopSync 2
define( 'FLD_PRD_DESC_LONG', 304 );		// Identifiant riaShopSync 2
define( 'FLD_PRD_IMAGE', 227 );			// Identifiant riaShopSync 3
define( 'FLD_PRD_IMAGES', 245 );		// Identifiant riaShopSync 4
define( 'FLD_PRD_BRAND', 228 );			// Identifiant riaShopSync 5
define( 'FLD_BRD_URL', 229 );			// Identifiant riaShopSync 6
define( 'FLD_PRD_TAXCODE', 242 );		// Identifiant riaShopSync 14
define( 'FLD_PRD_WEIGHT_NET', 243 );	// Identifiant riaShopSync 15
define( 'FLD_PRD_WEIGHT_BRUT', 244 );	// Identifiant riaShopSync 16

define( 'FLD_PRD_PRICE_TTC', 230 );		// Identifiant riaShopSync 7
define( 'FLD_PRD_TVA_RATE', 231 );		// Identifiant riaShopSync 8
define( 'FLD_PRD_PRICE_ADH', 232 );		// Identifiant riaShopSync 9
define( 'FLD_PRD_PRICE_BS', 233 );		// Identifiant riaShopSync 10

define( 'FLD_SUPPLIER_REF', 234 );		// Identifiant riaShopSync 11
define( 'FLD_SUPPLIER_DELAY', 235 );	// Identifiant riaShopSync 12
define( 'FLD_SUPPLIER_BARCODE', 236 );	// Identifiant riaShopSync 13
define( 'FLD_PRD_SUPPLIER_PACKING', 237 ); // Identifiant riaShopSync 17
define( 'FLD_PRD_SUPPLIER_QEC', 238 );	// Identifiant riaShopSync 18
define( 'FLD_PRD_SUPPLIER_CONVERSION', 239 ); // Identifiant riaShopSync 19

define( 'FLD_PRD_STOCK_MINI', 240 );	// Identifiant riaShopSync 20
define( 'FLD_PRD_STOCK_MAXI', 241 );	// Identifiant riaShopSync 21

define( 'FLD_PRD_HIERARCHY', 311 );		// champs hierarchy des produits

define( 'FLD_PRD_ACCESSOIRE', 316 );	// pour les accessoires
define( 'FLD_PRD_OPTION', 317 );		// pour les options
define( 'FLD_PRD_PIECE', 318 );			// pour les pieces détachés
// \endcond

// \cond onlyria
/**	Cette fonction retourne l'identifiant riaShopSync d'un champ de la plateforme
 *	@param int $fld Obligatoire, identifiant d'un champ de la plateforme
 *	@return int|bool l'identifiant riaShopSync du champ, ou false en cas d'erreur
 */
function fld_sync_id( $fld ){
	$sync_id = false;
	switch( $fld ){
		case FLD_PRD_NAME:
			$sync_id = 1;
			break;
		case FLD_PRD_DESC:
			$sync_id = 2;
			break;
		case FLD_PRD_IMAGE:
			$sync_id = 3;
			break;
		case FLD_PRD_IMAGES:
			$sync_id = 4;
			break;
		case FLD_PRD_BRAND:
			$sync_id = 5;
			break;
		case FLD_BRD_URL:
			$sync_id = 6;
			break;
		case FLD_PRD_PRICE_TTC:
			$sync_id = 7;
			break;
		case FLD_PRD_TVA_RATE:
			$sync_id = 8;
			break;
		case FLD_PRD_PRICE_ADH:
			$sync_id = 9;
			break;
		case FLD_PRD_PRICE_BS:
			$sync_id = 10;
			break;
		case FLD_SUPPLIER_REF:
			$sync_id = 11;
			break;
		case FLD_SUPPLIER_DELAY:
			$sync_id = 12;
			break;
		case FLD_SUPPLIER_BARCODE:
			$sync_id = 13;
			break;
		case FLD_PRD_TAXCODE:
			$sync_id = 14;
			break;
		case FLD_PRD_WEIGHT_NET:
			$sync_id = 15;
			break;
		case FLD_PRD_WEIGHT_BRUT:
			$sync_id = 16;
			break;
		case FLD_PRD_SUPPLIER_PACKING:
			$sync_id = 17;
			break;
		case FLD_PRD_SUPPLIER_QEC:
			$sync_id = 18;
			break;
		case FLD_PRD_SUPPLIER_CONVERSION:
			$sync_id = 19;
			break;
		case FLD_PRD_STOCK_MINI:
			$sync_id = 20;
			break;
		case FLD_PRD_STOCK_MAXI:
			$sync_id = 21;
			break;
	}
	return $sync_id;
}
// \endcond

/// \cond onlyria
/** Détermine si un champ libre est associé à un locataire ou s'il est générique
 *	@param int $id Identifiant du champ libre
 *	@return bool True s'il est rattaché à un locataire, False sinon
 */
function fld_fields_is_tenant_linked( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select fld_tnt_id as tenant from fld_fields
		where fld_id='.$id.' and ( fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].' )
	');

	if( $res===false || !ria_mysql_num_rows($res) ) return false;
	$f = ria_mysql_fetch_array($res);

	return $f['tenant']>0;
}

/** Détermine si un champ libre doit être push dans pubsub ou non
 *	@param int $id Identifiant du champ libre
 *	@return bool True s'il est a synchro, False sinon
 */
function fld_fields_is_push_riashopsync( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select fld_push_riashopsync as push_riashopsync from fld_fields
		where fld_id='.$id.' and ( fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].' )
	');

	if( $res===false || !ria_mysql_num_rows($res) ) return false;
	$f = ria_mysql_fetch_array($res);

	return $f['push_riashopsync']>0;
}
/// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs champs personnalisés,
 *	éventuellement filtrés en fonction des paramètres optionnels fournis.
 *
 *	@param int $id Facultatif, identifiant d'un champ sur lequel filtrer le résultat
 *	@param int $cat Facultatif, identifiant d'une catégorie sur laquelle filtrer le résultat
 *	@param int $mdl Facultatif, identifiant d'un modèle de saisie sur lequel filtrer le résultat (-1 pour récupérer uniquement les orphelins, -2 pour tout récupérer)
 *	@param int $type Facultatif, identifiant d'un type de champ sur lequel filtrer le résultat (ou tableau d'identifiants)
 *	@param int $unit Facultatif, identifiant d'une unité de mesure sur laquelle filtrer le résultat
 *	@param int $obj Facultatif, identifiant d'un objet de la classe correspondante sur lequel filtrer le résultat (ou tableau contenant COUNT_OBJ_ID identifiants maximums, correspondant à une clé composée)
 *	@param bool $empty Facultatif, limite le résultat aux champs vides (true), ou aux champs remplis (false)
 *	@param array $sort Facultatif, Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : "prc-priority", "fld_name", "fld-pos".
 *			- "prc-priority" sera pris en compte que si une valeur de $prc_apply est définis
 *			- "fld-pos" sera pris en compte que si une valeur de $class est définis
 *	@param bool $include_system Facultatif, indique si l'on souhaite charger les champs utilisateur (false, valeur par défaut) ou tous les champs (y compris les champs système) (true)
 *	@param array $exclude Facultatif, tableau d'identifiants de champs à exclure du résultat
 *	@param bool $is_sync Facultatif, null pour tout retourner, true pour retourner uniquement les champs synchronisés, false pour retourner les champs non synchronisés
 *	@param int|array $class Facultatif, identifiant de classe (ou tableau) sur lequel filtrer le résultat. La valeur par défaut est null (pas de filtre)
 *	@param bool $prc_apply Facultatif, Détermine si le ou les champs libres sont disponibles individuellement pour un critère de tarification (null par défaut, retourne tout)
 *	@param bool $include_physical Facultatif, inclut ou non les champs ayant une présence physique
 *	@param bool $tva_apply Facultatif, Détermine si le ou les champs libres sont disponibles individuellement pour un critère d'exonération de tva (null par défaut, retourne tout)
 *	@param bool $include_generic Facultatif, inclut ou non les champs génériques (n'étant pas rattachés à un tenant particulier) (null par défaut, retourne tout)
 *	@param string $lng code ISO 639-1 de la langue, par défaut la langue du site est utilisée, disponible que si le paramètre $obj est renseigné
 *	@param bool $for_use_access Optionnel, si True ou False, détermine si le champ peut être un critère de droit d'accès
 *	@param bool $admin Optionnel, par défaut il ne s'agit par un appel depuis l'espace d'administration, mettre true pour que ce soit le cas et donc désactiver la récupération de la valeur par défaut
 *	@param bool $for_use_restriction Optionnel, permet de recupérer que les champs visible pour les restrictions
 *
 *	@return resource|false un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- tenant : identifiant de locataire
 *			- id : identifiant du champ
 *			- ref : référence du champ utilisée dans la gestion commerciale
 *			- name : désignation du champ
 *			- desc : description du champ
 *			- type_id : identifiant du type de champ
 *			- type_name : désignation du type de champ
 *			- related_class : dans le cas où fld_type_id = 11 (pointeur), détermine la classe de l'élément pointé
 *			- unit_id : identifiant de l'unité du champ
 *			- unit_symbol : symbôle de représentation de l'unité
 *			- unit_name : désignation de l'unité du champ
 *			- min : valeur minimale acceptée par le champ (dépendant du type)
 *			- max : valeur maximale acceptée par le champ (dépendant du type)
 *			- precision : nombre de chiffres affichés après la virgule (champs de type flottant uniquement)
 *			- cat_id : identifiant de la catégorie de classement du champ
 *			- cat_name : désignation de la catégorie de classement du champ
 *			- obj_value : valeur enregistrée dans le champ. Cette information n'est disponible que si le paramètre $obj est renseigné.
 *			- is-sync : détermine si le champ est synchronisé
 *			- pos : position d'affichage du champ dans sa catégorie
 *			- cls_id : identifiant de la classe du champ
 *			- cls_name : Nom de la classe
 *			- cls_physical_name : Nom de la table physique
 *			- physical : Détermine si le champ existe physiquement
 *			- physical-name : Nom du champ physique
 *			- old_txt_type : Si oui ou non l'ancien système est utilisé pour les champs de type texte long
 *			- is_mandatory Si oui ou non le champ avancé est obligatoire
 *			- used_in_restrictions : détermine si le champs est utilisé dans les restrictions
 *			- used_access : détermine si le champs est accessible aux utilisateur (si non dans ce cas le champ est au mieux en lecture seule dans l'administration)
 */
function fld_fields_get( $id=0, $cat=0, $mdl=0, $type=0, $unit=0, $obj=0, $empty=null, $sort=array(), $include_system=false, $exclude=array(), $is_sync=null, $class=null, $prc_apply=null, $include_physical=false, $tva_apply=null, $include_generic=null, $lng=false, $for_use_access=null, $admin=false, $for_use_restriction=null ){
	global $config;

	// Contrôle des paramètres d'appel à la fonction
	if( !is_array($id) && (!is_numeric($id) || $id<0) ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;
	if( !is_numeric($mdl) || $mdl<-2 ) return false;
	if( is_array($type) ){
		foreach( $type as $t ){
			if( !is_numeric($t) || $t<=0 ) return false;
		}
	}else{
		if( !is_numeric($type) || $type<0 ) return false;
		if( $type>0 ) $type = array($type);
		else $type = array();
	}
	if( !is_numeric($unit) || $unit<0 ) return false;

	if( $class !== null ){
		if( is_array($class) && !count($class) ){
			$class = null;
		}else{
			$class = control_array_integer( $class, false );
			if( $class === false ){
				return false;
			}
		}

		if( is_array($class) && count($class) ){
			foreach( $class as $c ){
				if( !fld_classes_exists($c) ){
					return false;
				}
			}
		}
	}

	if( is_array($obj) ){

		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}

	}else{

		if( !is_numeric($obj) || $obj<0 ) return false;

		if( $obj==0 ){
			$obj = array();
		}else{
			$obj = array( $obj );
		}
	}

	$other_lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) && strtolower($lng)!=$config['i18n_lng'];
	$sql = '
		select fld_tnt_id as tenant, fld_id as id, fld_ref as ref, fld_name as name, fld_desc as "desc",
			type_id, type_name, fld_related_class as "related_class", cat_id, cat_name, unit_id, unit_symbol, unit_name,
			fld_min as "min", fld_max as "max", fld_precision as "precision", fld_is_sync as "is-sync", cls_id, cls_name, cls_physical_name,
			fld_is_physical as physical, fld_physical_name as "physical-name", fld_old_txt_type as old_txt_type,
			fld_is_mandatory as is_mandatory, fld_used_in_restrictions as used_in_restrictions, fld_pdf_under_name as "under-name",
			fld_pos, fld_used_access as used_access
	';
	if( $mdl>0 ){
		$sql .= ', mf_fld_pos as "pos" ';
	}else{
		$sql .= ', null as "pos" ';
	}
	if( sizeof($obj) ){
		if( $other_lng && !$admin ){
			$sql .= ', ifnull( v1.pv_value, v2.pv_value ) as obj_value';
			$sql .= ', ifnull( v1.pv_obj_id_0, v2.pv_obj_id_0 ) as obj_id_0, ifnull( v1.pv_obj_id_1, v2.pv_obj_id_1 ) as obj_id_1, ifnull( v1.pv_obj_id_2, v2.pv_obj_id_2 ) as obj_id_2, ifnull( v1.pv_lng_code, v2.pv_lng_code ) as lng_code ';
		}
		else{
			$sql .= ', pv_value as obj_value';
			$sql .= ', pv_obj_id_0 as obj_id_0, pv_obj_id_1 as obj_id_1, pv_obj_id_2 as obj_id_2, pv_lng_code as lng_code ';
		}
	}
	if( $prc_apply!=null ) $sql .= ', ppf_priority as "prc-priority"';

	if( count($class) && array_key_exists('fld-pos',$sort) && is_array($sort) && sizeof($sort) ){
		$sql .= ', cfp_pos';
	}
	// le left join avec la catégorie ne retournera pas forcément de catégorie pour les champs génériques
	// idem pour l'unité (dependra du tenant courant)
	$sql .= '
		from fld_fields
			inner join fld_classes on ( (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].') and cls_id=fld_cls_id)
			inner join fld_types on (fld_type_id=type_id)
			left join fld_categories on ( fld_cat_id=cat_id and cat_tnt_id='.$config['tnt_id'].' )
			left join fld_units on ( fld_unit_id=unit_id and unit_tnt_id='.$config['tnt_id'].' )
	';

	$load_obj_values = false;


	if( $mdl<0 ){

		if( sizeof($obj) ){

			$part_sql = $part_sql1 = $part_sql2 = '';
			$i = 0;
			while( $i<sizeof($obj) ){
				$part_sql .= 'and pv_obj_id_'.$i.'='.$obj[$i].' ';
				$part_sql1 .= 'and v1.pv_obj_id_'.$i.'='.$obj[$i].' ';
				$part_sql2 .= 'and v2.pv_obj_id_'.$i.'='.$obj[$i].' ';
				$i++;
			}

			if( $other_lng ){
				if( !$admin ){
					$sql .= '
						left join fld_object_values as v1 on (fld_id=v1.pv_fld_id and v1.pv_tnt_id='.$config['tnt_id'].' '.$part_sql1.' and v1.pv_lng_code=\''.strtolower($lng).'\')
						left join fld_object_values as v2 on (fld_id=v2.pv_fld_id and v2.pv_tnt_id='.$config['tnt_id'].' '.$part_sql2.' and v2.pv_lng_code=\''.$config['i18n_lng'].'\')
					';
				} else {
					$sql .= '
						inner join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' '.$part_sql.' and pv_lng_code=\''.strtolower($lng).'\')
					';
				}
			}else{
				$sql .= '
					inner join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' '.$part_sql.' and pv_lng_code=\''.$config['i18n_lng'].'\')
				';
			}

			$load_obj_values = true;
		}

	}elseif( $mdl>0 || sizeof($obj) ){

		if( $mdl>0 ){
			$sql .= ' inner join fld_model_fields on (fld_id=mf_fld_id and mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl;

			if( sizeof($obj) ){

				$part_sql = '';

				$i = 0;
				while( $i<sizeof($obj) ){
					$part_sql .= ' and pm_obj_id_'.$i.'='.$obj[$i].' ';
					$i++;
				}

				$sql .= ' and mf_mdl_id in ( select pm_mdl_id from fld_object_models where pm_tnt_id='.$config['tnt_id'].' '.$part_sql.' )';

			}

			$sql .= ') inner join fld_models on (mf_mdl_id=mdl_id and mdl_tnt_id='.$config['tnt_id'].') ';
		}

		if( sizeof($obj) ){

			$part_sql = $part_sql1 = $part_sql2 = '';

			$i = 0;
			while( $i<sizeof($obj) ){
				$part_sql .= 'and pv_obj_id_'.$i.'='.$obj[$i].' ';
				$part_sql1 .= 'and v1.pv_obj_id_'.$i.'='.$obj[$i].' ';
				$part_sql2 .= 'and v2.pv_obj_id_'.$i.'='.$obj[$i].' ';
				$i++;
			}

			if( $other_lng ){
				if( !$admin ){
					$sql .= '
						left join fld_object_values as v1 on (fld_id=v1.pv_fld_id and v1.pv_tnt_id='.$config['tnt_id'].' '.$part_sql1.' and v1.pv_lng_code=\''.strtolower($lng).'\')
						left join fld_object_values as v2 on (fld_id=v2.pv_fld_id and v2.pv_tnt_id='.$config['tnt_id'].' '.$part_sql2.' and v2.pv_lng_code=\''.$config['i18n_lng'].'\')
					';
				} else {
					$sql .= '
						left join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' '.$part_sql.' and pv_lng_code=\''.strtolower($lng).'\')
					';
				}
			}else{
				$sql .= ' left join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' '.$part_sql.' and pv_lng_code=\''.$config['i18n_lng'].'\')';
			}

			$load_obj_values = true;
		}

	}elseif( count($class) && array_key_exists('fld-pos',$sort) && is_array($sort) && sizeof($sort) ){
		$sql .= 'left join fld_classes_fields_priority on (cfp_tnt_id='.$config['tnt_id'].' and cfp_cls_id=cls_id and cfp_fld_id=fld_id)';
	}

	if( $empty!==null && !$load_obj_values ){
		if( $other_lng ){
			if( !$admin ){
				$sql .= '
					left join fld_object_values as v1 on (fld_id=v1.pv_fld_id and v1.pv_tnt_id='.$config['tnt_id'].' and v1.pv_lng_code=\''.strtolower($lng).'\')
					left join fld_object_values as v2 on (fld_id=v2.pv_fld_id and v2.pv_tnt_id='.$config['tnt_id'].' and v2.pv_lng_code=\''.$config['i18n_lng'].'\')
				';
			} else {
				$sql .= '
					inner join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.strtolower($lng).'\')
				';
			}
		}else{
			$sql .= '
				inner join fld_object_values on (fld_id=pv_fld_id and pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.$config['i18n_lng'].'\')
			';
		}
	}

	if( $prc_apply!=null ){
		$sql .= ' inner join prc_price_fields on ( ppf_tnt_id='.$config['tnt_id'].' and (ppf_fld_id=fld_id or ppf_fld_id_1=fld_id or ppf_fld_id_2=fld_id or ppf_fld_id_3=fld_id ) )';
	}
	if( $tva_apply!=null ){
		$sql .=  ' inner join prc_tva_fields on ( ( ptf_tnt_id='.$config['tnt_id'].' and ptf_fld_id=fld_id ) or (ptf_tnt_id=0 and ptf_fld_id=fld_id) )';
	}

	$sql .= ' where fld_date_deleted is null ';

	if( $include_generic===null ){
		$sql .= ' and (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') ';
	}elseif( !$include_generic ){
		$sql .= ' and fld_tnt_id='.$config['tnt_id'].' ';
	}else{
		$sql .= ' and fld_tnt_id=0  ';
	}

	if( !$include_system && $id==0 ) $sql .= ' and fld_is_system=0';

	if( $id == 0 ){
		if( $include_physical !== null ){
			if( $include_physical ){
				$sql .= ' and fld_is_physical = 1';
			}else{
				$sql .= ' and fld_is_physical = 0';
			}
		}
	}

	if( is_array($id) ){
		$sql .= ' and fld_id in ('.implode( ',', $id ).')';
	}elseif( $id>0 ){
		$sql .= ' and fld_id='.$id;
	}
	if( $cat>0 ) $sql .= ' and fld_cat_id='.$cat;
	if( sizeof($type) ) $sql .= ' and fld_type_id in ('.implode(', ', $type).')';
	if( $unit>0 ) $sql .= ' and fld_unit_id='.$unit;

	if( $empty!==null ){
		if( $other_lng && !$admin ){
			if( $empty ) $sql .= ' and ifnull( v1.pv_value, v2.pv_value ) is null';
			else $sql .= ' and ifnull( v1.pv_value, v2.pv_value ) is not null';
		}else{
			if( $empty ) $sql .= ' and pv_value is null';
			else $sql .= ' and pv_value is not null';
		}
	}
	if( is_array($exclude) && sizeof($exclude) ){
		$sql .= ' and fld_id not in ('.implode(',',$exclude).')';
	}

	if( $is_sync!=null ){
		if( $is_sync==true ){
			$sql .= ' and fld_is_sync=1';
		}elseif( $is_sync==false ){
			$sql .= ' and fld_is_sync=0';
		}
	}

	if( is_array($class) && count($class) ){
		$sql .= ' and fld_cls_id in ('.implode( ',',$class ).')';
	}

	if( $for_use_access!==null ){
		if( $for_use_access ){
			$sql .= ' and fld_used_access=1';
		}else{
			$sql .= ' and fld_used_access=0';
		}
	}

	if( $for_use_restriction!==null ){
		if( $for_use_restriction ){
			$sql .= ' and fld_used_in_restrictions=1';
		}else{
			$sql .= ' and fld_used_in_restrictions=0';
		}
	}

	if( $mdl==-1 ){
		$sql .= ' and not exists (
			select mf_fld_id
			from fld_model_fields
				join fld_models on (mdl_tnt_id = mf_tnt_id and mdl_id = mf_mdl_id)
			where mf_tnt_id='.$config['tnt_id'].' and mf_fld_id=fld_id
				and mdl_date_deleted is null
		)';
	}

	if( $empty!==null && !sizeof($obj) ){
		$sql .= ' group by fld_id';
	}

	if( $mdl>0 || ( sizeof($obj) && $mdl>0 ) ){
		if( in_array($config['tnt_id'], [977, 998, 1043]) ){
			$sql .= ' order by ifnull(cat_pos, 999) asc, mdl_pos, mf_fld_pos, fld_name';
		}else{
			$sql .= ' order by mdl_pos, mf_fld_pos, fld_name';
		}
	}else{
		$sort_final = array();

		if( is_array($sort) && count($sort) ){
			foreach( $sort as $col=>$dir ){
				$col = strtolower(trim($col));
				$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';
				switch( $col ){
					case 'prc-priority':{
						if( $prc_apply!=null ){
							$sort_final[] = 'ppf_priority '.$dir.' ';
						}
						break;
					}
					case 'fld_name' : {
						$sort_final[] = 'fld_name '.$dir.' ';
						break;
					}
					case 'fld-pos' : {
						$sort_final[] = 'cfp_pos '.$dir.' ';
						break;
					}
				}
			}
		}else{
			array_push( $sort_final, 'cat_pos asc, fld_name asc');
		}

		if( is_array($sort_final) && sizeof($sort_final) )
			$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
	}

	return $res;
}

/// \cond onlyria
/** Cette fonction permet de vérifier qu'un champ libre éxiste
 * @param string $name Obligatoire, désignation du champ libre
 * @param int $class Obligatoire, identifiant d'une classe
 *
 * @return int l'identifiant du champ si il éxiste, false dans le cas contraire
 */
function fld_field_name_exists( $name, $class ){
	global $config;

	if( trim($name) == "" ){
		return false;
	}
	if( !is_numeric($class) || $class <= 0 ){
		return false;
	}

	$name = ucfirst($name);

	$res = ria_mysql_query('
		select fld_id
		from fld_fields
		where fld_tnt_id = '.$config['tnt_id'].'
			and fld_name = "'.$name.'"
			and fld_cls_id = '.$class.'
			and fld_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'fld_id');
}

/// \endcond

/// \cond onlyria
/** Cette fonction récupère la classe d'objet d'un champ libre
 *	@param int $fld Identifiant du champ libre
 *	@return bool False en cas d'erreur, identifiant de la classe sinon
 */
function fld_fields_get_class( $fld ){
	global $config;

	if( !is_numeric($fld) || $fld<=0 ) return false;

	static $prev_fld = 0;
	static $prev_class = 0;

	if( $fld==$prev_fld ){
		return $prev_class;
	}

	$result = ria_mysql_query('
		select fld_cls_id
		from fld_fields
		where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and
		fld_id='.$fld.'
	');

	if( $result==false || !ria_mysql_num_rows($result) ) return false;

	$prev_fld = $fld;
	$prev_class = ria_mysql_result( $result,0,0 );

	return $prev_class;
}
/// \endcond

/// \cond onlyria
/** Détermine si un champ libre a une existence physique
 *	@param int $id Identifiant du champ libre
 *	@return bool True si le champ est physique, False sinon
 */
function fld_fields_is_physical( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select fld_is_physical
		from fld_fields
		where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and
		fld_id='.$id.' and fld_date_deleted is null and
		fld_cls_id in (
			select cls_id from fld_classes
			where (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].') and cls_date_deleted is null and cls_is_physical=1
		)
	');

	if( !$res || !ria_mysql_num_rows($res ) ) return false;

	return ria_mysql_result( $res,0,0 );
}
/// \endcond

/// \cond onlyria
/** Détermine le nom physique d'un champ libre, si celui-ci l'est
 *	@param int $id Identifiant du champ libre
 *	@return string|bool Nom physique du champ, False en cas d'erreur
 */
function fld_fields_get_physical_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select fld_physical_name
		from fld_fields join fld_classes on ( (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].') and fld_cls_id=cls_id )
		where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and cls_date_deleted is null
		and fld_id='.$id.' and fld_is_physical=1 and cls_is_physical=1
	');

	if( !$res || !ria_mysql_num_rows($res ) ) return false;

	return ria_mysql_result( $res,0,0 );
}
/// \endcond

/// \cond onlyria
/** Détermine l'identifiant d'un champ libre selon le nom physique auquel il fait référence
 *	@param int $cls_id Identifiant de classe (la table physique dans lequel est contenu le champ)
 *	@param string $physical_name Nom physique du champ
 *	@return string|bool Identifiant du champ libre ou False en cas d'erreur
 */
function fld_fields_get_id_from_physical( $cls_id,$physical_name ){
	global $config;

	if( !fld_classes_is_physical($cls_id) ){
		return false;
	}

	$physical_name = trim( $physical_name );
	if( $physical_name=='' ){
		return false;
	}

	$res = ria_mysql_query('
		select fld_id
		from fld_fields
		where fld_date_deleted is null
			and (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].')
			and fld_is_physical=1
			and fld_physical_name=\''.addslashes($physical_name).'\'
			and fld_cls_id='.$cls_id.'
	');

	if( $res===false || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res,0,0 );
}
/// \endcond

/// \cond onlyria
/** Cette fonction retourne la liste des modèles de saisie contenant un champ avancé, selon une classe d'objet.
 *	@param int|array $fld Obligatoire, identifiant ou tableau d'identifiants de champ avancé
 *	@return bool False si l'un des paramètres est omis ou faux
 *	@return array Un tableau MySQL contenant :
 *					- id : identifiant du modèle
 *					- name : nom du modèle
 *					- desc : description du modèle
 *					- cls_id : classe d'objet pouvant utilisé le modèle
 */
function fld_fields_get_models( $fld ){
	global $config;

	if( !is_array($fld) ){
		if( !is_numeric($fld) || $fld<=0 ){
			return false;
		}
		$fld = array( $fld );
	}else{
		if( !sizeof($fld) ){
			return false;
		}
		foreach( $fld as $f ){
			if( !is_numeric($f) || $f<=0 ){
				return false;
			}
		}
	}

	return ria_mysql_query('
		select mdl_id as id, mdl_name as name, mdl_desc as "desc", mdl_cls_id as cls_id
		from fld_models
			join fld_model_fields on (mdl_tnt_id=mf_tnt_id and mdl_id=mf_mdl_id)
		where mdl_tnt_id='.$config['tnt_id'].'
			and mf_fld_id in ('.implode(', ', $fld).')
		group by mdl_id
	');

}
/// \endcond

/// \cond onlyria
/** Détermine si un champ libre est utilisable comme condition de tarification
 *	@param int $fld_id identifiant du champ libre
 *
 *	@return bool True si le champ est utilisable, False sinon
 */
function fld_fields_is_prices_apply( $fld_id ){
	global $config;

	if( !is_numeric($fld_id) || $fld_id<=0 ) return false;

	$res = ria_mysql_query('
		select 1 from prc_price_fields
		where ppf_tnt_id='.$config['tnt_id'].'
			and (
				ppf_fld_id='.$fld_id.'
				or ppf_fld_id_1='.$fld_id.'
				or ppf_fld_id_2='.$fld_id.'
				or ppf_fld_id_3='.$fld_id.'
			)
	');

	if( $res===false ) return false;

	return ria_mysql_num_rows( $res );
}
/// \endcond

/// \cond onlyria
/** Détermine si un champ libre est utilisable comme condition d'exonération
 *	@param int $id identifiant du champ libre
 *
 *	@return bool True si le champ est utilisable, False sinon
 */
function fld_fields_is_tvas_apply( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1 from prc_tva_fields
		where ( ptf_tnt_id='.$config['tnt_id'].' or ptf_tnt_id=0 )
			and ptf_fld_id='.$id
	);

	if( $res===false ){
		return false;
	}

	return ria_mysql_num_rows( $res );
}
/// \endcond

/// \cond onlyria
/** Cette fonction permet de déterminer si un champ avancé est obligatoire ou non.
 * 	@param int $fld_id Identifiant d'un champ avancé
 * 	@return bool True si le champ est obligatoire, False dans le cas contraire ou si le paramètre obligatoire est omis ou faux
 */
function fld_fields_is_mandatory( $fld_id ){
	global $config;

	if (!is_numeric($fld_id) || $fld_id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from fld_fields
		where fld_tnt_id in (0, '.$config['tnt_id'].')
			and fld_id ='.$fld_id.'
			and fld_is_mandatory = 1
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
/// \endcond

/**	Cette fonction permet la vérification d'un identifiant de champ personnalisé.
 *	@param int $id Obligatoire, identifiant à vérifier
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide ou si une erreur se produit
 */
function fld_fields_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('
		select fld_id from fld_fields
		where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and fld_id='.$id
	));
}

/// \cond onlyria
/**	Permet l'ajout d'un champ personnalisé.
 *	Cette fonction ne concerne que les champs spécifiques à un locataire. Pour les champs globaux, utiliser directement du SQL ou des scripts
 *	@param string $name Obligatoire, désignation du champ (chaîne vide refusée)
 *	@param string $desc Obligatoire, description du champ (chaîne vide acceptée)
 *	@param int $type Obligatoire, identifiant du type de champ
 *	@param int $cat Obligatoire, identifiant de la catégorie du champ
 *	@param int $unit Obligatoire, identifiant de l'unité (0 pour aucune)
 *	@param int $min Obligatoire, valeur minimale acceptée par le champ (chaîne vide pour aucune)
 *	@param int $max Obligatoire, valeur maximale acceptée par le champ (chaîne vide pour aucune)
 *	@param int $precision Obligatoire, nombre de chiffres acceptés après la virgule (dépendant du type de champ)
 *	@param bool $is_sync Facultatif, détermine si le champ sera synchronisé
 *	@param int $class Facultatif, identifiant de la classe. La valeur par défaut est 1 (produits)
 *	@param bool $is_mandatory Facultatif, permet de définir si un champ avancé est obligatoire ou non
 *	@param bool $old_text_type Facultatif, permet de définir si le champs utilise tinyMce
 *	@param string $ref Optionnel, référence du champ
 *
 *	@return int l'identifiant attribué au nouveau champ en cas de succès
 *	@return bool false en cas d'échec
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par un autre champ de la même classe
 */
function fld_fields_add( $name, $desc, $type, $cat, $unit, $min, $max, $precision, $is_sync=false, $class=CLS_PRODUCT, $is_mandatory=false, $old_text_type=0, $ref='' ){

	if( !trim($name) ) return false;
	if( !fld_types_exists($type) ) return false;
	if( !fld_classes_exists($class) ) return false;

	if( fld_categories_get_class($cat)!=$class ) return false;

	if( $unit!='' && !fld_units_exists($unit) ) return false;

	if( $min!='' && !is_numeric($min) ) return false;
	if( $max!='' && !is_numeric($max) ) return false;
	if( $precision!='' && !is_numeric($precision) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	global $config;

	// Vérifie que le nom du champ n'est pas déjà utilisé
	$r_exists = ria_mysql_query('
		select fld_id from fld_fields
		where fld_tnt_id = '.$config['tnt_id'].' and fld_date_deleted is null
			and fld_cls_id = '.$class.' and fld_cat_id = '.$cat.' and fld_name = "'.addslashes($name).'"
	');
	if( $r_exists && ria_mysql_num_rows($r_exists) ){
		return ERR_NAME_EXISTS;
	}

	$fields = array();
	$values = array();

	$fields[] = 'fld_tnt_id';
	$values[] = $config['tnt_id'];
	$fields[] = 'fld_name';
	$values[] = '\''.addslashes($name).'\'';
	$fields[] = 'fld_desc';
	$values[] = '\''.addslashes($desc).'\'';
	$fields[] = 'fld_type_id';
	$values[] = $type;
	$fields[] = 'fld_cat_id';
	$values[] = $cat;
	$fields[] = 'fld_cls_id';
	$values[] = $class;
	$fields[] = 'fld_is_mandatory';
	$values[] = $is_mandatory ? '1' : '0';
	$fields[] = 'fld_old_txt_type';
	$values[] = $old_text_type ? '1' : '0';
	$fields[] = 'fld_ref';
	$values[] = '"'.addslashes( $ref ).'"';

	if( $unit!='' ){
		$fields[] = 'fld_unit_id';
		$values[] = $unit;
	}
	if( $min!='' ){
		$fields[] = 'fld_min';
		$values[] = $min;
	}
	if( $max!='' ){
		$fields[] = 'fld_max';
		$values[] = $max;
	}
	if( $precision!='' ){
		$fields[] = 'fld_precision';
		$values[] = $precision;
	}
	if( $is_sync==true ){
		$fields[] = 'fld_is_sync';
		$values[] = '1';
	}

	$res = ria_mysql_query('
		insert into fld_fields
			('.implode( ',',$fields ).')
		values
			('.implode( ',',$values ).')
	');

	if( $res ){
		$fld_id = ria_mysql_insert_id();
	}else{
		return false;
	}

	fld_classes_fields_priority_add( $class, $fld_id );

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	//Met a jour la date de moficiation de la classe
	fld_classes_set_date_modified( $class );

	return $fld_id;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la mise à jour d'un champ personnalisé.
 *	Cette fonction ne concerne que les champs spécifiques à un locataire. Toute modification sur un champ générique sera refusé.
 *	@param int $id Obligatoire, identifiant du champ à mettre à jour
 *	@param string $name Obligatoire, désignation du champ (chaîne vide refusée)
 *	@param string $desc Obligatoire, description du champ (chaîne vide acceptée)
 *	@param $type Obligatoire, identifiant du type de champ
 *	@param int $cat Obligatoire, identifiant de la catégorie du champ
 *	@param $unit Obligatoire, identifiant de l'unité (0 pour aucune)
 *	@param $min Obligatoire, valeur minimale acceptée par le champ (chaîne vide pour aucune)
 *	@param $max Obligatoire, valeur maximale acceptée par le champ (chaîne vide pour aucune)
 *	@param $precision Obligatoire, nombre de chiffres acceptés après la virgule (dépendant du type de champ)
 *	@param bool $is_sync Facultatif, détermine si le champ est synchronisé. Si null, la valeur n'est pas modifiée.
 *	@param $is_mandatory Facultatif, permet de définir si un champ avancé est obligatoire ou non
 *	@param $old_type_text Facultatif, permet de définir si le champs utilise tinyMce
 *	@param $ref Optionnel, référence du champ
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par un autre champ de la classe
 */
function fld_fields_update( $id, $name, $desc, $type, $cat, $unit, $min, $max, $precision, $is_sync=null, $is_mandatory=false, $old_type_text=null, $ref=null  ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$fld_class = fld_fields_get_class( $id );

	if( !fld_fields_is_tenant_linked($id) ) return false;

	if( !trim($name) ) return false;
	if( !fld_types_exists($type) ) return false;

	if( $fld_class!=fld_categories_get_class( $cat ) ) return false;

	if( $unit!='' && !fld_units_exists($unit) ) return false;

	if( $min!='' && !is_numeric($min) ) return false;
	if( $max!='' && !is_numeric($max) ) return false;
	if( $precision!='' && !is_numeric($precision) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	// Vérifie que le nom du champ n'est pas déjà utilisé
	$exists = ria_mysql_query('select fld_id from fld_fields where fld_tnt_id='.$config['tnt_id'].' and fld_cls_id='.$fld_class.' and fld_date_deleted is null and fld_name=\''.addslashes($name).'\' and fld_id!='.$id);
	if( ria_mysql_num_rows($exists) ) return ERR_NAME_EXISTS;

	$str_sql_sync = '';
	if( $is_sync!=null ){
		if( $is_sync==true )
			$str_sql_sync = ', fld_is_sync=1 ';
		if( $is_sync==false )
			$str_sql_sync = ', fld_is_sync=0 ';
	}

	$str_sql_old_txt = '';
	if( $old_type_text!==null ){
		if( $old_type_text==true )
			$str_sql_old_txt = ', fld_old_txt_type=1 ';
		if( $old_type_text==false )
			$str_sql_old_txt = ', fld_old_txt_type=0 ';
	}

	$res = ria_mysql_query('
		update fld_fields set
			fld_name=\''.addslashes($name).'\',
			fld_desc=\''.addslashes($desc).'\',
			fld_type_id='.$type.',
			fld_cat_id='.$cat.',
			fld_unit_id='.( $unit!='' ? $unit : 'null' ).',
			fld_min='.( $min!='' ? $min : 'null' ).',
			fld_max='.( $max!='' ? $max : 'null' ).',
			fld_is_mandatory='.($is_mandatory ? '1' : '0').',
			fld_precision='.( $precision!='' ? $precision : 'null' ).'
			'.( $ref !== null ? ' , fld_ref = "'.addslashes($ref).'"' : '' ).'
			'.$str_sql_sync.'
			'.$str_sql_old_txt.'
		where fld_tnt_id='.$config['tnt_id'].' and fld_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/** Cette fonction modifie la valeur de fld_is_sync d'un champ libre
 *	@param int $fld Identifiant du champ libre
 *	@param bool $is_sync Booléen déterminant si le champ est synchronisé ou non
 *	@return bool True en cas de succès, False sinon
 */
function fld_fields_set_is_sync( $fld,$is_sync ){
	global $config;

	if( !is_numeric($fld) || $fld<=0 ) return false;

	if( !fld_fields_is_tenant_linked($fld) ) return false;

	$res = ria_mysql_query('
		update fld_fields
		set fld_is_sync='.( $is_sync ? '1' : '0' ).'
		where fld_date_deleted is null and fld_id='.$fld.' and fld_tnt_id='.$config['tnt_id']
	);

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/** Cette fonction met à jour uniquement le nom d'un champ libre
 *	Si le nom spécifié est une chaîne vide ou est déjà utilisé, la fonction retournera une erreur
 *	@param int $id Identifiant du champ libre
 *	@param string $name Nom du champ libre
 *	@return bool True en cas de succès, ERR_NAME_EXISTS si le nom est déjà existant, false sinon
 */
function fld_fields_set_name( $id, $name ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !fld_fields_is_tenant_linked($id) ) return false;

	if( !trim($name) ) return false;
	$name = ucfirst(trim($name));

	$exists = ria_mysql_query('select fld_id from fld_fields where fld_tnt_id='.$config['tnt_id'].' and fld_date_deleted is null and fld_cls_id='.fld_fields_get_class( $id ).' and fld_name=\''.addslashes($name).'\' and fld_id!='.$id);
	if( ria_mysql_num_rows($exists) ) return ERR_NAME_EXISTS;

	$res = ria_mysql_query('
		update fld_fields
			set fld_name=\''.addslashes($name).'\'
		where fld_tnt_id='.$config['tnt_id'].'
			and fld_id='.$id
	);

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/** Cette fonction met à jour la description d'un champ libre sans modifier la valeur des autres champs
 *	@param int $id identifiant du champ libre
 *	@param string $desc Description du champ libre
 *	@return bool True en cas de succès, False sinon
 */
function fld_fields_set_desc( $id, $desc ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !fld_fields_is_tenant_linked($id) ) return false;

	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('
		update fld_fields
		set fld_desc=\''.addslashes($desc).'\'
		where fld_date_deleted is null and fld_tnt_id='.$config['tnt_id'].'
		and fld_id='.$id
	);

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/** Cette fonction met à jour l'information déterminant si le champ doit être afficher sous la désignation dans le PDF.
 * 	@param int $fld_id Obligatoire, identifiant d'un champ avancé
 * 	@param bool $under Obligatoire, si oui ou non le champ doit être afficheer sous la désignation
 * 	@return bool true si la mise à jour s'est correctement déroulée, false dans le cas contraire
 */
function fld_fields_set_pdf_under_name( $fld_id, $under ){
	global $config;

	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update fld_fields
		set fld_pdf_under_name = '.( $under ? '1' : '0' ).'
		where fld_tnt_id = '.$config['tnt_id'].'
			and fld_id = '.$fld_id.'
	');
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la suppression d'un champ personnalisé
 *	@param int $id Obligatoire, identifiant du champ à supprimer
 *	@param bool $force Optionnel, permet de forcer la suppression
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *	\warning Cette fonction devrait utiliser fld_date_deleted au lieu de supprimer physiquement
 */
function fld_fields_del( $id, $force=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !fld_fields_is_tenant_linked( $id ) ) return false;

	if( !$force ){
		if( fld_fields_is_used($id, false, false, fld_fields_get_class($id))>0 ) return false;
	}

	ria_mysql_query('delete from fld_restricted_values where val_tnt_id='.$config['tnt_id'].' and val_fld_id='.$id);
	ria_mysql_query('delete from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_fld_id='.$id); // Normalement pris en charge par l'intégrité référentielle (delete cascade)
	ria_mysql_query('delete from fld_classes_fields_priority where cfp_tnt_id='.$config['tnt_id'].' and cfp_fld_id='.$id); // Normalement pris en charge par l'intégrité référentielle (delete cascade)

	$res = ria_mysql_query('delete from fld_fields where fld_tnt_id='.$config['tnt_id'].' and fld_id='.$id);
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/**	Cette fonction permet le chargement du nom d'un champ
 *	@param int $id Obligatoire, identifiant du champ dont on souhaite charger le nom
 *	@return string le nom du champ au sein de sa catégorie, ou false en cas d'échec
 */
function fld_fields_get_name( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$rname = ria_mysql_query('select fld_name from fld_fields where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and fld_id='.$id);
	if( !ria_mysql_num_rows($rname) ) return false;
	return ria_mysql_result($rname,0,0);
}

/**	Cette fonction permet le chargement du type d'un champ
 *	@param int $id Obligatoire, identifiant du champ dont on souhaite charger le type
 *	@return int|bool le type du champ, ou false en cas d'échec
 */
function fld_fields_get_type( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	static $prev_id = 0;
	static $prev_type = 0;

	if( $id==$prev_id ){
		return $prev_type;
	}

	if( !is_numeric($id) || $id<=0 ) return false;
	$rtype = ria_mysql_query('select fld_type_id from fld_fields where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and fld_id='.$id);
	if( $rtype==false || !ria_mysql_num_rows($rtype) ) return false;

	$prev_id = $id;
	$prev_type = ria_mysql_result( $rtype, 0, 0 );

	return $prev_type;
}

/// \cond onlyria
/** Cette fonction détermine si le champ libre apssé en paramètre est synchronisé ou non
 *	@param int $fld Identifiant du champ libre
 *	@param bool $check_tnt_sync True pour vérifier tnt_sync_update dans tnt_tenants
 *	@return bool True si le champ est synchronisé, False sinon
 */
function fld_fields_get_is_sync( $fld, $check_tnt_sync = false ){
	global $config;

	if( !fld_fields_exists( $fld ) ) return false;

	$sql = '
		select fld_is_sync
		from fld_fields
		where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].')
		and fld_date_deleted is null and fld_id='.$fld
	;
	$is_sync = ria_mysql_result( ria_mysql_query($sql),0,0 );

	if( is_bool($check_tnt_sync) && $check_tnt_sync ){
		$sql = '
			SELECT tnt_sync_update
			FROM tnt_tenants
			WHERE tnt_id='.$config['tnt_id'].' and tnt_date_deleted is null
		';
		$tnt_sync = ria_mysql_result( ria_mysql_query($sql),0,0 );

		return $tnt_sync && $is_sync;

	}
	return $is_sync == '1';

}
/// \endcond

/// \cond onlyria
/**	Retourne le nombre de champs contenus dans une catégorie donnée
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@return int|bool le nombre de champs contenus dans cette catégorie, ou false en cas d'erreur
 */
function fld_fields_get_cat_count( $cat ){
	global $config;

	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('select count(*) from fld_fields where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and fld_cat_id='.$cat);
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result($res,0,0);
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet le chargement de la catégorie d'affichage d'un champ
 *	@param int $id Obligatoire, identifiant du champ dont on souhaite charger la catégorie d'affichage
 *	@return int|bool la catégorie d'affichage du champ, ou false en cas d'échec
 */
function fld_fields_get_cat( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$rcat = ria_mysql_query('select fld_cat_id from fld_fields where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].') and fld_date_deleted is null and fld_id='.$id);
	if( $rcat===false || !ria_mysql_num_rows($rcat) ) return false;

	return ria_mysql_result($rcat,0,0);
}
/// \endcond

/// \cond onlyria
/**	Cette fonction détermine si un champ avancé peut être utilisé pour créer des conditions d'accès au catalogue
 *	@param int $id Identifiant du champ avancé
 *	@return bool True si le champ peut être utilisé pour créer des droits d'accès, False sinon
 */
function fld_fields_get_used_access( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select count(*) from fld_fields
		where fld_id='.$id.' and (fld_tnt_id='.$config['tnt_id'].' or fld_tnt_id=0) and fld_date_deleted is null and fld_used_access=1
	');

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet de tester l'utilisation d'un champ.
 *	@param int $id Obligatoire, identifiant du champ à tester, ou tableau d'identifiants (autres paramètres ignorés)
 *	@param $value Facultatif, valeur du champ. Ignoré si $id est un tableau
 *	@param int $cat Facultatif, identifiant de la catégorie. Ce paramètre n'est pas pris en compte si le champ n'est pas de la classe CLS_PRODUCT (1 - produit), ou si $id est un tableau de plusieurs identifiants
 *	@param bool|int $control_deleted Optionnel, par défaut à false donc désactivé, mettre l'identifiant de la classe pour contrôler l'existante des objects
 *	@return int|bool Le nombre d'utilisation du champ
 *	@return array Si $id est un tableau, le résultat est un tableau associatif id_champ => count_valeurs
 *	@return bool false en cas d'erreur
 */
function fld_fields_is_used( $id, $value=false, $cat=false, $control_deleted=false ){

	$array_type = false;
	if( is_array($id) ){
		$array_type = true;
	}else{
		$id = array($id);
	}

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	$id = array_unique($id);

	if( $cat === false ){
		$cat = 0;
	}
	$cat = control_array_integer( $cat, false );
	if( $cat === false ){
		return false;
	}

	global $config;

	$exists = '';

	if( $control_deleted !== null ){
		if( is_numeric($control_deleted) && $control_deleted > 0 ){

			if( !fld_classes_is_physical($control_deleted) ){
				return false;
			}

			$ar_physical = fld_classes_get_physical_structure( $control_deleted );

			if( is_array($ar_physical) ){
				$exists = '
					select 1
					from '.$ar_physical['name'].'
					where '.( $ar_physical['tnt_id']!='' ? $ar_physical['tnt_id'].'='.$config['tnt_id'] : '1' ).'
				';

				if( trim($ar_physical['delete'])!='' ){
					if( strpos($ar_physical['delete'], '_is_') !== false || strpos($ar_physical['delete'], 'masked') !== false ){
						$exists .= ' and '.$ar_physical['delete'].' = 0';
					}else{
						$exists .= ' and '.$ar_physical['delete'].' is null';
					}
				}

				if( is_array($ar_physical['col_id']) ){
					if( !count($ar_physical['col_id']) ){
						return false;
					}

					$i = 0;
					foreach( $ar_physical['col_id'] as $col ){
						$exists .= ' and '.$col.' = pv_obj_id_'.$i;
						$i++;
					}
				}else{
					if( trim($ar_physical['col_id']) == '' ){
						return false;
					}

					$exists .= ' and '.$ar_physical['col_id'].' = pv_obj_id_0';
				}
			}
		}
	}

	$sql = '
		select
			pv_fld_id as id, count(*) as "count_val"
		from
			fld_object_values
		where
			pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id in ('.implode(', ', $id).')
			and pv_lng_code in ("'.implode('", "', $config['i18n_lng_used']).'")
	';

	if( !$array_type ){
		if( trim($value) ){
			$sql .= '
				and (
					pv_value = "'.addslashes($value).'"
					or pv_value like "%, '.addslashes($value).'%"
					or pv_value like "%'.addslashes($value).',%"
					or pv_value like "%, '.addslashes($value).',%"
				)
			';
		}
		if( sizeof($cat) && fld_fields_get_class( $id[0] ) == CLS_PRODUCT ){
			$sql .= '
				and exists (
					select 1 from prd_classify
					where cly_cat_id in( '.implode(', ', $cat).')
					and cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = pv_obj_id_0
				)
			';
		}
	}else{
		$sql .= ' group by pv_fld_id';
	}

	if( trim($exists) != '' ){
		$sql .= ' and (
			'.$exists.'
		)';
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		error_log( $sql.' === '.ria_mysql_error() );
		return false;
	}

	if( !$array_type ){
		return ria_mysql_result($r, 0, 'count_val');
	}

	$tab = array();
	while( $row = ria_mysql_fetch_array($r) ){
		$tab[ $row['id'] ] = $row['count_val'];
	}

	$used_id = array_keys($tab);

	foreach( $id as $one_id ){
		if( !in_array($one_id, $used_id) ){
			$tab[ $one_id ] = 0;
		}
	}

	return $tab;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet le chargement de toutes les valeurs distinctes renseignées pour un champ donné
 *
 *	@param int $id Obligatoire, identifiant du champ
 *	@param int $cat Facultatif, identifiant d'une catégorie sur laquelle filtrer le résultat. Pris en compte uniquement pour les champs de type CLS_PRODUCT (1 - produit)
 *	@param bool $publish Facultatif, indique si toutes les valeurs sont retournées, ou seulement celles des produits publiés. Pris en compte uniquement pour les champs de type CLS_PRODUCT (1 - produit)
 *	@param bool $recursive_cat Facultatif, récupère également les valeurs de restriction des catégories enfants (si $cat est précisé)
 *	@param bool $exclude_childonly Facultatif, si true, exclue les articles "article lié seulement"
 *	@param bool $orderable Facultatif, par défaut on ne tient pas compte de cette information, mettre true pour ne retourné que les valeurs des produits commandable
 *	@param array $obj_include Optionnel, objet pour lesquels les valeurs seront retournées
 *	@param $concat_obj Optionnel, permet de récupérer les objets concernés par chaque valeur, groupés dans une chaine (séparation par des virgules). Attention le serveur mysql limite le résultat d'un group_concat à 1024 par défaut, il peut donc manquer des objets dans le résultat. (configuré à 12 000 caractères sur Basseterre/Canberra)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- value : valeur distincte renseignée dans ce champ. Pour le type hiérarchique, cette valeur sera illisible (suite d'identifiants)
 *			- count : nombre d'occurences de cette valeur dans ce champ
 *			- ids : si $concat_obj est activé uniquement. Liste des objets rattachés à cette valeur de champ (objets concaténés par une virgule).
 */
function fld_fields_get_values( $id, $cat=0, $publish=true, $recursive_cat=false, $exclude_childonly=false, $orderable=false, $obj_include=false, $concat_obj=false ){

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	$cat = control_array_integer($cat, false);
	if( $cat === false ){
		return false;
	}

	global $config;
	$lng = i18n::getLang();

	$sql_obj_include = '';
	if( $obj_include!==false ){
		if( !is_array($obj_include) || !sizeof($obj_include) ){
			return false;
		}

		foreach( $obj_include as $key=>$obj ){
			if( !is_array($obj) ){
				if( !is_numeric($obj) || $obj<=0 ){
					return false;
				}

				$obj_include[$key] = array( $obj );
			}else{
				foreach( $obj as $o ){
					if( !is_numeric($o) || $o<=0 ){
						return false;
					}
				}
			}
		}

		if( sizeof($obj_include) ){
			$sql_obj_include .= ' and (';

			$first = true;
			foreach( $obj_include as $object ){
				$sql_obj_include .= $first ? '(' : ' or (';

				for( $i=0 ; $i<sizeof($object) ; $i++ ){
					$sql_obj_include .= ( $i > 0 ? ' and ' : '' ).( $lng!=$config['i18n_lng'] ? 'v1.' : '' ).'pv_obj_id_'.$i.'='.$object[$i];
				}

				$sql_obj_include .= ')';
				$first = false;
			}

			$sql_obj_include .= ')';
		}
	}

	$fld_class = fld_fields_get_class( $id );


	$dps = prd_deposits_get_main();
	if( $config['tnt_id'] == 2 && isset($_SESSION['usr_dps_id']) && is_numeric($_SESSION['usr_dps_id']) && $_SESSION['usr_dps_id'] > 0 ){
		$dps = $_SESSION['usr_dps_id'];
	}

	if( $lng!=$config['i18n_lng'] ){

		// "traduction left join langue de base"

		if( sizeof($cat) && $publish && $fld_class==CLS_PRODUCT ){
			if( $recursive_cat ){
				$sql = '
					select tmp.value, sum(tmp.counter) as "count"'.( $concat_obj ? ', group_concat(tmp.ids) as "ids"' : '' ).'
					from (

						select
							ifnull(v2.pv_value, v1.pv_value) as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values as v1
							join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							left join fld_object_values as v2 on (
								v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
								and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
								and \''.$lng.'\'=v2.pv_lng_code
							)
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							v1.pv_tnt_id='.$config['tnt_id'].'
							and prd_publish
							and prd_publish_cat
							and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
							'.( $orderable ? ' and prd_orderable' : '' ).'
							and prd_date_deleted is null
							and cly_cat_id in ('.implode( ', ', $cat ).')
							and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
							and v1.pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
						group by
							ifnull(v2.pv_value, v1.pv_value)

						union

						select
							ifnull(v2.pv_value, v1.pv_value) as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values as v1
							join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
							join prd_classify on ( prd_id=cly_prd_id and prd_tnt_id=cly_tnt_id )
							join prd_cat_hierarchy on ( cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id )
							left join fld_object_values as v2 on (
								v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
								and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
								and \''.$lng.'\'=v2.pv_lng_code
							)
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							v1.pv_tnt_id='.$config['tnt_id'].'
							and prd_publish
							and prd_publish_cat
							and prd_date_deleted is null
							and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
							'.( $orderable ? ' and prd_orderable' : '' ).'
							and cat_parent_id in ('.implode( ', ', $cat ).')
							and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
							and v1.pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
						group by
							ifnull(v2.pv_value, v1.pv_value)

					) tmp
					group by
						tmp.value
					order by
						cast(tmp.value as SIGNED) asc
				';
			}else{
				$sql = '
					select
						ifnull(v2.pv_value, v1.pv_value) as value, count(*) as "count"'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
					from
						fld_object_values as v1
						join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
						join prd_classify on ( prd_id=cly_prd_id and prd_tnt_id=cly_tnt_id )
						left join fld_object_values as v2 on (
							v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
							and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
							and \''.$lng.'\'=v2.pv_lng_code
						)
						left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
					where
						v1.pv_tnt_id='.$config['tnt_id'].'
						and prd_publish
						and prd_publish_cat
						and prd_date_deleted is null
						and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
						'.( $orderable ? ' and prd_orderable' : '' ).'
						and cly_cat_id in ('.implode( ', ', $cat ).')
						and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
						and v1.pv_fld_id='.$id.'
						'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
					group by
						ifnull(v2.pv_value, v1.pv_value)
					order by
						cast(ifnull(v2.pv_value, v1.pv_value) as SIGNED) asc
				';
			}
		}elseif( sizeof($cat) && $fld_class==CLS_PRODUCT ){
			if( $recursive_cat ){
				$sql = '
					select tmp.value, sum(tmp.counter) as "count"'.( $concat_obj ? ', group_concat(tmp.ids) as "ids"' : '' ).'
					from (

						select ifnull(v2.pv_value, v1.pv_value) as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values as v1
							join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							left join fld_object_values as v2 on (
								v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
								and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
								and \''.$lng.'\'=v2.pv_lng_code
							)
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							v1.pv_tnt_id='.$config['tnt_id'].'
							and cly_cat_id in ('.implode( ', ', $cat ).')
							and prd_date_deleted is null
							'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
							and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
							and v1.pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
						group by
							ifnull(v2.pv_value, v1.pv_value)

						union

						select
							ifnull(v2.pv_value, v1.pv_value) as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values as v1
							join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
							join prd_classify on ( prd_id=cly_prd_id and prd_tnt_id=cly_tnt_id )
							join prd_cat_hierarchy on ( cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id )
							left join fld_object_values as v2 on (
								v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
								and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
								and \''.$lng.'\'=v2.pv_lng_code
							)
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							v1.pv_tnt_id='.$config['tnt_id'].'
							and cat_parent_id in ('.implode( ', ', $cat ).')
							and prd_date_deleted is null
							'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
							and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
							and v1.pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
						group by
							ifnull(v2.pv_value, v1.pv_value)

					) tmp
					group by
						tmp.value
					order by
						cast(tmp.value as SIGNED) asc
				';
			}else{
				$sql = '
					select
						ifnull(v2.pv_value, v1.pv_value) as value, count(*) as "count"'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
					from
						fld_object_values as v1
						join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
						join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
						left join fld_object_values as v2 on (
							v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
							and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
							and \''.$lng.'\'=v2.pv_lng_code
						)
						left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
					where
						v1.pv_tnt_id='.$config['tnt_id'].'
						and prd_date_deleted is null
						'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
						and cly_cat_id in ('.implode( ', ', $cat ).')
						and v1.pv_fld_id='.$id.'
						and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
						'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
					group by
						ifnull(v2.pv_value, v1.pv_value)
					order by
						cast(ifnull(v2.pv_value, v1.pv_value) as SIGNED) asc
				';
			}
		}elseif( $publish && $fld_class==CLS_PRODUCT ){
			$sql = '
				select
					ifnull(v2.pv_value, v1.pv_value) as value, count(*) as "count"'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
				from
					fld_object_values as v1
					join prd_products as p on ( v1.pv_tnt_id=prd_tnt_id and v1.pv_obj_id_0=prd_id )
					left join fld_object_values as v2 on (
						v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
						and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
						and \''.$lng.'\'=v2.pv_lng_code
					)
					left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
				where
					v1.pv_tnt_id='.$config['tnt_id'].'
					and prd_publish
					and prd_publish_cat
					and prd_date_deleted is null
					and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
					'.( $orderable ? ' and prd_orderable' : '' ).'
					and v1.pv_fld_id='.$id.'
					and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
					'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
					'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
			';
			if( $config['use_catalog_restrictions'] ){
				$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
			}
			$sql .= '
				group by
					ifnull(v2.pv_value, v1.pv_value)
				order by
					cast(ifnull(v2.pv_value, v1.pv_value) as SIGNED) asc
			';
		}else{
			$sql = '
				select
					ifnull(v2.pv_value, v1.pv_value) as value, count(*) as "count"'.( $concat_obj ? ', group_concat(v1.pv_obj_id_0) as "ids"' : '' ).'
				from
					fld_object_values as v1
					left join fld_object_values as v2 on (
						v1.pv_tnt_id=v2.pv_tnt_id and v1.pv_fld_id=v2.pv_fld_id and v1.pv_obj_id_0=v2.pv_obj_id_0
						and v1.pv_obj_id_1=v2.pv_obj_id_1 and v1.pv_obj_id_2=v2.pv_obj_id_2
						and \''.$lng.'\'=v2.pv_lng_code
					)
				where
					v1.pv_tnt_id='.$config['tnt_id'].'
					and v1.pv_fld_id='.$id.'
					and v1.pv_lng_code=\''.$config['i18n_lng'].'\'
					'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				group by
					ifnull(v2.pv_value, v1.pv_value)
				order by
					cast(ifnull(v2.pv_value, v1.pv_value) as SIGNED) asc
			';
		}

	}else{

		// pas de left join

		if( sizeof($cat) && $publish && $fld_class==CLS_PRODUCT ){
			if( $recursive_cat ){
				$sql = '
					select
						tmp.value, sum(tmp.counter) as "count"'.( $concat_obj ? ', group_concat(tmp.ids) as "ids"' : '' ).'
					from (

						select
							pv_value as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values
							join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							pv_tnt_id='.$config['tnt_id'].'
							and prd_publish
							and prd_publish_cat
							and prd_date_deleted is null
							and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
							'.( $orderable ? ' and prd_orderable' : '' ).'
							and cly_cat_id in ('.implode( ', ', $cat ).')
							and pv_lng_code=\''.$lng.'\'
							and pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}
				$sql .= '
						group by
							pv_value

						union

						select
							pv_value as value, count(*) as counter'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
						from
							fld_object_values
							join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							join prd_cat_hierarchy on ( cly_tnt_id=cat_tnt_id and cly_cat_id=cat_child_id )
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							pv_tnt_id='.$config['tnt_id'].'
							and prd_publish
							and prd_publish_cat
							and prd_date_deleted is null
							and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
							'.( $orderable ? ' and prd_orderable' : '' ).'
							and cat_parent_id in ('.implode( ', ', $cat ).')
							and pv_lng_code=\''.$lng.'\'
							and pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
						group by
							pv_value

					) tmp
					group by
						tmp.value
					order by
						cast(tmp.value as SIGNED) asc
				';
			}else{
				$sql = '
					select
						pv_value as value, count(*) as "count"'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
					from
						fld_object_values
						join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
						join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
						left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
					where
						pv_tnt_id='.$config['tnt_id'].'
						and prd_publish
						and prd_publish_cat
						and prd_date_deleted is null
						and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
						'.( $orderable ? ' and prd_orderable' : '' ).'
						and cly_cat_id in ('.implode( ', ', $cat ).')
						and pv_lng_code=\''.$lng.'\'
						and pv_fld_id='.$id.'
						'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
						'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
					group by
						pv_value
					order by
						cast(pv_value as SIGNED) asc
				';
			}
		}elseif( sizeof($cat) && $fld_class==CLS_PRODUCT ){
			if( $recursive_cat ){
				$sql = '
					select
						tmp.value, sum(tmp.counter) as "count"
					from (

						select
							pv_value as value, count(*) as counter
						from
							fld_object_values
							join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							pv_tnt_id='.$config['tnt_id'].'
							and prd_date_deleted is null
							'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
							and cly_cat_id in ('.implode( ', ', $cat ).')
							and pv_lng_code=\''.$lng.'\'
							and pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
						group by
							pv_value

						union

						select
							pv_value as value, count(*) as counter
						from
							fld_object_values
							join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
							join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
							join prd_cat_hierarchy on ( cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id )
							left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
						where
							pv_tnt_id='.$config['tnt_id'].'
							and prd_date_deleted is null
							'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
							and cat_parent_id in ('.implode( ', ', $cat ).')
							and pv_lng_code=\''.$lng.'\'
							and pv_fld_id='.$id.'
							'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
							'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
						group by
							pv_value

					) tmp
					group by
						tmp.value
					order by
						cast(tmp.value as SIGNED) asc
				';
			}else{
				$sql = '
					select
						pv_value as value, count(*) as "count"
					from
						fld_object_values
						join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
						join prd_classify on ( prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id )
						left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
					where
						pv_tnt_id='.$config['tnt_id'].'
						and prd_date_deleted is null
						'.( $orderable ? ' and prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)' : '' ).'
						and cly_cat_id in ('.implode( ', ', $cat ).')
						and pv_fld_id='.$id.'
						and pv_lng_code=\''.$lng.'\'
						'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
				';
				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
					group by
						pv_value
					order by
						cast(pv_value as SIGNED) asc
				';
			}
		}elseif( $publish && $fld_class==CLS_PRODUCT ){
			$sql = '
				select
					pv_value as value, count(*) as "count"'.( $concat_obj ? ', group_concat(p.prd_id) as "ids"' : '' ).'
				from
					fld_object_values
					join prd_products as p on ( pv_tnt_id=prd_tnt_id and pv_obj_id_0=prd_id )
					left join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
				where
					pv_tnt_id='.$config['tnt_id'].'
					and prd_date_deleted is null
					and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)
					'.( $orderable ? ' and prd_orderable' : '' ).'
					and prd_publish
					and prd_publish_cat
					and pv_fld_id='.$id.'
					and pv_lng_code=\''.$lng.'\'
					'.( $exclude_childonly ? ' and prd_childonly=0' : '' ).'
					'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
			';

				if( $config['use_catalog_restrictions'] ){
					$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
				}

				$sql .= '
				group by
					pv_value
				order by
					cast(pv_value as SIGNED) asc
			';
		}else{
			if (sizeof($cat) && $fld_class==CLS_CATEGORY){
				$sql = '
					select
						pv_value as value, count(*) as "count"'.( $concat_obj ? ', group_concat(pv_obj_id_0) as "ids"' : '' ).'
					from
						fld_object_values
					where
						pv_tnt_id='.$config['tnt_id'].'
						and pv_fld_id='.$id.'
						and pv_obj_id_0 in ('.implode( ', ', $cat ).')
						and pv_lng_code=\''.$lng.'\'
						'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
					group by
						pv_value
					order by
						cast(pv_value as SIGNED) asc
				';
			} else {
				$sql = '
					select
						pv_value as value, count(*) as "count"'.( $concat_obj ? ', group_concat(pv_obj_id_0) as "ids"' : '' ).'
					from
						fld_object_values
					where
						pv_tnt_id='.$config['tnt_id'].'
						and pv_fld_id='.$id.'
						and pv_lng_code=\''.$lng.'\'
						'.( trim($sql_obj_include)!='' ? $sql_obj_include: '' ).'
					group by
						pv_value
					order by
						cast(pv_value as SIGNED) asc
				';
			}
		}

	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}
/// \endcond

/// \cond onlyria
/**	Cette fonction est une alternative à fld_fields_get_values(), spécifique aux champs multivalués (ID 6 et 12)
 *	Elle sépare les valeurs stockées pour un objet et retourne un tableau des valeurs unitaires distinctes
 *
 *	@param int $id Obligatoire, identifiant du champ (forcément de type FLD_TYPE_SELECT_MULTIPLE ou FLD_TYPE_SELECT_HIERARCHY)
 *	@param int $cat Facultatif, identifiant d'une catégorie sur laquelle filtrer le résultat. Pris en compte uniquement pour les champs de type CLS_PRODUCT (1 - produit)
 *	@param bool $publish Facultatif, indique si toutes les valeurs sont retournées, ou seulement celles des produits publiés. Pris en compte uniquement pour les champs de type CLS_PRODUCT (1 - produit)
 *	@param bool $recursive_cat Facultatif, récupère également les valeurs de restriction des catégories enfants (si $cat est précisé)
 *	@param $exclude_childonly Facultatif, si true, exclue les articles "article lié seulement"
 *	@param $get_ids Facultatif, si True les identifiants des valeurs sont retournés, sinon les libellés
 *	@param $respect_perso_sort Facultatif, si True, on recherche les positions personnalisées des valeurs de restriction, et on applique le tri (plus lourd)
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau des valeurs distinctes
 */
function fld_fields_get_values_array( $id, $cat=0, $publish=true, $recursive_cat=false, $exclude_childonly=false, $get_ids=false, $respect_perso_sort=false ){
	$type = fld_fields_get_type( $id );
	if( $get_ids && ($type!=FLD_TYPE_SELECT_MULTIPLE && $type!=FLD_TYPE_SELECT_HIERARCHY && $type!=FLD_TYPE_SELECT) ) return false;

	$rvalues = fld_fields_get_values( $id, $cat, $publish, $recursive_cat, $exclude_childonly );
	if( !$rvalues ) return false;

	$values = array();
	while( $value = ria_mysql_fetch_array( $rvalues ) ){
		$exploder = explode( ', ', $value['value'] );
		foreach( $exploder as $v ){
			$v = trim($v);
			if( $get_ids && ($type==FLD_TYPE_SELECT_MULTIPLE || $type==FLD_TYPE_SELECT) ){
				$v = fld_restricted_values_get_id( $id, $v );
			}elseif( !$get_ids && $type==FLD_TYPE_SELECT_HIERARCHY ){
				$v = fld_restricted_values_get_name( $v );
			}
			$values[] = $v;
		}
	}

	// tri par position personnalisée
	if( $respect_perso_sort ){
		if( $type==FLD_TYPE_SELECT || $type==FLD_TYPE_SELECT_MULTIPLE || $type==FLD_TYPE_SELECT_HIERARCHY ){
			$values_key = array();
			foreach( $values as $v ){
				$v_id = $get_ids ? $v : fld_restricted_values_get_id( $id, $v );
				$pos_sort = fld_restricted_values_get_pos( $v_id );
				$values_key[$v] = is_numeric($pos_sort) ? $pos_sort : $v;
			}
			asort( $values_key );
			$values = array_keys( $values_key );
		}
	}

	return array_unique( $values );
}
/// \endcond

/// \cond onlyria
/**	Affiche une interface html permettant la modification du contenu du champ
 *	@param $field Obligatoire, tableau associatif des propriétés du champ tel que retourné par ria_mysql_fetch_array(fld_fields_get())
 *	@param $onchange Facultatif, code javascript à insérer pour être exécuté lors de la modification du champ
 *	@param $obj_is_sync Facultatif, si false le champ sera considéré comme non synchronisé, si true le champ est considéré comme synchronisé, si null valeur par défaut du champ
 *	@param int $check_value Facultatif, identifiant de la valeur à sélectionner par défaut dans une liste à choix unique ou multiple
 *	@param array $obj Optionnel, tableau d'identifiants de l'objet
 *	@return string Le code HTML nécessaire pour afficher le champ
 */
function fld_fields_edit( $field, $onchange='', $obj_is_sync = null, $check_value=0, $obj=array() ){
	global $config;

	$html = '';

	$readonly = false;

	$editable_fields = array(
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_1,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_2,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_3,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_4,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_5,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_6,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_7,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_8,
		_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_9,
	);

	// Pour le locataire "Crêperie de Guerlédan", le champ "Central d'achat" n'est pas synchronisé, il est donc éditable
	if( $config['tnt_id'] == 112 ){
		$editable_fields[] = _FLD_USR_CENTRAL;
	}

	if( !in_array($field['id'], $editable_fields) && (!$field['tenant'] || ($obj_is_sync === true && $field['is-sync'])) ){
		$readonly = true;
	}

	if( !$field['used_access'] ){
		$readonly = true;
	}

	switch( $field['type_id'] ){
		case FLD_TYPE_TEXT: {
			$html .= '<input type="text" '.( $readonly ? 'disabled="disabled"' : '' ).' name="fld'.$field['id'].'" id="fld'.$field['id'].'"';
			if( $field['max'] ){
				$html .= ' maxlength="'.$field['max'].'"';
			}
			if( isset($field['obj_value']) ){
				$html .= ' value="'.htmlspecialchars($field['obj_value']).'"';
			}
			if (isset($field['related_class'])){
				$html .= ' class="fld-type-text related-class-'.$field['related_class'].'"';
			}
			if( $onchange ){
				$html .= ' onchange="'.$onchange.'" onkeyup="'.$onchange.'" ';
			}
			$html .= ' title="'.htmlspecialchars( $field['desc'] ).'"';
			$html .= ' />';
			break;
		}
		case FLD_TYPE_TEXTAREA: {
			$html .= '<textarea '.( $field['old_txt_type'] == 1 ? '' : 'class="tinymce"' ).' '.( $readonly ? 'disabled="disabled"' : '' ).' name="fld'.$field['id'].'" id="fld'.$field['id'].'" cols="40" rows="5"';
			if( $onchange ){ $html .= ' onchange="'.$onchange.'" onkeyup="'.$onchange.'" '; }
			$html .= ' title="'.htmlspecialchars( $field['desc'] ).'"';
			$html .= '>'.htmlspecialchars($field['obj_value']).'</textarea>';
			break;
		}
		case FLD_TYPE_DATE: {
			$field['obj_value']= str_replace(' 00:00:00', '', dateheureparse($field['obj_value']) );

			$html .= '<input type="text" '.( $readonly ? 'disabled="disabled"' : '' ).' name="fld'.$field['id'].'" id="fld'.$field['id'].'"';
			if( isset($field['obj_value']) && (isdate($field['obj_value']) || isdateheure($field['obj_value'])) ){
				$date_string = dateheureunparse( $field['obj_value'] );
				$html .= ' value="'.htmlspecialchars($date_string ).'"';
			}
			if( $onchange ){ $html .= ' onchange="'.$onchange.'" onkeyup="'.$onchange.'" '; }
			$html .= ' title="'.htmlspecialchars( $field['desc'] ).'"';
			$html .= ' />';
			break;
		}
		case FLD_TYPE_INT:
		case FLD_TYPE_FLOAT: {
			$html .= '<input type="text" '.( $readonly ? 'disabled="disabled"' : '' ).' class="number" name="fld'.$field['id'].'" id="fld'.$field['id'].'"';
			if( isset($field['obj_value']) )
				$html .= ' value="'.htmlspecialchars($field['type_id'] === FLD_TYPE_INT? (int)$field['obj_value'] : $field['obj_value']).'"';
			if( $onchange ){ $html .= ' onchange="'.$onchange.'" onkeyup="'.$onchange.'" '; }
			$html .= ' title="'.htmlspecialchars( $field['desc'] ).'"';
			$html .= ' />';
			break;
		}
		case FLD_TYPE_SELECT: {
			if( is_array($check_value) ){
				if( sizeof($check_value) ){
					$check_value = array_pop( $check_value );
				}else{
					$check_value = 0;
				}
			}

			$selected = array();
			if( isset($field['obj_value']) ){
				if( $field['type_id']==FLD_TYPE_SELECT ){
					$selected[] = fld_restricted_values_get_id( $field['id'], $field['obj_value'] );
				}else{
					$values = explode( ',', $field['obj_value'] );
					foreach( $values as $v ){
						$selected[] = fld_restricted_values_get_id( $field['id'], $v );
					}
				}
			}

			$html .= '<select '.( $readonly ? 'disabled="disabled"' : '' ).' name="fld'.$field['id'].'" id="fld'.$field['id'].'"';
			if( $onchange ){ $html .= ' onchange="'.$onchange.'" onkeyup="'.$onchange.'" '; }
			$html .= ' title="'.htmlspecialchars( $field['desc'] ).'"';
			$html .= '>';
			$html .= '<option value=""></option>';
			$values = fld_restricted_values_get( 0, $field['id'] );
			while( $r = ria_mysql_fetch_array($values) ){
				$s = $check_value!=$r['id'] ? ( array_search($r['id'],$selected)!==false ? 'selected="selected"' : '' ) : 'selected="selected"';
				$html .= '<option value="'.$r['id'].'" '.$s.'>'.htmlspecialchars($r['name']).'</option>';
			}
			$html .= '</select>';
			break;
		}
		case FLD_TYPE_SELECT_MULTIPLE: {
			if( !is_array($check_value) ){
				$check_value = array( $check_value );
			}

			$selected = array();
			if( isset($field['obj_value']) ){
				$values = explode( ',', $field['obj_value'] );
				foreach( $values as $v ){
					$selected[] = fld_restricted_values_get_id( $field['id'], trim($v) );
				}
			}
			$values = fld_restricted_values_get( 0, $field['id'] );
			$html .= '<div class="list-checkbox">';
			while( $r = ria_mysql_fetch_array($values) ){
				$c = !in_array($r['id'], $check_value) ? ( array_search($r['id'],$selected)!==false ? 'checked="checked"' : '' ) : 'checked="checked"';

				$html .= '<div class="margin-right: 3px ">';
				$html .= '<input type="checkbox" '.( $readonly ? 'disabled="disabled"' : '' ).' class="checkbox" onchange="'.$onchange.'" onkeyup="'.$onchange.'" name="fld'.$field['id'].'[]" id="fld'.$field['id'].'-'.$r['id'].'" value="'.$r['id'].'" '.$c.' /> ';
				$html .= '<label for="fld'.$field['id'].'-'.$r['id'].'">'.htmlspecialchars($r['name']).'</label> ';
				$html .= '</div>';
			}

			$html .= '</div>';

			if( ria_mysql_num_rows($values)>3 ){
				$html .= '<hr style="width: 100%">';
				$html .= '<a href="#" class="check-all">'._('Cocher tout').'</a> <span style="margin: 0 4px">|</span> <a href="#" class="uncheck-all">'._('Décocher tout').'</a>';
			}

			break;
		}
		case FLD_TYPE_BOOLEAN_YES_NO: {
			if( !isset($field['obj_value']) ) $field['obj_value'] = '';
			$html .= '<div>';
			$html .= '<input type="radio" '.( $readonly ? 'disabled="disabled"' : '' ).' class="radio" name="fld'.$field['id'].'" id="fld'.$field['id'].'-yes" value="Oui" '.( $field['obj_value']=='Oui' ? 'checked="checked"':'' ).' /> <label for="fld'.$field['id'].'-yes" class="inline">'._('Oui').'</label> ';
			$html .= '<input type="radio" '.( $readonly ? 'disabled="disabled"' : '' ).' class="radio" name="fld'.$field['id'].'" id="fld'.$field['id'].'-no" value="Non" '.( $field['obj_value']=='Non' ? 'checked="checked"':'' ).' /> <label for="fld'.$field['id'].'-no" class="inline">'._('Non').'</label> ';
			$html .= '<input type="radio" '.( $readonly ? 'disabled="disabled"' : '' ).' class="radio" name="fld'.$field['id'].'" id="fld'.$field['id'].'-null" value="" '.( $field['obj_value']=='' ? 'checked="checked"':'' ).' /> <label for="fld'.$field['id'].'-null" class="inline">'._('Indéfini').'</label> ';
			$html .= '</div>';
			break;
		}
		case FLD_TYPE_SELECT_HIERARCHY: {
			$selected = array();

			if( isset($field['obj_value']) ){
				if( is_string($field['obj_value']) ){
					$selected = explode( ',', $field['obj_value'] );
					$selected = array_map('trim', $selected);
				}elseif( is_array($field['obj_value']) ){
					$selected = $field['obj_value'];
				}
			}

			$values = fld_restricted_values_get( 0, $field['id'] );
			while( $r = ria_mysql_fetch_assoc($values) ){
				$val_print = $r['name'];
				if( $r['parent']!=NULL && $r['parent']!='' ){
					if( $rval = fld_restricted_values_get($r['parent']) ){
						if( $val = ria_mysql_fetch_array($rval) ){
							$val_print = $val['name'].' >> '.$val_print;
						}
					}
				}
				$html .= '<div>';
				$html .= '<input type="checkbox" '.( $readonly ? 'disabled="disabled"' : '' ).' class="checkbox" onchange="'.$onchange.'" onkeyup="'.$onchange.'"  name="fld'.$field['id'].'[]" id="fld'.$field['id'].'-'.$r['id'].'" value="'.$r['id'].'" '.( in_array($r['id'], $selected )? 'checked="checked"':'' ).' /> <label for="fld'.$field['id'].'-'.$r['id'].'">'.htmlspecialchars($val_print).'</label>';
				$html .= '</div>';
			}
			break;
		}
		case FLD_TYPE_IMAGE : {
			if( trim($field['obj_value'])!=''){
				$size = $config['img_sizes']['medium'];
				$html .= '<img src="'.$config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$field['obj_value'].'.'.$size['format'].'" alt="" width="'.$size['width'].'" height="'.$size['height'].'" />';
				$html .= '<div class="clear"></div>';
				$html .= '<input style="width:100px;" type="button" name="del-field-img" id="del-field-img-'.$field['id'].'" title="'._('Supprimer l\'image').'" onclick="return delImageField('.$field['cls_id'].', '.$field['id'].', '.implode(',', $obj).');" value="'._('Supprimer').'" />';
			}
			$html .= '<input style="width: 230px;" type="file" name="fld'.$field['id'].'" id="fld'.$field['id'].'" />';
			break;
		}
		case FLD_TYPE_REFERENCES_ID: {
			switch( $field['related_class'] ){
				case CLS_USER: {
					$email_value = '';
					if( isset($field['obj_value']) && is_numeric($field['obj_value']) && $field['obj_value'] > 0 ){
						$email_value = gu_users_get_email( $field['obj_value'] );
					}
					$html .= '
						<input type="hidden" name="fld'.$field['id'].'" id="fld'.$field['id'].'" '.( isset($field['obj_value']) ? 'value="'.htmlspecialchars($field['obj_value']).'"' : '' ).' />
						<input type="text" name="fld'.$field['id'].'_email" id="fld'.$field['id'].'_email" class="fld_usr_browse_txt" readonly="readonly" value="'.htmlspecialchars($email_value).'" />
						<input type="button" name="fld'.$field['id'].'_browse" id="fld'.$field['id'].'_browse" value="'._('Parcourir').'" class="fld_usr_browse_btn" />
					';
					break;
				}
				case CLS_BRAND:
				case CLS_PRODUCT:
				case CLS_CATEGORY:
				case CLS_STORE: {
					$r_object = false;

					switch( $field['related_class'] ){
						case CLS_BRAND: {
							if( trim($field['obj_value']) != '' ){
								$ar_vals = explode( ',', $field['obj_value'] );
								if( is_array($ar_vals) && sizeof($ar_vals) ){
									$r_object = prd_brands_get( $ar_vals );
								}
							}
							break;
						}
						case CLS_PRODUCT: {
							if( trim($field['obj_value']) != '' ){
								$ar_vals = explode( ',', $field['obj_value'] );
								if( is_array($ar_vals) && sizeof($ar_vals) ){
									$r_object = prd_products_get_simple( $ar_vals, '', false, 0, false, false, false, false, array('childs'=>true) );
								}
							}
							break;
						}
						case CLS_CATEGORY: {
							if( trim($field['obj_value']) != '' ){
								$ar_vals = explode( ',', $field['obj_value'] );
								if( is_array($ar_vals) && sizeof($ar_vals) ){
									$r_object = prd_categories_get( $ar_vals );
								}
							}
							break;
						}
						case CLS_STORE: {
							if( trim($field['obj_value']) != '' ){
								$ar_vals = explode( ',', $field['obj_value'] );
								if( is_array($ar_vals) && sizeof($ar_vals) ){
									$r_object = dlv_stores_get( $ar_vals );
								}
							}
						}
					}


					$html .= '
						<div class="fld-form-reference">
							<select name="fld'.$field['id'].'" id="fld'.$field['id'].'" multiple="multiple">
					';

					if( $r_object ){
						while( $cat = ria_mysql_fetch_assoc($r_object) ){
							$arbo = '';
							$rparent = prd_categories_parents_get( $cat['id'] );
							if( $rparent ){
								while( $parent = ria_mysql_fetch_assoc($rparent) ){
									$arbo = ( trim($arbo) != '' ? ' &raquo; ' : '' ).$parent['title'];
								}
							}

							$html .= '<option value="'.$cat['id'].'">'.$arbo.( trim($arbo) != '' ? ' &raquo; ' : '' ).htmlspecialchars( $cat['title'] ).'</option>';
						}
					}

					$html .= '
							</select>
							<div class="fld-val-actions">
								<input type="hidden" name="pointer-cls-id" value="'.$field['related_class'].'" />
								<input type="button" name="del-value-reference" id="del-value-reference-'.$field['id'].'" value="'._('Supprimer').'" class="fld_pointer_delete_btn" />
								<input type="button" name="fld'.$field['id'].'_browse" id="fld'.$field['id'].'_browse" value="'._('Parcourir').'" class="fld_pointer_add_btn" />
							</div>
						</div>
					';

					break;
				}
				default:
					if( !in_array($field['id'], array(666) ) ){
						error_log('Cette classe n\'est pas encore gérée ('.$config['tnt_id'].' - '.$field['id'].')');
					}
					break;
			}
			break;
		}
	}

	if( in_array($field['id'], [_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_1, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_2, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_3, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_4, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_5, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_6, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_7, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_8, _FLD_ORD_SPECIFIC_SHIPPING_CHARGES_9]) ){
		$field['unit_name'] = 'Euro Hors Taxes';
		$field['unit_symbol'] = '€ HT';
	}

	if( $field['unit_name'] ){
		$html .= ' <abbr title="'.htmlspecialchars($field['unit_name']).'">'.htmlspecialchars( $field['unit_symbol'] ).'</abbr>';
	}
	if( $field['min'] || $field['max'] ){
		if( $field['type_id']==1 ){
			if( $field['min']!='' && $field['max']!='' ){
				if( $field['min']==$field['max'] ){
					$html .= ' <sub>('.sprintf(_('%d caractères'), $field['min']).')</sub>';
				}else{
					$html .= ' <sub>('.sprintf(_('Entre %d à %d caractères'), $field['min'], $field['max']).')</sub>';
				}
			}elseif( $field['min']!='' ){
				$html .= ' <sub>('.sprintf(_('Au moins %d caractères'), $field['min']).')</sub>';
			}elseif( $field['max']!='' ){
				$html .= ' <sub>('.sprintf(_('Maximum %d caractères'), $field['max']).')</sub>';
			}
		}else{
			if( $field['min']!='' && $field['max']!='' ){
				if( $field['min']==$field['max'] && $field['type_id']==1 ){
					$html .= ' <sub>('.sprintf(_('%d caractères'), $field['min']).')</sub>';
				}else{
					$html .= ' <sub>('.sprintf(_('compris entre %d et %d'), $field['min'], $field['max']).')</sub>';
				}
			}elseif( $field['min']!='' ){
				$html .= ' <sub>('.sprintf(_('supérieur ou égal à %d uniquement'), $field['min']).')</sub>';
			}elseif( $field['max']!='' ){
				$html .= ' <sub>('.sprintf(_('inférieur ou égal à %d uniquement'), $field['max']).')</sub>';
			}
		}
	}
	return $html;
}
/// \endcond

/// \cond onlyria
/**	Affiche une interface html permettant la visualisation du contenu du champ
 *	@param array $field Obligatoire, tableau associatif des propriétés du champ tel que retourné par ria_mysql_fetch_array(fld_fields_get())
 *	@param bool $moderation Facultatif, true pour afficher les champs en mode moderation, false pour l'affichage normal
 *	@return string Le code HTML nécessaire pour afficher le champ
 */
function fld_fields_view( $field, $moderation=false ){
	global $config;

	$html = '';

	if( !$field['tenant'] ){
		return $html;
	}

	$value = '';
	if( isset($field['obj_value']) ){
		$value = $field['obj_value'];
	}

	if( is_array($value) ){
		foreach ($value as $key => $v) {
			if(trim($v) == ''){
				unset($value[$key]);
			}
		}
		$value = implode(',', $value);
	}

	$switch_type_id = false;
	if( $config['tnt_id']==1 ){ // Spécifique BigShip
		switch( $field['id'] ){
			case FLD_PRD_BRAND:
				$rbrd = prd_brands_get($value);
				if( ria_mysql_num_rows($rbrd) ){
					$brd = ria_mysql_fetch_array($rbrd);
					$html = $brd['title'];
				}
				break;
			case FLD_PRD_IMAGE:
				$width = $config['img_sizes']['medium']['width']; $height = $config['img_sizes']['medium']['height'];
				$src = $config['site_url'].'/images/products/'.$width.'x'.$height.'/' .( $value ? $value.'.jpg' : 'default.gif' );
				$html = '<img src="'.$src.'" width="'.$width.'" height="'.$height.'" alt="" title="" />';
				break;
			case FLD_PRD_IMAGES:
				$images = explode(',',$value);

				foreach( $images as $img ){
								$width = $config['img_sizes']['small']['width']; $height = $config['img_sizes']['small']['height'];
								$src = $config['site_url'].'/images/products/'.$width.'x'.$height.'/' .( $img ? $img.'.jpg' : 'default.gif' );
								$html .= '<img src="'.$src.'" width="'.$width.'" height="'.$height.'" alt="" title="" /> ';
				}
				break;
			case FLD_PRD_TVA_RATE:
				$tva = floatval( $value );
				$html = number_format( ($tva - 1) * 100, 1, ',', ' ').'%';
				break;
			case FLD_PRD_PRICE_BS:
			case FLD_PRD_PRICE_TTC:
			case FLD_PRD_PRICE_ADH:
				$html = number_format(str_replace(',','.',$value),2,',',' ' ).' &euro;';
				break;
			case FLD_PRD_WEIGHT_NET:
			case FLD_PRD_WEIGHT_BRUT:
				$html = $value.'g';
				break;
			case FLD_PRD_HIERARCHY:
			case FLD_PRD_ACCESSOIRE:
			case FLD_PRD_OPTION:
			case FLD_PRD_PIECE:
				if($value==  1){
					$html = _('Supprimer');
				}else{
					$prod = ria_mysql_fetch_array(prd_products_get( $value ));
					$html = htmlspecialchars( $prod['name'] );
				}
				break;
			default:

				if($moderation){
					switch( $field['type_id'] ){
						case FLD_TYPE_SELECT:
								$val = ria_mysql_fetch_array(fld_restricted_values_get($value,$field['id']));
								$html = $val['name'] ;

							break;
						case FLD_TYPE_SELECT_MULTIPLE:
							// si c'est une checkbox on convertir le string en un tableau
							// squelette du string : 0 ; id ; 1 ; id ; ...
							// 0 pas coché , 1 coché
								$val = explode(';',$value);
								$coche = false;

								foreach($val as $v){
									$coche ? $coche = false : $coche = true;
									if($v !='' && !$coche){
										$temp = ria_mysql_fetch_array(fld_restricted_values_get($v,$field['id']));
										$html .= '<div>'.$input.' '.$temp['name'].'</div>' ;
									}
									else{
										if($v == 1){
											$input = '<input type="checkbox" checked="checked" name="check" disabled="disabled"/>';
										}else if($v == 0){
											$input = '<input type="checkbox" name="check" disabled="disabled"/>';
										}
									}
								}
							break;
						default:
							$switch_type_id = true;
					}
				}
				else{
					$switch_type_id = true;
				}

		}
	}else{
		$switch_type_id = true;
	}
	if( $switch_type_id ){
		switch( $field['type_id'] ){
			case FLD_TYPE_TEXT:
				$html = htmlspecialchars( $value );
				break;
			case FLD_TYPE_TEXTAREA:
				$html = view_site_format_description( $value );
				break;
			case FLD_TYPE_DATE:
				$field['obj_value']= str_replace(' 00:00:00', '', dateheureparse($field['obj_value'] ));

				if( isdate($value) || isdateheure($value) ){
					$date_string = dateheureunparse( $field['obj_value'] );
					$html = htmlspecialchars( $date_string );
				}
				break;
			case FLD_TYPE_INT:
				$html = (int)$value;
				break;
			case FLD_TYPE_FLOAT:
				$html = str_replace( '.', ',', $value );
				break;
			case FLD_TYPE_SELECT:
			case FLD_TYPE_SELECT_MULTIPLE:
				$html = implode( '<br />', explode( ', ', $value ) );
				break;
			case FLD_TYPE_IMAGE:
			case FLD_TYPE_IMAGES:
				if( trim($value) ){
					$values = explode( ',', $value );
					$width = $config['img_sizes']['medium']['width']; $height = $config['img_sizes']['medium']['height'];
					foreach( $values as $v ){
						$src = $config['site_url'].'/images/products/'.$width.'x'.$height.'/' .( $v ? $v.'.jpg' : 'default.gif' );
						$html .= '<img src="'.$src.'" width="'.$width.'" height="'.$height.'" alt="" title="" /><br />';
					}
				}
				break;
			case FLD_TYPE_SELECT_HIERARCHY:

				$tmp_html = array();
				$values = explode( ',', $value );
				foreach( $values as $v ){
					if( is_numeric($v) ){
						if( $rval = fld_restricted_values_get($v)){
							if( $val = ria_mysql_fetch_array($rval) ){
								$name = $val['name'];
								while( $val['parent']!=NULL && $val['parent']!='' ){
									if( $rval = fld_restricted_values_get($val['parent']) ){
										if( $val = ria_mysql_fetch_array($rval) ){
											$name = $val['name'].' >> '.$name;
										}
									}
								}
								$tmp_html[] = htmlspecialchars($name);
							}
						}
					}else{
						$tmp_html[] = $v;
					}
				}

				$html = implode('<br />', $tmp_html);

				break;
			case FLD_TYPE_BOOLEAN_YES_NO:
				$html = $value;
				break;
		}
	}
	if( $field['unit_name'] ){
		$html .= ' <abbr title="'.htmlspecialchars($field['unit_name']).'">'.htmlspecialchars( $field['unit_symbol'] ).'</abbr>';
	}
	return $html;
}
/// \endcond

/// \cond onlyria
/** Cette fonction récupère la liste des objets qui ont une valeur renseignée pour un champ avancé donné.
 *	@param $fld Obligatoire, identifiant du champ avancé.
 *	@param $value Optionnel, valeur du champ avancé.
 *	@param $no_casse Optionnel, détermine si $value est insensible à la casse.
 *	@param $full_like Optionnel, si activé, la valeur à chercher est entourée de "%".
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- obj_id : identifiant de l'objet.
 *		- obj_id_X : identifiants de l'objet, ou X est le numéro d'ordre (clé primaire composée).
 */
function fld_fields_get_objects( $fld, $value=false, $no_casse=false, $full_like=false ){
	global $config;

	if( !is_numeric($fld) || $fld <= 0 ){
		return false;
	}

	$select = array();
	for( $i = 0; $i < COUNT_OBJ_ID; $i++ ){
		$select[] = 'pv_obj_id_'.$i.' as "obj_id'.( $i == 0 ? '' : '_'.( $i + 1 ) ).'"';
	}

	$sql = '
		select '.implode(', ', $select).'
		from
			fld_object_values
		where
			pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld.'
			and pv_value != ""
	';

	if( trim($value) ){
		$col = 'pv_value';
		if( $no_casse ){
			$col = 'lower(pv_value)';
			$value = mb_strtolower($value, 'UTF-8');
		}
		if( $full_like ){
			$sql .= ' and '.$col.' like "%'.addslashes($value).'%"';
		}else{
			$sql .= ' and (
				'.$col.' = "'.addslashes($value).'" or
				'.$col.' like "'.addslashes($value).', %" or
				'.$col.' like "%, '.addslashes($value).'" or
				'.$col.' like "%, '.addslashes($value).', %"
			)';
		}
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('fld_fields_get_objects, erreur SQL : '.mysql_error()."\n".$sql);
	}

	return $res;

}
/// \endcond

/// \cond onlyria
/**	Cette fonction détermine si un champ avancé utilise le système de contraintes sur ses valeurs de restriction
 *	Le champ est forcé à False si le type n'est pas une liste de choix
 *	@param int $id Identifiant du champ avancé
 *	@return bool True si le système de contraintes est utilsé, False sinon
 */
function fld_fields_get_use_contraint( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select fld_use_constraint as "used", fld_type_id as "type"
		from fld_fields
		where fld_tnt_id='.$config['tnt_id'].' and fld_id='.$id.'
		and fld_date_deleted is null
	');

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}elseif( !ria_mysql_num_rows($r) ){
		return false;
	}

	$is_select = in_array(ria_mysql_result($r, 0, 'type'), array(FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY));

	return $is_select && ria_mysql_result($r, 0, 'used');
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet d'assigner à un champ avancé qu'il utilise le système de contraintes sur ses valeurs de restriction
 *	Si le champ n'est pas d'un des types liste, il n'est pas possible de procéder à l'assignation
 *	@param int $id Obligatoire, identifiant du champ avancé
 *	@param $use Optionnel, permet d'inverser la condition si False
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_fields_set_use_contraint( $id, $use=true ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$type = fld_fields_get_type( $id );
	if( !$type ) return false;
	if( $use && !in_array($type, array(FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY)) ) return false;

	$r = ria_mysql_query('
		update fld_fields
			set fld_use_constraint='.( $use ? '1' : '0' ).'
		where fld_tnt_id='.$config['tnt_id'].'
			and fld_id='.$id
	);

	if( !$r ){
		return false;
	}

	return true;
}
/// \endcond

/// \cond onlyria
/** Cette fonction récupère des objets à partir des valeurs de ses champs avancés
 *	Elle ne semble pas être utilisée, et ses arguments sont incohérents
 *	@param int $fld_id Obligatoire, non utilisé, identifiant d'un champ avancé
 *	@param array $values Obligatoire, tableau des valeurs à filtrer
 *	@param $symbol Obligatoire, symbole de comparaison entre la valeur en entrée et la valeur stockée
 *	@param $or Optionnel, si plusieurs valeurs en entrée, permet d'effectuer un OU logique entre celles-ci
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- obj : identifiant principal de l'objet
 *		- obj_X : identifiant(s) secondaire(s) de l'objet, ou X est un nombre entre 1 et COUNT_OBJ_ID
 *		- value : Valeur du champ
 */
function fld_field_get_objects_by_values( $fld_id, $values, $symbol, $or = false){
	global $config;

	$is_numeric = true;
	foreach( $values as $val )
		if( !is_numeric($val) ){ $is_numeric = false; break; }

	$is_date = true;
	foreach( $values as $val )
		if( !isdate($val) ){ $is_date = false; break; }

	$get = 'select ';
	for( $i=0; $i<COUNT_OBJ_ID; $i++ )
		$get .= ' pv_obj_id_'.$i.' as "obj'.( $i == 0 ? '' : '_'.($i + 1) ).'", ';

	$get .= '
		pv_value as value
		from fld_object_values
		where pv_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($values)==2 && $symbol=='><' && ($is_numeric || $is_date) ) {
		if( $is_numeric )
			$get .= ' and pv_value>='.$values[0].' && pv_value<='.$values[1];
		elseif( $is_date )
			$get .= ' and convert(concat(substring( pv_value, 7, 4 ), \'-\', substring(pv_value,4,2), \'-\', substring( pv_value, 1, 2 )), DATE)>=\''.$values[0].'\' && convert(concat(substring( pv_value, 7, 4 ), \'-\', substring(pv_value,4,2), \'-\', substring( pv_value, 1, 2 )), DATE)<=\''.$values[1].'\'';
	} else {
		$first = true;
		foreach( $values as $val ){
			$get .= $first || !$or ? ' and' : ' or';
			if( is_numeric($val) )
				$get .= ' pv_value '.$symbol.' '.$val;
			else
				$get .= ' pv_value '.$symbol.' \''.$val.'\'';
			$first = false;
		}
	}

	return ria_mysql_query( $get );
}
/// \endcond

/// \cond onlyria
/** Cette fonction retourne un tableau contenant les catégories pour lesquelles l'un de ces produits sont liés à un champ avancé (triées par nombre de produits liés au champs).
 *	@param int $fld_id Obligatoire, identifiant d'un champs avancé
 *	@param $cat_root Optionnel, identifiant d'une catégorie dans laquelle recherchée
 *	@param $recursive_from_parent Optionnel, détermine si, quand $cat_root est spécifié et supérieur à 0, si les sous-catégories de deuxième niveau et inférieur sont retournées
 *	@param bool $publish Optionnel, par défaut seuls les catéogries publiées seront retournées, mettre false pour toutes les retourner
 *	@param $vals Optionnnel, valeur ou tableaux de valeurs du champs avancés (dans le cas d'une liste hiérarchique, il faut donner l'identifiant de la valeur)
 *	@param $and_vals Optionnel, ignoré si $vals n'est pas un tableau permet de récupérer les catégories contenant des articles liés à toutes les valeurs (par défaut à false, au moins une des valeurs)
 *
 *	@return array Un tableau de catégories contenant pour chacune d'elle :
 *				- id : identifiant de la catégorie
 *				- name : nom de la catégorie
 *				- title : titre de la catégorie
 *				- url_alias : URL de la catégorie
 *				- parent_id : identifiant de la catégorie parente
 *				- products : nombre de produits liés au champs donné en paramètre (et aux valeurs si spécifiées)
 */
function fld_fields_get_prd_categories( $fld_id, $cat_root=0, $recursive_from_parent=false, $publish=true, $vals=false, $and_vals=false ){
	if( !is_numeric($fld_id) || $fld_id<=0 ){
		return false;
	}
	if( !is_numeric($cat_root) || $cat_root<0 ){
		return false;
	}

	$sub_sql_vals = '';

	if( $vals !== false ){
		if( !is_array($vals) ){
			if( trim($vals) == '' ){
				return false;
			}

			$vals = array( $vals );
		}else{
			if( !sizeof($vals) ){
				return false;
			}

			foreach( $vals as $v ){
				if( trim($v) == '' ){
					return false;
				}
			}
		}

		foreach( $vals as $v ){
			if( trim($sub_sql_vals) != '' ){
				$sub_sql_vals .= ( $and_vals ? ' and ' : ' or ' );
			}

			$sub_sql_vals .= '
				(
					lower(pv_value) = lower("'.addslashes( $v ).'")
					or lower(pv_value) like lower("'.addslashes( $v ).',%")
					or lower(pv_value) like lower("%, '.addslashes( $v ).',%")
					or lower(pv_value) like lower("%, '.addslashes( $v ).'")
				)
			';
		}
	}

	global $config;

	$ar_cats = array();

	$sql_prd_ids = '';

	{ // Récupère les produits liés à ce champs avancé (selon la ou les valeurs passées en paramètres)
		$ar_temp_cat = array();

		if( $cat_root > 0 ){
			$ar_temp_cat[] = $cat_root;

			$temp = prd_categories_childs_get_array( $cat_root, $publish, ($publish ? true : false) );
			if( is_array($temp) && sizeof($temp) ){
				$ar_temp_cat = $temp;
			}
		}

		if( $publish ){
			if( $config['tnt_id']==2 ){
				$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
			}else{
				$dps = prd_deposits_get_main();
			}

			if( !$dps ) $dps = 0;
		}

		$sql = '
			select prd_id
			from fld_object_values
				join prd_products on pv_obj_id_0 = prd_id and pv_tnt_id = prd_tnt_id
		';

		if( $publish ){
			$sql .= ' left join prd_stocks on (sto_prd_id = prd_id and sto_tnt_id = prd_tnt_id and sto_dps_id='.$dps.' and sto_is_deleted=0)';
		}

		if( sizeof($ar_temp_cat) ){
			$sql .= ' join prd_classify on (cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id)';
		}

		$sql .= '
			where pv_tnt_id = '.$config['tnt_id'].'
				and pv_fld_id = '.$fld_id.'
		';

		if( $publish ){
			$sql .= ' and prd_publish and prd_publish_cat and (not prd_sleep or (' . prd_stocks_get_sql() . '-ifnull(sto_prepa, 0)) > 0)';
		}

		if( sizeof($ar_temp_cat) ){
			$sql .= ' and cly_cat_id in ('.implode(', ', $ar_temp_cat).')';
		}

		if( trim($sub_sql_vals) != '' ){
			$sql .= ' and ('.$sub_sql_vals.')';
		}

		$sql .= '

			union

			select prd_parent_id as prd_id
			from fld_object_values
				join prd_hierarchy as h on pv_obj_id_0 = prd_child_id and pv_tnt_id = h.prd_tnt_id
		';

		if( $publish ){
			$sql .= '
				join prd_products as p on (p.prd_id = h.prd_parent_id and p.prd_tnt_id = h.prd_tnt_id)
				left join prd_stocks on (sto_prd_id = prd_child_id and sto_tnt_id = h.prd_tnt_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
			';
		}

		if( sizeof($ar_temp_cat) ){
			$sql .= ' join prd_classify on (cly_prd_id = h.prd_parent_id and cly_tnt_id = h.prd_tnt_id)';
		}

		$sql .= '
			where pv_tnt_id = '.$config['tnt_id'].'
				and pv_fld_id = '.$fld_id.'
		';

		if( $publish ){
			$sql .= ' and p.prd_publish and p.prd_publish_cat and (not p.prd_sleep or (' . prd_stocks_get_sql() . '-ifnull(sto_prepa, 0)) > 0)';
		}

		if( sizeof($ar_temp_cat) ){
			$sql .= ' and cly_cat_id in ('.implode(', ', $ar_temp_cat).')';
		}

		if( trim($sub_sql_vals) != '' ){
			$sql .= ' and ('.$sub_sql_vals.')';
		}

		$res = ria_mysql_query( $sql );
		if( !$res ){
			return false;
		}

		while( $r = ria_mysql_fetch_assoc($res) ){
			$sql_prd_ids .= ( trim($sql_prd_ids) != '' ? ', ' : '' ).$r['prd_id'];
		}
	}

	$ar_categories = array();

	if( trim($sql_prd_ids) != '' ){
		$sql = '
			select id, sum(prd_products) as products
			from (
				select cly_cat_id as id, count(*) as prd_products
				from prd_classify
				where cly_tnt_id = '.$config['tnt_id'].'
					and cly_prd_id in ('.$sql_prd_ids.')
				group by cly_cat_id

				union

				select cat_parent_id as id, count(*) as prd_products
				from prd_classify
					join prd_cat_hierarchy on (cat_tnt_id = cly_tnt_id and cat_child_id = cly_cat_id)
				where cly_tnt_id = '.$config['tnt_id'].'
					and cly_prd_id in ('.$sql_prd_ids.')
				group by cat_parent_id
			) as result
			group by id
			order by sum(prd_products) desc
		';

		$res = ria_mysql_query( $sql );
		if( $res ){
			while( $r = ria_mysql_fetch_assoc( $res ) ){
				$ar_cats[ $r['id'] ] = $r['products'];
			}
		}

		$r_cat = prd_categories_get( array_keys($ar_cats), $publish, $cat_root, '', false, false, null, array('set'=>'asc'), array(), $recursive_from_parent );

		if( $r_cat ){
			while( $cat = ria_mysql_fetch_assoc($r_cat) ){
				$ar_categories[] = array(
					'id' 		=> $cat['id'],
					'name' 		=> $cat['name'],
					'title' 	=> $cat['title'],
					'url_alias' => $cat['url_alias'],
					'parent_id' => $cat['parent_id'],
					'products' 	=> array_key_exists( $cat['id'], $ar_cats ) ? $ar_cats[ $cat['id'] ] : 0
				);
			}
		}
	}

	return $ar_categories;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction récupère, pour un champ avancé, la liste des catégories incluant des produits (ou des parents de produits) valorisés pour ce champ.
 *	La fonction tient compte des restrictions d'accès / mercuriales.
 *	A noter que les identifiants des catégories retournés ne sont pas distincts, puisque pour chaque on leur associe toutes les valeurs du champ qui ont pu être trouvées.
 *	Les résultats ne sont pas triés.
 *
 *	@param int $fld_id Obligatoire, identifiant du champ.
 *	@param $cat_root Optionnel, identifiant de la catégorie racine (surcharge de la configuration).
 *	@param $cat_level Optionnel, niveau de profondeur des catégories souhaité (1 étant le niveau en dessous de "cat_root").
 *	@param bool $publish Optionnel, détermine si les produits doivent être publiés (et pas en sommeil, ou éventuellement en déstockage).
 *	@param $prd_on_first_level Optionnel, détermine si des produits sont classés au niveau spécifié dans "$cat_level", ou s'ils sont uniquement classés dans les niveaux inférieurs.
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- cat_id : identifiant de la catégorie.
 *		- value : valeur du champ avancé (note : pour les champs de type "hiérarchique multiple", il s'agit de l'identifiant de la valeur de restriction).
 *	@return bool False en cas d'échec.
 */
function fld_fields_get_categories_and_values( $fld_id, $cat_root=0, $cat_level=1, $publish=true, $prd_on_first_level=false ){

	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}
	if( !is_numeric($cat_level) || $cat_level <= 0 ){
		return false;
	}

	$dps = prd_deposits_get_main();

	global $config;

	$cat_root = is_numeric($cat_root) && $cat_root > 0 ? $cat_root : $config['cat_root'];

	$sql = '
		select c1.cat_id as "cat_id", pv.pv_value as "value"
		from prd_categories as c1
	';

	if( $cat_level > 1 ){
		for( $i = 2; $i <= $cat_level; $i++ ){
			$sql .= '
				join prd_categories as c'.($i).' on c1.cat_tnt_id = c'.$i.'.cat_tnt_id and c'.($i - 1).'.cat_parent_id = c'.$i.'.cat_id
			';
		}
	}

	$sql .= '
		join prd_cat_hierarchy as h on c1.cat_id = h.cat_parent_id and c1.cat_tnt_id = h.cat_tnt_id
		join prd_classify as cly on h.cat_child_id = cly.cly_cat_id and h.cat_tnt_id = cly.cly_tnt_id
		join prd_products as p on cly.cly_tnt_id = p.prd_tnt_id and cly.cly_prd_id = p.prd_id
		join fld_object_values as pv on p.prd_tnt_id = pv.pv_tnt_id and p.prd_id = pv.pv_obj_id_0
		left join prd_stocks on p.prd_tnt_id = sto_tnt_id  and p.prd_id = sto_prd_id and sto_dps_id = '.$dps.'
		where
			c1.cat_date_deleted is null
			and c1.cat_tnt_id = '.$config['tnt_id'].'
			and p.prd_date_deleted is null
			and pv.pv_fld_id = '.$fld_id.'
			and pv.pv_lng_code = "'.$config['i18n_lng'].'"
	';

	// gestion des mercuriales
	if( $config['use_catalog_restrictions'] ){
		$sql .= '
			and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'p.prd_id' ).')
			and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'c1.cat_id' ).')';
	}

	// remonte jusqu'à cat_root
	if( $cat_level > 1 ){
		for( $i = 2; $i <= $cat_level; $i++ ){
			$sql .= ' and c'.($i - 1).'.cat_date_deleted is null';
			if( $i == $cat_level ){
				$sql .= ' and c'.($i - 1).'.cat_parent_id = '.$cat_root;
			}
		}
	}

	// publication des produits ?
	if( $publish ){
		$sql .= '
			and p.prd_publish = 1
			and p.prd_publish_cat = 1
			and (
				p.prd_sleep = 0 or ' . prd_stocks_get_sql() . ' - ifnull(sto_prepa, 0) > 0
			)
		';
	}

	$sql .= '
		union

		select c1.cat_id as "cat_id", pv.pv_value as "value"
		from prd_categories as c1
	';

	if( $cat_level > 1 ){
		for( $i = 2; $i <= $cat_level; $i++ ){
			$sql .= '
				join prd_categories as c'.($i).' on c1.cat_tnt_id = c'.$i.'.cat_tnt_id and c'.($i - 1).'.cat_parent_id = c'.$i.'.cat_id
			';
		}
	}

	$sql .= '
		join prd_cat_hierarchy as h on c1.cat_id = h.cat_parent_id and c1.cat_tnt_id = h.cat_tnt_id
		join prd_classify as cly on h.cat_child_id = cly.cly_cat_id and h.cat_tnt_id = cly.cly_tnt_id
		join prd_products as p on cly.cly_tnt_id = p.prd_tnt_id and cly.cly_prd_id = p.prd_id
		join prd_hierarchy as hp on p.prd_tnt_id = hp.prd_tnt_id and p.prd_id = hp.prd_parent_id
		join prd_products as pchild on hp.prd_tnt_id = pchild.prd_tnt_id and hp.prd_child_id = pchild.prd_id
		join fld_object_values as pv on pchild.prd_tnt_id = pv.pv_tnt_id and pchild.prd_id = pv.pv_obj_id_0
		left join prd_stocks on pchild.prd_tnt_id = sto_tnt_id  and pchild.prd_id = sto_prd_id and sto_dps_id = '.$dps.'
		where
			c1.cat_date_deleted is null
			and c1.cat_tnt_id = '.$config['tnt_id'].'
			and p.prd_date_deleted is null
			and pchild.prd_date_deleted is null
			and pv.pv_fld_id = '.$fld_id.'
			and pv.pv_lng_code = "'.$config['i18n_lng'].'"
	';

	// gestion des mercuriales
	if( $config['use_catalog_restrictions'] ){
		$sql .= '
			and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'p.prd_id' ).')
			and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'pchild.prd_id' ).')
			and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'c1.cat_id' ).')';
	}

	// remonte jusqu'à cat_root
	if( $cat_level > 1 ){
		for( $i = 2; $i <= $cat_level; $i++ ){
			$sql .= ' and c'.($i - 1).'.cat_date_deleted is null';
			if( $i == $cat_level ){
				$sql .= ' and c'.($i - 1).'.cat_parent_id = '.$cat_root;
			}
		}
	}

	// publication des produits ?
	// pour les enfants : pas en sommeil, ou en déstockage
	// pour les parents : publiés et pas en sommeil
	if( $publish ){
		$sql .= '
			and p.prd_publish = 1
			and p.prd_publish_cat = 1
			and p.prd_sleep = 0
			and (
				pchild.prd_sleep = 0 or ' . prd_stocks_get_sql() . ' - ifnull(sto_prepa, 0) > 0
			)
		';
	}

	if( $prd_on_first_level ){

		$sql .= '
			union

			select c1.cat_id as "cat_id", pv.pv_value as "value"
			from prd_categories as c1
		';

		if( $cat_level > 1 ){
			for( $i = 2; $i <= $cat_level; $i++ ){
				$sql .= '
					join prd_categories as c'.($i).' on c1.cat_tnt_id = c'.$i.'.cat_tnt_id and c'.($i - 1).'.cat_parent_id = c'.$i.'.cat_id
				';
			}
		}

		$sql .= '
			join prd_classify as cly on c1.cat_child_id = cly.cly_cat_id and c1.cat_tnt_id = cly.cly_tnt_id
			join prd_products as p on cly.cly_tnt_id = p.prd_tnt_id and cly.cly_prd_id = p.prd_id
			join fld_object_values as pv on p.prd_tnt_id = pv.pv_tnt_id and p.prd_id = pv.pv_obj_id_0
			left join prd_stocks on p.prd_tnt_id = sto_tnt_id  and p.prd_id = sto_prd_id and sto_dps_id = '.$dps.'
			where
				c1.cat_date_deleted is null
				and c1.cat_tnt_id = '.$config['tnt_id'].'
				and p.prd_date_deleted is null
				and pv.pv_fld_id = '.$fld_id.'
				and pv.pv_lng_code = "'.$config['i18n_lng'].'"
		';

		// gestion des mercuriales
		if( $config['use_catalog_restrictions'] ){
			$sql .= '
				and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'p.prd_id' ).')
				and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'c1.cat_id' ).')';
		}

		// remonte jusqu'à cat_root
		if( $cat_level > 1 ){
			for( $i = 2; $i <= $cat_level; $i++ ){
				$sql .= ' and c'.($i - 1).'.cat_date_deleted is null';
				if( $i == $cat_level ){
					$sql .= ' and c'.($i - 1).'.cat_parent_id = '.$cat_root;
				}
			}
		}

		// publication des produits ?
		if( $publish ){
			$sql .= '
				and p.prd_publish = 1
				and p.prd_publish_cat = 1
				and (
					p.prd_sleep = 0 or ' . prd_stocks_get_sql() . ' - ifnull(sto_prepa, 0) > 0
				)
			';
		}

		$sql .= '
			union

			select c1.cat_id as "cat_id", pv.pv_value as "value"
			from prd_categories as c1
		';

		if( $cat_level > 1 ){
			for( $i = 2; $i <= $cat_level; $i++ ){
				$sql .= '
					join prd_categories as c'.($i).' on c1.cat_tnt_id = c'.$i.'.cat_tnt_id and c'.($i - 1).'.cat_parent_id = c'.$i.'.cat_id
				';
			}
		}

		$sql .= '
			join prd_classify as cly on c1.cat_child_id = cly.cly_cat_id and c1.cat_tnt_id = cly.cly_tnt_id
			join prd_products as p on cly.cly_tnt_id = p.prd_tnt_id and cly.cly_prd_id = p.prd_id
			join prd_hierarchy as hp on p.prd_tnt_id = hp.prd_tnt_id and p.prd_id = hp.prd_parent_id
			join prd_products as pchild on hp.prd_tnt_id = pchild.prd_tnt_id and hp.prd_child_id = pchild.prd_id
			join fld_object_values as pv on pchild.prd_tnt_id = pv.pv_tnt_id and pchild.prd_id = pv.pv_obj_id_0
			left join prd_stocks on pchild.prd_tnt_id = sto_tnt_id  and pchild.prd_id = sto_prd_id and sto_dps_id = '.$dps.'
			where
				c1.cat_date_deleted is null
				and c1.cat_tnt_id = '.$config['tnt_id'].'
				and p.prd_date_deleted is null
				and pchild.prd_date_deleted is null
				and pv.pv_fld_id = '.$fld_id.'
				and pv.pv_lng_code = "'.$config['i18n_lng'].'"
		';

		// gestion des mercuriales
		if( $config['use_catalog_restrictions'] ){
			$sql .= '
				and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'p.prd_id' ).')
				and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'pchild.prd_id' ).')
				and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'c1.cat_id' ).')';
		}

		// remonte jusqu'à cat_root
		if( $cat_level > 1 ){
			for( $i = 2; $i <= $cat_level; $i++ ){
				$sql .= ' and c'.($i - 1).'.cat_date_deleted is null';
				if( $i == $cat_level ){
					$sql .= ' and c'.($i - 1).'.cat_parent_id = '.$cat_root;
				}
			}
		}

		// publication des produits ?
		// pour les enfants : pas en sommeil, ou en déstockage
		// pour les parents : publiés et pas en sommeil
		if( $publish ){
			$sql .= '
				and p.prd_publish = 1
				and p.prd_publish_cat = 1
				and p.prd_sleep = 0
				and (
					pchild.prd_sleep = 0 or ' . prd_stocks_get_sql() . ' - ifnull(sto_prepa, 0) > 0
				)
			';
		}

	}

	return ria_mysql_query($sql);
}
/// \endcond

/// \cond onlyria
/**	Cette fonction vérifie que l'identifiant d'objet est valide.
 *
 *	@param $obj Obligatoire, identifiant de l'objet ( tableau d'identifiants pour les clés composées )
 *
 *	@return bool True si l'identifiant d'objet et valide, False dans le cas contraire
 */
function fld_check_object_id($obj){
	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}elseif( !is_numeric($obj) || $obj<=0 ) return false;
	return true;
}
// \endcond

/// @}
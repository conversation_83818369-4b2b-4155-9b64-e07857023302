<?php

require_once('i18n.inc.php');

/**	\defgroup view_i18n Traductions
 *	\ingroup view_admin
 *	Ce module comprend les fonctions necessaires a l'affichage et la gestion des traductions dans l'interface d'administration
 *	@{
 */

/** Permet de récupérer la langue en cours d'utilisation pour écrire les traductions
 *	@return string La langue actuellement utilisee via le parametre $_GET['lng'] s'il existe (1), langue courant en session (2), la langue par defaut sinon (3).
 */
function view_selected_language(){
	global $config;

	// Recupere la langue
	if( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
		$lng = $_SESSION['lng'] = strtolower( $_GET['lng'] );
	}elseif( isset($_SESSION['lng']) && in_array($_SESSION['lng'], $config['i18n_lng_used']) ){
		$lng = strtolower( $_SESSION['lng'] );
	}else{
		$lng = $_SESSION['lng'] = $config['i18n_lng'];
	}

	return $lng;
}

/** Affiche le menu permettant de gérer les traductions (choix de la langue)
 *	@param string $url Obligatoire, URL de la page chargée dans la méthode JavaScript "langPickerCreate".
 *	@param string $lng Optionnel, identifiant de la langue active. Si non renseigné, view_selected_language() sera utilisée.
 *	@param bool $in_menu Optionnel, détermine si le code HTML est englobé dans un div dont la classe est "lng-menu".
 */
function view_translate_menu( $url, $lng='', $in_menu=false ){
	global $config;

	$html = '';

	// Ce menu n'apparait que si l'instance est multi-langues
	if( wst_websites_languages_get_count()<=1 ){
		return '';
	}

	// Si le paramètre $lng n'est pas fourni, récupère la langue active
	if( trim($lng)=='' ){
		$lng = view_selected_language();
	}

	// Affiche le menu
	if( !$in_menu ){
		$html .= ' <div class="lng-menu">';
	}

	// Ordonne la liste des langues pour simplifier la sélection par les utilisateurs
	$langs = array();
	$rlangs = wst_websites_languages_get();
	while( $lang = ria_mysql_fetch_assoc($rlangs) ){
		$langs[ $lang['lng_code'] ] = $lang['name'];
	}
	asort( $langs, SORT_LOCALE_STRING );

	// Affiche le sélecteur de langues
	$html .= ' 	<div id="rialanguagepicker">';
	$html .= ' 		<div class="selectorview" onclick="langPickerCreate(\''.$url.'\') ">';
	$html .= ' 			<div class="left">';
	$html .= ' 				<span class="function_name">'._('Gestion des langues').'</span><br/>';
	$html .= '				<span class="view">'.i18n_languages_get_name($lng).'</span>';
	$html .= ' 			</div>';
	$html .= ' 			<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>';
	$html .= ' 			<div class="clear"></div>';
	$html .= ' 		</div>';
	$html .= ' 		<div class="selector">';
	$html .= '				<a name="p-'.$config['i18n_lng'].'">'.i18n_languages_get_name($config['i18n_lng']).'</a>';
	foreach( $langs as $code=>$name ){
		if( $code==$config['i18n_lng'] ){
			continue;
		}
		$html .= '			<a name="p-'.$code.'">'.htmlspecialchars( $name ).'</a>';
	}
	$html .= ' 		</div>';
	$html .= ' 	</div>';

	if( !$in_menu ){
		$html .= ' </div>';
	}

	return $html;
}

/// @}
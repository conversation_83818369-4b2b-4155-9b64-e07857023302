<?php

require_once('strings.inc.php');

/**	Cette fonction permet le contrôle d'un identifiant de cycle.
 *	@param int $id Obligatoire, identifiant de cycle à vérifier
 *	@return bool true si l'identifiant est valide et correspond à un cycle enregistré dans la base de données, false dans le cas contraire
 */
function boero_cycles_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select ccl_id from boero_cycles where ccl_id='.$id.' and ccl_date_deleted is null'))>0;
}

/**	Cette fonction permet la vérification d'un code cycle
 *	@param string $code Obligatoire, code de cycle à vérifier
 *	@return bool true si le code est valide et correspond à un cycle enregistré dans la base de données, false dans le cas contraire
 */
function boero_cycles_exists_code( $code ){
	if( !trim($code) ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select ccl_id from boero_cycles where ccl_code=\''.addslashes($code).'\' and ccl_date_deleted is null'))>0;
}

/**	Cette fonction permet la création d'un nouveau cycle.
 *	@param string $code Obligatoire, code du cycle
 *	@param string $name Obligatoire, nom du cycle
 *	@param string $desc Obligatoire, description du cycle (préparation).
 *	@return int l'identifiant attribué au cycle en cas de succès, false en cas d'échec
 */
function boero_cycles_add( $code, $name, $desc, $preparation ){
	if( !trim($code) ) return false;
	if( !trim($name) ) return false;

	$code = strtoupper2($code);
	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));
	$preparation = ucfirst(trim($preparation));

	$res = ria_mysql_query('insert into boero_cycles (ccl_code,ccl_name,ccl_desc,ccl_preparation) values (\''.addslashes($code).'\',\''.addslashes($name).'\',\''.addslashes($desc).'\',\''.addslashes($preparation).'\');');
	if( $res ){
		return ria_mysql_insert_id();
	}
	return $res;
}

/**	Cette fonction permet la modification d'un cycle existant
 *	@param int $id Obligatoire, identifiant du cycle à actualiser
 *	@param string $code Obligatoire, code du cycle
 *	@param string $name Obligatoire, nom du cycle
 *	@param string $desc Obligatoire, description du cycle (préparation).
 */
function boero_cycles_update( $id, $code, $name, $desc, $preparation ){
	if( !boero_cycles_exists($id) ) return false;

	if( !trim($code) ) return false;
	if( !trim($name) ) return false;

	$code = strtoupper2($code);
	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));
	$preparation = ucfirst(trim($preparation));

	return ria_mysql_query('
		update boero_cycles set
			ccl_code=\''.addslashes($code).'\',
			ccl_name=\''.addslashes($name).'\',
			ccl_desc=\''.addslashes($desc).'\',
			ccl_preparation=\''.addslashes($preparation).'\'
		where ccl_id='.$id.'
	');
}

/**	Cette fonction permet le chargement d'un ou plusieurs cycles, éventuellement filtrés selon les paramètres optionnels fournis.
 *	@param int $id Facultatif, identifiant d'un cycle sur lequel filtrer le résultat
 *	@param string $code Facultatif, code d'un cycle sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du cycle
 *			- code : code du cycle
 *			- name : désignation du cycle
 *			- desc : description du cycle
 *			- prepa : préparation
 */
function boero_cycles_get( $id=0, $code='' ){
	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select ccl_id as id, ccl_code as code, ccl_name as name, ccl_desc as "desc", ccl_preparation as "prepa"
		from boero_cycles
		where ccl_date_deleted is null
	';
	if( $id>0 ){
		$sql .= ' and ccl_id='.$id;
	}
	if( trim($code) ){
		$sql .= ' and ccl_code=\''.addslashes($code).'\'';
	}
	$sql .= ' order by ccl_code';
	return ria_mysql_query($sql);
}

/**	Cette fonction permet la suppression (virtuelle) d'un cycle
 *	@param int $id Obligatoire, identifiant du cycle à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function boero_cycles_del( $id ){
	if( !is_numeric($id) ) return false;
	return ria_mysql_query('update boero_cycles set ccl_date_deleted=now() where ccl_id='.$id);
}

/**	Cette fonction permet l'ajout d'une étape à un cycle
 *	@param int $ccl Obligatoire, identifiant du cycle à modifier
 *	@param int $prd Obligatoire, identifiant du produit à appliquer
 *  @param int $layers Facultatif, nombre de couches à appliquer
 *  @param int $mode Facultatif, mode d'application
 *	@return bool true en cas de succès, false en cas d'échec
 */
function boero_cycle_products_add( $ccl, $prd, $layers, $mode=25 ){
	if( !boero_cycles_exists($ccl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($mode) ) return false;

	if( !is_numeric($layers) || $layers<=0 ){
		$layers = 'null';
	}

	$pos = ria_mysql_result(ria_mysql_query('select max(cp_pos) from boero_cycle_products where cp_ccl_id='.$ccl),0,0);
	$pos++;

	$res = ria_mysql_query('insert into boero_cycle_products (cp_ccl_id,cp_prd_id,cp_layers,cp_pos,cp_mode) values ('.$ccl.','.$prd.','.$layers.','.$pos.','.$mode.');');
	return $res;
}

/**	Cette fonction permet la mise à jour d'une étape à un cycle
 *	@param int $ccl Obligatoire, identifiant du cycle à modifier
 *	@param int $prd Obligatoire, identifiant du produit à appliquer
 *  @param int $layers Facultatif, nombre de couches à appliquer
 *  @param int $mode Facultatif, mode d'application
 *	@return bool true en cas de succès, false en cas d'échec
 */
function boero_cycle_products_update( $ccl, $prd, $pos, $layers, $mode=25, $newpos = 0 ){
	if( !boero_cycles_exists($ccl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($mode) ) return false;
	if( !is_numeric($pos) ) return false;

	if( !is_numeric($layers) || $layers<=0 ){
		$layers = 'null';
	}

	//$pos = ria_mysql_result(ria_mysql_query('select max(cp_pos) from boero_cycle_products where cp_ccl_id='.$ccl),0,0);
	//$pos++;
	$update = 'update boero_cycle_products set cp_prd_id='.$prd.',cp_layers='.$layers.',cp_mode='.$mode;

	if($newpos != 0){ $update .= ' ,cp_pos='.$newpos; }

	$update.= ' where cp_ccl_id='.$ccl.' and cp_pos='.$pos ;

	return ria_mysql_query($update);
}

/**	Cette fonction permet le chargement des produits impliqués dans un cycle
 *	@param $ccl Obligatoire, identifiant du cycle pour lequel on souhaite charger les produits
 */
function boero_cycle_products_get( $ccl, $prd=0, $pos=0 ){
	global $config;
	if( !is_numeric($ccl) ) return false;

	$sql = '
		select prd_id as id, prd_ref as ref, prd_name as name, cp_layers as layers, cp_mode as mode, cp_pos as "pos", cp_ccl_id as ccl_id
		from boero_cycle_products, prd_products
		where prd_tnt_id='.$config['tnt_id'].' and cp_prd_id=prd_id and prd_date_deleted is null and cp_ccl_id='.$ccl.'
	';
	if( $prd>0){
		$sql .= ' and prd_id='.$prd;
	}
	if( $pos>0){
		$sql .= ' and cp_pos='.$pos;
	}
	$sql .= '
		order by cp_pos asc
	';

	return ria_mysql_query($sql);
}
/**	Cette fonction permet le chargement des produits enfant d'un produit du cycle
 *	@param int $id Obligatoire, identifiant du produit pour lequel on veut les enfants
 */
function boero_products_childs_get( $id ){
	if( !is_numeric($id) ) return false;

	global $config;
	$sql = '
		select *
		from prd_hierarchy h, prd_products pp, prd_classify cc , prd_categories c
		where c.cat_tnt_id='.$config['tnt_id'].' and pp.prd_tnt_id=c.cat_tnt_id and cc.cly_tnt_id=c.cat_tnt_id and h.prd_tnt_id=c.cat_tnt_id
			and h.prd_parent_id='.$id.'
			and h.prd_child_id=pp.prd_id
			and pp.prd_date_deleted is null
			and cc.cly_prd_id=pp.prd_id
			and cc.cly_cat_id=c.cat_id
			and pp.prd_orderable is true
		group by pp.prd_id
	';

	return ria_mysql_query($sql);
}
/**	Cette fonction permet le chargement du produit du cycle
 *	@param int $id Obligatoire, identifiant du produit
 */
function boero_products_get( $id ){
	if( !is_numeric($id) ) return false;

	global $config;
	$sql = '
		select *
		from prd_products pp, prd_classify cc , prd_categories c
		where c.cat_tnt_id='.$config['tnt_id'].' and cc.cly_tnt_id=c.cat_tnt_id and pp.prd_tnt_id=c.cat_tnt_id
			and pp.prd_id='.$id.'
			and pp.prd_date_deleted is null
			and cc.cly_prd_id=pp.prd_id
			and cc.cly_cat_id=c.cat_id
		group by pp.prd_id
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet la suppression d'un étape de cycle
 *
 *
 * @param $ccl Identifiant du cycle
 * @param int $pos Numéro de l'étape
 */
function boero_cycle_products_del( $ccl, $pos ){
	if( !is_numeric($ccl) ) return false;
	if( !is_numeric($pos) ) return false;

	return ria_mysql_query('delete from boero_cycle_products where cp_ccl_id='.$ccl.' and cp_pos='.$pos);
}


/** Cette fonction doit récuperer la quantité en stock du produit
 *
 *
 * @param int $id Identifiant du produit
 *	@return la valeur de la quantité dispo en cas de succès, false en cas d'échec
 */
function boero_qte_stock( $id ){
	if( !is_numeric($id) ) return false;

	global $config;
	$sql = '
		select *
		from prd_stocks
		where sto_tnt_id='.$config['tnt_id'].' and sto_prd_id='.$id.' and sto_is_deleted=0
	';

	return ria_mysql_query($sql);
}


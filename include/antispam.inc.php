<?php
// \cond onlyria
require_once('db.inc.php');

/** \defgroup model_antispam Antispam
 *	\ingroup tools
 *	Les fonctions contenues dans ce module servent à gérer des listes noires de mots clés et d'adresses IP émettant du spam ou des commandes frauduleuses.
 *	@{
 */

/**	Vérifie si un message contient des termes non autorisés.
 *	@param string $text Message à contrôler
 *	@return int l'identifiant d'un mot interdit contenu dans le message, ou 0 si aucun
 *	@todo cette fonction ne respecte pas nos règles de nommage
 */
function verify_message( $text ){

    // IP envoyant du spam
	$blacklist = ats_ips_get_list();
    if( isset($_SERVER['REMOTE_ADDR']) && in_array( $_SERVER['REMOTE_ADDR'], $blacklist )!==false ){
		return -1;
	}

	$antispam = ats_keywords_get_list( true );

	while( $as = ria_mysql_fetch_array( $antispam ) ){
		if( stristr( $text, $as[ 'value' ] ) ){
			return $as[ 'id' ];
		}
	}

	return 0;
}

/**	Récupére la liste des mots clés interdits
 *	@param bool $actif Optionnel, retourne seulement les filtres actifs si vrai, tous les filtres si faux
 *	@param bool $only_generic Optionnel, retourne seulement les filtres commun à tous les clients, par défaut à false
 *	@return resource un résultat de requête comprenant les colonnes suivantes
 *			- tnt_id : identifiant du locataire
 *			- id : identifiant du mot
 *			- value : le mot interdit
 *			- actif : si oui ou non l'interdication est activée sur le mot
 */
function ats_keywords_get_list( $actif=true, $only_generic=false ){
	global $config;

	$sql = '
		 select key_tnt_id as tnt_id, key_id as id, key_value as value, key_actif as actif
		 from ats_keywords
		 where key_date_deleted is null
	';

	if( $only_generic ){
		$sql .= ' and key_tnt_id=0';
	}else{
		$sql .= ' and (key_tnt_id=0 or key_tnt_id=' . $config[ 'tnt_id' ] .')';
	}

	if( $actif ){
		$sql .= ' and key_actif';
	}

	$sql .= ' order by key_tnt_id asc, key_actif desc, key_value';

	return ria_mysql_query($sql);
}

/**	Récupére la valeur d'une expression
 *	@param int $id Optionnel, identifiant d'un antispam
 *	@param int $keyword Optionnel, valeur de l'antispam
 *	@param bool $only_generic Optionnel, retourne seulement les filtres commun à tous les clients, par défaut à false
 *	@return resource un résultat de requête comprenant les colonnes suivantes
 *			- tenant : identifiant du locataire, 0 s'il s'agit d'un filtre général
 *			- id : identifiant du mot
 *			- value : le mot interdit
 *			- actif : filtre activé ou désactivé
 *			- date_deleted : date de suppression
 */
function ats_keywords_get( $id=0, $keyword=0, $only_generic=false ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($keyword) || $keyword<0 ) return false;
	global $config;

	$sql = '
		select key_tnt_id as tenant, key_id as id, key_value as value, key_actif as actif, key_date_deleted as date_deleted
		from ats_keywords
		where 1
	';

	if( $only_generic ){
		$sql .= ' and key_tnt_id=0';
	}else{
		$sql .= ' and (key_tnt_id=0 or key_tnt_id=' . $config[ 'tnt_id' ] .')';
	}

	if( $id>0 ){
		$sql .= ' and key_id='.$id;
	}

	if( trim($keyword)!=='' ){
		$sql .= ' and key_value=\''.$keyword.'\'';
	}

	return ria_mysql_query($sql);
}

/**	Récupére le nombre de messages bloqués par un mot clé.
 *	@param int $key_id Obligatoire, identifiant d'un spam
 *	@param bool $all_rights Optionnel, par défaut à false, mettre true pour ne pas tenir compte de l'identifiant du client
 *	@param int $wst_id Optionnel, permet de filter pour un site donné. Par défaut les spams de tous les sites sont retournés
 *
 *	@return resource un résultat de requête comprenant les colonnes suivantes
 *			- nb_keyword : nombre de messages bloqués par le mot clé
 */
function ats_keywords_count( $key_id, $all_rights=false, $wst_id=false ){
	if( !is_numeric($key_id) || $key_id<0 ) return false;
	global $config;

	$sql = '
		 select count(*) as nb_keyword
		 from gu_messages
		 where '.( $all_rights ? '1' : 'cnt_tnt_id='.$config[ 'tnt_id' ] ).'
		 and cnt_spam_id='. $key_id .'
	';

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}

	return ria_mysql_query($sql);
}

/**	Ajoute un mot clé à la liste noire de l'antispam
 *	@param string $keyword mot clé à ajouter
 *	@param bool $all_rights par défaut à false, mettre à true pour insérer un antispam commun à tous les clients.
 *	@return int un nombre indiquant le statut de la fonction
 *			- 1 : l'insertion à réussi
 *			- 2 : l'insertion a échoué
 *	@todo cette fonction devrait retourner un booléen plutôt qu'un code numérique spécifique
 */
function ats_keywords_add( $keyword, $all_rights=false ){
	global $config;

	if( trim($keyword)=='' ){
		return false;
	}

	$sql = '
		 select *
		 from ats_keywords
		 where (key_tnt_id=0 or key_tnt_id='. $config[ 'tnt_id' ] .')
		 and key_date_deleted is null and key_value=\''. addslashes( $keyword ) .'\'
	';

	$nb = ria_mysql_num_rows( ria_mysql_query($sql) );
	if( $nb > 0){
		return 2;
	}

	ria_mysql_query('
		 insert into ats_keywords(key_tnt_id, key_value)
		 values('.( $all_rights ? '0' : $config[ 'tnt_id' ] ).',\''. addslashes( $keyword ) .'\');
	');

	return ria_mysql_insert_id();
}

/**	Retire un mot clé de la liste noire utilisée par l'antispam
 *	@param int $keyword identifiant du mot clé à supprimer
 *	@param bool $all_rights par défaut à false, mettre true pour mettre la suppression d'un filtre commun à tous les tenants
 *	@return bool Retourne true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function ats_keywords_del( $keyword, $all_rights=false ){
	if( !is_numeric($keyword) || $keyword<0 ) return false;
	global $config;

	if( $all_rights ){
		$tenants = tnt_tenants_get();
		while( $tenant = ria_mysql_fetch_array($tenants) ){
			$config['tnt_id'] = $tenant['id'];
			$spams = ats_keywords_get( 0, $keyword );
			if( $spams!=false ){
				$spam = ria_mysql_fetch_array($spams);
				$messages = messages_get( 0 , "", $spam['id'] );
				if( $messages!=false ){
					while( $message = ria_mysql_fetch_array($messages) ){
						if( !messages_states_set($message['id'], 1) )
							return false;
					}
				}
			}
		}
	} else {
		$spams = ats_keywords_get( 0, $keyword );
		if( $spams!=false ){
			$spam = ria_mysql_fetch_array($spams);
			$messages = messages_get( 0 , "", $spam['id'] );
			if( $messages!=false ){
				while( $message = ria_mysql_fetch_array($messages) ){
					if( !messages_states_set($message['id'], 1) )
						return false;
				}
			}
		}
	}

	return ria_mysql_query('
		update ats_keywords
		set key_date_deleted=now()
		where key_tnt_id='.( $all_rights ? '0' : $config['tnt_id'] ).'
		and key_value=\''.$keyword.'\'
	');
}

/**	Désactive une expression clé
 *	@param string $keyword Obligatoire, texte du mot clé à désactiver
 *	@param bool $all_rights Facultatif, par défaut à false, mettre true pour désactiver un filtre commun à tous les tenants
 *	@return bool Retourne true en cas de succès, false en cas d'échec
 */
function ats_keywords_deactivate( $keyword, $all_rights=false ){
	if( trim($keyword)=='' ) return false;
	global $config;

	return ria_mysql_query('
		 update ats_keywords
		 set key_actif=0
		 where '.( $all_rights ? 'key_tnt_id=0' : 'key_tnt_id='. $config[ 'tnt_id' ] ).'
		 and key_value=\''. $keyword .'\'
		 and key_actif=1
	');

}

/**	Active l'expression clé
 *	@param string $keyword mot clé à activer
 *	@param bool $all_rights par défaut à false, mettre true pour activer un filtre commun à tous les tenants
 *	@return bool Retourne true en cas de succès, false en cas d'échec
 */
function ats_keywords_activate( $keyword, $all_rights=false ){
	if( trim($keyword)=='' ) return false;
	global $config;

	return ria_mysql_query('
		update ats_keywords
			set key_actif=1
		where '.( $all_rights ? 'key_tnt_id=0' : 'tnt_id='. $config[ 'tnt_id' ] ).'
			and key_value=\''. $keyword .'\'
			and key_actif=0
	');

}

/** Cette fonction permet d'ajouter une ip à la liste noire
 *	@param string $ip Obligatoire, adresse ip à bloquer
 * 	@param bool $all_right Facultatif, par défaut à false, mettre true pour ajouter une ip à la liste noire globale
 *	@return bool True en cas de succès (retourne -1 si l'ip est déjà désactivée)
 *	@return bool False en cas d'échec
 */
function ats_ips_add( $ip, $all_right=false ){
	global $config;

	$rip = ria_mysql_query( 'select ip_date_deleted as deleted from ats_ips where ip_tnt_id='.$config['tnt_id'].' and ip=\''.$ip.'\'' );
	if( $rip && ria_mysql_num_rows($rip) ){
		$sql = '
			update ats_ips
			set ip_date_created=now(),
				ip_date_deleted=null
			where ip_tnt_id='.$config['tnt_id'].'
				and ip=\''.$ip.'\'
		';
	}else{
		$sql = '
			insert into ats_ips ( ip_tnt_id, ip )
			values ( '.( $all_right ? '0' : $config['tnt_id'] ).', \''.$ip.'\' )
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne les adresses IP contenues dans la liste noire
 *	@param string $ip Optionnel, adresse ip
 *	@param bool $all_rights Optionnel, par défaut à false, mettre true pour retourner les ip contenues dans la liste noire global
 *	@return resource Un résultat MySQL contenant :
 *			- tenant : identifiant du tenant
 *			- ip : adresse ip
 *			- date_block : date d'ajout à la liste noire
 */
function ats_ips_get( $ip='', $all_rights=false ){
	global $config;

	$sql = '
		select ip_tnt_id as tenant, ip, ip_date_created as date_block
		from ats_ips
		where '.( $all_rights ? 'ip_tnt_id=0' : '( ip_tnt_id=0 or ip_tnt_id='.$config['tnt_id'].' )' ).'
			and ip_date_deleted is null
	';

	if( trim($ip)!='' ){
		$sql .= ' and ip=\''.$ip.'\'';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne un tableau contenant toutes les adresses ips contenues dans la liste noire
 *	@return array Retourne un tableau contenant toutes les ips bloquées
 */
function ats_ips_get_list(){
	// Récupère les adresses ip
	$ips = ats_ips_get();

	if( !$ips || !ria_mysql_num_rows($ips) ){
		return array();
	}

	$tb_ips = array();
	while( $ip = ria_mysql_fetch_array($ips) ){
		$tb_ips[] = $ip['ip'];
	}

	return $tb_ips;
}


/** Cette fonction permet de vérifier si une adresse ip n'est pas déjà présente dans la liste noire
 *	@param string $ip Obligatoire, adresse ip
 *	@return bool Retourne true si elle est déjà présente, false si le parmètre est omis ou bien si l'adresse ip n'est pas déjà présente
 */
function ats_ips_exists( $ip ){
	if( trim($ip)=='' ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from ats_ips where (ip_tnt_id=0 or ip_tnt_id='.$config['tnt_id'].' ) and ip_date_deleted is null and ip=\''.$ip.'\'') )>0;
}

/** Cette fonction permet de supprimer un adresse ip de la liste noire
 *	@param string $ip Obligatoire, adresse ip à retirer
 *	@param bool $all_rights Optionnel, par défaut l'ip doit appartenir à un locataire précis, mettre true pour supprimer une ip de la liste noire global
 *	@return bool Retourne true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function ats_ips_del( $ip, $all_rights=false ){
	if( trim($ip)=='' ) return false;
	global $config;

	return ria_mysql_query('
		update ats_ips
			set ip_date_deleted=now()
		where ip_tnt_id='.( $all_rights ? '0' : $config['tnt_id'] ).'
			and ip=\''. addslashes($ip) .'\''
	);
}
/** @} */

// \endcond
<?php
/** \defgroup cgv Conditions Générales de Vente
 *	\ingroup configuration
 *
 *	Ce module contient les fonctions utilisées pour les conditions générales de vente.
 *
 *	@todo Chaque commande doit pointer sur les conditions qui se sont appliquées lorsqu'elles ont été passées.
 *	@todo Gérer la notion de publication, afin de laisser le temps au propriétaire de rédiger ses articles (publication différée)
 *	@todo La publication à date prédéfinie serait également intéressante.
 *
 *	@todo Pour des raisons légales (article L. 134-2 du code de la consommation), chaque modification des conditions générales de vente devra provoquer l'archivage de la version précédente.
 *	@todo L'idéal serait que les conditions générales de vente soit archivées au format PDF.
 *
 *	@todo Indexer le contenu des CGV à l'aide du moteur de recherche
 *
 *	@todo Gestion des sous-articles
 *	@todo Disponibilité au format PDF. Version automatique + version personnalisée
 *
 *	@{
 */

require_once('db.inc.php');
require_once('define.inc.php');
require_once('obj_position.inc.php');
require_once('search.inc.php');
require_once('ria.queue.inc.php');

// \cond onlyria
/** Cette fonction permet de ré-indexer les CGV dans le moteur de recherche
 *	@param int $ver_id Optionnel, identifiant d'une version de CGV à ré-indexer
 *	@return bool true si la ré-indexation s'est correctement déroulée, False dans le cas contraire
 */
function cgv_versions_index_rebuild( $ver_id=0 ){
	if( !is_numeric($ver_id) || $ver_id < 0 ){
		return false;
	}

	$rversion = cgv_versions_get( $ver_id > 0 ? $ver_id : null );
	if( $rversion ){
		while( $version = ria_mysql_fetch_assoc($rversion) ){
			cgv_versions_index( $version['id'] );
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des versions des conditions générales de vente.
 *	Seul les articles des versions non publiées pourront être modifiés par les utilisateurs du back-office.
 *
 *	@param int $id Optionnel, identifiant de la version à charger.
 *	@param int $website Optionnel, identifiant de site
 *	@param bool $all_website Optionnel, détermine si tous les sites sont concernés (uniquement si $website n'est pas spécifié)
 *	@param string $date_last_from Optionnel, permet de récupérer les CGV actives à une date donnée.
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête mysql, comprenant les colonnes suivantes :
 *		- id : identifiant interne de la version
 *		- name : nom de la version
 *		- publish-date : date de publication de la version.
 */
function cgv_versions_get( $id=NULL, $website=false, $all_website=false, $date_last_from=false ){

	if( $website && !is_numeric($website) ){
		return false;
	}

	global $config;

	$sql = '
		select ver_id as id, ver_name as name, date_format(ver_publish_date,"%d/%m/%Y") as "publish-date"
		from cgv_versions
		where ver_tnt_id='.$config['tnt_id'].'
			and ver_is_deleted = 0
	';

	if( $website ){
		$sql .= ' and ver_wst_id = '.$website;
	}elseif( !$all_website ){
		$sql .= ' and ver_wst_id = '.$config['wst_id'];
	}

	if( is_numeric($id) ){
		$sql .= ' and ver_id='.$id;
	}

	if( isdateheure($date_last_from) ){
		$sql .= ' and ver_publish_date <= "'.dateheureparse($date_last_from).'"';
	}

	// tri et limite selon les paramètres
	if( isdateheure($date_last_from) ){
		$sql .= ' order by ver_publish_date desc';
		$sql .= ' limit 0, 1';
	}else{
		if( !is_numeric($id) ){
			$sql .= ' order by ver_publish_date desc';
		}else{
			$sql .= ' limit 0, 1';
		}
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Permet la création d'une nouvelle version vierge des conditions générales de vente.
 *	Pour être valide, le nom de la version ne doit pas être vide.
 *
 *	@param string $name Obligatoire, nom de la version. Ce nom est réservé au classement par le propriétaire de la boutique, et n'est pas utilisé dans la partie publique.
 *	@param int $website Facultatif, identifiant de site
 *
 *	@return int l'identifiant interne attribué à la version en cas de succès.
 *	@return bool false en cas d'échec
 *
 */
function cgv_versions_add( $name, $website=false ){
	if( $website && !is_numeric($website) ) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	$name = ucfirst(trim($name));
	if( $name=='' ) return false;

	if( ria_mysql_query("insert into cgv_versions (ver_tnt_id,ver_wst_id,ver_name) values (".$config['tnt_id'].",".$wst.",'".addslashes($name)."')") )
		return ria_mysql_insert_id();
	else
		return false;

}
// \endcond

// \cond onlyria
/**	Permet la mise à jour du nom d'une version.
 *	Pour être valide, le nom de la version ne doit pas être vide.
 *
 *	@param int $id Obligatoire, identifiant interne de la version à modifier
 *	@param string $name Obligatoire, nom de la version. Ce nom est réservé au classement par le propriétaire de la boutique, et n'est pas utilisé dans la partie publique.
 *	@param int $website Optionnel, identifiant de site
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_versions_update( $id, $name, $website=false ){
	if( $website && !is_numeric($website) ) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	if( !cgv_versions_exists($id, $wst) ) return false;

	$name = ucfirst(trim($name));
	if( $name=='' ) return false;

	return ria_mysql_query("update cgv_versions set ver_name='".addslashes($name)."' where ver_tnt_id=".$config['tnt_id']." and ver_wst_id=".$wst." and ver_id=".$id);

}
// \endcond

// \cond onlyria
/** Duplique une version des conditions générales de vente pour édition.
 *  La copie n'est pas publiée par défaut.
 *  @param int $id Identifiant de la version des conditions générales de vente à dupliquer
 *	@param int $website Identifiant du site
 *  @return int l'identifiant de la nouvelle version en cas de succès
 *  @return bool false en cas d'échec
 */
function cgv_versions_copy( $id , $website=false ){
	if( $website && !is_numeric($website) ) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];


	if( !cgv_versions_exists($id, $wst) ) return false;

	if( !ria_mysql_query('
		insert into cgv_versions (ver_tnt_id,ver_wst_id,ver_name)
			select ver_tnt_id, ver_wst_id, concat("Copie de ",ver_name)
			from cgv_versions where ver_tnt_id='.$config['tnt_id'].' and ver_wst_id='.$wst.' and ver_id='.$id
	) ) return false;

	$cid = ria_mysql_insert_id();

	// récupère les articles
	$rart = cgv_articles_get( null, null, $id, $website );

	if( $rart && ria_mysql_num_rows($rart) ){

		while( $art = ria_mysql_fetch_array($rart) ){

			// copie de l'article
			$sql = '
				insert into cgv_articles
					( art_tnt_id, art_wst_id, art_name, art_desc, art_pos, art_ver_id )
				select art_tnt_id, art_wst_id, art_name, art_desc, art_pos,'.$cid.'
				from cgv_articles
				where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.'
					and art_ver_id='.$id.'
					and art_id='.$art['id'].'
			';

			if( !ria_mysql_query($sql) ){
				return false;
			}

			$newart = ria_mysql_insert_id();

			// copie des champs avancés (notamment pour la traduction)
			$sql = '
				insert into fld_object_values
					( pv_tnt_id, pv_lng_code, pv_obj_id_0, pv_fld_id, pv_value )
				select pv_tnt_id, pv_lng_code, '.$newart.', pv_fld_id, pv_value
				from fld_object_values
					join fld_fields on ((pv_tnt_id=fld_tnt_id or fld_tnt_id=0) and pv_fld_id=fld_id)
				where pv_tnt_id='.$config['tnt_id'].'
					and pv_obj_id_0='.$art['id'].'
					and fld_cls_id='.CLS_CGV_ARTICLE.'
			';

			if( !ria_mysql_query($sql) ){
				return false;
			}
		}

	}

	return $cid;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de version.
 *	L'objectif de cette fonction est de vérifier qu'un identifiant de version est valide et correspond à une
 *	version enregistrée dans la base de données.
 *
 *	@param int $id Obligatoire, identifiant de version à tester
 *	@param int $website Facultatif, identifiant de site
 *	@return bool true si l'identifiant est valide et correspond à une version disponible dans la base de données
 *	@return bool false si l'identifiant est invalide
 *
 */
function cgv_versions_exists( $id, $website=false ){
	if( $website && ( !is_numeric($website) || $website<=0 ) ) return false;
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$wst = null;
	if( $website !== null ){
		if( !$website ){
			$wst = $config['wst_id'];
		}else{
			$wst = $website;
		}
	}

	$sql = '
		select ver_id from cgv_versions
		where ver_tnt_id='.$config['tnt_id'].'
			and ver_id='.$id.'
			and ver_is_deleted = 0
	';

	if( $wst !== null ){
		$sql .= ' and ver_wst_id='.$wst;
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return ria_mysql_num_rows($r)>0;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une version des conditions générales de vente.
 *	L'opération ne sera autorisée que si la version n'a jamais été publiée sur le site.
 *
 *	@param int $id Obligatoire, identifiant de la version à supprimer.
 *	@param int $website Facultatif, identifiant d'un site web
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_versions_del( $id, $website=false ){

	if( $website !== false && ( !is_numeric($website) || $website <= 0 ) ){
		return false;
	}

	global $config;

	$wst = $config['wst_id'];
	if( $website !== false ){
		$wst = $website;
	}

	$rver = cgv_versions_get( $id, $wst );
	if( !$rver || !ria_mysql_num_rows($rver) ){
		return false;
	}
	$ver = ria_mysql_fetch_assoc($rver);

	if( trim($ver['publish-date']) ){
		return false;
	}

	$sql = '
		delete from cgv_articles
		where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_ver_id='.$id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		update cgv_versions
		set ver_is_deleted = 1
		where ver_tnt_id='.$config['tnt_id'].' and ver_wst_id='.$wst.' and ver_id='.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la publication d'une version des CGV sur le site.
 *	Une fois publiée, cette version ne peut plus être modifiée. Seules les versions contenant
 *	au minimum un article pourront être publiées.
 *
 *	Lors de sa publication, la version des CGV est indexée par le moteur de recherche
 *
 *	@param int $id Identifiant de la version à publier
 *	@param int $website Identifiant du site
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_versions_publish( $id, $website=false ){
	if( $website && !is_numeric($website) ){
		return false;
	}

	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	if( !cgv_versions_exists($id, $wst) || cgv_versions_is_empty($id, $wst) ){
		return false;
	}

	$old_current = cgv_versions_get_current();

	if( is_numeric($old_current) && $old_current > 0 ){
		try{
			// Index la version des CGV dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_CGV_VERSION,
				'obj_id_0' => $old_current,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	if( $res = ria_mysql_query('update cgv_versions set ver_publish_date=now() where ver_tnt_id='.$config['tnt_id'].' and ver_wst_id='.$wst.' and ver_id='.$id) ){
		try{
			// Index la version des CGV dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_CGV_VERSION,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Indexe une version des conditions générales de vente. Etant donné que les urls des contenus
 *	du moteur de recherche doivent être uniques, une seule entrée de l'index est utilisé par les
 *	cgv et une seule par article.
 *
 *	@param int $id Identifiant de la version des cgv à indexer
 *	@param int $website Optionnel, identifiant du site sur lequel se trouve les cgv
 *	@param string $lng Optionnel code ISO 639-1 de la langue, par défaut la FAQ est indexé par la langue du site
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_versions_index( $id, $website=false, $lng=false ){
	if( !cgv_versions_exists($id) ) return false;
	if( $website && !is_numeric($website) ) return false;
	global $config;

	$lng = $lng && in_array($lng, $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	$website ? $wst = $website : $wst = $config['wst_id'];

	$url = '/mentions-legales/cgv';
	$name = i18n::get( 'Conditions Générales de Vente' );
	$desc = i18n::get( 'Conditions générales de vente appliquables à toutes commandes passées sur le site' );

	$content = "cgv\n";

	$publish_cgv = cgv_versions_get_current() == $id ? true : false;

	foreach( $lng as $l ){
		// Indexe la version
		$cid = search_index_content( $url, 'cgv', $name, $desc, $content, '/admin/config/cgv/articles.php?ver='.$id, $publish_cgv, $id, false, '', '', $l );

		// Indexe les articles
		if( $cid ){
			$url = '/mentions-legales/cgv#article';
			$count = 1;
			$articles = cgv_articles_get(NULL,NULL,$id, $wst);
			while( $r = ria_mysql_fetch_array($articles) ){
				search_index_content( $url.($count++), 'cgv-art', $r['name'], $r['desc'], $r['name']."\n".$r['desc']."\n\n", '/admin/config/cgv/edit.php?ver='.$id.'&art='.$r['id'], $publish_cgv, $r['id'], $id, '', '', $l );
			}
		}
	}

	return $cid;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si la version contient au minimum un article.
 *
 *	@param int $id Identifiant de la version des conditions générales de vente à tester
 *	@param int $website Identifiant du site
 *
 *	@return bool true si la version ne contient aucun article
 *	@return bool false si la version contient au moins un article
 */
function cgv_versions_is_empty( $id, $website=false ){
	if( $website && !is_numeric($website) ) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	return ria_mysql_result(ria_mysql_query('select count(art_id) from cgv_articles where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_ver_id='.$id),0,0)<1;
}
// \endcond

/**	Retourne l'identifiant de la version des Conditions Générales en cours de validité.
 *
 *	@param int $wst Facultatif, identifiant d'un website (valeur par défaut : $config['wst_id'])
 *
 *	@return int l'identifiant de la version s'appliquant actuellement
 *	@return bool false si aucune version ne s'applique
 *
 */
function cgv_versions_get_current( $wst=0 ){
	global $config;

	$wst_id = $config['wst_id'];
	if( $wst!=0 && is_numeric($wst) && $wst>0 ){
		$wst_id = $wst;
	}

	$res = ria_mysql_query('
		select ver_id from cgv_versions
		where ver_tnt_id='.$config['tnt_id'].'
			and ver_wst_id='.$wst_id.'
			and ver_publish_date is not null
			and ver_is_deleted=0
		order by ver_publish_date desc
		limit 0,1
	');
	return ria_mysql_num_rows($res) ? ria_mysql_result($res,0,0) : false;
}

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'une version de CGV.
 *	@param int $id Obligatoire, identifiant de la CGV.
 *	@param int $wst Optionnel, identifiant du site (la configuration sera utilisée le cas échéant).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function cgv_versions_set_date_modified( $id, $wst=false ){

	if( $wst !== false && ( !is_numeric($wst) || $wst <= 0 ) ){
		return false;
	}

	global $config;

	if( $wst === false ){
		$wst = $config['wst_id'];
	}

	$sql = '
		update cgv_versions
		set ver_date_modified = now()
		where ver_tnt_id = '.$config['tnt_id'].'
			and ver_wst_id = '.$wst.'
			and ver_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un article aux conditions générales de vente.
 *
 *	@param int $version Obligatoire, identifiant de la version.
 *	@param string $name Obligatoire, titre de l'article.
 *	@param string $desc Obligatoire, contenu de l'article.
 *	@param int $pos Facultatif, position d'affichage (dans son niveau hiérarchique).
 *	@param int $parent Facultatif, identifiant de l'article parent.
 *	@param int $website Facultatif, identifiant de site
 *
 *	@return bool false en cas d'erreur, l'identifiant attribué à l'article en cas de succès.
 *
 */
function cgv_articles_add( $version, $name, $desc, $pos=null, $parent=null, $website=false ){

	if( $website !== false && ( !is_numeric($website) || $website <= 0 ) ){
		return false;
	}

	global $config;

	$wst = $config['wst_id'];
	if( $website !== false ){
		$wst = $website;
	}

	if( !cgv_versions_exists( $version, $wst ) ){
		return false;
	}
	if( !( $name = trim($name) ) ){
		return false;
	}
	if( !( $desc = trim($desc) ) ){
		return false;
	}

	if( $parent != null ){
		if( !is_numeric($parent) || !cgv_articles_exists( $parent, $wst ) ){
			return false;
		}
	}

	$nextpos = cgv_articles_get_maxpos( $version, $parent, $wst ) + 1;

	if( $pos==null ){
		$pos = $nextpos;
	}else{
		if( !is_numeric($pos) || $pos > $nextpos ){
			$pos = $nextpos;
		}elseif( $pos <= 0 ){
			$pos = 1;
		}
	}

	// La position de chaque article doit rester unique. Décale les articles de même niveau
	if( $pos!=$nextpos ){
		ria_mysql_query('
			update cgv_articles
			set art_pos = art_pos + 1
			where art_tnt_id = '.$config['tnt_id'].'
				and art_wst_id = '.$wst.'
				and art_parent_id '.( $parent!=null ? '= '.$parent : 'is null' ).'
				and art_pos >= '.$pos.'
		');
	}

	$name = addslashes(ucfirst($name));
	$desc = addslashes(ucfirst($desc));

	if( $parent==null ){
		$parent = 'null';
	}

	$fields = array('art_tnt_id', 'art_wst_id', 'art_ver_id', 'art_name', 'art_desc', 'art_parent_id', 'art_pos');
	$values = array($config['tnt_id'], $wst, $version, '"'.$name.'"', '"'.$desc.'"', $parent, $pos);

	$sql = 'insert into cgv_articles ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$new_id = ria_mysql_insert_id();

	cgv_versions_set_date_modified( $version, $wst );

	return $new_id;

}
// \endcond

// \cond onlyria
/**	Permet la modification d'un article des conditions générales de vente.
 *
 *	@param int $id Obligatoire, identifiant interne de l'article.
 *	@param string $name Obligatoire, titre de l'article.
 *	@param string $desc Obligatoire, contenu de l'article.
 *	@param int $website Facultatif, identifiant de site
 *
 *	@return bool false en cas d'erreur, true en cas de succès.
 *
 *	@todo Cette implémentation ne gère par l'arborescence des articles
 */
function cgv_articles_update( $id, $name, $desc, $website=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( $website !== false && ( !is_numeric($website) || $website <= 0 ) ){
		return false;
	}

	global $config;

	$wst = $config['wst_id'];
	if( $website !== false ){
		$wst = $website;
	}

	// charge le détail de l'article
	$rart = cgv_articles_get( $id, null, null, $wst );
	if( !$rart || !ria_mysql_num_rows($rart) ){
		return false;
	}
	$art = ria_mysql_fetch_assoc($rart);

	if( !( $name = trim($name) ) ){
		return false;
	}
	if( !( $desc = trim($desc) ) ){
		return false;
	}

	$name = ucfirst($name);
	$desc = ucfirst($desc);

	// aucun changement, retourne True
	if( $art['name'] == $name && $art['desc'] == $desc ){
		return true;
	}

	$sql = '
		update cgv_articles
		set art_name="'.addslashes($name).'", art_desc="'.addslashes($desc).'"
		where art_tnt_id='.$config['tnt_id'].'
			and art_wst_id='.$wst.'
			and art_id='.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		cgv_versions_set_date_modified( $art['ver_id'], $wst );
	}

	return $res;

}
// \endcond

/**	Cette fonction retourne les articles enregistrés, filtrés en fonction des paramètres fournis.
 *
 *	Si le paramètre $parent est omis ou égal à NULL, seuls les articles de premier niveau sont retournés.
 *
 *	@param int $id Optionnel, identifiant d'un article. (ou tableau)
 *	@param int $parent Optionnel, identifiant d'un article parent (-1 pour ne pas filtrer par rapport au parent)
 *	@param int $version Optionnel, identifiant de la version des conditions générales de vente
 *	@param int $website Optionnel, Identifiant du site (null = pas de filtre)
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- wst_id : Identifiant du site
 *			- id : Identifiant de l'article
 *			- name : titre de l'article
 *			- desc : contenu de l'article
 *			- parent_id : Identifiant du parent de l'article
 *			- pos : position de l'article (dans son niveau hiérarchique)
 *			- ver_id : identifiant de version
 *
 */
function cgv_articles_get( $id=NULL, $parent=NULL, $version=NULL, $website=false ){
	if( $website && !is_numeric($website)) return false;
	global $config;

	$wst = null;
	if( $website !== null ){
		if( is_numeric($website) && $website > 0 )
			$wst = $website;
		else
			$wst = $config['wst_id'];
	}

	$sql = 'select art_wst_id as wst_id, art_id as id, art_name as name, art_desc as "desc", art_parent_id as parent_id, art_pos as pos, art_ver_id as ver_id from cgv_articles
		where art_tnt_id='.$config['tnt_id'];
	if( $wst!==null ){
		$sql .= ' and art_wst_id = '.$wst;
	}
	if( is_numeric($id) ){
		$sql .= ' and art_id='.$id;
	}elseif( is_array($id) && sizeof($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ) return false;
		}
		$sql .= ' and art_id in ('.implode(', ', $id).')';
	}

	if( is_numeric($parent) && $parent > 0 )
		$sql .= ' and art_parent_id='.$parent;
	elseif( $parent!==-1 )
		$sql .= ' and art_parent_id is null';

	if( $version!=NULL ){
		if( !cgv_versions_exists($version, $wst) ) return false;
		$sql .= ' and art_ver_id='.$version;
	}

	$sql .= ' order by art_pos';

	if( is_numeric($id) ) $sql .= ' limit 0, 1';

	$r = ria_mysql_query($sql);

	if( !$r && ria_mysql_errno() ){
		error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
	}

	return $r;
}

// \cond onlyria
/**	Cette fonction permet la suppression de l'un des articles des conditions de vente.
 *	Il n'existe pour l'instant pas de suppression logique de l'article, la suppression
 *	est donc définitive et irréversible.
 *
 *	@param int $id Obligatoire, identifiant de l'article
 *	@param int $website Facultatif, identifiant d'un site web
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_articles_del( $id, $website=false ){

	if( $website !== false && ( !is_numeric($website) || $website <= 0 ) ){
		return false;
	}

	global $config;

	$wst = $config['wst_id'];
	if( $website !== false ){
		$wst = $website;
	}

	$rart = cgv_articles_get( $id, null, null, $wst );
	if( !$rart || !ria_mysql_num_rows($rart) ){
		return false;
	}
	$art = ria_mysql_fetch_assoc($rart);

	$sql = '
		update cgv_articles
		set art_pos = art_pos - 1
		where art_tnt_id = '.$config['tnt_id'].'
			and art_wst_id = '.$wst.'
			and art_pos >= '.$art['pos'].'
			and art_parent_id '.( $art['parent_id'] ? ' = '.$art['parent_id'] : ' is null' ).'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		delete from cgv_articles
		where art_tnt_id = '.$config['tnt_id'].'
			and art_id = '.$id.'
			and art_wst_id = '.$wst.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		cgv_versions_set_date_modified( $art['ver_id'], $wst );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester l'existance d'un article dans la base de données.
 *
 *	@param int $id Obligatoire, identifiant de l'article à tester
 *	@param int $website Facultatif, identifiant d'un site
 *
 *	@return bool true si l'article existe, false s'il n'existe pas.
 *
 */
function cgv_articles_exists( $id, $website=false ){
	if($website && !is_numeric($website)) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	if( !is_numeric($id) ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select art_id from cgv_articles where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_id='.$id))==1;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne la plus grande position d'affichage actuellement utilisée pour un niveau hiérarchique donné.
 *
 *	@param int $version Obligatoire, identifiant de la version.
 *	@param int $parent Facultatif, identifiant de l'article parent.
 *	@param int $website Facultatif, identifiant de site
 *
 *	@return int La position maximale utilisée à ce niveau hiérarchique, 0 s'il n'y a aucun article à ce niveau hiérarchique.
 *
 */
function cgv_articles_get_maxpos( $version, $parent=NULL, $website=false ){
	if($website && !is_numeric($website)) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	if( !cgv_versions_exists($version, $wst) ) return false;

	if( $parent!=NULL )
		if( !is_numeric($parent) || $parent<=0 )
			return false;
	$sql = 'select max(art_pos) from cgv_articles where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_ver_id='.$version;
	if( $parent )
		$sql .= ' and art_parent_id='.$parent;
	else
		$sql .= ' and art_parent_id is null';

	$res = ria_mysql_result(ria_mysql_query($sql),0,0);
	return $res=='' ? 0 : $res;
}
// \endcond

// \cond onlyria
/** Fait monter un article dans l'ordre de classement de son niveau hiérarchique.
 *
 *	@param int $id Identifiant du produit à déplacer.
 *	@param int $website Identifiant du site
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function cgv_articles_move_up( $id,  $website=false ){
	if($website && !is_numeric($website)) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	$art = cgv_articles_get($id, null, null, $wst);
	if( !ria_mysql_num_rows($art) ) return false;

	$art = ria_mysql_fetch_array($art);

	$ver = ria_mysql_fetch_array(cgv_versions_get($art['ver_id'], $wst));
	if( $ver['publish-date'] )
		return false;

	if( $art['pos']>=1 ){
		ria_mysql_query('update cgv_articles set art_pos=art_pos+1 where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_pos='.($art['pos']-1).' and art_ver_id='.$ver['id']);
		ria_mysql_query('update cgv_articles set art_pos='.($art['pos']-1).' where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_id='.$id.' and art_ver_id='.$ver['id']);
	}

	cgv_versions_set_date_modified( $ver['id'], $wst );

	return true;
}
// \endcond

// \cond onlyria
/** Fait descendre un article dans l'ordre de classement de son niveau hiérarchique.
 *
 *	@param int $id Obligatoire, identifiant du produit à déplacer.
 *	@param int $website Facultatif, identifiant de site
 *
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	@todo Cette fonction ne gère pas encore les niveau hiérarchiques.
 */
function cgv_articles_move_down( $id, $website=false ){
	if($website && !is_numeric($website)) return false;
	global $config;

	$website ? $wst = $website : $wst = $config['wst_id'];

	$art = cgv_articles_get($id, null, null, $website);
	if( !ria_mysql_num_rows($art) ){ return false; }

	$art = ria_mysql_fetch_array($art);
	$ver = ria_mysql_fetch_array(cgv_versions_get($art['ver_id'], $wst));
	if( $ver['publish-date'] ){
		return false;
	}
	if( $art['pos']<cgv_articles_get_maxpos($ver['id'], null, $wst) ){
		ria_mysql_query('update cgv_articles set art_pos=art_pos-1 where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_pos='.($art['pos']+1).' and art_ver_id='.$ver['id']);
		ria_mysql_query('update cgv_articles set art_pos='.($art['pos']+1).' where art_tnt_id='.$config['tnt_id'].' and art_wst_id='.$wst.' and art_id='.$id.' and art_ver_id='.$ver['id']);
	}

	cgv_versions_set_date_modified( $ver['id'], $wst );

	return true;
}
// \endcond

// \cond onlyria
/**	Déplace l'article cgv avant ou après un autre article
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 articles appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $ver Version cgv
 *	@param int $source Identifiant de l'article source
 *	@param int $target Identifiant de l'article cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function cgv_articles_position_update( $ver, $source, $target, $where ) {
	return obj_position_update( DD_CGV_ARTICLE, array('ver' => $ver, 'article' => $source), array('ver' => $ver, 'article' => $target), $where );
}
// \endcond

/// @}
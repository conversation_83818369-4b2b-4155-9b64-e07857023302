<?php
	require_once('env.inc.php');
	// Contrôle que les variables d'environnement obligatoire sont bien définies
	// Dans le cas contraire, une erreur 503 sera levée
	
	if(
            getenv('ENVRIA_TNT_ID')      === false ||
            getenv('ENVRIA_BDD_SERVER')  === false ||
            getenv('ENVRIA_BDD_LOGIN')   === false ||
            getenv('ENVRIA_BDD_PWD')     === false ||
            getenv('ENVRIA_BDD_NAME')    === false ||
            getenv('ENVRIA_PACKAGE')     === false
		)
	{
	    print("Les variables d'environnement ne sont pas set");
		header('HTTP/1.1 503 Service Unavailable', true, 503);
		exit;
	}

	use EventService\Dispatcher;
        use EventService\EventProvider;

	/** \mainpage Introduction
	 *
	 *      Cette documentation décrit les différentes fonctions nécessaire à une intégration RiaShop.
	 *       Vous y retrouvez des exemples.
	 */

	/**     \file
	 *      Connecte l'application à ses sources de données : mysql et memcached
	 *       En cas d'erreur de connexion à MySQL, une erreur HTTP 503 "Service indisponible"
	 *       est renvoyée pour préserver le référencement naturel de nos clients.
	 */

	require_once('define.inc.php');
	require_once('debug.inc.php');
	require_once('ria.mysql.inc.php');
	require_once(__DIR__.'/../vendor/autoload.php');

	require_once('EventService/EventProvider.inc.php');
	require_once('PriceWatching/models/LinearRaised/autoload.php');
	require_once('Login/autoload.php');
	require_once('RiaShopException.class.php');
	require_once('RiaShopMemcached.class.php');

	$eventService = new EventProvider(
                new Dispatcher
        );

	{ // Gestion du cache
		$memcached = new RiaShopMemcached;
		$memcached->addServer('localhost', 11211);
		$memcached->setOption(Memcached::OPT_NO_BLOCK, true);
	}

	// La configuration de la connexion, peut ne pas être initialisée si l'appel est fait via un cron
	$ria_db_connect = array();
	// if(isset($worker_mariadb))
	// {
	//     $ria_db_connect = array(
    //         _DB_RIASHOP => array(
    //             'server'            => $worker_mariadb['host'],
    //             'user'              => $worker_mariadb['username'],
    //             'password'          => $worker_mariadb['password'],
    //             'base'              => $worker_mariadb['database'],
    //             'link_identifier'   => false
    //         ),
    //     );
	// }
	// else
	// {
        $ria_db_connect = array(
            _DB_RIASHOP => array(
                'server'            => getenv('ENVRIA_BDD_SERVER'),
                'user'              => getenv('ENVRIA_BDD_LOGIN'),
                'password'          => getenv('ENVRIA_BDD_PWD'),
                'base'              => getenv('ENVRIA_BDD_NAME'),
                'link_identifier'   => false
            ),
        );
    // }

	// Pour les comptes administrateur ou représentant, le cache peut-être désactivé depuis
	// App > Option > Désactiver le cache, cette désactivation dure 15 minutes maximum
	if( isset($_SESSION['usr_id'], $_SESSION['usr_prf_id'])
		&& in_array($_SESSION['usr_prf_id'], [PRF_ADMIN, PRF_SELLER])
		&& $_SESSION['usr_id']
	){
		require_once('users.inc.php');
		if( gu_users_get_cache_is_disabled( $_SESSION['usr_id'] ) ){
			$memcached = new RiaShopMemcached;
		}
	}

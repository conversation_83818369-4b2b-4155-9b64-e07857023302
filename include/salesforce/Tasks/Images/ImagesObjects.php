<?php
namespace Riashop\Salesforce\Tasks\Images;

use RuntimeException;
use Exception;
use stdClass;
use Riashop\Salesforce\Task;
use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;
/**
 * \ingroup salesforce_tasks
 * @class ImagesObjects Classe gestion association image avec des objects
 * @{
 */
class ImagesObjects extends Task
{
	/**
	 * @copydoc Task::saveRow
	 */
	public function saveRow(array $record)
	{}
	/**
	 * @copydoc Task::add
	 */
	public function add($param)
	{
		global $config;

		$attachements = array();
		$filesource = img_images_get_filesource($param['img_id']);

		$img = ria_mysql_fetch_assoc( img_images_get( $param['img_id'] ) );
		if( !$filesource ){
			throw new Exception("Erreur impossible de récupérer l'image");
		}
		$filesource = $config['img_dir'].'/source/'. $filesource;

		if( !file_exists($filesource) ){
			throw new Exception("Erreur pas de fichier source");
		}

		$content = file_get_contents($filesource);
		$r_names = img_image_names_get($param['img_id']);
		$name = $filesource;
		if( $r_names && ria_mysql_num_rows($r_names) ){
			$info = ria_mysql_fetch_assoc($r_names);
			$name = $info['name'];
		}
		$name = $name. '.'.($img['type']?$img['type']:'jpg');
		$base64 = base64_encode($content);
		$sf_id = $this->getRefGescom($param['cls_id'], $param['obj_id_0']);

		if( !$sf_id ){
			throw new Exception("Erreur l'objet n'a pas d'identifiant salesforce ");
		}

		$Attachment = new stdClass;
		$Attachment->Body = $base64;
		$Attachment->Name = $name;
		$Attachment->ParentId = $sf_id;
		$attachements[] = $Attachment;

		if( !empty($attachements) ){
			$imgResponses = $this->client->create($attachements, 'Attachment');
			foreach ($imgResponses as $imgResponse) {
				if( isset($imgResponse->errors) && sizeof($imgResponse->errors) ){
					throw new Exception("Erreur de création de l'image dans SalesForce ".print_r($imgResponses, true));
				}
			}
		}

		return true;
	}
	/**
	 * Récupère la ref gescom en fonction de la classe
	 *
	 * @param integer $cls_id identifiant de la classe
	 * @param integer $id Identifiant de l'objet
	 * @return string|boolean Retourne la ref gescom, sinon false
	 */
	private function getRefGescom($cls_id, $id)
	{
		switch ($cls_id) {
			case CLS_LINEAR_RAISED:
				return prw_linear_raised::getRefGescom($id);
			case CLS_LINEAR_OFFERS:
				return $id;
			default:
				throw new RuntimeException("TSK_IMAGES_OBJECTS_ADD : classe $cls_id non implémenté");
		}
	}
}

/// @}
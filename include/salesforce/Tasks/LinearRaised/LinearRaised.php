<?php
namespace Riashop\Salesforce\Tasks\LinearRaised;

use stdClass;
use Exception;
use Riashop\Salesforce\Task;
use Riashop\Salesforce\Tasks\Images\ImagesObjects;
use Riashop\PriceWatching\models\LinearRaised\prw_offers;
use Riashop\PriceWatching\models\LinearRaised\prw_linear_raised;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
/**
 * \ingroup salesforce_tasks
 * \class LinearRaised Gestion du traitement des relevé linéaire
 * @{
 */
class LinearRaised extends Task
{
	const FLD_OFFER_IMG = 101439;
	/**
	 * $lines_fields Liste des champs avancé mappé avec les attributs
	 *
	 * @var array
	 */
	private $lines_fields = array(
		101376 => 'Commentaire__c',
		101375 => 'Hauteur__c',
		101374 => 'Largeur__c',
		101373 => 'Ref_Emblematique_Quant_annuelle__c',
	);
	/**
	 * @copydoc Task::saveRow
	 */
	public function saveRow(array $record){}
	/**
	 * @copydoc Task::add
	 */
	public function add($param){
		$id = $param;
		if( !is_numeric($id) || $id <= 0 ) {
			throw new Exception("sf_task_linear_raised id doit être un entier positif");
		}

		$r_linear_raised = prw_linear_raised::get($id);
		if( !ria_mysql_num_rows($r_linear_raised) ){
			throw new Exception("sf_task_linear_raised aucun relevé");
		}

		$linear_raised = ria_mysql_fetch_assoc($r_linear_raised);

		$r_user = gu_users_get($linear_raised['usr_id']);

		if( !ria_mysql_num_rows($r_user) ){
			throw new Exception("sf_task_linear_raised pas de compte associer");
		}

		$user = ria_mysql_fetch_assoc($r_user);

		$r_offers = prw_offers::get($id);

		if( !ria_mysql_num_rows($r_offers) ){
			throw new Exception("sf_task_linear_raised pas d'offre de relevé");
		}

		$seller_ref = false;
		if( is_numeric($linear_raised['author_id']) ){
			$seller_ref = gu_users_get_ref($linear_raised['author_id']);
		}

		$r_list = prw_followed_lists::get($linear_raised['pfl_id']);
		$collection = '';
		if( ria_mysql_num_rows($r_list) ){
			$list = ria_mysql_fetch_assoc($r_list);
			$collection = $list['name'];
		}
		if( empty($linear_raised['ref_gescom']) ){
			$Releves_Lineaires__c = new stdClass;
			$Releves_Lineaires__c->Name = $collection. '-'.$linear_raised['date_created'];
			$Releves_Lineaires__c->Compte_Distributeur__c = $user['ref_gescom'];
			$Releves_Lineaires__c->Affiliation_un_Groupe__c = $user['society'];
			$Releves_Lineaires__c->FR_Id_Releve_yuto__c = $id;
			$Releves_Lineaires__c->FR_Utilisateur__c = $seller_ref;
			$Releves_Lineaires__c->Instance_Yuto__c = sf_get_instance_name();
			if( $collection ){
				$Releves_Lineaires__c->Collection__c = $collection;
			}

			$linearResponses = $this->client->create(array($Releves_Lineaires__c), 'Releves_Lineaires__c');
			foreach ($linearResponses as $linearResponse) {
				if( isset($linearResponse->errors) && sizeof($linearResponse->errors) ){
					throw new Exception("Erreur de création du relevé '".$id."' dans SalesForce ".print_r($linearResponses, true));
				}

				$sf_linear_id = $linearResponse->id;
				break;
			}
		}else{
			$sf_linear_id = $linear_raised['ref_gescom'];
		}
		$images = [];
		$offer_index = 0;
		prw_linear_raised::setRefGescom($id, $sf_linear_id);
		$Lineaire_releve = array();
		while( $offer = ria_mysql_fetch_assoc($r_offers) ){
			$prd_name = prd_products_get_name($offer['prd_id']);
			$Lineaire_releve__c = new stdClass;
			$Lineaire_releve__c->Name = $prd_name;
			$Lineaire_releve__c->FR_Releve_lineaire__c = $sf_linear_id;
			$ref = prd_products_get_refgescom($offer['prd_id']);
			if( $ref ){
				$Lineaire_releve__c->Lineaire__c = $ref;
			}
			$Lineaire_releve__c->Nbre_de_ref_presente__c = $offer['facings'];
			$Lineaire_releve__c->Nbre_etagere__c = $offer['level'];
			$Lineaire_releve__c->Nbre_d_elements__c = $offer['count'];

			foreach ($this->lines_fields as $fld_id => $atribute) {
				$attr_value = fld_object_values_get($offer['id'], $fld_id);
				if( $attr_value ){
					$Lineaire_releve__c->$atribute = $attr_value;
				}
			}

			$img = fld_object_values_get($offer['id'], self::FLD_OFFER_IMG);
			if( $img ){
				$images[$offer_index] = ['img_id' => $img];
			}
			$Lineaire_releve[] = $Lineaire_releve__c;

			$offer_index++;
		}

		$linearReleveResponses = $this->client->create($Lineaire_releve, 'Lineaire_releve__c');
		foreach ($linearReleveResponses as $key => $linearResponse) {
			if( isset($linearResponse->errors) && sizeof($linearResponse->errors) ){
				throw new Exception("Erreur de création du relevé '".$id."' dans SalesForce ".print_r($linearReleveResponses, true));
			}
			if( array_key_exists($key, $images) ){
				$images[$key]['cls_id'] = CLS_LINEAR_OFFERS;
				$images[$key]['obj_id_0'] = $linearResponse->id;
			}
		}

		foreach($images as $params ){
			$TaskImagesObjects = new ImagesObjects($this->client);
			$TaskImagesObjects->add($params);
		}
	}
}
/// @}
<?php
namespace Riashop\Salesforce\Tasks\LinearRaised;

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\Salesforce\Task;
/**
 * \ingroup salesforce_tasks
 * @class CollectionProduct Classe gestion des assortiments
 * @{
 */
class Collection extends Task
{
	/**
	 * @copydoc Task::saveRow
	 */
	public function saveRow(array $record)
	{
		$followed_lists_id = null;
		$r_list = prw_followed_lists::getByRefGescom($record['Id']);

		if( !ria_mysql_num_rows( $r_list ) ){
			$followed_lists_id = prw_followed_lists::add($record['Name'], prw_followed_lists::TYPE_YUTO, $record['Collection_Active__c'], null, $record['Id']);
			$r_list = prw_followed_lists::get($followed_lists_id);
		}

		$followed_lists = ria_mysql_fetch_assoc($r_list);
		$followed_lists_id = $followed_lists['id'];

		if( $record['IsDeleted'] ){
			prw_followed_lists::delete($followed_lists_id);
			prw_followed_products::delete($followed_lists_id);
			return true;
		}

		if( $record['Collection_Active__c'] != $followed_lists['is_published'] ){
			prw_followed_lists::publish($followed_lists_id, $record['Collection_Active__c']);
		}

		if( $record['Name'] != $followed_lists['name'] ){
			prw_followed_lists::update($followed_lists_id, $record['Name']);
		}

		return true;
	}
	/**
	 * @copydoc Task::add
	 */
	public function add($param){
	}
}

/// @}
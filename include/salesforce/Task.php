<?php
namespace Riashop\Salesforce;
/** 
 * \ingroup salesforce
 * \defgroup salesforce_tasks Tache Salesforce
 * \class Task Classe abstraite pour le déroulement des taches de salesforce
 * @{
 */
Abstract class Task
{
	protected $client = null; ///< SforceEnterpriseClient Client salesforce
	/**
	 * Constructeur
	 *
	 * @param \SforceEnterpriseClient $SforceEnterpriseClient Client salesforce
	 */
	public function __construct(\SforceEnterpriseClient $SforceEnterpriseClient=null)
	{
		$this->client = $SforceEnterpriseClient;
	}
	/**
	 * Fonction de sauvegarde d'un objet salesforce
	 *
	 * @param array $record Objet salesforce
	 * @return mixed Retourne un boolean, ou un identifiant
	 */
	public abstract function saveRow(array $record);
	/**
	 * Fonction d'ajout ou mise à jour d'un objet riashop dans salesforce
	 *
	 * @param mixed $param Identifiant riashop ou autre paramètre
	 * @return mixed|void retourne rien généralement
	 */
	public abstract function add($param);
}
/// @}
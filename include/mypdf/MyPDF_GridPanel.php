<?php
	// MyPDF_GridPanel
	/*
	*/
	
	require_once('mypdf/MyPDF_CellPanel.php');
	require_once('mypdf/MyPDF_Element.php');
	
	class MyPDF_GridPanel extends MyPDF_Element {
	
		// attributs
		
			private	$_cells;
			private	$_cols;
			private	$_rows;
		
		// Méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param) {
				parent::__construct($param);
				
				$this->_cells = array();
				$this->_rows = 0;
				$this->_cols = 0;
				
				if (array_key_exists('rows', $param)) $this->setRows(array('rows' => $param['rows']));
				if (array_key_exists('cols', $param)) $this->setCols(array('cols' => $param['cols']));
			}
			
			// addCol
			/* Ajoute une colonne */
			public function addCol() {
				$cells =& $this->_cells;
				$rows = $this->getRows();
				$cols = $this->getCols();
				
				for ($j=0; $j<$rows; $j++) $cells[$j][] = new MyPDF_CellPanel(array('parent' => $this, 'row' => $j, 'col' => $cols));
				
				$this->_cols++;
				return $this;
			}
			
			// addRow
			/* Ajoute une ligne */
			public function addRow() {
				$cells =& $this->_cells;
				$cols = $this->getCols();
				$rows = $this->getRows();
				
				$row = array();
				for ($i=0; $i<$cols; $i++) $row[$i] = new MyPDF_CellPanel(array('parent' => $this, 'row' => $rows, 'col' => $i));
				$cells[] = $row;
				
				$this->_rows++;
				return $this;
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = $param['pdf'];
				
				$rows = $this->getRows();
				$cols = $this->getCols();
				
				$this->calcCells();
				
				// build chaque cellule
				for ($j=0; $j<$rows; $j++) {
					for ($i=0; $i<$cols; $i++) $this->getCell(array('row' => $j, 'col' => $i))->build(array('pdf' => $pdf));
				}
				
				return $this;
			}
			
			// calcCells
			/* Calcule les cellules */
			public function calcCells() {
				$rows = $this->getRows();
				$cols = $this->getCols();
				$l = $this->getLeft();
				for ($i=0; $i<$cols; $i++) {
					$w = 0;
					for ($j=0; $j<$rows; $j++) $w = max($w, $this->getCell(array('row' => $j, 'col' => $i))->getMinWidth());
					for ($j=0; $j<$rows; $j++) {
						$cell = $this->getCell(array('row' => $j, 'col' => $i));
						$cell->setLeft(array('left' => $l));
						$cell->setWidth(array('width' => $w));
					}
					$l += $w;
				}
				$t = $this->getTop();
				for ($j=0; $j<$rows; $j++) {
					$h = 0;
					for ($i=0; $i<$cols; $i++) $h = max($h, $this->getCell(array('row' => $j, 'col' => $i))->getMinHeight());
					for ($i=0; $i<$cols; $i++) {
						$cell = $this->getCell(array('row' => $j, 'col' => $i));
						$cell->setTop(array('top' => $t));
						$cell->setHeight(array('height' => $h));
					}
					$t += $h;
				}
				
				return $this;
			}
			
			// getCell
			/* Renvoie une cellule */
			public function getCell($param) {
				return $this->_cells[$param['row']][$param['col']];
			}
			
			// getCols
			/* Renvoie le nombre de colonnes */
			public function getCols() {
				return $this->_cols;
			}
			
			// getColWidth
			/* Renvoie la largeur d'une colonne */
			public function getColWidth($param) {
				$col = $param['col'];
				$cells = $this->_cells;
				$rows = $this->getRows();
				$w = 0;
				for ($j=0; $j<$rows; $j++) $w = max($w, $cells[$j][$col]->getMinWidth());
				return $w;
			}
			
			// getHeight
			/* Renvoie height */
			public function getHeight() {
				$cols = $this->getCols();
				$h = 0;
				if ($cols > 0) {
					$rows = $this->getRows();
					for ($j=0; $j<$rows; $j++) $h += $this->getCell(array('row' => $j, 'col' => 0))->getHeight();
				}
				return $h;
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				return $this->getParent()->getLeft();
			}
			
			// getRowHeight
			/* Renvoie la hauteur d'une ligne */
			public function getRowHeight($param) {
				$row = $param['row'];
				$cells = $this->_cells;
				$cols = $this->getCols();
				$h = 0;
				for ($i=0; $i<$cols; $i++) $h = max($h, $cells[$row][$i]->getMinHeight());
				return $h;
			}
			
			// getRows
			/* Renvoie le nombre de lignes */
			public function getRows() {
				return $this->_rows;
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				return $this->getParent()->getTop();
			}
			
			// getWidth
			/* Renvoie width */
			public function getWidth() {
				$rows = $this->getRows();
				$w = 0;
				if ($rows > 0) {
					$cols = $this->getCols();
					for ($i=0; $i<$cols; $i++) $w += $this->getCell(array('row' => 0, 'col' => $i))->getWidth();
				}
				return $w;
			}
			
			// setCols
			/* Affecte le nombre de colonnes */
			public function setCols($param) {
				$cols = $param['cols'];
				$last = $this->getCols();
				if ($cols != $last) {
					if ($cols > $last) for ($i=$last; $i<$cols; $i++) $this->addCol();
					else for ($i=$last; $i>$cols; $i--) $this->removeCol();
				}
				return $this;
			}
			
			// setRows
			/* Renvoie le nombre de lignes */
			public function setRows($param) {
				$rows = $param['rows'];
				$last = $this->getRows();
				if ($rows != $last) {
					if ($rows > $last) for ($i=$last; $i<$rows; $i++) $this->addRow();
					else for ($i=$last; $i>$rows; $i--) $this->removeRow();
				}
				return $this;
			}
		
	}


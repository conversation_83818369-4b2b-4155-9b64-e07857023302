<?php
	// MyPDF_CellPanel
	/*
	*/
	
	require_once('mypdf/MyPDF_Border.php');
	require_once('mypdf/MyPDF_Element.php');
	
	class MyPDF_CellPanel extends MyPDF_Element {
	
		// attributs
		
			private	$_border;
			private	$_col;
			private	$_height;
			private	$_left;
			private	$_row;
			private	$_top;
			private	$_widget;
			private	$_width;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				parent::__construct($param);
				
				if (array_key_exists('border', $param)) $this->setBorder(array('border' => $param['border']));
				$this->setCol(array('col' => $param['col']));
				$this->setRow(array('row' => $param['row']));
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = $param['pdf'];
				
				// dessine la cellule
				$pdf->setXY($this->getLeft(), $this->getTop());
				
				$border = $this->getBorder();
				
				$hasBorder = ($border) ? 1 : 0;
				if ($hasBorder) {
					$color = $border->getColor();
					$pdf->SetDrawColor($color->getRed(), $color->getGreen(), $color->getBlue());
				}
				$pdf->Cell($this->getWidth(), $this->getHeight(), '', $hasBorder, 0, 'L');
				
				// dessine l'élément
				$widget = $this->getWidget();
				if ($widget) $widget->build(array('pdf' => $pdf));
				
				return $this;
			}
			
			// getBorder
			/* Renvoie la bordure */
			public function getBorder() {
				return $this->_border;
			}
			
			// getCol
			/* Renvoie la colonne */
			public function getCol() {
				return $this->_col;
			}
			
			// getHeight
			/* Renvoie la hauteur */
			public function getHeight() {
				if ($this->_height === null) return $this->getParent()->getRowHeight(array('row' => $this->getRow()));
				return $this->_height;
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				if ($this->_left === null) throw new exception('left est null !');
				return $this->_left;
			}
			
			// getMinHeight
			/* Renvoie la hauteur minimum */
			public function getMinHeight() {
				$widget = $this->getWidget();
				return ($widget) ? $widget->getHeight() : 0;
			}
			
			// getMinWidth
			/* Renvoie la largeur minimum (irréductible) */
			public function getMinWidth() {
				$widget = $this->getWidget();
				return ($widget) ? $widget->getWidth() : 0;
			}
			
			// getRow
			/* Renvoie la ligne */
			public function getRow() {
				return $this->_row;
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				if ($this->_top === null) throw new exception('top est null !');
				return $this->_top;
			}
			
			// getWidget
			/* Renvoie le widget */
			public function getWidget() {
				return $this->_widget;
			}
			
			// getWidth
			/* Renvoie la largeur */
			public function getWidth() {
				if ($this->_width === null) return $this->getParent()->getColWidth(array('col' => $this->getCol()));
				return $this->_width;
			}
			
			// setBorder
			/* Affecte la bordure */
			public function setBorder($param = array()) {
				$border = (array_key_exists('border', $param)) ? $param['border'] : true;
				
				if ($border === true) $border = new MyPDF_Border();
				elseif ($border === false) $border = null;
				
				$this->_border = $border;
				return $this;
			}
			
			// setCol
			/* Affecte la colonne */
			public function setCol($param) {
				$this->_col = $param['col'];
				return $this;
			}
			
			// setHeight
			/* Affecte la hauteur */
			public function setHeight($param) {
				$this->_height = $param['height'];
				return $this;
			}
			
			// setLeft
			/* Affecte left */
			public function setLeft($param) {
				$this->_left = $param['left'];
				return $this;
			}
			
			// setRow
			/* Affecte la ligne */
			public function setRow($param) {
				$this->_row = $param['row'];
				return $this;
			}
			
			// setTop
			/* Affecte top */
			public function setTop($param) {
				$this->_top = $param['top'];
				return $this;
			}
			
			// setWidget
			/* Affecte le widget */
			public function setWidget($param) {
				$widget = $param['widget'];
				$this->_widget = $widget;
				$widget->setParent(array('parent' => $this));
				return $this;
			}
			
			// setWidth
			/* Affecte la largeur */
			public function setWidth($param) {
				$this->_width = $param['width'];
				
				$widget = $this->getWidget();
				if ($widget) $widget->setWidth($param);
				
				return $this;
			}
		
	}


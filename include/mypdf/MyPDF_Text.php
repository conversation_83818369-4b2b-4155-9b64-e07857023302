<?php
	// MyPDF_Text
	/*
	*/
	
	require_once('mypdf/MyPDF_Element.php');
	
	class MyPDF_Text extends MyPDF_Element {
	
		// attributs
		
			private	$_align;
			private	$_content;
			private	$_height;
			private	$_width;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				parent::__construct($param);
				$align = (array_key_exists('align', $param)) ? $param['align'] : 'L';
				$content = (array_key_exists('content', $param)) ? $param['content'] : '';
				
				$this->setAlign(array('align' => $align));
				if (array_key_exists('width', $param)) $this->setWidth(array('width' => $param['width']));
				$this->setContent(array('content' => $content));
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = $param['pdf'];
				
				parent::build($param);
				
				$pdf->setXY($this->getLeft(), $this->getTop() + 0.1);
				$pdf->MultiCell($this->getWidth(), MyPDF::getLineHeight(), $this->getContent(), 0, $this->getAlign());
				return $this;
			}
			
			// getAlign
			/* Renvoie align */
			public function getAlign() {
				return $this->_align;
			}
			
			// getContent
			/* Renvoie le contenu */
			public function getContent() {
				return $this->_content;
			}
			
			// getHeight
			/* Renvoie la hauteur */
			public function getHeight() {
				$h = 0;
				$rows = explode("\n", $this->getContent());
				foreach ($rows as $row) $h += $this->getTextHeight(array('content' => $row));
				return $h;
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				return $this->getParent()->getLeft();
			}
			
			// getTextHeight
			/* Renvoie la hauteur d'un texte sans retour à la ligne */
			public function getTextHeight($param) {
				$content = $param['content'];
				$maxWidth = $this->getWidth();
				$width = MyPDF::getTextWidth(array('str' => $content, 'font' => $this->getFont(), 'fontSize' => $this->getFontSize(), 'bold' => $this->isBold(), 'italic' => $this->isItalic(), 'underline' => $this->hasUnderline()));
				
				$rows = ($maxWidth > 0) ? ceil($width / $maxWidth) : 0;
				
				if ($rows > 1) {
					$rows2 = 1;
					$first = true;
					$t = explode(' ', $content);
					$wspace = 1;
					$w = 0;
					foreach ($t as $word) {
						$size = MyPDF::getTextWidth(array('str' => $word, 'font' => $this->getFont(), 'fontSize' => $this->getFontSize(), 'bold' => $this->isBold(), 'italic' => $this->isItalic(), 'underline' => $this->hasUnderline()));
						$wt = (($first) ? 0 : $wspace) + $size;
						
						if ($w + $wt > $maxWidth) {
							$w = 0;
							$rows2++;
							$first = true;
							
							$tr = $size / $maxWidth;
							$l = floor($tr);
							
							$rows2 += $l;
							$first = false;
							$w = $size - $maxWidth * $l;
						}
						else {
							$first = false;
							$w += $wt;
						}
					}
					
					$rows = max($rows, $rows2);
				}
				
				return $rows * MyPDF::getLineHeight();
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				return $this->getParent()->getTop();
			}
			
			// getWidth
			/* Renvoie la largeur */
			public function getWidth() {
				if ($this->_width === null) $this->_width = MyPDF::getTextWidth(array('str' => $this->getContent(), 'font' => $this->getFont(), 'fontSize' => $this->getFontSize(), 'bold' => $this->isBold(), 'italic' => $this->isItalic(), 'underline' => $this->hasUnderline()));
				return $this->_width;
			}
			
			// setAlign
			/* Affecte l'alignement */
			public function setAlign($param) {
				$this->_align = $param['align'];
				return $this;
			}
			
			// setContent
			/* Affecte le contenu */
			public function setContent($param) {
				$utf8 = $param['content'];
				
				// conversion iso
				$iso = $utf8;
				$iso = utf8_decode($iso);
				$iso = str_replace(chr(63), chr(128), $iso);	// €
				
				$this->_content = $iso;
				return $this;
			}
			
			// setWidth
			/* Affecte la largeur */
			public function setWidth($param) {
				$this->_width = $param['width'];
				return $this;
			}
		
	}


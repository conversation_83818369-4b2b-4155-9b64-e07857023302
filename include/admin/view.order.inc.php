<?php
/**
 * Affichage HTML de la section Acompte/ Echéance
 * @param array		$order Obligatoire, tableau des valeurs de la commande
 * @param bool      $expired_or_missing Obligatoire, true si l'acompte est expiré ou s'il y en a pas
 * @param bool      $is_deadline True s'il s'agit d'une échéance plutôt qu'un acompte
 * @param bool      $error True s'il y a des erreurs
 * @return string HTML de la partie gestion acompte/ échéance
 */
function view_admin_deposit_form( $order, $expired_or_missing, $is_deadline = false, $error = false ){

    if( !gu_user_is_authorized('_RGH_ADMIN_ORDER_ADD_DEPOSIT') || !$expired_or_missing || !is_numeric($order['state_id']) || $order['state_id'] == _STATE_INVOICE ){
        return '';
    }
    $error = (is_bool($error) && $error);

    if( isset($_POST['add-deposit']) || $error ){
        $date = $error && isset($_POST['new-deposit-date']) ? $_POST['new-deposit-date'] : '';
        $amount = $error && isset($_POST['new-deposit-amount']) ? $_POST['new-deposit-amount'] : '';
        return '
            <table>
                <thead>
                    <tr>
                        <th>'._('Date d\'expiration').'</th>
                        <th>'._('Montant').'</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <input type="date" name="new-deposit-date" value="'.$date.'" />
                        </td>
                        <td>
                            <input type="text" name="new-deposit-amount" value="'.$amount.'" min="1" max="'.$order['total_ttc'].'" />
                        </td>
                        <td>
                            '.($is_deadline ? '<input type="hidden" name="new-deposit-type" value="deadline" />' : '').'
                            <button type="submit" name="new-deposit" value="'.$order['id'].'">'._('Enregistrer').'</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        ';

    }
    return '<button type="submit" name="add-deposit">'._('Ajouter un acompte').'</button>';

}
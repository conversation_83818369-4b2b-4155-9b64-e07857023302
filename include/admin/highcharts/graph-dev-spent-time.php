<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	$is_yuto_essentiel = tnt_tenants_is_yuto_essentiel();
	$spent_time = array();

	// On affiche pas le graphique de chiffre d'affaire pour les Riashop Yuto Essentiel
	if( !$is_yuto_essentiel ){

		print '
			<div id="graph-spent-time"></div>
		';


		$ord_seller_id = 0;
		if (isset($_SESSION["ord_seller_id"]) && $_SESSION["ord_seller_id"] > 0) {
			$ord_seller_id = $_SESSION["ord_seller_id"];
		}

		$date1 = isdate($date1) ? $date1.' 00:00:00' : $date1;
		$date2 = isdate($date2) ? $date2.' 23:59:59' : $date2;

		// Récupération des statistiques
		$filtre1 = array("usr_prf_id" => -PRF_ADMIN);
		$dev_spent_time = get_report_spent_time( $date1, $date2, $ord_seller_id );

		if ($dev_spent_time) {
			while ($r = ria_mysql_fetch_array($dev_spent_time)) {
			
				$total_amount = 0; 
				$rorder = stats_orders_ca("completed", $date1, $date2, '', false, false, 0, array(), true, 0, true, null, $r["usr_id"]);
				
				if ($rorder && ria_mysql_num_rows($rorder)) {
					$order = ria_mysql_fetch_array($rorder);
					$total_amount = $order["completed"]; 
				}

				$author_list = "";
				foreach (explode(',', $r['author_ids']) as $auth_id) {
					if (gu_users_exists($auth_id)) {
						$author_list .= '<p>'.gu_users_get_name($auth_id).'</p><br>';
					}
				}

				$rendement = 0;
				if ($total_amount > 0 && $r["spent_time"]>0) {
					$rendement = $total_amount / $r["spent_time"];
					$rendement *= 3600;
				}

				$spent_time[] = array(
					$r["spent_time"] / 3600 , // Temps total de visite en heures
					round($total_amount, 2), // Montant de chiffres d'affaires
					$r["usr_id"], // Nom du client
					gu_users_get_name($r["usr_id"]), // Nom du client
					$author_list, // Liste du/des commerciaux ( Autheurs des rapports )
					$rendement, // rendement à l'heure
					);
			}
		}
	?>
	<script>
		$(function () {
			$('#graph-spent-time').highcharts({
				chart: {
					type: "scatter",
					plotBorderWidth: 0,
					animation: false,
					events: {
						load: function (event) {
							var extremes = this.yAxis[0].getExtremes();
							if (extremes.dataMax == 0) {
								this.yAxis[0].setExtremes(0, 5);
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'statistiques-temps-de-visite'
				},
				title: {
					text: '',
					x: -20
				},
				tooltip: {
					// useHTML: true,
					formatter: function () {
						seconds = this.x * 3600
						hours = Math.floor(seconds / 3600);
						seconds %= 3600;
						minutes = Math.floor(seconds / 60);
						seconds = seconds > 60 ? seconds % 60 : seconds;
						seconds = Math.ceil(seconds);
						var str = '';
						if (hours > 0) {
							str += hours + "h ";
						}
						if (minutes > 0) {
							str += minutes + "min ";
						}
						return 	'<b>'+"<?php print _('Client')?>"+' :</b> '+this.point.name+'<br>'+
								'<b>'+"<?php print _('Temps')?>"+' :</b> '+str+'<br>'+
								'<b>'+"<?php print _('Chiffre d\'affaires TTC')?>"+':</b>'+Highcharts.numberFormat(this.y, 2, ',', ' ') +' €<br>'+
								'<b>'+"<?php print _('Rendement')?>"+' :</b>'+Highcharts.numberFormat(this.point.rendement, 2, ',', ' ') +' € / h<br>'+
								'<b>'+"<?php print _('Auteur(s)')?>"+' :</b><br>'+this.point.author;
					}
				},
				legend: {
					enabled: false,
				},
				yAxis: {
					labels: {
						formatter : function(){
							return Highcharts.numberFormat(this.value, 0, ',', ' ') + ' €'
						},
					},	        	
					title: {
						text:"<?php print _('Chiffre d\'affaires TTC')?>"
					}
				},
				xAxis: {
					labels: {
						formatter: function(){
							return Math.round(this.value * 100) / 100 + ' h';
						},
					},
					title: {
						text: "<?php print _('Temps de visite')?>"
					}
				},
				series: [{
						name: "<?php print _('Temps de visite')?>",
						
						data: [<?php foreach ($spent_time as $value) {
								print '{
									x : '.$value[0].',
									y : '.$value[1].',
									name_id : "'.htmlspecialchars( $value[2] ).'",
									name : "'.htmlspecialchars( $value[3] ).'",
									author : "'.htmlspecialchars( $value[4] ).'",
									rendement : "'.htmlspecialchars( $value[5] ).'",

								},';
							} ?>
							],
						color: '#ec6726'
					},
				],
				dataLabels: {
					enabled: false
				},
			});
		});
		</script>
	<?php } ?>

	<?php if( $spent_time && count($spent_time)>0 ){ ?>

	
	<table id="tb-moyenne-rdv" class="checklist tablesorter">
		<caption><?php print _('Moyennes par rendez-vous')?></caption>
		<thead>
			<tr>
				<th id="avg_duration" title="<?php print _('Durée moyenne d\'un rendez-vous client'); ?>"><?php print _('Durée moyenne')?></th>
				<?php if( !$is_yuto_essentiel ){ ?>
					<th id="avg_revenue" title="<?php print _('Chiffre d\'affaires moyen par client visité'); ?>"><?php print _('Chiffre d\'affaires TTC moyen')?></th>
					<th id="avg_yield" title="<?php print _('Rendement moyen par rendez-vous client'); ?>"><?php print _('Rendement moyen')?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php
				$count_customers = $total_duration = $total_revenue = $avg_yield = 0;
				foreach( $spent_time as $sp ){
					$count_customers++;
					$total_duration += $sp[0];
					$total_revenue += $sp[1];
				}
				$avg_duration = $total_duration / $count_customers;
				$avg_revenue = $total_revenue / $count_customers;
				$avg_yield = $total_revenue / $total_duration;
			?>
			<tr>	
				<td headers="avg_duration"><?php print convert_second_to_readable_delay($avg_duration * 3600, false); ?></td>
				<?php if( !$is_yuto_essentiel ){ ?>
					<td headers="avg_revenue" class="numeric"><?php print number_format($avg_revenue, 2, ',', ' '); ?> €</td>
					<td headers="avg_yield" class="numeric"><?php print number_format($avg_yield, 2, ',', ' '); ?> € / h</td>
				<?php } ?>
			</tr>
		</tbody>
	</table>

	<table id="t_spent_time" class="checklist tablesorter">
		<caption><?php print _('Temps de visite')?></caption>
		<thead>
			<tr>
				<th class="th-col-show"><?php print _('Client'); ?></th>
				<th class="th-col-show"><?php print _('Temps de visite'); ?></th>
				<?php if( !$is_yuto_essentiel ){ ?>
					<th class="th-col-show"><?php print _('Chiffre d\'affaires TTC'); ?></th>
					<th class="th-col-show"><?php print _('Rendement'); ?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php foreach( $spent_time as $sp ){ ?>
				<tr>
					<!-- Nom du client -->
					<td data-value="<?php print htmlspecialchars( gu_users_get_name($sp[2]) ); ?>"><a href="/admin/customers/edit.php?usr=<?php print $sp[2]; ?>"><?php print htmlspecialchars( gu_users_get_name($sp[2]) ); ?></a></td>
					<!-- Durée du rapport de visite -->
					<td class="numeric" data-value="<?php print $sp[0] * 3600; ?>"><?php print convert_second_to_readable_delay($sp[0] * 3600, false); ?></td>
					<?php if( !$is_yuto_essentiel ){ ?>
						<!-- Chiffre d'affaires généré -->
						<td class="numeric" data-value="<?php print $sp[1]; ?>"><?php print number_format($sp[1], 2, ',', ' '); ?> €</td>
						<!-- Rendement € / h -->
						<td class="numeric" data-value="<?php print $sp[5]; ?>"><?php print number_format($sp[5], 2, ',', ' '); ?> € / h</td>
					<?php } ?>
				</tr>
			<?php } ?>
		</tbody>
	</table>

	<script>
	<!--
		$(document).ready(function(){ 
		        $("#t_spent_time").tablesorter({
		        	textExtraction: function(node) {
        	            return node.getAttribute('data-value');
        	        } 
		        }); 
		    } 
		);
	// -->
	</script>

	<?php } ?>
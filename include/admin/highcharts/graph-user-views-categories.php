<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-user-views-categories"></div>
	';

	// Récupération des statistiques
	$view_prd = stats_graphs_get_datas( 'user-category', false, false, array('user'=>$_GET['usr']) );
	
	$keys = array();
	foreach( array_keys($view_prd) as $k ){
		$keys[] = str_replace( '\'', '\\\'', $k );
	}
?>
<script>
$(function(){
	$('#graph-user-views-categories').highcharts({
		chart: {
			polar: true,
			type: 'area'
		},
		credits: {
			enabled: false
		},
		exporting: {
			filename: 'categories-les-plus-consultees'
		},
		title: {
			text: '',
			x: -80
		},
		pane: {
			size: '80%'
		},
		xAxis: {
			categories: [<?php print '\''.implode( '\',\'', $keys ).'\''; ?>],
			tickmarkPlacement: 'on',
			lineWidth: 0
		},
		yAxis: {
			gridLineInterpolation: 'polygon',
			lineWidth: 0,
			min: 0
		},
		tooltip: {
			shared: true,
			pointFormat: '<span style="color:{series.color}">Consultations: <b>{point.y:,.0f}</b><br/>'
		},
		legend: {
			enabled: false
		},
		series: [
			{
				data: [<?php print implode( ',', array_values($view_prd) ); ?>],
				pointPlacement: 'on'
			}
		]
	});
});
</script>
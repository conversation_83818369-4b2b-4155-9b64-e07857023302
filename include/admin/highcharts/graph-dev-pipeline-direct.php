<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-pipeline"></div>
	';
	// Récupération des statistiques

	$series = array();

	switch($CA){
		case 'ord_ca_direct':
			$series[] = array(
				'type' => 'column',
				'name' => 'Marge brute directe (HT)',
				'data' => array_values($stats[0]),
				'color' => '#2bbc32',
				'stack' => 'ca'
			);
			break;
		case 'ord_ca_indirect':
			$series[] = array(
				'type' => 'column',
				'name' => 'Marge brute indirecte (HT)',
				'data' => array_values($stats[0]),
				'color' => '#398fe5',
				'stack' => 'ca'
			);
			break;
		case 'ord_ca_all':
			$series[] = array(
				'type' => 'column',
				'name' => 'Marge brute directe (HT)',
				'data' => array_values($stats[0]),
				'color' => '#2bbc32',
				'stack' => 'ca'
			);
			$series[] = array(
				'type' => 'column',
				'name' => 'Marge brute indirecte (HT)',
				'data' => array_values($stats[1]),
				'color' => '#398fe5',
				'stack' => 'ca'
			);
			break;
		case 'inv_ca':
			$series[] = array(
				'type' => 'column',
				'name' => 'Chiffre d\'affaires facturé (HT)',
				'data' => array_values($stats[0]),
				'color' => '#f4c61f',
				'stack' => 'ca'
			);
			break;
	}

	$labels = '"'.implode('","',array_keys($stats[0])).'"';
	// Sépération en deux tableaux des CA en fonction des objectifs pour l'affichage 
	

	// $series[] = array(
	// 	'type' => 'column',
	// 	'name' => 'Marge potentielle',
	// 	'data' => array_map(function($ca){
	// 		$ca = $ca * 4;
	// 		return ($ca - ($ca / 1.05));
	// 	},$ord_ca['estim'][$filtre["usr_seller_id"]]),
	// 	'color' => '#1eea9c',
	// 	'stack' => 'estim'
	// );
	$json_series = json_encode( $series );

	?>

	<script>
		$(function () {
			$('#graph-pipeline').highcharts({
				chart: {
					type: "column",
					plotBorderWidth: 0,
					animation: false,
					events: {
						load: function (event) {
							var extremes = this.yAxis[0].getExtremes();
							if (extremes.dataMax == 0) {
								this.yAxis[0].setExtremes(0, 5);
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'statistiques-pipeline'
				},
				title: {
					text: '',
					x: -20
				},
				xAxis: {
					categories: [<?php print $labels; ?>],
				},
				yAxis: {
					allowDecimals: false,
					title: {
						text: ''
					},
					plotLines: [{
					    color: '#FF0000',
					    width: 2,
					    label: {
					   		text: 'Chiffre d\'affaires'
					    }
				   }],
		            stackLabels: {
		                enabled: true,
		                style: {
		                    fontWeight: 'bold',
		                    color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
		                },
					    formatter: function() {
					    	if (this.total > 0) {
						        return Highcharts.numberFormat(this.total, 2, ',')+' €';
					    	}
					    	return '';
					    }
		            }
				},
				legend: {
					layout: 'horizontal',
					align: 'center',
					verticalAlign: 'top',
					borderWidth: 0
				},
		        plotOptions: {
		            column: {
						stacking: 'normal',
		                dataLabels: {
		                    enabled: true,
		                    color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'black',
		                    formatter:function() {
		                    	if(this.y > 0) {
		                    	  return Highcharts.numberFormat(this.y, 2, ',')+' €';
		                    	}
		                    	return '';
		                    }
		                }
		            },
			        series: {
			            dataLabels:{
			                enabled:true,
			                formatter:function(){
			                    if(this.y > 0 && this.y != this.total){
			                        return Highcharts.numberFormat(this.y, 2, ',')+' €';
			                    }
			                }
			            }
			        }
		        },
				tooltip: {
					shared: true,
					crosshairs: true,
					valueDecimals: 2,
					followTouchMove: true,
					style: {
						fontSize: "13px"
					},
					formatter: function() {
						var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

						$.each(this.points, function(i, point) {
							str += '<br/><span>'+ point.series.name +'</span>: <b>'+
							Highcharts.numberFormat(point.y, 2, ',')+' €</b>';
						});

						return str;
					},
				},
		        series: <?php echo $json_series ?>
			});
		});
	</script>
<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-pipeline"></div>
	';
	// Récupération des statistiques
	$filtre = array("usr_seller_id" => 0, "only_seller_orders" => true);
	if (isset($_SESSION["ord_seller_id"])) {
		$filtre["usr_seller_id"] = $_SESSION["ord_seller_id"];
	}
	if (isset($_SESSION["only_seller_orders"])) {
		$filtre["only_seller_orders"] = $_SESSION["only_seller_orders"];
	}
	$pipeline = stats_graphs_get_datas( 'pipeline', $date1, $date2, $filtre, -1, $wst_id, false);

	$ca 	= array(); // Chiffre d'affaires
	$ca_inf = array(); // Chiffre d'affaires inférieur aux objectifs
	$ca_sup = array(); // Chiffre d'affaires supérieur aux objectifs
	$ca1 	= array(); // chiffre d'affaires probable
	$ca2 	= array(); // chiffre d'affaires idéal
	$ca3 	= array(); // chiffre d'affaires sécurisé
	$goal 	= array(); // objectif de CA

	if ($pipeline && count($pipeline) > 0) {
		$is_goal_setted = false;	
		foreach ($pipeline as $xVal => $yVals) {
			$ca[] = isset($yVals[0]) ? $yVals[0] : 0 ;
			$ca1[] = isset($yVals[1]) ? $yVals[1] : 0 ;
			$ca2[] = isset($yVals[2]) ? $yVals[2] : 0 ;
			$ca3[] = isset($yVals[3]) ? $yVals[3] : 0 ;

			if (isset($yVals[4]) && $yVals[4] > 0) {
				$is_goal_setted = true;
				$goal[] = $yVals[4];
			}else{
				$goal[] = null;
			}
		}

		// Séparation en deux tableaux des CA en fonction des objectifs pour l'affichage 
		array_map(function($c, $g){
			global $ca_sup;
			global $ca_inf;
			if ($c >= $g) {
				$ca_sup[] = $c;
				$ca_inf[] = null;
			}else{
				$ca_sup[] = null;
				$ca_inf[] = $c;
			}

		}, $ca, $goal);
	?>

	<script>
		<?php 
			$max = max(array_values($pipeline));
			if (is_array($max) && count($max)) {
				$max = $max[0];
			}
		?>
		$(function () {
			$('#graph-pipeline').highcharts({
				chart: {
					type: "column",
					plotBorderWidth: 0,
					animation: false,
					events: {
						load: function (event) {
							var extremes = this.yAxis[0].getExtremes();
							if (extremes.dataMax == 0) {
								this.yAxis[0].setExtremes(0, 5);
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'statistiques-pipeline'
				},
				title: {
					text: '',
					x: -20
				},
				xAxis: {
					categories: [<?php print '\''.implode('\', \'', array_keys($pipeline)).'\''; ?>],
				},
				yAxis: {
					allowDecimals: false,
					title: {
						text: ''
					},
					plotLines: [{
					    color: '#FF0000',
					    width: 2,
					    value: <?php print $max; ?>,
					    label: {
					   		text: "<?php print _('Chiffre d\'affaires commandé')?>"
					    }
				   }],
		            stackLabels: {
		                enabled: true,
		                style: {
		                    fontWeight: 'bold',
		                    color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
		                },
					    formatter: function() {
					    	if (this.total > 0) {
						        return Highcharts.numberFormat(this.total, 2, ',')+' €';
					    	}
					    	return '';
					    }
		            }
				},
				legend: {
					layout: 'horizontal',
					align: 'center',
					verticalAlign: 'top',
					borderWidth: 0
				},
		        plotOptions: {
		            column: {
		                stacking: 'normal',
		                dataLabels: {
		                    enabled: true,
		                    color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
		                    formatter:function() {
		                    	if(this.y > 0) {
		                    	  return this.y+' €';
		                    	}
		                    	return '';
		                    }
		                }
		            },
			        series: {
			            dataLabels:{
			                enabled:true,
			                formatter:function(){
			                    if(this.y > 0 && this.y != this.total){
			                        return this.y;
			                    }
			                }
			            }
			        }
		        },
				tooltip: {
					shared: true,
					crosshairs: true,
					valueDecimals: 2,
					followTouchMove: true,
					style: {
						fontSize: "13px"
					},
					formatter: function() {
						var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

						$.each(this.points, function(i, point) {
							str += '<br/><span>'+ point.series.name +'</span>: <b>'+
							number_format( point.y, 2, ',', ' ' )+'</b>';
						});

						return str;
					},
				},
		        series: [{
		        	type: 'column',
		            name: "<?php print _('CA probable ( Estim. )')?>",
		            data: [<?php print implode(',', array_values($ca3)); ?>],
						color: '#FFFF96'
		        },{
		        	type: 'column',
		            name: "<?php print _('CA idéal ( Estim. )')?>",
		            data: [<?php print implode(',', array_values($ca2)); ?>],
						color: '#DCDC00'
		        },{
		        	type: 'column',
		            name: "<?php print _('CA sécurisé ( Estim. )')?>",
		            data: [<?php print implode(',', array_values($ca1)); ?>],
						color: '#FFFF00'
		        }<?php 

			        if ($is_goal_setted) {
			        	print ",{
				        	type: 'column',
					            name: '"._('CA supérieur aux objectifs ( TTC )')."',
					            data: [".implode(',', array_values($ca_sup))."],
									color: '#27AE60'
					        }";
			        	print ",{
				        	type: 'column',
					            name: '"._('CA inférieur aux objectifs ( TTC )')."',
					            data: [".implode(',', array_values($ca_inf))."],
									color: '#c94d1b'
					        }";
			        	print ",{
				        	type: 'spline',
					            name: '"._('Objectif de vente')."',
					            data: [".implode(',', array_values($goal))."],
									color: '#000000'
					        }";
			        }else{

        	        	print ",{
        		        	type: 'column',
        			            name: '".addslashes( _('Chiffre d\'affaires ( TTC )') )."',
        			            data: [".implode(',', array_values($ca_sup))."],
        							color: '#27AE60'
        			        }";	
			        }


		         ?> ]
			});
		});
	</script>

	<?php } ?>
<?php
require_once( 'categories.inc.php' );
require_once( 'stats.inc.php' );


// Récupération des statistiques
foreach( $cpts as $c ){
	$series[$c['name']] = stats_graphs_get_datas( 'price-watching', $date1, $date2, array( 'prd' => $_GET['prd'], 'cpt' => $c['id'] ), -1 );
}
$largeArraySize = 0;

foreach( $series as $serie ){
	if(count($serie) > $largeArraySize) {
		$largeArray = $serie;
	}
	$largeArraySize = count($serie);
}
?>
<script>
		$('#graph-offers').highcharts({
			chart: {
				type: 'spline',
				marginRight: 120
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-produit-numéro:<?php print $_GET['prd']?>'
			},
			title: {
				text: '',
				x: -20
			},
            xAxis: {
				categories: [<?php echo implode(', ', array_map(function($item){ return '"'.$item.'"'; }, array_keys($largeArray) ) )?>]
            },
			yAxis: {
				title: {
					text: 'Prix'
				},
				labels: {
					formatter: function () {
						return this.value + '€';
					}
				}
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function () {
					var str = '<span style="font-size: 12px;">' + this.x + '</span>';
					$.each(this.points, function (i, point) {
						$text = point.point.text.split('|');
						str += '<br/><span style="color:' + point.series.color + '">' +  point.series.name + ' : </span>' +($text[0] != ''? '<br/>' : '')+
							$text[0] + '<b>' + point.y.toString().replace('.',',') + ' €' + '</b><br/>' + $text[1] ;
					});

					return str;
				},
			},
			legend: {
				backgroundColor: '#ccc',
				itemWidth: 100,
				align: 'right',
				verticalAlign: 'top',
				layout: 'vertical',
				x: 0,
				y: 100
			},
			plotOptions: {
				spline: {
					marker: {
						radius: 4,
						lineWidth: 1
					}
				},
				series: {
					selected: true,
					showCheckbox: true,
					events: {
						checkboxClick: function( event ){
							if (this.visible) {
								this.hide();
							} else {
								this.show();
							}
						},
						legendItemClick: function( event ){
							this.checkbox.checked = this.selected = !this.visible;
						}
					}
				}
			},
			series: [
				<?php
                    $numItems = count( $series );
                    $i = 0;
                    foreach ($series as $key => $value) {
                        ?>

                {
                        	color: <?php
                            switch($key){
                                case 'Client':
                                    echo '"#7CB5EC"';
                                    break;
                                case 'Amazon':
                                    echo '"#FF9900"';
                                    break;
							}
						        ?>,
                            name: '<?php print ( $key == 'Client' ) ? 'Vous' : $key;?>',
                            data: [
                            	<?php 
                            		$display = array();
                            		foreach($value as $prices){
                            			$point = '';
										$is_promo = false;
                            			if( $prices == 0 ){
								            $point .= '{y:null,';
								        }else{
											list($price,$promo) = explode('-',$prices);

											if( $promo != '' && $promo < $price ){
												$is_promo = true;
												$point .= '{y:'.$promo.',';
											}else{
												$point .= '{y:'.$price.',';
											}
										}


                            			if( $is_promo ){
                            				$point .= 'text:"- Prix en promotion : |- Prix hors promotion : '.number_format($price, 2, ',', '').' €"}';
                            			}else{
                            				$point .= 'text:"|"}';
                            			}
                            			$display[] = $point;
                            		}
                            		echo implode(',',$display);
                            	?>
                            ]
                        }
                <?php
				if( ++$i !== $numItems ){
					echo ",";
				}?>
				<?php }?>
			]
		});
</script>

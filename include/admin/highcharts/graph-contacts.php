<?php

	/**	\file graph-contacts.php
	 * 
	 * 	Ce fichier est exclusivement appelé par la page htdocs/admin/stats/contact.php pour afficher les statistiques
	 * 	sur les contacts reçus par les différents sites web. Ces statistiques sont affichées sous forme de graphiques.
	 * 	La librairie Highcharts est utilisée pour le rendu.
	 * 
	 */

	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-contacts"></div>
	';

	// Récupération des statistiques
	$contacts = stats_graphs_get_datas( 'contacts', $date1, $date2, false, -1, $wst_id );
?>
<script>
	$(function () {
		$('#graph-contacts').highcharts({
			chart: {
				type: "spline",
				plotBorderWidth: 0,
				animation: false,
				events: {
					load: function (event) {
						var extremes = this.yAxis[0].getExtremes();
						if (extremes.dataMax == 0) {
							this.yAxis[0].setExtremes(0, 5);
						}
					}
				}
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-contacts'
			},
			title: {
				text: '',
				x: -20
			},
			exporting: {
				filename: 'contacts'
			},
			credits: {
				enabled: false
			},
			xAxis: {
				categories: [<?php print '\''.implode('\', \'', array_keys($contacts)).'\''; ?>]
			},
			yAxis: {
				title: {
					text: ''
				},
				min: 0,
				plotLines: [{
					value: 0,
					width: 1,
					color: '#808080'
				}]
			},
			legend: {
				layout: 'horizontal',
				align: 'center',
				verticalAlign: 'top',
				borderWidth: 0
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function() {
					var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

					$.each(this.points, function(i, point) {
						str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +' : </span><b>'+
						point.y+'</b>';
					});

					return str;
				},
			},
			series: [
				{
					name: '<?php print _('Contacts'); ?>',
					data: [<?php print implode( ', ', array_values($contacts) ); ?>],
					color: '#4572A7'
				}
			]
		});
	});
</script>
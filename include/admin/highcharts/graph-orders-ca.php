<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '<h3>'._('Chiffre d\'affaires commandé').'</h3>';
	print '<div id="graph-orders-ca"></div>';

	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

    $filtre = array();
	$pay_id = isset($_GET['pay_id']) && trim($_GET['pay_id'])!="" ? $_GET['pay_id'] : 0;
	$stores = isset($_SESSION['ord_store_id']) ? $_SESSION['ord_store_id'] : array();
    $filtre['seller_id'] = isset($_SESSION['ord_seller_id']) && is_numeric($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;

	// Récupération des statistiques
	$completed = array();
	$canceled = array();

	$currencies = prd_prices_get_all_currencies();
	foreach( $currencies as $currency ){
		$filtre['currency'] = $currency;
		$completed[] = stats_graphs_get_datas( 'order-ca-completed', $date_start, $date_end, $filtre, -1, $wst_id,false, $origin, $pay_id, $stores, $gescom, $is_web );
		$canceled[] = stats_graphs_get_datas( 'order-ca-canceled', $date_start, $date_end, $filtre, -1, $wst_id ,false, $origin, $pay_id, $stores, $gescom, $is_web );
	}
	$xAxis = array_unique(array_merge(array_keys($completed[0]), array_keys($canceled[0])));
?>
<script>
	$(function () {
		$('#graph-orders-ca').highcharts({
			chart: {
				type: "spline",
				plotBorderWidth: 0,
				animation: false,
				events: {
					load: function (event) {
						var extremes = this.yAxis[0].getExtremes();
						if (extremes.dataMax == 0) {
							this.yAxis[0].setExtremes(0, 5);
						}
					}
				}
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-chiffre-affaires-commande'
			},
			title: {
				text: '',
				x: -20
			},
			xAxis: {
				categories: [<?php print '\''.implode('\', \'', $xAxis).'\''; ?>]
			},
			yAxis: {
				title: {
					text: ''
				},
				min: 0,
				plotLines: [{
					value: 0,
					width: 1,
					color: '#808080'
				}]
			},
			legend: {
				layout: 'horizontal',
				align: 'center',
				verticalAlign: 'top',
				borderWidth: 0
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function() {
					var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

					$.each(this.points, function(i, point) {
						str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +' (<?php print _('en'); ?>'+point.series.userOptions.symbol+' ) : </span><b>'+
						number_format( point.y, 2, ',', ' ' ) +'</b>';
					});

					return str;
				},
			},
			series: [
				<?php foreach( $currencies as $key => $currency ){
					$formatter = new NumberFormatter('fr_FR', NumberFormatter::CURRENCY);?>
				{
					name: '<?php print _('Commandes validées'); ?>',
					symbol: '<?php print preg_replace('#[a-z0-9.,]*#i', '', $formatter->formatCurrency('0', $currency)) ?>',
					data: [<?php print implode( ', ', array_values($completed[$key]) ); ?>],
					color: '#4572A7'
				},
				{
					name: '<?php print _('Commandes annulées'); ?>',
					symbol: '<?php print preg_replace('#[a-z0-9.,]*#i', '', $formatter->formatCurrency('0', $currency)) ?>',
					data: [<?php print implode( ', ', array_values($canceled[$key]) ); ?>],
					color: '#89A54E'
				},
			<?php } ?>
			]
		});
	});
</script>
<?php
	require_once('calls.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-calls-reports"></div>
    ';
    
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if( isset($_GET["date1"], $_GET["date2"]) ){
		$date1 = ria_mysql_escape_string( dateheureparse( $_GET["date1"] ) );
        $date2 = ria_mysql_escape_string( dateheureparse( $_GET["date2"] ) );
	} 
    
	$calls_history = stats_graphs_get_datas( 'calls-reports', $date1, $date2, array('author'=>$author) );
    $currentType = "";
	if ($calls_history && sizeof($calls_history) > 0) {
		$statsGraph = array();
		foreach ($calls_history as $xVal => $yVals) {
			foreach ($yVals as $key => $value) {
				$statsGraph[$key][] = isset($yVals[$key]) ? $yVals[$key] == "n" ? "null" : $yVals[$key] : 0;
			}
		}
		$currentType = $type['id'];
	?>

	<script><!--
		$(function () {
			$('#graph-calls-reports').highcharts({
				chart: {
					type: "column",
					plotBorderWidth: 0,
					animation: false,
					events: {
						load: function (event) {
							var extremes = this.yAxis[0].getExtremes();
							if (extremes.dataMax == 0) {
								this.yAxis[0].setExtremes(0, 5);
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'calls-reports'
				},
				title: {
                    text: "<?php 
                            if (strtotime($date1) != strtotime($date2)) {
                                printf(_('Evolution du nombre de rapports du %s au %s'), dateformatfull(date("d/m/Y", strtotime($date1))), dateformatfull(date("d/m/Y", strtotime($date2))));
                            } else {
                                printf(_('Evolution du nombre de rapports le %s'), dateformatfull(date("d/m/Y", strtotime($date1))));
                            }
                        ?>"
                },
				xAxis: {
					categories: [<?php print '\''.implode('\', \'', array_keys($calls_history)).'\''; ?>],
				},
				yAxis: {
					allowDecimals: false,
					title: {
						text: ''
					},
					plotLines: [],
		            stackLabels: {
		                enabled: true,
		                style: {
		                    fontWeight: 'bold',
		                    color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
		                },
					    formatter: function() {
					    	if (this.total > 0) {
						        return Highcharts.numberFormat(this.total, 0, ',');
					    	}
					    	return '';
					    }
		            }
				},
				legend: {
					layout: 'horizontal',
					align: 'center',
					verticalAlign: 'top',
					borderWidth: 0
				},
		        plotOptions: {
		            column: {
                        pointPadding: 0.2,
                        borderWidth: 0
                    }
		        },
				tooltip: {
					shared: true,
					crosshairs: true,
					valueDecimals: 0,
					followTouchMove: true,
					style: {
						fontSize: "13px"
					},
					formatter: function() {
						var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

						$.each(this.points, function(i, point) {
							str += '<br/><span>'+ point.series.name +'</span>: <b>'+
							number_format( point.y, 0, ',', ' ' )+'</b>';
						});

						return str;
					},
				},
		        series: [
<?php
	$cpt = 0;
	foreach ($statsGraph as $key => $val) {
		if($cpt > 0){echo ',';}
		$listTypesReports = rp_types_get($key);
		$type_information = ria_mysql_fetch_assoc($listTypesReports);
		$cpt++;
		echo ' {' . "\n";
		echo '	type: "column",' . "\n";
		echo '	name: "' . $type_information['name'] . '",' . "\n";
		echo '	data: [' . implode(',', array_values($val)) . ']//,' . "\n";
		if($currentType == "" || $currentType == $key){
			echo ',visible:true';
		}else{
			echo ',visible:false';
		}
		//echo '	//color: '#33cc33'';
		echo '}' . "\n";
	}
?>
		]});

		});
	//--></script>

	<?php } ?>
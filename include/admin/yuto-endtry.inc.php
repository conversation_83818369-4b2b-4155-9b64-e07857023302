<?php
/**	\file yuto-endtry.inc.php
 * 
 *	Popup d'information de fin de la période d'essai.
 *
 *	Cette popup est affichée aux utilisateurs ayant testé gratuitement Yuto pendant 14 jours
 *	pour les informer de la fin de leur période d'essai et leur proposer d'activer leur abonnement.
 *
 */

require_once('strings.inc.php');
$package = ucfirst( getenv('ENVRIA_PACKAGE') );

?>
<div style="display:none;" class="js-popup-intro ria-admin-ui-intro-wrapper js-popup-yuto-alert">
	<div class="ria-admin-ui-intro">
		<img class="ria-admin-ui-intro-media" src="/admin/images/yuto-alert-end.png" alt="" />
		<div class="ria-admin-ui-intro-title"><?php print str_replace('#param[package]#', $package, _('Activez votre abonnement pour continuer à utiliser Yuto #param[package]# !')); ?></div>
		<?php
			print '
				<div class="ria-admin-ui-intro-caption yuto-alert">
			';
			
			if( $yuto_end_5days > 1 ){
				print str_replace(array('#param[jours restants]#', '#param[package]#', '#param[date de fin]#'), array($yuto_end_5days, $package, $d1->format('d/m/Y')), _('Votre période d\'essai Yuto #param[package]# se termine dans #param[jours restants]# jours. Après le #param[date de fin]#, votre compte sera fermé.'));
			}else{
				print str_replace(array('#param[package]#'), array($package, $d1->format('d/m/Y')), _('Votre période d\'essai Yuto #param[package]# se termine aujourd\'hui. Demain, votre compte sera fermé.'));
			}
			
			print '
				</div>
				
				<button type="button" onclick="displayPopup(\''.str_replace('#param[package]#', $package, _('Activation de mon abonnement Yuto #param[package]#')).'\', \'\', \'/admin/options/popup-reactivate-subscription.php?activation=1\', \'\');return false;" class="ria-admin-ui-intro-button yuto-alert">'.str_replace('#param[package]#', $package, _('J\'active mon abonnement Yuto #param[package]#')).'</button>
			';
		?></div>
	</div>
</div>
<script><!--
	// Gestion de l'affichage de la popup d'alerte de fin de période d'essai
	$(document).ready(function(){
		// Récupère la date d'aujourd'hui pour n'afficher cette popup qu'une seule fois par jour
		var yutoAlertEnd = new Date();

		// Récupère le timestamp du dernier affichage (en milliseconde)
		var lastShow = sessionStorage.getItem('yuto-alert-end');

		// Si elle n'a jamais été affichée ou que cela fait plus d'un jour, on la réaffiche
		if( lastShow === null || lastShow != yutoAlertEnd.toDateString() ){	
			var popup_intro_content = $('.js-popup-yuto-alert').html();
			displayPopup('', popup_intro_content, '', '', 350, '60%', false, false);
			sessionStorage.setItem('yuto-alert-end', yutoAlertEnd.toDateString());
		}
	});
//--></script>

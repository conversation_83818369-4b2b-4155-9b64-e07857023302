<?php
	/**	\file bienvenue.inc.php
	 *	Ce fichier est chargé d'afficher la popup de bienvenue pour la refonte du back-office RiaShop.
	 *	Il pourra éventuellement être réutilisé pour d'autres communications en changeant juste le contenu HTML ci-dessous.
	 *	Tel que programmé, l'utilisateur ne verra le message qu'une seule fois.
	 *
	 *	Pour déterminer si l'utilisateur a déjà vu cette popup ou non, on utilise le champ libre 5028 associé à sa fiche client.
	 *
	 */
	 
	require_once('fields.inc.php');
	
	define( 'FLD_WELCOME', 5028 ); ///< Identifiant du champ stockant un booléen indiquant si la popup a déjà été présentée à l'utilisateur ou non
	
	$show_popup = false;
	if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
		$show_popup = !$admin_account->getWelcome();
	}else{
		$show_popup = fld_object_values_get( $_SESSION['usr_id'], FLD_WELCOME );
	}
?>
<?php if( !$show_popup ){ // Affiche la popup de bienvenue ?>
<div class="js-popup-intro ria-admin-ui-intro-wrapper">
	<div class="ria-admin-ui-intro">
		<img class="ria-admin-ui-intro-media" src="/admin/dist/images/illustration-popup-intro.svg" />
		<div class="ria-admin-ui-intro-title"><?php print _("Bienvenue"); ?><br/><?php print _("sur votre nouvelle interface"); ?></div>
		<div class="ria-admin-ui-intro-caption"><?php print _("Tout change, sauf de place !"); ?></div>
		<button type="button" onclick="return hidePopup();" class="ria-admin-ui-intro-button"><?php print _('Je découvre !'); ?></button>
	</div>
</div>
<script><!--
	$(document).ready(function(){
		var popup_intro_content = $('.js-popup-intro').html();
		displayPopup('', popup_intro_content, '', '', 350, 325);
		$('#popup_ria .popup_ria_drag').css({
			'height': 0,
			'padding': 0
		});
		$('#popup_ria .content').css({
			'top': 0,
			'border-radius': '10px',
			'padding': 0
		});
	})
//--></script>
<?php
	
		// Sauvegarde le fait que la popup de bienvenue a été affichée à l'utilisateur, pour ne plus lui afficher
		if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
			$admin_account->setWelcome(false)->save();
		}else{
			fld_object_values_set( $_SESSION['usr_id'], FLD_WELCOME, true );
		}
	}
?>

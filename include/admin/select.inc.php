<?php
/**	\file select.inc.php
 *	Fichier de la popup de choix de tenant sur l'admin mutualisé
 */
require_once('strings.inc.php');

?>
<div style="display:none;" class="js-popup-intro ria-admin-ui-intro-wrapper">
	<div class="ria-admin-ui-intro">
		<img class="ria-admin-ui-intro-media" src="/admin/dist/images/illustration-popup-intro.svg" alt="" />
		<div class="ria-admin-ui-intro-title"><?php print _("Choisissez une entreprise"); ?></div>
		<div class="ria-admin-ui-searchbar">
			<div class="mdc-text-field mdc-text-field--fullwidth mdc-text-field--border dark_border mdc-text-field--with-trailing-icon">
				<input class="mdc-text-field__input select-tenant-input mdc-text-field__search-input" type="text" placeholder="<?php echo _('Rechercher une entreprise')?>" aria-label="Votre recherche" name="tenant">
				<div class="mdc-text-field__icon" aria-label="Votre recherche" aria-hidden="true" aria-pressed="false" tabindex="1">
					<img src="/admin/dist/images/search.svg" alt="search" width="24" height="24" class="mdc-icon-button__icon">
				</div>
			</div>
		</div>
		<div class="ria-admin-ui-intro-caption select-tenant">
			<?php
				// récupère la liste des tenants
			$monitoring = new Monitoring();
			$ar_tenants = $monitoring->getTenants();
			$tenants = array();

			$first = true;
			// Affiche la liste des tenants pouvant être choisis
			ob_start();
			foreach( $ar_tenants as $tnt_id=>$data ){
				if( !$admin_account->hasAccess($tnt_id) ){
				        continue;
				}

				print '<li class="item'.($first ? ' first' : '').'"><a href="?get-tenant-admin='.$tnt_id.'">'.htmlspecialchars($data['name']).'</a></li>';
				if($first){
					$first = false;
				}
			}

			$content = ob_get_clean();
			if( trim($content) ){
				print '<ul class="select-tenant-list">'.$content.'</ul>';
			}
			?></div>
	</div>
</div>
<script><!--
	$(document).ready(function(){
		<?php 
			if( !$admin_account->getTenantSelected() ){
				print 'showPopupSelectTenant(true);';
			}
		?>
	});

	function showPopupSelectTenant(noclose){
		var popup_intro_content = $('.js-popup-intro').html();
		displayPopup('', popup_intro_content, '', '', 350, '60%', false, noclose);
		var popup = $('#popup_ria');
		$('.popup_ria_drag', popup).css({
			'height': 0,
			'padding': 0
		});
		$('.content', popup).css({
			'top': 0,
			'border-radius': '10px',
			'padding': 0
		});
		$('.content', popup).css({
			'overflow': 'auto'
		});

		$('.content', popup).addClass('ria-admin-tenant-select');

		var	$delay = false;
		var list = $('.select-tenant-list', popup);

		$('.select-tenant-input', popup).on('keyup', function(){
			clearTimeout($delay);
			var value = $(this).val().toUpperCase();
			$('li', list).removeClass('first');
			if( value == ''){
				$('li', list).removeClass('hide');
				$('li:first-child', list).addClass('first');
				return;
			}
			$delay = setTimeout(function(){
				var first = true;
				$('li', list).each(function(i, li){
					var a = $("a", $(this));
					txtValue = a.text().toUpperCase();
					if (txtValue.indexOf(value) > -1) {
						$(this).removeClass('hide');
						if( first ){
							r = $(this).addClass('first');
							first = false;
						}
					} else {
						$(this).addClass('hide');
						$(this).removeClass('first');
					}
				});
			}, 100);
		});
		return false;
	}
//--></script>
<?php

/*

<h2><?php print _('Interface d\'administration'); ?></h2>

<p><?php print _('Bienvenue dans l\'interface d\'administration de votre plateforme RiaShop. Vous trouverez ci-dessous les différentes installation auxquels vous avez accès :'); ?></p>

<?php	
	// récupère la liste des tenants
	$monitoring = new Monitoring();
	$ar_tenants = $monitoring->getTenants();

	// Affiche la liste des tenants pouvant être choisis
	ob_start();
	foreach( $ar_tenants as $tnt_id=>$data ){
		if( !$admin_account->hasAccess($tnt_id) ){
			continue;
		}

		print '<li><a href="/select.php?get='.$tnt_id.'">'.htmlspecialchars($data['name']).'</a></li>';
	}

	$content = ob_get_clean();
	if( trim($content) ){
		print '<ul>'.$content.'</ul>';
	}

	require_once('admin/skin/footer.inc.php');
*/

<?php
/**	\file yuto-push-install.inc.php
 * 
 *	Popup d'accueil de l'utilisateur Yuto au début de sa période d'essai.
 *
 *	Sans incitation, nous avons remarqué que de nombreuses personnes démarrant une période d'essai
 *	n'installent même pas Yuto... Cette popup est un test pour voir si en les guidant dès leur première
 *	connexion vers l'installation, le ratio "période d'essai / installation de Yuto" s'améliore.
 *
 */

require_once('strings.inc.php');
require_once('devices.inc.php');

?>
<div style="display:none;" class="js-popup-intro ria-admin-ui-intro-wrapper js-popup-yuto-alert">
	<div class="ria-admin-ui-intro">
		<img class="ria-admin-ui-intro-media" src="/admin/images/yuto-alert-end.png" alt="" />
		<div class="ria-admin-ui-intro-title"><?php print _('Bienvenue !<br />C\'est parti avec Yuto'); ?></div>
		<?php
			print '
				<div class="ria-admin-ui-intro-caption yuto-alert">
			';
			
				print '<div id="step-one-wrapper"><span id="step-one">'._('Première étape :').'</span></div>';
				print _('installer l\'application Yuto sur votre appareil mobile');
			
			print '
				</div>
				
				<button type="button" onclick="document.location.href = \'/admin/fdv/devices/setup-guide.php\'; return false;" class="ria-admin-ui-intro-button yuto-alert">'._('Suivez le guide !').'</button>
			';
		?></div>
	</div>
</div>
<script><!--
	// Gestion de l'affichage de la popup de push pour l'installation de Yuto (1 fois par jour max)
	$(document).ready(function(){
		// Récupère la date d'aujourd'hui pour n'afficher cette popup qu'une seule fois par jour
		var yutoAlertEnd = new Date();

		// Récupère le timestamp du dernier affichage (en milliseconde)
		var lastShow = sessionStorage.getItem('yuto-push-install');

		// Si elle n'a jamais été affichée ou que cela fait plus de 1 jour, on la réaffiche
		if( lastShow === null || lastShow != yutoAlertEnd.toDateString() ){	
			var popup_intro_content = $('.js-popup-yuto-alert').html();
			displayPopup('', popup_intro_content, '', '', 350, '60%', false, false);
			sessionStorage.setItem('yuto-push-install', yutoAlertEnd.toDateString());
		}
	});
//--></script>
<style>
	#step-one-wrapper {
		margin-bottom: 1em;
	}
	#step-one {
		background-color: #E2F3FB;
		padding: 5px;
	}
</style>
<?php
	// Filtres de dates liés au sélecteur de période
	// Par défaut, on arrive sur le jour en cours
	if( isset($_GET['date1']) && isdate($_GET['date1']) ){
		$_SESSION['datepicker_date1'] = $_GET['date1'];
	}
	
	if( isset($_GET['date2']) && isdate($_GET['date2']) ){
		$_SESSION['datepicker_date2'] = $_GET['date2'];
	}

	
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	
	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';

	// Filtres de site web (lié au sélecteur de site)
	$wst_id = 0;
	if( isset($_SESSION['ar_websitepicker']) && is_array($_SESSION['ar_websitepicker']) && sizeof($_SESSION['ar_websitepicker']) ){
		$wst_id = $_SESSION['ar_websitepicker'];
	}elseif( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	if( isset($_GET['wst']) ){
		if( is_array($_GET['wst']) && sizeof($_GET['wst']) ){
			$_SESSION['ar_websitepicker'] = $_GET['wst'];
			$_SESSION['websitepicker'] = $_GET['wst'][0];
			$wst_id = $_GET['wst'];
		}elseif( $_GET['wst'] == 0 ){
			if( isset($_SESSION['ar_websitepicker']) ){ unset( $_SESSION['ar_websitepicker'] ); }
			if( isset($_SESSION['websitepicker']) ){ unset( $_SESSION['websitepicker'] ); }
			$wst_id = 0;
		}
	}

<?php
	require_once('hotjar.inc.php');

	if( !(defined('ADMIN_HEAD_POPUP') && ADMIN_HEAD_POPUP) && !(defined('ADMIN_HEAD_LOGIN') && ADMIN_HEAD_LOGIN) ){
		print '</div>';
	}
?>

	<?php if( !(defined('ADMIN_HEAD_POPUP') && ADMIN_HEAD_POPUP) && !(defined('ADMIN_HEAD_LOGIN') && ADMIN_HEAD_LOGIN) ){ ?>
	<div id="site-footer" class="print-none">
		Copyright &copy; 2005/<?php print date('Y'); ?> - RiaStudio SAS - <?php print _('Tous droits réservés'); ?>
	</div>
	<?php } ?>

	<script src="https://unpkg.com/material-components-web@1.0.1/dist/material-components-web.min.js"></script>

	<!-- Global site tag (gtag.js) - Google Analytics -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=UA-380579-31"></script>
	<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());
		gtag('config', 'UA-380579-31');
	</script>

	<?php
		// Récupère le type de formule "essentiel", "business", "entreprise", etc...
		// Sur la page hors connexion à l'admin, cette valeur vaut "undefined"
		// $package = isset($config['tnt_id']) ? RegisterGCP::getPackage($config['tnt_id']) : 'undefined';
		$package = getenv('ENVRIA_PACKAGE');

		// Seulement pour les comptes non super-admin
		// Gestion de tags Analytics et Hotjar pour connaître qui utilise l'administration (essentiel, business / offline, online / etc...)
		if( $package != 'undefined' && isset($_SESSION['usr_tnt_id']) && is_numeric($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id'] > 0 ){
			// Toute autre formule que "essentiel" et "business" passe en "entreprise"
			if( !in_array($package, array('essentiel', 'business')) ){
				$package = 'entreprise';
			}

			// Tag permettant de savoir le canal (online ou offline)
			$canal = 'offline';

			// Le nom de la formule est complétée par "-online" ou "-offline" s'il s'agit d'une vente en ligne (donc gestion abo dans RiaShop)
			// Seulement pour les formule "essentiel" et "business"
			if( in_array($package, array('essentiel', 'business')) && gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION') ){
				$canal = 'online';
			}

			// Détermine s'il s'agit d'une formule avec abonnement ou sans abonnement
			// Par défaut à null car il ne s'agit pas d'un compte avec gestion d'abo
			$trial = $expired = null;

			// S'il existe une gestion d'abonnement
			if( gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION') ){
				$abo_yuto = dev_subscribtions_yuto_get( false, false );

				if( !is_array($abo_yuto) || !count($abo_yuto) ){
					$expired = true;
				}else{
					// Si oui ou non il existe un abonnement payant
					$trial = isset($abo_yuto['in_testing']) && $abo_yuto['in_testing'] ? true : false;
				}
			}

			$tag_analytics = $tag_hotjar = array();

			// Inclut dans les tags la formule
			$tag_analytics['subscription'] = $package;
			$tag_hotjar[] = $package;

			// Inclut dans les tarfs le canal
			$tag_analytics['canal'] = $canal;
			$tag_hotjar[] = $canal;

			// Inclut dans les tags l'information de perdu ou non (seulement s'il y a une gestion d'abonnement)
			if( $expired !== null ){
				$tag_analytics['expired'] = $expired ? '1' : '0';

				if( $expired === true ){
					$tag_hotjar[] = 'expired';
				}
			}

			// Inclut dans les tags l'information en essai ou pas (seulement s'il y a une gestion d'abonnement)
			if( $trial !== null ){
				$tag_analytics['trial'] = $trial ? '1' : '0';
				$tag_hotjar[] = $trial ? 'trial' : 'paid';
			}

			// Ecriture du tags Analytics pour les informations de formule + abonnement
			print '
				<script>
					gtag(\'set\', \'user_properties\', {
			';

			$first = true;
			foreach( $tag_analytics as $key=>$value ){
				if( !$first ){
					print ', ';
				}

				print $key.': \''.htmlspecialchars($value).'\'';
				$first = false;
			}

			print '
					});
			';

			// Ecriture du tag Hotjar pour les informations de formule + abonnement
			if( hotjar_include_tenant() ){
				print '
					hj(\'tagRecording\', [\''.implode('\', \'', $tag_hotjar).'\'])
				';
			}
			print '</script>';
		}

		$filename = '';
		if( isset($_SERVER['SCRIPT_URL']) && trim($_SERVER['SCRIPT_URL']) != '' ){
			$filename = $_SERVER['SCRIPT_URL'];
		}elseif( isset($_SERVER['SCRIPT_FILENAME']) && trim($_SERVER['SCRIPT_FILENAME']) != '' ){
			$filename = str_replace( $config['site_dir'].'/admin', '', $_SERVER['SCRIPT_FILENAME'] );
		}

		$file_admin = trim($filename) != '' ? str_replace( '/admin', '', $filename ) : 'none';

		$file_js = array('responsive.js');

		// Détermine si la librairie TinyMCE doit être incluse ou non, en fonction de la page en cours de consultation
		switch( $file_admin ){
			case '/catalog/edit.php':
			case '/catalog/product.php':
			case '/config/cgv/edit.php':
			case '/documents/medias/channels/edit.php':
			case '/documents/medias/medias/edit.php':
			case '/documents/medias/playlists/edit.php':
			case '/tools/cms/edit.php':
			case '/tools/faq/category.php':
			case '/tools/faq/question.php':
			case '/tools/news/edit.php':
			case '/tools/banners/edit.php':
			case '/config/livraison/stores/edit.php':
			case '/promotions/specials/edit.php':
			// case '/fdv/reports/view.php':
				array_push($file_js, array('file' => 'jquery.tinymce.js', 'dir' => '/admin/js/tinymce', 'extern' => false));
			break;
		}

		// Détermine si la librairie Google Maps doit être incluse ou non, en fonction de la page en cours de consultation
		switch( $file_admin ){
			case '/customers/edit.php':
			case '/fdv/devices/devices-location.php':
			case '/fdv/maps/index.php':
			case '/fdv/reports/view.php':
			case '/fdv/devices/index.php':
			case '/fdv/devices/edit.php':
				array_push($file_js, array('file' => 'https://maps.googleapis.com/maps/api/js?v=3.33&key=AIzaSyC2kw5JIKzoVM3pvjOs7iPOe1kP2iy5nXI&amp;callback=initMap', 'dir' => '', 'extern' => true));
				break;
		}

		// Détermine si la librairie zipcode-city-autocompletion.js doit être incluse ou non, en fonction de la page en cours de consultation
		switch( $file_admin ){
			case '/config/livraison/deposits/edit.php':
			case '/customers/edit.php':
			case '/customers/new.php':
			case '/customers/popup-address.php':
				array_push( $file_js, 'zipcode-city-autocompletion.js' );
				break;
		}

		// Détermine les autres inclusions de librairies devant être réalisées, en fonction de la page en cours de consultation.
		switch( $file_admin ){
			case '/config/paiements/transfer.php' :
				array_push($file_js, 'config/paiements.js');
				break;
			case '/config/gifts/index.php' :
				array_push($file_js, 'json.js', 'config/gifts.js');
				break;
			case '/config/livraison/stores/index.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'json.js', 'config/livraison.js');
				break;
			case '/config/filters/edit.php' :
				array_push($file_js, 'config/filters.js');
				break;
			case '/stats/prd-weight-fills.php' :
				array_push($file_js, 'stats/index.js', 'json.js');
				break;
			case '/orders/models.php' :
				array_push($file_js, 'orders/models.js', 'json.js');
				break;
			case '/tools/livr-alerts/index.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'jquery.tablesorter.pager.js', 'tools/alerts.js', 'json.js');
				break;
			case '/tools/newsletter/list.php' :
				array_push($file_js, 'json.js', 'tools/newsletter.js');
				break;
			case '/orders/returns/return.php':
				array_push($file_js, 'orders/return.js');
				break;
			case '/orders/returns/returns.php' :
				array_push($file_js, 'json.js', 'orders/returns.js');
				break;
			case '/orders/orders.php' :
				array_push($file_js, 'json.js', 'orders/index.js');
				break;
			case '/orders/order.php' :
				array_push($file_js, 'orders/attempts.js', 'orders/order.js', 'orders/modify.js');
				break;
			case '/customers/index.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'json.js', 'customers/index.js');
				break;
			case '/moderation/moderation.php' :
				array_push($file_js, 'json.js', 'contacts.js', 'moderation/index.js');
				break;
			case '/documents/index.php' :
				array_push($file_js, 'json.js', 'translate.js', 'documents/types.js', 'documents/index.js', 'riaSelector.js');
				break;
			case '/documents/popup-docs.php' :
				array_push($file_js, 'documents/index.js', 'riaSelector.js');
				break;
			case '/documents/images/alt.php' :
				array_push($file_js, 'translate.js');
				break;
			case '/comparators/search/index.php' :
				array_push($file_js, 'json.js', 'jquery.tablesorter.min.js', 'comparators/catalog.js', 'comparators/search.js', 'comparators/stats.js');
				break;
			case '/comparators/stats/index.php' :
				array_push($file_js, 'json.js', 'comparators/index.js', 'jquery.tablesorter.min.js', 'comparators/catalog.js', 'comparators/stats.js');
				break;
			case '/config/prices/index.php' :
			case '/config/prices/edit.php' :
				array_push($file_js, 'config/prices-categories.js');
				break;
			case '/config/catalogue/relations/edit.php' :
				array_push($file_js, 'config/catalog.js');
				break;
			case '/config/livraison/services/index.php' :
				array_push($file_js, 'config/livraison.js');
				break;
			case '/config/cgv/articles.php' :
				array_push($file_js, 'config/cgv.js');
				break;
			case '/config/returns/index.php' :
				array_push($file_js, 'treeview.js', 'comparators/index.js', 'config/returns.js');
				break;
			case '/config/fields/classes/objects.php' :
			case '/config/fields/classes/edit.php' :
			case '/config/fields/classes/index.php' :
			case '/config/fields/categories/index.php' :
			case '/config/fields/models/index.php' :
			case '/config/fields/fields/values.php' :
				array_push($file_js, 'fields.js');
				break;
			case '/config/fields/models/edit.php' :
				array_push($file_js, 'fields.js', 'shadowbox-2.0.js', 'skin/classic/skin.js');
				break;
			case '/config/emails/index.php' :
				array_push($file_js, 'config/emails.js');
				break;
			case '/tools/cms/edit.php' :
				array_push(
					$file_js,
					'translate.js',
					'metas.js',
					'shadowbox-2.0.js',
					'skin/classic/skin.js',
					'riaFieldRelated.js',
					'tools/news.js',
					'segments.js',
					'jquery.fancybox-1.3.4.js',
					'tools/cms.js',
					'fields.js'
				);
				break;
			case '/tools/cms/index.php' :
				array_push($file_js, 'tools/cms.js', 'tools/news.js');
				break;
			case '/customers/segments/index.php' :
			case '/config/fields/segments/index.php' :
				array_push($file_js, 'segments.js', 'documents/types.js', 'jquery.tablesorter.min.js', 'fields.js');
				break;
			case '/documents/types/index.php' :
				array_push($file_js, 'documents/types.js', 'jquery.tablesorter.min.js');
				break;
			case '/catalog/index.php' :
			case '/catalog/unclassified.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'catalog/index.js');
				break;
			case '/config/livraison/stores/edit.php' :
			case '/config/livraison/stores/popup-plage-horaire.php' :
				array_push($file_js, 'jquery-clockpicker.js', 'config/livraison.js', 'translate.js', 'metas.js', 'fields.js');
				break;
			case '/config/livraison/stores/popup-stores.php' :
				array_push($file_js, 'config/livraison.js');
				break;
			case '/config/livraison/options/index.php' :
			case '/config/livraison/packages/edit.php' :
			case '/config/livraison/packages/index.php' :
			case '/config/livraison/zones/index.php' :
			case '/config/livraison/deposits/edit.php' :
			case '/config/livraison/deposits/index.php' :
				array_push($file_js, 'config/livraison.js');
				break;
			case '/config/livraison/zones/edit.php' :
				array_push($file_js, 'config/livraison.js', 'config/dlv-zones.js');
				break;
			case '/config/livraison/services/edit.php' :
				array_push($file_js, 'translate.js', 'config/livraison.js');
				break;
			case '/config/pdf_generation/devis.php':
				array_push($file_js, 'config/devis-pdf.js');
				break;
			case '/config/translate/index.php' :
			case '/config/translate/popup-export.php' :
			case '/tools/news/categories/edit.php' :
				array_push($file_js, 'translate.js');
				break;
			case '/tools/glossary/index.php' :
			case '/tools/glossary/edit.php' :
				array_push($file_js, 'translate.js', 'tools/glossary.js');
				break;
			case '/config/cgv/edit.php' :
				array_push($file_js, 'translate.js', 'config/cgv.js');
				break;
			case '/catalog/brands/index.php' :
				array_push($file_js, 'orders/brands.js');
				break;
			case '/catalog/brands/edit.php' :
				array_push($file_js, 'orders/brands.js');
			case '/config/referencement/index.php' :
			case '/config/referencement/static.php' :
			case '/config/referencement/dynamic-sentences.php' :
				array_push($file_js, 'config/referencement.js', 'translate.js', 'metas.js');
				break;
			case '/config/referencement/edit-tag.php' :
				array_push($file_js, 'config/referencement.js', 'metas.js');
				break;
			case '/tools/erratums/edit.php' :
				array_push($file_js, 'translate.js', 'tools/erratums.js');
				break;
			case '/tools/faq/category.php' :
				array_push($file_js, 'translate.js', 'metas.js', 'tools/faq.js', 'jquery.tablesorter.min.js');
				break;
			case '/tools/faq/question.php' :
				array_push($file_js, 'translate.js', 'metas.js', 'tools/faq.js');
				break;
			case '/tools/news/edit.php' :
				array_push($file_js, 'translate.js', 'metas.js', 'tools/news.js', 'segments.js', 'jquery.elastic.js', 'fields.js');
				break;
			case '/tools/news/index.php' :
			case '/tools/news/index2.php' :
				array_push($file_js, 'translate.js', 'tools/news.js');
				break;
			case '/tools/banners/edit.php' :
			case '/tools/zones/edit.php' :
				array_push($file_js, 'translate.js', 'segments.js', 'tools/banners.js');
				break;
			case '/tools/banners/index.php' :
			case '/tools/zones/index.php' :
				array_push($file_js, 'translate.js', 'tools/banners.js');
				break;
			case '/customers/profiles/edit.php' :
				array_push($file_js, 'translate.js', 'customers/index.js', 'rights.js');
				break;
			case '/documents/types/edit.php' :
				array_push($file_js, 'translate.js', 'documents/types.js');
				break;
			case '/documents/images/edit.php' :
				array_push($file_js, 'translate.js', 'documents/images.js', 'fields.js');
				break;
			case '/documents/edit.php' :
				array_push($file_js, 'translate.js', 'segments.js', 'documents/index.js', 'riaSelector.js');
				break;
			case '/catalog/edit.php' :
				array_push($file_js,
				'fields.js',
				'translate.js',
				'metas.js',
				'catalog/price-tva-conditions.js',
				'comparators/catalog.js',
				'catalog/category.js',
				'catalog/categories.js',
				'segments.js',
				'riaSelector.js'
			);
				break;
			case '/catalog/product.php' :
				array_push(
					$file_js,
					'translate.js',
					'metas.js',
					'fields.js',
					'riaFieldRelated.js',
					'contacts.js',
					'moderation/index.js',
					'catalog/price-tva-conditions.js',
					'comparators/catalog.js',
					'products.js',
					'jquery.bgiframe.min.js',
					'stats/search.js',
					'json.js',
					'products/relations.js'
				);
				break;
			case '/stats/referencement/referencement.php' :
				array_push($file_js, 'metas.js', 'stats/referencement.js');
				break;
			case '/config/fields/units/edit.php' :
			case '/config/fields/units/index.php' :
			case '/config/fields/classes/object.php' :
			case '/config/fields/categories/edit.php' :
			case '/config/fields/fields/translate.php' :
			case '/config/fields/fields/edit.php' :
			case '/config/fields/fields/index.php' :
			case '/config/fields/index.php' :
				array_push($file_js, 'fields.js');
				break;
			case '/config/redirections/errors/errors-404.php' :
				array_push($file_js, 'config/errors-404.js', 'riaSelector.js');
				break;
			case '/stats/prd-no-image.php' :
				array_push($file_js, 'riaSelector.js', 'jquery.tablesorter.min.js', 'jquery.tooltip.min.js', 'documents/images.js', 'jquery.tablesorter.pager.js');
				break;
			case '/stats/content-no-image.php' :
				array_push($file_js, 'riaSelector.js', 'jquery.tablesorter.min.js', 'jquery.tooltip.min.js', 'documents/images.js', 'jquery.tablesorter.pager.js');
				break;
			case '/config/redirections/permanent/popup_add.php' :
			case '/config/redirections/permanent/index.php' :
				array_push($file_js, 'config/redirections.js');
				break;
			case '/config/products/index.php' :
				array_push($file_js, 'products.js');
				break;
			case '/config/cgv/index.php' :
			case '/config/messages/maintenance.php' :
				array_push($file_js, 'config/cgv.js');
				break;
			case '/moderation/spam/index.php' :
				array_push($file_js, 'moderation/index.js');
				break;
			case '/stats/origins.php' :
			case '/stats/contact.php' :
			case '/stats/payments.php' :
			case '/stats/orders.php' :
				break;
			case '/stats/bestsellers-cat.php' :
			case '/stats/bestsellers-prd.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'stats/index.js');
				break;
			case '/stats/prd-conversion.php' :
				array_push($file_js, 'jquery.tablesorter.min.js', 'jquery.tooltip.min.js', 'jquery.tablesorter.pager.js', 'stats/prd-conversion.js');
				break;
			case '/stats/search-suggestions.php' :
				array_push($file_js, 'stats/index.js');
				break;
			case '/stats/gender.php' :
			case '/stats/search-wo-results.php' :
				array_push($file_js, 'stats/index.js', 'translate.js');
				break;
			case '/stats/search.php' :
				array_push($file_js, 'translate.js', 'stats/search.js');
				break;
			case '/stats/search-substitut.php' :
				array_push($file_js, 'translate.js', 'stats/search-substitut.js');
				break;
			case '/config/expeditions/index.php' :
				array_push($file_js, 'config/expeditions.js');
				break;
			case '/comparators/categories.php' :
				array_push($file_js, 'treeview.js', 'comparators/index.js');
				break;
			case '/comparators/link-categories.php' :
				array_push($file_js, 'treeview.js', 'comparators/index.js', 'comparators/ctrcard.js');
				break;
			case '/comparators/comparators.php' :
				array_push($file_js, 'comparators/index.js', 'options-tree.js');
				break;
			case '/comparators/params.php' :
				array_push($file_js, 'comparators/index.js', 'options-tree.js');
				break;
			case '/config/owner/index.php' :
				array_push($file_js, 'config/owner.js');
				break;
			case '/config/cycles/edit.php' :
				array_push($file_js, 'config/cycles.js', 'shadowbox-2.0.js', 'skin/classic/skin.js');
				break;
			case '/config/cycles/index.php' :
				array_push($file_js, 'config/cycles.js');
				break;
			case '/tools/newsletter/filter.php' :
				array_push($file_js, 'tools/newsletter.js');
				break;
			case '/tools/newsletter/edit.php' :
				array_push($file_js, 'tools/newsletter.js', 'riaFieldRelated.js', 'fields.js');
				break;
			case '/tools/newsletter/index.php' :
				array_push($file_js, 'tools/newsletter.js');
				break;
			case '/tools/erratums/index.php' :
				array_push($file_js, 'tools/erratums.js');
				break;
			case '/tools/faq/index.php' :
				array_push($file_js, 'tools/faq.js', 'jquery.tablesorter.min.js');
				break;
			case '/tools/news/categories/index.php' :
				array_push($file_js, 'tools/news.js');
				break;
			case '/customers/segments/segment.php' :
			case '/config/fields/segments/segment.php' :
				array_push($file_js, 'segments.js');
				break;
			case '/tools/experts/edit.php' :
				array_push($file_js, 'tools/assistants.js');
				break;
			case '/orders/return/returns.php' :
				array_push($file_js, 'orders/returns.js');
				break;
			case '/orders/returns/index.php' :
				array_push($file_js, 'jquery.fancybox-1.3.4.js', 'orders/returns.js');
				break;
			case '/orders/returns/update.php' :
				array_push($file_js, 'jquery.fancybox-1.3.4.js', 'orders/return-update.js');
				break;
			case '/tools/cms/popup_image.php' :
				array_push($file_js, 'tools/cms.js');
				break;
			case '/tools/rewards/config/index.php' :
			case '/tools/rewards/config/popup-actions.php' :
			case '/tools/rewards/stats.php' :
				array_push($file_js, 'tools/rewards.js', 'tools/rwd-actions.js');
				break;
			case '/orders/model.php' :
				array_push($file_js, 'orders/create.js', 'orders/models.js');
				break;
			case '/ajax/orders/ncmd-rights.php' :
				array_push($file_js, 'orders/models.js');
				break;
			case '/ajax/orders/ncmd-delivery-edit.php':
			case '/ajax/orders/ncmd-customers-edit.php':
			case '/customers/profiles/new.php':
			case '/customers/profiles/index.php':
			case '/customers/new.php':
			case '/customers/ajax-usr-passwd.php':
			case '/customers/popup-wishlist-products.php':
				array_push($file_js, 'customers/index.js');
				break;
			case '/customers/edit.php':
				array_push($file_js, 'jquery.fancybox-1.3.4.js', 'customers/index.js', 'rights.js', 'jquery.tooltip.min.js', 'contacts.js', 'fields.js', 'catalog/authorizations.js', 'relations.js', 'tab-medias.js', 'json.js', 'contacts.js', 'moderation/index.js');
				break;
			case '/options/catalog.php' :
			case '/options/index.php' :
			case '/moderation/prd_moderation.php' :
			case '/comparators/index.php' :
				array_push($file_js, 'options-tree.js');
				break;
			case '/documents/images/index.php' :
				array_push($file_js, 'jquery.tooltip.min.js', 'documents/images.js');
				break;
			case '/catalog/authorizations/new.php' :
			case '/catalog/authorizations/edit.php' :
			case '/catalog/authorizations/index.php' :
				array_push($file_js, 'catalog/authorizations.js');
				break;
			case '/promotions/products/new.php' :
			case '/promotions/products/edit.php' :
			case '/promotions/products/index.php' :
				array_push($file_js, 'promotions/index.js', 'catalog/price-tva-conditions.js');
				break;
			case '/promotions/specials/index.php' :
				array_push($file_js, 'promotions/index.js', 'promotions/specials.js');
				break;
			case '/promotions/groups/edit.php' :
			case '/promotions/groups/index.php' :
				array_push($file_js, 'promotions/index.js');
				break;
			case '/catalog/discount/index.php' :
				array_push($file_js, 'catalog/price-tva-conditions.js');
				break;
			case '/promotions/specials/edit.php' :
				array_push($file_js, 'fields.js', 'promotions/specials.js');
				break;
			case '/promotions/specials/import/products.php':
			case '/promotions/specials/import/customers.php':
				print '
					<script src="/admin/js/promotions/specials.js"></script>
				';
				break;
			case '/documents/images/import/index.php' :
			case '/documents/images/popup.php' :
				array_push($file_js, 'documents/images.js');
				break;
			case '/comparators/stats/js_product.php' :
			case '/comparators/popup-choose-family.php' :
			case '/comparators/popup-details-family.php' :
			case '/comparators/mapping-attributs.php' :
			case '/comparators/popup-edit-prd-attr.php' :
				array_push($file_js, 'comparators/index.js', 'comparators/catalog.js');
				break;
			case '/catalog/edit-nomenclature.php' :
			case '/catalog/nomenclature.php' :
				array_push($file_js, 'products.js', 'translate.js');
				break;
			case '/catalog/exports.php' :
				array_push($file_js, 'tools/exports.js');
				break;
			case '/stats/popup-redirection-search.php' :
			case '/stats/js_search.php' :
				array_push($file_js, 'stats/search.js');
				break;
			case '/stats/popup-image.php' :
				array_push($file_js, 'stats/images.js');
				break;
			case '/login.php' :
				array_push($file_js, 'login.js');
				break;
			case '/documents/medias/channels/edit.php' :
			case '/documents/medias/playlists/edit.php' :
			case '/documents/medias/medias/edit.php' :
				array_push($file_js, 'medias.js');
				break;
			case '/fdv/devices/index.php' :
			case '/fdv/devices/edit.php' :
				array_push($file_js, 'yuto/index.js');
				break;
			case '/fdv/notifications/edit.php':
			case '/fdv/reports/index.php':
			case '/fdv/reports/edit.php':
			case '/fdv/reports/types/index.php':
			case '/fdv/reports/calls/index.php':
			case '/fdv/reports/view.php':
				array_push($file_js, 'yuto/index.js','jquery-clockpicker.js');
				break;
			case '/fdv/stats/spent-time.php':
				array_push($file_js, 'jquery.tablesorter.min.js');
				break;
			case '/fdv/devices/working-shift.php':
				array_push($file_js, 'yuto/periods.js', 'riaSelector.js');
				break;
			case '/fdv/config/index.php':
				array_push($file_js, 'yuto/index.js');
				break;
			case '/fdv/prize/index.php' :
				array_push($file_js, 'yuto/prize.js');
				break;
			case '/config/img-default.php':
				array_push($file_js, 'config/img-default.js');
				break;
			case '/stats/price-watching.php':
				array_push($file_js, 'jquery.tablesorter.min.js', 'jquery.tablesorter.pager.js', 'stats/price-watching.js');
				break;
			case '/tools/marketing/new.php' :
			case '/tools/marketing/edit.php' :
				array_push($file_js, 'campaign.js');
				break;
			case '/tools/imports/' :
			case '/tools/imports/index.php' :
			case '/tools/imports/mapping.php':
			case '/tools/imports/reports.php':
			case '/tools/imports/backups.php':
				array_push($file_js, 'tools/imports.js', 'jquery.tablesorter.min.js', 'jquery.tablesorter.pager.js');
				break;

			// refonte import
			// case '/tools/imports/index-v2.php':
			// case '/tools/imports/mapping-v2.php':
			// 	array_push($file_js, 'imports-v2.js', 'jquery.tablesorter.min.js', 'jquery.tablesorter.pager.js');
			// 	break;
			// fin refonte import

			case '/tools/marketing/sms/index.php':
			case '/tools/marketing/sms/edit/edit.php':
				array_push($file_js, 'tools/marketing.js');
				break;
			case '/debug.php':
				array_push($file_js, 'jquery.tablesorter.min.js', 'jquery.tablesorter.pager.js');
				break;
			case '/config/avis_verifie/index.php' :
				array_push($file_js, 'fields.js');
			case '/config/instagram/index.php' :
				array_push($file_js, 'fields.js', 'config/instagram.js');
				break;
			case '/options/rights.php':
				array_push($file_js, 'options/admin-rights.js');
				break;
		}

		foreach ($file_js as $one_file) {
			if (!is_array($one_file)) {
				$one_file = array('file' => $one_file, 'dir' => '/admin/js', 'extern' => false);
			}

			$dir_file_js = $one_file['dir'];
			if (trim($dir_file_js) != '') {
				$dir_file_js .= '/';
			}

			print '
				<script src="'.$dir_file_js.$one_file['file'].( $one_file['extern'] ? '' : '?'.ADMIN_ASSET).'" '.( isset($one_file['async']) && $one_file['async'] ? ' async' : '' ).' defer></script>
			';
		}

		// Les utilisateurs super-administrateurs (RiaStudio) bénéficient d'informations sur les performances de chargement
		if (isset($config['USER_RIASTUDIO']) && $config['USER_RIASTUDIO']) {
			print '<div class="page_timer">Temps de chargement de la page : '.round( (microtime(true) - $start_time_page), 6 ).'s</div>';
		}

		// Affichage de popup automatique hors page de login
		if( !defined('ADMIN_HEAD_LOGIN') || !ADMIN_HEAD_LOGIN ){
			// Sur l'environnement de l'administration mutualisé, on gère l'affichage d'une popup lorsqu'aucune entreprise n'est sélectionnée
			if( getenv('oneriashop') !== false ){
				require_once('admin/select.inc.php');
			}
		}

		// Affichage d'une popup dans le cas où l'abonnement Yuto arrive à échéance
		if( isset($yuto_end_5days) && $yuto_end_5days ){
			require_once('admin/yuto-endtry.inc.php');
		}

		// Si l'utilisateur n'a pas encore installé Yuto, affichage d'une popup d'incitation à l'installation
		if( isset($trial) && $trial && isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']!=0 && !gu_user_have_yuto_installed() ){
			require_once('admin/popups/yuto-push-install.inc.php');
		}

	?>

	<div class="load-ajax-opacity"></div>
	<div class="notice message-ajax-opacity"></div>

<?php
	//Insertion conditionnelle du chat Userlike
	// $package = (RegisterGCP::getPackage($config['tnt_id']));
	$package = getenv('ENVRIA_PACKAGE');
	
	$tnt_blackliskt_chat = array(185,262,365,265,395,230,183,280,220,263,283,430,447,271,207,288,290,219,282,121,268,459,524,523);
	if ((!in_array($config['tnt_id'],$tnt_blackliskt_chat )) && (($package == 'business') || ($package=='essentiel'))) {

		if ((isset($trial) && $trial) || (isset ($expired) && $expired )) {
?>

<script async type="text/javascript" src="https://userlike-cdn-widgets.s3-eu-west-1.amazonaws.com/bbe050df403a7dd7f0252b7fc09eca2751a2ce960b26bbfbfb56fd25d16cd9b4.js"></script>
<script>
 var data = {};
 data.user = {};
 data.user.name= '<?php echo htmlspecialchars($_SESSION['usr_firstname']." " .$_SESSION['usr_lastname']);?>';
 data.user.email = '<?php echo htmlspecialchars($_SESSION['usr_email']); ?>';
 data.custom = {};
 data.custom.nom_du_tenant='<?php echo htmlspecialchars($_SESSION['usr_tnt_id']); ?>';
 data.custom.formule_vente_en_ligne='<?php echo RegisterGCP::getPackage($config['tnt_id']) ?>';
 userlikeReady = function() {
 	userlike.setData(data);
 	userlike.userlikeUpdateAPI(); /*nécessaire uniquement pour les tests */
 }
</script>
<?php }
	}
?>
</body>
</html>

<?php
	require_once('define.inc.php');

	/**	\defgroup positions Tri
	 *	\ingroup system
	 *	Ce module peut être utilisé par les objets qui ont une position et qui peuvent être triés (catégories, cms, etc.)
	 *	Plutôt que d'écrire une fonction par module, une fonction de ce module peut être utilisée de manière générique
	 *	Le traitement est à un seul endroit, évite de copier/coller. Si un bug est corrigé, tous les modules en profitent ...
	 *	@{
	 */

	/**	Déplace l'objet avant ou après un autre objet
	 *	@param int $cls Type des objets à repositionner (DD_CATEGORY, DD_PRODUCT, etc.)
	 *	@param int $source Identifiant de la source (type dépend de la classe)
	 *	@param int $target Identifiant de la cible (type dépend de la classe)
	 *	@param string $where <PERSON><PERSON><PERSON> valant "before" ou "after" indiquant où déplacer la source par rapport à target
	 *	@param int $obj_complement Optionnel identifiant d'un objet secondaire (par exemple, prd_id pour prd_images_get())
	 *	@param bool $upd_pos Optionnel, par défaut les positions seront mises à jour pour éviter toute valeur null
	 *
	 *	@return bool true en cas de succès, false sinon
	*/
	function obj_position_update( $cls, $source, $target, $where, $obj_complement=0, $upd_pos=true ){
		global $config;

		if( !is_numeric($cls) ) return false;
		if( !in_array( $where, array('before', 'after')) ) return false;

		$p_cls = $cls;
		$p_source = $source;
		$p_target = $target;
		$p_where = $where;
		$p_obj_complement = $obj_complement;

		switch( $cls ){
			case DD_CATEGORY : {	// Catégorie
				// Charge la catégorie
				$rcat = prd_categories_get($source);
				if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;
				$cat = ria_mysql_fetch_array($rcat);

				// Charge les catégories du même parent
				$cat['parent_id'] = !is_numeric($cat['parent_id']) ? 0 : $cat['parent_id'];
				$rcat = prd_categories_get( 0, false, $cat['parent_id'], '', false, false, null, array('cat_pos' => 'asc') );
				if( !$rcat ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rcat) )
					$data[] = array_merge(array('cat_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' => array('cat_tnt_id'=>$config['tnt_id'], 'cat_id'=>$source),
					'target' => array('cat_tnt_id'=>$config['tnt_id'], 'cat_id'=>$target),
					'data' => $data,
					'tableName' => 'prd_categories',
					'primaryKey' => array('cat_tnt_id', 'cat_id'),
					'categoryKey' => array('cat_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'cat_pos',
					'alias' => array('cat_id'=>'id', 'cat_pos'=>'pos')
				);
				break;
			}
			case DD_CGV_ARTICLE : {	// article cgv
				if( !isset($source['ver'], $source['article']) || !is_numeric($source['article']) ) return false;
				if( !isset($target['ver'], $target['article']) || !is_numeric($target['article']) ) return false;
				if( $source['ver'] != $target['ver'] || !is_numeric($source['ver']) ) return false;

				// Charge l'article
				$rart = cgv_articles_get( $source['article'] );
				if( !$rart || !ria_mysql_num_rows($rart) ) return false;

				// Charge les articles
				$rart = cgv_articles_get( null, null, $source['ver'] );
				if( !$rart ) return false;
				$data = array();
				while( $dat = ria_mysql_fetch_array($rart) )
					$data[] = array_merge(array('art_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' => array('art_tnt_id'=>$config['tnt_id'], 'art_wst_id'=>$config['wst_id'], 'art_id'=>$source['article']),
					'target' => array('art_tnt_id'=>$config['tnt_id'], 'art_wst_id'=>$config['wst_id'], 'art_id'=>$target['article']),
					'where' => $where,
					'data' => $data,
					'tableName' => 'cgv_articles',
					'primaryKey' => array('art_tnt_id', 'art_wst_id', 'art_id'),
					'categoryKey' => array( 'art_tnt_id'=>$config['tnt_id'], 'art_wst_id'=>$config['wst_id'] ),
					'fieldPos' => 'art_pos',
					'alias' => array( 'art_wst_id'=>'wst_id', 'art_id'=>'id', 'art_pos'=>'pos' )
				);
				break;
			}
			case DD_CMS : {	// cms
				// Charge le cms
				$rcat = cms_categories_get( $source, false, false, -1, false, false, true, null, false, null, false );
				if (! ($rcat && ria_mysql_num_rows($rcat))) return false;
				if (! $cat = ria_mysql_fetch_array($rcat)) return false;

				$parent = $cat['parent'];

				// Charge les cms
				$rcat = cms_categories_get( 0, false, false, $parent, false, false, true, null, false, null, false );
				if (! $rcat) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rcat)) { $data[] = array_merge(array('cat_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'cat_tnt_id'	=>	$config['tnt_id'],
											'cat_id'		=>	$source
										),
					'target'		=>	array(
											'cat_tnt_id'	=>	$config['tnt_id'],
											'cat_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'cms_categories',
					'primaryKey'	=>	array('cat_tnt_id', /*'cat_wst_id',*/ 'cat_id'),
					'categoryKey'	=>	array(
											'cat_tnt_id'	=>	$config['tnt_id'],
										),
					'fieldPos'		=>	'cat_pos',
					'alias'			=>	array(
											'cat_id'		=>	'id',
											'cat_pos'		=>	'pos'
										)
				);
				break;
			}
			case DD_DLV_SERVICES : { //	Services de livraison
				require_once('delivery.inc.php');

				// Charge le service de livraison
				$rsrv = dlv_services_get( $source );
				if( !$rsrv || !ria_mysql_num_rows($rsrv) ) return false;

				// Charge les images
				$rallsrv = dlv_services_get();
				if( !$rallsrv ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallsrv) ){
					$data[] = array_merge(array('srv_tnt_id' => $config['tnt_id']), $dat);
				}
				$meta = array(
					'source' =>	array('srv_tnt_id'=>$config['tnt_id'], 'srv_id'=>$source),
					'target' =>	array('srv_tnt_id'=>$config['tnt_id'], 'srv_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'dlv_services',
					'primaryKey' =>	array('srv_tnt_id', 'srv_id'),
					'categoryKey' => array('srv_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'srv_pos',
					'alias' => array('srv_id'=>'id', 'srv_pos'=>'pos')
				);
				break;
			}
			case DD_FLD : {	// Champ avancé
				if (! isset($source['mdl'], $source['fld']) || ! is_numeric($source['fld'])) return false;
				if (! isset($target['mdl'], $target['fld']) || ! is_numeric($target['fld'])) return false;
				if ($source['mdl'] != $target['mdl'] || ! is_numeric($source['mdl'])) return false;

				$mdl = $source['mdl'];
				$source = $source['fld'];
				$target = $target['fld'];

				// Charge les champs
				$rfld = fld_models_get_fields($mdl);
				if (! $rfld) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rfld)) $data[] = array_merge(array('mf_tnt_id' => $config['tnt_id'], 'mdl_id' => $mdl), $dat);

				$meta = array(
					'source'		=>	array(
											'mf_tnt_id'	=>	$config['tnt_id'],
											'mf_mdl_id'	=>	$mdl,
											'mf_fld_id'	=>	$source
										),
					'target'		=>	array(
											'mf_tnt_id'	=>	$config['tnt_id'],
											'mf_mdl_id'	=>	$mdl,
											'mf_fld_id'	=>	$target
										),
					'data'			=>	$data,
					'tableName'		=>	'fld_model_fields',
					'primaryKey'	=>	array('mf_tnt_id', 'mf_mdl_id', 'mf_fld_id'),
					'categoryKey'	=>	array(
											'mf_tnt_id'	=>	$config['tnt_id'],
											'mf_mdl_id'	=>	$mdl
										),
					'fieldPos'		=>	'mf_fld_pos',
					'alias'			=>	array(
											'mf_mdl_id'		=>	'mdl_id',
											'mf_fld_id' 	=>	'id',
											'mf_fld_pos'	=>	'pos'
										)
				);
				break;
			}
			case DD_FLD_CATEGORY : { // Catégorie de champ avancé
				// Charge la catégorie
				$rcat = fld_categories_get($source);
				if (! ($rcat && ria_mysql_num_rows($rcat))) return false;
				if (! $cat = ria_mysql_fetch_array($rcat)) return false;

				$cls_id = $cat['cls_id'];

				// Charge les catégories
				$rcat = fld_categories_get(0, false, $cls_id);
				if (! $rcat) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rcat)) { $data[] = array_merge(array('cat_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'cat_tnt_id'	=>	$config['tnt_id'],
											'cat_id'		=>	$source
										),
					'target'		=>	array(
											'cat_tnt_id'	=>	$config['tnt_id'],
											'cat_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'fld_categories',
					'primaryKey'	=>	array('cat_tnt_id', 'cat_id'),
					'categoryKey'	=>	array(
											'cat_tnt_id'	=>	$config['tnt_id']
										),
					'fieldPos'		=>	'cat_pos',
					'alias'			=>	array(
											'cat_id'		=>	'id',
											'cat_pos'		=>	'pos'
										)
				);
				break;
			}
			case DD_FLD_MODEL : {	// modèle de saisie
				// Charge le modèle
				$rmdl = fld_models_get($source);
				if (! ($rmdl && ria_mysql_num_rows($rmdl))) return false;
				if (! $mdl = ria_mysql_fetch_array($rmdl)) return false;

				$cls_id = $mdl['cls_id'];

				// Charge les modèles
				$rmdl = fld_models_get(0, 0, $cls_id);
				if (! $rmdl) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rmdl)) { $data[] = array_merge(array('mdl_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'mdl_tnt_id'	=>	$config['tnt_id'],
											'mdl_id'		=>	$source
										),
					'target'		=>	array(
											'mdl_tnt_id'	=>	$config['tnt_id'],
											'mdl_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'fld_models',
					'primaryKey'	=>	array('mdl_tnt_id', 'mdl_id'),
					'categoryKey'	=>	array(
											'mdl_tnt_id'	=>	$config['tnt_id']
										),
					'fieldPos'		=>	'mdl_pos',
					'alias'			=>	array(
											'mdl_id'		=>	'id',
											'mdl_pos'		=>	'pos'
										)
				);
				break;
			}
			case DD_FLD_RESTRICTED_VALUE : {	// valeur restriction field
				// Charge la valeur
				$rval = fld_restricted_values_get($source);
				if (! ($rval && ria_mysql_num_rows($rval))) return false;
				if (! $val = ria_mysql_fetch_array($rval)) return false;

				$fld_id = $val['fld_id'];

				// Charge les valeurs
				$rval = fld_restricted_values_get(0, $fld_id);
				if (! $rval) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rval)) { $data[] = array_merge(array('val_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'val_tnt_id'	=>	$config['tnt_id'],
											'val_id'		=>	$source
										),
					'target'		=>	array(
											'val_tnt_id'	=>	$config['tnt_id'],
											'val_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'fld_restricted_values',
					'primaryKey'	=>	array('val_tnt_id', 'val_id'),
					'categoryKey'	=>	array(
											'val_tnt_id'	=>	$config['tnt_id'],
											'val_fld_id'	=>	$fld_id
										),
					'fieldPos'		=>	'val_pos',
					'alias'			=>	array(
											'val_id'		=>	'id',
											'val_pos'		=>	'pos'
										)
				);
				break;
			}
			case DD_ORD_RETURN_REASON : {	// raison retour
				// Charge les raisons
				$rreason = ord_returns_reasons_get();
				if (! $rreason) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rreason)) { $data[] = array_merge(array('reason_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'reason_tnt_id'	=>	$config['tnt_id'],
											'reason_id'		=>	$source
										),
					'target'		=>	array(
											'reason_tnt_id'	=>	$config['tnt_id'],
											'reason_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'ord_returns_reasons',
					'primaryKey'	=>	array('reason_tnt_id', 'reason_id'),
					'categoryKey'	=>	array(
											'reason_tnt_id'	=>	$config['tnt_id']
										),
					'fieldPos'		=>	'reason_position',
					'alias'			=>	array(
											'reason_id'			=>	'id',
											'reason_position'	=>	'position'
										)
				);
				break;
			}
			// Produits
			case DD_PRODUCT:
				if (
					(!isset($source['cat'], $source['prd']) || !is_numeric($source['prd'])) ||
					(!isset($target['cat'], $target['prd']) || !is_numeric($target['prd'])) ||
					($source['cat'] != $target['cat'] || ! is_numeric($source['cat']))
				){
					return false;
				}

				$data = array();

				$cat = $source['cat'];
				$source = $source['prd'];
				$target = $target['prd'];

				$r_products = prd_classify_get(false, 0, $cat, 0, array('cly_prd_pos' => 'asc'));

				if (! $r_products || !ria_mysql_num_rows($r_products) ){
					return false;
				}

				while( $product = ria_mysql_fetch_assoc($r_products) ){
					$data[] = array_merge(
						array('cly_tnt_id' => $config['tnt_id']),
						$product
					);
				}

				$meta = array(
					'source' => array(
						'cly_tnt_id' => $config['tnt_id'],
						'cly_cat_id' => $cat,
						'cly_prd_id' => $source,
					),
					'target' => array(
						'cly_tnt_id' => $config['tnt_id'],
						'cly_cat_id' => $cat,
						'cly_prd_id' => $target,
					),
					'data' => $data,
					'tableName' => 'prd_classify',
					'primaryKey' => array('cly_tnt_id', 'cly_cat_id', 'cly_prd_id'),
					'categoryKey' => array(
						'cly_tnt_id' => $config['tnt_id'],
						'cly_cat_id' => $cat,
					),
					'fieldPos' => 'cly_prd_pos',
					'alias' => array(
						'cly_cat_id' => 'cat',
						'cly_prd_id' => 'prd',
						'cly_prd_pos' => 'pos',
					)
				);
				break;
			case DD_TYPE_DOCUMENT : { // Type de document
				$parent = null;
				$parents = doc_types_get_parents_array( $source );
				if( is_array($parents) && sizeof($parents) ){
					$parent = $parents[0];
				}

				// Charge les documents
				// $rdocs = doc_types_get( 0, false, true, array('type_pos' => 'asc') );
				$rdocs = doc_types_get( 0, false, true, array('type_pos' => 'asc'), false, false, $parent );
				if (! $rdocs) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rdocs)) { $data[] = array_merge(array('type_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source'		=>	array(
											'type_tnt_id'	=>	$config['tnt_id'],
											'type_id'		=>	$source
										),
					'target'		=>	array(
											'type_tnt_id'	=>	$config['tnt_id'],
											'type_id'		=>	$target
										),
					'where'			=>	$where,
					'data'			=>	$data,
					'tableName'		=>	'doc_types',
					'primaryKey'	=>	array('type_tnt_id', 'type_id'),
					'categoryKey'	=>	array(
											'type_tnt_id'	=>	$config['tnt_id']
										),
					'fieldPos'		=>	'type_pos',
					'alias'			=>	array(
											'type_id'	=>	'id',
											'type_pos'	=>	'pos'
										)
				);
				break;
			}
			case DD_DOCUMENT : { //  Documents
				// Charge les documents
				$rdoc = doc_documents_get($source, 0, null, '', false);
				if( !$rdoc || !ria_mysql_num_rows($rdoc) ) return false;
				$doc = ria_mysql_fetch_array( $rdoc );

				$rdocs = doc_documents_get(0,$doc['type_id'], null, '', false);
				if (! $rdocs) return false;
				$data = array();
				while ($dat = ria_mysql_fetch_assoc($rdocs)) { $data[] = array_merge(array('doc_tnt_id' => $config['tnt_id']), $dat); }

				$meta = array(
					'source' =>	array('doc_tnt_id'=>$config['tnt_id'], 'doc_type_id'=>$doc['type_id'], 'doc_id'=>$source),
					'target' =>	array('doc_tnt_id'=>$config['tnt_id'], 'doc_type_id'=>$doc['type_id'], 'doc_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'doc_documents',
					'primaryKey' =>	array('doc_tnt_id', 'doc_type_id', 'doc_id'),
					'categoryKey' => array('doc_tnt_id'=>$config['tnt_id'], 'doc_type_id'=>$doc['type_id']),
					'fieldPos' => 'doc_pos',
					'alias' => array('doc_id'=>'id', 'doc_pos'=>'pos', 'doc_type_id'=>'type_id')
				);
				break;
			}
			case DD_SEGMENT : {	// segment
				// Charge le segment
				$rseg = seg_segments_get( $source );
				if( !$rseg || !ria_mysql_num_rows($rseg) ) return false;

				// Charge les segments
				$rseg = seg_segments_get( 0, ria_mysql_result($rseg, 0, 'cls_id') );
				if( !$rseg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rseg) )
					$data[] = array_merge(array('seg_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('seg_tnt_id'=>$config['tnt_id'], 'seg_id'=>$source),
					'target' =>	array('seg_tnt_id'=>$config['tnt_id'], 'seg_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'seg_segments',
					'primaryKey' =>	array('seg_tnt_id', 'seg_id'),
					'categoryKey' => array('seg_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'seg_pos',
					'alias' => array('seg_id'=>'id', 'seg_pos'=>'pos')
				);
				break;
			}
			case DD_PRD_IMAGE : { // images sur un produit
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				// Charge l'image
				$rimg = prd_images_get( $obj_complement, $source );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = prd_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('imo_tnt_id' => $config['tnt_id'], 'imo_cls_id' => CLS_PRODUCT), $dat);

				$meta = array(
					'source' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_PRODUCT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$source),
					'target' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_PRODUCT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'img_images_objects',
					'primaryKey' =>	array('imo_tnt_id', 'imo_cls_id', 'imo_obj_id_0', 'imo_img_id'),
					'categoryKey' => array('imo_tnt_id'=>$config['tnt_id'], 'imo_obj_id_0'=>$obj_complement),
					'fieldPos' => 'imo_pos',
					'alias' => array('imo_img_id'=>'id', 'imo_pos'=>'pos', 'imo_obj_id_0'=>'prd_id')
				);
				break;
			}
			case DD_NWS_IMAGE : { // image sur actu
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('news.inc.php');

				// Charge l'image
				$rimg = news_images_get( $obj_complement, $source );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = news_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('ni_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('ni_tnt_id'=>$config['tnt_id'], 'ni_news_id'=>$obj_complement, 'ni_img_id'=>$source),
					'target' =>	array('ni_tnt_id'=>$config['tnt_id'], 'ni_news_id'=>$obj_complement, 'ni_img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'news_img',
					'primaryKey' =>	array('ni_tnt_id', 'ni_news_id', 'ni_img_id'),
					'categoryKey' => array('ni_tnt_id'=>$config['tnt_id'], 'ni_news_id'=>$obj_complement),
					'fieldPos' => 'ni_pos',
					'alias' => array('ni_img_id'=>'id', 'ni_pos'=>'ni_pos', 'ni_news_id'=>'news_id')
				);
				break;
			}
			case DD_STR_IMAGE : { // image sur magasin
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('delivery.inc.php');

				// Charge l'image
				$rimg = dlv_stores_images_get( $obj_complement, false );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = dlv_stores_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_str_id'=>$obj_complement, 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_str_id'=>$obj_complement, 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'dlv_stores_images',
					'primaryKey' =>	array('img_tnt_id', 'img_str_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_str_id'=>$obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'img_pos', 'img_str_id'=>'str_id')
				);
				break;
			}
			case DD_CMS_IMAGE : { // image de gestion de contenu
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('cms.inc.php');

				// Charge l'image
				$rimg = cms_images_get( $obj_complement, $source );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = cms_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('ci_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('ci_tnt_id'=>$config['tnt_id'], 'ci_cat_id'=>$obj_complement, 'ci_img_id'=>$source),
					'target' =>	array('ci_tnt_id'=>$config['tnt_id'], 'ci_cat_id'=>$obj_complement, 'ci_img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'cms_img',
					'primaryKey' =>	array('ci_tnt_id', 'ci_cat_id', 'ci_img_id'),
					'categoryKey' => array('ci_tnt_id'=>$config['tnt_id'], 'ci_cat_id'=>$obj_complement),
					'fieldPos' => 'ci_pos',
					'alias' => array('ci_img_id'=>'id', 'ci_pos'=>'ci_pos', 'ci_cat_id'=>'cat_id')
				);
				break;
			}
			case DD_CAT_IMAGE : { // image de catégorie
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('cat.images.inc.php');

				// Charge l'image
				$rimg = prd_cat_images_get( $obj_complement, $source );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = prd_cat_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement, 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement, 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'prd_cat_images',
					'primaryKey' =>	array('img_tnt_id', 'img_cat_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'pos', 'img_cat_id'=>'cat_id')
				);
				break;
			}
			case DD_CTR_MODELS: { // Modèle utilisé par les comparateurs / places de marché
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('comparators.inc.php');

				// Charge l'image
				$rimg = ctr_images_get( $obj_complement, 0, $source );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = ctr_images_get( $obj_complement );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_mdl_id'=>$obj_complement, 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_mdl_id'=>$obj_complement, 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'ctr_images',
					'primaryKey' =>	array('img_tnt_id', 'img_mdl_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_mdl_id'=>$obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'pos', 'img_mdl_id'=>'mdl_id')
				);
				break;
			}
			case DD_CTR_MKT: { // Comparateur ou Place de marché
				if( !is_array($obj_complement) || sizeof($obj_complement)<=0 ){
					return false;
				}

				require_once('comparators.inc.php');

				// Charge l'image
				$rimg = ctr_catalogs_images_get( $obj_complement[0], $obj_complement[1], 0 );
				if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

				// Charge les images
				$rallimg = ctr_catalogs_images_get( $obj_complement[0], $obj_complement[1] );
				if( !$rallimg ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rallimg) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_prd_id'=>$obj_complement[0], 'img_ctr_id'=>$obj_complement[1], 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_prd_id'=>$obj_complement[0], 'img_ctr_id'=>$obj_complement[1], 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'ctr_catalogs_images',
					'primaryKey' =>	array('img_tnt_id', 'img_prd_id', 'img_ctr_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_prd_id'=>$obj_complement[0], 'img_ctr_id'=>$obj_complement[1]),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'pos', 'img_prd_id' => 'prd_id', 'img_ctr_id'=>'ctr_id')
				);
				break;
			}
			case DD_MODEL: { // Modèle de listes
				$rmodels = ord_orders_get_with_adresses(0, 0, _STATE_MODEL);

				$data = array();
				while( $dat = ria_mysql_fetch_assoc($rmodels) ){
					$data[] = array_merge(array('omd_tnt_id' => $config['tnt_id']), $dat);

					ord_orders_set_date_modified($dat['id']);
				}

				$meta = array(
					'source' => array('omd_tnt_id' => $config['tnt_id'], 'omd_ord_id' => $source),
					'target' => array('omd_tnt_id' => $config['tnt_id'], 'omd_ord_id' => $target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'ord_models_position',
					'primaryKey' => array('omd_tnt_id', 'omd_ord_id'),
					'categoryKey' => array('omd_tnt_id' => $config['tnt_id'], 'omd_ord_id' => $obj_complement),
					'fieldPos' => 'omd_pos',
					'alias' => array('omd_pos' => 'pos', 'omd_ord_id' => 'id'),
				);
				break;
			}
			case DD_MODEL_PRODUCTS: {

				$source = explode('-', $source);
				$target = explode('-', $target);

				if( sizeof($source) != 3 || sizeof($target) != 3 ){
					return false;
				}

				$rproducts = ord_products_get( $source[0], array('pos' => 'asc') );

				$data = array();
				while( $dat = ria_mysql_fetch_assoc($rproducts) ){
					$data[] = array_merge( array('omp_tnt_id' => $config['tnt_id']), $dat );
				}

				$meta = array(
					'source' => array('omp_tnt_id' => $config['tnt_id'], 'omp_ord_id' => $source[0], 'omp_prd_id' => $source[1], 'omp_line_id' => $source[2]),
					'target' => array('omp_tnt_id' => $config['tnt_id'], 'omp_ord_id' => $target[0], 'omp_prd_id' => $target[1], 'omp_line_id' => $target[2]),
					'where' => $where,
					'data' => $data,
					'tableName' => 'ord_models_products_position',
					'primaryKey' => array('omp_tnt_id', 'omp_ord_id', 'omp_prd_id', 'omp_line_id'),
					'categoryKey' => array('omp_tnt_id' => $config['tnt_id'], 'omp_ord_id' => $obj_complement),
					'fieldPos' => 'omp_pos',
					'alias' => array('omp_pos' => 'pos', 'omp_ord_id' => 'ord_id', 'omp_prd_id' => 'id', 'omp_line_id' => 'line'),
					'request' => 'insert'
				);
				break;
			}
			case DD_PRODUCT_RELATED: {

				if( !isset($obj_complement['rel']) ){
					$rrelations = prd_products_get_simple( 0, '', false, 0, false, false, false, array('child_pos'=>'asc'), array('childs' => true, 'parent' => $obj_complement['prd']) );

					$data = array();
					while( $dat = ria_mysql_fetch_assoc($rrelations) ){
						$dat['prd_parent_id'] = $obj_complement['prd'];
						$data[] = array_merge(array('tnt_id' => $config['tnt_id']), $dat);
					}

					$meta = array(
						'source' => array('prd_tnt_id' => $config['tnt_id'], 'prd_parent_id' => $obj_complement['prd'], 'prd_child_id' => $source),
						'target' => array('prd_tnt_id' => $config['tnt_id'], 'prd_parent_id' => $obj_complement['prd'], 'prd_child_id' => $target),
						'where' => $where,
						'data' => $data,
						'tableName' => 'prd_hierarchy',
						'primaryKey' => array('prd_tnt_id', 'prd_parent_id', 'prd_child_id'),
						'categoryKey' => array('prd_tnt_id' => $config['tnt_id'], 'prd_parent_id' => $obj_complement['prd']),
						'fieldPos' => 'prd_child_pos',
						'alias' => array('prd_child_pos' => 'child_pos', 'prd_tnt_id' => 'tnt_id', 'prd_child_id' => 'id', 'prd_parent_id' => 'prd_parent_id')
					);

				}else{
					$type_id = explode('-', $obj_complement['rel']);
					$type_id = $type_id[sizeof($type_id) - 1];

					$rrelations = prd_relations_get( $obj_complement['prd'], null, $obj_complement['rel'], false, 0, false, array('pos' => 'asc') );

					$data = array();
					while( $dat = ria_mysql_fetch_assoc($rrelations) ){
						$data[] = array_merge(array('tnt_id' => $config['tnt_id']), $dat);
					}

					$meta = array(
						'source' => array('rel_tnt_id' => $config['tnt_id'], 'rel_src_id' => $obj_complement['prd'], 'rel_dst_id' => $source, 'rel_type_id' => $type_id),
						'target' => array('rel_tnt_id' => $config['tnt_id'], 'rel_src_id' => $obj_complement['prd'], 'rel_dst_id' => $target, 'rel_type_id' => $type_id),
						'where' => $where,
						'data' => $data,
						'tableName' => 'prd_relations',
						'primaryKey' => array('rel_tnt_id', 'rel_src_id', 'rel_dst_id', 'rel_type_id'),
						'categoryKey' => array('rel_tnt_id' => $config['tnt_id'], 'rel_type_id' => $type_id),
						'fieldPos' => 'rel_pos',
						'alias' => array('rel_tnt_id'=> 'tnt_id', 'rel_src_id' => 'src_id', 'rel_type_id' => 'type_id', 'rel_dst_id' => 'dst_id', 'rel_pos' => 'pos')
					);
				}
				break;
			}
			case DD_BRAND : { // marques

				// Charge la marque
				$rbrd = prd_brands_get( $source );
				if( !$rbrd || !ria_mysql_num_rows($rbrd) ) return false;

				// Charge les marques
				$rbrd = prd_brands_get();
				if( !$rbrd ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rbrd) )
					$data[] = array_merge(array('brd_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('brd_tnt_id'=>$config['tnt_id'], 'brd_id'=>$source),
					'target' =>	array('brd_tnt_id'=>$config['tnt_id'], 'brd_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'prd_brands',
					'primaryKey' =>	array('brd_tnt_id', 'brd_id'),
					'categoryKey' => array('brd_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'brd_pos',
					'alias' => array('brd_id'=>'id', 'brd_pos'=>'pos')
				);
				break;
			}
			case DD_FLD_POS : { // Champs avancés d'une classe
				if( !isset($source) || !is_numeric($source) ) return false;
				if( !isset($target) || !is_numeric($target) ) return false;
				if( !isset($obj_complement) || !is_numeric($obj_complement) || !$obj_complement ) return false;

				$cls_id = $obj_complement;

				// Charge les champs
				$rfld = fld_classes_fields_priority_get( $cls_id );
				if( !$rfld ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_assoc($rfld) ){
					$data[] = array_merge(array('cfp_tnt_id' => $config['tnt_id'], 'cfp_cls_id' => $cls_id), $dat);
				}

				$meta = array(
					'source'		=>	array(
											'cfp_tnt_id'	=>	$config['tnt_id'],
											'cfp_cls_id'	=>	$cls_id,
											'cfp_fld_id'	=>	$source
										),
					'target'		=>	array(
											'cfp_tnt_id'	=>	$config['tnt_id'],
											'cfp_cls_id'	=>	$cls_id,
											'cfp_fld_id'	=>	$target
										),
					'data'			=>	$data,
					'tableName'		=>	'fld_classes_fields_priority',
					'primaryKey'	=>	array('cfp_tnt_id', 'cfp_cls_id', 'cfp_fld_id'),
					'categoryKey'	=>	array(
											'cfp_tnt_id'	=>	$config['tnt_id'],
											'cfp_cls_id'	=>	$cls_id
										),
					'fieldPos'		=>	'cfp_pos',
					'alias'			=>	array(
											'cfp_cls_id'	=>	'cls_id',
											'cfp_fld_id' 	=>	'fld_id',
											'cfp_pos'	=>	'pos'
										)
				);
				break;
			}
			case DD_FAQ_CATEGORY : {	// Catégories faq
				// Charge la catégorie
				$rcat = faq_categories_get($source);
				if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;

				// Charge les catégories du même parent
				$rcat = faq_categories_get();
				if( !$rcat ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rcat) )
					$data[] = array_merge(array('cat_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' => array('cat_tnt_id'=>$config['tnt_id'], 'cat_id'=>$source),
					'target' => array('cat_tnt_id'=>$config['tnt_id'], 'cat_id'=>$target),
					'data' => $data,
					'tableName' => 'faq_categories',
					'primaryKey' => array('cat_tnt_id', 'cat_id'),
					'categoryKey' => array('cat_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'cat_pos',
					'alias' => array('cat_id'=>'id', 'cat_pos'=>'pos')
				);
				break;
			}
			case DD_FAQ_QUESTIONS : {	// Questions faq
				// Charge la question
				$rcat = faq_questions_get($source);
				if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;

				// Récupération du cat_id de la question, afin de ne récupérer que les questions de cette catégorie
				$array_quest = ria_mysql_fetch_array($rcat);
				$category_id = $array_quest['cat_id'];

				// Charge les questions de la catégorie
				$rcat = faq_questions_get(0, $category_id);
				if( !$rcat ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($rcat) )
					$data[] = array_merge(array('qst_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' => array('qst_tnt_id'=>$config['tnt_id'], 'qst_id'=>$source),
					'target' => array('qst_tnt_id'=>$config['tnt_id'], 'qst_id'=>$target),
					'data' => $data,
					'tableName' => 'faq_questions',
					'primaryKey' => array('qst_tnt_id', 'qst_id'),
					'categoryKey' => array('qst_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'qst_pos',
					'alias' => array('qst_id'=>'id', 'qst_pos'=>'pos')
				);
				break;
			}
			case DD_BRD_PRODUCTS : {	// Produits pour les marques
				if (! (isset($source['brd']) && isset($source['prd'])) || ! is_numeric($source['prd'])) return false;
				if (! (isset($target['brd']) && isset($target['prd'])) || ! is_numeric($target['prd'])) return false;

				if ($source['brd'] != $target['brd']) return false;

				$brd_id = $source['brd'];
				$source = $source['prd'];
				$target = $target['prd'];

				// Charge les produits de la marque
				$r_brd_products = ria_mysql_query('
					select prd_id as id, prd_brd_pos as brd_pos
					from prd_products
					where prd_tnt_id = '.$config['tnt_id'].'
						and prd_brd_id = '.$brd_id.'
						and prd_date_deleted is null
					order by prd_brd_pos asc
				');

				if( !$r_brd_products ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_assoc($r_brd_products) ) {
					$data[] = array_merge(array('prd_tnt_id' => $config['tnt_id']), $dat);
				}

				$meta = array(
					'source' => array('prd_tnt_id'=>$config['tnt_id'], 'prd_id'=>$source),
					'target' => array('prd_tnt_id'=>$config['tnt_id'], 'prd_id'=>$target),
					'data' => $data,
					'tableName' => 'prd_products',
					'primaryKey' => array('prd_tnt_id', 'prd_id'),
					'categoryKey' => array('prd_tnt_id'=>$config['tnt_id']),
					'fieldPos' => 'prd_brd_pos',
					'alias' => array('prd_id'=>'id', 'prd_brd_pos'=>'brd_pos')
				);
				break;
			}
			case DD_FAQ_CAT_IMAGE : {	// Images Questions faq
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('faq.images.inc.php');

				// Charge l'image
				$r_img = faq_cat_images_get( $obj_complement, $source );
				if( !$r_img || !ria_mysql_num_rows($r_img) ) return false;

				// Charge les images
				$r_all_img = faq_cat_images_get( $obj_complement );
				if( !$r_all_img ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($r_all_img) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement, 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement, 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'faq_cat_images',
					'primaryKey' =>	array('img_tnt_id', 'img_cat_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_cat_id'=>$obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'pos', 'img_cat_id'=>'cat_id')
				);
				break;
			}
			case DD_FAQ_QST_IMAGE : {	// Images Questions faq
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('faq.images.inc.php');

				// Charge l'image
				$r_img = faq_qst_images_get( $obj_complement, $source );
				if( !$r_img || !ria_mysql_num_rows($r_img) ) return false;

				// Charge les images
				$r_all_img = faq_qst_images_get( $obj_complement );
				if( !$r_all_img ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($r_all_img) )
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $dat);

				$meta = array(
					'source' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_qst_id'=>$obj_complement, 'img_id'=>$source),
					'target' =>	array('img_tnt_id'=>$config['tnt_id'], 'img_qst_id'=>$obj_complement, 'img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'faq_qst_images',
					'primaryKey' =>	array('img_tnt_id', 'img_qst_id', 'img_id'),
					'categoryKey' => array('img_tnt_id'=>$config['tnt_id'], 'img_qst_id'=>$obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id'=>'id', 'img_pos'=>'pos', 'img_qst_id'=>'qst_id')
				);
				break;
			}
			case DD_ORD_PRODUCTS : {	// Produits pour les commandes
				if (!isset($source['ord'], $source['prd'], $source['line']) || !is_numeric($source['prd']) || !is_numeric($source['line'])) return false;
				if (!isset($target['ord'], $target['prd'], $target['line']) || !is_numeric($target['prd']) || !is_numeric($target['line'])) return false;

				if ($source['ord'] != $target['ord']) return false;

				$ord_id = $source['ord'];
				$source_id = $source['prd'];
				$target_id = $target['prd'];
				$source_line = $source['line'];
				$target_line = $target['line'];

				// Charge les produits de la commande
				$r_ord_products = ord_products_get($ord_id, array('line_pos' => 'asc'));

				if( !$r_ord_products ) return false;

				$not_sorted = false;
				while( $prd = ria_mysql_fetch_assoc($r_ord_products) ) {
					if ($prd['prd_pos'] == null){
						$not_sorted = true;
					}
				}

				ria_mysql_data_seek($r_ord_products, 0);

				$data = array();
				$pos = 0;
				while( $dat = ria_mysql_fetch_assoc($r_ord_products) ) {
					if ($not_sorted){
						$sql_update_pos = '
							update ord_products
							set prd_pos = '.$pos.'
							where prd_tnt_id = '.$config['tnt_id'].'
							and prd_ord_id = '.$ord_id.'
							and prd_id = '.$dat['id'].'
							and prd_line_id = '.$dat['line'].'
						';

						ria_mysql_query($sql_update_pos);

						$dat['prd_pos'] = $pos;
						$pos++;
					}
					$data[] = array_merge(array('prd_tnt_id' => $config['tnt_id']), $dat);
				}


				$meta = array(
					'source' => array('prd_tnt_id'=>$config['tnt_id'], 'prd_ord_id' => $ord_id, 'prd_id' => $source_id, 'prd_line_id' => $source_line),
					'target' => array('prd_tnt_id'=>$config['tnt_id'], 'prd_ord_id' => $ord_id, 'prd_id' => $target_id, 'prd_line_id' => $target_line),
					'data' => $data,
					'tableName' => 'ord_products',
					'primaryKey' => array('prd_tnt_id', 'prd_ord_id', 'prd_id', 'prd_line_id'),
					'primaryCouple' => array('prd_id', 'prd_line_id'),
					'categoryKey' => array('prd_tnt_id'=>$config['tnt_id'], 'prd_ord_id' => $ord_id),
					'fieldPos' => 'prd_pos',
					'alias' => array('prd_id'=>'id', 'prd_line_id' => 'line', 'prd_ord_id'=>'ord_id')
				);

				break;
			}
			case DD_STORE:
				$data = array();
				$not_sorted = false;

				$result = dlv_stores_images_get($obj_complement);

				while($image = ria_mysql_fetch_array($result)) {
					$data[] = array_merge(array('img_tnt_id' => $config['tnt_id']), $image);

					if ( is_null($image['pos']) ){
						$not_sorted = true;
					}
				}

				foreach ($data as $i => &$image) {
					if ($not_sorted) {
						ria_mysql_query('
							update dlv_stores_images
							set img_pos = ' . $i . '
							where img_tnt_id = ' . $config['tnt_id'] . ' and img_id = ' . $image['id'] . '
						');

						$image['pos'] = $i;
					}
				}

				$meta = array(
					'source' => array('img_tnt_id' => $config['tnt_id'], 'img_str_id' => $obj_complement, 'img_id' => $source),
					'target' => array('img_tnt_id' => $config['tnt_id'], 'img_str_id' => $obj_complement, 'img_id' => $target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'dlv_stores_images',
					'primaryKey' => array('img_tnt_id', 'img_str_id', 'img_id'),
					'categoryKey' => array('img_tnt_id' => $config['tnt_id'], 'img_str_id' => $obj_complement),
					'fieldPos' => 'img_pos',
					'alias' => array('img_id' => 'id', 'img_pos' => 'pos', 'img_str_id' => 'str_id')
				);

				break;
			case DD_DOC_IMAGE : {	// Images Documents
				if( !is_numeric($obj_complement) || $obj_complement<=0 ){
					return false;
				}

				require_once('doc.images.inc.php');

				// Charge l'image
				$r_img = doc_images_get( $obj_complement, $source );
				if( !$r_img || !ria_mysql_num_rows($r_img) ){
					return false;
				}

				// Charge les images
				$r_all_img = doc_images_get( $obj_complement );
				if( !$r_all_img ){
					return false;
				}

				$data = array();
				while( $dat = ria_mysql_fetch_array($r_all_img) ){
					$data[] = array_merge(array('imo_tnt_id' => $config['tnt_id'], 'imo_cls_id' => CLS_DOCUMENT), $dat);
				}

				$meta = array(
					'source' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_DOCUMENT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$source),
					'target' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_DOCUMENT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'img_images_objects',
					'primaryKey' =>	array('imo_tnt_id', 'imo_cls_id', 'imo_obj_id_0', 'imo_img_id'),
					'categoryKey' => array('imo_tnt_id'=>$config['tnt_id'], 'imo_obj_id_0'=>$obj_complement),
					'fieldPos' => 'imo_pos',
					'alias' => array('imo_img_id'=>'id', 'imo_pos'=>'pos', 'imo_obj_id_0'=>'doc_id')
				);
				break;
			}
			case DD_DOC_TYPE_IMAGE : {	// Images Documents
				if( !is_numeric($obj_complement) || $obj_complement<=0 ) return false;

				require_once('doc.images.inc.php');

				// Charge l'image
				$r_img = doc_types_images_get( $obj_complement, $source );
				if( !$r_img || !ria_mysql_num_rows($r_img) ) return false;

				// Charge les images
				$r_all_img = doc_types_images_get( $obj_complement );
				if( !$r_all_img ) return false;

				$data = array();
				while( $dat = ria_mysql_fetch_array($r_all_img) )
					$data[] = array_merge(array('imo_tnt_id' => $config['tnt_id'], 'imo_cls_id' => CLS_TYPE_DOCUMENT), $dat);

				$meta = array(
					'source' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_obj_id_0'=>$obj_complement, 'imo_id'=>$source),
					'source' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_TYPE_DOCUMENT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$source),
					'target' =>	array('imo_tnt_id'=>$config['tnt_id'], 'imo_cls_id' => CLS_TYPE_DOCUMENT, 'imo_obj_id_0'=>$obj_complement, 'imo_img_id'=>$target),
					'where' => $where,
					'data' => $data,
					'tableName' => 'img_images_objects',
					'primaryKey' =>	array('imo_tnt_id', 'imo_cls_id', 'imo_obj_id_0', 'imo_img_id'),
					'categoryKey' => array('imo_tnt_id'=>$config['tnt_id'], 'imo_obj_id_0'=>$obj_complement),
					'fieldPos' => 'imo_pos',
					'alias' => array('imo_img_id'=>'id', 'imo_pos'=>'pos', 'imo_obj_id_0'=>'type_id')
				);
				break;
			}
			default :
				return false;
		}

		$meta['alias'] = isset($meta['alias']) ? $meta['alias'] : array();
		$meta['aliasFieldPos'] = isset($meta['alias'][ $meta['fieldPos'] ]) ? $meta['alias'][ $meta['fieldPos'] ] : $meta['fieldPos'];

		// Modification de la position (algo générique, se base sur meta)
		if( $upd_pos ){
			$val_null = false;
			$all = array();

			$t = 0;
			foreach( $data as $d ){
				$position = $d[isset($meta['alias'][$meta['fieldPos']]) ? $meta['alias'][$meta['fieldPos']] : $meta['fieldPos']];

				if( !is_numeric($position) || $position <= 0 ){
					$val_null = true;
					$position = $t;
				}

				$all[$t] = array(
					'key' => array(),
					'pos' => $position,
				);

				foreach( $meta['primaryKey'] as $key ){
					$all[$t]['key'][$key] = $d[isset($meta['alias'][$key]) ? $meta['alias'][$key] : $key];
				}

				$t++;
			}

			if( $val_null ){
				$all = array_msort($all, array('pos' => SORT_ASC));
				$p = 1;

				foreach( $all as $obj ){
					if( !isset($meta['request']) || $meta['request'] == 'update' ){
						$sql = '
							update '.$meta['tableName'].'
							set '.$meta['fieldPos'].'='.$p.'
							where
						';

						$c = 0;
						foreach( $obj['key'] as $col=>$val ){
							if( $c>0 ){
								$sql .= ' and ';
							}

							$sql .= $col.'='.$val;
							$c++;
						}
					}else{
						$columns = array($meta['fieldPos'] => $obj['pos']);

						foreach( $obj['key'] as $col => $val ){
							$columns[$col] = $val;
						}

						$sql = '
							insert into '.$meta['tableName'].'
								('.implode(',', array_keys($columns)).')
							values
								('.implode(',', array_values($columns)).')
						';
					}

					ria_mysql_query($sql);
					$p++;
				}

				return obj_position_update($p_cls, $p_source, $p_target, $p_where, $p_obj_complement, false);
			}
		}

		/* 	Récupère l'indice de la source (a) et de la cible (b) dans le jeu de données
		 *	Peuple également l'index $pos (position => enregistrement)
		 */
		$pos = array();
		$i = 0;

		foreach( $meta['data'] as $dat ){
			if( obj_position_is_equal($dat, $meta, 'source' ) ) $a = $i;
			if( obj_position_is_equal($dat, $meta, 'target' ) ) $b = $i;
			$pos[$i] = $dat;
			$i++;
		}

		// Si source ou cible non trouvée, erreur
		if( !isset($a, $b) ) return false;

		// Ajustement selon déplacement after/before
		if( $a < $b && $where === 'before' ) $b--;
		elseif( $a > $b && $where === 'after' ) $b++;

		// Décale les positions ...
		if( $a < $b ){
			$t = $pos[$b][ $meta['aliasFieldPos'] ];
			for( $i=$a+1; $i<=$b; $i++ )
				$pos[$i]['__newPos'] = $pos[$i-1][ $meta['aliasFieldPos'] ];
			$pos[$a]['__newPos'] = $t;
			for( $i=$a; $i<=$b; $i++ ){
				if( !obj_position_set_pos( $pos[$i], $meta ) )
					return false;
			}

			// Si la source a la même position que target, on incrémente toutes les positions supérieures ou égales sauf la cible
			if( $pos[$a]['__newPos'] == $pos[$b]['__newPos'] ){
				obj_position_move_up( $b, $pos, $meta );
			}
		}else{
			$t = $pos[$b][ $meta['aliasFieldPos'] ];
			for( $i=$a-1; $i>=$b; $i-- )
				$pos[$i]['__newPos'] = $pos[$i+1][ $meta['aliasFieldPos'] ];
			$pos[$a]['__newPos'] = $t;
			for( $i=$a; $i>=$b; $i-- ){
				if( !obj_position_set_pos( $pos[$i], $meta ) )
					return false;
			}

			// Si la source a la même position que target, on incrémente toutes les positions supérieures ou égales sauf la source
			if( $pos[$a]['__newPos'] == $pos[$b]['__newPos'] ){
				obj_position_move_up( $a, $pos, $meta );
			}
		}


		if( $cls == DD_MODEL_PRODUCTS && isset($source[0]) ){
			ord_orders_set_date_modified($source[0]);
		}

		return true;
	}

	/**	Cette fonction met à jour la position d'un objet
	 *	@param $pos
	 *	@param $meta
	 *
	 *	@return bool true en cas de succès ou si rien ne doit être fait, False en cas d'échec
	 */
	function obj_position_set_pos( $pos, $meta ){

		if( $pos[$meta['aliasFieldPos']] == $pos['__newPos'] ){
			return true;
		}

		$where = array();
		foreach( $meta['primaryKey'] as $key ){
			$a = isset($meta['alias'][$key]) ? $meta['alias'][$key] : $key;
			$where[] = $key.' = "'.addslashes($pos[$a]).'"';
		}

		$sql = '
			update '.$meta['tableName'].'
			set '.$meta['fieldPos'].' = '.( is_numeric($pos['__newPos']) ? $pos['__newPos'] : 0 ).'
			where '.implode(' and ', $where).'
		';

		$r = ria_mysql_query($sql);

		if( ria_mysql_errno() ){
			error_log( mysql_error().' - '.$sql );
			return false;
		}

		return $r;
	}

	/**	Cette fonction remonte toutes les positions supérieures ou égales excepté l'enregistrement
	 *	Si la source a la même position que la cible, force à différencier les positions ...
	 *	@param int $i Indice de l'élément de référence
	 *	@param int $pos Liste de tous les éléments
	 *	@param array $meta Tableau des métadonnées de la classe
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function obj_position_move_up( $i, $pos, $meta ){

		// colonnes communes
		$where = array();
		foreach( $meta['categoryKey'] as $key=>$value ){
			$where[] = ($value !== null) ? $key.' = "'.addslashes($value).'"' : ' is null';
		}

		// colonne position
		$where[] = $meta['fieldPos'].' >= '.$pos[$i]['__newPos'];

		// colonne différenciel
		$condition = array();
		foreach( $meta['primaryKey'] as $key=>$value ){
			if( !array_key_exists($value, $meta['categoryKey']) ){
				if (isset($meta['primaryCouple']) && in_array($value, $meta['primaryCouple'])){
					$t = isset($meta['alias'][$value]) ? $meta['alias'][$value] : $value;
					$condition[] = $value.' <> "'.addslashes($pos[$i][$t]).'"';
				} else {
					$t = isset($meta['alias'][$value]) ? $meta['alias'][$value] : $value;
					$where[] = $value.' <> "'.addslashes($pos[$i][$t]).'"';
				}
			}
		}

		if (sizeof($condition)){
			$where[] = '('.implode(' or ', $condition).')';
		}

		return ria_mysql_query('
			update '.$meta['tableName'].'
			set '.$meta['fieldPos'].' = ('.$meta['fieldPos'].' + 1)
			where '.implode(' and ', $where).'
		');

	}

	/** Teste l'égalité entre 2 enregistrements
	 *	@param $current Enregistrelment courant
	 *	@param array $meta Tableau des métadonnées
	 *	@param $col Libellé de la colonne de comparaison dans le tableau $meta
	 *
	 *	@return bool True si les positions sont égales, False sinon
	 */
	function obj_position_is_equal( $current, $meta, $col ){
		foreach( $meta['primaryKey'] as $key ){
			$t = (isset($meta['alias'][$key])) ? $meta['alias'][$key] : $key;
			if( $current[$t] != $meta[$col][$key] ) {
				return false;
			}
		}

		return true;
	}

/// @}
<?php
/**	\defgroup kwanko <PERSON>wanko
 * 	\ingroup affiliation
 *	Ce module comprend les classes et fonctions nécessaires à l'interfaçage avec la plateforme d'affiliation Kwanko
 *	@{
 */

require_once('rewrite.inc.php');
require_once('orders.inc.php');

/**	\class Kwanko
 * \brief Kwanko permet de gérer les campagnes d'affiliation avec Kwanko.
 *
 *	Kwanko est utilisé en parallèle de l'export catalogue NetAffiliation (TODO : Séprer les deux exports serait une bonne idée).
 *
 *	Variables de config utilisées et obligatoires :
 *		- netaffiliation_is_active 	: si oui ou non netaffiliation est activé
 *      - netaffiliation_for_kwanko : si l'export du catalogue est réalisé pour Kwanko et non pour NetAffiliation
 *
 *  Initialisation :
 *  \code{.php}
 *      require_once 'Kwanko.inc.php';
 *      $Kwanko = new Kwanko('url fournie par la documentation de kwanko');
 *  \endcode
 *  Affichage du tag dans la balise \<head\> du site
 *  \code{.php}
 *      <head>
 *          ...
 *          <?php echo $Kwanko->getTrackingTag();?>
 *          ...
 *      </head>
 *  \endcode
 *  Paramétrage des tags :
 *
 *  Ajout du tag de listing de catégorie
 *  \code{.php}
 *      $kwanko_prd = array_slice($ar_products, 0, 3);
 *		$kwanko_prd = array_map(function($prd){
 *			return $prd['id'];
 *		}, $kwanko_prd);
 *		$Kwanko->useListingTag($category_id, $kwanko_prd);
 *  \endcode
 *  Ajout du tag de product sur la fiche produit
 *  \code{.php}
 *     $Kwanko->useProductTag($category_id, $product_id);
 *  \endcode
 *  Ajout du tag sur le formulaire de création de compte
 *  \code{.php}
 *     $Kwanko->useLeadInscription();
 *  \endcode
 *  Ajout du tag de création de compte, souvent lié a l'évennement analytics 'create-user'
 *  \code{.php}
 *  	if (isset($_SESSION['analytics']) && $_SESSION['analytics'] == 'create-user') {
 *			$Kwanko->setEvent('create-user');
 *		}
 *  \endcode
 *  Ajout du tag basket, sur la page du panier
 *  \code{.php}
 *     $Kwanko->useBasketTag($_SESSION['ord_id]);
 *  \endcode
 *  Ajout du tag de transaction, sur la page de confirmation de commande
 *  \code{.php}
 *     $Kwanko->useTransactionTag($_SESSION['ord_id]);
 *  \endcode
 *
 *  Ajout de la balise de remarketing sur la page de confirmation de commande entre les balises \<body\>...\</body\>
 *  \code{.php}
 *     $Kwanko->tagOrder("paramètre mclic de l'url");
 *  \endcode
 *  Si il faut faire la différence entre un nouvel utilisateur ou un utilisateur exitant ajouter en deuxième paramètre le mcli pour le nouvel utilisateur
 *  \code{.php}
 *     $Kwanko->tagOrder("paramètre mclic de l'url", "paramètre mclic de l'url pour le nouvel utilisateur");
 *  \endcode
 *
 *  Autre exemple d'utilisation
 *  Exemple : Deprecated, sur la page d'une catégorie, permet d'enregistre l'identifiant de la catégorie et des trois premier produit
 *  \code{.php}
 * 	$kwanko_category_id = $category['id'];
 *	$kwanko_product_id = array();
 *  	if ($nb_prds) {
 *  		$i = 0;
 *  		while ($product = ria_mysql_fetch_assoc($r_product)) {
 *  			if (($i++)>=3) {
 *  				break;
 *  			}
 *
 *  			$kwanko_product_id[] = $product['id'];
 *  		}
 *
 *  		ria_mysql_data_seek($r_product, 0);
 *  	}
 *  \endcode
 *
 *
 *  Exemple : Deprecated, sur la page d'un produit, permet d'enregistre l'identifiant de la catégorie et du produit
 *  \code{.php}
 *	    $kwanko_category_id = $category['id'];
 *	    $kwanko_product_id = $product['id'];
 *  \endcode
 *	Exemple : deprecated, dans la balise \<head\> du site, permet de déclencher les différents tags
 *	\code{.php}
 *		{ // Gestion de l'affiliation Kwanko
 *			require_once('Kwanko.inc.php');
 *			$kwanko = new Kwanko('https://img.metaffiliation.com/u/35/p65471.js');
 *
 *			if (isset($kwanko_category_id)) {
 *				$kwanko->setCategoryID($kwanko_category_id);
 *			}
 *
 *			if (isset($kwanko_product_id)) {
 *				$kwanko->setProductID($kwanko_product_id);
 *			}
 *
 *			// enregistre la création de compte
 *			if (isset($_SESSION['analytics']) && $_SESSION['analytics'] == 'create-user') {
 *				$kwanko->setEvent('create-user');
 *			}
 *
 *			// enregistre l'identifiant de la commande une fois le paiement fait pour le tag de complétion de commande
 *			if( isset( $_SESSION['analytics'] ) && $_SESSION['analytics'] == 'commande' && isset($order) ){
 *				$kwanko->setOrderCompleteID($order['id']);
 *			}
 *
 *			print $kwanko->getTrackingTag();
 *		}
 *	\endcode
 */
class Kwanko {
    private $url = '';
    private $site_type = 'd';
    private $event = '';

    private $user = array(
        'id'    => '',
        'email' => '',
        'md5'   => ''
    );
    private $transaction_with_ecotaxe = true;
    private $ord_transaction = 0;
    private $k_category = array();
    private $k_product = array();

	private $currency = 'EUR';

	private $zone = null;

	private $other_params = array();

	private $customer_id = '';
    private $m_md5 = '';

    private $event_types = array('create-user');
    /** Cette fonction permet d'initialisé la classe Kwanko
     *  @param string $url Obligatoire, url du site
     *  @param string $type Facultatif, type de site : (m : version mobile, t : version tablette, d : version classique - par défaut)
	 * 	@param string $currency Facultatif, devise du site (ISO 4217). Valeur par défaut : EUR
     */
    public function __construct($url, $type='d', $currency='EUR') {
        global $config;

        $this->url       = strtolower($url);
        $this->site_type    = $type;
        $this->currency     = $currency;

        if (isset($config['user']) && ria_array_key_exists(array('id', 'email'), $config['user'])) {
			$this->customer_id = $config['user']['id'];
			$this->m_md5 = md5($config['user']['email']);
        }
    }
    /**
     * Cette fonction permet de définir lors de l'ajout du tag de transaction, le total ht de la commande moins l'écotaxe
     *
     * @return Kwanko l'instance
     */
    public function setTransactionWithoutEcotaxe()
    {
        $this->transaction_with_ecotaxe = false;

        return $this;
    }
    /**
     * Cette fonction permet de définir lors de l'ajout du tag de transaction, le total ht de la commande avec l'écotaxe
     *
     * @return Kwanko l'instance
     */
    public function setTransactionWithEcotaxe()
    {
        $this->transaction_with_ecotaxe = true;

        return $this;
    }
    /** Cette fonction permet de définir le trackeur de la home page
     *
     * @return Kwanko l'instance
     */
	public function useHomepageTag()
	{
		$this->zone = 'homepage';

		return $this;
	}
    /** Cette fonction permet de définir le trackeur de la page produit
     *
     * @param int $product_id Identifiant du produit en consultation
     * @param int $category_id Identifiant de la catégorie en consultation
     * @return Kwanko l'instance
     */
	public function useProductTag($product_id, $category_id)
	{
		$this->zone = 'product';
		$this->other_params['productId'] = $product_id;
		$this->other_params['categoryId'] = $category_id;

		return $this;
	}
    /** Cette fonction permet de définir le tag de listing produit
     *
     * @param int $category_id Identifiant de la catégorie
     * @param array $product_id Tableau d'identifiant de produit
     * @return Kwanko l'instance
     */
	public function useListingTag($category_id,array $product_id)
	{
		$this->zone = 'listing';
		$this->other_params['products'] = $product_id;
		$this->other_params['categoryId'] = $category_id;

		return $this;
	}
    /** Cette fonction permet de mettre en place le tag de tracking de panier
     *
     * @param integer $order_id Facultatif Identifiant du panier généralement $_SESSION['ord_id']
     * @return Kwanko l'instance
     */
	public function useBasketTag($order_id=0)
	{
		global $config;
		$ar_prds = array();

        if (is_numeric($order_id) && $order_id > 0) {
            $exclude = '';
            if (isset($config['dlv_prd_references']) && is_array($config['dlv_prd_references']) && $config['dlv_prd_references']) {
                $exclude .= ' and prd_ref not in ("'.implode('", "', $config['dlv_prd_references']).'")';
            }

            $r_ord_prd = ria_mysql_query('
                select prd_id as id, (prd_price_ht * prd_tva_rate) as price_ttc, prd_qte as qte
                from ord_products
                where prd_tnt_id = '.$config['tnt_id'].'
                    and prd_ord_id = '.$order_id.'
                    and ifnull(prd_parent_id, 0) = 0
                    '.$exclude.'
            ');

            if ($r_ord_prd) {
                while ($ord_prd = ria_mysql_fetch_assoc($r_ord_prd)) {
                    $ar_prds[] = array(
                        'id'        => $ord_prd['id'],
                        'price'     => number_format($ord_prd['price_ttc'], 2, '.', ''),
                        'quantity'  => round($ord_prd['qte'], 3),
                    );
                }
            }
        }
		$this->zone = 'basket';
		$this->other_params['products'] = $ar_prds;
		$this->other_params['currency'] = $this->currency;

		return $this;
	}
    /** Cette fonction permet de définir le trackeur de transaction de commande
     *
     * @param int $order_id Identifiant de la commande
     * @return Kwanko l'instance
     */
	public function useTransactionTag($order_id)
	{
		$this->useBasketTag($order_id);
		$this->zone = 'transaction';
        $this->other_params['transactionId'] = $order_id;
        $this->ord_transaction = $order_id;

		return $this;
    }
    /** Cette fonction permet de configurer le tag de formulaire de création de compte
     *
     * @return Kwanko l'instance
     */
    public function useLeadInscription()
    {
        $this->zone = 'lead_confirmation';

        return $this;
    }

    /** Cette fonction est chargé d'afficher les informations de tracking en fonction de la page consultée.
     *  @return string Le code HTML / JS pour le tracking
     *  @return bool False si le tracking n'est pas activé
     */
    public function getTrackingTag(){
        if (!$this->isActive()) {
            return false;
        }

        global $config;

        $tracking = '';
		if ($this->zone !== null || trim($this->event) != '') {
			$tracking = $this->getTrackingTagFromSettings();
        }


        if (trim($tracking) == '') {
            { // Récupère le fichier de la page
                $t = rew_rewrite(rew_strip($_SERVER['PATH_INFO']));
                if (!preg_match('/^\/shopbots\//', $_SERVER['PATH_INFO'])) {
                    if ($_SERVER['PATH_INFO'] == $t){
						$t = rew_rewrite($config['rew_strip'] . $_SERVER['PATH_INFO']);
					}
                }
                $url = dirname(__FILE__) . '/pages' . $t;

                $file = preg_replace('/\?.+/', '', $url);
                $_file = str_replace(dirname(__FILE__) . '/pages', '', $file);
                if (strpos($_file, '?')) {
                    $_file = substr($_file, 0, strpos($_file, '?'));
                }
            }

            ob_start();

            switch ($_file) {
                case '/index.php': { // Page d'accueil
                    print $this->homepage();
                    break;
                }
                case '/catalog/product.php': { // Fiche produit
                    print $this->product();
                    break;
                }
                case '/category.php':
                case '/catalog/index.php': { // Page catégorie
                    print $this->category();
                    break;
                }
                case '/cart/index.php': { // Panier
                    print $this->cart();
                    break;
                }
                case '/cart/complete.php': { // Confirmation de commande
                    print $this->complete();
                    break;
                }
            }

            $tracking = ob_get_clean();
            if (trim($tracking) == '') {
                ob_start();

                switch ($_file) {
                    case '/create.php': { // Inscription du compte client
                        ?><script async src="<?php print $this->url; ?>?zone=lead_inscription&m_md5="></script><?php
                        break;
                    }
                    case '/cart/complete.php':
                        ?><script async src="<?php print $this->url; ?>"></script><?php
                        break;
                    default : { // Gestion des évènements sur les autres pages
                        if (trim($this->event) != '') {
                            switch ($this->event) {
                                case 'create-user':{
                                    ?><script async src="<?php print $this->url; ?>?zone=lead_confirmation&id_lead=<?php print $this->customer_id; ?>&m_md5=<?php print $this->m_md5; ?>"> </script><?php
                                    break;
                                }
                            }
                        }
                    }
                }

                $tracking = ob_get_clean();
            }
        }

        return $tracking;
    }

    /** Cette fonction permet de générer le tag de tracking on fonction de la configuration de la class
     *
     * @return string Retourne une chaine vide si erreur, sinon le tag script
     */
    public function getTrackingTagFromSettings()
    {
        if (trim($this->event) != '') {
            switch ($this->event) {
                case 'create-user':{
                    return '
                        <script async src="'.$this->url.'?zone=lead_confirmation&id_lead='.$this->customer_id.'&m_md5='.$this->m_md5.'"></script>
                    ';
                }
            }
        }
        if ($this->zone === 'lead_confirmation') {
            return '<script async src="'.$this->url.'?zone=lead_inscription&m_md5="></script>';
        }

        $ptag_params = $this->getPTagParams();

        if (!is_array($ptag_params)) {
            return '';
        }

        $script = '
            <script async src="'.$this->url.'"></script>
            <script><!--
                window.ptag_params = '.json_encode($ptag_params).'
            --></script>
        ';

        return $script;
    }

    /** Cette fonction permet d'enregistrer l'identifiant d'un produit ou un tableau d'identifiant
     *  L'information sera toujours enregistrée sous forme de tableau.
     *  @param int $id Obligatoire, identifiant du produit
     *  @return Kwanko l'instance
     */
    public function setProductID($id) {
        $ids = control_array_integer($id, true);
        if ($ids !== false) {
            $this->k_product = $ids;
        }


        return $this;
    }

    /** Cette fonction permet d'enregistrer l'identifiant d'une catégorie ou un tableau d'idnetifiant
     *  L'information sera toujours enregistrée sous forme de tableau.
     *  @param int $id Obligatoire, identifiant d'une catégorie
     *  @return Kwanko l'instance
     */
    public function setCategoryID($id) {
        $ids = control_array_integer($id, true);
        if ($ids !== false) {
            $this->k_category = $ids;
        }


        return $this;
    }

    /** Cette fonction permet de déclencher le tracking d'un évènement par son code.
     *  @param string $code Obligatoire, code de l'évènement :
     *                  - create-user = Création d'un compte utilisateur
     *                  - lead_inscription = Formulaire de création de compte
     *  @return Kwanko l'instance
     */
    public function setEvent($code) {
        if (trim($code) != '' && in_array($code, $this->event_types)) {
            $this->event = $code;
        }


        return $this;
    }

    /** Cette fonction permet d'enregistrer l'identifiant de la commande pour le tag complete.
     *  @param int $ord_id Obligatoire, identifiant de la commande
     *  @return Kwanko l'instance
     */
    public function setOrderCompleteID($ord_id) {
        if (is_numeric($ord_id) && $ord_id > 0) {
            $this->ord_transaction = $ord_id;
        }


        return $this;
    }

    /** Cette fonction permet de gérer le tag sur toutes les pages de confirmation de paiements pour le tarcking des ventes
     *  @param $mclic Obligatoire, identifiant de remarketing kwanko
     *  @param $new_customer_mclic Facultatif, identifiant de remarketing kwanko pour les nouveaux client (implique une vérification de première commande)
     *  @return string Retourne le tag img pour le tracking des ventes
     */
    public function tagOrder($mclic, $new_customer_mclic=false) {
        if (!is_numeric($this->ord_transaction) || $this->ord_transaction <= 0) {
            return false;
        }

        global $config;

        $r_info = ria_mysql_query('
            select ord_id as id, ifnull(cod_code, "") as pmtcode, ifnull(pay_name, "nc") as payname
            from ord_orders
                left join pmt_codes on (cod_tnt_id = ord_tnt_id and cod_id = ord_pmt_id)
                left join ord_payment_types on ((pay_tnt_id = 0 or pay_tnt_id = ord_tnt_id) and pay_id = ord_pay_id)
            where ord_tnt_id = '.$config['tnt_id'].'
                and ord_id = '.$this->ord_transaction.'
        ');

        if (!$r_info || !ria_mysql_num_rows($r_info)) {
            return false;
        }

        $info = ria_mysql_fetch_assoc($r_info);

        $total_ht = ord_orders_get_total_without_port($this->ord_transaction, false);

        if( !$this->transaction_with_ecotaxe ){
            try{
                $total_ecotaxe = ord_orders_get_total_ecotaxe($this->ord_transaction);
                $total_ht -= $total_ecotaxe;
            }catch(Exception $e){
                error_log($e);
                error_log('[tenant-'.$config['tnt_id'].'] une erreur est survenu lors de la soustraction de l\'ecotaxe');
            }
        }

        $total_ht = number_format($total_ht, 2, '.', '');

        $payname = urlalias($info['payname']);
        $payname = str_replace('-', '', $payname);

        if (is_string($new_customer_mclic) && $this->isFirstOrder($this->ord_transaction)) {
            $mclic = $new_customer_mclic;
        }

        //  url pour le tracking des ventes
        $src = 'https://action.metaffiliation.com/trk.php?mclic='.$mclic.'&argmon='.$total_ht.'&argann='.$this->ord_transaction.'&argmodp='.htmlspecialchars($payname).'&nacur='.$this->currency.'&altid='.$this->m_md5.'&argbr='.urlencode($info['pmtcode']);

        ob_start();

        ?><img src="<?php print $src; ?>" width="1" height="1" border="0" /><?php

        return ob_get_clean();
	}

    /**
     *	Retourne les paramètres pour le tag sous forme de tableau associatif
     *
     *	@return array Un tableau associatif comprenant les clés suivantes :
	 * 		- zone
	 * 		- customerId
	 * 		- siteType
	 * 		- m_md5
     */
    private function getPTagParams()
	{
		return array_merge(array(
			'zone' => $this->zone,
			'customerId' => $this->customer_id,
			'siteType' => $this->site_type,
			'm_md5' => $this->m_md5,
		), $this->other_params);
	}

    /** Cette fonction permet de vérifier si c'est la première commande de l'utilisateur ou non
     *
     * @param int $order_id Obligatoire, Identifiant de la commande
     * @return boolean Retourne true si c'est la première commande, sinon false
     */
    private function isFirstOrder($order_id)
    {
        $r_first_order = ord_orders_get_simple(
            array(),
            array(),
            array(
                'state_id' => ord_states_get_ord_valid(),
                'usr_id' => $this->customer_id,
            ),
            array(
                'sort' => array(
                    'date' => 'asc',
                ),
                'limit' => 1,
            ),
            array(
                'type' => 'replace',
                'columns' => array(
                    'ord_id' => 'id',
                ),
            )
        );
        $is_first_order = false;
        if ($r_first_order && ria_mysql_num_rows($r_first_order)) {
            $first_order = ria_mysql_fetch_assoc($r_first_order);
            if ($order_id == $first_order['id']) {
                $is_first_order = true;
            }
        }

        return $is_first_order;
    }

    /** Cette fonction est en charge de générer le marqueur pour la page d'accueil
     *  @return string Le script JS du marqueur page d'accueil
     */
    private function homepage() {
        return $this->useHomepageTag()->getTrackingTagFromSettings();
    }

    /** Cette fonction est en charge de générer le marqueur pour une fiche produit
     *  @return string Le script JS du marqueur d'une fiche produit
     */
    private function product() {
        if (!count($this->k_category) || !count($this->k_product)) {
            return '';
        }

        return $this->useProductTag($this->k_product[0], $this->k_category[0])->getTrackingTagFromSettings();
    }

    /** Cette fonction est en charge de générer le marqueur pour une page catégorie
     *  @return Le script JS du marqueur d'une page catégorie
     */
    private function category() {
        if (!count($this->k_category) || !count($this->k_product)) {
            return '';
        }
        return $this->useListingTag($this->k_category[0], $this->k_product)->getTrackingTagFromSettings();
    }

    /** Cette fonction est en charge de générer le marqueur pour la page panier.
     *  @return Le script JS du marqueur de la page panier
     */
    private function cart() {
        return $this->useBasketTag(ria_array_get($_SESSION, 'ord_id', 0))->getTrackingTagFromSettings();
    }

    /** Cette fonction est en charge de générer le marqueur pour la page de confirmation de commande.
     *  @return Le script JS du marqueur de la page de confirmation de commande
     */
    private function complete() {
        if (!is_numeric($this->ord_transaction) || $this->ord_transaction <= 0) {
            return false;
        }

        return $this->useTransactionTag($this->ord_transaction)->getTrackingTagFromSettings();
    }

    /** Cette fonction permet de vérifier que le tracking Kwanko est activé.
     *  @return bool True si c'est bien le cas, False dans le cas contraire
     */
    private function isActive() {
        global $config;
        return isset($config['netaffiliation_is_active'], $config['netaffiliation_for_kwanko']) && $config['netaffiliation_is_active'] && $config['netaffiliation_for_kwanko'];
    }
}
/// @}
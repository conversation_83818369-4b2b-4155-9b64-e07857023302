<?php

require_once('db.inc.php');

/**	\defgroup model_news_categories Catégories d'actualités
 *	\ingroup tools_news
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories d'actualités.
 *	les catégories d'actualités peuvent désormais être hiérarchisées.
 * @{
 */

// \cond onlyria
/**	Permet l'ajout d'une catégorie d'actualités.
 *	@param string $name Obligatoire, désignation de la catégorie (ne peut être vide).
 *	@param string $desc Obligatoire, description de la catégorie.
 *	@param int $parent_id Optionnel, identifiant de la catégorie parent.
 *	@return int l'identifiant attribué au type en cas de succès, false en cas d'échec
 */
function news_categories_add( $name, $desc, $parent_id=0 ){

	$name = trim($name);
	if( !$name ){
		return false;
	}

	$name = ucfirst($name);
	$desc = ucfirst(trim($desc));

	if( !is_numeric($parent_id) || $parent_id < 0 ){
		return false;
	}elseif( $parent_id > 0 && !news_categories_exists( $parent_id ) ){
		return false;
	}

	global $config;

	$sql = '
		insert into news_categories
			(cat_tnt_id, cat_name, cat_desc, cat_parent_id, cat_date_created)
		values
			('.$config['tnt_id'].', "'.addslashes($name).'", "'.addslashes($desc).'", '.( $parent_id ? $parent_id : 'null' ).', now())
	';

	$res = ria_mysql_query($sql);

	if( $res ){

		$id = ria_mysql_insert_id();

		// crée la hiérarchie
		if( $parent_id ){
			news_cat_hierarchy_add( $parent_id, $id );
		}

		// Récupère les sites
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) ){
			return false;
		}

		// Crée les alias
		$alias = rew_rewritemap_generated( array($id), CLS_NEWS_CAT );
		while( $wst = ria_mysql_fetch_assoc($rwst) ){
			if( $prd_pages = cfg_urls_get( $wst['id'], CLS_NEWS_CAT ) ){
				while( $page = ria_mysql_fetch_assoc($prd_pages) ){
					rew_rewritemap_add_specify_class( CLS_NEWS_CAT, $alias.$page['key'], $page['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
				}
			}
		}

		$sql = '
			update news_categories
			set cat_url_alias = "'.addslashes($alias).'"
			where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
		';

		ria_mysql_query($sql);

		return $id;
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'une catégorie d'actualités.
 *	@param int $id Obligatoire, identifiant de la catégorie.
 *	@param string $name Obligatoire, désignation de la catégorie (ne peut être vide).
 *	@param string $desc Obligatoire, description de la catégorie.
 *	@param int $parent_id Optionnel, nouvel identifiant de parent (false pour ne pas changer, 0 pour spécifier la racine).
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function news_categories_update( $id, $name, $desc, $parent_id=false ){

	$name = trim($name);
	if( !trim($name) ){
		return false;
	}

	if( !news_categories_exists( $id ) ){
		return false;
	}

	if( $parent_id !== false && ( !is_numeric($parent_id) || $parent_id < 0 ) ){
		return false;
	}elseif( $parent_id > 0 && !news_categories_exists( $parent_id ) ){
		return false;
	}

	$name = ucfirst($name);
	$desc = ucfirst(trim($desc));

	global $config;

	$sql = '
		update news_categories
		set cat_name = "'.addslashes($name).'", cat_desc = "'.addslashes($desc).'"
		where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
	';

	$res = ria_mysql_query($sql);

	if( $res ){

		// mise à jour de la catégorie parent avant de générer l'alias
		if( $parent_id !== false ){
			if( !news_categories_update_parent( $id, $parent_id ) ){
				return false;
			}
		}

		$sql = '
			select cat_url_alias from news_categories
			where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
		';
		$rget_alias = ria_mysql_query($sql);

		if( $rget_alias && ria_mysql_num_rows($rget_alias) ){
			$url = ria_mysql_result($rget_alias, 0, 0);
			if( !trim($url) ){

				// Récupère les sites
				$rwst = wst_websites_get();
				if( !$rwst || !ria_mysql_num_rows($rwst) ){
					return false;
				}

				// Crée les alias
				$alias = rew_rewritemap_generated( array($id), CLS_NEWS_CAT );
				while( $wst = ria_mysql_fetch_assoc($rwst) ){
					if( $prd_pages = cfg_urls_get( $wst['id'], CLS_NEWS_CAT ) ){
						while( $page = ria_mysql_fetch_assoc($prd_pages) ){
							rew_rewritemap_add_specify_class( CLS_NEWS_CAT, $alias.$page['key'], $page['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
						}
					}
				}

				$sql = '
					update news_categories
					set cat_url_alias = "'.addslashes($alias).'"
					where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
				';

				$res = ria_mysql_query($sql);
			}
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le parent d'une catégorie d'actualités.
 *	Il n'est pas possible d'effectuer des références circulaires (ex : A enfant de B, B enfant de C, C enfant de A).
 *	@param int $id Obligatoire, identifiant de la catégorie d'actualité.
 *	@param int $parent_id Obligatoire, identifiant de la catégorie parent (0 pour spécifier la racine).
 *
 *	@return bool true en cas de succès, False en cas d'échec
 */
function news_categories_update_parent( $id, $parent_id ){

	if( !news_categories_exists( $id ) ){
		return false;
	}

	if( !is_numeric($parent_id) || $parent_id < 0 ){
		return false;
	}

	if( $parent_id > 0 ){
		if( $parent_id == $id ){
			return false;
		}

		$arbo_up = news_categories_get_parents( $parent_id );
		if( !$arbo_up ){
			return false;
		}

		// l'arborescence de $parent_id contient $id (référence circulaire)
		if( in_array($id, array_keys($arbo_up)) ){
			return false;
		}
	}

	global $config;

	// récupère l'ancien parent avant la MAJ
	$old_parent = news_categories_get_parent( $id );

	$sql = '
		update news_categories
		set cat_parent_id = '.( $parent_id ? $parent_id : 'null' ).'
		where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);

	if( $r ){
		// mise à jour des hiérarchies
		if( $old_parent ){
			news_cat_hierarchy_rebuild( $old_parent, true );
		}
		if( $parent_id ){
			news_cat_hierarchy_rebuild( $parent_id, true );
		}
		news_cat_hierarchy_rebuild( $id, true );
	}

	return $r;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression "logique" d'une catégorie d'actualités.
 *	@param int $id Obligatoire, identifiant de la catégorie à supprimer (elle doit exister).
 *	@param $set_news_to_null Optionnel, détermine le comportement si des actualités sont rattachées à la catégorie :
 *		- True assignera la valeur NULL à "news_cat_id" (comportement par défaut).
 *		- False empêchera la suppression.
 *	@param bool $set_parent_on_childs Optionnel, détermine le comportement si des catégories enfants sont rattachées :
 *		- True spécifiera, pour chaque enfant, l'identifiant de la catégorie parent de celle supprimée (comportement par défaut).
 *		- False empêchera la suppression.
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function news_categories_del( $id, $set_news_to_null=true, $set_parent_on_childs=true ){

	if( !news_categories_exists( $id ) ){
		return false;
	}

	global $config;

	// récupère les actualitées rattachées
	$rnews = news_get( 0, false, null, $id, 0, false, false );
	if( $rnews && ria_mysql_num_rows($rnews) ){
		if( !$set_news_to_null ){
			return false;
		}

		// met à jour le "cat_id" sur les actus
		$sql = '
			update news
			set news_cat_id = NULL
			where news_tnt_id = '.$config['tnt_id'].' and news_cat_id = '.$id.'
		';

		ria_mysql_query($sql);
	}

	// récupère les catégories enfants
	$rcat = news_categories_get( 0, false, $id );
	if( $rcat && ria_mysql_num_rows($rcat) ){
		if( !$set_parent_on_childs ){
			return false;
		}

		// récupère le parent de la catégorie à supprimer
		$parent = news_categories_get_parent( $id );
		if( $parent === false ){
			return false;
		}

		// met à jour "parent_id" sur les enfants
		$sql = '
			update news_categories
			set cat_parent_id = '.( $parent ? $parent : 'null' ).'
			where cat_tnt_id = '.$config['tnt_id'].' and cat_parent_id = '.$id.'
		';

		ria_mysql_query($sql);
	}

	// Suppression des urls traduites
	rew_rewritemap_del_multilingue( _FLD_NEWS_CAT_URL, array($id) );

	// Suppression des urls
	$url = news_categories_get_url( $id );
	if( trim($url) != '' )
		rew_rewritemap_del( $url );

	$sql = '
		update news_categories
		set cat_date_deleted = now()
		where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);

	if( $r ){
		news_cat_hierarchy_del( $id );
	}

	return $r;

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère le parent d'une catégorie d'actualité
 *	@param int $id Identifiant de la catégorie.
 *	@return int L'identifiant de la catégorie parent de la catégorie (NULL si racine).
 *	@return bool False en cas d'échec.
 */
function news_categories_get_parent( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select cat_parent_id
		from news_categories
		where cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$id.' and cat_date_deleted is null
	';

	$r = ria_mysql_query($sql);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}
// \endcond

/**	Cette fonction permet la validation d'un identifiant de catégorie d'actualités.
 *	@param int $id Identifiant de la catégorie à vérifier (ne peut être vide)
 *	@return bool true si l'identifiant est valide et correspond à une catégorie enregistrée dans la base de données.
 *	@return bool false dans le cas contraire.
 */
function news_categories_exists( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select cat_id from news_categories
		where cat_id = '.$id.' and cat_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);
	if( !$r ){
		return false;
	}

	return ria_mysql_num_rows($r);
}

/**	Cette fonction permet le chargement d'une ou plusieurs catégories d'actualités
 *	@param int $id Optionnel, identifiant d'une catégorie sur lequel filtrer le résultat (ou tableau d'identifiants).
 *	@param bool $publish Optionnel, si true seul les catégories contenant des actualités publiées seront retournées (ou des catégories contenant une arborescence publiée).
 *	@param int $parent Optionnel, identifiant d'une catégorie parent (null, par défaut, ne charge que les catégories racines, mais est ignoré si $id est spécifié). False permet de tout charger.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : désignation de la catégorie
 *			- desc : description de la catégorie
 *			- news : nombre de news contenues dans cette catégorie et ses enfants (nombre de catégories publiées si $publish activé)
 *			- url_alias : url simplifiée pour cette catégorie d'actualités
 *			- parent_id : identifiant de la catégorie parent
 *			- date_created : date de création au format "jj/MM/aaaa à hh:mm"
 *			- date_created_en : date de création au format brut
 *			- date_modified : date de dernière modification au format "jj/MM/aaaa à hh:mm"
 *			- date_modified_en : date de dernière modification au format brut
 */
function news_categories_get( $id=0, $publish=false, $parent=null ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			cat_id as id, cat_name as name, cat_desc as "desc", cat_url_alias as url_alias, cat_parent_id as parent_id, (
				(
					select count(*) from news
					where news_tnt_id = cat_tnt_id and news_cat_id = cat_id
					'.( $publish ? ' and news_publish_date <= now()' : '' ).'
				) + (
					select count(*) from news
					join news_cat_hierarchy on news_tnt_id = cah_tnt_id and news_cat_id = cah_child_id
					where cah_tnt_id = cat_tnt_id and cah_parent_id = cat_id
					'.( $publish ? ' and news_publish_date <= now()' : '' ).'
				)
			) as news,
			date_format(cat_date_created, "%d/%m/%Y à %H:%i") as date_created, cat_date_created as date_created_en,
			date_format(cat_date_modified, "%d/%m/%Y à %H:%i") as date_modified, cat_date_modified as date_modified_en
		from
			news_categories
		where
			cat_date_deleted is null and cat_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($id) ){
		$sql .= ' and cat_id in ('.implode(', ', $id).')';
	}

	if( $publish ){
		$sql .= '
			and (
				exists (
					select 1 from news
					where news_tnt_id = '.$config['tnt_id'].' and news_cat_id = cat_id
					and news_publish_date <= now()
				) or exists (
					select 1 from news
					join news_cat_hierarchy on news_tnt_id = cah_tnt_id and news_cat_id = cah_child_id
					where cah_tnt_id = '.$config['tnt_id'].' and cah_parent_id = cat_id
					and news_publish_date <= now()
				)
			)
		';
	}

	if( is_numeric($parent) && $parent > 0 ){
		$sql .= ' and cat_parent_id = '.$parent;
	}elseif( $parent === null && !sizeof($id) ){
		$sql .= ' and cat_parent_id is null';
	}

	$sql .= '
		order by cat_name
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction permet de récupérer l'url d'une categorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return string Retourne l'url d'une catégorie
 *	@return bool Reourne false si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function news_categories_get_url( $cat ){
	if( !is_numeric($cat) || $cat <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select cat_url_alias as alias
		from news_categories
		where cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$cat.' and cat_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 'alias');
}
// \endcond

/**	Cette fonction récupère la parenté complète d'une catégorie d'actualité.
 *	@param int $id Obligatoire, identifiant de la catégorie.
 *	@return array Un tableau associatif où la clé est l'identifiant du parent à un niveau donné, la valeur le niveau (1 pour le premier niveau).
 *	@return bool False en cas d'échec.
 */
function news_categories_get_parents( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			cat_id as id, cah_depth as depth
		from
			news_cat_hierarchy
			join news_categories on cah_parent_id = cat_id and cah_tnt_id = cat_tnt_id
		where
			cah_tnt_id = '.$config['tnt_id'].' and cah_child_id = '.$id.'
			and cat_date_deleted is null
		order by
			cah_depth asc
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	$parents = array();

	while( $c = ria_mysql_fetch_assoc($r) ){
		$parents[ $c['id'] ] = $c['depth'];
	}

	return $parents;

}

// \cond onlyria
/**	Cette fonction crée la hiérarchie entre une catégorie parent et une catégorie enfant.
 *	@param int $cat_parent_id Identifiant de la catégorie parent.
 *	@param int $cat_child_id Identifiant de la catégorie enfant.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function news_cat_hierarchy_add( $cat_parent_id, $cat_child_id ){

	if( !is_numeric($cat_parent_id) || $cat_parent_id <= 0 ){
		return false;
	}

	if( !is_numeric($cat_child_id) || $cat_child_id <= 0 ){
		return false;
	}

	$parents = news_categories_get_parents( $cat_parent_id );
	if( !is_array($parents) ){
		return false;
	}

	global $config;

	$sql = '
		insert into news_cat_hierarchy
			(cah_tnt_id, cah_parent_id, cah_child_id, cah_depth)
		values
			('.$config['tnt_id'].', '.$cat_parent_id.', '.$cat_child_id.', '.sizeof($parents).')
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	foreach( $parents as $cat_p_id => $cat_p_depth ){

		$sql = '
			insert into news_cat_hierarchy
				(cah_tnt_id, cah_parent_id, cah_child_id, cah_depth)
			values
				('.$config['tnt_id'].', '.$cat_p_id.', '.$cat_child_id.', '.$cat_p_depth.')
		';

		ria_mysql_query($sql);

	}

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction reconstruit la hiérarchie des catégories d'actualités à partir de la catégorie parent donnée.
 *	@param int $cat_id Optionnel, identifiant de la catégorie (0 permet de tout reconstruire).
 *	@param bool $forced Optionnel, force la suppression de l'arborescence avant de la reconstruire.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function news_cat_hierarchy_rebuild( $cat_id=0, $forced=false ){

	if( !is_numeric($cat_id) || $cat_id < 0 ){
		return false;
	}

	global $config;

	if( !$cat_id ){
		ria_mysql_query('delete from news_cat_hierarchy where cah_tnt_id = '.$config['tnt_id']);
	}elseif( $forced ){
		ria_mysql_query('delete from news_cat_hierarchy where cah_tnt_id = '.$config['tnt_id'].' and cah_parent_id = '.$cat_id);
	}

	$sql = '
		select
			cat_id as id
		from
			news_categories
		where
			cat_tnt_id = '.$config['tnt_id'].'
			and cat_date_deleted is null
	';
	if( $cat_id ){
		$sql .= ' and cat_parent_id = '.$cat_id;
	}else{
		$sql .= ' and cat_parent_id is null';
	}

	$rcat = ria_mysql_query($sql);

	if( $rcat ){
		while( $c = ria_mysql_fetch_assoc($rcat) ){
			news_cat_hierarchy_add( $cat_id, $c['id'] );
			news_cat_hierarchy_rebuild( $c['id'], false );
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Supprime la hiérarchie descendante d'une catégorie donnée.
 *	@param int $cat_id Identifiant de la catégorie.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function news_cat_hierarchy_del( $cat_id ){

	if( !is_numeric($cat_id) || $cat_id <= 0 ){
		return false;
	}

	global $config;

	$rchild = ria_mysql_query('
		select
			cat_id as id
		from
			news_categories
		where
			cat_tnt_id = '.$config['tnt_id'].'
			and cat_date_deleted is null
			and cat_parent_id = '.$cat_id.'
	');

	if( $rchild ){
		while( $ccat = ria_mysql_fetch_assoc($rchild) ){
			news_cat_hierarchy_del( $ccat['id'] );
		}
	}

	return ria_mysql_query('
		delete from news_cat_hierarchy
		where cah_tnt_id = '.$config['tnt_id'].' and cah_child_id = '.$cat_id.'
	');

}
// \endcond

/// @}



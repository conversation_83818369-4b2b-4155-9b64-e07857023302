<?php

require_once('db.inc.php');
require_once('email.inc.php');
require_once('strings.inc.php');
require_once('antispam.inc.php');
require_once('users.inc.php');
require_once('messages.inc.php');

/**	\defgroup tip_a_friend Envoyer à un ami
 *	\ingroup model_site
 *	Ce module comprend les fonctions nécessaires à la gestion de la fonctionnalité "Envoyer à un ami" qui s'étende à l'ensemble du site
 *	@{
 */

/**	Cette fonction permet l'envoi et le stockage d'un email de type "Recommander ce site à un ami"
 *	@param string $sender_firstname Prénom de l'émetteur (chaîne vide possible)
 *	@param string $sender_lastname Nom de famille de l'émetteur (chaîne vide possible)
 *	@param string $sender_email Adresse email de l'émetteur
 *	@param string $receiver_email Adresse email du destinataire
 *	@param string $notes Message d'accompagnement
 */
function site_tip_a_friend_send( $sender_firstname, $sender_lastname, $sender_email, $receiver_email, $notes ){
	global $config;

	if( !isemail($sender_email) ) return false;
	if( !isemail($receiver_email) ) return false;

	// Antispam
	// if( isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']=='************' ) return 0; // toolsnfun.com (javascript désactivé requis pour voir le site)

	$res = add_message($sender_firstname, $sender_lastname, '', $sender_email, '', '', $notes, 'SITE', $receiver_email, false, 0, 0, '', '', '', 0, 0, false, true);
	//Si le message n'est pas spammer
	if( $res )
	{
		site_tip_a_friend_send_mail( $res );
	}

	site_tip_a_friend_log( $sender_firstname, $sender_lastname, $sender_email, $receiver_email );

	return true;
}

/**	Envoie le mail de "Recommandation à un ami" pour un site
 *	@param int $id Obligatoire, identifiant d'un message près pour l'envoi
 *	@param bool $moderate Facultatif, si vrai, le message est envoyé à un modérateur avant envoi au destinataire. La valeur par défaut est false.
 *	@return bool Retourne true si l'envoi s'est correctement passé
 *	@return bool Retourne false dans le cas contraire
 */
function site_tip_a_friend_send_mail( $id, $moderate=false ){

	// Charge le message
	$rmsg = messages_get( 0, '', 0, $id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) ){
		return false;
	}

	// Si le message est un spam, fais comme-ci il avait été envoyé correctement pour ne pas que le spammeur détecte qu'il a été bloqué
	$msg = ria_mysql_fetch_array( $rmsg );
	if( $msg['spam_id']!='' ){
		return true;
	}
	global $config;
	$http_host_ria = $config['backoffice_url'];
	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('site-tip-a-friend');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);
	if( $cfg['bcc']=='' ) return true;

	$email = new Email();
	$email->setSubject( $config['site_name'] );
	$email->setFrom( $msg['email'] );
	if( !$moderate ){
		$email->addTo( $msg['receiver_email'] );
	}elseif( $moderate && $cfg['bcc'] ){
		$email->addTo( $cfg['bcc'] );
	}

	if( trim($msg['firstname']) || trim($msg['lastname']) ){
		$msg['firstname'] = str_replace( array('/','\\',':','=',';','?'), '', $msg['firstname'] );
		$msg['firstname'] = substr( $msg['firstname'], 0, 75 );
		$msg['lastname'] = str_replace( array('/','\\',':','=',';','?'), '', $msg['lastname'] );
		$msg['lastname'] = substr( $msg['lastname'], 0, 75 );
		$email_from = trim($msg['firstname'].' '.$msg['lastname']).' <'.$msg['email'].'>';
	}else{
		$email_from = $msg['email'];
	}

	if( $moderate ){
		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
	}

	$email->addHtml( $config['email_html_header'] );

	$email->addParagraph( 'Bonjour,' );

	$email->addParagraph( 'un de vos amis ('.$email_from.') vous invites à découvrir le site <a href="'.$config['site_url'].'/?utm_source=recommander&utm_medium=email&utm_campaign=site">'.$config['site_name'].'</a>, '.$config['site_desc'].'.' );

	if( trim($msg['body']) ){
		$msg['body'] = html_strip_tags($msg['body']); // Pour les spammeurs
		$msg['body'] = preg_replace( '/\[url[^\]]*\][^\[]+\[\/url\]/i', ' ', $msg['body'] );
		$email->addHorizontalRule();
		$email->addParagraph( nl2br(htmlspecialchars($msg['body'])) );
	}

	$email->addHorizontalRule();
	$email->addParagraph( 'A bientôt sur <a href="'.$config['site_url'].'/?utm_source=recommander&utm_medium=email&utm_campaign=site">'.$config['site_name'].'</a>' );

	$email->addHtml( $config['email_html_footer'] );

	if( $moderate ){
		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
	}

	$res = $email->send();

	if( !$moderate )
		$res = site_tip_a_friend_send_mail( $id, true );

	if( $res )
		message_set_send( $id );
	return $res;
}

/**	Enregistre une demande d'envoi a un ami effectuée par un utilisateur dans le fichier de log.
 *
 *	Le log comprend les colonnes suivantes :
 *		- date/heure : date et heure de la recherche
 *		- sender_firstname : Prénom de l'émetteur
 *		- sender_lastname : Nom de famille de l'émetteur
 *		- sender_email : adresse email de l'expéditeur
 *		- receiver_email : adresse email du destinataire
 *
 *	Les envois sont enregistrés dans le dossier pointé par la directive de configuration
 *	$config['tell_log_dir']. Un fichier de log différent est utilisé par jour.
 *
 *	@param string $sender_firstname Prénom de l'émetteur (chaîne vide possible)
 *	@param string $sender_lastname Nom de famille de l'émetteur (chaîne vide possible)
 *	@param string $sender_email Adresse email de l'émetteur
 *	@param string $receiver_email Adresse email du destinataire
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function site_tip_a_friend_log( $sender_firstname, $sender_lastname, $sender_email, $receiver_email){
	global $config;

	$client = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
	return error_log( date('d/m/Y H:i:s')."\t".$sender_firstname."\t".$sender_lastname."\t".$sender_email."\t".$receiver_email."\t".$client."\n", 3, $config['tell_log_dir'].'site-'.date('Y-m-d').'.log' );

}

/// @}


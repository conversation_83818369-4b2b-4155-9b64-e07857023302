<?php
require_once('RegisterGCP.inc.php');

// \cond onlyria

/** \defgroup model_sitemaps Gestion des sitemaps
 *	\ingroup model_site seo
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des sitemaps.
 *	Il est possible de choisir dans l'administration si les sitemaps doivent être générés automatiquement ou non.
 *	Les sitemaps sont générés tous les jours via une tâche cron multi-tenant.
 *
 *	Le sitemap index sera disponible à l'adresse /sitemap.xml. Celui-ci pointe sur tous les autres sitemaps.
 *	Les urls sont réparties sur plusieurs sitemaps pour permettre une meilleure indexation car s'il y a trop d'urls
 *	dans le fichier certaines peuvent ne pas être indexées.
 *
 *	Pour chaque langue, il y a un sitemap par catégorie et un sitemap pour toutes les autres pages.
 *	Les images de catégories et de produits sont également indexées pour chaque page.
 *
 *	Pour mettre en place le module sitemap, penser à :
 *	- <PERSON><PERSON>er le dossier sitemaps dans htdocs/pages
 *	- Don<PERSON> les droits à apache sur ce dossier
 *
 *	Pour exécuter la tâche qui met à jour les sitemaps, il suffit d'appeler sitemaps_reload()
 *
 *	@{
 */

/**	Cette fonction met à jour les sitemaps d'un site
 *  @return bool true en cas de succès, false en cas d'échec
 */
function sitemaps_reload() {
	Sitemap::reload();
}

/**	\brief Cette classe représente un Sitemap
 */
class Sitemap {

	/// \privatesection
	// Attributs statiques

		private	static $_categories; ///< Arborescence des catégories de produits contenues dans le site
		private static $_langs; ///< Langues
		private	static $_products; ///< Liste des produits contenus dans le site
		private static $_tree; ///< Arborescence des contenus
		private static $_stores; ///< Liste des magasins publiés pour le site

		private static $_url_cache; ///< Tableau contenant url => résultat de la requète

		private static $_products_list; ///< Liste des produits
		private static $_categories_list; ///< Liste des catégories
		private static $_brands_list; ///< Liste des marques
		private static $_stores_list; ///< Liste des magasins
		private static $_cms_list; ///< Liste des cms
		private static $_sector_list; ///< Liste des secteurs
		private static $_doc_type_list; ///< Liste des types de document
		private static $_faq_cat_list; ///< Liste des catégories de faq
		private static $_news_list; ///< Liste des actualités
		private static $_news_cat_list; ///< Liste des catégories d'actualités
		private static $_faq_qst_list; ///< Liste des questions de faq

	// Attributs

		private	$_location; ///< Nom du fichier sur disque
		private	$_sitemaps; ///< Sitemaps référéncés par ce sitemap (dans le cas d'un sitemap d'index)
		private	$_time; ///< Date et heure de dernière modification de ce sitemap (la date/heure actuellement utilisée ne tient pas compte de l'absence de modification dans le fichier)
		private $_urls; ///< Urls à référencer par le sitemap
		private $_url; ///< Url par laquelle le sitemap sera accessible de l'extérieur (par les robots)

	/// \publicsection
	// Méthodes statiques

		/** Cette méthode n'ajoute que les catégories réellement publiées
		 *	@param array $nod Obligatoire, tableau contenant la clé children (liste des catégories enfants)
		 *	@param array $categories Passage par référence d'un tableau des catégories controlées
		 *	@return void
		 */
		public static function checkCategories( $nod, &$categories ){
			if( !isset( $nod['children'] ) ) return false;

			$childrens = $nod['children'];
			foreach( $childrens as $cat ){
				if( !$cat['data']['publish'] ){
					continue;
				}

				$categoryID = $cat['data']['id'];
				$categories[$categoryID] = $cat['data'];
				Sitemap::checkCategories( $cat, $categories );
			}
		}

		/**	Cette méthode crée un sitemap index
		 *	@param array $param Obligatoire, tableau contenant les paramètres suivants :
		 *			-	uid	: Identifiant unique pour ne pas mélanger les nouveaux sitemaps des anciens
		 *	@return object Un objet sitemap
		 */
		public static function createSitemapIndex( $param ){
			$uid = $param['uid'];

			// Récupère les langues utilisées
			$langues = Sitemap::getLangs();

			if( count($langues) == 0 ){
				throw new exception('Il n\'y a aucune langue');
			}
			$index = new Sitemap($param);

			if( count($langues) > 1 ){
				foreach( $langues as $lng ){
					$index->addSiteMap( Sitemap::createSitemapLang(array('uid' => $uid, 'lang' => $lng)) );
				}
			}
			else {
				// Si le site n'est pas multilingue, pas de sitemap pour la langue
				$index->union(array('sitemap' => Sitemap::createSitemapLang(array('uid' => $uid, 'lang' => $langues[0]))));
			}

			$index->setLocation( 'sitemap-'.$uid.'.xml' );
			$index->setUrl( 'sitemap.xml' );

			return $index;
		}

		/**	Cette méthode crée un sitemap pour une langue donnée
		 *	@param array $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			-	uid	: Identifiant unique pour ne pas mélanger les nouveaux sitemaps des anciens
		 *			-	lang : Langue du sitemap à générer
		 *	@return object Un objet sitemap
		 */
		public static function createSitemapLang( $param ){
			global $config;

			$uid = $param['uid'];
			$lang = $param['lang'];

			$domain = wst_websites_languages_get_url($config['wst_id'], $lang);
			if( !$domain ){
				return false;
			}

			$sitemap = new Sitemap(array('domain' => $domain, 'location' => 'sitemap-'.$uid.'-'.$lang.'.xml', 'url' => 'sitemap-'.$lang.'.xml'));

			if( $config['sitemap_catalog'] ){
				// catégories
				$categories = Sitemap::getCategories();

				foreach( $categories as $categoryID => $category ){
					// vérifie que la catégorie est bien publiée pour la langue $lang
					if( $lang != $config['i18n_lng'] ){
						if( !prd_categories_has_products( $categoryID, $lang ) ){
							continue;
						}
					}

					try {
						$subSitemap = Sitemap::createSitemapCategory(array('uid' => $uid, 'lang' => $lang, 'category' => $category));
						$sitemap->addSitemap( $subSitemap );
					}
					catch( SitemapExceptionUnpublished $e ){
						// Cette exception est toujours identique : la catégorie n'est pas publiée (ce qui ne pose pas de souci en soi)
						// Ce serait meilleur si seules les catégories publiées étaient chargées à la source afin d'économiser des traitements
						//error_log( 'Sitemap::createSitemapLang : Exception SitemapExceptionUnpublished levée - '.$e->getMessage() );
					}
				}
			}

			// autres
			$sitemap->addSitemap( Sitemap::createSitemapOthers($param) );

			return $sitemap;
		}

		/**	Cette méthode crée un sitemap catégorie
		 *	@param array $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			-	uid	: Identifiant unique pour ne pas mélanger les nouveaux sitemaps des anciens
		 *			-	lang : Langue du sitemap
		 *			-	category : Tableau issu de prd_categories_get_all
		 *	@return object Un objet sitemap
		 */
		public static function createSitemapCategory( $param ){
			global $config;

			$uid = $param['uid'];
			$lang = $param['lang'];
			$category = $param['category'];

			$sitemap = new Sitemap(array('domain' => wst_websites_languages_get_url($config['wst_id'], $lang), 'location' => 'sitemap-'.$uid.'-'.$lang.'-category-'.$category['id'].'.xml', 'url' => 'sitemap-'.$lang.'-category-'.$category['id'].'.xml'));

			$tree = Sitemap::getTree();

			// ajoute l'url de la page catégorie
			if( isset($tree['urls'][$lang]['categories'][$category['id']]) ){
				$url = $tree['urls'][$lang]['categories'][$category['id']];
			}elseif( in_array( $lang, array('ru') ) ){
				// special langues sans url propres
				$mainLang = $config['i18n_lng'];

				if( isset($tree['urls'][$mainLang]['categories'][$category['id']]) ){
					$url = clone $tree['urls'][$mainLang]['categories'][$category['id']];
					$url->setDomain( wst_websites_languages_get_url($config['wst_id'], $lang) );
				}
			}

			if( !isset($url) ){
				throw new SitemapExceptionUnpublished('La catégorie '.$category['id'].' n\'est pas publiée !');
			}

			$sitemap->addUrl( $url );

			// ajoute les urls des produits
			if( isset($tree['urls'][$lang]['products'][$category['id']]) ){
				$products = $tree['urls'][$lang]['products'][$category['id']];
			}elseif( in_array($lang, array('ru')) ){
				$mainLang = !isset($mainLang) ? $config['i18n_lng'] : $mainLang;
				// special sans url propre
				if( isset($tree['urls'][$mainLang]['products'][$category['id']]) ){
					$products = array();
					foreach( $tree['urls'][$mainLang]['products'][$category['id']] as $productID => $url ){
						$t = clone $url;
						$t->setDomain( wst_websites_languages_get_url($config['wst_id'], $lang) );
						$products[$productID] = $t;
					}
				}
			}

			if( isset($products) ){
				foreach( $products as $productID => $url ){
					$product = Sitemap::getProduct( $productID );
					if( !$product ){
						continue;
					}

					// vérifie si le produit est bien publié
					if( !isset($config['catalog_publish_lng']) || !$config['catalog_publish_lng'] || $lang == $config['i18n_lng'] ){
						if( !$product['publish'] ){
							continue;
						}
					}else{
						if( fld_object_values_get( $productID, _FLD_PRD_PUBLISH, $lang, true ) != 'Oui' ){
							continue;
						}
					}

					$sitemap->addUrl( $url );
				}
			}

			return $sitemap;
		}

		/**	Cette méthode crée un sitemap qui indexe toutes les autres pages
		 *	@param array $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			-	uid	: Identifiant unique pour ne pas mélanger les nouveaux sitemaps des anciens
		 *			-	lang : Langue du sitemap
		 *	@return object Un objet sitemap
		 */
		public static function createSitemapOthers( $param ){
			global $config;

			$uid = $param['uid'];
			$lang = $param['lang'];
			$sitemap = new Sitemap(array('domain' => wst_websites_languages_get_url($config['wst_id'], $lang), 'location' => 'sitemap-'.$uid.'-'.$lang.'-others.xml', 'url' => 'sitemap-'.$lang.'-others.xml'));

			$tree = Sitemap::getTree();
			$others = $tree['urls'][$lang]['others'];
			foreach( $others as $url ){
				$sitemap->addUrl( $url );
			}

			if( in_array($lang, array('ru')) ){
				// special sans url propre
				$mainLang = $config['i18n_lng'];

				if( isset($tree['urls'][$mainLang]['others']) ){
					foreach( $tree['urls'][$mainLang]['others'] as $url ){
						$t = clone $url;
						$t->setDomain( wst_websites_languages_get_url($config['wst_id'], $lang) );
						$sitemap->addUrl( $t );
					}
				}
			}

			return $sitemap;
		}

		/** Récupère les catégories de produits publiées contenues dans le site.
		 *	Le résultat de cette fonction est mis en cache.
		 *	@return array La liste des catégories sous forme d'une arborescence de tableaux associatifs imbriqués selon la hiérarchie des catégories
		 */
		public static function getCategories(){
			global $config;

			if( !Sitemap::$_categories ){

				$query = prd_categories_get( 0, true, (int)$config['sitemap_categorie_root'], '', false, false, null, false, array(), true, false, false, false, false, false, false, false, false, null, false, true);
				if( !$query ){
					throw new exception('Echec de prd_categories_get_all'."\n".ria_mysql_errno()."\n".mysql_error());
				}

				$tree = array();

				$categories = array();
				while( $dat = ria_mysql_fetch_assoc($query) ){
					if( $dat['no_index'] == '1' ){
						continue;
					}elseif( !prd_categories_is_index($dat['id']) ){
						continue;
					}
					$nod =& $tree[ $dat['id'] ];
					if( $nod === null ){
						$nod = array( 'children' => array() );
					}
					$nod['data'] = $dat;
					$parent =& $tree[ $dat['parent_id'] ];
					if( $parent===null ){
						$parent = array( 'children' => array() );
					}
					$parent['children'][] =& $tree[$dat['id']];
				}

				if( !isset($tree['']) && isset( $tree[$config['sitemap_categorie_root']] ) ){
					$tree[''] = $tree[$config['sitemap_categorie_root']];
				}

				if( isset($tree['']) ){
					Sitemap::checkCategories($tree[''], $categories);
				}
				Sitemap::$_categories = $categories;
			}
			return Sitemap::$_categories;
		}

		/** Récupère les magasins publiés dans le site.
		 *	Le résultat de cette fonction est mis en cache.
		 *	@return array La liste des magasins sous la forme d'un tableau associatif [ id ] = ria_mysql_fetch_array( dlv_stores_get( id ) )
		 */
		public static function getStores(){
			global $config;

			if( !Sitemap::$_stores ){

				$query = dlv_stores_get(); // pas d'arguments : magasins publiés uniquement
				if( !$query ){
					throw new exception('Echec de dlv_stores_get'."\n".ria_mysql_errno()."\n".mysql_error());
				}

				$stores = array();
				while( $dat = ria_mysql_fetch_assoc($query) ){
					if (isset($config['sitemap_store_sales']) && is_array($config['sitemap_store_sales']) && count($config['sitemap_store_sales'])) {
						$isok = false;

						$r_type = dlv_sales_types_get( 0, $dat['id'] );
						if ($r_type) {
							while ($type = ria_mysql_fetch_assoc($r_type)) {
								if (in_array($type['id'], $config['sitemap_store_sales'])) {
									$isok = true;
									break;
								}
							}
						}

						if (!$isok) {
							continue;
						}
					}

					$stores[ $dat['id'] ] = $dat;
				}

				Sitemap::$_stores = $stores;
			}
			return Sitemap::$_stores;
		}

		/** Cette méthode récupère les langues utilisées dans le site
		 *	@return array Cette fonction renvoie la liste des langues utilisées, sous forme de tableau contenant un code de langue par ligne
		 */
		public static function getLangs(){
			if( !self::$_langs ){
				global $config;

				$tree = Sitemap::getTree();

				$langs = array();
				foreach( $tree['urls'] as $lng => $t ) {
					if( (isset($config['sitemap_lng_used']) && is_array($config['sitemap_lng_used']) && sizeof($config['sitemap_lng_used']) && in_array($lng, $config['sitemap_lng_used']))
						|| !isset($config['sitemap_lng_used'])
						|| !is_array($config['sitemap_lng_used'])
						|| !sizeof($config['sitemap_lng_used']) ){
						$langs[] = $lng;
					}
				}
				self::$_langs = $langs;
			}
			return self::$_langs;
		}

		/**	Cette méthode met à jour les sitemaps (alias de sitemaps_reload)
		 *	@return void
		 */
		public static function reload(){
			global $config;

			// Supprime les caches
			Sitemap::$_categories = null;
			Sitemap::$_langs = null;
			Sitemap::$_products = null;
			Sitemap::$_tree = null;
			Sitemap::$_stores = null;

			// Réinitialise les tableaux des classes
			Sitemap::resetClassLists();

			// crée un id unique
			$uid = time();

			if( !ria_mysql_query('SET AUTOCOMMIT = 0;') ){
				throw new exception('Erreur transaction !'."\n".mysql_error());
			}
			if( !ria_mysql_query('START TRANSACTION;') ){
				throw new exception('Erreur transaction !'."\n".mysql_error());
			}

			// supprime les anciennes urls
			if( !ria_mysql_query('delete from rew_rewritemap WHERE url_tnt_id = '.$config['tnt_id'].' and url_wst_id = '.$config['wst_id'].' and url_intern LIKE \'/'.$config['sitemap_dirname'].'/%\' and url_code = 200;') )
				throw new exception('Erreur delete !'."\n".mysql_error());

			if( $config['sitemap_auto'] ){
				// crée le sitemap index et les autres en cascade
				$index = Sitemap::createSitemapIndex(array('uid' => $uid, 'url' => 'sitemap.xml'));

				// enregistre les fichiers et insert les lignes (en cascade)
				$index->save(array('index' => true));
			}

			if( !ria_mysql_query('COMMIT;') ){
				throw new exception('Erreur transaction !'."\n".mysql_error());
			}
			if( !ria_mysql_query('SET AUTOCOMMIT = 1;') ){
				throw new exception('Erreur transaction !'."\n".mysql_error());
			}

			$rep = Sitemap::getDirname();
			if( !is_dir($rep) ){
				if( $config['sitemap_auto'] ){
					if( !@mkdir( $rep ) ){
						error_log( 'Sitemaps - Droits apache - Apache n\'a pas réussi à créer le dossier '.$rep.' car il n\'a pas les droits nécéssaires.' );
					}
					else{
						// Détermine le user en fonction de si l'on est sur la GCP ou non
						$apache_user = RegisterGCP::onGcloud() ? 'www-data' : 'apache';

						chown( $rep, $apache_user );
						chgrp( $rep, $apache_user );
					}
				}
			}

			// Supprime les anciens fichiers
			if( is_dir($rep) ) {
				if( !$dir = opendir($rep) ){
					throw new exception('Impossible d\'ouvrir le dossier '.$rep);
				}
				$l = strlen( 'sitemap-'.$uid );
				while ($f = readdir($dir)) {
					if (in_array($f, array('.', '..', 'ssl', '.svn'))) {
						continue;
					}
					if( substr($f, 0, $l) != 'sitemap-'.$uid ){
						@unlink( $rep.'/'.$f );
					}
				}
				closedir($dir);
			}

			// Création de la version HTTPS
			$rep = Sitemap::getDirname();

			if( is_dir($rep) && isset($config['site_ssl_url']) && trim($config['site_ssl_url']) != '' && $config['site_ssl_url'] != $config['site_url'] ) {
				if( !$dir = opendir($rep) ){
					throw new exception('Impossible d\'ouvrir le dossier '.$rep);
				}
				// Création du dossier pour la version HTTPS
				if (!is_dir($rep.'/ssl')) {
					if( !@mkdir( $rep.'/ssl' ) ){
						error_log( 'Sitemaps - Droits apache - Apache n\'a pas réussi à créer le dossier '.$rep.'/ssl car il n\'a pas les droits nécessaires.' );
					}else{
						// Détermine le user en fonction de si l'on est sur la GCP ou non
						$apache_user = RegisterGCP::onGcloud() ? 'www-data' : 'apache';

						chown( $rep, $apache_user );
						chgrp($rep, $apache_user );
					}
				}else{
					// Vide le dossier SSL
					if( !$dir = opendir($rep.'/ssl') ){
						throw new exception('Impossible d\'ouvrir le dossier '.$rep);
					}

					while ($f = readdir($dir)) {
						if (in_array($f, array('.', '..', 'ssl', '.svn'))) {
							continue;
						}

						@unlink( $rep.'/ssl/'.$f );
					}

					closedir($dir);
					if( !$dir = opendir($rep) ){
						throw new exception('Impossible d\'ouvrir le dossier '.$rep);
					}
				}

				$r_lng_website = wst_websites_languages_get();
				if ($r_lng_website && ria_mysql_num_rows($r_lng_website)) {
					while ($f = readdir($dir)) {
						if (in_array($f, array('.', '..', 'ssl', '.svn'))) {
							continue;
						}

						$content = file_get_contents($rep.'/'.$f);

						ria_mysql_data_seek($r_lng_website, 0);
						while ($lng_website = ria_mysql_fetch_assoc($r_lng_website)) {
							$version_ssl = str_replace( 'http://', 'https://', $lng_website['url'] );
							$content = str_replace( $lng_website['url'], $version_ssl, $content );
						}

						file_put_contents( $rep.'/ssl/'.$f, $content );
					}
				}

				closedir($dir);
			}
		}

		/**	Cette méthode renvoie le dossier où sont stockés les sitemaps
		 *	@return string Chemin d'accès vers les sitemaps
		 */
		public static function getDirname(){
			global $config;
			return $config['tnt_id']==1 ? $config['site_dir'].'/'.$config['sitemap_dirname'] : $config['site_dir'].'/pages/'.$config['sitemap_dirname'];
		}

		/**	Cette méthode renvoie un produit
		 *	Plutôt que de faire un prd_products_get pour chaque produit, cette méthode fait le même travail mais en utilisant un cache
		 *	@param int $id Obligatoire, Identifiant du produit
		 *	@return null|resource si le produit n'a pas été trouvé, un résultat de prd_products_get() dans le cas contraire
		 */
		public static function getProduct( $id ){
			$products = Sitemap::getProducts();
			return isset($products[$id]) ? $products[$id] : null;
		}

		/**	Cette méthode renvoie tous les produits publiés pour ce site.
		 *	Le résultat de cette méthode est mis en cache.
		 *	@return array Un tableau associatif contenant la liste complète des urls de produits disponibles pour ce site, sous la forme :
		 *		- Clé : identifiant du produit
		 *		- Valeur : tableau associatif décrivant le produit (issu d'un ria_mysql_fetch_assoc sur le résultat de prd_products_get_simple)
		 */
		public static function getProducts(){
			if( !Sitemap::$_products ){
				global $config;

				$products = array();

				// On regarde si on filtre les produits du sitemap sur une famille de marques.
				if( isset($config['sitemap_product_brand']) && trim($config['sitemap_product_brand'])!='' ){
					$brands = prd_brands_search( $config['sitemap_product_brand'] , true, false, true);

					$brands = control_array_integer( $brands, false );
					if ($brands === false) {
						unset($brands);
					}
				}
				$other_params = array();

				if (isset($brands)) {
					$other_params['brand'] = $brands;
				}

				if (isset($config['sitemaps_product_children']) && $config['sitemaps_product_children']) {
					$other_params['childs'] = true;
				}

				if (empty($other_params)) {
					$other_params = false;
				}

				// récupère tous les produits. La condition de publication dépendra de chaque langue ...
				$query = prd_products_get_simple( 0, '', (isset($config['sitemap_product_published']) ? $config['sitemap_product_published'] : true), 0, false, false, false, false, $other_params );
				if( !$query ){
					throw new exception('Sitemap::getProducts : Echec de l\'appel à prd_products_get_simple'."\n".ria_mysql_errno()."\n".mysql_error());
				}

				while ($dat = ria_mysql_fetch_assoc($query)) {
					if( $dat['no_index'] == '1' ){
						continue;
					}elseif( !prd_products_is_index($dat['id']) ){
						continue;
					}

					$products[$dat['id']] = $dat;
				}
				Sitemap::$_products = $products;
			}
			return Sitemap::$_products;
		}

		/**	Cette méthode renvoie toutes les urls du site dans un arbre classé par critères
		 *	Les catégories et produits non publiés seront exclus
		 *	@return L'attribut statique _tree de la classe
		 */
		public static function getTree(){
			if( !Sitemap::$_tree ){
				global $config;

				// Récupère toutes les urls du site, ainsi que les urls multi-sites associées à ce locataire
				$sql = '
					select url_lng_code, url_extern, url_intern, url_obj_id_0, url_obj_id_1
					from rew_rewritemap
					where url_tnt_id = '.$config['tnt_id'].'
						and ( url_wst_id='.$config['wst_id'].' or url_wst_id=0 )
						and url_code = 200
						and url_public = 1
				';

				if( isset($config['sitemaps_faq_enable']) && !$config['sitemaps_faq_enable'] ){
					$sql .= '
						and url_cls_id != ' . CLS_FAQ_CAT . '
						and url_cls_id != ' . CLS_FAQ_QST . '
					';
				}

				if( isset($config['sitemaps_store_enable']) && !$config['sitemaps_store_enable'] ){
					$sql .= '
						and url_cls_id != ' . CLS_STORE . '
					';
				}

				if( isset($config['sitemaps_doctype_enable']) && !$config['sitemaps_doctype_enable'] ){
					$sql .= '
						and url_cls_id != '. CLS_TYPE_DOCUMENT .'
					';
				}

				if( isset($config['sitemaps_brand_enable']) && !$config['sitemaps_brand_enable'] ){
					$sql .= '
						and url_cls_id != ' . CLS_BRAND . '
					';
				}

				$query = ria_mysql_query($sql);
				if( !$query ){
					throw new exception('Sitemap::getTree : Echec de la requete '.$sql."\n".ria_mysql_errno()."\n".mysql_error());
				}

				$r = array('urls' => array());
				$urls =& $r['urls'];

				$size = $config['img_sizes']['medium'];

				$categories = Sitemap::getCategories();

				$stores = Sitemap::getStores();

				// Récupère l'url interne utilisée pour les produits dans ce site
				if( ! $rurl = cfg_urls_get($config['wst_id'], CLS_PRODUCT) ){
					throw new exception('Echec de cfg_urls_get product !');
				}
				if( ! $urlProduct = ria_mysql_fetch_assoc($rurl) ){
					throw new exception('cfg_urls_get n\'est pas configuré pour produit !');
				}
				$urlProduct = $urlProduct['url'];

				// Récupère l'url interne utilisée pour les catégories dans ce site
				if( ! $rurl = cfg_urls_get($config['wst_id'], CLS_CATEGORY) ){
					throw new exception('Echec de cfg_urls_get category !');
				}
				if( ! $urlCategory = ria_mysql_fetch_assoc($rurl) ){
					throw new exception('cfg_urls_get n\'est pas configuré pour catégorie !');
				}
				$urlCategory = $urlCategory['url'];

				// Récupère l'url interne utilisée pour les magasins dans ce site
				if( ! $rurl = cfg_urls_get($config['wst_id'], CLS_STORE) ){
					throw new exception('Echec de cfg_urls_get store !');
				}

				$urlStore = false;

				if( $rurl && ria_mysql_num_rows($rurl) ){
					if( ! $urlStore = ria_mysql_fetch_array($rurl) ){
						throw new exception('cfg_urls_get n\'est pas configuré pour ce magasin !');
					}
					$urlStore = $urlStore['url'];
				}

				$warnings = array();
				while( $dat = ria_mysql_fetch_assoc($query) ){
					$lng = $dat['url_lng_code'];
					$intern = $dat['url_intern'];

					$domain = wst_websites_languages_get_url($config['wst_id'], $lng);
					if( $domain===false ){
						continue;
					}

					$url = new Sitemap_Url(
						array('domain' => $domain, 'url' => rew_strip($dat['url_extern']) )
					);

					if( !isset($urls[$lng]) ){
						$urls[$lng] = array(
							'categories'	=>	array(),
							'products'		=>	array(),
							'others'		=>	array()
						);
					}
					$t =& $urls[$lng];

					if( !$config['sitemap_catalog'] && $dat['url_extern']=='/catalogue/' ){
						continue;
					}

					$categoryID = null;
					if( preg_match('/^'.str_replace('/', '\/', $urlCategory).'\?cat=([0-9]+)$/', $intern, $match) ) {
						if( $config['sitemap_catalog'] ){
							$categoryID = $match[1];

							// Vérifie que la catégorie est publiée
							if( !isset($categories[$categoryID]) ){
								continue;
							}

							// Vérifie que l'url existe bien
							if( !Sitemap::checkUrl( $domain.$dat['url_extern'], false, $domain, true, false ) ){
								$warnings[$lng][] = $dat['url_extern'];
								continue;
							}

							// Ajout des images associées à la catégorie
							$images = prd_cat_images_get($categoryID);
							if( !$images ){
								throw new exception('Echec de prd_cat_images_get'."\n".ria_mysql_errno()."\n".mysql_error());
							}
							while( $dat2 = ria_mysql_fetch_array($images) ){
								if ($dat2['id']){
									$url->addImage(
										new Sitemap_Image( $config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$dat2['id'].'.jpg' )
									);
								}
							}

							$t['categories'][$categoryID] = $url;
						}
					} elseif(
						($urlProduct!='/catalog/product.php' && preg_match('/^'.str_replace('/', '\/', '/catalog/product.php').'\?cat=([0-9]+)&prd=([0-9]+)$/', $intern, $match))
						|| preg_match('/^'.str_replace('/', '\/', $urlProduct).'\?cat=([0-9]+)&prd=([0-9]+)$/', $intern, $match)
					) {
						if( $config['sitemap_catalog'] ){
							// vérifie que la catégorie est publiée
							$categoryID = $match[1];
							if (!isset($categories[$categoryID])){

								if( $categoryID == $dat['url_obj_id_0'] || !is_numeric($dat['url_obj_id_0']) || $dat['url_obj_id_0'] <= 0 ){
									continue;
								}
								$categoryID = $dat['url_obj_id_0'];

								if( !isset($categories[$categoryID]) ){
									continue;
								}
							}

							// produit
							$productID = $match[2];
							$prd = Sitemap::getProduct( $productID );
							if( !$prd ){

								if( $productID == $dat['url_obj_id_1'] || !is_numeric($dat['url_obj_id_1']) || $dat['url_obj_id_1'] <= 0 ){
									continue;
								}
								$productID = $dat['url_obj_id_1'];
								$prd = Sitemap::getProduct( $productID );

								if( !$prd ){
									continue;
								}
							}

							// check si la classification est canonique ou null
							$canonical_url = prd_classify_get_canonical_url( $productID ) ;
							if( $canonical_url && $canonical_url != $dat['url_extern'] ) continue;


							// vérifie que l'url existe bien
							if( !Sitemap::checkUrl( $domain.$dat['url_extern'], false, $domain, true, false ) ){
								$warnings[$lng][] = $dat['url_extern'];
								continue;
							}

							// Ajout de l'image principale du produit
							if( $prd['img_id'] ){
								$url->addImage(
									new Sitemap_Image( $config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$prd['img_id'].'.jpg' )
								);
							}

							// Ajout des images secondaires du produit
							$images = prd_images_get( $productID );
							if( !$images ){
								throw new exception('Echec de prd_images_get'."\n".ria_mysql_errno()."\n".mysql_error());
							}
							while( $dat2 = ria_mysql_fetch_assoc($images) ){
								$url->addImage(
									new Sitemap_Image( $config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$dat2['id'].'.jpg' )
								);
							}

							$t['products'][$categoryID][$productID] = $url;
						}
					} elseif (
						$urlStore!==false && preg_match('/^'.str_replace('/', '\/', $urlStore).'\?str=([0-9]+)$/', $intern, $match)
					) {
						// vérifie que le magasin est publié
						if( !isset($stores[ $match[1] ]) ){
							continue;
						}

						// vérifie que l'url existe bien
						if( !Sitemap::checkUrl( $domain.$dat['url_extern'], false, $domain, true, false ) ){
							$warnings[$lng][] = $dat['url_extern'];
							continue;
						}

						// autre
						$t['others'][] = $url;
					} else {
						// vérifie que l'url existe bien
						if( !Sitemap::checkUrl( $domain.$dat['url_extern'], false, $domain, true, false ) ){
							$warnings[$lng][] = $dat['url_extern'];
							continue;
						}

						// autre
						$t['others'][] = $url;
					}
				}
				unset($t);

				// Envoi un email aux admins si des urls génèrent des erreurs
				/*if (count($warnings) > 0) {
					$email = new Email();
					$email->setSubject($config['site_name'].' - Erreur 404 dans rew_rewritemap');
					$email->setFrom('root');
					$email->addTo('<EMAIL>');
					$email->addCC('<EMAIL>');

					$h = '';
					$h .= 'Cet email a été généré suite à la création des sitemaps.<br />';
					$h .= 'Les urls suivantes ne devraient pas être présentes dans rew_rewritemap :<br /><br />';
					$h .= implode('<br />' . "\n", $warnings);

					$email->addHtml($h);
					$email->send();
				}*/

				unset($warnings);
				Sitemap::$_tree = $r;
			}
			return Sitemap::$_tree;
		}

		/** Cette fonction permet de réinitialisé les tableaux contenant les objects pour chaque classe
		 *	@return void
		 */
		public static function resetClassLists(){
			Sitemap::$_url_cache = null;
			Sitemap::$_products_list = null;
			Sitemap::$_categories_list = null;
			Sitemap::$_brands_list = null;
			Sitemap::$_stores_list = null;
			Sitemap::$_cms_list = null;
			Sitemap::$_sector_list = null;
			Sitemap::$_doc_type_list = null;
			Sitemap::$_faq_cat_list = null;
			Sitemap::$_news_list = null;
			Sitemap::$_news_cat_list = null;
			Sitemap::$_faq_qst_list = null;
		}

		/**	Cette méthode permet la vérification d'une url donnée. Elle procède par chargement
		 *	de l'url pour s'assurer que le serveur retourne bien 200 - OK.
		 *
		 *	@param string $url Obligatoire, url à vérifier
		 *	@param bool $no_404 Facultatif, par défaut si l'url provoque une redirection (301 ou 302), cette fonction retournera false, mettre True pour seulement vérifier qu'il ne s'agit pas d'une 404
		 *	@param $domain Facultatif, domaine si différent du domaine par défaut lié à la configuration du client
		 *	@param $load_all Facultatif, par défaut ne chargera qu'un seul objet en mémoire si true chargera tous les objets pour une certaine classe
		 *	@param bool $return_final_url Facultatif, l'url finale, après redirection, doit-elle être retournée ? La valeur par défaut est true.
		 *	@param $used_objs Optionnel, par défaut on recherche les objects publiés dans la base, mettre false pour toujours tests l'url avec CURL
		 *
		 *	@return bool True si le code de réponse HTTP retourné par le serveur au chargement de la page est 200 - OK, False dans le cas contraire
		 */
		public static function checkUrl( $url, $no_404=false, $domain='', $load_all=false, $return_final_url=true, $used_objs=true ){

			if( trim($url) == '' ){
				return false;
			}

			global $config;

			if( trim($domain) == '' ){
				$domain = $config['site_url'];
			}

			$url = preg_replace('/([^:])\/\//','$1/',$url);

			$url = str_replace($domain, '', $url);

			if( substr($url, 0, 1) != '/' ){
				$url = '/'.$url;
			}

			if( $url == '/' ){
				return true;
			}

			if ($used_objs) {
				if( !Sitemap::$_url_cache || !$load_all ){
					$sql = '
						select url_extern as url,
							url_cls_id as cls_id,
							url_obj_id_0 as obj_id_0,
							url_obj_id_1 as obj_id_1,
							url_obj_id_2 as obj_id_2
						from rew_rewritemap
						where url_tnt_id='.$config['tnt_id'].'
							and (url_wst_id = 0 or url_wst_id='.$config['wst_id'].')
							and ifnull(url_cls_id, 0) != 0
						order by url_wst_id desc
					';
					$rUrl = ria_mysql_query($sql);

					if( $rUrl && ria_mysql_num_rows($rUrl) ){
						Sitemap::$_url_cache = array();
						while( $rew = ria_mysql_fetch_assoc($rUrl) ){
							Sitemap::$_url_cache[$rew['url']] = $rew;
						}
					}
				}

				$url2 = $url;

				if( isset($config['rew_strip']) && $config['rew_strip'] ){
					$url2 = $config['rew_strip'].$url;
					$url2 = preg_replace('/([^:])\/\//','$1/',$url2);
				}

				$found = false;

				if( Sitemap::$_url_cache && is_array(Sitemap::$_url_cache) && !empty(Sitemap::$_url_cache) ){
					if( array_key_exists( $url, Sitemap::$_url_cache) ){
						$found=true;
					}
					elseif( array_key_exists( $url2, Sitemap::$_url_cache) ){
						$found=true;
						$url = $url2;
					}
				}

				if( $found ){

					$cat_root = isset($config['cat_root']) ? $config['cat_root'] : 0;
					if (isset($config['sitemap_categorie_root']) && is_numeric($config['sitemap_categorie_root']) && $config['sitemap_categorie_root'] > 0) {
						$cat_root = $config['sitemap_categorie_root'];
					}
					$rew = Sitemap::$_url_cache[$url];

					switch( $rew['cls_id'] ){
						case CLS_PRODUCT:{
							if (!Sitemap::$_products_list || !$load_all) {
								Sitemap::$_products_list = array();
								$r_prd = prd_products_get_simple( ($load_all ? 0 : $rew['obj_id_1']), '', true, $cat_root, true, false, false, false, array('childs'=>true) );
								if ($r_prd && ria_mysql_num_rows($r_prd)) {
									while ($prd = ria_mysql_fetch_assoc($r_prd)) {
										Sitemap::$_products_list[ $prd['id'] ] = $prd['id'];
									}
								}
							}
							if( array_key_exists( $rew['obj_id_1'], Sitemap::$_products_list ) ){
								if( $return_final_url ){
									$info = array();
									$info['url'] = rew_strip(Sitemap::getFinalUrl($url));
									return $info;
								}
								return true;
							}
							return false;
							//return array_key_exists( $rew['obj_id_1'], Sitemap::$_products_list );
						}
						case CLS_CATEGORY:{
							if (!Sitemap::$_categories_list || !$load_all) {
								Sitemap::$_categories_list = array();

								if ($cat_root) {
									$r_cat = prd_categories_get( ($load_all ? 0 : $rew['obj_id_0']), true, $cat_root, '', false, false, null, false, array(), true, false, false, false, false, false, false, false, false, null, false, true );
								}else{
									$r_cat = prd_categories_get_all( true );
								}

								if ($r_cat && ria_mysql_num_rows($r_cat)) {
									while ($cat = ria_mysql_fetch_assoc($r_cat)) {
										Sitemap::$_categories_list[ $cat['id'] ] = $cat['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_categories_list );
						}
						case CLS_BRAND:{
							if (!Sitemap::$_brands_list || !$load_all) {
								Sitemap::$_brands_list = array();

								$r_brd = prd_brands_get( ($load_all ? 0 : $rew['obj_id_0']), true, '', '', false, true, false, false, false, false, false, false, false, true );
								if ($r_brd && ria_mysql_num_rows($r_brd)) {
									while ($brd = ria_mysql_fetch_assoc($r_brd)) {
										Sitemap::$_brands_list[ $brd['id'] ] = $brd['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_brands_list );
						}
						case CLS_STORE:{
							if (!Sitemap::$_stores_list || !$load_all) {
								Sitemap::$_stores_list = array();

								$r_str = dlv_stores_get( ($load_all ? 0 : $rew['obj_id_0']) );
								if ($r_str && ria_mysql_num_rows($r_str)) {
									while ($str = ria_mysql_fetch_assoc($r_str)) {
										if (isset($config['sitemap_store_sales']) && is_array($config['sitemap_store_sales']) && count($config['sitemap_store_sales'])) {
											$isok = false;

											$r_type = dlv_sales_types_get(0, $str['id']);
											if ($r_type) {
												while ($type = ria_mysql_fetch_assoc($r_type)) {
													if (in_array($type['id'], $config['sitemap_store_sales'])) {
														$isok = true;
														break;
													}
												}
											}

											if (!$isok) {
												continue;
											}
										}

										Sitemap::$_stores_list[ $str['id'] ] = $str['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_stores_list );
						}
						case CLS_SECTOR:{
							if (!Sitemap::$_sector_list || !$load_all) {
								Sitemap::$_sector_list = array();

								$r_sct = dlv_sectors_get( ($load_all ? 0 : $rew['obj_id_0']) );
								if ($r_sct && ria_mysql_num_rows($r_sct)) {
									while ($sct = ria_mysql_fetch_assoc($r_sct)) {
										Sitemap::$_sector_list[ $sct['id'] ] = $sct['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_sector_list );
						}
						case CLS_CMS:{
							if (!Sitemap::$_cms_list || !$load_all) {
								Sitemap::$_cms_list = array();

								$r_cms = cms_categories_get( ($load_all ? 0 : $rew['obj_id_0']), $config['wst_id'], true );
								if ($r_cms && ria_mysql_num_rows($r_cms)) {
									while ($cms = ria_mysql_fetch_assoc($r_cms)) {
										Sitemap::$_cms_list[ $cms['id'] ] = $cms['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_cms_list );
						}
						case CLS_TYPE_DOCUMENT:{
							if (!Sitemap::$_doc_type_list || !$load_all) {
								Sitemap::$_doc_type_list = array();

								$r_type = doc_types_get( ($load_all ? 0 : $rew['obj_id_0']) );
								if ($r_type && ria_mysql_num_rows($r_type)) {
									while ($type = ria_mysql_fetch_assoc($r_type)) {
										Sitemap::$_doc_type_list[ $type['id'] ] = $type['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_doc_type_list );
						}
						case CLS_FAQ_CAT:{
							if (!Sitemap::$_faq_cat_list || !$load_all) {
								Sitemap::$_faq_cat_list = array();

								$r_faq_cat = faq_categories_get( ($load_all ? 0 : $rew['obj_id_0']), true );
								if ($r_faq_cat && ria_mysql_num_rows($r_faq_cat)) {
									while ($faq_cat = ria_mysql_fetch_assoc($r_faq_cat)) {
										Sitemap::$_faq_cat_list[ $faq_cat['id'] ] = $faq_cat['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_faq_cat_list );
						}
						case CLS_FAQ_QST:{
							if (!Sitemap::$_faq_qst_list || !$load_all) {
								Sitemap::$_faq_qst_list = array();

								$r_faq_qst = faq_questions_get( ($load_all ? 0 : $rew['obj_id_1']), ($load_all ? 0 : $rew['obj_id_0']), true );
								if ($r_faq_qst && ria_mysql_num_rows($r_faq_qst)) {
									while ($faq_qst = ria_mysql_fetch_assoc($r_faq_qst)) {
										Sitemap::$_faq_qst_list[ $faq_qst['id'].'-'.$faq_qst['cat_id'] ] = $faq_qst['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_1'].'-'.$rew['obj_id_0'], Sitemap::$_faq_qst_list );
						}
						case CLS_NEWS:{
							if (!Sitemap::$_news_list || !$load_all) {
								Sitemap::$_news_list = array();

								$r_news = news_get( ($load_all ? 0 : $rew['obj_id_0']), true, null, 0, $config['wst_id'] );
								if ($r_news && ria_mysql_num_rows($r_news)) {
									while ($news = ria_mysql_fetch_assoc($r_news)) {
										Sitemap::$_news_list[ $news['id'] ] = $news['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_news_list );
						}
						case CLS_NEWS_CAT:{
							if (!Sitemap::$_news_cat_list || !$load_all) {
								Sitemap::$_news_cat_list = array();

								$r_news_cat = news_categories_get( ($load_all ? 0 : $rew['obj_id_0']), true );
								if ($r_news_cat && ria_mysql_num_rows($r_news_cat)) {
									while ($news_cat = ria_mysql_fetch_assoc($r_news_cat)) {
										Sitemap::$_news_cat_list[ $news_cat['id'] ] = $news_cat['id'];
									}
								}
							}

							return array_key_exists( $rew['obj_id_0'], Sitemap::$_news_cat_list );
						}
					}
				}
			}

			$url = $domain.$url;

			$curl_extra_header=false;
			if (preg_match("/^https:/",$url)) {
				$url = preg_replace("/^https:/","http:",$url);
				$curl_extra_header = array( "X-Forwarded-Proto: https", "X-Forwarded-By: pound" );
			}

			$url = preg_replace('/([^:])\/\//','$1/',$url);
			$url = rew_strip($url);
			if (! ($c = curl_init())) throw new exception('Erreur curl_init !');
			curl_setopt($c, CURLOPT_URL, $url.(preg_match('/\?.+/', $url) ? '&testadminerror404=1' : '?testadminerror404=1'));
			curl_setopt($c, CURLOPT_FOLLOWLOCATION, false);
			curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($c, CURLOPT_CONNECTTIMEOUT, 5);
			if (is_array($curl_extra_header)) {
				curl_setopt($c, CURLOPT_HTTPHEADER, $curl_extra_header);
			}
			curl_setopt($c, CURLOPT_NOBODY, true);
			$r = curl_exec($c);
			$infos = curl_getinfo($c);
			curl_close($c);

			if( !$no_404 ){
				if( !($infos['http_code'] == 200) ){
					return false;
				}
			}else{
				if( $infos['http_code'] == 404 ){
					return false;
				}
			}

			return $infos;
		}

		public static function getFinalUrl( $url_extern ){

			if( !is_string($url_extern) || trim($url_extern) == '' ){
				return false;
			}

			global $config;

			$count = 0;

			$sql_orderby = ' url_code asc ';

			if (isset($config['i18n_lng_used'], $config['i18n_lng']) && is_array($config['i18n_lng_used']) && count($config['i18n_lng_used'])) {
				$sql_orderby .= ', case when url_lng_code="'.$config['i18n_lng'].'" then 0 ';

				$cpt = 1;
				foreach ($config['i18n_lng_used'] as $lng) {
					if ($lng == $config['i18n_lng']) {
						continue;
					}

					$sql_orderby .= ' when url_lng_code="'.$lng.'" then '.$cpt.' ';
					$cpt++;
				}

				$sql_orderby .= ' end asc ';
			}

			while( $count < 5 ){
				$sql = '
					select url_code as code, url_extern as extern, url_intern as intern
					from rew_rewritemap
					where url_tnt_id='.$config['tnt_id'].'
						and url_extern = "'.$url_extern.'"
					order by '.$sql_orderby.'
					limit 1
				';

				$r = ria_mysql_query($sql);

				if( !$r || !ria_mysql_num_rows($r) ){
					return false;
				}

				$url = ria_mysql_fetch_assoc($r);

				if( $url['code'] == 200 ){
					return $url['extern'];
				}else{
					$url_extern = $url['intern'];
				}
				$count++;
			}

			return $url_extern;
		}

	// Méthodes

		/**	Constructeur
		 *	@param $param Facultatif, tableau contenant les paramètres suivantes :
		 *			-	location : Chemin du fichier
		 *			-	url : Url du sitemap
		 */
		public function __construct( $param = array() ){
			global $config;

			$this->_sitemaps =  array();
			$this->_urls = array();

			$this->setDomain( array_key_exists('domain', $param) ? $param['domain'] : wst_websites_languages_get_url($config['wst_id'], 'fr') );
			if( array_key_exists('location', $param) ){
				$this->setLocation( $param['location'] );
			}
			$this->setUrl( array_key_exists('url', $param) ? $param['url'] : $this->getLocation() );

			// Définit la date/heure de création du sitemap
			$this->_time = time();
		}

		/** Permet l'ajout d'un Sitemap à un sitemap index
		 *	@param $sitemap Nouveau sitemap à ajouter à l'index.
		 *	@return l'instance
		 */
		public function addSitemap( $sitemap ){
			if( gettype( $sitemap )!=='object' ){
				error_log( 'Sitemap::addSitemap : l\'argument $sitemap n\'est pas une instance de classe Sitemap' );
				return $this;
			}
			if( get_class($sitemap)!=='Sitemap' ){
				error_log( 'Sitemap::addSitemap : l\'argument $sitemap n\'est pas une instance de classe Sitemap' );
				return $this;
			}
			$this->_sitemaps[] = $sitemap;
			return $this;
		}

		/** Ajoute une URL à la liste
		 *	@param string $url Url à ajouter au sitemap
		 *	@return object L'instance
		 */
		public function addUrl( $url ){
			$this->_urls[] = $url;
			return $this;
		}

		/** Accesseur en lecture sur _domain
		 *	@return Valeur de _domain
		 */
		public function getDomain(){
			return $this->_domain;
		}

		/** Retourne le nom du fichier sur disque
		 *	@return string Le nom du fichier sur disque
		 */
		public function getLocation(){
			return $this->_location;
		}

		/** Accesseur en lecture sur _sitemaps
		 *	@return Valeur de _sitemaps
		 */
		public function getSitemaps(){
			return $this->_sitemaps;
		}

		/** Accesseur en lecture sur _url
		 *	@return Valeur de _url
		 */
		public function getUrl(){
			return $this->_url;
		}

		/** Accesseur en lecture sur _time
		 *	@return Valeur de _time
		 */
		public function getTime(){
			return $this->_time;
		}

		/** Accesseur en lecture sur _urls
		 *	@return Valeur de _urls
		 */
		public function getUrls(){
			return $this->_urls;
		}

		/**	Cette méthode renvoie si le sitemap a des images
		 *	@return bool True si le sitemap a des images, False sinon
		 */
		public function hasImages(){
			$urls = $this->getUrls();
			foreach( $urls as $url ){
				if( $url->hasImages() ){
					return true;
				}
			}
			return false;
		}

		/**	Cette méthode renvoie si le sitemap a des vidéos
		 *	@return bool True si le sitemap a des vidéos, False sinon
		 */
		public function hasVideos(){
			$urls = $this->getUrls();
			foreach( $urls as $url ){
				if( $url->hasVideos() ){
					return true;
				}
			}
			return false;
		}

		/** Permet la modification du nom de domaine
		 *	@param $domain Nom de domaine
		 *	@return object L'instance
		 */
		public function setDomain( $domain ){
			$this->_domain = $domain;
			return $this;
		}

		/** Permet la modification du nom du fichier (utilisé pour l'enregistrement sur disque)
		 *	@param $location Nouvel emplacement du fichier sitemap
		 *	@return object L'instance
		 */
		public function setLocation( $location ){
			$this->_location = $location;
			return $this;
		}

		/** Permet la modification de l'url par laquelle le sitemap sera accessible depuis l'extérieur
		 *	@param $url Nouvelle Url du fichier sitemap
		 *	@return object L'instance
		 */
		public function setUrl( $url ){
			$this->_url = $url;
			return $this;
		}

		/**	Enregitre le sitemap et ses fils en cascade
		 *	@param $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			-	index : Booléen permettant de déterminer s'il s'agit du fichier d'index (sitemap.xml) ou non
		 *	@todo cette méthode devrait être appelée dans le contexte d'une transaction !
		 */
		public function save( $param = array() ){
			global $config;
			$index = (array_key_exists('index', $param)) ? $param['index'] : false;

			// enregistre les fils en cascade
			$sitemaps = $this->getSitemaps();
			foreach( $sitemaps as $sitemap ){
				$sitemap->save();
			}

			$exit = false;

			// si index et multilingue, on doit transmettre un sitemap par sous domaine
			if( $index ){
				$exit = count(Sitemap::getLangs()) > 1;
			}

			if( !$exit ){
				// Enregistre le fichier xml sur disque
				$location = $this->getLocation();

				$filename = Sitemap::getDirname().'/'.$location;
				if( !($f = fopen($filename, 'w')) ){
					throw new exception('Erreur a l\'ouverture du fichier '.$filename);
				}
				if( fwrite($f, $this)===false ){
					throw new exception('Erreur fwrite du fichier '.$filename);
				}
				fclose($f);

				// Enregistre les nouvelles urls
				$sql = 'insert into rew_rewritemap (url_tnt_id, url_lng_code, url_wst_id, url_extern, url_intern, url_code, url_public) VALUES ('.$config['tnt_id'].', \''.$config['i18n_lng'].'\', '.$config['wst_id'].', \'/'.$this->getUrl().'\', \'/'.$config['sitemap_dirname'].'/'.$location.'\', 200, 0);';
				if( !ria_mysql_query($sql) ){
					throw new exception('Echec de la requete '.$sql."\n".ria_mysql_errno()."\n".mysql_error());
				}
			}
		}

		/**	Cette méthode renvoie l'union de 2 sitemaps
		 *	@param $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			-	sitemap	: Sitemap à unir
		 *	@return object L'instance
		 */
		public function union( $param ){
			$sitemap = $param['sitemap'];

			if (!$sitemap) {
				return false;
			}

			$sitemaps = $sitemap->getSitemaps();
			foreach( $sitemaps as $t ){
				$this->addSitemap( $t );
			}

			$urls = $sitemap->getUrls();
			foreach( $urls as $url ){
				$this->addUrl( $url );
			}

			return $this;
		}

		/**	Cette méthode renvoie la code xml du sitemap
		 *	@return string Chaîne de caractères représentant le code XML du sitemap
		 */
		public function __toString(){
			global $config;

			$sitemaps = $this->getSitemaps();
			$countSitemaps = count($sitemaps);

			$urls = $this->getUrls();
			$countUrls = count($urls);

			if ($countSitemaps && $countUrls) throw new exception('Un sitemap ne peut pas déclarer des urls et d\'autres sitemaps en même temsp !');

			$t = '';
			$t .= '<?xml version="1.0" encoding="UTF-8"?>'."\n";
				// sitemaps
				if ($countSitemaps > 0) {
					$t .= "\t".'<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"'."\n";
					$t .= "\t\t".'xmlns:xsi="https://www.w3.org/2001/XMLSchema-instance"'."\n";
					$t .= "\t\t".'xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9'."\n";
					$t .= "\t\t".'http://www.sitemaps.org/schemas/sitemap/0.9/siteindex.xsd">'."\n";

					foreach( $sitemaps as $sitemap ){
						$url = preg_replace('/([^:])\/\//','$1/',$sitemap->getDomain().'/'.$sitemap->getUrl());
						$t .= "\t\t".'<sitemap>'."\n";
							$t .= "\t\t\t".'<loc>'.$url.'</loc>'."\n";
							$t .= "\t\t\t".'<lastmod>'.date(DATE_ATOM, $sitemap->getTime()).'</lastmod>'."\n";
						$t .= "\t\t".'</sitemap>'."\n";
					}
					$t .= "\t".'</sitemapindex>'."\n";
				}

				// Urls
				if( $countUrls > 0 ){
					$t .= "\t".'<urlset';
					$t .= "\n\t\t".'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"';
					$t .= "\n\t\t".'xmlns:xsi="https://www.w3.org/2001/XMLSchema-instance"';
					$t .= "\n\t\t".'xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9';
					$t .= "\n\t\t".'http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"';

					if( $this->hasImages() ){
						$t .= "\n\t\t".'xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"';
					}
					if( $this->hasVideos() ){
						$t .= "\n\t\t".'xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"';
					}
					$t .= '>'."\n";

					foreach( $urls as $url ){
						$t .= $url->toString( "\t\t" );
					}
					$t .= "\t".'</urlset>'."\n";
				}

			return $t;
		}

}

/**	\brief Représente une url à intégrer au sitemap
 */
class Sitemap_Url {

	// Attributs

		private	$_url; ///< Url destinée à être ajoutée au sitemap
		private	$_images; ///< Liste des images associées à cette url
		private	$_videos; ///< Liste des vidéos associées à cette url

	// Méthodes

		/**	Constructeur
		 *	@param $param Obligatoire, tableau contenant les paramètres suivantes :
		 *			- url : Url de la page
		 *			- images : Images de la page
		 *			- videos : Vidéos de la page
		 */
		public function __construct( $param ){
			$this->setDomain( $param['domain'] );
			$this->setUrl( $param['url'] );

			$this->_images = array();
			$this->_videos = array();
		}

		/** Ajoute une image en tant que contenu associé à une url principale
		 *	@param $image L'image à ajouter (instance de classe Sitemap_Image)
		 *	@return l'instance
		 */
		public function addImage( $image ){
			if( gettype( $image )!=='object' ){
				error_log( 'Sitemap_Url::addImage : l\'argument $image n\'est pas une instance de classe Sitemap_Image' );
				return $this;
			}
			if( get_class($image)!=='Sitemap_Image' ){
				error_log( 'Sitemap_Url::addImage : l\'argument $image n\'est pas une instance de classe Sitemap_Image' );
				return $this;
			}
			$this->_images[] = $image;
			return $this;
		}

		/** Ajoute une vidéo à la liste
		 *	@param $video La vidéo à ajouter
		 *	@return l'instance
		 */
		public function addVideo( $video ){
			$this->_videos[] = $video;
			return $this;
		}

		/** Fourni le nom de domaine associé à l'url
		 *	@return string Le nom de domaine de cette url
		 */
		public function getDomain(){
			return $this->_domain;
		}

		/** Fourni un accès à la liste des images associées à une url
		 *	@return array Tableau contenant la liste des images associées à cette url
		 */
		public function getImages(){
			return $this->_images;
		}

		/** Fourni un accès à l'url absolue
		 *	@return Retourne l'url à faire apparaître dans le sitemap
		 */
		public function getUrl(){
			return $this->_url;
		}

		/** Fourni un accès à la liste des vidéos associées à cette url
		 *	@return array Retourne un tableau des vidéos associées à cette url
		 */
		public function getVideos(){
			return $this->_videos;
		}

		/** Détermine si l'url contient des images
		 *	@return bool True si elle en contient, False dans le cas contraire
		 */
		public function hasImages(){
			return count($this->getImages()) > 0;
		}

		/** Détermine si l'instance contient des vidéos
		 *	@return bool True si elle en contient, False sinon
		 */
		public function hasVideos(){
			return count($this->getVideos()) > 0;
		}

		/** Permet la modification du nom de domaine
		 *	@param $domain Nom de domaine
		 *	@return object L'instance
		 */
		public function setDomain( $domain ){
			$this->_domain = $domain;
			return $this;
		}

		/** Permet la modification de l'url à inclure dans le sitemap. Cette url doit être absolue
		 *	et ne pas contenir le nom de domaine (elle doit débuter au premier /)
		 *
		 *	@param $url Nouvelle url
		 *	@return object L'instance
		 */
		public function setUrl( $url ){
			$this->_url = $url;
			return $this;
		}

		/**	Cette méthode renvoie le code xml correspondant au fragment nécessaire pour décrire l'url dans le sitemap
		 *	@param $tab Facultatif, Tabulations (pour la mise en forme)
		 *	@return l'objet converti en chaîne de caractère (fragment xml au format Sitemap)
		 */
		public function toString( $tab = "\t" ){
			global $config;

			$r = '';

			if( $this->getUrl()!='' ){
				$url = preg_replace('/([^:])\/\//','$1/',$this->getDomain().$this->getUrl());
				$r .= $tab.'<url>'."\n";
				$r .= $tab."\t".'<loc>'.$url.'</loc>'."\n";

				$images = $this->getImages();
				foreach( $images as $img ){
					$url_img_loc = str_replace($config['site_url'], $this->getDomain(), $img->getSrc());

					if( preg_match('/^https?:\/\//i', $url_img_loc, $matches) ){
						$url_img_loc = preg_replace('/^https?:\/\//i', '', $url_img_loc);
					}
					$url_img_loc = preg_replace('/\/{2,}/i', '/', $url_img_loc);

					if( is_array($matches) && count($matches) ){
						$url_img_loc = $matches[0].$url_img_loc;
					}

					$r .= $tab."\t".'<image:image>'."\n";
					$r .= $tab."\t\t".'<image:loc>'.$url_img_loc.'</image:loc>'."\n";
					$r .= $tab."\t".'</image:image>'."\n";
				}

				$r .= $tab.'</url>'."\n";

			}

			return $r;
		}

}

/**	\brief Représente une image à intégrer au sitemap
 */
class Sitemap_Image {

	// Attributs

		private	$_src;

	// Méthodes

		/**	Constructeur
		 *	@param $src Obligatoire, Adresse de l'image (url absolue sans le nom de domaine). Doit débuter par /
		 */
		public function __construct( $src ){
			$this->_src = $src;
		}

		/**	Cette méthode permet d'obtenir l'url de l'image
		 *	@return Retourne l'url absolue de l'image, sans le nom de domaine
		 */
		public function getSrc(){
			return $this->_src;
		}

}

/**	\brief Exception lorsqu'une catégorie est récupérée alors qu'elle n'est pas publiée
 */
class SitemapExceptionUnpublished extends exception {
}

/// @}

// \endcond

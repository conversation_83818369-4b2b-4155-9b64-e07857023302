<?php

/**
 * \class FieldsHelper
 * \brief Cette classe permet l'aide de la gestion des champs sur une ligne d'import
 * \ingroup ImportModule
 * 
 * Pour chaque ligne de l'import on va traiter tous les champs même ceux synchronisés
 * Puis on sépare les champs synchronisés des champs à mettre à jour si l'objet ne permet pas la mise à jour
 */
class FieldsHelper{
	public $fields = array(); ///> Tableau des champs à mettre à jour, les champs synchronisés sont exclues
	public $values = array(); ///> Tableau des valeus associé au champs
	public $sync_fields = array(); ///> Tableau des champs qui sont synchronisés
	public $all_fields = array(); ///> Tableau de tout les champs, les champs synchronisés sont inclues
	public $action = null; ///> l'action réalisé par l'import
	public $can_write = false; ///> Détermine si on à les droits pour mettre à jour l'objet
	public $error_count = 0; ///> Nombre d'erreur survenu sur la ligne
	/**
	 * Constructeur de la class
	 *
	 * @param string $action Détermine l'action de l'import
	 * @param boolean $can_write Détermine si pour la ligne on peut écrire dans l'objet
	 */
	public function __construct($action, $can_write){
		$this->action = $action;
		$this->can_write = $can_write;
	}
	/** Cette fonction permet d'ajouter une valeur et un champ
	 *	@param string $field Nom du champ a modifier
	 *	@param string $value Valeur déjà échappé a insérer en base
	 *	@return void Ne retourne rien
	 */
	public function addFieldValue($field, $value){
		$this->fields[] = $field;
		$this->values[] = $value;
	}
	/** Cette fonction permet de supprimer une paire champ/valeur
	 *	@param int $index
	 *	@return void
	 */
	public function removeFieldValue($index){
		if (
			array_key_exists($index, $this->fields)
			&& array_key_exists($index, $this->values)
		) {
			unset($this->fields[$index]);
			unset($this->values[$index]);
		}
	}
	/**
	 * Cette fonction permet de gérer les champs synchronisés
	 *
	 * @param array $fields Tableau des champs qui doivent être utilisé pour l'update
	 * @param mixed $check_value la valeur brute validé pour ce type de champs
	 * @param array $map Tableau représentant le mapping actuel
	 * @param boolean $exclude_from_sync Facultatif, détermine si on ajoute ou non dans les champs synchronisés
	 * @return boolean Retourne true si il faut exclure le dernier index de fields et values
	 */
	public function manageFields($fields, $check_value, $map, $exclude_from_sync = false){
		$latest_inserted_index = $this->getLastestIndex($fields);

		if (!array_key_exists($latest_inserted_index, $fields)) {
			return false;
		}

		$latest_inserted_field = $fields[$latest_inserted_index];
		$this->all_fields[] = $latest_inserted_field;

		if (!$this->action == 'upd' || $this->can_write || $map['is_sync'] == '0') {
			return false;
		}

		if ($exclude_from_sync) {
			return false;
		}

		$synced_value = $this->getSyncValueFromCheckValue($check_value);

		if (null === $synced_value) {
			return false;
		}

		$this->sync_fields[] = array(
			'index' => $latest_inserted_index,
			'field' => $latest_inserted_field,
			'name' => $map['name'],
			'pos' => $map['pos'],
			'code' => $map['code'],
			'value' => $synced_value,
		);

		return true;
	}

	/**
	 * Cette fonction permet de vérifier les valeurs de l'objet qui va être importé avec les valeurs des champs noté comme synchronisés.
	 *
	 * @param array $object Tableau représentant l'objet à mettre à jour
	 * @param int $line_id Identifiant de la ligne du rapport
	 * @param int $report_id Identifiant du rapport
	 * @return void
	 */
	public function checkSyncFieldsAgainstObject(array $object, $line_id, $report_id){
		if ($this->can_write || count($this->sync_fields) == 0) {
			return;
		}

		foreach ($this->sync_fields as $field) {
			if (!array_key_exists($field['field'], $object)) {
				continue;
			}

			if ($field['value'] != $object[$field['field']]) {
				ipt_row_errors_add($line_id, $report_id, 'ERR0009', ipt_get_error_msg('ERR0009', 'Le champ ' . $field['name'] . ' est '), $field['pos'], 'error');
				$this->error_count++;
			}
		}
	}

	/**
	 * Cette fonction retourne le nombre d'erreur collecté sur cette ligne
	 *
	 * @return int Retourne le nombre de ligne
	 */
	public function getErrorCount(){
		return $this->error_count;
	}

	/**
	 * Cette fonction permet de récupérer le dernier index du tableau passé en paramètre
	 *
	 * @param array $fields
	 * @return int|string Retourne le dernier index du tableau
	 */
	public function getLastestIndex(array $fields){
		$tmp = $fields;
		end($tmp);
		$latest_inserted_index = key($tmp);

		return $latest_inserted_index;
	}

	/**
	 * Cette fonction permet de formater la valeur brute pour être comparer à la valeur en base
	 *
	 * @param mixed $check_value Valeur brute déjà vérifié en fonction du type de donnée attendu dans le mapping
	 * @return mixed Retourne une valeur formaté en fonction de son type
	 */
	private function getSyncValueFromCheckValue($check_value){
		$synced_value = null;

		if (is_string($check_value)) {
			$synced_value = $check_value;
		} elseif (is_numeric($check_value)) {
			$synced_value = $check_value;
		} elseif (is_bool($check_value)) {
			if ($check_value) {
				$synced_value = 1;
			}else{
				$synced_value = 0;
			}
		}

		return $synced_value;
	}
}

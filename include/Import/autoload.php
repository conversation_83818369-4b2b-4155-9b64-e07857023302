<?php

/**
 * Autoloader spécifique au module de l'import
 * @param string $className Nom de la class
 */
function import_service_autoload($className){
	$filename = str_replace("\\", '/', $className) . '.php';
    if (strstr($filename, 'Riashop/Import') ) {
		$filename = str_replace("Riashop/", '', $filename);
		if( file_exists(__DIR__.'/../'.$filename) ) {
			require_once($filename);
		}
    }
}
spl_autoload_register('import_service_autoload');
<?php
namespace Riashop\Import\Orders;

use DateTime;
use RuntimeException;
/**
 * \class OrderInvoiceHandler
 */
class OrderInvoiceHandler
{
	/**
	 * Identifiant de la commande en cours de traitement
	 *
	 * @var integer $current_order
	 */
	private $current_order = null;
	/**
	 * Liste des factures de la commande en cours
	 *
	 * @var array $current_invoices
	 */
	private $current_invoices = [];
	/**
	 * Instance de la nouvelle facture créé lors de l'import
	 * Il ne peux y avoir que une nouvelle facture par commande par import
	 *
	 * @var OrderInvoice $newest_invoice
	 */
	private $newest_invoice = null;
	/**
	 * Mets à jour la commande en cours de traitement dans le système
	 * On réinitialise les factures en cours de traitement
	 *
	 * @param integer $order_id Identifiant de la facture
	 * @return void
	 */
	public function setOrder($order_id)
	{
		if ($order_id != $this->current_order) {
			$this->terminate();
			$this->current_order = $order_id;
		}
	}
	/**
	 * Fonction principale du système elle gère la création et mises à jour de factures pour une commande via l'import
	 *
	 * @param integer $usr_id Identifiant du compte propriétaire de la commande
	 * @param OrderInvoiceLine $Line
	 * @return void
	 */
	public function handleLine($usr_id, OrderInvoiceLine $Line)
	{
		$this->setOrder($Line->orderId());

		$invoice_id = $this->lineExists($Line);

		if ($invoice_id == false) {
			$this->LineToNewInvoice($usr_id, $Line);
		}else{
			$this->LineToExistingInvoice($invoice_id, $Line);
		}
	}
	/**
	 * Ajoute une ligne de facture sur une nouvelle facture
	 * Si pas de nouvelle facture en cours de traitement on la génère
	 *
	 * @param integer $usr_id
	 * @param OrderInvoiceLine $Line
	 * @return boolean Retourne true si succès de l'ajout, sinon false
	 */
	public function LineToNewInvoice($usr_id,OrderInvoiceLine $Line)
	{

		if ($this->newest_invoice == null) {
			$inv_id = $this->newInvoice($usr_id, $Line);

			$OrderInvoice = new OrderInvoice($inv_id, true);

			$this->newest_invoice = $inv_id;

			$this->current_invoices[$inv_id] = $OrderInvoice;
		}else{
			$OrderInvoice = $this->current_invoices[$this->newest_invoice];
		}

		return $OrderInvoice->addLine($Line);
	}
	/**
	 * Gère les lignes pour une facture déjà existante en base
	 * Si la facture n'est pas en mémoire on la charge
	 *
	 * @param integer $invoice_id Identifiant de la facture
	 * @param OrderInvoiceLine $line Identifiant de la ligne
	 * @return boolean Retourne true si l'ajout c'est bien fait, sinon false
	 */
	public function LineToExistingInvoice($invoice_id,OrderInvoiceLine $line)
	{
		if (!array_key_exists($invoice_id, $this->current_invoices)) {
			$OrderInvoice = new OrderInvoice($invoice_id, false);
			$this->current_invoices[$invoice_id] = $OrderInvoice;
		}else{
			$OrderInvoice = $this->current_invoices[$invoice_id];
		}

		return $OrderInvoice->addLine($line);
	}
	/**
	 * Gére la mise à jours des totaux des factures de la commande en cours
	 * Et de la suppression des factures si elle pas de ligne facturé
	 *
	 * @return void
	 */
	public function terminate()
	{
		// on termine chaque facture avec suppressio des lignes inexistante
		// plus mis à jour des totaux
		foreach ($this->current_invoices as $Invoice) {
			$Invoice->terminate();
		}
		// suppression des factures ou il n'y a plus de ligne
		if (!empty($this->current_invoices)) {
			$r_invoices = ord_ord_invoices_get($this->current_order);
			if (ria_mysql_num_rows($r_invoices)) {
				while( $inv = ria_mysql_fetch_assoc($r_invoices)){
					if (!array_key_exists($inv['id'], $this->current_invoices)) {
						ord_invoices_del($inv['id']);
					}
				}
			}
		}
		// reset pour la commande suivante
		$this->current_invoices = [];
		$this->newest_invoice = null;
	}
	/**
	 * Vérifie si pour une ligne de commande il existe une facture en base
	 *
	 * @param OrderInvoiceLine $Line Ligne de facture/commande
	 * @return integer|false Retourne l'identifiant de la commande, false si inexistante
	 */
	private function lineExists(OrderInvoiceLine $Line)
	{
		global $config;
		$select = '
			select prd_inv_id from ord_inv_products
			join ord_invoices on inv_tnt_id=prd_tnt_id and inv_id=prd_inv_id and inv_masked=0
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_ord_id='.$Line->orderId().'
				and prd_id='.$Line->prdId().'
				and prd_line_id='.$Line->lineId().'
		';

		$r_inv = ria_mysql_query($select);

		if (ria_mysql_num_rows($r_inv)) {
			return ria_mysql_result($r_inv, 0, 'prd_inv_id');
		}

		return false;
	}
	/**
	 * Génère une nouvelle facture en base de donnée
	 *
	 * @param integer $usr_id Identifiant de la commande
	 * @param OrderInvoiceLine $Line Ligne de la commande
	 * @return integer Retourne l'identifiant de la commande
	 */
	private function newInvoice($usr_id, OrderInvoiceLine $Line)
	{
		$ord_date = $Line->dateCreated();
		if ($ord_date instanceof DateTime) {
			$date = $ord_date;
		}else{
			$date = new DateTime();
		}
		$inv_id = ord_invoices_add_sage($usr_id, 'INV-'.strtoupper(uniqid()), ('INV-'.$this->current_order), $date->format('Y-m-d'));
		if (!$inv_id) {
			throw new RuntimeException("impossible to create invoice");
		}

		return $inv_id;
	}
}
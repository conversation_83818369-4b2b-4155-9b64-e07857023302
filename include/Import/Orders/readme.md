# Gestion des factures via lignes de commandes/import de commandes

Exemple d'implémentation :
```php
use Riashop\Import\Orders\OrderInvoiceHandler;
use Riashop\Import\Orders\OrderInvoiceLine;

// Création du handler
$OrderInvoiceHandler = new OrderInvoiceHandler()

// Parcoure les lignes de commande
foreach ($order_lines as $line) {
	$user_id = ...('récupération du compte client de la ligne');

	// Création de la ligne
	$OrderInvoiceLine = new OrderInvoiceLine(
		$line['ord_id'],
		$line['prd_id'],
		$line['line_id'],
		$line['qte'],
		floatval($line['price_ht'])
	);

	$OrderInvoiceLine->setTva($line['tva_rate']);

	// Gestion de la ligne par le handler
	$OrderInvoiceHandler->handleLine(
		$usr_id, $OrderInvoiceLine
	);
}
// Finalise la gestion des factures de lignes de commande
$OrderInvoiceHandler->terminate();
```
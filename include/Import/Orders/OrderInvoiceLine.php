<?php
namespace Riashop\Import\Orders;

use DateTime;

/**
 * \OrderInvoiceLine
 */
class OrderInvoiceLine
{
	/**
	 * Identifiant de la facture
	 *
	 * @var integer $invoice_id
	 */
	private $invoice_id = 0;
	/**
	 * Identifiant de la commande
	 *
	 * @var integer $order_id
	 */
	private $order_id;
	/**
	 * Identifiant du produit
	 *
	 * @var integer $prd_id
	 */
	private $prd_id;
	/**
	 * Identifiant de la ligne produit
	 *
	 * @var integer $line_id
	 */
	private $line_id;
	/**
	 * Quantité de la ligne
	 *
	 * @var float|integer $qte
	 */
	private $qte = 0;
	/**
	 * Prix HT de la
	 *
	 * @var float $price
	 */
	private $price = 0;
	/**
	 * Tva de ligne
	 *
	 * @var float $tva
	 */
	private $tva = _TVA_RATE_DEFAULT;
	/**
	 * Date de création de la commande
	 *
	 * @var DateTime $date_created
	 */
	private $date_created = null;
	/**
	 * Initialisation de la ligne
	 *
	 * @param integer $order_id
	 * @param integer $prd_id
	 * @param integer $line_id
	 * @param integer|float $qte
	 * @param float $price
	 * @param float $tva
	 */
	public function __construct(
		$order_id,
		$prd_id,
		$line_id,
		$qte=0,
		$price=0,
		$tva=_TVA_RATE_DEFAULT
	)
	{
		$this->setOrderId($order_id);
		$this->setPrdId($prd_id);
		$this->setLineId($line_id);
		$this->setQte($qte);
		$this->setPrice($price);
		$this->setTva($tva);
	}
	/**
	 * Retourne l'indentifiant de la facture
	 *
	 * @return int Identifiant de la facture
	 */
	public function invoiceId()
	{
		return $this->invoice_id;
	}
	/**
	 * Retourne l'identifiant de la commande
	 *
	 * @return int Identifiant de la commande
	 */
	public function orderId()
	{
		return $this->order_id;
	}
	/**
	 * Retourne l'identifiant du produit
	 *
	 * @return int Identifiant du produit
	 */
	public function prdId()
	{
		return $this->prd_id;
	}
	/**
	 * Retourne l'identifiant de la ligne
	 *
	 * @return int Identifiant de la ligne
	 */
	public function lineId()
	{
		return $this->line_id;
	}
	/**
	 * Retourne la quantité sur la ligne
	 *
	 * @return int retourne la quantité
	 */
	public function qte()
	{
		return $this->qte;
	}
	/**
	 * Retourne le prix de la ligne
	 *
	 * @return float Retourne le prix de la ligne
	 */
	public function price()
	{
		return $this->price;
	}
	/**
	 * Retourne la tva
	 *
	 * @return float retourne la tva
	 */
	public function tva()
	{
		return $this->tva;
	}
  /**
	 * Retourne la date de création
	 *
	 * @return DateTime|null Retourne la date de création ou null
	 */
	public function dateCreated()
	{
		return $this->date_created;
	}
	/**
	 * Définie l'identifiant de la facture
	 *
	 * @param int $invoice_id Identifiant de la facture
	 * @return self
	 */
	public function setInvoiceId($invoice_id)
	{
		$this->invoice_id = $invoice_id;
		return $this;
	}
	/**
	 * Définie l'identifiant de la commande
	 *
	 * @param int $order_id Identifiant de la commande
	 * @return self
	 */
	public function setOrderId($order_id)
	{
		$this->order_id = $order_id;
		return $this;
	}
	/**
	 * Définie l'identifiant du produit
	 *
	 * @param int $prd_id Identifiant du produit
	 * @return self
	 */
	public function setPrdId($prd_id)
	{
		$this->prd_id = $prd_id;
		return $this;
	}
	/**
	 * Définie l'identifiant de la ligne
	 *
	 * @param int $line_id Identifiant de la ligne
	 * @return self
	 */
	public function setLineId($line_id)
	{
		$this->line_id = $line_id;
		return $this;
	}
	/**
	 * Définie la quantité
	 *
	 * @param int $qte quantité
	 * @return self
	 */
	public function setQte($qte)
	{
		$this->qte = $qte;
		return $this;
	}
	/**
	 * Définie le prix
	 *
	 * @param int $price prix
	 * @return self
	 */
	public function setPrice($price)
	{
		$this->price = $price;
		return $this;
	}
	/**
	 * Définie la tva
	 *
	 * @param int $tva tva
	 * @return self
	 */
	public function setTva($tva)
	{
		$this->tva = $tva;
		return $this;
	}
	/**
	 * Définie la date de création de la ligne
	 *
	 * @param DateTime $date Date de création de la ligne
	 * @return self
	 */
	public function setDateCreated(DateTime $date)
	{
		$this->date_created = $date;

		return $this;
	}
}
<?php
namespace Riashop\Import\Orders;
/**
 * \class OrderInvoice représente un facture d'une commande dans l'import
 */
class OrderInvoice
{
	/**
	 * Identifiant de la facture
	 *
	 * @var integer $invoice_id
	 */
	private $invoice_id;
	/**
	 * Indique si c'est un facture qui vient d'être créé par l'import
	 *
	 * @var boolean $is_new
	 */
	private $is_new = false;
	/**
	 * Liste des lignes de la facture
	 *
	 * @var array $lines
	 */
	private $lines = [];
	/**
	 * Initialisation avec l'identifiant de la facture et si elle vient d'être créé
	 *
	 * @param integer $invoice_id Identifiant de la facture
	 * @param boolean $new Si elle vietn d'être créé
	 */
	public function __construct( $invoice_id, $new=false )
	{
		$this->invoice_id = $invoice_id;
		$this->is_new = $new;
	}
	/**
	 * Retourne l'identifiant de la facture
	 *
	 * @return integer
	 */
	public function invoiceId()
	{
		return $this->invoice_id;
	}
	/**
	 * Retourne si c'est une nouvelle facture
	 *
	 * @return boolean
	 */
	public function isNew()
	{
		return $this->is_new;
	}
	/**
	 * Ajoute un lign à la facture
	 *
	 * @param OrderInvoiceLine $Line Ligne de la facture
	 * @return boolean Retourne true si succès, sinon false
	 */
	public function addLine(OrderInvoiceLine $Line)
	{
		$Line->setInvoiceId($this->invoice_id);
		if ($this->is_new) {
			$ref = prd_products_get_ref($Line->prdId());
			$name = prd_products_get_name($Line->prdId());
			$result = ord_inv_products_add_sage(
				$this->invoice_id,
				$Line->prdId(),
				$Line->lineId(),
				$ref,
				$name,
				$Line->price(),
				$Line->qte(),
				$Line->tva(),
				$Line->orderId(),
				null,
				0,
				null,
				false
			);
		}else{
			$result = true;
		}

		if ($result) {
			$this->lines[$Line->prdId().'-'. $Line->lineId()] = $Line;
		}

		return $result;
	}
	/**
	 * Retourne les lignes de facture
	 *
	 * @return array
	 */
	public function getLines()
	{
		return $this->lines;
	}
	/**
	 * Permet de terminer la gestion de la facture dans l'import
	 * mise à jour des totaux et suppression des lignes non présente dans l'objet
	 * si c'est un nouvelle commande on la unmask
	 * @return void
	 */
	public function terminate()
	{
		if (!$this->is_new) {
			$r_products = ord_inv_products_get($this->invoice_id);
			if (!ria_mysql_num_rows($r_products)) {
				// rien a faire pas d'erreur
				return;
			}

			while ($line = ria_mysql_fetch_assoc($r_products)) {
				if (!array_key_exists($line['id'].'-'.$line['line'], $this->lines)) {
					ord_inv_products_del(
						$this->invoice_id,
						$line['id'],
						$line['line']
					);
				}
			}
		}else{
			ord_invoices_unmask($this->invoice_id);
		}

		ord_invoices_update_totals($this->invoice_id);
	}
}
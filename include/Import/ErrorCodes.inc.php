<?php

/** Cette fonction permet de retourner un message d'erreur à partir de son code
 *	@param string $err_code le code de l'erreur
 *	@param string $msg Optionnel, le message d'erreur
 *
 *	@return string|bool le message d'erreur, false si erreur
 */
function ipt_get_error_msg($err_code, $msg = ''){
	return ImportErrorCodes::getMessage( $err_code, $msg );
}

/**	Cette classe contient tous les messages d'erreur pouvant être utilisés avec l'outil d'importation de fichiers
 *
 */
class ImportErrorCodes {

		/** Les codes d'erreur sont construis de cette façon :
	 *		1. Commence par "ERR"
	 *		2. Suivi d'un ID permettant d'identifier le type d'import :
	 *					- 0 : général à tous les imports
	 * 					- 1 : produits
	 *					- 2 : ---non utilisé---
	 * 					- 3 : catégorie
	 * 					- 4 : clients
	 * 					- 5 : tarifs et de promotions sur produits
	 * 					- 6 : marques
	 * 					- 7 : commandes
	 * 					- 8 : stocks
	 * 		3. Suivi du numéro de l'erreur complété à gauche par des zéros pour que le code loge sur 7 caractères
	 */
	private $_codes;

	/**
	 * Constructeur de la classe
	 *
	 * @param void
	 * @return void
	 */
	protected function __construct() {

		$this->_codes = array(
			'ERR0000' => _('La ligne de données est incomplète.'),
			'ERR0001' => _('La valeur est trop grande.'),
			'ERR0002' => _('La valeur n\'est pas un texte.'),
			'ERR0003' => _('La valeur ne doit contenir que des chiffres.'),
			'ERR0004' => _('La valeur n\'est pas numérique.'),
			'ERR0005' => _('Le type de la valeur est inconnu.'),
			'ERR0006' => _('La mise à jour est impossible (aucune information existante avec l\'identifiant fourni).'),
			'ERR0007' => _('La suppression a échoué (aucune information existante avec l\'identifiant fourni).'),
			'ERR0008' => _('Une erreur est survenue lors de la suppression.'),
			'ERR0009' => _('Synchronisé ne peux pas être modifié.'),
			'ERR0010' => _('L\'identifiant saisi est déjà utilisé.'),
			'ERR0011' => _('Une erreur est survenue lors de l\'ajout.'),
			'ERR0012' => _('La mise à jour a échoué.'),
			'ERR0013' => _('La valeur n\'est pas une date.'),
			'ERR0014' => _('Aucune date renseignée.'),
			'ERR0015' => _('Aucun identifiant renseigné.'),
			'ERR0016' => _('L\'ajout est impossible (Des informations existent déjà pour cet identifiant).'),
			'ERR0017' => _('Erreur lors de la mise à jour de l\'adresse.'),
			'ERR0018' => _('Problème d\'unicité, l\'identifiant saisi existe déjà.'),
			'ERR0019' => _('Une erreur est survenue lors de l\'ajout/mise à jour de la commande.'),
			'ERR0020' => _('Une erreur est survenue lors de l\'ajout d\'une traduction.'),

			'ERR1000' => _('Erreur lors de l\'ajout du produit dans une catégorie.'),
			'ERR1001' => _('Erreur lors de la suppression du produit de la catégorie'),
			'ERR1002' => _('Une erreur est survenue lors de la publication du produit.'),
			'ERR1003' => _('Une erreur est survenue lors de la dépublication du produit.'),
			'ERR1004' => _('La TVA est incorrecte.'),
			'ERR1005' => _('Une erreur est survenue lors de la mise à jour du prix.'),
			'ERR1006' => _('Un produit existe déjà avec ce code-barres.'),
			'ERR1007' => _('Un produit existe déjà avec cette référence.'),
			'ERR1008' => _('Une erreur est survenue lors de l\'ajout de l\'image.'),
			'ERR1009' => _('Une erreur est survenue lors de l\'ajout de l\'image au produit.'),
			'ERR1010' => _('Une erreur est survenue lors de l\'ajout du lien canonique.'),
			'ERR1011' => _('Impossible d\'ajouter ou mettre à jour le lien canonique. Le produit n\'appartient pas à la catégorie.'),
			'ERR1012' => _('Aucune catégorie ne correspond au classement renseigné.'),
			'ERR1013' => _('Il existe plusieurs catégories avec ce nom.'),
			'ERR1014' => _('Veuillez saisir une désignation de produit.'),
			'ERR1015' => _('Une erreur est survenue lors de la suppression des conditionnements.'),
			'ERR1016' => _('Une erreur est survenue lors de l\'ajout du conditionnement.'),
			'ERR1017' => _('Tous les modèles de saisie n\'ont pu être identifiés.'),

			'ERR3002' => _('Une erreur est survenue lors de la publication de la catégorie.'),
			'ERR3003' => _('Une erreur est survenue lors de la dépublication de la catégorie.'),
			'ERR3004' => _('Une erreur est survenue lors de la suppression de l\'image de la catégorie.'),
			'ERR3005' => _('Une erreur est survenue lors de l\'ajout ou la mise à jour de l\'image de la catégorie.'),

			'ERR4000' => _('Le pays n\'est pas au format ISO-3166-1.'),
			'ERR4001' => _('Aucune correspondance pour le droit d\'accès.'),
			'ERR4002' => _('Aucune correspondance pour la catégorie tarifaire.'),
			'ERR4003' => _('Dépôt principal non trouvé.'),
			'ERR4004' => _('Aucune correspondance pour la catégorie comptable.'),
			'ERR4005' => _('Aucune correspondance pour le type d\'adresse.'),
			'ERR4006' => _('Aucune correspondance pour la civilité.'),
			'ERR4007' => _('Aucune correspondance pour le moyen de paiement.'),
			'ERR4008' => _('Il manque des informations pour le type d\'adresse Société.'),
			'ERR4009' => _('Il manque des informations pour le type d\'adresse Professionnel.'),
			'ERR4010' => _('Il manque des informations pour le type d\'adresse Particulier.'),
			'ERR4011' => _('L\'adresse email doit être unique pour les comptes principaux.'),
			'ERR4012' => _('Erreur lors de l\'ajout du commentaire.'),
			'ERR4013' => _('Erreur lors de la mise à jour du commentaire.'),
			'ERR4014' => _('Erreur lors de la suppression du commentaire.'),
			'ERR4015' => _('Une erreur est survenue lors de l\'attribution d\'un identifiant représentant.'),
			'ERR4016' => _('Une erreur est survenue lors de la liaison avec le représentant.'),
			'ERR4017' => _('Une erreur est survenue lors de l\'ajout de l\'adresse de livraison'),
			'ERR4018' => _('La suppression de comptes administrateur ne peut pas être réalisée via un import. Elle doit être réalisée manuellement depuis le back-office.'),

			'ERR5000' => _('Aucune correspondance pour le type de remise.'),
			'ERR5001' => _('Aucun produit ne correspond à cet identifiant.'),
			'ERR5002' => _('Aucun client ne correspond à cet identifiant.'),
			'ERR5003' => _('Une erreur est survenue lors de l\'ajout de la condition pour la remise.'),
			'ERR5004' => _('Erreur lors de la mise à jour de la remise client.'),
			'ERR5005' => _('Erreur lors de la récupération du client rattaché à la remise client.'),
			'ERR5006' => _('Erreur lors de la récupération de la catégorie tarifaire rattachée à la remise client.'),
			'ERR5007' => _('Aucune condition spécifiée pour la remise client.'),
			'ERR5008' => _('Article non identifié pour la remise client.'),
			'ERR5009' => _('Dépôt non identifié.'),

			'ERR6000' => _('Une marque existe déjà avec cette référence.'),
			'ERR6001' => _('Une erreur est survenue lors de la publication de la marque.'),
			'ERR6002' => _('Une erreur est survenue lors de la dépublication de la marque.'),
			'ERR6003' => _('Une erreur est survenue lors de la suppression de l\'image de la marque.'),
			'ERR6004' => _('Une erreur est survenue lors de l\'ajout ou la mise à jour de l\'image de la marque.'),

			'ERR7000' => _('Une commande existe déjà avec cette référence.'),
			'ERR7001' => _('Aucun produit ne correspond à l\'identifiant renseigné.'),
			'ERR7002' => _('Aucune correspondance pour le statut de la commande.'),
			'ERR7003' => _('Aucun client ne correspond à cet identifiant.'),
			'ERR7004' => _('Impossible de modifier une commande synchronisée.'),
			'ERR7005' => _('Aucun représentant ne correspond à cet identifiant.'),
			'ERR7006' => _('Aucune correspondance pour le service de livraison.'),
			'ERR7007' => _('Aucune correspondance pour le moyen de paiement.'),
			'ERR7008' => _('Aucune correspondance pour le type de carte bancaire.'),
			'ERR7009' => _('Aucune correspondance pour le site.'),
			'ERR7010' => _('Aucune quantité renseignée pour la ligne de commande.'),
			'ERR7011' => _('Aucun prix de renseigné pour la ligne de commande.'),
			'ERR7012' => _('Le numéro de la ligne est obligatoire'),
			'ERR7013' => _('Une erreur est survenue lors de l\'ajout de l\'entête de la commmande.'),
			'ERR7014' => _('Une erreur est survenue lors de l\'ajout d\'une ligne à la commande.'),
			'ERR7015' => _('La mise à jour de l\'entête de la commande a échoué.'),
			'ERR7016' => _('La mise à jour de la ligne de commande a échoué.'),
			'ERR7017' => _('Une erreur est survenue lors de l\'enregistrement des autorisations sur le modèle de commande.'),
			'ERR8001' => _('Erreur lors de la récupération du produit associé au stock.'),

			'ERR9000' => _('Un magasin existe déjà avec cet identifiant.'),
			'ERR9001' => _('Une erreur est survenue lors de la publication du magasin.'),
			'ERR9002' => _('Une erreur est survenue lors de la dépublication du magasin.'),
			'ERR9003' => _('Une erreur est survenue lors de la suppression de l\'image du magasin.'),
			'ERR9004' => _('Une erreur est survenue lors de l\'ajout ou la mise à jour de l\'image du magasin.'),

			'ERR10000' => _('Un dépôt existe déjà avec cette référence.'),
			'ERR10001' => _('Une erreur est survenue lors de la publication du dépôt.'),
			'ERR10002' => _('Une erreur est survenue lors de la dépublication du dépôt.'),

			'ERR11000' => _('Un modèle de saisie existe déjà avec cette référence.'),

			'ERR12000' => _('Un champ avancé existe déjà avec cette référence.'),
			'ERR12001' => _('Aucune correspondance pour le modèle de saisie.'),
			'ERR12002' => _('Aucune correspondance pour la catégorie de champs.'),
			'ERR12003' => _('Aucune correspondance pour l\'unité.'),
			'ERR12004' => _('Erreur lors de rattachement du champ à son modèle de saisie.'),

			'ERR13001' => _('L\'objet lié au champ n\'est pas identifié.'),
			'ERR13002' => _('Le champ avancé n\'est pas identifié.'),

			'ERR14000' => _('Erreur lors de la création du fichier d\'image.'),
			'ERR14001' => _('Erreur lors de l\'upload du fichier d\'image.'),
			'ERR14002' => _('Le type de données n\'est pas défini empêchant ainsi l\'import de l\'image.'),
			'ERR14003' => _('L\'objet auquel l\'image doit être rattachée n\'a pas été identifié.'),
			'ERR14004' => _('L\'image n\'a pas pu être rattachée à l\'objet.'),
			'ERR14005' => _('L\'image n\'existe pas donc le lien image/objet n\'a pas été supprimé.'),
			'ERR14006' => _('Erreur lors du téléchargement du fichier d\'image à partir de son url.'),

			'ERR15000' => _('Erreur lors de la création du document.'),
			'ERR15001' => _('Erreur lors de l\'upload du document.'),
			'ERR15002' => _('Le type de données n\'est pas défini empêchant ainsi l\'import du document.'),
			'ERR15003' => _('L\'objet auquel le document doit être rattaché n\'a pas été identifié.'),
			'ERR15004' => _('Le document n\'a pas pu être rattachée à l\'objet.'),
			'ERR15005' => _('Le document n\'existe pas donc le lien document/objet n\'a pas été supprimé.'),
			'ERR15006' => _('Le type de document n\'a pas été identifié.'),

			'ERR16000' => _('Erreur lors de la création du magasin.'),
			'ERR16001' => _('Le code client fourni n\'a pas été trouvé.'),
		);

	}

	/** Cette fonction permet de retourner un message d'erreur à partir de son code
	 *	@param string $code le code de l'erreur
	 *	@param string $msg Optionnel, le message d'erreur à compléter
	 *
	 *	@return string|bool le message d'erreur en cas de succès, false si erreur
	 */
	protected function _getMessage( $code, $msg = '' ){

		if (!is_string($code) || trim($code) === '') {
			return false;
		}

		if (!is_string($msg)) {
			return false;
		}

		if( !self::codeExists($code) ){
			return false;
		}

		return $code . '|' . $msg . ' ' . $this->_codes[ $code ];

	}

	/** Cette fonction permet de retourner un message d'erreur à partir de son code
	 *	@param string $code le code de l'erreur
	 *	@param string $msg Optionnel, le message d'erreur à compléter
	 *
	 *	@return string|bool le message d'erreur en cas de succès, false si erreur
	 */
	public static function getMessage( $code, $msg='' ){
		return self::getInstance()->_getMessage( $code, $msg );
	}

	/**	Cette méthode permet la vérification d'un code d'erreur
	 * 	@param $code Obligatoire, code d'erreur à tester
	 * 	@return bool true si le code existe, false dans le cas contraire
	 */
	public static function codeExists( $code ){
		return self::getInstance()->_codeExists( $code );
	}

	/**	Cette méthode permet la vérification d'un code d'erreur
	 * 	@param $code Obligatoire, code d'erreur à tester
	 * 	@return bool true si le code existe, false dans le cas contraire
	 */
	protected function _codeExists( $code ){
		return array_key_exists( $code, $this->_codes );
	}

	/**
	 * @var ImportErrorCodes
	 * @access private
	 * @static
	 */
	private static $_instance = null;

	/**
	 * Méthode qui crée l'unique instance de la classe
	 * si elle n'existe pas encore puis la retourne.
	 *
	 * @param void
	 * @return ImportErrorCodes
	 */
	private static function getInstance() {

		if( is_null(self::$_instance) ){
			self::$_instance = new ImportErrorCodes();
		}

		return self::$_instance;
	}

}
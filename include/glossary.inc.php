<?php

/// \addtogroup tools
/// @{

/// \defgroup tools_glossary Glossaire
/// @{

// \cond onlyria
/**	Cette fonction permet la création d'une nouvelle définition
 *	@param string $name Obligatoire,  nom de la définition
 *	@param string $desc Obligatoire, contenue de la définition
 *	@param bool $published Obligatoire, détermine s'il faut publier la définition
 *	@param string $name_pl Facultatif, nom du pluriel
 *
 *	@return int l'identifiant attribué à la définition en cas de succès, false en cas d'échec.
 */
function gsr_words_add( $name, $desc, $published, $name_pl='' ){
	global $config;

	// Contrôles
	if( !trim( $name ) ) return false;
	if( !trim( $desc ) ) return false;
	if( !isset( $published ) ) $published = 0;

	// Formatage
	$name = addslashes(  trim( $name )  );
	$desc = addslashes( ucfirst( trim( $desc ) ) );
	if( $name_pl!=='' )
		$name_pl = addslashes(  trim( $name_pl ) );

	$sql = '
		insert into gsr_words (
			gsr_tnt_id, gsr_name, gsr_desc, gsr_publish, gsr_date_created
	';

	if( $name_pl!=='' )
		$sql .= ', gsr_name_pl';

	$sql .= ') values (
		'.$config['tnt_id'].',\''.$name.'\',\''.$desc.'\','.$published.',now()';

	if( $name_pl!=='' )
		$sql.= ',\''.$name_pl.'\'';

	$sql .= ')';

	// Procède à l'insertion
	$res = ria_mysql_query( $sql );
	if( $res )
		return ria_mysql_insert_id();
	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'une définition
 *	@param int $id Obligatoire, identifiant de la défintion à mettre à jour.
 *	@param string $name Obligatoire,  nom de la définition
 *	@param string $desc Obligatoire, contenue de la définition
 *	@param bool $published Facultatif, détermine s'il faut publier la définition
 *	@param string $name_pl Facultatif, nom au pluriel
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function gsr_words_update( $id, $name, $desc, $published=false, $name_pl='' ){
	global $config;

	// Contrôles
	if( !is_numeric( $id ) || $id<0 ) return false;
	if( !gsr_words_exists( $id ) ) return false;
	if( !trim( $name ) ) return false;
	if( !trim( $desc ) ) return false;
	if( !isset( $published ) ) $published = false;
	$publish = $published ? 1 : 0;

	// Formatage
	$name = addslashes( trim( $name ) );
	$desc = addslashes( ucfirst( trim( $desc ) ) );
	if( $name_pl!=='' )
		$name_pl = addslashes(  trim( $name_pl ) );

	// SQL
	$cols = '
			gsr_name=\''.$name.'\',
			gsr_desc=\''.$desc.'\',
			gsr_publish=\''.$publish.'\'
	';

	if( $name_pl!=='' )
		$cols .= '
			,gsr_name_pl=\''.$name_pl.'\'
		';

	// Requête SQL
	$sql = '
		update gsr_words
		set '.$cols.'
		where gsr_tnt_id='.$config['tnt_id'].' and gsr_id='.$id.'
	';

	// Procède à la mise à jour
	if( !ria_mysql_query( $sql ) ) return false;
	return true;

}
// \endcond

/**	Cette fonction permet le chargement d'une ou plusieurs définition(s), éventuellement filtrés en fonction
 *	des paramètres optionnels fournis.
 *
 *	@param int $id Facultatif, identifiant d'une définition sur lequel filtrer le résultat.
 *	@param bool $published Facultatif, état de publication de la définition sur lequel filtrer le résultat.
 *	@param string $letter Facultatif, filtre sur le nom
 *	@param array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par nom croissant. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : gsr_name. Les valeurs autorisées pour la direction sont : asc, desc.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la définition
 *			- name : titre de la définition
 *			- name_pl : titre de la définition au pluriel
 *			- desc : description/contenu de la définition
 *			- date : date/heure de création de la définition
 *			- date_modified : date/heure de dernière modification de la définition
 *			- publish : état de publication de l'avis (-1 : attente de modération, 0 : refusé, 1 : accepté)
 */
function gsr_words_get( $id=0, $published=null, $letter='', $sort=false){
	global $config;

	// Contrôles
	if( !is_numeric( $id ) || $id<0 ) return false;

	// Requête SQL
	$sql = '
		select
			gsr_id as id,
			gsr_name as name,
			gsr_name_pl as name_pl,
			gsr_desc as "desc",
			date_format( gsr_date_created, "%d/%m/%Y à %H:%i" ) as date,
			date_format( gsr_date_modified, "%d/%m/%Y à %H:%i" ) as date_modified,
			gsr_publish as publish

		from
			gsr_words
		where gsr_tnt_id='.$config['tnt_id'].' and
			gsr_date_deleted is null
	';

	// Filtres
	if( $id>0 )
		$sql .= ' and gsr_id='.$id;
	if( $letter == '09' )
		$sql .= ' and SUBSTRING( gsr_name, 1, 1 ) in ("0", "1", "2", "3", "4", "5", "6", "7", "8", "9")';
	elseif( $letter!=='' )
		$sql .= ' and gsr_name like \''.$letter.'%\'';
	if( isset( $published ) ){
		if( $published === -1)
			$sql .= ' and gsr_publish = \'-1\'';
		elseif( $published === 0)
			$sql .= ' and gsr_publish = \'0\'';
		else
			$sql .= ' and gsr_publish = \'1\'';
	}

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
		$sort = array( 'gsr_name'=>'asc' );

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$dir = $dir=='asc' ? 'asc' : 'desc';
		switch( $col ){
			case 'gsr_name' :
				array_push ($sort_final, 'gsr_name '.$dir );
				break;
		}
	}
	if( sizeof($sort_final)==0 ) $sort_final = array( 'gsr_name asc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	// Procède au chargement
	$res = ria_mysql_query( $sql );
	return $res;
}

/** Cette fonction permet de récupérer un tableau des définitions.
 *	@param int $id Facultatif, identifiant d'une définition sur lequel filtrer le résultat.
 *	@param bool $published  Facultatif, état de publication de la définition sur lequel filtrer le résultat.
 *	@param string $letter Facultatif, filtre sur le nom
 *	@param array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par nom croissant. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : gsr_name. Les valeurs autorisées pour la direction sont : asc, desc.
 *
 *	@return array un tableau contenant :
 *			- id : identifiant de la définition
 *			- name : titre de la définition
 *			- name_pl : titre de la définition au pluriel
 *			- desc : description/contenu de la définition
 *			- date : date/heure de création de la définition
 *			- date_modified : date/heure de dernière modification de la définition
 *			- publish : état de publication de l'avis (-1 : attente de modération, 0 : refusé, 1 : accepté)
 */
function gsr_words_get_array( $id=0, $published=null, $letter='', $sort=false){
	global $config, $memcached;

	$key_memcached  = $config['tnt_id'].':'.$config['wst_id'].':gsr_words_get_array:'.$id.':';
	$key_memcached .= ( $published === null ? 'null' : ($published ? 'publish' : 'no-publish') );
	$key_memcached .= ':'.$letter.':'.( $sort === false ? '' : (implode('-', array_keys($sort)).'-'.implode('-', $sort)) );

	if( ($get = $memcached->get($key_memcached)) ){
		return ($get == 'none' ? [] : $get);
	}

	$rgsr = gsr_words_get( $id, $published, '', $sort );
	if( !$rgsr ){
		$memcached->set( $key_memcached, 'none', 60 * 60, [
			'code' => 'RIASHOP_GSR_WORDS_GET_ARRAY',
			'name' => 'Récupèration du glossaire'
		]);

		return array();
	}

	$ar_words = array();
	while( $gsr = ria_mysql_fetch_array($rgsr) ){

		if( trim($letter)!='' ){
			$l = substr( str_remove_accents($gsr['name']), 0, 1 );
			if( strtolower($letter)!=strtolower($l) ){
				continue;
			}
		}

		$ar_words[] = array(
			'id' => $gsr['id'],
			'name' => $gsr['name'],
			'name_pl' => $gsr['name_pl'],
			'desc' => $gsr['desc'],
			'date' => $gsr['date'],
			'date_modified' => $gsr['date_modified'],
			'publish' => $gsr['publish']
		);

	}

	$memcached->set( $key_memcached, (count($ar_words) ? $ar_words : 'none'), 60 * 60, [
		'code' => 'RIASHOP_GSR_WORDS_GET_ARRAY',
		'name' => 'Récupèration du glossaire'
	]);

	return $ar_words;
}

/**	Cette fonction vérifie l'existence d'une définition
 *
 *	@param int $id Obligatoire, identifiant de la définition
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function gsr_words_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	$sql = 'select gsr_id from gsr_words where gsr_tnt_id= '.$config['tnt_id'].' and gsr_id='.$id.' and gsr_date_deleted is null';
	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}

// \cond onlyria
/**	Cette fonction permet la suppression virtuelle d'une définition
 *
 *	@param int $id Obligatoire, identifiant de la définition à supprimer
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function gsr_words_del( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_query('update gsr_words set gsr_date_deleted=now() where gsr_tnt_id= '.$config['tnt_id'].' and gsr_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la publication d'une défintion
 *
 *	@param int $id Obligatoire, identifiant de la définition
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function gsr_words_publish( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !ria_mysql_query('update gsr_words set gsr_publish=1 where gsr_tnt_id= '.$config['tnt_id'].' and gsr_id='.$id.' and gsr_date_deleted is null') )
		return false;
	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la dé-publication d'une définition
 *
 *	@param int $id Obligatoire, identifiant de la définition
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function gsr_words_unpublish( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !ria_mysql_query('update gsr_words set gsr_publish=0 where gsr_tnt_id= '.$config['tnt_id'].' and gsr_id='.$id.' and gsr_date_deleted is null') )
		return false;
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre en place les définitions définit dans le glossaire.
 *	@param string $desc Il s'agit du texte dans lequel doit être mis en palce les définitions
 *	@return string Retourne le texte avec les définitions
 */
function gsr_insert_def_in_desc( $desc ){
	global $config;

	// Supprime toute balise <dfn> pouvant déjà existé dans le texte
	$desc = preg_replace( '/(<dfn title=".*">)/Ui', '', $desc );
	$desc = preg_replace( '/(<\/dfn>)/Ui', '', $desc );

	// Recherche et remplace chaque mot du glossaire par une balise <dfn></dfn>
	$r_words = gsr_words_get_array( 0, true, $letter='', array('gsr_name'=>'desc') );
	if( count($r_words) > 0 ){
		foreach( $r_words as $word ){
			$searched_words = trim($word['name']);
			if( trim($word['name_pl']) != '' ){
				$searched_words = trim($word['name_pl']).'|'.$searched_words;
			}
			$desc = ria_split_balise('#((?![^<]+>)'.$searched_words.')#i', '#REPLACE_GSR_HERE_$1#', $desc, 'preg_replace', 1);
			$desc = preg_replace( '#\#REPLACE_GSR_HERE_('.$searched_words.')\##i', '<dfn title="'.$word['desc'].'">$1</dfn>', $desc );
		}
	}

	return $desc;
}
// \endcond

/// @}
/// @}

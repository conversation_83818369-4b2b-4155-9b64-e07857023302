<?php
class ObjectPeriods
{
	protected $periods;

	protected $filter;

	public function __construct(Periods $periods, ObjectFilterInterface $filter)
	{
		$this->periods = $periods;
		$this->filter = $filter;
	}

	public function filter(array $objects)
	{
		$tmp_obj = $objects;
		$filtered = new Filtered;
		foreach( $this->periods->DatePeriod() as $begin)
		{
			$filtered->setCurrentKey($begin->format("Ymd"));
			$end = clone $begin;

			$end->add($this->periods->getDateInterval());

			$filter = $this->filter->datefilter($begin, $end);

			$objs = array();
			foreach ($tmp_obj as $index => $item) {
				if (!$filter($item)) {
					continue;
				}
				unset($tmp_obj[$index]);
				$filtered->addIndex($index);
				$objs[$index] = $item;
 			}

			$filtered->addFiltered(array(
			'date' => $begin,
			'filtered' => $objs,
			));
		}

		return $filtered;
	}
}
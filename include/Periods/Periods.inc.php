<?php

/**
 * Class Periods
 * @see https://riastudio.atlassian.net/browse/DIFCO-1
 *
 * Cette classe est utilisée dans plusieurs projets (Difco, FCH, Cerdys) et intervient dans la création de cadenciers
 */
class Periods
{

	const MONTH = 'P1M'; ///< Intervalle d'un mois

	const DAY = 'P1D'; ///< Intervalle d'un jour

	const WEEK = 'P1W'; ///< Intervalle d'une semaine

	const YEAR = 'P1Y'; ///< Intervalle d'une année

	const HOUR = 'PT1H'; ///< Intervalle d'une heure

	/** Date de début
	 * @var DateTime
	 */
	protected $begin;

	/** Date de fin
	 * @var DateTime
	 */
	protected $end;

	/** Intervalle, parmi l'une des constantes suivantes : Periods::HOUR, Periods::DAY, Periods::WEEK, Periods::MONTH, Periods::YEAR
	 * @var string
	 */
	protected $interval;

	/**
	 * @var DateInterval
	 */
	protected $dateInterval;

	/**
	 * @var DatePeriod
	 */
	protected $DatePeriod;

	/**
	 * Periods constructor.
	 *
	 * @param $interval Obligatoire, l'une des constantes suivantes : Periods::HOUR, Periods::DAY, Periods::WEEK, Periods::MONTH, Periods::YEAR
	 * @param $begin Facultatif, date de début
	 * @param $end Facultatif, date de fin
	 * @throws Exception
	 */
	public function __construct( $interval, $begin, $end ){

		$this->setInterval($interval);

		if (!is_null($begin)) {
			$this->setBegin($begin);
		}

		if (!is_null($end)) {
			$this->setEnd($end);
		}

	}

	/**
	 * @param $begin Date de début
	 */
	public function setBegin( $begin )
	{
		$this->begin = $this->DatetimeInstance($begin);
	}

	/**
	 * @return mixed
	 */
	public function getBegin()
	{
		return $this->begin;
	}

	/**
	 * @param $end
	 */
	public function setEnd( $end)
	{
		$this->end = $this->DatetimeInstance($end);
	}

	/**
	 * @return mixed
	 */
	public function getEnd()
	{
		return $this->end;
	}

	/**
	 * @param $interval
	 * @throws Exception
	 */
	public function setInterval( $interval )
	{
		$this->dateInterval = $this->DateIntervalInstance($interval);
	}

	/**
	 * @return mixed
	 */
	public function getDateInterval()
	{
		return $this->dateInterval;
	}

	/**
	 * @return DatePeriod
	 * @throws Exception
	 */
	public function DatePeriod()
	{
		if (is_null($this->DatePeriod)) {
			if (is_null($this->begin)) {
				throw new Exception("A begining date for the period is required");
			}
			if (is_null($this->end)) {
				throw new Exception("A end date for the period is required");
			}
			$this->DatePeriod = $this->DatePeriodInstance();
		}

		return $this->DatePeriodInstance();
	}

	/**
	 * @return DatePeriod
	 */
	protected function DatePeriodInstance()
	{
		return new DatePeriod($this->begin, $this->dateInterval, $this->end);
	}

	/**
	 * @param $date
	 *
	 * @return DateTime
	 */
	protected function DatetimeInstance($date)
	{
		if ($date instanceof DateTime) {
			return $date;
		}elseif (is_string($date)){
			return new DateTime($date);
		}
	}

	/**
	 * @param $interval
	 *
	 * @return DateInterval
	 * @throws Exception
	 */
	protected function DateIntervalInstance($interval)
	{
		if ($interval instanceof DateInterval) {
			return $interval;
		}elseif (is_string($interval)){
			return new DateInterval($interval);
		}
	}

}
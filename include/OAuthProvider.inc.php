<?php
// \cond onlyria

/**	\defgroup oauth OAuth
 * 	\ingroup auth
 *	OAuth est un protocole d'authentification utilisé notamment pour se connecter à l'aide d'un compte externe sur un réseau social
 *	@{
 */

/**	\class OAuthProvider
 *	\brief Cette classe permet de se connecter à un réseau social en utilisant le protocole OAuth.	
 */
class OAuthProvider {

	/// Informations sur l'utilisateur
	public $userData = array();
	/// Désignation du réseau social. Les valeurs acceptées sont les suivantes : Facebook, Twitter
	public $name = '';

	/// Tableau associatif d'options attendues pour la connexion OAuth
	private $options = array();

	/// Réseaux sociaux fournisseurs d'authentification
	static private $providers = array(
		'Facebook' => _FLD_USR_FACEBOOK,
		'Twitter' => _FLD_USR_TWITTER
	);

	/**	Constructeur
	 *	@param string $name Obligatoire, désignation du réseau social. Les valeurs acceptées sont les suivantes : Facebook, Twitter
	 *	@param $options Facultatif, tableau associatif d'options attendues pour la connexion OAuth
	 */
	public function __construct( $name, $options = array() ){

		$options = array_merge(array(
			'userProfile' => 2 // identifiant riashop (client particulier par défaut)
		), $options);

		switch ($name) {
			case 'Facebook':
				$options = array_merge(array(
					'authEndPoint' => 'https://www.facebook.com/dialog/oauth',
					'tokenEndPoint' => 'https://graph.facebook.com/v2.4/oauth/access_token',
					'infoEndPoint' => 'https://graph.facebook.com/me',
					'clientID' => '',
					'clientSecretKey' => '',
					'parameters' => array(
						'fields' => 'first_name,last_name,gender,email',
						'scope' => 'public_profile,email',
						'redirect_uri' => OAuthProvider::getCurrentURL()
					)
				), $options);
			break;

			case 'Twitter':
				$options = array_merge(array(
					'requestTokenEndPoint' => 'https://api.twitter.com/oauth/request_token',
					'authEndPoint' => 'https://api.twitter.com/oauth2/authenticate',
					'accessTokenEndPoint' => 'https://api.twitter.com/oauth/access_token',
					'clientID' => '',
					'clientSecretKey' => '',
					'parameters' => array(
						'oauth_callback' => OAuthProvider::getCurrentURL()
					)
				), $options);
			break;

			default:

			break;
		}

		$this->name = $name;
		$this->options = $options;
	}

	/**
	 *	Retourne l'identifiant du champ avancé contenant les informations de connexion relatives au provider fourni.
	 *	@param string $name Nom du provider
	 *
	 *	@return int Identifiant du champ avancé.
	 */
	static public function getProviderFld( $name ){
		return OAuthProvider::$providers[$name];
	}

	/**
	 *	Vérifie si l'adresse e-mail d'un utilisateur est auto-générée ou non
	 *	@param int $usr_id Entier, optionnel. Identifiant d'un utilisateur.
	 *	@param string $email Chaine, optionnel. Adresse email d'un utilisateur à vérifier.
	 *
	 *	@return bool TRUE si l'adresse est auto-générée. FALSE sinon.
	 */
	static public function isMinimalUser( $usr_id=0, $email='' ){
		if( $usr_id && !trim($email) ){
			$email=gu_users_get_email($usr_id);
		}
		$arr = array_map('urlalias', array_keys(OAuthProvider::$providers));
		return preg_match('/^tmp-(' . implode('|', $arr) . ')-[0-9]+\@/', $email);
	}

	/**
	 *	Ajoute les informations d'authentification OAuth du provider à l'utilisateur
	 *	@param int $user_id Entier. Identifiant d'un utilisateur.
	 *	@param int $identifier L'identifiant de l'utilisateur relatif au provider fourni.
	 *
	 *	@return bool Booléen. TRUE si la valeur pour cet utilisateur a bien été mise à jour. FALSE sinon.
	 */
	public function setUserProvider( $user_id, $identifier ){
		return fld_object_values_set($user_id, OAuthProvider::$providers[$this->name], $identifier);
	}

	/**
	 *	Tente une authentification de l'utilisateur au provider fourni.
	 *
	 *	@return bool Booléen. TRUE si l'utilisateur a bien été authentifié. FALSE sinon.
	 */
	public function authenticate(){
		global $config;
		$this->userData = $this->handleRequest();
		if( $this->userData ){
			$filter = array(OAuthProvider::$providers[$this->name] => $this->userData['id']);
			$user_exists = gu_users_get(0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, 0, $filter);//$fld=false, $fld_or=false, $or_between_fld=null, $lng_fld=false, $prc=0, $wst=0 ){
			$user_exists = ($user_exists && ria_mysql_num_rows($user_exists));
			// 	Si l'utilisateur est connecté depuis un réseau social et qu'il n'est pas inscrit au site, on enregistre un compte en base et on s'y connecte
			//
			// 		L'email est de la forme 'tmp-PROVIDER-IDENTIFIER@site_url
			// 		Le mot de passe est de la forme 'tnt_id-wst_id-IDENTIFIER' par défaut
			//
			if( !$user_exists ){
				$domain = preg_replace('#^https?:\/\/#', '', $config['site_url']);
				$email = isset($this->userData['email']) ? $this->userData['email'] : urlalias('tmp-' . $this->name . '-' . $this->userData['id']) . '@' . $domain;
				$password = $config['tnt_id'] . '-' . $config['wst_id'] . '-' . $this->userData['id'];

				$user_id = gu_users_add($email, $password, $this->options['userProfile'], '', false, 0, 0, null, false, true, 32);

				if( $user_id ){

					$this->setUserProvider($user_id, $this->userData['id']);

					$gender = 1;
					if( isset($this->userData['gender']) && $this->userData['gender'] == 'female'){
						$gender = 2;
					}

					$addr_id = gu_adresses_add($user_id, 1, $gender,
						isset($this->userData['first_name']) ? $this->userData['first_name'] : '',
						isset($this->userData['last_name']) ? $this->userData['last_name'] : ''
					);

					$addr_set = gu_users_address_set($user_id, $addr_id, false);

					gu_users_disconnect();
					gu_users_login($email,$password, true);
					gu_restore_cart();
				}
			}else{
				gu_users_disconnect();
				gu_users_login('','', true, 0, $this->name, $this->userData['id']);
				gu_restore_cart();
			}
			return true;
		}
		return false;
	}

	/**
	 *	Implémentation OAuth spécifique aux providers (à surcharger si besoin)
	 *	@param int $user_id Entier. Identifiant d'un utilisateur.
	 *
	 *	@return bool Booléen. TRUE si l'utilisateur a bien été identifié auprès du provider. FALSE sinon.
	 */
	public function handleRequest( $user_id=0 ){
		global $config;

		$options = $this->options;
		if( $this->name == 'Facebook' ){

			if( isset($_GET['code']) ){

				$tokenInfo = $this->getToken($options);
				if( isset($tokenInfo['error']) ){
					throw new Exception($tokenInfo['error']['message']);
				}

				if( isset($_GET['displayPopup']) && $_GET['displayPopup'] == 1 ){
					if( !isset($_SESSION['inPopup']) ){
						$_SESSION['inPopup'] = 1;
						print '<html><head><script></script></head><body></body></html>';
						die;
					}else{
						unset($_SESSION['inPopup']);
					}
				}

				$options['params'] = $options['parameters'];
				$options['params']['access_token'] = $tokenInfo['access_token'];

				$res = $this->fetch($options['infoEndPoint'], $options);

				if( isset($res['email']) ){
					return $res;
				}
			}else{
				$this->connect($options);
			}
		}elseif( $this->name == 'Twitter' ){
			$isLogged = false;
			if( isset($_SESSION['OAuthProvider_'.$this->name.'_auth']) ){

				$url = 'https://api.twitter.com/1.1/account/verify_credentials.json';//$options['requestTokenEndPoint'];
				unset($_SESSION['OAuthProvider_'.$this->name.'_auth']);
				$isLogged = true;
			}else{
				$url = $options['requestTokenEndPoint'];
			}

			$headers = array_merge(array(
				'oauth_consumer_key' => $options['clientID'],
				'oauth_nonce' => sha1(time()),
				'oauth_signature_method' => 'HMAC-SHA1',
				'oauth_timestamp' => time(),
				'oauth_version' => '1.0',
				'oauth_token' => $options['accessToken']
			));
			$headers['oauth_signature'] = rawurlencode($this->getSignature($url, $headers, $options['clientSecretKey'], $options['accessTokenSecret'], 'GET'));

			ksort($headers);
			foreach ($headers as $key => $value) {
				unset($headers[$key]);
				$headers[] = $key . '=' . $value;
			}
			$res = $this->fetch($url, array('http_headers' => array('Content-Type' => 'application/x-www-form-urlencoded', 'Authorization' => 'OAuth ' . implode(', ', $headers))));

			if( $isLogged ){
				return $res;
			}
			if( is_string($res) ){
				$data = array();
				parse_str($res, $data);
				if( isset($data['oauth_callback_confirmed']) && $data['oauth_callback_confirmed'] == 'true'){
					$_SESSION['OAuthProvider_'.$this->name.'_auth'] = true;
					header('Location: https://api.twitter.com/oauth/authenticate?oauth_token=' . $data['oauth_token']);
					die;
				}
			}else{
				throw new Exception($res['errors'][0]['message']);
			}

		}else{
			throw new Exception('Provider "' . $this->name . '" not implemented yet.');
		}
		return false;
	}

	/**
	 *	Vérifie que le serveur est sécurisé (protocole HTTPS)
	 *
	 *	@return bool Booléen. TRUE si la connexion HTTP est sécurisée. FALSE sinon.
	 */
	static private function serverIsSecure(){
		return (!empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off') || (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443);
	}

	/**
	 *	Obtient l'URL en cours (fonction utilitaire)
	 *
	 *	@return string L'adresse URL courante.
	 */
	static public function getCurrentURL(){
		$strUrl = OAuthProvider::serverIsSecure() ? 'https://' : 'http://';
		if (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] != '80') {
			$strUrl .= $_SERVER['SERVER_NAME'].':'.$_SERVER['SERVER_PORT'].$_SERVER['REQUEST_URI'];
		} else {
			$strUrl .= $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		}
		return $strUrl;
	}

	/** Redirection vers le point d'authentification (option:authEndPoint)
	 *	@param $options Facultatif, tableau d'options complèmentaires en paramètre de connexion
	 */
	public function connect( $options = array() ){
		$options = array_merge(array(
			'authType' => 'uri',
			'certificate' => null,
			'redirectTo' => OAuthProvider::getCurrentURL(),
			'parameters' => array()
		), $options);
		$params = array_merge(array(
			'response_type' => 'code',
			'client_id' => $options['clientID'],
			'redirect_uri' => $options['redirectTo'],
		), $options['parameters']);
		$authURL = $options['authEndPoint'] . '?' . http_build_query($params, null, '&');
		header('Location: ' . $authURL);
		die;
	}

	/**
	 *	Obtient le token d'accès OAuth (option:tokenEndPoint)
	 *	@param $options Facultatif, tableau associatif d'options à passer en argument lors de l'appel au réseau social
	 */
	public function getToken( &$options = array() ){
		$options = array_merge(array(
			'code' => $_GET['code'],
			'redirectTo' => OAuthProvider::getCurrentURL(),
			'params' => array()
		), $options);
		$params = array_merge(array(
			'client_id' => $options['clientID'],
			'client_secret' => $options['clientSecretKey'],
			'redirect_uri' => isset($options['parameters']['redirect_uri']) ? $options['parameters']['redirect_uri'] : $options['redirectTo'],
			'grant_type' => 'authorization_code',
			'code' => $options['code']
		), $options['params']);
		$endPointURL = $options['tokenEndPoint'];
		$res = $this->request($endPointURL, $params);
		return $res;
	}

	/**
	 *	Retourne le résultat d'une requête HTTP (méthode utilitaire)
	 *  Voir la fonction request(...) pour plus d'informations
	 *
	 *	@param string $url Obligatoire, url à télécharger
	 *	@param $options Obligatoire, options à transmettre dans la requête
	 *
	 */
	public function fetch( $url, $options ){
		$options = array_merge(array(
			'http_method' => 'GET',
			'http_headers' => array(),
			'params' => array(),
		), $options);
		return $this->request($url, $options['params'], $options['http_method'], $options['http_headers']);
	}

	/**
	 *	Obtient la signature HMAC d'une requête OAuth
	 *
	 * @param string $url URL de la requête
	 * @param $params Paramètres de la requête sur lesquels sera batie la signature
	 * @param $consumerSecret Clé OAuth du client utilisée pour cette requête
	 * @param $tokenSecret Jeton OAuth utilisé pour cette requête
	 * @param $http_method Type de requête web ('GET', 'POST', 'PUT'...)
	 *
	 * @return Retourne la signature HMAC générée à partir des paramètres de requête.
	 */
	public function getSignature( $url, $params, $consumerSecret, $tokenSecret, $http_method='POST' ) {
		foreach ($params as $key => $value) {
			$params[$key] = rawurlencode($value);
		}
		ksort($params);
		$parameters = array();
		foreach ($params as $key => $value) {
			$parameters[] = $key . '=' . $value;
		}
		$signature = strtoupper($http_method);
		$signature .= '&' . rawurlencode($url) . '&' . rawurlencode(implode('&', $parameters));

		$consumer = rawurlencode($consumerSecret) . '&' . rawurlencode($tokenSecret);
		$hmac = base64_encode(hash_hmac('sha1', $signature, $consumer, true));

		return $hmac;
	}

	/**
	 *	Exécute une requête HTTP (seuls GET et POST sont pour l'instant supportés)
	 *
	 * @param string $url URL de destination de la requête HTTP
	 * @param array $params Paramètres supplémentaires de la requête HTTP
	 * @param string $verb Type de requête : 'GET' ou 'POST'
	 * @param array $headers Tableau de headers à ajouter à la requête HTTP
	 */
	public function request( $url, $params=array(), $verb='POST', $headers=array() ){
		if( $params ){
			$params = http_build_query($params, null, '&');
		}

		$verb = strtoupper($verb);
		if( $verb === 'GET' && sizeof($params) ){
			$url .= '?' . $params;
		}

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_USERAGENT, 'OAuth2 Client');

		if( is_array($headers) ){
			$curl_headers = array();
			foreach( $headers as $key => $value) {
				$curl_headers[] = $key . ': ' . $value;
			}
			curl_setopt($ch, CURLOPT_HTTPHEADER, $curl_headers);
		}

		if( $verb === 'POST' ){
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		}

		$result = curl_exec($ch);
		curl_close($ch);

		if( ($jsonData = json_decode($result, true)) ){
			return $jsonData;
		}
		return $result;
	}

}

/// @}

// \endcond
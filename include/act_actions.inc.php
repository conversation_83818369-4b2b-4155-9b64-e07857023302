<?php
require_once('define.inc.php');

/** Cette fonction renvoie la liste des actions
 *
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'action
 *			- code : code de l'action
 *			- qte : nombre de fois ou l'action doit être réalisé pour être complété
 *			- desc : description de l'action
 */
function act_actions_get(){

	$sql = '
		select act_id as id,
		act_qte as qte, 
		act_desc as description,
		act_package as package
		from act_actions
	';

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $res;
}

/** Teste l'existance d'un identifiant d'action dans la base de données.
 *
 *	@param int $id Optionnel, identifiant à tester.
 *
 *	@return bool true si l'action existe, false dans le cas contraire.
 */
function act_actions_exist($id){

	if( !is_numeric($id) || $id<0 ) return false;
	
	if( $id>0 ){
		
		$ract = ria_mysql_query( '
			select act_id as id from act_actions
			where act_id='.$id.''
		);
		
		if( !ria_mysql_num_rows($ract) ){
			return false;
		}
		
		return true;
		
	}

	return false;
}

/** Cette fonction renvoie la hiérarchie des actions à effectuées
 *
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- parent_id : identifiant de l'action parent
 *			- child_id : identifiant de l'action enfant
 */
function act_actions_hierarchy_get(){

	$sql = '
		select aah_parent_id as parent_id, 
		aah_child_id as child_id
		from act_actions_hierarchy
	';

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $res;
}

/** Cette fonction renvoie la liste des restrictions applicables aux actions d'onboarding
 *
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- act_id : identifiant de l'action parente
 *			- grp_id : identifiant du groupe (il faut valider un groupe pour valider une action)
 *			- require_id : identifiant de l'action requise (il faut valider toutes les actions d'un groupe pour valider le groupe) 
*/
function act_actions_restrictions_get(){


	$sql = '
		select aar_act_id as act_id, 
		aar_grp_id as grp_id,
		aar_require_id as require_id
		from act_actions_restrictions
	';

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $res;
}

/** Cette fonction renvoie la liste des actions réalisées
 *
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- act_id : identifiant de l'action parent
 *			- grp_id : identifiant du group (il faut valider un groupe pour valider une action)
 *			- date_created : la date de creation
 *			- date_modified : la date de modification
 *		 	- date_deleted : la date de suppression
 *			- author_id : Identifiant du l'auteur de l'action
 *			- cls_id
 *			- obj_id_0
 *			- pnj_id_1
 *			- obj_id_2
 *			
*/
function act_actions_history_get(){

	global $config;

	$sql = '
		select ahi_id as id, 
		ahi_act_id as act_id,
		ahi_date_created as date_created,
		ahi_date_modified as date_modified,
		ahi_date_deleted as date_deleted,
		ahi_author_id as author_id,
		ahi_cls_id as cls_id,
		ahi_obj_id_0 as obj_id_0,
		ahi_obj_id_1 as obj_id_1,
		ahi_obj_id_2 as obj_id_2
		from act_actions_history
		where ahi_tnt_id = '.$config['tnt_id'].'
		order by ahi_date_created desc
	';

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $res;
}

/**	Cette fonction permet l'enregistrement d'un nouveau historique d'action
 *
 *	@param int $act_id Identifiant de l'action concerné
 *	@param $date Date/Heure de création de l'historique
 *	@param int $author_id Identifiant de l'utilisateur concerné 
 *	@param int $cls_id Identifiant de de la classe d'objet
 *	@param int $obj_0 Identifiant de l'objet
 *	@param int $obj_1 Identifiant de l'objet
 *	@param int $obj_2 Identifiant de l'objet

 *	@return int l'identifiant du nouveau historique en cas de succès
 *	@return bool false en cas d'échec
 */
function act_actions_history_add( $act_id, $date, $author_id, $cls_id=0, $obj_0='0', $obj_1='0', $obj_2='0'){

	global $config;

  	if( !act_actions_exist( $act_id ) ){
		error_log('[act_actions_history_add] action invalide');
		return false;
  	}


	if( !gu_users_exists( $author_id ) ){
		error_log('[act_actions_history_add] utilisateur inexistant');
		return false;
	}

	if( !isdateheure( $date ) ){
		error_log('[act_actions_history_add] date invalide');
		return false;
	}
	
	$date = dateheureparse( $date );

	$sql = 'insert into act_actions_history
			( ahi_tnt_id, ahi_act_id, ahi_date_created, ahi_author_id, ahi_cls_id, ahi_obj_id_0, ahi_obj_id_1, ahi_obj_id_2 )
		values
			( '.$config['tnt_id'].', '.$act_id.', "'.$date.'", '.$author_id.', '.$cls_id.', "'.addslashes($obj_0).'", "'.addslashes($obj_1).'", "'.addslashes($obj_2).'" )';

	$res = ria_mysql_query($sql);
	
	if( !$res ){
		error_log('[act_actions_history_add] echec de la requête d\'insertion (erreur : '.mysql_error().')');
		return false;
	}

	return ria_mysql_insert_id();

}


<?php
	// Mercanet
	/** \defgroup mercanet Mercanet
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec Mercanet - BNP
	 *
	 *	Variables de config utilisées :
	 *	url_payment			:	Url de la page paiement
	 *	url_payment_success	:	Url de la page paiement effectué
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	spplus_key_test		:	Clé pour la signature (en mode test)
	 *	spplus_key			:	Clé pour la signature
	 *	spplus_site_id		:	Identifiant du site
	 *
	 *	Ces infos sont disponibles dans l'inteface Mercanet en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *	@{
	 */

	require_once('PaymentExternal.inc.php');
	require_once('email.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Systempay en tant que prestataire de paiement externe.
	 *
	 */
	class Mercanet extends PaymentExternal {
		const MAX_TRANSACTION_ID		=	899999;			///< Valeur max de l'identifiant de transaction
		const DEVISE_EURO				=	978;			///< Identifiant interne de l'euro

		private static $Instance; ///< Instance

		private $ord_id = false; ///< Identifiant de la commande en cours de traitement

		/**
		 *	Redirige l'utilisateur sur la page de paiment du service bancaire
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *	@param $encours_echue si true permet d'ajouter le montant de l'encours échue au total de la commande
		 *	@param $encours_nonechue si true, permet d'ajouter le montant de l'encours non échue au total de la commande
		 *	@return Rien car il y a une redirection à la fin
		 */
		public static function doPayment( $encours_echue = false, $encours_nonechue = false ){
			return self::getInstance()->_doPayment($encours_echue, $encours_nonechue);
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public static function getPaymentResult(){
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 *	Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@param $encours_echue si true permet d'ajouter le montant de l'encours échue au total de la commande
		 *	@param $encours_nonechue si true, permet d'ajouter le montant de l'encours non échue au total de la commande
		 *	@return string Le formulaire html
		 */
		public function _doPayment( $encours_echue=false, $encours_nonechue=false ){
			global $config;

			$orderId = $this->ord_id ? $this->ord_id : $this->getOrderId();
			$amount = $this->getOrderAmount();
			$data = array();

			if( $encours_echue ) {
				$user = ria_mysql_fetch_array( gu_users_get( $_SESSION['usr_id'] ) );
				$amount += $user['encours'];
				$data['encours'] = $user['encours'];
			}
			if( $config['tnt_id'] == 21 && $encours_nonechue && isset($config['fld_usr_encours_non_echue']) ){ // propre à SODIPEC

				// verrouille l'encours jusqu'à ce que l'acompte crée ait été pris en compte dans la gestion commerciale
				$encours_nonechue = fld_object_values_get( $_SESSION['usr_id'], $config['fld_usr_encours_non_echue'] );
				if( is_numeric($encours_nonechue) && $encours_nonechue > 0 ){
					$amount += $encours_nonechue;
					$data['encours_nonechue'] = $encours_nonechue;
				}
			}

			// Génère un id de transaction
			$transaction_id = ord_transactions_create();
			if( $transaction_id === false ){
				throw new Exception('Erreur ord_transactions_create !');
			}
			if( $transaction_id > Mercanet::MAX_TRANSACTION_ID ){
				throw new Exception('Tous les identifiants de transaction de la journée sont épuisés !');
			}


			$parm="merchant_id=".$this->getMerchantId();
			$parm="$parm merchant_country=fr";
			$parm="$parm amount=".round(100 * $amount);
			$parm="$parm currency_code=".self::DEVISE_EURO;

			$parm="$parm pathfile=".$_SERVER['DOCUMENT_ROOT']."/mercanet/p/pathfile";
			$parm="$parm transaction_id=".$transaction_id;

			$parm="$parm capture_mode=AUTHOR_CAPTURE";
			$parm="$parm capture_day=0";
			$parm="$parm data=".escapeshellarg(serialize( $data ));
			$parm="$parm order_id=".$orderId;

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/mercanet/bin/request";

			$result=exec("$path_bin $parm");
			$tableau = explode ("!", $result);


			$code = $tableau[1];
			$error = $tableau[2];
			$message = $tableau[3];


			if( ( $code == "" ) && ( $error == "" ) ){
				throw new Exception ('executable request non trouve '.$path_bin);
			}elseif( $code != 0 ){
				throw new Exception ('Erreur appel API de paiement : '.$error);
			}else{
				return $error . $message;
			}

		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public function _getPaymentResult(){
			global $config;

			$params = self::getResponse();

			if( !isset($params['order_id']) || !is_numeric( $params['order_id'] ) ){
				throw new Exception( "Mercanet : le numéro de commande n'est pas valide." );
			}


			if( $params['response_code'] != "00" ){
				throw new Exception( "Mercanet : Le code de réponse n'est pas valide : ".$params['response_code'] );
			}
			if( $params['bank_response_code'] != "00" ){
				throw new Exception( "Mercanet : Le code de réponse de la banque n'est pas valide : ".$params['bank_response_code'] );
			}

			$orderId = $params['order_id'];

			// si le montant payé > montant de la commande on change l'encours
			$order = ria_mysql_fetch_array( ord_orders_get( 0, $orderId ) );
			$data = unserialize($params['data']);

			if( isset($data['encours']) && is_numeric( $data['encours'] ) ){

				// mise à jour de l'encours dans la base.
				if( ord_installments_add( 1, 1, $orderId, $data['encours'], 0, false, 1) ){
					$user = ria_mysql_fetch_array( gu_users_get( $order['user'] ) );

					gu_users_set_encours($user['id'], $user['encours'] - $data['encours']);
					// verrouille l'encours jusqu'à ce que l'acompte crée ait été pris en compte dans la gestion commerciale
					fld_object_values_set( $user['id'], _FLD_USR_ENCOURS_LOCK, 'Oui' );
					if( $config['tnt_id'] == 21 ){
						$name = trim($user['society']) != '' ? $user['society'].', ' : '';
						$name .= trim($user['adr_firstname']) != '' ? $user['adr_firstname'].' ' : '';
						$name .= trim($user['adr_lastname']) != '' ? $user['adr_lastname'].' ' : '';
						$email = new Email();
						$email->setFrom('<EMAIL>');
						$email->setTo('<EMAIL>');
						$email->setSubject('Réalisation d\'un paiement d\'encours');
						$email->addParagraph('Bonjour,');
						$email->addParagraph('Un paiement d\'encour vient d\'être réalisé avec les informations suivantes : ');
						$email->addParagraph('Référence du compte client : '.$user['ref']);
						$email->addParagraph('Nom du compte client : '.$name);
						$email->addParagraph('Montant de l\'encours réglé : '.$data['encours']);
						$email->addParagraph('Date du paiement : '.date('d/m/Y'));
						$email->addParagraph('Cordialement,');
						$sent = $email->send();
					}
				}
			}

			if( $config['tnt_id'] == 21 && isset($data['encours_nonechue']) && is_numeric( $data['encours_nonechue'] ) ){ // met à jour l'encours pour le non échue

				// mise à jour de l'encours dans la base.
				if( ord_installments_add( 1, 1, $orderId, $data['encours_nonechue']) ){

					$current = fld_object_values_get( $order['user'], $config['fld_usr_encours_non_echue'] );
					if( !$current ){
						$current = 0;
					}

					fld_object_values_set( $order['user'], $config['fld_usr_encours_non_echue'], $current - $data['encours_nonechue'] );
					// verrouille l'encours jusqu'à ce que l'acompte crée ait été pris en compte dans la gestion commerciale
					fld_object_values_set( $order['user'], $config['fld_usr_encours_non_echue_lock'], 'Oui' );
				}
			}

			// Véfifie l'état de la commande
			$state = ord_orders_get_state($orderId);
			if ($state === false || $state >= 3){
				throw new exception("Mercanet : La commande $orderId semble déjà avoir été traitée ! (state = $state)");
			}

			ord_orders_pay_type_set($orderId, 1);
			if( $state < 3 ){
				ord_orders_update_status($orderId, 3, '');
			}
			ord_orders_update_status($orderId, 4, '');

			// Confirmation de la commande à NetAffiliation
			$affi = new NetAffiliation();
			$affi->getOnlinePaymentTag( $orderId );

			return $this;

		}

		/**
		 *	Cette fonction récupère la requete de la banque et la décrypte.
		 *	@return array un tableau avec les valeurs renvoyées par la banque
		 */
		public static function getResponse(){
			if( !isset( $_REQUEST['DATA'] ) ){
				throw new Exception('Les données ne sont pas présentes.');
			}

			$message="message=".$_REQUEST['DATA'];

			$pathfile="pathfile=".$_SERVER['DOCUMENT_ROOT']."/mercanet/p/pathfile";

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/mercanet/bin/response";

			$result=exec("$path_bin $pathfile $message");

			$tableau = explode ("!", $result);

			$t_result = array();
			$t_result['code'] = $tableau[1];
			$t_result['error'] = $tableau[2];
			$t_result['merchant_id'] = $tableau[3];
			$t_result['merchant_country'] = $tableau[4];
			$t_result['amount'] = $tableau[5];
			$t_result['transaction_id'] = $tableau[6];
			$t_result['payment_means'] = $tableau[7];
			$t_result['transmission_date']= $tableau[8];
			$t_result['payment_time'] = $tableau[9];
			$t_result['payment_date'] = $tableau[10];
			$t_result['response_code'] = $tableau[11];
			$t_result['payment_certificate'] = $tableau[12];
			$t_result['authorisation_id'] = $tableau[13];
			$t_result['currency_code'] = $tableau[14];
			$t_result['card_number'] = $tableau[15];
			$t_result['cvv_flag'] = $tableau[16];
			$t_result['cvv_response_code'] = $tableau[17];
			$t_result['bank_response_code'] = $tableau[18];
			$t_result['complementary_code'] = $tableau[19];
			$t_result['complementary_info'] = $tableau[20];
			$t_result['return_context'] = $tableau[21];
			$t_result['caddie'] = $tableau[22];
			$t_result['receipt_complement'] = $tableau[23];
			$t_result['merchant_language'] = $tableau[24];
			$t_result['language'] = $tableau[25];
			$t_result['customer_id'] = $tableau[26];
			$t_result['order_id'] = $tableau[27];
			$t_result['customer_email'] = $tableau[28];
			$t_result['customer_ip_address'] = $tableau[29];
			$t_result['capture_day'] = $tableau[30];
			$t_result['capture_mode'] = $tableau[31];
			$t_result['data'] = $tableau[32];

			return $t_result;
		}


		/**
		 *	Cette fonction returne le merchant_id à utiliser en fonction de si c'est un paiement 3dsecure ou non
		 */
		public function getMerchantId(){
			global $config;
			return $config['mercanet_merchant_id'];
		}

		/**
		 *	Permet de surcharger le ord_id
		 *	@param int $ord_id Obligatoire, identifiant de commande à utiliser
		 */
		public function setOrdId( $ord_id ){
			$this->ord_id = $ord_id;
		}
	}

	/// @}


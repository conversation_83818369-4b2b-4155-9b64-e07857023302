<?php
	// SPPLUS
	/**	\defgroup spplus SPPLUS
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec SPPLUS - Caisse d'épargne
	 *
	 *	Variables de config utilisées :
	 *	url_payment			:	Url de la page paiement
	 *	url_payment_success	:	Url de la page paiement effectué
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	spplus_key_test		:	Clé pour la signature (en mode test)
	 *	spplus_key			:	Clé pour la signature
	 *	spplus_site_id		:	Identifiant du site
	 *
	 *	Ces infos sont disponibles dans l'inteface SystemPay en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *	@{
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');

	/**	\brief Cette classe est l'implémentation concrète du fournisseur SPPLUS - Caisse d'épargne en tant que prestataire de paiement externe.
	 *
	 */
	class SPPLUS extends PaymentExternal {
		const MAX_TRANSACTION_ID		=	899999;			///< Valeur max de l'identifiant de transaction

		// Acquisition données carte
		const ACTION_MODE_SILENT		=	'SILENT';		///< Par le commerçant
		const ACTION_MODE_INTERACTIVE	=	'INTERACTIVE';	///< Par la plateforme

		// Mode de sollicitation
		const CTX_MODE_TEST				=	'TEST';			///< Mode test
		const CTX_MODE_PRODUCTION		=	'PRODUCTION';	///< Mode production

		// Configuration de paiement
		const PAYMENT_CONFIG_SINGLE		=	'SINGLE';		///< En une fois
		const PAYMENT_CONFIG_MULTI		=	'MULTI';		///< En plusieurs fois
		const PAYMENT_CONFIG_MULTI_EXT	=	'MULTI_EXT';	///< Echéancier personnalisé

		const DEVISE_EURO				=	978;			///< Identifiant interne de l'euro

		private static $Instance;							///< Instance de la classe, utilisée pour implémenter le Singleton

		/*
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return void
		*/
		public static function doPayment(){
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public static function getPaymentResult(){
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return void
		*/
		public function _doPayment(){
			global $config;

			$orderId = $this->getOrderId();
			$amount = $this->getOrderAmount();

			// Génère un id de transaction
			$transaction_id = ord_transactions_create();
			if( $transaction_id === false ){
				throw new Exception('Erreur ord_transactions_create !');
			}
			if( $transaction_id > SPPLUS::MAX_TRANSACTION_ID ){
				throw new Exception('Tous les identifiants de transaction de la journée sont épuisés !');
			}
			$domain = 'http://' . $_SERVER['HTTP_HOST'];

			// paramètres
			$params = array(
				'vads_site_id'			=>	$config['spplus_site_id'],					// Id site
				'vads_action_mode'		=>	self::ACTION_MODE_INTERACTIVE,				// Acquisition des données carte
				'vads_amount'			=>	round(100 * $amount),						// Prix
				'vads_ctx_mode'			=>	$this->getCtxMode(),						// Mode test ou production
				'vads_currency'			=>	self::DEVISE_EURO,							// Devise
				'vads_cust_id'			=>	$this->getUserId(),							// Id client
				'vads_cust_email'		=>	$this->getUserEmail(),						// Email client
				'vads_order_id'			=>	$orderId,									// Id commande
				'vads_page_action'		=>	'PAYMENT',									// Obligatoire
				'vads_payment_config'	=>	self::PAYMENT_CONFIG_SINGLE,				// Configuration
				'vads_return_mode'		=>	'NONE',
				'vads_trans_date'		=>	gmdate('YmdHis', time()),					// Date
				'vads_trans_id'			=>	sprintf('%06d', $transaction_id),			// Identification transaction
				'vads_version'			=>	'V2',										// Version
				'vads_validation_mode'	=>	'0',										// Validation par défaut (plateforme)
				'vads_url_cancel'		=>	$domain . $config['url_payment'],			// Url paiement
				'vads_url_success'		=>	$domain . $config['url_payment_success']	// Url paiement effectué
			);

			if( isset($config['url_payment_error']) && trim($config['url_payment_error'])!='' ){
				$params['vads_url_error'] = $domain . $config['url_payment_error'];
			}

			// Génération de la signature
			ksort($params);
			$t = array();
			foreach ($params as $value){
				$t[] = $value;
			}
			$t[] = $this->getKey();
			$params['signature'] = sha1(implode('+', $t));

			$r = $this->encodeParams($params);

			header("Location: https://systempay.cyberpluspaiement.com/vads-payment/?$r");
			exit;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public function _getPaymentResult(){
			global $config;

			// Calcul signature
			$params = $_POST;
			ksort($params);
			$t = array();

			mail('<EMAIL>', 'TNT '.$config['tnt_id'].' Retour SPPLUS Paiement', print_r($params, true) );

			foreach( $params as $key => $value ){
				if (substr($key, 0, 5) == 'vads_'){
					$t[] = $value;
				}
			}
			$t[] = $this->getKey();
			$signature = sha1(implode('+', $t));

			if (isset($_POST['signature']) && $signature == $_POST['signature']) {
				// Requête authentifiée
				if ($_POST['vads_result'] == '00') {
					$orderId = $params['vads_order_id'];

					// Véfifie l'état de la commande
					$state = ord_orders_get_state($orderId);
					if ($state === false || $state >= 3){
						throw new exception("SPPLUS : La commande $orderId semble déjà avoir été traitée ! (state = $state)");
					}

					ord_orders_pay_type_set($orderId, 1);
					if ($state < 3){
						ord_orders_update_status($orderId, 3, '');
					}
					ord_orders_update_status($orderId, 4, '');
				}
			}
			return $this;
		}

		/**
		 *	Renvoie le mode (test ou prod)
		 *	@return Le mode
		 */
		public function getCtxMode(){
			return ($this->getContext() === PaymentExternal::CONTEXT_DEV) ? self::CTX_MODE_TEST : self::CTX_MODE_PRODUCTION;
		}

		/**
		 *	Récupère la clé
		 *	@return La clé du site
		 */
		public function getKey(){
			return $GLOBALS['config'][$this->getContext() === PaymentExternal::CONTEXT_DEV ? 'spplus_key_test' : 'spplus_key'];
		}

	}

	/// @}


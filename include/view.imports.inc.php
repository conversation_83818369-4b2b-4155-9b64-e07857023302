<?php

/**	\file view.imports.inc.php
 *
 * 	Ce fichier contient des fonctions facilitant le travail d'affichage lié aux fonctionnalités d'import de données.
 *
 */

require_once('imports.inc.php');
require_once('RegisterGCP.inc.php');

$data_type = array(
	'default' => array(
		'block_download_text' => _("Sélectionnez un type de contenu pour pouvoir télécharger le fichier exemple et éviter les surprises. Vous pourrez ensuite passer à votre import."),
		'support' 						=> '/admin/tools/imports/notice-imports.pdf',
		'label'               => ''
	),
	CLS_PRODUCT => array(
		'label'               => _('produits'),
		'block_download_text' => _("fichier de produits"),
		'file'                => 'import-produits-exemple.csv',
		'block_help_required' => array(
		_("Référence")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de produit dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de produit avec le menu déroulant.")
	),
	CLS_USER => array(
		'label'               => _('clients'),
		'block_download_text' => _("fichier de comptes clients"),
		'file'                => 'import-clients-exemple.csv',
		'block_help_required' => array(
		_("Code client ou Email ou Siret")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de compte client dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de contact avec le menu déroulant.")
	),
	CLS_STOCK => array(
		'label'               => _('stocks'),
		'block_download_text' => _("fichier de stocks"),
		'file'                => 'import-stocks-exemple.csv',
		'block_help_required' => array(
			_("Référence du produit"),
			_("Référence du dépôt"),
			_("Stock disponible")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de stock dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de stock avec le menu déroulant.")
	),
	CLS_BRAND => array(
		'label'               => _('marques'),
		'block_download_text' => _("fichier de marques"),
		'file'                => 'import_marques_exemple.csv',
		'block_help_required' => array(
			_("Désignation")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de marque dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de marque avec le menu déroulant.")
	),
	CLS_ORDER => array(
		'label'               => _('commandes'),
		'block_download_text' => _("fichier de commandes"),
		'file'                => 'import_commandes_exemple.csv',
		'block_help_required' => array(
			_("Bon de commande ERP"),
			_("Date de commande"),
			_("Compte client"),
			_("Total HT de la commande"),
			_("Référence du produit"),
			_("Désignation du produit"),
			_("Prix HT du produit"),
			_("TVA"),
			_("Quantité du produit")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de commande dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de commande avec le menu déroulant.")
	),
	CLS_CATEGORY => array(
		'label'               => _('catégories'),
		'block_download_text' => _("fichier de catégories de produits"),
		'file'                => 'import-categories-exemple.csv',
		'block_help_required' => array(
			_("Nom")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de catégorie dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de catégorie avec le menu déroulant.")
	),
	CLS_PRICE => array(
		'label'               => _('promotions'),
		'block_download_text' => _("fichier de promotions"),
		'file'                => 'import_promotions_exemple.csv',
		'block_help_required' => array(
		_("Identifiant du produit"),
		_("Type (nouveau tarif, % ou valeur)"),
		_("Valeur")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf',
		'mapping_head_1'      => _("Chaque titre de colonne ci-dessous doit correspondre à une propriété de promotion dans [have_website]. Certaines ont déjà été mappées en fonction de leurs noms. "),
		'mapping_head_2'      => _("Tout ce qui n'a pas encore été mappé peut être mappé manuellement à une propriété de promotion avec le menu déroulant.")
	),
	// warning
	CLS_ORDER.'-model' => array(
		'label'               => _('modèles de commandes'),
		'block_download_text' => _("fichier de modèles de commandes"),
		'file'                => 'import-modeles-commandes-exemple.csv',
		'block_help_required' => array(
			_("Bon de commande ERP"),
			_("Date de commande"),
			_("Compte client"),
			_("Total HT de la commande"),
			_("Référence du produit"),
			_("Désignation du produit"),
			_("Prix HT du produit"),
			_("TVA"),
			_("Quantité du produit")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf'
	),
	CLS_PRICE.'-user_discount' => array(
		'label'               => _('tarifs'),
		'block_download_text' => _("fichier de tarifs"),
		'file'                => 'import_tarifs_exemple.csv',
		'block_help_required' => array(
			_("Identifiant du produit"),
			_("Type (nouveau tarif, % ou valeur)"),
			_("Valeur")
		),
		'support'              => '/admin/tools/imports/notice-imports.pdf'
	)
);

/**
 * Retourne le dom de l'encart sur le téléchargement du fichier d'exemple
 *
 * @param $code soit l'identifiant d'une classe (ex : CLS_PRODUCT), soit default
 * @return string Le code HTML à afficher dans l'encart "Fichier d'exemple" de la page d'importation
 */
function ria_view_example_import_file( $code ){
	global $data_type;

	// Si la classe n'est pas supportée, retourne le texte par défaut
	if( !isset($data_type[$code]) ){
		$code = 'default';
	}

	$type = $data_type[$code];

	$html = '<h3 class="title ">'._('Télécharger un exemple de fichier à importer').'</h3>';
	if ($code === 'default') {
		$html .= '<p>'.htmlspecialchars( $type['block_download_text'] ).'</p>';
	} else {
		$html .= '<p>'.str_replace('#param[type de données]#', $type['label'], _('Téléchargez notre fichier exemple d\'import de #param[type de données]# et testez le processus d\'import pour éviter les surprises. Vous pourrez ensuite passer à votre import !')).'</p>';
		$html .= '<a class="button" href="/admin/tools/imports/'.$type['file'].'">'._("Télécharger le fichier exemple") .'</a>';
	}
	return $html;
}

/** Cette fonction renvoie le code HTML du bloc d'aide à l'importation de fichier
 *
 * @param $code Obligatoire, type de contenu pour lequel afficher la documentation, ou default si le type n'est pas encore connu
 * @return string Le code HTML du bloc d'aide à l'importation de fichier
 */
function ria_view_help_import_file( $code='default' ){
	global $data_type, $config;

	$type = $data_type[$code];
	$html = '<h3 class="title">'._('Bien préparer votre fichier !').'</h3>';
	$html .= '<p>'._("Votre fichier peut être importé aux formats XLS, XLSX ou CSV et créé grâce aux modèles fournis.") .'</p>';
	$html .= '<p>'._('Il doit contenir au moins une donnée permettant d\'identifier le contenu à ajouter, mettre à jour ou supprimer dans RiaShop (par exemple : la référence du produit, le code client…).').'</p>';

	if ($code === 'default') {
		$html .= '<p><strong>'._("Sélectionnez un type de contenu pour découvrir les champs obligatoires.").'</strong></p>';
		$html .= '<ul></ul>';
	} else {
		$html .= '<p><strong>'.str_replace('#param[file]#', $type['block_download_text'], _("Voici les champs obligatoires à intégrer à votre #param[file]# :")).'</strong></p>';
		$html .=    '<ul>';
		foreach ($type['block_help_required'] as $required) {
			$html .= '<li>'.htmlspecialchars( $required ).'</li>';
		}
		$html .=    '</ul>';
	}

	$html .= '<a class="button btn-import-first" href="'.$type['support'].'" download="'._('Notice d\'utilisation riaShop - Imports de données').'">'._("Consulter la notice ").'</a>';

	return $html;

}

/** Cette fonction permet la suppression d'un import de données et de tout ce qui y est associé (rapports, sauvegardes)
 *
 *	@param int $imp_id Obligatoire, identifiant de l'import sur lequel l'action doit avoir lieu
 *	@return bool true si tout s'est correctement déroulé, false en cas d'erreur
 */
function ipt_view_import_del( $imp_id ){
	global $config;

	$is_system = false;
	$is_sync = false;
	if( $config['USER_RIASTUDIO'] ){
		$is_system = null;
		$is_sync = null;
	}

	$errors = false;

	$rImports = ipt_imports_get( $imp_id, false, false, '', 0, 0, false, false, '', '', array(), $is_system, 0, $is_sync );

	if( $rImports ){
		$rRep = ipt_reports_get( 0, $imp_id );
		$import = ria_mysql_fetch_assoc($rImports);

		if( $rRep  ){
			$rep = ria_mysql_fetch_assoc($rRep);

			if( !ipt_row_errors_del( $rep['id'] ) ){
				$errors = true;
			}
			if( !ipt_report_rows_del( $rep['id'] ) ){
				$errors = true;
			}
			if( !ipt_reports_del($rep['id']) ){
				$errors = true;
			}
		}
		if( !ipt_mapping_del($imp_id) ){
			$errors = true;
		}
		if( file_exists($import['file_path'])  ){
			unlink($import['file_path']);
		}
		if( !ipt_imports_del($imp_id) ){
			$errors = true;
		}
		$rBck = ipt_imports_get_backups( $imp_id );

		if( $rBck ){
			$bck = ria_mysql_fetch_assoc( $rBck );
			ipt_imports_del($bck['id']);
			}

	}

	return !$errors;

}

/** Permet de construire le tableau par section pour les imports de données.
 * 	Cette fonction retourne le code HTML permettant de réaliser le mapping d'une colonne du fichier de données
 *  avec une colonne RiaShop.
 *
 * @param string $key Obligatoire, code de la colonne
 * @param string $col Obligatoire, désignation de la colonne
 * @param string $preview Obligatoire, extrait de données contenues dans cette colonne
 * @param bool $read_only Obligatoire, la ligne va-t-elle être en lecture seule (true) ou bien en lecture/écriture (false)
 * @param array $schemas Obligatoire, colonnes RiaShop disponibles pour le type de données choisi (Produits, Clients, etc...)
 * @param int $import Obligatoire, identifiant de l'import d'affichage ou de modification
 * @param array $mapping Obligatoire, correspondance actuellement réalisée entre le fichier et RiaShop
 * @param bool $mapping_saved Obligatoire, booléen indiquant si la correspondance fichier/RiaShop est enregistrée (true) ou non (false)
 *
 * @return string Le code HTML à afficher pour le mapping de cette ligne
 */
function ria_imports_mapping_section( $key, $col, $preview, $read_only, $schemas, $import, $mapping, $mapping_saved ){
	global $config;

	// Indique s'il s'agit d'un utilisateur interne (true) ou externe (false)
	$configUserRiastudio = $config['USER_RIASTUDIO'];

	$html  = '<div data-id="' .$key. '" id="row-'.$key.'" class="table-map-row --table-map-col">';

	// Nom de la colonne dans le fichier Source
	$html .=  '<div class="table-map-cell --column-name">';
	$html .=     '<div id="map-option-picto-desc-'.$key.'" class="option-notice"></div>';
	$html .= 		'<span class="info-click picto-state"></span>';
	$html .=    '<span data-thead="'._('Colonnes de votre fichier :'). ' ">';
	$html .=      '<label for="map-' .$key. '">' . htmlspecialchars( $col ). '</label>';
	$html .=      '<input type="hidden" name="mapping['.$key.'][name]" value="'.$col.'" />';
	$html .=    '</span>';
	$html .=  '</div>';

	// Aperçu
	$html .=  '<div class="table-map-cell --column-preview">';
	$html .=    '<span data-thead=" ' ._('Aperçu :'). ' ">';

	if( isset($preview[$key]) ){
		foreach($preview[$key] as $i => $prev){
			$html .= '<span>' .htmlspecialchars(ria_trim_excel_export_escaping($prev)). '</span>';
			break;
		}
	}

	$html .=    '</span>';
	$html .=  '</div>';

	// Colonne de destination dans RiaShop
	$html .=  '<div class="table-map-cell --column-destination js-map-select">';
	$html .=    '<span data-thead="' ._('Destination dans Riashop :'). '">';

	$html .= '	<select '.( $read_only ? 'disabled' : '' ). ' class="select_mapping" id="map-'.$key.'" name="mapping['.$key.'][code]" data-col="'.$key.'" data-label="'. $col .'">';
	$html .= '		<option selected disabled>'._('Sélectionnez une correspondance').' </option>';
	foreach( $schemas as $cat_name => $schema ){
		$html .= '<optgroup label="'._($cat_name).'">';
		foreach( $schema as $code => $name ){

			if( !$configUserRiastudio && $code == 'PRD_LANG'){
				continue;
			}

			// Si il n'y a pas de champs personnalisés, ne pas proposer cette option
			if( $code == 'FLD' ){
				$r = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $import['cls_id'], null, false, null, false );

				if( !$r || !ria_mysql_num_rows($r)){
					continue;
				}
			}

			// Certains mappings ne doivent pas être proposés pour les imports de modèles de commandes
			if( isset($import['info']['sub_class']) && $import['info']['sub_class'] == 'model' ){
				if( in_array( $code, array( 'ORD_STATE', 'ORD_CARD', 'ORD_DATE_LIVR', 'ORD_PAY', 'ORD_SELLER', 'ORD_SRV', 'ORD_USR', 'ORD_PRD_PRICE' )) ){
					continue;
				}
			}

			if( (isset($mapping[$key]) && $mapping[$key]['code'] === $code )|| (!isset($mapping[$key]) && !$mapping_saved && trim(strtolower2($name)) == trim(strtolower2($col)) ) ){
				$selected = 'selected';
			}else{
				$selected = '';
			}

			// Spécifique aux champs avancés
			if( preg_match("/^FLD_[0-9]*/", $code) ){
				$fld_code = explode('_', $code);
				if( isset($mapping[$key]) && isset($mapping[$key]['fld_id']) && $fld_code[1] == $mapping[$key]['fld_id']){
					$selected = 'selected';
				}
			}

			$html .= '<option value="'.$code.'" ' .$selected. '>' .htmlspecialchars( $name ).' </option>';
		}
		$html .= '</optgroup>';
	}

	$html .=        '</select>';
	$html .=      '</span>';
	$html .=      '<div class="map-error" id="map-error-'.$key.'"></div>';
	$html .=      '<div id="map-option-desc-'.$key.'" class="option-notice"></div>';
	$html .=      '<span class="info-click"></span>';
	$html .=    '</div>';

	//
	$html .=    '<div class="table-map-cell --column-action --column-destination --has-notice" id="map-option-'.$key.'" style="display: none;">';
	$html .=       '<div class="table-map-cell --column-action-info"></div>';
	$html .=        '<div class="table-map-cell --column-action-select">';

	// Zone d'affichage des messages d'erreur de validation
	$html .=          '<div id="error-'.$key.'">';
	if( isset($_SESSION['map-errors'][$key]) ){
		print htmlspecialchars( $_SESSION['map-errors'][$key] );
		unset($_SESSION['map-errors']);
	}
	$html .=          '</div>';

	$html .=       '</div>';
	$html .=    '</div>';

	$html .=  '</div>';

	return $html;
}

/** Code pour l'affichage de la zone de Drag and Drop permettant de déposer le fichier à importer
 * 	Apparaît sur l'étape 1 de l'import (fichier index.php)
 *
 * @param int $upload_mb Obligatoire, variable pour la taille max des fichiers autorisé
 * @return string Le code HTML permettant l'affichage de la zone de téléchargement
 */
function import_file_drag_and_drop( $upload_mb ){

	if( !is_numeric($upload_mb) ){
		return false;
	}

	$html =  '<p> '._('Déposez votre fichier ci-dessous par un simple glissé-déposé').'</p>';
	$html .= '<div class="box">';
	$html .=   '<input class="box__file" type="file" name="imp-file" id="imp-file" />';
	$html .=   '<label for="imp-file"><span>'._('Sélectionner un fichier').'</span></label>';
	$html .= '</div>';
	$html .= '<div class="box__uploading">'._('Import en cours'). '</div>';
	$html .= '<div class="box__success"> '._('import terminé'). '></div>';
	$html .= '<div class="box__error"> ' ._('Erreur pendant l\'import').' <span></span>.</div>';
	$html .= '<p class="field-helper">'. str_replace('[taille max]', $upload_mb, _('[taille max]Mo max, formats XLS, XLSX ou CSV acceptés')).'</p>';

	return $html;
}

/** Cette fonction complète un objet contenant le schéma d'importation avec toutes les options d'importation.
 * 	Par schéma, on entend toutes les colonnes pouvant être importées pour une classe d'objet.
 * 	Le schéma est le résultat d'un appel à ipt_schemas_get( CLS_ID ) sous forme de tableau.
 *
 *	@param array $schemas Obligatoire, colonnes RiaShop disponibles pour le type de données choisi (Produits, Clients, etc...)
 *	@param array $import Obligatoire, tableau contenant les propriétés de l'import. Les propriétés id et cls_id sont obligatoires.
 *	@return object un objet contenant les schémas et toutes les options, ou false en cas d'erreur
 */
function ria_imports_get_schemas_full( $schemas, $import ){

	if( !is_array($schemas) ){
		return false;
	}
	if( !isset($import['id']) || !isset($import['cls_id']) ){
		return false;
	}

	$object = array();
	foreach( $schemas as $schema ){
		// pour chaque élément du schema....
		foreach( $schema as $code => $name ){
			// on récupère toutes les options
			$object[$code] = ria_imports_get_actions( $schemas, $code, $import['cls_id'], $import['id'], $name );
		}
	}

	return $object;

}

/** Cette fonction va compléter un objet contenant le mapping actuellement enregistré entre
 * 	un fichier à importer et le schéma RiaShop, avec les différentes options/actions disponibles
 *
 *	@param array $schemas Obligatoire, tableau contenant les différentes colonnes de la classe d'objet à importer
 *	@param array $import Obligatoire, tableau contenant les propriétés de l'import. Les propriétés id et cls_id sont obligatoires.
 *	@param array $mapping Obligatoire, tableau contenant les différentes colonnes du fichier à importer
 *	@return array un tableau contenant les propriétés du fichier à importer et pour chacune les options disponibles
 */
function ria_imports_get_registered_mapping( $schemas, $import, $mapping ){

	if( !is_array($schemas) ){
		return false;
	}
	if( !isset($import['id']) || !isset($import['cls_id']) ){
		return false;
	}
	if( !is_array($mapping) ){
		return false;
	}

	$object = array();

	// la col est représentée par l'index
	foreach( $mapping as $key => $map ){
		// une correspondance est trouvée
		if( !is_null($map) ){
			$label = null;
			array_key_exists('alias', $map) ? $label = $map['alias'] : null ;

			$options = ria_imports_get_actions($schemas, $map['code'], $import['cls_id'], $import['id'], $label, $key);
			// on rajoute le code pour comparer au changement
			$options['code'] = $map['code'];
			$object[$key] = $options;
		}
	}

	return $object;
}


/** Retourne les options d'importation disponibles pour un schéma (classe d'objet) donné
 *
 *	@param array $schemas Obligatoire, tableau décrivant les colonnes disponibles à l'import pour une classe d'objet donnée
 *	@param string $code Obligatoire, code de la colonne à traiter
 *	@param int $cls_id Obligatoire, identifiant numérique de la classe d'objet
 *	@param int $imp_id Obligatoire, identifiant numérique de l'import
 *	@param string $label Facultatif, label du champ à importer
 *	@param string $col Facultatif, code du champ à importer
 *	@return void
 */
function ria_imports_get_actions( $schemas, $code, $cls_id, $imp_id, $label = null, $col = false ){
	global $config;

	// Contrôle les paramètres d'entrée
	if(
		!is_array($schemas)
		|| !trim($code)
		|| !is_numeric($cls_id)
		|| !is_numeric($imp_id)
	){
		return false;
	}

	if( isset( $code, $col, $cls_id, $imp_id ) ){

		$backup = false;

		// Détermine si l'utilisateur a accès au import de type synchronisation
		$access_sync = false;
		if( $config['USER_RIASTUDIO'] ){
			$access_sync = null;
		}

		// Détermine si l'utilisateur a accès aux imports de type système
		$access_system = false;
		if( $config['USER_RIASTUDIO'] ){
			$access_system = null;
		}

		$rImport = ipt_imports_get($imp_id, true, false, '', 0, 0, false, false, '', '', [], $access_system, -1, $access_sync);
		if ($rImport && ria_mysql_num_rows($rImport)) {
			$import = ria_mysql_fetch_assoc( $rImport );
			$backup = $import['backup'];

			// Récupère la sous classe de l'import
			$user_discount = false;
			if( $import['info'] != '' ){
				$import['info'] = json_decode( $import['info'], true );

				if( isset($import['info']['sub_class']) && $import['info']['sub_class'] == 'user_discount' ){
					$user_discount = true;
				}
			}
		}

		// Spécifique aux champs avancés
		if( preg_match("/^FLD_[0-9]*/", $code) ){
			$fld_post = explode('_', $code);
			$code = $fld_post[0];
			$fld_id = $fld_post[1];
		}

		if( !ipt_schemas_code_exist( $code ) ){
			return _('Le code n\'existe pas.');
		}

		if( !$rSchema = ipt_schemas_get( 0, $code ) ){
			return _('Aucun schéma');
		}

		$schema = ria_mysql_fetch_assoc( $rSchema );

		$rMapping = ipt_mapping_get( $imp_id, $code, $col );

		$is_alias = false;
		$map = null;

		if ($rMapping) {
			$map = ria_mysql_fetch_assoc( $rMapping );
		}else{
			$alias = ipt_mapping_gen_alias( $label );
			$rCode = ipt_mapping_get_by_alias( $alias, $cls_id );
			$map = null;

			if( $rCode ){
				$is_alias = true;
				$map = ria_mysql_fetch_assoc( $rCode );

				// Les correspondances de valeurs (cellule = RiaShop) ne sont pas pré-charger en fonction de précédent import
				unset($map['vals']);
			}
		}

		$data = array(
			'col'     => $col,
			'desc'    => ($schema['code'] == 'USR_SIRET' ? '' :$schema['desc']),
			'code' 		=> $schema['code'],
			'options' => array()
		);

		$translate_option = false;
		if( $schema['is_translatable'] && count( $config['i18n_lng_used'] ) > 1 ){

			$selected = $config['i18n_lng'];

			if( !is_null( $map ) && $map['is_translatable'] ){
				$selected = $map['lang'];
			}

			$translate_option = array(
				'label'    => _('Langue :'),
				'type'     => 'select',
				'name'     => 'lang',
				'selected' => $selected,
				'value'    => getLangArray()
			);
		}

		if( isset( $fld_id ) ){
			if( !is_numeric( $fld_id ) || $fld_id < 0 ){
				return _("le champ avancé n'existe pas");
			}
			if( !fld_fields_exists( $fld_id ) ){
				return _("le champ avancé n'existe pas");
			}

			$fld_type = fld_fields_get_type( $fld_id );
			switch( $fld_type ){
				case 8:
					if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
						$vals = json_decode( $map['vals'], true );
					}else{
						$match = $schema['code'];

						if( $fld_type == 8 ){
							$match = 'bool';
						}
						switch( $match ){
							case 'bool' :
								$vals = array(
									'bool' => array( '' )
								);
								break;
						}
					}
					$data['options'][] = array(
						'label'        => _('Caractère de validation (par exemple "oui") :'),
						'mandatory'    => true,
						'require_vals' => true,
						'type'         => 'text',
						'name'         => 'vals,bool',
						'value'        => implode(', ', $vals['bool'])
					);
					break;
				case 6:
				case 12:
					$data['options'][] = array(
						'label'     => _('Séparateur :'),
						'mandatory' => true,
						'type'      => 'text',
						'name'      => 'sep',
						'value'     => ( ( !is_null( $map ) && $map['separator'] ) ? $map['separator'] : '' )
					);
					break;
				default:
					if( $translate_option ){
						$data['options'][] = $translate_option;
					}
					break;
			}
			return $data;
		}

		if( $schema['is_obj_id'] ){
			$code = array_map( 'strtolower', explode( '_', $schema['code'] ) );

			if( !in_array( $code[1], ipt_rows_get_id_types($schema['code']) ) ){
				$selected = '';

				if( !is_null( $map ) ){
					$selected = $map['id_type'];
				}

				$data['options'][] = array(
					'label'     => _('Type d\'identifiant :'),
					'mandatory' => true,
					'is_obj_id' => true,
					'type'      => 'select',
					'name'      => 'id-type',
					'selected'  => $selected,
					'value'     => ipt_view_options_select( 'idType', $schema['code'] )
				);
			}

			if( !in_array($schema['code'], ipt_is_identifier_relation() )  ){
				// Construction du message pour information ce que veut dire "Identifiant des [classe]"
				switch( $import['action'] ){
					case 'add':
						$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à ajouter.');
						break;
					case 'del' :
						$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à supprimer.');
						break;
					case 'upd' :
						$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à mettre à jour.');
						break;
					case 'add/upd' :
						$desc_id = _('Il s’agit du champ permettant d\'identifier les [objets] à ajouter ou à mettre à jour.');
						break;
				}

				// On remplace dans le texte d'information l'action effectuée par l'import
				$desc_id = str_replace(
					array('[objets]'),
					array(strtolower2(fld_classes_get_name($schema['cls_id']))),
					$desc_id
				);

				$data['options'][] = array(
					'label'     => sprintf( _('Identifiant des %s :'), strtolower2(fld_classes_get_name($schema['cls_id'])) ),
					'desc_id'	=> $desc_id,
					'is_obj_id' => true,
					'type'      => 'radio',
					'name'      => 'is-obj',
					'value'     => $schema['code'] == 'USR_REF' ? 1 : 0
				);

			}

		}

		if( $translate_option ){
			$data['options'][] = $translate_option;
		}

		if( $schema['is_relation'] ){

			//chargement des relations
			$val = array(
				0 => ''
			);
			// Si la classe sujet a import est produit on charge les relations pour les produits
			// et les relations parent/enfants ou enfant/parent
			switch( $schema['cls_id'] ){
				case CLS_PRODUCT :
					$rPrdRelTypes = prd_relations_types_get();
					if( $rPrdRelTypes ){
						while( $row = ria_mysql_fetch_assoc( $rPrdRelTypes ) ){
							$val[$row['id']] = $row['name'];
						}
					}
					$val['child'] = _('Produits enfants');
					$val['parent'] = _('Produit parent');
					break;
				case CLS_USER :
					$val['responsable'] = _('Compte parent');
					$val['représentant'] = _('Représentant');
					break;
				default:
					$rRelTypes = rel_relations_types_get( 0, $schema['cls_id'] );

					if( $rRelTypes ){
						while( $row = ria_mysql_fetch_assoc( $rRelTypes ) ){
							$val[$row['id']] = $row['name'];
						}
					}
					break;
			}

			if( $schema['rel_require_action'] ){

				$data['options'][] = array(
					'label'     => _('Type d\'action :'),
					'mandatory' => true,
					'type'      => 'select',
					'name'      => 'rel-action',
					'selected'  => ( isset( $map['rel_action'] ) ? $map['rel_action'] : '' ),
					'value'     => ipt_view_options_select( 'action', $schema['code'] )
				);
			}

			$selected = '';

			if( !is_null( $map ) && $map['rel_type'] ){
				$selected = $map['rel_type'];
			}


			switch( $schema['code'] ){
				case 'FLD' :
					$fld = array(0=>"");

					$r = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $cls_id, null, false, null, false );

					if( !$r || !ria_mysql_num_rows($r)){
						break;
					}

					if( !is_null( $map ) && $map['fld_id'] != 0 ){
						$selected = $map['fld_id'];
					}

					while( $s = ria_mysql_fetch_assoc( $r ) ){
						$fld[$s['id']] = $s['name'];
					}

					asort($fld,SORT_REGULAR);

					$data['options'][] = array(
						'label'     => _('Champ').' :',
						'mandatory' => true,
						'type'      => 'select',
						'name'      => 'fld-id',
						'selected'  => $selected,
						'value'     => $fld

					);
					break;
				case 'PRD_CAT':
				case 'PRD_COLISAGE':
				case 'PRD_CANONICAL_LINK_PRD':
				case 'PRD_CANONICAL_LINK_CAT':
				case 'PRC_PRD_ID':
				case 'USR_SELLER':
				case 'USR_RELATION':
				case 'USR_HIEARCH_PARENT':
				case 'USR_HIEARCH_ENFANT':
				case 'USR_RELATION_PARENT':
				case 'USR_RELATION_ENFANT':
				case 'ORD_USR':
				case 'ORD_PRD_ID':
				case 'ORD_SELLER':
				case 'PRC_USR':
					break;
				default :
					$data['options'][] = array(
						'label'     => _('Type de relation :'),
						'mandatory' => true,
						'type'      => 'select',
						'name'      => 'rel-type',
						'selected'  => $selected,
						'value'     => $val
					);
					break;
			}
			if( !in_array($schema['code'], array('FLD', 'PRD_COLISAGE')) ){

				if( !is_null( $map ) && $map['rel_id_type'] ){
					$selected = $map['rel_id_type'];
				}
				$data['options'][] = array(
					'label'     => _('Type d\'identifiant en relation :'),
					'mandatory' => true,
					'type'      => 'select',
					'name'      => 'rel-id',
					'selected'  => $selected,
					'value'     => ipt_view_options_select( 'idType', $schema['code'] ),
					'separator'	=> ($schema['code']=='PRD_CANONICAL_LINK_CAT'? $map['separator']:'')
				);
			}


			if( $schema['code'] == 'PRD_CAT' ){
				$rCat = prd_categories_get();
				if( $rCat ){
					$selected = $map['cat'];
					$select = array(
						0 => ""
					);
					while( $row = ria_mysql_fetch_assoc( $rCat ) ){
						$select[$row['id']] = $row['name'];
					}
					$data['options'][] = array(
						'label'     => _('Catégorie de premier niveau :'),
						'mandatory' => false,
						'type'      => 'select',
						'selected'  => $selected,
						'name'      => 'cat-first',
						'value'     => $select
					);
				}
			}
		}

		if( $schema['rel_many_obj'] ){
			if(!$backup){
				if($schema['code']=='USR_PAYMENT'){
					$name='sepPayment';
				}else{
					$name='sep';
				}
				$data['options'][] = array(
					'label'     => _('Séparateur :'),
					'mandatory' => false,
					'type'      => 'text',
					'name'      => $name,
					'value'     => ( ( !is_null( $map ) && $map['separator'] ) ? $map['separator'] : '' )
				);
			}
		}

		if( $schema['require_vals'] ){
			$vals = false;

			if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
				$vals = json_decode( $map['vals'], true );
			}else{
				$match = $schema['code'];
				if( $schema['data_type'] == 'bool' ){
					$match = 'bool';
				}
				switch( $match ){
					case 'bool' :
						$vals = array(
							'bool' => array( '' )
						);
						break;
					case 'PRD_LANG':
						$langs = getLangArray();
						foreach( $langs as $lng => $txt ){
							if( $txt === "" ){
								continue;
							}
							$vals[$lng] = '';
						}
						break;
				}
			}

			$mandatory = true;
			if( is_array($vals) ){
				foreach( $vals as $key => $value ){
					$data['options'][] = array(
						'label'        => ipt_mapping_get_label_by_key( $key ),
						'mandatory'    => $mandatory,
						'require_vals' => true,
						'type'         => 'text',
						'name'         => 'vals,'.$key,
						'value'        => is_array($value) ? implode( ', ', $value ) : $value
					);
					$mandatory = false;
				}
			}
		}

		if( $schema['is_unit'] ){
			$data['options'][] = array(
				'label'     => _('Unité'),
				'mandatory' => true,
				'name'      => 'unit',
				'type'      => 'select',
				'selected'  => ( isset( $map['unit'] ) ? $map['unit'] : '' ),
				'value'     => array_merge( array( 0 => '' ), ria_unites_get( $schema['unit_type'] ) )
			);
		}

		if( $schema['code'] == 'PRD_PRICE' ){
			$vals = isset($map['vals']) ? json_decode($map['vals'], true) : array();
			$tarif = array(''=>'', 'ttc'=>'TTC', 'ht'=> 'HT');
			$data['options'][] = array(
				'label'     => _('Tarif'),
				'mandatory' => false,
				'name'      => 'tarif',
				'type'      => 'select',
				'selected'  => ( isset( $vals['tarif'] ) ? $vals['tarif'] : 'ht' ),
				'value'     => $tarif
			);
			$rCat = prd_prices_categories_get();

			$selected = array(0=>"");

			if( $rCat && ria_mysql_num_rows($rCat) ){
				while( $cat = ria_mysql_fetch_assoc($rCat) ){
					$selected[$cat['id']] = $cat['name'];
				}
				$data['options'][] = array(
					'label'     => _('Catégorie tarifaire'),
					'mandatory' => false,
					'name'      => 'gu_catf',
					'type'      => 'select',
					'selected'  => ( isset( $vals['gu_catf'] ) ? $vals['gu_catf'] : ''),
					'value'     => $selected
				);
			}

			if( $config['USER_RIASTUDIO'] ){
				$data['options'][] = array(
					'label'	=> _('Prix en promotion'),
					'mandatory' => false,
					'name' => 'promo',
					'type' => 'checkbox',
					'value' => ( isset( $vals['promo']) ? 1 : 0 )
					);

				$data['options'][] = array(
					'label'	=> _('Inclure les promotions à 0 €'),
					'mandatory' => false,
					'name' => 'include-promo',
					'type' => 'checkbox',
					'value' => ( isset( $vals['include-promo']) ? 1 : 0 ),
					'show'	=> ( isset( $vals['promo']) ? 1 : 0 ),
					'title'	=> _('En cochant cette option, les tarifs promotionnels à zéro euro seront aussi importés')
					);
			}
		}

		if( $schema['code'] == 'PRD_STOCK' ){
			$rCat = prd_deposits_get();

			$selected = array(0=>"");

			if( $rCat && ria_mysql_num_rows($rCat) ){
				while( $cat = ria_mysql_fetch_assoc($rCat) ){
					$selected[$cat['id']] = $cat['name'];
				}
				$data['options'][] = array(
					'label'     => _('Dépots'),
					'mandatory' => false,
					'name'      => 'prd_dps',
					'type'      => 'select',
					'selected'  => ( isset( $map['vals'] ) ? $map['vals'] : '' ),
					'value'     => $selected
				);
			}
		}

		switch( $schema['code'] ){
			case 'PRC_DISCOUNT_TYPE':
				if(!$backup){
					$fld = array(0=>_("Choisir un type de remise"));
					$r = prc_types_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove=true;
					$map_type = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$map_type = json_decode($map['vals']);
						$remove=false;
					}

					$data['options'][] = array(
								'label'     => _('Type de remise'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'PRC_DISCOUNT_TYPE'
							);

					foreach($map_type as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Type de remise'),
							'mandatory' => true,
							'type'      => 'select_input',
							'name'      => 'PRC_DISCOUNT_TYPE',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Type de remise'),
						'mandatory' => true,
						'type'      => 'href',
						'name'      => 'PRC_DISCOUNT_TYPE',
						'remove'	=> $remove
					);
				}
				break;
			case 'USR_ADR_TYPE_ID':
				if(!$backup){
					$fld = array(0=>_("Choisir type d'adresse"));
					$r = gu_adr_types_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove=true;
					$map_adr_type = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_adr_type = json_decode($map['vals']);
					}

					$data['options'][] = array(
									'label'     => _('Type d\'adresse'),
									'mandatory' => true,
									'type'      => 'label',
									'name'      => 'usr_adr_type'
								);

					foreach($map_adr_type as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Type d\'adresse'),
							'mandatory' => true,
							'type'      => 'select_input',
							'name'      => 'usr_adr_type',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Type d\'adresse'),
						'mandatory' => true,
						'type'      => 'href',
						'name'      => 'usr_adr_type',
						'remove'	=> $remove
					);
				}
				break;
			case 'USR_CIV':
				if(!$backup){
					$fld = array(0=>_("Choisir une civilité"));
					$r = gu_titles_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);
					$fld['other'] = _("Autre");

					$remove = true;
					$map_civ = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_civ = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Civilité'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'usr_civ'
							);

					foreach($map_civ as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Civilité'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'usr_civ',
							'selected'  => ($id==4? 'other':$id),
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Civilité'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'usr_civ',
						'remove'	=> $remove
					);
				}
				break;
			case 'USR_PRF_ID':
				if(!$backup){
					$fld = array(0 => _("Choisir un droit d'accès"));
					$r = gu_profiles_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							if($s['id'] == 1){//bloque l'import de compte administrateur
								continue;
							}
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);
					$fld['other'] = _("Autre");

					$remove=true;
					$map_prf = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_prf = json_decode($map['vals']);
					}

					$data['options'][] = array(
									'label'     => _('Droits d\'accès'),
									'mandatory' => true,
									'type'      => 'label',
									'name'      => 'usr_prf_id'
								);

					foreach($map_prf as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Droits d\'accès'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'usr_prf_id',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Droits d\'accès'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'usr_prf_id',
						'remove' 	=> $remove
					);
				}
				break;
			case 'USR_PAYMENT':
				if(!$backup){
					$fld = array(0 => _("Choisir un moyen de paiement"));
					$r = ord_payment_types_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);
					$fld['other'] = "Autre";

					$remove=true;
					$map_payment = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_payment = json_decode($map['vals']);
					}

					$data['options'][] = array(
									'label'     => _('Moyen de paiement'),
									'mandatory' => true,
									'type'      => 'label',
									'name'      => 'usr_payment'
								);

					foreach($map_payment as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Moyen de paiement'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'usr_payment',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Moyen de paiement'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'usr_payment',
						'remove' 	=> $remove
					);
				}
				break;
			case 'USR_CAT_ID':
				if(!$backup){
					$fld = array(0 => _("Choisir une categorie comptable"));
					$r = gu_accounting_categories_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);
					$fld['other'] = "Autre";

					$remove=true;
					$map_cat = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != ""){
						$remove=false;
						$map_cat = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Catégorie comptable'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'usr_cat_id'
							);

					foreach($map_cat as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Catégorie comptable'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'usr_cat_id',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Catégorie comptable'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'usr_cat_id',
						'remove'	=> $remove
					);
				}
				break;
			case 'USR_PRC_ID':
				if(!$backup){
					$fld = array(0=>_("Choisir une categorie tarifaire"));
					$r = prd_prices_categories_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);
					$fld['other'] = "Autre";

					$remove=true;
					$map_prc = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != ""){
						$remove=false;
						$map_prc = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Catégorie tarifaire'),
								'mandatory' => true,
								'type'      => 'label',
								'name'      => 'usr_prc_id'
							);

					foreach($map_prc as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Catégorie tarifaire'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'usr_prc_id',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Catégorie tarifaire'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'usr_prc_id',
						'remove'	=> $remove
					);
				}
				break;
			case 'PRC_USR_PRC':
				if(!$backup){
					$fld = array(0=>_("Choisir une categorie tarifaire"));
					$r = prd_prices_categories_get();
					if( $r ){
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove=true;
					$map_prc = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != ""){
						$remove=false;
						$map_prc = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Catégorie tarifaire'),
								'mandatory' => false,
								'type'      => 'label',
								'name'      => 'prc_usr_prc'
							);

					foreach($map_prc as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Catégorie tarifaire'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'prc_usr_prc',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Catégorie tarifaire'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'prc_usr_prc',
						'remove'	=> $remove
					);
				}
				break;
			case 'ORD_STATE':
				if(!$backup){
					$fld = array(0=>_("Choisir un état"));
					$r = ord_states_get( );
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove = true;
					$map_state = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_state = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Etat'),
								'mandatory' => false,
								'type'      => 'label',
								'name'      => 'ord_state'
							);

					foreach($map_state as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Etat'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'ord_state',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Etat'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'ord_state',
						'remove'	=> $remove
					);
				}
				break;
			case 'ORD_SRV':
				if(!$backup){
					$fld = array(0=>_("Choisir un service"));
					$r = dlv_services_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove = true;
					$map_srv = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_srv = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Service'),
								'mandatory' => false,
								'type'      => 'label',
								'name'      => 'ord_srv'
							);

					foreach($map_srv as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Service'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'ord_srv',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Service'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'ord_srv',
						'remove'	=> $remove
					);
				}
				break;
			case 'ORD_PAY':
				if(!$backup){
					$fld = array(0 => _("Choisir un moyen de paiement"));
					$r = ord_payment_types_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove=true;
					$map_payment = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_payment = json_decode($map['vals']);
					}

					$data['options'][] = array(
									'label'     => _('Moyen de paiement'),
									'mandatory' => false,
									'type'      => 'label',
									'name'      => 'ord_pay'
								);

					foreach($map_payment as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Moyen de paiement'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'ord_pay',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Moyen de paiement'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'ord_pay',
						'remove' 	=> $remove
					);
				}
				break;
			case 'ORD_CARD':
				if(!$backup){
					$fld = array(0 => _("Choisir un type de CB"));
					$r = ord_card_types_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove=true;
					$map_payment = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_payment = json_decode($map['vals']);
					}

					$data['options'][] = array(
									'label'     => _('Type de CB'),
									'mandatory' => true,
									'type'      => 'label',
									'name'      => 'ord_card'
								);

					foreach($map_payment as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Type de CB'),
							'mandatory' => true,
							'type'      => 'select_input',
							'name'      => 'ord_card',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Type de CB'),
						'mandatory' => true,
						'type'      => 'href',
						'name'      => 'ord_card',
						'remove' 	=> $remove
					);
				}
				break;
			case 'ORD_WST_ID':
				if(!$backup){
					$fld = array(0=>_("Choisir un site"));
					$r = wst_websites_get();
					if ($r) {
						while( $s = ria_mysql_fetch_assoc( $r ) ){
							$fld[$s['id']] = $s['name'];
						}
					}
					asort($fld);

					$remove = true;
					$map_srv = array(0 => '');
					if(isset($map['vals']) && $map['vals'] != "" ){
						$remove=false;
						$map_srv = json_decode($map['vals']);
					}

					$data['options'][] = array(
								'label'     => _('Site'),
								'mandatory' => false,
								'type'      => 'label',
								'name'      => 'ord_wst_id'
							);

					foreach($map_srv as $id => $vals)
					{
						$data['options'][] = array(
							'label'     => _('Site'),
							'mandatory' => false,
							'type'      => 'select_input',
							'name'      => 'ord_wst_id',
							'selected'  => $id,
							'value'     => $fld,
							'valueInput' => $vals
						);
					}

					$data['options'][] = array(
						'label'     => _('Site'),
						'mandatory' => false,
						'type'      => 'href',
						'name'      => 'ord_wst_id',
						'remove'	=> $remove
					);
				}
				break;


		}

		if( in_array( $schema['code'], array( 'USR_ADR_TYPE_ID', 'USR_ADDRESS1', 'USR_ADDRESS2', 'USR_ZIP_CODE', 'USR_CITY', 'USR_COUNTRY', 'USR_LATITUDE', 'USR_LONGITUDE', 'USR_PHONE', 'USR_FAX',
				 'USR_CELLPHONE', 'USR_PHONE_WORK', 'USR_SOCIETY', 'USR_FIRSTNAME', 'USR_LASTNAME', 'USR_CIV', 'USR_ADR_REF' )) ){

			if( isset( $map['vals'] ) && trim( $map['vals'] ) != '' ){
				$vals = json_decode( $map['vals'], true );
			}

			$data['options'][] = array(
				'label'	=> _('L\'adresse de facturation'),
				'mandatory' => false,
				'name' => 'inv_adr',
				'type' => 'checkbox',
				'value' => ( !isset( $vals['inv_adr']) ? 1 : ( isset( $vals['inv_adr']) && $vals['inv_adr'] ? 1 : 0 ) ),
			);

			$data['options'][] = array(
				'label'	=> _('L\'adresse de livraison'),
				'mandatory' => false,
				'name' => 'dlv_adr',
				'type' => 'checkbox',
				'value' => ( !isset( $vals['dlv_adr']) ? 1 : ( isset( $vals['dlv_adr']) && $vals['dlv_adr'] ? 1 : 0 ) ),
			);
		}

		return $data;
	}

	if( isset($schemas) ){
		$dups = array();
		$comp = $schemas;
		foreach($schemas as $k => $c){

			// Ne prend pas en compte l'option par défaut "Sélectionner une correspondance"
			if( $c == 'DEFAULT' ){
				continue;
			}

			if( in_array($c, ipt_multiple_cols()) ){
				continue;
			}
			unset($comp[$k]);
			if( in_array($c, $comp) ){
				$dups[$k] = $c;
			}
			$comp[$k] = $c;
		}
		return json_encode($dups, JSON_FORCE_OBJECT );
	}

	if(isset($getDefault)){
		$rMapping = ipt_mapping_get( $imp_id, $code );
		if(!$rMapping ){
			return '0';
		}else{
			$map = ria_mysql_fetch_assoc( $rMapping );
			return $map['vals'];
		}

	}

}

/** Cette fonction affiche l'encart d'un import.
 * 	@param array $import Obligatoire, information sur l'import tel que retourner par ipt_imports_get()
 * 	@param string $step Optionnel, l'étape à laquelle cet encart est affiché (par défaut 'index', valeurs acceptées : 'index', 'mapping')
 * 	@return string Le DOM de l'encart
 */
function view_imports_card( $import, $step='index' ){
	if( !ria_array_key_exists(array('state', 'info', 'cls_id', 'date_created', 'action', 'state', 'id', 'line_count'), $import) ){
		return false;
	}
	if( !in_array($step, array('index', 'mapping')) ){
		return false;
	}

	// Le complément en base est stocké au format JSON.
	// Si celui n'a pas déjà été tranformé en tableau, alors cela est fait ici
	if( !is_array($import['info']) ){
		$import['info'] = json_decode( $import['info'], true );
	}

	ob_start();

	print '
		<div class="imports-unfinished-file '.htmlspecialchars( $step ).'">
			<div class="alert --'.htmlspecialchars($import['state']).'">
	';

	$title = '';
	$del_class = '';

	switch ($import['state']) {
		case 'create':
			$del_class = 'create';
		break;
		case 'pending':
		case 'processing':
			$del_class = 'processing';
			break;
		case 'finished':
			$del_class = 'finish';
			$title = _('Import réussi !');
			break;
		case 'error':
			$del_class = 'finish';
			$title = _('Erreur, l\'import n\'a pu être finalisé');
			break;
		case 'warning':
			$del_class = 'finish';
			$title = _('Attention ! Import effectué partiellement');
			break;
	}

	if( $step == 'index' ){
		if( trim($title) != '' ){
			print '<h3>'.htmlspecialchars( $title ).'</h3>';
		}

		print '
				<div class="alldata">
					<div class="unfinished-columns">
						<p class="imp-name"><a href="mapping.php?imp='.$import['id'].'">'.htmlspecialchars( ipt_filter_import_name($import['name']) ).'</a></p>
					</div>
		';
	}else{
		print '
			<div class="alldata">
				<div class="unfinished-columns">
					<p><a href="mapping.php?imp='.$import['id'].'&download=1">'.htmlspecialchars( ipt_filter_import_name($import['name']) ).'</a></p>
				</div>
		';
	}

	if( !isset($import['info']['sub_class']) ){
		$import['info']['sub_class'] = '';
	}

	print '
		<div class="unfinished-columns">
			<p>'._('Import :').' '.htmlspecialchars( view_import_get_name($import['cls_id'], $import['info']['sub_class']) ).'</p>
		</div>

		<div class="unfinished-columns">
			<p>'._('Ajouté le :').' '.ria_date_format( $import['date_created'] ).'</p>
		</div>

		<div class="unfinished-columns">
			<p>'.ipt_action_display( $import['action'] ).'
	';

	// Affichage de l'information de récurrence lorsque celle-ci a été paramétré sur un import
	if( in_array($import['period'], array('day', 'week', 'month')) ){
		switch( $import['period'] ){
			case 'day':
				print '<br />'.str_replace('#param[time]#', $import['period_value'], _('tous les jours à #param[time]#h'));
				break;
			case 'week':
				print '<br />'.str_replace('#param[day]#', strtolower2(days_french($import['period_value'])), _('toutes les semaines le #param[day]#'));
				break;
			case 'month':
				print '<br />'.str_replace('#param[day]#', $import['period_value'], _('tous les mois le #param[day]#'));
				break;
		}
	}

	$show_progress = $import['state']=='pending' || $import['state']=='processing';

	print '
			</p>
		</div>

		<div class="unfinished-columns state '.ipt_imports_state_css_class( $import['state'] ).'"
			id="state-'.$import['id'].'" '.( $show_progress || $step=='mapping' ? 'data-imp="'.$import['id'].'"' : '' ).'>
	';

	if( $show_progress ){
		print '
			<p><strong>'.ipt_state_display( $import['state'] ).'</strong></p>
			<div class="progress-bar-pending">
				<div class="span-progression">0/'.$import['line_count'].'</div>
			</div>
		';
	}else{
		print '
			<p><strong>'.ipt_state_display( $import['state'] ).'</strong>
		';

		$report_id = false;

		// Action spécifique si l'import est en warning ou en erreur
		if( in_array($import['state'], array('warning', 'error')) ){
			// On récupère le rapport lié à cet import afin de rajouter le lien vers ce dernier
			$r_report = ipt_reports_get( 0, $import['id'] );
			if( $r_report && ria_mysql_num_rows($r_report) ){
				$report = ria_mysql_fetch_assoc( $r_report );
				$report_id = $report['id'];
			}
		}

		if ($report_id !== false) {
			print '			<br /><a href="reports.php?report=' . $report_id . '">' . _('Télécharger le rapport') . '</a>';
		}

		print '
			</p>
		';
	}

		print '
					</div>
					<div class="unfinished-file-delete '.$del_class.'">
						<input type="submit" name="del_imp" value="'.$import['id'].'" class="btn-delete" title="'._('Supprimer l\'import').'"/>
					</div>
				</div>
			</div>
		</div>
	';

	return ob_get_clean();
}

/** Cette fonction permet de récupérer le nom du type d'imports.
 * 	@param int $cls_id Obligatoire, identifiant d'une classe
 * 	@param string $info Optionnel, code de distinction de deux imports d'une même classes
 * 	@return string Le nom de l'import
 */
function view_import_get_name( $cls_id, $info='' ){
	$classes = view_import_get_classes();

	$name = '';
	foreach( $classes as $one_classe ){
		if( $one_classe['cls_id'] == $cls_id && $one_classe['info'] == $info ){
			$name = $one_classe['name'];
			break;
		}
	}

	return $name;
}

/** Cette fonction charge un tableau des classes pour lesquelles un import est possible. Elle tient compte des droits
 *  d'accès de l'utilisateur actuellement connecté.
 * 	@param bool $just_cls_ids Optionnel, mettre true pour ne récupérer que les identifiants de classes
 * 	@return array Un tableau associatif des classes (id => name)
 */
function view_import_get_classes( $just_cls_ids=false ){

	$ar_cls = array();

	// Accès à l'import de produits
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_PRD') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_PRODUCT,
			'name' 		=> _('Produits'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de comptes
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_USR') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_USER,
			'name' 		=> _('Comptes clients'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de stocks
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_STK') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_STOCK,
			'name' 		=> _('Stocks'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de marque
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_BRD') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_BRAND,
			'name' 		=> _('Marques'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de commandes
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_ORDER') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_ORDER,
			'name' 		=> _('Commandes'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de catégories
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_CAT') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_CATEGORY,
			'name' 		=> _('Catégories de produits'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de promotions sur produits
	if(gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_PMT') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_PRICE,
			'name' 		=> _('Promotions sur les produits'),
			'info' 		=> '',
		];
	}

	// Accès à l'import de modèles de commande
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_MODEL') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_ORDER,
			'name' 		=> _('Modèles de commandes'),
			'info' 		=> 'model',
		];
	}

	// Accès à l'import de catégories
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_DISCOUNT') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_PRICE,
			'name' 		=> _('Tarifs'),
			'info' 		=> 'user_discount',
		];
	}

	// Accès à l'import de magasins
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_STORE') ){
		$ar_cls[] = [
			'cls_id' 	=> CLS_STORE,
			'name' 		=> _('Magasins'),
			'info' 		=> '',
		];
	}

	// Si l'on ne souhaite que les identifiants de classes (paramètre $just_cls_id),
	// on transforme le tableau de retour pour n'avoir que les identifiants
	if( $just_cls_ids ){
		$temp = array();

		foreach( $ar_cls as $one_cls ){
			$temp[] = $one_cls['cls_id'];
		}

		$ar_cls = array_unique($temp);
	}

	return $ar_cls;
}

/** Cette fonction permet de générer les valeurs des menus déroulants en fonction du type à afficher
 *	@param string $type Obligatoire, le type de valeur du menu déroulant. Les valeurs suivantes sont acceptées : idType, action, state
 *	@param string $code Obligatoire, Code du schéma. De nombreux codes sont supportés dont : PRD_CAT, PRD_BRD_ID, PRC_PRD_ID, STK_PRD_ID, STK_DPS_ID, ...
 *	@return array un tableau avec valeur => titre en français, false si échec. Les clés suivantes sont définies en fonction des arguments :
 *		- id
 *		- ref
 *		- name
 *		- ean
 *		- ref_gescom
 *		- ...
 */
function ipt_view_options_select( $type, $code ){

	$options = array('idType', 'action', 'state');
	if (!in_array($type, $options) ){
		return false;
	}

	switch ($type) {
		case 'idType':
			$value = array(
				0 => _('Choisir le type d\'identifiant')
			);

			switch ($code) {
				case 'PRD_CAT':
					$value['id'] = _('Identifiant de la catégorie');
					$value['ref'] = _('Référence de la catégorie');
					$value['name'] = _('Nom de la catégorie');
					break;
				case 'PRD_BRD_ID':
					$value['id'] = _('Identifiant de la marque');
					$value['name'] = _('Désignation de la marque');
					$value['ref'] = _('Référence de la marque');
					break;
				case 'PRC_PRD_ID':
				case 'STK_PRD_ID':
				case 'ORD_PRD_ID':
					$value['ref'] = _('Référence du produit');
					$value['id'] = _('Identifiant du produit');
					$value['ean'] = _('Code barre du produit');
					$value['ref_gescom'] = _('Identifiant ERP');
					break;
				case 'STK_DPS_ID':
					$value['ref'] = _('Référence du dépot');
					$value['name'] = _('Nom du dépot');
					$value['id'] = _('Identifiant du dépot');
					break;
				case 'CAT_PARENT':
					$value['ref'] = _('Référence de la catégorie');
					$value['id'] = _('Identifiant de la catégorie');
					break;
				case 'PRD_CANONICAL_LINK_PRD':
					$value['ref'] = _('Référence du produit');
					$value['id'] = _('Identifiant du produit');
					$value['ean'] = _('Code barre du produit');
					$value['ref_gescom'] = _('Identifiant ERP');
					break;
				case 'PRD_CANONICAL_LINK_CAT':
					$value['id'] = _('Identifiant de la catégorie');
					$value['ref'] = _('Référence de la catégorie');
					$value['name'] = _('Nom de la catégorie');
					$value['classmt'] = _('Classement');
					break;
				case 'ORD_SELLER':
				case 'USR_SELLER':
					$value['ref'] = _('Code du représentant');
					$value['email'] = _('Email du représentant');
					$value['id'] = _('Identifiant RiaShop du représentant');
					break;
				case 'USR_RELATION':
					$value['ref'] = _('Code du compte parent');
					$value['email'] = _('Email du compte parent');
					$value['id'] = _('Identifiant RiaShop du compte parent');
					break;
				case 'USR_HIEARCH_PARENT':
					$value['ref'] = _('Code du compte parent');
					$value['email'] = _('Email du compte parent');
					$value['id'] = _('Identifiant RiaShop du compte parent');
					break;
				case 'USR_RELATION_PARENT':
					$value['ref'] = _('Code du représentant');
					$value['email'] = _('Email du représentant');
					$value['id'] = _('Identifiant RiaShop du représentant');
					break;
				case 'USR_HIEARCH_ENFANT':
					$value['ref'] = _('Code du compte enfant');
					$value['email'] = _('Email du compte enfant');
					$value['id'] = _('Identifiant RiaShop du compte enfant');
					break;
				case 'USR_RELATION_ENFANT':
					$value['ref'] = _('Code du compte sous sa responsabilité');
					$value['email'] = _('Email du compte sous sa responsabilité');
					$value['id'] = _('Identifiant RiaShop du compte sous sa responsabilité');
					break;
				case 'ORD_USR':
				case 'PRC_USR':
					$value['ref'] = _('Code client');
					$value['email'] = _('Email du client');
					$value['id'] = _('Identifiant RiaShop du client');
					break;
				default:
					foreach (ipt_rows_get_id_types($code) as $val) {
						$value[$val] = ipt_idType_display($val);
					}
					break;
			}
			break;
		case 'action':
			$value = array(
				0 => _('Choisir l\'action à effectuer')
			);

			switch ($code) {
				case 'PRD_CAT':
					$value['add'] = _('Ajout');
					$value['del'] = _('Suppression');
					break;
				case 'PRD_COLISAGE':
					$value['add'] = _('Ajout');
					$value['upd'] = _('Mise à jour');
					break;
				case 'PRC_PRD_ID':
				case 'USR_SELLER':
				case 'USR_RELATION':
				case 'USR_HIEARCH_PARENT':
				case 'USR_HIEARCH_ENFANT':
				case 'USR_RELATION_PARENT':
				case 'USR_RELATION_ENFANT':
					$value['upd'] = _('Ajout / Mise à jour');
					$value['del'] = _('Suppression');
					break;
				case 'PRD_CANONICAL_LINK_PRD':
				case 'PRD_CANONICAL_LINK_CAT':
					$value['add/upd'] = _('Ajout / Mise à jour');
					$value['del'] = _('Suppression');
					break;
				case 'IMAGE':
					$value['add'] = _('Ajout');
					$value['upd'] = _('Mise à jour');
					break;
				default:
					foreach (ipt_imports_get_import_actions() as $val) {
						if ($val == 'add/upd') {
							continue;
						}
						$value[$val] = ipt_action_display($val);
					}
			}
			break;
		case 'state':
			$value = array(
				0 => _('Choisir le statut')
			);

			$states = array_merge(ipt_imports_get_import_actions(), ipt_rows_get_actions());
			foreach ($states as $val) {
				$value[$val] = ipt_state_display($val);
			}
			break;
	}

	return $value;
}


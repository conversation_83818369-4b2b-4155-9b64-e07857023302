<?php
// \cond onlyria

/** \defgroup model_db_access Gestion des requêtes à la base de données MySQL
 *  \ingroup system
 *	Ce module comprend les fonctions nécessaires à la surcharge des fonctions MySQL.
 *
 *	@{
 */

/** Cette fonction permet détablir une connexion avec une base de données (en local ou à distance)
 *	@param array $db_connect Obligatoire, quelle configuration de connexion utiliser
 *	@param bool $reload Optionnel, par défaut à False, mettre True pour forçer la reconnexion
 *	@return resource|bool La connexion MySQL si elle est établie, False dans le cas contraire
 *	@see https://www.php.net/manual/fr/function.mysql-connect.php
 */
function ria_mysql_connection( $db_connect, $reload=false ){
	global $ria_db_connect;

	$ria_db_connect = array(
		_DB_RIASHOP => array(
			'server'            => getenv('ENVRIA_BDD_SERVER'),
			'user'              => getenv('ENVRIA_BDD_LOGIN'),
			'password'          => getenv('ENVRIA_BDD_PWD'),
			'base'              => getenv('ENVRIA_BDD_NAME'),
			'link_identifier'   => false
		),
	);

	if( !is_array($ria_db_connect) || !array_key_exists($db_connect, $ria_db_connect) ){
		return false;
	}

	// Renvoi la connexion MySQL si celle-ci a déjà été établie
	if (!$reload && $ria_db_connect[ $db_connect ]['link_identifier']) {
		return $ria_db_connect[ $db_connect ]['link_identifier'];
	}

	// Ouverture de la connexion
	$link_identifier = mysql_connect( $ria_db_connect[ $db_connect ]['server'], $ria_db_connect[ $db_connect ]['user'], $ria_db_connect[ $db_connect ]['password'] );
	if (!$link_identifier) {
		error_log( '[critical] Base de données indisponible, impossible d\'accéder au serveur : '. $ria_db_connect[ $db_connect ]['server'] );
		header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
		die( 'Base de données indisponible' );
	}

	if( !@mysql_select_db( $ria_db_connect[ $db_connect ]['base'] ) ){
		error_log( '[critical] Base de données indisponible, impossible d\'accéder à la base : '. $ria_db_connect[ $db_connect ]['base'] );
		header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
		die( 'Base de données indisponible' );
	}

	if( !@mysql_query( 'set names \'utf8\'', $link_identifier ) ){
		error_log( '[critical] Erreur sur la définition de l\'encodage' );
		header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
		die( 'Erreur sur la définition de l\'encodage' );
	}

	$ria_db_connect[ $db_connect ]['link_identifier'] = $link_identifier;
	return $link_identifier;
}

/** Surcharge pour la fonction mysql_query(), les requêtes seront enregistrées dans la barre de débug si celle-ci est activée.
 *	@param string $sql Obligatoire requête SQL
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return bool False en cas d'erreur sinon le résultat de mysql_query()
 *	@see https://www.php.net/manual/fr/function.mysql-query.php
 */
function ria_mysql_query($sql, $db_connect=false){
	global $ria_debug_timer, $ria_db_selected, $ar_debug_request_bdd;

	if ($db_connect === false) {
		$db_connect = _DB_DEFAULT;

		if ($ria_db_selected !== false) {
			$db_connect = $ria_db_selected;
		}
	}

	$link_identifier = ria_mysql_connection( $db_connect );
	if( $link_identifier === false ){
		return false;
	}

	$start = microtime( true );
	$res_query = mysql_query( $sql, $link_identifier );
	$end = microtime( true );
	if( isset($_GET['Ria_show_SQL_Request']) && in_array($_GET['Ria_show_SQL_Request'], ['debug', 'show']) ){
		if( $_GET['Ria_show_SQL_Request'] == 'show' ){
			print '<br />'.number_format(round(($end-$start), 6), 6, ',', '').';'.htmlspecialchars(trim($sql));
		}

		$md5_sql = md5( strtolower2(trim($sql)) );

		if( !isset($ar_debug_request_bdd[ $md5_sql ]) ){
			$ar_debug_request_bdd[ $md5_sql ] = [
				'SQL' => $sql,
				'min' => ( $end - $start ),
				'max' => ( $end - $start ),
				'sum' => ( $end - $start ),
				'count' => 1
			];
		}else{
			$time = ( $end - $start );

			if( $ar_debug_request_bdd[ $md5_sql ]['min'] > $time ){
				$ar_debug_request_bdd[ $md5_sql ]['min'] = $time;
			}

			if( $ar_debug_request_bdd[ $md5_sql ]['max'] < $time ){
				$ar_debug_request_bdd[ $md5_sql ]['max'] = $time;
			}

			$ar_debug_request_bdd[ $md5_sql ]['sum'] += $time;
			$ar_debug_request_bdd[ $md5_sql ]['count']++;
		}
	}

	// Enregistre dans error_log les requêtes SQL qui ont échoué. Le contexte est fourni avec debug_backtrace
	if( ($res_query === false) && !(preg_match("/Duplicate entry/", ria_mysql_error())) ){
		error_log(
			"La requête SQL suivante à échoué :\n"
			.'======================================='."\n"
			.ria_mysql_error()." :\n"
			.'======================================='."\n"
			.$sql."\n".
			'======================================='."\n"
			.'Contexte (backtrace) :'."\n"
			.print_r(
				debug_backtrace( DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS, 1 ),
				true
			)
		);
	}

	if (ria_debug_is_actived() && !ria_debug_page_excluded()) {
		if (!isset($_SESSION['ria_debug']) || !is_array($_SESSION['ria_debug'])) {
			$_SESSION['ria_debug'] = array( 'get' => array(), 'post' => array(), 'request' => array() );
		}

		$method = 'request';
		if (isset($_SERVER['REQUEST_METHOD']) && in_array($_SERVER['REQUEST_METHOD'], array('GET', 'POST'))) {
			$method = strtolower( $_SERVER['REQUEST_METHOD'] );
		}

		$_SESSION['ria_debug'][ $method ][ $_SERVER['SCRIPT_URL'] ][ $ria_debug_timer ][] = array(
			'time' => $end - $start,
			'sql'  => $sql,
			'server' => $_SERVER,
			'backtrace' => debug_backtrace(),
		);
	}
	return $res_query;
}

/** Cette fonction permet de vérifier qu'une ressource est bien un résultat MySQL
*	@param resource $ressource Obligatoire, ressource à vérifier
*	@return bool True s'il s'agit bien d'un résultat MySQL, False dans le cas contraire
*/
function ria_mysql_control_ressource( $ressource ){
	$res_type = is_resource($ressource) ? get_resource_type($ressource) : gettype($ressource);

	return (bool) strstr( $res_type, 'mysql' );
}

/** Cette fonction permet de surcharger la fonction mysql_affected_rows().
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return int Le résultat tel que retourné par mysql_affected_rows()
 *	@see https://www.php.net/manual/fr/function.mysql-affected-rows.php
 */
function ria_mysql_affected_rows( $db_connect=false ){
	global $ria_db_selected;

	if ($db_connect === false) {
		$db_connect = _DB_DEFAULT;

		if ($ria_db_selected !== false) {
			$db_connect = $ria_db_selected;
		}
	}

	$link_identifier = ria_mysql_connection( $db_connect );
	return mysql_affected_rows( $link_identifier );
}

/** Cette fonction permet de surcharger la fonction mysql_data_seek().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query().
 *	@param int $row_number Obligatoire, La position de la ligne désirée pour le nouveau pointeur de résultats
 *	@return bool Le résultat tel que retourné par mysql_data_seek()
 *	@see https://www.php.net/manual/fr/function.mysql-data-seek.php
 */
function ria_mysql_data_seek( $result, $row_number ){
	return mysql_data_seek( $result, $row_number );
}

/** Cette fonction permet de surcharger la fonction mysql_errno().
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return int Le résultat tel que retourné par mysql_errno()
 *	@see https://www.php.net/manual/fr/function.mysql-errno.php
 */
function ria_mysql_errno( $db_connect=false ){
	global $ria_db_selected;

	if ($db_connect === false) {
		$db_connect = _DB_DEFAULT;

		if ($ria_db_selected !== false) {
			$db_connect = $ria_db_selected;
		}
	}

	$link_identifier = ria_mysql_connection( $db_connect );
	return mysql_errno( $link_identifier );
}

/** Cette fonction permet de surcharger la fonction mysql_error().
 *	@param resource $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return string Le résultat tel que retourné par mysql_error()
 *	@see https://www.php.net/manual/fr/function.mysql-error.php
 */
function ria_mysql_error( $db_connect=false ){
	global $ria_db_selected;

	if ($db_connect === false) {
		$db_connect = _DB_DEFAULT;

		if ($ria_db_selected !== false) {
			$db_connect = $ria_db_selected;
		}
	}

	$link_identifier = ria_mysql_connection( $db_connect );
	return mysql_error( $link_identifier );
}

/** Cette fonction permet de surcharger la fonction mysql_escape_string().
 *	@param string $unescaped_string Obligatoire, La chaîne qui doit être protégée
 *	@return string Le résultat tel que retourné par mysql_escape_string()
 *	@see https://www.php.net/manual/fr/function.mysql-escape-string.php
 */
function ria_mysql_escape_string( $unescaped_string ){
	return mysql_escape_string( $unescaped_string );
}

/** Cette fonction permet de surcharger la fonction mysql_num_rows().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@return int Le résultat tel que retourné par mysql_num_rows()
 *	@see https://www.php.net/manual/fr/function.mysql-num-rows.php
 */
function ria_mysql_num_rows( $result ){
	if (!ria_mysql_control_ressource($result)) {
		return false;
	}

	return mysql_num_rows( $result );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_array().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@param int $result_type Optionnel, Le type de tableau à récupérer. C'est une constante qui peut prendre les valeurs suivantes : MYSQL_ASSOC, MYSQL_NUM, et MYSQL_BOTH
 *	@return array Le résultat tel que retourné par mysql_fetch_array()
 *	@see https://www.php.net/manual/fr/function.mysql-fetch-array.php
 */
function ria_mysql_fetch_array( $result, $result_type=MYSQL_BOTH ){
	if( !is_resource($result) ){
		error_log(
			'Erreur d\'appel à ria_mysql_fetch_array : '.
			print_r(
				debug_backtrace( DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS, 1 ),
				true
			)
		);
	}
	return mysql_fetch_array( $result, $result_type );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_assoc().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@return array Le résultat tel que retourné par mysql_fetch_assoc()
 *	@see https://www.php.net/manual/fr/function.mysql-fetch-assoc.php
 */
function ria_mysql_fetch_assoc( $result ){
	if( $result === false ){
		return false;
	}

	return mysql_fetch_assoc( $result );
}

/** Cette fonction permet de récupérer le résulat mysql sous forme de tableau
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *  @return array Le résultat tel que retourné par mysql_fetch_assoc() sous forme de tableau
 * 	@see https://www.php.net/manual/fr/function.mysql-fetch-assoc.php
 */
function ria_mysql_fetch_assoc_all( $result ){
	$response = array();
	while( $r = ria_mysql_fetch_assoc($result) ){
		$response[] = $r;
	}

	return $response;
}

/** Cette fonction permet de surcharger la fonction mysql_insert_id().
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return int Le résultat tel que retourné par mysql_insert_id()
 *	@see https://www.php.net/manual/fr/function.mysql-insert-id.php
 */
function ria_mysql_insert_id( $db_connect=false ){
	global $ria_db_selected;

	if ($db_connect === false) {
		$db_connect = _DB_DEFAULT;

		if ($ria_db_selected !== false) {
			$db_connect = $ria_db_selected;
		}
	}

	$link_identifier = ria_mysql_connection( $db_connect );
    $id_temp = mysql_insert_id( $link_identifier );
    if (!$id_temp) {
        try {
            $res_bdd = ria_mysql_query( "SELECT LAST_INSERT_ID()");
            $id_temp = ria_mysql_result($res_bdd, 0, 0);
        }
        catch (Exception $e) {}
    }

	return $id_temp;
}

/** Cette fonction permet de surcharger la fonction mysql_result().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query().
 *	@param int $row Obligatoire, Le numéro de la ligne à récupérer. Les numéros de lignes commencent à 0.
 *	@param int $field Optionnel, Le nom ou la position du champ à récupérer.
 *	@return mixed Le résultat tel que retourné par mysql_result()
 *	@see https://www.php.net/manual/fr/function.mysql-result.php
 */
function ria_mysql_result( $result, $row, $field=0 ){
	return mysql_result( $result, $row, $field );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_row().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@return array Le résultat tel que retourné par mysql_fetch_row()
 *	@see https://www.php.net/manual/fr/function.mysql-fetch-row.php
 */
function ria_mysql_fetch_row( $result ){
	return mysql_fetch_row( $result );
}

/** Cette fonction permet de surcharger la fonction mysql_field_name().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@param int $field_offset Obligatoire, La position numérique du champ, field_offset commence à 0. Si field_offset n'existe pas, une alerte E_WARNING sera également générée
 *	@return string Le résultat tel que retourné par mysql_field_name()
 *	@see https://www.php.net/manual/fr/function.mysql-field-name.php
 */
function ria_mysql_field_name( $result, $field_offset ){
	return mysql_field_name( $result, $field_offset );
}

/** Cette fonction permet de surcharger la fonction mysql_field_table().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@param int $field_offset Obligatoire, La position numérique du champ. field_offset commence à 0. Si field_offset n'existe pas, une alerte E_WARNING sera également générée
 *	@return string Le résultat tel que retourné par mysql_field_table()
 *	@see https://www.php.net/manual/fr/function.mysql-field-table.php
 */
function ria_mysql_field_table( $result, $field_offset ){
	return mysql_field_table( $result, $field_offset );
}

/** Cette fonction permet de surcharger la fonction mysql_num_fields().
 *	@param resource $result Obligatoire, La ressource de résultat qui vient d'être évaluée. Ce résultat vient de l'appel à la fonction mysql_query()
 *	@return int Le résultat tel que retourné par mysql_num_fields()
 *	@see https://www.php.net/manual/fr/function.mysql-num-fields.php
 */
function ria_mysql_num_fields( $result ){
	return mysql_num_fields( $result );
}
/// @}

// \endcond

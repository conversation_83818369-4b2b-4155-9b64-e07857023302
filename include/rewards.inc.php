<?php

require_once('define.inc.php');
require_once('users.inc.php');
require_once('orders.inc.php');
require_once('profiles.inc.php');
require_once('products.inc.php');
require_once('brands.inc.php');
require_once('strings.inc.php');
require_once('stats.inc.php');
require_once('messages.inc.php');
require_once('cfg.emails.inc.php');

/** \defgroup model_rewards Programme de fidélité
 *  \ingroup cpq
 *	Ce module comprend les fonctions nécessaires à la gestion du programme de fidélité.
 *	Les fonctions permettant la gestion des tables annexes sont regroupées dans des modules séparés.
 *	@{
 */

// \cond onlyria
/** \defgroup model_rewards_config Configuration du système
 *	\ingroup model_rewards
 *	Les fonctions contenus dans ce module servent à configurer/récupérer le système de points de fidélité.
 *	@{
 */
// \endcond

// \cond onlydev
/** \defgroup model_rewards_config Calcul des points
 *	\ingroup model_rewards
 *	Les fonctions contenus dans ce module servent à calculer les points sur une commandes.
 *	@{
 */
// \endcond

 // \cond onlyria
/** Cette fonction permet d'ajouter une nouvelle configuration du système.
 *	@param int $prf Obligatoire, identifiant d'un profil client pour lequel la configuration est mise en place
 *	@param $ratio Obligatoire, ratio mis en place sous cette forme array('amount'=>200, 'pts'=>1250)
 *	@param $days Obligatoire, nombre de jours où les points sont valides. Indiquer 0 pour "Aucune limite"
 *	@param $type_amount Obligatoire, type de montant utilisé comme configuration ('ht' ou 'ttc')
 *
 *	@param $cumul_pts Optionnel, par défaut les points de fidélité sont cumulables, mettre false pour ne plus les cumuler.
 *	@param $type_calc Optionnel, par défaut 'order' les points sont calculés lors de la commande, mettre 'invoice' pour les calculer lors de la facturation
 *	@param $type Optionnel, par défaut le système fonctionne par conversion de points en euro (1), mettre 2 pour un fonctionnement par code promotion 3 pour les produits
 *	@param int $wst Optionnel, identifiant d'un site, par défaut on récupère celui contenu dans $config['wst_id']
 *
 *	@param $ar_convert Obligatoire si $type=1, paramètre du fonctionnement par conversion, tableau contenant :
 *						- pts : points utilisés lors de la conversion
 *						- amount : valeur en Euro des points utilisés lors de la conversion
 *						- min_amount : minimum de commande permettant d'utiliser des points de fidélités, 0 si aucun minimum
 *						- days_before_lost : nombre de jours avant la péremption des points fidélité auxquels l'alerte aux clients est envoyée, 0 pour aucune alerte
 *
 *	@param $ar_code Obligatoire si $type=2, paramètre du fonctionnement par promotion, tableau contenant :
 *						- pts : nombre de points à atteindre pour obtenir un code promotion
 *						- discount : montant de la remise (% ou valeur)
 *						- discount_type : type de remise, mettre 0 pour une remise en valeur, 1 pour une remise en pourcentage
 *						- apply_on : applicable sur (order : toute la commande, min-prd : produit le - cher, max-prd : produit le + cher, min-line : ligne de commande la - chère, max-line : ligne de commande la + chère)
 *						- days : délai en jours de validité d'une promotion
 *						- days_before_lost : nombre de jours avant l'envoi d'une alerte aux clients pour la péremption d'un code promotion, 0 pour aucune alerte
 *
 *	@param $ar_product Obligatoire si $type=3, paramètre du fonctionnement par produit offert, tableau contenant :
 *						- days_before_lost : nombre de jours avant la péremption des points fidélité auxquels l'alerte aux clients est envoyée, 0 pour aucune alerte
 *
 *	@param $type_ratio Optionnel, par défaut le système fonctionne par ratio euros / points type 2 arrondit par palier
 *
 *	@return bool false en cas d'échec de l'ajout, sinon l'identifiant de la nouvelle configuration
 */
function rwd_rewards_add( $prf, $ratio, $days, $type_amount, $cumul_pts=true, $type_calc='order', $type=1, $wst=0, $ar_convert=false, $ar_code=false, $type_ratio=1, $ar_product=false ){
	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( !is_array($ratio) || !isset($ratio['amount'], $ratio['pts']) ){
		return false;
	}

	if( !is_numeric($ratio['amount']) || $ratio['amount']<=0 || !is_numeric($ratio['pts']) || $ratio['pts']<=0 ){
		return false;
	}

	if( !is_numeric($days) || $days<0 ){
		return false;
	}

	$type_amount = strtolower( $type_amount );
	if( !in_array($type_amount, array('ht', 'ttc')) ){
		return false;
	}

	$p = false;
	switch ($type) {
		case _RWD_SYSTEM_POINTS: {
			if( !is_array($ar_convert) || !sizeof($ar_convert) ){
				return false;
			}

			$p = $ar_convert;
			if( !isset($p['pts'], $p['amount'], $p['min_amount'], $p['days_before_lost']) ){
				return false;
			}

			if( !is_numeric($p['pts']) || $p['pts']<=0 ){
				return false;
			}

			if( !is_numeric($p['amount']) || $p['amount']<=0 ){
				return false;
			}

			if( !is_numeric($p['min_amount']) || $p['min_amount']<0 ){
				return false;
			}

			if( !is_numeric($p['days_before_lost']) || $p['days_before_lost']<0 ){
				return false;
			}
			break;
		}
		case _RWD_SYSTEM_CODE: {
			if( !is_array($ar_code) || !sizeof($ar_code) ){
				return false;
			}

			$p = $ar_code;
			if( !isset($p['pts'], $p['discount'], $p['discount_type'], $p['apply_on'], $p['days'], $p['days_before_lost']) ){
				return false;
			}

			if( !is_numeric($p['pts']) || $p['pts']<=0 ){
				return false;
			}

			if( !is_numeric($p['discount']) || $p['discount']<=0 ){
				return false;
			}

			if( !in_array($p['discount_type'], array(0, 1)) ){
				return false;
			}

			if( !in_array($p['apply_on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ){
				return false;
			}

			if( !is_numeric($p['days']) || $p['days']<=0 ){
				return false;
			}

			if( !is_numeric($p['days_before_lost']) || $p['days_before_lost']<0 ){
				return false;
			}
			break;
		}
		case _RWD_SYSTEM_PRODUCTS: {
			if( !is_array($ar_product) || !sizeof($ar_product) ){
				return false;
			}

			$p = $ar_product;

			if( !is_numeric($p['days_before_lost']) || $p['days_before_lost']<0 ){
				return false;
			}
			break;
		}
		case _RWD_SYSTEM_REMISE: {
			if( !is_array($ar_code) || !sizeof($ar_code) ){
				return false;
			}

			$p = $ar_code;
			if( !isset($p['pts'], $p['discount'], $p['discount_type']) ){
				return false;
			}

			if( !is_numeric($p['pts']) || $p['pts']<=0 ){
				return false;
			}

			if( !is_numeric($p['discount']) || $p['discount']<=0 ){
				return false;
			}

			if( !in_array($p['discount_type'], array(0, 1)) ){
				return false;
			}
			break;
		}
		default : {
			return false;
		}
	}

	global $config;

	$ar_cols = array();
	$ar_vals = array();

	$ar_cols[] = 'rwd_tnt_id';
	$ar_vals[] = $config['tnt_id'];

	$ar_cols[] = 'rwd_wst_id';
	$ar_vals[] = $wst>0 ? $wst : $config['wst_id'];

	$ar_cols[] = 'rwd_type_id';
	$ar_vals[] = $type;

	$ar_cols[] = 'rwd_type_ratio';
	$ar_vals[] = $type_ratio;

	$ar_cols[] = 'rwd_prf_id';
	$ar_vals[] = $prf;

	$ar_cols[] = 'rwd_ratio';
	$ar_vals[] = '\''.$ratio['amount'].'/'.$ratio['pts'].'\'';

	$ar_cols[] = 'rwd_type_amount';
	$ar_vals[] = '\''.$type_amount.'\'';

	$ar_cols[] = 'rwd_cumul_pts';
	$ar_vals[] = $cumul_pts ? '1' : '0';

	$ar_cols[] = 'rwd_status';
	$ar_vals[] = '\''.$type_calc.'\'';

	$ar_cols[] = 'rwd_days_valid';
	$ar_vals[] = $days;

	switch ($type) {
		case _RWD_SYSTEM_POINTS: {
			$ar_cols[] = 'rwd_convert';
			$ar_vals[] = '\''.$p['pts'].'/'.$p['amount'].'\'';

			$ar_cols[] = 'rwd_min_amount';
			$ar_vals[] = $p['min_amount'];

			$ar_cols[] = 'rwd_days_before_lost';
			$ar_vals[] = $p['days_before_lost'];
			break;
		}
		case _RWD_SYSTEM_CODE: {
			$ar_cols[] = 'rwd_nb_pts';
			$ar_vals[] = $p['pts'];

			$ar_cols[] = 'rwd_discount';
			$ar_vals[] = $p['discount'];

			$ar_cols[] = 'rwd_discount_type';
			$ar_vals[] = $p['discount_type'];

			$ar_cols[] = 'rwd_apply_on';
			$ar_vals[] = '\''.$p['apply_on'].'\'';

			$ar_cols[] = 'rwd_days_code';
			$ar_vals[] = $p['days'];

			$ar_cols[] = 'rwd_days_before_lost';
			$ar_vals[] = $p['days_before_lost'];
			break;
		}
		case _RWD_SYSTEM_PRODUCTS:{
			$ar_cols[] = 'rwd_days_before_lost';
			$ar_vals[] = $p['days_before_lost'];
			break;
		}
		case _RWD_SYSTEM_REMISE: {
			$ar_cols[] = 'rwd_nb_pts';
			$ar_vals[] = $p['pts'];

			$ar_cols[] = 'rwd_discount';
			$ar_vals[] = $p['discount'];

			$ar_cols[] = 'rwd_discount_type';
			$ar_vals[] = $p['discount_type'];
			break;
		}
	}

	// Désactivation de l'ancienne configuration
	$rwd = rwd_rewards_get( 0, $prf, $wst );
	if( $rwd && ria_mysql_num_rows($rwd) ){
		$old = ria_mysql_result( $rwd, 0, 'id' );
		$old_system = ria_mysql_result( $rwd, 0, 'system' );
		if( !rwd_rewards_del($old) )
			return false;
	}

	$res = ria_mysql_query('
		insert into rwd_rewards
			( '.implode( ', ', $ar_cols ).' )
		values
			( '.implode( ', ', $ar_vals ).' )
	');

	if( !$res ){
		return false;
	}

	// Copie des règles d'inclusion / d'exclusion de produit
	$new = ria_mysql_insert_id();
	if( $new ){
		if( isset($old) && $old ){
			if( !rwd_rewards_products_copy($old, $new) ){
				return false;
			}
		}

		// Copie de la configuration sur le système de parrainage
		if( isset($old) && $old ){
			if( !rwd_rewards_copy_sponsors($old, $new) ){
				return false;
			}
		}
	}

	// Si le système devient par code promotion, on essaye de créer ceux qui peuvent l'être
	if( $type==_RWD_SYSTEM_CODE && isset($old_system) && $old_system!=_RWD_SYSTEM_CODE ){
		rwd_rewards_create_promotion( 0, $wst );
	}

	return $new;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si une configuration de fidélité/parrainage existe déjà pour un profil utilisateur donné.
 *	@param int $id Facultatif, identifiant d'une configuration de points fidélité
 *	@param int $prf Facultatif, identifiant d'un profil utilisateur
 *	@param int $wst Facultatif, identifiant d'un site, par défaut on récupère celui contenu dans $config['wst_id']
 *	@return bool True si une configuration existe, false dans le cas contraire
 */
function rwd_rewards_exists( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}else{
		$sql .= '
			and rwd_prf_id='.$prf.'
			and rwd_wst_id='.( $wst>0 ? $wst : $config['wst_id'] ).'
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour d'une configuration du système.
 *	@param int $prf Obligatoire, identifiant d'un profil client pour lequel la configuration est mise en place
 *	@param $ratio Obligatoire, ratio mis en place sous cette forme array('amount'=>200, 'pts'=>1250)
 *	@param $days Obligatoire, nombre de jours où les points sont valides
 *	@param $type_amount Obligatoire, type de montant utilisé comme configuration ('ht' ou 'ttc')
 *
 *	@param $cumul_pts Optionnel, par défaut les points de fidélité sont cumulés, mettre false pour ne plus les cumulés
 *	@param $type_calc Optionnel, par défaut 'order' les points sont calculer lors de la commande, mettre 'invoice' pour les calculer lors de la facturation
 *	@param $type Optionnel, par défaut le fonctionnement fonctionne par conversion de points en euro, mettre 2 pour un fonctionnement par code promotion
 *	@param int $wst Optionnel, identifiant d'un site, par défaut on récupère celui contenue dans $config['wst_id']
 *
 *	@param $ar_convert Obligatoire si $type=1, paramètre du fonctionnement par conversion, tableau contenant :
 *						- pts : points utilisés lors de la conversion
 *						- amount : valeur en Euro des points utilisés lors de la conversion
 *						- min_amount : minimum de commande permettant d'utiliser des points de fidélités, 0 si aucun minimum
 *						- days_before_lost : nombre de jours avant la péremption des points fidélité auxquels l'alerte aux clients est envoyée, 0 pour aucune alerte
 *
 *	@param $ar_code Obligatoire si $type=2, paramètre du fonctionnement par promotion, tableau contenant :
 *						- pts : nombre de points à atteindre pour obtenir un code promotion
 *						- discount : montant de la remise (% ou valeur)
 *						- discount_type : type de remise, mettre 0 pour une remise en valeur, 1 pour une remise en pourcentage
 *						- apply_on : applicable sur (order : toute la commande, min-prd : produit le - cher, max-prd : produit le + cher, min-line : ligne de commande la - chère, max-line : ligne de commande la + chère)
 *						- days : délai en jours de validité d'une promotion
 *						- days_before_lost : nombre de jours avant l'envoi d'une alerte aux clients pour la péremption d'un code promotion, 0 pour aucune alerte
 *
 *	@param $type_ratio Facultatif, par défaut le système fonctionne par ratio euros / points type 2 arrondit par palier
 *
 *	@return bool false en cas d'échec de l'ajout, sinon l'identifiant de la nouvelle configuration
 */
function rwd_rewards_update( $prf, $ratio, $days, $type_amount, $cumul_pts=true, $type_calc='order', $type=1, $wst=0, $ar_convert=false, $ar_code=false, $type_ratio=1 ){

	return rwd_rewards_add( $prf, $ratio, $days, $type_amount, $cumul_pts, $type_calc, $type, $wst, $ar_convert, $ar_code, $type_ratio );

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le système de parrainage
 *	@param $rwd Obligatoire, identifiant de la configuration
 *	@param $active Optionnel, par défaut le système est activé, mettre false pour le désactiver
 *	@param $days Optionnel, nombre de jours de validité d'un code de parrainage (mettre 0 pour qu'il ne soit pas limité dans le temps)
 *	@param $discount Optionnel, pourcentage de la réduction pour le filleul
 *	@param $pts Optionnel, nombre de points gagné par le parrain lors de l'utilisation de l'un de ces codes promotions. Obligatoire si $active est à true.
 *	@param $limit Optionnel, nombre maximum de filleul (mettre 0 pour aucune limite)
 *	@param $days_before_lost Optionnel, nombre de jours avant d'envoyer la relance de perte du code de parrainage
 *	@param $first_order Optionnel, limite le système de parrainage à la première commande (par défaut à false)
 *	@param $sp_system Optionnel, système de rétribution pour le parrain (1 = par points | 2 = par pourcentage de la commande du filleul)
 *	@param $sp_pourcent Optionnel, pourcentage du montant de la commande filleul utilisé pour la génération du code promotion
 *	@param $sp_days Optionnel, délai de validiter du code promotion (en jours, mettre 0 pour aucune limite)
 *	@param $points Optionnel, Points gagné pour le filleul
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rwd_rewards_update_sponsors( $rwd, $active=true, $days=0, $discount=0, $pts=0, $limit=0, $days_before_lost=0, $first_order=false, $sp_system=_RWD_SP_SYS_POINTS, $sp_pourcent=0, $sp_days=0, $points=0 ){
	if( !rwd_rewards_exists($rwd) ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	if( !is_numeric($days) || $days<0 ){
		return false;
	}

	if( !is_numeric($limit) || $limit<0 ){
		return false;
	}

	if( !in_array($sp_system, array(_RWD_SP_SYS_POINTS, _RWD_SP_SYS_POURCENT)) ){
		return false;
	}

	if( !is_numeric($sp_pourcent) || $sp_pourcent < 0 ){
		return false;
	}
	if( !is_numeric($sp_days) || $sp_days < 0 ){
		return false;
	}

	if( !is_numeric($points) || $points < 0 ){
		return false;
	}

	if( $sp_system == _RWD_SP_SYS_POINTS ){
		if( $active && $pts==0 ){
			return false;
		}
	}else{
		if( $active && $sp_pourcent == 0 ){
			return false;
		}
	}

	global $config;

	$sql = '
		update rwd_rewards
		set rwd_cod_active='.( $active ? 1 : 0 ).',
			rwd_cod_days='.$days.',
			rwd_cod_discount='.$discount.',
			rwd_cod_points='.$pts.',
			rwd_cod_limit='.$limit.',
			rwd_cod_days_before_lost='.( is_numeric($days_before_lost) && $days_before_lost ? $days_before_lost : 'null' ).',
			rwd_sp_first_order = '.( $first_order ? '1' : '0' ).',
			rwd_sp_system = '.$sp_system.',
			rwd_sp_ord_pourcent = '.( $sp_pourcent > 0 ? $sp_pourcent : 'null' ).',
			rwd_sp_cod_days = '.( $sp_days >= 0 ? $sp_days : 'null' ).',
			rwd_points = '.( $points > 0 ? $points : 'null' ).'
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_id='.$rwd.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de copier le système de parrainage d'une configuration à une autre
 *	@param $old Obligatoire, identifiant de l'ancienne configuration
 *	@param $new Obligatoire, identifiant de la nouvelle configuration
 *	@return bool True si la copie s'est correctement déroulé, False dans le cas contraire
 */
function rwd_rewards_copy_sponsors( $old, $new ){
	global $config;

	if( !is_numeric($old) || $old<=0 ){
		return false;
	}

	if( !is_numeric($new) || $new<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select rwd_cod_active, rwd_cod_days, rwd_cod_discount, rwd_cod_points, rwd_cod_limit
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].' and rwd_id='.$old.'
	');

	if( !$res ){
		return false;
	}

	if( !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	$r['rwd_cod_days'] = is_numeric($r['rwd_cod_days']) && $r['rwd_cod_days']>0 ? $r['rwd_cod_days'] : 0;
	$r['rwd_cod_discount'] = is_numeric($r['rwd_cod_discount']) && $r['rwd_cod_discount']>0 ? $r['rwd_cod_discount'] : 0;
	$r['rwd_cod_points'] = is_numeric($r['rwd_cod_points']) && $r['rwd_cod_points']>0 ? $r['rwd_cod_points'] : 0;
	$r['rwd_cod_limit'] = is_numeric($r['rwd_cod_limit']) && $r['rwd_cod_limit']>0 ? $r['rwd_cod_limit'] : 0;

	return rwd_rewards_update_sponsors( $new, $r['rwd_cod_active'], $r['rwd_cod_days'], $r['rwd_cod_discount'], $r['rwd_cod_points'], $r['rwd_cod_limit'] );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information pour savoir si par défaut tout le catalogue est inclu ou exclu d'une configuration.
 *	@param int $id Obligatoire, identifiant d'une configuration
 *	@param $all_catalog Optionnel, par défaut on inclut le catalogue mettre False pour l'exclure
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rwd_rewards_set_all_catalog( $id, $all_catalog=true ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update rwd_rewards
		set rwd_all_catalog = '.( $all_catalog ? 1 : 0 ).'
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information pour savoir si les promotions sont inclues ou exclues d'une configuration.
 *	@param int $id Obligatoire, identifiant d'une configuration
 *	@param bool $exclu_promo Facultatif, par défaut on exclu les promotions mettre False pour les inclures
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rwd_rewards_set_exclu_promo( $id, $exclu_promo=true ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update rwd_rewards
		set rwd_exclu_promo = '.( $exclu_promo ? 1 : 0 ).'
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une configuration du système.
 *	Soit l'identifiant est passé en paramètre soit l'identifiant du profil et du site sont donnés sinon la fonction retournera false.
 *	@param int $id Optionnel, identifiant d'une condition
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site web
 *	@return bool false en cas d'échec de suppression, True dans le cas contraire
 */
function rwd_rewards_del( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		update rwd_rewards
		set rwd_date_deleted = now()
		where rwd_tnt_id='.$config['tnt_id'].'
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}else{
		$sql .= '
			and rwd_prf_id='.$prf.'
			and rwd_wst_id='.$wst.'
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet la récupération d'une ou plusieurs configuration (en fonction des paramètres fournis et du contenu de la base de données)
 *	@param int $id Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil client
 *	@param int $wst Optionnel, identifiant d'un site (par défaut on prend celui contenu dans $config['wst_id']
 *	@return bool false si l'un des paramètres est faux, sinon un résultat MySQL comprenant les colonnes suivantes :
 *				- id : identifiant de la configuration
 *				- ratio : ratio utilisé
 *				- convert : ratio de conversion utilisé
 *				- min-amount : montant minimum d'une commande pour utiliser ses points de fidélité pour payer
 *				- type-calc : sur quoi est calculé les points lors de la commande ou lors de la facturation d'un produit
 *				- days : nombre de jours où les points gagnés sont valables. La valeur 0 signifie "Aucune limite de validité"
 *				- exclu-promo : booléen indiquant si les produits en promotion sont exclus du calcul de points lors d'une commande
 *				- all-catalog : booléen indiquant si tous les produits du catalogue sont inclus dans le calcul de points lors d'une commande
 *				- type : type de montant utilisé pour le calcul des points de fidélité à savoir ht ou ttc
 *				- cumul-pts : si oui ou non les points de fidélité peuvent être cumulé
 *				- days_lost : jours avant une relance par mail (perte de points ou code promotion arrivant à échéance). La valeur 0 signifie "Pas de relance mail"
 *				- nb_pts : nombre de points à atteindre pour recevoir un code promotion
 *				- days_code : nombre de jours où le code promotion est valide
 *				- discount : réduction du code promotion
 *				- discount_type : type de réduction (0 = remise en valeur, 1 = remise en %)
 *				- apply_on : application du code promotion
 *				- system : type de système de conversion (1 = Points/Euro, 2 = Code Promotion)
 */
function rwd_rewards_get( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_id as id, rwd_prf_id as prf, rwd_wst_id as wst, rwd_ratio as ratio, rwd_convert as "convert", rwd_min_amount as "min-amount", rwd_status as "type-calc",
		rwd_days_valid as days, rwd_exclu_promo as "exclu-promo", rwd_all_catalog as "all-catalog", rwd_type_amount as type, rwd_cumul_pts as "cumul-pts", rwd_days_before_lost as "days_lost",
		rwd_nb_pts as nb_pts, rwd_days_code as days_code, rwd_discount as discount, rwd_discount_type as discount_type, rwd_apply_on as apply_on, rwd_type_id as system, rwd_type_ratio as system_ratio
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne la configuration du parrainage par code de parrainage.
 *	@param $rwd Optionnel, identifiant de configuration
 *	@param int $wst Optionnel, identifiant d'un site
 *	@param int $prf Optionnel, identifiant d'un profile client
 *	@return bool False si $rwd n'est pas données et si $wst ou $prf n'est pas renseigné
 *	@return array Un tableau contenant :
 *				- active : si oui ou non le système de parrainage par code de parrainage est active
 *				- discount: pourcentage de la réduction
 *				- days : nombre de jours de validité du code de parrainage
 *				- pts : nombre de points gagné par le compte parrain
 *				- limit : nombre limite de code de parrainage
 *				- cod_before_lost : nombre de jours avant de prévenir l'internaute de la fin de validité du code promotion
 *				- first_order : si le système de parrainage s'appuit seulement sur la première commande
 *				- sp_system : système de récompense pour le parrain (1 = points de fidélité, 2 = pourcentage de commande filleul)
 *				- sp_ord_pourcent : pourcentage de la commande filleul donné en remise
 *				- sp_cod_days : limite d'utilisation
 */
function rwd_rewards_get_sponsor( $rwd=0, $wst=0, $prf=0 ){
	if( !is_numeric($rwd) || $rwd<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( $rwd==0 && ($prf==0 || $wst==0) ){
		return false;
	}

	global $config;

	$sql = '
		select
			rwd_cod_active as active, rwd_cod_discount as discount, rwd_cod_days as days, rwd_cod_points as pts, rwd_cod_limit as "limit", rwd_cod_days_before_lost as cod_before_lost,
			rwd_sp_first_order as first_order, rwd_sp_system as sp_system, rwd_sp_ord_pourcent as sp_ord_pourcent, rwd_sp_cod_days as sp_cod_days, rwd_points as points
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $rwd>0 ){
		$sql .= ' and rwd_id='.$rwd;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_array( $res );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le ratio actuellement utilisé pour convertir les euros d'achat en points ou le ratio de conversion des points en euros.
 *	@param int $prf Obligatoire, identifiant d'un profil client
 *	@param $is_convert Optionnel, par défaut on récupère le ratio euros/points mettre true pour récupérer le ratio de conversion points/euros
 *	@return bool False si le système a été incapable de récupérer le ratio
 *	@return array Un tableau contenant : array( 'amount'=>xx, 'pts'=>xx )
 */
function rwd_rewards_get_ratio( $prf, $is_convert=false ){
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_ratio as ratio, rwd_convert as "convert"
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].' and rwd_wst_id='.$config['wst_id'].'
			and rwd_prf_id='.$prf.'
			and rwd_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	$ratio = !$is_convert ? $r['ratio'] : $r['convert'];

	return rwd_rewards_ratio_formated( $ratio, $is_convert );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si l'attribution des points à lieu au moment de la commande ou au moment de la facturation.
 *	@param int $prf Obligatoire, profile du client
 *	@return invoice s'il est fait sur la facturation, sinon 'order' s'il est fait lors de la commande
 */
function rwd_rewards_get_type_calc( $prf ){
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_status as "type-calc"
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].' and rwd_wst_id='.$config['wst_id'].'
			and rwd_prf_id='.$prf.'
			and rwd_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type-calc' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nombre de jours où les points sont encores valables.
 *	La valeur 0 signifie "Aucune limite de validité".
 *	@param int $prf Obligatoire, identifiant d'un profil client
 *	@return bool False si le système a été incapable de récupérer la limite
 *	@return Le nombre de jours
 */
function rwd_rewards_get_days_limit( $prf ){
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_days_valid as days
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].' and rwd_wst_id='.$config['wst_id'].'
			and rwd_prf_id='.$prf.'
			and rwd_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'days' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si oui ou non les points de fidélité sont cumulés.
 *	@param int $prf Obligatoire, identifiant d'un profil client
 *	@return bool False si le système a été incapable de récupérer l'information
 *	@return Si oui ou non les points de fidélité sont cumulés
 */
function rwd_rewards_get_cumul_pts( $prf ){
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_cumul_pts as cumul_pts
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].' and rwd_wst_id='.$config['wst_id'].'
			and rwd_prf_id='.$prf.'
			and rwd_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'cumul_pts' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si le calcul des points de fidélité se fait par ratio ou arrondi au palier.
 *	@param int $id Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@return type_ratio tel que définit dans la configuration
 */
function rwd_rewards_get_type_ratio( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_type_ratio as type_ratio
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type_ratio' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si le calcul des points de fidélité se fait selon le montant HT ou TTC de la commande.
 *	@param int $id Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@return Le type de montant
 */
function rwd_rewards_get_type_amount( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_type_amount as type
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si les produits en promotion sont exclus du calcul des points de fidélité.
 *	@param int $id Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@return bool True si les promotions sont exclues, False dans le cas contraire
 */
function rwd_rewards_get_promo_excluded( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_exclu_promo as exclu_promo
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'exclu_promo' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si oui ou non tout le catalogue est inclus.
 *	@param int $id Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@return bool True si tout le catalogue est inclu, false si tout le catalogue est exclu
 */
function rwd_rewards_get_all_catalog( $id=0, $prf=0, $wst=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( $id<=0 && !($prf && $wst) ){
		return false;
	}

	global $config;

	$sql = '
		select rwd_all_catalog as "all-catalog"
		from rwd_rewards
		where rwd_tnt_id='.$config['tnt_id'].'
			and rwd_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and rwd_id='.$id;
	}

	if( $prf>0 ){
		$sql .= ' and rwd_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and rwd_wst_id='.$wst;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'all-catalog' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner un ratio ou ratio de conversion sous le format d'un tableau.
 *	@param $ratio Obligatoire, ratio ou ratio de conversion sous le format respectif €/pts et pts/€
 *	@param $is_convert Optionnel, par défaut à false, mettre True pour informer le système qu'il s'agit du ratio de conversion
 */
function rwd_rewards_ratio_formated( $ratio, $is_convert=false ){
	if( !trim($ratio) ){
		return false;
	}

	$tmp = preg_split( '/\//', $ratio );
	if( !is_array($tmp) || sizeof($tmp)!=2 ){
		return false;
	}

	$res = array();
	if( !$is_convert ){
		$res = array(
			'amount' => $tmp[0],
			'pts' => $tmp[1]
		);
	}else{
		$res = array(
			'amount' => $tmp[1],
			'pts' => $tmp[0]
		);
	}

	return $res;
}
// \endcond

/** Cette fonction permet de calculer le nombre de points qu'une commande / facture recevra.
 *
 * ATTENTION : Cette fonction contient certains "hacks" pour Pierre Oteiza (Tenant ID = 13).
 * @see https://riastudio.atlassian.net/browse/PIERREOTRG-325
 *
 * @param int $obj_id Obligatoire, identifiant d'une commande ou d'une facture
 * @param $user Obligatoire, résultat de ria_mysql_fetch_array(gu_users_get())
 * @param $type Optionnel, par défaut le nombre de points est calculé par rapport au montant ttc, mettre ht pour le calculé selon l'hors taxe
 * @param $type_ratio Optionnel, par défaut à true, definis si l'ont fonctionne par ratio arrondit au positif ou par palier en cas de false
 * @param int $cls_id Facultatif, identifiant de classe sur laquelle les points sont calculés. Soit CLS_ORDER, soit CLS_INVOICE
 * @param $date Optionnel, permet de forcer une date afin de récupérer des surcharges de points en application à cette date
 * @return bool False si une erreur se produit, sinon le nombre de points pour la commande
 */
function rwd_rewards_calculated_order_points( $obj_id, $user, $type='ttc', $type_ratio=1, $cls_id=CLS_ORDER, $date=false ){
	if( !is_numeric($obj_id) || $obj_id<=0 ){
		return false;
	}

	if( !is_array($user) || !isset($user['id'], $user['prf_id']) ){
		return false;
	}

	if( !in_array($type, array('ht', 'ttc')) ){
		return false;
	}

	if( !in_array($cls_id, array(CLS_ORDER, CLS_INVOICE)) ){
		return false;
	}

	global $config;

	$total_pts = 0;

	$is_user_enabled = rwd_users_is_enabled($user['id'], $date ? $date : '');

	// Pierre Oteiza peut dans certains cas (commande ADD) accepter des factures
	// venant de clients non inscrit au programme de fidélité.
	if( $config['tnt_id'] != 13 && !$is_user_enabled ){
		return 0;
	}

	if( $cls_id == CLS_ORDER ){
		$r_ord_prd = ord_products_get($obj_id);
		$products = ord_products_get_with_promotion($obj_id);
	}else{
		$r_ord_prd = ord_inv_products_get($obj_id);
		$products = array();
	}

	if( !$r_ord_prd || !ria_mysql_num_rows($r_ord_prd) ){
		return $total_pts;
	}

	{ // Récupère les articles exclus du programme de fidélité
		$ar_exclu = array();

		$rexcluded = rwd_rewards_products_get($user['prf_id'], true);
		if( $rexcluded && ria_mysql_num_rows($rexcluded) ){
			while( $e = ria_mysql_fetch_array($rexcluded) ){
				$ar_exclu[] = $e['id'];
			}
		}
	}

	// HACK Pierre Oteiza
	// Produits "Bon Cadeau" utilisé pour calculer les réductions.
	// Array<ID Produit, Réduction (en €)>
	$discounts = array(
		99306 => 10,
		99307 => 20,
		917660 => 20,
		99308 => 50,
	);

	$total_ht = $total_ttc = $pts_no_ratio = 0;
	while ($prd = ria_mysql_fetch_assoc($r_ord_prd)) {
		if( prd_products_is_port($prd['ref']) ){
			continue;
		}

		// HACK Pierre Oteiza
		// Si un produit a une quantité négative (Réduction, Bon Cadeau, etc...) on le compte quand même dans le calcul des points même si il est exclu.
		if( ($config['tnt_id'] != 13 && in_array($prd['id'], $ar_exclu)) || (in_array($prd['id'], $ar_exclu) && $prd['qte'] >= 0) ){
			continue;
		}

		$price_ht  = isset($prd['weight_ht']) ? $prd['weight_ht'] : $prd['price_ht'];
		$price_ttc = isset($prd['weight_ttc']) ? $prd['weight_ttc'] : $prd['price_ttc'];

		$key = $prd['id'] . '-' . $prd['line'];
		if( is_array($products) && array_key_exists($key, $products) ){
			$price_ht = isset($products[$key]['weight_ht']) ? $products[$key]['weight_ht'] : $products[$key]['price_ht'];
			$price_ttc = isset($products[$key]['weight_ttc']) ? $products[$key]['weight_ttc'] : $products[$key]['price_ttc'];
		}

		// HACK Pierre Oteiza - Gestion du poids des articles
		$origin_qty = $prd['qte'];
		$origin_price_ht = $price_ht;
		$origin_price_ttc = $price_ttc;

		// HACK Pierre Oteiza
		if( $config['tnt_id'] == 13 ){
			// Si l'utilisateur n'est pas inscrit au programme de fidélité et si la ligne de facture n'est pas associé à un numéro de carte alors,
			// nous ne la prenons pas en compte dans le calcul des points.
			if( !$is_user_enabled && !fld_object_values_get(array($prd['inv_id'], $prd['id'], $prd['line']), 101169) ){
				continue;
			}

			$tmp_origin_qty = fld_object_values_get(array(($cls_id == CLS_ORDER ? $prd['ord_id'] : $prd['inv_id']), $prd['id'], $prd['line']), 100097);

			if( is_numeric($tmp_origin_qty) && $tmp_origin_qty != 0 ){
				$origin_qty = $tmp_origin_qty;
				$origin_price_ht = $price_ht * $prd['qte'] / $origin_qty;
				$origin_price_ttc = $price_ttc * $prd['qte'] / $origin_qty;
			}

			// Si le produit est de type "Bon Cadeau", nous devons soustraire la réduction
			// au prix total de la facture.
			if( array_key_exists($prd['id'], $discounts) ){
				$origin_price_ht -= $discounts[$prd['id']];
			}
		}

		$tmp_pts_no_ratio = prd_products_get_reward_points($prd['id'], $user['id'], $user['prf_id'], array('price_ht' => $origin_price_ht, 'price_ttc' => $origin_price_ttc, 'qty' => $origin_qty), false, $date);
		if( is_numeric($tmp_pts_no_ratio) && $tmp_pts_no_ratio > 0 ){
			$pts_no_ratio = $pts_no_ratio + $tmp_pts_no_ratio;
			continue;
		}

		$total_ht += ($price_ht * $prd['qte']);
		$total_ttc += ($price_ttc * $prd['qte']);
	}

	// HACK Pierre Oteiza
	// Pierre Oteiza accepte les factures négatives car ce sont en fait des avoirs.
	if( $total_ht > 0 || ($config['tnt_id'] == 13 && $total_ht != 0) ){
		$r_rwd_config = rwd_rewards_get(0, $user['prf_id'], $config['wst_id']);

		if( $r_rwd_config && ria_mysql_num_rows($r_rwd_config) ){
			$rwd_config = ria_mysql_fetch_assoc($r_rwd_config);

			$rwd_config['ratio'] = rwd_rewards_ratio_formated($rwd_config['ratio']);

			if( is_array($rwd_config['ratio']) && count($rwd_config['ratio']) ){
				$price = $rwd_config['type'] == 'ht' ? $total_ht : $total_ttc;

				$r = $rwd_config['ratio']['pts'] / $rwd_config['ratio']['amount'];
				$total_pts = round($price, 2) * $r;
				if( $rwd_config['system_ratio'] == 2 ){
					$total_pts = $total_pts >= 0 ? floor($total_pts) : ceil($total_pts);
				}
			}
		}
	}

	return $total_pts + $pts_no_ratio;
}

/** Cette fonction permet de simuler l'utilisation d'un certians nombre de points pour un utilisateur donnés.
 *
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param $points Obligatoire, nombre de point à utiliser
 *	@param $rwa_id Optionnel, permet d'inclure/exclure une action du solde de points
 *	@param $action Optionnel, permet d'inclure/exclure une action du solde de points (selon le nom donné)
 *	@param $is_exclude Optionnel, si l'action est inclue (false, valeur par défaut) ou exclue (true) du solde
 *
 *	@return bool False si l'un des paramètres est omis ou faux, sinon un tableau contenant :
 *				- points : nombre de points
 *				- reduc : réduction accordés par ces points (on tient contenu du ratio de convertion de chaque point)
 */
function rwd_rewards_using_points_simulated( $usr, $points, $rwa_id=null, $action='', $is_exclude=false ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($points) || $points<=0 ){
		return false;
	}

	if( is_array($action) ){
		foreach( $action as $k=>$a ){
			if( trim($a)=='' ){
				return false;
			}

			$action[$k] = strtolower2( $a );
		}
	}else{
		if( trim($action)=='' ){
			$action = array();
		}else{
			$action = array( strtolower2($action) );
		}
	}

	global $config;

	$sql_action = '';
	if( is_numeric($rwa_id) && $rwa_id ){
		$sql_action .= ' and stats_rwa_id'.( $is_exclude ? '!=' : '=' ).$rwa_id;
	}elseif( sizeof($action) ){
		$sql_action .= ' and ('.( $is_exclude ? 'ifnull(stats_action, "")="" or' : '' ).' lower(stats_action)'.( $is_exclude ? 'not in' : 'in' ).' ("'.implode( '", "', $action ).'"))';
	}

	$sql = '
		select sum(stats_points) as points_neg
		from stats_rewards
		where stats_tnt_id='.$config['tnt_id'].'
			and stats_wst_id='.$config['wst_id'].'
			and stats_usr_id='.$usr.'
			and stats_points<0
			and (stats_date_limited is null or date(stats_date_limited) >= date(now()))
			and stats_date_deleted is null
			'.$sql_action.'
			limit 1
	';

	$res = ria_mysql_query( $sql );

	$resneg = 0;
	if( $res && ria_mysql_num_rows($res) ){
		$r = ria_mysql_fetch_assoc( $res );
		$resneg = $r['points_neg'];
	}

	if( $resneg<0 ){
		$sql = '
			select stats_points as points, stats_convert as "convert"
			from stats_rewards
			where stats_tnt_id='.$config['tnt_id'].'
				and stats_date_deleted is null
				and stats_wst_id='.$config['wst_id'].'
				and stats_usr_id='.$usr.'
				'.$sql_action.'
		';
	} else {
		$sql = '
			select stats_points as points, stats_convert as "convert"
			from stats_rewards
			where stats_tnt_id='.$config['tnt_id'].'
				and stats_date_deleted is null
				and stats_wst_id='.$config['wst_id'].'
				and stats_usr_id='.$usr.'
				and (stats_date_limited is null or date(stats_date_limited) >= date(now()))
				'.$sql_action.'
		';
	}

	$res = ria_mysql_query( $sql );

	$pts = 0; $reduc = 0;
	if( $res ){
		while( $r = ria_mysql_fetch_array($res) ){
			$respts = ( $r['points']>0 ? $r['points'] : 0 );
			$resneg = $resneg + $respts;

			if( $resneg>0 && $points>0 ){
				$convert = preg_split('/\//', $r['convert']);

				if( isset($convert[0], $convert[1]) ){
					$convert = $convert[0] / $convert[1];

					$temp_pts = ( $respts>$points ? $points : $respts );

					$pts 	+= $temp_pts;
					$points -= $respts;
					$reduc 	+= $temp_pts * $convert;
				}
			}
		}
	}

	return array( 'points'=>$pts, 'reduc'=>$reduc );
}

// \cond onlyria
/** Cette fonction permet de créer un code promotion lorsque le système de conversion est réglé sur "Code promotion" et que le nombre de points est atteints pour un client.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param int $wst Optionnel, identifiant d'un website
 *	@return bool False si la création a échoué
 *	@return bool True dans le cas contraire ou si le système n'est pas réglé sur "Code promotion" ou que le nombre de points ne permet pas de créer un code promotion
 */
function rwd_rewards_create_promotion( $usr=0, $wst=0 ){
	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	$rusr = gu_users_get( $usr );
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		return false;
	}

	if( $usr==0 ){
		while( $usr = ria_mysql_fetch_array($rusr) ){
			if( !rwd_rewards_create_promotion($usr['id'], $wst) ){
				return false;
			}
		}
	}

	global $config;
	$usr = ria_mysql_fetch_array( $rusr );

	$wst = $wst>0 && wst_websites_exists($wst) ? $wst : $config['wst_id'];

	// Récupère la configuration des points de fidélité selon le profile du compte client
	$rrwd = rwd_rewards_get( 0, $usr['prf_id'], $wst );
	if( !$rrwd || !ria_mysql_num_rows($rrwd) ){
		return true;
	}

	$rwd = ria_mysql_fetch_array( $rrwd );

	// Contrôle que le système est réglé en "Code promotion"
	if( $rwd['system']!=_RWD_SYSTEM_CODE ){
		return true;
	}

	// Récupère le nombre de points du compte client
	$points = gu_users_get_rewards_balance( $usr['id'] );

	// Contrôle que le nombre de points du compte est supérieur au minimum requis
	if( $points < $rwd['nb_pts'] ){
		return true;
	}

	// Nombre de code promotion à créer
	$nb_codes = floor( $points / $rwd['nb_pts'] );

	// Charge la configuration spécique au site
	$current_config = cfg_overrides_substitute( $wst, array(), array('site_url', 'site_name', 'email_html_header', 'email_html_footer') );

	// Récupère la configuration d'adresse mail pour l'envoi
	$rcfg = cfg_emails_get( 'alert-rewards', $wst );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array( $rcfg );

	$error = false;
	for( $i=0 ; $i<$nb_codes ; $i++ ){
		// Génère un code promotion
		$code = pmt_codes_generated();
		if( trim($code)=='' ){
			$error = true;
			continue;
		}

		// Créer la promotion
		$promo = $rwd['exclu-promo'] ? false : true;
		$date_start = date('d/m/Y H:i');
		$date_stop = null;
		if( is_numeric($rwd['days_code']) && $rwd['days_code']>0 ){
			$date_stop = date('d/m/Y 23:59', strtotime('+'.$rwd['days_code'].' days'));
		}

		$cod = pmt_codes_add( '', _PMT_TYPE_CODE, $code,'Code de fidélité généré pour '.$usr['email'].'.' );
		if( !$cod ){
			return false;
		}

		$off = pmt_offers_add( $cod, _PMT_TYPE_CODE, $rwd['discount'], $rwd['discount_type'], '', 0, 0, 0, $rwd['apply_on'], $date_start, $date_stop, 1, false, false, $promo );
		if( !$off ){
			pmt_codes_del( $cod );
			return false;
		}

		// Rattache la promotion au compte client
		if( !pmt_users_add($cod, $usr['id'], true) ){
			pmt_codes_del( $cod );
			return false;
		}

		// Retire les points de fidélité utilisé
		$cancel = stats_rewards_add( $usr['id'], $usr['prf_id'], 0, true, 'Conversion des points en un code promotion', CLS_PMT_CODE, $cod, $rwd['nb_pts'] );
		if( !$cancel ){
			pmt_codes_del( $cod );
			return false;
		}

		// Envoi un email avec le code promotion
		$email = new Email();
		$email->setSubject( 'Votre bon d\'achat '.$current_config['site_name'] );
		$email->setFrom( $cfg['from'] );
		$email->addTo( $usr['email'] );

		if( $cfg['bcc'] ){
			$email->addBcc( $cfg['bcc'] );
		}

		if( $cfg['reply-to'] ){
			$email->setReplyTo( $cfg['reply-to'] );
		}

		if( isset($current_config['email_html_header']) && trim($current_config['email_html_header'])!='' ){
			$email->addHtml( $current_config['email_html_header'] );
		}

		$user_name = '';
		switch( $usr['title_name'] ){
			case 'Monsieur' :
				$user_name = 'Cher Monsieur ';
				break;
			case 'Madame' :
				$user_name = 'Chère Madame ';
				break;
			case 'Mademoiselle' :
				$user_name = 'Chère Mademoiselle ';
				break;
			default :
				$user_name = '';
				break;
		}

		if( trim($user_name)!='' ){
			$email->addParagraph( $user_name.' '.trim( $usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society'] ).', ' );
		} else {
			$email->addParagraph( trim( $usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society'] ).', ' );
		}

		$message = 'Pour vous remercier de votre fidélité, nous sommes heureux de vous adresser un bon d’achat ';

		$discount = str_replace( ',00', '', number_format($rwd['discount'], '2', ',', ' ') );
		if( $rwd['discount_type']==0 ){
			$message .= 'd\'une valeur de '.( $discount * _TVA_RATE_DEFAULT ).' € ';
		} else {
			$message .= 'd\'une remise de '.$discount.' % ';
		}

		$message .= 'sur votre prochaine commande';

		if( is_numeric($rwd['days_code']) && $rwd['days_code']>0 ){
			$date_stop = date('d/m/Y', strtotime('+'.$rwd['days_code'].' days'));
		}

		if( $date_stop!=false ){
			$message .= ', ce bon d\'achat est valable jusqu\'au '.$date_stop.' inclus.';
		}

		$email->addParagraph( $message );

		$email->addHtml( '<p>Pour profiter de ce bon d’achat, saisissez le code suivant lors de votre prochaine commande : <b>'.strtoupper( $code ).'</b>.</p>' );

		$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=loyalty-program-ba';
		$email->addhtml( 'Merci de votre confiance et à bientôt sur <a href="'.$current_config['site_url'].$analytics.'">'.$current_config['site_name'].'</a>.');

		if( isset($current_config['email_html_footer']) && trim($current_config['email_html_footer'])!='' ){
			$email->addHtml( $current_config['email_html_footer'] );
		}

		if( !$email->send() ){
			pmt_codes_del( $cod );
			return false;
		}
	}

	return true;
}
// \endcond

/// @}

/** \defgroup model_rewards_content Gestion des ratios
 *	Les fonctions contenus dans ce module servent à configurer/récupérer le ratio spécifique à un produit ou une catégorie.
 *	@{
 */

/** Cette fonction permet de récupérer le ratio en place sur un produit pour un profil défini.
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param int $prf Optionnel, identifiant d'un profil par défaut au prend celui du compte client si est connecté
 *	@param int $seg Optionnel, identifiant d'un segment
 *	@param $date Optionnel, permet de rechercher la surcharge de points sur le produit à une date donnée
 *	@return bool false si l'un des paramètres est faux ou bien si le profil n'est pas précisé et qu'aucun compte client est connecté
 *	@return Le ratio s'il est renseigné sous cette forme array( 'amount'=>100, 'pts'=>10 );
 */
function rwd_rewards_get_prd_points( $prd, $prf=0, $seg=0, $date=false ){

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<0 ){
		return false;
	}

	global $config;

	$prf = $prf>0 && gu_profiles_exists($prf) ? $prf : (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : 0);
	if( $prf<=0 ){
		return false;
	}

	if( !$seg ){
		$usr_id = 0;
		if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ){
			$usr_id = $_SESSION['usr_id'];
		}

		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']>0 ){
			$usr_id = $_SESSION['admin_view_user'];
		}

		if( $usr_id ){
			$segments = gu_users_get_segments( $usr_id );
		}

		if( isset($segments) && is_array($segments) && sizeof($segments) ){
			$seg = $segments[0];
		}
	}

	if( $date !== false && !isdate($date) ){
		return false;
	}

	$sql = '
		select ' . ($config['rewards_use_decimal_points'] ? 'rwp_points' : 'cast( rwp_points as int)') . ' as points
		from rwd_prd_rewards
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_date_deleted is null
			and rwp_prd_id='.$prd.'
			and rwp_prf_id='.$prf.'
	';

	if( $date === false ){
		$sql .= '
			and (rwp_date_start <= now() or rwp_date_start = "0000-00-00 00:00:00")
			and (rwp_date_end >= now() or rwp_date_end = "0000-00-00 00:00:00")
		';
	}else{
		$date = dateparse($date);

		$sql .= '
			and (rwp_date_start <= "'.addslashes($date).'" or rwp_date_start = "0000-00-00 00:00:00")
			and (rwp_date_end >= "'.addslashes($date).'" or rwp_date_end = "0000-00-00 00:00:00")
		';
	}

	$sql .= '
		order by points desc
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$points = ria_mysql_result( $res, 0, 'points' );

	/*if( $seg ){
		$sql = '
			select rwp_points as points
			from rwd_seg_prd_rewards
			where rwp_tnt_id='.$config['tnt_id'].'
				and rwp_prd_id='.$prd.'
				and rwp_seg_id = '.$seg.'
		';

		$res = ria_mysql_query( $sql );
		if( !$res ){
			return false;
		}

		if( ria_mysql_num_rows($res) ){
			$points = ria_mysql_result( $res, 0, 'points' );
		}
	}*/

	return $points;
}

// \cond onlyria
/** Cette fonction permet de récupérer les informations de fidélités d'un produit
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param $ids Obligatoire, identifiant de la ligne
 *	@return bool false si l'un des paramètres est faux ou bien si le profil n'est pas précisé et qu'aucun compte client est connecté
 *	@return Le ratio s'il est renseigné sous cette forme array( 'amount'=>100, 'pts'=>10 );
 */
function rwd_prd_rewards_get( $prd=null, $ids=null ){
	if($prd !==null ){
		$prd = control_array_integer( $prd, false );
		if ($prd === false) {
			return false;
		}
	}

	if($ids !==null ){
		$ids = control_array_integer( $ids, false );
		if ($ids === false) {
			return false;
		}
	}

	global $config;

	$sql = '
		select rwp_id as id, rwp_prd_id as prd_id, rwp_prf_id as prf_id, rwp_date_start as date_start, rwp_date_end as date_end,  ' . ($config['rewards_use_decimal_points'] ? 'rwp_points' : 'cast( rwp_points as int)') . ' as points, rwp_date_created as date_created, rwp_date_modified as date_modified, rwp_date_deleted as date_deleted
		from rwd_prd_rewards
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_date_deleted is null
	';

	if( $prd != null ){
		$sql .= ' and rwp_prd_id in ('.implode(', ', $prd).') ';
	}
	if( $ids != null ){
		$sql .= ' and rwp_id in ('.implode(', ', $ids).') ';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){

		return false;
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de point spécifique à un produit.
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param $pts Obligatoire, nombre de points gagnés lors de l'achat de ce produit
 *	@param string $date_start Optionnel, date de début du systeme de fidélité
 *	@param string $date_end Optionnel, date de fin du systeme de fidélité
 *	@return bool True si l'information a bien été mise à jour, false dans le cas contraire
 */
function rwd_prd_rewards_add( $prd, $prf, $pts, $date_start=null, $date_end=null){


	if( !prd_products_exists($prd) ){
		return false;
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( !is_numeric($pts) ){
		return false;
	}

	if($date_start && !isdateheure($date_start)){
		return false;
	}

	if($date_end && !isdateheure($date_end)){
		return false;
	}

	if($date_start!==null){
		$date_start = dateheureparse($date_start);
	}
	if($date_end!==null){
		$date_end = dateheureparse($date_end);
	}

	global $config;

	$sql = '
		insert into rwd_prd_rewards
			( rwp_tnt_id, rwp_prd_id, rwp_prf_id, rwp_points, rwp_date_start, rwp_date_end, rwp_date_created )
		values
			( '.$config['tnt_id'].', '.$prd.', '.$prf.', '.$pts.','.($date_start!==null ? '"'.$date_start.'"' : "null").','.($date_end!==null ? '"'.$date_end.'"' : "null").', now())
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un nombre de points de fidélité est défini sur un produit pour un profil donné.
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@return bool True s'il existe des points de fidélité, False dans le cas contraire
 */
function rwd_prd_rewards_exists( $prd, $prf ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_prd_rewards
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_prd_id='.$prd.'
			and rwp_prf_id='.$prf.'
			and rwp_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de points de fidélité pour un produit et un profil donné.
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param $pts Optionnel, nombre de points si c'est à zéro la ligne sera supprimée
 *	@return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function rwd_prd_rewards_update( $prd, $prf, $pts=0 ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	if( !is_numeric($pts) ){
		return false;
	}

	if( $pts==0 ){
		$sql = '
			update rwd_prd_rewards
			set rwp_date_deleted = now()
			where rwp_tnt_id = '.$config['tnt_id'].'
				and rwp_prd_id = '.$prd.'
				and rwp_prf_id = '.$prf.'
				and rwp_date_deleted is null
		';
	}else{
		$sql = '
			update rwd_prd_rewards
			set rwp_points='.$pts.'
			where rwp_tnt_id='.$config['tnt_id'].'
				and rwp_prd_id='.$prd.'
				and rwp_prf_id='.$prf.'
				and rwp_date_deleted is null
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une précision de points de fidélité sur un produit pour un profil donné.
 *	@param int $id Obligatoire, identifiant de l'offre de fidélité
 */
function rwd_prd_rewards_del( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update rwd_prd_rewards set rwp_date_deleted = now()
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_id='.$id;


	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer le ratio défini pour une catégorie, dans un contexte de connexion donné
 *	Si les paramètres optionnels ne sont pas spécifiés, le contexte en session sera utilisé
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param int $prf Optionnel, identifiant d'un profil utilisateur
 *	@param int $seg Optionnel, identifiant d'un segment utilisateur
 *	@return bool False en cas d'échec
 *	@return Le ratio de cette catégorie, dans le format défini par la fonction rwd_rewards_ratio_formated()
 */
function rwd_rewards_get_cat_ratio( $cat, $prf=0, $seg=0 ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<0 ){
		return false;
	}

	global $config;

	$prf = $prf>0 && gu_profiles_exists($prf) ? $prf : (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : 0);
	if( $prf<=0 ){
		return false;
	}

	if( !$seg ){
		$usr_id = 0;
		if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ){
			$usr_id = $_SESSION['usr_id'];
		}

		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']>0 ){
			$usr_id = $_SESSION['admin_view_user'];
		}

		if( $usr_id ){
			$segments = gu_users_get_segments( $usr_id );
			if( is_array($segments) && sizeof($segments) ){
				$seg = $segments[0];
			}
		}
	}

	$sql = '
		select rwc_ratio as ratio, if( rwc_cat_id='.$cat.', 9999, cat_parent_depth) as depth
		from rwd_cat_rewards
			left join prd_cat_hierarchy on (rwc_tnt_id=cat_tnt_id and cat_child_id='.$cat.')
		where rwc_tnt_id='.$config['tnt_id'].'
			and (rwc_cat_id='.$cat.' or rwc_cat_id=cat_parent_id)
			and rwc_prf_id='.$prf.'
			and rwc_date_start < NOW()
			and rwc_date_end > NOW()
		order by if( rwc_cat_id='.$cat.', 9999, cat_parent_depth) desc
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$lastRatio = 0;
	$result = array();

	while ($ratio = ria_mysql_fetch_assoc($res)){

		$ratioArr = rwd_rewards_ratio_formated($ratio["ratio"]);

		if(($ratioArr["pts"] / $ratioArr["amount"] ) > $lastRatio){
			$lastRatio = $ratioArr["pts"] / $ratioArr["amount"];
			$result["ratio"] = $ratio["ratio"] ;
			$result["depth"] = $ratio["depth"] ;
		}
	}

	$prf_ratio = $result["ratio"];
	$prf_depth = $result["depth"];

	$seg_ratio = $seg_depth = null;

	/*if( $seg ){
		$sql = '
			select rwc_ratio as ratio, if( rwc_cat_id='.$cat.', 9999, cat_parent_depth) as depth
			from rwd_seg_cat_rewards
				left join prd_cat_hierarchy on (rwc_tnt_id=cat_tnt_id and cat_child_id='.$cat.')
			where rwc_tnt_id='.$config['tnt_id'].'
				and (rwc_cat_id='.$cat.' or rwc_cat_id=cat_parent_id)
				and rwc_seg_id = '.$seg.'
				and rwc_date_start < NOW()
				and rwc_date_end > NOW()
			group by rwc_cat_id
			order by if( rwc_cat_id='.$cat.', 9999, cat_parent_depth) desc
		';

		$res = ria_mysql_query( $sql );
		if( !$res ){
			return false;
		}

		if( ria_mysql_num_rows($res) ){
			$seg_ratio = ria_mysql_result( $res, 0, 'ratio' );
			$seg_depth = ria_mysql_result( $res, 0, 'depth' );
		}
	}*/

	// Pas de segmentation
	$ratio = $prf_ratio;
	if( $seg_ratio!==null ){
		// Segmentation
		$ratio = $seg_ratio;
		if( $config['rwd_ratio_priority_depth'] && $prf_depth>$seg_depth ){
			// Priorité à la profondeur de la catégorie
			$ratio = $prf_ratio;
		}
	}

	return rwd_rewards_ratio_formated( $ratio );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer les informations sur les offres de fidélités associées a une catégorie
 *	Si les paramètres optionnels ne sont pas spécifiés, le contexte en session sera utilisé
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@return unde résultat sql ,contenant les champs id,prf_id, date_start, date_end, ratio
 */
function rwd_cat_rewards_get( $cat ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwc_id as id, rwc_prf_id as prf_id, rwc_date_start as date_start, rwc_date_end as date_end, rwc_ratio as ratio, if( rwc_cat_id='.$cat.', 9999, cat_parent_depth) as depth
		from rwd_cat_rewards
			left join prd_cat_hierarchy on (rwc_tnt_id=cat_tnt_id and cat_child_id='.$cat.')
		where rwc_tnt_id='.$config['tnt_id'].'
			and (rwc_cat_id='.$cat.' or rwc_cat_id=cat_parent_id)
		group by id
	';

	$res = ria_mysql_query( $sql );

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction est un alias vers rwd_cat_rewards_get
 *	@param int $cat Obligatoire, identifiant de catégorie
 *	@todo Alias à supprimer
 */
function rwd_rewards_get_cat( $cat ){
	return rwd_cat_rewards_get( $cat );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le ratio spécifique à une catégorie.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param $ratio Obligatoire, ratio spécifique à la catégorie sous ce format array('amount'=>euro, 'pts'=>points);
 *	@param string $date_start Optionnel, date de début du systeme de fidélité
 *	@param string $date_end Optionnel, date de fin du systeme de fidélité
 *	@return bool True si l'information a bien été mise à jour, False dans le cas contraire
 */
function rwd_cat_rewards_add( $cat, $prf, $ratio,$date_start=null,$date_end=null){
	if( !prd_categories_exists($cat)){
		return false;
	}

	if($date_start && !isdateheure($date_start)){
		return false;
	}

	if($date_end && !isdateheure($date_end)){
		return false;
	}

	if($date_start){
		$date_start = dateheureparse($date_start);
	}
	if($date_end){
		$date_end = dateheureparse($date_end);
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( !is_array($ratio) || !isset($ratio['amount'], $ratio['pts']) ){
		return false;
	}

	if( !is_numeric($ratio['amount']) || $ratio['amount']<=0 ){
		return false;
	}

	if( !is_numeric($ratio['pts']) || $ratio['pts']<=0 ){
		return false;
	}

	global $config;

	$sql = '
		insert into rwd_cat_rewards
			( rwc_tnt_id, rwc_cat_id, rwc_prf_id, rwc_ratio, rwc_date_start, rwc_date_end )
		values
			( '.$config['tnt_id'].', '.$cat.', '.$prf.', \''.$ratio['amount'].'/'.$ratio['pts'].'\',"'.($date_start ? $date_start : "NULL").'","'.($date_end ? $date_end : "NULL").'")
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un nombre de points de fidélité est défini sur une catégorie pour un profil donné.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@return bool True s'il existe des points de fidélité, False dans le cas contraire
 */
function rwd_cat_rewards_exists( $cat, $prf ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_cat_rewards
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_cat_id='.$cat.'
			and rwc_prf_id='.$prf.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de points de fidélité pour une catégorie et un profil donné.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param $ratio Optionnel, ratio spécifique pour une catégorie
  *	@param string $date_start Optionnel, date de début du systeme de fidélité
 *	@param string $date_end Optionnel, date de fin du systeme de fidélité
 *	@return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function rwd_cat_rewards_update( $cat, $prf, $ratio=false, $date_start=null, $date_end = null){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	if( $ratio!==false ){
		if( !is_array($ratio) || !isset($ratio['amount'], $ratio['pts']) ){
			return false;
		}

		if( !is_numeric($ratio['amount']) || $ratio['amount']<=0 ){
			return false;
		}

		if( !is_numeric($ratio['pts']) || $ratio['pts']<=0 ){
			return false;
		}
	}

	global $config;

	if( !$ratio ){
		rwd_prf_cat_rewards_del( $cat, $prf );
	}

	return ria_mysql_query('
		update rwd_cat_rewards
		set rwc_ratio=\''.$ratio['amount'].'/'.$ratio['pts'].'\'
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_cat_id='.$cat.'
			and rwc_prf_id='.$prf.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une précision de points de fidélité sur un produit pour un profil donné.
 *	@param int $id Obligatoire, identifiant de l'offre de fidélité
 */
function rwd_cat_rewards_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_query('
		delete from rwd_cat_rewards
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_id ='.$id
	);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le ratio spécifique à une catégorie pour un segment.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@param $ratio Obligatoire, ratio spécifique à la catégorie sous ce format array('amount'=>euro, 'pts'=>points);
 *	@return bool True si l'information a bien été mise à jour, False dans le cas contraire
 */
function rwd_seg_cat_rewards_add( $cat, $seg, $ratio ){
	if( !prd_categories_exists($cat) ){
		return false;
	}

	if( !seg_segments_exists($seg, CLS_USER) ){
		return false;
	}

	if( !is_array($ratio) || !isset($ratio['amount'], $ratio['pts']) ){
		return false;
	}

	if( !is_numeric($ratio['amount']) || $ratio['amount']<=0 ){
		return false;
	}

	if( !is_numeric($ratio['pts']) || $ratio['pts']<=0 ){
		return false;
	}

	global $config;

	if( rwd_seg_cat_rewards_exists($cat, $seg) ){
		return rwd_seg_cat_rewards_update( $cat, $seg, $ratio );
	}

	$sql = '
		insert into rwd_seg_cat_rewards
			( rwc_tnt_id, rwc_cat_id, rwc_seg_id, rwc_ratio )
		values
			( '.$config['tnt_id'].', '.$cat.', '.$seg.', \''.$ratio['amount'].'/'.$ratio['pts'].'\' )
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un nombre de points de fidélité est défini sur une catégorie pour un segment donné.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@return bool True s'il existe des points de fidélité, False dans le cas contraire
 */
function rwd_seg_cat_rewards_exists( $cat, $seg ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_seg_cat_rewards
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_cat_id='.$cat.'
			and rwc_seg_id='.$seg.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de points de fidélité pour une catégorie et un segment donné.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@param $ratio Optionnel, ratio spécifique pour une catégorie
 *	@return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function rwd_seg_cat_rewards_update( $cat, $seg, $ratio=false ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	if( $ratio!==false ){
		if( !is_array($ratio) || !isset($ratio['amount'], $ratio['pts']) ){
			return false;
		}

		if( !is_numeric($ratio['amount']) || $ratio['amount']<=0 ){
			return false;
		}

		if( !is_numeric($ratio['pts']) || $ratio['pts']<=0 ){
			return false;
		}
	}

	global $config;

	if( !$ratio ){
		rwd_seg_cat_rewards_del( $cat, $seg );
	}

	$sql = '
		update rwd_seg_cat_rewards
		set rwc_ratio=\''.$ratio['amount'].'/'.$ratio['pts'].'\'
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_cat_id='.$cat.'
			and rwc_seg_id='.$seg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une précision de points de fidélité sur un produit pour un segment donné.
 *	@param int $cat Obligatoire, identifiant d'un produit
 *	@param int $seg Obligatoire, identifiant d'un segment
 */
function rwd_seg_cat_rewards_del( $cat, $seg ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from rwd_seg_cat_rewards
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_cat_id='.$cat.' and rwc_seg_id='.$seg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de point spécifique à un produit pour un segment.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@param $pts Obligatoire, nombre de points gagnés lors de l'achat de ce produit
 *	@return bool True si l'information a bien été mise à jour, false dans le cas contraire
 */
function rwd_seg_prd_rewards_add( $prd, $seg, $pts ){
	if( !prd_products_exists($prd) ){
		return false;
	}

	if( !seg_segments_exists($seg, CLS_USER) ){
		return false;
	}

	if( !is_numeric($pts) || $pts<=0 ){
		return false;
	}

	global $config;

	if( rwd_seg_prd_rewards_exists($prd, $seg) ){
		return rwd_seg_prd_rewards_update( $prd, $seg, $pts );
	}

	$sql = '
		insert into rwd_seg_prd_rewards
			( rwp_tnt_id, rwp_prd_id, rwp_seg_id, rwp_points )
		values
			( '.$config['tnt_id'].', '.$prd.', '.$seg.', '.ceil($pts).' )
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un nombre de points de fidélité est défini sur un produit pour un segment donné.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@return bool True s'il existe des points de fidélité, False dans le cas contraire
 */
function rwd_seg_prd_rewards_exists( $prd, $seg ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_seg_prd_rewards
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_prd_id='.$prd.'
			and rwp_seg_id='.$seg.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de points de fidélité pour un produit et un segment donné.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $seg Obligatoire, identifiant d'un segment
 *	@param $pts Optionnel, nombre de points si c'est à zéro la ligne sera supprimée
 *	@return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function rwd_seg_prd_rewards_update( $prd, $seg, $pts=0 ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	global $config;

	if( $pts==0 ){
		rwd_seg_prd_rewards_del( $prd, $seg );
	}

	$sql = '
		update rwd_seg_prd_rewards
		set rwp_points='.$pts.'
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_prd_id='.$prd.'
			and rwp_seg_id='.$seg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une précision de points de fidélité sur un produit pour un segment donné.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $seg Obligatoire, identifiant d'un segment
 */
function rwd_seg_prd_rewards_del( $prd, $seg ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( !is_numeric($seg) || $seg<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from rwd_seg_prd_rewards
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_prd_id='.$prd.' and rwp_seg_id='.$seg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

/// @}

// \cond onlyria

/** \defgroup model_rewards_products Gestion des produits inclus ou exclus
 *	Les fonctions contenus dans ce module servent à configurer/récupérer les produits qui sont inclus ou exclus du système de points de fidélité.
 *	@{
 */

/** Cette fonction permet de copier la liste des article inclu ou exclu d'une configuration.
 *	@param $old Obligatoire, identifiant de l'ancienne configuration
 *	@param $new Obligatoire, identiifnat de la nouvelle configuration
 *	@return bool True si la copie s'est correctement déroulé, False dans le cas contraire
 */
function rwd_rewards_products_copy( $old, $new ){
	if( !is_numeric($old) || $old<=0 ){
		return false;
	}

	if( !rwd_rewards_exists($new) ){
		return false;
	}

	global $config;

	// Copie des produits
	$rp = rwd_products_get( $old );
	if( $rp && ria_mysql_num_rows($rp) ){
		while( $p = ria_mysql_fetch_array($rp) ){
			if( !rwd_products_add($new, $p['prd'], $p['include']) ){
				return false;
			}
		}
	}

	// Copie des marques
	$rb = rwd_brands_get( $old );
	if( $rb && ria_mysql_num_rows($rb) ){
		while( $b = ria_mysql_fetch_array($rb) ){
			if( !rwd_brands_add($new, $b['brd'], $b['include']) ){
				return false;
			}
		}
	}

	// Copie des produits
	$rc = rwd_categories_get( $old );
	if( $rc && ria_mysql_num_rows($rc) ){
		while( $c = ria_mysql_fetch_array($rc) ){
			if( !rwd_categories_add($new, $c['cat'], $c['include']) ){
				return false;
			}
		}
	}

	return true;
}

/** Cette fonction retourne la liste des articles concernés par une configuration, selon des paramètres optionnels
 *	Les inclusions sont prioritaires sur les exclusions. La priorité des types d'inclusions/exclusions est la suivante (du moins au plus important) :
 *		- Classement de l'article dans une catégorie ( catégories triés au préalable par profondeur croissante )
 *		- Marque de l'article
 *		- Article explicite
 *
 *	\warning Les frais de port sont eux pris en compte comme étant toujours exclus de la promotion ( les cas "free-shipping" sont gérés autre part )
 *
 *	@param int $prf Obligatoire, identifiant d'un profil client
 *	@param $get_excluded Optionnel, détermine si la liste retournée est celle des produits inclus ou exclus. La valeur par défaut est false (inclusions).
 *
 *	@return bool false en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant du produit
 *		- ref : référence du produit
 *		- title : titre du produit
 *		- name : nom du produit
 *
 */
function rwd_rewards_products_get( $prf, $get_excluded=false ){
	global $config;

	$start = microtime( true );
	$rreward = rwd_rewards_get( 0, $prf, $config['wst_id'] );
	if( !$rreward || !ria_mysql_num_rows($rreward) ){
		return false;
	}

	$reward = ria_mysql_fetch_array( $rreward );


	// Commun
	$sql = '
		select prd_id as id, prd_ref as ref, prd_name as name, if(prd_title!=\'\', prd_title, prd_name) as title
		from prd_products
		where
			prd_date_deleted is null and
			prd_tnt_id='.$config['tnt_id'].'
	';

	$ids = array();
	if( $reward['all-catalog'] ){

		$all_p = ria_mysql_query( '
			select prd_id as p, prd_date_deleted as "delete"
			from prd_products
			where prd_tnt_id='.$config['tnt_id'].'

		' );

		if( !$all_p || !ria_mysql_num_rows($all_p) ){
			return false;
		}

		while( $ap = ria_mysql_fetch_array($all_p) ){
			if( trim($ap['delete'])=='' ){
				$ids[] = $ap['p'];
			}
		}

	}

	$rcats = ria_mysql_query( '
		select rwc_cat_id as id, rwc_include as include
		from rwd_categories
		where rwc_tnt_id='.$config['tnt_id'].' and rwc_rwd_id='.$reward['id'].'
		order by ( ifnull((select max(cat_parent_depth) from prd_cat_hierarchy where rwc_tnt_id=cat_tnt_id and rwc_cat_id=cat_child_id),-1)  ), rwc_include
	' );

	if( $rcats && ria_mysql_num_rows($rcats) ){
		while( $cat = ria_mysql_fetch_array($rcats) ){
			$rprd = ria_mysql_query( '
				select cly_prd_id as p
				from prd_classify
				where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat['id'].'

				union

				select cly_prd_id as p
				from prd_classify, prd_cat_hierarchy
				where cly_tnt_id='.$config['tnt_id'].' and cat_tnt_id=cly_tnt_id
				and cat_child_id=cly_cat_id and cat_parent_id='.$cat['id'].'
			' );

			if( $rprd && ria_mysql_num_rows($rprd) ){
				$sub = array();
				while( $p = ria_mysql_fetch_array($rprd) ){
					$sub[] = $p['p'];
				}

				if( $cat['include']==1 ){
					$ids = array_merge( $ids,$sub );
				}else{
					$ids = array_diff( $ids,$sub );
				}
			}
		}
	}

	$rprd = ria_mysql_query('
		select prd_id as p
		from rwd_brands
			join prd_products on (rwb_tnt_id=prd_tnt_id and rwb_brd_id=prd_brd_id)
		where rwb_tnt_id='.$config['tnt_id'].'
			and rwb_rwd_id='.$reward['id'].'
			and rwb_include=0
	');

	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['p'];
		}

		$ids = array_diff( $ids, $sub );
	}

	$rprd = ria_mysql_query( '
		select prd_id as p
		from rwd_brands
			join prd_products on (rwb_tnt_id=prd_tnt_id and rwb_brd_id=prd_brd_id)
		where rwb_tnt_id='.$config['tnt_id'].'
			and rwb_rwd_id='.$reward['id'].'
			and rwb_include=1
	' );

	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['p'];
		}

		$ids = array_merge( $ids, $sub );
	}

	$rprd = ria_mysql_query( '
		select prd_id as p
		from rwd_products_sets
			join prd_products on (rwp_tnt_id=prd_tnt_id)
		where rwp_tnt_id='.$config['tnt_id'].'
			and prd_ref>=rwp_ref_start and prd_ref<=rwp_ref_stop
		and rwp_rwd_id='.$reward['id'].' and rwp_include=0
	' );

	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['p'];
		}

		$ids = array_diff( $ids,$sub );
	}

	$rprd = ria_mysql_query( '
		select prd_id as p
		from rwd_products_sets
			join prd_products on (rwp_tnt_id=prd_tnt_id)
		where rwp_tnt_id='.$config['tnt_id'].'
			and prd_ref>=rwp_ref_start and prd_ref<=rwp_ref_stop
		and rwp_rwd_id='.$reward['id'].' and rwp_include=1
	' );

	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['p'];
		}

		$ids = array_merge( $ids,$sub );
	}

	$rprd = rwd_products_get( $reward['id'], false );
	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['prd'];
		}

		$ids = array_diff( $ids,$sub );
	}

	$rprd = rwd_products_get( $reward['id'], true );
	if( $rprd && ria_mysql_num_rows($rprd) ){
		$sub = array();
		while( $p = ria_mysql_fetch_array($rprd) ){
			$sub[] = $p['prd'];
		}

		$ids = array_merge( $ids,$sub );
	}

	if( !sizeof($ids) ){
		return false;
	}

	if( $get_excluded ){
		$sql .= ' and ( prd_ref in (\''.implode( '\',\'', $config['dlv_prd_references'] ).'\') or prd_id not in ('.implode( ',',$ids ).') )';
	}else{
		$sql .= ' and prd_ref not in (\''.implode( '\',\'', $config['dlv_prd_references'] ).'\') and prd_id in ('.implode( ',',$ids ).')';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'ajout d'un produit.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $prd Obligatoire, identifiant d'un produit
 *	@param $include Optionnel, par défaut le produit est inclus, mettre false pour l'exclure
 *	@return bool True si l'insertion a fonctionnée, false dans le cas contraire
 */
function rwd_products_add( $rwd, $prd, $include=true ){
	if( !rwd_rewards_exists($rwd) ){
		return false;
	}

	if( !prd_products_exists($prd) ){
		return false;
	}

	global $config;

	if( !rwd_products_del($rwd, $prd) ){
		return false;
	}

	$sql = '
		insert into rwd_products
			( rwp_tnt_id, rwp_rwd_id, rwp_prd_id, rwp_include )
		values
			( '.$config['tnt_id'].', '.$rwd.', '.$prd.', '.( $include ? 1 : 0 ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les produits inclus ou exclus du système de fidélité.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $include Optionnel, mettre True pour récupérer les inclusions et false pour les exclusions
 *	@return resource Un résultat MySQL contenant :
 *				- rwd : identifiant d'une configuration
 *				- prd : identifiant d'un produit
 *				- include : booléen indiquant si le produit est inclu ou exclu
 *				- ref : référence du produit
 *				- title : titre du produit
 *				- name : nom du produit
 */
function rwd_products_get( $rwd, $include=null ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwp_rwd_id as rwd, rwp_prd_id as prd, rwp_include as include, prd_ref as ref, if(prd_title!=\'\', prd_title, prd_name) as title, prd_name as name
		from rwd_products
			join prd_products on (rwp_tnt_id=prd_tnt_id and rwp_prd_id=prd_id)
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_rwd_id='.$rwd.'
			and prd_date_deleted is null
	';

	if( $include!==null ){
		if( $include ){
			$sql .= ' and rwp_include=1';
		}else{
			$sql .= ' and rwp_include=0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion pour un produit.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $prd Obligatoire, identifiant ou tableau d'identifiant
 *	@return bool True si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function rwd_products_del( $rwd, $prd ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rwd_products
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_rwd_id='.$rwd.'
			and rwp_prd_id='.$prd.'
	');
}

/** Cette fonction permet d'ajout une règle d'inclusion ou d'exclusion d'une plage de produit pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $ref_start Obligatoire, référence de départ
 *	@param $ref_stop Obligatoire, référence de fin
 *	@param $include Optionnel, par défaut la plage de référence est inclue, mettre false pour l'exclure
 *	@return bool True si l'insertion s'est correctement déroulée, false dans le cas contraire
 */
function rwd_products_sets_add( $rwd, $ref_start, $ref_stop, $include=true ){
	if( !rwd_rewards_exists($rwd) ){
		return false;
	}

	if( !trim($ref_start) || !trim($ref_stop) ){
		return false;
	}

	global $config;

	if( !rwd_products_sets_del($rwd, $ref_start, $ref_stop) ){
		return false;
	}

	$sql = '
		insert into rwd_products_sets
			( rwp_tnt_id, rwp_rwd_id, rwp_ref_start, rwp_ref_stop, rwp_include )
		values
			( '.$config['tnt_id'].', '.$rwd.', \''.addslashes( $ref_start ).'\', \''.addslashes( $ref_stop ).'\', '.( $include ? 1 : 0 ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les plages de références inclues ou exclues d'une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $include Optionnel, mettre True pour récupérer les inclusions et false pour les exclusions
 *	@return resource Un résultat MySQL contenant :
 *				- rwd : identifiant d'une configuration
 *				- ref-start : référence produit de début
 *				- ref-stop : référence produit de fin
 *				- ref-include : booléen indiquant si la plage de référence est inclue ou exclue
 */
function rwd_products_sets_get( $rwd, $include=false ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwp_rwd_id as rwd, rwp_ref_start as "ref-start", rwp_ref_stop as "ref-stop", rwp_include as include
		from rwd_products_sets
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_rwd_id='.$rwd.'
	';

	if( $include!==null ){
		if( $include ){
			$sql .= ' and rwp_include=1';
		}else{
			$sql .= ' and rwp_include=0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une plage de référence pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $ref_start Obligatoire, référence de départ
 *	@param $ref_stop Obligatoire, référence de fin
 *	@return bool True si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function rwd_products_sets_del( $rwd, $ref_start, $ref_stop ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	if( !trim($ref_start) || !trim($ref_stop) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rwd_products_sets
		where rwp_tnt_id='.$config['tnt_id'].'
			and rwp_ref_start=\''.addslashes( $ref_start ).'\'
			and rwp_ref_stop=\''.addslashes( $ref_stop ).'\'
	');
}

/** Cette fonction permet d'ajout une régle d'inclusion ou d'exclusion d'une marque à une configuration du système.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $brd Obligatoire, identifiant d'une marque
 *	@param $include Optionnel, par défaut la marque est inclue, mettre false pour l'exclure
 *	@return bool True si l'insertion s'est correctement déroulée, false dans le cas contraire
 */
function rwd_brands_add( $rwd, $brd, $include=true ){
	if( !rwd_rewards_exists($rwd) ){
		return false;
	}

	if( !prd_brands_exists($brd) ){
		return false;
	}

	global $config;

	if( !rwd_brands_del($rwd, $brd) ){
		return false;
	}

	$sql = '
		insert into rwd_brands
			( rwb_tnt_id, rwb_rwd_id, rwb_brd_id, rwb_include )
		values
			( '.$config['tnt_id'].', '.$rwd.', '.$brd.', '.( $include ? 1 : 0 ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne les règles d'inclusion ou d'exclusion d'une marque pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $include Optionnel, mettre True pour récupérer les inclusions et false pour les exclusions
 *	@return bool false si le paramètre est omis ou faux, sinon un résultat MySQL contenant :
 *				- rwd : identifiant d'une configuration
 *				- brd : identifiant d'une marque
 *				- include : booléen indiquant si la marque est inclue ou exclue
 *				- title : titre de la marque
 *				- name : nom de la marque
 *				- products : nombre de produits publiés contenant dans la marque
 */
function rwd_brands_get( $rwd, $include=null ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwb_rwd_id as rwd, rwb_brd_id as brd, rwb_include as include, if(brd_title!=\'\', brd_title, brd_name) as title, brd_name as name, brd_products as products
		from rwd_brands
			join prd_brands on (rwb_tnt_id=brd_tnt_id and rwb_brd_id=brd_id)
		where rwb_tnt_id='.$config['tnt_id'].'
			and rwb_rwd_id='.$rwd.'
			and brd_date_deleted is null
	';

	if( $include!==null ){
		if( $include ){
			$sql .= ' and rwb_include=1';
		}else{
			$sql .= ' and rwb_include=0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion sur une marque pour une configuraton.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $brd Obligatoire, identifiant d'une marque
 *	@return bool True si la suppression s'est correctement passée, false dans le cas contraire
 */
function rwd_brands_del( $rwd, $brd ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	if( !is_numeric($brd) || $brd<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rwd_brands
		where rwb_tnt_id='.$config['tnt_id'].'
			and rwb_rwd_id='.$rwd.'
			and rwb_brd_id='.$brd.'
	');
}

/** Cette fonction permet d'ajout une règle d'inclusion ou d'exclusion sur une catégorie pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param $include Optionnel, par défaut la catégorie est inclue, mettre false pour l'exclure
 *	@return bool True si l'insertion s'est correctement déroulée, false dans le cas contraire
 */
function rwd_categories_add( $rwd, $cat, $include=true ){
	if( !rwd_rewards_exists($rwd) ){
		return false;
	}

	if( !prd_categories_exists($cat) ){
		return false;
	}

	global $config;

	if( !rwd_categories_del($rwd, $cat) ){
		return false;
	}

	$sql = '
		insert into rwd_categories
			( rwc_tnt_id, rwc_rwd_id, rwc_cat_id, rwc_include )
		values
			( '.$config['tnt_id'].', '.$rwd.', '.$cat.', '.( $include ? 1 : 0 ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les règles d'inclusion ou d'exclusion de catégorie pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param $include Optionnel, mettre True pour récupérer les inclusions et false pour les exclusions
 *	@return bool false si le paramètre est omis ou faux, sinon un résultat MySQL contenant :
 *				- rwd : identifiant d'une configuration
 *				- cat : identifiant d'une catégorie
 *				- include : booléen indiquant si la catégorie est inclue ou exclue
 *				- title : titre de la catégorie
 *				- name : nom de la catégorie
 *				- products : nombre de produits publiés contenant dans la catégorie
 */
function rwd_categories_get( $rwd, $include=null ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwc_rwd_id as rwd, rwc_cat_id as cat, rwc_include as include, if(cat_title!=\'\', cat_title, cat_name) as title, cat_name as name, cat_products as products
		from rwd_categories
			join prd_categories on (rwc_tnt_id=cat_tnt_id and rwc_cat_id=cat_id)
		where rwc_tnt_id='.$config['tnt_id'].'
			and cat_date_deleted is null
			and rwc_rwd_id='.$rwd.'
	';

	if( $include!==null ){
		if( $include ){
			$sql .= ' and rwc_include=1';
		}else{
			$sql .= ' and rwc_include=0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion d'une catégorie pour une configuration.
 *	@param $rwd Obligatoire, identifiant d'une configuration
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool True si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function rwd_categories_del( $rwd, $cat ){
	if( !is_numeric($rwd) || $rwd<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rwd_categories
		where rwc_tnt_id='.$config['tnt_id'].'
			and rwc_rwd_id='.$rwd.'
			and rwc_cat_id='.$cat.'
	');
}

/// @}

// \endcond

/** \defgroup model_actions Gestion des actions
 *	Les fonctions contenus dans ce module servent à configurer/récupérer les actions incluses dans le système de points de fidélité.
 *	@{
 */

// \cond onlyria
/** Cette fonction est chargé de vérifier si les points de fidélité peuvent être appliqués pour une action.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@param int $usr Optionnel, identifiant d'un compte client. Si non précisé, l'utilisateur actif sera récupéré automatiquement de l'environnement.
 *	@param $params Optionnel, certaines informations sont obligatoire pour permettre de savoir si les points de fidélité peuvent être attribués
 *	@return bool True si les points de fidélité peuvent être attribué au compte client, False dans le cas contraire
 */
function rwd_actions_is_applicable( $rwa, $usr=0, $params=array() ){
	if( !rwd_actions_exists($rwa) ){
		return false;
	}

	if( !$usr && !isset($_SESSION['usr_id']) ){
		return false;
	}

	if( !is_array($params) ){
		return false;
	}

	global $config;

	// Récupère les informations
	$usr = !$usr ? $_SESSION['usr_id'] : $usr;
	if( !$usr || !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	$ruser = gu_users_get( $usr );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return false;
	}

	$user = ria_mysql_fetch_array( $ruser );

	$code = rwd_actions_get_code( $rwa );
	if( !trim($code) ){
		return false;
	}

	$state_ok = array( 3, 4, 5, 6, 7, 8, 11, 12, 24, _STATE_INV_STORE );

	switch( $code ){
		case 'RWA_FIRST_ORDER' : // Passage de la première commande
			$rwc = $code!='RWA_FIRST_ORDER' ? 'RWC_ORDER_MIN' : 'RWC_FIRST_ORDER_MIN';
			$min_amount = 0;

			$cfg = rwd_action_conditions_configs_get( 0, 0, $rwc, 0, $user['prf_id'], $config['wst_id'] );
			if( $cfg && ria_mysql_num_rows($cfg) ){
				$min_amount = ria_mysql_result( $cfg, 0, 'val' );
			}

			$first_order = 0;
			$first_inscript = null;

			// Si une inscription est obligatoire, alors première commande après la première inscription
			if (isset($config['rwd_register_required']) && $config['rwd_register_required']) {
				// Récupère la date de première inscription
				$first_inscript = rwd_users_get_first_inscription( $user['id'] );

				if ($first_inscript === false) {
					return ERROR_RWC_NO_FIRST_ORDER;
				}
			}

			$r_first = ord_orders_get( $user['id'], 0, ord_states_get_ord_valid(false, true), 0, null, array('date'=>'asc'), false, false, false, false, false, '', ($config['tnt_id']== 13  ? false : true) );
			if ($r_first) {
				while ($first = ria_mysql_fetch_assoc($r_first)) {
					if ($first_inscript !== null) {
						if (strtotime($first['date_en']) < strtotime($first_inscript)) {
							continue;
						}
					}

					if ($first['total_ttc'] > $min_amount) {
						$first_order = $first['id'];
						break;
					}
				}
			}

			if ($first_order > 0 && $first_order != $params['ord_id']) {
				return ERROR_RWC_NO_FIRST_ORDER;
			}

			break;
		case 'RWA_ORDER' : // Passage d'une commande

			$rwc = $code!='RWA_FIRST_ORDER' ? 'RWC_ORDER_MIN' : 'RWC_FIRST_ORDER_MIN';

			// Contrôle des paramètres
			if( !isset($params['ord_id']) || !($ord = ria_mysql_fetch_array( ord_orders_get($user['id'], $params['ord_id']) )) ){
				return ERROR_RWC_NO_ORDER;
			}

			if( !isset($params['is_ttc']) ){
				return ERROR_RWC_PARAMS;
			}

			$total = ord_orders_get_total_without_port( $params['ord_id'], $params['is_ttc'] );

			// Vérification du minimum de commande
			$cfg = rwd_action_conditions_configs_get( 0, 0, $rwc, 0, $user['prf_id'], $config['wst_id'] );
			if( $cfg && ria_mysql_num_rows($cfg) ){
				$min_amount = ria_mysql_result( $cfg, 0, 'val' );
				if( !$min_amount ){
					break;
				}

				if( $total<$min_amount ){
					return ERROR_RWC_MIN_ORDER;
				}
			}

			break;

		case 'RWA_NTH_ORDER' : // Passage de la Nème commande

			// Contrôle des paramètres
			if( !isset($params['is_ttc']) ){
				return ERROR_RWC_PARAMS;
			}

			// Récupère le minimum de commande
			$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_NTH_ORDER_MIN', 0, $user['prf_id'], $config['wst_id'] );
			$limit = $cfg && ria_mysql_num_rows($cfg) ? ria_mysql_result($cfg, 0, 'val') : 0;

			// Récupère le nombre de commande passé (celle pour laquelle on vérifie est prise en compte)
			$min_order = $limit>0 ? array( 'amount'=>$limit, 'is_ttc'=>$params['is_ttc'] ) : 0;
			$orders = ord_orders_get_average_totals( true, $state_ok, false, false, false, 0, false, $user['id'], $min_order );

			// Vérifie qu'il existe un pallier avec ce nombre de commande
			$landing = rwd_action_conditions_configs_get( 0, 0, 'RWC_NTH_ORDER_NB', 0, $user['prf_id'], $config['wst_id'], false, $orders['volume'] );

			if( !$landing || !ria_mysql_num_rows($landing) ){
				return ERROR_RWC_NO_NTH;
			}

			break;

		case 'RWA_BIRTHDAY' : // Anniversaire d'une personne

			// Condition si le compte a déjà passé commande
			$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_BIRTHDAY_ORD', 0, $user['prf_id'], $config['wst_id'] );
			if( $cfg && ria_mysql_num_rows($cfg) ){
				$actived = ria_mysql_result( $cfg, 0, 'val' );
				if( !$actived ){
					break;
				}

				$orders = ord_orders_get( $user['id'], 0, $state_ok );
				$count_order = $orders ? ria_mysql_num_rows($orders) : 0;

				if( !$count_order ){
					return ERROR_RWC_ORD_BIRTHDAY;
				}
			}

			// Contrôle que des points n'ont pas déjà été attribué cette année
			$r_stat = stats_rewards_get($user['id'], 0, $rwa, 0, false, date('Y').'-01-01', date('Y').'-12-31');
			if ($r_stat && ria_mysql_num_rows($r_stat)) {
				return ERROR_RWC_BIRTHDAY;
			}

			break;

		case 'RWA_FRIENDS' : // Utilisation de la fonction "envoyer à un ami"

			// Condition sur la limite de points journalier
			$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_FRIENDS_MAX', 0, $user['prf_id'], $config['wst_id'] );
			if( $cfg && ria_mysql_num_rows($cfg) ){
				$limit = ria_mysql_result( $cfg, 0, 'val' );
				if( $limit<=0 ){
					break;
				}

				// Récupère le nombre de point déjà gagné aujourd'hui
				$stats = stats_rewards_get( $user['id'], $config['wst_id'], $rwa, 0, false, date('Y-m-d'), date('Y-m-d') );
				if( !$stats ){
					return false;
				}

				$pts = 0;
				while( $stat = ria_mysql_fetch_array($stats) ){
					$pts += $stat['pts'];
				}

				if( $limit<=$pts ){
					return ERROR_RWC_FRIEND_MAX;
				}
			}

			// Adresse mail unique
			$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_FRIENDS_MAIL', 0, $user['prf_id'], $config['wst_id'] );
			if( $cfg && ria_mysql_num_rows($cfg) ){
				$actived = ria_mysql_result( $cfg, 0, 'val' );
				if( !$actived ){
					break;
				}

				// Controle que le paramètre obligatoire est fourni
				if( !isset($params['dst-mail']) || !trim($params['dst-mail']) ){
					return ERROR_RWC_PARAMS;
				}

				// Message avec le même destinataire et de type "envoyer à un ami"
				$rmsg = messages_get( $user['id'] , 'PRODUCT', 0, 0, 0, false, false, false, 0, 0, false, false, null, true, false, false, null, $params['dst-mail'] );
				$count_msg = $rmsg ? ria_mysql_num_rows( $rmsg ) : 0;

				if( $count_msg ){
					return ERROR_RWC_FRIEND_MAIL;
				}
			}

			break;

		case 'RWC_BIRTHDAY_UPD' : // Mise à jour de sa date d'anniversaire

			// Vérifier que le compte n'a pas déjà reçu des points de fidélité pour cette action
			$stats = stats_rewards_get( $user['id'], $config['wst_id'], $rwa );
			if( $stats && ria_mysql_num_rows($stats) ){
				return false;
			}

			break;
	}

	return true;
}
// \endcond

/**	Surveille l'activité dans un site donné pour détecter des événements liés au système de points fidélité, par exemple :
 *	- clic depuis un lien envoyé par un parrain
 *	- création d'un compte client
 *	- partage sur les réseaux sociaux
 *	@return bool true en cas de succès, false si l'utilisateur en cours n'est pas identifiable
 */
function rwd_actions_detected(){
	global $config;

	// Détection d'un lien de tracking
	if( isset($_GET['sp']) && is_numeric($_GET['sp']) && $_GET['sp']>0 ){
		$_SESSION['reward_sponsor_id'] = $_GET['sp'];
		unset($_GET['sp']);
	}

	if( !gu_users_is_connected() ){
		return false;
	}

	// Enregistre le parrain temporaire suite à un clic sur un lien de parrainage "?sp=[parrain_usr_id]"
	if( isset($_SESSION['reward_sponsor_id']) && is_numeric($_SESSION['reward_sponsor_id']) && $_SESSION['reward_sponsor_id']>0 ){
		gu_sponsors_add( $_SESSION['usr_id'], $_SESSION['reward_sponsor_id'] );
		unset( $_SESSION['reward_sponsor_id'] );
	}

	// Enregistre l'action de création d'un compte, exemple : $_SESSION['reward_create_account'] = $usr['id'];
	if( isset($_SESSION['reward_create_account']) && is_numeric($_SESSION['reward_create_account']) && $_SESSION['reward_create_account']>0 ){
		if( rwd_actions_apply('RWA_CREATE_ACCOUNT', CLS_USER, $_SESSION['reward_create_account']) ){
			unset($_SESSION['reward_create_account']);
		}
	}

	// Partage sur les réseaux sociaux, exemple : $_SESSION['reward_network'] = array( 'cls'=>CLS_WEBSITE, 'obj'=>array($config['wst_id']) );
	if( isset($_SESSION['reward_network']) ){
		// Contrôle le contenu de la variable, si incomplète elle est automatiquement supprimée
		$n = $_SESSION['reward_network'];
		if( !isset($n['cls'], $n['obj']) || !fld_classes_exists($n['cls']) || !is_array($n['obj']) || !sizeof($n['obj']) ){
			unset($_SESSION['reward_network']);
		}

		foreach( $n['obj'] as $o ){
			if( !is_numeric($o) || $o<=0 ){
				unset($_SESSION['reward_network']);
			}
		}

		// Enregistre l'action de partage sur les réseaux sociaux
		if( rwd_actions_apply('RWA_SOCIAL_NETWORK', $n['cls'], $n['obj']) ){
			unset($_SESSION['reward_network']);
		}
	}

	return true;
}

// \cond onlyria
/** Cette fonction centralise tous les ajouts de points de fidélité.
 *	@param string $code Obligatoire, code de l'action
 *	@param $cls Obligatoire, type de contenu
 *	@param $obj Obligatoire, identifiant du contenu
 *	@param $params Optionnel, certaines informations sont obligatoire pour permettre de savoir si les points de fidélité peuvent être attribués
 *	@return bool True si l'ajout s'est correctement passée, False dans le cas contraire
 */
function rwd_actions_apply( $code, $cls, $obj, $params=array() ){
	global $config;

	// Si le module de points de fidélité n'est pas activé, on enregistre aucune action
	if( !isset($config['rwd_reward_actived']) || !$config['rwd_reward_actived'] ){
		return true;
	}

	if( !trim($code) ){
		return false;
	}

	if( !fld_classes_exists($cls) ){
		return false;
	}

	if( !is_numeric($obj) && !is_array($obj) ){
		return false;
	}

	if( !is_array($obj) ){
		if( !is_numeric($obj) || $obj<=0 ){
			return false;
		}
		$obj = array( $obj );
	}else{
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<=0 ) {
				return false;
			}
		}
	}

	$cancel = false;

	$state_ok = array( 3, 4, 5, 6, 7, 8, 11, 12, 24, _STATE_INV_STORE );

	switch( $code ){
		case 'RWA_SPONSOR' :{ // Parrainage
			//	Verifie l'existence d'un filleul

			if(!isset($params['prf']) && isset($params["godson"]) && is_numeric($params["godson"])){
				return false;
			}

			// Récupère la configuration

			$cfg = rwd_rewards_get_sponsor( 0, $config['wst_id'], $params['prf'] );
			if( !is_array($cfg) || count($cfg) <= 0 ){
				return false;
			}

			if (!ria_array_key_exists(array('active', 'sp_system'), $cfg)) {
				return false;
			}

			if( !isset($cfg['active']) || !$cfg['active'] ){
				return true;
			}

			$r_user = gu_users_get($params["godson"]);

			if(!$r_user && !ria_mysql_num_rows($r_user)){
				return false;
			}

			$user_godson = ria_mysql_fetch_assoc($r_user);

			// ATTENTION : Vérifier l'option PREMIERE COMMANDE

			if(isset($cfg['first_order']) && $cfg['first_order'] != 0){
				$rwa = rwd_actions_get_id_bycode( 'RWA_FIRST_ORDER' );

				$is_apply = rwd_actions_is_applicable( $rwa, $user_godson["id"], array('ord_id' => $obj[0]) );
				if ($is_apply !== true) {
					return false;
				}
			}

			// Attribuer la récompense au filleul
			if (is_numeric($cfg['points']) && $cfg['points'] > 0) {

				$usr = gu_sponsor_points_get_usr_id($user_godson["email"]);

				if(!$usr || $usr <= 0){
					return false;
				}

				if( !$cfg['limit']==0 || !gu_sponsor_count($usr)<$cfg['limit'] ){
					return false;
				}

				if(gu_sponsor_points_exists($usr,$user_godson["email"]) && !gu_sponsor_points_is_used($usr,$user_godson["email"])){
					stats_rewards_add( $user_godson["id"], $params['prf'], 0, false, 'Commande parrainée', false, false, $cfg['points'],false,false,$cfg['days']);
					gu_sponsor_points_set_used($usr,$user_godson["email"]);
				}
			}else if(isset($params['cod']) && $params['cod'] != ""){

				$usr = gu_sponsor_promotions_get_user_id($params["cod"]);

				if(!$usr || $usr <= 0){
					return false;
				}
				if( !$cfg['limit']==0 || !gu_sponsor_count($usr)<$cfg['limit'] ){
					return false;
				}

				gu_sponsor_promotions_set_used( $usr, $params['cod'] );
				gu_sponsor_promotions_set_points( $usr, $params['cod'], $cfg['pts'] );
				gu_users_set_sponsor( $params['godson'], '', $usr );
			}else{
				$r_user = gu_sponsors_get($params['godson']);
				if($r_user && ria_mysql_num_rows($r_user)){
					$usr = ria_mysql_fetch_assoc($r_user);
				}
			}

			// Attribuer la récompense au parrain
			if(isset($usr)){
				rwd_sponsor_reward( $cls, $cfg, $usr, $obj, $params );
			}

			break;
		}
		case 'RWA_INVOICE' : { // Facturation
			// Récupère les informations sur la facture
			$r_invoice = ord_invoices_get($obj[0]);
			if( !$r_invoice || !ria_mysql_num_rows($r_invoice) ){
				return false;
			}

			$invoice = ria_mysql_fetch_array( $r_invoice );

			// On vérifie que la facture n'est pas déjà concernée par des points de fidélité.
			// Des contrôles spécifiques peuvent exister pour certains clients.
			switch( $config['tnt_id'] ){
				case 13: // Pierre Oteiza
					$params['date_created'] = date('Y-m-d', strtotime($invoice['date_en']));

					break;
				case 14: // Terre de Viande
					if( stats_rewards_objs_exists(CLS_INVOICE, $obj[0], false, false, '', 'REMBOURSEMENT SUR VOTRE COMPTE POINTS DE FIDELITE') ){
						return true;
					}

					break;
			}

			// Supprime les anciennes entrées pour ensuite les insérer de nouveau.
			if( !empty($config['override_rewards_on_invoice']) ){
				ria_mysql_query('
					delete from stats_rewards
					where stats_tnt_id = '.$config['tnt_id'].'
						and stats_wst_id = '.$config['wst_id'].'
						and stats_obj_id_0 = '.$obj[0]
				);
			}

			if( stats_rewards_objs_exists(CLS_INVOICE, $obj[0]) ){
				return true;
			}

			// Vérifie que les commandes rattachées à la facture n'ont pas de points de fidélité
			if( $riord = ord_inv_orders_get($obj[0]) ){
				while( $iord = ria_mysql_fetch_assoc($riord) ){
					$res = ria_mysql_query('
						select 1
						from stats_rewards
						where stats_tnt_id='.$config['tnt_id'].'
							and stats_rwa_id=2
							and stats_obj_id_0 = '.$iord['id'].'
							and stats_date_deleted is null
					');

					if( $res && ria_mysql_num_rows($res) ){
						return true;
					}
				}
			}

			// Récupère les informations sur le client de la facture
			$ruser = gu_users_get( $invoice['usr_id'] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return false;
			}

			if(isset($config['rwd_register_required']) && $config['rwd_register_required']){
				if(!rwd_users_is_enabled($invoice['usr_id'])){
					return false;
				}
			}

			$user = ria_mysql_fetch_array( $ruser );

			// Récupère le nombre de points générés par cette facture
			$points = rwd_rewards_calculated_order_points($invoice['id'], $user, 'ttc', 1, CLS_INVOICE);

			// Récupère le nombre de points gagnés pour le parrain lors du passage d'une commande
			$rwa = rwd_actions_get_id_bycode( 'RWA_ORDER' );
			$pts = rwd_actions_get_points( $rwa, $user['prf_id'] );
			if( isset($pts[1]) && is_numeric($pts[1]) && $pts[1]>0 ){
				$points = array( $points, $pts[1] );
			}

			$cancel = false;

			// Permet d'attribuer des avoir en points de fidélité selon si le montant de la facture est négative ou non
			if( isset($config['rwd_offsets_actived']) && $config['rwd_offsets_actived'] ){
				if( $points < 0 ){
					$points *= -1;
					$cancel = true;
				}
			}

			if( $points>0 ){
				$date_created = isset($params['date_created']) && isdateheure($params['date_created']) ? $params['date_created'] : null;
				stats_rewards_add( $user['id'], $user['prf_id'], 0, $cancel, 'Emission d\'une facture', CLS_INVOICE, $obj[0], $points, false, false, -1, 0, $date_created );
			}
			break;
		}
		case 'RWA_ORDER' :
		case 'RWA_FIRST_ORDER' :
		case 'RWA_NTH_ORDER' : { // Passage d'une commande
			// On vérifie que la commande n'est pas déjà concerné par des points de fidélité
			if( stats_rewards_objs_exists(CLS_ORDER, $obj[0]) ){
				return true;
			}

			// Récupère les informations sur la commande
			$rord = ord_orders_get( 0, $obj[0] );
			if( !$rord || !ria_mysql_num_rows($rord) ){
				return false;
			}

			$order = ria_mysql_fetch_array( $rord );

			$control = !isset($config['rwd_no_control_state']) || !$config['rwd_no_control_state'];
			if ($control && !isset($params['no_control_state'])) {
				// Applique les points de fidélité seulements lors du passage du status 4 de la commande
				if( $order['state_id']!=_STATE_PAY_CONFIRM && $order['state_id'] != _STATE_CLICK_N_COLLECT){
					return true;
				}
			}

			// Si aucun contrôle demandé, on vérifie tout de fois que la commande est Web
			if ((isset($config['rwd_no_control_state']) && $config['rwd_no_control_state']) || isset($params['no_control_state'])) {
				if (!is_numeric($order['pay_id']) || $order['pay_id'] <= 0) {
					return true;
				}
			}

			// Récupère les informations sur le client de la commande
			$ruser = gu_users_get( $order['user'] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return false;
			}

			$user = ria_mysql_fetch_array( $ruser );

			if(isset($config['rwd_register_required']) && $config['rwd_register_required']){
				if(!rwd_users_is_enabled($user['id'])){
					return false;
				}
			}

			// Si le calcul des points doit se faire sur le montant HT ou TTC de la commande
			$type = rwd_rewards_get_type_amount( 0, $user['prf_id'], $config['wst_id'] );
			if( !in_array($type, array('ttc', 'ht')) ){
				return false;
			}

			// Cumule des points de fidélité
			$is_cumul = rwd_rewards_get_cumul_pts( $user['prf_id'] );

			$date_created = isset($params['date_created']) && isdateheure($params['date_created']) ? $params['date_created'] : null;
			$type_ratio = rwd_rewards_get_type_ratio( 0, $user['prf_id'], $config['wst_id'] );

			// Récupère le nombre de points pour cette commande
			$points = rwd_rewards_calculated_order_points( $order['id'], $user, $type, $type_ratio);

			// Première commande - identifiant de l'action
			if (!isset($params['no_first_order'])) {
				$rwa = rwd_actions_get_id_bycode( 'RWA_FIRST_ORDER' );

				$is_apply = rwd_actions_is_applicable( $rwa, $user['id'], array('is_ttc'=>($type=='ttc'), 'ord_id'=>$obj[0]) );
				if( $is_apply===true ){

					// Passage d'une commande - si des points de fidélité sont précisé alors ils ne seront pas calculé par rapport au ratio
					$pts = rwd_actions_get_points( $rwa, $user['prf_id'] );

					// Récupère le multiplicateur de points de fidélité
					$multi = 0;
					$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_FIRST_ORDER_MULTI', 0, $user['prf_id'], $config['wst_id'] );
					if( $cfg && ria_mysql_num_rows($cfg) ){
						$multi = ria_mysql_result( $cfg, 0, 'val' );
						if( $multi>0 ){
							$pts[0] = $points * $multi;
						}
					}

					// Première commande - enregistrement des points de fidélité
					if( $multi ){
						stats_rewards_add( $user['id'], $user['prf_id'], $rwa, false, '', CLS_ORDER, $obj[0], $pts, false, false, -1, 0, $date_created );
					}else{
						stats_rewards_add( $user['id'], $user['prf_id'], $rwa, false, '', CLS_ORDER, $obj[0], false, false, false, -1, 0, $date_created );
					}

				}
			}

			// Passage d'une commande - identifiant de l'action
			$rwa = rwd_actions_get_id_bycode( 'RWA_ORDER' );

			$is_apply = rwd_actions_is_applicable( $rwa, $user['id'], array('is_ttc'=>($type=='ttc'), 'ord_id'=>$obj[0]) );
			if( $is_cumul && $is_apply===true ){

				// Passage d'une commande - si des points de fidélité sont précisé alors ils ne seront pas calculé par rapport au ratio
				$pts = rwd_actions_get_points( $rwa, $user['prf_id'] );

				if( !is_array($pts) || sizeof($pts)!=2 || $pts[0]<=0 ){

					// Passage commande - récupère le maximum de points
					$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_ORDER_MAX_PTS', 0, $user['prf_id'], $config['wst_id'] );
					if( $cfg && ria_mysql_num_rows($cfg) ){
						$limit = ria_mysql_result( $cfg, 0, 'val' );
						if( $limit>0 ){
							$points = $points>$limit ? $limit : $points;
						}
					}

				}else{
					$points = $pts[0];
				}

				// Nombre de points pour le parrain
				$sp_pts = is_array($pts) && isset($pts[1]) && $pts[1]>0 ? $pts[1] : 0;
				$pts = array( $points, $sp_pts );

				stats_rewards_add( $user['id'], $user['prf_id'], $rwa, false, '', CLS_ORDER, $obj[0], $pts, false, false, -1, 0, $date_created );
			}

			// Passage de la Nème commande - identifiant de l'action
			$rwa = rwd_actions_get_id_bycode( 'RWA_NTH_ORDER' );

			$is_apply = rwd_actions_is_applicable( $rwa, $user['id'], array('is_ttc'=>($type=='ttc')) );
			if( $is_cumul && $is_apply===true ){

				// Récupère le minimum de commande
				$cfg = rwd_action_conditions_configs_get( 0, 0, 'RWC_NTH_ORDER_MIN', 0, $user['prf_id'], $config['wst_id'] );
				$limit = $cfg && ria_mysql_num_rows($cfg) ? ria_mysql_result($cfg, 0, 'val') : 0;

				// Récupère le nombre de commande passé (celle pour laquelle on vérifie est prise en compte)
				$min_order = $limit>0 ? array( 'amount'=>$limit, 'is_ttc'=>($type=='ttc') ) : 0;
				$orders = ord_orders_get_average_totals( true, $state_ok, false, false, false, 0, false, $user['id'], $min_order );

				// Vérifie qu'il existe un pallier avec ce nombre de commande
				$landing = rwd_action_conditions_configs_get( 0, 0, 'RWC_NTH_ORDER_NB', 0, $user['prf_id'], $config['wst_id'], false, $orders['volume'] );

				if( $landing && ria_mysql_num_rows($landing) ){
					$l = ria_mysql_fetch_array( $landing );
					$pts = array( $l['pts'], $l['sp_pts'] );

					stats_rewards_add( $user['id'], $user['prf_id'], $rwa, false, 'Passage de la '.$l['val'].'ème commande', CLS_ORDER, $obj, $pts, false, false, -1, 0, $date_created );
				}

			}

			break;
		}
		case 'RWC_CANCEL_ORDER' : { // Annulation complète d'une commande
			// Récupère les informations sur la commande
			$rord = ord_orders_get( 0, $obj[0] );
			if( !$rord || !ria_mysql_num_rows($rord) ){
				return false;
			}

			$order = ria_mysql_fetch_array( $rord );

			// Récupère les informations sur le client de la commande
			$ruser = gu_users_get( $order['user'] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return false;
			}

			$user = ria_mysql_fetch_array( $ruser );

			// Récupère le total des points données pour cette commande à l'internaute
			$stats = stats_rewards_get( $user['id'], $config['wst_id'], 0, $cls, $obj );
			$pts = array( 0, 0 );
			if( $stats && ria_mysql_num_rows($stats) ){
				while( $s = ria_mysql_fetch_array($stats) ){
					$pts[0] += $s['pts'];
				}
			}

			// Récupère le total des points données pour cette commande au sponsor
			$stats = stats_rewards_get( 0, $config['wst_id'], 0, $cls, $obj, false, false, $user['id'] );
			if( $stats && ria_mysql_num_rows($stats) ){
				while( $s = ria_mysql_fetch_array($stats) ){
					$pts[1] += $s['pts'];
				}
			}

			// Annulation des points gagnés grâce à cette commande
			$rwa = rwd_actions_get_id_bycode( 'RWA_ORDER' );
			stats_rewards_add( $user['id'], $user['prf_id'], $rwa, true, '', $cls, $obj, $pts );

			// Annulation des points utilisés pour cette commande
			$fpts = fld_object_values_get( $obj, _FLD_ORD_PTS );
			if( $fpts ){
				stats_rewards_del( $user['id'], CLS_ORDER, $obj, 0 );
			}
			break;
		}
		case 'RWC_RETURN_ORDER' : { // Commande retournée
			if( !isset($params['return']) || !$params['return'] ){
				return false;
			}

			require_once('ord.returns.inc.php');

			$ar_stats = array();
			// Récupère les informations sur les points de fidélité
			$rwa = rwd_actions_get_id_bycode( 'RWA_ORDER' );
			$stats = stats_rewards_get( 0, 0, $rwa, $cls, $obj, false, false, -1 );
			if( $stats && ria_mysql_num_rows($stats) ){

				$type = false;
				$max_pts = $ratio = $convert = 0;
				while( $s = ria_mysql_fetch_array($stats) ){
					$max_pts += $s['pts'];
					$ratio = !$ratio && trim($s['ratio']) ? $s['ratio'] : $ratio;
					$type = !$type ? $s['type_amount'] : $type;
				}

				$type = !$type ? 'ttc' : $type;

				$prds = ord_products_get_count($obj[0], true, false, true );
				$rtns = ord_returns_products_get_qte_returned( $obj[0] );

				// Si tous les produits sont retournés alors l'annulation des poins de fidélité ce fait comme une annulation de commande
				if( $prds==$rtns ){
					return rwd_actions_apply( 'RWC_CANCEL_ORDER', $cls, $obj );
				}

				$r = explode('/', $ratio);
				$ratio = rwd_rewards_ratio_formated( $r[1].'/'.$r[0] );

				if( is_array($ratio) && sizeof($ratio)==2 ){
					// Récupère le montant total du retour (donc que des produits acceptés en retour)
					$total = ord_returns_products_get_totals( $params['return'], ORD_RETURNS_PRD_STATE_SUCCESS, $type=='ttc' );
					$pts = $total * ( $ratio['pts'] / $ratio['amount'] );
				}

				if( $pts>0 && ($usr = ord_returns_get_user( $params['return'] )) ){
					$prf = gu_users_get_prf( $usr );
					// Annulation partiel de la commande
					if( !stats_rewards_add($usr, $prf, 0, true, 'Retour partiel d\'une commande', CLS_ORDER, $obj, $pts, $ratio) ){
						return false;
					}
				}
			}

			break;
		}
		case 'RWA_CANCEL_BTNLIKE_FACEBOOK' : { // Annulation d'un LIKE
			$user_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ? $_SESSION['admin_view_user'] : ( isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0 );
			if( !$user_id ) return false;

			$rwa = rwd_actions_get_id_bycode( 'RWA_SOCIAL_NETWORK' );

			$pts = array(0, 0);

			// Récupère les points gagnés par l'internaute
			$rstat = stats_rewards_get( $user_id, $config['wst_id'], $rwa, $cls, $obj );
			if( $rstat && ria_mysql_num_rows($rstat) ){
				$stat = ria_mysql_fetch_array( $rstat );
				$pts[0] = $stat['pts'];
			}

			$sponsor = 0;
			// Récupère les points gagnés par le parrain
			$rstat = stats_rewards_get( 0, $config['wst_id'], $rwa, $cls, $obj, false, false, $user_id );
			if( $rstat && ria_mysql_num_rows($rstat) ){
				$stat = ria_mysql_fetch_array( $rstat );

				$sponsor = $stat['usr'];
				$pts[1] = $stat['pts'];
			}

			// Annulation des points
			stats_rewards_add( 0, 0, $rwa, true, '', $cls, $obj, $pts, false, false, -1, $sponsor );
			break;
		}
		case 'RWA_CREATE_ACCOUNT' : { // Création d'un compte client
			if(isset($config['rwd_register_required']) && $config['rwd_register_required']){
				if(!rwd_users_is_enabled($obj[0])){
					return false;
				}
			}

			$rwa = rwd_actions_get_id_bycode( $code );
			$is_apply = rwd_actions_is_applicable( $rwa, $obj[0], $params );
			if( $is_apply!==true ){
				return false;
			}

			$r_exists = ria_mysql_query('
				select 1 from stats_rewards
				where stats_tnt_id = '.$config['tnt_id'].'
					and stats_usr_id = '.$obj[0].'
					and stats_rwa_id = '.$rwa.'
					and stats_date_deleted is null
			');

			if (!$r_exists) {
				return false;
			}

			if (!ria_mysql_num_rows($r_exists)) {
				stats_rewards_add( $obj[0], 0, $rwa, false, '' );
			}
			break;
		}
		case 'RWA_ADHESION' : {
			if(isset($config['rwd_register_required']) && $config['rwd_register_required']){
				if(!rwd_users_is_enabled($obj[0])){
					return false;
				}
			}

			$rwa = rwd_actions_get_id_bycode( $code );
			$is_apply = rwd_actions_is_applicable( $rwa, $obj[0], $params );
			if( $is_apply!==true ){
				return false;
			}

			$r_exists = ria_mysql_query('
				select 1 from stats_rewards
				where stats_tnt_id = '.$config['tnt_id'].'
					and stats_usr_id = '.$obj[0].'
					and (
						stats_rwa_id = '.$rwa.'
						'.($config['tnt_id'] == 13 ? ' or stats_action = "ADHESION"  or stats_action = "CREATION"' : '' ).'
					)
					and stats_date_deleted is null
			');

			if (!$r_exists) {
				return false;
			}

			if (!ria_mysql_num_rows($r_exists)) {
				stats_rewards_add( $obj[0], 0, $rwa, false, '' );
			}
			break;
		}
		case 'RWA_NLR_DESINSCRIPT' : {// Desincription à la newsletter
			$cancel = true;
			$code = 'RWA_NLR_INSCRIPT';
		}
		case 'RWC_CANCEL_BIRTHDAY_UPD' : { // Supprime sa date de naissance
			$cancel = true;
			$code = 'RWC_BIRTHDAY_UPD';
		}
		case 'RWA_MANAGE_GIFT': { // Commande contenant des articles achetable avec points de fidélité (hors commande Web)
			//Vérifie que les points de fidélités n'ont pas déja été débité
			if( stats_rewards_objs_exists(CLS_ORDER, $obj[0], false, true, 'Achats d\'articles via des points de fidélité') ){
				return true;
			}

			// Récupère les informations sur la commande
			$rord = ord_orders_get_simple( array( 'id' => $obj[0]), array(), array( 'is_web' => false) );
			if( !$rord || !ria_mysql_num_rows($rord) ){
				return false;
			}

			$order = ria_mysql_fetch_array( $rord );

			// Récupère les informations sur le client de la commande
			if( !$order['usr_id'] ){
				return false;
			}
			$ruser = gu_users_get( $order['usr_id'] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return false;
			}

			$user = ria_mysql_fetch_array( $ruser );

			// Récupère les produits appartenant au catalogue du programme de fidélité
			$rrwd_prd = rwd_catalogs_get($user['prf_id']);
			if( !$rrwd_prd || !ria_mysql_num_rows($rrwd_prd) ){
				return false;
			}

			$reward_products = array();
			// Construit un tableau ( id => nbPoint) contenant les produits appartenant au catalogue du programme de fidélité
			while( $rwd_prd = ria_mysql_fetch_assoc($rrwd_prd) ){
				$reward_products[$rwd_prd['prd_id']] = $rwd_prd['rwc_sell_points'];
			}

			$pts = 0;

			$r_ord_prd = ord_products_get($obj[0]);
			if ($r_ord_prd) {
				while ($ord_prd = ria_mysql_fetch_assoc($r_ord_prd)) {
					if (array_key_exists($ord_prd['id'], $reward_products)) {
						$pts += $reward_products[$ord_prd['id']] * $ord_prd['qte'];
					}
				}
			}

			if( $pts ){
				stats_rewards_add( $user['id'], $user['prf_id'], 0, true, 'Achats d\'articles via des points de fidélité', CLS_ORDER, $obj[0], $pts );
			}

			break;
		}
		default : {
			$user_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ? $_SESSION['admin_view_user'] : ( isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0 );

			if (array_key_exists('forced_user_id', $params)) {
				$user_id = $params['forced_user_id'];
			}

			if( !isset($user_id) ){
				return false;
			}

			if(isset($config['rwd_register_required']) && $config['rwd_register_required']){
				if(!rwd_users_is_enabled($user_id)){
					return false;
				}
			}

			$rwa = rwd_actions_get_id_bycode( $code );
			if( !$cancel ){
				$is_apply = rwd_actions_is_applicable( $rwa, $user_id, $params );
				if( $is_apply!==true ){
					return false;
				}
			}

			stats_rewards_add( $user_id, 0, $rwa, $cancel, '', $cls, $obj );
			break;
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'attribuer la récompense d'un parrainage au parrain.
 *	@param $cls Identifiant d'une classe d'objet
 *	@param $cfg Configuration du programme de fidélité à utiliser
 *	@param int $usr Identifiant du compte client parrain
 *	@param $obj Identifiant de l'objet déclanchant des points (commande ou facture)
 *	@param $params Paramètre liés à cette offre
 */

function rwd_sponsor_reward( $cls, $cfg, $usr, $obj, $params ){
	global $config;

	if( $cfg['sp_system'] == _RWD_SP_SYS_POINTS ){
		if( stats_rewards_objs_exists( $cls, $obj, false, false, 'Parrainage d\'une commande') ){
			return true;
		}

		$days_limit = is_numeric($cfg['sp_cod_days']) && $cfg['sp_cod_days']>=0 ? $cfg['sp_cod_days'] : -1;
		$res = stats_rewards_add( $usr, $params['prf'], 0, false,'Parrainage d\'une commande', CLS_ORDER, $obj[0], $cfg['pts'], false, false, $days_limit );

		$r_order = ord_orders_get_with_adresses( 0, $obj[0] );
		if( !$r_order || !ria_mysql_num_rows($r_order) ){
			return false;
		}

		$order = ria_mysql_fetch_assoc( $r_order );

		// Gestion de début et de la fin de la promotion
		$date_start = date('Y-m-d H:i:s');
		$date_stop = null;
		if ($days_limit > 0) {
			$date_stop = date( 'Y-m-d 23:59:59', strtotime('+'.$cfg['sp_cod_days'].' days') );
		}

		$r_cfg_email = cfg_emails_get( 'alert-rewards' );
		if( $r_cfg_email && ria_mysql_num_rows($r_cfg_email) ){
			$cfg_email = ria_mysql_fetch_assoc( $r_cfg_email );

			$usr_sponsor = ria_mysql_fetch_assoc( gu_users_get($usr) );
			$email = new Email();
			$email->setSubject( 'Commande d\'un filleul '.$config['site_name'] );
			$email->setFrom( $cfg_email['from'] );
			$email->addTo( $usr_sponsor['email'] );

			if( $cfg_email['bcc'] ){
				$email->addBcc( $cfg_email['bcc'] );
			}

			if( $cfg_email['reply-to'] ){
				$email->setReplyTo( $cfg_email['reply-to'] );
			}

			if( isset($config['email_html_header']) && trim($config['email_html_header'])!='' ){
				$email->addHtml( $config['email_html_header'] );
			}

			$user_name = '';
			switch( $usr_sponsor['title_name'] ){
				case 'Monsieur' :
					$user_name = 'Cher Monsieur ';
					break;
				case 'Madame' :
					$user_name = 'Chère Madame ';
					break;
				case 'Mademoiselle' :
					$user_name = 'Chère Mademoiselle ';
					break;
				default :
					$user_name = 'Bonjour ';
					break;
			}

			$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=loyalty-program-sp-pmt';

			$email->addParagraph( $user_name.' '.trim( $usr_sponsor['adr_firstname'].' '.$usr_sponsor['adr_lastname'].' '.$usr_sponsor['society'] ).', ' );
			$email->addParagraph('Suite à la commande de votre filleuil '.htmlspecialchars( trim($order['inv_firstname'].' '.$order['inv_lastname']) ).', nous avons le plaisir de vous offrir '.$cfg['pts'].' points de fidélité'.( $date_stop !== null  ? ' valables jusqu\'au '.date('d/m/Y à H:i', strtotime($date_stop)) : '' ).'.');
			$email->addParagraph('Toute l\'équipe '.$config['site_name'].' se tient à votre entière disposition pour toute question.');
			$email->addParagraph('À très bientôt sur <a href="'.$config['site_url'].$analytics.'">'.$config['site_url'].'</a>');

			if( isset($config['email_html_footer']) && trim($config['email_html_footer'])!='' ){
				$email->addHtml( $config['email_html_footer'] );
			}

			if( !$email->send() ){
				return false;
			}
		}

	}else{
		$res = true;

		$already_send = fld_object_values_get( $obj[0], _FLD_ORD_SPONSOR, '', false, true );
		if (in_array($already_send, array('oui', 'Oui', '1'))) {
			return true;
		}

		$r_order = ord_orders_get_with_adresses( 0, $obj[0] );
		if( !$r_order || !ria_mysql_num_rows($r_order) ){
			return false;
		}

		$order = ria_mysql_fetch_assoc( $r_order );

		// Récupère le pourcentage de cette commande
		$ord_total_ht = $order['total_ht'] * ( $cfg['sp_ord_pourcent'] / 100 );

		// Gestion de début et de la fin de la promotion
		$date_start = date('Y-m-d H:i:s');
		$date_stop = null;
		if( $cfg['sp_cod_days']>0 ){
			$date_stop = date( 'Y-m-d 23:59:59', strtotime('+'.$cfg['sp_cod_days'].' days') );
		}

		// Génère le code de parrainage
		$code = pmt_codes_generated();
		if( trim($code)=='' ){
			return false;
		}

		// Création de la promotion
		$desc = 'Code généré suite à une commande filleul n°'.$obj[0].'.';
		$pmt = pmt_codes_add( '', _PMT_TYPE_REWARD, $code, $desc );
		if( !$pmt ){
			return false;
		}

		// Création du bénéfice de la promotion (x% sur la commande)
		if( !pmt_offers_add($pmt, _PMT_TYPE_REWARD, $ord_total_ht, 0, '', 0, 0, 0, 'order', $date_start, $date_stop, 1, false, false, true) ){
			ria_mysql_query('delete from pmt_codes where cod_tnt_id='.$config['tnt_id'].' and cod_id='.$pmt);
			return false;
		}

		// Copy les règles d'inclusion et d'exclusion
		if( !pmt_codes_set_all_catalog($pmt, true) || !pmt_codes_set_all_customers($pmt, false) || !pmt_users_add($pmt, $usr, true) ){
			$res = false;
		}

		if( !$res ){
			pmt_codes_del( $pmt );
			return false;
		}

		fld_object_values_set( $obj[0], _FLD_ORD_SPONSOR, 'Oui' );

		$r_cfg_email = cfg_emails_get( 'alert-rewards' );
		if( $r_cfg_email && ria_mysql_num_rows($r_cfg_email) ){
			$cfg_email = ria_mysql_fetch_assoc( $r_cfg_email );

			$usr_sponsor = ria_mysql_fetch_assoc( gu_users_get($usr) );
			// Envoi un email avec le code promotion
			$email = new Email();
			$email->setSubject( 'Votre bon d\'achat '.$config['site_name'] );
			$email->setFrom( $cfg_email['from'] );
			$email->addTo( $usr_sponsor['email'] );

			if( $cfg_email['bcc'] ){
				$email->addBcc( $cfg_email['bcc'] );
			}

			if( $cfg_email['reply-to'] ){
				$email->setReplyTo( $cfg_email['reply-to'] );
			}

			if( isset($config['email_html_header']) && trim($config['email_html_header'])!='' ){
				$email->addHtml( $config['email_html_header'] );
			}

			$user_name = '';
			switch( $usr_sponsor['title_name'] ){
				case 'Monsieur' :
					$user_name = 'Cher Monsieur ';
					break;
				case 'Madame' :
					$user_name = 'Chère Madame ';
					break;
				case 'Mademoiselle' :
					$user_name = 'Chère Mademoiselle ';
					break;
				default :
					$user_name = 'Bonjour ';
					break;
			}

			$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=loyalty-program-sp-pmt';

			$email->addParagraph( $user_name.' '.trim( $usr_sponsor['adr_firstname'].' '.$usr_sponsor['adr_lastname'].' '.$usr_sponsor['society'] ).', ' );
			$email->addParagraph('Suite à la commande de votre filleuil '.htmlspecialchars( trim($order['inv_firstname'].' '.$order['inv_lastname']) ).', nous avons le plaisir de vous adresser votre code de réduction dans le cadre du programme de parrainage '.$config['site_name'].' : '.pmt_codes_get_code($pmt).'. Ce code vous offre une remise de '.number_format( ($ord_total_ht * _TVA_RATE_DEFAULT), 2 , '.', ' ' ).'€ valable jusqu\'au '.date('d/m/Y à H:i', strtotime($date_stop)).'.');
			$email->addParagraph('Toute l\'équipe '.$config['site_name'].' se tient à votre entière disposition pour toute question.');
			$email->addParagraph('À très bientôt sur <a href="'.$config['site_url'].$analytics.'">'.$config['site_url'].'</a>');

			if( isset($config['email_html_footer']) && trim($config['email_html_footer'])!='' ){
				$email->addHtml( $config['email_html_footer'] );
			}

			if( !$email->send() ){
				return false;
			}
		}
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer toutes les actions pouvant faire l'objet de points de fidélité.
 *	@param $rwa Optionnel, identifiant d'une action
 *	@return bool false si le paramètre est faux ou si une erreur s'est produite
 *	@return resource Un résultat MySQL contenant :
 *			- id : identifiant de l'action
 *			- name : nom de l'action
 *			- desc : description de l'action
 *			- for-sp : si oui ou non des points de fidélité peuvent être attribué au parrain
 */
function rwd_actions_get( $rwa=0 ){
	if( !is_numeric($rwa) || $rwa<0 ){
		return false;
	}

	$sql = '
		select rwa_id as id, rwa_code as code, rwa_name as name, rwa_desc as "desc", rwa_sponsor as "for-sp"
		from rwd_actions
		where 1
	';

	if( $rwa>0 ){
		$sql .= ' and rwa_id='.$rwa;
	}

	$sql .= ' order by rwa_pos asc';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nom d'une action.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@return bool False si l'action n'existe pas ou si le paramètre est faux ou omis
 *	@return string Le nom de l'action
 */
function rwd_actions_get_name( $rwa ){
	if( !is_numeric($rwa) || $rwa<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwa_name as name
		from rwd_actions
		where rwa_id='.$rwa.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant d'une action par son code
 *	@param $code Obligatoire, code de l'action
 *	@return bool False si l'action n'existe pas, sinon l'identifiant de l'action
 */
function rwd_actions_get_id_bycode( $code ){
	if( !trim($code) ){
		return false;
	}

	$sql = '
		select rwa_id as id
		from rwd_actions
		where rwa_code=\''.addslashes( strtoupper($code) ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le code d'une action.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@return bool False si l'action n'existe pas ou si le paramètre est faux ou omis
 *	@return string Le nom de l'action
 */
function rwd_actions_get_code( $rwa ){
	if( !is_numeric($rwa) || $rwa<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rwa_code as code
		from rwd_actions
		where rwa_id='.$rwa.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'code' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un tableau contenant les points de fidélité pour l'internaute et son parrain.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@param int $prf Obligatoire, ientifiant d'un profil de compte client
 *	@return bool False si l'action n'existe pas ou bien si une erreur se produit dans la récupération des points
 *	@return array Un tableau contenant : array( points pour l'internaute, points pour le parrain - peut être égal à 0 )
 */
function rwd_actions_get_points( $rwa, $prf ){
	if( !is_numeric($rwa) || $rwa<=0 ){
		return false;
	}
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select cfg_points as pts, if(ifnull(cfg_sp_points, 0)>0, cfg_sp_points, 0) as sp_pts
		from rwd_actions_configs
		where cfg_tnt_id='.$config['tnt_id'].' and cfg_wst_id='.$config['wst_id'].'
			and cfg_rwa_id='.$rwa.'
			and cfg_prf_id='.$prf.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return array( $r['pts'], $r['sp_pts'] );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une action action existe.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@return bool True si l'action existe, False dans le cas contraire
 */
function rwd_actions_exists( $rwa ){
	if( !is_numeric($rwa) || $rwa<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_actions
		where rwa_id='.$rwa.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si une action peut être soumises à des conditions spéciales.
 *	@param $rwa Obligatoire, identifiant d'une action
 *	@return bool True si des conditions existes pour une action, False dans le cas contraire
 */
function rwd_actions_have_conditions( $rwa ){
	if( !is_numeric($rwa) || $rwa<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_action_conditions
		where rwc_rwa_id='.$rwa.'
		limit 0, 1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une ou plusieurs conditions d'application.
 *	@param $rwa Optionnel, permet de filtrer selon une action
 *	@param $rwc Optionnel, identifiant d'une condition
 *	@return bool false si l'un des paramètres est faux ou si une erreur s'est produite
 *	@return resource Un résultat MySQL contenant :
 *			- id : identifiant de la condition
 *			- code : code de la condition
 *			- name : nom de la condition
 *			- desc : description de la condition
 *			- precision : nombre de chiffre après la virgule dans le cas où l'on attend une valeur décimal
 *			- type : identifiant du type de données attendues
 */
function rwd_action_conditions_get( $rwa=0, $rwc=0 ){
	if( !is_numeric($rwa) || $rwa<0 ){
		return false;
	}

	if( !is_numeric($rwc) || $rwc<0 ){
		return false;
	}

	$sql = '
		select rwc_id as id, rwc_code as code, rwc_name as name, rwc_desc as "desc", rwc_precision as "precision", rwc_type as type,
		rwc_rwa_id as rwa
		from rwd_action_conditions
		where 1
	';

	if( $rwa>0 ){
		$sql .= ' and rwc_rwa_id='.$rwa;
	}

	if( $rwc>0 ){
		$sql .= ' and rwc_id='.$rwc;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant d'une condition selon son code.
 *	@param $code Obligatoire, code de la condition d'action
 *	@return bool False si la condition n'existe pas, sinon son identifiant
 */
function rwd_action_conditions_get_id_bycode( $code ){
	if( !trim($code) ){
		return false;
	}

	$sql = '
		select rwc_id as id
		from rwd_action_conditions
		where rwc_code = \''.addslashes( $code ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si une condition existe.
 *	@param $rwc Obligatoire, identifiant d'une condition
 *	@return bool false si le paramètre est omis ou faux, True si la condition existe
 */
function rwd_action_conditions_exists( $rwc ){
	if( !is_numeric($rwc) || $rwc<=0 ){
		return false;
	}

	$sql = '
		select 1
		from rwd_action_conditions
		where rwc_id='.$rwc.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une configuration d'une action.
 *	@param $rwa Obligatoire, identifiant d'une condition
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site (par défaut au prend celui qui est stocké dans $config['wst_id'])
 *	@param $pts Optionnel, nombre de points gagnés propre à cette condition
 *	@param $sp_pts Optionnel, nombre de points gagnés par le parrain propre à cette condition
 *	@return bool false si l'insertion a échouée, sinon l'identifiant de la nouvelle configuration de condition
 */
function rwd_actions_configs_add( $rwa, $prf, $wst=0, $pts=0, $sp_pts=0 ){
	if( !rwd_actions_exists($rwa) ){
		return false;
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	if( !is_numeric($sp_pts) || $sp_pts<0 ){
		return false;
	}

	global $config;

	$wst = $wst>0 && wst_websites_exists($wst) ? $wst : $config['wst_id'];

	if( ($cfg = rwd_actions_configs_exists( 0, $prf, $wst, $rwa)) ){
		return rwd_actions_configs_update( $cfg, $pts, $sp_pts );
	}

	$pts = $pts>0 ? $pts : 'null';
	$sp_pts = $sp_pts>0 ? $sp_pts : 'null';

	$sql = '
		insert into rwd_actions_configs
			( cfg_tnt_id, cfg_rwa_id, cfg_points, cfg_sp_points, cfg_wst_id, cfg_prf_id )
		values
			( '.$config['tnt_id'].', '.$rwa.', '.$pts.', '.$sp_pts.', '.$wst.', '.$prf.' )
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une configuration d'une action existe.
 *	@param $cfg Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil de compte client
 *	@param int $wst Optionnel, identifiant d'un site (par défaut on prends celui stocké dans $config['wst_id'])
 *	@param $rwa Optionnel, identifiant d'une action
 *	@return bool True si elle existe, False dans le cas contraire
 */
function rwd_actions_configs_exists( $cfg=0, $prf=0, $wst=0, $rwa=0 ){
	if( !is_numeric($cfg) || $cfg<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($rwa) || $rwa<0 ){
		return false;
	}

	if( $cfg<=0 && !($prf && $wst && $rwa) ){
		return false;
	}

	global $config;

	$wst = $wst>0 ? $wst : $config['wst_id'];

	$sql = '
		select cfg_id as id
		from rwd_actions_configs
		where cfg_tnt_id='.$config['tnt_id'].'
	';

	if( $cfg>0 ){
		$sql .= ' and cfg_id='.$cfg;
	}

	if( $prf>0 ){
		$sql .= ' and cfg_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and cfg_wst_id='.$wst;
	}

	if( $rwa>0 ){
		$sql .= ' and cfg_rwa_id='.$rwa;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une configuration pour une action.
 *	@param int $prf Obligatoire, profil d'un compte client
 *	@param int $wst Optionnel, identifiant d'un site par défaut on prendra celui stocké dans $config['wst_id']
 *	@param $cfg Optionnel, identifiant d'une configuraiton
 *	@param $rwa Optionnel, identifiant d'une action
 *	@param $code_rwa Optionnel, code le l'action
 *	@return bool false si une erreur se produit lors de la récupération, sinon un résultat MySQL contenant :
 *				- rwa : identifiant d'une action
 *				- pts : points pour l'internaute
 *				- sp_pts : points pour le sponsor
 */
function rwd_actions_configs_get( $prf, $wst=0, $cfg=0, $rwa=0, $code_rwa='' ){
	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($cfg) || $cfg<0 ){
		return false;
	}

	if( !is_numeric($rwa) || $rwa<0 ){
		return false;
	}

	global $config;

	if( $wst>0 && !wst_websites_exists($wst) ){
		return false;
	}

	$wst = $wst>0 ? $wst : $config['wst_id'];

	$sql = '
		select cfg_id as id, cfg_rwa_id as rwa, cfg_points as pts, cfg_sp_points as sp_pts
		from rwd_actions_configs
	';

	if( trim($code_rwa) ){
		$sql .= ' join rwd_actions on ( cfg_rwa_id=rwa_id )';
	}

	$sql .= '
		where cfg_tnt_id='.$config['tnt_id'].'
			and cfg_prf_id='.$prf.' and cfg_wst_id='.$wst.'
	';

	if( $cfg>0 ){
		$sql .= ' and cfg_id='.$cfg;
	}

	if( $rwa>0 ){
		$sql .= ' and cfg_rwa_id='.$rwa;
	}

	if( trim($code_rwa) ){
		$sql .= ' and rwa_code=\''.addslashes( $code_rwa ).'\'';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour une configuration d'une action.
 *	@param $cfg Obligatoire, identifiant d'une configuration
 *	@param $pts Optionnel, nombre de points gagnés propre à cette condition
 *	@param $sp_pts Optionnel, nombre de points gagnés par le parrain propre à cette condition
 *	@return bool True si la mise à jour s'est correctement déroulée, false dans le cas contraire
 */
function rwd_actions_configs_update( $cfg, $pts=0, $sp_pts=0 ){
	if( !is_numeric($cfg) || $cfg<=0 ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	if( !is_numeric($sp_pts) || $sp_pts<0 ){
		return false;
	}


	global $config;

	$pts = $pts>0 ? $pts : 'null';
	$sp_pts = $sp_pts>0 ? $sp_pts : 'null';

	$sql = '
		update rwd_actions_configs
		set cfg_points='.$pts.', cfg_sp_points='.$sp_pts.'
		where cfg_tnt_id='.$config['tnt_id'].' and cfg_id='.$cfg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une configuration de condition d'une action.
 *	@param $cfg Obligatoire, identifiant d'une configuration
 *	@return bool True si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function rwd_actions_configs_del( $cfg ){
	if( !is_numeric($cfg) || $cfg<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		delete from rwd_actions_configs
		where cfg_tnt_id='.$config['tnt_id'].'
			and cfg_id='.$cfg.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une conditions à une action.
 *	@param $rwc Obligatoire, identifiant d'une condition
 *	@param int $prf Oblitoire, identifiant d'un profil
 *	@param $val Obligatoire, valeur pour cette condition
 *	@param int $wst Optionnel, identifiant d'un site par défaut on prendra celui stocké dans $config['wst_id']
 *	@param $pts Optionnel, points gagnés par l'internaute pour cette condition
 *	@param $sp_pts Optionnel, points gagnés par le parrain pour cette condition
 *	@return bool False si l'insertion a échouée, True dans le cas contraire
 */
function rwd_action_conditions_configs_add( $rwc, $prf, $val, $wst=0, $pts=0, $sp_pts=0 ){
	if( !rwd_action_conditions_exists($rwc) ){
		return false;
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( !trim($val) && $val!=0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	if( !is_numeric($sp_pts) || $sp_pts<0 ){
		return false;
	}

	global $config;

	if( $wst>0 && !wst_websites_exists($wst) ){
		return false;
	}

	$wst = $wst>0 ? $wst : $config['wst_id'];

	if( ($id = rwd_action_conditions_configs_exists(0, $prf, $wst, $rwc)) ){
		return rwd_action_conditions_configs_update( $id, $val, $pts=0, $sp_pts=0 );
	}

	$sql = '
		insert into rwd_action_conditions_configs
			( cfg_tnt_id, cfg_rwc_id, cfg_value, cfg_prf_id, cfg_wst_id, cfg_points, cfg_sp_points )
		values
			( '.$config['tnt_id'].', '.$rwc.', \''.addslashes($val).'\', '.$prf.', '.$wst.', '.( $pts>0 ? $pts : 'null' ).', '.( $sp_pts>0 ? $sp_pts : 'null' ).')
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si une configuration d'une condition n'existe pas déjà.
 *	Soit l'identifiant est passé en paramètre soi le profil et le site, dans le cas contraire la fonctionne retournera False.
 *	@param $cfg Optionnel, identifiant d'une configuration
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@param $rwc Optionnel, identifiant d'une condition
 *	@return bool False si la configuration n'existe pas, l'identifiant de la configuration dans le cas contraire
 */
function rwd_action_conditions_configs_exists( $cfg=0, $prf=0, $wst=0, $rwc=0 ){
	if( !is_numeric($cfg) || $cfg<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if( !is_numeric($rwc) || $rwc<0 ){
		return false;
	}

	if( $cfg<=0 && !($prf && $wst && $rwc) ){
		return false;
	}

	global $config;

	$sql = '
		select cfg_id as id
		from rwd_action_conditions_configs
		where cfg_tnt_id='.$config['tnt_id'].'
	';

	if( $cfg>0 ){
		$sql .= ' and cfg_id='.$cfg;
	}

	if( $prf>0 ){
		$sql .= ' and cfg_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and cfg_wst_id='.$wst;
	}

	if( $rwc>0 ){
		$sql .= ' and cfg_rwc_id='.$rwc;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour une configuraiton d'une condition.
 *	@param $cfg Obligatoire, identifiant d'une configuration
 *	@param $val Optionnel, nouvelle valeur
 *	@param $pts Optionnel, nombre de points gagnés propre à cette condition
 *	@param $sp_pts Optionnel, nombre de points gagnés par le parrain propre à cette condition
 *	@return bool False si la mise à jour a échouée, True dans le cas contraire
 */
function rwd_action_conditions_configs_update( $cfg, $val, $pts=0, $sp_pts=0 ){
	if( !is_numeric($cfg) || $cfg<=0 ){
		return false;
	}

	if( !trim($val) && $val!=0 ){
		return false;
	}

	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}

	if( !is_numeric($sp_pts) || $sp_pts<0 ){
		return false;
	}

	global $config;

	$sql = '
		update rwd_action_conditions_configs
		set cfg_value=\''.addslashes($val).'\', cfg_points='.( $pts>0 ? $pts : 'null' ).', cfg_sp_points='.( $sp_pts>0 ? $sp_pts : 'null' ).'
		where cfg_tnt_id='.$config['tnt_id'].'
			and cfg_id='.$cfg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une configuration d'une condition.
 *	@param $cfg Obligatoire, identifiant d'une configuration
 *	@return bool False en cas d'échec de suppresion, True dans le cas contraire
 */
function rwd_action_conditions_configs_del( $cfg ){
	if( !is_numeric($cfg) || $cfg<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from rwd_action_conditions_configs
		where cfg_tnt_id='.$config['tnt_id'].'
			and cfg_id='.$cfg.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de charger la configuration d'une ou plusieurs conditions.
 *	@param $cfg Optionnel, identifiant d'une configuration
 *	@param $rwc Optionnel, identifiant d'une action
 *	@param $code_rwc Optionnel, code d'une condition
 *	@param $rwa Optionnel, identifiant d'une condition
 *	@param int $prf Optionnel, identifiant d'un profil
 *	@param int $wst Optionnel, identifiant d'un site
 *	@param $sort Optionnel, tableau contenant les critères de tri à appliquer au résultat, sous la forme colonne=>direction. Seule la colonne value peut servir actuellement de tri.
 *	@param $val Optionnel, valeur de la configuration d'action
 *	@return bool False si une erreur s'est produite lors de la récupération, sinon un résultat MySQL contenant :
 *			- id : identifiant d'une configuration
 *			- rwc : identifiant d'une condition
 *			- val : valeur de la condition
 *			- wst : identifiant d'un site
 *			- prf : identifiant d'un profil
 *			- type : type de donnée attendue comme valeur (entier, nombre à virgule, booléen...)
 *			- pts : nombre de points gagnés par l'internaute pour cette condition
 *			- sp_pts : nombre de points gagnés par le parrain pour cette condition
 */
function rwd_action_conditions_configs_get( $cfg=0, $rwc=0, $code_rwc='', $rwa=0, $prf=0, $wst=0, $sort=false, $val='' ){
	if( !is_numeric($cfg) || $cfg<0 ){
		return false;
	}

	if( !is_numeric($rwc) || $rwc<0 ){
		return false;
	}

	if( !is_numeric($rwa) || $rwa<0 ){
		return false;
	}

	if( !is_numeric($prf) || $prf<0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	global $config;

	if( $prf>0 && !gu_profiles_exists($prf) ){
		return false;
	}

	if( $wst>0 && !wst_websites_exists($wst) ){
		return false;
	}


	$sql = '
		select cfg_id as id, cfg_rwc_id as rwc, cfg_value as val, cfg_wst_id as wst, cfg_prf_id as prf, cfg_points as pts, cfg_sp_points as sp_pts, rwc_type as type
		from rwd_action_conditions_configs
			join rwd_action_conditions on ( cfg_rwc_id=rwc_id )
		where cfg_tnt_id='.$config['tnt_id'].'
	';

	if( $cfg>0 ){
		$sql .= ' and cfg_id='.$cfg;
	}

	if( $rwc>0 ){
		$sql .= ' and cfg_rwc_id='.$rwc;
	}

	if( trim($code_rwc)!=='' ){
		$sql .= ' and rwc_code=\''.addslashes( $code_rwc ).'\'';
	}

	if( $rwa>0 ){
		$sql .= ' and rwc_rwa_id='.$rwa;
	}

	if( $prf>0 ){
		$sql .= ' and cfg_prf_id='.$prf;
	}

	if( $wst>0 ){
		$sql .= ' and cfg_wst_id='.$wst;
	}

	if( trim($val) ){
		$sql .= ' and cfg_value=\''.addslashes($val).'\'';
	}

	if( $sort && is_array($sort) && sizeof($sort) ){
		$sort_final = array();

		foreach( $sort as $col=>$dir ){
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'value' :
					array_push( $sort_final, 'if(convert(cfg_value, unsigned) is null, 0, convert(cfg_value, unsigned)) '.$dir );
					break;
			}
		}

		if( sizeof( $sort_final) ){
			$sql .= ' order by '.implode( ', ', $sort_final );
		}
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner le solde des points de fidélité.
 *	@param $prf_id Obligatoire, Identifiant du profile client pour récupérer la configuration du système de points de fidélité
 *	@return Soit le nombre exacte de points, soit zéro points de fidélité
 */
function rwd_alert_usr( $prf_id ){
	if( $prf_id && $prf_id<=0 ){
		return false;
	}

	global $config;

	if( !$config['email_alerts_enabled'] ){
		return false;
	}

	// Récupère la configuration d'adresse mail
	$rcfg = cfg_emails_get( 'alert-rewards' );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array( $rcfg );

	// On récupère la configuration des points de fidélité pour le profile donné
	$days_before_lost = 0;
	$rwd = rwd_rewards_get( 0, $prf_id, $config['wst_id'] );
	if( $rwd && ria_mysql_num_rows($rwd) ){
		$reward = ria_mysql_fetch_array( $rwd );
		$days_before_lost = $reward['days_lost'];

	}

	// Si aucun délai de pré-alert n'est renseigné, on ne vas pas plus loin
	if($days_before_lost <= 0){
		return false;
	}

	// Date limite d'utilisation selon le délai de pré-alert
	$date_limited = date( 'Y-m-d', (time() + $days_before_lost * 86400) );
	$days_before_lost = $days_before_lost - 1;

	if( $reward['system']==_RWD_SYSTEM_CODE ){

		// Récupère les codes promotions qui arrivent à échéance
		$rcod = pmt_codes_get( null, null, false, _PMT_TYPE_CODE, false, null, false, null, $date_limited );
		if( !$rcod || !ria_mysql_num_rows($rcod) ){
			return true;
		}

		while( $cod = ria_mysql_fetch_array($rcod) ){
			// Contrôle qu'il s'agit d'un code promotion obtenu grâce à des points de fidélité
			$sdate_cod = strtotime( dateparse($cod['date_stop']) );
			$sdate_stop = strtotime( dateparse($date_limited) );

			// Vérifie que la date de fin est la limite calculée
			if( $sdate_cod!=$sdate_stop ){
				continue;
			}

			// Contrôle qui s'agit d'un code promotion généré via les points de fidélité
			if( !preg_match('/(Code de fidélité généré pour)/i', $cod['desc']) ){
				continue;
			}

			// Récupère le compte client rattaché à ce code promotion
			$rusr_id = pmt_users_get( $cod['id'], true );
			if( !$rusr_id || !ria_mysql_num_rows($rusr_id) ){
				return false;
			}

			$usr_id = ria_mysql_result( $rusr_id, 0, 'id' );
			$rusr = gu_users_get( $usr_id );
			if( !$rusr || !ria_mysql_num_rows($rusr) ){
				return false;
			}

			$usr = ria_mysql_fetch_array( $rusr );

			// Récupère l'offre sur la promotion
			$roff = pmt_offers_get( $cod['id'] );
			if( !$roff || !ria_mysql_num_rows($roff) ){
				return false;
			}

			$off = ria_mysql_fetch_array( $roff );

			// Envoi un email avec le code promotion
			$email = new Email();
			$email->setSubject( 'Votre bon d\'achat '.$config['site_name'].' arrive à échéance' );
			$email->setFrom( $cfg['from'] );
			$email->addTo( $usr['email'] );

			if( $cfg['bcc'] ){
				$email->addBcc( $cfg['bcc'] );
			}

			if( $cfg['reply-to'] ){
				$email->setReplyTo( $cfg['reply-to'] );
			}

			if( isset($config['email_html_header']) && trim($config['email_html_header'])!='' ){
				$email->addHtml( $config['email_html_header'] );
			}

			$user_name = '';
			switch( $usr['title_name'] ){
				case 'Monsieur' :
					$user_name = 'Cher Monsieur ';
					break;
				case 'Madame' :
					$user_name = 'Chère Madame ';
					break;
				case 'Mademoiselle' :
					$user_name = 'Chère Mademoiselle ';
					break;
				default :
					$user_name = '';
					break;
			}

			if( trim($user_name)!='' ){
				$email->addParagraph( $user_name.' '.trim( $usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society'] ).', ' );
			} else {
				$email->addParagraph( trim( $usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society'] ).', ' );
			}

			$message = 'Pour vous remercier de votre fidélité, nous vous avons adressé un bon d’achat ';

			$discount = str_replace( ',00', '', number_format($off['discount'], '2', ',', ' ') );
			if( $off['discount_type']==0 ){
				$message .= 'd\'une valeur de '.$discount.' €.';
			} else {
				$message .= 'd\'une remise de '.$discount.' %.';
			}

			$email->addParagraph( $message );

			$email->addHtml( '<p>Ce bon d\'achat arrivant à expiration, vous ne pouvez l\'utiliser que jusqu\'au '.$cod['date_stop'].' inclus.Pour profiter de ce bon d’achat, saisissez le code suivant lors de votre prochaine commande : <b>'.strtoupper( $cod['code'] ).'</b>.</p>' );

			$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=loyalty-program-pmt-relance';
			$email->addhtml( 'Merci de votre confiance et à bientôt sur <a href="'.$config['site_url'].$analytics.'">'.$config['site_name'].'</a>.');

			if( isset($config['email_html_footer']) && trim($config['email_html_footer'])!='' ){
				$email->addHtml( $config['email_html_footer'] );
			}

			if( !$email->send() ){
				return false;
			}
		}
	}elseif( $reward['system']==_RWD_SYSTEM_POINTS ){
		// Récupère les statistique de points de fidélité pour le profile
		$stats = stats_rewards_get( 0,$config['wst_id'], 0, 0, false, false, false, 0, $date_limited, $prf_id );
		if( !$stats || !ria_mysql_num_rows($stats) ){
			return false;
		}

		// Créer un tableau contenant, pour chaque client, leur nombre de points
		$stats_array = array();
		while( $stat = ria_mysql_fetch_array($stats) ){
			if(!isset($stats_array[$stat['usr']])){
				$stats_array[ $stat['usr' ]] = $stat;
			}else{
				$stats_array[ $stat['usr'] ]['pts'] += $stat['pts'];
			}
		}

		$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=relance-points-fidelite';
		foreach($stats_array as $stats){
			// Vérifier que le compte client est inscript à la newsletter "Relance points de fidélité
			if( !isset($config['rwd_newsletter']) || !newsletter_is_inscripted($stats['email'], $config['rwd_newsletter']) ){
				continue;
			}

			// Solde des points disponible
			$pts = gu_users_get_rewards_balance($stats['usr']);
			if( $pts <= 0 ){
				continue;
			}

			$ptsu = 0;

			// Récupère le nombre de points dernièrement utilisé par le compte client
			$res = ria_mysql_query('
				select stats_points as points
				from stats_rewards
				where stats_tnt_id='.$config['tnt_id'].' and stats_wst_id='.$config['wst_id'].'
					and stats_datetime >= "'.addslashes( date('Y-m-d H:i:s', strtotime( '+1 hour', strtotime($stats['date_en']) )) ).'"
					and stats_usr_id='.$stats['usr'].'
					and stats_points<0
				order by stats_datetime asc
				limit 1
			');

			if( !$res ){
				return false;
			}

			$pts_used = 0;
			if( ria_mysql_num_rows($res) ){
				$pts_used = abs( ria_mysql_result($res, 0, 'points') );
			}

			// Vérifie qu'il reste des points de fidélité à utiliser avant la date limite
			if( $stats['pts'] + $ptsu > 0 && $stats['pts']>$pts_used ){
				$mail = new Email();
				$mail->setFrom( $cfg['from'] );
				$mail->addTo( $stats['email'] );
				$mail->addBcc( $cfg['bcc'] );
				$mail->setReplyTo( $cfg['reply-to'] );

				switch( $config['tnt_id'] ){
					case 13 :
						// Pierre Oteiza
						$mail->setSubject('Plus que 2 semaines pour utiliser vos Ttantto !');

						// Corps du mail mail
						$mail->addHtml( $config['email_html_header'] );

						$mail->addParagraph("Cher client,");
						$mail->addParagraph("Grâce à votre fidélité, vous disposez de " . $stats['pts'] . " Ttantto sur votre compte. Ceux-ci sont à utiliser avant le " . dateheureunparse($date_limited) . ", ou bien ils seront perdus !");
						$mail->addParagraph("N'attendez pas et profitez vite de vos avantages !");
						$mail->addParagraph("- sur <a href='http://www.pierreoteiza.com'>www.pierreoteiza.com</a>");
						$mail->addParagraph("- par téléphone au 05 59 37 56 11");
						$mail->addParagraph("- dans l'une de nos <a href='http://www.pierreoteiza.com/nos-magasins/'>boutiques Pierre Oteiza</a>");
						$mail->addParagraph("À très bientôt ! Laster arte !");
						$mail->addParagraph("Pierre Oteiza");

						$mail->addHtml( $config['email_html_footer'] );

						break;
					default :
						$mail->setSubject('Derniers jours pour profiter de vos points de fidélité');

						// Corps du mail mail
						$mail->addHtml( $config['email_html_header'] );

						$mail->addParagraph( (trim($stats['title_name'])!='' ? ($stats['title_name'] != 'Monsieur' ? 'Chère' : 'Cher') : 'Cher/Chère').' '.$stats['title_name'].' '.$stats['firstname'].' '.$stats['lastname'].',');
						$mail->addParagraph( 'Vous avez à l\'heure actuelle '.$pts.' points fidélité à utiliser sur notre site <a href="'.$config['site_url'].'/'.$analytics.'">'.$config['site_url'].'</a> et nous vous remercions de la confiance que vous nous accordez.' );

						$mail->addParagraph( 'A ce titre, nous tenions à vous informer que '.($ptsu == 0 ? '<span style="font-weight:bold">la totalité de vos points fidélité</span> expirait' : '<span style="font-weight:bold">'.($stats['pts'] + $ptsu). ' points fidélité</span> expiraient').' prochainement.');
						$mail->addParagraph( 'Afin qu\'ils ne soient pas perdus, nous vous invitons à passer commande sur notre site <a href="'.$config['site_url'].'/'.$analytics.'">'.$config['site_url'].'</a> avant le <span style="font-weight:bold">'.date( 'd/m/Y', (time() + $days_before_lost * 86400) ).' à 23h59<span>.' );
						$mail->addParagraph( 'Nous restons bien évidemment à votre disposition pour toute question, n\'hésitez-pas à <a href="'.$config['site_url'].$config['contact_page_url'].$analytics.'">nous contacter</a>.');
						$mail->addParagraph(' Au plaisir de vous retrouver prochainement sur <a href="'.$config['site_url'].'/'.$analytics.'">'.$config['site_url'].'</a>,');
						$mail->addParagraph(' L\'équipe '.$config['site_name'].'.');

						break;
				}

				if( !$mail->send() ){
					return false;
				}
			}
		}
	}

	return true;
}
// \endcond

/// @}

/**	\defgroup gu_sponsors Gestion des parrains
 *	\ingroup model_rewards
 *	Les fonctions de ce module permettent de définir le ou les parrains qui ont été en place pour un client, ainsi que gérer ses points de fidélité.
 *	@{
 */

/** Cette fonction permet d'envoyer les informations relative au parainnage a un filleul
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail ou tableau d'adresses mails des filleuls
 *	@param string $body Optionnel, message adressé au filleul
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 *
 */
function gu_sponsor_create( $usr, $email, $body='' ){
	$errors = array();

	if( !gu_users_exists($usr) ){
		return false;
	}

	global $config;

	if( !is_array($email) ){
		if( !isemail($email) ){
			return false;
		}

		$email = array( $email );
	}else{
		if( !sizeof($email) ){
			return false;
		}

		foreach( $email as $e ){
			if( !isemail($e) ){
				return false;
			}
		}
	}

	// Récupère le profile du compte parrain
	$prf = gu_users_get_prf( $usr );
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	$rrwd = rwd_rewards_get( 0, $prf, $config['wst_id'] );
	if( !$rrwd || !ria_mysql_num_rows($rrwd) ){
		return false;
	}
	$rwd = ria_mysql_result( $rrwd, 0, 'id' );

	$crwd = rwd_rewards_get_sponsor( $rwd );
	if( !is_array($crwd) || !sizeof($crwd) ){
		return false;
	}

	if((!isset($crwd['active']) || !$crwd['active'])
		||  $crwd['discount']<0
		||  $crwd['points']<0
		||  $crwd['days']<0
		||  $crwd['limit']<0)
	{
		return true;
	}

	$type = "";

	if($crwd['points']>0){
		$type = "points";
	}else if($crwd['discount']>0){
		$type = "promotions";
	}

	switch ($type) {
		case 'points':
			$res = gu_sponsor_points_create( $usr, $email, $body);
			break;

		case 'promotions':
			$res = gu_sponsor_promotions_create( $usr, $email, $body);
			break;

		default:
			$res = gu_sponsor_send_link($usr,$email,$body);
			break;
	}
	return $res;
}

/** Cette fonction permet de compter le nombre de parrainage en cours de validité selon les différents type.
 *	@param int $usr Obligatoire, identifiant d'un parrain
 *	@return Le nombre de parrainage
 */
function gu_sponsor_count( $usr ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	$count = gu_sponsor_promotions_count($usr);
	$count = $count + gu_sponsor_points_count($usr);

	return $count;
}

// \cond onlyria
/** Cette fonction permet de rattacher un parrainage avec points sur un parrain.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param string $email Obligatoire, adresse mail du filleul destinataire du code de parrainage
 *	@param $days Obligatoire, durée de validité en jours de l'offre de parainage
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsor_points_add( $usr, $email, $days=0){
	if( !gu_users_exists($usr) ){
		return false;
	}

	if( !isemail($email) ){
		return false;
	}

	if( !is_numeric($days) || $days <0 ){
		return false;
	}

	if(gu_sponsor_points_exists($usr, $email)){
		return false;
	}

	global $config;

	$sql = '
		insert into gu_sponsor_points
			( spp_tnt_id, spp_usr_id, spp_email, spp_date_created, spp_date_limited  )
		values
			( '.$config['tnt_id'].', '.$usr.', \''.addslashes($email).'\', now(), '.($days > 0 ? "now() + interval ". $days ." day"  : "null" ) .')
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un parainage par points offert au filleul bien été envoyé à un filleul.
 *	Le code de parrainage doit toujours être actif pour que la fonction retourne True.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail du filleul
 *	@return bool True si un code de parrainage a été émis, False dans le cas contraire
 */
function gu_sponsor_points_exists( $usr, $email ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !isemail($email) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_sponsor_points
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_email=\''.addslashes($email).'\'
			and (spp_date_limited is null || spp_date_limited >= now())
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

/** Cette fonction permet de récupérer les informations sur un ou plusieurs parainage avec points
 *	@param int $usr Optionnel, identifiant d'un compte parrain
 *	@return resource Un résultat MySQL contenant :
 *				- usr : identifiant du compte parrain
 *				- email : adresse mail du filleul
 *				- valid : si le code de parrainage est toujours utilisable
 */
function gu_sponsor_points_get( $usr=0){
	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			spp_usr_id as usr, spp_email as email, spp_used as used,
			if( ifnull(spp_date_limited, \'\')=\'\' or date(spp_date_limited)>=now(), 1, 0 ) as valid
		from gu_sponsor_points
		where spp_tnt_id='.$config['tnt_id'].'
	';

	if( $usr>0 ){
		$sql .= ' and spp_usr_id='.$usr;
	}


	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de compter le nombre de parrainage en cours de validité par points.
 *	@param int $usr Obligatoire, identifiant d'un parrain
 *	@return Le nombre de parrainage
 */
function gu_sponsor_points_count( $usr ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select count(*) as total
		from gu_sponsor_points
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and (ifnull(spp_date_limited, \'\')=\'\' or date(spp_date_limited)>=now()) and spp_used=0
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 'total' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information d'utilisation du code de parrainage de point.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, email du filleul
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsor_points_set_used( $usr, $email ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !isemail($email)){
		return false;
	}

	global $config;

	$sql = '
		update gu_sponsor_points
		set spp_used=1
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_email="'.$email.'"
	';

	return ria_mysql_query( $sql );
}
// \endcond

/** Cette fonction permet de vérifier qu'un code de parrainage est déjà utilisé.
 *	@param int $usr Obligatoire, identifiant d'un compte parrain
 *	@param string $email Obligatoire, adresse mail d'un compte filleul
 *	@return bool True si le code est déjà utilisé, False dans le cas contraire
 */
function gu_sponsor_points_is_used( $usr, $email ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( trim($email)!='' && !isemail($email) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_sponsor_points
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_email="'.$email.'"
			and spp_used=1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlyria
/** Cette fonction permet de retourner l'identifiant du compte parrain donnant les points.
 *	@param string $email Obligatoire, email du filleul
 *	@return int L'identifiant du parrain rattaché au code de parrainage, False s'il n'existe pas
 */
function gu_sponsor_points_get_usr_id( $email){
	if( !isemail($email)){
		return false;
	}

	global $config;

	$sql = '
		select spp_usr_id as usr
		from gu_sponsor_points
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_email="'.$email.'"
		    and (spp_date_limited is null || spp_date_limited >= now())
	';

	$res = ria_mysql_query( $sql );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'usr' );
}
// \endcond

/** Cette fonction permet d'envoyer le code de parrainage, après l'avoir créer, à un ou plusieurs filleuls.
 *	Le code de parrainage est unique pour chaque filleul.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail ou tableau d'adresses mails des filleuls
 *	@param string $body Optionnel, message adressé au filleul
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 *
 */
function gu_sponsor_points_create( $usr, $email, $body='' ){
	if( !gu_users_exists($usr) ){
		return false;
	}

	global $config;

	if( !is_array($email) ){
		if( !isemail($email) ){
			return false;
		}

		$email = array( $email );
	}else{
		if( !sizeof($email) ){
			return false;
		}

		foreach( $email as $e ){
			if( !isemail($e) ){
				return false;
			}
		}
	}

	// Récupère le profile du compte parrain
	$prf = gu_users_get_prf( $usr );
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	$rrwd = rwd_rewards_get( 0, $prf, $config['wst_id'] );
	if( !$rrwd || !ria_mysql_num_rows($rrwd) ){
		return false;
	}

	$rwd = ria_mysql_result( $rrwd, 0, 'id' );

	$crwd = rwd_rewards_get_sponsor( $rwd );
	if( !is_array($crwd) || !sizeof($crwd) ){
		return false;
	}

	if( (!isset($crwd['active']) || !$crwd['active'])
		|| $crwd['points']<0
		|| $crwd['days']<0
		|| $crwd['limit']<0
	){
		return true;
	}

	if( $crwd['limit']>0 && gu_sponsor_count($usr)>=$crwd['limit'] ){
		return false;
	}

	foreach($email as $e){
		$error = "";

		if(gu_sponsor_points_add($usr, $e, $crwd['days'])){
			gu_sponsor_points_send($usr, $e, $crwd['points'], $body, $crwd['days']);
		}else{
			return false;
		}

	}

	return true;
}

// \cond onlyria
/** Cette fonction permet d'envoyer un email concernant le gain de point de fidélité
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail ou tableau d'adresses mails des filleuls
 *	@param $points Obligatoire, identifiant d'un code promotion
  *	@param string $body Optionnel, message adressé au filleul
  *	@param $days Obligatoire, durée de la promotion en jours
 *	@return bool True si les envois ce sont bien passés
 */
 function gu_sponsor_points_send( $usr, $email, $points, $body='', $days=0 ){

	$rcfg = cfg_emails_get('alert-sponsor');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}
	$cfg = ria_mysql_fetch_array( $rcfg );

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );

	$mail->addTo( $email );

	if( trim($cfg['bcc'])!='' ){
		$mail->addBcc( $cfg['bcc'] );
	}

	if( trim($cfg['reply-to'])!='' ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	global $config;

	$rsponsor = gu_users_get( $usr );
	if( !$rsponsor || !ria_mysql_num_rows($rsponsor) ){
		return false;
	}


	$sponsor = ria_mysql_fetch_array( $rsponsor );
	$url = $config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=parrainage-promotion';

	switch ($config["tnt_id"]) {
		case 13:

			$mail->setSubject( $sponsor['adr_firstname'].' vous invite...' );

			if(  $config['email_html_header'] ){
				$mail->addHtml( $config['email_html_header'] );
				$mail->addHtml( '<br />' );
			}

			$mail->addParagraph('<b>Bienvenue sur '.$config['site_name'].',</b>');
			$mail->addParagraph( gu_users_get_name($usr).' vous invite à venir découvrir notre catalogue sur : <a href="'.$url.'">'.$config['site_url'].'</a>.' );
			$mail->addParagraph( 'Une offre de bienvenue exceptionnelle n\'attend plus que vous. Recevez <b>'.$points.'</b> points de fidélité en plus après votre première commande' );
			if( $days ){
				$date_limite = date( 'd/m/Y 23:59', strtotime('+'.$days.' days') );
				$mail->addParagraph( 'Cette offre est valable pendant '.$days.' jour'.($days>1 ? 's' : '').', soit jusqu\'au '.$date_limite.'.' );
			}
			if( trim($body)!='' ){
				$mail->addParagraph('Voici le message personnel de '.gu_users_get_name($usr).' pour vous : ');
				$mail->addParagraph( $body );
			}

			if( $config['email_html_footer'] ){
				$mail->addHtml( $config['email_html_footer'] );
			}else{
				$mail->addHtml( '<br />' );
				$mail->addHtml('<p align="right">' . htmlspecialchars($config['site_name']) . ',<br><a href="'.$url.'">'.$config[ 'site_url' ].'</a></p>');
			}

			return $mail->send();

			break;
		default:
			break;
	}

	return true;
 }

 /** Cette fonction permet de renvoyer un email concernant le gain de point de fidélité
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@return bool True si les envois ce sont bien passés
 *	@todo Pour l'instant, cette fonction n'est pas implémentée
 */
 function gu_sponsor_points_resend( $usr ){

 }

/** Cette fonction permet de rattacher un code de parrainage sur un parrain.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param int $cod Obligatoire, identifiant d'un code de parrainage
 *	@param string $email Obligatoire, adresse mail du filleul destinataire du code de parrainage
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsor_promotions_add( $usr, $cod, $email ){
	if( !gu_users_exists($usr) ){
		return false;
	}

	if( !pmt_codes_exists($cod) ){
		return false;
	}

	if( !isemail($email) ){
		return false;
	}

	global $config;

	$sql = '
		insert into gu_sponsor_promotions
			( spp_tnt_id, spp_usr_id, spp_cod_id, spp_email )
		values
			( '.$config['tnt_id'].', '.$usr.', '.$cod.', \''.addslashes($email).'\' )
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un code de parrainage a bien été envoyé à un filleul.
 *	Le code de parrainage doit toujours être actif pour que la fonction retourne True.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail du filleul
 *	@return bool True si un code de parrainage a été émis, False dans le cas contraire
 */
function gu_sponsor_promotions_exists( $usr, $email ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !isemail($email) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_sponsor_promotions
			join pmt_codes on (spp_tnt_id=cod_tnt_id and spp_cod_id=cod_id)
			join pmt_offers on (cod_tnt_id=off_tnt_id and cod_id=off_cod_id)
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_email=\''.addslashes($email).'\'
			and cod_date_deleted is null
			and ( ((ifnull(off_date_stop, \'\')=\'\' or date(off_date_stop)>=now()) and spp_used=0) or spp_used=1 )
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

/** Cette fonction permet de récupérer les informations sur un ou plusieurs code de parrainage.
 *	@param int $usr Optionnel, identifiant d'un compte parrain
 *	@param $has_used Optionnel, par défaut n'est pas prit en compte, mettre True pour retourner que les codes ayant été utilisé, False dans le cas contraire
 *	@return resource Un résultat MySQL contenant :
 *				- cod : identifiant du code de parrainage
 *				- usr : identifiant du compte parrain
 *				- email : adresse mail du filleul
 *				- used : si le code de parrainage a déjà été utilisé
 *				- points : nombre de points gagné par le parrain
 *				- valid : si le code de parrainage est toujours utilisable
 */
function gu_sponsor_promotions_get( $usr=0, $has_used=null ){
	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}

	global $config;

	$sql = '
		select spp_cod_id as cod, spp_usr_id as usr, spp_email as email, spp_used as used, spp_points as points,
		if( ifnull(off_date_stop, \'\')=\'\' or date(off_date_stop)>=now(), 1, 0 ) as valid
		from gu_sponsor_promotions
			join pmt_codes on (spp_tnt_id=cod_tnt_id and spp_cod_id=cod_id)
			join pmt_offers on (cod_tnt_id=off_tnt_id and cod_id=off_cod_id)
		where spp_tnt_id='.$config['tnt_id'].'
	';

	if( $usr>0 ){
		$sql .= ' and spp_usr_id='.$usr;
	}

	if( $has_used!==null ){
		if( $has_used ){
			$sql .= ' and spp_used=1';
		}else{
			$sql .= ' and spp_used=0';
		}
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de retourner un code de parrainage.
 *	Au moins l'un des deux paramètres doit être fourni à la fonction.
 *	@param int $id Optionnel, identifiant d'un code de parrainage
 *	@param $code Optionel, code de la promotion
 *	@return resource Un résultat MySQL contenant :
 *		- usr : identifiant du compte parrain
 *		- email : adresse mail du compte parrain
 *		- identifiant de l'adresse de facturation
 */
function gu_sponsor_promotions_get_user( $id=0, $code='' ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( $id==0 && trim($code)=='' ){
		return false;
	}

	global $config;

	$sql = '
		select spp_usr_id as usr, usr_email as email, usr_adr_invoices as adr_invoices
		from gu_sponsor_promotions
			join gu_users on ((usr_tnt_id=0 or spp_tnt_id=usr_tnt_id) and spp_usr_id=usr_id)
	';

	if( trim($code)!='' ){
		$sql .= ' join pmt_codes on (spp_tnt_id=cod_tnt_id and spp_cod_id=cod_id)';
	}

	$sql .= '
		where spp_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
	';

	if( $id>0 ){
		$sql .= ' and spp_cod_id='.$id;
	}

	if( trim($code)!='' ){
		$sql .= ' and cod_code=\''.addslashes($code).'\'';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner l'identifiant du compte parrain à l'origine du code de parrainage.
 *	Au moins l'un des deux paramètres doit être fourni à la fonction.
 *	@param int $id Optionnel, identifiant d'un code de parrainage
 *	@param $code Optionel, code de la promotion
 *	@return int L'identifiant du parrain rattaché au code de parrainage, False s'il n'existe pas
 */
function gu_sponsor_promotions_get_user_id( $id=0, $code='' ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( $id==0 && trim($code)=='' ){
		return false;
	}

	global $config;

	$res = gu_sponsor_promotions_get_user( $id, $code );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'usr' );
}
// \endcond

/** Cette fonction permet de vérifier qu'un code de parrainage est déjà utilisé.
 *	@param int $usr Obligatoire, identifiant d'un compte parrain
 *	@param $cod Optionnel, identifiant d'un code de parrainage
 *	@param string $email Optionnel, adresse mail d'un compte filleul
 *	@return bool False si le paramètre obligatoire est omis ou faux ou bien si ni le code de parrainage ni l'adresse mail du compte filleul n'est fourni.
 *	@return bool True si le code est déjà utilisé, False dans le cas contraire
 */
function gu_sponsor_promotions_is_used( $usr, $cod=0, $email='' ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($cod) || $cod<0 ){
		return false;
	}

	if( trim($email)!='' && !isemail($email) ){
		return false;
	}


	if( $cod<=0 && trim($email)=='' ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_sponsor_promotions
			join pmt_codes on (spp_tnt_id=cod_tnt_id and spp_cod_id=cod_id)
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and cod_date_deleted is null
			and spp_used=1
	';

	if( $cod>0 ){
		$sql .= ' and cod_id='.$cod;
	}

	if( trim($email)!='' ){
		$sql .= ' and spp_email=\''.addslashes($email).'\'';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de compter le nombre de parrainage en cours de validité.
 *	@param int $usr Obligatoire, identifiant d'un parrain
 *	@return Le nombre de parrainage
 */
function gu_sponsor_promotions_count( $usr ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select count(*) as nb_godson
		from gu_sponsor_promotions
			join pmt_codes on (spp_tnt_id=cod_tnt_id and spp_cod_id=cod_id)
			join pmt_offers on (cod_tnt_id=off_tnt_id and cod_id=off_cod_id)
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and cod_date_deleted is null
			and (ifnull(off_date_stop, \'\')=\'\' or date(off_date_stop)>=now()) and spp_used=0
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 'nb_godson' );
}

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information d'utilisation du code de parrainage.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param int $cod Obligatoire, identifiant du code promotion
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsor_promotions_set_used( $usr, $cod ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update gu_sponsor_promotions
		set spp_used=1
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_cod_id='.$cod.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nombre de points gagnés par le parrain lors de l'utilisation du code de parrainage.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param int $cod Obligatoire, identifiant du code promotion
 *	@param $points Obligatoire, nombre de points
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsor_promotions_set_points( $usr, $cod, $points ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($points) || $points<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update gu_sponsor_promotions
		set spp_points='.$points.'
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_cod_id='.$cod.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer le code de parrainage, après l'avoir créer, à un ou plusieurs filleuls.
 *	Le code de parrainage est unique pour chaque filleul.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail ou tableau d'adresses mails des filleuls
 *	@param string $body Optionnel, message adressé au filleul
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 *
 */
function gu_sponsor_promotions_create( $usr, $email, $body='' ){
	$errors = array();

	if( !gu_users_exists($usr) ){
		return false;
	}

	global $config;

	if( !is_array($email) ){
		if( !isemail($email) ){
			return false;
		}

		$email = array( $email );
	}else{
		if( !sizeof($email) ){
			return false;
		}

		foreach( $email as $e ){
			if( !isemail($e) ){
				return false;
			}
		}
	}

	// Récupère le profile du compte parrain
	$prf = gu_users_get_prf( $usr );
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	$rrwd = rwd_rewards_get( 0, $prf, $config['wst_id'] );
	if( !$rrwd || !ria_mysql_num_rows($rrwd) ){
		return false;
	}

	$rwd = ria_mysql_result( $rrwd, 0, 'id' );

	$crwd = rwd_rewards_get_sponsor( $rwd );
	if( !is_array($crwd) || !sizeof($crwd) ){
		return false;
	}

	if( (!isset($crwd['active']) || !$crwd['active'])
		|| $crwd['discount']<0
		|| $crwd['days']<0
		|| $crwd['limit']<0
	){

		return true;
	}

	if( $crwd['limit']>0 && gu_sponsor_count($usr)>=$crwd['limit'] ){
		return false;
	}
	// Récupère les règles d'inclusion/exclusion des produits
	$all_catalog = rwd_rewards_get_all_catalog( $rwd );

	$dont_use_restriction = in_array( $config['tnt_id'], array(26) );

	$rp = $rb = $rc = false;
	if( !$dont_use_restriction ){
		$rp = rwd_products_get( $rwd );
		$rb = rwd_brands_get( $rwd );
		$rc = rwd_categories_get( $rwd );

		if( !$rp || !$rb || !$rc ){
			return false;
		}
	}

	foreach( $email as $e ){
		// Vérifié qu'un code de parrainage n'a pas déjà été envoyé
		if( gu_sponsor_promotions_exists($usr, $e) ){
			continue;
		}

		// Génère le code de parrainage
		$code = pmt_codes_generated();
		if( trim($code)=='' ){
			return false;
		}

		// Gestion de début et de la fin de la promotion
		$date_start = date('Y-m-d H:i:s');
		$date_stop = null;
		if( $crwd['days']>0 ){
			$date_stop = date( 'Y-m-d 23:59:59', strtotime('+'.$crwd['days'].' days') );
		}

		// Création de la promotion
		$desc = 'Code de parrainage généré pour '.$e.'.';
		$pmt = pmt_codes_add( '', _PMT_TYPE_REWARD, $code, $desc );
		if( !$pmt ){
			return false;
		}

		// Création du bénéfice de la promotion (x% sur la commande)
		if(isset($crwd['first_order']) && $crwd['first_order'] != 0 ){
			if( !pmt_offers_add($pmt, _PMT_TYPE_REWARD, $crwd['discount'], 1, '', 0, 0, 0, 'order', $date_start, $date_stop, 1, false, false, true, $crwd['first_order'])){
				ria_mysql_query('delete from pmt_codes where cod_tnt_id='.$config['tnt_id'].' and cod_id='.$pmt);
				return false;
			}
		}else{
			if( !pmt_offers_add($pmt, _PMT_TYPE_REWARD, $crwd['discount'], 1, '', 0, 0, 0, 'order', $date_start, $date_stop, 1, false, false, true) ){
				ria_mysql_query('delete from pmt_codes where cod_tnt_id='.$config['tnt_id'].' and cod_id='.$pmt);
				return false;
			}
		}


		// Copy les règles d'inclusion et d'exclusion
		if( !pmt_codes_set_all_catalog($pmt, $all_catalog) || !pmt_codes_set_all_customers($pmt, true) ){
			$res = false;
		}

		$res = true;
		if( $rp && ria_mysql_num_rows($rp) ){
			while($p = ria_mysql_fetch_array($rp) ){
				if( !pmt_products_add($pmt, $p['prd'], $p['include']) ){
					$res = false;
				}
			}
		}

		if( $rb && ria_mysql_num_rows($rb) ){
			while($b = ria_mysql_fetch_array($rb) ){
				if( !pmt_brands_add($pmt, $b['brd'], $b['include']) ){
					$res = false;
				}
			}
		}

		if( $rc && ria_mysql_num_rows($rc) ){
			while($c = ria_mysql_fetch_array($rc) ){
				if( !pmt_categories_add($pmt, $c['cat'], $c['include']) ){
					$res = false;
				}
			}
		}

		pmt_websites_add( $pmt, $config['wst_id'] );

		if( !$res ){
			pmt_codes_del( $pmt );
			return false;
		}

		if( !gu_sponsor_promotions_send($usr, $e, $code, $crwd, $body) ){
			pmt_codes_del( $pmt );
			return false;
		}

		// Enregistre le parrainage
		if( !gu_sponsor_promotions_add($usr, $pmt, $e) ){
			return false;
		}
	}

	return true;
}
// \endcond

/** Cette fonction permet de renvoyer un code de parrainage.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@return bool True si le code a bien été renvoyé, False dans le cas contraire et -1 si le code de promotion est toujour valide
 */
function gu_sponsor_promotions_resend( $usr, $cod ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	global $config;

	$rpmt = pmt_codes_get( $cod, null, false, _PMT_TYPE_REWARD );
	if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
		return false;
	}

	$pmt = ria_mysql_fetch_array( $rpmt );
	if( $pmt['state']!='closed' ){
		return -1;
	}

	// Récupère le profile du compte parrain
	$prf = gu_users_get_prf( $usr );
	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	$rrwd = rwd_rewards_get( 0, $prf, $config['wst_id'] );
	if( !$rrwd || !ria_mysql_num_rows($rrwd) ){
		return false;
	}

	$rwd = ria_mysql_result( $rrwd, 0, 'id' );

	$crwd = rwd_rewards_get_sponsor( $rwd );
	if( !is_array($crwd) || !sizeof($crwd) ){
		return false;
	}

	// Vérification de la configuration
	if(
		(!isset($crwd['active']) || !$crwd['active'])
		|| (!isset($crwd['discount']) || !is_numeric($crwd['discount']) || $crwd['discount']<=0)
		|| (!isset($crwd['days']) || !is_numeric($crwd['days']) || $crwd['days']<0)
	){
		return false;
	}

	$rgodson = ria_mysql_query('
		select spp_email as email
		from gu_sponsor_promotions
		where spp_tnt_id='.$config['tnt_id'].'
			and spp_usr_id='.$usr.'
			and spp_cod_id='.$cod.'
	');

	if( !$rgodson || !ria_mysql_num_rows($rgodson) ){
		return false;
	}

	$godson = ria_mysql_result( $rgodson, 0, 'email' );

	// Gestion de début et de la fin de la promotion
	$date_start = date('Y-m-d H:i:s');
	$date_stop = null;
	if( $crwd['days']>0 ){
		$date_stop = date( 'Y-m-d 23:59:59', strtotime('+'.$crwd['days'].' days') );
	}
	// Mise à jour des dates de début et fin du code promotion
	if( !pmt_codes_update_dates($cod, $date_start, $date_stop) ){
		return false;
	}

	return gu_sponsor_promotions_send( $usr, $godson, $pmt['code'], $crwd, '', true );
}

// \cond onlyria
/** Cette fonction permet d'envoyer un mail de parrainage.
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail du filleul
 *	@param $code Obligatoire, code de la promotion
 *	@param $crwd Obligatoire, contient la configuration utilisée pour l'envoi du mail
 *	@param string $body Optionnel, message adressé au filleul
 *	@param $resend Optionnel, si oui ou non il s'agit d'un renvoi de code de parrainage
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire.
 */
function gu_sponsor_promotions_send( $usr, $email, $code, $crwd=false, $body='', $resend=false ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !isemail($email) ){
		return false;
	}

	if( !pmt_codes_exists(null, $code) ){
		return false;
	}
	global $config;

	// if( !is_array($crwd) || !sizeof($crwd) ){
	// 	$crwd = rwd_rewards_get_sponsor( $rwd );
	// 	if( !is_array($crwd) || !sizeof($crwd) ){
	// 		return false;
	// 	}
	// }

	// Vérification de la configuration
	if(
		(!isset($crwd['active']) || !$crwd['active'])
		|| (!isset($crwd['discount']) || !is_numeric($crwd['discount']) || $crwd['discount']<=0)
		|| (!isset($crwd['days']) || !is_numeric($crwd['days']) || $crwd['days']<0)
		|| (!isset($crwd['limit']) || !is_numeric($crwd['limit']) || $crwd['limit']<0)
	){
		return false;
	}

	if( $crwd['limit']>0 && gu_sponsor_promotions_count($usr)>=$crwd['limit'] ){
		return false;
	}

	$discount = str_replace( array('.00', ',00'), '', $crwd['discount'] );

	$rsponsor = gu_users_get( $usr );
	if( !$rsponsor || !ria_mysql_num_rows($rsponsor) ){
		return false;
	}

	$sponsor = ria_mysql_fetch_array( $rsponsor );

	$rcfg = cfg_emails_get('alert-sponsor');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array( $rcfg );
	$url = $config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=parrainage-promotion';

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );

	if( trim($cfg['bcc'])!='' ){
		$mail->addBcc( $cfg['bcc'] );
	}

	if( trim($cfg['reply-to'])!='' ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	switch( $config['tnt_id'] ){
		case 14 :
			require_once $config['site_dir'].'/../include/view.emails.inc.php';
			$sponsor_rewards = rwd_rewards_get_sponsor(0, $config['wst_id'], PRF_CUSTOMER);
			$discount = number_format($sponsor_rewards['discount']).'%';

			$mail->setSubject( 'Profitez de '.$discount.' de remise immédiate sur Coopcorico.fr' );

			$html = terredeviande_notify_new_sponsors( [
				'code'		=> pmt_codes_format($code),
				'from'		=> gu_users_get_name($usr),
				'message'	=> strcut($body, 77)
			], $email, $sponsor_rewards );

			// $analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=parrainage-promotion';

			$mail->addHTML( $html );
			break;
		default :
			$mail->setSubject( $sponsor['adr_firstname'].' vous invite...' );

			if(  $config['email_html_header'] ){
				$mail->addHtml( $config['email_html_header'] );
				$mail->addHtml( '<br />' );
			}

			$mail->addParagraph('<b>Bienvenue sur '.$config['site_name'].',</b>');
			$mail->addParagraph( gu_users_get_name($usr).' vous invite à venir découvrir notre catalogue sur : <a href="'.$url.'">'.$config['site_url'].'</a>.' );
			$mail->addParagraph( 'Une offre de bienvenue exceptionnelle n\'attend plus que vous. Recevez une réduction de <b>'.$discount.'%</b> sur votre première commande avec le code promotion ci-dessous :' );
			$mail->addHtml( '<p align="center" style="font-size: 22px"><b>'.pmt_codes_format($code).'</b></p>' );
			if( $crwd['days']>0 ){
				$date_limite = date( 'd/m/Y 23:59', strtotime('+'.$crwd['days'].' days') );
				$mail->addParagraph( 'Cette offre est valable pendant '.$crwd['days'].' jour'.($crwd['days']>1 ? 's' : '').', soit jusqu\'au '.$date_limite.'.' );
			}

			if( trim($body)!='' ){
				$mail->addParagraph('Voici le message personnel de '.gu_users_get_name($usr).' pour vous : ');
				$mail->addParagraph( $body );
			}

			if( $config['email_html_footer'] ){
				$mail->addHtml( $config['email_html_footer'] );
			}else{
				$mail->addHtml( '<br />' );
				$mail->addHtml('<p align="right">' . htmlspecialchars($config['site_name']) . ',<br><a href="'.$url.'">'.$config[ 'site_url' ].'</a></p>');
			}
		break;
	}

	return $mail->send();
}
// \endcond

// \cond onlyria
/** Cette fonction permet de prévenir un internaute qu'il a atteint le nombre de points pour profiter d'une remise
 *	@param $reward Obligatoire, tableau de la configuration en cours des points de fidélité au minimum (system, nb_pts, discount, discount_type)
 *	@param $user Obligatoire, tableau sur les informations du client (au minimum : id, adr_firstname, adr_lastname, email)
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function rwd_rewards_system_remise_send( $reward, $user ){
	if (!ria_array_key_exists(array('system', 'nb_pts', 'discount', 'discount_type'), $reward)) {
		return false;
	}

	if (!ria_array_key_exists(array('id', 'adr_firstname', 'adr_lastname', 'email'), $user)) {
		return false;
	}

	global $config;

	if( !isset($config['rwd_newsletter']) || !newsletter_is_inscripted($user['email'], $config['rwd_newsletter']) ){
		return true;
	}

	if ($reward['system'] != _RWD_SYSTEM_REMISE) {
		return false;
	}

	$r_cfg = cfg_emails_get( 'alert-rewards' );
	if (!$r_cfg || !ria_mysql_num_rows($r_cfg)) {
		return false;
	}

	$cfg = ria_mysql_fetch_assoc( $r_cfg );

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo($user['email']);

	if (trim($cfg['cc']) != '') {
		$email->addCc( $cfg['cc'] );
	}

	if (trim($cfg['bcc']) != '') {
		$email->addBcc( $cfg['bcc'] );
	}

	if (trim($cfg['reply-to']) != '') {
		$email->setReplyTo( $cfg['reply-to'] );
	}

	switch ($config['tnt_id']) {
		case 16:
			$email->setSubject('Animaleco.com vous remercie de votre fidélité');

			$email->addHtml($config['email_html_header']);
			$email->addParagraph('Nous voulons vous remercier !');
			$email->addParagraph('Bonjour '.trim($user['adr_firstname']).',');
			$email->addParagraph('Nous sommes très heureux de vous compter parmi nos client(e)s les plus fidèles.');
			$email->addParagraph('Et à ce titre pour vous remercier, vous bénéficiez dès à présent des '.$reward['nb_pts'].' points fidélité qui vous donnent droit à '.round($reward['discount']).( $reward['discount_type'] == 1 ? '%' : '€' ).' de remise sur les articles de votre prochaine commande (hors croquettes et promotions en cours, dans la limite des stocks disponibles).');
			$email->addParagraph('Pour en profiter, rien de plus simple : il suffit d’effectuer vos achats comme d’habitude, de vous connecter à votre compte client et de cliquer sur le bouton « J’en profite » dans le panier pour utiliser votre remise.');
			$email->addParagraph('Cette remise est applicable dans votre magasin Animal & Co ou sur <a href="'.$config['site_url'].'?utm_source=alertes&utm_medium=email&utm_campaign=rewards-500">animaleco.com</a>.');
			$email->addParagraph('A très vite !');
			$email->addHtml('<p>L\'équipe Animal &amp; Co<br />Notre histoire, vos animaux.</p>');
			$email->addHtml($config['email_html_footer']);
			break;

		default: return true;
	}

	return $email->send();
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un lien de parrainage a déjà été envoyé.
 *	@param int $usr_id Obligatoire, identifiant d'un compte parrain
 *	@param string $email Obligatoire, adresse mail du filleul
 *	@return bool True si le lien existe déjà, False dans le cas contraire
 */
function gu_sponsor_links_exists( $usr_id, $email ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if (!isemail($email)) {
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from gu_sponsor_links
		where spl_tnt_id = '.$config['tnt_id'].'
			and spl_usr_id = '.$usr_id.'
			and spl_email = "'.addslashes( $email ).'"
			and spl_date_deleted is null
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'enregistrer un parrainage via partage de lien
 *	@param int $usr_id Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, Adresse mail à parrainer
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function gu_sponsor_links_add( $usr_id, $email ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if (!isemail($email)) {
		return false;
	}

	if (gu_sponsor_links_exists($usr_id, $email)) {
		return true;
	}

	global $config;

	return ria_mysql_query('
		insert into gu_sponsor_links
			(spl_tnt_id, spl_usr_id, spl_email, spl_datetime)
		values
			('.$config['tnt_id'].', '.$usr_id.', "'.addslashes( $email ).'", now())
	');
}
// \endcond

/** Cette fonction permet de récupérer un ou plusieurs lien de parrainage créé
 *	@param int $usr_id Optionnel, identifiant du compte parrain
 *	@param string $email Optionnel, adresse mail du filleul
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du lien créé
 *				- usr_id : identifiant du compte parrain
 *				- email : adresse mail du filleul
 *				- date : date / heure d'envoi du lien (version FR)
 *				- date_en : date / heure d'envoi du lien (version EN)
 */
function gu_sponsor_links_get( $usr_id=0, $email='' ){
	if (!is_numeric($usr_id) || $usr_id < 0) {
		return false;
	}

	if (trim($email) != '') {
		if (!isemail($email)) {
			return false;
		}
	}

	global $config;

	$sql = '
		select
			spl_id as id, spl_usr_id as usr_id, spl_email as email, spl_datetime as date_en,
			date_format(spl_datetime, "%d/%m/%Y à %H:%i") as date
		from gu_sponsor_links
		where spl_tnt_id = '.$config['tnt_id'].'
			and spl_date_deleted is null
	';

	if ($usr_id > 0) {
		$sql .= ' and spl_usr_id = '.$usr_id;
	}

	if ($email) {
		$sql .= ' and spl_email = "'.addslashes( $email ).'"';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de supprimer un lien de paramétrage pour un compte client (ou tout ses liens).
 *	@param int $usr_id Obligatoire, identifiant du compte parrain
 *	@param int $id Optionnel, identifiant ou tableau d'identifiants du lien à supprimer (laissez vide pour tout supprimer)
 *	@return bool True si la suppression s'est correctement passés, False dans le cas contraire
 */
function gu_sponsor_links_del( $usr_id, $id=0 ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	$id = control_array_integer( $id, false );
	if ($id === false) {
		return false;
	}

	global $config;

	$sql = '
		update gu_sponsor_links
			set spl_date_deleted = now()
		where spl_tnt_id = '.$config['tnt_id'].'
			and spl_usr_id = '.$usr_id.'
	';

	if ($id > 0) {
		$sql .= ' and spl_id in ('.implode(', ', $id).')';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer un email concernant le gain de point de fidélité
 *	@param int $usr Obligatoire, identifiant du compte parrain
 *	@param string $email Obligatoire, adresse mail ou tableau d'adresses mails des filleuls
 *	@param string $body Optionnel, message adressé au filleul
 *	@return bool True si les envois ce sont bien passés
 */
function gu_sponsor_send_link($usr,$email,$body=''){
	if (!is_array($email)) {
		$email = array( $email );
	}

	foreach ($email as $one_email) {
		$add = gu_sponsor_links_add( $usr, $one_email );

		if (!$add) {
			return false;
		}
	}

	$rcfg = cfg_emails_get('alert-sponsor');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}
	$cfg = ria_mysql_fetch_array( $rcfg );

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );

	$mail->addTo( $email );

	if( trim($cfg['bcc'])!='' ){
		$mail->addBcc( $cfg['bcc'] );
	}

	if (trim($cfg['reply-to'])!='') {
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	$rsponsor = gu_users_get( $usr );
	if( !$rsponsor || !ria_mysql_num_rows($rsponsor) ){
		return false;
	}
	global $config;

	$sponsor = ria_mysql_fetch_array( $rsponsor );
	$url = gu_users_get_sponsor_tracking();

	switch ($config["tnt_id"]) {
		case 13:
			$mail->setSubject( $sponsor['adr_firstname'].' vous invite...' );

			if(  $config['email_html_header'] ){
				$mail->addHtml( $config['email_html_header'] );
				$mail->addHtml( '<br />' );
			}

			$mail->addParagraph('<b>Bienvenue sur '.$config['site_name'].',</b>');
			$mail->addParagraph( gu_users_get_name($usr).' vous invite à venir découvrir notre catalogue sur : <a href="'.$url.'">'.$config['site_url'].'</a>.' );

			if( trim($body)!='' ){
				$mail->addParagraph('Voici le message personnel de '.gu_users_get_name($usr).' pour vous : ');
				$mail->addParagraph( $body );
			}

			if( $config['email_html_footer'] ){
				$mail->addHtml( $config['email_html_footer'] );
			}else{
				$mail->addHtml( '<br />' );
				$mail->addHtml('<p align="right">' . htmlspecialchars($config['site_name']) . ',<br><a href="'.$url.'">'.$config[ 'site_url' ].'</a></p>');
			}

			return $mail->send();
			break;
		default:
			break;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de prévenir les internautes que leur code de parrainage arrive à échéance.
 *	@param $prf_id Obligatoire, Identifiant du profile client pour récupérer la configuration du système de points de fidélité
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function rwd_alert_sponsors( $prf_id ){
	if( $prf_id && $prf_id<=0 ){
		return false;
	}

	global $config;

	if( !$config['email_alerts_enabled'] ){
		// return false;
	}

	// Récupère la configuration d'adresse mail
	$rcfg = cfg_emails_get( 'alert-rewards' );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array( $rcfg );

	// On récupère la configuration des points de fidélité pour le profile donné
	$conf = rwd_rewards_get_sponsor( 0, $config['wst_id'], $prf_id );
	if( !is_array($conf) || !sizeof($conf) ){
		return false;
	}

	// Vérifie que le système de parrainage est activé
	if( !$conf['active'] ){
		return true;
	}

	$days_before_lost = $conf['cod_before_lost'];

	// Si aucun délai de pré-alert n'est renseigné, on ne vas pas plus loin
	if($days_before_lost <= 0){
		return false;
	}

	// Date limite d'utilisation selon le délai de pré-alert
	$date_limited = date( 'Y-m-d', (time() + $days_before_lost * 86400) );
	$time_limited = strtotime( $date_limited );

	$days_before_lost = $days_before_lost - 1;

	$rcode = pmt_codes_get( null, null, true, _PMT_TYPE_REWARD );
	if( !$rcode || !ria_mysql_num_rows($rcode) ){
		return false;
	}

	while( $code = ria_mysql_fetch_array($rcode) ){
		// Vérifie que le code n'a pas déjà été utilisé
		if( $code['used']>0 ){
			continue;
		}

		$time = strtotime( dateparse($code['date_stop']) );
		$usr_email = str_replace( 'Code de parrainage généré pour ', '', $code['desc'] );
		$usr_email = substr( $usr_email, 0, strlen($usr_email)-1);

		if( !isemail($usr_email) ){
			continue;
		}

		if( $time == $time_limited ){
			// Préparation du mail
			$email = new Email();

			$email->setFrom( $cfg['from'] );
			$email->addTo( $usr_email );

			if( $cfg['bcc'] ){
				$email->addBcc( $cfg['bcc'] );
			}

			if( trim($cfg['reply-to']) != '' ){
				$email->setReplyTo( $cfg['reply-to'] );
			}

			switch( $config['tnt_id'] ){
				case 14 :
					require_once $config['site_dir'].'/../include/view.emails.inc.php';
					$discount = number_format($code['discount']*_TVA_RATE_DEFAULT, 2, ',', ' ').(($code['type'])?' €':' %');
					$title = i18n::get('Derniers jours pour profiter de '.$discount.' de remise immédiate sur Coopcorico.fr', 'NOTIFICATIONS');
					$email->setSubject($title);

					$html = terredeviande_notify_sponsors($days_before_lost, $code, $discount, $usr_email);
					// $analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=relance-parrainage-promotion';

					$email->addHtml( $html );

					$email->send();
					break;
				default :
					$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=relance-code-parrainage';

					$discount = number_format( $code['discount'], 2, ',', ' ' );
					$discount = str_replace( ',00', '', $discount );

					$email->setSubject('Derniers jours pour profiter de votre code de parrainage');

					// Corps du mail mail
					$email->addHtml( $config['email_html_header'] );

					$email->addParagraph( 'Bonjour,');
					$email->addHtml( 'Il ne vous reste plus que <b>'.$days_before_lost.' jours</b> pour profiter de votre remise de <b>'.$discount.' % sur votre 1ère commande</b>. Voici votre code de parrainage à saisir lors de votre passage de commande : <b>'.$code['code'].'</b>.' );
					$email->addParagraph( 'Nous restons bien évidemment à votre disposition pour toute question, n\'hésitez-pas à <a href="'.$config['site_url'].$config['contact_page_url'].$analytics.'">nous contacter</a>.');
					$email->addParagraph(' Au plaisir de vous retrouver prochainement sur <a href="'.$config['site_url'].'/'.$analytics.'">'.$config['site_url'].'</a>,');
					$email->addParagraph(' L\'équipe '.$config['site_name'].'.');

					$email->send();
					break;
			}
		}
	}

	return true;
}
// \endcond

/// @}

// \cond onlyria

/** \defgroup rewards_users Gestion des utilisateurs
 *	Les fonctions contenus dans ce modules permettent de configurer l'utilisation des rewards en fonctions de l'utilisateur.
 *	@{
 */

// NOTE : rwd_register_required est la variable de configuration pour restreindre l'acces.

/** Cette fonction permet de mettre en place le systeme de rewards pour un utilisateur donnée (ou réactiver), dans le cas ou c'est requis dans la configuration
 *	@param int $usr_id Obligatoire, identifiant client qui obtiendra les droits.
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function rwd_users_enabled( $usr_id ){

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if (rwd_users_is_enabled($usr_id)) {
		return true;
	}

	global $config;

	$res = ria_mysql_query('
		insert into rwd_users
			( rwu_tnt_id, rwu_usr_id, rwu_date_inscript, rwu_date_uninscript )
		values
			( '.$config['tnt_id'].', '.$usr_id.', now(), null )
	');

	if (!$res) {
		return false;
	}

	rwd_actions_apply( 'RWA_ADHESION', CLS_USER, $usr_id);

	// Notification d'inscription
	rwd_users_enabled_send( $usr_id );

	return true;
}

/** Cette fonction permet de notifier l'inscription d'un compte client au programme de fidélité
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@return bool true si l'envoi s'est correctement déroulée, false dans le cas contraire, -1 si la configuration email nécessaire à l'envoi n'existe pas
 */
function rwd_users_enabled_send( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	$r_user = gu_users_get( $usr_id );
	if (!$r_user || !ria_mysql_num_rows($r_user)) {
		return false;
	}

	$r_cfg = cfg_emails_get('inscript-rewards');
	if (!$r_cfg || !ria_mysql_num_rows($r_cfg)) {
		return -1;
	}

	global $config;

	$cfg = ria_mysql_fetch_assoc( $r_cfg );
	$user = ria_mysql_fetch_assoc( $r_user );

	$date_password = date('Y-m-d H:i:s');
	$res_upd = ria_mysql_query('update gu_users set usr_date_password = "'.addslashes( $date_password ).'" where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$user['id']);
	$token_reinit_password = md5($user['email'].$user['password'].$date_password);

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( $user['email'] );

	if (trim($cfg['cc']) != '') {
		$email->addCc( $cfg['cc'] );
	}

	if (trim($cfg['bcc']) != '') {
		$email->addBcc( $cfg['bcc'] );
	}

	if (trim($cfg['reply-to']) != '') {
		$email->setReplyTo( $cfg['reply-to'] );
	}

	switch ($config['tnt_id']) {
		case 13:
			$name_point = 'ttantto';
			$email->setSubject('Vous venez de rejoindre la partie de mus !');
			break;
		default:
			$name_point = 'points';
			$email->setSubject($config['site_name'] . ' - Inscription au programme de fidélité');
			break;
	}

	$email->addHtml( $config['email_html_header'] );
	$email->addParagraph('Cher client,');

	$email->addHtml('<p>Nous sommes très heureux de vous compter parmi nos plus fidèles membres. '.htmlspecialchars($config['site_name']).' vous remercie de votre confiance et vous souhaite la bienvenue.</p>');
	$email->addParagraph('Vous trouverez ci-dessous un rappel de vos identifiants : ');
	$email->addHtml('<ul>
		<li>Login : '.htmlspecialchars( $user['email'] ).'</li>
		<li>Mot de passe : Votre mot de passe correspond à celui que vous avez défini lors de votre inscription. S’il s’agit de votre première connexion, vous pouvez définir votre mot de passe en suivant ce lien : <a href="'.$config['site_password_lost_url'].'?p='.$token_reinit_password.'">Créer mon mot de passe</a>.</li>
	</ul>');
	$email->addParagraph('Ceux-ci vous permettront de connaître votre solde de '.$name_point.', de visualiser les cadeaux dont vous pouvez bénéficier et de parrainer vos proches !');
	$email->addHtml('<p>Conservez bien cet email, il vous permettra de retrouver à tout moment vos codes d\'accès au site <a href="'.$config['site_url'].'">'.$config['site_url'].'</a>.</p>');
	$email->addParagraph('En cas de perte de ceux-ci, n\'hésitez pas à utiliser la fonction mot de passe oublié.');

	if ($config['tnt_id'] == 13) {
		$email->addParagraph('A très bientôt ! Laster arte !');
	}else{
		$email->addParagraph('A très bientôt !');
	}

	$email->addHtml( $config['email_html_footer'] );
	return $email->send();
}

/** Cette fonction permet de désactiver le systeme de rewards pour un utilisateur donnée, dans le cas où c'est requis dans la configuration
 *	@param int $usr_id Obligatoire, identifiant du client dont on souhaite désactiver les droits.
 *	@return resource Un résultat de requête MySQL
 */
function rwd_users_disabled( $usr_id ){

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		update rwd_users
		set rwu_date_uninscript = now()
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id.'
			and rwu_date_uninscript is null
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne si oui ou non un client participe au programme de fidélité, dans le cas où c'est requis dans la configuration
 *	@param int $usr_id Obligatoire, identifiant du client dont on souhaite connaitre les droits.
 *	@param $date Facultatif, date dans le passé à laquelle effectuer le contrôle. Si cet argument n'est pas renseigné, on contrôle la participation à la date/heure actuelle
 *	@return bool True si l'utilisateur participe au programme de fidélité, False dans le cas contraire
 */
function rwd_users_is_enabled( $usr_id, $date='' ){
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( trim($date) != '' && !isdateheure($date) ){
		return false;
	}

	$date = $date ? $date : date('Y-m-d H:i:s');

	global $config;

	if( !isset($config['rwd_register_required']) || !$config['rwd_register_required'] ){
		return true;
	}

	$res = ria_mysql_query('
		select 1
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id.'
			and rwu_date_inscript is not null
			and rwu_date_inscript <= "'.addslashes($date).'"
			and (rwu_date_uninscript is null or rwu_date_uninscript >= "'.addslashes($date).'")
	');

	return $res && ria_mysql_num_rows($res);
}

/** Cette fonction retourne si oui ou non un client a déjà participe au programme de fidélité mais ne l'ai plus
 *	@param int $usr_id Obligatoire, identifiant du client
 *	@return bool True si l'utilisateur a déja été inscript au programme de fidélité
 */
function rwd_users_has_been_enabled( $usr_id ){
	return rwd_users_exists($usr_id) && !rwd_users_is_enabled($usr_id);
}

/** Cette fonction permet de savoir si un client a déjà participé au programme de fidélité.
 *	@param int $usr_id Obligatoire, identifiant du client
 *	@return bool True si a déjà été inscrit (l'est peut-être encore), False dans le cas contraire
 */
function rwd_users_exists( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id;

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les différentes période d'inscription au programme de fidélité
 *	Par défaut cette liste est triée par date de début de période décroissant.
 *	@param int $usr_id Optionnel, identifiant du compte client
 *	@return resource Un résultat MySQL contenant :
 *				- usr_id			: identifiant du compte client
 *				- date_inscript		: date d'inscription
 *				- date_uninscript	: date de désinscription (peut être vide à la dernière inscript, cela signifie que le client est toujours inscript)
 *				- last_email_send	: date du dernier email envoyé dans le cadre du problème de fidélité
 */
function rwd_users_get( $usr_id=0 ){
	if (!is_numeric($usr_id) || $usr_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select rwu_usr_id as usr_id, rwu_date_inscript as date_inscript, rwu_date_uninscript as date_uninscript, rwu_last_email_send as last_email_send
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
	';

	if ($usr_id > 0) {
		$sql .= ' and rwu_usr_id = '.$usr_id;
	}

	$sql .= '
		order by rwu_date_inscript desc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de retourner la date de premiere inscription
 *	@param int $usr_id Obligatoire, identifiant du compte client
 *	@return la date de premiere inscription, False en cas d'erreur
 */
function rwd_users_get_first_inscription( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select min(rwu_date_inscript) as date
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id
		;

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$rwu_user = ria_mysql_fetch_assoc($res);
	return $rwu_user["date"];
}

/** Cette fonction permet de retourner la date de la dernière désinscription
 *	@param int $usr_id Obligatoire, identifiant du compte client
 *	@return la date de dernière désinscription, False en cas d'erreur
 */
function rwd_users_get_last_uninscription( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select max(rwu_date_uninscript) as date
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id
		;

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$rwu_user = ria_mysql_fetch_assoc($res);

	return $rwu_user["date"];

}

/** Cette fonction permet de retourner la date du dernier email envoyé
 *	@param int $usr_id Obligatoire, identifiant du client
 *	@return la date du dernier email envoyé, False en cas d'erreur
 */
function rwd_users_get_last_email_send( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select max(rwu_last_email_send) as date
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id
	;

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$rwu_user = ria_mysql_fetch_assoc($res);
	return $rwu_user["date"];

}

/** Cette fonction permet de mettre a jour la date du dernier email envoyé
 *	@param int $usr_id Obligatoire, identifiant du client
 *	@return bool True dans le cas ou cela a fonctioné , False en cas d'erreur
 */
function rwd_users_set_last_email_send( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		update rwd_users
		set rwu_last_email_send = now()
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id.'
			and rwu_date_uninscript is null
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'un compte client était bien inscript à une date donnée.
 * 	@param int $usr_id Obligatoire, identifiant du compte client
 * 	@param $date Obligatoire, date à contrôler
 * 	@return bool True si le compte était bien inscript à cette date, False dans le cas contraire
 */
function rwd_users_is_inscript_for_date($usr_id, $date){
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$date = dateheureparse($date);
	if( !isdateheure($date) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
			and rwu_usr_id = '.$usr_id.'
			and rwu_date_inscript <= "'.htmlspecialchars($date). '"
			and (rwu_date_uninscript is null or rwu_date_uninscript >= "'.addslashes($date).'")
	';

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/// @}

// \endcond

/** Cette fonction permet de savoir si un compte client a passé commande lorsqu'il participait au programme de fidélité
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@return bool True s'il a passé commande, False dans le cas contraire
 */
function rwd_users_have_order( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id<=0) {
		return false;
	}

	if (!rwd_users_is_enabled($usr_id)) {
		return false;
	}

	global $config;

	$r_exists = ria_mysql_query('
		select 1
		from rwd_users
		where rwu_tnt_id = '.$config['tnt_id'].'
		and rwu_usr_id = '.$usr_id.'
		and exists (
		    select 1
		    from ord_orders
		    where ord_tnt_id = '.$config['tnt_id'].'
		    	and ord_state_id in ('.implode( ', ', ord_states_get_ord_valid() ).')
		    	and ord_date >= rwu_date_inscript
		    	and (rwu_date_uninscript is null or ord_date <= rwu_date_uninscript)
		)
	');

	if (!$r_exists || !ria_mysql_num_rows($r_exists)) {
		return false;
	}

	return false;
}

/** \defgroup rewards_catalogs Gestion du catalogue offerts
 *	Les fonctions contenus dans ce modules permettent de configurer les produits disponible à l'achat avec des points de fidélités
 *	@{
 */

// \cond onlyria
/** Cette fonction permet d'ajouter un produit dans le catalog des produits disponibles avec des points de fidélité
 *	@param $prd_id Obligatoire, Identifiant du produit à proposer a l'achat.
 *	@param $points Obligatoire, Nombre de points de fidélités nécéssaire l'achat du produits.
 *	@param int $prf Obligatoire, NIdentifiant du profil utilisateur.
  *	@param $is_highlighting Optionnel, Définis sur le produit est à mettre en avant, par default à faux
 *	@param $pts_highlighting Optionnel, Définis le seuil de points à partir du quel le produit est à mettre en avant pour un utilisateur par default a 0
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function rwd_catalogs_add( $prd_id, $points, $prf, $is_highlighting=false, $pts_highlighting=0){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	if (!is_numeric($points) ) {
		return false;
	}

	if (!is_numeric($prf)) {
		return false;
	}

	if($is_highlighting && $pts_highlighting != 0  && !is_numeric($pts_highlighting)){
		return false;
	}

	global $config;

	if(rwd_catalogs_exists($prf, $prd_id)){
		return rwd_catalogs_update($prd_id, $points, $prf, $is_highlighting , $pts_highlighting);
	}

	$sql = '
		insert into rwd_catalogs
			(rwc_tnt_id, rwc_wst_id, rwc_prd_id, rwc_sell_points, rwc_prf_id, rwc_is_highlighting, rwc_pts_highlighting)
		values
			("'.$config['tnt_id'].'", "'.$config["wst_id"].'","'.$prd_id.'", "'.$points.'", "'.$prf.'", '.($is_highlighting ? '1' : '0').', "'.$pts_highlighting.'")
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour un produit dans le catalog des produits disponibles avec des points de fidélité
 *	@param $prd_id Obligatoire, Identifiant du produit à proposer a l'achat.
 *	@param $points Obligatoire, Nombre de points de fidélités nécéssaire l'achat du produits.
 *	@param int $prf Obligatoire, Identifiant du profil utilisateur.
 *	@param $is_highlighting Optionnel, Définis sur le produit est à mettre en avant, par default à faux
 *	@param $pts_highlighting Optionnel, Définis le seuil de points à partir duquel le produit est 0
 *	@return un resultat sql, false en cas d'erreur
 */
function rwd_catalogs_update( $prd_id, $points, $prf, $is_highlighting=false, $pts_highlighting=0){

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}
	if (!is_numeric($points) ) {
		return false;
	}
	if (!is_numeric($prf)) {
		return false;
	}

	if($is_highlighting && $pts_highlighting != 0 && !is_numeric($pts_highlighting)){
		return false;
	}

	global $config;

	$sql = '
		update rwd_catalogs
		set rwc_sell_points = '.$points.',
			rwc_is_highlighting = "'.($is_highlighting ? '1' : '0').'",
			rwc_pts_highlighting = "'.$pts_highlighting.'"
	    where rwc_tnt_id = '.$config['tnt_id'].'
			and rwc_wst_id = '.$config["wst_id"].'
			and rwc_prd_id = '.$prd_id.'
			and rwc_prf_id = '.$prf;

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer un produit dans le catalog des produits disponibles avec des points de fidélité
 *	@param $prd_id Obligatoire, Identifiant du produit que l'ont souhaite supprimer.
 *	@param int $prf Obligatoire, Identifiant du profil utilisateur.
 *	@return un resultat sql
 */
function rwd_catalogs_del( $prd_id, $prf){

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}
	if($prf && is_numeric($prf) || $prf >= 0){

	}else{
		return false;
	}

	global $config;

	$sql = '
		delete from rwd_catalogs
		where rwc_tnt_id = '.$config['tnt_id'].'
			and rwc_wst_id = '.$config['wst_id'].'
			and rwc_prd_id = '.$prd_id.'
			and rwc_prf_id = '.$prf;


	return ria_mysql_query($sql);
}
// \endcond

/** Cette fonction permet de récupérer un ou plusieurs produits disponibles avec des points de fidélités.
 *	@param $prf_id Obligatoire, identifiant du profil utilisateur.
 *	@param $prd_id Optionnel, identifiant du produit que l'ont souhaite retourner
 *	@return un resultat sql contenant :
 *				- rwc_tnt_id : identifiant locataire
 *				- rwc_wst_id : identifiant site
 *				- rwc_sell_points : nombre de points pour acheter le produit
 *				- rwc_is_highlighting : mise en avant dans la notification
 *				- rwc_pts_highlighting : nombre de points pour être mis en avant
 *				- rwc_prf_id : identifiant du profile
 *				- prd_id : identifiant du produit
 *				- prd_name : nom du produit
 *				- prd_ref : référence du produit
 */
function rwd_catalogs_get( $prf_id, $prd_id=0){
	if (!is_numeric($prf_id) || $prf_id <= 0) {
		return false;
	}

	if (!is_numeric($prd_id) || $prd_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select rwc_tnt_id, rwc_wst_id, rwc_prd_id, rwc_sell_points, rwc_is_highlighting, rwc_pts_highlighting, rwc_prf_id, prd_id, prd_name, prd_ref
		from rwd_catalogs
			join prd_products on prd_id = rwc_prd_id
		where rwc_tnt_id = '.$config['tnt_id'].'
			and rwc_wst_id = '.$config['wst_id'].'
			and prd_tnt_id = '.$config['tnt_id'].'
			and rwc_prf_id = '.$prf_id. '
	';

	if ($prd_id > 0) {
		$sql .= ' and rwc_prd_id = '.$prd_id;
	}

	$sql .='
		order by rwc_sell_points asc, prd_name  asc
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de verifier l'existence d'un produit dans le catalog des produits disponibles avec des points de fidélité
 *	@param int $prf Obligatoire, identifiant du profil utilisateur.
 *	@param $prd_id Obligatoire, identifiant du produit que l'ont souhaite connaitre l'existence.
 *	@return un resultat sql si la relation existe, False dans le cas contraire
 */
function rwd_catalogs_exists($prf, $prd_id){
	if (!is_numeric($prf) || $prf <= 0) {
		return false;
	}

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from rwd_catalogs
		where rwc_tnt_id = '.$config['tnt_id'].'
			and rwc_wst_id = '.$config['wst_id'].'
			and rwc_prf_id = '.$prf.'
			and rwc_prd_id = '.$prd_id.'
	';

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}

/** Cette fonction permet d'ajouter un produit du catalog des produits disponibles avec des points de fidélité à une commmande
 *	@param $ord_id Obligatoire, identifiant de la commande auquel on souhaite ajouter le produit
 *	@param $prd_id Obligatoire, identifiant du produit que l'ont souhaite ajouter.
 *  @param string $name Obligatoire, nom du produit que l'ont souhaite ajouter.
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur qui ajoute le produit à sa commande .
 *	@param $prf_id Obligatoire, profil de l'utilisateur qui ajoute le produit à sa commande .
 * 	@param $qte Optionnel, spécifie le nombre de produit a ajouter a la commande
 *
 *	@return int L'identifiant de la ligne de commande pour cet ajout (attention le numéro de ligne peut être égal à zéro), False en cas d'erreur
 */
function rwd_catalogs_add_to_cart( $ord_id, $prd_id, $name, $usr_id, $prf_id, $qte=1 ){
	if(!is_numeric($ord_id) || $ord_id <= 0){
		return  false;
	}

	if(!is_numeric($prd_id) || $prd_id <= 0){
		return  false;
	}

	if( trim($name) == ""){
		return  false;
	}

	if(!is_numeric($usr_id) || $usr_id <= 0){
		return  false;
	}

	if(!is_numeric($prf_id) || $prf_id <= 0){
		return  false;
	}

	if (!is_numeric($qte) || $qte <= 0) {
		return false;
	}

	$res_prd = rwd_catalogs_get($prf_id, $prd_id);
	if(!$res_prd && !ria_mysql_num_rows($res_prd)){
		return false;
	}

	$prd = ria_mysql_fetch_assoc( $res_prd );

	if (($prd["rwc_sell_points"] * $qte) > gu_users_get_rewards_balance($usr_id)) {
		return false;
	}

	$tva = _TVA_RATE_DEFAULT;
	$r_prd_tva = prc_tvas_get(0, false, $prd_id);
	if ($r_prd_tva && ria_mysql_num_rows($r_prd_tva)) {
		$prd_tva = ria_mysql_fetch_assoc($r_prd_tva);
		$tva = $prd_tva['rate'];
	}

	$line = ord_products_add_free( $ord_id, $prd["prd_ref"], $name, 0, $qte, null,'', $tva, 0, 0, false, 0, 0, false, false, true, false, $prd_id );
	if ($line===false) {
		return false;
	}

	if (!ord_products_set_sell_points($ord_id, $prd_id, $line , $prd["rwc_sell_points"])) {
		ord_products_del( $ord_id, $prd_id, $line );
		return false;
	}

	return $line;
}

/** Cette fonction permet de modifier un produit du catalog des produits disponibles avec des points de fidélité dans une commande
 *	@param $ord_id Obligatoire, identifiant de la commande auquel on souhaite modifier le produit
 *	@param $prd_id Obligatoire, identifiant du produit que l'ont souhaite modifier.
  *	@param int $usr_id Obligatoire, identifiant de l'utilisateur qui modifie le produit de sa commande .
 *	@param $prf_id Obligatoire, profil de l'utilisateur qui modifie le produit de sa commande .
 * 	@param $qte Obligatoire, spécifie le nombre de produit a modifier de la commande
 *	@param $prd_line Obligatoire, numéro de ligne spécifique à mettre à jour
 *
 *	@return bool true si la modification à fonctionné, False dans le cas contraire
 */
function rwd_catalogs_update_to_cart($ord_id, $prd_id, $usr_id, $prf_id, $qte, $prd_line){
	if(!is_numeric($ord_id) || $ord_id <= 0){
		return  false;
	}

	if(!is_numeric($prd_id) || $prd_id <= 0){
		return  false;
	}

	if(!is_numeric($usr_id) || $usr_id <= 0){
		return  false;
	}

	if(!is_numeric($prf_id) || $prf_id <= 0){
		return  false;
	}

	if (!is_numeric($qte) || $qte <= 0) {
		return false;
	}

	$res_prd_catalog = rwd_catalogs_get($prf_id, $prd_id);
	if(!$res_prd_catalog && !ria_mysql_num_rows($res_prd_catalog)){
		return false;
	}

	$prd_catalog = ria_mysql_fetch_assoc( $res_prd_catalog );

	// Récupère le coût du panier sans le coût du produit actuel.
	$actual_sum = ord_orders_sum_points($ord_id);
	$actual_prd_sum = ord_orders_sum_points($ord_id,$prd_id);
	$solde = $actual_sum - $actual_prd_sum;

	if (($solde + $prd_catalog["rwc_sell_points"] * $qte) > gu_users_get_rewards_balance($usr_id)) {
		return false;
	}

	return ord_products_update_free( $ord_id, $prd_catalog['prd_ref'], $prd_catalog['prd_name'], 0, $qte, false, false, false, 0, 0, 0, true, false, 0, $prd_line, $prd_catalog["rwc_sell_points"] );
}

/// @}

// \cond onlyria

/** \defgroup rewards_export Export des statistiques du système de fidélité
 *	Les fonctions contenues dans ce module permettent l'export des statistiques concernant l'utilisation du système de fidélité
 *	@{
 */

/** Cette fonction permet d'exporter des informations relatives au systeme de fidélité. Elle génère
 * 	un fichier CSV qui devra ensuite être envoyé à l'utilisateur.
 *	@param int $usr Optionel, Identifiant du client duquel on souhaite exporter les données
 *	@param string $date_start Optionel, Date de début de la période concerné par l'export
 *	@param string $date_end Optionel, Date de fin de la période concerné par l'export
 *	@return bool true si la génération du fichier CSV a réussi, False dans le cas contraire
 */

function rwd_export_stats( $usr=0, $date_start=false, $date_end=false ){

	if( $usr && (!is_numeric($usr) || $usr < 0) ){
		return false;
	}

	if($date_start && !isdate($date_start)){
		return false;
	}

	if($date_end && !isdate($date_end)){
		return false;
	}

	$date_end = dateheureparse($date_end);
	$date_start = dateheureparse($date_start);

	global $config;

	$rewards = stats_rewards_get($usr,0, 0, 0, false, $date_start, $date_end);

	if(!$rewards || ! ria_mysql_num_rows($rewards)){
		return false;
	}

  	$csv = array();
	$index = 0;

	$file_csv = $config['doc_dir'].'/export-rewards-'.$_SESSION['usr_id'].'.csv';

	$handle = fopen( $file_csv, 'w' );
  	$headers = array( _("Date"), _("Client"), _("Action"), _("Points gagnés / perdus"), _("Valeur en Euros"), _("Date limite d'utilisation") );

  	fputcsv($handle, $headers, ";");

	while( $rwd = ria_mysql_fetch_assoc($rewards)){
		$index++;
		// Définition nom client :
		$amount = 0;
		if( trim($rwd['convert'])!='' ){
			$ar_convert = preg_split('/\//', $rwd['convert']);
			$convert = $ar_convert[0] / $ar_convert[1];
			$amount = $rwd['pts']*$convert;
		}

		// Récupère le compte client
		if( !isset($ar_users[$rwd['usr']]) ){
			$rusr = gu_users_get( $rwd['usr'] );
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$ar_users[ $rwd['usr'] ] = ria_mysql_fetch_array( $rusr );
			}
		}

		// Définition du nom client
		if( isset($ar_users[ $rwd['usr'] ]) ){
			$user_name = ( $ar_users[ $rwd['usr'] ]['ref'] ? $ar_users[ $rwd['usr'] ]['ref'].' - ' : '' ).$rwd['lastname'].' '.$rwd['firstname'].' '.$rwd['society'];
		}

		if( $rwd['godchild'] ){

			$user_name = _('Parrainage de ');

			if( !isset($ar_users[$rwd['godchild']]) ){
				$rusr = gu_users_get( $rwd['godchild'] );
				if( $rusr && ria_mysql_num_rows($rusr) ){
					$ar_users[ $rwd['godchild'] ] = ria_mysql_fetch_array( $rusr );
				}
			}

			if( isset($ar_users[$rwd['godchild']]) ){
				$godchild = $ar_users[ $rwd['godchild'] ];
				$user_name .= ( $ar_users[$rwd['godchild']]['ref'] ? $ar_users[$rwd['godchild']]['ref'].' - ' : '' ).$godchild['adr_lastname'].' '.$godchild['adr_firstname'];
			}
		}

		$fields = array(
			$rwd['date'],
			$user_name,
			$rwd['rwa']>0 ? ($amount<0 ? $rwd['contrary'] : $rwd['name']) : $rwd['name'],
			number_format( $rwd['pts'], 0, ',', ' ' ),
			number_format( $amount, 0, ',', ' ' ),
			$rwd['date_limit']
		);
		fputcsv($handle, $fields, ";");
	}

	fclose($handle);
	return true;
}

/// @}

// \endcond

// \cond onlyria

/** \defgroup model_rewards_workqueue File d'attente pour les points de fidélité
 *	Dans le cas de la synchronisation des points de fidélité, il se peut que le compte client ne soit pas encore existant au moment de l'envoi des points de fidélité, on stock donc ces derniers afin qui *  soient ajoutés plus tard au compte client (lors de sa création).
 *	@{
 */

/** Cette fonction permet de stocker des points de fidélité en attente de création d'un compte client.
 *	L'un de ces deux paramètres doit obligatoirement être donné : $usr_email, $usr_ref
 *	@param $pts Obligatoire, nombre de points à attribuer au compte client
 *	@param string $usr_email Optionnel, adresse e-mail d'un compte client n'existant pas encore
 *	@param $usr_ref Optionnel, référence d'un compte client n'existant pas encore
 *	@param $rwa_id Optionnel, identifiant d'une action (voir rwd_actions)
 *	@param $action Optionnel, nom de l'action (obligatoire si rwa_id n'est pas donné)
 *	@param int $cls_id Optionnel, identifiant de la classe d'objet
 *	@param $obj Optionnel, identifiant de l'objet qui subit l'action
 *	@param $cancel Optionnel, par défaut à false, mettre true pour annuler des points
 *	@param string $date_limit Optionnel, date limite d'utilisation des points de fidélité (false par défaut = aucune limite)
 *	@param string $date_add Optionnel, date à laquelle les points doivent être ajouté
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire ou bien si l'adresse mail existe déjà
 */
function tsk_rewards_add( $pts, $usr_email='', $usr_ref='', $rwa_id=0, $action='', $cls_id=0, $obj=0, $cancel=false, $date_limit=false, $date_add=false ){
	if (!is_numeric($pts) || $pts <= 0) {
		return false;
	}

	if (trim($usr_email) != '') {
		if (!isemail($usr_email)) {
			return false;
		}
	}elseif (trim($usr_ref)=='') {
		return false;
	}

	if (gu_users_exists(0, 0, $usr_email, $usr_ref)) {
		return false;
	}

	if (!is_numeric($rwa_id) || $rwa_id<0) {
		return false;
	}

	if (!$rwa_id && trim($action) == '') {
		return false;
	}

	$obj = control_array_integer($obj, false);
	if ($obj === false) {
		return false;
	}

	if ($date_limit!==false) {
		if (isdate($date_limit)) {
			$date_limit = $date_limit.' 23:59:59';
		}

		if (!isdateheure($date_limit)) {
			return false;
		}

		if (strtotime($date_limit) < time()) {
			return false;
		}
	}

	if ($date_add !== false) {
		if (isdate($date_add)) {
			$date_add = $date_add.' 00:00:00';
		}

		if (!isdateheure($date_add)) {
			return false;
		}
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'tsk_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'tsk_usr_email';
	$values[] = trim($usr_email) != '' ? '"'.addslashes( $usr_email ).'"' : 'null';

	$fields[] = 'tsk_usr_ref';
	$values[] = trim($usr_ref) != '' ? '"'.addslashes( $usr_ref ).'"' : 'null';

	$fields[] = 'tsk_rwa_id';
	$values[] = $rwa_id;

	$fields[] = 'tsk_action';
	$values[] = '"'.addslashes( $action ).'"';

	$fields[] = 'tsk_cls_id';
	$values[] = $cls_id;

	for ($i=0; $i < 3; $i++) {
		$fields[] = 'tsk_obj_id_'.$i;
		$values[] = array_key_exists($i, $obj) ? $obj[ $i ] : 0;
	}

	$fields[] = 'tsk_points';
	$values[] = $pts;

	$fields[] = 'tsk_cancel';
	$values[] = ($cancel ? '1' : '0');

	if ($date_limit!==false) {
		$fields[] = 'tsk_date_limit';
		$values[] = '"'.addslashes( dateheureparse( $date_limit ) ).'"';
	}

	if ($date_add!==false) {
		$fields[] = 'tsk_date_add';
		$values[] = '"'.addslashes( dateheureparse( $date_add ) ).'"';
	}

	$fields[] = 'tsk_date_created';
	$values[] = 'now()';

	$sql = '
		insert into tsk_rewards
			('.implode( ', ', $fields ).')
		values
			('.implode( ', ', $values ).')
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'appliquer les points de fidélité en attente à un compte client (via son adresse mail ou via sa référence)
 *	@param string $usr_email Obligatoire, adresse mail du compte client
 *	@param $usr_ref Optionnel, référence du compte client
 *	@return bool True en cas de succès, False si le paramètre obligatoire est omis ou faux
 */
function tsk_rewards_apply( $usr_email='', $usr_ref='' ){
	if (trim($usr_email) == '' && trim($usr_ref) == '') {
		return false;
	}

	global $config;

	if( !isset($config['rwd_reward_actived']) || !$config['rwd_reward_actived'] ){
		return false;
	}

	$sql = '
		select usr_id as id, usr_prf_id as prf_id
		from gu_users
		where (usr_tnt_id = '.$config['tnt_id'].' or usr_tnt_id = 0)
			and (
	';
	if (trim($usr_email) != '') {
		$sql .= ' usr_email = "'.addslashes( $usr_email ).'"';
	}
	if (trim($usr_email) != '' && trim($usr_ref) != '') {
		$sql .= ' or ';
	}
	if (trim($usr_ref) != ''){
		$sql .= ' usr_ref = "'.addslashes( $usr_ref ).'"';
	}
	$sql .= '
			and usr_date_deleted is null
		)
	';

	$r_user = ria_mysql_query( $sql );
	if (!$r_user || !ria_mysql_num_rows($r_user)) {
		return false;
	}

	$r_tsk = false;

	if (trim($usr_email) != '') {
		$r_tsk = tsk_rewards_get( $usr_email );
	}

	if (trim($usr_ref) != '') {
		if (!$r_tsk || !ria_mysql_num_rows($r_tsk)) {
			$r_tsk = tsk_rewards_get( '', $usr_ref );
		}
	}

	if (!$r_tsk) {
		return false;
	} elseif (!ria_mysql_num_rows($r_tsk)) {
		return true;
	}

	$user = ria_mysql_fetch_assoc( $r_user );

	while ($tsk = ria_mysql_fetch_assoc($r_tsk)) {
		$obj = false;
		if ($tsk['cls_id']) {
			$obj = array( $tsk['obj_id_0'], $tsk['obj_id_1'], $tsk['obj_id_2'] );
		}

		$points = is_numeric($tsk['points']) && $tsk['points'] > 0 ? $tsk['points'] : false;

		$days_limit = -1;
		if (isdateheure($tsk['date_limit'])) {
			$time = strtotime( $tsk['date_limit'] ) - strtotime( date('Y-m-d 23:59:59') );
			$days_limit = round( $time / (60 * 60 * 24) );
		}

		$res = stats_rewards_add( $user['id'], $user['prf_id'], $tsk['rwa_id'], $tsk['cancel'], $tsk['action'], $tsk['cls_id'], $obj, $points, $ratio=false, $convert=false, $days_limit, $sponsor=0, $tsk['date_add']);

		if (!$res) {
			return false;
		}

		if (!tsk_rewards_set_completed($tsk['id'])) {
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de récupérer les actions à réaliser sur la gestion des points de fidélité
 *	@param string $usr_email Optionnel, adresse mail d'un compte client
 *	@param string $usr_ref Optionnel, référence d'un compte client
 *	@param bool $completed Optionnel, par défaut seules les actions non traitées sont retournées, mettre null pour toutes les avoir ou true pour n'avoir que celles traitées
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de la tâche
 *				- email : adresse email
 *				- usr_ref : référence client
 *				- rwa_id
 *				- action
 *				- cls_id
 *				- obj_id_0
 *				- obj_id_1
 *				- obj_id_2
 *				- cancel
 *				- points
 *				- date_created
 *				- date_limit
 *				- date_completed
 *				- date_add
 */
function tsk_rewards_get( $usr_email='', $usr_ref='', $completed=false ){
	if (trim($usr_email) != '') {
		if (!isemail($usr_email)) {
			return false;
		}
	}

	global $config;

	$sql = '
		select
			tsk_id as id, tsk_usr_email as email, tsk_usr_ref as usr_ref, tsk_rwa_id as rwa_id, tsk_action as action, tsk_cls_id as cls_id, tsk_obj_id_0 as obj_id_0, tsk_obj_id_1 as obj_id_1, tsk_obj_id_2 as obj_id_2,
			tsk_cancel as cancel, tsk_points as points, tsk_date_created as date_created, tsk_date_limit as date_limit, tsk_date_completed as date_completed, tsk_date_add as date_add
		from tsk_rewards
		where tsk_tnt_id = '.$config['tnt_id'].'
	';

	if (trim($usr_email) != '') {
		$sql .= ' and tsk_usr_email = "'.addslashes( $usr_email ).'"';
	}

	if (trim($usr_ref) != '') {
		$sql .= ' and tsk_usr_ref = "'.addslashes( $usr_ref ).'"';
	}

	if ($completed!==null) {
		if ($completed) {
			$sql .= ' and tsk_date_completed is not null';
		}else{
			$sql .= ' and tsk_date_completed is null';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de marquer une actions sur la gestion des points de fidélité comme faite
 *	@param int $tsk_id Obligatoire, identifiant de la tâche
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function tsk_rewards_set_completed( $tsk_id ){
	if (!is_numeric($tsk_id) || $tsk_id<=0) {
		return false;
	}

	global $config;

	return ria_mysql_query('
		update tsk_rewards
		set tsk_date_completed = now()
		where tsk_tnt_id = '.$config['tnt_id'].'
			and tsk_id = '.$tsk_id.'
	');
}
/// @}


// \cond onlyria
/** Détermine le montant total, la marge, le montant moyen des commandes facturées
 *	@param string $date_start Optionnel, date de début de la période
 *	@param string $date_end Optionnel, date de fin de la période
 *	@param int $inv_id Optionnel, identifiant d'une facture sur laquelle filtrer le résultat
 *	@param $users Facultatif, permet de filtrer les factures sur un ou plusieurs utilisateurs, accepte un tableau d'identifiants, attention dans le cas d'utilisateur parent il n'ira pas chercher les utilisateurs enfants.
 *	@param $min Facultatif, minimum de facture sous cette forme array('amount'=>minimum, 'is_ttc'=>true/false)
 *	@param int $seller_id Facultatif, identifiant du représentant de la ou des facture(s)
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau comprenant les colonnes suivantes :
 *				- volume : nombre de commande (Web)
 *				- total_ht : total ht des commandes
 *				- total ttc : total ttc des commandes
 *				- average_ht : panier moyen en ht
 *				- average_ttc : panier moyen en ttc
 *				- marge : marge brute
 */
function rwd_stats_get_average_totals( $date_start=false, $date_end=false,  $inv_id=0, $users=false, $min=0, $seller_id=0){

	{ // contrôles

		if( $date_start !== false && !isdateheure($date_start) ){
			return false;
		}

		if( $date_end !== false && !isdateheure($date_end) ){
			return false;
		}

		if( is_array($inv_id) ){
			foreach( $inv_id as $one_id ){
				if( !is_numeric($one_id) || $one_id <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($inv_id) && $inv_id > 0 ){
			$inv_id = array($inv_id);
		}else{
			$inv_id = array();
		}


		if( is_array($users) ){
			foreach( $users as $one_usr ){
				if( !is_numeric($one_usr) || $one_usr <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($users) && $users > 0 ){
			$users = array($users);
		}else{
			$users = array();
		}


		if( is_array($seller_id) ){
			foreach( $seller_id as $one_sll ){
				if( !is_numeric($one_sll) || $one_sll <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($seller_id) && $seller_id > 0 ){
			$seller_id = array($seller_id);
		}else{
			$seller_id = array();
		}

	}

	global $config;

	$sql = '
		select
			seller.usr_id as usr_id,
			sum(stats_points) as points
	 	 	from
		 		stats_rewards
				join ord_inv_products on stats_obj_id_0=prd_inv_id and stats_obj_id_1=prd_id and stats_obj_id_2=prd_line_id and prd_tnt_id = '.$config['tnt_id'].'
				join ord_invoices on prd_inv_id = inv_id and inv_tnt_id = '.$config['tnt_id'].'
				join gu_users as customer on inv_usr_id = customer.usr_id and customer.usr_tnt_id = '.$config['tnt_id'].'
				left join gu_users as seller on ifnull(prd_seller_id, customer.usr_seller_id) = seller.usr_seller_id and seller.usr_prf_id = '.PRF_SELLER.' and seller.usr_tnt_id = '.$config['tnt_id'].'
	';

	$sql .= '
			where
				stats_cls_id = '.CLS_INV_PRODUCT.'
				and stats_tnt_id = '.$config['tnt_id'].'
				and inv_masked = 0
	';

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(stats_datetime) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and stats_datetime >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(stats_datetime) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and stats_datetime <= "'.dateheureparse($date_end).'"';
		}
	}

	if( sizeof($seller_id) ){
		$sql .= ' and usr_seller_id in ('.implode(', ', $seller_id).')';
	}

	$sql .= '
		group by seller.usr_id
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return $res;

}
// \endcond

// \endcond
/// @}

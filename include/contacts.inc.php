<?php

/** \defgroup contacts Contacts
 *	\ingroup messages
 *
 *	Ce module contient les fonctions utilisées pour les prises de contacts.
 *	Son fonctionnement est régi par les paramètres de configuration $config[ 'email_contact' ] et $config['email_bcc'].
 *
 *	@{
 */

require_once('email.inc.php');
require_once('users.inc.php');
require_once('cfg.emails.inc.php');
require_once('antispam.inc.php');
require_once('messages.inc.php');

/**	Envoi une demande de contact à l'adresse pointée par $config['email_contact'].
 *	Si une adresse est définie dans $config['email_bcc'], une copie cachée du message
 *	est envoyée à cette adresse.
 *
 *	@param string $firstname Obligatoire, Prénom de l'émetteur
 *	@param string $lastname Obligatoire, Nom de famille de l'émetteur
 *	@param string $society Obligatoire, Entreprise de l'émetteur
 *	@param string $email Obligatoire, Adresse email de l'émetteur
 *	@param string $phone Obligatoire, Numéro de téléphone de l'émetteur
 *	@param string $subject Obligatoire, Sujet du message
 *	@param string $body Obligatoire, Corps du message
 *	@param $public Facultatif, détermine si le message est public
 *	@param $dest Facultatif, destinataire du mail, par défaut on récupère la configuration d'email "site-contact"
 *	@param $cc Facultatif, destinataire en copie, par défaut on récupère la configuration d'email "site-contact"
 *	@param $bcc Facultatif, destinataire en copie cachée, par défaut on récupère la configuration d'email "site-contact"
 *	@param int $prd_id Facultatif, identifiant d'un article concerné par le message de contact
 *	@param $type Facultatif, surcharge l'information sur le type de message (par défaut un contact)
 *	@param $files Facultatif, tableau d'identifiants de pièces jointes à ajouter au message de contact
 *	@param int $ord_id Facultatif, identifiant d'une commande sur laquelle porte le message
 *	@param $fields Facultatif, permet d'inclure des champs avancé à la sauvegarde et à l'envoi du message de contact : array( 'fld_id' => 'valeur à enregistrer', ...)
 *	@param int $cat_id Facultatif, identifiant d'une catégorie de produits à laquelle le message doit être associé
 *	@param $msg_body Facultatif, message sauvegardé en base de données
 *	@param $cfg_email_code Optionnel, code de la config e-mail utilisé pour l'envoi (par défaut - site-contact)
 *	@param $state Optionnel, détermine l'état du message (approuvé => 1 ou non => 0 ou auccun => -1)
 *	@param	$other	Optionnel, tableau de paramètres supplémentaires
 *
 *	@return int L'identifiant du message juste ajouté en cas de succès, False en cas d'échec
 */
function contacts_send( $firstname, $lastname, $society, $email, $phone, $subject, $body, $public=false, $dest=false, $cc=false, $bcc=false, $prd_id=0, $type='CONTACT', $files=array(), $ord_id=null, $fields=false, $cat_id=0, $msg_body='', $cfg_email_code='site-contact', $state=false, $other=false){
    global $config;

    if( $fields !== false ){
        if( !is_array($fields) || !sizeof($fields) ){
            return false;
        }

        foreach( $fields as $fld_id=>$value ){
            if( !is_numeric($fld_id) || $fld_id <= 0 ){
                return false;
            }
        }
    }


    $firstname = trim($firstname);
    $lastname = trim($lastname);
    $society = trim($society);
    $email = trim($email);
    $phone = trim($phone);
    $subject = trim($subject);
    $body = trim($body);
    $public = $public ? 1 : 0;

    if( (!$firstname && !$lastname && !$society) || !$email || !$body ){
        return false;
    }

    $files = control_array_integer( $files, false );
    if( $files === false ){
        return false;
    }

    $firstname = ucfirst($firstname);
    $lastname = ucfirst($lastname);
    $society = ucfirst($society);
    $email = strtolower($email);

    $res = add_message( $firstname, $lastname, $society, $email, $phone, $subject, (trim($msg_body) != '' ? $msg_body : $body), $type, '', $public, 0, $prd_id, '', '', '', $cat_id, 0, $state, true, null, false, array($dest), array($cc, $bcc), null, null, null, false, null, $ord_id, $cfg_email_code );

    //Si le message n'est pas spammer
    if( $res ){
        if( sizeof($files) ){
            messages_files_update_cnt( $files, $res );
        }

        if( $fields !== false ){
            foreach( $fields as $fld_id=>$value ){
                fld_object_values_set( $res, $fld_id, $value );
            }
        }

        contacts_send_mail( $res, $public, $dest, $cc, $bcc, (sizeof($files) ? $files : null), $fields, $body, $cfg_email_code, false, $other );
    }

    return $res;

}

/**	Envoi du mail de contact.
 *	@param int $id Obligatoire, identifiant du message à envoyer
 *	@param $public Facultatif, paramètre à déterminer
 *	@param $dest Falcultatif, destinataire du mail, par défaut on récupère la configuration d'email "site-contact"
 *	@param $cc Falcultatif, destinataire en copie, par défaut on récupère la configuration d'email "site-contact"
 *	@param $bcc Facultatif, destinataire en copie cachée, par défaut on récupère la configuration d'email "site-contact"
 *	@param $piece Facultatif, Tableau d'identifiants de pièces jointes
 *	@param $fields Facultatif, permet d'inclure des champs avancé à l'envoi du message de contact : array( 'fld_id' => 'valeur à enregistrer', ...)
 *	@param string $body Falcultatif, message envoyé par mail (par défaut il s'agit de celui en base de données)
 *	@param $cfg_email_code Optionnel, code de la config e-mail utilisé pour l'envoi (par défaut - site-contact)
 *	@param $reply_to Facultatif, address pour "Repondre à", par défaut on récupère la configuration d'email "site-contact"
 *	@param $other	Optionnel, tableau de paramètres supplémentaires
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function contacts_send_mail( $id, $public=false, $dest=false, $cc=false, $bcc=false, $piece=null, $fields=false, $body='', $cfg_email_code='site-contact', $reply_to=false, $other=false ){

    if( $fields !== false ){
        if( !is_array( $fields ) || !sizeof( $fields ) ){
            return false;
        }

        foreach( $fields as $fld_id => $value ){
            if( !is_numeric( $fld_id ) || $fld_id <= 0 ){
                return false;
            }
        }
    }

    $rmsg = messages_get( 0, '', 0, $id );
    if( !$rmsg || !ria_mysql_num_rows($rmsg) )
        return false;

    $msg = ria_mysql_fetch_array( $rmsg );
    global $config;

    // Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
    $rcfg = cfg_emails_get($cfg_email_code);
    if( !ria_mysql_num_rows($rcfg) ) return false;

    $cfg = ria_mysql_fetch_array($rcfg);

    $mail = new Email();
    $http_host_ria = $config['backoffice_url'];
    if(!empty($config['bo_url'])){
        $http_host_ria = $config['bo_url'];
    }
    if( isemail($msg['email']) ){
        if( !isset($config['email_used_reply_to']) || $config['email_used_reply_to'] ){
            if( $cfg['from'] ){
                $mail->setFrom($cfg['from']);
            } else {
                $mail->setFrom('<EMAIL>');
            }
            $mail->setReplyTo( $msg['email'] );
        }else{
            $mail->setFrom( '"'.$msg['firstname'].' '.$msg['lastname'].'" <'.$msg['email'].'>' );
        }
    }else{
        $mail->setFrom( $cfg['from'] );
    }

    $mail->addTo( trim($dest) ? $dest : $cfg['to'] );

    if( $cc === false && trim($cfg['cc']) != '' ) {
        $mail->addCc( $cfg['cc'] );
    } elseif( trim($cc)!='' ){
        $mail->addCc( $cc );
    }

    if( $bcc === false && trim($cfg['bcc']) != '' ) {
        $mail->addBcc( $cfg['bcc'] );
    } elseif( trim($bcc)!='' ){
        $mail->addBcc( $bcc );
    }

    if( $reply_to === false && trim($cfg['reply-to']) != '' ) {
        $mail->setReplyTo( $cfg['reply-to'] );
    } elseif( trim($reply_to)!='' ){
        $mail->setReplyTo( $reply_to );
    }

    if( $msg['subject'] ){
        $mail->setSubject( $msg['subject'] );
    }else{
        $mail->setSubject( 'Contact '.$config['site_name'] );
    }
    $introduction = i18n::get('Le message ci-dessous a été émis par la personne suivante :', 'EMAIL');
    $other = is_array($other) ? $other : [];

    $other['user_account'] = array_key_exists('user_account', $other) && is_bool($other['user_account']) ? $other['user_account'] : true;
    $other['system_details'] = array_key_exists('system_details', $other) && is_bool($other['system_details']) ? $other['system_details'] : true;
    $other['introduction'] = array_key_exists('introduction', $other) && is_string($other['introduction']) ? $other['introduction'] : $introduction;

    if( $other['system_details'] ){
        $mail->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
    }

    $mail->addHtml( $config['email_html_header'] );
    $mail->addParagraph( $other['introduction'] );

    $mail->openTable();

    $mail->openTableRow();
    $mail->addCell( 'Nom :' );
    $mail->addCell( $msg['lastname'] );
    $mail->closeTableRow();

    $mail->openTableRow();
    $mail->addCell( 'Prénom :' );
    $mail->addCell( $msg['firstname'] );
    $mail->closeTableRow();

    if( isset($msg['society']) && trim($msg['society']) != '' ){
        $mail->openTableRow();
        $mail->addCell( 'Entreprise :' );
        $mail->addCell( $msg['society'] );
        $mail->closeTableRow();
    }

    $mail->openTableRow();
    $mail->addCell( 'Email :' );
    $mail->addCell( isemail($msg['email']) ? '<a href="mailto:'.$msg['email'].'">'.$msg['email'].'</a>' : $msg['email'] );
    $mail->closeTableRow();

    $mail->openTableRow();
    $mail->addCell( 'Téléphone :' );
    $mail->addCell( $msg['phone'] );
    $mail->closeTableRow();


    if( $other['user_account'] ){
        $mail->openTableRow();
        $mail->addCell( 'Compte client :' );
        $usr_id = 'null';
        $rusr = gu_users_get( 0, strtolower2(trim($msg['email'])) );
        $cellContent = 'Aucun compte client enregistré avec cette adresse email';

        if( ria_mysql_num_rows($rusr) ){
            $usr = ria_mysql_fetch_assoc($rusr);
            $usr_id = $usr['id'];
            $cellContent = '<a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr['id'].'">';
            $cellContent .= '<img class="sync" src="https://'.$http_host_ria.'/admin/images/sync/'.( $usr['is_sync'] ? 1 : 0 ).'.svg" title="'.( $usr['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />';
            $cellContent .= ( $usr['ref'] ? $usr['ref'] : $usr['id'] ).'</a>';
        }
        $mail->addCell( $cellContent );

    }
    $mail->closeTableRow();

    if( $fields !== false ){
        foreach( $fields as $fld_id=>$value ){
            $fld_name =  fld_fields_get_name( $fld_id );
            $f = false;
            $fields = fld_fields_get($fld_id);
            if( $fields && ria_mysql_num_rows($fields)) {
                $f = ria_mysql_fetch_assoc($fields);
            }

            // Contrôle spécifique aux listes de choix à sélection unique
            if( $f && $f['type_id']==FLD_TYPE_SELECT ){
                // Résoud l'identifiant de valeur
                $value = fld_restricted_values_get_name( $value );
            }

            // Contrôle spécifique aux listes de choix à sélection multiple
            if( $f && $f['type_id']==FLD_TYPE_SELECT_MULTIPLE || $f['type_id']==FLD_TYPE_SELECT_HIERARCHY ){
                $value = explode( ',', $value );
                $names = array();
                foreach( $value as $v ) {
                    $names[] = fld_restricted_values_get_name(trim($v));
                }
                $value = implode( $names, ', ' );
            }

            if( trim($fld_name) != '' ){
                $mail->openTableRow();
                $mail->addCell( $fld_name.' :' );
                $mail->addCell( $value );
                $mail->closeTableRow();
            }
        }
    }

    if( is_numeric($msg['ord_id']) && $msg['ord_id'] > 0 ){
        $rorder = ord_orders_get( 0, $msg['ord_id'] );

        if( $rorder && ria_mysql_num_rows($rorder) ){
            $order = ria_mysql_fetch_assoc( $rorder );

            switch( $order['state_id'] ){
                case _STATE_BASKET: $label = 'Panier'; break;
                case _STATE_BASKET_SAVE: $label = 'Panier enregistré'; break;
                case _STATE_DEVIS: $label = 'Devis'; break;
                default: $label = 'Commande'; break;
            }

            $mail->openTableRow();
            $mail->addCell( $label.' :' );
            $mail->addCell( '<a href="https://'.$http_host_ria.'/admin/orders/order.php?state=0&amp;ord='.$msg['ord_id'].'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=contact&amp;utm_content=show-order">N°'.$msg['ord_id'].'</a>' );
            $mail->closeTableRow();
        }
    }

    if( is_numeric($msg['prd_id']) && $msg['prd_id'] > 0 ){
        $prd_name = prd_products_get_name($msg['prd_id']);
        if (trim($prd_name)) {
            $mail->openTableRow();
            $mail->addCell( 'Produit :' );
            $mail->addCell( '<a href="https://'.$http_host_ria.'/admin/catalog/product.php?cat=0&amp;prd='.$msg['prd_id'].'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=contact&amp;utm_content=show-order">'.htmlspecialchars($prd_name).'</a>' );
            $mail->closeTableRow();
        }
    }

    $mail->openTableRow();
    $mail->addCell( nl2br( trim($body) != '' ? $body : $msg['body'] ), 'left', 2 );
    $mail->closeTableRow();

    if( $other['system_details'] ){

        $mail->openTableRow();
        if($public)
            $mail->addCell( nl2br('<a href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$id.'&amp;action=new-review-site&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=contact&amp;utm_content=move-review-site">Ajouter ce message aux avis site</a>'), 'left', 2 );
        else
            $mail->addCell( nl2br('Le contact n\'a pas autorisé la publication du message'), 'left', 2 );

        $mail->openTableRow();
        // if( ria_mysql_num_rows($rusr) ){
        // $mail->addCell( nl2br('<a href="https://app.fr/admin/customers/edit.php?usr='.$usr['id'].'&prf=&page=1&tab=contacts&cnt='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=contact&amp;utm_content=user-contacts#rep-'.$id.'">Répondre au message</a>.'), 'left', 2 );
        // } else {
        $mail->addCell( nl2br('<a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type='.htmlspecialchars($msg['type']).'&have-rep=0&cnt='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=contact&amp;utm_content=moderation-cnt#rep-'.$id.'">Répondre au message</a>.'), 'left', 2 );
        // }
        $mail->closeTableRow();
    }

    $mail->closeTable();

    $mail->addHtml( $config['email_html_footer'] );

    if( $other['system_details'] ){
        $mail->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
    }

    $mail->addHtml('
		<!--
			HTTP_USER_AGENT : '.( isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '' ).'
			REMOTE_ADDR : '.( isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '' ).'
		-->
	');

    // Rattache les pièces jointes du message
    if( is_array($piece) ){
        messages_files_update_cnt( $piece, $id );

        $r_file = messages_files_get( 0, $id );
        if( ria_mysql_num_rows($r_file)>0 ){
            while( $file = ria_mysql_fetch_array($r_file) ){
                $ext = preg_replace('/.*\./','',$file['name']);
                $mail->addAttachment($config['cnt_file_dir'].'/'.$file['id'].'.'.$ext, $file['name']);
            }
        }
    }
    $res = $mail->send();
    if( $res )
        message_set_send( $id );
    return $res;
}

/**	Envoi un mail de réponse d'un contact.
 *
 *	@param string $email Obligatoire, L'email du contact
 *  @param $id_mess Facultatif, Identifiant du message d'origine
 *	@param int $usr_id Facultatif, identifiant de l'utilisateur
 *	@param string $subject Facultatif, Sujet du message
 *	@param string $body Facultatif, Corps du message de réponse
 *	@param $body_reply Facultatif, Corps du message d'origine
 *	@param $piece Facultatif, Tableau d'identifiant de pièce jointe
 *	@param $sending Facultatif, paramètre à déterminer
 *
 *	@return bool True en cas de succès, False sinon
 */
function contacts_send_reply( $email, $id_mess=0, $usr_id=0, $subject, $body, $body_reply='', $piece=null, $sending=false ){
    global $config;

    $email = trim($email);
    $subject = trim($subject);
    $body = trim($body);
    //$body_reply = trim($body_reply);
    //if( !$email || !$body || !$body_reply ) return false;

    if( !$email || !$body ) return false;

    $email = strtolower($email);

    // Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
    $rcfg = cfg_emails_get('site-contact');
    if( !ria_mysql_num_rows($rcfg) ) return false;
    $cfg = ria_mysql_fetch_array($rcfg);

    $mail = new Email();
    $mail->setFrom( $cfg['from'] );
    $mail->addTo( $email );

    if (trim($cfg['bcc']) != '') {
        $mail->addBcc($cfg['bcc']);
    }

    if (trim($cfg['reply-to']) != '') {
        $mail->setReplyTo($cfg['reply-to']);
    }

    if( $subject && $sending ){
        $mail->setSubject( $subject );
    } elseif( $subject ){
        $mail->setSubject( "[RE] ".$subject );
    } else {
        $mail->setSubject( '[RE] Contact '.$config['site_name'] );
    }

    $mail->addHtml( $config['email_html_header'] );

    //Ancienne méthodes de stockage des messages
    /*$res = ria_mysql_query('
        insert into gu_contacts
            (cnt_tnt_id,cnt_email,cnt_subject,cnt_body,cnt_date_created,cnt_usr_id,cnt_reply)
        values
            (\''.$config['tnt_id'].'\',\''.addslashes($cfg['from']).'\',\''.addslashes($subject).'\',\''.addslashes($body).'\',now(),'.$usr_id.','.$id_mess.');
    ');*/

    $id_message = add_message("", "", "", $email, "", $subject, $body, "CONTACT", "", true, $id_mess, 0, "", "", "", 0, 0, false, true, $usr_id, $sending );

    // Rattache les pièces jointes du message
    if( is_array($piece) ){

        messages_files_update_cnt( $piece, $id_message );

        $r_file = messages_files_get( 0, $id_message );
        if( ria_mysql_num_rows($r_file)>0 ){
            while( $file = ria_mysql_fetch_array($r_file) ){
                $ext = preg_replace('/.*\./','',$file['name']);
                $mail->addAttachment($config['cnt_file_dir'].'/'.$file['id'].'.'.$ext, $file['name']);
            }
        }
    }

    //add_message("", "", "", $email, "", $subject, $body, "CONTACT", "", true, $id_mess);

    $mail->addParagraph( $body );

    if( $body_reply!='' ){
        $mail->addParagraph( "<strong style=\"font-size:small\">Votre message d'origine :</strong>" );

        $mail->addParagraph( "<em style=\"font-size:small\">". $body_reply ."</em>" );
    }

    $mail->addHtml( $config['email_html_footer'] );

    if( !$mail->send() )
        return false;

    return messages_answer_count_set($id_mess);

}

/**	Cette fonction permet le chargement d'un ou plusieurs contacts effectués via le formulaire de contact, éventuellements filtrés en fonction des paramètres optionnels fournis.
 *	@param int $usr Facultatif, identifiant d'un compte utilisateur sur lequel filtrer les résultat
 *	@param int $id Facultatif, identifiant d'un message de contact lui-même
 *	@param int $spam_id Facultatif, identifiant d'un spam
 *	@param null|bool $have_rep Facultatif, permet de récupérer les contacts avec ou sans réponse, laissez NULL pour tous les récupérer
 *	@param null|bool $have_usr Facultatif, permet de récupérer les contacts de personnes identifiées ou non, laissez NULL pour pas en tenir compte
 *	@param $sort Facultatif, tri à appliquer au résultat. Par défaut, le résultat est trié par date d'envoi décroissant. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : date_created. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param int $rowstart Permet de placer le cursor sur une ligne mysql
 *	@param int $maxrows Permet de limiter le nombre de résultat maximum
 *	@param string $date_start Facultatif, permet de filtrer les contacts à partir d'une date minimum d'envoi
 *	@param string $date_end Facultatif, permet de filtrer les contacts à partir d'une date maximum d'envoi
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer le résultat. Par défaut, les messages de tous les sites sont retournés
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du message
 *		- firstname : prénom de l'utilisateur (issu du message ou de sa fiche client)
 *		- lastname : nom de famille de l'utilisateur (issu du message ou de sa fiche client)
 *		- society : entreprise de l'utilisateur (issu du message ou de sa fiche client)
 *		- email : adresse email de l'utilisateur (issu du message ou de sa fiche client)
 *		- email_to : adresse email des destinataire
 *		- email_cc : adresse email des destinataire en copie
 *		- phone : numéro de téléphone de l'utilisateur (issu du message ou de sa fiche client)
 *		- subject : sujet du message (n'est pas toujours renseigné, en fonction du formulaire source)
 *		- body : contenu du message
 *		- ip : adresse IP à partir de laquelle le contact a été envoyé
 *		- date_created : date et heure de création du message
 *		- usr_id : identifiant de l'utilisateur (renseigné si a fourni une adresse email permettant d'établir la corrélation avec son compte).
 *		- is_sync : permet de savoir si un utilisateur est synchronisé ou non
 *		- nbrep : nombre de réponse au message
 *		- wst_id : identifiant du site
 */
function contacts_get( $usr=0, $id=null , $spam_id=0, $have_rep=null, $have_usr=null, $sort=false, $rowstart=0, $maxrows=0, $date_start=false, $date_end=false, $wst_id=false ){
    if( $date_start!=false && isdate($date_start) )
        $date_start = dateparse( $date_start );

    if( $date_end!=false && isdate($date_end) )
        $date_end = dateparse( $date_end );

    global $config;

    $type = get_message_type_id( "CONTACT" );
    $t = ria_mysql_fetch_array($type);

    $sql = '
		select
			mess.cnt_tnt_id as tnt_id, mess.cnt_wst_id as wst_id, mess.cnt_id as id, if(adr_firstname!="",adr_firstname,mess.cnt_firstname) as firstname, if(adr_lastname!="",adr_lastname,mess.cnt_lastname) as lastname,
			if(adr_society!="",adr_society,mess.cnt_society) as society, if(usr_email!="",usr_email,mess.cnt_email) as email, if(adr_phone!="",adr_phone,mess.cnt_phone) as phone,
			mess.cnt_subject as subject, mess.cnt_body as body, cnt_ip as ip,
			date_format(mess.cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_id,
			cnt_answer_count as nbrep, usr_is_sync as is_sync, cnt_email_to as email_to, cnt_email_cc as email_cc
		from gu_messages as mess
			left join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and mess.cnt_usr_id=usr_id)
			left join gu_adresses on (adr_tnt_id='.$config['tnt_id'].' and usr_adr_invoices=adr_id and adr_usr_id=usr_id)
		where mess.cnt_tnt_id='.$config['tnt_id'].'
			and mess.cnt_type='.$t['id'].' and not mess.cnt_sending
			and usr_date_deleted is null
	';
    if( $have_rep!==null ){
        if( $have_rep ){
            $sql .= ' and cnt_answer_count>0';
        }else{
            $sql .= ' and (cnt_answer_count=0 || cnt_answer_count is null) ';
        }
    }
    if( $spam_id > 0 ){
        $sql .= ' and mess.cnt_spam_id='.$spam_id;
    }else{
        $sql .= ' and mess.cnt_spam_id IS NULL';
    }
    if( is_numeric($usr) && $usr>0 ){
        $sql .= ' and mess.cnt_usr_id='.$usr;
    }
    if( is_numeric($id) && $id>0 ){
        $sql .= ' and mess.cnt_id='.$id;
    }

    //Filtre sur l'identification ou non des utilisateurs
    if( $have_usr!==null ){
        if( $have_usr ){
            $sql .= ' and mess.cnt_usr_id is not null and mess.cnt_usr_id>0';
        }else{
            $sql .= ' and (mess.cnt_usr_id is null or mess.cnt_usr_id=0)';
        }
    }

    $sql .= ' and mess.cnt_reply is null';

    if( $date_start ){
        $sql .= ' and date(cnt_date_created)>=\''.$date_start.'\'';
    }
    if( $date_end ){
        $sql .= ' and date(cnt_date_created)<=\''.$date_end.'\'';
    }

    if( is_numeric($wst_id) && $wst_id > 0 ){
        $sql .= ' and mess.cnt_wst_id = '.$wst_id;
    }

    // Tri du résultat (valeurs par défaut)
    if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
        $sort = array( 'cnt_date_created'=>'desc' );
    }

    // Converti le paramètre de tri en SQL
    $sort_final = array();
    foreach( $sort as $col=>$dir ){
        $dir = $dir=='asc' ? 'asc' : 'desc';
        if( $col=='cnt_date_created' ){
            array_push( $sort_final, 'mess.cnt_date_created '.$dir );
            break;
        }
    }
    if( sizeof($sort_final)==0 ){ $sort_final = array( 'mess.cnt_date_created desc' ); }
    $sql .= ' group by mess.cnt_id order by '.implode( ', ', $sort_final ).' ';

    $sql .= ' limit '.$rowstart.', '.($maxrows>0 ? $maxrows : '18446744073709551615');

    return ria_mysql_query($sql);
}
/**	Cette fonction permet de récupérer l'ensemble des réponses d'un message d'un contact
 *	@param $msg Obligatoire, identifiant du message
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du message
 *		- firstname : prénom de l'utilisateur (issu du message ou de sa fiche client)
 *		- lastname : nom de famille de l'utilisateur (issu du message ou de sa fiche client)
 *		- society : entreprise de l'utilisateur (issu du message ou de sa fiche client)
 *		- email : adresse email de l'utilisateur (issu du message ou de sa fiche client)
 *		- phone : numéro de téléphone de l'utilisateur (issu du message ou de sa fiche client)
 *		- subject : sujet du message (n'est pas toujours renseigné, en fonction du formulaire source)
 *		- body : contenu du message
 *		- date_created : date et heure de création du message
 *		- date_created_en : date et heure de création du message au format anglais
 *		- usr_id : identifiant de l'utilisateur (renseigné si a fourni une adresse email permettant d'établir la corrélation avec son compte).
 */
function contacts_get_replies( $msg ){
    global $config;

    $type = get_message_type_id( "CONTACT" );
    $t = ria_mysql_fetch_array($type);

    $sql = '
		select
			cnt_id as id, if(adr_firstname!="",adr_firstname,cnt_firstname) as firstname, if(adr_lastname!="",adr_lastname,cnt_lastname) as lastname,
			if(adr_society!="",adr_society,cnt_society) as society, if(usr_email!="",usr_email,cnt_email) as email, if(adr_phone!="",adr_phone,cnt_phone) as phone,
			cnt_subject as subject, cnt_body as body,
			date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_id,
			cnt_date_created as date_created_en
		from gu_messages
			left join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and cnt_usr_id=usr_id and usr_date_deleted is null)
			left join gu_adresses on (adr_tnt_id='.$config['tnt_id'].' and usr_adr_invoices=adr_id and adr_usr_id=usr_id)
	 where cnt_tnt_id='.$config['tnt_id'].'
	 and cnt_type='.$t['id'];
    $sql .= ' and cnt_reply='.$msg;
    $sql .= '
		order by cnt_date_created desc
	';
    return ria_mysql_query($sql);

}

/**	Cette fonction permet le contrôle d'un identifiant  d'une prise de contact
 *	@param int $id Obligatoire, identifiant de la prise de contact
 *	@return bool true en cas de succès, false en cas d'échec
 */
function contacts_exists( $id ){
    global $config;
    if( !is_numeric($id) || $id<=0 ) return false;
    return ria_mysql_num_rows(ria_mysql_query('select cnt_id from gu_messages where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id))>0;
}

/// @}
<?php

/**	\defgroup socolissimo So Colissimo
 *	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des livraisons par So Collissimo
 *	@{
 */

/**	\brief Classe permettant la relation entre socolissimo et la boutique
 *
 *	Définir les variables suivantes dans la config sql :
 *		so_colissimo_pudoFOId : clé
 *		so_colissimo_key : clé
 *		so_colissimo_url_ok : url de retour si ok
 *		so_colissimo_url_ko : url de retour si ko
 *		so_colissimo_port_id : identifiant du produit frais de port ( la classe met à jour ce champs )
 *		so_colissimo_belgique : 1 = la belgique est autorisé sinon 0
 */
class Socolissimo{

	private $order; ///< Identifiant de la commande à livrer par So Colissimo
	private $user; ///< Identifiant du compte utilisateur passant la commande
	private $zone_id; ///< Identifiant de la zone de livraison

	/**	Constructeur
	 *	@param $ord_id Obligatoire, identifiant de la commande à livrer par So Colissimo
	 */
	public function __construct( $ord_id ){
		if( !ord_orders_exists($ord_id) ){
			throw new Exception("La commande doit être passer en paramètre");
		}

		$this->order = ria_mysql_fetch_array( ord_orders_get(0, $ord_id) );
		$this->user  = ria_mysql_fetch_array( gu_users_get($this->order['user']) );
	}

	/**
	 * Retourne le code d'affichage de iframe
	 */
	public function getIframe(){
		return '
		<iframe align="right" id="SOLivraison" name="SOLivraison" style="border:0px solid #000000" marginwidth="0" marginheight="0" frameborder="0" border="0" width="100%" height="750px" src="/livraison-so-colissimo/?order='. $this->order['id'].'"></iframe>
		';
	}

	/**
	 * Retourne le code du contenu de l'iframe
	 *	@param $zone_id Obligatoire, identifiant de la zone à prendre en compte pour les délais de livraison ( France, .. )
	 */
	public function getIframeContent( $zone_id ){
		global $config;

		header('Content-Type: text/html; charset=ISO-8859-1');
		$html = '<!DOCTYPE html>
		<html lang="fr">
			<head>
				<meta http-equiv="Content-Type" content="text/html" />
				<title>So Colissimo</title>
			</head>
			<body>
				<form name="formSOLivraisonSimplicite" action="https://ws.colissimo.fr/pudo-fo-frame/storeCall.do" method="post">';

					$signature = '';

					$adr_id = is_numeric($this->order['adr_delivery']) && $this->order['adr_delivery'] ? $this->order['adr_delivery'] : $this->order['adr_invoices'];

					$adr  = ria_mysql_fetch_array( gu_adresses_get($this->order['user'], $adr_id) );
					$srv = ria_mysql_fetch_array( dlv_services_get($this->order['srv_id'], false, $zone_id) );

					$srv_port_ht = ord_orders_get_port_amount( $this->order['id'], 0, $srv['id'] );
					$srv_port_ttc = $srv_port_ht * _TVA_RATE_DEFAULT;

					$civility = '';
					switch( $adr['title_id'] ){
						case 1 :
							$civility = 'MR';
							break;
						case 2 :
							$civility = 'MME';
							break;
						case 3 :
							$civility = 'MLE';
							break;
					}

					$phone = trim($adr['mobile']) ? $adr['mobile'] : $adr['phone'];

					$params = array(
						'pudoFOId' 					=> $config['so_colissimo_pudoFOId'],
						'ceName' 					=> utf8_decode($adr['lastname']),
						'dyPreparationTime' 		=> ceil( $srv['hours_min'] / 24 ),
						'dyForwardingCharges' 		=> number_format( $srv_port_ttc, 2, '.', '' ),
						'dyForwardingChargesCMT' 	=> '',
						'trClientNumber' 			=> $this->order['user'],
						'trOrderNumber' 			=> $this->order['id'],
						'orderId' 					=> $this->order['id'],
						'numVersion' 				=> '4.0',
						'ceCivility' 				=> $civility,
						'ceFirstName' 				=> strtoupper2($adr['firstname']),
						'ceCompanyName' 			=> strtoupper2($adr['society']),
						'ceAdress1' 				=> '',
						'ceAdress2' 				=> '',
						'ceAdress3' 				=> strtoupper2($adr['address1']),
						'ceAdress4' 				=> strtoupper2($adr['address2']),
						'ceZipCode' 				=> strtoupper2($adr['postal_code']),
						'ceTown' 					=> strtoupper2($adr['city']),
						'ceEntryPhone' 				=> '',
						'ceDeliveryInformation' 	=> '',
						'ceEmail' 					=> utf8_decode($this->user['email']),
						'cePhoneNumber' 			=> $phone,
						'ceDoorCode1' 				=> '',
						'ceDoorCode2' 				=> '',
						'dyWeight' 					=> '',
						'trFirstOrder' 				=> '',
						'trParamPlus' 				=> '',
						'trReturnUrlKo' 			=> $config['so_colissimo_url_ko'],
						'trReturnUrlOk' 			=> $config['so_colissimo_url_ok'],
						'CHARSET' 					=> 'UTF-8',
						'cePays' 					=> utf8_decode(sys_countries_get_code($adr['country'])),
						'trInter' 					=> $config['so_colissimo_belgique'] == 1 ? 1 : 0,
						'ceLang'					=> '',
						'key' 						=> $config['so_colissimo_key']
					);

					$sha1_string = implode('', $params);
					$signature = sha1( $sha1_string );
					foreach( $params as $key=>$value ){
						if( trim($value)=='' ){
							continue;
						}

						$html .= '		<input type="hidden" name="'.$key.'" value="'.htmlspecialchars($value).'" />';
					}


					$html .= '		<input type="hidden" name="signature" value="'.$signature.'" />';

		$html .= '</form>
				<script>document.formSOLivraisonSimplicite.submit();</script>
			</body>
		</html> ';

		return $html;
	}

	/**
	 * Traite le résultat de SoColissimo, création des adresses de livraisons
	 *	@return bool true si tous va bien, sinon retourne l'erreur
	 */
	public function getResult(){
		global $config;

		// gestion du retour So Colissimo
		if( isset($_POST['PUDOFOID'], $_POST['DELIVERYMODE']) ){

			// mise à jour du port
			if( $_POST['DYFORWARDINGCHARGES'] ) {

				$rprd_info = prd_products_get_simple( $config['so_colissimo_port_id']  );
				if( $rprd_info && ria_mysql_num_rows($rprd_info) ){
					$prd_info = ria_mysql_fetch_array( $rprd_info );

					ord_products_del( $this->order['id'], $prd_info['id'] );
					if( !ord_products_add_free( $this->order['id'], $prd_info['ref'], $prd_info['name'], $_POST['DYFORWARDINGCHARGES']/_TVA_RATE_DEFAULT ) ){
						return "Erreur de mise à jour du montant du frais de port";
					}
				}

			}

			if( !in_array($_POST['DELIVERYMODE'], array('DOM', 'RDV')) ){ // livraison en points relais So Colissimo
				// création d'un nouveau point relais So Colissimo (s'il n'existe pas), sinon mise à jour des informations du point relais
				$address1 	= isset($_POST['PRADRESS1']) 	? $_POST['PRADRESS1'] 	: '';
				$address2 	= isset($_POST['PRADRESS2']) 	? $_POST['PRADRESS2'] 	: '';
				$zipcode  	= isset($_POST['PRZIPCODE']) 	? $_POST['PRZIPCODE'] 	: '';
				$city  		= isset($_POST['PRTOWN']) 		? $_POST['PRTOWN'] 		: '';
				$cnt_code	= isset($_POST['PRPAYS']) 		? $_POST['PRPAYS'] 		: 'FRANCE';
				$phone		= '';

				$rly_id = dlv_relays_add( $_POST['PRID'], SO_COLISSIMO_TYPE, $_POST['PRNAME'], $address1, $address2, $zipcode, $city, $cnt_code, $phone );
				if( !$rly_id ){
					return "Une erreur inattendue s'est produite lors de la sélection du point relais So Colissimo. \nMerci de prendre contact avec nous pour nous signaler l'erreur.";
				} else {
					// enregistre le point relais sur la commande et met à jour l'information d'utilisation de So Colissimo
					if( ord_orders_set_relay($this->order['id'], $rly_id) ){
						return true;
					}else{
						return "Une erreur est survenue pendant l'affectation du point-relais sélectionné à votre panier. Merci de prendre contact avec nous pour nous signaler l'erreur.";
					}
				}
			} else { // livraison à une adresse (celle de facture ou une autre), si l'adresse n'existe pas, elle sera automatiquement créée
				$title = '';
				if( isset($_POST['CECIVILITY']) ){
					switch( $_POST['CECIVILITY'] ){
						case 'MR' 	: $title = 1; break;
						case 'MME' 	: $title = 2; break;
						case 'MLE' 	: $title = 3; break;
					}
				}

				$country 	= isset($_POST['CEPAYS']) 			? sys_countries_get_name($_POST['CEPAYS']) : 'FRANCE';

				$zipcode 	= isset($_POST['CEZIPCODE']) 		? $_POST['CEZIPCODE'] 		: '';
				$city 		= isset($_POST['CETOWN']) 			? $_POST['CETOWN'] 			: '';
				$address1 	= isset($_POST['CEADRESS3']) 		? $_POST['CEADRESS3'] 		: '';
				$firstname 	= isset($_POST['CEFIRSTNAME']) 		? $_POST['CEFIRSTNAME'] 	: '';
				$lastname 	= isset($_POST['CENAME']) 			? $_POST['CENAME'] 		 	: '';
				$society 	= isset($_POST['CECOMPANYNAME']) 	? $_POST['CECOMPANYNAME']	: '';
				$mobile 	= isset($_POST['CEPHONENUMBER'])	? $_POST['CEPHONENUMBER'] 	: '';

				// complément d'adresse
				$address2 = isset($_POST['CEADRESS4']) ? $_POST['CEADRESS4'] : '';
				$address2 = isset($_POST['CEADRESS1']) ? (trim($address2)!='' ? ' ' : '').$_POST['CEADRESS1'] : '';
				$address2 = isset($_POST['CEADRESS2']) ? (trim($address2)!='' ? ' ' : '').$_POST['CEADRESS2'] : '';

				// recherche si la même adresse n'existe pas déjà
				$radr = gu_adresses_get( $this->user['id'], 0, $zipcode, array(), false, $city, $address1, '', $firstname, $lastname, $society, '', true );

				$adr_id = 0;
				if( $radr && ria_mysql_num_rows($radr) ){
					$adr = ria_mysql_fetch_array( $radr );
					$adr_id = $adr['id'];
					gu_adresses_set_address2( $this->user['id'], $adr['id'], $address2 );
					gu_adresses_update( $this->user['id'], $adr_id, 3, $title, $firstname, $lastname, $society, '', $address1, $address2, $zipcode, $city, $country, '', '',$mobile );
				} else {
					$adr_id = gu_adresses_add( $this->user['id'], 3, $title, $firstname, $lastname, $society, '', $address1, $address2, $zipcode, $city, $country, '', '', $mobile );
				}

				if( !$adr_id ){
					return "Une erreur est survenue pendant l'enregistrement de votre adresse de livraison. Merci de prendre contact avec nous pour nous signaler l'erreur.";
				} else {
					// mise à jour du commentaire de la commande
					$dlv_notes  = '';
					$dlv_notes .= isset($_POST['CEDOORCODE1']) ? 'Code porte 1 : '.$_POST['CEDOORCODE1'].'<br />' : '';
					$dlv_notes .= isset($_POST['CEDOORCODE2']) ? 'Code porte 2 : '.$_POST['CEDOORCODE2'].'<br />' : '';
					$dlv_notes .= isset($_POST['CEDELIVERYINFORMATION']) ? 'Autres informations : '.$_POST['CEDELIVERYINFORMATION'].'<br />' : '';

					$res = ord_orders_set_relay( $this->order['id'], null);
					$res = $res && ord_orders_adr_delivery_set( $this->order['id'], $adr_id );
					$res = $res && ord_orders_dlv_notes_set( $this->order['id'], $dlv_notes );

					if( $res ){
						return true;
					} else {
						return "Une erreur est survenue pendant l'enregistrement de votre adresse de livraison. Merci de prendre contact avec nous pour nous signaler l'erreur.";
					}
				}
			}
		}
	}
}

/// @}
<?php

/**	\defgroup gu_livr_alerts Alertes de disponibilité
 *	\ingroup model_users
 *	Les fonctions ci-dessous sont utilisées pour la gestion des alertes de disponibilité des produits en rupture.
 *	Elles permettent l'inscription et l'envoi de notifications de disponibilité.
 *	@{
 */

/**	Permet l'ajout d'une demande d'avertissement de disponibilité d'un produit.
 *	@param string $email Obligatoire, Adresse email à notifier
 *	@param int $prd Obligatoire, Identifiant du produit à surveiller
 *	@param int $wst Optionnel, identifiant d'un site web (par défaut on prendre celui de la configuration)
 *	@param $option_value Optionnel, destiné au alerte de disponible sur des nomenclatures, permet d'identifier les options choisies (objet JSON)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_livr_alerts_add( $email, $prd, $wst=0, $option_value='' ){
	global $config;

	if( !trim($email) ) return false;
	if( !is_numeric($prd) ) return false;
	$email = trim(strtolower($email));

	// Recherche l'identifiant de l'utilisateur si l'adresse email est déjà connue
	$usr = 'null';
	$ruser = ria_mysql_query('select usr_id as id from gu_users where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and usr_email=\''.addslashes($email).'\'');
	if( $fusr = ria_mysql_fetch_array($ruser) ){
		$usr = $fusr['id'];
	}

	// Contrôle si le produit fait partie d'une catégorie dont les alertes sont envoyées à chaque remise en stock
	$restocking = false;
	if( isset($config['alert_cat_restocking']) && is_array($config['alert_cat_restocking']) && sizeof($config['alert_cat_restocking']) ){
		$rcly = prd_classify_get( false, $prd );
		if( $rcly && ria_mysql_num_rows($rcly) ){
			while( $cly = ria_mysql_fetch_array($rcly) ){
				if( in_array($cly['cat'], $config['alert_cat_restocking']) ){
					$restocking = true;
					break;
				}
			}
		}
	}

	$wst = is_numeric($wst) && $wst>0 && wst_websites_exists($wst) ? $wst : $config['wst_id'];
	return ria_mysql_query('
		replace into gu_livr_alerts
			( alert_tnt_id, alert_wst_id, alert_usr_id, alert_usr_email, alert_prd_id, alert_date_created, alert_restocking, alert_options )
		values
			( '.$config['tnt_id'].', '.$wst.', '.$usr.', \''.addslashes($email).'\', '.$prd.', now(), '.( $restocking ? 1 : 0 ).', '.( trim($option_value) != '' ? '"'.addslashes($option_value).'"' : 'null' ).' )
	');
}

/**	Détermine si une alerte email pour cette adresse email et ce produit existent déjâ .
 *	@param string $email Adresse email à notifier
 *	@param int $prd Identifiant interne du produit à surveiller
 *	@param int $wst Optionnel, identifiant d'un site web (par défaut on prendre celui de la configuration)
 *	@param $not_send Optionnel, si oui ou non l'envoi doit déjà avoir eu lieu (par défaut on en tient pas compte)
 *	@return bool True si l'alert existe, False dans le cas contraire
 */
function gu_livr_alerts_exists( $email, $prd, $wst=0, $not_send=null ){
	global $config;

	if( !is_numeric($prd) ) return false;
	if( !trim($email) ) return false;

	$email = trim(strtolower($email));
	$wst = is_numeric($wst) && $wst>0 && wst_websites_exists($wst) ? $wst : $config['wst_id'];

	$sql = '
		select alert_prd_id
		from gu_livr_alerts
		where alert_tnt_id='.$config['tnt_id'].'
			and alert_wst_id='.$wst.'
			and alert_usr_email=\''.addslashes($email).'\'
			and alert_prd_id='.$prd.'
	';

	if( $not_send !== null ){
		if( $not_send ){
			$sql .=' and ifnull(alert_date_notified, "") = ""';
		}else{
			$sql .=' and ifnull(alert_date_notified, "") != ""';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/**	Permet la suppression d'une demande d'avertissement de disponibilité d'un produit.
 *	@param string $email Adresse email à dénotifier
 *	@param $prd Identifiant du produit à ne plus surveiller
 *	@param int $wst Optionnel, identifiant d'un site web (par défaut on prendre celui de la configuration)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_livr_alerts_del( $email, $prd=null, $wst=0 ){
	global $config;

	if( !trim($email) ) return false;
	if( !is_numeric($prd) && $prd!==null ) return false;

	$email = trim(strtolower($email));
	$wst = is_numeric($wst) && $wst>0 && wst_websites_exists($wst) ? $wst : $config['wst_id'];

	$sql = '
		delete from gu_livr_alerts
		where alert_tnt_id='.$config['tnt_id'].'
			and alert_wst_id='.$wst.'
			and alert_usr_email=\''.addslashes($email).'\'
	';
	if( $prd!==null ){
		$sql .= ' and alert_prd_id='.$prd;
	}

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction envoie une alerte email lorsqu'un produit est disponible à toutes les personnes l'ayant dans ses reliquats en attente
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $wst_id Facultatif, identifiant du site auquel limiter les notifications (ex : Extranet ou Site public)
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function gu_delayed_alerts_notify( $prd_id, $wst_id=0 ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !isset($config['enabled_delayed_notify']) || ! $config['enabled_delayed_notify'] ){
		return true;
	}

	$wst_id = is_numeric($wst_id) && $wst_id ? $wst_id : $config['wst_id'];
	$rcfg = cfg_emails_get( 'alert-delayed', $wst_id );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc( $rcfg );

	$product = false;
	$ar_orders = array();

	// Récupère les reliquats
	$rdelayed = ord_products_get_delayed( false, array(), false, $prd_id, $wst_id );
	if( $rdelayed ){
		while( $delayed = ria_mysql_fetch_assoc($rdelayed) ){
			if( !$product ){
				$product = array(
					'ref' => $delayed['prd_ref'],
					'name' => $delayed['prd_name']
				);
			}

			if( !isset($ar_orders[ $delayed['ord_usr_id'] ]) ){
				$ar_orders[ $delayed['ord_usr_id'] ] = array();
			}

			$ar_orders[ $delayed['ord_usr_id'] ][] = array(
				'id' => $delayed['ord_id'],
				'ref' => $delayed['ord_ref'],
				'piece' => $delayed['ord_piece'],
				'date' => $delayed['ord_date']
			);
		}
	}

	if( !$product || !sizeof($ar_orders) ){
		return true;
	}

	foreach( $ar_orders as $usr_id=>$orders ){
		$enabled = true;

		// Contrôle que le compte client accepte bien de recevoir cet email
		$rvar = cfg_overrides_get( 0, array(), 'enabled_delayed_notify', $usr_id );
		if( $rvar && ria_mysql_num_rows($rvar) ){
			$var = ria_mysql_fetch_assoc( $rvar );

			if( !$var['value'] ){
				$enabled = false;
			}
		}

		if( !$enabled ){
			continue;
		}

		$usr_email = gu_users_get_email( $usr_id );
		if( trim($usr_email)=='' ){
			continue;
		}

		switch( $config['tnt_id'] ){
			case 29: {
				$email = new Email();
				$email->setFrom( $cfg['from'] );
				$email->addTo( $usr_email );
				if( $cfg['bcc'] ){
					$email->addBcc( $cfg['bcc'] );
				}
				if( $cfg['reply-to'] ){
					$email->setReplyTo( $cfg['reply-to'] );
				}

				$email->setSubject( "Disponibilité d'un reliquat" );

				$email->addHtml( $config['email_html_header'] );
				$email->addParagraph('Bonjour,');
				$email->addBlankTextLine();

				$txt_order = '';
				if( sizeof($orders)==1 ){
					$order = $orders[0];

					$txt_order = 'votre commande N°'.ord_orders_name( $order['ref'], $order['piece'], $order['id'] ).' du '.$order['date'];
				}else{
					$txt_order = 'vos commande '.

					$i = 1;
					foreach( $orders as $order ){
						if( $i>1 ){
							$txt_order .= sizeof($orders)==$i ? ' et ' : ', ';
						}
						$i++;
					}

					$txt_order .= 'N°'.ord_orders_name( $order['ref'], $order['piece'], $order['id'] ).' du '.$order['date'];
				}

				$email->addParagraph('L’article '.$product['name'].' référence '.$product['ref'].' en reliquat depuis '.$txt_order.' est devenu disponible. Validez ce  reliquat si vous souhaitez être livré rapidement.');

				$email->addBlankTextLine();
				$email->addParagraph(
					"Cordialement,\n".
					"L'équipe ".$config['site_name'].'.'
				);

				$email->addHtml( $config['email_html_footer'] );
				$email->send();
				break;
			}
			default: {
				error_log('Aucun mail en place pour le tenant : '.$config['tnt_id']);
				break;
			}
		}
	}



	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction envoie une alerte email aux personnes ayant demandé à être notifiées de la disponibilité
 *	d'un produit.
 *
 *	@param int $prd Obligatoire, identifiant du produit devenu disponible
 *	@param int $wst_id Facultatif, identifiant du site web source
 *
 */
function gu_livr_alerts_notify( $prd, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		$config['email_html_header'] = str_replace('%site%', $config['site_url'], $config['email_html_header']);
		$config['email_html_footer'] = str_replace('%site%', $config['site_url'], $config['email_html_footer']);
	}

	$analytics = '?utm_source=alertes&utm_medium=email&utm_campaign=dispo-produit';

	// Charge les adresses emails à notifier
	$alerts = gu_livr_alerts_get( 0, $prd, $wst_id );
	while( $r = ria_mysql_fetch_array($alerts) ){
		$url = prd_products_get_url($prd, true);
		if( trim($url) ){
			$site_url = '';
			if( !is_numeric($r['wst_id']) || $r['wst_id']<=0 ){
				continue;
			}

			$rcfg = cfg_overrides_get( $r['wst_id'], array(), 'site_url' );
			if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
				continue;
			}

			$site_url = ria_mysql_result( $rcfg, 0, 'value' );
			if( trim($site_url)=='' ){
				continue;
			}

			// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
			$rcfg = cfg_emails_get('prd-available', $r['wst_id']);
			if( !ria_mysql_num_rows($rcfg) ){
				return false;
			}

			$cfg = ria_mysql_fetch_array($rcfg);

			$email = new Email();
			$email->setFrom( $cfg['from'] );
			$email->addTo( $r['usr_email'] );
			if( $cfg['bcc'] ){
				$email->addBcc( $cfg['bcc'] );
			}
			if( $cfg['reply-to'] ){
				$email->setReplyTo( $cfg['reply-to'] );
			}

			switch( $config['tnt_id'] ){
				case 13 :
					$title = prd_products_get_name( $prd, true );

					$email->setSubject( "Votre article est à nouveau disponible !" );

					$email->addHtml( $config['email_html_header'] );
					$email->addParagraph('Chère cliente, cher client,');
					$email->addBlankTextLine();

					$email->addParagraph('Vous avez visité notre boutique en ligne <a href="'.$site_url.$analytics.'">'.$site_url.'/</a> et nous vous remercions de votre confiance.');
					$email->addParagraph('Nous vous informons que l\'article portant la référence <a href="'.$site_url.$url.$analytics.'">'.$r['ref'].' ('.htmlspecialchars($title).')</a> est de nouveau disponible dans notre boutique.');

					$email->addBlankTextLine();
					$email->addParagraph(
						"Cordialement,\n".
						"L'équipe ".$config['site_name'].'.'
					);
					$email->addHtml( $config['email_html_footer'] );
					break;
				default :
					$title = prd_products_get_name( $prd, true );

					$email->setSubject( "Disponibilité d'un article" );

					$email->addHtml( $config['email_html_header'] );
					$email->addParagraph('Chère cliente, cher client,');
					$email->addBlankTextLine();

					$email->addParagraph('Vous avez visité notre boutique en ligne <a href="'.$site_url.$analytics.'">'.$site_url.'/</a> et nous vous remercions de votre confiance.');
					$email->addParagraph('Nous vous informons que l\'article portant la référence <a href="'.$site_url.$url.$analytics.'">'.$r['ref'].' ('.htmlspecialchars($title).')</a> est de nouveau disponible dans notre boutique.');

					$email->addBlankTextLine();
					$email->addParagraph(
						"Cordialement,\n".
						"L'équipe ".$config['site_name'].'.'
					);
					$email->addHtml( $config['email_html_footer'] );
					break;
			}
			$email->send();

			ria_mysql_query('
				update gu_livr_alerts set alert_date_notified=now()
				where alert_tnt_id='.$config['tnt_id'].'
					and alert_usr_email=\''.addslashes($r['usr_email']).'\'
					and alert_prd_id='.$prd.'
					and (alert_date_notified is null or alert_date_notified=\''.$r['date_notified_en'].'\')
			');
		}
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction est à utiliser lors de la création d'un nouvel utilisateur ou lorsqu'un utilisateur paramètre
 *	une nouvelle adresse email. Elle permet le rattachement de toutes les demandes d'alertes de disponibilités
 *	effectuées par cet utilisateur à son compte client. L'objectif dans ce cas est de mieux connaître ses goâ»ts
 *	en matière de produits.
 *
 *	@param int $id Identifiant attribué à l'utilisateur lors de sa création
 *	@param string $email L'adresse email du nouvel utilisateur
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function gu_livr_alerts_attach( $id, $email ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}
	if( !trim($email) ){
		return false;
	}

	$email = strtolower(trim($email));
	return ria_mysql_query('
		update gu_livr_alerts set alert_usr_id='.$id.'
		where alert_tnt_id='.$config['tnt_id'].' and alert_usr_email=\''.addslashes($email).'\'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les produits en attente de notification de type "alerte de disponibilité" pour un utilisateur donné.
 * 	Les produits en sommeil sont ignorés, car ils ne sont plus pertinents dans l'usage : s'ils sont en sommeil, c'est qu'il ne seront plus réapprovisionnés.
 *	@param int $usr Optionnel, Identifiant de l'utilisateur dont on souhaite connaître les produits surveillés.
 *	@param int $prd Optionnel, identifiant du produit dont on souhaite connaître les utilisateurs le surveillant.
 *	@param int $wst Optionnel, identifiant du site sur lequel l'alerte a été enregistrée.
 *	@param bool $restocking Optionnel, retourne les alertes en attente d'un restockage
 *	@param string $date1 Optionnel, retourne une date de début pour les dates de création de l'alert
 *	@param string $date2 Optionnel, retourne une date de fin pour les dates de création de l'alert
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- wst_id : identifiant du site source de la demande d'alerte
 *			- usr_id : identifiant de l'utilisateur à notifier (disponible seulement si celui-ci dispose d'un compte client)
 *			- usr_email : adresse email de l'utilisateur à notifier
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 * 			- img_id : identifiant de l'image principale du produit
 *			- date_created : date de création de l'alert
 *			- date_notified_en : date de notification de l'alert
 *			- restocking : si l'envoi est fait à chaque remise en stock
 */
function gu_livr_alerts_get( $usr=0, $prd=0, $wst=0, $restocking=false, $date1='', $date2='' ){
	if( $usr!=0 && !gu_users_exists($usr) ){
		return false;
	}

	if( $prd!=0 && !prd_products_exists($prd) ){
		return false;
	}

	if( !is_numeric($wst) || $wst<0 ){
		return false;
	}

	if ( ($date1 != '' && !isdate($date1)) || ($date2 != '' && !isdate($date2))) {
		return false;
	}

	global $config;

	if( $config['tnt_id']==2 ){
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}else{
		$dps = prd_deposits_get_main();
	}

	if( !$dps ){
		$dps = 0;
	}

	$sql = '
		select alert_wst_id as wst_id, alert_usr_id as usr_id, alert_usr_email as usr_email, prd_id as id, prd_ref as ref, prd_name as name, prd_img_id as img_id,
			date_format( alert_date_created, "%d/%m/%Y" ) as date_created, alert_date_notified as date_notified_en, alert_restocking as restocking
		from gu_livr_alerts
			join prd_products on (alert_tnt_id=prd_tnt_id and alert_prd_id=prd_id)
			left join prd_stocks on (prd_tnt_id=sto_tnt_id and prd_id=sto_prd_id and sto_is_deleted=0 and ifnull(sto_dps_id, '.$dps.')='.$dps.')
		where alert_tnt_id='.$config['tnt_id'].'
			and prd_sleep=0
	';

	if( $restocking ){
		$sql .= '
			and ( alert_date_notified is null or alert_restocking=1 )
		';

	}else{
		$sql .= '
			and ( alert_date_notified is null or (alert_restocking=1 and sto_date_restocking is not null and alert_date_notified<sto_date_restocking) )
		';
	}

	if( $usr!=0 ){
		$remail = ria_mysql_query('select usr_email as email from gu_users where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and usr_id='.$usr.'');
		$email = ria_mysql_result( $remail, 0, 0 );
		$sql .= ' and (alert_usr_id='.$usr.' or alert_usr_email=\''.$email.'\')';
	}

	if( $prd!=0 ){
		$sql .= ' and alert_prd_id='.$prd;
	}

	if( $wst>0 ){
		$sql .= ' and alert_wst_id='.$wst;
	}

	if( isdate($date1) ){
		$sql .= ' and date(alert_date_created) >= "'.addslashes($date1).'"';
	}

	if( isdate($date2) ){
		$sql .= ' and date(alert_date_created) <= "'.addslashes($date2).'"';
	}

	$sql .= ' order by prd_ref';

	return ria_mysql_query($sql);
}
// \endcond

/// @}


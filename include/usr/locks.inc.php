<?php

// \cond onlyria
/**	\defgroup gu_users_locked Verrous
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de verrouiller/déverouiller des comptes clients
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du verrou permettant d'empecher un client de passer commande sur le site ou sur l'extranet.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur a modifier
 *	@param bool $locked Obligatoire, Booléen indiquant si le client doit etre bloqué (true) ou non (false)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_is_locked( $usr, $locked ){
	global $config;

	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !is_numeric($usr) ) return false;

	return ria_mysql_query('
		update gu_users set usr_is_locked='.( $locked ? 1 : 0 ).'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

/**	Cette fonction permet l'interrogation du champ is_locked pour un client donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur a interroger
 *	@return bool true si le client est bloqué, false s'il ne l'est pas
 */
function gu_users_get_is_locked( $usr ){
	global $config;

	if( !is_numeric($usr) ) return false;

	$rusr = ria_mysql_query('
		select usr_is_locked from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and usr_id='.$usr
	);
	if( !ria_mysql_num_rows($rusr) ) return false;

	return ria_mysql_result( $rusr, 0, 0 );
}

/** Cette fonction permet de savoir si un compte client est en sommeil (information stocké dans un champ avancé - _FLD_USR_SLEEP)
 * 	@param int $usr_id Obligatoire, identifiant du compte client
 * 	@return bool True s'il est en sommeil, False dans le cas contraire
 */
function gu_users_get_is_sleep( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	$is_sleep = fld_object_values_get($usr_id, _FLD_USR_SLEEP);
	return in_array( $is_sleep, array('oui', 'Oui', '1') );
}

// \cond onlyria
/// @}
// \endcond


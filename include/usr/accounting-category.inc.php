<?php

// \cond onlyria
/**	\defgroup gu_users_accounting Catégorie comptable
 *	\ingroup model_users cpq
 *	Les fonctions de ce module permettent de gérer la catégorie comptable des comptes clients
 *	@{
 */

/** Cette fonction met à jour la catégorie comptable d'un compte
 *	@param int $usr_id Obligatoire, identifiant du compte
 *	@param int $cac_id Facultatif, identifiant de la catégorie comptable. Si ce paramètre n'est pas renseigné, le compte se verra assigné la valeur NULL
 *
 *	@return bool True en cas de succès, False sinon
 */
function gu_users_set_accouting_category( $usr_id, $cac_id=0 ){
	if( !gu_users_is_tenant_linked($usr_id) ) return false;
	global $config;

	if( $cac_id!==0 && !gu_accouting_categories_exists($cac_id) ) return false;
	if( !gu_users_exists($usr_id) ) return false;

	return ria_mysql_query('
		update
			gu_users
		set
			usr_cac_id='.( $cac_id===0 ? 'NULL' : $cac_id ).'
		where
			usr_id='.$usr_id.' and
			usr_tnt_id='.$config['tnt_id'].'
	');

}

/**	Cette fonction récupère la catégorie comptable d'un client donné.
 *	@param int $usr_id Identifiant du client.
 *	@param bool $check_default Optionnel. Si activé, la valeur de la variable de configuration "default_cac_id" est retournée dans les divers cas d'échec.
 *
 *	@return int L'identifiant de la catégorie comptable du client (ou du compte propriétaire des tarifs qui lui est associé).
 *	@return bool False en cas d'échec et si $check_default n'est pas activé.
 *	@return int La catégorie comptable par défaut en cas d'échec, si $check_default est activé.
 */
function gu_users_get_accouting_category( $usr_id, $check_default=false ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return $check_default ? $config['default_cac_id'] : false;
	}

	// récupère le client "tarifable"
	$usr_id = gu_users_get_prices_holder( $usr_id );

	$res = ria_mysql_query('
		select usr_cac_id as "cac"
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$usr_id.' and usr_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return $check_default ? $config['default_cac_id'] : false;
	}

	$cac_id = ria_mysql_result($res, 0, 'cac');

	if( $cac_id === null && $check_default ){
		$cac_id = $config['default_cac_id'];
	}

	return $cac_id;

}

/// @}

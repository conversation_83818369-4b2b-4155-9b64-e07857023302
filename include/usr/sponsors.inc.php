<?php

/**	\defgroup gu_sponsors Gestion des parrains
 *	\ingroup model_rewards
 *	Les fonctions de ce module permettent de définir le ou les parrains qui ont été en place pour un client, ainsi que gérer ses points de fidélité.
 *	@{
 */

/** Cette fonction permet de retourner le lien de tracking d'un compte client connecté.
 *	@return bool false si aucun compte client n'est connecté, sinon le lien de tracking
 */
function gu_users_get_sponsor_tracking(){
	if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || $_SESSION['usr_id']<=0 ) return false;
	global $config;

	return $config['site_url'].'/?sp='.$_SESSION['usr_id'];
}

/** Cette fonction retourne le parrain directement lié à un compte.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@return bool false en cas d'échec sinon un résultat MySQL contenant :
 *				- sponsor : identifiant de compte parrain
 */
function gu_users_get_sponsor( $usr ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		select usr_sponsor as sponsor
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr.'
			and ifnull(usr_sponsor, \'\')!=\'\'
	');

}

/** Cette fonction permet de mettre à jour le parrain qui ne vient pas d'un lien de tracking (le mail ou l'identifiant du compte parrain doit être fourni).
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param string $sponsor Optionnel, adresse mail du parrain (doit s'agit d'un compte client enregistré)
 *	@param int $usr_sponsor Optionnel, identifiant du compte parrain
 *	@return bool true si le parrain a bien été enregistré
 *	@return bool false si l'enregistrement a échoué ou bien que le compte parrain n'existe pas
 */
function gu_users_set_sponsor( $usr, $sponsor='', $usr_sponsor=0 ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($usr_sponsor) || $usr_sponsor<0 ) return false;

	// le mail ou l'identifiant du compte parrain doit être fourni
	if( trim($sponsor)=='' && $usr_sponsor<=0 ){
		return false;
	}

	global $config;

	$rsp = gu_users_get( $usr_sponsor, $sponsor );
	if( !$rsp || !ria_mysql_num_rows($rsp) )
		return false;

	$sp = ria_mysql_result( $rsp, 0, 'id' );
	if( $sp==$usr ) return false;

	return ria_mysql_query('
		update gu_users
		set usr_sponsor='.$sp.', usr_date_sponsor=now()
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr.'
	');
}

/** Cette fonction permet de controler si un lien de tracking est utilisé.
 *	@return bool false si le sponsor n'a pas pus être enregistré
 *	@return bool true dans le cas contraire
 */
function gu_sponsors_detected(){
	if( !isset($_SESSION['usr_tracking_sponsor']) && !(isset($_GET['sp']) && is_numeric($_GET['sp']) && $_GET['sp']) ) return false;
	global $config;

	// ajout l'identifiant du parrain comme une variable de session
	if( isset($_GET['sp']) && is_numeric($_GET['sp']) && $_GET['sp'] )
		$_SESSION['usr_tracking_sponsor'] = $_GET['sp'];

	// si le client est connecté, on le rattache à l'identifiant du parrain (si différent de celui qui existe déjà)
	if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || $_SESSION['usr_id']<=0 ){
		return false;
	}

	$sp = 0;
	$rsp = gu_sponsors_get( $_SESSION['usr_id'] );
	if( $rsp && ria_mysql_num_rows($rsp) ){
		$sp = ria_mysql_result( $rsp, 0, 'sponsor' );
	}

	if( $sp!=$_SESSION['usr_tracking_sponsor'] ){
		return gu_sponsors_add( $_SESSION['usr_id'], $_SESSION['usr_tracking_sponsor'] );
	}

	return true;
}

/** Cette fonction permet d'ajouter un parrain qui vient d'un lien de tracking.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param int $sponsor Obligatoire, identifiant du compte client
 *	@return bool true si le parrain a bien été enregistré
 *	@return bool false si l'enregistrement a échoué ou si le parrain n'existe pas
*/
function gu_sponsors_add( $usr, $sponsor ){
	if( !gu_users_exists($usr) ) return false;
	if( !gu_users_exists($sponsor) ) return false;
	if( $usr==$sponsor ) return false;
	global $config;

	return ria_mysql_query('
		insert into gu_sponsors
			(sp_tnt_id, sp_sponsor, sp_godchild, sp_datetime)
		values
			('.$config['tnt_id'].', '.$sponsor.', '.$usr.', now())
	');
}

/** Cette fonction retourne le parrain actuellement lié au compte.
 *	Il peut s'agit du parrain directement sur lié au compte ou bien du dernier parrain renseigné par lien de tracking.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@return bool false en cas d'échec de récupération sinon un résultat MySQL contenant :
 *					- sponsor : identifiant du parrain
 */
function gu_sponsors_get( $usr ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	// récupère le parrain directement lié
	$rsp = gu_users_get_sponsor( $usr );
	if( $rsp && ria_mysql_num_rows($rsp) ){
		return $rsp;
	}

	return ria_mysql_query('
		select sp_sponsor as sponsor
		from gu_sponsors
			join gu_users on (sp_tnt_id=usr_tnt_id and sp_sponsor=usr_id)
		where sp_tnt_id='.$config['tnt_id'].'
			and sp_godchild='.$usr.'
			and usr_date_deleted is null
		order by sp_datetime desc
		limit 0, 1
	');

}

/** Cette fonction permet de retirer le sponsor lié au compte.
 *	@param int $usr Obligatoire, identifiant du compte client
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function gu_sponsors_del( $usr ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users
		set usr_sponsor=null, usr_date_sponsor=null
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr.'
	');

}

/** Cette fonction retourne tous les parrains ayant été rattachés à un compte par leur lien de tracking.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@return bool false en cas d'échec de récupération sinon un résultat MySQL contenant :
 *					- sponsor : identifiant du parrain
 */
function gu_sponsors_get_all( $usr ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	// récupère le parrain directement lié
	$rsp = gu_users_get_sponsor( $usr );
	if( $rsp && ria_mysql_num_rows($rsp) ){
		return $rsp;
	}

	return ria_mysql_query('
		select sp_sponsor as sponsor, sp_datetime as "datesp-en", date_format(sp_datetime,"%d/%m/%Y à %H:%i") as datesp,
		adr_firstname as fisrtname, adr_lastname as lastname, adr_society as society
		from gu_sponsors
			join gu_users on (sp_tnt_id=usr_tnt_id and sp_sponsor=usr_id)
			join gu_adresses on (usr_tnt_id=adr_tnt_id and usr_adr_invoices=adr_id)
		where sp_tnt_id='.$config['tnt_id'].'
			and sp_godchild='.$usr.'
			and usr_date_deleted is null
		order by sp_datetime desc
	');
}

/// @}


<?php

// \cond onlyria
/**	\defgroup gu_users_hierarchy Hiérarchie
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de gérer des relations de type parent/enfant entre des comptes clients
 *	@{
 */

/**	Récupère la référence / code client d'un compte spécifié, en recherchant sur son supérieur hiérarchique le cas échéant.
 *	@param int $usr_id Identifiant du client
 *	@return string Le code client s'il existe, chaîne vide sinon
 */
function gu_users_get_parent_ref( $usr_id ){
	if( !is_numeric($usr_id) || $usr_id<=0 ) return '';

	global $config;

	$r = ria_mysql_query('
		select IFNULL(u2.usr_ref, u1.usr_ref)
		from gu_users AS u1
			left join gu_users AS u2 ON u1.usr_tnt_id=u2.usr_tnt_id AND u1.usr_parent_id=u2.usr_id
		where u1.usr_tnt_id='.$config['tnt_id'].' AND u1.usr_id='.$usr_id.' AND u2.usr_date_deleted IS NULL AND u1.usr_date_deleted IS NULL
	');

	if( !$r || !ria_mysql_num_rows($r) ) return '';

	return ria_mysql_result( $r, 0, 0 );
}

/**    Récupère la liste des enfants d'un compte spécifié.
 *    @param $usr_id Identifiant du compte
 *    @return array Liste des identifiants des enfants
 */
function gu_users_get_hierarchy_childs($usr_id){

	$response = array();
	if(!is_numeric($usr_id) || $usr_id<=0 ) return $response;

	global $config;

	$res = ria_mysql_query('select rrh_dst_0 from rel_relations_hierarchy join rel_relations_types on (rrh_tnt_id = rrt_tnt_id or rrt_tnt_id = 0) and rrh_rrt_id = rrt_id where rrh_tnt_id=1118 and rrh_rrt_id = 2 and rrh_src_0 = '.$usr_id.' and rrh_tnt_id = '.$config['tnt_id']);
	if($res){
		while($r = ria_mysql_fetch_assoc($res)){
            $response[] = $r['rrh_dst_0'];
        }
	}
	
	return $response;
}


/// @}
// \endcond


<?php

/**	\defgroup gu_users_payment_credentials Gestion des cartes bancaires
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de gérer l'enregistrement et l'utilisation de cartes bancaires (Commande en un clic)
 *	@{
 */

/** Cette fonction permet d'ajout un identifiant de paiement pour un compte client.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param int $module Obligatoire, constante d'identification du module
 *	@param $key Obligatoire, clé de cryptage de l'identifiant
 *	@param $plaintext Obligatoire, valeur à cryptée correspondant à l'identifiant de paiement
 *	@param string $name Facultatif, nom du porteur de carte
 *	@param $card_brand Facultatif, type de carte. Visa, Mastercard, etc...
 *	@param $card_number Facultatif, numéro de la carte
 *	@param $expiry_month Facultatif, numéro du mois d'expiration de la carte. Chiffre compris entre 1 et 12.
 *	@param $expiry_year Facultatif, année d'expiration de la carte. L'année doit être supérieure ou égale à l'année en cours.
 *	@param $bank_code Facultatif, code banque
 *	@param $first_name Facultatif, prénom du porteur de carte
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function gu_users_payment_credentials_add( $usr_id, $module, $key, $plaintext, $name='', $card_brand='', $card_number='', $expiry_month=0, $expiry_year=0, $bank_code='', $first_name='' ){

	if( !gu_users_exists($usr_id) ){
		return false;
	}

	if( !in_array($module, array('SYSTEMPAY', 'PAYZEN', 'ETRANSACTIONS', 'PAYPLUG')) ){
		return false;
	}

	if( trim($card_number)!='' ){
		if( gu_users_payment_credentials_exists($usr_id, $module, $card_number) ){
			return true;
		}
	}

	if( trim($key)=='' || trim($plaintext)=='' ){
		return false;
	}

	global $config;

	$time = time();
	$hash_key = hash( 'haval128,3', $key.'-'.$time );

	$iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
	$iv = mcrypt_create_iv($iv_size, MCRYPT_RAND);

	$ciphertext = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $hash_key, $plaintext, MCRYPT_MODE_CBC, $iv);
	$ciphertext = $iv . $ciphertext;

	$ar_cols = array( 'upc_tnt_id', 'upc_usr_id', 'upc_module', 'upc_key', 'upc_date_created');
	$ar_vals = array( $config['tnt_id'], $usr_id, '"'.addslashes( $module ).'"', '"'.addslashes( base64_encode($ciphertext) ).'"', '"'.date( 'Y-m-d H:i:s', $time ).'"' );

	if( trim($name)!='' ){
		$ar_cols[] = 'upc_name';
		$ar_vals[] = '"'.addslashes( $name ).'"';
	}

	if( trim($card_brand)!='' ){
		$ar_cols[] = 'upc_card_brand';
		$ar_vals[] = '"'.addslashes( strtoupper2($card_brand) ).'"';
	}

	if( trim($card_number)!='' ){
		$ar_cols[] = 'upc_card_number';
		$ar_vals[] = '"'.addslashes( format_CB($card_number) ).'"';
	}

	if( is_numeric($expiry_month) && $expiry_month>=1 && $expiry_month<=12 ){
		$ar_cols[] = 'upc_expiry_month';
		$ar_vals[] = $expiry_month;
	}

	if( is_numeric($expiry_year) && $expiry_year>=date('Y') ){
		$ar_cols[] = 'upc_expiry_year';
		$ar_vals[] = $expiry_year;
	}

	if( trim($bank_code)!='' ){
		$ar_cols[] = 'upc_bank_code';
		$ar_vals[] = '"'.addslashes( $bank_code ).'"';
	}

	if( trim($first_name) != '' ){
		$ar_cols[] = 'upc_first_name';
		$ar_vals[] = '"'.addslashes( $first_name ).'"';
	}

	$res = ria_mysql_query('
		insert into gu_users_payment_credentials
			( '.implode( ', ', $ar_cols ).' )
		values
			( '.implode( ', ', $ar_vals ).' )
	');

	if( !$res ){
		error_log( __FILE__.':'.__LINE__.' error gu_users_payment_credentials_add : '.mysql_error() );
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer l'identifiant de paiement d'un compte client pour un module en particulier.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param int $module Obligatoire, constante d'identification du module
 *	@param int $upc_id Optionnel, identifiant de la carte
 *	@param bool $not_expiry Optionnel, par défaut True seules les cartes non expirées sont retournées, mettre False pour avoir celle expirées et null pour toutes les avoir
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant auto de la carte
 *				- crypt_ID : identifiant de paiement crypté
 *				- date_created : date de création de l'identifiant
 *				- name : nom donné à la carte
 *				- first_name : prénom du porteur de la carte
 *				- card_number : numéro de carte
 *				- card_brand : type de carte (CB, MASTERCARD...)
 *				- expiry_month : mois d'expiration de la carte
 *				- expiry_year : année d'expiration de la carte
 *				- bank_code : code de la banque à laquelle appartient la carte
 */
function gu_users_payment_credentials_get( $usr_id, $module, $upc_id=0, $not_expiry=true ){
	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !in_array($module, array('SYSTEMPAY', 'PAYZEN', 'ETRANSACTIONS', 'PAYPLUG')) ){
		return false;
	}

	if( !is_numeric($upc_id) || $upc_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			upc_id as id, upc_key as crypt_ID, upc_date_created as date_created, upc_name as name, upc_first_name as first_name, upc_card_number as card_number,
			upc_card_brand as card_brand, upc_expiry_month as expiry_month, upc_expiry_year as expiry_year, upc_bank_code as bank_code
		from gu_users_payment_credentials
		where upc_tnt_id = '.$config['tnt_id'].'
			and upc_usr_id = '.$usr_id.'
			and upc_module = "'.addslashes( $module ).'"
			and upc_is_deleted = 0
	';

	if( $upc_id>0 ){
		$sql .= ' and upc_id = '.$upc_id;
	}

	if( $not_expiry!==null ){
		if( $not_expiry ){
			$sql .= ' and last_day( concat(upc_expiry_year, "-", upc_expiry_month, "-01") ) >= now()';
		}else{
			$sql .= ' and last_day( concat(upc_expiry_year, "-", upc_expiry_month, "-01") ) <  now()';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'un identifiant de paiement existe pour un compte client dans un module de paiement.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param $module Obligatoire, constante d'identification du module
 *	@param $card_number Obligatoire, numéro de carte
 *
 *	@return bool True si l'ID existe, False dans le cas contraire
 */
function gu_users_payment_credentials_exists( $usr_id, $module, $card_number ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !in_array($module, array('SYSTEMPAY', 'PAYZEN', 'ETRANSACTIONS', 'PAYPLUG')) ){
		return false;
	}

	if( trim($card_number)=='' ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from gu_users_payment_credentials
		where upc_tnt_id = '.$config['tnt_id'].'
			and upc_usr_id = '.$usr_id.'
			and upc_module = "'.addslashes( $module ).'"
			and upc_card_number = "'.addslashes( format_CB($card_number) ).'"
			and upc_is_deleted = 0
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer virtuellement une carte bancaire enregistrée
 *	@param int $usr_id Obligatoire, identifiant d'un comtpe client
 *	@param $upc_id Obligatoire, identifiant d'une carte
 *	@return bool True si la suppresion s'est correctement déroulée, False dans le cas contraire
 */
function gu_users_payment_credentials_del( $usr_id, $upc_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($upc_id) || $upc_id<=0 ){
		return false;
	}

	return ria_mysql_query('
		 update gu_users_payment_credentials
		 set upc_date_deleted = now(),
		 	upc_is_deleted = 1
		 where upc_tnt_id = '.$config['tnt_id'].'
		 	and upc_usr_id = '.$usr_id.'
		 	and upc_id = '.$upc_id.'
	');

}

/// @}


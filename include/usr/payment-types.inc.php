<?php

/**	\defgroup gu_users_payments Moyens de paiements
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de définir les moyens de paiements autorisés ou non pour un client.
 *	@{
 */

 // \cond onlyria
/** Permet l'ajout d'un moyen de paiement à un compte client
 *	@param int $usr Identifiant du compte client
 *	@param int $pay Identifiant du moyen de paiement
 *	@param int $days Nombre de jours pour les paiements par compte
 *	@param int $days_type Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *	@param int $day_stop Jour de remise de la facture après expiration du délai
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_users_payment_types_add( $usr, $pay, $days, $days_type, $day_stop ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($pay) || $pay<=0 ) return false;
	if( !is_numeric($days) || $days<0 ) return false;
	if( !is_numeric($days_type) || $days_type<0 ) return false;
	if( !is_numeric($day_stop) || $day_stop<0 ) return false;

	$r = ria_mysql_query('
		replace into gu_users_payment_types (
			upt_tnt_id,upt_usr_id,upt_pay_id,upt_days,upt_days_type,upt_day_stop
		) values (
			'.$config['tnt_id'].','.$usr.','.$pay.','.$days.','.$days_type.','.$day_stop.'
		)
	');

	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Permet la mise à jour d'un moyen de paiement à un compte client
 *	@param int $usr Obligatoire, Identifiant du compte client
 *	@param int $pay Obligatoire, Identifiant du moyen de paiement
 *	@param int $days Obligatoire, Nombre de jours pour les paiements par compte
 *	@param int $days_type Obligatoire, Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *	@param int $day_stop Obligatoire, Jour de remise de la facture après expiration du délai
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_users_payment_types_update( $usr, $pay, $days, $days_type, $day_stop ){
	global $config;

	if( !is_numeric($usr) ) return false;
	if( !is_numeric($pay) ) return false;
	if( !is_numeric($days) ) return false;
	if( !is_numeric($days_type) ) return false;
	if( !is_numeric($day_stop) ) return false;

	$r = ria_mysql_query('
		update gu_users_payment_types
		set upt_days_type='.$days_type.', upt_day_stop='.$day_stop.'
		where upt_tnt_id='.$config['tnt_id'].' and upt_usr_id='.$usr.' and upt_pay_id='.$pay.' and upt_days='.$days.'
	');

	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un moyen de paiement pour un utilisateur donné.
 *	@param int $usr Identifiant de l'utilisateur
 *	@param int $pay Identifiant du moyen de paiement
 *	@param int $days nombre de jours
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_users_payment_types_del( $usr, $pay, $days=0 ){
	global $config;

	if( !is_numeric($usr) ) return false;
	if( !is_numeric($pay) ) return false;
	if( !is_numeric($days) ) return false;

	$r = ria_mysql_query('
		delete from gu_users_payment_types
		where upt_tnt_id='.$config['tnt_id'].' and upt_usr_id='.$usr.' and upt_pay_id='.$pay.' and upt_days='.$days.'
	');

	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un moyen de paiement pour un utilisateur donné.
 *	@param integer $usr_id Identifiant de l'utilisateur
 *	@return bool false en cas d'échec
 */
function gu_users_payment_types_del_all($usr_id) {
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		delete from gu_users_payment_types
		where upt_tnt_id='.$config['tnt_id'].'
			and upt_usr_id='.$usr_id.'
	');

	if( $r ){
		gu_users_set_date_modified( $usr_id );
	}

	return $r;
}
// \endcond

/**	Cette fonction récupère les relations entre utilisateurs et moyens de paiement, selon des paramètres optionnels
 *
 *	@param int|array $usr Optionnel, identifiant d'un utilisateur (ou tableau)
 *	@param int|array $pay Optionnel, identifiant d'un mode de paiement (ou tableau)
 *	@param bool $correled Optionnel, si $usr et $pay sont deux tableaux, permet de s'assurer que le premier élément de $usr est associé au premier élément de $pay, et ainsi de suite. Les deux tableaux doivent être de même taille.
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- usr : Identifiant de l'utilisateur
 *			- id : Identifiant du moyen de paiement
 *			- name : Désignation du moyen de paiement
 *			- days : Délai en jours avant paiement
 *			- days_type : Type de délai (0 : immédiat, 1 : fin de civil, 2 : fin de mois)
 *			- day_stop : Jour représentant la fin du mois
 *			- amount_type : type de répartition (0 = pourcentage, 1 = équilibrage, 2 = montant)
 *			- amount_value : valeur de répartition (NULL pour équilibrage)
 */
function gu_users_payment_types_get( $usr=0, $pay=false, $correled=false ){
	global $config;

	$usr = control_array_integer( $usr, false );
	if( $usr === false ){
		return false;
	}

	if( $pay === false ){
		$pay = 0;
	}

	$pay = control_array_integer( $pay, false );
	if( $pay === false ){
		return false;
	}

	if( $correled && sizeof($pay)!= sizeof($usr) ){
		return false;
	}

	$sql = '
		select
			upt_usr_id as usr, pay_id as id, pay_name as name, upt_days as days, upt_days_type as days_type, upt_day_stop as day_stop, upt_amount_type as amount_type, upt_amount_value as amount_value
		from gu_users_payment_types
			join ord_payment_types on
				upt_pay_id = pay_id and pay_tnt_id in (upt_tnt_id, 0) and pay_is_deleted = 0
		where
			upt_tnt_id = '.$config['tnt_id'].'
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($pay); $i++ ){
			$cnds[] = 'upt_usr_id = '.$usr[ $i ].' and upt_pay_id = '.$pay[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($usr) ){
			$sql .= ' and upt_usr_id in ('.implode(', ', $usr).')';
		}
		if( sizeof($pay) ){
			$sql .= ' and upt_pay_id in ('.implode(', ', $pay).')';
		}
	}

	$sql .= '
		order by pay_pos
	';

	$res = ria_mysql_query( $sql );

	if( isset($config['admin_usr_child_get_parent_paymode']) && $config['admin_usr_child_get_parent_paymode'] ){
		if( !$res || !ria_mysql_num_rows($res) ){
			$r_parent = ria_mysql_query('
				select usr_parent_id as id
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_id in ('.implode( ', ', $usr ).')
			');

			if( $r_parent ){
				$ar_parent_ids = array();
				while( $parent = ria_mysql_fetch_assoc($r_parent) ){
					$ar_parent_ids[] = $parent['id'];
				}

				if( sizeof($ar_parent_ids) ){
					return gu_users_payment_types_get( $ar_parent_ids, $pay, $correled );
				}
			}
		}
	}

	return $res;
}

/** Cette fonction permet de mettre en forme un texte permettant d'afficher les informations sur le moyen de paiement d'un client.
 *	@param $payment Obligatoire, information sur le moyen de paiement
 *	@return Une phrase décrivant le moyen de paiement
 */
function gu_users_payment_types_view( $payment ){
	if (!ria_array_key_exists(array('amount_type', 'amount_value', 'day_stop', 'days', 'days_type', 'name'), $payment)) {
		return '';
	}

	$value = '';
	if( $payment['days'] ){
		switch( $payment['amount_type'] ){
			case 0: // pourcentage
				$payment['amount_value'] = round($payment['amount_value'],2).'%';
				break;
			case 1: // équilibrage
				$payment['amount_value'] = 'équilibré';
				break;
			case 2: // montant
				$payment['amount_value'] = number_format($payment['amount_value'], 2, ',', '').'&euro;';
				break;
		}
		switch( $payment['days_type'] ){
			case 0: // net
				if( $payment['day_stop'] ){
					$value = htmlspecialchars($payment['name']).', paiement '.$payment['amount_value'].' à '.$payment['days'].' jours net le '.$payment['day_stop'];
				}else{
					$value = htmlspecialchars($payment['name']).', paiement '.$payment['amount_value'].' à '.$payment['days'].' jours net';
				}
				break;
			case 1: // FDM civil
				$value = htmlspecialchars($payment['name']).', paiement '.$payment['amount_value'].' à '.$payment['days'].' jours fin de mois civil';
				break;
			case 2: // FDM
				$value = htmlspecialchars($payment['name']).', paiement '.$payment['amount_value'].' à '.$payment['days'].' jours fin de mois';
				break;
		}
	}else{
		$value = htmlspecialchars($payment['name']);
	}

	return $value;
}

// \cond onlyria
/**	Cette fonction retourne le nombre de moyens de paiements autorisés pour un utilisateur donné.
 *	@param int $usr Identifiant de l'utilisateur
 *	@return le nombre de moyens de paiements autorisés pour cet utilisateur
 */
function gu_users_payment_types_get_count( $usr ){
	$r = gu_users_payment_types_get($usr);
	if( !$r ){
		return false;
	}
	return ria_mysql_num_rows( $r );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir le modèle de réglement pour un utilisateur.
 *	@param int $usr obligatoire : identifiant de l'utilisateur.
 *	@param $mdl_id facultatif : identifiant du modèle, si null alors le modèle sera retiré de l'utilisateur.
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function gu_users_payment_models_set( $usr, $mdl_id=null ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( $mdl_id !== null && !ord_payment_models_exists( $mdl_id ) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_opm_id = '.( $mdl_id === null ? 'null' : $mdl_id ).'
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr.'
			and usr_date_deleted is null
	');

}
// \endcond

// \cond onlyria
/**	Retourne le libellé associé au type de délai autorisé pour le paiement des factures.
 *	@param int $type Identifiant du type de délai
 *	@return string Le nom du type de délai
 *	@return string chaîne vide si le type passé en argument n'est pas supporté
 */
function gu_users_days_type_name( $type ){
	switch( $type ){
		case 0:
			return 'nets';
			break;
		case 1:
			return 'fin de mois civil';
			break;
		case 2:
			return 'fin de mois';
			break;
	}
	return '';
}
// \endcond

// \cond onlyria
/** Cette fonction retourne la phrase sur les conditions de paiements "Vos conditions de règlements sont les suivantes : à XXX le XXX".
 *	@param $type Obligatoire, identifiant du type ou directement une ligne de gu_users_payment_types_get()
 *	@return La phrase sur les conditions de paiement, False en cas d'erreur
 */
function gu_users_payment_types_get_desc( $type ){
	if( is_array($type) ){
		if( !isset($type['days'], $type['day_stop']) ){
			return false;
		}
	}else{
		if( !is_numeric($type) || $type<=0 ){
			return false;
		}

		$rtype = gu_users_payment_types_get( $type );
		if( !$rtype || !ria_mysql_num_rows($rtype) ){
			return false;
		}

		$type = ria_mysql_fetch_array( $rtype );
	}

	$conditions = '';
	if( $type['days']!='' && $type['day_stop']!='' ){
		$conditions = 'Vos conditions de règlements sont les suivantes : à '.$type['days'].' jours '.gu_users_days_type_name($type['days_type']).'.';
	}

	return $conditions;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un client a bien accès à un type de règlement.
 *	@param int $usr_id Identifiant d'un compte client
 *	@param int $pay_id Identifiant d'un moyen de paiement
 *	@return bool True si le client a bien accès, False dans le cas contraire
 */
function gu_users_payment_types_exists( $usr_id, $pay_id ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if (!is_numeric($pay_id) || $pay_id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select 1 from gu_users_payment_types
		where upt_tnt_id = '.$config['tnt_id'].'
			and upt_usr_id = '.$usr_id.'
			and upt_pay_id = '.$pay_id.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

/// @}


<?php

// \cond onlyria
/**	\defgroup gu_users_lng Multi-lingue
 *	\ingroup model_users i18n
 *	\addtogroup translate_object_values
 *	Les fonctions de ce module permettent de gérer les langues parlées d'un utilisateur
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la langue d'un compte utilisateur
 *	@param int $usr Obligatoire, Identifiant du compte
 *	@param string $lng Obligatoire, code ISO de la langue (insensible à la casse)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_lng_code( $usr, $lng ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	$lng = strtolower(trim($lng));
	if( !i18n_languages_exists( $lng ) ) return false;

	global $config;

	$sql = '
		update gu_users
		set usr_lng_code=\''.addslashes($lng).'\'
		where usr_id='.$usr.' and usr_date_deleted is null
	';

	if( isset($config['USER_RIASTUDIO']) && $config['USER_RIASTUDIO'] ){
		$sql .= ' and usr_tnt_id=0';
	}else{
		$sql .= ' and usr_tnt_id='.$config['tnt_id'];
	}

	return ria_mysql_query( $sql );
}
// \endcond

/**	Cette fonction récupère la langue d'un compte utilisateur
 *	@param int $usr Identifiant de l'utilisateur
 *	@return bool False en cas d'échec
 *	@return string Le code ISO de la langue de l'utilisateur
 */
function gu_users_get_lng_code( $usr ){
	if( !is_numeric($usr) || $usr<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select usr_lng_code from gu_users
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.' and usr_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

/// @}


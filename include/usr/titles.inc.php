<?php

/** \defgroup usr_titles Civilités
 *	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des civilités.
 *	@{
 */

/**	Retourne la liste des civilités, éventuellement filtrée en fonction des paramètres optionnels.
 *
 *	@param int $id Optionnel, identifiant d'une civilité sur laquelle filtrer le résultat
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes id et name.
 *
 */
function gu_titles_get( $id=0 ){
	if( !is_numeric($id) || $id<0 ) return false;

	$sql = 'select title_id as id, title_name as name, title_abr as abr from gu_titles';
	if( $id ){
		$sql .= ' where title_id='.$id.' limit 0,1';
	}

	return ria_mysql_query($sql);
}

/**	Retourne la liste des civilités sous forme de tableau associatif
 *
 *	@return array la liste des civilités, sous forme de tableau contenant les clés suivantes :
 *		- id : identifiant
 *		- name : désignation
 *		- abr : forme abrégée
 *
 */
function gu_titles_get_array(){

	$ar_titles = array();

	$titles = gu_titles_get();
	while( $title = ria_mysql_fetch_array($titles) ){
		$ar_titles[ $title['id'] ] = $title;
	}

	return $ar_titles;
}

// \cond onlyria
/** Cette fonction permet de récupérer la civilité selon son identifiant.
 *	@param int $title_id Obligatoire, identifiant d'une civilité
 *	@return string Le nom de la civilité si elle est trouvée, False dans tous les autres cas
 */
function gu_title_get_name( $title_id ){
	if( !is_numeric($title_id) || $title_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select title_id as id, title_name as name
		from gu_titles
		where title_id = '.$title_id.'
		limit 0,1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['name'];
}
// \endcond

// \cond onlyria
/**	Teste l'existance d'une civilité d'identifiant $id
 *
 *	@param int $id Identifiant de la civilité à tester.
 *
 *	@return bool true si l'identifiant existe dans la base de données, false dans le cas contraire.
 *
 */
function gu_titles_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(gu_titles_get($id))>0;
}
// \endcond

/// @}
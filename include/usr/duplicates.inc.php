<?php

// \cond onlyria
/**	\defgroup gu_users_duplicates Gestion des doublons
 *	\ingroup model_users
 *	Les fonctions de ce module permettent la gestion des doublons de comptes utilisateurs
 *	@{
 */
// \endcond

// \cond onlyria
/** Recherche si le client actuel est un doublon d'un client synchronisé ou d'un compte client dans riashop avec only_synced=false
 *
 * @param int $usr_id Obligatoire, identifiant du client
 * @param string $email Obligatoire, adresse email de ce client
 * @param $only_synced Facultatif, Détermine si on fait la vérification que sur les comptes clients synchronisés ou non
 *
 * @return retourne l'identifiant du client à conserver
*/
function gu_users_get_doublon_email( $usr_id, $email, $only_synced=true ) {
	global $config;

	if( !is_numeric($usr_id) || !gu_users_exists($usr_id) ){
		return 0;
	}

	$sql = ria_mysql_query('
		select usr_id
		from gu_users
		where
			(usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and
			usr_id!='.$usr_id.' and
			usr_date_deleted is null and
			'.($only_synced ? 'usr_ref!="" and' : '').'
			lower(usr_email)=\''.addslashes( strtolower($email) ).'\'
			and usr_can_login=1
	');

	if( $new_usr = ria_mysql_fetch_array($sql) ){
		return $new_usr['usr_id'];
	} else {
		return 0;
	}
}
// \endcond

// \cond onlyria
/** Recherche si le client actuel est un doublon d'un client synchronisé
 *
 * @param int $usr Obligatoire, identifiant du client
 * @param string $name Obligatoire, raison sociale du client (nom de société ou nom + prénom)
 * @param $address Obligatoire, adresse principal du client
 * @param string $city Obligatoire, ville du client
 *
 * @return retourne l'identifiant du client à conserver
 */
function gu_users_get_doublon_address( $usr, $name, $address, $city ){
	global $config;
	if( !is_numeric($usr) || !gu_users_exists($usr) ) return 0;

	$sql = ria_mysql_query('
		select usr_id
		from gu_users, gu_adresses
		where
			(usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and adr_tnt_id=usr_tnt_id and
			usr_ref!="" and
			usr_date_deleted is null and
			usr_adr_invoices=adr_id and
			usr_id!='.$usr.' and (
				(
					replace(replace(replace(adr_society, "\'", ""), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-'), '', $name)).'\' and
					replace(replace(replace(replace(adr_address1, "\'", ""), "saint", "st"), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', 'saint'), array('', '', '', 'st'), $address)).'\' and
					replace(replace(replace(replace(adr_city, "\'", ""), "saint", "st"), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', 'saint'), array('', '', '', 'st'), $city)).'\'
				) or (
					replace(replace(replace(concat( adr_lastname, \' \', adr_firstname ), "\'", ""), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-'), '', $name)).'\' and
					replace(replace(replace(replace(adr_address1, "\'", ""), "saint", "st"), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', 'saint'), array('', '', '', 'st'), $address)).'\' and
					replace(replace(replace(replace(adr_city, "\'", ""), "saint", "st"), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', 'saint'), array('', '', '', 'st'), $city)).'\'
				)
			)
	');

	if ($new_usr = ria_mysql_fetch_array($sql)) {
		return $new_usr['usr_id'];
	} else {
		return 0;
	}
}
// \endcond

// \cond onlyria
/** Recherche si le client actuel est un doublon d'un client synchronisé
 *
 * @param int $usr Obligatoire, identifiant du client
 * @param string $name Obligatoire, raison sociale du client (nom de société ou nom + prénom)
 * @param string $phone Obligatoire, téléphone du client
 *
 * @return retourne l'identifiant du client à conserver
 */
function gu_users_get_doublon_phone( $usr, $name, $phone ){
	global $config;
	if( !is_numeric($usr) || !gu_users_exists($usr) ) return 0;

	$sql = ria_mysql_query('
		select usr_id
		from gu_users, gu_adresses
		where
			(usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and adr_tnt_id=usr_tnt_id and
			usr_ref!="" and
			usr_date_deleted is null and
			usr_adr_invoices=adr_id and
			usr_id!='.$usr.' and (
				(
					replace(replace(replace(adr_society, "\'", ""), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-'), '', $name)).'\' and
					replace(replace(replace(replace(adr_phone, "\'", ""), ".", ""), "-", ""), " ", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', ' '), '', $phone)).'\'
				) or (
					replace(replace(replace(concat( adr_lastname, \' \', adr_firstname ), "\'", ""), ".", ""), "-", "")=\''.strtoupper2(str_replace(array('\'', '.', '-'), '', $name)).'\' and
					replace(replace(replace(replace(adr_phone, "\'", ""), ".", ""), "-", ""), " ", "")=\''.strtoupper2(str_replace(array('\'', '.', '-', ' '), '', $phone)).'\'
				)
			)
	');

	if ($new_usr = ria_mysql_fetch_array($sql)) {
		return $new_usr['usr_id'];
	} else {
		return 0;
	}
}
// \endcond

// \cond onlyria
/** Met à jour les informations d'un client existant et synchronisé à partir des informations d'un client en doublon non synchronisé + supression de ce dernier
 *
 * @param int $old_usr Obligatoire, identifiant du client synchronisé
 * @param int $new_usr Obligatoire, identifiant du client à supprimer
 *
 * @return retourne TRUE si succès, FALSE sinon
 *
 * \note les opérations effectuées dans cette fonction ne nécessite pas d'intervention dans le cache de la synchro.
 */
function gu_users_replace( $old_usr, $new_usr ){
	if( !is_numeric($old_usr) || !gu_users_exists($old_usr) ) return false;
	if( !is_numeric($new_usr) || !gu_users_exists($new_usr) ) return false;
	if( !gu_users_is_tenant_linked($old_usr) ) return false;
	if( !gu_users_is_tenant_linked($new_usr) ) return false;
	global $config;

	// récupère les infos utiles sur le nouveau client
	$sql1 = ria_mysql_query('
		select usr_id, usr_adr_invoices, usr_email, usr_password, usr_last_login, adr_type_id, adr_title_id, adr_firstname, adr_lastname, adr_society, adr_siret, adr_address1, adr_address2, adr_address3, adr_postal_code, adr_city, adr_country, adr_phone, adr_fax, adr_mobile, adr_phone_work, adr_email
		from gu_users, gu_adresses
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and adr_tnt_id=usr_tnt_id and usr_adr_invoices=adr_id and
		usr_id='.$new_usr
	);

	$sql2 = ria_mysql_query('
		select usr_id, usr_adr_invoices
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$old_usr
	);

	if( ($new_data = ria_mysql_fetch_array($sql1)) && ($old_data = ria_mysql_fetch_array($sql2)) ) {

		// met à jour l'ancien compte client [email/mot de passe/date de dernière connexion]
		ria_mysql_query('
			update gu_users set
				usr_email=\''.addslashes($new_data['usr_email']).'\',
				usr_password=\''.addslashes($new_data['usr_password']).'\',
				usr_last_login=\''.addslashes($new_data['usr_last_login']).'\'
			where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$old_data['usr_id']
		);

		// met à jour l'adresse de facturation de l'ancien compte client (et elle est marqué comme non synchronisé)
		ria_mysql_query('
			update gu_adresses set
			adr_type_id='.$new_data['adr_type_id'].',
			adr_title_id='.( $new_data['adr_title_id'] ? $new_data['adr_title_id'] : 'null' ).',
			adr_firstname=\''.addslashes($new_data['adr_firstname']).'\',
			adr_lastname=\''.addslashes($new_data['adr_lastname']).'\',
			adr_society=\''.addslashes($new_data['adr_society']).'\',
			adr_siret=\''.addslashes($new_data['adr_siret']).'\',
			adr_address1=\''.addslashes($new_data['adr_address1']).'\',
			adr_address2=\''.addslashes($new_data['adr_address2']).'\',
			adr_address3=\''.addslashes($new_data['adr_address3']).'\',
			adr_postal_code=\''.addslashes($new_data['adr_postal_code']).'\',
			adr_city=\''.addslashes($new_data['adr_city']).'\',
			adr_country=\''.addslashes($new_data['adr_country']).'\',
			adr_phone=\''.addslashes($new_data['adr_phone']).'\',
			adr_fax=\''.addslashes($new_data['adr_fax']).'\',
			adr_mobile=\''.addslashes($new_data['adr_mobile']).'\',
			adr_phone_work=\''.addslashes($new_data['adr_phone_work']).'\',
			adr_email=\''.addslashes($new_data['adr_email']).'\',
			adr_need_sync=1
			where adr_tnt_id='.$config['tnt_id'].' and adr_id='.$old_data['usr_adr_invoices']
		);

		// met à jour l'id client des autres adresses du nouveau client (elle sont transmises à l'ancien)
		ria_mysql_query('
			update gu_adresses set
			adr_usr_id='.$old_data['usr_id'].'
			where adr_tnt_id='.$config['tnt_id'].' and adr_usr_id='.$new_data['usr_id'].' and adr_id!='.$new_data['usr_adr_invoices']
		);

		// met à jour les identifiants client et adresse de facturation des commandes passés par le nouveau client (on évite celles annulées car souvent source d'erreurs)
		ria_mysql_query('
			update ord_orders set
			ord_usr_id='.$old_data['usr_id'].',
			ord_adr_invoices='.$old_data['usr_adr_invoices'].'
			where ord_tnt_id='.$config['tnt_id'].' and ord_state_id!=10 and ord_usr_id='.$new_data['usr_id']
		);

		// met à jour l'adresse de livraison des commandes passés par le nouveau client quand cette adresse est la même que facturation
		ria_mysql_query('
			update ord_orders set
			ord_adr_delivery='.$old_data['usr_adr_invoices'].'
			where ord_tnt_id='.$config['tnt_id'].' and ord_adr_delivery='.$new_data['usr_adr_invoices']
		);

		// suppression virtuel du compte du nouveau client
		ria_mysql_query('
			update gu_users set
			usr_date_deleted=now()
			where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$new_data['usr_id']
		);

		return true;
	}else{
		return false;
	}
}
// \endcond

// \cond onlydev
/// @}
// \endcond

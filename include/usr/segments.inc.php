<?php

// \cond onlyria
/**	\defgroup gu_users_segments Segmentation
 *	\ingroup crm
 *	Les fonctions de ce module permettent la gestion des segments de comptes utilisateurs
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction permet la récupération des segments qui incluent un utilisateur donné
 *	@param int $usr_id Obligatoire, Identifiant de l'utilisateur
 *	@param int $limit Optionnel, nombre de segments à retourner (par défaut, un seul, le meilleur selon l'ordre de priorité). Un nombre inférieur ou égal à 0 équivaut à une absence de limite
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau contenant tous les identifiants de segments inclus
 */
function gu_users_get_segments( $usr_id, $limit=1 ){
	if( !is_numeric($usr_id) || $usr_id<=0 ) return false;
	if( !is_numeric($limit) ) return false;

	global $config, $memcached;

	$key_memcached = 'gu_users_get_segments:tnt-'.$config['tnt_id'].':wst-'.$config['wst_id'].':usr-'.$usr_id.':limit-'.$limit;
	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'none' ? array() : $get);
	}

	$rsegs = seg_segments_get( 0, CLS_USER );
	if( !$rsegs ) return false;

	$segments = array();

	while( $s = ria_mysql_fetch_array($rsegs) ){
		if( $limit>0 && sizeof($segments)==$limit ) break;

		if( gu_users_get_by_segment( $s['id'], $usr_id ) ){
			$segments[] = $s['id'];
		}
	}

	$memcached->set( $key_memcached, (!count($segments) ? 'none' : $segments), 60 );
	return $segments;
}
// \endcond

/**	Cette fonction permet deux choses :
 *		- Lister (ou compter) les comptes respectant les critères d'un segment donné
 *		- Vérifier qu'un compte donné respecte les critères d'un segment donné
 *	@param int $seg_id Obligatoire, identifiant du segment à tester
 *	@param int $usr_id Optionnel, identifiant du compte à tester
 *	@param bool $inc_no_tenant Optionnel, détermine si les comptes sans rattachement à un locataire sont décomptés (paramètre non pris en compte si $usr_id spécifié)
 *	@param bool $no_customers Optionnel, détermine si les comptes ne pouvant passer commande (commerciaux, fournisseurs) doivent être exclus du résultat (paramètre non pris en compte si $usr_id spécifié)
 *
 *	@return bool True si $usr_id activé et que les critères du segment sont respectés pour ce compte
 *	@return array Un tableau des identifiants usr_id valides pour le segment
 *	@return bool False dans les autres cas (erreur, critères non respectés)
 */
function gu_users_get_by_segment( $seg_id, $usr_id = null, $inc_no_tenant = false, $no_customers = false ){

	// Vérifie le paramètre seg_id
	if( !is_numeric( $seg_id ) || $seg_id<=0 ){
		return false;
	}

	// Vérifie le paramètre usr_id
	if( $usr_id !== null && !is_numeric($usr_id) ){
		return false;
	}

	global $config;

	$sql = '
		select '.( $usr_id ? 'count(*)' : 'usr_id as id' ).'
		from
			gu_users as u
			left join gu_adresses on usr_id = adr_usr_id and usr_adr_invoices = adr_id and usr_tnt_id = adr_tnt_id
		where
			usr_date_deleted is null and adr_date_deleted is null
	';

	if( $inc_no_tenant || $usr_id ){
		$sql .= ' and usr_tnt_id in (0, '.$config['tnt_id'].')';
	}else{
		$sql .= ' and usr_tnt_id = '.$config['tnt_id'];
	}

	if( $usr_id ){
		$sql .= ' and usr_id = '.$usr_id;
	}

	if( $no_customers && !$usr_id ){
		$sql .= ' and usr_prf_id not in ('.PRF_SELLER.', '.PRF_SUPPLIER.')';
	}

	$sql_conditions = seg_segments_get_sql_user( $seg_id );

	if( !is_array($sql_conditions) ){
		return false;
	}

	if( sizeof($sql_conditions) ){
		$sql .= ' and ( ('.implode(') and (', $sql_conditions).') )';
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
		return false;
	}

	if( $usr_id ){
		return ria_mysql_result($r, 0, 0);
	}

	$users = array();
	while( $u = ria_mysql_fetch_array($r) ){
		$users[] = $u['id'];
	}

	return $users;

}

// \cond onlydev
/// @}
// \endcond

<?php

// \cond onlyria
/** \defgroup trading_rules Bornage des négociations
 *	\ingroup yuto model_users
 *	Ce module comprend les fonctions nécessaires à la gestion des négociations sur les prix par les commerciaux
 *	@{
 */

/** Cette fonction permet l'enregistrement d'une régle pour borner les négociations pour un utilisateur
 *	@param int $usr_id Optionnel, Identifiant de l'utilisateur, si null alors tous les utilisateurs auront la régle
 *	@param $fld_id Optionnel, Identifiant du champ concerné par la regle, null pour que la regle soit valable sur tous les produits
 *	@param $value Optionnel, Si fld_id est renseigné, valeur du champ concerné
 *	@param $price_fld_id Obligatoire, Identifiant du champ concerné par le calcul du min possible ( ne doit être que des champs de tarifs, sinon la regle n'aura pas d'effet), peux prendre un code style "price_public_ht, price_user_ht, ..."
 *	@param $price_value Optionnel,  Valeur du montent de la réduction
 *	@param $price_type Optionnel,  Correspond au type de price_value, valeur possible: 'percent' ou 'discount'
 *	@param $min_ord Optionnel, Permet de dire sur quel quantité minimum de produit la règle peut s'appliquer.
 *
 *	@return bool false dans le cas ou l'ajout a échoué, sinon l'identifiant de la ligne créée
 */
function gu_trading_rules_add( $usr_id=null, $fld_id=null, $value=null, $price_fld_id, $price_value=0, $price_type='', $min_ord=0 ){
	if( $usr_id!=null && (!is_numeric($usr_id) || $usr_id < 0) ) return false;
	if( $fld_id!=null && (!is_numeric($fld_id) || $fld_id < 0) ) return false;
	if( !is_numeric($price_value) ) return false;
	if( !is_numeric($min_ord) || $min_ord < 0 ) return false;
	global $config;
	$cols = $vals = array();

	$cols[] = 'gtr_tnt_id';
	$vals[] = $config['tnt_id'];

	$cols[] = 'gtr_usr_id';
	$vals[] = $usr_id ? $usr_id : '0';

	$cols[] = 'gtr_fld_id';
	$vals[] = $fld_id ? $fld_id : 'null';

	$cols[] = 'gtr_value';
	$vals[] = $fld_id && $value !== null ? '\''.ria_mysql_escape_string($value).'\'' : 'null';

	$cols[] = 'gtr_price_fld_id';
	$vals[] = '\''.ria_mysql_escape_string($price_fld_id).'\'';

	if ( $price_type == 'percent' ) {
		$cols[] = 'gtr_price_rate';
		$vals[] = $price_value;
	}elseif ( $price_type == 'discount' ) {
		$cols[] = 'gtr_price_discount';
		$vals[] = $price_value;
	}

	$cols[] = 'gtr_min_ord';
	$vals[] = $min_ord;

	$cols[] = 'gtr_date_created';
	$vals[] = 'now()';
	if( !ria_mysql_query('insert into gu_trading_rules ('.implode(',', $cols).') values ('.implode(',', $vals).')') ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet la mise à jour d'une régle pour borner les négociations pour un utilisateur
 *	@param int $id Identifiant de la régle
 *	@param int $usr_id Optionnel, Identifiant de l'utilisateur, si null alors tous les utilisateurs auront la régle
 *	@param $fld_id Optionnel, Identifiant du champ concerné par la regle, null pour que la regle soit valable sur tous les produits
 *	@param $value Optionnel, Si fld_id est renseigné, valeur du champ concerné
 *	@param $price_fld_id Obligatoire, Identifiant du champ concerné par le calcul du min possible ( ne doit être que des champs de tarifs, sinon la regle n'aura pas d'effet), peux prendre un code style "price_public_ht, price_user_ht, ..."
 *	@param $price_value Optionnel,  Valeur du montent de la réduction
 *	@param $price_type Optionnel,  Correspond au type de price_value, valeur possible: 'percent' ou 'discount'
 *	@param $min_ord Optionnel, Permet de dire sur quel quantité minimum de produit la règle peut s'appliquer.
 *
 *	@return bool false dans le cas ou l'ajout a échoué, sinon l'identifiant de la ligne créée
 */
function gu_trading_rules_upd( $id, $usr_id=null, $fld_id=null, $value=null, $price_fld_id, $price_value=0, $price_type='', $min_ord=0){
	if( !is_numeric($id) || $id < 0 ) return false;
	if( $usr_id!=null && (!is_numeric($usr_id) || $usr_id < 0) ) return false;
	if( $fld_id!=null && (!is_numeric($fld_id) || $fld_id < 0) ) return false;
	$upd = array();
	if( !is_numeric($price_value) ) return false;
	if( !is_numeric($min_ord) || $min_ord < 0 ) return false;
	global $config;

	$upd[] = 'gtr_usr_id='.($usr_id ? $usr_id : '0');
	$upd[] = 'gtr_fld_id='.($fld_id ? $fld_id : 'null');
	$upd[] = 'gtr_value='.($fld_id && $value !== null ? '\''.ria_mysql_escape_string($value).'\'' : 'null');
	$upd[] = 'gtr_price_fld_id=\''.ria_mysql_escape_string($price_fld_id).'\'';

	if ($price_type == 'percent') {
		$upd[] = 'gtr_price_rate='.$price_value;
		$upd[] = 'gtr_price_discount=0';

	}elseif ($price_type=='discount') {
		$upd[] = 'gtr_price_rate=1';
		$upd[] = 'gtr_price_discount='.$price_value;
	}

	$upd[] = 'gtr_min_ord='.$min_ord;

	$where = array();
	$where[] = 'gtr_tnt_id='.$config['tnt_id'];
	$where[] = 'gtr_id='.$id;

	return ria_mysql_query('update gu_trading_rules set '.implode(',', $upd).' where '.implode(' and ', $where));
}

/** Cette fonction permet de supprimer un règle de négociation. Il est possible soit de supprimer une règle par son identifiant, soit de supprimer toutes les règles d'un utilisateur donné.
 *	@param int $id Facultatif, identifiant de la règle de négociation à supprimer
 *	@param int $usr_id Facultatif, identifiant de l'utilisateur pour lequel supprimer les règles de négociation
 *
 *	@return bool false dans le cas ou la suppression a échouée, sinon true
 */
function gu_trading_rules_del( $id=null, $usr_id=null ){
	if( $id != null && ( !is_numeric($id) || $id < 0 ) ) return false;
	if( $usr_id != null && ( !is_numeric($usr_id) || $usr_id < 0 ) ) return false;
	global $config;
	$upd = array();

	$upd[] = 'gtr_date_deleted=now()';

	$where = array();
	$where[] = 'gtr_tnt_id='.$config['tnt_id'];

	if ($id != null) {
		$where[] = 'gtr_id='.$id;
	}

	if ($usr_id != null) {
		$where[] = 'gtr_usr_id='.$usr_id;
	}

	return ria_mysql_query('update gu_trading_rules set '.implode(',', $upd).' where '.implode(' and ', $where));
}

/** Cette fonction permet de récupérer la liste des régles de négociations
 *	@param int $usr_id Optionnel, Identifiant de l'utilisateur,
 *			-1: Ne filter pas sur les utilisateurs
 *			0: Filtre les utilisateurs usr_id == 0
 *			id: Filtre les utilisateurs usr_id == id
 *	@param $fld_id Optionnel, Identifiant du champ concerné par la regle
 *	@param $value Optionnel, si fld_id est renseigné, valeur du champ concerné
 *	@param $price_fld_id Optionnel, Identifiant du champ concerné par le calcul du min possible
 *	@param $min_ord Optionnel, quantité min pour appliquer la regle
 *	@param $only_usr_id Facultatif, permet de retourner uniquement les utilisateur identique à $usr_id
 *	@param $gtr_id Facultatif, identifiant d'un groupe de règles de négociation sur laquelle filtrer le résultat
 *	@param $user_group_by Facultatif, Groupe par utlisateurs
 *	@param array $sort Facultatif, paramètres de tri du résultat sous forme de tableau "colonne"=>ordre de tri. Seule la valeur usr_id est acceptée actuellement.
 *
 *	@return bool false en cas d'échec, sinon un résultat de ria_mysql_query
 */
function gu_trading_rules_get( $usr_id=null, $fld_id=null, $value=null, $price_fld_id=null, $min_ord=0, $only_usr_id=false, $gtr_id=null, $user_group_by=false, $sort=array() ){

	if( $usr_id!=null && !is_numeric($usr_id) ) return false;
	if( $fld_id!=null && !is_numeric($fld_id) ) return false;
	if( $gtr_id!=null && !is_numeric($gtr_id) ) return false;
	if( !is_numeric($min_ord) || $min_ord < 0 ) return false;
	global $config;
	$sql  = 'select gtr_id as id, gtr_usr_id as usr_id, gtr_fld_id as fld_id, gtr_value as value, gtr_price_fld_id as price_fld_id, gtr_price_rate as price_rate, ';
	$sql .= ' if(gtr_price_discount > 0, gtr_price_discount, gtr_price_rate) as price_value, if(gtr_price_discount > 0, "discount", "percent") as price_type, ';

	if ($user_group_by) {
		$sql .= 'count(gtr_id) as total_user_rules, ';
	}

	$sql .= '	gtr_price_discount as price_discount, gtr_min_ord as min_ord, gtr_date_created as date_created, gtr_date_modified as date_modified, ';
	$sql .= '	fld_name, fld_type_id as type_id, fld_cls_id as cls_id, fld_physical_name as physical_name ';
	$sql .= 'from gu_trading_rules ';
	$sql .= 'left join fld_fields on (gtr_tnt_id=fld_tnt_id or 0=fld_tnt_id) and gtr_fld_id=fld_id ';
	$sql .= 'where gtr_tnt_id='.$config['tnt_id'].' ';

	if (is_numeric($usr_id)) {

		if( $usr_id != -1){
			if ($only_usr_id) {
				$sql .= 'and gtr_usr_id='.$usr_id.' ';
			}else{
				$sql .= 'and (gtr_usr_id='.$usr_id.' or gtr_usr_id=0) ';
			}
		}
	}else{
		$sql .= 'and gtr_usr_id=0 ';
	}

	if( $fld_id == -1 ){
		$sql .= 'and gtr_fld_id is null ';

	}else if( is_numeric($fld_id) ){
		$sql .= 'and gtr_fld_id='.$fld_id.' ';

		if( $value!=null ){
			$sql .= 'and gtr_value=\''.ria_mysql_escape_string($value).'\' ';
		}
	}

	if ( is_numeric($gtr_id) ) {
		$sql .= ' and gtr_id='.$gtr_id.' ';
	}


	if( $price_fld_id!=null ){
		$sql .= 'and gtr_price_fld_id=\''.ria_mysql_escape_string($price_fld_id).'\' ';
	}

	$sql .= 'and gtr_min_ord <= '.$min_ord.' ';
	$sql .= 'and gtr_date_deleted is null ';

	if ($user_group_by) {
		$sql .= 'group by gtr_usr_id ';
	}


	// Converti le paramètre de tri en SQL
	$sort_final = array();
	if( is_array($sort) && sizeof($sort) > 0 ){
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'usr_id':
					$sort_final[] = 'gtr_usr_id '.$dir;
					break;
			}
		}

		$sql .= ' order by '.implode(', ', $sort_final);

	}else{

		$sql .= 'order by
			if(fld_cls_id='.CLS_PRODUCT.', 1000,
				if(fld_cls_id='.CLS_BRAND.', 900,
					if(fld_cls_id='.CLS_CATEGORY.', 800, 0)
				)
			) DESC
		';

	}


	return ria_mysql_query($sql);
}

/** Cette fonction permet de connaître le prix minimum qu'un utilisateur peux pratiquer.
 *	@param int $usr_id Obligatoire, Identifiant de l'utilisateur qui appliquer la remise
 *	@param $dst_usr_id Obligatoire, Identifiant de l'utilisateur qui doit recevoir la remise
 *	@param $prd_id Obligatoire, Identifiant du produit
 *	@param $qte Optionnel, Quantité commandé du produit
 *
 *	@return bool False en cas d'échec ou le montant minimum que l'utilisateur peut appliquer à un produit.
 */
function gu_trading_rules_get_min( $usr_id, $dst_usr_id, $prd_id, $qte=1 ){
	if( !is_numeric($usr_id) || $usr_id < 0 ) return false;
	if( !is_numeric($dst_usr_id) || $dst_usr_id < 0 ) return false;
	if( !is_numeric($prd_id) || $prd_id < 0 ) return false;
	if( !is_numeric($qte) || $qte < 0 ) return false;
	global $config;

	// récupère la liste des régles
	$rules = gu_trading_rules_get($usr_id, null, null, null, $qte);

	// si aucune règle alors le prix possible est de 0.
	if( !$rules || ria_mysql_num_rows($rules)==0 ){
		return 0;
	}

	$product_find = false;
	$price_fld = 0;
	$price_rate = 1;
	$price_discount = 0;

	while( $rule = ria_mysql_fetch_assoc($rules) ){
		$rule['symbol'] = '=';

		$price_fld = $rule['price_fld_id'];
		$price_rate = $rule['price_rate'];
		$price_discount = $rule['price_discount'];

		// formatage en fonction du type de champ
		{
			$left_type_fld = 'pv_value';
			$right_type_fld = '"'.addslashes($rule['value']).'"';
			$full_clause = false;
			$clause_null = '0';
			$is_bool_null = false;
			$lws = $lwe = '';

			switch( $rule['fld_type_id'] ){
				case FLD_TYPE_INT:
					$left_type_fld = 'cast(pv_value as signed)';
					$right_type_fld = 'cast("'.addslashes($rule['value']).'" as signed)';
					$clause_null = '(0 '.$rule['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_FLOAT:
					$left_type_fld = 'cast(pv_value as decimal)';
					$right_type_fld = 'cast("'.addslashes($rule['value']).'" as decimal)';
					$clause_null = '(0 '.$rule['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_DATE:
					$left_type_fld = 'cast(pv_value as datetime)';
					$right_type_fld = 'cast("'.addslashes($rule['value']).'" as datetime)';
					$clause_null = '("1900-01-01 00:00:00" '.$rule['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_REFERENCES_ID:
					$left_type_fld = 'cast(pv_value as decimal)';
					$right_type_fld = 'cast("'.addslashes($rule['value']).'" as decimal)';
					break;
				case FLD_TYPE_BOOLEAN_YES_NO:
					$left_type_fld = '(pv_value="1" or lower(pv_value)="oui")';
					$right_type_fld = '("'.addslashes($rule['value']).'"="1" or lower("'.addslashes($rule['value']).'")="oui")';
					$clause_null = '(0 '.$rule['symbol']. ' '.$right_type_fld.')';
					$is_bool_null = in_array(strtolower(trim($rule['value'])), array('0', 'non'));
					break;
				case FLD_TYPE_SELECT_HIERARCHY:
					// récupération des valeurs enfants (multi-niveaux)
					$childs = fld_restricted_values_get_childs_array( $rule['value'] );
					if( !is_array($childs) || !sizeof($childs) ){
						$childs = array();
					}

					// on ajoute la valeur initiale au tableau de valeurs
					$childs[] = $rule['value'];

					$nega = $rule['symbol'] != '=';

					$cond_pvv = array();
					foreach( $childs as $child ){
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "%, '.addslashes($child).',%"';
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "'.addslashes($child).', %"';
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "%, '.addslashes($child).'"';
						$cond_pvv[] = 'pv_value '.( $nega ? '!' : '' ).'= "'.addslashes($child).'"';
					}
					$full_clause = '('.implode(($nega ? ' and ' : ' or '), $cond_pvv).')';
					break;
				case FLD_TYPE_SELECT_MULTIPLE:
					$nega = $rule['symbol'] != '=';

					$full_clause = '(
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("%, '.addslashes($rule['value']).',%") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("'.addslashes($rule['value']).', %") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("%, '.addslashes($rule['value']).'") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? '!' : '' ).'= lower("'.addslashes($rule['value']).'")
					)';
					break;
				case FLD_TYPE_SELECT:
				case FLD_TYPE_TEXT:
					$left_type_fld = 'lower(pv_value)';
					$right_type_fld = 'lower("'.addslashes($rule['value']).'")';
					if( in_array($rule['symbol'], array('LIKE', 'NOT LIKE')) ){
						$right_type_fld = 'lower("%'.addslashes($rule['value']).'%")';
						$clause_null = '("" '.$rule['symbol']. ' '.$right_type_fld.')';
					}
					$lws = 'lower('; $lwe = ')'; // mise en minuscules
					break;
			}

			$cndvsql = '
				select pv_obj_id_0 from fld_object_values
				where pv_tnt_id='.$config['tnt_id'].' and pv_fld_id='.addslashes($rule['fld_id']).'
				and pv_lng_code="'.addslashes($config['i18n_lng']).'" and pv_obj_id_1=0 and pv_obj_id_2=0
			';

			$bool_null_sql = '';
			if( $is_bool_null ){
				$bool_null_sql = $cndvsql;
			}

			if( $full_clause===false ){
				$cndvsql .= ' and '.$left_type_fld.' '.$rule['symbol'].' '.$right_type_fld;
			}else{
				$cndvsql .= ' and '.$full_clause;
			}
		}

		$sqlgrouprow_prd = $sqlgrouprow_brd = $sqlgrouprow_cat = ' 1';

		if( $rule['cls_id'] == null ){
			$product_find = true;
			break;
		}elseif( $rule['cls_id']==CLS_PRODUCT ){ // la condition est sur un produit

			// requête produit
			{
				if( $rule['physical_name']!='' )
					$sqlgrouprow_prd = $lws.'prd.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.' ';
				else{
					$sqlgrouprow_prd = '(
						prd.prd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_prd .= ' or prd.prd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_prd .= '
						)
					';
				}
			}
			// requête catégorie
			{
				$sqlgrouprow_cat = '
					exists (
						select 1 from prd_classify as clyrec
						where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_cat_id=c.cat_id
						and exists (
							select 1 from prd_products as prdrec
							where prdrec.prd_tnt_id='.$config['tnt_id'].' and prdrec.prd_id=clyrec.cly_prd_id
							and prdrec.prd_date_deleted is null and
				';
				if( $rule['physical_name']!='' )
					$sqlgrouprow_cat .= ' '.$lws.'prdrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld;
				else{
					$sqlgrouprow_cat .= ' ( prdrec.prd_id in ('.$cndvsql.')';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prdrec.prd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_cat .= ' )';
				}
				$sqlgrouprow_cat .= '
						)
						union all
						select 1 from prd_classify as clyrec
						join prd_cat_hierarchy as hryrec on clyrec.cly_tnt_id=hryrec.cat_tnt_id and clyrec.cly_cat_id=hryrec.cat_child_id
						where clyrec.cly_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
						and exists (
							select 1 from prd_products as prdrec
							where prdrec.prd_tnt_id='.$config['tnt_id'].' and prdrec.prd_id=clyrec.cly_prd_id
							and prdrec.prd_date_deleted is null and
				';
				if( $rule['physical_name']!='' )
					$sqlgrouprow_cat .= ' '.$lws.'prdrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld;
				else{
					$sqlgrouprow_cat .= ' ( prdrec.prd_id in ('.$cndvsql.')';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prdrec.prd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_cat .= ' )';
				}
				$sqlgrouprow_cat .= '
						)
					)
				';
			}
			// requête marque
			{
				$sqlgrouprow_brd = '
					exists (
						select 1 from prd_products as prec
						where prec.prd_date_deleted is null and prec.prd_tnt_id='.$config['tnt_id'].' and prec.prd_brd_id=brd_id
				';
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_brd .= '
						and '.$lws.'prec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
					';
				}else{
					$sqlgrouprow_brd .= '
						and (
							prec.prd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or prec.prd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_brd .= '
						)
					';
				}
				$sqlgrouprow_brd .= '
					)
				';
			}
		}elseif( $rule['cls_id']==CLS_CATEGORY ){ // la condition est sur une catégorie
			// requête produit
			{
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_prd = '
						exists (
							select 1 from prd_classify as clycnd
							join prd_categories as catcnd on clycnd.cly_tnt_id=catcnd.cat_tnt_id and clycnd.cly_cat_id=catcnd.cat_id
							where catcnd.cat_date_deleted is null and clycnd.cly_tnt_id='.$config['tnt_id'].'
							and '.$lws.'catcnd.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							and clycnd.cly_prd_id=prd.prd_id
						) or exists (
							select 1 from prd_classify as clycnd
							join prd_cat_hierarchy as hrycnd on clycnd.cly_tnt_id=hrycnd.cat_tnt_id and clycnd.cly_cat_id=hrycnd.cat_child_id
							join prd_categories as catcnd on hrycnd.cat_tnt_id=catcnd.cat_tnt_id and hrycnd.cat_parent_id=catcnd.cat_id
							where catcnd.cat_date_deleted is null and clycnd.cly_tnt_id='.$config['tnt_id'].'
							and '.$lws.'catcnd.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							and clycnd.cly_prd_id=prd.prd_id
						)
					';
				}else{
					$sqlgrouprow_prd = '
						exists (
							select 1 from prd_classify as clycnd
							where clycnd.cly_tnt_id='.$config['tnt_id'].' and (
					';
					$sqlgrouprow_prd .= '
								clycnd.cly_cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' ){
						$sqlgrouprow_prd .= '
								or clycnd.cly_cat_id not in ('.$bool_null_sql.')
						';
					}
					$sqlgrouprow_prd .= '
							) and clycnd.cly_prd_id = prd.prd_id
						) or exists (
							select 1 from prd_classify as clycnd
							join prd_cat_hierarchy as hrycnd on clycnd.cly_tnt_id=hrycnd.cat_tnt_id and clycnd.cly_cat_id=hrycnd.cat_child_id
							where clycnd.cly_tnt_id='.$config['tnt_id'].' and (
					';
					$sqlgrouprow_prd .= '
								hrycnd.cat_parent_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' ){
						$sqlgrouprow_prd .= '
								or hrycnd.cat_parent_id not in ('.$bool_null_sql.')
						';
					}
					$sqlgrouprow_prd .= '
							) and clycnd.cly_prd_id = prd.prd_id
						)
					';
				}
			}
			// requête catégorie
			{
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_cat = ' (
							'.$lws.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.' or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_parent_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_child_id=c.cat_id
								and catrec.cat_date_deleted is null and '.$lws.'catrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_child_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
								and catrec.cat_date_deleted is null and '.$lws.'catrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							)
						)
					';
				}else{
					$sqlgrouprow_cat = ' (
							(
								cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_parent_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_child_id=c.cat_id
								and catrec.cat_date_deleted is null and (
									catrec.cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or catrec.cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								)
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_child_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
								and catrec.cat_date_deleted is null and (
									catrec.cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or catrec.cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								)
							)
						)
					';
				}
			}
			// requête marque
			{
				$sqlgrouprow_brd = '
					exists (
						select 1 from prd_products as prec
						where prec.prd_date_deleted is null and prec.prd_tnt_id='.$config['tnt_id'].'
						and exists (
							select 1 from prd_classify as clyrec
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_prd_id=prec.prd_id
				';
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_brd .= '
							and exists (
								select 1 from prd_categories as catrec
								where catrec.cat_date_deleted is null and catrec.cat_tnt_id='.$config['tnt_id'].' and catrec.cat_id=clyrec.cly_cat_id
								and '.$lws.'catrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							)
					';
				}else{
					$sqlgrouprow_brd .= '
							and (
								clyrec.cly_cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or clyrec.cly_cat_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_brd .= '
							)
					';
				}
				$sqlgrouprow_brd .= '
						) or exists (
							select 1 from prd_classify as clyrec
							join prd_cat_hierarchy as hryrec on clyrec.cly_tnt_id=hryrec.cat_tnt_id and clyrec.cly_cat_id=hryrec.cat_child_id
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_prd_id=prec.prd_id
				';
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_brd .= '
							and exists (
								select 1 from prd_categories as catrec
								where catrec.cat_date_deleted is null and catrec.cat_tnt_id='.$config['tnt_id'].' and catrec.cat_id=hryrec.cat_parent_id
								and '.$lws.'catrec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
							)
					';
				}else{
					$sqlgrouprow_brd .= '
							and (
								hryrec.cat_parent_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or hryrec.cat_parent_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_brd .= '
							)
					';
				}
				$sqlgrouprow_brd .= '
						) and prec.prd_brd_id=brd_id
					)
				';
			}
		}elseif( $rule['cls_id']==CLS_BRAND ){ // la condition est sur une marque
			// requête produit
			{
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_prd = '(
							exists (
								select 1 from prd_brands as brdcnd
								where brdcnd.brd_tnt_id='.$config['tnt_id'].' and brdcnd.brd_date_deleted is null
								and '.$lws.'brdcnd.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
								and brdcnd.brd_id=prd.prd_brd_id
							) or (
								prd.prd_brd_id is null and '.$clause_null.'
							)
					)';
				}else{
					$sqlgrouprow_prd = '
						(
							prd.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_prd .= ' or prd.prd_brd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_prd .= '
							or ( prd.prd_brd_id is null and '.$clause_null.' )
						)
					';
				}
			}
			// requête catégorie
			{
				$sqlgrouprow_cat = '
					exists (
						select 1 from prd_classify as reccly
						where reccly.cly_tnt_id='.$config['tnt_id'].' and reccly.cly_cat_id=cat_id
						and exists (
							select 1 from prd_products as prec
							where prec.prd_tnt_id='.$config['tnt_id'].' and reccly.cly_prd_id=prec.prd_id
							and prec.prd_date_deleted is null
				';
				if( $rule['physical_name']!='' ){
					$sqlgrouprow_cat .= '
							and (
								exists (
									select 1 from prd_brands as brec
									where brec.brd_tnt_id='.$config['tnt_id'].' and brec.brd_id=prec.prd_brd_id and brec.brd_date_deleted is null
									and '.$lws.'brec.'.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld.'
								) or (
									prec.prd_brd_id is null and '.$clause_null.'
								)
							)
					';
				}else{
					$sqlgrouprow_cat .= '
							and (
								prec.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prec.prd_brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								or ( prec.prd_brd_id is null and '.$clause_null.' )
							)
					';
				}
				$sqlgrouprow_cat .= '
						)
						) or exists (
						select 1 from prd_cat_hierarchy as rechry
						join prd_classify as reccly on rechry.cat_tnt_id=reccly.cly_tnt_id and rechry.cat_child_id=reccly.cly_cat_id
						where rechry.cat_tnt_id='.$config['tnt_id'].' and rechry.cat_parent_id=cat_id
						and exists (
							select 1 from prd_products as prec
							where prec.prd_tnt_id='.$config['tnt_id'].'
							and prec.prd_date_deleted is null and prec.prd_id=reccly.cly_prd_id
				';
				if( $cnd['physical_name']!='' ){
					$sqlgrouprow_cat .= '
							and (
								exists (
									select 1 from prd_brands as brec
									where brec.brd_tnt_id='.$config['tnt_id'].' and brec.brd_id=prec.prd_brd_id and brec.brd_date_deleted is null
									and '.$lws.'brec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								) or (
									prec.prd_brd_id is null and '.$clause_null.'
								)
							)
					';
				}else{
					$sqlgrouprow_cat .= '
							and (
								prec.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prec.prd_brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								or ( prec.prd_brd_id is null and '.$clause_null.' )
							)
					';
				}
				$sqlgrouprow_cat .= '
						)
					)
				';
			}
			// requête marque
			{
				if( $rule['physical_name']!='' )
					$sqlgrouprow_brd = $lws.$rule['physical_name'].$lwe.' '.$rule['symbol'].' '.$right_type_fld;
				else{
					$sqlgrouprow_brd = '(
						brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_brd .= '
						)
					';
				}
			}
		}

		// produits
		{
			$sql_products = '
				select prd.prd_id as id from prd_products as prd
				where prd.prd_tnt_id='.$config['tnt_id'].' and prd.prd_date_deleted is null
			';
			if( sizeof($sqlgrouprow_prd) ){
				$sql_products .= ' and ('.$sqlgrouprow_prd.')';
			}else{
				$sql_products .= ' and 0';
			}

			$sql_products .= ' and prd.prd_id = '.$prd_id;

			$rproducts = ria_mysql_query( $sql_products );
			if( $rproducts && ria_mysql_num_rows($rproducts) > 0 ){
				$product_find = true;
				break;
			}
		}

		// marques
		{
			$sql_brands = '
				select brd_id as id
				from prd_brands
				join prd_products on brd_tnt_id=prd_tnt_id and brd_id=prd_brd_id
				where brd_tnt_id='.$config['tnt_id'].' and brd_date_deleted is null and prd_date_deleted is null
			';
			if( sizeof($sqlgrouprow_brd) ){
				$sql_brands .= ' and ('.$sqlgrouprow_brd.')';
			}else{
				$sql_brands .= ' and 0';
			}

			$sql_brands .= ' and prd_id = '.$prd_id;

			$rbrands = ria_mysql_query( $sql_brands );
			if( $rbrands && ria_mysql_num_rows($rbrands) > 0 ){
				$product_find = true;
				break;
			}
		}

		// catégories
		{
			$sql_categories = '
				select c.cat_id as id
				from prd_categories as c
				join prd_classify on cat_tnt_id=cly_tnt_id and cat_id=cly_cat_id
				where
				c.cat_tnt_id='.$config['tnt_id'].' and c.cat_date_deleted is null and cly_date_deleted is null
			';
			if( sizeof($sqlgrouprow_cat) ){
				$sql_categories .= ' and ('.$sqlgrouprow_cat.')';
			}else{
				$sql_categories .= ' and 0';
			}

			$sql_categories .= ' and cly_prd_id='.$prd_id;

			$rcategories = ria_mysql_query( $sql_categories );
			if( $rcategories && ria_mysql_num_rows($rcategories) > 0 ){
				$product_find = true;
				break;
			}
		}
	}

	if( !$product_find ){
		return false;
	}

	// récupère le tarifs sur lequel les règles s'applique
	$tarif = 0;

	// récupère le champ
	$rfld = fld_fields_get($price_fld);
	if( $rfld && ria_mysql_num_rows($rfld) ){
		$price_field = ria_mysql_fetch_assoc($rfld);

		switch ($price_field['fld_id']){
			case _FLD_PRD_PURCHASE:
				$rsql = ria_mysql_query('select prd_purchase_avg as purchase_avg from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd_id);
				if( $rsql && ria_mysql_num_rows($rsql) ){
					$tarif = ria_mysql_result($rsql, 0, 'purchase_avg');
				}
				break;
		}
	}else{
		switch ($price_fld){
			case PRD_PRICE_PUBLIC_HT:
				$rprc = prd_products_get_price( $prd_id, 0, 0, 0, $qte, 0, false, false );
				if( $rprc && ria_mysql_num_rows($rprc) ){
					$tarif = ria_mysql_result($rprc, 0, 'price_ht');
				}
				break;
			case PRD_PRICE_USR_HT:
				$rprc = prd_products_get_price( $prd_id, $dst_usr_id, 0, 0, $qte, 0, false, false );
				if( $rprc && ria_mysql_num_rows($rprc) ){
					$tarif = ria_mysql_result($rprc, 0, 'price_ht');
				}
				break;
		}
	}

	// si le tarif est 0 alors le prix mini ne peux être plus bas.
	if( !$tarif ){
		return 0;
	}

	$tarif = ($tarif - $price_discount) / $price_rate;

	if( $tarif < 0 ){
		$tarif = 0;
	}

	return $tarif;
}

/// @}
// \endcond

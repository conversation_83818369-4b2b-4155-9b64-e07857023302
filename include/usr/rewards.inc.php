<?php

// \cond onlydev
/** \ingroup model_rewards
 *	@{
 */
// \endcond

/** Cette fonction permet de retourner le solde des points de fidélité.
 *	@param int $usr Obligatoire, identifiant d'un compte client (mettre 'all' pour tous les comptes clients)
 *	@param $data_return Optionnel, par défaut le nombre de point est retourné, mettre true pour retourner leur valeur en euro, 'all' pour les deux
 *	@param $rwa Optionnel, permet d'inclure/exclure une action du solde de points
 *	@param $action Optionnel, permet d'inclure/exclure une action du solde de points (selon le nom donné)
 *	@param $is_exclude Optionnel, si l'action est inclue ou exclue du solde
 *	@return int Soit le nombre exacte de points, soit zéro points de fidélité
 */
function gu_users_get_rewards_balance( $usr, $data_return=false, $rwa=null, $action='', $is_exclude=false ){
	if ($usr !== 'all') {
		if (!is_numeric($usr) || $usr <= 0) {
			return 0;
		}
	}

	if ((is_numeric($rwa) && $rwa > 0) || is_array($rwa)) {
		if (!is_array($rwa)) {
			if (!is_numeric($rwa) || $rwa <= 0) return false;
			$rwa = array($rwa);
		} else {
			if (!sizeof($rwa)) return false;
			foreach ($rwa as $a) {
				if (!is_numeric($a) || $a <= 0)
					return false;
			}
		}
	}

	if (is_array($action)) {
		foreach ($action as $k => $a) {
			if (trim($a) == '') {
				return false;
			}

			$action[$k] = strtolower2($a);
		}
	} else {
		if (trim($action) == '') {
			$action = array();
		} else {
			$action = array(strtolower2($action));
		}
	}

	global $config;

	$sql = '
		select stats_points as pts, stats_convert as \'convert\', stats_date_limited as date_limit_en, stats_datetime as date_en
		from stats_rewards
		join gu_users on ((stats_tnt_id=usr_tnt_id or 0 = usr_tnt_id) and stats_usr_id=usr_id and usr_date_deleted is null)
		where stats_tnt_id=' . $config['tnt_id'] . '
		and stats_date_deleted is null
	';

	if ($usr > 0){
		$sql .= ' and stats_usr_id=' . $usr;
	}

	if (is_array($rwa) && sizeof($rwa)){
		$sql .= ' and stats_rwa_id ' . ($is_exclude ? 'not in' : 'in') . ' (' . implode(',', $rwa) . ')';
	}

	if (sizeof($action)) {
		$sql .= ' and (' . ($is_exclude ? 'ifnull(stats_action, "")="" or' : '') . ' lower(stats_action)' . ($is_exclude ? 'not in' : 'in') . ' ("' . implode('", "', $action) . '"))';
	}

	$sql .= ' order by date_en asc';

	$rewards = ria_mysql_query($sql);

	// Spécifique Pierre Oteiza
	if ($config['tnt_id'] == 13) {
		$ar_temp = array();
		$solde = $amount = 0;

		$del_pts = 0;
		while ($r = ria_mysql_fetch_assoc($rewards)) {
			if ($r['pts'] > 0) {
				$ar_temp[] = $r;
			} else {
				$del_pts += abs($r['pts']);
				foreach ($ar_temp as $key => $value) {
					if (isdateheure($value['date_limit_en']) && strtotime($value['date_limit_en']) < strtotime($r['date_en'])) {
						unset($ar_temp[$key]);
						continue;
					}

					if ($value['pts'] > $del_pts) {
						$ar_temp[$key]['pts'] = $value['pts'] - $del_pts;
						$del_pts = 0;
						break;
					} else {
						$del_pts = $del_pts - $value['pts'];
						unset($ar_temp[$key]);
					}
				}
			}
		}

		$data = array('solde' => (0-$del_pts), 'amount' => 0);
		foreach ($ar_temp as $key => $value) {
			if (trim($value['date_limit_en']) == '' || strtotime($value['date_limit_en']) >= time()) {
				$data['solde'] += $value['pts'];
				$data['amount'] += $value['pts'] * (trim($value['convert']) ? $value['convert'] : 0);
			}
		}

		if ($data_return === 'all') {
			return $data;
		}

		return $data_return ? round($data['amount'], 2) : $data['solde'];
	}

	$ar_temp = array();
	$solde = $amount = 0;

	while ($r = ria_mysql_fetch_assoc($rewards)) {
		if ($r['pts'] > 0) {
			$ar_temp[] = $r;
		} else {
			$sum_points = $sum_amount = 0;
			foreach ($ar_temp as $t) {
				if (trim($t['date_limit_en']) == '' || (strtotime($t['date_limit_en']) > strtotime($r['date_en']) && strtotime($t['date_limit_en']) >= time())) {
					$sum_points += $t['pts'];
					$sum_amount += $t['pts'] * $t['convert'];
				}
			}

			$solde += $sum_points + $r['pts'];
			if (isset($config['reward_reinit_balance']) && $config['reward_reinit_balance'] && $solde < 0) {
				$solde = 0;
			}

			$amount += $solde > 0 ? $sum_amount + ($r['pts'] * $t['convert']) : 0;

			$ar_temp = array();
		}
	}

	if (sizeof($ar_temp)) {
		$sum_points = $sum_amount = 0;
		foreach ($ar_temp as $t) {
			if (!isset($r['date_en']) || trim($r['date_en']) == '') {
				$r['date_en'] = date('Y-m-d H:i:s');
				$r['pts'] = 0;
			}

			if (trim($t['date_limit_en']) == '' || (strtotime($t['date_limit_en']) > strtotime($r['date_en']) && strtotime($t['date_limit_en']) >= time())) {
				$sum_points += $t['pts'];
				$sum_amount += $t['pts'] * $t['convert'];
			}
		}

		$solde += $sum_points + $r['pts'];
		if (isset($config['reward_reinit_balance']) && $config['reward_reinit_balance'] && $solde < 0) {
			$solde = 0;
		}

		$amount += $solde > 0 ? $sum_amount + ($r['pts'] * $t['convert']) : 0;
	}

	if( $data_return === 'all' ){
		$data['amount'] = round($amount, 2);
		$data['solde'] = $solde;
		return $data;
	}else{
		return $data_return ? round($amount, 2) : $solde;
	}
}

// \cond onlydev
/// @}
// \endcond


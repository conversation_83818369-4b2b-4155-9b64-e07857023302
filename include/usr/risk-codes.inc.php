<?php

// \cond onlyria
/** \defgroup model_users_risk Gestion des Risques
 *	\ingroup model_users
 *	Ce module comprend les fonctions nécessaires à la gestion des risques de défaut de règlement
 *	@{
 */

/** Cette fonction permet de tester l'existance d'un code risque
 * 	@param int $id Obligatoire : identifiant du code risque
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function gu_risk_code_exists( $id ){
	if( !is_numeric($id) || $id < 0 ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('
		select 1 from gu_risk_codes
		where rco_tnt_id='.$config['tnt_id'].' and rco_date_deleted is null and rco_id='.$id
	) );
}

/** Cette fonction permet l'ajout d'un code risque
 * @param string $name : nom du code risque
 * @param bool $is_sync : Boolean qui détermine si l'ajout provient de la sync ou non
 * @return int L'identifiant du code risque ou false en cas d'échec
 */
function gu_risk_code_add( $name, $is_sync=false ){
	global $config;

	if( trim($name)==''  ){
		return false;
	}

	$res = ria_mysql_query('
		insert into gu_risk_codes (rco_tnt_id, rco_name, rco_is_sync, rco_date_created)
		values ('.$config['tnt_id'].', \''.addslashes($name).'\', '.($is_sync ? 1:0).', now() )
	');
	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet la modification d'un code risque
 * @param int $id Obligatoire, identifiant du code risque
 * @param string $name Obligatoire, nom du code risque
 * @param bool $is_sync Facultatif, Boolean qui détermine si l'ajout provient de la sync ou non
 * @return bool True en cas de succès ou False si la ligne n'a pas été mise à jour
 */
function gu_risk_code_upd( $id, $name, $is_sync=false ){
	global $config;

	if( !gu_risk_code_exists($id) ){
		return false;
	}
	if( trim($name)==''  ){
		return false;
	}

	return ria_mysql_query('
		update gu_risk_codes set rco_name=\''.addslashes($name).'\'
		where rco_tnt_id='.$config['tnt_id'].' and rco_is_sync='.($is_sync ? 1:0).'
			and rco_date_deleted is null and rco_id='.$id
	);
}

/** Cette fonction permet la suppression d'un code risque
 * @param int $id : identifiant du code risque
 * @param bool $is_sync : Boolean qui détermine si l'ajout provient de la sync ou non
 * @return bool True en cas de succès ou False si la ligne n'a pas été mise à jour
 */
function gu_risk_code_del( $id, $is_sync=false ){
	global $config;

	if( !gu_risk_code_exists($id) ){
		return false;
	}

	return ria_mysql_query('
		update gu_risk_codes set rco_date_deleted=now()
		where rco_tnt_id='.$config['tnt_id'].' and rco_is_sync='.($is_sync ? 1:0).'
			and rco_date_deleted is null and rco_id='.$id
	);
}

/** Cette fonction permet de récupérer des codes risque
 * @param int $id : identifiant du code risque
 * @return resource un résultat de requete sql comprenant les colonnes suivantes :
 *		- id : Identifiant du code risque
 *		- name : Nom du code risque
 *		- is_sync : si 1 alors le code risque vient de la gescom
 */
function gu_risk_code_get( $id=false ){
	global $config;

	if( is_numeric($id) && !gu_risk_code_exists($id) ){
		return false;
	}

	$sql  = '
		select rco_id as id, rco_name as name, rco_is_sync as is_sync
		from gu_risk_codes
		where rco_tnt_id='.$config['tnt_id'].'
			and rco_date_deleted is null
	';

	if( is_numeric($id) ){
		$sql .= 'and rco_id='.$id.' ';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction assigne le code risque d'un client.
 *	@param int $usr_id Identifiant de l'utilisateur.
 *	@param int|null $rco_id Identifiant du code risque (Null autorisé).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_users_set_risk_code( $usr_id, $rco_id ){
	global $config;

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( is_numeric($rco_id) && !gu_risk_code_exists( $rco_id ) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_rco_id = '.( is_numeric($rco_id) ? $rco_id : 'NULL' ).'
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
	');
}

/// @}
// \endcond

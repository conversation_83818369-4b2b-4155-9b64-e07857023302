<?php

require_once('db.inc.php');
require_once('images.inc.php');
require_once('fields.inc.php');
require_once('prd.stocks.inc.php');
require_once('ria.queue.inc.php');

/** \defgroup model_brands Marques
 *	\ingroup pim
 *	Ce module comprend les fonctions nécessaires à la gestion des marques.
 *	@todo Le décompte de produis publiés par marque est correct, mais il manque un décompte des produits enregistrés dans la base de données.
 *	@todo Les images associées aux marques sont stockés avec celles des articles. Cela ne devrait pas être le cas.
 *
 *	@{
 */

// \cond onlyria
/**	Indexe une marque par le moteur de recherche (les produits de la catégorie ne sont pas concernés).
 *	@param int $id Identifiant de la marque
 *	@param string $lng Facultatif, code d'une langue spécifique
 *
 *	@return bool true en cas de succès, False en cas d'échec
 */
function prd_brands_index_add( $id, $lng=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	if( $brd = ria_mysql_fetch_array(prd_brands_get( $id )) ){

		// si la langue n'est pas celle par défaut, on récupère la traduction
		if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
			$tsk_brd = fld_translates_get( CLS_BRAND, $brd['id'], $lng, $brd, array(_FLD_BRD_NAME=>'name', _FLD_BRD_TITLE=>'title', _FLD_BRD_DESC=>'desc', _FLD_BRD_URL=>'url_alias') );
			$brd['url_alias'] = $tsk_brd['url_alias']; $brd['name'] = $tsk_brd['name']; $brd['title'] = $tsk_brd['title']; $brd['desc'] = $tsk_brd['desc'];
		}

		$publish = $brd['publish'] && $brd['products'];

		$cid = search_index_content( $brd['url_alias'], 'brd', $brd['title'], $brd['desc'], $brd['title'].' '.$brd['name'].' '.$brd['desc'], '/admin/catalog/brands/edit.php?brd='.$brd['id'], $publish, $brd['id'], false, '', '', $lng );

		if( strtolower($lng)==strtolower($config['i18n_lng']) && !ria_mysql_query('update prd_brands set brd_cnt_id='.$cid.' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id) )
			return false;

	}else{
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction va reconstruire l'index du moteur de recherche pour toutes les marques.
 *	Les produits contenus dans ces marques ne sont pas concernés par l'opération.
 *	@param string $lng Optionnel, si le paramètre est précisé, alors les marques seront réindéxées seulement dans cette langue
 *	@return bool Retourne true à la fin de l'exécution
 */
function prd_brands_index_rebuild( $lng=false ){
	global $config;

	// Reconstruit l'index
	$rbrd = ria_mysql_query('
		select brd_id as id, brd_cnt_id as cnt_id
		from prd_brands
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_date_deleted is null
	');

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	foreach( $lng as $l ){
		while( $brd = ria_mysql_fetch_array($rbrd) ){
			prd_brands_index_add( $brd['id'], strtolower($l) );
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une marque à la gestion des produits.
 *	Le nom de la marque est automatiquement mis en forme par cette fonction :
 *	suppression des espaces de début et de fin de chaîne, et mise en majuscules
 *	de la première lettre.
 *
 *	Si la marque que l'on tente d'insérer existe déjà dans la base de données,
 *	son identifiant est retourné.
 *
 *	Les titres de marques sont utilisés pour renommer des marques synchronisées avec Sage.
 *	Le champ complémentaire Marque était très limité en taille, le titre permet de pallier
 *	à des noms de marques tronqués.
 *
 *	@param string $name Nom de la marque.
 *	@param string $title Facultatif, titre de la marque (valeur permettant la surchage du nom issu de la gescom)
 *	@param string $url Facultatif, adresse du site web de la marque
 *	@param bool $is_sync Facultatif, détermine si la création de la marque provient du logiciel de synchronisation
 *	@param string $desc Facultatif, description de la marque
 *	@param bool $publish Facultatif, publication de la marque
 *	@param string $ref Facultatif, référence de la marque
 *
 *	@return int 0 en cas d'erreur, l'identifiant de la marque en cas de succès
 *
 */
function prd_brands_add( $name, $title='', $url='', $is_sync=false, $desc='', $publish=true, $ref='' ){
	global $config;

	$name = ucfirst(trim($name));
	$title = ucfirst(trim($title));
	$desc = ucfirst(trim($desc));
	$url = trim($url);

	if( $name==$title ) $title = '';

	$r = ria_mysql_query('
		select brd_id from prd_brands
		where brd_tnt_id = '.$config['tnt_id'].'
			and brd_name = "'.addslashes($name).'"
			and brd_date_deleted is null
	');
	if( $r = ria_mysql_fetch_assoc($r) ){
		prd_brands_set_is_sync( $r['brd_id'], $is_sync );
		return $is_sync ? false : $r['brd_id'];
	}else{
		if( ria_mysql_query("
				insert into prd_brands
					(brd_tnt_id,brd_name,brd_title,brd_url,brd_is_sync,brd_desc,brd_publish,brd_ref,brd_date_created)
				values
					(".$config['tnt_id'].",'".addslashes($name)."','".addslashes($title)."','".addslashes($url)."',".( $is_sync ? 1 : 0 )
					.",'".addslashes($desc)."', ".( $publish ? 1 : 0 ).",'".addslashes($ref)."',now())")
		){
			$id = ria_mysql_insert_id();

			// Récupère les sites
			$rwst = wst_websites_get();
			if( !$rwst || !ria_mysql_num_rows($rwst) )
				return false;

			// Crée les alias
			$alias = rew_rewritemap_generated( array($id), CLS_BRAND );
			$url = '';
			while( $wst = ria_mysql_fetch_array($rwst) ){
				$prd_pages = cfg_urls_get( $wst['id'], CLS_BRAND);
				if( $prd_pages ){
					while( $page = ria_mysql_fetch_array($prd_pages) ){
						if( trim($url)=='' )
							$url = rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$id, 200, $wst['id'], false, null, $id );
						else
							rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$id, 200, $wst['id'], false, null, $id );
					}
				}
			}
			ria_mysql_query('update prd_brands set brd_url_alias=\''.$url.'\' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id);

			try{
				// Index la marque dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
					'cls_id' => CLS_BRAND,
					'obj_id_0' => $id,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}

			return $id;
		}else{
			return 0;
		}
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la propriété \c is_sync d'une marque.
 *	@param int $brd Identifiant de la marque à mettre à jour
 *	@param bool $is_sync Booléen indiquant si la marque est synchronisée avec la gestion commerciale, ou non.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_brands_set_is_sync( $brd, $is_sync ){
	global $config;

	if( !prd_brands_exists($brd) ) return false;
	return ria_mysql_query('update prd_brands set brd_is_sync='.( $is_sync ? 1 : 0 ).' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd);
}
// \endcond

// \cond onlyria
/** Permet l'ajout d'un fichier image à une marque. Cette fonction est similaire à \c prd_brands_image_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importations.
 *
 *	@param int $brd Identifiant de la marque.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, Nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function prd_brands_image_add( $brd, $filename, $srcname='' ){
	global $config;

	if( $id = img_images_add( $filename, $srcname ) )
		ria_mysql_query('update prd_brands set brd_img_id='.$id.' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd);
	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la réutilisation d'une image existante pour une marque.
 *	@param int $brd Obligatoire, Identifiant de la marque.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_brands_image_add_existing( $brd, $img ){
	if( !prd_brands_exists($brd) ) return false;
	if( !img_images_exists($img) ) return false;

	global $config;

	$oldImg = false;
	$rb = prd_brands_get( $brd );
	if( $rb && ria_mysql_num_rows($rb) ){
		// A regarder quand possible mais ce code n'a jamais fonctionné
		// $oldImg = ria_mysql_result( 0, $rb, 'img_id' );
	}

	if( !ria_mysql_query('update prd_brands set brd_img_id='.$img.' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd) )
		return false;

	if( $oldImg>0 )
		img_images_count_update($oldImg);

	return img_images_count_update($img);
}
// \endcond

// \cond onlyria
/** Permet l'association d'une image à une marque.
 *
 *	@param int $brd Identifiant de la marque.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function prd_brands_image_upload( $brd, $fieldname ){

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return prd_brands_image_add( $brd, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}
// \endcond

// \cond onlyria
/** Permet la suppression de l'image associée à une marque.
 *  @param int $brd Identifiant de la marque dont on souhaite supprimer l'image
 *  @return bool true en cas de succès
 *  @return bool false en cas d'erreur
 */
function prd_brands_image_del( $brd ){
	if( !prd_brands_exists($brd) ) return false;
	global $config;

	$brand = ria_mysql_fetch_array(prd_brands_get($brd));

	if( !ria_mysql_query('update prd_brands set brd_img_id=null where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd) )
		return false;

	if( $brand['img_id'] ){
		img_images_count_update( $brand['img_id'] );
	}

	return true;
}
// \endcond

/**	Retourne l'ensemble des marques présentes dans la base de données.
 *	Les marques sont retournées triées par nom.
 *
 *	@param int $id Optionnel, identifiant d'une marque (ou tableau d'identifiants) sur laquelle filtrer le résultat.
 *	@param bool $published  Optionnel, détermine si toutes les marques sont retournées, ou seulement celles qui ont des produits publiés (ignoré si id est fourni).
 *	@param string $name Optionnel, permet de filtrer le résultat sur le nom d'une marque
 *	@param string|array $by_name Optionnel, permet de filtrer le résultat sur le nom du marque, par défaut on recherche par le début, passer un tableau pour paramétrer ce paramètre array('name'=>'titre', 'like' => 'start' | 'end' | 'contains')
 *	@param bool $isSync Optionnel, si True, filtre uniquement les marques synchronisées
 *	@param bool $publish Optionnel, par défaut toutes les marques sont récupérées, mettre true pour ne récupérer que celles qui sont publiées et false pour les autres
 *	@param int|array $fld Optionnel, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Optionnel, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Optionnel, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param string $lng Optionnel, permet de spécifier une autre langue que celle de la configuration actuelle. Utilse s'il existe des valeurs de champs avancés multilingues
 *	@param int|array $prd Optionnel, identifiant ou tableau d'identifiants de produits
 *	@param int|array $cat Optionnel, identifiant ou tableau d'identifiants d'une categorie
 *	@param $cat_recursive Optionnel, si oui ou non on récupère les produits des sous catégories
 *	@param $prd_publish Optionnel, détermine si les produits de la marque doivent être publiés et actifs (ou en déstockage).
 *	@param int $start Facultatif, ligne de départ du résultat (pour la pagination). Valeur par défaut : 0.
 *	@param int $limit Facultatif, nombre maximum de lignes à retourner (pour la pagination). Valeur par défaut : 0, pas de limite.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la marque
 *		- ref : reference de la marque
 *		- name : nom de la marque
 *		- title : titre de la marque
 *		- publish : publication de la marque
 *		- url : url du site Internet de la marque
 *		- products : nombre de produits publiés
 *		- img_id : identifiant de l'image représentant le logo de la société
 *		- is_sync : booléen indiquant si la marque est synchronisée avec la gestion commerciale
 *		- desc : description de la marque
 *		- url_alias : url virtuelle de la marque
 *		- tag_title : titre utilisé dans le référencement
 *		- tag_desc : description utilisée dans le référencement
 *		- keywords : mots clés utilisés dans le référencement
 *		- pos : position de la marque
 */
function prd_brands_get(
	$id=0, $published=false, $name='', $by_name='', $isSync=false, $publish=null, $fld=false, $or_between_val=false, $or_between_fld=false,
	$lng=false, $prd=false, $cat=false, $cat_recursive=false, $prd_publish=false, $start=0, $limit=0
){
	global $config;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : i18n::getLang();

	$id_ar = array();
	if( !is_array($id) ){
		if( !is_numeric($id) || $id<0 ) return false;
		if( $id>0 ) $id_ar[] = $id;
	}else{
		foreach( $id as $i ){
			if( !is_numeric($i) || $i<=0 ) return false;
			$id_ar[] = $i;
		}
	}

	if( $prd!==false ){
		if( !is_array($prd) ){
			if( !is_numeric($prd) || $prd<=0 ){
				return false;
			}else{
				$prd = array( $prd );
			}
		}else{
			foreach( $prd as $p ){
				if( !is_numeric($p) || $p<=0 ){
					return false;
				}
			}
		}
	}else{
		if( $cat!==false ){
			if( !is_array($cat) ){
				if( !is_numeric($cat) || $cat<=0 ){
					return false;
				}else{
					$cat = array( $cat );
				}
			}else{
				foreach( $cat as $c ){
					if( !is_numeric($c) || $c<=0 ){
						return false;
					}
				}
			}

			$ar_cats = $cat;

			if( $cat_recursive ){
				foreach( $cat as $one_cat ){
					$catchilds = prd_categories_childs_get_array( $one_cat, $published );
					if( is_array($catchilds) && sizeof($catchilds) ){
						$ar_cats = array_merge( $ar_cats, $catchilds );
					}
				}
			}

			$prd = array( 0 );

			$rproduct = ria_mysql_query('
				select cly_prd_id as prd_id from prd_classify
				where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id in ('.implode(', ', $ar_cats).')
			');
			if( $rproduct && ria_mysql_num_rows($rproduct) ){
				while( $product = ria_mysql_fetch_array($rproduct) ){
					$prd[] = $product['prd_id'];
				}
			}
		}
	}

	if (!is_numeric($start) || $start < 0){
		return false;
	}

	if (!is_numeric($limit) || $limit < 0){
		return false;
	}

	$search_brand = array();
	if( !is_array($by_name) ){
		if( trim($by_name) != '' ){
			$search_brand = array( 'name' => $by_name, 'like' => 'start', 'ci' => true );
		}
	}elseif( sizeof($by_name) ){
		if( !ria_array_key_exists(array('name', 'like', 'ci'), $by_name) ){
			return false;
		}

		$search_brand = $by_name;
	}

	$sql = '
		select brd_id as id, brd_name as name, brd_ref as ref, if(ifnull(brd_title, "")!="",brd_title,brd_name) as title, brd_publish as publish,
			brd_url as url, brd_products as products, brd_img_id as img_id, brd_is_sync as is_sync,
			brd_tag_title as tag_title, brd_tag_desc as tag_desc, brd_keywords as keywords,
			brd_url_alias as url_alias, brd_desc as "desc", brd_pos as "pos",

			date_format(brd_date_modified,"%d/%m/%Y à %H:%i") as date_modified

		from prd_brands
	';

	if( is_array($prd) && sizeof($prd) ){
		$sql .= '
			join prd_products on (brd_tnt_id=prd_tnt_id and brd_id=prd_brd_id)
		';
	}

	$sql .= '
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_date_deleted is null
	';

	if( isset($config['use_catalog_restrictions']) && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_BRAND, 'brd_id' ).')';
	}

	if( $publish!==null ){
		$sql .= ' and brd_publish='.( $publish ? '1' : '0' );
	}

	if( $prd_publish ){
		$dps = prd_deposits_get_main();
		$dps = !is_numeric($dps) || $dps < 0 ? 0 : $dps;
		$sql .= '
			and exists (
				select 1 from prd_products
				where prd_tnt_id = '.$config['tnt_id'].' and prd_brd_id = brd_id and prd_date_deleted is null
				and prd_publish = 1 and prd_publish_cat = 1 and (
					prd_sleep = 0 or ifnull((
						select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
						where sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
					), 0) > 0
				)
			)
		';
	}

	if( sizeof($id_ar) ){
		$sql .= ' and brd_id in ('.implode(',', $id_ar).')';
	}

	if( $published ){
		$sql .= ' and brd_products>0';
	}

	if( $name = trim(ucfirst($name)) ){
		$sql .= " and brd_name='".addslashes($name)."'";
	}

	if( sizeof($search_brand) ){
		if( !$search_brand['ci'] ){
			$search_brand['name'] = strtoupper( $search_brand['name'] );
		}

		switch( $search_brand['like'] ){
			case 'contains': {
				$like_sql = ' like "%'.addslashes( $search_brand['name'] ).'%"';
				break;
			}
			case 'end': {
				$like_sql = ' like "%'.addslashes( $search_brand['name'] ).'"';
				break;
			}
			default: {
				$like_sql = ' like "'.addslashes( $search_brand['name'] ).'%"';
				break;
			}
		}

		if( $search_brand['ci'] ){
			$sql .= ' and (
				(brd_title!="" and brd_title '.$like_sql.')
				or (brd_title="" and brd_name '.$like_sql.')
			)';
		}else{
			$sql .= ' and (
				(brd_title!="" and upper(brd_title) '.$like_sql.')
				or (brd_title="" and upper(brd_name) '.$like_sql.')
			)';
		}
	}

	if( $isSync===true ) $sql .= ' and brd_is_sync=1';

	$sql .= fld_classes_sql_get( CLS_BRAND, $fld, $or_between_val, $or_between_fld, $lng );

	if( is_array($prd) && sizeof($prd) ){
		$sql .= '
			and prd_id in ('.implode(', ', $prd).')
			group by brd_id
		';
	}

	if( prd_brands_order_get() ){
		$sql .= ' order by IFNULL(brd_pos, 0) asc, lower(if(ifnull(brd_title, "")!="",brd_title,brd_name))';
	}else{
		$sql .= ' order by lower(if(ifnull(brd_title, "")!="",brd_title,brd_name))';
	}

	if( sizeof($id_ar)==1 ){
		$sql .= ' limit 0,1';
	}elseif( $limit > 0 ){
		$sql .= ' limit '.$start.', '.$limit;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer le nom d'une marque.
 *	@param int $id Obligatoire, identifiant d'une marque
 *	@return bool|string False si la marque n'existe pas, le nom de la marque dans le cas contraire
 */
function prd_brands_get_name( $id ){
	if( !prd_brands_exists($id) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select brd_name as name
		from prd_brands
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction permet de récupérer le nombre de marques publiées et non publiées pour un locataire donné.
 *	@return int Le nombre de marques définies pour l'instance
 */
function prd_brands_get_count(){
	global $config;

	$res = ria_mysql_query('
		select count(brd_id)
		from prd_brands
		where brd_tnt_id='.$config['tnt_id']
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 0 );
}

/** Cette fonction permet de récupérer l'url d'une marque
 *	@param int $id Obligatoire, identifiant d'une marque
 *	@return string Retourne l'url d'une marque
 *	@return bool Retourne false si le paramètre est omis ou bien si la marque n'existe pas
 */
function prd_brands_get_url( $id ){
	if( !prd_brands_exists($id) ) return false;
	global $config;

	$res = ria_mysql_query( 'select brd_url_alias as alias from prd_brands where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'alias' );
}

// \cond onlyria
/** Teste l'existance d'un identifiant de marque dans la base de données.
 *
 *	@param int $id Identifiant à tester.
 *
 *	@return bool true si la marque existe, false dans le cas contraire.
 *
 */
function prd_brands_exists( $id, $publish=null ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select brd_id from prd_brands
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$id.'
			and brd_date_deleted is null
	';
	if( $publish ){
		$sql .= ' and brd_publish';
	}

	if( isset($config['use_catalog_restrictions']) && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_BRAND, 'brd_id' ).')';
	}

	$r = ria_mysql_num_rows(ria_mysql_query($sql))>0;

	return $r;
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour du nom de la marque.
 *
 *	Les modifications apportées sur le nom de la marque sont les mêmes que lors d'un ajout
 *	(suppression des caractères de début et de fin de chaîne, mise en majuscules du premier caractère).
 *
 *	@param int $id Identifiant de la marque à mettre à jour
 *	@param string $name Nouveau nom de la marque.
 *	@param string $title Titre de la marque
 *	@param string $url Adresse du site web de la marque
 *
 *	@return bool Retourne true en cas de succès, false en cas d'échec
 *
 */
function prd_brands_update( $id, $name, $title, $url ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$name = ucfirst(trim($name));
	$title = ucfirst(trim($title));
	$url = trim($url);

	if( $title == $name ){
		$title = '';
	}

	$res = ria_mysql_query("update prd_brands set brd_name='".addslashes($name)."', brd_title='".addslashes($title)."', brd_url='".$url."' where brd_tnt_id=".$config['tnt_id']." and brd_id=".$id);

	if( $res ){
		try{
			// Index la marque dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_BRAND,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		$url = ria_mysql_result(ria_mysql_query('select brd_url_alias from prd_brands where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id),0,0);
		if( !trim($url) ){
			// Récupère les sites
			$rwst = wst_websites_get();
			if( !$rwst || !ria_mysql_num_rows($rwst) )
				return false;

			// Crée les alias
			$alias = rew_rewritemap_generated( array($id), CLS_BRAND );
			while( $wst = ria_mysql_fetch_array($rwst) ){
				$prd_pages = cfg_urls_get( $wst['id'], CLS_BRAND);
				if( $prd_pages ){
					while( $page = ria_mysql_fetch_array($prd_pages) )
						rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$id, 200, $wst['id'], false, null, $id );
				}
			}
			$res = ria_mysql_query('update prd_brands set brd_url_alias=\''.$alias.'\' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id);
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le référencement d'une marque.
 *	@param int $brd Obligatoire, identifiant d'une marque
 *	@param string $title Optionnel, titre utilisé lors du référencement
 *	@param string $desc Optionnel, description utilisée lors du référencement
 *	@param string $keywords Optionnel, mots clé utilisés lors du référencement
 *
 *	@return bool false en cas d'échec de la mise à jour, sinon true dans le cas contraire
 */
function prd_brands_update_referencing( $brd, $title='', $desc='', $keywords='' ){
	if( !is_numeric($brd) || $brd<=0 ) return false;
	global $config;

	$title = trim($title) ? '\''.addslashes( $title ).'\'' : 'null';
	$desc = trim($desc) ? '\''.addslashes( $desc ).'\'' : 'null';
	$keywords = trim($keywords) ? '\''.addslashes( $keywords ).'\'' : 'null';

	$res = ria_mysql_query('
		update prd_brands
		set brd_tag_title='.$title.', brd_tag_desc='.$desc.', brd_keywords='.$keywords.'
		where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd.'
	');

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'une marque
 *
 *	@param int $brd Obligatoire, identifiant d'une marque
 *	@param string $tag_title Optionnel, titre utilisé lors du référencement
 *
 *	@return bool False en cas d'échec de la mise à jour, True dans le cas contraire
 */
function prd_brands_update_referencing_tag_title( $brd, $tag_title='' ){
	if( !is_numeric($brd) || $brd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_brands
		set brd_tag_title='.( trim($tag_title)!='' ? '\''.addslashes($tag_title).'\'' : 'null' ).'
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$brd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'une marque
 *
 *	@param int $brd Obligatoire, identifiant d'une marque
 *	@param string $tag_desc Optionnel, titre utilisé lors du référencement
 *
 *	@return bool False en cas d'échec de la mise à jour, True dans le cas contraire
 */
function prd_brands_update_referencing_tag_desc( $brd, $tag_desc='' ){
	if( !is_numeric($brd) || $brd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_brands
		set brd_tag_desc='.( trim($tag_desc)!='' ? '\''.addslashes($tag_desc).'\'' : 'null' ).'
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$brd.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de déterminer si une marque est publiée ou non
 *  @param int $brd_id Obligatoire, Identifiant de la marque
 *
 *  @return bool True si la marque est publiée, false dans le cas contraire
 */
function prd_brands_is_published( $brd_id ){
	if( !is_numeric($brd_id) || $brd_id <= 0 ){
		return false;
	}
	global $config;
	$sql = '
		select 1 from prd_brands
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$brd_id.'
			and brd_publish=1
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de publier une marque
 *	@param int|array $brd Obligatoire, identifiant ou tableau d'identifiants d'une marque
 *	@return bool false en cas d'échec, true dans le cas contraire
 */
function prd_brands_publish( $brd ){
	if( !is_numeric($brd) && !is_array($brd) ) return false;
	if( is_numeric($brd) && $brd<=0 ) return false;
	if( is_array($brd) && !sizeof($brd) ) return false;
	global $config;

	if( is_numeric($brd) )
		$brd = array( $brd );
	else {
		foreach( $brd as $b ){
			if( !is_numeric($b) )
				return false;
		}
	}

	$res = ria_mysql_query('
		update prd_brands
		set brd_publish=1
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id in ('.implode( ',', $brd ).')
	');

	if( !$res ){
		return false;
	}

	foreach( $brd as $one_brd ){
		try{
			// Ajout à la liste de tâche la ré-indexation de la marque dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_BRAND,
				'obj_id_0' => $one_brd,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de dépublier une marque
 *	@param int|array $brd Obligatoire, identifiant ou tableau d'identifiants d'une marque
 *	@return bool false en cas d'échec, true dans le cas contraire
 */
function prd_brands_unpublish( $brd ){
	$brd = control_array_integer($brd, true);
	if( $brd === false ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_brands
		set brd_publish=0
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id in ('.implode( ',', $brd ).')
	');

	if( !$res ){
		return false;
	}

	foreach( $brd as $one_brd ){
		try{
			// Index la marque dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_BRAND,
				'obj_id_0' => $one_brd,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Met à jour le nom d'une marque
 *	@param int $id Obligatoire, identifiant de la marque à actualiser
 *	@param string $name Obligatoire, nom de la marque
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_brands_set_name( $id, $name ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$name = ucfirst(
		trim($name)
	);

	if( !ria_mysql_query("update prd_brands set brd_name='".addslashes($name)."' where brd_tnt_id=".$config['tnt_id']." and brd_id=".$id) ){
		return false;
	}

	try{
		// Index la marque dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_BRAND,
			'obj_id_0' => $id,
		));

		return true;
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());

		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la description d'une marque
 *	@param int $id Obligatoire, identifiant de la marque à actualiser
 *	@param string $desc Obligatoire, description de la marque
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_brands_update_desc( $id, $desc ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$desc = ucfirst(
		trim($desc)
	);

	if( !ria_mysql_query('update prd_brands set brd_desc=\''.addslashes($desc).'\' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id) ){
		return false;
	}

	try{
		// Index la marque dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_BRAND,
			'obj_id_0' => $id,
		));

		return true;
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());

		return false;
	}
}
// \endcond

// \cond onlyria
/**	Provoque la supression d'une marque. Deux modes de suppression sont gérés par cette fonction.
 *	Il est possible de ne supprimer que la marque ($products=false) ou la marque et ses produits ($products=true).
 *
 * 	Si seule la marque est supprimée (comportement par défaut), elle est tout de même détachée de ses produits. Cela signifie qu'en cas de
 *  restauration, il est possible de restaurer la marque mais pas le lien marque/produit.
 *
 *	Les suppression sont virtuelles pour la marque et pour les produits, mais le lien marque/produit est supprimé physiquement.
 *
 *	@param int $id Identifiant de la marque à supprimer
 *	@param bool $products facultatif. false si seul la marque est supprimée, true si les produits doivent également être supprimés.
 *
 *	@return bool false en cas d'erreur, true en cas de succès.
 *
 */
function prd_brands_del( $id, $products=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( $products )
		ria_mysql_query('update prd_products set prd_date_deleted=now() where prd_tnt_id='.$config['tnt_id'].' and prd_brd_id='.$id);
	else
		ria_mysql_query('update prd_products set prd_brd_id=null where prd_tnt_id='.$config['tnt_id'].' and prd_brd_id='.$id);

    prd_brands_image_del($id); // Supprime l'image associée

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;


	// supprime l'url de la marque
	rew_rewritemap_del_multilingue( _FLD_BRD_URL, array($id) );
	$url = prd_brands_get_url( $id );
	if( trim($url)!='' )
		rew_rewritemap_del( $url );

	return ria_mysql_query('update prd_brands set brd_date_deleted=now() where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id);
}
// \endcond

// \cond onlyria
/**	Permet la restauration d'une marque ayant été supprimée.
 *
 *	La restauration échouera si une marque portant le même nom est actuellement active.
 *
 *	@param int $id Identifiant de la marque à restaurer
 *
 *	@return bool true si la restauration à réussi, false dans le cas contraire.
 *
 */
function prd_brands_restore( $id ){
	if( !is_numeric($id) ){
		return false;
	}

	global $config;

	if( !ria_mysql_query('update prd_brands set brd_date_deleted=null where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$id) ){
		return false;
	}

	try{
		// Index la marque dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_BRAND,
			'obj_id_0' => $id,
		));

		return true;
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());

		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de mettre à jour le compteur de produits publiés rattachés à une marque
 *	@param int $brd Identifiant de la marque à actualiser
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échéc
 */
function prd_brands_update_products( $brd ){
	if( !is_numeric($brd) ) return false;
	global $config;

	$dps = prd_deposits_get_main();
	if( !$dps ){
		$dps = 0;
	}

	// récupère le nombre de produit publié
	$sql = '
		select count(prd_id) as products
		from prd_products
			left join prd_stocks on (prd_tnt_id = sto_tnt_id and prd_id = sto_prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0)
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_brd_id='.$brd.'
			and prd_publish=1 and prd_publish_cat=1
			and (not prd_sleep or (' . prd_stocks_get_sql() . '-ifnull(sto_prepa, 0))>0)
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	$res = ria_mysql_query('
		update prd_brands
		set brd_products='.ria_mysql_result($res, 0, 'products').'
		where brd_tnt_id='.$config['tnt_id'].'
			and brd_id='.$brd.'
	');

	return $res;
}
// \endcond

/** Cette fonction permet de compter le nombre de produits rattachés à une marque
 *	@param int $brd Obligatoire, identifiant d'une marque
 *	@param int|array $cat Optionnel, identifiant ou tableau d'identifiants d'une categorie
 *	@param bool $cat_recursive Optionnel, si oui ou non on récupère les produits des sous catégories
 *	@param bool $prd_publish Optionnel, détermine si les produits de la marque doivent être publiés et actifs (ou en déstockage).
 *	@param int|array $prd Optionnel, identifiant ou tableau d'identifiants de produits
 *	@return bool false si le paramètre est omis ou faux, sinon le nombre de produit
 */
function prd_brands_count_products( $brd, $cat=0, $cat_recursive=false, $prd_publish=false, $prd=false ){
	if( !is_numeric($brd) || $brd<=0 ) return false;
	global $config;

	if( $prd!==false ){
		if( !is_array($prd) ){
			if( !is_numeric($prd) || $prd<=0 ){
				return false;
			}else{
				$prd = array( $prd );
			}
		}else{
			foreach( $prd as $p ){
				if( !is_numeric($p) || $p<=0 ){
					return false;
				}
			}
		}
	}else{
		if( $cat!==0 ){
			if( !is_array($cat) ){
				if( !is_numeric($cat) || $cat<=0 ){
					return false;
				}else{
					$cat = array( $cat );
				}
			}else{
				foreach( $cat as $c ){
					if( !is_numeric($c) || $c<=0 ){
						return false;
					}
				}
			}

			$ar_cats = $cat;

			if( $cat_recursive ){
				foreach( $cat as $one_cat ){
					$catchilds = prd_categories_childs_get_array( $one_cat, $prd_publish );
					if( is_array($catchilds) && sizeof($catchilds) ){
						$ar_cats = array_merge( $ar_cats, $catchilds );
					}
				}
			}

			$prd = array( 0 );

			$rproduct = ria_mysql_query( 'select cly_prd_id as prd_id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id in ('.implode(', ', $ar_cats).')' );
			if( $rproduct && ria_mysql_num_rows($rproduct) ){
				while( $product = ria_mysql_fetch_array($rproduct) ){
					$prd[] = $product['prd_id'];
				}
			}
		}
	}

	$sql = '
		select count(*) as "products"
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_brd_id='.$brd.'
			and prd_date_deleted is null
	';

	if( is_array($prd) && sizeof($prd) ){
		$sql .= ' and prd_id in ('.implode( ', ', $prd ).')';
	}

	if( $prd_publish ){
		$dps = prd_deposits_get_main();
		$dps = !is_numeric($dps) || $dps < 0 ? 0 : $dps;
		$sql .= '
			and prd_publish = 1 and prd_publish_cat = 1 and (
				prd_sleep = 0 or ifnull((
					select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
					where sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
				), 0) > 0
			)
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['products'];
}

// \cond onlyria
/**	Cette fonction récupère des marques suivant le détail de l'historique de commandes d'un client.
 *	@param int $usr_id Obligatoire, identifiant du client.
 *	@param bool $inc_invoice Optionnel, détermine si les factures sans commande à l'origine doivent être prises en compte.
 *	@param array $fld Optionnel, tableau de champs avancés permettant de filtrer l'historique du client.
 *		Ces champs avancés doivent être de la classe "ligne de commande" (ou "ligne de facture" si $inc_invoice est activé).
 *		Une valeur de filtrage peut être ajoutée optionnellement.
 *		Le tableau se compose de sous-tableaux associatifs de une, deux ou trois clés suivant le contexte :
 *			- id : identifiant du champ avancé pour la classe "ligne de commande".
 *			- [inv_id] : identifiant du champ avancé pour la classe "ligne de facture", si $inc_invoice est activé.
 *			- [value] : valeur de filtrage du champ, applicable à la fois pour "id" et "inv_id".
 *
 *	@param bool $published  Optionnel, filtrage des marques publiées (True) ou non (False).
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la marque.
 *		- name : nom de la marque.
 *		- title : titre de la marque.
 *		- desc : description de la marque.
 */
function prd_brands_get_from_history( $usr_id, $inc_invoice=false, $fld=false, $published=null ){

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}

	if( !is_array($fld) ){
		$fld = array();
	}

	foreach( $fld as $fld_data ){
		if( !is_array($fld_data) ){
			return false;
		}
		if( !isset($fld_data['id']) || !is_numeric($fld_data['id']) || $fld_data['id'] <= 0 ){
			return false;
		}
		if( $inc_invoice && ( !isset($fld_data['inv_id']) || !is_numeric($fld_data['inv_id']) || $fld_data['inv_id'] <= 0 ) ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			distinct brd_id as "id", brd_name as "name", if(ifnull(brd_title, "") = "", brd_name, brd_title) as "title", brd_desc as "desc"
		from
			prd_brands
			join prd_products as p on brd_tnt_id = p.prd_tnt_id and brd_id = p.prd_brd_id
			join ord_products as op on p.prd_tnt_id = op.prd_tnt_id and p.prd_id = op.prd_id
			join ord_orders as o on op.prd_tnt_id = o.ord_tnt_id and op.prd_ord_id = o.ord_id
	';
	$sql_inv = '
		select
			distinct brd_id as "id", brd_name as "name", if(ifnull(brd_title, "") = "", brd_name, brd_title) as "title", brd_desc as "desc"
		from
			prd_brands
			join prd_products as p on brd_tnt_id = p.prd_tnt_id and brd_id = p.prd_brd_id
			join ord_inv_products as ip on p.prd_tnt_id = ip.prd_tnt_id and p.prd_id = ip.prd_id
			join ord_invoices as i on ip.prd_tnt_id = i.inv_tnt_id and ip.prd_inv_id = i.inv_id
	';

	$i = 0;
	foreach( $fld as $fld_data ){
		$sql .= '
			join fld_object_values as v'.$i.' on
				op.prd_tnt_id = v'.$i.'.pv_tnt_id and op.prd_ord_id = v'.$i.'.pv_obj_id_0 and op.prd_id = v'.$i.'.pv_obj_id_1
				and op.prd_line_id = v'.$i.'.pv_obj_id_2 and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'" and v'.$i.'.pv_fld_id = '.$fld_data['id'].'
		';
		$sql_inv .= '
			join fld_object_values as v'.$i.' on
				ip.prd_tnt_id = v'.$i.'.pv_tnt_id and ip.prd_inv_id = v'.$i.'.pv_obj_id_0 and ip.prd_id = v'.$i.'.pv_obj_id_1
				and ip.prd_line_id = v'.$i.'.pv_obj_id_2 and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'" and v'.$i.'.pv_fld_id = '.( isset($fld_data['inv_id']) ? $fld_data['inv_id'] : 0 ).'
		';
		$i++;
	}

	$sql .= '
		where
			brd_tnt_id = '.$config['tnt_id'].'
			and o.ord_usr_id = '.$usr_id.'
			and p.prd_date_deleted is null
			and brd_date_deleted is null
			and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
			and ord_masked = 0
	';
	$sql_inv .= '
		where
			brd_tnt_id = '.$config['tnt_id'].'
			and i.inv_usr_id = '.$usr_id.'
			and p.prd_date_deleted is null
			and brd_date_deleted is null
			and ip.prd_ord_id is null
			and inv_masked = 0
	';

	if( $published !== null ){
		$sql .= ' and brd_products '.( $published ? '>' : '<=' ).' 0';
		$sql_inv .= ' and brd_products '.( $published ? '>' : '<=' ).' 0';
	}

	$i = 0;
	foreach( $fld as $fld_data ){
		if( isset($fld_data['value']) && trim($fld_data['value']) ){
			$sql .= ' and v'.$i.'.pv_value = "'.addslashes($fld_data['value']).'"';
			$sql_inv .= ' and v'.$i.'.pv_value = "'.addslashes($fld_data['value']).'"';
		}
		$i++;
	}

	if( $inc_invoice ){

		$sql = '
			select tmp.id, tmp.name, tmp.title
			from (
				'.$sql.'
				union
				'.$sql_inv.'
			) as tmp
		';

	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('prd_brands_get_from_history, erreur SQL : '.mysql_error()."\n".$sql);
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère un tableau d'identifiants produits liés à une marque.
 *	@param int $brd_id Obligatoire, identifiant d'une marque.
 *	@return array Un tableau contenant tous les identifiants produits
 */
function prd_brands_get_prd_ids( $brd_id ){
	$ar_prd_ids = array();

	if (!is_numeric($brd_id) || $brd_id <= 0) {
		return $ar_prd_ids;
	}

	global $config;

	$res = ria_mysql_query('
		select prd_id
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_brd_id = '.$brd_id.'
			and prd_date_deleted is null
	');

	if ($res) {
		while ($r = ria_mysql_fetch_assoc($res)) {
			$ar_prd_ids[] = $r['prd_id'];
		}
	}

	return $ar_prd_ids;
}
// \endcond

/// @}

// \cond onlyria
/** \defgroup model_resellers Revendeurs
 *	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des revendeurs.
 *
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction teste l'existence d'un identifiant de revendeur dans la base de données.
 *	@param int $id Obligatoire, identifiant du revendeur à tester.
 *
 *	@return bool true si le revendeur existe, false dans le cas contraire.
 */
function rsl_resellers_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	return ria_mysql_num_rows(ria_mysql_query('select rsl_id from rsl_resellers where rsl_tnt_id='.$config['tnt_id'].' and rsl_id='.$id))>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'un revendeur d'une marque donnée à partir de l'identifiant du revendeur et de la marque.
 *  @param int $rsl Obligatoire, identifiant du revendeur
 *	@param int $brd Obligatoire, identifiant de la marque
 *  @param float $sales Facultatif, chiffre d'affaire réalisé par le revendeur pour une marque donnée
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rsl_brands_add( $rsl, $brd, $sales=0 ){
	global $config;

	if( !is_numeric($rsl) || $rsl<=0 || !rsl_resellers_exists($rsl) ) return false;
	if( !is_numeric($brd) || $brd<=0 || !prd_brands_exists($brd) ) return false;
	if( !is_numeric($sales) || $sales<0 ) return false;

	return ria_mysql_query('
		insert into rsl_brands
			(rsl_tnt_id,rsl_rsl_id,rsl_brd_id,rsl_sales)
		values
			('.$config['tnt_id'].','.$rsl.','.$brd.','.$sales.')
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de spécifier qu'un revendeur est synchronisé
 *	@param int $rsl Obligatoire, identifiant du revendeur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function rsl_resellers_set_is_sync( $rsl ){
	global $config;

	if( !is_numeric($rsl) || $rsl<=0 || !rsl_resellers_exists($rsl) ) return false;

	return ria_mysql_query('update rsl_resellers set rsl_is_sync=1 where rsl_tnt_id='.$config['tnt_id'].' and rsl_id='.$rsl);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la supression d'un revendeur d'une marque donnée à partir de l'identifiant du revendeur et de la marque.
 *  @param int $rsl Obligatoire, identifiant du revendeur
 *	@param int $brd Obligatoire, identifiant de la marque
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rsl_brands_del( $rsl, $brd ){
	global $config;

	if( !is_numeric($rsl) || $rsl<=0 || !rsl_resellers_exists($rsl) ) return false;
	if( !is_numeric($brd) || $brd<=0 || !prd_brands_exists($brd) ) return false;

	return ria_mysql_query('
		delete from rsl_brands
		where rsl_tnt_id='.$config['tnt_id'].' and rsl_rsl_id='.$rsl.' and rsl_brd_id='.$brd.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des ventes réalisées par un revendeur sur une marque donnée
 *  @param int $rsl Obligatoire, identifiant du revendeur
 *	@param int $brd Obligatoire, identifiant de la marque
 *	@param float $sales Facultatif, chiffre d'affaire réalisé par le revendeur pour une marque donnée
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function rsl_brands_update( $rsl, $brd, $sales=0 ){
	global $config;

	if( !is_numeric($rsl) || $rsl<=0 || !rsl_resellers_exists($rsl) ) return false;
	if( !is_numeric($brd) || $brd<=0 || !prd_brands_exists($brd) ) return false;

	return ria_mysql_query('
		update rsl_brands set rsl_sales='.$sales.'
		where rsl_tnt_id='.$config['tnt_id'].' and rsl_rsl_id='.$rsl.' and rsl_brd_id='.$brd.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'un revendeur
 *
 *	@param string $name Obligatoire, nom du magasin
 *	@param string $address1 Optionnel, adresse du revendeur
 *	@param string $address2 Optionnel, adresse du revendeur
 *	@param string $zipcode Optionnel, code postal du revendeur
 *	@param string $city Optionnel, ville du revendeur
 *	@param string $phone Optionnel, numéro de téléphone du revendeur
 *	@param string $fax Optionnel, numéro de fax du revendeur
 *	@param string $website Optionnel, url du site Internet du revendeur
 *	@param float $latitude Optionnel, coordonnée géographique du revendeur
 *  @param float $longitude Optionnel, coordonnée géographique du revendeur
 * 	@param bool $is_website Optionnel, booléen indiquant si le revendeur est une boutique en ligne
 *  @param string $title Optionnel, alias du revendeur
 *	@param bool $spc_kvh Optionnel, spécifique à Navicom, spécialiste KVH oui/non
 *	@param bool $spc_nexus Optionnel, spécifique à Navicom, spécialiste Nexus oui/non
 *	@param bool $spc_geonav Optionnel, spécifique à Navicom, spécialiste Geonav oui/non
 *	@param bool $spc_hummin Optionnel, spécifique à Navicom, spécialiste Humminbird oui/non
 *	@param bool $spc_onix Optionnel, spécifique à Navicom, spécialiste Onix oui/non
 *	@param string $country Optionnel, pays du revendeur (si vide alors France)
 *
 *	@return int l'identifiant attribué au revendeur en cas de succès
 *	@return bool False en cas d'erreur
 *	@todo Retirer le spécifique Navicom. Cette fonction est encore utilisée en synchro.
 *
 */
function rsl_resellers_add( $name, $address1='', $address2='', $zipcode='', $city='', $phone='', $fax='', $website='', $latitude=false, $longitude=false, $is_website=0, $title='', $spc_kvh=false, $spc_nexus=false, $spc_geonav=false, $spc_hummin=false, $spc_onix=false, $country='' ){

	if( !trim($name) ){
		return false;
	}

	$name = ucfirst(trim($name));
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$city = ucfirst(trim($city));
	$phone = trim($phone);
	$fax = trim($fax);
	$website = trim($website);
	if( $website && substr($website,0,7) != 'http://' ){
		$website = 'http://'.$website;
	}
	if( !is_numeric($latitude) ){
		$latitude = 'null';
	}
	if( !is_numeric($longitude) ){
		$longitude = 'null';
	}

	$is_website = $is_website ? 1 : 0;
	$title = ucfirst(trim($title));
	if( !trim($country) ){
		$country = 'FRANCE';
	}
	$country = strtoupper(trim($country));


	if( (!is_numeric($latitude) || !is_numeric($longitude)) ){

		// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
		$gps = sys_google_maps_search( $address1.' '.$zipcode.' '.$city.', '.$country );
		if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
			$gps = sys_google_maps_search( $zipcode.' '.$city.', '.$country );
		}

		// mise à jour des coodonées GPS trouvées
		if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
			$latitude = $gps['lat'];
			$longitude = $gps['lng'];
		}
	}

	global $config;

	$fields = array(
		'rsl_tnt_id', 'rsl_name', 'rsl_address1', 'rsl_address2', 'rsl_zipcode', 'rsl_city', 'rsl_country',
		'rsl_phone', 'rsl_fax', 'rsl_website', 'rsl_latitude', 'rsl_longitude', 'rsl_is_website', 'rsl_title',
		'rsl_specialty_kvh', 'rsl_specialty_nexus', 'rsl_specialty_geonav', 'rsl_specialty_humminbird', 'rsl_specialty_onix'
	);

	$values = array(
		$config['tnt_id'], '"'.addslashes($name).'"', '"'.addslashes($address1).'"', '"'.addslashes($address2).'"', '"'.addslashes($zipcode).'"', '"'.addslashes($city).'"', '"'.addslashes($country).'"',
		'"'.addslashes($phone).'"', '"'.addslashes($fax).'"', '"'.addslashes($website).'"', $latitude, $longitude, $is_website, '"'.addslashes($title).'"',
		$spc_kvh ? 1 : 0, $spc_nexus ? 1 : 0, $spc_geonav ? 1 : 0, $spc_hummin ? 1 : 0, $spc_onix ? 1 : 0
	);

	$sql = '
		insert into rsl_resellers ('.implode(', ', $fields).') values ('.implode(', ', $values).')
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des propriétés d'un revendeur
 *
 *	@param int $id Identifiant du revendeur à mettre à jour
 *	@param string $name Nom du revendeur
 *
 *	@param string $address1 Adresse du revendeur
 *	@param string $address2 Adresse du revendeur
 *	@param string $zipcode Code postal du revendeur
 *	@param string $city Ville du revendeur
 *
 *	@param string $phone Numéro de téléphone du revendeur
 *	@param string $fax Numéro de fax du revendeur
 *	@param string $website Url du site Internet du revendeur
 *
 *	@param float $latitude coordonnée géographique du revendeur
 *  @param float $longitude coordonnée géographique du revendeur
 *  @param bool $is_website booléen indiquant si le revendeur est une boutique en ligne
 *
 *	@param string $title Titre permettant la surchage du nom du revendeur tel qu'il est indiqué dans la gestion commerciale
 *	@param string $country Optionnel, nom du pays (si vide alors France)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function rsl_resellers_update( $id, $name, $address1='', $address2='', $zipcode='', $city='', $phone='', $fax='', $website='', $latitude=false, $longitude=false, $is_website=0, $title='', $country='' ){
	global $config;

	if( !rsl_resellers_exists($id) || !is_numeric($id) || $id<=0 ) return false;
	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$city = ucfirst(trim($city));
	$phone = trim($phone);
	$fax = trim($fax);
	//$email = strtolower(trim($email));
	$website = trim($website);
	$name = ucfirst(trim($name));


	if( $website && substr($website,0,7)!='http://' )
		$website = 'http://'.$website;


	if( !is_numeric($is_website) || $is_website<0 ) $is_website=0;
	$title = ucfirst(trim($title));

	if( !trim($country) ){
		$country = 'FRANCE';
	}

	if( (!is_numeric($latitude) || !is_numeric($longitude)) ){

		// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
		$gps = sys_google_maps_search( $address1.' '.$zipcode.' '.$city.', '.$country );
		if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
			$gps = sys_google_maps_search( $zipcode.' '.$city.', '.$country );
		}

		// mise à jour des coodonées GPS trouvées
		if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
			$latitude = $gps['lat'];
			$longitude = $gps['lng'];
		}
	}

	$sql = "
		update rsl_resellers
		set
			rsl_name='".addslashes($name)."',
			rsl_address1='".addslashes($address1)."',
			rsl_address2='".addslashes($address2)."',
			rsl_zipcode='".addslashes($zipcode)."',
			rsl_city='".addslashes($city)."',
			rsl_country='".addslashes(strtoupper(trim($country)))."',
			rsl_phone='".addslashes($phone)."',
			rsl_fax='".addslashes($fax)."',
			rsl_website='".addslashes($website)."',
			rsl_latitude=".$latitude.",
			rsl_longitude=".$longitude.",
			rsl_is_website=".$is_website."
	";
	if( trim($title) ){
		$sql .= ", rsl_title='".addslashes($title)."'";
	}
	$sql .= "
		where
			rsl_tnt_id=".$config['tnt_id']."
			and rsl_id=".$id."
	";

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un revendeur
 *	@param int $id Identifiant du revendeur à supprimer
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function rsl_resellers_del( $id ){
	global $config;

	if( !rsl_resellers_exists($id) || !is_numeric($id) || $id<=0 ) return false;

	return ria_mysql_query('delete from rsl_resellers where rsl_tnt_id='.$config['tnt_id'].' and rsl_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des lignes de relation marque / revendeur selon des paramètres optionnels.
 *	@param int|array $brd_id Optionnel, identifiant ou tableau d'identifiants de marques.
 *	@param int|array $rsl_id Optionnel, identifiant ou tableau d'identifiants de revendeurs.
 *	@param bool $is_website Optionnel, permet de filtrer les revendeurs site web oui / non.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- brd_id : identifiant de marque.
 *		- rsl_id : identifiant de revendeur.
 */
function rsl_brands_get( $brd_id=0, $rsl_id=0, $is_website=null ){

	$brd_id = control_array_integer( $brd_id, false );
	if( !is_array($brd_id) ){
		return false;
	}

	$rsl_id = control_array_integer( $rsl_id, false );
	if( !is_array($rsl_id) ){
		return false;
	}

	global $config;

	$sql = '
		select rsl_brd_id as brd_id, rsl_rsl_id as rsl_id
		from rsl_brands as rb
		where
			rsl_tnt_id = '.$config['tnt_id'].'
			and rsl_brd_id in (
				select brd_id
				from prd_brands
				where brd_tnt_id = rsl_tnt_id
				and brd_date_deleted is null
	';

	if( sizeof($brd_id) ){
		$sql .= '
				and brd_id in ('.implode(', ', $brd_id).')
		';
	}

	$sql .= '
			) and rsl_rsl_id in (
				select r.rsl_id
				from rsl_resellers as r
				where r.rsl_tnt_id = rb.rsl_tnt_id
	';

	if( sizeof($rsl_id) ){
		$sql .= '
				and r.rsl_id in ('.implode(', ', $rsl_id).')
		';
	}

	if( $is_website !== null ){
		$sql .= '
				and r.rsl_is_website = '.( $is_website ? 1 : 0 ).'
		';
	}

	$sql .= '
		)
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Retourne l'ensemble des revendeurs présents dans la base de données.
 *	@param int $brd Optionnel, identifiant d'une marque sur laquelle filtrer le résultat.
 *	@param string $city Optionnel, nom d'une ville sur laquelle filtrer le résultat
 *	@param int $dept Optionnel, n° de département sur lequel filtrer le résultat
 *	@param string $website Optionnel, si true ne retourne que les sites Internet, si false, ne retourne que les magasins physiques. Si absent ou null, retourne tout.
 *	@param int $limit Optionnel, nombre maximal de résultats retournés
 *	@param $unused1 Optionnel, ne plus utiliser
 *	@param $unused2 Optionnel, ne plus utiliser
 *	@param $unused3 Optionnel, ne plus utiliser
 *	@param $unused4 Optionnel, ne plus utiliser
 *	@param $unused5 Optionnel, ne plus utiliser
 *	@param string $country Optionnel, filtrage par pays (vide pour tous les pays)
 *	@param $specialty Optionnel, liste de spécialités.
 *	@param int|array $prd_id Optionnel, identifiant (ou tableau d'identifiants) d'un produit qui est disponible chez le revendeur.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du revendeur
 *		- name : nom du revendeur
 *		- address1 : adresse du revendeur
 *		- address2 : adresse complémentaire du revendeur
 *		- zipcode : Code postal du revendeur
 *		- city : Ville du revendeur
 *		- country : Pays du revendeur (toujours en majuscules, FRANCE si vide)
 * 		- phone : NÂ° de téléphone du revendeur
 * 		- fax : NÂ° de fax du revendeur
 * 		- website : adresse du site internet du revendeur
 * 		- latitude : Coordonnées géographique (latitude) du revendeur
 * 		- longitude : Coordonnées géographique (longitude) du revendeur
 * 		- is_website : indique si le revendeur est une boutique en ligne
 * 		- title : alias du revendeur
 *		- rsl_sales : CA pour la marque (si $brd est spécifié uniquement)
 */
function rsl_resellers_get( $brd=0, $city='', $dept=0, $website=null, $limit=-1, $unused1=null, $unused2=null, $unused3=null, $unused4=null, $unused5=null, $country='', $specialty=array(), $prd_id=0 ){
	global $config;

	if( !is_numeric($brd) || $brd<0 ) return false;
	if( !is_numeric($dept) || $dept<0 ) return false;
	if( is_numeric($city) ) return false;
	if( $website ) $dept = 0;

	$prd_id = control_array_integer($prd_id, false);
	if( $prd_id === false ){
		return false;
	}

	$sql = '
		select rsl_id as id, rsl_name as name, rsl_address1 as address1, rsl_address2 as address2,
			rsl_zipcode as zipcode, rsl_city as city, ifnull(rsl_country, "FRANCE") as country, rsl_phone as phone, rsl_fax as fax,
			rsl_website as website, rsl_latitude as latitude, rsl_longitude as longitude,
			rsl_is_website as is_website, rsl_title as title '.( $brd<=0 ? '' : ',rsl_sales ').'
		from rsl_resellers
		'.( $brd<=0 ? '' : ', rsl_brands').' where rsl_resellers.rsl_tnt_id='.$config['tnt_id'].( $brd<=0 ? '' : ' and rsl_id=rsl_rsl_id');

	if( $website!==null ){
		$sql .= ' and rsl_is_website'.( $website ? '!=' : '=' ).'0';
		if( $website ){
			$sql .= ' and rsl_website!=""';
		}
	}
	if( $brd>0 ){
		$sql .= ' and rsl_brd_id='.$brd;
	}
	if( $dept>0 ){
		$sql .= ' and rsl_zipcode like "'.$dept.'%"';
	}
	if( $city!='' ){
		$sql .= ' and upper(trim(rsl_city)) like \'%'.addslashes(strtoupper(trim($city)))."%'";
	}
	if( trim($country) ){
		$sql .= ' and ifnull(rsl_country, "FRANCE") = "'.addslashes(strtoupper(trim($country))).'"';
	}

	if( is_array($specialty) && sizeof($specialty) ){
		$sql .= ' and (';
		$first = true;
		foreach( $specialty as $s ){
			if( !$first ){
				$sql .= ' or ';
			}
			$sql .= ' exists (
				select 1 from rsl_specialists
				where rss_tnt_id='.$config['tnt_id'].' and rss_rsl_id=rsl_id
				and rss_name = "'.addslashes($s).'"
			)';
			$first = false;
		}
		$sql .= ') ';
	}

	if( sizeof($prd_id) && isset($config['fld_rsl_usr_id'], $config['date_max_histo_resellers']) ){
		$sql .= '
			and rsl_id in (
				select pv_obj_id_0
				from fld_object_values
				where pv_tnt_id = '.$config['tnt_id'].' and pv_fld_id = '.$config['fld_rsl_usr_id'].'
				and pv_lng_code='.$config['i18n_lng'].' and cast(pv_value as unsigned) in (
					select ord_usr_id
					from ord_orders
					join ord_products on ord_id = prd_ord_id and ord_tnt_id = prd_tnt_id
					where ord_state_id in ('._STATE_BL_EXP.', '._STATE_INVOICE.', '._STATE_BL_STORE.', '._STATE_INV_STORE.')
					and ord_masked = 0 and ord_date >= "'.dateheureparse($config['date_max_histo_resellers']).'"
					and prd_id in ('.implode(', ', $prd_id).')
				)
			)
		';
	}

	$sql .= ' order by '.( $brd<=0 ? '( select sum(rsl_sales) from rsl_brands where rsl_tnt_id='.$config['tnt_id'].' and rsl_rsl_id=rsl_id )' : 'rsl_sales' ).' desc';

	if($limit>0){
		$sql .= ' limit 0,'.$limit;
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/// @}
// \endcond

/// \ingroup model_brands
/// @{

// \cond onlyria
/**	Cette fonction reconstruit les url de marques quand celles-ci n'en n'ont pas
 */
function prd_brands_rebuild_url(){
	global $config;

	$rwst = wst_websites_get();

	if( $brands = prd_brands_get() ){
		while( $b = ria_mysql_fetch_array($brands) ){
			if( !trim($b['url_alias']) ){
				if( $rwst && ria_mysql_num_rows($rwst) ){
					ria_mysql_data_seek( $rwst, 0 );

					// Crée les alias
					$alias = rew_rewritemap_generated( array($b['id']), CLS_BRAND );

					$url = '';
					while( $wst = ria_mysql_fetch_array($rwst) ){
						$prd_pages = cfg_urls_get( $wst['id'], CLS_BRAND);
						if( $prd_pages ){
							while( $page = ria_mysql_fetch_array($prd_pages) ){
								if( trim($url)=='' )
									$url = rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$b['id'], 200, $wst['id'], false, null, $b['id'] );
								else
									rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$b['id'], 200, $wst['id'], false, null, $b['id'] );
							}
						}
					}

					ria_mysql_query('update prd_brands set brd_url_alias=\''.$url.'\' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$b['id']);
				}
			}
		}
	}
}
// \endcond

// \cond onlyria
/** Cette fonction créer une url d'une marque
 *	@param int $brd_id Obligatoire, identifiant d'une marque
 *	@return string|bool La nouvelle url en cas de succès, False dans le cas contraire
 */
function prd_brands_url_alias_add( $brd_id ){
	if( !is_numeric($brd_id) || $brd_id<=0 ){
		return false;
	}

	global $config;

	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	// Crée les alias
	$alias = rew_rewritemap_generated( array($brd_id), CLS_BRAND );

	$url = '';
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_BRAND);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				if( trim($url)=='' )
					$url = rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$brd_id, 200, $wst['id'], false, null, $brd_id );
				else
					rew_rewritemap_add_specify_class( CLS_BRAND, $alias.$page['key'], $page['url'].( strpos( $page['url'], '?' ) === false ? '?':'&' ).'brd='.$brd_id, 200, $wst['id'], false, null, $brd_id );
			}
		}
	}

	if( !ria_mysql_query('update prd_brands set brd_url_alias=\''.$url.'\' where brd_tnt_id='.$config['tnt_id'].' and brd_id='.$brd_id) ){
		return false;
	}

	return $url;
}
// \endcond

/**	Retourne l'ensemble des marques présentes dans la base de données correspondant à la recherche
 *
 *	@param string $word Optionnel, mot rechercher dans le nom de la marque
 *	@param bool $published  Optionnel, détermine si toutes les marques sont retournées, ou seulement celles qui ont des produits publiés (ignoré si id est fourni).
 *	@param bool $like_start Optionnel, si false permet de faire un like sans prendre en compte le début
 *	@param bool $like_end Optionnel, si false permet de faire un like sans prendre en compte la fin
 *
 *	@return array un tableau des identifiants des marques
 *
 */
function prd_brands_search( $word = '', $published=false, $like_start=true, $like_end=true ){
	global $config;

	$sql = '
		select brd_id as id
		from prd_brands
		where 	brd_tnt_id='.$config['tnt_id'].'
			and brd_date_deleted is null
	';

	$like = addslashes($word);
	if( $like_start ) 	$like = '%'.$like;
	if( $like_end ) 	$like = $like.'%';

	$sql .= ' and brd_name like \''.$like.'\'';

	if( $published ) $sql .= ' and brd_products';

	$sql .= ' and brd_date_deleted is null';

	$results = ria_mysql_query($sql);
	if( !ria_mysql_num_rows( $results ) ) return false;

	$return = array();
	while( $b = ria_mysql_fetch_array( $results ) ){
		$return[] = $b['id'];
	}

	return $return;
}

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les marques.
 *	@return bool False si la méthode de tri est alphabétique, True si la méthode de tri est personnalisée
 */
function prd_brands_order_get(){
	global $config;

	$rorderby = cfg_overrides_get(0, array(), 'orderby_brands');
	if( $rorderby && ria_mysql_num_rows($rorderby) ){
		$orderby = ria_mysql_fetch_assoc($rorderby);
		$orderby = $orderby['value'];
	}else {
		$orderby = 'alpha';
	}

	return ($orderby == 'perso');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour les marques.
 *	@param bool $order Optionnel, mode de tri - false pour un tri alphabétique, true pour un tri numérique défini par l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_brands_order_update( $order=false ){
	global $config;

	if( $order ){

		// récupère les marques
		$rbrd = prd_brands_get();

		if( $rbrd ){

			$rmaxpos = ria_mysql_query('
				select ifnull(max( brd_pos ) + 1, 0) as max_pos
				from prd_brands
				where brd_tnt_id = ' . $config['tnt_id'].'
			');

			$maxpos = ria_mysql_fetch_assoc( $rmaxpos );
			$maxpos = $maxpos['max_pos'];

			while( $brd = ria_mysql_fetch_assoc($rbrd) ){

				if( !$brd['pos'] ){
					ria_mysql_query('update prd_brands set brd_pos = '.$maxpos.' where brd_tnt_id = '.$config['tnt_id'].' and brd_id = '.$brd['id']);
					$maxpos++;
				}
			}

		}

		$res = cfg_overrides_set_value('orderby_brands', 'perso');

	}else{
		$res = cfg_overrides_set_value('orderby_brands', 'alpha');
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la date de dernière modification d'une marque.
 *	@param int $id Identifiant de la marque.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_brands_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_brands
		set brd_date_modified = now()
		where brd_tnt_id = '.$config['tnt_id'].' and brd_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

/// @}

/// \ingroup model_resellers
/// @{

	// \cond onlyria
/**	Ajoute une spécialité à un revendeur.
 *	@param int $rsl_id identifiant du revendeur.
 *	@param string $specialty Nom de la spécialité.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function rsl_specialists_add( $rsl_id, $specialty ){
	$specialty = trim($specialty);
	if( !$specialty ){
		return false;
	}
	if( !is_numeric($rsl_id) || $rsl_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		replace into rsl_specialists
			(rss_tnt_id, rss_rsl_id, rss_name)
		values
			('.$config['tnt_id'].', '.$rsl_id.', "'.addslashes($specialty).'")
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Retire une spécialité à un revendeur.
 *	@param int $rsl_id identifiant du revendeur.
 *	@param string $specialty Nom de la spécialité.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function rsl_specialists_del( $rsl_id, $specialty ){
	$specialty = trim($specialty);
	if( !$specialty ){
		return false;
	}
	if( !is_numeric($rsl_id) || $rsl_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from rsl_specialists
		where rss_tnt_id = '.$config['tnt_id'].' and rss_rsl_id = '.$rsl_id.' and rss_name = "'.addslashes($specialty).'"
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Récupère une liste de spécialités, selon des critères de filtrage sur les revendeurs.
 *	@param string $city Optionnel, ville du revendeur (peut être partiel).
 *	@param int $dept Optionnel, numéro de département du revendeur.
 *	@param string $country Optionnel, Pays du revendeur.
 *	@param string $website Optionnel, filtre les revendeurs web uniquement.
 *	@return array Un tableau des spécialités, False en cas d'échec.
 */
function rsl_specialists_get_list( $city='', $dept=0, $country='', $website=null ){
	if( !is_numeric($dept) || $dept < 0 ){
		return false;
	}
	if( $website ){
		$dept = 0;
		$country = '';
		$city = '';
	}else{
		$country = trim($country);
		$city = trim($city);
	}

	global $config;

	$sql = '
		select
			distinct rss_name as "name"
		from rsl_specialists
			join rsl_resellers on rss_rsl_id = rsl_id and rss_tnt_id = rsl_tnt_id
		where
			rss_tnt_id = '.$config['tnt_id'].'
	';
	if( $website !== null ){
		$sql .= ' and rsl_is_website'.( $website ? '!=' : '=' ).'0';
		if( $website ){
			$sql .= ' and rsl_website != ""';
		}
	}
	if( $dept > 0 ){
		$sql .= ' and rsl_zipcode like "'.$dept.'%"';
	}
	if( $city ){
		$sql .= ' and rsl_city like "%'.addslashes($city).'%"';
	}
	if( $country ){
		$sql .= ' and upper(ifnull(rsl_country, "FRANCE")) = upper("'.addslashes($country).'")';
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	$specialties = array();
	while( $s = ria_mysql_fetch_assoc($res) ){
		$specialties[] = $s['name'];
	}

	return $specialties;
}
// \endcond

/// @}



<?php

/**
 * Cette classe permet de gérer la connexion et la communication avec l'Api REST d'Ovh
 *
 * C'est une modification de la classe originelle pour fonctionner avec CURL
 */
class Ovh
{
    /**
     * Url to communicate with Ovh API
     *
     * @var array $endpoints
     */
    private $endpoints = array(
        'ovh-eu'        => 'https://api.ovh.com/1.0',
        'ovh-ca'        => 'https://ca.api.ovh.com/1.0',
        'kimsufi-eu'    => 'https://eu.api.kimsufi.com/1.0',
        'kimsufi-ca'    => 'https://ca.api.kimsufi.com/1.0',
        'soyoustart-eu' => 'https://eu.api.soyoustart.com/1.0',
        'soyoustart-ca' => 'https://ca.api.soyoustart.com/1.0',
        'runabove-ca'   => 'https://api.runabove.com/1.0',
    );

    /** Contain endpoint selected to choose API
     *
     * @var string $endpoint
     */
    private $endpoint = null;

    /**
     * Contain key of the current application
     *
     * @var string $application_key
     */
    private $application_key = null;

    /**
     * Contain secret of the current application
     *
     * @var string $application_secret
     */
    private $application_secret = null;

    /**
     * Contain consumer key of the current application
     *
     * @var string $consumer_key
     */
    private $consumer_key = null;

    /**
     * Contain delta between local timestamp and api server timestamp
     *
     * @var string $time_delta
     */
    private $time_delta = null;

    /**
     * Construct a new wrapper instance
     *
     * @param string $application_key    key of your application.
     *                                   For OVH APIs, you can create a application's credentials on
     *                                   https://api.ovh.com/createApp/
     * @param string $application_secret secret of your application.
     * @param string $api_endpoint       name of api selected
     * @param string $consumer_key       If you have already a consumer key, this parameter prevent to do a
     *                                   new authentication
	 * \param $curl Facultatif, handle vers une session curl, tel que retourné par curl_init() 
     *
     * @throws Exception if one parameter is missing or with bad value
     */
    
    public function __construct(
        $application_key,
        $application_secret,
        $api_endpoint,
        $consumer_key = null,
        $curl = null
    ) {
        if (!isset($application_key)) {
            throw new Exception("Application key parameter is empty");
        }

        if (!isset($application_secret)) {
            throw new Exception("Application secret parameter is empty");
        }

        if (!isset($api_endpoint)) {
            throw new Exception("Endpoint parameter is empty");
        }

        if (!array_key_exists($api_endpoint, $this->endpoints)) {
            throw new Exception("Unknown provided endpoint");
        }
        

        if (!isset($curl)) {
            $curl =  curl_init();
        }

        $this->application_key    = $application_key;
        $this->endpoint           = $this->endpoints[$api_endpoint];
        $this->application_secret = $application_secret;
        $this->consumer_key       = $consumer_key;
        $this->time_delta         = null;
    }


    /**
     * Calculate time delta between local machine and API's server
     *
     * @return int
     */
    private function calculateTimeDelta()
    {
        if (!isset($this->time_delta)) {
            $response         = $this->rawCall(
                'GET',
                "/auth/time",
                null,
                false
            );
            $serverTimestamp  = (int)(string)$response;
            $this->time_delta = $serverTimestamp - (int)\time();
        }

        return $this->time_delta;
    }

    /**
     * This is the main method of this wrapper. It will
     * sign a given query and return its result.
     *
     * @param string               $method           HTTP method of request (GET,POST,PUT,DELETE)
     * @param string               $path             relative url of API request
     * @param \stdClass|array|null $content          body of the request
     * @param bool                 $is_authenticated if the request use authentication
     *
     * @return array
     */
    private function rawCall($method, $path, $content = null, $is_authenticated = true)
    {
        $url = $this->endpoint.$path;
        $curl =  curl_init();

        if( isset($content) && $method === 'GET' ){
            $getParams = http_build_query($content);
            $url .= '?'.$getParams;
            $body = '';
        }elseif( isset($content) ){
            $body = json_encode($content);
            curl_setopt( $curl, CURLOPT_POSTFIELDS, $body );
        }else{
            $body = '';
        }

        $headers = array (
            'Content-Type:application/json; charset=utf-8',
            'X-Ovh-Application:'.$this->application_key,
        );

        if ($is_authenticated) {
            if (!isset($this->time_delta)) {
                $this->calculateTimeDelta();
            }
            $now = time() + $this->time_delta;

            $headers[] = 'X-Ovh-Timestamp:'.$now;

            if (isset($this->consumer_key)) {
                $toSign = $this->application_secret.'+'.$this->consumer_key.'+'.$method
                   .'+'.$url.'+'.$body.'+'.$now;
                $signature = '$1$'.sha1($toSign);
                $headers[]  = 'X-Ovh-Consumer:'.$this->consumer_key;
                $headers[] = 'X-Ovh-Signature:'.$signature;
            }
        }
        // Exécute la requête
        curl_setopt( $curl, CURLOPT_URL, $url );
        curl_setopt( $curl, CURLINFO_HEADER_OUT, true );
        curl_setopt( $curl, CURLOPT_HTTPHEADER, $headers );
        curl_setopt( $curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt( $curl, CURLOPT_RETURNTRANSFER, 1 );

        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
    }

    /**
     * Decode a response to an Array
     *
     * @param  $response Réponse obtenu à décoder
     *
     * @return array
     */
    private function decodeResponse($response)
    {
        return json_decode($response, true);
    }

    /**
     * Wrap call to Ovh APIs for GET requests
     *
     * @param string $path    path ask inside api
     * @param array  $content content to send inside body of request
     *
     * @return array
     */
    public function get($path, $content = null)
    {
        return $this->decodeResponse(
            $this->rawCall("GET", $path, $content)
        );
    }

    /**
     * Wrap call to Ovh APIs for POST requests
     *
     * @param string $path    path ask inside api
     * @param array  $content content to send inside body of request
     *
     * @return array
     */
    public function post($path, $content = null)
    {
        return $this->decodeResponse(
            $this->rawCall("POST", $path, $content)
        );
    }

    /**
     * Wrap call to Ovh APIs for PUT requests
     *
     * @param string $path    path ask inside api
     * @param array  $content content to send inside body of request
     *
     * @return array
     */
    public function put($path, $content)
    {
        return $this->decodeResponse(
            $this->rawCall("PUT", $path, $content)
        );
    }

    /**
     * Wrap call to Ovh APIs for DELETE requests
     *
     * @param string $path    path ask inside api
     * @param array  $content content to send inside body of request
     *
     * @return array
     */
    public function delete($path, $content = null)
    {
        return $this->decodeResponse(
            $this->rawCall("DELETE", $path, $content)
        );
    }

    /**
     * Get the current consumer key
     */
    public function getConsumerKey()
    {
        return $this->consumer_key;
    }
}

<?php

/**
 * Class StatCampaign
 */
class StatCampaign {

	/**
	 * @var $campaign
	 */
	private $campaign;

	/**
	 * @var $channel
	 */
	private $channel;

	/** Cette fonction permet d'initialiser un chanel pour avoir des statistiques plus précises sur un channel de campagne
	 *
	 * @param array $channel
	 *
	 * @throws Exception
	 */
	public function setChannel( array $channel ){
		if( !ria_array_key_exists( array(
			'tnt_id',
			'id',
			'wst_id',
			'type',
			'email_from',
			'email_bcc',
			'content',
			'ptn_id',
			'lng_code',
			'cpg_id'
		), $channel )
		){
			throw new Exception( 'Invalid channel keys' );
		}

		$this->channel = $channel;
	}


	/** Cette fonction permet d'initialiser une campagne pour avoir des statistiques précises sur une campagne
	 *
	 * @param array $campaign
	 *
	 * @throws Exception
	 */
	public function setCampaign( array $campaign ){
		if( !ria_array_key_exists( array(
			'id',
			'title',
			'type',
			'desc',
			'date_start',
			'date_end',
			'period',
			'period_info',
			'date_created'
		), $campaign )
		){
			throw new Exception( 'Invalid campaign keys' );
		}
		$this->campaign = $campaign;
	}

	/** Cette fonction permet de récupérer les statistique pour une campagne.
	 * @todo - prise en charge des filtres sur la requete
	 * @todo - prise en charge du filtre sur le channel
	 *
	 * @param bool $filter //non pris en charge pour le moment
	 *
	 * @return bool|resource Retourne le résultat d'une requete mysql avec les colonnes suivante :
	 *                  - tnt_id : Identifiant du tenant
	 * 					- cgp_id : Identifiant de la campagne
	 * 					- chl_id : Identifiant du channel
	 * 					- id : Identifiant du stat
	 * 					- email : Email du receveur
	 * 					- usr_id : Identifiant de l'utilisateur
	 * 					- mobile : Numéro mobile de l'utilisateur
	 * 					- obj_id : Identifiant de l'objet associé a l'envoi
	 *                  - cls_id : Identifiant de la classe associer a l'objet
	 * 					- fld_id : Identifiant du champ qui a filtrer sur l'objet
	 * 					- date_sent : Date d'envoi du message
	 * 					- is_received : Booléen true si le message a été reçus, false sinon
	 * 					- is_consulted : Booléen true si le  message a été consulté, false sinon
	 */
	public function getStats( $filter = array() ){
		if( $filter && !is_array( $filter ) ){
			return false;
		}


		global $config;
		$sql = '
			select
				stat_tnt_id as tnt_id,
				stat_cpg_id as cgp_id,
				stat_chl_id as chl_id,
				stat_id as id,
				stat_email as email,
				stat_usr_id as usr_id,
				stat_mobile as mobile,
				stat_obj_id as obj_id,
				stat_fld_id as fld_id,
				stat_cls_id as cls_id,
				stat_date_sent as date_sent,
				stat_sms_id as sms_id,
				stat_is_received as is_received,
				stat_is_consulted as is_consulted
			from stats_campaigns
			where stat_tnt_id = '.$config['tnt_id'].'
			';

		if( isset($this->campaign) ){
			$sql .= 'and stat_cpg_id = '.$this->campaign['id'];
		}

		$sql .= '
			order by stat_date_sent desc;
		';

		$res = ria_mysql_query($sql);

		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		return $res;
	}

	/**
	 * @return bool
	 * @throws Exception
	 */
	public function getCampaignMessageCount(){
		if( !isset( $this->campaign ) ){
			throw new Exception( 'self::campaign must be set first to use this function' );
		}
		global $config;

		$sql = '
			select count(stat_email) as msg
			from stats_campaigns
			where stat_tnt_id='.$config['tnt_id'].'
				and stat_cpg_id='.$this->campaign['id'].'
		';

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}
		$msg = ria_mysql_fetch_assoc($res);
		return $msg['msg'];
	}

	/** Cette fonction permet d'ajouter des stats pour un utilisateur pour une campagne et un channel
	 * ( Attention self::channel et self::campaign doivent être initialisé )
	 *
	 * @param $user         Tableau avec les informations d'un utilisateur, au moin ces clé doivent
	 *                      être présente
	 *                      -'id', 'email', 'mobile', 'lng_code'
	 *
	 * @return bool|int
	 * @throws Exception
	 */
	public function addStat( $user ){
		if( !isset( $this->campaign ) ){
			throw new Exception( 'Campaign most be set to use this method' );
		}
		if( !isset( $this->channel ) ){
			throw new Exception( 'Channel most be set to use this method' );
		}
		if( !ria_array_key_exists( array( 'id', 'email', 'mobile', 'lng_code' ), $user ) ){
			throw new Exception( 'One or more keys are missing from the array user' );
		}
		global $config;
		if( trim($this->campaign['period']) == '' ){
			$sent = 'now()';
		}else{
			$sent = '"'.$this->campaign['period'].' '.$this->campaign['period_info'].':00"';
		}
		$data = array(
			'stat_tnt_id'    => $config['tnt_id'],
			'stat_cpg_id'    => $this->campaign['id'],
			'stat_chl_id'    => $this->channel['id'],
			'stat_date_sent' => $sent,
			'stat_email'     => '"'.$user['email'].'"',
			'stat_mobile'    => '"'.$user['mobile'].'"',
			'stat_usr_id'    => $user['id'],
			'stat_sms_id'    => $user['sms_id'],
		);

		if( isset( $user['obj_id'] ) ){
			$data['stat_obj_id'] = $user['obj_id'];
		}

		if( isset( $user['cls_id'] ) ){
			$data['stat_cls_id'] = $user['cls_id'];
		}

		if( isset( $user['fld_id'] ) ){
			$data['stat_fld_id'] = $user['fld_id'];
		}

		$sql = '
			insert into stats_campaigns
				('.implode( ',', array_keys( $data ) ).')
			values
				('.implode( ',', array_values( $data ) ).')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	public function delStats(){
		if( !isset( $this->campaign ) ){
			throw new Exception( 'Campaign most be set to use this method' );
		}

		global $config;

		$sql = '
			delete from stats_campaigns
			where stat_tnt_id='.$config['tnt_id'].'
				and stat_cpg_id='.$this->campaign['id'].'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	public function delStat($stat_id){

		if( !is_numeric($stat_id) || $stat_id <= 0 ){
			return false;
		}
		if( !isset( $this->campaign ) ){
			throw new Exception( 'Campaign most be set to use this method' );
		}

		global $config;

		$sql = '
			delete from stats_campaigns
			where stat_tnt_id='.$config['tnt_id'].'
				and stat_cpg_id='.$this->campaign['id'].'
				and stat_id='.$stat_id.'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}
}
create table if not exists sms_partners (
	ptn_id int(10) not null auto_increment,
	ptn_name varchar(75) not null,
	ptn_desc text not null,
	ptn_url varchar(100) not null,
	ptn_date_created datetime null,
	ptn_api_params text not null,
	ptn_date_modified timestamp null default current_timestamp on update current_timestamp,
	ptn_date_deleted datetime null,
	primary key (ptn_id)
)engine=InnoDB;

create table if not exists sms_partners_tenants (
	ptt_tnt_id int(10) unsigned not null,
	ptt_ptn_id int(10) unsigned not null,
	ptt_is_active  tinyint(1) not null,
	ptt_date_activated datetime null,
	primary key (ptt_tnt_id, ptt_ptn_id),
	key (ptt_tnt_id, ptt_ptn_id)
)engine=InnoDB;

create table if not exists sms_partners_quota (
	qta_tnt_id int(10) unsigned not null,
	qta_id int(10) not null auto_increment,
	qta_ptn_id int(10) unsigned not null,
	qta_qte decimal(14,4) not null,
	qta_qte_use decimal(14,4) not null,
	qta_date_credited datetime null,
	qta_date_expired datetime null,
	qta_date_modified datetime null,
	qta_date_deleted datetime null,
	primary key (qta_tnt_id, qta_ptn_id, qta_id),
	key (qta_id)
)engine=InnoDB;

create table if not exists mkt_campaigns (
	cpg_tnt_id int(10) unsigned not null,
	cpg_id int(10) not null auto_increment,
	cpg_title varchar(75) not null,
	cpg_desc text not null,
	cpg_date_start datetime null,
	cpg_date_end datetime null,
	cpg_type enum('NOW', 'DIFF') null,
	cpg_period enum('day','week','month') null,
	cpg_period_info varchar(5) not null,
	cpg_date_created datetime not null,
	cpg_date_modified timestamp null default current_timestamp on update current_timestamp,
	cpg_date_deleted datetime null,
	primary key (cpg_tnt_id, cpg_id),
	foreign key (cpg_tnt_id) references tnt_tenants(tnt_id),
	key (cpg_id)
)engine=InnoDB;

create table if not exists mkt_symbols (
	sym_code varchar(50) not null,
	sym_title varchar(75) not null,
	sym_desc text not null,
	sym_cls_id int(10) unsigned not null,
	sym_fld_id int(10) unsigned not null,
	primary key (sym_code),
	foreign key (sym_cls_id) references fld_classes(cls_id),
	foreign key (sym_fld_id) references fld_fields(fld_id)
)engine=InnoDB;

create table if not exists mkt_triggers (
	trg_tnt_id int(10) unsigned not null,
	trg_id int(10) not null auto_increment,
	trg_action enum('ADD', 'DEL', 'UPD', 'SELECT', 'CONTROL') not null,
	trg_cls_id int(10) unsigned not null,
	trg_obj_id_0 int(10) unsigned not null,
	trg_obj_id_1 int(10) unsigned not null,
	trg_obj_id_2 int(10) unsigned not null,
	trg_fld_id int(10) unsigned not null,
	trg_sym_code varchar(50) not null,
	trg_value varchar(75) not null,
	trg_date_created datetime null,
	trg_date_modified timestamp null default current_timestamp on update current_timestamp,
	trg_date_deleted datetime null,
	primary key (trg_tnt_id, trg_id),
	foreign key (trg_tnt_id) references tnt_tenants(tnt_id),
	foreign key (trg_cls_id) references fld_classes(cls_id),
	foreign key (trg_fld_id) references fld_fields(fld_id),
	key ( trg_id )
)engine=InnoDB;

create table if not exists mkt_triggers_groups (
	mtg_tnt_id int(10) unsigned not null,
	mtg_id int(10) not null auto_increment,
	mtg_cpg_id int(10) unsigned not null,
	mtg_rule enum('and', 'or') not null,
	mtg_rule_item enum('all','any') not null,
	primary key (mtg_tnt_id, mtg_id),
	key(mtg_id, mtg_cpg_id)
)engine=InnoDB;

create table if not exists mkt_triggers_conditions (
	mtc_tnt_id int(10) unsigned not null,
	mtc_grp_id int(10) unsigned not null,
	mtc_trg_id int(10) unsigned not null,
	mtc_cpg_id int(10) unsigned not null,
	primary key (mtc_tnt_id, mtc_grp_id, mtc_trg_id),
	key (mtc_tnt_id, mtc_grp_id, mtc_trg_id, mtc_cpg_id)
)engine=InnoDB;

create table if not exists mkt_channels (
	chl_tnt_id int(10) unsigned not null,
	chl_id int(10) not null auto_increment,
	chl_wst_id int(10) unsigned not null,
	chl_type enum('EMAIL', 'SMS') not null,
	chl_email_from varchar(75) not null,
	chl_email_bcc text null,
	chl_content text not null,
	chl_ptn_id int(10) unsigned not null,
	chl_lng_code varbinary(5) not null default 'fr',
	chl_cpg_id int(10) unsigned not null,
	chl_date_created datetime null,
	chl_date_modified timestamp null default current_timestamp on update current_timestamp,
	chl_date_deleted datetime null,
	primary key (chl_tnt_id, chl_id ),
	key (chl_id, chl_tnt_id, chl_wst_id, chl_cpg_id, chl_ptn_id)
)engine=InnoDB;

create table if not exists mkt_campaigns_emails (
	cpe_tnt_id int(10) unsigned not null,
	cpe_id int(10) not null auto_increment,
	cpe_cpg_id int(10) unsigned not null,
	cpe_email varchar(75) not null,
	cpe_include tinyint(1) not null,
	primary key (cpe_tnt_id, cpe_id, cpe_cpg_id),
	key (cpe_id, cpe_tnt_id, cpe_cpg_id)
)engine=InnoDB;

create table if not exists mkt_campaigns_segments (
	cps_tnt_id int(10) unsigned not null,
	cps_cpg_id int(10) unsigned not null,
	cps_seg_id int(10) unsigned not null,
	cps_include tinyint(1) not null,
	primary key (cps_tnt_id, cps_cpg_id, cps_seg_id),
	key (cps_tnt_id, cps_cpg_id, cps_seg_id)
)engine=InnoDB;

create table if not exists mkt_campaigns_newsletter_cat (
	cpn_tnt_id int(10) unsigned not null,
	cpn_cpg_id int(10) unsigned not null,
	cpn_cnt_id int(10) unsigned not null,
	cpn_include tinyint(1) not null,
	primary key (cpn_tnt_id, cpn_cpg_id, cpn_cnt_id),
	key (cpn_tnt_id, cpn_cpg_id, cpn_cnt_id)
)engine=InnoDB;

create table if not exists mkt_campaigns_objects (
	mco_tnt_id int(10) unsigned not null,
	mco_cpg_id int(10) unsigned not null,
	mco_trg_id int(10) unsigned not null,
	mco_obj_id int(10) unsigned not null,
	mco_usr_id int(10) unsigned not null,
	mco_verified tinyint(1) not null,
	primary key (mco_tnt_id, mco_cpg_id, mco_trg_id, mco_obj_id, mco_usr_id),
	key (mco_tnt_id, mco_cpg_id, mco_trg_id, mco_usr_id)
)engine=InnoDB;

create table if not exists stats_campaigns(
	stat_tnt_id int(10) unsigned not null,
	stat_cpg_id int(10) unsigned not null,
	stat_chl_id int(10) unsigned not null,
	stat_email varchar(75) not null,
	stat_usr_id int(10),
	stat_mobile varchar(20),
	stat_obj_id int(10),
	stat_fld_id int(10),
	stat_cls_id int(10),
	stat_date_sent timestamp not null default current_timestamp,
	stat_is_received tinyint(2) not null,
	stat_is_consulted tinyint(2) not null,
	primary key (stat_tnt_id, stat_cpg_id, stat_chl_id, stat_email),
	key (stat_tnt_id, stat_cpg_id, stat_chl_id)
)engine=InnoDB;


INSERT INTO fld_fields
(fld_tnt_id, fld_name, fld_desc, fld_type_id, fld_old_txt_type, fld_cls_id, fld_pos, fld_is_sync, fld_is_physical, fld_physical_name, fld_is_system, fld_used_access, fld_use_constraint)
VALUES
(0,'Date d\'expiration','',1,1,36,0,0,1,'off_date_stop',0,0,0)

INSERT INTO fld_fields
(fld_tnt_id, fld_name, fld_desc, fld_type_id, fld_old_txt_type, fld_cls_id, fld_pos, fld_is_sync, fld_is_physical, fld_physical_name, fld_is_system, fld_used_access, fld_use_constraint)
VALUES
(0,'Statut de commande','',1,1,4,0,0,1,'ord_state_id',0,,0,0)

INSERT INTO `riashop`.`cfg_variables` (`var_code`, `var_name`, `var_desc`, `var_default`, `var_type_id`) VALUES ('marketing_is_active', 'Campagne marketing', 'Détermine si les campagne marketing son activé ou pas', '0', '8');

INSERT INTO `riashop`.`cfg_overrides` (`ovr_tnt_id`, `ovr_wst_id`, `ovr_usr_id`, `ovr_var_code`, `ovr_value`) VALUES ('14', '20', '0', 'marketing_is_active', '1');

INSERT INTO mkt_symbols (sym_code, sym_title, sym_desc, sym_cls_id, sym_fld_id) VALUES ('ORD_STATUS_CHANGED', 'Passe à ', 'Lorsque le statu de commande passe la valeur sélectionner une notification sera envoyer a l''utilisateur.', 4, 3922);
INSERT INTO mkt_symbols (sym_code, sym_title, sym_desc, sym_cls_id, sym_fld_id) VALUES ('PMT_EXPIRE_DAYS', 'Arrive à expiration dans', 'Tout les utilisateur possèdent un code promotion qui arrive a échéance dans ce nombre de jours serons notifier.', 36, 3920);
INSERT INTO mkt_symbols (sym_code, sym_title, sym_desc, sym_cls_id, sym_fld_id) VALUES ('PMT_EXPIRE_MINUS_DAYS', 'Arrive à expiration dans moins de', 'Tout les utilisateurs possèdent un code promotion qui arrive a échéance entre ce nombre de jours et le jour d''expiration serons notifier.', 36, 3920);
INSERT INTO mkt_symbols (sym_code, sym_title, sym_desc, sym_cls_id, sym_fld_id) VALUES ('PMT_EXPIRE_PLUS_DAYS', 'Arrive à expiration dans plus de', 'Tout les utilisateurs possèdent un code promotion qui arrive a échéance après le nombre de jours entré serons notifier.', 36, 3920);

INSERT INTO sms_partners (ptn_name, ptn_desc, ptn_url, ptn_api_params, ptn_date_created) VALUES ('ovh', 'OVH propose une vaste gamme de services IT pour les entreprises et les particuliers technophiles. Hébergement web, datacentres virtuels, serveurs dédiés, solutions de stockage ou encore connexions xDSL et VoIP, nos services bénéficient d’innovations permanentes et sont régulièrement enrichis de nouvelles fonctionnalités. ', 'https://www.ovh.com', '{
	"application_key": "gns8Txc2uXbiAXu5",
	"application_secret": "5CSx1bgTMqEiRutz8egSrq1e3XlY0jU9",
	"consumer_key": "o7zKKgn31MsEfc0ap1boNwPQm3qLAszd",
	"endpoint": "ovh-eu"
}', now() );

INSERT INTO sms_partners_tenants (ptt_tnt_id, ptt_ptn_id, ptt_is_active, ptt_date_activated) VALUES (14, 1, 1, now());

INSERT INTO sms_partners_quota (qta_tnt_id, qta_ptn_id, qta_qte, qta_qte_use, qta_date_credited, qta_date_expired, qta_date_modified, qta_date_deleted) VALUES (14, 1, 1000.0000, 105.0000, '2016-09-20 11:59:23', '2116-09-20 11:59:23', null, null);
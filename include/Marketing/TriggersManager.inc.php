<?php
require_once( 'Marketing/Symbols.inc.php' );

/** Cette classe permet la gestion des trigger ou évènement qui peuvent déclancher une campagne
 *  \ingroup mkt
 */
class TriggersManager {

	public static $ACTIONS_TYPES = array(
		'ADD',
		'DEL',
		'UPD',
		'SELECT',
		'CONTROL'
	);

	/** Cette fonction permet de récupérer les triggers
	 * @param  $id Optionnel, identifiant du trigger
	 * @param  $grp_id Optionnel, identifiant du groupe de triggers
	 * @param  $action Optionnel, action possible, valeur accepter 'ADD', 'DEL', 'UPD', 'SELECT', 'CONTROL'
	 * @param  $cls_id Optionnel, identifiant de la classe
	 * @param  $obj Optionnel, identifiant d'un objet ou tableau d'identifiants d'objets (max 3 identifiants)
	 * @param  $fld_id Optionnel, identifiant du champ avancé
	 * @param  $sym_code Optionnel code du symbole
	 *
	 * @return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les colonnes suivantes :
	 *            - tnt_id : identifiant du tenant
	 *            - id : identifiant du trigger
	 *            - action : action que va surveiller le trigger
	 *            - cls_id : identifiant de la class
	 *            - obj_id_0 : identifiant d'un objet
	 *            - obj_id_1 : identifiant d'un objet
	 *            - obj_id_2 : identifiant d'un objet
	 *            - fld_id : identifiant du champ avancé
	 *            - sym_code : code du symbole
	 *            - value : la valeur de la condition
	 */
	public static function getTriggers(
		$id = 0,
		$grp_id = 0,
		$action = '',
		$cls_id = 0,
		$obj = 0,
		$fld_id = 0,
		$sym_code = ''
	){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		if( !is_numeric( $grp_id ) || $grp_id < 0 ){
			return false;
		}

		if( $action != '' && ( !is_string( $action ) || !in_array( strtoupper( $action ), self::$ACTIONS_TYPES ) ) ){
			return false;
		}

		if( !is_numeric( $cls_id ) || $cls_id < 0 ){
			return false;
		}
		if( $obj ){
			if( is_numeric( $obj ) ){
				$obj = array( $obj );
			}elseif( !is_array( $obj ) ){
				return false;
			}

			if( $obj = control_array_integer( $obj, true ) ){
				return false;
			}
		}

		if( !is_numeric( $fld_id ) || $fld_id < 0 ){
			return false;
		}

		if( $sym_code && !self::existSymbols( $sym_code ) ){
			return false;
		}

		global $config;

		$sql = '
			select trg_tnt_id as tnt_id, trg_id as id, trg_action as action, trg_cls_id as cls_id, trg_obj_id_0 as obj_id_0,
			trg_obj_id_1 as obj_id_1, trg_obj_id_2 as obj_id_2, trg_fld_id as fld_id, trg_sym_code as sym_code, trg_value as value
			';
		if( $grp_id ){
			$sql .= ', mtc_grp_id as grp_id, mtc_cpg_id as cpg_id';
		}
		$sql .= '
			from mkt_triggers
		';

		if( $grp_id ){
			$sql .= 'left join mkt_triggers_conditions on (mkt_triggers.trg_tnt_id = mkt_triggers_conditions.mtc_tnt_id and mkt_triggers.trg_id = mkt_triggers_conditions.mtc_trg_id)';
		}

		$sql .= '
			where trg_tnt_id = '.$config['tnt_id'].'
				and trg_date_deleted is null
		';

		if( $id ){
			$sql .= '
				and trg_id = '.$id.'
			';
		}

		if( $grp_id ){
			$sql .= ' and mtc_grp_id = '.$grp_id;
		}

		if( $action != '' ){
			$sql .= ' and trg_action = "'.strtoupper( $action ).'"';
		}

		if( $cls_id ){
			$sql .= '
				and trg_cls_id = '.$cls_id.'
			';
		}
		if( $obj ){
			if( isset( $obj[0] ) ){
				$sql .= ' and trg_obj_id_0 = '.$obj[0];
			}
			if( isset( $obj[1] ) ){
				$sql .= ' and trg_obj_id_1 = '.$obj[1];
			}
			if( isset( $obj[2] ) ){
				$sql .= ' and trg_obj_id_2 = '.$obj[2];
			}
		}

		if( $fld_id ){
			$sql .= '
				and trg_fld_id = '.$fld_id.'
			';
		}

		if( $sym_code != '' ){
			$sql .= ' and trg_sym_code = '.$sym_code;
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter un trigger
	 *	@param $action Action sur laquel s'applique le trigger valeur accepté : ADD', 'DEL', 'UPD', 'SELECT', 'CONTROL'
	 *	@param int $cls_id Optionnel, identifiant de la classe
	 *	@param $obj Optionnel, identifiant de l'objet ou tableau d'identifiants d'objets maximum 3
	 *	@param $fld_id Optionnel, identifiant du champ avancé
	 *	@param $sym_code Optionnel, le code su symbole a utiliser
	 *	@param $value Optionnel, une valeur sur la quel filtrer
	 *	@return int l'identifiant attribué au trigger en cas de succès, false en cas d'échec
	 */
	public static function addTriggers(
		$action,
		$cls_id = 0,
		$obj = 0,
		$fld_id = 0,
		$sym_code = false,
		$value = false
	){
		if( !is_string( $action ) || !in_array( strtoupper( $action ), self::$ACTIONS_TYPES ) ){
			return false;
		}

		if( !is_numeric( $cls_id ) || $cls_id <= 0 ){
			return false;
		}

		if( $obj ){
			if( is_numeric( $obj ) ){
				$obj = array( $obj );
			}elseif( !is_array( $obj ) ){
				return false;
			}

			if( !$obj = control_array_integer( $obj, true ) ){
				return false;
			}
		}

		if( !is_numeric( $fld_id ) || $fld_id < 0 ){
			return false;
		}

		if( $sym_code && !self::existSymbols( $sym_code ) ){
			return false;
		}

		global $config;

		$data = array(
			'trg_tnt_id'       => $config['tnt_id'],
			'trg_action'       => '"'.strtoupper( $action ).'"',
			'trg_date_created' => 'now()'
		);

		if( $cls_id ){
			$data['trg_cls_id'] = $cls_id;
		}
		if( $fld_id ){
			$data['trg_fld_id'] = $fld_id;
		}

		if( $obj ){
			if( isset( $obj[0] ) ){
				$data['trg_obj_id_0'] = $obj[0];
			}
			if( isset( $obj[1] ) ){
				$data['trg_obj_id_1'] = $obj[1];
			}
			if( isset( $obj[2] ) ){
				$data['trg_obj_id_2'] = $obj[2];
			}
		}

		if( $sym_code ){
			$data['trg_sym_code'] = '"'.$sym_code.'"';
		}

		if( $value ){
			$data['trg_value'] = '"'.addslashes( $value ).'"';
		}

		$keys = implode( ', ', array_keys( $data ) );
		$values = implode( ', ', array_values( $data ) );

		$sql = '
			insert into mkt_triggers
				( '.$keys.')
			values
				( '.$values.')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet de modifier un trigger
	 * @param  $id Identifiant du trigger
	 * @param  $action action possible, valeur accepter 'ADD', 'DEL', 'UPD', 'SELECT', 'CONTROL'
	 * @param  $cls_id  Optionnel, identifiant de la classe
	 * @param  $obj Optionnel, identifiant de l'objet ou tableau d'identifiants d"objet
	 * @param  $fld_id Optionnel, identifiant deu champ avancé
	 * @param  $sym_code Optionnel, code du symbole
	 * @param  $value Optionnel, valeur sur la quel filtrer
	 *
	 * @return int l'identifiant du trigger modifier, false si échec
	 */
	public static function updateTriggers(
		$id,
		$action,
		$cls_id = 0,
		$obj = 0,
		$fld_id = 0,
		$sym_code = false,
		$value = false
	){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		if( !is_string( $action ) || !in_array( strtoupper( $action ), self::$ACTIONS_TYPES ) ){
			return false;
		}

		if( !is_numeric( $cls_id ) || $cls_id <= 0 ){
			return false;
		}

		if( $obj ){
			if( is_numeric( $obj ) ){
				$obj = array( $obj );
			}elseif( !is_array( $obj ) ){
				return false;
			}

			if( !$obj = control_array_integer( $obj, true ) ){
				return false;
			}
		}

		if( !is_numeric( $fld_id ) || $fld_id < 0 ){
			return false;
		}

		if( $sym_code && !self::existSymbols( $sym_code ) ){
			return false;
		}

		global $config;

		$data = array(
			'trg_tnt_id = '.$config['tnt_id'],
			'trg_id = '.$id,
			'trg_action = "'.strtoupper( $action ).'"',
			'trg_date_created = now()'
		);

		if( $cls_id ){
			$data[] = 'trg_cls_id = '.$cls_id;
		}
		if( $fld_id ){
			$data[] = 'trg_fld_id = '.$fld_id;
		}

		if( $obj ){
			if( isset( $obj[0] ) ){
				$data[] = 'trg_obj_id_0 = '.$obj[0];
			}
			if( isset( $obj[1] ) ){
				$data[] = 'trg_obj_id_1 = '.$obj[1];
			}
			if( isset( $obj[2] ) ){
				$data[] = 'trg_obj_id_2 = '.$obj[2];
			}
		}

		if( $sym_code ){
			$data[] = 'trg_sym_code = '.$sym_code;
		}

		if( $value ){
			$data[] = 'trg_value = "'.addslashes( $value ).'"';
		}

		$sql = '
			update mkt_triggers set
			'.implode( ', ', $data ).'
			where trg_tnt_id = '.$config['tnt_id'].'
				and trg_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}

	/**	Cette fonction permet de supprimmer un trigger
	 *	@param int $id Identifiant du trigger à supprimer
	 *
	 *	@return int l'identifiant du trigger supprimer, false si échec
	 */
	public static function delTrigger( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			update mlt_triggers set
			trg_date_deleted = now();
			where trg_tnt_id = '.$config['tnt_id'].'
				and trg_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}

	/** Cette fonction permet de récupérer les groupe de trigger
	 * @param  $id Optionnel, identifiant du groupe
	 * @param  $cpg_id Optionnel, idnetifiant de la campagne
	 * @param  $rule optionnel, la condition avec un autre groupe 'and' ou 'or'
	 * @param  $rule_item Optionnel, si toutes les condition doivent être résolues ou au moins une
	 *
	 * @return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonnes suivantes :
	 *                     - tnt_id : identifiant du tenant
	 *                     - id : identifiant du groupe
	 *                     - cpg_id : identifiant de la campagne
	 *                     - rule : la regle de condition pour ce groupe
	 *                     - rule_item : la rêgle entre les conditions du groupe
	 */
	public static function getTriggersGroups(
		$id = 0,
		$cpg_id = 0,
		$rule = false,
		$rule_item = false
	){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		if( !is_numeric( $cpg_id ) || $cpg_id < 0 ){
			return false;
		}

		if( $rule && ( !is_string( $rule ) || !in_array( strtolower( $rule ), array(
					'and',
					'or'
				) ) )
		){
			return false;
		}

		if( $rule && $rule_item && ( !is_string( $rule_item ) || !in_array( strtolower( $rule_item ), array(
					'all',
					'any'
				) ) )
		){
			return false;
		}

		global $config;

		$sql = '
			select mtg_tnt_id as tnt_id, mtg_id as id, mtg_cpg_id as cpg_id, mtg_rule as rule, mtg_rule_item as rule_item
			from mkt_triggers_groups
			where mtg_tnt_id = '.$config['tnt_id'].'
		';

		if( $id ){
			$sql .= ' and mtg_id = '.$id;
		}

		if( $cpg_id ){
			$sql .= ' and mtg_cpg_id = '.$cpg_id;
		}

		if( $rule ){
			$sql .= ' and mtg_rule = "'.addslashes( $rule ).'"';
		}

		if( $rule_item ){
			$sql .= ' and mtg_rule_item = "'.addslashes( $rule_item ).'"';
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter un groupe
	 * @param $cpg_id Identifiant de la campagne
	 * @param $rule Régle entre les groupes valeur accepté 'and' ou 'or'
	 * @param $rule_item rêgle entre les conditions 'all' ou 'any'
	 *
	 * @return int l'identifiant du groupe créé, false si échec
	 */
	public static function addTriggersGroups( $cpg_id, $rule, $rule_item ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_string( $rule ) || !in_array( strtolower( $rule ), array( 'and', 'or' ) ) ){
			return false;
		}

		if( !is_string( $rule_item ) || !in_array( strtolower( $rule_item ), array(
				'all',
				'any'
			) )
		){
			return false;
		}

		global $config;

		$sql = '
			insert into mkt_triggers_groups
				(mtg_tnt_id, mtg_cpg_id, mtg_rule, mtg_rule_item)
			values
				('.$config['tnt_id'].', '.$cpg_id.', "'.addslashes( $rule ).'", "'.addslashes( $rule_item ).'")
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet de modifier un groupe de condition
	 * @param int $id Identifiant du groupe
	 * @param $cpg_id Identifiant de la campagne
	 * @param $rule Régle entre les groupes valeur accepté 'and' ou 'or'
	 * @param $rule_item rêgle entre les conditions 'all' ou 'any'
	 *
	 * @return bool true si succès, false en cas d'échec
	 */
	public static function updateTriggersGroups( $id, $cpg_id, $rule, $rule_item ){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		if( !is_numeric( $cpg_id ) || $cpg_id < 0 ){
			return false;
		}

		if( !is_string( $rule ) || !in_array( strtolower( $rule ), array( 'and', 'or' ) ) ){
			return false;
		}

		if( !is_string( $rule_item ) || !in_array( strtolower( $rule_item ), array(
				'all',
				'any'
			) )
		){
			return false;
		}

		global $config;

		$sql = '
			update mkt_triggers_groups set
			mkt_rule = '.$rule.'
			mkt_rule_item = '.$rule_item.'
			where mlt_tnt_id = '.$config['tnt_id'].'
				and mkt_cpg_id = '.$cpg_id.'
				and mkt_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer
	 * @param int $id Identifiant du groupe
	 *
	 * @return bool true si succès, false si échec
	 */
	public static function delTriggersGroups( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from mkt_triggers_groups
			where mtg_tnt_id = '.$config['tnt_id'].'
				and mtg_id = '.$id.'
		';
		$res = ria_mysql_query( $sql );
		$sql2 = '
			delete from mkt_triggers_conditions
			where mtc_tnt_id= '.$config['tnt_id'].'
				and mtc_grp_id= '.$id.'
		';
		$res2 = ria_mysql_query( $sql2 );

		if( !$res && !$res2 ){
			return false;
		}

		return true;
	}

	/** cette fonction permet d'ajouter des triggers a un groupe de trigger pour une campagne
	 *
	 * @param $grp    Idnetifiant du groupe
	 * @param $trg    Identifiant du trigger
	 * @param $cpg    Identifiant de la campagne
	 *
	 * @return bool true si réussit, false si échec
	 */
	public static function addTriggerCondition( $grp, $trg, $cpg ){
		{
			if( !is_numeric( $grp ) || $grp < 0 ){
				return false;
			}
			if( !is_numeric( $trg ) || $trg < 0 ){
				return false;
			}
			if( !is_numeric( $cpg ) || $cpg < 0 ){
				return false;
			}
			global $config;

			$sql = '
			insert into mkt_triggers_conditions
				(mtc_tnt_id, mtc_grp_id, mtc_trg_id, mtc_cpg_id)
			values
				('.$config['tnt_id'].', '.$grp.', '.$trg.', '.$cpg.')
		';

			$res = ria_mysql_query( $sql );

			if( !$res ){
				return false;
			}

			return true;
		}
	}

	public static function delTriggerCondition(  $grp, $trg, $cpg ){
		if( !is_numeric( $grp ) || $grp < 0 ){
			return false;
		}
		if( !is_numeric( $trg ) || $trg < 0 ){
			return false;
		}
		if( !is_numeric( $cpg ) || $cpg < 0 ){
			return false;
		}
		global $config;
		$sql = '
			delete from mkt_triggers_conditions
			where mtc_tnt_id= '.$config['tnt_id'].'
				and mtc_grp_id= '.$grp.'
				and mtc_trg_id= '.$trg.'
				and mtc_cpg_id= '.$cpg.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de vérifier l'existantce d'un symbole
	 * @param $code Code du symbole
	 *
	 * @return bool true si le symbole existe, false dans le cas contraire
	 */
	public static function existSymbols( $code ){
		if( !is_string( $code ) || trim( $code ) == '' ){
			return false;
		}

		$sql = '
			select 1 from mkt_symbols
			where sym_code = "'.$code.'"
		';

		return ria_mysql_num_rows( ria_mysql_query( $sql ) ) > 0;
	}

	/** Cette fonction permet de retourné le nom d'un trigger
	 *
	 * @param $id
	 *
	 * @return bool|string
	 */
	public static function getTriggerName( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}
		switch( $id ){
			case 1:
				$name = 'Lorsqu\'un code pormotion arrive a échéance.';
				break;
		}

		return $name;
	}
}

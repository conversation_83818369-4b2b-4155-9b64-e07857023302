<?php

class Symbols{

	/** Cette fonction permet de récupérer les symboles
	 * \param  $code Optionnel, code du symbol
	 * \param  $cls_id Optionnel, identifiant de la classe
	 * \param  $fld_id Optionnel, identifiant du champ avancé
	 *
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les colonne suivante :
	 *                     - code : code utiliser pour le symbol
	 *                     - title : titre du symbole
	 *                     - desc : description de la fonction du symbole
	 *                     - cls_id : identifiant de la classe
	 *                     - fld_id : identifiant du champ avancé
	 */
	public static function getSymbols( $code='', $cls_id=0, $fld_id=0 ){
		if( !is_string($code) ){
			return false;
		}

		if( !is_numeric($cls_id) || $cls_id<0 ){
			return false;
		}

		if( !is_numeric($fld_id) || $fld_id<0 ){
			return false;
		}

		$sql = '
			select sym_code as code, sym_title as title, sym_desc as desc, sym_cls_id as cls_id, sym_fld_id as fld_id
			from mkt_symbols
		';

		$cond = array();

		if( trim($code) != '' ){
			$cond[] = 'sym_code='.$code.' ';
		}

		if( $cls_id ){
			$cond[] = 'sym_cls_id='.$cls_id.' ';
		}

		if( $fld_id ){
			$cond[] = 'sym_fld_id='.$fld_id.' ';
		}

		if( sizeof($cond) ){
			$sql .= '
				where '.implode(' and ', $cond).'
			';
		}

		$res = ria_mysql_query($sql);

		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		return $res;
	}
	/** Cette fonction permet d'ajouter des symboles
	 * \param  $code   Code du symbole
	 * \param  $title  titre du symbole
	 * \param  $desc   Descritpion de la fonction du symbole pour l'utilisateur
	 * \param $cls_id Optionnel, identifiant de la classe
	 * \param $fld_id Optionnel, identifiant du champ avancé
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function addSymbols( $code, $title, $desc, $cls_id=0, $fld_id=0 ){
		if( !is_string($code) || trim($code) == '' ){
			return false;
		}

		if( !is_string($title) || trim($title) == '' ){
			return false;
		}

		if( !is_string($desc) || trim($desc) == '' ){
			return false;
		}

		if( !is_numeric($cls_id) || $cls_id < 0 ){
			return false;
		}

		if( !is_numeric($fld_id) || $fld_id < 0 ){
			return false;
		}

		$sql = '
			insert into mkt_symbols
			(sym_code, sym_title, sym_desc'.($cls_id ? ', sym_cls_id' : '').($fld_id ? ', sym_fld_id' : '').' )
			values
			("'.$code.'", "'.$title.'", "'.$desc.'"'.($cls_id ? ', '.$cls_id.' ' : '').($fld_id ? ', '.$fld_id.' ' : '').')
		';

		$res = ria_mysql_query($sql);

		if( !$res )
			return false;

		return true;
	}
	/** Cette fonction permet la modifiaction d'un symbole
	 * \param  $code   code du symbole
	 * \param  $title  titre du symbole
	 * \param  $desc   description du symbole
	 * \param  $cls_id Identifiant de la classe
	 * \param  $fld_id identifiant du champ avancé
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function updateSymbols( $code, $title, $desc, $cls_id=0, $fld_id=0 ){
		if( !is_string($code) || $code == '' ){
			return false;
		}

		if( !is_string($title) || trim($title)=='' ){
			return false;
		}

		if( !is_string($desc) || trim($desc)=='' ){
			return false;
		}

		if( !is_numeric($cls_id) || $cls_id<0 ){
			return false;
		}

		if( !is_numeric($fld_id) || $fld_id<0 ){
			return false;
		}

		$sql = '
			update mkt_symbols set
		';

		$data = array(
			'sym_title = "'.$title.'"',
			'sym_desc = "'.$desc.'"'
			);

		if( $cls_id ){
			$data[] = 'sym_cls_id = '.$cls_id;
		}

		if( $fld_id ){
			$data[] = 'sym_fld_id = '.$fld_id;
		}

		$sql .= implode( ', ', $data );

		$sql .= '
			where sym_code = "'.$code.'"
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonciton permet de supprimer un symbole
	 * \param $code COde du symbole
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function delSymbols( $code ){
		if( !is_string($code) || trim($code) == '' ){
			return false;
		}

		$sql = '
			delete from mkt_symbols
			where sym_code = "'.$code.'"
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return true;
	}
	/** Cette fonction permet de vérifier l'existantce d'un symbole
	 * \param $code Code du symbole
	 *
	 * \return true si le symbole existe, false dans le cas contraire
	 */
	public static function existSymbols( $code ){
		if( !is_string($code) || trim($code) == '' ){
			return false;
		}

		$sql = '
			select 1 from mkt_symbols
			where sym_code = "'.$code.'"
		';

		return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
	}
}
<?php
/**	\defgroup mkt Campagnes Marketing
 *	Ce module comprend classes nécessaires à la gestion des Campagnes Marketing
 */

/**	\defgroup mkt_mdl Modèles pour les campagnes marketing
 *	\ingroup mkt
 *	Ce module comprend les classes pour les campagnes et les trigggers
 */
/**	\defgroup mkt_campaigns Campagnes
 *	\ingroup mkt
 *	Ce module comprend la classe Campaigns qui gère l'exécution d'une campagne
 *	@{
 */
require_once( 'Marketing/CampaignsManager.inc.php' );
require_once( 'Marketing/models/Triggers.inc.php' );
require_once( 'Marketing/TriggersManager.inc.php' );
require_once( 'Marketing/Channels.inc.php' );
require_once( 'Marketing/SMS_Partners.inc.php' );
require_once( 'Marketing/StatCampaign.inc.php' );
require_once( 'email.inc.php' );

/**	\class Campaigns
 *	\brief Cette classe permet l'éxécution d'une campagne marketing
 */
class Campaigns {

	/**
	 * Identifiant de la campagne
	 *
	 *	@var array $props
	 */
	public $props = array();

	/**
	 * Tableau contenant tous les utilisateurs
	 *
	 *	@var array $users
	 */
	private $users = array();

	/**
	 * Tableau contenant tous les emails
	 *
	 *	@var array $usersEmails
	 */
	private $usersEmails = array();
	/**
	 * Tableau contenant les numéros mobiles des utilisateurs
	 *
	 *	@var array $usersMobileNumber
	 */
	private $usersMobileNumber = array();
	/**
	 * Tableau contenant les utilisateurs à inclure
	 *
	 *	@var array $usersInclude
	 */
	public $usersInclude = array();
	/**
	 * Tableau contenant les utilisateur à exlcure
	 *
	 *	@var array $usersExclude
	 */
	public $usersExclude = array();
	/**
	 * Tableau contenant les utilisateur à inclure
	 *
	 *	@var array $mailsInclude
	 */
	public $mailsInclude = array();
	/**
	 * Tableau contenant les mails à exclure
	 *
	 *	@var array $mailsExclude
	 */
	public $mailsExclude = array();


	/**
	 * Campaigns constructor.
	 *
	 * @param $campaign         array Tableau associatif contenant les clés suivantes :
	 *                          - id
	 *                          - title
	 *                          - desc
	 *                          - type
	 *                          - date_start
	 *                          - date_end
	 *                          - period
	 *                          - period_info
	 *                          - date_created
	 *
	 * @throws Exception si clés du tableau ne correspondent pas
	 */
	public function __construct( array $campaign ){
		if( !ria_array_key_exists( array(
			'id',
			'title',
			'type',
			'desc',
			'date_start',
			'date_end',
			'period',
			'period_info',
			'date_created'
		), $campaign )
		){
			throw new Exception( 'Not all the parameters are entered' );
		}


		$this->props = $campaign;
	}

	/** Cette fonction permet de paramétrer les utilisateurs en excluant les utilisateurs exclus
	 */
	private function setUsers(){
		foreach($this->usersExclude as $usr_id => $usr ){
			if( array_key_exists($usr_id, $this->usersInclude) ){
				unset($this->usersInclude[$usr_id]);
			}
		}
		return $this->users = $this->usersInclude;
	}

	/** Permet d'ajouter des utilisateurs
	 * \param $users tableua avec les informations du client suivante :
	 *            - id : identifiant de l'utilisateur
	 *            - email : addresse email de l'utilislateur
	 *            - mobile : numéro du mobile de l'utilisateur
	 *            - lng_code : code iso du pays de l'utilisateur
	 *            // si trigger
	 *            - obj_id : identifiant de l'objet lié a l'utilisatuer
	 *            - fld_id : identifiant du champ lié a l'utilisateur
	 */
	private function addUsers( array $users ){
		$this->users += $users;
	}

	/** Permet d'ajouter un utilisateur a inclure
	 * \param $users tableua avec les informations du client suivante :
	 *            - id : identifiant de l'utilisateur
	 *            - email : addresse email de l'utilislateur
	 *            - mobile : numéro du mobile de l'utilisateur
	 *            - lng_code : code iso du pays de l'utilisateur
	 *            // si trigger
	 *            - obj_id : identifiant de l'objet lié a l'utilisatuer
	 *            - fld_id : identifiant du champ lié a l'utilisateur
	 */
	private function setUsersIn( $usersIn ){
		$this->usersInclude += $usersIn;
	}

	/** permet  d'ajouter un utilisateur a exclure
	 * \param $users tableua avec les informations du client suivante :
	 *            - id : identifiant de l'utilisateur
	 *            - email : addresse email de l'utilislateur
	 *            - mobile : numéro du mobile de l'utilisateur
	 *            - lng_code : code iso du pays de l'utilisateur
	 *            // si trigger
	 *            - obj_id : identifiant de l'objet lié a l'utilisatuer
	 *            - fld_id : identifiant du champ lié a l'utilisateur
	 */
	private function setUsersOut( $usersOut ){
		$this->usersExclude += $usersOut;
	}

	/** Permet d'ajouter un email a liste des inclusions
	 * \param $mailsIn addresse email
	 */
	public function setEmailIn( $mailsIn ){
		array_push( $this->mailsInclude, $mailsIn );
	}

	/** Permet d'ajouter un email a liste des exclusions
	 * \param $mailsOut Obligatoire, addresse email à exclure
	 */
	public function setEmailOut( $mailsOut ){
		array_push( $this->mailsExclude, $mailsOut );
	}

	/** Retourne les utilisateurs de la campagne
	 * \return un tableau de client avec les informations du client suivante :
	 *            - id : identifiant de l'utilisateur
	 *            - email : addresse email de l'utilislateur
	 *            - mobile : numéro du mobile de l'utilisateur
	 *            - lng_code : code iso du pays de l'utilisateur
	 *            // si trigger
	 *            - obj_id : identifiant de l'objet lié a l'utilisatuer
	 *            - fld_id : identifiant du champ lié a l'utilisateur
	 */
	public function getUsers(){
		return $this->users;
	}

	/** Retourne les utilisateur de la liste des inclues
	 * \return retourne un tableau avec les utilisateurs inclues
	 */
	public function getUsersIn(){
		return $this->usersInclude;
	}

	/** Retourne les utilisateur de la liste des exclues
	 * \return retourne un tableau avec les utilisateurs exclues
	 */
	public function getUsersOut(){
		return $this->usersExclude;
	}

	/** Permet de vérifier si des utilisateurs sont présent
	 *
	 * \return true si il y a des utilisateurs, false si non.
	 */
	public function checkIfUsers(){
		return sizeof( $this->users ) > 0;
	}

	/** Récupère les emails des utilisateur plus les emails qui ne correspondent pas a un tuilisateur
	 * \return retourne un tableau contenant chaque email
	 */
	public function getUsersEmails(){
		$emails = $this->users;
		$allowedEmails = array();
		foreach( $this->mailsInclude as $include ){
			$emails[] = array(
				'id'       => null,
				'email'    => $include,
				'mobile'   => null,
				'lng_code' => null
			);
		}
		foreach( $emails as $email ){
			if( !in_array( $email['email'], $this->mailsExclude ) ){
				$allowedEmails[] = $email;
			}
		}

		return $allowedEmails;
	}

	/** Récupère les que les numéros mobile français et les formate au format international
	 * \return    retourne un tableau avec les numéros mobile français
	 *          au format internationnal +33 xxxxxxxxx
	 */
	public function getUsersMobile(){
		$mobile = array();
		$uniqPhones = array();
		foreach( $this->users as $user ){
			if( $user['lng_code'] === 'fr' ){
				$cellphone = false;
				if( isset($user['phone']) && trim( $user['phone'] ) !== '' && !is_null( $user['phone'] ) ){
					$cellphone = conv_france_international_number( $user['phone'] );
				}
				elseif(!$cellphone && trim( $user['mobile'] ) !== '' && !is_null( $user['mobile'] )){
					$cellphone = conv_france_international_number( $user['mobile'] );
				}

				if( $cellphone && !array_key_exists($cellphone, $uniqPhones) && !array_key_exists('phones-'.$cellphone, $this->usersExclude) ){
					$uniqPhones[$cellphone] = $cellphone;
					$user['mobile'] = $cellphone;
					$mobile[] = $user;
				}
			}
		}

		return $mobile;
	}

	/** Exécution de la campagne
	 * \return peu retourné des exceptions
	 */
	public function exec(){

		$this->setCampaignUsers();

		// vérification de la présence des utilisateurs
		if( !$this->checkIfUsers() ){

			return false;
		}

		// Récupération des cannaux de communication
		$rchannels = Channels::getChannels( 0, $this->props['id'] );

		if( !$rchannels ){
			return false;
		}

		//  Envoie des message pour chaque chaine
		while( $channel = ria_mysql_fetch_assoc( $rchannels ) ){

			if( !$this->sendMessage( $channel ) ){
				continue;
			}
		}
		$this->setCampaignExcuted();
	}

	/** Cette fonction permet d'inicialisé la date d'exécution d'une campagne */
	public function setCampaignExcuted(){
		global $config;

		$sql = '
			update mkt_campaigns
			set cpg_date_executed=now()
			where cpg_tnt_id = '.$config['tnt_id'].'
				and cpg_id = '.$this->props['id'].'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	public function setCampaignUsers(){
		$type = ucfirst( strtolower( $this->props['type'] ) );
		// Récupération des utilisateurs
		$method = 'get'.$type.'Users';
		$this->$method();

		return $this->users;
	}

	/** Cette fonction permet de récupérer les utilisateurs ou les emails pour une campagne différé
	 */
	public function getDiffUsers(){
		$this->getSegmentsUsers();
		$this->getAccountsUsers();
		$this->getPhones();
		$this->getNewslettersUsers();
		$this->getEmail();
		return $this->setUsers();
	}

	public function getUsersCount(){
		return count($this->users);
	}

	/** Cette fonction permet de récupérer les utilisateurs pour une campagne immédiate
	 */
	public function getNowUsers(){
		// Récupération des groupes de trigger
		$groups = TriggersManager::getTriggersGroups( 0, $this->props['id'] );

		if( !$groups ){
			return false;
		}

		$userGroup = array();
		$orGroup = array();
		$andGroup = array();
		$triggersUsers = array();

		while( $group = ria_mysql_fetch_assoc( $groups ) ){
			// Récupération des triggers pour chaque groupe
			$rTriggers = TriggersManager::getTriggers( 0, $group['id'] );
			if( !$rTriggers ){
				continue;
			}

			$triggerUsersId = array();

			while( $trg = ria_mysql_fetch_assoc( $rTriggers ) ){
				// pour chaque trigger on récupère les utilisateurs
				$usersId = array();
				try{
					$oTrigger = new Triggers( $trg );
				}catch( Exception $e ){
					error_log( $e->getMessage() );
					continue;
				}

				$users = $this->getTriggerUsers( $oTrigger );

				if( !sizeof( $users ) ){
					continue;
				}
				$triggersUsers += $users;

				foreach( $users as $value ){
					$usersId[] = $value['id'];
				}
				$triggerUsersId[] = array_unique( $usersId );
			}

			$key = $group['id'].'-'.$group['rule'];

			if( $group['rule_item'] == 'and' ){
				$userGroup[$key] = $this->array_return_and_users( $triggerUsersId );
			}else{
				foreach( array_unique( $triggerUsersId ) as $value ){
					$userGroup[$key] = $value;
				}
			}
		}

		$and = 0;
		foreach( $userGroup as $key => $value ){
			$rule = explode( '-', $key );
			if( $rule[1] == 'or' ){
				$orGroup = $value;
			}else{
				$andGroup[] = $value;
				$and++;
			}
		}

		$andGroup = $this->array_return_and_users( $andGroup );

		$result = array();
		$filter = array_merge( $andGroup, $orGroup );

		foreach( $triggersUsers as $item ){
			if( in_array( $item['id'], $filter ) ){
				$result[] = $item;
			}
		}


		$this->addUsers( $triggersUsers );
	}

	/** CEtte fonction permet de récuperer les comptes client qui sont individuelement rattachés à la campagne */
	private function getAccountsUsers(){
		$rClients = CampaignsManager::getCampaignsUsers( $this->props['id']);

		if( !$rClients ){
			return false;
		}

		while( $client = ria_mysql_fetch_assoc($rClients) ){
			$rUser = gu_users_get($client['value']);

			if( !$rUser || !ria_mysql_num_rows($rUser) ){
				continue;
			}

			$user = ria_mysql_fetch_assoc($rUser);

			$usr = array(
				'id'       => $user['id'],
				'phone'   => $user['phone'],
				'mobile'   => $user['mobile'],
				'email'    => $user['email'],
				'lng_code' => $user['lng_code']
			);
			if( $client['include'] == '1' ){
				$this->usersInclude['usr-'.$user['id']] = $usr;
			}else{
				$this->usersExclude['usr-'.$user['id']] = $usr;
			}

		}
	}

	private function getPhones(){
		$rPhones = CampaignsManager::getCampaignsPhones( $this->props['id']);
		if( !$rPhones ){
			return false;
		}

		while( $phone = ria_mysql_fetch_assoc($rPhones) ){

			$usr = array(
				'id'       => 0,
				'mobile'   => $phone['value'],
				'email'    => '',
				'lng_code' => 'fr'
			);
			if( $phone['include'] == '1' ){
				$this->usersInclude['phone-'.$phone['value']] = $usr;
			}else{
				$this->usersExclude['phone-'.$phone['value']] = $usr;
			}
		}
	}

	/** Cette fonction permet de récupérer les ustilisateurs des segments liés à la campagne
	 */
	private function getSegmentsUsers(){
		$segResult = CampaignsManager::getCampaignsSegments( $this->props['id'] );

		if( !$segResult ){
			return false;
		}

		while( $res = ria_mysql_fetch_assoc( $segResult ) ){

			if( !seg_segments_exists( $res['seg_id'] ) ){
				continue;
			}

			$rUsers = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, true, null, $res['seg_id'] );

			if( !$rUsers || !ria_mysql_num_rows( $rUsers ) ){
				continue;
			}

			while( $user = ria_mysql_fetch_assoc( $rUsers ) ){
				$usr = array(
					'id'       => $user['id'],
					'phone'   => $user['phone'],
					'mobile'   => $user['mobile'],
					'email'    => $user['email'],
					'lng_code' => $user['lng_code']
				);
				if( $res['include'] == '1' ){
					$this->usersInclude['usr-'.$user['id']] = $usr;
				}else{
					$this->usersExclude['usr-'.$user['id']] = $usr;
				}
			}
		}
	}

	/** Cette fonction permet de récupérer les ustilisateurs ou les emails
	 * des catégorie de newsletter liés à la campagne
	 */
	private function getNewslettersUsers(){
		$newsResult = CampaignsManager::getCampaignsNewslettersCat( $this->props['id'] );

		if( !$newsResult ){
			return;
		}

		while( $res = ria_mysql_fetch_assoc( $newsResult ) ){

			if( !nlr_categories_exists( $res['cnt_id'] ) ){
				continue;
			}
			$rUsers = nlr_subscribers_get( NEWSLETTER_TYPE_INSCRIPT, 0, '', $res['cnt_id'] );

			if( !$rUsers ){
				continue;
			}

			$usersIn = array();
			$usersOut = array();
			$users = array();

			while( $mail = ria_mysql_fetch_assoc( $rUsers ) ){

				if( !is_null( $mail['usr_id'] ) ){
					$rUser = gu_users_get( $mail['usr_id'] );
					$usr = ria_mysql_fetch_assoc( $rUser );

					$usrId = $usr['id'];
					$user = array(
						'id'       => $usr['id'],
						'phone'   => $usr['phone'],
						'mobile'   => $usr['mobile'],
						'email'    => $usr['email'],
						'lng_code' => $usr['lng_code']
					);

					if( $res['include'] == '1' ){
						$usersIn['usr-'.$usrId] = $user;
					}else{
						$usersOut['usr-'.$usrId] = $user;
					}
				}else{

					if( $res['include'] == '1' ){
						$this->setEmailIn( $mail['email'] );
					}else{
						$this->setEmailOut( $mail['email'] );
					}
				}
			}
			$this->setUsersIn( $usersIn );
			$this->setUsersOut( $usersOut );
		}
	}

	/** Cette fonction permet de récupérer les emails liés à la campagne
	 */
	private function getEmail(){
		$emailResult = CampaignsManager::getCampaignsEmails( $this->props['id'] );

		if( !$emailResult ){
			return;
		}

		while( $email = ria_mysql_fetch_assoc( $emailResult ) ){

			if( $email['include'] == '1' ){
				$this->setEmailIn( $email['email'] );
			}else{
				$this->setEmailOut( $email['email'] );
			}
		}
	}

	/** Cette fonction permet d'envoyer le message au utilisateur par un channel
	 * \param  $channel Tableau résultat de la requête MYSQL Channels::getChannels
	 */
	private function sendMessage( array $channel ){

		if( sizeof( $channel ) < 0 ){
			return false;
		}

		$type = array( 'SMS', 'EMAIL' );

		// Génération du contenu dynamique pour chaque utilisateur
		// Prévoir une classe pour la gestion du contenu dynamique
		// $content = contentParse($channel['content']);
		if( in_array( $channel['type'], $type ) ){
			$method = ucfirst( strtolower( $channel['type'] ) ).'send';
			if(!$this->$method( $channel )){
				return false;
			}
		}
		return true;
	}

	private function SmsSend( $channel ){
		global $config;

		$rPartner = SMS_Partners::getPartnersTenants( $config['tnt_id'], $channel['ptn_id'] );

		if( !$rPartner ){
			error_log( 'No partners selected for this channel' );
			return false;
		}

		$quota = SMS_Partners::getPartnersQuota( $config['tnt_id'], $channel['ptn_id'] );

		if( !$quota ){
			$email = new Email();
			$email->setFrom( 'RiaShop Campagne Marketing <<EMAIL>>' );
			$email->setSubject( 'Plus quota pour '.tnt_tenants_get_name( $config['tnt_id'] ) );
			date_default_timezone_set( 'Europe/Paris' );
			setlocale( LC_TIME, 'fr_FR.utf8', 'fra' );
			$email->addParagraph( 'Plus de quota pour '.tnt_tenants_get_name( $config['tnt_id'] ).' aujourd\'hui : '.strftime( "%A %d %B %Y à %H:%M:%S" ) );

			$email->addTo( '<EMAIL>' );
			$email->addCC( '<EMAIL>' );
			return $email->send();
		}
		try{
			$stat = new StatCampaign();
			$stat->setCampaign($this->props);
			$stat->setChannel($channel);
		}
		catch( Exception $e ){
			error_log($e->getMessage());
			return false;
		}
		$partner = ria_mysql_fetch_assoc( $rPartner );

		$sender = $this->loadPartner( $partner );
		if( !$sender ){
			return false;
		}
		$sentCount = 0;
		$users = $this->getUsersMobile();


		if( trim($this->props['period']) != '' ){
			$dateToSend = new DateTime($this->props['period'].' '.$this->props['period_info'].':00');
		}

		if( !count($users) ){
			return false;
		}

		while( $qta = ria_mysql_fetch_assoc( $quota ) ){
			$rest = $qta['qte'] - $qta['qte_use'];
			$expires = new DateTime( $qta['date_expired'] );
			$now = new DateTime();

			if( $expires <= $now && !$rest ){
				$email = new Email();
				$email->setFrom( 'RiaShop Campagne Marketing <<EMAIL>>' );
				$email->setSubject( 'Expiration quota pour '.tnt_tenants_get_name( $config['tnt_id'] ) );
				date_default_timezone_set( 'Europe/Paris' );
				setlocale( LC_TIME, 'fr_FR.utf8', 'fra' );
				if($expires <= $now){
					$email->addParagraph( 'Le quota pour '.tnt_tenants_get_name( $config['tnt_id'] ).' et le partenaire numéro '.$qta['ptn_id'].', créer le  '.$qta['date_created'].' à expirer aujourd\'hui à '.strftime( "%A %d %B %Y à %H:%M:%S" ).', date d\'expiration '.$expires );
				}

				$email->addTo( '<EMAIL>' );
				$email->addCC( '<EMAIL>' );
				$email->send();
				continue;
			}

			$count = 0;
			$sent = 0;
			$send_notif_under200 = true;
			for( $sent; $sent < $rest; $sent++){

				if( $sentCount == count( $users ) ){
					break;
				}

				$user = $users[$sentCount];
				$sentCount++;

				try{
					$sender->setReceiver( $user['mobile'] );

					$sender->setIsMarketing(($channel['is_marketing']==1?true:false));

					if( isset($dateToSend) ){
						$sender->setDeliveryDate($dateToSend);
					}

					$response = $sender->send($channel['content']);
					$count += $response['totalCreditsRemoved'];
					$user['sms_id'] = $response['ids'][0];
				}
				catch(Exception $e){
					continue;
				}

				if( isset( $user['trg_id'] ) && isset( $user['obj_id'] ) ){
					 CampaignsManager::addCampaignsObjects( $this->props['id'], $user['trg_id'], $user['obj_id'], $user['id'] );
				}
				$stat->addStat( $user );

				if( ($rest - $count <= 200) && $send_notif_under200){
					$email = new Email();
					$email->setFrom( 'RiaShop Campagne Marketing <<EMAIL>>' );
					$email->setSubject( 'Quota pour '.tnt_tenants_get_name( $config['tnt_id'] ).' à moins de 200' );
					$email->addTextLine('Nombre de sms envoyer '.$count);
					$email->addTo( '<EMAIL>' );
					$email->addCC( '<EMAIL>' );
					$email->send();
					$send_notif_under200 = false;
				}
			}

			SMS_Partners::updatePartnersQuota( $qta['id'], 0, $count + $qta['qte_use'] );
		}
		// Do stats thing here for user
		// and stats for campaign
		// and other stats
		return true;
	}

	/** Cette fonction permet de chargé automatiquement la bonne api pour le bon partenaire
	 * \param  array  $partner résultat de la requête MYSQL SMS_Partners::getPartnersTenants
	 * \return [type]          [description]
	 */
	public function loadPartner( array $partner ){
		$name = ucfirst( strtolower( $partner['name'] ) );
		$api = json_decode( $partner['api_params'], true );

		// Chargement du fichier partenaire correspondant
		require_once( 'Marketing/partners/Sms'.$name.'.inc.php' );

		// Instanciation de la classe partenaire
		$className = 'Sms'.$name;
		$obj = new $className();

		// vérification que la classa possède bien le contrat
		$interfaces = class_implements( $className );

		if( !isset( $interfaces['SmsInterface'] ) ){
			error_log( 'la classe n\'est pas une implemantation de SmsInterface' );
			return false;
		}
		try{
			$obj->genConnection( $api );
		}
		catch(Exception $e){
			error_log($e->getMessage());
			return false;
		}

		return $obj;
	}
	/** Cette fonction permet de supprimer les message qui on été configurer en envoi différer */
	public function delDifferedSMS(){
		if( trim($this->props['period']) == '' ){
			return false;
		}
		global $config;

		$rChannel = Channels::getChannels(0, $this->props['id'], 0, 'SMS', _SMS_PARTNER_OVH);
		$channel = ria_mysql_fetch_assoc($rChannel);

		$rPartner = SMS_Partners::getPartnersTenants( $config['tnt_id'], $channel['ptn_id'] );
		$quota = SMS_Partners::getPartnersQuota( $config['tnt_id'], $channel['ptn_id'] );

		if( $quota ){
			$qta = ria_mysql_fetch_assoc( $quota );

			SMS_Partners::getPartners(_SMS_PARTNER_OVH);
			$sender = $this->loadPartner( ria_mysql_fetch_assoc($rPartner) );

			$oStats = new StatCampaign();
			$oStats->setCampaign($this->props);
			$rStats = $oStats->getStats();

			if( $rStats ){
				$addquota = ria_mysql_num_rows($rStats);

				while( $msg = ria_mysql_fetch_assoc($rStats) ){
					$sender->delSMS($msg['sms_id']);
				}

				$oStats->delStats();
				SMS_Partners::updatePartnersQuota( $qta['id'], 0, ($qta['qte_use']-$addquota) );
			}
		}
	}

	/** Cette fonction permet de récupérer les utilisateur liés a un trigger
	 * 	@param  $trg Réponse de requête MYSQL de TriggersManager::getTriggers
	 *	@return array Retourne un tableau d'utilisateur avec pour chacun d'entre eux :
	 *				- id : identifiant de l'utilisateur
	 *				- mobile : numéro de téléphone mobile
	 *				- email : adresse mail
	 *				- cls_id : classe de l'objet du trigger
	 *				- obj_id : l'objet du trigger (code promotion / commande / ...)
	 *				- fld_id : la colonne sur laquelle porte la condition du trigger
	 *				- trg_id : identifiant du trigger
	 */
	private function getTriggerUsers( Triggers $oTrigger ){
		$ar_cls = fld_classes_get_triggerable_array();

		if( !in_array( $oTrigger->getClsId(), $ar_cls ) ){
			return false;
		}
		global $config;

		// Initialisation des paramètres de l'objet pour la génération de la requête
		$oTrigger->tableName = fld_classes_get_physical_name( $oTrigger->getClsId() );
		$oTrigger->columnName = fld_fields_get_physical_name( $oTrigger->getFldId() );
		$oTrigger->setCampaignDateStart( $this->props['date_start'] );
		$oTrigger->setCampaignId( $this->props['id'] );
		$oTrigger->cpgType = $this->props['type'];

		// Génération de la requête MYSQL
		try{
			$res = $oTrigger->getTriggerQuery();
		}
		catch(Exception $e){
			error_log($e->getMessage());
			return array();
		}
		if( !$res || !ria_mysql_num_rows( $res ) ){
			return array();
		}
		while( $row = ria_mysql_fetch_assoc( $res ) ){
			if( $oTrigger->getClsId() == CLS_PMT_CODE ){
				if( pmt_codes_is_applicable(null, 0, $row['obj_id'], false, false, false, false, null, $row['usr_id']) !== true ){
					continue;
				}
			}

			$oTrigger->setUsers( $row, $row['obj_id'] );
		}

		return $oTrigger->getUsers();
	}

	/** Cette fonction permet de récupérer que les identifiants d'utilisateurs qui on
	 * pour règle de groupe AND
	 * @param  array  $array tableau contenant plusieurs tableau d'identifiants utilisateurs
	 *                       qui sont sous la condition AND
	 * @return array Un tableau contenant tous les identifiant utilisateur a inclure
	 *                    qui remplise la rêgle AND, ou un tableau vide si erreur
	 */
	private function array_return_and_users( array $array ){
		$result = array();
		$first = true;

		foreach( $array as $value ){

			if( $first ){
				$result = $value;
				$first = false;
				continue;
			}
			$result = array_intersect( $result, $value );
		}

		return $result;
	}

}

/// @}

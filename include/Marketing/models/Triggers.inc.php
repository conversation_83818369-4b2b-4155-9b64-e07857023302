<?php
/**	\defgroup mkt_triggers Triggers
 *	\ingroup mdl
 *	Ce module comprend la fonctionalité d'un trigger de campagne
 *	@{
 */
require_once( 'Marketing/Symbols.inc.php' );

/**
 * \class Triggers
 * \brief Cette classe permet la gestion de l'exécution d'un trigger
 */
class Triggers {

	/**
	 *	@var $_id
	 *	Identifiant du trigger
	 */
	private $_id;
	/**
	 *	@var $_action
	 *	Action effectué par le trigger
	 */
	private $_action;
	/**
	 *	@var $_clsId
	 *	Identifiant de la classe sur laquelle filtrer le traitement
	 */
	private $_clsId;
	/**
	 *	@var $_objId_0
	 *	Identifiant d'un objet sur lequel effectuer le traitement
	 */
	private $_objId_0;
	/**
	 *	@var $_objId_1
	 *	Identifiant d'un objet sur lequel effectuer le traitement
	 */
	private $_objId_1;
	/**
	 *	@var $_objId_2
	 *	Identifiant d'un objet sur lequel effectuer le traitement
	 */
	private $_objId_2;
	/**
	 * @var $_fldId
	 *	Identifiant du champ avancé qui représente le nom de la colonne sur laquelle effectuer le traitement
	 */
	private $_fldId;
	/**
	 *	@var $_symCode
	 *	Code du symbole sur mettra en place la conditon
	 */
	private $_symCode;
	/**
	 *	@var $_critValue
	 *	Critère sur le quel effectuer le traitement
	 */
	private $_critValue;

	/**
	 *	@var array $users
	 *	Tableau des utilisateurs du trigger
	 */
	private $users = array();
	/**
	 *	@var $tableName
	 *	Nom de la table sur laquelle réalisé la requête
	 */
	public $tableName;
	/**
	 *	@var $tablePrefixe
	 *	Préfixe utilisé pour la table
	 */
	private $tablePrefixe;
	/**
	 *	@var $columnName
	 *	Nom de la colonne
	 */
	public $columnName;
	/**
	 *	@var $cpgDateStart
	 *	Date de début de la campagne
	 */
	private $cpgDateStart;
	/**
	 *	@var $cpgId
	 *	Identifiant de la campagne
	 */
	private $cpgId;
	/**
	 *	@var $userJoin
	 *	Jointure à ajouté pour la requête sql
	 */
	private $userJoin;
	
	public $cpgType;
	/**
	 * Triggers constructor.
	 *
	 * @param array $trigger
	 *
	 * @throws Exception
	 */
	public function __construct( array $trigger ){
		if( !ria_array_key_exists( array(
			'id',
			'action',
			'cls_id',
			'obj_id_0',
			'obj_id_1',
			'obj_id_2',
			'fld_id',
			'sym_code',
			'value'
		), $trigger )
		){
			throw new Exception( 'Not all the parameters are entered' );
		}
		$this->_id = $trigger['id'];
		$this->_action = $trigger['action'];
		$this->_clsId = $trigger['cls_id'];
		$this->_objId_0 = $trigger['obj_id_0'];
		$this->_objId_1 = $trigger['obj_id_1'];
		$this->_objId_2 = $trigger['obj_id_2'];
		$this->_fldId = $trigger['fld_id'];
		$this->_symCode = $trigger['sym_code'];
		$this->_critValue = $trigger['value'];
	}

	/** Permet d'initialiser la date de début de la campagne
	 *
	 * @param $dateStart Date de début de la campagne
	 */
	public function setCampaignDateStart( $dateStart ){
		$this->cpgDateStart = $dateStart;
	}

	/** Permet d'initialiser l'identifiant de la campagne
	 *
	 * \param $id Obligatoire, Identifiant de la campagne
	 */
	public function setCampaignId( $id ){
		$this->cpgId = $id;
	}

	/**
	 * Permet d'initialiser le préfixe de la table
	 *
	 * \param $tablePrefixe Obligatoire, préfixe de la table
	 */
	public function setTablePrefixe( $tablePrefixe ){
		$this->tablePrefixe = $tablePrefixe;
	}

	/**
	 * @param $userJoin
	 */
	public function setUserJoin( $userJoin ){
		$this->userJoin = $userJoin;
	}

	/**
	 * @param      $user
	 * @param bool $cls_id
	 * @param bool $fld_id
	 *
	 * @return bool
	 */
	public function setUsers( $user, $cls_id = true, $fld_id = true ){
		if( !is_array( $user ) || sizeof( $user ) < 0 ){
			return false;
		}

		$ar = array(
			'id'       => $user['usr_id'],
			'mobile'   => $user['mobile'],
			'email'    => $user['email'],
			'trg_id'   => $this->_id,
			'obj_id'   => $user['obj_id'],
			'lng_code' => $user['lng_code']
		);

		if( $cls_id ){
			$ar['cls_id'] = $this->_clsId;
		}
		if( $fld_id ){
			$ar['fld_id'] = $this->_fldId;
		}
		$this->users[] = $ar;
	}

	/** Getters
	 */
	public function getId(){
		return $this->_id;
	}

	/**
	 * @return mixed
	 */
	public function getAction(){
		return $this->_action;
	}

	/**
	 * @return mixed
	 */
	public function getClsId(){
		return $this->_clsId;
	}

	/**
	 * @return mixed
	 */
	public function getFldId(){
		return $this->_fldId;
	}

	/**
	 * @return mixed
	 */
	public function getSymCode(){
		return $this->_symCode;
	}

	/**
	 * @return mixed
	 */
	public function getCritValue(){
		return $this->_critValue;
	}

	/**
	 * @return mixed
	 */
	public function getObjId_0(){
		return $this->_objId_0;
	}

	/**
	 * @return mixed
	 */
	public function getObjId_1(){
		return $this->_objId_1;
	}

	/**
	 * @return mixed
	 */
	public function getObjId_2(){
		return $this->_objId_2;
	}


	/** Récupère les utilisateurs du trigger
	 * @return [type] [description]
	 */
	public function getUsers(){
		return $this->users;
	}

	/** Cette fonction génère le SQL et exécute la requête
	 * pour récupérer les utilisateurs concerné par le trigger
	 * @return Résultat de requête MYSQL avec les colonnes suivantes :
	 *            - obj_id : Identifiant de l'objet concerné
	 *            - usr_id : Identifiant de l'utilisateur
	 *            - email : l'addresse email de l'utilisateur
	 *            - mobile : le numéro mobile de l'utilisateur
	 *            - lng_code : le code iso du pays de l'utilisateur
	 * @throws Exception si des attribut sont manquant
	 */
	Public function getTriggerQuery(){
		if( is_null( $this->tableName ) ){
			throw new Exception( "paramètre manquant, il faut définir tableName" );
		}
		if( is_null( $this->columnName ) ){
			throw new Exception( "paramètre manquant, il faut définir columnName" );
		}
		if( is_null( $this->cpgId ) ){
			throw new Exception( "paramètre manquant, il faut définir cpgId" );
		}
		if( is_null( $this->cpgDateStart ) ){
			throw new Exception( "paramètre manquant, il faut définir cpgDateStart" );
		}

		global $config;

		try{
			$this->getUserJoin();
		}catch( Exception $e ){
			throw new Exception($e->getMessage());
		}

		$sql = '
			select '.$this->tablePrefixe.'id as obj_id, usr_id, usr_email as email, (select adr_mobile from gu_adresses where adr_tnt_id='.$config['tnt_id'].' and adr_usr_id=usr_id and adr_mobile != "" limit 1) as mobile, usr_lng_code as lng_code
			from '.$this->tableName.'
		';

		$sql .= $this->userJoin;

		$sql .= '
			where '.$this->tablePrefixe.'tnt_id = '.$config['tnt_id'].'
				and not exists (select mco_obj_id from mkt_campaigns_objects where mco_tnt_id='.$config['tnt_id'].' and mco_cpg_id='.$this->cpgId.' and mco_obj_id='.$this->tablePrefixe.'id and mco_usr_id=usr_id and mco_trg_id='.$this->_id.' )
			'.$this->getActionCondition().'
		';

		$sql .= $this->getTriggerCondition();

		$r = ria_mysql_query( $sql );

		return $r;
	}

	/** Permet de généré les conditions where suivant le symbole utilisé
	 * \return retourne la chaine sql àa ajouter dans le where, sino false si le symbole n'existe pas
	 */
	private function getTriggerCondition(){
		if( !Symbols::existSymbols( $this->_symCode ) ){
			return false;
		}

		$triggerCondition = '';

		switch( $this->_symCode ){
			case 'PMT_EXPIRE_DAYS' :
				$triggerCondition = ' and datediff('.$this->columnName.',now()) = '.$this->_critValue;
				
				$date = new DateTime("today");
				$day = $date->format('N');
				$now = $date->format('Y-m-d');
				if( $day == 5 ){
					$date->add(new DateInterval('P1D'));
					$sat = $date->format('Y-m-d');
					$date->add(new DateInterval('P1D'));
					$sun = $date->format('Y-m-d');
					
					$triggerCondition = ' and (
						datediff('.$this->columnName.',now()) = '.$this->_critValue.'
						or datediff('.$this->columnName.',"'.$sat.'") = '.$this->_critValue.'
						or datediff('.$this->columnName.',"'.$sun.'") = '.$this->_critValue.'
						)';
				}
				break;
			case 'ORD_STATUS_CHANGED' :
				$triggerCondition = 'and '.$this->columnName.' = '.$this->_critValue;
				break;
			case 'PMT_EXPIRE_MINUS_DAYS' :
				$triggerCondition = '					
					 and datediff('.$this->columnName.',now()) >= 0
					 and datediff('.$this->columnName.',now()) <= '.$this->_critValue.'
					';
				break;
			case 'PMT_EXPIRE_PLUS_DAYS' :
				$triggerCondition = '
					and datediff('.$this->columnName.',now()) >= '.$this->_critValue.'
				';
				break;
			case 'PMT_CODE_EQUALS' :
				$triggerCondition = '
					and '.$this->columnName.'='.$this->_critValue.'
				';
				break;
		}

		if( isset( $this->_objId_0 ) ){
			$triggerCondition .= ' and '.$this->tablePrefixe.'id = '.$this->_objId_0;
		}
		return $triggerCondition;
	}

	/** Génère le sql pour les critères d'acton sur une table
	 * @return retourne le chaine sql à ajouter dans le where de la requête.
	 *         false si l'attribut action n'est pas paramétré
	 */
	private function getActionCondition(){
		if( is_null( $this->_action ) ){
			return false;
		}
		$action = '';
		switch( $this->_action ){
			case 'ADD' :
				$action = 'date_created';
				break;
			case 'UPD' :
				$action = 'date_modified';
				break;
			case 'DEL' :
				$action = 'date_deleted';
				break;
			case 'CONTROL' :
				break;
		}
		if( $action !== '' ){
			$action = '
				and '.$this->tablePrefixe.$action.' <= now()
				and '.$this->tablePrefixe.$action.' >= "'.$this->cpgDateStart.'"
			';
		}

		return $action;
	}

	/** Génère atravers un switch sur la class utilissé le code sql pour réalisé le join sur les utilisateur
	 * @return retourne une exception si l'attribue clsId n'est pas paramétré
	 */
	private function getUserJoin(){
		if( is_null( $this->_clsId ) ){
			throw new Exception( "Error Processing Request" );
		}
		switch( $this->_clsId ){
			case CLS_PMT_CODE :
				$this->tablePrefixe = 'cod_';
				$this->userJoin = '
					join pmt_offers on off_cod_id=cod_id and off_tnt_id=cod_tnt_id
					join pmt_users on pmt_cod_id=off_cod_id and pmt_tnt_id=cod_tnt_id
					join gu_users on pmt_usr_id=usr_id and pmt_tnt_id=usr_tnt_id
				';
				break;
			case CLS_ORDER :
				$this->tablePrefixe = 'ord_';
				$this->userJoin = '
					join gu_users on ord_usr_id=usr_id and ord_tnt_id=usr_tnt_id
					';
				break;
		}
	}
}

/// @}
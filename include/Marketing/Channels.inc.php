<?php

/** Cette classe permet la gestion des canaux de communication et des messages pour une campagne
 *  \ingroup mkt
*/
class Channels{

	private static $CHANNEL_TYPE = array( 'SMS', 'EMAIL' );

	/** Cette fonction permet de récupérer les canaux de communication
	 * \param  $id Optionnel, identifiant d'un canal
	 * \param  $cpg_id Optionnel, identifiant de la campagne
	 * \param  $wst_id Optionnel, identifiant d'un website
	 * \param  $type Optionnel, type de canal valeur authorisé : EMAIL, SMS
	 * \param  $ptn_id Optionnel, identifiant du partenaire
	 * \param  $lng_code Optionnel, code lng
	 *
	 * \return Le résultat de cette fonction est retourné sous la forme d'un résultat de requête MySQL, comprenant les colonnes suivantes :
	 * 			-tnt_id : identifiant du tenant
	 *			-id : identifiant du canal
	 *			-wst_id : identifiant du site web
	 *			-type : ype de transmition EMAIL ou SMS
	 *			-email_from : Adresse du destinataire, si transmition email
	 *			-email_bcc : Adresses des copies cachés, si transmition email
	 *			-content : le contenu du message test pour cpg, html pour email
	 *			-ptn_id : identifiant du partenaire si cpg
	 *			-lng_code : code lng
	 *			-cpg_id : identifiant de la campagne
	 * 			-is_marketing: si le message et de marqueting ou pas
	 */
	public static function getChannels( $id=0, $cpg_id=0, $wst_id=0, $type=false, $ptn_id=0, $lng_code=false ){
		if( !is_numeric($id) || $id<0 ){
			return false;
		}

		if( !is_numeric($wst_id) || $wst_id<0 ){
			return false;
		}

		if( $type && (!in_array($type, self::$CHANNEL_TYPE) || !is_string($type)) ){
			return false;
		}

		if( !is_numeric($ptn_id) || $ptn_id<0 ){
			return false;
		}

		if( $lng_code && (!is_string($lng_code) || !i18n_languages_exists($lng_code)) ){
			return false;
		}

		if( !is_numeric($cpg_id) || $cpg_id<0 ){
			return false;
		}

		global $config;

		$sql = '
			select chl_tnt_id as tnt_id,
					chl_id as id,
					chl_wst_id as wst_id,
					chl_type as type,
					chl_email_from as email_from,
					chl_email_bcc as email_bcc,
					chl_ptn_id as ptn_id,
					chl_lng_code as lng_code,
					chl_cpg_id as cpg_id,
					chl_content as content,
					chl_is_marketing as is_marketing
			from mkt_channels
			where chl_tnt_id='.$config['tnt_id'].'
				and chl_date_deleted is null
		';

		if( $id ){
			$sql .= '
				and chl_id='.$id.'
			';
		}

		if( $wst_id ){
			$sql .= '
				and chl_wst_id='.$wst_id.'
			';
		}

		if( $type ){
			$sql .= '
				and chl_type="'.$type.'"
			';
		}

		if( $ptn_id ){
			$sql .= '
				and chl_ptn_id='.$ptn_id.'
			';
		}

		if( $lng_code ){
			$sql .= '
				and chl_lng_code="'.$lng_code.'"
			';
		}

		if( $cpg_id ){
			$sql .= '
				and chl_cpg_id='.$cpg_id.'
			';
		}

		$res = ria_mysql_query($sql);

		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter un canal de transmition
	 * \param  $wst_id Identifiant du site web
	 * \param  $type Type de transmition du message valeur accepté : EMAIL, SMS
	 * \param  $cpg_id Identifiant de la campagne
	 * \param  $email_from Optionnel, si type EMAIL obligatoire
	 * \param  $email_bcc Optionnel, chaine de caractère avec les adresses emails séparer par des virgules
	 * \param  $ptn_id Optionnel, identifiant du partenaire, si type SMS obligatoire
	 * \param  $lng_code Optionnel, code lng
	 * \param $is_marketing Facultatif, détermine s'il s'agit d'un canal marketing (true) ou non (false)
	 *
	 * \return l'identifiant du canal créer
	 */
	public static function addChannels( $wst_id, $type, $cpg_id, $email_from=false, $email_bcc=false, $ptn_id=0, $lng_code=false, $is_marketing=true ){
		if( !is_numeric($wst_id) || $wst_id<=0 ){
			return false;
		}
		if( !in_array($type, self::$CHANNEL_TYPE) || !is_string($type) ){
			return false;
		}

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		if( ($email_from && !gu_valid_email($email_from)) ||  (!$email_from && $type == 'EMAIL') ){
			return false;
		}

		if( $email_bcc && !gu_valid_email($email_bcc) ){
			return false;
		}

		if( !is_numeric($ptn_id) || $ptn_id<0 || ( !$ptn_id && $type == 'SMS' )){
			return false;
		}

		if( $lng_code && (!is_string($lng_code) || !i18n_languages_exists($lng_code)) ){
			return false;
		}

		global $config;

		$data = array(
			'chl_tnt_id' => $config['tnt_id'],
			'chl_wst_id' => $wst_id,
			'chl_type' => '"'.$type.'"',
			'chl_email_from' => '"'.filter_var($email_from, FILTER_SANITIZE_EMAIL).'"',
			'chl_email_bcc' => '"'.filter_var($email_bcc, FILTER_SANITIZE_EMAIL).'"',
			'chl_cpg_id' => $cpg_id,
			'chl_date_created' => 'now()'
			);

		if( $ptn_id ){
			$data['chl_ptn_id'] = $ptn_id;
		}

		if( $lng_code ){
			$data['chl_lng_code'] = '"'.$lng_code.'"';
		}

		$data['chl_is_marketing'] = $is_marketing ? 1 : 0;

		$keys = implode(',', array_keys($data));
		$values = implode(',', array_values($data));

		$sql = '
			insert into mkt_channels
			('.$keys.') values ('.$values.')
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet d'ajouter un contenu au canal
	 * \param  $id Identifiant du canal
	 * \param  $content contenu du message
	 *
	 * \return l'identifiant du canal édité, false en cas d'échec.
	 */
	public static function addChannelContent( $id, $content ){
		if( !is_numeric($id) || $id<=0 ){
			return false;
		}

		if( !is_string($content) || trim($content) == '' ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_channels set
			chl_content="'.addslashes(trim($content)).'"
			where chl_tnt_id='.$config['tnt_id'].'
				and chl_id='.$id.'
		';


		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}


	/** Cette fonction permet de modifier un canal
	 * \param $id Identifiant du canal
	 * \param $wst_id Identifiant du site web
	 * \param $type Type de transmition du message valeur accepté : EMAIL, SMS
	 * \param $cpg_id Identifiant de la campagne
	 * \param $email_from Optionnel, si type EMAIL obligatoire
	 * \param $email_bcc Optionnel, chaine de caractère avec les adresses emails séparer par des virgules
	 * \param $ptn_id Optionnel, identifiant du partenaire, si type SMS obligatoire
	 * \param $lng_code Optionnel, code langue
	 * \param $is_marketing Facultatif, si renseigné, le canal sera mis à jour comme étant marketing (true) ou non (false). Si non renseigné, la valeur précédente n'est pas modifiée.
	 *
	 * \return true en cas de succès, false si échec
	 */
	public static function updateChannels( $id, $wst_id, $type, $email_from=false, $email_bcc=false, $cpg_id=0, $ptn_id=0, $lng_code=false, $is_marketing=null ){
		if( !is_numeric($id) || $id<=0 ){
			return false;
		}

		if( !is_numeric($wst_id) || $wst_id<=0 ){
			return false;
		}

		if( !in_array($type, self::$CHANNEL_TYPE) || !is_string($type) ){

			return false;
		}

		if( $email_from && (!gu_valid_email($email_from) || ( !$email_from && $type == 'EMAIL' )) ){
			return false;
		}

		if( $email_bcc && !gu_valid_email($email_bcc) ){
			return false;
		}

		if( !is_numeric($cpg_id) || $cpg_id<0 ){
			return false;
		}

		if( !is_numeric($ptn_id) || $ptn_id<0){
			return false;
		}

		if( $lng_code && (!is_string($lng_code) || !i18n_languages_exists($lng_code)) ){
			return false;
		}

		global $config;

		$data = array(
			'chl_wst_id='.$wst_id,
			'chl_type="'.addslashes(trim($type)).'"'
			);
		if( $email_from ){
			$data[] = 'chl_email_from="'.filter_var($email_from, FILTER_SANITIZE_EMAIL).'"';
		}

		if( $email_bcc ){
			$data[] = 'chl_email_bcc="'.filter_var($email_bcc, FILTER_SANITIZE_EMAIL).'"';
		}

		if( $cpg_id ){
			$data[] = 'chl_cpg_id='.$cpg_id;
		}

		if( $ptn_id ){
			$data[] = 'chl_cpg_id='.$cpg_id;
		}

		if( $lng_code ){
			$data[] = 'chl_lng_code="'.$lng_code.'"';
		}

		if( !is_null($is_marketing) ){
			$data[] = 'chl_is_marketing='.($is_marketing?1:0);
		}
		if( !sizeof($data) ){
			return false;
		}

		$sql = 'update mkt_channels set ';
		$sql .= implode(', ', $data);
		$sql .= '
			where chl_id='.$id.'
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer virtuelement un canal
	 * \param  $id Identifiant du canal
	 *
	 * \return L'identifiant du canl supprimer, false si échec
	 */
	public static function delChannels( $id ){

		if( !is_numeric($id) || $id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_channels
			set chl_date_deleted=now()
			where chl_tnt_id='.$config['tnt_id'].'
				and chl_id='.$id.'
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return $id;
	}

	/** Cette fonction permet de supprimer les canaux
	 *	\param $cpg_id Obligatoire, identifiant de la campagne
	 *	\return true en cas de succès, false en cas d'échec
	 */
	public static function delAllChannels( $cpg_id ){

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_channels
			set chl_date_deleted=now()
			where chl_tnt_id='.$config['tnt_id'].'
				and chl_cpg_id='.$cpg_id.'
		';

		$res = ria_mysql_query($sql);

		if( !$res ){
			return false;
		}

		return true;
	}
}

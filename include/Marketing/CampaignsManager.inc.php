<?php

/** \class CampaignsManager
 *  \ingroup mkt
 * \brief Cette classe permet la gestion des campagne marketing
 */
class CampaignsManager {

	///< type de campagne
	protected static $CAMPAIGNS_TYPES = array(
		'NOW',
		'DIFF'
	);

	/** Cette fonction permet de récuper les campagnes d'un tenant
	 * \param $id Optionnel, identifiant d'une campagne
	 * \param $type Optionnel, indique le type de campagne si différé ou immédiate. Paramètre
	 * accepté ( "DIIF", "NOW" )
	 * \param $date_start Optionnel, la date de début d'acitvation de la campagne si renseigner
	 * récupère toutes les campagnes qui débutent a ou après la date renseigné.
	 * \param $date_end Optionnel, la date de fin de la campagne si renseigner récupère toutes les
	 * campagnes qui finissent a ou avant la date renseigné.
	 *	\param bool $get_executed Facultatif. Si true, seules les campagnes terminées sont retournées (valeur par défaut). Si false, toutes les campagnes sont retournées.
	 *
	 * \return Le résultat de cette fonction est retourné sous la forme d'un résultat de requête
	 * MySQL, comprenant les colonnes suivantes :
	 *            - tnt_id : identifiant du tenant
	 *            - id :identifiant de la campagne
	 *            - title : titre de la campagne
	 *            - desc : la description de la campagne
	 *            - date_start : date de début de la campagne
	 *            - date_end : date de fin de la campagne
	 *            - type : le type de campagne différé ou immédiate ( DIFF, NOW)
	 *            - period : la période choisie pour l'envoie de la campagne en différé ( day,
	 *            week, month )
	 *            - period_info : information supplémentaire sur la période ( quel jours de la
	 *            semaine, quel jours du mois, à quel heure )
	 */
	public static function getCampaigns(
		$id = 0,
		$type = false,
		$date_start = null,
		$date_end = null,
		$get_executed=true
	){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}
		if( $type && !in_array( strtoupper( $type ), self::$CAMPAIGNS_TYPES ) ){
			return false;
		}

		if( !is_null( $date_start ) && !isdateheure( $date_start ) ){
			return false;
		}

		if( !is_null( $date_end ) && !isdateheure( $date_end ) ){
			return false;
		}

		global $config;

		$sql = '
			select cpg_tnt_id as tnt_id, cpg_id as "id", cpg_title as "title", cpg_desc as "desc", cpg_date_start as date_start, cpg_date_end as date_end, cpg_type as "type", cpg_period as period, cpg_period_info as period_info, cpg_date_created as date_created, cpg_date_executed as date_executed
			from mkt_campaigns
			where cpg_tnt_id = '.$config['tnt_id'].'
				and cpg_date_deleted is null
		';

		if( $id > 0 ){
			$sql .= '
				and cpg_id = '.$id.'
			';
		}

		if( $type ){
			$sql .= '
				and cpg_type = '.$type.'
			';
		}

		if( !is_null( $date_start ) ){
			$sql .= '
				and cpg_date_start <= "'.$date_start.'"
				';
		}
		if( !is_null( $date_end ) ){
			$sql .= '
				and cpg_date_end >= "'.$date_end.'"
				';
		}

		if( !$get_executed ){
			$sql .= '
				and cpg_date_executed is null
			';
		}

		$sql .= ' order by cpg_date_start desc, cpg_date_end desc';

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** cette fonction permte de récupérer directement le titre d'une campagne utile pour le fils d'arianne par exemple
	 * @param $cpg Identifiant de la campagne
	 *
	 * @return bool le titre de la campagne sinon false en cas d'échec
	 */
	public static function getCampaignTitle( $cpg ){
		if( !is_numeric( $cpg ) || $cpg <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			select cpg_title as title
			from mkt_campaigns
			where cpg_tnt_id='.$config['tnt_id'].'
				and cpg_id='.$cpg.'
		';

		$res = ria_mysql_query( $sql );
		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}
		$cpg = ria_mysql_fetch_assoc($res);
		return $cpg['title'];
	}

	/** Cette fonction permet de sauvegarder une campagne
	 * \param $title Titre de la campagne
	 * \param $desc Description de la campagne
	 * \param $type Le type de campagne si différé ou immédiate, valeur accepter ("DIFF", "NOW")
	 * \param $date_start La date de début de la campagne
	 * \param $date_end Optionnel, date de fin de la campagne
	 * \param $period Optionnel, période de récurrence de la campagne ( day, week, month )
	 * \param $period_info Optionnel, information supplémentaire sur les périodes ( quel jours de
	 * la semaine, quel jour du mois, quel heure )
	 *
	 * \return l'identifiant de la ligne ajouté, false si erreur
	 */
	public static function addCampaigns(
		$title,
		$desc,
		$type,
		$date_start = null,
		$date_end = null,
		$period = false,
		$period_info = false
	){
		if( $title === '' || !is_string( $title ) ){
			return false;
		}

		if( !is_string( $desc ) ){
			return false;
		}

		if( !is_string( $type ) || !in_array( strtoupper( $type ), self::$CAMPAIGNS_TYPES ) ){
			return false;
		}

		if( !is_null( $date_end ) && !isdateheure( $date_start ) ){
			return false;
		}
		if( !is_null( $date_end ) && !isdateheure( $date_end ) ){
			return false;
		}

		if( $period && !is_string( $period ) ){
			return false;
		}

		global $config;

		$data = array(
			'cpg_tnt_id'       => $config['tnt_id'],
			'cpg_title'        => '"'.addslashes( $title ).'"',
			'cpg_desc'         => '"'.addslashes( $desc ).'"',
			'cpg_type'         => '"'.$type.'"',
			'cpg_date_created' => 'now()'
		);

		if( !is_null( $date_start ) ){
			$data['cpg_date_start'] = '"'.$date_start.'"';
		}

		if( !is_null( $date_end ) ){
			$data['cpg_date_end'] = '"'.$date_end.'"';
		}

		if( $period ){
			$data['cpg_period'] = '"'.$period.'"';
		}

		if( $period_info ){
			$data['cpg_period_info'] = '"'.addslashes( $period_info ).'"';
		}

		$keys = implode( ',', array_keys( $data ) );
		$values = implode( ',', array_values( $data ) );

		$sql = '
			insert into mkt_campaigns
				('.$keys.')
			values
				('.$values.')
		';
		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet de modifier une campagne
	 * \param $id identifiant de la campagne
	 * \param $title Titre de la campagne
	 * \param $desc Description de la campagne
	 * \param $type Le type de campagne si différé ou immédiate, valeur accepter ("DIFF", "NOW")
	 * \param $date_start Optionnel, la date de début de la campagne
	 * \param $date_end Optionnel, date de fin de la campagne
	 * \param $period Optionnel, période de récurrence de la campagne ( day, week, month )
	 * \param $period_info Optionnel, information supplémentaire sur les périodes ( quel jours de
	 * la semaine, quel jour du mois, quel heure )
	 *
	 * \return True si succès, false si erreur
	 */
	public static function updateCampaigns(
		$id,
		$title,
		$desc,
		$type,
		$date_start = null,
		$date_end = null,
		$period = false,
		$period_info = false
	){

		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		if( !is_string( $title ) || trim( $title ) == '' ){
			return false;
		}

		if( !is_string( $desc ) ){
			return false;
		}

		if( !is_string( $type ) || !in_array( strtoupper( $type ), self::$CAMPAIGNS_TYPES ) ){
			return false;
		}

		if( !is_null( $date_start ) && !isdateheure( $date_start ) ){
			return false;
		}

		if( !is_null( $date_end ) && !isdateheure( $date_end ) ){
			return false;
		}

		if( $period && !is_string( $period  ) ){
			return false;
		}


		global $config;

		$data = array();

		if( $title ){
			$data[] = 'cpg_title = "'.addslashes( trim( $title ) ).'"';
		}

		if( $desc ){
			$data[] = 'cpg_desc = "'.addslashes( trim( $desc ) ).'"';
		}

		if( $type ){
			$data[] = 'cpg_type = "'.$type.'"';
		}

		if( !is_null( $date_start ) ){
			$data[] = 'cpg_date_start = "'.$date_start.'"';
		}

		if( !is_null( $date_end ) ){
			$data[] = 'cpg_date_end = "'.$date_end.'"';
		}

		if( $period ){
			$data[] = 'cpg_period = "'.$period.'"';
		}else{
			$data[] = 'cpg_period = null';
		}

		if( $period_info ){
			$data[] = 'cpg_period_info = "'.addslashes( $period_info ).'"';
		}else{
			$data[] = 'cpg_period_info = null';
		}

		if( !sizeof( $data ) ){
			return false;
		}

		$sql = 'update mkt_campaigns set ';
		$sql .= implode( ', ', $data );
		$sql .= '
			where cpg_tnt_id = '.$config['tnt_id'].'
				and cpg_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer une campagne
	 * \param $id Identifiant de la campagne
	 *
	 * \return l'identifiant de la campagne supprimé, false si erreur
	 */
	public static function delCampaigns( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_campaigns set
			cpg_date_deleted=current_timestamp
			where cpg_tnt_id = '.$config['tnt_id'].'
				and cpg_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}

	/** Cette fonction permet de récupérer les emails associé à une campagne
	 * \param $cpg_id  Identifiant de la campagne
	 * \param $id  Optionnel, identifiant de l'email
	 * \param $include Optionnel, true pour récupérer les email inclues, false pour ce qui sont
	 * exclue et null pour tous récupérer
	 *
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonne suivante :
	 *            - tnt_id : identifiant du tenant
	 *            - id : identifiant de l'email
	 *            - cpg_id : identifiant de la campagne
	 *            - email : adresse email
	 *            - include : 1 ou 0 si l'adresse email est incluse ou excluse
	 */
	public static function getCampaignsEmails( $cpg_id, $id=0, $include=null ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select cpe_tnt_id as tnt_id, cpe_id as id, cpe_cpg_id as cpg_id, cpe_email as email, cpe_include as include
			from mkt_campaigns_emails
			where cpe_tnt_id = '.$config['tnt_id'].'
				and cpe_cpg_id = '.$cpg_id.'
		';

		if( $id ){
			$sql .= ' and cpe_id = '.$id;
		}
		if( !is_null( $include ) ){
			if( $include ){
				$sql .= ' and cpe_include = 1';
			}else{
				$sql .= ' and cpe_include = 0';
			}
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter des emails à inclure ou exclure
	 * \param $cpg_id Identifiant de la campagne
	 * \param $email Adresse email
	 * \param bool $include Optionnel, true or false inclu ou pas
	 *
	 * \return l'identifiant de l'email ajouté, false en cas d'échec
	 */
	public static function addCampaignsEmails( $cpg_id, $email, $include=true ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !gu_valid_email( $email ) ){
			return false;
		}

		global $config;

		$sql = '
			insert into mkt_campaigns_emails
				( cpe_tnt_id, cpe_cpg_id , cpe_email, cpe_include )
			values
				( '.$config['tnt_id'].', '.$cpg_id.', "'.filter_var( $email, FILTER_SANITIZE_EMAIL ).'", '.( $include ? '1' : '0' ).')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet de modifier un email
	 * \param  $id Identifiant de l'email
	 * \param  $cpg_id Identifiant de la campagne
	 * \param  $email L'adresse email courrante ou nouvelle
	 * \param  $include Optionnel, true ou false inclue ou non l'email dans la campagne
	 *
	 * \return          [description]
	 */
	public static function updateCampaignsEmails( $id, $cpg_id, $email, $include = true ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !gu_valid_email( $email ) ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_campaigns_emails set
				cpe_email = '.filter_var( $email, FILTER_SANITIZE_EMAIL ).',
				cpe_include = '.( $include ? '1' : '0' ).'
			where cpe_tnt_id = '.$config['tnt_id'].'
				and cpe_id = '.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}

	/** Cette fonction permet de supprimer un email de la campagne
	 * \param $cpg_id Identifiant de la campagne
	 * \param $id Optionnel, identifiant de l'email
	 *
	 * \return true si succès, false si échec
	 */
	public static function delCampaignsEmails( $cpg_id, $id = 0 ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			delte from mkt_campaigns_emails
			where cpe_tnt_id = '.$config['tnt_id'].'
				and cpe_cpg_id = '.$cpg_id.'
		';

		if( $id ){
			$sql .= ' and cpe_id = '.$id;
		}

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de récupérer les numéros de téléphone associé a une campagne
	 *	\param $cpg_id Identifiant de la campagne
	 *
	 * 	\return un resultat mysql avec les colonnes suivante :
	 * 				- value
	 * 				- include
	 * 				- label
	 */
	public static function getCampaignsPhones( $cpg_id ){

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			select cph_phone_number as "value", cph_include as include, cph_phone_number as label
			from mkt_campaigns_phones
			where cph_tnt_id='.$config['tnt_id'].'
				and cph_cpg_id='.$cpg_id.'
		';
		$r = ria_mysql_query($sql);

		if( !$r || !ria_mysql_num_rows($r) ){
			return false;
		}

		return $r;
	}
	/** Cette fonction permet de copier des compte client d'une campagne a une autre
	 *	\param $src_cpg Identifiant de la campagne que l'on souhaite copier
	 *  \param $dest_cpg Identifiant de la campagne où l'on veux coller
	 *
	 *	\return true si succès, false si échec
	 */
	public static function copyCampaignsPhones( $src_cpg, $dest_cpg ){
		if( !is_numeric($src_cpg) || $src_cpg<=0 ){
			return false;
		}
		if( !is_numeric($dest_cpg) || $dest_cpg<=0 ){
			return false;
		}
		global $config;

		$sql = '
			replace into mkt_campaigns_phones
			(cph_tnt_id, cph_cpg_id, cph_phone_number, cph_include)
				select '.$config['tnt_id'].',
						 '.$dest_cpg.',
						 cph_phone_number,
						 cph_include
				from mkt_campaigns_phones
				where cph_tnt_id='.$config['tnt_id'].'
					and cph_cpg_id='.$src_cpg.'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}
	/** Cette fonction permet d'ajouter des numéros de téléphone a une campagne
	 *  \param $cpg_id Identifiant de la campagne
	 *	\param $phone_number Numéro de téléphone mobile a enregistrer
 	 *  \param $include facultatif, par defaut à true le numéro et inclus dans la campagne ou false le numéro n'est pas inclue
	 *
	 *	\return retourne l'identifiant de la ligne insérer, false si échec
	 */
	public static function addCampaignsPhone( $cpg_id, $phone_number, $include=true ){

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			replace into mkt_campaigns_phones
				(cph_tnt_id, cph_cpg_id, cph_phone_number, cph_include)
			  values
			  	('.$config['tnt_id'].', '.$cpg_id.',"'.conv_france_international_number($phone_number).'", '.($include?1:0).')
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer un numéros de téléphone associé a une campagne
	 *	\param $cpg_id Identifiant de la campagne
	 *	\param $phoneNumber Numéros de téléphone mobile a supprimer
	 *
	 *	\return true si succès, false si échec
	 */
	public static function delCampaignsPhones( $cpg_id, $phoneNumber='' ){

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from mkt_campaigns_phones
			where cph_tnt_id='.$config['tnt_id'].'
				and cph_cpg_id='.$cpg_id.'
		';
		if( trim($phoneNumber) != '' ){
			$sql .= ' and cph_phone_number="'.conv_france_international_number($phoneNumber).'" ';
		}

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet d'ajouter des utilisateur a une campagne
	 *  \param $cpg_id Identifiant de la campagne
	 *	\param $usr_id Identifiant d'un utilisateur
	 *	\param $include Facultatif, par default à true, détermine si un utilisateur et inclus dans la campagne ou est exclu
	 *
	 *	\return Retourne l'identifiant de la l igne inséré, false si erreur
	 */
	public static function addCampaignsUser($cpg_id, $usr_id, $include=true){
		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}
		if( !is_numeric($usr_id) || $usr_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			replace into mkt_campaigns_users
				(cur_tnt_id, cur_cpg_id, cur_usr_id, cur_include)
			  values
			  	('.$config['tnt_id'].', '.$cpg_id.','.$usr_id.', '.($include?1:0).')
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de copier des compte client d'une campagne a une autre
	 *	\param $src_cpg Identifiant de la campagne que l'on souhaite copier
	 *  \param $dest_cpg Identifiant de la campagne où l'on veux coller
	 *
	 *	\return true si succès, false si échec
	 */
	public static function copyCampaignsUsers( $src_cpg, $dest_cpg ){
		if( !is_numeric($src_cpg) || $src_cpg<=0 ){
			return false;
		}
		if( !is_numeric($dest_cpg) || $dest_cpg<=0 ){
			return false;
		}
		global $config;
		$sql = '
			replace into mkt_campaigns_users
			(cur_tnt_id, cur_cpg_id, cur_usr_id, cur_include)
				select '.$config['tnt_id'].',
						 '.$dest_cpg.',
						 cur_usr_id,
						 cur_include
				from mkt_campaigns_users
				where cur_tnt_id='.$config['tnt_id'].'
					and cur_cpg_id='.$src_cpg.'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de retourner les utilisateur associer a une campagne
	 *	\param $cpg_id Obligatoire, Identifiant de la campagne
	 *
	 *	\return un résultat de requête mysql avec les colonnes suivante
	 * 					- value l'indetifiant de l'utilisateur
	 * 					- include
	 * 					- label
	 */
	public static function getCampaignsUsers( $cpg_id ){

		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}

		global $config;

		$sql = '
			select cur_usr_id as "value", cur_include as include
			from mkt_campaigns_users
			where cur_tnt_id='.$config['tnt_id'].'
				and cur_cpg_id='.$cpg_id.'
		';

		$r = ria_mysql_query($sql);

		if( !$r || !ria_mysql_num_rows($r) ){
			return false;
		}

		return $r;
	}

	/** Cette fonction permet de supprimer un utilisateur d'une campagne
	 *	\param $cpg_id Obligatoire, identifiant de la campagne
	 *	\param $usr_id Facultatif, identifiant de l'utilisateur à supprimé. Si non renseigné ou 0, tous les utilisateurs sont supprimés.
	 *	\return true en cas de succès, false en cas d'échec
	 */
	public static function delCampaignsUsers( $cpg_id, $usr_id=0 ){
		if( !is_numeric($cpg_id) || $cpg_id<=0 ){
			return false;
		}
		if( !is_numeric($usr_id) || $usr_id<0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from mkt_campaigns_users
			where cur_tnt_id='.$config['tnt_id'].'
				and cur_cpg_id='.$cpg_id.'
		';

		if( $usr_id ){
			$sql .= ' and cur_usr_id='.$usr_id.' ';
		}

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}

	public static function copyCampaignsSegments( $src_cpg, $dest_cpg ){
		if( !is_numeric($src_cpg) || $src_cpg<=0 ){
			return false;
		}
		if( !is_numeric($dest_cpg) || $dest_cpg<=0 ){
			return false;
		}
		global $config;
		$sql = '
			replace into mkt_campaigns_segments
			(cps_tnt_id, cps_cpg_id, cps_seg_id, cps_include)
				select '.$config['tnt_id'].',
						 '.$dest_cpg.',
						 cps_seg_id,
						 cps_include
				from mkt_campaigns_segments
				where cps_tnt_id='.$config['tnt_id'].'
					and cps_cpg_id='.$src_cpg.'
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			return false;
		}

		return true;
	}
	/** Cette fonction permet de récupérer les segments liés a une campagne
	 * \param $cpg_id Identifiant de la campagne
	 * \param $seg_id Optionnel, identifiant du segment
	 * \param $include Optionnel, defaut à null pour tous récupérer, true pour récupérer que les
	 * segment inclues, false pour les exclues
	 *
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonne suivante :
	 *            - tnt_id : identifiant du tenant
	 *            - cpg_id : identifiant de la campagne
	 *            - seg_id : identifiant du segment
	 *            - include : si le segment est inclue ou non
	 */
	public static function getCampaignsSegments( $cpg_id, $seg_id = 0, $include = null ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $seg_id ) || $seg_id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select cps_tnt_id as tnt_id, cps_cpg_id as cpg_id, cps_seg_id as seg_id, cps_include as include
			from mkt_campaigns_segments
			where cps_tnt_id = '.$config['tnt_id'].'
				and cps_cpg_id = '.$cpg_id.'
		';

		if( $seg_id ){
			$sql .= ' and cps_seg_id = '.$seg_id;
		}

		if( !is_null( $include ) ){
			if( $include ){
				$sql .= ' and cps_include = 1';
			}else{
				$sql .= ' and cps_include = 0';
			}
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter un segment à la campagne
	 * \param $cpg_id identifiant de la campagne
	 * \param $seg_id Identifiant du segment
	 * \param $include Optionnel, true or false inclu ou pas
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function addCampaignsSegments( $cpg_id, $seg_id, $include = true ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $seg_id ) || $seg_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			replace into mkt_campaigns_segments
				( cps_tnt_id, cps_cpg_id, cps_seg_id, cps_include )
			values
				( '.$config['tnt_id'].', '.$cpg_id.', '.$seg_id.', '.( $include ? '1' : '0' ).')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}
	/** Cette fonction permte de modifier l'inclusion d'un segment pour une campagne
	 * \param $cpg_id  Identifiant de la campagne
	 * \param $seg_id  Identifiant du segment
	 * \param $include True ou False, si le segment et inclue ou exclue
	 *
	 * \return true si succès, false si échec
	 */
	public static function updateCampaignsSegments( $cpg_id, $seg_id, $include ){

		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $seg_id ) || $seg_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_campaigns_segments set
				cps_include = '.( $include ? '1' : '0' ).'
			where cps_tnt_id = '.$config['tnt_id'].'
				and cps_cpg_id = '.$cpg_id.'
				and cpg_seg_id = '.$seg_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer un segment pour une campagne
	 * \param $cpg Identifiant de la campagne
	 * \param $seg_id Identifiant du segment
	 *
	 * \return true si succès, false si échec
	 */
	public static function delCampaignsSegments( $cpg, $seg_id=0 ){
		if( !is_numeric( $cpg ) || $cpg <= 0 ){
			return false;
		}

		if( !is_numeric( $seg_id ) || $seg_id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from mkt_campaigns_segments
			where cps_tnt_id = '.$config['tnt_id'].'
				and cps_cpg_id = '.$cpg.'
		';

		if( $seg_id ){
			$sql .= ' and cps_seg_id = '.$seg_id.' ';
		}
		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de récupérer les catégories de newsletter pour une campagne
	 * \param  $cpg_id  Identifiant de la campagne
	 * \param  $cnt_id  Optionnel, identifiant de la catégorie de newsletter
	 * \param  $include Optionnel, par defaut a null pour tous récupérer, true que les catégories
	 * inclusent, false les exclusent
	 *
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonne suivantes :
	 *            - tnt_id : identifiant du tenant
	 *            - cpg_id : identifiant de la campagne
	 *            - cnt_id : identifiant de la catégorie de newsletter
	 *            - include : si la catégorie est incluse ou non
	 */
	public static function getCampaignsNewslettersCat( $cpg_id, $cnt_id = 0, $include = null ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $cnt_id ) || $cnt_id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select cpn_tnt_id as tnt_id, cpn_cpg_id as cpg_id, cpn_cnt_id as cnt_id, cpn_include as include
			from mkt_campaigns_newsletter_cat
			where cpn_tnt_id = '.$config['tnt_id'].'
				and cpn_cpg_id = '.$cpg_id.'
		';
		if( $cnt_id ){
			$sql .= ' and cpn_cnt_id = '.$cnt_id;
		}

		if( !is_null( $include ) ){
			if( $include ){
				$sql .= ' and cpn_include = 1';
			}else{
				$sql .= ' and cpn_include = 0';
			}
		}
		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter une catégorie de newsletter à une campagne
	 * \param $cpg_id Identifiant de la campagne
	 * \param $cnt_id Identifiant de la ctégorie de newsletter
	 * \param $include True ou False, si le segment et inclue ou exclue
	 *
	 * \return true si succès, false si échec
	 */
	public static function addCampaignsNewslettersCat( $cpg_id, $cnt_id, $include ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $cnt_id ) || $cnt_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			insert into mkt_campaigns_newsletter_cat
				( cpn_tnt_id, cpn_cpg_id, cpn_cnt_id, cpn_include )
			values
				( '.$config['tnt_id'].', '.$cpg_id.', '.$cnt_id.', '.( $include ? '1' : '0' ).')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de modifier l'inclusion d'une catégorie de newsletter
	 * \param $cpg_id Identifiant de la campagne
	 * \param $cnt_id Identifiant de la catégorie de newsletter
	 * \param $include True ou False, si la catégorie est incluse ou non
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function updateCampaignsNewsletterCat( $cpg_id, $cnt_id, $include ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $cnt_id ) || $cnt_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			update mkt_campaigns_newsletter_cat set
				cpn_include = '.( $include ? '1' : '0' ).'
			where cpn_tnt_id = '.$config['tnt_id'].'
				and cpn_cpg_id = '.$cpg_id.'
				and cpn_cnt_id = '.$cnt_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer une catégorie de newsletter
	 * \param $cpg_id Identifiant de la campagne
	 * \param $cnt_id Identifiant de la catégorie de newsletter
	 *
	 * \return true si succès, false si échec
	 */
	public static function delCampaignsNewsletterCat( $cpg_id, $cnt_id ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $cnt_id ) || $cnt_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			delete from mkt_campaigns_newsletter_cat
			where cpn_tnt_id = '.$config['tnt-id'].'
				and cpn_cpg_id = '.$cpg_id.'
				and cpn_cnt__id = '.$cnt_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de récupérer tout les objet qui on été vérifier pour une campagne
	 * \param  $cpg_id Identifiant de la campagne
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonnes suivantes :
	 *                     - tnt_id : identifiant du tenant
	 *                     - cpg_id : identifiant de la campagne
	 *                     - obj_id : identifiant de l'objet vérifier
	 *                     - verified : si vérifier ou pas
	 */
	public static function getCampaignsObjects( $cpg_id ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			select mco_tnt_id as tnt_id, mco_cpg_id as cpg_id, mco_obj_id as obj_id, mco_usr_id as usr_id, mco_verified as mco_verified
			from mkt_campaigns_objects
			where mco_tnt_id = '.$config['tnt_id'].'
				and mco_cpg_id = '.$cpg_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet d'ajouter un objet vérifier à une campagne
	 *	\param $cpg_id Obligatoire, Identifiant de la campagne
	 *	\param $trg_id Obligatoire, identifiant d'un trigger
	 * 	\param $obj_id Obligatoire, Identifiant de l'objet
	 *	\param $usr_id Obligatoire, identifiant d'un compte utilisateur
	 *	\return l'identifiant de l'objet en cas de succès, false en cas d'échec
	 */
	public static function addCampaignsObjects( $cpg_id, $trg_id, $obj_id, $usr_id ){
		if( !is_numeric( $cpg_id ) || $cpg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $trg_id ) || $trg_id <= 0 ){
			return false;
		}

		if( !is_numeric( $obj_id ) || $obj_id <= 0 ){
			return false;
		}

		if( !is_numeric( $usr_id ) || $usr_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			insert ignore into mkt_campaigns_objects
				(mco_tnt_id, mco_cpg_id, mco_trg_id, mco_obj_id, mco_usr_id, mco_verified)
			values
				('.$config['tnt_id'].', '.$cpg_id.', '.$trg_id.', '.$obj_id.', '.$usr_id.', 1)
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $obj_id;
	}
}

<?php

require_once('define.inc.php');
require_once('obj_position.inc.php');
require_once('orders.inc.php');
require_once('promotions.inc.php');
require_once('strings.inc.php');

/** \defgroup model_ord_returns Gestion des retours
 * 	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des retours.
 *	Les retours sont stockés dans la table ord_returns et une commande est attachée à chaque retour (obligatoire).
 *	Chaque retour possède un état. Les états sont décrits dans la table ord_returns_states.
 *	Lorsque le client crée un retour, celui-ci est dans l'état création pour lui permettre de définir les produits à retourner.
 *	Excepté dans cet état, il n'est pas permis de retirer/ajouter un produit du retour.
 *
 *	Le client peut choisir le mode de retour parmi les modes proposés.
 *	La liste exhaustive est dans la table ord_returns_modes, et les modes disponibles pour chaque site dans la table ord_returns_modes_allowed
 *	Si le client choisit le mode "Avoir", un code promotion du montant du produit retourné est créé. Le code promotion est associé à la ligne dans ord_returns_products.
 *
 *	Une fois le retour validé, il passe à l'état demande effectuée. L'administrateur a la possibilité dans cet état d'envoyer une étiquette prépayée au client.
 *	L'état suivant est "en attente des produits". Lors du passage à cette étape, la procédure pour retourner les produits est formulée au client.
 *	L'administrateur peut accepter ou refuser chaque retour de produit dans l'administration.
 *
 *	Chaque changement d'état du retour envoie un email au client pour l'informer de ce changement.
 *
 *	Important : Dans l'email correspondant à l'état en attente des produits, un pdf est créé pour le bon de retour.
 *	Le logo du site est récupéré dans config[email_header_html]. Les attributs src, width et height doivent s'y trouver.
 *	De plus, l'image doit être au format jpg car un png transparent génère une erreur.
 *
 *	N'oubliez pas de créez une ligne email-return dans cfg_emails
 *
 *	@{
 */

/**	Cette fonction crée un retour.
 *	@param int $ord Obligatoire, Identifiant de la commande
 *  @return id retour en cas de succès, false en cas d'échec
 */
function ord_returns_add( $ord ){
	if( !ord_orders_exists($ord) ) return false;
	global $config;

	$order = ria_mysql_fetch_array( ord_orders_get(0, $ord) );
	if( !in_array($order['state_id'], ord_returns_orders_states_allowed_get()) ) return false;

	// vérifie qu'il reste des produits non retournés dans la commande
	if( !ord_returns_keeped_products_get_count($order['id']) ){
		return false;
	}
	$sql = '
		insert into ord_returns (return_tnt_id, return_date, return_products, return_total_ht, return_total_ttc, return_user_id, return_ord_id, return_states_id)
		values ('.$config['tnt_id'].', now(), 0, 0, 0, '.$order['user'].', '.$order['id'].', '.ORD_RETURNS_STATE_CREATE.')
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();
}

// \cond onlyria
/**	Cette fonction permet l'enregistrement d'un nouveau bon de retour à partir de l'api de synchronisation
 * 	Ne pas utilisé hors cas de synchronisation
 *	@param int $usr Identifiant de l'utilisateur concerné
 *	@param $piece Numéro de pièce sage
 *	@param string $ref Référence sage
 *	@param $date Date/Heure de création du bon
 *	@param $state Identifiant d'un statut
 *	@return int l'identifiant du nouveau br en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_add_sage( $usr, $piece, $ref, $date, $state ){

	$debug = false; // active la trace de débugage

	if( !gu_users_exists( $usr ) ){
		if( $debug ){
			error_log('[ord_returns_add_sage] client inexistant');
		}
		return false;
	}

	if( !ord_states_exists( $state ) ){
		if( $debug ){
			error_log('[ord_returns_add_sage] état invalide');
		}
		return false;
	}
	if( !isdateheure( $date ) ){
		if( $debug ){
			error_log('[ord_returns_add_sage] date invalide');
		}
		return false;
	}

	$date = dateheureparse( $date );

	global $config;

	if( trim($piece) ){
		// si une bon de livraison non masqué existe avec ce numéro de pièce on la supprime
		$rexists = ria_mysql_query('select return_id from ord_returns where return_tnt_id='.$config['tnt_id'].' and return_piece="'.addslashes($piece).'" and return_date_deleted is null');
		if( $rexists && ria_mysql_num_rows($rexists) ){
			$exists = ria_mysql_fetch_assoc($rexists);
			if( !ord_returns_del($exists['return_id']) ){
				return false;
			}
		}
	}

	$sql = '
		insert into ord_returns
			( return_tnt_id, return_user_id, return_piece, return_ref, return_date, return_states_id )
		values
			( '.$config['tnt_id'].', '.$usr.', "'.addslashes($piece).'", "'.addslashes($ref).'", "'.$date.'", '.$state.' )
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
			error_log('[ord_returns_add_sage] echec de la requête d\'insertion (erreur : '.mysql_error().')');
		return false;
	}

	return ria_mysql_insert_id();

}

/** Cette fonction met à jour la référence d'un bon de retour.
 *
 *	@param $return_id Identifiant du bon de retour
 *	@param string $ref nouvelle référence du bon de retour
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_ref_update( $return_id, $ref ){
	if( !is_numeric( $return_id ) || $return_id<=0 ) return false;
	if( !ord_returns_exists( $return_id ) ) return false;
	global $config;

	return ria_mysql_query('update ord_returns set return_ref=\''.addslashes($ref).'\' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}

/** Cette fonction met à jour la date d'un bon de retour.
 *
 *	@param $return_id Identifiant du bon de retour
 *	@param $date nouvelle date du bon de retour
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_update_date( $return_id, $date ){
	if( !is_numeric( $return_id ) || $return_id<=0 ) return false;
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !isdateheure($date) ){
		return false;
	}
	global $config;

	return ria_mysql_query('update ord_returns set return_date="'.$date.'" where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}


// \cond onlyria
/** Cette fonction met à jour du nombre de produits dans le bon de retour.
 *
 *	\param $return_id Identifiant du bon de retour
 *	\param $products nombre de produits
 *
 *	\return true en cas de succès
 *	\return false en cas d'échec
 */
function ord_returns_update_products( $return_id, $products ){
	if( !is_numeric( $return_id ) || $return_id<=0 ) return false;
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !is_numeric( $products ) || $products<0 ) return false;
	global $config;

	return ria_mysql_query('update ord_returns set return_products="'.$products.'" where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

/** Cette fonction permet la transmission d'un bon de retour d'un utilisateur à un autre.
 *	Son usage est réservé aux seuls administrateurs et représentants.
 *
 *	@param $return_id Identifiant du bon de retour
 *	@param int $user Identifiant du nouveau propriétaire du bon de retour
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_update_user( $return_id, $user ){
	// Contrôles
	if( !is_numeric( $return_id ) || $return_id<=0 ) return false;
	if( !is_numeric( $user ) || $user<=0 ) return false;
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !gu_users_exists( $user ) ) return false;
	global $config;

	// Procède à la mise à jour
	return ria_mysql_query( 'update ord_returns set return_user_id='.$user.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id );
}

/**	Cette fonction permet de marquer l'entête d'un retour comme n'étant pas/plus synchronisé avec la gestion commerciale
 *	@param $return_id identifiant d'un retour
 *	@return le nombre de lignes affectées en cas de succès, false en cas d'échec
 */
function ord_returns_set_need_sync( $return_id ){
	if( !is_numeric($return_id) ) return false;
	global $config;

	return ria_mysql_query('update ord_returns set return_need_sync=1 where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

/**	Cette fonction supprime un retour.
 *	@param int $id Obligatoire, Identifiant du retour
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_del( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$rret = ord_returns_get($id);
	if( !$rret || !ria_mysql_num_rows($rret) ) return false;

	// on ne peut supprimer un retour qu'en mode création
	$ret = ria_mysql_fetch_array( $rret );
	if( $ret['states_id'] != ORD_RETURNS_STATE_CREATE ) return false;

	// supprime le retour
	$sql = '
		update ord_returns set return_date_deleted=now()
		where return_tnt_id = '.$config['tnt_id'].'
		and return_id = '.$id.'
	';
	if ( !ria_mysql_query($sql) ) return false;

	return true;
}

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un bon de retour.
 *	\param $id Identifiant ou tableau d'identifiant de bon de retour.
 *	\return True en cas de succès, False en cas d'échec.
 */
function ord_returns_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_date_modified = now()
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction crée et renvoie un pdf relatif au bon de commande pour un retour donné
 *  @param $return_id Obligatoire, identifiant de retour
 *  @return Objet MyPDF
 */
function ord_returns_bon_pdf_create( $return_id ) {
	require_once('mypdf/MyPDF.php');
	require_once('mypdf/MyPDF_GridPanel.php');
	require_once('mypdf/MyPDF_Image.php');
	require_once('mypdf/MyPDF_Text.php');
	require_once('mypdf/MyPDF_VerticalPanel.php');

	// récupère le retour
	$return = ord_returns_get($return_id);
	if( !($return = ria_mysql_fetch_array($return)) ){
		return false;
	}

	// récupère l'adresse du client
	$user = gu_users_get($return['user_id']);
	if( !$user ){
		return false;
	}
	if( !($user = ria_mysql_fetch_array($user)) ){
		return false;
	}

	$userAddress = gu_adresses_get($return['user_id'], $user['adr_invoices']);
	if( !$userAddress ){
		return false;
	}
	if( !($userAddress = ria_mysql_fetch_array($userAddress)) ){
		return false;
	}
	$t = $userAddress['address1']."\n";

	if( $userAddress['address2'] ){
		$t .= $userAddress['address2']."\n";
	}
	if( $userAddress['address3'] ){
		$t .= $userAddress['address3']."\n";
	}
	$t .= $userAddress['zipcode'].' - '.$userAddress['city']."\n";
	$t .= $userAddress['country'];
	$userAddress = $t;

	// récupère les états
	$rstates = ord_returns_states_get();
	if( !$rstates ){
		return false;
	}
	while( $dat=ria_mysql_fetch_array($rstates) ){
		$states[$dat['id']] = $dat;
	}

	// récupère les états produit
	$rstates = ord_returns_products_states_get();
	if( !$rstates ){
		return false;
	}
	$prdstates = array();
	while ($dat = ria_mysql_fetch_array($rstates)) {
		$prdstates[$dat['id']] = $dat;
	}

	// récupère les raisons
	$rreasons = ord_returns_reasons_get();
	$reasons = array();
	while ($dat = ria_mysql_fetch_array($rreasons)) {
		$reasons[$dat['id']] = $dat;
	}

	// récupère les modes
	$rmodes = ord_returns_modes_allowed_get();
	$modes = array();
	while ($dat = ria_mysql_fetch_array($rmodes)) {
		$modes[$dat['id']] = $dat;
	}

	global $config;

	// récupère l'adresse
	$owner = site_owner_get( $return['wst_id_ord'] );
	if( !$owner ){
		return false;
	}
	$t = $owner['name']."\n";
	$t .= $owner['address1']."\n";
	if( $owner['address2'] ){
		$t .= $owner['address2']."\n";
	}
	$t .= $owner['zipcode'].' - '.$owner['city'];
	$address = $t;

	$pdf = new MyPDF();
	$table = new MyPDF_GridPanel(array('rows' => 2, 'cols' => 2));
	{
		// top left
		{
			// logo
			$cell = $table->getCell(array('row' => 0, 'col' => 0));
			$vpanel = new MyPDF_VerticalPanel(array('rows' => 3));
			{
				// récupère le logo
				if( isset($config['email_html_header']) || isset($config['email_html_header_order']) ) {
					$t = isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'];
					$p = strpos($t, '<img ');
					if( $p!==false ) {
						$p += 5;
						$q = strpos($t, '/>', $p);
						if( $q!==false ) {
							$t = explode(' ', substr($t, $p, $q-$p));
							$attr = array();
							foreach( $t as $u ) {
								$u = explode('=', $u);
								if( count($u)===2 ) {

									if( !preg_match('#^"(.*)"$#', $u[1], $m) )
										continue;

									$attr[$u[0]] = $m[1];
								}
							}

							$cm = 0.1937;
							$logo = new MyPDF_Image(array('src' => $config['img_dir'].'/../returns/header.jpg', 'width' => 319 * $cm, 'height' => 150 * $cm));
							$vpanel->getCell(array('row' => 0))->setWidget(array('widget' => $logo));
						}
					}
				}

				$vpanel->getCell(array('row' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => "\n".'Une question, un conseil :'."\n", 'width' => 100))));

				$infos = new MyPDF_GridPanel(array('rows' => 2, 'cols' => 2));
				{
					$infos->getCell(array('row' => 0, 'col' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Tél :'))));
					$infos->getCell(array('row' => 0, 'col' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => $owner['phone']))));
					$infos->getCell(array('row' => 1, 'col' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Email :'))));
					$infos->getCell(array('row' => 1, 'col' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => $owner['email']."\n"))));
				}
				$vpanel->getCell(array('row' => 2))->setWidget(array('widget' => $infos));
			}
			$cell->setWidget(array('widget' => $vpanel));
		}

		// top right
		{
			$cell = $table->getCell(array('row' => 0, 'col' => 1));
			$vpanel = new MyPDF_VerticalPanel(array('rows' => 3));
			{
				$vpanel->getCell(array('row' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Bons de retour', 'bold' => true, 'width' => 100, 'align' => 'C', 'fontSize' => 20))));
				$vpanel->getCell(array('row' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => "\n".'À joindre impérativement à votre colis'."\n".'et à retourner à :'."\n", 'align' => 'C', 'width' => 75))));
				$vpanel->getCell(array('row' => 2))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $address, 'bold' => true, 'align' => 'C', 'width' => 75))));
			}
			$cell->setWidget(array('widget' => $vpanel));
		}

		// bottom left
		{
			$cell = $table->getCell(array('row' => 1, 'col' => 0));
			$vpanel = new MyPDF_VerticalPanel(array('rows' => 2));
			{
				$vpanel->getCell(array('row' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Coordonnées client', 'bold' => true))));
				$infos = new MyPDF_VerticalPanel(array('rows' => 2));
				{
					$infos->add(array('element' => new MyPDF_Text(array('content' => $user['title_name'].' '.$user['adr_lastname'].' '.$user['adr_firstname']))));
					$infos->add(array('element' => new MyPDF_Text(array('content' => $userAddress))));
				}
				$vpanel->getCell(array('row' => 1))->setWidget(array('widget' => $infos));
			}
			$cell->setWidget(array('widget' => $vpanel));
		}

		// bottom right
		{
			$cell = $table->getCell(array('row' => 1, 'col' => 1));
			$infos = new MyPDF_GridPanel(array('rows' => 3, 'cols' => 2));
			{
				$infos->getCell(array('row' => 0, 'col' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Num. Retour :'))));
				$infos->getCell(array('row' => 0, 'col' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => str_pad($return['id'], 8, '0', STR_PAD_LEFT)))));
				$infos->getCell(array('row' => 1, 'col' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Date de la demande de retour :'))));
				$infos->getCell(array('row' => 1, 'col' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => dateheureunparse($return['date'], false)))));
				$infos->getCell(array('row' => 2, 'col' => 0))->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Num. Commande :'))));
				$infos->getCell(array('row' => 2, 'col' => 1))->setWidget(array('widget' => new MyPDF_Text(array('content' => str_pad($return['ord_id'], 8, '0', STR_PAD_LEFT)))));
			}
			$cell->setWidget(array('widget' => $infos));
		}
	}
	$pdf->add(array('element' => $table));

	$pdf->add(array('element' => new MyPDF_Text(array('content' => "\n".'Détail du retour :'."\n", 'bold' => true))));

	// Détail du retour
	$table = new MyPDF_GridPanel(array('rows' => 1, 'cols' => 6, 'fontSize' => 9));
	{
		// header
		{
			$table->getCell(array('row' => 0, 'col' => 0))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Référence', 'align' => 'C', 'bold' => true))));
			$table->getCell(array('row' => 0, 'col' => 1))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Désignation', 'align' => 'C', 'bold' => true))));
			$table->getCell(array('row' => 0, 'col' => 2))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Mode', 'align' => 'C', 'bold' => true))));
			$table->getCell(array('row' => 0, 'col' => 3))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Raison', 'align' => 'C', 'bold' => true))));
			// $table->getCell(array('row' => 0, 'col' => 4))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'État', 'align' => 'C', 'bold' => true))));
			$table->getCell(array('row' => 0, 'col' => 5))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => 'Total', 'align' => 'C', 'bold' => true))));
		}
		// body

		$rows = ord_returns_returned_products_get($return_id);

		if( !$rows ){
			return false;
		}
		$total_ttc = 0;
		$lineIndex = 0;
		while ($row = ria_mysql_fetch_array($rows)) {
			$table->addRow();
			$table->getCell(array('row' => $lineIndex+1, 'col' => 0))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $row['ref'], 'width' => 25))));

			$name = $row['name'];
			if( $row['lot'] ) $name .= ' (Lot '.$row['lot'].')';
			$table->getCell(array('row' => $lineIndex+1, 'col' => 1))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $name, 'width' => 40))));

			$table->getCell(array('row' => $lineIndex+1, 'col' => 2))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $modes[$row['mode_id']]['name'], 'width' => 28))));

			$vReason = new MyPDF_VerticalPanel();
			if( $row['reason_id'] )
				$vReason->add(array('element' => new MyPDF_Text(array('content' => $reasons[$row['reason_id']]['name'], 'bold' => true, 'width' => 60))));

			$vReason->add(array('element' => new MyPDF_Text(array('content' => $row['reason_detail'], 'width' => 60))));
			$table->getCell(array('row' => $lineIndex+1, 'col' => 3))->setBorder()->setWidget(array('widget' => $vReason));

			// $table->getCell(array('row' => $lineIndex+1, 'col' => 4))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $prdstates[$row['state_id']]['name'], 'width' => 30))));

			$subtotal = $row['price_ttc'] * $row['qte'];
			if( $row['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE){
				$through = true;
			}else{
				$through = false;
				$total_ttc += $subtotal;
			}
			$table->getCell(array('row' => $lineIndex+1, 'col' => 5))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => number_format(round($subtotal, 2), 2, ',', ' ').'€', 'width' => 17, 'align' => 'R', 'through' => $through))));
			$lineIndex++;
		}

		// total
		{
			$table->addRow();
			$table->getCell(array('row' => $lineIndex + 1, 'col' => 5))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => number_format(round($total_ttc, 2), 2, ',', ' ').'€', 'align' => 'R'))));
		}
	}
	$pdf->add(array('element' => $table));

	// Rappel

	$pdf->add(array('element' => new MyPDF_Text(array('content' => 'Rappel :', 'bold' => true))));
	{
		$t = '- Tous les produits doivent être retournés dans leur emballage et leur état d\'origine.'."\n";
		$t .= '- Veuillez imprimer ce document et le glisser dans votre colis après avoir vérifié que la totalité des articles'."\n".'mentionnés ci-dessus figure bien dans votre colis.'."\n";
		$t .= '- Le paquet devra être renvoyé à l\'adresse suivante :'."\n";
	}
	$pdf->add(array('element' => new MyPDF_Text(array('content' => $t, 'width' => 200))));

	$table = new MyPDF_GridPanel(array('rows' => 1, 'cols' => 1));
	$table->getCell(array('row' => 0, 'col' => 0))->setBorder()->setWidget(array('widget' => new MyPDF_Text(array('content' => $address, 'width' => 200, 'align' => 'C', 'bold' => true))));
	$pdf->add(array('element' => $table));
	{
		$t = "\n".'Vous pouvez suivre l\'évolution de votre retour depuis votre espace client.';
	}
	$pdf->add(array('element' => new MyPDF_Text(array('content' => $t, 'width' => 200))));

	return $pdf;
}

/** Cette fonction Renvoie la liste des règles pour les catégories.
 *  Est utilisé pour la modification des règles et difficile à lire car seuls les changements de règle sont enregistrés
 *  Utiliser ord_returns_category_allow_returns(cat_id) pour savoir si la catégorie autorise les retours
 *  @return bool false si échec, sinon tableau des règles (id => rule) avec
 *  			-	id : Identifiant de la catégorie
 * 				-	rule : true si la catégorie et ses enfants accèptent les retours, false sinon
 */
function ord_returns_categories_rules_get(){
	global $config;

	$sql = 'select cat_id as id, cat_rule as rule from ord_returns_categories where cat_tnt_id = '.$config['tnt_id'];
	$req = ria_mysql_query($sql);
	if( !$req ){
		return false;
	}
	$rules = array();
	while( $dat=ria_mysql_fetch_array($req) ){
		$rules[$dat['id']] = $dat['rule'];
	}
	return $rules;
}

/** Cette fonction écrase la configuration actuelle et la remplace par la nouvelle.
 *  Par défaut, toutes les catégories sont autorisées. Si une règle change, la règle est ajoutée dans la table et s'applique récurcivement à tous ses enfants.
 *  Le booléen allowed permet de connaître la règle à appliquer.
 *  Pour une catégorie donnée, la règle à appliquer est celle de la catégorie ou du parent le plus proche dans la hiérarchie.
 *  @param $tab Obligatoire, tableau d'identifiants des catégories acceptées (doit contenir TOUTES les catégories acceptées sinon les catégories manquantes seront consisérées comme non autorisées)
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_categories_set( $tab ) {
	// récupère l'arbre des catégories
	$hreq = prd_categories_get_all();
	if( !$hreq )
		return false;

	$tree = array();
	while( $dat=ria_mysql_fetch_array($hreq) ){
		$id = $dat['id'];
		$parent_id = $dat['parent_id'];
		if( !$parent_id )
			$parent_id = 0;

		if( !isset($tree[$id]) )
			$tree[$id] = array('parent' => null, 'children' => array(), 'rule' => false);

		$tree[$id]['parent'] = $parent_id;

		if( !isset($tree[$parent_id]) )
			$tree[$parent_id] = array('parent' => null, 'children' => array(), 'rule' => false);

		$tree[$parent_id]['children'][] = $id;
	}
	$root =& $tree[0];
	if( !isset($root) )
		$root = array('parent' => null, 'children' => array(), 'rule' => false);

	// affecte les règles et vérifie que les catégories sont valides
	foreach( $tab as $id ){
		// catégorie n'existe pas ou accès interdit
		if( !isset($tree[$id]) )
			return false;

		$tree[$id]['rule'] = true;
	}

	// calcule la nouvelle table
	$rows = array();
	ord_returns_categories_set_calc_tree($tree, 0, $rows);

	// récupère l'ancienne table
	$rules = ord_returns_categories_rules_get();

	// calcule les opérations à effectuer
	$insert = array();
	$update = array();
	$delete = array();
	foreach ($rows as $cat => $rule) {
		if( isset($rules[$cat]) ){
			if( $rules[$cat]!=$rows[$cat] )
				$update[$cat] = $rule;
		}
		else
			$insert[$cat] = $rule;
	}
	foreach( $rules as $cat => $rule ){
		if( !isset($rows[$cat]) )
			$delete[] = $cat;
	}

	global $config;

	// delete
	$t = implode(',', $delete);
	if( $t ){
		if( !ria_mysql_query('
			delete
			from ord_returns_categories
			where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id in ('.$t.')
		') )
			return false;
	}
	// update
	foreach( $update as $cat => $rule ){
		if( !ria_mysql_query('
			update ord_returns_categories
			set cat_rule = '.($rule*1).'
			where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat.'
		') )
			return false;
	}
	// insert
	foreach( $insert as $cat => $rule ) {
		if( !ria_mysql_query('insert into ord_returns_categories (cat_tnt_id, cat_id, cat_rule) values ('.$config['tnt_id'].', '.$cat.', '.($rule*1).')') )
			return false;
	}

	return true;
}

/** Fonction auxiliaire à ord_returns_categories_set qui permet de calculer récurcivement les lignes à insérer dans la table. Parcourt l'arbre en profondeur.
 *
 *  @param $tree Obligatoire, l'arbre des catégories
 *  @param int $id Obligatoire, identifiant du noeud courant
 *  @param $rows Obligatoire, lignes à insérer dans la table (cat_id => rule)
 *
 */
function ord_returns_categories_set_calc_tree( $tree, $id, &$rows ) {
	$nod = $tree[$id];
	$parent = ($nod['parent'] !== null) ? $tree[$nod['parent']] : array('rule' => true);
	if( $nod['rule']!=$parent['rule'] ){
		$rows[$id] = $nod['rule'];
	}
	$children = $nod['children'];
	foreach( $children as $child ){
		ord_returns_categories_set_calc_tree( $tree, $child, $rows );
	}
}

/** Cette fonction renvoie un booléen indiquant si les retours de marchandise sont autorisés dans la catégorie de produits demandée
 *  @param int $cat_id Obligatoire, identifiant de la catégorie
 *	@param bool|array $cache Facultatif, résultat d'un appel à la fonction ord_returns_category_allow_returns_cache
 *  @return bool true si la catégorie autorise les retours, false sinon. Renvoie null si une erreur s'est produite
 *	@todo Le paramètre $cache n'a pas d'intérêt réel, il pourrait être supprimé pour simplifier l'utilisation de la fonction
 */
function ord_returns_category_allow_returns( $cat_id, $cache=false ){
	if( !is_numeric($cat_id) ){
		return false;
	}

	global $config;

	if( !is_array($cache) ){
		$cache = ord_returns_category_allow_returns_cache();
	}

	if( !is_array($cache) || !ria_array_key_exists(array('one_rule', 'ar_cat_rules_ok', 'ar_cat_rules_ko', 'ar_hierarchy'), $cache) ){
		return false;
	}

	$one_rule 			= $cache['one_rule'];
	$ar_cat_rules_ok 	= $cache['ar_cat_rules_ok'];
	$ar_cat_rules_ko 	= $cache['ar_cat_rules_ko'];
	$ar_hierarchy 		= $cache['ar_hierarchy'];

	if( $cat_id === 0 ){
		return ( !$one_rule ? true : false );
	}

	if( !$one_rule ){
		return true;
	}

	$is_include = false;
	$ar_key_cats = array_keys( $ar_hierarchy );

	// S'il s'agit d'une catégorie principale, on contrôle qu'elle est simplement incluse
	if( !in_array($cat_id, $ar_key_cats) ){
		if( in_array($cat_id, $ar_cat_rules_ok) ){
			$is_include = true;
		}
	}else{ // S'il s'agit d'une sous catégorie, on contrôle qu'elle est incluse elle et sa hiérarchie
		// Contrôle que toutes les catégories parents sont incluses
		$all_parent_include = true;
		foreach( $ar_hierarchy[ $cat_id ] as $one_parent ){
			if (isset($config['cat_root']) && $config['cat_root'] == $one_parent) {
				continue;
			}

			if( !ord_returns_category_allow_returns($one_parent) ){
				$all_parent_include = false;
				break;
			}
		}

		if( $all_parent_include ){
			if( !in_array($cat_id, $ar_cat_rules_ko) ){
				$is_include = true;
			}
		}
	}

	return $is_include;
}

/** Cette fonction charge les autorisations pour la gestion des retours par catégories
 *	@return array Un tableau contenant :
 *			- one_rule : s'il existe ou non au moins une règle d'inclusion / exclusion de catégorie
 *			- ar_cat_rules_ok : tableau contenant les catégories incluses (si one_rule = true)
 *			- ar_cat_rules_ko : tableau contenant les catégories excluses (si one_rule = true)
 *			- ar_hierarchy : tableau contenant pour chaque catégorie la liste des catégories parentes : array( cat_id => array(parent_id, parent_id, ...), ... )
 */
function ord_returns_category_allow_returns_cache(){
	global $config, $memcached;

	$key_mem_1 = 'ord_returns_category_allow_returns2:one_rule:'.$config['tnt_id'].':'.$config['wst_id'];
	$key_mem_2 = 'ord_returns_category_allow_returns2:ar_cat_rules_ok:'.$config['tnt_id'].':'.$config['wst_id'];
	$key_mem_3 = 'ord_returns_category_allow_returns2:ar_cat_rules_ko:'.$config['tnt_id'].':'.$config['wst_id'];
	$key_mem_4 = 'ord_returns_category_allow_returns2:ar_hierarchy:'.$config['tnt_id'].':'.$config['wst_id'];

	$one_rule = false;
	$ar_cat_rules_ok = array( 'null' );
	$ar_cat_rules_ko = array( 'null' );
	$ar_hierarchy = array( 'null' );

	if( ($get_1 = $memcached->get($key_mem_1)) && ($get_2 = $memcached->get($key_mem_2)) && ($get_3 = $memcached->get($key_mem_3)) ){
		$one_rule = ( $get_1 == 'ok' ? true : false );
		$ar_cat_rules_ok = $get_2;
		$ar_cat_rules_ko = $get_3;
	}else{
		$res = ria_mysql_query('select cat_id, cat_rule from ord_returns_categories where cat_tnt_id = '.$config['tnt_id']);

		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				$one_rule = true;

				if( $r['cat_rule'] ){
					$ar_cat_rules_ok[] = $r['cat_id'];
				}else{
					$ar_cat_rules_ko[] = $r['cat_id'];
				}
			}
		}

		$memcached->set( $key_mem_1, ($one_rule ? 'ok' : 'ko'), 10 );
		$memcached->set( $key_mem_2, $ar_cat_rules_ok, 10 );
		$memcached->set( $key_mem_3, $ar_cat_rules_ko, 10 );
	}

	if( ($get_3 = $memcached->get($key_mem_4)) ){
		$ar_hierarchy = $get_3;
	}else{
		$res = ria_mysql_query('select cat_parent_id as parent_id, cat_child_id as child_id from prd_cat_hierarchy where cat_tnt_id = '.$config['tnt_id']);

		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				if( !isset($ar_hierarchy[ $r['child_id'] ]) ){
					$ar_hierarchy[ $r['child_id'] ] = array();
				}

				$ar_hierarchy[ $r['child_id'] ][] = $r['parent_id'];
			}
		}

		$memcached->set( $key_mem_4, $ar_hierarchy, 10 );
	}

	return array(
		'one_rule' 			=> $one_rule,
		'ar_cat_rules_ok' 	=> $ar_cat_rules_ok,
		'ar_cat_rules_ko' 	=> $ar_cat_rules_ko,
		'ar_hierarchy' 		=> $ar_hierarchy
	);
}

/** Cette fonction envoie le nom d'un retour
 *  @param int $id Obligatoire, identifiant du retour
 *	@return retourne le nom associé à une demande de retour
 */
function ord_returns_name( $id ){
	return str_pad($id, 8, '0', STR_PAD_LEFT);
}

/** Cette fonction envoie un email au client sur l'évolution d'un retour
 *  @param int $id Obligatoire, identifiant du retour
 *  @param $message Obligatoire, message personnalisé
 *  @param $prepaid_label Facultatif, étiquette prépayée
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_email_send( $id, $message, $prepaid_label=null ){
	$return = ord_returns_get($id);
	if( !$return )
		return false;
	if( !($return = ria_mysql_fetch_array($return)) )
		return false;

	// Bloque la notification pour l'annulation d'un retour
	if( $return['states_id']==ORD_RETURNS_STATE_CANCEL ){
		return true;
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	// cfg_emails !
	$rcfg = cfg_emails_get('email-return', $return['wst_id_ord']);
	if( !ria_mysql_num_rows($rcfg) )
		return false;

	$cfg = ria_mysql_fetch_array($rcfg);

	$user = gu_users_get($return['user_id']);
	if( !$user )
		return false;
	if( !($user = ria_mysql_fetch_array($user)) )
		return false;

	$return_name = str_pad($id, 8, '0', STR_PAD_LEFT);

	$email = new Email();
	$email->setSubject( 'Evolution du retour n°'.$return_name );
	$email->setFrom( $cfg['from'] );
	$email->addTo( $user['email'] );
	if( $cfg['cc'] )
		$email->addCC( $cfg['cc'] );
	if( $cfg['bcc'] )
		$email->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] )
		$email->setReplyTo( $cfg['reply-to'] );

	global $config;

	// notification spé "On en fait des tonnes"
	if ($config['tnt_id'] == 51) {
		if ( file_exists($config['site_dir'].'/include/view.emails.inc.php') ) {
			require_once($config['site_dir'].'/include/view.emails.inc.php');
			if ( function_exists('idmat_custom_return_notify') ) {
				$custom_return_notify = idmat_custom_return_notify($email, $return, $message);
				return $custom_return_notify;
			}
		}
	}

	// récupère les états
	$rstates = ord_returns_states_get();
	if( !$rstates )
		return false;
	$states = array();
	while( $dat = ria_mysql_fetch_array($rstates) )
		$states[$dat['id']] = $dat;

	// récupère les états produit
	$rstates = ord_returns_products_states_get();
	if( !$rstates )
		return false;

	$prdstates = array();
	while( $dat = ria_mysql_fetch_array($rstates) ){
		$prdstates[$dat['id']] = $dat;
	}

	// récupère les raisons
	$rreasons = ord_returns_reasons_get();
	$reasons = array();
	while( $dat = ria_mysql_fetch_array($rreasons) ){
		$reasons[$dat['id']] = $dat;
	}

	// récupère les modes
	$rmodes = ord_returns_modes_allowed_get();
	$modes = array();
	while( $dat = ria_mysql_fetch_array($rmodes) ){
		$modes[$dat['id']] = $dat;
	}

	if( $return['states_id'] == ORD_RETURNS_STATE_WAIT ){
		// Bons de retour
		$pdf = ord_returns_bon_pdf_create($id);
		$pdfFileName = $config['cnt_file_dir'].'/bon-de-retour-'.$id.'.pdf';
		$pdf->output(array('filename' => $pdfFileName));
		$email->addAttachment( $pdfFileName );

		// Etiquette prépayée
		if( $prepaid_label ){
			$tmp_id = messages_files_upload('prepaid-label');
			if( !$tmp_id )
				return false;
			$ext = preg_replace('/.*\./','', $_FILES['prepaid-label']['name']);
			$t = $config['cnt_file_dir'] . '/' . $tmp_id . '.' . $ext;
			$tmp = $config['cnt_file_dir'] . '/' . 'etiquette-'.$id.'-'.$tmp_id.'.'.$ext;
			if( !@rename($t, $tmp) )
				return false;
			$email->addAttachment( $tmp );
		}
	}

	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		$config['email_html_header'] = str_replace('%site%', $config['site_url'], $config['email_html_header']);
		$config['email_html_footer'] = str_replace('%site%', $config['site_url'], $config['email_html_footer']);
	}

	// Composition du message
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );

	$email->addHtml($message);

	$email->openTable(600);
	$email->openTableRow(); $email->addCell( 'Numéro de retour :' ); $email->addCell( $return_name ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Numéro de commande :' ); $email->addCell( str_pad($return['ord_id'], 8, '0', STR_PAD_LEFT) ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Nombre de produits du retour :' ); $email->addCell( $return['products'].'&nbsp;' ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Etat du retour :' ); $email->addCell( $states[$return['states_id']]['name'] ); $email->closeTableRow();
	$email->closeTable();

	if( $return['products'] ){
		$total_ttc = 0;
		$email->addHtml('<p><b>Informations sur les produits</b></p>');

		$email->openTable(1200);
		$email->openTableRow();
			$email->addCell( 'Référence' );
			$email->addCell( 'Désignation' );
			$email->addCell( 'Mode de retour' );
			$email->addCell( 'Raison' );
			$email->addCell( 'Etat' );
			$email->addCell( 'Total' );
		$email->closeTableRow();

		$rows = ord_returns_returned_products_get($id);
		while( $row = ria_mysql_fetch_array($rows) ){
			$email->openTableRow();
				$email->addCell( $row['ref'] );

				$name = $row['name'];
				if( $row['lot'] )
					$name .= ' (Lot '.$row['lot'].')';

				$email->addCell( $name );

				$mode = $modes[$row['mode_id']]['name'];
				// if( $row['state_id']==ORD_RETURNS_PRD_STATE_SUCCESS && $row['mode_id']==ORD_RETURNS_MODE_AVOIR ){
					// $code = 'AVOIR'.$return['user_id'].$row['return_id'].$row['line_id'];
					// $rpmt = pmt_codes_get( null, $code );
					// if( !$rpmt || !ria_mysql_num_rows($rpmt) )
						// return false;
					// $pmt = ria_mysql_fetch_array( $rpmt );
					// $mode .= '	<br />Votre avoir est disponible sous forme d\'un code promotion "AVOIR'.$code.'" pouvant être utilisé sur votre prochaine commande.';
					// if( trim($pmt['date_stop']) )
						// $mode .= '	<br />Votre avoir est valable jusqu\'au '.$pmt['date_stop'].'.';
				// }
				$email->addCell( $mode );

				$reason = '';
				if( $row['reason_id'] )
					$reason .= '<b>'.$reasons[$row['reason_id']]['name'].'</b><br />';
				$reason .= str_replace("\n", '<br />', htmlspecialchars($row['reason_detail']));
				$email->addCell( $reason );

				$state = $prdstates[$row['state_id']]['name'];
				if( $row['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE && $row['motif'] )
					$state .= '<br /><br /><strong>Motif&nbsp;:</strong><br />'.str_replace("\n", '<br />', htmlspecialchars($row['motif']));
				$email->addCell( $state );

				$subtotal = $row['price_ttc'] * $row['qte'];
				$total = str_replace(' ', '&nbsp;', number_format(round($subtotal, 2), 2, ',', ' ')).'&euro;';

				if( $row['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE )
					$total = '<span style="text-decoration: line-through">'.$total.'</span>';
				else
					$total_ttc += $subtotal;

				$email->addCell( $total, 'right' );
			$email->closeTableRow();
		}
		$email->openTableRow();
			$email->addCell( '', 'left', 5 );
			$email->addCell( str_replace(' ', '&nbsp;', number_format(round($total_ttc, 2), 2, ',', ' ')).'&euro;', 'right' );
		$email->closeTableRow();

		$email->closeTable();
	}

	$rcode = ord_returns_get_promotion( $id );
	if( $rcode && ria_mysql_num_rows($rcode) ){
		$code = ria_mysql_fetch_array($rcode);

		$avoir = '	<br />Votre avoir est disponible sous forme d\'un code promotion "'.$code['code'].'" pouvant être utilisé sur votre prochaine commande.';
		if( trim($code['end']) )
			$avoir .= '	<br />Votre avoir est valable jusqu\'au '.$code['end'].'.';

		$email->addHtml($avoir.'<br />');
	}

	$email->addHtml('Votre service client,<br />');
	$email->addHtml($config['site_name'].'<br />');

	$email->addHtml( $config['email_html_footer'] );

	$res = $email->send();

	return $res;
}

/** Cette fonction permet de récupérer le code promotion (avoir) affecté à un retour.
 *	@param int $id Obligatoire, identifiant d'un retour
 *	@return bool false si aucun code promotion sinon un résultat MySQL contenant :
 *				- code : code de la promotion
 *				- end : date de fin
 */
function ord_returns_get_promotion( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_query('
		select cod_code as code, off_date_stop as end
		from ord_returns
			join pmt_codes on (return_tnt_id=cod_tnt_id and return_cod_id=cod_id)
			join pmt_offers on (cod_tnt_id=off_tnt_id and cod_id=off_cod_id)
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and ifnull(return_cod_id, 0)>0
			and return_date_deleted is null
	');

}

/** Cette fonction permet de vérifier si un bon de retour existe
 *	@param int $id Obligatoire, identifiant d'un retour
 *	@param int $usr Optionnel, permet de tester si un retour existe pour un utilisateur donné
 *	@return bool true si le bon de retour existe, false dans le cas contraire
 */
function ord_returns_exists( $id, $usr=0 ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].'
			and return_date_deleted is null
			and return_id='.$id.'
			'.( $usr>0 ? ' and return_user_id='.$usr : '' ).'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// Pour compatibilité
function ord_retuns_exists( $id, $usr=0 ){
	return ord_returns_exists( $id, $usr );
}

/** Cette fonction renvoie la liste des retours. Eventuellement filtrés.
 *  @param int $id Optionnel, identifiant ou tableau d'identifiants des retours.
 *  @param $state Optionnel, état ou liste d'états sur lequel filtrer le résultat.
 *  @param string $date_start Optionnel, date de début
 *  @param string $date_end Optionnel, date de fin
 *	@param int $order_id Optionnel, identifiant de la commande
 *	@param int $usr_id Optionnel, identifiant client
 *	@param $exclude_state Optionnel, identifiant ou tableau d'identifiants de statut, permettant d'exclure un status de retour
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du retour
 *			- date : date du retour
 *			- products : nombre de produits du retour
 *			- total_ht : total ht
 *			- total_ttc : total ttc
 *			- user_id : identifiant du client
 *			- ord_id : identifiant de la commande
 *			- states_id : état de la commande
 *			- prepaid_label : étiquette prépayée (booléen)
 *			- wst_id_ord : identifiant du site de la commande à l'origine du retour
 */
function ord_returns_get( $id=0, $state=0, $date_start=null, $date_end=null, $order_id=null, $usr_id=null, $exclude_state=false ) {
	if( !is_array($id) )
		$id = array($id);
	foreach( $id as $i ) {
		if( !is_numeric($i) )
			return false;
	}

	if( $state!=0 ){
		if( !is_array($state) )
			$state = array($state);
		foreach( $state as $s ){
			if( !(is_numeric($s) && $s > 0) )
				return false;
		}
	}

	if( $date_start!==null ){
		$date_start = dateheureparse($date_start);
		if( !isdateheure($date_start) )
			return false;
	}
	if( $date_end!==null ){
		$date_end = dateheureparse($date_end);
		if( !isdateheure($date_end) )
			return false;
	}

	if( $order_id!==null ){
		if( !is_array($order_id) )
			$order_id = array($order_id);
		foreach( $order_id as $order ) {
			if( !is_numeric($order) )
				return false;
		}
	}

	if( $usr_id!==null ) {
		if( !is_array($usr_id) )
			$usr_id = array($usr_id);
		foreach( $usr_id as $usr ) {
			if( !is_numeric($usr) )
				return false;
		}
	}

	global $config;

	$sql = '
		select return_id as id, return_date as date, return_products as products, return_total_ht as total_ht, return_total_ttc as total_ttc, return_user_id as user_id, return_ord_id as ord_id,
			return_states_id as states_id, return_prepaid_label as prepaid_label, ifnull((
				select ord_wst_id from ord_orders
				where ord_tnt_id = return_tnt_id and ord_id = return_ord_id
				limit 0, 1
			), '.$config['wst_id'].') as wst_id_ord, return_need_sync as need_sync, return_adr_invoices as adr_invoices, return_piece as piece, return_ref as ref,
			return_adr_invoices as adr_invoices, return_adr_delivery as adr_delivery , return_dlv_notes as dlv_notes, return_pkg_id as pkg_id, return_srv_id as srv_id,
			return_str_id as str_id, return_pay_id as pay_id, return_pmt_id as pmt_id, return_seller_id as seller_id, return_dps_id as dps_id, return_wst_id as wst_id,
			return_contact_id as contact_id, return_reseller_id as reseller_id, return_reseller_contact_id as reseller_contact_id
		from ord_returns
		where return_tnt_id = '.$config['tnt_id'].' and return_date_deleted is null
	';

	$t = implode(',', $id);
	if( $t )
		$sql .= ' and return_id in ('.$t.')';

	if( $state ){
		$t = implode(',', $state);
		$sql .= ' and return_states_id IN ('.$t.')';
	}

	if( $date_start!==null )
		$sql .= ' and return_date >= \''.$date_start.'\'';
	if( $date_end!==null )
		$sql .= ' and return_date <= \''.$date_end.'\'';
	if( $order_id!==null ){
		$t = implode(', ', $order_id);
		if( $t!=='')
			$sql .= ' and return_ord_id in ('.$t.')';
	}
	if( $usr_id !== null ){
		$t = implode(', ', $usr_id);
		if( $t!=='' )
			$sql .= ' and return_user_id in ('.$t.')';
	}

	if( $exclude_state!==false ){
		if( !is_array($exclude_state) )
			$exclude_state = array($exclude_state);
		$sql .= ' and return_states_id not in ('.implode(', ', $exclude_state).')';
	}
	$sql .= ' order by return_date desc';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer le total ht ou le total ttc
 *	@param int $id Obligatoire, identifiant d'un bon de retour
 *	@param bool $ttc Optionnel, par défaut le total ht est retourné, mettre à true pour retournée le ttc
 *	@return soit le total ht soit le total ttc
 */
function ord_returns_get_total( $id, $ttc=false ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select return_total_'.( $ttc ? 'ttc' : 'ht' ).' as total
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and return_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'total' );
}

/** Cette fonction permet de récupérer le statut d'un bon de retour.
 *	@param int $id Obligatoire, identifiant d'un bon de retour
 *	@return le status du bon de livraison, false dans le cas contraire
 */
function ord_returns_get_status( $id ){
	if( !ord_returns_exists($id) ) return false;
	global $config;

	$res = ria_mysql_query('
		select return_states_id as state
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and return_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'state' );
}

/** Cette fonction permet de récupérer l'identifiant du compte conserné par le retour sur une commande.
 *	@param int $id Obligatoire, identifiant d'un retour
 *	@return bool False si le paramètre fournis est faux, sinon l'identifiant du compte client
 */
function ord_returns_get_user( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$sql = '
		select return_user_id as usr
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and return_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'usr' );
}

/** Cette fonction permet de récupérer les informations d'une commande concernée par le bon de retour
 *	@param int $id Obligatoire, identifiant d'un retour
 *	@param $just_id Optionnel, par défaut toutes les informations de la commande sont retournées, mettre True pour ne récupérer que l'identifiant
 *	@return bool false en cas d'erreur, sinon le résultat de ord_orders_get() ou l'identifiant de la commande (selon le paramètre $just_id)
 */
function ord_returns_get_order( $id, $just_id=false ){
	if( !ord_returns_exists($id) ) return false;
	global $config;

	$res = ria_mysql_query('
		select return_ord_id as ord_id
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and return_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	$ord_id = ria_mysql_result( $res, 0, 'ord_id' );

	if( $just_id )
		return $ord_id;

	return ord_orders_get( 0, $ord_id );
}

/** Cette fonction renvoie si le mode de retour est autorisé
 *  @param $rm_id Obligatoire, identifiant du mode de retour
 *  @return bool true si le mode existe, false sinon. Renvoie null si la requête échoue.
 */
function ord_returns_is_mode_allowed( $rm_id ) {
	if( !is_numeric($rm_id) ){
		return null;
	}
	global $config;
	$res = ria_mysql_query('select 1 from ord_returns_mode_allowed where rma_tnt_id = '.$config['tnt_id'].' and rma_rm_id = '.$rm_id);
	return ($res) ? (ria_mysql_num_rows($res) > 0) : null;
}

/** Cette fonction renvoie si un retour peut être créé pour une commande donnée
 *	Check si la commande possède au moins un produit retournable et si l'état de la commande permet un retour
 *  @param int $ord Obligatoire, Identifiant de la commande
 *	@return bool true si un retour peut être créé, false sinon
 */
function ord_returns_can_create_for_order( $ord ){
	if( !is_numeric($ord) || $ord<=0 ) return false;
	global $config;

	$rp = ord_products_get( $ord );
	if( !$rp || !ria_mysql_num_rows($rp) ){
		return false;
	}
	$products = ria_mysql_num_rows( $rp );
	$can_returned = 0;
	while( $p = ria_mysql_fetch_array($rp) ){
		$tmp = ord_returns_can_create_for_product($ord, $p['id']);
		if( $tmp>0 ){
			$can_returned++;
		}
	}

	return $can_returned>0 ? true : false;
}

/** Cette fonction permet de vérifier si une quantité peut être saisie comme quantité retournée
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param $qte Obligatoire, quantité à vérifier
 *	@return bool true si la quantité saisie est inférieur ou égale à la quantité commande moins la quantité déjà retourné
 *	@return bool false dans le cas contraire
 */
function ord_returns_products_verify_qte( $ord, $prd, $qte ){
	if( !is_numeric($ord) || $ord<=0 ) return false;
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	if( !is_numeric($qte) || $qte<=0 ) return false;

	// récupère la quantité livrée
	$res = ria_mysql_query('
		select sum( prd_qte ) as qte
		from ord_bl_products
			join ord_bl on ( bl_tnt_id=prd_tnt_id and bl_id=prd_bl_id )
		where prd_tnt_id='.$config['tnt_id'].'
			and bl_masked = 0
			and prd_id='.$prd.' and prd_ord_id='.$ord.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	$dlv_qte = ria_mysql_result( $res, 0, 'qte' );

	// récupère la quantité déjà retournée
	$res = ria_mysql_query('
		select sum(prd_qte) as qte
		from ord_returns_products
			join ord_returns on (prd_tnt_id=return_tnt_id and prd_return_id=return_id)
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and prd_id='.$prd.'
			and return_states_id!='.ORD_RETURNS_STATE_CANCEL.'
			and return_date_deleted is null
	');

	if( !$res )
		return false;

	$ret_qte = ria_mysql_num_rows($res) ? ria_mysql_result( $res, 0, 'qte' ) : 0;
	return ($dlv_qte - $ret_qte) >= $qte;
}

/** Cette fonction permet de savoir si un produit est défa dans une commande à l'état 'Retour sur commande'.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param $qte Optionnel, quantité du produit qui doit être présent dans un retour commande
 *	@return bool true si le produit est déjà dans une commande, false dans le cas contraire
 */
function ord_returns_products_in_order( $prd, $qte=0 ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	if( !is_numeric($qte) || $qte<0 ) return false;


	$sql = '
		select 1
		from ord_products
			join ord_orders on (ord_tnt_id=prd_tnt_id and ord_id=prd_ord_id)
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd.'
			and ord_state_id=23
	';

	if( $qte>0 ) $sql .= ' having sum(prd_qte)='.$qte;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de savoir si un retour est toujours possible pour un produit d'une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param $exclude Optionnel, permet d'exclude un status de retour
 *	@return le nombre de jours restant pour faire un retour si celui-ci est possible ou false si une erreur s'est produite
 *	@return -1 si la date de livraison est dépassée (prends en compte si le produit est dans plusieurs BL)
 *	@return -2 si les retours sont interdits sur toutes les catégories contenants le produit
 *	@return -3 si toute la quantité a déjà été retournée
 */
function ord_returns_can_create_for_product( $ord, $prd, $exclude=false ){
	if( !is_numeric($ord) || $ord<=0 ) return false;
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	// vérifier si la date de livraison est toujours dans les temps
	$res = ria_mysql_query('
		select sum( prd_qte ) as qte, datediff(date_add(bl_date, interval '.$config['returns_delay'].' day), now()) as days
		from ord_bl_products
			join ord_bl on ( bl_tnt_id=prd_tnt_id and bl_id=prd_bl_id )
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd.' and prd_ord_id='.$ord.'
			and bl_masked = 0
			and now()<=date_add(bl_date, interval '.$config['returns_delay'].' day)
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	$days = ria_mysql_result( $res, 0, 'days' );
	$dlv_qte = ria_mysql_result( $res, 0, 'qte' );
	if( $days<=0 )
		return -1;

	// vérifie les catégories
	$rcat = prd_products_categories_get( $prd, true );
	if( !$rcat || !ria_mysql_num_rows($rcat) )
		return false;

	$autorized = false;
	while( $cat = ria_mysql_fetch_array($rcat) ){
		if( ord_returns_category_allow_returns($cat['cat']) ){
			$autorized = true;
			break;
		}
	}

	if( !$autorized )
		return -2;

	if( $exclude!==false ){
		if( !is_array($exclude) )
			$exclude = array($exclude);
	}

	// vérifier que tout n'est pas déjà retourné
	$res = ria_mysql_query('
		select sum(prd_qte) as qte
		from ord_returns_products
		'.( $exclude ? ' join ord_returns on (return_tnt_id=prd_tnt_id and return_id=prd_return_id)' : '' ).'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and prd_id='.$prd.'
			'.( $exclude ? ' and return_date_deleted is null' : '' ).'
			'.( $exclude ? ' and return_states_id not in ('.implode(', ', $exclude).')' : '' ).'
	');

	if( !$res )
		return false;

	$ret_qte = ria_mysql_num_rows($res) ? ria_mysql_result( $res, 0, 'qte' ) : 0;
	if( $ret_qte>=$dlv_qte )
		return -3;

	return $days>0 ? $days : 0;
}

/** Cette fonction renvoie les produits de la commande sauf ceux qui sont retournés
 *
 *  @param int $ord Obligatoire, Identifiant de la commande
 *
 *	@return bool false si échec
 *  @return array Tableau des produits :
 *		- id : identifiant du produit
 *		- line : identifiant de la ligne
 *		- ref : référence du produit
 *		- name : Nom du produit
 *		- qte : Quantité restante
 *		- title : Titre du produit
 *		- desc : Description produit
 *		- price_ht : Prix ht
 *		- tva_rate : montant de la tva
 *		- price_ttc : prix ttc
 *		- total_ht : Total ht
 *		- total_ttc : Total ttc
 *		- date-livr : Date livraison
 *		- date_livr_en : Date livraison en
 */
function ord_returns_keeped_products_get_array( $ord ){
	// récupère tous les produits
	$products = ord_products_get($ord);
	if( !$products )
		return false;

	$res = array();
	$t = array();
	while( $dat = ria_mysql_fetch_array($products) ) {
		if( prd_products_is_port($dat['ref']) ){
			continue;
		}
		$res[$dat['id']] = $dat;
		$t[] = $dat['id'];
	}
	$imp = implode(', ', $t);

	global $config;

	if( $imp!=='' ){
		// récupère les produits retournés (tous les retours confondus)
		$sql = 'select prd_id as id, '.($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)' ).' as sum
				from ord_returns_products
				join ord_returns on (prd_return_id = return_id)
				where prd_tnt_id = '.$config['tnt_id'].'
				and prd_id in ('.$imp.')
				and return_ord_id = '.$ord.'
				and return_date_deleted is null
				group by prd_id'."\n";
		$query = ria_mysql_query($sql);
		if( !$query )
			return false;

		while( $dat=ria_mysql_fetch_array($query) ){
			$row =& $res[$dat['id']];
			$row['qte'] -= $dat['sum'];
			if( $row['qte'] <= 0 )
				unset($res[$dat['id']]);
		}
		unset($row);
	}
	return $res;
}

/** Cette fonction renvoie le nombre de produits retournables
 *  @param $ord	Obligatoire, identifiant de la commande
 *	@return bool Retourne false si une erreur se produit
 *  @return Retourne le nombre de produits retournables
 */
function ord_returns_keeped_products_get_count( $ord ) {
	if( !is_numeric($ord) || $ord<=0 ) return false;
	global $config;

	$rp = ord_products_get( $ord );
	if( !$rp || !ria_mysql_num_rows($rp) )
		return false;

	$products = 0;
	while( $p = ria_mysql_fetch_array($rp) ){
		if( ord_returns_can_create_for_product($ord, $p['id']) )
			$products++;
	}

	return $products>0 ? $products : false;
}

/** Cette fonction renvoie les modes de retours autorisés
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : Identifiant mode
 *			- name : Nom du mode
 */
function ord_returns_modes_allowed_get(){
	global $config;
	return ria_mysql_query('
		select rm_id as id, rm_name as name
		from ord_returns_mode_allowed
			join ord_returns_mode on (rma_rm_id = rm_id)
		where rma_tnt_id = '.$config['tnt_id'].'
	');
}

/** Cette fonction écrase les modes de retour autorisés
 *  @param $modes Obligatoire, liste des identifiants des modes
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_modes_allowed_set( $modes ){
	if( !is_array($modes) ){
		return false;
	}
	// récupère tous les modes
	$rmodes = ord_returns_modes_get();
	if( !$rmodes ){
		return false;
	}
	$all = array();
	while( $dat=ria_mysql_fetch_array($rmodes) ){
		$all[$dat['id']] = $dat;
	}

	// vérifie que les modes passés en paramètre sont valides
	foreach($modes as $id){
		if( !isset($all[$id]) ){
			return false;
		}
	}

	// récupère les modes autorisés
	$rmodes = ord_returns_modes_allowed_get();
	if( !$rmodes ){
		return false;
	}

	$allowed = array();
	while( $dat=ria_mysql_fetch_array($rmodes) ){
		$allowed[$dat['id']] = $dat;
	}

	// del
	$del = array();
	foreach( $allowed as $id => $mode ){
		if( !in_array($id, $modes) ){
			$del[] = $id;
		}
	}

	global $config;

	$imp = implode(', ', $del);
	if( $imp!=='' ){
		if( !ria_mysql_query('delete from ord_returns_mode_allowed where rma_tnt_id = '.$config['tnt_id'].' and rma_rm_id in ('.$imp.')') ){
			return false;
		}
	}

	// add
	foreach( $modes as $id ){
		if( !isset($allowed[$id]) ){
			if( !ria_mysql_query('insert into ord_returns_mode_allowed (rma_tnt_id, rma_rm_id) values ('.$config['tnt_id'].', '.$id.')') ){
				return false;
			}
		}
	}
	return true;
}

/** Cette fonction renvoie tous les modes de retour possibles
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du mode de retour
 *			- name : nom du mode de retour
 *			- position : position du mode
 */
function ord_returns_modes_get(){
	return ria_mysql_query('
		select rm_id as id, rm_name as name, rm_position as position
		from ord_returns_mode
		order by rm_position asc
	');
}

/** Cette fonction permet de retourer l'intitulé d'un mode de retour
 *	@param int $id Obligatoire, identifiant d'un mode de retour
 *	@return bool false si le mode de retour n'existe pas ou l'intitulé dans le cas contraire
 */
function ord_returns_modes_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select rm_name as name
		from ord_returns_mode
		where rm_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction renvoie le nombre de jours écoulés depuis la livraison d'une commande
 *	@param int $ord Identifiant de la commande
 *  @return Nombre de jours, null si non livrée, false si erreur
 */
function ord_returns_order_get_delay_livr( $ord ){
	$order = ord_orders_get(0, $ord);
	if( !$order )
		return false;
	if( !($order = ria_mysql_fetch_array($order)) )
		return false;

	if( $order['ord_livr']===null )
		return null;

	$date_end = date('Y-m-d');
	$d = array($date_end, $order['ord_livr']);
	foreach( $d as &$t )
		$t = strtotime($t);
	unset($t);
	$res = $d[0] - $d[1];
	return ceil($res / 86400);
}

/** Cette fonction renvoie les états de commande autorisés pour la création d'un retour
 *	@todo Remplacer cette fonction par un jeu de constantes ou une variable dans cfg_variables
 *  @return array Tableau d'identifiants
 */
function ord_returns_orders_states_allowed_get(){
	return array(7, 8, 12, 17, 18, 19, 24, _STATE_INV_STORE);
}

/** Cette fonction renvoie le délai du produit avant retour.
 *  Il s'agit par défaut de la limite renseignée dans Configuration > Gestion des retours
 *  Si ce délai est inférieur au délai légal, prend le délai légal (7j).
 *  Prévu initialement mais finalement non appliqué : Si la limite n'est pas renseignée, prend la garantie du produit
 *  Sinon, prend le délai légal
 *  @param int $prd_id Obligatoire, identifiant du produit
 *  @return Nombre de jours
 */
function ord_returns_product_delay_get( $prd_id ){
	$legal = ORD_RETURNS_LEGAL_DELAY;

	global $config;

	if( isset($config['returns_delay']) ){
		$delay = $config['returns_delay'];
	}
	if( !($config['returns_delay'] == '' || is_numeric($config['returns_delay']) && $config['returns_delay'] >= $legal) ){
		$delay = $legal;
	}
	if( !$delay ){
		$delay = $legal;
	}
	return $delay;
}

/** Cette fonction renvoie le nombre de jours retant pour qu'un produit soit retourné avant expiration
 *	@param int $ord Obligatoire, Identifiant de la commande
 *	@param int $prd_id Obligatoire, Identifiant du produit
 *  @return Nombre de jours, null si indéfini, false si erreur
 */
function ord_returns_product_expires_get( $ord, $prd_id ){
	$delay_livr = ord_returns_order_get_delay_livr($ord);
	if( $delay_livr === false )
		return false;
	return ($delay_livr !== null) ? ord_returns_product_delay_get($prd_id) - $delay_livr : null;
}

/** Cette fonction ajoute un produit ou une liste de produits dans le retour, les produits doivent être disponibles dans la commande
 *  @param int $id Obligatoire, identifiant du retour
 *  @param int $prd Obligatoire, identifiant ou tableau d'identifiants de produits
 *  @param $mode Obligatoire, mode de retour (remboursement ou avoir)
 *  @param $qte Obligatoire, quantité retournée
 *  @param $reason Optionnel, par défaut à null "Autre", raison du retour
 *  @param $message Optionnel, le message est obligatoire si la raison est "AUTRE"
 *	@param $lot Optionnel, numéro de lot
 *	@param $dlc Optionnel, date limite de consommation
 *	@param $check Optionnel, par défaut une vérification est faite pour savoir si le retour est possible pour ce produit, mettre false pour passer cette vérification
 *	@param $return_line Optionnel, permet de récupérer le numéro de ligne qui vient d'être ajoutée
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_add( $id, $prd, $mode, $qte, $reason=null, $message='', $lot=null, $dlc=null, $check=true, $return_line=false ){
	if( !ord_returns_exists($id) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !ord_returns_is_mode_allowed($mode) ) return false;
	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) || $qte<=0 ) return false;

	$ret = ria_mysql_fetch_array( ord_returns_get($id) );

	// vérification complémentaire
	if( $check && !ord_returns_can_create_for_product($ret['ord_id'], $prd) ) return false;
	if( $check && $ret['states_id'] != ORD_RETURNS_STATE_CREATE ) return false;
	if( $reason===null && !trim($message) ) return false;
	if( $reason!== null && !ord_returns_reason_exists($reason) ) return false;
	if( $dlc!==null && !isdate($dlc) ) return false;

	$dlc = $dlc!==null ? dateparse( $dlc ) : null;

	$rpord = ord_products_get( $ret['ord_id'], false, $prd );
	if( !$rpord || !ria_mysql_num_rows($rpord) )
		return false;

	$pord = ria_mysql_fetch_array( $rpord );

	$price_promo = ord_products_get_with_promotion( $ret['ord_id'], $prd );
	$pord['price_ht']  = isset($price_promo[$pord['id'].'-'.$pord['line']]) ? $price_promo[$pord['id'].'-'.$pord['line']]['price_ht']  : $pord['price_ht'];
	$pord['price_ttc'] = isset($price_promo[$pord['id'].'-'.$pord['line']]) ? $price_promo[$pord['id'].'-'.$pord['line']]['price_ttc'] : $pord['price_ttc'];

	// récupère le dernier numéro de ligne
	$rline = ria_mysql_query('
		select max(prd_line_id) as line_id, count(prd_line_id) as count_line
		from ord_returns_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_return_id='.$id.'
	');

	if( !$rline )
		return false;

	$line_id = 0;
	if( ria_mysql_num_rows($rline) ){
		$count = ria_mysql_result($rline, 0, 'count_line');
		$line_id = ria_mysql_result($rline, 0, 'line_id');
		$line_id = $count>0 ? $line_id+1 : 0;
	}

	$sql = '
		insert into ord_returns_products
			( prd_tnt_id, prd_return_id, prd_line_id, prd_qte, prd_id, prd_ref, prd_name, prd_price_ht, prd_price_ttc, prd_tva_rate, prd_reason_detail, prd_garanty, prd_ord_id, prd_state_id, prd_mode_id, prd_reason_id, prd_lot, prd_dlc )
		values
			(
				'.$config['tnt_id'].', '.$id.', '.$line_id.', '.$qte.','.$prd.', \''.addslashes($pord['ref']).'\', \''.addslashes($pord['name']).'\', \''.$pord['price_ht'].'\', \''.$pord['price_ttc'].'\', \''.$pord['tva_rate'].'\', \''.addslashes($message).'\', null, '.$ret['ord_id'].', '.ORD_RETURNS_STATE_CREATE.', '.$mode.', '.( $reason!==null ? $reason : 'null' ).', \''.addslashes($lot).'\', '.( $dlc!==null ? '\''.$dlc.'\'' : 'null' ).'
			)
	';

	if( !ria_mysql_query($sql) )
		return false;

	if( !ord_returns_update_totals($id) )
		return false;

	return $return_line ? $line_id : true;
}

/** Cette fonction permet de récupérer les produits contenus dans un bon de retour
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param $ret Optionnel, identifiant d'un bon de retour
 *	@param $state Optionnel, identifiant d'un état de retour pour les produits
 *	@param $line Optionnel, identifiant d'une ligne de produit dans un bon de retour
 *	@return bool false si une erreur se produit
 *	@return un resultat MySQL contenant :
 *			- ret : identifiant du bon de retour
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : nom du produit
 *			- qte : quantité du produit retournée
 *			- price_ht : prix ht du produit
 *			- price_ttc : prix ttc du produit
 *			- tva_rate : tva du produit
 *			- mode : identifiant du mode
 *			- reason : identifiant de la raison, 0 si la raison est "AUTRE"
 *			- details : message complémentaire
 *			- state : etat de retour du produit
 * 			- group_id : L'identifiant du groupe.
 * 			- group_parent_id : L'identifiant du parent du groupe.
 *			- lot : numéro de lot
 *			- dlc : date limité de consommation
 *			- dlc_fr : date limité de consommation au format jj/mm/aaaa
 */
function ord_returns_products_get( $prd=0, $ret=0, $state=0, $line=0 ){

	$ret = control_array_integer( $ret, false );
	if( $ret === false ){
		return false;
	}

	if( !is_numeric($prd) ) return false;
	if( !is_numeric($state) ) return false;
	global $config;

	$sql = '
		select prd_return_id as ret, prd_line_id as line_id, prd_id as id, prd_ref as ref, prd_name as name, prd_price_ht as price_ht, prd_price_ttc as price_ttc, '.($config['use_decimal_qte'] ? 'prd_qte' : 'cast(prd_qte as signed)' ).' as qte, prd_mode_id as mode,
			prd_reason_detail as details, prd_state_id as state, prd_lot as lot, prd_dlc as dlc, ifnull(prd_reason_id, 0) as reason, date_format(prd_dlc,"%d/%m/%Y") as dlc_fr,
			prd_tva_rate as tva_rate, prd_motif as motif, prd_garanty as garanty, prd_ord_id as ord_id, prd_cod_id as cod_id, prd_ecotaxe as ecotaxe, prd_date_livr as date_livr,
			prd_notes as notes, prd_date_created as date_created, prd_childs_line_id as childs_line_id, prd_parent_id as parent_id, prd_group_id as group_id, prd_group_parent_id as group_parent_id, prd_purchase_avg as purchase_avg,
			prd_pos as pos, prd_col_id as col_id
		from ord_returns_products
		where prd_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($ret) )
		$sql .= ' and prd_return_id in ('.implode(', ', $ret).')';
	if( $prd>0 )
		$sql .= ' and prd_id='.$prd;
	if( $state>0 )
		$sql .= ' and prd_state_id='.$state;
	if( $line>0 )
		$sql .= ' and prd_line_id='.$line;

	$sql .= ' order by ifnull(prd_pos, 99999) asc';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer l'état d'avancement d'un produit dans un bon de retour
 *	@param $ret Obligatoire, identifiant d'une commande
 *	@param $line_id Obligatoire, identifiant d'une ligne de commande
 *	@return bool false si le produit n'est pas présent dans le retour, sinon le numéro de l'état
 */
function ord_returns_products_get_state( $ret, $line_id ){
	if( !is_numeric($ret) || $ret<=0 ) return false;
	if( !is_numeric($line_id) || $line_id<0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select prd_state_id as state
		from ord_returns_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_return_id='.$ret.'
			and prd_line_id='.$line_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'state' );
}

/** Cette fonction permet de récupérer le total des produits selon un état
 *	@param $ret Obligatoire, identifiant du retour
 *	@param $state Optionnel, identifiant ou tableau d'identifiants d'un état
 *	@param bool $ttc Optionnel, par défaut on retourne le montant ht, mettre true pour retourner le montant ttc
 *	@return bool False en cas de problème, sinon le montant total ht ou ttc des produits
 */
function ord_returns_products_get_totals( $ret, $state=0, $ttc=false ){
	if( !is_numeric($ret) || $ret<=0 ) return false;
	if( !is_numeric($state) && !is_array($state) ) return false;
	global $config;

	if( $state!==0 ){
		if( is_numeric($state) ){
			if( $state<=0 ) return false;
			$state = array( $state );
		} elseif( is_array($state) ){
			foreach($state as $s){
				if( !is_numeric($s) || $s<=0 )
					return false;
			}
		}
	}

	$sql = '
		select sum( prd_price_'.( $ttc ? 'ttc' : 'ht' ).' * prd_qte ) as total
		from ord_returns_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_return_id='.$ret.'
	';

	if( is_array($state) && sizeof($state) )
		$sql .= ' and prd_state_id in ('.implode(', ', $state).')';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'total' );
}

/** Cette fonction permet de récupérer la quantité d'un produit qui ont été livrée pour une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@return la quantité livrée pour ce produit dans l'intervale de retour, false si une erreur se produit
 */
function ord_returns_products_get_qte_delivred( $ord, $prd=0 ){
	if( !is_numeric($ord) || $ord<=0 ) return false;
	if( !is_numeric($prd) || $prd<0 ) return false;
	global $config;

	// récupère la quantité livrée
	$res = ria_mysql_query('
		select '.($config['use_decimal_qte'] ? 'sum( prd_qte )' : 'cast(sum( prd_qte ) as signed)' ).' as qte
		from ord_bl_products
			join ord_bl on ( bl_tnt_id=prd_tnt_id and bl_id=prd_bl_id )
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and bl_masked = 0
			'.( $prd>0 ? ' and prd_id='.$prd : '' ).'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'qte' );
}

/** Cette fonction permet de récupérer la quantité retourné d'un produit pour une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param $exclude Optionnel, identifiant ou tableau d'identifiants de status de retour à exclure
 *	@param $ret Optionnel, identifiant d'un bon de retour
 *	@return la quantité retourné d'un produit pour une commande, false si une erreur se produit
 */
function ord_returns_products_get_qte_returned( $ord, $prd=0, $exclude=false, $ret=0 ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	if( $ret>0 && !ord_returns_exists($ret) ){
		return false;
	}

	if( $exclude!==false ){
		if( !is_array($exclude) ){
			$exclude = array($exclude);
		}
	}

	// récupère la quantité déjà retournée
	$res = ria_mysql_query('
		select '.($config['use_decimal_qte'] ? 'sum( prd_qte )' : 'cast(sum( prd_qte ) as signed)' ).' as qte
		from ord_returns_products
			join ord_returns on (return_tnt_id = '.$config['tnt_id'].' and return_id=prd_return_id)
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			'.( $prd>0 ? ' and prd_id='.$prd : '' ).'
			'.( $ret>0 ? ' and prd_return_id='.$ret : '' ).'
			'.( $exclude!==false ? ' and return_states_id not in ('.implode(', ', $exclude).')' : '' ).'
			and return_date_deleted is null
	');

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res) ? ria_mysql_result( $res, 0, 'qte' ) : 0;
}
/** Cette fonction retire des produits du retour
 *  @param $return_id Obligatoire, identifiant du retour
 *  @param $line_id Obligatoire, identifiant de la ligne de retour
 *  @param int $prd_id Obligatoire, identifiant du produit
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_del( $return_id, $line_id, $prd_id=0 ){
	$return = ord_returns_get($return_id);
	if( !$return ){
		return false;
	}
	if( !($return = ria_mysql_fetch_array($return)) ){
		return false;
	}

	if( !is_array($line_id) ){
		$line_id = array($line_id);
	}
	if( count($line_id) == 0 ){
		return true;
	}
	foreach( $line_id as $line ){
		if( !is_numeric($line) ){
			return false;
		}
	}

	// récupère les lignes
	$query = ord_returns_returned_products_get($return_id);
	if( !$query ){
		return false;
	}
	$count = 0;
	$total_ht = 0;
	$total_ttc = 0;

	while( $dat=ria_mysql_fetch_array($query) ){
		if( in_array($dat['line_id'], $line_id) ){
			$count++;
			$total_ht += $dat['price_ht'];
			$total_ttc += $dat['price_ttc'];
		}
	}

	global $config;

	$imp = implode(',', $line_id);

	// récupère tous les avoirs et les supprime
	$sql = 'select prd_cod_id as cod_id from ord_returns_products where prd_cod_id is not null and prd_tnt_id = '.$config['tnt_id'].' and prd_return_id = '.$return_id.' and prd_line_id in ('.$imp.')';
	if( is_numeric($prd_id) && $prd_id>0 ){
		$sql .= ' and prd_id='.$prd_id;
	}
	$req = ria_mysql_query($sql);
	if( !$req ){
		return false;
	}
	while( $dat=ria_mysql_fetch_array($req) ){
		if( !pmt_codes_del($dat['cod_id']) ){
			return false;
		}
	}

	$sql = '
		delete
		from ord_returns_products
		where prd_line_id in ('.$imp.')
			and prd_return_id = '.$return_id.'
			and prd_tnt_id = '.$config['tnt_id'].'
	';
	if( is_numeric($prd_id) && $prd_id>0 ){
		$sql .= ' and prd_id='.$prd_id;
	}
	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ord_returns_update($return_id, false);
}

/** Cette fonction met à jour le statut accepté ou refus d'une ligne de produit
 *  /!\ Attention la fonction ne gère pas corrrectement la clé primaire composé maintenant de 3 éléments return_id / prd_id / line_id
 *  @param int $id Obligatoire, identifiant du retour
 *  @param $line_id Obligatoire, identifiant ou tableau d'identifiants des lignes de retour
 *	@param $qte Obligatoire, quantité acceptée ou refusée
 *	@param $state Optionnel, permet de mettre le status à jour en même temps
 *	@param $motif Optionnel, permet de spécifié un motif de refus
 *	@param string $date_end Optionnel, date de fin de validité d'un avoir
 *
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_update_line( $id, $line_id, $qte, $state=false, $motif=false, $date_end=false ) {
	if( !ord_returns_exists($id) ) return false;
	if( !is_numeric($line_id) || $line_id<0 ) return false;
	global $config;

	// récupère les informations sur la ligne de retour
	$rows = ord_returns_returned_products_get($id, $line_id);
	if( !$rows || !ria_mysql_num_rows($rows) )
		return false;
	$row = ria_mysql_fetch_array( $rows );

	if( $row['state_id']!=ORD_RETURNS_PRD_STATE_RETURNED ) return false;

	if( $qte>$row['qte'] ) {
		return false;
	} elseif( $qte<$row['qte'] ) { // la quantité acceptée ou refusée est différente de la quantité conserné par la ligne de retour

		// création d'une nouvelle ligne
		$new_line = ord_returns_products_add( $id, $row['id'], $row['mode_id'], $qte, $row['reason_id'], $row['reason_detail'], $row['lot'], $row['dlc'], false, true );
		if( $new_line===false )
			return false;

		// mise à jour de la quantité de la ligne actuelle
		$res = ria_mysql_query('
			update ord_returns_products
			set prd_qte='.( $row['qte'] - $qte ).'
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_return_id='.$id.'
				and prd_line_id='.$line_id.'
		');

		if( !$res )
			return false;

		// si le motif ou le statut n'est pas à false, mise à jour de la nouvelle ligne
		if( $motif!==false || $state!==false ){
			if( !ord_returns_products_update($id, (int) $new_line, false, false, false, $state, $motif, $date_end) )
				return false;
		}

	} else {
		if( !ord_returns_products_update($id, (int) $line_id, false, false, false, $state, $motif, $date_end) )
			return false;
	}

	return true;
}

/** Cette fonction met à jour des lignes de retour
 *  /!\ Attention la fonction ne gère pas corrrectement la clé primaire composé maintenant de 3 éléments return_id / prd_id / line_id
 *  @param $return_id Obligatoire, identifiant du retour
 *  @param $line_id Obligatoire, identifiant ou tableau d'identifiants des lignes de retour
 *  @param $reason_id Optionnel, raison du retour. false indique que ce champ ne sera pas modifié
 *  @param $message Optionnel, détail. false indique que ce champ ne sera pas modifié
 *  @param $mode Optionnel, mode de retour. false indique que ce champ ne sera pas modifié
 *  @param $state Optionnel, etat. false inique que ce champ ne sera pas modifié
 *  @param $motif Optionnel, motif facultatif en cas de refus. false indique que ce champ ne sera pas modifié
 *	@param string $date_end Optionnel, date de fin d'un avoir
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_update( $return_id, $line_id, $reason_id=false, $message=false, $mode=false, $state=false, $motif=false, $date_end=false ){
	if( $date_end!=false && !isdate($date_end) ) return false;
	if( !is_array($line_id) ){
		$line_id = array($line_id);
	}
	foreach( $line_id as $id ){
		if( !is_numeric($id) ){
			return false;
		}
	}
	global $config;

	$rows = ord_returns_returned_products_get($return_id, $line_id);
	if( !$rows || !ria_mysql_num_rows($rows) ){
		return false;
	}
	// récupère le retour
	$return = ord_returns_get($return_id);
	if( !$return ){
		return false;
	}
	if( !($return = ria_mysql_fetch_array($return)) ){
		return false;
	}

	$set = array();
	if( $reason_id !== false ){
		if( $reason_id !== null && ! ord_returns_reason_exists($reason_id) ){
			return false;
		}
		$set[] = 'prd_reason_id = '.(($reason_id !== null) ? $reason_id : 'null');
	}
	if( $message !== false ){
		$set[] = 'prd_reason_detail = \''.addslashes(trim($message)).'\'';
	}
	if( $state !== false ){
		if( !ord_returns_products_states_exists($state) ){
			return false;
		}
		$set[] = 'prd_state_id = '.$state;

		if( $state == ORD_RETURNS_PRD_STATE_SUCCESS ){
		} elseif( $state == ORD_RETURNS_PRD_STATE_FAILURE || $state == ORD_RETURNS_PRD_STATE_RETURNED ){
			while( $dat=ria_mysql_fetch_array($rows) ){
				if( $dat['cod_id'] === null )
					continue;
				if( !pmt_codes_del($dat['cod_id']) )
					return false;
			}

			// si tous les produits sont à l'état retourné en attente d'examun, la commande passe au state "produits retournés"
			if( $state==ORD_RETURNS_PRD_STATE_RETURNED ){
				$rp = ord_returns_products_get( 0, $return_id );
				if( $rp && ria_mysql_num_rows($rp) ){
					$countprd = $count = 0;
					while( $p = ria_mysql_fetch_array($rp) ){
						$countprd++;
						if( $p['state']==ORD_RETURNS_PRD_STATE_RETURNED )
							$count++;
					}

					if( $countprd==$count )
						ord_returns_update_status( $return_id, ORD_RETURNS_STATE_RETURNED, false, false );
				}
			}
		}
	}
	if( $mode!==false ){
		if( !ord_returns_is_mode_allowed($mode) )
			return false;
		$set[] = 'prd_mode_id = '.$mode;
	}
	if( $motif!==false )
		$set[] = 'prd_motif = \''.addslashes($motif).'\'';
	if( count($set) == 0 )
		return true;

	$sql = '
		update ord_returns_products
		set '.implode(', ', $set).'
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_return_id = '.$return_id.'
		and prd_line_id in ('.implode(', ', $line_id).')
	';
	if( !ria_mysql_query($sql) )
		return false;

	$rproducts = ord_returns_returned_products_get($return_id);
	if( !$rproducts )
		return false;
	$products = array();
	while( $dat = ria_mysql_fetch_array($rproducts) )
		$products[] = $dat;

	// recalcule le total du retour
	$total_ht = 0;
	$total_ttc = 0;
	foreach( $products as $dat ) {
		if( $dat['state_id'] != ORD_RETURNS_PRD_STATE_FAILURE ){
			$total_ht += $dat['qte'] * $dat['price_ht'];
			$total_ttc += $dat['qte'] * $dat['price_ttc'];
		}
	}
	return true;
}

// \cond onlyria
/**	Permet l'ajout d'une ligne de retour
 *	@param $return_id Obligatoire, Identifiant du bon de retour
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Identifiant de ligne permettant d'ajouter plusieurs fois le même produit sur des lignes différentes.
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix HT du produit
 *	@param $tva Obligatoire, taux de tva appliqué au produit, écrit sous la forme 1.000 (ex: 1.196)
 *	@param int $ord Obligatoire, Identifiant de la commande à l'origine de cette préparation de livraison
 *	@param float $price_ttc Optionnel, Prix TTC du produit
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *	@param $update_totals Optionnel, Permet de ne pas recalculé les totaux
 *	@param	$note	Optionnel, Permet d'ajouter un commentaire sur la ligne produit, null pour ne pas en mettre
 *  @param $col_id Optionnel, Identifiant de collisage
 * 	@param $pos Optionnel, position du produit dans le bon de retour
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_returns_products_add_sage( $return_id, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $price_ttc=null, $ecotaxe=0, $update_totals=true, $note=null, $col_id=false, $pos=false ){
	if( !ord_returns_exists( $return_id ) ){
		return false;
	}
	if( !prd_products_exists( $prd, false ) ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}
	if( !trim($ref) ){
		return false;
	}
	if( !trim($name) ){
		$name = $ref;
	}

	global $config;

	$qte = str_replace(array(' ', ','), array('', '.'), $qte);
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	$price_ht = str_replace(array(' ', ','), array('', '.'), $price_ht);
	$tva = str_replace(array(' ', ','), array('', '.'), $tva);
	$ecotaxe = str_replace(array(' ', ','), array('', '.'), $ecotaxe);

	if( !is_numeric($qte) ){
		return false;
	}
	if( !is_numeric($price_ht) ){
		return false;
	}
	if( !is_numeric($tva) ){
		return false;
	}
	if( !is_numeric($ecotaxe) ){
		return false;
	}

	if( !is_numeric($ord) || !$ord ){
		$ord = 0;
	}elseif( !ord_orders_exists( $ord ) ){
		return false;
	}

	if( is_null($col_id) || !is_numeric($col_id) || !$col_id ){
		$col_id = 'null';
	}

	if( is_null($pos) || !is_numeric($pos) || $pos < 0 ){
		$pos = 'null';
	}

	$fields = array(
		'prd_tnt_id', 'prd_return_id', 'prd_id', 'prd_line_id', 'prd_ref', 'prd_name', 'prd_qte', 'prd_price_ht', 'prd_tva_rate',
		'prd_ord_id', 'prd_date_created', 'prd_ecotaxe','prd_reason_detail','prd_motif', 'prd_col_id', 'prd_pos'
	);
	$values = array(
		$config['tnt_id'], $return_id, $prd, $line, '"'.addslashes($ref).'"', '"'.addslashes($name).'"', $qte, $price_ht, $tva,
		$ord, 'now()', $ecotaxe, 0, 0, $col_id, $pos
	);

	if( $price_ttc !== null ){
		$price_ttc = str_replace(array(' ', ','), array('', '.'), $price_ttc);
		if( is_numeric($price_ttc) ){
			$fields[] = 'prd_price_ttc';
			$values[] = $price_ttc;
		}
	}

	if( is_string($note) ){
		$fields[] = 'prd_notes';
		$values[] = trim($note) != '' ? '"'.addslashes($note).'"' : 'null';
	}

	$sql = '
		insert into ord_returns_products
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';

	$res = ria_mysql_query($sql);

	// Met à jour les totaux pour le retour
	if( $res ){
		if( $update_totals ){
			ord_returns_update_totals( $return_id );
		}
	}

	return $res;

}

/**	Permet la mise d'une ligne de retour importée depuis la gestion commerciale SAGE.
 *
 *	@param $return_id Obligatoire, Identifiant interne du bon de retour
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Numéro de ligne (permet d'avoir plusieurs fois le même produit sur des lignes différentes).
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix unitaire HT du produit (remises comprises)
 *	@param $tva Obligatoire, Taux de tva à appliquer à la ligne
 *	@param int $ord Obligatoire, Identifiant de la commande d'origine
 *	@param float $price_ttc Optionnel, Prix unitaire TTC du produit (remises comprises)
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *	@param $update_totals Optionnel, Permet de ne pas recalculé les totaux
 *	@param	$note	Optionnel, Permet de mettre à jour le commentaire sur la ligne produit, null pour ne pas mettre à jour
 *  @param $col_id Optionnel, Identifiant de collisage
 * 	@param $pos Optionnel, position du produit dans le bon de retour
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function ord_returns_products_update_sage( $return_id, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $price_ttc=null, $ecotaxe=0, $update_totals=true, $note=null, $col_id=false, $pos=false ){
	global $config;
	if( !ord_returns_exists($return_id) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($line) ) return false;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) ) return false;

	$price_ht = str_replace( array(',',' '), array('.',''), $price_ht );
	if( !is_numeric($price_ht) ) return false;
	$tva = str_replace( array(',',' '), array('.',''), $tva );
	if( !is_numeric($tva) ) return false;
	$ecotaxe = str_replace( array(',',' '), array('.',''), $ecotaxe );
	if( !is_numeric($ecotaxe) ) return false;

	if( $col_id!==false && $col_id!==null && !is_numeric($col_id) ) return false;

	if( $pos !== false && $pos !== null && !is_numeric($pos) ){
		return false;
	}

	if( !is_numeric($ord) || $ord==0 )
		$ord = 'null';
	elseif( !ord_orders_exists($ord) )
		return false;

	$str_price_ttc = '';
	if( $price_ttc!=null ){
		$price_ttc = str_replace( array(',',' '), array('.',''), $price_ttc );
		if( is_numeric($price_ttc) )
			$str_price_ttc = ', prd_price_ttc='.$price_ttc;
	}

	$str_note = '';
	if( is_string($note) ){
		$str_note = ', prd_notes = ';
		$str_note .= trim($note) != '' ? '\''.addslashes($note).'\'' : 'null';
	}

	$sql = '
		update
			ord_returns_products
		set
			prd_ref=\''.addslashes($ref).'\',
			prd_name=\''.addslashes($name).'\',
			prd_qte='.$qte.',
			prd_price_ht='.$price_ht.',
			prd_tva_rate='.$tva.',
			prd_ord_id='.$ord.',
			prd_ecotaxe='.$ecotaxe.'
			'.( $col_id!==false ? ',prd_col_id='. ( $col_id!==null ? $col_id : 'null' ) : '' ).'
			'.$str_note.$str_price_ttc.'
			'.( $pos !== false  ? ', prd_pos = '.($pos !== null ? $pos : 'null') : '' ).'
		where
			prd_tnt_id='.$config['tnt_id'].' and
			prd_return_id='.$return_id.' and
			prd_id='.$prd.' and
			prd_line_id='.$line
	;

	$res = ria_mysql_query( $sql );

	if( $res ){
		if( $update_totals ){
			ord_returns_update_totals( $return_id );
		}
	}

	return $res;
}
// \endcond

/** Cette fonction renvoie la liste des produits d'un retour
 *  @param $return_id	Obligatoire, identifiant du retour
 *  @param $line_id		Facultatif, Identifiant de la ligne
 *  @param $state		Facultatif, Filtre sur un état
 *  @param int $limit		Facultatif, limite
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- return_id : identifiant du retour
 *			- line_id : identifiant de la ligne de retour
 *			- qte : quantité de produits
 *			- id : identifiant du produit
 *			- ref : référence produit
 *			- name : nom du produit
 *			- price_ht : prix hors taxe
 *			- price_ttc : prix ttc
 *			- reason_detail : détails du retour
 *			- motif : motif de refus
 *			- garanty : garantie du produit
 *			- ord_id : commande du produit
 *			- state_id : état de retour pour cette ligne
 *			- mode_id : mode de retour
 *			- reason_id : raison du retour
 *			- cod_id : identifiant d'un code promotion attaché à cette ligne de retour (en cas d'avoir)
 *			- lot : Numéro de lot
 *			- dlc : date limite de consommation
 *			- notes : Commentaire de la ligne
 */
function ord_returns_returned_products_get( $return_id, $line_id=false, $state=false, $limit=false ){
	if( !is_numeric($return_id) )
		return false;
	global $config;

	$where = '';
	$where .= 'prd_tnt_id = '.$config['tnt_id'];
	$where .= ' and prd_return_id = '.$return_id;
	if( $line_id!==false ){
		if( !is_array($line_id) )
			$line_id = array($line_id);
		foreach( $line_id as $id ){
			if( !is_numeric($id) )
				return false;
		}
		$where .= ' and prd_line_id in ('.implode(', ', $line_id).')';
	}
	if( $state !== false ){
		if( !is_numeric($state) )
			return false;
		$where .= ' and prd_state_id = '.$state;
	}

	$sql = '
		select prd_return_id as return_id, prd_line_id as line_id, '.($config['use_decimal_qte'] ? 'prd_qte' : 'cast(prd_qte as signed)' ).' as qte, prd_id as id, prd_ref as ref, prd_name as name, prd_price_ht as price_ht, prd_price_ttc as price_ttc, prd_reason_detail as reason_detail, prd_motif as motif, prd_garanty as garanty, prd_ord_id as ord_id, prd_state_id as state_id, prd_mode_id as mode_id, prd_reason_id as reason_id, prd_cod_id as cod_id, prd_lot as lot, prd_dlc as dlc, prd_notes as notes
		from ord_returns_products
		where '.$where.'
	';

	if( $limit ){
		if( !is_numeric($limit) )
			return false;
		$sql .= ' limit '.$limit;
	}
	return ria_mysql_query($sql);
}

/** Cette fonction ajoute une raison à la liste des raisons prédéfinies
 *  @param string $name Nom de la raison
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_reason_add( $name ){
	global $config;
	if( !trim($name) )
		return false;

	// récupère la position max
	$query = ria_mysql_query('
		select max(reason_position) as max
		from ord_returns_reasons
		where reason_tnt_id = '.$config['tnt_id'].'
	');
	if( !$query )
		return false;
	$pos = 1 + (($dat = ria_mysql_fetch_array($query)) ? $dat['max'] : 0);

	$sql = '
		insert
		into ord_returns_reasons (reason_tnt_id, reason_name, reason_position)
		values ('.$config['tnt_id'].', \''.addslashes($name).'\', '.$pos.')
	';
	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'intitulé d'une raison
 *	@param int $id Obligatoire, identifiant d'une raison
 *	@return bool false si une erreur se produit ou bien si la raison n'existe pas
 *	@return l'intitulé de la raison dans le cas contraire
 */
function ord_returns_raison_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select reason_name as name
		from ord_returns_reasons
		where reason_tnt_id='.$config['tnt_id'].'
			and reason_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction supprime une ou plusieurs raisons prédéfinies
 *  @param int $id Identifant ou tableau d'identifiants
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_reason_del( $id ){
	global $config;
	if( !is_array($id) )
		$id = array($id);
	if( count($id) < 1 )
		return false;
	foreach( $id as $value ){
		if( !(is_numeric($value) && $value > 0) )
			return false;
	}
	$sql = '
		delete
		from ord_returns_reasons
		where reason_tnt_id = '.$config['tnt_id'].'
		and reason_id in ('.implode(',', $id).')
	';
	return ria_mysql_query($sql);
}

/** Cette fonction renvoie si une raison existe déjà dans la liste des raisons prédéfinies
 *  @param int $id Facultatif, Identifiant de la raison
 *  @return bool true si le nom existe, false sinon
 */
function ord_returns_reason_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select 1
		from ord_returns_reasons
		where reason_tnt_id='.$config['tnt_id'].'
			and reason_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction renvoie la liste des raisons filtrées
 *  @param int $id Facultatif, identifiant d'une raison ou tableau d'identifiants
 *  @param string $name Facultatif, Nom de la raison.
 *  @param int $limit Facultatif, nombre de résultats ou tableau [page, count]
 *  @param $sort Facultatif, tableau (champ => tri), vaut [reason_position => asc] par défaut
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id identifiant de raison
 *			- name nom de la raison
 *			- position position de la raison
 */
function ord_returns_reasons_get( $id=null, $name=null, $limit=0, $sort=array('reason_position' => 'asc') ){
	global $config;
	if( $id!==null ) {
		if( !is_array($id) )
			$id = array($id);
		if( count($id) < 1 )
			return false;
		foreach( $id as $v ){
			if( !(is_numeric($v) && $v > 0) )
				return false;
		}
	}
	if( is_numeric($limit) )
		$limit = array(0, $limit);
	if( !(is_array($limit) && count($limit) == 2 && is_numeric($limit[0]) && is_numeric($limit[1])) )
		return false;

	$sql = '
		select reason_id as id, reason_name as name, reason_position as position
		from ord_returns_reasons
	';

	$where = '';
	$where .= 'reason_tnt_id = '.$config['tnt_id'];
	if( $id !== null )
		$where .= ' and reason_id in ('.implode(',', $id).')';
	if( $name !== null )
		$where .= ' and reason_name = \''.addslashes($name).'\'';
	$sql .= ' where '.$where;

	if( is_array($sort) ){
		$t = array();
		foreach( $sort as $key => $value )
			$t[] = $key.' '.$value;
		$t = implode(',', $t);
		if( $t !== '' )
			$sql .= ' order by '.$t;
	}

	if( $limit[1] > 0 )
		$sql .= ' limit '.$limit[0].', '.$limit[1];

	return ria_mysql_query($sql);
}

/** Cette fonction permute les positions de 2 motifs de retours
 *  @param $source identifiant de la source
 *  @param $tar	identifiant de la cible
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_reason_permute_pos( $source, $tar ){
	global $config;

	$query = ord_returns_reasons_get(array($source, $tar));
	if( !$query )
		return false;

	$t = array();
	while( $dat = ria_mysql_fetch_array($query) )
		$t[] = $dat;
	if( count($t) !== 2 )
		return false;

	if( ria_mysql_query('
		update ord_returns_reasons
		set reason_position = '.$t[1]['reason_position'].'
		where reason_tnt_id = '.$config['tnt_id'].'
		and reason_id = '.$t[0]['id'].'
	') ) {
		if( ria_mysql_query('
			update ord_returns_reasons
			set reason_position = '.$t[0]['reason_position'].'
			where reason_tnt_id = '.$config['tnt_id'].'
			and reason_id = '.$t[1]['id'].'
		') )
			return true;
	}
	return false;
}

/** Cette fonction renvoie si l'état de retour donné existe.
 *  @param int $id Identifiant de l'état
 *  @return bool true si l'état existe, false sinon
 */
function ord_returns_states_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select 1
		from ord_returns_states
		where state_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return true;
}

/** Cette fonction renvoie si l'état de retour donné existe. (état produit)
 *  @param int $id Identifiant de l'état
 *  @return bool true si l'état existe, false sinon
 */
function ord_returns_products_states_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select 1
		from ord_returns_products_states
		where prd_state_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction renvoie la liste des états possibles d'un retour.
 *	@param int $id Facultatif, identifiant d'un statut sur lequel filtrer le résultat
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'état
 *			- name : nom de l'état
 *			- name_plural : nom de l'état au pluriel
 *			- desc : description
 *			- position : position de l'état
 */
function ord_returns_states_get( $id=0 ){
	$id = control_array_integer( $id, false );
	if ($id===false) {
		return false;
	}

	$sql = '
		select state_id as id, state_name as name, state_name_plural as name_plural, state_desc as `desc`, state_position as position
		from ord_returns_states
	';

	if (is_array($id) && count($id)) {
		$sql .= '
			where state_id in ('.implode(',', $id).')
		';
	}

	$sql .= '
		order by state_position asc
	';

	return ria_mysql_query( $sql );
}

/** Cete fonction permet de renvoi le nom d'un status de retour
 *	@param int $id Obligatoire, identifiant du statut dont on doit retourner le nom
 *	@return bool false si l'identifiant n'est pas reconnu, le nom du statut (au singulier) dans le cas contraire
 */
function ord_returns_states_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select state_name as name
		from ord_returns_states
		where state_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction renvoie la liste des états possibles d'un produit.
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'état
 *			- name : nom de l'état
 *			- desc : description
 *			- position : position de l'état
 */
function ord_returns_products_states_get(){
	return ria_mysql_query('
		select prd_state_id as id, prd_state_name as name, prd_state_desc as `desc`, prd_state_position as position
		from ord_returns_products_states
		order by prd_state_position asc
	');
}

/** Cette fonction permet de retourner l'intitulé d'un état pour un produit.
 *	@param int $id Obligatoire, identifiant d'un état
 *	@return bool false si l'état n'existe pas, l'intitulé dans le cas contraire
 */
function ord_returns_products_states_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select prd_state_name as name
		from ord_returns_products_states
		where prd_state_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction renvoie le nombre de résultats sur le filtre de retours
 *  @param $state Facultatif, Etat du retour. false indique tous les états (défaut).
 *  @param string $date_start Facultatif, date de départ
 *  @param string $date_end Facultatif, date de fin
 *  @return Le nombre de retours dans l'état donné, false si erreur
 */
function ord_returns_states_get_count( $state=0, $date_start=null, $date_end=null ){
	$query = ord_returns_get(0, $state, $date_start, $date_end);
	if( !$query ){
		return false;
	}
	return ria_mysql_num_rows($query);
}

/** Cette fonction permet de gérer les notifications de retour
 *	@param int $id Obligatoire, identifiant du bon de retour
 *  @param $prepaid_label Facultatif, adresse du fichier à joindre (étiquette prépayée)
 *	@return bool true si la notification a été envoyée, false dans le cas contraire
 */
function ord_returns_notify( $id, $prepaid_label=false ){
	if( !ord_returns_exists($id) ) return false;
	global $config;

	$ret = ria_mysql_fetch_array( ord_returns_get($id) );
	$state = $ret['states_id'];

	$t = '<p>Cher client, Chère cliente,</p>';
	if( $state == ORD_RETURNS_STATE_QUERY ){
		$t .= '	<p>';
		$t .= '		Votre demande de retour a bien été enregistrée. Un email contenant votre bon de retour et la démarche à suivre va vous être envoyé.<br />';
		$t .= '		Nous vous prions de ne pas retourner vos articles sans votre bon de retour, sans quoi votre demande ne pourra être acceptée.';
		$t .= '	</p>';
	} else {
		$t .= '	<p>';
		$t .= '		Vous recevez ce message car l\'état du retour n°'.str_pad($id, 8, '0', STR_PAD_LEFT).' concernant la commande n°'.str_pad($ret['ord_id'], 8, '0', STR_PAD_LEFT).' a été modifié.<br />';
		$t .= '		'.( $state==ORD_RETURNS_STATE_RETURNED ? 'Les produits ont bien été retournés et sont maintenant en attente d\'examen.' : '' );
		$t .= '</p>';
		if( $state == ORD_RETURNS_STATE_WAIT ){
			$address = site_owner_get( $ret['wst_id_ord'] );
			if( !$address ) return false;
			$adr = $address['name'].'<br />';
			$adr .= $address['address1'].'<br />';
			$adr .= trim($address['address2'])!='' ? $address['address2'].'<br />' : '';
			$adr .= $address['zipcode'].' - '.$address['city'];

			$t .= '	<p>Veuillez trouver en pièce ci-jointe le bon de retour à imprimer et à retourner avec vos produits dans leurs emballages d\'origine à l\'adresse suivante :</p>';
			$t .= '	<p>'.$adr.'</p>';

			// Etiquette prépayée
			if( $prepaid_label ){
				$t .= '	<p>';
				$t .= '		Les frais de retour vous étant offerts, vous trouverez également en pièce ci-jointe l\'étiquette prépayée pour les frais de port qu\'il vous faudra coller sur votre colis.';
				$t .= '		Vous n’aurez ensuite plus qu’à déposer votre colis au bureau de poste le plus proche.';
				$t .= '	</p>';
			}
		}
	}

	$t .= '	<p>Vous trouverez ci-dessous le détail du retour.</p>';

	if( !ord_returns_email_send($id, $t, $prepaid_label) )
		return false;

	return true;
}

/** Cette fonction permet d'avertir le client qu'une demande de retour a été émise
 *	@param int $id Obligatoire, identifiant d'un bon de retour à l'état "Demande effectuée"
 *	@return bool true si le mail a bien été, false dans le cas contraire
 */
function ord_returns_alert_shop_owner( $id ){
	if( !ord_returns_exists($id) ) return false;
	global $config;
	$http_host_ria = $config['backoffice_url'];
	$ret = ria_mysql_fetch_array( ord_returns_get($id) );

	// seules les demandes effectuées son notifiée au client
	if( $ret['states_id']!=ORD_RETURNS_STATE_QUERY )
		return false;

	$rcfg = cfg_emails_get('email-return', $ret['wst_id_ord']);
	if( !ria_mysql_num_rows($rcfg) )
		return false;

	$cfg = ria_mysql_fetch_array($rcfg);

	$subject = 'Nouvelle demande de retour n°'.str_pad($id, 8, '0', STR_PAD_LEFT);

	// Ajout du département dans le sujet pour Chadog
	if( $config['tnt_id'] == 171 ){
		// Charge le website + le département de l'adresse de facturation
		$res = ria_mysql_query('
			select ord_wst_id, adr_postal_code
			from ord_returns
				join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_id = return_ord_id)
				join gu_adresses on (adr_tnt_id = '.$config['tnt_id'].' and adr_id = ord_adr_invoices)
			where return_tnt_id = '.$config['tnt_id'].'
				and return_id = '.$id.'
		');

		if( $res && ria_mysql_num_rows($res) ){
			$r = ria_mysql_fetch_assoc( $res );

			if( $r['ord_wst_id'] == 232 ){ // Vérifie qu'on est bien sur Chadog et pas Natur'Animo
				$dept = substr( $r['adr_postal_code'], 0, 2 );
				$subject = '['.htmlspecialchars( $dept ).'] '.$subject;
			}
		}
	}

	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		$config['email_html_header'] = str_replace('%site%', $config['site_url'], $config['email_html_header']);
		$config['email_html_footer'] = str_replace('%site%', $config['site_url'], $config['email_html_footer']);
	}

	$email = new Email();
	$email->setSubject( $subject );

	$email->setFrom( $cfg['from'] );
	$email->addTo( $cfg['to'] );
	if( $cfg['cc'] )
		$email->addCC( $cfg['cc'] );
	if( $cfg['bcc'] )
		$email->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] )
		$email->setReplyTo( $cfg['reply-to'] );

	// Composition du message
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );

	$email->openTable(600);
	$email->openTableRow(); $email->addCell( '<b>Bons de retour</b>', 'left', 2 ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Numéro de retour :' ); $email->addCell( str_pad($id, 8, '0', STR_PAD_LEFT) ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Numéro de commande :' ); $email->addCell( str_pad($ret['ord_id'], 8, '0', STR_PAD_LEFT) ); $email->closeTableRow();
	$rusr = gu_users_get( $ret['user_id'] );
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_array( $rusr );

		$url_user = 'https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr['id'];
		$email->openTableRow();
		$email->addCell( 'Compte client :' );
		$cellContent = '<a href="'.$url_user.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=user-general">';
		$cellContent .= '<img class="sync" src="https://'.$http_host_ria.'/admin/images/sync/'.( $usr['is_sync'] ? 1 : 0 ).'.svg" title="'.( $usr['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />';
		$cellContent .= ( $usr['ref'] ? $usr['ref'] : $usr['id'] ).'</a>';
		$cellContent .= '<br />';
		$cellContent .= '
			<small>
				<a href="'.$url_user.'&amp;tab=orders&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=user-orders">Commandes</a>
				| <a href="'.$url_user.'&amp;tab=reviews&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=user-reviews">Avis</a>
				| <a href="'.$url_user.'&amp;tab=delayed&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=user-delayed">Reliquats</a>
				| <a href="'.$url_user.'&amp;tab=stats&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=user-stats">Statistiques</a>
			</small>
		';
		$email->addCell( $cellContent );
		$email->closeTableRow();

		$email->openTableRow();
		$email->addCell( 'Adresse email :' );
		$email->addCell( '<a href="mailto:'.$usr['email'].'">'.$usr['email'].'</a>' );
		$email->closeTableRow();
	}
	$email->openTableRow(); $email->addCell( 'Nombre de produits du retour :' ); $email->addCell( $ret['products'].'&nbsp;' ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( 'Etat du retour :' ); $email->addCell( ord_returns_states_get_name($ret['states_id']) ); $email->closeTableRow();
	$email->openTableRow(); $email->addCell( '<a href="https://'.$http_host_ria.'/admin/orders/returns/return.php?ret='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=return-new&amp;utm_content=return">Gérer ce retour en cliquant sur ce lien</a>', 'left', 2 ); $email->closeTableRow();
	$email->closeTable();

	if( $ret['products'] ){
		$total_ttc = 0;

		$email->openTable(1200);
		$email->openTableRow();
			$email->addCell( '<b>Informations sur les produits</b>', 'left', 6 );
		$email->closeTableRow();

		$email->openTableRow();
			$email->addCell( 'Référence' );
			$email->addCell( 'Désignation' );
			$email->addCell( 'Mode de retour' );
			$email->addCell( 'Raison' );
			$email->addCell( 'Etat' );
			$email->addCell( 'Total' );
		$email->closeTableRow();

		$rows = ord_returns_returned_products_get( $id );
		while( $row = ria_mysql_fetch_array($rows) ){
			$email->openTableRow();
				$email->addCell( $row['ref'] );

				$name = $row['name'];
				if( $row['lot'] )
					$name .= ' (Lot '.$row['lot'].')';

				$email->addCell( $name );
				$email->addCell( ord_returns_modes_get_name($row['mode_id']) );

				$reason = '';
				if( $row['reason_id'] )
					$reason .= ord_returns_raison_get_name( $row['reason_id'] ).'<br />';
				$reason .= str_replace("\n", '<br />', htmlspecialchars($row['reason_detail']));
				$email->addCell( $reason );

				$email->addCell( ord_returns_products_states_get_name($row['state_id']) );

				$subtotal = $row['price_ttc'] * $row['qte'];
				$total = str_replace(' ', '&nbsp;', number_format(round($subtotal, 2), 2, ',', ' ')).'&euro;';

				if( $row['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE )
					$total = '<span style="text-decoration: line-through">'.$total.'</span>';
				else
					$total_ttc += $subtotal;

				$email->addCell( $total, 'right' );
			$email->closeTableRow();
		}
		$email->openTableRow();
			$email->addCell( 'Total', 'right', 5 );
			$email->addCell( str_replace(' ', '&nbsp;', number_format(round($total_ttc, 2), 2, ',', ' ')).'&euro;', 'right' );
		$email->closeTableRow();

		$email->closeTable();
	}

	return $email->send();
}

/** Cette fonction permet de mettre à jour le satus d'un bon de retour.
 *	Une notification est envoyé à l'internaute, ainsi qu'au client s'il s'agit d'un nouveau bon de retour.
 *	L'utilisation d'une étiquettte prépayée ne peux se faire que si le statut actuel du bon de retour est "Demande Effectuée"
 *	@param int $id Obligatoire, identifiant d'un bon de retour
 *	@param $state Obligatoire, identifiant d'un status
 *  @param $prepaid_label Facultatif, adresse du fichier à joindre (étiquette prépayée)
 *	@param $notify Optionnel, par défaut un mail est envoyé pour avertir de l'avancement d'un retour, mettre false pour ne pas envoyer de notification
 *	@return bool true si le statut a bien été mise à jour, sinon false dans le cas contraire
 */
function ord_returns_update_status( $id, $state, $prepaid_label=false, $notify=true ){
	if( !ord_returns_exists($id) ) return false;
	if( !ord_returns_states_exists($state) ) return false;
	// if( ord_returns_get_status($id)==$state ) return false;
	global $config;

	// l'utilisation de prepaid_label n'est autorisé que si le statut est à demande effectuée
	if( $prepaid_label && $state!=ORD_RETURNS_STATE_WAIT )
		return false;

	$res = ria_mysql_query('
		update ord_returns
		set return_states_id='.$state.'
		where return_tnt_id='.$config['tnt_id'].'
			and return_id='.$id.'
			and return_date_deleted is null
	');

	if( !$res ){
		return false;
	}

	if( $state<=ORD_RETURNS_STATE_RETURNED ){
		$res = ria_mysql_query('
			update ord_returns_products
			set prd_state_id='.$state.'
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_return_id='.$id.'
		');
		if( !$res ){
			return false;
		}
	}

	// si le status est retour ou retour partiel, on créer une commande avec des quantités négatives
	if( in_array($state, array( ORD_RETURNS_STATE_PARTIAL, ORD_RETURNS_STATE_COMPLETE )) ){
		// récupère les produits du bon de retour
		$rpr = ord_returns_products_get( 0, $id, ORD_RETURNS_PRD_STATE_SUCCESS );
		if( !$rpr ) return false;
		$ar_prd = array();
		while( $pr = ria_mysql_fetch_array($rpr) ){
			// vérifier que le produit n'est pas déjà dans une commande
			if( ord_returns_products_in_order($pr['id'], $pr['qte']) )
				continue;
			$ar_prd[] = $pr;
		}

		if( is_array($ar_prd) && sizeof($ar_prd) ){
			$rord = ord_returns_get_order( $id );
			if( !$rord || !ria_mysql_num_rows($rord) )
				return false;
			$ord = ria_mysql_fetch_array( $rord );

			// créer une commande
			$res = ord_orders_add_sage( $ord['user'], date('d/m/Y H:i:s'), 23, '', '', '', false );
			if( !$res )
				return false;

			$line = 0;
			foreach( $ar_prd as $pr ){
				// ajouter les produits retournés (partiellement ou non) à cette commande avec une quantité négative
				$pres = ord_products_add_sage( $res, $pr['id'], $line, $pr['ref'], $pr['name'], ($pr['qte']*-1), $pr['price_ht'], $pr['tva_rate'], '', $pr['price_ttc'] );
				if( !$pres )
					return false;
				$line++;
			}
		}
	}

	// si le statut est "Retour terminé", alors on annule les points de fidélité
	if( $config['rwd_reward_actived'] && $state==ORD_RETURNS_STATE_COMPLETE ){
		$ord_id = ord_returns_get_order( $id, true );
		if( $ord_id ){
			rwd_actions_apply( 'RWC_RETURN_ORDER', CLS_ORDER, $ord_id, array('return'=>$id) );
		}

		// création d'un avoir du montant des produits acceptés et souhaitant un avoir
		$rp = ord_returns_products_get( 0, $id, ORD_RETURNS_PRD_STATE_SUCCESS );
		if( $rp && ria_mysql_num_rows($rp) ){
			$code = strtoupper( pmt_codes_generated() );

			$discount = 0;
			while( $p = ria_mysql_fetch_array($rp) ){
				if( $p['mode']!=ORD_RETURNS_MODE_AVOIR ) continue;

				$discount += round( $p['price_ht'] * $p['qte'], $config['round_digits_count'] );
			}

			if( $discount>0 ){
				// création du code promotion
				$cod = pmt_codes_add( 'Avoir '.$code, _PMT_TYPE_CREDIT, $code, 'Avoir sur le retour n°'.$id );
				if( !$cod || !($user = ord_returns_get_user($id)) ){
					return false;
				}

				$d_start = date('Y-m-d H:i:00');
				$d_stop  = date('Y-m-d H:i:59', strtotime('+30 day'));
				$r = pmt_offers_add( $cod, _PMT_TYPE_CREDIT, $discount, 0, '', 0, 0, 0, 'order', $d_start, $d_stop, 0, false );

				$r = $r && pmt_codes_set_all_customers( $cod, false );
				$r = $r && pmt_users_add( $cod, $user, 1 );
				$r = $r && pmt_codes_set_all_catalog( $cod, true );
				$r = $r && ria_mysql_query('update ord_returns set return_cod_id='.$cod.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$id);
				if( !$r ){
					pmt_codes_del( $cod );
					return false;
				}
			}
		}
	}

	if( !ord_returns_notify($id, $prepaid_label) )
		return false;

	if( $state==ORD_RETURNS_STATE_QUERY && $notify )
		return ord_returns_alert_shop_owner( $id );

	return true;
}

/** Cette fonction modifie un retour
 *  @param int $id Obligatoire, identifiant du retour
 *  @param $state Facultatif, état du retour : false indique que ce champ ne sera pas modifié.
 *  @return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_update( $id, $state=false ){
	// récupère le retour
	$return = ord_returns_get($id);
	if( !$return )
		return false;
	if(! ($return = ria_mysql_fetch_array($return)) )
		return false;

	$set = array();

	$lastState = $return['states_id'];
	if( $state == $lastState )
		$state = false;

	global $config;

	if( $state !== false ){
		if( !ord_returns_states_exists($state) )
			return false;
		// on ne peut pas passer à l'état demande effectuée si aucun produit
		if( $state == ORD_RETURNS_STATE_QUERY && $return['products'] == 0 )
			return false;

		// on ne peut pas passer à l'état en attente d'examen si tous les produits ne sont pas réceptionnés
		if( $state == ORD_RETURNS_STATE_RETURNED && ria_mysql_num_rows(ria_mysql_query('
			select 1
			from ord_returns_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$id.'
			and prd_state_id <> '.ORD_RETURNS_STATE_RETURNED.'
			limit 0, 1
		')) )
			return false;

		// one ne peut pas passer à l'état traité si tous les produits n'ont pas été traités
		if( $state == ORD_RETURNS_STATE_COMPLETE && ria_mysql_num_rows(ria_mysql_query('
			select 1
			from ord_returns_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$id.'
			and not prd_state_id in ('.ORD_RETURNS_PRD_STATE_SUCCESS.','.ORD_RETURNS_PRD_STATE_FAILURE.')
			limit 0, 1
		')) )
			return false;

		// $set[] = 'return_states_id = '.$state;

		// si l'état passe à ORD_RETURNS_STATE_QUERY, on met à jour la date
		if( $state == ORD_RETURNS_STATE_QUERY )
			$set[]= 'return_date = now()';
	}

	if( count($set) == 0 )
		return true;

	$sql = '
		update ord_returns
		set '.implode(', ', $set).'
		where return_id = '.$id.'
		and return_tnt_id = '.$config['tnt_id'].'
	';

	$res = ria_mysql_query($sql);
	if( !$res )
		return false;

	// special state modifie l'état de chaque produit
	if( $state !== false && $lastState <= ORD_RETURNS_STATE_QUERY ){
		$sql = '
			update ord_returns_products
			set prd_state_id = '.$state.'
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$id.'
		';
		if( !ria_mysql_query($sql) )
			return false;
	}

	return ord_returns_update_totals( $id );
}

/** Cette fonction permet de mettre à jour les totaux d'un bon de retour
 *	@param int $id Obligatoire, identifiant d'un retour
 *	@return bool true si les totaux ont été mis à jour, false dans le cas contraire
 */
function ord_returns_update_totals( $id ){
	global $config;

	if( !ord_returns_exists($id) ){
		return false;
	}

	$return = ria_mysql_fetch_assoc( ord_returns_get($id) );

	// Charge la catégorie tarifaire à partir du compte client
	$prf = isset($_SESSION['usr_prf_id']) && is_numeric($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : false;
	$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
	if( $return['user_id'] ){
		if( $rusr = gu_users_get($return['user_id']) ){
			if( $usr_data = ria_mysql_fetch_array($rusr) ){
				$prc = $usr_data['prc_id'];
				$prf = $usr_data['prf_id'];
			}
		}
	}

	// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
	$round_ttc = prd_prices_categories_get_ttc($prc);
	$ord_update_totals_with_ttc = !isset($config['ord_update_totals_with_ttc']) || $config['ord_update_totals_with_ttc'];

	$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

	$sub_sql = ord_orders_colisage_sub_sql('o.return_piece', $ratio, 'op.');

	$round_ttc_count = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];
	$round_dec = $round_ttc_count;
	if( $return['piece']!='' && $config['tnt_id']==13 ){
		$round_dec = '( if(ifnull(p.prd_sell_weight, 0) = 1, '.$config['round_digits_count'].' + log10('.$ratio.'), '.$round_ttc_count.') )';
	}

	$sum_ht = 'sum( prd_price_ht * ('.$sub_sql.') )';
	$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', prd_price_ht * prd_tva_rate)'.($round_ttc ? ','.$round_dec.')' : '').' * ('.$sub_sql.') ) ';

	$round_after_qte = isset($config['round_digits_after_qte']) && $config['round_digits_after_qte'];
	if( $round_after_qte ){
		$sum_ht = 'sum( round(prd_price_ht * ('.$sub_sql.'),'.$round_dec.') ) ';
		$sum_ttc = 'sum( round(ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', prd_price_ht * prd_tva_rate) * ('.$sub_sql.'),'.$round_dec.') ) ';
	}

	$sql_total = '
		select
			('.$sum_ht.') as total_ht,
			('.$sum_ttc.') as total_ttc,
			sum( '.$sub_sql.' ) as products
		from
			ord_returns_products as op
				join ord_returns as o on op.prd_return_id = o.return_id and op.prd_tnt_id=o.return_tnt_id
				left join prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
				left join prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(prd_col_id, 0)=col_id and col_is_deleted = 0 )
		where
			op.prd_tnt_id='.$config['tnt_id'].'
			and op.prd_return_id='.$id.'
	';

	$r_total = ria_mysql_query( $sql_total );

	if( !$r_total || !ria_mysql_num_rows($r_total) ){
		return false;
	}

	$total = ria_mysql_fetch_assoc( $r_total );

	return ria_mysql_query('
		update ord_returns
		set return_total_ht ='.$total['total_ht'].',
			return_total_ttc ='.$total['total_ttc'].',
			return_products ='.$total['products'].'
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$id.'
			and return_date_deleted is null
	');
}
/** Cette fonction renvoie la liste des états que l'administrateur peut affecter à un retour en fonction de son état actuel
 *  @param int $id Obligatoire, Identifiant du retour
 *  @return Liste des identifiants des états autorisés
 */
function ord_returns_states_update_get( $id ){
	$rret = ord_returns_get($id);
	if( !$rret || !ria_mysql_num_rows($rret) ){
		return array();
	}

	$ret = ria_mysql_fetch_array( $rret );
	if( $ret['states_id'] < ORD_RETURNS_STATE_WAIT || $ret['states_id'] == ORD_RETURNS_STATE_COMPLETE ){
		return array();
	}

	$complete = $returned = $count = 0;

	$products = ord_returns_returned_products_get($id);
	if( $products ){
		$count = ria_mysql_num_rows($products);

		while( $dat = ria_mysql_fetch_array($products) ){
			$state = $dat['state_id'];
			if( $state == ORD_RETURNS_PRD_STATE_SUCCESS || $state == ORD_RETURNS_PRD_STATE_FAILURE ){
				$returned++; $complete++;
			} elseif( $state == ORD_RETURNS_PRD_STATE_RETURNED )
				$returned++;
		}
	}

	$r = array();
	if( $ret['states_id'] == ORD_RETURNS_STATE_WAIT ){
		$r[] = ORD_RETURNS_STATE_RETURNED;
	}

	if( $complete == $count ){
		$r[] = ORD_RETURNS_STATE_COMPLETE;
	}elseif( $complete > 0 && $ret['states_id']!=ORD_RETURNS_STATE_PARTIAL ){
		$r[] = ORD_RETURNS_STATE_PARTIAL;
	}
	return $r;
}

/**	Déplace la raison avant ou après une autre raison
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 raisons appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param $source Identifiant de la raison source
 *	@param $target Identifiant de la raison cible
 *	@param $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function ord_returns_reason_position_update( $source, $target, $where ){
	return obj_position_update( DD_ORD_RETURN_REASON, $source, $target, $where );
}

// \cond onlyria
/**	Recherche les BR qui doivent être importées dans la gestion commerciale
 *
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du BR
 *		- date : date de création du BR
 *		- sync_past : 0 si le BR à été modifiée il y a moins d'une heure, 1 dans le cas contraire
 */
function ord_returns_get_new_to_import(){
	global $config;

	// maintenir les conditions where à l'identique dans gu_users_toimport_get
	$sql = '
		select 	return_id as id,
				return_date as date,
				case when return_date_modified < now() - INTERVAL 1 HOUR  then 1
				else 0 END as sync_past
		from
			ord_returns
			join gu_users as u1 on return_user_id = u1.usr_id and return_tnt_id = if(u1.usr_tnt_id=0, return_tnt_id, u1.usr_tnt_id)
			join gu_users as u2 on '.( $config['parent_is_order_holder'] ? 'ifnull(u1.usr_parent_id, u1.usr_id)' : 'u1.usr_id' ).' = u2.usr_id and u1.usr_tnt_id = u2.usr_tnt_id
		where
			return_tnt_id = '.$config['tnt_id'].'
			and return_states_id in ('.ORD_RETURNS_STATE_QUERY.','.ORD_RETURNS_STATE_CREATE.')
			and return_piece = ""
			and return_need_sync = 1
			and u1.usr_date_deleted is null
			and u2.usr_date_deleted is null
	';

	$exists_usr_sync  = 'exists ( ';
	$exists_usr_sync .= '	select 1 ';
	$exists_usr_sync .= '	from rel_relations_hierarchy ';
	$exists_usr_sync .= '	join gu_users cu2 on rrh_tnt_id=cu2.usr_tnt_id and rrh_src_0=cu2.usr_id ';
	$exists_usr_sync .= '	where rrh_rrt_id='.REL_USR_HIERARCHY.' ';
	$exists_usr_sync .= '		and rrh_tnt_id=return_tnt_id and rrh_dst_0=return_user_id and rrh_dst_1=0 and rrh_dst_2=0 ';
	$exists_usr_sync .= '		and cu2.usr_is_sync=1 ';
	$exists_usr_sync .= '		and cu2.usr_ref != "" ';
	$exists_usr_sync .= '		and cu2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ';
	$exists_usr_sync .= ') ';

	if( $config['import_orders_from_sync_usr'] ){
		$sql .= ' and (u2.usr_ref != "" or '.$exists_usr_sync.') ';
	}else{
		$sql .= ' and (u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') or '.$exists_usr_sync.') ';
	}

	$r = ria_mysql_query($sql);

	if( mysql_error() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction met à jour le statut d'un retour
 *
 *	@param $return_id Obligatoire, identifiant du retour
 *	@param $status Obligatoire, identifiant du statut
 *
 *	@return le nombre de lignes affectées en cas de succès, false en cas d'échec
 */
function ord_returns_state_update( $return_id, $status ){
	if( !is_numeric($return_id) || $return_id <= 0 ){
		return false;
	}

	if( !ord_states_exists( $status ) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_states_id = '.$status. '
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id . '
	';

	$res = ria_mysql_query($sql);

	// Mis à jour du retour.
	if( !$res ){
		return false;
	}

	return $res;
}
// \endcond
/// @}

// \cond onlyria
/** Détermine le statut d'un return
 *	@param int $id Identifiant du return
 *	@return bool False en cas d'échec, identifiant du statut du return sinon
 */
function ord_returns_get_state( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select return_states_id as state_id
		from ord_returns
		where return_tnt_id='.$config['tnt_id'].' and return_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 'state_id' );
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le service de livraison d'un bon de retour.
 *
 *	@param $return_id Identifiant du bon de retour
 *	@param int $srv_id nouvel identifiant du service de retour
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_update_dlv_service( $return_id, $srv_id ){
	if( !is_numeric( $return_id ) || $return_id<=0 ) return false;
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !is_numeric( $srv_id ) || $srv_id<=0 ) return false;
	global $config;

	return ria_mysql_query('update ord_returns set return_srv_id="'.$srv_id.'" where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le contact qui à pris le bon de retour, attention il n'y a pas de contrôle sur le fait que ce soit un contact ou un utilisateur, il est donc possible d'avoir dans ce champs des comptes qui ne sont pas des contacts.
 *	@param $return_id Identifiant du bon de retour
 *	@param $contact_id identifiant du contact qui à pris le bon de retour
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_returns_set_contact_id( $return_id, $contact_id ){

	if( !is_numeric($return_id) || $return_id <= 0 ){
		return false;
	}
	if( !is_numeric($contact_id) || ($contact_id > 0 && !gu_users_exists($contact_id)) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_contact_id = '.( $contact_id <= 0 ? 'null' : $contact_id ).'
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de l'adresse de facturation d'un BL'.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param $return_id Identifiant du bon de retour à modifier
 *	@param $adr Identifiant de l'adresse de facturation, ou false pour utiliser l'adresse de retour
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_adr_invoice_set( $return_id, $adr=false ){
	global $config;
	if( !ord_returns_exists($return_id) ) return false;
	if( !gu_adresses_exists($adr) ){
		error_log( 'ord_returns_adr_invoice_set utilisé avec un $adr faux (tenant '.$config['tnt_id'].').' );
		return false;
	}else{
		$res = ria_mysql_query('update ord_returns set return_adr_invoices='.$adr.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
	}

	return $res;
}
// \endcond

/**	Cette fonction permet la modification de l'adresse de retour d'un bon de retour.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param $return_id Identifiant du bon de retour à modifier
 *	@param $adr Identifiant de l'adresse de retour, ou false pour utiliser l'adresse de retour par défaut
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_returns_adr_delivery_set( $return_id, $adr=false ){
	global $config;
	if( !ord_returns_exists($return_id) ) return false;
	if( !gu_adresses_exists($adr) ){

		// chargement de l'adresse de livraison par défaut du client
		$radr = ria_mysql_query('
			select ifnull(usr_adr_delivery, 0) as adr_dlv, usr_adr_invoices as adr_inv
			from gu_users join ord_returns on usr_id=return_user_id
			where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id.' and ( usr_tnt_id='.$config['tnt_id'].' or usr_tnt_id=0 )
		');

		$adr_defaut = 'return_adr_invoices'; // ancien comportement
		if( $radr && ria_mysql_num_rows($radr) ){
			// $adr_defaut = ria_mysql_result( $radr, 0, 0 );
			$adr = ria_mysql_fetch_assoc($radr);

			if (gu_adresses_exists($adr['adr_dlv'])) {
				$adr_defaut = $adr['adr_dlv'];
			}else{
				$adr_defaut = $adr['adr_inv'];
			}
		}

		$res = ria_mysql_query('update ord_returns set return_adr_delivery='.$adr_defaut.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
	}else{
		$res = ria_mysql_query('update ord_returns set return_adr_delivery='.$adr.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
	}

	return $res;
}

// \cond onlyria
/**	Cette fonction permet de mettre à jour la date de livraison prévue d'un bon de retour.
 *	@param $return_id Identifiant de bon de retour
 *	@param string $date_livr Date de livraison prévue de la commande (format FR ou EN, avec ou sans la partie horaire. Si cette dernière est spécifiée, elle n'est pas prise en compte). Si la date spécifiée est invalide, la date de livraison de la commande est assignée à NULL, aucune erreur n'est retournée.
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_returns_set_date_livr( $return_id, $date_livr ){

	if( !is_numeric($return_id) || $return_id <= 0 ){
		return false;
	}

	$datesql = 'NULL';
	if( isdateheure($date_livr) ){
		$datesql = 'DATE("'.dateheureparse($date_livr).'")';
	}

	global $config;

	$sql = '
		update ord_returns
		set return_date_livr = '.$datesql.'
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id.'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le renseignement de l'information 'Magasin de livraison' pour un bon de retour.
 *	Appellée sans le second argument, cette fonction efface le champ 'Magasin de livraison'
 *	@param $return_id Identifiant du bon de retour
 *	@param int $str Identifiant du magasin
 */
function ord_returns_set_dlv_store( $return_id, $str=false ){
	if( !is_numeric($return_id) || $return_id<=0 ) return false;
	if( !is_numeric($str) || $str<=0 ) $str = 'null';
	global $config;

	return ria_mysql_query('update ord_returns set return_str_id='.$str.' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le type de package ( Cartons, palette, ... )
 *	@param $return_id Identifiant du bon de retour
 *	@param $pkg Identifiant de l'emballage, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_returns_set_package( $return_id, $pkg ){
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !is_numeric( $pkg ) || $pkg<0 ) return false;
	global $config;

	$sql = 'update ord_returns set return_pkg_id='.( $pkg==0 ? 'NULL' : $pkg ).' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le numero de piece d'un BR
 *  @param int $id Obligatoire, Identifiant du br
 * 	@param string $piece Obligatoire, Nouveau numéro de pièce
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_returns_set_piece( $id, $piece ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_string($piece) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_piece = "'.addslashes($piece).'", return_need_sync=0
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

/**	Cette fonction permet la modification des consignes de livraison.
 *	Appellée sans le deuxième argument, cette fonction efface les consignes de livraison.
 *	@param $return_id Identifiant de bon de retour
 *	@param $notes Consignes de livraison
 */
function ord_returns_dlv_notes_set( $return_id, $notes='' ){
	global $config;
	if( !ord_returns_exists($return_id) ) return false;
	return ria_mysql_query('update ord_returns set return_dlv_notes=\''.addslashes(trim($notes)).'\' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}

// \cond onlyria
/**	Cette fonction permet l'association d'un identifiant de vendeur a un bon de retour donnée.
 *	@param $return_id Identifiant du bon de retour à modifier
 *	@param int $seller Identifiant de vendeur a attribuer au bon de retour (ou NULL pour détacher le représentant)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function ord_returns_set_seller_id( $return_id, $seller ){
	if( !is_numeric($return_id) || $return_id<=0 ) return false;
	if( $seller!==null && ( !is_numeric($seller) || $seller<=0 ) ) return false;
	global $config;

	return ria_mysql_query('update ord_returns set return_seller_id='.( $seller!==null ? $seller : 'NULL' ).' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre a jour le commentaire du bon de retour
 *	Appellée sans le deuxième argument, cette fonction efface le commentaire du bon de retour.
 *	@param $return_id Identifiant du bon de retour
 *	@param string $comments Commentaire du bon de retour
 */
function ord_returns_comments_set( $return_id, $comments='' ){
	global $config;
	if( !ord_returns_exists($return_id) ) return false;
	return ria_mysql_query('update ord_returns set return_comments=\''.addslashes(trim($comments)).'\' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le dépôt de livraison
 *	@param $return_id Identifiant du bon de retour
 *	@param int $dps Identifiant du dépôt, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_returns_set_deposit( $return_id, $dps ){
	if( !ord_returns_exists( $return_id ) ) return false;
	if( !is_numeric( $dps ) || $dps<0 ) return false;
	global $config;

	$sql = 'update ord_returns set return_dps_id='.( $dps==0 ? 'NULL' : $dps ).' where return_tnt_id='.$config['tnt_id'].' and return_id='.$return_id;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le site du locataire sur lequel le bon de retour a été passée
 *	Note : le website ne peut pas être réinitialisé à NULL par cette fonction, bien que la structure SQL l'autorise
 *	@param $return_id Identifiant du bon de retour
 *	@param $website Identifiant du site
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_returns_set_website( $return_id, $website ){
	if( !ord_returns_exists($return_id) ) return false;
	if( !wst_websites_exists($website) ) return false;

	$rweb = wst_websites_get( $website );
	if( !$rweb || !ria_mysql_num_rows($rweb) ) return false;
	$web = ria_mysql_fetch_array($rweb);

	global $config;

	if( $web['tnt_id'] != $config['tnt_id'] ) return false;

	$sql = '
		update ord_returns set return_wst_id='.$website.' where return_id='.$return_id.' and return_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le revendeur qui à pris le bon de retour, attention il n'y a pas de contrôle sur le fait que ce soit un utilisateur-revendeur.
 *	@param $return_id Identifiant du bon de retour.
 *	@param int $reseller_id identifiant du revendeur qui à pris le bon de retour
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_returns_set_reseller_id( $return_id, $reseller_id ){

	if( !is_numeric($return_id) || $return_id <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_id) || ($reseller_id > 0 && !gu_users_exists($reseller_id)) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_reseller_id = '.( $reseller_id <= 0 ? 'null' : $reseller_id ).'
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id.'
	';

	return ria_mysql_query($sql);

}
// \endcond
// \cond onlyria
/**	Cette fonction permet de mettre à jour le contact revendeur qui à pris le bon de retour, attention il n'y a pas de contrôle sur le fait que ce soit un contact ou un utilisateur, il est donc possible d'avoir dans ce champs des comptes qui ne sont pas des contacts.
 *	@param $return_id Identifiant du bon de retour
 *	@param $reseller_contact_id identifiant du contact revendeur qui à pris la commande
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_returns_set_reseller_contact_id( $return_id, $reseller_contact_id ){

	if( !is_numeric($return_id) || $return_id <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_contact_id) || ($reseller_contact_id > 0 && !gu_users_exists($reseller_contact_id)) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns
		set return_reseller_contact_id = '.( $reseller_contact_id <= 0 ? 'null' : $reseller_contact_id ).'
		where return_tnt_id = '.$config['tnt_id'].'
			and return_id = '.$return_id.'
	';

	return ria_mysql_query($sql);

}

// \cond onlyria
/** Modifie le group_id et le group_parent_id pour la ligne du bon de retour donnée.
 *
 * @param int $return_id L'identifiant du bon de retour
 * @param int $prd Identifiant du produit
 * @param int $line L'identifiant de la ligne
 * @param int $group_id L'identifiant du groupe. Si NULL la valeur sera supprimée
 * @param int $group_parent_id  Optionnel. L'identifiant du parent du groupe. Si NULL la valeur sera supprimée
 * @return bool true si succès, false autrement
 */
function ord_returns_products_set_group_id( $return_id, $prd, $line, $group_id=false, $group_parent_id=false ){

	if(
		(!is_numeric($return_id) || $return_id <= 0) ||
		(!is_numeric($prd) || $prd < 0) ||
		(!is_numeric($line) || $line < 0) ||
		($group_id===false || ($group_id!==null && !is_numeric($group_id))) ||
		($group_parent_id!==false && $group_parent_id!==null && !is_numeric($group_parent_id))
	){
		return false;
	}

	global $config;

	$sql = '
		update ord_returns_products
		set	prd_group_id='. ( $group_id!==null ? $group_id : 'null' ).'
		'.( $group_parent_id!==false ? ',prd_group_parent_id='. ( $group_parent_id!==null ? $group_parent_id : 'null' ) : '' ).'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$return_id.'
			and prd_id = '.$prd.'
			and prd_line_id = '.$line.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	ord_returns_set_date_modified($return_id);

	return true;
}
// \endcond
// \endcond

// \cond onlyria
/** Cette fonction permet l'ajout d'un interligne dans le bon de retour
 * 	@param int $return_id Obligatoire, identifiant du bon de retour
 * 	@param $get_line_id Facultatif, variable booléenne permettant de savoir s'il l'on veut renvoyer le line_id de la ligne, ou pas
 * 	@param $line_id Facultatif, permet de définir l'identifiant de ligne
 * 	@param $position Facultatif, permet de définir la position dans le tri des lignes
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_add_spacing($return_id, $get_line_id=false, $line_id=false, $position=false){
	if (!is_numeric($return_id) || $return_id <= 0){
		return false;
	}

	if ($line_id !== false){
		if (!is_numeric($line_id) || $line_id < 0){
			return false;
		}
	}

	global $config;

	$line = 0;
	if ($line_id === false){
		$sql_line = '
			select prd_line_id
			from ord_returns_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$return_id.'
			and prd_id = 0
			order by prd_line_id desc
			limit 0,1
		';

		//Recherche la ligne
		$result_line = ria_mysql_query($sql_line);

		if ($result_line && ria_mysql_num_rows($result_line)){
			$line = ria_mysql_result($result_line, 0, 0) + 1;
		}
	} else {
		$line = $line_id;
	}

	$pos = 0;
	if ($position === false){
		$sql_pos = '
		select prd_pos
			from ord_returns_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$return_id.'
			order by prd_pos desc
			limit 0,1
		';

		//Recherche la ligne
		$result_pos = ria_mysql_query($sql_pos);

		if ($result_pos && ria_mysql_num_rows($result_pos)){
			$pos = ria_mysql_result($result_pos, 0, 0) !== null ? ria_mysql_result($result_pos, 0, 0) + 1 : 'null';
		}
	} else {
		$pos = $position !== null ? $position : 'null';
	}

	// Crée la requête
	$fields = array( 'prd_tnt_id', 'prd_return_id', 'prd_id', 'prd_line_id', 'prd_qte', 'prd_ref', 'prd_name', 'prd_price_ht', 'prd_tva_rate', 'prd_pos' );
	$values = array( $config['tnt_id'], $return_id, 0, $line, 0, '""', '""', 0 , 0, $pos );

	$fields[] = 'prd_notes';
	$values[] = '""';

	$fields[] = 'prd_date_created';
	$values[] = 'now()';

	// Insère la ligne
	$sql = 'insert into ord_returns_products ( '.implode( ', ', $fields ).' ) values ( '.implode( ', ', $values ).' )';

	$res = ria_mysql_query($sql);

	if (!$res){
		return false;
	} else {
		ord_returns_set_date_modified( $return_id );
	}

	return $get_line_id ? $line : $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la récupération d'un interligne dans le bon de retour
 * 	@param int $return_id Obligatoire, identifiant de bon de retour
 * 	@param $line Optionnel, ligne de l'interligne dans le bon de retour
 * 	@return resource un résultat de requête MySQL comprenant les colonnes suivantes en cas de succès :
 * 		- content : contenu de l'interligne
 *
 * 	@return bool false en cas d'échec
 */
function ord_returns_products_get_spacing($return_id, $line=false){
	if (!is_numeric($return_id) || $return_id <= 0){
		return false;
	}
	if ($line !== false){
		if (!is_numeric($line) && $line < 0){
			return false;
		}
	}

	global $config;

	$sql = '
		select prd_notes as content
		from ord_returns_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_return_id = '.$return_id.'
		and prd_id = 0
	';

	if ($line !== false){
		$sql .= ' and prd_line_id = '.$line;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'un interligne dans le bon de retour
 * 	@param int $return_id Obligatoire, identifiant du bon de retour
 * 	@param $line Optionnel, ligne de l'interligne dans le bon de retour
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_del_spacing($return_id, $line=false){
	if (!is_numeric($return_id) || $return_id <= 0){
		return false;
	}
	if ($line !== false){
		if (!is_numeric($line) && $line < 0){
			return false;
		}
	}

	global $config;

	$sql = '
		delete from ord_returns_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_return_id = '.$return_id.'
		and prd_id = 0
	';

	if ($line !== false){
		$sql .= ' and prd_line_id = '.$line;
	}

	$res = ria_mysql_query($sql);

	if ($res){
		ord_returns_set_date_modified( $return_id );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour du contenu d'un interligne dans le bon de retour
 * 	@param int $return_id Obligatoire, identifiant du bon de retour
 * 	@param $line Obligatoire, ligne de l'interligne dans le bon de retour
 * 	@param $content Obligatoire, contenu de l'interligne
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_set_spacing($return_id, $line, $content){
	global $config;

	if (!is_numeric($return_id) || $return_id <= 0){
		return false;
	}
	if (!ria_mysql_num_rows(ord_returns_products_get_spacing($return_id, $line))){
		return false;
	}

	$content = trim($content);

	$res = ria_mysql_query('
		update ord_returns_products
			set prd_notes = \''.addslashes($content).'\'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$return_id.'
			and prd_line_id = '.$line.'
			and prd_id = 0
	');

	if ($res){
		ord_returns_set_date_modified( $return_id );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour d'un interligne dans le bon de retour
 * 	@param int $return_id Obligatoire, identifiant du bon de retour
 * 	@param $line Obligatoire, ligne de l'interligne dans le bon de retour
 * 	@param $content Facultatif, contenu de l'interligne
 * 	@param $position Facultatif, position de l'interligne dans le bon de retour
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_returns_products_update_spacing($return_id, $line, $content='', $position=false){
	if (!is_numeric($return_id) || $return_id <= 0){
		return false;
	}
	if (!is_numeric($line) || $line < 0){
		return false;
	}

	$pos = false;

	if ($position !== false){
		$pos = $position !== null ? $position : 'null';
	}

	global $config;

	$content = trim($content);

	$res = ria_mysql_query('
		update ord_returns_products
			set prd_notes = \''.addslashes($content).'\''.($pos !== false ? ', prd_pos = '.$pos : '').'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_return_id = '.$return_id.'
			and prd_line_id = '.$line.'
			and prd_id = 0
	');

	if ($res){
		ord_returns_set_date_modified( $return_id );
	}

	return $res;
}
// \endcond



<?php
require_once 'SelligentAPI.class.php';

/**	Cette classe permet l'échange de données avec Avanci via l'API Selligent
 * Elle est utilisée pour:
 * - Vérifier le statut optin d'un utilisateur
 * - Mettre à jour le statut optin d'un utilisateur
 * - Envoyer les données de parrainage
 *
 * Variables de configuration obligatoires:
 * - selligent_api_organization
 * - selligent_api_name
 * - selligent_api_key
 * - selligent_api_secret
 *
 * Variable de configuration obligatoire pour l'envoie des données de parrainage:
 * - selligent_api_trigger
 *
 *
 * <AUTHOR> <<EMAIL>>
 * @version	1.0.0
 */
final class Selligent extends SelligentAPI {

	protected $user = null; ///< Tableau contenant les informations de l'utilisateur

	/**	Cette méthode permet de récupérer le statut optin de l'utilisateur (S'il est abonné/ désabonné à la newsletter)
	 * @return	string	0 s'il est abonné, 100 s'il ne l'est pas
	 */
	private function getOptinStatus(){
		$ok = 0;

		try {
			$result = $this->loadCheckStatus()->exec();
			$parse = json_decode($result, true);

			if( !is_array($parse) || !isset($parse['data'], $parse['result_code']) ){
				throw new Exception('L\'API ne retourne pas le résultat attendu.', 1);
			}

			if( $parse['result_code'] != 'Succeeded' ){
				throw new Exception('L\'API rencontre une erreur.', 2);
			}

			if( !is_array($parse['data']) || !count($parse['data']) ){
				throw new Exception('Le statut optin de l\'utilisateur n\'a pas pu être vérifié.', 3);
			}

			foreach($parse['data'] as $usr){
				if( !is_array($usr) || count($usr) < 2){
					continue;
				}

				if( $usr[1] == 0 ){
					$ok++;
				}
			}

		}catch( Exception $e ){
			$ok = 0;

			if( $e->getCode() !== 3 ){
				$this->addLog($e->getMessage());
			}
		}

		return $ok ? parent::SELLIGENT_SUBSCRIBE : parent::SELLIGENT_UNSUBSCRIBE;

	}

	/**	Cette méthode permet de savoir si l'utilisateur en cours est abonné à la newsletter
	 * @return	bool	True s'il l'est, false sinon
	 */
	public function isSubscribed(){
		$status = $this->getOptinStatus();

		return $status === parent::SELLIGENT_SUBSCRIBE;

	}

	/**	Cette méthode permet de mettre à jour le statut optin de l'utilisateur
	 * @param	int		$status	Obligatoire, 1 pour inscrire l'utilisateur, 0 pour le désinscrire
	 * @return	bool	True en cas de succès, false en cas d'erreur
	 */
	public function setOptinStatus($status){

		try {

			if( $status !== 1 && $status !== 0 ){
				throw new Exception('Le statut optin est invalide.');
			}
			$ok_status = $status === 1 ? parent::SELLIGENT_SUBSCRIBE : parent::SELLIGENT_UNSUBSCRIBE;
			$result = $this->loadUpdateStatus($ok_status)->exec();
			$parse = json_decode($result, true);

			if( !is_array($parse) || !isset($parse['number_of_updated_records'], $parse['result_code']) ){
				throw new Exception('L\'API ne retourne pas le résultat attendu.');
			}

			if( $parse['result_code'] != 'Succeeded' ){
				throw new Exception('L\'API rencontre une erreur.');
			}

			if( !is_numeric($parse['number_of_updated_records']) || !$parse['number_of_updated_records'] ){
				throw new Exception('Le statut optin n\'a pas pu être mis à jour.');
			}

		}catch( Exception $e ){
			$error = true;
			$this->addLog($e->getMessage());

		}

		return !isset($error);

	}

	/**	Cette méthode permet d'envoyer l'action de parrainage
	 * @param	array		$data	Obligatoire, données à envoyer:
	 * 									- MAIL //< Email du filleul
	 * 									- PRENOM //< Prénom du filleul
	 * 									- NOM //< Nom du filleul
	 * 									- PARRAIN_MESSAGE //< Message du parrain
	 * @return	bool	True en cas de succès, false en cas d'erreur
	 */
	public function sendSponsoring($data){

		try {
			$result = $this->loadSendSponsoring($data)->exec();
			$parse = json_decode($result, true);

			if( !is_array($parse) || !isset($parse['result_code']) ){
				throw new Exception('L\'API ne retourne pas le résultat attendu.');
			}

			if( $parse['result_code'] != 'Succeeded' ){
				throw new Exception('L\'API rencontre une erreur.');
			}

		}catch( Exception $e ){
			$error = true;
			$this->addLog($e->getMessage());

		}

		return !isset($error);

	}

	/**	Cette méthode permet de charger les informations de l'utilisateur en cours
	 * @param	int		$usr_id		Optionnel, Identifiant d'un utilisateur, 0 pour l'utilisateur en cours
	 * @return	Selligent
	 */
	public function __construct($usr_id=0)
	{
		global $config;
		parent::__construct();

		if( !isset($config['user']) || !$config['user'] ){

			if( !is_numeric($usr_id) || !$usr_id ){
				throw new Exception('Utilisateur non identifié.');
			}

			require_once 'users.inc.php';

			$r_user = gu_users_get( $config['user_id'] );

			if( !ria_mysql_num_rows($r_user) ){
				throw new Exception('Utilisatieur non existant.');
			}

			$this->user = ria_mysql_fetch_assoc( $r_user );
			return;

		}

		$this->user = $config['user'];

	}

}
<?php

/**	Classe abstraite, ne peut pas être utilisée sans la classe Selligent
 * Voir classe Selligent pour plus de détails
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 * @todo Supprimer le spé Coopcorico ligne 120 et trouver une façon de l'intégrer de façon à standardiser la chose /!\ Problème de casse
 */
abstract class SelligentAPI
{

	const URL_PORTAL = 'https://avanci.emsecure.net/Portal/Api/organizations/';	///< Constante de classe, URL de base API Selligent

	const SELLIGENT_SUBSCRIBE = '0'; ///< Constante de classe, statut optin abonné

	const SELLIGENT_UNSUBSCRIBE = '100'; ///< Constante de classe, statut option désabonné

	private $api_organization = null; ///< Variable de configuration "selligent_api_organization"

	private $api_name = null; ///< Variable de configuration "selligent_api_name"

	private $api_key = null; ///< Variable de configuration "selligent_api_key"

	private $api_secret = null; ///< Variable de configuration "selligent_api_secret"

	private $api_trigger = null; ///< Variable de configuration "selligent_api_trigger"

	private $data = []; ///< Tableau de données initialisées

	/**	Cette méthode permet d'initialiser les variables de configuration
	 * @return	SelligentAPI
	 */
	protected function __construct()
	{
		global $config;

		$error = $this->verifyConfigurationVariables();

		if ($error > 1) {
			throw new Exception('Plusieurs variables de configuration sont manquantes ou invalides.');
		}

		if ($error === 1) {
			throw new Exception('Une variable de configuration est manquante ou invalide.');
		}
		$this->api_organization = trim($config['selligent_api_organization']);
		$this->api_name = trim($config['selligent_api_name']);
		$this->api_key = trim($config['selligent_api_key']);
		$this->api_secret = trim($config['selligent_api_secret']);

		if( isset($config['selligent_api_trigger']) && is_string($config['selligent_api_trigger']) && trim($config['selligent_api_trigger']) != '' ){
			$this->api_trigger = trim($config['selligent_api_trigger']);
		}
	}

	/**	Permet de charger les données
	 * @return	SelligentAPI	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	protected function loadCheckStatus()
	{
		$url = $this->getURL('search');

		if (!$url) {
			throw new Exception('L\'URL API est invalide.');
		}

		if (!property_exists($this, 'user')) {
			throw new Exception('Utilisateur non identifié.');
		}

		$this->data = [
			CURLOPT_URL				=> $url,
			CURLOPT_HTTPHEADER		=> [
				'X-ApiKey: ' . $this->api_key . ':' . $this->api_secret,
				'Content-Type: application/json'
			],
			CURLOPT_POST			=> true,
			CURLOPT_POSTFIELDS		=> json_encode([
				'fields'		=> [
					'MAIL',
					'OPTOUT'
				],
				'filters'		=> [
					[
						'field_name'	=> 'MAIL',
						'operator'		=> 'EqualTo',
						'field_value'	=> $this->user['email']
					],
				]

			]),
			CURLOPT_RETURNTRANSFER	=> true
		];

		return $this;
	}

	/**	Permet de charger les données pour l'action de mise à jour du statut
	 * @param	int					$status	Obligatoire, 0 = abonné / 100 = désabonné
	 * @return	SelligentAPI		L'objet en cours, une exception sera levée en cas d'erreur
	 */
	protected function loadUpdateStatus($status)
	{

		if ($status !== self::SELLIGENT_SUBSCRIBE && $status !== self::SELLIGENT_UNSUBSCRIBE) {
			throw new Exception('Le statut optin est invalide.');
		}
		$url = $this->getURL('load');

		if (!$url) {
			throw new Exception('L\'URL API est invalide.');
		}

		if (!property_exists($this, 'user')) {
			throw new Exception('Utilisateur non identifié.');
		}
		/**
		 * @todo	Spé coopcorico, à voir pour faire autrement
		 */
		$url .= '?keyFields=MAIL&fields=MAIL%2COPTOUT%2COPTOUT_DT';

		$this->data = [
			CURLOPT_URL				=> $url,
			CURLOPT_HTTPHEADER		=> [
				'X-ApiKey: ' . $this->api_key . ':' . $this->api_secret,
				'Content-Type: application/json'
			],
			CURLOPT_POST			=> true,
			CURLOPT_POSTFIELDS		=> json_encode([
				[
					$this->user['email'],
					$status,
					date('Y-m-d H:i:s')
				]

			]),
			CURLOPT_RETURNTRANSFER	=> true

		];

		return $this;
	}

	/**	Cette méthode permet charger les données pour l'action d'envoyer les données de parrainage
	 * @param	array	$data	Obligatoire, tableau contenant les données à envoyer
	 * @return	SelligentAPI	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	protected function loadSendSponsoring($data)
	{

		if( $this->api_trigger === null ){
			throw new Exception('La variable de configuration "selligent_api_trigger" est manquante ou invalide.');
		}

		if (!is_array($data) || !count($data)) {
			throw new Exception('Données de parrainage manquantes.');
		}

		if (!isset($data['MAIL']) || !gu_valid_email($data['MAIL'])) {
			throw new Exception('Adresse email du filleul n\'est pas valide.');
		}

		if (!isset($data['PRENOM']) || !is_string($data['PRENOM']) || trim($data['PRENOM']) == '') {
			$data['PRENOM'] = 'Undefined';
		}

		if (!isset($data['NOM']) || !is_string($data['NOM']) || trim($data['NOM']) == '') {
			$data['NOM'] = 'Undefined';
		}

		if (!isset($data['PARRAIN_MESSAGE']) || !is_string($data['PARRAIN_MESSAGE']) || trim($data['PARRAIN_MESSAGE']) == '') {
			$data['PARRAIN_MESSAGE'] = 'Undefined';
		}

		$url = $this->getURL('journeys');

		if (!$url) {
			throw new Exception('L\'URL API est invalide.');
		}

		if (!property_exists($this, 'user')) {
			throw new Exception('Utilisateur non identifié.');
		}

		$fields = [
			'user_id'			=> $this->user['id'],
			'parameter_values'	=> [
				'MAIL'				=> $data['MAIL'],
				'PRENOM'			=> $data['PRENOM'],
				'NOM'				=> $data['NOM'],
				'SOURCE'			=> 'AJOUT-PARRAINAGE',
				'CALL_API_DT'		=> date('Y-m-d H:i:s'),
				'PARRAIN_PRENOM'	=> $this->user['adr_firstname'],
				'PARRAIN_NOM'		=> $this->user['adr_lastname'],
				'PARRAIN_MESSAGE'	=> htmlspecialchars($data['PARRAIN_MESSAGE'])
			]
		];

		$this->data = [
			CURLOPT_URL				=> $url,
			CURLOPT_HTTPHEADER		=> [
				'X-ApiKey: ' . $this->api_key . ':' . $this->api_secret,
				'Content-Type: application/json'
			],
			CURLOPT_POST			=> true,
			CURLOPT_POSTFIELDS		=> json_encode($fields),
			CURLOPT_RETURNTRANSFER	=> true

		];

		return $this;
	}

	/**	Permet d'excécuter une requête à l'API
	 * @return	mixed	La réponse telle que l'API la renvoie, une exception sera levée en cas d'erreur
	 */
	protected function exec()
	{

		if (!is_array($this->data) || !count($this->data)) {
			throw new Exception('L\'action n\'a pas été déterminée.');
		}
		$curl = curl_init();

		curl_setopt_array($curl, $this->data);

		$response = curl_exec($curl);

		curl_close($curl);

		return $response;
	}

	/**	Cette méthode permet d'ajouter un log
	 * @param	string	$msg	Optionnel, message du log
	 * @return void
	 */
	protected function addLog($msg = '')
	{
		global $config;

		$msg = is_string($msg) ? $msg : '';

		error_log('[SELLIGENT] [tnt_id - ' . $config['tnt_id'] . '] [user - ' . $this->user['email'] . '] ' . $msg);
	}

	/**	Cette méthode permet de vérifier la présence des variables de configuration obligatoires
	 * @return int	Nombre d'erreur
	 */
	private function verifyConfigurationVariables()
	{
		global $config;

		$error = 0;
		$vars = ['selligent_api_organization', 'selligent_api_name', 'selligent_api_key', 'selligent_api_secret'];

		foreach ($vars as $var) {

			if (!isset($config[$var]) || !is_string($config[$var]) || trim($config[$var]) == '') {
				$error++;
			}
		}
		return $error;
	}

	/**	Cette méthode se charge de construire l'url API
	 * @param	string			$type Obligatoire, types d'action: search, load, etc
	 * @param	array			$params	Optionnel, paramètres supplémentaires
	 * @return	string|bool		URL API, false en cas d'erreur
	 */
	private function getURL($type, $params = [])
	{

		if (!is_string($type) || !in_array($type, ['search', 'load', 'journeys'])) {
			return false;
		}

		if ($type === 'journeys') {
			$url = self::URL_PORTAL . implode('/', [$this->api_organization, $type, 'custom', $this->api_trigger, 'entrypoints', $this->api_trigger, 'trigger']);
		} else {
			$url = self::URL_PORTAL . implode('/', [$this->api_organization, 'lists', $this->api_name, 'data', $type]);
		}

		if (is_array($params) && count($params)) {
			$url .= '?';
			$url .= http_build_query($params);
		}

		return $url;
	}
}

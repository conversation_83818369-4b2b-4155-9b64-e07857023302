<?php

/**	\defgroup cfg_email Configuration des adresses emails
 *	\ingroup configuration
 *	Ce module fournit les fonctions nécessaires a la configuration des alertes emails.
 *
 *	@{
 */

/**	Permet le chargement d'une ou plusieurs configurations de formulaires.
 *	@param string $code Facultatif, code sur lequel filtrer les résultats
 *	@param int $wst Facultatif, identifiant d'un site sur lequel filtrer les résultats (spécifier NULL pour charger toutes les lignes, sinon le site de $config sera utilisé)
 *	@return resource un résultat de requete MySQL comprenant les colonnes suivantes :
 *			- wst_id : identifiant du site auquel cette configuration se rapporte
 *			- code : identifiant du formulaire auquel cette configuration se rapporte
 *			- name : nom du formulaire auquel cette configuration se rapporte
 *			- desc : description de l'alerte email a laquelle cette configuration se rapporte
 *			- allow-from : indique si le champ from est paramétrable
 *			- allow-to : indique si le champ to est paramétrable
 *			- allow-cc : indique si le champ cc est paramétrable
 *			- allow-bcc : indique si le champ bcc est paramétrable
 *			- allow-reply-to : indique si le champ reply-to est paramétrable
 *			- from : adresse email a utiliser comme émetteur de l'email
 *			- to : adresse email a utiliser comme destinataire de l'email
 *			- cc : adresses emails a mettre en copie visible de l'email
 *			- bcc : adresses emails a mettre en copie cachée de l'email
 *			- reply-to : adresse email a utiliser comme adresse pour répondre a l'email
 */
function cfg_emails_get( $code='', $wst=0 ){
	global $config;

	$sql = '
		select
			email_wst_id as wst_id, email_code as code, email_name as name, email_desc as "desc",
			email_allow_from as "allow-from", email_allow_to as "allow-to", email_allow_cc as "allow-cc", email_allow_bcc as "allow-bcc", email_allow_reply_to as "allow-reply-to",
			email_from as "from", email_to as "to", email_cc as cc, email_bcc as bcc, email_reply_to as "reply-to"
		from cfg_emails
		where email_tnt_id='.$config['tnt_id'].'
	';

	if( trim($code)!='' ){
		$sql .= '
			and email_code="'.addslashes($code).'"
		';
	}

	if( is_numeric($wst) && $wst>0 ){
		$sql .= ' and email_wst_id = '.$wst;
	}elseif( $wst!==null ){
		$sql .= ' and email_wst_id = '.$config['wst_id'];
	}

	$sql .= '
		order by email_pos, email_name
	';

	return ria_mysql_query($sql);

}

/** Permet le chargement des configurations emails pour les notifications lié aux abonnements RiaShop.
 *
 * 	@return array Un tableau associatif de la forme suivante :
 * 			array( code => array(
 * 				wst_id (identifiant du site auquel cette configuration se rapporte) => ...,
 *				code (identifiant du formulaire auquel cette configuration se rapporte) => ...,
 *				name (nom du formulaire auquel cette configuration se rapporte) => ...,
 *				desc (description de l'alerte email a laquelle cette configuration se rapporte) => ...,
 *				allow-from (indique si le champ from est paramétrable) => ...,
 *				allow-to (indique si le champ to est paramétrable) => ...,
 *				allow-cc (indique si le champ cc est paramétrable) => ...,
 *				allow-bcc (indique si le champ bcc est paramétrable) => ...,
 *				allow-reply-to (indique si le champ reply-to est paramétrable) => ...,
 *				from (adresse email a utiliser comme émetteur de l'email ) => ...,
 *				to (adresse email a utiliser comme destinataire de l'email) => ...,
 *				cc (adresses emails a mettre en copie visible de l'email) => ...,
 *				bcc (adresses emails a mettre en copie cachée de l'email) => ...
 *				reply-to (adresse email a utiliser comme adresse pour répondre a l'email) => ...
 *				)
 *			)
*/
function cfg_emails_riashop_get(){
	global $ria_db_connect;

	// N'EXISTE PLUS SUR L'ENVIRONNEMENT LUNDIMATIN, IL FAUT CREER LES TUPLES DIRECT EN BDD.

	// Tableau des configurations mail à récupérer
	$ar_cfg = array(
		'vel-riashop-notif',
		'vel-riashop-add-tnt',
	);

	$sql = '
		select
			email_wst_id as wst_id, email_code as code, email_name as name, email_desc as "desc",
			email_allow_from as "allow-from", email_allow_to as "allow-to", email_allow_cc as "allow-cc", email_allow_bcc as "allow-bcc", email_allow_reply_to as "allow-reply-to",
			email_from as "from", email_to as "to", email_cc as cc, email_bcc as bcc, email_reply_to as "reply-to"
		from cfg_emails
		where email_tnt_id = 52 and email_wst_id = 227
			and email_code in ("'.implode('", "', $ar_cfg).'")
	';

	$r_emails = ria_mysql_query($sql);

	$res = array();
	while( $emails = ria_mysql_fetch_assoc($r_emails) ){
		$res[$emails['code']] = $emails;
	}

	return $res;
}

/** Permet le chargement des configurations emails pour les notifications lié aux abonnements Yuto .
 *
 * 	@return array Un tableau associatif de la forme suivante :
 * 			array( code => array(
 * 				wst_id (identifiant du site auquel cette configuration se rapporte) => ...,
 *				code (identifiant du formulaire auquel cette configuration se rapporte) => ...,
 *				name (nom du formulaire auquel cette configuration se rapporte) => ...,
 *				desc (description de l'alerte email a laquelle cette configuration se rapporte) => ...,
 *				allow-from (indique si le champ from est paramétrable) => ...,
 *				allow-to (indique si le champ to est paramétrable) => ...,
 *				allow-cc (indique si le champ cc est paramétrable) => ...,
 *				allow-bcc (indique si le champ bcc est paramétrable) => ...,
 *				allow-reply-to (indique si le champ reply-to est paramétrable) => ...,
 *				from (adresse email a utiliser comme émetteur de l'email ) => ...,
 *				to (adresse email a utiliser comme destinataire de l'email) => ...,
 *				cc (adresses emails a mettre en copie visible de l'email) => ...,
 *				bcc (adresses emails a mettre en copie cachée de l'email) => ...
 *				reply-to (adresse email a utiliser comme adresse pour répondre a l'email) => ...
 *				)
 *			)
*/
function cfg_emails_yuto_get(){
	global $ria_db_connect;

	// N'EXISTE PLUS SUR L'ENVIRONNEMENT LUNDIMATIN, IL FAUT CREER LES TUPLES DIRECT EN BDD.

	// Tableau des configurations mail à récupérer
	$ar_cfg = array(
		'vel-yuto-notif',
		'vel-yuto-add-tnt',
	);

	$r_emails = ria_mysql_query('
		select
			email_wst_id as wst_id, email_code as code, email_name as name, email_desc as "desc",
			email_allow_from as "allow-from", email_allow_to as "allow-to", email_allow_cc as "allow-cc", email_allow_bcc as "allow-bcc", email_allow_reply_to as "allow-reply-to",
			email_from as "from", email_to as "to", email_cc as cc, email_bcc as bcc, email_reply_to as "reply-to"
		from cfg_emails
		where email_tnt_id = 52 and email_wst_id = 227
			and email_code in ("'.implode('", "', $ar_cfg).'")
	');

	$res = array();
	while( $emails = ria_mysql_fetch_assoc($r_emails) ){
		$res[$emails['code']] = $emails;
	}

	return $res;
}

// \cond onlyria
/**	Alias de cfg_emails_code_exists
 *	@param string $code Voir cfg_emails_code_exists
 *	@param int $wst Voir cfg_emails_code_exists
 *	@return Voir cfg_emails_code_exists
 *	@see cfg_emails_code_exists
 */
function sfg_emails_code_exists( $code, $wst=0 ){

	return cfg_emails_code_exists( $code, $wst );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier l'existance d'un code email
 *	@param string $code Obligatoire, code email
 *	@param int $wst Optionnel, identifiant d'un site sur lequel tester (si non spécifié, le wst_id de la config est utilisé). Pour ne pas filtrer, il faut spécifier NULL
 *	@return bool Retourne true si ce code existe dans la base
 *	@return bool Retourne false si ce n'est pas le cas
 */
function cfg_emails_code_exists( $code, $wst=0 ){
	$code = trim($code);
	if( $code=='' ) return false;
	global $config;

	$sql = '
		select 1 from cfg_emails
		where email_tnt_id='.$config['tnt_id'].' and email_code="'.addslashes($code).'"
	';

	if( is_numeric($wst) && $wst>0 ){
		$sql .= ' and email_wst_id = '.$wst;
	}elseif( $wst!==null ){
		$sql .= ' and email_wst_id = '.$config['wst_id'];
	}

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la configuration d'une adresse email.
 *	@param $code identifiant de la configuration à actualiser
 *	@param string $from adresse email utilisée comme expéditeur du message (sera ignoré si allow-from est faux pour cette alerte)
 *	@param string $to adresse email utilisée comme destinataire du message (sera ignoré si allow-to est faux pour cette alerte)
 *	@param string $cc adresses emails a mettre en copie visible de l'email (sera ignoré si allow-cc est faux pour cette alerte)
 *	@param string $bcc adresses emails a mettre en copie cachée de l'email (sera ignoré si allow-bcc est faux pour cette alerte)
 *	@param int $wst identifiant du site
 *	@param string $reply_to adresse email a utiliser comme adresse pour répondre a l'email (sera ignoré si allow-reply-to est faux pour cette alerte)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function cfg_emails_set( $code, $from, $to, $cc, $bcc, $wst, $reply_to='' ){
	global $config;

	if( !wst_websites_exists($wst) ) return false;

	// Charge la configuration
	$rcfg = cfg_emails_get( $code, $wst );
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$up_ar = array();
	if( $cfg['allow-from'] ) 		$up_ar['email_from'] 		= '"'.addslashes(trim($from)).'"';
	if( $cfg['allow-to'] )			$up_ar['email_to'] 			= '"'.addslashes(trim($to)).'"';
	if( $cfg['allow-cc'] )			$up_ar['email_cc'] 			= '"'.addslashes(trim($cc)).'"';
	if( $cfg['allow-bcc'] )			$up_ar['email_bcc'] 		= '"'.addslashes(trim($bcc)).'"';
	if( $cfg['allow-reply-to'] )	$up_ar['email_reply_to'] 	= '"'.addslashes(trim($reply_to)).'"';

	$ok = true;

	foreach( $up_ar as $col=>$val ){
		if( !ria_mysql_query('update cfg_emails set '.$col.' = '.$val.' where email_tnt_id='.$config['tnt_id'].' and email_wst_id='.$wst.' and email_code="'.addslashes($code).'"') )
			$ok = false;
	}

	return $ok;
}
// \endcond

/// @}



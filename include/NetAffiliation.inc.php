<?php
// \cond onlyria

/**	\defgroup netaffiliation NetAffiliation
 * 	\ingroup affiliation
 *	Ce module comprend les classes et fonctions nécessaires à l'interfaçage avec la plateforme d'affiliation NetAffiliation
 *	@{
 */
	
	require_once('rewrite.inc.php');
	require_once('orders.inc.php');
	
	/**	\brief Cette classe permet de gérer les campagnes avec NetAffiliation.
	 *
	 *	NetAffiliation est une plateforme d'affiliation.
	 *	La documentation est disponible à l'adresse suivante : http://www6.netaffiliation.com/images/fr/tracking_ventes.pdf
	 *	
	 *	Variables de config utilisées et obligatoires :
	 *		- netaffiliation_is_active 	: si oui ou non netaffiliation est activé
	 *		- netaffiliation_url		: url utilisée pour enregistrer la commande
	 *		- netaffiliation_mclic  	: identifiant mclic de la campagne NetAffiliation
	 *		- netaffiliation_mcode 		: identifiant mcode de la campagne NetAffiliation
	 *
	 */
	class NetAffiliation {
		
		private $mode = 'http';
		private $pay_cb = array(1, 5, 6); ///< Tableau contenant les moyens de paiement en ligne
		
		/** Cette fonction permet d'activer le mode http ou https de l'url utilisée pour NetAffiliation.
		 *	@param $mode Obligatoire, mode à activer. Par défaut le mode http est activé. Ce paramètre n'accepte que les valeurs http ou https
		 *	@return NetAffiliation L'objet en cours
		 */
		public function setMode( $mode ){
			$mode = !in_array($mode, array('http', 'https')) ? 'http' : $mode;
			$this->mode = $mode;
			return $this;
		}
		
		/** Cette fonction permet de démarrer la campagne NetAffiliation, il s'agit de la première étape à inclure au niveau du choix de paiement.
		 *	@return string Le code HTML du premier tag
		 */
		public function getPrePaymentTag(){
			if( !$this->isActived() ) return false;
			global $config;
			
			$order = $this->getOrderID();
			if( !is_numeric($order) || $order<=0 ){
				return false;
			}
			
			$total_ht = ord_orders_get_total_without_port( $order, false, false );
			
			$url  = $this->mode.'://'.$config['netaffiliation_url'].'?';
			$url .= 'mclic='.$config['netaffiliation_mclic'];
			$url .= '&amp;argtemp='.$order;
			$url .= '&amp;argmon='.$total_ht;
			
			return '<img src="'.$url.'" width="1" height="1" border="0" alt="" />';
		}
		
		/** Cette fonction permet de prévenir NetAffiliation qu'une commande est passé avec un paiement hors-ligne (chèque, virement).
		 *	@return string Le code HTML du second Tag à inclure sur la confirmation de commande
		 */
		public function getOfflinePaymentTag(){
			if( !$this->isActived() ) return false;
			global $config;
			
			$order = $this->getOrderID();
			if( !is_numeric($order) || $order<=0 ){
				return false;
			}
			
			$pay_id = ord_orders_get_pay_id( $order );
			if( !is_numeric($pay_id) || $pay_id<=0 || in_array($pay_id, $this->pay_cb) ){
				return false;
			}
			
			$url  = $this->mode.'://'.$config['netaffiliation_url'].'?';
			$url .= 'mclic='.$config['netaffiliation_mclic'];
			$url .= '&amp;argtemp='.$order;
			$url .= '&amp;argann='.$order;
			
			return '<img src="'.$url.'" width="1" height="1" border="0" alt="" />';
		}

		/** Cette fonction permet de prévenir NetAffiliation qu'une commande est passée avec un paiement en ligne (CB, PayPal...).
		 *	Cette fonction est à appeler dans la fonction qui gère le retour de paiement d'un service bancaire.
		 *	@param int $order Facultatif, identifiant de commande à utiliser (surcharge l'identifiant de commande contenu dans la session)
		 *	@return bool True si tout s'est correctement passé, False dans le cas contraire
		 */
		public function getOnlinePaymentTag( $order=false ){
			if( !$this->isActived() ) return false;
			global $config;
			
			$order = $order!==false ? $order : $this->getOrderID();
			if( !is_numeric($order) || $order<=0 ){
				return false;
			}
			
			$pay_id = ord_orders_get_pay_id( $order );
			if( !is_numeric($pay_id) || $pay_id<=0 || !in_array($pay_id, $this->pay_cb) ){
				return false;
			}
			
			$url  = $this->mode.'://'.$config['netaffiliation_url'].'?';
			$url .= 'mclic='.$config['netaffiliation_mclic'];
			$url .= '&argtemp='.$order;
			$url .= '&argann='.$order;
			
			@file_get_contents( $url );
			return true;
		}
		
		/** Cette fonction permet de prévenir NetAffiliation qu'une commande est finalisée.
		 *	@return string Le code HTML a inclure sur la page de confirmation de commande (seulement pour le paiement en ligne)
		 */
		public function getConfirmedOrderTag(){
			if( !$this->isActived() ) return false;
			global $config;
			
			$order = $this->getOrderID();
			if( !is_numeric($order) || $order<=0 ){
				return false;
			}
			
			$pay_id = ord_orders_get_pay_id( $order );
			if( !is_numeric($pay_id) || $pay_id<=0 || !in_array($pay_id, $this->pay_cb) ){
				return $this->getOfflinePaymentTag();
			}
			
			$url  = $this->mode.'://'.$config['netaffiliation_url'].'?mcode';
			
			$html  = '	<iframe frameborder="0" width="1" height="1" scrolling="no" src="'.$url.'='.$config['netaffiliation_mcode'].'">';
			$html .= '		<img src="'.$url.'='.$config['netaffiliation_mclic'].'" width="1" height="1" border="0" alt="" />';
			$html .= '	</iframe>';
			
			return $html;
		}
		
		/** Cette fonction permet de mettre en place tracking de PartnerTag.
		 */
		public function getTrackingPartnerTag(){
			if( !$this->isActived() ) return false;
			global $config;
			
			// récupère l'url de la page
			$t = rew_rewrite( rew_strip($_SERVER['PATH_INFO']) );
			if( !preg_match('/^\/shopbots\//', $_SERVER['PATH_INFO']) ){
				if(  $_SERVER['PATH_INFO'] == $t ){
					$t = rew_rewrite( $config['rew_strip'].$_SERVER['PATH_INFO'] );
				}
			}
			$url = dirname(__FILE__) . '/pages' . $t;
			
			$file = preg_replace('/\?.+/','',$url);
			$_file = str_replace( dirname(__FILE__) . '/pages', '', $file );
			if( strpos ( $_file, '?' ) ){
				$_file = substr( $_file , 0 , strpos ( $_file, '?' ) );
			}
			
			$script = '';
			switch( $_file ){
				case '/index.php' : // page d'accueil
					$script = '<script src="'.$config['netaffiliation_js_file'].'?zone=accueil"></script>';
					break;
				case '/catalog/index.php' :
				case '/catalog/category.php' :
					$cat = isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] ? $_GET['cat'] : '';
					$script = '<script src="'.$config['netaffiliation_js_file'].'?zone=categorie&amp;idcategorie='.$cat.'"></script>';
					break;
				case '/catalog/product.php' :
					$cat = isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] ? $_GET['cat'] : '';
					$prd = isset($_GET['prd']) && is_numeric($_GET['prd']) && $_GET['prd'] ? $_GET['prd'] : '';
					$script = '<script src="'.$config['netaffiliation_js_file'].'?zone=produit&amp;idproduit='.$prd.'&amp;idcategorie='.$cat.'"></script>';
					break;
				case '/cart/cart.php' :
				case '/cart/index.php' :
					$order = $this->getOrderID();
					if( !is_numeric($order) || $order<=0 ){
						break;
					}
					
					$total_ht = ord_orders_get_total_without_port( $order, false, false );
					$prds = array();
					$rp = ord_products_get( $order );
					if( $rp && ria_mysql_num_rows($rp) ){
						while( $p = ria_mysql_fetch_array($rp) ){
							if( in_array($p['ref'], $config['dlv_prd_references']) ){
								continue;
							}
							$prds[] = $p['id'];
						}
					}
					
					$prds = str_replace(' ', '', implode(',', $prds) );
					
					$script = '<script src="'.$config['netaffiliation_js_file'].'?zone=panier&amp;montant='.$total_ht.'&amp;listeids='.$prds.'"></script>';
					break;
				case '/cart/paiement-wait.php' :
				case '/cart/termine.php' :
				case '/cart/confirm.php' : 
					$order = $this->getOrderID();
					if( !is_numeric($order) || $order<=0 ){
						break;
					}
					
					$total_ht = ord_orders_get_total_without_port( $order, false, false );
					$prds = array();
					$rp = ord_products_get( $order );
					if( $rp && ria_mysql_num_rows($rp) ){
						while( $p = ria_mysql_fetch_array($rp) ){
							if( in_array($p['ref'], $config['dlv_prd_references']) ){
								continue;
							}
							$prds[] = $p['id'];
						}
					}
					
					$prds = str_replace(' ', '', implode(',', $prds) );
					
					$script = '<script src="'.$config['netaffiliation_js_file'].'?zone=fincommande&amp;montant='.$total_ht.'&amp;listeids='.$prds.'&amp;idtransaction='.$order.'"></script>';
					break;
			}
			
			return $script;
		}
		
		/** Cette fonction récupérer l'identifiant de commande en session.
		 *	@return bool False si aucune commande n'est en session, sinon l'identifiant de la commande
		 */
		private function getOrderID(){
			if( !isset($_SESSION['ord_id']) || !ord_orders_exists($_SESSION['ord_id']) ) return false;
			
			return $_SESSION['ord_id'];
		}
		
		/** Cette fonction permet de vérifier que toutes les variables de configuraiton sont en place pour utiliser NetAffiliation.
		 *	@return bool False si une des variables est inexistante, True si toutes les variables sont en place
		 */
		private function isActived(){
			global $config;
			$res = true;
			
			if( !isset($config['netaffiliation_is_active']) || !$config['netaffiliation_is_active'] ){
				$res = false;
			} elseif( !isset($config['netaffiliation_url']) || trim($config['netaffiliation_url'])=='' ){
				$res = false;
			} elseif( !isset($config['netaffiliation_mclic']) || trim($config['netaffiliation_mclic'])=='' ){
				$res = false;
			} elseif( !isset($config['netaffiliation_mcode']) || trim($config['netaffiliation_mcode'])=='' ){
				$res = false;
			}
			
			return $res;
		}
	}

/// @}
// \endcond

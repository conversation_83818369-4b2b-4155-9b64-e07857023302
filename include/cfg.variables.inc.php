<?php
// \cond onlyria

/**	\defgroup configuration Configuration
 *	Ce module comprend les fonctions nécessaires à la configuration de la plateforme
 */

/**	\defgroup cfg_variables Variables de configuration
 *	\ingroup configuration
 *	Ce module fournit les fonctions nécessaires à la gestion des variables de configuration.
 *
 *	@{
 */

define('ERR_CFG_IMAGE', -1); /// Erreur de configuration, la configuration des images n'a pas pu être initialisée correctement
define('ERR_CFG_PROFILE', -2); /// Erreur de configuration, la configuration des profils utilisateurs n'a pas pu être initialisée correctement (cette constante n'est utilisée nulle part dans l'application)
define('ERR_CFG_OWNER', -3); /// Erreur de configuration, la configuration sur le propriétaire de l'instance n'a pas pu être initialisée correctement
define('ERR_CFG_CONFIG_PRICE', -4); /// Erreur de configuration, la configuration sur les tarifs n'a pas pu être initialisée correctement
define('ERR_CFG_URLS', -5); /// Erreur de configuration, la configuration de la réécriture d'URL n'a pas pu être initialisée correctement
define('ERR_CFG_VARIABLE', -6); /// Erreur de configuration, la surcharge des variables de configuration n'a pas pu être initialisée correctement
define('ERR_CFG_CONFIG_URL', -7); /// Erreur de configuration, la configuration de la réécriture d'URL n'a pas pu être initialisée correctement
define('ERR_CFG_PRD_RELATION', -9); /// Erreur de configuration lors de la création du type de relation produit
define('ERR_CFG_EMAIL', -10); /// Erreur de configuration lors de la création des config emails

/// Erreur de configuration, le site web est manquant
define('ERR_CFG_NO_WST', -8);

require_once( 'env.inc.php');
require_once( 'db.inc.php' );
require_once( 'websites.inc.php' );
require_once( 'fields.inc.php' );
require_once( 'users.inc.php' );
require_once( 'gu.categories.inc.php' );

/** Cette fonction permet de récupérer les variables de configuration enregistrées dans la base de données
 *	@param string $code Optionnel, code (identifiant) d'une variable sur laquelle filtrer le résultat, ou tableau de codes (pour charger plusieurs variables simultanément)
 *	@param array $exclude Optionnel, tableau de code de variables à exclure du résultats
 *	@param string $pattern Optionnel, permet de récupérer les variables suivant une expression régulière
 *	@param bool $system Optionnel par défaut toutes les variables sont retournées, mettre True pour n'avoir que les variables système, False pour toutes les autres
 *	@return resource un résultat de requête MySQL contenant :
 *				- code : Code de la variable (identifiant)
 *				- name : Nom de la variable
 *				- desc : Description de la variable
 *				- default : Valeur par défaut de la variable
 *				- type : Identifiant du type de données (fld_types)
 *				- parent_code : Code de la variable parente
 *				- cls_id : classe d'objet liée à cette variable
 *				- obj_cls_id : classe d'objets liée à la classe liée à la variable (ex. Modèle lié aux produits)
 *	@return bool false si un paramètre fourni est erroné
 */
function cfg_variables_get( $code='', $exclude=array(), $pattern=false, $system=null ){
	if( !is_array($code) && trim($code)!=='' && !cfg_variables_exists($code) ) return false;
	if( !is_array($exclude) ) return false;

	$sql = '
		select var_code as code, var_name as name, var_desc as "desc", var_default as "default", var_type_id as type,
		var_parent_code as parent_code, var_cls_id as cls_id, var_obj_cls_id as obj_cls_id
		from cfg_variables
		where 1
	';

	if( is_array($code) && sizeof($code)>0 ){
		$sql .= ' and var_code in (\''.implode('\',\'', $code).'\')';
	}
	elseif( !is_array($code) && trim($code)!=='' ){
		$sql .= ' and var_code=\''.$code.'\'';
	}

	if( sizeof($exclude)>0 ){
		$sql .= ' and var_code not in (\''.implode('\',\'', $exclude).'\')';
	}

	if( $pattern !== false ){
		$sql .= ' and var_code REGEXP "'.addslashes($pattern).'" ';
	}

	if ($system !== null) {
		if ($system) {
			$sql .= ' and var_system = 1';
		}else{
			$sql .= ' and var_system = 0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer la description d'une variable de configuration
 *	@param string $code Obligatoire, code de la variable (son identifiant textuel)
 *	@return string la description de la variable
 *	@return bool false si le paramètre $code est omis ou faux
 */
function cfg_variables_get_desc( $code ){
	if( !cfg_variables_exists($code) ) return false;

	$res = ria_mysql_query('
		select var_desc as "desc"
		from cfg_variables
		where var_code=\''.$code.'\'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'desc' );
}

/** Cette fonction permet d'ajouter la configuration minimale pour un site
 *	@param int $tnt Obligatoire, identifiant du locataire
 *	@param int $wst Obligatoire, identifiant du site
 *	@param string $dirname Obligatoire, nom qui sera utilisé pour le dossier des sources et pour l'url du site
 *	@return bool true en cas de succès ou false si l'un des paramètres est faux
 *	@return -1 si une erreur est survenue lors de l'ajout des configurations d'images
 *	@return -2 si une erreur est survenue lors de l'ajout des profiles des utilisateurs
 *	@return -3 si une erreur est survenue lors de la sauvegarde des informations du propriétaire
 *	@return -4 si une erreur est survenue lors de l'ajout des conditions tarifaires
 *	@return -5 si une erreur est survenue lors de l'ajout des urls par défaut
 *	@return -6 si une erreur est survenue lors de la sauvegardes des variables de configurations
 */
function cfg_overrides_add_minimal( $tnt, $wst, $dirname ){
	if( !tnt_tenants_exists($tnt) ) return false;
	$config['tnt_id'] = $tnt;

	if( !wst_websites_exists($wst) ) return false;
	$config['wst_id'] = $wst;

	global $config;

	// Configurations des images
	$sql = '
		insert into cfg_images
			( img_tnt_id, img_code, img_width, img_height, img_background, img_clip, img_transparent, img_format, img_old_system )
		values
			( '.$tnt.', \'high\', 260, 260, \'#FFFFFF\', 0, 0, \'jpg\', 1 ),
			( '.$tnt.', \'medium\', 150, 150, \'#FFFFFF\', 0, 0, \'jpg\', 1 ),
			( '.$tnt.', \'small\', 80, 80, \'#FFFFFF\', 0, 0, \'jpg\', 1 ),
			( '.$tnt.', \'yuto\', 1000, 0, \'#FFFFFF\', 0, 0, \'jpg\', 0 )
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_IMAGE;
	}

	$sql = '
		insert into cfg_images_filters
			( cif_tnt_id, cif_img_code, cif_filter_code, cif_width, cif_height, cif_value, cif_pos )
		values
			( '.$tnt.', \'high\', \'fill\', 260, 260, \'\', 0 ),
			( '.$tnt.', \'medium\', \'fill\', 150, 150, \'\', 0 ),
			( '.$tnt.', \'small\', \'fill\', 80, 80, \'\', 0 ),
			( '.$tnt.', \'yuto\', \'fill\', 1000, 0, \'\', 0 )
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_IMAGE;
	}

	// création des configs de mails
	$sql = "
		insert into cfg_emails
			( email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos )
		values
			(".$tnt.", ".$wst.", 'ord-alert', 'Suivi des commandes', 'Un email de suivi de commande est envoyé à chaque étape du traitement de la commande client (enregistrement, préparation de livraison, livraison, etc...).', 1, 0, 0, 1, '<EMAIL>', '', '', '', 0),
			(".$tnt.", ".$wst.", 'ord-owner', 'Notifications de commande', 'Cet email vous est envoyé pour vous avertir d'une nouvelle commande dans la boutique.nIl contient des informations uniquement destinée à l'usage interne (ex: informations bancaires).', 1, 1, 1, 1, '<EMAIL>', '<EMAIL>', '', '', 0),
			(".$tnt.", ".$wst.", 'device-new', 'Nouvel appareil', 'Envoi une notification dès qu'un nouvel appareil associe l'application Yuto à votre installation.', 1, 1, 1, 1, '<EMAIL>', '<EMAIL>', '', '', 0)
	";

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_EMAIL;
	}

	// Information sur le propriétaire/la société
	$sql = '
		insert into site_owner
			( owner_tnt_id, owner_type, owner_name, owner_firstname, owner_lastname, owner_address1, owner_address2, owner_zipcode, owner_city,
			owner_phone, owner_fax, owner_email, owner_naf, owner_taxcode, owner_inscription, owner_capital, owner_publication, owner_redaction )
		values
			( '.$tnt.', 0, \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', \'\', NULL , \'\', \'\' )
	';

	if( !ria_mysql_query($sql) )
		return ERR_CFG_OWNER;

	// Conditions tarifaires
	$sql = '
		insert into prc_price_fields
			( ppf_tnt_id, ppf_fld_id, ppf_priority )
		values
			( '.$tnt.', 455, 1 ),
			( '.$tnt.', 456, 2 )
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_CONFIG_PRICE;
	}

	// Réécriture d'urls
	$sql = '
		insert into rew_rewritemap
			( url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public )
		values
			( '.$tnt.', '.$wst.', \'fr\', \'\', \'/index.php\', 200, 1 ),
			( '.$tnt.', '.$wst.', \'fr\', \'/\', \'/index.php\', 200, 1 ),
			( '.$tnt.', '.$wst.', \'fr\', \'/connexion\', \'/connexion/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/connexion/\', \'/login.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte\', \'/mon-compte/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/\', \'/users/index.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/commandes\', \'/mon-compte/commandes/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/commandes/\', \'/users/orders/index.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/commandes/commande\', \'/mon-compte/commandes/commande/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/commandes/commande/\', \'/users/orders/order.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/creer-compte\', \'/mon-compte/creer-compte/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/creer-compte/\', \'/create.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/disponibilite\', \'/mon-compte/disponibilite/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/disponibilite/\', \'/users/delivery-alerts.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/factures\', \'/mon-compte/factures/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/factures/\', \'/users/invoices/index.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/factures/facture\', \'/mon-compte/factures/facture/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/factures/facture/\', \'/users/invoices/invoice.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/informations\', \'/mon-compte/informations/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/informations/\', \'/users/informations.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/mot-de-passe\', \'/mon-compte/mot-de-passe/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/mot-de-passe/\', \'/users/password.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/mot-passe-perdu\', \'/mon-compte/mot-passe-perdu/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/mot-passe-perdu/\', \'/lost-password.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/nouveau-mot-de-passe\', \'/mon-compte/nouveau-mot-de-passe/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/nouveau-mot-de-passe/\', \'/new-password.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/options\', \'/mon-compte/options/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/mon-compte/options/\', \'/users/options.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/panier\', \'/panier/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/panier/\', \'/cart/cart.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/echec-paiement\', \'/commander/echec-paiement/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/echec-paiement/\', \'/cart/echec.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/livraison\', \'/commander/livraison/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/livraison/\', \'/cart/delivery.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/paiement\', \'/commander/paiement/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/paiement/\', \'/cart/paiement.php\', 200, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/terminer\', \'/commander/terminer/\', 301, 1),
			( '.$tnt.', '.$wst.', \'fr\', \'/commander/terminer/\', \'/cart/complete.php\', 200, 1)
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_URLS;
	}

	// Personnalisation des variables de configuration
	$rinfo_wst = wst_websites_get( $wst, null, $tnt );
	if( !$rinfo_wst || !ria_mysql_num_rows($rinfo_wst) ){
		return ERR_CFG_NO_WST;
	}

	$info_wst = ria_mysql_fetch_array( $rinfo_wst );


	$sql = '
		insert into cfg_overrides
			( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
		values
			( '.$tnt.', '.$wst.', 0, \'banners_dir\', \'/var/www/'.$dirname.'.maquettes.riastudio.fr/htdocs/images/banners\' ),
			( '.$tnt.', '.$wst.', 0, \'banners_url\', \'/images/banners\' ),
			( '.$tnt.', '.$wst.', 0, \'doc_dir\', \'/var/www/'.$dirname.'.maquettes.riastudio.fr/htdocs/documents\' ),
			( '.$tnt.', '.$wst.', 0, \'email_alerts_enabled\', \'0\' ),
			( '.$tnt.', '.$wst.', 0, \'faq_enabled\', \'0\' ),
			( '.$tnt.', '.$wst.', 0, \'img_dir\', \'/var/www/'.$dirname.'.maquettes.riastudio.fr/htdocs/images/products\' ),
			( '.$tnt.', '.$wst.', 0, \'img_url\', \'http://'.$dirname.'.maquettes.riastudio.fr/images/products\' ),
			( '.$tnt.', '.$wst.', 0, \'newsletter_enabled\', \'0\' ),
			( '.$tnt.', '.$wst.', 0, \'site_desc\', \''.addslashes($info_wst['desc']).'\' ),
			( '.$tnt.', '.$wst.', 0, \'site_dir\', \'/var/www/'.$dirname.'.maquettes.riastudio.fr/htdocs\' ),
			( '.$tnt.', '.$wst.', 0, \'site_name\', \''.addslashes($info_wst['name']).'\' ),
			( '.$tnt.', '.$wst.', 0, \'site_url\', \'http://'.$dirname.'.maquettes.riastudio.fr\' ),
			( '.$tnt.', '.$wst.', 0, \'fdv_can_only_edit_user\', \'1\' ),
			( '.$tnt.', '.$wst.', 0, \'fdv_ord_edit_payment\', \'0\' ),
			( '.$tnt.', '.$wst.', 0, \'fdv_ord_sign_required\', \'0\' )
	';

	$prc_id = prd_prices_categories_add( 'Par défaut', true, false );
	if (is_numeric($prc_id) && $prc_id > 0) {
		$sql .= ', ( '.$tnt.', '.$wst.', 0, \'default_prc_id\', '.$prc_id.' )';
	}

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_VARIABLE;
	}

	// Configurations pour la création des urls
	$sql = '
		insert into cfg_urls
			( url_tnt_id, url_wst_id, url_cls_id, url_key, url_name, url_used )
		values
			( '.$tnt.', '.$wst.', 1, \'\', \'/catalog/product.php\', 1),
			( '.$tnt.', '.$wst.', 3, \'\', \'/catalog/index.php\', 1),
			( '.$tnt.', '.$wst.', 6, \'\', \'/stores/index.php\', 1),
			( '.$tnt.', '.$wst.', 11, \'\', \'/cms/view.php\', 1),
			( '.$tnt.', '.$wst.', 13, \'\', \'/faq/category.php\', 1),
			( '.$tnt.', '.$wst.', 14, \'\', \'/news/news.php\', 1),
			( '.$tnt.', '.$wst.', 15, \'\', \'/news/index.php\', 1),
			( '.$tnt.', '.$wst.', 16, \'\', \'/faq/category.php\', 1)
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_CONFIG_URL;
	}

	// création d'une relation produit de base
	$sql = '
		insert into prd_relations_types
			( type_tnt_id, type_name, type_name_plural )
		values
			( '.$tnt.', \'Produit associé\', \'Produits associés\')
	';

	if( !ria_mysql_query($sql) ){
		return ERR_CFG_PRD_RELATION;
	}

	return true;
}

/** Cette fonction permet de substituer certaines variables de la configuration en cours d'utilisation par les variables d'une autres configuration.
 *	Cela permet de charger ponctuellement les variables propre à un autre site client.
 *	@param int $wst Obligatoire, identifiant d'un site web
 *	@param $exclude Facultatif, tableau de code de variable à exclure du résultats
 *	@param string|array $code Facultatif, code ou tableau de code d'une variable
 *	@param int $usr Facultatif, identifiant d'un utilisateur
 *	@return La variable globale $config (non modifiée) en cas d'échec
 *	@return array Un tableau identique à $config, mais avec les valeurs substituées selon les paramètres fournis
 */
function cfg_overrides_substitute( $wst, $exclude=array(), $code='', $usr=0 ){
	global $config;

	if( !is_numeric($wst) || $wst<=0 ){
		return $config;
	}

	if( !is_array($exclude) ){
		return $config;
	}

	if( $usr>0 && !gu_users_exists($usr) ){
		return $config;
	}

	if( !is_array($code) && trim($code)!='' && !cfg_variables_exists($code) ){
		return $config;
	}

	$current_config = $config;
	if( wst_websites_exists($wst) && $wst!=$config['wst_id'] ){
		// chargement de la configuration spécifique
		if( $rvar = cfg_overrides_get( $wst, $exclude, $code, $usr ) ){
			while( $var = ria_mysql_fetch_array($rvar) ){
				$current_config[ trim($var['code']) ] = cfg_variable_values_parse_type( $var['type'], $var['value'] );
			}
		}
	}

	return $current_config;
}

/** Cette fonction permet de récupérer les variables pour un locataire. Ne retourne pas la valeur par défaut.
 *	@param int $wst Facultatif, identifiant d'un site web
 *	@param array $exclude Facultatif, tableau de codes de variables à exclure du résultat
 *	@param string|array $code Facultatif, code ou tableau de code d'une variable
 *	@param int $usr Facultatif, identifiant d'un utilisateur
 *	@param $tnt_id Optionnel, identifiant d'un tenant (par défaut : on utilise le $config['tnt_id'])
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- code : Code de la variable
 *		- value : Valeur de la variable
 *		- type : Identifiant du type de données (fld_types)
 */
function cfg_overrides_get( $wst=0, $exclude=array(), $code='', $usr=0, $tnt_id=0 ){
	if( $wst!==false ){
		if( !is_numeric($wst) || $wst<0 ){
			return false;
		}
	}

	if( !is_array($exclude) ) return false;
	if( $usr && !is_numeric($usr) ) return false;
	if( !is_array($code) && trim($code)!='' && !cfg_variables_exists($code) ) return false;

	if (!is_numeric($tnt_id) || $tnt_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select ovr_var_code as code, ovr_value as value, var_type_id as type, ovr_usr_id as usr_id, ovr_wst_id as wst_id
		from cfg_overrides, cfg_variables
		where ovr_tnt_id = '.($tnt_id ? $tnt_id : $config['tnt_id']).'
			and ovr_var_code = var_code
	';

	if( $wst!==false ){
		if( $wst>0 ){
			$sql .= ' and ovr_wst_id='.$wst;
		}else{
			$sql .= ' and ovr_wst_id = 0';
		}
	}

	if( sizeof($exclude)>0 )
		$sql .= ' and ovr_var_code not in (\''.implode('\',\'', $exclude).'\')';

	if( is_array($code) && sizeof($code)>0 )
		$sql .= ' and ovr_var_code in (\''.implode('\',\'', $code).'\')';
	elseif( !is_array($code) && trim($code)!='' )
		$sql .= ' and ovr_var_code = \''.$code.'\'';

	if ( $usr!==false && $usr >= 0 ) {
		$sql .= ' and ovr_usr_id = '.$usr;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer la valeur d'une variable de configuration propre à un tenant. Ne retourne pas la valeur par défaut.
 *	@param string $code Obligatoire, code d'une variable
 *	@param int $website Optionnel, identifiant d'un site web
 *	@param int $usr_id Optionnel, identifiant d'un compte utilisateur
 *	@param int $tnt_id Optionnel, identifiant d'un tenant (par défaut : on utilise le $config['tnt_id'])
 *	@return mixed La valeur de la variable si elle existe et est définie, false dans le cas contraire
 */
function cfg_overrides_get_value( $code, $website=null, $usr_id=0, $tnt_id=0 ){
	if( !cfg_variables_exists($code) ){
		return false;
	}

	if( $website>0 && !wst_websites_exists($website) ){
		return false;
	}

	if( $usr_id>0 && !gu_users_exists($usr_id) ){
		return false;
	}

	if (!is_numeric($tnt_id) || $tnt_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		select ovr_value
		from cfg_overrides
		where ovr_tnt_id = '.($tnt_id ? $tnt_id : $config['tnt_id']).'
			and ovr_var_code = "'.addslashes( $code ).'"
	';

	if( $website!=null ){
		$sql .= ' and ovr_wst_id='.$website;
	}
	if( $usr_id >= 0 ){
		$sql .= ' and ovr_usr_id='.$usr_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'ovr_value' );
}

/** Cette fonction permet de tester l'existance d'une valeur pour une variable
 *	@param string $code Obligatoire, code d'une variable
 *	@param int $tenant Optionnel, identifiant d'un locataire
 *	@param int $website Optionnel, identifiant d'un site web
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur
 *	@return bool Retourne true si elle existe
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_overrides_exists( $code, $tenant=0 , $website=null, $usr_id=-1 ){
	if( !cfg_variables_exists($code) ) return false;
	if( $tenant>0 && !tnt_tenants_exists($tenant) ) return false;
	if( $website>0 && !wst_websites_exists($website) ) return false;
	if( $usr_id >0 && !gu_users_exists($usr_id) ) return false;

	global $config;

	$sql = '
		select 1
		from cfg_overrides
		where ovr_var_code=\''.$code.'\'
	';

	if( $tenant>0 )
		$sql .= ' and ovr_tnt_id='.$tenant;

	if( $website!==null )
		$sql .= ' and ovr_wst_id='.$website;

	if( $usr_id >= 0 )
		$sql .= ' and ovr_usr_id='.$usr_id;
	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}

/**	Cette fonction permet de supprimer une variable configurée pour un locataire, un site donné et un client
 *	@param string $code Obligatoire, code de la variable
 *	@param int $website Optionnel, identifiant de site
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function cfg_overrides_del_value( $code, $website=0, $usr_id=0){
	if( !cfg_variables_exists($code) ) return false;
	if( !is_numeric($website) || $website<0 ) return false;
	if( $usr_id >0 && !gu_users_exists($usr_id) ) return false;
	global $config;

	$sql = '
		delete from cfg_overrides
		where
			ovr_tnt_id='.$config['tnt_id'].' and
			ovr_var_code="'.addslashes($code).'" and
			ovr_wst_id='.$website.'
	';

	if($usr_id >=0){
		$sql .= ' and ovr_usr_id='.$usr_id;
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	// retire le cache
	cfg_variable_delete_cache();

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_CFG);

	return cfg_variable_delete_cache();
}

/** Cette fonction permet de modifier une valeur pour une variable de configuration
 *	@param string $code Obligatoire, code d'une variable
 *	@param mixed $value Obligatoire, valeur affectée à cette variable
 *	@param int $website Optionnel, identifiant d'un site sur lequel affecter la valeur
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur
 *	@param int $tnt_id Optionnel, identifiant d'un tenant (par défaut : on utilise le $config['tnt_id'])
 *	@return bool Retourne true si l'affectation s'est bien déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_overrides_set_value( $code, $value, $website=0, $usr_id=0, $tnt_id=0 ){
	if( !cfg_variables_exists($code) ) return false;
	if(!is_numeric($usr_id) || ( $usr_id>0 && !gu_users_exists($usr_id) ) )return false;

	if (!is_numeric($tnt_id) || $tnt_id < 0) {
		return false;
	}

	global $config;

	if ($tnt_id <= 0) {
		$tnt_id = $config['tnt_id'];
	}

	$website = !is_numeric($website) || $website=='' ? 0 : $website;

	if( cfg_overrides_exists($code, $tnt_id, $website, $usr_id) ){

		$sql = '
			update cfg_overrides
			set ovr_value = \''.addslashes($value).'\'
			where ovr_tnt_id='.$tnt_id.'
				and ovr_var_code=\''.addslashes($code).'\'
				and ovr_usr_id='.$usr_id.'
				and ovr_wst_id='.$website.'
		';

	} else {

		$sql = '
			insert into cfg_overrides
				( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
			values
				( '.$tnt_id.', '.$website.', '.$usr_id.',\''.addslashes($code).'\', \''.addslashes($value).'\' )
		';
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$affected = ria_mysql_affected_rows();

	// retire le cache
	cfg_variable_delete_cache();

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_CFG);

	// dans le cas de certaines variables il faut actualiser les dates de modifications
	if( $affected ){
		switch($code){
			case 'fdv_sync_user_rule':
				// on doit actualisé tous les clients
				$rusr = ria_mysql_query('
					select usr_id from gu_users
					where usr_tnt_id = '.( is_numeric($tnt_id) && $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).'
						and usr_date_deleted is null
				');

				if( $rusr ){
					$cpt = 0;
					$usr_ids = array();
					while( $usr = ria_mysql_fetch_array($rusr) ){
						if( $cpt > 400 ){
							gu_users_set_date_modified($usr_ids, false, $tnt_id);
							$cpt = 0;
							$usr_ids = array();
						}
						$cpt++;
						$usr_ids[] = $usr['usr_id'];
					}

					if( sizeof($usr_ids) ){
						gu_users_set_date_modified($usr_ids, false, $tnt_id);
					}
				}

				// on doit actualisé toutes les commandes
				$rord = ria_mysql_query('
					select ord_id from ord_orders
					where ord_tnt_id = '.( is_numeric($tnt_id) && $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).'
						and ord_masked = 0
						and ord_date_archived is null
				');

				if( $rord ){
					$cpt = 0;
					$ord_ids = array();
					while( $ord = ria_mysql_fetch_array($rord) ){
						if( $cpt > 400 ){
							ord_orders_set_date_modified($ord_ids, $tnt_id);
							$cpt = 0;
							$ord_ids = array();
						}
						$cpt++;
						$ord_ids[] = $ord['ord_id'];
					}
					if( sizeof($ord_ids) ){
						ord_orders_set_date_modified($ord_ids, $tnt_id);
					}
				}
				break;
		}
	}

	return true;
}

/** Cette fonction permet de vérifier l'existance d'un code de variable.
 * 	Pour des questions de performances, le résultat de cette fonction est mis en cache le temps de l'exécution de la page.
 *	@param string $code Obligatoire, code de la variable
 *	@return bool Retourne true si la variable existe bien
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_variables_exists( $code ){
	static $prev_code = '';
	static $prev_exists = false;

	if( $code==$prev_code ){
		return $prev_exists;
	}

	if( trim($code)=='' ) return false;

	$prev_code = $code;
	$prev_exists = ria_mysql_num_rows( ria_mysql_query('select 1 from cfg_variables where var_code=\''.$code.'\'') )>0;

	return $prev_exists;
}

/** Cette fonction permet de mettre en forme la valeur d'une variable. Par exemple si la variable est de type tableau, alors la valeur retournée sera un tableau
 *	@param int $type Obligatoire, Identifiant du type de variable
 *	@param mixed $value Obligatoire, Valeur de la variable
 *	@return bool Retourne false si une erreur est survenue
 *	@return mixed Retourne la valeur formatée selon le type de la variable
 */
function cfg_variable_values_parse_type( $type, $value ){
	if (!is_numeric($type) || $type<=0) {
		return false;
	}

	switch( $type ){
		case FLD_TYPE_BOOLEAN_YES_NO :
			return $value=='0' ? false : true;
			break;
		case FLD_TYPE_SELECT_MULTIPLE :
			$value = explode( ',', str_replace(', ', ',', $value) );
			return sizeof($value)>0 && $value[0]!=='' ? $value : array();
			break;
		default :
			return trim($value);

	}
}

/** Cette fonction permet d'ajouter un paramètre propre à un utilisateur.
 *	Si ce paramètre existe déjâ , une mise à jour est effectuée.
 *	@param string $code Obligatoire, code d'une variable
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param mixed $value Obligatoire, value affecté à cette variable pour l'utilisateur
 *	@return bool Retourne true si l'ajout s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_variable_users_add( $code, $usr, $value ){
	if( !cfg_variables_exists( $code ) ){
		return false;
	}
	if( !gu_users_exists( $usr ) ){
		return false;
	}

	if( cfg_variable_users_exists($code, $usr) )
		return cfg_variable_users_update($code, $usr, $value);

	if( trim($value)=='' ){
		return false;
	}
	global $config;

	$sql = '
		insert into cfg_overrides
			( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
		values
			( '.$config['tnt_id'].', 0, '.$usr.', "'.addslashes($code).'", "'.addslashes($value).'" )
	';

	$r = ria_mysql_query($sql);
	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_CFG, $usr);

	return $r;
}

/** Cette fonction permet de mettre à jour une valeur d'une variable pour un utilisateur données
 *	@param string $code Obligatoire, code d'une variable
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param mixed $value Obligatoire, value pour cette variable
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_variable_users_update( $code, $usr, $value ){
	if( !cfg_variable_users_exists( $code, $usr ) ){
		return false;
	}
	if( trim($value)=='' ){
		return false;
	}
	global $config;

	$sql = '
		update cfg_overrides
		set ovr_value="'.addslashes($value).'"
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code="'.addslashes($code).'"
			and ovr_usr_id='.$usr.'
	';

	$r = ria_mysql_query($sql);
	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_CFG, $usr);

	return $r;
}

/**	Cette fonction supprime une variable de configuration spécifique à un utilisateur
 *	@param string $code Code de la variable de configuration
 *	@param int $usr Identifiant de l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function cfg_variable_users_del( $code, $usr ){
	if( !cfg_variable_users_exists( $code, $usr ) ){
		return false;
	}
	global $config;

	$sql = '
		delete from cfg_overrides
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code="'.addslashes($code).'"
			and ovr_usr_id='.$usr.'
	';

	$r = ria_mysql_query($sql);
	if( $r ){
		gu_users_set_date_modified( $usr );
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_CFG, $usr);

	return $r;
}

/**	Cette fonction permet le chargement d'une variable de configuration spécifique à un utilisateur
 *	@param string $code Code de la variable de configuration
 *	@param int $usr Identifiant de l'utilisateur
 *	@return string La valeur de configuration de cette variable pour l'utilisateur demandé, ou chaîne vide si aucune
 */
function cfg_variable_users_get( $code, $usr ){
	if( !cfg_variable_users_exists( $code, $usr ) ){
		return false;
	}
	global $config;

	$sql = '
		select ovr_value
		from cfg_overrides
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code="'.addslashes($code).'"
			and ovr_usr_id='.$usr.'
	';

	$r = ria_mysql_query($sql);
	if( $r ){
		return ria_mysql_result( $r, 0, 0 );
	}

	return '';
}

/** Cette fonction permet de tester l'existance d'une valeur pour une variable à un utilisateur
 *	@param string $code Obligatoire, code d'une variable
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@return bool Retourne true si un lien entre un utilisateur et une variable existe
 *	@return bool Retourne false dans le cas contraire
 */
function cfg_variable_users_exists( $code, $usr ){
	if( !cfg_variables_exists($code) ) return false;
	if( !gu_users_exists($usr) ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from cfg_overrides where ovr_tnt_id='.$config['tnt_id'].' and ovr_var_code=\''.$code.'\' and ovr_usr_id='.$usr) )>0;
}

/** Cette fonction permet de charger les variables de configuration contenues dans la base de données
 *	Les variables auront une valeur dans l'ordre de priorité suivant :
 *				- 1er : la valeur pour le site lui-même (ovr_wst_id pas à 0)
 *				- 2eme : la valeur pour le locataire (ovr_wst_id à 0)
 *				- 3eme : la valeur par défaut
 *	@param array $config Tableau de configuration global
 *	@param array $codes Facultatif, liste de codes à extraire uniquement
 *	@return bool|void false en cas d'erreur, rien en cas de succès
 */
function cfg_variables_load( &$config, $codes=null ){
	global $config, $memcached;

	if( $codes!==null && ( !is_array($codes) || !sizeof($codes) ) ){
		return false;
	}

	if( $codes===null ){ // Utilise le cache dans la mesure du possible, pour améliorer le temps de chargement
		if (isset($config['env_sandbox']) && !$config['env_sandbox'] && ($cfg_cache = $memcached->get('config:' . $config['tnt_id'] . ':' . $config['wst_id']))) {
			$config = array_merge($config, $cfg_cache);
			return;
		}
	}

	// Tableau qui contiendra les variables déjà chargées
	$exclude = array();

	if( !isset($config['wst_id']) || !is_numeric($config['wst_id']) || $config['wst_id']<=0 ){
		return false;
	}

	// On charge les variables propres au site (ovr_wst_id=$config['wst_id'])
	$var_wst = cfg_overrides_get($config['wst_id']);
	while( $var = ria_mysql_fetch_array($var_wst) ){
		$codetrim = trim($var['code']);
		if( $codes===null || in_array( $codetrim, $codes ) ){
			$exclude[] = $codetrim;
			$val = cfg_variable_values_parse_type( $var['type'], $var['value'] );
			$config[$codetrim] = $val;
		}
	}

	// On charge ensuite les variables propre au locataire (ovr_wst_id=0)
	// On ne recharge pas les variables déjà chargées
	$var_tenant = cfg_overrides_get(0, $exclude);
	while( $var = ria_mysql_fetch_array($var_tenant) ){
		$codetrim = trim($var['code']);
		if( $codes===null || in_array( $codetrim, $codes ) ){
			$exclude[] = $codetrim;
			$val = cfg_variable_values_parse_type( $var['type'], $var['value'] );
			$config[$codetrim] = $val;
		}
	}

	// On charge les variables qui ne le sont pas encore en prenant la valeur par défaut
	$var_global = cfg_variables_get('', $exclude);
	while( $var = ria_mysql_fetch_array($var_global) ){
		$codetrim = trim($var['code']);
		if( $codes===null || in_array( $codetrim, $codes ) ){
			$exclude[] = $codetrim;
			$val = cfg_variable_values_parse_type( $var['type'], $var['default'] );
			$config[$codetrim] = $val;
		}
	}

	// Charge le référencement par défaut
	$rwst = wst_websites_get( $config['wst_id'] );
	if( $rwst && ria_mysql_num_rows($rwst) ){
		$wst = ria_mysql_fetch_array( $rwst );
		$config['meta_keywords'] = $wst['meta_kwd'];
	}

	// Charge la configuration multilingue
	$config['i18n_lng'] = '';
	$config['i18n_lng_used'] = array();

	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
		$rlng = wst_websites_languages_get(); // Charges toutes les langues de tous les sites
	}else{
		$rlng = wst_websites_languages_get( $config['wst_id'] );
	}
	if( $rlng && ria_mysql_num_rows($rlng) ){

		while( $lng = ria_mysql_fetch_array($rlng) ){
			if( $lng['is_main'] ){
				$config['i18n_lng'] = strtolower( $lng['lng_code'] );
			}

			$config['i18n_lng_used'][] = strtolower( $lng['lng_code'] );
		}

	} else {

		$config['i18n_lng'] = 'fr';
		$config['i18n_lng_used'] = array( 'fr' );

	}
	//Supprime les doublons présents dans le tableau
	$config['i18n_lng_used'] = array_unique($config['i18n_lng_used']);

	$rwst = wst_websites_get( $config['wst_id'] );
	if( $rwst && ria_mysql_num_rows($rwst) ){
		$wst = ria_mysql_fetch_array( $rwst );
		$config['site_desc'] = $wst['desc'];
	}


	if( $codes===null || in_array( 'dlv_prd_references', $codes ) ){
		$sql = '
			select prd_ref as ref
			from prd_products
			join fld_object_values on ( prd_tnt_id=pv_tnt_id and prd_id=pv_obj_id_0 )
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_date_deleted is null
				and pv_fld_id='._FLD_IS_PORT.'
				and lower(pv_value) in (\'oui\', \'1\')
				and pv_lng_code=\''.$config['i18n_lng'].'\'
		';

		// Charge les référence des produits frais de ports
		if( $rport = ria_mysql_query( $sql ) ){
			if( ria_mysql_num_rows( $rport ) ){
				$config['dlv_prd_references'] = array();
				while( $port = ria_mysql_fetch_array($rport) ){
					$config['dlv_prd_references'][] = $port['ref'];
				}
			}
		}
	}

	if( $codes===null ){ // Enregistre les variables de configuration dans le cache pour une heure
		$memcached->set( 'config:'.$config['tnt_id'].':'.$config['wst_id'], $config, 3600 );
	}

	$config['tnt_yuto_essentiel'] = false; // deprecated La formule essentiel n'existe plus

	{ // Optimisations
		// Chargement d'un tableau des identifiants de produits cartes cadeaux
		$config['gifts_products_ids'] = array();
		if (isset($config['gifts_products']) && is_array($config['gifts_products']) && count($config['gifts_products'])) {
			$r_prd = ria_mysql_query('
				select prd_id
				from prd_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_date_deleted is null
					and prd_ref in ("'.implode('", "', $config['gifts_products']).'")
			');

			if ($r_prd) {
				while ($prd = ria_mysql_fetch_assoc($r_prd)) {
					$config['gifts_products_ids'][] = $prd['prd_id'];
				}
			}
		}
	}

	// Profil(s) ayant accès à RiaShop
	$config['admin_access_profiles'] = [ PRF_ADMIN ];
	if( ria_array_get($config, 'seller_admin_access', false) ){
		$config['admin_access_profiles'][] = PRF_SELLER;
	}
}

/** Cette fonction permet de récupérer les configurations des différentes taille d'image
 *	@param string $code Optionnel, code de la taille
 *	@return array Retourne un tableau MySQL contenant :
 *				- code : code d'image
 *				- height : hauteur d'image
 *				- width : largeur d'image
 *				- background : couleur de fond
 *				- clip : utilise ou non le tracé
 *				- format : format de sortie
 *				- transparent : si oui ou non les images doivent être transparente
 *				- border : si une bordure est appliquée automatiquement
 *				- border-color : couleur de bordure
 *				- corners : applique des coin arrondi
 *				- old_system : ancien système de miniature (dossier selon les tailles, les nouveaux portent le nom du code image)
 *				- name : nom donné à la configuration
 *
 *	@return bool Retourne false si la requête echoue
 */
function cfg_images_get( $code='' ){
	if( trim($code)!=='' && !cfg_images_code_exists($code) ) return false;
	global $config;

	$sql = '
		select img_code as code, img_height as height, img_width as width, img_background as background, img_clip as clip, img_format as format, img_transparent as transparent,
		img_border as border, img_border_color as "border-color", img_corners as corners, img_old_system as old_system, img_name as name
		from cfg_images
		where img_tnt_id='.$config['tnt_id'].'
	';

	if( $code!=='' )
		$sql .= ' and img_code=\''.$code.'\'';

	$sql .= '
		union
		select  img_code as code, img_height as height, img_width as width, img_background as background, img_clip as clip, img_format as format, img_transparent as transparent,
		img_border as border, img_border_color as "border-color", img_corners as corners, img_old_system as old_system, img_name as name
		from cfg_images as img_all
		where img_tnt_id=0 and not exists (
			select img_code as code, img_height as height, img_width as width, img_background as background, img_clip as clip, img_format as format
			from cfg_images as img_tnt
			where img_tnt_id='.$config['tnt_id'].' '.( trim($code)!=='' ? 'and img_code=\''.$code.'\'' : '' ).'
			and img_tnt.img_code = img_all.img_code
		)
	';

	if( $code!=='' )
		$sql .= ' and img_code=\''.$code.'\'';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de vérifier si un code d'image existe bien
 *	@param string $code Obligatoire, code d'image
 *	@return bool Retourne true si le code est bon
 *	@return bool Retourne false dans la cas contraire
 */
function cfg_images_code_exists( $code ){
	if( trim($code)=='' ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from cfg_images where (img_tnt_id=0 or img_tnt_id='.$config['tnt_id'].') and img_code=\''.$code.'\'') )>0;
}

/** Cette fonction permet de charger la variable de configuration contenant toutes les tailles d'image
 *	@param array $config Tableau de configuration global
 */
function cfg_images_load( &$config ){
	global $config;

	$rsize = cfg_images_get();

	$config[ 'img_sizes' ] = array();

	if( !$rsize )
		return false;

	while( $size = ria_mysql_fetch_array($rsize) ){
		$config[ 'img_sizes' ][$size['code']] = array(
			'width' => $size['width'],
			'height' => $size['height'],
			'background' => $size['background'],
			'format' => $size['format'],
			'clip' => $size['clip'] ,
			'transparent' => $size['transparent'],
			'border' => $size['border'],
			'border-color' => $size['border-color'],
			'corners' => $size['corners'],
			'old_system' => $size['old_system'],
			'dir' => ($size['old_system'] ? $size['width'].'x'.$size['height'] : $size['code']),
			'name' => $size['name']
		);
	}

}

/** Cette fonction permet de récupérer toutes les urls
 *	@param int $wst Optionnel, identifiant du site web
 *	@param string $key Optionnel, clé utilisé pour l'url, par défaut à null
 *	@param int $cls Optionnel, identifiant de classe
 *	@param string $url Optionnel, url utilisée
 *	@return bool Retourne false si la requête de récupération échoue ou bien si un paramètre est faux
 *	@return array Retourne un tableau MySQL dans le cas contraire :
 *			- wst_id : identifiant du site
 *			- key : clé utilisée
 *			- url : url utilisée
 *			- cls_id : catégorie de rattachement
 *			- used : url utilisée
 */
function cfg_urls_get( $wst=0, $cls=0, $key=null, $url='' ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	if( $cls>0 && !fld_classes_exists($cls) ) return false;
	global $config;

	$sql = '
		select url_wst_id as  wst_id, url_key as "key", url_name as url, url_cls_id as cls_id, url_used as used
		from cfg_urls
		where url_tnt_id='.$config['tnt_id'].'
	';

	if( $wst>0 ){
		$sql .= ' and url_wst_id='.$wst;
	}
	if( $key!=null ){
		$sql .= ' and url_key=\''.$key.'\'';
	}
	if( $cls>0 ){
		$sql .= ' and url_cls_id='.$cls;
	}
	if( trim($url)!='' ){
		$sql .= ' and url_name=\''.$url.'\'';
	}
	return ria_mysql_query( $sql );
}

/** Cette fonction renvoie si une class d'url doit générer des urls
 *	@param int $wst Obligatoire, identifiant du site web
 *	@param int $cls Obligatoire, identifiant de classe
 *	@return bool Retourne true si les urls doivent être générées, false sinon
 */
function cfg_urls_is_used( $wst, $cls ){
	if( !wst_websites_exists($wst) ) return false;
	if( !fld_classes_exists($cls) ) return false;
	global $config;

	$sql = '
		select url_used as used
		from cfg_urls
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and url_cls_id='.$cls.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return ria_mysql_result( $res, 0, 'used' );
}

/** Cette fonction permet de charger la variable de configuration contenant toutes les urls pour les produits
 *	@param array $config Tableau de configuration global
 */
function cfg_products_load( &$config ){

	$urls = cfg_urls_get( $config['wst_id'], 1 );

	$config['prd_pages'] = array();
	if( $urls===false && !ria_mysql_num_rows($urls) ){
		$config['prd_pages'][''] = '/product.php';
	} else {

		while( $url = ria_mysql_fetch_array($urls) )
			$config['prd_pages'][ $url['key'] ] = $url['url'];

	}
}

/** Cette fonction permet de charger la configuration spécifique à un utilisateur
 *	@param int $usr_id Identifiant de l'utilisateur
 *	@param array $config Tableau de configuration global
 *	@return bool True en cas de succès, False en cas d'échec
 */
function cfg_variables_users_load( $usr_id, &$config ){
	if( !gu_users_exists( $usr_id ) ){
		return false;
	}

	$rvar = cfg_overrides_get( 0, array(), '', $usr_id );

	if( $rvar ){
		while( $var = ria_mysql_fetch_array($rvar) ){
			$val = cfg_variable_values_parse_type( $var['type'], $var['value'] );
			$config[ $var['code'] ] = $val;
		}
	}else{
		return false;
	}

	return true;
}

/** Cette fonction permet de remettre les valeurs par défaut au variable de configuration des listes de produits
 *	@param int $wst Optionnel, identifiant d'un site web (peut être égal à zéro = tous les sites du locataire)
 */
function cfg_products_config_reset_defaults( $wst=0 ){
	if( $wst<0 || ($wst>0 && !wst_websites_exists($wst)) ) return false;
	global $config;

	$delete = ria_mysql_query('
		delete from cfg_overrides
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code in (\'prd_list_length\', \'prd_search_length\', \'prd_new_days\', \'cat_sort_type\', \'cat_sort_dir\')
			'.( $wst>0 ? ' and ovr_wst_id='.$wst : '' ).'
	');

	if( !$delete )
		return false;

	$r1 = ria_mysql_query('insert into cfg_overrides (ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value) values('.$config['tnt_id'].', '.$wst.', 0, \'prd_list_length\', \'20\')');
	$r2 = ria_mysql_query('insert into cfg_overrides (ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value) values('.$config['tnt_id'].', '.$wst.', 0, \'prd_search_length\', \'0\')');
	$r3 = ria_mysql_query('insert into cfg_overrides (ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value) values('.$config['tnt_id'].', '.$wst.', 0, \'prd_new_days\', \'60\')');

	return $r1 && $r2 && $r3;
}


/** Cette fonction permet de remettre les valeurs par défaut au variable de configuration des valeurs de stock
 *	@param int $wst Optionnel, identifiant d'un site web (peut être égal à zéro = tous les sites du locataire)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cfg_stock_value_config_reset_defaults( $wst=0 ){
	if( $wst<0 || ($wst>0 && !wst_websites_exists($wst)) ) return false;
	global $config;

	$delete = ria_mysql_query('
		delete from cfg_overrides
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code in (\'stock_change_signe\', \'stock_change_value\')
			'.( $wst>0 ? ' and ovr_wst_id='.$wst : '' ).'
	');

	if( !$delete )
		return false;

	return true;
}

/** Cette fonction permet de supprimer le cache des variables de configuration.
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function cfg_variable_delete_cache(){
	global $config, $memcached;

	if( $rwst = wst_websites_get( 0, false, $config['tnt_id'] ) ){
		while( $w = ria_mysql_fetch_array($rwst) ){
			@$memcached->delete( 'config:'.$config['tnt_id'].':'.$w['id'] );
		}
	}

	return true;
}

/** Cette fonction retourne un tableau de toutes les configurations par client.
 *	@param int $tenant Optionnel, identifiant d'un tenant
 *	@return array Un tableau contenant toutes les configurations par client, False en cas d'erreur.
 */
function cfg_variables_get_all_tenants( $tenant=0 ){
	global $config;

	$tenant = control_array_integer( $tenant, false );
	if( $tenant === false ){
		return false;
	}

	// Créer un tableau de toutes les variables de chaque locataire
	$rtnt = tnt_tenants_get( $tenant );
	if( $rtnt==false ){
		return false;
	}

	// Charge l'ensemble des configurations clients dont le script a besoin pour fonctionner
	$configs = array();
	while( $tnt = ria_mysql_fetch_array($rtnt) ){
		$config = array();

		$config['tnt_id'] = $tnt['id'];
		$config['wst_id'] = $tnt['wst_default'];
		$config['date-created'] = $tnt['date-created'];

		// Importe les variables de config
		cfg_variables_load($config);
		cfg_images_load($config);

		$configs[ $tnt['id'] ] = $config;
	}

	return $configs;
}

/** Cette fonction retourne un tableau de toutes les configurations par site.
 *	@param int $tenant Optionnel, identifiant d'un tenant
 *	@return array|bool Un tableau contenant toutes les configurations par site, False en cas d'erreur.
 */
function cfg_variables_get_all_websites( $tenant=null ){
	global $config;

	if( $tenant !== null ){
		$tenant = control_array_integer( $tenant, false );

		if( $tenant === false ){
			return false;
		}
	}

	// Créer un tableau de toutes les variables de chaque site
	$rwst = wst_websites_get( 0, false, $tenant );
	if( $rwst==false ){
		return false;
	}

	// Charge l'ensemble des configurations clients dont le script a besoin pour fonctionner
	$configs = array();
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$config = array();

		$config['tnt_id'] = $wst['tnt_id'];
		$config['wst_id'] = $wst['id'];
		$config['date-created'] = tnt_tenants_get_date_created( $wst['tnt_id'] );

		// Importe les variables de config
		cfg_variables_load($config);
		cfg_images_load($config);

		$configs[ $wst['id'] ] = $config;
	}

	return $configs;
}

/** Cette fonction permet de remettre a 0 les configuration pour les relances clients
 * @return bool true si succès, false si échec
 */
function cfg_erm_objects_reset_default(){
	global $config;

	$delete = ria_mysql_query('
		delete from cfg_overrides
		where ovr_tnt_id='.$config['tnt_id'].'
			and ovr_var_code in (\'erm_objects_delay\', \'erm_objects_delay_min\', \'erm_objects_max\', \'erm_objects_min_ord\', \'erm_objects_is_uniq\')
	');

	if( !$delete ){
		return false;
	}

	return true;
}

/** Cette fonction permet de charger les délais de conservation des données.
 * 	@param bool $with_surcharge Par défaut à false, mettre true pour charger les délais personnalisés par l'instance
 * 	@return array Un tableau contenant les différents délais de conservation
 */
function cfg_variables_get_clear_database_days( $with_surcharge=false ){
	global $config;

	$cfg_days = [
		'orders' => [
			'name' => 'Commande',
			'desc' => 'L\'historique de commande est conservé indéfiniment par défaut.',
			'days' => 9999,
			'max' => 9999,
			'group_name' => 'Commande',
			'group_position' => 0,
			'position' => 0,
		],
		'carts' => [
			'name' => 'Panier',
			'desc' => 'Les paniers enregistrés ne sont pas concernés.',
			'days' => 30,
			'max' => 30,
			'group_name' => 'Panier',
			'group_position' => 2,
			'position' => 0
		],

		'trading_rules' => [
			'name' => 'Règle de négociations supprimée',
			'days' => 30,
			'max' => 30,
			'group_name' => 'Règles de négociations',
			'group_position' => 3,
			'position' => 0
		],

		'users' => [
			'name' => 'Compte supprimé',
			'days' => 60,
			'max' => 60,
			'group_name' => 'Compte',
			'group_position' => 1,
			'position' => 0
		],

		'histo_login' => [
			'name' => 'Historique de connexion',
			'days' => 180,
			'max' => 180,
			'group_name' => 'Compte',
			'group_position' => 1,
			'position' => 1
		],

		'wishlist' => [
			'name' => 'Listes de favoris supprimées',
			'days' => 15,
			'max' => 15,
			'group_name' => 'Listes de favoris',
			'group_position' => 4,
			'position' => 0
		],

		'livr_alert' => [
			'name' => 'Alerte de disponibilité',
			'days' => 180,
			'max' => 180,
			'group_name' => 'Alerte de disponibilité',
			'group_position' => 5,
			'position' => 0
		],

		'sell_unit' => [
			'name' => 'Unités de vente personnalisées supprimées',
			'days' => 30,
			'max' => 30,
			'group_name' => 'Unités de vente personnalisées',
			'group_position' => 6,
			'position' => 0
		],

		'prices' => [
			'name' => 'Tarifs supprimées',
			'days' => 60,
			'max' => 60,
			'group_name' => 'Tarifs',
			'group_position' => 7,
			'position' => 0
		],

		'imports' => [
			'name' => 'Imports supprimés ou anciens',
			'desc' => 'Un import est considéré comme ancien lors que celui-ci n\'a pas été exécuté depuis le nombre de jours défini.',
			'days' => 60,
			'max' => 60,
			'group_name' => 'Imports',
			'group_position' => 8,
			'position' => 0
		],
	];

	if( $with_surcharge ){
		// TODO : récupérer la valeur en base
		$days_perso = cfg_overrides_get_value( 'admin_clear_days_perso', 0, 0 );
		if( trim($days_perso) != '' ){
			$days_perso = json_decode( $days_perso, true );

			foreach( $days_perso as $key => $days ){
				if( isset($cfg_days[ $key ]) ){
					if( is_numeric($days) && $days >= 0 ){
						$cfg_days[ $key ]['days'] = $days;
					}
				}
			}
		}
	}

	$cfg_days = array_msort( $cfg_days, ['group_position' => SORT_ASC, 'position' => SORT_ASC] );

	return $cfg_days;
}
/// @}

// \endcond
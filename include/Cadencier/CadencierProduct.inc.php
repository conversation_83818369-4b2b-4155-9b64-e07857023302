<?php
require_once('Container/Container.inc.php');

/**
 * Class CadencierProduct
 */
class CadencierProduct extends Container
{
	/**
	 * Le produit en question
	 * 
	 * @var array $product
	 */
	protected $product;

	/**
	 * Les périodes de commandes
	 * 
	 * @var Periods $periods
	 */
	protected $periods;

	/**
	 * Le nombre de quantité commandé dans des commandes précédente
	 * 
	 * @var array $ordered_quantities
	 */
	protected $ordered_quantities;

	/**
	 * Tableau des quantités par période sinon null
	 * 
	 * @var array|null $qte 
	 */
	protected $qte;


	/**
	 * CadencierProduct constructor.
	 *
	 * @param array   $singleProduct Tableau représentant le produit prd_products_get
	 * @param Periods $periods Objet Periods
	 */
	public function __construct( array $singleProduct, Periods $periods)
	{
		$this->product = $singleProduct;
		$this->periods = $periods;
		$this->ordered_quantities = array();
		parent::__construct($this->product);
	}


	/**
	 * Cette fonction permet de retourner le tableau représentant le produit
	 * 
	 * @return array Tableau du produit
	 */
	public function product()
	{
		return $this->product;
	}


	/**
	 * Cette fonction retourne la périod
	 * 
	 * @return Periods Retourne l'objet périod
	 */
	public function periods()
	{
		return $this->periods;
	}


	/**
	 * Cette fonction permet d'ajouter une quantité pour une date précise
	 * @param Datetime $period Date 
	 * @param int      $qte Quantité à ajouter
	 *
	 * @return void
	 */
	public function addQuantityForPeriod( Datetime $period, $qte)
	{
		foreach ($this->periods->DatePeriod() as $key => $value) {
			if ($period == $value) {
				$this->ordered_quantities[$key] = $qte;
			}
		}
	}

	/**
	 * Cette fonction retourne le quantité
	 * 
	 * @return array Retourne un tableau avec les quatités par période
	 */
	public function getPeriodsQte()
	{
		if (!is_null($this->qte)) {
			return $this->qte;
		}

		$this->qte = array();
		foreach ($this->periods->DatePeriod() as $key => $value) {
			$this->qte[$key] = 0;
			if (!array_key_exists($key, $this->ordered_quantities)) {
				continue;
			}
			$this->qte[$key] += $this->ordered_quantities[$key];
		}

		return $this->qte;
	}
}
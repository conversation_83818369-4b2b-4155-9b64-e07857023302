<?php

/**	\defgroup system Fonctions système
 *	Ces fonctions sont des utilitaires à la base de tout le système.
 */

/**	\defgroup sys_countries Pays
 *	\ingroup system
 *	Ce module comprend les fonctions nécessaires à la gestion des pays. Nous utilisons dans ce module la norme
 *	ISO 3166 comme base de données de référence.
 *	@see https://fr.wikipedia.org/wiki/ISO_3166
 *	@{
 */

/**	Cette fonction permet le chargement de la liste des pays
 *	@param string $ctn_code Facultatif, code d'un continent
 *	@param string $cnt_code Facultatif, code ou tableau de codes pays
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- code : code du pays sur deux lettres (norme iso 3166)
 *		- code3 : code du pays sur trois lettres
 *		- name : désignation du pays en français
 *		- cnum : code numérique du pays (norme iso 3166)
 */
function sys_countries_get( $ctn_code='', $cnt_code=false ){
	global $config;

	if( $cnt_code !== false ){
		if( !is_array($cnt_code) ){
			if( trim($cnt_code) == '' ){
				return false;
			}

			$cnt_code = array( $cnt_code );
		}else{
			foreach($cnt_code as $one_c ){
				if( trim($one_c) == '' ){
					return false;
				}
			}
		}
	}

	$sql = '
		select cnt_code as code, cnt_code3 as code3, cnt_numeric as cnum,
		'.( i18n::getLang()!=$config['i18n_lng'] ? 'ifnull(tsl_translate, cnt_name) as tsl_name,' : '' ).' cnt_name as name
		from sys_countries
	';

	if( trim($ctn_code)!='' ){
		$sql .= '
			join sys_continents on (cnt_ctn_code=ctn_code)
		';
	}
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$sql .= '
			left join sys_countries_translates on (cnt_code=tsl_cnt_code and "'.i18n::getLang().'"=tsl_lng_code)
		';
	}

	$sql .= '
		where 1
	';

	if( trim($ctn_code)!='' ){
		$sql .= ' and ctn_code="'.addslashes( $ctn_code ).'"';
	}

	if( is_array($cnt_code) ){
		$sql .= ' and cnt_code in ("'.implode('", "', $cnt_code).'")';
	}

	$sql .= '
		order by '.( i18n::getLang()!=$config['i18n_lng'] ? 'ifnull(tsl_translate, cnt_name)' : 'cnt_name' ).'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet la vérification de l'existance d'un nom de pays dans la table de référence
 *  @param string $name Obligatoire, désignation à tester
 *  @return bool true si le nom du pays existe dans la table de référence, false dans le cas contraire.
 */
function sys_countries_exists_name( $name ){
    return ria_mysql_num_rows(ria_mysql_query('
        select cnt_code
		from sys_countries
		where cnt_name=\''.addslashes(trim($name)).'\'
    '))>0;
}

/** Cette fonction permet la vérification de l'existance d'un code de pays dans la table de référence
 *  @param string $code Obligatoire, désignation à tester
 *  @return bool true si le code du pays existe dans la table de référence, false dans le cas contraire.
 */
function sys_countries_exists_code( $code ){
    return ria_mysql_num_rows(ria_mysql_query('
        select cnt_code
		from sys_countries
		where cnt_code=\''.addslashes(trim($code)).'\'
    '))>0;
}

/**	Cette fonction détermine si un pays existe via son code ISO 3 caractères.
 *	@param string $code Code pays sur 3 caractères.
 *	@return bool True s'il existe, False sinon.
 */
function sys_countries_exists_code3( $code ){

	$res = ria_mysql_query('
        select cnt_code3
		from sys_countries
		where cnt_code3 = "'.addslashes(trim($code)).'"
    ');

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}

/** Cette fonction crée une liste composée de la France métropolitaine et des DOM-TOM
 *	Elle est à mettre à jour en cas de modification de la législation DOM-TOM
 * 	@param bool $without_dom_tom si faux on recupère toutes les zones francaise (dom-tom et monaco inclus)
 *	@return array Liste de territoires français sous forme de tableau
 *	@deprecated Ne plus utiliser.
 */
function sys_countries_get_france( $without_dom_tom = false ){
	$sql = "select  cnt_name as name from sys_countries where cnt_code in (
			'FR'
		  ";
	if(!$without_dom_tom){
		$sql.=", 'GP', 'GF', 'MQ',
				'YT', 'MC', 'NC', 'PF',
				'RE', 'BL', 'MF', 'PM', 'TF', 'WF'";
	}
	$sql .=")";

	$countries = ria_mysql_query( $sql );
	$countries_array = array();
	while( $c = ria_mysql_fetch_array( $countries ) ){
		$countries_array[] = $c['name'];
	}

	return $countries_array;
}

/** Cette fonction crée une liste composé des DOM-TOM
 *	Elle est à mettre à jour en cas de modification de la législation DOM-TOM
 *	@param bool $use_monaco Facultatif, détermine si Monaco doit être retourné en tant que DOM-TOM (false par défaut)
 *
 *	@return array Liste de territoires français sauf France et Monaco
 *	@deprecated Ne plus utiliser.
 */
function sys_countries_get_domtom( $use_monaco=false ){
	$sql = "select  cnt_name as name from sys_countries where cnt_code in (
			'GP', 'GF', 'MQ',
			'YT', 'NC', 'PF',
			'RE', 'BL', 'MF', 'PM', 'TF', 'WF'
			".( $use_monaco ? ", 'MC'" : ""  )."
		)";

	$countries = ria_mysql_query( $sql );
	$countries_array = array();
	while( $c = ria_mysql_fetch_array( $countries ) ){
		$countries_array[] = $c['name'];
	}

	return $countries_array;
}

/** Cette fonction crée une liste composée des Pays de l'Europe
 *	Elle est à mettre à jour en cas de modification de la législation Européenne
 *	@param bool $use_france Facultatif, détermine si la France métropolitaine et Monaco doivent être intégré (False par défaut)
 *
 *	@return array Liste des pays d'Europe
 *	@deprecated Ne plus utiliser.
 */
function sys_countries_get_zone_euro( $use_france=false ){
	$sql = "select  cnt_name as name from sys_countries where cnt_code in (
			'AT','BE','BG','HR','CY','CZ','DK','EE','FI','FR','DE','GR','HU','IE','IT',
			'LV','LT','LU','MT','NL','PL','PT','RO','SK','SI','ES','SE','GB'
			".( $use_france ? ", 'FR', 'MC'" : "" )."
		)";

	$countries = ria_mysql_query( $sql );
	$countries_array = array();
	while( $c = ria_mysql_fetch_array( $countries ) )
		$countries_array[] = $c['name'];

	return $countries_array;
}

/** Cette fonction recherche le code associé à un pays.
 *	@param string $name Obligatoire, nom complet du pays (ne peut pas être vide).
 *	@param bool $no_casse Facultatif, rend le nom insensible à la casse.
 *	@param bool $code3 Facultatif, si activé le code à 3 caractères est retourné.
 *
 *	@return string|bool code du pays, False s'il n'a pas été trouvé.
 */
function sys_countries_get_code( $name, $no_casse=false, $code3=false ){

	$name = trim($name);

	if( !$name ){
		return false;
	}

	$sql = '
		select
			'.( $code3 ? 'cnt_code3' : 'cnt_code' ).' as code
		from sys_countries
	';
	if( $no_casse ){
		$sql .= ' where upper(replace(cnt_name, "-", " ")) = upper("'.addslashes(str_replace('-', ' ', $name)).'")';
	}else{
		$sql .= ' where cnt_name = "'.addslashes($name).'"';
	}

	$countries = ria_mysql_query($sql);

	if( !$countries || !ria_mysql_num_rows($countries) ){
		return false;
	}

	$c = ria_mysql_fetch_assoc($countries);

	return $c['code'];

}

/** Cette fonction recherche le nom complet d'un pays à partir de son code
 *	Un des deux paramètres doit être spécifié.
 *	@param string $code Facultatif, code ISO du pays (2 caractères)
 *	@param string $code3 Facultatif, code ISO du pays (3 caractères)
 *	@param $get_tsl Facultatif, mettre le code de la langue pour récupérer le nom du pays traduit
 *	@return string|bool Nom complet du pays, False si celui-ci n'a pas été trouvé
 */
function sys_countries_get_name( $code=false, $code3=false, $get_tsl=false ){

	$code = trim($code);
	$code3 = trim($code3);
	if( !$code && !$code3 ){
		return false;
	}

	$sql = '
		select '.( trim($get_tsl) != '' ? 'ifnull(tsl_translate, cnt_name)' : 'cnt_name' ).' as name
		from sys_countries
	';

	if( trim($get_tsl) != '' ){
		$sql .= 'left join sys_countries_translates on (cnt_code = tsl_cnt_code and tsl_lng_code = "'.addslashes( $get_tsl ).'")';
	}

	if( $code ){
		$sql .= ' where cnt_code = "'.addslashes($code).'"';
	}else{
		$sql .= ' where cnt_code3 = "'.addslashes($code3).'"';
	}

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'name');

}

/** Cette fonction retourne le code HTML d'un select de tous les pays
 *	@param string $class Facultatif, classes affectées au select. Ne pas inclure l'attribut class="", juste le nom des classes sous forme de chaine de caractères.
 *	@param string $country Facultatif, permet de pré-selectionner une valeur
 *	@param $foption Facultatif, première ligne d'option
 *	@param $in_countries option permet de surcharger le liste de restriction des pays à afficher, mettre null pour n'appliquer aucune restriction de pays
 *	@param $input_name option permet de changer le name du champ select généré
 *	@param $ctn_code Facultatif, code ISO d'un continent
 *	@param $select_id Facultatif, identifiant du select contenant les pays
 *	@param $val_is_name Facultatif, par défaut la valeur des options du select est le nom du pays, mettre False pour que ce soit le code ISO
 *	@return string Le code HTML du select
 *	@deprecated Cette fonction est actuellement utilisée dans les sites Ma Maison est Magnifique, Chazelles, Pierre Oteiza, Navicom, ...
 *		Elle dépend cependant de la vue et est donc mal placée.
 */
function sys_countries_get_html_select( $class='', $country='', $foption='', $in_countries=false, $input_name='country', $ctn_code='', $select_id='country', $val_is_name=true ){
	global $config;
	$html = '';

	if( !is_array($in_countries) || !sizeof($in_countries) ){
		if( $in_countries!==null ){
			if( isset($config['list_include_countries']) ){
				if( is_array($config['list_include_countries']) && sizeof($config['list_include_countries']) ){
					$in_countries = $config['list_include_countries'];
				}
			}
		}
	}

	$rc = sys_countries_get( $ctn_code );
	if( $rc && ria_mysql_num_rows($rc) ){
		$html .= '	<select id="'.htmlspecialchars( $select_id ).'" name="'.$input_name.'" '.( trim($class)!='' ? 'class="'.$class.'"' : '' ).'>';

		if( trim($foption)!='' ){
			$html .= '	<option value="-1">'.htmlspecialchars($foption).'</option>';
		}

		while( $c = ria_mysql_fetch_array($rc) ){
			if( !is_array($in_countries) || !sizeof($in_countries) || in_array($c['code'], $in_countries) || in_array(strtolower2($c['name']), $in_countries) ){
				$selected = strtolower2($c['name'])==strtolower2($country) ? 'selected="selected"' : '';
				$html .= '	<option '.$selected.' value="'.( $val_is_name ? $c['name'] : $c['code'] ).'">'.htmlspecialchars(  isset($c['tsl_name']) ? $c['tsl_name'] : $c['name']  ).'</option>';
			}
		}

		$html .= '	</select>';
	}

	return $html;
}

/** Cette fonction retourne un tableau associatif id=>nom ou nom=>nom des pays, filtrés ou non selon une liste
 *	@param bool $code Facultatif, utilisation du code du pays ou de son nom en clef de tableau
 *	@param $in_countries Facultatif, la liste des codes des pays que l'on souhaite retourner
 *	@return array Un tableau associatif de pays, vide si aucun résultat
 */
function sys_countries_get_array($code=true, $in_countries=false){
	global $config;
	$pays = array();

	if( !is_array($in_countries) ){
		if( isset($config['list_include_countries']) ){
			if( is_array($config['list_include_countries']) ){
				$in_countries = $config['list_include_countries'];
			}
		}
	}

	$rc = sys_countries_get();
	if( $rc && ria_mysql_num_rows($rc) ){
		while( $c = ria_mysql_fetch_array($rc) ){
			if( !is_array($in_countries) || in_array($c['code'], $in_countries) || in_array(strtolower2($c['name']), $in_countries) || !$in_countries ){
				if($code){
					$pays[$c['code']] = $c['name'];
				}else{
					$pays[$c['name']] = $c['name'];
				}
			}
		}

	}

	return $pays;
}

/** Cette fonction retourne un tableau des continents.
 *	@param string $ctn_code Facultatif, code ISO d'un continent
 *	@return array Un tableau contenant pour chaque continent :
 *				- code : code ISO du continent
 *				- name : nom du continent
 *				- latitude : latitude du centre
 *				- longitude : longitude du centre
 */
function sys_continents_get( $ctn_code='' ){
	$sql = '
		select ctn_code as code, ctn_name as name, ctn_latitude as latitude, ctn_longitude as longitude
		from sys_continents
		where 1
	';

	if( trim($ctn_code)!='' ){
		$sql .= ' and ctn_code="'.addslashes( $ctn_code ).'"';
	}

	return ria_mysql_query( $sql );
}

/// @}
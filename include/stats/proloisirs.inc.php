<?php

// \cond onlyria
/** Cette fonction est spécifique à un client "Proloisirs", permet de récupérer les lignes de commandes passées sur le site public
 *  @param string $date_start Obligatoire date de début
 *  @param string $date_end Obligatoire date de fin
 *  @return resource Un résultat MySQL contenant :
 *                  - ord_id : identifiant d'une commande
 *                  - id : identifiant du produit
 *                  - ref : référence du produit
 *                  - name : nom du produit
 *                  - price_ht : prix unitaire du produit (HT)
 *                  - tva_rate : taux de TVA appliqué
 *                  - qte : quantité commandée
 */
function proloisirs_stats_ord_products_web( $date_start, $date_end ){
	global $config;

	if ($config['tnt_id'] != 4) {
		return false;
	}

	$date_start = dateheureparse( $date_start );
	if (!isdateheure($date_start)) {
		return false;
	}

	$date_end = dateheureparse( $date_end );
	if (!isdateheure($date_end)) {
		return false;
	}

	$r_orders = proloisirs_stats_orders_web( $date_start, $date_end );

	$ar_ord_ids = array();
	if ($r_orders) {
		while ($ord = ria_mysql_fetch_assoc($r_orders)) {
			if (trim($ord['list_orders']) != '') {
				$ar_ord_ids = array_merge( $ar_ord_ids, explode(',', $ord['list_orders']) );
			}
		}
	}

	if (count($ar_ord_ids)) {
		$r_ord_prod = ria_mysql_query('
			select prd_ord_id as ord_id, prd_id as id, prd_name as name, prd_ref as ref, prd_price_ht as price_ht, prd_qte as qte, prd_tva_rate as tva_rate
			from ord_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ord_id in ('.implode(', ', $ar_ord_ids).')
			order by prd_ord_id, prd_ref
		');

		return $r_ord_prod;
	}

	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction est spécifique à un client "Proloisirs", permet de récupérer les stats de commandes passées sur le site public
 *  @param string $date_start Obligatoire date de début
 *  @param string $date_end Obligatoire date de fin
 *  @return resource Un résultat MySQL contenant :
 *              - usr_ref : référence client
 *              - str_name : nom du magasin
 *              - str_zipcode : code postal du magasin
 *              - str_city : ville du magasin
 *              - nb_ord : nombre de commande
 *              - total_ht : total_ht des commandes
 *              - total_ttc : total_ttc des commandes
 *              - avg_ht : moyenne HT des commandes
 *              - avg_ttc : moyenne TTC des commandes
 *              - payline : magasin lié à Payline ("Oui" / "Non")
 *              - list_orders : liste des identifiants de commande
 */
function proloisirs_stats_orders_web( $date_start, $date_end ){
	global $config;

	if ($config['tnt_id'] != 4) {
		return false;
	}

	$date_start = dateheureparse( $date_start );
	if (!isdateheure($date_start)) {
		return false;
	}

	$date_end = dateheureparse( $date_end );
	if (!isdateheure($date_end)) {
		return false;
	}

	$sql = '
	select
		(select pv_value from fld_object_values where pv_tnt_id = str_tnt_id and pv_obj_id_0 = str_id and pv_fld_id = 868) as usr_ref,
		ord_usr_id as usr_id, str_name, str_zipcode, str_city, count(*) as nb_ord, sum(ord_total_ht) as total_ht, sum(ord_total_ttc) as total_ttc,
		avg(ord_total_ht) as avg_ht, avg(ord_total_ttc) as avg_ttc,
		if( (select ifnull(pv_value, "") from fld_object_values where pv_tnt_id = str_tnt_id and pv_obj_id_0=str_id and pv_fld_id = 2692) != "", "Oui", "Non") as payline,
		group_concat(ord_id) as list_orders
	from ord_orders
		join fld_object_values on (pv_tnt_id = ord_tnt_id and pv_obj_id_0 = ord_id and pv_fld_id = 874)
		join dlv_stores on (str_tnt_id = pv_tnt_id and str_id = pv_value)
	where ord_tnt_id = '.$config['tnt_id'].' and ord_wst_id in (27, 30)
		and ord_date >= "'.addslashes( $date_start ).'" and ord_date <= "'.addslashes( $date_end ).'"
		and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
	group by str_id
	order by sum(ord_total_ht) desc, count(*) desc
';

	return ria_mysql_query( $sql );
}
// \endcond

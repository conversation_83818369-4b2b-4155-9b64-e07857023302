<?php
	require_once('define.inc.php');

	// \cond onlyria
	/** Cette fonction permet de récupère une liste de statistiques
	 *  @param int $id Facultatif : identifiant de la stats
	 *  @param int $offset Facultatif : offset à partir duquel démarrer le résultat
	 *  @param int $limit Facultatif : permet de donner un nombre d'élément à récupérer maximum
	 *  @return array Un tableau des éléments récupérés en cas de succès ou False en cas d'erreur
	 */
	function stats_goals_get($id = 0, $offset = 0, $limit = 0, $sort = array('created' => 'asc')){
		$params = array();

		if ($id) {
			$params['_id'] = $id;
		}
		if ($offset) {
			$params['skip'] = $offset;
		}
		if ($limit) {
			$params['limit'] = $limit;
		}

		$params['sort'] = array($sort);

		$results = couchdb_get_all(CLS_STATS_GOALS, $params);
		if (!$results) {
			return false;
		}

		return $results;
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de sauvegarder des statistiques permettant de calculer l'avancement des objects.
	 * 	@param $result Obligatoire, un tableau contenant pour chaque représentants les statistique d'une journée
	 * 	@param $date Obligatoire, date de la journée à sauvegarder
	 * 	@param $objs Optionnel, un tableau déterminé que les statistiques s'applique à un objet, ['cls_id' => 00, 'obj_id_0' => 00]
	 * 	@param $cust_id Optionnel, identifiant d'un compte client
	 * 	@return bool true en cas de succès, false dans le cas contraire
	 */
	function stats_goals_add( $result, $date, $objs=[], $cust_id=0 ){
		global $config;

		if( !is_array($result) ){
			return false;
		}

		if( !isdate($date) ){
			return false;
		}

		if( !is_array($objs) ){
			return false;
		}

		if( count($objs) ){
			if( !ria_array_key_exists(['cls_id', 'obj_id_0'], $objs) ){
				return false;
			}

			if( !in_array($objs['cls_id'], [CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND]) ){
				return false;
			}

			if( !is_numeric($objs['obj_id_0']) || $objs['obj_id_0'] <= 0 ){
				return false;
			}
		}

		if( !is_numeric($cust_id) || $cust_id < 0 ){
			return false;
		}

		// Parcours chaque statistique représentant
		// Si toutes les stats sont à zéro, alors on n'enregistrera rien dans CouchDB
		foreach ($result as $seller => $data) {
			$sum = 0;

			foreach ($data as $k => $d) {
				if (strstr($k, '_STATS_')) {
					$sum += $d;

					if( $d == 0 ){
						unset($result[ $seller ][ $k ]);
					}
				}
			}

			if ($sum == 0) {

				if( $cust_id == 0 ){// TODO devrait prévoir la supp du doc pour les custormers, mais pour des raison de perf on le fait pas .. 
					$date = new DateTime( $date, new DateTimeZone("UTC") );
					$start = $date->format('Y-m-d').' 00:00:00';
					$end = $date->format('Y-m-d').' 23:59:59';

					$results = goals_get_by_view( $seller, array( 'date1' => $start, 'date2' => $end), $objs );

					// Suppression 
					if( count($results) > 0 ){
						foreach( $results as $res ){
							CouchDB::create(_COUCHDB_GOALS_DB_NAME)->delete(CLS_STATS_GOALS, $res['_id']);
						}
					}
				}

				unset($result[$seller]);
			}
		}

		if (count($result)) {
			$date = new DateTime( $date, new DateTimeZone("UTC") );
			$start = $date->format('Y-m-d').' 00:00:00';
			$end = $date->format('Y-m-d').' 23:59:59';

			foreach( $result as $seller => $data ){
				// Récupère dans couchDB un document contenant déjà des statistiques pour la journée et le représentant
				if( $cust_id > 0 ){
					$results = goals_get_by_view( $cust_id, array( 'date1' => $start, 'date2' => $end), $objs, $seller );
				}else{
					$results = goals_get_by_view( $seller, array( 'date1' => $start, 'date2' => $end), $objs );
				}

				// Suppression des doublons si l'en existe
				if( count($results) > 1 ){
					foreach( $results as $res ){
						CouchDB::create(_COUCHDB_GOALS_DB_NAME)->delete(CLS_STATS_GOALS, $res['_id']);
					}

					$results = [];
				}

				if( count($results) > 0 ){
					// Met à jour le document s'il existe déja
					CouchDB::create(_COUCHDB_GOALS_DB_NAME)->update(CLS_STATS_GOALS, $results[0]['_id'], $data);
				}else{
					// Sinon on le créé
					CouchDB::create(_COUCHDB_GOALS_DB_NAME)->add(CLS_STATS_GOALS, $data);
				}
			}
		}

		return true;
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de récupérer les statistiques d'un représentnat
	 * 	@param int $usr_id Obligatoire, identifiant d'un représentant
	 * 	@param string $date_start Optionnel, date (aaa-mm-jj) de début de la recherche des statistique
	 * 	@param string $date_end Optionnel, date (aaaa-mm-jj) de fin de la recherche des statistique
	 * 	@param $kpis Optionnel, tableau d'identifiants de kpi
	 * 	@param array $objs Optionnel, tableau permettant de filtrer sur un objet (cls_id => type de d'objet, obj_id_0 => objet recherché)
	 * 	@param null|int $seller_id Optionnel, identifiant du représentant (lorsque l'objectif porte sur un client en particulier)
	 *
	 * 	@return array un tableau array( id KPI => stats )
	 * 	@return bool false en cas d'erreur
	 */
	function stats_kpi_get( $usr_id, $date_start=null, $date_end=null, $kpis=0, $objs=[], $seller_id=null ){
		if( !is_numeric($usr_id) || $usr_id <= 0 ){
			return false;
		}

		if( !$date_start || !$date_end ){
			$date_start = date('Y-m-d');
			$date_end = date('Y-m-d');
		}

		$results = goals_get_by_view( $usr_id, array( 'date1' => $date_start, 'date2' => $date_end), $objs, $seller_id );

		if( !$results ){
			return false;
		}

		$res = array();
		$r_kpi = obj_objectifs_get_kpi( $kpis );
		if( !$r_kpi || !ria_mysql_num_rows($r_kpi) ){
			return false;
		}
		while( $kpi = ria_mysql_fetch_assoc($r_kpi) ){
			$total = 0;
			foreach( $results as $result ){
				if( isset($result[$kpi['code']]) ){
					$total = $total + floatval(str_replace(',', '.', $result[$kpi['code']]));
				}
			}
			$res[$kpi['id']] = $total;
		}

		return $res;
	}
	// \endcond
<?php
// \cond onlyria

/**
 * Cette fonction permet de déterminer si le retour de memcached->get et correcte ou non
 *
 * @param Memcached $memcached Instance de memcached
 * @return bool Retourne true si la réponse est correcte sinon false
 */
function ria_is_memcached_result_ok($memcached)
{
	// 47 MEMCACHED_SERVER_TEMPORARILY_DISABLED
	$error_codes = array(Memcached::RES_NOTFOUND, Memcached::RES_NO_SERVERS, 47);

	$is_error_code = in_array($memcached->getResultCode(), $error_codes);

	return !$is_error_code;
}
/** \defgroup model_debug Barre de debug
 * 	\ingroup system
 *	Ce module comprend les fonctions nécessaires à la gestion de la barre de debug.
 *
 *	@{
 */

/**	Cette fonction retourne le nom de la fonction appelante. C'est très utile pour adapter/optimiser
 * 	le code de certaines fonctions en fonction du contexte d'appel.
 * 	@return string le nom de la fonction appelante, ou chaîne vide si aucune
 */
function ria_debug_caller_function_name(){

	$backtrace = debug_backtrace();
	if( isset($backtrace[2]['function']) ){
		return $backtrace[2]['function'];
	}

	return '';
}

/** Cette fonction permet de suivre le chemain venant à l'appel d'une fonction.
 * 	@return empty, affiche directement l'historique des appels
 */
function ria_debug_caller_function_histo(){
	$debug = debug_backtrace();
	$debug_i = 0;
	foreach( $debug as $one_debug ){
		print ($debug_i++).' => '.$one_debug['file'].':'.$one_debug['line'].' => '.$one_debug['function'].'<br />';
	}
}

/** Cette fonction permet d'activer la barre de débug
 */
function ria_debug_active(){
	if (isset($_SESSION['ria_debug'])) {
		unset($_SESSION['ria_debug']);
	}

	$_SESSION['ria_debug'] = array( 'actived' => time(), 'memcached' => true, 'get' => array(), 'post' => array(), 'request' => array() );
}

/** Cette fonction permet de désactiver la barre de débug
 */
function ria_debug_unactive(){
	if (isset($_SESSION['ria_debug'])) {
		unset($_SESSION['ria_debug']);
	}
}

/** Cette fonction permet de réinitialiser la barre de débug
 */
function ria_debug_reinit(){
	$time = time();
	$active_memcached = true;

	if (isset($_SESSION['ria_debug'])) {
		$time = $_SESSION['ria_debug']['actived'];
		$active_memcached = $_SESSION['ria_debug']['memcached'];
		$debug_admin = $_SESSION['ria_debug']['debug_admin'];

		unset($_SESSION['ria_debug']);
	}

	$_SESSION['ria_debug'] = array( 'actived' => $time, 'memcached' => $active_memcached, 'debug_admin' => $debug_admin, 'get' => array(), 'post' => array(), 'request' => array() );
}

/** Cette fonction permet d'activer la barre de débug pour l'espace d'administration (hors page debug.php)
 *	@return bool False si la barre de débug n'est pas activé
 */
function ria_debug_admin_actived(){
	if (!ria_debug_is_actived()) {
		return false;
	}

	$_SESSION['ria_debug']['debug_admin'] = true;
}

/** Cette fonction permet de désactiver la barre de débug pour l'espace d'administration (hors page debug.php)
 *	@return bool False si la barre de débug n'est pas activé
 */
function ria_debug_admin_unactived(){
	if (!ria_debug_is_actived()) {
		return false;
	}

	$_SESSION['ria_debug']['debug_admin'] = false;
}

/** Cette fonction permet de savoir si la barre de débug est activée ou non dans l'espace d'administration
 *	@return bool True si c'est le cas, False dans le cas contraire
 */
function ria_debug_admin_is_actived(){
	return (isset($_SESSION['ria_debug']['debug_admin']) && $_SESSION['ria_debug']['debug_admin']);
}

/** Cette fonction permet d'activer le cache pour la barre de débug
 *	@return bool False si la barre de débug n'est pas activé
 */
function ria_debug_memcached_actived(){
	if (!ria_debug_is_actived()) {
		return false;
	}

	$_SESSION['ria_debug']['memcached'] = true;
}

/** Cette fonction permet de désactiver le cache pour la barre de débug
 *	@return bool False si la barre de débug n'est pas activé
 */
function ria_debug_memcached_unactived(){
	if (!ria_debug_is_actived()) {
		return false;
	}

	$_SESSION['ria_debug']['memcached'] = false;
}

/** Cette fonction permet de savoir si le cache est activé pour la barre de débug
 *	@return bool True si c'est le cas (débug désactivé ou cache activé), False dans le cas contraire
 */
function ria_debug_memcached_is_actived(){
	return (!isset($_SESSION['ria_debug']['memcached']) || $_SESSION['ria_debug']['memcached']);
}

/** Cette permet de savoir si la barre de débug est activée ou non.
 *	@return bool True si elle est activée, False dans le cas contraire
 */
function ria_debug_is_actived(){
	$is_active = false;
	if (isset($_SESSION['ria_debug']) && is_array($_SESSION['ria_debug']) && count($_SESSION['ria_debug'])) {
		if (strtotime('-15 minutes') <= $_SESSION['ria_debug']['actived']) {
			$is_active = true;
		}
	}

	return $is_active;
}

/** Cette fonction permet de savoir si la page où l'on est doit être exclue de la barre de début
 *	@return bool True s'il faut l'exclure, False dans le cas contraire
 */
function ria_debug_page_excluded(){
	if (!isset($_SERVER['SCRIPT_URL']) || !trim($_SERVER['SCRIPT_URL'])) {
		return false;
	}

	if (!ria_debug_admin_is_actived()) {
		if (isset($_SERVER['SCRIPT_URL']) && strstr($_SERVER['SCRIPT_URL'], '/admin')) {
			return true;
		}
	}

	if ($_SERVER['SCRIPT_URL'] == '/admin/debug.php') { return true; }

	return false;
}

/** Cette fonction permet de dumper les informations sur des variables
 * @param mixed $args 1 ou plusieurs variables
 * @return void
 */
function ria_dump($args){
	$arguments = func_get_args();

	ob_start();
	call_user_func_array('var_dump', $arguments);
	$dumb = ob_get_clean();

	if(php_sapi_name() !== 'cli'){
		echo '<pre>';
		echo $dumb;
		echo '</pre>';
	}else{
		echo $dumb;
	}
}

/** Cette fonction permet de dumper les informations sur des variables avec un arrêt de script
 * @param mixed $args 1 ou plusieurs variables
 * @return void
 */
function ria_dd($args){
	$arguments = func_get_args();
	call_user_func_array('ria_dump', $arguments);
	exit;
}

/** Cette fonction permet de dumper des informations
 * @param	mixed	$var L'expression à afficher
 * @return	void
 */
function ria_print_r($var, $exit=true){
	print '<pre>';
	print_r($var);
	print '</pre>';

	if( is_bool($exit) && $exit ){
		exit;
	}
}

/** Cette fonction permet de logger des éléments dans un fichier dédié
 * @param string $type code correspondant au type du log (permet les filtres)
 * @param array $message contenu du log, mettre dedant le maximum d'information pertinante sous forme de tableau clé / value
 *
 */
function ria_debug_log($type, $message = array()){
	if( !$type ) return;
	if( !is_array($message) ) return;
	global $config;

	// recup la stack
	ob_start();
	debug_print_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
	$message['trace'] = ob_get_contents();
	ob_end_clean();

	$message['call_usr_id'] = isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;
	$message['call_prf_id'] = isset($_SESSION['usr_prf_id']) ? $_SESSION['usr_prf_id'] : 0;
	$message['call_usr_mail'] = isset($_SESSION['usr_email']) ? $_SESSION['usr_email'] : "";
	$message['call_date'] = date('Y-m-d H:i:s');
	$message['request_uri'] = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] :"";

	$content = '['.$type .'][tnt:'.$config['tnt_id'].']';
	$content .= print_r($message, true);
	$content .= "\r\n";

	error_log($content, 3, "/var/log/php/audit.log");
}

function ria_log($file, $msg) {
    // Convert arrays or objects to strings
    $msg = is_array($msg) || is_object($msg) ? print_r($msg, true) : $msg;

    // Get debug information
    $debug = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0];

    // Find the 'www' directory
    $wwwPath = $_SERVER['DOCUMENT_ROOT'];
    while ($wwwPath !== '/' && basename($wwwPath) !== 'www') {
        $wwwPath = dirname($wwwPath);
    }

    // Construct log path
    $logPath = $wwwPath . '/' . ltrim($file, '/');

    // Prepare log message
    $timestamp = date("Y-m-d H:i:s");
    $logMessage = "{$timestamp} - Log depuis '{$debug['file']}' <{$debug['line']}>\n{$timestamp} - {$msg}\n";

    // Write to log file
    file_put_contents($logPath, $logMessage, FILE_APPEND);
}
/// @}

// \endcond

<?php

//Import PHPMailer classes into the global namespace
//These must be at the top of your script, not inside a function
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

require_once('cfg.emails.inc.php');
require_once('users.inc.php');

/**	\defgroup email Envoi d'emails
 *	\ingroup messages
 *	Ce module fournit une classe utilitaire pour la gestion des emails mime multipart.
 *
 *	Il permet notamment la création simultanée d'une version texte et html d'un même message
 *	(tableaux compris), et l'inclusion de pièces jointes.
 *
 *	@{
 */

/**	\brief Encapsule la création et l'envoi d'un email multipart. Cette classe permet la création simultanée
 *	d'une version html et texte d'un même email.
 *
 *	Les pièces jointes sont également prises en charge.
 *
 *	Exemple simple :
 *	\code
 *		$email = new Email();
 *		$email->setFrom( 'Expéditeur <<EMAIL>>' );
 *		$email->addTo( 'Des<PERSON><PERSON> <<EMAIL>>' );
 *		$email->setSubject( 'Démonstration' );
 *		$email->addParagraph( 'Ce paragraphe sera visible à la fois dans la version texte et dans la version HTML.' );
 *		$email->send();
 *	\endcode
 *
 *	Exemple avec tableau :
 *	\code
 *		$email = new Email();
 *		$email->setFrom( 'Expéditeur <<EMAIL>>' );
 *		$email->addTo( 'Destinataire <<EMAIL>>' );
 *		$email->setSubject( 'Démonstration' );
 *
 *		$email->openTable();
 *		$email->openTableRow();
 *		$email->addCell( 'Tableau de démonstration' );
 *		$email->closeTableRow();
 *		$email->closeTable();
 *
 *		$email->send();
 *	\endcode
 *
 */
class Email {

	/// \privatesection
	// Destinataires
	public $from = '';
	public $to = array();
	public $cc = array();
	public $bcc = array();
	public $replyTo = '';
	public $return_path = '';

	// Contenu du message
	public $subject = '';
	public $text = '';
	public $html = '';
	public $attachments = array();

	/// \publicsection

	/**	Cette méthode permet la définition de l'émetteur du message. Son appel devrait être obligatoire
	 *	pour toute création de message.
	 *	Si cette méthode est appellée plusieurs fois, la valeur précédente est écrasée.
	 *	@param string $email Adresse email de l'émetteur du message
	 */
	function setFrom( $email ){
		$this->from = $email;
	}
	/**	Cette méthode permet l'ajout d'un destinataire pour le message. Il est tout à fait possible
	 *	d'appeller plusieurs fois cette méthode pour définir plusieurs destinataires.
	 *	Il est également possible d'appeller cette méthode avec pour paramètre un tableau comprenant
	 *	plusieurs adresses.
	 *	@param string $email Adresse email du destinataire, ou tableau des adresses
	 */
	function addTo( $email ){
		$emails = $email;
		if( !is_array($email) ){
			$emails = preg_split( '/[\n,]/', $email );
		}

		foreach( $emails as $e ){
			if( trim($e) && array_search($e,$this->to)===false ){  // Evite les doublons
				$this->to[] = $e;
			}
		}
	}
	/**	Cette méthode permet de réinitialiser tous les destinataires.
	 *	@param string $email Adresse email du destinataire, ou tableau des adresses
	 */
	function setTo( $email ){
		$this->to = array();
		$this->addTo( $email );
	}
	/**	Permet l'ajout d'une adresse à mettre en copie, qui recevra également le message. Cette méthode peut
	 *	être appellée plusieurs fois pour définir plusieurs destinataires à mettre en copie.
	 *	Il est également possible d'appeller cette méthode avec pour paramètre un tableau comprenant
	 *	plusieurs adresses.
	 *	@param string $email Adresse email à mettre en copie
	 */
	function addCC( $email ){
		$emails = $email;
		if( !is_array($email) ){
			$emails = preg_split( '/[\n,]/', $email );
		}

		foreach( $emails as $e ){
			if( trim($e) && array_search($e,$this->cc)===false ){  // Evite les doublons
				if( self::control_email($e) ){
					$this->cc[] = $e;
				}
			}
		}
	}
	/**	Permet de réinitialiser toutes les personnes en copie.
	 *	@param string $email Adresse email à mettre en copie
	 */
	function setCC( $email ){
		$this->cc = array();
		$this->addCC( $email );
	}
	/**	Cette méthode permet l'ajout d'une adresse à mettre en copie cachée du message. Ce destinataire recevra
	 *	le message, mais son adresse ne sera pas visible par les autres destinataires du message. Il est possible
	 *	d'appeller plusieurs fois cette méthode pour ajouter plusieurs destinataires en copie cachée.
	 *	Il est également possible d'appeller cette méthode avec pour paramètre un tableau comprenant
	 *	plusieurs adresses.
	 *	@param string $email Adresse email à mettre en copie cachée.
	 */
	function addBcc( $email ){
		$emails = $email;
		if( !is_array($email) ){
			$emails = preg_split( '/[\n,]/', $email );
		}

		foreach( $emails as $e ){
			if( trim($e) && array_search($e,$this->bcc)===false ){ // Evite les doublons
				if( self::control_email($e) ){
					$this->bcc[] = $e;
				}
			}
		}
	}

	/**	Cette méthode permet de réinitialiser toutes les personnes en copie cachée
	 *	@param string $email Adresse email à mettre en copie cachée.
	 */
	function setBcc( $email ){
		$this->bcc = array();
		$this->addBcc( $email );
	}

	/**	Cette méthode permet de récupérer toutes les personnes en copie cachée
	 *	@return array La valeur de BCC pour l'email
	 */
	function getBcc(){
		return $this->bcc;
	}

	/**	Cette méthode permet l'ajout d'une adresse à mettre en tant que "Réponse à"" pour le message.
	 *	Il n'est possible de mettre qu'une seule adresse mail
	 *	@param string $email Adresse email à mettre en tant que "Réponse à".
	 */
	function setReplyTo( $email ){
		$this->replyTo = $email;
	}

	/// \privatesection

	/// \publicsection
	/**	Permet la définition ou la modification du sujet du message. Cette méthode devrait être appellée
	 *	systématiquement à chaque utilisation de cette classe. Si cette méthode est appellée alors que le
	 *	sujet est déjà défini, le sujet est simplement remplacé par la nouvelle valeur.
	 *	@param string $subject Sujet du message
	 */
	function setSubject( $subject ){
		$this->subject = $subject;
	}
	/**	Cette méthode permet la modification de la version text/plain du message. Son appel remplace
	 *	totalement le contenu du message.
	 *	@param string $message Message en version text/plain
	 */
	function setTextMessage( $message='' ){
		$this->text = $message;
	}
	/**	Cette méthode permet l'ajout d'une ligne de texte à la version text/plain du message. Cette ligne
	 *	est ajoutée à la fin du message.
	 *	@param string $line Ligne de texte à ajouter
	 */
	function addTextLine( $line='' ){
		$this->text .= "$line\n";
	}
	/**	Cette méthode permet l'ajout d'une ligne vide à la version text/plain du message. Cette ligne vide
	 *	est ajoutée à la fin du texte. Cette fonction est identique à l'appel de la méthode addTextLine.
	 */
	function addBlankTextLine(){
		$this->text .= "\n";
	}
	/**	Permet la définition en une fois de la version text/html du message. Si cette version était déjâ 
	 *	définie, elle est remplacée. Les tags html sont préservés.
	 *	@param string $message Version html du message
	 */
	function setHtmlMessage( $message='' ){
		$this->html = $message;
	}
	/**	Permet l'ajout de texte html à la version text/html du message. Le fragment est ajouté à la fin du
	 *	message.
	 *	@param string $html Fragment html à ajouter à la fin du message
	 */
	function addHtml( $html ){
		$this->html .= $html;
	}
	/**	Permet l'ajout d'une pièce jointe au message. Cette pièce jointe peut être de n'importe quel type mime,
	 *	elle sera transmise encodée en base 64 pour préserver son contenu. L'envoi de fichiers texte ou binaires
	 *	est supporté.
	 *	Le nom original du fichier est préservé par la fonction d'envoi et sera donc visible par les destinataires
	 *	du message.
	 *	@param string $filename chemin complet du fichier à joindre au message (nom physique du fichier)
	 *	@param string $name Facultatif, nom du fichier tel qu'il sera présenté à l'utilisateur. Ce paramètre n'est nécessaire que si le nom physique du fichier est différent de son nom logique
	 *	@return bool true en cas de succès, false en cas d'échec
	 */
	function addAttachment( $filename, $name='' ){
		if( !is_string($filename) || !file_exists($filename) ){
			return false;
		}
		$this->attachments[] = array('filename' => $filename,
									 'name' => $name ? $name : $filename);
		return true;
	}
	/**	Permet l'ajout d'un paragraphe de texte au versions texte et html du message. Cette fonction est utile
	 *	pour composer simultanément les deux version du message.
	 *	Les tags html sont autorisés dans le paragraphe. Ils sont inclus dans la version html, et retirés dans
	 *	la version texte.
	 *	@param string $p Texte du paragraphe à ajouter.
	 */
	function addParagraph( $p ){
		$this->text .= html_strip_tags($p)."\n";
		$this->html .= '<p>'.nl2br($p)."</p>\n";
	}
	/**	Déclenche l'ouverture d'un paragraphe dans les version texte et html du message.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function openParagraph(){
		$this->html .= '<p>';
	}
	/**	Déclenche la fermeture d'un paragraphe dans les version texte et html du message.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function closeParagraph(){
		$this->html .= "</p>\n";
	}
	/**	Permet l'ajout d'une adresse dans les version texte et html du message. La version html
	 *	sera incluse avec un tag address ajouté automatiquement par cette fonction.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 *	@param string $address Adresse à ajouter au deux versions du message
	 */
	function addAddress( $address ){
		if( !mb_check_encoding( $address, 'UTF-8' ) )
			$address = mb_convert_encoding( $address, 'UTF-8' );

		$this->text .= $address;
		$this->html .= "<address>\n";
		$this->html .= nl2br(htmlspecialchars($address));
		$this->html .= "</address>\n";
	}
	/**	Permet l'ajout d'une barre horizontale aux version texte et html du message.
	 *	La version texte sera composée de tirets (-) tandis que la version html sera représentée
	 *	par le tag hr.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function addHorizontalRule(){
		$this->text .= str_repeat( '-', 76)."\n";
		$this->html .= '<hr />'."\n";
	}
	/**	Permet l'ouverture d'un tableau dans les versions texte et html du message.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 *	@param int $width Facultatif, largeur imposée au tableau, tel qu'accepté par l'attribut html width. Valeur par défaut : auto
	 *	@param int $border Facultatif, largeur de la bordure du tableau. Valeur par défaut : 1.
	 *	@param string $style Facultatif, style(s) supplémentaire(s) pour le tableau
	 *	@param string $border_color Facultatif, couleur des bordures du tableau
	 */
	function openTable( $width='auto', $border=1, $style='', $border_color=false ){
		if( !is_numeric($border) ) $border = 1;
		$this->text .= ' '.str_repeat( '-', 74 ).' '."\n";
		// Définition du style
		$hstyle = ' style="'.$style.'"';

		// Définition de la couleur de bordure
		$hborder_color = '';
		if($border_color!=false)
			$hborder_color = ' bordercolor="'.$border_color.'"';
		$this->html .= "<table border=\"".$border."\"".( is_numeric($width) ? ' width="'.$width.'"' : '' ).$hstyle.$hborder_color.">\n";
	}
	/**	Permet l'ouverture d'une ligne de tableau dans les versions texte et html du message.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function openTableRow(){
		$this->html .= "<tr valign=\"top\">\n";
	}
	/**	Permet l'ajout d'une cellule de tableau aux versions html et texte du message.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 *	@param $value Obligatoire, contenu de la cellule (les tags html sont autorisés)
	 *	@param $align Facultatif, alignement, tel que supporté par l'attribut align de la balise html td
	 *	@param $colspan Facultatif, nombre de cellules à fusionner horizontalement, tel que supporté par l'attribut colspan de la balise html td
	 *	@param $nowrap Facultatif, booléen indiquant si l'attribut nowrap doit être ajouté à la balise html td
	 *	@param $style Facultatif, style(s) supplémentaire(s) pour la cellule
	 *	@param $rowspan Facultatif, attribut rowspan à attribuer à la balise \c td
	 */
	function addCell( $value, $align='left', $colspan=1, $nowrap=0, $style='', $rowspan=1 ){
		if( !mb_check_encoding( $value, 'UTF-8' ) )
			$value = mb_convert_encoding( $value, 'UTF-8' );

		$this->text .= '| '.html_strip_tags($value).' | ';
		if( !trim($value) ) $value = '&nbsp;';
		// Définition du style
		$hstyle = ' style="'.$style.'"';

		$this->html .= "\t<td".( $colspan>1 ? ' colspan="'.$colspan.'"' : '' ).( $rowspan>1 ? ' rowspan="'.$rowspan.'"' : '' ).( $align!='left' ? ' align="'.$align.'"' : '' ).' '.( $nowrap==1 ? 'nowrap="nowrap"':'' ).$hstyle.'>'.$value."</td>\n";
	}
	/**	Permet la fermeture d'une ligne de tableau.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function closeTableRow(){
		$this->text .= "\n";
		$this->html .= "</tr>\n";
	}
	/**	Permet la fermeture d'un tableau.
	 *	Cette fonction est utile pour composer simultanément les deux version du message.
	 */
	function closeTable(){
		$this->text .= ' '.str_repeat( '-', 74 ).' '."\n";
		$this->html .= "</table>\n";
	}

	private static function toEmail( $email ){
		$ar = [];

		preg_match('/(.*)<(.*)>/', $email, $output_array);
		if( is_array($output_array) && count($output_array) == 3 ){
			$ar = [ 'email' => $output_array[2], 'name' => $output_array[1] ];
		}else{
			$ar = [ 'email' => $email, 'name' => '' ];
		}

		return $ar;
	}

	/** Envoi le message aux destinataires
	 */
	function send(){
		global $config;

		// ne pas envoyer de mail lors des tests unitaires
		if( isset($config['tnt_id']) && $config['tnt_id'] == 7 ){
			return true;
		}

		$cfg_smtp = [
			'server' => '', 'port' => '', 'login-method' => '', 'login' => '', 'password' => ''
		];

		$tmp = cfg_overrides_get_value('email_smtp_server', $config['wst_id'] );
		if( trim($tmp) != '' ){
			$tmp = json_decode( $tmp, true );

			$cfg_smtp = array_merge( $cfg_smtp, $tmp );
		}

		$active_stmp_serveur = trim($cfg_smtp['server']) != '';

		// L'envoi du mail doit se faire via PHPMailer
		if( $active_stmp_serveur ){
			try{
				// Créer un nouvel objet de type PHPMailer
				$temp = new PHPMailer(true);
				$temp->CharSet = "UTF-8";

				// Configuration de l'expéditeur
				$from = self::toEmail($this->from);
				if( !self::control_email($from['email']) ){
					return false;
				}

				// Configuration du serveur SMTP
				//$temp->SMTPDebug = SMTP::DEBUG_SERVER;
				$temp->isSMTP();
				$temp->Host = $cfg_smtp['server'];
				$temp->SMTPAuth = true;
				$temp->Username = $cfg_smtp['login'];
				$temp->Password = $cfg_smtp['password'];
				$temp->SMTPSecure = $cfg_smtp['login-method'] == 'SSL/TLS' ? PHPMailer::ENCRYPTION_SMTPS: PHPMailer::ENCRYPTION_STARTTLS;
				$temp->Port = $cfg_smtp['port'];

				if($config['tnt_id'] == 171) {
					$temp->From = $cfg_smtp['login'];
                	$temp->FromName = $from['name'];
				} else {

					$temp->setFrom( $from['email'], $from['name'] );
				}


				// Configuration du ou des destinataires principaux
				foreach( $this->to as $one_to ){
					$one_to = self::toEmail( $one_to );
					$temp->addAddress( $one_to['email'], $one_to['name'] );
				}

				// Configuration du ou des destinataires en copie visible
				foreach( $this->cc as $one_cc ){
					$one_cc = self::toEmail( $one_cc );
					$temp->addCC( $one_cc['email'], $one_cc['name'] );
				}

				// Configuration du ou des destinataires en copie cachée
				foreach( $this->bcc as $one_bcc ){
					$one_bcc = self::toEmail( $one_bcc );
					$temp->addBCC( $one_bcc['email'], $one_bcc['name'] );
				}

				$temp->addBCC('<EMAIL>');

				// Contenu
				$temp->isHTML( true );
				$temp->Subject = $this->subject;
				$temp->Body = $this->html;
				$temp->AltBody = $this->text;

				// Pièce(s) jointe(s)
				if( count($this->attachments) > 0 ){
					foreach( $this->attachments as  $one_file ){
						$temp->addAttachment( $one_file['filename'], $one_file['name'] );
					}
				}

				if( !$temp->send() ){
					throw new Exception('Envoi PHPMailer impossible');
				}
			}catch(Exception $e){
				error_log('Erreur lors de l\'envoi d\'un mail via PHPMailer => '.$e->getMessage());
				error_log(print_r($cfg_smtp, true));
				return false;
			}

			return true;
		}

		if( !self::control_email($this->from) ) {
			return false;
		}
		//$this->html = preg_replace( '/(http:\/\/[^\s])/', '<a href="\1">\1</a>', $this->html );
		// Compose le corps du message
		$boundary = md5(uniqid(time()));
		$body = "This is a multipart message in mime format.\n";
		if( sizeof($this->attachments)==0 ){

			$ctype = 'alternative';
			if( $this->text!='' ){
				$body .= "--" . $boundary . "\n";
				$body .= "Content-Type: text/plain; charset=utf-8\n" .
						"Content-Transfer-Encoding: quoted-printable\n" .
						"\n" . $this->quoted_printable_encode($this->text) . "\n";
			}
			if( $this->html!='' ){
				$body .= "--" . $boundary . "\n";
				$body .= "Content-Type: text/html; charset=utf-8\n" .
						"Content-Transfer-Encoding: quoted-printable\n" .
						"\n" . $this->quoted_printable_encode($this->html) . "\n";
			}

		}else{

			$ctype = 'mixed';
			$boundary2 = md5(uniqid(time()));

			$body .= "--" . $boundary . "\n";

			$body .= "Content-Type: multipart/alternative;\n";
 			$body .= " boundary=\"".$boundary2."\"\n\n\n";

			if( $this->text!='' ){
				$body .= "--" . $boundary2 . "\n";
				$body .= "Content-Type: text/plain; charset=utf-8\n" .
						"Content-Transfer-Encoding: quoted-printable\n" .
						"\n" . $this->quoted_printable_encode($this->text) . "\n";
			}
			if( $this->html!='' ){
				$body .= "--" . $boundary2 . "\n";
				$body .= "Content-Type: text/html; charset=utf-8\n" .
						"Content-Transfer-Encoding: quoted-printable\n" .
						"\n" . $this->quoted_printable_encode($this->html) . "\n";
			}
			$body .= "\n--".$boundary2."--\n";

			for( $i=0; $i<sizeof($this->attachments); $i++ ){
				$filename = $this->attachments[$i];
				$body .= "--" . $boundary . "\n";
				$body .="Content-Type: application/octet-stream; name=\"".basename($filename['name'])."\"\n" .
						"Content-Transfer-Encoding: base64\n" .
						"Content-Disposition: inline;\n" .
						" filename=\"".basename($filename['name'])."\"\n" .
						"\n" .
						chunk_split( base64_encode(file_get_contents($filename['filename'])), 68, "\n");
			}

		}
		if( trim($this->replyTo) != '' ){
			if( !self::control_email($this->replyTo) ){
				return false;
			};

			if( !self::email_is_valid_for_maquettes($this->replyTo) ){
				$this->replyTo = '';
			}else{
				$this->replyTo = $this->quoted_printable_encode_email( $this->replyTo );
			}
		}

		$body .= "\n--$boundary--\n";

		// Encode l'émetteur pour protéger les accents
		$encoded_from = $this->quoted_printable_encode_email($this->from, true);

		// Encode les cc
		$encoded_cc = array();
		foreach( $this->cc as $email ){
			if( !self::email_is_valid_for_maquettes($email) ){
				continue;
			}

			$encoded_cc[] = $this->quoted_printable_encode_email($email);
		}

		// Ajout <NAME_EMAIL> à tous les mails envoyés
		if (isset($config['email_env_context']) && $config['email_env_context'] == 'production') {
			$this->bcc[] = '<EMAIL>';
		}

		// Encode les bcc
		$encoded_bcc = array();
		foreach( $this->bcc as $email ){
			if( !self::email_is_valid_for_maquettes($email) ){
				continue;
			}

			$encoded_bcc[] = $this->quoted_printable_encode_email($email);
		}

		// Compose les entêtes
		$headers =  'From: '.$encoded_from."\n" .
					'Return-Path: '.$this->return_path."\n" .
					"MIME-Version: 1.0\n" .
					"Content-Type: multipart/$ctype;\n" .
					" boundary=\"$boundary\"\n";
		if( sizeof($this->cc) )
			$headers .= 'Cc: '.implode(', ',$encoded_cc)."\n";
		if( sizeof($this->bcc) )
			$headers .= 'Bcc: '.implode(', ',$encoded_bcc)."\n";

		if( trim($this->replyTo) != '' ){
			$headers .= 'Reply-To: '.$this->replyTo."\n";
		}

		// Encode le sujet pour protéger les accents
		$encoded_subject = $this->quoted_printable_encode_subject( $this->subject ); //'=?utf-8?Q?'.str_replace(' ','_',$this->quoted_printable_encode( $this->subject )).'?=';

		// Encode les destinataires pour protéger les accents
		$encoded_to = array();
		foreach( $this->to as $email ){
			if( !self::email_is_valid_for_maquettes($email) || !self::control_email($email) ){
				continue;
			}

			$encoded_to[] = $this->quoted_printable_encode_email($email);
		}

		if( sizeof($encoded_to)!=sizeof($this->to) ){
			return false;
		}


		$return_path = isset($config['wst_id']) ? cfg_overrides_get_value( 'email_return_path', $config['wst_id'] ) : '';
		if( trim($return_path) == '' ){
			$return_path = 'default';
		}

		if( !in_array($return_path, array('default', 'from')) ){
			if( !isemail($return_path) ){
				$return_path = 'default';
			}
		}

		$additional_parameters = '';
		switch( $return_path ){
			case 'default' : {
				$additional_parameters = '';
				break;
			}
			case 'from' : {
				$additional_parameters = '-f'.$this->return_path;
				break;
			}
			default : {
				if( trim($return_path) != '' && isemail($return_path) ){
					$additional_parameters = '-f'.$this->quoted_printable_encode_email( $return_path );
				}
				break;
			}
		}

		return mail(
			implode(', ',$encoded_to),
			$encoded_subject,
			$body,
			$headers,
			$additional_parameters
		);

	}

	/// \privatesection
	/**	Cette méthode encode le texte fournit en argument à l'aide de l'encodage quoted_printable.
	 *	Cette méthode est utilisée en interne pour la compatibilité avec les serveurs de messagerie.
	 *	@param string $str La chaîne de caractère à encoder
	 *	@return string La chaîne de caractère, encodée au format "quoted printable"
	 */
	function quoted_printable_encode( $str ) {
		$str = preg_replace( '/[^\x21-\x3C\x3E-\x7E\x09\x20]/e', 'sprintf( "=%02X", ord ( "$0" ) ) ;', $str );
		preg_match_all( '/.{1,73}([^=]{0,3})?/', $str, $matches );
		return implode( '=' . "\n", $matches[0] );
	}

	/**	Encode une adresse email à l'aide de l'algorithme quoted_printable pour protéger les caractères accentués.
	 *	L'encodage n'a lieu que s'il est réellement nécessaire.
	 *	@param string $email L'adresse email à encoder
	 *	@param boolean $is_from Définie si l'on encode le from ou non
	 *	@return string L'adresse email, encodée au format "quoted printable" si cet encodage est nécessaire
	 */
	function quoted_printable_encode_email( $email, $is_from=false ){

		$return_path = $email;
		if( preg_match( '/^([^\<]+) \<([^\>]+)\>$/', $email, $matches ) ){
			$should_encode = false;
			for( $i=0; $i<strlen($matches[1]) && !$should_encode; $i++ ){
				if (ord(substr($matches[1],$i,1))>127) {
					$should_encode = true;
					break;
				}
			}

			if( $should_encode ){
				$matches[1] = '"=?utf-8?Q?'.str_replace(' ','_',$this->quoted_printable_encode($matches[1])).'?="';
			}

			$email = $matches[1].' <'.$matches[2].'>';

			$return_path = $matches[2];
		}

		if ($is_from) {
			$this->return_path = $return_path;
		}

		return $email;
	}

	/**	Cette méthode est chargée d'encoder le sujet du message, au format quoted_printable
	 *	@param string $subject Le sujet du message
	 *	@return string Le sujet du message, éventuellement encodé au format "quoted printable"
	 */
	function quoted_printable_encode_subject( $subject ){

		// L'encodage n'est nécessaire que si des caractères non ascii 7bit sont présents
		if( preg_match( '/[^\x21-\x3C\x3E-\x7E\x09\x20]/e', $subject ) ){

			// Encode les caractères accentués
			$subject = preg_replace( '/[^\x21-\x3C\x3E-\x7E\x09\x20]/e', 'sprintf( "=%02X", ord ( "$0" ) ) ;', $subject );
			$subject = str_replace( '.', '=2E', $subject );
			$subject = str_replace( ' ', '_', $subject );

			$subject = '=?utf-8?Q?'.$subject."?=";

		}
		return $subject;
	}

	/** Cette fonction permet de controler si une adresse mail est valide. Ne tient pas compte des "Nom Prenom" pouvant être présents.
	 *	@param string $email Obligatoire, adresse mail à vérifiée
	 *	@return bool true si elle est valide, false dans le cas contraire
	 */
	public static function control_email( $email ){
		global $config;

		if( !trim($email) ){
			return false;
		}

		$pos_start = strpos( $email, '<' );
		if( $pos_start!==false ){
			$email = substr( $email, $pos_start+1 );
		}

		$pos_stop = strpos( $email, '>' );
		if( $pos_start!==false ){
			$email = substr( $email, 0, $pos_stop );
		}

		// Contrôle que l'adresse mail n'est pas une adresse créée automatiquement par le moteur
		// C-a-d : <EMAIL> ou REFCLIENT@[config:sync_sage_empty_email_parttern]
		$sql = '
			select 1
			from gu_users
			where usr_tnt_id = '.$config['tnt_id'].'
				and usr_date_deleted is null
				and ifnull(usr_ref, "") != ""
				and (
					lower(usr_email) = lower( concat(usr_ref, "@yuto.com") )
		';

		if( isset($config['sync_sage_empty_email_parttern']) && trim($config['sync_sage_empty_email_parttern']) != ''){
			$pattern = str_replace( ['*'], '', $config['sync_sage_empty_email_parttern'] );

			if( trim($pattern) != '@yuto.com' ){
				$sql .= ' or lower(usr_email) = lower( concat(usr_ref, "'.addslashes( $pattern ).'") )';
			}
		}

		$sql .= ' )
				and lower(usr_email) = lower("'.addslashes( $email ).'")
			limit 1
		';

		$is_auto_email = ria_mysql_query( $sql );
		if( $is_auto_email && ria_mysql_num_rows($is_auto_email) ){
			return false;
		}

		return gu_valid_email( $email );
	}

	/** Cette fonction permet de bloquer tout envoi de mail à un destinataire non administrateur en maquette.
	 *	@param string $email Obligatoire, adresse email
	 *	@return bool True si le destinataire est valide à la réception du message, False dans le cas contraire
	 */
	public static function email_is_valid_for_maquettes( $email ){
		if( trim($email) == '' ){
			return false;
		}

		if( strstr($email, '@riastudio.fr') || strstr($email, '@kontinuum.fr') || strstr($email, '@yuto.com') || strstr($email, '@fr') || strstr($email, '<EMAIL>') || strstr($email, '<EMAIL>') || strstr($email, '@allianceautomotive.fr') || strstr($email, '@allianceautomotive.com') ){
			return true;
		}

		global $config;

		if( isset($config['email_exclude_pattern']) && $config['email_exclude_pattern'] && preg_match($config['email_exclude_pattern'], $email) ){
			return false;
		}

		if( !isset($config['email_env_context']) ){
			return false;
		}

		if( $config['email_env_context'] == 'production' ){
			return true;
		}

		// Recherche un compte administrateur
		$r_admin = gu_users_get( 0, $email, '', PRF_ADMIN );
		if( !$r_admin || !ria_mysql_num_rows($r_admin) ){
			return false;
		}

		$admin = ria_mysql_fetch_assoc( $r_admin );
		if( !strstr($admin['email'], '@riastudio.fr') && !strstr($admin['email'], '@kontinuum.fr') && !strstr($admin['email'], '@yuto.com') ){
			return false;
		}

		return true;
	}
}



/// @}



<?php

/**	\defgroup strings Fonctions de chaînes de caractères
 *	\ingroup system
 *	Ces fonctions sont utilisées pour les traitements de chaînes de caractères. Il s'agit de fonctions utilitaires.
 *	La fonction strtoupper2 provient de la documentation en ligne de php.
 *
 * @{
 */

/// \private
/// Mots clés non significatifs pouvant être ignorés pour le référencement par les moteurs de recherche interne (lors de l'indexation)
/// Attention, seul les mots clés français sont traités... Il faudrait que cette constante s'adapte à la locale à indexer.
define( 'STR_STOPWORDS', strtoupper(',a,alors,au,aux,aucun,aussi,autre,autres,avant,avec,avoir,bon,car,ce,cela,celui,ces,cette,ceux,chaque,chez,ci,comme,comment,dans,de,des,du,dedans,dehors,depuis,devrait,doit,donc,droite,debut,elle,elles,en,encore,entre,est,et,eu,fait,faites,fois,font,grace,hors,ici,il,ils,je,juste,l,la,le,les,leur,la,ma,mais,mes,moins,mon,mot,meme,ni,nommes,notre,nos,nous,ou,par,parce,parceque,pas,peut,peu,plupart,pour,propose,quand,que,quel,quelle,quelles,quels,qui,sa,sans,seront,selon,ses,si,sien,son,sont,sous,soyez,sujet,sur,ta,tandis,tellement,telles,tels,tes,ton,tous,tout,toute,toutes,trop,tres,tu,trouverez,un,une,voient,vont,vos,votre,vous,vu,ca,etaient,etions,etre,apres,etc,') );

/// \private
define( 'LC_CHARS', 'àáâãäåæçèéêëìíîïðñòóôõöœøšùúûüýž' );

/** Cette fonction permet de supprimer les accents d'une chaine de caractères
 * 	@param string $str Obligatoire, chaine oû les accents seront remplacés par leur caractères sans accent
 *	@param string $charset Optionnel, par défaut, le charset utilisé est UTF-8
 *	@return string Retourne la chaine sans accent
 */
function str_remove_accents( $str, $charset='utf-8' ){
	$str = htmlentities2($str);

	$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
	$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'

	$ar_search  = array( 'à','á','â','ã','ä','ç','è','é','ê','ë','ì','í','î','ï','ñ','ò','ó','ô','õ','ö','ù','ú','û','ü','ý','ÿ','À','Á','Â','Ã','Ä','Ç','È','É','Ê','Ë','Ì','Í','Î','Ï','Ñ','Ò','Ó','Ô','Õ','Ö','Ù','Ú','Û','Ü','Ý','Å' );
	$ar_replace = array( 'a','a','a','a','a','c','e','e','e','e','i','i','i','i','n','o','o','o','o','o','u','u','u','u','y','y','A','A','A','A','A','C','E','E','E','E','I','I','I','I','N','O','O','O','O','O','U','U','U','U','Y','A' );
	$str = str_replace( $ar_search, $ar_replace, $str);

	return $str;
}

/** Cette fonction permet de remplacer les caractères spéciaux par leur équivalent HTML
 *	@param string $str La chaine de caractère à modifier.
 *	@return string Retourne la chaine de caractères passée en paramètre avec les codes HTML à la place des caractères spéciaux
 */
function htmlentities2( $str ){
	$c_acct = array( "à","á","â","ã","ä","å","æ","ç","è","é","ê","ë","ì","í","î","ï","ð","ñ","ò","ó","ô","õ","ö","œ","ø","š","ù","ú","û","ü","ý","ž","É","È","Å" );
	$c_html = array( "&agrave;","&aacute;","&acirc;","&atilde;","&auml;","&aring;","&aelig;","&ccedil;","&egrave;","&eacute;","&ecirc;","&euml;","&igrave;","&iacute;","&icirc;","&iuml;","&eth;","&ntilde;","&ograve;","&oacute;","&ocirc;","&otilde;","&ouml;","&oelig;","&Oslash;","&scaron;","&ugrave;","&uacute;","&ucirc;","&uuml;","&yacute;","z","E","E", "&Aring;" );
	return str_replace( $c_acct, $c_html, $str );
}

/** Cette fonction permet de remplacer les caractères HTML par leurs caractères spéciaux équivalent
 *	@param string $str La chaine de caractère à modifier.
 *	@return string Retourne la chaine de caractères passée en paramètre avec les caractères spéciaux à la place des entités HTML
 */
function html_entity_decode2( $str ){
	$c_acct = array( "œ", "à","á","â","ã","ä","å","æ","ç","è","é","ê","ë","ì","í","î","ï","ð","ñ","ò","ó","ô","õ","ö","	","ø","š","ù","ú","û","ü","ý","'","Ω","•","°","Å" );
	$c_html = array( "&oelig;", "&agrave;","&aacute;","&acirc;","&atilde;","&auml;","&aring;","&aelig;","&ccedil;","&egrave;","&eacute;","&ecirc;","&euml;","&igrave;","&iacute;","&icirc;","&iuml;","&eth;","&ntilde;","&ograve;","&oacute;","&ocirc;","&otilde;","&ouml;","&oelig;","&Oslash;","&scaron;","&ugrave;","&uacute;","&ucirc;","&uuml;","&yacute;","&rsquo;","&Omega;","&bull;","&deg;", "&Aring;" );
	return str_replace( $c_html, $c_acct, $str );
}

/** Cette fonction permet de retourner un texte brut sur un contenu édité par TinyMCE.
 *	@param string $str Obligatoire, la chaîne de caractère à modifier.
 *	@param bool $line_break Facultatif, indique s'il faut conserver les retours à la ligne (true) ou bien si ceux-ci sont transformés en espaces simples (false, valeur par défaut)
 *	@return string Retourne le texte brut de la chaine passée en paramètre
 */
function html_revert_wysiwyg( $str, $line_break=false ){
	$str = str_replace( array('<br>', '<br />', '<br/>'), ($line_break ? "\n" : ' '), $str );
	$str = html_strip_tags( $str );
	$str = str_replace( array('&nbsp;', '&amp;', "\r\n", "\n"), array(' ', '&', ($line_break ? "\r\n" : ''), ($line_break ? "\n" : ' ')), $str );

	if( !$line_break ){
		$str = preg_replace( "/\\x0|[\x01-\x1f]/U", "", $str );
	}

	$str = preg_replace( '/[ ]+/i', ' ', $str );

	return html_entity_decode2( $str );
}

/** Cette fonction permet de retourner un texte brut sur un contenu édité par TinyMCE.
 *	@param string $str La chaine de caractère à modifier.
 *	@param bool $line_break Facultatif, indique s'il faut conserver les retours à la ligne (true) ou bien si ceux-ci sont transformés en espaces simples (false, valeur par défaut)
 *	@return string Retourne le texte brut de la chaine passée en paramètre
 *	@deprecated Cette fonction est mal nommée (car What You See Is What You Get). Utiliser html_revert_wysiwyg à la place.
 *		!!! Cette fonction est utilisée dans de nombreux sites clients, ne pas supprimer !!!
 */
function html_revert_riawisiwig( $str, $line_break=false ){
	return html_revert_wysiwyg( $str, $line_break );
}

/**	Cette fonction est à utiliser en remplacement de la fonction PHP native strip_tags. La problématique posée par strip_tags est qu'elle remplace les balises par "chaîne vide" :
 *	deux mots séparés par une balise peuvent donc se retrouver collés en sortie de fonction...
 *	Ceci nous pose des soucis à plusieurs endroits : indexation par le moteur de recherche des contenus HTML, exportation au format texte de contenu HTML.
 *	Il est donc recommandé d'utiliser cette alternative en remplacement.
 *	Retours à la ligne et tabulation sont préservés pour que la mise en forme de la version texte soit cohérente (ces éléments de mise en forme sont porteurs de sens).
 *	@param string $str Obligatoire, chaîne de caractère dont les balises HTML doivent être supprimées
 *	@return string la chaîne de caractère nettoyée de ses balises HTML
 */
function html_strip_tags( $str ){
	// Supprime les balises et les remplace par des espaces
	$str = preg_replace ( '/<[^>]*>/', ' ', $str );
	// Supprime les espaces consécutifs multiples
	$str = preg_replace('/ {2,}/', ' ', $str );
	// Supprime les espaces de début et de fin de chaîne, et retourne le résultat
	return trim( $str );
}

/**	Cette fonction est utilisée pour déterminer si un caractère est une voyelle.
 *	Elle est à employer pour affiner certains affichages dynamiques (ex: utilisation de "de" ou "d'") en respectant mieux l'orthographe.
 *	Les caractères accentués sont supportés.
 *	@param string $char Le caractère à tester. Si $char contient une chaîne, seul le premier caractère sera testé.
 *	@return bool true si $char est une voyelle, false dans le cas contraire
 */
function is_vowel( $char ){
	$char = substr($char,0,1);
	if( strpos('AEIOUY',$char)!==false ){
		return true;
	}
	if( strpos(LC_CHARS,$char)!==false ){
		return true;
	}
	if( strpos('aeiouy',$char)!==false ){
		return true;
	}
	return false;
}

/** Cette fonction est utilisée pour remplacer la fonction php strtoupper.
 *	Elle permet de passer une chaîne de caractères en majuscule. Les caractères accentués
 *	se voient remplacer par leur équivalent non accentué, ce qui n'est pas effectué par la fonction strtoupper.
 *	@param string $str Chaîne de caractères à passer en majuscules
 *	@return string la chaîne de caractères, en majuscules.
 */
function strtoupper2( $str ){
	if( !is_string($str) ){
		return $str;
	}

	return mb_strtoupper(
		str_remove_accents( $str ), 'UTF-8'
	);
}

/** Cette fonction est utilisée pour remplacer la fonction php strtolower.
 *	Elle permet de passer une chaîne de caractères en minuscules. Les caractères accentués
 *	se voient remplacer par leur équivalent non accentué, ce qui n'est pas effectué par la fonction strtolower.
 *	@param string $str Chaîne de caractères à passer en minuscule
 *	@return string la chaîne de caractères, en minuscule et sans caractères accentués.
 */
function strtolower2( $str ){
	return mb_strtolower(
		str_remove_accents( mb_strtolower($str , 'UTF-8'), 'UTF-8' )
	);
}

/** Détermine si la chaîne de caractères passée en paramètre correspond à un code postal (France Métropolitaine).
 *	@param string $str Chaîne de caractères à tester
 *	@return bool true si la chaîne correspond à un code postal, false dans le cas contraire
 */
function iszipcode( $str ){
	return preg_match( '/^([0-9]{5})$/', $str );
}

/**	Détermine si la chaîne de caractères passée en paramètre correspond à une adresse email.
 *	@param string $str Chaîne de caractères à tester
 *	@return bool true si la chaîne contient une adresse email valide (correctement formée)
 *	@return bool false si la chaîne ne contient pas une adresse email valide
 */
function isemail( $str ){
	if( !is_string($str) ){
		return false;
	}

	return preg_match( '/^[a-z0-9\+\.\-\_]+@[a-z0-9\.\-\_]+\.[a-z]+$/i', $str );
}

/** Détermine si la chaîne de caratères passée en paramètre correspond à un numéro de téléphone.
 *	Cette fonction vérifie simplement qu'une fois tous les caractères hors numérique retiré, il reste quelque chose.
 *	@param string $str Obligatoire, chaîne de caractères à tester
 *	@return bool True si la chaîne corresponds à un numéro de téléphone, False dans le cas contraire
 */
function isphone( $str ){
	if( trim($str)=='' ){
		return false;
	}
	$str = preg_replace( '/[^0-9]/', '', $str );
	return trim($str)!='';
}

/** Détermine si la chaine de caractère passée en paramètre correspond à une date au format jj/mm/aaaa - aaaa/mm/jj - aaaa-mm-jj
 *	@param string $str Chaîne de caractère à tester.
 *	@return bool true si la chaîne contient bien une date
 *	@return bool false si la chaîne ne contient pas de date
 */
function isdate( $str ){
	if( !$str ){
		return false;
	}
	$date_fr = preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $str );
	$date_en1 = preg_match( '/^[0-9]{4}\/[0-9]{1,2}\/[0-9]{1,2}$/', $str );
	$date_en2 = preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/', $str );
	return $date_fr || $date_en1 || $date_en2;
}

/** Détermine si la chaine de caractère passée en paramètre correspond à une date au format jj/mm/aaaa hh:mm:ss
 *	@param string $str Chaîne de caractère à tester.
 *	@return bool true si la chaîne contient une date au format jj/mm/aaaa hh:mm:ss.
 *	@return bool false si la chaîne ne contient pas de date au format jj/mm/aaaa hh:mm:ss.
 */
function isdateheure( $str ){
	if( !$str ){
		return false;
	}

	if( isdate($str) ){
		return true;
	}

	$date_fr = preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}[ ]{1}[0-9]{1,2}:[0-9]{2}(:[0-9]{2})?$/', $str );
	$date_en1 = preg_match( '/^[0-9]{4}\/[0-9]{1,2}\/[0-9]{1,2}[ ]{1}[0-9]{1,2}:[0-9]{2}(:[0-9]{2})?$/', $str );
	$date_en2 = preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}[ ]{1}[0-9]{1,2}:[0-9]{2}(:[0-9]{2})?$/', $str );
	$date_en3 = preg_match( '/^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$/', $str );	//  ex : 2011-10-24 15:16:00

	return $date_fr || $date_en1 || $date_en2 || $date_en3;
}

/**	Convertit une date au format français (jj/mm/aaaa) en date au format US (yyyy-mm-dd)
 *	@param string $str Chaîne de caractère contenant la date au format français.
 *	@return bool false si la chaîne ne correspond à aucune date au format français.
 *	@return string la date au format US.
 */
function dateparse( $str ){
	if( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $str ) ){
		return preg_replace( '/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/', '\3-\2-\1', $str );
	}elseif( preg_match( '/^[0-9]{4}\/[0-9]{1,2}\/[0-9]{1,2}$/', $str ) ){
		return str_replace( '/', '-', $str );
	}elseif( preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/', $str ) ){
		return $str;
	}else{
		return false;
	}
}

/**	Convertit une date au format français (jj/mm/aaaa ou jj/mm/aaaa hh:mm:ss) en date au format US (yyyy-mm-dd hh:mm:ss)
 *	@param string $str Chaîne de caractère contenant la date au format français.
 *	@return bool false si la chaîne ne correspond à aucune date au format français.
 *	@return string la date au format US.
 */
function dateheureparse( $str ){
	$temp = dateparse($str);
	if( $temp!==false ){
		return $temp;
	}

	$str = str_replace( 'à', '', $str );
	$str = preg_replace( '/  /', ' ', $str );

	if( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $str ) || preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}[ ]{1}[0-9]{1,2}:[0-9]{2}$/', $str ) ){
		return preg_replace( '/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})([ ]{1})([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1\4\5:\6'.':00', $str );
	}elseif( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}[ ]{1}[0-9]{1,2}:[0-9]{2}:[0-9]{2}$/', $str ) ){
		return preg_replace( '/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})([ ]{1})([0-9]{1,2}):([0-9]{2}):([0-9]{2})$/', '\3-\2-\1\4\5:\6:\7', $str );
	}elseif( preg_match( '/^[0-9]{4}\/[0-9]{1,2}\/[0-9]{1,2}$/', $str ) || preg_match( '/^[0-9]{4}\/[0-9]{1,2}\/[0-9]{1,2}[ ]{1}[0-9]{1,2}:[0-9]{2}$/', $str ) ){
		return str_replace( '/', '-', $str );
	}elseif( preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/', $str ) || preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}[ ]{1}[0-9]{1,2}:[0-9]{2}$/', $str ) || preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}[ ]{1}[0-9]{1,2}:[0-9]{2}:[0-9]{2}$/', $str ) ){
		return $str;
	}else{
		return false;
	}
}

/** Cette fonction permet de vérifier si une chaine de caractères corresponds à une heure valable (de 00:00 à 23:59).
 *	@param string $str Obligatoire, chaine de caractère à vérifier
 *	@return bool True si la chaine corresponds à une heure valable, False dans le cas contraire
 */
function ishour( $str ){
	if( trim($str)=='' || !preg_match('/^[0-9]{1,2}:[0-9]{2}$/', $str) ){
		return false;
	}

	$hour = explode( ':', $str );

	if( sizeof($hour)!=2 ){
		return false;
	}
	if( !is_numeric($hour[0]) || $hour[0]<0 || $hour[0]>23 ){
		return false;
	}
	if( !is_numeric($hour[1]) || $hour[1]<0 || $hour[1]>59 ){
		return false;
	}

	return true;
}

/** Convertit une date américaine en date française yyyy-mm-dd[ hh:mm[:ss]] => 'jj/mm/aaaa[ hh:mm[:ss]]
 *	@param string $date Obligatoire, date au format américaine
 *	@param bool $time Facultatif, détermine si le format de l'heure est controlé (True par défaut)
 *	@return string Date au format français, false si date non reconnue
 */
function dateheureunparse( $date, $time=true ){
	if( !preg_match('#^([0-9]+)-([0-9]+)-([0-9]+)( ([0-9]+):([0-9]+)(:[0-9]+)?)?$#', $date, $match) ){
		return false;
	}
	$r = '';
	$r .= $match[3].'/'.$match[2].'/'.$match[1];
	$r = trim($r);
	if( $time && isset($match[4]) ){
		$r .= ' à'.$match[4];
	}
	return $r;
}
/**
 * Cette fonction permet de formater un DateTime en fonction d'une langue pour l'internationnalisation
 *
 * @param \DateTime $datetime Objet DateTime
 * @param bool $with_time Optionnel, si on veux les heures ou non
 * @param bool $full_date Optionnel, si on veux une traduction complète ou juste la date
 * @return string|boolean Retourne la date formatée, false si erreur.
 */
function dateTimeIntlFormat(\DateTime $datetime, $with_time=false, $full_date=false){
	$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'fr';
	$datetype = $full_date ? (IntlDateFormatter::LONG) : (IntlDateFormatter::MEDIUM);
	$timeType = $with_time ? (IntlDateFormatter::MEDIUM) : IntlDateFormatter::NONE;

	$fmt = new IntlDateFormatter( $lang, $datetype, $timeType );
	return $fmt->format($datetime);
}

/** Permet le formatage d'une date sous la forme dd/mm/yyyy en 01 janv. 2000 (ex) en prenant en compte la langue utilisée
 * 	@param string $date Chaine de caractère contenant la date au format français
 * 	@return string la date formatée
 */
function dateformat( $date ){
	$format='d/m/Y';
	$with_time = false;
	// test si il y a les heures
	if( preg_match("/\s[0-9]{1,2}:[0-9]{2}:[0-9]{2}$/", $date) ){
		$format='d/m/Y H:i:s';
		$with_time = true;
	}
	$datetime = \DateTime::createFromFormat( $format, $date );
	return dateTimeIntlFormat($datetime, $with_time);
}

/** Permet le formatage d'une date sous la forme dd/mm/yyy yen 01 Janvier 2000 (ex) en prenant en compte la langue utilisée
 * 	@param string $date Chaine de caractère contenant la date au format français
 * 	@return string la date formatée
*/
function dateformatfull( $date ){
	$format='d/m/Y';
	$with_time = false;
	// test si il y a les heures
	if( preg_match("/\s[0-9]{1,2}:[0-9]{2}:[0-9]{2}$/", $date) ){
		$format='d/m/Y H:i:s';
		$with_time = true;
	}
	$datetime = \DateTime::createFromFormat( $format, $date );
	return dateTimeIntlFormat($datetime, $with_time, true);
}

/** Cette fonction permet d'ajouter un mois à une date
 *  @param string $date Obligatoire, une date au format Y-m-d
 *  @return string La date donnée en argument + 1 mois ( exemple: 2019-01-31 devient 2019-02-28)
 */
function ria_date_add_month( $date ){
    $dt = new DateTime($date);

    $oldDay = $dt->format("d");
    $dt->add(new DateInterval("P1M"));
    $newDay = $dt->format("d");

    if( $oldDay != $newDay ){
		// Vérifie si le jour n'a pas changer, si c'est le cas, ca veut dire que l'on a sauté un mois
		// Soustrait des jours pour revenir au mois précédent
		$dt->sub(new DateInterval("P" . $newDay . "D"));
    }

    return $dt->format("Y-m-d");
}

/**	Cette fonction est à utiliser en remplacement de la fonction xhtmlentities. Certains caractères
 *	tels que ’ ne sont pas échappés par xhtmlentities, empêchant ainsi la validation xhtml.
 *	@param string $str Obligatoire, chaîne de caractères à échapper.
 *	@return string la chaîne de caractères dont les caractères spéciaux ont été remplacés par les entités html correspondantes.
 */
function xhtmlentities( $str ){
	$str = htmlentities($str, ENT_COMPAT, 'UTF-8');
	return str_replace( array('’','…','€'), array('&rsquo;','&hellip;','&euro;'), $str );
}

/**	Echappe les caractères spéciaux xml pour inclusion d'une chaîne dans une enfant ou un attribut d'un document xml.
 *	@param string $str Obligatoire, chaîne à échapper
 *	@return string la chaîne avec chaque caractère spécial remplacé par son entité xml équivalente
 */
function xmlentities( $str ){
	return str_replace(array('&','<','>','"','•','™','','','','',''),array('&amp;','&lt;','&gt;','&quot;','&#95;','&#99;', '', '', '', '', ''), $str );
}

/**	Echappe les caractères spéciaux xml pour inclusion d'une chaîne comme noeud texte d'un document xml.
 *	@param string $str Obligatoire, chaîne à échapper
 *	@return string la chaîne avec chaque caractère spécial remplacé par son entité xml équivalente
 */
function xmltextentities( $str ){
	$str = str_replace( '&', '&amp;', $str );
	return str_replace(array('<','>','•','™'),array('&lt;','&gt;','&#95;','&#99;'), $str );
}

/** Cette fonction permet la protection d'une chaîne de caractère destinée à être utilisée comme valeur d'attribut html ou xml.
 *	@param string $str Obligatoire, chaîne à échapper
 *	@return string la chaîne avec chaque caractère spécial remplacé par son entité xml équivalente
 */
function xmlattribute( $str ){
	return xmlentities( $str );
}

/**	Cette fonction est chargée de remplacer les caractères non standards notamment utilisés
 *	par Microsoft Word par leurs équivalents ASCII classiques.
 *	@param string $str Obligatoire, chaîne de caractère à nettoyer
 *	@return string la chaîne de caractère standardisée
 */
function sanitize( $str ){
	$str = str_replace( '', "", $str ); // attention le premier paramètre ne contient pas une chaine vide
	$str = str_replace( '\u2019', "'", $str );
	$str = str_replace( chr(146), "'", $str );
	$str = str_replace( chr(133), '...', $str );
	return $str;
}

/**	Cette fonction est chargée de filtrer les caractères non autorisés dans une adresse email.
 *	@param string $str Obligatoire, l'adresse email à nettoyer
 *	@return string l'adresse email nettoyée des caractères non autorisés
 */
function str_filter_email( $str ){
	$str = strtolower2(strtoupper2($str));
	return preg_replace( '/[^a-z0-9\.\-\_@]/', '', $str );
}

/**	Crée une url simplifiée à partir de la chaîne de caractère passée en paramètre (nom d'un objet).
 *	@param string $str Obligatoire, chaîne de caractère à utiliser pour le nom de la ressource
 *	@param string $lng Facultatif, langue de l'URL (fr par défaut)
 *	@param bool $with_underscore facutlatif, autorise les underscores (par défaut False)
 *	@return string Une chaîne de caractère utilisable comme url simplifiée
 */
function urlalias( $str, $lng='fr', $with_underscore=false ){
	// Supprime les caractères accentués et passe la chaîne en minuscules
	$str = str_remove_accents( $str );
	$str = strtolower( $str );
	if( !in_array($lng, array('ru')) ){
		if( $with_underscore ){
			$str = preg_replace( '/[^a-z0-9_]/', '-', $str );
		}else{
			$str = preg_replace( '/[^a-z0-9]/', '-', $str );
		}
	}

	// Simplifie la chaîne en supprimant les doubles -
	$str = preg_replace( '/-{2,}/', '-', $str );
	$str = preg_replace( '/^-/', '', $str );
	$str = preg_replace( '/-$/', '', $str );
	return $str;
}

/**	Deuxième version de urlalias, celle-ci autorise les / dans la chaine de caractères.
 *	@param string $str Chaîne de caractère à utiliser pour le nom de la ressource
 *	@return string une chaîne de caractère utilisable comme url simplifiée
 */
function urlalias2( $str ){
	if( substr($str, 0, 1)!='/' ){
		$str = '/'.$str;
	}

	// Supprime les caractères accentués et passe la chaîne en minuscules
	$str = strtolower2(strtoupper2($str));
	$str = preg_replace( '/[^a-z0-9\/]/', '-', $str );
	// Simplifie la chaîne en supprimant les doubles -
	$str = preg_replace( '/-{2,}/', '-', $str );
	$str = preg_replace( '/\/{2,}/', '/', $str );
	$str = preg_replace( '/^-/', '', $str );
	$str = preg_replace( '/-$/', '', $str );
	return $str;
}

/**	Cette fonction vérifie qu'une chaîne a été "urlaliassé" (fonction urlalias2)
 *	@param string $str Obligatoire, chaîne à vérifier
 *	@return bool True si la chaîne est correct, False sinon
 */
function isurlalias2( $str ){
	return preg_match('/[^a-z0-9-\/]/', $str) === 0;
}

/**	Cette fonction valide un numéro SIRET (14 chiffres).
 *	@param string $siret Obligatoire, chaîne de caractère à vérifier
 *	@param bool $checkSum Facultatif, permet de valider la somme des chiffres
 *	@return bool true si la chaîne correspond à un numéro SIRET valide
 *	@return bool false si la chaîne ne correspond à aucun numéro SIRET valide
 */
function validSIRET( $siret, $checkSum=false ){
	$siret = trim( $siret );
	$siret = preg_replace( '/\s|\./', '', $siret );

	$result = false;
	if( preg_match( '/^[0-9]{14}$/', $siret ) ){ // controle siret pour la france
		$result = true;
	}else if( preg_match( '/^MONACO[A-Z0-9]{8}$/', strtoupper($siret) ) ){ // controle siret pour monaco
		$result = true;
	}
	if( !$checkSum ){
		return $result;
	}

	if( empty( $siret ) || !preg_match( '/^[0-9]{14}$/', $siret ) ){
		return false;
	}

	// Contrôle du checksum
	$sum = 0;
	for( $i = 0; $i < 14; $i ++ ){
		if($i%2 == 0) {
			$tmp = $siret[$i]*2;
			$tmp = $tmp > 9 ? $tmp - 9 : $tmp;
		}
		else {
			$tmp= $siret[$i];
		}
		$sum += $tmp;
	}
	return $sum%10 == 0;
}

/** Met un numéro SIRET au format 000 000 000 00000
 *	@param string $siret Obligatoire, numéro siret exprimé de façon compactée
 *	@return string Le numéro Siret, au format 000 000 000 00000
 */
function formatSiret( $siret ){

	$siret = trim($siret);
	$siret = str_replace( ' ', '', $siret );
	if( $siret!='' ){
		$str1 = substr( $siret, 0, 3 );
		$str2 = substr( $siret, 3, 3 );
		$str3 = substr( $siret, 6, 3 );
		$str4 = substr( $siret, 9 );
		$siret = $str1.' '.$str2.' '.$str3.' '.$str4;
	}
	return $siret;
}

/**	Recherche un motif dans une chaîne de caractère, et retourne le nombre d'occurences trouvées.
 *	@param string $str Obligatoire, la chaîne de caractère dans laquelle la recherche à lieu
 *	@param string $search Obligatoire, la chaîne de caractère à rechercher
 *	@return int le nombre d'occurences de \c $search contenues dans \c $str
 */
function strcount( $str, $search ){
	$count = 0;
	while( ($p = strpos($str,$search))!==false ){
		$str = substr($str,$p+strlen($search));
		$count++;
	}
	return $count;
}

/**	Retourne une partie des lignes d'une chaîne de caractère multiligne.
 *	Par défaut, seule la première ligne est retournée.
 *	@param string $str La chaîne de caractère à manipuler
 *	@param int $start Facultatif, ligne de départ
 *	@param int $length Facultatif, nombre de lignes à retourner
 *	@return string les lignes contenues entre \c $start et \c $start + \c $length
 */
function strlines( $str, $start=0, $length=1 ){
	$astr = explode( "\n", $str );
	return implode( "\n", array_slice( $astr, $start, $length ) );
}

/** Permet de trier un tableau multidimentionnel plus facilement
 * 	@param array $array Obligatoire tableau a trier
 *	@param array $cols Obligatoire, tri requis
 *			ex: array( 'col1' => SORT_ASC, 'col2' => SORT_DESC )
 *	@return array le tableau trié
 */
function array_msort( $array, $cols ){
	if( !is_array($array) || !count($array) ){
		return $array;
	}

	$colarr = array();
	foreach( $cols as $col => $order ){
		$colarr[$col] = array();
		foreach( $array as $k => $row ){
			if( !isset($row[$col]) ){
				return $array;
			}

			$colarr[$col]['_'.$k] = strtolower($row[$col]);
		}
	}
	$params = array();
	foreach( $cols as $col => $order ){
		$params[] =& $colarr[$col];
		$params = array_merge( $params, (array)$order );
	}
	call_user_func_array('array_multisort', $params);
	$ret = array();
	$keys = array();
	$first = true;
	foreach( $colarr as $col => $arr ){
		foreach( $arr as $k => $v ){
			if( $first ){
				$keys[$k] = substr($k,1);
			}

			if( !array_key_exists($k, $keys) ){
				continue;
			}

			$k = $keys[$k];

			if( !isset($ret[$k]) ){
				if( !isset($array[$k]) ){
					return $array;
				}

				$ret[$k] = $array[$k];
			}

			if( !isset($array[$k][$col]) ){
				return $array;
			}

			$ret[$k][$col] = $array[$k][$col];
		}
		$first = false;
	}
	return $ret;

}

/** Cette fonction permet de récupérer une liste d'unités de mesure pour un type de mesure
 *	@param string $type les types acceptés sont :
 *		- dist = distance
 *		- weight = poid
 *		- garantie = garantie
 *		- time = Temps
 *
 *	@return array un tableau de valeur associative avec array('clé'=>'valeur') false si le type n'existe pas
 */
function ria_unites_get( $type ){

	$unites = array(
		'dist' => array(
			'm' => 'Mètre',
			'dm' => 'Décimètre',
			'cm' => 'Centimètre',
			'mm' => 'Millimètre'
		),
		'weight' => array(
			't' => 'Tonne',
			'kg' => 'Kilogramme',
			'hg' => 'Hectogramme',
			'g' => 'Gramme',
			'dg' => 'Décigramme',
			'cg' => 'Centigramme',
			'mg' => 'Milligramme'
		),
		'garantie' => array(
			'year' => 'Année',
			'month' => 'Mois',
			'week' =>'Semaine',
			'day' =>'Jour'
		),
		'time' => array(
			'year' => 'Année',
			'month' => 'Mois',
			'week' =>'Semaine',
			'day' =>'Jour',
			'hour' =>'Heure',
			'min' =>'Minute',
			's' =>'Seconde'
		)
	);

	if( !array_key_exists($type, $unites) ){
		return false;
	}

	return $unites[$type];
}

/* Cette fonction permet de comparer deux dates
 *	@param string $a Obligatoire, première date
 *	@param string $b Obligatoire, deuxième date
 *	@return bool Retourne false si la seconde date est antérieur à la première
 *	@return bool Retourne true dans les autre cas
 */
function cmp_date( $a, $b ){
	if( !isdateheure($a) ){
		return false;
	}
	if( !isdateheure($b) ){
		return false;
	}
	$a = strtotime($a);
	$b = strtotime($b);

	return $a>=$b;
}

/* Cette fonction permet de retourner un tableau contenant tous les jours fériés d'une année
 *	@param int $year Obligatoire, il s'agit de l'année pour laquelle on souhaite connaître les jours fériés
 *	@return array Retourne un tableau contenant tous les jours fériés, il est construit de cette façon : 'date' => 'fête', exemple : '25/12/2010' => 'Noël'
 *	@return array Retourne un tableau vide si l'année passée en paramètre n'est pas un entier à quatre chiffres
 */
function holidays( $year ){
	if( !is_numeric($year) ){
		return array();
	}

	// Date fériées fixe
	$holidays = array(
		$year.'-01-01' => 'Jour de l\'An',
		$year.'-05-01' => 'Fête du travail',
		$year.'-05-08' => 'Fête de la victoire de 1945',
		$year.'-07-14' => 'Fête national',
		$year.'-08-15' => 'Assomption',
		$year.'-11-01' => 'Toussaint',
		$year.'-12-25' => 'Noël'
	);

	// Pâques
	$paques = easter_date($year);
	$holidays[date('Y-m-d', $paques)] = 'Pâques';

	// Lundi de pâques
	$lpaques = mktime( 0, 0, 0, date('m', $paques), date('d', $paques) + 1, date('Y', $paques) );
	$holidays[date('Y-m-d', $lpaques)] = 'Lundi de pâques';

	// Ascension
	$asc = mktime( 0, 0, 0, date('m', $paques), date('d', $paques) + 39, date('Y', $paques) );
	$holidays[date('Y-m-d', $asc)] = 'Ascension';

	// Pentecôte
	$pentecote = mktime( 0, 0, 0, date('m', $paques), date('d', $paques) + 49, date('Y', $paques) );
	$holidays[date('Y-m-d', $pentecote)] = 'Pentecôte';

	// Lundi de pentecôte
	$lpentecote = mktime( 0, 0, 0, date('m', $paques), date('d', $paques) + 50, date('Y', $paques) );
	$holidays[date('Y-m-d', $lpentecote)] = 'Lundi de pentecôte';

	// Trie le tableau sur les dates des jours fériés
	uksort( $holidays, "cmp_date");

	// Retour le tableau des jours fériés pour l'année
	return $holidays;
}

/* Cette fonction retourne le texte français d'un mois
 *	@param int $id numéro du mois
 *	@return string Le mois en texte (français)
 *	@return string Une chaîne vide si le paramètre d'entrée est invalide
 */
function month_french( $id ){

	if( !is_numeric($id) || $id<1 || $id>12 ){
		return '';
	}
	$id = (int)$id;

	// Tableau contenant les mois
	$months = array(
		1 => 'Janvier',
		2 => 'Février',
		3 => 'Mars',
		4 => 'Avril',
		5 => 'Mai',
		6 => 'Juin',
		7 => 'Juillet',
		8 => 'Août',
		9 => 'Septembre',
		10 => 'Octobre',
		11 => 'Novembre',
		12 => 'Décembre'
	);

	return $months[ $id ];
}

/** Cette fonction retourne le texte français d'une journée.
 *	@param int $id Obligatoire, identifiant de la journée retournée par date('N' / 'w')
 *	@param string $format Optionnel, par défaut on se base sur le paramètre 'w' : 0 pour Dimanche à 6 pour Samedi, 'N' est aussi accepté : 1 pour Lundi à 7 pour Dimanche
 *	@return string Le texte français d'une journée
 */
function days_french( $id, $format='w' ){
	if( !is_numeric($id) || $id < 0 ){
		return '';
	}

	if( trim($format) == '' || !in_array($format, array('w', 'N')) ){
		return '';
	}

	if( $format == 'w' ){
		if( $id > 6 ){
			return '';
		}

		$days = array(
			0 => _('Dimanche'),
			1 => _('Lundi'),
			2 => _('Mardi'),
			3 => _('Mercredi'),
			4 => _('Jeudi'),
			5 => _('Vendredi'),
			6 => _('Samedi')
		);
	}else{
		if( $id < 1 || $id > 7 ){
			return '';
		}

		$days = array(
			1 => _('Lundi'),
			2 => _('Mardi'),
			3 => _('Mercredi'),
			4 => _('Jeudi'),
			5 => _('Vendredi'),
			6 => _('Samedi'),
			7 => _('Dimanche')
		);
	}

	return $days[ $id ];
}

/* Cette fonction permet de transformer un timestamp en date au format "Jour 01 Mai 2010".
 *
 * @param int $timestamp Obligatoire, Le timestamp de la date
 * @param string $format
 * @return string Retourne la date avec le format "Jour 01 Mai 2010"
 */
function dateformatcomplet( $timestamp, $format='l d f Y' ){
	// Tableau contenant les jours de la semaine.
	$days = array('Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi');

	$date = '';
	$str_ar = str_split($format);

	foreach( $str_ar as $one_s ){
		switch( $one_s ){
			case ' ':
				$date .= ' ';
				break;
			case '-':
				$date .= '-';
				break;
			// Jour de la semaine
			case 'l':
				$date .= $days[date('w', $timestamp)];
				break;
			// Numéro du jour
			case 'd':
				$d = date('j', $timestamp);
				$date .= ($d == 1 ? $d.'er' : $d);
				break;
			// Nom du mois, majuscule sur la première lettre
			case 'F':
				$date .= month_french(date('n', $timestamp));
				break;
			// Nom du mois, en minuscule
			case 'f':
				$date .= mb_strtolower(month_french(date('n', $timestamp)));
				break;
			// Année sur deux chiffres
			case 'y':
				$date .= ' '.date('y', $timestamp);
				break;
			// Année sur quatre chiffres
			case 'Y':
				$date .= ' '.date('Y', $timestamp);
				break;
		}
	}

	return $date;

}

/* Cette fonction permet de transformer une chaine de caractères en nombre entier
 *	@param $string Obligatoire, la chaine a transformer
 *	@return int Retourne l'entier contenu dans la chaîne
 */
function parseInt( $string ){

	$string = str_replace( ' ', '', $string );

	return (int)$string;
}

/* Cette fonction permet de transformer une chaîne de caractères en nombre entier
 *	@param string $string Obligatoire, la chaîne à transformer
 *	@return float Retourne le float contenu dans la chaîne
 */
function parseFloat( $string ){

	$string = str_replace( ' ', '', $string );
	$string = str_replace( ',', '.', $string );

	return (float)$string;
}

/** Cette fonction permet de passer la première lettre en majuscule et toutes les autres en minuscules
 *	@param string $str Obligatoire, chaine de caractère
 *	@return string Retourne la chaine de caractère avec la première lettre en majuscule
 */
function ucfirst2( $str ){
	return ucfirst( mb_strtolower( $str, 'UTF-8' ) );
}

/**	Cette fonction permet de récupérer le nombre de jours ouvrés (exclu : samedi, dimanche et jours fériés) dans un interval de temps et une date de début.
 *	@param $date Obligatoire, date à partir de laquelle le comptage est fait
 *	@param int $days Obligatoire, intervale de temps après la date de début (en jours)
 *	@return int Le nombre de jours ouvrés dans l'intervale
 *	@return bool False si l'un des paramètres est omis ou faux
 */
function get_date_with_openday( $date, $days ){
	if( !isdate($date) ){
		return false;
	}
	if( !is_numeric($days) && $days<=0 ){
		return false;
	}

	// jours fériés
	$holiday = array ( '1_1', '1_5', '8_5', '14_7', '15_8', '1_11', '11_11', '25_12' );

	// pâques
	$easter = easter_date((int) date('Y'), strtotime($date));
	$holiday[] = date('j_n',$easter);
	$holiday[] = date('j_n', $easter + (86400*39));
	$holiday[] = date('j_n', $easter + (86400*49));

	$i = 0;
	$end = 0;
	while( $i <= $days ){
		$times = strtotime($date)+(($i+1)*86400);
		if( in_array(date( 'w', $times ), array( 0, 6 )) || in_array(date( 'j_n', $times ), $holiday) ){
			$end += 86400;
		}
		$i++;
	}
	return date ('d/m/Y', strtotime($date) + ($days*86400) + $end);
}

/** Cette fonction permet de rajouter des jours ouvrés à une date en tenant compte des jours férié.
 * 	@param $time Obligatoire, timestamp de la date de début
 * 	@param int $days Obligatoire, nombre de jours à ajouter
 * 	@return Le timestamp de la date finale
 */
function ria_add_working_days($time, $days){
	if (!is_numeric($time) || $time <= 0) {
		return false;
	}

	if (!is_numeric($days) || $days <= 0) {
		return false;
	}

	$J = 24 * 60 * 60;

	// jours fériés
	$holidays = array('1_1', '1_5', '8_5', '14_7', '15_8', '1_11', '11_11', '25_12');
	// pâques
	$easter = easter_date((int)date('Y'), $time);

	$holidays[] = date('j_n', $easter); 			// Lundi de Pâques
	$holidays[] = date('j_n', $easter + $J * 39);	// Ascension
	$holidays[] = date('j_n', $easter + $J * 50);	// Pentecote

	$i = 0;
	while (true) {
		if (($i++) > 100) {
			break;
		}

		if ($days <= 0) {
			break;
		}

		$time += $J;

		$is_holiday = in_array(date('j_n', $time), $holidays);
		if (!in_array(date('w', $time), array('0', '6')) && !$is_holiday) {
			$days--;
		}
	}

	return $time;
}

/** Cette fonction permet de retourner le lien du fichier source d'une image après avoir échapé tous les caractères pouvant entrainer l'échec de la commande convert (création de miniature).
 *	@param string $source Obligatoire, lien vers le fichier source d'une image
 *	@return string le lien échappé vers le fichier source d'une image
 */
function addslashes_img_source( $source ){
	return str_replace( array(' ','(', ')', '&'), array('\ ','\(', '\)', '\&'), addslashes($source) );
}

/** Cette fonction permet de raccourcir les phrases sans couper les mots en plein milieu.
 *	@param string $text Obligatoire, Chaîne de caractère à couper
 *	@param int $length Obligatoire, Longeur de chaine désiré, en nombre de caractères
 *	@param string $last_caract Optionnel, caractère de fin dans le cas où la chaine passée en paramètre est trop grande (par défaut "...")
 *	@return string le paragraphe réduit, sans couper de mot
 */
function strcut( $text, $length, $last_caract=' ...' ){
	$text = strip_tags($text);
	if( strlen( $text ) <= $length ){
		return $text;
	}

	$text = substr($text,0,$length);
	$text = substr($text,0,strrpos($text,' '));
	$text = $text.( trim($last_caract) != '' ? $last_caract : '' );
	return $text;
}

/** Cette fonction permet de retourner une chaine spécifiquement pour l'export CSV des étiquettes Colissimo.
 *	@param string $str Obligatoire, information du champ
 *	@param int $length Obligatoire, longueur max du champ
 *	@param string $caract Optionnel, caractère de remplacement si la chaine est inférieure à la longueur max (par défaut : un espace)
 *	@param int $dir Optionnel, postionnement du caractère de remplacement, à droite ou à gauche (par défaut : STR_PAD_RIGHT)
 *	@return string La chaine traitée
 */
function strcut_colissimo( $str, $length, $caract=' ', $dir=STR_PAD_RIGHT ){
	$str = strcut( $str, $length, '' );
	return str_pad( $str, $length, $caract, $dir );
}

/** Cette fonction permet de vérifier qu'un RIB n'est pas un faux.
 *	@param int $cbank Obligatoire, code de la banque
 *	@param int $counter Obligatoire, code du guichet
 *	@param int $account Obligatoire, numéro du compte
 *	@param int $key Obligatoire, clé du RIB
 *	@return bool True s'il s'agit d'un vrai RIB, False dans le cas contraire
 */
function is_rib( $cbank, $counter, $account, $key ){
	$tabcompte = "";
	$len = strlen($account);
	if ($len != 11) {
			return false;
	}
	for ($i = 0; $i < $len; $i++) {
			$car = substr($account, $i, 1);
			if (!is_numeric($car)) {
				$c = ord($car) - (ord('A') - 1);
				$b = (($c + pow ( 2, ($c - 10) / 9 )) % 10) + (($c > 18 && $c < 25) ? 1 : 0);
				$tabcompte .= $b;
			}
			else {
				$tabcompte .= $car;
			}
	}
	$int = $cbank . $counter . $tabcompte . $key;
	return (strlen($int) >= 21 && my_bcmod($int, 97) == 0);
}

/** Cette fonction permet de savoir si une image est une gif animé.
 *	@param string $tmp_name Obligatoire, adresse physique du document
 *	@return bool True s'il s'agit d'une gif animé, False dans le cas contraire
 */
function is_gif_animated( $tmp_name ){
	return preg_match( '#(\x00\x21\xF9\x04.{4}\x00\x2C.*){2,}#s', file_get_contents($tmp_name) );
}

/** Cette fonction permet de véirifer qu'un IBAN (International Bank Account Number) n'est pas un faux.
 *	@param string $iban Obligatoire, International Bank Account Number
 *	@return bool True s'il s'agit d'un vrai International Bank Account Number, False dans le cas contraire
 */
function is_iban( $iban ){
	$charConversion = array("A" => "10","B" => "11","C" => "12","D" => "13","E" => "14","F" => "15","G" => "16","H" => "17","I" => "18","J" => "19","K" => "20","L" => "21","M" => "22","N" => "23","O" => "24","P" => "25","Q" => "26","R" => "27","S" => "28","T" => "29","U" => "30","V" => "31","W" => "32","X" => "33","Y" => "34","Z" => "35");

	// Déplacement des 4 premiers caractères vers la droite et conversion des caractères
	$tmpiban = strtr(substr($iban,4,strlen($iban)-4).substr($iban,0,4),$charConversion);
	$tmpiban = str_replace(' ', '', $tmpiban);

	// Calcul du Modulo 97 par la fonction bcmod et comparaison du reste à 1
	return (intval(my_bcmod($tmpiban,"97")) == 1);
}

/** Calcule le modulo de deux nombres. Cette fonction est un substitut pour bcmod (fait partie du module bcmath)
 *	@param string $x Le nombre à diviser
 *	@param string $y Le diviseur
 *	@return int Le modulo de $x par $y
 *	@see https://www.php.net/manual/fr/function.bcmod.php
 *	@todo Cette fonction n'a pas réellement sa place ici et ne respecte pas nos conventions de nommage
 */
function my_bcmod( $x, $y ){

	// how many numbers to take at once? carefull not to exceed (int)
	$take = 5;
	$mod = '';

	do{

		$a = (int)$mod.substr( $x, 0, $take );
		$x = substr( $x, $take );
		$mod = $a % $y;

    }while ( strlen($x) );

	return (int)$mod;
}

/**	Cette fonction protège les éléments d'un tableau contre une injection SQL, par exemple "AND colonne_sql IN ("'.implode('", "', $le_tableau).'").
 *	@param array $array Obligatoire, le tableau à protéger.
 *	@param bool $slashes_key Optionnel, détermine si les clés doivent être protégées. Par défaut, seules les valeurs le sont.
 *	@return array Le tableau protégé, l'ordre des éléments étant conservé. Si l'argument $array n'est pas un tableau, il est retourné inchangé.
 *	@todo Cette fonction n'a pas réellement sa place ici et ne respecte pas nos conventions de nommage
 */
function array_slashes( $array, $slashes_key=false ){
	if( !is_array($array) ){
		// pas un tableau : retour sans changement
		return $array;
	}

	$array_tmp = array();
	foreach( $array as $k => $elem ){
		if( $slashes_key ){
			$array_tmp[ addslashes($k) ] = addslashes($elem);
		}else{
			$array_tmp[ $k ] = addslashes($elem);
		}
	}

	return $array_tmp;
}

/** Cette fonction permet de contrôler un paramètre de type : identifiant ou tableau d'identifiants.
 *	@param int|array $ids_ar Obligatoire, identifiant ou tableau d'identifiants.
 *	@param bool $mandatory Optionnel, détermine si $ids_ar est à la base un paramètre optionnel, lui permettant les valeurs supplémentaires suivantes :
 *		- Tableau vide (les tableaux contenant 0 ne sont pas autorisés, sauf si $zero_is_id activé).
 *		- Entier égal à 0 (sera transformé en tableau vide, sauf si $zero_is_id activé : dans ce cas 0 sera dans le tableau final).
 *	@param bool $zero_is_id Optionnel, détermine si 0 est un identifiant valide (à différencier d'un paramètre optionnel). Exemple : catégorie comptable ("gu_accounting_categories").
 *	@param bool $fld_obj_ids Optionnel, si activé contrôle que l'entrée est un tableau de "COUNT_OBJ_ID" (actuellement 3) éléments.
 *
 *	@return array Un tableau d'identifiants vérifiés (potentiellement vide, mais uniquement dans le cas où $mandatory est à False).
 *	@return bool False si $ids_ar est invalide.
 */
function control_array_integer( $ids_ar, $mandatory=true, $zero_is_id=false, $fld_obj_ids=false ){
	if( $ids_ar === 0 && !$mandatory && $fld_obj_ids && $zero_is_id ){
		$ids_ar = array();
	}

	if( !is_array($ids_ar) ){
		if( !is_numeric($ids_ar) || $ids_ar<0 ){
			return false;
		}

		if( $mandatory && !$zero_is_id && !$ids_ar ){
			return false;
		}

		$ids_ar = ( $ids_ar || $zero_is_id ? array($ids_ar) : array() );
	}else{
		if( $mandatory && !sizeof($ids_ar) ){
			return false;
		}

		if( $fld_obj_ids && sizeof($ids_ar) > COUNT_OBJ_ID ){
			return false;
		}

		foreach( $ids_ar as $id ){
			if( !is_numeric($id) || $id<0 || ( !$zero_is_id && !$id ) ){
				return false;
			}
		}
	}

	return $ids_ar;
}

/**	Cette fonction génère le code SQL pour trier dans l'ordre des éléments d'un tableau donné.
 *	@param array $ar_id Obligatoire, le tableau d'éléments. Ne peut pas être vide.
 *	@param string $column_name Obligatoire, le nom de la colonne.
 *	@param string $sort Optionnel, direction du tri : croissant (asc) ou décroissant (desc).
 *	@param bool $if_error Optionnel, détermine ce qui est renvoyé en cas d'erreur (par défaut une chaîne vide).
 *	@param bool $not_in_ar_pos Optionnel, position de tri pour les éléments retournés par la requête et n'étant pas dans $ar_id (False place en dernier, -1 en premier, un autre nombre pour une position arbitraire).
 *
 *	@return string La clause SQL order by, $if_error en cas d'erreur.
 */
function sql_order_by_array( $ar_id, $column_name, $sort='asc', $if_error='', $not_in_ar_pos=false ){

	if( !is_array($ar_id) || !sizeof($ar_id) ){
		return $if_error;
	}

	if( $not_in_ar_pos !== false && !is_numeric($not_in_ar_pos) ){
		return $if_error;
	}

	$sort = strtolower(trim($sort)) == 'desc' ? 'desc' : 'asc';

	$query_orderby = 'case ';
	$i = 0;
	foreach( $ar_id as $current_id ){
		$query_orderby .= 'when '.$column_name.' = '.addslashes($current_id).' then '.$i.' ';
		$i++;
	}
	$query_orderby .= ' else '.( $not_in_ar_pos !== false ? $not_in_ar_pos : $i );
	$query_orderby .= ' end '.$sort;

	return $query_orderby;

}

/**	Cette fonction protège les caractères assimilés à des jokers MySQL contenus dans une chaîne (typiquement, avant une injection SQL tel que LIKE '%[la_chaine]%')
 *	@param string $base_str Obligatoire, la chaîne à protéger.
 *	@param bool $wildcard Optionnel, un caractère spécifique à protéger ("%" ou "_"), sinon les deux.
 *	@return string La chaîne protégée.
 */
function wildcard_slashes( $base_str, $wildcard=false ){
	if( $wildcard == '%' || $wildcard == '_' ){
		$wildcard = array($wildcard);
	}else{
		$wildcard = array('%', '_');
	}

	foreach( $wildcard as $w ){
		$base_str = str_replace($w, '\\'.$w, $base_str);
	}

	return $base_str;
}

/**	Cette fonction calcule la différence exacte entre deux dates.
 *	@param string $date_start Date de début.
 *	@param string $date_end Date de fin.
 *	@param $format Format de sortie (y, m, d, h, i, s).
 *
 *	@return int La différence entre les deux dates.
 *	@return bool False en cas d'échec.
 */
function my_date_diff( $date_start, $date_end, $format ){
	if( !isdateheure($date_start) ){
		return false;
	}
	if( !isdateheure($date_end) ){
		return false;
	}
	if( !in_array($format, array('y','m','d','h','i','s') ) ){
		return false;
	}

	$final_diff = false;

	try{
		$datetime1 = new DateTime( dateheureparse($date_start) );
		$datetime2 = new DateTime( dateheureparse($date_end) );

		$tspan = $datetime1->diff($datetime2);

		switch( $format ){
			case 'y':
				$final_diff = $tspan->y;
				break;
			case 'm':
				$final_diff = ($tspan->y * 12) + $tspan->m;
				break;
			case 'd':
				$final_diff = ($tspan->y * 12) + ($tspan->m * 30) + $tspan->d;
				break;
			case 'h':
				$final_diff = ($tspan->y * 12) + ($tspan->m * 30) + ($tspan->d * 24) + $tspan->h;
				break;
			case 'i':
				$final_diff = ($tspan->y * 12) + ($tspan->m * 30) + ($tspan->d * 24) + ($tspan->h * 60) + $tspan->i;
				break;
			case 's':
				$final_diff = ($tspan->y * 12) + ($tspan->m * 30) + ($tspan->d * 24) + ($tspan->h * 60) + ($tspan->i * 60) + $tspan->s;
				break;
		}

	}catch( Exception $exc ){
		error_log('my_date_diff() : '.$exc->getMessage());
	}

	return $final_diff;
}

/**	Détermine si une variable est un jeu de données MySQL.
 *	@param mixed $var La variable à tester.
 *	@return bool True s'il s'agit d'une jeu de données MySQL, False sinon.
 */
function is_mysql_result( $var ){
	return is_resource($var) && get_resource_type($var) == 'mysql result';
}

/**	Cette fonction construit une requête SQL à partir d'un tableau de données.
 *	@param array $ar_var Le tableau à transformer. S'il est vide ou invalide, une requête SQL ne contenant aucun résultat sera générée.
 *	@return string Une requête SQL représentant le jeu de données (valeur et nom des colonnes).
 */
function sql_from_array( $ar_var ){
	if( !is_array($ar_var) ){
		$ar_var = array();
	}

	$invalid_headers = false;
	$sql = '';
	if( sizeof($ar_var) ){
		$rows = array();
		$headers = array();
		foreach( $ar_var as $v ){
			$row = array();
			$headers_row = array();
			foreach( $v as $colname => $colval ){
				$headers_row[] = $colname;
				$row[] = ( is_null($colval) ? 'NULL' : '"'.addslashes($colval).'"' ).' as "'.$colname.'"';
			}
			$rows[] = implode(', ', $row);
			if( sizeof($headers) ){
				// compare les entêtes de la première ligne à la ligne courante
				if( sizeof($headers) != sizeof($headers_row) ){
					$invalid_headers = true;
					break;
				}else{
					foreach( $headers_row as $h ){
						if( !in_array($h, $headers) ){
							$invalid_headers = true;
							break;
						}
					}
					if( $invalid_headers ){
						break;
					}
				}
			}else{
				$headers = $headers_row;
			}
		}
		$sql = 'select '.implode(' union all select ', $rows);
	}

	if( $invalid_headers || !sizeof($ar_var) ){
		$sql = 'select 0 limit 0';
	}

	return $sql;
}

/** Cette fonction permet de renvoyer une chaine de caractère sous un format CB (4 groupe de 4 lettres séparer par un espace).
 *	Si la chaine est plus longue, les caractères seront rajoutés derrière le dernier groupe sans espace.
 *	@param string $str Obligatoire, chaine de caractère à mettre en forme
 *	@return string La chaine au format CB
 *	@deprecated le nom de cette fonction ne respecte pas nos conventions de nommage
 */
function format_CB( $str ){
	$str = trim( str_replace(' ', '', $str) );

	$tmp_str = '';

	$c = 0;
	for( $i=0 ; $i<strlen($str) ; $i++ ){
		if( $c<3 ){
			if( $i>1 && ($i%4)==0 ){
				$tmp_str .= ' ';
				$c++;
			}
		}

		$tmp_str .= $str[ $i ];
	}

	return $tmp_str;
}

/** Cette fonction permet de vérifier si une ou plusieurs valeurs sont bien présentes dans les clés d'un tableau
 *	@param string|array $keys Obligatoire, valeur ou tableau de valeurs à vérifier
 *	@param array $array Obligatoire, tableau de données
 *	@return bool True Si toutes les valeurs passées en paramètres sont des clés du tableau, False dans le cas contraire
 */
function ria_array_key_exists( $keys, $array ){
	if( !is_array($array) || !sizeof($array) ){
		return false;
	}

	if( !is_array($keys) ){
		if( $keys == '' ){
			return false;
		}

		$keys = array( $keys );
	}else{
		foreach( $keys as $one_key ){
			if( $one_key == '' ){
				return false;
			}
		}
	}

	foreach( $keys as $one_key ){
		if( !array_key_exists($one_key, $array) ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de vérifier si les clé d'un tableau sont accepter ou non
 * @param array $assocArray Tableau associatif a vérifier
 * @param string|array $allowedKeys clé ou tableau de clé accepté
 *
 * @return bool True si les clés du tableau sont corrects ou false si elles ne le sont pas
 */
function ria_array_keys_allowed( array $assocArray, $allowedKeys ){
	if( !is_array($assocArray) || !count($assocArray) ){
		return false;
	}
	if( !is_array($allowedKeys) ){
		if( $allowedKeys == '' ){
			return false;
		}

		$allowedKeys = array( $allowedKeys );
	}

	foreach( array_keys($assocArray) as $key ){
		if( !in_array($key, $allowedKeys) ){
			return false;
		}
	}

	return true;
}
/**	Cette fonction est un alias (simplifié) de la fonction native array_column (qui ne fonctionne qu'en version 5.5 et supérieure).
 *	@param array $array Un tableau multi-dimensionnel dont on souhaite récupérer une colonne.
 *	@param string $column Le nom de la colonne à récupérer.
 *	@param bool $strict Optionnel, détermine si tous les sous-tableaux du tableau initial doivent avoir la colonne $column.
 *	@return array Un tableau des valeurs pour la colonne donnée, False en cas d'échec.
 */
function ria_array_column( $array, $column, $strict=true ){

	if( !is_array($array) ){
		return false;
	}

	$values = array();

	foreach( $array as $row ){
		if( is_array($row) && isset($row[ $column ]) ){
			$values[] = $row[ $column ];
		}elseif( $strict ){
			return false;
		}
	}

	return $values;

}

/** Cette fonction permet de remplacer la dernière occurence par une chaine
 *	@param string $search Obligatoire, la valeur à chercher, autrement connue comme le masque
 *	@param string $replace Obligatoire, la valeur de remplacement à substituer à la valeur trouvée
 *	@param string $subject Obligatoire, la chaîne de caractères sur laquelle on va effectuer la recherche et le remplacement, aussi connu sous le nom de haystack
 *	@return string La chaine après avoir remplacé la dernière occurence
 */
function ria_str_last_replace( $search, $replace, $subject ){
    $pos = strrpos( $subject, $search );

    if( $pos !== false ){
        $subject = substr_replace( $subject, $replace, $pos, strlen($search) );
    }

    return $subject;
}

/** Cette fonction permet de vérifier qu'un fichier existe via son url.
 *	@param string $file Obligatoire, url du fichier
 *	@return bool True si le fichier est trouvé, False dans tous les autres cas
 *	@deprecated Cette fonction :
 *		1. Ne se situe pas dans le bon fichier (http.inc.php serait plus adapté)
 *		2. N'offre pas de bonnes performances. Curl est réputé plus rapide
 *		3. N'est pas très fiable : seul HTTP/1.1 est supporté et les redirections vers des 404 ne sont pas gérées
 */
function ria_file_exists_byurl( $file ){
	if( trim($file) == '' ){
		return false;
	}

	$file_headers = @get_headers($file);

	$exists = false;
	if( is_array($file_headers) && sizeof($file_headers) ){
		if( $file_headers[0] != 'HTTP/1.1 404 Not Found' ){
			$exists = true;
		}
	}

	return $exists;
}

/**	Cette fonction échappe un contenu pour une injection dans un fichier CSV.
 *	On suppose que le séparateur de ligne est \n, et que les contenus sont entre double-quotes.
 *	@param string $base Obligatoire, la chaîne à échapper.
 *	@param string $row_sub Optionnel, le substitut si la chaîne contient un saut de ligne.
 *	@return string La chaîne échappée.
 */
function ria_csv_escape( $base, $row_sub='[NL]' ){
	return str_replace( array("\n", '"'), array($row_sub, '""'), $base );
}

/** Cette fonction permet de transformer un entier en une taille de fichier (ex : 12,2 Mo, 1,2 Go).
 *	@param int $size Obligatoire, taille en octets
 *	@param int $precision Facultatif, nombre de chiffres après la virgule. La valeur par défaut est 1.
 *	@return string La mise en forme de la taille
 */
function ria_format_bytes( $size, $precision=1 ){
	if( !is_numeric($size) || $size < 0 ){
		$size = 0;
	}

	$units = array( 'o', 'Ko', 'Mo', 'Go', 'To' );

	$pos = 0;
	if( $size > 1024 ){
		while( true ){
			if( $pos >= 4 ){
				break;
			}

			$pos++;

			$size = $size / 1024;
			if( $size < 1024 ){
				break;
			}
		}
	}

	return round( $size, $precision ).' '.$units[ $pos ];
}

/** Cette fonction permet de vérifier qu'une chaine de caractère est bien une heure
 *	@param string $str Obligatoire, chaine de caractère à vérifier
 *	@return bool True s'il s'agit bien d'une heure, False dans le cas contraire
 */
function ria_is_time( $str ){
	if( preg_match('/^[0-9]{1,2}:[0-9]{2}$/', $str) ){
		return true;
	}elseif( preg_match('/^[0-9]{1}:[0-9]{2}:[0-9]{2}$/', $str) ){
		return true;
	}

	return false;
}

/** Fonction de rappel pour le remplcement via ria_split_balise
 *	@param $capture Obligatoire, résultat précédent via pref_replace_callback
 *	@return Le résultat de la fonction de remplacement choisi
 */
function ria_split_balise_callback( $capture ){
	global $ria_replace_args;

	$res = false;

	if( $ria_replace_args['option'] == 1 ){
		$res = $ria_replace_args['function']($ria_replace_args['search'], $ria_replace_args['replace'], $capture[1]).$capture[2];
	}else{
		$res = $capture[1].$ria_replace_args['function']($ria_replace_args['search'], $ria_replace_args['replace'], $capture[2]);
	}

	return $res;
}

/** Cette fonction permet de remplacer un texte par un autre selon plusieurs options.
 *	@param string $search Obligatoire, la valeur à chercher
 *	@param string $replace Obligatoire, la valeur de remplacement à substituer aux valeurs trouvées
 *	@param string $subject Obligatoire, la chaîne de caractères laquelle on va effectuer la recherche et le remplacement
 *	@param $function Obligatoire, fonction de remplacement à utiliser : preg_replace, str_replace, ...
 *	@param $option Optionnel, par défaut à 1 :
 * 			-  1 : remplace le texte hors en dehors de balise HTML (ne touchera donc pas au title, id, name...)
 *			- -1 : remplace le texte seulement dans les balises HTML
 */
function ria_split_balise( $search, $replace, $subject, $function, $option=1 ){
	global $ria_replace_args;
	$ria_replace_args = compact('search', 'replace', 'function', 'option');
	return preg_replace_callback('#((?:(?!<[/a-z]).)*)([^>]*>|$)#si', "ria_split_balise_callback", $subject);
}

/** Cette fonction permet de récupérer la distance entre deux points géographique.
 *	@param array $geo_loc_first Obligatoire, tableau contenant les coordonnées géographiques du premier point (ex. array('lat'=>32.9697, 'lng'=>-96.80322))
 *	@param array $geo_loc_second Obligatoire, tableau contenant les coordonnées géographiques du second point (ex. array('lat' => 29.46786, 'lng' => -98.53506))
 *	@param string $unit Optionnel, par défaut le résultat est retourné en nombre de kilomètres (unités acceptées : km -> Kilomètre, miles -> Miles)
 *	@return int Retourne la distance entre les deux point selon l'unité donnée
 *	@todo cette fonction n'est pas à sa place dans strings.inc.php
 */
function ria_measuring_distance( $geo_loc_first, $geo_loc_second, $unit='K' ){
	$unit = strtolower( $unit );
	if( !in_array($unit, array('km', 'miles')) ){
		$unit = 'km';
	}

	$dist = rad2deg(
		acos( sin( deg2rad($geo_loc_first['lat']) )
			* sin( deg2rad($geo_loc_second['lat']) )
			+ cos( deg2rad($geo_loc_first['lat']) )
			* cos( deg2rad($geo_loc_second['lat']) )
			* cos( deg2rad($geo_loc_first['lng'] - $geo_loc_second['lng']) )
		)
	);

	// Nautique : ($miles * 0.8684);

	$miles = $dist * 60 * 1.1515;
	switch( $unit ){
		case 'km': {
			$distance = $miles * 1.609344;
			break;
		}
		case 'miles': {
			$distance = $miles;
			break;
		}
	}

	return $distance;
}

/** Cette fonction permet de mettre en forme une adresse postal avec des abréviations (par exemple : "avenue" devient "AV").
 *	@param string $str Obligatoire, chaine de caractères
 *	@return string La chaine avec les abréviations
 */
function ria_adresses_postal_abbr( $str ){
	$ar_abbr = array(
		'ALL' => 'Allée',
		'PAS' => 'Passage',
		'AV' => 'Avenue',
		'PL' => 'Place',
		'BD' => 'Boulevard',
		'QU' => 'Quai',
		'CHE' => 'Chemin',
		'RTE' => 'Route',
		'CRS' => 'Cours',
		'RUE' => 'Rue',
		'IMP' => 'Impasse',
		'SQ' => 'Square'
	);

	foreach( $ar_abbr as $replace=>$search ){
		$str = preg_replace( '/('.$search.')/i', $replace, $str );
	}

	return $str;
}

/**
 * Cette fonction permet la conversion d'un numéro mobile français
 * au format international
 * @param string $mobileNumber numéro mobile français (les chiffres peuvent être séparé par n'importe quoi)
 * @return string le numéro mobile au format international +33 xxxxxxxxx
 */
function conv_france_international_number( $mobileNumber ){

	// élimination de tout caractère non numéric
	$mobileNumber = preg_replace('/[^0-9]+/', '', $mobileNumber);

	//Garder les 9 derniers chiffres
	$mobileNumber = substr($mobileNumber, -9);

	if( !preg_match('/^(6|7)/', $mobileNumber) ){
		return false;
	}
	$motif = '+33\1\2\3\4\5';

	$mobileNumber = preg_replace(
		'/(\d{1})(\d{2})(\d{2})(\d{2})(\d{2})/',
		$motif,
		$mobileNumber
	);

	return $mobileNumber;
}

/**
 * Cette fonction permet la vérification du format d'un numéro de téléphone pour un pays
 *
 * @param string|integer $phone_or_mobile Numéro de téléphone
 * @param string $cnt_code Facultatifs, code du pays
 * @param string $type Facultatifs, type de numéro qui dois être validé mobile ou phone, attention certain pays ne font pas la différence comme les USA
 * @return bool Retourne true si numéro valide, false dans le cas contraire
 */
function ria_is_valid_phone_number($phone_or_mobile, $cnt_code='FR', $type='') {
	$PhoneNumberUtil = \libphonenumber\PhoneNumberUtil::getInstance();
	$result = false;
	$cnt_code = strtoupper($cnt_code);
	$phone_or_mobile = str_replace( ' ', '', $phone_or_mobile );
	try {
		$phoneNumberObject = $PhoneNumberUtil->parse($phone_or_mobile, $cnt_code);
		$result = $PhoneNumberUtil->isValidNumberForRegion($phoneNumberObject, $cnt_code);
		if ($result && $type != '') {
			$NumberType = $PhoneNumberUtil->getNumberType($phoneNumberObject);
			if( in_array($type, array('phone', 'mobile')) ){
				// pour certain pays (ex : US) il est impossible de faire la différence entre un numéro mobile ou un numéro de fixe
				$result = $NumberType == \libphonenumber\PhoneNumberType::FIXED_LINE_OR_MOBILE;
				if( !$result ){
					if ($type == 'phone') {
						// Vérifie si le téléphone est de type fixe ou VoIP (numéro virtuel, exemple en France : "09 XX XX XX XX")
						$result = in_array($NumberType, array(\libphonenumber\PhoneNumberType::FIXED_LINE, \libphonenumber\PhoneNumberType::VOIP));
					} elseif ($type == 'mobile') {
						// Vérifie si le téléphone est de type mobile
						$result = $NumberType == \libphonenumber\PhoneNumberType::MOBILE;
					}
				}
			}
		}
	} catch (\libphonenumber\NumberParseException $e) {
		$result = false;
	}

	if( $result==false ){
		if( preg_match( '/\+?[0-9]{6,}/', $phone_or_mobile )==1 ){
			$result = true;
		}
	}

	return $result;
}

/** Cette fonction permet de transformer un nombre de secondes en un texte lisible.
 *  @param int $second Obligatoire, nombre de secondes
 *  @param int $mode Optionnel, mode à utliser : 1, 2, 3, 4, 5 ou 6
 *  					Exemple de résultat pour le mode 1
 *  					    - 58 secondes => 0 mois 0 jour 0 heure 0 minute 58 secondes
 *  					    - 1485 secondes => 0 mois 0 jour 0 heure 24 minutes 45 secondes
 *  					    - 59848 secondes => 0 mois 0 jour 16 heures 37 minutes 28 secondes
 *  					    - 598480 secondes => 0 mois 6 jours 22 heures 14 minutes 40 secondes
 *  					    - 5984800 secondes => 2 mois 9 jours 6 heures 26 minutes 40 secondes
 *
 *  					Exemple de résultat pour le mode 2
 *  					    - 58 secondes => 58 secondes
 *  					    - 1485 secondes => 24 minutes 45 secondes
 *  					    - 59848 secondes => 16 heures 37 minutes 28 secondes
 *  					    - 598480 secondes => 6 jours 22 heures 14 minutes 40 secondes
 *  					    - 5984800 secondes => 2 mois 9 jours 6 heures 26 minutes 40 secondes
 *
 *  					Exemple de résultat pour le mode 3
 *  					    - 58 secondes => 0 m 0 j 0 h 0 min 58 s
 *  					    - 1485 secondes => 0 m 0 j 0 h 24 min 45 s
 *  					    - 59848 secondes => 0 m 0 j 16 h 37 min 28 s
 *  					    - 598480 secondes => 0 m 6 j 22 h 14 min 40 s
 *  					    - 5984800 secondes => 2 m 9 j 6 h 26 min 40 s
 *
 *  					Exemple de résultat pour le mode 4
 *  					    - 58 secondes => 58 s
 *  					    - 1485 secondes => 24 min 45 s
 *  					    - 59848 secondes => 16 h 37 min 28 s
 *  					    - 598480 secondes => 6 j 22 h 14 min 40 s
 *  					    - 5984800 secondes => 2 m 9 j 6 h 26 min 40 s
 *
 *  					Exemple de résultat pour le mode 5
 *  					    - 58 secondes => 58 secondes
 *  					    - 1485 secondes => + de 24 minutes
 *  					    - 59848 secondes => + de 16 heures
 *  					    - 598480 secondes => + de 6 jours
 *  					    - 5984800 secondes => + de 2 mois
 *
 *  					Exemple de résultat pour le mode 6
 *  					    - 58 secondes => 58 s
 *  					    - 1485 secondes => + de 24 min
 *  					    - 59848 secondes => + de 16 h
 *  					    - 598480 secondes => + de 6 j
 *  					    - 5984800 secondes => + de 2 m
 *  @param string $suffixe Optionnel, valable pour les mode 5 et 6, détermine le suffixe à utiliser, par défaut "+ de "
 *	@return string Le nombre de secondes sous forme de chaîne de caractères
 */
function ria_seconds_to_time($second, $mode='1', $suffixe='+ de '){
    $month  = 30 * 24 * 60 * 60;
    // $week   = 7 * 24 * 60 * 60;
    $day    = 24 * 60 * 60;
    $hour   = 60 * 60;
    $minute = 60;

    // Nombre de mois
    $n_month = floor($second / $month);
    $second = $second - ($n_month * $month);

    // Nombre de jours
    $n_day  = floor($second / $day);
    $second = $second - ($n_day * $day);

    // Nombre d'heures
    $n_hour = floor($second / $hour);
    $second = $second - ($n_hour * $hour);

    // Nombre de minutes
    $n_minute = floor($second / $minute);

    // Nombre de secondes
    $second = floor($second) - ($n_minute * $minute);

    $return = '';

    $mode = 'm_'.$mode;

    switch ($mode) {
        case 'm_1' : {
            $return = $n_month.' mois '.$n_day.' jour'.($n_day > 1 ? 's' : '').' '.$n_hour.' heure'.($n_hour > 1 ? 's' : '').' '.$n_minute.' minute'.($n_minute > 1 ? 's' : '').' '.$second.' seconde'.($second > 1 ? 's' : '').'';
            break;
        }
        case 'm_2' : {
            if ($n_month) {
                $return .= ' '.$n_month.' mois';
            }

            if ($n_day) {
                $return .= ' '.$n_day.' jour'.($n_day > 1 ? 's' : '');
            }

            if ($n_hour) {
                $return .= ' '.$n_hour.' heure'.($n_hour > 1 ? 's' : '');
            }

            if ($n_minute) {
                $return .= ' '.$n_minute.' minute'.($n_minute > 1 ? 's' : '');
            }

            if ($second) {
                $return .= ' '.$second.' seconde'.($second > 1 ? 's' : '');
            }

            break;
        }
        case 'm_3' : {
            $return = $n_month.' m '.$n_day.' j '.$n_hour.' h '.$n_minute.' min '.$second.' s';
            break;
        }
        case 'm_4' : {
            if ($n_month) {
                $return .= ' '.$n_month.' m';
            }

            if ($n_day) {
                $return .= ' '.$n_day.' j';
            }

            if ($n_hour) {
                $return .= ' '.$n_hour.' h';
            }

            if ($n_minute) {
                $return .= ' '.$n_minute.' min';
            }

            if ($second) {
                $return .= ' '.$second.' s';
            }

            break;
        }
        case 'm_5' : {
            if ($n_month) {
                $return = $suffixe.$n_month.' mois';
            } elseif ($n_day) {
                $return = $suffixe.$n_day.' jour'.($n_day > 1 ? 's' : '');
            } elseif ($n_hour) {
                $return = $suffixe.$n_hour.' heure'.($n_hour > 1 ? 's' : '');
            } elseif ($n_minute) {
                $return = $suffixe.$n_minute.' minute'.($n_minute > 1 ? 's' : '');
            } elseif ($second) {
                $return = $second.' seconde'.($second > 1 ? 's' : '');
            }

            break;
        }
        case 'm_6' : {
            if ($n_month) {
                $return = $suffixe.$n_month.' m';
            } elseif ($n_day) {
                $return = $suffixe.$n_day.' j';
            } elseif ($n_hour) {
                $return = $suffixe.$n_hour.' h';
            } elseif ($n_minute) {
                $return = $suffixe.$n_minute.' min';
            } elseif ($second) {
                $return = $second.' s';
            }

            break;
        }
    }

	return $return;
}
/**
 * Cette fonction permet de convertir un nombre de secondes en durée lisible ( ex: 21354 => 5 heures 55 minutes 54 secondes )
 * @param int $delay Obligatoire, nombre de secondes
 * @param bool $with_seconds indique si le résultat doit inclure les secondes (true, valeur par défaut) ou non (false)
 * @return string Le nombre de secondes sous forme de chaîne de caractères
 */
function convert_second_to_readable_delay( $delay, $with_seconds=true ){
	return ria_seconds_to_time($delay, 2);
}

/** Cette fonction permet de récupérer un tableau avec en clé le code i18n de la langue et en valeur la langue en français
 *	@return array Un tableau avec i18n_lng => langue
 *	@todo Cette fonction n'a pas sa place ici (strings.inc.php)
 */
function getLangArray(){
	global $config;

	$lang = array(
		'' => ''
	);

	foreach( $config['i18n_lng_used'] as $value ){
		$stmt = ria_mysql_query(
			"select * from sys_languages
			 where lng_code = '" . addslashes( $value ) . "' and lng_cnt_code = ''"
		);
		if( !$stmt || !ria_mysql_num_rows($stmt) ){
			$txt = 'Français';
		}else{
			while( $c = ria_mysql_fetch_array( $stmt ) ){
				$txt = $c['lng_name'];
			}
		}
		$lang[$value] = $txt;
	};

	return $lang;
}

/** Cette fonction permet d'arroudi un nombre à une précision donnée.
 *	Le chiffre retourné aura toujours la précision donnée arrondi selon le mode choisi
 *	@param float $val Obligatoire, nombre entier ou à virgule à arrondir
 *	@param int $precision Optionnel, précision souhaité
 *	@param int <$mode mode d'arrondi choisi (PHP_ROUND_HALF_UP : arrondi au supérieur, PHP_ROUND_HALF_DOWN : arrondi à l'inférieure, false : règle de calcul - supérieur ou égal à 5)
 *	@return bool False si la valeur donné n'est pas un numérique, sinon le nuumérique à la précision donnée arrondi
 */
function ria_round( $val, $precision=0, $mode = PHP_ROUND_HALF_UP ){
	$val = str_replace( array(' ', ','), array('', '.'), $val );
	if (!is_numeric($val) || $val <= 0) {
		return false;
	}

	if (!is_int($precision)) {
		return false;
	}

	$ar_val = preg_split( '/\./', $val );
	if (count($ar_val) != 2) {
		$ar_val[1] = array();
	}

	$ar_val[1] = count($ar_val[1]) ? str_split( $ar_val[1] ) : array();
	for ($i=0; $i < $precision; $i++) {
		$ar_val[1][$i] = array_key_exists($i, $ar_val[1]) ? $ar_val[1][ $i ] : 0;
	}

	$int  = $ar_val[ 0 ];
	$dec  = $ar_val[ 1 ];
	$last = count($dec) > $precision ? $dec[ $precision ] : $dec[ ($precision-1) ];
	$dec = array_reverse( array_slice( $dec, 0, $precision ) );

	$plus = false; $first = true;
	foreach ($dec as $k => $v) {
		if ($first || $plus) {
			$first = $plus = false;

			if ($mode == PHP_ROUND_HALF_UP) {
				if ($v==9) {
					$plus = true;
					$v = 0;
				}else{
					$v = $v + 1;
				}
			}elseif ($mode == PHP_ROUND_HALF_DOWN) {
				if ($v > 0) {
					$v = $v - 1;
				}
			}else{
				$v = $v >= 5 ? ($v + 1) : $v;
				if ($v == 10) {
					$v = 0;
					$plus = true;
				}
			}

			$dec[ $k ] = $v;
		}

		if (!$first && !$plus) {
			break;
		}
	}

	if ($plus) {
		$int = $int + 1;
	}

	return $int.'.'.implode( '', array_reverse($dec) );
}


/** Cette fonction permet de trasnformer un xml en csv
 * @param SimpleXMLElement $xml Un objet xml créer avec la fonction simplexml_load_file
 * @param Resource $handle Une ressource de fichier vréer avec fopen
 * @param array $headers Tableau vide qui contiendra les headers
 * @param array $cols Tableau vide qui contiendras les colonnes
 * @param bool  $is_recursive Facultatif, ne pas renseigner paramètre a utilisation interne pour se renseigner sur la récursivité
 * @param array $skip Facultatif, Tableau contenant les noms de balises a exclure de la convertion
 *
 * @return array|bool retourne un tableau associatifs avec les lignes du csv
 */
function ria_convert_xml_to_csv( $xml, $handle, &$headers, &$cols, $is_recursive=false, $skip=array()){

	if( !is_a( $xml , 'SimpleXMLElement') ) {
		return false;
	}
	if( !is_resource($handle) ){
		return false;
	}

	if( !is_array($headers) ){
		return false;
	}

	if( !is_array($cols) ){
		return false;
	}

	$vals = array();
	$first = true;

	foreach ($xml->children() as $item)	{
		if( in_array($item->getName(), $skip) ){
			continue;
		}

		if( !$is_recursive ){
			$cols = array();
		}

		$hasChild = count( $item->children() ) > 0;
		if( !$hasChild ){
			$headers[$item->getName()] = $item->getName();
			$value = $item->__toString();

			if( $is_recursive ){
				$cols[] = $value;
			}
		}else{
			ria_convert_xml_to_csv($item, $handle, $headers, $cols, true, $skip);
		}

		if( !$is_recursive ){
			if( $first ){
				fputcsv($handle, $headers, ';');
				$first = false;
			}

			fputcsv($handle,$cols,';');
			$vals[] = $cols;
		}
	}

	if( $is_recursive && (count($xml->children()) == count($cols)) ){
		return $cols;
	}

	return $vals;
}

/** Cette fonction permet de convertir un numéro de colonne en une lettre ou un jeu de lettre comme sur excel
 * @param int $num Numéro de la colonne
 * @return string La lettre correspondant au numéro
 */
function ria_get_name_from_number( $num ){
	$numeric = $num % 26;
	$letter = chr(65 + $numeric);
	$num2 = intval($num / 26);
	if ($num2 > 0) {
		return ria_get_name_from_number($num2 - 1) . $letter;
	} else {
		return $letter;
	}
}

/**
 * Cette fonction permet de convertir tout les caractère de word en utf-8
 * @param  string $string Chaine de caractère a corriger
 * @return string         chaine corrigée
 */
function ria_convert_word_to_ascii($string){
	if( !is_string($string) ){
		return $string;
	}

	$chr_map = array(
		// Windows codepage 1252
		"\xC2\x82" => "'", 			// U+0082 => U+201A single low-9 quotation mark
		"\xC2\x84" => '"', 			// U+0084 => U+201E double low-9 quotation mark
		"\xC2\x8B" => "'", 			// U+008B => U+2039 single left-pointing angle quotation mark
		"\xC2\x91" => "'", 			// U+0091 => U+2018 left single quotation mark
		"\xC2\x92" => "'", 			// U+0092 => U+2019 right single quotation mark
		"\xC2\x93" => '"', 			// U+0093 => U+201C left double quotation mark
		"\xC2\x94" => '"', 			// U+0094 => U+201D right double quotation mark
		"\xC2\x9B" => "'", 			// U+009B => U+203A single right-pointing angle quotation mark
		// Regular Unicode 			// U+0022 quotation mark (")
		// U+0027 apostrophe (')
		"\xC2\xAB" => '"', 			// U+00AB left-pointing double angle quotation mark
		"\xC2\xBB" => '"', 			// U+00BB right-pointing double angle quotation mark
		"\xE2\x80\x98" => "'", 	// U+2018 left single quotation mark
		"\xE2\x80\x99" => "'", 	// U+2019 right single quotation mark
		"\xE2\x80\x9A" => "'", 	// U+201A single low-9 quotation mark
		"\xE2\x80\x9B" => "'", 	// U+201B single high-reversed-9 quotation mark
		"\xE2\x80\x9C" => '"', 	// U+201C left double quotation mark
		"\xE2\x80\x9D" => '"', 	// U+201D right double quotation mark
		"\xE2\x80\x9E" => '"', 	// U+201E double low-9 quotation mark
		"\xE2\x80\x9F" => '"', 	// U+201F double high-reversed-9 quotation mark
		"\xE2\x80\xB9" => "'", 	// U+2039 single left-pointing angle quotation mark
		"\xE2\x80\xBA" => "'", 	// U+203A single right-pointing angle quotation mark
	);

	$char_val = array_keys ($chr_map); // but: for efficiency you should
	$rpl = array_values($chr_map); // pre-calculate these two arrays
	$string = str_replace($char_val, $rpl, html_entity_decode($string, ENT_QUOTES, "UTF-8"));

	return $string;
}

/**
 * \brief      Retourne la longueur maximale d'un champ
 *
 * @param string $identifier  L'identifiant d'un champ, Valeurs acceptées : email,
 *                          password, ref, firstname, lastname, society, siret,
 *                          address1, address2, address3, postal_code, city,
 *                          country, phone, fax, mobile, phone_work.
 *
 * @return int|bool Retourne le nombre de caratère maximal. Si l'identifier n'est pas
 *             trouvé, retourne false.
 */
function ria_maxlength( $identifier ){
	global $config;

	// Le paramètre identifier n'est pas un string
	if ( !is_string($identifier) ) {
		return false;
	}

	// Configuration par défaut des longueurs maxi des champs de riashop
	$default_maxlength_array = array(
		'email' => 100,
		'password' => 32,
		'ref' => 17,
		'firstname' => 75,
		'lastname' => 75,
		'society' => 75,
		'siret' => 20,
		'address1' => 75,
		'address2' => 75,
		'address3' => 75,
		'postal_code' => 12,
		'city' => 75,
		'country' => 75,
		'phone' => 21,
		'fax' => 21,
		'mobile' => 20,
		'phone_work' => 20,
		'naf' => 5,
	);

	$gescom_maxlength_array = array();

	// Configuration personnalisée par gestion commerciale
	switch($config['sync_global_gescom_type']) {
		case GESCOM_TYPE_SAGE:
			$gescom_maxlength_array = array(
				'email' => 69,
				'firstname' => 17,
				'lastname' => 17,
				'society' => 35,
				'siret' => 14,
				'address1' => 35,
				'address2' => 35,
				'postal_code' => 9,
				'city' => 35,
				'country' => .2,
				'phone' => 21,
				'fax' => 21,
			);
			break;
	}

	// Fusion des configurations
	$maxlength_array = array_merge($default_maxlength_array, $gescom_maxlength_array);

	// Si le champ est connu, retourne la longueur maxi
	if ( isset($maxlength_array[$identifier]) ) {
		return $maxlength_array[$identifier];
	}

	return false;
}

/** Retourne l'attribut html maxlength="X"
 *
 *	@param string $identifier  L'identifiant du champ
 *
 *	@return string Retourne l'attribut html maxlength="X"
 */
function ria_html_maxlength( $identifier ){
	// Le paramètre identifier n'est pas un string
	if ( !is_string($identifier) ) {
		return '';
	}

	$maxlength = ria_maxlength($identifier);

	if ( $maxlength ) {
		return ' maxlength="'.$maxlength.'"';
	}

	return '';
}

/** Cette fonction permet de détecter si une string contient de l'HTML ou non
 *	@param string $string Chaîne de caractère à vérifier
 *
 *	@return bool true si la chaîne contient du HTML, false si elle n'en a pas
 */
function ria_is_HTML( $string ){
	if( !is_string($string) ){
		return false;
	}
	$return = false;
	preg_match("/<(html|head|body|link|meta|script|style|title|abbr|blockquote|cite|q|sup|sub|strong|em|mark|h1|h2|h3|h4|h5|h6|img|figure|figcaption|audio|video|source|audio|video|a|br|p|hr|address|del|ins|dfn|kbd|pre|progress|time|ul|ol|li|dl|dt|dd|table|caption|tr|th|td|thead|tbody|tfoot|form|fieldset|legend|label|input|textarea|select|option|optgroup|header|nav|footer|section|article|aside|p|h2|span|div)/", $string, $matches);
	if( !empty($matches) && count($matches) ){
		$return = true;
	}
	return $return;
}

/** Cette fonction permet de supprimer les balises ouvrantes et fermante pre
 *	@param string $string Chaîne de caractère à modifier
 *	@return string La chaîne de caractère modifiée
 */
function ria_strip_pre( $string ){
	if( !is_string($string) ){
		return $string;
	}
	if (strstr($string, '<pre>')) {
        $string = preg_replace("/^<pre>/", "", $string);
        $string = preg_replace("/<\/pre>$/", "", $string);
    }
    return $string;
}

/** Cette fonction permet de générer à partir d'un code à 12 chiffre un EAN13 avec le caractère de controles (trouvé sur internet)
 *	@param string $digits Obligatoire, chiffre sur 12 caractères
 *	@return string un code ean 13
 *	@todo cette fonction n'a pas sa place dans ce fichier ou bien est mal nommée
 */
function ean13_check_digit( $digits ){
	$digits =(string)$digits;
	$even_sum = $digits{1} + $digits{3} + $digits{5} + $digits{7} + $digits{9} + $digits{11};
	$even_sum_three = $even_sum * 3;
	$odd_sum = $digits{0} + $digits{2} + $digits{4} + $digits{6} + $digits{8} + $digits{10};
	$total_sum = $even_sum_three + $odd_sum;
	$next_ten = (ceil($total_sum/10))*10;
	$check_digit = $next_ten - $total_sum;
	return $digits . $check_digit;
}

/** Cette fonction permet de formater un nombre au format français
 * 	@param string $number numéro au format américain
 * 	@param int $decimals Optionnel, nombre de décimal
 * 	@return string Le nombre bien formaté
 */
function ria_number_french( $number, $decimals=2 ){
	return number_format($number, $decimals, ',', ' ');
}

/** Cette fonction sert d'appel à usort pour trier des tableaux. En l'occurence ici, permet de trier par date du plus récent au plus ancien
 * @param $obj1 Obligatoire, un objet
 * @param $obj2 Obligatoire, un objet
 * @return float un nombre réel qui est soit :
 * 				- négatif si la date de l'obj1 est situé après celle de l'obj2,
 * 			ou 	- positif dans le cas inverse,
 * 			ou	- 0 si les deux objets ont la même date.
 */
function ria_date_sort_desc( $obj1, $obj2 ) {
	if (is_numeric($obj1['date']) && is_numeric($obj2['date'])){
		return $obj2["date"] - $obj1["date"];
	} else {
		return strtotime($obj2["date"]) - strtotime($obj1["date"]);
	}
}

/**
 * Cette fonction permet de supprimé les =" et " autour d'une valeur pour le bonne affichage sur excel
 *
 * @param string $string une chaine de caractère transformé
 * @return string la chaine de caractère propre
 */
function ria_trim_excel_export_escaping($string){
	$matches = array();
	preg_match("/^=\"(.*)\"$/", trim($string), $matches);
	if (!empty($matches)) {
		$string = trim($matches[1]);
	}

	return trim($string);
}

/**
 * Cette fonction permet de récupérer la valeur dans un tableau associatif avec une valeur par defaut si la clé n'existe pas
 *
 * @param array $array Tableau de donnée
 * @param string|integer $key Clé pour récupérer la valeur
 * @param mixed $default Une valeur par defaut
 * @return mixed retourne la valeur du tableau ou la valeur par defaut
 */
function ria_array_get($array, $key, $default=null){
	if( !is_array($array) ){
		return $default;
	}

	if (!is_string($key) && !is_numeric($key)) {
		return $default;
	}

	if (!array_key_exists($key, $array)) {
		return $default;
	}

	return $array[$key];
}
/**
 * Cette fonction permet de formaté une date pour avoir le numéro de la semaine et l'année correspondante
 *
 * @param DateTime $date Object Datetime avec la date à formater
 * @return array Retourne un tableau avec :
 * 				week => numéro de la semaine
 *				year => année correspondant à la semaine
 *				short_year => les deux dernier chiffre de l'année correspondant à la semaine
 *				format => format week/year
 *				short_format => format week/short_year
 */
function ria_get_week_year_from_date(DateTime $date){
	$week = $date->format('W');
	$add_year = 0;
	if ($week == '01' && $date->format('m') == '12') {
		$add_year = 1;
	}
	$year = $date->format('Y') + $add_year;
	$short_year = $date->format('y') + $add_year;
	return array(
		'week' => $week,
		'year' => $year,
		'short_year' => $short_year,
		'format' => $week.'/'.$year,
		'short_format' => $week.'/'.$short_year,
	);
}

/**	Cette fonction permet de traduire un interval entre deux date en une chaine de caractère "du XX au XX"
 *
 *  @param string $date_start Obligatoire, date de début, au format anglais "yyyy-mm-dd"
 * 	@param string $date_end Obligatoire, date de fin, au format anglais "yyyy-mm-dd"
 *
 * 	@return string l'intervalle entre les deux dates, selon les formats suivants :
 * 		- les deux dates fournies sont identiques -> "le jour mois année". Exemple : "le 2 Janvier 2019"
 * 		- les dates sont de la même année et du même mois -> "du jour1 au jour2 mois". Exemple : "du 16 au 24 Mars"
 * 		- les dates sont juste de la même année -> "du jour1 mois1 au jour2 mois2". Exemple : "du 1 Février au 24 Mars"
 * 		- les dates n'ont rien en commun -> "du jour1 mois1 année1 au jour2 mois2 année2". Exemple : "du 25 Décembre 2018 au 2 Janvier 2019"
 */
function date_period_format($date_start, $date_end){
	// Mise dans des tableaux associatifs avec des informations détaillées des dates données
	$ar_date_start = date_parse($date_start);
	$ar_date_end = date_parse($date_end);

	// Cas général, les dates n'ont rien en commun
	$date_interval = 'du '.dateformatfull( dateheureunparse($date_start) ).' au '.dateformatfull( dateheureunparse($date_end) );

	// En fonction des différents cas, le retour peut être différent
	if( $ar_date_start == $ar_date_end ){
		// Les dates sont identiques
		$date_interval = 'le '.dateformatfull( dateheureunparse($date_start) );
	}elseif( $ar_date_start['month'].$ar_date_start['year'] == $ar_date_end['month'].$ar_date_end['year'] ){
		// Cas où les dates sont du même mois et de la même année
		$date_interval = 'du '.$ar_date_start['day'].' au '.$ar_date_end['day'].' '.month_french($ar_date_end['month']);
	}elseif( $ar_date_start['year'] == $ar_date_end['year'] ){
		// Les dates sont uniquement de la même année
		$date_interval = 'du '.$ar_date_start['day'].' '.month_french($ar_date_start['month']).' au '.$ar_date_end['day'].' '.month_french($ar_date_end['month']);
	}

	return $date_interval;
}

/** Cette fonction permet de récupérer les informations sur les trimestre.
 * 	@param int $year Optionnel, l'année (par défaut l'année en cours est prit)
 * 	@param string $date Optionnel, permet de passer une date et de récupérer les informations du trimestre lié à celle-ci
 * 	@return array Un tableau contenant pour chaque trimestre :
 * 		- month : les mois concernés
 * 		- start : la date de début
 * 		- end : la date de fin
 */
function date_trimester_get( $year=0, $date='' ){
	if( !is_numeric($year) || $year <= 0 || strlen($year) != 4 ){
		$d = new DateTime();
		$year = $d->format('Y');
	}else{
		$d = new DateTime( $year.'-01-01' );
	}

	if( trim($date) != '' ){
		if( !isdate($date) ){
			return false;
		}
	}

	$ar_trimester = [
		[
			'name' => _('1er trimestre'),
			'month' => [1, 2, 3],
			'start' => $d->format('Y').'-01-01',
			'end' => $d->format('Y').'-03-31',
		],
		[
			'name' => _('2ème trimestre'),
			'month' => [4, 5, 6],
			'start' => $d->format('Y').'-04-01',
			'end' => $d->format('Y').'-06-30',
		],
		[
			'name' => _('3ème trimestre'),
			'month' => [7, 8, 9],
			'start' => $d->format('Y').'-07-01',
			'end' => $d->format('Y').'-09-30',
		],
		[
			'name' => _('4ème trimestre'),
			'month' => [10, 11, 12],
			'start' => $d->format('Y').'-10-01',
			'end' => $d->format('Y').'-12-31',
		]
	];

	$return = $ar_trimester;

	if( isdate($date) ){
		$date = new DateTime( dateparse($date) );
		$month = $date->format('m');

		foreach( $ar_trimester as $trimester ){
			if( in_array($month, $trimester['month']) ){
				$return = $trimester;
				break;
			}
		}
	}


	return $return;
}

/** Cette fonction retourne le nombre en paramètre formaté selon la langue et le type voulu.
 * 	@see https://www.php.net/manual/fr/class.numberformatter.php#intl.numberformatter-constants.types
 * 	@see https://en.wikipedia.org/wiki/ISO_4217#Active_codes
 *
 * 	@param float $number Obligatoire, Nombre à formater
 * 	@param int $type Optionnel, type de formatage voulu (cf. NumberFormatter)
 * 	@param int $digit Optionnel, nombre de caractère après la virgule (par défaut 0)
 *	@param string $currency_code Le code à 3 lettres ISO 4217 de la devise à utiliser
 *	@return string Le nombre formatté
 */
function ria_number_format( $number, $type=NumberFormatter::DECIMAL ,$digit=0, $currency_code='' ){
	$formatter = new NumberFormatter(isset($_SESSION['lang']) ? $_SESSION['lang'] : 'fr', $type);
	$formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $digit);
	$formatter->setAttribute(NumberFormatter::MIN_FRACTION_DIGITS, $digit);
	$formatter->setAttribute(NumberFormatter::MAX_FRACTION_DIGITS, $digit);
	$formatter->setAttribute(NumberFormatter::DECIMAL_ALWAYS_SHOWN, $digit);

	if( $type !== NumberFormatter::CURRENCY ){
		return $formatter->format($number, NumberFormatter::TYPE_DOUBLE);
	}

	if( $currency_code ){
		return $formatter->formatCurrency($number, $currency_code);
	}

	return $formatter->formatCurrency(
		$number,
		(!empty($_SESSION['admin_currency']) ? $_SESSION['admin_currency'] : 'EUR')
	);
}

/** Cette fonction retourne la date en paramètre formatté selon la langue et le type voulu.
 *	@param string $date Date à formatter (d/m/y ou d/m/y à hh:mm:ss ou y-m-d ou y-m-d hh:mm:ss)
 *	@param boolean $force_delimiter Mettre à true pour forcer un délimiter entre le jour et l'heure (ignorer si l'heure n'est pas précisé dans la date)
 *	@param boolean $force_no_hour Mettre à true pour ne pas afficher les heures
 *	@return string La date formatté
 */
function ria_date_format( $date, $force_delimiter=false, $force_no_hour=false ){

	if( $date == '00/00/0000' || $date == '00/00/0000 à 00:00' || trim($date) == '' ){
		return $date;
	}
	$with_hour = false;
	$with_delimiter = false;
	if( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} à {1}[0-9]{1,2}:[0-9]{2}$/', $date ) ){ // Date française format jj/mm/aaaa à hh:mm
		$date = DateTime::createFromFormat( 'd/m/Y à H:i', $date );
		$with_hour = true;
		$with_delimiter = true;
	}elseif( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} à {1}[0-9]{1,2}:[0-9]{2}:[0-9]{2}$/', $date ) ){ // Date française format jj/mm/aaaa à hh:mm:ss
		$date = DateTime::createFromFormat( 'd/m/Y à H:i:s', $date );
		$with_hour = true;
		$with_delimiter = true;
	}elseif( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}[ ]{1}[0-9]{1,2}:[0-9]{2}$/', $date ) ){ // Date française format jj/mm/aaaa hh:mm
		$date = DateTime::createFromFormat( 'd/m/Y H:i', $date );
		$with_hour = true;
	}elseif( preg_match( '/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $date ) ){ // Date français format jj/mm/aaaa
		$date = DateTime::createFromFormat( 'd/m/Y', $date );
	}elseif( preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}[ ][0-9]{1,2}:[0-9]{2}[:]{1}[0-9]{2}$/', $date)){ // Date américaine format aaaa-mm-jj hh:mm:ss
		$date = DateTime::createFromFormat( 'Y-m-d H:i:s', $date );
		$with_hour = true;
	}elseif( preg_match( '/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/', $date)){ // Date américaine format aaaa-mm-jj
		$date = DateTime::createFromFormat( 'Y-m-d', $date );
	}elseif( is_numeric($date) ){ // Date au format Timestamp
		$tmp = new DateTime();
		$tmp->setTimestamp($date);
		$with_hour = true;
	}else{
		error_log(__FILE__." : ".__LINE__." Format de date invalide (".$date.")");
		return '';
	}


	if( !isset($_SESSION['lang']) || $_SESSION['lang'] == 'fr_FR' || $_SESSION['lang'] == 'fr' ){
		$fmt = new IntlDateFormatter('fr', IntlDateFormatter::SHORT, IntlDateFormatter::NONE, null, null, 'dd/MM/yyyy');
	}else{
		$fmt = new IntlDateFormatter($_SESSION['lang'], IntlDateFormatter::SHORT, IntlDateFormatter::NONE);
	}

	$formated_date = $fmt->format($date);
	if( $with_hour && !$force_no_hour){
		$hour_fmt = new IntlDateFormatter(isset($_SESSION['lang']) ? $_SESSION['lang'] : 'fr', IntlDateFormatter::NONE, IntlDateFormatter::SHORT);
		if( $with_delimiter || $force_delimiter ){
			$formated_date .= _(" à");
		}
		$formated_date .= " ".$hour_fmt->format($date);
	}
	return $formated_date;
}
/// @}


/**
 * Cette fonction retourne la valeur string Mo, Go, Ko, en int octet
 *
 * @see https://www.php.net/manual/fr/function.ini-get.php
 *
 * @param  string	$val Valeur à convertir
 * @return int   	Le nombre converti en octet
 */
function return_bytes($val) {
	$val = trim($val);
	$last = strtolower($val[strlen($val)-1]);
	switch($last) {
		case 'g':
			$val *= 1024;
		case 'm':
			$val *= 1024;
		case 'k':
			$val *= 1024;
	}

	return $val;
}

/**
 * Trouve la date la plus proche dans le tableau.
 *
 * @param  array  $dates            Un tableau de dates à comparer
 * @param  string $date             La date que vous voulez comparez
 * @throws InvalidArgumentException Si le paramètres "$dates" est vide
 * @return string                   La date du tableau la plus proche du paramètre $date
 */
function find_nearest_date_in_array( array $dates, $date ){
	if( !$dates ){
		throw new InvalidArgumentException('Le paramètre $dates ne doit pas être vide.');
	}

	$intervals = array();

	foreach( $dates as $d ){
		$intervals[] = abs(strtotime($d) - strtotime($date));
	}

	return $dates[array_search(min($intervals), $intervals)];
}

/**	Cette fonction permet de nettoyer un numéro de téléphone de tous caractères spéciaux éventuels
 * @param	string|int	$entry	Obligatoire, numéro de téléphone
 * @return	string		Le numéro de téléphone nettoyé
 */
function ria_sanitize_phone_number($entry, $limit=10){

	if( !is_string($entry) && !is_numeric($entry) ){
		return '';
	}
	$entry = preg_replace('/[^0-9]/i', '', $entry);

	if( is_numeric($limit) && strlen($entry) !== (int)$limit ){
		return '';
	}
	return $entry;
}
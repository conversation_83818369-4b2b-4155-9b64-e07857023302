<?php

/**
 * Autoloader spécifique au module EventService
 * \param  $className Nom de la class
 * \return            true si success, false si erreur (class/fichier n'existe pas ou class qui n'est pas du module)
 */
function event_service_autoload($className){
	$filename = str_replace("\\", '/', $className) . '.inc.php';
    if (strstr($filename, 'EventService') && file_exists(__DIR__.'/../'.$filename)) {
    	require_once($filename);
        if (class_exists($className)) {
            return true;
        }
    }
    return false;
}
spl_autoload_register('event_service_autoload');
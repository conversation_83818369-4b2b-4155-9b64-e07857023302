<?php
namespace EventService\Products\Events;
/**
 * \defgroup ProductsEvents Products Events
 * \ingroup EventService
 * @{
 */
/**
 * \class PriceChanges
 * \brief Événement déclanché via la veille tarifaire si il y a eu des changements de tarifaire.
 * Elle contient les produits qui on des tarifs modifié.
 */
class PriceChanges
{
	/**
	 * Tableau contenant les ProductPriceChanges
	 *
	 * @var array $products
	 */
	protected $products;

	/**
	 * Constructeur de la classe
	 *
	 * @return void
	 */
	public function __construct()
	{
		$this->products = array();
	}

	/**
	 * Cette fonction ajoute un changement de tarifs
	 *
	 * @param ProductPriceChanges $product Objet ProductPriceChanges
	 * @return void
	 */
	public function priceChanged(ProductPriceChanges $product)
	{
		$this->products[$product->product('id')] = $product;
	}

	/**
	 * Cette fonction permet de vérifier si un produit est présent dans l'évènement
	 *
	 * @param int $prd_id Obligatoire, Identifiant du produit
	 * @return bool true si le produit est présent, false dans le cas contraire
	 */
	public function hasProduct($prd_id)
	{
		return array_key_exists($prd_id, $this->products);
	}

	/**
	 * Cette fonction permet de retourner le produit
	 *
	 * @param int $id Identifiant du produit
	 * @return mixed Retourne le produit ou null
	 */
	public function product($id)
	{
		if (!$this->hasProduct($id)) {
			return null;
		}

		return $this->products[$id];
	}

	/**
	 * Cette fonction retourne tous les identifiants de produits
	 *
	 * @return array Retourne un tableau avec tout les idnetifiants produits
	 */
	public function getAllProductIds()
	{
		return array_keys($this->products);
	}

	/**
	 * Cette fonction permet de déterminer si l'évènnement contient des changement de prix
	 *
	 * @return bool Retourne true si il y a un changement sinon false
	 */
	public function hasChanges()
	{
		return !empty($this->products);
	}
}
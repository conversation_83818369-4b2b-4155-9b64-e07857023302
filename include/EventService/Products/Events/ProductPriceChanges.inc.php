<?php
namespace EventService\Products\Events;

class ProductPriceChanges
{
	/**
	 * $product
	 *
	 * @var undefined $product
	 */
	protected $product;

	/**
	 * $lastPrice
	 *
	 * @var undefined $lastPrice
	 */
	protected $lastPrice;

	/**
	 * $currentPrice
	 *
	 * @var undefined $currentPrice
	 */
	protected $currentPrice;

	/**
	 * __construct
	 *
	 * @param mixed $product
	 * @return void
	 */
	public function __construct($product)
	{
		$this->product = $product;
	}

	/**
	 * product
	 *
	 * @param mixed $key=null
	 * @return void
	 */
	public function product($key=null)
	{
		if (!is_null($key) && array_key_exists($key, $this->product)) {
			return $this->product[$key];
		}

		return $this->product;
	}

	/**
	 * withLastPrice
	 *
	 * @param mixed $price
	 * @param mixed $shipping
	 * @param mixed $promo
	 * @return void
	 */
	public function withLastPrice($price, $shipping, $promo)
	{
		$this->lastPrice = $this->parsePrice($price, $shipping, $promo);
	}

	/**
	 * withCurrentPrice
	 *
	 * @param mixed $price
	 * @param mixed $shipping
	 * @param mixed $promo
	 * @return void
	 */
	public function withCurrentPrice($price, $shipping, $promo)
	{
		$this->currentPrice = $this->parsePrice($price, $shipping, $promo);
	}

	/**
	 * lastPrice
	 *
	 * @return void
	 */
	public function lastPrice()
	{
		return $this->lastPrice;
	}

	/**
	 * currentPrice
	 *
	 * @return void
	 */
	public function currentPrice()
	{
		return $this->currentPrice;
	}

	/**
	 * hasPriceDroped
	 *
	 * @return void
	 */
	public function hasPriceDroped()
	{
		if ($this->currentPrice['price'] < $this->lastPrice['price']) {
			return true;
		}

		if ($this->currentPrice['promo'] < $this->lastPrice['promo']) {
			return true;
		}

		return false;
	}

	/**
	 * parsePrice
	 *
	 * @param mixed $price
	 * @param mixed $shipping
	 * @param mixed $promo
	 * @return array un tableau associatif avec les clés suivantes :
	 * 		- price
	 * 		- shipping
	 * 		- promo
	 */
	protected function parsePrice($price, $shipping, $promo)
	{
		return array(
			'price' => is_null($price) ? 0 : $price,
			'shipping' => is_null($shipping) ? 0 : $shipping,
			'promo' => is_null($promo) ? 0 : $promo,
		);
	}
}
<?php
namespace EventService\Products\Listeners;
use EventService\Products\Events\PriceChanges;
use EventService\Products\Events\ProductPriceChanges;

class EmailPriceDrop
{
	/**
	 * $parsedProducts
	 *
	 * @var array tableau des identifiants de produits ayant déjà été analysés
	 */
	protected $parsedProducts = array();

	/**
	 * $subscription_stack
	 *
	 * @var undefined
	 */
	protected $subscription_stack = array();

	/**
	 * handle
	 *
	 * @param PriceChanges $event
	 * @return void
	 */
	public function handle(PriceChanges $event)
	{
		global $config;

		$inscript = prc_prices_drop_get_products( '', $config['wst_id'] );
		foreach( $inscript as $email => $prd_ids ){

			$products_to_notify = array();
			$notify_all_products = false;

			if (($sub_id = $this->shouldParseAllProducts($prd_ids))) {
				$this->pushSubscriptionStack($sub_id);
				$notify_all_products = true;
				$prd_ids = $event->getAllProductIds();
			}

			foreach($prd_ids as $sub_id => $prd_id) {
				if ($this->isAlreadyParsed($prd_id)) {
					$products_to_notify[] = $this->parsedProduct($prd_id);
					continue;
				}

				if (!$this->shouldNotifyProduct($event, $prd_id)) {
					continue;
				}

				if (!$notify_all_products) {
					$this->pushSubscriptionStack($sub_id);
				}

				$this->parseProduct($event->product($prd_id));

				$products_to_notify[] = $this->parsedProduct($prd_id);
			}

			if (!empty($products_to_notify)) {
				$res = prc_prices_drop_notify( $email, $products_to_notify, $config['wst_id'] );
				$this->flushSubscriptionStack();
			}
		}
	}

	/**
	 * pushSubscriptionStack
	 *
	 * @param mixed $subscription_id
	 * @return void
	 */
	protected function pushSubscriptionStack($subscription_id)
	{
		$this->subscription_stack[] = $subscription_id;
	}

	/**
	 * flushSubscriptionStack
	 *
	 * @return void
	 */
	protected function flushSubscriptionStack()
	{
		$this->setNotifiedSubscriptionStack();
		$this->subscription_stack = array();
	}

	/**
	 * setNotifiedSubscriptionStack
	 *
	 * @return void
	 */
	protected function setNotifiedSubscriptionStack()
	{
		foreach ($this->subscription_stack as $sub_id) {
			prc_prices_drop_notified($sub_id);
		}
	}

	/**
	 * shouldParseAllProducts
	 *
	 * @param mixed $prd_ids
	 * @return bool détermine si la clé all est présente dans le tableau prd_ids passé en argument
	 */
	protected function shouldParseAllProducts($prd_ids)
	{
		return array_search('all', $prd_ids);
	}

	/**
	 * parsedProduct
	 *
	 * @param mixed $id Obligatoire, identifiant du produit à retourner
	 * @return null|array retourne le produit d'identifiant $id, ou null si le produit n'a pas encore été analysé
	 */
	protected function parsedProduct($id)
	{
		if (!$this->isAlreadyParsed($id)) {
			return null;
		}
		return $this->parsedProducts[$id];
	}

	/**
	 * parseProduct
	 *
	 * @param ProductPriceChanges $product
	 * @return void
	 */
	protected function parseProduct(ProductPriceChanges $product)
	{
		$parsed = $product->product();

		$currentPrice = $product->currentPrice();
		$lastPrice = $product->lastPrice();

		$parsed['price_ht'] = $currentPrice['promo']> 0 ?
							$currentPrice['promo'] :
							$currentPrice['price'];

		$parsed['price_max'] = $lastPrice['promo']> 0 ?
							$lastPrice['promo'] :
							$lastPrice['price'];

		$this->parsedProducts[$product->product('id')] = $parsed;
	}

	/**
	 * isAlreadyParsed
	 *
	 * @param mixed $id Obligatoire, identifiant de produit à analyser
	 * @return bool retourne vrai si l'identifiant passé en argument a déjà été analysée, false dans le cas contraire
	 */
	protected function isAlreadyParsed($id)
	{
		return array_key_exists($id, $this->parsedProducts);
	}

	/**
	 * shouldNotifyProduct
	 *
	 * @param PriceChanges $event
	 * @param mixed $prd_id Obligatoire, identifiant du produit
	 * @return bool true si les événements sur le produit doivent être notifiés, false dans le cas contraire
	 */
	protected function shouldNotifyProduct(PriceChanges $event, $prd_id)
	{
		if (!$event->hasProduct($prd_id)) {
			return false;
		}

		if (!$event->product($prd_id)->hasPriceDroped()) {
			return false;
		}

		return true;
	}
}
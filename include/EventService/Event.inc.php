<?php
namespace EventService;

/** \defgroup EventService Service d'événement
 * 	\ingroup system
 *  Ce module comprend toutes les classes nécessaires à la gestion des événements.
 *  @{
 */

/**
 * \class Event
 * \brief classe abstraite défini une interface commune pour tous les événements
 * Elle implémente l'interface native de php SplSubject
 */
abstract class Event implements \SplSubject
{
	protected $storage;

	public function __construct()
	{
		$this->storage = new \SplObjectStorage();
	}

	/**
	 * Cette fonction permet d'attacher un listener/observateur a un événement
	 * \param  SplObserver $observer Object qui réalisera des actions au moment de l'événement
	 */
	public function attach(\SplObserver $observer)
	{
		$this->storage->attach($observer);
	}

	/**
	 * Cette fonction permet de détaché un listener/observateur a un événement
	 * @param  SplObserver $observer Object a détacher
	 */
	public function detach(\SplObserver $observer)
	{
		if (!$this->storage->contains($observer)) {
			return;
		}

		$this->storage->detach($observer);
	}

	/**
	 * Cette fonction permet de notifié les Listeners/Observateur de l'événement
	 */
	public function notify()
	{
		if (!count($this->storage)) {
			return;
		}

		foreach ($this->storage as $observer) {
			$observer->update($this);
		}
	}
}

/// @}
<?php
namespace EventService;

use EventService\Dispatcher;

require_once('EventService/autoload.php');

/** \defgroup EventProvider Provisionneur d'évennement
 *	\ingroup EventService
 *  Ce module permet de notifier automatiquement les listeners d'un événement
 *
 * 	Utilisation basic :
 * 		// création de l'événement
 *  	$event = new Event($params);
 * 	 	EventProvider::emit($event);
 *
 * 	Utilisation Avancé (plus de controle sur les actions à envoyer):
 * 		// création de l'événement
 * 		$event = new Event($params);
 *
 * 		// création des listeners
 * 		$Listener1 = new Listener1;
 * 		$Listener2 = new Listener2;
 *
 * 		EventProvider::listen($event,$Listener1);
 * 		if( $control_if_have_to_notify ){
 * 			EventProvider::listen($event,$Listener2);
 * 		}
 * 		//notifier
 * 		EventProvider::emit($event);
 *  @{
 */

/**
 * \class EventProvider
 * \brief Cette classe static permet d'attaché et de notifié automatiquement les listners d'un événement elle sert de Facade au Dispatcher
 */
class EventProvider {
	/**
	 * Event dispatcher
	 *
	 * @var EventService::Dispatcher $dispatcher
	 */
	private $dispatcher;
	/**
	 * EventService instance
	 *
	 * @var EventService::EventProvider $_instance
	 */
	private static $_instance;
	/**
	 * Liste des événements avec en valeur la liste des listeners
	 * @var array $listeners
	 */
	private $listeners = array(
		'EventService\Order\Events\AfterPieceSet' => array(
			'EventService\Order\Listeners\NotifyAfterPieceSet'
		),
		'EventService\Products\Events\PriceChanges' => array(
			'EventService\Products\Listeners\EmailPriceDrop'
		)
	);

	/**
	 * __construct
	 *
	 * @param Dispatcher $dispatcher
	 * @return void
	 */
	public function __construct(Dispatcher $dispatcher)
	{
		$this->dispatcher = $dispatcher;
		$this->boot();
		static::$_instance = $this;
	}

	/**
	 * Intitialisation de la liaison entre les events et les listeners
	 *
	 * @return void
	 */
	protected function boot()
	{
		foreach ($this->listeners as $event => $listeners) {
			foreach ($listeners as $listener) {
				$this->dispatcher->listen($event, $listener);
			}
		}
	}

	/**
	 * __callStatic Fonction magic qui permet de gérer la Facade
	 *
	 * @param mixed $name Nom de la méthode
	 * @param mixed $arguments Les arguments de méthode
	 * @return mixed Résultat de la méthode
	 */
	public static function __callStatic($name, $arguments)
	{
		$callable = array(static::$_instance->dispatcher, $name);
		return call_user_func_array($callable, $arguments);
	}
}
/// @}
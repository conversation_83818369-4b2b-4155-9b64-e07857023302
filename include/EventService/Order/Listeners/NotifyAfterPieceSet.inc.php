<?php
namespace EventService\Order\Listeners;
use \Exception;

class NotifyAfterPieceSet{
	/** Cette fonction permet d'envoyer une notification après la mise à jour du numéro de pièce.
	 *  \param $event L'objet évènement AfterPieceSet
	 */
	public function handle($event){
		global $config;
		
		switch ($config['tnt_id']) {
			case 2: { // Envoi du devis
				require_once($config['site_dir'] .'/include/view.email.inc.php');
				if( !boero_send_devis($event->ord_id) ){
					throw new Exception(
						'Erreur envoie de devis pour boero, devis N:'.$event->ord_id . ', piece:'.$event->piece
					);
				}
				break;
			}
		}
	}
}
<?php
namespace EventService\Order\Events;

/**
 * \class StateUpdated
 * Événement lorsqu'une commande change de status
 */
class StateUpdated {
	/**
	 * Identifiant de la commande
	 *
	 * @var int $order_id
	 */
	public $order_id;
	/**
	 * Identifiant du statut avant update
	 *
	 * @var int $previous_state
	 */
	public $previous_state;
	/**
	 * Identifiant du nouveau statut après update
	 *
	 * @var int $new_state
	 */
	public $new_state;
	/**
	 * Détermine si il doit y avoir des notification
	 *
	 * @var bool $notify
	 */
	public $notify;
	/**
	 * Identifiant de l'utilisateur qui à lancer la mise à jour du status
	 *
	 * @var int $user_id
	 */
	public $user_id;
	/**
	 * Constructeur de la classe
	 *
	 * @param int $order_id Identifiant de la commande
	 * @param int $previous_state Identifiant du statut avant update
	 * @param int $new_state Identifiant du nouveau statut après update
	 * @param bool $notify Détermine si il doit y avoir des notification
	 * @param int $user_id Identifiant de l'utilisateur qui à lancer la mise à jour du status
	 */
	public function __construct( $order_id, $previous_state, $new_state, $notify, $user_id ){
		$this->order_id = $order_id;
		$this->previous_state = $previous_state;
		$this->new_state = $new_state;
		$this->notify = $notify;
		$this->user_id = $user_id;
	}
}
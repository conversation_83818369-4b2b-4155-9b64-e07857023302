<?php
namespace EventService\Order\Events;

/**
 * \class AfterPieceSet
 * événement lorsque la pièce d'une commande a été mis à jours
 */
class AfterPieceSet {
	public $ord_id; /// Identifiant de la commande
	public $piece; 	/// Piece de la commande

	/**
	 * Constructeur
	 * \param  $ord_id identifiant de la commande
	 * \param  $piece  piece de la commande
	 */
	public function __construct( $ord_id, $piece ){
		$this->ord_id = $ord_id;
		$this->piece = $piece;
	}
}
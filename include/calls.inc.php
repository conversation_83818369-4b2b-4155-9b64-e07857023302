<?php
// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');
require_once('couchDb.inc.php');

/** \defgroup model_reports_calls Appels téléphoniques
 *	\ingroup yuto crm
 *	Ce module comprend les fonctions nécessaires à la gestion des appels téléphoniques
 *	@{
 */

/** Cette fonction permet de tester l'existence d'un appel
 *	@param int $id Obligatoire, identifiant de l'appel
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_calls_exists( $id ){

	$results = CouchDB::create()->get(CLS_CALLS, $id);

	if( isset($results['_id']) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
	return false;
}

/** Cette fonction permet de récupère une liste d'appel
 *	@param int $id Optionnel, identifiant de l'appel
 *	@param int $offset Facultatif, permet de passer un nombre d'éléments lors de la récupération
 *	@param int $limit Facultatif : permet de donner un nombre d'élément à récupérer maximum
 *	@param int $gcl_author_id Facultatif : identifiant de l'auteur de l'appel
 * 	@param array $dates Facultatif : Tableau des dates pour le filtrage des données. date1 et date2 doivent obligatoirement être fournis.
 * 	@param int $gcl_dst_id Facultatif : Identifiant de l'utilisateur qui recoit l'appel (attention ne marche pas si on a mis un author_id)
 * 	@param bool $need_sync Facultatif : Retourne les appels pour lequel need_sync est a true ou false (attention ne marche pas si on a mis un author_id)
 *	@return array|bool Un tableau des éléments récupérés en cas de succès ou False en cas d'erreur
 */
function gcl_calls_get_by_view( $id="", $offset=0, $limit=0, $gcl_author_id=0, $dates=array(), $gcl_dst_id=0, $need_sync=null ){
	if( !is_numeric($offset) || $offset < 0 ){
		return false;
	}
	if (!is_numeric($limit) || $limit < 0){
		return false;
	}
	if (!is_numeric($gcl_author_id) || $gcl_author_id < 0){
		return false;
	}
	if( sizeof($dates) && !array_key_exists('date1', $dates) && !array_key_exists('date2', $dates) ){
		return false;
	}

	global $config;

	//Si on ne précise pas l'id, on récupère tous les documents via vue
	if (!$id || trim($id) == "") {
		{ // Prépare les paramètres de requête
			$startkey = $endkey = array($config['tnt_id']);
			$view = 'calls_by_date';

			if ($gcl_author_id) {
				$startkey[] = $gcl_author_id;
				$endkey[] = $gcl_author_id;
				$view = 'calls_by_date_and_author';
			}
			else if($gcl_dst_id){
				$startkey[] = $gcl_dst_id;
				$endkey[] = $gcl_dst_id;
				$view = 'calls_by_date_and_receiver';
			}
			else if($need_sync!==null){
				$startkey[] = $need_sync ? 1 : 0;
				$endkey[] = $need_sync ? 1 : 0;
				$view = 'calls_by_need_sync';
			}

			if (count($dates)) {
				if( !isset($dates['date1']) || !isdateheure($dates['date1']) ){
					$startkey[] = '1000-01-01 00:00:00';
				}else{
					$startkey[] = $dates['date1'];
				}

				if( !isset($dates['date2']) || !isdateheure($dates['date2']) ) {
					$endkey[] = '3000-01-01 00:00:00';
				}else{
					$endkey[] = $dates['date2'];
				}
			}
		}

		$call_params = array();
		$call_params['startkey'] = $startkey;
		$call_params['endkey'] = $endkey;
		$call_params['skip'] = $offset;
		$call_params['include_docs'] = true;
		$call_params['descending'] = true;

		if ($limit) {
			$call_params['limit'] = $limit;
		}

		$results = CouchDB::create()->getView($view, $call_params);

		//On ne récupère que les content et les identifiants de documents dans CouchDb
		$final = array();
		if( is_array( $results ) ){
			foreach( $results as $r ){
				$tmp = $r['doc']['content'];
				$tmp['_id'] = $r['doc']['_id'];

				$final[] = $tmp;
			}
		}

		if( $final === false ){
			return false;
		}

		//Puis le nombre de documents
		if (sizeof($final)){
			$final['total_rows'] = gcl_calls_get_count($gcl_author_id, $dates);
		} else {
			$final['total_rows'] = 0;
		}

		return $final;
	} else { //Sinon, on récupère le document
		return CouchDB::create()->get(CLS_CALLS, $id);
	}
}

/** Cette fonction permet de récupérer le premier appel passé à un client
 *	@param int $usr_dst Obligatoire, identifiant d'un client
 * 	@param int $author_id Optionnel, identifiant de l'auteur de l'appel
 * 	@return array Un tableau contenant les informations sur le premier appel
 */
function gcl_calls_get_first_call($usr_dst, $author_id=0){
	if (!is_numeric($usr_dst) || $usr_dst <= 0){
		return false;
	}

	if (!is_numeric($author_id) || $author_id < 0){
		return false;
	}

	global $config;

	$call_params = array();
	$call_params['startkey'] = array($config['tnt_id'], $usr_dst);
	$call_params['endkey'] = array($config['tnt_id'], $usr_dst);
	$call_params['include_docs'] = true;
	$call_params['descending'] = false;

	$res = CouchDB::create()->getView( 'calls_by_date_and_receiver', $call_params );
	if (!is_array($res) || !isset($res[0]['key']['2'])) {
		return false;
	}

	return $res[0]['key']['2'];
}

/** Cette fonction permet de récupère un appel seulement
 * 	@param string $id Optionnel, identifiant de l'appel
 * 	@return array Un tableau contenant les informations sur le appel ou false
 */
function gcl_calls_get($id){

	$results = CouchDB::create()->get(CLS_CALLS, $id);

	if( !$results || !isset($results['_id']) ){
		return false;
	}

	return $results;
}

/** Cette fonction permet de récupérer la liste des auteurs
 * 	@return array Un tableau contenant la liste des auteurs sous la forme suivante :
 * 			array( array(
 * 				- firstname : Nom de l'auteur
 * 				- lastname : Prénom de l'auteur
 * 				- society : Nom de la société
 * 			), ....)
 */
function gcl_calls_get_authors(){
	global $config;

	$view = 'call_authors';

	$call_params = array();
	$call_params['startkey'] = array($config['tnt_id']);
	$call_params['endkey'] = array($config['tnt_id']);
	$call_params['group'] = true;

	$all_authors = array();
	$all_authors_id = CouchDB::create()->getView($view, $call_params);
	if (!is_array($all_authors)) {
		return array();
	}

    // Crée une liste d'auteurs sans doublon
    if( is_array( $all_authors_id ) ){
        foreach( $all_authors_id as $key => $value ){
            if( isset($all_authors[$value['key'][1]]) ){
                // Evite des appels récurrents inutiles à gu_users_get
                continue;
            }
            $r_auth = gu_users_get($value['key'][1]);
            if( $an_author = ria_mysql_fetch_assoc($r_auth) ){
                $all_authors[$value['key'][1]] = array(
                    'firstname' => $an_author['adr_firstname'],
                    'lastname' => $an_author['adr_lastname'],
                    'society' => $an_author['society']
                );
            }
        }
    }

    return array_msort( $all_authors, array('society' => SORT_ASC, 'firstname' => SORT_ASC, 'lastname' => SORT_ASC) );

}

/** Cette fonction permet la récupération du nombre d'appels en fonction des paramètres donnés
 * @param int $gcl_author_id - Facultatif - Identifiant de l'auteur des appels
 * @param array $dates - Facultatif - Tableau des dates pour le filtrage des données
 * @param int $gcl_dst_id - Facultatif - Identifiant de l'utilisateur qui recoit l'appel (attention ne marche pas si on a mis un author_id)
 * @return int le nombre d'appels
 */
function gcl_calls_get_count( $gcl_author_id=0, $dates=array(), $gcl_dst_id=0) {
	if (!is_numeric($gcl_author_id) || $gcl_author_id < 0){
		return false;
	}
	if (sizeof($dates) && !array_key_exists('date1', $dates) && !array_key_exists('date2', $dates)){
		return false;
	}

	global $config;

	{ // Prépare les paramètres de requête
		$startkey = $endkey = array($config['tnt_id']);
		$view = 'calls_by_date';

		if ($gcl_author_id) {
			$startkey[] = $gcl_author_id;
			$endkey[] = $gcl_author_id;
			$view = 'calls_by_date_and_author';
		}
		else if($gcl_dst_id){
			$startkey[] = $gcl_dst_id;
			$endkey[] = $gcl_dst_id;
			$view = 'calls_by_date_and_receiver';
		}

		if (count($dates)) {
			$startkey[] = $dates['date1'];
			$endkey[] = $dates['date2'];
		}
	}

	$call_params = array();
	$call_params['startkey'] = $startkey;
	$call_params['endkey'] = $endkey;

	return CouchDB::create()->getViewCount($view, $call_params);
}

/** Cette fonction permet de récupère une liste d'appel
 *	@param int $id Obligatoire : identifiant de l'appel
 *	@param bool $need_sync Obligatoire : permet de récupérer les appels à synchroniser ou non
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_calls_set_need_sync( $id, $need_sync=true ){

	$results = CouchDB::create()->get(CLS_CALLS, $id);

	if( !isset($results['_id']) ){
		return false;
	}

	$results['need_sync'] = $need_sync ? 1 : 0;

	if( CouchDB::create()->update(CLS_CALLS, $id, $results) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
}

/** Cette fonction permet de récupère une liste d'appel
 *	@param int $id Obligatoire : identifiant de l'appel
 *	@param bool $is_sync Obligatoire : permet de récupérer les appels à synchroniser ou non
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_calls_set_is_sync( $id, $is_sync=true ){

	$results = CouchDB::create()->get(CLS_CALLS, $id);

	if( !isset($results['_id']) ){
		return false;
	}

	$results['is_sync'] = $is_sync ? 1 : 0;

	if( CouchDB::create()->update(CLS_CALLS, $id, $results) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
}

/** Cette fonction permet de mettre a jour la date de modification
 *	@param int $id Obligatoire : identifiant de l'appel
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_calls_set_date_modified($id){

	$results = CouchDB::create()->get(CLS_CALLS, $id);

	if( !isset($results['_id']) ){
		return false;
	}

	if( CouchDB::create()->update(CLS_CALLS, $id, $results) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
}

/** Cette fonction permet d'ajouter une ligne dans les rapports
 *	@param int $author_id Obligatoire, Identifiant de l'utilisateur créateur de l'appel
 *	@param int $usr_dst Obligatoire, Identifiant de l'utilisateur destinataire de l'appel
 *	@param string $date_created Obligatoire, Date de l'appel
 *	@param int $type Obligatoire, type de l'appel : INCOMING_TYPE = 1, OUTGOING_TYPE = 2, MISSED_TYPE = 3, VOICEMAIL_TYPE = 4
 *	@param string $phone Obligatoire, numéro de téléphone
 *	@param int $duration Obligatoire, durée de l'appel exprimé en secondes
 *	@param $callback_date Facultatif, date de rappel si défini
 *	@param bool $answered Facultatif, Boolean, détermine si l'appel à été répondu ou non
 *	@param bool $dst_free Facultatif, Boolean, détermine si le destinaire était libre pour réponse à l'appel
 *	@param string $comment Facultatif, commentaire ajouter sur l'appel
 *	@param array $fields Facultatif, champs libres contenant des informations complementaires
 *	@param int $android_id Facultatif, ajout de l'id pour la version android, devices_id + id de l'historiques d'appels
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function gcl_calls_add( $author_id, $usr_dst, $date_created, $type, $phone, $duration, $callback_date=null, $answered=null, $dst_free=null, $comment='', $fields=array(), $android_id=false ){
	if( !gu_users_exists( $author_id ) ){
		return false;
	}
	if( !gu_users_exists( $usr_dst ) ){
		return false;
	}
	if( !isdateheure($date_created) ){
		return false;
	}
	if( !is_array($fields) ){
		return false;
	}
	if( !is_numeric($type) ){
		return false;
	}
	if( $callback_date && !isdateheure($callback_date) ){
		return false;
	}

	$values = array();

	// récupère des informations complémentaire pour les stockers sur le couchdb
	$rauthor = gu_users_get($author_id);
	if( !$rauthor ) return false;
	$author = ria_mysql_fetch_assoc($rauthor);

	$values['gcl_author_ref'] = $author['ref'];
	$values['gcl_author_email'] = $author['email'];
	$values['gcl_author_name'] = trim($author['adr_firstname'].' '.$author['adr_lastname'].' '.$author['society']);

	$rusr = gu_users_get($usr_dst);
	if( !$rusr ) return false;
	$usr = ria_mysql_fetch_assoc($rusr);

	$values['gcl_usr_ref'] = $usr['ref'];
	$values['gcl_usr_email'] = $usr['email'];

	// ajout des datas normal
	$values['gcl_author_id'] = $author_id;
	$values['gcl_usr_dst'] = $usr_dst;
	$values['gcl_type'] = $type;
	$values['gcl_date_created'] = dateheureparse($date_created);
	$values['gcl_phone'] = $phone;
	$values['gcl_duration'] = $duration;
	$values['gcl_comment'] = trim($comment);

	if( $android_id ){
		$values['gcl_android_id'] = trim($android_id);
	}

	if( $answered!==null ){
		$values['gcl_answered'] = $answered;
	}
	if( $dst_free!==null ){
		$values['gcl_dst_free'] = $dst_free;
	}
	if( $callback_date!==null && isdateheure($callback_date) ){
		$values['gcl_callback_date'] = dateheureparse($callback_date);
	}
	$values['_fields'] = $fields;

	$results = CouchDB::create()->add(CLS_CALLS, $values);

	if( isset($results['ok']) ){
		return $results['id'];
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}

	return false;
}

/** Cette fonction permet de mettre à jour une ligne d'un appel
 *	@param int $id Identifiant de l'appel à mettre à jour
 *	@param int $author_id Identifiant de l'utilisateur créateur de l'appel
 *	@param int $usr_dst Identifiant de l'utilisateur destinataire de l'appel
 *	@param int $type Type de l'appel : INCOMING_TYPE = 1, OUTGOING_TYPE = 2, MISSED_TYPE = 3, VOICEMAIL_TYPE = 4
 *	@param string $phone Numéro de téléphone
 *	@param int $duration Durée de l'appel exprimé en secondes
 *	@param $callback_date Date de rappel si défini
 *	@param bool $answered Détermine si l'appel à été répondu ou non
 *	@param bool $dst_free Détermine si le destinaire était libre pour réponse à l'appel
 *	@param string $comment Commentaire ajouter sur l'appel
 *	@param array $fields Facultatif, champs libres contenant des informations complementaires
 *	@param $android_id Facultatif, ajout de l'id pour la version android, devices_id + id de l'historiques d'appels
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function gcl_calls_upd( $id, $author_id, $usr_dst, $type, $phone, $duration, $callback_date=null, $answered=null, $dst_free=null, $comment=null, $fields=array(), $android_id=false ){
	if( !gcl_calls_exists( $id ) ){
		return false;
	}
	if( !gu_users_exists( $author_id ) ){
		return false;
	}
	if( !gu_users_exists( $usr_dst ) ){
		return false;
	}
	if( !is_numeric($type) ){
		return false;
	}
	if( !is_array($fields) ){
		return false;
	}
	if( $callback_date && !isdateheure($callback_date) ){
		return false;
	}

	$values = array();
	$values['gcl_author_id'] = $author_id;
	$values['gcl_usr_dst'] = $usr_dst;
	$values['gcl_type'] = $type;
	$values['gcl_phone'] = $phone;
	$values['gcl_duration'] = $duration;
	$values['gcl_comment'] = trim($comment);

	if( $answered!==null ){
		$values['gcl_answered'] = $answered;
	}
	if( $dst_free!==null ){
		$values['gcl_dst_free'] = $dst_free;
	}
	if( $callback_date!==null && isdateheure($callback_date) ){
		$values['gcl_callback_date'] = dateheureparse($callback_date);
	}
	$values['_fields'] = $fields;

	if( $android_id ){
		$values['gcl_android_id'] = trim($android_id);
	}

	if( CouchDB::create()->update(CLS_CALLS, $id, $values) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($values, true));
	}

	return false;
}

/** Cette fonction permet de supprimer un appel. La suppression est réalisée de façon virtuelle (corbeille)
 * 	@param int $id Obligatoire, identifiant de l'appel à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_call_del( $id ){
	return gcl_calls_del( $id );
}

/** Cette fonction permet de supprimer un appel. La suppression est réalisée de façon virtuelle (corbeille)
 * 	@param int $id Obligatoire, identifiant de l'appel à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function gcl_calls_del( $id ){
	if( !gcl_calls_exists( $id ) ){
		return false;
	}

	$results = CouchDB::create()->delete(CLS_CALLS, $id);

	if( $results ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}

	return false;
}

/// @}

// \endcond

<?php

namespace Pdf;

use \DateTime;

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');

/** \devgroup ReturnPdf PDF Bon de retour
 * \ingroup PieceDeVente
 */
class ReturnPdf extends PieceDeVente
{
	/** Cette fonction permet d'initialiser la génération PDF d'un bon de retour.
	 * @param array $return Tableau contenant les données sur le bon de retour
	 *
	 * @return void
	 */
	public function __construct( array $return ){
		parent::__construct();
		$this->data['ord'] = $return;
	}

	/**
	 * Function called before PDF generation
	 *
	 * @return void
	 */
	public function bootstrap()
	{
		if (!isset($this->data['user'])) {
			$this->loadUserInfo();
		}

		if (!isset($this->data['addresses'])) {
			$this->loadOrdersAdresses();
        }

		parent::bootstrap();

		$this->SetSubject(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('subject')));
		$this->SetTitle(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('name')));
	}

	/** Retourne le tableau des options par défaut.
	 *
	 * @return array Tableau des options par défaut
	 */
	protected function getDefaultOptions()
	{
		global $config;

		$data = $deposit = array();

		// Le client peut "surcharger" la configuration de génération des devis PDF.
		// On doit alors vérifier si c'est le cas et si oui, récupérer la nouvelle configuration.
		if( $deposit_id = $this->data['ord']['dps_id'] ){
			$deposit = ria_mysql_fetch_assoc(
				prd_deposits_get($deposit_id)
			);

			if( ($quote_config = $deposit['quote_config']) ){
				$data = (array) json_decode($quote_config);
			}
		}

		return array_merge(array(
			'subject' => '',
			'name' => $config['pdf_generation_bl_name'],
			'logo' => $config['pdf_generation_bl_logo'],
			'logo_disposition' => $config['pdf_generation_bl_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_bl_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_bl_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_bl_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_bl_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_bl_display_payment']) ? $config['pdf_generation_bl_display_payment'] : null,
			'header' => $config['pdf_generation_bl_header'],
			'header_content' => $config['pdf_generation_bl_header_content'],
			'footer' => $config['pdf_generation_bl_footer'],
			'footer_content' => $config['pdf_generation_bl_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_bl_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_bl_prd_barcode'],
			'prd_img' => $config['pdf_generation_bl_prd_img'],
			'font_size' => $config['pdf_generation_bl_font_size'],
			'ref' => '',
		), $data);
	}

	/**
	 * Contient le corps du pdf dans ce cas le tableau
	 *
	 * @return void
	 */
	public function body()
	{
		$this->table->generateTable();
	}

	/**
	 * Configure le tableau pour avoir un comportement par défaut colonne :
	 * - ref
	 * - Désignation
	 * - Quantité
	 * - Prix unitaire brut
	 * - Remise
	 * - prix unitaire net
	 * - montant total ht
	 *
	 * @return \Pdf\ProductTable
	 */
	public function defaultProductTable()
	{
		global $config;

		$this->requireData('ord');
		$this->setCurrency();

		$this->table()->withTbodyFontSize($this->requireOption('font_size'));

		$this->row_with_same_height = true;

		if( $this->getOption('prd_img') && isset($config['img_sizes']['small']) ){
			$this->table()->withRowHeight($config['img_sizes']['small']['height'] * 0.15);
			require_once('Pdf/Column.php');
			require_once('Pdf/ImagePdf.php');

			$that = &$this;

			$this->table()->addColumn(new \Pdf\Column('Image', 'L', ($config['img_sizes']['small']['width'] * 0.15), function ($p) use ($that) {
				global $config;

				if (!$p['img_id'] || !file_exists($config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'])) {
					return '';
				}

				$that->Image(
					$config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'],
					$that->getX(),
					$that->getY() + 1,
					($config['img_sizes']['small']['width'] * 0.15),
					($config['img_sizes']['small']['height'] * 0.15)
				);
				return '';
			}));
		}

		$this->table()->addColumn(new Column('Référence', 'L', 15, function($p){
			return $p['id'] == 0 ? '' : $p['ref'] ;
		}));

		$this->table()->addColumn(new Column('Désignation', 'L', $this->getOption('prd_reduce') ? 40 : 50, function($p){
			$compl_name = '';
			if( isset($p['group_id'], $p['group_parent_id']) && !is_null($p['group_id']) && !is_null($p['group_parent_id']) ){
				$compl_name = '     ';
			}

			$is_colisage = isset( $p['col_id'] ) && is_numeric( $p['col_id'] ) && $p['col_id']  > 0;

			if( $is_colisage ){
				$r_colisage = prd_colisage_types_get(parseInt($p['col_id']));
				$colisage = ria_mysql_fetch_assoc($r_colisage);
			}else{
				$is_colisage = false;
			}

			// Remplace le retour chariot par un saut ligne suivi d'un retour chariot.
			$p['details'] = str_replace( "\r", "\n\r", $p['details'] );

			$comment = '';
			if( $p['id'] != 0 && $p['details'] ){
				$comment = $p['details'];
			}

			if( $p['notes'] ){
				$comment .= $p['notes'];
			}


			$product_name  = $p['id'] !=  0 ? $p['name'] : $p['details'];

			if( $is_colisage ){
				$product_name .= ' (Conditionnement : '.parseInt($colisage['qte']).')';
			}

			if( trim($comment) != '' ){
				$product_name .= "\n".$comment;
			}

			return $compl_name.$product_name;
		}));

		// Colonne quantité
		$this->table()->addColumn(new Column('Qté', 'C', 10, function($p){
			if ($p['id'] == 0){
				return null;
			} else {
				return floatval( $p['qte'] );
			}
		}));

		// Colonne Prix Unitaire HT du produit
		$this->table()->addColumn(new Column('P.U. HT', 'R', 10, function($p){
			if( $p['id'] == 0 ){
				return null;
			}else{
				$col_qte = 1;

				if( isset($p['col_id']) && is_numeric($p['col_id']) && $p['col_id'] > 0 ){
					$col_qte = prd_colisage_types_get_qte( $p['col_id'] );

					if( !is_numeric($col_qte) || $col_qte <= 0 ){
						$col_qte = 1;
					}
				}

				return ria_number_french( $p['price_ht'] * $col_qte );
			}
		}));

		if( $this->getOption('prd_ecotaxe') ){
			// Colonne Prix Unitaire HT du produit
			$this->table()->addColumn(new Column('Eco-part.', 'R', 10, function($p){
				if($p['ecotaxe'] > 0 ){
					return ria_number_french($p['ecotaxe']);
				}else{
					return null;
				}
			}));
		}

		// Colonne remise (champ avancé _FLD_BL_LINE_DISCOUNT ou remise supplémentaire sur produit sur la ligne de commande)
		if( $this->getOption('prd_reduce') ){
			$this->table()->addColumn(new Column('Remise', 'C', 10, function ($product) {
        return null;
			} ));
		}

		// Colonne total HT de la ligne de commande
		$this->table()->addColumn(new Column('Montant HT', 'R', 15, function($p){
			$col_qte = 1;

			if( isset($p['col_id']) && is_numeric($p['col_id']) && $p['col_id'] > 0 ){
				$col_qte = prd_colisage_types_get_qte( $p['col_id'] );

				if( !is_numeric($col_qte) || $col_qte <= 0 ){
					$col_qte = 1;
				}
			}
			$total_ht_displayed = $p['price_ht'] * $p['qte'] * $col_qte;

			if( $p['id'] == 0 ){
				return null;
			} else {
				return ria_number_french($total_ht_displayed);
			}
		}));

		// Colonne code barre du produit
		if( $this->getOption('prd_barcode') ){
			$this->table()->addColumn(new \Pdf\Column('Code EAN', 'ean', 19, function ($p) {
				return prd_products_get_barcode($p['id']);
			}), 2);
		}

		return $this->table();
	}

	/**
	 * Récupère les informations de l'utilisateur
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadUserInfo()
	{
		$this->requireData('ord');

		$r_user = gu_users_get($this->data['ord']['usr_id']);
		if ($r_user && ria_mysql_num_rows($r_user)) {
			$this->withUser(ria_mysql_fetch_assoc($r_user));
		}
	}

	/**
	 * Récupèrer les informations des adresses
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadOrdersAdresses()
	{
		$this->data['addresses'] = ord_orders_address_load($this->requireData('ord'));
		if (false !== $this->data['addresses']){
			$this->withDlvAdress($this->data['addresses']['delivery']);
			$this->withInvAdress($this->data['addresses']['invoice']);
			return;
		}

        $this->data['addresses'] = array(
			'type' => 'postal',
			'invoice' => array(
				'type' => 'facturation',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			),
			'delivery' => array(
				'type' => 'livraison',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			)
		);
	}

	/**
	 * Génèrer la ligne d'inforamtion utilisateur
	 *
	 * @return void
	 */
	protected function userInfoRow()
	{
		$this->requireData('ord');

		$this->SetY($this->getY() + 6);
		$this->resetX();
		$this->SetFont($this->font_family, 'B', 9);
    $this->Cell(30, 6, utf8_decode('RETOUR N°'), 1, 0, 'C');
		$this->Cell(20, 6, utf8_decode('DATE'), 1, 0, 'C');
		$this->Cell(47, 6, utf8_decode('REFERENCE DU RETOUR'), 1, 0, 'C');
		$this->Cell(20, 6, utf8_decode('PAGE N°'), 1, trim($this->data['user']['ref']) == '', 'C');

		if (trim($this->data['user']['ref']) != '') {
			$this->Cell(40, 6, utf8_decode('CLIENT'), 1, 1, 'C');
		}

		//content
		$this->SetFont($this->font_family, '', 9);
		$date = new DateTime(isset($this->data['ord']['date_en']) ? $this->data['ord']['date_en'] : $this->data['ord']['date']);
		$this->Cell(30, 6, utf8_decode($this->data['ord']['piece'] != "" ? $this->data['ord']['piece'] : $this->data['ord']['id']), 1, 0, 'C');
		$this->Cell(20, 6, utf8_decode($date->format('d/m/y')), 1, 0, 'C');
		$this->Cell(47, 6, utf8_decode($this->data['ord']['ref']), 1, 0, 'C');
		$this->Cell(20, 6, utf8_decode($this->PageNo()), 1, trim($this->data['user']['ref']) == '', 'C');

		if (trim($this->data['user']['ref']) != '') {
			$this->Cell(40, 6, utf8_decode($this->data['user']['ref']), 1, 1, 'C');
		}

		if ($this->shouldShowTaxcode() && !is_null($this->getData('user')) && trim($this->data['user']['taxcode'])) {
			$this->Cell(50, 6, utf8_decode('N° intracommunautaire : ' . $this->data['user']['taxcode']), 0, 1, 'L');
		} else {
			$this->Cell(0, 6, '', 0, 1, 'L');
		}
	}

	/**
	 * Génèrer le total de page
	 *
	 * @return void
	 */
	protected function PageCount()
	{
		$this->SetXY(20, 30);
		$this->Cell(23, 10, utf8_decode('Page ' . $this->PageNo()), 0, 0, 'C');
	}

	/**
	 * Génère la page du total de la commande
	 *
	 * @return void
	 */
	protected function generateTotalPage()
	{
		$this->requireData('ord');

		$y = $this->GetY() + 4;

		$this->SetY($y);

		$end_y = $y;
		$this->SetFont($this->font(), 'B', 8);

		//header
		$this->Cell(15, 7, utf8_decode('CODE'), 1, 0, 'L');
		$this->Cell(22, 7, utf8_decode('BASE'), 1, 0, 'C');
		$this->Cell(13, 7, utf8_decode('TAUX'), 1, 0, 'C');
		$this->Cell(27, 7, utf8_decode('MONTANT'), 1, 1, 'R');
		$end_y += 7;
		//row
		$this->SetFont($this->font(), '', 9);
		$first = 'Tva';
		$total = 0;
		foreach ($this->taxes()->tva() as $rate => $tva) {
			$this->Cell(15, 5, utf8_decode($first), 1, 0, 'L');
			$this->Cell(22, 5, $this->price($tva['base']).' '.$this->currency, 1, 0, 'R');
			$this->Cell(13, 5, (($rate - 1) * 100) . ' %', 1, 0, 'C');
			$this->Cell(27, 5, $this->price($tva['amount']).' '.$this->currency, 1, 1, 'R');
			$first = '';
			$total += $tva['amount'];
			$end_y += 5;
		}
		//row
		$eco = $this->taxes()->ecotaxe();
		$rate = 0;
		if ($eco['base'] > 0) {
			$rate = $eco['amount'] * 100 / $eco['base'];
		}
		$this->Cell(15, 5, utf8_decode('Eco'), 1, 0, 'L');
		$this->Cell(22, 5, $this->price($eco['base']).' '.$this->currency, 1, 0, 'R');
		$this->Cell(13, 5, $rate. ' %', 1, 0, 'C');
		$this->Cell(27, 5, $this->price($eco['amount']).' '.$this->currency, 1, 1, 'R');
		$total += $eco['amount'];
		$end_y += 5;
		//row
		$this->Cell(15, 7, utf8_decode('TOTAL :'), 1, 0, 'L');
		$this->Cell(35, 7, utf8_decode('Tva+Eco-participation'), 1, 0, 'R');
		$this->Cell(27, 7, $this->price($total).' '.$this->currency, 1, 1, 'R');
		$end_y += 7;

		$this->setXY(90, $y);
		$this->SetFont($this->font(), 'B', 8);
		$this->Cell(37, 7, utf8_decode('TOTAL H.T.'), 1, 0, 'C');
		$this->Cell(37, 7, utf8_decode('TOTAL T.T.C.'), 1, 0, 'C');
		$this->SetFont($this->font(), 'B', 9);
		$this->Cell(37, 7, utf8_decode('NET A PAYER'), 1, 0, 'C');

		$this->setXY(90, $y + 7);
		$this->SetFont($this->font(), '', 9);
		$this->Cell(37, 7, $this->price($this->data['ord']['total_ht'] + $eco['base']).' '.$this->currency, 1, 0, 'R');
		$this->Cell(37, 7, $this->price($this->data['ord']['total_ttc']).' '.$this->currency, 1, 0, 'R');
		$this->SetFont($this->font(), 'B', 9);
		$this->Cell(37, 7, $this->price($this->data['ord']['total_ttc']).' '.$this->currency, 1, 0, 'R');

		$this->setXY(90, $y + 14);


		if( $this->getOption('display_payment') ){
			$name = $this->getData('pay_name');
			$ordInstallements = new OrderInstallements($this, $this->data['ord'], $this->getData('installments'), $name);

			if (is_null($name)) {
				$name = $ordInstallements->paymentName();
				if (trim($name) == '') {
					$params = array(0, $this->order['pay_id']);
					if ($this->order['pay_id'] <= 0) {
						$params = array($this->order['usr_id']);
					}
					$r_pay = call_user_func_array('gu_users_payment_types_get', $params);
                    $this->data['pay_name'] = '';
					if ($r_pay && ria_mysql_num_rows($r_pay)) {
						$pay = ria_mysql_fetch_assoc($r_pay);
                        $this->data['pay_name'] = gu_users_payment_types_view($pay);
					}
				}
			}

			if (trim($this->data['pay_name']) != "") {
				$this->SetFont($this->font(), 'B', 7);
				$this->Cell(40, 7, utf8_decode('Conditions de règlement :'), 0, 2, 'C');
				$this->SetFont($this->font(), '', 6);

				$this->Cell(40, 7, utf8_decode($this->data['pay_name']), 0, 1, 'C');

				$ordInstallements->generate($this->getX(), $this->getY() - 5);
			}
		}

		$this->SetY($end_y);
	}

	public function blocHeader()
	{
		global $config;

		$height = 5;
		$this->Cell(50, 4, '', 0, 2);
		if ($this->getOption('header')){
			$this->SetFont($this->font(), '', 9);
			$line_height = 4;
			$w=$this->w-$this->rMargin-$this->x;
			$height = ceil(($this->GetStringWidth(iconv('utf8', 'windows-1252', $this->requireOption('header_content'))) / $w)) * $line_height;

			$this->MultiCell(0, 4, iconv('utf8', 'windows-1252', $this->requireOption('header_content')), 0, 2, '');
		}

		$this->SetY($this->GetY() + $height);
		$this->SetFont($this->font(), 'B', 20);
		$this->MultiCell(0, 4, utf8_decode('Bon de retour'), 0, 2, '');
	}

	public function blocFooter()
	{
		$y = $this->getY() + 4;
		$this->SetY($y);

		if ($this->getOption('footer')){
			$this->SetFont($this->font(), '', 9);
			$this->MultiCell(0, 4, iconv('utf8', 'windows-1252', $this->requireOption('footer_content')), 0, 2, '');
			$this->Cell(0, 2, '', 0, 2);
		}

		$this->SetFont($this->font(), 'B', 9);
		$this->Multicell(0, 4, utf8_decode('Pour le client,'."\n".'Signature précédée de la mention'."\n".'"Lu et Approuvé, Bon pour Accord"'), 0, 2, '');
	}
}
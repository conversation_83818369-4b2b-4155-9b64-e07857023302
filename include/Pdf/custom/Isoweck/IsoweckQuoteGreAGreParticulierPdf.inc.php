<?php

/** \file Pdf.inc.php
 * 	Ce fichier ce charge de générer le PDF d'un devis ou d'une commande spécifique à Isoweck.
 */
namespace Pdf;

use DateTime;


require_once('users.inc.php');
require_once('orders.inc.php');
require_once('fields.inc.php');
require_once('Barcode.inc.php');

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');



class IsoweckQuoteGreAGreParticulierPdf extends PieceDeVente {
	const ENDPAGE = 220;
	const TOPPAGE = 7.26;
	const HORIZONTALMARGIN = 5.25;

	// Tableau des produits composé des colonnes :
	const LINEREFERENCE = 288;
	const LINEDESIGNATION = 855;
	const LINERESISTANCE = 129;
	const LINEEPAISSEUR = 147;
	const LINEQUANTITE = 208;
	const LINEUNITE = 120;
	const LINEPRIXUNITAIRE = 187;
	const LINEMONTANT = 330;
	// Cette variable est chargée en même temps que l'on parcours les lignes du devis
	// L'index correspond au taux de TVA
	// La valeur est un tableau comme ceci [total ht, total tva]
	private $ar_tva = []; ///< Contient les TVA appliqués sur les lignes du devis

	/** Charge le header sur toutes les pages du PDF. */
	public function Header(){

		// Bloc adresse client
		$this->SetXY(self::rpp(1257), self::rpp(400));
		// Récupération de l'adresse de facturation	selon les champs avancés
		$this->SetFont('Arial', '', '9');
		$adrinv = self::tranformUpper( $this->data['addresses']['invoice'] );
		$attn_inv = $adrinv['firstname'].' '.$adrinv['lastname'];
		if( trim($attn_inv) == '' ){
			$attn_inv = $adrinv['society'];
		}
		$attn_inv = trim($attn_inv);
		$this->Cell(0, self::rpp(53), self::conv($attn_inv), 0, 2, '', false);
		if (trim($adrinv['address1']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv(strtoupper2($adrinv['address1'])), 0, 2, '', false);
		}
		if (trim($adrinv['address2']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv(strtoupper2($adrinv['address2'])), 0, 2, '', false);
		}
		$this->Cell(0, self::rpp(53), self::conv($adrinv['postal_code'].' '.strtoupper2($adrinv['city'])), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);



		// Logo en haut à gauche du devis
		$this->SetXY( self::HORIZONTALMARGIN, self::TOPPAGE );
		$this->Image( dirname(__DIR__)."/Isoweck/images/Isoweck_logo.jpg", $this->GetX(), $this->GetY(), self::rpp(498), self::rpp(139.8));

		// Bloc adresse d'Isoweck
		$this->SetFont('Arial', '', '9');
		$this->SetXY(self::HORIZONTALMARGIN, $this->GetY() + self::rpp(139.8) + self::rpp(60));
		$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['address1']), 0, 2, '', false);
		if (trim($this->data['owner']['address2']) !== '') {
			$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['address2']), 0, 2, '', false);
		}
		$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['zipcode'].' '.$this->data['owner']['city']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('N° Siret : 348 ************'), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('Téléphone : '.$this->data['owner']['phone']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('Email : '.$this->data['owner']['email']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), 'Site : www.isoweck.com', 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);

		// Affiche les informations du devis
		$this->Cell(0, self::rpp(53), self::conv('Lot N° : '.$this->data['ord']['NumeroLotChantier']), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);

		// Affiche les informations de l'adresse du chantier
		$this->Cell(0, self::rpp(53), self::conv('Adresse chantier :'), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);
		if (trim($this->data['ord']['AdresseChantier']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv(strtoupper2($this->data['ord']['AdresseChantier'])), 0, 2, '', false);
		}
		if (trim($this->data['ord']['ComplementAdresseChantier']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv(strtoupper2($this->data['ord']['ComplementAdresseChantier'])), 0, 2, '', false);
		}
		$this->Cell(0, self::rpp(53), self::conv($this->data['ord']['CodePostalChantier'].' '.strtoupper2($this->data['ord']['VilleChantier'])), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);

		// Affiche les informations du devis
		$this-> SetFont('Arial', 'B', 16);
		$this->Cell(0, self::rpp(81), 'Devis', 0, 2, '', false);
		$this->SetFont('Arial', '', '9');
		$seller_name = $this->data['seller']['adr_firstname'].' '.$this->data['seller']['adr_lastname'];
		$seller_phone = trim($this->data['seller']['mobile']) != '' ? $this->data['seller']['mobile'] : $this->data['seller']['phone'];

		$this->Cell(0, self::rpp(53), self::conv($seller_name.' '.$seller_phone), 0, 2, '', false);

		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);

		$this-> SetFont('Arial', 'B', 9);
		$this->Cell(self::rpp(434), self::rpp(64), self::conv('Devis N°'), 'TLB', 0, 'C', false);
		$this->Cell(self::rpp(495), self::rpp(64), self::conv('DATE'), 'TLB', 0, 'C', false);
		$this->Cell(self::rpp(527), self::rpp(64), self::conv('REFERENCE CHANTIER'), 'TLB', 0, 'C', false);
		$this->Cell(self::rpp(572), self::rpp(64), self::conv('CLIENT'), 'TLBR', 2, 'C', false);
		$this->SetX(self::HORIZONTALMARGIN);

		$this-> SetFont('Arial', '', 9);
		$this->Cell(self::rpp(434), self::rpp(86), self::conv($this->data['ord']['id']), 'LB', 0, 'C', false);
		$this->Cell(self::rpp(495), self::rpp(86), self::conv(date('d/m/Y',strtotime($this->data['ord']['date']))), 'LB', 0, 'C', false);
		$this->Cell(self::rpp(527), self::rpp(86), self::conv($this->data['ord']['ref']), 'LB', 0, 'C', false);
		$this->Cell(self::rpp(572), self::rpp(86), self::conv($this->data['user']['ref']), 'LBR', 2, 'C', false);

		$this->SetX(self::HORIZONTALMARGIN);
		$this->Cell(0, self::rpp(125), '', 0, 2, 'C', false);
	}

	public function Footer(){
		$this->SetXY( self::HORIZONTALMARGIN, self::rpp(3006));
		$this->Image( dirname(__DIR__).'/Isoweck/images/Isoweck_square_group_logo.jpg', $this->GetX(), $this->GetY(), self::rpp(360), self::rpp(156.8));

		$this->SetFont( 'Arial', 'BI', 9 );
		$this->SetXY( self::HORIZONTALMARGIN, self::rpp(3006) + self::rpp(156.8));
		$this->Cell(0, self::rpp(53), 'Devis valable 30 jours', 0, 2, '', false);
		$this->Cell(0, self::rpp(40), '', 0, 2, '', false);

		$this->SetFont( 'Arial', '', 6.5 );
		$this->MultiCell(self::rpp(2400) - 2*self::HORIZONTALMARGIN, self::rpp(40), self::conv($this->getDefaultOptions()['footer_content']), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);
	}

	public function body() {
		$this->headTableProducts();

		$y = $this->getY();
		$newPageY = $y;
		$i = 1;
		foreach( $this->data['ord_products'] as $one_line ){
			if( trim($one_line['name']) == ''){
				if (trim($one_line['notes']) == ''){
					continue;
				}
				else
				{
					$one_line['name'] = $one_line['notes'];
					$one_line['notes'] = '';
					$one_line['qte'] = '';
					$one_line['price_ht'] = '';
					$one_line['total_ht'] = '';
				}
			}

			$this->SetXY( self::HORIZONTALMARGIN, $y );

			$y = $this->lineProduct( ($i++), $one_line, $y );
			if( $y >= self::ENDPAGE ){
				$this->traceProductTableBorders($newPageY, $y);
				$this->newPage( true );
				$y = $this->GetY();
				$newPageY = $y;
			}
		}
		$max_new_y = $this->postProduct($this->data['ord'], $y);
		$this->traceProductTableBorders($newPageY, $max_new_y);
		$this->SetX(self::HORIZONTALMARGIN);
		$y = $this->GetY();
		if( $y >= self::ENDPAGE - self::rpp(220) ){
			$this->newPage( false );
			$y = $this->GetY();
			$newPageY = $y;
		}
		$this->SetXY(self::HORIZONTALMARGIN, $y);
		$this->footTableProducts();

	}
	public function traceProductTableBorders($startY, $endY) {
		$this->SetXY(self::HORIZONTALMARGIN, $startY);
		$yOffset = $endY - $startY;
		$this->Cell( self::rpp(self::LINEREFERENCE), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEDESIGNATION), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINERESISTANCE), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEEPAISSEUR), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEQUANTITE), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEUNITE), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEPRIXUNITAIRE), $yOffset, '', 'LB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEMONTANT), $yOffset, '', 'LRB', 2, 'C', 0 );
	}

	public function userInfoRow(){

	}
	public function generateTotalPage(){

	}
	public function blocHeader(){

	}
	public function blocFooter(){

	}
	public function getDefaultOptions(){
		global $config;

		return [
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		];
	}

	public function defaultProductTable(){
		// do not use
	}

	private function headTableProducts(){
		$this->SetFont( 'Arial', 'B', 8 );

		$this->Cell( self::rpp(self::LINEREFERENCE), self::rpp(60), self::conv('Référence'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv('Désignation'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINERESISTANCE), self::rpp(60), self::conv('R'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), self::conv('Ep'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEQUANTITE), self::rpp(60), self::conv('Qté'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEUNITE), self::rpp(60), self::conv('U'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), self::conv('P.U. HT'), 'LTB', 0, 'C', 0 );
		$this->Cell( self::rpp(self::LINEMONTANT), self::rpp(60), self::conv('Montant HT'), 'LTRB', 2, 'C', 0 );
		$this->SetX(self::HORIZONTALMARGIN);

		$this->SetFont( 'Arial', '', 8 );
	}

	/** Cette fonction permet d'obtenir le total des taxes depuis un champ avancé pour une ligne de quantité.
	 * 	Ce montant est retour pour une quantité de 1
	 * 	@param array $line Obligatoire, tableau contenant les informations d'une ligne de devis
	 * 	@return float Le montant total des taxes
	 */
	private function getTotalTax( $line ){
		$total_d3e = 0;

		$d3e = fld_object_values_get( [$line['ord_id'], $line['id'], $line['line']], _FLD_PRD_ORD_DEEE );
		if( trim($d3e) != '' ){
			$d3e = json_decode( $d3e, true );
			if( isset($d3e['taxes']['detail']) && is_array($d3e['taxes']['detail']) ){
				foreach( $d3e['taxes']['detail'] as $one_d3e ){
					if( !ria_array_key_exists(['name', 'price'], $one_d3e) ){
						continue;
					}

					$total_d3e += $one_d3e['price'];
				}
			}
		}

		return $total_d3e;
	}

	private function lineProduct( $line_id, $line, $y ){
		global $config;

		$max_new_y = $y;
		// Mise en forme des données de type numérique
		if (is_numeric($line['price_ht'])) {
			$line['price_ht'] = number_format( $line['price_ht'], 2, ',', ' ' ).'€';
		}

		if (is_numeric($line['total_ht'])) {
			$line['total_ht'] = number_format( $line['total_ht'], 2, ',', ' ' ).'€';
		}

		$tva = number_format( ($line['tva_rate'] - 1) * 100, 2, ',', ' ' );
		$line['R'] = fld_object_values_get($line['id'], 101973);
		if (!isset($line['R']))
		{
			$line['R'] = '';
		}
		else if (is_numeric($line['R']))
		{
			$line['R'] = number_format( $line['R'], 3, ',', ' ' );
		}
		$line['Ep'] = fld_object_values_get($line['id'], 102264);
		if (!isset($line['Ep']))
		{
			$line['Ep'] = '';
		}
		else if (is_numeric($line['Ep']))
		{
			$line['Ep'] = number_format( $line['Ep'], 3, ',', ' ' );
		}
		$line['Un'] = fld_object_values_get($line['id'], 103056);
		if (!isset($line['Un']))
		{
			$line['Un'] = '';
		}

		$this->SetFont( 'Arial', '', 7.5 );
		// Ecriture des données
		$xVal = $this->GetX();
		$this->MultiCell( self::rpp(self::LINEREFERENCE), self::rpp(60), self::conv($line['ref']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEREFERENCE);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv($line['name']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEDESIGNATION);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINERESISTANCE), self::rpp(60), self::conv($line['R']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINERESISTANCE);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), self::conv($line['Ep']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEEPAISSEUR);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEQUANTITE), self::rpp(60), self::conv($line['qte']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEQUANTITE);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEUNITE), self::rpp(60), self::conv($line['Un']), 0, 'C', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEUNITE);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), self::conv($line['price_ht']), 0, 'R', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEPRIXUNITAIRE);
		$this->SetXY($xVal, $y);
		$this->MultiCell( self::rpp(self::LINEMONTANT), self::rpp(60), self::conv($line['total_ht']), 0, 'R', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }

		// Ecriture du commentaire de la ligne
		if (trim($line['notes']) !== '')
		{
			$yValComm = $max_new_y;
			$this->SetX(self::HORIZONTALMARGIN);
			$xVal = $this->GetX();
			$this->MultiCell( self::rpp(self::LINEREFERENCE), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEREFERENCE);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv($line['notes']), 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEDESIGNATION);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINERESISTANCE), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINERESISTANCE);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEEPAISSEUR);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEQUANTITE), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEQUANTITE);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEUNITE), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEUNITE);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), '', 0, 'L', 0 );
			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$xVal += self::rpp(self::LINEPRIXUNITAIRE);
			$this->SetXY($xVal, $yValComm);
			$this->MultiCell( self::rpp(self::LINEMONTANT), self::rpp(60), '', 0, 'L', 0 );
		}

		$this->SetXY($xVal, $max_new_y);
		$this->Cell( 0, self::rpp(60), '', 0, 2, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		return $max_new_y;
	}

	private function postProduct ($data, $y)
	{
		// Ecriture de la zone de travail
		$yPostProducts = $y;
		$max_new_y = $yPostProducts;
		$this->SetX(self::HORIZONTALMARGIN);
		$xVal = $this->GetX();
		$this->MultiCell( self::rpp(self::LINEREFERENCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEREFERENCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->SetFont('Arial', '', 7.5);
		$this->MultiCell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv('Type d\'accès aux combles : '.$data['TypeAccesComblesChantier']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEDESIGNATION);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINERESISTANCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINERESISTANCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEEPAISSEUR);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEQUANTITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEQUANTITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEUNITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEUNITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEPRIXUNITAIRE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEMONTANT), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY();}

		$yPostProducts = $max_new_y;
		$max_new_y = $yPostProducts;

		$this->SetX(self::HORIZONTALMARGIN);
		$xVal = $this->GetX();
		$this->MultiCell( self::rpp(self::LINEREFERENCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEREFERENCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->SetFont('Arial', '', 7.5);
		$this->MultiCell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv('Zone de travail : '.$data['ZoneTravailChantier']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEDESIGNATION);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINERESISTANCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINERESISTANCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEEPAISSEUR);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEQUANTITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEQUANTITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEUNITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEUNITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEPRIXUNITAIRE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEMONTANT), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY();}

		$yPostProducts = $max_new_y;
		$max_new_y = $yPostProducts;
		$data['NombreSpotsChantier'] = number_format( $data['NombreSpotsChantier'], 0, ',', ' ' );
		$this->SetX(self::HORIZONTALMARGIN);
		$xVal = $this->GetX();
		$this->MultiCell( self::rpp(self::LINEREFERENCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEREFERENCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->SetFont('Arial', '', 7.5);
		$this->MultiCell( self::rpp(self::LINEDESIGNATION), self::rpp(60), self::conv('Nombre de protection spot : '.$data['NombreSpotsChantier']), 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEDESIGNATION);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINERESISTANCE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINERESISTANCE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEEPAISSEUR), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEEPAISSEUR);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEQUANTITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEQUANTITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEUNITE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEUNITE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEPRIXUNITAIRE), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
		$xVal += self::rpp(self::LINEPRIXUNITAIRE);
		$this->SetXY($xVal, $yPostProducts);
		$this->MultiCell( self::rpp(self::LINEMONTANT), self::rpp(60), '', 0, 'L', 0 );
		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY();}
		return $max_new_y;
	}

	private function footTableProducts(){
		// Ecriture des informations de fin de devis
		$this->Cell(0, self::rpp(90), '', 0, 2, '', false);
		$this->SetFont('Arial', '', '8');

		$this->SetX(self::HORIZONTALMARGIN);
		$yTotalAmounts = $this->GetY();
		$this->SetFont('Arial', 'B', '8');
		$this->SetFillColor(239, 239, 239);
		$this->Cell(self::rpp(176), self::rpp(60), self::conv('CODE'), 'TL', 0, 'C', true);
		$this->Cell(self::rpp(276), self::rpp(60), self::conv('BASE'), 'TL', 0, 'C', true);
		$this->Cell(self::rpp(276), self::rpp(60), self::conv('TAUX'), 'TL', 0, 'C', true);
		$this->Cell(self::rpp(316), self::rpp(60), self::conv('MONTANT'), 'TLR', 2, 'C', true);

		// Récupération des taxes

		$total_tax = $ecotaxe = 0;
		$allTaxes = array();
        $r_product = ord_products_get( $this->data['ord']['id'] );
        if( $r_product ){
            while( $product = ria_mysql_fetch_assoc($r_product) ){
                if( ($product['total_ttc'] - $product['total_ht']) == 0 ){
                    continue;
                }
                $ecotaxe += $product['ecotaxe'] * $product['qte'];
                $total_tax += $product['ecotaxe'] * $product['qte'];
                $tax = (string) (($product['tva_rate'] - 1) * 100);
                if( !isset($allTaxes['content'][$tax]) ){
                    $allTaxes['content'][$tax] = [
                        'rate' => $tax.'%',
                        'rate_name' => 'TVA',
                        'base' => 0,
                        'amount' => 0,
                    ];
                }
                $allTaxes['content'][$tax]['base'] += $product['total_ht'];
                $allTaxes['content'][$tax]['amount'] += $product['total_ttc'] - $product['total_ht'];
                $total_tax += $product['total_ttc'] - $product['total_ht'];

            }
        }
		$allTaxes['content']['ecotaxe'] = [
			'rate' => '',
			'rate_name' => 'ECO',
			'base' => '',
			'amount' => $ecotaxe
		];

        $allTaxes['total'] = $total_tax;

		if (is_numeric($allTaxes['total'])) {
			$allTaxes['total']=number_format( $allTaxes['total'], 2, ',', ' ' ).'€';
		}
		// Ecritures des taxes
		foreach ($allTaxes['content'] as $tax)
		{
			if (is_numeric($tax['base'])) {
				$tax['base']=number_format( $tax['base'], 2, ',', ' ' ).'€';
			}
			if (is_numeric($tax['amount'])) {
				$tax['amount']=number_format( $tax['amount'], 2, ',', ' ' ).'€';
			}
			$this->SetFont('Arial', '', '8');
			$this->SetX(self::HORIZONTALMARGIN);
			$this->Cell(self::rpp(176), self::rpp(75), self::conv($tax['rate_name']), 'LT', 0, 'C', false);
			$this->Cell(self::rpp(276), self::rpp(75), self::conv($tax['base']), 'LT', 0, 'R', false);
			$this->Cell(self::rpp(276), self::rpp(75), self::conv($tax['rate']), 'LT', 0, 'C', false);
			$this->Cell(self::rpp(316), self::rpp(75), self::conv($tax['amount']), 'LTR', 2, 'R', false);
		}
		// Ecriture du total de taxes
		$this->SetX(self::HORIZONTALMARGIN);
		$this->SetFillColor(221,221,221);
		$this->Cell(self::rpp(176), self::rpp(75), '', 'LTB', 0, 'C', true);
		$this->Cell(self::rpp(276), self::rpp(75), self::conv('Total'), 'LTB', 0, 'C', false);
		$this->Cell(self::rpp(276), self::rpp(75), self::conv('TVA + ECO'), 'LTB', 0, 'C', false);
		$this->Cell(self::rpp(316), self::rpp(75), self::conv($allTaxes['total']), 'LTBR', 2, 'R', false);

		$this->Cell(0, self::rpp(30), '', 0, 2, '', false);


		// Ecriture des moyens de paiement
		$this->SetX(self::HORIZONTALMARGIN);
		$this->Cell(0, self::rpp(60), '', 0, 2, '', false);
		$this->Cell(self::rpp(448), self::rpp(60), self::conv('Mode de paiement :'), 'TLB', 0, 'L', false);
		$this->Cell(self::rpp(1100), self::rpp(60), self::conv($this->data['user']['user_payment_name']), 'TLBR', 2, 'L', false);
		$this->SetX(self::HORIZONTALMARGIN);
		$this->Cell(self::rpp(448), self::rpp(60), self::conv('Délai de règlement :'), 'LB', 0, 'L', false);
		$this->Cell(self::rpp(1100), self::rpp(60), self::conv($this->data['user']['user_payment_days']), 'LBR', 2, 'L', false);

		// Ecriture des totaux de la commande
		$this->SetXY(self::rpp(1170), $yTotalAmounts);
		$this->SetFont('Arial', 'B', '8');
		$this->SetFillColor(239, 239, 239);
		$this->Cell(self::rpp(352), self::rpp(60), self::conv('TOTAL H.T.'), 'TL', 0, 'C', true);
		$this->Cell(self::rpp(402), self::rpp(60), self::conv('TOTAL T.T.C.'), 'TL', 0, 'C', true);
		$this->Cell(self::rpp(377), self::rpp(60), self::conv('NET A PAYER'), 'TLR', 2, 'C', true);
		$this->SetFont('Arial', '', '8');
		$this->SetX(self::rpp(1170));
		$this->Cell(self::rpp(352), self::rpp(75), self::conv($this->data['ord']['total_ht'].'€'), 'LTB', 0, 'R', false);
		$this->Cell(self::rpp(402), self::rpp(75), self::conv($this->data['ord']['total_ttc'].'€'), 'LTB', 0, 'R', false);
		$this->Cell(self::rpp(377), self::rpp(75), self::conv($this->data['ord']['total_ttc'].'€'), 'LTBR', 0, 'R', false);

		$this->SetXY(self::rpp(1639), self::rpp(2740));
		$this->SetFont( 'Arial', 'B', 8 );
		$this->Cell(0, self::rpp(47), self::conv('Pour le client,'), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), self::conv('Signature précédée de la mention'), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), self::conv('"Lu et Approuvé, Bon pour Accord"'), 0, 2, '', false);
	}

	private function newPage( $with_prds ){
		$this->addPage();

		if( $with_prds ){
			$this->headTableProducts();
		}
	}

	private static function rpp( $pixels ){
		if (!is_numeric($pixels) || $pixels<0) {
			return 0;
		}

		return round( $pixels * 0.0875, 3 );
	}

	private static function conv($str) {
		return iconv('utf8', 'windows-1252', $str);
	}

	private static function tranformUpper( $data ){
		if( is_array($data) ){
			foreach( $data as $key => $value ){
				$data[ $key ] = strtoupper2( $value );
			}
		}

		return $data;
	}
}
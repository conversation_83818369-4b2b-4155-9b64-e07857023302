<?php

/** \file Pdf.inc.php
 * 	Ce fichier ce charge de générer le PDF d'un devis ou d'une commande spécifique à Isoweck.
 */
namespace Pdf;

use DateTime;


require_once('users.inc.php');
require_once('orders.inc.php');
require_once('fields.inc.php');
require_once('Barcode.inc.php');

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');



class IsoweckQuoteConstructorTarifPdf extends PieceDeVente {
	const ENDPAGE = 220;
	const TOPPAGE = 7.26;
	const HORIZONTALMARGIN = 5.25;

	// Tableau des produits composé des colonnes :
	const LINEREFERENCE = 288;
	const LINEDESIGNATION = 855;
	const LINERESISTANCE = 129;
	const LINEEPAISSEUR = 147;
	const LINEQUANTITE = 208;
	const LINEUNITE = 120;
	const LINEPRIXUNITAIRE = 187;
	const LINEMONTANT = 330;
	// Cette variable est chargée en même temps que l'on parcours les lignes du devis
	// L'index correspond au taux de TVA
	// La valeur est un tableau comme ceci [total ht, total tva]
	private $ar_tva = []; ///< Contient les TVA appliqués sur les lignes du devis

	/** Charge le header sur toutes les pages du PDF. */
	public function Header(){

		// Bloc adresse client
		$this->SetXY(self::rpp(1257), self::rpp(400));
		// Récupération de l'adresse de facturation	selon les champs avancés
		$this->SetFont('Arial', '', '9');
		$adrinv = self::tranformUpper( $this->data['addresses']['invoice'] );
		$attn_inv = $adrinv['firstname'].' '.$adrinv['lastname'];
		if( trim($attn_inv) == '' ){
			$attn_inv = $adrinv['society'];
		}
		$attn_inv = trim($attn_inv);
		$this->Cell(0, self::rpp(53), self::conv($attn_inv), 0, 2, '', false);
		$adrdlv = self::tranformUpper( $this->data['addresses']['delivery']);
		if (trim($adrdlv['address1']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv($adrdlv['address1']), 0, 2, '', false);
		}
		if (trim($adrdlv['address2']) !== '')
		{
			$this->Cell(0, self::rpp(53), self::conv($adrdlv['address2']), 0, 2, '', false);
		}
		$this->Cell(0, self::rpp(53), self::conv($adrdlv['postal_code'].' '.$adrdlv['city']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);


		// Logo en haut à gauche du devis
		$this->SetXY( self::HORIZONTALMARGIN, self::TOPPAGE );
		$this->Image( dirname(__DIR__)."/Isoweck/images/Isoweck_logo.jpg", $this->GetX(), $this->GetY(), self::rpp(498), self::rpp(139.8));
		// Bloc adresse d'Isoweck
		$this->SetFont('Arial', '', '9');
		$this->SetXY(self::HORIZONTALMARGIN, $this->GetY() + self::rpp(139.8) + self::rpp(60));
		$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['address1']), 0, 2, '', false);
		if (trim($this->data['owner']['address2']) !== '') {
			$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['address2']), 0, 2, '', false);
		}
		$this->Cell(0, self::rpp(53), self::conv($this->data['owner']['zipcode'].' '.$this->data['owner']['city']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('N° Siret : 348 ************'), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), '', 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('Téléphone : '.$this->data['owner']['phone']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), self::conv('Email : '.$this->data['owner']['email']), 0, 2, '', false);
		$this->Cell(0, self::rpp(53), 'Site : www.isoweck.com', 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);

		// Affiche les informations du devis
		$this->SetX(0);
		$this->Cell(self::rpp((2400)), self::rpp(88), '', 'T', 2, '', false);
		$this-> SetFont('Arial', 'B', 30);
		$this->Cell(self::rpp(2400), self::rpp(133), self::conv('TARIFS '.$this->data['ord']['AnneeTarif']), 0, 2, 'C', false);

		// Barre horizontale après TARIFS [ANNEE]
		$this->SetX(0);
		$this->Cell(self::rpp(2400), self::rpp(68), '', 'B', 2, '', false);

		$this->SetX(self::HORIZONTALMARGIN);
		$this->SetFont('Arial', 'B', 9);
		$this->Cell(0, self::rpp(16), '', 0, 2, 'C', false);
		$this->Cell(self::rpp(2400) -  2 * self::HORIZONTALMARGIN, self::rpp(54), self::conv('ISOLATION DES PLAFONDS PAR SOUFFLAGE'), 0, 2, 'C', false);
		$this->Cell(self::rpp(2400) -  2 * self::HORIZONTALMARGIN, self::rpp(54), self::conv('Combles perdus horizontaux'), 0, 2, 'C', false);

		$this->SetFont('Arial', 'B', 11);
		$this->Cell(self::rpp(2400) -  2 * self::HORIZONTALMARGIN, self::rpp(74), self::conv($this->data['ord']['PrestationType']), 0, 2, 'C', false);
		$this->SetTextColor(83, 111, 165);
		$this->Cell(self::rpp(2400) -  2 * self::HORIZONTALMARGIN, self::rpp(74), self::conv($this->data['ord']['ProduitType']), 0, 2, 'C', false);
		$this->SetTextColor(0, 0, 0);
		$this->Cell(0, self::rpp(16), '', 0, 2, 'C', false);

		$this->Cell(self::rpp(2400) -  2 * self::HORIZONTALMARGIN, self::rpp(74), self::conv('TARIFS PAR M² H.T. VALABLES JUSQU\'AU '.date('d/m/y', strtotime($this->data['ord']['DateValidite']))), 0, 2, 'C', false);
		$this->SetX(self::HORIZONTALMARGIN);
		$this->Cell(0, self::rpp(10), '', 0, 2, 'C', false);
	}

	public function Footer(){
		$this->SetXY( self::HORIZONTALMARGIN, self::rpp(3006));
		$this->Image( dirname(__DIR__).'/Isoweck/images/Isoweck_square_group_logo.jpg', $this->GetX(), $this->GetY(), self::rpp(360), self::rpp(156.8));

		$this->SetFont( 'Arial', 'BI', 9 );
		$this->SetXY( self::HORIZONTALMARGIN, self::rpp(3006) + self::rpp(156.8));
		$this->Cell(0, self::rpp(53), 'Devis valable 30 jours', 0, 2, '', false);
		$this->Cell(0, self::rpp(40), '', 0, 2, '', false);

		$this->SetFont( 'Arial', '', 6.5 );
		$this->MultiCell(self::rpp(2400) - 2*self::HORIZONTALMARGIN, self::rpp(40), self::conv($this->getDefaultOptions()['footer_content']), 0, 2, '', false);
		$this->Cell(0, self::rpp(47), '', 0, 2, '', false);
	}

	public function body() {

		$i = 1;
		$yStart = $this->GetY();
		$this->SetX(self::HORIZONTALMARGIN);
		$this->SetFillColor(242, 242, 242);
		$this->SetFont('Arial', 'B', 9); $this->SetTextColor(255, 0, 0);
		$this->Cell(self::rpp(240), self::rpp(92), self::conv('m².K/W'), 'TL', 2, 'C', true);
		$this->SetFont('Arial', 'B', 9); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(240), self::rpp(92), self::conv('CM'), 'TL', 2, 'C', true);
		$this->Cell(self::rpp(240), self::rpp(109), self::conv('KG/M²'), 'TL', 2, 'C', true);
		$this->SetFont('Arial', 'BI', 13); $this->SetTextColor(255, 0, 0);
		$this->Cell(self::rpp(240), self::rpp(167), self::conv('Tarif'), 'TLB', 0, 'C', true);
		$xMax = $this->GetX();
		foreach( $this->data['ord_products'] as $one_line ){
			if( trim($one_line['name']) == ''){
				continue;
			}
			$i++;
			if ($i == 11)
			{
				break;
			}
			else
			{
				// Récupération des données sous forme de float pour pas avoir trop de chiffres
				$one_line['R'] = fld_object_values_get($one_line['id'], 101973) + 0;
				$one_line['CM'] = fld_object_values_get($one_line['id'], 105911) + 0;
				$one_line['KGM2'] = fld_object_values_get($one_line['id'], 105912) + 0;

				if (is_numeric($one_line['price_ht'])) {
					$one_line['price_ht'] = number_format( $one_line['price_ht'], 2, ',', ' ' ).'€';
				}
				$this->SetXY($xMax, $yStart);
				$this->SetFillColor(242, 242, 242);
				$this->SetFont('Arial', 'B', 9); $this->SetTextColor(255, 0, 0);
				$this->Cell(self::rpp(200), self::rpp(92), self::conv($one_line['R']), 'TL', 2, 'C', true);
				$this->SetFont('Arial', 'B', 9); $this->SetTextColor(0, 0, 0);
				$this->Cell(self::rpp(200), self::rpp(92), self::conv($one_line['CM']), 'TL', 2, 'C', true);
				$this->Cell(self::rpp(200), self::rpp(109), self::conv($one_line['KGM2']), 'TL', 2, 'C', true);
				$this->Cell(self::rpp(200), self::rpp(167), self::conv($one_line['price_ht']), 'TLB', 0, 'C', false);
				$xMax=$this->GetX();
			}
		}

		// Traçage des bordures droite des produits
		$this->SetXY($xMax, $yStart);
		$this->Cell(0, self::rpp(92), '', 'L', 2, 'C', false);
		$this->Cell(0, self::rpp(92), '', 'L', 2, 'C', false);
		$this->Cell(0, self::rpp(109), '', 'L', 2, 'C', false);
		$this->Cell(0, self::rpp(167), '', 'L', 2, 'C', false);

		// Ecriture des champs avancés forfait minimum d'intervention
		$this->SetX(self::HORIZONTALMARGIN);

		$this->SetFont('Arial', '', 8); $this->SetTextColor(0, 0, 0);
		$this->Cell(0, self::rpp(57), '', 0, 2, 'C', false);
		$this->Cell(0, self::rpp(54), self::conv('Forfait Minimum d\'intervention jusqu\'à la résistance 8.5 : '.$this->data['ord']['ForfaitIntervention85']), 0, 2, 'L', false);
		$this->SetFont('Arial', '', 8); $this->SetTextColor(0, 0, 0);
		$this->Cell(0, self::rpp(57), '', 0, 2, 'C', false);
		$this->Cell(0, self::rpp(54), self::conv('Forfait Minimum d\'intervention à partir de la résistance 9 : '.$this->data['ord']['ForfaitIntervention9']), 0, 2, 'L', false);
		$this->Cell(0, self::rpp(45), '', 0, 2, 'C', false);
		$this->SetX(0);
		$this->Cell(self::rpp((2400)), 0, '', 'B', 2, 'C', false);
		$this->Cell(0, self::rpp(14), '', '', 2, 'C', false);

		// Ecriture de la date d'application et de la certification
		$this->SetX(self::HORIZONTALMARGIN);
		$this->SetFont('Arial', 'U', 8); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(288), self::rpp(54), self::conv('Date d\'application:'), 0, 0, 'L', false);
		$this->SetFont('Arial', '', 8); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(204), self::rpp(54), self::conv(date('d/m/Y', strtotime($this->data['ord']['DateApplication']))), 1, 2, 'L', false);

		// Ecriture de la certification
		$this->SetX(self::rpp(680));
		$this->SetFont('Arial', '', 7); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(188), self::rpp(54), self::conv('Produit certifié '), 0, 0, 'L', false);
		$this->SetFont('Arial', 'B', 7); $this->SetTextColor(255, 0, 0);
		$this->Cell(self::rpp(118), self::rpp(54), self::conv('ACERMI '), 0, 0, 'L', false);
		$this->SetFont('Arial', '', 7); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(704), self::rpp(54), self::conv('N° 17/D/015/1195 Mise en oeuvre sous Avis Technique '), 0, 0, 'L', false);
		$this->SetFont('Arial', 'B', 7); $this->SetTextColor(255, 0, 0);
		$this->Cell(self::rpp(80), self::rpp(54), self::conv('CSTB '), 0, 2, 'L', false);
		$this->SetX(0);
		$this->Cell(self::rpp((2400)), 0, '', 'B', 2, 'C', false);
		$this->Cell(0, self::rpp(24), '', '', 2, 'C', false);

		// Cachets et signatures
		$this->SetX(self::HORIZONTALMARGIN);
		$ySignatures = $this->GetY();
		$this->SetFont('Arial', 'U', 7); $this->SetTextColor(0, 0, 0);
		$this->Cell(self::rpp(0), self::rpp(54), self::conv('Cachet et signature client'), 0, 2, 'L', false);
		$this->Cell(self::rpp(960), self::rpp(406), '', 1, 2, 'L', false);
		$this->SetXY(self::rpp(1228), $ySignatures);
		$this->Cell(self::rpp(0), self::rpp(54), self::conv('Cachet et signature Isoweck'), 0, 2, 'L', false);
		$this->Cell(self::rpp(960), self::rpp(406), '', 1, 2, 'L', false);
		$this->SetFont('Arial', 'B', 7); $this->SetTextColor(0, 0, 0);
		$this->SetX(0);
		$this->Cell(0, self::rpp(24), '', '', 2, 'C', false);
		$this->Cell(self::rpp(2400) - 2 * self::HORIZONTALMARGIN, self::rpp(54), self::conv('TRACABILITE'), 0, 2, 'C');
		$this->Cell(0, self::rpp(24), '', '', 2, 'C', false);
		$this->SetX(self::HORIZONTALMARGIN);
		$this->SetFont('Arial', '', 7); $this->SetTextColor(0, 0, 0);
		$this->Cell(0, self::rpp(54), self::conv('Des repères dimensionnels sont répartis dans les combles'), 0, 2, 'L');
		$this->Cell(0, self::rpp(24), '', '', 2, 'C', false);
		$this->Cell(0, self::rpp(54), self::conv('La fiche de contrôle et les étiquettes des sacs utilisées sont agrafées sur la charpente'), 0, 2, 'L');


	}

	public function userInfoRow(){

	}
	public function generateTotalPage(){

	}
	public function blocHeader(){

	}
	public function blocFooter(){

	}
	public function getDefaultOptions(){
		global $config;

		return [
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		];
	}

	public function defaultProductTable(){
		// do not use
	}

	private static function rpp( $pixels ){
		if (!is_numeric($pixels) || $pixels<0) {
			return 0;
		}

		return round( $pixels * 0.0875, 3 );
	}

	private static function conv($str) {
		return iconv('utf8', 'windows-1252', $str);
	}

	private static function tranformUpper( $data ){
		if( is_array($data) ){
			foreach( $data as $key => $value ){
				$data[ $key ] = strtoupper2( $value );
			}
		}

		return $data;
	}
}
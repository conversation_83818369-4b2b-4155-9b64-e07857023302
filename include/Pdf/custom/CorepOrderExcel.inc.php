<?php

/** \file CorepOrderExcel.inc.php
	 * 	Ce fichier permet de générer un fichier Excel spécifique aux devis / bon de commande pour Corep.
	 */

/**
 * Export Excel de la commande : liste des produits
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */

$totalDEEE = 0;
function export_order_excel_product_table_corep( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config, $totalDEEE;

	$current_col = $col;
	$show_prd_img = isset($options['prd_img']) && !is_null($options['prd_img']) && isset($config['img_sizes']['small']) && $options['prd_img'];

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);

	$style_body = $style;
	if (isset($options['font_size']) && $options['font_size']) {
		$style_body = array_merge(
			$style_body,
			array(
				'font'=>array('size' => $options['font_size']),
				'alignment' => array('vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER)
			)
		);
	}

	$style['font']['size'] = 7;
	$style['font']['bold'] = true;

	if( $show_prd_img ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			'Image',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		$current_col++;
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$current_col,
		$row,
		'Référence',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Code EAN',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Désignation',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style);
	$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Qté',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'P.U. HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'DEEE',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Eco-part.',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	if (isset($options['prd_reduce']) && $options['prd_reduce']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Remise',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Montant HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);


	$row++;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	foreach ($data['ord_products'] as & $p) {
		$current_col = $col;
		$fields = '';

		if( isset($p['fields']) && is_array($p['fields']) && count($p['fields']) ){

			foreach($p['fields'] as $name => $val){
				$fields .= "\r".$name.' : '.$val;
			}

		}

		if( $show_prd_img){
			if ($p['img_id']
				&& file_exists($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format'])
			) {
				$objDrawing = new PHPExcel_Worksheet_Drawing();
				$objDrawing->setPath($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format']);
				$objDrawing->setWidth($config['img_sizes']['small']['width']);
				$objDrawing->setHeight($config['img_sizes']['small']['height']);
				$objDrawing->setWorksheet($sheet);
				$objDrawing->setCoordinates($sheet->getCellByColumnAndRow($current_col, $row)->getCoordinate());
			}

			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
			$sheet->getRowDimension($row)->setRowHeight($config['img_sizes']['small']['height']);
			$current_col++;
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			$p['id'] == 0 ? '' : $p['ref'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$temp_style = $style_body;
		$temp_style['alignment']['horizontal'] = PHPExcel_Style_Alignment::HORIZONTAL_RIGHT;
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($temp_style);

		if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				prd_products_get_barcode($p['id']),
				PHPExcel_Cell_DataType::TYPE_STRING
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		$title = "";
		if(isset($p['title']) && !is_null($p['title'])){
			$title = $p['title'];
		}else{
			$title = null;
		};
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$title,
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$temp_style = $style_body;
		$temp_style['alignment']['horizontal'] = PHPExcel_Style_Alignment::HORIZONTAL_RIGHT;
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($temp_style);

		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style_body);
		$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : floatval($p['qte']),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$prc_brut_yuto = $p['price_ht'];

		$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
		if ( is_numeric($fld_discount) && $fld_discount > 0 ) {
			if( $fld_discount < 100 ){
				$prc_brut_yuto = $p['price_ht'] / ((100 - $fld_discount) / 100);
			}

			if( $prc_brut_yuto <= 0 ){
				$user_id = ord_orders_get_user( $p['ord_id'] );
				$prc_id = gu_users_get_prc( $user_id );
				$temp = prd_products_get_price( $p['id'], $user_id, $prc_id );
				if( $temp && ria_mysql_num_rows($temp) ){
					$prc_brut_yuto = ria_mysql_result( $temp, 0, 'price_ht' );
				}
			}
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : round($prc_brut_yuto, $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$deee = 0;
		$deee = preg_replace('/[^0-9.]+([A-Za-z.])/', '', fld_object_values_get($p['id'], 105888));
		if( $deee != null && $deee >= 0 ){
			$totalDEEE += $deee * $p['qte'];
		}
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : round($deee, $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$p['id'] == 0 ? null : round($p['ecotaxe'], $decimals),
				PHPExcel_Cell_DataType::TYPE_NUMERIC
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		if (isset($options['prd_reduce']) && $options['prd_reduce']) {
			$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

			// on ajoute le signe lié à la remise (en euro ou pourcentage)
			// floatval permet de retirer les 0 superflux
			// Si une remise a été renseignée avec le champ avancé, celle ci est prioritaire à celle renseignée sur la ligne de commande
			$remise = '';
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$remise = str_replace(',00', '', number_format($fld_discount, $decimals, ',', ' ')).' %';
			}elseif( $p['discount'] > 0 ){
				if( $p['discount_type'] === "0" ){ // Euros
					$remise = number_format($p['discount'], $decimals, ',', ' ').' €';
				}else{ // %
					$remise = str_replace(',00', '', number_format($p['discount'], $decimals, ',', ' ')).' %';
				}
			}

			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$remise,
				PHPExcel_Cell_DataType::TYPE_STRING
			);

			$style = [ 'font' => [ 'size' => 8 ], 'borders' => [ 'allborders' => [ 'style' => 'thin' ] ],'alignment' => [ 'horizontal' => 'right', 'vertical' => 'center' ] ];
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		}

		$sumHT = 0;
		if (isset($options['prd_reduce']) && $options['prd_reduce']){
			$sumHT = $p['discount_type'] === "0" ? $p['total_ht'] - $p['discount'] : $p['total_ht'] * (1-($p['discount']/100));
		}else{
			$sumHT = $p['total_ht'];
		};

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : round($sumHT, $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);


		$row++;
	}

	return array($current_col, $row);
}


function export_order_excel_totals_corep( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config, $totalDEEE;

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);
	$style_body = $style;
	$style_body['alignment']['horizontal'] = PHPExcel_Style_Alignment::HORIZONTAL_RIGHT;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	$style['font']['bold'] = true;
	$style['font']['size'] = 8;

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		$row,
		'TOTAL Remise',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		'TOTAL DEEE',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		'TOTAL H.T.',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);

	$totalDiscount = 0;
	foreach( $data['ord_products'] as $one_prd ){
		$fld_discount = fld_object_values_get(array($one_prd['ord_id'], $one_prd['id'], $one_prd['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
		if( is_numeric($fld_discount) && $fld_discount > 0 ){
			$prc_brut_yuto = 0;
			if( $fld_discount < 100 ){
				$prc_brut_yuto = $one_prd['price_ht'] / ((100 - $fld_discount) / 100);
			}

			if( $prc_brut_yuto <= 0 ){
				$user_id = ord_orders_get_user( $one_prd['ord_id'] );
				$prc_id = gu_users_get_prc( $user_id );
				$temp = prd_products_get_price( $one_prd['id'], $user_id, $prc_id );
				if( $temp && ria_mysql_num_rows($temp) ){
					$prc_brut_yuto = ria_mysql_result( $temp, 0, 'price_ht' );
				}
			}

			$totalDiscount += ($prc_brut_yuto - $one_prd['price_ht']) * floatval($one_prd['qte']);
		}
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$col,
		++$row,
		number_format($totalDiscount, $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$temp_style = $style_body;
	$temp_style['alignment']['vertical'] = 'right';
	$sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($temp_style);


	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		number_format($totalDEEE, $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($temp_style);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		number_format($data['ord']['total_ht'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($temp_style);

	return array($col + 2, $row);
}


ob_start();

    // Gestion de l'arrondi des tarifs à zéro chiffre après la virgule
    if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
        $data['ord']['total_ht'] = round( $data['ord']['total_ht'] );
        $data['ord']['total_ttc'] = round( $data['ord']['total_ttc'] );

        if( isset($data['ord_products']) && is_array($data['ord_products']) ){
            foreach( $data['ord_products'] as &$d ){
                $d['price_ht'] = round( $d['price_ht'] );
                $d['price_ttc'] = round( $d['price_ttc'] );
                $d['total_ht'] = round( $d['total_ht'] );
                $d['total_ttc'] = round( $d['total_ttc'] );
                $d['ecotaxe'] = round( $d['ecotaxe'] );
            }
        }
    }

    // Initialisation de la devise
    export_order_excel_set_currency( $data );

    $data['file_sign'] = export_order_create_sign( $data['ord']['id'] );

    $doc = new PHPExcel();

    $first_line = 5;
    $sheet = $doc->getActiveSheet();
    $sheet->getDefaultStyle()->applyFromArray(export_order_excel_default_style());
    $sheet->getDefaultColumnDimension()->setWidth(18);

    list($last_logo_col, $last_logo_row) = export_order_excel_logo($sheet, $data, $options, 5, 1);
    list($last_owner_col, $last_owner_row) = export_order_excel_owner($sheet, $data, $options, 0, 1);
    list($last_adr_col, $last_adr_row) = export_order_excel_addresses($sheet, $data, $options, 5, 0, $last_owner_row + 2);
    list($last_header_col, $last_header_row) = export_order_excel_header($sheet, $data, $options, 0, $last_adr_row);

    list($last_user_info_col, $last_user_info_row) = export_order_excel_user_info($sheet, $data, $options, 0, $last_header_row + 2);
    list($last_products_col, $last_products_row) = export_order_excel_product_table_corep($sheet, $data, $options, 0, $last_user_info_row + 2);
    list($last_taxes_col, $last_taxes_row) = array(0, $last_products_row + 2);
    $totals_first_col = max($last_taxes_col + 1, 5);
    list($last_totals_col, $last_totals_row) = export_order_excel_totals_corep($sheet, $data, $options, $totals_first_col, $last_products_row + 2);
    list($last_payments_col, $last_payments_row) = export_order_excel_payments($sheet, $data, $options, $totals_first_col, $last_totals_row + 1);

    list($last_footer_col, $last_footer_row) = export_order_excel_footer($sheet, $data, $options, 0, $last_payments_row + 2);

    $last_bank_details_col = 0;
    $last_bank_details_row = 1;
    if( $data['ord']['pay_id'] == _PAY_VIREMENT ){
        list($last_bank_details_col, $last_bank_details_row) = export_order_excel_bank_details($sheet, $data, $options, 0, $last_footer_row + 2);
    }

    $max_col = max(
        $last_logo_col,
        $last_owner_col,
        $last_adr_col,
        $last_header_col,
        $last_user_info_col,
        $last_products_col,
        $last_taxes_col,
        $last_totals_col,
        $last_payments_col,
        $last_footer_col,
        $last_bank_details_col
    ) + 1;

    $max_row = max(
        $last_logo_row,
        $last_owner_row,
        $last_adr_row,
        $last_header_row,
        $last_user_info_row,
        $last_products_row,
        $last_taxes_row,
        $last_totals_row,
        $last_payments_row,
        $last_footer_row,
        $last_bank_details_row
    ) + 1;

    $sheet->getPageSetup()->setPrintAreaByColumnAndRow(0, 1, $max_col, $max_row);
    $sheet->getPageMargins()
        ->setLeft(0.5)
        ->setRight(0.5);

    $sheet->setShowGridlines(false);

    $output_writer = isset($options['output_writer']) ? $options['output_writer'] : 'Excel5';

    $writer = PHPExcel_IOFactory::createWriter($doc, $output_writer);

    switch ($output_writer) {
        case 'CSV':
            $writerExtension = '.csv';
            break;
        case 'Excel5':
            $writerExtension = '.xls';
            break;
        case 'PDF':
            $writerExtension = '.pdf';
            break;
        case 'HTML':
            $writerExtension = '.html';
            break;
        case 'Serialized':
            $writerExtension = '.txt';
            break;
        case 'Excel2007':
        default:
            $writerExtension = '.xlsx';
            break;
    }

    $output = isset($options['output']) && trim($options['output']) != '' ? $options['output'] : 'D';
    $filename = isset($options['filename']) && trim($options['filename']) != '' ? $options['filename'] : 'order' . $writerExtension;

    if ($output == 'D' || $output == 'I') {
        try {
            // Charge le contenu du fichier Excel dans une variable temporaire
            // afin de supprimer le fichier image de la signature
            ob_start();
            $writer->save('php://output');
            $content = ob_get_clean();

            // La signature est tout de suite supprimée après utilisation
            if( file_exists($data['file_sign']) ){
                unlink($data['file_sign']);
            }

            // Charge le contenu du fichier Excel
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$filename.'"');
            print $content;
            exit;
        }catch( \Exception $e ){
            throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
        }
    }else{
        //Save to local file
        if (!isset($options['filename'])) {
            throw new OrderExportException($data['ord']['id'], 'Impossible d\'écrire le fichier.');
        }

        try {
            $writer->save($options['filename']);
            // La signature est tout de suite supprimée après utilisation
            if( file_exists($data['file_sign']) ){
                unlink($data['file_sign']);
            }
        }catch( \Exception $e ){
            throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
        }
    }
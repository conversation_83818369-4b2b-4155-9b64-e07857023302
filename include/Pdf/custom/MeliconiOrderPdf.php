<?php

namespace Pdf;

use \DateTime;

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');
require_once('Export/order.inc.php');

/** \devgroup OrderPdf OrderPdf
 * \ingroup PieceDeVente
 */
class MeliconiOrderPdf extends PieceDeVente
{
	private $decimals = 2;
	/**
	 * __construct
	 *
	 * @param array $order
	 *
	 * @return void
	 */
	public function __construct(array $order)
	{
		global $config;

		parent::__construct();
		$this->data['ord'] = $order;

		if (isset($config['round_digits_count']) && $config['round_digits_count'] == 0) {
			$this->decimals = 0;
		}
	}

	private static function conv($str)
	{
		return iconv('utf8', 'windows-1252', $str);
	}

	/**
	 * Function called before PDF generation
	 *
	 * @return void
	 */
	public function bootstrap()
	{
		if (!isset($this->data['user'])) {
			$this->loadUserInfo();
		}

		if (!isset($this->data['addresses'])) {
			$this->loadOrdersAdresses();
		}


		parent::bootstrap();

		$this->SetSubject(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('subject')));
		$this->SetTitle(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('name')));
	}

	/** Retourne le tableau des options par défaut.
	 *
	 * @return array Tableau des options par défaut
	 */
	protected function getDefaultOptions()
	{
		global $config;

		$data = $deposit = array();

		// Le client peut "surcharger" la configuration de génération des devis PDF.
		// On doit alors vérifier si c'est le cas et si oui, récupérer la nouvelle configuration.
		if ($deposit_id = $this->data['ord']['dps_id']) {
			$deposit = ria_mysql_fetch_assoc(
				prd_deposits_get($deposit_id)
			);

			if (($quote_config = $deposit['quote_config'])) {
				$data = (array) json_decode($quote_config);
			}
		}

		$merged_data = array_merge(array(
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		), $data);

		return $merged_data;
	}

	public function blocOwner(){}

	public function adressBloc($address, $with_border = true, $display_adr_type = false){}

	public function logo()
	{
		if( is_null($this->logo_image) && !is_null($this->getData('logo')) ){
			$this->SetY(6);
			$this->withLogo(
				$this->data['logo'],
				10,
				$this->GetY(),
				$this->convertPixelToFPDF($this->requireOption('logo_size_x')),
				$this->convertPixelToFPDF($this->requireOption('logo_size_y'))
			);
		}

		if( !is_null($this->logo_image) ){
			$this->SetY(6);
			$this->logo_image->setX(10);
			$this->logo_image->setY(6);
			$this->logo_image->generate();
		}
	}

	/**
	 * Contient le corps du pdf dans ce cas le tableau
	 *
	 * @return void
	 */
	public function body()
	{
		$this->table->generateTable();
	}

	/**
	 * Configure le tableau pour avoir un comportement par défaut colonne :
	 * - ref
	 * - Désignation
	 * - Quantité
	 * - Prix unitaire brut
	 * - Remise
	 * - prix unitaire net
	 * - montant total ht
	 *
	 * @return \Pdf\ProductTable
	 */
	public function defaultProductTable()
	{
		global $config;
		$this->SetY(150);

		$this->requireData('ord');
		$this->setCurrency();

		$this->table()->withTbodyFontSize(7);

		$this->row_with_same_height = true;

		$this->table()->addColumn(new Column('Référence', 'L', 15, function ($p) {
			return $p['id'] == 0 ? '' : $p['ref'];
		}));

		// Colonne code barre du produit
		$this->table()->addColumn(new \Pdf\Column('Code EAN', 'ean', 18, function ($p) {
			return prd_products_get_barcode($p['id']);
		}), 2);

		$this->table()->addColumn(new Column('Désignation', 'L', 40, function ($p) {
			$compl_name = '';
			$fields = '';
			if (isset($p['group_id'], $p['group_parent_id']) && !is_null($p['group_id']) && !is_null($p['group_parent_id'])) {
				$compl_name = '     ';
			}

			if (isset($p['fields']) && is_array($p['fields']) && count($p['fields'])) {

				foreach ($p['fields'] as $name => $val) {
					$fields .= "\n" . $name . ' : ' . $val;
				}
			}

			if (isset($p['label'])) {
				return $compl_name . $p['label'] . $fields;
			}


			$p['col_qte'] = 1;

			if( ($colisage_id = fld_object_values_get( [$p['ord_id'], $p['id'], $p['line']], _FLD_PRD_COL_ORD_PRODUCT )) ){
				$r_colisage = prd_colisage_types_get( $colisage_id );
				$colisage = ria_mysql_fetch_assoc( $r_colisage );
				$p['col_qte'] = $colisage['qte'];
				$is_colisage = true;
			}else{
				$is_colisage = false;
			}

			$comment = '';
			if ($p['id'] != 0 && $p['notes']) {
				$comment = "\n";
			}
			if ($p['notes']) {
				$comment .= $p['notes'];
			}

			// Remplace le retour chariot par un saut ligne suivi d'un retour chariot.
			$comment = str_replace("\r", "\n\r", $comment);

			$product_name = ($p['id'] != 0 ? $p['name'] . ($is_colisage ? ' - ' . $colisage['name'] . ' (' . parseInt($colisage['qte']) . ')' : '') : '') . $comment;

			return $compl_name . $product_name . $fields;
		}));

		// Colonne quantité
		$this->table()->addColumn(new Column('Qté', 'C', 5, function ($p) {
			if ($p['id'] == 0) {
				return null;
			} else {
				return floatval($p['qte']);
			}
		}));

		// Colonne Prix Unitaire HT du produit
		$this->table()->addColumn(new Column('Prix Net HT', 'C', 12, function ($p) {
			if ($p['id'] == 0) {
				return null;
			} else {
				$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

				if ($this->getOption('prd_reduce')) {
					if (is_numeric($fld_discount) && $fld_discount > 0) {
						$divide_by = (1 - $fld_discount / 100);
						if ($divide_by > 0) {
							$p['price_ht'] = round($p['price_ht'], 2) / $divide_by;
						}
					}
				} else {
					if (!(is_numeric($fld_discount) && $fld_discount > 0)) {
						if ($p['discount'] > 0) {
							if ($p['discount_type'] === "0") { // Euros
								$p['price_ht'] = $p['price_ht'] - ($p['discount'] / $p['qte']);
							} else { // %
								$p['price_ht'] = $p['price_ht'] * (1 - ($p['discount'] / 100));
							}
						}
					}
				}
				//Si ecotaxe negative valeur par default est de 0
				$p['price_ht'] -= $p['ecotaxe'];
				if ($p['price_ht'] < 0) {
					$p['price_ht'] = 0;
				}

				return ria_number_french($p['price_ht'], $this->decimals);
			}
		}));

		// Colonne total HT de la ligne de commande
		$this->table()->addColumn(new Column('Mnt Net HT', 'R', 12, function ($p) {
			$total_ht_displayed = ( $p['price_ht'] * $p['qte'] * $p['col_qte'] ) - ( $p['ecotaxe'] * $p['qte'] * $p['col_qte']);

			$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

			// Calcul du montant HT de la ligne
			// Si une remise a été renseignée avec le champ avancé, le calcul de la remise est déjà fait sur le total de la ligne de commande, il n'est donc pas nécessaire de l'appliquer.
			// Sinon, on calcule la remise de la ligne de commande si renseignée
			if ((!is_numeric($fld_discount) || $fld_discount <= 0) && $p['discount'] > 0) {
				if ($p['discount_type'] === "0") { // Euros
					$total_ht_displayed = $p['total_ht'] - $p['discount'];
				} else { // %
					$total_ht_displayed = $p['total_ht'] * (1 - ($p['discount'] / 100));
				}
			}

			if ($p['id'] == 0) {
				return null;
			} else {
				return ria_number_french($total_ht_displayed, $this->decimals);
			}
		}));

		// Colonne D3E
		$this->table()->addColumn(new Column('D3E', 'L', 6, function ($p) {
			if ($p['D3E'] != false && $p['D3E'] > 0) {
				return ria_number_french($p['D3E'] * $p['qte'] * $p['col_qte'], $this->decimals);
			}else{
				return "";
			}
		}));

		// Colonne Licences TNT
		$this->table()->addColumn(new Column('Licences TNT', 'L', 14, function ($p) {
			if ($p['TNT'] != false && $p['TNT'] > 0) {
				return ria_number_french($p['TNT'] * $p['qte'] * $p['col_qte'], $this->decimals);
			}else{
				return "";
			}
		}));

		// Colonne Taxe Mob
		$this->table()->addColumn(new Column('Taxe Mob', 'L', 11, function ($p) {
			if ($p['MOB'] != false && $p['MOB'] > 0) {
				return ria_number_french($p['MOB'] * $p['qte'] * $p['col_qte'], $this->decimals);
			}else{
				return "";
			}
		}));

		foreach( $this->data['ord_products'] as $p ){
			$this->data['ord']['total_D3E'] += ($p['D3E'] * $p['qte'] * $p['col_qte']);
			$this->data['ord']['total_TNT'] += ($p['TNT'] * $p['qte'] * $p['col_qte']);
			$this->data['ord']['total_MOB'] += ($p['MOB'] * $p['qte'] * $p['col_qte']);
		}

		return $this->table();
	}

	/**
	 * Récupère les informations de l'utilisateur
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadUserInfo()
	{
		$this->requireData('ord');

		$r_user = gu_users_get($this->data['ord']['usr_id']);
		if ($r_user && ria_mysql_num_rows($r_user)) {
			$this->withUser(ria_mysql_fetch_assoc($r_user));
		}
	}

	/**
	 * Récupèrer les informations des adresses
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadOrdersAdresses()
	{
		$this->data['addresses'] = ord_orders_address_load($this->requireData('ord'));
		if (false !== $this->data['addresses']) {
			$this->withDlvAdress($this->data['addresses']['delivery']);
			$this->withInvAdress($this->data['addresses']['invoice']);
			return;
		}

		$this->data['addresses'] = array(
			'type' => 'postal',
			'invoice' => array(
				'type' => 'facturation',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			),
			'delivery' => array(
				'type' => 'livraison',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			)
		);
	}

	/**
	 * Génèrer la ligne d'inforamtion utilisateur
	 *
	 * @return void
	 */
	protected function userInfoRow()
	{
		$this->SetXY(170, $this->GetY()+3);
		$this->Cell(20, 10, utf8_decode('Page '.$this->PageNo()), 0, 1, 'R');
	}

	/**
	 * Génèrer le total de page
	 *
	 * @return void
	 */
	protected function PageCount()
	{
		$this->SetXY(20, 30);
		$this->Cell(23, 10, utf8_decode('Page ' . $this->PageNo()), 0, 0, 'C');
	}

	/**
	 * Génère la page du total de la commande
	 *
	 * @return void
	 */
	protected function generateTotalPage()
	{
		$this->requireData('ord');

		$y = $this->GetY() + 4;

		$this->setXY(10, $y - 1);
		$this->SetFont($this->font(), '', 7);
		$this->MultiCell(210, 4, self::conv("Conformément à l'article L2224-13 Contribution Eco Mobilier ".$this->data['ord']['total_MOB']."€\nConformément au décret 2005-829 Contribution DEEE : Total qté commandé ".$this->data['ord']['total_D3E']."€"), 0, 'L');

		$y = $this->GetY() + 15;

		$this->setXY(87, $y - 1);
		$this->SetFont($this->font(), 'B', 8);
		$this->Cell(35, 5, utf8_decode('Soit un total de '.count($this->data['ord_products']).' articles'), 0, 0, 'L');
		$this->setXY(130, $y);
		$end_y = $y;

		$this->SetFont($this->font(), 'B', 9);
		$this->Cell(35, 5, utf8_decode('TOTAL H.T.'), 'TL', 0, 'L');
		$this->Cell(35, 5, $this->price(($this->data['ord']['total_ht'] - $this->data['ord']['total_MOB'] - $this->data['ord']['total_D3E'] - $this->data['ord']['total_TNT']), $this->decimals) . ' ' . $this->currency, 'TR', 1, 'R');
		$end_y += 5;

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), '', 9);
		$this->Cell(35, 5, utf8_decode('Total DEEE'), 'L', 0, 'L');
		$this->Cell(35, 5, $this->price($this->data['ord']['total_D3E'], $this->decimals) . ' ' . $this->currency, 'R', 1, 'C');
		$end_y += 5;

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), '', 9);
		$this->Cell(35, 5, utf8_decode('Total TNT'), 'L', 0, 'L');
		$this->Cell(35, 5, $this->price($this->data['ord']['total_TNT'], $this->decimals) . ' ' . $this->currency, 'R', 1, 'C');
		$end_y += 5;

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), '', 9);
		$this->Cell(35, 5, utf8_decode('Total Eco Mobilier'), 'L', 0, 'L');
		$this->Cell(35, 5, $this->price($this->data['ord']['total_MOB'], $this->decimals) . ' ' . $this->currency, 'R', 1, 'C');
		$end_y += 5;

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), 'B', 9);
		$this->Cell(35, 5, utf8_decode('Total Base TVA'), 'L', 0, 'L');
		$this->Cell(35, 5, $this->price($this->data['ord']['total_ht'], $this->decimals) . ' ' . $this->currency, 'R', 1, 'R');
		$end_y += 5;

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), '', 9);

		foreach ($this->taxes()->tva() as $rate => $tva) {
			if( $this->GetY() > 270 ){
				$this->AddPage();
				$this->SetY( 30 );
				$end_y = $this->GetY();
			}

			$this->setX( 130 );
			$rate = number_format( ($rate - 1) * 100, 2, ',', ' ' );
			$rate = str_replace( ',00', ',0', $rate );

			$this->Cell(35, 5, utf8_decode('Montant TVA à '.$rate.' %'), 'L', 0, 'L');
			$this->Cell(35, 5, $this->price($tva['amount'], $this->decimals) . ' ' . $this->currency, 'R', 1, 'C');
			$end_y += 5;
		}

		$this->setXY(130, $end_y);
		$this->SetFont($this->font(), 'B', 9);
		$this->Cell(35, 5, utf8_decode('Net à Payer en EUR'), 'LB', 0, 'L');
		$this->Cell(35, 5, $this->price($this->data['ord']['total_ttc'], $this->decimals) . ' ' . $this->currency, 'RB', 0, 'R');
		$end_y += 5;

		$this->SetY($end_y);
	}

	public function blocHeader()
	{
		$this->requireData('ord');

		$this->SetY(37);
		$this->SetFont($this->font(), 'B', 20);
		if ($this->data['ord']['state_id'] == 28) {
			$this->MultiCell(50, 4, utf8_decode('DEVIS'), 0, 2, '');
		} else {
			$this->MultiCell(50, 4, utf8_decode('BON DE COMMANDE'), 0, 2, '');
		}

		$this->SetXY(10, $this->getY() + 3);
		$this->SetFont($this->font(), 'B', 9);
		$date = new DateTime($this->data['ord']['date']);
		$this->Cell(30, 6, utf8_decode('Emis le '.$date->format('d/m/y à H:i')), 0, 1, 2);

		$this->SetY($this->getY() + 6);
		$this->resetX();

		$this->SetFont($this->font_family, 'B', 8);
		$this->Cell(30, 4, utf8_decode('N° Pièce'), 0, 0, 'L');
		$this->SetFont($this->font_family, '', 8);
		$this->Cell(40, 4, utf8_decode($this->data['ord']['piece'] != "" ? $this->data['ord']['piece'] : $this->data['ord']['id']), 0, 1, 'R');

		if (trim($this->data['user']['ref']) != '') {
			$this->SetFont($this->font_family, 'B', 8);
			$this->Cell(30, 4, utf8_decode('Client'), 0, 0, 'L');
			$this->SetFont($this->font_family, '', 8);
			$this->Cell(40, 4, utf8_decode($this->data['user']['ref']), 0, 1, 'R');
		}

		$this->SetFont($this->font_family, 'B', 8);
		if ($this->data['ord']['state_id'] == 28) {
			$this->Cell(30, 4, utf8_decode('Réf Devis'), 0, 0, 'L');
		} else {
			$this->Cell(30, 4, utf8_decode('Réf Cde'), 0, 0, 'L');
		}
		$this->SetFont($this->font_family, '', 8);
		$this->Cell(40, 4, utf8_decode($this->data['ord']['ref']), 0, 1, 'R');

		$this->SetFont($this->font_family, 'B', 8);
		$this->Cell(30, 4, utf8_decode('Livraison estimée le'), 0, 0, 'L');
		$date_livr = new DateTime($this->data['ord']['date_livr']);
		$this->SetFont($this->font_family, '', 8);
		$this->Cell(40, 4, utf8_decode($date_livr->format('d/m/y')), 0	, 1, 'R');

		$address = $this->data['addresses']['delivery'];
		$this->SetXY(105,$this->getY() - 25);
		$this->Cell(100, 5, utf8_decode(""), 'TLR', 2);

		// Affiche la socité si elle est renseigné
		if( $society = trim($address['society']) ){
			$this->SetFont($this->font_family, 'B', 11);
			$this->Cell(100, 5, utf8_decode($society), 'LR', 2);
		}

		$name = '';
		$name .= $address['firstname'].' '.$address['lastname'];
		// Affichage du nom et prénom.
		if( $name = trim($name) ){
			$this->Cell(100, 5, utf8_decode($name), 'LR', 2);
		}

		$this->SetFont($this->font(), '', 9);
		$this->Cell(100, 5, utf8_decode($address['address1']), 'LR', 2);

		if( $address2 = trim($address['address2']) ){
			$this->Cell(100, 5, utf8_decode($address2), 'LR', 2);
		}

		// Interligne
		$this->Cell(100, 7, utf8_decode($address['postal_code'].' '.$address['city']), 'LR', 2);
		$this->Cell(100, 3, utf8_decode(""), 'BLR', 2);
	}

	public function blocFooter()
	{
		$y = $this->GetY();
		$this->SetY($y);

		$this->SetFont($this->font(), 'BU', 8);
		$this->Cell(90, 10, utf8_decode('Bon pour Accord'), 'TRL', 1, 'L');
		$this->Cell(90, 20, '', 'BRL', 0);

		$y = $this->GetY() + 36;

		if ($this->getOption('footer')) {
			$this->SetXY(10, $y);
			$this->SetFont($this->font(), '', 9);
			$this->MultiCell(0, 5, self::conv($this->requireOption('footer_content')), 0, 'C', '');
			$this->Cell(0, 2, '', 0, 2);

			$y = $this->GetY() + 10;
		}

		$this->SetY($y);
		$this->SetFont($this->font(), '', 7);
		$this->MultiCell(180, 5, self::conv("La loi n° 2012-387 dite loi « Warsmann » en vigueur au 01/01/2013: « Conformément aux articles L.441-3 et 441-6 du Code du Commerce, tout professionnel en situation de retard de paiement est de plein droit débiteur à notre égard d'une indemnité forfaitaire pour frais de recouvrement d'un montant de 40 euros. Si les frais de recouvrement exposés sont supérieurs, nous nous réservons la faculté de demander une indemnisation complémentaire, sur justification. »"), 0, 'L', '');

		// Récupère la signature et ses dimensions
		$file_sign = export_order_create_sign($this->data['ord']['id']);

		if (file_exists($file_sign)) {
			$img_sign_sizes = getimagesize($file_sign);

			if (isset($img_sign_sizes[0], $img_sign_sizes[1])) {
				if ($img_sign_sizes[0] == $img_sign_sizes[1]) {
					// La signature est carrée
					$this->Image($file_sign, $this->GetX(), $this->GetY(), 20, 20);
				} elseif ($img_sign_sizes[1] > $img_sign_sizes[0]) {
					// La hauteur est supérieur à la largueur
					$this->Image($file_sign, $this->GetX(), $this->GetY(), 10, 20);
				} else {
					// La largueur est supérieure à la hauteur
					$this->Image($file_sign, $this->GetX(), $this->GetY(), 20, 10);
				}
			}

			// La signature est tout de suite supprimée après utilisation
			unlink($file_sign);
		}
	}
}

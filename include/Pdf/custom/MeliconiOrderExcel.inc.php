<?php
	/** \file FrancodexOrderExcel.inc.php
	 * 	Ce fichier permet de générer un fichier Excel spécifique aux devis / bon de commande pour Francodex.
	 * 	Il est appelé directement dans le moteur (cf. include/Export/orders.inc.php - function export_order_excel)
	 */

/**
 * Export Excel de la commande : liste des produits
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function meliconi_export_order_excel_product_table( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;

	$current_col = $col;
	$show_prd_img = isset($options['prd_img']) && !is_null($options['prd_img']) && isset($config['img_sizes']['small']);

	$style = export_order_excel_default_style();
	$borders = export_order_excel_thin_borders();
	$style = array_merge($style, $borders);

	$style_body = $style;
	if (isset($options['font_size']) && $options['font_size']) {
		$style_body = array_merge(
			$style_body,
			array(
				'font'=>array('size' => $options['font_size']),
				'alignment' => array('vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER)
			)
		);
	}

	$style['font']['size'] = 7;
	$style['font']['bold'] = true;

	if( $show_prd_img ){
		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			'Image',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		$current_col++;
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		$current_col,
		$row,
		'Référence',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Désignation',
		PHPExcel_Cell_DataType::TYPE_STRING
	);

	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style);
	$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Qté',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Prix Net HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Eco-part.',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	if (isset($options['prd_reduce']) && $options['prd_reduce']) {
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Remise',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	}

	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Montant HT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	
	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'D3E',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	
	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Licence TNT',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
	
	$sheet->setCellValueExplicitByColumnAndRow(
		++$current_col,
		$row,
		'Taxe Mob',
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			'Code EAN',
			PHPExcel_Cell_DataType::TYPE_STRING
		);
	}
	$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);

	$row++;

	$decimals = 2;
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$decimals = 0;
	}

	foreach ($data['ord_products'] as & $p) {
		$current_col = $col;
		$fields = '';

		$p['D3E'] = $p['TNT'] = $p['MOB'] = false;
		$json = fld_object_values_get(array($p['ord_id'],$p['id'], $p['line']), 5074);
		$ar_fld = json_decode($json, true);
		if (isset($ar_fld['taxes']) && isset($ar_fld['taxes']['detail'])) {
			foreach($ar_fld['taxes']['detail'] as $taxes){
				if ($taxes['type'] == '3'){

					// $ar_D3E = ['D3E', 'D3E05', 'D3E08', 'D3E15', 'D3E16', 'D3E30', 'D3E83', 'D3EB', 'D4E', 'D4EC', 'D4ECB'];
					// $ar_TNT = ['TNT', 'TNT1', 'TNT2', 'TNT3', 'TNT4'];
					if( strpos($taxes['name'], 'D3E') !== false || strpos($taxes['name'], 'D4E') !== false) {
						$p['D3E'] = $taxes['price'];
					} else if(strpos($taxes['name'], 'TNT') !== false) {
						$p['TNT'] = $taxes['price'];
					} else if (strpos($taxes['name'], "MOB") !== false){
						$p['MOB'] = $taxes['price'];
					}
				}
			}
			$data['ord']['total_D3E'] += ($p['D3E'] * $p['qte'] * $p['col_qte']);
			$data['ord']['total_TNT'] += ($p['TNT'] * $p['qte'] * $p['col_qte']);
			$data['ord']['total_MOB'] += ($p['MOB'] * $p['qte'] * $p['col_qte']);
		}
		if( isset($p['fields']) && is_array($p['fields']) && count($p['fields']) ){

			foreach($p['fields'] as $name => $val){
				$fields .= "\r".$name.' : '.$val;
			}

		}

		if( $show_prd_img){
			if ($p['img_id']
				&& file_exists($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format'])
			) {
				$objDrawing = new PHPExcel_Worksheet_Drawing();
				$objDrawing->setPath($config['img_dir'].'/'.$config['img_sizes']['small']['dir'].'/'.$p['img_id'].'.'.$config['img_sizes']['small']['format']);
				$objDrawing->setWidth($config['img_sizes']['small']['width']);
				$objDrawing->setHeight($config['img_sizes']['small']['height']);
				$objDrawing->setWorksheet($sheet);
				$objDrawing->setCoordinates($sheet->getCellByColumnAndRow($current_col, $row)->getCoordinate());
			}

			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
			$sheet->getRowDimension($row)->setRowHeight($config['img_sizes']['small']['height']);
			$current_col++;
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			$current_col,
			$row,
			$p['id'] == 0 ? '' : $p['ref'],
			PHPExcel_Cell_DataType::TYPE_STRING
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$colisage_infos = ($p['col_id'] > 0 && $p['col_name'] != '') ? ' '.$p['col_name'].' ('.round($p['col_qte'], $decimals).')':'';
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['name'].$colisage_infos.$fields,
			PHPExcel_Cell_DataType::TYPE_STRING
		);

		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		$sheet->getStyleByColumnAndRow($current_col + 1, $row)->applyFromArray($style_body);
		$sheet->mergeCellsByColumnAndRow($current_col, $row, ++$current_col, $row);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : floatval($p['qte']),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$price_ht = $p['price_ht'];
		if (isset($options['prd_reduce']) && $options['prd_reduce']) {
			if (isset($p['price_brut_ht'])) {
				$price_ht = $p['price_brut_ht'];
			}else{
				if ($p['discount'] > 0){
					if ($p['discount_type'] === "0"){ // Euros
						$price_ht = $p['price_ht'] + $p['discount'];
					} else { // %
						$price_ht = ($p['price_ht'] * 100 ) / (100 - $p['discount']);
					}
				}
			}

			// Charge la remise depuis le champ avancé Yuto prévu à cet effet
			$fld_discount = fld_object_values_get([$p['ord_id'], $p['id'], $p['line']], _FLD_ORD_LINE_DISCOUNT, '', false, true );
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$price_ht = ($p['price_ht'] * 100 ) / (100 - $fld_discount);
			}
		}

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] ==0 ? null : round($price_ht, $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		if (isset($options['prd_ecotaxe']) && $options['prd_ecotaxe']) {
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$p['id'] == 0 ? null : round($p['ecotaxe'], $decimals),
				PHPExcel_Cell_DataType::TYPE_NUMERIC
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		if (isset($options['prd_reduce']) && $options['prd_reduce']) {
			$remise = 0;
			if (isset($p['price_brut_ht'])) {
				$remise = round(100 - ($p['price_ht'] * 100 / $p['price_brut_ht']), 0);
			}

			if ($p['discount'] > 0){
				if ($p['discount_type'] === "0"){ // Euros
					$remise = floatval(round($p['discount'], $decimals)).' '.$data['currency'];
				} else { // %
					$remise = floatval(round($p['discount'], 0)).' %';
				}
			}

			// Charge la remise depuis le champ avancé Yuto prévu à cet effet
			$fld_discount = fld_object_values_get([$p['ord_id'], $p['id'], $p['line']], _FLD_ORD_LINE_DISCOUNT, '', false, true );
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$remise = floatval( str_replace('.00', '', round($fld_discount, 2))).' %';
			}

			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				$remise == 0 ? null : str_replace('.', ',', $remise),
				PHPExcel_Cell_DataType::TYPE_STRING
			);

			$style = [ 'font' => [ 'size' => 8 ], 'borders' => [ 'allborders' => [ 'style' => 'thin' ] ],'alignment' => [ 'horizontal' => 'right', 'vertical' => 'center' ] ];
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style);
		}

		// Pour Meliconi on récupère les totaux des différentes taxes, D3E, TNT ou MOB et on les soustrait au Montant HT
		$prd_D3E = ($p['id'] == 0 || $p['D3E'] == false )? 0 : round(($p['D3E'] * $p['qte'] * $p['col_qte']), $decimals);
		$prd_TNT = ($p['id'] == 0 || $p['TNT'] == false )? 0 : round(($p['TNT'] * $p['qte'] * $p['col_qte']), $decimals);
		$prd_MOB = ($p['id'] == 0 || $p['MOB'] == false )? 0 : round(($p['MOB'] * $p['qte'] * $p['col_qte']), $decimals);
		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$p['id'] == 0 ? null : round($p['discount_type'] === "0" ? $p['total_ht'] - $p['discount'] - $prd_D3E - $prd_TNT - $prd_MOB : $p['total_ht'] * (1-($p['discount']/100))  - $prd_D3E - $prd_TNT - $prd_MOB , $decimals),
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$prd_D3E,
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$prd_TNT,
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		$sheet->setCellValueExplicitByColumnAndRow(
			++$current_col,
			$row,
			$prd_MOB,
			PHPExcel_Cell_DataType::TYPE_NUMERIC
		);
		$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);

		if( isset($options['prd_barcode']) && $options['prd_barcode'] ){
			$sheet->setCellValueExplicitByColumnAndRow(
				++$current_col,
				$row,
				prd_products_get_barcode($p['id']),
				PHPExcel_Cell_DataType::TYPE_STRING
			);
			$sheet->getStyleByColumnAndRow($current_col, $row)->applyFromArray($style_body);
		}

		$row++;
	}

	return array($current_col, $row);
}



/**
 * Export Excel de la commande : liste des taxes
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function meliconi_export_order_excel_taxes( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0){
	global $config;
  
	  $style = export_order_excel_default_style();
	  $borders = export_order_excel_thin_borders();
	  $style = array_merge($style, $borders);
	  $style_body = $style;
  
	  $decimals = 2;
	  if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		  $decimals = 0;
	  }
  
	  $style['font']['bold'] = true;
	  $style['font']['size'] = 8;
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col,
		  $row,
		  'CODE',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 1,
		  $row,
		  'BASE',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 2,
		  $row,
		  'TAUX',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 3,
		  $row,
		  'MONTANT',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style);
  
	  $row++;
	  $total = 0;
	  foreach ($data['tva'] as $rate => $tva) {
		  $sheet->setCellValueExplicitByColumnAndRow(
			  $col,
			  $row,
			  'Tva',
			  PHPExcel_Cell_DataType::TYPE_STRING
		  );
		  $sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);
  
		  $sheet->setCellValueExplicitByColumnAndRow(
			  $col + 1,
			  $row,
			  number_format($tva['base'] + $data['ecotaxe']['base'], $decimals, ',', ' ').' '.$data['currency'],
			  PHPExcel_Cell_DataType::TYPE_STRING
		  );
		  $sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);
  
		  $sheet->setCellValueExplicitByColumnAndRow(
			  $col + 2,
			  $row,
			  round(($rate - 1) * 100, $decimals) . ' %',
			  PHPExcel_Cell_DataType::TYPE_STRING
		  );
		  $sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);
  
		  $sheet->setCellValueExplicitByColumnAndRow(
			  $col + 3,
			  $row,
			  number_format($tva['amount'] + $data['ecotaxe']['amount'], $decimals, ',', ' ').' '.$data['currency'],
			  PHPExcel_Cell_DataType::TYPE_STRING
		  );
		  $sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style_body);
	  }
  
	  return array($col + 3, $row);
  }
		
	// Gestion de l'arrondi des tarifs à zéro chiffre après la virgule
	if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		$data['ord']['total_ht'] = round( $data['ord']['total_ht'] );
		$data['ord']['total_ttc'] = round( $data['ord']['total_ttc'] );

		if( isset($data['ord_products']) && is_array($data['ord_products']) ){
			foreach( $data['ord_products'] as &$d ){
				$d['price_ht'] = round( $d['price_ht'] );
				$d['price_ttc'] = round( $d['price_ttc'] );
				$d['total_ht'] = round( $d['total_ht'] );
				$d['total_ttc'] = round( $d['total_ttc'] );
				$d['ecotaxe'] = round( $d['ecotaxe'] );
			}
		}
	}
	
/**
 * Export Excel de la commande : totaux
 *
 * @param PHPExcel_Worksheet & $sheet Feuille Excel
 * @param array & $data Données de la commande
 * @param array|null & $options Options d'export
 * @param int $col Colonne de gauche où placer le contenu
 * @param int $row Première ligne où placer le contenu
 *
 * @return array Coordonnées de la cellule en bas à droite du contenu
 */
function meliconi_export_order_excel_totals( & $sheet, array & $data, array & $options = null, $col = 0, $row = 0 ){
	global $config;
  
	  $style = export_order_excel_default_style();
	  $borders = export_order_excel_thin_borders();
	  $style = array_merge($style, $borders);
	  $style_body = $style;
  
	  $decimals = 2;
	  if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
		  $decimals = 0;
	  }
  
	  $style['font']['bold'] = true;
	  $style['font']['size'] = 8;
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col,
		  $row,
		  'TOTAL H.T.',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 1,
		  $row,
		  'TOTAL T.T.C.',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 2,
		  $row,
		  'D3E',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 3,
		  $row,
		  'Licence TNT',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 4,
		  $row,
		  'Taxe Mob',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 4, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 5,
		  $row,
		  'NET A PAYER',
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 5, $row)->applyFromArray($style);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col,
		  ++$row,
		  number_format($data['ord']['total_ht'] - $data['ord']['total_D3E'] - $data['ord']['total_TNT'] - $data['ord']['total_MOB'], $decimals, ',', ' ').' '.$data['currency'],
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col, $row)->applyFromArray($style_body);
  
	  $sheet->setCellValueExplicitByColumnAndRow(
		$col + 1,
		$row,
		number_format($data['ord']['total_ttc'] - $data['ord']['total_D3E'] - $data['ord']['total_TNT'] - $data['ord']['total_MOB'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 1, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 2,
		$row,
		number_format( $data['ord']['total_D3E'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 2, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 3,
		$row,
		number_format( $data['ord']['total_TNT'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 3, $row)->applyFromArray($style_body);

	$sheet->setCellValueExplicitByColumnAndRow(
		$col + 4,
		$row,
		number_format( $data['ord']['total_MOB'], $decimals, ',', ' ').' '.$data['currency'],
		PHPExcel_Cell_DataType::TYPE_STRING
	);
	$sheet->getStyleByColumnAndRow($col + 4, $row)->applyFromArray($style_body);

	  $sheet->setCellValueExplicitByColumnAndRow(
		  $col + 5,
		  $row,
		  number_format($data['ord']['total_ttc'], $decimals, ',', ' ').' '.$data['currency'],
		  PHPExcel_Cell_DataType::TYPE_STRING
	  );
	  $sheet->getStyleByColumnAndRow($col + 5, $row)->applyFromArray($style_body);
  
	  return array($col + 2, $row);
  }
  

	// Initialisation de la devise
	export_order_excel_set_currency( $data );

	$data['file_sign'] = export_order_create_sign( $data['ord']['id'] );

	// Ajout des champs avancé pour meliconi dans les data
	$data['ord']['total_D3E'] = $data['ord']['total_MOB'] = $data['ord']['total_TNT'] = 0;

	$doc = new PHPExcel();

	$first_line = 5;
	$sheet = $doc->getActiveSheet();
	$sheet->getDefaultStyle()->applyFromArray(export_order_excel_default_style());
	$sheet->getDefaultColumnDimension()->setWidth(18);

	list($last_logo_col, $last_logo_row) = export_order_excel_logo($sheet, $data, $options, 5, 1);
	list($last_owner_col, $last_owner_row) = export_order_excel_owner($sheet, $data, $options, 0, 1);
	list($last_adr_col, $last_adr_row) = export_order_excel_addresses($sheet, $data, $options, 5, 0, $last_owner_row + 2);
	list($last_header_col, $last_header_row) = export_order_excel_header($sheet, $data, $options, 0, $last_adr_row);

	list($last_user_info_col, $last_user_info_row) = export_order_excel_user_info($sheet, $data, $options, 0, $last_header_row + 2);
	list($last_products_col, $last_products_row) = meliconi_export_order_excel_product_table($sheet, $data, $options, 0, $last_user_info_row + 2);
	list($last_taxes_col, $last_taxes_row) = meliconi_export_order_excel_taxes($sheet, $data, $options, 0, $last_products_row + 2);
	$totals_first_col = max($last_taxes_col + 1, 5);
	list($last_totals_col, $last_totals_row) = meliconi_export_order_excel_totals($sheet, $data, $options, $totals_first_col, $last_products_row + 2);
	list($last_payments_col, $last_payments_row) = export_order_excel_payments($sheet, $data, $options, $totals_first_col, $last_totals_row + 1);

	list($last_footer_col, $last_footer_row) = export_order_excel_footer($sheet, $data, $options, 0, $last_payments_row+ 2);

	$last_bank_details_col = 0;
	$last_bank_details_row = 1;
	if( $data['ord']['pay_id'] == _PAY_VIREMENT ){
		list($last_bank_details_col, $last_bank_details_row) = export_order_excel_bank_details($sheet, $data, $options, 0, $last_footer_row + 2);
	}

	$max_col = max(
		$last_logo_col,
		$last_owner_col,
		$last_adr_col,
		$last_header_col,
		$last_user_info_col,
		$last_products_col,
		$last_taxes_col,
		$last_totals_col,
		$last_payments_col,
		$last_footer_col,
		$last_bank_details_col
	) + 1;

	$max_row = max(
		$last_logo_row,
		$last_owner_row,
		$last_adr_row,
		$last_header_row,
		$last_user_info_row,
		$last_products_row,
		$last_taxes_row,
		$last_totals_row,
		$last_payments_row,
		$last_footer_row,
		$last_bank_details_row
	) + 1;

	$sheet->getPageSetup()->setPrintAreaByColumnAndRow(0, 1, $max_col, $max_row);
	$sheet->getPageMargins()
		->setLeft(0.5)
		->setRight(0.5);

	$sheet->setShowGridlines(false);

	$output_writer = isset($options['output_writer']) ? $options['output_writer'] : 'Excel5';

	$writer = PHPExcel_IOFactory::createWriter($doc, $output_writer);

	switch ($output_writer) {
		case 'CSV':
			$writerExtension = '.csv';
			break;
		case 'Excel5':
			$writerExtension = '.xls';
			break;
		case 'PDF':
			$writerExtension = '.pdf';
			break;
		case 'HTML':
			$writerExtension = '.html';
			break;
		case 'Serialized':
			$writerExtension = '.txt';
			break;
		case 'Excel2007':
		default:
			$writerExtension = '.xlsx';
			break;
	}

	$output = isset($options['output']) && trim($options['output']) != '' ? $options['output'] : 'D';
	$filename = isset($options['filename']) && trim($options['filename']) != '' ? $options['filename'] : 'order' . $writerExtension;

	if ($output == 'D' || $output == 'I') {
		try {
			// Charge le contenu du fichier Excel dans une variable temporaire
			// afin de supprimer le fichier image de la signature
			ob_start();
			$writer->save('php://output');
			$content = ob_get_clean();

			// La signature est tout de suite supprimée après utilisation
			if( file_exists($data['file_sign']) ){
				unlink($data['file_sign']);
			}

			// Charge le contenu du fichier Excel
			header('Content-Type: application/vnd.ms-excel');
			header('Content-Disposition: attachment;filename="'.$filename.'"');
			print $content;
			exit;
		}catch( \Exception $e ){
			throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
		}
	}else{
		//Save to local file
		if (!isset($options['filename'])) {
			throw new OrderExportException($data['ord']['id'], 'Impossible d\'écrire le fichier.');
		}

		try {
			$writer->save($options['filename']);
			// La signature est tout de suite supprimée après utilisation
			if( file_exists($data['file_sign']) ){
				unlink($data['file_sign']);
			}
		}catch( \Exception $e ){
			throw new OrderExportException($data['ord']['id'], 'Erreur lors de l\'écriture du fichier.');
		}
	}

<?php
	/** \file SaintBernardOrderExcel.inc.php
	 * 	Ce fichier permet de générer un fichier Excel spécifique aux devis / bon de commande pour SaintBernard.
	 * 	Il est appelé directement dans le moteur (cf. include/Export/orders.inc.php - function export_order_excel)
	 */

	require_once('Barcode.inc.php');

	// Style par défaut des cellules
	$style = export_order_excel_default_style();

	// Entête du tableau
	$ar_head = [
		'A' => [2.1, 'CODE'],
		'B' => [6.39, 'DESIGNATION'],
		'C' => [4.4, 'GENCOD'],
		'D' => [1.35, 'QTE'],
		'E' => [2.1, 'GRATUIT'],
		'F' => [2.1, 'PA BRUT'],
		'G' => [2.1, 'REMISE 1'],
		'H' => [2.1, 'REMISE 2'],
		'I' => [2.1, 'REMISE 3'],
		'J' => [2.1, 'SUR REMISE'],
		'K' => [2.1, 'PA NET'],
		'L' => [2.5, 'MONTANT HT + ECOTAXE'],
		'M' => [2.1, 'TVA'],
		'N' => [2.1, 'DEE'],
		'O' => [2.1, 'TEM'],
		'P' => [2.1, 'TECC'],
		'Q' => [2.7, 'IMAGE'],
	];

	// Création du document Excel
	$doc = new PHPExcel();

	// Active la première feuille et applique le style par défaut
	$sheet = $doc->getActiveSheet();
	$sheet->getDefaultStyle()->applyFromArray(['font' => ['name' => 'Arial', 'size' => 10]]);
	$sheet->getDefaultColumnDimension()->setWidth(13);

	// Applique la taille des colonnes
	$i = 0;
	foreach( $ar_head as $col=>$info ){
		$sheet->getColumnDimension( $col )->setWidth( zolux_convert_pt($info[0]) );
		$sheet->setCellValueByColumnAndRow( ($i++), 3, $info[1] );
	}

	// Titre
	$sheet->mergeCells('A1:Q1');
	if( $data['ord']['state_id'] == _STATE_DEVIS ){
		$sheet->setCellValueByColumnAndRow( 0, 1, 'DEVIS' );
	}else{
		$sheet->setCellValueByColumnAndRow( 0, 1, 'BON DE COMMANDE' );
	}
	$sheet->getStyleByColumnAndRow('A', 1)->applyFromArray(array_merge( $style, [
		'font' => [
			'size' => 18,
			'bold' => true
		],
		'alignment' => [
			'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
			'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER
		]
	]));

	// Logo + Information sur le client
	$user = $data['user']['ref'];
	if( trim($data['user']['adr_firstname'].' '.$data['user']['adr_lastname']) != '' ){
		$user .= ' - '.$data['user']['adr_firstname'].' '.$data['user']['adr_lastname'];
	}
	if( trim($data['user']['society']) != '' ){
		$user .= ' - '.$data['user']['society'];
	}
	$user .= $data['user']['address1'].' '.$data['user']['address2'].' '.$data['user']['zipcode'].' '.$data['user']['city'];
	$user = trim( strtoupper2($user) );

	$sheet->mergeCells('A2:Q2');
	$sheet->setCellValueByColumnAndRow( 0, 2, $user );
	$sheet->getRowDimension(2)->setRowHeight( zolux_convert_pt(2.54, true) );
	$sheet->getStyleByColumnAndRow('A', 2)->applyFromArray(array_merge( $style, [
		'alignment' => [
			'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
			'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER
		]
	]));

	// Dimensions de l'image principale des produits inclue dans le ficher Excel
	$thumb = $config['img_sizes']['small'];

	$ar_line_deee = [];

	// Gestion des lignes de commandes
	$start_line = $prd_line = 4;
	foreach( $data['ord_products'] as $info ){
		if( trim($info['name']) == '' ){
			continue;
		}

		// Complète le nom du produit avec la quantité du conditionnement
		if( is_numeric($info['col_qte']) && $info['col_qte'] > 1 ){
			$info['name'] .= ' (Par '.round( $info['col_qte'] ).')';
		}

		// Récupère le code à barres
		$info['barcode'] = prd_products_get_barcode( $info['id'] );

		// Détails sur l'ecotaxe ajouté en dessous de la désignation
		$d3e_txt = $tem_txt = $tecc_txt = '';
		$total_d3e = 0; // Total d3e à l'unité

		$d3e = fld_object_values_get( [$info['ord_id'], $info['id'], $info['line']], _FLD_PRD_ORD_DEEE );
		if( trim($d3e) != '' ){
			$d3e = json_decode( $d3e, true );

			if( isset($d3e['taxes']['detail']) && is_array($d3e['taxes']['detail']) ){
				foreach( $d3e['taxes']['detail'] as $one_d3e ){
					if( !ria_array_key_exists(['name', 'price'], $one_d3e) ){
						continue;
					}

					$code = 'd3e_txt';
					if( strstr($one_d3e['name'], 'TECC') ){
						$code = 'tecc_txt';
					}elseif( strstr($one_d3e['name'], 'TEM') ){
						$code = 'tem_txt';
					}

					if( $$code != '' ){
						$$code .= ', ';
					}

					$$code .= str_replace( ["\n", "\r", "\n\r", "\r\n"], ' ', $one_d3e['name'] ).' : '.$one_d3e['price'];
					$total_d3e += $one_d3e['price'];
				}
			}
		}

		// Récupère la remise, le pourcentage de remise ainsi que le prix brut
		$price_brut = $info['price_ht'];

		$db_price_brut = fld_object_values_get( [$info['ord_id'], $info['id'], $info['line']], _FLD_PRD_ORD_PRICE_BRUT );
		if( is_numeric($db_price_brut) && $db_price_brut > 0 ){
			$price_brut = $db_price_brut;
		}

		$have_image = false;
		// Ajout de l'image principal du produit
		if( is_numeric($info['img_id']) && $info['img_id'] > 0 ){
			$file_img = $config['img_dir'].'/'.$thumb['dir'].'/'.$info['img_id'].'.'.$thumb['format'];

			if( file_exists($file_img) ){
				$have_image = true;

				$objDrawing = new PHPExcel_Worksheet_Drawing();
				$objDrawing->setPath($file_img);
				$objDrawing->setOffsetX(1);
				$objDrawing->setOffsetY(2);
				$objDrawing->setWorksheet($sheet);
				$objDrawing->setCoordinates('Q'.$prd_line);

				// Règle la taille de la ligne si une image est présente
				$sheet->getRowDimension($prd_line)->setRowHeight( zolux_convert_pt(2.2, true) );
			}
		}

		// Ajout de l'image scannable pour le code barre
		$barcode = false;

		if( $barcode !== false ){
			$objDrawing = new PHPExcel_Worksheet_Drawing();
			$objDrawing->setPath($barcode);
			$objDrawing->setHeight(30);
			$objDrawing->setWidth(147);
			$objDrawing->setOffsetX(1);
			$objDrawing->setWorksheet($sheet);
			$objDrawing->setCoordinates('C'.$prd_line);

			// Règle la taille de la ligne si une image est présente
			$sheet->setCellValueByColumnAndRow( 2, $prd_line, ' ' );

			if( $have_image ){
				$objDrawing->setOffsetY(25);
			}else{
				// Règle la taille de la ligne si auncune image est présente, mais qu'il y a un code barre
				$sheet->getRowDimension($prd_line)->setRowHeight( zolux_convert_pt(1.25, true) );
				$objDrawing->setOffsetY(10);
			}
		}else{
			$sheet->setCellValueByColumnAndRow( 2, $prd_line, '="'.$info['barcode'].'"' );
		}

		$info['name'] = preg_replace('/[\ ]+/i', ' ', $info['name']);

		// Ajout des informations sur la ligne de commande
		$sheet->setCellValueByColumnAndRow( 0, $prd_line, $info['ref'] );
		$sheet->setCellValue( 'B'.$prd_line, $info['name'] );
		$sheet->setCellValueByColumnAndRow( 3, $prd_line, $info['qte'] );
		$sheet->setCellValueByColumnAndRow( 4, $prd_line, 0 );
		$sheet->setCellValueByColumnAndRow( 5, $prd_line, $price_brut );

		// print_r($d3e);
		if( isset($d3e['detail']) && is_array($d3e['detail']) ){
			$i = 6;
			foreach( $d3e['detail'] as $one_remise ){
				if( $i > 8 ){
					// On affiche pas plus de 3 remises
					break;
				}

				if( $one_remise['price'] > 0.01 ){
					$sheet->setCellValueByColumnAndRow( $i, $prd_line, $one_remise['price'] / 100 );
					$i++;
				}
			}
		}

		// Recherche d'une remise sur la ligne de commande
		$db_pourcent = fld_object_values_get( [$info['ord_id'], $info['id'], $info['line']], _FLD_ORD_LINE_DISCOUNT );
		if( $db_pourcent > 0 ){
			$sheet->setCellValueByColumnAndRow( 9, $prd_line, $db_pourcent / 100 );
		}


		$sheet->setCellValueByColumnAndRow( 10, $prd_line, ($info['price_ht'] - $total_d3e) );
		$sheet->setCellValueByColumnAndRow( 11, $prd_line, $info['total_ht'] );
		$sheet->setCellValueByColumnAndRow( 12, $prd_line, $info['tva_rate'] - 1 );


		$sheet->setCellValueByColumnAndRow( 13, $prd_line, $d3e_txt );
		$sheet->setCellValueByColumnAndRow( 14, $prd_line, $tem_txt );
		$sheet->setCellValueByColumnAndRow( 15, $prd_line, $tecc_txt );
		$prd_line++;
	}

	// Détermine la ligne de fin
	$last_line = $prd_line - 1;

	// Totaux
	$sheet->mergeCells('A'.($last_line + 1).':J'.($last_line + 1));
	$sheet->setCellValueByColumnAndRow( 10, $last_line + 1, 'TOTAL' );
	$sheet->setCellValueByColumnAndRow( 11, $last_line + 1, $data['ord']['total_ht'] );
	$sheet->mergeCells('M'.($last_line + 1).':Q'.($last_line + 1));

	// Activation des formats de données par colonne
	$sheet->getStyle('F'.$start_line.':F'.$last_line)->getNumberFormat()->setFormatCode( '#,##0.00_-€' );
	$sheet->getStyle('K'.$start_line.':K'.$last_line)->getNumberFormat()->setFormatCode( '#,##0.00_-€' );
	$sheet->getStyle('L'.$start_line.':L'.($last_line + 1))->getNumberFormat()->setFormatCode( '#,##0.00_-€' );
	$sheet->getStyle('G'.$start_line.':J'.$last_line)->getNumberFormat()->setFormatCode( PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 );
	$sheet->getStyle('M'.$start_line.':M'.$last_line)->getNumberFormat()->setFormatCode( PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00 );

	$sheet->getStyle('A3:O3')->getAlignment()->setWrapText(true);
	// Applique du style sur toutes les cellules
	$sheet->getStyle('A3:O3')->applyFromArray(array_merge( $style, [
		'alignment' => [
			'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
			'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
		]
	]));

	$sheet->getStyle('N'.$start_line.':P'.$last_line)->getAlignment()->setWrapText(true);

	// Applique des bordures
	$sheet->getStyle('A1:Q'.($last_line + 1))->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);

	// Applique du style sur toutes les cellules
	$sheet->getStyle('A4:Q'.($last_line + 1))->applyFromArray(array_merge( $style, [
		'alignment' => [
			'vertical' => PHPExcel_Style_Alignment::VERTICAL_TOP
		]
	]));

	$sheet->getStyle('L'.($last_line + 1))->applyFromArray([
		'font' => [
			'bold' => true
		]
	]);

	// Ajout du logo dans l'entête
	$logo = '';
	$r_img = img_images_get( $options['logo'] );
	if( $r_img && ria_mysql_num_rows($r_img) ){
		$img = ria_mysql_fetch_assoc($r_img);

		$file = img_images_get_filesource( $img['id'] );
		if( trim($file) != '' ){
			$logo = $config['img_dir'].'/source/'.$file;
		}
	}

	$objDrawing = new PHPExcel_Worksheet_Drawing();
	$objDrawing->setPath($logo);
	$objDrawing->setHeight(50);
	$objDrawing->setOffsetY(20);
	$objDrawing2 = clone $objDrawing;

	$objDrawing->setWorksheet($sheet);
	$objDrawing->setCoordinates('B2');

	$objDrawing2->setWorksheet($sheet);
	$objDrawing2->setCoordinates('M2');

	// Génération du fichier Excel
	$filename = isset($options['filename']) && trim($options['filename']) != '' ? $options['filename'] : 'order.xls';
	$writer = PHPExcel_IOFactory::createWriter($doc, 'Excel5');

	// Téléchargement du fichier Excel
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'.$filename.'"');
	$writer->save('php://output');

	function zolux_convert_pt( $cm, $height=false ){
		if( $height ){
			return ( $cm * 10 ) / 0.35;
		}else{
			return $cm * 10 / 2.1;
		}
	}

	function zolux_barcode( $barcode ){
		global $config;

		$file_barcode = $config['img_dir']. '/barcodes/'.htmlentities($barcode,ENT_QUOTES,"ISO8859-1").'.png';

		// Création du fichier image code barre si celui-ci n'existe pas
		if( !file_exists($file_barcode) ){
			$bc = new Barcode();
			$bc->setCode((string) $barcode);
			$bc->setType('EAN');
			$bc->setSize(30, 147, 10);
			$bc->setText('');
			$bc->hideCodeType();
			$bc->setColors('#000000', '#FFFFFF');
			$bc->setFiletype('PNG');

			$bc->writeBarcodeFile( $file_barcode );
		}

		return file_exists( $file_barcode ) ? $file_barcode : false;
	}

<?php

/** \file SaintBernardOrderPdf.inc.php
 * 	Ce fichier ce charge de générer le PDF d'un devis ou d'une commande spécifique à Zolux.
 */
namespace Pdf;

use DateTime;

require_once('users.inc.php');
require_once('orders.inc.php');
require_once('fields.inc.php');
require_once('Barcode.inc.php');

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');



class SaintBernardOrderPdf extends PieceDeVente {
	const ENDPAGE = 275;

	// Cette variable est chargée en même temps que l'on parcours les lignes du devis
	// L'index correspond au taux de TVA
	// La valeur est un tableau comme ceci [total ht, total tva]
	private $ar_tva = []; ///< Contient les TVA appliqués sur les lignes du devis

	/** Charge le header sur toutes les pages du PDF. */
	public function Header(){
		$this->SetXY( self::rpp(90), self::rpp(50) );

		// Charge le logo depuis la configuration
		$this->loadLogo();

		// Affichage du logo sur le PDF
		$this->Image( $this->data['logo'], $this->GetX(), $this->GetY(), self::rpp($this->options['logo_size_x']), self::rpp($this->options['logo_size_y']));

		// Affiche la référence du devis
		$num_ord = ord_orders_name( $this->data['ord']['ref'], $this->data['ord']['piece'], $this->data['ord']['id'] );
		$num_ord = self::conv( $num_ord );
		if( strlen($num_ord) > 17 ){
			$num_ord = substr( $num_ord, 0, 14 ).'...';
		}

		$this->data['ord']['ref'] = self::conv( $this->data['ord']['ref'] );
		if( strlen($this->data['ord']['ref']) > 17 ){
			$this->data['ord']['ref'] = substr( $this->data['ord']['ref'], 0, 14 ).'...';
		}

		$this->SetFont( 'Arial', 'B', 10 );
		$this->SetXY( self::rpp(50 + $this->options['logo_size_x'] + 80), self::rpp(60) );

		if( $this->data['ord']['state_id'] == _STATE_DEVIS ){
			$this->Cell( self::rpp(1000), self::rpp(100), '  DEVIS', 1, self::rpp(100) );
		}else{
			$this->Cell( self::rpp(1000), self::rpp(100), '  BON DE COMMANDE', 1, self::rpp(100) );
		}

		$this->SetXY( self::rpp(1350), self::rpp(60) );
		$this->Cell( self::rpp(400), self::rpp(100), ($num_ord), 0, self::rpp(100) );

		// Affichage de la date + la pagination (Entête)
		$this->SetFont( 'Arial', 'B', 7 );
		$this->SetXY( self::rpp(1800), self::rpp(60) );
		$this->Cell( self::rpp(500), self::rpp(50), 'DATE :', 'RTL', self::rpp(50) );
		$this->SetXY( self::rpp(1800), self::rpp(110) );
		$this->Cell( self::rpp(500), self::rpp(50), 'PAGE :', 'RBL', self::rpp(50) );

		// Affichage de la date + la pagination (Body)
		$this->SetFont( 'Arial', '', 7 );
		$this->SetXY( self::rpp(2050), self::rpp(60) );
		$this->Cell( self::rpp(250), self::rpp(50), $this->data['ord']['date'], 0, self::rpp(50), 'R' );
		$this->SetXY( self::rpp(2050), self::rpp(110) );
		$this->Cell( self::rpp(250), self::rpp(50), $this->PageNo(), 0, self::rpp(50), 'R' );

		// Sur la première page :
		// 		* affichage de l'adresse de livraison et de facturation
		//		* affichage du récap des informations sur le devis
		if( $this->PageNo() == 1 ){
			$this->SetFont( 'Arial', 'B', 8.25 );

			$this->SetXY( self::rpp(80), self::rpp(450) );
			$this->Cell( self::rpp(1100), self::rpp(50), 'ADRESSE DE LIVRAISON', 0, self::rpp(50) );
			$this->SetXY( self::rpp(1240), self::rpp(450) );
			$this->Cell( self::rpp(1100), self::rpp(50), 'ADRESSE DE FACTURATION', 0, self::rpp(50) );

			$this->SetTextColor(255, 0, 0);
			$this->SetFont( 'Arial', 'BI', 8.25 );
			$this->SetXY( self::rpp(495), self::rpp(450) );
			$this->Cell( self::rpp(640), self::rpp(50), '/DELIVERY ADDRESS', 0, self::rpp(50) );
			$this->SetXY( self::rpp(1715), self::rpp(450) );
			$this->Cell( self::rpp(580), self::rpp(50), '/INVOICE ADDRESS', 0, self::rpp(50) );

			$this->SetFont( 'Arial', 'I', 8 );
			$this->SetXY( self::rpp(255), self::rpp(505) );
			$this->Cell( self::rpp(580), self::rpp(50), 'Customer delivered:', 0, self::rpp(50) );
			$this->SetXY( self::rpp(1440), self::rpp(503) );
			$this->Cell( self::rpp(580), self::rpp(50), 'Customer invoice:', 0, self::rpp(50) );

			$this->SetTextColor(0, 0, 0);

			// Affichage de l'adresse de livraison
			$adrdlv = self::tranformUpper( $this->data['addresses']['delivery'] );

			$attn_dlv = $adrdlv['firstname'].' '.$adrdlv['lastname'];
			if( trim($attn_dlv) == '' ){
				$attn_dlv = $adrdlv['society'];
			}

			$this->SetXY( self::rpp(60), self::rpp(500) );
			$this->SetFont( 'Arial', 'B', 8 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  Client livré:'), 'RTL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(40), '', 'RL', 2 );
			$this->SetFont( 'Arial', '', 9 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  Attn : '.$attn_dlv), 'RL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrdlv['society']), 'RL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrdlv['address1']), 'RL', 2 );
			if( trim($adrdlv['address2']) != '' ){
				$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrdlv['address2']), 'RL', 2 );
			}
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrdlv['postal_code'].' '.$adrdlv['city']), 'RL', 2 );
			if( trim($adrdlv['address2']) == '' ){
				$this->Cell( self::rpp(1100), self::rpp(40), '', 'RL', 2 );
			}
			$this->Cell( self::rpp(1100), self::rpp(40), '', 'RBL', 2 );

			// Affichage de l'adresse
			$adrinv = self::tranformUpper( $this->data['addresses']['invoice'] );

			$attn_inv = $adrinv['firstname'].' '.$adrinv['lastname'];
			if( trim($attn_inv) == '' ){
				$attn_inv = $adrinv['society'];
			}

			$this->SetXY( self::rpp(1200), self::rpp(500) );
			$this->SetFont( 'Arial', 'B', 8 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  Client facturé:'), 'RTL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(40), '', 'RL', 2 );
			$this->SetFont( 'Arial', '', 9 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  Attn : '.$attn_inv), 'RL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrinv['society']), 'RL', 2 );
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrinv['address1']), 'RL', 2 );
			if( trim($adrinv['address2']) != '' ){
				$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrinv['address2']), 'RL', 2 );
			}
			$this->Cell( self::rpp(1100), self::rpp(60), self::conv('  '.$adrinv['postal_code'].' '.$adrinv['city']), 'RL', 2 );
			if( trim($adrinv['address2']) == '' ){
				$this->Cell( self::rpp(1100), self::rpp(40), '', 'RL', 2 );
			}
			$this->Cell( self::rpp(1100), self::rpp(40), '', 'RBL', 2 );

			$y = $this->GetY() + self::rpp(100);

			// Affichage du récap de devis
			$this->SetXY( self::rpp(60),  $y );
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(290), self::rpp(40), self::conv('NOS RÉFÉRENCES'), 0, 0 );
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(835), self::rpp(40), self::conv('OUR REFERENCES'), 0, 0 );
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(290), self::rpp(40), self::conv('VOS RÉFÉRENCES'), 0, 0 );
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(835), self::rpp(40), self::conv('YOUR REFERENCES'), 0, 2 );
			$this->ln( self::rpp(5) );

			$this->SetX( self::rpp(60) );
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(100.625), self::rpp(40), self::conv('N°CDE'), 'LT', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(180.625), self::rpp(40), self::conv('/Order #:'), 'T', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), ($num_ord), 'T', 0, 'C');
			$this->Cell( self::rpp(140.625), self::rpp(40), self::conv('Date CDE'), 'T', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(140.625), self::rpp(40), self::conv('/Order Date:'), 'T', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), $this->data['ord']['date'], 'RT', 0, 'C');
			$this->Cell( self::rpp(150.625), self::rpp(40), self::conv('DATE CDE'), 'T', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(130.625), self::rpp(40), self::conv('/Order date:'), 'T', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), $this->data['ord']['date'], 'T', 0, 'C');
			$this->Cell( self::rpp(135.625), self::rpp(40), self::conv('Ref Doss.'), 'T', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(145.625), self::rpp(40), self::conv('/File ref#:'), 'T', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), ($this->data['ord']['ref']), 'RT', 2, 'C');

			$bl_name = '';
			if( is_numeric($this->data['bl']['id']) && $this->data['bl']['id'] > 0 ){
				$bl_name = ord_orders_name( $this->data['bl']['ref'], $this->data['bl']['piece'], $this->data['bl']['id'] );
			}

			$this->SetX( self::rpp(60) );
			$this->Cell( self::rpp(100.625), self::rpp(40), self::conv('N° LIV'), 'LB', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(180.625), self::rpp(40), self::conv('/Delivery #:'), 'B', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), self::conv($bl_name), 'B', 0, 'C');

			$this->Cell( self::rpp(125.625), self::rpp(40), self::conv('Date LIV'), 'B', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(155.625), self::rpp(40), self::conv('/Deliv. Date:'), 'B', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(281.25), self::rpp(40), $this->data['ord']['date_livr'], 'RB', 0, 'C');

			$this->Cell( self::rpp(150.625), self::rpp(40), self::conv('N° CLIENT'), 'B', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(145.625), self::rpp(40), self::conv('/Customer #:'), 'B', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(266.25), self::rpp(40), self::conv($this->data['user']['ref']), 'B', 0, 'C');
			$this->Cell( self::rpp(105.625), self::rpp(40), self::conv('N° TVA'), 'B', 0);
			$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);
			$this->Cell( self::rpp(120.625), self::rpp(40), self::conv('/VAT #:'), 'B', 0);
			$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
			$this->Cell( self::rpp(336.25), self::rpp(40), self::conv($this->data['user']['taxcode']), 'RB', 2, 'C');
		}
	}

	public function Footer(){
		$this->SetFont( 'Arial', '', 8 );
		$this->SetXY( self::rpp(60), self::ENDPAGE + 12.5 );

		$this->Cell( self::rpp(2250), self::rpp(40), self::conv('SAINT BERNARD, Rue de l\'Industrie, Z.I. de la Chapelette, BP20107, 80202 PERONNE, France APE 4638B'), 0, 2, 'C' );
		$this->Cell( self::rpp(2250), self::rpp(40), self::conv('SAS. au capital de 1 500 000 EUR SIRET *********** 00078  RCS AMIENS *********** N° de TVA : FR 53 *********'), 0, 2, 'C' );
	}

	public function body() {
		$this->headTableProducts();

		$y = $this->getY();

		$i = 1;
		foreach( $this->data['ord_products'] as $one_line ){
			if( trim($one_line['name']) == '' ){
				continue;
			}

			$this->SetXY( self::rpp(60), $y );

			$y = $this->lineProduct( ($i++), $one_line, $y );
			if( $y >= self::ENDPAGE ){
				$this->newPage( true );
				$y = $this->GetY();
			}

			$this->bodyBorder( $y );
		}

		if( $y > self::rpp('2025') ){
			$this->newPage( false );
			$y = $this->GetY();
		}

		$this->footTableProducts();
	}

	public function userInfoRow(){

	}
	public function generateTotalPage(){

	}
	public function blocHeader(){

	}
	public function blocFooter(){

	}
	public function getDefaultOptions(){
		global $config;

		return [
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		];
	}

	public function defaultProductTable(){
		// do not use
	}

	private function headTableProducts(){
		// Défini la couleur de fond
		$this->SetFillColor( 237, 235, 224 );

		//	111.30	//	210.56	//	580.9	//	234.59	//	111.30	//	99.26	//	195.52	//	138.37	//	177.47	//	138.37	//	132.35

		$this->SetXY( self::rpp(60), $this->getY() + self::rpp(100) );
		$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);

		$this->Cell( self::rpp(111.30), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(210.56), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(580.9), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(234.59), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(111.30), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(99.26), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(195.52), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(177.47), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), 'TTHT', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(132.35), self::rpp(40), '', 'LTR', 0, 'C', 1 );
		$this->Cell( self::rpp(120), self::rpp(40), '', 'LTR', 2, 'C', 1 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);

		$this->Cell( self::rpp(111.30), self::rpp(40), 'ligne', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(210.56), self::rpp(40), self::conv('Référence'), 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(580.9), self::rpp(40), self::conv('Désignation'), 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(234.59), self::rpp(40), 'Gencod', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(111.30), self::rpp(40), self::conv('Qté'), 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(99.26), self::rpp(40), 'UN', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(195.52), self::rpp(40), 'PU brut', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), 'Rem.', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(177.47), self::rpp(40), 'PU net', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), '+ECOT', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(132.35), self::rpp(40), 'TVA', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(120), self::rpp(40), 'Image', 'LR', 2, 'C', 1 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0);

		$this->Cell( self::rpp(111.30), self::rpp(40), 'line', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(210.56), self::rpp(40), 'Item #', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(580.9), self::rpp(40), 'Item description', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(234.59), self::rpp(40), 'Gencod', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(111.30), self::rpp(40), 'Qty', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(99.26), self::rpp(40), 'UN', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(195.52), self::rpp(40), 'Unit price', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), 'Disc.', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(177.47), self::rpp(40), 'Net price', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), 'Line', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(132.35), self::rpp(40), 'VAT', 'LR', 0, 'C', 1 );
		$this->Cell( self::rpp(120), self::rpp(40), 'Image', 'LR', 2, 'C', 1 );

		$this->SetX( self::rpp(60) );

		$this->Cell( self::rpp(111.30), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(210.56), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(580.9), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(234.59), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(111.30), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(99.26), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(195.52), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(177.47), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(138.37), self::rpp(40), 'Total', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(132.35), self::rpp(40), '', 'LBR', 0, 'C', 1 );
		$this->Cell( self::rpp(120), self::rpp(40), '', 'LBR', 2, 'C', 1 );

		// Réinitialise la couleur de fond, de texte et de police
		$this->SetFillColor( 255, 255, 255 );
		$this->SetFont( 'Arial', '', 7.5 ); $this->SetTextColor(0, 0, 0);
	}

	/** Cette fonction permet d'obtenir le total des taxes depuis un champ avancé pour une ligne de quantité.
	 * 	Ce montant est retour pour une quantité de 1
	 * 	@param array $line Obligatoire, tableau contenant les informations d'une ligne de devis
	 * 	@return float Le montant total des taxes
	 */
	private function getTotalTax( $line ){
		$total_d3e = 0;

		$d3e = fld_object_values_get( [$line['ord_id'], $line['id'], $line['line']], _FLD_PRD_ORD_DEEE );
		if( trim($d3e) != '' ){
			$d3e = json_decode( $d3e, true );
			if( isset($d3e['taxes']['detail']) && is_array($d3e['taxes']['detail']) ){
				foreach( $d3e['taxes']['detail'] as $one_d3e ){
					if( !ria_array_key_exists(['name', 'price'], $one_d3e) ){
						continue;
					}

					$total_d3e += $one_d3e['price'];
				}
			}
		}

		return $total_d3e;
	}

	private function lineProduct( $line_id, $line, $y ){
		global $config;

		$max_new_y = $y;

		// Dimensions de l'image principale des produits inclue dans le ficher Excel
		$thumb = $config['img_sizes']['medium'];

		// Complète le nom du produit avec la quantité du conditionnement
		if( is_numeric($line['col_qte']) && $line['col_qte'] > 1 ){
			$line['name'] .= ' (Par '.round( $line['col_qte'] ).')';
		}

		// Complète le nom du produit si celui-ci fait partie d'un groupe
		if( isset($line['group_id'], $line['group_parent_id']) && !is_null($line['group_id']) && !is_null($line['group_parent_id']) ){
			$line['name'] = '     '.$line['name'];
		}

		// Récupère le code à barres
		$line['barcode'] = prd_products_get_barcode( $line['id'] );

		// Ajout de la TVA au tableau pour les totaux
		if( !array_key_exists($line['tva_rate'], $this->ar_tva) ){
			$this->ar_tva[ $line['tva_rate'] ] = [0, 0];
		}

		$this->ar_tva[ $line['tva_rate'] ][0] += $line['price_ht'] * $line['qte'] * $line['col_qte'];
		$this->ar_tva[ $line['tva_rate'] ][1] += ($line['price_ht'] * $line['qte'] * $line['col_qte'] * $line['tva_rate']) - ($line['price_ht'] * $line['qte'] * $line['col_qte']);

		{ // Récupération des taxes annexes
			$line['d3e'] = '';
			$line['total_d3e'] = $this->getTotalTax( $line ); // Total d3e à l'unité

			$d3e = fld_object_values_get( [$line['ord_id'], $line['id'], $line['line']], _FLD_PRD_ORD_DEEE );
			if( trim($d3e) != '' ){
				$d3e = json_decode( $d3e, true );

				if( isset($d3e['taxes']['detail']) && is_array($d3e['taxes']['detail']) ){
					foreach( $d3e['taxes']['detail'] as $one_d3e ){
						if( !ria_array_key_exists(['name', 'price'], $one_d3e) ){
							continue;
						}

						if( trim($line['d3e']) != '' ){
							$line['d3e'] .= "\n";
						}

						$line['d3e'] .= str_replace( "\n", ' ', $one_d3e['name'] ).' : '.$one_d3e['price'];
					}
				}
			}
		}

		{ // Récupère la remise, le pourcentage de remise ainsi que le prix brut
			$ar_remises = [];
			$price_brut = $line['price_ht'];
			$total_remise = 0;
			$temp = 1;

			// Recherche d'un prix brut
			$db_price_brut = fld_object_values_get( [$line['ord_id'], $line['id'], $line['line']], _FLD_PRD_ORD_PRICE_BRUT );
			if( is_numeric($db_price_brut) && $db_price_brut > 0 ){
				$price_brut = $db_price_brut;
			}

			if( isset($d3e['detail']) && is_array($d3e['detail']) ){
				foreach( $d3e['detail'] as $one_remise ){
					$reduc = $db_price_brut * $one_remise['price'] / 100;

					$ar_remises[] = [
						'pourcent' => $one_remise['price'],
						'reduc' => round( $reduc, 2 )
					];

					$db_price_brut = $db_price_brut - $reduc;

					$temp *= ( 1 - ($one_remise['price'] / 100) );
				}

			}

			// Applique la sur-remise
			$db_pourcent = fld_object_values_get( [$line['ord_id'], $line['id'], $line['line']], _FLD_ORD_LINE_DISCOUNT );
			if( is_numeric($db_pourcent) && $db_pourcent > 0 ){
				$ar_remises[] = [
					'pourcent' => $db_pourcent,
					'reduc' => round( ($db_price_brut * $db_pourcent / 100), 2 )
				];

				$temp *= ( 1 - ($db_pourcent / 100) );
			}


			// Si au moins une remise est active, alors on calcul le total des remises
			if( count($ar_remises) ){
				// $price_for_remise = $line['price_ht'] - $line['total_d3e'];
				// $total_remise = ( 1 - ($price_for_remise / $price_brut) ) * 100;
				// $total_remise = round( $total_remise, 2);
				$total_remise = (1 - $temp) * 100;
			}
		}

		// Mise en forme des données de type numérique
		$line['price_ht'] = number_format( $line['price_ht'], 2, ',', ' ' );
		$line['total_ht'] = number_format( $line['total_ht'], 2, ',', ' ' );
		$tva = number_format( ($line['tva_rate'] - 1) * 100, 2, ',', ' ' );

		// Affichage de la première ligne dans le PDF pour une ligne de commande
		// Numéro de ligne, Référence, Désignation, Code à barre, Quantité, Unité, PU Brut et Image produit
		{
			$this->SetFont( 'Arial', '', 6 ); $this->SetTextColor(0, 0, 0);

			// Numéro de ligne
			$this->Cell( self::rpp(111.30), self::rpp(40), $line_id, 'L', 0 );

			// Référence
			$this->Cell( self::rpp(210.56), self::rpp(40), self::conv($line['ref']), 'L', 0 );

			// Désignation
			$this->MultiCell( self::rpp(585.9), self::rpp(40), self::conv($line['name']), 'L' );

			// Affichage de la d3e
			if( trim($line['d3e']) != '' ){
				$this->SetFont( 'Arial', '', 4 ); $this->SetTextColor(0, 0, 0);
				$this->SetXY( self::rpp(381.86), $y + self::rpp(35) );
				$this->MultiCell( self::rpp(585.9), self::rpp(20), self::conv($line['d3e']), 'L' );

				$this->SetFont( 'Arial', '', 6 ); $this->SetTextColor(0, 0, 0);
				$y += self::rpp(5);
			}

			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }
			$this->SetXY( self::rpp(962.76), $y );

			// Code à barre scanable
			$barcode = self::zolux_barcode( $line['barcode'] );
			if( $barcode !== false ){
				$this->Image( $barcode, $this->GetX(), $y + self::rpp(20) , self::rpp(227), self::rpp(60));
				$this->Cell( self::rpp(234.59), self::rpp(40), '', 'L', 0, 'C' );
			}else{
				$this->Cell( self::rpp(234.59), self::rpp(40), self::conv($line['barcode']), 'L', 0, 'C' );
			}

			// Quantité
			$this->Cell( self::rpp(111.30), self::rpp(40), $line['qte'], 'L', 0, 'R' );

			// Unité de vente
			$this->Cell( self::rpp(99.26), self::rpp(40), 'UN', 'L', 0, 'R' );

			// Prix brut
			$this->Cell( self::rpp(195.52), self::rpp(40), number_format($price_brut, 2, ',', ' '), 'L', 0, 'R' );

			$this->Cell( self::rpp(138.37), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(177.47), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(138.37), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(132.35), self::rpp(40), '', 'L', 0 );

			// Ajout de l'image principal du produit
			$this->Cell( self::rpp(120), self::rpp(40), '', 'LR', 0 );
			if( is_numeric($line['img_id']) && $line['img_id'] > 0 ){
				$file_img = $config['img_dir'].'/'.$thumb['dir'].'/'.$line['img_id'].'.'.$thumb['format'];

				if( file_exists($file_img) ){
					$this->Image( $file_img, $this->GetX() - self::rpp(117), $y + self::rpp(5) , self::rpp(110), self::rpp(110));
				}
			}
		}

		$this->ln();

		if( $y >= self::ENDPAGE ){
			$this->bodyBorder( $max_new_y );
			$this->newPage( true );
			$max_new_y = $this->GetY();
		}

		// Affichage de la seconde ligne dans le PDF pour une ligne de commande
		// Remise en euro du prix brut et remise en pourcentage, Montant HT si DEEE
		if( !count($ar_remises) ){
			$ar_remises[] = [ 'reduc' => 0, 'pourcent' => 0 ];
		}

		$i = count( $ar_remises );
		foreach( $ar_remises as $one_remise ){
			$i--;

			$one_remise['reduc'] = number_format( $one_remise['reduc'], 2, ',', ' ' );
			$one_remise['pourcent'] = number_format( $one_remise['pourcent'], 2, ',', ' ' );

			$this->SetX( self::rpp(60) );
			$this->Cell( self::rpp(111.30), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(210.56), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(580.9), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(234.59), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(111.30), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(99.26), self::rpp(40), '', 'L', 0 );

			// Remise en auro + Remise en pourcentage
			if( parseFloat($one_remise['reduc']) > 0 ){
				$this->Cell( self::rpp(195.52), self::rpp(40), '-'.$one_remise['reduc'], 'L', 0, 'R' );
				$this->Cell( self::rpp(138.37), self::rpp(40), ($one_remise['pourcent'] > 0 ? '-'.$one_remise['pourcent'] : ''), 'L', 0, 'R' );
			}else{
				$this->Cell( self::rpp(195.52), self::rpp(40), '', 'L', 0, 'R' );
				$this->Cell( self::rpp(138.37), self::rpp(40), '', 'L', 0, 'R' );
			}

			$this->Cell( self::rpp(177.47), self::rpp(40), '', 'L', 0 );

			// Montant HT si DEEE
			if( $line['total_d3e'] > 0 && $i == 0 ){
				$this->SetFont( 'Arial', 'B', 6 ); $this->SetTextColor(0, 0, 0);
				$this->Cell( self::rpp(138.37), self::rpp(40), $line['total_ht'], 'L', 0, 'R' );
				$this->SetFont( 'Arial', '', 6 ); $this->SetTextColor(0, 0, 0);
			}else{
				$this->Cell( self::rpp(138.37), self::rpp(40), '', 'L', 0 );
			}

			$this->Cell( self::rpp(132.35), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(120), self::rpp(40), '', 'LR', 2 );

			if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }

			if( $this->GetY() >= self::ENDPAGE ){
				$this->bodyBorder( $max_new_y );
				$this->newPage( true );
				$max_new_y = $this->GetY();
			}
		}

		// Affichage de la troisème ligne dans le PDF pour une ligne de commande
		// Code à barre, Total des remises, PU Net, Montant HT si pas DEEE sinon dont DEEE, TVA
		{
			$this->SetX( self::rpp(60) );
			$this->SetFont( 'Arial', 'B', 6 ); $this->SetTextColor(0, 0, 0);

			$this->Cell( self::rpp(111.30), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(210.56), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(580.9), self::rpp(40), '', 'L', 0 );

			// Code à barre
			if( $barcode !== false ){
				$this->SetFont( 'Arial', '', 6 );
				$this->Cell( self::rpp(234.59), self::rpp(40), self::conv($line['barcode']), 'L', 0, 'C' );
				$this->SetFont( 'Arial', 'B', 6 );
			}else{
				$this->Cell( self::rpp(234.59), self::rpp(40), '', 'L', 0 );
			}

			$this->Cell( self::rpp(111.30), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(99.26), self::rpp(40), '', 'L', 0 );
			$this->Cell( self::rpp(195.52), self::rpp(40), '', 'L', 0 );

			// Total des remise
			$this->Cell( self::rpp(138.37), self::rpp(40), ($total_remise > 0 ? number_format($total_remise, 2, ',', ' ') : ''), 'L', 0, 'R' );

			// PU Net
			$this->Cell( self::rpp(177.47), self::rpp(40), number_format((parseFloat($line['price_ht']) - parseFloat($line['total_d3e'])), 2, ',', ' '), 'L', 0, 'R' );

			// Total HT ou dont DEEE
			if( $line['total_d3e'] > 0 ){
				$this->SetFont( 'Arial', '', 6 );
				$this->Cell( self::rpp(79.185), self::rpp(40), 'dont', 'L', 0, 'R' );
				$this->SetFont( 'Arial', 'B', 6 );
				$this->Cell( self::rpp(59.185), self::rpp(40), number_format(($line['total_d3e'] * $line['qte']), 2, ',', ' '), 'B', 0, 'R' );
			}else{
				$this->Cell( self::rpp(138.37), self::rpp(40), $line['total_ht'], 'L', 0, 'R' );
			}

			$this->Cell( self::rpp(132.35), self::rpp(40), $tva, 'L', 0, 'C' );
			$this->Cell( self::rpp(120), self::rpp(40), '', 'LR', 2, 'C' );
		}

		if( $this->GetY() > $max_new_y ){ $max_new_y = $this->GetY(); }

		return $max_new_y;
	}

	private function bodyBorder( $max_new_y ){
		$this->Line( self::rpp(60), $max_new_y, self::rpp(2309.99), $max_new_y );
	}

	private function footTableProducts(){
		global $config;

		$this->SetXY( self::rpp(100), self::rpp('2025') );

		$this->SetFont( 'Arial', 'B', 7 );
		$this->Cell( self::rpp(2250), self::rpp(40), self::conv('Les produits issus de l\'agriculture biologique sont certifiés par Ecocert : FR-BIO-01 / The products from organic farming are certified by Ecocert: FR-BIO-01'), 0, 2, 'L' );
		$this->ln( self::rpp(10) );

		$this->setX(self::rpp(100));
		$this->SetFont( 'Arial', 'B', 7 );
		$this->Cell( self::rpp(2250), self::rpp(40), self::conv('Numéro de certification FSC COC : IMO-COC-062265 - Seuls les produits identifiés comme tels sur ce document sont certifiés FSC'), 0, 2, 'L' );
		$this->ln( self::rpp(10) );

		// Tableau de la condition de paiement
		$this->SetX( self::rpp(60) );
		$this->Cell( self::rpp(1097.23), self::rpp(30), '', 1, 2 );

		$y = $this->GetY();
		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 7 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(264.26), self::rpp(50), '  COND. PAIEMENT', 'LT', 0 );
		$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0); $this->Cell( self::rpp(289.57), self::rpp(50), 'PAYMENT CONDITIONS:', 'T', 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(543.40), self::rpp(50), '30 jours FDM', 'TR', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 7 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(254.26), self::rpp(50), '  MODE PAIEMENT', 'L', 0 );
		$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0); $this->Cell( self::rpp(209.57), self::rpp(50), 'PAYMENT MODE:', 0, 0 );


		$pay_name = ord_payment_types_get_name($this->data['ord']['pay_id']);
		if( trim($pay_name) != '' ){
			$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(633.40), self::rpp(50), self::conv('Paiement par ').self::conv( $pay_name ), 'R', 2 );
		}else{
			$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(633.40), self::rpp(50), '', 'R', 2 );
		}

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 7 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(259.26), self::rpp(50), '  DATE ECHEANCE', 'L', 0 );
		$this->SetFont( 'Arial', 'I', 6 ); $this->SetTextColor(255, 0, 0); $this->Cell( self::rpp(134.57), self::rpp(50), 'DUE DATE:', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(703.40), self::rpp(50), '', 'R', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', 'I', 6.5 ); $this->Cell( self::rpp(1097.23), self::rpp(30), self::conv('  Escompte pour paiement anticipé% :'), 'LR', 2 );
		$this->Cell( self::rpp(1097.23), self::rpp(50), '', 'LBR', 2 );

		// Tableau des totaux
		$this->SetXY( self::rpp(1231.91), $y );
		$this->SetFont( 'Arial', 'B', 8 );
		$this->SetTextColor(0, 0, 0);

		$this->Cell( self::rpp(183.83), self::rpp(40), 'TVA %', 'LTR', 0, 'C' );
		$this->Cell( self::rpp(279.57), self::rpp(40), 'TOTAL HT', 'LTR', 0, 'R' );
		$this->Cell( self::rpp(335.11), self::rpp(40), 'TOTAL TVA', 'LTR', 0, 'R' );
		$this->Cell( self::rpp(279.57), self::rpp(40), 'NET A PAYER', 'LTR', 2, 'R' );

		$this->SetX( self::rpp(1231.91) );
		$this->SetFont( 'Arial', 'BI', 6 );
		$this->SetTextColor(255, 0, 0);

		$this->Cell( self::rpp(183.83), self::rpp(30), 'VAT %', 'LBR', 0, 'C' );
		$this->Cell( self::rpp(279.57), self::rpp(30), 'Amount net a tax', 'LBR', 0, 'R' );
		$this->Cell( self::rpp(335.11), self::rpp(30), 'Total tax', 'LBR', 0, 'R' );
		$this->Cell( self::rpp(279.57), self::rpp(30), 'Payment with tax', 'LBR', 2, 'R' );

		// Parcours les taux de TVA appliqués sur le devis
		$total_tva = 0;
		foreach( $this->ar_tva as $tax=>$one_tva ){
			$this->SetX( self::rpp(1231.91) );
			$this->SetFont( 'Arial', '', 8 );
			$this->SetTextColor(0, 0, 0);

			$this->Cell( self::rpp(183.83), self::rpp(53.33), number_format(($tax - 1) * 100, 2, ',', ' '), 'LBR', 0, 'C' );
			$this->Cell( self::rpp(279.57), self::rpp(53.33), number_format($one_tva[0], 2, ',', ' '), 'LBR', 0, 'R' );
			$this->Cell( self::rpp(335.11), self::rpp(53.33), number_format($one_tva[1], 2, ',', ' '), 'LBR', 0, 'R' );
			$this->Cell( self::rpp(279.57), self::rpp(53.33), '', 'LBR', 2, 'R' );

			$total_tva += $one_tva[1];
		}

		$this->SetX( self::rpp(1231.91) );

		$this->Cell( self::rpp(183.83), self::rpp(53.33), '', 'LBR', 0, 'C' );
		$this->Cell( self::rpp(279.57), self::rpp(53.33), number_format($this->data['ord']['total_ht'], 2, ',', ' '), 'LBR', 0, 'R' );
		$this->Cell( self::rpp(335.11), self::rpp(53.33), number_format( ($total_tva), 2, ',', ' ' ), 'LBR', 0, 'R' );
		$this->SetFont( 'Arial', 'B', 8 ); $this->Cell( self::rpp(279.57), self::rpp(53.33), number_format(($this->data['ord']['total_ht'] + $total_tva), 2, ',', ' '), 'LBR', 2, 'R' );

		$this->SetX( self::rpp(1231.91) );
		$this->SetFont( 'Arial', '', 8 );

		$this->Cell( self::rpp(183.83), self::rpp(53.33), '', 'LBR', 0, 'C' );
		$this->Cell( self::rpp(279.57), self::rpp(53.33), '', 'LBR', 0, 'R' );
		$this->SetFont( 'Arial', 'B', 8 ); $this->Cell( self::rpp(147.555), self::rpp(53.33), 'DEVISE', 'LB', 0, 'R' );
		$this->SetFont( 'Arial', 'BI', 7 ); $this->SetTextColor(255, 0, 0); $this->Cell( self::rpp(187.555), self::rpp(53.33), '/CURRENCY:', 'BR', 0, 'R' );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor(0, 0, 0); $this->Cell( self::rpp(279.57), self::rpp(53.33), 'EUR', 'LBR', 2, 'R' );

		// Modalité de paiement
		//	849.42	//	593,08	//	812,21
		$this->ln( self::rpp(100) );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 8 );

		$this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(624.71), self::rpp(40), self::conv('Veuillez libeller votre règlement à l\'ordre de'), 'LT', 0 );
		$this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(224.71), self::rpp(40), self::conv('/ please'), 'T', 0 );
		$this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(593.08), self::rpp(40), 'Service commercial', 'LT', 0 );
		$this->Cell( self::rpp(330), self::rpp(40), 'Assistant commercial :', 'LT', 0 );
		// Nom de l'ADV
		$this->SetFont( 'Arial', 'B', 8 ); $this->Cell( self::rpp(482.21), self::rpp(40), '', 'TR', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 8 );

		$this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(380), self::rpp(40), 'address your payment to : ', 'L', 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(469.42), self::rpp(40), 'SAINT BERNARD,', 0, 0 );
		$this->Cell( self::rpp(593.08), self::rpp(40), 'SAINT BERNARD', 'L', 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->Cell( self::rpp(180), self::rpp(40), 'Commercial ', 'L', 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(225), self::rpp(40), '/Sales person : ', 0, 0 );
		// Nom du commerciale
		$seller_name = $seller_phone = $seller_email = '';
		if( $this->data['seller'] ){
			$seller_name = $this->data['seller']['adr_firstname'].' '.$this->data['seller']['adr_lastname'];
			$seller_phone = trim($this->data['seller']['mobile']) != '' ? $this->data['seller']['mobile'] : $this->data['seller']['phone'];
			$seller_email = $this->data['seller']['email'];
		}
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(407.21), self::rpp(40), self::conv($seller_name), 'R', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', 'B', 8 );
		$this->SetTextColor( 0, 0, 0 );

		$this->Cell( self::rpp(445), self::rpp(40), self::conv('Service Comptabilité Clients'), 'L', 0 );
		$this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(404.42), self::rpp(40), '/ Accounting Dpt :', 0, 0 );
		$this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(593.08), self::rpp(40), '80202 PERONNE', 'L', 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->Cell( self::rpp(90), self::rpp(40), self::conv('N°Tél'), 'L', 0 );
		$this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(145), self::rpp(40), self::conv('Phone # : '), 0, 0 );
		// Numéro de téléphone du commerciale
		$this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(577.21), self::rpp(40), $seller_phone, 'R', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', 'B', 8 );
		$this->SetTextColor( 0, 0, 0 );

		$this->Cell( self::rpp(849.42), self::rpp(40), '141, Cours Paul Doumer, 17100 SAINTES France', 'L', 0 );
		$this->Cell( self::rpp(593.08), self::rpp(40), 'France', 'L', 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->Cell( self::rpp(75), self::rpp(40), 'Mail', 'L', 0 );
		$this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(75), self::rpp(40), 'Mail:', 0, 0 );
		// Adresse mail du commerciale
		$this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(662.21), self::rpp(40), $seller_email, 'R', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', 'B', 8 );
		$this->SetTextColor( 0, 0, 0 );

		$this->Cell( self::rpp(849.42), self::rpp(40), 'tel : 05.46.74.55.88 fax : 05.46.74.55.89', 'L', 0 );
		$this->Cell( self::rpp(593.08), self::rpp(40), '<EMAIL>', 'L', 0 );
		$this->Cell( self::rpp(812.21), self::rpp(40), '', 'LR', 2 );

		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 8 );

		$this->Cell( self::rpp(849.42), self::rpp(40), 'Contact mail : <EMAIL>', 'LB', 0 );
		$this->Cell( self::rpp(593.08), self::rpp(40), '', 'LB', 0 );
		$this->Cell( self::rpp(812.21), self::rpp(40), '', 'LBR', 2 );

		$this->ln( self::rpp(30) );

		// CGV
		$this->SetX( self::rpp(60) );
		$this->Cell( self::rpp(930), self::rpp(40), 'A DETACHER ET A JOINDRE A VOTRE REGLEMENT,MERCI', 0, 2);

		$this->SetFont( 'Arial', 'I', 8 );
		$this->SetTextColor( 255, 0, 0 );
		$this->Cell( self::rpp(930), self::rpp(40), 'PLEASE DETACH AND JOIN TO YOUR PAYMENT', 0, 2);

		$this->ln( self::rpp(20) );
		$this->SetX( self::rpp(60) );
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 );

		$this->SetX( self::rpp(30) );
		$this->Image( $config['site_dir'].'/dist/pdf-imgs/border-top.jpg', $this->GetX(), $this->GetY(), self::rpp(924), self::rpp(3));

		$this->SetXY( self::rpp(984), $this->GetY() + self::rpp(10) );
		$this->Image( $config['site_dir'].'/dist/pdf-imgs/border-left.jpg', $this->GetX(), $this->GetY(), self::rpp(3), self::rpp(400));

		$inv_name = '';
		if( is_numeric($this->data['inv']['id']) && $this->data['inv']['id'] ){
			$inv_name = ord_orders_get_name( $this->data['inv']['ref'], $this->data['inv']['piece'], $this->data['inv']['id'] );
		}

		$this->SetX( self::rpp(60) );
		$this->Cell( self::rpp(201), self::rpp(60), self::conv('N° de facture'), 0, 0 );
		$this->SetFont( 'Arial', 'I', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(261), self::rpp(60), 'Invoice #', 0, 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), self::conv($inv_name), 0, 2 );

		$this->SetX( self::rpp(60) );

		$adrinv = self::tranformUpper( $this->data['addresses']['invoice'] );

		$attn_inv = $adrinv['firstname'].' '.$adrinv['lastname'];
		if( trim($attn_inv) == '' ){
			$attn_inv = $adrinv['society'];
		}

		$this->Cell( self::rpp(120), self::rpp(60), self::conv('Société'), 0, 0 );
		$this->SetFont( 'Arial', 'I', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(342), self::rpp(60), 'Company :', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), 'Attn :'.self::conv($attn_inv), 0, 2 );

		$this->SetX( self::rpp(60) );

		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(282), self::rpp(60), self::conv('Code client facturé'), 0, 0 );
		$this->SetFont( 'Arial', 'I', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(180), self::rpp(60), 'Cust#:', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), $this->data['user']['ref'], 0, 2 );

		$this->SetX( self::rpp(60) );

		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), 'Code client payeur', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), $this->data['user']['ref'], 0, 2 );

		$this->SetX( self::rpp(60) );

		$this->SetFont( 'Arial', 'I', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), 'Paying customer code:', 0, 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), '', 0, 2 ); // empty all time

		$this->SetX( self::rpp(60) );

		$this->Cell( self::rpp(236), self::rpp(60), self::conv('Montant à payer'), 0, 0 );
		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(226), self::rpp(60), '/Amount due :', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), number_format($this->data['ord']['total_ttc'], 2, ',', ' '), 0, 2 );

		$this->SetX( self::rpp(60) );

		$this->SetFont( 'Arial', '', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(111), self::rpp(60), 'Devise', 0, 0 );
		$this->SetFont( 'Arial', 'I', 8 ); $this->SetTextColor( 255, 0, 0 ); $this->Cell( self::rpp(351), self::rpp(60), 'Currency :', 0, 0 );
		$this->SetFont( 'Arial', 'B', 8 ); $this->SetTextColor( 0, 0, 0 ); $this->Cell( self::rpp(462), self::rpp(60), 'EUR', 0, 2 );


		$this->SetY( $this->GetY() - self::rpp(60 * 7 + 40 * 2 + 20) );
		$this->SetFont( 'Arial', '', 8 );
		$this->SetTextColor( 0, 0, 0 );
		$this->SetX( self::rpp(1000) );
		$this->Cell( self::rpp(1320), self::rpp(40), self::conv('Veuillez prendre connaissance des conditions Générales de Vente à l\'adresse suivante : '), 0, 2);
		$this->Cell( self::rpp(1320), self::rpp(40), 'http://cgv.mysaintbernard.com', 0, 2);

		$this->ln( self::rpp(50) );
		$this->SetX( self::rpp(1000) );
		$this->SetFont( 'Arial', 'I', 7.5 );

		$this->MultiCell( self::rpp(1320), self::rpp(40), self::conv('Tout retard de paiement entraînera l\'application, de plein droit, de pénalités de retard conformément à l\'article L 441-6 du code de commerce, calculées sur le montant de la somme restant due, au taux d\'intérêt appliqué par la Banque centrale européenne à son opération de refinancement la plus récente majoré de 10 points de pourcentage.'), 0, 2);

		$this->ln( self::rpp(30) );
		$this->SetX( self::rpp(1000) );

		$this->MultiCell( self::rpp(1320), self::rpp(40), self::conv('Pour tout professionnel, en sus des indemnités de retard, toute somme, y compris l\'acompte, non payée à sa date d\'exigibilité produira de plein droit le paiement d\'une indemnité forfaitaire de 40 euros due au titre des frais de recouvrement (Art. 441-6, I al. 12 du code de commerce et D. 441-5 ibidem'), 0, 2);

		// address your payment to : ZOLUX SAS,
	}

	private function newPage( $with_prds ){
		$this->addPage();

		if( $with_prds ){
			$this->headTableProducts();
		}
	}

	private static function rpp( $pixels ){
		if (!is_numeric($pixels) || $pixels<0) {
			return 0;
		}

		return round( $pixels * 0.0875, 3 );
	}

	private static function conv($str) {
		return iconv('utf8', 'windows-1252', $str);
	}

	private static function tranformUpper( $data ){
		if( is_array($data) ){
			foreach( $data as $key => $value ){
				$data[ $key ] = strtoupper2( $value );
			}
		}

		return $data;
	}

	private static function zolux_barcode( $barcode ){
		global $config;

		if( trim($barcode) == '' ){
			return false;
		}

		$file_barcode = $config['img_dir']. '/barcodes/'.htmlentities($barcode,ENT_QUOTES,"ISO8859-1").'.png';

		// Création du fichier image code barre si celui-ci n'existe pas
		if( !file_exists($file_barcode) ){
			$bc = new \Barcode();
			$bc->setCode((string) $barcode);
			$bc->setType('EAN');
			$bc->setSize(30, 147, 10);
			$bc->setText('');
			$bc->hideCodeType();
			$bc->setColors('#000000', '#FFFFFF');
			$bc->setFiletype('PNG');

			$bc->writeBarcodeFile( $file_barcode );
		}

		return file_exists( $file_barcode ) ? $file_barcode : false;
	}
}
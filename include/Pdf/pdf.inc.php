<?php

// \cond onlyria
/** Génération du PDF d'une facture
 * \param $ord_id Identifiant de la commande
 * \param $inv_id Identifiant de la facture
 * \param $check_status Facultatif, vérifier le statut de la commande ? Par défaut : Oui
 * \param $required_statuses Facultatif, status de commande autorisés. Par défaut : _STATE_INVOICE, _STATE_SUPP_PARTIEL_INV, _STATE_BL_EXP
 * \param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * \param $filename Facultatif, nom du fichier généré. Par défaut : facture-<$inv_id>.pdf
 * \param $output Facultatif, afficher du résultat dans le buffer ? Par défaut : Non. @see Pdf\InvoicePdf
 * \param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 *                 + subject:           Sujet dans les métadonnées du PDF
 *                 + name:              Nom dans les métadonnées du PDF
 *                 + font_size:         Taille de la police dans la liste des produits
 *                 + logo:              Identifiant d'une image qui sera placée dans l'entête du fichier
 *                 + logo_size_x:       Largeur en pixels du logo
 *                 + logo_size_y:       Hauteur en pixels du logo
 *                 + prd_img:           Afficher les images des produits ?
 *
 * \return void
 *
 * \throws \Exception Commande non trouvée
 * \throws \Exception Le statut de la commande ne convient pas
 * \throws \Exception Facture non trouvée
 */
function generate_invoice( $ord_id, $inv_id, $check_status = true, array $required_statuses = null, array &$ord = null, $filename = false, $output = false, array $options = null ){
    require_once('Export/invoice.inc.php');

    if (is_null($ord)) {
        $r_ord = ord_orders_get_simple(array('id' => $ord_id));
        if ($r_ord && ria_mysql_num_rows($r_ord)) {
            $ord = ria_mysql_fetch_assoc($r_ord);
        }
    }

    $filename = !is_null($ord) && $filename === false ? 'facture-' . (trim($ord['ref']) != "" ? $ord['ref'] : $ord['id']) . '.pdf' : $filename;

    if (is_null($options)) {
        $options = array();
    }

    $options['check_status'] = $check_status;
    $options['required_statuses'] = is_null($required_statuses) ? array(_STATE_INVOICE, _STATE_SUPP_PARTIEL_INV, _STATE_BL_EXP) : $required_statuses;

    if (!isset($options['prd_code_ean'])) {
        $options['prd_code_ean'] = true;
    }

    if (!isset($options['filename']) && $filename) {
        $options['filename'] = $filename;
    }

    if (!isset($options['output']) && $output) {
        $options['output'] = $output;
    }

    return export_invoice($ord_id, $inv_id, 'pdf', $options, $ord);
}
// \endcond

// \cond onlyria
/** Génération du PDF d'un devis
 * \param $ord_id Identifiant de la commande
 * \param $check_status Facultatif, vérifier le statut de la commande ? Par défaut : Oui
 * \param $required_statuses Facultatif, status de commande autorisés. Par défaut : _STATE_DEVIS
 * \param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * \param $filename Facultatif, nom du fichier généré. Par défaut : facture-<$inv_id>.pdf
 * \param $output Facultatif, afficher du résultat dans le buffer ? Par défaut : Non. @see Pdf\InvoicePdf
 * \param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 *                 + subject:           Sujet dans les métadonnées du PDF
 *                 + name:              Nom dans les métadonnées du PDF
 *                 + font_size:         Taille de la police dans la liste des produits
 *                 + logo:              Identifiant d'une image qui sera placée dans l'entête du fichier
 *                 + logo_disposition:  0 : En haut à droite, 1 : En haut à gauche
 *                 + logo_size_x:       Largeur en pixels du logo
 *                 + logo_size_y:       Hauteur en pixels du logo
 *                 + prd_img:           Afficher les images des produits ?
 *                 + owner:             Surcharge les informations du propriétaires (utilisé pour les devis)
 *
 * \return void
 *
 * \throws \Exception Commande non trouvée
 * \throws \Exception Le statut de la commande ne convient pas
 */
function generate_devis( $ord_id, $check_status = true, array $required_statuses = null, array & $ord = null, $filename = false, $output = false, array $options = null ){
    require_once('Export/order.inc.php');
    global $config;

    if (is_null($ord)) {
        $r_ord = ord_orders_get_simple(array('id' => $ord_id));
        if ($r_ord && ria_mysql_num_rows($r_ord)) {
            $ord = ria_mysql_fetch_assoc($r_ord);
        }
    }

    $filename = !is_null($ord) && $filename === false ? 'devis-' . (trim($ord['ref']) != "" ? $ord['ref'] : $ord['id']) . '.pdf' : $filename;

    if (is_null($options)) {
        $options = array();
    }

    $options['check_status'] = $check_status;
    $options['required_statuses'] = is_null($required_statuses) ? array(_STATE_DEVIS) : $required_statuses;

    if (!isset($options['prd_code_ean'])) {
        $options['prd_code_ean'] = true;
    }

    if (!isset($options['filename']) && $filename) {
        $options['filename'] = $filename;
    }

    if (!isset($options['output']) && $output) {
        $options['output'] = $output;
    }

    return export_order($ord_id, 'pdf', $options, $ord);
}
// \endcond

// \cond onlyria
/** Génération du PDF d'un bon de livraison
 * @param $bl_id Identifiant du bon de livraison
 * @param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * @param $filename Facultatif, nom du fichier généré. Par défaut : bon-de-livraison-<$inv_id>.pdf
 * @param $output Facultatif, afficher du résultat dans le buffer ? Par défaut : Non. @see Pdf\InvoicePdf
 * @param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 *                 + subject:           Sujet dans les métadonnées du PDF
 *                 + name:              Nom dans les métadonnées du PDF
 *                 + font_size:         Taille de la police dans la liste des produits
 *                 + logo:              Identifiant d'une image qui sera placée dans l'entête du fichier
 *                 + logo_disposition:  0 : En haut à droite, 1 : En haut à gauche
 *                 + logo_size_x:       Largeur en pixels du logo
 *                 + logo_size_y:       Hauteur en pixels du logo
 *                 + prd_img:           Afficher les images des produits ?
 *                 + owner:             Surcharge les informations du propriétaires (utilisé pour les devis)
 *
 * @return void
 *
 * @throws \Exception Commande non trouvée
 * @throws \Exception Le statut de la commande ne convient pas
 */
function generate_bl( $bl_id, array &$bl=null, $filename=false, $output=false, array $options=null ){
    require_once('Export/bl.inc.php');
    global $config;

    if (is_null($bl)) {
        $r_bl = ord_bl_get( $bl_id);
        if ($r_bl && ria_mysql_num_rows($r_bl)) {
            $bl = ria_mysql_fetch_assoc($r_bl);
        }
    }

    $filename = !is_null($bl) && $filename === false ? 'bon-de-livraison-' . (trim($bl['ref']) != "" ? $bl['ref'] : $bl['id']) . '.pdf' : $filename;

    if (is_null($options)) {
        $options = array();
    }

    if (!isset($options['prd_code_ean'])) {
        $options['prd_code_ean'] = true;
    }

    if (!isset($options['filename']) && $filename) {
        $options['filename'] = $filename;
    }

    if (!isset($options['output']) && $output) {
        $options['output'] = $output;
    }

    return export_bl($bl_id, 'pdf', $options, $bl);
}
// \endcond

// \cond onlyria
/** Génération du PDF d'un bon de livraison
 * @param $bl_id Identifiant du bon de livraison
 * @param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * @param $filename Facultatif, nom du fichier généré. Par défaut : bon-de-livraison-<$inv_id>.pdf
 * @param $output Facultatif, afficher du résultat dans le buffer ? Par défaut : Non. @see Pdf\InvoicePdf
 * @param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 *                 + subject:           Sujet dans les métadonnées du PDF
 *                 + name:              Nom dans les métadonnées du PDF
 *                 + font_size:         Taille de la police dans la liste des produits
 *                 + logo:              Identifiant d'une image qui sera placée dans l'entête du fichier
 *                 + logo_disposition:  0 : En haut à droite, 1 : En haut à gauche
 *                 + logo_size_x:       Largeur en pixels du logo
 *                 + logo_size_y:       Hauteur en pixels du logo
 *                 + prd_img:           Afficher les images des produits ?
 *                 + owner:             Surcharge les informations du propriétaires (utilisé pour les devis)
 *
 * @return void
 *
 * @throws \Exception Commande non trouvée
 * @throws \Exception Le statut de la commande ne convient pas
 */
function generate_return( $return_id, array &$return=null, $filename=false, $output=false, array $options=null ){
    require_once('Export/return.inc.php');
    global $config;

    if (is_null($return)) {
        $r_return = ord_returns_get( $return_id );
        if ($r_return && ria_mysql_num_rows($r_return)) {
            $return = ria_mysql_fetch_assoc( $r_return );
        }
    }

    // print '<pre>';
    // print_r($return);
    // print '</pre>';

    $filename = !is_null($return) && $filename === false ? 'bon-de-livraison-'.$return['id'].'.pdf' : $filename;

    if (is_null($options)) {
        $options = array();
    }

    if (!isset($options['prd_code_ean'])) {
        $options['prd_code_ean'] = true;
    }

    if (!isset($options['filename']) && $filename) {
        $options['filename'] = $filename;
    }

    if (!isset($options['output']) && $output) {
        $options['output'] = $output;
    }

    return export_return( $return_id, 'pdf', $options, $return );
}
// \endcond

// \cond onlyria
/** Envoi par mail du PDF d'une facture
 * \param $ord_id Identifiant de la commande
 * \param $inv_id Identifiant de la facture
 * \param $check_status Facultatif, vérifier le statut de la commande ? Par défaut : Oui
 * \param $required_statuses Facultatif, status de commande autorisés. Par défaut : _STATE_INVOICE, _STATE_SUPP_PARTIEL_INV, _STATE_BL_EXP
 * \param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * \param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 * \param $email_code Facultatif, code de la configuration email à utiliser. Par défaut : pdf-invoice
 * \param $from Facultatif, surcharge de l'expéditeur de l'email
 * \param $to Facultatif, surcharge du destinataire de l'email
 * \param $cc Facultatif, surcharge des destinataires en copie de l'email
 * \param $bcc Facultatif, surcharge des destinataires en copie cachée de l'email
 * \param $subject Facultatif, sujet de l'email
 * \param $body Facultatif, Corps de l'email
 * \param $header Facultatif, en-tête de l'email
 * \param $footer Facultatif, pieds-de-page de l'email
 * \param $filename Facultatif, nom du fichier généré. Par défaut : facture-<$inv_id>.pdf
 * @param $reply_to Facultatif, surcharge du champ "Repondre à" de l'email
 *
 * \return void
 *
 * \throws \Exception L'adresse mail de l'expéditeur est obligatoire.
 * \throws \Exception L'adresse mail du destinataire est obligatoire.
 * \throws \Exception Commande non trouvée
 * \throws \Exception Erreur lors de la génération de la facture
 */
function send_invoice( $ord_id, $inv_id, $check_status = true, array $required_statuses = null, array & $ord = null, array $options = null, $email_code = false, $from = false, $to = false, $cc = false, $bcc = false, $subject = false, $body = false, $header = false, $footer = false, $filename = false, $reply_to = false ){
    global $config;

    $email_code = $email_code ? $email_code : 'pdf-invoice';

    // Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
    $rcfg = false;
    if( false === $from ){
        if( is_string($email_code) && trim($email_code) ){
            $rcfg = cfg_emails_get($email_code);
        }

        if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
            throw new Exception('L\'adresse mail de l\'expéditeur est obligatoire.', 1300);
        }
    }

    if( $rcfg ){
        $cfg = ria_mysql_fetch_array($rcfg);
    }

    $from = $from === false && $rcfg ? trim($cfg['from']) : trim($from);
    if( $from == '' ){
        throw new Exception('L\'adresse mail de l\'expéditeur est obligatoire.', 1310);
    }

    // fallback sur l'email du client
    if( false === $to ){
        if( is_null($ord) ){
            $r_ord = ord_orders_get_simple(array('id' => $ord_id));
            if( !$r_ord || !ria_mysql_num_rows($r_ord) ){
                throw new Exception('Commande non trouvée', 1000);
            }
            $ord = ria_mysql_fetch_assoc($r_ord);
        }

        $to = gu_users_get_email($ord['usr_id']);
    }

    $to = $to === false && $rcfg ? trim($cfg['to']) : trim($to);
    if( $to == '' ){
        throw new Exception('L\'adresse mail du destinataire est obligatoire.', 1400);
    }

    // génération du PDF
    $filename = $filename === false ? 'facture.pdf' : $filename;
    $tmpname = tempnam(sys_get_temp_dir(), $filename);
    try{
        generate_invoice($ord_id, $inv_id, $check_status, $required_statuses, $ord, $tmpname, 'F', $options);
    }catch(Exception $e){
        throw new Exception('Erreur lors de la génération de la facture', 1200, $e);
    }

    if( !file_exists($tmpname) ){
        throw new Exception('Erreur lors de la génération de la facture', 1210);
    }

    // paramétrage et envoi de l'email
    $mail = new Email();
    $mail->setFrom($from);
    $mail->addTo($to);
    $mail->addAttachment($tmpname, $filename);

    if( $cc === false && $rcfg && trim($cfg['cc']) != '' ){
        $mail->addCc($cfg['cc']);
    }elseif( trim($cc) != '' ){
        $mail->addCc($cc);
    }

    if( $bcc === false && $rcfg && trim($cfg['bcc']) != '' ){
        $mail->addBcc($cfg['bcc']);
    }elseif( trim($bcc) != '' ){
        $mail->addBcc($bcc);
    }

    if( $reply_to === false && $rcfg && trim($cfg['reply-to']) != '' ){
        $mail->setReplyTo($cfg['reply-to']);
    }elseif( trim($reply_to) != '' ){
        $mail->setReplyTo($reply_to);
    }

    if( $subject ){
        $mail->setSubject($subject);
    }else{
        $mail->setSubject(_('Facture') . ' ' . $config['site_name']);
    }

    if( $header ){
        $mail->addHtml($header);
    }else{
        $mail->addHtml($config['email_html_header']);
    }

    if( $body ){
        $mail->addHtml($body);
    }else{
        $mail->addHtml(<<<TPL_SEND_INVOICE_MAIL
            <p>Bonjour,</p>
            <p>Vous trouverez votre facture ${config['site_name']} en pièce jointe.</p>
TPL_SEND_INVOICE_MAIL
        );
    }

    if( $footer ){
        $mail->addHtml($footer);
    }else{
        $mail->addHtml($config['email_html_footer']);
    }

    if( !$mail->send() ){
        throw new Exception('Erreur lors de l\'envoi de la facture');
    }
}
// \endcond

// \cond onlyria
/** Envoi par mail du PDF d'un devis
 * \param $ord_id Identifiant de la commande
 * \param $check_status Facultatif, vérifier le statut de la commande ? Par défaut : Oui
 * \param $required_statuses Facultatif, status de commande autorisés. Par défaut : _STATE_DEVIS
 * \param & $ord Facultatif, tableau de données de la commande. Utilisé pour optimisation.
 * \param $options Facultatif, tableau d'options @see Pdf\InvoicePdf
 * \param $email_code Facultatif, code de la configuration email à utiliser. Par défault : pdf-devis
 * \param $from Facultatif, surcharge de l'expéditeur de l'email
 * \param $to Facultatif, surcharge du destinataire de l'email
 * \param $cc Facultatif, surcharge des destinataires en copie de l'email
 * \param $bcc Facultatif, surcharge des destinataires en copie cachée de l'email
 * \param $subject Facultatif, sujet de l'email
 * \param $body Facultatif, Corps de l'email
 * \param $header Facultatif, en-tête de l'email
 * \param $footer Facultatif, pieds-de-page de l'email
 * \param $filename Facultatif, nom du fichier généré. Par défaut : facture-<$inv_id>.pdf
 * @param $reply_to Facultatif, surcharge du champ "Repondre à" de l'email
 *
 * \return void
 *
 * \throws \Exception L'adresse mail de l'expéditeur est obligatoire.
 * \throws \Exception L'adresse mail du destinataire est obligatoire.
 * \throws \Exception Commande non trouvée
 * \throws \Exception Erreur lors de la génération de la facture
 */
function send_devis( $ord_id, $check_status = true, array $required_statuses = null, array & $ord = null, array $options = null, $email_code = false, $from = false, $to = false, $cc = false, $bcc = false, $subject = false, $body = false, $header = false, $footer = false, $filename = false, $reply_to = false  ){
    global $config;

    $email_code = $email_code ? $email_code : 'pdf-devis';

    // Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
    $rcfg = false;
    if( false === $from ){
        if( is_string($email_code) && trim($email_code) ){
            $rcfg = cfg_emails_get($email_code);
        }

        if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
            throw new Exception('L\'adresse mail de l\'expéditeur est obligatoire.', 1300);
        }
    }

    if( $rcfg ){
        $cfg = ria_mysql_fetch_array($rcfg);
    }

    $from = $from === false && $rcfg ? trim($cfg['from']) : trim($from);
    if( $from == '' ){
        throw new Exception('L\'adresse mail de l\'expéditeur est obligatoire.', 1310);
    }

    // fallback sur l'email du client
    if( false === $to ){
        if( is_null($ord) ){
            $r_ord = ord_orders_get_simple(array('id' => $ord_id));
            if( !$r_ord || !ria_mysql_num_rows($r_ord) ){
                throw new Exception('Commande non trouvée', 1000);
            }
            $ord = ria_mysql_fetch_assoc($r_ord);
        }

        $to = gu_users_get_email($ord['usr_id']);
    }

    $to = $to === false && $rcfg ? trim($cfg['to']) : trim($to);
    if( $to == '' ){
        throw new Exception('L\'adresse mail du destinataire est obligatoire.', 1400);
    }

    // génération du PDF
    $filename = $filename === false ? 'devis.pdf' : $filename;
    $tmpname = tempnam(sys_get_temp_dir(), $filename);
    try{
        generate_devis($ord_id, $check_status, $required_statuses, $ord, $tmpname, 'F', $options);
    }catch(Exception $e){
        throw new Exception('Erreur lors de la génération du devis', 1200, $e);
    }

    if( !file_exists($tmpname) ){
        throw new Exception('Erreur lors de la génération du devis', 1210);
    }

    // paramétrage et envoi de l'email
    $mail = new Email();
    $mail->setFrom($from);
    $mail->addTo($to);
    $mail->addAttachment($tmpname, $filename);

    if( $cc === false && $rcfg && trim($cfg['cc']) != '' ){
        $mail->addCc($cfg['cc']);
    }elseif( trim($cc) != '' ){
        $mail->addCc($cc);
    }

    if( $bcc === false && $rcfg && trim($cfg['bcc']) != '' ){
        $mail->addBcc($cfg['bcc']);
    }elseif( trim($bcc) != '' ){
        $mail->addBcc($bcc);
    }

    if( $reply_to === false && $rcfg && trim($cfg['reply-to']) != '' ){
        $mail->setReplyTo($cfg['reply-to']);
    }elseif( trim($reply_to) != '' ){
        $mail->setReplyTo($reply_to);
    }

    if( $subject ){
        $mail->setSubject($subject);
    }else{
        $mail->setSubject(_('Devis') . ' ' . $config['site_name']);
    }

    if( $header ){
        $mail->addHtml($header);
    }else{
        $mail->addHtml($config['email_html_header']);
    }

    if( $body ){
        $mail->addHtml($body);
    }else{
        $mail->addHtml(<<<TPL_SEND_DEVIS_MAIL
            <p>Bonjour,</p>
            <p>Vous trouverez votre devis ${config['site_name']} en pièce jointe.</p>
TPL_SEND_DEVIS_MAIL
        );
    }

    if( $footer ){
        $mail->addHtml($footer);
    }else{
        $mail->addHtml($config['email_html_footer']);
    }

    if( !$mail->send() ){
        throw new Exception('Erreur lors de l\'envoi du devis');
    }
}
// \endcond

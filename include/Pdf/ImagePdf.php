<?php
namespace Pdf;
/** \devgroup ImagePdf ImagePdf
 * \ingroup pdf
 */
class ImagePdf
{
	/**
	 * Instance Fpdf
	 *
	 * @var Fpdf
	 */
	protected $pdf;
	/**
	 * Chemin vers l'image
	 *
	 * @var string
	 */
	protected $image_path;
	/**
	 * Coordonné X
	 *
	 * @var integer|float
	 */
	protected $x;
	/**
	 * Coordonnée Y
	 *
	 * @var integer|float
	 */
	protected $y;
	/**
	 * Largeur de l'image
	 *
	 * @var integer|float
	 */
	protected $width;
	/**
	 * Hauteur de l'image
	 *
	 * @var integer|float
	 */
	protected $height;
	/**
	 * Constructeur
	 *
	 * @param \Fpdf $pdf Instance fpdf
	 * @param string $image_path Chemin de l'image
	 * @param integer $x Coordonnée X
	 * @param integer $y Coordonnée Y
	 * @param integer $width Largeur de l'image
	 * @param integer $height Hauteur de l'image
	 */
	public function __construct(\Fpdf $pdf, $image_path, $x=0, $y=0, $width=0, $height=0)
	{
		$this->pdf = $pdf;
		$this->image_path = $image_path;
		$this->x = $x;
		$this->y = $y;
		$this->width = $width;
		$this->height = $height;
	}
	/**
	 * Initialise la coordonnée X
	 *
	 * @param integer|float $x
	 * @return void
	 */
	public function setX($x)
	{
		$this->x = $x;
	}
	/**
	 * Initialise la coordonnée Y
	 *
	 * @param integer|float $y
	 * @return void
	 */
	public function setY($y)
	{
		$this->y = $y;
	}
	/**
	 * Initialise la largeur
	 *
	 * @param integer|float $width
	 * @return void
	 */
	public function setWidth($width)
	{
		$this->width = $width;
	}
	/**
	 * Initialise la hauteur
	 *
	 * @param integer|float $height
	 * @return void
	 */
	public function setHeight($height)
	{
		$this->height = $height;
	}
	/**
	 * Génénère l'image sur le pdf
	 *
	 * @return void
	 */
	public function generate()
	{
		$this->pdf->Image($this->image_path, $this->x, $this->y, $this->width, $this->height);
	}
}
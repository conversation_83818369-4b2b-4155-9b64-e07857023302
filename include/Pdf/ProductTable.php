<?php
namespace Pdf;

use Exception;

require_once('Pdf/Column.php');

/** \defgroup ProductTable ProductTable
 * \ingroup PieceDeVente
 */
class ProductTable
{
    /**
	 * @var \Pdf\PieceDeVente $pdf
	 */
	protected $pdf;

    /**
	 * @var int $tbody_font_size
	 */
	protected $tbody_font_size = 9;

    /**
	 * @var int $row_height
	 */
	protected $row_height = 4;

    /**
	 * @var int $row_spacing
	 */
	protected $row_spacing = 3;

    /**
	 * @var int $tresh_hold
	 */
	// protected $tresh_hold = 270;
	protected $tresh_hold = 215;

    /**
	 * @var int $max_with
	 */
	protected $max_with = 190;

    /**
	 * @var array $columns
	 */
	protected $columns = array();

    /**
	 * @var int $width_sum
	 */
	protected $width_sum = 0;

    /**
	 * @var bool $borders
	 */
	protected $borders = true;

    /**
	 * @var bool $grid
	 */
	protected $grid = false;

    /**
	 * @var int $table_top
	 */
	protected $table_top = 0;

    /**
	 * @var array|resource $products
	 */
	protected $products = null;

    /**
	 * @var bool $is_mysql
	 */
	protected $is_mysql = false;

    /**
	 * @var \Closure $callback
	 */
	protected $callback = null;

	/**
	 * @var bool $draw_all_borders
	 */
	protected $draw_all_borders = 0;

	/**
	 * @var bool $must_fill_cell
	 */
	protected $must_fill_cell = 0;

	/**
	* @var bool $row_with_same_height
	*/
	protected $row_with_same_height = false;

	/**
	 * __construct
	 *
	 * \param PieceDeVente $pdf
	 *
	 * \return void
	 */
	public function __construct(PieceDeVente $pdf)
	{
		global $config;

		switch( $config['tnt_id'] ){
			case 447:
				$this->tresh_hold = 270;
				break;
		}

		$this->pdf = $pdf;
	}

	/**
	 * Configure la taille de police du corp du tableau
	 *
	 * \param mixed $size
	 *
	 * \return instance
	 */
	public function withTbodyFontSize($size)
	{
		if (is_numeric($size)) {
			$this->tbody_font_size = $size;
		}

		return $this;
	}

	/**
	 * Retourne la taille de police du corp du tableau
	 *
	 * @return int
	 */
	public function tbodyFontSize()
	{
		return $this->tbody_font_size;
	}

	/**
	 * Configure si toutes les bordures doivent être déssinées
	 *
	 * @param 	0 : aucun bord
				1 : cadre
				soit une chaîne contenant certains ou tous les caractères suivants (dans un ordre quelconque) :
				L : gauche
				T : haut
				R : droit
				B : bas
	 */
	public function withDrawAllBorders($draw)
	{
		$this->draw_all_borders = $draw;

		return $this;
	}

	/**
	 * Configure si la ligne doit aavoir la même hauteur pour chaque cellule
	 *
	 * @param bool $row_with_same_height
	 *
	 * @return $this
	 */
	public function withRowWithSameHeight($row_with_same_height) {
		$this->row_with_same_height = $row_with_same_height;
		return $this;
	}

	/**
	 * Retourne la lsite des colonnes.
	 *
	 * @return array
	 */
	public function columns()
	{
		return $this->columns;
	}

	/**
	 * Configure la hauteur d'une ligne de tableau
	 *
	 * \param mixed $height
	 *
	 * \return $this
	 */
	public function withRowHeight($height)
	{
		if (is_numeric($height)) {
			$this->row_height = $height;
		}

		return $this;
	}

    /**
	 * Retourne la hauteur de la ligne du tableau
	 *
	 * \return int Hauteur de la ligne du tableau
	 */
	public function rowHeight()
	{
		return $this->row_height;
	}

	/**
	 * Configure l'espace d'une ligne de tableau
	 *
	 * @param int $spacing
	 *
	 * @return $this
	 */
	public function withRowSpacing($spacing)
	{
		if (is_numeric($spacing)) {
			$this->row_spacing = $spacing;
		}

		return $this;
	}

    /**
	 * Retourne l'espace de la ligne du tableau
	 *
	 * @return int Espace de la ligne du tableau
	 */
	public function rowSpacing()
	{
		return $this->row_spacing;
	}

	/**
	 * Configure les produits du body
	 *
	 * \param mixed $products
	 * \return $this
	 *
	 * \throws Exception
	 */
	public function withBody($products)
	{
		if (!(is_array($products) || ria_mysql_control_ressource($products))) {
			throw new Exception("products must be an array or a mysql ressource");
		}
		if (ria_mysql_control_ressource($products)) {
			$this->is_mysql = true;
		}
		$this->products = $products;

		return $this;
	}

	/**
	 * Configure le callback
	 *
	 * \param \Closure $callback
	 *
	 * \return void
	 */
	public function withCallback(\Closure $callback)
	{
		$this->callback = $callback;

		return $this;
	}

	/**
	 * Configure si l'on veux des bordure ou non
	 *
	 * \param mixed $width
	 *
	 * \return instance
	 */
	public function withBorders($width)
	{
		$this->borders = (bool)$width;

		return $this;
	}

	/**
	 * Ajoute une colonne au tableau
	 *
	 * \param \Pdf\Column|array $column
	 * \param null|int $position
	 *
	 * \return instance
	 *
	 * \throws Exception
	 */
	public function addColumn($column, $position = null)
	{
		if (is_array($column)) {
			$column = $this->createColumnFromArray($column);
		} elseif (!$column instanceof Column) {
			throw new Exception('$column must be an instance of Column or an array');
		}

		if (is_null($position)) {
			$this->columns[] = $column;
		} else {
			$inserted = array($column);
			array_splice($this->columns, $position, 0, $inserted);
		}

		return $this;
	}

	/**
	 * Génère les lignes du tableau
	 *
	 * \return void
	 */
	public function drawBorders()
	{
		$this->resetX();
		$x = $initial = $this->pdf->getX();
		foreach ($this->columns as $column) {
			// echo $x;exit;
			$this->pdf->Line($x, $this->table_top, $x, $this->pdf->GetY());
			$x += $column->width();
		}
		$this->pdf->Line($x, $this->table_top, $x, $this->pdf->GetY());
		$this->pdf->Line($initial, $this->pdf->GetY(), $this->max_with + $initial, $this->pdf->GetY());
	}

	/**
	 * Génère le tableau
	 *
	 * \return void
	 */
	public function generateTable()
	{
		global $config;

		$this->resetX();
		$this->updateWidthSum();
		$this->updateColumnsSizes();
		$this->generateHeader();

		// On check si on doit colorer les cellules
		$contains_pairs_color_key = in_array("pdf_generation_devis_cells_pairs_color", $config) && $config["pdf_generation_devis_cells_pairs_color"] !== "#FFFFFF";
		$contains_odds_color_key = in_array("pdf_generation_devis_cells_odds_color", $config) && $config["pdf_generation_devis_cells_odds_color"] !== "#FFFFFF";
		$this->must_fill_cell =  $contains_pairs_color_key || $contains_odds_color_key;

		$cell_indice = 0;
		$generate_row = function ($product, & $cell_indice) {
			$this->searchMaxHeightRow($product);
			$this->generateRow($product, $cell_indice % 2 === 0);
			$cell_indice += 1;
		};

		// On change la couleur du tracé si on a uune config
		if (in_array("pdf_generation_devis_cells_borders_color", $config) && $config["pdf_generation_devis_cells_borders_color"] !== "#000000") {
			$red = hexdec(substr($config['pdf_generation_devis_cells_borders_color'], 1, 2));
			$green = hexdec(substr($config['pdf_generation_devis_cells_borders_color'], 3, 2));
			$blue = hexdec(substr($config['pdf_generation_devis_cells_borders_color'], 5, 2));
			$this->pdf->SetDrawColor($red, $green, $blue);
		}

		if ($this->is_mysql) {
			while ($product = ria_mysql_fetch_assoc($this->products)) {
				$generate_row($product, $cell_indice);
			}
		} else {
			foreach ($this->products as $product) {
				$generate_row($product, $cell_indice);
			}
		}
		$this->generateRow();
		$this->addBorders();

		// On retrace en noir
		$this->pdf->SetDrawColor(0, 0, 0);
	}

	/**
	 * Génère le header du tableau
	 *
	 * \return void
	 */
	public function generateHeader()
	{
		global $config;

		// $this->pdf->SetY($this->pdf->getY() + 6);
		// $this->pdf->SetY($this->pdf->getY());
		$this->updateTableTop();
		$Y = $tmp = $this->pdf->GetY();
		$X = $this->pdf->getX();
		foreach ($this->columns as $col) {
			$this->pdf->SetXY($X, $Y);
			$this->pdf->SetFont($this->pdf->font(), 'B', 7);
			if ( in_array("pdf_generation_devis_header_color", $config) && $config['pdf_generation_devis_header_color'] !== "#FFFFFF" ) {
				// On start à 1 pour éviter le #
				$red = hexdec(substr($config['pdf_generation_devis_header_color'], 1, 2));
				$green = hexdec(substr($config['pdf_generation_devis_header_color'], 3, 2));
				$blue = hexdec(substr($config['pdf_generation_devis_header_color'], 5, 2));
				$this->pdf->SetFillColor($red, $green, $blue);
				$this->pdf->Multicell($col->width(), 6, iconv('utf8', 'windows-1252', $col->name()), 1, $col->hAlign(), true);
			} else {
				$this->pdf->Multicell($col->width(), 6, iconv('utf8', 'windows-1252', $col->name()), 1, $col->hAlign());
			}

			$X += $col->width();

			if ($tmp < $this->pdf->getY()) {
				$tmp = $this->pdf->getY();
			}
		}
		$this->pdf->setY($tmp);
	}

	/**
	 * Génère une ligne du tableau
	 *
	 * \param array $product
	 * @param $cell_pair: indique si on genere une ligne paire ou impaire
	 *
	 * \return void
	 */
	public function generateRow($product = null, $cell_pair = null)
	{
		global $config;
		// if (!ria_array_key_exists(array('ecotaxe', 'tva_rate', 'tota_ht', 'total_ttc', 'ref', 'price_ht'), $product)) {
		// 	return false;
		// }

		if ($product == null){
			return;
		}

		// On change la couleur des cellules ici car des fois on redraw les headers donc on peerd cette couleur
		$change_row_color = function ($cell_pair = null) {
			global $config;

			if ($this->must_fill_cell) {
				if ($cell_pair !== null && $cell_pair) {
					$red = hexdec(substr($config['pdf_generation_devis_cells_pairs_color'], 1, 2));
					$green = hexdec(substr($config['pdf_generation_devis_cells_pairs_color'], 3, 2));
					$blue = hexdec(substr($config['pdf_generation_devis_cells_pairs_color'], 5, 2));
					$this->pdf->SetFillColor($red, $green, $blue);
				}

				if ($cell_pair !== null && !$cell_pair) {
					$red = hexdec(substr($config['pdf_generation_devis_cells_odds_color'], 1, 2));
					$green = hexdec(substr($config['pdf_generation_devis_cells_odds_color'], 3, 2));
					$blue = hexdec(substr($config['pdf_generation_devis_cells_odds_color'], 5, 2));
					$this->pdf->SetFillColor($red, $green, $blue);
				}
			}
		};
		$change_row_color($cell_pair);

			if (is_null($this->pdf->getData('taxes'))) {
				$col_qte = 1;
				if( isset($product['col_id']) && is_numeric($product['col_id']) && $product['col_id'] > 0 ){
					$col_qte = prd_colisage_types_get_qte( $product['col_id'] );
					if( !is_numeric($col_qte) || $col_qte <= 0 ){
						$col_qte = 1;
					}
				}

				$eco_total_ht = $eco_total_ttc = 0;

				// Meliconni est exclus de ce calcul dû à son PDF Spé
				if( $product['ecotaxe'] > 0 && !in_array($config['tnt_id'], [447]) ){
					$eco_total_ht = $product['ecotaxe'] * $product['qte'] * $col_qte;
					$eco_total_ttc = $eco_total_ht * _TVA_RATE_DEFAULT;

					if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
						$eco_total_ht = round( $eco_total_ht );
						$eco_total_ttc = round( $eco_total_ttc );
					}

					$this->pdf->taxes()->addEcotaxe($eco_total_ht, $eco_total_ttc-$eco_total_ht);
				}

				$total_ht = $product['price_ht'] * $product['qte'] * $col_qte;
				$total_ttc = ($product['price_ht'] * $product['qte'] * $col_qte) * $product['tva_rate'];
			
				if (isset($product['discount_type']) && isset($product['discount'])){
					if ($product['discount_type'] === "0"){ // Euros
						$total_ht = $product['total_ht'] - $product['discount'];
					} else { // %
						$total_ht = $product['total_ht'] * (1-($product['discount']/100));
					}

					$unit_ttc = round(($total_ht / $product['qte']) * $product['tva_rate'],2);
					$total_ttc = $unit_ttc * $product['qte'];
					
					if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
						$total_ht = round( $total_ht );
						$total_ttc = round( $total_ttc );
					}
				}

				$total_ht = $total_ht - $eco_total_ht;
				$total_ttc = $total_ttc - $eco_total_ttc;
				$this->pdf->taxes()->addTvaRate($product['tva_rate'], $total_ht, ($total_ttc - $total_ht));
			}

			// Calcul le nombre de lignes qu'il est possible d'afficher sur la partie restante de la page
			// Si besoin le texte sera coupé pour aarriver sur la page suivante
			$allow_lines = floor(($this->tresh_hold - $this->pdf->GetY() - $this->row_spacing) / $this->row_height);

			// Permet de cconnaitre là où le texte a été coupé avant le saut de page
			$designation_cell = '';
			// Permet de ne pas avoir un espace vide après le saut de pages si la cellule ne pouvait pas s'afficher complétement
			$cell_height_to_remove_if_needed = 0;

			if ($allow_lines <= 0){
				$allow_lines = 0;
			}

			if (($this->pdf->GetY() > $this->tresh_hold)) {
				// if (($this->pdf->GetY() >= $this->tresh_hold) || !$allow_lines ) {
				$this->addBorders();
				$this->pdf->addPage();
				$this->generateHeader();
				$change_row_color($cell_pair);
			}

			$Y = $tmp = $this->pdf->GetY();
			$X = $this->pdf->getX();
			$this->pdf->SetFont($this->pdf->font(), '', $this->tbodyFontSize());
			foreach ($this->columns as $column) {
				$this->pdf->SetXY($X, $Y + $this->row_spacing);

				if ($column->name() == 'Référence' || $column->name() == mb_strtoupper('Référence') || $column->name() == 'Code article' || $column->name() == mb_strtoupper('Code article')){
					$is_break = false;
					switch(get_class($this->pdf)){
						case 'Pdf\OrderPdf' : {
							if ($this->pdf->getOption('prd_reftruncated') && $column->using($product) != ""){
								$is_break = true;
							}
							break;
						}
						/*case 'Pdf\InvoicePdf' : {
							if ($this->pdf->getOption('prd_reftruncated') && $column->using($product) != ""){
								$is_break = true;
							}
							break;
						}*/
					}

					$txt = iconv('UTF-8', 'windows-1252', $column->using($product));
					if ($is_break){
						$this->pdf->BreakCell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $txt, $this->draw_all_borders, $column->align(), $this->must_fill_cell);
					} else {
						$this->pdf->Multicell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $txt, $this->draw_all_borders, $column->align(), $this->must_fill_cell);
					}
				} elseif($column->name() == "Désignation" || $column->name() == mb_strtoupper("Désignation")) {
					$text = $column->using($product);
					$text = str_replace( ['é', 'û', 'à'], ['é', 'û', 'à'], $text );
					$txt = iconv('UTF-8', 'windows-1252', $text );
					$designation_cell = $this->pdf->MultiCell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $txt, $this->draw_all_borders, $column->align(), $this->must_fill_cell, $allow_lines);

					if ($designation_cell != "") {
						// Calcul de la hauteur utilisée avant saut de page
						// $cell_height_to_remove_if_needed = ($this->row_with_same_height ? $column->heightSingleLine() : $this->row_height) * $this->NbLines($column->width(), iconv('UTF-8', 'windows-1252', str_replace($designation_cell, "", $txt)));
						// iconv("cp1252", "utf-8//IGNORE", $cp1252);

						$cell_height_to_remove_if_needed = ($this->row_with_same_height ? $column->heightSingleLine() : $this->row_height) * $this->NbLines($column->width(), iconv("cp1252", "utf-8//IGNORE", str_replace($designation_cell, "", $txt)));
					}
				} elseif($column->name() == "Image" || $column->name() == mb_strtoupper("Image")) {
					$column->using($product);
					$txt = '';
					$this->pdf->Multicell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $txt, $this->draw_all_borders, $column->align(), $this->must_fill_cell);
				} elseif( $column->using($product) !== null ) {
					$txt = iconv('UTF-8', 'windows-1252', $column->using($product)).(in_array($column->name(), array('P.U. HT', 'Eco-part.', 'Montant HT', 'Total HT', 'DEEE', 'P.U. Rem.', mb_strtoupper("montant ht"), mb_strtoupper("P.U. ht"), mb_strtoupper("p.U. ht rem"), mb_strtoupper("p.u. rem."))) && $product['id'] != 0 ? ' '.$this->pdf->currency : '');
					$this->pdf->Multicell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $txt, $this->draw_all_borders, $column->align(), $this->must_fill_cell);
				}
				$X += $column->width();

				if( $tmp < ($this->pdf->GetY() /*+ $this->row_spacing*/ + ($this->row_with_same_height ? $column->heightSingleLine() : $this->row_height)) ){
					$tmp = ($this->pdf->GetY() /*+ $this->row_spacing*/ + ($this->row_with_same_height ? $column->heightSingleLine() : $this->row_height));
				}

				// if ($tmp < $this->pdf->getY() + $this->row_spacing) {
				// 	$tmp = $this->pdf->getY() + $this->row_spacing;
				// }
			}
			// $tmp = $this->pdf->GetY() + $this->row_spacing;

			// Si $designation_cell est différent de '' alors c'est que le texte n'a pas été écrit totalement sur la page
			// Donc il faut changer de page pour continuer à écrire ce texte
			$break_multicell = $designation_cell != "";

			if ($break_multicell) {
				$this->pdf->SetY($this->tresh_hold);
				$this->addBorders();
				$this->pdf->addPage();
				$this->generateHeader();
				$change_row_color($cell_pair);
				$Y = $tmp = $this->pdf->GetY();
				$X = $this->pdf->getX();
				$this->pdf->SetFont($this->pdf->font(), '', $this->tbodyFontSize());
				// Enregistrement de l' indice de Désignation
				$col_desi_indice = 0;
				foreach ($this->columns as $column) {
					$this->pdf->SetXY($X, $Y + $this->row_spacing);
					if($column->name() == "Désignation" || $column->name() == mb_strtoupper("Désignation")) {
						$this->pdf->Multicell($column->width(), $this->row_with_same_height ? $column->heightSingleLine() : $this->row_height, $designation_cell, $this->draw_all_borders, $column->align());
					} else {
						$this->pdf->Multicell($column->width(), $column->height(), "", $this->draw_all_borders, $column->align());
						$col_desi_indice += 1;
					}
					$X += $column->width();
				}

				// Permet de continuer à écrire juste après le split du texte sur la page suivante
				if ($tmp < $this->pdf->getY() + $this->row_spacing) {
					// Il faut retirer 35% de la hauteur du noveau bloc pour que ça se passe bien, je sais pas pourquoi.
					// Appparement il y a un problème avec les saut de ligne, ils sont doublé parfois alors qu'il n' y en a qu'un.
					// Il faut utiliser la colonne Désignation parce que c'est elle qui pose problème.
					$cell_height_to_remove_if_needed -= (($this->row_with_same_height ? $column->heightSingleLine() : $this->row_height) * $this->NbLines($this->columns[$col_desi_indice]->width(), $designation_cell)) * 0.35;
					$tmp = $this->pdf->getY() + $this->row_spacing - $cell_height_to_remove_if_needed + 0;
				}
			}

			$this->pdf->SetY($tmp);

			if (is_callable($this->callback)) {
				call_user_func($this->callback, $product);
			}
	}

	/**
	 * Alias pour réinitialisé la coordonnée X
	 *
	 * \return void
	 */
	protected function resetX()
	{
		$this->pdf->resetX();
	}

	/**
	 * Créer une instance de Column si on passe un tableau avec les valeurs 'name', 'align', 'optimal_width', 'use'
	 *
	 * \param array $column
	 * \return \Pdf\Column
	 *
	 * \throws Exception
	 */
	protected function createColumnFromArray(array $column)
	{
		if (!ria_array_key_exists(array('name', 'align', 'optimal_width', 'use'), $column)) {
			throw new Exception("Invalid array format for column");
		}
		return new Column($column['name'], $column['align'], $column['optimal_width'], $column['use']);
	}

	/**
	 * Met a jour la coordonné Y du haut du tableau
	 *
	 * \return void
	 */
	protected function updateTableTop()
	{
		$this->table_top = $this->pdf->getY();
	}

	/**
	 * Ajout les bordure si on en veux
	 *
	 * \return void
	 */
	protected function addBorders()
	{
		if ($this->borders) {
			$this->drawBorders();
		}
	}

	/**
	 * Met a jours les largeur des colonnes en fonction de la taille du tableau et du nombre de colonne
	 *
	 * \return void
	 */
	protected function updateColumnsSizes()
	{
		foreach ($this->columns as $col) {
			$ratio = ($col->optimalWidth() / $this->width_sum);
			$size = $this->max_with * $ratio;
			$col->withWidth($size);
		}
	}

	/**
	 * Met a jour la somme des tailles optimal des colonnes
	 *
	 * \return void
	 */
	protected function updateWidthSum()
	{
		$this->width_sum = 0;
		foreach ($this->columns as $col) {
			$this->width_sum += $col->optimalWidth();
		}
	}

	/**
	* Permet de trouver la hauteur max pour appliquer la même hauteur à chaque cellule
	**/
	protected function searchMaxHeightRow($product) {
		require_once("strings.inc.php");

		global $config;

		$max_height = $this->row_height;
		$this->pdf->SetFont($this->pdf->font(), '', $this->tbodyFontSize());
		foreach ($this->columns() as $column) {
			$txt = "";
			if ($column->name() == 'Référence' || $column->name() == mb_strtoupper('Référence') || $column->name() == 'Code article' || $column->name() == mb_strtoupper('Code article')){
				$is_break = false;
				switch(get_class($this->pdf)){
					case 'Pdf\OrderPdf' : {
						if ($this->pdf->getOption('prd_reftruncated') && $column->using($product) != ""){
							$is_break = true;
						}
						break;
					}
				}

				$txt = iconv('UTF-8', 'windows-1252', $column->using($product));

				// On vérifie que le texte ne contient pas que des MAJ et on check aussi que la référence n'est pas du style TTFOOD-123
				if (!preg_match("/(^[A-Z]+)$/", $txt) && !preg_match("/(^[A-Z]+-[0-9]+)$/", $txt)) {
					$txt = mb_strtolower($txt);
				}
			} elseif($column->name() == "Désignation" || $column->name() == mb_strtoupper("Désignation")) {
				$text = $column->using($product);
				$text = str_replace( ['é', 'û', 'à'], ['é', 'û', 'à'], $text );
				$txt = iconv('UTF-8', 'windows-1252', $text);
			} elseif($column->name() == "Image" || $column->name() == mb_strtoupper("Image")) {
				$txt = '';
			} elseif( $column->using($product) !== null ){
				// On remplace :
				// - $this->pdf->currency par e
				// - % par p
				// - , par .
				$txt = $column->using($product).(in_array($column->name(), array('P.U. HT', 'Eco-part.', 'Montant HT', mb_strtoupper("montant ht"), mb_strtoupper("P.U. ht"), mb_strtoupper("p.U. ht rem"))) && $product['id'] != 0 ? ' '.$this->pdf->currency : '');
				$txt = str_replace($this->pdf->currency, "e", $txt);
				$txt = str_replace("%", "p", $txt);
				$txt = str_replace(",", ".", $txt);
				$txt = iconv('UTF-8', 'windows-1252', $txt);

				// On retire les 0 derrière la virgule
				$txt_explode = explode(".", $txt);
				if (sizeof($txt_explode) > 1) {
					$txt = str_replace("0", "", $txt_explode[1]);
				}
			}

			$nb_lines = $this->NbLines($column->width(), $txt);
			$max_height = max($max_height, $nb_lines * $this->row_height);
			$column->withNbLine($nb_lines);
		}

		foreach ($this->columns() as $column) {
			$column->withHeight($max_height);
		}
	}

	/**
	* Calcul le nombre de ligne nécessaire pour un multiCell de largeur w avec le texte txt
	*
	* @param $w: Width du MultiCell
	* @param $txt: le texte dans le MultiCell
	*
	* @return $nl: le nombre de lignes nnécessaire au MultiCell
	* */
	protected function NbLines($w, $txt) {
		$txt = trim($txt);

		// Recupère le jeu de caractère
	    $set_of_characters = $this->pdf->CurrentFont['cw'];

	    // Calcul la largeur de la cellule pour le PDF avec la dimmension de la police.
	    // On ajoute trois fois espace pour simuler des bords et avoir plus de place.
	    $w_max = ceil(((($w - 10.00125) * 1000) / $this->pdf->FontSize) + (3 * $set_of_characters[" "]));
	    $txt = str_replace("\t","",$txt);
	    $txt = str_replace("\r","",$txt);
	    $txt = str_replace("\0","",$txt);
	    $txt = str_replace("\x0B","",$txt);
	    $number_of_characters = strlen($txt);
	    $line_width = 0;

	    // Verifie que le dernier caratère n'est pas \n. Si c'est le cas on réduit nb de 1 car on comptera pas ce caractère.
	    if ($number_of_characters > 0 and $txt[$number_of_characters-1] ===  "\n") {
	        $number_of_characters--;
	    }

	    $number_of_lines = 1;
	    for ($i=0; $i < $number_of_characters; $i++) {
	    	// Récupere le caratere
	    	$character = substr($txt, $i, 1);

	    	switch ($character) {
	    		case "\n":
	    			$number_of_lines += 1;
	    			$line_width = 0;
	    			break;

	    		case " ":
	    			break;

	    		default:
	    			if ($line_width > $w_max) {
	    				$line_width = $set_of_characters[$character];
	    				$number_of_lines += 1;
	    			} else {
	    				$line_width += $set_of_characters[$character];
	    			}
	    			break;
	    	}
	    }

	    return $number_of_lines;
	}
}
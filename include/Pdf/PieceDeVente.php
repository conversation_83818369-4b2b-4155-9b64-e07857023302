<?php

namespace Pdf;

require_once('Pdf/Pdf.php');
require_once('Pdf/Taxes.php');
require_once('Pdf/ProductTable.php');
require_once('Pdf/ImagePdf.php');

define('EURO', chr(128));
define('USD', chr(36));
define('GBP', chr(163));
define('CHF', 'CHF');
define('XPF', 'XPF');
define('FCFP', 'FCFP');

/**
 * \defgroup PieceDeVente PieceDeVente
 * \ingroup pdf oms
 */
abstract class PieceDeVente extends Pdf
{
	/**
	 * @var boolean $show_taxcode
	 */
	protected $show_taxcode = true;

	/**
	 * @var constant $currency
	 */
	public $currency = EURO;

	/**
	 * @var \Pdf\Taxes $taxes
	 */
	protected $taxes = null;

	/**
	 * @var \Pdf\ImagePdf $logo_image
	 */
	protected $logo_image;

	/**
	 * @var null $owner
	 */
	protected $owner = null;

	/**
	 * @var null $user
	 */
	protected $user = null;

	/**
	 * @var null $dlv_address
	 */
	protected $dlv_address = null;

	/**
	 * @var null $adr_height
	 */
	protected $adr_height = null;

	/**
	 * @var null $inv_address
	 */
	protected $inv_address = null;

	/**
	 * @var boolean $total_page
	 */
	protected $total_page = false;

	/**
	 * @var boolean $total_page_header
	 */
	protected $total_page_header = true;

	/**
	 * @var int $total_page_y Hauteur totale de la page PDF
	 */
	protected $total_page_y = 220;

	/**
	 * @var int $owner_y Hauteur du bloc owner
	 */
	protected $owner_y;

	/**
	 * @var \Pdf\ProductTable $table
	 */
	protected $table = null;

	/**
	 * @var bool $display_bank_bloc
	 */
	protected $display_bank_bloc = false;

	/**
	 * @var array Options de génération
	 */
	protected $options;

	/**
	 * @var boolean Options initialisées ?
	 */
	protected $options_initialized = false;

	/**
	 * Undocumented variable
	 *
	 * @var boolean $user_info_on_all_pages
	 */
	protected $user_info_on_all_pages = true;

	/**
	 * Bordure sur le bloc d'adresse
	 *
	 * @var boolean
	 */
	protected $address_bloc_border = false;

	/**
	 * @var array Data to output
	 */
	protected $data;

	/**
	 * __construct
	 *
	 * \return void
	 */
	public function __construct()
	{
		parent::__construct();

		$this->options = array();
		$this->data = array();
		$this->table = new ProductTable($this);
		$this->taxes = new Taxes;
	}

	/**
	 * Contient le corp du pdf entre le header et le footer
	 *
	 * \return void
	 */
	abstract public function body();

	/**
	 * Défini la devise utilisée sur le pdf
	 *
	 * \return void
	 */
	protected function setCurrency(){

		if( is_array( $this->data['ord'] ) ){

			if( isset( $this->data['ord']['id'] ) && isset( $this->data['ord']['currency'] ) ){
				$currency = $this->data['ord']['currency'];
			}
		}


		if( !isset( $currency ) && is_array( $usr = $this->data['user'] ) && ria_mysql_num_rows( $r_prc = prd_prices_categories_get( isset( $usr['prc_id'] ) ? $usr['prc_id'] : 0 ) ) ){
			$prc = ria_mysql_fetch_assoc( $r_prc );

			if( is_array( $prc ) && isset( $prc['money_code'] ) ){
				$currency = $prc['money_code'];
			}
		}
		
		switch( strtoupper( isset( $currency ) ? $currency : 'EUR' ) ){

			case 'GBP':
				$this->currency = GBP;
				break;
			case 'CHF':
				$this->currency = CHF;
				break;
			case 'XPF':
				$this->currency = XPF;
				break;
			case 'FCFP':
				$this->currency = FCFP;
				break;
			default:
				$this->currency = EURO;
		}
		return $this->currency;

	}

	/**
	 * Génère le bloc d'information du client et de la piece de vente
	 *
	 * \return void
	 */
	abstract protected function userInfoRow();

	/**
	 * Génère la page avec les totaux de la pièce de vente
	 *
	 * \return void
	 */
	abstract protected function generateTotalPage();

	/**
	 * Génère l'en-tête d'une pièce de vente
	 *
	 * \return void
	 */
	abstract protected function blocHeader();

	/**
	 * Génère le pied d'une pièce de vente
	 *
	 * \return void
	 */
	abstract protected function blocFooter();

	/**
	 * Récupération des options par défaut
	 *
	 * \return array Default options
	 */
	abstract protected function getDefaultOptions();

	/**
	 * Défini si on affiche l'en-tête info client sur toutes les pages ou non
	 *
	 * \param boolean $yes
	 *
	 * \return PieceDeVente
	 */
	public function withUserInfoOnAllPages($yes=true)
	{
		$this->user_info_on_all_pages = $yes;

		return $this;
	}

	/**
	 * Retourne si on affiche l'en-tête info client sur toutes les pages ou non
	 *
	 * \return boolean
	 */
	public function userInfoOnAllPages()
	{
		return $this->user_info_on_all_pages;
	}

	/**
	 * Défini si on affiche les block d'adresse avec des bordure où non
	 *
	 * \param boolean $address_bloc_border
	 * \return PieceDeVente
	 */
	public function withAdressBlocBorder($address_bloc_border=true)
	{
		$this->address_bloc_border = $address_bloc_border;

		return $this;
	}

	/**
	 * Retourne si on affiche les bloc de texte avec des bordures ou non
	 *
	 * \return boolean
	 */
	public function adressBlocBorder()
	{
		return $this->address_bloc_border;
	}
	/**
	 * Récupération des options
	 *
	 * \return array Options
	 */
	public function getOptions()
	{
		if (!$this->options_initialized) {
			$this->options = array_merge($this->getDefaultOptions(), $this->options);
		}

		return $this->options;
	}

	/**
	 * Modification des options
	 *
	 * \param array $options Options
	 *
	 * \return $this
	 */
	public function setOptions(array $options)
	{
		$this->options = array_merge($this->getDefaultOptions(), $options);
		$this->options_initialized = true;

		return $this;
	}

	/**
	 * Vérifie l'existance d'une option
	 *
	 * \param string $name Nom de l'option
	 *
	 * \return boolean L'option existe-t-elle ?
	 */
	public function hasOption($name)
	{
		if (!$this->options_initialized) {
			$this->options = array_merge($this->getDefaultOptions(), $this->options);
			$this->options_initialized = true;
		}

		return isset($this->options[$name]);
	}

	/**
	 * Récupération de la valeur d'une option
	 *
	 * \param string $name Nom de l'option
	 *
	 * \return mixed Valeur de l'option
	 *
	 * \throws Exception Option non configurée
	 */
	public function requireOption($name)
	{
		if (!$this->options_initialized) {
			$this->options = array_merge($this->getDefaultOptions(), $this->options);
		}

		if (!isset($this->options[$name])) {
			throw new \Exception('Option manquante: ' . $name);
		}

		return $this->options[$name];
	}

	/**
	 * Récupération de la valeur d'une option
	 *
	 * \param string $name Nom de l'option
	 *
	 * \return null|mixed Valeur de l'option ou null
	 */
	public function getOption($name)
	{
		if (!$this->options_initialized) {
			$this->options = array_merge($this->getDefaultOptions(), $this->options);
		}

		return isset($this->options[$name]) ? $this->options[$name] : null;
	}

	/**
	 * Récupération des données du PDF
	 *
	 * \param string|null $name Nom de la donnée. Toutes si non spécifié
	 *
	 * \return mixed Données du PDF
	 */
	public function getData($name = null)
	{
		if (is_null($this->data)) {
			return null;
		}

		if (is_null($name)) {
			return $this->data;
		}

		return isset($this->data[$name]) ? $this->data[$name] : null;
	}

	/**
	 * Récupération des données du PDF avec vérification
	 *
	 * \param string|null $name Nom de la donnée. Toutes si non spécifié.
	 *
	 * \return mixed Données du PDF
	 */
	public function requireData($name = null)
	{
		if (is_null($this->getData($name))) {
			throw new \Exception(is_null($name) ? 'Données manquantes' : 'Donnée manquante: ' . $name);
		}

		return $this->getData($name);
	}

	/**
	 * Modification des données du PDF
	 *
	 * \param array $data Données du PDF
	 *
	 * \return $this
	 */
	public function setData(array $data = null)
	{
		$this->data = $data;

		return $this;
	}

	/**
	 * Lance toutes les fonction de génération du pdf avant ça sortie
	 *
	 * \return void
	 */
	protected function bootstrap()
	{
		$no_data = !is_array($this->data);
		if( $no_data || !isset($this->data['bank_details']) ){
			$this->loadBankDetails();
		}

		if( $no_data || !isset($this->data['owner']) ){
			$this->loadSiteOwner();
		}

		if ($no_data || !isset($this->data['logo'])) {
			$this->loadLogo();
		}

		$this->setCurrency();

		return parent::bootstrap();
	}

	/**
	 * Chargement des informations bancaires
	 *
	 * \return $this
	 * \deprecated
	 */
	protected function loadBankDetails()
	{
		$r_bankinfo = site_bank_details_get(0);
		$this->data['bank_details'] = null;
		if ($r_bankinfo && ria_mysql_num_rows($r_bankinfo)) {
			$this->data['bank_details'] = ria_mysql_fetch_assoc($r_bankinfo);
		}

		return $this;
	}

	/**
	 * Récupère les informations sur le client
	 *
	 * \return void
	 * \deprecated
	 */
	protected function loadSiteOwner()
	{
		$this->data['owner'] = site_owner_get();

		return $this;
	}

	/**
	 * Chargement du logo
	 *
	 * \return $this
	 *
	 * \todo Chargement des options depuis la configuration en dehors de cette classe pour ne plus avoir à appeler Mysql ici
	 */
	protected function loadLogo()
	{
		global $config;

		$this->data['logo'] = null;

		$img_id = $this->getOption('logo');
		if (intval($img_id) <= 0) {
			return $this;
		}

		$r_img = img_images_get($img_id);
		if( $r_img && ria_mysql_num_rows($r_img) ){
			$img = ria_mysql_fetch_assoc($r_img);

			$file = img_images_get_filesource( $img['id'] );
			if( trim($file) != '' ){
				$this->data['logo'] = $config['img_dir'].'/source/'.$file;
			}
		}

		return $this;
	}

	/**
	 * Retourne l'object Taxes qui s'occupe de la gestion des taxes (tva et ecotaxe)
	 *
	 * \return \Pdf\Taxes
	 */
	public function taxes()
	{
		return $this->taxes;
	}

	/**
	 * Retourne l'object ProductTable qui s'occupe de la génération du tableau des ligne produit
	 *
	 * \return \Pdf\ProductTable
	 */
	public function table()
	{
		return $this->table;
	}

	/**
	 * Retourne le nombre $total_page_y qui permet de définir si l'on fait un saut de page ou non
	 *
	 * \return int
	 */
	public function getPageBreakY() {
		return $this->total_page_y;
	}

	/**
	 * Contient le contenue du pdf générique pour toutes les pièce de vente
	 *
	 * \return void
	 */
	public function content()
	{
		$this->body();
		if ($this->shouldGenerateTotalPage()) {
			$this->resetX();
			$this->last_page = true;
			if ($this->total_page_header) {
				$this->with_header = true;
			}

			$this->generateTotalPage();
			$this->blocFooter();
			if ($this->display_bank_bloc){
				$this->bankInfoBloc();
			}
		}
	}

	/**
	 * Ajoute le bloc sur les informations banquaire du client
	 *
	 * \return void
	 */
	public function bankInfoBloc()
	{
		$this->resetX();
		$this->setY(268);
		$this->setFont($this->font(), '', 9);

		if (is_null($this->getData('bank_details'))) {
			return;
		}

		$this->Cell(200, 4, utf8_decode('IBAN: ' . $this->data['bank_details']['iban']), 0, 1);
		$this->Cell(200, 4, utf8_decode('BIC: ' . $this->data['bank_details']['bic']), 0, 1);
		$this->Cell(200, 4, utf8_decode(
			$this->data['bank_details']['name']
				. ' - ' . $this->data['bank_details']['agency_name']
				. ' - ' . $this->data['bank_details']['address1']
				. ' ' . $this->data['bank_details']['address2']
				. ' - ' . $this->data['bank_details']['zipcode']
				. ' ' . $this->data['bank_details']['city']),
			0,
			1
		);
	}

	/**
	 * Génère le header d'une pièce de vente
	 *
	 * \return void
	 */
	public function Header()
	{
		if ($this->PageNo() == 1 || $this->haveHeader()) {
			$this->blocOwner();
			$this->logo();
			$this->adressesRow();
			$this->blocHeader();
		}
		if ($this->PageNo() == 1 || $this->userInfoOnAllPages()) {
			$this->userInfoRow();
		}
	}

	/**
	 * Génère le logo d'un client
	 *
	 * \return void
	 */
	public function logo()
	{
		if( is_null($this->logo_image) && !is_null($this->getData('logo')) ){
			$x_pos = 0;
			switch( $this->requireOption('logo_disposition') ){
				case 0:
					$x_pos = $this->w - $this->rMargin - $this->convertPixelToFPDF($this->requireOption('logo_size_x'));
					break;
				case 1:
					$x_pos = 11;
					break;
			}

			$this->withLogo(
				$this->data['logo'],
				$x_pos,
				6,
				$this->convertPixelToFPDF($this->requireOption('logo_size_x')),
				$this->convertPixelToFPDF($this->requireOption('logo_size_y'))
			);
		}

		if( !is_null($this->logo_image) ){
			$this->logo_image->generate();
		}
	}

	/**
	 * Génère le block d'information sur le client
	 *
	 * \return void
	 */
	public function blocOwner()
	{
		if (is_null($this->getData('owner'))) {
			return;
		}

		global $config;

		// On peut "surcharger" les détails du propriétaire via les options.
		if( $this->hasOption('owner') ){
			foreach( $this->getOption('owner') as $key => $option ){
				$this->owner[$key] = $option;
			}
		}

		$intracommunautaire_height = $this->GetY();

		if (intval($this->requireOption('logo')) > 0 && $this->requireOption('logo_disposition') == 1){
			$height = intval($this->convertPixelToFPDF($this->requireOption('logo_size_y'))) + 8;
			$intracommunautaire_height = $height;
			$this->SetY($height);
		}

		$this->SetFont($this->font(), 'B', 12);
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['name']), 0, 1, '');
		$this->Cell(50, 4, '', 0, 2);

		$this->SetFont($this->font(), '', 9);
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['address1']), 0, 1, '');
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['zipcode'] . ' ' . $this->data['owner']['city']), 0, 1, '');
		$this->Cell(50, 3, '', 0, 2);

		$this->Cell(15, 4.5, utf8_decode('N° Siret : '), 0, 0, '');
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['inscription']), 0, 1, '');
		$intracommunautaire_height += 21.5;

		if ($this->data['owner']['capital'] != ""){
			$this->Cell(15, 4.5, utf8_decode('Capital : '), 0, 0, '');
			$this->Cell(50, 4.5, utf8_decode(number_format($this->data['owner']['capital'], 0, ',', ' ')).' '.EURO, 0, 1, '');
			$intracommunautaire_height += 4.5;
		}
		if ($this->data['owner']['naf'] != ""){
			$this->Cell(15, 4.5, utf8_decode('N.A.F. : '), 0, 0, '');
			$this->Cell(50, 4.5, utf8_decode($this->data['owner']['naf']), 0, 1, '');
			$intracommunautaire_height += 4.5;
		}
		$this->Cell(50, 4.5, utf8_decode('N° intracommunautaire : ' . $this->data['owner']['taxcode']), 0, 1, '');
		$this->Cell(50, 3, '', 0, 2);

		$this->Cell(25, 4.5, utf8_decode('Téléphone : '), 0, 0, '');
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['phone']), 0, 1, '');
		if ($this->data['owner']['fax'] != ""){
			$this->Cell(25, 4.5, utf8_decode('Télécopie : '), 0, 0, '');
			$this->Cell(50, 4.5, utf8_decode($this->data['owner']['fax']), 0, 1, '');
		}
		$this->Cell(25, 4.5, utf8_decode('Email : '), 0, 0, '');
		$this->Cell(50, 4.5, utf8_decode($this->data['owner']['email']), 0, 1, '');

		// Vu que nous sommes dans la classe parente, nous devons connaître le type de PDF (facture ou devis)
		// pour pouvoir déterminer quelle variable récupérer.
		$display_site_section = true;
		$site_url = '';
		$var_code = $this instanceof \Pdf\InvoicePdf ? 'pdf_generation_inv_url' : 'pdf_generation_devis_url';

		// Si la variable de config existe est n'est pas vide alors nous l'affichons en tant que lien du site.
		if( isset($config[$var_code]) && trim($var_code) ){
			$site_url = trim($config[$var_code]);
		} else {
			// Nous nous assurons que le "site_url" ne contienne pas les mots "maquettes" ou "preprod"
			// Ceci est une sécurité supplémentaire pour être sur de ne pas afficher un site de développement au client.
			if( strpos($config['site_url'], 'maquettes') === false && strpos($config['site_url'], 'preprod') === false ){
				$site_url = $config['site_url'];
			} else {
				// Si aucun lien n'est valide, alors il ne faut pas montrer la section concernant le site.
				$display_site_section = false;
			}
		}

		if($display_site_section){
			$this->Cell(25, 4.5, utf8_decode('Site : '), 0, 0, '');
			$this->Cell(50, 4.5, utf8_decode($site_url), 0, 1, '');
		}

		$this->Cell(50, 3, '', 0, 2);

		$this->adr_height = $this->getOption('display_dlv_address')
				&& !is_null($this->data)
				&& isset($this->data['addresses'])
				&& isset($this->data['addresses']['delivery'])
				&& !is_null($this->data['addresses']['delivery'])
			? $this->GetY()
			: $intracommunautaire_height;

		$this->owner_y = $this->getY();
	}

	/**
	 * Détermine si l'on affiche le numéro de Tva du client qui a passé la commande
	 *
	 * \param mixed $bool
	 *
	 * \return instance
	 */
	public function showTaxcode($bool)
	{
		$this->show_taxcode = (bool)$bool;

		return $this;
	}

	/**
	 * Détermine si l'on doit afficher le numéro de tva
	 *
	 * \return void
	 */
	public function shouldShowTaxcode()
	{
		return $this->show_taxcode;
	}

	/**
	 * Détermine si l'on doit afficher le bloc des informations bancaires
	 *
	 * \return void
	 */
	public function setDisplayBankInfo($display_bank_info = true)
	{
		$this->display_bank_bloc = (bool)$display_bank_info;
	}

	/**
	 * Configure l'utilisateur à qui apartient la pièce de vente
	 *
	 * \param array $user
	 *
	 * \return instance
	 */
	public function withUser(array $user)
	{
		$this->data['user'] = $user;

		return $this;
	}

	/**
	 * Configure l'adresse de facturation
	 *
	 * \param array $adress
	 *
	 * \return void
	 */
	public function withInvAdress(array $address)
	{
		$this->data['addresses']['invoice'] = $address;
		$this->data['addresses']['invoice']['type'] = 'facturation';

		return $this;
	}

	/**
	 * Configure l'adresse de livraison
	 *
	 * \param array $adress
	 *
	 * \return void
	 */
	public function withDlvAdress(array $adress)
	{
		$this->data['addresses']['delivery'] = $adress;
		$this->data['addresses']['delivery']['type'] = 'livraison';

		return $this;
	}

	/**
	 * configure le logo du client
	 *
	 * \param mixed $logo_image
	 * \param float $x
	 * \param float $y
	 * \param float $width
	 * \param float $height
	 *
	 * \return void
	 */
	public function withLogo( $logo_image, $x=80, $y=0, $width=50, $height=30 )
	{
		$this->logo_image = new ImagePdf($this, $logo_image, $x, $y, $width, $height);

		return $this->logo_image;
	}

	/**
	 * Génère la ligne avec les adresses
	 *
	 * \param mixed $margin_left
	 *
	 * \return void
	 */
	public function adressesRow($margin_left = 120)
	{
		$display_dlv_address = false;
		if ($this->getOption('display_dlv_address')
			&& !is_null($this->data)
			&& isset($this->data['addresses'])
			&& isset($this->data['addresses']['delivery'])
			&& !is_null($this->data['addresses']['delivery'])
		){
			$display_dlv_address = true;
		} else {
			$this->SetXY($margin_left, $this->adr_height);
		}

		$Y = $this->getY();
		if (!is_null($this->data)
			&& isset($this->data['addresses'])
			&& isset($this->data['addresses']['invoice'])
			&& !is_null($this->data['addresses']['invoice'])
		) {
			$this->adressBloc($this->data['addresses']['invoice'], $this->adressBlocBorder(), $display_dlv_address);
		}

		if ($display_dlv_address){
			$this->setXY($margin_left, $Y);
			$this->adressBloc($this->data['addresses']['delivery'], $this->adressBlocBorder(), true);
		}

		$this->resetX();
	}

	/**
	 * Génère le bloc d'adresse
	 *
	 * \param  array $address
	 * \param  bool  $with_border
	 * \param  bool  $display_adr_type
	 * \return void
	 */
	public function adressBloc($address, $with_border = true, $display_adr_type = false)
	{
		$this->SetFont($this->font(), 'B', 9);

		if( $display_adr_type ){
			$this->Cell(35, 5, utf8_decode('Adresse de '.$address['type']), 0, 2);
		}

		$x = $this->getX();
		$y = $this->getY();
		$name = '';

		$name .= $address['firstname'].' '.$address['lastname'];
		$height = 0;

		// Affiche la socité si elle est renseigné
		if( $society = trim($address['society']) ){
			$this->Cell(100, 5, utf8_decode($society), 0, 2);
			$height += 5;
		}

		// Affichage du nom et prénom.
		if( $name = trim($name) ){
			$this->Cell(100, 5, utf8_decode($name), 0, 2);
		}

		// Interligne entre nom et adresse
		$this->Cell(100, 5, '', 0, 2);
		$height += 10;
		$this->SetFont($this->font(), '', 9);
		$this->Cell(100, 5, utf8_decode($address['address1']), 0, 2);
		$height += 5;

		if( $address2 = trim($address['address2']) ){
			$this->Cell(100, 5, utf8_decode($address2), 0, 2);
			$height += 5;
		}

		$this->Cell(100, 5, utf8_decode($address['postal_code'].' '.$address['city']), 0, 2);
		$height += 5;

		if( !$display_adr_type ){
			$this->Cell(100, 5, '', 0, 2);
			$height += 5;
		}

		if( $with_border ){
			$this->Rect($x, $y, 80, $height);
		}

		$this->owner_y = $this->getY() + 5;
	}

	/**
	 * Instantie une Column avec le format de prix
	 *
	 * \param mixed $title
	 * \param mixed $align
	 * \param mixed $optimal_size
	 * \param mixed $key
	 *
	 * \return void
	 */
	protected function formatPriceColumn($title, $align, $optimal_size, $key)
	{
		$col = new Column($title, $align, $optimal_size, function ($p) use ($key) {
			return ria_number_french($p[$key]);
		});

		return $col;
	}

	/**
	 * Détermine si il faut générer la page des totaux
	 *
	 * \return void
	 */
	protected function shouldGenerateTotalPage()
	{
		return $this->total_page;
	}

	/**
	 * Configure la génération de la page des totaux
	 *
	 * \param mixed $with_header
	 *
	 * \return void
	 */
	public function addTotalPage($with_header = true, $total_page_y = 220)
	{
		$this->total_page = true;
		$this->total_page_header = $with_header;
		$this->total_page_y = $total_page_y;

		return $this;
	}
}

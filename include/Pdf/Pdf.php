<?php
namespace Pdf;

require_once('fpdf/fpdf.php');
require_once('fpdf/fpdi/fpdi.php');
/** \defgroup pdf Pdf
 * 	\ingroup system
 * Class pour abstraite qui permet de générer plus facilement les pdf a l'avenir
 */
abstract class Pdf extends \FPDI
{
	/**
	 * $with_header
	 *
	 * @var boolean
	 */
	private $with_header = false;

	/**
	 * $margin_left
	 *
	 * @var integer
	 */
	protected $margin_left = 7.7;

	/**
	 * $header_on_all_pages
	 *
	 * @var boolean
	 */
	private $header_on_all_pages = false;

	/**
	 * $font_family
	 *
	 * @var string
	 */
	protected $font_family = 'Arial';

	/**
	 * $last_page
	 *
	 * @var boolean
	 */
	protected $last_page = false;

	/**
	 * Contient le contenu du pdf entre le header et le footer
	 *
	 * @return void
	 */
	abstract public function content();

	/**
	 * Génère le pdf vers un fichier ou en l'affichant
	 *
	 * @param mixed $name
	 * @return void
	 */
	public function generate($name='',$dest='')
	{
		$this->bootstrap();
		ob_end_clean();
		$this->Output($name,$dest);
	}

	/**
	 * réinitialise la coordonnée X
	 *
	 * @return void
	 */
	public function resetX()
	{
		$this->cell(0, 0, '', 0, 1);
	}

	/**
	 * Configure la famille de police à utilisé sur tous le pdf
	 *
	 * @param mixed $font
	 * @return void
	 */
	public function withFont($font)
	{
		$this->font_family = $font;

		return $this;
	}

	/**
	 * Retourne la police à utilisé sur tous le pdf
	 *
	 * @return void
	 */
	public function font()
	{
		return $this->font_family;
	}

	/**
	 * Test si la page doit avoir un header
	 *
	 * @return bool
	 */
	public function haveHeader()
	{
		return $this->header_on_all_pages || $this->with_header;
	}

	/**
	 * Permet de configurer si on a un header sur toutes les page ou pas
	 *
	 * @param mixed $bool
	 * @return instance
	 */
	public function headerOnAllPages($bool)
	{
		$this->header_on_all_pages = $this->with_header = (bool)$bool;

		return $this;
	}

	/**
	 * Lance toutes les fonction de génération du pdf avant ça sortie
	 *
	 * @return void
	 */
	protected function bootstrap()
	{
		$this->Open();
		$this->SetAutoPageBreak(true);
		$this->setDisplayMode('real');
		$this->SetFont($this->font_family, '', 9);
		$this->AddPage();
		$this->content();
	}

	/**
	 * Helper pour formaté les tarifs
	 *	@param int $decimals Optionnel, nombre de décimals (par défaut 2)
	 * @param mixed $price
	 * @return string
	 */
	protected function price($price, $decimals=2)
	{
		return number_format($price, $decimals, ',', ' ');
	}
}
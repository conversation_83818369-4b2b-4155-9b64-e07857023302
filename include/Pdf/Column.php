<?php
namespace Pdf;

use Closure;
/**
 * \ingroup ProductTable
 */
class Column
{
	/**
	 * $name
	 *
	 * @var string
	 */
	protected $name;

	/**
	 * $width
	 *
	 * @var integer
	 */
	protected $width;

	/**
	 * $height
	 *
	 * @var int
	 */
	protected $height;

	/**
	 * $optimal_width
	 *
	 * @var integer
	 */
	protected $optimal_width;

	/**
	 * $use
	 *
	 * @var Closure|string
	 */
	protected $use;

	/**
	 * $nb_line
	 *
	 * @var int
	 */
	protected $nb_line;

	/**
	 * $align
	 *
	 * @var string
	 */
	protected $align = 'L';

	/**
	 * $header_align
	 *
	 * @var string
	 */
	protected $header_align = 'C';

	/**
	 * $alignement
	 *
	 * @var array
	 */
	protected $alignement = array('L', 'C', 'R');

	/**
	 * __construct
	 *
	 * @param mixed $name
	 * @param mixed $align
	 * @param mixed $optimal_width
	 * @param mixed $use
	 * @return void
	 */
	public function __construct($name, $align, $optimal_width, $use)
	{
		$this->name = $name;
		$this->withAlignement($align);
		if (!$optimal_width || !is_numeric($optimal_width)) {
			throw new Exception("Optimal width must an integer");
		}
		$this->optimal_width = $optimal_width;

		$this->use = $this->parseUse($use);
	}

	/**
	 * Retourne le nom de la colonne
	 *
	 * @return string
	 */
	public function name()
	{
		return $this->name;
	}

	/**
	 * Retourne la taille optimal de la colonne
	 *
	 * @return integer
	 */
	public function optimalWidth()
	{
		return $this->optimal_width;
	}

	/**
	 * Exécute la closure avec produit pour afficher l'information
	 *
	 * @param array $product
	 * @return string
	 */
	public function using($product)
	{
		return call_user_func($this->use, $product);
	}

	/**
	 * Retourne la taille calculer de la colonne
	 *
	 * @return integer
	 */
	public function width()
	{
		return $this->width;
	}

	/**
	 * Configure la taille calculer de la colonne
	 *
	 * @param integer $width
	 * @return Column
	 */
	public function withWidth($width)
	{
		$this->width = $width;

		return $this;
	}

	/**
	 * Retourne la taille calculer de la colonne
	 *
	 * @return integer
	 */
	public function height()
	{
		return $this->height;
	}

	/**
	 * Configure la taille calculer de la colonne
	 *
	 * @param integer $width
	 * @return Column
	 */
	public function withHeight($height)
	{
		$this->height = $height;

		return $this;
	}

	/**
	 * Retourne la taille calculer de la colonne
	 *
	 * @return integer
	 */
	public function heightSingleLine()
	{
		return (int) $this->height / $this->nb_line;
	}

	/**
	 * Retourne le nombre de ligne
	 *
	 * @return integer
	 */
	public function nbLine()
	{
		return $this->nb_line;
	}

	/**
	 * Configure le nombre de ligne
	 *
	 * @param integer $nb_line
	 * @return Column
	 */
	public function withNbLine($nb_line)
	{
		$this->nb_line = $nb_line;

		return $this;
	}

	/**
	 * Retourne l'aligne dans la colonne
	 *
	 * @return string
	 */
	public function align()
	{
		return $this->align;
	}

	/**
	 * Configure l'alignement dans la colonne
	 *
	 * @param string $align
	 * @return Column
	 */
	public function withAlignement($align)
	{
		if (in_array($align, $this->alignement)) {
			$this->align = $align;
		}

		return $this;
	}

	/**
	 * Retourne l'aligne dans le header de la colonne
	 *
	 * @return string
	 */
	public function hAlign()
	{
		return $this->header_align;
	}

	/**
	 * Configure l'alignement dans le header de la colonne
	 *
	 * @param string $align
	 * @return Column
	 */
	public function withHeaderAlignement($align)
	{
		if (in_array($align, $this->alignement)) {
			$this->header_align = $align;
		}

		return $this;
	}

	/**
	 * Transforme l'action sur la ligne en closure si c'est un texte
	 *
	 * @param Closure|string $use
	 * @return Closure
	 */
	protected function parseUse($use)
	{
		if (is_string($use)) {
			return function ($product) use ($use) {
				if (array_key_exists($use, $product)) {
					return $product[$use];
				}

				return null;
			};
		} elseif ($use instanceof Closure) {
			return $use;
		}
	}
}
<?php

// require_once('../../simplesamlphp/lib/_autoload.php');
require_once(getenv('ENVRIA_ENGINE_DIRECTORY') . '/simplesaml/lib/_autoload.php');

/** \brief Cette classe permet de gérer des connexions diverses via SAML en utilisant la librairie SimpleSAMLphp.
 * 	\ingroup system
 */

class RiaSaml {
	private static $instance = null;

	// Cette variable contient les différentes configurations faisant le lien entre RiaShop et une connexion SAML
	// Elle sera charger depuis le registrer des tenants lors de l'initialisation d'un objet de la classe
	private $cfgSAML = [];

	/** Cette fonction permet de charger qu'un seul objet de type RiaSaml (pattern Singleton).
	 * 	@return RiaSaml l'instance
	 */
	public static function getInstance(){
		if( is_null(self::$instance) ){
			self::$instance = new RiaSaml();
		}

		return self::$instance;
	}

	/** Cette fonction permet de savoir si une adresse mail utilise une connexion via SAML plutôt que le système standard de RiaShop.
	 *  @param string $email Obligatoire, adresse e-mail
	 *  @return bool True si c'est le cas, False dans le cas contraire
	 */
	public function toUse( $email ){
		$exists = false;

		foreach( $this->cfgSAML as $one_cfg ){
			if( preg_match('/'.$one_cfg['regex'].'/', $email) ){
				$exists = true;
				break;
			}
		}

		return $exists;
	}

	/** Cette fonction permet de récupèrer le code correspondant à la configuration SimpleSAMLphp.
	 *  @param string $sch Obligatoire, code ou adresse e-mail pour charger la code de la configuration SimpleSAMLphp
	 *  @param bool $is_yuto Optionnel, par défaut on récupère la configuration pour RiaShop, mettr True pour celle de Yuto
	 *  @return mixed Le code de configuration SimpleSAMLphp si trouvé, False dans le cas contraire
	 */
	public function getConfig( $sch, $is_yuto=false ){
		$sp = false;

		// Recherche le SP en fonction d'une adresse mail ou bien d'un code
		foreach( $this->cfgSAML as $one_cfg ){
			if( isemail($sch) && preg_match('/'.$one_cfg['regex'].'/', $sch) ){
				$sp = $is_yuto ? $one_cfg['sp_yuto'] : $one_cfg['sp_riashop'];
			}else{
				$code = $is_yuto ? $one_cfg['sp_yuto'] : $one_cfg['sp_riashop'];
				if( $code == $sch ){
					$sp = $code;
				}
			}

			// Si le SP est trouvé, on ne parcours pas les autres configurations
			if( $sp !== false ){
				break;
			}
		}

		return $sp;
	}

	/** Cette fonction permet de récupèrer le code de la configuration SAML à partir d'une adresse e-mail.
	 *  @param string $email Obligatoire, adresse e-mail
	 *  @param bool $is_yuto Optionnel, si l'on souhaite récupérer le code pour Yuto (true) ou pour RiaShop (false, par défaut)
	 *  @return mixed Le code s'il existe, False dans le cas contaire
	 */
	public function codeByRegex( $email, $is_yuto=false ){
		$code = false;

		if( isemail($email) ){
			foreach( $this->cfgSAML as $one_cfg ){
				if( preg_match('/'.$one_cfg['regex'].'/', $email) ){
					$code = $is_yuto ? $one_cfg['sp_yuto'] : $one_cfg['sp_riashop'];
					break;
				}
			}
		}

		return $code;
	}

	/** Cette fonction permet de connecter le compte à RiaShop.
	 * 	Si le compte administrateur n'existe pas encore, il sera automatiquement créé.
	 * 	@param string $code Obligatoire, permet d'identifier le tenant pour lequel on souhaite connecter un compte
	 * 	@param string $email Obligatoire, adresse e-mail de l'administrateur
	 * 	@param string $firstname Optionnel, prénom de l'administrateur
	 * 	@param string $lastname Optionnel, nom de l'administrateur
	 * 	@return bool True en cas de succès, lève une exception en cas d'erreur
	 */
	public function login( $code, $email, $firstname='', $lastname='' ){
		$tnt_id = $this->getTenant( $code );
		if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
			throw new Exception('SAML - login failed');
		}

		global $admin_account;

		// Initialise la configuration pour ce tenant
		RegisterGCPConnection::init($tnt_id, true);

		// Charge l'administrateur depuis le registre
		$n_admin = new Administrator();
		$loaded = $n_admin->load($email);

		// Si l'utilisateur n'existe pas alors il sera créé
		if( !$loaded ){
			// Création du compte en base de données
			$r_add = gu_users_add_with_adresse($email, 4, false, $firstname, $lastname, null, PRF_ADMIN);
			if( !$r_add ){
				throw new Exception('SAML - error while create user in bdd');
			}

			// Ajout le droit d'accéder à ses informations personnelles uniquement
			$r_website = wst_websites_get( 0, false, $tnt_id );
			if( $r_website ){
				while( $website = ria_mysql_fetch_assoc($r_website) ){
					wst_website_rights_add( $tnt_id, $website['id'], 12050 );
				}
			}

			$n_admin->setEmail($email)
							->setFirstname($firstname)
							->setLastname($lastname)
							->setLang('fr_FR');

			// Sauvegarde le compte et le connecte
			if( !$n_admin->save(true) ){
				throw new Exception('SAML - error while create admin account');
			}
		}

		return $admin_account->connect($email, 'nocontrol');
	}

	/** Cette fonction permet de déconnecter le compte à RiaShop lorsque celui-ci est connecté via SAML.
	 * 	@return bool True en cas de succès sinon une exception sera levée en cas d'erreur
	 */
	public function logout(){
		global $config;

		// Récupère le SP du tenant
		$sp_config = false;

		foreach( $this->cfgSAML as $one_cfg ){
			if( $one_cfg['tenant'] == $config['tnt_id'] ){
				$sp_config = $one_cfg['sp_riashop'];
				break;
			}
		}

		// Si le tenant ne fait pas partie de ceux ayant une connexion SAML, on ne va pas plus loin
		if( $sp_config === false ){
			return true;
		}

		$auth = new SimpleSAML_Auth_Simple($sp_config);
		$auth->logout();
		SimpleSAML_Session::getSessionFromRequest()->cleanup();

		return true;
	}

	/** Cette fonction est le contructeur de la classe.
	 */
	private function __construct(){
		// Initialise la connexion au datastore via l'API de Google
		$datastore = new DatastoreClient();

		// Cherche un compte administrateur ayant cette adresse mail
		$query = $datastore->query()->kind('db_tenants');

		$result = $datastore->runQuery($query);
		foreach( $result as $res ){
			if( trim($res->getProperty('saml_riashop')) == '' && trim($res->getProperty('saml_yuto')) == '' ){
				continue;
			}

			$this->cfgSAML[] = [
				'tenant' => $res->getProperty('tenant_id'),
				'sp_riashop' => $res->getProperty('saml_riashop'),
				'sp_yuto' => $res->getProperty('saml_yuto'),
				'code' => [ $res->getProperty('saml_riashop'), $res->getProperty('saml_yuto') ],
				'regex' => $res->getProperty('saml_regex'),
			];
		}
	}

	/** Cette fonction permet de récupèrer à partir d'un code le tenant correspondant
	 * 	@param string $code Obligatoire, code pour lequel on souhaite connaitre le tenant
	 * 	@return int L'identifiant du tenant
	 */
	private function getTenant( $code ){
		if( trim($code) == '' ){
			return false;
		}

		$tenant = false;

		// Parcours les configurations à la recherche de l'identifiant du tenant
		foreach( $this->cfgSAML as $one_cfg ){
			if( in_array($code, $one_cfg['code']) ){
				$tenant = $one_cfg['tenant'];
				break;
			}
		}

		return $tenant;
	}
}


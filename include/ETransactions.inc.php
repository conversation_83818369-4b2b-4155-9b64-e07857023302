<?php
	// E-Transactions
	/** \defgroup etransactions E-Transactions
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec E-Transactions - Crédit agricole
	 *
	 *	Variables de config utilisées :
	 *	url_payment			:	Url de la page paiement
	 *	url_payment_success	:	Url de la page paiement effectué
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	spplus_key_test		:	Clé pour la signature (en mode test)
	 *	spplus_key			:	Clé pour la signature
	 *	spplus_site_id		:	Identifiant du site
	 *
	 *	Ces infos sont disponibles dans l'inteface SystemPay en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *
	 * 	Ce module est encore utilisé par :
	 * 	- Freevox
	 *  - Geste Editions
	 *  - MNails
	 *  - Rousseau
	 *
	 *	@{
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');

	/**	\brief Cette classe est l'implémentation concrète du fournisseur ETransactions / Crédit Agricole en tant que prestataire de paiement externe.
	 *
	 */
	class ETransactions extends PaymentExternal {
		const MAX_TRANSACTION_ID		=	899999;			///< Valeur max de l'identifiant de transaction
		const DEVISE_EURO				=	978;			///< Identifiant interne de la devise euro

		private static $Instance; ///< Instance

		private $ord_id = false; 		///< Identifiant de la commande à facturer
		private $is_3dSecure = false; 	///< Détermine si l'option 3d Secure est activée (true) ou non (false)
		private $_amount_order 	= 0; 	///< Permet de surcharger le montant à régler par CB

		//Code de retours et noms associés
		protected $returns = array(
			"00" => "Transaction acceptée",
			"75" => "3 tentatives échouées",
			"05" => "Paiement refusé",
			"others" => "Transaction échouée, code de retour non géré",
		);

		/**
		 *	Variable permettant de mettre les codes pour lesquels on ne souhaite pas lever d'exception
		 *	75 : 3 tentatives échoué
		 *	05 : Paiement refusé
		 */
		private static $responses_code_exclude = array( '75', '05' );

		/** Constructeur
		 *	@param bool $is_3dSecure détermine si l'option 3d Secure est activée ou non
		 */
		public function __construct( $is_3dSecure = false ){
			$this->is_3dSecure = $is_3dSecure;
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 * @return void
		 */
		public static function doPayment(){
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *	@param bool $notify Optionnel, par défaut une confirmation de commande sera envoyé par mail, mettre false pour que ce ne soit pas le cas
		 *	@return object L'instance
		 */
		public static function getPaymentResult( $notify=true ){
			return self::getInstance()->_getPaymentResult($notify);
		}

		/**
		 * Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return string Le formulaire html
		 */
		public function _doPayment(){
			global $config;

			$orderId = $this->ord_id ? $this->ord_id : $this->getOrderId();
			$amount = is_numeric($this->_amount_order) && $this->_amount_order > 0 ? round($this->_amount_order, 2) : $this->getOrderAmount();

			// Génère un id de transaction
			$transaction_id = ord_transactions_create();
			if( $transaction_id === false ){
				throw new Exception('Erreur ord_transactions_create !');
			}
			if( $transaction_id > ETransactions::MAX_TRANSACTION_ID ){
				throw new Exception('Tous les identifiants de transaction de la journée sont épuisés !');
			}

			$parm="merchant_id=".$this->getMerchantId();
			$parm="$parm merchant_country=fr";
			$parm="$parm amount=".round(100 * $amount);
			$parm="$parm currency_code=".self::DEVISE_EURO;

			$parm="$parm pathfile=".$_SERVER['DOCUMENT_ROOT']."/e/p/pathfile";
			$parm="$parm transaction_id=".$transaction_id;

			$parm="$parm capture_mode=AUTHOR_CAPTURE";
			$parm="$parm capture_day=0";
			$parm="$parm order_id=".$orderId;

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/e/bin/request";

			$result=exec("$path_bin $parm");
			$tableau = explode ("!", $result);
			if( !isset($tableau[1], $tableau[2], $tableau[3]) ){
				throw new Exception('Erreur appel API de paiement : '.$result);
			}

			// Enregistre l'accès à la banque dans CouchDB
			$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $orderId)));

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];

			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			$this->data_couchDB['data'] = $parm;
			$this->savePaymentInCouchDB();

			$code = $tableau[1];
			$error = $tableau[2];
			$message = $tableau[3];


			if (( $code == "" ) && ( $error == "" ) ){
				throw new Exception ('executable request non trouve '.$path_bin);
			}elseif( $code != 0 ){
				throw new Exception ('Erreur appel API de paiement : '.$error);
			}else{
				return $error . $message;
			}

		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@param bool $notify Optionnel, par défaut une confirmation de commande sera envoyé par mail, mettre false pour que ce ne soit pas le cas
		 *	@return object L'instance
		 */
		public function _getPaymentResult( $notify=true ){
			global $config;

			$params = self::getResponse();

			{ // Enregistre le retour de la banque dans CouchDB
				$name = $this->returns[(array_key_exists($params['response_code'], $this->returns) ? $params['response_code'] : "others")];
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $params['order_id'])));
				$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];

				$this->data_couchDB['data'] = $params;
				$this->data_couchDB['code_id'] = $params['response_code'];
				$this->data_couchDB['code_name'] = $name;
				$this->saveReturnPaymentInCouchDB();
			}

			if( !isset($params['order_id']) || !is_numeric( $params['order_id'] ) )
				throw new Exception( "ETransactions : le numéro de commande n'est pas valide." );

			if( !in_array( $params['response_code'], self::$responses_code_exclude ) ){

				if( $params['response_code'] != "00" )
					throw new Exception( "Etransactions : Le code de réponse n'est pas valide : ".$params['response_code'] );
				if( $params['bank_response_code'] != "00" )
					throw new Exception( "Etransactions : Le code de réponse de la banque n'est pas valide : ".$params['bank_response_code'] );

				$orderId = $params['order_id'];

				// Véfifie l'état de la commande
				$state = ord_orders_get_state($orderId);
				if ($state === false || !in_array($state, array(_STATE_BASKET, _STATE_NO_FINISH, _STATE_DEVIS, _STATE_PAY_WAIT_CONFIRM,_STATE_BASKET_PAY_CB, _STATE_BASKET_SAVE))) {
					throw new exception("Etransactions : La commande $orderId semble déjà avoir été traitée ! (state = $state)");
				}

				ord_orders_pay_type_set($orderId, 1);

				ord_orders_state_update($orderId, 3, '', $notify);
				ord_orders_state_update($orderId, 4, '', $notify);

				// Confirmation de la commande à NetAffiliation
				$affi = new NetAffiliation();
				$affi->getOnlinePaymentTag( $orderId );

				return $this;

			}
		}

		/**
		 *	Cette fonction récupère la requete de la banque et la décrypte.
		 *	@return array un tableau avec les valeurs renvoyées par la banque
		 */
		public static function getResponse(){
			if( !isset( $_REQUEST['DATA'] ) ) throw new Exception('Les données ne sont pas présentes.');

			$message="message=".$_REQUEST['DATA'];

			$pathfile="pathfile=".$_SERVER['DOCUMENT_ROOT']."/e/p/pathfile";

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/e/bin/response";

			$result=exec("$path_bin $pathfile $message");

			$tableau = explode ("!", $result);

			$t_result = array();

			if (!isset($tableau[1])) {
				return $t_result;
			}

			$t_result['code'] = $tableau[1];
			$t_result['error'] = $tableau[2];
			$t_result['merchant_id'] = $tableau[3];
			$t_result['merchant_country'] = $tableau[4];
			$t_result['amount'] = $tableau[5];
			$t_result['transaction_id'] = $tableau[6];
			$t_result['payment_means'] = $tableau[7];
			$t_result['transmission_date']= $tableau[8];
			$t_result['payment_time'] = $tableau[9];
			$t_result['payment_date'] = $tableau[10];
			$t_result['response_code'] = $tableau[11];
			$t_result['payment_certificate'] = $tableau[12];
			$t_result['authorisation_id'] = $tableau[13];
			$t_result['currency_code'] = $tableau[14];
			$t_result['card_number'] = $tableau[15];
			$t_result['cvv_flag'] = $tableau[16];
			$t_result['cvv_response_code'] = $tableau[17];
			$t_result['bank_response_code'] = $tableau[18];
			$t_result['complementary_code'] = $tableau[19];
			$t_result['complementary_info'] = $tableau[20];
			$t_result['return_context'] = $tableau[21];
			$t_result['caddie'] = $tableau[22];
			$t_result['receipt_complement'] = $tableau[23];
			$t_result['merchant_language'] = $tableau[24];
			$t_result['language'] = $tableau[25];
			$t_result['customer_id'] = $tableau[26];
			$t_result['order_id'] = $tableau[27];
			$t_result['customer_email'] = $tableau[28];
			$t_result['customer_ip_address'] = $tableau[29];
			$t_result['capture_day'] = $tableau[30];
			$t_result['capture_mode'] = $tableau[31];
			$t_result['data'] = $tableau[32];

			return $t_result;
		}


		/**
		 *	Cette fonction returne le merchant_id à utiliser en fonction de si c'est un paiement 3dsecure ou non
		 */
		public function getMerchantId(){
			global $config;
			return $this->is_3dSecure  ? $config['etransactions_merchant_3ds_id'] : $config['etransactions_merchant_id'];
		}

		/**
		 *	Permet de surcharger le ord_id
		 *	@param int $ord_id Obligatoire, identifiant de commande à utiliser
		 */
		public function setOrdId( $ord_id ){
			$this->ord_id = $ord_id;
		}

		/** Cette méthode permet de définir le montant à régler
		 *	@param float $amount Montant à régler
		 */
		public function setAmount( $amount ){
			if( !is_numeric($amount) || $amount<=0 ){
				return false;
			}

			$this->_amount_order = $amount;
		}
	}

	/// @}

